[{"__type__": "cc.Prefab", "_name": "UIFarmCjjj", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "UIFarmCjjj", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 28}, {"__id__": 265}], "_active": true, "_components": [{"__id__": 350}], "_prefab": {"__id__": 352}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "main", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}], "_active": true, "_components": [{"__id__": 262}], "_prefab": {"__id__": 264}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "dialog_background", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 4}, {"__id__": 18}, {"__id__": 53}], "_active": true, "_components": [{"__id__": 257}, {"__id__": 259}], "_prefab": {"__id__": 261}, "_lpos": {"__type__": "cc.Vec3", "x": 4.5, "y": 19.292, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "dialog_icon", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [{"__id__": 5}], "_active": true, "_components": [{"__id__": 11}, {"__id__": 13}, {"__id__": 15}], "_prefab": {"__id__": 17}, "_lpos": {"__type__": "cc.Vec3", "x": -178.07799999999997, "y": 393.823, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "dialog_title", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 4}, "_children": [], "_active": true, "_components": [{"__id__": 6}, {"__id__": 8}], "_prefab": {"__id__": 10}, "_lpos": {"__type__": "cc.Vec3", "x": -135.797, "y": -0.853, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 5}, "_enabled": true, "__prefab": {"__id__": 7}, "_contentSize": {"__type__": "cc.Size", "width": 259.9936220687599, "height": 85.68}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "77JTmITHxP3o2uOTL8Xc1/"}, {"__type__": "cc.RichText", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 5}, "_enabled": true, "__prefab": {"__id__": 9}, "_lineHeight": 68, "_string": "<outline color=#ffa200 width=3><color=#ffffff><size=68>采</size></color></outline><outline color=#ffa200 width=3><color=#ffffff><size=60>集基金</size></color></outline>", "_horizontalAlign": 1, "_verticalAlign": 2, "_fontSize": 68, "_fontColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_maxWidth": 0, "_fontFamily": "<PERSON><PERSON>", "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_userDefinedFont": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_cacheMode": 0, "_imageAtlas": null, "_handleTouchEvent": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c20IT7iotMZoOMJ1uoc6qU"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0cODzb/YBBdLJOfDkHoIoh", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 4}, "_enabled": true, "__prefab": {"__id__": 12}, "_contentSize": {"__type__": "cc.Size", "width": 298, "height": 135}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c8/FqV1KZEKrSzhWuFAoSW"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 4}, "_enabled": true, "__prefab": {"__id__": 14}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3a4cdee5-c1a0-4286-8097-08ccc00c31c6@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7aIfeicUxAUY1KJEWPY9NA"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 4}, "_enabled": true, "__prefab": {"__id__": 16}, "_alignFlags": 9, "_target": null, "_left": -22.578000000000003, "_right": 0, "_top": -15.32299999999999, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "23cx8FDKRKo5dgEI/lk1MN"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5fvrlSsthGAJl3UoZTk+LJ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "dialog_close", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [], "_active": true, "_components": [{"__id__": 19}, {"__id__": 21}, {"__id__": 23}, {"__id__": 25}], "_prefab": {"__id__": 52}, "_lpos": {"__type__": "cc.Vec3", "x": 281.361, "y": 424.196, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 20}, "_contentSize": {"__type__": "cc.Size", "width": 77, "height": 77}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d1/+iOZWZAxqtiC6yb8SPJ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 22}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8e27d803-97dc-403c-bae5-609f4c45539b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dd/neQ2lFMTptRC8y/fe9r"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 24}, "_alignFlags": 33, "_target": null, "_left": 0, "_right": -15.36099999999999, "_top": -16.696000000000026, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c7/CWSyy1P37nSOIbZINCD"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 26}, "clickEvents": [{"__id__": 27}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "990eHp/E1MD4pdd+tY/KwO"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 28}, "component": "", "_componentId": "37daazP0QhHJq5Z8nQt0ily", "handler": "onClickClose", "customEventData": ""}, {"__type__": "cc.Node", "_name": "DialogSub", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 29}, {"__id__": 39}], "_active": false, "_components": [{"__id__": 47}, {"__id__": 49}], "_prefab": {"__id__": 51}, "_lpos": {"__type__": "cc.Vec3", "x": 6.286999999999998, "y": 6.357000000000008, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "dialog_content", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 28}, "_children": [], "_active": true, "_components": [{"__id__": 30}, {"__id__": 32}, {"__id__": 34}, {"__id__": 36}], "_prefab": {"__id__": 38}, "_lpos": {"__type__": "cc.Vec3", "x": -6.391999999999996, "y": -5.593500000000006, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 29}, "_enabled": true, "__prefab": {"__id__": 31}, "_contentSize": {"__type__": "cc.Size", "width": 542.5880000000001, "height": 800.8330000000001}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "92FZc3zsNE5piYB1NL8tnH"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 29}, "_enabled": true, "__prefab": {"__id__": 33}, "_alignFlags": 45, "_target": null, "_left": 26.813999999999982, "_right": 39.597999999999935, "_top": 51.17700000000004, "_bottom": 39.989999999999974, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "74DN66nGlH96gdECipSVUO"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 29}, "_enabled": true, "__prefab": {"__id__": 35}, "_type": 2, "_inverted": false, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b7FFgezd5MX6PK48Hcu/Ll"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 29}, "_enabled": true, "__prefab": {"__id__": 37}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "03JF1mhutJ6ZuI8HPfVsgr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "845M+Z8khAjIsJjUacQhaT", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "dialog_head", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 28}, "_children": [], "_active": true, "_components": [{"__id__": 40}, {"__id__": 42}, {"__id__": 44}], "_prefab": {"__id__": 46}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 418, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 39}, "_enabled": true, "__prefab": {"__id__": 41}, "_contentSize": {"__type__": "cc.Size", "width": 609, "height": 112}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "daXhgKdeRDQopIWYJGruGF"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 39}, "_enabled": true, "__prefab": {"__id__": 43}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "94XCWad3dFmpbpH1ne5SII"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 39}, "_enabled": true, "__prefab": {"__id__": 45}, "_alignFlags": 41, "_target": null, "_left": 0, "_right": 0, "_top": -28, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 148, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "68uYvEJB9NjL7ZQ1YiRg/Z"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9dNPSMX4lFCryl9QOFFAtP", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 28}, "_enabled": true, "__prefab": {"__id__": 48}, "_contentSize": {"__type__": "cc.Size", "width": 609, "height": 892}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3bcBxHWmFBOpLRxIeGsrJP"}, {"__type__": "37daazP0QhHJq5Z8nQt0ily", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 28}, "_enabled": true, "__prefab": {"__id__": 50}, "graphics": {"__id__": 36}, "_userRadius": 20, "dialogTitle": {"__id__": 5}, "_strTitle": "采集基金", "dialogTitleType": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bbbRpSfApIB6RVN3L44OQp"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "397YAc1pFN1JhXt9FGuZYf", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4e6/DSMGFCVKR439DHR1pD", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "context", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [{"__id__": 54}, {"__id__": 60}, {"__id__": 66}, {"__id__": 72}, {"__id__": 78}, {"__id__": 86}, {"__id__": 106}, {"__id__": 120}, {"__id__": 138}, {"__id__": 156}, {"__id__": 176}, {"__id__": 182}, {"__id__": 206}, {"__id__": 230}], "_active": true, "_components": [{"__id__": 254}], "_prefab": {"__id__": 256}, "_lpos": {"__type__": "cc.Vec3", "x": -5.95150000000001, "y": -20.052499999999895, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 53}, "_children": [], "_active": true, "_components": [{"__id__": 55}, {"__id__": 57}], "_prefab": {"__id__": 59}, "_lpos": {"__type__": "cc.Vec3", "x": 3, "y": 4.2, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 54}, "_enabled": true, "__prefab": {"__id__": 56}, "_contentSize": {"__type__": "cc.Size", "width": 554, "height": 801}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0fIRNJMixEmrLLP9mbG6Co"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 54}, "_enabled": true, "__prefab": {"__id__": 58}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e52f50fd-3ed6-4835-a0a0-78f51fc83448@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ddOoaF5clA6LLBnrDizUiB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "eb2Gnb8RhO/5Jb3q8mDHqN", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_lv1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 53}, "_children": [], "_active": false, "_components": [{"__id__": 61}, {"__id__": 63}], "_prefab": {"__id__": 65}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 109.234, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 60}, "_enabled": true, "__prefab": {"__id__": 62}, "_contentSize": {"__type__": "cc.Size", "width": 293, "height": 268}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "04l2gGoRFM8r+l+Fh+abvw"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 60}, "_enabled": true, "__prefab": {"__id__": 64}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "55297046-7bf9-4121-ac76-e61b9512053f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "91KMzzxH9EppPFfB5rMMWS"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "58jYE1cvZFqYFpuwCh9i7Z", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_lv2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 53}, "_children": [], "_active": true, "_components": [{"__id__": 67}, {"__id__": 69}], "_prefab": {"__id__": 71}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 109.234, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 66}, "_enabled": true, "__prefab": {"__id__": 68}, "_contentSize": {"__type__": "cc.Size", "width": 285, "height": 268}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cchbiFvv1FJrkKVUzc52Xw"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 66}, "_enabled": true, "__prefab": {"__id__": 70}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "7d65d95a-fbd7-4309-a5b5-71b94c0466f6@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "58jEZrBilENrNnbrF0xDeF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f85nvDq7VP2IOVpxFq0fBS", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_lv3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 53}, "_children": [], "_active": false, "_components": [{"__id__": 73}, {"__id__": 75}], "_prefab": {"__id__": 77}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 109.234, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 72}, "_enabled": true, "__prefab": {"__id__": 74}, "_contentSize": {"__type__": "cc.Size", "width": 291, "height": 259}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0789bKXv5GsZmSDq2E7uvR"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 72}, "_enabled": true, "__prefab": {"__id__": 76}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "110f960b-bf61-4df1-8357-a9b2409d346b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0bcj6E7MpBmIrHZ95OVe41"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0d7pmAnwdBO7z7JzqcRhxh", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_hint", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 53}, "_children": [], "_active": false, "_components": [{"__id__": 79}, {"__id__": 81}, {"__id__": 83}], "_prefab": {"__id__": 85}, "_lpos": {"__type__": "cc.Vec3", "x": -25.269, "y": -178.82, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 78}, "_enabled": true, "__prefab": {"__id__": 80}, "_contentSize": {"__type__": "cc.Size", "width": 426.39996337890625, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1cnr5X2w9BOIXmrBotK6XH"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 78}, "_enabled": true, "__prefab": {"__id__": 82}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "每次成功彩集他人福地能增加%1进度", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "79UG7O30dMqY6uKbJ6fvXE"}, {"__type__": "4b414FkqlpD0ac92djm25UF", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 78}, "_enabled": true, "__prefab": {"__id__": 84}, "_args": [], "_messageKey": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2ehW/ct+tNnKlCerS32feu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "aaRh0eGeBHFJzCccqijA+y", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "progress_bar", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 53}, "_children": [{"__id__": 87}, {"__id__": 93}], "_active": true, "_components": [{"__id__": 99}, {"__id__": 101}, {"__id__": 103}], "_prefab": {"__id__": 105}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -159.372, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bar", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 86}, "_children": [], "_active": true, "_components": [{"__id__": 88}, {"__id__": 90}], "_prefab": {"__id__": 92}, "_lpos": {"__type__": "cc.Vec3", "x": -205.5, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 87}, "_enabled": true, "__prefab": {"__id__": 89}, "_contentSize": {"__type__": "cc.Size", "width": 411, "height": 14}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f86NFakLZNVqZHz1STtog/"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 87}, "_enabled": true, "__prefab": {"__id__": 91}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "280c6471-a23b-4c40-950d-dbbced4ba398@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f46v1f84lGrbKe9rrRMUgn"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5bcvwN92ZPIbK9HQ5evLHc", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_progress", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 86}, "_children": [], "_active": true, "_components": [{"__id__": 94}, {"__id__": 96}], "_prefab": {"__id__": 98}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 1.163, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 93}, "_enabled": true, "__prefab": {"__id__": 95}, "_contentSize": {"__type__": "cc.Size", "width": 57.99045231611437, "height": 38.019999999999996}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "10v7lnomhCLLEPzcME0TOC"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 93}, "_enabled": true, "__prefab": {"__id__": 97}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "60%", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 28.305, "_fontSize": 27, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 27, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 150, "g": 88, "b": 28, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c7WB9rFxtEzr2iTyWvJMeC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "33qDutECVCPoDCcmwh7rEL", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 86}, "_enabled": true, "__prefab": {"__id__": 100}, "_contentSize": {"__type__": "cc.Size", "width": 429, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "129bDemXtHdpfOjNAqu4l/"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 86}, "_enabled": true, "__prefab": {"__id__": 102}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "dfe175ca-f833-4f4a-9f41-a21e7d1c5b87@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "46jrIdZzNI4aJZ5W9LEZzp"}, {"__type__": "cc.ProgressBar", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 86}, "_enabled": true, "__prefab": {"__id__": 104}, "_barSprite": {"__id__": 90}, "_mode": 0, "_totalLength": 411, "_progress": 1, "_reverse": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9fLsMxxTpPQoTs1lRrDLEW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f9Wz6OKepGZrqL+QwFu7gO", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_reward", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 53}, "_children": [{"__id__": 107}], "_active": true, "_components": [{"__id__": 113}, {"__id__": 115}, {"__id__": 117}], "_prefab": {"__id__": 119}, "_lpos": {"__type__": "cc.Vec3", "x": 226.856, "y": -149.277, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lbl_cur_award", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 106}, "_children": [], "_active": true, "_components": [{"__id__": 108}, {"__id__": 110}], "_prefab": {"__id__": 112}, "_lpos": {"__type__": "cc.Vec3", "x": 5, "y": -30, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 107}, "_enabled": true, "__prefab": {"__id__": 109}, "_contentSize": {"__type__": "cc.Size", "width": 53.98469482810016, "height": 56.400000000000006}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ccsJu7Y6JC7rvq85Mz5XNj"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 107}, "_enabled": true, "__prefab": {"__id__": 111}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 242, "g": 220, "b": 72, "a": 255}, "_string": "6000", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20.96666666666667, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 67, "g": 39, "b": 12, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "77G4PNh41PqrC1/RoxIU4I"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "84LsQu+dVA3r9t8EnCkFF2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 106}, "_enabled": true, "__prefab": {"__id__": 114}, "_contentSize": {"__type__": "cc.Size", "width": 75, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f6ykLQGhJJqZvtqzOCilyO"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 106}, "_enabled": true, "__prefab": {"__id__": 116}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b3081c1c-ea61-4023-8779-3c72e3422dd9@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bfiKvEpHxBNYI0ztinzbf1"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 106}, "_enabled": true, "__prefab": {"__id__": 118}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": {"__id__": 106}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "48kSY41IJDzpmXrQIMJ7lM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "01RdpUZpFL8qYaUG1876cH", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_do", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 53}, "_children": [{"__id__": 121}, {"__id__": 127}], "_active": true, "_components": [{"__id__": 133}, {"__id__": 135}], "_prefab": {"__id__": 137}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -245, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btn_huangse", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 120}, "_children": [], "_active": true, "_components": [{"__id__": 122}, {"__id__": 124}], "_prefab": {"__id__": 126}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 121}, "_enabled": true, "__prefab": {"__id__": 123}, "_contentSize": {"__type__": "cc.Size", "width": 199, "height": 68}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b2mw4qaIBKcZw1B4JqA2Xl"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 121}, "_enabled": true, "__prefab": {"__id__": 125}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8aac8aa5-631a-436e-92ba-84e56018ec61@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dfhrNTpVNLmoWZTbaZF+HH"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "61L4lXzeJEDIN7SruVs76A", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 120}, "_children": [], "_active": true, "_components": [{"__id__": 128}, {"__id__": 130}], "_prefab": {"__id__": 132}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 127}, "_enabled": true, "__prefab": {"__id__": 129}, "_contentSize": {"__type__": "cc.Size", "width": 110.9951938453274, "height": 58.92000000000001}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "97+64H3cdLyLC7XLalVnjq"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 127}, "_enabled": true, "__prefab": {"__id__": 131}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "去完成", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 36.69166666666667, "_fontSize": 35, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 42, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 183, "g": 110, "b": 56, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "87tzzXCatIH6iNpqmxuK1e"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "95H27D8I1PobpMM2HTTLcO", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 120}, "_enabled": true, "__prefab": {"__id__": 134}, "_contentSize": {"__type__": "cc.Size", "width": 199, "height": 68}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "11TCu4GYdNk6z9mvGamSfm"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 120}, "_enabled": true, "__prefab": {"__id__": 136}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": {"__id__": 120}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1fFR58+fhEf6xuofGen2Hv"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "70alFTqy1E371Uj5yTGXih", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_buy", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 53}, "_children": [{"__id__": 139}, {"__id__": 145}], "_active": true, "_components": [{"__id__": 151}, {"__id__": 153}], "_prefab": {"__id__": 155}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -245, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btn_huangse", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 138}, "_children": [], "_active": true, "_components": [{"__id__": 140}, {"__id__": 142}], "_prefab": {"__id__": 144}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 139}, "_enabled": true, "__prefab": {"__id__": 141}, "_contentSize": {"__type__": "cc.Size", "width": 199, "height": 68}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f40zUye8ZH9Yi7SH3Sc5PP"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 139}, "_enabled": true, "__prefab": {"__id__": 143}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8aac8aa5-631a-436e-92ba-84e56018ec61@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6493dvW6dM84cx1H6C8vRA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cbs5ijvZxM/ZzMZC8r2ws5", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 138}, "_children": [], "_active": true, "_components": [{"__id__": 146}, {"__id__": 148}], "_prefab": {"__id__": 150}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 145}, "_enabled": true, "__prefab": {"__id__": 147}, "_contentSize": {"__type__": "cc.Size", "width": 110.9951938453274, "height": 58.92000000000001}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "90UfjeMolEnJCEDm/6PGE/"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 145}, "_enabled": true, "__prefab": {"__id__": 149}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "去完成", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 36.69166666666667, "_fontSize": 35, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 42, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 183, "g": 110, "b": 56, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e8s6UC6H9G/LPH1SdKrNhF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c3t7AfD1JIHLNvY4HiE/EN", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 138}, "_enabled": true, "__prefab": {"__id__": 152}, "_contentSize": {"__type__": "cc.Size", "width": 199, "height": 68}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4eByRJjztCh5dDpZ5yxRhP"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 138}, "_enabled": true, "__prefab": {"__id__": 154}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": {"__id__": 138}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3f32SFjx9ALaVTRk9MRBH7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ceIR08HNtDsrwK6WR+7iu+", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_collect", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 53}, "_children": [{"__id__": 157}, {"__id__": 163}], "_active": true, "_components": [{"__id__": 171}, {"__id__": 173}], "_prefab": {"__id__": 175}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -245, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btn_huangse", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 156}, "_children": [], "_active": true, "_components": [{"__id__": 158}, {"__id__": 160}], "_prefab": {"__id__": 162}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 157}, "_enabled": true, "__prefab": {"__id__": 159}, "_contentSize": {"__type__": "cc.Size", "width": 199, "height": 68}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5eNXOu+EdILpBI7tLSwkul"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 157}, "_enabled": true, "__prefab": {"__id__": 161}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8aac8aa5-631a-436e-92ba-84e56018ec61@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e4vsmQRlFPkY3S4MJxknHL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fcFnBYDYZO9pIS9/A4PhD5", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 156}, "_children": [], "_active": true, "_components": [{"__id__": 164}, {"__id__": 166}, {"__id__": 168}], "_prefab": {"__id__": 170}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 163}, "_enabled": true, "__prefab": {"__id__": 165}, "_contentSize": {"__type__": "cc.Size", "width": 75.99679589688495, "height": 58.92000000000001}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "45uTntxjhLvJuTRlL8DBVo"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 163}, "_enabled": true, "__prefab": {"__id__": 167}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "领取", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 36.69166666666667, "_fontSize": 35, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 42, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 183, "g": 110, "b": 56, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5f6BF3EGZK5bEJXvuWLjMR"}, {"__type__": "4b414FkqlpD0ac92djm25UF", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 163}, "_enabled": true, "__prefab": {"__id__": 169}, "_args": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], "_messageKey": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b5I4YxxZNCGZWcYBAinAAN"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "73XKw6rCBBu4lCljQzZ7m+", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 156}, "_enabled": true, "__prefab": {"__id__": 172}, "_contentSize": {"__type__": "cc.Size", "width": 199, "height": 68}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aaOtONH9lD6afAXjD7rpLJ"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 156}, "_enabled": true, "__prefab": {"__id__": 174}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": {"__id__": 156}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8d5kQEJG9BT5oBg4Bgo7+L"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "02J+U12F9HwYlVz/R8TZY2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_finish", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 53}, "_children": [], "_active": true, "_components": [{"__id__": 177}, {"__id__": 179}], "_prefab": {"__id__": 181}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -245, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1.5, "y": 1.5, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 176}, "_enabled": true, "__prefab": {"__id__": 178}, "_contentSize": {"__type__": "cc.Size", "width": 113, "height": 58}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "09Qefot6dPf4T8pHifPaeW"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 176}, "_enabled": true, "__prefab": {"__id__": 180}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "11a6fc33-408d-4211-898c-34e5fb98fda9@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8fSv3bVGpI5asPQzkiBRU0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c1Y5fr3SlML7Ffh6ixrpTo", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_lv1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 53}, "_children": [{"__id__": 183}, {"__id__": 189}, {"__id__": 195}], "_active": true, "_components": [{"__id__": 201}, {"__id__": 203}], "_prefab": {"__id__": 205}, "_lpos": {"__type__": "cc.Vec3", "x": -115, "y": -342, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_unactive", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 182}, "_children": [], "_active": true, "_components": [{"__id__": 184}, {"__id__": 186}], "_prefab": {"__id__": 188}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 183}, "_enabled": true, "__prefab": {"__id__": 185}, "_contentSize": {"__type__": "cc.Size", "width": 90, "height": 82}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "14BBUwiMpLELgFl9osYzFS"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 183}, "_enabled": true, "__prefab": {"__id__": 187}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "80470fdb-7da4-4c1f-9e60-bcbb38cd4037@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a3BVy0S+pJ4JY1FkSETg+S"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c8Ah4hrKtKn4IS5GtrKEik", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_active", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 182}, "_children": [], "_active": false, "_components": [{"__id__": 190}, {"__id__": 192}], "_prefab": {"__id__": 194}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 189}, "_enabled": true, "__prefab": {"__id__": 191}, "_contentSize": {"__type__": "cc.Size", "width": 105, "height": 97}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "favgU9QUZCwrLDJwp/i3GH"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 189}, "_enabled": true, "__prefab": {"__id__": 193}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "f5b0c5d6-0a3d-4140-8b00-ea97defce7e5@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d344Zi6kFFVLuyLw7I9lV6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f8qTmBDKpF4o01U7DvkUMy", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 182}, "_children": [], "_active": true, "_components": [{"__id__": 196}, {"__id__": 198}], "_prefab": {"__id__": 200}, "_lpos": {"__type__": "cc.Vec3", "x": 10.63, "y": -30, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 195}, "_enabled": true, "__prefab": {"__id__": 197}, "_contentSize": {"__type__": "cc.Size", "width": 55.98409592669416, "height": 58.92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "21JYzaY0NEA41X4FCUfdfG"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 195}, "_enabled": true, "__prefab": {"__id__": 199}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 242, "g": 220, "b": 72, "a": 255}, "_string": "青铜", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26.208333333333332, "_fontSize": 25, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 42, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 67, "g": 39, "b": 12, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dbXNTAUxdHSKI7bPRYwPiR"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b7HtfYK4BCPIibI6Xt3rz6", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 182}, "_enabled": true, "__prefab": {"__id__": 202}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b5+T4K+CJI17i2KoyYYbah"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 182}, "_enabled": true, "__prefab": {"__id__": 204}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0bJEartuhPcZw66BW0O2IR"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5dc/5ANNhJqZgz0U4GW+vl", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_lv2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 53}, "_children": [{"__id__": 207}, {"__id__": 213}, {"__id__": 219}], "_active": true, "_components": [{"__id__": 225}, {"__id__": 227}], "_prefab": {"__id__": 229}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -342, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_unactive", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 206}, "_children": [], "_active": true, "_components": [{"__id__": 208}, {"__id__": 210}], "_prefab": {"__id__": 212}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 207}, "_enabled": true, "__prefab": {"__id__": 209}, "_contentSize": {"__type__": "cc.Size", "width": 87, "height": 82}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ddSXPKfmRM8Yyuz73bXR9e"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 207}, "_enabled": true, "__prefab": {"__id__": 211}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0589391c-1471-4f9f-8e6c-21e1c7aaafcd@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a0BDhKJqFJ7oy344wAecdY"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "14qjOHFOVAJ5xdVd31CIkg", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_active", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 206}, "_children": [], "_active": false, "_components": [{"__id__": 214}, {"__id__": 216}], "_prefab": {"__id__": 218}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 213}, "_enabled": true, "__prefab": {"__id__": 215}, "_contentSize": {"__type__": "cc.Size", "width": 101, "height": 97}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "010Iu1SopIUoO3RY2WE7Fz"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 213}, "_enabled": true, "__prefab": {"__id__": 217}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "*************-4002-90c7-1adf7e5c6f71@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "043K7KA9tA55pbdoFZhPCF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "130MMOHd1GjqZSxRBneKgA", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 206}, "_children": [], "_active": true, "_components": [{"__id__": 220}, {"__id__": 222}], "_prefab": {"__id__": 224}, "_lpos": {"__type__": "cc.Vec3", "x": 10.63, "y": -30, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 219}, "_enabled": true, "__prefab": {"__id__": 221}, "_contentSize": {"__type__": "cc.Size", "width": 55.98409592669416, "height": 58.92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aaiBO2SL1BcJ8zd8nLJ++0"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 219}, "_enabled": true, "__prefab": {"__id__": 223}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 242, "g": 220, "b": 72, "a": 255}, "_string": "白银", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26.208333333333332, "_fontSize": 25, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 42, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 67, "g": 39, "b": 12, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "14j6k/jd9PSqCkHm0LffXC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "232Cukct9LxIYyOnYStryu", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 206}, "_enabled": true, "__prefab": {"__id__": 226}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "19AdwQeHBPaJnb7wZ9+/IM"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 206}, "_enabled": true, "__prefab": {"__id__": 228}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "daKbetnXRLk7dZoxUCZT0k"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8fcMF59hlOy5Kq3CCZ1BXt", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_lv3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 53}, "_children": [{"__id__": 231}, {"__id__": 237}, {"__id__": 243}], "_active": true, "_components": [{"__id__": 249}, {"__id__": 251}], "_prefab": {"__id__": 253}, "_lpos": {"__type__": "cc.Vec3", "x": 115, "y": -342, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_unactive", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 230}, "_children": [], "_active": true, "_components": [{"__id__": 232}, {"__id__": 234}], "_prefab": {"__id__": 236}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 231}, "_enabled": true, "__prefab": {"__id__": 233}, "_contentSize": {"__type__": "cc.Size", "width": 91, "height": 81}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d0I2pYWwJPJodI97gPiLTA"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 231}, "_enabled": true, "__prefab": {"__id__": 235}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "aad6bf96-1d9e-4138-9678-1a6812b3d898@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d7hwDpUxpPBIkaQAdiGBlU"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f0LeJyxuFE970KFCtLiAVb", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_active", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 230}, "_children": [], "_active": false, "_components": [{"__id__": 238}, {"__id__": 240}], "_prefab": {"__id__": 242}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 237}, "_enabled": true, "__prefab": {"__id__": 239}, "_contentSize": {"__type__": "cc.Size", "width": 106, "height": 96}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "45KJcz1stOBqukAP2prz49"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 237}, "_enabled": true, "__prefab": {"__id__": 241}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2c3cb976-3b3c-428b-9b83-ee98d383b2dd@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "58JJbFOeNH0q1cDWPPDKHU"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "79nn2goB9HLoA2TyH7j/8w", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 230}, "_children": [], "_active": true, "_components": [{"__id__": 244}, {"__id__": 246}], "_prefab": {"__id__": 248}, "_lpos": {"__type__": "cc.Vec3", "x": 10.63, "y": -30, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 243}, "_enabled": true, "__prefab": {"__id__": 245}, "_contentSize": {"__type__": "cc.Size", "width": 55.98409592669416, "height": 58.92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b5khptN4dHdYjckjT8Q6oC"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 243}, "_enabled": true, "__prefab": {"__id__": 247}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 242, "g": 220, "b": 72, "a": 255}, "_string": "紫金", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26.208333333333332, "_fontSize": 25, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 42, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 67, "g": 39, "b": 12, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "88/z/z3jFKoLLdP5T1Gm9t"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1dZ7Gt2N5Hh5tAS7nkzfXy", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 230}, "_enabled": true, "__prefab": {"__id__": 250}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f5Cd73Qa9M5oBULDhJChmo"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 230}, "_enabled": true, "__prefab": {"__id__": 252}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1eL4grSptJka3gFguR+e/T"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "34zOkliQRK/58rvbhgRMMT", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 53}, "_enabled": true, "__prefab": {"__id__": 255}, "_contentSize": {"__type__": "cc.Size", "width": 554, "height": 801}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c1Sc7noWtB74EGvQW5IfSz"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "91uXUOfPFFxKaR+sO/dxE6", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 258}, "_contentSize": {"__type__": "cc.Size", "width": 609, "height": 892}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "24cTmcMChP/anAsBiQ2JU9"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 260}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "f455f8cd-3502-417f-ac6b-a83c72ca8523@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6aPyEsgJRFs5TM7YTzyCUG"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "80luAUp1FE5pjSVJpSSwQb", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 263}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8as6+oFlRCrpfqG5SMbpjJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "29Dc+KYnZABK3glomZEVg1", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 266}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 265}, "asset": {"__uuid__": "89b64f36-c02b-43d5-b79f-998781162d58", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 267}, "targetOverrides": [{"__id__": 316}, {"__id__": 320}, {"__id__": 323}, {"__id__": 326}, {"__id__": 329}, {"__id__": 332}, {"__id__": 335}, {"__id__": 338}, {"__id__": 341}, {"__id__": 344}, {"__id__": 347}]}, {"__type__": "cc.PrefabInstance", "fileId": "b9hJgILSxLQagE62BI3FZp", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 268}, {"__id__": 270}, {"__id__": 272}, {"__id__": 274}, {"__id__": 276}, {"__id__": 278}, {"__id__": 280}, {"__id__": 282}, {"__id__": 284}, {"__id__": 286}, {"__id__": 288}, {"__id__": 290}, {"__id__": 292}, {"__id__": 294}, {"__id__": 296}, {"__id__": 298}, {"__id__": 300}, {"__id__": 302}, {"__id__": 304}, {"__id__": 306}, {"__id__": 308}, {"__id__": 310}, {"__id__": 312}, {"__id__": 314}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 269}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 6.286999999999978, "y": 9.001999999999953, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 271}, "propertyPath": ["_name"], "value": "UIClubTips"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 273}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 275}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 277}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 750, "height": 1100}}, {"__type__": "cc.TargetInfo", "localID": ["82np0Pq5JHso2E+BFt167N"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 279}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 71, "height": 71}}, {"__type__": "cc.TargetInfo", "localID": ["4dpZ8WKpBACYHar+3RCucw", "c3FKi/GdFIerw2SbrXGJ9M"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 281}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 58, "height": 58}}, {"__type__": "cc.TargetInfo", "localID": ["4dpZ8WKpBACYHar+3RCucw", "7fVX9mvddJUaVADkQ2cBdp"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 283}, "propertyPath": ["_left"], "value": 6.2870000000000035}, {"__type__": "cc.TargetInfo", "localID": ["22mrFEaUtLsZFJIBbVqE19"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 285}, "propertyPath": ["_right"], "value": -6.2870000000000035}, {"__type__": "cc.TargetInfo", "localID": ["22mrFEaUtLsZFJIBbVqE19"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 287}, "propertyPath": ["_top"], "value": -9.001999999999954}, {"__type__": "cc.TargetInfo", "localID": ["22mrFEaUtLsZFJIBbVqE19"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 289}, "propertyPath": ["_bottom"], "value": 9.001999999999954}, {"__type__": "cc.TargetInfo", "localID": ["22mrFEaUtLsZFJIBbVqE19"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 291}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -925.807, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["2aF+mNuEpO0pNy+KTQUZPD"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 293}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -1158.705, "y": -50, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["4dpZ8WKpBACYHar+3RCucw", "f5VJJTfHtB5aqbwW4jl16A"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 295}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 297}, "propertyPath": ["_active"], "value": true}, {"__type__": "cc.TargetInfo", "localID": ["4dpZ8WKpBACYHar+3RCucw", "f5VJJTfHtB5aqbwW4jl16A"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 299}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 0.6228070175438597, "y": 0.6228070175438597, "z": 1}}, {"__type__": "cc.TargetInfo", "localID": ["4dpZ8WKpBACYHar+3RCucw", "05/dyc9JZPzqOEp1/yFyae"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 301}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["4dpZ8WKpBACYHar+3RCucw", "83uRFCZDpOWIYKQdKjv3ks"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 303}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 58, "height": 58}}, {"__type__": "cc.TargetInfo", "localID": ["4dpZ8WKpBACYHar+3RCucw", "b8lliivRBLlLauY3fNUDkb"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 305}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["4dpZ8WKpBACYHar+3RCucw", "eeCRBl0e1DboDDLjZnVY/4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 307}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["4dpZ8WKpBACYHar+3RCucw", "a1tsAamAJGy7Lb6gW02dNW"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 309}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 59, "height": 32.980000000000004}}, {"__type__": "cc.TargetInfo", "localID": ["4dpZ8WKpBACYHar+3RCucw", "07IRwvCO9Nyq7noluFiXK4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 311}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 31.5, "y": -19.009999999999998, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["4dpZ8WKpBACYHar+3RCucw", "3f2CI9oKNLFK7+fzc6STXj"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 313}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["4dpZ8WKpBACYHar+3RCucw", "3f2CI9oKNLFK7+fzc6STXj"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 315}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["4dpZ8WKpBACYHar+3RCucw", "f5VJJTfHtB5aqbwW4jl16A"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 317}, "sourceInfo": {"__id__": 318}, "propertyPath": ["bgColor"], "target": {"__id__": 317}, "targetInfo": {"__id__": 319}}, {"__type__": "cc.Node", "__editorExtras__": {}}, {"__type__": "cc.TargetInfo", "localID": ["66mTC3DbNCcIAV99gOJC6M"]}, {"__type__": "cc.TargetInfo", "localID": ["ef0OK1CglK8Y/O9Qla7khp"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 317}, "sourceInfo": {"__id__": 321}, "propertyPath": ["bgIcon"], "target": {"__id__": 317}, "targetInfo": {"__id__": 322}}, {"__type__": "cc.TargetInfo", "localID": ["66mTC3DbNCcIAV99gOJC6M"]}, {"__type__": "cc.TargetInfo", "localID": ["d1PcUCEbVCQ7t7NK/Q0EQM"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 317}, "sourceInfo": {"__id__": 324}, "propertyPath": ["lblHas"], "target": {"__id__": 317}, "targetInfo": {"__id__": 325}}, {"__type__": "cc.TargetInfo", "localID": ["66mTC3DbNCcIAV99gOJC6M"]}, {"__type__": "cc.TargetInfo", "localID": ["8aXJ8fJdZOsa3oa+CmpHN6"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 317}, "sourceInfo": {"__id__": 327}, "propertyPath": ["lblName"], "target": {"__id__": 317}, "targetInfo": {"__id__": 328}}, {"__type__": "cc.TargetInfo", "localID": ["66mTC3DbNCcIAV99gOJC6M"]}, {"__type__": "cc.TargetInfo", "localID": ["2bAKtKLnZKCYBH2GWUXLMe"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 317}, "sourceInfo": {"__id__": 330}, "propertyPath": ["nodeTips"], "target": {"__id__": 317}, "targetInfo": {"__id__": 331}}, {"__type__": "cc.TargetInfo", "localID": ["66mTC3DbNCcIAV99gOJC6M"]}, {"__type__": "cc.TargetInfo", "localID": ["92Zd3+BPtDyqBDJ5KJAHiv"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 317}, "sourceInfo": {"__id__": 333}, "propertyPath": ["bgT<PERSON>s<PERSON><PERSON>nt"], "target": {"__id__": 317}, "targetInfo": {"__id__": 334}}, {"__type__": "cc.TargetInfo", "localID": ["66mTC3DbNCcIAV99gOJC6M"]}, {"__type__": "cc.TargetInfo", "localID": ["9bNy6qjUJAlYXrFfJFfxs0"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 317}, "sourceInfo": {"__id__": 336}, "propertyPath": ["bgItemColor"], "target": {"__id__": 317}, "targetInfo": {"__id__": 337}}, {"__type__": "cc.TargetInfo", "localID": ["66mTC3DbNCcIAV99gOJC6M"]}, {"__type__": "cc.TargetInfo", "localID": ["3eIOGCiW5HbYMZaWwXgGaX"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 317}, "sourceInfo": {"__id__": 339}, "propertyPath": ["bgItemIcon"], "target": {"__id__": 317}, "targetInfo": {"__id__": 340}}, {"__type__": "cc.TargetInfo", "localID": ["66mTC3DbNCcIAV99gOJC6M"]}, {"__type__": "cc.TargetInfo", "localID": ["35RZGvJ0hBD5629CGQ6j+D"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 317}, "sourceInfo": {"__id__": 342}, "propertyPath": ["lblItemName"], "target": {"__id__": 317}, "targetInfo": {"__id__": 343}}, {"__type__": "cc.TargetInfo", "localID": ["66mTC3DbNCcIAV99gOJC6M"]}, {"__type__": "cc.TargetInfo", "localID": ["43L6/IG/9I/pZCDUJhm1Cm"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 317}, "sourceInfo": {"__id__": 345}, "propertyPath": ["lblItemNum"], "target": {"__id__": 317}, "targetInfo": {"__id__": 346}}, {"__type__": "cc.TargetInfo", "localID": ["66mTC3DbNCcIAV99gOJC6M"]}, {"__type__": "cc.TargetInfo", "localID": ["c4pyc25U9Dxaj24eKfnB/4"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 317}, "sourceInfo": {"__id__": 348}, "propertyPath": ["lblItemInfo"], "target": {"__id__": 317}, "targetInfo": {"__id__": 349}}, {"__type__": "cc.TargetInfo", "localID": ["66mTC3DbNCcIAV99gOJC6M"]}, {"__type__": "cc.TargetInfo", "localID": ["9aY8Z0t2hGpYCt3Bxc17wV"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 351}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a8l4qv6+tM5p4sdl7EvKvn"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": [{"__id__": 353}, {"__id__": 356}], "nestedPrefabInstanceRoots": [{"__id__": 265}]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 28}, "sourceInfo": {"__id__": 354}, "propertyPath": ["graphics"], "target": {"__id__": 28}, "targetInfo": {"__id__": 355}}, {"__type__": "cc.TargetInfo", "localID": ["f2ofLYMWBPU4DygV2KdYZ4"]}, {"__type__": "cc.TargetInfo", "localID": ["9ddIeszclP2KkZe+N/mFmL"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 28}, "sourceInfo": {"__id__": 357}, "propertyPath": ["dialogTitle"], "target": {"__id__": 28}, "targetInfo": {"__id__": 358}}, {"__type__": "cc.TargetInfo", "localID": ["f2ofLYMWBPU4DygV2KdYZ4"]}, {"__type__": "cc.TargetInfo", "localID": ["e9cwQVQARBJrTgOq5ECZIX"]}]