import { _decorator, Node, Label, Layout, UITransform } from "cc";
import { ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { PupilModule } from "../PupilModule";
import { BasePupilInfoMessage } from "../../../../game/net/protocol/Pupil";
import { Sprite } from "cc";
import { Size } from "cc";
import { UIMgr } from "../../../../lib/ui/UIMgr";
import TipMgr from "../../../../lib/tips/TipMgr";
import Formate from "../../../../lib/utils/Formate";
import { PupilHeader } from "../prefab/ui/PupilHeader";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";

const { ccclass, property } = _decorator;
@ccclass("PupilWorkMarryltemViewHolder")
export class PupilWorkMarryltemViewHolder extends ViewHolder {
  @property(Node)
  private nodePupilLeft: Node;

  @property(Node)
  private nodePupilRight: Node;

  @property(Node)
  private nodeStart: Node;

  @property(Node)
  private nodeStop: Node;

  @property(Node)
  private nodeJob: Node;

  getViewType() {
    return 1;
  }

  private _pupilId: number;
  private _slotIndex: number;

  public init() {}

  public updateData(position: number, args: any) {
    this._pupilId = args.pupilId;
    this._slotIndex = args.slotIndex;

    const pupilMsg = PupilModule.data.allPupilMap[this._pupilId];
    this.setPupilNode(this.nodePupilLeft, pupilMsg.ownInfo);
    this.setPupilNode(this.nodePupilRight, pupilMsg.partnerInfo);

    // 任职显示
    this.nodeJob.getChildByName("bg_job3").active = PupilModule.data.pupilTrainMsg.workSlotList[0] == this._pupilId;
    this.nodeJob.getChildByName("bg_job2").active =
      PupilModule.data.pupilTrainMsg.workSlotList[1] == this._pupilId ||
      PupilModule.data.pupilTrainMsg.workSlotList[2] == this._pupilId;
    this.nodeJob.getChildByName("bg_job1").active =
      PupilModule.data.pupilTrainMsg.workSlotList[3] == this._pupilId ||
      PupilModule.data.pupilTrainMsg.workSlotList[4] == this._pupilId;

    // 上下阵控制按钮状态
    const isWorking = PupilModule.data.pupilTrainMsg.workSlotList.indexOf(pupilMsg.id) != -1;
    this.nodeStart.active = !isWorking;
    this.nodeStop.active = isWorking;

    // 高度重新适配=========begin=========
    const layoutLeft: Layout = this.nodePupilLeft.getComponent(Layout);
    // 本次更新就适配一次
    layoutLeft.updateLayout();
    const yLeft = layoutLeft.node.getComponent(UITransform).contentSize.y;

    const layoutRight: Layout = this.nodePupilRight.getComponent(Layout);
    // 本次更新就适配一次

    layoutRight.updateLayout();
    const yRight = layoutRight.node.getComponent(UITransform).contentSize.y;

    let y = Math.max(yLeft, yRight);

    let uiTransform = this.node.getComponent(UITransform);
    uiTransform.setContentSize(new Size(uiTransform.contentSize.x, y + 99));
    // 高度重新适配=========end=========

    this.node.getComponent(Layout).updateLayout();
  }

  // 设置单个徒弟节点内容
  private setPupilNode(nodePupil: Node, pupilInfo: BasePupilInfoMessage) {
    // 徒弟头像
    nodePupil.getChildByName("PupilHeader").getComponent(PupilHeader).setHeaderByNameId(pupilInfo.nameId);

    // 徒弟名称
    nodePupil.getChildByName("lbl_pupil_name").getComponent(Label).string = PupilModule.service.getPupilName(
      pupilInfo.nameId
    );

    // 徒弟出师标签背景
    PupilModule.service.setPupilAdultAttrBg(
      nodePupil.getChildByName("bg_attr_new").getComponent(Sprite),
      pupilInfo.talentId
    );

    // 出师属性配置
    nodePupil.getChildByPath("bg_attr_new/Label").getComponent(Label).string = Formate.formatAttribute(
      pupilInfo.adultAttrList[0],
      pupilInfo.adultAttrList[1]
    );

    // 设置初始属性
    const layoutAttr = nodePupil.getChildByName("layout_attr").getComponent(Layout);
    PupilModule.service.setLayoutAttr(layoutAttr, pupilInfo.initAttrList);

    // 单条属性居中配置
    if (pupilInfo.initAttrList.length <= 2) {
      layoutAttr.constraintNum = 1;
      layoutAttr.node.getComponent(UITransform).setContentSize(new Size(128, 1));
    } else {
      layoutAttr.constraintNum = 2;
      layoutAttr.node.getComponent(UITransform).setContentSize(new Size(260, 1));
    }
    // 本次更新就适配一次
    layoutAttr.updateLayout();
  }

  public onStartWork() {
    AudioMgr.instance.playEffect(1557);
    PupilModule.api.work(this._pupilId, this._slotIndex, (data) => {
      UIMgr.instance.back();
      TipMgr.showTip("上阵成功");
    });
  }

  public onStopWork() {
    AudioMgr.instance.playEffect(1557);
    PupilModule.api.leaveWork(this._slotIndex, (data) => {
      UIMgr.instance.back();
      TipMgr.showTip("下阵成功");
    });
  }
}
