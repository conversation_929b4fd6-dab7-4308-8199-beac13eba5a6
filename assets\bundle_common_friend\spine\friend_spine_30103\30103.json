{"skeleton": {"hash": "q7RMhmhsl9XikUc6RPPDT7oQL0I", "spine": "3.8.75", "x": -387.8, "y": -587.9, "width": 718.2, "height": 1205.4, "images": "./images/", "audio": "D:/spine/琵琶女"}, "bones": [{"name": "root", "y": -478, "scaleX": 0.7, "scaleY": 0.7}, {"name": "st", "parent": "root", "length": 244.55, "rotation": -3.72, "x": -87.91, "y": 465.7}, {"name": "st2", "parent": "st", "length": 163.27, "rotation": 95.75, "x": 98.68, "y": 18}, {"name": "st3", "parent": "st2", "length": 145.67, "rotation": -40.09, "x": 328.22, "y": -44.86}, {"name": "st4", "parent": "st2", "length": 197.03, "rotation": -98.33, "x": 268.2, "y": 94.77}, {"name": "st1", "parent": "st3", "length": 151.5, "rotation": -95.71, "x": 154.17, "y": -57.28}, {"name": "zs1", "parent": "st1", "length": 304.37, "rotation": -30.97, "x": 154.85, "y": 0.6}, {"name": "zs2", "parent": "zs1", "length": 126.02, "rotation": -82.98, "x": 308.31, "y": -6.9}, {"name": "zs4", "parent": "zs2", "length": 77.78, "rotation": -2.2, "x": 137.49, "y": -1.66}, {"name": "zs3", "parent": "zs4", "length": 70.85, "rotation": 4.13, "x": 64.73, "y": -3.54}, {"name": "zs5", "parent": "zs3", "length": 40.08, "rotation": -52.85, "x": 25.03, "y": -34.11}, {"name": "zs6", "parent": "zs5", "length": 23.05, "rotation": 56.13, "x": 42.82, "y": 5.22}, {"name": "zs8", "parent": "zs3", "length": 38.83, "rotation": -0.6, "x": 74.66, "y": -25.82}, {"name": "zs9", "parent": "zs8", "length": 37.67, "rotation": -2.6, "x": 44.15, "y": -0.09}, {"name": "zs10", "parent": "zs3", "length": 40.42, "rotation": 2.89, "x": 74.07, "y": 10.77}, {"name": "zs11", "parent": "zs10", "length": 31.08, "rotation": -27.08, "x": 41.7, "y": -0.19}, {"name": "zs12", "parent": "zs11", "length": 13.84, "rotation": -18.97, "x": 35.17}, {"name": "ys1", "parent": "st3", "length": 68.05, "rotation": 143.54, "x": 30.66, "y": 91.08}, {"name": "ys2", "parent": "ys1", "length": 315.46, "rotation": 61.02, "x": 70.75, "y": 9.72}, {"name": "ys3", "parent": "ys2", "length": 101.75, "rotation": -106.88, "x": 314.2, "y": -3.41}, {"name": "ys5", "parent": "ys3", "length": 167.83, "rotation": -20.48, "x": 108, "y": 1.32}, {"name": "ys4", "parent": "ys5", "length": 98.29, "rotation": -33.84, "x": 167.97, "y": -1.42}, {"name": "ys6", "parent": "ys4", "length": 40.11, "rotation": -55.13, "x": 99.89, "y": -3.54}, {"name": "ys7", "parent": "ys6", "length": 41.32, "rotation": -53.54, "x": 45.98, "y": -6}, {"name": "ys9", "parent": "ys4", "length": 36.75, "rotation": 9.77, "x": 128.7, "y": 11.87}, {"name": "ys10", "parent": "ys9", "length": 31.01, "rotation": -90.62, "x": 36.94, "y": -8.06}, {"name": "bozi", "parent": "st3", "length": 109.1, "rotation": 29.59, "x": 108.14, "y": 8.11}, {"name": "t1", "parent": "bozi", "length": 183.21, "rotation": 35.69, "x": 96.2, "y": 4.26}, {"name": "yy", "parent": "t1", "length": 46.58, "rotation": 50.08, "x": 100.26, "y": 57.47}, {"name": "zy", "parent": "t1", "length": 59.93, "rotation": -97.23, "x": 83.35, "y": 4.99}, {"name": "t3", "parent": "t1", "length": 60.4, "rotation": -30.97, "x": 212.2, "y": 0.13}, {"name": "t4", "parent": "t3", "length": 64.33, "rotation": -43.74, "x": 68.48, "y": -2.44}, {"name": "t5", "parent": "t4", "length": 61.86, "rotation": 20.92, "x": 77.63, "y": -0.12}, {"name": "t6", "parent": "t5", "length": 53.73, "rotation": -5.66, "x": 78.65, "y": 8.4}, {"name": "t7", "parent": "t6", "length": 50.32, "rotation": -14.36, "x": 72.37, "y": 0.07}, {"name": "t9", "parent": "t3", "length": 66.23, "rotation": -177.96, "x": -54.04, "y": -197.61}, {"name": "t10", "parent": "t9", "length": 62.74, "rotation": 8.95, "x": 75.11, "y": 0.27}, {"name": "t11", "parent": "t10", "length": 67.1, "rotation": 6.39, "x": 67.77, "y": 0.36}, {"name": "t12", "parent": "t11", "length": 56.63, "rotation": 29.25, "x": 67.1}, {"name": "t13", "parent": "t12", "length": 52.73, "rotation": 34.13, "x": 56.63}, {"name": "t14", "parent": "t3", "length": 36.9, "rotation": -172.26, "x": -115.52, "y": -169.94}, {"name": "t15", "parent": "t14", "length": 22.26, "rotation": -3.99, "x": 46.61, "y": -8.4}, {"name": "t16", "parent": "t15", "length": 18.42, "rotation": -1.84, "x": 22.25}, {"name": "t17", "parent": "t16", "length": 16.1, "rotation": -2.32, "x": 18.42}, {"name": "t18", "parent": "t17", "length": 12.59, "rotation": 0.17, "x": 16.98, "y": 0.06}, {"name": "t19", "parent": "t18", "length": 11.42, "rotation": -0.41, "x": 12.59}, {"name": "t8", "parent": "t14", "length": 13.73, "rotation": -2.77, "x": 46.3, "y": 11.23}, {"name": "t20", "parent": "t8", "length": 13.73, "x": 13.73}, {"name": "t21", "parent": "t20", "length": 13.55, "rotation": -8.65, "x": 13.73}, {"name": "t22", "parent": "t1", "length": 28.35, "rotation": 147.43, "x": 78.77, "y": 100.33}, {"name": "t23", "parent": "t22", "length": 16.15, "rotation": 15.85, "x": 36.24, "y": -5.46}, {"name": "t24", "parent": "t23", "length": 17.88, "rotation": -1.03, "x": 16.15}, {"name": "t25", "parent": "t24", "length": 15.68, "rotation": -15.92, "x": 17.88}, {"name": "t26", "parent": "t25", "length": 16.31, "rotation": -21.49, "x": 17.34, "y": -1.59}, {"name": "qin", "parent": "ys6", "length": 720.96, "rotation": -88.1, "x": 39.3, "y": -3.79}, {"name": "zs7", "parent": "zs2", "length": 50.68, "rotation": 100.54, "x": 110.6, "y": 17.22}, {"name": "zs13", "parent": "zs7", "length": 50.68, "x": 50.68}, {"name": "zs14", "parent": "zs13", "length": 50.68, "x": 50.68}, {"name": "zs15", "parent": "zs14", "length": 50.68, "x": 50.68}, {"name": "zs16", "parent": "zs15", "length": 50.68, "x": 50.68}, {"name": "zs17", "parent": "zs16", "length": 50.68, "x": 50.68}, {"name": "zs18", "parent": "zs17", "length": 50.68, "x": 50.68}, {"name": "ys8", "parent": "ys3", "length": 42, "rotation": 95.2, "x": 45.63, "y": 24.84}, {"name": "ys11", "parent": "ys8", "length": 42, "x": 42}, {"name": "ys12", "parent": "ys11", "length": 42, "rotation": 18, "x": 42}, {"name": "ys13", "parent": "ys12", "length": 42, "x": 42}, {"name": "ys14", "parent": "ys13", "length": 42, "rotation": 2.4, "x": 42}, {"name": "ys15", "parent": "ys14", "length": 42, "x": 42}, {"name": "ys16", "parent": "ys15", "length": 42, "x": 42}, {"name": "ys17", "parent": "ys16", "length": 42, "x": 42}, {"name": "ys18", "parent": "ys17", "length": 42, "rotation": -9.6, "x": 42}, {"name": "ys19", "parent": "ys18", "length": 42, "rotation": -7.2, "x": 42}, {"name": "ys20", "parent": "ys19", "length": 42, "rotation": -33.6, "x": 42}, {"name": "ys21", "parent": "ys20", "length": 42, "rotation": -25.2, "x": 42}, {"name": "st5", "parent": "st", "length": 134.02, "rotation": -91.04, "x": 100.21, "y": -35.61}, {"name": "st6", "parent": "st5", "length": 99.16, "rotation": -11.06, "x": 146.7, "y": 1.06}, {"name": "st7", "parent": "st6", "length": 109.53, "rotation": -11.86, "x": 99.16}, {"name": "st8", "parent": "st7", "length": 128.78, "rotation": 7.46, "x": 109.53}, {"name": "st9", "parent": "st8", "length": 113.6, "rotation": 13.8, "x": 128.78}, {"name": "st10", "parent": "st", "length": 306.34, "rotation": -134.64, "x": -77.51, "y": -259.1}, {"name": "st11", "parent": "st10", "length": 150.92, "rotation": -21.29, "x": 306.34}, {"name": "st12", "parent": "st1", "length": 249.89, "rotation": -55.75, "x": 84.27, "y": -36.27}, {"name": "st13", "parent": "st12", "length": 205.1, "rotation": 16.65, "x": 249.89}, {"name": "st14", "parent": "st13", "length": 91.52, "rotation": 0.86, "x": 205.1}, {"name": "st15", "parent": "st14", "length": 90.63, "rotation": -7.99, "x": 91.52}, {"name": "st16", "parent": "st15", "length": 86.58, "rotation": 7.39, "x": 90.63}, {"name": "st17", "parent": "st16", "length": 89.05, "rotation": -6.36, "x": 86.58}, {"name": "st18", "parent": "st17", "length": 85.48, "rotation": 8.61, "x": 89.05}, {"name": "st19", "parent": "st18", "length": 69.42, "rotation": 10.46, "x": 85.48}, {"name": "t27", "parent": "t1", "length": 41.72, "rotation": 151.7, "x": 42.73, "y": 96.52}, {"name": "t28", "parent": "t27", "length": 39.07, "rotation": -17.72, "x": 41.72}, {"name": "t29", "parent": "t28", "length": 38.21, "rotation": -13.58, "x": 39.07}, {"name": "t30", "parent": "t29", "length": 39.55, "rotation": -15.04, "x": 38.21}, {"name": "t31", "parent": "t30", "length": 52.87, "rotation": 1.82, "x": 39.55}, {"name": "t32", "parent": "t31", "length": 40.17, "rotation": 10.98, "x": 52.87}, {"name": "t33", "parent": "t32", "length": 34.85, "rotation": 9.22, "x": 40.17}, {"name": "t34", "parent": "t33", "length": 38.33, "rotation": 6.23, "x": 34.85}, {"name": "t36", "parent": "t3", "length": 66.42, "rotation": -173.57, "x": -116.32, "y": -173.48}, {"name": "t37", "parent": "t36", "length": 75.71, "rotation": -0.62, "x": 66.42}, {"name": "t38", "parent": "t37", "length": 89.08, "rotation": 2.31, "x": 76.14, "y": 0.5}, {"name": "t39", "parent": "t38", "length": 74.05, "rotation": -8.04, "x": 89.08}, {"name": "t40", "parent": "t39", "length": 84.53, "rotation": -2.31, "x": 74.05}, {"name": "t41", "parent": "t40", "length": 53.05, "rotation": -8.32, "x": 85.53, "y": 1.05}, {"name": "t42", "parent": "t41", "length": 61.81, "rotation": 4.91, "x": 53.05}, {"name": "t43", "parent": "t42", "length": 44.18, "rotation": 30.09, "x": 61.81}, {"name": "t44", "parent": "t43", "length": 31.6, "rotation": 39.27, "x": 43.54, "y": 0.2}, {"name": "zhit", "parent": "qin", "length": 10.52, "rotation": 144.27, "x": -18.62, "y": 24.34}, {"name": "t35", "parent": "t1", "length": 20.99, "rotation": 157.98, "x": -3.12, "y": 72.6}, {"name": "t45", "parent": "t35", "length": 15.58, "rotation": -6.59, "x": 23.74, "y": 0.89}], "slots": [{"name": "bg1", "bone": "root", "attachment": "bg1"}, {"name": "t7", "bone": "t35", "attachment": "t7"}, {"name": "t8", "bone": "t9", "attachment": "t8"}, {"name": "ys1", "bone": "ys1", "attachment": "ys1"}, {"name": "st", "bone": "st", "attachment": "st"}, {"name": "st1", "bone": "st1", "attachment": "st1"}, {"name": "qin", "bone": "qin", "attachment": "qin"}, {"name": "ys2", "bone": "ys5", "attachment": "ys2"}, {"name": "ys3", "bone": "ys9", "attachment": "ys3"}, {"name": "zhit", "bone": "zhit", "attachment": "zhit"}, {"name": "bozi", "bone": "bozi", "attachment": "bozi"}, {"name": "zs1", "bone": "zs1", "attachment": "zs1"}, {"name": "zs2", "bone": "zs4", "attachment": "zs2"}, {"name": "zs3", "bone": "zs3", "attachment": "zs3"}, {"name": "zs4", "bone": "zs10", "attachment": "zs4"}, {"name": "zs5", "bone": "zs8", "attachment": "zs5"}, {"name": "t1", "bone": "t1", "attachment": "t1"}, {"name": "zy", "bone": "zy", "attachment": "zy"}, {"name": "yy", "bone": "yy", "attachment": "yy"}, {"name": "t3", "bone": "t36", "attachment": "t3"}, {"name": "t2", "bone": "t3", "attachment": "t2"}, {"name": "t4", "bone": "t27", "attachment": "t4"}, {"name": "t5", "bone": "t22", "attachment": "t5"}, {"name": "t6", "bone": "t8", "attachment": "t6"}], "skins": [{"name": "default", "attachments": {"t4": {"t4": {"type": "mesh", "uvs": [0.89253, 0, 0.98215, 0.04633, 1, 0.06724, 1, 0.09326, 0.98639, 0.15532, 0.97442, 0.20987, 0.96281, 0.26281, 0.92077, 0.31, 0.88319, 0.35217, 0.83799, 0.4029, 0.7521, 0.4475, 0.67524, 0.48741, 0.58946, 0.53195, 0.50544, 0.57558, 0.43151, 0.61396, 0.35937, 0.65142, 0.3172, 0.6763, 0.26416, 0.72438, 0.21467, 0.76924, 0.1666, 0.82428, 0.13686, 0.86382, 0.10179, 0.91047, 0.0682, 0.95515, 0.03931, 0.99357, 0.01122, 1, 0.01087, 0.96396, 0.01059, 0.93432, 0.03453, 0.90743, 0.06294, 0.87553, 0.10271, 0.83086, 0.145, 0.78337, 0.18982, 0.73303, 0.24047, 0.67615, 0.27591, 0.65119, 0.30525, 0.63053, 0.37627, 0.5805, 0.46124, 0.53581, 0.53916, 0.49483, 0.62281, 0.45083, 0.68529, 0.41797, 0.76212, 0.37756, 0.79628, 0.33159, 0.83255, 0.2805, 0.86298, 0.22477, 0.88064, 0.18724, 0.8826, 0.09665, 0.87396, 0.03112, 0.87554, 0], "triangles": [21, 28, 20, 27, 28, 21, 22, 27, 21, 26, 27, 22, 25, 26, 22, 23, 25, 22, 24, 25, 23, 30, 31, 18, 19, 30, 18, 29, 30, 19, 20, 29, 19, 28, 29, 20, 16, 34, 15, 33, 34, 16, 32, 33, 16, 17, 32, 16, 31, 32, 17, 18, 31, 17, 36, 37, 12, 13, 36, 12, 35, 36, 13, 14, 35, 13, 15, 34, 35, 14, 15, 35, 11, 38, 39, 11, 39, 10, 37, 38, 11, 12, 37, 11, 8, 41, 7, 40, 41, 8, 9, 40, 8, 10, 39, 40, 10, 40, 9, 43, 44, 5, 6, 43, 5, 42, 43, 6, 7, 42, 6, 41, 42, 7, 45, 1, 2, 0, 46, 47, 1, 46, 0, 3, 45, 2, 1, 45, 46, 4, 45, 3, 44, 45, 4, 5, 44, 4], "vertices": [1, 89, -17.25, -6.19, 1, 1, 89, -3.69, 9.12, 1, 1, 89, 2.51, 12.24, 1, 1, 89, 10.29, 12.38, 1, 1, 89, 28.88, 10.45, 1, 2, 89, 45.23, 8.74, 0.28863, 90, 0.68, 9.4, 0.71137, 1, 90, 16.29, 12.65, 1, 2, 90, 31.93, 10.51, 0.95223, 91, -9.42, 8.54, 0.04777, 2, 90, 45.9, 8.6, 0.12055, 91, 4.62, 9.96, 0.87945, 1, 91, 21.49, 11.67, 1, 2, 91, 40.48, 6.63, 0.38374, 92, 0.47, 6.99, 0.61626, 1, 92, 18.06, 7.04, 1, 2, 92, 37.68, 7.1, 0.67603, 93, -1.64, 7.15, 0.32397, 1, 93, 17.57, 6.6, 1, 1, 93, 34.47, 6.11, 1, 2, 93, 50.97, 5.63, 0.6761, 94, -0.79, 5.89, 0.3239, 1, 94, 9.35, 4.29, 1, 1, 94, 26.25, 5.12, 1, 2, 94, 42.01, 5.9, 0.23479, 95, 2.76, 5.53, 0.76521, 1, 95, 21.09, 5.3, 1, 2, 95, 33.92, 5.86, 0.65103, 96, -0.29, 5.92, 0.34897, 1, 96, 14.81, 4.94, 1, 1, 96, 29.29, 4, 1, 1, 96, 41.73, 3.19, 1, 1, 96, 45.1, -0.64, 1, 1, 96, 34.94, -4.23, 1, 1, 96, 26.58, -7.19, 1, 1, 96, 17.67, -6.03, 1, 1, 96, 7.09, -4.66, 1, 1, 95, 27.48, -3.55, 1, 1, 95, 11.6, -3.23, 1, 1, 94, 35.47, -3.69, 1, 2, 94, 16.64, -6.35, 0.99999, 95, -24.24, -2.5, 1e-05, 1, 94, 7.12, -5.69, 1, 2, 93, 53.1, -5.19, 0.54009, 94, -0.77, -5.14, 0.45991, 1, 93, 34.11, -7.53, 1, 1, 93, 14.56, -7.1, 1, 2, 92, 36.39, -6.79, 0.80592, 93, -3.37, -6.69, 0.19408, 1, 92, 17.14, -6.97, 1, 2, 91, 39.03, -7.58, 0.27658, 92, 2.76, -7.1, 0.72342, 1, 91, 21.92, -3.15, 1, 2, 90, 44.78, -7.21, 0.01433, 91, 7.24, -5.66, 0.98567, 1, 90, 28.35, -6.36, 1, 2, 89, 50.03, -9.89, 0.00061, 90, 10.93, -6.89, 0.99939, 2, 89, 38.76, -7.13, 0.69114, 90, -0.65, -7.7, 0.30886, 1, 89, 11.67, -7.32, 1, 1, 89, -7.89, -9.14, 1, 1, 89, -17.2, -9.05, 1], "hull": 48, "edges": [0, 94, 0, 2, 2, 4, 4, 6, 30, 32, 36, 38, 46, 48, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 78, 80, 76, 78, 20, 22, 74, 76, 22, 24, 70, 72, 72, 74, 24, 26, 26, 28, 28, 30, 68, 70, 64, 66, 66, 68, 32, 34, 34, 36, 62, 64, 60, 62, 38, 40, 58, 60, 56, 58, 40, 42, 52, 54, 54, 56, 42, 44, 44, 46, 48, 50, 50, 52], "width": 131, "height": 233}}, "t5": {"t5": {"type": "mesh", "uvs": [0.59349, 0.02542, 0.64081, 0.07401, 0.74004, 0.14734, 0.88012, 0.21293, 0.79149, 0.28866, 0.78906, 0.3506, 0.7863, 0.42076, 0.78298, 0.50524, 0.78016, 0.57682, 0.77667, 0.66577, 0.77254, 0.77069, 0.69852, 0.82699, 0.62198, 0.8852, 0.53749, 0.89686, 0.43977, 0.91034, 0.29933, 0.92973, 0.21053, 0.96203, 0.19581, 0.91377, 0.18067, 0.86414, 0.16593, 0.81582, 0.17985, 0.75315, 0.19905, 0.6667, 0.21844, 0.57941, 0.23607, 0.50004, 0.25446, 0.41721, 0.27808, 0.36935, 0.29945, 0.32603, 0.15999, 0.283, 0.01965, 0.23968, 0.0191, 0.13287, 0.21284, 0.0463, 0.3838, 0.01095], "triangles": [16, 17, 15, 14, 15, 18, 15, 17, 18, 13, 14, 19, 11, 12, 13, 13, 20, 11, 18, 19, 14, 13, 19, 20, 11, 20, 10, 20, 21, 10, 10, 21, 9, 21, 22, 9, 9, 22, 8, 22, 23, 8, 8, 23, 7, 23, 24, 7, 7, 24, 6, 24, 25, 6, 6, 25, 5, 5, 25, 26, 5, 26, 4, 2, 26, 27, 26, 2, 4, 2, 27, 1, 0, 1, 31, 31, 1, 30, 27, 30, 1, 4, 2, 3, 30, 27, 29, 27, 28, 29], "vertices": [1, 49, -4.2, 2.35, 1, 1, 49, 1.49, 5.26, 1, 1, 49, 9.93, 11.03, 1, 2, 49, 17.24, 18.75, 0.9993, 51, -28.32, 27.98, 0.0007, 3, 49, 26.85, 15.2, 0.91808, 50, -3.39, 22.44, 0.05084, 51, -19.93, 22.09, 0.03108, 3, 49, 34.39, 15.79, 0.64225, 50, 4.02, 20.95, 0.21876, 51, -12.5, 20.73, 0.13899, 3, 49, 42.93, 16.45, 0.29179, 50, 12.41, 19.25, 0.3031, 51, -4.08, 19.18, 0.40511, 4, 49, 53.2, 17.25, 0.06579, 50, 22.52, 17.21, 0.10607, 51, 6.06, 17.32, 0.81083, 52, -16.12, 13.42, 0.01731, 4, 49, 61.91, 17.92, 0.0089, 50, 31.08, 15.48, 0.0062, 51, 14.65, 15.75, 0.77705, 52, -7.43, 14.26, 0.20786, 2, 51, 25.33, 13.79, 0.22439, 52, 3.37, 15.31, 0.77561, 3, 51, 37.92, 11.48, 0.00155, 52, 16.12, 16.54, 0.94978, 53, -7.78, 16.42, 0.04867, 2, 52, 23.36, 13.64, 0.71579, 53, 0.02, 16.37, 0.28421, 2, 52, 30.84, 10.63, 0.39798, 53, 8.09, 16.32, 0.60202, 2, 52, 32.73, 6.59, 0.26124, 53, 11.33, 13.25, 0.73876, 2, 52, 34.92, 1.92, 0.09828, 53, 15.07, 9.71, 0.90172, 2, 52, 38.06, -4.79, 0.00507, 53, 20.45, 4.61, 0.99493, 1, 53, 26.01, 2.54, 1, 1, 53, 21.15, -0.87, 1, 1, 53, 16.16, -4.38, 1, 2, 52, 25, -12.98, 0.00022, 53, 11.3, -7.79, 0.99978, 3, 51, 30.94, -17.39, 0.00183, 52, 17.32, -13.15, 0.13684, 53, 4.22, -10.76, 0.86133, 3, 51, 20.69, -14.71, 0.17475, 52, 6.74, -13.38, 0.5591, 53, -5.55, -14.86, 0.26615, 4, 50, 26.27, -12.19, 0.00497, 51, 10.34, -12.01, 0.78201, 52, -3.96, -13.61, 0.18711, 53, -15.41, -18.99, 0.02591, 4, 50, 16.91, -9.56, 0.33149, 51, 0.94, -9.55, 0.66402, 52, -13.68, -13.83, 0.00442, 53, -24.38, -22.75, 6e-05, 2, 50, 7.14, -6.82, 0.98667, 51, -8.88, -6.98, 0.01333, 1, 50, 1.62, -4.59, 1, 2, 49, 33.69, -8.87, 0.24572, 50, -3.38, -2.58, 0.75428, 2, 49, 29.11, -16.3, 0.70478, 50, -9.82, -8.48, 0.29522, 2, 49, 24.51, -23.78, 0.82183, 50, -16.29, -14.42, 0.17817, 2, 49, 11.54, -25.02, 0.91597, 50, -29.11, -12.07, 0.08403, 2, 49, 0.12, -16.36, 0.98243, 50, -37.73, -0.62, 0.01757, 2, 49, -4.98, -8.26, 0.9982, 50, -40.41, 8.57, 0.0018], "hull": 32, "edges": [0, 62, 0, 2, 2, 4, 4, 6, 56, 58, 58, 60, 60, 62, 30, 32, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 24, 26, 26, 28, 28, 30], "width": 39, "height": 95}}, "t3": {"t3": {"type": "mesh", "uvs": [0.45119, 0.02427, 0.52426, 0.10255, 0.52514, 0.14525, 0.52593, 0.18357, 0.52669, 0.22058, 0.57177, 0.26968, 0.62737, 0.33025, 0.67238, 0.37928, 0.71094, 0.42129, 0.74948, 0.46327, 0.78094, 0.49754, 0.8149, 0.53453, 0.82665, 0.57982, 0.83774, 0.62257, 0.84709, 0.65858, 0.83556, 0.68762, 0.8265, 0.71043, 0.81504, 0.73929, 0.70517, 0.76088, 0.57623, 0.77287, 0.40588, 0.78872, 0.23182, 0.80491, 0.20578, 0.81796, 0.19726, 0.84841, 0.18899, 0.87797, 0.25051, 0.91566, 0.30791, 0.93184, 0.37854, 0.95175, 0.47063, 0.96643, 0.69626, 0.98553, 0.74869, 0.99629, 0.69984, 0.99772, 0.52884, 0.98663, 0.32395, 0.97333, 0.14405, 0.94837, 0.07026, 0.91649, 0, 0.88612, 0, 0.85034, 0.05785, 0.82304, 0.11702, 0.79512, 0.17943, 0.76568, 0.23209, 0.73567, 0.26249, 0.70747, 0.29504, 0.67727, 0.32894, 0.64581, 0.33806, 0.63664, 0.34968, 0.59897, 0.36363, 0.55373, 0.37698, 0.51043, 0.36987, 0.45733, 0.3653, 0.42316, 0.33354, 0.39123, 0.322, 0.36402, 0.30939, 0.33425, 0.29486, 0.29996, 0.27047, 0.24243, 0.2437, 0.17924, 0.21715, 0.1166, 0.19705, 0.06917, 0.15626, 0.02362, 0.41265, 0.00095], "triangles": [32, 29, 31, 31, 29, 30, 33, 28, 32, 32, 28, 29, 33, 27, 28, 33, 34, 27, 34, 26, 27, 34, 25, 26, 34, 35, 25, 25, 35, 24, 35, 36, 24, 36, 37, 24, 24, 37, 23, 37, 38, 23, 23, 38, 22, 38, 39, 22, 22, 39, 21, 21, 39, 40, 20, 21, 40, 20, 40, 19, 18, 19, 41, 19, 40, 41, 17, 18, 42, 18, 41, 42, 17, 42, 16, 42, 43, 16, 16, 43, 15, 15, 43, 14, 43, 44, 14, 44, 45, 14, 45, 13, 14, 45, 46, 13, 46, 12, 13, 46, 47, 12, 47, 11, 12, 47, 48, 11, 48, 10, 11, 48, 9, 10, 48, 49, 9, 49, 8, 9, 49, 50, 8, 50, 51, 8, 51, 7, 8, 51, 52, 7, 52, 6, 7, 52, 53, 6, 53, 54, 6, 54, 5, 6, 54, 55, 5, 55, 4, 5, 4, 56, 3, 4, 55, 56, 56, 2, 3, 56, 57, 2, 57, 1, 2, 57, 58, 1, 58, 0, 1, 58, 59, 0, 59, 60, 0], "vertices": [1, 97, 3.47, 8.32, 1, 1, 97, 49.11, 12.19, 1, 2, 97, 73.85, 11.1, 0.08537, 98, 7.31, 11.18, 0.91463, 1, 98, 29.52, 10.45, 1, 1, 98, 50.97, 9.74, 1, 2, 98, 79.57, 12.41, 0.25449, 99, 3.9, 11.77, 0.74551, 1, 99, 39.28, 13.64, 1, 2, 99, 67.92, 15.15, 0.99951, 100, -23.07, 12.05, 0.00049, 2, 99, 92.45, 16.45, 0.33057, 100, 1.04, 16.76, 0.66943, 1, 100, 25.14, 21.48, 1, 2, 100, 44.81, 25.33, 0.97255, 101, -30.24, 24.13, 0.02745, 2, 100, 66.04, 29.48, 0.65354, 101, -9.2, 29.13, 0.34646, 3, 100, 92.19, 32.12, 0.08801, 101, 16.83, 32.83, 0.90102, 102, -72.57, 21.51, 0.01097, 2, 101, 41.39, 36.32, 0.86473, 102, -48.77, 28.52, 0.13527, 2, 101, 62.08, 39.26, 0.54259, 102, -28.72, 34.42, 0.45741, 2, 101, 78.94, 40.07, 0.20181, 102, -12.17, 37.66, 0.79819, 2, 101, 92.17, 40.72, 0.04128, 102, 0.83, 40.21, 0.95872, 1, 102, 17.29, 43.44, 1, 2, 102, 31.65, 37.8, 0.99998, 103, -18.09, 39.49, 2e-05, 2, 102, 41, 29.27, 0.97665, 103, -9.5, 30.2, 0.02335, 2, 102, 53.36, 18.01, 0.71127, 103, 1.85, 17.91, 0.28873, 2, 102, 65.98, 6.5, 0.02865, 103, 13.44, 5.36, 0.97135, 1, 103, 21.25, 4.49, 1, 2, 103, 38.79, 6.68, 0.99967, 104, -16.57, 17.32, 0.00033, 2, 103, 55.82, 8.81, 0.70764, 104, -0.77, 10.63, 0.29236, 1, 104, 21.46, 7.61, 1, 2, 104, 31.91, 8.7, 0.97036, 105, -3.63, 13.94, 0.02964, 2, 104, 44.76, 10.03, 0.20542, 105, 7.17, 6.84, 0.79458, 1, 105, 17.96, 3.24, 1, 1, 105, 39.53, 2.91, 1, 1, 105, 46.37, -0.35, 1, 1, 105, 43.32, -3.06, 1, 1, 105, 27.96, -4.51, 1, 1, 105, 9.55, -6.24, 1, 1, 104, 36.12, -7.26, 1, 1, 104, 16.68, -6.38, 1, 2, 103, 63.01, -5.71, 0.5929, 104, -1.83, -5.54, 0.4071, 1, 103, 42.53, -9.09, 1, 1, 103, 26.14, -7, 1, 1, 103, 9.37, -4.85, 1, 1, 102, 44.99, -3.29, 1, 1, 102, 27.06, -3.4, 1, 2, 101, 95.28, -5.46, 0.1051, 102, 10.59, -5.03, 0.8949, 1, 101, 77.58, -4.63, 1, 1, 101, 59.15, -3.77, 1, 1, 101, 53.78, -3.58, 1, 1, 101, 31.95, -4.91, 1, 2, 100, 79.52, -6.74, 0.06205, 101, 5.73, -6.51, 0.93795, 1, 100, 54.39, -7.25, 1, 1, 100, 23.69, -9.81, 1, 2, 99, 91.37, -11.89, 0.30695, 100, 3.94, -11.45, 0.69305, 1, 99, 72.71, -13.08, 1, 1, 99, 56.9, -12.82, 1, 1, 99, 39.61, -12.54, 1, 1, 99, 19.69, -12.21, 1, 2, 98, 62.88, -11.71, 0.9918, 99, -13.74, -11.66, 0.0082, 1, 98, 26.18, -12.59, 1, 2, 97, 56.07, -13.35, 0.93967, 98, -10.2, -13.46, 0.06033, 1, 97, 28.52, -13.71, 1, 1, 97, 1.97, -15.82, 1, 1, 97, -10.18, 5.8, 1], "hull": 61, "edges": [0, 120, 0, 2, 34, 36, 42, 44, 48, 50, 54, 56, 56, 58, 58, 60, 60, 62, 66, 68, 72, 74, 100, 102, 116, 118, 118, 120, 88, 90, 80, 82, 114, 116, 2, 4, 112, 114, 4, 6, 6, 8, 110, 112, 108, 110, 8, 10, 10, 12, 106, 108, 102, 104, 104, 106, 12, 14, 14, 16, 96, 98, 98, 100, 16, 18, 18, 20, 20, 22, 94, 96, 22, 24, 90, 92, 92, 94, 24, 26, 26, 28, 86, 88, 28, 30, 82, 84, 84, 86, 30, 32, 32, 34, 36, 38, 38, 40, 40, 42, 78, 80, 74, 76, 76, 78, 44, 46, 46, 48, 68, 70, 70, 72, 62, 64, 64, 66, 50, 52, 52, 54], "width": 63, "height": 452}}, "t7": {"t7": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [51.59, 28.39, 45.97, -33.35, -33.7, -26.11, -28.08, 35.64], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 48, "height": 62}}, "t8": {"t8": {"type": "mesh", "uvs": [0.42323, 0.02778, 0.39769, 0.07319, 0.4266, 0.10397, 0.54262, 0.14419, 0.51827, 0.15272, 0.44813, 0.15308, 0.41343, 0.16666, 0.43603, 0.20555, 0.46005, 0.24688, 0.47784, 0.27749, 0.50742, 0.30745, 0.53994, 0.33105, 0.57504, 0.35652, 0.63118, 0.38583, 0.66634, 0.4042, 0.71517, 0.41549, 0.7858, 0.43182, 0.86473, 0.42406, 0.92648, 0.41886, 0.97458, 0.41481, 0.99678, 0.42372, 0.99597, 0.45785, 0.95051, 0.47481, 0.9188, 0.46245, 0.8826, 0.44834, 0.84669, 0.45514, 0.78928, 0.46601, 0.69356, 0.47114, 0.59011, 0.47669, 0.4778, 0.45879, 0.42603, 0.45053, 0.37664, 0.4367, 0.29745, 0.41452, 0.25037, 0.36808, 0.21277, 0.33099, 0.20982, 0.37074, 0.20737, 0.40382, 0.20487, 0.43751, 0.14661, 0.42323, 0.10786, 0.37596, 0.09029, 0.35451, 0.09027, 0.32041, 0.09026, 0.29627, 0.09024, 0.25814, 0.09023, 0.21764, 0.05068, 0.1655, 0.11052, 0.12124, 0.23587, 0.09036, 0.26139, 0.06563, 0.22862, 0.03547, 0.26189, 0.00132, 0.3392, 0.00187, 0.27116, 0.11897, 0.23764, 0.16854, 0.27725, 0.21683, 0.29706, 0.25941, 0.32601, 0.29499, 0.38543, 0.32804, 0.41895, 0.35791, 0.45095, 0.38459, 0.51646, 0.40772, 0.61397, 0.43186, 0.67492, 0.44394, 0.76329, 0.44775], "triangles": [28, 62, 27, 28, 61, 62, 28, 29, 61, 22, 23, 21, 27, 63, 26, 27, 62, 63, 26, 63, 25, 23, 18, 21, 21, 18, 20, 23, 24, 18, 20, 18, 19, 63, 16, 25, 25, 17, 24, 25, 16, 17, 24, 17, 18, 62, 15, 63, 63, 15, 16, 61, 14, 62, 62, 14, 15, 61, 13, 14, 61, 60, 13, 29, 60, 61, 29, 30, 60, 30, 31, 60, 31, 59, 60, 31, 32, 59, 32, 58, 59, 32, 33, 58, 60, 12, 13, 60, 59, 12, 12, 58, 11, 12, 59, 58, 58, 10, 11, 37, 38, 36, 38, 39, 36, 36, 39, 35, 39, 40, 35, 35, 40, 34, 33, 57, 58, 33, 34, 57, 58, 57, 10, 40, 41, 34, 34, 56, 57, 56, 34, 42, 57, 9, 10, 57, 56, 9, 34, 41, 42, 42, 55, 56, 42, 43, 55, 56, 8, 9, 56, 55, 8, 55, 7, 8, 43, 54, 55, 55, 54, 7, 43, 44, 54, 44, 53, 54, 44, 45, 53, 54, 6, 7, 54, 53, 6, 45, 46, 53, 53, 52, 6, 53, 46, 52, 5, 6, 2, 6, 52, 2, 5, 2, 4, 4, 2, 3, 46, 47, 52, 47, 48, 52, 52, 1, 2, 52, 48, 1, 1, 48, 0, 48, 51, 0, 51, 49, 50, 51, 48, 49], "vertices": [1, 35, 6.62, 29.04, 1, 2, 35, 34.46, 23.38, 0.99337, 36, -36.56, 29.15, 0.00663, 2, 35, 52.98, 31.28, 0.79432, 36, -17.04, 34.08, 0.20568, 2, 35, 76.58, 61.46, 0.49707, 36, 10.97, 60.22, 0.50293, 2, 35, 81.96, 55.44, 0.49499, 36, 15.35, 53.43, 0.50501, 2, 35, 82.71, 37.63, 0.40796, 36, 13.32, 35.73, 0.59204, 2, 35, 91.24, 29.07, 0.15327, 36, 20.41, 25.95, 0.84673, 3, 35, 114.74, 35.52, 0.00117, 36, 44.63, 28.66, 0.87998, 37, -19.84, 30.7, 0.11885, 3, 36, 70.36, 31.54, 0.26241, 37, 6.05, 30.7, 0.73751, 38, -38.26, 56.62, 7e-05, 3, 36, 89.43, 33.68, 0.02233, 37, 25.24, 30.7, 0.94893, 38, -21.52, 47.24, 0.02875, 2, 37, 44.74, 33.7, 0.77021, 38, -3.04, 40.33, 0.22979, 3, 37, 60.65, 38.34, 0.40963, 38, 13.11, 36.6, 0.58439, 39, -15.49, 54.71, 0.00599, 3, 37, 77.83, 43.35, 0.10224, 38, 30.55, 32.58, 0.8108, 39, -3.31, 41.6, 0.08696, 3, 37, 98.53, 53.01, 0.00294, 38, 53.33, 30.88, 0.44391, 39, 14.6, 27.42, 0.55315, 2, 38, 67.6, 29.82, 0.09777, 39, 25.81, 18.53, 0.90223, 2, 38, 81.08, 34.23, 0.00269, 39, 39.45, 14.62, 0.99731, 1, 39, 59.16, 8.96, 1, 1, 39, 77.64, 18.08, 1, 1, 39, 92.21, 24.69, 1, 1, 39, 103.55, 29.84, 1, 1, 39, 110.27, 25.82, 1, 1, 39, 114.74, 5.51, 1, 1, 39, 105.81, -7.14, 1, 1, 39, 96.28, -1.62, 1, 1, 39, 85.38, 4.68, 1, 1, 39, 77.43, -1.4, 1, 1, 39, 64.71, -11.13, 1, 1, 39, 41.72, -19.64, 1, 2, 38, 86.78, -14.41, 0.02096, 39, 16.88, -28.84, 0.97904, 2, 38, 59.38, -27.9, 0.67595, 39, -13.37, -24.63, 0.32405, 2, 38, 46.75, -34.11, 0.92757, 39, -27.32, -22.69, 0.07243, 3, 37, 113.41, -17.12, 0.00837, 38, 32.04, -37.57, 0.98819, 39, -41.43, -17.31, 0.00345, 2, 37, 95.54, -33.49, 0.15646, 38, 8.45, -43.12, 0.84354, 3, 36, 136.88, -30.59, 0.00044, 37, 65.24, -38.45, 0.71941, 38, -20.41, -32.64, 0.28015, 3, 36, 113.27, -37.22, 0.0412, 37, 41.03, -42.41, 0.94876, 38, -43.46, -24.26, 0.01004, 2, 36, 137.19, -41.01, 0.00963, 37, 64.39, -48.84, 0.99037, 2, 36, 157.1, -44.17, 0.00013, 37, 83.82, -54.19, 0.99988, 1, 37, 103.61, -59.64, 1, 1, 37, 91.67, -71.97, 1, 2, 36, 137.08, -67.1, 0.01158, 37, 61.37, -74.76, 0.98842, 2, 36, 123.56, -69.89, 0.03302, 37, 47.63, -76.02, 0.96698, 2, 36, 102.96, -67.28, 0.11157, 37, 27.44, -71.13, 0.88843, 2, 36, 88.37, -65.43, 0.22634, 37, 13.15, -67.67, 0.77366, 2, 36, 65.34, -62.5, 0.51294, 37, -9.42, -62.2, 0.48706, 3, 35, 124.72, -52.06, 0.00766, 36, 40.87, -59.4, 0.83073, 37, -33.39, -56.39, 0.16161, 3, 35, 93.28, -63.05, 0.09316, 36, 8.1, -65.37, 0.89742, 37, -66.61, -58.67, 0.00942, 2, 35, 65.89, -48.66, 0.30869, 36, -16.72, -46.89, 0.69131, 2, 35, 46.14, -17.39, 0.93175, 36, -31.37, -12.94, 0.06825, 2, 35, 30.89, -11.36, 0.99985, 36, -45.49, -4.61, 0.00015, 1, 35, 12.78, -20.23, 1, 1, 35, -8.26, -12.41, 1, 1, 35, -8.51, 7.23, 1, 2, 35, 63.29, -7.92, 0.84443, 36, -12.95, -6.24, 0.15557, 3, 35, 93.71, -15.52, 0.02605, 36, 15.92, -18.49, 0.97338, 37, -53.63, -12.96, 0.00057, 2, 36, 46.36, -12.21, 0.98406, 37, -22.67, -10.11, 0.01594, 2, 36, 72.72, -10.49, 0.21389, 37, 3.71, -11.33, 0.78611, 2, 36, 95.15, -5.93, 0.00249, 37, 26.51, -9.29, 0.99751, 2, 37, 49.62, 0.64, 0.99684, 38, -14.94, 9.09, 0.00316, 2, 37, 69.3, 4.62, 0.13105, 38, 4.19, 2.96, 0.86895, 1, 38, 21.63, -2.15, 1, 3, 37, 104.62, 21.55, 4e-05, 38, 43.27, 0.47, 0.99883, 39, -10.8, 7.88, 0.00113, 1, 39, 16.64, -0.88, 1, 1, 39, 33.38, -4.56, 1, 1, 39, 55.77, -1.78, 1], "hull": 52, "edges": [0, 102, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 18, 20, 32, 34, 38, 40, 40, 42, 42, 44, 74, 76, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 90, 92, 88, 90, 92, 104, 104, 4, 90, 106, 106, 12, 88, 108, 12, 14, 108, 14, 86, 88, 86, 110, 14, 16, 16, 18, 110, 16, 84, 86, 84, 112, 112, 18, 80, 82, 82, 84, 82, 68, 68, 114, 114, 20, 64, 66, 66, 68, 66, 116, 20, 22, 22, 24, 116, 22, 64, 118, 118, 24, 60, 62, 62, 64, 62, 120, 24, 26, 26, 28, 120, 26, 56, 58, 58, 60, 58, 122, 122, 28, 56, 124, 28, 30, 30, 32, 124, 30, 52, 54, 54, 56, 54, 126, 126, 32, 48, 50, 50, 52, 34, 36, 36, 38, 44, 46, 46, 48, 76, 78, 78, 80, 68, 70, 70, 72, 72, 74], "width": 198, "height": 475}}, "bg1": {"bg1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [472, -157, -554, -157, -554, 1565, 472, 1565], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 186, "height": 313}}, "zhit": {"zhit": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-9.19, -8.58, -6.76, 13.29, 18.08, 10.53, 15.65, -11.34], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 17, "height": 19}}, "zs4": {"zs4": {"type": "mesh", "uvs": [0.77034, 0, 0.81697, 0.0285, 0.98416, 0.52993, 1, 0.67038, 0.99445, 0.706, 0.66, 0.96921, 0.56537, 0.97307, 0.3553, 0.89335, 0.08417, 0.66518, 0.07408, 0.61553, 0.06245, 0.51034, 0.08556, 0.45439, 0.25077, 0.34338, 0.42241, 0.26739, 0.46599, 0.2392, 0.68552, 0.05065, 0.75513, 0, 0.52584, 0.54268, 0.57876, 0.73719, 0.28109, 0.55096, 0.31857, 0.7703, 0.47733, 0.57165, 0.53907, 0.77444], "triangles": [8, 9, 19, 9, 11, 19, 9, 10, 11, 11, 12, 19, 7, 22, 6, 8, 20, 7, 8, 19, 20, 22, 7, 21, 7, 20, 21, 21, 17, 22, 20, 19, 21, 19, 13, 21, 13, 14, 21, 21, 14, 17, 19, 12, 13, 6, 18, 5, 6, 22, 18, 5, 2, 4, 2, 18, 15, 15, 1, 2, 15, 16, 1, 16, 0, 1, 2, 5, 18, 22, 17, 18, 18, 17, 15, 4, 2, 3, 17, 14, 15], "vertices": [1, 14, -4.74, -24.18, 1, 1, 14, -8.96, -19.94, 1, 1, 14, -12.28, 18.37, 1, 1, 14, -9.85, 27.38, 1, 1, 14, -8.19, 29.13, 1, 2, 14, 35.93, 25.78, 0.93647, 15, -16.97, 20.5, 0.06353, 2, 14, 46.32, 20.75, 0.68462, 15, -5.42, 20.75, 0.31538, 3, 14, 66.78, 4.47, 0.00307, 15, 20.2, 15.57, 0.99318, 16, -19.22, 9.86, 0.00375, 1, 16, 16.88, 6.59, 1, 1, 16, 19.1, 3.93, 1, 2, 15, 55.93, -9.32, 0.00015, 16, 22.66, -2.07, 0.99985, 2, 15, 53.11, -12.96, 0.00764, 16, 21.18, -6.43, 0.99236, 3, 14, 61.86, -33.16, 0.01342, 15, 32.96, -20.18, 0.59013, 16, 4.46, -19.8, 0.39645, 3, 14, 40.97, -28.03, 0.34596, 15, 12.02, -25.12, 0.64734, 16, -13.73, -31.28, 0.00671, 3, 14, 35.4, -27.24, 0.5347, 15, 6.7, -26.95, 0.465, 16, -18.17, -34.74, 0.0003, 2, 14, 5.97, -25.96, 0.99306, 15, -20.08, -39.2, 0.00694, 2, 14, -3.09, -25.02, 0.99996, 15, -28.57, -42.5, 4e-05, 2, 14, 37.88, -6.35, 0.68121, 15, -0.6, -7.22, 0.31879, 2, 14, 37.89, 7.84, 0.88388, 15, -7.06, 5.42, 0.11612, 3, 14, 64.71, -19.47, 0.00329, 15, 29.26, -6.68, 0.81205, 16, -3.42, -8.24, 0.18466, 2, 15, 24.68, 7.57, 0.96122, 16, -12.38, 3.75, 0.03878, 2, 14, 44.01, -7.37, 0.1765, 15, 5.32, -5.34, 0.8235, 2, 14, 43.3, 7.8, 0.57349, 15, -2.21, 7.84, 0.42651], "hull": 17, "edges": [0, 32, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 24, 26, 26, 28, 28, 30, 30, 32, 22, 24, 22, 20, 14, 16, 18, 16, 18, 20, 28, 34, 34, 36, 36, 10, 24, 38, 38, 40, 40, 14, 26, 42, 42, 44, 44, 12], "width": 122, "height": 65}}, "ys3": {"ys3": {"type": "mesh", "uvs": [0.49038, 0, 0.50877, 0.02861, 0.53925, 0.12818, 0.60493, 0.1673, 0.6626, 0.18311, 0.78805, 0.28637, 0.86553, 0.32916, 0.94911, 0.32969, 0.96494, 0.41876, 0.82797, 0.45609, 0.72154, 0.46085, 0.66705, 0.48944, 0.66589, 0.56186, 0.65754, 0.66272, 0.63699, 0.82794, 0.54736, 0.98703, 0.54788, 0.99471, 0.33452, 0.99519, 0.22254, 0.97282, 0.22262, 0.66456, 0.20871, 0.62508, 0.16055, 0.43766, 0.20162, 0.36568, 0.18636, 0.31961, 0.13417, 0.18899, 0.11487, 0.12696, 0.08435, 0.07461, 0.2336, 0.02067, 0.41352, 0.02088, 0.55722, 0.52226, 0.49028, 0.56308, 0.55722, 0.25425, 0.44278, 0.3217, 0.30243, 0.40689, 0.42982, 0.10339, 0.34777, 0.22586], "triangles": [10, 11, 31, 10, 5, 9, 10, 31, 5, 8, 6, 7, 8, 9, 6, 9, 5, 6, 5, 31, 4, 33, 32, 29, 29, 32, 11, 11, 32, 31, 32, 33, 35, 32, 35, 31, 35, 2, 31, 31, 3, 4, 31, 2, 3, 2, 35, 34, 34, 27, 28, 34, 35, 27, 34, 1, 2, 1, 28, 0, 1, 34, 28, 33, 22, 35, 22, 23, 35, 23, 24, 35, 24, 27, 35, 24, 25, 27, 25, 26, 27, 17, 15, 16, 15, 17, 14, 19, 14, 17, 17, 18, 19, 19, 30, 14, 14, 30, 13, 19, 20, 30, 13, 30, 12, 12, 30, 29, 20, 33, 30, 20, 21, 33, 30, 33, 29, 12, 29, 11, 21, 22, 33], "vertices": [3, 25, 43.73, 7.69, 0.99675, 22, 69.53, 50.43, 0.00322, 23, -31.39, 52.48, 4e-05, 3, 25, 45.16, 1.68, 0.97562, 22, 68.21, 44.4, 0.02248, 23, -27.32, 47.83, 0.0019, 4, 24, 18.52, -52.91, 0.01317, 25, 45.05, -17.93, 0.35782, 22, 59.6, 26.78, 0.57058, 23, -18.26, 30.43, 0.05843, 4, 24, 8.62, -60.93, 0.00634, 25, 53.17, -27.74, 0.20339, 22, 62.66, 14.42, 0.39844, 23, -6.51, 25.55, 0.39183, 4, 24, 3.35, -68.88, 0.00021, 25, 61.19, -32.92, 0.08665, 22, 67.63, 6.27, 0.22719, 23, 3, 24.7, 0.68595, 2, 25, 75.34, -56.93, 3e-05, 23, 26.72, 10.07, 0.99997, 1, 23, 40.45, 4.93, 1, 1, 23, 53.24, 7.86, 1, 1, 23, 59.59, -8.11, 1, 3, 21, 89.38, -76.48, 0.00464, 22, 53.84, -50.32, 0.00801, 23, 40.32, -20.02, 0.98735, 3, 21, 90.01, -59.76, 0.06179, 22, 40.48, -40.24, 0.11993, 23, 24.27, -24.77, 0.81828, 3, 21, 85.37, -50.74, 0.21378, 22, 30.42, -38.89, 0.28193, 23, 17.21, -32.06, 0.50429, 3, 21, 71.61, -49.28, 0.50187, 22, 21.36, -49.34, 0.26721, 23, 20.23, -45.56, 0.23092, 3, 21, 52.55, -46.19, 0.78616, 22, 7.93, -63.22, 0.13035, 23, 23.41, -64.6, 0.08349, 3, 21, 21.43, -40.06, 0.97689, 22, -14.89, -85.25, 0.01447, 23, 27.56, -96.05, 0.00863, 1, 21, -7.53, -23.24, 1, 2, 21, -9, -23.19, 1, 22, -46.13, -100.57, 0, 1, 21, -5.99, 10.17, 1, 1, 21, -0.12, 27.28, 1, 1, 21, 58.51, 21.83, 1, 2, 21, 66.22, 23.31, 0.99948, 24, -59.63, 21.87, 0.00052, 2, 21, 102.56, 27.53, 0.79436, 24, -23.1, 19.87, 0.20564, 3, 21, 115.66, 19.84, 0.40514, 24, -11.5, 10.07, 0.58503, 22, -10.17, 26.31, 0.00983, 2, 21, 124.64, 21.41, 0.08481, 24, -2.38, 10.09, 0.91519, 1, 24, 23.84, 11.52, 1, 1, 24, 36.07, 11.36, 1, 2, 24, 46.97, 13.39, 0.632, 25, -21.56, 9.79, 0.368, 2, 24, 50.82, -11.91, 0.06053, 25, 3.7, 13.93, 0.93947, 1, 25, 31.05, 6.84, 1, 3, 21, 80.72, -32.99, 0.42185, 22, 13.2, -32.56, 0.37644, 23, 1.88, -42.14, 0.20171, 3, 21, 73.93, -21.8, 0.67867, 22, 0.14, -31.74, 0.24246, 23, -6.54, -52.16, 0.07887, 4, 24, -5.47, -49.38, 0.01236, 25, 41.78, -41.95, 0.07919, 22, 46.22, 6.56, 0.69238, 23, -9.95, 7.66, 0.21607, 3, 24, -13.23, -28.68, 0.0884, 25, 21.16, -49.95, 0.02922, 22, 24.18, 8.3, 0.88238, 3, 21, 106.36, 4.81, 0.61655, 24, -23.22, -3.17, 0.19309, 22, -3.15, 10.08, 0.19036, 4, 24, 27.56, -37.55, 0.00574, 25, 29.59, -9.06, 0.37325, 22, 49.53, 41.48, 0.62017, 23, -36.07, 31.07, 0.00083, 3, 24, 8.32, -19.03, 0.32951, 25, 11.28, -28.5, 0.16533, 22, 24.59, 31.91, 0.50516], "hull": 29, "edges": [0, 56, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 54, 56, 52, 54, 50, 52, 0, 2, 14, 16, 16, 18, 26, 28, 28, 30, 26, 24, 24, 22, 22, 20, 20, 18, 22, 58, 58, 60, 10, 62, 62, 64, 64, 66, 4, 68, 68, 70, 70, 44], "width": 122, "height": 148}}, "qin": {"qin": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [911.2, 3.52, 345.03, -623.76, -360.94, 13.43, 205.23, 640.71], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 659, "height": 741}}, "ys1": {"ys1": {"type": "mesh", "uvs": [1, 0.03704, 1, 0.03879, 0.91078, 0.10747, 0.89409, 0.11691, 0.87623, 0.14879, 0.8552, 0.18633, 0.84385, 0.1971, 0.83375, 0.2392, 0.82404, 0.2797, 0.81437, 0.32003, 0.8043, 0.36206, 0.78906, 0.38621, 0.76969, 0.41692, 0.74924, 0.45027, 0.73434, 0.4765, 0.72348, 0.51034, 0.71472, 0.5376, 0.693, 0.56527, 0.66075, 0.60633, 0.64144, 0.63092, 0.63341, 0.67345, 0.62906, 0.72735, 0.62552, 0.77139, 0.59268, 0.80266, 0.55641, 0.83721, 0.52014, 0.85983, 0.46645, 0.89331, 0.41435, 0.91473, 0.36061, 0.93682, 0.30123, 0.94944, 0.2471, 0.96095, 0.18712, 0.95355, 0.15132, 0.94914, 0.09003, 0.9403, 0.05014, 0.93455, 0, 0.92732, 0, 0.90165, 0.0034, 0.89123, 0.04501, 0.88113, 0.09157, 0.86983, 0.12521, 0.86167, 0.16814, 0.85125, 0.22216, 0.8429, 0.28415, 0.83332, 0.32371, 0.8163, 0.35093, 0.80458, 0.36347, 0.77782, 0.3764, 0.75023, 0.36289, 0.71584, 0.33966, 0.66982, 0.31824, 0.62739, 0.31818, 0.59481, 0.3181, 0.55469, 0.31801, 0.50991, 0.33669, 0.45801, 0.35439, 0.40884, 0.3766, 0.37864, 0.40047, 0.34618, 0.43366, 0.31422, 0.459, 0.28981, 0.49978, 0.27661, 0.52611, 0.26809, 0.58088, 0.26072, 0.63051, 0.25404, 0.64344, 0.24801, 0.69188, 0.21757, 0.72054, 0.17815, 0.74345, 0.14664, 0.76384, 0.11859, 0.79289, 0.08862, 0.81926, 0.06142, 0.91533, 0.04338, 0.98685, 0.0372, 0.53743, 0.31157, 0.54827, 0.37174, 0.53897, 0.43192, 0.51265, 0.48828, 0.48013, 0.53126, 0.43523, 0.57806, 0.40736, 0.62869, 0.39807, 0.68027, 0.86105, 0.09331, 0.82234, 0.13151, 0.80376, 0.15826, 0.78208, 0.19646, 0.76505, 0.23467, 0.74647, 0.27574, 0.6563, 0.28844, 0.74567, 0.33701, 0.60948, 0.31338, 0.69247, 0.3672, 0.62863, 0.38623, 0.69992, 0.40461, 0.62757, 0.44071, 0.7095, 0.45253, 0.60416, 0.49453, 0.668, 0.50503, 0.58356, 0.54024, 0.65379, 0.55139, 0.53994, 0.58946, 0.6091, 0.59406, 0.51227, 0.63147, 0.58995, 0.64263, 0.49525, 0.68266, 0.58143, 0.69973, 0.3855, 0.72083, 0.48248, 0.72604, 0.57399, 0.73983, 0.46971, 0.75295, 0.54845, 0.77133, 0.44737, 0.79037, 0.5293, 0.81137, 0.4229, 0.82515, 0.38778, 0.85206, 0.34416, 0.87832, 0.29734, 0.89538, 0.20371, 0.88554, 0.23776, 0.92557, 0.15705, 0.89669, 0.16344, 0.92557, 0.10598, 0.90391, 0.05172, 0.90457, 0.47954, 0.32293, 0.46999, 0.37297, 0.43898, 0.41123, 0.41512, 0.44803, 0.39126, 0.48629, 0.3674, 0.55105, 0.35786, 0.59667, 0.35939, 0.64408], "triangles": [31, 117, 30, 30, 117, 29, 32, 119, 31, 31, 119, 117, 117, 115, 29, 29, 115, 28, 32, 33, 119, 34, 121, 33, 33, 120, 119, 33, 121, 120, 115, 114, 28, 28, 114, 27, 34, 35, 121, 35, 36, 121, 120, 118, 119, 119, 116, 117, 119, 118, 116, 117, 116, 115, 114, 113, 27, 36, 37, 121, 37, 38, 121, 120, 121, 39, 121, 38, 39, 120, 40, 118, 120, 39, 40, 118, 41, 116, 118, 40, 41, 116, 42, 115, 42, 43, 115, 115, 43, 114, 116, 41, 42, 43, 44, 114, 114, 44, 113, 44, 45, 113, 27, 113, 26, 113, 112, 26, 26, 112, 25, 45, 46, 112, 25, 112, 111, 25, 111, 24, 112, 46, 110, 111, 112, 110, 113, 45, 112, 46, 47, 110, 24, 111, 23, 111, 109, 23, 111, 110, 109, 23, 109, 22, 110, 108, 109, 110, 47, 108, 109, 107, 22, 22, 107, 21, 108, 106, 109, 109, 106, 107, 47, 105, 108, 108, 105, 106, 107, 104, 21, 107, 106, 104, 47, 48, 105, 21, 104, 20, 105, 80, 106, 106, 103, 104, 106, 80, 103, 105, 48, 80, 48, 49, 80, 104, 103, 102, 80, 79, 103, 104, 102, 20, 103, 101, 102, 103, 79, 101, 49, 129, 80, 80, 129, 79, 20, 102, 19, 49, 50, 129, 79, 129, 128, 101, 99, 102, 102, 100, 19, 102, 99, 100, 79, 78, 101, 101, 78, 99, 19, 100, 18, 129, 50, 128, 79, 128, 78, 50, 51, 128, 128, 127, 78, 128, 51, 52, 100, 98, 18, 18, 98, 17, 99, 97, 100, 100, 97, 98, 78, 77, 99, 17, 98, 16, 127, 128, 52, 99, 77, 97, 78, 127, 77, 52, 53, 127, 98, 97, 96, 127, 126, 77, 127, 53, 126, 77, 76, 97, 77, 126, 76, 53, 54, 126, 98, 96, 16, 96, 97, 95, 97, 76, 95, 16, 96, 15, 95, 93, 96, 76, 75, 95, 126, 125, 76, 76, 125, 75, 75, 125, 124, 126, 54, 125, 54, 55, 125, 125, 55, 124, 96, 93, 94, 95, 75, 93, 93, 75, 91, 93, 91, 92, 124, 123, 75, 75, 74, 91, 75, 123, 74, 55, 56, 124, 124, 56, 123, 90, 91, 89, 56, 57, 123, 123, 57, 122, 123, 122, 74, 122, 57, 58, 122, 73, 74, 91, 74, 89, 74, 73, 89, 58, 59, 122, 122, 60, 73, 122, 59, 60, 87, 89, 62, 60, 61, 73, 62, 89, 61, 89, 73, 61, 15, 96, 14, 96, 94, 14, 14, 94, 13, 13, 94, 92, 94, 93, 92, 13, 92, 12, 12, 92, 11, 91, 90, 92, 92, 90, 11, 90, 88, 11, 11, 88, 10, 89, 87, 90, 90, 87, 88, 10, 88, 9, 88, 86, 9, 88, 87, 86, 9, 86, 8, 62, 63, 87, 63, 64, 87, 87, 64, 86, 86, 85, 8, 8, 85, 7, 64, 65, 86, 86, 65, 85, 85, 84, 7, 7, 84, 6, 85, 65, 84, 65, 66, 84, 5, 6, 83, 66, 67, 84, 6, 84, 83, 84, 67, 83, 5, 83, 4, 67, 68, 83, 83, 82, 4, 83, 68, 82, 4, 82, 3, 68, 69, 82, 82, 81, 3, 82, 69, 81, 3, 81, 2, 1, 2, 72, 2, 71, 72, 2, 81, 71, 69, 70, 81, 81, 70, 71, 72, 0, 1], "vertices": [1, 18, -74.67, 82.29, 1, 1, 18, -73.07, 82.67, 1, 1, 18, 1.54, 47.55, 1, 1, 18, 12.39, 40.24, 1, 1, 18, 43.85, 37.17, 1, 1, 18, 80.89, 33.57, 1, 1, 18, 92.23, 29.54, 1, 1, 18, 131.95, 33.08, 1, 1, 18, 170.16, 36.48, 1, 1, 18, 208.21, 39.87, 1, 1, 18, 247.87, 43.41, 1, 1, 18, 271.93, 40.13, 1, 4, 18, 302.52, 35.96, 0.99724, 63, -82.02, 83.88, 0.00208, 64, -92.03, 118.1, 0.00051, 65, -134.03, 118.1, 0.00016, 7, 18, 335.67, 31.76, 0.83929, 19, -39.89, 10.33, 0.03608, 62, -6.7, 86.48, 0.04976, 63, -48.7, 86.48, 0.05038, 64, -59.54, 110.28, 0.01683, 65, -101.54, 110.28, 0.0074, 66, -138.8, 116.19, 0.00025, 7, 18, 361.57, 29.13, 0.50124, 19, -44.89, 35.88, 0.13228, 62, 19.2, 89.15, 0.11264, 63, -22.8, 89.15, 0.15219, 64, -34.08, 104.81, 0.06493, 65, -76.09, 104.81, 0.03265, 66, -113.6, 109.66, 0.00408, 7, 18, 393.87, 30.42, 0.2589, 19, -55.51, 66.41, 0.11026, 62, 50.56, 96.95, 0.08804, 63, 8.56, 96.95, 0.24722, 64, -1.84, 102.54, 0.16353, 65, -43.85, 102.54, 0.10693, 66, -81.48, 106.05, 0.02512, 8, 18, 419.89, 31.47, 0.15944, 19, -64.06, 91.01, 0.07122, 62, 75.84, 103.24, 0.0439, 63, 33.84, 103.24, 0.23742, 64, 24.14, 100.71, 0.22373, 65, -17.87, 100.71, 0.19762, 66, -55.6, 103.13, 0.0645, 67, -97.6, 103.13, 0.00217, 8, 18, 448.03, 25.31, 0.08657, 19, -66.34, 119.73, 0.03704, 62, 104.64, 102.91, 0.01252, 63, 62.64, 102.91, 0.16473, 64, 51.43, 91.49, 0.21848, 65, 9.42, 91.49, 0.30965, 66, -28.72, 92.78, 0.15165, 67, -70.72, 92.78, 0.01935, 8, 18, 489.79, 16.17, 0.02431, 19, -69.72, 162.35, 0.00803, 63, 105.39, 102.41, 0.04819, 64, 91.93, 77.81, 0.08198, 65, 49.93, 77.81, 0.30992, 66, 11.18, 77.41, 0.37784, 67, -30.83, 77.41, 0.14243, 68, -72.83, 77.41, 0.0073, 9, 18, 514.8, 10.7, 0.00785, 19, -71.74, 187.87, 0.00181, 63, 130.99, 102.11, 0.01379, 64, 116.19, 69.62, 0.02026, 65, 74.18, 69.62, 0.15514, 66, 35.07, 68.21, 0.40745, 67, -6.94, 68.21, 0.33325, 68, -48.94, 68.21, 0.05538, 69, -90.94, 68.21, 0.00507, 8, 18, 554.63, 15.49, 0.00043, 63, 169.02, 114.86, 0.0003, 65, 114.3, 69.99, 0.01469, 66, 75.16, 66.9, 0.13166, 67, 33.16, 66.9, 0.45378, 68, -8.84, 66.9, 0.3082, 69, -50.85, 66.9, 0.09, 70, -102.71, 50.48, 0.00093, 5, 66, 125.7, 68.61, 0.00374, 67, 83.7, 68.61, 0.09188, 68, 41.69, 68.61, 0.35185, 69, -0.31, 68.61, 0.46038, 70, -53.16, 60.59, 0.09215, 5, 67, 124.99, 70.01, 0.00508, 68, 82.99, 70.01, 0.11148, 69, 40.99, 70.01, 0.49693, 70, -12.68, 68.86, 0.3694, 71, -62.88, 61.46, 0.01711, 4, 68, 113.77, 53.53, 0.01862, 69, 71.77, 53.53, 0.22448, 70, 20.42, 57.75, 0.57585, 71, -28.65, 54.59, 0.18104, 4, 69, 105.78, 35.34, 0.00921, 70, 56.99, 45.48, 0.27537, 71, 9.16, 47, 0.71292, 72, -53.36, 20.98, 0.0025, 3, 70, 82.72, 30.44, 0.02705, 71, 36.58, 35.3, 0.89886, 72, -24.05, 26.4, 0.07409, 3, 71, 77.17, 17.98, 0.23924, 72, 19.34, 34.44, 0.73186, 73, -35.17, 21.51, 0.0289, 3, 71, 106.91, -2.64, 0.14698, 72, 55.52, 33.72, 0.3688, 73, -2.13, 36.27, 0.48422, 3, 71, 137.58, -23.91, 0.14, 72, 92.84, 32.98, 0.1802, 73, 31.96, 51.48, 0.6798, 3, 71, 161.2, -51.47, 0.09217, 72, 127.77, 23.09, 0.15487, 73, 67.77, 57.41, 0.75297, 3, 71, 182.74, -76.6, 0.02673, 72, 159.61, 14.08, 0.1324, 73, 100.42, 62.81, 0.84087, 2, 72, 184.11, -11.41, 0.04943, 73, 133.44, 50.19, 0.95057, 1, 73, 153.15, 42.65, 1, 1, 73, 186.7, 28.57, 1, 1, 73, 208.53, 19.4, 1, 1, 73, 235.97, 7.88, 1, 2, 72, 245.14, -113.09, 0.03661, 73, 231.95, -15.83, 0.96339, 2, 72, 237.95, -119.98, 0.06509, 73, 228.38, -25.13, 0.93491, 2, 72, 212.8, -114.02, 0.12385, 73, 203.09, -30.44, 0.87615, 3, 71, 136.41, -191.6, 0.00475, 72, 184.66, -107.34, 0.26347, 73, 174.79, -36.38, 0.73178, 3, 71, 122.15, -176.33, 0.08293, 72, 164.33, -102.52, 0.32993, 73, 154.34, -40.68, 0.58713, 3, 71, 103.94, -156.85, 0.21578, 72, 138.39, -96.37, 0.33112, 73, 128.24, -46.16, 0.4531, 4, 70, 110.13, -140.34, 0.00031, 71, 85.18, -130.69, 0.31844, 72, 108.29, -84.96, 0.2957, 73, 96.15, -48.65, 0.38555, 5, 69, 115.26, -121.78, 0.00014, 70, 92.54, -107.86, 0.0071, 71, 63.66, -100.67, 0.32582, 72, 73.75, -71.87, 0.33979, 73, 59.32, -51.52, 0.32714, 6, 68, 139.46, -100.33, 0.0004, 69, 97.46, -100.33, 0.004, 70, 71.41, -89.67, 0.03595, 71, 40.41, -85.28, 0.32887, 72, 45.87, -71.91, 0.3651, 73, 34.12, -63.42, 0.26568, 7, 67, 169.21, -85.56, 0.00027, 68, 127.21, -85.56, 0.00458, 69, 85.2, -85.56, 0.02203, 70, 56.87, -77.16, 0.10827, 71, 24.42, -74.68, 0.39021, 72, 26.68, -71.94, 0.28097, 73, 16.77, -71.62, 0.19366, 8, 66, 185.62, -80.43, 0.00012, 67, 143.62, -80.43, 0.00636, 68, 101.61, -80.43, 0.03556, 69, 59.61, -80.43, 0.10587, 70, 30.77, -76.36, 0.25676, 71, -1.57, -77.17, 0.39495, 72, 6.41, -88.39, 0.11761, 73, 5.43, -95.13, 0.08278, 10, 64, 246.25, -68.4, 0.00035, 65, 204.24, -68.4, 5e-05, 66, 159.24, -75.13, 0.00538, 67, 117.23, -75.13, 0.04435, 68, 75.23, -75.13, 0.1499, 69, 33.23, -75.13, 0.25729, 70, 3.88, -75.54, 0.27002, 71, -28.36, -79.73, 0.19775, 72, -14.48, -105.34, 0.04936, 73, -6.26, -119.37, 0.02555, 10, 64, 215.25, -80.17, 0.00715, 65, 173.25, -80.17, 0.00605, 66, 127.77, -85.6, 0.04801, 67, 85.77, -85.6, 0.18963, 68, 43.77, -85.6, 0.34441, 69, 1.76, -85.6, 0.24838, 70, -25.4, -91.11, 0.11552, 71, -55.45, -98.84, 0.02837, 72, -26.47, -136.26, 0.01033, 73, -3.94, -152.44, 0.00215, 9, 63, 238.19, -40.23, 0.00039, 64, 174.15, -98.89, 0.04462, 65, 132.15, -98.89, 0.05239, 66, 85.92, -102.58, 0.18849, 67, 43.92, -102.58, 0.351, 68, 1.92, -102.58, 0.26731, 69, -40.08, -102.58, 0.07907, 70, -63.83, -114.83, 0.01651, 72, -40.07, -179.32, 0.00021, 8, 63, 207.48, -68.35, 0.0091, 64, 136.26, -116.14, 0.11737, 65, 94.25, -116.14, 0.13846, 66, 47.34, -118.23, 0.29628, 67, 5.34, -118.23, 0.29582, 68, -36.67, -118.23, 0.12549, 69, -78.67, -118.23, 0.01689, 70, -99.26, -136.69, 0.00058, 8, 19, 106.56, 253.16, 0.00053, 63, 179.86, -81.37, 0.03048, 64, 105.97, -119.99, 0.1966, 65, 63.96, -119.99, 0.20734, 66, 16.92, -120.81, 0.30105, 67, -25.09, -120.81, 0.20045, 68, -67.09, -120.81, 0.0601, 69, -109.09, -120.81, 0.00345, 9, 19, 125.61, 220.76, 0.00891, 62, 187.87, -97.41, 0.00019, 63, 145.87, -97.41, 0.09307, 64, 68.68, -124.74, 0.32128, 65, 26.68, -124.74, 0.2505, 66, -20.54, -123.99, 0.21904, 67, -62.54, -123.99, 0.09091, 68, -104.54, -123.99, 0.01609, 69, -146.55, -123.99, 0, 8, 19, 146.88, 184.58, 0.04238, 62, 149.91, -115.31, 0.00807, 63, 107.91, -115.31, 0.20538, 64, 27.05, -130.03, 0.40085, 65, -14.95, -130.03, 0.20223, 66, -62.35, -127.53, 0.11102, 67, -104.35, -127.53, 0.02844, 68, -146.36, -127.53, 0.00164, 7, 19, 162.16, 137.17, 0.15242, 62, 101.31, -126.23, 0.04549, 63, 59.31, -126.23, 0.333, 64, -22.55, -125.4, 0.33604, 65, -64.55, -125.4, 0.0978, 66, -111.71, -120.83, 0.03204, 67, -153.71, -120.83, 0.0032, 6, 19, 176.63, 92.25, 0.36584, 62, 55.27, -136.57, 0.08619, 63, 13.26, -136.57, 0.32685, 64, -69.53, -121.01, 0.18569, 65, -111.54, -121.01, 0.03066, 66, -158.47, -114.47, 0.00476, 6, 19, 179.87, 61.35, 0.55541, 62, 24.2, -136.99, 0.08276, 63, -17.81, -136.99, 0.24715, 64, -99.21, -111.81, 0.10359, 65, -141.22, -111.81, 0.01059, 66, -187.74, -104.04, 0.00051, 6, 18, 287.9, -187.03, 0.00012, 19, 183.34, 28.13, 0.7691, 62, -9.2, -137.45, 0.04741, 63, -51.2, -137.45, 0.13827, 64, -131.12, -101.92, 0.04341, 65, -173.12, -101.92, 0.00168, 5, 18, 254.3, -175.37, 0.01632, 19, 181.94, -7.4, 0.91374, 62, -44.46, -132.83, 0.01015, 63, -86.46, -132.83, 0.04884, 64, -163.23, -86.63, 0.01096, 5, 18, 228.63, -166.47, 0.05329, 19, 180.87, -34.55, 0.9281, 62, -71.39, -129.31, 0.00035, 63, -113.4, -129.31, 0.01606, 64, -187.75, -74.96, 0.00221, 4, 18, 211.11, -146.44, 0.12071, 19, 166.79, -57.13, 0.87604, 63, -134.61, -113.24, 0.00314, 64, -202.96, -53.12, 0.00011, 3, 18, 199.79, -133.5, 0.18602, 19, 157.7, -71.72, 0.81365, 63, -148.31, -102.86, 0.00033, 2, 18, 185.68, -104.33, 0.37101, 19, 133.88, -93.68, 0.62899, 2, 18, 172.9, -77.9, 0.63304, 19, 112.3, -113.59, 0.36696, 2, 18, 165.66, -71.96, 0.72653, 19, 108.71, -122.25, 0.27347, 2, 18, 131.39, -51.39, 0.9483, 19, 98.98, -161.01, 0.0517, 2, 18, 91.61, -43.91, 0.99778, 19, 103.37, -201.25, 0.00222, 1, 18, 59.81, -37.93, 1, 1, 18, 31.5, -32.6, 1, 1, 18, 0.28, -22.83, 1, 1, 18, -28.06, -13.96, 1, 1, 18, -57.47, 36.08, 1, 1, 18, -72.75, 74.93, 1, 4, 18, 237.88, -117.63, 0.12539, 19, 131.45, -39.88, 0.87261, 63, -114.23, -79.61, 0.00187, 64, -173.19, -27.43, 0.00013, 4, 19, 97.53, 5.6, 0.95348, 62, -23.87, -49.95, 0.0354, 63, -65.87, -49.95, 0.01007, 64, -118.03, -14.17, 0.00105, 5, 19, 73.65, 56.96, 0.11427, 62, 29.45, -30.82, 0.58115, 63, -12.56, -30.82, 0.29041, 64, -61.41, -12.45, 0.01395, 65, -103.42, -12.45, 0.00022, 6, 19, 60.07, 110.2, 0.01096, 62, 83.71, -22.12, 0.01588, 63, 41.7, -22.12, 0.57318, 64, -7.12, -20.95, 0.38424, 65, -49.12, -20.95, 0.01491, 66, -91.92, -17.11, 0.00084, 6, 19, 55.92, 154.45, 0.00366, 63, 86.15, -21.99, 0.06185, 64, 35.19, -34.56, 0.60039, 65, -6.82, -34.56, 0.29576, 66, -50.22, -32.48, 0.03507, 67, -92.22, -32.48, 0.00327, 7, 19, 56.13, 205.41, 0.00037, 63, 136.88, -26.82, 0.01979, 64, 81.94, -54.83, 0.19796, 65, 39.94, -54.83, 0.35088, 66, -4.36, -54.69, 0.33504, 67, -46.36, -54.69, 0.08388, 68, -88.36, -54.69, 0.01207, 8, 63, 186.66, -21.22, 0.00313, 64, 131.02, -64.88, 0.0747, 65, 89.02, -64.88, 0.10437, 66, 44.26, -66.79, 0.33657, 67, 2.25, -66.79, 0.35237, 68, -39.75, -66.79, 0.11535, 69, -81.75, -66.79, 0.01302, 70, -110.88, -86.5, 0.00049, 8, 64, 179.64, -64.17, 0.01998, 65, 137.64, -64.17, 0.0209, 66, 92.87, -68.12, 0.11335, 67, 50.86, -68.12, 0.34302, 68, 8.86, -68.12, 0.35542, 69, -33.14, -68.12, 0.11816, 70, -62.73, -79.7, 0.02825, 72, -62.19, -152.01, 0.00093, 1, 18, -4.66, 16.5, 1, 1, 18, 35.38, 3.11, 1, 1, 18, 62.25, -1.49, 1, 2, 18, 99.99, -5.31, 0.99984, 19, 64, -204.44, 0.00016, 2, 18, 137.1, -6.52, 0.99629, 19, 54.39, -168.57, 0.00371, 2, 18, 177.03, -7.98, 0.98499, 19, 44.19, -129.94, 0.01501, 2, 18, 200.76, -55.88, 0.6596, 19, 83.14, -93.32, 0.3404, 1, 18, 232.95, 4.98, 1, 2, 18, 229.8, -76.74, 0.35408, 19, 94.67, -59.48, 0.64592, 2, 18, 267.64, -18.32, 0.74859, 19, 27.78, -40.23, 0.25141, 2, 18, 293.6, -50.03, 0.04804, 19, 50.59, -6.18, 0.95196, 2, 18, 300.72, -5.95, 0.78501, 19, 6.34, -12.16, 0.21499, 6, 18, 343.37, -38.71, 0.01309, 19, 25.31, 38.17, 0.22304, 62, 15.11, 19.03, 0.72415, 63, -26.89, 19.03, 0.03478, 64, -59.64, 39.38, 0.00387, 65, -101.64, 39.38, 0.00107, 7, 18, 343.08, 9.92, 0.58288, 19, -21.14, 23.77, 0.18692, 62, 4.98, 66.59, 0.11462, 63, -37.02, 66.59, 0.07933, 64, -54.58, 87.75, 0.02492, 65, -96.58, 87.75, 0.0108, 66, -134.79, 93.48, 0.00055, 7, 18, 395.57, -40.09, 0.04372, 19, 11.48, 88.51, 0.04659, 62, 66.51, 28.24, 0.06976, 63, 24.5, 28.24, 0.60755, 64, -7.92, 32.26, 0.20506, 65, -49.92, 32.26, 0.02469, 66, -90.49, 36.08, 0.00263, 7, 18, 396.52, -1.92, 0.17733, 19, -25.33, 78.34, 0.10828, 62, 59.71, 65.82, 0.10174, 63, 17.71, 65.82, 0.30604, 64, -2.76, 70.1, 0.18995, 65, -44.77, 70.1, 0.09636, 66, -83.76, 73.68, 0.02029, 8, 18, 439.99, -41.67, 0.02108, 19, 0.09, 131.48, 0.01087, 62, 110.33, 35.69, 0.00041, 63, 68.33, 35.69, 0.09348, 64, 36.06, 25.81, 0.52555, 65, -5.94, 25.81, 0.32228, 66, -46.82, 27.79, 0.02573, 67, -88.82, 27.79, 0.00059, 8, 18, 440.68, 0.24, 0.07964, 19, -40.21, 119.97, 0.0364, 62, 102.52, 76.87, 0.01325, 63, 60.52, 76.87, 0.17372, 64, 41.36, 67.38, 0.25601, 65, -0.64, 67.38, 0.30924, 66, -39.78, 69.11, 0.1201, 67, -81.79, 69.11, 0.01164, 6, 18, 490.73, -55.42, 0.00083, 19, -1.48, 184.02, 0.00021, 63, 120.8, 32.49, 0.00142, 65, 42.97, 6.55, 0.43262, 66, 1.25, 6.51, 0.5614, 67, -40.76, 6.51, 0.00353, 8, 18, 485.58, -15.54, 0.01663, 19, -38.15, 167.52, 0.00535, 63, 107.68, 70.5, 0.03377, 64, 84.25, 46.75, 0.06473, 65, 42.25, 46.75, 0.36797, 66, 2.2, 46.7, 0.41435, 67, -39.8, 46.7, 0.09502, 68, -81.8, 46.7, 0.0022, 4, 64, 126.03, -4.39, 0.00173, 66, 41.8, -6.15, 0.50976, 67, -0.2, -6.15, 0.48837, 68, -42.2, -6.15, 0.00014, 9, 18, 532.42, -15.68, 0.00185, 19, -51.61, 212.38, 0.00017, 63, 153.58, 79.85, 0.00241, 64, 130.79, 41.46, 0.00151, 65, 88.79, 41.46, 0.04852, 66, 48.48, 39.46, 0.3355, 67, 6.48, 39.46, 0.51704, 68, -35.53, 39.46, 0.08326, 69, -77.53, 39.46, 0.00976, 6, 64, 174.85, -8.16, 0.00076, 66, 90.42, -11.96, 0.00035, 67, 48.42, -11.96, 0.2917, 68, 6.42, -11.96, 0.7023, 69, -35.58, -11.96, 0.00456, 70, -74.51, -24.73, 0.00033, 5, 66, 102.21, 39.02, 0.01504, 67, 60.2, 39.02, 0.21126, 68, 18.2, 39.02, 0.55329, 69, -23.8, 39.02, 0.2115, 70, -71.39, 27.5, 0.00891, 10, 64, 218.26, -66.62, 0.00414, 65, 176.26, -66.62, 0.0029, 66, 131.35, -72.19, 0.03145, 67, 89.35, -72.19, 0.14943, 68, 47.35, -72.19, 0.33027, 69, 5.34, -72.19, 0.28212, 70, -24.11, -77.29, 0.14346, 71, -55.9, -84.97, 0.03814, 72, -34.53, -124.95, 0.0143, 73, -16.05, -145.64, 0.0038, 7, 66, 131.54, -15.92, 0.00023, 67, 89.54, -15.92, 0.01105, 68, 47.54, -15.92, 0.34501, 69, 5.54, -15.92, 0.61933, 70, -33.3, -21.78, 0.0228, 72, -77.76, -88.94, 0.00155, 73, -70.49, -131.46, 3e-05, 4, 67, 98, 37.86, 0.01995, 68, 56, 37.86, 0.23583, 69, 14, 37.86, 0.61643, 70, -33.93, 32.66, 0.12778, 8, 66, 157.29, -21.17, 8e-05, 67, 115.28, -21.17, 0.00524, 68, 73.28, -21.17, 0.04092, 69, 31.28, -21.17, 0.62991, 70, -7.04, -22.67, 0.25619, 71, -45.82, -28.64, 0.05694, 72, -57.3, -72.45, 0.00836, 73, -59.01, -107.84, 0.00236, 4, 68, 86.65, 25.61, 0.02357, 69, 44.64, 25.61, 0.43642, 70, -1.67, 25.69, 0.50143, 71, -46.54, 20.02, 0.03859, 7, 67, 151.29, -31.12, 0.00039, 68, 109.29, -31.12, 0.00512, 69, 67.29, -31.12, 0.04607, 70, 30.12, -26.47, 0.46171, 71, -8.47, -27.75, 0.42634, 72, -26.68, -51.05, 0.04382, 73, -40.41, -75.43, 0.01655, 3, 69, 82.95, 17.71, 0.01241, 70, 37.42, 24.29, 0.49529, 71, -7.59, 23.52, 0.4923, 6, 68, 142.95, -42.5, 0.00023, 69, 100.95, -42.5, 0.00326, 70, 65.21, -32.08, 0.05697, 71, 27.04, -28.92, 0.70509, 72, 3.54, -32.36, 0.19876, 73, -21.02, -45.66, 0.03568, 5, 69, 127.77, -60.63, 0.00014, 70, 94.67, -45.47, 0.01031, 71, 57.95, -38.51, 0.30635, 72, 34.6, -23.25, 0.50932, 73, 3.2, -24.19, 0.17387, 4, 70, 124.76, -63.79, 0.00064, 71, 90.1, -52.91, 0.21817, 72, 69.35, -17.45, 0.25968, 73, 32.17, -4.15, 0.52151, 3, 71, 114.93, -72.2, 0.15491, 72, 100.69, -19.78, 0.3582, 73, 61.52, 7.09, 0.48689, 3, 71, 126.25, -125.91, 0.14883, 72, 139.85, -58.25, 0.32069, 73, 113.34, -11.05, 0.53048, 3, 71, 153.9, -93.82, 0.10271, 72, 145.12, -16.22, 0.18921, 73, 100.21, 29.23, 0.70808, 3, 71, 145.89, -147.15, 0.08602, 72, 167.96, -65.07, 0.17214, 73, 141.68, -5.24, 0.74183, 3, 71, 169.7, -133.76, 0.036, 72, 180.38, -40.75, 0.02414, 73, 142.56, 22.05, 0.93987, 3, 71, 163.04, -172.11, 0.00254, 72, 196.06, -76.37, 0.06578, 73, 171.91, -3.51, 0.93167, 2, 72, 222.16, -93.77, 0.04282, 73, 202.94, -8.14, 0.95718, 5, 18, 256.04, -147.68, 0.02932, 19, 154.93, -13.77, 0.92931, 62, -48.36, -105.36, 0.0054, 63, -90.36, -105.36, 0.03008, 64, -158.44, -59.3, 0.0059, 5, 19, 135.98, 29.47, 0.73739, 62, -3.58, -90.4, 0.09027, 63, -45.58, -90.4, 0.13552, 64, -111.23, -58.91, 0.03557, 65, -153.24, -58.91, 0.00124, 6, 19, 133.31, 69.46, 0.40922, 62, 36.5, -91.37, 0.14395, 63, -5.51, -91.37, 0.31212, 64, -73.42, -72.22, 0.11995, 65, -115.42, -72.22, 0.01367, 66, -160.31, -65.56, 0.00109, 7, 19, 127.78, 106.18, 0.19857, 62, 73.56, -89.18, 0.0882, 63, 31.56, -89.18, 0.38775, 64, -37.49, -81.59, 0.26175, 65, -79.49, -81.59, 0.05209, 66, -124.81, -76.43, 0.01138, 67, -166.81, -76.43, 0.00026, 8, 19, 121.54, 144.08, 0.08033, 62, 111.88, -86.41, 0.02698, 63, 69.87, -86.41, 0.30027, 64, -0.19, -90.79, 0.40233, 65, -42.2, -90.79, 0.13519, 66, -87.93, -87.18, 0.0479, 67, -129.93, -87.18, 0.00699, 68, -171.93, -87.18, 1e-05, 8, 19, 102.75, 203.4, 0.0093, 62, 172.65, -73.07, 0.00024, 63, 130.65, -73.07, 0.09482, 64, 61.73, -96.89, 0.33643, 65, 19.73, -96.89, 0.26684, 66, -26.31, -95.87, 0.20691, 67, -68.32, -95.87, 0.07418, 68, -110.32, -95.87, 0.01127, 8, 19, 85.89, 243.07, 0.00028, 63, 171.68, -59.87, 0.02539, 64, 104.83, -97.02, 0.1834, 65, 62.83, -97.02, 0.20801, 66, 16.75, -97.8, 0.31672, 67, -25.26, -97.8, 0.20428, 68, -67.26, -97.8, 0.05867, 69, -109.26, -97.8, 0.00325, 8, 63, 211.51, -40.17, 0.00268, 64, 148.79, -90.59, 0.07399, 65, 106.79, -90.59, 0.09074, 66, 60.94, -93.22, 0.26062, 67, 18.93, -93.22, 0.34979, 68, -23.07, -93.22, 0.18322, 69, -65.07, -93.22, 0.0351, 70, -90.03, -109.77, 0.00385], "hull": 73, "edges": [0, 144, 0, 2, 2, 4, 4, 6, 10, 12, 24, 26, 38, 40, 70, 72, 72, 74, 94, 96, 126, 128, 128, 130, 140, 142, 142, 144, 26, 28, 106, 108, 108, 110, 104, 106, 100, 102, 102, 104, 96, 98, 98, 100, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 74, 76, 76, 78, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 36, 38, 32, 34, 34, 36, 28, 30, 30, 32, 18, 20, 16, 18, 12, 14, 14, 16, 114, 116, 116, 118, 110, 112, 112, 114, 122, 124, 124, 126, 20, 22, 22, 24, 134, 136, 130, 132, 132, 134, 6, 8, 8, 10, 136, 138, 138, 140, 118, 120, 120, 122, 120, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 138, 162, 162, 4, 136, 164, 164, 8, 134, 166, 166, 10, 132, 168, 168, 14, 130, 170, 170, 16, 128, 172, 172, 18, 124, 174, 174, 176, 176, 20, 122, 178, 178, 180, 180, 22, 148, 182, 182, 184, 184, 24, 150, 186, 186, 188, 188, 26, 152, 190, 190, 192, 192, 30, 154, 194, 194, 196, 196, 34, 156, 198, 198, 200, 200, 36, 158, 202, 202, 204, 204, 40, 160, 206, 206, 208, 208, 42, 94, 210, 210, 160, 210, 212, 212, 214, 214, 44, 94, 216, 216, 218, 218, 46, 92, 220, 220, 222, 222, 48, 90, 224, 224, 50, 88, 226, 226, 52, 86, 228, 228, 54, 84, 230, 230, 58, 82, 232, 232, 234, 234, 60, 78, 80, 80, 82, 80, 236, 236, 238, 238, 64, 78, 240, 240, 66, 76, 242, 242, 68, 116, 244, 244, 146, 114, 246, 246, 148, 112, 248, 248, 150, 110, 250, 250, 152, 108, 252, 252, 154, 106, 254, 254, 156, 104, 256, 256, 158, 100, 258, 258, 160], "width": 450, "height": 730}}, "ys2": {"ys2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-57.5, 36.53, 30.24, 144.33, 200.09, 6.08, 112.35, -101.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 108, "height": 170}}, "zs2": {"zs2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [4.86, 57.2, 160.77, 0.23, 132.29, -77.73, -23.63, -20.76], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 166, "height": 83}}, "zs3": {"zs3": {"type": "mesh", "uvs": [0.3721, 0, 0.50642, 0.04218, 0.61554, 0.07644, 0.75726, 0.13271, 0.88251, 0.18244, 0.97257, 0.3385, 0.99462, 0.52588, 0.99144, 0.66784, 0.82084, 0.80544, 0.51797, 0.87685, 0.43418, 0.79296, 0.34878, 0.70869, 0.21949, 0.41695, 0.24047, 0.33854, 0.35384, 0.26774, 0.32319, 0.21904, 0.26565, 0.24445, 0.0908, 0.24459, 0, 0.17925, 0, 0.17532, 0.08432, 0.13577, 0.18325, 0.07787, 0.28133, 0.02047, 0.51121, 0.19202], "triangles": [18, 19, 20, 21, 22, 15, 16, 21, 15, 20, 21, 16, 17, 20, 16, 18, 20, 17, 23, 1, 2, 15, 22, 0, 23, 15, 0, 23, 0, 1, 14, 15, 23, 23, 2, 3, 23, 11, 14, 5, 23, 3, 5, 6, 23, 14, 12, 13, 11, 12, 14, 11, 8, 10, 5, 3, 4, 11, 6, 8, 6, 11, 23, 7, 8, 6, 9, 10, 8], "vertices": [2, 10, 40.84, -11.47, 0.98928, 11, -14.96, -7.66, 0.01072, 1, 10, 23.69, -14.54, 1, 2, 9, 17.34, -52.18, 2e-05, 10, 9.76, -17.04, 0.99998, 2, 9, 4.46, -38.31, 0.1608, 10, -9.07, -18.93, 0.8392, 2, 9, -6.92, -26.06, 0.53074, 10, -25.71, -20.6, 0.46926, 2, 9, -8.65, -2.87, 0.96746, 10, -45.24, -7.97, 0.03254, 1, 9, -1.06, 20.63, 1, 1, 9, 6.92, 37.44, 1, 1, 9, 33.45, 45.28, 1, 1, 9, 71.26, 38.54, 1, 1, 9, 76.16, 24.29, 1, 1, 9, 81.22, 9.92, 1, 1, 9, 80.06, -31.46, 1, 1, 9, 73.49, -39.77, 1, 3, 9, 56.97, -42.52, 0.27176, 10, 25.99, 20.39, 0.57827, 11, 3.21, 22.43, 0.14997, 3, 9, 57.8, -49.88, 0.05672, 10, 32.36, 16.6, 0.40106, 11, 3.61, 15.03, 0.54223, 3, 9, 65.62, -49.75, 0.0112, 10, 36.97, 22.92, 0.09513, 11, 11.43, 14.72, 0.89367, 1, 11, 30.52, 4.81, 1, 1, 11, 36.48, -7.94, 1, 1, 11, 36.24, -8.39, 1, 1, 11, 24.65, -8.2, 1, 2, 10, 56.33, 8.63, 0.0035, 11, 10.35, -9.32, 0.9965, 2, 10, 49.35, -3.76, 0.51567, 11, -3.82, -10.43, 0.48433, 3, 9, 35.25, -43.63, 0.01416, 10, 13.76, 2.4, 0.98257, 11, -18.54, 22.56, 0.00327], "hull": 23, "edges": [0, 44, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 20, 22, 22, 24, 32, 34, 34, 36, 36, 38, 38, 40, 18, 20, 32, 30, 30, 28, 24, 26, 28, 26, 40, 42, 42, 44, 0, 2, 2, 4, 4, 6, 6, 8, 28, 46, 46, 4], "width": 123, "height": 131}}, "zs1": {"zs1": {"type": "mesh", "uvs": [0.46374, 0.03031, 0.49588, 0.0577, 0.54511, 0.11837, 0.58584, 0.16855, 0.63805, 0.23289, 0.72028, 0.22811, 0.74144, 0.24606, 0.79052, 0.28771, 0.81551, 0.30891, 0.84928, 0.33757, 0.87054, 0.35561, 0.82678, 0.36405, 0.83219, 0.41636, 0.83802, 0.47276, 0.84351, 0.52586, 0.86778, 0.56735, 0.90119, 0.58967, 0.95412, 0.62502, 0.99693, 0.65362, 0.99689, 0.68913, 0.99685, 0.72594, 0.99681, 0.76654, 0.99677, 0.79675, 0.99674, 0.8298, 0.99671, 0.85718, 0.99668, 0.88456, 0.99663, 0.92233, 0.9966, 0.9516, 0.99656, 0.9856, 0.9288, 0.99056, 0.86713, 0.99508, 0.79991, 1, 0.73969, 1, 0.67867, 0.98009, 0.63137, 0.96466, 0.57237, 0.9454, 0.49897, 0.91344, 0.43283, 0.88463, 0.36691, 0.85592, 0.30518, 0.82904, 0.25614, 0.79732, 0.21126, 0.76829, 0.17137, 0.74248, 0.12262, 0.71096, 0.07209, 0.67827, 0.02384, 0.64706, 0.01756, 0.61592, 0.01129, 0.58491, 0.0049, 0.55325, 0, 0.52898, 0, 0.50346, 0.05433, 0.48038, 0.12768, 0.44922, 0.10549, 0.42969, 0.11393, 0.38895, 0.12008, 0.35921, 0.12469, 0.33697, 0.10586, 0.31747, 0.08093, 0.27112, 0.05236, 0.21798, 0.09343, 0.15573, 0.28309, 0.07495, 0.38985, 0.03946, 0.1583, 0.51001, 0.19847, 0.5493, 0.23627, 0.60987, 0.28116, 0.67535, 0.33077, 0.73674, 0.39457, 0.7924, 0.45363, 0.84069, 0.57885, 0.90863, 0.71352, 0.96102, 0.3635, 0.09946, 0.23059, 0.16011, 0.14954, 0.22188, 0.44454, 0.16235, 0.27597, 0.23535, 0.50289, 0.20615, 0.29542, 0.27466, 0.51586, 0.26568, 0.30515, 0.30948, 0.29218, 0.37574, 0.50289, 0.32969, 0.34385, 0.42854, 0.61365, 0.3851, 0.35748, 0.48707, 0.5646, 0.46913, 0.74446, 0.4342, 0.382, 0.52767, 0.63545, 0.50407, 0.40926, 0.57771, 0.68996, 0.54278, 0.51554, 0.64664, 0.78534, 0.61265, 0.58912, 0.69762, 0.8562, 0.67024, 0.63, 0.75899, 0.90526, 0.73633, 0.67906, 0.81753, 0.92433, 0.80148, 0.71994, 0.88079, 0.94068, 0.86096, 0.79897, 0.94027, 0.91343, 0.97048], "triangles": [31, 103, 30, 71, 31, 32, 32, 33, 71, 31, 71, 102, 30, 103, 29, 103, 31, 102, 29, 103, 28, 28, 103, 27, 33, 34, 71, 27, 103, 26, 34, 35, 71, 100, 102, 71, 26, 103, 102, 71, 35, 70, 35, 36, 70, 71, 70, 100, 26, 102, 25, 102, 100, 101, 25, 102, 101, 25, 101, 24, 100, 70, 69, 100, 98, 101, 24, 101, 23, 23, 101, 99, 23, 99, 22, 22, 99, 21, 36, 37, 70, 70, 37, 69, 37, 38, 69, 69, 98, 100, 98, 99, 101, 38, 39, 69, 39, 68, 69, 98, 69, 96, 98, 96, 99, 21, 99, 97, 21, 97, 20, 20, 97, 19, 39, 40, 68, 69, 68, 96, 96, 97, 99, 68, 67, 96, 96, 94, 97, 19, 97, 95, 19, 95, 18, 95, 17, 18, 68, 40, 67, 40, 41, 67, 41, 42, 67, 67, 94, 96, 94, 66, 92, 94, 67, 66, 94, 95, 97, 94, 93, 95, 94, 92, 93, 95, 93, 17, 93, 16, 17, 67, 42, 66, 42, 43, 66, 43, 44, 66, 66, 44, 65, 66, 65, 92, 92, 91, 93, 92, 90, 91, 44, 45, 65, 45, 46, 65, 65, 90, 92, 46, 47, 65, 65, 64, 90, 93, 15, 16, 93, 91, 15, 47, 64, 65, 47, 48, 64, 64, 88, 90, 90, 89, 91, 90, 88, 89, 91, 14, 15, 48, 63, 64, 48, 49, 63, 64, 63, 88, 91, 89, 14, 49, 50, 63, 63, 85, 88, 88, 86, 89, 88, 85, 86, 89, 13, 14, 50, 51, 63, 51, 52, 63, 63, 52, 85, 89, 86, 87, 52, 83, 85, 85, 83, 86, 83, 53, 81, 83, 52, 53, 89, 87, 13, 87, 12, 13, 86, 84, 87, 86, 83, 84, 87, 84, 12, 53, 54, 81, 84, 81, 82, 84, 83, 81, 84, 11, 12, 54, 55, 81, 84, 8, 11, 8, 82, 7, 8, 84, 82, 81, 80, 82, 81, 55, 80, 11, 9, 10, 11, 8, 9, 55, 56, 80, 80, 56, 78, 7, 79, 6, 6, 4, 5, 4, 6, 79, 79, 7, 82, 82, 80, 79, 56, 57, 78, 76, 57, 58, 76, 58, 74, 57, 76, 78, 80, 78, 79, 79, 78, 77, 58, 59, 74, 78, 76, 77, 79, 77, 4, 74, 73, 76, 76, 75, 77, 76, 73, 75, 4, 77, 3, 59, 60, 74, 74, 60, 73, 77, 75, 3, 75, 2, 3, 73, 72, 75, 75, 72, 2, 72, 73, 61, 73, 60, 61, 72, 1, 2, 72, 62, 1, 62, 0, 1, 72, 61, 62], "vertices": [1, 6, -42.61, 40.94, 1, 1, 6, -19.48, 43.72, 1, 1, 6, 30.18, 44.11, 1, 1, 6, 71.26, 44.43, 1, 1, 6, 123.92, 44.84, 1, 1, 6, 126.19, 67.49, 1, 1, 6, 141.36, 69.34, 1, 1, 6, 176.55, 73.64, 1, 1, 6, 194.46, 75.82, 1, 1, 6, 218.67, 78.77, 1, 1, 6, 233.91, 80.63, 1, 1, 6, 237.19, 67.36, 1, 4, 6, 277.34, 57.94, 0.99966, 56, -57.34, 183.04, 0.00012, 57, -108.02, 183.04, 0.0002, 58, -158.69, 183.04, 1e-05, 6, 6, 320.64, 47.78, 0.82597, 7, -52.76, 18.92, 0.12368, 56, -19.12, 160.29, 0.0236, 57, -69.8, 160.29, 0.02017, 58, -120.47, 160.29, 0.00602, 59, -171.15, 160.29, 0.00056, 6, 6, 361.4, 38.21, 0.25604, 7, -38.29, 58.2, 0.47729, 56, 16.85, 138.88, 0.11208, 57, -33.82, 138.88, 0.10565, 58, -84.5, 138.88, 0.04048, 59, -135.17, 138.88, 0.00844, 6, 6, 394.69, 36, 0.05881, 7, -32.03, 90.97, 0.41504, 56, 47.92, 126.73, 0.16214, 57, -2.75, 126.73, 0.21886, 58, -53.43, 126.73, 0.11215, 59, -104.1, 126.73, 0.033, 6, 6, 414.05, 40.18, 0.02057, 7, -33.8, 110.7, 0.32166, 56, 67.64, 124.86, 0.14753, 57, 16.97, 124.86, 0.27191, 58, -33.71, 124.86, 0.17651, 59, -84.38, 124.86, 0.06182, 7, 6, 444.74, 46.79, 0.0025, 7, -36.61, 141.96, 0.20449, 56, 98.89, 121.91, 0.09421, 57, 48.22, 121.91, 0.28554, 58, -2.46, 121.91, 0.28227, 59, -53.13, 121.91, 0.13087, 60, -103.81, 121.91, 0.00012, 7, 6, 469.55, 52.13, 7e-05, 7, -38.88, 167.24, 0.14392, 56, 124.16, 119.52, 0.05763, 57, 73.49, 119.52, 0.25152, 58, 22.81, 119.52, 0.34348, 59, -27.86, 119.52, 0.20019, 60, -78.54, 119.52, 0.0032, 6, 7, -28.27, 193.13, 0.09176, 56, 147.67, 104.35, 0.0282, 57, 97, 104.35, 0.1857, 58, 46.32, 104.35, 0.37243, 59, -4.36, 104.35, 0.30157, 60, -55.03, 104.35, 0.02034, 6, 7, -17.26, 219.97, 0.04538, 56, 172.05, 88.62, 0.00739, 57, 121.37, 88.62, 0.09498, 58, 70.7, 88.62, 0.32012, 59, 20.02, 88.62, 0.44836, 60, -30.65, 88.62, 0.08377, 6, 7, -5.12, 249.57, 0.01515, 56, 198.93, 71.27, 0.00033, 57, 148.25, 71.27, 0.02587, 58, 97.58, 71.27, 0.16297, 59, 46.9, 71.27, 0.51864, 60, -3.77, 71.27, 0.27704, 6, 7, 3.91, 271.6, 0.00472, 57, 168.25, 58.36, 0.00506, 58, 117.58, 58.36, 0.06085, 59, 66.9, 58.36, 0.38335, 60, 16.23, 58.36, 0.54006, 61, -34.45, 58.36, 0.00596, 6, 7, 13.79, 295.69, 0.0006, 57, 190.14, 44.24, 3e-05, 58, 139.46, 44.24, 0.00935, 59, 88.79, 44.24, 0.12732, 60, 38.11, 44.24, 0.75617, 61, -12.56, 44.24, 0.10652, 5, 7, 21.98, 315.66, 1e-05, 58, 157.59, 32.54, 0.00019, 59, 106.91, 32.54, 0.01499, 60, 56.24, 32.54, 0.5478, 61, 5.56, 32.54, 0.43702, 2, 60, 74.37, 20.84, 0.10471, 61, 23.69, 20.84, 0.89529, 1, 61, 48.7, 4.7, 1, 1, 61, 68.07, -7.8, 1, 1, 61, 90.59, -22.34, 1, 3, 59, 185.2, -40, 0.00187, 60, 134.52, -40, 0.00738, 61, 83.85, -40, 0.99075, 3, 59, 179.07, -56.08, 0.00853, 60, 128.39, -56.08, 0.03007, 61, 77.72, -56.08, 0.9614, 3, 59, 172.38, -73.6, 0.02034, 60, 121.7, -73.6, 0.06454, 61, 71.03, -73.6, 0.91513, 4, 58, 214.14, -87.42, 4e-05, 59, 163.47, -87.42, 0.03176, 60, 112.79, -87.42, 0.09292, 61, 62.12, -87.42, 0.87529, 4, 58, 191.93, -92.92, 0.00208, 59, 141.26, -92.92, 0.06466, 60, 90.58, -92.92, 0.15803, 61, 39.91, -92.92, 0.77522, 5, 57, 225.39, -97.18, 8e-05, 58, 174.71, -97.18, 0.00823, 59, 124.04, -97.18, 0.11439, 60, 73.36, -97.18, 0.23123, 61, 22.69, -97.18, 0.64607, 5, 57, 203.91, -102.49, 0.00179, 58, 153.23, -102.49, 0.02519, 59, 102.56, -102.49, 0.19935, 60, 51.88, -102.49, 0.3064, 61, 1.21, -102.49, 0.46727, 6, 56, 222.55, -105.68, 0.00034, 57, 171.88, -105.68, 0.01496, 58, 121.2, -105.68, 0.08277, 59, 70.53, -105.68, 0.34996, 60, 19.85, -105.68, 0.32008, 61, -30.82, -105.68, 0.23189, 6, 56, 193.7, -108.56, 0.00529, 57, 143.02, -108.56, 0.04984, 58, 92.35, -108.56, 0.1783, 59, 41.67, -108.56, 0.44345, 60, -9, -108.56, 0.23157, 61, -59.68, -108.56, 0.09155, 7, 55, 215.6, -111.42, 4e-05, 56, 164.93, -111.42, 0.02311, 57, 114.25, -111.42, 0.11865, 58, 63.58, -111.42, 0.28806, 59, 12.9, -111.42, 0.42098, 60, -37.77, -111.42, 0.12393, 61, -88.45, -111.42, 0.02523, 7, 55, 188.67, -114.11, 0.00217, 56, 137.99, -114.11, 0.05824, 57, 87.32, -114.11, 0.20884, 58, 36.64, -114.11, 0.34836, 59, -14.03, -114.11, 0.3216, 60, -64.71, -114.11, 0.05661, 61, -115.38, -114.11, 0.00419, 7, 55, 160.4, -111.81, 0.01321, 56, 109.73, -111.81, 0.12581, 57, 59.05, -111.81, 0.31232, 58, 8.38, -111.81, 0.33233, 59, -42.3, -111.81, 0.19734, 60, -92.97, -111.81, 0.01898, 61, -143.65, -111.81, 1e-05, 7, 7, 193.85, 169.55, 0.00017, 55, 134.54, -109.71, 0.04107, 56, 83.86, -109.71, 0.22601, 57, 33.19, -109.71, 0.37618, 58, -17.49, -109.71, 0.25027, 59, -68.16, -109.71, 0.10216, 60, -118.84, -109.71, 0.00413, 7, 7, 196.22, 146.61, 0.00369, 55, 111.55, -107.84, 0.08952, 56, 60.87, -107.84, 0.33246, 57, 10.2, -107.84, 0.36632, 58, -40.48, -107.84, 0.15973, 59, -91.15, -107.84, 0.0479, 60, -141.83, -107.84, 0.00037, 6, 7, 199.12, 118.57, 0.02352, 55, 83.46, -105.56, 0.18913, 56, 32.78, -105.56, 0.42889, 57, -17.89, -105.56, 0.27129, 58, -68.57, -105.56, 0.07288, 59, -119.24, -105.56, 0.0143, 6, 7, 202.12, 89.51, 0.0795, 55, 54.33, -103.2, 0.32068, 56, 3.66, -103.2, 0.42614, 57, -47.02, -103.2, 0.14825, 58, -97.69, -103.2, 0.02335, 59, -148.37, -103.2, 0.00208, 6, 7, 204.99, 61.76, 0.17592, 55, 26.53, -100.94, 0.40575, 56, -24.15, -100.94, 0.3411, 57, -74.82, -100.94, 0.07171, 58, -125.5, -100.94, 0.0055, 59, -176.17, -100.94, 1e-05, 5, 7, 197.27, 38.4, 0.3165, 55, 4.97, -89.08, 0.41557, 56, -45.7, -89.08, 0.23532, 57, -96.38, -89.08, 0.03188, 58, -147.05, -89.08, 0.00073, 4, 7, 189.59, 15.14, 0.55281, 55, -16.49, -77.28, 0.31938, 56, -67.17, -77.28, 0.11942, 57, -117.84, -77.28, 0.00839, 5, 6, 321.99, -188.34, 0.00018, 7, 181.75, -8.6, 0.82206, 55, -38.4, -65.23, 0.13753, 56, -89.07, -65.23, 0.03959, 57, -139.75, -65.23, 0.00064, 4, 6, 303.18, -184.6, 0.00449, 7, 175.74, -26.81, 0.94928, 55, -55.2, -55.99, 0.03579, 56, -105.87, -55.99, 0.01043, 4, 6, 283.78, -179.31, 0.01618, 7, 168.12, -45.42, 0.98049, 55, -72.1, -45.09, 0.00225, 56, -122.77, -45.09, 0.00108, 2, 6, 270.14, -160.21, 0.05222, 7, 147.5, -56.62, 0.94778, 2, 6, 251.72, -134.43, 0.22068, 7, 119.66, -71.76, 0.77932, 2, 6, 235.28, -136.23, 0.34473, 7, 119.43, -88.3, 0.65527, 2, 6, 204.91, -125.56, 0.55156, 7, 105.13, -117.13, 0.44844, 2, 6, 182.74, -117.77, 0.70023, 7, 94.69, -138.18, 0.29977, 2, 6, 166.17, -111.95, 0.80108, 7, 86.88, -153.92, 0.19892, 2, 6, 149.99, -112.86, 0.8693, 7, 85.82, -170.09, 0.1307, 2, 6, 112.96, -109.82, 0.95346, 7, 78.27, -206.47, 0.04654, 2, 6, 70.51, -106.32, 0.98983, 7, 69.61, -248.17, 0.01017, 2, 6, 26.14, -82.6, 0.99969, 7, 40.64, -289.31, 0.00031, 1, 6, -21.65, -15.9, 1, 1, 6, -40.96, 19.58, 1, 2, 6, 300.13, -138.97, 0.02484, 7, 130.08, -24.26, 0.97516, 3, 7, 131.67, 8.54, 0.72924, 55, -12.38, -19.13, 0.26135, 56, -63.06, -19.13, 0.00941, 5, 7, 140.21, 56.62, 0.08044, 55, 33.32, -36.32, 0.63425, 56, -17.35, -36.32, 0.26781, 57, -68.03, -36.32, 0.01721, 58, -118.7, -36.32, 0.00029, 6, 7, 148.43, 109.02, 0.01104, 55, 83.33, -53.99, 0.15371, 56, 32.66, -53.99, 0.54935, 57, -18.02, -53.99, 0.24799, 58, -68.69, -53.99, 0.03386, 59, -119.37, -53.99, 0.00405, 7, 7, 154.23, 158.92, 8e-05, 55, 131.33, -68.82, 0.03407, 56, 80.65, -68.82, 0.22565, 57, 29.98, -68.82, 0.45173, 58, -20.7, -68.82, 0.22593, 59, -71.37, -68.82, 0.06157, 60, -122.05, -68.82, 0.00098, 7, 55, 177.63, -77.95, 0.00331, 56, 126.95, -77.95, 0.06595, 57, 76.28, -77.95, 0.24508, 58, 25.6, -77.95, 0.39712, 59, -25.07, -77.95, 0.25896, 60, -75.75, -77.95, 0.02892, 61, -126.42, -77.95, 0.00065, 6, 56, 167.67, -85.02, 0.01586, 57, 117, -85.02, 0.09414, 58, 66.32, -85.02, 0.27582, 59, 15.65, -85.02, 0.45479, 60, -35.03, -85.02, 0.13279, 61, -85.7, -85.02, 0.0266, 5, 57, 180.52, -85.3, 0.0073, 58, 129.84, -85.3, 0.05332, 59, 79.17, -85.3, 0.29686, 60, 28.49, -85.3, 0.34809, 61, -22.19, -85.3, 0.29443, 4, 58, 184.46, -76.78, 0.00243, 59, 133.78, -76.78, 0.06792, 60, 83.11, -76.78, 0.17109, 61, 32.43, -76.78, 0.75856, 1, 6, 2.76, 0.2, 1, 2, 6, 39.32, -47.38, 0.99992, 7, 7.3, -271.93, 8e-05, 2, 6, 80.45, -81.54, 0.989, 7, 46.23, -235.27, 0.011, 1, 6, 56.39, 8.5, 1, 2, 6, 99.78, -51.03, 0.98893, 7, 18.31, -212.36, 0.01107, 1, 6, 93.88, 14.79, 1, 2, 6, 131.06, -54.06, 0.96516, 7, 25.14, -181.69, 0.03484, 1, 6, 140.07, 5.86, 1, 2, 6, 158.23, -58.72, 0.91888, 7, 33.08, -155.29, 0.08112, 2, 6, 207.67, -75.87, 0.69585, 7, 56.15, -108.32, 0.30415, 2, 6, 187.8, -10.83, 0.98914, 7, -10.83, -120.09, 0.01086, 2, 6, 251.52, -73.21, 0.42975, 7, 58.87, -64.47, 0.57025, 1, 6, 237.88, 6.86, 1, 2, 6, 297, -81.76, 0.06366, 7, 72.91, -20.38, 0.93634, 2, 6, 298.24, -23.49, 0.21495, 7, 15.23, -12.02, 0.78505, 4, 6, 284.6, 31.13, 0.99848, 56, -58.5, 155.29, 0.00064, 57, -109.18, 155.29, 0.00076, 58, -159.85, 155.29, 0.00012, 3, 7, 78.84, 11.77, 0.79722, 55, 0.45, 32.22, 0.1975, 56, -50.22, 32.22, 0.00528, 7, 6, 329.89, -12.07, 0.02786, 7, 7.76, 20.78, 0.89219, 55, 22.32, 100.45, 0.00743, 56, -28.36, 100.45, 0.04536, 57, -79.03, 100.45, 0.02105, 58, -129.71, 100.45, 0.00553, 59, -180.38, 100.45, 0.00059, 5, 7, 86.91, 51.08, 0.13483, 55, 37.62, 17.1, 0.71894, 56, -13.05, 17.1, 0.14515, 57, -63.73, 17.1, 0.00104, 58, -114.4, 17.1, 3e-05, 7, 6, 363.23, -5.74, 0.05837, 7, 5.56, 54.65, 0.62371, 55, 56.02, 96.42, 0.01663, 56, 5.34, 96.42, 0.16423, 57, -45.33, 96.42, 0.10029, 58, -96.01, 96.42, 0.03101, 59, -146.68, 96.42, 0.00576, 5, 7, 80.64, 112.33, 0.02391, 56, 48.32, 12.05, 0.59679, 57, -2.36, 12.05, 0.37629, 58, -53.03, 12.05, 0.00245, 59, -103.71, 12.05, 0.00056, 7, 6, 423.2, 4.9, 0.00797, 7, 2.33, 115.47, 0.27957, 55, 116.4, 88.47, 0.00014, 56, 65.72, 88.47, 0.17336, 57, 15.05, 88.47, 0.31488, 58, -35.63, 88.47, 0.17062, 59, -86.3, 88.47, 0.05347, 4, 7, 77.28, 157.13, 0.00464, 57, 42.29, 7.16, 0.92784, 58, -8.38, 7.16, 0.06534, 59, -59.06, 7.16, 0.00217, 6, 7, 1.63, 164.8, 0.11987, 56, 114.35, 80.13, 0.05228, 57, 63.67, 80.13, 0.27048, 58, 13, 80.13, 0.37168, 59, -37.68, 80.13, 0.18283, 60, -88.35, 80.13, 0.00287, 4, 56, 139.66, -9.67, 0.00047, 57, 88.98, -9.67, 0.00137, 58, 38.31, -9.67, 0.92457, 59, -12.37, -9.67, 0.07359, 6, 7, 8.98, 218.07, 0.03274, 56, 165.37, 63.16, 0.00374, 57, 114.7, 63.16, 0.07031, 58, 64.02, 63.16, 0.31479, 59, 13.35, 63.16, 0.49134, 60, -37.33, 63.16, 0.08709, 5, 57, 135.01, -23.41, 0.00311, 58, 84.33, -23.41, 0.04547, 59, 33.66, -23.41, 0.84359, 60, -17.02, -23.41, 0.10486, 61, -67.69, -23.41, 0.00297, 6, 7, 23.62, 267.55, 0.00317, 57, 160.67, 39.72, 0.0026, 58, 109.99, 39.72, 0.04345, 59, 59.32, 39.72, 0.39145, 60, 8.64, 39.72, 0.5557, 61, -42.03, 39.72, 0.00364, 5, 57, 182.95, -41.04, 0.00065, 58, 132.27, -41.04, 0.01266, 59, 81.6, -41.04, 0.18463, 60, 30.92, -41.04, 0.56717, 61, -19.75, -41.04, 0.2349, 3, 59, 101.13, 18.07, 0.00631, 60, 50.45, 18.07, 0.57616, 61, -0.22, 18.07, 0.41752, 4, 58, 183.36, -48.31, 0.00041, 59, 132.68, -48.31, 0.03494, 60, 82.01, -48.31, 0.12737, 61, 31.33, -48.31, 0.83727, 3, 59, 169.63, -34.95, 0.00257, 60, 118.95, -34.95, 0.00939, 61, 68.28, -34.95, 0.98803], "hull": 63, "edges": [8, 10, 20, 22, 28, 30, 62, 64, 98, 100, 104, 106, 112, 114, 118, 120, 120, 122, 122, 124, 0, 124, 0, 2, 100, 102, 102, 104, 102, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 62, 76, 78, 74, 76, 70, 72, 72, 74, 68, 70, 64, 66, 66, 68, 60, 62, 56, 58, 58, 60, 88, 90, 86, 88, 84, 86, 82, 84, 78, 80, 80, 82, 96, 98, 94, 96, 90, 92, 92, 94, 2, 144, 144, 146, 146, 148, 114, 116, 116, 118, 148, 116, 2, 4, 4, 150, 150, 152, 152, 114, 4, 6, 6, 8, 6, 154, 154, 156, 156, 112, 8, 158, 158, 160, 110, 112, 160, 110, 106, 162, 162, 164, 164, 14, 10, 12, 12, 14, 18, 20, 104, 166, 166, 168, 168, 22, 126, 170, 170, 172, 172, 174, 22, 24, 174, 24, 128, 176, 176, 178, 24, 26, 26, 28, 178, 26, 130, 180, 180, 182, 182, 28, 132, 184, 184, 186, 30, 32, 186, 32, 134, 188, 188, 190, 190, 36, 32, 34, 34, 36, 136, 192, 192, 194, 194, 40, 138, 196, 196, 198, 198, 44, 140, 200, 200, 202, 202, 48, 142, 204, 204, 52, 62, 206, 52, 54, 54, 56, 206, 54, 40, 42, 42, 44, 36, 38, 38, 40, 48, 50, 50, 52, 44, 46, 46, 48, 106, 108, 108, 110, 14, 16, 16, 18], "width": 212, "height": 614}}, "zs5": {"zs5": {"type": "mesh", "uvs": [0.96754, 0.07468, 1, 0.1928, 1, 0.22546, 0.97363, 0.32916, 0.83508, 0.50353, 0.61986, 0.67267, 0.27922, 0.82275, 0.22938, 0.86573, 0.07091, 0.98481, 0.00881, 0.93029, 0.00988, 0.82969, 0.0434, 0.72833, 0.16026, 0.51688, 0.26603, 0.43857, 0.52894, 0.24391, 0.62847, 0.17023, 0.83519, 0.01717, 0.90302, 0.01724], "triangles": [6, 13, 5, 12, 13, 6, 7, 11, 12, 6, 7, 12, 8, 11, 7, 10, 11, 8, 9, 10, 8, 3, 0, 1, 2, 3, 1, 4, 15, 16, 3, 4, 16, 17, 3, 16, 3, 17, 0, 5, 14, 15, 5, 15, 4, 13, 14, 5], "vertices": [1, 12, -10.64, -3.31, 1, 1, 12, -11.01, 4.2, 1, 1, 12, -10.27, 5.9, 1, 1, 12, -5.41, 10.23, 1, 1, 12, 11.64, 13.63, 1, 2, 12, 35.82, 13.59, 0.86328, 13, -8.94, 13.29, 0.13672, 1, 13, 26.88, 8.7, 1, 1, 13, 32.55, 9.15, 1, 1, 13, 50.22, 9.64, 1, 1, 13, 55.08, 4.45, 1, 1, 13, 52.92, -0.86, 1, 1, 13, 47.63, -5.02, 1, 1, 13, 32.07, -11.96, 1, 1, 13, 20.3, -12.23, 1, 2, 12, 34.62, -12.56, 0.87272, 13, -8.95, -12.89, 0.12728, 2, 12, 23.54, -12.3, 0.99995, 13, -20.03, -13.13, 5e-05, 1, 12, 0.54, -11.77, 1, 1, 12, -5.86, -8.97, 1], "hull": 18, "edges": [0, 34, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 32, 34, 30, 32, 28, 30, 24, 26, 26, 28], "width": 103, "height": 57}}, "yy": {"yy": {"type": "mesh", "uvs": [0.75493, 0.10594, 0.867, 0.33696, 0.97007, 0.54942, 0.96991, 0.7009, 0.96977, 0.8415, 0.67913, 1, 0.61386, 1, 0.39357, 0.88156, 0.03629, 0.42353, 0.02207, 0.20403, 0.20172, 0.11448, 0.4314, 0, 0.80148, 0.50287, 0.61982, 0.40207, 0.43816, 0.35527, 0.2565, 0.44167, 0.48025, 0.57487, 0.56222, 0.78367, 0.69293, 0.82687, 0.87237, 0.74767, 0.88199, 0.59753], "triangles": [13, 14, 11, 10, 11, 14, 0, 13, 11, 13, 0, 1, 8, 9, 10, 15, 10, 14, 8, 10, 15, 12, 13, 1, 20, 12, 1, 16, 14, 13, 2, 20, 1, 3, 20, 2, 19, 12, 20, 19, 20, 3, 17, 16, 13, 18, 17, 13, 12, 18, 13, 19, 18, 12, 4, 19, 3, 16, 7, 15, 16, 15, 14, 7, 16, 17, 8, 15, 7, 6, 17, 18, 7, 17, 6, 5, 6, 18, 5, 18, 19, 5, 19, 4], "vertices": [1, 28, 11.84, -19.99, 1, 1, 28, 2.7, -12.58, 1, 1, 28, -5.7, -5.76, 1, 1, 28, -7.02, 0.15, 1, 1, 28, -8.25, 5.64, 1, 1, 28, 8.78, 15.98, 1, 1, 28, 12.92, 16.91, 1, 1, 28, 27.93, 15.43, 1, 1, 28, 54.61, 2.67, 1, 1, 28, 57.45, -5.7, 1, 1, 28, 46.84, -11.76, 1, 1, 28, 33.29, -19.51, 1, 1, 28, 5.4, -5.17, 1, 1, 28, 17.8, -6.51, 1, 1, 28, 29.73, -5.74, 1, 1, 28, 40.49, 0.23, 1, 1, 28, 25.13, 2.23, 1, 1, 28, 18.1, 9.21, 1, 1, 28, 9.43, 9.02, 1, 1, 28, -1.25, 3.37, 1, 1, 28, -0.54, -2.63, 1], "hull": 12, "edges": [8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 0, 2, 2, 4, 22, 0, 4, 6, 6, 8, 24, 26, 26, 28, 28, 30, 30, 16, 28, 32, 32, 34, 34, 36, 36, 38, 6, 40, 40, 24, 38, 40], "width": 50, "height": 31}}, "st": {"st": {"type": "mesh", "uvs": [0.78555, 0.00116, 0.91615, 0.00052, 0.97497, 0.01117, 0.97858, 0.01893, 0.98435, 0.04671, 0.99118, 0.07957, 0.9955, 0.11825, 1, 0.15851, 1, 0.16772, 0.99293, 0.18557, 0.98501, 0.20558, 0.97667, 0.22665, 0.968, 0.24855, 0.96327, 0.26853, 0.95757, 0.29257, 0.95447, 0.33503, 0.95191, 0.37011, 0.95795, 0.41097, 0.9689, 0.45485, 0.97181, 0.46649, 0.97974, 0.50848, 0.97898, 0.54882, 0.97799, 0.60105, 0.97736, 0.63456, 0.97199, 0.67888, 0.97487, 0.71777, 0.97862, 0.73796, 0.98751, 0.78581, 0.9897, 0.79765, 0.98943, 0.81951, 0.98909, 0.84767, 0.97177, 0.89689, 0.96316, 0.92137, 0.95821, 0.95472, 0.95459, 0.97908, 0.95825, 0.99911, 0.85028, 0.99916, 0.74418, 0.99922, 0.64906, 0.99927, 0.54906, 0.99932, 0.44296, 0.99937, 0.37944, 0.99941, 0.24731, 0.99947, 0.13266, 0.99953, 0.04851, 0.99958, 0.00105, 0.9996, 0.00096, 0.94892, 0.00086, 0.89631, 0.00073, 0.82414, 0.05065, 0.81289, 0.08311, 0.80558, 0.10547, 0.79997, 0.16013, 0.78576, 0.20865, 0.77532, 0.21819, 0.77328, 0.24588, 0.76412, 0.32615, 0.73757, 0.37245, 0.71706, 0.38048, 0.71217, 0.4033, 0.69778, 0.44192, 0.67344, 0.46313, 0.65577, 0.49196, 0.62738, 0.53273, 0.57419, 0.55694, 0.5349, 0.57006, 0.50861, 0.58026, 0.48453, 0.58688, 0.46681, 0.60417, 0.42046, 0.61147, 0.38548, 0.61896, 0.34955, 0.61974, 0.34309, 0.61939, 0.30558, 0.61029, 0.26662, 0.60414, 0.25464, 0.58374, 0.23368, 0.57563, 0.21645, 0.5755, 0.21624, 0.57423, 0.21019, 0.57429, 0.17903, 0.59097, 0.15252, 0.63402, 0.12315, 0.66331, 0.10798, 0.67372, 0.09403, 0.69324, 0.07819, 0.72436, 0.05294, 0.75993, 0.02407, 0.81814, 0.0405, 0.91601, 0.05275, 0.79744, 0.07208, 0.90848, 0.08561, 0.77579, 0.10494, 0.90378, 0.11912, 0.73156, 0.11783, 0.77767, 0.13909, 0.88119, 0.13265, 0.96119, 0.15134, 0.69679, 0.13148, 0.76136, 0.16192, 0.86914, 0.15233, 0.96477, 0.17944, 0.6656, 0.15789, 0.74893, 0.17841, 0.86222, 0.1754, 0.96163, 0.19893, 0.65498, 0.18591, 0.74123, 0.20043, 0.85818, 0.20043, 0.94882, 0.21944, 0.65168, 0.21945, 0.7372, 0.22645, 0.83441, 0.23847, 0.92419, 0.24697, 0.65295, 0.24697, 0.74431, 0.25398, 0.82983, 0.26299, 0.91827, 0.26799, 0.69223, 0.281, 0.81137, 0.29302, 0.91516, 0.29652, 0.6915, 0.32653, 0.81064, 0.33153, 0.69515, 0.36656, 0.8121, 0.37657, 0.69515, 0.40868, 0.81356, 0.41068, 0.68858, 0.44672, 0.8121, 0.44772, 0.682, 0.49426, 0.81576, 0.49826, 0.67834, 0.52929, 0.81576, 0.5393, 0.67472, 0.56957, 0.81673, 0.58959, 0.65551, 0.60961, 0.81005, 0.63077, 0.6388, 0.66223, 0.79752, 0.68453, 0.61457, 0.70465, 0.79016, 0.73644, 0.62288, 0.4772, 0.61432, 0.51706, 0.60394, 0.54873, 0.58545, 0.5894, 0.54989, 0.64113, 0.52342, 0.67523, 0.48827, 0.72981, 0.46215, 0.77378, 0.43821, 0.82222, 0.42188, 0.86395, 0.40774, 0.90568, 0.39576, 0.94815, 0.39032, 0.97349, 0.50673, 0.70115, 0.60799, 0.73354, 0.77016, 0.76558, 0.60123, 0.76688, 0.76221, 0.79611, 0.59513, 0.80863, 0.76343, 0.84287, 0.58903, 0.85205, 0.76221, 0.88128, 0.57833, 0.89464, 0.7576, 0.92136, 0.56369, 0.93722, 0.7515, 0.95393, 0.55516, 0.9723, 0.75028, 0.97814, 0.38198, 0.78191, 0.33198, 0.83452, 0.2832, 0.87376, 0.19051, 0.8746, 0.30027, 0.93055, 0.1473, 0.89547, 0.26315, 0.95476, 0.09242, 0.91468, 0.19974, 0.96645, 0.05705, 0.93806, 0.10705, 0.97647], "triangles": [45, 46, 44, 44, 178, 43, 44, 177, 178, 44, 46, 177, 43, 176, 42, 43, 178, 176, 42, 174, 41, 42, 176, 174, 177, 175, 178, 178, 175, 176, 175, 173, 176, 176, 173, 174, 173, 171, 174, 174, 171, 172, 46, 47, 177, 177, 47, 175, 173, 175, 49, 49, 175, 48, 175, 47, 48, 49, 50, 173, 50, 51, 173, 173, 51, 171, 51, 52, 171, 171, 170, 172, 172, 170, 150, 170, 169, 150, 150, 169, 149, 171, 52, 170, 52, 53, 170, 53, 54, 170, 170, 54, 169, 169, 54, 55, 149, 169, 148, 169, 55, 168, 169, 168, 148, 168, 55, 56, 148, 168, 147, 56, 57, 168, 168, 57, 147, 57, 58, 147, 147, 58, 146, 147, 146, 156, 146, 58, 59, 146, 153, 154, 59, 60, 146, 146, 60, 153, 153, 145, 138, 145, 144, 138, 138, 144, 136, 60, 61, 153, 153, 61, 145, 61, 62, 145, 145, 62, 144, 136, 144, 134, 134, 144, 143, 62, 63, 144, 144, 63, 143, 41, 152, 40, 41, 174, 152, 39, 40, 166, 39, 166, 38, 151, 166, 40, 37, 38, 167, 37, 167, 36, 167, 38, 166, 36, 34, 35, 36, 167, 34, 167, 165, 34, 34, 165, 33, 166, 164, 167, 167, 164, 165, 174, 172, 152, 40, 152, 151, 152, 172, 151, 166, 151, 164, 165, 163, 33, 33, 163, 32, 164, 162, 165, 165, 162, 163, 151, 150, 164, 151, 172, 150, 164, 150, 162, 163, 162, 161, 150, 149, 162, 31, 32, 161, 32, 163, 161, 162, 160, 161, 162, 149, 160, 31, 161, 159, 161, 160, 159, 149, 148, 160, 160, 158, 159, 160, 148, 158, 158, 157, 159, 148, 147, 158, 158, 156, 157, 158, 147, 156, 30, 31, 159, 30, 159, 29, 159, 157, 29, 29, 157, 28, 27, 28, 157, 27, 157, 155, 157, 156, 155, 155, 139, 27, 139, 26, 27, 156, 154, 155, 156, 146, 154, 139, 155, 138, 155, 154, 138, 138, 136, 139, 154, 153, 138, 139, 25, 26, 139, 137, 25, 137, 24, 25, 139, 136, 137, 137, 136, 135, 137, 135, 24, 135, 136, 134, 24, 135, 23, 23, 135, 22, 22, 135, 133, 134, 132, 135, 135, 132, 133, 22, 133, 21, 134, 143, 132, 132, 131, 133, 133, 131, 21, 143, 142, 132, 143, 63, 142, 63, 64, 142, 132, 130, 131, 132, 142, 130, 142, 64, 141, 142, 141, 130, 141, 64, 65, 131, 130, 129, 130, 128, 129, 131, 129, 20, 130, 141, 128, 65, 66, 141, 141, 140, 128, 21, 131, 20, 141, 66, 140, 129, 19, 20, 129, 128, 127, 128, 126, 127, 19, 127, 18, 19, 129, 127, 128, 140, 126, 66, 67, 140, 126, 140, 68, 140, 67, 68, 18, 127, 17, 127, 125, 17, 126, 124, 127, 127, 124, 125, 126, 68, 124, 68, 69, 124, 125, 16, 17, 69, 122, 124, 124, 123, 125, 125, 123, 16, 124, 122, 123, 69, 70, 122, 122, 121, 123, 123, 121, 16, 16, 121, 15, 70, 71, 122, 71, 120, 122, 122, 120, 121, 71, 72, 120, 121, 119, 15, 121, 118, 119, 121, 120, 118, 120, 117, 118, 120, 72, 117, 72, 73, 117, 118, 115, 119, 117, 114, 118, 118, 114, 115, 73, 113, 117, 117, 113, 114, 95, 92, 96, 99, 94, 95, 114, 110, 111, 119, 116, 14, 119, 115, 116, 14, 116, 13, 116, 112, 13, 13, 112, 12, 112, 116, 111, 116, 115, 111, 74, 75, 113, 112, 108, 12, 12, 108, 11, 75, 109, 113, 113, 109, 110, 111, 107, 112, 112, 107, 108, 110, 106, 111, 111, 106, 107, 75, 76, 109, 11, 108, 10, 10, 108, 104, 110, 109, 106, 109, 105, 106, 105, 109, 78, 108, 107, 104, 78, 109, 76, 76, 77, 78, 78, 79, 105, 10, 104, 9, 106, 102, 107, 107, 103, 104, 107, 102, 103, 106, 105, 102, 104, 103, 100, 104, 100, 9, 100, 103, 99, 79, 80, 105, 105, 101, 102, 105, 80, 101, 9, 100, 8, 100, 99, 96, 99, 95, 96, 100, 96, 8, 102, 98, 103, 101, 97, 102, 102, 97, 98, 103, 98, 99, 96, 7, 8, 98, 94, 99, 97, 93, 98, 98, 93, 94, 96, 6, 7, 80, 81, 101, 101, 81, 97, 81, 82, 97, 97, 82, 93, 115, 114, 111, 113, 110, 114, 73, 74, 113, 15, 119, 14, 96, 92, 6, 82, 83, 93, 93, 91, 94, 94, 91, 95, 95, 91, 92, 91, 89, 92, 92, 90, 6, 92, 89, 90, 90, 5, 6, 83, 84, 93, 93, 84, 91, 84, 85, 91, 91, 85, 89, 5, 88, 4, 5, 90, 88, 89, 87, 90, 90, 87, 88, 85, 86, 89, 89, 86, 87, 88, 3, 4, 3, 1, 2, 3, 88, 1, 88, 87, 1, 86, 0, 87, 87, 0, 1], "vertices": [2, 3, 91.64, 87.17, 1, 80, -737.43, -712.13, 0, 2, 3, 152.33, 10.79, 1, 80, -829.14, -678.86, 0, 2, 3, 170.27, -30.97, 0.9882, 4, 245.4, 208.1, 0.0118, 2, 3, 165.26, -38.31, 0.98462, 4, 249.01, 199.98, 0.01538, 2, 3, 144.06, -60.39, 0.9392, 4, 256.62, 170.33, 0.0608, 2, 3, 118.98, -86.52, 0.81184, 4, 265.63, 135.25, 0.18816, 2, 3, 87.74, -115.07, 0.5679, 4, 273.48, 93.66, 0.4321, 4, 3, 55.23, -144.8, 0.23194, 4, 281.64, 50.38, 0.76785, 2, 277.23, -191.2, 0.00014, 1, 261.13, 312.99, 6e-05, 4, 3, 47.32, -150.99, 0.16814, 4, 282.74, 40.38, 0.8306, 2, 267.18, -190.84, 0.00102, 1, 261.78, 302.96, 0.00024, 4, 3, 28.73, -158.84, 0.06457, 4, 279.64, 20.45, 0.93061, 2, 247.91, -184.88, 0.00404, 1, 257.78, 283.19, 0.00077, 5, 3, 7.9, -167.64, 0.00257, 4, 276.15, -1.89, 0.99589, 2, 226.31, -178.2, 0.0013, 1, 253.3, 261.02, 0.00024, 80, -799.6, -451.22, 0, 5, 3, -14.05, -176.91, 0.00522, 4, 272.48, -25.43, 0.96813, 2, 203.55, -171.15, 0.02247, 1, 248.57, 237.67, 0.00419, 80, -785.76, -431.82, 0, 5, 3, -36.85, -186.54, 0.00129, 4, 268.67, -49.89, 0.85108, 2, 179.9, -163.84, 0.1221, 1, 243.66, 213.41, 0.02553, 80, -771.38, -411.67, 0, 4, 4, 267.55, -71.94, 0.69832, 2, 158.25, -159.53, 0.24129, 1, 241.55, 191.44, 0.06039, 80, -760.49, -392.47, 0, 4, 4, 266.2, -98.48, 0.50104, 2, 132.18, -154.35, 0.37212, 1, 239, 164.98, 0.12684, 80, -747.38, -369.35, 0, 4, 4, 268.99, -144.78, 0.24901, 2, 85.97, -150.4, 0.43581, 1, 239.7, 118.61, 0.31518, 80, -729.1, -326.73, 0, 4, 4, 271.29, -183.03, 0.11314, 2, 47.79, -147.13, 0.32666, 1, 240.28, 80.29, 0.5602, 80, -714, -291.5, 0, 4, 4, 280.67, -226.85, 0.02489, 2, 3.07, -150.06, 0.09789, 1, 247.67, 36.1, 0.87722, 80, -702.73, -248.14, 0, 3, 1, 258.95, -11.14, 0.96536, 74, -27.35, 158.27, 0.01813, 75, -200.97, 120.92, 0.01652, 3, 1, 261.94, -23.67, 0.9177, 74, -14.87, 161.49, 0.04305, 75, -189.34, 126.47, 0.03925, 4, 1, 270.83, -69, 0.69141, 74, 30.28, 171.19, 0.14536, 75, -146.88, 144.66, 0.1627, 76, -270.52, 90.99, 0.00053, 7, 1, 273.12, -112.96, 0.46673, 74, 74.2, 174.28, 0.19567, 75, -104.37, 156.11, 0.32948, 76, -231.27, 110.94, 0.00805, 77, -323.52, 154.23, 8e-05, 79, -350.36, 146.79, 0, 80, -665.18, -101.66, 0, 8, 1, 276.09, -169.87, 0.23873, 74, 131.04, 178.28, 0.14581, 75, -49.35, 170.93, 0.56319, 76, -180.47, 136.76, 0.04771, 77, -269.8, 173.23, 0.00391, 78, -345.77, 263.28, 0.00064, 79, -311.95, 188.88, 0, 80, -644.68, -48.49, 0, 8, 1, 277.99, -206.38, 0.14416, 74, 167.51, 180.85, 0.08678, 75, -14.05, 180.44, 0.65222, 76, -147.88, 153.32, 0.10077, 77, -235.33, 185.43, 0.01292, 78, -309.39, 266.91, 0.00314, 79, -287.31, 215.89, 0, 80, -631.52, -14.38, 0, 8, 1, 277.12, -254.89, 0.065, 74, 216.03, 180.86, 0.02616, 75, 33.57, 189.76, 0.64363, 76, -103.19, 172.23, 0.21186, 77, -188.57, 198.38, 0.0407, 78, -260.89, 268.33, 0.01265, 79, -252.19, 249.37, 0, 80, -610.95, 29.56, 0, 8, 1, 282.03, -297.09, 0.02752, 74, 258.14, 186.53, 0.00407, 75, 73.81, 203.41, 0.53299, 76, -66.62, 193.85, 0.32048, 77, -149.5, 215.07, 0.08441, 78, -218.96, 275.23, 0.03053, 79, -225.61, 282.52, 1e-05, 80, -598.22, 70.1, 0, 8, 1, 286.26, -318.89, 0.0167, 74, 279.86, 191.15, 0.00084, 75, 94.23, 212.1, 0.46216, 76, -48.42, 206.56, 0.3637, 77, -129.8, 225.31, 0.11259, 78, -197.39, 280.47, 0.044, 79, -213.07, 300.84, 1e-05, 80, -593.19, 91.72, 0, 7, 1, 296.27, -370.56, 0.00417, 75, 142.65, 232.72, 0.31288, 76, -5.27, 236.69, 0.41441, 77, -83.11, 249.59, 0.18239, 78, -146.26, 292.91, 0.08614, 79, -183.34, 344.27, 2e-05, 80, -581.26, 142.98, 1e-05, 7, 1, 298.75, -383.34, 0.00279, 75, 154.63, 237.82, 0.28329, 76, 5.41, 244.15, 0.41725, 77, -71.55, 255.59, 0.19841, 78, -133.6, 295.99, 0.09823, 79, -175.99, 355.01, 2e-05, 80, -578.31, 155.66, 1e-05, 7, 1, 300.1, -407.15, 0.00112, 75, 177.63, 244.13, 0.23394, 76, 26.62, 255.05, 0.41562, 77, -49.11, 263.65, 0.22662, 78, -109.88, 298.46, 0.12266, 79, -159.99, 372.7, 2e-05, 80, -569.83, 177.95, 1e-05, 7, 1, 301.83, -437.82, 0.0002, 75, 207.26, 252.25, 0.18158, 76, 53.94, 269.09, 0.40192, 77, -20.19, 274.02, 0.2582, 78, -79.33, 301.64, 0.15805, 79, -139.39, 395.49, 3e-05, 80, -558.91, 206.66, 1e-05, 6, 75, 262.46, 254.45, 0.10478, 76, 107.51, 282.58, 0.35439, 77, 34.68, 280.45, 0.30352, 78, -24.51, 294.8, 0.23726, 79, -94.04, 427.03, 4e-05, 80, -528.1, 252.52, 1e-05, 6, 75, 289.9, 255.54, 0.07476, 76, 134.15, 289.29, 0.3247, 77, 61.96, 283.65, 0.31747, 78, 2.74, 291.4, 0.28301, 79, -71.49, 442.72, 5e-05, 80, -512.79, 275.32, 2e-05, 6, 75, 325.92, 261.9, 0.04825, 76, 168.08, 302.92, 0.2917, 77, 97.37, 292.76, 0.325, 78, 39.31, 291.8, 0.33498, 79, -44.56, 467.46, 5e-05, 80, -496.68, 308.15, 2e-05, 6, 75, 352.23, 266.55, 0.03773, 76, 192.88, 312.87, 0.27669, 77, 123.25, 299.41, 0.32629, 78, 66.03, 292.09, 0.35921, 79, -24.88, 485.53, 6e-05, 80, -484.9, 332.14, 2e-05, 6, 75, 372.5, 275.13, 0.03535, 76, 210.95, 325.44, 0.27338, 77, 142.8, 309.53, 0.32645, 78, 87.43, 297.25, 0.36474, 79, -12.41, 503.67, 6e-05, 80, -479.87, 353.57, 2e-05, 6, 75, 394.54, 197.55, 0.02348, 76, 248.48, 254.05, 0.23156, 77, 170.74, 233.87, 0.3193, 78, 96.52, 217.12, 0.42558, 79, 47.91, 450.13, 6e-05, 80, -404.23, 325.59, 2e-05, 6, 75, 416.21, 121.31, 0.00573, 76, 285.35, 183.89, 0.12836, 77, 198.2, 159.52, 0.25068, 78, 105.46, 138.36, 0.61514, 79, 107.19, 397.52, 7e-05, 80, -329.89, 298.09, 2e-05, 5, 76, 318.41, 120.99, 0.03493, 77, 222.81, 92.86, 0.08836, 78, 113.47, 67.76, 0.8766, 79, 160.33, 350.36, 9e-05, 80, -263.25, 273.44, 3e-05, 3, 78, 121.89, -6.47, 0.99536, 79, 216.2, 300.77, 0.00352, 80, -193.19, 247.52, 0.00112, 4, 76, 390.03, -15.29, 4e-05, 78, 130.82, -85.22, 0.774, 79, 275.48, 248.16, 0.15937, 80, -118.86, 220.02, 0.06659, 5, 76, 412.1, -57.29, 3e-05, 77, 292.58, -96.07, 0.00062, 78, 136.17, -132.36, 0.58216, 79, 310.97, 216.66, 0.25752, 80, -74.35, 203.56, 0.15968, 4, 76, 458.02, -144.66, 1e-05, 78, 147.29, -230.44, 0.23354, 79, 384.79, 151.15, 0.22528, 80, 18.22, 169.31, 0.54117, 4, 76, 497.86, -220.47, 0, 78, 156.95, -315.53, 0.06267, 79, 448.84, 94.3, 0.04265, 80, 98.54, 139.6, 0.89468, 4, 76, 527.11, -276.12, 0, 78, 164.03, -377.99, 0.0136, 79, 495.85, 52.57, 0.00094, 80, 157.5, 117.79, 0.98545, 3, 76, 543.6, -307.5, 0, 78, 168.03, -413.22, 0.00691, 80, 190.74, 105.49, 0.99309, 3, 76, 494.67, -333.25, 0, 78, 113.09, -419.48, 0.00374, 80, 171.59, 53.62, 0.99626, 3, 76, 443.87, -359.98, 0, 78, 56.06, -425.98, 0, 80, 151.7, -0.22, 1, 2, 78, -22.17, -434.89, 0, 80, 124.42, -74.08, 1, 3, 78, -38.54, -399.21, 0, 79, 359.34, -98.6, 0.01417, 80, 85.18, -72.62, 0.98583, 3, 78, -49.19, -376.01, 0, 79, 335.92, -88.45, 0.07279, 80, 59.68, -71.68, 0.92721, 3, 78, -57.13, -360.09, 0, 79, 319.37, -81.92, 0.16441, 80, 41.89, -71.6, 0.83559, 3, 78, -77.12, -321.26, 0, 79, 278.55, -66.39, 0.61236, 80, -1.78, -71.95, 0.38764, 4, 76, 254.87, -283.85, 0, 78, -92.49, -286.52, 0, 79, 243.9, -50.82, 0.93705, 80, -39.73, -70.03, 0.06295, 3, 76, 249.6, -278.56, 0, 79, 237.1, -47.75, 0.96533, 80, -47.18, -69.63, 0.03467, 3, 76, 231.14, -264.89, 0, 79, 215, -41.47, 0.99991, 80, -70.05, -71.81, 9e-05, 2, 79, 150.93, -23.29, 1, 80, -136.35, -78.12, 0, 2, 79, 110.22, -17.04, 1, 80, -176.55, -87.08, 0, 2, 79, 102.19, -17.04, 1, 80, -184.03, -90, 0, 2, 79, 79.02, -17.45, 1, 80, -205.47, -98.79, 0, 2, 79, 39.82, -18.13, 1, 80, -241.75, -113.66, 0, 4, 1, -103.82, -254.42, 0.00014, 74, 222.48, -200.03, 0.00049, 79, 15.17, -22.01, 0.99936, 80, -263.31, -126.22, 0, 6, 1, -84.35, -222.11, 0.01347, 74, 189.82, -181.15, 0.07048, 75, 77.26, -170.56, 0.05022, 76, 13.63, -171.41, 0.00094, 79, -21.51, -30.86, 0.8649, 80, -294.27, -147.78, 0, 5, 1, -57.73, -162.23, 0.09693, 74, 129.46, -155.62, 0.33767, 75, 13.13, -157.07, 0.12127, 79, -82.82, -53.99, 0.44413, 80, -343, -191.6, 0, 5, 1, -42.47, -118.27, 0.23451, 74, 85.24, -141.16, 0.47477, 75, -33.05, -151.36, 0.06621, 79, -124.82, -74.02, 0.22451, 80, -374.86, -225.51, 0, 5, 1, -34.55, -89.02, 0.38152, 74, 55.85, -133.77, 0.46216, 75, -63.31, -149.75, 0.02748, 79, -151.2, -88.94, 0.12884, 80, -394.02, -248.99, 0, 5, 1, -28.65, -62.31, 0.55857, 74, 29.03, -128.35, 0.36494, 75, -90.67, -149.57, 0.00763, 79, -174.35, -103.51, 0.06886, 80, -410.3, -270.98, 0, 5, 1, -24.97, -42.7, 0.70707, 74, 9.36, -125.04, 0.2534, 75, -110.61, -150.09, 0.00153, 79, -190.89, -114.68, 0.038, 80, -421.65, -287.39, 0, 2, 2, 2.08, 114.41, 0.03174, 1, -15.37, 8.6, 0.96826, 2, 2, 40.03, 107.61, 0.31778, 1, -12.41, 47.04, 0.68222, 3, 4, 21.62, -188.05, 0.00471, 2, 79, 100.63, 0.64955, 1, -9.37, 86.52, 0.34574, 3, 4, 21.42, -180.98, 0.00908, 2, 86.03, 99.8, 0.69573, 1, -9.25, 93.59, 0.2952, 3, 4, 16.66, -140.33, 0.1019, 2, 126.93, 98.62, 0.79742, 1, -12.17, 134.41, 0.10068, 3, 4, 5.24, -98.83, 0.46784, 2, 169.66, 103.9, 0.51116, 1, -21.71, 176.39, 0.021, 3, 4, -0.76, -86.34, 0.61672, 2, 182.88, 108.03, 0.37362, 1, -27.14, 189.13, 0.00966, 3, 4, -18.41, -65.29, 0.83656, 2, 206.26, 122.45, 0.16168, 1, -43.83, 210.95, 0.00176, 3, 4, -26.51, -47.27, 0.94917, 2, 225.27, 127.85, 0.0506, 1, -51.1, 229.32, 0.00023, 3, 4, -26.63, -47.06, 0.95001, 2, 225.5, 127.93, 0.04977, 1, -51.21, 229.54, 0.00023, 3, 4, -28.3, -40.6, 0.97637, 2, 232.13, 128.65, 0.02356, 1, -52.59, 236.07, 7e-05, 1, 4, -31.98, -6.81, 1, 1, 4, -22.77, 23.31, 1, 3, 3, -82.94, 94.24, 0.11631, 4, 5.67, 58.7, 0.88369, 80, -585.02, -626.69, 0, 3, 3, -56.42, 87.21, 0.33269, 4, 25.6, 77.55, 0.66731, 80, -611.29, -634.6, 0, 3, 3, -39.64, 90.48, 0.54986, 4, 31.66, 93.53, 0.45014, 80, -623.88, -646.17, 0, 3, 3, -17.05, 89.65, 0.74338, 4, 44.25, 112.31, 0.25662, 80, -643.55, -657.3, 0, 3, 3, 18.98, 88.33, 0.93819, 4, 64.33, 142.24, 0.06181, 80, -674.93, -675.05, 0, 3, 3, 60.16, 86.83, 0.99761, 4, 87.29, 176.47, 0.00239, 80, -710.79, -695.34, 0, 2, 3, 72.84, 41.54, 1, 80, -745.33, -663.41, 0, 2, 3, 107.4, -24.26, 0.96882, 4, 206.61, 158.17, 0.03118, 2, 3, 36.19, 32.48, 1, 80, -718.85, -636.49, 0, 2, 3, 75.71, -41.94, 0.87541, 4, 204.96, 121.92, 0.12459, 3, 3, -2.01, 23.1, 0.8784, 4, 108.75, 90.07, 0.1216, 80, -691.22, -608.5, 0, 2, 3, 44.76, -61.71, 0.57212, 4, 205.48, 85.2, 0.42788, 3, 3, -33.45, 40.45, 0.51555, 4, 77.45, 72.47, 0.48445, 80, -655.36, -606.8, 0, 3, 3, -30.48, -0.97, 0.47386, 4, 114.24, 53.19, 0.52614, 80, -679.59, -573.07, 0, 2, 3, 22.73, -57.52, 0.44786, 4, 190.33, 68.67, 0.55214, 3, 3, 43.52, -117.14, 0.21401, 4, 251.96, 54.97, 0.78599, 1, 231.69, 318.92, 0, 3, 3, -61.18, 51.72, 0.21204, 4, 53.28, 54.82, 0.78796, 80, -625.83, -601.87, 0, 3, 3, -57.6, -6.73, 0.08987, 4, 104.86, 27.1, 0.91013, 80, -659.51, -553.96, 0, 2, 3, 0.28, -63.67, 0.24678, 4, 183.74, 46.34, 0.75322, 4, 3, 21.03, -138.15, 0.03656, 4, 257.99, 24.79, 0.96163, 2, 255.34, -164.09, 0.00151, 1, 236.36, 288.5, 0.0003, 1, 4, 33.28, 23.62, 1, 1, 4, 97.61, 8.2, 1, 1, 4, 181.37, 20.75, 1, 1, 4, 257.99, 3.4, 1, 1, 4, 28.76, -7.65, 1, 1, 4, 94.53, -16.31, 1, 1, 4, 181.36, -6.71, 1, 1, 4, 250.94, -19.9, 1, 3, 4, 30.32, -44.28, 0.88433, 2, 219.99, 71.18, 0.11511, 1, 5.8, 229.75, 0.00056, 2, 4, 94.66, -44.86, 0.73251, 2, 210.09, 7.61, 0.26749, 4, 4, 168.27, -49.91, 0.8024, 2, 194.43, -64.5, 0.1928, 1, 143.36, 217.91, 0.00481, 80, -681.64, -456.69, 0, 4, 4, 235.96, -51.77, 0.88392, 2, 182.78, -131.19, 0.09877, 1, 210.89, 213, 0.01732, 80, -741.3, -424.67, 0, 3, 4, 34.56, -74.03, 0.61345, 2, 189.95, 71.3, 0.38062, 1, 8.7, 199.84, 0.00594, 2, 4, 103.24, -74.13, 0.31692, 2, 179.89, 3.36, 0.68308, 4, 4, 167.81, -76.88, 0.5662, 2, 167.81, -60.13, 0.42068, 1, 141.69, 190.99, 0.01312, 80, -669.13, -432.79, 0, 4, 4, 234.08, -75.05, 0.7301, 2, 160.02, -125.96, 0.22364, 1, 207.97, 189.83, 0.04626, 80, -729.18, -404.7, 0, 3, 4, 67.8, -107.71, 0.22724, 2, 151.8, 43.29, 0.75438, 1, 40.39, 164.7, 0.01838, 4, 4, 157.7, -110.96, 0.29829, 2, 135.56, -45.19, 0.68131, 1, 130.05, 157.4, 0.0204, 80, -644.81, -406.87, 0, 4, 4, 235.19, -106.24, 0.48028, 2, 129, -122.54, 0.39922, 1, 207.67, 158.63, 0.1205, 80, -716.18, -376.33, 0, 3, 4, 72.72, -157.13, 0.00543, 2, 102.19, 45.59, 0.89779, 1, 43.07, 115.11, 0.09678, 4, 4, 161.78, -152.78, 0.07059, 2, 93.59, -43.16, 0.87037, 1, 132.24, 115.44, 0.05903, 80, -629.69, -367.66, 0, 2, 2, 58.44, 44.41, 0.75395, 1, 48.63, 71.7, 0.24605, 4, 4, 168.26, -201.51, 0.02078, 2, 44.44, -42.51, 0.79217, 1, 136.52, 66.47, 0.18706, 80, -613.63, -321.2, 0, 2, 2, 12.52, 46.03, 0.35037, 1, 51.62, 25.84, 0.64963, 4, 4, 173.43, -238.38, 0.00482, 2, 7.21, -42.29, 0.44839, 1, 140.03, 29.41, 0.54679, 80, -601.72, -285.93, 0, 4, 1, 49.41, -15.89, 0.85227, 74, -18.8, -51.15, 0.14584, 79, -262.23, -80.6, 0.00188, 80, -500.51, -281.53, 0, 3, 1, 141.56, -10.98, 0.8985, 74, -25.38, 40.89, 0.10039, 75, -176.52, 6.1, 0.0011, 4, 1, 47.88, -67.97, 0.32785, 74, 33.3, -51.74, 0.64859, 79, -224.1, -45.09, 0.02355, 80, -477.87, -234.6, 0, 3, 1, 147.87, -65.83, 0.31736, 74, 29.35, 48.2, 0.64344, 75, -124.21, 23.76, 0.0392, 5, 1, 47.63, -106.28, 0.14586, 74, 71.61, -51.29, 0.78966, 75, -63.66, -65.77, 0.0121, 79, -196.67, -18.34, 0.05239, 80, -462.02, -199.72, 0, 4, 1, 150.78, -110.51, 0.15336, 74, 73.97, 51.91, 0.69452, 75, -81.13, 35.97, 0.15212, 80, -554.47, -153.79, 0, 5, 1, 47.79, -150.31, 0.05202, 74, 115.63, -50.33, 0.72632, 75, -20.64, -56.39, 0.12962, 79, -165.45, 12.71, 0.09203, 80, -444.21, -159.45, 0, 7, 1, 155.06, -165.21, 0.07583, 74, 128.58, 57.19, 0.37878, 75, -28.54, 51.62, 0.53999, 76, -135.58, 24.27, 0.00532, 77, -239.89, 55.87, 8e-05, 79, -230.22, 99.51, 0, 80, -536.08, -102.1, 0, 6, 1, 36.31, -194.83, 0.02244, 74, 160.35, -61.01, 0.31068, 75, 25.3, -58.29, 0.48352, 76, -60.3, -72.23, 0.01436, 79, -125.7, 35.82, 0.16899, 80, -415.56, -123.49, 0, 8, 1, 153, -210.37, 0.03747, 74, 173.77, 55.95, 0.0644, 75, 16.04, 59.07, 0.86358, 76, -93.48, 40.72, 0.03148, 77, -196, 66.72, 0.00258, 78, -299.5, 142.25, 0.0005, 79, -196.64, 129.77, 0, 80, -515.77, -61.71, 0, 6, 1, 27.58, -252.93, 0.00261, 74, 218.6, -68.68, 0.05117, 75, 83.94, -54.65, 0.44368, 76, -3.66, -56.61, 0.28079, 79, -78.23, 70.44, 0.22175, 80, -383.9, -74, 0, 7, 1, 147.47, -269.51, 0.01139, 75, 75.03, 66.05, 0.72618, 76, -37.19, 59.68, 0.23838, 77, -137.73, 78.22, 0.0194, 78, -240.17, 139.51, 0.00465, 79, -150.68, 167.39, 0, 80, -486.61, -9.96, 0, 6, 1, 12.53, -300.29, 5e-05, 74, 266.22, -82.87, 0.00533, 75, 133.4, -59.45, 0.05385, 76, 45.73, -51.14, 0.68704, 77, -69.89, -42.43, 0.01045, 79, -33.96, 93.01, 0.24327, 7, 1, 145.67, -326.38, 0.00282, 75, 131.02, 76.2, 0.31019, 76, 15.52, 81.13, 0.58437, 77, -82.68, 92.64, 0.08434, 78, -183.27, 140.39, 0.01828, 79, -108.95, 206.07, 0, 80, -461.76, 41.23, 0, 5, 1, 2.6, -52.26, 0.63368, 74, 18.42, -97.29, 0.32624, 75, -107.04, -121.12, 0.00146, 79, -203.46, -88.34, 0.03862, 80, -442.93, -267.41, 0, 5, 1, -0.96, -96.08, 0.33769, 74, 62.29, -100.06, 0.52744, 75, -63.45, -115.42, 0.0253, 79, -169.78, -60.08, 0.10957, 80, -421.81, -228.85, 0, 5, 1, -6.45, -131.06, 0.18617, 74, 97.37, -104.91, 0.53201, 75, -28.1, -113.46, 0.08135, 79, -141.03, -39.41, 0.20047, 80, -402.53, -199.15, 0, 6, 1, -17.35, -176.23, 0.0773, 74, 142.72, -114.99, 0.35712, 75, 18.35, -114.66, 0.18067, 76, -55.51, -128.82, 0.00785, 79, -101.23, -15.43, 0.37706, 80, -374.16, -162.36, 0, 7, 1, -40.19, -234.27, 0.0144, 74, 201.17, -136.78, 0.09292, 75, 79.89, -124.83, 0.12281, 76, 6.81, -126.12, 0.07348, 77, -118.22, -111.73, 0.00025, 79, -43.89, 9.11, 0.69614, 80, -329.63, -118.67, 0, 6, 1, -57.51, -272.68, 0.00048, 74, 239.89, -153.4, 0.00722, 75, 121.08, -133.72, 0.02497, 76, 48.94, -126.35, 0.10256, 77, -76.47, -117.42, 0.01265, 79, -4.39, 23.77, 0.85211, 3, 76, 113.87, -121.93, 0.09838, 77, -11.51, -121.47, 0.1485, 79, 54.8, 50.84, 0.75312, 4, 76, 165.41, -116.93, 0.02829, 77, 40.24, -123.19, 0.30593, 78, -115.36, -98.53, 0.01336, 79, 101.25, 73.73, 0.65242, 5, 76, 220.52, -108.22, 0.00067, 77, 96.01, -121.71, 0.34388, 78, -60.85, -110.38, 0.10428, 79, 149.72, 101.35, 0.55098, 80, -182.72, 37.57, 0.00019, 4, 77, 142.95, -117.41, 0.24404, 78, -14.24, -117.4, 0.27706, 79, 189.09, 127.27, 0.46687, 80, -155.46, 76.01, 0.01203, 5, 76, 311.73, -86.08, 1e-05, 77, 189.32, -111.59, 0.10913, 78, 32.19, -122.81, 0.46365, 79, 227.23, 154.28, 0.37587, 80, -129.72, 115.03, 0.05134, 5, 76, 356.92, -72.47, 2e-05, 77, 235.9, -103.96, 0.02707, 78, 79.24, -126.51, 0.5751, 79, 264.7, 182.97, 0.29117, 80, -105.23, 155.37, 0.10664, 5, 76, 383.29, -63.23, 3e-05, 77, 263.24, -98.22, 0.00694, 78, 107.16, -127.45, 0.6009, 79, 286.11, 200.93, 0.2593, 80, -91.8, 179.87, 0.13283, 4, 75, 151.69, -138, 0.00221, 76, 79.78, -124.25, 0.12374, 77, -45.62, -119.34, 0.05588, 79, 23.72, 36.63, 0.81817, 4, 75, 165.07, -55.59, 0.00206, 76, 75.93, -40.85, 0.75424, 77, -38.61, -36.15, 0.06465, 79, -9.34, 113.3, 0.17905, 7, 1, 132.82, -359.08, 0.00045, 75, 165.68, 70.49, 0.14517, 76, 50.61, 82.66, 0.65744, 77, -47.69, 89.61, 0.16732, 78, -150.01, 129.1, 0.02961, 79, -76.66, 219.9, 0, 80, -436.7, 65.84, 0, 3, 76, 110.49, -28.43, 0.46192, 77, -2.73, -28.31, 0.4314, 79, 18.6, 137.13, 0.10669, 7, 1, 129.06, -392.7, 1e-05, 75, 199.34, 73.86, 0.08136, 76, 82.86, 92.87, 0.55463, 77, -14.39, 95.55, 0.30847, 78, -116.25, 126.93, 0.05551, 79, -50.09, 240.85, 1e-05, 80, -419.56, 95, 0, 2, 77, 41.58, -16.84, 0.96162, 79, 52.27, 168.15, 0.03838, 6, 75, 248.17, 88.64, 0.04787, 76, 127.62, 117.38, 0.35107, 77, 33.17, 114.04, 0.45586, 78, -65.66, 133.55, 0.14518, 79, -16.88, 279.58, 2e-05, 80, -402.67, 143.15, 1e-05, 2, 77, 87.61, -4.74, 0.99471, 79, 87.14, 200.53, 0.00529, 6, 75, 288.74, 99.19, 0.02977, 76, 165.15, 136.04, 0.24512, 77, 72.81, 127.67, 0.45417, 78, -23.91, 137.34, 0.27091, 79, 11.64, 310.3, 3e-05, 80, -387.25, 182.13, 1e-05, 3, 76, 241.87, 21.17, 0.00036, 77, 133.97, 3.82, 0.27735, 78, 5.95, 2.47, 0.72229, 6, 75, 331.75, 107.8, 0.01619, 76, 205.47, 153.31, 0.17938, 77, 115.03, 139.56, 0.37466, 78, 19.93, 138.81, 0.42971, 79, 43.26, 340.7, 4e-05, 80, -368.82, 221.93, 1e-05, 3, 78, 53.34, -3.19, 0.99815, 79, 163.03, 257.4, 0.00166, 80, -226.99, 187.8, 0.0002, 6, 75, 367.18, 113.1, 0.00929, 76, 239.05, 165.78, 0.14723, 77, 149.94, 147.57, 0.30144, 78, 55.74, 138.26, 0.54197, 79, 70.27, 364.22, 6e-05, 80, -352.2, 253.66, 2e-05, 3, 78, 92.08, -5.24, 0.99694, 79, 193.21, 281.76, 0.00242, 80, -207.71, 221.46, 0.00064, 6, 75, 392.84, 119.43, 0.00702, 76, 262.87, 177.24, 0.13737, 77, 175.05, 155.85, 0.26782, 78, 82.1, 140.32, 0.5877, 79, 88.51, 383.37, 7e-05, 80, -342.16, 278.11, 2e-05, 4, 76, 201.09, -165.84, 0.00033, 77, 69.27, -176.32, 0.09876, 78, -99.84, -157.05, 0.02374, 79, 151.9, 40.58, 0.87716, 5, 76, 269.27, -172.26, 0, 77, 136.04, -191.53, 0.06849, 78, -38.62, -187.74, 0.08978, 79, 217.95, 58.66, 0.83577, 80, -103.65, 22.56, 0.00596, 5, 76, 324.11, -184.64, 0, 77, 188.81, -210.92, 0.02415, 78, 8, -219.15, 0.12362, 79, 273.63, 66.45, 0.73076, 80, -54.6, 50.04, 0.12147, 5, 76, 357.08, -245.52, 0, 77, 213.6, -275.57, 0.00058, 78, 16.66, -287.85, 0.02518, 79, 325.98, 21.13, 0.23451, 80, 10.63, 26.82, 0.73972, 5, 76, 373.05, -144.56, 1e-05, 77, 242.53, -177.54, 0.0155, 78, 68.13, -199.54, 0.28459, 79, 305.26, 121.22, 0.4171, 80, -45.02, 112.56, 0.2828, 4, 76, 392.25, -263.53, 0, 78, 42.91, -317.38, 0.02193, 79, 365.24, 16.71, 0.01863, 80, 48.82, 36.96, 0.95944, 5, 76, 409.32, -156.84, 1e-05, 77, 276.91, -194.42, 0.00276, 78, 97.49, -224.14, 0.2414, 79, 343.53, 122.55, 0.29716, 80, -9.84, 127.69, 0.45867, 3, 76, 429.85, -290.1, 0, 78, 68.32, -355.77, 0.01188, 80, 94.54, 42.35, 0.98812, 5, 76, 442.62, -192.87, 1e-05, 77, 305.25, -234.46, 2e-05, 78, 115.47, -269.78, 0.13569, 79, 387.41, 100.61, 0.15294, 80, 39.01, 123.18, 0.71135, 3, 76, 464.71, -301.65, 0, 78, 96.62, -379.17, 0.00917, 80, 128.18, 57.08, 0.99083, 4, 76, 484.47, -249.1, 0, 78, 134.09, -337.36, 0.03937, 79, 446.42, 62.78, 0.01869, 80, 107.73, 109.36, 0.94193], "hull": 87, "edges": [0, 172, 0, 2, 2, 4, 4, 6, 14, 16, 38, 40, 46, 48, 48, 50, 68, 70, 100, 102, 102, 104, 106, 108, 112, 114, 114, 116, 120, 122, 122, 124, 124, 126, 126, 128, 130, 132, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 152, 154, 32, 34, 104, 106, 128, 130, 172, 174, 174, 176, 6, 8, 8, 10, 176, 8, 170, 172, 170, 178, 178, 180, 180, 10, 166, 168, 168, 170, 168, 182, 182, 184, 10, 12, 12, 14, 184, 12, 164, 186, 186, 188, 188, 190, 190, 192, 192, 14, 162, 194, 194, 196, 196, 198, 198, 200, 16, 18, 200, 18, 160, 202, 202, 204, 204, 206, 206, 208, 18, 20, 208, 20, 158, 210, 210, 212, 212, 214, 214, 216, 20, 22, 22, 24, 216, 22, 156, 218, 218, 220, 220, 222, 222, 224, 224, 24, 150, 226, 226, 228, 228, 230, 230, 232, 24, 26, 26, 28, 232, 26, 146, 234, 234, 236, 236, 238, 238, 28, 144, 240, 240, 242, 28, 30, 30, 32, 242, 30, 140, 244, 244, 246, 246, 32, 136, 138, 138, 140, 138, 248, 248, 250, 250, 34, 136, 252, 252, 254, 34, 36, 36, 38, 254, 36, 132, 134, 134, 136, 256, 258, 258, 40, 260, 262, 40, 42, 262, 42, 264, 266, 42, 44, 44, 46, 266, 44, 268, 270, 270, 46, 272, 274, 274, 48, 276, 278, 50, 52, 278, 52, 134, 280, 280, 256, 130, 282, 282, 260, 280, 282, 128, 284, 284, 264, 282, 284, 126, 286, 286, 268, 284, 286, 124, 288, 288, 272, 286, 288, 122, 290, 290, 276, 288, 290, 292, 294, 294, 296, 296, 298, 298, 300, 300, 302, 302, 304, 304, 82, 290, 306, 306, 292, 120, 306, 306, 308, 308, 310, 52, 54, 54, 56, 310, 54, 116, 118, 118, 120, 118, 292, 292, 312, 312, 314, 56, 58, 58, 60, 314, 58, 114, 294, 294, 316, 316, 318, 318, 60, 296, 320, 320, 322, 60, 62, 62, 64, 322, 62, 298, 324, 324, 326, 326, 64, 300, 328, 328, 330, 64, 66, 66, 68, 330, 66, 302, 332, 332, 334, 334, 68, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 112, 336, 336, 296, 108, 110, 110, 112, 110, 338, 338, 298, 104, 340, 340, 300, 102, 342, 342, 344, 344, 302, 96, 98, 98, 100, 98, 346, 346, 348, 348, 304, 96, 350, 350, 352, 82, 84, 352, 84, 94, 96, 94, 354, 354, 356, 84, 86, 356, 86, 90, 92, 92, 94, 86, 88, 88, 90, 92, 88], "width": 582, "height": 850}}, "t6": {"t6": {"type": "mesh", "uvs": [0.57582, 0, 1, 0.22, 1, 0.24382, 0.82705, 0.336, 0.76829, 0.34444, 0.7896, 0.4138, 0.80889, 0.4766, 0.82423, 0.52653, 0.84392, 0.59064, 0.85838, 0.63768, 0.87079, 0.67808, 0.79241, 0.71147, 0.70497, 0.71886, 0.62672, 0.69537, 0.56864, 0.65346, 0.5745, 0.6164, 0.58035, 0.57937, 0.58751, 0.53411, 0.59643, 0.47769, 0.6044, 0.42734, 0.61159, 0.38183, 0.49902, 0.39479, 0.5112, 0.45479, 0.52624, 0.52881, 0.54074, 0.60023, 0.55218, 0.65659, 0.56212, 0.72476, 0.57375, 0.80462, 0.53611, 0.86295, 0.50502, 0.91112, 0.47565, 0.95664, 0.27481, 0.9639, 0.24299, 0.89908, 0.2174, 0.84694, 0.18186, 0.77455, 0.19214, 0.7362, 0.20772, 0.67806, 0.22263, 0.62242, 0.24448, 0.54092, 0.26536, 0.463, 0.28206, 0.4007, 0.20974, 0.3634, 0, 0.27649, 0, 0.27098, 0.2797, 0.13174, 0.44636, 0], "triangles": [12, 13, 11, 10, 11, 9, 13, 14, 9, 9, 14, 15, 15, 8, 9, 11, 13, 9, 8, 15, 16, 7, 8, 16, 16, 17, 7, 17, 18, 7, 18, 6, 7, 18, 19, 6, 19, 5, 6, 19, 20, 5, 20, 4, 5, 4, 20, 44, 3, 4, 1, 31, 32, 30, 30, 32, 29, 29, 32, 28, 32, 33, 28, 28, 33, 27, 33, 34, 27, 34, 26, 27, 26, 34, 35, 26, 36, 25, 25, 36, 37, 35, 36, 26, 37, 24, 25, 37, 38, 24, 38, 23, 24, 38, 39, 23, 39, 22, 23, 39, 40, 22, 40, 21, 22, 40, 41, 21, 44, 21, 41, 41, 42, 43, 20, 21, 44, 1, 4, 44, 44, 45, 0, 44, 41, 43, 2, 3, 1, 0, 1, 44], "vertices": [1, 40, -10.58, 5.98, 1, 2, 40, 24.64, 30.32, 0.68139, 46, -22.56, 18.02, 0.31861, 2, 40, 28.25, 30.06, 0.66327, 46, -18.94, 17.94, 0.33673, 2, 40, 41.47, 18.22, 0.38248, 46, -5.16, 6.75, 0.61752, 2, 40, 42.49, 14.44, 0.23546, 46, -3.96, 3.02, 0.76454, 1, 46, 6.61, 4.14, 1, 2, 46, 16.18, 5.15, 0.15705, 47, 2.46, 5.15, 0.84295, 2, 47, 10.06, 5.96, 0.88431, 48, -4.52, 5.34, 0.11569, 2, 47, 19.83, 6.99, 0.01784, 48, 4.99, 7.83, 0.98216, 1, 48, 11.96, 9.66, 1, 1, 48, 17.95, 11.23, 1, 1, 48, 23.62, 6.99, 1, 1, 48, 25.44, 1.67, 1, 1, 48, 22.54, -3.68, 1, 1, 48, 16.7, -8.13, 1, 2, 47, 23.39, -10.06, 0.00382, 48, 11.06, -8.5, 0.99618, 2, 47, 17.77, -9.58, 0.12664, 48, 5.43, -8.86, 0.87336, 3, 46, 24.63, -8.98, 0.00314, 47, 10.9, -8.98, 0.66587, 48, -1.45, -9.3, 0.33099, 3, 46, 16.06, -8.23, 0.28772, 47, 2.34, -8.23, 0.70705, 48, -10.02, -9.85, 0.00523, 4, 40, 54.34, 3.26, 0.01008, 41, 6.9, 12.17, 0.00863, 46, 8.42, -7.57, 0.90321, 47, -5.3, -7.57, 0.07808, 3, 40, 47.47, 4.19, 0.2075, 41, -0.01, 12.62, 0.11618, 46, 1.52, -6.97, 0.67632, 3, 40, 48.94, -3.02, 0.12282, 41, 1.96, 5.53, 0.80563, 46, 3.33, -14.1, 0.07155, 1, 41, 11.08, 6.3, 1, 2, 41, 22.33, 7.25, 0.48685, 42, -0.16, 7.24, 0.51315, 2, 42, 10.66, 8.5, 0.96511, 43, -8.09, 8.18, 0.03489, 3, 42, 19.2, 9.5, 0.39797, 43, 0.4, 9.52, 0.5868, 44, -16.55, 9.51, 0.01524, 3, 42, 29.54, 10.46, 0.00315, 43, 10.69, 10.9, 0.68162, 44, -6.26, 10.85, 0.31523, 3, 43, 22.74, 12.51, 0.0342, 44, 5.8, 12.43, 0.88815, 45, -6.87, 12.38, 0.07765, 2, 44, 14.81, 10.68, 0.42529, 45, 2.15, 10.7, 0.57471, 2, 44, 22.25, 9.24, 0.03852, 45, 9.6, 9.31, 0.96148, 1, 45, 16.64, 7.99, 1, 1, 45, 18.71, -4.54, 1, 2, 44, 21.57, -7.36, 0.01227, 45, 9.04, -7.29, 0.98773, 3, 43, 30.79, -9.41, 0.00454, 44, 13.78, -9.52, 0.45704, 45, 1.26, -9.51, 0.53842, 3, 43, 19.97, -12.44, 0.35777, 44, 2.96, -12.52, 0.63118, 45, -9.54, -12.59, 0.01104, 3, 42, 32.02, -12.78, 0.00507, 43, 14.11, -12.22, 0.723, 44, -2.9, -12.28, 0.27194, 3, 42, 23.16, -12.09, 0.17445, 43, 5.23, -11.88, 0.82003, 44, -11.78, -11.91, 0.00552, 2, 42, 14.67, -11.42, 0.70296, 43, -3.28, -11.56, 0.29704, 3, 41, 24.17, -10.51, 0.30576, 42, 2.25, -10.44, 0.69393, 43, -15.73, -11.09, 0.00031, 2, 41, 12.32, -9.19, 0.98058, 42, -9.63, -9.5, 0.01942, 2, 40, 48.89, -16.72, 0.03345, 41, 2.85, -8.14, 0.96655, 2, 40, 42.92, -20.87, 0.2531, 41, -2.81, -12.69, 0.7469, 2, 40, 28.82, -33.13, 0.56274, 41, -16.02, -25.91, 0.43726, 2, 40, 27.98, -33.07, 0.56329, 41, -16.86, -25.91, 0.43671, 2, 40, 8.1, -14.02, 0.95091, 41, -38.03, -8.29, 0.04909, 1, 40, -11.15, -2.15, 1], "hull": 46, "edges": [0, 90, 0, 2, 2, 4, 4, 6, 6, 8, 60, 62, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 42, 40, 28, 26, 26, 24, 20, 22, 24, 22, 78, 80, 42, 44, 76, 78, 44, 46, 74, 76, 46, 48, 48, 50, 72, 74, 68, 70, 70, 72, 50, 52, 52, 54, 66, 68, 62, 64, 64, 66, 54, 56, 56, 58, 58, 60, 38, 40, 8, 10, 36, 38, 10, 12, 34, 36, 12, 14, 32, 34, 14, 16, 28, 30, 30, 32, 16, 18, 18, 20], "width": 49, "height": 118}}, "st1": {"st1": {"type": "mesh", "uvs": [0.25188, 0.01987, 0.3183, 0.05178, 0.39449, 0.0712, 0.50682, 0.09323, 0.57322, 0.10625, 0.63944, 0.13067, 0.62707, 0.15242, 0.61095, 0.18078, 0.59942, 0.20105, 0.55426, 0.23773, 0.52174, 0.26413, 0.49137, 0.28879, 0.47354, 0.30327, 0.4577, 0.31017, 0.4289, 0.3227, 0.42182, 0.3413, 0.41283, 0.36492, 0.40492, 0.38572, 0.40114, 0.39566, 0.37827, 0.40801, 0.33934, 0.42905, 0.38619, 0.45417, 0.41416, 0.47342, 0.44765, 0.49645, 0.4882, 0.52436, 0.49687, 0.54503, 0.50738, 0.57007, 0.5181, 0.59564, 0.52584, 0.61409, 0.53279, 0.63064, 0.54196, 0.65249, 0.552, 0.67643, 0.55613, 0.68166, 0.56741, 0.69821, 0.58587, 0.72531, 0.60523, 0.75373, 0.62258, 0.77919, 0.63851, 0.80256, 0.65689, 0.82954, 0.68457, 0.85091, 0.69672, 0.86028, 0.73145, 0.8773, 0.7833, 0.90271, 0.80451, 0.9131, 0.89637, 0.93104, 0.94742, 0.92528, 0.99756, 0.93859, 0.99778, 0.96481, 0.99807, 0.99913, 0.88493, 0.99915, 0.74493, 0.99916, 0.59706, 0.99918, 0.45707, 0.99919, 0.28355, 0.99921, 0.12871, 0.99923, 0.08244, 0.99924, 0.00996, 0.99924, 0, 1, 0, 0.99239, 0.01227, 0.95946, 0.02271, 0.9314, 0.03497, 0.89851, 0.05232, 0.87032, 0.06855, 0.84395, 0.08093, 0.82383, 0.09218, 0.80556, 0.10832, 0.77933, 0.12255, 0.7562, 0.14576, 0.7185, 0.15515, 0.68773, 0.16237, 0.66405, 0.16224, 0.63996, 0.16212, 0.61683, 0.162, 0.59258, 0.16189, 0.57103, 0.15398, 0.5535, 0.14696, 0.53469, 0.13684, 0.50756, 0.12835, 0.48481, 0.11862, 0.45873, 0.1086, 0.43188, 0.09946, 0.40736, 0.09974, 0.38505, 0.10005, 0.36081, 0.1004, 0.33375, 0.10067, 0.31343, 0.10087, 0.29728, 0.12803, 0.2814, 0.15857, 0.26355, 0.17937, 0.25138, 0.20202, 0.22892, 0.22096, 0.21014, 0.22463, 0.19089, 0.22824, 0.17195, 0.2299, 0.15209, 0.23119, 0.13662, 0.22958, 0.12393, 0.22796, 0.11109, 0.2251, 0.09299, 0.22164, 0.07757, 0.21876, 0.0647, 0.21116, 0.04579, 0.20492, 0.0303, 0.19881, 0.01511, 0.19273, 0, 0.21967, 0.00439, 0.25524, 0.05958, 0.26762, 0.10444, 0.27312, 0.15969, 0.26075, 0.20974, 0.21399, 0.25155, 0.15622, 0.28508, 0.12596, 0.3441, 0.12459, 0.40286, 0.15622, 0.47274, 0.17548, 0.54695, 0.19336, 0.63566, 0.18098, 0.71054, 0.1686, 0.78387, 0.1521, 0.86367, 0.14109, 0.93189, 0.26142, 0.08195, 0.329, 0.08111, 0.35129, 0.10407, 0.45429, 0.10038, 0.27069, 0.13521, 0.35898, 0.13463, 0.48888, 0.1283, 0.36582, 0.16045, 0.50495, 0.15781, 0.26668, 0.18574, 0.36608, 0.18645, 0.51212, 0.18276, 0.36223, 0.21469, 0.50136, 0.21337, 0.23545, 0.23236, 0.35608, 0.2392, 0.49136, 0.23893, 0.3507, 0.25978, 0.47445, 0.26374, 0.18318, 0.26943, 0.32688, 0.28293, 0.44525, 0.28847, 0.30074, 0.30509, 0.41988, 0.31116, 0.13733, 0.32193, 0.27384, 0.33729, 0.26462, 0.35649, 0.12532, 0.37154, 0.26077, 0.38103, 0.25309, 0.40856, 0.13697, 0.43022, 0.25054, 0.43343, 0.14848, 0.45563, 0.25515, 0.45587, 0.26591, 0.47867, 0.1628, 0.49809, 0.27215, 0.50553, 0.17064, 0.52831, 0.27807, 0.52876, 0.28314, 0.55083, 0.18113, 0.575, 0.28906, 0.57754, 0.18629, 0.60059, 0.30091, 0.6028, 0.19083, 0.62315, 0.31275, 0.62691, 0.32036, 0.64404, 0.18984, 0.65691, 0.3322, 0.66597, 0.18588, 0.6809, 0.34742, 0.68659, 0.34996, 0.70924, 0.17661, 0.73643, 0.35842, 0.73643, 0.17235, 0.76167, 0.36434, 0.7661, 0.55971, 0.75884, 0.36772, 0.79146, 0.58085, 0.78507, 0.16255, 0.8131, 0.37424, 0.81412, 0.59311, 0.80702, 0.15688, 0.84055, 0.37911, 0.84251, 0.61864, 0.8329, 0.38883, 0.86505, 0.64175, 0.85628, 0.14717, 0.89419, 0.40342, 0.89511, 0.68187, 0.88342, 0.41194, 0.92642, 0.73416, 0.91139, 0.1352, 0.96395, 0.43735, 0.95939, 0.66804, 0.95736, 0.81986, 0.93976, 0.13166, 0.98321, 0.45904, 0.98511, 0.72325, 0.98173, 0.90859, 0.95804, 0.8731, 0.98715], "triangles": [56, 58, 55, 54, 55, 197, 54, 197, 53, 59, 197, 55, 52, 53, 198, 52, 198, 51, 198, 53, 197, 51, 199, 50, 51, 198, 199, 50, 201, 49, 50, 199, 201, 49, 201, 48, 201, 47, 48, 201, 200, 47, 201, 199, 200, 198, 195, 199, 198, 197, 194, 197, 193, 194, 198, 194, 195, 199, 196, 200, 199, 195, 196, 200, 46, 47, 193, 191, 194, 194, 191, 195, 196, 44, 200, 200, 45, 46, 200, 44, 45, 196, 192, 43, 196, 195, 192, 192, 42, 43, 195, 191, 192, 196, 43, 44, 190, 41, 192, 192, 41, 42, 57, 58, 56, 55, 58, 59, 197, 59, 193, 193, 120, 191, 59, 60, 193, 193, 60, 120, 191, 188, 189, 191, 120, 188, 60, 61, 120, 120, 61, 188, 191, 190, 192, 191, 189, 190, 61, 62, 188, 189, 187, 190, 189, 188, 186, 188, 119, 186, 189, 186, 187, 188, 62, 119, 190, 40, 41, 40, 190, 187, 186, 185, 187, 40, 187, 39, 62, 63, 119, 186, 119, 184, 119, 183, 184, 186, 184, 185, 119, 63, 183, 187, 38, 39, 187, 185, 38, 63, 64, 183, 184, 183, 181, 183, 180, 181, 184, 182, 185, 184, 181, 182, 183, 64, 180, 38, 185, 37, 185, 182, 37, 64, 65, 180, 180, 178, 181, 181, 179, 182, 181, 178, 179, 180, 118, 178, 180, 65, 118, 182, 179, 37, 65, 66, 118, 179, 36, 37, 118, 176, 178, 178, 177, 179, 179, 177, 36, 178, 176, 177, 118, 175, 176, 118, 66, 175, 66, 67, 175, 177, 35, 36, 176, 175, 174, 175, 173, 174, 176, 174, 177, 175, 67, 173, 177, 34, 35, 177, 174, 34, 67, 68, 173, 173, 172, 174, 173, 117, 172, 173, 68, 117, 174, 33, 34, 174, 172, 33, 172, 32, 33, 68, 69, 117, 117, 171, 172, 117, 170, 171, 117, 69, 170, 32, 171, 31, 32, 172, 171, 69, 70, 170, 170, 169, 171, 31, 171, 30, 170, 168, 169, 170, 70, 168, 171, 169, 30, 168, 167, 169, 169, 167, 30, 70, 71, 168, 168, 116, 167, 168, 71, 116, 167, 29, 30, 116, 166, 167, 167, 166, 29, 71, 165, 116, 116, 165, 166, 166, 28, 29, 71, 72, 165, 165, 164, 166, 166, 27, 28, 166, 164, 27, 72, 163, 165, 165, 163, 164, 72, 73, 163, 163, 162, 164, 164, 26, 27, 164, 162, 26, 73, 161, 163, 163, 161, 162, 73, 74, 161, 161, 160, 162, 162, 25, 26, 162, 160, 25, 74, 115, 161, 161, 115, 160, 74, 75, 115, 160, 24, 25, 75, 76, 115, 115, 159, 160, 160, 159, 24, 76, 158, 115, 115, 158, 159, 76, 77, 158, 158, 157, 159, 159, 23, 24, 159, 157, 23, 77, 156, 158, 158, 156, 157, 77, 78, 156, 156, 155, 157, 157, 22, 23, 157, 155, 22, 78, 114, 156, 156, 114, 155, 78, 79, 114, 114, 154, 155, 155, 21, 22, 155, 154, 21, 79, 153, 114, 114, 153, 154, 79, 80, 153, 153, 152, 154, 154, 20, 21, 154, 152, 20, 80, 151, 153, 153, 151, 152, 152, 150, 20, 152, 151, 150, 80, 81, 151, 81, 113, 151, 151, 113, 150, 20, 150, 19, 150, 149, 19, 150, 113, 149, 19, 149, 18, 81, 82, 113, 113, 148, 149, 113, 82, 148, 18, 149, 17, 17, 149, 16, 82, 83, 148, 149, 148, 147, 149, 147, 16, 148, 112, 147, 148, 83, 112, 147, 146, 16, 16, 146, 15, 83, 84, 112, 147, 112, 146, 112, 145, 146, 112, 84, 145, 15, 146, 14, 14, 146, 143, 14, 143, 144, 146, 145, 143, 84, 85, 145, 14, 144, 13, 85, 86, 145, 145, 86, 111, 145, 111, 143, 111, 86, 87, 143, 141, 144, 144, 142, 13, 144, 141, 142, 13, 142, 12, 111, 140, 143, 143, 140, 141, 12, 142, 11, 142, 139, 11, 11, 139, 10, 141, 138, 142, 142, 138, 139, 140, 111, 88, 140, 110, 141, 141, 110, 138, 111, 87, 88, 88, 89, 140, 140, 89, 110, 139, 137, 10, 10, 137, 9, 138, 136, 139, 139, 136, 137, 110, 135, 138, 138, 135, 136, 89, 90, 110, 110, 90, 135, 135, 109, 136, 136, 133, 137, 136, 109, 133, 137, 134, 9, 137, 133, 134, 90, 91, 135, 135, 91, 109, 133, 109, 131, 133, 131, 134, 131, 109, 130, 134, 131, 132, 91, 92, 109, 109, 92, 130, 92, 93, 130, 130, 108, 131, 108, 128, 131, 132, 131, 128, 130, 93, 108, 93, 94, 108, 108, 126, 128, 128, 126, 127, 108, 94, 125, 94, 95, 125, 108, 125, 126, 95, 96, 125, 9, 134, 8, 134, 132, 8, 8, 132, 7, 132, 128, 129, 132, 129, 7, 7, 129, 6, 128, 127, 129, 129, 127, 6, 6, 127, 5, 5, 127, 4, 96, 107, 125, 125, 123, 126, 125, 107, 123, 126, 124, 127, 126, 123, 124, 127, 3, 4, 127, 124, 3, 96, 97, 107, 97, 98, 107, 98, 121, 107, 107, 122, 123, 107, 121, 122, 123, 2, 124, 123, 122, 2, 124, 2, 3, 98, 99, 121, 122, 121, 106, 121, 99, 106, 106, 1, 122, 122, 1, 2, 99, 100, 106, 100, 101, 106, 1, 106, 0, 106, 101, 0, 101, 102, 0, 102, 103, 0, 103, 105, 0, 103, 104, 105], "vertices": [2, 5, -44.87, 40.89, 1, 85, -774.56, 63.4, 0, 2, 5, -1.2, 32.65, 1, 85, -735.38, 84.38, 0, 2, 5, 35.43, 37.27, 1, 82, -323.7, 97.85, 0, 2, 5, 84.24, 49.47, 1, 82, -293.52, 138.11, 0, 2, 5, 113.1, 56.69, 1, 82, -275.68, 161.91, 0, 2, 5, 150.84, 54.53, 1, 82, -245.03, 184.04, 0, 2, 5, 164.42, 33.4, 1, 82, -221.17, 176.21, 0, 3, 5, 182.11, 5.87, 0.9447, 81, 20.24, 104.59, 0.0553, 85, -575.8, 178.48, 0, 3, 5, 194.77, -13.82, 0.78283, 81, 43.64, 103.97, 0.21717, 85, -553.6, 171.08, 0, 3, 5, 210.83, -55.98, 0.38744, 81, 87.52, 93.52, 0.61256, 85, -514.65, 148.32, 0, 3, 5, 222.39, -86.34, 0.18196, 81, 119.12, 85.99, 0.81804, 85, -486.6, 131.93, 0, 2, 5, 233.19, -114.68, 0.07453, 81, 148.63, 78.96, 0.92547, 2, 5, 239.53, -131.33, 0.04167, 81, 165.95, 74.84, 0.95833, 3, 5, 240.48, -141.23, 0.02956, 81, 174.68, 70.05, 0.96982, 82, -52, 88.66, 0.00062, 3, 5, 242.21, -159.23, 0.01155, 81, 190.53, 61.35, 0.96878, 82, -39.3, 75.78, 0.01967, 3, 5, 254.81, -176.36, 0.00243, 81, 211.78, 62.12, 0.88325, 82, -18.72, 70.44, 0.11432, 3, 5, 270.8, -198.1, 9e-05, 81, 238.74, 63.1, 0.62117, 82, 7.39, 63.65, 0.37874, 2, 81, 262.5, 63.96, 0.32768, 82, 30.4, 57.67, 0.67232, 2, 81, 273.85, 64.38, 0.22595, 82, 41.39, 54.82, 0.77405, 2, 81, 289.12, 57.92, 0.10919, 82, 54.18, 44.25, 0.89081, 2, 81, 315.13, 46.93, 0.00814, 82, 75.95, 26.27, 0.99186, 2, 82, 106.45, 40.82, 1, 85, -279.86, 51.96, 0, 3, 82, 129.44, 48.92, 0.99167, 83, -74.92, 50.05, 0.00833, 85, -256.83, 59.95, 0, 3, 82, 156.95, 58.6, 0.90006, 83, -47.26, 59.32, 0.09994, 85, -229.27, 69.51, 0, 3, 82, 190.28, 70.34, 0.59631, 83, -13.76, 70.55, 0.40369, 85, -195.89, 81.09, 0, 4, 82, 213.94, 70.78, 0.35846, 83, 9.9, 70.64, 0.64025, 84, -90.64, 58.61, 0.0013, 85, -172.23, 81.42, 0, 3, 82, 242.6, 71.31, 0.11903, 83, 38.57, 70.74, 0.84393, 84, -62.27, 62.69, 0.03704, 4, 82, 271.86, 71.86, 0.01784, 83, 67.83, 70.85, 0.78844, 84, -33.3, 66.87, 0.19246, 85, -114.31, 82.24, 0.00126, 4, 82, 292.98, 72.26, 0.00135, 83, 88.95, 70.92, 0.58837, 84, -12.4, 69.88, 0.39863, 85, -93.19, 82.54, 0.01165, 3, 83, 107.9, 70.99, 0.3747, 84, 6.35, 72.58, 0.58539, 85, -74.25, 82.81, 0.03991, 3, 83, 132.91, 71.08, 0.15532, 84, 31.12, 76.14, 0.71574, 85, -49.23, 83.16, 0.12894, 4, 83, 160.31, 71.18, 0.03623, 84, 58.23, 80.05, 0.63633, 85, -21.84, 83.55, 0.3229, 86, -117.01, 71.02, 0.00454, 4, 83, 166.41, 71.95, 0.02361, 84, 64.16, 81.66, 0.5897, 85, -15.75, 84.38, 0.3783, 86, -111.05, 72.52, 0.00839, 4, 83, 185.59, 73.69, 0.00375, 84, 82.92, 86.05, 0.42404, 85, 3.41, 86.32, 0.5403, 86, -92.22, 76.57, 0.03191, 4, 84, 113.62, 93.23, 0.17769, 85, 34.78, 89.5, 0.68602, 86, -61.39, 83.21, 0.13477, 87, -136.29, 104.8, 0.00152, 4, 84, 145.82, 100.76, 0.0442, 85, 67.68, 92.83, 0.58354, 86, -29.07, 90.16, 0.35428, 87, -103.29, 106.84, 0.01798, 5, 84, 174.67, 107.51, 0.00637, 85, 97.16, 95.81, 0.35592, 86, -0.1, 96.4, 0.57037, 87, -73.71, 108.66, 0.06411, 88, -136.82, 135.75, 0.00323, 4, 85, 124.22, 98.55, 0.17236, 86, 26.48, 102.12, 0.66122, 87, -46.57, 110.34, 0.14744, 88, -109.83, 132.47, 0.01898, 4, 85, 155.45, 101.72, 0.04981, 86, 57.17, 108.72, 0.58485, 87, -15.24, 112.27, 0.28965, 88, -78.66, 128.69, 0.07569, 4, 85, 180.84, 109.28, 0.01081, 86, 81.57, 119.05, 0.42243, 87, 10.43, 118.83, 0.38636, 88, -52.23, 130.48, 0.18041, 4, 85, 191.98, 112.6, 0.00457, 86, 92.27, 123.59, 0.34747, 87, 21.69, 121.71, 0.40311, 88, -40.63, 131.27, 0.24485, 4, 85, 212.84, 123.52, 7e-05, 86, 111.8, 136.75, 0.21889, 87, 42.97, 131.81, 0.38508, 88, -17.88, 137.33, 0.39596, 3, 86, 140.94, 156.41, 0.09757, 87, 74.73, 146.87, 0.27399, 88, 16.09, 146.38, 0.62844, 3, 86, 152.86, 164.44, 0.06306, 87, 87.71, 153.03, 0.21628, 88, 29.98, 150.08, 0.72066, 3, 86, 173.81, 199.81, 0.01085, 87, 113.73, 184.86, 0.10129, 88, 61.34, 176.66, 0.88787, 3, 86, 167.65, 219.78, 0.00423, 87, 110.63, 205.53, 0.0825, 88, 62.04, 197.55, 0.91327, 3, 86, 183.07, 239.02, 0.0017, 87, 128.75, 222.24, 0.07391, 88, 82.9, 210.69, 0.92439, 3, 86, 212.78, 238.57, 0.00037, 87, 158.06, 217.35, 0.06624, 88, 110.83, 200.56, 0.93339, 2, 87, 196.42, 210.95, 0.05944, 88, 147.39, 187.31, 0.94056, 3, 86, 250.89, 193.98, 0.00016, 87, 189.06, 167.55, 0.06672, 88, 132.28, 145.97, 0.93311, 3, 86, 249.93, 139.53, 0.00172, 87, 179.96, 113.86, 0.07385, 88, 113.58, 94.82, 0.92443, 3, 86, 248.92, 82.02, 0.00026, 87, 170.35, 57.15, 0.03245, 88, 93.84, 40.79, 0.96729, 2, 83, 517.32, -16.22, 0, 88, 75.14, -10.36, 1, 3, 83, 507.96, -83.06, 0, 87, 149.97, -63.09, 0.20381, 88, 51.97, -73.75, 0.79619, 4, 83, 499.61, -142.71, 0, 86, 245.73, -100.14, 0.00767, 87, 139.91, -122.48, 0.44471, 88, 31.3, -130.32, 0.54761, 4, 83, 497.12, -160.54, 0, 86, 245.41, -118.14, 0.01023, 87, 136.9, -140.23, 0.47544, 88, 25.12, -147.23, 0.51434, 4, 83, 493.21, -188.46, 0, 86, 244.92, -146.33, 0.01212, 87, 132.19, -168.02, 0.49544, 88, 15.44, -173.71, 0.49244, 4, 83, 493.52, -192.42, 0, 86, 245.71, -150.22, 0.01203, 87, 132.39, -171.99, 0.4955, 88, 14.91, -177.64, 0.49247, 4, 83, 484.98, -191.22, 0, 86, 237.09, -150.06, 0.01289, 87, 123.89, -170.54, 0.49659, 88, 6.82, -174.68, 0.49052, 4, 83, 448.69, -181.31, 0, 86, 199.87, -144.63, 0.0409, 87, 87.9, -159.59, 0.52894, 88, -26.58, -157.38, 0.43017, 5, 83, 417.78, -172.87, 0, 85, 238.19, -157.77, 0.0007, 86, 168.16, -139.99, 0.10413, 87, 57.24, -150.26, 0.56971, 88, -55.04, -142.64, 0.32547, 5, 83, 381.54, -162.97, 0, 85, 201.84, -148.25, 0.01012, 86, 130.98, -134.56, 0.23333, 87, 21.3, -139.33, 0.57035, 88, -88.4, -125.36, 0.1862, 5, 83, 350.85, -151.84, 0, 85, 171.03, -137.45, 0.039, 86, 99.17, -127.25, 0.38904, 87, -9.06, -127.33, 0.48253, 88, -116.08, -108.05, 0.08944, 5, 83, 322.14, -141.44, 0, 85, 142.22, -127.35, 0.1009, 86, 69.41, -120.4, 0.5335, 87, -37.46, -116.1, 0.33263, 88, -141.96, -91.86, 0.03297, 5, 83, 300.23, -133.5, 0, 85, 120.23, -119.64, 0.18267, 86, 46.71, -115.18, 0.59668, 87, -59.12, -107.53, 0.20943, 88, -161.72, -79.5, 0.01123, 6, 83, 280.34, -126.29, 0, 84, 204.54, -98.82, 0.00111, 85, 100.26, -112.64, 0.28935, 86, 26.08, -110.43, 0.589, 87, -78.8, -99.75, 0.11796, 88, -179.66, -68.28, 0.00258, 5, 83, 251.78, -115.94, 0, 84, 174.82, -92.54, 0.01575, 85, 71.6, -102.6, 0.48584, 86, -3.52, -103.62, 0.46027, 87, -107.05, -88.59, 0.03813, 5, 83, 226.6, -106.81, 0, 84, 148.62, -87, 0.06739, 85, 46.32, -93.74, 0.63292, 86, -29.62, -97.62, 0.29162, 87, -131.96, -78.74, 0.00807, 4, 83, 185.56, -91.94, 1e-05, 84, 105.9, -77.98, 0.3295, 85, 5.12, -79.29, 0.59697, 86, -72.17, -87.83, 0.07352, 5, 83, 151.53, -83.48, 0.0242, 84, 71.03, -74.32, 0.67467, 85, -28.99, -71.19, 0.2897, 86, -106.97, -83.56, 0.01144, 87, -206.32, -53.25, 0, 5, 83, 125.36, -76.97, 0.12704, 84, 44.21, -71.52, 0.77606, 85, -55.22, -64.96, 0.09611, 86, -133.73, -80.27, 0.0008, 87, -232.29, -46, 0, 4, 83, 98.32, -73.22, 0.3754, 84, 16.91, -71.56, 0.60943, 85, -82.31, -61.49, 0.01517, 87, -259.22, -41.47, 0, 5, 82, 278.52, -68.53, 0.00576, 83, 72.37, -69.62, 0.67306, 84, -9.29, -71.61, 0.32101, 85, -108.29, -58.17, 0.00018, 87, -285.05, -37.13, 0, 5, 82, 251.24, -65.17, 0.06803, 83, 45.15, -65.85, 0.83466, 84, -36.76, -71.66, 0.09731, 85, -135.55, -54.69, 0, 87, -312.15, -32.58, 0, 5, 82, 227.01, -62.18, 0.24033, 83, 20.97, -62.5, 0.74441, 84, -61.18, -71.7, 0.01526, 85, -159.77, -51.59, 0, 87, -336.23, -28.54, 0, 5, 82, 206.92, -62.77, 0.4893, 83, 0.87, -62.79, 0.50985, 84, -81.04, -74.78, 0.00085, 85, -179.86, -52.09, 0, 87, -356.33, -28.24, 0, 4, 82, 185.44, -62.83, 0.75209, 83, -20.61, -62.53, 0.24791, 85, -201.34, -52.06, 0, 87, -377.79, -27.37, 0, 4, 82, 154.45, -62.93, 0.95468, 83, -51.6, -62.16, 0.04532, 85, -232.33, -52.01, 0, 87, -408.76, -26.1, 0, 3, 82, 128.46, -63.01, 0.99697, 83, -77.59, -61.84, 0.00303, 85, -258.32, -51.97, 0, 2, 82, 98.67, -63.1, 1, 85, -288.11, -51.93, 0, 2, 82, 68, -63.19, 1, 85, -318.78, -51.88, 0, 2, 81, 306.33, -49.17, 0.02117, 82, 39.99, -63.28, 0.97883, 2, 81, 281.39, -53.24, 0.16915, 82, 14.92, -60.03, 0.83085, 2, 81, 254.28, -57.66, 0.52321, 82, -12.32, -56.5, 0.47679, 2, 81, 224.03, -62.6, 0.87489, 82, -42.72, -52.57, 0.12511, 2, 81, 201.3, -66.31, 0.97866, 82, -65.55, -49.61, 0.02134, 2, 81, 183.24, -69.26, 0.99754, 82, -83.7, -47.26, 0.00246, 1, 81, 163.75, -61.81, 1, 2, 81, 141.83, -53.45, 1, 82, -118.84, -20.25, 0, 2, 81, 126.9, -47.74, 1, 82, -131.51, -10.51, 0, 2, 81, 100.35, -43.27, 1, 82, -155.67, 1.39, 0, 2, 81, 78.15, -39.52, 1, 82, -175.87, 11.34, 0, 2, 81, 56.4, -41.72, 1, 82, -197.34, 15.46, 0, 3, 5, 67.7, -89.9, 0.00059, 81, 35, -43.88, 0.99941, 82, -218.46, 19.52, 0, 2, 5, 52.59, -73.21, 0.06116, 81, 12.7, -46.97, 0.93884, 2, 5, 40.83, -60.2, 0.23878, 81, -4.67, -49.38, 0.76122, 2, 5, 30.44, -50.26, 0.46019, 81, -18.74, -52.37, 0.53981, 2, 5, 19.92, -40.19, 0.69035, 81, -32.98, -55.4, 0.30965, 2, 5, 4.92, -26.14, 0.91882, 81, -53.03, -59.89, 0.08118, 2, 5, -8.14, -14.46, 0.98952, 81, -70.04, -64.11, 0.01048, 2, 5, -19.03, -4.7, 1, 85, -725.84, 44.1, 0, 2, 5, -35.99, 8.72, 1, 85, -747.46, 43.92, 0, 2, 5, -49.88, 19.72, 1, 85, -765.19, 43.77, 0, 2, 5, -63.51, 30.5, 1, 85, -782.56, 43.62, 0, 2, 5, -77.05, 41.22, 1, 85, -799.84, 43.48, 0, 2, 5, -66.05, 44.88, 1, 85, -793.56, 53.23, 0, 2, 5, -12.79, 9.3, 1, 85, -729.76, 58.92, 0, 2, 5, 25.84, -24.07, 0.80593, 81, -42.96, -41.43, 0.19407, 3, 5, 70.69, -67.79, 0.01396, 81, 18.41, -28.96, 0.98604, 82, -230.07, 38.56, 0, 2, 81, 75.14, -24.33, 1, 82, -174.4, 26.75, 0, 2, 81, 124.86, -34.43, 1, 82, -129.65, 2.83, 0, 1, 81, 166.04, -50.31, 1, 2, 81, 233.94, -50.86, 0.77446, 82, -29.85, -44.15, 0.22554, 2, 81, 299.69, -40.37, 0.03208, 82, 36.14, -52.94, 0.96792, 3, 82, 116.24, -50.55, 0.99996, 83, -89.62, -49.21, 4e-05, 85, -270.49, -39.46, 0, 4, 82, 200.59, -53.55, 0.57326, 83, -5.32, -53.48, 0.42674, 85, -186.15, -42.84, 0, 87, -362.25, -18.75, 0, 4, 83, 95.18, -60.56, 0.42394, 84, 12.04, -59.46, 0.56824, 85, -85.58, -48.87, 0.00782, 87, -261.99, -28.72, 0, 4, 83, 178.53, -77.12, 0.00153, 84, 96.89, -64.28, 0.43177, 85, -2.06, -64.55, 0.5234, 86, -80.94, -73.97, 0.04331, 5, 83, 260.13, -93.43, 0, 84, 179.96, -69.09, 0.01101, 85, 79.71, -80, 0.47635, 86, 2.04, -80.27, 0.47988, 87, -98.06, -66.33, 0.03276, 5, 83, 348.78, -112.36, 0, 85, 168.55, -97.99, 0.04831, 86, 92.33, -88.3, 0.46058, 87, -9.99, -87.8, 0.44267, 88, -109.82, -69.01, 0.04844, 5, 83, 424.73, -127.34, 0, 85, 244.66, -112.17, 0.00054, 86, 169.54, -93.96, 0.09186, 87, 65.5, -104.96, 0.58326, 88, -38.7, -99.59, 0.32435, 2, 5, 6.47, -7.34, 0.98657, 81, -67.7, -48.03, 0.01343, 3, 5, 24.8, 11.53, 1, 82, -315.72, 71.18, 0, 85, -701.89, 84.23, 0, 2, 5, 49.05, -1.25, 0.9897, 81, -48.76, -9.4, 0.0103, 2, 5, 75.09, 29.49, 1, 82, -288.02, 116.83, 0, 2, 5, 50.82, -48.42, 0.24878, 81, -8.78, -34.49, 0.75122, 2, 5, 75.17, -24.19, 0.37188, 81, -15.11, -0.72, 0.62812, 2, 5, 106.69, 15.95, 1, 82, -254.95, 126.26, 0, 3, 5, 97.33, -43.47, 0.06755, 81, 13.3, 6.74, 0.93245, 85, -610.9, 86.88, 0, 3, 5, 134.34, -3.87, 0.97656, 81, 1.4, 59.62, 0.02344, 85, -606.91, 140.94, 0, 2, 81, 47.93, -26.55, 1, 82, -201.1, 32.42, 0, 3, 5, 117.78, -64.67, 0.07642, 81, 42.33, 11.71, 0.92358, 85, -581.67, 83.19, 0, 3, 5, 155.9, -22.35, 0.77796, 81, 28.81, 67.05, 0.22204, 85, -578.52, 140.07, 0, 3, 5, 138.83, -88.81, 0.05938, 81, 74.13, 15.53, 0.94062, 85, -550.14, 77.6, 0, 3, 5, 176.88, -50.29, 0.47627, 81, 63.71, 68.66, 0.52373, 85, -544.66, 131.46, 0, 2, 81, 102.04, -29.8, 1, 82, -150.19, 13.81, 0, 3, 5, 156.31, -110.51, 0.03764, 81, 101.92, 17.77, 0.96236, 85, -522.9, 71.66, 0, 3, 5, 194.11, -73.89, 0.2828, 81, 92.92, 69.61, 0.7172, 85, -516.44, 123.88, 0, 2, 5, 170.94, -128.8, 0.02356, 81, 125.26, 19.56, 0.97644, 2, 5, 208.8, -98.74, 0.1419, 81, 121.72, 67.78, 0.8581, 2, 81, 146.82, -42.9, 1, 82, -111.04, -11.57, 0, 2, 5, 182.39, -154.15, 0.00743, 81, 152.66, 14.76, 0.99257, 2, 5, 219.98, -126.83, 0.05491, 81, 151.24, 61.21, 0.94509, 2, 5, 192.42, -179.32, 0.00105, 81, 179.11, 8.89, 0.99895, 3, 5, 230.64, -152.22, 0.01903, 81, 178.23, 55.73, 0.97648, 82, -52.7, 73.92, 0.00448, 2, 81, 208.43, -50.65, 0.96041, 82, -54.23, -36.65, 0.03959, 3, 5, 210.1, -212.9, 1e-05, 81, 216.82, 4.6, 0.99662, 82, -30.37, 13.88, 0.00337, 2, 81, 238.87, 4.66, 0.94172, 82, -9.23, 7.62, 0.05828, 2, 81, 264.64, -45.96, 0.34634, 82, 0.96, -48.26, 0.65366, 1, 82, 18.18, 2.69, 1, 1, 82, 48.76, -4.15, 1, 2, 82, 67.5, -52.01, 1, 85, -319.23, -40.69, 0, 2, 82, 76.59, -8.62, 1, 85, -309.94, 2.65, 0, 2, 82, 96.63, -51.14, 1, 85, -290.1, -39.96, 0, 2, 82, 102.04, -9.99, 1, 85, -284.5, 1.16, 0, 2, 82, 128.2, -9.05, 1, 85, -258.34, 1.99, 0, 4, 82, 145.05, -51.58, 0.98291, 83, -60.82, -50.67, 0.01709, 85, -241.68, -40.62, 0, 87, -417.65, -14.35, 0, 3, 82, 158.69, -10.41, 1, 85, -227.86, 0.49, 0, 87, -402.22, 26.18, 0, 4, 82, 179.4, -52.8, 0.81456, 83, -26.5, -52.4, 0.18544, 85, -207.34, -41.99, 0, 87, -383.39, -17.07, 0, 4, 82, 185.09, -11.39, 0.9564, 83, -20.18, -11.09, 0.0436, 85, -201.46, -0.61, 0, 87, -375.89, 24.04, 0, 4, 82, 210.14, -12.53, 0.34122, 83, 4.85, -12.61, 0.65878, 85, -176.41, -1.87, 0, 87, -350.91, 21.8, 0, 5, 82, 232.4, -55.31, 0.18779, 83, 26.46, -55.71, 0.78971, 84, -56.68, -64.22, 0.02249, 85, -154.35, -44.75, 0, 87, -330.55, -21.91, 0, 4, 82, 240.46, -14, 0.00863, 83, 35.15, -14.54, 0.99137, 85, -146.09, -3.48, 0, 87, -320.68, 19, 0, 4, 82, 261.42, -56.92, 0.03128, 83, 55.45, -57.76, 0.82223, 84, -27.69, -62.21, 0.14649, 87, -301.62, -24.78, 0, 3, 83, 64.13, -13.95, 0.97808, 84, -25.18, -17.62, 0.02192, 87, -291.69, 18.75, 0, 5, 82, 287.01, -58.33, 0.00143, 83, 81.01, -59.56, 0.59775, 84, -2.13, -60.44, 0.39942, 85, -99.76, -48.02, 0.0014, 87, -276.13, -27.32, 0, 3, 83, 91.81, -13.19, 0.46646, 84, 2.12, -13.02, 0.53354, 87, -264, 18.72, 0, 3, 83, 111.45, -12.95, 0.03863, 84, 21.54, -10.06, 0.96137, 87, -244.36, 18.39, 0, 5, 83, 118.84, -65.26, 0.18121, 84, 36.13, -60.83, 0.76251, 85, -61.87, -53.32, 0.05617, 86, -141.63, -69.44, 0.00011, 87, -238.48, -34.1, 0, 3, 83, 136.7, -11.85, 0.00084, 84, 46.39, -5.45, 0.99916, 87, -219.09, 18.77, 0, 5, 83, 145.53, -70.56, 0.04165, 84, 63.3, -62.37, 0.74052, 85, -35.12, -58.34, 0.21241, 86, -114.49, -71.47, 0.00543, 87, -211.95, -40.17, 0, 1, 84, 69.75, 0.47, 1, 2, 84, 95.41, 1.46, 0.28101, 85, 4.93, 0.83, 0.71899, 4, 84, 126.22, -65.98, 0.17288, 85, 26.81, -70, 0.69006, 86, -51.64, -76.2, 0.13698, 87, -150.52, -54.26, 8e-05, 2, 84, 126.21, 4.75, 7e-05, 85, 35.9, 0.13, 0.99993, 5, 83, 235.43, -88.5, 0, 84, 154.82, -67.63, 0.05145, 85, 54.96, -75.32, 0.63661, 86, -23.07, -78.36, 0.30441, 87, -122.6, -60.68, 0.00753, 2, 83, 250.78, -15.23, 0, 85, 69.53, -1.9, 1, 4, 84, 151.6, 83.05, 0.03542, 85, 71.15, 74.52, 0.58138, 86, -23.6, 72.35, 0.36572, 87, -100.54, 88.41, 0.01747, 2, 83, 279.42, -17.92, 0, 86, 12.03, -2.98, 1, 5, 84, 181.33, 91.28, 0.00389, 85, 101.68, 78.86, 0.32493, 86, 6.27, 80.05, 0.60146, 87, -69.86, 91.54, 0.06636, 88, -136.14, 118.22, 0.00337, 6, 83, 292.61, -100.37, 0, 84, 213.09, -71.44, 5e-05, 85, 112.25, -86.59, 0.24643, 86, 35.12, -83.21, 0.63408, 87, -65.79, -74.2, 0.11718, 88, -162.23, -45.51, 0.00227, 2, 83, 305.19, -18.98, 0, 86, 37.74, -0.9, 1, 4, 85, 126.95, 80.39, 0.15531, 86, 31.22, 84.37, 0.68167, 87, -44.55, 92.08, 0.14583, 88, -111.15, 114.15, 0.01719, 5, 83, 323.09, -106.87, 0, 85, 142.81, -92.78, 0.1093, 86, 66.17, -85.98, 0.59977, 87, -35.51, -81.58, 0.2742, 88, -133.78, -58.26, 0.01673, 2, 86, 69.93, 0.42, 1, 88, -102, 22.16, 0, 4, 85, 157.31, 86.47, 0.04643, 86, 60.71, 93.78, 0.59647, 87, -13.97, 96.96, 0.28902, 88, -80.2, 113.4, 0.06809, 3, 86, 95.53, 3.74, 0.18717, 87, 6.97, 2.73, 0.81256, 88, -76.71, 16.94, 0.00026, 4, 85, 184.74, 91.98, 0.00922, 86, 87.36, 102.29, 0.42578, 87, 13.65, 101.39, 0.39911, 88, -52.23, 112.74, 0.16589, 5, 83, 382.75, -119.06, 0, 85, 202.59, -104.34, 0.01151, 86, 126.86, -90.84, 0.25723, 87, 23.78, -95.47, 0.59359, 88, -78, -82.69, 0.13766, 3, 86, 129.69, 8.81, 0.00123, 87, 41.49, 2.63, 0.9976, 88, -42.78, 10.57, 0.00117, 4, 85, 217.23, 103.51, 0.00016, 86, 118.38, 117.35, 0.22868, 87, 46.57, 111.63, 0.40765, 88, -18, 116.84, 0.36351, 2, 87, 77.02, -0.05, 0.89377, 88, -8.32, 1.49, 0.10623, 3, 86, 150.43, 137.12, 0.09928, 87, 81.22, 126.38, 0.28692, 88, 18.75, 125.05, 0.61379, 4, 83, 460.38, -134.66, 0, 86, 205.81, -96.9, 0.02809, 87, 100.92, -113.3, 0.49366, 88, -5.38, -114.22, 0.47825, 3, 83, 471.6, -17.55, 0, 87, 115.51, 3.44, 0.00017, 88, 30.16, -2.07, 0.99983, 3, 86, 202.04, 110.47, 0.02003, 87, 128.26, 92.3, 0.14117, 88, 58.83, 83, 0.8388, 3, 86, 183.16, 169.88, 0.02448, 87, 118.49, 153.86, 0.13834, 88, 60.39, 145.32, 0.83718, 4, 83, 481.8, -139.06, 0, 86, 227.6, -98.67, 0.01158, 87, 122.21, -118.31, 0.45484, 88, 14.65, -123.01, 0.53358, 2, 83, 501.64, -13.24, 0, 88, 60.43, -4.16, 1, 3, 86, 230.04, 131.45, 0.00475, 87, 159.08, 108.85, 0.0832, 88, 92.14, 93.68, 0.91204, 3, 86, 204.48, 204.02, 0.00375, 87, 144.68, 184.43, 0.07985, 88, 91.7, 170.61, 0.91639, 3, 86, 237.21, 189.62, 0.00083, 87, 174.89, 165.29, 0.07007, 88, 117.93, 146.32, 0.92909], "hull": 106, "edges": [2, 4, 8, 10, 40, 42, 62, 64, 86, 88, 88, 90, 90, 92, 112, 114, 114, 116, 148, 150, 194, 196, 208, 210, 2, 0, 0, 210, 0, 212, 212, 2, 212, 242, 242, 214, 242, 244, 244, 4, 214, 246, 246, 248, 4, 6, 6, 8, 248, 6, 214, 250, 250, 216, 250, 252, 252, 254, 254, 8, 216, 256, 256, 258, 10, 12, 258, 12, 216, 260, 260, 218, 260, 262, 262, 264, 12, 14, 14, 16, 264, 14, 218, 266, 266, 268, 268, 16, 218, 270, 270, 220, 270, 272, 272, 274, 16, 18, 274, 18, 220, 276, 276, 278, 18, 20, 278, 20, 220, 280, 280, 222, 280, 282, 282, 284, 20, 22, 22, 24, 284, 22, 222, 286, 286, 288, 24, 26, 26, 28, 288, 26, 222, 290, 290, 224, 290, 292, 28, 30, 292, 30, 224, 294, 30, 32, 294, 32, 224, 296, 296, 226, 296, 298, 32, 34, 34, 36, 298, 34, 226, 300, 36, 38, 38, 40, 300, 38, 226, 302, 302, 304, 304, 40, 228, 306, 306, 302, 306, 308, 308, 42, 228, 310, 42, 44, 310, 44, 228, 312, 312, 314, 44, 46, 46, 48, 314, 46, 230, 316, 316, 312, 316, 318, 318, 48, 230, 320, 48, 50, 320, 50, 230, 322, 322, 324, 50, 52, 324, 52, 326, 322, 326, 328, 52, 54, 328, 54, 232, 330, 330, 326, 330, 332, 54, 56, 332, 56, 232, 334, 56, 58, 334, 58, 232, 336, 336, 338, 58, 60, 60, 62, 338, 60, 234, 340, 340, 336, 340, 342, 342, 62, 234, 344, 64, 66, 344, 66, 234, 346, 346, 348, 66, 68, 348, 68, 236, 350, 350, 346, 350, 352, 352, 354, 68, 70, 354, 70, 236, 356, 356, 358, 70, 72, 358, 72, 236, 360, 360, 362, 362, 364, 72, 74, 74, 76, 364, 74, 238, 366, 366, 360, 366, 368, 368, 370, 370, 76, 238, 372, 372, 374, 76, 78, 78, 80, 374, 78, 238, 376, 376, 240, 376, 378, 378, 380, 80, 82, 380, 82, 240, 382, 382, 384, 82, 84, 84, 86, 384, 84, 386, 240, 386, 388, 388, 390, 390, 392, 392, 88, 108, 394, 394, 386, 394, 396, 396, 398, 398, 400, 400, 92, 92, 94, 94, 96, 94, 402, 402, 100, 106, 108, 104, 106, 100, 102, 102, 104, 96, 98, 98, 100, 108, 110, 110, 112, 134, 136, 122, 124, 124, 126, 120, 122, 116, 118, 118, 120, 130, 132, 132, 134, 126, 128, 128, 130, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 170, 172, 168, 170, 166, 168, 162, 164, 164, 166, 176, 178, 172, 174, 174, 176, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 196, 198, 198, 200, 206, 208, 204, 206, 200, 202, 202, 204], "width": 303, "height": 883}}, "bozi": {"bozi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-72.22, -85.34, -95.2, 68.96, 117.46, 100.63, 140.44, -53.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 121, "height": 167}}, "t1": {"t1": {"type": "mesh", "uvs": [0.59719, 0, 0.70823, 0.04835, 0.82436, 0.09892, 0.90279, 0.18741, 0.95929, 0.25116, 0.97091, 0.33005, 0.98525, 0.42736, 1, 0.52743, 1, 0.56364, 0.96293, 0.64541, 0.92828, 0.72184, 0.86824, 0.77686, 0.81286, 0.82762, 0.73422, 0.89968, 0.66305, 0.9649, 0.53203, 0.99813, 0.48202, 0.97995, 0.40766, 0.90441, 0.287, 0.835, 0.21393, 0.75147, 0.17361, 0.66978, 0.12759, 0.57652, 0.0782, 0.47645, 0.03733, 0.39363, 0, 0.318, 0, 0.24488, 0.04444, 0.16399, 0.08595, 0.08843, 0.25139, 0.03547, 0.37247, 0.0172, 0.48644, 0, 0.0797, 0.38737, 0.15538, 0.37942, 0.23106, 0.39401, 0.27818, 0.40992, 0.48666, 0.37942, 0.54806, 0.3383, 0.65373, 0.29586, 0.75654, 0.27861, 0.8208, 0.28392, 0.43383, 0.54732, 0.41098, 0.66404, 0.44097, 0.71975, 0.5195, 0.6972, 0.42383, 0.80768, 0.48952, 0.81962, 0.5452, 0.80901, 0.64088, 0.78115], "triangles": [38, 1, 2, 39, 38, 2, 3, 39, 2, 39, 3, 4, 37, 0, 1, 37, 1, 38, 32, 24, 25, 39, 4, 5, 37, 36, 30, 37, 30, 0, 36, 29, 30, 35, 29, 36, 28, 26, 27, 32, 26, 28, 32, 25, 26, 34, 33, 28, 33, 32, 28, 31, 24, 32, 23, 24, 31, 34, 28, 29, 35, 34, 29, 22, 23, 31, 40, 34, 35, 39, 6, 38, 32, 22, 31, 33, 22, 32, 33, 21, 22, 7, 37, 6, 6, 39, 5, 8, 9, 7, 34, 40, 21, 21, 40, 20, 34, 21, 33, 41, 20, 40, 36, 40, 35, 43, 40, 36, 41, 40, 43, 6, 37, 38, 9, 37, 7, 42, 41, 43, 36, 37, 43, 19, 20, 41, 9, 10, 47, 37, 9, 43, 9, 47, 43, 10, 11, 47, 41, 18, 19, 41, 42, 18, 46, 43, 47, 45, 42, 43, 45, 44, 42, 43, 46, 45, 12, 47, 11, 44, 18, 42, 13, 47, 12, 17, 18, 44, 17, 44, 45, 14, 47, 13, 46, 47, 14, 16, 17, 45, 46, 16, 45, 14, 15, 46, 15, 16, 46], "vertices": [1, 27, 189.92, -55.38, 1, 1, 27, 168.37, -72.02, 1, 1, 27, 145.83, -89.41, 1, 1, 27, 119.06, -95.23, 1, 1, 27, 99.77, -99.41, 1, 1, 27, 81.83, -93.08, 1, 1, 27, 59.69, -85.28, 1, 1, 27, 36.92, -77.25, 1, 1, 27, 29.23, -73.29, 1, 1, 27, 15.61, -57.04, 1, 1, 27, 2.88, -41.84, 1, 1, 27, -2.71, -23.98, 1, 1, 27, -7.88, -7.49, 1, 1, 27, -15.21, 15.91, 1, 1, 27, -21.85, 37.09, 1, 1, 27, -15.61, 66.59, 1, 1, 27, -6.67, 74.47, 1, 1, 27, 16.94, 80.9, 1, 1, 27, 43.94, 97.13, 1, 1, 27, 69.11, 102.42, 1, 1, 27, 90.57, 101.45, 1, 1, 27, 115.07, 100.35, 1, 1, 27, 141.35, 99.16, 1, 1, 27, 163.1, 98.18, 1, 1, 27, 182.97, 97.28, 1, 1, 27, 198.51, 89.29, 1, 1, 27, 211.19, 71.67, 1, 1, 27, 223.03, 55.22, 1, 1, 27, 217.49, 16.77, 1, 1, 27, 209.08, -9.14, 1, 1, 27, 201.16, -33.52, 1, 1, 27, 160.13, 89.13, 1, 1, 27, 154.14, 73.32, 1, 1, 27, 143.35, 59.97, 1, 1, 27, 135.18, 52.41, 1, 1, 27, 120.5, 7.91, 1, 1, 27, 123.01, -8.7, 1, 1, 27, 121.3, -34.2, 1, 1, 27, 114.52, -56.38, 1, 1, 27, 106.87, -68.49, 1, 1, 27, 90.18, 36.7, 1, 1, 27, 67.69, 53.97, 1, 1, 27, 52.81, 54.14, 1, 1, 27, 49.62, 36.17, 1, 1, 27, 35.86, 67.13, 1, 1, 27, 26.65, 55.47, 1, 1, 27, 23.25, 43.31, 1, 1, 27, 19.46, 21.38, 1], "hull": 31, "edges": [0, 60, 14, 16, 28, 30, 48, 50, 54, 56, 34, 36, 36, 38, 30, 32, 32, 34, 46, 48, 44, 46, 42, 44, 38, 40, 40, 42, 26, 28, 24, 26, 20, 22, 22, 24, 16, 18, 18, 20, 8, 10, 10, 12, 12, 14, 4, 6, 6, 8, 0, 2, 2, 4, 56, 58, 58, 60, 50, 52, 52, 54, 62, 64, 64, 66, 66, 68, 70, 72, 72, 74, 74, 76, 76, 78, 80, 82, 82, 84, 84, 86, 88, 90, 90, 92, 92, 94], "width": 173, "height": 186}}, "t2": {"t2": {"type": "mesh", "uvs": [0.46301, 0.06224, 0.4867, 0.07583, 0.502, 0.10371, 0.50173, 0.11717, 0.56748, 0.11524, 0.63322, 0.11331, 0.71369, 0.13674, 0.79517, 0.20549, 0.80308, 0.17203, 0.81006, 0.15073, 0.82107, 0.11712, 0.85871, 0.14725, 0.85879, 0.17997, 0.85891, 0.2247, 0.88569, 0.22074, 0.93095, 0.22096, 0.92748, 0.2385, 0.90005, 0.26156, 0.87011, 0.26155, 0.83304, 0.26154, 0.79257, 0.27527, 0.77058, 0.32565, 0.78962, 0.34578, 0.85504, 0.34632, 0.85466, 0.38197, 0.87895, 0.40126, 0.90753, 0.39916, 0.93404, 0.39721, 0.98913, 0.39316, 1, 0.39412, 1, 0.40633, 0.99251, 0.42542, 0.94569, 0.45201, 0.90225, 0.47668, 0.91614, 0.48035, 0.93095, 0.51923, 0.90013, 0.54505, 0.84264, 0.54536, 0.8161, 0.55269, 0.84708, 0.60147, 0.87806, 0.65024, 0.87073, 0.67552, 0.94607, 0.68308, 0.90046, 0.72159, 0.91151, 0.74765, 0.95272, 0.73069, 0.98113, 0.71899, 0.98039, 0.77653, 0.91848, 0.78658, 0.87953, 0.79471, 0.90425, 0.81676, 0.93184, 0.84136, 0.89958, 0.84042, 0.85478, 0.83912, 0.81826, 0.83805, 0.77773, 0.83687, 0.77251, 0.84963, 0.73424, 0.93315, 0.70609, 0.9501, 0.65639, 0.95311, 0.64621, 0.87284, 0.62142, 0.81258, 0.5952, 0.77509, 0.52438, 0.74666, 0.45357, 0.71823, 0.43255, 0.71476, 0.35582, 0.72909, 0.2791, 0.74342, 0.22619, 0.78081, 0.21824, 0.8253, 0.23038, 0.85954, 0.25708, 0.905, 0.23793, 0.96732, 0.22395, 0.9963, 0.16383, 0.99992, 0.15547, 0.97731, 0.19873, 0.92857, 0.21078, 0.88147, 0.1907, 0.86515, 0.17869, 0.89119, 0.12989, 0.92113, 0.12021, 0.90905, 0.14238, 0.87774, 0.15705, 0.85454, 0.16781, 0.82305, 0.1639, 0.81338, 0.15607, 0.82885, 0.12738, 0.86006, 0.10017, 0.88402, 0.03789, 0.88399, 0.00189, 0.86269, 0.00211, 0.83996, 0.05161, 0.85034, 0.10976, 0.8071, 0.06034, 0.77492, 0.06689, 0.7156, 0.10526, 0.69892, 0.13597, 0.65678, 0.12246, 0.65555, 0.12367, 0.63736, 0.14563, 0.62141, 0.13573, 0.61589, 0.11836, 0.61733, 0.09845, 0.61364, 0.08864, 0.56023, 0.07884, 0.50681, 0.06456, 0.50315, 0.02914, 0.47291, 0.00404, 0.45149, 0.0096, 0.44168, 0.04543, 0.44808, 0.07235, 0.45288, 0.07647, 0.44529, 0.15048, 0.42801, 0.15583, 0.42043, 0.17407, 0.37606, 0.19571, 0.32343, 0.24427, 0.31442, 0.25521, 0.30777, 0.24058, 0.27938, 0.2296, 0.25808, 0.27583, 0.26288, 0.32308, 0.26779, 0.3539, 0.23001, 0.38697, 0.23904, 0.43117, 0.18547, 0.47537, 0.1319, 0.44897, 0.11929, 0.44059, 0.0919, 0.4476, 0.05763, 0.59801, 0.25465, 0.77246, 0.70679, 0.29808, 0.45873, 0.61385, 0.53962, 0.35458, 0.36057, 0.22137, 0.59124, 0.41163, 0.56095, 0.56101, 0.3752, 0.78101, 0.43831, 0.44782, 0.45897, 0.66927, 0.65415, 0.51732, 0.65585, 0.69965, 0.39176, 0.34008, 0.66783, 0.4664, 0.283, 0.68397, 0.32474, 0.72479, 0.80236, 0.74958, 0.83018, 0.75825, 0.85695, 0.74214, 0.89054, 0.71426, 0.92728], "triangles": [128, 129, 0, 128, 0, 1, 2, 128, 1, 2, 127, 128, 2, 126, 127, 3, 126, 2, 9, 10, 11, 9, 11, 12, 8, 9, 12, 16, 14, 15, 130, 4, 5, 130, 5, 6, 130, 6, 7, 7, 12, 13, 12, 7, 8, 19, 7, 13, 18, 13, 14, 17, 18, 14, 19, 13, 18, 16, 17, 14, 20, 130, 7, 20, 7, 19, 21, 145, 20, 4, 126, 3, 130, 144, 125, 124, 125, 144, 145, 130, 20, 124, 122, 123, 134, 124, 144, 134, 122, 124, 126, 4, 130, 137, 130, 145, 126, 130, 125, 137, 144, 130, 24, 22, 23, 142, 145, 21, 142, 21, 22, 137, 145, 142, 28, 29, 30, 31, 28, 30, 138, 142, 22, 138, 22, 24, 31, 32, 27, 31, 27, 28, 137, 139, 134, 137, 134, 144, 33, 25, 26, 26, 27, 32, 33, 26, 32, 133, 137, 142, 133, 142, 138, 139, 137, 133, 25, 33, 138, 35, 36, 33, 35, 33, 34, 138, 33, 37, 25, 138, 24, 36, 37, 33, 38, 133, 138, 37, 38, 138, 140, 133, 38, 140, 38, 39, 141, 133, 140, 41, 39, 40, 131, 140, 39, 41, 131, 39, 43, 41, 42, 47, 45, 46, 47, 48, 44, 47, 44, 45, 43, 131, 41, 49, 43, 44, 49, 44, 48, 49, 131, 43, 131, 146, 140, 55, 146, 131, 55, 147, 146, 55, 131, 54, 49, 54, 131, 53, 54, 49, 53, 49, 50, 52, 53, 50, 52, 50, 51, 56, 147, 55, 148, 147, 56, 119, 120, 121, 118, 119, 121, 118, 122, 134, 122, 118, 121, 134, 117, 118, 132, 117, 134, 132, 134, 139, 110, 108, 109, 107, 108, 110, 111, 107, 110, 106, 107, 111, 113, 111, 112, 105, 111, 113, 106, 111, 105, 136, 132, 139, 136, 139, 133, 115, 117, 132, 117, 115, 116, 135, 132, 136, 132, 114, 115, 132, 113, 114, 135, 113, 132, 135, 105, 113, 141, 136, 133, 135, 104, 105, 104, 102, 103, 135, 101, 104, 101, 102, 104, 100, 101, 135, 97, 99, 100, 98, 99, 97, 143, 135, 136, 65, 143, 136, 141, 65, 136, 64, 65, 141, 66, 143, 65, 67, 135, 143, 67, 143, 66, 97, 100, 135, 67, 97, 135, 68, 96, 97, 63, 141, 140, 64, 141, 63, 62, 63, 140, 146, 62, 140, 67, 68, 97, 85, 93, 96, 94, 95, 96, 93, 94, 96, 61, 62, 146, 68, 85, 96, 69, 84, 85, 68, 69, 85, 86, 93, 85, 87, 93, 86, 92, 93, 87, 90, 91, 92, 78, 84, 69, 78, 69, 70, 83, 84, 78, 60, 61, 146, 60, 146, 147, 60, 147, 148, 77, 78, 70, 89, 90, 92, 88, 92, 87, 89, 92, 88, 149, 60, 148, 79, 83, 78, 82, 83, 79, 77, 70, 71, 80, 81, 82, 80, 82, 79, 150, 60, 149, 76, 77, 71, 57, 150, 149, 56, 57, 149, 56, 149, 148, 150, 59, 60, 58, 150, 57, 58, 59, 150, 72, 76, 71, 75, 76, 72, 73, 75, 72, 74, 75, 73], "vertices": [3, 31, 219.44, 172.73, 0, 33, 104.87, 113.32, 0.35072, 34, 3.4, 117.77, 0.64927, 3, 31, 222.48, 159.93, 0.00052, 33, 104.44, 100.16, 0.3533, 34, 6.24, 104.92, 0.64618, 3, 31, 217.44, 144.01, 0.00306, 33, 95.39, 86.13, 0.36067, 34, 0.95, 89.08, 0.63627, 3, 31, 212.41, 138.71, 0.00522, 33, 89.14, 82.34, 0.36484, 34, -4.16, 83.86, 0.62994, 3, 31, 235.41, 119.05, 0.00129, 33, 106.15, 57.32, 0.18792, 34, 18.53, 63.84, 0.81079, 2, 33, 123.17, 32.29, 0.0263, 34, 41.22, 43.82, 0.9737, 1, 34, 59.37, 9.14, 1, 3, 32, 192.95, -60.64, 0.02062, 33, 120.56, -57.42, 0.03922, 34, 60.95, -43.74, 0.94016, 3, 32, 210.83, -55.77, 0.00376, 33, 137.86, -50.81, 0.00856, 34, 76.07, -33.04, 0.98768, 3, 32, 222.61, -53.47, 0.00059, 33, 149.36, -47.35, 0.0019, 34, 86.35, -26.85, 0.99751, 1, 34, 102.57, -17.07, 1, 2, 33, 162.9, -65.28, 0.00071, 34, 103.91, -40.85, 0.99929, 3, 32, 218.43, -80.62, 0.00281, 33, 147.89, -74.79, 0.0069, 34, 91.73, -53.79, 0.99029, 3, 32, 196.73, -91.53, 0.01049, 33, 127.37, -87.78, 0.01994, 34, 75.07, -71.47, 0.96957, 3, 32, 204.17, -101.59, 0.00345, 33, 135.76, -97.06, 0.00729, 34, 85.5, -78.37, 0.98926, 1, 34, 100.54, -92.77, 1, 3, 32, 204.13, -123.09, 0.00014, 33, 137.85, -118.46, 0.00063, 34, 92.84, -98.59, 0.99923, 3, 32, 187.29, -117.41, 0.0027, 33, 120.53, -114.47, 0.00587, 34, 75.07, -99.01, 0.99143, 3, 32, 181.14, -105.09, 0.0092, 33, 113.19, -102.81, 0.01713, 34, 65.07, -89.54, 0.97367, 3, 32, 173.52, -89.83, 0.033, 33, 104.1, -88.39, 0.05374, 34, 52.68, -77.82, 0.91326, 4, 30, 240.94, -188.19, 7e-05, 32, 158.52, -76.52, 0.12138, 33, 87.87, -76.61, 0.16286, 34, 34.04, -70.44, 0.71569, 4, 30, 212.98, -179.89, 0.00288, 32, 129.53, -79.71, 0.43864, 33, 59.33, -82.65, 0.29371, 34, 7.89, -83.37, 0.26477, 4, 30, 202.64, -189.34, 0.00685, 32, 123.67, -92.43, 0.62845, 33, 54.76, -95.89, 0.24504, 34, 6.74, -97.33, 0.11966, 4, 30, 204.32, -219.39, 0.00952, 32, 136.87, -119.47, 0.74739, 33, 70.55, -121.5, 0.1943, 34, 28.4, -118.22, 0.04879, 5, 30, 184.99, -220.48, 0.01256, 31, 234.92, -76.99, 0.0001, 32, 119.47, -127.97, 0.78276, 33, 54.08, -131.67, 0.17205, 34, 14.97, -132.16, 0.03253, 5, 30, 175.27, -232.32, 0.01532, 31, 236.09, -92.26, 0.00038, 32, 115.1, -142.65, 0.83255, 33, 51.18, -146.71, 0.13991, 34, 15.89, -147.45, 0.01185, 5, 30, 177.27, -245.36, 0.01334, 31, 246.55, -100.3, 0.00012, 32, 122, -153.9, 0.85409, 33, 59.16, -157.23, 0.1283, 34, 26.22, -155.66, 0.00415, 4, 30, 179.12, -257.46, 0.01096, 32, 128.4, -164.33, 0.86326, 33, 66.56, -166.98, 0.12448, 34, 35.81, -163.27, 0.0013, 4, 30, 182.97, -282.6, 0.00777, 32, 141.7, -186.01, 0.86923, 33, 81.93, -187.24, 0.123, 34, 55.73, -179.08, 0, 3, 30, 182.78, -287.63, 0.0077, 32, 143.47, -190.72, 0.86932, 33, 84.16, -191.75, 0.12298, 3, 30, 176.17, -288.06, 0.00775, 32, 137.54, -193.69, 0.8693, 33, 78.55, -195.28, 0.12295, 3, 30, 165.6, -285.3, 0.00825, 32, 126.73, -195.24, 0.86894, 33, 67.95, -197.9, 0.12281, 5, 30, 149.78, -264.75, 0.01375, 31, 240.1, -133.32, 0.00047, 32, 104.19, -182.43, 0.86469, 33, 44.25, -187.38, 0.12051, 34, 19.26, -188.57, 0.00059, 5, 30, 135.11, -245.69, 0.02952, 31, 216.31, -129.69, 0.00446, 32, 83.27, -170.55, 0.85995, 33, 22.26, -177.62, 0.10516, 34, -4.47, -184.57, 0.00092, 5, 30, 133.54, -252.2, 0.03241, 31, 219.68, -135.48, 0.00522, 32, 84.34, -177.16, 0.86133, 33, 23.98, -184.09, 0.10059, 34, -1.19, -190.41, 0.00046, 4, 30, 112.91, -260.38, 0.03915, 31, 210.43, -155.64, 0.00728, 32, 68.51, -192.69, 0.86249, 33, 9.75, -201.11, 0.09108, 4, 30, 98, -247.15, 0.04476, 31, 190.51, -156.39, 0.0099, 32, 49.63, -186.28, 0.85911, 33, -9.67, -196.59, 0.08623, 4, 30, 96.1, -220.77, 0.0718, 31, 170.9, -138.65, 0.02427, 32, 37.65, -162.7, 0.83494, 33, -23.91, -174.31, 0.06899, 4, 30, 91.33, -208.85, 0.11269, 31, 159.21, -133.34, 0.04566, 32, 28.63, -153.57, 0.7965, 33, -33.79, -166.11, 0.04515, 4, 30, 65.83, -224.8, 0.2011, 31, 151.82, -162.49, 0.08019, 32, 11.31, -178.16, 0.70688, 33, -48.6, -192.29, 0.01183, 4, 30, 40.34, -240.76, 0.2437, 31, 144.43, -191.64, 0.09043, 32, -6, -202.75, 0.66234, 33, -63.4, -218.47, 0.00353, 4, 30, 26.42, -238.29, 0.26205, 31, 132.67, -199.48, 0.09351, 32, -19.79, -205.87, 0.64297, 33, -76.81, -222.94, 0.00147, 3, 30, 24.59, -273.14, 0.27494, 31, 155.44, -225.93, 0.09348, 32, -7.96, -238.7, 0.63158, 4, 30, 2.35, -253.57, 0.28208, 31, 125.85, -227.16, 0.09509, 32, -36.04, -229.29, 0.62274, 33, -90.68, -247.84, 9e-05, 3, 30, -11.44, -259.57, 0.29243, 31, 120.03, -241.03, 0.0949, 32, -46.43, -240.16, 0.61267, 3, 30, -1.01, -277.88, 0.29451, 31, 140.23, -247.05, 0.09272, 32, -29.71, -253, 0.61276, 3, 30, 6.19, -290.51, 0.29462, 31, 154.15, -251.2, 0.09242, 32, -18.19, -261.85, 0.61296, 3, 30, -25.01, -292.21, 0.29474, 31, 132.79, -274, 0.09277, 32, -46.29, -275.52, 0.61249, 3, 30, -32.32, -264.15, 0.296, 31, 108.11, -258.78, 0.095, 32, -63.9, -252.48, 0.609, 3, 30, -37.9, -246.56, 0.30299, 31, 91.92, -249.93, 0.09869, 32, -75.87, -238.44, 0.59831, 3, 30, -49.1, -258.69, 0.30736, 31, 92.21, -266.43, 0.09914, 32, -81.49, -253.96, 0.59349, 3, 30, -61.6, -272.23, 0.30788, 31, 92.54, -284.86, 0.09893, 32, -87.76, -271.28, 0.59319, 3, 30, -62.06, -257.39, 0.30786, 31, 81.94, -274.46, 0.09917, 32, -93.94, -257.78, 0.59297, 3, 30, -62.7, -236.78, 0.31045, 31, 67.23, -260.01, 0.10112, 32, -102.52, -239.04, 0.58844, 3, 30, -63.23, -219.98, 0.31703, 31, 55.24, -248.23, 0.10458, 32, -109.52, -223.75, 0.57839, 3, 30, -63.81, -201.33, 0.33274, 31, 41.93, -235.16, 0.11086, 32, -117.28, -206.79, 0.5564, 3, 30, -70.88, -199.39, 0.34043, 31, 35.48, -238.64, 0.11268, 32, -124.55, -207.74, 0.54689, 1, 30, -117.28, -184.79, 1, 1, 30, -127.32, -172.47, 1, 1, 30, -130.44, -149.77, 1, 1, 30, -87.26, -142.24, 1, 1, 30, -55.35, -128.72, 1, 1, 30, -35.83, -115.35, 1, 1, 30, -22.55, -81.84, 1, 1, 30, -9.28, -48.32, 1, 1, 30, -8.03, -38.55, 1, 1, 30, -18.1, -3.84, 1, 1, 30, -28.18, 30.87, 1, 1, 30, -50.03, 53.83, 1, 1, 30, -74.38, 55.89, 1, 1, 30, -92.56, 49.1, 1, 1, 30, -116.39, 35.23, 1, 1, 30, -150.74, 41.81, 1, 1, 30, -166.86, 47.2, 1, 1, 30, -170.63, 74.66, 1, 1, 30, -158.63, 79.31, 1, 1, 30, -130.92, 61.18, 1, 1, 30, -105.04, 57.32, 1, 1, 30, -96.8, 67.12, 1, 1, 30, -111.27, 71.7, 1, 1, 30, -128.96, 93.04, 1, 1, 30, -122.71, 97.91, 1, 1, 30, -105.08, 88.85, 1, 1, 30, -92.06, 82.94, 1, 1, 30, -74.67, 79.12, 1, 1, 30, -69.55, 81.26, 1, 1, 30, -78.17, 84.3, 1, 1, 30, -95.95, 96.36, 1, 1, 30, -109.75, 108, 1, 1, 30, -111.61, 136.59, 1, 1, 30, -101.15, 153.87, 1, 1, 30, -88.82, 154.58, 1, 1, 30, -92.96, 131.49, 1, 1, 30, -67.78, 106.33, 1, 1, 30, -51.83, 130.16, 1, 3, 30, -19.5, 129.26, 0.99546, 31, -154.62, 34.34, 0.00454, 33, -292.42, 78.26, 0, 3, 30, -9.3, 112.24, 0.98101, 31, -135.48, 29.09, 0.01869, 33, -275.34, 68.16, 0.0003, 3, 30, 14.46, 99.64, 0.89131, 31, -109.61, 36.41, 0.10479, 33, -248.45, 68.41, 0.0039, 3, 30, 14.72, 105.89, 0.87463, 31, -113.74, 41.1, 0.12068, 33, -251.2, 74.02, 0.00468, 3, 30, 24.61, 105.98, 0.85652, 31, -106.65, 48.01, 0.13784, 33, -242.55, 78.82, 0.00564, 3, 30, 33.91, 96.47, 0.76243, 31, -93.36, 47.56, 0.22633, 33, -229.84, 74.89, 0.01124, 3, 30, 36.6, 101.21, 0.69837, 31, -94.69, 52.85, 0.28634, 33, -229.73, 80.34, 0.01529, 3, 30, 35.31, 109.13, 0.65486, 31, -101.1, 57.68, 0.32709, 33, -234.65, 86.69, 0.01805, 3, 30, 36.7, 118.4, 0.6298, 31, -106.5, 65.34, 0.35045, 33, -237.84, 95.5, 0.01975, 3, 30, 65.35, 124.8, 0.51423, 31, -90.23, 89.77, 0.4547, 33, -215.71, 114.78, 0.03107, 3, 30, 94, 131.2, 0.36734, 31, -73.96, 114.2, 0.58317, 33, -193.58, 134.07, 0.04949, 3, 30, 95.55, 137.88, 0.34927, 31, -77.45, 120.1, 0.59953, 33, -195.4, 140.68, 0.0512, 3, 30, 110.87, 155.21, 0.32481, 31, -78.37, 143.21, 0.6234, 33, -190.2, 163.22, 0.05179, 3, 30, 121.72, 167.49, 0.32222, 31, -79.02, 159.59, 0.62688, 33, -186.52, 179.19, 0.0509, 3, 30, 127.2, 165.29, 0.3222, 31, -73.54, 161.79, 0.62692, 33, -180.65, 179.87, 0.05088, 3, 30, 124.81, 148.62, 0.32285, 31, -63.73, 148.09, 0.62472, 33, -174.8, 164.07, 0.05244, 4, 30, 123.02, 136.09, 0.31801, 31, -56.37, 137.8, 0.62476, 32, -75.9, 176.68, 2e-05, 33, -170.4, 152.21, 0.05721, 4, 30, 127.26, 134.47, 0.31275, 31, -52.18, 139.56, 0.62755, 32, -71.36, 176.83, 9e-05, 33, -165.9, 152.8, 0.05961, 4, 30, 138.85, 101.11, 0.20412, 31, -20.75, 123.47, 0.67976, 32, -47.75, 150.57, 0.00564, 33, -139.81, 129.01, 0.11048, 4, 30, 143.12, 98.92, 0.17447, 31, -16.15, 124.84, 0.6904, 32, -42.96, 150.21, 0.00823, 33, -135.01, 129.12, 0.12691, 4, 30, 167.71, 92.13, 0.07543, 31, 6.31, 136.93, 0.69717, 32, -17.66, 153.48, 0.02208, 33, -110.16, 134.87, 0.20532, 4, 30, 196.88, 84.06, 0.03058, 31, 32.96, 151.27, 0.66194, 32, 12.35, 157.36, 0.03451, 33, -80.68, 141.69, 0.27298, 4, 30, 203.22, 62.1, 0.01349, 31, 52.73, 139.78, 0.60008, 32, 26.71, 139.57, 0.04834, 33, -64.63, 125.41, 0.33808, 5, 30, 207.15, 57.31, 0.00677, 31, 58.89, 139.04, 0.56145, 32, 32.2, 136.68, 0.05365, 33, -58.89, 123.08, 0.37798, 34, -157.66, 86.6, 0.00014, 5, 30, 222.09, 65.04, 0.00069, 31, 64.34, 154.96, 0.52345, 32, 42.97, 149.6, 0.05303, 33, -49.44, 136.99, 0.42283, 34, -151.96, 102.42, 1e-05, 3, 31, 68.43, 166.89, 0.51934, 32, 51.06, 159.28, 0.05246, 33, -42.35, 147.43, 0.42821, 5, 30, 232.09, 49.44, 0.00013, 31, 82.35, 150.6, 0.50333, 32, 58.24, 139.1, 0.05594, 33, -33.21, 128.05, 0.43938, 34, -134.02, 97.79, 0.00122, 4, 31, 96.57, 133.95, 0.40624, 32, 65.57, 118.46, 0.06458, 33, -23.87, 108.24, 0.51759, 34, -120.06, 80.92, 0.01159, 4, 31, 120.88, 139.49, 0.2883, 32, 90.26, 114.96, 0.05256, 33, 1.04, 107.19, 0.62105, 34, -95.67, 86.08, 0.03808, 4, 31, 128.78, 125.6, 0.22458, 32, 92.68, 99.16, 0.04326, 33, 5.01, 91.7, 0.66401, 34, -87.99, 72.06, 0.06815, 4, 31, 163.42, 133.3, 0.06697, 32, 127.79, 93.98, 0.00314, 33, 40.45, 90.02, 0.64371, 34, -53.23, 79.22, 0.28618, 3, 31, 198.07, 141.01, 0.00919, 33, 75.9, 88.33, 0.40208, 34, -18.47, 86.38, 0.58873, 3, 31, 193.74, 154.26, 0.00312, 33, 75.22, 102.26, 0.37001, 34, -22.58, 99.7, 0.62687, 3, 31, 200.95, 167.83, 0.00065, 33, 85.75, 113.45, 0.3557, 34, -15.16, 113.15, 0.64365, 2, 33, 103.21, 120.65, 0.35024, 34, -0.03, 124.46, 0.64976, 3, 31, 194.62, 53.75, 0.00094, 33, 49.61, 5.06, 0.99666, 34, -23.29, -0.81, 0.0024, 4, 30, 6.52, -194.29, 0.28396, 31, 87.87, -181.45, 0.10949, 32, -55.19, -173.03, 0.60597, 33, -115.28, -193.75, 0.00059, 4, 30, 126.65, 32.27, 0.06109, 31, 18.03, 65.29, 0.7967, 32, -32.3, 82.38, 0.03021, 33, -117.71, 62.68, 0.112, 4, 30, 92.32, -115.55, 0.11687, 31, 95.42, -65.24, 0.15771, 32, -6.63, -67.18, 0.72276, 33, -77.4, -83.62, 0.00266, 5, 30, 181.53, 9.82, 0.00091, 31, 73.21, 87.02, 0.50299, 32, 26.99, 82.97, 0.14583, 33, -58.76, 69.12, 0.34973, 34, -144.16, 34.36, 0.00054, 3, 30, 52.54, 62.77, 0.71088, 31, -56.6, 36.1, 0.27496, 33, -197.4, 54.16, 0.01417, 3, 30, 74.68, -23.48, 0.1076, 31, 19.03, -10.92, 0.88863, 32, -58.59, 10.84, 0.00377, 3, 30, 179.82, -85.45, 0.0002, 32, 62.36, -5.51, 0.99667, 33, -14.84, -15.45, 0.00312, 5, 30, 152.25, -188.68, 0.0284, 31, 189.28, -76.64, 0.00556, 32, 76.96, -111.35, 0.81483, 33, 10.14, -119.33, 0.13494, 34, -30.67, -131.11, 0.01626, 3, 31, 68.72, 18.65, 0.6061, 32, -1.62, 20.72, 0.36211, 33, -81.09, 4.34, 0.03179, 4, 30, 31.94, -145.06, 0.27866, 31, 72.2, -128.31, 0.15494, 32, -50.85, -117.79, 0.56566, 33, -116.41, -138.35, 0.00074, 3, 30, 26.44, -75.37, 0.47933, 31, 20.04, -81.75, 0.27482, 32, -82.94, -55.68, 0.24585, 5, 30, 175.02, -149.68, 0.01039, 31, 178.77, -32.72, 0.00027, 32, 82.83, -66.57, 0.71185, 33, 11.56, -74.19, 0.23555, 34, -40.48, -87.02, 0.04194, 1, 30, 14.61, 5.56, 1, 4, 31, 139.59, 83.31, 0.11245, 32, 87.67, 55.8, 0.05477, 33, 4.3, 48.07, 0.78229, 34, -77.84, 29.61, 0.05049, 4, 30, 210.86, -140.1, 0.0009, 32, 112.16, -43.85, 0.35039, 33, 38.5, -48.68, 0.48871, 34, -20.71, -55.63, 0.16, 4, 30, -46.7, -175.81, 0.54421, 31, 36.64, -204.89, 0.0738, 32, -111.41, -176.63, 0.38187, 33, -170.87, -202.88, 0.00012, 4, 30, -61.03, -188.17, 0.44103, 31, 34.84, -223.73, 0.09377, 32, -119.82, -193.58, 0.46515, 33, -177.57, -220.58, 5e-05, 4, 30, -75.27, -193.1, 0.48527, 31, 27.96, -237.14, 0.08734, 32, -131.04, -203.65, 0.42737, 33, -187.74, -231.7, 2e-05, 4, 30, -93.96, -186.9, 0.73318, 31, 10.17, -245.58, 0.04528, 32, -150.67, -205.18, 0.22153, 33, -207.12, -235.16, 1e-05, 4, 30, -114.7, -175.41, 0.91607, 31, -12.77, -251.62, 0.01424, 32, -174.25, -202.63, 0.06969, 33, -230.84, -234.95, 0], "hull": 130, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 20, 22, 26, 28, 28, 30, 30, 32, 32, 34, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 54, 56, 56, 58, 58, 60, 60, 62, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 92, 94, 94, 96, 96, 98, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 208, 210, 210, 212, 216, 218, 222, 224, 224, 226, 226, 228, 232, 234, 234, 236, 244, 246, 246, 248, 248, 250, 250, 252, 252, 254, 254, 256, 256, 258, 258, 0, 218, 220, 220, 222, 212, 214, 214, 216, 228, 230, 230, 232, 240, 242, 242, 244, 236, 238, 238, 240, 16, 18, 18, 20, 22, 24, 24, 26, 34, 36, 36, 38, 62, 64, 64, 66, 50, 52, 52, 54, 88, 90, 90, 92, 102, 104, 104, 106, 106, 108, 108, 110, 98, 100, 100, 102, 292, 294, 294, 296, 296, 298, 298, 300, 300, 116], "width": 358, "height": 423}}, "zy": {"zy": {"type": "mesh", "uvs": [0.98807, 0.0009, 0.98838, 0.19494, 0.94586, 0.29545, 0.73642, 0.79054, 0.54119, 0.9592, 0.35489, 0.95992, 0.1201, 0.96083, 0.07041, 0.85794, 0.00707, 0.72681, 0.13386, 0.27478, 0.36856, 0.07928, 0.61116, 0.10584, 0.88506, 0.13583, 0.22779, 0.59899, 0.40196, 0.40993, 0.63497, 0.38629, 0.8115, 0.37842, 0.32987, 0.48818, 0.35489, 0.67776, 0.44668, 0.83531, 0.63027, 0.76442, 0.68911, 0.55566], "triangles": [12, 0, 1, 2, 12, 1, 16, 11, 12, 16, 12, 2, 15, 11, 16, 14, 10, 11, 14, 11, 15, 17, 9, 10, 14, 17, 10, 21, 15, 16, 13, 9, 17, 18, 17, 14, 13, 17, 18, 8, 9, 13, 20, 14, 15, 20, 15, 21, 19, 18, 14, 3, 21, 16, 20, 21, 3, 3, 16, 2, 20, 19, 14, 7, 8, 13, 4, 19, 20, 4, 20, 3, 5, 18, 19, 5, 19, 4, 6, 7, 13, 5, 6, 13, 5, 13, 18], "vertices": [1, 29, 78.32, 12.32, 1, 1, 29, 75.1, 3.37, 1, 1, 29, 70.14, -0.07, 1, 1, 29, 45.71, -16.99, 1, 1, 29, 27.84, -19.29, 1, 1, 29, 13.47, -14.1, 1, 1, 29, -4.64, -7.57, 1, 1, 29, -6.75, -1.43, 1, 1, 29, -9.43, 6.38, 1, 1, 29, 7.91, 23.64, 1, 1, 29, 29.27, 26.07, 1, 1, 29, 47.52, 18.05, 1, 1, 29, 68.12, 8.99, 1, 1, 29, 9.72, 6.08, 1, 1, 29, 26.3, 9.91, 1, 1, 29, 44.66, 4.46, 1, 1, 29, 58.39, -0.12, 1, 1, 29, 19.44, 8.32, 1, 1, 29, 18.19, -1.11, 1, 1, 29, 22.63, -10.94, 1, 1, 29, 37.96, -12.82, 1, 1, 29, 45.99, -4.85, 1], "hull": 13, "edges": [0, 24, 0, 2, 6, 8, 16, 18, 18, 20, 20, 22, 22, 24, 12, 14, 14, 16, 14, 26, 28, 30, 30, 32, 2, 4, 4, 6, 32, 4, 26, 34, 34, 28, 34, 36, 36, 38, 38, 40, 40, 42, 42, 30, 8, 10, 10, 12], "width": 63, "height": 38}}}}], "animations": {"animation": {"bones": {"st2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "ys5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "ys6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "bozi": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "t1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 1.2, "curve": "stepped"}, {"time": 1.8667, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "angle": 1.2, "curve": "stepped"}, {"time": 5.2667, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}], "shear": [{"curve": 0.25, "c3": 0.75}, {"time": 0.9, "y": 1.2, "curve": "stepped"}, {"time": 1.8667, "y": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "y": 1.2, "curve": "stepped"}, {"time": 5.2667, "y": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "st12": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "st": {"translate": [{"x": -0.05, "y": -1, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 1.3, "x": -0.78, "y": -15.97, "curve": 0.25, "c3": 0.75}, {"time": 3.0667, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 3.3667, "x": -0.05, "y": -1, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 4.7, "x": -0.78, "y": -15.97, "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 6.6667, "x": -0.05, "y": -1}], "shear": [{"curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "x": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "yy": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 2.81, "y": 4.41, "curve": "stepped"}, {"time": 1.5, "x": 2.81, "y": 4.41, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "x": 2.81, "y": 4.41, "curve": "stepped"}, {"time": 4.9, "x": 2.81, "y": 4.41, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 0.974, "curve": "stepped"}, {"time": 1.5, "x": 0.974, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "x": 0.974, "curve": "stepped"}, {"time": 4.9, "x": 0.974, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "zy": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": 3.8, "y": 6.08, "curve": "stepped"}, {"time": 1.7, "x": 3.8, "y": 6.08, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "x": 3.8, "y": 6.08, "curve": "stepped"}, {"time": 5.0667, "x": 3.8, "y": 6.08, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "t3": {"shear": [{"curve": 0.25, "c3": 0.75}, {"time": 0.9333, "y": -1.2, "curve": "stepped"}, {"time": 1.5, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "y": -1.2, "curve": "stepped"}, {"time": 4.8667, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "zs1": {"rotate": [{"angle": 0.47, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 3.6, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.3667, "angle": 0.47, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.2, "angle": 3.6, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 6.6667, "angle": 0.47}]}, "zs2": {"rotate": [{"angle": -0.22, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": -1.2, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 3.3667, "angle": -0.22, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.7667, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "angle": -1.2, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 6.6667, "angle": -0.22}]}, "st13": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}], "scale": [{"x": 1.02, "y": 1.06}]}, "st14": {"rotate": [{"angle": -0.51, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": -0.51, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -0.51}]}, "st15": {"rotate": [{"angle": -0.47, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.2333, "angle": -0.51, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 3.3667, "angle": -0.47, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 3.6, "angle": -0.51, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 6.6667, "angle": -0.47}]}, "st16": {"rotate": [{"angle": -0.39, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4667, "angle": -0.51, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3667, "angle": -0.39, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8333, "angle": -0.51, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "angle": -0.39}]}, "st17": {"rotate": [{"angle": -0.29, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.6667, "angle": -0.51, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 3.3667, "angle": -0.29, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 4.0667, "angle": -0.51, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 6.6667, "angle": -0.29}]}, "st18": {"rotate": [{"angle": -0.19, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.9, "angle": -0.51, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.3667, "angle": -0.19, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.2667, "angle": -0.51, "curve": 0.25, "c3": 0.75}, {"time": 5.9667, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6.6667, "angle": -0.19}]}, "st19": {"rotate": [{"angle": -0.09, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.1333, "angle": -0.51, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 3.3667, "angle": -0.09, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.5, "angle": -0.51, "curve": 0.25, "c3": 0.75}, {"time": 6.2333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 6.6667, "angle": -0.09}]}, "zs4": {"translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.7667, "x": -0.8, "y": -1.84, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.5, "x": -1.6, "y": -3.67, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.1333, "x": -0.8, "y": -1.84, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.8667, "x": -1.6, "y": -3.67, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 1.06, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "x": 1.06, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "st5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 1.02, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": 1.02, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}], "scale": [{"x": 1.02}]}, "st6": {"rotate": [{"angle": 0.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": 1.02, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 3.3667, "angle": 0.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.5667, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "angle": 1.02, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 6.6667, "angle": 0.06}]}, "st7": {"rotate": [{"angle": 0.19, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": 1.02, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 3.3667, "angle": 0.19, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.7667, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "angle": 1.02, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 6.6667, "angle": 0.19}]}, "st8": {"rotate": [{"angle": 0.34, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 1.02, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 3.3667, "angle": 0.34, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.9333, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": 1.02, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 6.6667, "angle": 0.34}]}, "st9": {"rotate": [{"angle": 0.51, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "angle": 1.02, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3667, "angle": 0.51, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.1333, "curve": 0.25, "c3": 0.75}, {"time": 5.7667, "angle": 1.02, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "angle": 0.51}]}, "zs3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -6, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "angle": -6, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "zs5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 7.2, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": 7.2, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "zs6": {"rotate": [{"angle": -41.97, "curve": 0.349, "c2": 0.65, "c3": 0.683}, {"time": 0.2, "angle": -43.2, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": 2.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.5667, "angle": -43.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.0667, "angle": 2.4, "curve": 0.366, "c2": 0.46, "c3": 0.732, "c4": 0.91}, {"time": 6.6667, "angle": -41.97}]}, "zs10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -0.78, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "angle": -0.78, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3, "x": 0.94, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "x": 0.94, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "zs11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -14.4, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": -14.4, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.1, "x": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.4667, "x": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "zs8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -4.7, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -4.7, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": 0.638, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 0.638, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "zs9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -24.2, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -24.2, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}], "scale": [{"x": 0.76, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": 0.617, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "x": 0.76, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 0.617, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": 0.76}]}, "ys7": {"rotate": [{}, {"time": 0.7667, "angle": -10.8, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -18, "curve": "stepped"}, {"time": 1.7, "angle": -18, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "angle": -9.6}, {"time": 3.3667}, {"time": 4.1333, "angle": -10.8, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -18, "curve": "stepped"}, {"time": 5.0667, "angle": -18, "curve": 0.25, "c3": 0.75}, {"time": 5.7667, "angle": -9.6}, {"time": 6.6667}]}, "ys10": {"rotate": [{"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -10.8, "curve": "stepped"}, {"time": 1.4333, "angle": -10.8, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "curve": "stepped"}, {"time": 4.1333, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "angle": -10.8, "curve": "stepped"}, {"time": 4.8, "angle": -10.8, "curve": 0.25, "c3": 0.75}, {"time": 5.7667}]}, "ys9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -8.4, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "angle": -8.4, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "ys8": {"rotate": [{"angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 1.01, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": 1.01, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -0.35}]}, "ys11": {"rotate": [{"angle": -0.31, "curve": 0.354, "c2": 0.65, "c3": 0.689}, {"time": 0.1, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 1.01, "curve": 0.246, "c3": 0.72, "c4": 0.87}, {"time": 3.3667, "angle": -0.31, "curve": 0.354, "c2": 0.65, "c3": 0.689}, {"time": 3.5, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 1.01, "curve": 0.246, "c3": 0.72, "c4": 0.87}, {"time": 6.6667, "angle": -0.31}]}, "ys12": {"rotate": [{"angle": -0.23, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.2333, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": 1.01, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 3.3667, "angle": -0.23, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 3.6, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "angle": 1.01, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 6.6667, "angle": -0.23}]}, "ys13": {"rotate": [{"angle": -0.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.3333, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 1.01, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 3.3667, "angle": -0.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 3.7, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "angle": 1.01, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 6.6667, "angle": -0.13}]}, "ys14": {"rotate": [{"angle": -0.02, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4667, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": 1.01, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3667, "angle": -0.02, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8333, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "angle": 1.01, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "angle": -0.02}]}, "ys15": {"rotate": [{"angle": 0.11, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5667, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 1.01, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 3.3667, "angle": 0.11, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.9333, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": 1.01, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 6.6667, "angle": 0.11}]}, "ys16": {"rotate": [{"angle": 0.24, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.6667, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": 1.01, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 3.3667, "angle": 0.24, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 4.0667, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": 1.01, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 6.6667, "angle": 0.24}]}, "ys17": {"rotate": [{"angle": 0.38, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 0.8, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": 1.01, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 3.3667, "angle": 0.38, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 4.1667, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 5.8, "angle": 1.01, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 6.6667, "angle": 0.38}]}, "ys18": {"rotate": [{"angle": 0.51, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.9, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": 1.01, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.3667, "angle": 0.51, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.2667, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 5.9667, "angle": 1.01, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6.6667, "angle": 0.51}]}, "ys19": {"rotate": [{"angle": 0.64, "curve": 0.35, "c2": 0.39, "c3": 0.757}, {"time": 1, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": 1.01, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 3.3667, "angle": 0.64, "curve": 0.35, "c2": 0.39, "c3": 0.757}, {"time": 4.4, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "angle": 1.01, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 6.6667, "angle": 0.64}]}, "ys20": {"rotate": [{"angle": 0.76, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.1333, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "angle": 1.01, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 3.3667, "angle": 0.76, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.5, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 6.2333, "angle": 1.01, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 6.6667, "angle": 0.76}]}, "ys21": {"rotate": [{"angle": 0.87, "curve": 0.313, "c2": 0.26, "c3": 0.757}, {"time": 1.2333, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 1.01, "curve": 0.289, "c3": 0.628, "c4": 0.38}, {"time": 3.3667, "angle": 0.87, "curve": 0.313, "c2": 0.26, "c3": 0.757}, {"time": 4.6, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 6.3667, "angle": 1.01, "curve": 0.289, "c3": 0.628, "c4": 0.38}, {"time": 6.6667, "angle": 0.87}]}, "zs7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -2.53, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": -2.53, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}], "scale": [{"x": 1.14}]}, "zs13": {"rotate": [{"angle": -0.11, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -2.53, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3667, "angle": -0.11, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5333, "curve": 0.25, "c3": 0.75}, {"time": 5.0333, "angle": -2.53, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "angle": -0.11}]}, "zs14": {"rotate": [{"angle": -0.33, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -2.53, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.3667, "angle": -0.33, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.2, "angle": -2.53, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 6.6667, "angle": -0.33}]}, "zs15": {"rotate": [{"angle": -0.61, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": -2.53, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3667, "angle": -0.61, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8333, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "angle": -2.53, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "angle": -0.61}]}, "zs16": {"rotate": [{"angle": -0.93, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "angle": -2.53, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.3667, "angle": -0.93, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.9667, "curve": 0.25, "c3": 0.75}, {"time": 5.6, "angle": -2.53, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6.6667, "angle": -0.93}]}, "zs17": {"rotate": [{"angle": -1.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "angle": -2.53, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3667, "angle": -1.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.1333, "curve": 0.25, "c3": 0.75}, {"time": 5.7667, "angle": -2.53, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "angle": -1.27}]}, "zs18": {"rotate": [{"angle": -1.6, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": -2.53, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.3667, "angle": -1.6, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.2667, "curve": 0.25, "c3": 0.75}, {"time": 5.9667, "angle": -2.53, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6.6667, "angle": -1.6}]}, "st10": {"scale": [{"x": 1.02}], "shear": [{"curve": 0.25, "c3": 0.75}, {"time": 1.5, "y": 6, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "y": 6, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "ys1": {"translate": [{"x": -0.26, "y": -0.19, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -6.01, "y": -4.5, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3667, "x": -0.26, "y": -0.19, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5333, "curve": 0.25, "c3": 0.75}, {"time": 5.0333, "x": -6.01, "y": -4.5, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "x": -0.26, "y": -0.19}]}, "st1": {"translate": [{"x": -0.26, "y": -0.19, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667}, {"time": 1.6667, "x": -6.01, "y": -4.5, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3667, "x": -0.26, "y": -0.19, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5333}, {"time": 5.0333, "x": -6.01, "y": -4.5, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "x": -0.26, "y": -0.19}]}, "ys2": {"rotate": [{"angle": 0.16, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 1.2, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.3667, "angle": 0.16, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.2, "angle": 1.2, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 6.6667, "angle": 0.16}]}, "st3": {"rotate": [{"angle": 0.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7667, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3667, "angle": 0.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.1333, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 5.7667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "angle": 0.6}]}, "t9": {"rotate": [{"angle": -0.18, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": -2.19, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 3.3667, "angle": -0.18, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "angle": -2.19, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 6.6667, "angle": -0.18}]}, "t10": {"rotate": [{"angle": -0.53, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": -2.19, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3667, "angle": -0.53, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8333, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "angle": -2.19, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "angle": -0.53}]}, "t11": {"rotate": [{"angle": -0.95, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": -2.19, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 3.3667, "angle": -0.95, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 4.0667, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": -2.19, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 6.6667, "angle": -0.95}]}, "t12": {"rotate": [{"angle": -1.38, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": -2.19, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.3667, "angle": -1.38, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.2667, "curve": 0.25, "c3": 0.75}, {"time": 5.9667, "angle": -2.19, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6.6667, "angle": -1.38}]}, "t13": {"rotate": [{"angle": -1.79, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.1333, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "angle": -2.19, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 3.3667, "angle": -1.79, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.5, "curve": 0.25, "c3": 0.75}, {"time": 6.2333, "angle": -2.19, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 6.6667, "angle": -1.79}]}, "st4": {"translate": [{"x": -2.35, "y": 0.19, "curve": 0.326, "c2": 0.31, "c3": 0.662, "c4": 0.65}, {"time": 0.2, "x": -3.64, "y": 0.29, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 1.1333, "x": -8.53, "y": 0.69, "curve": "stepped"}, {"time": 1.8667, "x": -8.53, "y": 0.69, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 3.5667, "x": -3.64, "y": 0.29, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 4.5, "x": -8.53, "y": 0.69, "curve": "stepped"}, {"time": 5.2667, "x": -8.53, "y": 0.69, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6.6667, "x": -2.35, "y": 0.19}], "scale": [{"x": 1.006, "y": 1.006, "curve": 0.326, "c2": 0.31, "c3": 0.662, "c4": 0.65}, {"time": 0.2, "x": 1.009, "y": 1.009, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 1.1333, "x": 1.02, "y": 1.02, "curve": "stepped"}, {"time": 1.8667, "x": 1.02, "y": 1.02, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 3.5667, "x": 1.009, "y": 1.009, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 4.5, "x": 1.02, "y": 1.02, "curve": "stepped"}, {"time": 5.2667, "x": 1.02, "y": 1.02, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6.6667, "x": 1.006, "y": 1.006}]}, "t14": {"rotate": [{"angle": 1.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7667, "angle": 4.9, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "angle": -2.4, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 3.0667, "angle": 0.05, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 3.3667, "angle": 1.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.1333, "angle": 4.9, "curve": 0.25, "c3": 0.75}, {"time": 5.7667, "angle": -2.4, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 6.4667, "angle": 0.05, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 6.6667, "angle": 1.25}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": -0.04, "y": 4.03, "curve": "stepped"}, {"time": 1.5, "x": -0.04, "y": 4.03, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "x": -0.04, "y": 4.03, "curve": "stepped"}, {"time": 4.8667, "x": -0.04, "y": 4.03, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "t15": {"rotate": [{"angle": 0.83, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.1333, "angle": 4.53}, {"time": 2.8667, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 3.3667, "angle": 0.83, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.5, "angle": 4.53}, {"time": 6.2333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 6.6667, "angle": 0.83}]}, "t16": {"rotate": [{"angle": 0.28, "curve": 0.289, "c2": 0.18, "c3": 0.711, "c4": 0.82}, {"time": 1.1333, "angle": 4.24, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.3, "angle": 4.53, "curve": 0.25, "c3": 0.75}, {"time": 3.0667, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 3.3667, "angle": 0.28, "curve": 0.289, "c2": 0.18, "c3": 0.711, "c4": 0.82}, {"time": 4.5, "angle": 4.24, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 4.7, "angle": 4.53, "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 6.6667, "angle": 0.28}]}, "t17": {"rotate": [{"curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.1333, "angle": 3.69, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.5, "angle": 4.53, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 4.5, "angle": 3.69, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 4.8667, "angle": 4.53, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "t18": {"rotate": [{"angle": 0.28, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.2, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 1.1333, "angle": 3.01, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1.7, "angle": 4.53, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 3.3667, "angle": 0.28, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.5667, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 4.5, "angle": 3.01, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 5.0667, "angle": 4.53, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 6.6667, "angle": 0.28}]}, "t19": {"rotate": [{"angle": 0.83, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.1333, "angle": 2.26, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.9, "angle": 4.53, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 3.3667, "angle": 0.83, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.7667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.5, "angle": 2.26, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3, "angle": 4.53, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 6.6667, "angle": 0.83}]}, "t8": {"rotate": [{"angle": 0.83, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.1333, "angle": 4.53}, {"time": 2.8667, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 3.3667, "angle": 0.83, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.5, "angle": 4.53}, {"time": 6.2333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 6.6667, "angle": 0.83}]}, "t20": {"rotate": [{"angle": 0.12, "curve": 0.271, "c2": 0.12, "c3": 0.693, "c4": 0.76}, {"time": 1.1333, "angle": 4.06, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 1.4, "angle": 4.53, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "curve": 0.311, "c3": 0.646, "c4": 0.35}, {"time": 3.3667, "angle": 0.12, "curve": 0.271, "c2": 0.12, "c3": 0.693, "c4": 0.76}, {"time": 4.5, "angle": 4.06, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 4.7667, "angle": 4.53, "curve": 0.25, "c3": 0.75}, {"time": 6.5667, "curve": 0.311, "c3": 0.646, "c4": 0.35}, {"time": 6.6667, "angle": 0.12}]}, "t21": {"rotate": [{"angle": 0.19, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.1333, "angle": 3.15, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.6667, "angle": 4.53, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3667, "angle": 0.19, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5333, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 4.5, "angle": 3.15, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 5.0333, "angle": 4.53, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "angle": 0.19}]}, "t22": {"rotate": [{"angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 2.49, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": 2.49, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -2.4}]}, "t23": {"rotate": [{"angle": -2.85, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 1.54, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": -2.85, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": 1.54, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -2.85}]}, "t24": {"rotate": [{"angle": -2.57, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.2, "angle": -2.85, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": 1.54, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 3.3667, "angle": -2.57, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.5667, "angle": -2.85, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "angle": 1.54, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 6.6667, "angle": -2.57}]}, "t25": {"rotate": [{"angle": -2.04, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "angle": -2.85, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": 1.54, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 3.3667, "angle": -2.04, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.7667, "angle": -2.85, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "angle": 1.54, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 6.6667, "angle": -2.04}]}, "t26": {"rotate": [{"angle": -1.37, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5667, "angle": -2.85, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 1.54, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 3.3667, "angle": -1.37, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.9333, "angle": -2.85, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": 1.54, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 6.6667, "angle": -1.37}]}, "t31": {"rotate": [{"angle": 0.56, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": 5.47, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 3.3667, "angle": 0.56, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 3.6333, "curve": 0.25, "c3": 0.75}, {"time": 5.1333, "angle": 5.47, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 6.6667, "angle": 0.56}]}, "t32": {"rotate": [{"angle": 1.66, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "angle": 5.47, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 3.3667, "angle": 1.66, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 3.9, "curve": 0.25, "c3": 0.75}, {"time": 5.4667, "angle": 5.47, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 6.6667, "angle": 1.66}]}, "t33": {"rotate": [{"angle": 2.91, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": 5.47, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 3.3667, "angle": 2.91, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 4.1667, "curve": 0.25, "c3": 0.75}, {"time": 5.8, "angle": 5.47, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 6.6667, "angle": 2.91}]}, "t34": {"rotate": [{"angle": 4.14, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "angle": 5.47, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.3667, "angle": 4.14, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 4.4333, "curve": 0.25, "c3": 0.75}, {"time": 6.1, "angle": 5.47, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6.6667, "angle": 4.14}]}, "t27": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -3.67, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -3.67, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "t35": {"rotate": [{"angle": -0.19, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": -3.09, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 3.3667, "angle": -0.19, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.5667, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "angle": -3.09, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 6.6667, "angle": -0.19}]}, "t45": {"rotate": [{"angle": -0.57, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": -3.09, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 3.3667, "angle": -0.57, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.7667, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "angle": -3.09, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 6.6667, "angle": -0.57}]}, "t36": {"rotate": [{"angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 0.19, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": 0.19, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -1.28}]}, "t37": {"rotate": [{"angle": -1.24, "curve": 0.354, "c2": 0.65, "c3": 0.689}, {"time": 0.1, "angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 0.19, "curve": 0.246, "c3": 0.72, "c4": 0.87}, {"time": 3.3667, "angle": -1.24, "curve": 0.354, "c2": 0.65, "c3": 0.689}, {"time": 3.5, "angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 0.19, "curve": 0.246, "c3": 0.72, "c4": 0.87}, {"time": 6.6667, "angle": -1.24}]}, "t38": {"rotate": [{"angle": -1.16, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.2333, "angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": 0.19, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 3.3667, "angle": -1.16, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 3.6, "angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "angle": 0.19, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 6.6667, "angle": -1.16}]}, "t39": {"rotate": [{"angle": -1.05, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.3333, "angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 0.19, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 3.3667, "angle": -1.05, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 3.7, "angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "angle": 0.19, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 6.6667, "angle": -1.05}]}, "t40": {"rotate": [{"angle": -0.93, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4667, "angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": 0.19, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3667, "angle": -0.93, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8333, "angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "angle": 0.19, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "angle": -0.93}]}, "t41": {"rotate": [{"angle": -0.79, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5667, "angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 0.19, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 3.3667, "angle": -0.79, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.9333, "angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": 0.19, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 6.6667, "angle": -0.79}]}, "t42": {"rotate": [{"angle": -0.64, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.6667, "angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": 0.19, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 3.3667, "angle": -0.64, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 4.0667, "angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": 0.19, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 6.6667, "angle": -0.64}]}, "t43": {"rotate": [{"angle": -0.5, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 0.8, "angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": 0.19, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 3.3667, "angle": -0.5, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 4.1667, "angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 5.8, "angle": 0.19, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 6.6667, "angle": -0.5}]}, "t44": {"rotate": [{"angle": -0.35, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.9, "angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": 0.19, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.3667, "angle": -0.35, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.2667, "angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 5.9667, "angle": 0.19, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6.6667, "angle": -0.35}]}, "ys4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "qin": {"rotate": [{"angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "angle": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 1.2}]}}, "deform": {"default": {"t1": {"t1": [{"curve": 0.25, "c3": 0.75}, {"time": 0.9333, "offset": 64, "vertices": [3.87187, 6.63394, 4.97698, 7.21738, 5.42076, 6.72934, 2.77624, 7.72873, 2.68207, 7.11789, 1.34955, 5.29867, 2.39282, 9.63699, 1.88013, 10.36136, 3.73686, 7.40156, 3.18498, 8.96442, 4.26478, 7.62738, 3.11739, 6.33676, 4.85137, 7.47104, 4.85137, 7.47104, 4.85137, 7.47104, 4.85137, 7.47104], "curve": "stepped"}, {"time": 2, "offset": 64, "vertices": [3.87187, 6.63394, 4.97698, 7.21738, 5.42076, 6.72934, 2.77624, 7.72873, 2.68207, 7.11789, 1.34955, 5.29867, 2.39282, 9.63699, 1.88013, 10.36136, 3.73686, 7.40156, 3.18498, 8.96442, 4.26478, 7.62738, 3.11739, 6.33676, 4.85137, 7.47104, 4.85137, 7.47104, 4.85137, 7.47104, 4.85137, 7.47104], "curve": 0.35, "c2": 0.4, "c3": 0.757}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "offset": 64, "vertices": [3.87187, 6.63394, 4.97698, 7.21738, 5.42076, 6.72934, 2.77624, 7.72873, 2.68207, 7.11789, 1.34955, 5.29867, 2.39282, 9.63699, 1.88013, 10.36136, 3.73686, 7.40156, 3.18498, 8.96442, 4.26478, 7.62738, 3.11739, 6.33676, 4.85137, 7.47104, 4.85137, 7.47104, 4.85137, 7.47104, 4.85137, 7.47104], "curve": "stepped"}, {"time": 5.3667, "offset": 64, "vertices": [3.87187, 6.63394, 4.97698, 7.21738, 5.42076, 6.72934, 2.77624, 7.72873, 2.68207, 7.11789, 1.34955, 5.29867, 2.39282, 9.63699, 1.88013, 10.36136, 3.73686, 7.40156, 3.18498, 8.96442, 4.26478, 7.62738, 3.11739, 6.33676, 4.85137, 7.47104, 4.85137, 7.47104, 4.85137, 7.47104, 4.85137, 7.47104], "curve": 0.35, "c2": 0.4, "c3": 0.757}, {"time": 6.6667}]}, "st1": {"st1": [{"offset": 312, "vertices": [0.19388, 1.33479, 0.44445, 1.27349, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.02648, 0.13986, 0.0173, 0.13325, -0.04614, 0.24365, 0.03013, 0.23214, -0.08014, 0.42324, 0.05227, 0.40323, -0.09402, 0.49648, -0.11408, 0.60247, 0.07447, 0.574, -0.11408, 0.60247, 0.07447, 0.574, -0.11408, 0.60247, 0.07447, 0.574, -0.11408, 0.60247, 0.07447, 0.574, -0.11408, 0.60247, 0.07447, 0.574, 0.44271, 0.42423, -0.11408, 0.60247, 0.07447, 0.574, 0.44271, 0.42423, -0.11408, 0.60247, 0.36103, 0.34592, -0.09303, 0.49129, 0.27845, 0.26682, -0.07176, 0.37895, 0.16337, 0.15655, -0.0421, 0.22234, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.29003, 0.27787, -0.07472, 0.39466, 0.04882, 0.37601, -0.07472, 0.39466, 0.04882, 0.37601, -0.0018, 0.59179, 0.17607, 0.53195, 0.10614, 0.60748, -0.03951, 0.20869, 0.02581, 0.19882, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.00121, 0.29595, 0.03861, 0.29342, 0.0047, 0.29591, 0.04254, 0.29287, 0.00121, 0.29595, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.04254, 0.29287, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0.04254, 0.29287, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.20894, 0.20021, -0.05384, 0.28435, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.07472, 0.39466, 0.04882, 0.37601, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.00326, 0.55609, 0.17007, 0.49843, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.12142, 0.64132, 0.07933, 0.61101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.07472, 0.39466, 0.04882, 0.37601, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.04288, 0.29521, 0.00122, 0.29832, 0.03891, 0.29576, 0.00474, 0.29827, 0.04769, 0.29448, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.00121, 0.29595, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.09308, -0.23135, 0.08697, -0.1354, 0.06912, -0.1454, 0.00121, 0.29595, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.07141, -0.02048, 0.06822, -0.02943, 0.04254, 0.29287, 0.00121, 0.29595, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.07362, 0.09564, 0.07237, 0.09664, 0, 0, 0, 0, 0, 0, 0, 0, 0.08143, 0.20301, 0.05479, 0.21184, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.00121, 0.29595, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0.08317, 0.29956, 0.04415, 0.30782, 0, 0, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0.03605, 0.42816, 0.16886, 0.3951, 0, 0, 0, 0, 0, 0, 0, 0, 0.02051, 0.52361, 0.09578, 0.51522, 0.18413, 0.49059, 0, 0, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, -0.00213, 0.65088, 0.09173, 0.64442, 0.20267, 0.61852, 0, 0, 0, 0, 0, 0, 0, 0, 0.09791, 0.77764, 0.23198, 0.74863, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0.06468, 0.92243, 0.07972, 0.92137, 0.23912, 0.89333, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0.04592, 1.03464, 0.24209, 1.00705], "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 1.3, "offset": 312, "vertices": [3.08899, 21.26614, 7.0811, 20.28955, 0, 0, 0, 0, 0, 0, 0.67773, 4.66605, 0.07486, 4.71454, 0.75384, 4.65456, 1.5537, 4.45178, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.42187, 2.22825, 0.2756, 2.12294, -0.73505, 3.88194, 0.48004, 3.69849, -1.27679, 6.74312, 0.83286, 6.42433, -1.49792, 7.90999, -1.81757, 9.59874, 1.18643, 9.14505, -1.81757, 9.59874, 1.18643, 9.14505, -1.81757, 9.59874, 1.18643, 9.14505, -1.81757, 9.59874, 1.18643, 9.14505, -1.81757, 9.59874, 1.18643, 9.14505, 7.05341, 6.75891, -1.81757, 9.59874, 1.18643, 9.14505, 7.05341, 6.75891, -1.81757, 9.59874, 5.75192, 5.51129, -1.48212, 7.82738, 4.43634, 4.2511, -1.14331, 6.03758, 2.60278, 2.49426, -0.67078, 3.54243, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.62076, 4.42712, -1.19043, 6.2878, 0.77783, 5.99074, -1.19043, 6.2878, 0.77783, 5.99074, -0.02875, 9.42851, 2.80521, 8.47514, 1.6911, 9.67844, -0.62952, 3.32482, 0.41129, 3.16766, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.67773, 4.66605, 0.01932, 4.71521, 0.61509, 4.67476, 0.07486, 4.71454, 0.67773, 4.66605, 0.01932, 4.71521, 0.61509, 4.67476, 0.07486, 4.71454, 0.75384, 4.65456, 0.67773, 4.66605, 0.61509, 4.67476, 0.07486, 4.71454, 0.75384, 4.65456, 1.5537, 4.45178, 0.67773, 4.66605, 0.61509, 4.67476, 0.07486, 4.71454, 0.75384, 4.65456, 1.5537, 4.45178, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.32895, 3.18982, -0.85779, 4.53029, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.19043, 6.2878, 0.77783, 5.99074, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.05188, 8.85966, 2.70963, 7.94101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.93445, 10.21757, 1.26392, 9.73473, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.19043, 6.2878, 0.77783, 5.99074, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.68317, 4.70329, 0.01949, 4.75285, 0.62, 4.71208, 0.07546, 4.75217, 0.75987, 4.6917, 0, 0, 0, 0, 0, 0, 0.67773, 4.66605, 0.01932, 4.71521, 0.61509, 4.67476, 0.07486, 4.71454, 0.75384, 4.65456, 1.48303, -3.68584, 1.38567, -2.15726, 1.1012, -2.31656, 0.01932, 4.71521, 0.61509, 4.67476, 0.07486, 4.71454, 0.75384, 4.65456, 1.13777, -0.32631, 1.08684, -0.46893, 0.67773, 4.66605, 0.01932, 4.71521, 0.61509, 4.67476, 0.07486, 4.71454, 0.75384, 4.65456, 1.17294, 1.52368, 1.15297, 1.53963, 0, 0, 0, 0, 0, 0, 0, 0, 1.29736, 3.23438, 0.87286, 3.37514, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.67773, 4.66605, 0.01932, 4.71521, 0.61509, 4.67476, 0.07486, 4.71454, 0.75384, 4.65456, 1.5537, 4.45178, 1.32504, 4.77257, 0.70335, 4.90427, 0, 0, 0, 0, 0, 0, 0, 0, 0.67773, 4.66605, 0.61509, 4.67476, 0.07486, 4.71454, 0.75384, 4.65456, 1.5537, 4.45178, 0.57435, 6.82152, 2.69034, 6.29482, 0, 0, 0, 0, 0, 0, 0, 0, 0.32677, 8.34221, 1.52596, 8.20857, 2.93358, 7.81619, 0, 0, 0, 0, 0, 0, 0, 0, 0.67773, 4.66605, 0.61509, 4.67476, 0.07486, 4.71454, 0.75384, 4.65456, 1.5537, 4.45178, -0.03386, 10.36989, 1.46146, 10.26707, 3.22891, 9.85443, 0, 0, 0, 0, 0, 0, 0, 0, 1.55996, 12.38948, 3.69591, 11.92725, 0, 0, 0, 0, 0, 0, 0.67773, 4.66605, 0.07486, 4.71454, 0.75384, 4.65456, 1.5537, 4.45178, 1.03043, 14.6964, 1.27011, 14.67944, 3.80975, 14.23271, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.67773, 4.66605, 0.07486, 4.71454, 0.75384, 4.65456, 1.5537, 4.45178, 0.73163, 16.4841, 3.85703, 16.04459], "curve": 0.25, "c3": 0.75}, {"time": 3.0667, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 3.3667, "offset": 312, "vertices": [0.19388, 1.33479, 0.44445, 1.27349, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.02648, 0.13986, 0.0173, 0.13325, -0.04614, 0.24365, 0.03013, 0.23214, -0.08014, 0.42324, 0.05227, 0.40323, -0.09402, 0.49648, -0.11408, 0.60247, 0.07447, 0.574, -0.11408, 0.60247, 0.07447, 0.574, -0.11408, 0.60247, 0.07447, 0.574, -0.11408, 0.60247, 0.07447, 0.574, -0.11408, 0.60247, 0.07447, 0.574, 0.44271, 0.42423, -0.11408, 0.60247, 0.07447, 0.574, 0.44271, 0.42423, -0.11408, 0.60247, 0.36103, 0.34592, -0.09303, 0.49129, 0.27845, 0.26682, -0.07176, 0.37895, 0.16337, 0.15655, -0.0421, 0.22234, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.29003, 0.27787, -0.07472, 0.39466, 0.04882, 0.37601, -0.07472, 0.39466, 0.04882, 0.37601, -0.0018, 0.59179, 0.17607, 0.53195, 0.10614, 0.60748, -0.03951, 0.20869, 0.02581, 0.19882, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.00121, 0.29595, 0.03861, 0.29342, 0.0047, 0.29591, 0.04254, 0.29287, 0.00121, 0.29595, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.04254, 0.29287, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0.04254, 0.29287, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.20894, 0.20021, -0.05384, 0.28435, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.07472, 0.39466, 0.04882, 0.37601, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.00326, 0.55609, 0.17007, 0.49843, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.12142, 0.64132, 0.07933, 0.61101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.07472, 0.39466, 0.04882, 0.37601, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.04288, 0.29521, 0.00122, 0.29832, 0.03891, 0.29576, 0.00474, 0.29827, 0.04769, 0.29448, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.00121, 0.29595, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.09308, -0.23135, 0.08697, -0.1354, 0.06912, -0.1454, 0.00121, 0.29595, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.07141, -0.02048, 0.06822, -0.02943, 0.04254, 0.29287, 0.00121, 0.29595, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.07362, 0.09564, 0.07237, 0.09664, 0, 0, 0, 0, 0, 0, 0, 0, 0.08143, 0.20301, 0.05479, 0.21184, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.00121, 0.29595, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0.08317, 0.29956, 0.04415, 0.30782, 0, 0, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0.03605, 0.42816, 0.16886, 0.3951, 0, 0, 0, 0, 0, 0, 0, 0, 0.02051, 0.52361, 0.09578, 0.51522, 0.18413, 0.49059, 0, 0, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, -0.00213, 0.65088, 0.09173, 0.64442, 0.20267, 0.61852, 0, 0, 0, 0, 0, 0, 0, 0, 0.09791, 0.77764, 0.23198, 0.74863, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0.06468, 0.92243, 0.07972, 0.92137, 0.23912, 0.89333, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0.04592, 1.03464, 0.24209, 1.00705], "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 4.7, "offset": 312, "vertices": [3.08899, 21.26614, 7.0811, 20.28955, 0, 0, 0, 0, 0, 0, 0.67773, 4.66605, 0.07486, 4.71454, 0.75384, 4.65456, 1.5537, 4.45178, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.42187, 2.22825, 0.2756, 2.12294, -0.73505, 3.88194, 0.48004, 3.69849, -1.27679, 6.74312, 0.83286, 6.42433, -1.49792, 7.90999, -1.81757, 9.59874, 1.18643, 9.14505, -1.81757, 9.59874, 1.18643, 9.14505, -1.81757, 9.59874, 1.18643, 9.14505, -1.81757, 9.59874, 1.18643, 9.14505, -1.81757, 9.59874, 1.18643, 9.14505, 7.05341, 6.75891, -1.81757, 9.59874, 1.18643, 9.14505, 7.05341, 6.75891, -1.81757, 9.59874, 5.75192, 5.51129, -1.48212, 7.82738, 4.43634, 4.2511, -1.14331, 6.03758, 2.60278, 2.49426, -0.67078, 3.54243, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.62076, 4.42712, -1.19043, 6.2878, 0.77783, 5.99074, -1.19043, 6.2878, 0.77783, 5.99074, -0.02875, 9.42851, 2.80521, 8.47514, 1.6911, 9.67844, -0.62952, 3.32482, 0.41129, 3.16766, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.67773, 4.66605, 0.01932, 4.71521, 0.61509, 4.67476, 0.07486, 4.71454, 0.67773, 4.66605, 0.01932, 4.71521, 0.61509, 4.67476, 0.07486, 4.71454, 0.75384, 4.65456, 0.67773, 4.66605, 0.61509, 4.67476, 0.07486, 4.71454, 0.75384, 4.65456, 1.5537, 4.45178, 0.67773, 4.66605, 0.61509, 4.67476, 0.07486, 4.71454, 0.75384, 4.65456, 1.5537, 4.45178, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.32895, 3.18982, -0.85779, 4.53029, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.19043, 6.2878, 0.77783, 5.99074, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.05188, 8.85966, 2.70963, 7.94101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.93445, 10.21757, 1.26392, 9.73473, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.19043, 6.2878, 0.77783, 5.99074, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.68317, 4.70329, 0.01949, 4.75285, 0.62, 4.71208, 0.07546, 4.75217, 0.75987, 4.6917, 0, 0, 0, 0, 0, 0, 0.67773, 4.66605, 0.01932, 4.71521, 0.61509, 4.67476, 0.07486, 4.71454, 0.75384, 4.65456, 1.48303, -3.68584, 1.38567, -2.15726, 1.1012, -2.31656, 0.01932, 4.71521, 0.61509, 4.67476, 0.07486, 4.71454, 0.75384, 4.65456, 1.13777, -0.32631, 1.08684, -0.46893, 0.67773, 4.66605, 0.01932, 4.71521, 0.61509, 4.67476, 0.07486, 4.71454, 0.75384, 4.65456, 1.17294, 1.52368, 1.15297, 1.53963, 0, 0, 0, 0, 0, 0, 0, 0, 1.29736, 3.23438, 0.87286, 3.37514, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.67773, 4.66605, 0.01932, 4.71521, 0.61509, 4.67476, 0.07486, 4.71454, 0.75384, 4.65456, 1.5537, 4.45178, 1.32504, 4.77257, 0.70335, 4.90427, 0, 0, 0, 0, 0, 0, 0, 0, 0.67773, 4.66605, 0.61509, 4.67476, 0.07486, 4.71454, 0.75384, 4.65456, 1.5537, 4.45178, 0.57435, 6.82152, 2.69034, 6.29482, 0, 0, 0, 0, 0, 0, 0, 0, 0.32677, 8.34221, 1.52596, 8.20857, 2.93358, 7.81619, 0, 0, 0, 0, 0, 0, 0, 0, 0.67773, 4.66605, 0.61509, 4.67476, 0.07486, 4.71454, 0.75384, 4.65456, 1.5537, 4.45178, -0.03386, 10.36989, 1.46146, 10.26707, 3.22891, 9.85443, 0, 0, 0, 0, 0, 0, 0, 0, 1.55996, 12.38948, 3.69591, 11.92725, 0, 0, 0, 0, 0, 0, 0.67773, 4.66605, 0.07486, 4.71454, 0.75384, 4.65456, 1.5537, 4.45178, 1.03043, 14.6964, 1.27011, 14.67944, 3.80975, 14.23271, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.67773, 4.66605, 0.07486, 4.71454, 0.75384, 4.65456, 1.5537, 4.45178, 0.73163, 16.4841, 3.85703, 16.04459], "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 6.6667, "offset": 312, "vertices": [0.19388, 1.33479, 0.44445, 1.27349, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.02648, 0.13986, 0.0173, 0.13325, -0.04614, 0.24365, 0.03013, 0.23214, -0.08014, 0.42324, 0.05227, 0.40323, -0.09402, 0.49648, -0.11408, 0.60247, 0.07447, 0.574, -0.11408, 0.60247, 0.07447, 0.574, -0.11408, 0.60247, 0.07447, 0.574, -0.11408, 0.60247, 0.07447, 0.574, -0.11408, 0.60247, 0.07447, 0.574, 0.44271, 0.42423, -0.11408, 0.60247, 0.07447, 0.574, 0.44271, 0.42423, -0.11408, 0.60247, 0.36103, 0.34592, -0.09303, 0.49129, 0.27845, 0.26682, -0.07176, 0.37895, 0.16337, 0.15655, -0.0421, 0.22234, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.29003, 0.27787, -0.07472, 0.39466, 0.04882, 0.37601, -0.07472, 0.39466, 0.04882, 0.37601, -0.0018, 0.59179, 0.17607, 0.53195, 0.10614, 0.60748, -0.03951, 0.20869, 0.02581, 0.19882, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.00121, 0.29595, 0.03861, 0.29342, 0.0047, 0.29591, 0.04254, 0.29287, 0.00121, 0.29595, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.04254, 0.29287, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0.04254, 0.29287, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.20894, 0.20021, -0.05384, 0.28435, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.07472, 0.39466, 0.04882, 0.37601, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.00326, 0.55609, 0.17007, 0.49843, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.12142, 0.64132, 0.07933, 0.61101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.07472, 0.39466, 0.04882, 0.37601, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.04288, 0.29521, 0.00122, 0.29832, 0.03891, 0.29576, 0.00474, 0.29827, 0.04769, 0.29448, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.00121, 0.29595, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.09308, -0.23135, 0.08697, -0.1354, 0.06912, -0.1454, 0.00121, 0.29595, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.07141, -0.02048, 0.06822, -0.02943, 0.04254, 0.29287, 0.00121, 0.29595, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.07362, 0.09564, 0.07237, 0.09664, 0, 0, 0, 0, 0, 0, 0, 0, 0.08143, 0.20301, 0.05479, 0.21184, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.00121, 0.29595, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0.08317, 0.29956, 0.04415, 0.30782, 0, 0, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0.03605, 0.42816, 0.16886, 0.3951, 0, 0, 0, 0, 0, 0, 0, 0, 0.02051, 0.52361, 0.09578, 0.51522, 0.18413, 0.49059, 0, 0, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, -0.00213, 0.65088, 0.09173, 0.64442, 0.20267, 0.61852, 0, 0, 0, 0, 0, 0, 0, 0, 0.09791, 0.77764, 0.23198, 0.74863, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0.06468, 0.92243, 0.07972, 0.92137, 0.23912, 0.89333, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0.04592, 1.03464, 0.24209, 1.00705]}]}, "st": {"st": [{"offset": 36, "vertices": [-0.23437, -0.17191, 0.02536, -0.28388, -0.2902, 0.01636, 0.01887, -0.29006, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.58461, -0.18832, -0.13852, -0.58611, 0.40357, 0.48831, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.30799, -0.22587, 0.0334, -0.37377, 0, 0, 0, 0, 0, 0, 0.08603, -0.11725, 0.14201, 0.01266, -0.12528, 0.05545, -0.1958, -0.14362, 0.02118, -0.23714, -0.36579, 0.03234, -0.21264, -0.29038, -0.22396, -0.29103, -0.34406, 0.46908, -0.56805, -0.05081, 0.50116, -0.22177, 0, 0, 0, 0, 0, 0, -0.11748, -0.08617, 0.01271, -0.1423, 0, 0, 0, 0, 0, 0, 0, 0, -0.1387, -0.19947, -0.03107, -0.00272, 0.19749, -0.10776, 0, 0, -0.07376, -0.22559, 0, 0, 0.16642, -0.11054, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.39695, -0.16662, 0.16324, 0.397, 0.09111, 0.41947, 0.00919, 0.42915, 0.06984, 0.42353, -0.11607, 0.40172, -0.25399, 0.33216, 0.70487, 0.04587, -0.04518, 0.70486, -0.16805, 0.68603, -0.29616, 0.64121, -0.20244, 0.67668, -0.46924, 0.47188, 0.04367, 0.65762, 0.13631, 0.64483, -0.14962, 0.62784, -0.26924, 0.58292, -0.18403, 0.61516, -0.0263, 0.64156, -0.42658, 0.42898, -0.16154, 0.34976, -0.11042, 0.3691, -0.01578, 0.38494, -0.25595, 0.25739, -0.33192, 0.1469, -0.07361, 0.24607, -0.01052, 0.25663, -0.17063, 0.1716, -0.22128, 0.09793, -0.1077, 0.23318, -0.07361, 0.24607, -0.01052, 0.25663, -0.17064, 0.1716, -0.22129, 0.09793, -0.08077, 0.17488, -0.05521, 0.18455, -0.00789, 0.19247, -0.12797, 0.12869, -0.16596, 0.07345, -0.05385, 0.11659, -0.03681, 0.12303, -0.00526, 0.12831, -0.08532, 0.0858, -0.11064, 0.04897, -0.18332, 0.7484, -0.32309, 0.69951, -0.22084, 0.7382, -0.5119, 0.51478, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.16155, 0.34976, -0.11042, 0.36911, -0.01578, 0.38495, -0.25595, 0.25739, -0.18847, 0.40806, -0.12882, 0.43063, -0.01841, 0.44911, -0.29861, 0.30029, -0.38725, 0.17139, -0.16155, 0.34976, -0.11042, 0.3691, -0.01578, 0.38494, -0.25595, 0.25739, -0.33192, 0.1469], "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 1.3, "offset": 36, "vertices": [-3.73407, -2.73883, 0.40398, -4.52277, -4.62344, 0.2607, 0.3007, -4.62137, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -9.31409, -3.00027, -2.20685, -9.33801, 6.42969, 7.77989, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.90698, -3.59866, 0.53221, -5.95496, 0, 0, 0, 0, 0, 0, 1.37061, -1.86801, 2.26254, 0.20166, -1.99597, 0.88351, -3.11957, -2.28812, 0.33748, -3.7782, -5.82788, 0.51526, -3.38785, -4.62646, -3.56825, -4.63675, -5.48157, 7.47348, -9.05032, -0.80945, 7.98465, -3.53326, 0, 0, 0, 0, 0, 0, -1.87164, -1.3728, 0.2025, -2.26709, 0, 0, 0, 0, 0, 0, 0, 0, -2.20982, -3.17792, -0.49493, -0.0434, 3.14639, -1.7168, 0, 0, -1.17523, -3.59412, 0.72292, -1.67804, 2.65147, -1.76111, 0, 0, 0, 0, 0, 0, 0, 0, 0.77567, -2.25458, -2.39081, -0.44873, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.31046, -3.42242, -3.49857, 0.19264, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.40807, -4.49951, -4.59821, 0.25321, 0.29906, -4.59534, 0.40807, -4.49951, -4.59821, 0.25321, 0.29906, -4.59534, 1.84866, 4.43217, 0, 0, 0, 0, 0, 0, 0, 0, 0.81613, -8.99921, -9.19635, 0.5064, 0.59814, -9.19067, 0.81613, -8.99921, -9.19635, 0.5064, 0.59814, -9.19067, 3.69724, 8.86433, -8.04675, 0.44308, 0.52339, -8.04175, 0.7141, -7.87433, -8.04675, 0.44308, 0.52339, -8.04175, 3.23502, 7.75623, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.32769, -5.03485, 4.9357, 0.33478, 4.79799, 1.20531, 4.47318, 2.11294, 4.72291, 1.47243, 3.65045, 3.78946, 0.32769, -5.03485, 4.79799, 1.20531, 4.47318, 2.11294, 4.72291, 1.47243, 4.93948, 0.27411, 3.65045, 3.78946, 2.02548, 4.85612, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.32426, -2.65469, 2.60074, 6.32505, 1.4516, 6.68308, 0.14647, 6.83726, 1.11265, 6.74773, -1.84919, 6.40021, -4.04657, 5.2921, 11.23017, 0.73083, -0.71979, 11.23, -2.67734, 10.92995, -4.71855, 10.21594, -3.22525, 10.78094, -7.47597, 7.51804, 0.69582, 10.4774, 2.17166, 10.27354, -2.38379, 10.00284, -4.28958, 9.2872, -2.93202, 9.80087, -0.41901, 10.22144, -6.79631, 6.83453, -2.57376, 5.57237, -1.75922, 5.88056, -0.25141, 6.13291, -4.07779, 4.10077, -5.28827, 2.34039, -1.17282, 3.92039, -0.16761, 4.08863, -2.71854, 2.73389, -3.52551, 1.56026, -1.7159, 3.71503, -1.17284, 3.92048, -0.16761, 4.08872, -2.71861, 2.73395, -3.52557, 1.56029, -1.28685, 2.78616, -0.8796, 2.94024, -0.12571, 3.06642, -2.03886, 2.05035, -2.6441, 1.17018, -0.85793, 1.85748, -0.58641, 1.96021, -0.08381, 2.04433, -1.35927, 1.36694, -1.76276, 0.78014, -2.92076, 11.92371, -5.14754, 11.14479, -3.51846, 11.76116, -8.15565, 8.20157, 6.39722, 1.60705, 5.96416, 2.8172, 6.29712, 1.96321, 4.86718, 5.05252, 0.43691, -6.71304, 6.39722, 1.60705, 5.96416, 2.8172, 6.29712, 1.96321, 6.58588, 0.36546, 4.86718, 5.05252, 2.70059, 6.47476, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.29718, 1.96323, 4.86724, 5.05258, 6.39728, 1.60707, 5.9642, 2.81722, 6.29718, 1.96323, 6.58595, 0.36547, 4.86724, 5.05258, 2.70062, 6.47479, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.57381, 5.57247, -1.75924, 5.88067, -0.25142, 6.13303, -4.07782, 4.10083, -3.00281, 6.50123, -2.05246, 6.86081, -0.29332, 7.15523, -4.75754, 4.78433, -6.16968, 2.73055, -2.57378, 5.57243, -1.75924, 5.88063, -0.25142, 6.13299, -4.07782, 4.1008, -5.28821, 2.34045], "curve": 0.25, "c3": 0.75}, {"time": 3.0667, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 3.3667, "offset": 36, "vertices": [-0.23437, -0.17191, 0.02536, -0.28388, -0.2902, 0.01636, 0.01887, -0.29006, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.58461, -0.18832, -0.13852, -0.58611, 0.40357, 0.48831, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.30799, -0.22587, 0.0334, -0.37377, 0, 0, 0, 0, 0, 0, 0.08603, -0.11725, 0.14201, 0.01266, -0.12528, 0.05545, -0.1958, -0.14362, 0.02118, -0.23714, -0.36579, 0.03234, -0.21264, -0.29038, -0.22396, -0.29103, -0.34406, 0.46908, -0.56805, -0.05081, 0.50116, -0.22177, 0, 0, 0, 0, 0, 0, -0.11748, -0.08617, 0.01271, -0.1423, 0, 0, 0, 0, 0, 0, 0, 0, -0.1387, -0.19947, -0.03107, -0.00272, 0.19749, -0.10776, 0, 0, -0.07376, -0.22559, 0, 0, 0.16642, -0.11054, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.39695, -0.16662, 0.16324, 0.397, 0.09111, 0.41947, 0.00919, 0.42915, 0.06984, 0.42353, -0.11607, 0.40172, -0.25399, 0.33216, 0.70487, 0.04587, -0.04518, 0.70486, -0.16805, 0.68603, -0.29616, 0.64121, -0.20244, 0.67668, -0.46924, 0.47188, 0.04367, 0.65762, 0.13631, 0.64483, -0.14962, 0.62784, -0.26924, 0.58292, -0.18403, 0.61516, -0.0263, 0.64156, -0.42658, 0.42898, -0.16154, 0.34976, -0.11042, 0.3691, -0.01578, 0.38494, -0.25595, 0.25739, -0.33192, 0.1469, -0.07361, 0.24607, -0.01052, 0.25663, -0.17063, 0.1716, -0.22128, 0.09793, -0.1077, 0.23318, -0.07361, 0.24607, -0.01052, 0.25663, -0.17064, 0.1716, -0.22129, 0.09793, -0.08077, 0.17488, -0.05521, 0.18455, -0.00789, 0.19247, -0.12797, 0.12869, -0.16596, 0.07345, -0.05385, 0.11659, -0.03681, 0.12303, -0.00526, 0.12831, -0.08532, 0.0858, -0.11064, 0.04897, -0.18332, 0.7484, -0.32309, 0.69951, -0.22084, 0.7382, -0.5119, 0.51478, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.16155, 0.34976, -0.11042, 0.36911, -0.01578, 0.38495, -0.25595, 0.25739, -0.18847, 0.40806, -0.12882, 0.43063, -0.01841, 0.44911, -0.29861, 0.30029, -0.38725, 0.17139, -0.16155, 0.34976, -0.11042, 0.3691, -0.01578, 0.38494, -0.25595, 0.25739, -0.33192, 0.1469], "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 4.7, "offset": 36, "vertices": [-3.73407, -2.73883, 0.40398, -4.52277, -4.62344, 0.2607, 0.3007, -4.62137, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -9.31409, -3.00027, -2.20685, -9.33801, 6.42969, 7.77989, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.90698, -3.59866, 0.53221, -5.95496, 0, 0, 0, 0, 0, 0, 1.37061, -1.86801, 2.26254, 0.20166, -1.99597, 0.88351, -3.11957, -2.28812, 0.33748, -3.7782, -5.82788, 0.51526, -3.38785, -4.62646, -3.56825, -4.63675, -5.48157, 7.47348, -9.05032, -0.80945, 7.98465, -3.53326, 0, 0, 0, 0, 0, 0, -1.87164, -1.3728, 0.2025, -2.26709, 0, 0, 0, 0, 0, 0, 0, 0, -2.20982, -3.17792, -0.49493, -0.0434, 3.14639, -1.7168, 0, 0, -1.17523, -3.59412, 0.72292, -1.67804, 2.65147, -1.76111, 0, 0, 0, 0, 0, 0, 0, 0, 0.77567, -2.25458, -2.39081, -0.44873, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.31046, -3.42242, -3.49857, 0.19264, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.40807, -4.49951, -4.59821, 0.25321, 0.29906, -4.59534, 0.40807, -4.49951, -4.59821, 0.25321, 0.29906, -4.59534, 1.84866, 4.43217, 0, 0, 0, 0, 0, 0, 0, 0, 0.81613, -8.99921, -9.19635, 0.5064, 0.59814, -9.19067, 0.81613, -8.99921, -9.19635, 0.5064, 0.59814, -9.19067, 3.69724, 8.86433, -8.04675, 0.44308, 0.52339, -8.04175, 0.7141, -7.87433, -8.04675, 0.44308, 0.52339, -8.04175, 3.23502, 7.75623, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.32769, -5.03485, 4.9357, 0.33478, 4.79799, 1.20531, 4.47318, 2.11294, 4.72291, 1.47243, 3.65045, 3.78946, 0.32769, -5.03485, 4.79799, 1.20531, 4.47318, 2.11294, 4.72291, 1.47243, 4.93948, 0.27411, 3.65045, 3.78946, 2.02548, 4.85612, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.32426, -2.65469, 2.60074, 6.32505, 1.4516, 6.68308, 0.14647, 6.83726, 1.11265, 6.74773, -1.84919, 6.40021, -4.04657, 5.2921, 11.23017, 0.73083, -0.71979, 11.23, -2.67734, 10.92995, -4.71855, 10.21594, -3.22525, 10.78094, -7.47597, 7.51804, 0.69582, 10.4774, 2.17166, 10.27354, -2.38379, 10.00284, -4.28958, 9.2872, -2.93202, 9.80087, -0.41901, 10.22144, -6.79631, 6.83453, -2.57376, 5.57237, -1.75922, 5.88056, -0.25141, 6.13291, -4.07779, 4.10077, -5.28827, 2.34039, -1.17282, 3.92039, -0.16761, 4.08863, -2.71854, 2.73389, -3.52551, 1.56026, -1.7159, 3.71503, -1.17284, 3.92048, -0.16761, 4.08872, -2.71861, 2.73395, -3.52557, 1.56029, -1.28685, 2.78616, -0.8796, 2.94024, -0.12571, 3.06642, -2.03886, 2.05035, -2.6441, 1.17018, -0.85793, 1.85748, -0.58641, 1.96021, -0.08381, 2.04433, -1.35927, 1.36694, -1.76276, 0.78014, -2.92076, 11.92371, -5.14754, 11.14479, -3.51846, 11.76116, -8.15565, 8.20157, 6.39722, 1.60705, 5.96416, 2.8172, 6.29712, 1.96321, 4.86718, 5.05252, 0.43691, -6.71304, 6.39722, 1.60705, 5.96416, 2.8172, 6.29712, 1.96321, 6.58588, 0.36546, 4.86718, 5.05252, 2.70059, 6.47476, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.29718, 1.96323, 4.86724, 5.05258, 6.39728, 1.60707, 5.9642, 2.81722, 6.29718, 1.96323, 6.58595, 0.36547, 4.86724, 5.05258, 2.70062, 6.47479, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.57381, 5.57247, -1.75924, 5.88067, -0.25142, 6.13303, -4.07782, 4.10083, -3.00281, 6.50123, -2.05246, 6.86081, -0.29332, 7.15523, -4.75754, 4.78433, -6.16968, 2.73055, -2.57378, 5.57243, -1.75924, 5.88063, -0.25142, 6.13299, -4.07782, 4.1008, -5.28821, 2.34045], "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 6.6667, "offset": 36, "vertices": [-0.23437, -0.17191, 0.02536, -0.28388, -0.2902, 0.01636, 0.01887, -0.29006, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.58461, -0.18832, -0.13852, -0.58611, 0.40357, 0.48831, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.30799, -0.22587, 0.0334, -0.37377, 0, 0, 0, 0, 0, 0, 0.08603, -0.11725, 0.14201, 0.01266, -0.12528, 0.05545, -0.1958, -0.14362, 0.02118, -0.23714, -0.36579, 0.03234, -0.21264, -0.29038, -0.22396, -0.29103, -0.34406, 0.46908, -0.56805, -0.05081, 0.50116, -0.22177, 0, 0, 0, 0, 0, 0, -0.11748, -0.08617, 0.01271, -0.1423, 0, 0, 0, 0, 0, 0, 0, 0, -0.1387, -0.19947, -0.03107, -0.00272, 0.19749, -0.10776, 0, 0, -0.07376, -0.22559, 0, 0, 0.16642, -0.11054, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.39695, -0.16662, 0.16324, 0.397, 0.09111, 0.41947, 0.00919, 0.42915, 0.06984, 0.42353, -0.11607, 0.40172, -0.25399, 0.33216, 0.70487, 0.04587, -0.04518, 0.70486, -0.16805, 0.68603, -0.29616, 0.64121, -0.20244, 0.67668, -0.46924, 0.47188, 0.04367, 0.65762, 0.13631, 0.64483, -0.14962, 0.62784, -0.26924, 0.58292, -0.18403, 0.61516, -0.0263, 0.64156, -0.42658, 0.42898, -0.16154, 0.34976, -0.11042, 0.3691, -0.01578, 0.38494, -0.25595, 0.25739, -0.33192, 0.1469, -0.07361, 0.24607, -0.01052, 0.25663, -0.17063, 0.1716, -0.22128, 0.09793, -0.1077, 0.23318, -0.07361, 0.24607, -0.01052, 0.25663, -0.17064, 0.1716, -0.22129, 0.09793, -0.08077, 0.17488, -0.05521, 0.18455, -0.00789, 0.19247, -0.12797, 0.12869, -0.16596, 0.07345, -0.05385, 0.11659, -0.03681, 0.12303, -0.00526, 0.12831, -0.08532, 0.0858, -0.11064, 0.04897, -0.18332, 0.7484, -0.32309, 0.69951, -0.22084, 0.7382, -0.5119, 0.51478, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.16155, 0.34976, -0.11042, 0.36911, -0.01578, 0.38495, -0.25595, 0.25739, -0.18847, 0.40806, -0.12882, 0.43063, -0.01841, 0.44911, -0.29861, 0.30029, -0.38725, 0.17139, -0.16155, 0.34976, -0.11042, 0.3691, -0.01578, 0.38494, -0.25595, 0.25739, -0.33192, 0.1469]}]}, "yy": {"yy": [{"curve": 0.25, "c3": 0.75}, {"time": 0.7667, "offset": 14, "vertices": [0.18896, -1.0351, 0, 0, 0, 0, 0, 0, 0, 0, 6.189, 0.96375, 5.05435, 2.68372, 2.23596, 2.98248, 0, 0, 1.99164, 0.44873, 2.91748, -0.95813, 3.5865, -0.97601, 5.35748, -0.51062, 1.78903, 0.28662], "curve": "stepped"}, {"time": 1.5, "offset": 14, "vertices": [0.18896, -1.0351, 0, 0, 0, 0, 0, 0, 0, 0, 6.189, 0.96375, 5.05435, 2.68372, 2.23596, 2.98248, 0, 0, 1.99164, 0.44873, 2.91748, -0.95813, 3.5865, -0.97601, 5.35748, -0.51062, 1.78903, 0.28662], "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 2.8667, "offset": 14, "vertices": [0.0424, -0.23225, 0, 0, 0, 0, 0, 0, 0, 0, 1.38867, 0.21624, 1.13408, 0.60217, 0.5017, 0.6692, 0, 0, 0.44688, 0.10069, 0.65462, -0.21498, 0.80473, -0.219, 1.2021, -0.11457, 0.40142, 0.06431], "curve": 0.351, "c2": 0.4, "c3": 0.687, "c4": 0.75}, {"time": 3.1667, "vertices": [-0.87225, 12.63184, -1.18964, 6.32471, -0.61589, 2.73535, 0, 0, -0.31953, 1.42053, 2.06265, -9.14734, 2.06265, -9.14734, 2.09213, -9.31301, 0, 0, 0, 0, -0.65282, 2.90173, -0.67206, 10.99048, -1.15667, 9.80922, -1.87574, 13.35853, -1.96514, 11.78273, -0.97955, 4.35132, -0.58094, 4.10913, 0.94999, -2.24567, 1.16876, -2.73164, 0.9537, -0.40787, -0.57376, 3.90097], "curve": 0.369, "c2": 0.63, "c3": 0.707}, {"time": 3.5667, "offset": 14, "vertices": [0.02458, -0.13466, 0, 0, 0, 0, 0, 0, 0, 0, 0.80517, 0.12538, 0.65756, 0.34914, 0.29089, 0.38801, 0, 0, 0.25911, 0.05838, 0.37956, -0.12465, 0.46659, -0.12698, 0.69699, -0.06643, 0.23275, 0.03729], "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 4.1667, "offset": 14, "vertices": [0.18896, -1.0351, 0, 0, 0, 0, 0, 0, 0, 0, 6.189, 0.96375, 5.05435, 2.68372, 2.23596, 2.98248, 0, 0, 1.99164, 0.44873, 2.91748, -0.95813, 3.5865, -0.97601, 5.35748, -0.51062, 1.78903, 0.28662], "curve": "stepped"}, {"time": 4.9, "offset": 14, "vertices": [0.18896, -1.0351, 0, 0, 0, 0, 0, 0, 0, 0, 6.189, 0.96375, 5.05435, 2.68372, 2.23596, 2.98248, 0, 0, 1.99164, 0.44873, 2.91748, -0.95813, 3.5865, -0.97601, 5.35748, -0.51062, 1.78903, 0.28662], "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "ys1": {"ys1": [{"curve": 0.25, "c3": 0.75}, {"time": 1.5, "offset": 32, "vertices": [1.08539, -4.91229, 4.38577, 2.46463, 1.97568, -4.62665, 1.89615, -4.66, 0.28427, -5.02245, 0.21423, -5.02617, -0.05676, -5.03056, 0.72729, -3.29205, 2.93918, 1.65167, 1.32407, -3.10062, 1.27069, -3.12302, 0.19049, -3.36583, 0.14355, -3.36835, -0.03806, -3.37131, 0.36536, -1.65457, 1.4772, 0.83011, 0.66577, -1.55826, 0.63879, -1.56979, 0.09579, -1.69147, 0.0723, -1.69296, -0.01904, -1.69455, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.29379, 5.18698, -0.22092, 5.19028, 0.05875, 5.19469, 0.10922, 5.194, 0.14734, 5.19318, 0.17383, 5.19228, -0.68054, 5.15021, -1.31871, 5.02477, -3.88492, 3.44933, -4.98895, 1.44897, -0.33655, 5.94382, -0.25305, 5.94774, 0.06729, 5.95259, 0.12518, 5.95187, 0.16913, 5.95091, 0.19911, 5.94984, -0.7798, 5.9016, -1.51099, 5.75792, -4.45169, 3.95248, -5.71695, 1.66043, -3.00574, 7.38611, -0.45081, 7.96214, -0.33908, 7.96736, 0.09015, 7.97383, 0.16763, 7.97292, 0.2265, 7.97159, 0.26671, 7.97012, -1.0446, 7.90559, -5.96334, 5.29465, -3.24908, 7.98425, -0.4873, 8.60686, -0.36658, 8.61253, 0.09741, 8.61952, 0.18121, 8.61852, 0.24487, 8.61707, 0.28831, 8.61552, -1.12918, 8.54576, -7.51477, -4.22351, -3.24908, 7.98419, -0.4873, 8.6068, -0.36658, 8.61247, 0.09744, 8.61946, 0.18121, 8.61845, 0.24481, 8.617, 0.28833, 8.61548, -8.13272, -4.57083, -3.6629, 8.57971, -3.51624, 8.64078, -0.52737, 9.31454, -0.39673, 9.32069, 0.10547, 9.32828, 0.19608, 9.32716, 0.26495, 9.32559, 0.31204, 9.32397, -9.27695, -5.21384, -4.17819, 9.78674, -4.01093, 9.85648, -0.60156, 10.625, -0.45261, 10.63202, 0.1203, 10.64066, 0.22366, 10.63939, 0.30222, 10.63757, -8.1694, -4.59143, -3.67917, 8.61838, -3.53214, 8.67966, -0.52975, 9.35667, -0.3985, 9.36273, 0.10599, 9.37024, 0.19702, 9.36926, -5.18964, -2.91684, -2.33734, 5.47491, -2.2438, 5.51382, -0.33655, 5.94382, -0.25305, 5.94774, 0.06729, 5.95259, -5.18964, -2.91684, -2.33734, 5.47491, -2.2438, 5.51382, -0.33655, 5.94382, -0.25305, 5.94774, 0.06729, 5.95259, -1.28448, 5.81302, -5.18964, -2.91684, -2.33734, 5.47491, -2.2438, 5.51382, -0.33655, 5.94382, -0.25305, 5.94774, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.81091, -1.57968, -1.26584, 2.96542, -1.21524, 2.98654, -0.18225, 3.21942, -0.13708, 3.22137, -2.26981, -1.27563, -1.02222, 2.39459, -0.98138, 2.41162, -0.14719, 2.59973, -0.11075, 2.60132, 0.02948, 2.60353, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.10663, -0.48497, 0.43317, 0.24316, 0, 0, 0, 0, 1.66248, -7.52542, 6.71872, 3.77557, 3.02667, -7.08774, 2.90466, -7.13873, 0.43549, -7.69447, 0.32803, -7.70004, 1.08539, -4.91232, 4.38586, 2.46463, 1.97565, -4.62668, 1.89621, -4.6601, 0.28427, -5.02248, 0.21423, -5.0262, -0.05682, -5.03061, 1.66248, -7.52542, 6.71872, 3.77557, 3.02667, -7.08774, 2.90466, -7.13873, 0.43549, -7.69447, 0.32803, -7.70004, -0.08685, -7.70644, 2.24591, -10.16568, 9.0762, 5.10034, 4.08856, -9.57458, 3.92386, -9.64359, 0.58826, -10.39391, 0.44299, -10.40144, -0.11743, -10.41054, 1.66248, -7.52542, 6.71872, 3.77557, 3.02667, -7.08774, 2.90466, -7.13873, 0.43549, -7.69447, 0.32803, -7.70004, -0.08685, -7.70644, -0.16202, -7.70515, 1.52234, -6.89082, 6.15231, 3.45728, 2.77145, -6.49017, 2.65973, -6.53687, 0.39874, -7.04562, 0.30017, -7.05067, -0.07956, -7.05682, -0.14813, -7.05554, 1.66248, -7.52542, 6.71872, 3.77557, 2.90466, -7.13873, 0.32803, -7.70004, -0.08685, -7.70644, -0.16202, -7.70515, 1.52234, -6.89082, 6.15231, 3.45728, 2.65973, -6.53687, 0.39874, -7.04562, 0.30017, -7.05067, -0.07956, -7.05682, -0.14813, -7.05554, -0.20062, -7.05406, 0.43549, -7.69447, -0.08685, -7.70644, -0.16202, -7.70515, -0.21906, -7.70367, 1.52234, -6.89082, 6.15231, 3.45728, 2.65973, -6.53687, 0.39874, -7.04562, 0.30017, -7.05067, -0.07956, -7.05682, -0.14813, -7.05554, -0.20062, -7.05406, -0.23633, -7.05316, 0.43549, -7.69447, -0.08685, -7.70644, -0.16202, -7.70515, -0.21906, -7.70367, -0.25793, -7.70259, 1.00974, -7.64059, -0.07956, -7.05682, -0.14813, -7.05554, -0.20062, -7.05406, -0.23633, -7.05316, 0.92458, -6.99635, -0.29486, 5.20718, -0.22186, 5.21062, 0.05896, 5.215, 0.10962, 5.21428, 0.14801, 5.21339, 0.1745, 5.21251, -0.68321, 5.17036, -1.32375, 5.04448, -3.90003, 3.46283, -5.00845, 1.45468, -0.08685, -7.70644, -0.16202, -7.70515, -0.21906, -7.70367, -0.25793, -7.70259, 1.00974, -7.64059, 5.7629, -5.11716, 7.40096, -2.14981, -0.14813, -7.05554, -0.20062, -7.05406, -0.23633, -7.05316, 0.92458, -6.99635, -0.08685, -7.70644, -0.16202, -7.70515, -0.21906, -7.70367, -0.25793, -7.70259, 1.00974, -7.64059, 1.95591, -7.45473, 5.7629, -5.11716, 7.40096, -2.14981, -0.20062, -7.05406, -0.23633, -7.05316, 0.92458, -6.99635, 1.79092, -6.82614, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.23633, -7.05316, 0.92458, -6.99635, 1.79092, -6.82614, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.36548, -1.89151, -1.51575, 3.55045, -1.45511, 3.57565, -0.21835, 3.85445, -0.16428, 3.85698, 0.04355, 3.86024, -4.51105, -2.53537, -2.03168, 4.75891, -1.95041, 4.79279, -0.29266, 5.16646, -0.22015, 5.16986, 0.05841, 5.17426, 0.1087, 5.17352, -2.81931, -1.58447, -1.26965, 2.9743, -1.21887, 2.99542, -0.18283, 3.22894, -0.13751, 3.23106, 0.03659, 3.23381, 0.06802, 3.23334, 0.09183, 3.2328, -4.51105, -2.53534, -2.03165, 4.75894, -1.95038, 4.79279, -0.29266, 5.16644, -0.22015, 5.16985, 0.05841, 5.17426, 0.1087, 5.17351, 0.14682, 5.17265, -3.3833, -1.90152, -1.4628, 3.5946, -0.21948, 3.87486, -0.1651, 3.87741, 0.04382, 3.88071, 0.08154, 3.88016, 0.11011, 3.87952, 0.12985, 3.87886, -1.22668, 3.01447, -0.18402, 3.24951, -0.1384, 3.25162, 0.0368, 3.25436, 0.06842, 3.25394, 0.09241, 3.2534, 0.10892, 3.25284, -0.42633, 3.22652], "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "offset": 32, "vertices": [1.08539, -4.91229, 4.38577, 2.46463, 1.97568, -4.62665, 1.89615, -4.66, 0.28427, -5.02245, 0.21423, -5.02617, -0.05676, -5.03056, 0.72729, -3.29205, 2.93918, 1.65167, 1.32407, -3.10062, 1.27069, -3.12302, 0.19049, -3.36583, 0.14355, -3.36835, -0.03806, -3.37131, 0.36536, -1.65457, 1.4772, 0.83011, 0.66577, -1.55826, 0.63879, -1.56979, 0.09579, -1.69147, 0.0723, -1.69296, -0.01904, -1.69455, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.29379, 5.18698, -0.22092, 5.19028, 0.05875, 5.19469, 0.10922, 5.194, 0.14734, 5.19318, 0.17383, 5.19228, -0.68054, 5.15021, -1.31871, 5.02477, -3.88492, 3.44933, -4.98895, 1.44897, -0.33655, 5.94382, -0.25305, 5.94774, 0.06729, 5.95259, 0.12518, 5.95187, 0.16913, 5.95091, 0.19911, 5.94984, -0.7798, 5.9016, -1.51099, 5.75792, -4.45169, 3.95248, -5.71695, 1.66043, -3.00574, 7.38611, -0.45081, 7.96214, -0.33908, 7.96736, 0.09015, 7.97383, 0.16763, 7.97292, 0.2265, 7.97159, 0.26671, 7.97012, -1.0446, 7.90559, -5.96334, 5.29465, -3.24908, 7.98425, -0.4873, 8.60686, -0.36658, 8.61253, 0.09741, 8.61952, 0.18121, 8.61852, 0.24487, 8.61707, 0.28831, 8.61552, -1.12918, 8.54576, -7.51477, -4.22351, -3.24908, 7.98419, -0.4873, 8.6068, -0.36658, 8.61247, 0.09744, 8.61946, 0.18121, 8.61845, 0.24481, 8.617, 0.28833, 8.61548, -8.13272, -4.57083, -3.6629, 8.57971, -3.51624, 8.64078, -0.52737, 9.31454, -0.39673, 9.32069, 0.10547, 9.32828, 0.19608, 9.32716, 0.26495, 9.32559, 0.31204, 9.32397, -9.27695, -5.21384, -4.17819, 9.78674, -4.01093, 9.85648, -0.60156, 10.625, -0.45261, 10.63202, 0.1203, 10.64066, 0.22366, 10.63939, 0.30222, 10.63757, -8.1694, -4.59143, -3.67917, 8.61838, -3.53214, 8.67966, -0.52975, 9.35667, -0.3985, 9.36273, 0.10599, 9.37024, 0.19702, 9.36926, -5.18964, -2.91684, -2.33734, 5.47491, -2.2438, 5.51382, -0.33655, 5.94382, -0.25305, 5.94774, 0.06729, 5.95259, -5.18964, -2.91684, -2.33734, 5.47491, -2.2438, 5.51382, -0.33655, 5.94382, -0.25305, 5.94774, 0.06729, 5.95259, -1.28448, 5.81302, -5.18964, -2.91684, -2.33734, 5.47491, -2.2438, 5.51382, -0.33655, 5.94382, -0.25305, 5.94774, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.81091, -1.57968, -1.26584, 2.96542, -1.21524, 2.98654, -0.18225, 3.21942, -0.13708, 3.22137, -2.26981, -1.27563, -1.02222, 2.39459, -0.98138, 2.41162, -0.14719, 2.59973, -0.11075, 2.60132, 0.02948, 2.60353, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.10663, -0.48497, 0.43317, 0.24316, 0, 0, 0, 0, 1.66248, -7.52542, 6.71872, 3.77557, 3.02667, -7.08774, 2.90466, -7.13873, 0.43549, -7.69447, 0.32803, -7.70004, 1.08539, -4.91232, 4.38586, 2.46463, 1.97565, -4.62668, 1.89621, -4.6601, 0.28427, -5.02248, 0.21423, -5.0262, -0.05682, -5.03061, 1.66248, -7.52542, 6.71872, 3.77557, 3.02667, -7.08774, 2.90466, -7.13873, 0.43549, -7.69447, 0.32803, -7.70004, -0.08685, -7.70644, 2.24591, -10.16568, 9.0762, 5.10034, 4.08856, -9.57458, 3.92386, -9.64359, 0.58826, -10.39391, 0.44299, -10.40144, -0.11743, -10.41054, 1.66248, -7.52542, 6.71872, 3.77557, 3.02667, -7.08774, 2.90466, -7.13873, 0.43549, -7.69447, 0.32803, -7.70004, -0.08685, -7.70644, -0.16202, -7.70515, 1.52234, -6.89082, 6.15231, 3.45728, 2.77145, -6.49017, 2.65973, -6.53687, 0.39874, -7.04562, 0.30017, -7.05067, -0.07956, -7.05682, -0.14813, -7.05554, 1.66248, -7.52542, 6.71872, 3.77557, 2.90466, -7.13873, 0.32803, -7.70004, -0.08685, -7.70644, -0.16202, -7.70515, 1.52234, -6.89082, 6.15231, 3.45728, 2.65973, -6.53687, 0.39874, -7.04562, 0.30017, -7.05067, -0.07956, -7.05682, -0.14813, -7.05554, -0.20062, -7.05406, 0.43549, -7.69447, -0.08685, -7.70644, -0.16202, -7.70515, -0.21906, -7.70367, 1.52234, -6.89082, 6.15231, 3.45728, 2.65973, -6.53687, 0.39874, -7.04562, 0.30017, -7.05067, -0.07956, -7.05682, -0.14813, -7.05554, -0.20062, -7.05406, -0.23633, -7.05316, 0.43549, -7.69447, -0.08685, -7.70644, -0.16202, -7.70515, -0.21906, -7.70367, -0.25793, -7.70259, 1.00974, -7.64059, -0.07956, -7.05682, -0.14813, -7.05554, -0.20062, -7.05406, -0.23633, -7.05316, 0.92458, -6.99635, -0.29486, 5.20718, -0.22186, 5.21062, 0.05896, 5.215, 0.10962, 5.21428, 0.14801, 5.21339, 0.1745, 5.21251, -0.68321, 5.17036, -1.32375, 5.04448, -3.90003, 3.46283, -5.00845, 1.45468, -0.08685, -7.70644, -0.16202, -7.70515, -0.21906, -7.70367, -0.25793, -7.70259, 1.00974, -7.64059, 5.7629, -5.11716, 7.40096, -2.14981, -0.14813, -7.05554, -0.20062, -7.05406, -0.23633, -7.05316, 0.92458, -6.99635, -0.08685, -7.70644, -0.16202, -7.70515, -0.21906, -7.70367, -0.25793, -7.70259, 1.00974, -7.64059, 1.95591, -7.45473, 5.7629, -5.11716, 7.40096, -2.14981, -0.20062, -7.05406, -0.23633, -7.05316, 0.92458, -6.99635, 1.79092, -6.82614, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.23633, -7.05316, 0.92458, -6.99635, 1.79092, -6.82614, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.36548, -1.89151, -1.51575, 3.55045, -1.45511, 3.57565, -0.21835, 3.85445, -0.16428, 3.85698, 0.04355, 3.86024, -4.51105, -2.53537, -2.03168, 4.75891, -1.95041, 4.79279, -0.29266, 5.16646, -0.22015, 5.16986, 0.05841, 5.17426, 0.1087, 5.17352, -2.81931, -1.58447, -1.26965, 2.9743, -1.21887, 2.99542, -0.18283, 3.22894, -0.13751, 3.23106, 0.03659, 3.23381, 0.06802, 3.23334, 0.09183, 3.2328, -4.51105, -2.53534, -2.03165, 4.75894, -1.95038, 4.79279, -0.29266, 5.16644, -0.22015, 5.16985, 0.05841, 5.17426, 0.1087, 5.17351, 0.14682, 5.17265, -3.3833, -1.90152, -1.4628, 3.5946, -0.21948, 3.87486, -0.1651, 3.87741, 0.04382, 3.88071, 0.08154, 3.88016, 0.11011, 3.87952, 0.12985, 3.87886, -1.22668, 3.01447, -0.18402, 3.24951, -0.1384, 3.25162, 0.0368, 3.25436, 0.06842, 3.25394, 0.09241, 3.2534, 0.10892, 3.25284, -0.42633, 3.22652], "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "zs1": {"zs1": [{"curve": 0.25, "c3": 0.75}, {"time": 1.5, "offset": 8, "vertices": [9.71643, -1.41382, 7.50586, -9.51886, 13.18427, -6.30457, 5.25757, -2.93549, 9.73724, -0.61078, 10.20276, 1.09589, 10.20276, 1.09589, 10.20276, 1.09589, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.50679, 2.44498, 1.40179, 2.50656, 1.30434, 2.55798, 1.21778, 2.60101, 1.14494, 2.63342, 1.08621, 2.65848, 1.04287, 2.67606, 1.57565, 3.20084, 1.43865, 3.26468, 1.31193, 3.31705, 1.20002, 3.35965, 1.10581, 3.39163, 1.03038, 3.41541, 0.97474, 3.43185, 2.06396, 4.19257, 1.88455, 4.27631, 1.71854, 4.34476, 1.57195, 4.40067, 1.44858, 4.44254, 1.34976, 4.47366, 1.27689, 4.49524, -4.43231, 1.72726, 2.04198, 4.14786, 1.86432, 4.23059, 1.70016, 4.29779, 1.55504, 4.35382, 1.43327, 4.39468, 1.33533, 4.42563, -3.37183, 1.31424, 1.55353, 3.15576, 1.41849, 3.21872, 1.29355, 3.27029, 1.1832, 3.31238, 1.09033, 3.3439, 1.01595, 3.36728, -3.32385, 1.29562, 1.53148, 3.11093, 1.39835, 3.17294, 1.27518, 3.22385, 1.1664, 3.26529, 1.07485, 3.29645, -3.92596, 1.53021, 1.80882, 3.67444, 1.65155, 3.74762, 1.5061, 3.8078, 1.37758, 3.85675, 1.26944, 3.89349, -5.08148, 1.98087, 2.34142, 4.75583, 2.13793, 4.85065, 1.9497, 4.92868, 1.78335, 4.99191, 1.64333, 5.03955, -3.37177, 1.3143, 1.55354, 3.15567, 1.41851, 3.21854, 1.2936, 3.27026, 1.18322, 3.31229, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.53369, -1.50934, 2.08307, 5.3443, 4.38789, -2.80722, 4.50291, -2.61905, 8.02722, -2.18903, 3.02155, 7.75235, 6.8913, -1.87933, 2.59393, 6.65546, 9.0954, 0.56036, 0.39966, 9.1044, 9.0954, 0.56036, 0.39966, 9.1044, 7.26135, 1.06042, -0.29248, 7.33432, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -9.4223, -0.03418, -0.95795, -9.37164, -1.3573, 7.49844, 6.67944, 0.29956, 6.66072, 0.58203, 3.58911, -1.39929, -1.6539, -3.35931, -1.51028, -3.42633, -1.37732, -3.48227, -1.25988, -3.52615, 5.02478, -1.95901, -2.31549, -4.70306, -2.11443, -4.79687, -1.92827, -4.87521, -1.76385, -4.93668, -1.62489, -4.98434, 6.52246, -2.54297, -3.00562, -6.10486, -2.74461, -6.22675, -2.50301, -6.32834, -2.28953, -6.40802, -2.10922, -6.47, -1.96573, -6.51474, -3.60985, -7.33209, -3.29644, -7.47842, -3.00622, -7.60077, -2.74987, -7.69614, -2.53316, -7.77075, -2.36089, -7.82446, -2.23328, -7.8616, -3.5984, -8.16379, -3.2816, -8.297, -3.00179, -8.40158, -2.76537, -8.4827, -2.57726, -8.54144, -2.43794, -8.58218, -3.03012, -7.66104, -2.77174, -7.7576, -2.55345, -7.83252, -2.37972, -7.88681, -2.25111, -7.92432, 0, 0, 0, 0, 0, 0, 0, 0, 4.06348, -0.55072, 0, 0, 0, 0, -1.03876, -3.80707, 3.67639, -1.43359, 8.26965, -2.25525, 7.15973, -6.32397, 7.04169, 6.45508, 8.26965, -2.25525, 6.6142, -8.32355, 8.9726, 5.70203, 8.20282, 0.80469, 8.20282, 0.80469, 0.06226, 8.24438, 11.81525, -0.18115, 1.42285, 11.73135, 11.81525, -0.18115, 1.42285, 11.73135, 4.47375, -1.22003, 1.68402, 4.32053, 5.36853, -1.46405, -1.7514, -11.17657, 10.92859, -2.91656, -1.67438, -6.13739, 5.9267, -2.31055, 0, 0, 0, 0, 0, 0, 0, 0, -1.3573, 7.49844, 6.67944, 0.29956, 6.66072, 0.58203, -2.04645, -7.50128, 7.24365, -2.82391, -3.33791, -6.78021, -3.04813, -6.9155, -2.77977, -7.02838, -2.54272, -7.11691, -2.34245, -7.18542, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.04645, -7.50128, 7.24365, -2.82391, -3.33791, -6.78021, -3.04813, -6.9155, -2.77977, -7.02838, -2.54272, -7.11691, -2.34245, -7.18542, 6.04468, -2.35645, -2.54344, -5.77063, -2.31953, -5.8649, -2.12177, -5.93869, -1.95465, -5.99591, -1.84424, -6.76031, 6.5282, -2.54507, -3.00819, -6.11041, -2.74698, -6.23233, -2.50517, -6.33405, -2.29156, -6.41385, -2.11107, -6.47568, 6.04468, -2.35645, -2.31953, -5.8649, -2.12177, -5.93869, -1.95465, -5.99591, 7.84528, -3.05844, -3.30111, -7.48975, -3.01054, -7.61191, -2.75381, -7.70779, -2.53692, -7.78207, -2.3644, -7.83624, -1.90482, -4.32166, -1.73711, -4.3924, -1.58901, -4.44754, -1.46376, -4.49051, 5.21112, -2.03156, -2.19274, -4.97495, -1.99973, -5.05609, -1.8292, -5.11981, -1.68516, -5.16913, -1.57054, -5.20508, -1.73711, -4.3924, -1.58901, -4.44754, -1.46376, -4.49051, -1.36425, -4.52161, -1.29044, -4.543, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.33334, -1.3624, -2.28659, -1.43854, -2.24495, -1.50281, -2.2114, -1.55161, -2.1858, -1.58694], "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "offset": 8, "vertices": [9.71643, -1.41382, 7.50586, -9.51886, 13.18427, -6.30457, 5.25757, -2.93549, 9.73724, -0.61078, 10.20276, 1.09589, 10.20276, 1.09589, 10.20276, 1.09589, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.50679, 2.44498, 1.40179, 2.50656, 1.30434, 2.55798, 1.21778, 2.60101, 1.14494, 2.63342, 1.08621, 2.65848, 1.04287, 2.67606, 1.57565, 3.20084, 1.43865, 3.26468, 1.31193, 3.31705, 1.20002, 3.35965, 1.10581, 3.39163, 1.03038, 3.41541, 0.97474, 3.43185, 2.06396, 4.19257, 1.88455, 4.27631, 1.71854, 4.34476, 1.57195, 4.40067, 1.44858, 4.44254, 1.34976, 4.47366, 1.27689, 4.49524, -4.43231, 1.72726, 2.04198, 4.14786, 1.86432, 4.23059, 1.70016, 4.29779, 1.55504, 4.35382, 1.43327, 4.39468, 1.33533, 4.42563, -3.37183, 1.31424, 1.55353, 3.15576, 1.41849, 3.21872, 1.29355, 3.27029, 1.1832, 3.31238, 1.09033, 3.3439, 1.01595, 3.36728, -3.32385, 1.29562, 1.53148, 3.11093, 1.39835, 3.17294, 1.27518, 3.22385, 1.1664, 3.26529, 1.07485, 3.29645, -3.92596, 1.53021, 1.80882, 3.67444, 1.65155, 3.74762, 1.5061, 3.8078, 1.37758, 3.85675, 1.26944, 3.89349, -5.08148, 1.98087, 2.34142, 4.75583, 2.13793, 4.85065, 1.9497, 4.92868, 1.78335, 4.99191, 1.64333, 5.03955, -3.37177, 1.3143, 1.55354, 3.15567, 1.41851, 3.21854, 1.2936, 3.27026, 1.18322, 3.31229, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.53369, -1.50934, 2.08307, 5.3443, 4.38789, -2.80722, 4.50291, -2.61905, 8.02722, -2.18903, 3.02155, 7.75235, 6.8913, -1.87933, 2.59393, 6.65546, 9.0954, 0.56036, 0.39966, 9.1044, 9.0954, 0.56036, 0.39966, 9.1044, 7.26135, 1.06042, -0.29248, 7.33432, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -9.4223, -0.03418, -0.95795, -9.37164, -1.3573, 7.49844, 6.67944, 0.29956, 6.66072, 0.58203, 3.58911, -1.39929, -1.6539, -3.35931, -1.51028, -3.42633, -1.37732, -3.48227, -1.25988, -3.52615, 5.02478, -1.95901, -2.31549, -4.70306, -2.11443, -4.79687, -1.92827, -4.87521, -1.76385, -4.93668, -1.62489, -4.98434, 6.52246, -2.54297, -3.00562, -6.10486, -2.74461, -6.22675, -2.50301, -6.32834, -2.28953, -6.40802, -2.10922, -6.47, -1.96573, -6.51474, -3.60985, -7.33209, -3.29644, -7.47842, -3.00622, -7.60077, -2.74987, -7.69614, -2.53316, -7.77075, -2.36089, -7.82446, -2.23328, -7.8616, -3.5984, -8.16379, -3.2816, -8.297, -3.00179, -8.40158, -2.76537, -8.4827, -2.57726, -8.54144, -2.43794, -8.58218, -3.03012, -7.66104, -2.77174, -7.7576, -2.55345, -7.83252, -2.37972, -7.88681, -2.25111, -7.92432, 0, 0, 0, 0, 0, 0, 0, 0, 4.06348, -0.55072, 0, 0, 0, 0, -1.03876, -3.80707, 3.67639, -1.43359, 8.26965, -2.25525, 7.15973, -6.32397, 7.04169, 6.45508, 8.26965, -2.25525, 6.6142, -8.32355, 8.9726, 5.70203, 8.20282, 0.80469, 8.20282, 0.80469, 0.06226, 8.24438, 11.81525, -0.18115, 1.42285, 11.73135, 11.81525, -0.18115, 1.42285, 11.73135, 4.47375, -1.22003, 1.68402, 4.32053, 5.36853, -1.46405, -1.7514, -11.17657, 10.92859, -2.91656, -1.67438, -6.13739, 5.9267, -2.31055, 0, 0, 0, 0, 0, 0, 0, 0, -1.3573, 7.49844, 6.67944, 0.29956, 6.66072, 0.58203, -2.04645, -7.50128, 7.24365, -2.82391, -3.33791, -6.78021, -3.04813, -6.9155, -2.77977, -7.02838, -2.54272, -7.11691, -2.34245, -7.18542, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.04645, -7.50128, 7.24365, -2.82391, -3.33791, -6.78021, -3.04813, -6.9155, -2.77977, -7.02838, -2.54272, -7.11691, -2.34245, -7.18542, 6.04468, -2.35645, -2.54344, -5.77063, -2.31953, -5.8649, -2.12177, -5.93869, -1.95465, -5.99591, -1.84424, -6.76031, 6.5282, -2.54507, -3.00819, -6.11041, -2.74698, -6.23233, -2.50517, -6.33405, -2.29156, -6.41385, -2.11107, -6.47568, 6.04468, -2.35645, -2.31953, -5.8649, -2.12177, -5.93869, -1.95465, -5.99591, 7.84528, -3.05844, -3.30111, -7.48975, -3.01054, -7.61191, -2.75381, -7.70779, -2.53692, -7.78207, -2.3644, -7.83624, -1.90482, -4.32166, -1.73711, -4.3924, -1.58901, -4.44754, -1.46376, -4.49051, 5.21112, -2.03156, -2.19274, -4.97495, -1.99973, -5.05609, -1.8292, -5.11981, -1.68516, -5.16913, -1.57054, -5.20508, -1.73711, -4.3924, -1.58901, -4.44754, -1.46376, -4.49051, -1.36425, -4.52161, -1.29044, -4.543, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.33334, -1.3624, -2.28659, -1.43854, -2.24495, -1.50281, -2.2114, -1.55161, -2.1858, -1.58694], "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "zy": {"zy": [{"curve": 0.25, "c3": 0.75}, {"time": 0.9333, "offset": 10, "vertices": [0.64224, 1.76709, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.37744, -1.03754, -3.26154, -2.27429, -8.14642, -0.75378, 0, 0, -3.29144, -2.35712, -4.18204, -2.39087, -4.20996, 1.20282, -4.25751, 3.07983, -4.81635, 2.75848], "curve": "stepped"}, {"time": 1.7, "offset": 10, "vertices": [0.64224, 1.76709, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.37744, -1.03754, -3.26154, -2.27429, -8.14642, -0.75378, 0, 0, -3.29144, -2.35712, -4.18204, -2.39087, -4.20996, 1.20282, -4.25751, 3.07983, -4.81635, 2.75848], "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 2.8667, "offset": 10, "vertices": [0.1441, 0.3965, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.08469, -0.2328, -0.73182, -0.5103, -1.82787, -0.16913, 0, 0, -0.73853, -0.52888, -0.93836, -0.53646, -0.94462, 0.26989, -0.95529, 0.69105, -1.08068, 0.61894], "curve": 0.351, "c2": 0.4, "c3": 0.687, "c4": 0.75}, {"time": 3.1667, "vertices": [-2.42514, -6.66821, -2.7709, -7.6189, -2.93881, -8.0816, -1.57254, 3.57477, -1.9104, 7.00006, -0.27787, 3.5081, 1.95425, 3.04883, 0, 0, 0, 0, -4.80835, -13.22034, -5.53513, -15.9386, -1.88837, -13.35114, -2.6825, -7.3761, -4.30389, -11.83392, -6.74612, -19.48779, -3.24177, -16.81177, -3.7157, -10.22205, -6.57962, -16.99279, -4.60183, -7.55168, -3.6467, 0.80876, -3.73971, 0.69125, -4.97066, -6.87937], "curve": 0.369, "c2": 0.63, "c3": 0.707}, {"time": 3.5667, "offset": 10, "vertices": [0.08355, 0.22989, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.0491, -0.13498, -0.42432, -0.29588, -1.05982, -0.09806, 0, 0, -0.42821, -0.30665, -0.54407, -0.31104, -0.5477, 0.15648, -0.55389, 0.40068, -0.62659, 0.35887], "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 4.3, "offset": 10, "vertices": [0.64224, 1.76709, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.37744, -1.03754, -3.26154, -2.27429, -8.14642, -0.75378, 0, 0, -3.29144, -2.35712, -4.18204, -2.39087, -4.20996, 1.20282, -4.25751, 3.07983, -4.81635, 2.75848], "curve": "stepped"}, {"time": 5.0667, "offset": 10, "vertices": [0.64224, 1.76709, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.37744, -1.03754, -3.26154, -2.27429, -8.14642, -0.75378, 0, 0, -3.29144, -2.35712, -4.18204, -2.39087, -4.20996, 1.20282, -4.25751, 3.07983, -4.81635, 2.75848], "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "t2": {"t2": [{"curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.9333, "vertices": [13.37635, -6.12567, 11.2971, -9.42395, 13.28173, -6.33438, 10.43037, -6.31382, 8.40524, -8.83015, 10.33247, -6.47623, 7.10246, -4.59587, 5.64693, -6.29684, 7.0322, -4.70638, 6.11975, -3.28324, 5.0449, -4.77218, 6.07017, -3.37871, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.69296, -0.48422, 2.72264, -0.21272, 2.69118, 0.4646, 3.09732, -4.54634, 3.52697, -4.21543, 4.46338, -3.21294, 3.16745, -7.19021, 3.85737, -6.83911, 5.43384, -5.67332, 6.96612, -9.55583, -0.53157, -9.12561, 1.74917, -8.9765, -2.5801, -5.29059, -2.04885, -5.51671, -0.61679, -5.85697, -4.15713, -0.1767, -4.12405, -0.58314, -3.85048, -1.59224, -2.31358, -3.22514, -1.98747, -3.43478, -1.07417, -3.8249, -3.75043, -5.99914, -5.8476, -2.77593, -5.54857, -3.33639, -4.54803, -4.61281, -4.56481, -0.12578, -4.53392, -0.57292, -4.25078, -1.68378, -2.41307, 0.63124, -2.46747, 0.39284, -2.48766, -0.23587, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.29333, 4.47679, -0.09692, 1.49583, 2.88184, 4.07812, 1.32056, 4.59874, -0.22266, 3.39721, -0.24915, 3.80297, 0.64685, 10.11107, -0.40979, 6.24939, -0.4834, 7.36987, -0.39722, 6.05603, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7.17146, -0.19828, 6.8715, -2.0724, 7.1698, -0.31034, 10.26663, 0.25565, 9.97725, -2.44911, 10.27263, 0.09519, 12.99288, -1.85198, 12.05253, -5.2, 12.96438, -2.05513, 12.98083, -9.33338, 14.89055, -5.82891, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.11878, 1.80221, -1.33128, 1.22016, -0.80652, 1.61529, -0.96309, 1.52843, 0, 0, 0, 0, 0, 0, 0, 0, -0.12243, 1.85848, -1.37299, 1.2583, -0.8319, 1.66566, -0.99317, 1.57598, -1.35325, 1.27955, -0.02722, 0.41406, -0.30603, 0.2804, -0.22119, 0.35101, -0.14221, 2.16186, -1.59778, 1.46405, -0.97009, 1.93802, -0.09503, 1.44188, -0.64517, 1.29232, -0.77052, 1.22271, -0.23862, 3.6327, -2.68348, 2.45981, -1.62723, 3.25609, -1.94158, 3.07992, -2.6448, 2.50149, -0.49087, 0.4498, -0.29668, 0.59556, -0.35506, 0.56384, -0.21753, 3.31335, -2.44824, 2.24371, -1.48572, 2.97, -1.77051, 2.80884, -0.19263, 2.92968, -2.16486, 1.98407, -1.31384, 2.62622, -0.01857, 0.27593, -0.20384, 0.18669, -0.12251, 0.24718, -0.14741, 0.23441, -0.201, 0.18985, -0.19824, 3.02063, 0, 0, 0, 0, 0, 0, 0, 0, -3.26337, 0.8722, -3.34356, -0.46123, -3.28315, -0.78795, -2.98559, -1.57902, -0.25684, 3.92272, -2.89905, 2.65637, -1.7605, 3.51636, -2.09595, 3.32513, 0.08362, 4.92357, -3.34424, 3.61487, -1.83374, 4.57111, -2.27319, 4.36725, -0.2865, 4.38158, -3.23877, 2.96698, -1.96704, 3.92758, -2.34143, 3.71362, -1.45972, 4.15952, -3.93201, 1.99603, -2.96289, 3.26889, -3.26404, 2.95966], "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1.5, "vertices": [18.25489, -8.27203, 15.44016, -12.77588, 18.1267, -8.55685, 14.18519, -8.42015, 11.47506, -11.84742, 14.05439, -8.64066, 9.6616, -5.92576, 7.76778, -8.2502, 9.5709, -6.0757, 8.35645, -4.07852, 6.99521, -6.12545, 8.29466, -4.20848, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.29184, -0.62637, 1.32823, -0.48286, 1.40395, -0.15753, 1.71402, -5.4229, 2.22313, -5.21433, 3.44495, -4.5186, 1.7612, -8.54254, 2.5771, -8.31329, 4.5564, -7.43388, 6.31183, -12.0344, -2.6302, -10.95087, 0.16641, -11.28052, -4.99379, -6.22412, -4.37173, -6.67375, -2.58371, -7.56864, -6.78433, -0.1705, -6.75271, -0.82642, -6.33962, -2.49409, -5.47606, -3.58079, -5.11174, -4.09182, -3.94075, -5.25044, -7.29266, -7.90753, -9.85126, -2.97136, -9.52672, -3.91634, -8.26092, -6.17641, -8.22277, 0.29649, -8.22813, -0.50346, -7.8488, -2.547, -5.54002, 1.20381, -5.64972, 0.66371, -5.64269, -0.77755, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.29333, 4.47679, -0.09692, 1.49583, 2.88184, 4.07812, 1.32056, 4.59874, -0.22266, 3.39721, -0.24915, 3.80297, 0.64685, 10.11107, -0.40979, 6.24939, -0.4834, 7.36987, -0.39722, 6.05603, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9.92311, 0.13527, 9.6154, -2.47176, 9.927, -0.01957, 14.20773, 0.64371, 13.8835, -3.10902, 14.22002, 0.42209, 17.88747, -2.36498, 16.64128, -6.98046, 17.85061, -2.64437, 17.76489, -12.71473, 20.36284, -7.92071, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.11878, 1.80221, -1.33128, 1.22016, -0.80652, 1.61529, -0.96309, 1.52843, 0, 0, 0, 0, 0, 0, 0, 0, -0.12243, 1.85848, -1.37299, 1.2583, -0.8319, 1.66566, -0.99317, 1.57598, -1.35325, 1.27955, -0.02722, 0.41406, -0.30603, 0.2804, -0.22119, 0.35101, -0.14221, 2.16186, -1.59778, 1.46405, -0.97009, 1.93802, -0.09503, 1.44188, -0.64517, 1.29232, -0.77052, 1.22271, -0.23862, 3.6327, -2.68348, 2.45981, -1.62723, 3.25609, -1.94158, 3.07992, -2.6448, 2.50149, -0.49087, 0.4498, -0.29668, 0.59556, -0.35506, 0.56384, -0.21753, 3.31335, -2.44824, 2.24371, -1.48572, 2.97, -1.77051, 2.80884, -0.19263, 2.92968, -2.16486, 1.98407, -1.31384, 2.62622, -0.01857, 0.27593, -0.20384, 0.18669, -0.12251, 0.24718, -0.14741, 0.23441, -0.201, 0.18985, -0.19824, 3.02063, 0, 0, 0, 0, 0, 0, 0, 0, -3.26337, 0.8722, -3.34356, -0.46123, -3.28315, -0.78795, -2.98559, -1.57902, -0.25684, 3.92272, -2.89905, 2.65637, -1.7605, 3.51636, -2.09595, 3.32513, 0.08362, 4.92357, -3.34424, 3.61487, -1.83374, 4.57111, -2.27319, 4.36725, -0.2865, 4.38158, -3.23877, 2.96698, -1.96704, 3.92758, -2.34143, 3.71362, -1.45972, 4.15952, -3.93201, 1.99603, -2.96289, 3.26889, -3.26404, 2.95966], "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.3667, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 4.3, "vertices": [13.37635, -6.12567, 11.2971, -9.42395, 13.28173, -6.33438, 10.43037, -6.31382, 8.40524, -8.83015, 10.33247, -6.47623, 7.10246, -4.59587, 5.64693, -6.29684, 7.0322, -4.70638, 6.11975, -3.28324, 5.0449, -4.77218, 6.07017, -3.37871, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.69296, -0.48422, 2.72264, -0.21272, 2.69118, 0.4646, 3.09732, -4.54634, 3.52697, -4.21543, 4.46338, -3.21294, 3.16745, -7.19021, 3.85737, -6.83911, 5.43384, -5.67332, 6.96612, -9.55583, -0.53157, -9.12561, 1.74917, -8.9765, -2.5801, -5.29059, -2.04885, -5.51671, -0.61679, -5.85697, -4.15713, -0.1767, -4.12405, -0.58314, -3.85048, -1.59224, -2.31358, -3.22514, -1.98747, -3.43478, -1.07417, -3.8249, -3.75043, -5.99914, -5.8476, -2.77593, -5.54857, -3.33639, -4.54803, -4.61281, -4.56481, -0.12578, -4.53392, -0.57292, -4.25078, -1.68378, -2.41307, 0.63124, -2.46747, 0.39284, -2.48766, -0.23587, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.29333, 4.47679, -0.09692, 1.49583, 2.88184, 4.07812, 1.32056, 4.59874, -0.22266, 3.39721, -0.24915, 3.80297, 0.64685, 10.11107, -0.40979, 6.24939, -0.4834, 7.36987, -0.39722, 6.05603, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7.17146, -0.19828, 6.8715, -2.0724, 7.1698, -0.31034, 10.26663, 0.25565, 9.97725, -2.44911, 10.27263, 0.09519, 12.99288, -1.85198, 12.05253, -5.2, 12.96438, -2.05513, 12.98083, -9.33338, 14.89055, -5.82891, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.11878, 1.80221, -1.33128, 1.22016, -0.80652, 1.61529, -0.96309, 1.52843, 0, 0, 0, 0, 0, 0, 0, 0, -0.12243, 1.85848, -1.37299, 1.2583, -0.8319, 1.66566, -0.99317, 1.57598, -1.35325, 1.27955, -0.02722, 0.41406, -0.30603, 0.2804, -0.22119, 0.35101, -0.14221, 2.16186, -1.59778, 1.46405, -0.97009, 1.93802, -0.09503, 1.44188, -0.64517, 1.29232, -0.77052, 1.22271, -0.23862, 3.6327, -2.68348, 2.45981, -1.62723, 3.25609, -1.94158, 3.07992, -2.6448, 2.50149, -0.49087, 0.4498, -0.29668, 0.59556, -0.35506, 0.56384, -0.21753, 3.31335, -2.44824, 2.24371, -1.48572, 2.97, -1.77051, 2.80884, -0.19263, 2.92968, -2.16486, 1.98407, -1.31384, 2.62622, -0.01857, 0.27593, -0.20384, 0.18669, -0.12251, 0.24718, -0.14741, 0.23441, -0.201, 0.18985, -0.19824, 3.02063, 0, 0, 0, 0, 0, 0, 0, 0, -3.26337, 0.8722, -3.34356, -0.46123, -3.28315, -0.78795, -2.98559, -1.57902, -0.25684, 3.92272, -2.89905, 2.65637, -1.7605, 3.51636, -2.09595, 3.32513, 0.08362, 4.92357, -3.34424, 3.61487, -1.83374, 4.57111, -2.27319, 4.36725, -0.2865, 4.38158, -3.23877, 2.96698, -1.96704, 3.92758, -2.34143, 3.71362, -1.45972, 4.15952, -3.93201, 1.99603, -2.96289, 3.26889, -3.26404, 2.95966], "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 4.8667, "vertices": [18.25489, -8.27203, 15.44016, -12.77588, 18.1267, -8.55685, 14.18519, -8.42015, 11.47506, -11.84742, 14.05439, -8.64066, 9.6616, -5.92576, 7.76778, -8.2502, 9.5709, -6.0757, 8.35645, -4.07852, 6.99521, -6.12545, 8.29466, -4.20848, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.29184, -0.62637, 1.32823, -0.48286, 1.40395, -0.15753, 1.71402, -5.4229, 2.22313, -5.21433, 3.44495, -4.5186, 1.7612, -8.54254, 2.5771, -8.31329, 4.5564, -7.43388, 6.31183, -12.0344, -2.6302, -10.95087, 0.16641, -11.28052, -4.99379, -6.22412, -4.37173, -6.67375, -2.58371, -7.56864, -6.78433, -0.1705, -6.75271, -0.82642, -6.33962, -2.49409, -5.47606, -3.58079, -5.11174, -4.09182, -3.94075, -5.25044, -7.29266, -7.90753, -9.85126, -2.97136, -9.52672, -3.91634, -8.26092, -6.17641, -8.22277, 0.29649, -8.22813, -0.50346, -7.8488, -2.547, -5.54002, 1.20381, -5.64972, 0.66371, -5.64269, -0.77755, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.29333, 4.47679, -0.09692, 1.49583, 2.88184, 4.07812, 1.32056, 4.59874, -0.22266, 3.39721, -0.24915, 3.80297, 0.64685, 10.11107, -0.40979, 6.24939, -0.4834, 7.36987, -0.39722, 6.05603, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9.92311, 0.13527, 9.6154, -2.47176, 9.927, -0.01957, 14.20773, 0.64371, 13.8835, -3.10902, 14.22002, 0.42209, 17.88747, -2.36498, 16.64128, -6.98046, 17.85061, -2.64437, 17.76489, -12.71473, 20.36284, -7.92071, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.11878, 1.80221, -1.33128, 1.22016, -0.80652, 1.61529, -0.96309, 1.52843, 0, 0, 0, 0, 0, 0, 0, 0, -0.12243, 1.85848, -1.37299, 1.2583, -0.8319, 1.66566, -0.99317, 1.57598, -1.35325, 1.27955, -0.02722, 0.41406, -0.30603, 0.2804, -0.22119, 0.35101, -0.14221, 2.16186, -1.59778, 1.46405, -0.97009, 1.93802, -0.09503, 1.44188, -0.64517, 1.29232, -0.77052, 1.22271, -0.23862, 3.6327, -2.68348, 2.45981, -1.62723, 3.25609, -1.94158, 3.07992, -2.6448, 2.50149, -0.49087, 0.4498, -0.29668, 0.59556, -0.35506, 0.56384, -0.21753, 3.31335, -2.44824, 2.24371, -1.48572, 2.97, -1.77051, 2.80884, -0.19263, 2.92968, -2.16486, 1.98407, -1.31384, 2.62622, -0.01857, 0.27593, -0.20384, 0.18669, -0.12251, 0.24718, -0.14741, 0.23441, -0.201, 0.18985, -0.19824, 3.02063, 0, 0, 0, 0, 0, 0, 0, 0, -3.26337, 0.8722, -3.34356, -0.46123, -3.28315, -0.78795, -2.98559, -1.57902, -0.25684, 3.92272, -2.89905, 2.65637, -1.7605, 3.51636, -2.09595, 3.32513, 0.08362, 4.92357, -3.34424, 3.61487, -1.83374, 4.57111, -2.27319, 4.36725, -0.2865, 4.38158, -3.23877, 2.96698, -1.96704, 3.92758, -2.34143, 3.71362, -1.45972, 4.15952, -3.93201, 1.99603, -2.96289, 3.26889, -3.26404, 2.95966], "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 6.6667}]}}}}, "animation2": {"slots": {"bg1": {"attachment": [{"name": null}]}}, "bones": {"st2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "ys5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "ys6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "bozi": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "t1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 1.2, "curve": "stepped"}, {"time": 1.8667, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "angle": 1.2, "curve": "stepped"}, {"time": 5.2667, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}], "shear": [{"curve": 0.25, "c3": 0.75}, {"time": 0.9, "y": 1.2, "curve": "stepped"}, {"time": 1.8667, "y": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "y": 1.2, "curve": "stepped"}, {"time": 5.2667, "y": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "st12": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "st": {"translate": [{"x": -0.05, "y": -1, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 1.3, "x": -0.78, "y": -15.97, "curve": 0.25, "c3": 0.75}, {"time": 3.0667, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 3.3667, "x": -0.05, "y": -1, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 4.7, "x": -0.78, "y": -15.97, "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 6.6667, "x": -0.05, "y": -1}], "shear": [{"curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "x": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "yy": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 2.81, "y": 4.41, "curve": "stepped"}, {"time": 1.5, "x": 2.81, "y": 4.41, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "x": 2.81, "y": 4.41, "curve": "stepped"}, {"time": 4.9, "x": 2.81, "y": 4.41, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 0.974, "curve": "stepped"}, {"time": 1.5, "x": 0.974, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "x": 0.974, "curve": "stepped"}, {"time": 4.9, "x": 0.974, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "zy": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": 3.8, "y": 6.08, "curve": "stepped"}, {"time": 1.7, "x": 3.8, "y": 6.08, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "x": 3.8, "y": 6.08, "curve": "stepped"}, {"time": 5.0667, "x": 3.8, "y": 6.08, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "t3": {"shear": [{"curve": 0.25, "c3": 0.75}, {"time": 0.9333, "y": -1.2, "curve": "stepped"}, {"time": 1.5, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "y": -1.2, "curve": "stepped"}, {"time": 4.8667, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "zs1": {"rotate": [{"angle": 0.47, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 3.6, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.3667, "angle": 0.47, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.2, "angle": 3.6, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 6.6667, "angle": 0.47}]}, "zs2": {"rotate": [{"angle": -0.22, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": -1.2, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 3.3667, "angle": -0.22, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.7667, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "angle": -1.2, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 6.6667, "angle": -0.22}]}, "st13": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}], "scale": [{"x": 1.02, "y": 1.06}]}, "st14": {"rotate": [{"angle": -0.51, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": -0.51, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -0.51}]}, "st15": {"rotate": [{"angle": -0.47, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.2333, "angle": -0.51, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 3.3667, "angle": -0.47, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 3.6, "angle": -0.51, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 6.6667, "angle": -0.47}]}, "st16": {"rotate": [{"angle": -0.39, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4667, "angle": -0.51, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3667, "angle": -0.39, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8333, "angle": -0.51, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "angle": -0.39}]}, "st17": {"rotate": [{"angle": -0.29, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.6667, "angle": -0.51, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 3.3667, "angle": -0.29, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 4.0667, "angle": -0.51, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 6.6667, "angle": -0.29}]}, "st18": {"rotate": [{"angle": -0.19, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.9, "angle": -0.51, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.3667, "angle": -0.19, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.2667, "angle": -0.51, "curve": 0.25, "c3": 0.75}, {"time": 5.9667, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6.6667, "angle": -0.19}]}, "st19": {"rotate": [{"angle": -0.09, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.1333, "angle": -0.51, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 3.3667, "angle": -0.09, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.5, "angle": -0.51, "curve": 0.25, "c3": 0.75}, {"time": 6.2333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 6.6667, "angle": -0.09}]}, "zs4": {"translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.7667, "x": -0.8, "y": -1.84, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.5, "x": -1.6, "y": -3.67, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.1333, "x": -0.8, "y": -1.84, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.8667, "x": -1.6, "y": -3.67, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 1.06, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "x": 1.06, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "st5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 1.02, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": 1.02, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}], "scale": [{"x": 1.02}]}, "st6": {"rotate": [{"angle": 0.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": 1.02, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 3.3667, "angle": 0.06, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.5667, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "angle": 1.02, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 6.6667, "angle": 0.06}]}, "st7": {"rotate": [{"angle": 0.19, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": 1.02, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 3.3667, "angle": 0.19, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.7667, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "angle": 1.02, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 6.6667, "angle": 0.19}]}, "st8": {"rotate": [{"angle": 0.34, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 1.02, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 3.3667, "angle": 0.34, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.9333, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": 1.02, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 6.6667, "angle": 0.34}]}, "st9": {"rotate": [{"angle": 0.51, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "angle": 1.02, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3667, "angle": 0.51, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.1333, "curve": 0.25, "c3": 0.75}, {"time": 5.7667, "angle": 1.02, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "angle": 0.51}]}, "zs3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -6, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "angle": -6, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "zs5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 7.2, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": 7.2, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "zs6": {"rotate": [{"angle": -41.97, "curve": 0.349, "c2": 0.65, "c3": 0.683}, {"time": 0.2, "angle": -43.2, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": 2.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.5667, "angle": -43.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.0667, "angle": 2.4, "curve": 0.366, "c2": 0.46, "c3": 0.732, "c4": 0.91}, {"time": 6.6667, "angle": -41.97}]}, "zs10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -0.78, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "angle": -0.78, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3, "x": 0.94, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "x": 0.94, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "zs11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -14.4, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": -14.4, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.1, "x": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.4667, "x": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "zs8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -4.7, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -4.7, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": 0.638, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 0.638, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "zs9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -24.2, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -24.2, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}], "scale": [{"x": 0.76, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": 0.617, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "x": 0.76, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 0.617, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": 0.76}]}, "ys7": {"rotate": [{}, {"time": 0.7667, "angle": -10.8, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -18, "curve": "stepped"}, {"time": 1.7, "angle": -18, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "angle": -9.6}, {"time": 3.3667}, {"time": 4.1333, "angle": -10.8, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -18, "curve": "stepped"}, {"time": 5.0667, "angle": -18, "curve": 0.25, "c3": 0.75}, {"time": 5.7667, "angle": -9.6}, {"time": 6.6667}]}, "ys10": {"rotate": [{"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -10.8, "curve": "stepped"}, {"time": 1.4333, "angle": -10.8, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "curve": "stepped"}, {"time": 4.1333, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "angle": -10.8, "curve": "stepped"}, {"time": 4.8, "angle": -10.8, "curve": 0.25, "c3": 0.75}, {"time": 5.7667}]}, "ys9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -8.4, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "angle": -8.4, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "ys8": {"rotate": [{"angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 1.01, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": 1.01, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -0.35}]}, "ys11": {"rotate": [{"angle": -0.31, "curve": 0.354, "c2": 0.65, "c3": 0.689}, {"time": 0.1, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 1.01, "curve": 0.246, "c3": 0.72, "c4": 0.87}, {"time": 3.3667, "angle": -0.31, "curve": 0.354, "c2": 0.65, "c3": 0.689}, {"time": 3.5, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 1.01, "curve": 0.246, "c3": 0.72, "c4": 0.87}, {"time": 6.6667, "angle": -0.31}]}, "ys12": {"rotate": [{"angle": -0.23, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.2333, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": 1.01, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 3.3667, "angle": -0.23, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 3.6, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "angle": 1.01, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 6.6667, "angle": -0.23}]}, "ys13": {"rotate": [{"angle": -0.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.3333, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 1.01, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 3.3667, "angle": -0.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 3.7, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "angle": 1.01, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 6.6667, "angle": -0.13}]}, "ys14": {"rotate": [{"angle": -0.02, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4667, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": 1.01, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3667, "angle": -0.02, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8333, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "angle": 1.01, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "angle": -0.02}]}, "ys15": {"rotate": [{"angle": 0.11, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5667, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 1.01, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 3.3667, "angle": 0.11, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.9333, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": 1.01, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 6.6667, "angle": 0.11}]}, "ys16": {"rotate": [{"angle": 0.24, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.6667, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": 1.01, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 3.3667, "angle": 0.24, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 4.0667, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": 1.01, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 6.6667, "angle": 0.24}]}, "ys17": {"rotate": [{"angle": 0.38, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 0.8, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": 1.01, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 3.3667, "angle": 0.38, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 4.1667, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 5.8, "angle": 1.01, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 6.6667, "angle": 0.38}]}, "ys18": {"rotate": [{"angle": 0.51, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.9, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": 1.01, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.3667, "angle": 0.51, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.2667, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 5.9667, "angle": 1.01, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6.6667, "angle": 0.51}]}, "ys19": {"rotate": [{"angle": 0.64, "curve": 0.35, "c2": 0.39, "c3": 0.757}, {"time": 1, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": 1.01, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 3.3667, "angle": 0.64, "curve": 0.35, "c2": 0.39, "c3": 0.757}, {"time": 4.4, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "angle": 1.01, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 6.6667, "angle": 0.64}]}, "ys20": {"rotate": [{"angle": 0.76, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.1333, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "angle": 1.01, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 3.3667, "angle": 0.76, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.5, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 6.2333, "angle": 1.01, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 6.6667, "angle": 0.76}]}, "ys21": {"rotate": [{"angle": 0.87, "curve": 0.313, "c2": 0.26, "c3": 0.757}, {"time": 1.2333, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 1.01, "curve": 0.289, "c3": 0.628, "c4": 0.38}, {"time": 3.3667, "angle": 0.87, "curve": 0.313, "c2": 0.26, "c3": 0.757}, {"time": 4.6, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 6.3667, "angle": 1.01, "curve": 0.289, "c3": 0.628, "c4": 0.38}, {"time": 6.6667, "angle": 0.87}]}, "zs7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -2.53, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": -2.53, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}], "scale": [{"x": 1.14}]}, "zs13": {"rotate": [{"angle": -0.11, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -2.53, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3667, "angle": -0.11, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5333, "curve": 0.25, "c3": 0.75}, {"time": 5.0333, "angle": -2.53, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "angle": -0.11}]}, "zs14": {"rotate": [{"angle": -0.33, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -2.53, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.3667, "angle": -0.33, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.2, "angle": -2.53, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 6.6667, "angle": -0.33}]}, "zs15": {"rotate": [{"angle": -0.61, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": -2.53, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3667, "angle": -0.61, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8333, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "angle": -2.53, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "angle": -0.61}]}, "zs16": {"rotate": [{"angle": -0.93, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "angle": -2.53, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.3667, "angle": -0.93, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.9667, "curve": 0.25, "c3": 0.75}, {"time": 5.6, "angle": -2.53, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6.6667, "angle": -0.93}]}, "zs17": {"rotate": [{"angle": -1.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "angle": -2.53, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3667, "angle": -1.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.1333, "curve": 0.25, "c3": 0.75}, {"time": 5.7667, "angle": -2.53, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "angle": -1.27}]}, "zs18": {"rotate": [{"angle": -1.6, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": -2.53, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.3667, "angle": -1.6, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.2667, "curve": 0.25, "c3": 0.75}, {"time": 5.9667, "angle": -2.53, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6.6667, "angle": -1.6}]}, "st10": {"scale": [{"x": 1.02}], "shear": [{"curve": 0.25, "c3": 0.75}, {"time": 1.5, "y": 6, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "y": 6, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "ys1": {"translate": [{"x": -0.26, "y": -0.19, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -6.01, "y": -4.5, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3667, "x": -0.26, "y": -0.19, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5333, "curve": 0.25, "c3": 0.75}, {"time": 5.0333, "x": -6.01, "y": -4.5, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "x": -0.26, "y": -0.19}]}, "st1": {"translate": [{"x": -0.26, "y": -0.19, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667}, {"time": 1.6667, "x": -6.01, "y": -4.5, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3667, "x": -0.26, "y": -0.19, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5333}, {"time": 5.0333, "x": -6.01, "y": -4.5, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "x": -0.26, "y": -0.19}]}, "ys2": {"rotate": [{"angle": 0.16, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 1.2, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.3667, "angle": 0.16, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.2, "angle": 1.2, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 6.6667, "angle": 0.16}]}, "st3": {"rotate": [{"angle": 0.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7667, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3667, "angle": 0.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.1333, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 5.7667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "angle": 0.6}]}, "t9": {"rotate": [{"angle": -0.18, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": -2.19, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 3.3667, "angle": -0.18, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "angle": -2.19, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 6.6667, "angle": -0.18}]}, "t10": {"rotate": [{"angle": -0.53, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": -2.19, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3667, "angle": -0.53, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8333, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "angle": -2.19, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "angle": -0.53}]}, "t11": {"rotate": [{"angle": -0.95, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": -2.19, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 3.3667, "angle": -0.95, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 4.0667, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": -2.19, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 6.6667, "angle": -0.95}]}, "t12": {"rotate": [{"angle": -1.38, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": -2.19, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.3667, "angle": -1.38, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.2667, "curve": 0.25, "c3": 0.75}, {"time": 5.9667, "angle": -2.19, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6.6667, "angle": -1.38}]}, "t13": {"rotate": [{"angle": -1.79, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.1333, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "angle": -2.19, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 3.3667, "angle": -1.79, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.5, "curve": 0.25, "c3": 0.75}, {"time": 6.2333, "angle": -2.19, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 6.6667, "angle": -1.79}]}, "st4": {"translate": [{"x": -2.35, "y": 0.19, "curve": 0.326, "c2": 0.31, "c3": 0.662, "c4": 0.65}, {"time": 0.2, "x": -3.64, "y": 0.29, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 1.1333, "x": -8.53, "y": 0.69, "curve": "stepped"}, {"time": 1.8667, "x": -8.53, "y": 0.69, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 3.5667, "x": -3.64, "y": 0.29, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 4.5, "x": -8.53, "y": 0.69, "curve": "stepped"}, {"time": 5.2667, "x": -8.53, "y": 0.69, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6.6667, "x": -2.35, "y": 0.19}], "scale": [{"x": 1.006, "y": 1.006, "curve": 0.326, "c2": 0.31, "c3": 0.662, "c4": 0.65}, {"time": 0.2, "x": 1.009, "y": 1.009, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 1.1333, "x": 1.02, "y": 1.02, "curve": "stepped"}, {"time": 1.8667, "x": 1.02, "y": 1.02, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 3.5667, "x": 1.009, "y": 1.009, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 4.5, "x": 1.02, "y": 1.02, "curve": "stepped"}, {"time": 5.2667, "x": 1.02, "y": 1.02, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6.6667, "x": 1.006, "y": 1.006}]}, "t14": {"rotate": [{"angle": 1.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7667, "angle": 4.9, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "angle": -2.4, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 3.0667, "angle": 0.05, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 3.3667, "angle": 1.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.1333, "angle": 4.9, "curve": 0.25, "c3": 0.75}, {"time": 5.7667, "angle": -2.4, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 6.4667, "angle": 0.05, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 6.6667, "angle": 1.25}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": -0.04, "y": 4.03, "curve": "stepped"}, {"time": 1.5, "x": -0.04, "y": 4.03, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "x": -0.04, "y": 4.03, "curve": "stepped"}, {"time": 4.8667, "x": -0.04, "y": 4.03, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "t15": {"rotate": [{"angle": 0.83, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.1333, "angle": 4.53}, {"time": 2.8667, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 3.3667, "angle": 0.83, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.5, "angle": 4.53}, {"time": 6.2333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 6.6667, "angle": 0.83}]}, "t16": {"rotate": [{"angle": 0.28, "curve": 0.289, "c2": 0.18, "c3": 0.711, "c4": 0.82}, {"time": 1.1333, "angle": 4.24, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.3, "angle": 4.53, "curve": 0.25, "c3": 0.75}, {"time": 3.0667, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 3.3667, "angle": 0.28, "curve": 0.289, "c2": 0.18, "c3": 0.711, "c4": 0.82}, {"time": 4.5, "angle": 4.24, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 4.7, "angle": 4.53, "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 6.6667, "angle": 0.28}]}, "t17": {"rotate": [{"curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.1333, "angle": 3.69, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.5, "angle": 4.53, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 4.5, "angle": 3.69, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 4.8667, "angle": 4.53, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "t18": {"rotate": [{"angle": 0.28, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.2, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 1.1333, "angle": 3.01, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1.7, "angle": 4.53, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 3.3667, "angle": 0.28, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.5667, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 4.5, "angle": 3.01, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 5.0667, "angle": 4.53, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 6.6667, "angle": 0.28}]}, "t19": {"rotate": [{"angle": 0.83, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.1333, "angle": 2.26, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.9, "angle": 4.53, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 3.3667, "angle": 0.83, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.7667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.5, "angle": 2.26, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3, "angle": 4.53, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 6.6667, "angle": 0.83}]}, "t8": {"rotate": [{"angle": 0.83, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.1333, "angle": 4.53}, {"time": 2.8667, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 3.3667, "angle": 0.83, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.5, "angle": 4.53}, {"time": 6.2333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 6.6667, "angle": 0.83}]}, "t20": {"rotate": [{"angle": 0.12, "curve": 0.271, "c2": 0.12, "c3": 0.693, "c4": 0.76}, {"time": 1.1333, "angle": 4.06, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 1.4, "angle": 4.53, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "curve": 0.311, "c3": 0.646, "c4": 0.35}, {"time": 3.3667, "angle": 0.12, "curve": 0.271, "c2": 0.12, "c3": 0.693, "c4": 0.76}, {"time": 4.5, "angle": 4.06, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 4.7667, "angle": 4.53, "curve": 0.25, "c3": 0.75}, {"time": 6.5667, "curve": 0.311, "c3": 0.646, "c4": 0.35}, {"time": 6.6667, "angle": 0.12}]}, "t21": {"rotate": [{"angle": 0.19, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.1333, "angle": 3.15, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.6667, "angle": 4.53, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 3.3667, "angle": 0.19, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.5333, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 4.5, "angle": 3.15, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 5.0333, "angle": 4.53, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 6.6667, "angle": 0.19}]}, "t22": {"rotate": [{"angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 2.49, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": 2.49, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -2.4}]}, "t23": {"rotate": [{"angle": -2.85, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 1.54, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": -2.85, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": 1.54, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -2.85}]}, "t24": {"rotate": [{"angle": -2.57, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.2, "angle": -2.85, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": 1.54, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 3.3667, "angle": -2.57, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.5667, "angle": -2.85, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "angle": 1.54, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 6.6667, "angle": -2.57}]}, "t25": {"rotate": [{"angle": -2.04, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "angle": -2.85, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": 1.54, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 3.3667, "angle": -2.04, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.7667, "angle": -2.85, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "angle": 1.54, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 6.6667, "angle": -2.04}]}, "t26": {"rotate": [{"angle": -1.37, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5667, "angle": -2.85, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 1.54, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 3.3667, "angle": -1.37, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.9333, "angle": -2.85, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": 1.54, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 6.6667, "angle": -1.37}]}, "t31": {"rotate": [{"angle": 0.56, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": 5.47, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 3.3667, "angle": 0.56, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 3.6333, "curve": 0.25, "c3": 0.75}, {"time": 5.1333, "angle": 5.47, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 6.6667, "angle": 0.56}]}, "t32": {"rotate": [{"angle": 1.66, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "angle": 5.47, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 3.3667, "angle": 1.66, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 3.9, "curve": 0.25, "c3": 0.75}, {"time": 5.4667, "angle": 5.47, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 6.6667, "angle": 1.66}]}, "t33": {"rotate": [{"angle": 2.91, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": 5.47, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 3.3667, "angle": 2.91, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 4.1667, "curve": 0.25, "c3": 0.75}, {"time": 5.8, "angle": 5.47, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 6.6667, "angle": 2.91}]}, "t34": {"rotate": [{"angle": 4.14, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "angle": 5.47, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.3667, "angle": 4.14, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 4.4333, "curve": 0.25, "c3": 0.75}, {"time": 6.1, "angle": 5.47, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6.6667, "angle": 4.14}]}, "t27": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -3.67, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -3.67, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "t35": {"rotate": [{"angle": -0.19, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": -3.09, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 3.3667, "angle": -0.19, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3.5667, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "angle": -3.09, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 6.6667, "angle": -0.19}]}, "t45": {"rotate": [{"angle": -0.57, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": -3.09, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 3.3667, "angle": -0.57, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.7667, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "angle": -3.09, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 6.6667, "angle": -0.57}]}, "t36": {"rotate": [{"angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 0.19, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": 0.19, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -1.28}]}, "t37": {"rotate": [{"angle": -1.24, "curve": 0.354, "c2": 0.65, "c3": 0.689}, {"time": 0.1, "angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 0.19, "curve": 0.246, "c3": 0.72, "c4": 0.87}, {"time": 3.3667, "angle": -1.24, "curve": 0.354, "c2": 0.65, "c3": 0.689}, {"time": 3.5, "angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 0.19, "curve": 0.246, "c3": 0.72, "c4": 0.87}, {"time": 6.6667, "angle": -1.24}]}, "t38": {"rotate": [{"angle": -1.16, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.2333, "angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": 0.19, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 3.3667, "angle": -1.16, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 3.6, "angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "angle": 0.19, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 6.6667, "angle": -1.16}]}, "t39": {"rotate": [{"angle": -1.05, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.3333, "angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 0.19, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 3.3667, "angle": -1.05, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 3.7, "angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "angle": 0.19, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 6.6667, "angle": -1.05}]}, "t40": {"rotate": [{"angle": -0.93, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4667, "angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": 0.19, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3667, "angle": -0.93, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.8333, "angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "angle": 0.19, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 6.6667, "angle": -0.93}]}, "t41": {"rotate": [{"angle": -0.79, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5667, "angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 0.19, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 3.3667, "angle": -0.79, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.9333, "angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": 0.19, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 6.6667, "angle": -0.79}]}, "t42": {"rotate": [{"angle": -0.64, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.6667, "angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": 0.19, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 3.3667, "angle": -0.64, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 4.0667, "angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": 0.19, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 6.6667, "angle": -0.64}]}, "t43": {"rotate": [{"angle": -0.5, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 0.8, "angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": 0.19, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 3.3667, "angle": -0.5, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 4.1667, "angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 5.8, "angle": 0.19, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 6.6667, "angle": -0.5}]}, "t44": {"rotate": [{"angle": -0.35, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.9, "angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": 0.19, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.3667, "angle": -0.35, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.2667, "angle": -1.28, "curve": 0.25, "c3": 0.75}, {"time": 5.9667, "angle": 0.19, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6.6667, "angle": -0.35}]}, "ys4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "qin": {"rotate": [{"angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "angle": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 1.2}]}}, "deform": {"default": {"t1": {"t1": [{"curve": 0.25, "c3": 0.75}, {"time": 0.9333, "offset": 64, "vertices": [3.87187, 6.63394, 4.97698, 7.21738, 5.42076, 6.72934, 2.77624, 7.72873, 2.68207, 7.11789, 1.34955, 5.29867, 2.39282, 9.63699, 1.88013, 10.36136, 3.73686, 7.40156, 3.18498, 8.96442, 4.26478, 7.62738, 3.11739, 6.33676, 4.85137, 7.47104, 4.85137, 7.47104, 4.85137, 7.47104, 4.85137, 7.47104], "curve": "stepped"}, {"time": 2, "offset": 64, "vertices": [3.87187, 6.63394, 4.97698, 7.21738, 5.42076, 6.72934, 2.77624, 7.72873, 2.68207, 7.11789, 1.34955, 5.29867, 2.39282, 9.63699, 1.88013, 10.36136, 3.73686, 7.40156, 3.18498, 8.96442, 4.26478, 7.62738, 3.11739, 6.33676, 4.85137, 7.47104, 4.85137, 7.47104, 4.85137, 7.47104, 4.85137, 7.47104], "curve": 0.35, "c2": 0.4, "c3": 0.757}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "offset": 64, "vertices": [3.87187, 6.63394, 4.97698, 7.21738, 5.42076, 6.72934, 2.77624, 7.72873, 2.68207, 7.11789, 1.34955, 5.29867, 2.39282, 9.63699, 1.88013, 10.36136, 3.73686, 7.40156, 3.18498, 8.96442, 4.26478, 7.62738, 3.11739, 6.33676, 4.85137, 7.47104, 4.85137, 7.47104, 4.85137, 7.47104, 4.85137, 7.47104], "curve": "stepped"}, {"time": 5.3667, "offset": 64, "vertices": [3.87187, 6.63394, 4.97698, 7.21738, 5.42076, 6.72934, 2.77624, 7.72873, 2.68207, 7.11789, 1.34955, 5.29867, 2.39282, 9.63699, 1.88013, 10.36136, 3.73686, 7.40156, 3.18498, 8.96442, 4.26478, 7.62738, 3.11739, 6.33676, 4.85137, 7.47104, 4.85137, 7.47104, 4.85137, 7.47104, 4.85137, 7.47104], "curve": 0.35, "c2": 0.4, "c3": 0.757}, {"time": 6.6667}]}, "st1": {"st1": [{"offset": 312, "vertices": [0.19388, 1.33479, 0.44445, 1.27349, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.02648, 0.13986, 0.0173, 0.13325, -0.04614, 0.24365, 0.03013, 0.23214, -0.08014, 0.42324, 0.05227, 0.40323, -0.09402, 0.49648, -0.11408, 0.60247, 0.07447, 0.574, -0.11408, 0.60247, 0.07447, 0.574, -0.11408, 0.60247, 0.07447, 0.574, -0.11408, 0.60247, 0.07447, 0.574, -0.11408, 0.60247, 0.07447, 0.574, 0.44271, 0.42423, -0.11408, 0.60247, 0.07447, 0.574, 0.44271, 0.42423, -0.11408, 0.60247, 0.36103, 0.34592, -0.09303, 0.49129, 0.27845, 0.26682, -0.07176, 0.37895, 0.16337, 0.15655, -0.0421, 0.22234, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.29003, 0.27787, -0.07472, 0.39466, 0.04882, 0.37601, -0.07472, 0.39466, 0.04882, 0.37601, -0.0018, 0.59179, 0.17607, 0.53195, 0.10614, 0.60748, -0.03951, 0.20869, 0.02581, 0.19882, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.00121, 0.29595, 0.03861, 0.29342, 0.0047, 0.29591, 0.04254, 0.29287, 0.00121, 0.29595, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.04254, 0.29287, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0.04254, 0.29287, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.20894, 0.20021, -0.05384, 0.28435, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.07472, 0.39466, 0.04882, 0.37601, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.00326, 0.55609, 0.17007, 0.49843, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.12142, 0.64132, 0.07933, 0.61101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.07472, 0.39466, 0.04882, 0.37601, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.04288, 0.29521, 0.00122, 0.29832, 0.03891, 0.29576, 0.00474, 0.29827, 0.04769, 0.29448, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.00121, 0.29595, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.09308, -0.23135, 0.08697, -0.1354, 0.06912, -0.1454, 0.00121, 0.29595, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.07141, -0.02048, 0.06822, -0.02943, 0.04254, 0.29287, 0.00121, 0.29595, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.07362, 0.09564, 0.07237, 0.09664, 0, 0, 0, 0, 0, 0, 0, 0, 0.08143, 0.20301, 0.05479, 0.21184, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.00121, 0.29595, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0.08317, 0.29956, 0.04415, 0.30782, 0, 0, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0.03605, 0.42816, 0.16886, 0.3951, 0, 0, 0, 0, 0, 0, 0, 0, 0.02051, 0.52361, 0.09578, 0.51522, 0.18413, 0.49059, 0, 0, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, -0.00213, 0.65088, 0.09173, 0.64442, 0.20267, 0.61852, 0, 0, 0, 0, 0, 0, 0, 0, 0.09791, 0.77764, 0.23198, 0.74863, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0.06468, 0.92243, 0.07972, 0.92137, 0.23912, 0.89333, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0.04592, 1.03464, 0.24209, 1.00705], "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 1.3, "offset": 312, "vertices": [3.08899, 21.26614, 7.0811, 20.28955, 0, 0, 0, 0, 0, 0, 0.67773, 4.66605, 0.07486, 4.71454, 0.75384, 4.65456, 1.5537, 4.45178, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.42187, 2.22825, 0.2756, 2.12294, -0.73505, 3.88194, 0.48004, 3.69849, -1.27679, 6.74312, 0.83286, 6.42433, -1.49792, 7.90999, -1.81757, 9.59874, 1.18643, 9.14505, -1.81757, 9.59874, 1.18643, 9.14505, -1.81757, 9.59874, 1.18643, 9.14505, -1.81757, 9.59874, 1.18643, 9.14505, -1.81757, 9.59874, 1.18643, 9.14505, 7.05341, 6.75891, -1.81757, 9.59874, 1.18643, 9.14505, 7.05341, 6.75891, -1.81757, 9.59874, 5.75192, 5.51129, -1.48212, 7.82738, 4.43634, 4.2511, -1.14331, 6.03758, 2.60278, 2.49426, -0.67078, 3.54243, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.62076, 4.42712, -1.19043, 6.2878, 0.77783, 5.99074, -1.19043, 6.2878, 0.77783, 5.99074, -0.02875, 9.42851, 2.80521, 8.47514, 1.6911, 9.67844, -0.62952, 3.32482, 0.41129, 3.16766, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.67773, 4.66605, 0.01932, 4.71521, 0.61509, 4.67476, 0.07486, 4.71454, 0.67773, 4.66605, 0.01932, 4.71521, 0.61509, 4.67476, 0.07486, 4.71454, 0.75384, 4.65456, 0.67773, 4.66605, 0.61509, 4.67476, 0.07486, 4.71454, 0.75384, 4.65456, 1.5537, 4.45178, 0.67773, 4.66605, 0.61509, 4.67476, 0.07486, 4.71454, 0.75384, 4.65456, 1.5537, 4.45178, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.32895, 3.18982, -0.85779, 4.53029, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.19043, 6.2878, 0.77783, 5.99074, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.05188, 8.85966, 2.70963, 7.94101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.93445, 10.21757, 1.26392, 9.73473, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.19043, 6.2878, 0.77783, 5.99074, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.68317, 4.70329, 0.01949, 4.75285, 0.62, 4.71208, 0.07546, 4.75217, 0.75987, 4.6917, 0, 0, 0, 0, 0, 0, 0.67773, 4.66605, 0.01932, 4.71521, 0.61509, 4.67476, 0.07486, 4.71454, 0.75384, 4.65456, 1.48303, -3.68584, 1.38567, -2.15726, 1.1012, -2.31656, 0.01932, 4.71521, 0.61509, 4.67476, 0.07486, 4.71454, 0.75384, 4.65456, 1.13777, -0.32631, 1.08684, -0.46893, 0.67773, 4.66605, 0.01932, 4.71521, 0.61509, 4.67476, 0.07486, 4.71454, 0.75384, 4.65456, 1.17294, 1.52368, 1.15297, 1.53963, 0, 0, 0, 0, 0, 0, 0, 0, 1.29736, 3.23438, 0.87286, 3.37514, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.67773, 4.66605, 0.01932, 4.71521, 0.61509, 4.67476, 0.07486, 4.71454, 0.75384, 4.65456, 1.5537, 4.45178, 1.32504, 4.77257, 0.70335, 4.90427, 0, 0, 0, 0, 0, 0, 0, 0, 0.67773, 4.66605, 0.61509, 4.67476, 0.07486, 4.71454, 0.75384, 4.65456, 1.5537, 4.45178, 0.57435, 6.82152, 2.69034, 6.29482, 0, 0, 0, 0, 0, 0, 0, 0, 0.32677, 8.34221, 1.52596, 8.20857, 2.93358, 7.81619, 0, 0, 0, 0, 0, 0, 0, 0, 0.67773, 4.66605, 0.61509, 4.67476, 0.07486, 4.71454, 0.75384, 4.65456, 1.5537, 4.45178, -0.03386, 10.36989, 1.46146, 10.26707, 3.22891, 9.85443, 0, 0, 0, 0, 0, 0, 0, 0, 1.55996, 12.38948, 3.69591, 11.92725, 0, 0, 0, 0, 0, 0, 0.67773, 4.66605, 0.07486, 4.71454, 0.75384, 4.65456, 1.5537, 4.45178, 1.03043, 14.6964, 1.27011, 14.67944, 3.80975, 14.23271, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.67773, 4.66605, 0.07486, 4.71454, 0.75384, 4.65456, 1.5537, 4.45178, 0.73163, 16.4841, 3.85703, 16.04459], "curve": 0.25, "c3": 0.75}, {"time": 3.0667, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 3.3667, "offset": 312, "vertices": [0.19388, 1.33479, 0.44445, 1.27349, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.02648, 0.13986, 0.0173, 0.13325, -0.04614, 0.24365, 0.03013, 0.23214, -0.08014, 0.42324, 0.05227, 0.40323, -0.09402, 0.49648, -0.11408, 0.60247, 0.07447, 0.574, -0.11408, 0.60247, 0.07447, 0.574, -0.11408, 0.60247, 0.07447, 0.574, -0.11408, 0.60247, 0.07447, 0.574, -0.11408, 0.60247, 0.07447, 0.574, 0.44271, 0.42423, -0.11408, 0.60247, 0.07447, 0.574, 0.44271, 0.42423, -0.11408, 0.60247, 0.36103, 0.34592, -0.09303, 0.49129, 0.27845, 0.26682, -0.07176, 0.37895, 0.16337, 0.15655, -0.0421, 0.22234, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.29003, 0.27787, -0.07472, 0.39466, 0.04882, 0.37601, -0.07472, 0.39466, 0.04882, 0.37601, -0.0018, 0.59179, 0.17607, 0.53195, 0.10614, 0.60748, -0.03951, 0.20869, 0.02581, 0.19882, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.00121, 0.29595, 0.03861, 0.29342, 0.0047, 0.29591, 0.04254, 0.29287, 0.00121, 0.29595, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.04254, 0.29287, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0.04254, 0.29287, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.20894, 0.20021, -0.05384, 0.28435, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.07472, 0.39466, 0.04882, 0.37601, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.00326, 0.55609, 0.17007, 0.49843, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.12142, 0.64132, 0.07933, 0.61101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.07472, 0.39466, 0.04882, 0.37601, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.04288, 0.29521, 0.00122, 0.29832, 0.03891, 0.29576, 0.00474, 0.29827, 0.04769, 0.29448, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.00121, 0.29595, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.09308, -0.23135, 0.08697, -0.1354, 0.06912, -0.1454, 0.00121, 0.29595, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.07141, -0.02048, 0.06822, -0.02943, 0.04254, 0.29287, 0.00121, 0.29595, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.07362, 0.09564, 0.07237, 0.09664, 0, 0, 0, 0, 0, 0, 0, 0, 0.08143, 0.20301, 0.05479, 0.21184, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.00121, 0.29595, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0.08317, 0.29956, 0.04415, 0.30782, 0, 0, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0.03605, 0.42816, 0.16886, 0.3951, 0, 0, 0, 0, 0, 0, 0, 0, 0.02051, 0.52361, 0.09578, 0.51522, 0.18413, 0.49059, 0, 0, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, -0.00213, 0.65088, 0.09173, 0.64442, 0.20267, 0.61852, 0, 0, 0, 0, 0, 0, 0, 0, 0.09791, 0.77764, 0.23198, 0.74863, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0.06468, 0.92243, 0.07972, 0.92137, 0.23912, 0.89333, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0.04592, 1.03464, 0.24209, 1.00705], "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 4.7, "offset": 312, "vertices": [3.08899, 21.26614, 7.0811, 20.28955, 0, 0, 0, 0, 0, 0, 0.67773, 4.66605, 0.07486, 4.71454, 0.75384, 4.65456, 1.5537, 4.45178, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.42187, 2.22825, 0.2756, 2.12294, -0.73505, 3.88194, 0.48004, 3.69849, -1.27679, 6.74312, 0.83286, 6.42433, -1.49792, 7.90999, -1.81757, 9.59874, 1.18643, 9.14505, -1.81757, 9.59874, 1.18643, 9.14505, -1.81757, 9.59874, 1.18643, 9.14505, -1.81757, 9.59874, 1.18643, 9.14505, -1.81757, 9.59874, 1.18643, 9.14505, 7.05341, 6.75891, -1.81757, 9.59874, 1.18643, 9.14505, 7.05341, 6.75891, -1.81757, 9.59874, 5.75192, 5.51129, -1.48212, 7.82738, 4.43634, 4.2511, -1.14331, 6.03758, 2.60278, 2.49426, -0.67078, 3.54243, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.62076, 4.42712, -1.19043, 6.2878, 0.77783, 5.99074, -1.19043, 6.2878, 0.77783, 5.99074, -0.02875, 9.42851, 2.80521, 8.47514, 1.6911, 9.67844, -0.62952, 3.32482, 0.41129, 3.16766, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.67773, 4.66605, 0.01932, 4.71521, 0.61509, 4.67476, 0.07486, 4.71454, 0.67773, 4.66605, 0.01932, 4.71521, 0.61509, 4.67476, 0.07486, 4.71454, 0.75384, 4.65456, 0.67773, 4.66605, 0.61509, 4.67476, 0.07486, 4.71454, 0.75384, 4.65456, 1.5537, 4.45178, 0.67773, 4.66605, 0.61509, 4.67476, 0.07486, 4.71454, 0.75384, 4.65456, 1.5537, 4.45178, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.32895, 3.18982, -0.85779, 4.53029, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.19043, 6.2878, 0.77783, 5.99074, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.05188, 8.85966, 2.70963, 7.94101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.93445, 10.21757, 1.26392, 9.73473, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.19043, 6.2878, 0.77783, 5.99074, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.68317, 4.70329, 0.01949, 4.75285, 0.62, 4.71208, 0.07546, 4.75217, 0.75987, 4.6917, 0, 0, 0, 0, 0, 0, 0.67773, 4.66605, 0.01932, 4.71521, 0.61509, 4.67476, 0.07486, 4.71454, 0.75384, 4.65456, 1.48303, -3.68584, 1.38567, -2.15726, 1.1012, -2.31656, 0.01932, 4.71521, 0.61509, 4.67476, 0.07486, 4.71454, 0.75384, 4.65456, 1.13777, -0.32631, 1.08684, -0.46893, 0.67773, 4.66605, 0.01932, 4.71521, 0.61509, 4.67476, 0.07486, 4.71454, 0.75384, 4.65456, 1.17294, 1.52368, 1.15297, 1.53963, 0, 0, 0, 0, 0, 0, 0, 0, 1.29736, 3.23438, 0.87286, 3.37514, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.67773, 4.66605, 0.01932, 4.71521, 0.61509, 4.67476, 0.07486, 4.71454, 0.75384, 4.65456, 1.5537, 4.45178, 1.32504, 4.77257, 0.70335, 4.90427, 0, 0, 0, 0, 0, 0, 0, 0, 0.67773, 4.66605, 0.61509, 4.67476, 0.07486, 4.71454, 0.75384, 4.65456, 1.5537, 4.45178, 0.57435, 6.82152, 2.69034, 6.29482, 0, 0, 0, 0, 0, 0, 0, 0, 0.32677, 8.34221, 1.52596, 8.20857, 2.93358, 7.81619, 0, 0, 0, 0, 0, 0, 0, 0, 0.67773, 4.66605, 0.61509, 4.67476, 0.07486, 4.71454, 0.75384, 4.65456, 1.5537, 4.45178, -0.03386, 10.36989, 1.46146, 10.26707, 3.22891, 9.85443, 0, 0, 0, 0, 0, 0, 0, 0, 1.55996, 12.38948, 3.69591, 11.92725, 0, 0, 0, 0, 0, 0, 0.67773, 4.66605, 0.07486, 4.71454, 0.75384, 4.65456, 1.5537, 4.45178, 1.03043, 14.6964, 1.27011, 14.67944, 3.80975, 14.23271, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.67773, 4.66605, 0.07486, 4.71454, 0.75384, 4.65456, 1.5537, 4.45178, 0.73163, 16.4841, 3.85703, 16.04459], "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 6.6667, "offset": 312, "vertices": [0.19388, 1.33479, 0.44445, 1.27349, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.02648, 0.13986, 0.0173, 0.13325, -0.04614, 0.24365, 0.03013, 0.23214, -0.08014, 0.42324, 0.05227, 0.40323, -0.09402, 0.49648, -0.11408, 0.60247, 0.07447, 0.574, -0.11408, 0.60247, 0.07447, 0.574, -0.11408, 0.60247, 0.07447, 0.574, -0.11408, 0.60247, 0.07447, 0.574, -0.11408, 0.60247, 0.07447, 0.574, 0.44271, 0.42423, -0.11408, 0.60247, 0.07447, 0.574, 0.44271, 0.42423, -0.11408, 0.60247, 0.36103, 0.34592, -0.09303, 0.49129, 0.27845, 0.26682, -0.07176, 0.37895, 0.16337, 0.15655, -0.0421, 0.22234, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.29003, 0.27787, -0.07472, 0.39466, 0.04882, 0.37601, -0.07472, 0.39466, 0.04882, 0.37601, -0.0018, 0.59179, 0.17607, 0.53195, 0.10614, 0.60748, -0.03951, 0.20869, 0.02581, 0.19882, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.00121, 0.29595, 0.03861, 0.29342, 0.0047, 0.29591, 0.04254, 0.29287, 0.00121, 0.29595, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.04254, 0.29287, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0.04254, 0.29287, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.20894, 0.20021, -0.05384, 0.28435, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.07472, 0.39466, 0.04882, 0.37601, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.00326, 0.55609, 0.17007, 0.49843, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.12142, 0.64132, 0.07933, 0.61101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.07472, 0.39466, 0.04882, 0.37601, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.04288, 0.29521, 0.00122, 0.29832, 0.03891, 0.29576, 0.00474, 0.29827, 0.04769, 0.29448, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.00121, 0.29595, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.09308, -0.23135, 0.08697, -0.1354, 0.06912, -0.1454, 0.00121, 0.29595, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.07141, -0.02048, 0.06822, -0.02943, 0.04254, 0.29287, 0.00121, 0.29595, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.07362, 0.09564, 0.07237, 0.09664, 0, 0, 0, 0, 0, 0, 0, 0, 0.08143, 0.20301, 0.05479, 0.21184, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.00121, 0.29595, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0.08317, 0.29956, 0.04415, 0.30782, 0, 0, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0.03605, 0.42816, 0.16886, 0.3951, 0, 0, 0, 0, 0, 0, 0, 0, 0.02051, 0.52361, 0.09578, 0.51522, 0.18413, 0.49059, 0, 0, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.03861, 0.29342, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, -0.00213, 0.65088, 0.09173, 0.64442, 0.20267, 0.61852, 0, 0, 0, 0, 0, 0, 0, 0, 0.09791, 0.77764, 0.23198, 0.74863, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0.06468, 0.92243, 0.07972, 0.92137, 0.23912, 0.89333, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.04254, 0.29287, 0.0047, 0.29591, 0.04732, 0.29215, 0.09752, 0.27942, 0.04592, 1.03464, 0.24209, 1.00705]}]}, "st": {"st": [{"offset": 36, "vertices": [-0.23437, -0.17191, 0.02536, -0.28388, -0.2902, 0.01636, 0.01887, -0.29006, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.58461, -0.18832, -0.13852, -0.58611, 0.40357, 0.48831, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.30799, -0.22587, 0.0334, -0.37377, 0, 0, 0, 0, 0, 0, 0.08603, -0.11725, 0.14201, 0.01266, -0.12528, 0.05545, -0.1958, -0.14362, 0.02118, -0.23714, -0.36579, 0.03234, -0.21264, -0.29038, -0.22396, -0.29103, -0.34406, 0.46908, -0.56805, -0.05081, 0.50116, -0.22177, 0, 0, 0, 0, 0, 0, -0.11748, -0.08617, 0.01271, -0.1423, 0, 0, 0, 0, 0, 0, 0, 0, -0.1387, -0.19947, -0.03107, -0.00272, 0.19749, -0.10776, 0, 0, -0.07376, -0.22559, 0, 0, 0.16642, -0.11054, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.39695, -0.16662, 0.16324, 0.397, 0.09111, 0.41947, 0.00919, 0.42915, 0.06984, 0.42353, -0.11607, 0.40172, -0.25399, 0.33216, 0.70487, 0.04587, -0.04518, 0.70486, -0.16805, 0.68603, -0.29616, 0.64121, -0.20244, 0.67668, -0.46924, 0.47188, 0.04367, 0.65762, 0.13631, 0.64483, -0.14962, 0.62784, -0.26924, 0.58292, -0.18403, 0.61516, -0.0263, 0.64156, -0.42658, 0.42898, -0.16154, 0.34976, -0.11042, 0.3691, -0.01578, 0.38494, -0.25595, 0.25739, -0.33192, 0.1469, -0.07361, 0.24607, -0.01052, 0.25663, -0.17063, 0.1716, -0.22128, 0.09793, -0.1077, 0.23318, -0.07361, 0.24607, -0.01052, 0.25663, -0.17064, 0.1716, -0.22129, 0.09793, -0.08077, 0.17488, -0.05521, 0.18455, -0.00789, 0.19247, -0.12797, 0.12869, -0.16596, 0.07345, -0.05385, 0.11659, -0.03681, 0.12303, -0.00526, 0.12831, -0.08532, 0.0858, -0.11064, 0.04897, -0.18332, 0.7484, -0.32309, 0.69951, -0.22084, 0.7382, -0.5119, 0.51478, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.16155, 0.34976, -0.11042, 0.36911, -0.01578, 0.38495, -0.25595, 0.25739, -0.18847, 0.40806, -0.12882, 0.43063, -0.01841, 0.44911, -0.29861, 0.30029, -0.38725, 0.17139, -0.16155, 0.34976, -0.11042, 0.3691, -0.01578, 0.38494, -0.25595, 0.25739, -0.33192, 0.1469], "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 1.3, "offset": 36, "vertices": [-3.73407, -2.73883, 0.40398, -4.52277, -4.62344, 0.2607, 0.3007, -4.62137, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -9.31409, -3.00027, -2.20685, -9.33801, 6.42969, 7.77989, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.90698, -3.59866, 0.53221, -5.95496, 0, 0, 0, 0, 0, 0, 1.37061, -1.86801, 2.26254, 0.20166, -1.99597, 0.88351, -3.11957, -2.28812, 0.33748, -3.7782, -5.82788, 0.51526, -3.38785, -4.62646, -3.56825, -4.63675, -5.48157, 7.47348, -9.05032, -0.80945, 7.98465, -3.53326, 0, 0, 0, 0, 0, 0, -1.87164, -1.3728, 0.2025, -2.26709, 0, 0, 0, 0, 0, 0, 0, 0, -2.20982, -3.17792, -0.49493, -0.0434, 3.14639, -1.7168, 0, 0, -1.17523, -3.59412, 0.72292, -1.67804, 2.65147, -1.76111, 0, 0, 0, 0, 0, 0, 0, 0, 0.77567, -2.25458, -2.39081, -0.44873, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.31046, -3.42242, -3.49857, 0.19264, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.40807, -4.49951, -4.59821, 0.25321, 0.29906, -4.59534, 0.40807, -4.49951, -4.59821, 0.25321, 0.29906, -4.59534, 1.84866, 4.43217, 0, 0, 0, 0, 0, 0, 0, 0, 0.81613, -8.99921, -9.19635, 0.5064, 0.59814, -9.19067, 0.81613, -8.99921, -9.19635, 0.5064, 0.59814, -9.19067, 3.69724, 8.86433, -8.04675, 0.44308, 0.52339, -8.04175, 0.7141, -7.87433, -8.04675, 0.44308, 0.52339, -8.04175, 3.23502, 7.75623, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.32769, -5.03485, 4.9357, 0.33478, 4.79799, 1.20531, 4.47318, 2.11294, 4.72291, 1.47243, 3.65045, 3.78946, 0.32769, -5.03485, 4.79799, 1.20531, 4.47318, 2.11294, 4.72291, 1.47243, 4.93948, 0.27411, 3.65045, 3.78946, 2.02548, 4.85612, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.32426, -2.65469, 2.60074, 6.32505, 1.4516, 6.68308, 0.14647, 6.83726, 1.11265, 6.74773, -1.84919, 6.40021, -4.04657, 5.2921, 11.23017, 0.73083, -0.71979, 11.23, -2.67734, 10.92995, -4.71855, 10.21594, -3.22525, 10.78094, -7.47597, 7.51804, 0.69582, 10.4774, 2.17166, 10.27354, -2.38379, 10.00284, -4.28958, 9.2872, -2.93202, 9.80087, -0.41901, 10.22144, -6.79631, 6.83453, -2.57376, 5.57237, -1.75922, 5.88056, -0.25141, 6.13291, -4.07779, 4.10077, -5.28827, 2.34039, -1.17282, 3.92039, -0.16761, 4.08863, -2.71854, 2.73389, -3.52551, 1.56026, -1.7159, 3.71503, -1.17284, 3.92048, -0.16761, 4.08872, -2.71861, 2.73395, -3.52557, 1.56029, -1.28685, 2.78616, -0.8796, 2.94024, -0.12571, 3.06642, -2.03886, 2.05035, -2.6441, 1.17018, -0.85793, 1.85748, -0.58641, 1.96021, -0.08381, 2.04433, -1.35927, 1.36694, -1.76276, 0.78014, -2.92076, 11.92371, -5.14754, 11.14479, -3.51846, 11.76116, -8.15565, 8.20157, 6.39722, 1.60705, 5.96416, 2.8172, 6.29712, 1.96321, 4.86718, 5.05252, 0.43691, -6.71304, 6.39722, 1.60705, 5.96416, 2.8172, 6.29712, 1.96321, 6.58588, 0.36546, 4.86718, 5.05252, 2.70059, 6.47476, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.29718, 1.96323, 4.86724, 5.05258, 6.39728, 1.60707, 5.9642, 2.81722, 6.29718, 1.96323, 6.58595, 0.36547, 4.86724, 5.05258, 2.70062, 6.47479, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.57381, 5.57247, -1.75924, 5.88067, -0.25142, 6.13303, -4.07782, 4.10083, -3.00281, 6.50123, -2.05246, 6.86081, -0.29332, 7.15523, -4.75754, 4.78433, -6.16968, 2.73055, -2.57378, 5.57243, -1.75924, 5.88063, -0.25142, 6.13299, -4.07782, 4.1008, -5.28821, 2.34045], "curve": 0.25, "c3": 0.75}, {"time": 3.0667, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 3.3667, "offset": 36, "vertices": [-0.23437, -0.17191, 0.02536, -0.28388, -0.2902, 0.01636, 0.01887, -0.29006, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.58461, -0.18832, -0.13852, -0.58611, 0.40357, 0.48831, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.30799, -0.22587, 0.0334, -0.37377, 0, 0, 0, 0, 0, 0, 0.08603, -0.11725, 0.14201, 0.01266, -0.12528, 0.05545, -0.1958, -0.14362, 0.02118, -0.23714, -0.36579, 0.03234, -0.21264, -0.29038, -0.22396, -0.29103, -0.34406, 0.46908, -0.56805, -0.05081, 0.50116, -0.22177, 0, 0, 0, 0, 0, 0, -0.11748, -0.08617, 0.01271, -0.1423, 0, 0, 0, 0, 0, 0, 0, 0, -0.1387, -0.19947, -0.03107, -0.00272, 0.19749, -0.10776, 0, 0, -0.07376, -0.22559, 0, 0, 0.16642, -0.11054, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.39695, -0.16662, 0.16324, 0.397, 0.09111, 0.41947, 0.00919, 0.42915, 0.06984, 0.42353, -0.11607, 0.40172, -0.25399, 0.33216, 0.70487, 0.04587, -0.04518, 0.70486, -0.16805, 0.68603, -0.29616, 0.64121, -0.20244, 0.67668, -0.46924, 0.47188, 0.04367, 0.65762, 0.13631, 0.64483, -0.14962, 0.62784, -0.26924, 0.58292, -0.18403, 0.61516, -0.0263, 0.64156, -0.42658, 0.42898, -0.16154, 0.34976, -0.11042, 0.3691, -0.01578, 0.38494, -0.25595, 0.25739, -0.33192, 0.1469, -0.07361, 0.24607, -0.01052, 0.25663, -0.17063, 0.1716, -0.22128, 0.09793, -0.1077, 0.23318, -0.07361, 0.24607, -0.01052, 0.25663, -0.17064, 0.1716, -0.22129, 0.09793, -0.08077, 0.17488, -0.05521, 0.18455, -0.00789, 0.19247, -0.12797, 0.12869, -0.16596, 0.07345, -0.05385, 0.11659, -0.03681, 0.12303, -0.00526, 0.12831, -0.08532, 0.0858, -0.11064, 0.04897, -0.18332, 0.7484, -0.32309, 0.69951, -0.22084, 0.7382, -0.5119, 0.51478, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.16155, 0.34976, -0.11042, 0.36911, -0.01578, 0.38495, -0.25595, 0.25739, -0.18847, 0.40806, -0.12882, 0.43063, -0.01841, 0.44911, -0.29861, 0.30029, -0.38725, 0.17139, -0.16155, 0.34976, -0.11042, 0.3691, -0.01578, 0.38494, -0.25595, 0.25739, -0.33192, 0.1469], "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 4.7, "offset": 36, "vertices": [-3.73407, -2.73883, 0.40398, -4.52277, -4.62344, 0.2607, 0.3007, -4.62137, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -9.31409, -3.00027, -2.20685, -9.33801, 6.42969, 7.77989, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.90698, -3.59866, 0.53221, -5.95496, 0, 0, 0, 0, 0, 0, 1.37061, -1.86801, 2.26254, 0.20166, -1.99597, 0.88351, -3.11957, -2.28812, 0.33748, -3.7782, -5.82788, 0.51526, -3.38785, -4.62646, -3.56825, -4.63675, -5.48157, 7.47348, -9.05032, -0.80945, 7.98465, -3.53326, 0, 0, 0, 0, 0, 0, -1.87164, -1.3728, 0.2025, -2.26709, 0, 0, 0, 0, 0, 0, 0, 0, -2.20982, -3.17792, -0.49493, -0.0434, 3.14639, -1.7168, 0, 0, -1.17523, -3.59412, 0.72292, -1.67804, 2.65147, -1.76111, 0, 0, 0, 0, 0, 0, 0, 0, 0.77567, -2.25458, -2.39081, -0.44873, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.31046, -3.42242, -3.49857, 0.19264, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.40807, -4.49951, -4.59821, 0.25321, 0.29906, -4.59534, 0.40807, -4.49951, -4.59821, 0.25321, 0.29906, -4.59534, 1.84866, 4.43217, 0, 0, 0, 0, 0, 0, 0, 0, 0.81613, -8.99921, -9.19635, 0.5064, 0.59814, -9.19067, 0.81613, -8.99921, -9.19635, 0.5064, 0.59814, -9.19067, 3.69724, 8.86433, -8.04675, 0.44308, 0.52339, -8.04175, 0.7141, -7.87433, -8.04675, 0.44308, 0.52339, -8.04175, 3.23502, 7.75623, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.32769, -5.03485, 4.9357, 0.33478, 4.79799, 1.20531, 4.47318, 2.11294, 4.72291, 1.47243, 3.65045, 3.78946, 0.32769, -5.03485, 4.79799, 1.20531, 4.47318, 2.11294, 4.72291, 1.47243, 4.93948, 0.27411, 3.65045, 3.78946, 2.02548, 4.85612, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.32426, -2.65469, 2.60074, 6.32505, 1.4516, 6.68308, 0.14647, 6.83726, 1.11265, 6.74773, -1.84919, 6.40021, -4.04657, 5.2921, 11.23017, 0.73083, -0.71979, 11.23, -2.67734, 10.92995, -4.71855, 10.21594, -3.22525, 10.78094, -7.47597, 7.51804, 0.69582, 10.4774, 2.17166, 10.27354, -2.38379, 10.00284, -4.28958, 9.2872, -2.93202, 9.80087, -0.41901, 10.22144, -6.79631, 6.83453, -2.57376, 5.57237, -1.75922, 5.88056, -0.25141, 6.13291, -4.07779, 4.10077, -5.28827, 2.34039, -1.17282, 3.92039, -0.16761, 4.08863, -2.71854, 2.73389, -3.52551, 1.56026, -1.7159, 3.71503, -1.17284, 3.92048, -0.16761, 4.08872, -2.71861, 2.73395, -3.52557, 1.56029, -1.28685, 2.78616, -0.8796, 2.94024, -0.12571, 3.06642, -2.03886, 2.05035, -2.6441, 1.17018, -0.85793, 1.85748, -0.58641, 1.96021, -0.08381, 2.04433, -1.35927, 1.36694, -1.76276, 0.78014, -2.92076, 11.92371, -5.14754, 11.14479, -3.51846, 11.76116, -8.15565, 8.20157, 6.39722, 1.60705, 5.96416, 2.8172, 6.29712, 1.96321, 4.86718, 5.05252, 0.43691, -6.71304, 6.39722, 1.60705, 5.96416, 2.8172, 6.29712, 1.96321, 6.58588, 0.36546, 4.86718, 5.05252, 2.70059, 6.47476, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.29718, 1.96323, 4.86724, 5.05258, 6.39728, 1.60707, 5.9642, 2.81722, 6.29718, 1.96323, 6.58595, 0.36547, 4.86724, 5.05258, 2.70062, 6.47479, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.57381, 5.57247, -1.75924, 5.88067, -0.25142, 6.13303, -4.07782, 4.10083, -3.00281, 6.50123, -2.05246, 6.86081, -0.29332, 7.15523, -4.75754, 4.78433, -6.16968, 2.73055, -2.57378, 5.57243, -1.75924, 5.88063, -0.25142, 6.13299, -4.07782, 4.1008, -5.28821, 2.34045], "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 6.6667, "offset": 36, "vertices": [-0.23437, -0.17191, 0.02536, -0.28388, -0.2902, 0.01636, 0.01887, -0.29006, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.58461, -0.18832, -0.13852, -0.58611, 0.40357, 0.48831, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.30799, -0.22587, 0.0334, -0.37377, 0, 0, 0, 0, 0, 0, 0.08603, -0.11725, 0.14201, 0.01266, -0.12528, 0.05545, -0.1958, -0.14362, 0.02118, -0.23714, -0.36579, 0.03234, -0.21264, -0.29038, -0.22396, -0.29103, -0.34406, 0.46908, -0.56805, -0.05081, 0.50116, -0.22177, 0, 0, 0, 0, 0, 0, -0.11748, -0.08617, 0.01271, -0.1423, 0, 0, 0, 0, 0, 0, 0, 0, -0.1387, -0.19947, -0.03107, -0.00272, 0.19749, -0.10776, 0, 0, -0.07376, -0.22559, 0, 0, 0.16642, -0.11054, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.39695, -0.16662, 0.16324, 0.397, 0.09111, 0.41947, 0.00919, 0.42915, 0.06984, 0.42353, -0.11607, 0.40172, -0.25399, 0.33216, 0.70487, 0.04587, -0.04518, 0.70486, -0.16805, 0.68603, -0.29616, 0.64121, -0.20244, 0.67668, -0.46924, 0.47188, 0.04367, 0.65762, 0.13631, 0.64483, -0.14962, 0.62784, -0.26924, 0.58292, -0.18403, 0.61516, -0.0263, 0.64156, -0.42658, 0.42898, -0.16154, 0.34976, -0.11042, 0.3691, -0.01578, 0.38494, -0.25595, 0.25739, -0.33192, 0.1469, -0.07361, 0.24607, -0.01052, 0.25663, -0.17063, 0.1716, -0.22128, 0.09793, -0.1077, 0.23318, -0.07361, 0.24607, -0.01052, 0.25663, -0.17064, 0.1716, -0.22129, 0.09793, -0.08077, 0.17488, -0.05521, 0.18455, -0.00789, 0.19247, -0.12797, 0.12869, -0.16596, 0.07345, -0.05385, 0.11659, -0.03681, 0.12303, -0.00526, 0.12831, -0.08532, 0.0858, -0.11064, 0.04897, -0.18332, 0.7484, -0.32309, 0.69951, -0.22084, 0.7382, -0.5119, 0.51478, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.16155, 0.34976, -0.11042, 0.36911, -0.01578, 0.38495, -0.25595, 0.25739, -0.18847, 0.40806, -0.12882, 0.43063, -0.01841, 0.44911, -0.29861, 0.30029, -0.38725, 0.17139, -0.16155, 0.34976, -0.11042, 0.3691, -0.01578, 0.38494, -0.25595, 0.25739, -0.33192, 0.1469]}]}, "yy": {"yy": [{"curve": 0.25, "c3": 0.75}, {"time": 0.7667, "offset": 14, "vertices": [0.18896, -1.0351, 0, 0, 0, 0, 0, 0, 0, 0, 6.189, 0.96375, 5.05435, 2.68372, 2.23596, 2.98248, 0, 0, 1.99164, 0.44873, 2.91748, -0.95813, 3.5865, -0.97601, 5.35748, -0.51062, 1.78903, 0.28662], "curve": "stepped"}, {"time": 1.5, "offset": 14, "vertices": [0.18896, -1.0351, 0, 0, 0, 0, 0, 0, 0, 0, 6.189, 0.96375, 5.05435, 2.68372, 2.23596, 2.98248, 0, 0, 1.99164, 0.44873, 2.91748, -0.95813, 3.5865, -0.97601, 5.35748, -0.51062, 1.78903, 0.28662], "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 2.8667, "offset": 14, "vertices": [0.0424, -0.23225, 0, 0, 0, 0, 0, 0, 0, 0, 1.38867, 0.21624, 1.13408, 0.60217, 0.5017, 0.6692, 0, 0, 0.44688, 0.10069, 0.65462, -0.21498, 0.80473, -0.219, 1.2021, -0.11457, 0.40142, 0.06431], "curve": 0.351, "c2": 0.4, "c3": 0.687, "c4": 0.75}, {"time": 3.1667, "vertices": [-0.87225, 12.63184, -1.18964, 6.32471, -0.61589, 2.73535, 0, 0, -0.31953, 1.42053, 2.06265, -9.14734, 2.06265, -9.14734, 2.09213, -9.31301, 0, 0, 0, 0, -0.65282, 2.90173, -0.67206, 10.99048, -1.15667, 9.80922, -1.87574, 13.35853, -1.96514, 11.78273, -0.97955, 4.35132, -0.58094, 4.10913, 0.94999, -2.24567, 1.16876, -2.73164, 0.9537, -0.40787, -0.57376, 3.90097], "curve": 0.369, "c2": 0.63, "c3": 0.707}, {"time": 3.5667, "offset": 14, "vertices": [0.02458, -0.13466, 0, 0, 0, 0, 0, 0, 0, 0, 0.80517, 0.12538, 0.65756, 0.34914, 0.29089, 0.38801, 0, 0, 0.25911, 0.05838, 0.37956, -0.12465, 0.46659, -0.12698, 0.69699, -0.06643, 0.23275, 0.03729], "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 4.1667, "offset": 14, "vertices": [0.18896, -1.0351, 0, 0, 0, 0, 0, 0, 0, 0, 6.189, 0.96375, 5.05435, 2.68372, 2.23596, 2.98248, 0, 0, 1.99164, 0.44873, 2.91748, -0.95813, 3.5865, -0.97601, 5.35748, -0.51062, 1.78903, 0.28662], "curve": "stepped"}, {"time": 4.9, "offset": 14, "vertices": [0.18896, -1.0351, 0, 0, 0, 0, 0, 0, 0, 0, 6.189, 0.96375, 5.05435, 2.68372, 2.23596, 2.98248, 0, 0, 1.99164, 0.44873, 2.91748, -0.95813, 3.5865, -0.97601, 5.35748, -0.51062, 1.78903, 0.28662], "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "ys1": {"ys1": [{"curve": 0.25, "c3": 0.75}, {"time": 1.5, "offset": 32, "vertices": [1.08539, -4.91229, 4.38577, 2.46463, 1.97568, -4.62665, 1.89615, -4.66, 0.28427, -5.02245, 0.21423, -5.02617, -0.05676, -5.03056, 0.72729, -3.29205, 2.93918, 1.65167, 1.32407, -3.10062, 1.27069, -3.12302, 0.19049, -3.36583, 0.14355, -3.36835, -0.03806, -3.37131, 0.36536, -1.65457, 1.4772, 0.83011, 0.66577, -1.55826, 0.63879, -1.56979, 0.09579, -1.69147, 0.0723, -1.69296, -0.01904, -1.69455, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.29379, 5.18698, -0.22092, 5.19028, 0.05875, 5.19469, 0.10922, 5.194, 0.14734, 5.19318, 0.17383, 5.19228, -0.68054, 5.15021, -1.31871, 5.02477, -3.88492, 3.44933, -4.98895, 1.44897, -0.33655, 5.94382, -0.25305, 5.94774, 0.06729, 5.95259, 0.12518, 5.95187, 0.16913, 5.95091, 0.19911, 5.94984, -0.7798, 5.9016, -1.51099, 5.75792, -4.45169, 3.95248, -5.71695, 1.66043, -3.00574, 7.38611, -0.45081, 7.96214, -0.33908, 7.96736, 0.09015, 7.97383, 0.16763, 7.97292, 0.2265, 7.97159, 0.26671, 7.97012, -1.0446, 7.90559, -5.96334, 5.29465, -3.24908, 7.98425, -0.4873, 8.60686, -0.36658, 8.61253, 0.09741, 8.61952, 0.18121, 8.61852, 0.24487, 8.61707, 0.28831, 8.61552, -1.12918, 8.54576, -7.51477, -4.22351, -3.24908, 7.98419, -0.4873, 8.6068, -0.36658, 8.61247, 0.09744, 8.61946, 0.18121, 8.61845, 0.24481, 8.617, 0.28833, 8.61548, -8.13272, -4.57083, -3.6629, 8.57971, -3.51624, 8.64078, -0.52737, 9.31454, -0.39673, 9.32069, 0.10547, 9.32828, 0.19608, 9.32716, 0.26495, 9.32559, 0.31204, 9.32397, -9.27695, -5.21384, -4.17819, 9.78674, -4.01093, 9.85648, -0.60156, 10.625, -0.45261, 10.63202, 0.1203, 10.64066, 0.22366, 10.63939, 0.30222, 10.63757, -8.1694, -4.59143, -3.67917, 8.61838, -3.53214, 8.67966, -0.52975, 9.35667, -0.3985, 9.36273, 0.10599, 9.37024, 0.19702, 9.36926, -5.18964, -2.91684, -2.33734, 5.47491, -2.2438, 5.51382, -0.33655, 5.94382, -0.25305, 5.94774, 0.06729, 5.95259, -5.18964, -2.91684, -2.33734, 5.47491, -2.2438, 5.51382, -0.33655, 5.94382, -0.25305, 5.94774, 0.06729, 5.95259, -1.28448, 5.81302, -5.18964, -2.91684, -2.33734, 5.47491, -2.2438, 5.51382, -0.33655, 5.94382, -0.25305, 5.94774, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.81091, -1.57968, -1.26584, 2.96542, -1.21524, 2.98654, -0.18225, 3.21942, -0.13708, 3.22137, -2.26981, -1.27563, -1.02222, 2.39459, -0.98138, 2.41162, -0.14719, 2.59973, -0.11075, 2.60132, 0.02948, 2.60353, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.10663, -0.48497, 0.43317, 0.24316, 0, 0, 0, 0, 1.66248, -7.52542, 6.71872, 3.77557, 3.02667, -7.08774, 2.90466, -7.13873, 0.43549, -7.69447, 0.32803, -7.70004, 1.08539, -4.91232, 4.38586, 2.46463, 1.97565, -4.62668, 1.89621, -4.6601, 0.28427, -5.02248, 0.21423, -5.0262, -0.05682, -5.03061, 1.66248, -7.52542, 6.71872, 3.77557, 3.02667, -7.08774, 2.90466, -7.13873, 0.43549, -7.69447, 0.32803, -7.70004, -0.08685, -7.70644, 2.24591, -10.16568, 9.0762, 5.10034, 4.08856, -9.57458, 3.92386, -9.64359, 0.58826, -10.39391, 0.44299, -10.40144, -0.11743, -10.41054, 1.66248, -7.52542, 6.71872, 3.77557, 3.02667, -7.08774, 2.90466, -7.13873, 0.43549, -7.69447, 0.32803, -7.70004, -0.08685, -7.70644, -0.16202, -7.70515, 1.52234, -6.89082, 6.15231, 3.45728, 2.77145, -6.49017, 2.65973, -6.53687, 0.39874, -7.04562, 0.30017, -7.05067, -0.07956, -7.05682, -0.14813, -7.05554, 1.66248, -7.52542, 6.71872, 3.77557, 2.90466, -7.13873, 0.32803, -7.70004, -0.08685, -7.70644, -0.16202, -7.70515, 1.52234, -6.89082, 6.15231, 3.45728, 2.65973, -6.53687, 0.39874, -7.04562, 0.30017, -7.05067, -0.07956, -7.05682, -0.14813, -7.05554, -0.20062, -7.05406, 0.43549, -7.69447, -0.08685, -7.70644, -0.16202, -7.70515, -0.21906, -7.70367, 1.52234, -6.89082, 6.15231, 3.45728, 2.65973, -6.53687, 0.39874, -7.04562, 0.30017, -7.05067, -0.07956, -7.05682, -0.14813, -7.05554, -0.20062, -7.05406, -0.23633, -7.05316, 0.43549, -7.69447, -0.08685, -7.70644, -0.16202, -7.70515, -0.21906, -7.70367, -0.25793, -7.70259, 1.00974, -7.64059, -0.07956, -7.05682, -0.14813, -7.05554, -0.20062, -7.05406, -0.23633, -7.05316, 0.92458, -6.99635, -0.29486, 5.20718, -0.22186, 5.21062, 0.05896, 5.215, 0.10962, 5.21428, 0.14801, 5.21339, 0.1745, 5.21251, -0.68321, 5.17036, -1.32375, 5.04448, -3.90003, 3.46283, -5.00845, 1.45468, -0.08685, -7.70644, -0.16202, -7.70515, -0.21906, -7.70367, -0.25793, -7.70259, 1.00974, -7.64059, 5.7629, -5.11716, 7.40096, -2.14981, -0.14813, -7.05554, -0.20062, -7.05406, -0.23633, -7.05316, 0.92458, -6.99635, -0.08685, -7.70644, -0.16202, -7.70515, -0.21906, -7.70367, -0.25793, -7.70259, 1.00974, -7.64059, 1.95591, -7.45473, 5.7629, -5.11716, 7.40096, -2.14981, -0.20062, -7.05406, -0.23633, -7.05316, 0.92458, -6.99635, 1.79092, -6.82614, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.23633, -7.05316, 0.92458, -6.99635, 1.79092, -6.82614, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.36548, -1.89151, -1.51575, 3.55045, -1.45511, 3.57565, -0.21835, 3.85445, -0.16428, 3.85698, 0.04355, 3.86024, -4.51105, -2.53537, -2.03168, 4.75891, -1.95041, 4.79279, -0.29266, 5.16646, -0.22015, 5.16986, 0.05841, 5.17426, 0.1087, 5.17352, -2.81931, -1.58447, -1.26965, 2.9743, -1.21887, 2.99542, -0.18283, 3.22894, -0.13751, 3.23106, 0.03659, 3.23381, 0.06802, 3.23334, 0.09183, 3.2328, -4.51105, -2.53534, -2.03165, 4.75894, -1.95038, 4.79279, -0.29266, 5.16644, -0.22015, 5.16985, 0.05841, 5.17426, 0.1087, 5.17351, 0.14682, 5.17265, -3.3833, -1.90152, -1.4628, 3.5946, -0.21948, 3.87486, -0.1651, 3.87741, 0.04382, 3.88071, 0.08154, 3.88016, 0.11011, 3.87952, 0.12985, 3.87886, -1.22668, 3.01447, -0.18402, 3.24951, -0.1384, 3.25162, 0.0368, 3.25436, 0.06842, 3.25394, 0.09241, 3.2534, 0.10892, 3.25284, -0.42633, 3.22652], "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "offset": 32, "vertices": [1.08539, -4.91229, 4.38577, 2.46463, 1.97568, -4.62665, 1.89615, -4.66, 0.28427, -5.02245, 0.21423, -5.02617, -0.05676, -5.03056, 0.72729, -3.29205, 2.93918, 1.65167, 1.32407, -3.10062, 1.27069, -3.12302, 0.19049, -3.36583, 0.14355, -3.36835, -0.03806, -3.37131, 0.36536, -1.65457, 1.4772, 0.83011, 0.66577, -1.55826, 0.63879, -1.56979, 0.09579, -1.69147, 0.0723, -1.69296, -0.01904, -1.69455, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.29379, 5.18698, -0.22092, 5.19028, 0.05875, 5.19469, 0.10922, 5.194, 0.14734, 5.19318, 0.17383, 5.19228, -0.68054, 5.15021, -1.31871, 5.02477, -3.88492, 3.44933, -4.98895, 1.44897, -0.33655, 5.94382, -0.25305, 5.94774, 0.06729, 5.95259, 0.12518, 5.95187, 0.16913, 5.95091, 0.19911, 5.94984, -0.7798, 5.9016, -1.51099, 5.75792, -4.45169, 3.95248, -5.71695, 1.66043, -3.00574, 7.38611, -0.45081, 7.96214, -0.33908, 7.96736, 0.09015, 7.97383, 0.16763, 7.97292, 0.2265, 7.97159, 0.26671, 7.97012, -1.0446, 7.90559, -5.96334, 5.29465, -3.24908, 7.98425, -0.4873, 8.60686, -0.36658, 8.61253, 0.09741, 8.61952, 0.18121, 8.61852, 0.24487, 8.61707, 0.28831, 8.61552, -1.12918, 8.54576, -7.51477, -4.22351, -3.24908, 7.98419, -0.4873, 8.6068, -0.36658, 8.61247, 0.09744, 8.61946, 0.18121, 8.61845, 0.24481, 8.617, 0.28833, 8.61548, -8.13272, -4.57083, -3.6629, 8.57971, -3.51624, 8.64078, -0.52737, 9.31454, -0.39673, 9.32069, 0.10547, 9.32828, 0.19608, 9.32716, 0.26495, 9.32559, 0.31204, 9.32397, -9.27695, -5.21384, -4.17819, 9.78674, -4.01093, 9.85648, -0.60156, 10.625, -0.45261, 10.63202, 0.1203, 10.64066, 0.22366, 10.63939, 0.30222, 10.63757, -8.1694, -4.59143, -3.67917, 8.61838, -3.53214, 8.67966, -0.52975, 9.35667, -0.3985, 9.36273, 0.10599, 9.37024, 0.19702, 9.36926, -5.18964, -2.91684, -2.33734, 5.47491, -2.2438, 5.51382, -0.33655, 5.94382, -0.25305, 5.94774, 0.06729, 5.95259, -5.18964, -2.91684, -2.33734, 5.47491, -2.2438, 5.51382, -0.33655, 5.94382, -0.25305, 5.94774, 0.06729, 5.95259, -1.28448, 5.81302, -5.18964, -2.91684, -2.33734, 5.47491, -2.2438, 5.51382, -0.33655, 5.94382, -0.25305, 5.94774, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.81091, -1.57968, -1.26584, 2.96542, -1.21524, 2.98654, -0.18225, 3.21942, -0.13708, 3.22137, -2.26981, -1.27563, -1.02222, 2.39459, -0.98138, 2.41162, -0.14719, 2.59973, -0.11075, 2.60132, 0.02948, 2.60353, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.10663, -0.48497, 0.43317, 0.24316, 0, 0, 0, 0, 1.66248, -7.52542, 6.71872, 3.77557, 3.02667, -7.08774, 2.90466, -7.13873, 0.43549, -7.69447, 0.32803, -7.70004, 1.08539, -4.91232, 4.38586, 2.46463, 1.97565, -4.62668, 1.89621, -4.6601, 0.28427, -5.02248, 0.21423, -5.0262, -0.05682, -5.03061, 1.66248, -7.52542, 6.71872, 3.77557, 3.02667, -7.08774, 2.90466, -7.13873, 0.43549, -7.69447, 0.32803, -7.70004, -0.08685, -7.70644, 2.24591, -10.16568, 9.0762, 5.10034, 4.08856, -9.57458, 3.92386, -9.64359, 0.58826, -10.39391, 0.44299, -10.40144, -0.11743, -10.41054, 1.66248, -7.52542, 6.71872, 3.77557, 3.02667, -7.08774, 2.90466, -7.13873, 0.43549, -7.69447, 0.32803, -7.70004, -0.08685, -7.70644, -0.16202, -7.70515, 1.52234, -6.89082, 6.15231, 3.45728, 2.77145, -6.49017, 2.65973, -6.53687, 0.39874, -7.04562, 0.30017, -7.05067, -0.07956, -7.05682, -0.14813, -7.05554, 1.66248, -7.52542, 6.71872, 3.77557, 2.90466, -7.13873, 0.32803, -7.70004, -0.08685, -7.70644, -0.16202, -7.70515, 1.52234, -6.89082, 6.15231, 3.45728, 2.65973, -6.53687, 0.39874, -7.04562, 0.30017, -7.05067, -0.07956, -7.05682, -0.14813, -7.05554, -0.20062, -7.05406, 0.43549, -7.69447, -0.08685, -7.70644, -0.16202, -7.70515, -0.21906, -7.70367, 1.52234, -6.89082, 6.15231, 3.45728, 2.65973, -6.53687, 0.39874, -7.04562, 0.30017, -7.05067, -0.07956, -7.05682, -0.14813, -7.05554, -0.20062, -7.05406, -0.23633, -7.05316, 0.43549, -7.69447, -0.08685, -7.70644, -0.16202, -7.70515, -0.21906, -7.70367, -0.25793, -7.70259, 1.00974, -7.64059, -0.07956, -7.05682, -0.14813, -7.05554, -0.20062, -7.05406, -0.23633, -7.05316, 0.92458, -6.99635, -0.29486, 5.20718, -0.22186, 5.21062, 0.05896, 5.215, 0.10962, 5.21428, 0.14801, 5.21339, 0.1745, 5.21251, -0.68321, 5.17036, -1.32375, 5.04448, -3.90003, 3.46283, -5.00845, 1.45468, -0.08685, -7.70644, -0.16202, -7.70515, -0.21906, -7.70367, -0.25793, -7.70259, 1.00974, -7.64059, 5.7629, -5.11716, 7.40096, -2.14981, -0.14813, -7.05554, -0.20062, -7.05406, -0.23633, -7.05316, 0.92458, -6.99635, -0.08685, -7.70644, -0.16202, -7.70515, -0.21906, -7.70367, -0.25793, -7.70259, 1.00974, -7.64059, 1.95591, -7.45473, 5.7629, -5.11716, 7.40096, -2.14981, -0.20062, -7.05406, -0.23633, -7.05316, 0.92458, -6.99635, 1.79092, -6.82614, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.23633, -7.05316, 0.92458, -6.99635, 1.79092, -6.82614, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.36548, -1.89151, -1.51575, 3.55045, -1.45511, 3.57565, -0.21835, 3.85445, -0.16428, 3.85698, 0.04355, 3.86024, -4.51105, -2.53537, -2.03168, 4.75891, -1.95041, 4.79279, -0.29266, 5.16646, -0.22015, 5.16986, 0.05841, 5.17426, 0.1087, 5.17352, -2.81931, -1.58447, -1.26965, 2.9743, -1.21887, 2.99542, -0.18283, 3.22894, -0.13751, 3.23106, 0.03659, 3.23381, 0.06802, 3.23334, 0.09183, 3.2328, -4.51105, -2.53534, -2.03165, 4.75894, -1.95038, 4.79279, -0.29266, 5.16644, -0.22015, 5.16985, 0.05841, 5.17426, 0.1087, 5.17351, 0.14682, 5.17265, -3.3833, -1.90152, -1.4628, 3.5946, -0.21948, 3.87486, -0.1651, 3.87741, 0.04382, 3.88071, 0.08154, 3.88016, 0.11011, 3.87952, 0.12985, 3.87886, -1.22668, 3.01447, -0.18402, 3.24951, -0.1384, 3.25162, 0.0368, 3.25436, 0.06842, 3.25394, 0.09241, 3.2534, 0.10892, 3.25284, -0.42633, 3.22652], "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "zs1": {"zs1": [{"curve": 0.25, "c3": 0.75}, {"time": 1.5, "offset": 8, "vertices": [9.71643, -1.41382, 7.50586, -9.51886, 13.18427, -6.30457, 5.25757, -2.93549, 9.73724, -0.61078, 10.20276, 1.09589, 10.20276, 1.09589, 10.20276, 1.09589, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.50679, 2.44498, 1.40179, 2.50656, 1.30434, 2.55798, 1.21778, 2.60101, 1.14494, 2.63342, 1.08621, 2.65848, 1.04287, 2.67606, 1.57565, 3.20084, 1.43865, 3.26468, 1.31193, 3.31705, 1.20002, 3.35965, 1.10581, 3.39163, 1.03038, 3.41541, 0.97474, 3.43185, 2.06396, 4.19257, 1.88455, 4.27631, 1.71854, 4.34476, 1.57195, 4.40067, 1.44858, 4.44254, 1.34976, 4.47366, 1.27689, 4.49524, -4.43231, 1.72726, 2.04198, 4.14786, 1.86432, 4.23059, 1.70016, 4.29779, 1.55504, 4.35382, 1.43327, 4.39468, 1.33533, 4.42563, -3.37183, 1.31424, 1.55353, 3.15576, 1.41849, 3.21872, 1.29355, 3.27029, 1.1832, 3.31238, 1.09033, 3.3439, 1.01595, 3.36728, -3.32385, 1.29562, 1.53148, 3.11093, 1.39835, 3.17294, 1.27518, 3.22385, 1.1664, 3.26529, 1.07485, 3.29645, -3.92596, 1.53021, 1.80882, 3.67444, 1.65155, 3.74762, 1.5061, 3.8078, 1.37758, 3.85675, 1.26944, 3.89349, -5.08148, 1.98087, 2.34142, 4.75583, 2.13793, 4.85065, 1.9497, 4.92868, 1.78335, 4.99191, 1.64333, 5.03955, -3.37177, 1.3143, 1.55354, 3.15567, 1.41851, 3.21854, 1.2936, 3.27026, 1.18322, 3.31229, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.53369, -1.50934, 2.08307, 5.3443, 4.38789, -2.80722, 4.50291, -2.61905, 8.02722, -2.18903, 3.02155, 7.75235, 6.8913, -1.87933, 2.59393, 6.65546, 9.0954, 0.56036, 0.39966, 9.1044, 9.0954, 0.56036, 0.39966, 9.1044, 7.26135, 1.06042, -0.29248, 7.33432, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -9.4223, -0.03418, -0.95795, -9.37164, -1.3573, 7.49844, 6.67944, 0.29956, 6.66072, 0.58203, 3.58911, -1.39929, -1.6539, -3.35931, -1.51028, -3.42633, -1.37732, -3.48227, -1.25988, -3.52615, 5.02478, -1.95901, -2.31549, -4.70306, -2.11443, -4.79687, -1.92827, -4.87521, -1.76385, -4.93668, -1.62489, -4.98434, 6.52246, -2.54297, -3.00562, -6.10486, -2.74461, -6.22675, -2.50301, -6.32834, -2.28953, -6.40802, -2.10922, -6.47, -1.96573, -6.51474, -3.60985, -7.33209, -3.29644, -7.47842, -3.00622, -7.60077, -2.74987, -7.69614, -2.53316, -7.77075, -2.36089, -7.82446, -2.23328, -7.8616, -3.5984, -8.16379, -3.2816, -8.297, -3.00179, -8.40158, -2.76537, -8.4827, -2.57726, -8.54144, -2.43794, -8.58218, -3.03012, -7.66104, -2.77174, -7.7576, -2.55345, -7.83252, -2.37972, -7.88681, -2.25111, -7.92432, 0, 0, 0, 0, 0, 0, 0, 0, 4.06348, -0.55072, 0, 0, 0, 0, -1.03876, -3.80707, 3.67639, -1.43359, 8.26965, -2.25525, 7.15973, -6.32397, 7.04169, 6.45508, 8.26965, -2.25525, 6.6142, -8.32355, 8.9726, 5.70203, 8.20282, 0.80469, 8.20282, 0.80469, 0.06226, 8.24438, 11.81525, -0.18115, 1.42285, 11.73135, 11.81525, -0.18115, 1.42285, 11.73135, 4.47375, -1.22003, 1.68402, 4.32053, 5.36853, -1.46405, -1.7514, -11.17657, 10.92859, -2.91656, -1.67438, -6.13739, 5.9267, -2.31055, 0, 0, 0, 0, 0, 0, 0, 0, -1.3573, 7.49844, 6.67944, 0.29956, 6.66072, 0.58203, -2.04645, -7.50128, 7.24365, -2.82391, -3.33791, -6.78021, -3.04813, -6.9155, -2.77977, -7.02838, -2.54272, -7.11691, -2.34245, -7.18542, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.04645, -7.50128, 7.24365, -2.82391, -3.33791, -6.78021, -3.04813, -6.9155, -2.77977, -7.02838, -2.54272, -7.11691, -2.34245, -7.18542, 6.04468, -2.35645, -2.54344, -5.77063, -2.31953, -5.8649, -2.12177, -5.93869, -1.95465, -5.99591, -1.84424, -6.76031, 6.5282, -2.54507, -3.00819, -6.11041, -2.74698, -6.23233, -2.50517, -6.33405, -2.29156, -6.41385, -2.11107, -6.47568, 6.04468, -2.35645, -2.31953, -5.8649, -2.12177, -5.93869, -1.95465, -5.99591, 7.84528, -3.05844, -3.30111, -7.48975, -3.01054, -7.61191, -2.75381, -7.70779, -2.53692, -7.78207, -2.3644, -7.83624, -1.90482, -4.32166, -1.73711, -4.3924, -1.58901, -4.44754, -1.46376, -4.49051, 5.21112, -2.03156, -2.19274, -4.97495, -1.99973, -5.05609, -1.8292, -5.11981, -1.68516, -5.16913, -1.57054, -5.20508, -1.73711, -4.3924, -1.58901, -4.44754, -1.46376, -4.49051, -1.36425, -4.52161, -1.29044, -4.543, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.33334, -1.3624, -2.28659, -1.43854, -2.24495, -1.50281, -2.2114, -1.55161, -2.1858, -1.58694], "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "offset": 8, "vertices": [9.71643, -1.41382, 7.50586, -9.51886, 13.18427, -6.30457, 5.25757, -2.93549, 9.73724, -0.61078, 10.20276, 1.09589, 10.20276, 1.09589, 10.20276, 1.09589, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.50679, 2.44498, 1.40179, 2.50656, 1.30434, 2.55798, 1.21778, 2.60101, 1.14494, 2.63342, 1.08621, 2.65848, 1.04287, 2.67606, 1.57565, 3.20084, 1.43865, 3.26468, 1.31193, 3.31705, 1.20002, 3.35965, 1.10581, 3.39163, 1.03038, 3.41541, 0.97474, 3.43185, 2.06396, 4.19257, 1.88455, 4.27631, 1.71854, 4.34476, 1.57195, 4.40067, 1.44858, 4.44254, 1.34976, 4.47366, 1.27689, 4.49524, -4.43231, 1.72726, 2.04198, 4.14786, 1.86432, 4.23059, 1.70016, 4.29779, 1.55504, 4.35382, 1.43327, 4.39468, 1.33533, 4.42563, -3.37183, 1.31424, 1.55353, 3.15576, 1.41849, 3.21872, 1.29355, 3.27029, 1.1832, 3.31238, 1.09033, 3.3439, 1.01595, 3.36728, -3.32385, 1.29562, 1.53148, 3.11093, 1.39835, 3.17294, 1.27518, 3.22385, 1.1664, 3.26529, 1.07485, 3.29645, -3.92596, 1.53021, 1.80882, 3.67444, 1.65155, 3.74762, 1.5061, 3.8078, 1.37758, 3.85675, 1.26944, 3.89349, -5.08148, 1.98087, 2.34142, 4.75583, 2.13793, 4.85065, 1.9497, 4.92868, 1.78335, 4.99191, 1.64333, 5.03955, -3.37177, 1.3143, 1.55354, 3.15567, 1.41851, 3.21854, 1.2936, 3.27026, 1.18322, 3.31229, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.53369, -1.50934, 2.08307, 5.3443, 4.38789, -2.80722, 4.50291, -2.61905, 8.02722, -2.18903, 3.02155, 7.75235, 6.8913, -1.87933, 2.59393, 6.65546, 9.0954, 0.56036, 0.39966, 9.1044, 9.0954, 0.56036, 0.39966, 9.1044, 7.26135, 1.06042, -0.29248, 7.33432, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -9.4223, -0.03418, -0.95795, -9.37164, -1.3573, 7.49844, 6.67944, 0.29956, 6.66072, 0.58203, 3.58911, -1.39929, -1.6539, -3.35931, -1.51028, -3.42633, -1.37732, -3.48227, -1.25988, -3.52615, 5.02478, -1.95901, -2.31549, -4.70306, -2.11443, -4.79687, -1.92827, -4.87521, -1.76385, -4.93668, -1.62489, -4.98434, 6.52246, -2.54297, -3.00562, -6.10486, -2.74461, -6.22675, -2.50301, -6.32834, -2.28953, -6.40802, -2.10922, -6.47, -1.96573, -6.51474, -3.60985, -7.33209, -3.29644, -7.47842, -3.00622, -7.60077, -2.74987, -7.69614, -2.53316, -7.77075, -2.36089, -7.82446, -2.23328, -7.8616, -3.5984, -8.16379, -3.2816, -8.297, -3.00179, -8.40158, -2.76537, -8.4827, -2.57726, -8.54144, -2.43794, -8.58218, -3.03012, -7.66104, -2.77174, -7.7576, -2.55345, -7.83252, -2.37972, -7.88681, -2.25111, -7.92432, 0, 0, 0, 0, 0, 0, 0, 0, 4.06348, -0.55072, 0, 0, 0, 0, -1.03876, -3.80707, 3.67639, -1.43359, 8.26965, -2.25525, 7.15973, -6.32397, 7.04169, 6.45508, 8.26965, -2.25525, 6.6142, -8.32355, 8.9726, 5.70203, 8.20282, 0.80469, 8.20282, 0.80469, 0.06226, 8.24438, 11.81525, -0.18115, 1.42285, 11.73135, 11.81525, -0.18115, 1.42285, 11.73135, 4.47375, -1.22003, 1.68402, 4.32053, 5.36853, -1.46405, -1.7514, -11.17657, 10.92859, -2.91656, -1.67438, -6.13739, 5.9267, -2.31055, 0, 0, 0, 0, 0, 0, 0, 0, -1.3573, 7.49844, 6.67944, 0.29956, 6.66072, 0.58203, -2.04645, -7.50128, 7.24365, -2.82391, -3.33791, -6.78021, -3.04813, -6.9155, -2.77977, -7.02838, -2.54272, -7.11691, -2.34245, -7.18542, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.04645, -7.50128, 7.24365, -2.82391, -3.33791, -6.78021, -3.04813, -6.9155, -2.77977, -7.02838, -2.54272, -7.11691, -2.34245, -7.18542, 6.04468, -2.35645, -2.54344, -5.77063, -2.31953, -5.8649, -2.12177, -5.93869, -1.95465, -5.99591, -1.84424, -6.76031, 6.5282, -2.54507, -3.00819, -6.11041, -2.74698, -6.23233, -2.50517, -6.33405, -2.29156, -6.41385, -2.11107, -6.47568, 6.04468, -2.35645, -2.31953, -5.8649, -2.12177, -5.93869, -1.95465, -5.99591, 7.84528, -3.05844, -3.30111, -7.48975, -3.01054, -7.61191, -2.75381, -7.70779, -2.53692, -7.78207, -2.3644, -7.83624, -1.90482, -4.32166, -1.73711, -4.3924, -1.58901, -4.44754, -1.46376, -4.49051, 5.21112, -2.03156, -2.19274, -4.97495, -1.99973, -5.05609, -1.8292, -5.11981, -1.68516, -5.16913, -1.57054, -5.20508, -1.73711, -4.3924, -1.58901, -4.44754, -1.46376, -4.49051, -1.36425, -4.52161, -1.29044, -4.543, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.33334, -1.3624, -2.28659, -1.43854, -2.24495, -1.50281, -2.2114, -1.55161, -2.1858, -1.58694], "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "zy": {"zy": [{"curve": 0.25, "c3": 0.75}, {"time": 0.9333, "offset": 10, "vertices": [0.64224, 1.76709, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.37744, -1.03754, -3.26154, -2.27429, -8.14642, -0.75378, 0, 0, -3.29144, -2.35712, -4.18204, -2.39087, -4.20996, 1.20282, -4.25751, 3.07983, -4.81635, 2.75848], "curve": "stepped"}, {"time": 1.7, "offset": 10, "vertices": [0.64224, 1.76709, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.37744, -1.03754, -3.26154, -2.27429, -8.14642, -0.75378, 0, 0, -3.29144, -2.35712, -4.18204, -2.39087, -4.20996, 1.20282, -4.25751, 3.07983, -4.81635, 2.75848], "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 2.8667, "offset": 10, "vertices": [0.1441, 0.3965, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.08469, -0.2328, -0.73182, -0.5103, -1.82787, -0.16913, 0, 0, -0.73853, -0.52888, -0.93836, -0.53646, -0.94462, 0.26989, -0.95529, 0.69105, -1.08068, 0.61894], "curve": 0.351, "c2": 0.4, "c3": 0.687, "c4": 0.75}, {"time": 3.1667, "vertices": [-2.42514, -6.66821, -2.7709, -7.6189, -2.93881, -8.0816, -1.57254, 3.57477, -1.9104, 7.00006, -0.27787, 3.5081, 1.95425, 3.04883, 0, 0, 0, 0, -4.80835, -13.22034, -5.53513, -15.9386, -1.88837, -13.35114, -2.6825, -7.3761, -4.30389, -11.83392, -6.74612, -19.48779, -3.24177, -16.81177, -3.7157, -10.22205, -6.57962, -16.99279, -4.60183, -7.55168, -3.6467, 0.80876, -3.73971, 0.69125, -4.97066, -6.87937], "curve": 0.369, "c2": 0.63, "c3": 0.707}, {"time": 3.5667, "offset": 10, "vertices": [0.08355, 0.22989, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.0491, -0.13498, -0.42432, -0.29588, -1.05982, -0.09806, 0, 0, -0.42821, -0.30665, -0.54407, -0.31104, -0.5477, 0.15648, -0.55389, 0.40068, -0.62659, 0.35887], "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 4.3, "offset": 10, "vertices": [0.64224, 1.76709, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.37744, -1.03754, -3.26154, -2.27429, -8.14642, -0.75378, 0, 0, -3.29144, -2.35712, -4.18204, -2.39087, -4.20996, 1.20282, -4.25751, 3.07983, -4.81635, 2.75848], "curve": "stepped"}, {"time": 5.0667, "offset": 10, "vertices": [0.64224, 1.76709, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.37744, -1.03754, -3.26154, -2.27429, -8.14642, -0.75378, 0, 0, -3.29144, -2.35712, -4.18204, -2.39087, -4.20996, 1.20282, -4.25751, 3.07983, -4.81635, 2.75848], "curve": 0.25, "c3": 0.75}, {"time": 6.6667}]}, "t2": {"t2": [{"curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.9333, "vertices": [13.37635, -6.12567, 11.2971, -9.42395, 13.28173, -6.33438, 10.43037, -6.31382, 8.40524, -8.83015, 10.33247, -6.47623, 7.10246, -4.59587, 5.64693, -6.29684, 7.0322, -4.70638, 6.11975, -3.28324, 5.0449, -4.77218, 6.07017, -3.37871, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.69296, -0.48422, 2.72264, -0.21272, 2.69118, 0.4646, 3.09732, -4.54634, 3.52697, -4.21543, 4.46338, -3.21294, 3.16745, -7.19021, 3.85737, -6.83911, 5.43384, -5.67332, 6.96612, -9.55583, -0.53157, -9.12561, 1.74917, -8.9765, -2.5801, -5.29059, -2.04885, -5.51671, -0.61679, -5.85697, -4.15713, -0.1767, -4.12405, -0.58314, -3.85048, -1.59224, -2.31358, -3.22514, -1.98747, -3.43478, -1.07417, -3.8249, -3.75043, -5.99914, -5.8476, -2.77593, -5.54857, -3.33639, -4.54803, -4.61281, -4.56481, -0.12578, -4.53392, -0.57292, -4.25078, -1.68378, -2.41307, 0.63124, -2.46747, 0.39284, -2.48766, -0.23587, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.29333, 4.47679, -0.09692, 1.49583, 2.88184, 4.07812, 1.32056, 4.59874, -0.22266, 3.39721, -0.24915, 3.80297, 0.64685, 10.11107, -0.40979, 6.24939, -0.4834, 7.36987, -0.39722, 6.05603, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7.17146, -0.19828, 6.8715, -2.0724, 7.1698, -0.31034, 10.26663, 0.25565, 9.97725, -2.44911, 10.27263, 0.09519, 12.99288, -1.85198, 12.05253, -5.2, 12.96438, -2.05513, 12.98083, -9.33338, 14.89055, -5.82891, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.11878, 1.80221, -1.33128, 1.22016, -0.80652, 1.61529, -0.96309, 1.52843, 0, 0, 0, 0, 0, 0, 0, 0, -0.12243, 1.85848, -1.37299, 1.2583, -0.8319, 1.66566, -0.99317, 1.57598, -1.35325, 1.27955, -0.02722, 0.41406, -0.30603, 0.2804, -0.22119, 0.35101, -0.14221, 2.16186, -1.59778, 1.46405, -0.97009, 1.93802, -0.09503, 1.44188, -0.64517, 1.29232, -0.77052, 1.22271, -0.23862, 3.6327, -2.68348, 2.45981, -1.62723, 3.25609, -1.94158, 3.07992, -2.6448, 2.50149, -0.49087, 0.4498, -0.29668, 0.59556, -0.35506, 0.56384, -0.21753, 3.31335, -2.44824, 2.24371, -1.48572, 2.97, -1.77051, 2.80884, -0.19263, 2.92968, -2.16486, 1.98407, -1.31384, 2.62622, -0.01857, 0.27593, -0.20384, 0.18669, -0.12251, 0.24718, -0.14741, 0.23441, -0.201, 0.18985, -0.19824, 3.02063, 0, 0, 0, 0, 0, 0, 0, 0, -3.26337, 0.8722, -3.34356, -0.46123, -3.28315, -0.78795, -2.98559, -1.57902, -0.25684, 3.92272, -2.89905, 2.65637, -1.7605, 3.51636, -2.09595, 3.32513, 0.08362, 4.92357, -3.34424, 3.61487, -1.83374, 4.57111, -2.27319, 4.36725, -0.2865, 4.38158, -3.23877, 2.96698, -1.96704, 3.92758, -2.34143, 3.71362, -1.45972, 4.15952, -3.93201, 1.99603, -2.96289, 3.26889, -3.26404, 2.95966], "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1.5, "vertices": [18.25489, -8.27203, 15.44016, -12.77588, 18.1267, -8.55685, 14.18519, -8.42015, 11.47506, -11.84742, 14.05439, -8.64066, 9.6616, -5.92576, 7.76778, -8.2502, 9.5709, -6.0757, 8.35645, -4.07852, 6.99521, -6.12545, 8.29466, -4.20848, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.29184, -0.62637, 1.32823, -0.48286, 1.40395, -0.15753, 1.71402, -5.4229, 2.22313, -5.21433, 3.44495, -4.5186, 1.7612, -8.54254, 2.5771, -8.31329, 4.5564, -7.43388, 6.31183, -12.0344, -2.6302, -10.95087, 0.16641, -11.28052, -4.99379, -6.22412, -4.37173, -6.67375, -2.58371, -7.56864, -6.78433, -0.1705, -6.75271, -0.82642, -6.33962, -2.49409, -5.47606, -3.58079, -5.11174, -4.09182, -3.94075, -5.25044, -7.29266, -7.90753, -9.85126, -2.97136, -9.52672, -3.91634, -8.26092, -6.17641, -8.22277, 0.29649, -8.22813, -0.50346, -7.8488, -2.547, -5.54002, 1.20381, -5.64972, 0.66371, -5.64269, -0.77755, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.29333, 4.47679, -0.09692, 1.49583, 2.88184, 4.07812, 1.32056, 4.59874, -0.22266, 3.39721, -0.24915, 3.80297, 0.64685, 10.11107, -0.40979, 6.24939, -0.4834, 7.36987, -0.39722, 6.05603, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9.92311, 0.13527, 9.6154, -2.47176, 9.927, -0.01957, 14.20773, 0.64371, 13.8835, -3.10902, 14.22002, 0.42209, 17.88747, -2.36498, 16.64128, -6.98046, 17.85061, -2.64437, 17.76489, -12.71473, 20.36284, -7.92071, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.11878, 1.80221, -1.33128, 1.22016, -0.80652, 1.61529, -0.96309, 1.52843, 0, 0, 0, 0, 0, 0, 0, 0, -0.12243, 1.85848, -1.37299, 1.2583, -0.8319, 1.66566, -0.99317, 1.57598, -1.35325, 1.27955, -0.02722, 0.41406, -0.30603, 0.2804, -0.22119, 0.35101, -0.14221, 2.16186, -1.59778, 1.46405, -0.97009, 1.93802, -0.09503, 1.44188, -0.64517, 1.29232, -0.77052, 1.22271, -0.23862, 3.6327, -2.68348, 2.45981, -1.62723, 3.25609, -1.94158, 3.07992, -2.6448, 2.50149, -0.49087, 0.4498, -0.29668, 0.59556, -0.35506, 0.56384, -0.21753, 3.31335, -2.44824, 2.24371, -1.48572, 2.97, -1.77051, 2.80884, -0.19263, 2.92968, -2.16486, 1.98407, -1.31384, 2.62622, -0.01857, 0.27593, -0.20384, 0.18669, -0.12251, 0.24718, -0.14741, 0.23441, -0.201, 0.18985, -0.19824, 3.02063, 0, 0, 0, 0, 0, 0, 0, 0, -3.26337, 0.8722, -3.34356, -0.46123, -3.28315, -0.78795, -2.98559, -1.57902, -0.25684, 3.92272, -2.89905, 2.65637, -1.7605, 3.51636, -2.09595, 3.32513, 0.08362, 4.92357, -3.34424, 3.61487, -1.83374, 4.57111, -2.27319, 4.36725, -0.2865, 4.38158, -3.23877, 2.96698, -1.96704, 3.92758, -2.34143, 3.71362, -1.45972, 4.15952, -3.93201, 1.99603, -2.96289, 3.26889, -3.26404, 2.95966], "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.3667, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 4.3, "vertices": [13.37635, -6.12567, 11.2971, -9.42395, 13.28173, -6.33438, 10.43037, -6.31382, 8.40524, -8.83015, 10.33247, -6.47623, 7.10246, -4.59587, 5.64693, -6.29684, 7.0322, -4.70638, 6.11975, -3.28324, 5.0449, -4.77218, 6.07017, -3.37871, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.69296, -0.48422, 2.72264, -0.21272, 2.69118, 0.4646, 3.09732, -4.54634, 3.52697, -4.21543, 4.46338, -3.21294, 3.16745, -7.19021, 3.85737, -6.83911, 5.43384, -5.67332, 6.96612, -9.55583, -0.53157, -9.12561, 1.74917, -8.9765, -2.5801, -5.29059, -2.04885, -5.51671, -0.61679, -5.85697, -4.15713, -0.1767, -4.12405, -0.58314, -3.85048, -1.59224, -2.31358, -3.22514, -1.98747, -3.43478, -1.07417, -3.8249, -3.75043, -5.99914, -5.8476, -2.77593, -5.54857, -3.33639, -4.54803, -4.61281, -4.56481, -0.12578, -4.53392, -0.57292, -4.25078, -1.68378, -2.41307, 0.63124, -2.46747, 0.39284, -2.48766, -0.23587, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.29333, 4.47679, -0.09692, 1.49583, 2.88184, 4.07812, 1.32056, 4.59874, -0.22266, 3.39721, -0.24915, 3.80297, 0.64685, 10.11107, -0.40979, 6.24939, -0.4834, 7.36987, -0.39722, 6.05603, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7.17146, -0.19828, 6.8715, -2.0724, 7.1698, -0.31034, 10.26663, 0.25565, 9.97725, -2.44911, 10.27263, 0.09519, 12.99288, -1.85198, 12.05253, -5.2, 12.96438, -2.05513, 12.98083, -9.33338, 14.89055, -5.82891, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.11878, 1.80221, -1.33128, 1.22016, -0.80652, 1.61529, -0.96309, 1.52843, 0, 0, 0, 0, 0, 0, 0, 0, -0.12243, 1.85848, -1.37299, 1.2583, -0.8319, 1.66566, -0.99317, 1.57598, -1.35325, 1.27955, -0.02722, 0.41406, -0.30603, 0.2804, -0.22119, 0.35101, -0.14221, 2.16186, -1.59778, 1.46405, -0.97009, 1.93802, -0.09503, 1.44188, -0.64517, 1.29232, -0.77052, 1.22271, -0.23862, 3.6327, -2.68348, 2.45981, -1.62723, 3.25609, -1.94158, 3.07992, -2.6448, 2.50149, -0.49087, 0.4498, -0.29668, 0.59556, -0.35506, 0.56384, -0.21753, 3.31335, -2.44824, 2.24371, -1.48572, 2.97, -1.77051, 2.80884, -0.19263, 2.92968, -2.16486, 1.98407, -1.31384, 2.62622, -0.01857, 0.27593, -0.20384, 0.18669, -0.12251, 0.24718, -0.14741, 0.23441, -0.201, 0.18985, -0.19824, 3.02063, 0, 0, 0, 0, 0, 0, 0, 0, -3.26337, 0.8722, -3.34356, -0.46123, -3.28315, -0.78795, -2.98559, -1.57902, -0.25684, 3.92272, -2.89905, 2.65637, -1.7605, 3.51636, -2.09595, 3.32513, 0.08362, 4.92357, -3.34424, 3.61487, -1.83374, 4.57111, -2.27319, 4.36725, -0.2865, 4.38158, -3.23877, 2.96698, -1.96704, 3.92758, -2.34143, 3.71362, -1.45972, 4.15952, -3.93201, 1.99603, -2.96289, 3.26889, -3.26404, 2.95966], "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 4.8667, "vertices": [18.25489, -8.27203, 15.44016, -12.77588, 18.1267, -8.55685, 14.18519, -8.42015, 11.47506, -11.84742, 14.05439, -8.64066, 9.6616, -5.92576, 7.76778, -8.2502, 9.5709, -6.0757, 8.35645, -4.07852, 6.99521, -6.12545, 8.29466, -4.20848, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.29184, -0.62637, 1.32823, -0.48286, 1.40395, -0.15753, 1.71402, -5.4229, 2.22313, -5.21433, 3.44495, -4.5186, 1.7612, -8.54254, 2.5771, -8.31329, 4.5564, -7.43388, 6.31183, -12.0344, -2.6302, -10.95087, 0.16641, -11.28052, -4.99379, -6.22412, -4.37173, -6.67375, -2.58371, -7.56864, -6.78433, -0.1705, -6.75271, -0.82642, -6.33962, -2.49409, -5.47606, -3.58079, -5.11174, -4.09182, -3.94075, -5.25044, -7.29266, -7.90753, -9.85126, -2.97136, -9.52672, -3.91634, -8.26092, -6.17641, -8.22277, 0.29649, -8.22813, -0.50346, -7.8488, -2.547, -5.54002, 1.20381, -5.64972, 0.66371, -5.64269, -0.77755, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.29333, 4.47679, -0.09692, 1.49583, 2.88184, 4.07812, 1.32056, 4.59874, -0.22266, 3.39721, -0.24915, 3.80297, 0.64685, 10.11107, -0.40979, 6.24939, -0.4834, 7.36987, -0.39722, 6.05603, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9.92311, 0.13527, 9.6154, -2.47176, 9.927, -0.01957, 14.20773, 0.64371, 13.8835, -3.10902, 14.22002, 0.42209, 17.88747, -2.36498, 16.64128, -6.98046, 17.85061, -2.64437, 17.76489, -12.71473, 20.36284, -7.92071, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.11878, 1.80221, -1.33128, 1.22016, -0.80652, 1.61529, -0.96309, 1.52843, 0, 0, 0, 0, 0, 0, 0, 0, -0.12243, 1.85848, -1.37299, 1.2583, -0.8319, 1.66566, -0.99317, 1.57598, -1.35325, 1.27955, -0.02722, 0.41406, -0.30603, 0.2804, -0.22119, 0.35101, -0.14221, 2.16186, -1.59778, 1.46405, -0.97009, 1.93802, -0.09503, 1.44188, -0.64517, 1.29232, -0.77052, 1.22271, -0.23862, 3.6327, -2.68348, 2.45981, -1.62723, 3.25609, -1.94158, 3.07992, -2.6448, 2.50149, -0.49087, 0.4498, -0.29668, 0.59556, -0.35506, 0.56384, -0.21753, 3.31335, -2.44824, 2.24371, -1.48572, 2.97, -1.77051, 2.80884, -0.19263, 2.92968, -2.16486, 1.98407, -1.31384, 2.62622, -0.01857, 0.27593, -0.20384, 0.18669, -0.12251, 0.24718, -0.14741, 0.23441, -0.201, 0.18985, -0.19824, 3.02063, 0, 0, 0, 0, 0, 0, 0, 0, -3.26337, 0.8722, -3.34356, -0.46123, -3.28315, -0.78795, -2.98559, -1.57902, -0.25684, 3.92272, -2.89905, 2.65637, -1.7605, 3.51636, -2.09595, 3.32513, 0.08362, 4.92357, -3.34424, 3.61487, -1.83374, 4.57111, -2.27319, 4.36725, -0.2865, 4.38158, -3.23877, 2.96698, -1.96704, 3.92758, -2.34143, 3.71362, -1.45972, 4.15952, -3.93201, 1.99603, -2.96289, 3.26889, -3.26404, 2.95966], "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 6.6667}]}}}}}}