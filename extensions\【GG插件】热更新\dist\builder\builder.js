"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.configs = exports.getBuilderPluginConfig = void 0;
const config_1 = require("../config");
/**
 * 获取插件配置
 *
 * @param options 构建任务配置
 */
function getBuilderPluginConfig(options) {
  var _a;
  return (_a = options.packages) === null || _a === void 0 ? void 0 : _a[config_1.PACKAGE_NAME];
}
exports.getBuilderPluginConfig = getBuilderPluginConfig;
const buildPlugin = {
  hooks: "./builder-hook",
  options: {
    enable: {
      label: Editor.I18n.t(`${config_1.PACKAGE_NAME}.build_item_enable_label`),
      description: Editor.I18n.t(`${config_1.PACKAGE_NAME}.build_item_enable_desc`),
      default: false,
      render: {
        ui: "ui-checkbox",
      },
    },
    configPath: {
      label: Editor.I18n.t(`${config_1.PACKAGE_NAME}.build_item_config_path_label`),
      description: Editor.I18n.t(`${config_1.PACKAGE_NAME}.build_item_config_path_desc`),
      default: "",
      render: {
        ui: "ui-input",
        attributes: {
          placeholder: "",
        },
      },
    },
  },
};
exports.configs = {
  ios: buildPlugin,
  mac: buildPlugin,
  android: buildPlugin,
  windows: buildPlugin,
};
