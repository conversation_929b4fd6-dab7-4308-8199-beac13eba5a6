import { _decorator, CCInteger, Component, math, Node, UITransform } from "cc";
const { ccclass, property } = _decorator;

@ccclass("ScrollBg")
export class ScrollBg extends Component {
  @property(CCInteger)
  speed: number = 0;

  @property(CCInteger)
  direct: number = 1; // 1: 向上滚动，-1: 向下滚动

  size: math.Size;

  start() {
    this.size = this.node.getComponent(UITransform).contentSize;
  }

  update(deltaTime: number) {
    let y = this.node.position.y + this.speed * this.direct * deltaTime;
    if (this.direct == 1) {
      if (y > this.size.height / 2) {
        y -= this.size.height / 2;
      }
    } else {
      if (y < -this.size.height / 2) {
        y += this.size.height / 2;
      }
    }

    this.node.setPosition(0, y);
  }
}
