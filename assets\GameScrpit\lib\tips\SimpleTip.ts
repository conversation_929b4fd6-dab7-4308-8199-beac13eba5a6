import { Label, Layers, Node, RichText, Size, Sprite, UIOpacity, UITransform, color, tween, v3 } from "cc";
import ResMgr from "../common/ResMgr";
import { BundleEnum } from "../../game/bundleEnum/BundleEnum";
import { LayerEnum } from "../../game/GameDefine";

export default class SimpleTip extends Node {
  private static all: Array<SimpleTip> = [];
  private static clean: Array<SimpleTip> = [];
  private _label: RichText;
  private _bg: Sprite;
  private _hei: number = 40;
  private _vy: number = 200;
  constructor() {
    super("SimpleTip");
    // this.layer = Layers.Enum["UILayer"];
    this.layer = LayerEnum.TOP;
    this.addComponent(UIOpacity);
    var node: Node = new Node();
    this.addChild(node);
    // node.layer = Layers.Enum["UILayer"];
    node.layer = LayerEnum.TOP;
    this._bg = node.addComponent(Sprite);
    ResMgr.loadImage("resources?images/fightUI/tipS", this._bg);
    node.addComponent(UITransform).setContentSize(new Size(720, this._hei));
    node.setPosition(v3(0, 5, 0));
    var node: Node = new Node();
    // node.layer = Layers.Enum["UILayer"];
    node.layer = LayerEnum.TOP;
    this.addChild(node);
    this._label = node.addComponent(RichText);
    this._label.fontSize = 30;
    this._label.node.setPosition(v3(0, 2, 0));
    this._label.fontColor = color().fromHEX("#FFFFFF");
    this._label.useSystemFont = false;

    ResMgr.loadFont(`${BundleEnum.BUNDLE_COMMON_FONT}?font/almm_lishu`, this._label);
  }

  public static create(content: string, time: number): SimpleTip {
    var instance: SimpleTip = null;
    if (!instance) {
      instance = new SimpleTip();
    }
    instance.init(content, time);
    return instance;
  }

  public init(content: string, time: number) {
    this._label.string = content;
    //距离上一次点击时间如果超过一定时间整体坐标Y向上
    if (time > 800) {
      // 删除为空的对象，防止切换场景报错
      SimpleTip.all = SimpleTip.all.filter((e) => e != null && e.position);

      for (let i = 0; i < SimpleTip.all.length; i++) {
        let pos =
          SimpleTip.all.length > 4
            ? v3(
                SimpleTip.all[i].getPosition().x,
                SimpleTip.all[i].getPosition().y + 400,
                SimpleTip.all[i].getPosition().z
              )
            : v3(
                SimpleTip.all[i].getPosition().x,
                20 + (SimpleTip.all.length - i - 1) * 80,
                SimpleTip.all[i].getPosition().z
              );
        SimpleTip.all[i].setPosition(pos);
        if (SimpleTip.all[i].getPosition().y > 20 + (SimpleTip.all.length - i - 1) * 80) {
          SimpleTip.all[i].setPosition(
            v3(
              SimpleTip.all[i].getPosition().x,
              20 + (SimpleTip.all.length - i - 1) * 80,
              SimpleTip.all[i].getPosition().z
            )
          );
        }
      }
    }
    for (let i: number = 0; i < SimpleTip.all.length; i++) {
      // SimpleTip.all[i].stopAllActions();
      SimpleTip.all[i].setScale(v3(1, 1, 1));
      if (i > 0) {
        let pos = v3(
          SimpleTip.all[i - 1].getPosition().x,
          SimpleTip.all[i - 1].getPosition().y - 90,
          SimpleTip.all[i - 1].getPosition().z
        );
        SimpleTip.all[i].setPosition(pos);
      }
      let simpleNode = SimpleTip.all[i];
      let pos = v3(simpleNode.getPosition().x, 70, simpleNode.getPosition().z);
      tween(SimpleTip.all[i]).by(0.2, { position: pos }, { easing: "sineOut" }).start();
    }
    if (!this) return;
    SimpleTip.all.push(this);
    this.setScale(0.1, 0.1);
    this.getComponent(UIOpacity).opacity = 255;
    if (SimpleTip.all.length >= 2)
      this.setPosition(
        v3(this.getPosition().x, SimpleTip.all[SimpleTip.all.length - 2].getPosition().y - 20, this.getPosition().z)
      );
    if (SimpleTip.all.length == 1) this.setPosition(v3(this.getPosition().x, 0, this.getPosition().z));

    let node = this as Node;
    tween(node)
      .to(0.1, { scale: v3(1.1, 1.1, 1.1) })
      .to(0.1, { scale: v3(1.1, 1.1, 1.1) })
      .start();

    tween(node)
      .delay(2)
      .call(() => {
        this.alpha();
      })
      .start();
  }

  public alpha() {
    if (!this) return;
    tween(this.getComponent(UIOpacity))
      .to(0.6, { opacity: 0 })
      .call(() => {
        SimpleTip.all.splice(SimpleTip.all.indexOf(this), 1);
        this.recovery();
      })
      .start();
  }

  public recovery() {
    this.parent.removeChild(this);
  }
}
