import { _decorator, Label, math, RichText, Node } from "cc";
import { ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { JsonMgr } from "../../../mgr/JsonMgr";
import { divide } from "../../../../lib/utils/NumbersUtils";
import { HeroModule } from "../../../../module/hero/HeroModule";
import { IConfigHeroPicture } from "../../../JsonDefine";
import { LangMgr } from "../../../mgr/LangMgr";

const { ccclass, property } = _decorator;
@ccclass("TuJianLevelViewHolder")
export class TuJianLevelViewHolder extends ViewHolder {
  @property(Label)
  private lblLevel: Label = null;
  @property(RichText)
  private richContent: RichText = null;
  @property(Node)
  private nodeFinish: Node;
  @property(Node)
  private nodeNormal: Node;
  @property(Node)
  private nodeBar: Node;

  //
  //
  updateData(position: number, data: IConfigHeroPicture) {
    this.position = position;

    this.lblLevel.string = `${this.position + 1}`;
    let pictureLv = HeroModule.data.pictureMessage?.pictureMap[data.id] ?? 0;

    let content = ""; //data.des.replace("%s", `${data.level[this.position]}`);
    if (position == 0) {
      content = LangMgr.txMsgCode(228, [], ".获得该组合所有战将.");
    } else {
      content = LangMgr.txMsgCode(229, [data.level[this.position]], ".获得该组合所有战将.");
    }

    let reachNum = 0;
    for (let i = 0; i < data.heroId.length; i++) {
      let heroMessage = HeroModule.data.getHeroMessage(data.heroId[i]);
      if (heroMessage && heroMessage.level >= data.level[this.position]) {
        reachNum++;
      }
    }
    if (reachNum >= data.heroId.length && pictureLv > this.position) {
      content += `(${reachNum}/${data.heroId.length})`;
      this.richContent.fontColor = math.color("#b27728");
      this.lblLevel.color = math.color("#fff7b2");
      this.nodeFinish.active = true;
      this.nodeNormal.active = false;
      content = `${content}<br/><color=#00af04>`;
      for (let i = 0; i < data.attrAdd[this.position].length; i++) {
        let config = JsonMgr.instance.jsonList.c_attribute[data.attrAdd[this.position][i]];
        content += `${config.name} +${divide(data.attrAdd[this.position][++i], 100)}%<br/>`;
      }
      content = content.slice(0, -5);
      content = `${content}</color>`;
    } else {
      this.richContent.fontColor = math.color("#3973ae");
      this.lblLevel.color = math.color("#e6f2ff");
      content += `<color=#ff6f6f>(${reachNum}/${data.heroId.length})</color>`;
      this.nodeFinish.active = false;
      this.nodeNormal.active = true;
      content = `${content}<br/>`;
      for (let i = 0; i < data.attrAdd[this.position].length; i++) {
        let config = JsonMgr.instance.jsonList.c_attribute[data.attrAdd[this.position][i]];
        content += `${config.name} +${divide(data.attrAdd[this.position][++i], 100)}%<br/>`;
      }
      content = content.slice(0, -5);
    }

    this.richContent.string = content;

    this.refreshBar(data);
  }

  private refreshBar(data: IConfigHeroPicture) {
    let reachNumCur = 0;
    let reachNumLast = 0;
    for (let i = 0; i < data.heroId.length; i++) {
      let heroMessage = HeroModule.data.getHeroMessage(data.heroId[i]);
      if (heroMessage && heroMessage.level >= data.level[this.position]) {
        reachNumCur++;
      }
      if (this.position > 0 && heroMessage && heroMessage.level >= data.level[this.position - 1]) {
        reachNumLast++;
      }
    }
    let pictureLv = HeroModule.data.pictureMessage?.pictureMap[data.id] ?? 0;
    if (reachNumCur >= data.heroId.length && pictureLv > this.position) {
      this.nodeBar.getChildByName("bg_bar_finish_top").active = this.position != 0;
      this.nodeBar.getChildByName("bg_bar_finish_bottom").active = this.position != data.level.length - 1;
      this.nodeBar.getChildByName("bg_bar_nor_bottom").active = false;
      this.nodeBar.getChildByName("bg_bar_nor_top").active = false;
    } else if (reachNumLast >= data.heroId.length && pictureLv > this.position - 1) {
      this.nodeBar.getChildByName("bg_bar_finish_top").active = true;
      this.nodeBar.getChildByName("bg_bar_finish_bottom").active = false;
      this.nodeBar.getChildByName("bg_bar_nor_top").active = false;
      this.nodeBar.getChildByName("bg_bar_nor_bottom").active = this.position != data.level.length - 1;
    } else {
      this.nodeBar.getChildByName("bg_bar_finish_top").active = false;
      this.nodeBar.getChildByName("bg_bar_finish_bottom").active = false;
      this.nodeBar.getChildByName("bg_bar_nor_top").active = this.position != 0;
      this.nodeBar.getChildByName("bg_bar_nor_bottom").active = this.position != data.level.length - 1;
    }
  }
}
