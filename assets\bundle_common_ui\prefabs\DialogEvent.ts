import {
  _decorator,
  ccenum,
  CCInteger,
  CCString,
  Component,
  Font,
  Graphics,
  Label,
  math,
  Node,
  RichText,
  TTFFont,
  UITransform,
  Widget,
} from "cc";
import { UIMgr } from "db://assets/GameScrpit/lib/ui/UIMgr";
import { AudioMgr } from "../../platform/src/AudioHelper";
import { LangMgr } from "../../GameScrpit/game/mgr/LangMgr";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
// import { UIMgr } from "db://assets/GameScrpit/lib/";
const { ccclass, property, executeInEditMode } = _decorator;

const log = Logger.getLoger(LOG_LEVEL.STOP);

/**
 * @zh
 * 数据类型。
 */
enum DialogTileType {
  /**
   * @zh
   * 富文本 首字放大
   */
  RICH_TYPE1 = 0,
  RICH_TYPE2,
  /**
   * @zh
   * 普通文本
   */
  TEXT_TYPE1,
  /**
   * @zh
   * 双Label 首字放大（新增方案）
   */
  LABEL_DOUBLE, // 新增枚举值
}
ccenum(DialogTileType);

@ccclass("DialogEvent")
@executeInEditMode
export class DialogEvent extends Component {
  @property(Graphics)
  private graphics: Graphics;
  @property({ type: CCInteger })
  private _userRadius: number = 10;
  @property(Node)
  private _wrapNode: Node; //决定窗体大小的节点
  @property(Node)
  private dialogTitle: Node;
  @property({ serializable: true })
  private _strTitle: string = "";
  @property({ type: DialogTileType, tooltip: "标题类型", displayName: "标题类型" })
  private dialogTitleType: DialogTileType = DialogTileType.RICH_TYPE1;

  @property(Node)
  set wrapNode(value: Node) {
    this._wrapNode = value;
    this.draw();
  }
  get wrapNode(): Node {
    return this._wrapNode;
  }
  @property({ type: CCInteger })
  set radiu(value: number) {
    this._userRadius = value;
    this.draw();
  }
  get radiu(): number {
    return this._userRadius;
  }
  @property({ type: CCString, tooltip: "标题", displayName: "窗口标题", serializable: true })
  set strTitle(value: string) {
    this._strTitle = value;
    this.drawTitle();
  }
  get strTitle(): string {
    return this._strTitle;
  }

  private onClickClose() {
    //   // 关闭音效 effect_guanbiyinxiao
    // try {
    // } catch (error) {}
    AudioMgr.instance.playEffect?.(504);
    UIMgr.instance?.back();
    //UIMgr; //.instance; //.back();
    // AudioMgr;
    // FmUtils;
    log.log("onClickClose");
  }
  protected start(): void {
    // log.log("dialog event on start");
    // 兼容旧版本
    if (this.dialogTitle && this.strTitle === "" && this.dialogTitle.getComponent(Label)) {
      this.strTitle = this.dialogTitle.getComponent(Label).string;
    }
    this.draw();
    this._wrapNode &&
      this._wrapNode?.on(
        Node.EventType.SIZE_CHANGED,
        () => {
          this.node.getComponent(UITransform).setContentSize(this._wrapNode.getComponent(UITransform).contentSize);
          this.node.getComponentsInChildren(Widget).forEach((item) => {
            item.updateAlignment();
          });
        },
        this
      );
  }
  onFocusInEditor() {
    this.draw();
  }
  /**
   * 格式化标题文本样式（首字大，后续字小）
   * @param text 原始标题文本
   * @returns 格式化后的富文本字符串
   */
  private formatTitleText1(text: string, firstSize: number, otherSize: number): string {
    if (!text || text.length === 0) return "";
    // 首字符样式（对应"日"的样式）
    const firstChar = `<outline color=#ffa200 width=3><color=#ffffff><size=${firstSize}>${text[0]}</size></color></outline>`;
    // 后续字符样式（对应"志"的样式）
    const restChars = `<outline color=#ffa200 width=3><color=#ffffff><size=${otherSize}>${text.slice(
      1
    )}</size></color></outline>`;
    return firstChar + restChars;
  }
  private draw() {
    if (this.graphics) {
      this.graphics.getComponent(Widget).updateAlignment();
      let width = this.graphics.getComponent(UITransform).width;
      let height = this.graphics.getComponent(UITransform).height;
      let anchorX = this.graphics.getComponent(UITransform).anchorX;
      let anchorY = this.graphics.getComponent(UITransform).anchorY;
      let x = -width * anchorX;
      let y = height * (anchorY - 1);
      this.graphics.roundRect(x, y, width, height, this._userRadius);
      this.graphics.fill();
      // log.log("graphics is finish ");
      // log.log(`${width}-${height}-${this._userRadius}`);
    }
    if (this._wrapNode) {
      this.node.getComponent(UITransform).setContentSize(this._wrapNode.getComponent(UITransform).contentSize);
    }
    this.drawTitle();
  }
  private drawTitle() {
    if (this.dialogTitleType == DialogTileType.RICH_TYPE1 && this.dialogTitle) {
      this.dialogTitle.getComponent(Label)?.destroy();
      if (!this.dialogTitle.getComponent(RichText)) {
        this.dialogTitle.addComponent(RichText);
        this.dialogTitle.getComponent(RichText).lineHeight = 68;
        this.dialogTitle.getComponent(RichText).useSystemFont = false;
      }
      // 使用封装函数生成富文本（使用类属性strTitle作为输入）
      this.dialogTitle.getComponent(RichText).string = this.formatTitleText1(this.strTitle, 68, 60);
    } else if (this.dialogTitleType == DialogTileType.TEXT_TYPE1 && this.dialogTitle) {
      this.dialogTitle.getComponent(RichText)?.destroy();
      if (!this.dialogTitle.getComponent(Label)) {
        this.dialogTitle.addComponent(Label);
      }
      this.dialogTitle.getComponent(Label).string = this.strTitle;
    } else if (this.dialogTitleType == DialogTileType.LABEL_DOUBLE && this.dialogTitle) {
    } else if (this.dialogTitleType == DialogTileType.RICH_TYPE2 && this.dialogTitle) {
      this.dialogTitle.getComponent(Label)?.destroy();
      if (!this.dialogTitle.getComponent(RichText)) {
        this.dialogTitle.addComponent(RichText);
      }
      // 使用封装函数生成富文本（使用类属性strTitle作为输入）
      this.dialogTitle.getComponent(RichText).string = this.formatTitleText1(this.strTitle, 91, 77);
    }
  }
  public setTitle(title: string) {
    this.strTitle = title;
  }
  public setTitleX(code: number, args: any[] = [], defaultMsg: string = "") {
    this.setTitle(LangMgr.txMsgCode(code, args, defaultMsg));
  }
}
