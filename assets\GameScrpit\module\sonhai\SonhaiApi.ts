import { <PERSON>piH<PERSON><PERSON>, ApiHandlerFail, ApiHandlerSuccess } from "../../game/mgr/ApiHandler";
import { ActivityCmd } from "../../game/net/cmd/CmdData";
import {
  AchieveRewardRequest,
  AchieveRewardResponse,
  ActivityDrawRequest,
  ActivityTakeRequest,
  ActivityTakeResponse,
  AdventureChooseRequest,
  AdventureDrawResponse,
  AdventureMessage,
  RedeemRequest,
  RedeemResponse,
} from "../../game/net/protocol/Activity";
import { LongValue } from "../../game/net/protocol/ExternalMessage";
import { SonhaiModule } from "./SonhaiModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class SonhaiApi {
  // 获取山海探险信息
  public adventureInfo(activityId: number, success?: ApiHandlerSuccess) {
    let data: LongValue = {
      value: activityId,
    };
    ApiHandler.instance.request(
      AdventureMessage,
      ActivityCmd.adventureInfo,
      LongValue.encode(data),
      (res: AdventureMessage) => {
        log.log("获取山海探险信息==========", res);
        SonhaiModule.data.adventureMessage = res;
        success && success(res);
      }
    );
  }

  /**选择山海探险本轮选定的大奖  */
  chooseAdventureBigPrizeId(param: AdventureChooseRequest, success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(
      LongValue,
      ActivityCmd.chooseAdventureBigPrizeId,
      AdventureChooseRequest.encode(param),
      (res: LongValue) => {
        log.log("选择山海探险本轮选定的大奖==========", res);
        SonhaiModule.data.curBigPrizeId = res.value;
        // SonhaiModule.data.bigChosenMap = res.bigChosenMap;
        // SonhaiModule.data.curBigPrizeId = res.curBigPrizeId;
        success && success(res);
      }
    );
  }

  /**领取探险活动任务的奖励  */
  takeAdventureDailyTaskReward(param: ActivityTakeRequest, success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(
      ActivityTakeResponse,
      ActivityCmd.takeAdventureDailyTaskReward,
      ActivityTakeRequest.encode(param),
      (res: ActivityTakeResponse) => {
        log.log("领取探险活动任务的奖励==========", res);
        SonhaiModule.data.taskTakeList = res.takeList;
        success && success(res);
      }
    );
  }

  /**山海探险抽奖  */
  adventureDraw(param: ActivityDrawRequest, success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(
      AdventureDrawResponse,
      ActivityCmd.adventureDraw,
      ActivityDrawRequest.encode(param),
      (res: AdventureDrawResponse) => {
        log.log("山海探险抽奖==========", res);
        SonhaiModule.data.bigWinCount = res.bigWinCount;
        SonhaiModule.data.bigChosenMap = res.bigChosenMap;
        SonhaiModule.data.curCostCount = res.curCostCount;

        if (res.curCostCount == 0) {
          SonhaiModule.data.curBigPrizeId = -1;
        }
        success && success(res);
      }
    );
  }

  // 山海成就奖励
  public takeFundReward(req: AchieveRewardRequest, success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(
      AchieveRewardResponse,
      ActivityCmd.takeFundReward,
      AchieveRewardRequest.encode(req),
      (data: AchieveRewardResponse) => {
        SonhaiModule.data.setAchieveMap(data.achieve.id, data.achieve);
        success && success(data);
      }
    );
  }

  /**看广告兑换固定礼包；VIP有跳过广告的权限，也是调用此接口 */
  public watchAdFixedPack(param: any, success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(
      RedeemResponse,
      ActivityCmd.watchAdFixedPack,
      RedeemRequest.encode(param),
      (data: RedeemResponse) => {
        log.log("看广告兑换固定礼包；VIP有跳过广告的权限，也是调用此接口=======", data);
        SonhaiModule.data.redeemMap = data.redeemMap;
        SonhaiModule.data.adMap = data.adMap;
        SonhaiModule.data.chosenMap = data.chosenMap;
        success && success(data);
      }
    );
  }

  /**消耗道具兑换或免费领取固定礼包道具 -----  每日礼包，累计回馈都有使用 */
  public buyFixedPack(param: RedeemRequest, success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(
      RedeemResponse,
      ActivityCmd.buyFixedPack,
      RedeemRequest.encode(param),
      (data: RedeemResponse) => {
        log.log("消耗道具兑换或免费领取固定礼包道具=======", data);
        SonhaiModule.data.redeemMap = data.redeemMap;
        SonhaiModule.data.adMap = data.adMap;
        SonhaiModule.data.chosenMap = data.chosenMap;
        success && success(data);
      }
    );
  }

  // export interface RedeemResponse {
  //   /** 购买情况 key:商品ID */

  //   /** 奖励 */
  //   rewardList: number[];
  // }

  // export interface AdventureDrawResponse {
  //   /** 抽中的道具ID */
  //   drawItemId: number;
  //   rewardList: number[];
  //   /** 本轮选中的大奖的ID 小于等于0表示未选中 */
  //   curBigPrizeId: number;
  //   /** 中大奖的次数 */
  //   bigWinCount: number;
  //   /** 当前轮次已经抽奖的次数   值为0时表示中大奖 */
  //   curCostCount: number;
  // }

  // 接口方法，数据模块在这里改好，然后通知上层，下面这个是个例子
  // public improveSkill(friendId: number, rank: number, consumeitem: boolean, success?: ApiHandlerSuccess) {
  //   let data: FriendCitySkillRequest = {
  //     friendId: friendId,
  //     rank: rank,
  //     consumeItem: consumeitem,
  //   };
  //   log.log("apiImproveSkill", friendId, rank, consumeitem);
  //   ApiHandler.instance.request(
  //     FriendCitySkillMessage,
  //     FriendSubCmd.improveSkill,
  //     FriendCitySkillRequest.encode(data),
  //     (data: FriendCitySkillMessage) => {
  //       log.log(data);
  //       FriendModule.data.updateFriendCitySkill(friendId, rank, data);
  //       success && success(data);
  //     },
  //     (errorCode: any, msg: string, data: any) => {
  //       log.log(`${errorCode}`);
  //       log.log(data);
  //     }
  //   );
  // }
}
