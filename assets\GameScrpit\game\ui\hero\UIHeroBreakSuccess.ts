import { _decorator, EventTouch, Input, Label, Node } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { HeroModule } from "../../../module/hero/HeroModule";
import { IConfigHero, IConfigHeroBreak } from "../../JsonDefine";
import { UIHeroCard } from "./UIHeroCard";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Thu May 23 2024 20:51:45 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/hero/UIHeroBreakSuccess.ts
 *
 */

@ccclass("UIHeroBreakSuccess")
export class UIHeroBreakSuccess extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_HERO}?prefab/ui/UIHeroBreakSuccess`;
  }

  //=================================================
  // dialog
  dialog: Node;
  UIHeroCard: Node;
  levelup: Node; // 执行英雄等级升级操作的按钮节点
  attrDodgeL: Node; // 英雄躲避属性的标签节点
  attrCriticalHitL: Node; // 英雄暴击属性的标签节点
  attrRetaliteL: Node; // 英雄反击属性的标签节点
  attrComboL: Node; // 英雄连击属性的标签节点
  attrStunedL: Node; // 英雄眩晕抵抗属性的标签节点
  attrBloodSucking: Node; // 英雄吸血能力的标签节点

  heroInfo: IConfigHero;
  heroBreak: IConfigHeroBreak;
  heroNextBreak: IConfigHeroBreak;

  public init(args: any): void {
    super.init(args);
    this.heroInfo = args.hero;
    let heroMessage = HeroModule.data.getHeroMessage(this.heroInfo.id);
    this.heroBreak = HeroModule.config.getBreakInfo(heroMessage.breakTopLevel);
    this.heroNextBreak = HeroModule.config.getBreakInfo(heroMessage.breakTopLevel + 1);
  }

  protected onEvtShow(): void {
    super.onEvtShow();
    AudioMgr.instance.playEffect(1472);
    this.findNodeByPath();
    this.onMsg();
    this.refreshUI();
  }

  protected findNodeByPath(): void {
    this.UIHeroCard = this.getNode("UIHeroCard");
    this.levelup = this.getNode("levelup");
    this.attrDodgeL = this.getNode("attrDodgeL");
    this.attrCriticalHitL = this.getNode("attrCriticalHitL");
    this.attrRetaliteL = this.getNode("attrRetaliteL");
    this.attrComboL = this.getNode("attrComboL");
    this.attrStunedL = this.getNode("attrStunedL");
    this.attrBloodSucking = this.getNode("attrBloodSucking");
  }

  protected onMsg(): void {
    this.node.on(Input.EventType.TOUCH_END, this.click_block, this);
  }

  protected offMsg(): void {
    this.node.off(Input.EventType.TOUCH_END, this.click_block, this);
  }

  private refreshUI() {
    // this.lblevel.getComponent(Label).string = `${this.heroInfo.remoteData.level}`;
    // this.lblName.getComponent(Label).string = `${this.heroInfo.name}`;
    let heroMessage = HeroModule.data.getHeroMessage(this.heroInfo.id);
    this.UIHeroCard.getComponent(UIHeroCard).initInfo(this.heroInfo, heroMessage.level, 0);
    let nextBreakLevel = heroMessage.level;
    if (this.heroNextBreak && this.heroNextBreak.levelMax < this.heroInfo.maxLv) {
      nextBreakLevel = this.heroNextBreak.levelMax;
    }
    this.levelup.getComponent(Label).string = `${heroMessage.level}/${nextBreakLevel}`;
    for (let i = 0; i < this.heroBreak?.attrAddList.length; i++) {
      let attr = this.heroBreak.attrAddList[i];
      switch (attr[0]) {
        case 21: //击晕
          this.attrStunedL.getComponent(Label).string = `+${attr[1] / 100}%`;
          attr[1] != 0 && (this.attrStunedL.parent.active = true);
          break;
        case 22: //闪避
          this.attrDodgeL.getComponent(Label).string = `+${attr[1] / 100}%`;
          attr[1] != 0 && (this.attrDodgeL.parent.active = true);
          break;
        case 23: //连击
          this.attrComboL.getComponent(Label).string = `+${attr[1] / 100}%`;
          attr[1] != 0 && (this.attrComboL.parent.active = true);
          break;
        case 24: //反击
          this.attrRetaliteL.getComponent(Label).string = `+${attr[1] / 100}%`;
          attr[1] != 0 && (this.attrRetaliteL.parent.active = true);
          break;
        case 25: //暴击
          this.attrCriticalHitL.getComponent(Label).string = `+${attr[1] / 100}%`;
          attr[1] != 0 && (this.attrCriticalHitL.parent.active = true);
          break;
        case 26: //吸血
          this.attrBloodSucking.getComponent(Label).string = `+${attr[1] / 100}%`;
          attr[1] != 0 && (this.attrBloodSucking.parent.active = true);
          break;
      }
    }
  }

  private click_block(e: EventTouch) {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
}
