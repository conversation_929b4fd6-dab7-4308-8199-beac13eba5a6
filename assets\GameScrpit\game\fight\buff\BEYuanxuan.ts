import { _decorator, Component, instantiate, isValid, Layers, Node, sp, UITransform, v3 } from "cc";
import BEBuff from "./BEBuff";
import GameObject from "../../../lib/object/GameObject";
import { GOBuff } from "./GOBuff";
import ResMgr from "../../../lib/common/ResMgr";
import RenderSection from "../../../lib/object/RenderSection";
import DirectSection, { DIRECT } from "../../../lib/object/DirectSection";
import { JsonMgr } from "../../mgr/JsonMgr";
import { PlayerModule } from "../../../module/player/PlayerModule";
import FightManager, { scaleList } from "../manager/FightManager";
import { TipManager } from "../manager/TipManager";
const { ccclass, property } = _decorator;

@ccclass("BEYuanxuan")
export class BEYuanxuan extends BEBuff {
  private _render: Node;
  public static sectionName(): string {
    return "BEYuanxuan";
  }

  private _buffId: number;
  private _buffPoint: Node;

  public onInit(go: GameObject, args) {
    super.onInit(go, args);
    this._buffId = args.buffId;
  }

  public onStart() {
    this.playPrefab();
  }

  protected playPrefab(): void {
    let role = (this.getSub() as GOBuff).attachment;
    this._buffPoint = role.getSection(RenderSection).getBuffPoint();

    let db = JsonMgr.instance.jsonList.c_buff[this._buffId];
    this.initRender(db.showRes);
    let path = "resources?prefab/effectLab/hint_lab_stun";
    FightManager.instance.getSection(TipManager).callTip(path, role);
  }

  protected onLoadRender(prefab) {
    let node = instantiate(prefab);
    this._render = node.getChildByName("render");
    this._buffPoint.addChild(node);
    // node.layer = Layers.Enum["UILayer"];
    node.layer = node.walk((val) => {
      val.layer = this.getSub().layer;
    });

   // let role = (this.getSub() as GOBuff).attachment;
    node.setPosition(0, 0, 1);

    this._render.getComponent(sp.Skeleton).timeScale = scaleList[FightManager.instance.speed];
    this._render.getComponent(sp.Skeleton).setAnimation(0, "animation", true);

    this.ready();
  }

  public removeBe() {}

  public onRemove() {
    if (isValid(this) == false) {
      return;
    }
    this._render.parent.removeFromParent();
    this._render.parent.destroy();
  }
}
