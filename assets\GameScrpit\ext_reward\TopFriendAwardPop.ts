import { _decorator, Node, sp, UIOpacity, UITransform } from "cc";
import { AudioItem, AudioMgr, AudioName } from "../../platform/src/AudioHelper";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
import { FriendModule } from "../module/friend/FriendModule";
import { instantiate, Sprite, Label } from "cc";
import { BundleEnum } from "../game/bundleEnum/BundleEnum";
import ResMgr from "../lib/common/ResMgr";
import { NodeTool } from "../lib/utils/NodeTool";
import { tween } from "cc";
import { IConfigFriend } from "../game/JsonDefine";
import { JsonMgr } from "../game/mgr/JsonMgr";
import { TipsMgr } from "../../platform/src/TipsHelper";
const { ccclass, property } = _decorator;

@ccclass("TopFriendAwardPop")
export class TopFriendAwardPop extends BaseCtrl {
  protected _bind: boolean = false;

  @property(Node)
  lblFriendName: Node;

  @property(Node)
  bgColor: Node;

  @property(Node)
  friendImg: Node;

  @property(Node)
  nodeFriendGuadian: Node;
  @property(Node)
  nodeFriendGuadian2: Node;
  @property(Node)
  nodeTitle: Node;
  @property(Node)
  nodeAttrLayout: Node;

  private _friendInfo: IConfigFriend;
  private _itemAudio: AudioItem;

  private isAniFinish = false;

  init(args: any) {
    super.init(args);

    TipsMgr.setEnableTouch(false, 1);
    let friendId = args;
    this._friendInfo = FriendModule.config.getFriendById(friendId);
  }

  start() {
    TipsMgr.setEnableTouch(false, 0.2);

    super.start();
    this.refreshUI(this._friendInfo);
    AudioMgr.instance.playEffect(AudioName.Effect.获得奖励);
    this.playVoice();

    tween(this.node)
      .delay(0.5)
      .call(() => {
        this.isAniFinish = true;
      })
      .start();

    // 动画效果
    this.nodeFriendGuadian2.on(Node.EventType.TRANSFORM_CHANGED, this.nodeFriendGuadian2Transform, this);
    this.node
      .getChildByName("node_dialog")
      .getComponent(sp.Skeleton)
      .setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
        if (trackEntry.animation.name == "animation") {
          this.nodeFriendGuadian2.off(Node.EventType.TRANSFORM_CHANGED, this.nodeFriendGuadian2Transform, this);
          tween(this.node.getChildByName("btn_close_huang").getComponent(UIOpacity)).to(0.5, { opacity: 255 }).start();
          this.node.getChildByName("node_dialog").getComponent(sp.Skeleton).setAnimation(0, "animation2", true);
        }
      });
    this.nodeTitle.on(Node.EventType.TRANSFORM_CHANGED, () => {
      let opacity = ((0.69 - (this.nodeTitle.scale.x - 1)) / 0.69) * 255;
      this.nodeTitle.getComponent(UIOpacity).opacity = opacity;
    });
    this.node
      .getChildByName("node_dialog")
      .getComponent(sp.Skeleton)
      .setEventListener((animation, event) => {
        if (event["data"].name == "pinzhichuxian") {
          tween(this.bgColor.getComponent(UIOpacity)).to(0.5, { opacity: 255 }).start();
        }
        if (event["data"].name == "jinengchuxian") {
          for (let i = 0; i < this.nodeAttrLayout.children.length; i++) {
            let child = this.nodeAttrLayout.children[i];
            if (child.active) {
              tween(child.getComponent(UIOpacity))
                .delay(0.1 * i)
                .to(0.5, { opacity: 255 })
                .start();
            }
          }
        }
      });
  }
  private nodeFriendGuadian2Transform() {
    let height = this.nodeFriendGuadian.position.y - this.nodeFriendGuadian2.position.y;
    let contentSize = this.nodeFriendGuadian.getComponent(UITransform).contentSize;
    this.nodeFriendGuadian.getComponent(UITransform).setContentSize(contentSize.width, height);
  }
  private playVoice() {
    if (this._itemAudio) {
      this._itemAudio.release();
    }

    if (this._friendInfo.voice && this._friendInfo.voice.length > 0) {
      this._itemAudio = AudioMgr.instance.playVoice(this._friendInfo.voice[0]);
    }
  }

  private refreshUI(friendInfo: IConfigFriend) {
    this.assetMgr.loadPrefab(BundleEnum.BUNDLE_COMMON_FRIEND, `prefab/friend_${friendInfo.id}`, (prefab) => {
      this.friendImg.destroyAllChildren();
      let item = instantiate(prefab);
      this.friendImg.addChild(item);
      item.walk((child) => {
        child.layer = this.friendImg.layer;
      });
    });

    ResMgr.setSpriteFrame(
      BundleEnum.BUNDLE_COMMON_UI,
      `atlas_imgs/pingzhi_${friendInfo.color}`,
      this.bgColor.getComponent(Sprite)
    );
    this.lblFriendName.getComponent(Label).string = `${friendInfo.name}`;

    // 加载属性信息
    this.nodeAttrLayout.getChildByPath("node_talent/talent_bkg/talent").getComponent(Label).string = `${
      JsonMgr.instance.jsonList.c_friend[friendInfo.id].talentFirst
    }`;
    this.nodeAttrLayout.getChildByPath("node_friendship/friendship_bkg/friendship").getComponent(Label).string = `${
      JsonMgr.instance.jsonList.c_friend[friendInfo.id].friendlyFirst
    }`;
    this.nodeAttrLayout.getChildByName("node_talent").getComponent(UIOpacity).opacity = 0;
    this.nodeAttrLayout.getChildByName("node_friendship").getComponent(UIOpacity).opacity = 0;
  }

  onBtnClose() {
    if (!this.isAniFinish) {
      return;
    }

    this.closeBack();
  }
}
