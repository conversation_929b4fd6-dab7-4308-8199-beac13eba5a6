import GameHttpApi from "../../game/httpNet/GameHttpApi";
import { BadgeMgr, BadgeType } from "../../game/mgr/BadgeMgr";
import TickerMgr from "../../lib/ticker/TickerMgr";
import { FundModule } from "./FundModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class FundService {
  private _tickId: number = null;
  public async init() {
    if (this._tickId) {
      TickerMgr.clearInterval(this._tickId);
    }
    this._tickId = TickerMgr.setInterval(3, this.updatePopover.bind(this), false);
  }

  // 获取所有活动信息
  public async httpGetAllActivity() {
    GameHttpApi.getActivityConfigAll().then((resp: any) => {
      log.log(resp);
    });
  }

  /**
   * 红点更新方法
   */
  private updatePopover() {
    this.FundBanner003cb();
    this.FundBanner004cb();
    this.FundBanner005cb();
    this.FundBanner006cb();
    this.FundBanner007cb();
  }

  //**基金红点 */
  private async FundBanner003cb() {
    const fundId: number = 10403;
    let bool = await this.getFundBool(fundId);
    BadgeMgr.instance.setShowById(BadgeType.UITerritory.btn_chongzhihaoli.FundBanner003.id, bool);
  }

  private async FundBanner004cb() {
    const fundId: number = 10401;
    let bool = await this.getFundBool(fundId);
    BadgeMgr.instance.setShowById(BadgeType.UITerritory.btn_chongzhihaoli.FundBanner004.id, bool);
  }

  private async FundBanner005cb() {
    const fundId: number = 10404;
    let bool = await this.getFundBool(fundId);
    BadgeMgr.instance.setShowById(BadgeType.UITerritory.btn_chongzhihaoli.FundBanner005.id, bool);
  }

  private async FundBanner006cb() {
    const fundId: number = 10402;
    let bool = await this.getFundBool(fundId);
    BadgeMgr.instance.setShowById(BadgeType.UITerritory.btn_chongzhihaoli.FundBanner006.id, bool);
  }

  private async FundBanner007cb() {
    const fundId: number = 10405;
    let bool = await this.getFundBool(fundId);
    BadgeMgr.instance.setShowById(BadgeType.UITerritory.btn_chongzhihaoli.FundBanner007.id, bool);
  }

  public async getFundBool(fundId) {
    let db = await FundModule.data.getFundVO(fundId);
    let id = db.achieveVOList[0].id;
    let data = await FundModule.data.getFundData(id);
    let bool = false;
    if (data == null) {
      bool = false;
    } else {
      let index = data.basicTakeList.length;
      let require = db.achieveVOList[0].requireList[index];

      if (!require) {
        require = -1;
      }

      if (data.targetVal >= require && require != -1) {
        bool = true;
      }

      if (data.paid == true) {
        index = data.paidTakeList.length;
        if (index < data.basicTakeList.length) {
          bool = true;
        }
      }
    }
    return bool;
  }
}
