import { AudioName } from "db://assets/platform/src/AudioHelper";
import { UICityBuild } from "../../game/ui/city/UICityBuild";
import { UICityBuildRes } from "../../game/ui/city/UICityBuildRes";
import { UICityDetail } from "../../game/ui/city/UICityDetail";
import { UICityFight } from "../../game/ui/city/UICityFight";
import { UICityHeroInfo } from "../../game/ui/city/UICityHeroInfo";
import { UICityReel } from "../../game/ui/city/UICityReel";
import { UICityReward } from "../../game/ui/city/UICityReward";
import { UIEnergyUpgrade } from "../../game/ui/city/UIEnergyUpgrade";
import { UIHaoZhaoBox } from "../../game/ui/city/UIHaoZhaoBox";
import { UIWorldPreview } from "../../game/ui/city/UIWorldPreview";
import { UIWorldPreviewAward } from "../../game/ui/city/UIWorldPreviewAward";
import { UIWorldPreviewCity } from "../../game/ui/city/UIWorldPreviewCity";
import { UIWorldPreviewDetail } from "../../game/ui/city/UIWorldPreviewDetail";
import { UIWorldPreviewTrim } from "../../game/ui/city/UIWorldPreviewTrim";
import { UIGameMap } from "../../game/ui/ui_gameMap/UIGameMap";
import { UISanJieXiaoJia } from "../../game/ui/ui_san_jie_xiao_jia/UISanJieXiaoJia";
import { UISanJieXiaoJiaCj } from "../../game/ui/ui_san_jie_xiao_jia/UISanJieXiaoJiaCj";
import { UISanJieXiaoJiaHero } from "../../game/ui/ui_san_jie_xiao_jia/UISanJieXiaoJiaHero";
import { UISanJieXiaoJiaLvUp } from "../../game/ui/ui_san_jie_xiao_jia/UISanJieXiaoJiaLvUp";
import { UISanJieXiaoJiaOpen } from "../../game/ui/ui_san_jie_xiao_jia/UISanJieXiaoJiaOpen";
import { Recording, RecordingMap } from "../../lib/ui/recordingMap";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { BundleEnum } from "../../game/bundleEnum/BundleEnum";
import { CityRouteName } from "./CityConstant";

export class CityRoute {
  rotueTables: Recording[] = [
    {
      node: UIGameMap,
      uiName: CityRouteName.UIGameMap,
      keep: true,
      relevanceUIList: [],
      music: AudioName.Sound.府邸和三界通用,
    },
    {
      node: UICityFight,
      uiName: CityRouteName.UICityFight,
      keep: false,
      relevanceUIList: [],
      music: AudioName.Sound.战斗,
    },
    {
      node: UICityBuild,
      uiName: CityRouteName.UICityBuild,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UICityBuildRes,
      uiName: CityRouteName.UICityBuildRes,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UICityDetail,
      uiName: CityRouteName.UICityDetail,
      keep: false,
      relevanceUIList: [UIGameMap],
    },
    {
      node: UICityReel,
      uiName: CityRouteName.UICityReel,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UICityReward,
      uiName: CityRouteName.UICityLvReward,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIEnergyUpgrade,
      uiName: CityRouteName.UIEnergyUpgrade,
      keep: false,
      relevanceUIList: [UIGameMap],
    },
    {
      node: UIWorldPreview,
      uiName: CityRouteName.UIWorldPreview,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIWorldPreviewDetail,
      uiName: CityRouteName.UIWorldPreviewDetail,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UICityHeroInfo,
      uiName: CityRouteName.UICityHeroInfo,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIWorldPreviewCity,
      uiName: CityRouteName.UIWorldPreviewCity,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIWorldPreviewTrim,
      uiName: CityRouteName.UIWorldPreviewTrim,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIWorldPreviewAward,
      uiName: CityRouteName.UIWorldPreviewAward,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UISanJieXiaoJia,
      uiName: CityRouteName.UISanJieXiaoJia,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UISanJieXiaoJiaOpen,
      uiName: CityRouteName.UISanJieXiaoJiaOpen,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UISanJieXiaoJiaLvUp,
      uiName: CityRouteName.UISanJieXiaoJiaLvUp,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UISanJieXiaoJiaCj,
      uiName: CityRouteName.UISanJieXiaoJiaCj,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UISanJieXiaoJiaHero,
      uiName: CityRouteName.UISanJieXiaoJiaHero,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIHaoZhaoBox,
      uiName: CityRouteName.UIHaoZhaoBox,
      keep: false,
      relevanceUIList: [],
    },
  ];
  public async init() {
    this.rotueTables.forEach((item) => {
      RecordingMap.instance.addRecording(item.uiName, item);
    });

    // 注册弹窗
    TipsMgr.topRouteCtrl.regRoute(CityRouteName.TopCityBuild, {
      bundle: BundleEnum.BUNDLE_G_GAME_MAP,
      url: "prefab/top/TopCityBuild",
      nextHop: [],
      exit: "",
    });
  }
}
