import { find, Node } from "cc";
import SimpleTip from "./SimpleTip";
import MgrNode from "./MrgNode";
import { tween } from "cc";
import { v3 } from "cc";
import { UITransform } from "cc";
import TickerMgr from "../ticker/TickerMgr";
import { StartUp } from "../StartUp";
import { BoutStartUp } from "../../game/BoutStartUp";
import { TipsMgr } from "../../../platform/src/TipsHelper";
import { LayerEnum } from "../../game/GameDefine";

export default class TipMgr extends Node {
  private static _prev: string;
  private static _time: number = 0;

  private static _nodeTipList: Node[] = [];
  private static _showTimeList: number[] = [];
  private static _nodeShowEndTime: number = 0;

  private static _tickId: number = -1;

  public static showTip(content: string) {
    TipsMgr.showTip(content);

    // var t: number = new Date().getTime();
    // if (this._prev == content && t < this._time) return;

    // this._prev = content;
    // var tip: SimpleTip = SimpleTip.create(content, this._time - t);
    // BoutStartUp.instance.tipRoot.addChild(tip);
    // tip.setSiblingIndex(BoutStartUp.instance.tipRoot.children.length);
    // this._time = t + 2000;
  }

  public static showAlert() {}

  public static errTip(err: number) {}

  // 展示提示节点
  public static fromTop(node: Node, showTime: number) {
    node.walk((child) => (child.layer = LayerEnum.TOP));
    this._nodeTipList.push(node);
    this._showTimeList.push(showTime);
    if (this._tickId == -1) {
      this._tickId = TickerMgr.setInterval(0.5, this.playNodeTipAni.bind(this), true);
    }
  }

  private static playNodeTipAni() {
    if (this._nodeShowEndTime > new Date().getTime()) {
      return;
    } else if (this._nodeTipList.length == 0) {
      // 没有要显示的，清空当前状态
      TickerMgr.clearInterval(this._tickId);
      this._tickId = -1;
      return;
    }

    // 要展示的结点
    let node = this._nodeTipList.shift();
    // 显示时间
    let showTime = this._showTimeList.shift();
    // 显示结束时间
    this._nodeShowEndTime = showTime + 1 + showTime;

    TipsMgr.getTipsRoot().addChild(node);

    // 上面进入页面动画过程
    const size = StartUp.instance.getVisibleSize();

    // 获取节点在顶部屏幕外部
    const uiTransform = node.getComponent(UITransform);
    const startPos = v3(0, size.y / 2 + uiTransform.contentSize.y * uiTransform.anchorY + 100);

    node.position = startPos;

    // 滑入位置
    const targetPos = v3(0, size.y / 2 - uiTransform.contentSize.y * (1 - uiTransform.anchorY) - 100);

    // 滑入动作
    tween(node)
      .to(0.3, { position: targetPos }, { easing: "backOut" })
      .delay(showTime)
      .to(0.2, { position: startPos }, { easing: "cubicIn" })
      .removeSelf()
      .start();
  }
}
