const fs = require('fs'); // 引入文件系统模块

// 定义要替换的目标字符串和新字符串
const targetString = 'import _m0 from "protobufjs/minimal";';
const newString = 'import _m0 from "protobufjs/minimal.js";';

// 获取指定目录下所有文件名
function getAllFiles(dir) {
  const files = [];

  function traverseDir(path) {
    const dirents = fs.readdirSync(path);

    for (let i = 0; i < dirents.length; i++) {
      const dirent = dirents[i];

      if (fs.statSync(`${path}/${dirent}`).isDirectory()) {
        traverseDir(`${path}/${dirent}`);
      } else {
        files.push(`${path}/${dirent}`);
      }
    }
  }

  traverseDir(dir);

  return files;
}

// 遍历指定目录下的每个文件并进行替换操作
function replaceStringsInFiles(files) {
  for (let file of files) {
    let content = fs.readFileSync(file, 'utf8');

    while (content.includes(targetString)) {
      content = content.replace(targetString, newString);
    }

    fs.writeFileSync(file, content, 'utf8');
  }
}

// 调用函数来处理指定目录下的文件
const directoryPath = './assets//GameScrpit/game/net/protocol/'; // 将路径更改为需要处理的目录路径
const allFiles = getAllFiles(directoryPath);
replaceStringsInFiles(allFiles);
console.log("已完成批量替换！");