import { IConfigVipReward } from "../../../game/JsonDefine";
import { JsonMgr } from "../../../game/mgr/JsonMgr";
export class VipConfig {
  public getVipRewardList(): IConfigVipReward[] {
    let vipRewardList: IConfigVipReward[] = [];
    Object.values(JsonMgr.instance.jsonList.c_vipReward).forEach((val: IConfigVipReward) => {
      vipRewardList.push(val);
    });
    return vipRewardList;
  }
}

export const VipAudioName = {
  Effect: {
    点击贵族豪礼图标: 1361,
    点击领取按钮: 1362,
  },
  Sound: {},
};
