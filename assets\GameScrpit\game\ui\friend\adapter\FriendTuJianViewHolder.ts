import { _decorator, EventTouch, Label, math, Node, Sprite } from "cc";
import { ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { UIFriendMainTuJian } from "../UIFriendMainTuJian";
import { ItemCtrl } from "../../../common/ItemCtrl";
import { FriendModule } from "../../../../module/friend/FriendModule";
import { BadgeMgr, BadgeType } from "../../../mgr/BadgeMgr";
import { IConfigFriendPicture } from "../../../JsonDefine";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
import { FriendAudioName } from "db://assets/GameScrpit/module/friend/FriendConfig";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;
@ccclass("FriendTuJianViewHolder")
export class FriendTuJianViewHolder extends ViewHolder {
  @property(Label)
  private lblTitle: Label; //标题 未完成 外#9e0e0e 内#ff8181 完成 外#105319 内#87f59f
  @property(Node)
  private nodeFriends: Node;
  // @property(Node)
  // private lblTips: Node;
  @property(Node)
  private btnAward: Node;

  private _data: IConfigFriendPicture;
  private _context: UIFriendMainTuJian;
  updateData(data: IConfigFriendPicture, context: UIFriendMainTuJian, position: number) {
    this.position = position;
    this._data = data;
    this._context = context;
    this.lblTitle.string = `【${data.name}】`;
    let index = 0;
    let collectNum = 0;
    log.log("data.friendId", data);
    BadgeMgr.instance.setBadgeId(
      this.btnAward,
      BadgeType.UITerritory.btn_friend.tab_tujian["friendPicture" + data.id].id
    );

    for (; index < this.nodeFriends.children.length && index < data.friendId.length; index++) {
      let node = this.nodeFriends.children[index];
      node.active = true;
      node.getComponent(ItemCtrl).setItemId(data.friendId[index]);
      let friendMessage = FriendModule.data.getFriendMessage(data.friendId[index]);
      if (friendMessage) {
        collectNum++;
        node.getComponent(ItemCtrl).itemName.enableOutline = true;
        node.getComponentsInChildren(Sprite).forEach((item) => {
          item.color = math.color("#FFFFFF");
        });
      } else {
        node.getComponent(ItemCtrl).itemName.color = math.color("#838D98");
        node.getComponent(ItemCtrl).itemName.enableOutline = false;
        node.getComponentsInChildren(Sprite).forEach((item) => {
          item.color = math.color("#6E6E6E");
        });
      }
      log.log("friendId", data.friendId[index]);
    }
    for (; index < this.nodeFriends.children.length; index++) {
      this.nodeFriends.children[index].active = false;
    }
    this.btnAward.getChildByName("node_disable").active = false;
    this.btnAward.getChildByName("node_enable").active = false;
    this.btnAward.getChildByName("node_open").active = false;
    if (collectNum < data.friendId.length) {
      this.lblTitle.color = math.color("#a5899d");
      this.btnAward.getChildByName("node_disable").active = true;
    } else {
      this.lblTitle.color = math.color("#cb527d");
      if (FriendModule.data.pictureMessage[data.id]) {
        this.btnAward.getChildByName("node_open").active = true;
      } else {
        this.btnAward.getChildByName("node_enable").active = true;
      }
    }
  }
  private onClickDetail(e: EventTouch) {
    AudioMgr.instance.playEffect(FriendAudioName.Effect.点击图鉴宝箱);
    this._context.showDetail(this._data);
  }
}
