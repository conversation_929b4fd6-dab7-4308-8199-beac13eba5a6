import { GameData } from "../../game/GameData";
import data from "../../lib/data/data";
import { ActivityID } from "../activity/ActivityConstant";
import { ActivityModule } from "../activity/ActivityModule";
import { HdSevenApi } from "./HdSevenApi";
import { HdSevenConfig } from "./HdSevenConfig";
import { HdSevenData } from "./HdSevenData";
import { HdSevenRoute } from "./HdSevenRoute";
import { HdSevenService } from "./HdSevenService";
import { HdSevenSubscriber } from "./HdSevenSubscriber";
import { HdSevenViewModel } from "./HdSevenViewModel";

export class HdSevenModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): HdSevenModule {
    if (!GameData.instance.HdSevenModule) {
      GameData.instance.HdSevenModule = new HdSevenModule();
    }
    return GameData.instance.HdSevenModule;
  }
  private _data = new HdSevenData();
  private _api = new HdSevenApi();
  private _service = new HdSevenService();
  private _subscriber = new HdSevenSubscriber();
  private _route = new HdSevenRoute();
  private _viewModel = new HdSevenViewModel();
  private _config = new HdSevenConfig();

  public static get data() {
    return this.instance._data;
  }

  public static get api() {
    return this.instance._api;
  }

  public static get config() {
    return this.instance._config;
  }

  public static get service() {
    return this.instance._service;
  }

  public static get viewModel() {
    return this.instance._viewModel;
  }

  public init(data?: any) {
    if (this._subscriber) {
      this._subscriber.unRegister();
    }
    this._data = new HdSevenData();
    this._api = new HdSevenApi();
    this._service = new HdSevenService();
    this._subscriber = new HdSevenSubscriber();
    this._route = new HdSevenRoute();
    this._viewModel = new HdSevenViewModel();
    this._config = new HdSevenConfig();

    this._config.activityConfig = ActivityModule.data.allActivityConfig[ActivityID.SEVEN_DAY] as any;
    this._service.canShowSeven(() => {
      this._api.sevenDaySign(ActivityID.SEVEN_DAY);
    });

    // 模块初始化
    this._subscriber.register();
    this._route.init();
  }

  protected saveKey(): string {
    return this.constructor.name;
  }
}
