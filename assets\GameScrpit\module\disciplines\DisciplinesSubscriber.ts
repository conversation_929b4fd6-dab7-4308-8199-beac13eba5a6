import MsgEnum from "../../game/event/MsgEnum";
import { ApiHandler } from "../../game/mgr/ApiHandler";
import { ActivityCmd } from "../../game/net/cmd/CmdData";
import {
  AchievePayResponse,
  AchieveTargetValResponse,
  DayTaskResponse,
  LeaderFundRechargeResponse,
  RedeemBuyMessage,
  RedeemLimitUpdateMessage,
} from "../../game/net/protocol/Activity";
import { CommLongListMessage } from "../../game/net/protocol/Comm";
import { LongValue } from "../../game/net/protocol/ExternalMessage";
import MsgMgr from "../../lib/event/MsgMgr";
import { DayActivityModule } from "../day/DayActivityModule";
import { DisciplinesModule } from "./DisciplinesModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class DisciplinesSubscriber {
  private fundMessageCallback(rs: AchievePayResponse) {
    //log.log("基金购买成功后进行推送", rs);
    let list = [11001];
    let is = list.indexOf(rs.activityId) > -1;
    if (is == false) {
      return;
    }
    /**修改用户支付状态 */
    let data = DisciplinesModule.data.getAchieveData(rs.achieveId);
    if (data) {
      data.paid = true;
    }
    MsgMgr.emit(MsgEnum.ON_ACTIVITY_FUND_BUT_UP, rs);
  }

  private dayRechargeCallback(rs: LongValue) {
    //log.log("当修行基金签到活动充值后进行推送", rs);
    let data = DisciplinesModule.data.daySign;
    if (data) {
      data.paid = true;
    }
    MsgMgr.emit(MsgEnum.ON_ACTIVITY_FUND_RECHARGE, rs);
  }

  private xiuXingFundCallback(rs: DayTaskResponse) {
    //log.log("当修行基金的日常任务完成情况变更时推送", rs);
    DisciplinesModule.data.getDayTask().targetValList = rs.targetValList;
    MsgMgr.emit(MsgEnum.ON_ACTIVITY_FUND_DAILY_TASK_UPDATE, rs);
  }

  private fundTargetUp(rs: AchieveTargetValResponse) {
    //log.log("任务指标数值变化", rs);
    let list = [11001];
    let is = list.indexOf(rs.activityId) > -1;
    if (is == false) {
      return;
    }
    let data = DisciplinesModule.data.getAchieveData(rs.achieveId);
    if (data) {
      data.targetVal = rs.targetVal;
    }
    MsgMgr.emit(MsgEnum.ON_ACTIVITY_FUND_BUT_UP, rs);
  }

  private redeemBuyCallback(rs: RedeemBuyMessage) {
    let list = [11001];
    let is = list.indexOf(rs.activityId) > -1;
    if (is == false) {
      return;
    }
    //log.log("修行礼包购买成功", rs);
    DisciplinesModule.data.redeemMap = rs.redeemMap;
    DisciplinesModule.data.setChoseMap(rs.chosenMap);
    MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: rs.rewardList });
    MsgMgr.emit(MsgEnum.ON_ACTIVITY_FUND_GIFT_UPDATE, this);
  }

  private xiuXingCiShuBianHuaCallback(rs: LeaderFundRechargeResponse) {
    let list = [11001];
    let is = list.indexOf(rs.activityId) > -1;
    if (is == false) {
      return;
    }
    //log.log("当修行基金的累计付费次数变化时推送", rs);
    DisciplinesModule.data.numerator = rs.numerator;
    MsgMgr.emit(MsgEnum.ON_ACTIVITY_FUND_GIFT_UPDATE, this);
  }

  private upActivityDb(rs: CommLongListMessage) {
    for (let i = 0; i < rs.longList.length; i++) {
      if (rs.longList.includes(11001)) {
        DisciplinesModule.data.upVO();
        return;
      }
    }
  }
  private RedeemLimitUpdate(rs: RedeemLimitUpdateMessage) {
    let list = [11001];
    let is = list.indexOf(rs.activityId) > -1;
    if (is == false) {
      return;
    }

    /** achieveId*/
    DayActivityModule.data.limitMap = rs.limitMap;
  }
  public register() {
    //订阅服务器消息
    //订阅服务器消息
    ApiHandler.instance.subscribe(AchievePayResponse, ActivityCmd.fundMessage, this.fundMessageCallback);

    ApiHandler.instance.subscribe(LongValue, ActivityCmd.DayRechargeCash, this.dayRechargeCallback);

    ApiHandler.instance.subscribe(DayTaskResponse, ActivityCmd.XiuXingFundTask, this.xiuXingFundCallback);

    ApiHandler.instance.subscribe(AchieveTargetValResponse, ActivityCmd.FundTargetUp, this.fundTargetUp);

    //订阅服务器消息
    ApiHandler.instance.subscribe(RedeemBuyMessage, ActivityCmd.RedeemBuyMessage, this.redeemBuyCallback);

    //订阅服务器消息
    ApiHandler.instance.subscribe(
      LeaderFundRechargeResponse,
      ActivityCmd.xiuXingCiShuBianHua,
      this.xiuXingCiShuBianHuaCallback
    );
    ApiHandler.instance.subscribe(RedeemLimitUpdateMessage, ActivityCmd.RedeemLimitUpdate, this.RedeemLimitUpdate);
    ApiHandler.instance.subscribe(CommLongListMessage, ActivityCmd.ActivityUp, this.upActivityDb);
  }
  public unRegister() {
    ApiHandler.instance.unSubscribe(ActivityCmd.RedeemLimitUpdate, this.RedeemLimitUpdate);
    //取消订阅服务器消息
    ApiHandler.instance.unSubscribe(ActivityCmd.fundMessage, this.fundMessageCallback);
    ApiHandler.instance.unSubscribe(ActivityCmd.DayRechargeCash, this.dayRechargeCallback);
    ApiHandler.instance.unSubscribe(ActivityCmd.XiuXingFundTask, this.xiuXingFundCallback);
    ApiHandler.instance.unSubscribe(ActivityCmd.FundTargetUp, this.fundTargetUp);
    ApiHandler.instance.unSubscribe(ActivityCmd.RedeemBuyMessage, this.redeemBuyCallback);
    ApiHandler.instance.unSubscribe(ActivityCmd.xiuXingCiShuBianHua, this.xiuXingCiShuBianHuaCallback);
    ApiHandler.instance.unSubscribe(ActivityCmd.ActivityUp, this.upActivityDb);
  }
}
