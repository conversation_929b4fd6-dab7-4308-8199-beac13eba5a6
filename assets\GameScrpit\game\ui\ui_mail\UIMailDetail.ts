import { _decorator } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { Label } from "cc";
import { MailModule } from "../../../module/mail/MailModule";
import { MailMessage, ReadResponse } from "../../net/protocol/Mail";
import { TimeUtils } from "../../../lib/utils/TimeUtils";
import { RichText } from "cc";
import FmUtils from "../../../lib/utils/FmUtils";
import { instantiate } from "cc";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { PublicRouteName } from "../../../module/player/PlayerConstant";
import { ConfirmMsg } from "../UICostConfirm";
import { TipsMgr } from "../../../../platform/src/TipsHelper";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
import { MailAudioName } from "../../../module/mail/MailConfig";
const { ccclass, property } = _decorator;

/**
 *
 * hopewsw
 * Sun Jul 28 2024 16:04:35 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/ui_mail/UIMailDetail.ts
 *
 */
@ccclass("UIMailDetail")
export class UIMailDetail extends UINode {
  protected _openAct: boolean = true;
  mail: MailMessage;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_MAIL}?prefab/ui/UIMailDetail`;
  }

  protected onRegEvent(): void {
    MsgMgr.on(MsgEnum.ON_MAIL_UPDATE, this.refresh, this);
  }

  protected onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_MAIL_UPDATE, this.refresh, this);
  }

  //=================================================
  public init(args: any): void {
    super.init(args);
    this.mail = args["mail"];
  }

  protected onEvtShow(): void {
    super.onEvtShow();

    this.refresh();

    // 发送已读信息
    if (!this.mail.read && !this.mail.rewardList.length) {
      this.on_click_btn_read();
    }
  }

  public refresh() {
    this.getNode("lbl_title").getComponent(Label).string = this.mail.title;
    this.getNode("lbl_from").getComponent(Label).string = `发件人:${
      this.mail.sender
    }\n发件时间:${TimeUtils.formatTimestamp(this.mail.sendTs, "YYYY/MM/DD")}`;
    this.getNode("rich_text_body").getComponent(RichText).string = this.mail.content;

    this.getNode("btn_read").active = !this.mail.read && this.mail.rewardList.length > 0;
    this.getNode("btn_delete").active = this.mail.read;

    // 道具展示
    const nodeScrollView = this.getNode("scroll_view");
    nodeScrollView.active = this.mail.rewardList.length > 0;
    nodeScrollView.getChildByPath("view/content").removeAllChildren();
    if (this.mail.rewardList.length > 0) {
      for (let i = 0; i < this.mail.rewardList.length; i += 2) {
        let nodeItem = instantiate(this.getNode("Item"));
        nodeScrollView.getChildByPath("view/content").addChild(nodeItem);
        FmUtils.setItemNode(nodeItem, this.mail.rewardList[i], this.mail.rewardList[i + 1]);
      }
    }
  }

  public on_click_btn_read() {
    AudioMgr.instance.playEffect(MailAudioName.Effect.点击领取按钮);
    this.getNode("btn_read").active = false;
    this.getNode("btn_delete").active = true;
    MailModule.api.readMail(this.mail.id, (resp: ReadResponse) => {
      if (!resp.idList.length) {
        TipsMgr.showTip("没有可领取的邮件");
      }

      if (resp.rewardList.length) {
        MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: resp.rewardList });
      }
    });
  }

  public on_click_btn_delete() {
    AudioMgr.instance.playEffect(MailAudioName.Effect.点击删除按钮);

    let msg: ConfirmMsg = {
      msg: "确认删除邮件？",
      itemList: [],
    };

    UIMgr.instance.showDialog(PublicRouteName.UICostConfirm, msg, (resp) => {
      if (resp?.ok) {
        MailModule.api.deleteRead(this.mail.id, () => {
          TipsMgr.showTip("删除成功");
          UIMgr.instance.back();
        });
      }
    });
  }
}
