import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { <PERSON>pi<PERSON><PERSON><PERSON>, ApiHandlerSuccess } from "../../game/mgr/ApiHandler";
import { ActivityCmd } from "../../game/net/cmd/CmdData";
import {
  AchieveRewardRequest,
  AchieveRewardResponse,
  ActivityRechargeResponse,
  ActivityTakeRequest,
  ActivityTakeResponse,
  LeaderFundMessage,
  LeaderSignResponse,
  RedeemChosenRequest,
  RedeemRequest,
  RedeemResponse,
} from "../../game/net/protocol/Activity";
import { CommIntegerListMessage } from "../../game/net/protocol/Comm";
import { LongValue } from "../../game/net/protocol/ExternalMessage";
import { DisciplinesModule } from "./DisciplinesModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class DisciplinesApi {
  ///获取修行基金的情况
  leaderFundInfo(activityId: number, success?) {
    let data: LongValue = {
      value: activityId,
    };
    ApiHandler.instance.request(
      LeaderFundMessage,
      ActivityCmd.leaderFundInfo,
      LongValue.encode(data),
      (res: LeaderFundMessage) => {
        log.log("获取修行基金的情况==========", res);
        DisciplinesModule.data.leaderFundInfo = res;
        success && success(res);
      },
      (errorCode: any, msg: string[], data: any) => {
        TipsMgr.showErrX(errorCode, msg);
        return true;
      }
    );
  }

  // 领取基金进度奖励
  public takeFundReward(req: AchieveRewardRequest, success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(
      AchieveRewardResponse,
      ActivityCmd.takeFundReward,
      AchieveRewardRequest.encode(req),
      (data: AchieveRewardResponse) => {
        DisciplinesModule.data.setAchieveData(data.achieve.id, data.achieve);
        success && success(data);
      },
      (errorCode: any, msg: string[], data: any) => {
        TipsMgr.showErrX(errorCode, msg);
        return true;
      }
    );
  }

  /**领取修行基金签到奖励 */
  public takeLeaderSignPaidReward(req: ActivityTakeRequest, success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(
      LeaderSignResponse,
      ActivityCmd.takeLeaderSignPaidReward,
      ActivityTakeRequest.encode(req),
      (data: LeaderSignResponse) => {
        DisciplinesModule.data.setDaySign(data);
        success && success(data);
      },
      (errorCode: any, msg: string[], data: any) => {
        TipsMgr.showErrX(errorCode, msg);
        return true;
      }
    );
  }

  /**领取修行基金每日任务奖励 */
  public takeDayTaskReward(req: ActivityTakeRequest, success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(
      ActivityTakeResponse,
      ActivityCmd.takeDayTaskReward,
      ActivityTakeRequest.encode(req),
      (data: ActivityTakeResponse) => {
        log.log("领取修行基金每日任务奖励============", data);
        DisciplinesModule.data.getDayTask().takeList = data.takeList;
        success && success(data);
      },
      (errorCode: any, msg: string[], data: any) => {
        TipsMgr.showErrX(errorCode, msg);
        return true;
      }
    );
  }

  /**消耗道具兑换或免费领取固定礼包道具 -----  每日礼包，累计回馈都有使用 */
  public buyFixedPack(param: RedeemRequest, success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(
      RedeemResponse,
      ActivityCmd.buyFixedPack,
      RedeemRequest.encode(param),
      (data: RedeemResponse) => {
        log.log("消耗道具兑换或免费领取固定礼包道具=======", data);
        DisciplinesModule.data.leaderFundInfo.redeemMap = data.redeemMap;
        DisciplinesModule.data.leaderFundInfo.adMap = data.adMap;
        success && success(data);
      },
      (errorCode: any, msg: string[], data: any) => {
        TipsMgr.showErrX(errorCode, msg);
        return true;
      }
    );
  }

  /**.额外自选一个礼包兑换 */
  public chosenRedeem(param: RedeemChosenRequest, success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(
      RedeemResponse,
      ActivityCmd.chosenRedeem,
      RedeemChosenRequest.encode(param),
      (data: RedeemResponse) => {
        log.log("自选礼包道具成功=======", data);
        success && success(data);
      },
      (errorCode: any, msg: string[], data: any) => {
        TipsMgr.showErrX(errorCode, msg);
        return true;
      }
    );
  }

  /**记录自选情况 */
  public recordRedeemChosen(param: RedeemChosenRequest, success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(
      CommIntegerListMessage,
      ActivityCmd.recordRedeemChosen,
      RedeemChosenRequest.encode(param),
      (data: CommIntegerListMessage) => {
        log.log("自选礼包道具成功=======", data);
        DisciplinesModule.data.setChosen(param.redeemId, data);
        success && success(data);
      },
      (errorCode: any, msg: string[], data: any) => {
        TipsMgr.showErrX(errorCode, msg);
        return true;
      }
    );
  }

  ///领取充值任务奖励
  takeLeaderRechargeReward(value: number, success?) {
    let data: LongValue = {
      value: value,
    };
    ApiHandler.instance.request(
      ActivityRechargeResponse,
      ActivityCmd.takeLeaderRechargeReward,
      LongValue.encode(data),
      (res: ActivityRechargeResponse) => {
        DisciplinesModule.data.leaderRecharge = res.leaderRecharge;
        success && success(res);
      },
      (errorCode: any, msg: string[], data: any) => {
        TipsMgr.showErrX(errorCode, msg);
        return true;
      }
    );
  }
}
// export interface LeaderSignResponse {
//   sign: boolean;
//   /** 领取基础签到奖励 0-6 */
//   basicList: number[];
//   /** 领取付费签到奖励 0-6 */
//   paidList: number[];
//   rewardList: number[];
// }
