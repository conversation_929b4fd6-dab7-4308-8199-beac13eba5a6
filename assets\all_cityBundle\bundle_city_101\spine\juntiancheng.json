{"skeleton": {"hash": "dfLHyTSM1R4ZtLU4yzAiLWUlW5A=", "spine": "3.8.75", "x": -145.51, "y": -4.11, "width": 293.22, "height": 235.11, "images": "./images/", "audio": "D:/spine导出/新据点升级动画/君天城"}, "bones": [{"name": "root", "scaleX": 0.28, "scaleY": 0.28}, {"name": "lv1", "parent": "root", "y": -79.46, "scaleX": 1.12, "scaleY": 1.14, "color": "ff0000ff"}, {"name": "lv2", "parent": "root", "y": -79.46, "scaleX": 1.1, "scaleY": 1.1, "color": "fff200ff"}, {"name": "lv3", "parent": "root", "y": -79.46, "scaleX": 1.08, "scaleY": 1.08, "color": "003dffff"}, {"name": "lv4", "parent": "root", "y": -79.46, "scaleX": 1.12, "scaleY": 1.12, "color": "00ff00ff"}, {"name": "lv5", "parent": "root", "y": -79.46, "scaleX": 1.12, "scaleY": 1.12, "color": "ea06ffff"}, {"name": "lv6", "parent": "root", "y": -79.46, "scaleX": 1.12, "scaleY": 1.12, "color": "ffa100ff"}, {"name": "1j1", "parent": "lv1", "y": 79.46, "color": "ff0000ff"}, {"name": "1j2", "parent": "lv1", "x": -239.84, "y": 129.76, "color": "ff0000ff"}, {"name": "1j3", "parent": "lv1", "x": 234.74, "y": 132.68, "color": "ff0000ff"}, {"name": "1j4", "parent": "lv1", "y": 124.66, "color": "ff0000ff"}, {"name": "1j5", "parent": "lv1", "y": 290.14, "color": "ff0000ff"}, {"name": "lv7", "parent": "lv2", "x": -0.99, "y": 83.99, "color": "fff200ff"}, {"name": "lv8", "parent": "lv2", "x": -0.8, "y": 334.22, "color": "fff200ff"}, {"name": "lv9", "parent": "lv3", "x": -0.4, "y": 56.13, "color": "003dffff"}, {"name": "lv10", "parent": "lv3", "x": -1.1, "y": 156.24, "color": "003dffff"}, {"name": "lv11", "parent": "lv4", "x": -0.26, "y": 60.03, "color": "00ff00ff"}, {"name": "lv12", "parent": "lv4", "x": -0.91, "y": 83.43, "color": "00ff00ff"}, {"name": "lv13", "parent": "lv4", "x": 311.09, "y": 128.93, "color": "00ff00ff"}, {"name": "lv14", "parent": "lv4", "x": -310.96, "y": 128.93, "color": "00ff00ff"}, {"name": "lv15", "parent": "lv4", "x": -0.91, "y": 368.78, "color": "00ff00ff"}, {"name": "lv16", "parent": "lv5", "x": -0.34, "y": 151.96, "color": "ea06ffff"}, {"name": "lv17", "parent": "lv5", "x": -0.79, "y": 112.52, "color": "ea06ffff"}, {"name": "lv18", "parent": "lv5", "x": -1.24, "y": 321.93, "color": "ea06ffff"}, {"name": "lv19", "parent": "lv5", "x": -2.08, "y": 403.39, "color": "ea06ffff"}, {"name": "lv20", "parent": "lv6", "x": -0.19, "y": 510.09, "color": "ffa100ff"}, {"name": "lv21", "parent": "lv6", "x": 395.5, "y": 57.95, "color": "ffa100ff"}, {"name": "lv22", "parent": "lv6", "x": -401.01, "y": 57.95, "color": "ffa100ff"}, {"name": "bone", "parent": "root", "x": 294.43, "y": 761.6, "scaleX": 1.4, "scaleY": 1.4}, {"name": "bone2", "parent": "root", "x": 292.44, "y": 513.57, "scaleX": 0.36, "scaleY": 0.32}, {"name": "bone10", "parent": "bone2", "length": 60.26, "rotation": 1.57, "x": 35.97, "y": -65.89}, {"name": "图层 38", "parent": "bone10", "length": 56.14, "rotation": 120.95, "x": -12.13, "y": 32.84}, {"name": "图层 44", "parent": "图层 38", "length": 23.36, "rotation": -62.48, "x": 49.25, "y": -64.76}, {"name": "图层 45", "parent": "图层 44", "length": 23.36, "x": 68.4}, {"name": "图层 46", "parent": "图层 38", "length": 19.17, "rotation": -42.83, "x": 132.03, "y": -24.62}, {"name": "图层 47", "parent": "图层 46", "length": 19.17, "x": 56.15}, {"name": "bone3", "parent": "root", "x": 19.72, "y": -129.08, "scaleX": 1.4, "scaleY": 1.4}, {"name": "bone4", "parent": "root", "x": 292.44, "y": 513.57, "scaleX": 0.36, "scaleY": 0.32}, {"name": "bone11", "parent": "bone4", "length": 60.26, "rotation": 1.57, "x": 35.97, "y": -65.89}, {"name": "图层 39", "parent": "bone11", "length": 56.14, "rotation": 120.95, "x": -12.13, "y": 32.84}, {"name": "图层 48", "parent": "图层 39", "length": 23.36, "rotation": -62.48, "x": 49.25, "y": -64.76}, {"name": "图层 49", "parent": "图层 48", "length": 23.36, "x": 68.4}, {"name": "图层 50", "parent": "图层 39", "length": 19.17, "rotation": -42.83, "x": 132.03, "y": -24.62}, {"name": "图层 51", "parent": "图层 50", "length": 19.17, "x": 56.15}, {"name": "bone5", "parent": "root", "x": 164.89, "y": 253.92, "scaleX": 0.58, "scaleY": 0.58}, {"name": "ganzi", "parent": "bone5", "length": 864.32, "rotation": 78.98, "x": 11.02, "y": 19.83}, {"name": "ganzi2", "parent": "ganzi", "rotation": -109.32, "x": 611.7, "y": -16.02, "color": "ff0000ff"}, {"name": "daizi", "parent": "ganzi", "length": 101.88, "rotation": 150.42, "x": 779.13, "y": -2.88}, {"name": "daizi2", "parent": "daizi", "length": 63.63, "rotation": 10.86, "x": 101.88}, {"name": "daizi3", "parent": "daizi2", "length": 70.4, "rotation": 10.09, "x": 63.63}, {"name": "daizi4", "parent": "daizi3", "length": 70.1, "rotation": 27.42, "x": 70.4}, {"name": "daizi5", "parent": "daizi4", "length": 51.93, "rotation": 11.77, "x": 70.1}, {"name": "daizi6", "parent": "daizi5", "length": 50.17, "rotation": 4.61, "x": 51.93}, {"name": "daizi7", "parent": "daizi6", "length": 46.43, "rotation": 11.17, "x": 50.17}, {"name": "daizi8", "parent": "daizi7", "length": 45.89, "rotation": 5.5, "x": 46.43}, {"name": "daizi9", "parent": "daizi8", "length": 40.74, "rotation": -5.28, "x": 45.89}, {"name": "daizi10", "parent": "daizi9", "length": 25.89, "rotation": 2.03, "x": 40.74}, {"name": "daizi11", "parent": "daizi10", "length": 30.36, "rotation": -28.6, "x": 25.89}, {"name": "bone6", "parent": "root", "x": -168.79, "y": 253.92, "scaleX": -0.58, "scaleY": 0.58}, {"name": "bone7", "parent": "root", "x": -176.8, "y": 259.45, "scaleX": 1.8, "scaleY": 1.8}, {"name": "qq1", "parent": "bone7", "length": 151.02, "rotation": 115.67, "x": 4.92, "y": 4.79}, {"name": "qq2", "parent": "qq1", "length": 10.72, "rotation": 107.17, "x": 122.32, "y": 12.03}, {"name": "qq3", "parent": "qq2", "length": 13.36, "rotation": 14.83, "x": 10.72}, {"name": "qq4", "parent": "qq3", "length": 11.32, "rotation": -11.65, "x": 13.36}, {"name": "qq5", "parent": "qq4", "length": 13.56, "rotation": -22.43, "x": 11.32}, {"name": "qq6", "parent": "qq5", "length": 14.07, "rotation": -38.9, "x": 13.69, "y": -0.06}, {"name": "qq7", "parent": "qq6", "length": 13.29, "rotation": 17.15, "x": 14.07}, {"name": "bone8", "parent": "root", "x": 174.86, "y": 259.45, "scaleX": -1.8, "scaleY": 1.8}, {"name": "qq8", "parent": "bone8", "length": 151.02, "rotation": 115.67, "x": 4.92, "y": 4.79}, {"name": "qq9", "parent": "qq8", "length": 10.72, "rotation": 107.17, "x": 122.32, "y": 12.03}, {"name": "qq10", "parent": "qq9", "length": 13.36, "rotation": 14.83, "x": 10.72}, {"name": "qq11", "parent": "qq10", "length": 11.32, "rotation": -11.65, "x": 13.36}, {"name": "qq12", "parent": "qq11", "length": 13.56, "rotation": -22.43, "x": 11.32}, {"name": "qq13", "parent": "qq12", "length": 14.07, "rotation": -38.9, "x": 13.69, "y": -0.06}, {"name": "qq14", "parent": "qq13", "length": 13.29, "rotation": 17.15, "x": 14.07}, {"name": "bone9", "parent": "root", "rotation": -117.6, "x": 159.86, "y": 164.62, "scaleX": 0.66, "scaleY": 0.64}, {"name": "qq15", "parent": "bone9", "length": 776.16, "rotation": -165.32, "x": -5.83, "y": -1.46}, {"name": "bone12", "parent": "root", "rotation": 118.8, "x": -155.76, "y": 164.62, "scaleX": -0.66, "scaleY": 0.64}, {"name": "ganzi3", "parent": "ganzi2", "x": 43.04, "y": -91, "color": "ff0000ff"}, {"name": "ganzi4", "parent": "ganzi2", "x": 25.24, "y": 26.44, "color": "ff0000ff"}, {"name": "ganzi5", "parent": "ganzi2", "x": -40.94, "y": 168.5, "color": "ff0000ff"}, {"name": "ganzi6", "parent": "ganzi2", "x": 112.5, "y": 56.54, "color": "ff0000ff"}, {"name": "ganzi7", "parent": "ganzi2", "x": 130.3, "y": -60.9, "color": "ff0000ff"}, {"name": "ganzi8", "parent": "ganzi2", "x": 46.32, "y": 198.6, "color": "ff0000ff"}, {"name": "ganzi9", "parent": "ganzi2", "x": 209.14, "y": 79.15, "color": "ff0000ff"}, {"name": "ganzi10", "parent": "ganzi2", "x": 250.62, "y": -22.43, "color": "ff0000ff"}, {"name": "ganzi11", "parent": "ganzi2", "x": 155.29, "y": 198.46, "color": "ff0000ff"}, {"name": "ganzi12", "parent": "ganzi2", "x": 251.42, "y": 146.85, "color": "ff0000ff"}, {"name": "ganzi13", "parent": "ganzi2", "x": 319.58, "y": 91.85, "color": "ff0000ff"}, {"name": "ganzi14", "parent": "ganzi2", "x": 327.38, "y": 184.32, "color": "ff0000ff"}, {"name": "ganzi15", "parent": "bone5", "length": 864.32, "rotation": 104.01, "x": -518.58, "y": -6.28, "scaleY": -1}, {"name": "ganzi16", "parent": "ganzi15", "rotation": -109.32, "x": 611.7, "y": -16.02, "color": "ff0000ff"}, {"name": "ganzi17", "parent": "ganzi16", "x": 43.04, "y": -91, "color": "ff0000ff"}, {"name": "ganzi18", "parent": "ganzi16", "x": 25.24, "y": 26.44, "color": "ff0000ff"}, {"name": "ganzi19", "parent": "ganzi16", "x": -40.94, "y": 168.5, "color": "ff0000ff"}, {"name": "ganzi20", "parent": "ganzi16", "x": 112.5, "y": 56.54, "color": "ff0000ff"}, {"name": "ganzi21", "parent": "ganzi16", "x": 130.3, "y": -60.9, "color": "ff0000ff"}, {"name": "ganzi22", "parent": "ganzi16", "x": 46.32, "y": 198.6, "color": "ff0000ff"}, {"name": "ganzi23", "parent": "ganzi16", "x": 209.14, "y": 79.15, "color": "ff0000ff"}, {"name": "ganzi24", "parent": "ganzi16", "x": 250.62, "y": -22.43, "color": "ff0000ff"}, {"name": "ganzi25", "parent": "ganzi16", "x": 155.29, "y": 198.46, "color": "ff0000ff"}, {"name": "ganzi26", "parent": "ganzi16", "x": 251.42, "y": 146.85, "color": "ff0000ff"}, {"name": "ganzi27", "parent": "ganzi16", "x": 319.58, "y": 91.85, "color": "ff0000ff"}, {"name": "ganzi28", "parent": "ganzi16", "x": 327.38, "y": 184.32, "color": "ff0000ff"}, {"name": "daizi12", "parent": "ganzi15", "length": 101.88, "rotation": 150.42, "x": 779.13, "y": -2.88}, {"name": "daizi13", "parent": "daizi12", "length": 63.63, "rotation": 10.86, "x": 101.88}, {"name": "daizi14", "parent": "daizi13", "length": 70.4, "rotation": 10.09, "x": 63.63}, {"name": "daizi15", "parent": "daizi14", "length": 70.1, "rotation": 27.42, "x": 70.4}, {"name": "daizi16", "parent": "daizi15", "length": 51.93, "rotation": 11.77, "x": 70.1}, {"name": "daizi17", "parent": "daizi16", "length": 50.17, "rotation": 4.61, "x": 51.93}, {"name": "daizi18", "parent": "daizi17", "length": 46.43, "rotation": 11.17, "x": 50.17}, {"name": "daizi19", "parent": "daizi18", "length": 45.89, "rotation": 5.5, "x": 46.43}, {"name": "daizi20", "parent": "daizi19", "length": 40.74, "rotation": -5.28, "x": 45.89}, {"name": "daizi21", "parent": "daizi20", "length": 25.89, "rotation": 2.03, "x": 40.74}, {"name": "daizi22", "parent": "daizi21", "length": 30.36, "rotation": -28.6, "x": 25.89}, {"name": "qq16", "parent": "qq15", "x": 545.82, "y": 9.03, "color": "54ff00ff"}, {"name": "qq17", "parent": "qq16", "x": 154.84, "y": -56.99, "color": "54ff00ff"}, {"name": "qq18", "parent": "qq16", "x": 154.84, "y": -56.99, "color": "54ff00ff"}, {"name": "qq19", "parent": "qq16", "x": 40.25, "y": -58.3, "color": "54ff00ff"}, {"name": "qq20", "parent": "qq16", "x": -53.35, "y": -28.87, "color": "54ff00ff"}, {"name": "qq21", "parent": "qq16", "x": 131.36, "y": -128.11, "color": "54ff00ff"}, {"name": "qq22", "parent": "qq16", "x": 32.45, "y": -130.04, "color": "54ff00ff"}, {"name": "qq23", "parent": "qq16", "x": -60.98, "y": -117.37, "color": "54ff00ff"}, {"name": "qq24", "parent": "qq16", "x": 36.59, "y": -205.07, "color": "54ff00ff"}, {"name": "qq25", "parent": "qq16", "x": -35.24, "y": -200.41, "color": "54ff00ff"}, {"name": "qq26", "parent": "qq16", "x": 38.63, "y": -274.41, "color": "54ff00ff"}, {"name": "qq27", "parent": "qq16", "x": 45.69, "y": -346.96, "color": "54ff00ff"}, {"name": "qq28", "parent": "bone9", "length": 776.16, "rotation": -137.63, "x": 212.07, "y": -452.29, "scaleY": -1}, {"name": "qq29", "parent": "qq28", "x": 545.82, "y": 9.03, "color": "54ff00ff"}, {"name": "qq30", "parent": "qq29", "x": 154.84, "y": -56.99, "color": "54ff00ff"}, {"name": "qq31", "parent": "qq29", "x": 154.84, "y": -56.99, "color": "54ff00ff"}, {"name": "qq32", "parent": "qq29", "x": 40.25, "y": -58.3, "color": "54ff00ff"}, {"name": "qq33", "parent": "qq29", "x": -53.35, "y": -28.87, "color": "54ff00ff"}, {"name": "qq34", "parent": "qq29", "x": 131.36, "y": -128.11, "color": "54ff00ff"}, {"name": "qq35", "parent": "qq29", "x": 32.45, "y": -130.04, "color": "54ff00ff"}, {"name": "qq36", "parent": "qq29", "x": -60.98, "y": -117.37, "color": "54ff00ff"}, {"name": "qq37", "parent": "qq29", "x": 36.59, "y": -205.07, "color": "54ff00ff"}, {"name": "qq38", "parent": "qq29", "x": -35.24, "y": -200.41, "color": "54ff00ff"}, {"name": "qq39", "parent": "qq29", "x": 38.63, "y": -274.41, "color": "54ff00ff"}, {"name": "qq40", "parent": "qq29", "x": 45.69, "y": -346.96, "color": "54ff00ff"}], "slots": [{"name": "qq3", "bone": "qq15", "attachment": "qq2"}, {"name": "qq4", "bone": "qq28", "attachment": "qq2"}, {"name": "qq1", "bone": "qq1", "attachment": "qq1"}, {"name": "qq2", "bone": "qq8", "attachment": "qq1"}, {"name": "daizi", "bone": "daizi", "attachment": "daizi"}, {"name": "daizi2", "bone": "daizi12", "attachment": "daizi"}, {"name": "qizi2", "bone": "root"}, {"name": "ganzi", "bone": "ganzi", "attachment": "ganzi"}, {"name": "ganzi2", "bone": "ganzi15", "attachment": "ganzi"}, {"name": "back", "bone": "root"}, {"name": "a1t1", "bone": "1j2"}, {"name": "a1t2", "bone": "1j3"}, {"name": "a12l", "bone": "1j5"}, {"name": "a11l", "bone": "1j4"}, {"name": "b12l", "bone": "lv8"}, {"name": "b11l", "bone": "lv7"}, {"name": "b1tz1", "bone": "1j2"}, {"name": "b1tz2", "bone": "1j3"}, {"name": "b2wl", "bone": "lv8"}, {"name": "b1m", "bone": "lv7"}, {"name": "b1wd2", "bone": "lv7"}, {"name": "b1wd", "bone": "lv2"}, {"name": "a1st", "bone": "1j1"}, {"name": "b2hl", "bone": "lv10"}, {"name": "b2syhl", "bone": "lv9"}, {"name": "c33l", "bone": "lv20"}, {"name": "b2wd", "bone": "lv8"}, {"name": "c1tz2", "bone": "lv14"}, {"name": "c1tz1", "bone": "lv13"}, {"name": "c12l", "bone": "lv15"}, {"name": "c11l", "bone": "lv12"}, {"name": "c1ss", "bone": "lv11"}, {"name": "c1lang", "bone": "lv15"}, {"name": "c2people1", "bone": "lv17", "attachment": "c2people1"}, {"name": "c2people3", "bone": "bone3"}, {"name": "c2people4", "bone": "bone3"}, {"name": "c2people5", "bone": "bone3"}, {"name": "c2people2", "bone": "lv17", "attachment": "c2people2"}, {"name": "c2people6", "bone": "bone"}, {"name": "c2people7", "bone": "bone"}, {"name": "c2people8", "bone": "bone"}, {"name": "c2hw", "bone": "lv16"}, {"name": "c2hw2", "bone": "lv18"}, {"name": "qizi", "bone": "ganzi2", "attachment": "qizi"}, {"name": "qizi3", "bone": "ganzi16", "attachment": "qizi"}, {"name": "c2tz2l", "bone": "lv19"}, {"name": "c3tz2_2", "bone": "lv22"}, {"name": "c3tz2_1", "bone": "lv21"}, {"name": "c3people3", "bone": "lv22", "attachment": "c3people3"}, {"name": "c3people4", "bone": "lv21", "attachment": "c3people4"}, {"name": "C103-0", "bone": "root", "attachment": "C103-0"}, {"name": "图层 30", "bone": "图层 46"}, {"name": "图层 35", "bone": "图层 50"}, {"name": "图层 31", "bone": "图层 38"}, {"name": "图层 33", "bone": "图层 39"}, {"name": "图层 32", "bone": "图层 44"}, {"name": "图层 34", "bone": "图层 48"}], "skins": [{"name": "default", "attachments": {"ganzi": {"ganzi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [18.68, -201.49, -23.77, 16.41, 872.38, 190.99, 914.83, -26.92], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 222, "height": 913}}, "b1tz2": {"b1tz2": {"x": 9.09, "y": 111.28, "width": 187, "height": 339}}, "c11l": {"c11l": {"x": 0.91, "y": 153.53, "width": 750, "height": 329}}, "c1ss": {"c1ss": {"x": 0.26, "y": 28.93, "width": 854, "height": 135}}, "ganzi2": {"ganzi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [18.68, -201.49, -23.77, 16.41, 872.38, 190.99, 914.83, -26.92], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 222, "height": 913}}, "qq1": {"qq1": {"type": "mesh", "uvs": [0.54937, 0.0037, 0.58344, 0.02714, 0.58898, 0.04365, 0.58064, 0.06531, 0.64247, 0.11253, 0.69835, 0.16525, 0.71301, 0.197, 0.72242, 0.24973, 0.7283, 0.31201, 0.72846, 0.3298, 0.7371, 0.35816, 0.75746, 0.39618, 0.79719, 0.43087, 0.83141, 0.39606, 0.88284, 0.39609, 0.91202, 0.40656, 0.95467, 0.43848, 0.97878, 0.4644, 0.99961, 0.50309, 1, 0.51424, 1, 0.53481, 0.98333, 0.5348, 0.94771, 0.50852, 0.92071, 0.49642, 0.89893, 0.49636, 0.81389, 0.53939, 0.71267, 0.55848, 0.58006, 0.55772, 0.45412, 0.53259, 0.35454, 0.49004, 0.33075, 0.47622, 0.05299, 0.99143, 0.03361, 1, 0.0201, 1, 0.01543, 0.99882, 0, 0.98495, 0, 0.95754, 0.02036, 0.92394, 0.03301, 0.89688, 0.22064, 0.55192, 0.27165, 0.4439, 0.28188, 0.39599, 0.30573, 0.38751, 0.32637, 0.35336, 0.44717, 0.13161, 0.45182, 0.1099, 0.45172, 0.08047, 0.46238, 0.05174, 0.47284, 0.04204, 0.48953, 0.00379], "triangles": [24, 14, 15, 24, 15, 16, 23, 24, 16, 17, 23, 16, 22, 23, 17, 18, 22, 17, 22, 18, 19, 21, 22, 19, 21, 19, 20, 14, 12, 13, 24, 12, 14, 12, 24, 25, 26, 11, 12, 26, 12, 25, 11, 27, 10, 26, 27, 11, 27, 28, 9, 10, 27, 9, 5, 6, 4, 3, 0, 1, 3, 1, 2, 48, 45, 46, 48, 49, 0, 3, 48, 0, 48, 46, 47, 3, 45, 48, 45, 3, 4, 43, 29, 42, 30, 42, 29, 40, 41, 42, 30, 40, 42, 43, 28, 29, 44, 7, 8, 4, 6, 45, 6, 44, 45, 6, 7, 44, 8, 43, 44, 43, 8, 9, 9, 28, 43, 39, 40, 30, 35, 32, 33, 31, 38, 39, 37, 38, 31, 31, 36, 37, 31, 39, 30, 33, 34, 35, 32, 35, 36, 31, 32, 36], "vertices": [3, 60, 163.23, 2.11, 0.99257, 61, -21.56, -36.16, 0.00663, 62, -40.46, -26.69, 0.0008, 1, 60, 162.09, 7.9, 1, 1, 60, 160.18, 9.67, 1, 1, 60, 156.73, 10.04, 1, 4, 60, 154.03, 20.85, 0.38658, 61, -0.94, -32.9, 0.52425, 62, -19.69, -28.82, 0.08208, 63, -26.54, -34.9, 0.00708, 6, 60, 150.22, 31.28, 0.23321, 61, 10.15, -32.34, 0.52236, 62, -8.82, -31.11, 0.20989, 63, -15.44, -34.95, 0.0329, 64, -11.4, -42.52, 0.00062, 65, 7.14, -48.8, 0.00103, 6, 60, 146.78, 35.18, 0.17216, 61, 14.89, -30.21, 0.49866, 62, -3.69, -30.27, 0.26978, 63, -10.59, -33.09, 0.05277, 64, -7.62, -38.94, 0.00332, 65, 7.83, -43.65, 0.00331, 6, 60, 140.18, 39.8, 0.07892, 61, 21.26, -25.26, 0.43167, 62, 3.72, -27.12, 0.35279, 63, -3.96, -28.51, 0.10505, 64, -3.24, -32.18, 0.01828, 65, 6.99, -35.63, 0.01329, 6, 60, 132.07, 44.6, 0.01907, 61, 28.24, -18.93, 0.17819, 62, 12.1, -22.79, 0.41733, 63, 3.36, -22.57, 0.23887, 64, 1.26, -23.9, 0.08914, 65, 5.3, -26.36, 0.0574, 6, 60, 129.66, 45.78, 0.00935, 61, 30.08, -16.98, 0.12412, 62, 14.37, -21.37, 0.37464, 63, 5.31, -20.72, 0.27558, 64, 2.36, -21.45, 0.13122, 65, 4.61, -23.77, 0.0851, 6, 60, 126.32, 48.72, 0.00144, 61, 33.87, -14.66, 0.05378, 62, 18.63, -20.09, 0.2527, 63, 9.22, -18.62, 0.2931, 64, 5.17, -18.01, 0.23116, 65, 4.64, -19.32, 0.16782, 5, 61, 39.83, -12.37, 0.00943, 62, 24.98, -19.41, 0.0877, 63, 15.31, -16.67, 0.16807, 64, 10.05, -13.88, 0.32826, 65, 5.84, -13.05, 0.40654, 6, 61, 47.43, -12.28, 0.0001, 62, 32.35, -21.27, 0.00738, 63, 22.89, -17, 0.01743, 64, 17.19, -11.29, 0.07416, 65, 9.78, -6.55, 0.79861, 66, -6.03, -4.99, 0.1023, 5, 62, 30.45, -28.08, 7e-05, 63, 22.41, -24.06, 0.00023, 64, 19.43, -18, 0.00111, 65, 15.74, -10.36, 0.34694, 66, -1.46, -10.39, 0.65164, 2, 65, 22.61, -8.48, 0.07122, 66, 5.66, -10.62, 0.92878, 2, 65, 26.1, -5.88, 0.00655, 66, 9.76, -9.17, 0.99345, 1, 66, 15.82, -4.55, 1, 1, 66, 19.29, -0.75, 1, 1, 66, 22.36, 4.99, 1, 1, 66, 22.47, 6.67, 1, 1, 66, 22.57, 9.77, 1, 1, 66, 20.26, 9.84, 1, 1, 66, 15.2, 6.04, 1, 2, 65, 23.68, 7.5, 0.00044, 66, 11.4, 4.34, 0.99956, 2, 65, 20.77, 6.7, 0.02112, 66, 8.38, 4.42, 0.97888, 3, 64, 25.86, 2.78, 0.01222, 65, 7.69, 9.85, 0.94714, 66, -3.19, 11.29, 0.04064, 4, 60, 97.63, 58.75, 0.00025, 63, 28.61, 4.8, 0.01189, 64, 14.15, 11.03, 0.82726, 65, -6.6, 8.92, 0.1606, 4, 60, 89.77, 42.13, 0.06025, 62, 32.41, 14.39, 0.02896, 63, 15.76, 17.94, 0.5004, 64, -2.74, 18.28, 0.41039, 5, 60, 85.63, 24.75, 0.28804, 61, 22.99, 31.3, 0.20412, 62, 19.88, 27.12, 0.14073, 63, 0.91, 27.88, 0.336, 64, -20.25, 21.8, 0.0311, 5, 60, 85.43, 9.53, 0.57025, 61, 8.5, 35.98, 0.30172, 62, 7.07, 35.35, 0.05508, 63, -13.29, 33.35, 0.07284, 64, -35.47, 21.44, 0.00012, 1, 60, 85.88, 5.66, 1, 1, 60, -0.82, 4.6, 1, 1, 60, -3.15, 2.74, 1, 1, 60, -3.96, 1.05, 1, 1, 60, -4.08, 0.39, 1, 1, 60, -3.13, -2.44, 1, 1, 60, 0.6, -4.23, 1, 1, 60, 6.39, -3.88, 1, 1, 60, 10.83, -4.07, 1, 1, 60, 68.98, -3.16, 1, 1, 60, 86.72, -3.84, 1, 1, 60, 93.85, -5.69, 1, 1, 60, 96.44, -3.26, 1, 1, 60, 102.32, -2.91, 1, 1, 60, 139.71, -2.3, 1, 1, 60, 142.94, -3.14, 1, 1, 60, 146.93, -5.08, 1, 1, 60, 151.48, -5.62, 1, 1, 60, 153.43, -4.95, 1, 1, 60, 159.63, -5.36, 1], "hull": 50, "edges": [0, 98, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98], "width": 239, "height": 260}}, "qq2": {"qq1": {"type": "mesh", "uvs": [0.54937, 0.0037, 0.58344, 0.02714, 0.58898, 0.04365, 0.58064, 0.06531, 0.64247, 0.11253, 0.69835, 0.16525, 0.71301, 0.197, 0.72242, 0.24973, 0.7283, 0.31201, 0.72846, 0.3298, 0.7371, 0.35816, 0.75746, 0.39618, 0.79719, 0.43087, 0.83141, 0.39606, 0.88284, 0.39609, 0.91202, 0.40656, 0.95467, 0.43848, 0.97878, 0.4644, 0.99961, 0.50309, 1, 0.51424, 1, 0.53481, 0.98333, 0.5348, 0.94771, 0.50852, 0.92071, 0.49642, 0.89893, 0.49636, 0.81389, 0.53939, 0.71267, 0.55848, 0.58006, 0.55772, 0.45412, 0.53259, 0.35454, 0.49004, 0.33075, 0.47622, 0.05299, 0.99143, 0.03361, 1, 0.0201, 1, 0.01543, 0.99882, 0, 0.98495, 0, 0.95754, 0.02036, 0.92394, 0.03301, 0.89688, 0.22064, 0.55192, 0.27165, 0.4439, 0.28188, 0.39599, 0.30573, 0.38751, 0.32637, 0.35336, 0.44717, 0.13161, 0.45182, 0.1099, 0.45172, 0.08047, 0.46238, 0.05174, 0.47284, 0.04204, 0.48953, 0.00379], "triangles": [24, 14, 15, 24, 15, 16, 23, 24, 16, 17, 23, 16, 22, 23, 17, 18, 22, 17, 22, 18, 19, 21, 22, 19, 21, 19, 20, 14, 12, 13, 24, 12, 14, 12, 24, 25, 26, 11, 12, 26, 12, 25, 11, 27, 10, 26, 27, 11, 27, 28, 9, 10, 27, 9, 5, 6, 4, 3, 0, 1, 3, 1, 2, 48, 45, 46, 48, 49, 0, 3, 48, 0, 48, 46, 47, 3, 45, 48, 45, 3, 4, 43, 29, 42, 30, 42, 29, 40, 41, 42, 30, 40, 42, 43, 28, 29, 44, 7, 8, 4, 6, 45, 6, 44, 45, 6, 7, 44, 8, 43, 44, 43, 8, 9, 9, 28, 43, 39, 40, 30, 35, 32, 33, 31, 38, 39, 37, 38, 31, 31, 36, 37, 31, 39, 30, 33, 34, 35, 32, 35, 36, 31, 32, 36], "vertices": [3, 68, 163.23, 2.11, 0.99257, 69, -21.56, -36.16, 0.00663, 70, -40.46, -26.69, 0.0008, 1, 68, 162.09, 7.9, 1, 1, 68, 160.18, 9.67, 1, 1, 68, 156.73, 10.04, 1, 4, 68, 154.03, 20.85, 0.38658, 69, -0.94, -32.9, 0.52425, 70, -19.69, -28.82, 0.08208, 71, -26.54, -34.9, 0.00708, 6, 68, 150.22, 31.28, 0.23321, 69, 10.15, -32.34, 0.52236, 70, -8.82, -31.11, 0.20989, 71, -15.44, -34.95, 0.0329, 72, -11.4, -42.52, 0.00062, 73, 7.14, -48.8, 0.00103, 6, 68, 146.78, 35.18, 0.17216, 69, 14.89, -30.21, 0.49866, 70, -3.69, -30.27, 0.26978, 71, -10.59, -33.09, 0.05277, 72, -7.62, -38.94, 0.00332, 73, 7.83, -43.65, 0.00331, 6, 68, 140.18, 39.8, 0.07892, 69, 21.26, -25.26, 0.43167, 70, 3.72, -27.12, 0.35279, 71, -3.96, -28.51, 0.10505, 72, -3.24, -32.18, 0.01828, 73, 6.99, -35.63, 0.01329, 6, 68, 132.07, 44.6, 0.01907, 69, 28.24, -18.93, 0.17819, 70, 12.1, -22.79, 0.41733, 71, 3.36, -22.57, 0.23887, 72, 1.26, -23.9, 0.08914, 73, 5.3, -26.36, 0.0574, 6, 68, 129.66, 45.78, 0.00935, 69, 30.08, -16.98, 0.12412, 70, 14.37, -21.37, 0.37464, 71, 5.31, -20.72, 0.27558, 72, 2.36, -21.45, 0.13122, 73, 4.61, -23.77, 0.0851, 6, 68, 126.32, 48.72, 0.00144, 69, 33.87, -14.66, 0.05378, 70, 18.63, -20.09, 0.2527, 71, 9.22, -18.62, 0.2931, 72, 5.17, -18.01, 0.23116, 73, 4.64, -19.32, 0.16782, 5, 69, 39.83, -12.37, 0.00943, 70, 24.98, -19.41, 0.0877, 71, 15.31, -16.67, 0.16807, 72, 10.05, -13.88, 0.32826, 73, 5.84, -13.05, 0.40654, 6, 69, 47.43, -12.28, 0.0001, 70, 32.35, -21.27, 0.00738, 71, 22.89, -17, 0.01743, 72, 17.19, -11.29, 0.07416, 73, 9.78, -6.55, 0.79861, 74, -6.03, -4.99, 0.1023, 5, 70, 30.45, -28.08, 7e-05, 71, 22.41, -24.06, 0.00023, 72, 19.43, -18, 0.00111, 73, 15.74, -10.36, 0.34694, 74, -1.46, -10.39, 0.65164, 2, 73, 22.61, -8.48, 0.07122, 74, 5.66, -10.62, 0.92878, 2, 73, 26.1, -5.88, 0.00655, 74, 9.76, -9.17, 0.99345, 1, 74, 15.82, -4.55, 1, 1, 74, 19.29, -0.75, 1, 1, 74, 22.36, 4.99, 1, 1, 74, 22.47, 6.67, 1, 1, 74, 22.57, 9.77, 1, 1, 74, 20.26, 9.84, 1, 1, 74, 15.2, 6.04, 1, 2, 73, 23.68, 7.5, 0.00044, 74, 11.4, 4.34, 0.99956, 2, 73, 20.77, 6.7, 0.02112, 74, 8.38, 4.42, 0.97888, 3, 72, 25.86, 2.78, 0.01222, 73, 7.69, 9.85, 0.94714, 74, -3.19, 11.29, 0.04064, 4, 68, 97.63, 58.75, 0.00025, 71, 28.61, 4.8, 0.01189, 72, 14.15, 11.03, 0.82726, 73, -6.6, 8.92, 0.1606, 4, 68, 89.77, 42.13, 0.06025, 70, 32.41, 14.39, 0.02896, 71, 15.76, 17.94, 0.5004, 72, -2.74, 18.28, 0.41039, 5, 68, 85.63, 24.75, 0.28804, 69, 22.99, 31.3, 0.20412, 70, 19.88, 27.12, 0.14073, 71, 0.91, 27.88, 0.336, 72, -20.25, 21.8, 0.0311, 5, 68, 85.43, 9.53, 0.57025, 69, 8.5, 35.98, 0.30172, 70, 7.07, 35.35, 0.05508, 71, -13.29, 33.35, 0.07284, 72, -35.47, 21.44, 0.00012, 1, 68, 85.88, 5.66, 1, 1, 68, -0.82, 4.6, 1, 1, 68, -3.15, 2.74, 1, 1, 68, -3.96, 1.05, 1, 1, 68, -4.08, 0.39, 1, 1, 68, -3.13, -2.44, 1, 1, 68, 0.6, -4.23, 1, 1, 68, 6.39, -3.88, 1, 1, 68, 10.83, -4.07, 1, 1, 68, 68.98, -3.16, 1, 1, 68, 86.72, -3.84, 1, 1, 68, 93.85, -5.69, 1, 1, 68, 96.44, -3.26, 1, 1, 68, 102.32, -2.91, 1, 1, 68, 139.71, -2.3, 1, 1, 68, 142.94, -3.14, 1, 1, 68, 146.93, -5.08, 1, 1, 68, 151.48, -5.62, 1, 1, 68, 153.43, -4.95, 1, 1, 68, 159.63, -5.36, 1], "hull": 50, "edges": [0, 98, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98], "width": 239, "height": 260}}, "qq3": {"qq2": {"type": "mesh", "uvs": [0, 0.97048, 0.22635, 0.06851, 0.31907, 0, 0.34436, 0, 0.39493, 0.08479, 0.36965, 0.12276, 0.49397, 0.14854, 0.59302, 0.22178, 0.63938, 0.31672, 0.7321, 0.34385, 0.78486, 0.33728, 0.86275, 0.32757, 0.90731, 0.34081, 0.97232, 0.36013, 1, 0.40895, 1, 0.43879, 0.92807, 0.42387, 0.88283, 0.44439, 0.83535, 0.46592, 0.77153, 0.48646, 0.68784, 0.51339, 0.51505, 0.54188, 0.34857, 0.54188, 0.23689, 0.52831, 0.07252, 1, 0, 1, 0.30153, 0.33084, 0.40597, 0.35074, 0.49202, 0.37188, 0.60111, 0.40482, 0.72227, 0.41055, 0.8715, 0.37793, 0.94217, 0.39095, 0.90534, 0.38416, 0.79633, 0.39436, 0.26902, 0.43017, 0.38265, 0.43977, 0.48072, 0.45911, 0.61189, 0.46768, 0.72652, 0.45586, 0.82255, 0.42752, 0.87567, 0.40939, 0.91314, 0.40623, 0.94039, 0.411, 0.95818, 0.37981, 0.90874, 0.35938, 0.86097, 0.35661, 0.78502, 0.36526, 0.72896, 0.37807, 0.62267, 0.362, 0.54297, 0.31763, 0.44202, 0.26758, 0.3321, 0.23746], "triangles": [36, 35, 27, 27, 26, 51, 35, 1, 26, 26, 52, 51, 52, 5, 6, 52, 1, 5, 35, 23, 1, 24, 25, 0, 24, 0, 23, 2, 5, 1, 4, 2, 3, 4, 5, 2, 0, 1, 23, 26, 1, 52, 51, 52, 6, 23, 35, 22, 35, 26, 27, 16, 43, 15, 15, 32, 14, 16, 42, 43, 15, 43, 32, 43, 42, 32, 32, 44, 14, 44, 13, 14, 42, 33, 32, 32, 33, 44, 33, 45, 44, 44, 45, 13, 13, 45, 12, 18, 19, 40, 19, 39, 40, 18, 40, 17, 40, 30, 34, 40, 39, 30, 40, 41, 17, 16, 41, 42, 16, 17, 41, 40, 34, 41, 30, 48, 34, 34, 31, 41, 41, 33, 42, 41, 31, 33, 48, 47, 34, 34, 46, 31, 34, 47, 46, 33, 31, 45, 31, 46, 45, 9, 10, 47, 47, 10, 46, 46, 12, 45, 46, 11, 12, 46, 10, 11, 21, 38, 20, 20, 39, 19, 20, 38, 39, 37, 29, 38, 38, 30, 39, 38, 29, 30, 29, 49, 30, 30, 49, 48, 48, 9, 47, 48, 49, 9, 49, 8, 9, 49, 50, 8, 22, 37, 21, 21, 37, 38, 22, 36, 37, 22, 35, 36, 37, 28, 29, 37, 36, 28, 36, 27, 28, 29, 28, 49, 28, 50, 49, 28, 27, 50, 27, 51, 50, 8, 50, 7, 50, 51, 7, 51, 6, 7], "vertices": [1, 76, 131.84, 26.05, 1, 1, 76, 765.75, 37.33, 1, 1, 76, 819.87, 4.98, 1, 1, 76, 821.87, -6.13, 1, 1, 76, 767.96, -38.78, 1, 1, 76, 740.02, -32.33, 1, 2, 117, 31.6, -42.19, 0.62564, 120, 55.08, 28.94, 0.37436, 4, 117, -10.59, -94.73, 0.0058, 120, 12.89, -23.6, 0.96342, 121, 111.79, -21.67, 0.01257, 123, 107.66, 53.35, 0.01821, 4, 120, -48.29, -55.64, 0.24706, 121, 50.61, -53.71, 0.23292, 123, 46.48, 21.31, 0.51496, 125, 44.43, 90.66, 0.00507, 5, 120, -59.49, -99.73, 0.01549, 121, 39.42, -97.8, 0.00736, 123, 35.28, -22.78, 0.63339, 125, 33.23, 46.57, 0.34262, 126, 26.17, 119.12, 0.00114, 4, 120, -50.83, -122.12, 0.00066, 123, 43.94, -45.17, 0.28022, 125, 41.89, 24.18, 0.68486, 126, 34.84, 96.73, 0.03427, 3, 123, 56.72, -78.21, 0.04381, 125, 54.68, -8.86, 0.74073, 126, 47.62, 63.69, 0.21546, 3, 123, 51.21, -99.43, 0.00771, 125, 49.16, -30.08, 0.60025, 126, 42.1, 42.47, 0.39204, 2, 125, 41.11, -61.03, 0.27273, 126, 34.05, 11.52, 0.72727, 1, 126, 2.89, -6.65, 1, 1, 126, -17.49, -10.31, 1, 3, 124, 67.95, -123.41, 0.00029, 125, -5.93, -49.41, 0.38866, 126, -12.99, 23.14, 0.61106, 4, 123, -21.47, -101.39, 0.00089, 124, 50.36, -106.04, 0.02361, 125, -23.52, -32.04, 0.79862, 126, -30.57, 40.51, 0.17688, 4, 123, -39.93, -83.16, 0.04339, 124, 31.9, -87.81, 0.13007, 125, -41.98, -13.81, 0.80415, 126, -49.03, 58.74, 0.02239, 3, 123, -59, -57.63, 0.12398, 124, 12.83, -62.28, 0.39535, 125, -61.05, 11.72, 0.48067, 3, 123, -84.01, -24.15, 0.0388, 124, -12.18, -28.8, 0.86203, 125, -86.06, 45.2, 0.09917, 3, 121, -112.99, -26.71, 0.00552, 122, -19.56, -39.38, 0.67424, 124, -45.3, 43.66, 0.32024, 2, 118, -133.95, -25.27, 0.00436, 122, -32.72, 33.8, 0.99564, 1, 76, 452.55, -23.78, 1, 1, 76, 117.41, -9.46, 1, 1, 76, 111.68, 22.42, 1, 1, 76, 592.53, -27.94, 1, 5, 117, -113.46, -28.34, 0.00798, 118, 1.13, -27.03, 0.58362, 120, -89.98, 42.79, 0.03825, 121, 8.93, 44.71, 0.35113, 122, 102.36, 32.05, 0.01902, 4, 117, -121.1, -68.76, 0.00023, 118, -6.51, -67.45, 0.02967, 120, -97.62, 2.36, 0.0028, 121, 1.29, 4.29, 0.9673, 5, 120, -111.49, -49.64, 0.00768, 121, -12.58, -47.71, 0.31655, 122, 80.85, -60.38, 0.02109, 123, -16.72, 27.31, 0.49645, 124, 55.11, 22.66, 0.15822, 3, 123, -11.05, -26.65, 0.54717, 124, 60.78, -31.3, 0.1137, 125, -13.1, 42.7, 0.33913, 3, 123, 23.03, -88.24, 0.0081, 125, 20.98, -18.9, 0.73185, 126, 13.92, 53.66, 0.26005, 2, 125, 17.67, -51.56, 0.28249, 126, 10.61, 20.99, 0.71751, 2, 125, 19.39, -34.54, 0.53716, 126, 12.34, 38.01, 0.46284, 3, 123, 5.86, -57.22, 0.1418, 124, 77.69, -61.87, 0.01163, 125, 3.82, 12.13, 0.84657, 1, 76, 522.12, -25.85, 1, 4, 118, -61.52, -27.71, 0.30246, 121, -53.72, 44.03, 0.23538, 122, 39.71, 31.36, 0.45749, 124, 13.97, 114.4, 0.00467, 5, 118, -66.97, -73.2, 0.03479, 121, -59.17, -1.46, 0.27941, 122, 34.26, -14.12, 0.51281, 123, -63.31, 73.57, 0.01144, 124, 8.52, 68.92, 0.16155, 5, 121, -54.66, -60.17, 0.07915, 122, 38.77, -72.84, 0.05351, 123, -58.8, 14.86, 0.11507, 124, 13.03, 10.2, 0.74553, 125, -60.85, 84.2, 0.00674, 3, 123, -41.66, -34.08, 0.2414, 124, 30.17, -38.74, 0.40796, 125, -43.71, 35.26, 0.35063, 4, 123, -14.71, -72.82, 0.03122, 124, 57.12, -77.47, 0.05321, 125, -16.76, -3.47, 0.89118, 126, -23.82, 69.08, 0.02439, 3, 124, 73.7, -98.59, 0.00157, 125, -0.18, -24.59, 0.74876, 126, -7.23, 47.96, 0.24967, 3, 124, 78.82, -114.68, 1e-05, 125, 4.94, -40.68, 0.48367, 126, -2.12, 31.87, 0.51632, 2, 125, 3.84, -53.24, 0.26351, 126, -3.22, 19.31, 0.73649, 2, 125, 26.54, -57.23, 0.23402, 126, 19.48, 15.32, 0.76598, 3, 123, 38.63, -102.34, 0.00296, 125, 36.59, -32.99, 0.54936, 126, 29.53, 39.56, 0.44768, 3, 123, 36.75, -81, 0.03516, 125, 34.7, -11.65, 0.74795, 126, 27.64, 60.9, 0.21689, 5, 120, -69.93, -125.63, 0.00032, 123, 24.84, -48.67, 0.28397, 124, 96.67, -53.33, 0.00104, 125, 22.79, 20.67, 0.69747, 126, 15.74, 93.23, 0.0172, 4, 120, -83.11, -102.56, 0.00335, 123, 11.66, -25.6, 0.6349, 124, 83.49, -30.26, 0.01011, 125, 9.61, 43.74, 0.35163, 4, 120, -80.54, -53.86, 0.08269, 121, 18.37, -51.93, 0.24685, 123, 14.23, 23.09, 0.66519, 124, 86.06, 18.44, 0.00528, 5, 117, -80.01, -84.5, 0.00238, 118, 34.57, -83.18, 0.03493, 120, -56.53, -13.37, 0.32829, 121, 42.38, -11.45, 0.45135, 123, 38.24, 63.58, 0.18305, 5, 117, -53.81, -33.97, 0.21663, 118, 60.78, -32.66, 0.24626, 120, -30.33, 37.15, 0.38238, 121, 68.58, 39.08, 0.14917, 123, 64.44, 114.11, 0.00556, 1, 76, 658.72, -29.91, 1], "hull": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 26, 28, 28, 30, 30, 32, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 0, 50, 52, 54, 54, 56, 56, 58, 58, 60, 64, 30, 22, 24, 24, 26, 32, 34, 34, 36, 62, 66, 66, 64, 18, 20, 20, 22, 60, 68, 68, 62, 36, 38, 38, 40, 46, 70, 70, 52, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 10, 104, 104, 52, 102, 104], "width": 289, "height": 449}}, "qq4": {"qq2": {"type": "mesh", "uvs": [0, 0.97048, 0.22635, 0.06851, 0.31907, 0, 0.34436, 0, 0.39493, 0.08479, 0.36965, 0.12276, 0.49397, 0.14854, 0.59302, 0.22178, 0.63938, 0.31672, 0.7321, 0.34385, 0.78486, 0.33728, 0.86275, 0.32757, 0.90731, 0.34081, 0.97232, 0.36013, 1, 0.40895, 1, 0.43879, 0.92807, 0.42387, 0.88283, 0.44439, 0.83535, 0.46592, 0.77153, 0.48646, 0.68784, 0.51339, 0.51505, 0.54188, 0.34857, 0.54188, 0.23689, 0.52831, 0.07252, 1, 0, 1, 0.30153, 0.33084, 0.40597, 0.35074, 0.49202, 0.37188, 0.60111, 0.40482, 0.72227, 0.41055, 0.8715, 0.37793, 0.94217, 0.39095, 0.90534, 0.38416, 0.79633, 0.39436, 0.26902, 0.43017, 0.38265, 0.43977, 0.48072, 0.45911, 0.61189, 0.46768, 0.72652, 0.45586, 0.82255, 0.42752, 0.87567, 0.40939, 0.91314, 0.40623, 0.94039, 0.411, 0.95818, 0.37981, 0.90874, 0.35938, 0.86097, 0.35661, 0.78502, 0.36526, 0.72896, 0.37807, 0.62267, 0.362, 0.54297, 0.31763, 0.44202, 0.26758, 0.3321, 0.23746], "triangles": [36, 35, 27, 27, 26, 51, 35, 1, 26, 26, 52, 51, 52, 5, 6, 52, 1, 5, 35, 23, 1, 24, 25, 0, 24, 0, 23, 2, 5, 1, 4, 2, 3, 4, 5, 2, 0, 1, 23, 26, 1, 52, 51, 52, 6, 23, 35, 22, 35, 26, 27, 16, 43, 15, 15, 32, 14, 16, 42, 43, 15, 43, 32, 43, 42, 32, 32, 44, 14, 44, 13, 14, 42, 33, 32, 32, 33, 44, 33, 45, 44, 44, 45, 13, 13, 45, 12, 18, 19, 40, 19, 39, 40, 18, 40, 17, 40, 30, 34, 40, 39, 30, 40, 41, 17, 16, 41, 42, 16, 17, 41, 40, 34, 41, 30, 48, 34, 34, 31, 41, 41, 33, 42, 41, 31, 33, 48, 47, 34, 34, 46, 31, 34, 47, 46, 33, 31, 45, 31, 46, 45, 9, 10, 47, 47, 10, 46, 46, 12, 45, 46, 11, 12, 46, 10, 11, 21, 38, 20, 20, 39, 19, 20, 38, 39, 37, 29, 38, 38, 30, 39, 38, 29, 30, 29, 49, 30, 30, 49, 48, 48, 9, 47, 48, 49, 9, 49, 8, 9, 49, 50, 8, 22, 37, 21, 21, 37, 38, 22, 36, 37, 22, 35, 36, 37, 28, 29, 37, 36, 28, 36, 27, 28, 29, 28, 49, 28, 50, 49, 28, 27, 50, 27, 51, 50, 8, 50, 7, 50, 51, 7, 51, 6, 7], "vertices": [1, 127, 131.84, 26.05, 1, 1, 127, 765.75, 37.33, 1, 1, 127, 819.87, 4.98, 1, 1, 127, 821.87, -6.13, 1, 1, 127, 767.96, -38.78, 1, 1, 127, 740.02, -32.33, 1, 2, 130, 31.6, -42.19, 0.62564, 133, 55.08, 28.94, 0.37436, 4, 130, -10.59, -94.73, 0.0058, 133, 12.89, -23.6, 0.96342, 134, 111.79, -21.67, 0.01257, 136, 107.66, 53.35, 0.01821, 4, 133, -48.29, -55.64, 0.24706, 134, 50.61, -53.71, 0.23292, 136, 46.48, 21.31, 0.51496, 138, 44.43, 90.66, 0.00507, 5, 133, -59.49, -99.73, 0.01549, 134, 39.42, -97.8, 0.00736, 136, 35.28, -22.78, 0.63339, 138, 33.23, 46.57, 0.34262, 139, 26.17, 119.12, 0.00114, 4, 133, -50.83, -122.12, 0.00066, 136, 43.94, -45.17, 0.28022, 138, 41.89, 24.18, 0.68486, 139, 34.84, 96.73, 0.03427, 3, 136, 56.72, -78.21, 0.04381, 138, 54.68, -8.86, 0.74073, 139, 47.62, 63.69, 0.21546, 3, 136, 51.21, -99.43, 0.00771, 138, 49.16, -30.08, 0.60025, 139, 42.1, 42.47, 0.39204, 2, 138, 41.11, -61.03, 0.27273, 139, 34.05, 11.52, 0.72727, 1, 139, 2.89, -6.65, 1, 1, 139, -17.49, -10.31, 1, 3, 137, 67.95, -123.41, 0.00029, 138, -5.93, -49.41, 0.38866, 139, -12.99, 23.14, 0.61106, 4, 136, -21.47, -101.39, 0.00089, 137, 50.36, -106.04, 0.02361, 138, -23.52, -32.04, 0.79862, 139, -30.57, 40.51, 0.17688, 4, 136, -39.93, -83.16, 0.04339, 137, 31.9, -87.81, 0.13007, 138, -41.98, -13.81, 0.80415, 139, -49.03, 58.74, 0.02239, 3, 136, -59, -57.63, 0.12398, 137, 12.83, -62.28, 0.39535, 138, -61.05, 11.72, 0.48067, 3, 136, -84.01, -24.15, 0.0388, 137, -12.18, -28.8, 0.86203, 138, -86.06, 45.2, 0.09917, 3, 134, -112.99, -26.71, 0.00552, 135, -19.56, -39.38, 0.67424, 137, -45.3, 43.66, 0.32024, 2, 131, -133.95, -25.27, 0.00436, 135, -32.72, 33.8, 0.99564, 1, 127, 452.55, -23.78, 1, 1, 127, 117.41, -9.46, 1, 1, 127, 111.68, 22.42, 1, 1, 127, 592.53, -27.94, 1, 5, 130, -113.46, -28.34, 0.00798, 131, 1.13, -27.03, 0.58362, 133, -89.98, 42.79, 0.03825, 134, 8.93, 44.71, 0.35113, 135, 102.36, 32.05, 0.01902, 4, 130, -121.1, -68.76, 0.00023, 131, -6.51, -67.45, 0.02967, 133, -97.62, 2.36, 0.0028, 134, 1.29, 4.29, 0.9673, 5, 133, -111.49, -49.64, 0.00768, 134, -12.58, -47.71, 0.31655, 135, 80.85, -60.38, 0.02109, 136, -16.72, 27.31, 0.49645, 137, 55.11, 22.66, 0.15822, 3, 136, -11.05, -26.65, 0.54717, 137, 60.78, -31.3, 0.1137, 138, -13.1, 42.7, 0.33913, 3, 136, 23.03, -88.24, 0.0081, 138, 20.98, -18.9, 0.73185, 139, 13.92, 53.66, 0.26005, 2, 138, 17.67, -51.56, 0.28249, 139, 10.61, 20.99, 0.71751, 2, 138, 19.39, -34.54, 0.53716, 139, 12.34, 38.01, 0.46284, 3, 136, 5.86, -57.22, 0.1418, 137, 77.69, -61.87, 0.01163, 138, 3.82, 12.13, 0.84657, 1, 127, 522.12, -25.85, 1, 4, 131, -61.52, -27.71, 0.30246, 134, -53.72, 44.03, 0.23538, 135, 39.71, 31.36, 0.45749, 137, 13.97, 114.4, 0.00467, 5, 131, -66.97, -73.2, 0.03479, 134, -59.17, -1.46, 0.27941, 135, 34.26, -14.12, 0.51281, 136, -63.31, 73.57, 0.01144, 137, 8.52, 68.92, 0.16155, 5, 134, -54.66, -60.17, 0.07915, 135, 38.77, -72.84, 0.05351, 136, -58.8, 14.86, 0.11507, 137, 13.03, 10.2, 0.74553, 138, -60.85, 84.2, 0.00674, 3, 136, -41.66, -34.08, 0.2414, 137, 30.17, -38.74, 0.40796, 138, -43.71, 35.26, 0.35063, 4, 136, -14.71, -72.82, 0.03122, 137, 57.12, -77.47, 0.05321, 138, -16.76, -3.47, 0.89118, 139, -23.82, 69.08, 0.02439, 3, 137, 73.7, -98.59, 0.00157, 138, -0.18, -24.59, 0.74876, 139, -7.23, 47.96, 0.24967, 3, 137, 78.82, -114.68, 1e-05, 138, 4.94, -40.68, 0.48367, 139, -2.12, 31.87, 0.51632, 2, 138, 3.84, -53.24, 0.26351, 139, -3.22, 19.31, 0.73649, 2, 138, 26.54, -57.23, 0.23402, 139, 19.48, 15.32, 0.76598, 3, 136, 38.63, -102.34, 0.00296, 138, 36.59, -32.99, 0.54936, 139, 29.53, 39.56, 0.44768, 3, 136, 36.75, -81, 0.03516, 138, 34.7, -11.65, 0.74795, 139, 27.64, 60.9, 0.21689, 5, 133, -69.93, -125.63, 0.00032, 136, 24.84, -48.67, 0.28397, 137, 96.67, -53.33, 0.00104, 138, 22.79, 20.67, 0.69747, 139, 15.74, 93.23, 0.0172, 4, 133, -83.11, -102.56, 0.00335, 136, 11.66, -25.6, 0.6349, 137, 83.49, -30.26, 0.01011, 138, 9.61, 43.74, 0.35163, 4, 133, -80.54, -53.86, 0.08269, 134, 18.37, -51.93, 0.24685, 136, 14.23, 23.09, 0.66519, 137, 86.06, 18.44, 0.00528, 5, 130, -80.01, -84.5, 0.00238, 131, 34.57, -83.18, 0.03493, 133, -56.53, -13.37, 0.32829, 134, 42.38, -11.45, 0.45135, 136, 38.24, 63.58, 0.18305, 5, 130, -53.81, -33.97, 0.21663, 131, 60.78, -32.66, 0.24626, 133, -30.33, 37.15, 0.38238, 134, 68.58, 39.08, 0.14917, 136, 64.44, 114.11, 0.00556, 1, 127, 658.72, -29.91, 1], "hull": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 26, 28, 28, 30, 30, 32, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 0, 50, 52, 54, 54, 56, 56, 58, 58, 60, 64, 30, 22, 24, 24, 26, 32, 34, 34, 36, 62, 66, 66, 64, 18, 20, 20, 22, 60, 68, 68, 62, 36, 38, 38, 40, 46, 70, 70, 52, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 10, 104, 104, 52, 102, 104], "width": 289, "height": 449}}, "b1wd2": {"b1wd": {"x": 0.99, "y": 273.97, "width": 370, "height": 63}}, "a1t2": {"a1t2": {"x": 6.76, "y": 107.78, "width": 185, "height": 326}}, "c2hw": {"c2hw": {"x": 0.84, "y": 46.5, "width": 743, "height": 150}}, "c12l": {"c12l": {"x": 0.91, "y": 68.18, "width": 424, "height": 237}}, "b11l": {"b11l": {"x": -2.01, "y": 125.47, "width": 576, "height": 274}}, "a1t1": {"a1t1": {"x": -2.66, "y": 110.7, "width": 187, "height": 326}}, "c2people8": {"c2people2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-372.76, -591.98, -498.2, -591.98, -498.2, -439.66, -372.76, -439.66], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 112, "height": 136}}, "c2hw2": {"c2hw2": {"x": -226.76, "y": -26.47, "width": 738, "height": 460}}, "C103-0": {"C103-0": {"x": 5.31, "y": 405.16, "scaleX": 1.64, "scaleY": 1.64, "width": 512, "height": 512}}, "c33l": {"c33l": {"x": 0.19, "y": 76.87, "width": 324, "height": 195}}, "图层 31": {"图层 31": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-88.55, -13.64, 9.07, 139.45, 204.14, 15.06, 106.52, -138.03], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 62, "height": 79}}, "daizi": {"daizi": {"type": "mesh", "uvs": [0.83022, 0.00127, 0.83178, 0.01427, 0.62157, 0.09718, 0.53742, 0.13256, 0.43769, 0.18717, 0.37983, 0.21886, 0.311, 0.26352, 0.24047, 0.31282, 0.18628, 0.35748, 0.16181, 0.38307, 0.13817, 0.4078, 0.13495, 0.41724, 0.15698, 0.4709, 0.18163, 0.50269, 0.20632, 0.53452, 0.25757, 0.58052, 0.29937, 0.6132, 0.36978, 0.65717, 0.43467, 0.69254, 0.49937, 0.72217, 0.59189, 0.7564, 0.70091, 0.79673, 0.77134, 0.81766, 0.8501, 0.84005, 0.93855, 0.86805, 1, 0.89751, 1, 0.9024, 0.97665, 0.9171, 0.95736, 0.93957, 0.95344, 0.94762, 0.96244, 0.9859, 0.95334, 0.99943, 0.93672, 0.99775, 0.91634, 0.97795, 0.87968, 0.95379, 0.83925, 0.92714, 0.78724, 0.89972, 0.74253, 0.87614, 0.63552, 0.83221, 0.53941, 0.79275, 0.43794, 0.75109, 0.34276, 0.71201, 0.25534, 0.67612, 0.20642, 0.69829, 0.17061, 0.71452, 0.25263, 0.75183, 0.34028, 0.79169, 0.41686, 0.82653, 0.49517, 0.86215, 0.49496, 0.86436, 0.45526, 0.86625, 0.377, 0.84714, 0.29459, 0.82701, 0.25117, 0.83877, 0.22279, 0.83127, 0.17724, 0.80395, 0.11698, 0.7678, 0.05031, 0.70985, 0.0184, 0.65419, 0.00563, 0.61383, 0.00571, 0.57581, 0.0058, 0.53284, 0.01981, 0.48838, 0.02827, 0.46151, 0.02087, 0.43383, 0.02916, 0.42959, 0.02907, 0.38763, 0.04814, 0.34249, 0.07804, 0.30403, 0.10711, 0.27684, 0.19442, 0.22232, 0.26731, 0.18405, 0.41447, 0.12375, 0.56896, 0.05053, 0.61189, 0.05479, 0.78985, 0.00181], "triangles": [31, 32, 30, 32, 33, 30, 33, 29, 30, 33, 34, 29, 29, 34, 28, 28, 34, 35, 28, 35, 27, 35, 36, 27, 26, 27, 25, 25, 27, 24, 27, 36, 24, 36, 37, 24, 37, 23, 24, 37, 22, 23, 37, 38, 22, 38, 21, 22, 38, 39, 21, 39, 20, 21, 39, 40, 20, 40, 19, 20, 40, 41, 19, 19, 41, 18, 41, 17, 18, 49, 50, 48, 47, 48, 50, 50, 51, 47, 51, 52, 47, 53, 54, 52, 54, 55, 52, 52, 46, 47, 52, 55, 46, 55, 45, 46, 55, 56, 45, 56, 44, 45, 56, 57, 44, 44, 57, 43, 41, 42, 17, 57, 58, 43, 43, 58, 42, 42, 16, 17, 42, 58, 16, 58, 59, 16, 59, 15, 16, 59, 60, 15, 60, 14, 15, 60, 61, 14, 61, 13, 14, 61, 62, 13, 62, 12, 13, 62, 63, 12, 63, 65, 12, 65, 11, 12, 63, 64, 65, 11, 65, 10, 10, 65, 66, 10, 66, 9, 66, 67, 9, 9, 67, 8, 67, 68, 8, 8, 68, 7, 68, 69, 7, 7, 69, 6, 4, 72, 3, 3, 72, 2, 2, 73, 74, 2, 72, 73, 1, 74, 75, 1, 2, 74, 75, 0, 1, 69, 70, 6, 6, 70, 5, 70, 71, 5, 5, 71, 4, 71, 72, 4], "vertices": [1, 47, -10.39, -0.98, 1, 1, 47, -5.21, 3.82, 1, 1, 47, 52.3, 5.76, 1, 1, 47, 76.22, 7.32, 1, 2, 48, 10.35, 11.9, 0.90043, 47, 109.8, 13.64, 0.09957, 1, 48, 30.17, 11.83, 1, 2, 48, 57.02, 13.62, 0.75714, 49, -4.12, 14.57, 0.24286, 1, 49, 25.1, 12.2, 1, 2, 49, 51.01, 11.61, 0.98647, 50, -11.87, 19.24, 0.01353, 2, 49, 65.48, 12.33, 0.63962, 50, 1.31, 13.22, 0.36038, 2, 49, 79.46, 13.04, 0.05092, 50, 14.05, 7.4, 0.94908, 2, 49, 84.47, 14.24, 0.00436, 50, 19.04, 6.16, 0.99564, 1, 50, 48.37, 5.97, 1, 2, 50, 66.01, 7.81, 0.75386, 51, -2.41, 8.48, 0.24614, 1, 51, 15.26, 6.69, 1, 1, 51, 41.69, 6.61, 1, 1, 52, 9.41, 6.69, 1, 1, 52, 36.09, 7.93, 1, 2, 52, 58.12, 10.21, 0.02306, 53, 9.78, 8.48, 0.97694, 2, 52, 77.3, 13.74, 0.00012, 53, 29.28, 8.23, 0.99988, 3, 52, 100.7, 20.59, 0, 53, 53.56, 10.41, 0.05535, 54, 8.1, 9.68, 0.94465, 2, 54, 36.83, 9.5, 0.95728, 55, -9.9, 8.63, 0.04272, 2, 54, 53.29, 11.2, 0.05753, 55, 6.34, 11.83, 0.94247, 2, 55, 24.04, 15.74, 0.939, 56, -16.13, 16.32, 0.061, 2, 55, 45.18, 19.23, 0.2452, 56, 5.12, 19.06, 0.7548, 3, 55, 64.28, 18.5, 0.00192, 56, 24.18, 17.65, 0.90242, 57, -9.95, 14.68, 0.09566, 2, 56, 26.28, 16.03, 0.86702, 57, -7.33, 14.27, 0.13298, 2, 56, 30.16, 8.01, 0.5522, 57, -0.08, 9.08, 0.4478, 1, 57, 11.43, 3.92, 1, 1, 57, 15.64, 2.58, 1, 1, 57, 36.37, 0.86, 1, 1, 57, 43.37, -1.82, 1, 1, 57, 42.03, -4.48, 1, 1, 57, 30.89, -6.25, 1, 2, 56, 35.82, -17.26, 0.02929, 57, 16.98, -10.4, 0.97071, 2, 56, 20.15, -13.93, 0.64344, 57, 1.63, -14.98, 0.35656, 3, 55, 44.11, -11.8, 0.29378, 56, 2.95, -11.92, 0.70266, 57, -14.44, -21.44, 0.00356, 2, 55, 29.27, -10.6, 0.95991, 56, -11.84, -10.18, 0.04009, 2, 54, 44.07, -11.53, 0.58762, 55, -0.75, -11.65, 0.41238, 1, 54, 17.14, -9.99, 1, 1, 53, 35.99, -9.4, 1, 2, 52, 61.32, -8.44, 0.05997, 53, 9.3, -10.44, 0.94003, 1, 52, 37.45, -14.13, 1, 2, 51, 98.92, -22.97, 0.00109, 52, 44.99, -26.68, 0.99891, 2, 51, 105.16, -31.69, 0.00014, 52, 50.51, -35.86, 0.99986, 1, 52, 74.71, -31.34, 1, 1, 52, 100.55, -26.5, 1, 1, 52, 123.14, -22.27, 1, 1, 52, 146.23, -17.95, 1, 1, 52, 147.31, -18.47, 1, 1, 52, 145.47, -25.09, 1, 1, 52, 130.54, -33.06, 1, 1, 52, 114.82, -41.46, 1, 1, 52, 117.6, -50.84, 1, 1, 52, 111.91, -53.61, 1, 1, 52, 95.21, -54.66, 1, 1, 52, 73.12, -56.04, 1, 2, 51, 95.9, -50.23, 0.01099, 52, 39.79, -53.6, 0.98901, 2, 51, 65.64, -45.28, 0.19938, 52, 10.03, -46.24, 0.80062, 2, 51, 44.29, -40.02, 0.56743, 52, -10.82, -39.28, 0.43257, 3, 50, 101.21, -27.35, 0.004, 51, 24.88, -33.12, 0.88813, 52, -29.62, -30.84, 0.10786, 3, 50, 78.14, -24.19, 0.2438, 51, 2.94, -25.32, 0.75457, 52, -50.87, -21.3, 0.00163, 2, 50, 54.58, -18.56, 0.93794, 51, -18.98, -15, 0.06206, 1, 50, 40.35, -15.15, 1, 1, 50, 25.31, -14.38, 1, 1, 50, 23.23, -12.67, 1, 2, 49, 75.44, -8.21, 0.28372, 50, 0.69, -9.61, 0.71628, 1, 49, 51.3, -13.37, 1, 1, 49, 29.96, -15.56, 1, 2, 48, 80.59, -13.07, 0.01609, 49, 14.4, -15.84, 0.98391, 1, 48, 47.52, -14.77, 1, 1, 48, 23.33, -14.24, 1, 1, 47, 86.28, -11.75, 1, 1, 47, 38.96, -17.52, 1, 1, 47, 35.93, -10.44, 1, 1, 47, -5.67, -6.03, 1], "hull": 76, "edges": [0, 150, 0, 2, 2, 4, 4, 6, 10, 12, 12, 14, 14, 16, 20, 22, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 96, 98, 98, 100, 104, 106, 106, 108, 112, 114, 114, 116, 116, 118, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 38, 40, 40, 42, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 88, 90, 90, 92, 92, 94, 94, 96, 108, 110, 110, 112, 70, 72, 72, 74, 66, 68, 68, 70, 118, 120, 120, 122, 122, 124, 124, 126, 24, 26, 26, 28, 6, 8, 8, 10, 16, 18, 18, 20, 84, 86, 86, 88, 100, 102, 102, 104], "width": 171, "height": 542}}, "b1m": {"b1m": {"x": 0.49, "y": 52.97, "width": 145, "height": 117}}, "c2people7": {"c2people2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-201.76, -591.98, -327.2, -591.98, -327.2, -439.66, -201.76, -439.66], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 112, "height": 136}}, "c2tz2l": {"c2tz2l": {"x": 2.58, "y": 73.58, "width": 805, "height": 167}}, "qizi": {"qizi": {"type": "mesh", "uvs": [0.14217, 0, 0.10426, 0.20794, 0.06778, 0.40797, 0.02926, 0.61924, 0, 0.77972, 0, 0.84845, 0.1738, 1, 0.32246, 1, 0.45214, 1, 0.58499, 0.97135, 0.70992, 0.90886, 0.85858, 0.80888, 0.93291, 0.73598, 1, 0.6235, 1, 0.48811, 0.90919, 0.39854, 0.73997, 0.36938, 0.6609, 0.40479, 0.61187, 0.25274, 0.49959, 0.11526, 0.40628, 0.02778, 0.30489, 0, 0.92168, 0.54223, 0.81368, 0.59765, 0.68043, 0.65122, 0.55, 0.5921, 0.43358, 0.51821, 0.31577, 0.46648, 0.18673, 0.43692, 0.22881, 0.21894, 0.36486, 0.26698, 0.50231, 0.36488, 0.60329, 0.50528, 0.67623, 0.52375, 0.79264, 0.48496, 0.91326, 0.47203, 0.93851, 0.65307, 0.83191, 0.71403, 0.69586, 0.77868, 0.5514, 0.77129, 0.3887, 0.71771, 0.27229, 0.67707, 0.15167, 0.64567], "triangles": [6, 5, 42, 42, 4, 3, 6, 42, 41, 42, 5, 4, 28, 1, 29, 3, 2, 42, 42, 28, 41, 41, 28, 27, 42, 2, 28, 2, 1, 28, 27, 28, 29, 30, 27, 29, 29, 21, 30, 1, 0, 29, 29, 0, 21, 30, 19, 31, 30, 20, 19, 30, 21, 20, 40, 41, 26, 41, 27, 26, 26, 27, 30, 6, 41, 7, 7, 41, 40, 7, 40, 8, 8, 39, 9, 8, 40, 39, 31, 18, 17, 31, 19, 18, 39, 25, 24, 40, 26, 25, 25, 32, 24, 32, 33, 24, 32, 17, 33, 33, 16, 34, 33, 17, 16, 26, 31, 32, 31, 26, 30, 32, 31, 17, 25, 26, 32, 12, 36, 13, 9, 38, 10, 9, 39, 38, 11, 38, 37, 11, 10, 38, 11, 37, 12, 39, 24, 38, 38, 24, 37, 40, 25, 39, 12, 37, 36, 24, 23, 37, 37, 23, 36, 23, 22, 36, 36, 22, 13, 22, 14, 13, 22, 35, 14, 14, 35, 15, 24, 33, 23, 33, 34, 23, 22, 34, 35, 22, 23, 34, 34, 15, 35, 34, 16, 15], "vertices": [7, 84, -310.26, 116.17, 1e-05, 86, -256.41, -3.14, 0.00171, 81, -213.62, 138.78, 0.00076, 83, -147.44, -3.29, 0.02412, 80, -60.18, 26.81, 0.95747, 79, -126.36, 168.88, 0.01458, 78, -144.16, 286.32, 0.00135, 9, 87, -327.14, -35.46, 0.00038, 84, -284.86, 32.25, 0.0009, 86, -231.01, -87.07, 0.00768, 82, -206.02, 172.29, 0.00037, 81, -188.22, 54.85, 0.01065, 83, -122.04, -87.21, 0.04, 80, -34.78, -57.11, 0.44865, 79, -100.96, 84.96, 0.47594, 78, -118.76, 202.4, 0.01544, 10, 87, -302.71, -116.18, 0.0001, 84, -260.43, -48.48, 0.0016, 86, -206.58, -167.79, 0.00139, 85, -301.91, 53.1, 5e-05, 82, -181.59, 91.57, 0.00226, 81, -163.79, -25.87, 0.01537, 83, -97.61, -167.94, 0.00779, 80, -10.35, -137.83, 0.04623, 79, -76.52, 4.23, 0.89217, 78, -94.33, 121.67, 0.03305, 9, 84, -234.62, -133.74, 0.00087, 86, -180.77, -253.05, 7e-05, 85, -276.1, -32.16, 0.00066, 82, -155.78, 6.31, 0.0078, 81, -137.98, -111.13, 0.00665, 83, -71.8, -253.2, 0.00064, 80, 15.46, -223.1, 0.01055, 79, -50.72, -81.03, 0.21855, 78, -68.52, 36.41, 0.75421, 9, 84, -215.02, -198.51, 8e-05, 86, -161.16, -317.82, 0, 85, -256.5, -96.93, 0.00201, 82, -136.18, -58.46, 0.022, 81, -118.38, -175.9, 0.00259, 83, -52.2, -317.96, 0.0001, 80, 35.06, -287.86, 0.00561, 79, -31.11, -145.8, 0.04153, 78, -48.91, -28.36, 0.92607, 6, 85, -242.26, -121.25, 0.02504, 82, -121.94, -82.78, 0.0978, 81, -104.14, -200.22, 0.00045, 80, 49.3, -312.18, 0.00202, 79, -16.88, -170.12, 0.03743, 78, -34.68, -52.68, 0.83726, 7, 88, -198.83, -241.74, 0.00827, 84, -88.4, -229.04, 0.00036, 85, -129.88, -127.46, 0.17578, 82, -9.55, -88.99, 0.34885, 80, 161.69, -318.39, 4e-05, 79, 95.51, -176.33, 0.02097, 78, 77.71, -58.89, 0.44574, 7, 89, -137.36, -293.66, 2e-05, 88, -129.55, -201.18, 0.05265, 84, -19.12, -188.48, 0.00156, 85, -60.6, -86.9, 0.33921, 82, 59.72, -48.44, 0.37406, 79, 164.79, -135.77, 0.00152, 78, 146.99, -18.33, 0.23098, 7, 89, -76.92, -258.28, 0.00844, 88, -69.12, -165.81, 0.15767, 84, 41.32, -153.11, 0.00364, 85, -0.16, -51.53, 0.46343, 82, 120.16, -13.06, 0.28517, 79, 225.22, -100.4, 7e-05, 78, 207.42, 17.04, 0.08158, 6, 89, -20.95, -211.9, 0.03723, 88, -13.15, -119.43, 0.31712, 84, 97.29, -106.73, 0.00563, 85, 55.81, -5.15, 0.47079, 82, 176.13, 33.32, 0.15319, 78, 263.4, 63.42, 0.01604, 6, 89, 24.33, -155.71, 0.09666, 88, 32.14, -63.23, 0.47366, 84, 142.57, -50.53, 0.00626, 85, 101.09, 51.05, 0.36765, 82, 221.41, 89.51, 0.0549, 78, 308.68, 119.62, 0.00088, 5, 89, 72.9, -79.77, 0.18058, 88, 80.71, 12.7, 0.58119, 84, 191.14, 25.4, 0.00541, 85, 149.66, 126.98, 0.22284, 82, 269.98, 165.45, 0.00998, 5, 89, 92.44, -33.7, 0.30807, 88, 100.25, 58.77, 0.58991, 87, 168.4, 3.77, 0.00134, 84, 210.68, 71.47, 0.0033, 85, 169.2, 173.05, 0.09737, 6, 89, 100.41, 24.4, 0.50082, 88, 108.21, 116.87, 0.43754, 87, 176.37, 61.87, 0.03798, 84, 218.65, 129.57, 0.00117, 86, 272.5, 10.26, 0.00207, 85, 177.17, 231.15, 0.02043, 7, 89, 72.37, 72.3, 0.63389, 88, 80.17, 164.77, 0.18011, 87, 148.32, 109.77, 0.15576, 84, 190.6, 177.48, 0.00723, 86, 244.46, 58.16, 0.02238, 85, 149.12, 279.06, 0.0006, 81, 287.25, 200.08, 3e-05, 7, 89, 11.5, 79.22, 0.52576, 88, 19.3, 171.69, 0.03385, 87, 87.45, 116.69, 0.31043, 84, 129.73, 184.39, 0.00269, 86, 183.59, 65.08, 0.12573, 81, 226.38, 207, 0.0001, 83, 292.55, 64.94, 0.00145, 7, 89, -73.4, 43.38, 0.36363, 88, -65.6, 135.85, 0.00295, 87, 2.55, 80.85, 0.35031, 84, 44.83, 148.55, 0.00402, 86, 98.69, 29.24, 0.2436, 81, 141.48, 171.16, 0.00014, 83, 207.65, 29.09, 0.03535, 7, 89, -102.92, 9.28, 0.21552, 87, -26.96, 46.75, 0.29806, 84, 15.32, 114.45, 0.00392, 86, 69.17, -4.86, 0.35684, 81, 111.96, 137.06, 0.00014, 83, 178.14, -5.01, 0.12127, 80, 265.4, 25.1, 0.00425, 7, 89, -157.26, 49.7, 0.1009, 87, -81.3, 87.17, 0.1988, 84, -39.02, 154.88, 0.00313, 86, 14.83, 35.56, 0.39351, 81, 57.62, 177.48, 0.00011, 83, 123.8, 35.42, 0.27178, 80, 211.06, 65.52, 0.03178, 7, 89, -238.06, 67.71, 0.03081, 87, -162.11, 105.19, 0.09107, 84, -119.83, 172.89, 0.00154, 86, -65.97, 53.57, 0.33461, 81, -23.18, 195.49, 5e-05, 83, 42.99, 53.43, 0.4228, 80, 130.26, 83.53, 0.11911, 8, 89, -299.67, 73.21, 0.00439, 87, -223.71, 110.69, 0.02548, 84, -181.43, 178.39, 0.00051, 86, -127.58, 59.07, 0.19643, 81, -84.79, 200.99, 3e-05, 83, -18.61, 58.93, 0.4804, 80, 68.65, 89.03, 0.28237, 79, 2.48, 231.1, 0.01039, 8, 89, -352.67, 55.39, 1e-05, 87, -276.71, 92.86, 0.00112, 86, -180.58, 41.25, 0.07783, 81, -137.79, 183.17, 0.00107, 83, -71.61, 41.1, 0.39447, 80, 15.65, 71.2, 0.46922, 79, -50.53, 213.27, 0.05525, 78, -68.33, 330.71, 0.00103, 8, 89, 47.08, 31.79, 0.59801, 88, 54.88, 124.26, 0.19839, 87, 123.04, 69.26, 0.12205, 84, 165.31, 136.96, 0.07373, 86, 219.17, 17.65, 0.0043, 85, 123.83, 238.54, 0.00193, 81, 261.96, 159.57, 0.00159, 83, 328.14, 17.5, 1e-05, 11, 89, 8.23, -17.28, 0.42503, 88, 16.03, 75.19, 0.21819, 87, 84.18, 20.19, 0.14436, 84, 126.46, 87.89, 0.16406, 86, 180.32, -31.42, 0.01193, 85, 84.98, 189.47, 0.00295, 82, 205.31, 227.94, 9e-05, 81, 223.11, 110.5, 0.03179, 83, 289.28, -31.57, 0.00157, 80, 376.55, -1.46, 0, 79, 310.37, 140.6, 3e-05, 11, 89, -42.77, -72.59, 0.25482, 88, -34.97, 19.88, 0.1882, 87, 33.18, -35.12, 0.15282, 84, 75.46, 32.58, 0.25665, 86, 129.32, -86.73, 0.02542, 85, 33.98, 134.16, 0.0029, 82, 154.31, 172.63, 0.00022, 81, 172.11, 55.19, 0.09992, 83, 238.28, -86.87, 0.00755, 80, 325.55, -56.77, 0.0013, 79, 259.37, 85.29, 0.01021, 11, 89, -115.81, -87.26, 0.11463, 88, -108, 5.22, 0.13336, 87, -39.85, -49.78, 0.13169, 84, 2.43, 17.92, 0.29086, 86, 56.29, -101.39, 0.03996, 85, -39.05, 119.5, 0.00244, 82, 81.27, 157.97, 0.00039, 81, 99.08, 40.53, 0.20202, 83, 165.25, -101.54, 0.01808, 80, 252.52, -71.44, 0.00788, 79, 186.34, 70.63, 0.05869, 12, 89, -185.36, -92.87, 0.0352, 88, -177.56, -0.4, 0.06292, 87, -109.4, -55.4, 0.08228, 84, -67.12, 12.31, 0.25447, 86, -13.27, -107.01, 0.04497, 85, -108.61, 113.89, 0.0014, 82, 11.72, 152.35, 0.00045, 81, 29.52, 34.91, 0.28698, 83, 95.7, -107.15, 0.03053, 80, 182.96, -77.05, 0.02582, 79, 116.78, 65.02, 0.17361, 78, 98.98, 182.46, 0.00138, 12, 89, -250.98, -106.7, 0.00219, 88, -243.18, -14.23, 0.01952, 87, -175.02, -69.23, 0.03607, 84, -132.74, -1.53, 0.1521, 86, -78.89, -120.84, 0.03745, 85, -174.22, 100.05, 0.00066, 82, -53.9, 138.52, 0.00039, 81, -36.1, 21.08, 0.2912, 83, 30.08, -120.99, 0.03532, 80, 117.34, -90.89, 0.06947, 79, 51.16, 51.18, 0.33657, 78, 33.36, 168.62, 0.01905, 11, 88, -309.43, -38.98, 0.00054, 87, -241.28, -93.98, 0.00847, 84, -199, -26.27, 0.06043, 86, -145.14, -145.59, 0.02045, 85, -240.48, 75.31, 0.00016, 82, -120.16, 113.77, 0.00188, 81, -102.36, -3.67, 0.20308, 83, -36.18, -145.73, 0.03119, 80, 51.08, -115.63, 0.14902, 79, -15.09, 26.44, 0.45088, 78, -32.89, 143.88, 0.0739, 10, 89, -342.78, -42.84, 1e-05, 88, -334.97, 49.63, 2e-05, 87, -266.82, -5.37, 0.02808, 84, -224.54, 62.33, 0.01905, 86, -170.68, -56.98, 0.09613, 81, -127.9, 84.94, 0.05584, 83, -61.72, -57.12, 0.19064, 80, 25.54, -27.02, 0.40831, 79, -40.63, 115.04, 0.18789, 78, -58.43, 232.48, 0.01403, 10, 89, -269.43, -22.72, 0.0029, 88, -261.62, 69.75, 0.00013, 87, -193.47, 14.75, 0.11289, 84, -151.19, 82.45, 0.04482, 86, -97.33, -36.86, 0.18223, 81, -54.54, 105.06, 0.07756, 83, 11.63, -37, 0.21495, 80, 98.9, -6.9, 0.2611, 79, 32.72, 135.16, 0.10239, 78, 14.92, 252.6, 0.00103, 9, 89, -185.09, -19.87, 0.03635, 88, -177.29, 72.6, 0.00033, 87, -109.13, 17.6, 0.25964, 84, -66.85, 85.31, 0.07095, 86, -13, -34.01, 0.2202, 81, 29.79, 107.91, 0.07557, 83, 95.97, -34.15, 0.1806, 80, 183.23, -4.05, 0.11334, 79, 117.05, 138.02, 0.04303, 9, 89, -108.95, -41.99, 0.11933, 88, -101.15, 50.48, 0.00057, 87, -32.99, -4.52, 0.40872, 84, 9.29, 63.18, 0.07622, 86, 63.14, -56.13, 0.19731, 81, 105.93, 85.79, 0.05363, 83, 172.11, -56.28, 0.09859, 80, 259.37, -26.18, 0.03195, 79, 193.19, 115.89, 0.01368, 9, 89, -71.14, -28.64, 0.27851, 88, -63.33, 63.84, 0.00069, 87, 4.82, 8.84, 0.47177, 84, 47.1, 76.54, 0.06084, 86, 100.96, -42.77, 0.11976, 81, 143.75, 99.15, 0.02655, 83, 209.92, -42.92, 0.03667, 80, 297.19, -12.82, 0.00254, 79, 231.01, 129.25, 0.00267, 8, 89, -24.92, 16.85, 0.46551, 88, -17.12, 109.32, 0.00067, 87, 51.04, 54.32, 0.43923, 84, 93.32, 122.02, 0.03111, 86, 147.17, 2.71, 0.05089, 81, 189.96, 144.63, 0.00836, 83, 256.14, 2.56, 0.00416, 79, 277.23, 174.73, 7e-05, 6, 89, 28.62, 54.33, 0.58675, 88, 36.42, 146.8, 0.00058, 87, 104.57, 91.8, 0.38376, 84, 146.85, 159.5, 0.01428, 86, 200.71, 40.19, 0.0134, 81, 243.5, 182.11, 0.00124, 7, 89, 77.88, -2.84, 0.30369, 88, 85.68, 89.63, 0.60119, 87, 153.84, 34.63, 0.0015, 84, 196.12, 102.33, 0.03837, 85, 154.63, 203.91, 0.0542, 82, 274.96, 242.38, 0.00052, 81, 292.76, 124.94, 0.00053, 8, 89, 40.83, -53.49, 0.23219, 88, 48.63, 38.98, 0.56842, 87, 116.79, -16.02, 0.0022, 84, 159.07, 51.68, 0.07489, 85, 117.58, 153.26, 0.09222, 82, 237.91, 191.73, 0.0141, 81, 255.71, 74.29, 0.01584, 79, 342.97, 104.39, 0.00015, 9, 89, -9.18, -113.48, 0.12863, 88, -1.38, -21.01, 0.46438, 87, 66.77, -76.01, 0.00312, 84, 109.05, -8.31, 0.13441, 85, 67.57, 93.27, 0.14998, 82, 187.9, 131.74, 0.05256, 81, 205.7, 14.3, 0.05597, 79, 292.96, 44.4, 0.00788, 78, 275.16, 161.84, 0.00307, 9, 89, -78.04, -150.28, 0.0488, 88, -70.24, -57.8, 0.31727, 87, -2.08, -112.8, 0.00311, 84, 40.2, -45.1, 0.1647, 85, -1.28, 56.48, 0.17148, 82, 119.04, 94.95, 0.11361, 81, 136.84, -22.49, 0.11711, 79, 224.11, 7.61, 0.03974, 78, 206.3, 125.05, 0.02419, 10, 89, -164.96, -175.7, 0.0125, 88, -157.15, -83.23, 0.158, 87, -89, -138.23, 0.00232, 84, -46.72, -70.53, 0.15104, 85, -88.2, 31.05, 0.14806, 82, 32.12, 69.52, 0.16829, 81, 49.93, -47.92, 0.16652, 80, 203.37, -159.88, 4e-05, 79, 137.19, -17.82, 0.11075, 78, 119.39, 99.62, 0.08248, 9, 88, -219.82, -100.61, 0.05267, 87, -151.67, -155.61, 0.00109, 84, -109.39, -87.91, 0.09737, 85, -150.87, 13.67, 0.08904, 82, -30.54, 52.14, 0.17835, 81, -12.74, -65.3, 0.16974, 80, 140.7, -177.26, 0.0048, 79, 74.52, -35.2, 0.21717, 78, 56.72, 82.24, 0.18977, 11, 88, -282.54, -122.4, 0.00684, 87, -214.38, -177.4, 0.00032, 84, -172.11, -109.7, 0.04213, 86, -118.25, -229.01, 0, 85, -213.59, -8.12, 0.03604, 82, -93.26, 30.35, 0.12998, 81, -75.46, -87.09, 0.11905, 83, -9.28, -229.16, 0.00055, 80, 77.98, -199.05, 0.0293, 79, 11.8, -56.99, 0.31519, 78, -6, 60.45, 0.3206], "hull": 22, "edges": [8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 0, 42, 28, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 4, 0, 2, 2, 4, 2, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 4, 6, 6, 8, 84, 6], "width": 378, "height": 287}}, "b12l": {"b12l": {"x": -3.7, "y": 89.24, "width": 399, "height": 210}}, "qizi2": {"qizi2": {"x": 1056.73, "y": 685.42, "width": 184, "height": 356}}, "qizi3": {"qizi": {"type": "mesh", "uvs": [0.14217, 0, 0.10426, 0.20794, 0.06778, 0.40797, 0.02926, 0.61924, 0, 0.77972, 0, 0.84845, 0.1738, 1, 0.32246, 1, 0.45214, 1, 0.58499, 0.97135, 0.70992, 0.90886, 0.85858, 0.80888, 0.93291, 0.73598, 1, 0.6235, 1, 0.48811, 0.90919, 0.39854, 0.73997, 0.36938, 0.6609, 0.40479, 0.61187, 0.25274, 0.49959, 0.11526, 0.40628, 0.02778, 0.30489, 0, 0.92168, 0.54223, 0.81368, 0.59765, 0.68043, 0.65122, 0.55, 0.5921, 0.43358, 0.51821, 0.31577, 0.46648, 0.18673, 0.43692, 0.22881, 0.21894, 0.36486, 0.26698, 0.50231, 0.36488, 0.60329, 0.50528, 0.67623, 0.52375, 0.79264, 0.48496, 0.91326, 0.47203, 0.93851, 0.65307, 0.83191, 0.71403, 0.69586, 0.77868, 0.5514, 0.77129, 0.3887, 0.71771, 0.27229, 0.67707, 0.15167, 0.64567], "triangles": [6, 5, 42, 42, 4, 3, 6, 42, 41, 42, 5, 4, 28, 1, 29, 3, 2, 42, 42, 28, 41, 41, 28, 27, 42, 2, 28, 2, 1, 28, 27, 28, 29, 30, 27, 29, 29, 21, 30, 1, 0, 29, 29, 0, 21, 30, 19, 31, 30, 20, 19, 30, 21, 20, 40, 41, 26, 41, 27, 26, 26, 27, 30, 6, 41, 7, 7, 41, 40, 7, 40, 8, 8, 39, 9, 8, 40, 39, 31, 18, 17, 31, 19, 18, 39, 25, 24, 40, 26, 25, 25, 32, 24, 32, 33, 24, 32, 17, 33, 33, 16, 34, 33, 17, 16, 26, 31, 32, 31, 26, 30, 32, 31, 17, 25, 26, 32, 12, 36, 13, 9, 38, 10, 9, 39, 38, 11, 38, 37, 11, 10, 38, 11, 37, 12, 39, 24, 38, 38, 24, 37, 40, 25, 39, 12, 37, 36, 24, 23, 37, 37, 23, 36, 23, 22, 36, 36, 22, 13, 22, 14, 13, 22, 35, 14, 14, 35, 15, 24, 33, 23, 33, 34, 23, 22, 34, 35, 22, 23, 34, 34, 15, 35, 34, 16, 15], "vertices": [7, 98, -310.26, 116.17, 1e-05, 100, -256.41, -3.14, 0.00171, 95, -213.62, 138.78, 0.00076, 97, -147.44, -3.29, 0.02412, 94, -60.18, 26.81, 0.95747, 93, -126.36, 168.88, 0.01458, 92, -144.16, 286.32, 0.00135, 9, 101, -327.14, -35.46, 0.00038, 98, -284.86, 32.25, 0.0009, 100, -231.01, -87.07, 0.00768, 96, -206.02, 172.29, 0.00037, 95, -188.22, 54.85, 0.01065, 97, -122.04, -87.21, 0.04, 94, -34.78, -57.11, 0.44865, 93, -100.96, 84.96, 0.47594, 92, -118.76, 202.4, 0.01544, 10, 101, -302.71, -116.18, 0.0001, 98, -260.43, -48.48, 0.0016, 100, -206.58, -167.79, 0.00139, 99, -301.91, 53.1, 5e-05, 96, -181.59, 91.57, 0.00226, 95, -163.79, -25.87, 0.01537, 97, -97.61, -167.94, 0.00779, 94, -10.35, -137.83, 0.04623, 93, -76.52, 4.23, 0.89217, 92, -94.33, 121.67, 0.03305, 9, 98, -234.62, -133.74, 0.00087, 100, -180.77, -253.05, 7e-05, 99, -276.1, -32.16, 0.00066, 96, -155.78, 6.31, 0.0078, 95, -137.98, -111.13, 0.00665, 97, -71.8, -253.2, 0.00064, 94, 15.46, -223.1, 0.01055, 93, -50.72, -81.03, 0.21855, 92, -68.52, 36.41, 0.75421, 9, 98, -215.02, -198.51, 8e-05, 100, -161.16, -317.82, 0, 99, -256.5, -96.93, 0.00201, 96, -136.18, -58.46, 0.022, 95, -118.38, -175.9, 0.00259, 97, -52.2, -317.96, 0.0001, 94, 35.06, -287.86, 0.00561, 93, -31.11, -145.8, 0.04153, 92, -48.91, -28.36, 0.92607, 6, 99, -242.26, -121.25, 0.02504, 96, -121.94, -82.78, 0.0978, 95, -104.14, -200.22, 0.00045, 94, 49.3, -312.18, 0.00202, 93, -16.88, -170.12, 0.03743, 92, -34.68, -52.68, 0.83726, 7, 102, -198.83, -241.74, 0.00827, 98, -88.4, -229.04, 0.00036, 99, -129.88, -127.46, 0.17578, 96, -9.55, -88.99, 0.34885, 94, 161.69, -318.39, 4e-05, 93, 95.51, -176.33, 0.02097, 92, 77.71, -58.89, 0.44574, 7, 103, -137.36, -293.66, 2e-05, 102, -129.55, -201.18, 0.05265, 98, -19.12, -188.48, 0.00156, 99, -60.6, -86.9, 0.33921, 96, 59.72, -48.44, 0.37406, 93, 164.79, -135.77, 0.00152, 92, 146.99, -18.33, 0.23098, 7, 103, -76.92, -258.28, 0.00844, 102, -69.12, -165.81, 0.15767, 98, 41.32, -153.11, 0.00364, 99, -0.16, -51.53, 0.46343, 96, 120.16, -13.06, 0.28517, 93, 225.22, -100.4, 7e-05, 92, 207.42, 17.04, 0.08158, 6, 103, -20.95, -211.9, 0.03723, 102, -13.15, -119.43, 0.31712, 98, 97.29, -106.73, 0.00563, 99, 55.81, -5.15, 0.47079, 96, 176.13, 33.32, 0.15319, 92, 263.4, 63.42, 0.01604, 6, 103, 24.33, -155.71, 0.09666, 102, 32.14, -63.23, 0.47366, 98, 142.57, -50.53, 0.00626, 99, 101.09, 51.05, 0.36765, 96, 221.41, 89.51, 0.0549, 92, 308.68, 119.62, 0.00088, 5, 103, 72.9, -79.77, 0.18058, 102, 80.71, 12.7, 0.58119, 98, 191.14, 25.4, 0.00541, 99, 149.66, 126.98, 0.22284, 96, 269.98, 165.45, 0.00998, 5, 103, 92.44, -33.7, 0.30807, 102, 100.25, 58.77, 0.58991, 101, 168.4, 3.77, 0.00134, 98, 210.68, 71.47, 0.0033, 99, 169.2, 173.05, 0.09737, 6, 103, 100.41, 24.4, 0.50082, 102, 108.21, 116.87, 0.43754, 101, 176.37, 61.87, 0.03798, 98, 218.65, 129.57, 0.00117, 100, 272.5, 10.26, 0.00207, 99, 177.17, 231.15, 0.02043, 7, 103, 72.37, 72.3, 0.63389, 102, 80.17, 164.77, 0.18011, 101, 148.32, 109.77, 0.15576, 98, 190.6, 177.48, 0.00723, 100, 244.46, 58.16, 0.02238, 99, 149.12, 279.06, 0.0006, 95, 287.25, 200.08, 3e-05, 7, 103, 11.5, 79.22, 0.52576, 102, 19.3, 171.69, 0.03385, 101, 87.45, 116.69, 0.31043, 98, 129.73, 184.39, 0.00269, 100, 183.59, 65.08, 0.12573, 95, 226.38, 207, 0.0001, 97, 292.55, 64.94, 0.00145, 7, 103, -73.4, 43.38, 0.36363, 102, -65.6, 135.85, 0.00295, 101, 2.55, 80.85, 0.35031, 98, 44.83, 148.55, 0.00402, 100, 98.69, 29.24, 0.2436, 95, 141.48, 171.16, 0.00014, 97, 207.65, 29.09, 0.03535, 7, 103, -102.92, 9.28, 0.21552, 101, -26.96, 46.75, 0.29806, 98, 15.32, 114.45, 0.00392, 100, 69.17, -4.86, 0.35684, 95, 111.96, 137.06, 0.00014, 97, 178.14, -5.01, 0.12127, 94, 265.4, 25.1, 0.00425, 7, 103, -157.26, 49.7, 0.1009, 101, -81.3, 87.17, 0.1988, 98, -39.02, 154.88, 0.00313, 100, 14.83, 35.56, 0.39351, 95, 57.62, 177.48, 0.00011, 97, 123.8, 35.42, 0.27178, 94, 211.06, 65.52, 0.03178, 7, 103, -238.06, 67.71, 0.03081, 101, -162.11, 105.19, 0.09107, 98, -119.83, 172.89, 0.00154, 100, -65.97, 53.57, 0.33461, 95, -23.18, 195.49, 5e-05, 97, 42.99, 53.43, 0.4228, 94, 130.26, 83.53, 0.11911, 8, 103, -299.67, 73.21, 0.00439, 101, -223.71, 110.69, 0.02548, 98, -181.43, 178.39, 0.00051, 100, -127.58, 59.07, 0.19643, 95, -84.79, 200.99, 3e-05, 97, -18.61, 58.93, 0.4804, 94, 68.65, 89.03, 0.28237, 93, 2.48, 231.1, 0.01039, 8, 103, -352.67, 55.39, 1e-05, 101, -276.71, 92.86, 0.00112, 100, -180.58, 41.25, 0.07783, 95, -137.79, 183.17, 0.00107, 97, -71.61, 41.1, 0.39447, 94, 15.65, 71.2, 0.46922, 93, -50.53, 213.27, 0.05525, 92, -68.33, 330.71, 0.00103, 8, 103, 47.08, 31.79, 0.59801, 102, 54.88, 124.26, 0.19839, 101, 123.04, 69.26, 0.12205, 98, 165.31, 136.96, 0.07373, 100, 219.17, 17.65, 0.0043, 99, 123.83, 238.54, 0.00193, 95, 261.96, 159.57, 0.00159, 97, 328.14, 17.5, 1e-05, 11, 103, 8.23, -17.28, 0.42503, 102, 16.03, 75.19, 0.21819, 101, 84.18, 20.19, 0.14436, 98, 126.46, 87.89, 0.16406, 100, 180.32, -31.42, 0.01193, 99, 84.98, 189.47, 0.00295, 96, 205.31, 227.94, 9e-05, 95, 223.11, 110.5, 0.03179, 97, 289.28, -31.57, 0.00157, 94, 376.55, -1.46, 0, 93, 310.37, 140.6, 3e-05, 11, 103, -42.77, -72.59, 0.25482, 102, -34.97, 19.88, 0.1882, 101, 33.18, -35.12, 0.15282, 98, 75.46, 32.58, 0.25665, 100, 129.32, -86.73, 0.02542, 99, 33.98, 134.16, 0.0029, 96, 154.31, 172.63, 0.00022, 95, 172.11, 55.19, 0.09992, 97, 238.28, -86.87, 0.00755, 94, 325.55, -56.77, 0.0013, 93, 259.37, 85.29, 0.01021, 11, 103, -115.81, -87.26, 0.11463, 102, -108, 5.22, 0.13336, 101, -39.85, -49.78, 0.13169, 98, 2.43, 17.92, 0.29086, 100, 56.29, -101.39, 0.03996, 99, -39.05, 119.5, 0.00244, 96, 81.27, 157.97, 0.00039, 95, 99.08, 40.53, 0.20202, 97, 165.25, -101.54, 0.01808, 94, 252.52, -71.44, 0.00788, 93, 186.34, 70.63, 0.05869, 12, 103, -185.36, -92.87, 0.0352, 102, -177.56, -0.4, 0.06292, 101, -109.4, -55.4, 0.08228, 98, -67.12, 12.31, 0.25447, 100, -13.27, -107.01, 0.04497, 99, -108.61, 113.89, 0.0014, 96, 11.72, 152.35, 0.00045, 95, 29.52, 34.91, 0.28698, 97, 95.7, -107.15, 0.03053, 94, 182.96, -77.05, 0.02582, 93, 116.78, 65.02, 0.17361, 92, 98.98, 182.46, 0.00138, 12, 103, -250.98, -106.7, 0.00219, 102, -243.18, -14.23, 0.01952, 101, -175.02, -69.23, 0.03607, 98, -132.74, -1.53, 0.1521, 100, -78.89, -120.84, 0.03745, 99, -174.22, 100.05, 0.00066, 96, -53.9, 138.52, 0.00039, 95, -36.1, 21.08, 0.2912, 97, 30.08, -120.99, 0.03532, 94, 117.34, -90.89, 0.06947, 93, 51.16, 51.18, 0.33657, 92, 33.36, 168.62, 0.01905, 11, 102, -309.43, -38.98, 0.00054, 101, -241.28, -93.98, 0.00847, 98, -199, -26.27, 0.06043, 100, -145.14, -145.59, 0.02045, 99, -240.48, 75.31, 0.00016, 96, -120.16, 113.77, 0.00188, 95, -102.36, -3.67, 0.20308, 97, -36.18, -145.73, 0.03119, 94, 51.08, -115.63, 0.14902, 93, -15.09, 26.44, 0.45088, 92, -32.89, 143.88, 0.0739, 10, 103, -342.78, -42.84, 1e-05, 102, -334.97, 49.63, 2e-05, 101, -266.82, -5.37, 0.02808, 98, -224.54, 62.33, 0.01905, 100, -170.68, -56.98, 0.09613, 95, -127.9, 84.94, 0.05584, 97, -61.72, -57.12, 0.19064, 94, 25.54, -27.02, 0.40831, 93, -40.63, 115.04, 0.18789, 92, -58.43, 232.48, 0.01403, 10, 103, -269.43, -22.72, 0.0029, 102, -261.62, 69.75, 0.00013, 101, -193.47, 14.75, 0.11289, 98, -151.19, 82.45, 0.04482, 100, -97.33, -36.86, 0.18223, 95, -54.54, 105.06, 0.07756, 97, 11.63, -37, 0.21495, 94, 98.9, -6.9, 0.2611, 93, 32.72, 135.16, 0.10239, 92, 14.92, 252.6, 0.00103, 9, 103, -185.09, -19.87, 0.03635, 102, -177.29, 72.6, 0.00033, 101, -109.13, 17.6, 0.25964, 98, -66.85, 85.31, 0.07095, 100, -13, -34.01, 0.2202, 95, 29.79, 107.91, 0.07557, 97, 95.97, -34.15, 0.1806, 94, 183.23, -4.05, 0.11334, 93, 117.05, 138.02, 0.04303, 9, 103, -108.95, -41.99, 0.11933, 102, -101.15, 50.48, 0.00057, 101, -32.99, -4.52, 0.40872, 98, 9.29, 63.18, 0.07622, 100, 63.14, -56.13, 0.19731, 95, 105.93, 85.79, 0.05363, 97, 172.11, -56.28, 0.09859, 94, 259.37, -26.18, 0.03195, 93, 193.19, 115.89, 0.01368, 9, 103, -71.14, -28.64, 0.27851, 102, -63.33, 63.84, 0.00069, 101, 4.82, 8.84, 0.47177, 98, 47.1, 76.54, 0.06084, 100, 100.96, -42.77, 0.11976, 95, 143.75, 99.15, 0.02655, 97, 209.92, -42.92, 0.03667, 94, 297.19, -12.82, 0.00254, 93, 231.01, 129.25, 0.00267, 8, 103, -24.92, 16.85, 0.46551, 102, -17.12, 109.32, 0.00067, 101, 51.04, 54.32, 0.43923, 98, 93.32, 122.02, 0.03111, 100, 147.17, 2.71, 0.05089, 95, 189.96, 144.63, 0.00836, 97, 256.14, 2.56, 0.00416, 93, 277.23, 174.73, 7e-05, 6, 103, 28.62, 54.33, 0.58675, 102, 36.42, 146.8, 0.00058, 101, 104.57, 91.8, 0.38376, 98, 146.85, 159.5, 0.01428, 100, 200.71, 40.19, 0.0134, 95, 243.5, 182.11, 0.00124, 7, 103, 77.88, -2.84, 0.30369, 102, 85.68, 89.63, 0.60119, 101, 153.84, 34.63, 0.0015, 98, 196.12, 102.33, 0.03837, 99, 154.63, 203.91, 0.0542, 96, 274.96, 242.38, 0.00052, 95, 292.76, 124.94, 0.00053, 8, 103, 40.83, -53.49, 0.23219, 102, 48.63, 38.98, 0.56842, 101, 116.79, -16.02, 0.0022, 98, 159.07, 51.68, 0.07489, 99, 117.58, 153.26, 0.09222, 96, 237.91, 191.73, 0.0141, 95, 255.71, 74.29, 0.01584, 93, 342.97, 104.39, 0.00015, 9, 103, -9.18, -113.48, 0.12863, 102, -1.38, -21.01, 0.46438, 101, 66.77, -76.01, 0.00312, 98, 109.05, -8.31, 0.13441, 99, 67.57, 93.27, 0.14998, 96, 187.9, 131.74, 0.05256, 95, 205.7, 14.3, 0.05597, 93, 292.96, 44.4, 0.00788, 92, 275.16, 161.84, 0.00307, 9, 103, -78.04, -150.28, 0.0488, 102, -70.24, -57.8, 0.31727, 101, -2.08, -112.8, 0.00311, 98, 40.2, -45.1, 0.1647, 99, -1.28, 56.48, 0.17148, 96, 119.04, 94.95, 0.11361, 95, 136.84, -22.49, 0.11711, 93, 224.11, 7.61, 0.03974, 92, 206.3, 125.05, 0.02419, 10, 103, -164.96, -175.7, 0.0125, 102, -157.15, -83.23, 0.158, 101, -89, -138.23, 0.00232, 98, -46.72, -70.53, 0.15104, 99, -88.2, 31.05, 0.14806, 96, 32.12, 69.52, 0.16829, 95, 49.93, -47.92, 0.16652, 94, 203.37, -159.88, 4e-05, 93, 137.19, -17.82, 0.11075, 92, 119.39, 99.62, 0.08248, 9, 102, -219.82, -100.61, 0.05267, 101, -151.67, -155.61, 0.00109, 98, -109.39, -87.91, 0.09737, 99, -150.87, 13.67, 0.08904, 96, -30.54, 52.14, 0.17835, 95, -12.74, -65.3, 0.16974, 94, 140.7, -177.26, 0.0048, 93, 74.52, -35.2, 0.21717, 92, 56.72, 82.24, 0.18977, 11, 102, -282.54, -122.4, 0.00684, 101, -214.38, -177.4, 0.00032, 98, -172.11, -109.7, 0.04213, 100, -118.25, -229.01, 0, 99, -213.59, -8.12, 0.03604, 96, -93.26, 30.35, 0.12998, 95, -75.46, -87.09, 0.11905, 97, -9.28, -229.16, 0.00055, 94, 77.98, -199.05, 0.0293, 93, 11.8, -56.99, 0.31519, 92, -6, 60.45, 0.3206], "hull": 22, "edges": [8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 0, 42, 28, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 4, 0, 2, 2, 4, 2, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 4, 6, 6, 8, 84, 6], "width": 378, "height": 287}}, "a1st": {"a1st": {"x": 2.5, "y": 3.5, "width": 557, "height": 115}}, "c3people3": {"c3people3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [42.01, 136.51, -62.99, 136.51, -62.99, 195.51, 42.01, 195.51], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 105, "height": 59}}, "c3people4": {"c3people4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [75.5, 136.51, -36.5, 136.51, -36.5, 238.51, 75.5, 238.51], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 112, "height": 102}}, "daizi2": {"daizi": {"type": "mesh", "uvs": [0.83022, 0.00127, 0.83178, 0.01427, 0.62157, 0.09718, 0.53742, 0.13256, 0.43769, 0.18717, 0.37983, 0.21886, 0.311, 0.26352, 0.24047, 0.31282, 0.18628, 0.35748, 0.16181, 0.38307, 0.13817, 0.4078, 0.13495, 0.41724, 0.15698, 0.4709, 0.18163, 0.50269, 0.20632, 0.53452, 0.25757, 0.58052, 0.29937, 0.6132, 0.36978, 0.65717, 0.43467, 0.69254, 0.49937, 0.72217, 0.59189, 0.7564, 0.70091, 0.79673, 0.77134, 0.81766, 0.8501, 0.84005, 0.93855, 0.86805, 1, 0.89751, 1, 0.9024, 0.97665, 0.9171, 0.95736, 0.93957, 0.95344, 0.94762, 0.96244, 0.9859, 0.95334, 0.99943, 0.93672, 0.99775, 0.91634, 0.97795, 0.87968, 0.95379, 0.83925, 0.92714, 0.78724, 0.89972, 0.74253, 0.87614, 0.63552, 0.83221, 0.53941, 0.79275, 0.43794, 0.75109, 0.34276, 0.71201, 0.25534, 0.67612, 0.20642, 0.69829, 0.17061, 0.71452, 0.25263, 0.75183, 0.34028, 0.79169, 0.41686, 0.82653, 0.49517, 0.86215, 0.49496, 0.86436, 0.45526, 0.86625, 0.377, 0.84714, 0.29459, 0.82701, 0.25117, 0.83877, 0.22279, 0.83127, 0.17724, 0.80395, 0.11698, 0.7678, 0.05031, 0.70985, 0.0184, 0.65419, 0.00563, 0.61383, 0.00571, 0.57581, 0.0058, 0.53284, 0.01981, 0.48838, 0.02827, 0.46151, 0.02087, 0.43383, 0.02916, 0.42959, 0.02907, 0.38763, 0.04814, 0.34249, 0.07804, 0.30403, 0.10711, 0.27684, 0.19442, 0.22232, 0.26731, 0.18405, 0.41447, 0.12375, 0.56896, 0.05053, 0.61189, 0.05479, 0.78985, 0.00181], "triangles": [31, 32, 30, 32, 33, 30, 33, 29, 30, 33, 34, 29, 29, 34, 28, 28, 34, 35, 28, 35, 27, 35, 36, 27, 26, 27, 25, 25, 27, 24, 27, 36, 24, 36, 37, 24, 37, 23, 24, 37, 22, 23, 37, 38, 22, 38, 21, 22, 38, 39, 21, 39, 20, 21, 39, 40, 20, 40, 19, 20, 40, 41, 19, 19, 41, 18, 41, 17, 18, 49, 50, 48, 47, 48, 50, 50, 51, 47, 51, 52, 47, 53, 54, 52, 54, 55, 52, 52, 46, 47, 52, 55, 46, 55, 45, 46, 55, 56, 45, 56, 44, 45, 56, 57, 44, 44, 57, 43, 41, 42, 17, 57, 58, 43, 43, 58, 42, 42, 16, 17, 42, 58, 16, 58, 59, 16, 59, 15, 16, 59, 60, 15, 60, 14, 15, 60, 61, 14, 61, 13, 14, 61, 62, 13, 62, 12, 13, 62, 63, 12, 63, 65, 12, 65, 11, 12, 63, 64, 65, 11, 65, 10, 10, 65, 66, 10, 66, 9, 66, 67, 9, 9, 67, 8, 67, 68, 8, 8, 68, 7, 68, 69, 7, 7, 69, 6, 4, 72, 3, 3, 72, 2, 2, 73, 74, 2, 72, 73, 1, 74, 75, 1, 2, 74, 75, 0, 1, 69, 70, 6, 6, 70, 5, 70, 71, 5, 5, 71, 4, 71, 72, 4], "vertices": [1, 104, -10.39, -0.98, 1, 1, 104, -5.21, 3.82, 1, 1, 104, 52.3, 5.76, 1, 1, 104, 76.22, 7.32, 1, 2, 105, 10.35, 11.9, 0.90043, 104, 109.8, 13.64, 0.09957, 1, 105, 30.17, 11.83, 1, 2, 105, 57.02, 13.62, 0.75714, 106, -4.12, 14.57, 0.24286, 1, 106, 25.1, 12.2, 1, 2, 106, 51.01, 11.61, 0.98647, 107, -11.87, 19.24, 0.01353, 2, 106, 65.48, 12.33, 0.63962, 107, 1.31, 13.22, 0.36038, 2, 106, 79.46, 13.04, 0.05092, 107, 14.05, 7.4, 0.94908, 2, 106, 84.47, 14.24, 0.00436, 107, 19.04, 6.16, 0.99564, 1, 107, 48.37, 5.97, 1, 2, 107, 66.01, 7.81, 0.75386, 108, -2.41, 8.48, 0.24614, 1, 108, 15.26, 6.69, 1, 1, 108, 41.69, 6.61, 1, 1, 109, 9.41, 6.69, 1, 1, 109, 36.09, 7.93, 1, 2, 109, 58.12, 10.21, 0.02306, 110, 9.78, 8.48, 0.97694, 2, 109, 77.3, 13.74, 0.00012, 110, 29.28, 8.23, 0.99988, 3, 109, 100.7, 20.59, 0, 110, 53.56, 10.41, 0.05535, 111, 8.1, 9.68, 0.94465, 2, 111, 36.83, 9.5, 0.95728, 112, -9.9, 8.63, 0.04272, 2, 111, 53.29, 11.2, 0.05753, 112, 6.34, 11.83, 0.94247, 2, 112, 24.04, 15.74, 0.939, 113, -16.13, 16.32, 0.061, 2, 112, 45.18, 19.23, 0.2452, 113, 5.12, 19.06, 0.7548, 3, 112, 64.28, 18.5, 0.00192, 113, 24.18, 17.65, 0.90242, 114, -9.95, 14.68, 0.09566, 2, 113, 26.28, 16.03, 0.86702, 114, -7.33, 14.27, 0.13298, 2, 113, 30.16, 8.01, 0.5522, 114, -0.08, 9.08, 0.4478, 1, 114, 11.43, 3.92, 1, 1, 114, 15.64, 2.58, 1, 1, 114, 36.37, 0.86, 1, 1, 114, 43.37, -1.82, 1, 1, 114, 42.03, -4.48, 1, 1, 114, 30.89, -6.25, 1, 2, 113, 35.82, -17.26, 0.02929, 114, 16.98, -10.4, 0.97071, 2, 113, 20.15, -13.93, 0.64344, 114, 1.63, -14.98, 0.35656, 3, 112, 44.11, -11.8, 0.29378, 113, 2.95, -11.92, 0.70266, 114, -14.44, -21.44, 0.00356, 2, 112, 29.27, -10.6, 0.95991, 113, -11.84, -10.18, 0.04009, 2, 111, 44.07, -11.53, 0.58762, 112, -0.75, -11.65, 0.41238, 1, 111, 17.14, -9.99, 1, 1, 110, 35.99, -9.4, 1, 2, 109, 61.32, -8.44, 0.05997, 110, 9.3, -10.44, 0.94003, 1, 109, 37.45, -14.13, 1, 2, 108, 98.92, -22.97, 0.00109, 109, 44.99, -26.68, 0.99891, 2, 108, 105.16, -31.69, 0.00014, 109, 50.51, -35.86, 0.99986, 1, 109, 74.71, -31.34, 1, 1, 109, 100.55, -26.5, 1, 1, 109, 123.14, -22.27, 1, 1, 109, 146.23, -17.95, 1, 1, 109, 147.31, -18.47, 1, 1, 109, 145.47, -25.09, 1, 1, 109, 130.54, -33.06, 1, 1, 109, 114.82, -41.46, 1, 1, 109, 117.6, -50.84, 1, 1, 109, 111.91, -53.61, 1, 1, 109, 95.21, -54.66, 1, 1, 109, 73.12, -56.04, 1, 2, 108, 95.9, -50.23, 0.01099, 109, 39.79, -53.6, 0.98901, 2, 108, 65.64, -45.28, 0.19938, 109, 10.03, -46.24, 0.80062, 2, 108, 44.29, -40.02, 0.56743, 109, -10.82, -39.28, 0.43257, 3, 107, 101.21, -27.35, 0.004, 108, 24.88, -33.12, 0.88813, 109, -29.62, -30.84, 0.10786, 3, 107, 78.14, -24.19, 0.2438, 108, 2.94, -25.32, 0.75457, 109, -50.87, -21.3, 0.00163, 2, 107, 54.58, -18.56, 0.93794, 108, -18.98, -15, 0.06206, 1, 107, 40.35, -15.15, 1, 1, 107, 25.31, -14.38, 1, 1, 107, 23.23, -12.67, 1, 2, 106, 75.44, -8.21, 0.28372, 107, 0.69, -9.61, 0.71628, 1, 106, 51.3, -13.37, 1, 1, 106, 29.96, -15.56, 1, 2, 105, 80.59, -13.07, 0.01609, 106, 14.4, -15.84, 0.98391, 1, 105, 47.52, -14.77, 1, 1, 105, 23.33, -14.24, 1, 1, 104, 86.28, -11.75, 1, 1, 104, 38.96, -17.52, 1, 1, 104, 35.93, -10.44, 1, 1, 104, -5.67, -6.03, 1], "hull": 76, "edges": [0, 150, 0, 2, 2, 4, 4, 6, 10, 12, 12, 14, 14, 16, 20, 22, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 96, 98, 98, 100, 104, 106, 106, 108, 112, 114, 114, 116, 116, 118, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 38, 40, 40, 42, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 88, 90, 90, 92, 92, 94, 94, 96, 108, 110, 110, 112, 70, 72, 72, 74, 66, 68, 68, 70, 118, 120, 120, 122, 122, 124, 124, 126, 24, 26, 26, 28, 6, 8, 8, 10, 16, 18, 18, 20, 84, 86, 86, 88, 100, 102, 102, 104], "width": 171, "height": 542}}, "b2wd": {"b2wd": {"x": 0.8, "y": 189.24, "width": 258, "height": 64}}, "b2wl": {"b2wl": {"x": -0.2, "y": 27.74, "width": 288, "height": 55}}, "图层 33": {"图层 31": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-88.55, -13.64, 9.07, 139.45, 204.14, 15.06, 106.52, -138.03], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 62, "height": 79}}, "b2hl": {"b2hl": {"x": 0.6, "y": 6.22, "width": 493, "height": 166}}, "a11l": {"a11l": {"x": -1, "y": 61.8, "width": 396, "height": 228}}, "a12l": {"a12l": {"x": -1, "y": 89.82, "width": 370, "height": 227}}, "图层 34": {"图层 32": {"type": "mesh", "uvs": [0.9994, 0.01233, 0.90156, 0.38888, 0.80828, 0.74788, 0.59967, 0.86961, 0.37621, 1, 0.25575, 1, 0.02329, 0.84084, 0.02301, 0.72636, 0.25359, 0.3519, 0.74074, 0.01929], "triangles": [8, 9, 1, 1, 9, 0, 3, 4, 6, 4, 5, 6, 8, 2, 3, 3, 6, 7, 3, 7, 8, 2, 8, 1], "vertices": [1, 41, 92.14, -7.39, 1, 2, 40, 100.5, -29.09, 0.10611, 41, 32.1, -29.09, 0.89389, 2, 40, 43.26, -49.78, 0.94619, 41, -25.14, -49.78, 0.05381, 2, 40, 13.76, -39.28, 0.99986, 41, -54.64, -39.28, 0.00014, 1, 40, -17.84, -28.04, 1, 1, 40, -24.71, -16.12, 1, 1, 40, -14.95, 20.15, 1, 1, 40, 1.59, 29.72, 1, 2, 40, 68.9, 38.11, 0.61237, 41, 0.5, 38.11, 0.38763, 1, 41, 76.39, 17.63, 1], "hull": 10, "edges": [0, 18, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 2, 2, 4, 4, 6, 6, 8], "width": 39, "height": 57}}, "图层 35": {"图层 30": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-22.02, -34.77, -32.5, 22.86, 114.45, 49.58, 124.92, -8.05], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 20, "height": 51}}, "c3tz2_1": {"c3tz2_1": {"x": 0.5, "y": 135.51, "width": 202, "height": 286}}, "c3tz2_2": {"c3tz2_2": {"x": 5.01, "y": 135.51, "width": 202, "height": 286}}, "c1lang": {"c1lang": {"x": 0.91, "y": 23.68, "width": 250, "height": 56}}, "c2people1": {"c2people1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [102.79, -47.06, -9.21, -47.06, -9.21, 88.94, 102.79, 88.94], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 112, "height": 136}}, "c2people2": {"c2people2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [7.79, -49.06, -104.21, -49.06, -104.21, 86.94, 7.79, 86.94], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 112, "height": 136}}, "c2people3": {"c2people1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [94.52, 29.64, -30.92, 29.64, -30.92, 181.96, 94.52, 181.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 112, "height": 136}}, "c2people4": {"c2people1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [173.72, 29.64, 48.28, 29.64, 48.28, 181.96, 173.72, 181.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 112, "height": 136}}, "c2people5": {"c2people1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [257.72, 29.64, 132.28, 29.64, 132.28, 181.96, 257.72, 181.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 112, "height": 136}}, "c2people6": {"c2people2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-286.59, -591.98, -412.03, -591.98, -412.03, -439.66, -286.59, -439.66], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 112, "height": 136}}, "图层 30": {"图层 30": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-22.02, -34.77, -32.5, 22.86, 114.45, 49.58, 124.92, -8.05], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 20, "height": 51}}, "b2syhl": {"b2syhl": {"x": 9.4, "y": 46.33, "width": 652, "height": 214}}, "图层 32": {"图层 32": {"type": "mesh", "uvs": [0.9994, 0.01233, 0.90156, 0.38888, 0.80828, 0.74788, 0.59967, 0.86961, 0.37621, 1, 0.25575, 1, 0.02329, 0.84084, 0.02301, 0.72636, 0.25359, 0.3519, 0.74074, 0.01929], "triangles": [8, 9, 1, 1, 9, 0, 3, 4, 6, 4, 5, 6, 8, 2, 3, 3, 6, 7, 3, 7, 8, 2, 8, 1], "vertices": [1, 33, 92.14, -7.39, 1, 2, 32, 100.5, -29.09, 0.10611, 33, 32.1, -29.09, 0.89389, 2, 32, 43.26, -49.78, 0.94619, 33, -25.14, -49.78, 0.05381, 2, 32, 13.76, -39.28, 0.99986, 33, -54.64, -39.28, 0.00014, 1, 32, -17.84, -28.04, 1, 1, 32, -24.71, -16.12, 1, 1, 32, -14.95, 20.15, 1, 1, 32, 1.59, 29.72, 1, 2, 32, 68.9, 38.11, 0.61237, 33, 0.5, 38.11, 0.38763, 1, 33, 76.39, 17.63, 1], "hull": 10, "edges": [0, 18, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 2, 2, 4, 4, 6, 6, 8], "width": 39, "height": 57}}, "b1tz1": {"b1tz1": {"x": -5.59, "y": 114.2, "width": 186, "height": 339}}, "c1tz1": {"c1tz1": {"x": -5.59, "y": 133.53, "width": 255, "height": 294}}, "c1tz2": {"c1tz2": {"x": 6.46, "y": 133.53, "width": 255, "height": 294}}}}], "animations": {"a0-1": {"slots": {"qizi": {"attachment": [{"name": null}]}, "qq3": {"attachment": [{"name": null}]}, "c3people4": {"attachment": [{"name": null}]}, "ganzi2": {"attachment": [{"name": null}]}, "qq1": {"attachment": [{"name": null}]}, "qq2": {"attachment": [{"name": null}]}, "daizi2": {"attachment": [{"name": null}]}, "qq4": {"attachment": [{"name": null}]}, "c2people1": {"attachment": [{"name": null}]}, "c2people2": {"attachment": [{"name": null}]}, "c3people3": {"attachment": [{"name": null}]}, "daizi": {"attachment": [{"name": null}]}, "qizi3": {"attachment": [{"name": null}]}, "ganzi": {"attachment": [{"name": null}]}}, "bones": {"lv21": {"rotate": [{"angle": 0.03}]}}}, "a01": {"slots": {"qizi": {"attachment": [{"name": null}]}, "a1t2": {"attachment": [{"name": "a1t2"}]}, "qq3": {"attachment": [{"name": null}]}, "c3people4": {"attachment": [{"name": null}]}, "ganzi2": {"attachment": [{"name": null}]}, "qq1": {"attachment": [{"name": null}]}, "qq2": {"attachment": [{"name": null}]}, "daizi2": {"attachment": [{"name": null}]}, "qq4": {"attachment": [{"name": null}]}, "a11l": {"attachment": [{"name": "a11l"}]}, "a1t1": {"attachment": [{"name": "a1t1"}]}, "daizi": {"attachment": [{"name": null}]}, "a12l": {"attachment": [{"name": "a12l"}]}, "a1st": {"attachment": [{"name": "a1st"}]}, "c2people1": {"attachment": [{"name": null}]}, "c2people2": {"attachment": [{"name": null}]}, "c3people3": {"attachment": [{"name": null}]}, "C103-0": {"attachment": [{"name": null}]}, "qizi3": {"attachment": [{"name": null}]}, "ganzi": {"attachment": [{"name": null}]}}, "bones": {"lv1": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 233.31, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}], "scale": [{"x": 0.548, "y": 0.247, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "1j5": {"translate": [{"y": -284.26, "curve": "stepped"}, {"time": 0.3, "y": -284.26, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": 304.02, "curve": 0.25, "c3": 0.75}, {"time": 1.0333}], "scale": [{"x": 0.289, "y": 0.01, "curve": "stepped"}, {"time": 0.3, "x": 0.289, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 1.0333}]}, "1j4": {"translate": [{"y": -130.85, "curve": "stepped"}, {"time": 0.1333, "y": -130.85, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "y": 128.44, "curve": 0.25, "c3": 0.75}, {"time": 0.9}], "scale": [{"x": 0.368, "y": 0.01, "curve": "stepped"}, {"time": 0.1333, "x": 0.368, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 0.9}]}, "1j3": {"translate": [{"y": -218.83, "curve": "stepped"}, {"time": 0.4333, "y": -218.83, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "y": 303.13, "curve": 0.25, "c3": 0.75}, {"time": 1.2}], "scale": [{"x": 0.289, "y": 0.01, "curve": "stepped"}, {"time": 0.4333, "x": 0.289, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 1.2}]}, "1j2": {"translate": [{"y": -218.83, "curve": "stepped"}, {"time": 0.4333, "y": -218.83, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "y": 303.13, "curve": 0.25, "c3": 0.75}, {"time": 1.2}], "scale": [{"x": 0.289, "y": 0.01, "curve": "stepped"}, {"time": 0.4333, "x": 0.289, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 1.2}]}, "1j1": {"translate": [{"y": -89.11, "curve": "stepped"}, {"time": 0.6, "y": -89.11, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "y": 259.88, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}], "scale": [{"x": 0.052, "y": 0.03, "curve": "stepped"}, {"time": 0.6, "x": 0.052, "y": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}}}, "a01-1": {"slots": {"qizi": {"attachment": [{"name": null}]}, "a1t2": {"attachment": [{"name": "a1t2"}]}, "qq3": {"attachment": [{"name": null}]}, "c3people4": {"attachment": [{"name": null}]}, "ganzi2": {"attachment": [{"name": null}]}, "qq1": {"attachment": [{"name": null}]}, "qq2": {"attachment": [{"name": null}]}, "c2people3": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 4.1667, "color": "ffffff00"}, {"time": 4.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 7.1667, "color": "ffffffff"}, {"time": 7.5, "color": "ffffff00"}], "attachment": [{"name": "c2people1"}]}, "daizi2": {"attachment": [{"name": null}]}, "qq4": {"attachment": [{"name": null}]}, "a11l": {"attachment": [{"name": "a11l"}]}, "a1t1": {"attachment": [{"name": "a1t1"}]}, "daizi": {"attachment": [{"name": null}]}, "a12l": {"attachment": [{"name": "a12l"}]}, "a1st": {"attachment": [{"name": "a1st"}]}, "c2people1": {"attachment": [{"name": null}]}, "c2people4": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 4.1667, "color": "ffffff00"}, {"time": 4.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 7.1667, "color": "ffffffff"}, {"time": 7.5, "color": "ffffff00"}]}, "c2people2": {"attachment": [{"name": null}]}, "c2people5": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 4.1667, "color": "ffffff00"}, {"time": 4.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 7.1667, "color": "ffffffff"}, {"time": 7.5, "color": "ffffff00"}]}, "c3people3": {"attachment": [{"name": null}]}, "C103-0": {"attachment": [{"name": null}]}, "qizi3": {"attachment": [{"name": null}]}, "ganzi": {"attachment": [{"name": null}]}}, "bones": {"bone3": {"translate": [{"time": 4.1667, "x": 231.21}, {"time": 4.5, "x": 164.94, "y": 8}, {"time": 4.8667, "x": 92.05}, {"time": 5.2333, "x": 22.48, "y": 4}, {"time": 5.6, "x": -47.1}, {"time": 5.9, "x": -106.74, "y": 4}, {"time": 6.3, "x": -186.25}, {"time": 6.6333, "x": -250.68, "y": 4}, {"time": 6.9, "x": -302.21}, {"time": 7.2, "x": -360.19, "y": 4}, {"time": 7.5, "x": -418.18}]}}, "drawOrder": [{"offsets": [{"slot": "c2people2", "offset": -4}]}]}, "a02": {"slots": {"qizi": {"attachment": [{"name": null}]}, "a1t1": {"attachment": [{"name": "a1t1"}]}, "qq3": {"attachment": [{"name": null}]}, "c3people4": {"attachment": [{"name": null}]}, "ganzi2": {"attachment": [{"name": null}]}, "qq1": {"attachment": [{"name": null}]}, "qq2": {"attachment": [{"name": null}]}, "qq4": {"attachment": [{"name": null}]}, "daizi2": {"attachment": [{"name": null}]}, "daizi": {"attachment": [{"name": null}]}, "a1t2": {"attachment": [{"name": "a1t2"}]}, "a1st": {"attachment": [{"name": "a1st"}]}, "b1wd2": {"attachment": [{"name": "b1wd"}]}, "c2people1": {"attachment": [{"name": null}]}, "b1m": {"attachment": [{"name": "b1m"}]}, "b11l": {"attachment": [{"name": "b11l"}]}, "b1tz1": {"attachment": [{"name": "b1tz1"}]}, "c2people2": {"attachment": [{"name": null}]}, "c3people3": {"attachment": [{"name": null}]}, "b12l": {"attachment": [{"name": "b12l"}]}, "C103-0": {"attachment": [{"name": null}]}, "qizi3": {"attachment": [{"name": null}]}, "ganzi": {"attachment": [{"name": null}]}, "b1tz2": {"attachment": [{"name": "b1tz2"}]}}, "bones": {"1j1": {"translate": [{"y": -227.84, "curve": "stepped"}, {"time": 0.6, "y": -227.84, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "y": 417.84, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}], "scale": [{"x": 0.203, "y": 0.01, "curve": "stepped"}, {"time": 0.6, "x": 0.203, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "1j2": {"translate": [{"x": -26.38, "y": -227.84, "curve": "stepped"}, {"time": 0.4333, "x": -26.38, "y": -227.84, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -26.38, "y": 461.25, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": -26.38}], "scale": [{"x": 0.203, "y": 0.01, "curve": "stepped"}, {"time": 0.4333, "x": 0.203, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 1.2}]}, "1j3": {"translate": [{"x": 36.27, "y": -227.84, "curve": "stepped"}, {"time": 0.4333, "x": 36.27, "y": -227.84, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 36.27, "y": 461.25, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": 36.27}], "scale": [{"x": 0.203, "y": 0.01, "curve": "stepped"}, {"time": 0.4333, "x": 0.203, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 1.2}]}, "lv7": {"translate": [{"y": -227.84, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 5.65, "y": 386.78, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}], "scale": [{"x": 0.203, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "lv8": {"translate": [{"y": -227.84, "curve": "stepped"}, {"time": 0.1333, "y": -227.84, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "y": 338.08, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}], "scale": [{"x": 0.203, "y": 0.01, "curve": "stepped"}, {"time": 0.1333, "x": 0.203, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}]}, "lv2": {"translate": [{"curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 0.2333, "y": 274.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.4333}], "scale": [{"x": 0.106, "y": 0.106, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "lv1": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5333, "y": 296.31, "curve": 0.25, "c3": 0.75}, {"time": 1.1}], "scale": [{"x": 0.106, "y": 0.106, "curve": 0.25, "c3": 0.75}, {"time": 1.1}]}}}, "a02-1": {"slots": {"c2people6": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.9667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "color": "ffffff00"}], "attachment": [{"name": "c2people2"}]}, "qq3": {"attachment": [{"name": null}]}, "a1t2": {"attachment": [{"name": "a1t2"}]}, "c3people4": {"attachment": [{"name": null}]}, "ganzi2": {"attachment": [{"name": null}]}, "qq1": {"attachment": [{"name": null}]}, "qq2": {"attachment": [{"name": null}]}, "b2wl": {"attachment": [{"name": "b2wl"}]}, "c2people4": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 5.8333, "color": "ffffff00"}, {"time": 6.3, "color": "ffffffff", "curve": "stepped"}, {"time": 8.8333, "color": "ffffffff"}, {"time": 9.1667, "color": "ffffff00"}]}, "daizi2": {"attachment": [{"name": null}]}, "qq4": {"attachment": [{"name": null}]}, "a1t1": {"attachment": [{"name": "a1t1"}]}, "daizi": {"attachment": [{"name": null}]}, "qizi": {"attachment": [{"name": null}]}, "a1st": {"attachment": [{"name": "a1st"}]}, "b1wd2": {"attachment": [{"name": "b1wd"}]}, "c2people8": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.9667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "color": "ffffff00"}]}, "c2people1": {"attachment": [{"name": null}]}, "b1m": {"attachment": [{"name": "b1m"}]}, "b11l": {"attachment": [{"name": "b11l"}]}, "b1tz1": {"attachment": [{"name": "b1tz1"}]}, "c2people2": {"attachment": [{"name": null}]}, "c2people5": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 5.8333, "color": "ffffff00"}, {"time": 6.3, "color": "ffffffff", "curve": "stepped"}, {"time": 8.8333, "color": "ffffffff"}, {"time": 9.1667, "color": "ffffff00"}]}, "c3people3": {"attachment": [{"name": null}]}, "b12l": {"attachment": [{"name": "b12l"}]}, "C103-0": {"attachment": [{"name": null}]}, "qizi3": {"attachment": [{"name": null}]}, "c2people7": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.9667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "color": "ffffff00"}]}, "ganzi": {"attachment": [{"name": null}]}, "c2people3": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 5.8333, "color": "ffffff00"}, {"time": 6.3, "color": "ffffffff", "curve": "stepped"}, {"time": 8.8333, "color": "ffffffff"}, {"time": 9.1667, "color": "ffffff00"}], "attachment": [{"name": "c2people1"}]}, "b1tz2": {"attachment": [{"name": "b1tz2"}]}}, "bones": {"1j2": {"translate": [{"x": -26.38}]}, "1j3": {"translate": [{"x": 36.27}]}, "图层 45": {"shear": [{"y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.3333, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.6667, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.6667, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.3333, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.6667, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 9.3333, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 10, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.6667, "y": -21}]}, "图层 46": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 4.1, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "curve": 0.25, "c3": 0.75}, {"time": 4.3667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 4.6, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 5.6, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.7667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 5.8667, "curve": 0.25, "c3": 0.75}, {"time": 5.9333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 6.2333, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 6.6, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 6.9333, "curve": 0.25, "c3": 0.75}, {"time": 7.0333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 7.1, "curve": 0.25, "c3": 0.75}, {"time": 7.2, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 7.5667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.7667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.9333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.1, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 8.2667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 8.4333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "curve": 0.25, "c3": 0.75}, {"time": 8.6, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 8.7333, "curve": 0.25, "c3": 0.75}, {"time": 8.8333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 9, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 9.0667, "curve": 0.25, "c3": 0.75}, {"time": 9.1667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 9.2667, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 9.4333, "curve": 0.25, "c3": 0.75}, {"time": 9.5, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 9.7667, "curve": 0.25, "c3": 0.75}, {"time": 9.8667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 9.9333, "curve": 0.25, "c3": 0.75}, {"time": 10.0333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 10.1667, "curve": 0.25, "c3": 0.75}, {"time": 10.2333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "curve": 0.25, "c3": 0.75}, {"time": 10.4333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "curve": 0.25, "c3": 0.75}, {"time": 10.6, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "图层 38": {"translate": [{"time": 0.4, "curve": 0.674, "c3": 0.358}, {"time": 0.9333, "x": -86.29, "y": 36.87, "curve": "stepped"}, {"time": 1.3667, "x": -86.29, "y": 36.87, "curve": 0.839, "c3": 0.332}, {"time": 2, "x": 9.69, "y": 68.2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": "stepped"}, {"time": 3.0667, "curve": 0.674, "c3": 0.358}, {"time": 3.6, "x": -103.44, "y": 26.58}, {"time": 4.0333, "x": -86.29, "y": 36.87, "curve": 0.839, "c3": 0.332}, {"time": 4.6667, "x": 23.78, "y": -92.53, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": "stepped"}, {"time": 5.7333, "curve": 0.674, "c3": 0.358}, {"time": 6.2667, "x": -86.29, "y": 36.87, "curve": "stepped"}, {"time": 6.7, "x": -86.29, "y": 36.87, "curve": 0.839, "c3": 0.332}, {"time": 7.3333, "x": -133.04, "y": 97.67}, {"time": 8, "curve": "stepped"}, {"time": 8.4, "curve": 0.674, "c3": 0.358}, {"time": 8.9333, "x": -121.28, "y": 13.55, "curve": 0.25, "c3": 0.75}, {"time": 9.3667, "x": -86.29, "y": 36.87, "curve": 0.839, "c3": 0.332}, {"time": 10, "x": -150.48, "y": 38.54, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "图层 44": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 4.1, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "curve": 0.25, "c3": 0.75}, {"time": 4.3667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 4.6, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 5.6, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.7667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 5.8667, "curve": 0.25, "c3": 0.75}, {"time": 5.9333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 6.2333, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 6.6, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 6.9333, "curve": 0.25, "c3": 0.75}, {"time": 7.0333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 7.1, "curve": 0.25, "c3": 0.75}, {"time": 7.2, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 7.5667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.7667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.9333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.1, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 8.2667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 8.4333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "curve": 0.25, "c3": 0.75}, {"time": 8.6, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 8.7333, "curve": 0.25, "c3": 0.75}, {"time": 8.8333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 9, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 9.0667, "curve": 0.25, "c3": 0.75}, {"time": 9.1667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 9.2667, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 9.4333, "curve": 0.25, "c3": 0.75}, {"time": 9.5, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 9.7667, "curve": 0.25, "c3": 0.75}, {"time": 9.8667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 9.9333, "curve": 0.25, "c3": 0.75}, {"time": 10.0333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 10.1667, "curve": 0.25, "c3": 0.75}, {"time": 10.2333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "curve": 0.25, "c3": 0.75}, {"time": 10.4333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "curve": 0.25, "c3": 0.75}, {"time": 10.6, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone10": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.7667, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 6.2333, "curve": 0.25, "c3": 0.75}, {"time": 6.7, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 7.1, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.4333, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 9.3667, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 9.7667, "curve": 0.25, "c3": 0.75}, {"time": 10.2667, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "图层 39": {"translate": [{"time": 0.4, "curve": 0.674, "c3": 0.358}, {"time": 0.9333, "x": -86.29, "y": 36.87, "curve": "stepped"}, {"time": 1.3667, "x": -86.29, "y": 36.87, "curve": 0.839, "c3": 0.332}, {"time": 2, "x": 9.69, "y": 68.2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": "stepped"}, {"time": 3.0667, "curve": 0.674, "c3": 0.358}, {"time": 3.6, "x": -103.44, "y": 26.58}, {"time": 4.0333, "x": -86.29, "y": 36.87, "curve": 0.839, "c3": 0.332}, {"time": 4.6667, "x": 23.78, "y": -92.53, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": "stepped"}, {"time": 5.7333, "curve": 0.674, "c3": 0.358}, {"time": 6.2667, "x": -86.29, "y": 36.87, "curve": "stepped"}, {"time": 6.7, "x": -86.29, "y": 36.87, "curve": 0.839, "c3": 0.332}, {"time": 7.3333, "x": -133.04, "y": 97.67}, {"time": 8, "curve": "stepped"}, {"time": 8.4, "curve": 0.674, "c3": 0.358}, {"time": 8.9333, "x": -121.28, "y": 13.55, "curve": 0.25, "c3": 0.75}, {"time": 9.3667, "x": -86.29, "y": 36.87, "curve": 0.839, "c3": 0.332}, {"time": 10, "x": -150.48, "y": 38.54, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "图层 48": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 4.1, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "curve": 0.25, "c3": 0.75}, {"time": 4.3667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 4.6, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 5.6, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.7667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 5.8667, "curve": 0.25, "c3": 0.75}, {"time": 5.9333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 6.2333, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 6.6, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 6.9333, "curve": 0.25, "c3": 0.75}, {"time": 7.0333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 7.1, "curve": 0.25, "c3": 0.75}, {"time": 7.2, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 7.5667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.7667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.9333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.1, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 8.2667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 8.4333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "curve": 0.25, "c3": 0.75}, {"time": 8.6, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 8.7333, "curve": 0.25, "c3": 0.75}, {"time": 8.8333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 9, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 9.0667, "curve": 0.25, "c3": 0.75}, {"time": 9.1667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 9.2667, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 9.4333, "curve": 0.25, "c3": 0.75}, {"time": 9.5, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 9.7667, "curve": 0.25, "c3": 0.75}, {"time": 9.8667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 9.9333, "curve": 0.25, "c3": 0.75}, {"time": 10.0333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 10.1667, "curve": 0.25, "c3": 0.75}, {"time": 10.2333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "curve": 0.25, "c3": 0.75}, {"time": 10.4333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "curve": 0.25, "c3": 0.75}, {"time": 10.6, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "图层 49": {"shear": [{"y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.3333, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.6667, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.6667, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.3333, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.6667, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 9.3333, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 10, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.6667, "y": -21}]}, "图层 50": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 4.1, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "curve": 0.25, "c3": 0.75}, {"time": 4.3667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 4.6, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 5.6, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.7667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 5.8667, "curve": 0.25, "c3": 0.75}, {"time": 5.9333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 6.2333, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 6.6, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 6.9333, "curve": 0.25, "c3": 0.75}, {"time": 7.0333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 7.1, "curve": 0.25, "c3": 0.75}, {"time": 7.2, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 7.5667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.7667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.9333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.1, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 8.2667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 8.4333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "curve": 0.25, "c3": 0.75}, {"time": 8.6, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 8.7333, "curve": 0.25, "c3": 0.75}, {"time": 8.8333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 9, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 9.0667, "curve": 0.25, "c3": 0.75}, {"time": 9.1667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 9.2667, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 9.4333, "curve": 0.25, "c3": 0.75}, {"time": 9.5, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 9.7667, "curve": 0.25, "c3": 0.75}, {"time": 9.8667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 9.9333, "curve": 0.25, "c3": 0.75}, {"time": 10.0333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 10.1667, "curve": 0.25, "c3": 0.75}, {"time": 10.2333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "curve": 0.25, "c3": 0.75}, {"time": 10.4333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "curve": 0.25, "c3": 0.75}, {"time": 10.6, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone11": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.7667, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 6.2333, "curve": 0.25, "c3": 0.75}, {"time": 6.7, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 7.1, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.4333, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 9.3667, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 9.7667, "curve": 0.25, "c3": 0.75}, {"time": 10.2667, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone3": {"translate": [{"time": 5.8333, "x": 231.21}, {"time": 6.1667, "x": 164.94, "y": 8}, {"time": 6.5333, "x": 92.05}, {"time": 6.9, "x": 22.48, "y": 4}, {"time": 7.2667, "x": -47.1}, {"time": 7.5667, "x": -106.74, "y": 4}, {"time": 7.9667, "x": -186.25}, {"time": 8.3, "x": -250.68, "y": 4}, {"time": 8.5667, "x": -302.21}, {"time": 8.8667, "x": -360.19, "y": 4}, {"time": 9.1667, "x": -418.18}]}, "bone": {"translate": [{"x": -155.25, "y": -82.8, "curve": 0.31, "c3": 0.645, "c4": 0.35}, {"time": 0.2667, "x": -141.69, "y": -78.8, "curve": 0.316, "c2": 0.23, "c3": 0.651, "c4": 0.57}, {"time": 0.5333, "x": -109.67, "y": -82.8, "curve": 0.321, "c2": 0.28, "c3": 0.655, "c4": 0.62}, {"time": 0.8, "x": -70.63, "y": -78.8, "curve": 0.324, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 1.1, "x": -18.3, "y": -82.8, "curve": 0.328, "c2": 0.32, "c3": 0.662, "c4": 0.65}, {"time": 1.4, "x": 39.12, "y": -78.8, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 1.6333, "x": 85.54, "y": -82.8, "curve": 0.334, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 1.9333, "x": 146.1, "y": -80.8, "curve": 0.337, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 2.1667, "x": 191.71, "y": -82.8, "curve": 0.34, "c2": 0.36, "c3": 0.674, "c4": 0.69}, {"time": 2.4333, "x": 241.96, "y": -78.8, "curve": 0.342, "c2": 0.37, "c3": 0.676, "c4": 0.7}, {"time": 2.6667, "x": 280.84, "y": -82.8, "curve": 0.351, "c2": 0.42, "c3": 0.686, "c4": 0.76}, {"time": 3, "x": 325.52, "y": -78.8, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.3333, "x": 345.31, "y": -82.8}]}}, "drawOrder": [{"offsets": [{"slot": "c2people2", "offset": -4}]}]}, "a03": {"slots": {"qizi": {"attachment": [{"name": null}]}, "a1t1": {"attachment": [{"name": "a1t1"}]}, "qq3": {"attachment": [{"name": null}]}, "c3people4": {"attachment": [{"name": null}]}, "ganzi2": {"attachment": [{"name": null}]}, "qq1": {"attachment": [{"name": null}]}, "qq2": {"attachment": [{"name": null}]}, "b2wl": {"attachment": [{"name": "b2wl"}]}, "b2hl": {"attachment": [{"name": "b2hl"}]}, "qq4": {"attachment": [{"name": null}]}, "daizi2": {"attachment": [{"name": null}]}, "daizi": {"attachment": [{"name": null}]}, "a1t2": {"attachment": [{"name": "a1t2"}]}, "b1wd2": {"attachment": [{"name": "b1wd"}]}, "c2people1": {"attachment": [{"name": null}]}, "b1m": {"attachment": [{"name": "b1m"}]}, "C103-0": {"attachment": [{"name": null}]}, "b11l": {"attachment": [{"name": "b11l"}]}, "b1tz1": {"attachment": [{"name": "b1tz1"}]}, "c2people2": {"attachment": [{"name": null}]}, "c3people3": {"attachment": [{"name": null}]}, "b12l": {"attachment": [{"name": "b12l"}]}, "b2syhl": {"attachment": [{"name": "b2syhl"}]}, "qizi3": {"attachment": [{"name": null}]}, "b2wd": {"attachment": [{"name": "b2wd"}]}, "ganzi": {"attachment": [{"name": null}]}, "b1tz2": {"attachment": [{"name": "b1tz2"}]}}, "bones": {"1j2": {"translate": [{"x": -28.2, "curve": "stepped"}, {"time": 0.5, "x": -28.2, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": -28.2, "y": 328.28, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -28.2}], "scale": [{"x": 0.105, "y": -0.003, "curve": "stepped"}, {"time": 0.5, "x": 0.105, "y": -0.003, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "1j3": {"translate": [{"x": 28.2, "curve": "stepped"}, {"time": 0.5, "x": 28.2, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": 31.1, "y": 328.28, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 28.2}], "scale": [{"x": 0.105, "y": -0.003, "curve": "stepped"}, {"time": 0.5, "x": 0.105, "y": -0.003, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "lv3": {"translate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "y": 211.08, "curve": 0.25, "c3": 0.75}, {"time": 0.6333}], "scale": [{"x": 0.105, "y": 0.105, "curve": "stepped"}, {"time": 0.2333, "x": 0.105, "y": 0.105, "curve": 0.25, "c3": 0.75}, {"time": 0.6333}]}, "lv10": {"translate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "y": 138.18, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}], "scale": [{"x": 0.105, "y": 0.105, "curve": "stepped"}, {"time": 0.1333, "x": 0.105, "y": 0.105, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "lv9": {"translate": [{"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "y": 158.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}], "scale": [{"x": 0.105, "y": 0.105, "curve": "stepped"}, {"time": 0.2667, "x": 0.105, "y": 0.105, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "lv8": {"translate": [{"y": -302.49, "curve": "stepped"}, {"time": 0.1333, "y": -302.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "y": 217.96, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}], "scale": [{"x": 0.105, "y": 0.105, "curve": "stepped"}, {"time": 0.1333, "x": 0.105, "y": 0.105, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "lv7": {"translate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "y": 90.97, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}], "scale": [{"x": 0.105, "y": 0.105, "curve": "stepped"}, {"time": 0.0667, "x": 0.105, "y": 0.105, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}]}, "lv2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 211.08, "curve": 0.25, "c3": 0.75}, {"time": 0.4}], "scale": [{"x": 0.105, "y": 0.105, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "lv1": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4333, "y": 211.08, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}], "scale": [{"x": 0.105, "y": 0.105, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}]}}}, "a03-1": {"slots": {"c2people6": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.9667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "color": "ffffff00"}], "attachment": [{"name": "c2people2"}]}, "a1t1": {"attachment": [{"name": "a1t1"}]}, "a1t2": {"attachment": [{"name": "a1t2"}]}, "c3people4": {"attachment": [{"name": null}]}, "ganzi2": {"attachment": [{"name": null}]}, "qq3": {"attachment": [{"name": null}]}, "b2wl": {"attachment": [{"name": "b2wl"}]}, "b2hl": {"attachment": [{"name": "b2hl"}]}, "c2people4": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 5.8333, "color": "ffffff00"}, {"time": 6.3, "color": "ffffffff", "curve": "stepped"}, {"time": 8.8333, "color": "ffffffff"}, {"time": 9.1667, "color": "ffffff00"}]}, "qq4": {"attachment": [{"name": null}]}, "c2people3": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 5.8333, "color": "ffffff00"}, {"time": 6.3, "color": "ffffffff", "curve": "stepped"}, {"time": 8.8333, "color": "ffffffff"}, {"time": 9.1667, "color": "ffffff00"}], "attachment": [{"name": "c2people1"}]}, "daizi2": {"attachment": [{"name": null}]}, "daizi": {"attachment": [{"name": null}]}, "qizi": {"attachment": [{"name": null}]}, "b1wd2": {"attachment": [{"name": "b1wd"}]}, "c2people8": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.9667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "color": "ffffff00"}]}, "c2people1": {"attachment": [{"name": null}]}, "b1m": {"attachment": [{"name": "b1m"}]}, "C103-0": {"attachment": [{"name": null}]}, "b11l": {"attachment": [{"name": "b11l"}]}, "b1tz1": {"attachment": [{"name": "b1tz1"}]}, "c2people2": {"attachment": [{"name": null}]}, "c2people5": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 5.8333, "color": "ffffff00"}, {"time": 6.3, "color": "ffffffff", "curve": "stepped"}, {"time": 8.8333, "color": "ffffffff"}, {"time": 9.1667, "color": "ffffff00"}]}, "c3people3": {"attachment": [{"name": null}]}, "b12l": {"attachment": [{"name": "b12l"}]}, "b2syhl": {"attachment": [{"name": "b2syhl"}]}, "qizi3": {"attachment": [{"name": null}]}, "b2wd": {"attachment": [{"name": "b2wd"}]}, "ganzi": {"attachment": [{"name": null}]}, "c2people7": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.9667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "color": "ffffff00"}], "attachment": [{"name": "c2people2"}]}, "b1tz2": {"attachment": [{"name": "b1tz2"}]}}, "bones": {"1j2": {"translate": [{"x": -28.2}]}, "1j3": {"translate": [{"x": 28.2}]}, "lv9": {"shear": [{"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6, "y": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "y": 3.64, "curve": 0.25, "c3": 0.75}, {"time": 7.3333}]}, "图层 45": {"shear": [{"y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.3333, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.6667, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.6667, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.3333, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.6667, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 9.3333, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 10, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.6667, "y": -21}]}, "图层 46": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 4.1, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "curve": 0.25, "c3": 0.75}, {"time": 4.3667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 4.6, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 5.6, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.7667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 5.8667, "curve": 0.25, "c3": 0.75}, {"time": 5.9333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 6.2333, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 6.6, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 6.9333, "curve": 0.25, "c3": 0.75}, {"time": 7.0333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 7.1, "curve": 0.25, "c3": 0.75}, {"time": 7.2, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 7.5667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.7667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.9333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.1, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 8.2667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 8.4333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "curve": 0.25, "c3": 0.75}, {"time": 8.6, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 8.7333, "curve": 0.25, "c3": 0.75}, {"time": 8.8333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 9, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 9.0667, "curve": 0.25, "c3": 0.75}, {"time": 9.1667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 9.2667, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 9.4333, "curve": 0.25, "c3": 0.75}, {"time": 9.5, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 9.7667, "curve": 0.25, "c3": 0.75}, {"time": 9.8667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 9.9333, "curve": 0.25, "c3": 0.75}, {"time": 10.0333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 10.1667, "curve": 0.25, "c3": 0.75}, {"time": 10.2333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "curve": 0.25, "c3": 0.75}, {"time": 10.4333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "curve": 0.25, "c3": 0.75}, {"time": 10.6, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "图层 38": {"translate": [{"time": 0.4, "curve": 0.674, "c3": 0.358}, {"time": 0.9333, "x": -86.29, "y": 36.87, "curve": "stepped"}, {"time": 1.3667, "x": -86.29, "y": 36.87, "curve": 0.839, "c3": 0.332}, {"time": 2, "x": 9.69, "y": 68.2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": "stepped"}, {"time": 3.0667, "curve": 0.674, "c3": 0.358}, {"time": 3.6, "x": -103.44, "y": 26.58}, {"time": 4.0333, "x": -86.29, "y": 36.87, "curve": 0.839, "c3": 0.332}, {"time": 4.6667, "x": 23.78, "y": -92.53, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": "stepped"}, {"time": 5.7333, "curve": 0.674, "c3": 0.358}, {"time": 6.2667, "x": -86.29, "y": 36.87, "curve": "stepped"}, {"time": 6.7, "x": -86.29, "y": 36.87, "curve": 0.839, "c3": 0.332}, {"time": 7.3333, "x": -133.04, "y": 97.67}, {"time": 8, "curve": "stepped"}, {"time": 8.4, "curve": 0.674, "c3": 0.358}, {"time": 8.9333, "x": -121.28, "y": 13.55, "curve": 0.25, "c3": 0.75}, {"time": 9.3667, "x": -86.29, "y": 36.87, "curve": 0.839, "c3": 0.332}, {"time": 10, "x": -150.48, "y": 38.54, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "图层 44": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 4.1, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "curve": 0.25, "c3": 0.75}, {"time": 4.3667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 4.6, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 5.6, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.7667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 5.8667, "curve": 0.25, "c3": 0.75}, {"time": 5.9333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 6.2333, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 6.6, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 6.9333, "curve": 0.25, "c3": 0.75}, {"time": 7.0333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 7.1, "curve": 0.25, "c3": 0.75}, {"time": 7.2, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 7.5667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.7667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.9333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.1, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 8.2667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 8.4333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "curve": 0.25, "c3": 0.75}, {"time": 8.6, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 8.7333, "curve": 0.25, "c3": 0.75}, {"time": 8.8333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 9, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 9.0667, "curve": 0.25, "c3": 0.75}, {"time": 9.1667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 9.2667, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 9.4333, "curve": 0.25, "c3": 0.75}, {"time": 9.5, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 9.7667, "curve": 0.25, "c3": 0.75}, {"time": 9.8667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 9.9333, "curve": 0.25, "c3": 0.75}, {"time": 10.0333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 10.1667, "curve": 0.25, "c3": 0.75}, {"time": 10.2333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "curve": 0.25, "c3": 0.75}, {"time": 10.4333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "curve": 0.25, "c3": 0.75}, {"time": 10.6, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone10": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.7667, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 6.2333, "curve": 0.25, "c3": 0.75}, {"time": 6.7, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 7.1, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.4333, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 9.3667, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 9.7667, "curve": 0.25, "c3": 0.75}, {"time": 10.2667, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone2": {"translate": [{"y": 84}], "scale": [{"x": 0.778}]}, "图层 39": {"translate": [{"time": 0.4, "curve": 0.674, "c3": 0.358}, {"time": 0.9333, "x": -86.29, "y": 36.87, "curve": "stepped"}, {"time": 1.3667, "x": -86.29, "y": 36.87, "curve": 0.839, "c3": 0.332}, {"time": 2, "x": 9.69, "y": 68.2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": "stepped"}, {"time": 3.0667, "curve": 0.674, "c3": 0.358}, {"time": 3.6, "x": -103.44, "y": 26.58}, {"time": 4.0333, "x": -86.29, "y": 36.87, "curve": 0.839, "c3": 0.332}, {"time": 4.6667, "x": 23.78, "y": -92.53, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": "stepped"}, {"time": 5.7333, "curve": 0.674, "c3": 0.358}, {"time": 6.2667, "x": -86.29, "y": 36.87, "curve": "stepped"}, {"time": 6.7, "x": -86.29, "y": 36.87, "curve": 0.839, "c3": 0.332}, {"time": 7.3333, "x": -133.04, "y": 97.67}, {"time": 8, "curve": "stepped"}, {"time": 8.4, "curve": 0.674, "c3": 0.358}, {"time": 8.9333, "x": -121.28, "y": 13.55, "curve": 0.25, "c3": 0.75}, {"time": 9.3667, "x": -86.29, "y": 36.87, "curve": 0.839, "c3": 0.332}, {"time": 10, "x": -150.48, "y": 38.54, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "图层 48": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 4.1, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "curve": 0.25, "c3": 0.75}, {"time": 4.3667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 4.6, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 5.6, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.7667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 5.8667, "curve": 0.25, "c3": 0.75}, {"time": 5.9333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 6.2333, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 6.6, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 6.9333, "curve": 0.25, "c3": 0.75}, {"time": 7.0333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 7.1, "curve": 0.25, "c3": 0.75}, {"time": 7.2, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 7.5667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.7667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.9333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.1, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 8.2667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 8.4333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "curve": 0.25, "c3": 0.75}, {"time": 8.6, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 8.7333, "curve": 0.25, "c3": 0.75}, {"time": 8.8333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 9, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 9.0667, "curve": 0.25, "c3": 0.75}, {"time": 9.1667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 9.2667, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 9.4333, "curve": 0.25, "c3": 0.75}, {"time": 9.5, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 9.7667, "curve": 0.25, "c3": 0.75}, {"time": 9.8667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 9.9333, "curve": 0.25, "c3": 0.75}, {"time": 10.0333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 10.1667, "curve": 0.25, "c3": 0.75}, {"time": 10.2333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "curve": 0.25, "c3": 0.75}, {"time": 10.4333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "curve": 0.25, "c3": 0.75}, {"time": 10.6, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "图层 49": {"shear": [{"y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.3333, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.6667, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.6667, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.3333, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.6667, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 9.3333, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 10, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.6667, "y": -21}]}, "图层 50": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 4.1, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "curve": 0.25, "c3": 0.75}, {"time": 4.3667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 4.6, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 5.6, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.7667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 5.8667, "curve": 0.25, "c3": 0.75}, {"time": 5.9333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 6.2333, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 6.6, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 6.9333, "curve": 0.25, "c3": 0.75}, {"time": 7.0333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 7.1, "curve": 0.25, "c3": 0.75}, {"time": 7.2, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 7.5667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.7667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.9333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.1, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 8.2667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 8.4333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "curve": 0.25, "c3": 0.75}, {"time": 8.6, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 8.7333, "curve": 0.25, "c3": 0.75}, {"time": 8.8333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 9, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 9.0667, "curve": 0.25, "c3": 0.75}, {"time": 9.1667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 9.2667, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 9.4333, "curve": 0.25, "c3": 0.75}, {"time": 9.5, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 9.7667, "curve": 0.25, "c3": 0.75}, {"time": 9.8667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 9.9333, "curve": 0.25, "c3": 0.75}, {"time": 10.0333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 10.1667, "curve": 0.25, "c3": 0.75}, {"time": 10.2333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "curve": 0.25, "c3": 0.75}, {"time": 10.4333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "curve": 0.25, "c3": 0.75}, {"time": 10.6, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone11": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.7667, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 6.2333, "curve": 0.25, "c3": 0.75}, {"time": 6.7, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 7.1, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.4333, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 9.3667, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 9.7667, "curve": 0.25, "c3": 0.75}, {"time": 10.2667, "x": -0.8, "y": 24.66, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone4": {"translate": [{"y": 84}], "scale": [{"x": 0.778}]}, "bone": {"translate": [{"x": -155.25, "y": -82.8, "curve": 0.31, "c3": 0.645, "c4": 0.35}, {"time": 0.2667, "x": -141.69, "y": -78.8, "curve": 0.316, "c2": 0.23, "c3": 0.651, "c4": 0.57}, {"time": 0.5333, "x": -109.67, "y": -82.8, "curve": 0.321, "c2": 0.28, "c3": 0.655, "c4": 0.62}, {"time": 0.8, "x": -70.63, "y": -78.8, "curve": 0.324, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 1.1, "x": -18.3, "y": -82.8, "curve": 0.328, "c2": 0.32, "c3": 0.662, "c4": 0.65}, {"time": 1.4, "x": 39.12, "y": -78.8, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 1.6333, "x": 85.54, "y": -82.8, "curve": 0.334, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 1.9333, "x": 146.1, "y": -80.8, "curve": 0.337, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 2.1667, "x": 191.71, "y": -82.8, "curve": 0.34, "c2": 0.36, "c3": 0.674, "c4": 0.69}, {"time": 2.4333, "x": 241.96, "y": -78.8, "curve": 0.342, "c2": 0.37, "c3": 0.676, "c4": 0.7}, {"time": 2.6667, "x": 280.84, "y": -82.8, "curve": 0.351, "c2": 0.42, "c3": 0.686, "c4": 0.76}, {"time": 3, "x": 325.52, "y": -78.8, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.3333, "x": 345.31, "y": -82.8}]}, "bone3": {"translate": [{"time": 5.8333, "x": 231.21}, {"time": 6.1667, "x": 164.94, "y": 8}, {"time": 6.5333, "x": 92.05}, {"time": 6.9, "x": 22.48, "y": 4}, {"time": 7.2667, "x": -47.1}, {"time": 7.5667, "x": -106.74, "y": 4}, {"time": 7.9667, "x": -186.25}, {"time": 8.3, "x": -250.68, "y": 4}, {"time": 8.5667, "x": -302.21}, {"time": 8.8667, "x": -360.19, "y": 4}, {"time": 9.1667, "x": -418.18}]}, "daizi": {"rotate": [{"angle": -3.4, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1333, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 4.52, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2.6667, "angle": -3.4, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 2.8, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "angle": 4.52, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 5.3333, "angle": -3.4, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 5.4667, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 6.8, "angle": 4.52, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 8, "angle": -3.4, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 8.1333, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 9.4667, "angle": 4.52, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 10.6667, "angle": -3.4}]}, "daizi6": {"rotate": [{"angle": 1.48, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 4.52, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "angle": 1.48, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.4667, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "angle": 4.52, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.3333, "angle": 1.48, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.1333, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 7.4667, "angle": 4.52, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 8, "angle": 1.48, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 8.8, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 10.1333, "angle": 4.52, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 10.6667, "angle": 1.48}]}, "daizi4": {"rotate": [{"angle": -0.71, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5333, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 4.52, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": -0.71, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.2, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "angle": 4.52, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.3333, "angle": -0.71, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 5.8667, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 7.2, "angle": 4.52, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8, "angle": -0.71, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 8.5333, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 9.8667, "angle": 4.52, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 10.6667, "angle": -0.71}]}, "daizi3": {"rotate": [{"angle": -1.75, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": 4.52, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.6667, "angle": -1.75, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.0667, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 4.4, "angle": 4.52, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 5.3333, "angle": -1.75, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 5.7333, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 7.0667, "angle": 4.52, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 8, "angle": -1.75, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.4, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 9.7333, "angle": 4.52, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 10.6667, "angle": -1.75}]}, "daizi2": {"rotate": [{"angle": -2.68, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2667, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 4.52, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.6667, "angle": -2.68, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.9333, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "angle": 4.52, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.3333, "angle": -2.68, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.6, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 6.9333, "angle": 4.52, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 8, "angle": -2.68, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 8.2667, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "angle": 4.52, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 10.6667, "angle": -2.68}]}, "daizi5": {"rotate": [{"angle": 0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 4.52, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 4.52, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": 4.52, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": 0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.6667, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": 4.52, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.6667, "angle": 0.38}]}, "daizi11": {"rotate": [{"angle": 4.17, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1333, "angle": 4.52, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -3.75, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2.6667, "angle": 4.17, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 2.8, "angle": 4.52, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "angle": -3.75, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 5.3333, "angle": 4.17, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 5.4667, "angle": 4.52, "curve": 0.25, "c3": 0.75}, {"time": 6.8, "angle": -3.75, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 8, "angle": 4.17, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 8.1333, "angle": 4.52, "curve": 0.25, "c3": 0.75}, {"time": 9.4667, "angle": -3.75, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 10.6667, "angle": 4.17}]}, "daizi10": {"rotate": [{"angle": 4.52, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 4.52, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 4.52, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 4.52, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 4.52}]}, "daizi9": {"rotate": [{"angle": 4.17, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1.2, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "angle": 4.52, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2.6667, "angle": 4.17, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 3.8667, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 5.2, "angle": 4.52, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 5.3333, "angle": 4.17, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 6.5333, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 7.8667, "angle": 4.52, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 8, "angle": 4.17, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 9.2, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 10.5333, "angle": 4.52, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 10.6667, "angle": 4.17}]}, "daizi8": {"rotate": [{"angle": 3.44, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.0667, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "angle": 4.52, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "angle": 3.44, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.7333, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "angle": 4.52, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.3333, "angle": 3.44, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 6.4, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 7.7333, "angle": 4.52, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 8, "angle": 3.44, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 9.0667, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 10.4, "angle": 4.52, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 10.6667, "angle": 3.44}]}, "daizi7": {"rotate": [{"angle": 2.52, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.9333, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 4.52, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "angle": 2.52, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 3.6, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "angle": 4.52, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 5.3333, "angle": 2.52, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 6.2667, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "angle": 4.52, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 8, "angle": 2.52, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 8.9333, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 10.2667, "angle": 4.52, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 10.6667, "angle": 2.52}]}, "ganzi": {"translate": [{"x": -45.73, "y": -234.75}]}, "qq2": {"rotate": [{"angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.58, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 4.58, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 4.58, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 4.58, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": -4.51}]}, "qq3": {"rotate": [{"angle": -3.94, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 4.58, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": -3.94, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 2.8333, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": 4.58, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.3333, "angle": -3.94, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 5.5, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 4.58, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 8, "angle": -3.94, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 8.1667, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 9.5, "angle": 4.58, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 10.6667, "angle": -3.94}]}, "qq4": {"rotate": [{"angle": -2.83, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 4.58, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -2.83, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 4.58, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -2.83, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": 4.58, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -2.83, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.3333, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "angle": 4.58, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "angle": -2.83}]}, "qq5": {"rotate": [{"angle": -1.46, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 4.58, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -1.46, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": 4.58, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "angle": -1.46, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 5.8333, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "angle": 4.58, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8, "angle": -1.46, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 8.5, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 9.8333, "angle": 4.58, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 10.6667, "angle": -1.46}]}, "qq6": {"rotate": [{"angle": 0.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 4.58, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 0.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 4.58, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 0.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": 4.58, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": 0.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.6667, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": 4.58, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.6667, "angle": 0.04}]}, "qq7": {"rotate": [{"angle": 1.53, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 4.58, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": 1.53, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 3.5, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "angle": 4.58, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": 1.53, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 6.1667, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "angle": 4.58, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 8, "angle": 1.53, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 8.8333, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 10.1667, "angle": 4.58, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 10.6667, "angle": 1.53}]}, "qq14": {"rotate": [{"angle": 1.53, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 4.58, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": 1.53, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 3.5, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "angle": 4.58, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": 1.53, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 6.1667, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "angle": 4.58, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 8, "angle": 1.53, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 8.8333, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 10.1667, "angle": 4.58, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 10.6667, "angle": 1.53}]}, "qq13": {"rotate": [{"angle": 0.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 4.58, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 0.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 4.58, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 0.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": 4.58, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": 0.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.6667, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": 4.58, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.6667, "angle": 0.04}]}, "qq12": {"rotate": [{"angle": -1.46, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 4.58, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -1.46, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": 4.58, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "angle": -1.46, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 5.8333, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "angle": 4.58, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8, "angle": -1.46, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 8.5, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 9.8333, "angle": 4.58, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 10.6667, "angle": -1.46}]}, "qq11": {"rotate": [{"angle": -2.83, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 4.58, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -2.83, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 4.58, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -2.83, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": 4.58, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -2.83, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 8.3333, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "angle": 4.58, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 10.6667, "angle": -2.83}]}, "qq10": {"rotate": [{"angle": -3.94, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 4.58, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": -3.94, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 2.8333, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": 4.58, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.3333, "angle": -3.94, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 5.5, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 4.58, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 8, "angle": -3.94, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 8.1667, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 9.5, "angle": 4.58, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 10.6667, "angle": -3.94}]}, "qq9": {"rotate": [{"angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.58, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 4.58, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 4.58, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 4.58, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": -4.51}]}, "ganzi15": {"translate": [{"x": -45.73, "y": -234.75}]}, "daizi12": {"rotate": [{"angle": -3.4, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1333, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 4.52, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2.6667, "angle": -3.4, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 2.8, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "angle": 4.52, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 5.3333, "angle": -3.4, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 5.4667, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 6.8, "angle": 4.52, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 8, "angle": -3.4, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 8.1333, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 9.4667, "angle": 4.52, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 10.6667, "angle": -3.4}]}, "daizi22": {"rotate": [{"angle": 4.17, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1333, "angle": 4.52, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -3.75, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2.6667, "angle": 4.17, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 2.8, "angle": 4.52, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "angle": -3.75, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 5.3333, "angle": 4.17, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 5.4667, "angle": 4.52, "curve": 0.25, "c3": 0.75}, {"time": 6.8, "angle": -3.75, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 8, "angle": 4.17, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 8.1333, "angle": 4.52, "curve": 0.25, "c3": 0.75}, {"time": 9.4667, "angle": -3.75, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 10.6667, "angle": 4.17}]}, "daizi21": {"rotate": [{"angle": 4.52, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 4.52, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 4.52, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 4.52, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 4.52}]}, "daizi20": {"rotate": [{"angle": 4.17, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1.2, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "angle": 4.52, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2.6667, "angle": 4.17, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 3.8667, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 5.2, "angle": 4.52, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 5.3333, "angle": 4.17, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 6.5333, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 7.8667, "angle": 4.52, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 8, "angle": 4.17, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 9.2, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 10.5333, "angle": 4.52, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 10.6667, "angle": 4.17}]}, "daizi19": {"rotate": [{"angle": 3.44, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.0667, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "angle": 4.52, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "angle": 3.44, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.7333, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "angle": 4.52, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.3333, "angle": 3.44, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 6.4, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 7.7333, "angle": 4.52, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 8, "angle": 3.44, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 9.0667, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 10.4, "angle": 4.52, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 10.6667, "angle": 3.44}]}, "daizi18": {"rotate": [{"angle": 2.52, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.9333, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 4.52, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "angle": 2.52, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 3.6, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "angle": 4.52, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 5.3333, "angle": 2.52, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 6.2667, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 7.6, "angle": 4.52, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 8, "angle": 2.52, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 8.9333, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 10.2667, "angle": 4.52, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 10.6667, "angle": 2.52}]}, "daizi17": {"rotate": [{"angle": 1.48, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 4.52, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "angle": 1.48, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.4667, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "angle": 4.52, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.3333, "angle": 1.48, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.1333, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 7.4667, "angle": 4.52, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 8, "angle": 1.48, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 8.8, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 10.1333, "angle": 4.52, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 10.6667, "angle": 1.48}]}, "daizi16": {"rotate": [{"angle": 0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 4.52, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 4.52, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": 4.52, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": 0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.6667, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": 4.52, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.6667, "angle": 0.38}]}, "daizi15": {"rotate": [{"angle": -0.71, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5333, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 4.52, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": -0.71, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.2, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "angle": 4.52, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.3333, "angle": -0.71, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 5.8667, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 7.2, "angle": 4.52, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8, "angle": -0.71, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 8.5333, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 9.8667, "angle": 4.52, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 10.6667, "angle": -0.71}]}, "daizi14": {"rotate": [{"angle": -1.75, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": 4.52, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.6667, "angle": -1.75, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.0667, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 4.4, "angle": 4.52, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 5.3333, "angle": -1.75, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 5.7333, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 7.0667, "angle": 4.52, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 8, "angle": -1.75, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.4, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 9.7333, "angle": 4.52, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 10.6667, "angle": -1.75}]}, "daizi13": {"rotate": [{"angle": -2.68, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2667, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 4.52, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.6667, "angle": -2.68, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.9333, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "angle": 4.52, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.3333, "angle": -2.68, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.6, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 6.9333, "angle": 4.52, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 8, "angle": -2.68, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 8.2667, "angle": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "angle": 4.52, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 10.6667, "angle": -2.68}]}}, "drawOrder": [{"offsets": [{"slot": "c2people2", "offset": -4}]}]}, "a04": {"slots": {"qizi": {"attachment": [{"name": null}]}, "c1tz1": {"attachment": [{"name": "c1tz1"}]}, "qq3": {"attachment": [{"name": null}]}, "c12l": {"attachment": [{"name": "c12l"}]}, "c3people4": {"attachment": [{"name": null}]}, "ganzi2": {"attachment": [{"name": null}]}, "qq1": {"attachment": [{"name": null}]}, "qq2": {"attachment": [{"name": null}]}, "c1lang": {"attachment": [{"name": "c1lang"}]}, "c11l": {"attachment": [{"name": "c11l"}]}, "qq4": {"attachment": [{"name": null}]}, "daizi2": {"attachment": [{"name": null}]}, "daizi": {"attachment": [{"name": null}]}, "c1tz2": {"attachment": [{"name": "c1tz2"}]}, "c2people1": {"attachment": [{"name": null}]}, "c1ss": {"attachment": [{"name": "c1ss"}]}, "c2people2": {"attachment": [{"name": null}]}, "c3people3": {"attachment": [{"name": null}]}, "C103-0": {"attachment": [{"name": null}]}, "qizi3": {"attachment": [{"name": null}]}, "ganzi": {"attachment": [{"name": null}]}}, "bones": {"lv11": {"translate": [{"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "y": 254.77, "curve": 0.25, "c3": 0.75}, {"time": 1.3667}], "scale": [{"x": 0.245, "y": 0.01, "curve": "stepped"}, {"time": 0.6, "x": 0.245, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 1.3667}]}, "lv12": {"translate": [{"time": 0.0667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4667, "y": 597, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333}], "scale": [{"x": 0.245, "y": 0.01, "curve": "stepped"}, {"time": 0.0667, "x": 0.245, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}]}, "lv13": {"translate": [{"time": 0.4667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.8333, "y": 716.08, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.2}], "scale": [{"x": 0.245, "y": 0.01, "curve": "stepped"}, {"time": 0.4667, "x": 0.245, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 1.2}]}, "lv14": {"translate": [{"time": 0.4667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.8333, "y": 716.08, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.2}], "scale": [{"x": 0.245, "y": 0.01, "curve": "stepped"}, {"time": 0.4667, "x": 0.245, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 1.2}]}, "lv15": {"translate": [{"time": 0.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "y": 456.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.0667}], "scale": [{"x": 0.245, "y": 0.01, "curve": "stepped"}, {"time": 0.3, "x": 0.245, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 1.0667}]}, "lv4": {"translate": [{"y": -242.16, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 205.25, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}], "scale": [{"x": 0.245, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}}}, "a04-1": {"slots": {"qizi": {"attachment": [{"name": null}]}, "c1tz1": {"attachment": [{"name": "c1tz1"}]}, "c12l": {"attachment": [{"name": "c12l"}]}, "c3people4": {"attachment": [{"name": null}]}, "ganzi2": {"attachment": [{"name": null}]}, "qq1": {"attachment": [{"name": null}]}, "qq2": {"attachment": [{"name": null}]}, "c2people3": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 5.8333, "color": "ffffff00"}, {"time": 6.3, "color": "ffffffff", "curve": "stepped"}, {"time": 8.8333, "color": "ffffffff"}, {"time": 9.1667, "color": "ffffff00"}], "attachment": [{"name": "c2people1"}]}, "c1lang": {"attachment": [{"name": "c1lang"}]}, "c11l": {"attachment": [{"name": "c11l"}]}, "daizi2": {"attachment": [{"name": null}]}, "daizi": {"attachment": [{"name": null}]}, "c1tz2": {"attachment": [{"name": "c1tz2"}]}, "c2people8": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.9667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "color": "ffffff00"}]}, "c2people1": {"attachment": [{"name": null}]}, "c2people6": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.9667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "color": "ffffff00"}], "attachment": [{"name": "c2people2"}]}, "c1ss": {"attachment": [{"name": "c1ss"}]}, "c2people4": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 5.8333, "color": "ffffff00"}, {"time": 6.3, "color": "ffffffff", "curve": "stepped"}, {"time": 8.8333, "color": "ffffffff"}, {"time": 9.1667, "color": "ffffff00"}], "attachment": [{"name": "c2people1"}]}, "c2people2": {"attachment": [{"name": null}]}, "c2people5": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 5.8333, "color": "ffffff00"}, {"time": 6.3, "color": "ffffffff", "curve": "stepped"}, {"time": 8.8333, "color": "ffffffff"}, {"time": 9.1667, "color": "ffffff00"}]}, "c3people3": {"attachment": [{"name": null}]}, "C103-0": {"attachment": [{"name": null}]}, "qizi3": {"attachment": [{"name": null}]}, "ganzi": {"attachment": [{"name": null}]}, "c2people7": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.9667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "color": "ffffff00"}], "attachment": [{"name": "c2people2"}]}}, "bones": {"lv11": {"shear": [{"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6, "y": 3.6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "y": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "图层 44": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 4.1, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "curve": 0.25, "c3": 0.75}, {"time": 4.3667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 4.6, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 5.6, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.7667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 5.8667, "curve": 0.25, "c3": 0.75}, {"time": 5.9333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 6.2333, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 6.6, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 6.9333, "curve": 0.25, "c3": 0.75}, {"time": 7.0333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 7.1, "curve": 0.25, "c3": 0.75}, {"time": 7.2, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 7.5667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.7667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.9333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.1, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 8.2667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 8.4333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "curve": 0.25, "c3": 0.75}, {"time": 8.6, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 8.7333, "curve": 0.25, "c3": 0.75}, {"time": 8.8333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 9, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 9.0667, "curve": 0.25, "c3": 0.75}, {"time": 9.1667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 9.2667, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 9.4333, "curve": 0.25, "c3": 0.75}, {"time": 9.5, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 9.7667, "curve": 0.25, "c3": 0.75}, {"time": 9.8667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 9.9333, "curve": 0.25, "c3": 0.75}, {"time": 10.0333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 10.1667, "curve": 0.25, "c3": 0.75}, {"time": 10.2333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "curve": 0.25, "c3": 0.75}, {"time": 10.4333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "curve": 0.25, "c3": 0.75}, {"time": 10.6, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "图层 46": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 4.1, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "curve": 0.25, "c3": 0.75}, {"time": 4.3667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 4.6, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 5.6, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.7667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 5.8667, "curve": 0.25, "c3": 0.75}, {"time": 5.9333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 6.2333, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 6.6, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 6.9333, "curve": 0.25, "c3": 0.75}, {"time": 7.0333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 7.1, "curve": 0.25, "c3": 0.75}, {"time": 7.2, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 7.5667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.7667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.9333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.1, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 8.2667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 8.4333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "curve": 0.25, "c3": 0.75}, {"time": 8.6, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 8.7333, "curve": 0.25, "c3": 0.75}, {"time": 8.8333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 9, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 9.0667, "curve": 0.25, "c3": 0.75}, {"time": 9.1667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 9.2667, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 9.4333, "curve": 0.25, "c3": 0.75}, {"time": 9.5, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 9.7667, "curve": 0.25, "c3": 0.75}, {"time": 9.8667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 9.9333, "curve": 0.25, "c3": 0.75}, {"time": 10.0333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 10.1667, "curve": 0.25, "c3": 0.75}, {"time": 10.2333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "curve": 0.25, "c3": 0.75}, {"time": 10.4333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "curve": 0.25, "c3": 0.75}, {"time": 10.6, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "图层 38": {"translate": [{"time": 0.4, "curve": 0.674, "c3": 0.358}, {"time": 0.9333, "x": -86.29, "y": 36.87, "curve": "stepped"}, {"time": 1.3667, "x": -86.29, "y": 36.87, "curve": 0.839, "c3": 0.332}, {"time": 2, "x": 9.69, "y": 68.2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": "stepped"}, {"time": 3.0667, "curve": 0.674, "c3": 0.358}, {"time": 3.6, "x": -103.44, "y": 26.58}, {"time": 4.0333, "x": -86.29, "y": 36.87, "curve": 0.839, "c3": 0.332}, {"time": 4.6667, "x": 23.78, "y": -92.53, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": "stepped"}, {"time": 5.7333, "curve": 0.674, "c3": 0.358}, {"time": 6.2667, "x": -86.29, "y": 36.87, "curve": "stepped"}, {"time": 6.7, "x": -86.29, "y": 36.87, "curve": 0.839, "c3": 0.332}, {"time": 7.3333, "x": -133.04, "y": 97.67}, {"time": 8, "curve": "stepped"}, {"time": 8.4, "curve": 0.674, "c3": 0.358}, {"time": 8.9333, "x": -121.28, "y": 13.55, "curve": 0.25, "c3": 0.75}, {"time": 9.3667, "x": -86.29, "y": 36.87, "curve": 0.839, "c3": 0.332}, {"time": 10, "x": -150.48, "y": 38.54, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "图层 45": {"shear": [{"y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.3333, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.6667, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.6667, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.3333, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.6667, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 9.3333, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 10, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.6667, "y": -21}]}, "bone3": {"translate": [{"time": 5.8333, "x": 231.21}, {"time": 6.5333, "x": 92.05}, {"time": 7.2667, "x": -47.1}, {"time": 7.9667, "x": -186.25}, {"time": 9.1667, "x": -418.18}]}, "bone2": {"translate": [{"y": 70}], "scale": [{"x": 0.722, "y": 0.813}]}, "图层 39": {"translate": [{"time": 0.4, "curve": 0.674, "c3": 0.358}, {"time": 0.9333, "x": -86.29, "y": 36.87, "curve": "stepped"}, {"time": 1.3667, "x": -86.29, "y": 36.87, "curve": 0.839, "c3": 0.332}, {"time": 2, "x": 9.69, "y": 68.2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": "stepped"}, {"time": 3.0667, "curve": 0.674, "c3": 0.358}, {"time": 3.6, "x": -103.44, "y": 26.58}, {"time": 4.0333, "x": -86.29, "y": 36.87, "curve": 0.839, "c3": 0.332}, {"time": 4.6667, "x": 23.78, "y": -92.53, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": "stepped"}, {"time": 5.7333, "curve": 0.674, "c3": 0.358}, {"time": 6.2667, "x": -86.29, "y": 36.87, "curve": "stepped"}, {"time": 6.7, "x": -86.29, "y": 36.87, "curve": 0.839, "c3": 0.332}, {"time": 7.3333, "x": -133.04, "y": 97.67}, {"time": 8, "curve": "stepped"}, {"time": 8.4, "curve": 0.674, "c3": 0.358}, {"time": 8.9333, "x": -121.28, "y": 13.55, "curve": 0.25, "c3": 0.75}, {"time": 9.3667, "x": -86.29, "y": 36.87, "curve": 0.839, "c3": 0.332}, {"time": 10, "x": -150.48, "y": 38.54, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "图层 48": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 4.1, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "curve": 0.25, "c3": 0.75}, {"time": 4.3667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 4.6, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 5.6, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.7667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 5.8667, "curve": 0.25, "c3": 0.75}, {"time": 5.9333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 6.2333, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 6.6, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 6.9333, "curve": 0.25, "c3": 0.75}, {"time": 7.0333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 7.1, "curve": 0.25, "c3": 0.75}, {"time": 7.2, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 7.5667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.7667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.9333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.1, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 8.2667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 8.4333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "curve": 0.25, "c3": 0.75}, {"time": 8.6, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 8.7333, "curve": 0.25, "c3": 0.75}, {"time": 8.8333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 9, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 9.0667, "curve": 0.25, "c3": 0.75}, {"time": 9.1667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 9.2667, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 9.4333, "curve": 0.25, "c3": 0.75}, {"time": 9.5, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 9.7667, "curve": 0.25, "c3": 0.75}, {"time": 9.8667, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 9.9333, "curve": 0.25, "c3": 0.75}, {"time": 10.0333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 10.1667, "curve": 0.25, "c3": 0.75}, {"time": 10.2333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "curve": 0.25, "c3": 0.75}, {"time": 10.4333, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "curve": 0.25, "c3": 0.75}, {"time": 10.6, "angle": -158.71, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "图层 49": {"shear": [{"y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.3333, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.6667, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.6667, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.3333, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.6667, "y": -42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 9.3333, "y": -21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 10, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.6667, "y": -21}]}, "图层 50": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 4.1, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "curve": 0.25, "c3": 0.75}, {"time": 4.3667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 4.6, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 5.6, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.7667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 5.8667, "curve": 0.25, "c3": 0.75}, {"time": 5.9333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 6.2333, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 6.6, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 6.9333, "curve": 0.25, "c3": 0.75}, {"time": 7.0333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 7.1, "curve": 0.25, "c3": 0.75}, {"time": 7.2, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "curve": 0.25, "c3": 0.75}, {"time": 7.3667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 7.5667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.7667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.9333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 8.1, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 8.2667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 8.4333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "curve": 0.25, "c3": 0.75}, {"time": 8.6, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 8.7333, "curve": 0.25, "c3": 0.75}, {"time": 8.8333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 9, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 9.0667, "curve": 0.25, "c3": 0.75}, {"time": 9.1667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 9.2667, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 9.4333, "curve": 0.25, "c3": 0.75}, {"time": 9.5, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "curve": 0.25, "c3": 0.75}, {"time": 9.7, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 9.7667, "curve": 0.25, "c3": 0.75}, {"time": 9.8667, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 9.9333, "curve": 0.25, "c3": 0.75}, {"time": 10.0333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 10.1667, "curve": 0.25, "c3": 0.75}, {"time": 10.2333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "curve": 0.25, "c3": 0.75}, {"time": 10.4333, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "curve": 0.25, "c3": 0.75}, {"time": 10.6, "angle": 164.32, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "bone4": {"translate": [{"y": 70}], "scale": [{"x": 0.722, "y": 0.813}]}, "bone": {"translate": [{"x": -155.25, "y": -82.8, "curve": 0.31, "c3": 0.645, "c4": 0.35}, {"time": 0.2667, "x": -141.69, "y": -78.8, "curve": 0.316, "c2": 0.23, "c3": 0.651, "c4": 0.57}, {"time": 0.5333, "x": -109.67, "y": -82.8, "curve": 0.321, "c2": 0.28, "c3": 0.655, "c4": 0.62}, {"time": 0.8, "x": -70.63, "y": -78.8, "curve": 0.324, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 1.1, "x": -18.3, "y": -82.8, "curve": 0.328, "c2": 0.32, "c3": 0.662, "c4": 0.65}, {"time": 1.4, "x": 39.12, "y": -78.8, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 1.6333, "x": 85.54, "y": -82.8, "curve": 0.334, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 1.9333, "x": 146.1, "y": -80.8, "curve": 0.337, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 2.1667, "x": 191.71, "y": -82.8, "curve": 0.34, "c2": 0.36, "c3": 0.674, "c4": 0.69}, {"time": 2.4333, "x": 241.96, "y": -78.8, "curve": 0.342, "c2": 0.37, "c3": 0.676, "c4": 0.7}, {"time": 2.6667, "x": 280.84, "y": -82.8, "curve": 0.351, "c2": 0.42, "c3": 0.686, "c4": 0.76}, {"time": 3, "x": 325.52, "y": -78.8, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 3.3333, "x": 345.31, "y": -82.8}]}, "daizi": {"rotate": [{"angle": -3.4}]}, "daizi6": {"rotate": [{"angle": 1.48}]}, "daizi4": {"rotate": [{"angle": -0.71}]}, "daizi3": {"rotate": [{"angle": -1.75}]}, "daizi2": {"rotate": [{"angle": -2.68}]}, "daizi5": {"rotate": [{"angle": 0.38}]}, "daizi11": {"rotate": [{"angle": 4.17}]}, "daizi10": {"rotate": [{"angle": 4.52}]}, "daizi9": {"rotate": [{"angle": 4.17}]}, "daizi8": {"rotate": [{"angle": 3.44}]}, "daizi7": {"rotate": [{"angle": 2.52}]}, "ganzi": {"translate": [{"x": -45.73, "y": -234.75}]}, "ganzi15": {"translate": [{"x": -45.73, "y": -234.75}]}, "daizi12": {"rotate": [{"angle": -3.4}]}, "daizi22": {"rotate": [{"angle": 4.17}]}, "daizi21": {"rotate": [{"angle": 4.52}]}, "daizi20": {"rotate": [{"angle": 4.17}]}, "daizi19": {"rotate": [{"angle": 3.44}]}, "daizi18": {"rotate": [{"angle": 2.52}]}, "daizi17": {"rotate": [{"angle": 1.48}]}, "daizi16": {"rotate": [{"angle": 0.38}]}, "daizi15": {"rotate": [{"angle": -0.71}]}, "daizi14": {"rotate": [{"angle": -1.75}]}, "daizi13": {"rotate": [{"angle": -2.68}]}, "qq26": {"translate": [{"x": -50.29, "y": 54.66, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": -70.22, "y": 76.32, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2.1333, "x": -50.29, "y": 54.66, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "x": -70.22, "y": 76.32, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4.2667, "x": -50.29, "y": 54.66, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.9667, "curve": 0.25, "c3": 0.75}, {"time": 6.0333, "x": -70.22, "y": 76.32, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6.4, "x": -50.29, "y": 54.66, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 7.1, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "x": -70.22, "y": 76.32, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8.5333, "x": -50.29, "y": 54.66, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 9.2333, "curve": 0.25, "c3": 0.75}, {"time": 10.3, "x": -70.22, "y": 76.32, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 10.6667, "x": -50.29, "y": 54.66}]}, "qq21": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": -16.62, "y": 33.49, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "x": -16.62, "y": 33.49, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": -16.62, "y": 33.49, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "curve": 0.25, "c3": 0.75}, {"time": 7.4667, "x": -16.62, "y": 33.49, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "x": -16.62, "y": 33.49, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "qq22": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": -16.62, "y": 33.49, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "x": -16.62, "y": 33.49, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": -16.62, "y": 33.49, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "curve": 0.25, "c3": 0.75}, {"time": 7.4667, "x": -16.62, "y": 33.49, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "x": -16.62, "y": 33.49, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "qq25": {"translate": [{"x": -9.41, "y": 15.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": -33.16, "y": 54.78, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2.1333, "x": -9.41, "y": 15.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "x": -33.16, "y": 54.78, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4.2667, "x": -9.41, "y": 15.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.6333, "curve": 0.25, "c3": 0.75}, {"time": 5.7, "x": -33.16, "y": 54.78, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6.4, "x": -9.41, "y": 15.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.7667, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": -33.16, "y": 54.78, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8.5333, "x": -9.41, "y": 15.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "x": -33.16, "y": 54.78, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 10.6667, "x": -9.41, "y": 15.55}]}, "qq23": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": -16.62, "y": 33.49, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "x": -16.62, "y": 33.49, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": -16.62, "y": 33.49, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "curve": 0.25, "c3": 0.75}, {"time": 7.4667, "x": -16.62, "y": 33.49, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "x": -16.62, "y": 33.49, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "qq27": {"translate": [{"x": -97.11, "y": 94.22, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "x": -97.11, "y": 94.22, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "x": -97.11, "y": 94.22, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "x": -97.11, "y": 94.22, "curve": 0.25, "c3": 0.75}, {"time": 7.4667, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "x": -97.11, "y": 94.22, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "x": -97.11, "y": 94.22}]}, "qq24": {"translate": [{"x": -9.41, "y": 15.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": -33.16, "y": 54.78, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2.1333, "x": -9.41, "y": 15.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "x": -33.16, "y": 54.78, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4.2667, "x": -9.41, "y": 15.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.6333, "curve": 0.25, "c3": 0.75}, {"time": 5.7, "x": -33.16, "y": 54.78, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6.4, "x": -9.41, "y": 15.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.7667, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": -33.16, "y": 54.78, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8.5333, "x": -9.41, "y": 15.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "x": -33.16, "y": 54.78, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 10.6667, "x": -9.41, "y": 15.55}]}, "qq34": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": -16.62, "y": 33.49, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "x": -16.62, "y": 33.49, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": -16.62, "y": 33.49, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "curve": 0.25, "c3": 0.75}, {"time": 7.4667, "x": -16.62, "y": 33.49, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "x": -16.62, "y": 33.49, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "qq35": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": -16.62, "y": 33.49, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "x": -16.62, "y": 33.49, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": -16.62, "y": 33.49, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "curve": 0.25, "c3": 0.75}, {"time": 7.4667, "x": -16.62, "y": 33.49, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "x": -16.62, "y": 33.49, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "qq36": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": -16.62, "y": 33.49, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "x": -16.62, "y": 33.49, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": -16.62, "y": 33.49, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "curve": 0.25, "c3": 0.75}, {"time": 7.4667, "x": -16.62, "y": 33.49, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "x": -16.62, "y": 33.49, "curve": 0.25, "c3": 0.75}, {"time": 10.6667}]}, "qq37": {"translate": [{"x": -9.41, "y": 15.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": -33.16, "y": 54.78, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2.1333, "x": -9.41, "y": 15.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "x": -33.16, "y": 54.78, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4.2667, "x": -9.41, "y": 15.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.6333, "curve": 0.25, "c3": 0.75}, {"time": 5.7, "x": -33.16, "y": 54.78, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6.4, "x": -9.41, "y": 15.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.7667, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": -33.16, "y": 54.78, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8.5333, "x": -9.41, "y": 15.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "x": -33.16, "y": 54.78, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 10.6667, "x": -9.41, "y": 15.55}]}, "qq38": {"translate": [{"x": -9.41, "y": 15.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": -33.16, "y": 54.78, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2.1333, "x": -9.41, "y": 15.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "x": -33.16, "y": 54.78, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4.2667, "x": -9.41, "y": 15.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.6333, "curve": 0.25, "c3": 0.75}, {"time": 5.7, "x": -33.16, "y": 54.78, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6.4, "x": -9.41, "y": 15.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.7667, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": -33.16, "y": 54.78, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8.5333, "x": -9.41, "y": 15.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "x": -33.16, "y": 54.78, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 10.6667, "x": -9.41, "y": 15.55}]}, "qq39": {"translate": [{"x": -50.29, "y": 54.66, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": -70.22, "y": 76.32, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2.1333, "x": -50.29, "y": 54.66, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "x": -70.22, "y": 76.32, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4.2667, "x": -50.29, "y": 54.66, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.9667, "curve": 0.25, "c3": 0.75}, {"time": 6.0333, "x": -70.22, "y": 76.32, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6.4, "x": -50.29, "y": 54.66, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 7.1, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "x": -70.22, "y": 76.32, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8.5333, "x": -50.29, "y": 54.66, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 9.2333, "curve": 0.25, "c3": 0.75}, {"time": 10.3, "x": -70.22, "y": 76.32, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 10.6667, "x": -50.29, "y": 54.66}]}, "qq40": {"translate": [{"x": -97.11, "y": 94.22, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "x": -97.11, "y": 94.22, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "x": -97.11, "y": 94.22, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "x": -97.11, "y": 94.22, "curve": 0.25, "c3": 0.75}, {"time": 7.4667, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "x": -97.11, "y": 94.22, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "x": -97.11, "y": 94.22}]}}, "deform": {"default": {"c2people1": {"c2people1": [{"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "vertices": [9.30042, 11.98154, 15.53032, -24.85151, -29.19555, -32.41638, -35.4254, 4.41669], "curve": 0.25, "c3": 0.75}, {"time": 7.1}]}, "c2people2": {"c2people2": [{"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "vertices": [-22.42417, -22.38144, -15.39971, 16.65864, 32.00611, 8.129, 24.98169, -30.91115], "curve": 0.25, "c3": 0.75}, {"time": 7.1}]}}}}, "a05": {"slots": {"qizi": {"attachment": [{"name": null}]}, "c2hw2": {"attachment": [{"name": "c2hw2"}]}, "c1tz1": {"attachment": [{"name": "c1tz1"}]}, "qq3": {"attachment": [{"name": null}]}, "c12l": {"attachment": [{"name": "c12l"}]}, "c3people4": {"attachment": [{"name": null}]}, "ganzi2": {"attachment": [{"name": null}]}, "qq1": {"attachment": [{"name": null}]}, "qq2": {"attachment": [{"name": null}]}, "c1lang": {"attachment": [{"name": "c1lang"}]}, "c11l": {"attachment": [{"name": "c11l"}]}, "qq4": {"attachment": [{"name": null}]}, "daizi2": {"attachment": [{"name": null}]}, "daizi": {"attachment": [{"name": null}]}, "c1tz2": {"attachment": [{"name": "c1tz2"}]}, "ganzi": {"attachment": [{"name": null}]}, "c1ss": {"attachment": [{"name": "c1ss"}]}, "c2tz2l": {"attachment": [{"name": "c2tz2l"}]}, "c2hw": {"attachment": [{"name": "c2hw"}]}, "c3people3": {"attachment": [{"name": null}]}, "C103-0": {"attachment": [{"name": null}]}, "qizi3": {"attachment": [{"name": null}]}}, "bones": {"lv11": {"translate": [{"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "y": 254.77, "curve": 0.25, "c3": 0.75}, {"time": 1.1667}], "scale": [{"x": 0.245, "y": 0.01, "curve": "stepped"}, {"time": 0.4333, "x": 0.245, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 1.1667}]}, "lv12": {"translate": [{"time": 0.0667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4333, "y": 335.66, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8}], "scale": [{"x": 0.245, "y": 0.01, "curve": "stepped"}, {"time": 0.0667, "x": 0.245, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 0.8}]}, "lv13": {"translate": [{"time": 0.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "y": 302.41, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.0333}], "scale": [{"x": 0.245, "y": 0.01, "curve": "stepped"}, {"time": 0.3, "x": 0.245, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 1.0333}]}, "lv14": {"translate": [{"time": 0.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "y": 302.41, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.0333}], "scale": [{"x": 0.245, "y": 0.01, "curve": "stepped"}, {"time": 0.3, "x": 0.245, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 1.0333}]}, "lv15": {"translate": [{"time": 0.3667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.7333, "y": 162.84, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.1}], "scale": [{"x": 0.245, "y": 0.01, "curve": "stepped"}, {"time": 0.3667, "x": 0.245, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 1.1}]}, "lv4": {"translate": [{"y": -242.16, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 132.18, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}], "scale": [{"x": 0.245, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "lv16": {"translate": [{"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": 282.43, "curve": 0.25, "c3": 0.75}, {"time": 1.0333}], "scale": [{"x": 0.051, "y": 0.09, "curve": "stepped"}, {"time": 0.3, "x": 0.051, "y": 0.09, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 0.997, "y": 1.272, "curve": 0.25, "c3": 0.75}, {"time": 1.0333}]}, "lv17": {"translate": [{"time": 0.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.8667, "y": 219.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.2667}], "scale": [{"x": 0.191, "y": 0.15, "curve": "stepped"}, {"time": 0.5, "x": 0.191, "y": 0.15, "curve": 0.25, "c3": 0.75}, {"time": 1.2667}]}, "lv18": {"translate": [{"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "y": 235.22, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}], "scale": [{"x": 0.133, "y": 0.234, "curve": "stepped"}, {"time": 0.6, "x": 0.133, "y": 0.234, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "lv19": {"translate": [{"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "y": 313.79, "curve": 0.25, "c3": 0.75}, {"time": 1.1667}], "scale": [{"x": 0.052, "y": 0.092, "curve": "stepped"}, {"time": 0.4333, "x": 0.052, "y": 0.092, "curve": 0.25, "c3": 0.75}, {"time": 1.1667}]}, "lv5": {"translate": [{"y": -228.8, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 133.38, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}], "scale": [{"x": 0.568, "y": -0.011, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}}}, "a05-1": {"slots": {"c2hw2": {"attachment": [{"name": "c2hw2"}]}, "c1tz1": {"attachment": [{"name": "c1tz1"}]}, "qq3": {"attachment": [{"name": null}]}, "c12l": {"attachment": [{"name": "c12l"}]}, "c3people4": {"attachment": [{"name": null}]}, "qq1": {"attachment": [{"name": null}]}, "qq2": {"attachment": [{"name": null}]}, "c2people3": {"color": [{"color": "ffffff00"}], "attachment": [{"name": "c2people1"}]}, "c1lang": {"attachment": [{"name": "c1lang"}]}, "c11l": {"attachment": [{"name": "c11l"}]}, "qq4": {"attachment": [{"name": null}]}, "daizi2": {"attachment": [{"name": null}]}, "daizi": {"attachment": [{"name": null}]}, "c1tz2": {"attachment": [{"name": "c1tz2"}]}, "c2people8": {"color": [{"color": "ffffff00"}], "attachment": [{"name": "c2people2"}]}, "c2people6": {"color": [{"color": "ffffff00"}], "attachment": [{"name": "c2people2"}]}, "c1ss": {"attachment": [{"name": "c1ss"}]}, "c2tz2l": {"attachment": [{"name": "c2tz2l"}]}, "c2people4": {"color": [{"color": "ffffff00"}], "attachment": [{"name": "c2people1"}]}, "c2hw": {"attachment": [{"name": "c2hw"}]}, "c2people5": {"color": [{"color": "ffffff00"}], "attachment": [{"name": "c2people1"}]}, "c3people3": {"attachment": [{"name": null}]}, "C103-0": {"attachment": [{"name": null}]}, "c2people7": {"color": [{"color": "ffffff00"}], "attachment": [{"name": "c2people2"}]}}, "bones": {"图层 45": {"shear": [{"y": -21}]}, "bone3": {"translate": [{"x": -418.18}]}, "图层 39": {"translate": [{"x": -150.48, "y": 38.54}]}, "图层 48": {"rotate": [{"angle": -113.67}]}, "图层 50": {"rotate": [{"angle": 117.69}]}, "bone11": {"translate": [{"x": -0.37, "y": 11.25}]}, "bone4": {"translate": [{"x": -117.9, "y": 84.21}]}, "bone": {"translate": [{"x": -155.25, "y": -82.8}]}, "ganzi2": {"rotate": [{"angle": 1.76}], "translate": [{"x": 13.35, "y": -4.22}]}, "daizi": {"rotate": [{"angle": -3.4}]}, "daizi11": {"rotate": [{"angle": 4.17}]}, "daizi10": {"rotate": [{"angle": 4.52}]}, "daizi9": {"rotate": [{"angle": 4.17}]}, "daizi8": {"rotate": [{"angle": 3.44}]}, "daizi7": {"rotate": [{"angle": 2.52}]}, "daizi6": {"rotate": [{"angle": 1.48}]}, "daizi5": {"rotate": [{"angle": 0.38}]}, "daizi4": {"rotate": [{"angle": -0.71}]}, "daizi3": {"rotate": [{"angle": -1.75}]}, "daizi2": {"rotate": [{"angle": -2.68}]}, "ganzi16": {"rotate": [{"angle": 1.76}], "translate": [{"x": 13.35, "y": -4.22}]}, "daizi12": {"rotate": [{"angle": -3.4}]}, "daizi22": {"rotate": [{"angle": 4.17}]}, "daizi21": {"rotate": [{"angle": 4.52}]}, "daizi20": {"rotate": [{"angle": 4.17}]}, "daizi19": {"rotate": [{"angle": 3.44}]}, "daizi18": {"rotate": [{"angle": 2.52}]}, "daizi17": {"rotate": [{"angle": 1.48}]}, "daizi16": {"rotate": [{"angle": 0.38}]}, "daizi15": {"rotate": [{"angle": -0.71}]}, "daizi14": {"rotate": [{"angle": -1.75}]}, "daizi13": {"rotate": [{"angle": -2.68}]}, "ganzi6": {"translate": [{"x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2.1333, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4.2667, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.6333, "curve": 0.25, "c3": 0.75}, {"time": 5.7, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6.4, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.7667, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8.5333, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 10.6667, "x": -9.74, "y": -11.89}]}, "ganzi14": {"translate": [{"x": -20.17, "y": -88.61, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "x": -28.17, "y": -123.72, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2.1333, "x": -20.17, "y": -88.61, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.5, "x": -28.17, "y": -123.72, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4.2667, "x": -20.17, "y": -88.61, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.6333, "x": -28.17, "y": -123.72, "curve": 0.25, "c3": 0.75}, {"time": 5.7, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6.4, "x": -20.17, "y": -88.61, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.7667, "x": -28.17, "y": -123.72, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8.5333, "x": -20.17, "y": -88.61, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 8.9, "x": -28.17, "y": -123.72, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 10.6667, "x": -20.17, "y": -88.61}]}, "ganzi23": {"translate": [{"x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2.1333, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4.2667, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.9667, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6.4, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 7.1333, "curve": 0.25, "c3": 0.75}, {"time": 8.2, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8.5333, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 9.2667, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 10.6667, "x": -46.84, "y": -43.05}]}, "ganzi11": {"translate": [{"x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2.1333, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4.2667, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.9667, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6.4, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 7.1333, "curve": 0.25, "c3": 0.75}, {"time": 8.2, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8.5333, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 9.2667, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 10.6667, "x": -46.84, "y": -43.05}]}, "ganzi21": {"translate": [{"x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": -34.3, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2.1333, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "x": -34.3, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4.2667, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.6333, "curve": 0.25, "c3": 0.75}, {"time": 5.7, "x": -34.3, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6.4, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.7667, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": -34.3, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8.5333, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "x": -34.3, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 10.6667, "x": -9.74, "y": -11.89}]}, "ganzi24": {"translate": [{"x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2.1333, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4.2667, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.9667, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6.4, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 7.1333, "curve": 0.25, "c3": 0.75}, {"time": 8.2, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8.5333, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 9.2667, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 10.6667, "x": -46.84, "y": -43.05}]}, "ganzi25": {"translate": [{"x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2.1333, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4.2667, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.9667, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6.4, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 7.1333, "curve": 0.25, "c3": 0.75}, {"time": 8.2, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8.5333, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 9.2667, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 10.6667, "x": -46.84, "y": -43.05}]}, "ganzi12": {"translate": [{"x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 7.4667, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "x": -57.27, "y": -99.95}]}, "ganzi8": {"translate": [{"x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2.1333, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4.2667, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.6333, "curve": 0.25, "c3": 0.75}, {"time": 5.7, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6.4, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.7667, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8.5333, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 10.6667, "x": -9.74, "y": -11.89}]}, "ganzi20": {"translate": [{"x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2.1333, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4.2667, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.6333, "curve": 0.25, "c3": 0.75}, {"time": 5.7, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6.4, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.7667, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8.5333, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 10.6667, "x": -9.74, "y": -11.89}]}, "ganzi22": {"translate": [{"x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2.1333, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4.2667, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.6333, "curve": 0.25, "c3": 0.75}, {"time": 5.7, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6.4, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.7667, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8.5333, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 10.6667, "x": -9.74, "y": -11.89}]}, "ganzi9": {"translate": [{"x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2.1333, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4.2667, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.9667, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6.4, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 7.1333, "curve": 0.25, "c3": 0.75}, {"time": 8.2, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8.5333, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 9.2667, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 10.6667, "x": -46.84, "y": -43.05}]}, "ganzi10": {"translate": [{"x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2.1333, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4.2667, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.9667, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6.4, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 7.1333, "curve": 0.25, "c3": 0.75}, {"time": 8.2, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8.5333, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 9.2667, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 10.6667, "x": -46.84, "y": -43.05}]}, "ganzi26": {"translate": [{"x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 7.4667, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "x": -57.27, "y": -99.95}]}, "ganzi13": {"translate": [{"x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 7.4667, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "x": -57.27, "y": -99.95}]}, "ganzi7": {"translate": [{"x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": -34.3, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2.1333, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "x": -34.3, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4.2667, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.6333, "curve": 0.25, "c3": 0.75}, {"time": 5.7, "x": -34.3, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6.4, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.7667, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": -34.3, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8.5333, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "x": -34.3, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 10.6667, "x": -9.74, "y": -11.89}]}, "ganzi27": {"translate": [{"x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 7.4667, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "x": -57.27, "y": -99.95}]}, "ganzi28": {"translate": [{"x": -20.17, "y": -88.61, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "x": -28.17, "y": -123.72, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2.1333, "x": -20.17, "y": -88.61, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.5, "x": -28.17, "y": -123.72, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4.2667, "x": -20.17, "y": -88.61, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.6333, "x": -28.17, "y": -123.72, "curve": 0.25, "c3": 0.75}, {"time": 5.7, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6.4, "x": -20.17, "y": -88.61, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.7667, "x": -28.17, "y": -123.72, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8.5333, "x": -20.17, "y": -88.61, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 8.9, "x": -28.17, "y": -123.72, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 10.6667, "x": -20.17, "y": -88.61}]}}, "drawOrder": [{"offsets": [{"slot": "c2people2", "offset": -4}]}]}, "a06": {"slots": {"qizi": {"attachment": [{"name": null}]}, "c2hw2": {"attachment": [{"name": "c2hw2"}]}, "c1tz1": {"attachment": [{"name": "c1tz1"}]}, "qq3": {"attachment": [{"name": null}]}, "c12l": {"attachment": [{"name": "c12l"}]}, "ganzi2": {"attachment": [{"name": null}]}, "qq1": {"attachment": [{"name": null}]}, "qq2": {"attachment": [{"name": null}]}, "c1lang": {"attachment": [{"name": "c1lang"}]}, "c3tz2_2": {"attachment": [{"name": "c3tz2_2"}]}, "c11l": {"attachment": [{"name": "c11l"}]}, "qq4": {"attachment": [{"name": null}]}, "daizi2": {"attachment": [{"name": null}]}, "daizi": {"attachment": [{"name": null}]}, "c1tz2": {"attachment": [{"name": "c1tz2"}]}, "c33l": {"attachment": [{"name": "c33l"}]}, "c3tz2_1": {"attachment": [{"name": "c3tz2_1"}]}, "ganzi": {"attachment": [{"name": null}]}, "c1ss": {"attachment": [{"name": "c1ss"}]}, "c2tz2l": {"attachment": [{"name": "c2tz2l"}]}, "c2hw": {"attachment": [{"name": "c2hw"}]}, "C103-0": {"attachment": [{"name": null}]}, "qizi3": {"attachment": [{"name": null}]}}, "bones": {"lv11": {"translate": [{"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "y": 254.77, "curve": 0.25, "c3": 0.75}, {"time": 1.2}], "scale": [{"x": 0.245, "y": 0.01, "curve": "stepped"}, {"time": 0.4333, "x": 0.245, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 1.2}]}, "lv12": {"translate": [{"time": 0.0667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4333, "y": 335.66, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333}], "scale": [{"x": 0.245, "y": 0.01, "curve": "stepped"}, {"time": 0.0667, "x": 0.245, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}]}, "lv13": {"translate": [{"time": 0.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "y": 302.41, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.0333}], "scale": [{"x": 0.245, "y": 0.01, "curve": "stepped"}, {"time": 0.3, "x": 0.245, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 1.0333}]}, "lv14": {"translate": [{"time": 0.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "y": 302.41, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.0333}], "scale": [{"x": 0.245, "y": 0.01, "curve": "stepped"}, {"time": 0.3, "x": 0.245, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 1.0333}]}, "lv15": {"translate": [{"time": 0.3667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.7333, "y": 162.84, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.1333}], "scale": [{"x": 0.245, "y": 0.01, "curve": "stepped"}, {"time": 0.3667, "x": 0.245, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 1.1333}]}, "lv4": {"translate": [{"y": -242.16, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 132.18, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}], "scale": [{"x": 0.245, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "lv16": {"translate": [{"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": 282.43, "curve": 0.25, "c3": 0.75}, {"time": 1.0333}], "scale": [{"x": 0.051, "y": 0.09, "curve": "stepped"}, {"time": 0.3, "x": 0.051, "y": 0.09, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 0.997, "y": 1.272, "curve": 0.25, "c3": 0.75}, {"time": 1.0333}]}, "lv17": {"translate": [{"time": 0.5333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.9, "y": 219.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.2667}], "scale": [{"x": 0.191, "y": 0.15, "curve": "stepped"}, {"time": 0.5333, "x": 0.191, "y": 0.15, "curve": 0.25, "c3": 0.75}, {"time": 1.2667}]}, "lv18": {"translate": [{"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "y": 235.22, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}], "scale": [{"x": 0.133, "y": 0.234, "curve": "stepped"}, {"time": 0.6, "x": 0.133, "y": 0.234, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "lv19": {"translate": [{"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "y": 313.79, "curve": 0.25, "c3": 0.75}, {"time": 1.2}], "scale": [{"x": 0.052, "y": 0.092, "curve": "stepped"}, {"time": 0.4333, "x": 0.052, "y": 0.092, "curve": 0.25, "c3": 0.75}, {"time": 1.2}]}, "lv5": {"translate": [{"y": -228.8, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 133.38, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}], "scale": [{"x": 0.568, "y": -0.011, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "lv6": {"translate": [{"y": -235.44, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "y": 199.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7333}], "scale": [{"x": 0.187, "y": 0.076, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "lv22": {"translate": [{"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "y": 259.41, "curve": 0.25, "c3": 0.75}, {"time": 1.2667}], "scale": [{"x": 0.187, "y": 0.076, "curve": "stepped"}, {"time": 0.5333, "x": 0.187, "y": 0.076, "curve": 0.25, "c3": 0.75}, {"time": 1.2667}]}, "lv21": {"translate": [{"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "y": 259.41, "curve": 0.25, "c3": 0.75}, {"time": 1.2667}], "scale": [{"x": 0.187, "y": 0.076, "curve": "stepped"}, {"time": 0.5333, "x": 0.187, "y": 0.076, "curve": 0.25, "c3": 0.75}, {"time": 1.2667}]}, "lv20": {"translate": [{"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "y": 259.41, "curve": 0.25, "c3": 0.75}, {"time": 1.2667}], "scale": [{"x": 0.187, "y": 0.076, "curve": "stepped"}, {"time": 0.5333, "x": 0.187, "y": 0.076, "curve": 0.25, "c3": 0.75}, {"time": 1.2667}]}}}, "a06-1": {"slots": {"图层 31": {"color": [{"color": "ff9191ff"}]}, "c2hw2": {"attachment": [{"name": "c2hw2"}]}, "c1tz1": {"attachment": [{"name": "c1tz1"}]}, "qq3": {"attachment": [{"name": null}]}, "c2people8": {"color": [{"color": "ffffff00"}], "attachment": [{"name": "c2people2"}]}, "c12l": {"attachment": [{"name": "c12l"}]}, "qq1": {"attachment": [{"name": null}]}, "qq2": {"attachment": [{"name": null}]}, "c2people3": {"color": [{"color": "ffffff00"}], "attachment": [{"name": "c2people1"}]}, "c1lang": {"attachment": [{"name": "c1lang"}]}, "c3tz2_2": {"attachment": [{"name": "c3tz2_2"}]}, "c11l": {"attachment": [{"name": "c11l"}]}, "图层 32": {"color": [{"color": "ff9191ff"}]}, "qq4": {"attachment": [{"name": null}]}, "图层 30": {"color": [{"color": "ff9191ff"}]}, "c1tz2": {"attachment": [{"name": "c1tz2"}]}, "c33l": {"attachment": [{"name": "c33l"}]}, "c3tz2_1": {"attachment": [{"name": "c3tz2_1"}]}, "c2people6": {"color": [{"color": "ffffff00"}], "attachment": [{"name": "c2people2"}]}, "c1ss": {"attachment": [{"name": "c1ss"}]}, "c2tz2l": {"attachment": [{"name": "c2tz2l"}]}, "c2people4": {"color": [{"color": "ffffff00"}], "attachment": [{"name": "c2people1"}]}, "c2hw": {"attachment": [{"name": "c2hw"}]}, "c2people5": {"color": [{"color": "ffffff00"}], "attachment": [{"name": "c2people1"}]}, "C103-0": {"attachment": [{"name": null}]}, "c2people7": {"color": [{"color": "ffffff00"}], "attachment": [{"name": "c2people2"}]}}, "bones": {"lv6": {"scale": [{"x": 0.911}]}, "图层 44": {"rotate": [{"angle": -158.71}]}, "图层 46": {"rotate": [{"angle": 164.32}]}, "图层 38": {"translate": [{"x": -146.81, "y": 38.44}]}, "图层 45": {"shear": [{"y": -1.72}]}, "bone10": {"translate": [{"x": -0.1, "y": 3.21}]}, "bone": {"translate": [{"x": -155.25, "y": -82.8}]}, "图层 49": {"shear": [{"y": -21}]}, "bone4": {"scale": [{"x": 0.889}]}, "bone2": {"translate": [{"x": 248.43, "y": -101.06}], "scale": [{"x": 0.889}]}, "ganzi2": {"rotate": [{"angle": 1.76}], "translate": [{"x": 13.35, "y": -4.22}]}, "daizi": {"rotate": [{"angle": -3.4}]}, "daizi11": {"rotate": [{"angle": 4.17}]}, "daizi10": {"rotate": [{"angle": 4.52}]}, "daizi9": {"rotate": [{"angle": 4.17}]}, "daizi8": {"rotate": [{"angle": 3.44}]}, "daizi7": {"rotate": [{"angle": 2.52}]}, "daizi6": {"rotate": [{"angle": 1.48}]}, "daizi5": {"rotate": [{"angle": 0.38}]}, "daizi4": {"rotate": [{"angle": -0.71}]}, "daizi3": {"rotate": [{"angle": -1.75}]}, "daizi2": {"rotate": [{"angle": -2.68}]}, "ganzi16": {"rotate": [{"angle": 1.76}], "translate": [{"x": 13.35, "y": -4.22}]}, "daizi12": {"rotate": [{"angle": -3.4}]}, "daizi22": {"rotate": [{"angle": 4.17}]}, "daizi21": {"rotate": [{"angle": 4.52}]}, "daizi20": {"rotate": [{"angle": 4.17}]}, "daizi19": {"rotate": [{"angle": 3.44}]}, "daizi18": {"rotate": [{"angle": 2.52}]}, "daizi17": {"rotate": [{"angle": 1.48}]}, "daizi16": {"rotate": [{"angle": 0.38}]}, "daizi15": {"rotate": [{"angle": -0.71}]}, "daizi14": {"rotate": [{"angle": -1.75}]}, "daizi13": {"rotate": [{"angle": -2.68}]}, "ganzi6": {"translate": [{"x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2.1333, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4.2667, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.6333, "curve": 0.25, "c3": 0.75}, {"time": 5.7, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6.4, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.7667, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8.5333, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 10.6667, "x": -9.74, "y": -11.89}]}, "ganzi14": {"translate": [{"x": -20.17, "y": -88.61, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "x": -28.17, "y": -123.72, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2.1333, "x": -20.17, "y": -88.61, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.5, "x": -28.17, "y": -123.72, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4.2667, "x": -20.17, "y": -88.61, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.6333, "x": -28.17, "y": -123.72, "curve": 0.25, "c3": 0.75}, {"time": 5.7, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6.4, "x": -20.17, "y": -88.61, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.7667, "x": -28.17, "y": -123.72, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8.5333, "x": -20.17, "y": -88.61, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 8.9, "x": -28.17, "y": -123.72, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 10.6667, "x": -20.17, "y": -88.61}]}, "ganzi23": {"translate": [{"x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2.1333, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4.2667, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.9667, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6.4, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 7.1333, "curve": 0.25, "c3": 0.75}, {"time": 8.2, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8.5333, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 9.2667, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 10.6667, "x": -46.84, "y": -43.05}]}, "ganzi11": {"translate": [{"x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2.1333, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4.2667, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.9667, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6.4, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 7.1333, "curve": 0.25, "c3": 0.75}, {"time": 8.2, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8.5333, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 9.2667, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 10.6667, "x": -46.84, "y": -43.05}]}, "ganzi21": {"translate": [{"x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": -34.3, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2.1333, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "x": -34.3, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4.2667, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.6333, "curve": 0.25, "c3": 0.75}, {"time": 5.7, "x": -34.3, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6.4, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.7667, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": -34.3, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8.5333, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "x": -34.3, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 10.6667, "x": -9.74, "y": -11.89}]}, "ganzi24": {"translate": [{"x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2.1333, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4.2667, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.9667, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6.4, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 7.1333, "curve": 0.25, "c3": 0.75}, {"time": 8.2, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8.5333, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 9.2667, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 10.6667, "x": -46.84, "y": -43.05}]}, "ganzi25": {"translate": [{"x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2.1333, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4.2667, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.9667, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6.4, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 7.1333, "curve": 0.25, "c3": 0.75}, {"time": 8.2, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8.5333, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 9.2667, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 10.6667, "x": -46.84, "y": -43.05}]}, "ganzi12": {"translate": [{"x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 7.4667, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "x": -57.27, "y": -99.95}]}, "ganzi8": {"translate": [{"x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2.1333, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4.2667, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.6333, "curve": 0.25, "c3": 0.75}, {"time": 5.7, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6.4, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.7667, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8.5333, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 10.6667, "x": -9.74, "y": -11.89}]}, "ganzi20": {"translate": [{"x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2.1333, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4.2667, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.6333, "curve": 0.25, "c3": 0.75}, {"time": 5.7, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6.4, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.7667, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8.5333, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 10.6667, "x": -9.74, "y": -11.89}]}, "ganzi22": {"translate": [{"x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2.1333, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4.2667, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.6333, "curve": 0.25, "c3": 0.75}, {"time": 5.7, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6.4, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.7667, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8.5333, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "x": -34.31, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 10.6667, "x": -9.74, "y": -11.89}]}, "ganzi9": {"translate": [{"x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2.1333, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4.2667, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.9667, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6.4, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 7.1333, "curve": 0.25, "c3": 0.75}, {"time": 8.2, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8.5333, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 9.2667, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 10.6667, "x": -46.84, "y": -43.05}]}, "ganzi10": {"translate": [{"x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2.1333, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4.2667, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.9667, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6.4, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 7.1333, "curve": 0.25, "c3": 0.75}, {"time": 8.2, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8.5333, "x": -46.84, "y": -43.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 9.2667, "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "x": -65.4, "y": -60.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 10.6667, "x": -46.84, "y": -43.05}]}, "ganzi26": {"translate": [{"x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 7.4667, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "x": -57.27, "y": -99.95}]}, "ganzi13": {"translate": [{"x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 7.4667, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "x": -57.27, "y": -99.95}]}, "ganzi7": {"translate": [{"x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": -34.3, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2.1333, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "x": -34.3, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4.2667, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.6333, "curve": 0.25, "c3": 0.75}, {"time": 5.7, "x": -34.3, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6.4, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.7667, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": -34.3, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8.5333, "x": -9.74, "y": -11.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "x": -34.3, "y": -41.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 10.6667, "x": -9.74, "y": -11.89}]}, "ganzi27": {"translate": [{"x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 7.4667, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "x": -57.27, "y": -99.95, "curve": 0.25, "c3": 0.75}, {"time": 9.6, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "x": -57.27, "y": -99.95}]}, "ganzi28": {"translate": [{"x": -20.17, "y": -88.61, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "x": -28.17, "y": -123.72, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2.1333, "x": -20.17, "y": -88.61, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.5, "x": -28.17, "y": -123.72, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4.2667, "x": -20.17, "y": -88.61, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.6333, "x": -28.17, "y": -123.72, "curve": 0.25, "c3": 0.75}, {"time": 5.7, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6.4, "x": -20.17, "y": -88.61, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.7667, "x": -28.17, "y": -123.72, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8.5333, "x": -20.17, "y": -88.61, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 8.9, "x": -28.17, "y": -123.72, "curve": 0.25, "c3": 0.75}, {"time": 9.9667, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 10.6667, "x": -20.17, "y": -88.61}]}}, "drawOrder": [{"offsets": [{"slot": "c2people2", "offset": -4}]}]}}}