{"skeleton": {"hash": "GvPg+IJxu4j3puxP1Dv8COGl80k=", "spine": "3.8.75", "images": "./skill2/", "audio": "./skill2/"}, "bones": [{"name": "root", "rotation": 0.02}, {"name": "bao", "parent": "root"}, {"name": "bao3", "parent": "root"}, {"name": "bao5", "parent": "root"}, {"name": "dd", "parent": "root", "scaleX": 0.741, "scaleY": 0.741}, {"name": "dq", "parent": "root", "scaleX": 0.741, "scaleY": 0.741}, {"name": "dq2", "parent": "root", "scaleX": 0.741, "scaleY": 0.741}, {"name": "dq3", "parent": "root", "scaleX": 0.741, "scaleY": 0.741}, {"name": "gz", "parent": "root"}, {"name": "gz2", "parent": "root"}], "slots": [{"name": "ef/g2", "bone": "gz"}, {"name": "ef/quan12", "bone": "dq2"}, {"name": "ef/tx_hb02", "bone": "bao"}, {"name": "ef/tx_hb3", "bone": "bao3"}, {"name": "ef/tx_hb5", "bone": "bao5"}, {"name": "ef/dd_4", "bone": "dd", "blend": "additive"}, {"name": "ef/quan33", "bone": "dq", "blend": "additive"}, {"name": "ef/quan34", "bone": "dq3", "blend": "additive"}, {"name": "ef/g3", "bone": "gz2", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"ef/g2": {"ef/g2": {"y": 58.29, "width": 100, "height": 147}}, "ef/g3": {"ef/g2": {"y": 58.29, "width": 100, "height": 147}}, "ef/quan33": {"ef/quan33": {"width": 150, "height": 150}}, "ef/quan12": {"ef/quan12": {"width": 150, "height": 150}}, "ef/dd_4": {"ef/dd_4": {"width": 50, "height": 50}}, "ef/tx_hb02": {"ef/tx_hb02": {"width": 50, "height": 100}, "ef/tx_hb03": {"width": 50, "height": 100}, "ef/tx_hb04": {"width": 50, "height": 100}, "ef/tx_hb05": {"width": 50, "height": 100}, "ef/tx_hb06": {"width": 50, "height": 100}, "ef/tx_hb07": {"width": 50, "height": 100}, "ef/tx_hb08": {"width": 50, "height": 100}}, "ef/tx_hb3": {"ef/tx_hb02": {"width": 50, "height": 100}, "ef/tx_hb03": {"width": 50, "height": 100}, "ef/tx_hb04": {"width": 50, "height": 100}, "ef/tx_hb05": {"width": 50, "height": 100}, "ef/tx_hb06": {"width": 50, "height": 100}, "ef/tx_hb07": {"width": 50, "height": 100}, "ef/tx_hb08": {"width": 50, "height": 100}}, "ef/tx_hb5": {"ef/tx_hb02": {"width": 50, "height": 100}, "ef/tx_hb03": {"width": 50, "height": 100}, "ef/tx_hb04": {"width": 50, "height": 100}, "ef/tx_hb05": {"width": 50, "height": 100}, "ef/tx_hb06": {"width": 50, "height": 100}, "ef/tx_hb07": {"width": 50, "height": 100}, "ef/tx_hb08": {"width": 50, "height": 100}}, "ef/quan34": {"ef/quan33": {"width": 150, "height": 150}}}}], "events": {"appear": {}}, "animations": {"animation": {"slots": {"ef/quan34": {"color": [{"time": 0.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3333, "color": "ffffffff", "curve": 0.808, "c2": 0.28, "c3": 0.482, "c4": 0.85}, {"time": 0.6333, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 0.0333, "name": "ef/quan33"}]}, "ef/tx_hb5": {"attachment": [{"name": null}, {"time": 0.0333, "name": "ef/tx_hb02"}, {"time": 0.1, "name": "ef/tx_hb03"}, {"time": 0.1667, "name": "ef/tx_hb04"}, {"time": 0.2333, "name": "ef/tx_hb05"}, {"time": 0.3, "name": "ef/tx_hb06"}, {"time": 0.3667, "name": "ef/tx_hb07"}, {"time": 0.4333, "name": "ef/tx_hb08"}, {"time": 0.5, "name": null}]}, "ef/g3": {"color": [{"time": 0.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4667, "color": "ffffffff", "curve": 0.808, "c2": 0.28, "c3": 0.482, "c4": 0.85}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 0.0333, "name": "ef/g2"}]}, "ef/tx_hb02": {"attachment": [{"name": null}, {"time": 0.0333, "name": "ef/tx_hb02"}, {"time": 0.1, "name": "ef/tx_hb03"}, {"time": 0.1667, "name": "ef/tx_hb04"}, {"time": 0.2333, "name": "ef/tx_hb05"}, {"time": 0.3, "name": "ef/tx_hb06"}, {"time": 0.3667, "name": "ef/tx_hb07"}, {"time": 0.4333, "name": "ef/tx_hb08"}, {"time": 0.5, "name": null}]}, "ef/tx_hb3": {"attachment": [{"name": null}, {"time": 0.0333, "name": "ef/tx_hb02"}, {"time": 0.1, "name": "ef/tx_hb03"}, {"time": 0.1667, "name": "ef/tx_hb04"}, {"time": 0.2333, "name": "ef/tx_hb05"}, {"time": 0.3, "name": "ef/tx_hb06"}, {"time": 0.3667, "name": "ef/tx_hb07"}, {"time": 0.4333, "name": "ef/tx_hb08"}, {"time": 0.5, "name": null}]}, "ef/g2": {"color": [{"time": 0.0333, "color": "ffffffff", "curve": 0.808, "c2": 0.28, "c3": 0.482, "c4": 0.85}, {"time": 0.3333, "color": "ffffffaa", "curve": 0.808, "c2": 0.28, "c3": 0.482, "c4": 0.85}, {"time": 0.7333, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 0.0333, "name": "ef/g2"}]}, "ef/quan12": {"color": [{"time": 0.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff", "curve": 0.808, "c2": 0.28, "c3": 0.482, "c4": 0.85}, {"time": 0.6, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 0.0333, "name": "ef/quan12"}]}, "ef/quan33": {"color": [{"time": 0.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.1667, "color": "ffffffff", "curve": 0.808, "c2": 0.28, "c3": 0.482, "c4": 0.85}, {"time": 0.4667, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 0.0333, "name": "ef/quan33"}]}, "ef/dd_4": {"color": [{"time": 0.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.1333, "color": "ffffffff", "curve": 0.808, "c2": 0.28, "c3": 0.482, "c4": 0.85}, {"time": 0.7667, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 0.0333, "name": "ef/dd_4"}]}}, "bones": {"dd": {"scale": [{"time": 0.0333, "x": 9.791, "y": 5.429, "curve": 0.808, "c2": 0.28, "c3": 0.482, "c4": 0.85}, {"time": 0.7667, "x": 33.839, "y": 18.763}]}, "gz": {"scale": [{"time": 0.0333, "x": 2.531, "y": 1.232, "curve": 0.808, "c2": 0.28, "c3": 0.482, "c4": 0.85}, {"time": 0.2, "x": 3.402, "y": 2.009, "curve": 0.808, "c2": 0.28, "c3": 0.482, "c4": 0.85}, {"time": 0.4333, "x": 3.402, "y": 5.082, "curve": 0.808, "c2": 0.28, "c3": 0.482, "c4": 0.85}, {"time": 0.7333, "x": 4.105, "y": 5.257}]}, "gz2": {"scale": [{"time": 0.0333, "x": 2.531, "y": 1.232, "curve": 0.808, "c2": 0.28, "c3": 0.482, "c4": 0.85}, {"time": 0.2, "x": 2.817, "y": 1.133, "curve": 0.808, "c2": 0.28, "c3": 0.482, "c4": 0.85}, {"time": 0.6667, "x": 0.047, "y": 4.911}]}, "dq2": {"scale": [{"time": 0.0333, "x": 2.087, "y": 0.758, "curve": 0.808, "c2": 0.28, "c3": 0.482, "c4": 0.85}, {"time": 0.6, "x": 3.663, "y": 1.33}]}, "dq": {"scale": [{"time": 0.0333, "x": 1.261, "y": 0.458, "curve": 0.808, "c2": 0.28, "c3": 0.482, "c4": 0.85}, {"time": 0.4667, "x": 3.314, "y": 1.204}]}, "dq3": {"scale": [{"time": 0.0333, "x": 1.261, "y": 0.458, "curve": 0.808, "c2": 0.28, "c3": 0.482, "c4": 0.85}, {"time": 0.6333, "x": 2.826, "y": 1.026}]}, "bao": {"rotate": [{"time": 0.0333, "angle": 18.92}], "translate": [{"time": 0.0333, "x": -80.62, "y": 98.53}], "scale": [{"time": 0.0333, "x": 2.43, "y": 2.43}]}, "bao3": {"rotate": [{"time": 0.0333, "angle": 74.5}], "translate": [{"time": 0.0333, "x": -100.33, "y": 7.58}], "scale": [{"time": 0.0333, "x": 2.144, "y": 2.144}]}, "bao5": {"rotate": [{"time": 0.0333, "angle": -60.21}], "translate": [{"time": 0.0333, "x": 105.84, "y": 33.35}], "scale": [{"time": 0.0333, "x": 2.217, "y": 2.217}]}}, "events": [{"time": 0.4, "name": "appear"}]}}}