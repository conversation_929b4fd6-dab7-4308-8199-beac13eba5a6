import { _decorator, Component, Node } from "cc";
import { Recording, RecordingMap } from "../../GameScrpit/lib/ui/recordingMap";
const { ccclass, property } = _decorator;

/**
 *  限时任务路由
 */
@ccclass("TimeTestRoute")
export class TimeTestRoute {
  /**
   * 路由表
   */
  routeTables: Recording[] = [];

  /**
   *  初始化路由表
   */
  public async init() {
    //遍历路由表
    this.routeTables.forEach((item) => {
      // 注册路由
      RecordingMap.instance.addRecording(item.uiName, item);
    });
  }
}
