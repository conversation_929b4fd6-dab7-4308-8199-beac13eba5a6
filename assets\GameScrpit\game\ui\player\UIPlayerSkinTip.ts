import { _decorator, Component, Label, Node, RichText } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { JsonMgr } from "../../mgr/JsonMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
const { ccclass, property } = _decorator;

@ccclass("UIPlayerSkinTip")
export class UIPlayerSkinTip extends UINode {
  protected _openAct: boolean = true;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_PLAYER}?prefab/ui/UIPlayerSkinTip`;
  }

  private _info: any;
  public init(args: any): void {
    super.init(args);
    this._info = args.info;
  }

  protected onEvtShow(): void {
    this.setUnlockTip();
    this.setAttrTip();
  }

  private setUnlockTip() {
    if (this._info.unlock.length < 2) {
      this.getNode("rich_unlock").getComponent(RichText).string = `<color=#dfeaff>默认解锁</color>`;
      return;
    }
    let type = this._info.unlock[0];
    let param = this._info.unlock[1];
    switch (type) {
      case 0:
        this.getNode("rich_unlock").getComponent(RichText).string = `<color=#dfeaff>默认解锁</color>`;
        break;

      case 1:
        this.getNode("rich_unlock").getComponent(
          RichText
        ).string = `<color=#dfeaff>身份等级达到</color><color=#eda03e>${param}</color><color=#dfeaff>级，永久有效;</color>`;
        break;

      case 2:
        this.getNode("rich_unlock").getComponent(RichText).string = `<color=#dfeaff>${this._info.source}</color>`;
        break;

      case 3:
        this.getNode("rich_unlock").getComponent(RichText).string = this._info.source;
        break;

      default:
        break;
    }
  }

  private setAttrTip() {
    let db = JsonMgr.instance.jsonList.c_attribute;
    let attrAdd = this._info.attrAdd;

    if (attrAdd.length <= 0) {
      this.getNode("rich_addAttr").getComponent(RichText).string = `<color=#dfeaff>无</color>`;
      return;
    }

    let str = ``;

    for (let i = 0; i < attrAdd.length; i++) {
      let info = db[attrAdd[i][0]];
      str += `<color=#dfeaff>${info.name}</color>`;
      if (info.type1 == 1) {
        str += `<color=#eda03e>+${attrAdd[i][1]}</color>`;
      } else {
        str += `<color=#eda03e>+${attrAdd[i][1]}%</color>`;
      }
    }

    this.getNode("rich_addAttr").getComponent(RichText).string = str;
  }

  private on_click_btn_close() {
    UIMgr.instance.back();
  }
}
