import { _decorator } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import { BadgeMgr, BadgeType } from "../../mgr/BadgeMgr";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { HeroAudioName } from "../../../module/hero/HeroConfig";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass } = _decorator;

/**
 *
 * ivan_huang
 * Wed May 22 2024 15:47:22 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/UIHeroMain.ts
 *
 */

@ccclass("UIHeroMain")
export class UIHeroMain extends UINode {
  protected _openAct: boolean = true;
  private _startTime: number = 0;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_HERO}?prefab/ui/UIHeroMain`;
  }

  protected dependOn(): BundleEnum[] {
    return [BundleEnum.BUNDLE_HD_SEVEN, BundleEnum.BUNDLE_COMMON_FRIEND, BundleEnum.BUNDLE_COMMON_HERO_ICON];
  }

  public init(args: any): void {
    super.init(args);
    this._startTime = new Date().getTime();
    log.log("UIHeroMain init", this._startTime);
  }
  private on_click_btn_back() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  public onEvtClose(): void {
    MsgMgr.emit(MsgEnum.ON_GUIDE_NEXT, "HERO_MAIN_CLOSE");
  }

  private on_click_btn_wenhao() {
    AudioMgr.instance.playEffect(522);
    UIMgr.instance.showDialog(PlayerRouteName.UIHelpPop, { titleId: -1, desId: 6 });
  }

  private on_click_tab_hero_unselect() {
    AudioMgr.instance.playEffect(HeroAudioName.Effect.点击下方页签);
    this.getNode("tab_hero_select").active = true;
    this.getNode("tab_tujian_select").active = false;
    this.getNode("dialog_hero").active = true;
    this.getNode("dialog_tujian").active = false;
  }

  private on_click_tab_tujian_unselect() {
    AudioMgr.instance.playEffect(HeroAudioName.Effect.点击下方页签);
    this.getNode("tab_hero_select").active = false;
    this.getNode("tab_tujian_select").active = true;
    this.getNode("dialog_hero").active = false;
    this.getNode("dialog_tujian").active = true;
  }

  protected onEvtShow(): void {
    super.onEvtShow();
    let duration = new Date().getTime() - this._startTime;
    log.log("UIHeroMain onEvtShow", duration);
    BadgeMgr.instance.setBadgeId(this.getNode("tab_tujian"), BadgeType.UIHeroMain.tab_tujian.id);
  }
}
