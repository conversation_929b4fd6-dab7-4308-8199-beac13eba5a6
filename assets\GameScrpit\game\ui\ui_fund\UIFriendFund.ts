import {
  _decorator,
  Animation,
  Color,
  Input,
  isValid,
  Label,
  Layout,
  Node,
  ScrollView,
  Sprite,
  tween,
  Tween,
  UITransform,
  Vec2,
} from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import ToolExt from "../../common/ToolExt";
import { GoodsRouteName } from "../../../module/goods/GoodsRoute";
import GameHttpApi from "../../httpNet/GameHttpApi";
import { PlayerModule } from "../../../module/player/PlayerModule";
import MsgEnum from "../../event/MsgEnum";
import MsgMgr from "../../../lib/event/MsgMgr";
import { Sleep } from "../../GameDefine";
import { AchieveRewardRequest, AchieveRewardResponse, FundMessage } from "../../net/protocol/Activity";
import { FundModule } from "../../../module/fund/FundModule";
import TipMgr from "../../../lib/tips/TipMgr";
import TickerMgr from "../../../lib/ticker/TickerMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { ActivityAudioName, FundVO } from "../../../module/activity/ActivityConfig";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { LangMgr } from "../../mgr/LangMgr";
import FmUtils from "../../../lib/utils/FmUtils";
const log = Logger.getLoger(LOG_LEVEL.WARN);
/**仙友基金id */
const fundId: number = 10403;
const dtTime: number = 0.032;
const { ccclass, property } = _decorator;

@ccclass("UIFriendFund")
export class UIFriendFund extends UINode {
  protected _openAct: boolean = true; //打开动作
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_HD_FUND}?prefab/ui/UIFriendFund`;
  }

  /**配置数据 */
  private _fundVO: FundVO = null;
  /**成就id */
  private _achieveId: number = null;

  private _tickMgrIdList: number[] = [];

  protected onRegEvent(): void {
    MsgMgr.on(MsgEnum.ON_ACTIVITY_FUND_BUT_UP, this.fund_buy_up, this);
  }
  protected onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_ACTIVITY_FUND_BUT_UP, this.fund_buy_up, this);
  }
  private fund_buy_up() {
    this.getNode("btn_goumai").active = !FundModule.data.getFundData(this._achieveId).paid;
    let basicRewardList = this._fundVO.achieveVOList[0].basicRewardList;
    let paidRewardList = this._fundVO.achieveVOList[0].paidRewardList;
    let requireList = this._fundVO.achieveVOList[0].requireList;

    for (let i = 0; i < basicRewardList.length; i++) {
      let node = this.getNode("content_list").children[i];
      if (!node) {
        node = ToolExt.clone(this.getNode("fund_item"), this);
        this.getNode("content_list").addChild(node);
        this.on_Touch_get_award(node);
        node["itemIndex"] = i;
        node.active = true;
      }
      let basic = basicRewardList[i];
      let paid = paidRewardList[i] || [];
      let require = requireList[i] || 0;

      /**注册道具的点击领取事件 */
      this.on_Touch_get_award(node);

      let list = [node["basics_Item1"], node["btn_pay_Item1"], node["btn_pay_Item2"]];
      list.forEach((val) => {
        this.hide_node_mask(val);
        val.getChildByName("btn_get_award")["itemIndex"] = i;
      });

      this.set_state(node["state_has"], require);
      this.set_bar_yellow(node, require);

      this.set_require(node["no_lab_require"], require);
      this.set_require(node["lab_require"], require);

      this.set_basics_Item1(node["basics_Item1"], basic[0], basic[1], require, i);
      this.set_pay_Item1(node["btn_pay_Item1"], paid[0], paid[1], require, i);
      this.set_pay_Item2(node["btn_pay_Item2"], paid[2], paid[3], require, i);

      this.check_is_get(node, i);
    }
  }

  protected onEvtShow(): void {
    this.getNode("lbl_fund_des").getComponent(Label).string = LangMgr.txMsgCode(527, []);
    FundModule.api.fund(fundId, this.initMain.bind(this));
  }

  private async initMain(res: FundMessage) {
    await Sleep(0.2);
    this._fundVO = await FundModule.data.getFundVO(fundId);
    if (isValid(this.node) == false) return;
    this._achieveId = this._fundVO.achieveVOList[0].id;

    this.set_lbl_price();
    this.load_fund_item();
  }

  /**设置价格 */
  private set_lbl_price() {
    this.getNode("lbl_price").getComponent(Label).string = (this._fundVO.achieveVOList[0].price % 10000) + "元";
    this.getNode("btn_goumai").active = !FundModule.data.getFundData(this._achieveId).paid;
  }

  private async load_fund_item() {
    let basicRewardList = this._fundVO.achieveVOList[0].basicRewardList;
    let paidRewardList = this._fundVO.achieveVOList[0].paidRewardList;
    let requireList = this._fundVO.achieveVOList[0].requireList;

    for (let i = 0; i < basicRewardList.length; i++) {
      await Sleep(0.01);
      if (isValid(this.node) == false) return;
      let basic = basicRewardList[i];
      let paid = paidRewardList[i] || [];
      let require = requireList[i] || 0;
      let node = ToolExt.clone(this.getNode("fund_item"), this);
      this.getNode("content_list").addChild(node);
      node.active = true;
      node["itemIndex"] = i;
      /**注册道具的点击领取事件 */
      this.on_Touch_get_award(node);
      let list = [node["basics_Item1"], node["btn_pay_Item1"], node["btn_pay_Item2"]];
      list.forEach((val) => {
        this.hide_node_mask(val);
        val.getChildByName("btn_get_award")["itemIndex"] = i;
      });
      this.set_state(node["state_has"], require);
      this.set_bar_yellow(node, require);
      this.set_require(node["no_lab_require"], require);
      this.set_require(node["lab_require"], require);
      this.set_basics_Item1(node["basics_Item1"], basic[0], basic[1], require, i);
      this.set_pay_Item1(node["btn_pay_Item1"], paid[0], paid[1], require, i);
      this.set_pay_Item2(node["btn_pay_Item2"], paid[2], paid[3], require, i);
      this.check_is_get(node, i);
    }

    let basicTakeList = FundModule.data.getFundData(this._achieveId).basicTakeList;
    let index = basicTakeList.length == 0 ? 0 : basicTakeList[basicTakeList.length - 1];
    if (FundModule.data.getFundData(this._achieveId).paid == true) {
      let paidTakeList = FundModule.data.getFundData(this._achieveId).paidTakeList;
      let paidInddex = paidTakeList.length == 0 ? 0 : paidTakeList[paidTakeList.length - 1];
      if (index > paidInddex) {
        index = paidInddex;
      }
    }

    tween(this.node)
      .delay(dtTime)
      .call(() => {
        let spacingY = this.getNode("content_list").getComponent(Layout).spacingY;
        let height = this.getNode("fund_item").getComponent(UITransform).height;
        let y = (height + spacingY) * index;
        this.getNode("scroll_list").getComponent(ScrollView).scrollToOffset(new Vec2(0, y), 0.35);
      })
      .start();
  }

  private on_Touch_get_award(node: Node) {
    node["basics_Item1"].getChildByName("btn_get_award").off(Input.EventType.TOUCH_END, this.click_get_award, this);
    node["btn_pay_Item1"].getChildByName("btn_get_award").off(Input.EventType.TOUCH_END, this.click_get_award, this);
    node["btn_pay_Item2"].getChildByName("btn_get_award").off(Input.EventType.TOUCH_END, this.click_get_award, this);
    node["basics_Item1"].getChildByName("btn_get_award").on(Input.EventType.TOUCH_END, this.click_get_award, this);
    node["btn_pay_Item1"].getChildByName("btn_get_award").on(Input.EventType.TOUCH_END, this.click_get_award, this);
    node["btn_pay_Item2"].getChildByName("btn_get_award").on(Input.EventType.TOUCH_END, this.click_get_award, this);
  }

  private hide_node_mask(node: Node) {
    node.getChildByName("spr_gou").active = false;
    node.getChildByName("spr_suo").active = false;
    node.getChildByName("btn_get_award").active = false;
  }

  private set_state(node: Node, require: number) {
    let my = FundModule.data.getFundData(this._achieveId).targetVal;
    let bool = false;
    if (my >= require) bool = true;
    node.getChildByName("no_has").active = !bool;
    node.getChildByName("no_lab_require").active = !bool;

    node.getChildByName("has").active = bool;
    node.getChildByName("lab_require").active = bool;
  }

  private set_bar_yellow(node: Node, require: number) {
    let my = FundModule.data.getFundData(this._achieveId).targetVal;
    if (my > require) {
      node.getChildByName("bar_yellow1").active = true;
      node.getChildByName("bar_yellow2").active = false;
      return;
    }

    if (my >= require) {
      node.getChildByName("bar_yellow1").active = false;
      node.getChildByName("bar_yellow2").active = true;
      return;
    }

    node.getChildByName("bar_yellow1").active = false;
    node.getChildByName("bar_yellow2").active = false;
  }

  private set_require(node: Node, require: number) {
    node.getComponent(Label).string = String(require);
  }

  private check_is_get(node: Node, index: number) {
    let is1 = FundModule.data.getFundData(this._achieveId).basicTakeList.indexOf(index);
    let is2 = FundModule.data.getFundData(this._achieveId).paidTakeList.indexOf(index);
    if (is1 != -1) {
      node["basics_Item1"].getChildByName("spr_gou").active = true;
      node["basics_Item1"].getChildByName("spr_suo").active = false;
      node["basics_Item1"].getChildByName("btn_get_award").active = false;
      this.award_snake(node["basics_Item1"], false);
    }
    if (is2 != -1) {
      node["btn_pay_Item1"].getChildByName("spr_gou").active = true;
      node["btn_pay_Item1"].getChildByName("btn_get_award").active = false;
      this.award_snake(node["btn_pay_Item1"], false);
      node["btn_pay_Item2"].getChildByName("spr_gou").active = true;
      node["btn_pay_Item2"].getChildByName("btn_get_award").active = false;
      this.award_snake(node["btn_pay_Item2"], false);
    }
  }
  private set_basics_Item1(item: Node, id: number, num: number, require: number, itemIndex: number) {
    FmUtils.setItemNode(item, id, num);
    this.set_is_gray(item, require);

    let my = FundModule.data.getFundData(this._achieveId).targetVal;
    if (my >= require) {
      let bool = FundModule.data.getFundData(this._achieveId).basicTakeList.indexOf(itemIndex) == -1 ? true : false;
      this.award_snake(item, bool);
      item.getChildByName("btn_get_award").active = true;
    }
  }
  private set_pay_Item1(item: Node, id: number, num: number, require: number, itemIndex: number) {
    FmUtils.setItemNode(item, id, num);
    this.set_is_gray(item, require);

    let my = FundModule.data.getFundData(this._achieveId).targetVal;
    if (my >= require && FundModule.data.getFundData(this._achieveId).paid == true) {
      let bool = FundModule.data.getFundData(this._achieveId).paidTakeList.indexOf(itemIndex) == -1 ? true : false;
      this.award_snake(item, bool);
      item.getChildByName("btn_get_award").active = FundModule.data.getFundData(this._achieveId).paid;
    }

    this.set_pay_suo(item);
  }
  private set_pay_Item2(item: Node, id: number, num: number, require: number, itemIndex: number) {
    FmUtils.setItemNode(item, id, num);
    this.set_is_gray(item, require);

    let my = FundModule.data.getFundData(this._achieveId).targetVal;
    if (my >= require && FundModule.data.getFundData(this._achieveId).paid == true) {
      let bool = FundModule.data.getFundData(this._achieveId).paidTakeList.indexOf(itemIndex) == -1 ? true : false;
      this.award_snake(item, bool);
      item.getChildByName("btn_get_award").active = FundModule.data.getFundData(this._achieveId).paid;
    }

    this.set_pay_suo(item);
  }

  private set_is_gray(node: Node, require: number) {
    let my = FundModule.data.getFundData(this._achieveId).targetVal;
    let bool = false;
    if (my >= require) bool = true;
    let color = new Color(100, 100, 100, 255);
    if (bool == true) {
      color = new Color(255, 255, 255, 255);
    }
    node.getComponentsInChildren(Sprite).forEach((sprite) => {
      sprite.color = color;
    });
    node.getComponentsInChildren(Label).forEach((label) => {
      label.color = color;
    });

    if (bool == false) {
      node.getChildByName("btn_get_award").active = false;
    }
  }

  private set_pay_suo(node: Node) {
    node.getChildByName("spr_suo").active = !FundModule.data.getFundData(this._achieveId).paid;
  }

  private award_snake(targetNode: Node, is: boolean) {
    // 获取目标节点的初始位置
    const nodeStartPos = targetNode.getPosition();
    if (!targetNode["nodeStartPos"]) {
      targetNode["nodeStartPos"] = nodeStartPos;
    }
    let ani = targetNode.getComponent(Animation);
    if (is == false) {
      ani.stop();
      targetNode.setRotation(0, 0, 0, 0);
      return;
    }
    targetNode.setRotation(0, 0, 0, 0);
    ani.play("ani_dou");
  }

  private click_get_award(event) {
    AudioMgr.instance.playEffect(ActivityAudioName.Effect.点击道具图标);
    let fundRequest_data: AchieveRewardRequest = {
      /** 基金的ID */
      activityId: this._fundVO.id,
      /** 成就ID */
      achieveId: this._fundVO.achieveVOList[0].id,
      /** 领取的索引处对应的奖励 从0开始 */
      index: event.target.itemIndex,
      takeAll: false,
    };
    FundModule.api.takeFundReward(fundRequest_data, (res: AchieveRewardResponse) => {
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: res.rewardList });
      this.getNode("content_list").children.forEach((val) => {
        this.check_is_get(val, val["itemIndex"]);
      });
    });
  }

  private on_click_btn_goumai() {
    let data = {
      goodsId: this._fundVO.achieveVOList[0].id,
      goodsType: this._fundVO.buyType,
      playerId: PlayerModule.data.playerId,
      orderAmount: (this._fundVO.achieveVOList[0].price % 10000) % 10000,
      goodsName: this._fundVO.achieveVOList[0].name,
      platformType: "TEST",
    };

    //log.log("购买基金参数-=-====", data);
    GameHttpApi.pay(data).then((resp: any) => {
      //log.log("pay resp", resp);
      if (resp.code != 200) {
        let err = JSON.parse(resp.msg);
        log.log(err);
        return;
      }
      UIMgr.instance.showDialog(GoodsRouteName.UIPay, { url: resp.data.url });
    });
  }

  private async on_click_btn_getAll() {
    AudioMgr.instance.playEffect(ActivityAudioName.Effect.点击一键领取);

    let bool = await FundModule.service.getFundBool(fundId);
    if (bool == false) {
      TipsMgr.showTipX(198, [], "");
      return;
    }

    let fundRequest_data: AchieveRewardRequest = {
      /** 基金的ID */
      activityId: this._fundVO.id,
      /** 成就ID */
      achieveId: this._fundVO.achieveVOList[0].id,
      /** 领取的索引处对应的奖励 从0开始 */
      index: 0,
      takeAll: true,
    };
    FundModule.api.takeFundReward(fundRequest_data, (res: AchieveRewardResponse) => {
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: res.rewardList });
      this.getNode("content_list").children.forEach((val) => {
        this.check_is_get(val, val["itemIndex"]);
      });
    });
  }

  private on_click_btn_pay_Item1() {
    if (FundModule.data.getFundData(this._achieveId).paid == false) {
      TipMgr.showTip("购买基金可以解锁");
    }
  }

  private on_lcikc_btn_pay_Item2() {
    if (FundModule.data.getFundData(this._achieveId).paid == false) {
      TipMgr.showTip("购买基金可以解锁");
    }
  }

  private on_click_btn_close() {
    // 关闭窗口 effect_guanbiyinxiao
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  protected onEvtClose(): void {
    Tween.stopAllByTarget(this.node);

    for (let i = 0; i < this._tickMgrIdList.length; i++) {
      TickerMgr.clearTimeout(this._tickMgrIdList[i]);
    }
  }

  private _tickIndex: number = 0;

  public tick(dt: any): void {
    let pengz = this.getNode("pengz");

    let content_list = this.getNode("content_list");

    const Box1 = pengz.getComponent(UITransform).getBoundingBoxToWorld();

    const index = this._tickIndex;
    for (let i = index; i < index + 5 && i < content_list.children.length; i++) {
      const Box2 = content_list.children[i].getComponent(UITransform).getBoundingBoxToWorld();

      if (Box1.intersects(Box2)) {
        content_list.children[i].walk((val) => {
          if (val.getComponent(Label)) {
            val.getComponent(Label).enabled = true;
          }
          if (val.getComponent(Sprite)) {
            val.getComponent(Sprite).enabled = true;
          }
        });
      } else {
        content_list.children[i].walk((val) => {
          if (val.getComponent(Label)) {
            val.getComponent(Label).enabled = false;
          }
          if (val.getComponent(Sprite)) {
            val.getComponent(Sprite).enabled = false;
          }
        });
      }
      this._tickIndex = i;
    }

    if (this._tickIndex >= content_list.children.length - 1) {
      this._tickIndex = 0;
    }
  }
}
