import { _decorator, Component, Label, Node } from "cc";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { LangMgr } from "../../../mgr/LangMgr";
import MsgMgr from "db://assets/GameScrpit/lib/event/MsgMgr";
import MsgEnum from "../../../event/MsgEnum";
const { ccclass, property } = _decorator;

@ccclass("ui_thief")
export class ui_thief extends BaseCtrl {
  targetCtrl: BaseCtrl;
  methodName: string;

  init(args: any) {
    super.init(args);
    TipsMgr;
    this.targetCtrl = args.ctrl;
    this.methodName = args.methodName;
  }

  start() {
    super.start();
    this.getNode("hint").getComponent(Label).string = LangMgr.txMsgCode(247, [], "点击这里");

    MsgMgr.on(MsgEnum.ON_THIEF_UNFOCOUS, this.closeBack, this);
  }

  protected onDestroy(): void {
    MsgMgr.off(MsgEnum.ON_THIEF_UNFOCOUS, this.closeBack, this);
  }

  on_click_btn_dianji_nvwa() {
    this.targetCtrl[this.methodName]?.();
  }
}
