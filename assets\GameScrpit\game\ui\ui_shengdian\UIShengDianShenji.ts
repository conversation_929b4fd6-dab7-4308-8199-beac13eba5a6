import { _decorator, Component, instantiate, Node, RichText } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { JsonMgr } from "../../mgr/JsonMgr";
import { divide } from "../../../lib/utils/NumbersUtils";
import { ShengDianModule } from "../../../module/sheng_dian/ShengDianModule";
const { ccclass, property } = _decorator;

/**
 * ivan_huang
 * Wed Jan 15 2025 14:34:30 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/ui_shengdian/UIShengDianShenji.ts
 * https://docs.cocos.com/creator/3.8/manual/zh/
 *
 */

@ccclass("UIShengDianShenji")
export class UIShengDianShenji extends UINode {
  protected _openAct: boolean = true;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_SHENGDIAN}?prefab/ui/UIShengDianShenji`;
  }

  //=================================================
  public init(args: any): void {
    super.init(args);
  }
  protected onEvtShow(): void {
    // do something
    let shenjiList = Object.values(JsonMgr.instance.jsonList.c_templeShenji);
    let i = 0;
    for (; i < shenjiList.length; i++) {
      if (!this.getNode("node_content").children[i]) {
        this.getNode("node_content").addChild(instantiate(this.getNode("node_content").children[0]));
      }
      let child = this.getNode("node_content").children[i];
      child.active = true;
      let remain = ShengDianModule.data.templeMessage.miracleRemainMap[shenjiList[i].id];
      if (remain) {
        let string = `<color=#fffbd6>${shenjiList[i].title}:${shenjiList[i].des
          .replace("%s", `${divide(shenjiList[i].rate, 100)}`)
          .replace("%n", `${shenjiList[i].max}`)}(今日剩余:<color=#74ff77>${remain}/${
          shenjiList[i].max
        }</color>)</color>`;
        child.getComponent(RichText).string = string;
      } else {
        child.getComponent(RichText).string =
          `${shenjiList[i].title}:` +
          shenjiList[i].des.replace("%s", `${divide(shenjiList[i].rate, 100)}`).replace("%n", `${shenjiList[i].max}`);
      }
    }
    for (; i < this.getNode("node_content").children.length; i++) {
      this.getNode("node_content").children[i].active = false;
    }
  }
}
