import { _decorator, instantiate, Label, Node, Sprite } from "cc";
import { ListAdapter } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { PupilWaitItemViewHolder } from "./PupilWaitItemViewHolder";
import { PupilWorkUnMarryltemViewHolder } from "./PupilWorkUnMarryltemViewHolder";
import { PupilWorkMarryltemViewHolder } from "./PupilWorkMarryltemViewHolder";
import { PupilModule } from "../PupilModule";

export class PupilWorkAdapter extends ListAdapter {
  // 常规节点
  item1: Node;
  item2: Node;

  constructor(item1: Node, item2: Node) {
    super();
    this.item1 = item1;
    this.item2 = item2;
  }

  // 所有缓存节点
  datas: any[] = [];
  setDatas(data: any[]) {
    if (!data) {
      return;
    }
    this.datas = data;
    this.notifyDataSetChanged();
  }

  getCount(): number {
    return this.datas.length;
  }

  getViewType(position: number): number {
    return PupilModule.data.allPupilMap[this.datas[position].pupilId].partnerInfo ? 1 : 2;
  }

  onCreateView(viewType: number): Node {
    let item: Node;
    if (viewType == 1) {
      item = instantiate(this.item1);
      item.getComponent(PupilWorkMarryltemViewHolder).init();
    } else {
      item = instantiate(this.item2);
      item.getComponent(PupilWorkUnMarryltemViewHolder).init();
    }
    item.active = true;
    return item;
  }

  onBindData(node: Node, position: number): void {
    let itemData = this.datas[position];

    if (PupilModule.data.allPupilMap[itemData.pupilId].partnerInfo) {
      node.getComponent(PupilWorkMarryltemViewHolder).updateData(position, itemData);
    } else {
      node.getComponent(PupilWorkUnMarryltemViewHolder).updateData(position, itemData);
    }
  }
}
