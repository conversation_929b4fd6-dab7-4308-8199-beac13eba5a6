{"skeleton": {"hash": "j6TCALVJ62uZT9AfinwB32038Gw=", "spine": "3.8.75", "x": -144.33, "y": -4.69, "width": 270, "height": 313, "images": "./images/", "audio": "D:/spine导出/boss动画/黑熊精"}, "bones": [{"name": "root", "scaleX": -1}, {"name": "all", "parent": "root", "x": -2.45, "y": 18.93}, {"name": "bone", "parent": "all", "length": 165.88, "rotation": 1.66, "x": 2.53, "y": 72.69}, {"name": "bone2", "parent": "bone", "length": 59.19, "rotation": 84.21, "x": -34.38, "y": -6.91}, {"name": "bone3", "parent": "bone2", "length": 40.36, "rotation": 2.73, "x": 59.19}, {"name": "bone4", "parent": "bone3", "length": 25.94, "rotation": 10.86, "x": 41.66, "y": 0.36}, {"name": "bone5", "parent": "bone3", "x": 78.26, "y": -87.99}, {"name": "bone6", "parent": "bone3", "x": 53.18, "y": 39.36}, {"name": "bone7", "parent": "bone3", "x": 104.22, "y": 18.29}, {"name": "bone8", "parent": "bone7", "x": -98.15, "y": -24.7}, {"name": "bone9", "parent": "bone5", "length": 59.68, "rotation": -136.5, "x": -1.26, "y": -2}, {"name": "bone10", "parent": "bone9", "length": 53.26, "rotation": -34.67, "x": 59.68}, {"name": "bone11", "parent": "bone10", "length": 35.58, "rotation": -25.7, "x": 53.26}, {"name": "bone12", "parent": "bone6", "length": 35.6, "rotation": 164.8, "x": -5.5, "y": 10.36}, {"name": "bone13", "parent": "bone12", "length": 44.95, "rotation": 2.66, "x": 35.6}, {"name": "bone14", "parent": "bone13", "length": 28.33, "rotation": 8.62, "x": 44.95}, {"name": "bone15", "parent": "bone", "length": 18.93, "rotation": -77.63, "x": -79.22, "y": -2.98}, {"name": "bone16", "parent": "bone15", "length": 25.31, "rotation": -45.25, "x": 19.01, "y": 0.32}, {"name": "bone17", "parent": "bone", "length": 19.69, "rotation": -81.1, "x": 76.14, "y": 2.35}, {"name": "bone18", "parent": "bone17", "length": 24.85, "rotation": 11.94, "x": 19.69}, {"name": "bone19", "parent": "bone", "x": 30.1, "y": -23.88}, {"name": "bone20", "parent": "bone19", "x": -54.81, "y": -0.38}, {"name": "bone21", "parent": "bone19", "length": 17.72, "rotation": -90.6, "x": 0.27, "y": -13.46}, {"name": "bone22", "parent": "bone21", "length": 21.38, "rotation": 3.34, "x": 17.72}, {"name": "bone24", "parent": "bone", "length": 25.73, "rotation": -74.61, "x": -44.68, "y": -11.21}, {"name": "bone25", "parent": "bone24", "length": 27.61, "rotation": -4, "x": 25.73}, {"name": "bone26", "parent": "bone", "length": 35.32, "rotation": -98.06, "x": -55.78, "y": -20.73}, {"name": "bone27", "parent": "bone26", "length": 33.39, "rotation": -4.36, "x": 35.32}, {"name": "bone28", "parent": "bone", "length": 44.48, "rotation": -58.09, "x": 21.11, "y": -17.05}, {"name": "bone29", "parent": "bone28", "length": 39.32, "rotation": -1.87, "x": 44.48}, {"name": "bone30", "parent": "bone4", "x": 45.71, "y": 28.9}, {"name": "bone31", "parent": "bone2", "x": 26.29, "y": 28.09}, {"name": "target1", "parent": "all", "x": -66.3, "y": -22.51, "color": "ff3f00ff"}, {"name": "target2", "parent": "all", "x": 73.92, "y": -16.52, "color": "ff3f00ff"}, {"name": "bone32", "parent": "all", "x": 443.47, "y": 261.17}, {"name": "bone33", "parent": "all", "x": 438.79, "y": 552.51}, {"name": "bone34", "parent": "bone30", "x": 1.45, "y": -26.1}, {"name": "bone35", "parent": "bone34", "x": 7.04, "y": -23.07}, {"name": "bone36", "parent": "bone34", "x": 12.35, "y": -34.22}, {"name": "bone37", "parent": "bone34", "x": 14.5, "y": -43.6}, {"name": "bone38", "parent": "bone30", "x": 1.03, "y": 12.19}, {"name": "bone39", "parent": "bone38", "x": 9.45, "y": 11.41}, {"name": "bone40", "parent": "bone38", "x": 12.86, "y": 19.25}, {"name": "bone41", "parent": "bone38", "x": 19.05, "y": 25.27}, {"name": "target3", "parent": "all", "length": 26.63, "rotation": 170.27, "x": -77.55, "y": -18.3, "color": "ff3f00ff"}, {"name": "target4", "parent": "all", "length": 25.5, "rotation": -175.99, "x": 68.28, "y": -16.65, "color": "ff3f00ff"}, {"name": "bone23", "parent": "all", "rotation": -45.31, "x": 327.32, "y": -0.64}], "slots": [{"name": "you<PERSON>ou", "bone": "bone", "attachment": "you<PERSON>ou"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "bone", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "bone": "bone", "attachment": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "jiasha1", "bone": "bone", "attachment": "jiasha1"}, {"name": "jiasha2", "bone": "bone", "attachment": "jiasha2"}, {"name": "jiasha3", "bone": "bone", "attachment": "jiasha3"}, {"name": "jiasha4", "bone": "bone", "attachment": "jiasha4"}, {"name": "body", "bone": "bone", "attachment": "body"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "bone", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "fozhu", "bone": "bone", "attachment": "fozhu"}, {"name": "xiongtou", "bone": "bone4", "attachment": "xiongtou"}, {"name": "biyan", "bone": "bone4", "attachment": "biyan"}, {"name": "eye", "bone": "bone30", "attachment": "eye", "blend": "additive"}, {"name": "fangun", "bone": "bone32"}, {"name": "shunyi", "bone": "bone33"}, {"name": "zuoshou2", "bone": "bone23", "color": "ffd000ff", "dark": "ffd200", "blend": "additive"}], "ik": [{"name": "target1", "bones": ["bone26", "bone27"], "target": "target1", "stretch": true}, {"name": "target2", "order": 1, "bones": ["bone28", "bone29"], "target": "target2", "bendPositive": false, "stretch": true}], "skins": [{"name": "default", "attachments": {"zuojiao": {"zuojiao": {"type": "mesh", "uvs": [0.15454, 0.02345, 0.37639, 0, 0.64973, 0.01666, 0.84187, 0.16154, 0.97458, 0.34717, 1, 0.60071, 1, 0.81803, 1, 0.94479, 0.86168, 1, 0.6537, 1, 0.4061, 1, 0.24368, 0.82708, 0.09116, 0.6573, 0, 0.44451, 0, 0.22719, 0.05947, 0.10495, 0.31697, 0.16154, 0.44175, 0.39244, 0.53485, 0.56901, 0.65172, 0.76596], "triangles": [19, 6, 8, 7, 8, 6, 10, 19, 9, 8, 9, 19, 19, 10, 18, 10, 11, 18, 19, 5, 6, 19, 18, 5, 18, 4, 5, 18, 11, 17, 17, 12, 13, 11, 12, 17, 17, 13, 16, 16, 15, 0, 14, 15, 16, 18, 3, 4, 18, 17, 3, 16, 13, 14, 17, 16, 2, 17, 2, 3, 2, 16, 1, 16, 0, 1], "vertices": [1, 28, -16.28, -5.58, 1, 1, 28, -5.58, 14.67, 1, 2, 28, 10.87, 37.27, 0.98108, 29, -33.62, 37.27, 0.01892, 2, 28, 31.93, 46.32, 0.79606, 29, -12.55, 46.32, 0.20394, 3, 28, 52.59, 48.17, 0.30782, 29, 8.1, 48.17, 0.31618, 45, -30.59, -52.43, 0.376, 3, 28, 72.1, 37.33, 0.08348, 29, 27.62, 37.33, 0.40452, 45, -31.62, -29.23, 0.512, 3, 28, 87.61, 26.16, 0.00771, 29, 43.13, 26.16, 0.44029, 45, -30.23, -9.51, 0.552, 1, 45, -29.43, 2, 1, 1, 45, -14.73, 6.01, 1, 1, 45, 6.85, 4.5, 1, 1, 45, 32.54, 2.7, 1, 2, 28, 46.05, -39.22, 0.19263, 29, 1.57, -39.22, 0.80737, 2, 28, 25.42, -43.59, 0.85071, 29, -19.06, -43.59, 0.14929, 2, 28, 5.15, -40.48, 0.97862, 29, -39.34, -40.48, 0.02138, 2, 28, -10.37, -29.31, 0.99992, 29, -54.85, -29.31, 8e-05, 1, 28, -15.77, -17.93, 1, 1, 28, 2.64, 1.27, 1, 1, 28, 26.08, 0.11, 1, 2, 28, 43.88, -0.97, 0.75076, 29, -0.6, -0.97, 0.24924, 1, 29, 19.97, -1.06, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30, 0, 32, 32, 34, 34, 36, 36, 38], "width": 52, "height": 46}}, "zuoshou2": {"zuoshou": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-107.73, -5.76, -45.18, -91.34, 96.91, 12.52, 34.36, 98.1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 53, "height": 88}}, "jiasha1": {"jiasha1": {"type": "mesh", "uvs": [0.04352, 0.27285, 0.1554, 0.33315, 0.28906, 0.39145, 0.44746, 0.37938, 0.64349, 0.303, 0.77022, 0.18642, 0.87022, 0.04772, 0.93358, 0, 0.97813, 0.03566, 0.97021, 0.20049, 0.9514, 0.31506, 0.95692, 0.37111, 0.96229, 0.42562, 0.99199, 0.5422, 1, 0.67085, 1, 0.79146, 0.91279, 0.87588, 0.79695, 0.95427, 0.69498, 1, 0.58706, 0.98643, 0.44746, 0.90603, 0.35341, 0.81759, 0.24351, 0.86181, 0.10986, 0.85578, 0, 0.80754, 0.01778, 0.63467, 0.05045, 0.56833, 0.07224, 0.51406, 0.03263, 0.44572, 0.02669, 0.34722, 0.20391, 0.57838, 0.30589, 0.60653, 0.42271, 0.61859, 0.5732, 0.61859, 0.69003, 0.56632, 0.80784, 0.502, 0.91378, 0.41356, 0.89445, 0.42969, 0.95338, 0.19244, 0.84744, 0.27687, 0.73953, 0.36732, 0.61379, 0.42964, 0.45341, 0.46984, 0.33361, 0.48793, 0.21381, 0.45577, 0.10689, 0.41959, 0.10491, 0.70502, 0.23658, 0.71105, 0.35242, 0.72713, 0.45539, 0.74522, 0.5732, 0.7814, 0.71874, 0.76934, 0.84348, 0.68492, 0.93754, 0.61256], "triangles": [27, 45, 30, 46, 27, 30, 30, 45, 44, 26, 27, 46, 27, 28, 45, 28, 29, 45, 31, 44, 43, 10, 39, 38, 39, 6, 38, 41, 4, 40, 42, 3, 41, 44, 1, 2, 38, 7, 8, 9, 38, 8, 41, 3, 4, 45, 0, 1, 40, 4, 5, 39, 5, 6, 6, 7, 38, 43, 2, 3, 29, 0, 45, 45, 1, 44, 44, 2, 43, 43, 3, 42, 40, 5, 39, 35, 40, 39, 10, 38, 9, 32, 43, 42, 18, 51, 17, 51, 18, 50, 20, 50, 19, 18, 19, 50, 50, 34, 51, 49, 33, 50, 50, 33, 34, 51, 34, 35, 33, 41, 34, 33, 42, 41, 34, 40, 35, 34, 41, 40, 30, 44, 31, 20, 49, 50, 20, 21, 49, 21, 47, 48, 21, 22, 47, 21, 48, 49, 48, 32, 49, 49, 32, 33, 47, 31, 48, 48, 31, 32, 47, 30, 31, 31, 43, 32, 32, 42, 33, 25, 26, 46, 24, 46, 23, 24, 25, 46, 23, 47, 22, 23, 46, 47, 46, 30, 47, 17, 52, 16, 17, 51, 52, 16, 53, 15, 16, 52, 53, 53, 14, 15, 51, 35, 52, 52, 37, 53, 53, 13, 14, 53, 12, 13, 52, 35, 37, 37, 36, 53, 53, 36, 12, 35, 39, 37, 11, 36, 37, 36, 11, 12, 11, 37, 10, 10, 37, 39], "vertices": [1, 2, -89.75, 10.29, 1, 1, 2, -67.44, 3.67, 1, 1, 2, -40.76, -2.88, 1, 1, 2, -8.9, -2.61, 1, 1, 2, 30.71, 3.8, 1, 1, 2, 56.51, 14.6, 1, 1, 2, 77, 27.74, 1, 1, 2, 89.86, 32.1, 1, 1, 2, 98.71, 28.31, 1, 1, 2, 96.65, 12.04, 1, 2, 18, 4.05, 15.96, 0.97505, 19, -11.99, 18.85, 0.02495, 2, 18, 9.71, 16.03, 0.86845, 19, -6.44, 17.75, 0.13155, 2, 18, 15.21, 16.1, 0.64128, 19, -1.04, 16.68, 0.35872, 2, 18, 27.65, 19.86, 0.11134, 19, 11.9, 17.78, 0.88866, 2, 18, 40.47, 19.11, 0.00149, 19, 24.29, 14.39, 0.99851, 1, 19, 35.32, 9.82, 1, 2, 19, 36.33, -9.57, 0.92118, 20, 53.07, -30.58, 0.07882, 3, 19, 34.59, -34.05, 0.55502, 21, 84.38, -37.29, 0.00196, 20, 29.57, -37.66, 0.44302, 3, 19, 30.93, -54.72, 0.26316, 21, 63.76, -41.22, 0.04325, 20, 8.95, -41.59, 0.69359, 3, 19, 21.38, -74.25, 0.08043, 21, 42.12, -39.24, 0.1993, 20, -12.69, -39.62, 0.72027, 4, 19, 3.29, -97.12, 0.00276, 17, -7.06, 72.15, 0.00275, 21, 14.3, -30.47, 0.62411, 20, -40.51, -30.85, 0.37038, 4, 16, 52.21, 39.91, 0.00395, 17, -4.75, 51.45, 0.10652, 21, -4.34, -21.17, 0.82944, 20, -59.15, -21.55, 0.06009, 3, 16, 51.1, 17.42, 0.02147, 17, 10.44, 34.82, 0.53611, 21, -26.55, -24.91, 0.44242, 3, 17, 23.9, 11.57, 0.55346, 21, -53.39, -23.59, 0.02254, 26, 28.83, -16.06, 0.424, 2, 17, 31.26, -9.79, 0.576, 26, 28.26, -38.64, 0.424, 3, 16, 18.35, -21.16, 0.03575, 17, 14.79, -15.59, 0.54025, 26, 12.03, -38.29, 0.424, 3, 16, 13.59, -13.2, 0.19257, 17, 5.78, -13.37, 0.38343, 26, 4.91, -33.05, 0.424, 3, 16, 9.45, -7.66, 0.48431, 17, -1.07, -12.41, 0.09169, 26, -0.75, -29.74, 0.424, 2, 16, 0.96, -13.74, 0.576, 26, -5.55, -38.81, 0.424, 2, 16, -34.54, -6.1, 0.088, 2, -93.42, 0.48, 0.912, 4, 16, 22.05, 16.48, 0.27942, 17, -9.34, 13.53, 0.14216, 21, -33.69, 3.26, 0.15442, 26, 0.52, -2.55, 0.424, 3, 16, 29.62, 35.71, 0.13801, 17, -17.67, 32.45, 0.10561, 21, -13.28, -0.01, 0.75638, 3, 17, -28.82, 53.15, 8e-05, 21, 10.15, -1.88, 0.84186, 20, -44.66, -2.26, 0.15806, 3, 19, -13.33, -62.88, 0.00135, 21, 40.39, -2.76, 0.22176, 20, -14.42, -3.14, 0.77689, 3, 18, 18.88, -40.25, 0.10153, 19, -9.12, -39.21, 0.07311, 20, 9.2, 1.35, 0.82536, 3, 18, 16.96, -15.8, 0.53341, 19, -5.94, -14.89, 0.23888, 20, 33.06, 7.03, 0.22771, 2, 18, 12.25, 6.74, 0.89925, 19, -5.88, 8.13, 0.10075, 2, 18, 13.11, 2.63, 0.9717, 19, -5.89, 3.93, 0.0283, 2, 18, -32.65, 23.21, 0.128, 2, 93.18, 9.23, 0.872, 3, 18, -29.02, 0.87, 0.10065, 20, 42.42, 55.03, 0.00335, 2, 71.67, 2.19, 0.896, 5, 18, -22.83, -22.35, 0.08237, 19, -46.22, -13.07, 0.00265, 21, 75.24, 45.7, 0, 20, 20.43, 45.32, 0.06697, 2, 49.69, -7.52, 0.848, 4, 18, -8.86, -50.66, 0.04443, 21, 49.43, 27.52, 0.05655, 20, -5.38, 27.14, 0.49102, 2, 23.88, -25.7, 0.408, 4, 16, 0.74, 73.49, 0.00028, 21, 17.44, 36.3, 0.1294, 20, -37.37, 35.92, 0.05432, 2, -8.12, -16.93, 0.816, 5, 16, -1.11, 49.14, 0.03914, 17, -48.84, 20.07, 0.00272, 21, -6.75, 32.89, 0.22095, 20, -61.56, 32.51, 0.00118, 2, -32.3, -20.33, 0.736, 5, 16, -2.96, 24.78, 0.05658, 17, -32.84, 1.62, 0.00239, 21, -30.94, 29.47, 0.02397, 2, -56.49, -23.75, 0.49306, 26, -24.08, -5.51, 0.424, 4, 16, -13.32, 5.22, 0.02296, 21, -52.27, 35.4, 8e-05, 2, -77.82, -17.82, 0.55296, 26, -25.26, -27.62, 0.424, 3, 17, 11.67, 2.99, 0.57049, 21, -53.95, -8.66, 0.00551, 26, 15.38, -19.79, 0.424, 3, 16, 36.28, 19.69, 0.12027, 17, -1.6, 25.9, 0.45115, 21, -27.51, -9.95, 0.42858, 4, 16, 43.47, 41.89, 0.0097, 17, -12.31, 46.63, 0.08924, 21, -4.28, -12.21, 0.86838, 20, -59.09, -12.59, 0.03268, 4, 19, -10.81, -89.56, 0.00049, 17, -21.5, 65.26, 0.00176, 21, 16.35, -14.6, 0.67551, 20, -38.46, -14.98, 0.32225, 3, 19, 1.56, -69.05, 0.03153, 21, 39.92, -18.87, 0.24743, 20, -14.89, -19.25, 0.72105, 4, 18, 39.69, -38.26, 0.01414, 19, 11.66, -41.57, 0.30063, 21, 69.2, -18.53, 0.00967, 20, 14.39, -18.91, 0.67556, 3, 18, 36.07, -12.08, 0.03178, 19, 13.53, -15.21, 0.7907, 20, 39.69, -11.28, 0.17752, 2, 18, 32.49, 7.82, 0.00949, 19, 14.15, 5, 0.99051], "hull": 30, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 24, 26, 20, 22, 22, 24, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 0, 58, 54, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 74, 74, 72, 22, 74, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 50, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106], "width": 101, "height": 50}}, "jiasha2": {"jiasha2": {"type": "mesh", "uvs": [0.03075, 0.35618, 0.15151, 0.48946, 0.29543, 0.53389, 0.46912, 0.545, 0.53033, 0.53945, 0.55349, 0.37839, 0.61139, 0.26177, 0.69079, 0.3173, 0.73711, 0.42837, 0.83471, 0.30064, 0.93728, 0.08405, 0.97036, 0, 1, 0.25066, 1, 0.52834, 0.9075, 0.67273, 0.80825, 0.76159, 0.74208, 0.79491, 0.71726, 0.9282, 0.64944, 1, 0.57334, 0.96707, 0.55223, 0.93328, 0.4695, 0.93878, 0.30239, 0.92503, 0.14685, 0.88568, 0, 0.75064, 0.00607, 0.54454, 0.14897, 0.6618, 0.29928, 0.72932, 0.46864, 0.74709, 0.53427, 0.72577, 0.638, 0.67602, 0.74597, 0.62271, 0.85182, 0.49834, 0.95344, 0.37396], "triangles": [26, 1, 2, 24, 25, 26, 1, 25, 0, 23, 24, 26, 26, 25, 1, 23, 26, 27, 23, 27, 22, 26, 2, 27, 22, 28, 21, 22, 27, 28, 28, 27, 3, 27, 2, 3, 21, 28, 29, 29, 28, 3, 17, 18, 30, 18, 19, 30, 30, 19, 29, 21, 29, 20, 19, 20, 29, 17, 30, 16, 16, 31, 15, 16, 30, 31, 29, 3, 4, 29, 4, 30, 4, 5, 30, 5, 6, 30, 30, 8, 31, 30, 7, 8, 30, 6, 7, 31, 8, 32, 15, 31, 32, 15, 32, 14, 14, 33, 13, 14, 32, 33, 33, 12, 13, 8, 9, 32, 32, 9, 33, 9, 10, 33, 33, 10, 12, 10, 11, 12], "vertices": [5, 16, -1.21, -4.72, 0.27919, 17, -10.65, -17.91, 0.14066, 20, -114.19, 21.06, 0.00764, 21, -59.38, 21.44, 0.1005, 26, -10.63, -31.48, 0.472, 5, 16, 11.54, 15.49, 0.19635, 17, -16.04, 5.37, 0.13848, 20, -91.72, 12.94, 0.03742, 21, -36.91, 13.32, 0.15575, 26, -7.72, -7.79, 0.472, 4, 16, 20.51, 41.14, 0.17426, 17, -27.93, 29.8, 0.22301, 20, -64.74, 9.67, 0.20325, 21, -9.93, 10.05, 0.39948, 6, 18, 5.82, -80.03, 0.00027, 16, 29.04, 72.67, 0.05052, 17, -44.33, 58.05, 0.1318, 19, -30.13, -75.43, 2e-05, 20, -32.12, 8.1, 0.40431, 21, 22.69, 8.48, 0.41306, 6, 18, 7.62, -68.66, 0.00345, 16, 31.53, 83.9, 0.00452, 17, -50.56, 67.73, 0.0522, 19, -26.01, -64.68, 0.00097, 20, -20.61, 8.08, 0.62521, 21, 34.2, 8.45, 0.31365, 5, 18, -0.45, -62.72, 0.01863, 17, -60.53, 66.78, 0.00829, 19, -32.68, -57.2, 0.00627, 20, -15.99, 16.97, 0.78831, 21, 38.82, 17.34, 0.1785, 4, 18, -4.87, -50.83, 0.07794, 19, -34.54, -44.65, 0.01689, 20, -4.92, 23.18, 0.83169, 21, 49.89, 23.56, 0.07349, 4, 18, 0.92, -36.72, 0.21046, 19, -25.96, -32.04, 0.0302, 20, 9.91, 19.64, 0.73717, 21, 64.72, 20.01, 0.02217, 4, 18, 8.63, -29.3, 0.42587, 19, -16.88, -26.38, 0.03594, 20, 18.43, 13.17, 0.53339, 21, 73.24, 13.54, 0.0048, 4, 18, 4.96, -9.95, 0.65838, 19, -16.46, -6.69, 0.03589, 20, 36.98, 19.78, 0.30513, 21, 91.79, 20.16, 0.0006, 3, 18, -3.43, 11.23, 0.82274, 19, -20.29, 15.77, 0.05181, 20, 56.61, 31.35, 0.12546, 3, 18, -6.91, 18.2, 0.83521, 19, -22.26, 23.31, 0.13074, 20, 62.96, 35.87, 0.03404, 3, 18, 7.91, 21.11, 0.70773, 19, -7.16, 23.09, 0.27517, 20, 68.12, 21.68, 0.0171, 3, 18, 23.19, 18.26, 0.50006, 19, 7.21, 17.14, 0.43104, 20, 67.67, 6.14, 0.0689, 3, 18, 27.96, -0.32, 0.31567, 19, 8.02, -2.02, 0.48621, 20, 50.05, -1.44, 0.19812, 4, 18, 29.43, -19.58, 0.18837, 19, 5.48, -21.17, 0.41283, 20, 31.26, -5.87, 0.39806, 21, 86.07, -5.5, 0.00074, 4, 18, 28.98, -32.15, 0.12014, 19, 2.44, -33.37, 0.25515, 20, 18.77, -7.38, 0.61678, 21, 73.58, -7, 0.00793, 4, 18, 35.46, -38.1, 0.06948, 19, 7.55, -40.54, 0.11858, 20, 13.89, -14.7, 0.78155, 21, 68.7, -14.33, 0.03039, 4, 18, 37.08, -51.37, 0.03385, 19, 6.39, -53.86, 0.03911, 20, 1.03, -18.35, 0.83829, 21, 55.84, -17.98, 0.08876, 5, 18, 32.64, -65.1, 0.00978, 17, -34.27, 87.06, 0.00637, 19, -0.79, -66.37, 0.01098, 20, -13.22, -16.09, 0.77627, 21, 41.59, -15.72, 0.1966, 6, 18, 30.06, -68.65, 0.00177, 16, 53.92, 82.55, 0, 17, -33.83, 82.68, 0.04985, 19, -4.06, -69.31, 0.0024, 20, -17.13, -14.09, 0.61229, 21, 37.68, -13.71, 0.3337, 6, 18, 27.51, -84, 9e-05, 16, 50.45, 67.39, 0.02699, 17, -25.5, 69.54, 0.14045, 19, -9.73, -83.8, 0.00024, 20, -32.69, -13.94, 0.39847, 21, 22.12, -13.57, 0.43377, 4, 16, 42.08, 37.09, 0.11723, 17, -9.88, 42.27, 0.25981, 20, -64.07, -12.26, 0.20105, 21, -9.26, -11.88, 0.42191, 5, 16, 32.85, 9.26, 0.15332, 17, 3.39, 16.12, 0.17122, 20, -93.23, -9.21, 0.03736, 21, -38.42, -8.83, 0.16609, 26, 12.64, -4.55, 0.472, 5, 16, 18.82, -15.69, 0.24621, 17, 11.23, -11.41, 0.1676, 20, -120.61, -0.85, 0.00749, 21, -65.8, -0.47, 0.1067, 26, 10.47, -33.08, 0.472, 5, 16, 7.9, -11.78, 0.28436, 17, 0.77, -16.41, 0.13413, 20, -119.14, 10.65, 0.0056, 21, -64.33, 11.03, 0.1039, 26, -0.23, -34.09, 0.472, 5, 16, 20.79, 12.69, 0.21108, 17, -7.54, 9.97, 0.10516, 20, -92.47, 3.31, 0.03585, 21, -37.66, 3.69, 0.1759, 26, 1.16, -6.47, 0.472, 4, 16, 31.31, 39.18, 0.20723, 17, -18.95, 36.09, 0.14436, 20, -64.34, -1.29, 0.19819, 21, -9.53, -0.91, 0.45022, 6, 18, 16.93, -82.19, 0.00797, 16, 40, 69.83, 0.06733, 17, -34.6, 63.84, 0.08753, 19, -19.71, -79.84, 0.00535, 20, -32.54, -3.21, 0.38626, 21, 22.27, -2.83, 0.44555, 6, 18, 18.01, -69.84, 0.04946, 16, 41.83, 82.09, 0.01035, 17, -42.02, 73.77, 0.03595, 19, -16.09, -67.99, 0.02414, 20, -20.17, -2.37, 0.55918, 21, 34.64, -2, 0.32091, 5, 18, 18.85, -50.16, 0.15345, 17, -54.51, 89, 0.00703, 19, -11.2, -48.9, 0.06444, 20, -0.6, -0.15, 0.60614, 21, 54.21, 0.22, 0.16894, 4, 18, 19.64, -29.66, 0.3233, 19, -6.19, -29.01, 0.11827, 20, 19.78, 2.24, 0.50014, 21, 74.59, 2.62, 0.05829, 4, 18, 16.44, -8.82, 0.49826, 19, -5, -7.96, 0.16807, 20, 39.87, 8.62, 0.32177, 21, 94.68, 9, 0.01191, 4, 18, 13.09, 11.24, 0.5914, 19, -4.13, 12.36, 0.19166, 20, 59.17, 15.03, 0.21678, 21, 113.98, 15.41, 0.00016], "hull": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 0, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66], "width": 94, "height": 28}}, "jiasha3": {"jiasha3": {"type": "mesh", "uvs": [0, 0.00804, 0.41786, 0.00435, 0.93107, 0, 0.94154, 0.26232, 0.98344, 0.52765, 1, 0.81141, 0.51212, 1, 0, 0.81141, 0, 0.53502, 0, 0.266, 0.40739, 0.26231, 0.40739, 0.5387, 0.48071, 0.85563], "triangles": [10, 0, 1, 1, 2, 3, 7, 12, 6, 6, 12, 5, 5, 12, 11, 12, 7, 11, 11, 4, 5, 7, 8, 11, 11, 3, 4, 11, 8, 9, 11, 9, 10, 11, 10, 3, 9, 0, 10, 10, 1, 3], "vertices": [1, 20, -7.67, -4.53, 1, 1, 20, 0.27, -4.56, 1, 1, 20, 10.02, -4.61, 1, 1, 22, 5.21, 9.6, 1, 2, 22, 19.55, 10.13, 0.39375, 23, 2.42, 10.01, 0.60625, 1, 23, 17.73, 9.15, 1, 1, 23, 27.17, -0.88, 1, 2, 22, 34.53, -8.83, 5e-05, 23, 16.27, -9.8, 0.99995, 2, 22, 19.6, -8.56, 0.45211, 23, 1.39, -8.65, 0.54789, 1, 22, 5.08, -8.29, 1, 1, 22, 5.02, -0.55, 1, 2, 22, 19.95, -0.82, 0.04543, 23, 2.18, -0.95, 0.95457, 1, 23, 19.35, -0.87, 1], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18, 2, 20, 20, 22, 22, 24], "width": 10, "height": 27}}, "jiasha4": {"jiasha4": {"type": "mesh", "uvs": [0.03043, 0, 0.30019, 0.02633, 0.59648, 0.01126, 0.66281, 0.07759, 0.66281, 0.1982, 0.70261, 0.41227, 0.81317, 0.66253, 0.94583, 0.85852, 1, 1, 0.67165, 0.99118, 0.29134, 0.96706, 0.1233, 0.95802, 0.21174, 0.77409, 0.21174, 0.57509, 0.06139, 0.39117, 0, 0.16503, 0.2825, 0.14091, 0.33999, 0.37609, 0.47708, 0.58715, 0.53014, 0.82233], "triangles": [1, 4, 16, 16, 0, 1, 1, 2, 3, 3, 4, 1, 15, 0, 16, 18, 17, 5, 13, 17, 18, 18, 5, 6, 12, 13, 18, 19, 18, 6, 12, 18, 19, 19, 6, 7, 10, 12, 19, 11, 12, 10, 9, 19, 7, 10, 19, 9, 9, 7, 8, 17, 16, 4, 14, 15, 16, 14, 16, 17, 17, 4, 5, 13, 14, 17], "vertices": [1, 2, -54.52, -3.73, 1, 1, 2, -42.43, -5.82, 1, 1, 2, -29.08, -5.21, 1, 1, 2, -26.22, -9.67, 1, 2, 24, 11.03, 15.87, 0.98595, 25, -15.77, 14.8, 0.01405, 2, 24, 25.06, 13.44, 0.48423, 25, -1.61, 13.36, 0.51577, 1, 25, 15.61, 14.47, 1, 1, 25, 29.56, 17.37, 1, 1, 25, 39.2, 17.64, 1, 1, 25, 35.3, 3.37, 1, 1, 25, 29.89, -12.94, 1, 1, 25, 27.6, -20.17, 1, 1, 25, 16.67, -13.55, 1, 2, 24, 28.86, -10.83, 0.19766, 25, 3.88, -10.59, 0.80234, 2, 24, 15.27, -13.74, 0.92154, 25, -9.48, -14.44, 0.07846, 2, 24, -17.32, -6.64, 0.368, 2, -56.51, -25.24, 0.632, 2, 24, -14.89, 5.91, 0.376, 2, -43.77, -24.24, 0.624, 2, 24, 17.99, -1.47, 0.98935, 25, -7.62, -2, 0.01065, 1, 25, 7.35, 0.86, 1, 1, 25, 23.01, -0.31, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30, 2, 32, 32, 34, 34, 36, 36, 38], "width": 23, "height": 33}}, "youjiao": {"youjiao": {"type": "mesh", "uvs": [0.16606, 0.08853, 0.35377, 0, 0.64255, 0, 0.89523, 0.09193, 1, 0.27566, 0.97825, 0.50701, 0.94216, 0.64651, 0.9205, 0.84044, 0.83747, 1, 0.64255, 1, 0.38265, 1, 0.09026, 0.95952, 0, 0.85064, 0, 0.6329, 0.02889, 0.43897, 0.09026, 0.25184, 0.53787, 0.16678, 0.48372, 0.3403, 0.45484, 0.4968, 0.42235, 0.68393, 0.39347, 0.85064], "triangles": [8, 9, 7, 10, 20, 9, 10, 11, 20, 11, 12, 20, 9, 20, 7, 12, 13, 20, 20, 19, 7, 20, 13, 19, 7, 19, 6, 19, 13, 14, 6, 19, 18, 5, 6, 18, 5, 18, 17, 5, 17, 16, 16, 2, 3, 18, 19, 14, 4, 5, 16, 4, 16, 3, 14, 15, 18, 18, 15, 17, 15, 0, 17, 17, 0, 16, 16, 0, 1, 16, 1, 2], "vertices": [2, 26, 0.44, -31.43, 0.99801, 27, -34.87, -31.43, 0.00199, 1, 26, -9.2, -17.73, 1, 1, 26, -13.25, 5.55, 1, 1, 26, -9.51, 27.39, 1, 3, 26, 3.58, 38.78, 0.93565, 27, -31.73, 38.78, 0.00835, 44, -51.68, -67.41, 0.056, 3, 26, 22.22, 40.74, 0.74334, 27, -13.1, 40.74, 0.11266, 44, -53.33, -47.27, 0.144, 3, 26, 33.78, 40.07, 0.4925, 27, -1.54, 40.07, 0.2515, 44, -52.46, -34.81, 0.256, 3, 26, 49.45, 41.43, 0.1178, 27, 14.13, 41.43, 0.2182, 44, -53.56, -17.88, 0.664, 1, 44, -49.2, -3.04, 1, 3, 26, 65.99, 21.59, 0.00077, 27, 30.67, 21.59, 0.00723, 44, -33.44, -0.34, 0.992, 1, 44, -12.44, 3.26, 1, 1, 44, 11.79, 3.84, 1, 1, 44, 20.68, -4.25, 1, 2, 26, 45.91, -36.08, 0.34647, 27, 10.59, -36.08, 0.65353, 2, 26, 30.14, -36.87, 0.71171, 27, -5.18, -36.87, 0.28829, 2, 26, 14.45, -34.92, 0.94389, 27, -20.87, -34.92, 0.05611, 1, 26, 1.43, -0.21, 1, 1, 26, 15.94, -1.79, 1, 1, 26, 28.75, -1.61, 1, 2, 26, 44.03, -1.22, 0.00116, 27, 8.72, -1.22, 0.99884, 1, 27, 22.33, -0.88, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30, 4, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 14], "width": 41, "height": 44}}, "fangun": {"fangun": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [152.95, -135.46, -145.05, -135.46, -145.05, 144.54, 152.95, 144.54], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 149, "height": 140}}, "zuoshou": {"zuoshou": {"type": "mesh", "uvs": [0.15143, 0, 0.35426, 0.00755, 0.53115, 0.08141, 0.7269, 0.18226, 0.82008, 0.28278, 0.88492, 0.35272, 0.94265, 0.45177, 1, 0.55016, 1, 0.73908, 0.93917, 0.92516, 0.79058, 1, 0.49577, 1, 0.22219, 0.94363, 0.14672, 0.82431, 0.27171, 0.71351, 0.2591, 0.6215, 0.25049, 0.55868, 0.1609, 0.49124, 0.08068, 0.43084, 0, 0.30442, 0, 0.13681, 0.02643, 0.03596, 0.18681, 0.11976, 0.43917, 0.29732, 0.60898, 0.43226, 0.65615, 0.58709, 0.63492, 0.76323, 0.54766, 0.89391], "triangles": [26, 14, 25, 26, 25, 8, 27, 14, 26, 13, 14, 27, 9, 26, 8, 27, 26, 9, 12, 13, 27, 11, 12, 27, 10, 27, 9, 11, 27, 10, 24, 4, 5, 24, 5, 6, 16, 17, 24, 25, 24, 6, 25, 6, 7, 15, 16, 24, 25, 15, 24, 14, 15, 25, 25, 7, 8, 22, 0, 1, 21, 0, 22, 20, 21, 22, 2, 22, 1, 23, 2, 3, 23, 3, 4, 23, 22, 2, 19, 20, 22, 19, 22, 23, 18, 19, 23, 24, 23, 4, 17, 18, 23, 17, 23, 24], "vertices": [1, 10, -25.73, 12.95, 1, 1, 10, -10.33, 28.01, 1, 1, 10, 11.89, 33.21, 1, 2, 10, 38.97, 36.7, 0.9827, 11, -37.91, 18.4, 0.0173, 2, 10, 58.72, 32.17, 0.74024, 11, -19.09, 25.9, 0.25976, 2, 10, 72.46, 29.01, 0.38156, 11, -6, 31.13, 0.61844, 3, 10, 89.49, 21.86, 0.05476, 11, 12.08, 34.94, 0.94485, 12, -52.26, 13.63, 0.00038, 3, 10, 106.42, 14.76, 0.0001, 11, 30.04, 38.73, 0.95943, 12, -37.72, 24.83, 0.04046, 2, 11, 63.01, 34.43, 0.41462, 12, -6.14, 35.25, 0.58538, 2, 11, 94.65, 23.8, 0.0075, 12, 26.98, 39.39, 0.9925, 1, 12, 44.42, 28.57, 1, 1, 12, 54.22, -1.11, 1, 2, 11, 88.04, -51.98, 0.00866, 12, 53.89, -31.76, 0.99134, 3, 10, 81.57, -84.69, 0.00048, 11, 66.18, -57.2, 0.06249, 12, 36.45, -45.94, 0.93704, 3, 10, 75.99, -61.79, 0.03382, 11, 48.56, -41.54, 0.31695, 12, 13.78, -39.47, 0.64923, 3, 10, 63.08, -51.92, 0.16859, 11, 32.33, -40.77, 0.55985, 12, -1.18, -45.81, 0.27156, 3, 10, 54.26, -45.19, 0.37404, 11, 21.25, -40.25, 0.51997, 12, -11.39, -50.15, 0.10599, 3, 10, 39.09, -44.27, 0.7014, 11, 8.25, -48.13, 0.28039, 12, -19.68, -62.88, 0.01821, 3, 10, 25.5, -43.46, 0.86433, 11, -3.39, -55.19, 0.13351, 12, -27.11, -74.29, 0.00217, 2, 10, 3.26, -34.88, 0.98124, 11, -26.56, -60.79, 0.01876, 1, 10, -18.63, -15.1, 1, 1, 10, -29.92, -1.12, 1, 1, 10, -7.58, 1.6, 1, 1, 10, 33.54, 0.49, 1, 1, 11, 4.1, 0.31, 1, 1, 11, 31.77, 1.75, 1, 3, 10, 108.29, -39.09, 0, 11, 62.22, -4.5, 0.00068, 12, 10.02, -0.16, 0.99932, 2, 11, 83.83, -16.64, 0.00034, 12, 34.76, -1.74, 0.99966], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 36, 38, 38, 40, 40, 42, 0, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 32, 34, 34, 36, 34, 48, 6, 8, 8, 10, 8, 48, 10, 12, 12, 14, 12, 48, 28, 30, 30, 32, 48, 30], "width": 53, "height": 88}}, "youshou": {"youshou": {"type": "mesh", "uvs": [0.91182, 0.05217, 1, 0.18898, 1, 0.28129, 0.92046, 0.38677, 0.86852, 0.45935, 0.82255, 0.52358, 0.78224, 0.65215, 0.78512, 0.7494, 0.77936, 0.90598, 0.56052, 1, 0.29272, 0.99828, 0.10267, 0.92246, 0.00189, 0.77412, 0, 0.59446, 0.00604, 0.46296, 0.01053, 0.36535, 0.08246, 0.26756, 0.14875, 0.17744, 0.37047, 0.04723, 0.58931, 0, 0.77936, 0.00602, 0.72753, 0.14448, 0.5634, 0.26151, 0.44822, 0.39831, 0.37623, 0.54995, 0.32152, 0.68841, 0.35607, 0.87961], "triangles": [26, 25, 7, 12, 25, 26, 8, 26, 7, 11, 12, 26, 10, 11, 26, 9, 26, 8, 10, 26, 9, 15, 16, 23, 4, 23, 3, 14, 15, 23, 5, 23, 4, 24, 14, 23, 24, 23, 5, 13, 14, 24, 6, 24, 5, 25, 13, 24, 25, 24, 6, 25, 6, 7, 12, 13, 25, 21, 19, 20, 21, 20, 0, 21, 0, 1, 21, 22, 18, 21, 18, 19, 17, 18, 22, 21, 1, 2, 22, 21, 2, 3, 22, 2, 22, 16, 17, 23, 22, 3, 23, 16, 22], "vertices": [1, 13, -22.59, 22.87, 1, 2, 13, -5.67, 35.55, 0.98704, 14, -39.57, 37.43, 0.01296, 2, 13, 7.15, 39.37, 0.94059, 14, -26.58, 40.65, 0.05941, 3, 13, 23.7, 37.41, 0.75991, 14, -10.15, 37.93, 0.23989, 15, -48.79, 45.76, 0.0002, 3, 13, 35.01, 36.29, 0.51931, 14, 1.11, 36.28, 0.47207, 15, -37.91, 42.44, 0.00861, 3, 13, 45.03, 35.29, 0.27501, 14, 11.06, 34.82, 0.67937, 15, -28.28, 39.5, 0.04562, 3, 13, 63.85, 37.41, 0.03342, 14, 29.96, 36.06, 0.65925, 15, -9.41, 37.9, 0.30733, 3, 13, 77.3, 41.67, 0.00146, 14, 43.59, 39.69, 0.36946, 15, 4.61, 39.44, 0.62908, 2, 14, 65.74, 44.69, 0.07993, 15, 27.26, 41.07, 0.92007, 2, 14, 83.35, 30.34, 0.00404, 15, 42.52, 24.24, 0.99596, 1, 15, 44.33, 2.09, 1, 1, 15, 34.84, -14.63, 1, 2, 14, 62.73, -22.55, 0.06152, 15, 14.2, -24.96, 0.93848, 3, 13, 74.38, -27.2, 0.00131, 14, 37.48, -28.97, 0.7185, 15, -11.73, -27.53, 0.28018, 3, 13, 55.96, -32.16, 0.09202, 14, 18.85, -33.08, 0.87888, 15, -30.76, -28.79, 0.0291, 3, 13, 42.29, -35.85, 0.30271, 14, 5.03, -36.12, 0.69649, 15, -44.88, -29.73, 0.0008, 2, 13, 27, -34.18, 0.63607, 14, -10.17, -33.74, 0.36393, 2, 13, 12.91, -32.64, 0.87803, 14, -24.18, -31.55, 0.12197, 2, 13, -10.45, -20.4, 0.99979, 14, -46.94, -18.24, 0.00021, 1, 13, -22.2, -4.95, 1, 1, 13, -25.87, 10.42, 1, 2, 13, -5.4, 12.03, 0.99982, 14, -40.39, 13.92, 0.00018, 2, 13, 14.76, 3.82, 0.99713, 14, -20.64, 4.79, 0.00287, 2, 13, 36.5, 0.33, 0.18881, 14, 0.92, 0.29, 0.81119, 1, 14, 23.7, -0.22, 1, 2, 14, 44.27, 0.21, 0.72775, 15, -0.63, 0.31, 0.27225, 1, 15, 26.71, 5.73, 1], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 34, 36, 36, 38, 38, 40, 0, 40, 0, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 46, 10, 6, 46, 30, 46, 26, 28, 28, 30, 46, 28, 6, 8, 8, 10, 30, 32, 32, 34], "width": 42, "height": 73}}, "fozhu": {"fozhu": {"type": "mesh", "uvs": [0.02744, 0.37748, 0.10547, 0.32442, 0.22445, 0.37748, 0.24786, 0.49327, 0.21275, 0.60906, 0.23616, 0.67901, 0.34929, 0.71037, 0.49559, 0.69348, 0.62043, 0.63077, 0.71406, 0.48121, 0.74722, 0.32924, 0.6731, 0.23516, 0.60873, 0.17245, 0.62238, 0.03254, 0.71796, 0, 0.8467, 0.0253, 0.96959, 0.17968, 1, 0.36301, 0.97544, 0.57287, 0.89351, 0.73932, 0.78038, 0.90335, 0.62433, 1, 0.39026, 1, 0.16984, 0.98054, 0.05865, 0.85993, 0, 0.76585, 0, 0.54634], "triangles": [22, 7, 21, 7, 8, 21, 21, 8, 20, 23, 6, 22, 22, 6, 7, 23, 5, 6, 23, 24, 5, 20, 8, 19, 24, 25, 5, 8, 9, 19, 19, 9, 18, 9, 10, 18, 25, 4, 5, 4, 26, 3, 3, 26, 0, 3, 1, 2, 1, 3, 0, 18, 10, 17, 25, 26, 4, 10, 16, 17, 10, 11, 15, 10, 15, 16, 15, 11, 14, 11, 12, 14, 14, 12, 13], "vertices": [2, 8, -37.7, 47.76, 0.92593, 9, 60.45, 72.47, 0.07407, 2, 8, -30.44, 35.3, 0.88889, 9, 67.71, 60, 0.11111, 2, 8, -36.92, 15.86, 0.74074, 9, 61.23, 40.56, 0.25926, 2, 8, -51.99, 11.69, 0.59259, 9, 46.16, 36.4, 0.40741, 2, 8, -67.3, 17.01, 0.40741, 9, 30.86, 41.72, 0.59259, 2, 8, -76.36, 13, 0.25926, 9, 21.79, 37.7, 0.74074, 2, 8, -80.02, -5.43, 0.11111, 9, 18.13, 19.28, 0.88889, 2, 8, -77.24, -29.06, 0.07407, 9, 20.92, -4.36, 0.92593, 2, 8, -68.53, -49.08, 0.14815, 9, 29.63, -24.38, 0.85185, 2, 8, -48.57, -63.77, 0.37037, 9, 49.58, -39.06, 0.62963, 2, 8, -28.54, -68.65, 0.62963, 9, 69.61, -43.95, 0.37037, 2, 8, -16.51, -56.35, 0.85185, 9, 81.64, -31.64, 0.14815, 2, 8, -8.55, -45.72, 0.96296, 9, 89.6, -21.02, 0.03704, 1, 8, 9.82, -47.49, 1, 1, 8, 14.46, -62.86, 1, 2, 8, 11.66, -83.79, 0.96296, 9, 109.81, -59.09, 0.03704, 2, 8, -8.08, -104.19, 0.85185, 9, 90.08, -79.48, 0.14815, 2, 8, -31.96, -109.7, 0.62963, 9, 66.19, -84.99, 0.37037, 2, 8, -59.54, -106.39, 0.37037, 9, 38.61, -81.69, 0.62963, 2, 8, -81.67, -93.66, 0.14815, 9, 16.49, -68.95, 0.85185, 2, 8, -103.59, -75.86, 0.03704, 9, -5.44, -51.15, 0.96296, 1, 9, -18.71, -26.19, 1, 2, 8, -117.79, -12.99, 0.03704, 9, -19.64, 11.72, 0.96296, 2, 8, -116.12, 22.77, 0.14815, 9, -17.96, 47.48, 0.85185, 2, 8, -100.76, 41.17, 0.37037, 9, -2.6, 65.87, 0.62963, 2, 8, -88.67, 50.97, 0.62963, 9, 9.48, 75.67, 0.37037, 2, 8, -59.92, 51.67, 0.85185, 9, 38.23, 76.37, 0.14815], "hull": 27, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 0, 52], "width": 162, "height": 131}}, "body": {"body": {"type": "mesh", "uvs": [0.13905, 0.22077, 0.24932, 0.12032, 0.39433, 0.04538, 0.562, 0, 0.74024, 0, 0.90187, 0.15061, 0.99552, 0.36267, 1, 0.58111, 1, 0.8139, 0.88827, 0.92232, 0.6481, 0.98929, 0.42303, 1, 0.2886, 1, 0.20401, 1, 0.03785, 0.93986, 0, 0.86014, 0, 0.67199, 0.02576, 0.63692, 0.00915, 0.56038, 0.06051, 0.40413, 0.37621, 0.21598, 0.31427, 0.40413, 0.28709, 0.59227, 0.28558, 0.65764, 0.26443, 0.80752, 0.21156, 0.22077, 0.17229, 0.40891, 0.13603, 0.57473, 0.15416, 0.65764, 0.1164, 0.83463, 0.56804, 0.20482, 0.57257, 0.39456, 0.57409, 0.59068, 0.58013, 0.65605, 0.56653, 0.81549, 0.78556, 0.20323, 0.79915, 0.39934, 0.79462, 0.5843, 0.78707, 0.76447], "triangles": [14, 29, 13, 13, 24, 12, 11, 12, 24, 9, 38, 8, 8, 38, 7, 10, 38, 9, 10, 34, 38, 10, 11, 34, 38, 37, 7, 37, 36, 7, 37, 31, 36, 36, 6, 7, 31, 35, 36, 6, 35, 5, 6, 36, 35, 31, 30, 35, 30, 4, 35, 30, 3, 4, 35, 4, 5, 17, 18, 27, 18, 19, 27, 27, 19, 26, 21, 26, 25, 25, 26, 0, 26, 19, 0, 21, 20, 31, 21, 25, 20, 20, 30, 31, 25, 1, 20, 25, 0, 1, 20, 2, 30, 20, 1, 2, 2, 3, 30, 23, 28, 22, 32, 23, 22, 22, 28, 27, 33, 32, 37, 27, 26, 22, 22, 21, 32, 22, 26, 21, 21, 31, 32, 32, 31, 37, 34, 11, 24, 13, 29, 24, 14, 15, 29, 15, 16, 29, 29, 16, 28, 29, 28, 24, 28, 16, 17, 24, 23, 34, 34, 23, 33, 34, 33, 38, 23, 32, 33, 24, 28, 23, 38, 33, 37, 17, 27, 28], "vertices": [2, 5, 39.47, 35.08, 0.28059, 7, 20.64, 2.89, 0.71941, 3, 5, 53.86, 11.44, 0.81757, 7, 39.22, -17.61, 0.16941, 6, 14.13, 109.73, 0.01302, 4, 4, 106.55, -5.47, 0.02148, 5, 62.63, -17.95, 0.81527, 7, 53.38, -44.83, 0.00168, 6, 28.29, 82.52, 0.16157, 3, 4, 115.5, -37.12, 0.04193, 5, 65.45, -50.72, 0.49333, 6, 37.23, 50.87, 0.46475, 3, 4, 116.32, -70.97, 0.01081, 5, 59.89, -84.12, 0.209, 6, 38.06, 17.02, 0.78019, 3, 3, 153.94, -97.93, 0.00067, 5, 28.1, -109.96, 0.00341, 6, 11.7, -14.35, 0.99592, 3, 3, 117.15, -118.42, 0.08187, 4, 52.24, -121.05, 0.09125, 6, -26.02, -33.06, 0.82688, 3, 3, 78, -122.11, 0.24083, 4, 12.96, -122.86, 0.18443, 6, -65.31, -34.87, 0.57474, 1, 2, 93.76, 16.47, 1, 1, 2, 71.98, -2.42, 1, 1, 2, 26.01, -13.14, 1, 1, 2, -16.79, -13.83, 1, 1, 2, -42.32, -13.09, 1, 1, 2, -58.39, -12.62, 1, 1, 2, -89.63, -0.88, 1, 4, 3, 14.21, 63.78, 0.56577, 4, -41.89, 65.85, 0.03706, 7, -95.06, 26.5, 0.08518, 31, -12.07, 35.69, 0.312, 4, 3, 47.99, 66.22, 0.41884, 4, -8.03, 66.68, 0.13246, 7, -61.21, 27.32, 0.21669, 31, 21.7, 38.13, 0.232, 3, 3, 54.64, 61.79, 0.44966, 4, -1.6, 61.94, 0.20578, 7, -54.77, 22.58, 0.34457, 3, 3, 68.16, 65.94, 0.27485, 4, 12.1, 65.43, 0.23393, 7, -41.08, 26.08, 0.49122, 3, 3, 96.91, 58.23, 0.05805, 4, 40.45, 56.36, 0.12002, 7, -12.72, 17.01, 0.82194, 4, 4, 75.77, -2.78, 0.04158, 5, 32.91, -9.51, 0.90463, 7, 22.59, -42.13, 0.00034, 6, -2.49, 85.21, 0.05345, 3, 4, 41.63, 8.16, 0.27589, 5, 1.44, 7.67, 0.63821, 7, -11.55, -31.19, 0.0859, 4, 3, 66.23, 12.85, 0.16914, 4, 7.65, 12.5, 0.75017, 5, -31.12, 18.33, 0.00044, 7, -45.53, -26.86, 0.08026, 4, 3, 54.48, 12.29, 0.48089, 4, -4.12, 12.5, 0.20821, 7, -57.3, -26.86, 0.0389, 31, 28.19, -15.8, 0.272, 4, 3, 27.28, 14.35, 0.6467, 4, -31.19, 15.86, 0.00755, 7, -84.37, -23.5, 0.01775, 31, 0.99, -13.74, 0.328, 2, 5, 37.2, 21.49, 0.53591, 7, 20.97, -10.88, 0.46409, 4, 3, 97.58, 36.98, 0.02383, 4, 40.11, 35.11, 0.16657, 5, 5.02, 34.42, 0.08474, 7, -13.07, -4.25, 0.72486, 4, 3, 67.32, 41.7, 0.27689, 4, 10.1, 41.27, 0.32518, 5, -23.29, 46.12, 0.00112, 7, -43.07, 1.91, 0.3968, 4, 3, 52.68, 37.19, 0.32425, 4, -4.73, 37.46, 0.16366, 7, -57.91, -1.89, 0.15209, 31, 26.39, 9.1, 0.36, 4, 3, 20.39, 42.06, 0.6097, 4, -36.76, 43.86, 0.03576, 7, -89.93, 4.5, 0.07454, 31, -5.9, 13.96, 0.28, 4, 3, 139.64, -35.37, 0.00041, 4, 78.67, -39.16, 0.19521, 5, 28.9, -45.79, 0.39547, 6, 0.4, 48.82, 0.40892, 4, 3, 105.64, -38.69, 0.03487, 4, 44.55, -40.86, 0.58329, 5, -4.93, -41.02, 0.07839, 6, -33.72, 47.13, 0.30345, 3, 3, 70.45, -41.52, 0.27058, 4, 9.26, -42.01, 0.53087, 6, -69, 45.98, 0.19855, 3, 3, 58.8, -43.51, 0.42122, 4, -2.47, -43.44, 0.39724, 6, -80.74, 44.55, 0.18154, 4, 3, 29.98, -43, 0.50942, 4, -31.23, -41.56, 0.09137, 6, -109.49, 46.43, 0.08721, 31, 3.7, -71.09, 0.312, 4, 3, 142.9, -76.57, 0.00043, 4, 79.96, -80.47, 0.0256, 5, 22.39, -86.6, 0.03688, 6, 1.7, 7.52, 0.93709, 4, 3, 107.88, -81.69, 0.08483, 4, 44.73, -83.92, 0.20854, 5, -12.86, -83.35, 0.00418, 6, -33.53, 4.07, 0.70245, 3, 3, 74.61, -83.23, 0.25472, 4, 11.43, -83.87, 0.27286, 6, -66.83, 4.12, 0.47242, 3, 3, 42.16, -84.13, 0.45494, 4, -21.02, -83.22, 0.20792, 6, -99.29, 4.77, 0.33714], "hull": 20, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 0, 38, 4, 40, 40, 42, 42, 44, 44, 46, 46, 48, 22, 24, 24, 26, 48, 24, 2, 50, 50, 52, 52, 54, 54, 56, 56, 58, 6, 60, 60, 62, 62, 64, 64, 66, 66, 68, 8, 70, 70, 72, 72, 74, 74, 76], "width": 95, "height": 90}}, "xiongtou": {"xiongtou": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-21.88, -63.76, 0.81, 72.36, 133.97, 50.17, 111.28, -85.95], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 69, "height": 68}}, "eye": {"eye": {"type": "mesh", "uvs": [0.38662, 0.7654, 0.48492, 0.71404, 0.48423, 0.57377, 0.56385, 0.39992, 0.68015, 0.24977, 0.75838, 0.24384, 0.77154, 0.10357, 0.82969, 0.07196, 0.89477, 0.01072, 0.98338, 0, 1, 0.06999, 0.97231, 0.20433, 0.919, 0.29521, 0.87123, 0.39004, 0.81238, 0.46313, 0.75354, 0.47696, 0.74108, 0.60735, 0.72031, 0.78713, 0.66285, 0.91555, 0.56385, 0.90567, 0.49669, 0.82072, 0.48423, 0.75355, 0.38731, 0.80294, 0.36513, 0.90134, 0.33665, 0.99003, 0.26672, 0.958, 0.21493, 0.84222, 0.19853, 0.74122, 0.13469, 0.70781, 0.06796, 0.67508, 0.03563, 0.56498, 0, 0.43703, 0.00852, 0.34181, 0.06587, 0.32396, 0.10758, 0.36561, 0.14721, 0.38644, 0.21186, 0.47274, 0.24001, 0.53225, 0.31404, 0.562, 0.36514, 0.62449, 0.61227, 0.63938, 0.72592, 0.38943, 0.80517, 0.29123, 0.89068, 0.18708, 0.95533, 0.09782, 0.27964, 0.7584, 0.21603, 0.64532, 0.14825, 0.57094, 0.09194, 0.49655, 0.04189, 0.42513, 0.75095, 0.34479], "triangles": [11, 12, 44, 11, 44, 10, 44, 9, 10, 44, 8, 9, 13, 43, 12, 13, 42, 43, 42, 7, 43, 12, 43, 44, 43, 8, 44, 43, 7, 8, 15, 50, 14, 50, 42, 14, 14, 42, 13, 50, 5, 42, 50, 4, 5, 5, 6, 42, 42, 6, 7, 30, 49, 48, 30, 31, 49, 49, 33, 48, 48, 33, 34, 31, 32, 49, 49, 32, 33, 29, 48, 28, 28, 48, 47, 35, 48, 34, 48, 35, 47, 29, 30, 48, 27, 28, 47, 47, 36, 46, 47, 35, 36, 46, 36, 37, 27, 46, 45, 46, 37, 45, 23, 24, 45, 24, 25, 45, 25, 26, 45, 0, 23, 45, 23, 0, 22, 0, 45, 39, 39, 45, 38, 26, 27, 45, 22, 0, 21, 45, 37, 38, 46, 27, 47, 19, 40, 18, 18, 40, 17, 19, 20, 40, 21, 1, 20, 20, 1, 40, 17, 40, 16, 21, 0, 1, 1, 2, 40, 2, 3, 40, 40, 41, 16, 40, 4, 41, 40, 3, 4, 16, 41, 15, 41, 4, 50, 41, 50, 15], "vertices": [1, 40, -0.64, -10.98, 1, 1, 36, -0.87, 15.62, 1, 1, 36, 4.82, 14.76, 1, 1, 36, 10.31, 4.4, 1, 1, 36, 14.15, -10.04, 1, 3, 36, 12.88, -19.11, 0.09021, 37, 5.84, 3.96, 0.80225, 38, 0.53, 15.11, 0.10753, 3, 36, 18.3, -21.57, 0.0027, 37, 11.26, 1.49, 0.61417, 38, 5.95, 12.65, 0.38312, 3, 37, 11.42, -5.43, 0.29139, 38, 6.11, 5.72, 0.70609, 39, 3.96, 15.11, 0.00252, 3, 37, 12.65, -13.35, 0.00594, 38, 7.33, -2.2, 0.69121, 39, 5.19, 7.18, 0.30286, 2, 41, 9.39, -96.42, 0, 39, 3.92, -3.12, 1, 2, 41, 6.24, -97.86, 0, 39, 0.77, -4.56, 1, 3, 41, 1.34, -93.76, 0, 38, -1.99, -9.85, 0.12443, 39, -4.13, -0.46, 0.87557, 4, 41, -1.31, -87, 0, 37, 0.68, -14.23, 0.02702, 38, -4.64, -3.08, 0.80409, 39, -6.78, 6.31, 0.16888, 4, 41, -4.22, -80.85, 0, 37, -2.24, -8.08, 0.41898, 38, -7.55, 3.07, 0.58094, 39, -9.7, 12.46, 8e-05, 5, 36, 2.98, -23.86, 0.00699, 41, -6.05, -73.56, 0, 37, -4.06, -0.8, 0.95982, 38, -9.38, 10.35, 0.03319, 39, -11.52, 19.74, 0, 1, 36, 3.55, -16.98, 1, 1, 36, -1.48, -14.66, 1, 1, 36, -8.35, -11.05, 1, 1, 36, -12.44, -3.55, 1, 1, 36, -10.14, 7.8, 1, 1, 36, -5.41, 14.98, 1, 1, 36, -2.46, 15.97, 1, 1, 40, -2.17, -10.8, 1, 1, 40, -5.72, -7.58, 1, 1, 40, -8.76, -3.69, 1, 1, 40, -6.12, 4.16, 1, 1, 40, -0.44, 9.36, 1, 1, 40, 3.96, 10.57, 1, 2, 41, -2.91, 6.3, 0.41379, 42, -6.32, -1.54, 0.58621, 3, 41, -0.3, 13.78, 4e-05, 42, -3.71, 5.94, 0.80785, 43, -9.91, -0.08, 0.19211, 2, 42, 1.36, 8.93, 0.30507, 43, -4.83, 2.91, 0.69493, 1, 43, 1.03, 6.16, 1, 1, 43, 4.71, 4.53, 1, 3, 41, 13.94, 11.65, 0.0002, 42, 10.53, 3.81, 0.04695, 43, 4.33, -2.21, 0.95285, 3, 41, 11.45, 7.12, 0.05527, 42, 8.04, -0.72, 0.44148, 43, 1.85, -6.74, 0.50326, 3, 41, 9.85, 2.69, 0.31674, 42, 6.44, -5.15, 0.55578, 43, 0.24, -11.17, 0.12747, 3, 40, 14.56, 7.22, 0.05201, 41, 5.11, -4.19, 0.88362, 42, 1.71, -12.03, 0.06437, 1, 40, 11.61, 4.37, 1, 1, 40, 8.99, -3.97, 1, 1, 40, 5.48, -9.45, 1, 1, 36, -0.3, 0.42, 1, 1, 36, 7.62, -14.38, 1, 5, 36, 10.07, -24.19, 0.05115, 41, 1.04, -73.89, 0, 37, 3.03, -1.12, 0.62989, 38, -2.29, 10.03, 0.31881, 39, -4.43, 19.41, 0.00016, 4, 41, 3.61, -84.46, 0, 37, 5.6, -11.69, 0.09539, 38, 0.28, -0.54, 0.75863, 39, -1.87, 8.84, 0.14598, 4, 41, 5.98, -92.52, 0, 37, 7.96, -19.76, 0.00153, 38, 2.65, -8.6, 0.23654, 39, 0.5, 0.78, 0.76192, 1, 40, 1.7, 1.32, 1, 1, 40, 7.5, 7.9, 1, 4, 40, 11.81, 15.22, 0.00629, 41, 2.37, 3.81, 0.43789, 42, -1.04, -4.03, 0.51281, 43, -7.24, -10.05, 0.04301, 4, 40, 15.9, 21.22, 0.00011, 41, 6.46, 9.81, 0.03894, 42, 3.05, 1.97, 0.5936, 43, -3.15, -4.05, 0.36735, 3, 41, 10.31, 15.1, 9e-05, 42, 6.9, 7.26, 0.1186, 43, 0.7, 1.24, 0.88131, 4, 36, 8.95, -17.57, 0.20564, 37, 1.91, 5.5, 0.74055, 38, -3.41, 16.65, 0.05381, 39, -5.56, 26.03, 0], "hull": 40, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 0, 78], "width": 117, "height": 41}}, "shunyi": {"shunyi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [193.78, -212.96, -158.13, -212.96, -158.13, 244.29, 193.78, 244.29], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 82, "height": 136}}, "biyan": {"biyan": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [30.24, -11.55, 41.09, 53.55, 65.75, 49.44, 54.9, -15.66], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 66, "height": 25}}}}], "events": {"atk": {}}, "animations": {"boss_attack1": {"slots": {"zuoshou2": {"twoColor": [{"light": "ffcf0000", "dark": "5f4e00", "curve": "stepped"}, {"time": 0.3, "light": "ffcf0000", "dark": "5f4e00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "light": "ffcf00ff", "dark": "9f8200", "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "light": "ffcf0000", "dark": "5f4e00"}], "attachment": [{"name": "<PERSON><PERSON><PERSON>"}]}}, "bones": {"bone40": {"translate": [{"x": -2.18, "y": 2.28}]}, "bone41": {"translate": [{"x": -3.04, "y": 3.18}]}, "bone36": {"translate": [{"x": -2.61, "y": -2.54}]}, "bone37": {"translate": [{"x": -3.65, "y": -3.55}]}, "target1": {"translate": [{"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 61.73, "y": 31.28, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -3.45, "y": 19.83, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "bone": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.62, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.2667, "angle": -10.85, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.3667, "angle": 7.7, "curve": "stepped"}, {"time": 0.4333, "angle": 7.7, "curve": 0.25, "c3": 0.75}, {"time": 0.6}], "translate": [{"x": 3.19, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.1333, "x": 4.44, "y": -14.28, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 67.78, "y": 6.21, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -19.16, "y": -1.24, "curve": "stepped"}, {"time": 0.4333, "x": -19.16, "y": -1.24, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 3.19}], "scale": [{"time": 0.2667, "curve": 0.381, "c2": 0.59, "c3": 0.727}, {"time": 0.3667, "x": 1.162, "y": 1.162, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -12.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3, "angle": 40.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7}]}, "bone3": {"rotate": [{"angle": 1.06, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1333, "angle": -0.61, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -18.76, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 2.21, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.7, "angle": 1.06}], "translate": [{"x": 0.54, "y": 0.06}]}, "bone4": {"rotate": [{"angle": -1.55, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -10.4, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 22.55, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -1.55}], "translate": [{"x": 2.41, "y": 0.24}]}, "bone5": {"translate": [{"x": 2.98, "y": -0.67}]}, "bone6": {"translate": [{"x": 2.62, "y": 2.93}]}, "bone8": {"translate": [{"x": 0.48, "y": 11.27, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 12.25, "y": -15.77, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 1.17, "y": 27.78, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.6, "x": 0.48, "y": 11.27}]}, "bone9": {"rotate": [{"angle": 1.9, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.2667, "angle": 116.08, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 79.31, "curve": "stepped"}, {"time": 0.3667, "angle": 79.31, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -103.35, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.6, "angle": 1.9}], "translate": [{"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -32.14, "y": 21.92, "curve": "stepped"}, {"time": 0.3667, "x": -32.14, "y": 21.92, "curve": 0.25, "c3": 0.75}, {"time": 0.6}], "scale": [{"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -1.271, "y": 1.271, "curve": "stepped"}, {"time": 0.3667, "x": -1.271, "y": 1.271, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "bone10": {"rotate": [{"angle": 4.18, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2667, "angle": 40.06, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3, "angle": 46.95, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3667, "angle": 42.05, "curve": "stepped"}, {"time": 0.4333, "angle": 42.05, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 4.18}], "scale": [{"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 1.16, "y": 1.143, "curve": "stepped"}, {"time": 0.4333, "x": 1.16, "y": 1.143, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone11": {"rotate": [{"angle": 5.84, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 22.03, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 37.2, "curve": "stepped"}, {"time": 0.4333, "angle": 37.2, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 5.84}], "scale": [{"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 1.226, "y": 1.108, "curve": "stepped"}, {"time": 0.4333, "x": 1.226, "y": 1.108, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone12": {"rotate": [{"angle": 23.42, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -12.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 51.05, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 23.42}]}, "bone13": {"rotate": [{"angle": -45.47, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -3.98, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.7, "angle": -45.47}]}, "bone14": {"rotate": [{"angle": -5.56}]}, "bone16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 28.12, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -20.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 25.28, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone18": {"rotate": [{"angle": 20.61, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -20.2, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 25.28, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.7, "angle": 20.61}]}, "bone31": {"translate": [{"x": -1.81, "y": 0.97}]}, "bone35": {"translate": [{"x": -1.03, "y": -1.01}]}, "bone21": {"rotate": [{"angle": 34.66, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 61.26, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -27.19, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 34.66}]}, "bone22": {"rotate": [{"angle": 24.82, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 51.43, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -42.25, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 24.82}]}, "bone24": {"rotate": [{"angle": -5.76, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 24.41, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -37.36, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 0.7, "angle": -5.76}]}, "bone25": {"rotate": [{"angle": -33.45, "curve": 0.293, "c2": 0.18, "c3": 0.755}, {"time": 0.2667, "angle": 4.01, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.5, "angle": 28.42, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -35.48, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 0.7, "angle": -33.45}]}, "bone26": {"rotate": [{"angle": -6.63, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -19.16, "curve": "stepped"}, {"time": 0.4333, "angle": -19.16, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -6.63}]}, "bone39": {"translate": [{"x": -0.86, "y": 0.9}]}, "bone27": {"rotate": [{"angle": 4.36, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 44.82, "curve": "stepped"}, {"time": 0.4333, "angle": 44.82, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 4.36}]}, "bone28": {"rotate": [{"angle": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 0.64, "curve": "stepped"}, {"time": 0.4333, "angle": 0.64, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -0.94}]}, "bone29": {"rotate": [{"angle": 1.87}]}, "bone23": {"rotate": [{"angle": -73.61, "curve": "stepped"}, {"time": 0.3, "angle": -73.61, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -61.22}], "translate": [{"x": -344.76, "y": 140.61, "curve": "stepped"}, {"time": 0.3, "x": -344.76, "y": 140.61, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -417.77, "y": 109.51, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -418.37, "y": 89.91}], "scale": [{"x": -1, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -0.827, "y": 0.827, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -2.359, "y": 2.359}]}}, "drawOrder": [{"offsets": [{"slot": "<PERSON><PERSON><PERSON>", "offset": 6}]}], "events": [{"time": 0.5, "name": "atk"}]}, "boss_attack2_1": {"slots": {"jiasha2": {"attachment": [{"time": 0.2333, "name": null}]}, "eye": {"color": [{"color": "ffffff00"}], "attachment": [{"time": 0.2667, "name": null}]}, "zuoshou": {"attachment": [{"time": 0.2333, "name": null}]}, "xiongtou": {"attachment": [{"time": 0.2333, "name": null}]}, "youjiao": {"attachment": [{"time": 0.2333, "name": null}]}, "youshou": {"attachment": [{"time": 0.2333, "name": null}]}, "jiasha3": {"attachment": [{"time": 0.2333, "name": null}]}, "zuojiao": {"attachment": [{"time": 0.2333, "name": null}]}, "biyan": {"attachment": [{"time": 0.2333, "name": null}]}, "shunyi": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "color": "ffffff00"}]}, "fozhu": {"attachment": [{"time": 0.2333, "name": null}]}, "jiasha1": {"attachment": [{"time": 0.2333, "name": null}]}, "jiasha4": {"attachment": [{"time": 0.2333, "name": null}]}, "body": {"attachment": [{"time": 0.2333, "name": null}]}, "fangun": {"color": [{"time": 0.3333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "color": "ffffff00"}], "attachment": [{"time": 0.2333, "name": "fangun"}]}}, "bones": {"target1": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 2.91, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 2.91, "y": 80.03}]}, "bone25": {"rotate": [{"angle": 4.01}]}, "bone10": {"rotate": [{"angle": 4.18}]}, "target2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 17.94, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 17.94, "y": 80.03}]}, "bone31": {"translate": [{"x": -1.81, "y": 0.97}]}, "bone21": {"rotate": [{"angle": 9.84}]}, "bone14": {"rotate": [{"angle": -5.56}]}, "bone4": {"rotate": [{"angle": -1.55}], "translate": [{"x": 2.41, "y": 0.24}]}, "bone3": {"rotate": [{"angle": -0.61}], "translate": [{"x": 0.54, "y": 0.06}]}, "bone28": {"rotate": [{"angle": 0.8}]}, "bone22": {"rotate": [{"angle": 24.82}]}, "bone11": {"rotate": [{"angle": 5.84}]}, "bone26": {"rotate": [{"angle": -4.23}]}, "bone12": {"rotate": [{"angle": -1.81}]}, "bone9": {"rotate": [{"angle": 1.9}]}, "bone29": {"rotate": [{"angle": 1.87}]}, "bone27": {"rotate": [{"angle": 4.36}]}, "bone6": {"translate": [{"x": 2.62, "y": 2.93}]}, "bone13": {"rotate": [{"angle": -3.98}]}, "bone5": {"translate": [{"x": 2.98, "y": -0.67}]}, "bone36": {"translate": [{"x": -2.61, "y": -2.54}]}, "bone39": {"translate": [{"x": -0.86, "y": 0.9}]}, "bone40": {"translate": [{"x": -2.18, "y": 2.28}]}, "bone35": {"translate": [{"x": -1.03, "y": -1.01}]}, "bone37": {"translate": [{"x": -3.65, "y": -3.55}]}, "bone41": {"translate": [{"x": -3.04, "y": 3.18}]}, "bone": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 9.71}], "translate": [{"x": 3.19, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.2, "x": 3.19, "y": -27.84, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 3.19, "y": 82.82}]}, "bone33": {"translate": [{"x": -448.16, "y": -354.3, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.1667, "x": -448.16, "y": -659.68, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.3667, "x": -448.16, "y": -256.47}]}, "bone32": {"rotate": [{"time": 0.2333}, {"time": 0.3333, "angle": 163.52}, {"time": 0.4, "angle": -123.04, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 44.04}], "translate": [{"x": -453.3, "y": -90.66, "curve": "stepped"}, {"time": 0.2333, "x": -453.3, "y": -90.66, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.3333, "x": -453.3, "y": -30.6, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.4667, "x": -799.32, "y": -143.55}]}}, "events": [{"time": 0.4667, "name": "atk"}]}, "boss_attack2_2": {"slots": {"jiasha2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.2, "color": "ffffffff"}]}, "eye": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.2, "color": "ffffffff"}]}, "zuoshou": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.2, "color": "ffffffff"}]}, "xiongtou": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.2, "color": "ffffffff"}]}, "youjiao": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.2, "color": "ffffffff"}]}, "youshou": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.2, "color": "ffffffff"}]}, "jiasha3": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.2, "color": "ffffffff"}]}, "zuojiao": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.2, "color": "ffffffff"}]}, "biyan": {"attachment": [{"name": null}]}, "shunyi": {"color": [{"time": 0.1333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.2, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "color": "ffffff00"}]}, "fozhu": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.2, "color": "ffffffff"}]}, "jiasha1": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.2, "color": "ffffffff"}]}, "jiasha4": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.2, "color": "ffffffff"}]}, "body": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.2, "color": "ffffffff"}]}, "fangun": {"color": [{"time": 0.0333, "color": "ffffff00", "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 0.1, "color": "fffffff0", "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1667, "color": "ffffffff"}], "attachment": [{"time": 0.0333, "name": "fangun"}, {"time": 0.2, "name": null}]}}, "bones": {"target1": {"translate": [{"x": 2.91, "y": 80.03, "curve": 0.25, "c3": 0.75}, {"time": 0.2667}]}, "target2": {"translate": [{"x": 17.94, "y": 80.03, "curve": 0.25, "c3": 0.75}, {"time": 0.2667}]}, "bone31": {"translate": [{"time": 0.2667, "x": -1.81, "y": 0.97}]}, "bone": {"rotate": [{"time": 0.2667, "angle": 9.71, "curve": 0.25, "c3": 0.75}, {"time": 0.3}], "translate": [{"time": 0.2, "x": 3.19, "y": 82.82, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 3.19, "y": -27.84, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 3.19}]}, "bone33": {"translate": [{"time": 0.1333, "x": -448.16, "y": -256.47, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -448.16, "y": -484.16}]}, "bone32": {"rotate": [{"angle": 44.04, "curve": "stepped"}, {"time": 0.0333, "angle": -42.88, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 141.61}], "translate": [{"time": 0.0333, "x": -799.32, "y": -143.55, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -453.3, "y": -30.6}]}}}, "boss_idle": {"slots": {"eye": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.9, "color": "ffffffff", "curve": "stepped"}, {"time": 1.0333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffff00"}]}}, "bones": {"bone": {"translate": [{"x": 3.19, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": 7, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "x": 3.19}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -2.16, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone3": {"rotate": [{"angle": -0.61, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.16, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -0.61}], "translate": [{"x": 0.54, "y": 0.06, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.92, "y": 0.21, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.54, "y": 0.06}]}, "bone4": {"rotate": [{"angle": -1.55, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -2.16, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -1.55}], "translate": [{"x": 2.41, "y": 0.24, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 3.36, "y": 0.34, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 2.41, "y": 0.24}]}, "bone5": {"translate": [{"x": 2.98, "y": -0.67, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 5.96, "y": -1.35, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 2.98, "y": -0.67}]}, "bone6": {"translate": [{"x": 2.62, "y": 2.93, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 5.24, "y": 5.86, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 2.62, "y": 2.93}]}, "bone8": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": -1.16, "y": 11.55, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone9": {"rotate": [{"angle": 1.9, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": 5.84, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 2, "angle": 1.9}]}, "bone10": {"rotate": [{"angle": 4.18, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 5.84, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 4.18}]}, "bone11": {"rotate": [{"angle": 5.84, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 5.84}]}, "bone12": {"rotate": [{"angle": -1.81, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": -5.56, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 2, "angle": -1.81}]}, "bone13": {"rotate": [{"angle": -3.98, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -5.56, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -3.98}]}, "bone14": {"rotate": [{"angle": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -5.56}]}, "bone21": {"rotate": [{"angle": 34.66, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "angle": 9.84, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 34.66}]}, "bone22": {"rotate": [{"angle": 24.82, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 34.66, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "angle": 24.82, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 24.82}]}, "bone24": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 14.13, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone25": {"rotate": [{"angle": 4.01, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 14.13, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 4.01}]}, "bone26": {"rotate": [{"angle": -4.23}]}, "bone27": {"rotate": [{"angle": 4.36}]}, "bone28": {"rotate": [{"angle": 0.8}]}, "bone29": {"rotate": [{"angle": 1.87}]}, "bone31": {"translate": [{"x": -1.81, "y": 0.97, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.8, "x": -13.89, "y": 7.42, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "x": -1.81, "y": 0.97}]}, "bone39": {"translate": [{"x": -0.86, "y": 0.9, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -3.04, "y": 3.18, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -0.86, "y": 0.9}]}, "bone40": {"translate": [{"x": -2.18, "y": 2.28, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -3.04, "y": 3.18, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -2.18, "y": 2.28}]}, "bone41": {"translate": [{"x": -3.04, "y": 3.18, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -3.04, "y": 3.18}]}, "bone35": {"translate": [{"x": -1.03, "y": -1.01, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -3.65, "y": -3.55, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -1.03, "y": -1.01}]}, "bone36": {"translate": [{"x": -2.61, "y": -2.54, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -3.65, "y": -3.55, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -2.61, "y": -2.54}]}, "bone37": {"translate": [{"x": -3.65, "y": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -3.65, "y": -3.55}]}}}, "die": {"slots": {"eye": {"color": [{"time": 0.2667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "color": "ffffff00"}]}}, "bones": {"bone4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -13.19}]}, "bone6": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -2.58, "y": -18.3}]}, "bone8": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 1.25, "y": -20.43}]}, "bone9": {"rotate": [{"curve": 0.259, "c3": 0.618, "c4": 0.45}, {"time": 0.1667, "angle": -60.87, "curve": 0.36, "c2": 0.43, "c3": 0.755}, {"time": 0.4333, "angle": -35.41}]}, "bone10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 24.74}]}, "bone11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 22.58}]}, "bone12": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -36.53, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -15.24}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -1.31, "y": -41.69}]}, "bone13": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 40.52}]}, "bone15": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 29.28, "y": -2.49}]}, "bone17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -27.43}]}, "bone18": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -27.43, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -3.28}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 22.39, "y": -3.11, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 22.77, "y": 8.64}]}, "bone26": {"rotate": [{"angle": -4.23}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 26.45, "y": -2.25}]}, "bone27": {"rotate": [{"angle": 4.36}]}, "bone28": {"rotate": [{"angle": 0.8}]}, "bone29": {"rotate": [{"angle": 1.87}]}, "bone31": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 4.86, "y": -36.42}]}, "target1": {"translate": [{"curve": 0.259, "c3": 0.618, "c4": 0.45}, {"time": 0.1667, "x": -37.91, "y": 11, "curve": 0.36, "c2": 0.43, "c3": 0.755}, {"time": 0.4333, "x": 26.5, "y": -1.48}]}, "target2": {"translate": [{"curve": 0.259, "c3": 0.618, "c4": 0.45}, {"time": 0.1667, "x": -83.49, "y": -10.74, "curve": 0.36, "c2": 0.43, "c3": 0.755}, {"time": 0.4333, "x": -26.64, "y": -9.43}]}, "target3": {"translate": [{"curve": 0.259, "c3": 0.618, "c4": 0.45}, {"time": 0.1667, "x": -37.91, "y": 11, "curve": 0.36, "c2": 0.43, "c3": 0.755}, {"time": 0.4333, "x": 26.5, "y": -1.48}]}, "target4": {"translate": [{"curve": 0.259, "c3": 0.618, "c4": 0.45}, {"time": 0.1667, "x": -83.49, "y": -10.74, "curve": 0.36, "c2": 0.43, "c3": 0.755}, {"time": 0.4333, "x": -26.64, "y": -9.43}]}, "all": {"rotate": [{"curve": 0.279, "c3": 0.622, "c4": 0.39}, {"time": 0.1, "angle": -62.05, "curve": "stepped"}, {"time": 0.1667, "angle": -62.05, "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 0.4333, "angle": -86.8}], "translate": [{"curve": 0.279, "c3": 0.622, "c4": 0.39}, {"time": 0.1, "x": -60.82, "y": 145.19, "curve": "stepped"}, {"time": 0.1667, "x": -60.82, "y": 145.19, "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 0.4333, "x": -77.17, "y": 30.74}]}}, "deform": {"default": {"jiasha1": {"jiasha1": [{"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "vertices": [32.9656, 3.28765, 15.28915, 0.63745, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.32716, 1.0544, -0.13223, 1.09472, 9.72332, 44.37574, 45.42855, 0.01075, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.48057, 13.08006, 13.522, -0.59711, 13.52205, -0.59711, -2.12717, 13.33916]}]}, "body": {"body": [{"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "offset": 60, "vertices": [25.22432, -6.30226, -0.09846, -19.46203, -1.02657, -19.43526, -1.02657, -19.43532, -0.09846, -19.46211, -5.02554, -22.02626, -6.07032, -21.76153, -6.07033, -21.76162, -5.02553, -22.02631, 0.74954, -20.69049, -0.23811, -20.70271, -0.23812, -20.70279, 0.42282, -26.07811, -0.8214, -26.06891, -0.82142, -26.06928, -1.04714, -12.02452, -1.61945, -11.96091, -1.61944, -11.96097]}]}}}}, "hurt": {"slots": {"eye": {"color": [{"time": 0.0667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "color": "00000000"}], "attachment": [{"name": null}]}, "biyan": {"attachment": [{"name": null}, {"time": 0.0667, "name": "biyan"}]}}, "bones": {"bone": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -23.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 9.23, "y": -18.47, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone2": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -11.06, "curve": 0.25, "c3": 0.75}, {"time": 0.2667}]}, "bone3": {"rotate": [{"angle": -4.07, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -24.48, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -4.07}]}, "bone4": {"rotate": [{"angle": -9.63, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -27.58, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -9.63}]}, "bone8": {"rotate": [{"angle": 9.18, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 24.95, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.18}], "translate": [{"x": 10.1, "y": -27.71, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 27.45, "y": -75.34, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 10.1, "y": -27.71}]}, "bone12": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -18.17, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone13": {"rotate": [{"angle": -6.68, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -18.17, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.68}]}, "bone14": {"rotate": [{"angle": -15.8, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -18.17, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -15.8}]}, "bone21": {"rotate": [{"angle": -13.23, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -35.96, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": -13.23}]}, "bone22": {"rotate": [{"angle": -31.3, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.0667, "angle": -13.23, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -35.96, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": -31.3}]}, "bone24": {"rotate": [{"angle": -4.68, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -35.96, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": -4.68}]}, "bone25": {"rotate": [{"angle": -22.7, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.0333, "angle": -13.23, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -35.96, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "angle": -22.7}]}, "bone26": {"rotate": [{"angle": -4.23}]}, "bone27": {"rotate": [{"angle": 4.36}]}, "bone28": {"rotate": [{"angle": 0.8}]}, "bone29": {"rotate": [{"angle": 1.87}]}, "bone9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -23.28, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone10": {"rotate": [{"angle": -8.56, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -23.28, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -8.56}]}, "bone11": {"rotate": [{"angle": -20.25, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -23.28, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -20.25}]}}}}}