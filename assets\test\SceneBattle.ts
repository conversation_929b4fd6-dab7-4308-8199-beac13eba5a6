import { _decorator, instantiate } from "cc";
import { BaseCtrl } from "../platform/src/core/BaseCtrl";
import { RouteCtrl } from "../platform/src/core/RouteCtrl";
import { BundleEnum } from "../platform/src/ResHelper";
const { ccclass, property } = _decorator;

@ccclass("SceneBattle")
export class SceneBattle extends BaseCtrl {
  routeCtrl: RouteCtrl;

  battleData = {
    a: { a: 100, b: 1001, c: 10, d: [10000, 5000, 2500, 1250, 625, 313, 156, 78, 39, 20], e: 23000, f: 17000, g: 3000 },
    b: [
      [
        {
          a: 1,
          b: 1009,
          c: "1009_100",
          d: 2058,
          e: 100,
          f: {
            32: 214,
            1: 1815,
            33: 202,
            2: 535,
            34: 186,
            3: 218,
            35: 208,
            4: 216,
            36: 190,
            37: 92,
            21: 206,
            22: 10184,
            23: 208,
            24: 194,
            25: 200,
            26: 192,
            27: 93,
            31: 184,
          },
          g: [],
          h: [],
          i: [],
        },
      ],
      [
        {
          a: 2,
          b: 1669,
          c: "1669_100",
          d: 2699,
          e: 100,
          f: {
            32: 188,
            1: 11485,
            33: 186,
            2: 545,
            34: 208,
            3: 212,
            35: 202,
            4: 216,
            36: 188,
            37: 94,
            21: 214,
            22: 212,
            23: 210,
            24: 210,
            25: 180,
            26: 192,
            27: 105,
            31: 192,
          },
          g: [],
          h: [],
          i: [],
        },
      ],
    ],
    c: [],
    d: [
      {
        a: 0,
        b: [
          { a: 11, b: 1, c: 21, d: [{ a: 21, b: { 101: [-323, 1] }, c: [], d: [], e: [] }], e: [] },
          { a: 21, b: 1, c: 11, d: [], e: [] },
        ],
        c: [
          [
            {
              a: 1,
              b: 1009,
              c: "1009_100",
              d: 2058,
              e: 100,
              f: {
                32: 214,
                1: 1815,
                33: 202,
                2: 535,
                34: 186,
                3: 218,
                35: 208,
                4: 216,
                36: 190,
                37: 92,
                21: 206,
                22: 10184,
                23: 208,
                24: 194,
                25: 200,
                26: 192,
                27: 93,
                31: 184,
                101: 1815,
                102: 0,
                103: 0,
                104: 0,
                105: 0,
              },
              g: [],
              h: [],
              i: [],
            },
          ],
          [
            {
              a: 2,
              b: 1669,
              c: "1669_100",
              d: 2699,
              e: 100,
              f: {
                32: 188,
                1: 11485,
                33: 186,
                2: 545,
                34: 208,
                3: 212,
                35: 202,
                4: 216,
                36: 188,
                37: 94,
                21: 214,
                22: 212,
                23: 210,
                24: 210,
                25: 180,
                26: 192,
                27: 105,
                31: 192,
                101: 11162,
                102: 0,
                103: 0,
                104: 0,
                105: 0,
              },
              g: [],
              h: [],
              i: [],
            },
          ],
        ],
      },
      {
        a: 1,
        b: [
          { a: 11, b: 1, c: 21, d: [{ a: 21, b: { 101: [-323, 1] }, c: [], d: [], e: [] }], e: [] },
          { a: 21, b: 1, c: 11, d: [], e: [] },
        ],
        c: [
          [
            {
              a: 1,
              b: 1009,
              c: "1009_100",
              d: 2058,
              e: 100,
              f: {
                32: 214,
                1: 1815,
                33: 202,
                2: 535,
                34: 186,
                3: 218,
                35: 208,
                4: 216,
                36: 190,
                37: 92,
                21: 206,
                22: 10184,
                23: 208,
                24: 194,
                25: 200,
                26: 192,
                27: 93,
                31: 184,
                101: 1815,
                102: 0,
                103: 0,
                104: 0,
                105: 0,
              },
              g: [],
              h: [],
              i: [],
            },
          ],
          [
            {
              a: 2,
              b: 1669,
              c: "1669_100",
              d: 2699,
              e: 100,
              f: {
                32: 188,
                1: 11485,
                33: 186,
                2: 545,
                34: 208,
                3: 212,
                35: 202,
                4: 216,
                36: 188,
                37: 94,
                21: 214,
                22: 212,
                23: 210,
                24: 210,
                25: 180,
                26: 192,
                27: 105,
                31: 192,
                101: 10839,
                102: 0,
                103: 0,
                104: 0,
                105: 0,
              },
              g: [],
              h: [],
              i: [],
            },
          ],
        ],
      },
      {
        a: 2,
        b: [
          { a: 11, b: 1, c: 21, d: [{ a: 21, b: { 101: [-323, 1] }, c: [], d: [], e: [] }], e: [] },
          { a: 21, b: 1, c: 11, d: [], e: [] },
        ],
        c: [
          [
            {
              a: 1,
              b: 1009,
              c: "1009_100",
              d: 2058,
              e: 100,
              f: {
                32: 214,
                1: 1815,
                33: 202,
                2: 535,
                34: 186,
                3: 218,
                35: 208,
                4: 216,
                36: 190,
                37: 92,
                21: 206,
                22: 10184,
                23: 208,
                24: 194,
                25: 200,
                26: 192,
                27: 93,
                31: 184,
                101: 1815,
                102: 0,
                103: 0,
                104: 0,
                105: 0,
              },
              g: [],
              h: [],
              i: [],
            },
          ],
          [
            {
              a: 2,
              b: 1669,
              c: "1669_100",
              d: 2699,
              e: 100,
              f: {
                32: 188,
                1: 11485,
                33: 186,
                2: 545,
                34: 208,
                3: 212,
                35: 202,
                4: 216,
                36: 188,
                37: 94,
                21: 214,
                22: 212,
                23: 210,
                24: 210,
                25: 180,
                26: 192,
                27: 105,
                31: 192,
                101: 10516,
                102: 0,
                103: 0,
                104: 0,
                105: 0,
              },
              g: [],
              h: [],
              i: [],
            },
          ],
        ],
      },
      {
        a: 3,
        b: [
          { a: 11, b: 1, c: 21, d: [{ a: 21, b: { 101: [-323, 1] }, c: [], d: [], e: [] }], e: [] },
          { a: 21, b: 1, c: 11, d: [], e: [] },
        ],
        c: [
          [
            {
              a: 1,
              b: 1009,
              c: "1009_100",
              d: 2058,
              e: 100,
              f: {
                32: 214,
                1: 1815,
                33: 202,
                2: 535,
                34: 186,
                3: 218,
                35: 208,
                4: 216,
                36: 190,
                37: 92,
                21: 206,
                22: 10184,
                23: 208,
                24: 194,
                25: 200,
                26: 192,
                27: 93,
                31: 184,
                101: 1815,
                102: 0,
                103: 0,
                104: 0,
                105: 0,
              },
              g: [],
              h: [],
              i: [],
            },
          ],
          [
            {
              a: 2,
              b: 1669,
              c: "1669_100",
              d: 2699,
              e: 100,
              f: {
                32: 188,
                1: 11485,
                33: 186,
                2: 545,
                34: 208,
                3: 212,
                35: 202,
                4: 216,
                36: 188,
                37: 94,
                21: 214,
                22: 212,
                23: 210,
                24: 210,
                25: 180,
                26: 192,
                27: 105,
                31: 192,
                101: 10193,
                102: 0,
                103: 0,
                104: 0,
                105: 0,
              },
              g: [],
              h: [],
              i: [],
            },
          ],
        ],
      },
      {
        a: 4,
        b: [
          { a: 11, b: 1, c: 21, d: [{ a: 21, b: { 101: [-323, 1] }, c: [], d: [], e: [] }], e: [] },
          { a: 21, b: 1, c: 11, d: [], e: [] },
        ],
        c: [
          [
            {
              a: 1,
              b: 1009,
              c: "1009_100",
              d: 2058,
              e: 100,
              f: {
                32: 214,
                1: 1815,
                33: 202,
                2: 535,
                34: 186,
                3: 218,
                35: 208,
                4: 216,
                36: 190,
                37: 92,
                21: 206,
                22: 10184,
                23: 208,
                24: 194,
                25: 200,
                26: 192,
                27: 93,
                31: 184,
                101: 1815,
                102: 0,
                103: 0,
                104: 0,
                105: 0,
              },
              g: [],
              h: [],
              i: [],
            },
          ],
          [
            {
              a: 2,
              b: 1669,
              c: "1669_100",
              d: 2699,
              e: 100,
              f: {
                32: 188,
                1: 11485,
                33: 186,
                2: 545,
                34: 208,
                3: 212,
                35: 202,
                4: 216,
                36: 188,
                37: 94,
                21: 214,
                22: 212,
                23: 210,
                24: 210,
                25: 180,
                26: 192,
                27: 105,
                31: 192,
                101: 9870,
                102: 0,
                103: 0,
                104: 0,
                105: 0,
              },
              g: [],
              h: [],
              i: [],
            },
          ],
        ],
      },
      {
        a: 5,
        b: [
          { a: 11, b: 1, c: 21, d: [{ a: 21, b: { 101: [-323, 1] }, c: [], d: [], e: [] }], e: [] },
          { a: 21, b: 1, c: 11, d: [], e: [] },
        ],
        c: [
          [
            {
              a: 1,
              b: 1009,
              c: "1009_100",
              d: 2058,
              e: 100,
              f: {
                32: 214,
                1: 1815,
                33: 202,
                2: 535,
                34: 186,
                3: 218,
                35: 208,
                4: 216,
                36: 190,
                37: 92,
                21: 206,
                22: 10184,
                23: 208,
                24: 194,
                25: 200,
                26: 192,
                27: 93,
                31: 184,
                101: 1815,
                102: 0,
                103: 0,
                104: 0,
                105: 0,
              },
              g: [],
              h: [],
              i: [],
            },
          ],
          [
            {
              a: 2,
              b: 1669,
              c: "1669_100",
              d: 2699,
              e: 100,
              f: {
                32: 188,
                1: 11485,
                33: 186,
                2: 545,
                34: 208,
                3: 212,
                35: 202,
                4: 216,
                36: 188,
                37: 94,
                21: 214,
                22: 212,
                23: 210,
                24: 210,
                25: 180,
                26: 192,
                27: 105,
                31: 192,
                101: 9547,
                102: 0,
                103: 0,
                104: 0,
                105: 0,
              },
              g: [],
              h: [],
              i: [],
            },
          ],
        ],
      },
      {
        a: 6,
        b: [
          { a: 11, b: 1, c: 21, d: [{ a: 21, b: { 101: [-323, 1] }, c: [], d: [], e: [] }], e: [] },
          { a: 21, b: 1, c: 11, d: [], e: [] },
        ],
        c: [
          [
            {
              a: 1,
              b: 1009,
              c: "1009_100",
              d: 2058,
              e: 100,
              f: {
                32: 214,
                1: 1815,
                33: 202,
                2: 535,
                34: 186,
                3: 218,
                35: 208,
                4: 216,
                36: 190,
                37: 92,
                21: 206,
                22: 10184,
                23: 208,
                24: 194,
                25: 200,
                26: 192,
                27: 93,
                31: 184,
                101: 1815,
                102: 0,
                103: 0,
                104: 0,
                105: 0,
              },
              g: [],
              h: [],
              i: [],
            },
          ],
          [
            {
              a: 2,
              b: 1669,
              c: "1669_100",
              d: 2699,
              e: 100,
              f: {
                32: 188,
                1: 11485,
                33: 186,
                2: 545,
                34: 208,
                3: 212,
                35: 202,
                4: 216,
                36: 188,
                37: 94,
                21: 214,
                22: 212,
                23: 210,
                24: 210,
                25: 180,
                26: 192,
                27: 105,
                31: 192,
                101: 9224,
                102: 0,
                103: 0,
                104: 0,
                105: 0,
              },
              g: [],
              h: [],
              i: [],
            },
          ],
        ],
      },
      {
        a: 7,
        b: [
          { a: 11, b: 1, c: 21, d: [{ a: 21, b: { 101: [-323, 1] }, c: [], d: [], e: [] }], e: [] },
          { a: 21, b: 1, c: 11, d: [], e: [] },
        ],
        c: [
          [
            {
              a: 1,
              b: 1009,
              c: "1009_100",
              d: 2058,
              e: 100,
              f: {
                32: 214,
                1: 1815,
                33: 202,
                2: 535,
                34: 186,
                3: 218,
                35: 208,
                4: 216,
                36: 190,
                37: 92,
                21: 206,
                22: 10184,
                23: 208,
                24: 194,
                25: 200,
                26: 192,
                27: 93,
                31: 184,
                101: 1815,
                102: 0,
                103: 0,
                104: 0,
                105: 0,
              },
              g: [],
              h: [],
              i: [],
            },
          ],
          [
            {
              a: 2,
              b: 1669,
              c: "1669_100",
              d: 2699,
              e: 100,
              f: {
                32: 188,
                1: 11485,
                33: 186,
                2: 545,
                34: 208,
                3: 212,
                35: 202,
                4: 216,
                36: 188,
                37: 94,
                21: 214,
                22: 212,
                23: 210,
                24: 210,
                25: 180,
                26: 192,
                27: 105,
                31: 192,
                101: 8901,
                102: 0,
                103: 0,
                104: 0,
                105: 0,
              },
              g: [],
              h: [],
              i: [],
            },
          ],
        ],
      },
      {
        a: 8,
        b: [
          { a: 11, b: 1, c: 21, d: [{ a: 21, b: { 101: [-323, 1] }, c: [], d: [], e: [] }], e: [] },
          { a: 21, b: 1, c: 11, d: [], e: [] },
        ],
        c: [
          [
            {
              a: 1,
              b: 1009,
              c: "1009_100",
              d: 2058,
              e: 100,
              f: {
                32: 214,
                1: 1815,
                33: 202,
                2: 535,
                34: 186,
                3: 218,
                35: 208,
                4: 216,
                36: 190,
                37: 92,
                21: 206,
                22: 10184,
                23: 208,
                24: 194,
                25: 200,
                26: 192,
                27: 93,
                31: 184,
                101: 1815,
                102: 0,
                103: 0,
                104: 0,
                105: 0,
              },
              g: [],
              h: [],
              i: [],
            },
          ],
          [
            {
              a: 2,
              b: 1669,
              c: "1669_100",
              d: 2699,
              e: 100,
              f: {
                32: 188,
                1: 11485,
                33: 186,
                2: 545,
                34: 208,
                3: 212,
                35: 202,
                4: 216,
                36: 188,
                37: 94,
                21: 214,
                22: 212,
                23: 210,
                24: 210,
                25: 180,
                26: 192,
                27: 105,
                31: 192,
                101: 8578,
                102: 0,
                103: 0,
                104: 0,
                105: 0,
              },
              g: [],
              h: [],
              i: [],
            },
          ],
        ],
      },
      {
        a: 9,
        b: [
          { a: 11, b: 1, c: 21, d: [{ a: 21, b: { 101: [-323, 1] }, c: [], d: [], e: [] }], e: [] },
          { a: 21, b: 1, c: 11, d: [], e: [] },
        ],
        c: [
          [
            {
              a: 1,
              b: 1009,
              c: "1009_100",
              d: 2058,
              e: 100,
              f: {
                32: 214,
                1: 1815,
                33: 202,
                2: 535,
                34: 186,
                3: 218,
                35: 208,
                4: 216,
                36: 190,
                37: 92,
                21: 206,
                22: 10184,
                23: 208,
                24: 194,
                25: 200,
                26: 192,
                27: 93,
                31: 184,
                101: 1815,
                102: 0,
                103: 0,
                104: 0,
                105: 0,
              },
              g: [],
              h: [],
              i: [],
            },
          ],
          [
            {
              a: 2,
              b: 1669,
              c: "1669_100",
              d: 2699,
              e: 100,
              f: {
                32: 188,
                1: 11485,
                33: 186,
                2: 545,
                34: 208,
                3: 212,
                35: 202,
                4: 216,
                36: 188,
                37: 94,
                21: 214,
                22: 212,
                23: 210,
                24: 210,
                25: 180,
                26: 192,
                27: 105,
                31: 192,
                101: 8255,
                102: 0,
                103: 0,
                104: 0,
                105: 0,
              },
              g: [],
              h: [],
              i: [],
            },
          ],
        ],
      },
    ],
    e: [
      [
        {
          a: 1,
          b: 1009,
          c: "1009_100",
          d: 2058,
          e: 100,
          f: {
            32: 214,
            1: 1815,
            33: 202,
            2: 535,
            34: 186,
            3: 218,
            35: 208,
            4: 216,
            36: 190,
            37: 92,
            21: 206,
            22: 10184,
            23: 208,
            24: 194,
            25: 200,
            26: 192,
            27: 93,
            31: 184,
            101: 1815,
            102: 0,
            103: 0,
            104: 0,
            105: 0,
          },
          g: [],
          h: [],
          i: [],
        },
      ],
      [
        {
          a: 2,
          b: 1669,
          c: "1669_100",
          d: 2699,
          e: 100,
          f: {
            32: 188,
            1: 11485,
            33: 186,
            2: 545,
            34: 208,
            3: 212,
            35: 202,
            4: 216,
            36: 188,
            37: 94,
            21: 214,
            22: 212,
            23: 210,
            24: 210,
            25: 180,
            26: 192,
            27: 105,
            31: 192,
            101: 8255,
            102: 0,
            103: 0,
            104: 0,
            105: 0,
          },
          g: [],
          h: [],
          i: [],
        },
      ],
    ],
  };

  async start() {
    // 加载配置

    let pb = await this.assetMgr.loadPrefabSync(BundleEnum.BUNDLE_G_FIGHT, "prefab/fight/FIghtIndex");
    let nodeBattle = instantiate(pb);

    this.routeCtrl = this.getNode("root").getComponent(RouteCtrl);
    this.routeCtrl.showNode(nodeBattle, { battleData: this.battleData });
  }
}
