import { _decorator, find, Input, instantiate, Label, Node } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { HeroModule } from "../../../module/hero/HeroModule";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { HeroRouteItem } from "../../../module/hero/HeroRoute";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import { ItemCtrl } from "../../common/ItemCtrl";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { IConfigHero, IConfigHeroBreak } from "../../JsonDefine";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Thu May 23 2024 20:46:13 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/hero/UIHeroBreak.ts
 *
 */

@ccclass("UIHeroBreak")
export class UIHeroBreak extends UINode {
  protected _openAct: boolean = true;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_HERO}?prefab/ui/UIHeroBreak`;
  }
  //=================================================

  // popup/btnBreak
  btnBreak: Node;
  // popup/close
  // btnClose: Node;

  // popup/nodeProp
  layoutNodeProp: Node;

  // itemProp
  itemProP: Node;
  // itemProp/txt
  txtProp: Node;

  // popup/breakProfit/profit1/original
  levelMaxBefore: Node;
  // popup/breakProfit/profit1/after
  levelMaxAfter: Node;
  // popup/breakProfit/profit2/original
  qualityAddBefore: Node;
  // popup/breakProfit/profit2/after
  qualityAddAfter: Node;
  //data
  hero: IConfigHero;
  heroBreak: IConfigHeroBreak;

  public init(args: any): void {
    super.init(args);
    this.hero = args.heroInfo;
    let heroMessage = HeroModule.data.getHeroMessage(this.hero.id);
    this.heroBreak = HeroModule.config.getBreakInfo(heroMessage.breakTopLevel + 1);
  }
  protected onEvtShow(): void {
    super.onEvtShow();
    this.findNodeByPath();
    this.initUI();
  }
  public zOrder(): number {
    return 10;
  }
  protected findNodeByPath(): void {
    // popup/btnBreak
    this.btnBreak = find("popup/btnBreak", this.node);
    // this.btnClose = find("popup/close", this.node);
    this.layoutNodeProp = find("popup/nodeProp", this.node);
    this.itemProP = find("itemProp", this.node);

    // this.txtProp = find("itemProp/txt", this.node);
    this.levelMaxBefore = find("popup/breakProfit/profit1/original", this.node);
    this.levelMaxAfter = find("popup/breakProfit/profit1/after", this.node);
    this.qualityAddAfter = find("popup/breakProfit/profit2/after", this.node);
    this.qualityAddBefore = find("popup/breakProfit/profit2/original", this.node);
  }
  private initUI() {
    this.btnBreak.on(Input.EventType.TOUCH_END, this.click_break, this);
    // this.btnClose.on(Input.EventType.TOUCH_END, this.click_close, this);
    for (let i = 0; i < this.heroBreak.breakCostList.length; i++) {
      let attr = this.heroBreak.breakCostList[i];
      let item = instantiate(this.getNode("Item"));
      log.log(attr);
      // FmUtils.setItemNode(item, attr[0], attr[1]);
      item.getComponent(ItemCtrl).setItemNeed(attr[0], attr[1]);
      // this.node.addChild(node)

      this.layoutNodeProp.addChild(item);
    }
    let heroMessage = HeroModule.data.getHeroMessage(this.hero.id);
    let nextLevelBreak: IConfigHeroBreak = HeroModule.config.getBreakInfo(heroMessage.breakTopLevel + 2);
    this.levelMaxBefore.getComponent(Label).string = `${this.heroBreak.levelMax}`;
    this.levelMaxAfter.getComponent(Label).string = `${nextLevelBreak?.levelMax ?? this.hero.maxLv}`;
    let breakQuality = HeroModule.service.getBreakQuality(this.hero.id);
    this.qualityAddBefore.getComponent(Label).string = `${breakQuality}`;
    this.qualityAddAfter.getComponent(Label).string = `${breakQuality + this.heroBreak.qualityAdd}`;
  }
  private click_break() {
    log.log("==========break=========");
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    log.log(this.heroBreak);
    for (let i = 0; i < this.heroBreak.breakCostList.length; i++) {
      let attr = this.heroBreak.breakCostList[i];
      let itemNum = PlayerModule.data.getItemNum(attr[0]);
      if (attr[1] > itemNum) {
        UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, { itemId: attr[0] });
        //弹窗礼包事件
        MsgMgr.emit(MsgEnum.ON_ACTIVITY_TANCHUANG, attr[0]);
        return;
      }
    }
    // HeroApi.breakTop(this.hero.id);
    HeroModule.api.breakTop(this.hero.id, this.break_response.bind(this));
  }
  private break_response(data: any) {
    log.log(data);
    UIMgr.instance.back();
    UIMgr.instance.showDialog(HeroRouteItem.UIHeroBreakSuccess, { hero: this.hero });
  }
  private click_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
}
