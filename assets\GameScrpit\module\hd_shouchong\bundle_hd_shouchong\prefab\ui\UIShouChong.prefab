[{"__type__": "cc.Prefab", "_name": "UIShouChong", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "UIShouChong", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 341}, {"__id__": 849}, {"__id__": 875}, {"__id__": 895}, {"__id__": 903}], "_active": true, "_components": [{"__id__": 969}, {"__id__": 971}], "_prefab": {"__id__": 973}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "shouchong1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 96}, {"__id__": 110}, {"__id__": 118}, {"__id__": 124}, {"__id__": 206}], "_active": true, "_components": [{"__id__": 336}, {"__id__": 338}], "_prefab": {"__id__": 340}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 43, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "node_fixed", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 4}, {"__id__": 27}, {"__id__": 43}, {"__id__": 49}, {"__id__": 63}, {"__id__": 81}], "_active": true, "_components": [{"__id__": 93}], "_prefab": {"__id__": 95}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [{"__id__": 5}], "_active": true, "_components": [{"__id__": 20}, {"__id__": 22}, {"__id__": 24}], "_prefab": {"__id__": 26}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 94.71299999999997, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 4}, "_prefab": {"__id__": 6}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 5}, "asset": {"__uuid__": "3e19df77-4de3-48b7-a0b1-4f0cecb29d6a", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 7}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "d2xctZaVdFKZoVWmO//t9N", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 8}, {"__id__": 10}, {"__id__": 11}, {"__id__": 12}, {"__id__": 13}, {"__id__": 15}, {"__id__": 16}, {"__id__": 18}, {"__id__": 19}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 9}, "propertyPath": ["_name"], "value": "hero_10401"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 9}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -21, "y": 50, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 9}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 9}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 14}, "propertyPath": ["defaultAnimation"], "value": "animation1"}, {"__type__": "cc.TargetInfo", "localID": ["f1B/GOgZNLtrsJeB7LJz65"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 9}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 17}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["b91DzF0pdLaLIgomxUuK4+"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 17}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -26, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 9}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1.2, "y": 1.2, "z": 1}}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 4}, "_enabled": true, "__prefab": {"__id__": 21}, "_contentSize": {"__type__": "cc.Size", "width": 700.744, "height": 787.3670000000001}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "daqy02d2ZFRrriq0gWM8qG"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 4}, "_enabled": true, "__prefab": {"__id__": 23}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "60v1zPlYJMCb/3dtqtXdfg"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 4}, "_enabled": true, "__prefab": {"__id__": 25}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "33r7aumWZKHIBUKOAGAfXm"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cbVJ2DWYREsLt0U5wM1mA9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_sc6_jieshuo", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [{"__id__": 28}, {"__id__": 34}], "_active": true, "_components": [{"__id__": 40}], "_prefab": {"__id__": 42}, "_lpos": {"__type__": "cc.Vec3", "x": 30, "y": -174, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "RichText", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 27}, "_children": [], "_active": true, "_components": [{"__id__": 29}, {"__id__": 31}], "_prefab": {"__id__": 33}, "_lpos": {"__type__": "cc.Vec3", "x": -40, "y": 21, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 28}, "_enabled": true, "__prefab": {"__id__": 30}, "_contentSize": {"__type__": "cc.Size", "width": 452, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "38CRzrIldIlr36beHb+zXR"}, {"__type__": "cc.RichText", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 28}, "_enabled": true, "__prefab": {"__id__": 32}, "_lineHeight": 40, "_string": "<outline color=#d46b0e width=2>解锁<size=38><color=#fdd924>种族印记</color></size>坐拥<size=38><color=#fdd924>专属天赋</color></size></outline>", "_horizontalAlign": 0, "_verticalAlign": 2, "_fontSize": 33, "_fontColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_maxWidth": 0, "_fontFamily": "<PERSON><PERSON>", "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_userDefinedFont": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_cacheMode": 0, "_imageAtlas": null, "_handleTouchEvent": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "59SvRe9iRIhLlHrGHMYPyn"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "115ZMkrEBOpJWZiqjbVhbn", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "RichText", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 27}, "_children": [], "_active": true, "_components": [{"__id__": 35}, {"__id__": 37}], "_prefab": {"__id__": 39}, "_lpos": {"__type__": "cc.Vec3", "x": 5, "y": -32, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 34}, "_enabled": true, "__prefab": {"__id__": 36}, "_contentSize": {"__type__": "cc.Size", "width": 432, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e0d5lcseBGEKSL95c/wfGp"}, {"__type__": "cc.RichText", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 34}, "_enabled": true, "__prefab": {"__id__": 38}, "_lineHeight": 40, "_string": "<outline color=#d46b0e width=2>解锁专属主角<size=38><color=#fdd924>皮肤</color></size>战斗<size=38><color=#fdd924>特效</color></size></outline>", "_horizontalAlign": 0, "_verticalAlign": 2, "_fontSize": 33, "_fontColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_maxWidth": 0, "_fontFamily": "<PERSON><PERSON>", "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_userDefinedFont": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_cacheMode": 0, "_imageAtlas": null, "_handleTouchEvent": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f5yQR5tKxL+Z3ZTIYo7oEu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "64AGO7YFpLX7qyJSSejc1+", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 27}, "_enabled": true, "__prefab": {"__id__": 41}, "_contentSize": {"__type__": "cc.Size", "width": 69.2999267578125, "height": 90.39999999999999}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "caCyGijR1Eqqe8zU7/vXoC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "25IngqJM1HEZlAGqX8V4k0", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [], "_active": true, "_components": [{"__id__": 44}, {"__id__": 46}], "_prefab": {"__id__": 48}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 43}, "_enabled": true, "__prefab": {"__id__": 45}, "_contentSize": {"__type__": "cc.Size", "width": 746, "height": 1036}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "589Oy/a1hPXaetmes+XIZU"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 43}, "_enabled": true, "__prefab": {"__id__": 47}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ec74e6c1-a090-411d-ac96-96a5b19386a3@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3aPwlmvOFKTI2lhwjkuwcp"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e5tMimLuJDA4sV1Ir6leHn", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "pingzhi", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [{"__id__": 50}], "_active": true, "_components": [{"__id__": 56}, {"__id__": 58}, {"__id__": 60}], "_prefab": {"__id__": 62}, "_lpos": {"__type__": "cc.Vec3", "x": -276, "y": 293, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "hero_name", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 49}, "_children": [], "_active": true, "_components": [{"__id__": 51}, {"__id__": 53}], "_prefab": {"__id__": 55}, "_lpos": {"__type__": "cc.Vec3", "x": -3, "y": -7, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 50}, "_enabled": true, "__prefab": {"__id__": 52}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 103.96}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cd8u7xFthB6KXwFs4MSH55"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 50}, "_enabled": true, "__prefab": {"__id__": 54}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "大侠", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 46, "_overflow": 3, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1d8dtJGg9DyJQGDy7T+Y7x"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "58lP6BYXVBh7i3aOOS6Ewb", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 49}, "_enabled": true, "__prefab": {"__id__": 57}, "_contentSize": {"__type__": "cc.Size", "width": 115, "height": 288}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "649fFiaPVKHLIKPkOz5i4R"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 49}, "_enabled": true, "__prefab": {"__id__": 59}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "334ccec8-bf96-4948-b6c1-0109251f4f32@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "62ycBaJhJLq68YeLLgRlW9"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 49}, "_enabled": true, "__prefab": {"__id__": 61}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6bvSZ1QKRKdpSlXLfRbxGa"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4eeCX8elhP2rxcl83MhUjz", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "zi_shouchong", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [{"__id__": 64}, {"__id__": 70}], "_active": true, "_components": [{"__id__": 76}, {"__id__": 78}], "_prefab": {"__id__": 80}, "_lpos": {"__type__": "cc.Vec3", "x": -264, "y": 520, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 63}, "_children": [], "_active": true, "_components": [{"__id__": 65}, {"__id__": 67}], "_prefab": {"__id__": 69}, "_lpos": {"__type__": "cc.Vec3", "x": 138, "y": -33, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 64}, "_enabled": true, "__prefab": {"__id__": 66}, "_contentSize": {"__type__": "cc.Size", "width": 63, "height": 77.3}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9cgN7rpRdPiZnN1gP0OFf0"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 64}, "_enabled": true, "__prefab": {"__id__": 68}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "送", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 55, "_fontSize": 55, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 55, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 255, "g": 124, "b": 37, "a": 255}, "_outlineWidth": 4, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d9ui6vo9BAzKi46Zi5siWw"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "16+bmIxqJKPqboB76kzU2y", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 63}, "_children": [], "_active": true, "_components": [{"__id__": 71}, {"__id__": 73}], "_prefab": {"__id__": 75}, "_lpos": {"__type__": "cc.Vec3", "x": 307, "y": -29, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 70}, "_enabled": true, "__prefab": {"__id__": 72}, "_contentSize": {"__type__": "cc.Size", "width": 288, "height": 94.94}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8fp2GEDPVO863Z8COPjYiO"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 70}, "_enabled": true, "__prefab": {"__id__": 74}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 248, "b": 63, "a": 255}, "_string": "传奇战将", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 70, "_fontSize": 70, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 69, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 255, "g": 124, "b": 37, "a": 255}, "_outlineWidth": 4, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "47n148oYhHkov9VI8fsp7k"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0bt/PP8txKG5ImjIFXqyzQ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 63}, "_enabled": true, "__prefab": {"__id__": 77}, "_contentSize": {"__type__": "cc.Size", "width": 185, "height": 114}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e5DY7CJgBPpKv/fcEd4Lws"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 63}, "_enabled": true, "__prefab": {"__id__": 79}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "4131ef4a-8f48-4353-a410-3e3f486c17b5@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dbgbmZt9pLp7B/Tv/IycRW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "56DxPYCvRESa+bvu2b2Zig", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "zi_ch<PERSON><PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [{"__id__": 82}], "_active": true, "_components": [{"__id__": 88}, {"__id__": 90}], "_prefab": {"__id__": 92}, "_lpos": {"__type__": "cc.Vec3", "x": 223, "y": 354, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 81}, "_children": [], "_active": true, "_components": [{"__id__": 83}, {"__id__": 85}], "_prefab": {"__id__": 87}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 3, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 82}, "_enabled": true, "__prefab": {"__id__": 84}, "_contentSize": {"__type__": "cc.Size", "width": 217.199951171875, "height": 89.16}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a020C+rXNPjJ24Lt7yMgs3"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 82}, "_enabled": true, "__prefab": {"__id__": 86}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 250, "b": 165, "a": 255}, "_string": "8000%", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 66, "_fontSize": 66, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 66, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 206, "g": 74, "b": 9, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "00s6gfzhxC+56ugl8GjFJ7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "46Vmm1mEhKCIaEuXCTCpRD", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 81}, "_enabled": true, "__prefab": {"__id__": 89}, "_contentSize": {"__type__": "cc.Size", "width": 244, "height": 197}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "768lfXRptHBrPMAtrFkzQ9"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 81}, "_enabled": true, "__prefab": {"__id__": 91}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "18bd5d8f-994c-48d3-ad8f-9e82e4645a37@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1ey4RwMG1FfqX6bVnOn3GY"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0bkjsti8hFHqcSWc3mA7iA", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 94}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "32sWy7l9FNxZG/zvRIgNdu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3cMyaB3DdJOaF3xarVTGq2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_zhandouyulan", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 97}], "_active": true, "_components": [{"__id__": 103}, {"__id__": 105}, {"__id__": 107}], "_prefab": {"__id__": 109}, "_lpos": {"__type__": "cc.Vec3", "x": 258, "y": -46, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 96}, "_children": [], "_active": true, "_components": [{"__id__": 98}, {"__id__": 100}], "_prefab": {"__id__": 102}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -45, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 97}, "_enabled": true, "__prefab": {"__id__": 99}, "_contentSize": {"__type__": "cc.Size", "width": 122.2, "height": 52.6}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e6du31oMRLc7B01YBiQo20"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 97}, "_enabled": true, "__prefab": {"__id__": 101}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "战斗预览", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 250, "g": 128, "b": 70, "a": 255}, "_outlineWidth": 1.1, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "81npY8CllE8JD9ltFoxclN"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a1zAWppblI6Ji7YsEwaeeD", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 96}, "_enabled": true, "__prefab": {"__id__": 104}, "_contentSize": {"__type__": "cc.Size", "width": 89, "height": 89}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e3P6rRNORM4qgju8YgAZQw"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 96}, "_enabled": true, "__prefab": {"__id__": 106}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "21aa65d8-5e2e-4a7f-a211-de5c497d36ae@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4dZ12WQcBKp7gE8QsM9H9S"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 96}, "_enabled": true, "__prefab": {"__id__": 108}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "41qFO7kGdPyrrUO6/6c3G2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "64neoKQPBP7pGihlf5wsE1", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_buy_6", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 111}, {"__id__": 113}, {"__id__": 115}], "_prefab": {"__id__": 117}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -622, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 110}, "_enabled": true, "__prefab": {"__id__": 112}, "_contentSize": {"__type__": "cc.Size", "width": 251, "height": 76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4cFdyutYJGD4jWilKZ5QZg"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 110}, "_enabled": true, "__prefab": {"__id__": 114}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ce4b1782-fe29-464a-9d36-e512396e7e66@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "25/NcCXIFIJIFY+QQB3n/9"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 110}, "_enabled": true, "__prefab": {"__id__": 116}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1eHvmn98JO3JzFZ8Keyvuh"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a0Bd0uBBZJWpIW+jlCXNvG", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_yichongzhi_6", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": false, "_components": [{"__id__": 119}, {"__id__": 121}], "_prefab": {"__id__": 123}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -618, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 118}, "_enabled": true, "__prefab": {"__id__": 120}, "_contentSize": {"__type__": "cc.Size", "width": 202, "height": 133}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "83NVQteshLRqdsQs7kErnV"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 118}, "_enabled": true, "__prefab": {"__id__": 122}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "76be6699-1688-4921-beae-e8074e36241f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6dID50OddN9q8xD2Hjinr6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8dg9vmly1FfLR+sF/XjZhU", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 125}], "_active": true, "_components": [{"__id__": 197}, {"__id__": 199}, {"__id__": 201}, {"__id__": 203}], "_prefab": {"__id__": 205}, "_lpos": {"__type__": "cc.Vec3", "x": 2, "y": -405, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "node_item_6_layout", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 124}, "_children": [{"__id__": 126}], "_active": true, "_components": [{"__id__": 192}, {"__id__": 194}], "_prefab": {"__id__": 196}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "node_item", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 125}, "_children": [{"__id__": 127}, {"__id__": 175}, {"__id__": 181}], "_active": true, "_components": [{"__id__": 187}, {"__id__": 189}], "_prefab": {"__id__": 191}, "_lpos": {"__type__": "cc.Vec3", "x": 67, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 126}, "_prefab": {"__id__": 128}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 127}, "asset": {"__uuid__": "3416dcab-ee56-478f-b062-b1e826f7b707", "__expectedType__": "cc.Prefab"}, "fileId": "f5VJJTfHtB5aqbwW4jl16A", "instance": {"__id__": 129}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "ebmqceS0NPGqIWlijyuy7n", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 130}, {"__id__": 132}, {"__id__": 133}, {"__id__": 134}, {"__id__": 135}, {"__id__": 137}, {"__id__": 139}, {"__id__": 140}, {"__id__": 141}, {"__id__": 142}, {"__id__": 144}, {"__id__": 146}, {"__id__": 147}, {"__id__": 148}, {"__id__": 149}, {"__id__": 151}, {"__id__": 153}, {"__id__": 155}, {"__id__": 157}, {"__id__": 158}, {"__id__": 160}, {"__id__": 162}, {"__id__": 163}, {"__id__": 165}, {"__id__": 167}, {"__id__": 168}, {"__id__": 170}, {"__id__": 171}, {"__id__": 173}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 131}, "propertyPath": ["_name"], "value": "<PERSON><PERSON>"}, {"__type__": "cc.TargetInfo", "localID": ["f5VJJTfHtB5aqbwW4jl16A"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 131}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 131}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 131}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 136}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 101, "height": 101}}, {"__type__": "cc.TargetInfo", "localID": ["7fVX9mvddJUaVADkQ2cBdp"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 138}, "propertyPath": ["_left"], "value": 0}, {"__type__": "cc.TargetInfo", "localID": ["d3SDf6g+VPSJ1nfJHYwdlU"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 138}, "propertyPath": ["_right"], "value": 0}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 138}, "propertyPath": ["_top"], "value": 0}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 138}, "propertyPath": ["_bottom"], "value": 0}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 143}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 114, "height": 114}}, {"__type__": "cc.TargetInfo", "localID": ["c3FKi/GdFIerw2SbrXGJ9M"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 145}, "propertyPath": ["_left"], "value": 0}, {"__type__": "cc.TargetInfo", "localID": ["0aBeGKL5ZEQYQ9QqfPUOsM"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 145}, "propertyPath": ["_right"], "value": 0}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 145}, "propertyPath": ["_top"], "value": 0}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 145}, "propertyPath": ["_bottom"], "value": 0}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 150}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 114, "height": 114}}, {"__type__": "cc.TargetInfo", "localID": ["76eqiBuHdJiZAdj+7P63VD"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 152}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 53, "y": -36.1, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["3f2CI9oKNLFK7+fzc6STXj"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 154}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 102, "height": 34}}, {"__type__": "cc.TargetInfo", "localID": ["07IRwvCO9Nyq7noluFiXK4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 156}, "propertyPath": ["_bottom"], "value": 3.8999999999999986}, {"__type__": "cc.TargetInfo", "localID": ["64bvRI4TpF94y4cA2dRx5b"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 131}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 159}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 101, "height": 101}}, {"__type__": "cc.TargetInfo", "localID": ["b8lliivRBLlLauY3fNUDkb"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 161}, "propertyPath": ["_sizeMode"], "value": 0}, {"__type__": "cc.TargetInfo", "localID": ["d1PcUCEbVCQ7t7NK/Q0EQM"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 131}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 164}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["83uRFCZDpOWIYKQdKjv3ks"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 166}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["eeCRBl0e1DboDDLjZnVY/4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 152}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 169}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"__type__": "cc.TargetInfo", "localID": ["05/dyc9JZPzqOEp1/yFyae"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 169}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 172}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["16SAIdCm9HC5vnRtxu66Bi"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 174}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["a1tsAamAJGy7Lb6gW02dNW"]}, {"__type__": "cc.Node", "_name": "icon_suo", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 126}, "_children": [], "_active": false, "_components": [{"__id__": 176}, {"__id__": 178}], "_prefab": {"__id__": 180}, "_lpos": {"__type__": "cc.Vec3", "x": 32, "y": 36, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 175}, "_enabled": true, "__prefab": {"__id__": 177}, "_contentSize": {"__type__": "cc.Size", "width": 43, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3c8I1CSUJLdZOBxx38luIE"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 175}, "_enabled": true, "__prefab": {"__id__": 179}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "33ea29d6-5c1c-4e07-8c6d-09c5154daa2c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2aAuUteHZCJ6uvQcEsn8ZE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "43LLij0eFPipT+U+e5Lmh6", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "icon_duihao", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 126}, "_children": [], "_active": false, "_components": [{"__id__": 182}, {"__id__": 184}], "_prefab": {"__id__": 186}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 181}, "_enabled": true, "__prefab": {"__id__": 183}, "_contentSize": {"__type__": "cc.Size", "width": 79, "height": 62}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d6FRXd6HZDi6yuYbAz2meb"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 181}, "_enabled": true, "__prefab": {"__id__": 185}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "a5798517-ec85-41b4-9388-a3fe54eba80c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ad1ka863lPL4Zm4BKX93Tu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "439V2bRbxMAJji8J2YJ5zp", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 126}, "_enabled": true, "__prefab": {"__id__": 188}, "_contentSize": {"__type__": "cc.Size", "width": 114, "height": 114}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "806V+KaFtFurOUguLeh59r"}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 126}, "_enabled": true, "__prefab": {"__id__": 190}, "playOnLoad": false, "_clips": [{"__uuid__": "04d0353f-53fd-4672-8ebb-723eab0ca991", "__expectedType__": "cc.AnimationClip"}], "_defaultClip": {"__uuid__": "04d0353f-53fd-4672-8ebb-723eab0ca991", "__expectedType__": "cc.AnimationClip"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "18I+7GmrpKvLK4exU4eP65"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b7Xh2pxZlOKYLahVKAwikn", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 125}, "_enabled": true, "__prefab": {"__id__": 193}, "_contentSize": {"__type__": "cc.Size", "width": 124, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7ePjTKaHVEu49b7ngatTEx"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 125}, "_enabled": true, "__prefab": {"__id__": 195}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 10, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 33, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "84v+JpBrZAlZMebQPHP8aw"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c1UeoODe5Im6caXGID0HF8", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 124}, "_enabled": true, "__prefab": {"__id__": 198}, "_contentSize": {"__type__": "cc.Size", "width": 640.45, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a4xCqIOmdHgbFuofmdDTmD"}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 124}, "_enabled": true, "__prefab": {"__id__": 200}, "bounceDuration": 1, "brake": 0.5, "elastic": true, "inertia": true, "horizontal": true, "vertical": false, "cancelInnerEvents": true, "scrollEvents": [], "_content": {"__id__": 125}, "_horizontalScrollBar": null, "_verticalScrollBar": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "38ldfibdVCra2yC67BYe22"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 124}, "_enabled": true, "__prefab": {"__id__": 202}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d0f0F6HJVFdKYDjvNQtEe7"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 124}, "_enabled": true, "__prefab": {"__id__": 204}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4fxrAZqJ9KyJ6B8p3PQr9Q"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a3ZTG3WZdOdJ7WdzMs5oJd", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_day_switch_6", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 207}, {"__id__": 249}, {"__id__": 291}], "_active": true, "_components": [{"__id__": 333}], "_prefab": {"__id__": 335}, "_lpos": {"__type__": "cc.Vec3", "x": -92, "y": -278, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "node_day_6_0", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 206}, "_children": [{"__id__": 208}, {"__id__": 220}], "_active": true, "_components": [{"__id__": 244}, {"__id__": 246}], "_prefab": {"__id__": 248}, "_lpos": {"__type__": "cc.Vec3", "x": -167, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_sc_tip_0", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 207}, "_children": [{"__id__": 209}], "_active": true, "_components": [{"__id__": 215}, {"__id__": 217}], "_prefab": {"__id__": 219}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "zi_sc_diyitian_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 208}, "_children": [], "_active": true, "_components": [{"__id__": 210}, {"__id__": 212}], "_prefab": {"__id__": 214}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 209}, "_enabled": true, "__prefab": {"__id__": 211}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5bSW2TvAhNrKeMFaLbQI9+"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 209}, "_enabled": true, "__prefab": {"__id__": 213}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "731de326-7694-4a9f-81ae-7f4fbaef8aea@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bba91Y06lPTpC+WBQiYxBh"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "64KREKY9JMYrFuyC/1GkGC", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 208}, "_enabled": true, "__prefab": {"__id__": 216}, "_contentSize": {"__type__": "cc.Size", "width": 157, "height": 67}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e7Ts/zLpdFHJUixUNVJmuw"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 208}, "_enabled": true, "__prefab": {"__id__": 218}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b64d1179-8f6d-4957-9fef-583f14677441@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "10J7zeyaBE0pprfEUj1cZG"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d5cl+P4ThNo4EUMSnm+fTN", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_select", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 207}, "_children": [{"__id__": 221}, {"__id__": 227}, {"__id__": 233}], "_active": true, "_components": [{"__id__": 239}, {"__id__": 241}], "_prefab": {"__id__": 243}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "zi_sc_diyitian_2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 220}, "_children": [], "_active": true, "_components": [{"__id__": 222}, {"__id__": 224}], "_prefab": {"__id__": 226}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 221}, "_enabled": true, "__prefab": {"__id__": 223}, "_contentSize": {"__type__": "cc.Size", "width": 104, "height": 36}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b4joJ8r1pJQZpdoQgqKc5W"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 221}, "_enabled": true, "__prefab": {"__id__": 225}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9ec802d8-462e-41ab-8757-2d2a3cf9573e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "419Zk3ZxZCKLYb22yZHHMy"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a8k/YHpA9Lhr5pE0tyJ8hU", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_moquan_juhong", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 220}, "_children": [], "_active": true, "_components": [{"__id__": 228}, {"__id__": 230}], "_prefab": {"__id__": 232}, "_lpos": {"__type__": "cc.Vec3", "x": 44, "y": -8, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 227}, "_enabled": true, "__prefab": {"__id__": 229}, "_contentSize": {"__type__": "cc.Size", "width": 73, "height": 73}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2dSfBKtidNHoTe/rddtQ5K"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 227}, "_enabled": true, "__prefab": {"__id__": 231}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ac31b390-a3a8-4d97-b22c-a8be45f77695@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0cvxRF1BdIFIajAPwEkPDv"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "21NWFc+LRBjYBVa12/sqB1", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_lingqu_6_0", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 220}, "_children": [], "_active": false, "_components": [{"__id__": 234}, {"__id__": 236}], "_prefab": {"__id__": 238}, "_lpos": {"__type__": "cc.Vec3", "x": 259, "y": -127, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 233}, "_enabled": true, "__prefab": {"__id__": 235}, "_contentSize": {"__type__": "cc.Size", "width": 640.45, "height": 122}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1c2jHjfplFeLFh3ymu+uCa"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 233}, "_enabled": true, "__prefab": {"__id__": 237}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3cU8lo3lVNnIC4+lJ+qacK"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a6AlhsJwFIsLhvFbYQ1PmN", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 220}, "_enabled": true, "__prefab": {"__id__": 240}, "_contentSize": {"__type__": "cc.Size", "width": 157, "height": 68}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6fduRQTbZH06To0NBXBgES"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 220}, "_enabled": true, "__prefab": {"__id__": 242}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0360c806-e77c-429d-8b2c-9582855ade50@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "74kvushwhPUqxC6Ty1pzOu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f0Lz551DpJx6TRr1VUb7bn", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 207}, "_enabled": true, "__prefab": {"__id__": 245}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 87}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6993S1XW9P65P5CWCl7TF2"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 207}, "_enabled": true, "__prefab": {"__id__": 247}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "44tqqZugNNCrfeN1xGDDWg"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d5s2josqRP9oEvbsOk98+x", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_day_6_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 206}, "_children": [{"__id__": 250}, {"__id__": 262}], "_active": true, "_components": [{"__id__": 286}, {"__id__": 288}], "_prefab": {"__id__": 290}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_sc_tip_0", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 249}, "_children": [{"__id__": 251}], "_active": true, "_components": [{"__id__": 257}, {"__id__": 259}], "_prefab": {"__id__": 261}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "zi_sc_diertian_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 250}, "_children": [], "_active": true, "_components": [{"__id__": 252}, {"__id__": 254}], "_prefab": {"__id__": 256}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 251}, "_enabled": true, "__prefab": {"__id__": 253}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "35JM+FxX5IMpw2wi2UHRo5"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 251}, "_enabled": true, "__prefab": {"__id__": 255}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e906f6f2-01c2-4491-a948-4bd861a59d49@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "65B0d/oF5MMp/JLXNgEONf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1e6I9fuzBE8JvKRs9tuKk9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 250}, "_enabled": true, "__prefab": {"__id__": 258}, "_contentSize": {"__type__": "cc.Size", "width": 157, "height": 67}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "78oLg+4PNP+4tA2WHWstqz"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 250}, "_enabled": true, "__prefab": {"__id__": 260}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b64d1179-8f6d-4957-9fef-583f14677441@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a4sJ6Uqz5BWKg+72oJRjHh"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f0zrP/p9RH86jhmaQth2sy", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_select", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 249}, "_children": [{"__id__": 263}, {"__id__": 269}, {"__id__": 275}], "_active": false, "_components": [{"__id__": 281}, {"__id__": 283}], "_prefab": {"__id__": 285}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "zi_sc_diertian_2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 262}, "_children": [], "_active": true, "_components": [{"__id__": 264}, {"__id__": 266}], "_prefab": {"__id__": 268}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 263}, "_enabled": true, "__prefab": {"__id__": 265}, "_contentSize": {"__type__": "cc.Size", "width": 104, "height": 36}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e6zfPiyc1E27SKr4Lw+6zh"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 263}, "_enabled": true, "__prefab": {"__id__": 267}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "57d123c1-bffc-4fa6-8a73-8ed8dd545bbe@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2aw1QzjItMJbCYMxeAf3wN"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "44/IPF+8dF8ZRZ9CokvCzj", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_moquan_juhong", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 262}, "_children": [], "_active": true, "_components": [{"__id__": 270}, {"__id__": 272}], "_prefab": {"__id__": 274}, "_lpos": {"__type__": "cc.Vec3", "x": 44, "y": -8, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 269}, "_enabled": true, "__prefab": {"__id__": 271}, "_contentSize": {"__type__": "cc.Size", "width": 73, "height": 73}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d1h1LzlghHaYLDn4Et6GJm"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 269}, "_enabled": true, "__prefab": {"__id__": 273}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ac31b390-a3a8-4d97-b22c-a8be45f77695@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "beDi77/C1JXoEn0VxWwaWw"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c7iwuty7hO5qFvjb6pHoW7", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_lingqu_6_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 262}, "_children": [], "_active": false, "_components": [{"__id__": 276}, {"__id__": 278}], "_prefab": {"__id__": 280}, "_lpos": {"__type__": "cc.Vec3", "x": -76, "y": -127, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 275}, "_enabled": true, "__prefab": {"__id__": 277}, "_contentSize": {"__type__": "cc.Size", "width": 640.45, "height": 122}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e4OLumsnND44F3shjeCX9v"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 275}, "_enabled": true, "__prefab": {"__id__": 279}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bdc4JemS5OjKs6xX+wU4nX"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "deoUzPLx1BmLl49DUxKmgc", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 262}, "_enabled": true, "__prefab": {"__id__": 282}, "_contentSize": {"__type__": "cc.Size", "width": 157, "height": 68}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "easabJG8BI2Z4P07bB6DAd"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 262}, "_enabled": true, "__prefab": {"__id__": 284}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0360c806-e77c-429d-8b2c-9582855ade50@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "16EXbexvJMCI4DYPR9Sjb6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "baqxWo6QZOnZ/rUaZeXJr/", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 249}, "_enabled": true, "__prefab": {"__id__": 287}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 87}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2eXI4ZyypNWb0HlYpm7dud"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 249}, "_enabled": true, "__prefab": {"__id__": 289}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d1LAZhCDNEhZPGM9crZKz1"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b788ky+P5BZaLtmZ8aWehz", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_day_6_2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 206}, "_children": [{"__id__": 292}, {"__id__": 304}], "_active": true, "_components": [{"__id__": 328}, {"__id__": 330}], "_prefab": {"__id__": 332}, "_lpos": {"__type__": "cc.Vec3", "x": 168, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_sc_tip_0", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 291}, "_children": [{"__id__": 293}], "_active": true, "_components": [{"__id__": 299}, {"__id__": 301}], "_prefab": {"__id__": 303}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "zi_sc_disanitian_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 292}, "_children": [], "_active": true, "_components": [{"__id__": 294}, {"__id__": 296}], "_prefab": {"__id__": 298}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 293}, "_enabled": true, "__prefab": {"__id__": 295}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "960Q0QyjpMN7kWI8lk+cUV"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 293}, "_enabled": true, "__prefab": {"__id__": 297}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "29076484-320c-41ff-9452-08eefc1b9b35@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "39/1a7+txC76n1Ak0vGEYb"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "73DXzg9HFOO7TlvMA4tjt6", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 292}, "_enabled": true, "__prefab": {"__id__": 300}, "_contentSize": {"__type__": "cc.Size", "width": 157, "height": 67}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9fj/xrUnJHlI3AAvYZ2NR8"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 292}, "_enabled": true, "__prefab": {"__id__": 302}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b64d1179-8f6d-4957-9fef-583f14677441@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b2px0mxOpN8aXPBzkAwt0I"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "03gvK7HZBFR4DffQrJ9auK", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_select", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 291}, "_children": [{"__id__": 305}, {"__id__": 311}, {"__id__": 317}], "_active": false, "_components": [{"__id__": 323}, {"__id__": 325}], "_prefab": {"__id__": 327}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "zi_sc_disantian_2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 304}, "_children": [], "_active": true, "_components": [{"__id__": 306}, {"__id__": 308}], "_prefab": {"__id__": 310}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 305}, "_enabled": true, "__prefab": {"__id__": 307}, "_contentSize": {"__type__": "cc.Size", "width": 104, "height": 36}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e4d/QPDGtH55PAFULS9mfh"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 305}, "_enabled": true, "__prefab": {"__id__": 309}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "10d2ef85-7d64-45dd-b086-8c2228e9bea7@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f90IzzN4ZOKpQMsmQfkif9"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ccqtY/snpCwZa9X1VmINpd", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_moquan_juhong", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 304}, "_children": [], "_active": true, "_components": [{"__id__": 312}, {"__id__": 314}], "_prefab": {"__id__": 316}, "_lpos": {"__type__": "cc.Vec3", "x": 44, "y": -8, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 311}, "_enabled": true, "__prefab": {"__id__": 313}, "_contentSize": {"__type__": "cc.Size", "width": 73, "height": 73}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "68VE1kcnJFMZrsIN9takZT"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 311}, "_enabled": true, "__prefab": {"__id__": 315}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ac31b390-a3a8-4d97-b22c-a8be45f77695@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b8zvhBjsxEkpUsmj77cMRv"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "75gq+Rme1FxZCVlqzGE4Fd", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_lingqu_6_2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 304}, "_children": [], "_active": false, "_components": [{"__id__": 318}, {"__id__": 320}], "_prefab": {"__id__": 322}, "_lpos": {"__type__": "cc.Vec3", "x": -76, "y": -127, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 317}, "_enabled": true, "__prefab": {"__id__": 319}, "_contentSize": {"__type__": "cc.Size", "width": 640.45, "height": 122}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "84vYyg5UBJb4xmC9kdVgZ/"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 317}, "_enabled": true, "__prefab": {"__id__": 321}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "76UDn68ARKJYkRlAYJnI5/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "93GfJxG5lF5L6mKjV6KSM9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 304}, "_enabled": true, "__prefab": {"__id__": 324}, "_contentSize": {"__type__": "cc.Size", "width": 157, "height": 68}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "74jkuF+0ZGa7UGGQj0VCSo"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 304}, "_enabled": true, "__prefab": {"__id__": 326}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0360c806-e77c-429d-8b2c-9582855ade50@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0346MWCXFNXJr0eHOsbb0e"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3bREWCHwBDAbB1jUbDXkv5", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 291}, "_enabled": true, "__prefab": {"__id__": 329}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 87}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "69yOUrrY9PFYwmhvX5JhKH"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 291}, "_enabled": true, "__prefab": {"__id__": 331}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "77zw8fqHlI+7F5+NS1AKvH"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c8ZG4jwyhJRYH+fZWccIOs", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 206}, "_enabled": true, "__prefab": {"__id__": 334}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "98mNT7zgJHJrmnDJjSYM6K"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "65i41PUypIGo1LSZT66uBK", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 337}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cbSQ0st6FEOrS/DOqC9mdx"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 339}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b4PUHzBsFC+ZHuUo/LSNs/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cdbOU86atIcI1Bsjf9k0EO", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "shouchong2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 342}, {"__id__": 352}, {"__id__": 360}, {"__id__": 374}, {"__id__": 380}, {"__id__": 546}, {"__id__": 554}, {"__id__": 560}, {"__id__": 576}, {"__id__": 658}, {"__id__": 788}, {"__id__": 806}, {"__id__": 818}, {"__id__": 828}, {"__id__": 836}], "_active": true, "_components": [{"__id__": 844}, {"__id__": 846}], "_prefab": {"__id__": 848}, "_lpos": {"__type__": "cc.Vec3", "x": 889, "y": 43, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "node_page", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 341}, "_children": [], "_active": true, "_components": [{"__id__": 343}, {"__id__": 345}, {"__id__": 347}, {"__id__": 349}], "_prefab": {"__id__": 351}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 94, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 342}, "_enabled": true, "__prefab": {"__id__": 344}, "_contentSize": {"__type__": "cc.Size", "width": 690, "height": 790.205}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "88aGDSX5FBO7lZiIC7TiVi"}, {"__type__": "fe48a55/2VHg5MmeyH/Ap7U", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 342}, "_enabled": true, "__prefab": {"__id__": 346}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "57pz4zwyNBrJ8m+JcvdaiV"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 342}, "_enabled": true, "__prefab": {"__id__": 348}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "acMWipkL5L8Jnno5/Xw90g"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 342}, "_enabled": true, "__prefab": {"__id__": 350}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f83tVo4B5FrptYZkx/vxuH"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "87tarmcHZIOrVndrRbJZdw", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Block", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 341}, "_children": [], "_active": true, "_components": [{"__id__": 353}, {"__id__": 355}, {"__id__": 357}], "_prefab": {"__id__": 359}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 352}, "_enabled": true, "__prefab": {"__id__": 354}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f7R8BUom5Edpm+2XYphtBK"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 352}, "_enabled": true, "__prefab": {"__id__": 356}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 690, "_originalHeight": 790.205, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9eweUiRYtBLJHwVZVqJhpy"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 352}, "_enabled": true, "__prefab": {"__id__": 358}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "27rJcVkmNLG54lhb/f3yB0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "22FNBlpZ9FnLQFYN6eCkCd", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_zhandouyulan_2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 341}, "_children": [{"__id__": 361}], "_active": true, "_components": [{"__id__": 367}, {"__id__": 369}, {"__id__": 371}], "_prefab": {"__id__": 373}, "_lpos": {"__type__": "cc.Vec3", "x": 258, "y": -46, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 360}, "_children": [], "_active": true, "_components": [{"__id__": 362}, {"__id__": 364}], "_prefab": {"__id__": 366}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -45, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 361}, "_enabled": true, "__prefab": {"__id__": 363}, "_contentSize": {"__type__": "cc.Size", "width": 122.2, "height": 52.6}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0fgODPEi9CY4a3WaAgCL8u"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 361}, "_enabled": true, "__prefab": {"__id__": 365}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "战斗预览", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 250, "g": 128, "b": 70, "a": 255}, "_outlineWidth": 1.1, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b0gUJDsnNE+p22Bx+7IvlH"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "01cUe/k25BQ5D+ZWvyqWwb", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 360}, "_enabled": true, "__prefab": {"__id__": 368}, "_contentSize": {"__type__": "cc.Size", "width": 89, "height": 89}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9b9rlVUyVDjr0Q7DsEIqbK"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 360}, "_enabled": true, "__prefab": {"__id__": 370}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "21aa65d8-5e2e-4a7f-a211-de5c497d36ae@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f3bI3kU1BHjpCLduJKRCd/"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 360}, "_enabled": true, "__prefab": {"__id__": 372}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "71oKlghg9BgY2GroVWNNBC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "44nruNNXtHeYu2f/WxJysN", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 341}, "_children": [], "_active": true, "_components": [{"__id__": 375}, {"__id__": 377}], "_prefab": {"__id__": 379}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 374}, "_enabled": true, "__prefab": {"__id__": 376}, "_contentSize": {"__type__": "cc.Size", "width": 746, "height": 1036}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9ayo5K0hZKJ5wjbpnpZgiq"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 374}, "_enabled": true, "__prefab": {"__id__": 378}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6a4a7590-4705-45e1-b2e8-bf7f991fafa4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6dAaBJ63VBpLHYhxEDzON9"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "16XretWuVDAbLyP2Ojg6i7", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "select", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 341}, "_children": [{"__id__": 381}, {"__id__": 399}, {"__id__": 417}, {"__id__": 435}, {"__id__": 453}, {"__id__": 471}, {"__id__": 489}, {"__id__": 507}, {"__id__": 525}], "_active": true, "_components": [{"__id__": 543}], "_prefab": {"__id__": 545}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -196, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "tab_0", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 380}, "_children": [{"__id__": 382}, {"__id__": 388}], "_active": true, "_components": [{"__id__": 396}], "_prefab": {"__id__": 398}, "_lpos": {"__type__": "cc.Vec3", "x": -212, "y": -12, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "head", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 381}, "_children": [], "_active": true, "_components": [{"__id__": 383}, {"__id__": 385}], "_prefab": {"__id__": 387}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.7, "y": 0.7, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 382}, "_enabled": true, "__prefab": {"__id__": 384}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2egABsv19MFLPpxpSEXcOQ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 382}, "_enabled": true, "__prefab": {"__id__": 386}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e34c2d74-5c66-4d6b-9d56-12fa42383360@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bfIndIc7pIxqhVoNkOL0QM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "47ttvEew9Ky5Z2GmuIIUIX", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_select", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 381}, "_children": [], "_active": true, "_components": [{"__id__": 389}, {"__id__": 391}, {"__id__": 393}], "_prefab": {"__id__": 395}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 388}, "_enabled": true, "__prefab": {"__id__": 390}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "72YcwZqAZA8pE7l9ZHMMWb"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 388}, "_enabled": true, "__prefab": {"__id__": 392}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b47390ca-96e2-4dfd-b431-646c0b1d2e39@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "92RepVlQ9I/oYn0nZarQAE"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 388}, "_enabled": true, "__prefab": {"__id__": 394}, "_opacity": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ab5kp6PHRDrLhmMhJxXnyN"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4dd0wVJWxOQpg8rX2zNiHv", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 381}, "_enabled": true, "__prefab": {"__id__": 397}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "955UpCe8dKMo5l/zakTaIl"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5dMPdcf1JPvaf6ZLwqKoA9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "tab_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 380}, "_children": [{"__id__": 400}, {"__id__": 406}], "_active": true, "_components": [{"__id__": 414}], "_prefab": {"__id__": 416}, "_lpos": {"__type__": "cc.Vec3", "x": -159, "y": -12, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "head", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 399}, "_children": [], "_active": true, "_components": [{"__id__": 401}, {"__id__": 403}], "_prefab": {"__id__": 405}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.7, "y": 0.7, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 400}, "_enabled": true, "__prefab": {"__id__": 402}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ac0tE2dA5ORLs+HTvzgtV1"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 400}, "_enabled": true, "__prefab": {"__id__": 404}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e34c2d74-5c66-4d6b-9d56-12fa42383360@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f8yj+J3r5C7auBBjbTYs9S"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b9aFeUCBBLMKhR/yiRC+zX", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_select", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 399}, "_children": [], "_active": true, "_components": [{"__id__": 407}, {"__id__": 409}, {"__id__": 411}], "_prefab": {"__id__": 413}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 406}, "_enabled": true, "__prefab": {"__id__": 408}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5deXNr7EhAGoOMBK8QJGEu"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 406}, "_enabled": true, "__prefab": {"__id__": 410}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b47390ca-96e2-4dfd-b431-646c0b1d2e39@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f6aqD/0mBHko5jrAlv2LxP"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 406}, "_enabled": true, "__prefab": {"__id__": 412}, "_opacity": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "02uTTq8IxHBKWo4brjGdFj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cej3xZuwNBj78e2OaaQyHf", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 399}, "_enabled": true, "__prefab": {"__id__": 415}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "16dcUBGHpMz7WC9t/XOxKX"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f5otcNysNHOLwuiBwJv/jk", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "tab_2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 380}, "_children": [{"__id__": 418}, {"__id__": 424}], "_active": true, "_components": [{"__id__": 432}], "_prefab": {"__id__": 434}, "_lpos": {"__type__": "cc.Vec3", "x": -106, "y": -12, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "head", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 417}, "_children": [], "_active": true, "_components": [{"__id__": 419}, {"__id__": 421}], "_prefab": {"__id__": 423}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.7, "y": 0.7, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 418}, "_enabled": true, "__prefab": {"__id__": 420}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fa//abw/tAx7AqoaWFCLru"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 418}, "_enabled": true, "__prefab": {"__id__": 422}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e34c2d74-5c66-4d6b-9d56-12fa42383360@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fdVQsXmAFPt7/4rx94XT8w"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "81caDO1fFIgJxE12l9uYqo", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_select", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 417}, "_children": [], "_active": true, "_components": [{"__id__": 425}, {"__id__": 427}, {"__id__": 429}], "_prefab": {"__id__": 431}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 424}, "_enabled": true, "__prefab": {"__id__": 426}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d44znT/M1NjIxEsZevr5+f"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 424}, "_enabled": true, "__prefab": {"__id__": 428}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b47390ca-96e2-4dfd-b431-646c0b1d2e39@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f8Xdx/KXFOsovqq5IJ97Iu"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 424}, "_enabled": true, "__prefab": {"__id__": 430}, "_opacity": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "baCj5/QV5NbLyGbbhspGuu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8atDN5kDRE5KG46rQ/WaWw", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 417}, "_enabled": true, "__prefab": {"__id__": 433}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "50WFl5B4dBNpfy0nDYBZN+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "91bH1dEWZCuKASOe8B/9P6", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "tab_3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 380}, "_children": [{"__id__": 436}, {"__id__": 442}], "_active": true, "_components": [{"__id__": 450}], "_prefab": {"__id__": 452}, "_lpos": {"__type__": "cc.Vec3", "x": -53, "y": -12, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "head", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 435}, "_children": [], "_active": true, "_components": [{"__id__": 437}, {"__id__": 439}], "_prefab": {"__id__": 441}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.7, "y": 0.7, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 436}, "_enabled": true, "__prefab": {"__id__": 438}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6dJrbnUNlPrpeufkovFvw0"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 436}, "_enabled": true, "__prefab": {"__id__": 440}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e34c2d74-5c66-4d6b-9d56-12fa42383360@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "34YjA37LVFYY82AE1kJp5t"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9b17cJq5RB8YbrjoiP9GFj", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_select", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 435}, "_children": [], "_active": true, "_components": [{"__id__": 443}, {"__id__": 445}, {"__id__": 447}], "_prefab": {"__id__": 449}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 442}, "_enabled": true, "__prefab": {"__id__": 444}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "42JUOGWsdLVYSnnAjlr5UD"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 442}, "_enabled": true, "__prefab": {"__id__": 446}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b47390ca-96e2-4dfd-b431-646c0b1d2e39@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a6OKdhWGtJSJDoI9IhTyE0"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 442}, "_enabled": true, "__prefab": {"__id__": 448}, "_opacity": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c7ZTLgB4NLmK7AlWuglgrk"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dbmBojBjZJF6CINMlKJ/XN", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 435}, "_enabled": true, "__prefab": {"__id__": 451}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a1o/JikuxJiaW+EdWFT970"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c84XPz349G1ptdyzpv3WBf", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "tab_4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 380}, "_children": [{"__id__": 454}, {"__id__": 460}], "_active": true, "_components": [{"__id__": 468}], "_prefab": {"__id__": 470}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -12, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "head", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 453}, "_children": [], "_active": true, "_components": [{"__id__": 455}, {"__id__": 457}], "_prefab": {"__id__": 459}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.7, "y": 0.7, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 454}, "_enabled": true, "__prefab": {"__id__": 456}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "79Fef6nr9LR5m9apWSnoaI"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 454}, "_enabled": true, "__prefab": {"__id__": 458}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e34c2d74-5c66-4d6b-9d56-12fa42383360@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "406A1BAjtGCKVUvNQH39Ns"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bdjYaNO8NPS4rk8byKg29m", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_select", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 453}, "_children": [], "_active": true, "_components": [{"__id__": 461}, {"__id__": 463}, {"__id__": 465}], "_prefab": {"__id__": 467}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 460}, "_enabled": true, "__prefab": {"__id__": 462}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "feUh10v01O4Zsg+dL8tzUb"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 460}, "_enabled": true, "__prefab": {"__id__": 464}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b47390ca-96e2-4dfd-b431-646c0b1d2e39@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "22n0PNylBGjr0kFXzJCJ/k"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 460}, "_enabled": true, "__prefab": {"__id__": 466}, "_opacity": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5cFvYsV8NM7IPQnYOc1Cut"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "91a6zvFyBGIa1mGW9jt+pT", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 453}, "_enabled": true, "__prefab": {"__id__": 469}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "986PMDIpdJJL9aSSwuCRP+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "28notva1RFJ6iUYaPpgzoD", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "tab_5", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 380}, "_children": [{"__id__": 472}, {"__id__": 478}], "_active": true, "_components": [{"__id__": 486}], "_prefab": {"__id__": 488}, "_lpos": {"__type__": "cc.Vec3", "x": 53, "y": -12, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "head", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 471}, "_children": [], "_active": true, "_components": [{"__id__": 473}, {"__id__": 475}], "_prefab": {"__id__": 477}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.7, "y": 0.7, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 472}, "_enabled": true, "__prefab": {"__id__": 474}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "39E32iTPxBnKPW4ZiKH+uY"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 472}, "_enabled": true, "__prefab": {"__id__": 476}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e34c2d74-5c66-4d6b-9d56-12fa42383360@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d3putKzZxKaYAq6hON9lFz"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "454ULWj8lNlLMG7Npy5EDK", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_select", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 471}, "_children": [], "_active": true, "_components": [{"__id__": 479}, {"__id__": 481}, {"__id__": 483}], "_prefab": {"__id__": 485}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 478}, "_enabled": true, "__prefab": {"__id__": 480}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8f6JwvZ+ZOObn3JOWfLPLa"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 478}, "_enabled": true, "__prefab": {"__id__": 482}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b47390ca-96e2-4dfd-b431-646c0b1d2e39@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4fEqwYLGBB9qQkcsw42eBk"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 478}, "_enabled": true, "__prefab": {"__id__": 484}, "_opacity": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f3b8EMRkBGh7ZYgPtxZSTs"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ddgEHulr9ODZ3alF/FEXrf", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 471}, "_enabled": true, "__prefab": {"__id__": 487}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c56xNhNN5B+IVLSoW4PzYW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "04p3JNOENOP5PiVaRAva2p", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "tab_6", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 380}, "_children": [{"__id__": 490}, {"__id__": 496}], "_active": true, "_components": [{"__id__": 504}], "_prefab": {"__id__": 506}, "_lpos": {"__type__": "cc.Vec3", "x": 106, "y": -12, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "head", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 489}, "_children": [], "_active": true, "_components": [{"__id__": 491}, {"__id__": 493}], "_prefab": {"__id__": 495}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.7, "y": 0.7, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 490}, "_enabled": true, "__prefab": {"__id__": 492}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f509WEUn5AfqHsqS3QPUHD"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 490}, "_enabled": true, "__prefab": {"__id__": 494}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e34c2d74-5c66-4d6b-9d56-12fa42383360@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ddlYX0N/xMl62TaetTjKVF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e5V6qFXwVPD4so58eO247p", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_select", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 489}, "_children": [], "_active": true, "_components": [{"__id__": 497}, {"__id__": 499}, {"__id__": 501}], "_prefab": {"__id__": 503}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 496}, "_enabled": true, "__prefab": {"__id__": 498}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "66bnsjuslBLoiUqYkHtlP/"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 496}, "_enabled": true, "__prefab": {"__id__": 500}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b47390ca-96e2-4dfd-b431-646c0b1d2e39@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0dTTnR19dP04LIKEvjLQFl"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 496}, "_enabled": true, "__prefab": {"__id__": 502}, "_opacity": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3183qm0i1Hlpkb4UsFkrcr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "efWxKfZnRMrJb6Yw/1vCT4", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 489}, "_enabled": true, "__prefab": {"__id__": 505}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cf1NflT9hC4Ic4FrMrAWV0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b6iPTPwHhJwo8wnGiUhwI8", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "tab_7", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 380}, "_children": [{"__id__": 508}, {"__id__": 514}], "_active": true, "_components": [{"__id__": 522}], "_prefab": {"__id__": 524}, "_lpos": {"__type__": "cc.Vec3", "x": 159, "y": -12, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "head", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 507}, "_children": [], "_active": true, "_components": [{"__id__": 509}, {"__id__": 511}], "_prefab": {"__id__": 513}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.7, "y": 0.7, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 508}, "_enabled": true, "__prefab": {"__id__": 510}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b0Fg88kblD6qwH/1XEuBuf"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 508}, "_enabled": true, "__prefab": {"__id__": 512}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e34c2d74-5c66-4d6b-9d56-12fa42383360@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "48SJ39So1DJ4lpsCBRjxFi"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c9pwczdzhKVY6N4ihLK3eJ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_select", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 507}, "_children": [], "_active": true, "_components": [{"__id__": 515}, {"__id__": 517}, {"__id__": 519}], "_prefab": {"__id__": 521}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 514}, "_enabled": true, "__prefab": {"__id__": 516}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "72TWNCuplCvKNH0u71izBT"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 514}, "_enabled": true, "__prefab": {"__id__": 518}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b47390ca-96e2-4dfd-b431-646c0b1d2e39@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bc7qQI5rxK5oBKIo7rbJ9n"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 514}, "_enabled": true, "__prefab": {"__id__": 520}, "_opacity": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "84FfCRXmdESIyk2EGLAiVL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2aohX2q6RPjpddAxMdIvMR", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 507}, "_enabled": true, "__prefab": {"__id__": 523}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2charc7jhADL/IhIQPYjAM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "69kvVJhy9E8qGiv73u9NFv", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "tab_8", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 380}, "_children": [{"__id__": 526}, {"__id__": 532}], "_active": true, "_components": [{"__id__": 540}], "_prefab": {"__id__": 542}, "_lpos": {"__type__": "cc.Vec3", "x": 212, "y": -12, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "head", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 525}, "_children": [], "_active": true, "_components": [{"__id__": 527}, {"__id__": 529}], "_prefab": {"__id__": 531}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.7, "y": 0.7, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 526}, "_enabled": true, "__prefab": {"__id__": 528}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "30fGAa1cJMN5TmjDl/ADLD"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 526}, "_enabled": true, "__prefab": {"__id__": 530}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e34c2d74-5c66-4d6b-9d56-12fa42383360@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "80X6zdMyNOX60wfngqD96p"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cbPd2YzN5OkobSAqm1jGEW", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_select", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 525}, "_children": [], "_active": true, "_components": [{"__id__": 533}, {"__id__": 535}, {"__id__": 537}], "_prefab": {"__id__": 539}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 532}, "_enabled": true, "__prefab": {"__id__": 534}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fe7sVqS+FJoJJNtfzceQfh"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 532}, "_enabled": true, "__prefab": {"__id__": 536}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b47390ca-96e2-4dfd-b431-646c0b1d2e39@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e7YaOFhvFMZoETURPKuKm4"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 532}, "_enabled": true, "__prefab": {"__id__": 538}, "_opacity": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "77qo+C4VFGlKkk0xrGnPqN"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "15xhRX2fBEIYyF+ECx0rHG", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 525}, "_enabled": true, "__prefab": {"__id__": 541}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5aAMuPyeVGSI8PhbEREhHi"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bfGyHRcGlIranbhBzfu9lr", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 380}, "_enabled": true, "__prefab": {"__id__": 544}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "27x8/M6CRIZ5+H6o+lz3pz"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "eeRIZ6M0ZBu4kf03MUB9+g", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_buy_30", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 341}, "_children": [], "_active": true, "_components": [{"__id__": 547}, {"__id__": 549}, {"__id__": 551}], "_prefab": {"__id__": 553}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -622, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 546}, "_enabled": true, "__prefab": {"__id__": 548}, "_contentSize": {"__type__": "cc.Size", "width": 251, "height": 76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e1VLSWS0dBrKAfkkRXs6P8"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 546}, "_enabled": true, "__prefab": {"__id__": 550}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5206dad9-cf72-4cc5-83ef-6d7675fe2b35@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "42ghcNfIRLhYa6laBNI1WP"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 546}, "_enabled": true, "__prefab": {"__id__": 552}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "662VkKpqhNoIWrzp7OZInc"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c4yIHwRDlACKjwiZxDwdDs", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_yichongzhi_30", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 341}, "_children": [], "_active": false, "_components": [{"__id__": 555}, {"__id__": 557}], "_prefab": {"__id__": 559}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -618, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 554}, "_enabled": true, "__prefab": {"__id__": 556}, "_contentSize": {"__type__": "cc.Size", "width": 202, "height": 133}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "60pVdWKO5F1p2uph6jl9vJ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 554}, "_enabled": true, "__prefab": {"__id__": 558}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "76be6699-1688-4921-beae-e8074e36241f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f3OCkB5dJH0a227KILP1DV"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bc/9IcnhdEMKzDf/+sdHu7", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "pingzhi", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 341}, "_children": [{"__id__": 561}], "_active": true, "_components": [{"__id__": 569}, {"__id__": 571}, {"__id__": 573}], "_prefab": {"__id__": 575}, "_lpos": {"__type__": "cc.Vec3", "x": -276, "y": 293, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "hero_name", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 560}, "_children": [], "_active": true, "_components": [{"__id__": 562}, {"__id__": 564}, {"__id__": 566}], "_prefab": {"__id__": 568}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -30, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 561}, "_enabled": true, "__prefab": {"__id__": 563}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "441QJVBJBD5KZD5t9+7SZU"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 561}, "_enabled": true, "__prefab": {"__id__": 565}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 3, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dcct3g9B9AVIe58Wh/jRCF"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 561}, "_enabled": true, "__prefab": {"__id__": 567}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f81qr6lbdNUYK4KUNP3746"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "57HgdiOsND74FcTMP99zfS", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 560}, "_enabled": true, "__prefab": {"__id__": 570}, "_contentSize": {"__type__": "cc.Size", "width": 115, "height": 288}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b93JC5JKhLoZO6EClE58k2"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 560}, "_enabled": true, "__prefab": {"__id__": 572}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "334ccec8-bf96-4948-b6c1-0109251f4f32@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "87A2oeCnROko/s4bLND6DM"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 560}, "_enabled": true, "__prefab": {"__id__": 574}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e27vUME6VLWqaKkcGPYrGc"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f5AFFSelhFna2uhud1xyri", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 341}, "_children": [{"__id__": 577}], "_active": true, "_components": [{"__id__": 649}, {"__id__": 651}, {"__id__": 653}, {"__id__": 655}], "_prefab": {"__id__": 657}, "_lpos": {"__type__": "cc.Vec3", "x": 2, "y": -405, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "node_item_30_layout", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 576}, "_children": [{"__id__": 578}], "_active": true, "_components": [{"__id__": 644}, {"__id__": 646}], "_prefab": {"__id__": 648}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "node_item", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 577}, "_children": [{"__id__": 579}, {"__id__": 627}, {"__id__": 633}], "_active": true, "_components": [{"__id__": 639}, {"__id__": 641}], "_prefab": {"__id__": 643}, "_lpos": {"__type__": "cc.Vec3", "x": 67, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 578}, "_prefab": {"__id__": 580}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 579}, "asset": {"__uuid__": "3416dcab-ee56-478f-b062-b1e826f7b707", "__expectedType__": "cc.Prefab"}, "fileId": "f5VJJTfHtB5aqbwW4jl16A", "instance": {"__id__": 581}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "42MUj2Hg9Iw6/rte0GrQyS", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 582}, {"__id__": 584}, {"__id__": 585}, {"__id__": 586}, {"__id__": 587}, {"__id__": 589}, {"__id__": 591}, {"__id__": 592}, {"__id__": 593}, {"__id__": 594}, {"__id__": 596}, {"__id__": 598}, {"__id__": 599}, {"__id__": 600}, {"__id__": 601}, {"__id__": 603}, {"__id__": 605}, {"__id__": 607}, {"__id__": 609}, {"__id__": 610}, {"__id__": 612}, {"__id__": 614}, {"__id__": 615}, {"__id__": 617}, {"__id__": 619}, {"__id__": 620}, {"__id__": 622}, {"__id__": 623}, {"__id__": 625}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 583}, "propertyPath": ["_name"], "value": "<PERSON><PERSON>"}, {"__type__": "cc.TargetInfo", "localID": ["f5VJJTfHtB5aqbwW4jl16A"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 583}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 583}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 583}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 588}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 114, "height": 114}}, {"__type__": "cc.TargetInfo", "localID": ["7fVX9mvddJUaVADkQ2cBdp"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 590}, "propertyPath": ["_left"], "value": 0}, {"__type__": "cc.TargetInfo", "localID": ["d3SDf6g+VPSJ1nfJHYwdlU"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 590}, "propertyPath": ["_right"], "value": 0}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 590}, "propertyPath": ["_top"], "value": 0}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 590}, "propertyPath": ["_bottom"], "value": 0}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 595}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 114, "height": 114}}, {"__type__": "cc.TargetInfo", "localID": ["c3FKi/GdFIerw2SbrXGJ9M"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 597}, "propertyPath": ["_left"], "value": 0}, {"__type__": "cc.TargetInfo", "localID": ["0aBeGKL5ZEQYQ9QqfPUOsM"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 597}, "propertyPath": ["_right"], "value": 0}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 597}, "propertyPath": ["_top"], "value": 0}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 597}, "propertyPath": ["_bottom"], "value": 0}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 602}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 114, "height": 114}}, {"__type__": "cc.TargetInfo", "localID": ["76eqiBuHdJiZAdj+7P63VD"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 604}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 53, "y": -36.1, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["3f2CI9oKNLFK7+fzc6STXj"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 606}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 102, "height": 34}}, {"__type__": "cc.TargetInfo", "localID": ["07IRwvCO9Nyq7noluFiXK4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 608}, "propertyPath": ["_bottom"], "value": 3.8999999999999986}, {"__type__": "cc.TargetInfo", "localID": ["64bvRI4TpF94y4cA2dRx5b"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 583}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 611}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 101, "height": 101}}, {"__type__": "cc.TargetInfo", "localID": ["b8lliivRBLlLauY3fNUDkb"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 613}, "propertyPath": ["_sizeMode"], "value": 0}, {"__type__": "cc.TargetInfo", "localID": ["d1PcUCEbVCQ7t7NK/Q0EQM"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 583}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 616}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["83uRFCZDpOWIYKQdKjv3ks"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 618}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["eeCRBl0e1DboDDLjZnVY/4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 604}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 621}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"__type__": "cc.TargetInfo", "localID": ["05/dyc9JZPzqOEp1/yFyae"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 621}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 624}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["16SAIdCm9HC5vnRtxu66Bi"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 626}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["a1tsAamAJGy7Lb6gW02dNW"]}, {"__type__": "cc.Node", "_name": "icon_suo", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 578}, "_children": [], "_active": false, "_components": [{"__id__": 628}, {"__id__": 630}], "_prefab": {"__id__": 632}, "_lpos": {"__type__": "cc.Vec3", "x": 32, "y": 36, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 627}, "_enabled": true, "__prefab": {"__id__": 629}, "_contentSize": {"__type__": "cc.Size", "width": 43, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "18BTY/S0xAS6MUOEgESev6"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 627}, "_enabled": true, "__prefab": {"__id__": 631}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "33ea29d6-5c1c-4e07-8c6d-09c5154daa2c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9bFxdC0iRBELblbVgji+8L"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8b8KPQQTNM54bqHAdj/4ev", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "icon_duihao", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 578}, "_children": [], "_active": false, "_components": [{"__id__": 634}, {"__id__": 636}], "_prefab": {"__id__": 638}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 633}, "_enabled": true, "__prefab": {"__id__": 635}, "_contentSize": {"__type__": "cc.Size", "width": 79, "height": 62}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "09wPzZg1pHm4qp8FqQS3CZ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 633}, "_enabled": true, "__prefab": {"__id__": 637}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "a5798517-ec85-41b4-9388-a3fe54eba80c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3eVN4dh7lM3YlnEYaDNi+L"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2a/3U8CA1IWaIfouGMuN5I", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 578}, "_enabled": true, "__prefab": {"__id__": 640}, "_contentSize": {"__type__": "cc.Size", "width": 114, "height": 114}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f1sfRxR3JASYe6VUCOeadI"}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 578}, "_enabled": true, "__prefab": {"__id__": 642}, "playOnLoad": false, "_clips": [{"__uuid__": "04d0353f-53fd-4672-8ebb-723eab0ca991", "__expectedType__": "cc.AnimationClip"}], "_defaultClip": {"__uuid__": "04d0353f-53fd-4672-8ebb-723eab0ca991", "__expectedType__": "cc.AnimationClip"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cbrotT/HZMnKwynkXlhPF9"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b4RDK2r9NPRYn/kwST5knu", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 577}, "_enabled": true, "__prefab": {"__id__": 645}, "_contentSize": {"__type__": "cc.Size", "width": 124, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "32ao4eXtNE06pdAdvOu00s"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 577}, "_enabled": true, "__prefab": {"__id__": 647}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 10, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 33, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "70TN11on9LuaLX+oExqrDj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "739hgDazlIVp52zgNd9KCb", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 576}, "_enabled": true, "__prefab": {"__id__": 650}, "_contentSize": {"__type__": "cc.Size", "width": 640.45, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b486hDcCNMSJifAv3AKRmK"}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 576}, "_enabled": true, "__prefab": {"__id__": 652}, "bounceDuration": 1, "brake": 0.5, "elastic": true, "inertia": true, "horizontal": true, "vertical": false, "cancelInnerEvents": true, "scrollEvents": [], "_content": {"__id__": 577}, "_horizontalScrollBar": null, "_verticalScrollBar": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "09wjwP8bZEvqcaHVH0fz1U"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 576}, "_enabled": true, "__prefab": {"__id__": 654}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ebVm6VqbZDHrDTKBOYXgmr"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 576}, "_enabled": true, "__prefab": {"__id__": 656}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3br3elJTFH/JxcWwrqcOZi"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0aGxnSsTNAvI8SQTHViORK", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_day_switch_30", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 341}, "_children": [{"__id__": 659}, {"__id__": 701}, {"__id__": 743}], "_active": true, "_components": [{"__id__": 785}], "_prefab": {"__id__": 787}, "_lpos": {"__type__": "cc.Vec3", "x": -92, "y": -278, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "node_day_30_0", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 658}, "_children": [{"__id__": 660}, {"__id__": 672}], "_active": true, "_components": [{"__id__": 696}, {"__id__": 698}], "_prefab": {"__id__": 700}, "_lpos": {"__type__": "cc.Vec3", "x": -167, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_sc_tip_0", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 659}, "_children": [{"__id__": 661}], "_active": true, "_components": [{"__id__": 667}, {"__id__": 669}], "_prefab": {"__id__": 671}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "zi_sc_diyitian_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 660}, "_children": [], "_active": true, "_components": [{"__id__": 662}, {"__id__": 664}], "_prefab": {"__id__": 666}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 661}, "_enabled": true, "__prefab": {"__id__": 663}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "88L06YMndL/Li1B94UsOHR"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 661}, "_enabled": true, "__prefab": {"__id__": 665}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "731de326-7694-4a9f-81ae-7f4fbaef8aea@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "52h7OWioxCCJtzxwPy4tGL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f4iw5wI9tBqZ/aNAnIB/sr", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 660}, "_enabled": true, "__prefab": {"__id__": 668}, "_contentSize": {"__type__": "cc.Size", "width": 157, "height": 67}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f9yg6mt7xH3pNaLVlbg+II"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 660}, "_enabled": true, "__prefab": {"__id__": 670}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b64d1179-8f6d-4957-9fef-583f14677441@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eaxiA2Hg1JhrcIlD9xwLNR"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "15YyBuiGJEV77CysLRmwL+", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_select", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 659}, "_children": [{"__id__": 673}, {"__id__": 679}, {"__id__": 685}], "_active": true, "_components": [{"__id__": 691}, {"__id__": 693}], "_prefab": {"__id__": 695}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "zi_sc_diyitian_2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 672}, "_children": [], "_active": true, "_components": [{"__id__": 674}, {"__id__": 676}], "_prefab": {"__id__": 678}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 673}, "_enabled": true, "__prefab": {"__id__": 675}, "_contentSize": {"__type__": "cc.Size", "width": 104, "height": 36}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8efueMyYhF4bFo4P+gNzfu"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 673}, "_enabled": true, "__prefab": {"__id__": 677}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9ec802d8-462e-41ab-8757-2d2a3cf9573e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e05n1u/HJJyKDZyy6lnxJk"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "00HxxD6QRCs4go2A92tTPI", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_moquan_juhong", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 672}, "_children": [], "_active": true, "_components": [{"__id__": 680}, {"__id__": 682}], "_prefab": {"__id__": 684}, "_lpos": {"__type__": "cc.Vec3", "x": 44, "y": -8, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 679}, "_enabled": true, "__prefab": {"__id__": 681}, "_contentSize": {"__type__": "cc.Size", "width": 73, "height": 73}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1cbDh7DIBHc4JLea84Rtri"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 679}, "_enabled": true, "__prefab": {"__id__": 683}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ac31b390-a3a8-4d97-b22c-a8be45f77695@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "98GhdfscxM1ql8CUdxXYb6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "55hB2ItslJ7KYZ1lA037bi", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_lingqu_30_0", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 672}, "_children": [], "_active": false, "_components": [{"__id__": 686}, {"__id__": 688}], "_prefab": {"__id__": 690}, "_lpos": {"__type__": "cc.Vec3", "x": 259, "y": -127, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 685}, "_enabled": true, "__prefab": {"__id__": 687}, "_contentSize": {"__type__": "cc.Size", "width": 640.45, "height": 122}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "33oGVTAuNPW6MLGbot2vwC"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 685}, "_enabled": true, "__prefab": {"__id__": 689}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c0Fev4+71G6r/mmvi7RV0M"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "daIiuBTA9DkL/m7+1/IV8u", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 672}, "_enabled": true, "__prefab": {"__id__": 692}, "_contentSize": {"__type__": "cc.Size", "width": 157, "height": 68}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a2pwz3qzZDt4FeJe5A/c2P"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 672}, "_enabled": true, "__prefab": {"__id__": 694}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0360c806-e77c-429d-8b2c-9582855ade50@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "88idTIKmVPW6/+v5OkAUjy"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "884W57tWVPh4LjuHaHhgN+", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 659}, "_enabled": true, "__prefab": {"__id__": 697}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 87}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "65Z10OAklAupnJ+Q+T/1h8"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 659}, "_enabled": true, "__prefab": {"__id__": 699}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "74ejDVCw5FKJ0Az48VHLlh"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c7k6V+5c1L9bOuwj9Ha54L", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_day_30_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 658}, "_children": [{"__id__": 702}, {"__id__": 714}], "_active": true, "_components": [{"__id__": 738}, {"__id__": 740}], "_prefab": {"__id__": 742}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_sc_tip_0", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 701}, "_children": [{"__id__": 703}], "_active": true, "_components": [{"__id__": 709}, {"__id__": 711}], "_prefab": {"__id__": 713}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "zi_sc_diertian_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 702}, "_children": [], "_active": true, "_components": [{"__id__": 704}, {"__id__": 706}], "_prefab": {"__id__": 708}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 703}, "_enabled": true, "__prefab": {"__id__": 705}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "30K+p/1g1DE4A4UjED+oc1"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 703}, "_enabled": true, "__prefab": {"__id__": 707}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e906f6f2-01c2-4491-a948-4bd861a59d49@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "30XWzVGSBJ6ogS6fWJzR1l"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "98qx0q4KNF17u3dNLHN7/a", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 702}, "_enabled": true, "__prefab": {"__id__": 710}, "_contentSize": {"__type__": "cc.Size", "width": 157, "height": 67}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "14c9GHYvpMBb3TZLDHTlEM"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 702}, "_enabled": true, "__prefab": {"__id__": 712}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b64d1179-8f6d-4957-9fef-583f14677441@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "80G46IkIBBpIuh5qz5rnON"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "953UE3LZ1LQ4DHwLOb3Ly9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_select", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 701}, "_children": [{"__id__": 715}, {"__id__": 721}, {"__id__": 727}], "_active": false, "_components": [{"__id__": 733}, {"__id__": 735}], "_prefab": {"__id__": 737}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "zi_sc_diertian_2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 714}, "_children": [], "_active": true, "_components": [{"__id__": 716}, {"__id__": 718}], "_prefab": {"__id__": 720}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 715}, "_enabled": true, "__prefab": {"__id__": 717}, "_contentSize": {"__type__": "cc.Size", "width": 104, "height": 36}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ddVvz0qUBB6rNxFXHtqYZS"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 715}, "_enabled": true, "__prefab": {"__id__": 719}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "57d123c1-bffc-4fa6-8a73-8ed8dd545bbe@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "13ILyrkV1BZY/08M4ZUDv/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "28SEXA5M5I8KhMhfl7KVKK", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_moquan_juhong", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 714}, "_children": [], "_active": true, "_components": [{"__id__": 722}, {"__id__": 724}], "_prefab": {"__id__": 726}, "_lpos": {"__type__": "cc.Vec3", "x": 44, "y": -8, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 721}, "_enabled": true, "__prefab": {"__id__": 723}, "_contentSize": {"__type__": "cc.Size", "width": 73, "height": 73}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fahtFzTbFDPKrDwNjhGsSL"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 721}, "_enabled": true, "__prefab": {"__id__": 725}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ac31b390-a3a8-4d97-b22c-a8be45f77695@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b7V4QwSBRIZb2RssVCwg4T"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4fbrbh7WBN3o81/KAHvazS", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_lingqu_30_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 714}, "_children": [], "_active": false, "_components": [{"__id__": 728}, {"__id__": 730}], "_prefab": {"__id__": 732}, "_lpos": {"__type__": "cc.Vec3", "x": 92, "y": -127, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 727}, "_enabled": true, "__prefab": {"__id__": 729}, "_contentSize": {"__type__": "cc.Size", "width": 640.45, "height": 122}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "16dWNLn7ZMdqSb1fNDoVJD"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 727}, "_enabled": true, "__prefab": {"__id__": 731}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9eBXyPkHZONLYiZKKgj7v4"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ddTga/Fz5BCabSbCfd2hVl", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 714}, "_enabled": true, "__prefab": {"__id__": 734}, "_contentSize": {"__type__": "cc.Size", "width": 157, "height": 68}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a2MpXJOURLw6RGDGK4inZL"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 714}, "_enabled": true, "__prefab": {"__id__": 736}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0360c806-e77c-429d-8b2c-9582855ade50@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "08v2Zx+OdAt7W4476LAmFv"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "32mvufS6RIprSHcLKRhJXK", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 701}, "_enabled": true, "__prefab": {"__id__": 739}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 87}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dcYWk6HKVLfY+u8TO444Os"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 701}, "_enabled": true, "__prefab": {"__id__": 741}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "08OcNEqEFNNqP2Cr0ufHuv"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "039l3trFJPkq9mPKuINWOM", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_day_30_2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 658}, "_children": [{"__id__": 744}, {"__id__": 756}], "_active": true, "_components": [{"__id__": 780}, {"__id__": 782}], "_prefab": {"__id__": 784}, "_lpos": {"__type__": "cc.Vec3", "x": 168, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_sc_tip_0", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 743}, "_children": [{"__id__": 745}], "_active": true, "_components": [{"__id__": 751}, {"__id__": 753}], "_prefab": {"__id__": 755}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "zi_sc_disanitian_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 744}, "_children": [], "_active": true, "_components": [{"__id__": 746}, {"__id__": 748}], "_prefab": {"__id__": 750}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 745}, "_enabled": true, "__prefab": {"__id__": 747}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "73ZZa8PelDBbxkjklk9/rU"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 745}, "_enabled": true, "__prefab": {"__id__": 749}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "29076484-320c-41ff-9452-08eefc1b9b35@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "71XEMLDp5KnpySxSnaZKQO"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bc/cCMuoxCrqZtNwrNtOcc", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 744}, "_enabled": true, "__prefab": {"__id__": 752}, "_contentSize": {"__type__": "cc.Size", "width": 157, "height": 67}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "02XRMPlXxKF4BS7lzOhnp0"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 744}, "_enabled": true, "__prefab": {"__id__": 754}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b64d1179-8f6d-4957-9fef-583f14677441@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fbHet/1w9HG6jc0b4P2PSS"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f2SHrZa5pAVK2DTExj4p44", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_select", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 743}, "_children": [{"__id__": 757}, {"__id__": 763}, {"__id__": 769}], "_active": false, "_components": [{"__id__": 775}, {"__id__": 777}], "_prefab": {"__id__": 779}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "zi_sc_disantian_2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 756}, "_children": [], "_active": true, "_components": [{"__id__": 758}, {"__id__": 760}], "_prefab": {"__id__": 762}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 757}, "_enabled": true, "__prefab": {"__id__": 759}, "_contentSize": {"__type__": "cc.Size", "width": 104, "height": 36}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "darOJNP6BLYKD9rktxvG+k"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 757}, "_enabled": true, "__prefab": {"__id__": 761}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "10d2ef85-7d64-45dd-b086-8c2228e9bea7@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1ffWmen7dFN512UCz76k1t"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "09JyuCseNIoIzY27jVMseU", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_moquan_juhong", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 756}, "_children": [], "_active": true, "_components": [{"__id__": 764}, {"__id__": 766}], "_prefab": {"__id__": 768}, "_lpos": {"__type__": "cc.Vec3", "x": 44, "y": -8, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 763}, "_enabled": true, "__prefab": {"__id__": 765}, "_contentSize": {"__type__": "cc.Size", "width": 73, "height": 73}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b1gnE6oYZIEo4+oyW6bgJb"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 763}, "_enabled": true, "__prefab": {"__id__": 767}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ac31b390-a3a8-4d97-b22c-a8be45f77695@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "796mQcIqJATbZZRQqs1m8/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e01wEFj+lAyoXeQSLFPPfv", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_lingqu_30_2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 756}, "_children": [], "_active": false, "_components": [{"__id__": 770}, {"__id__": 772}], "_prefab": {"__id__": 774}, "_lpos": {"__type__": "cc.Vec3", "x": -76, "y": -127, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 769}, "_enabled": true, "__prefab": {"__id__": 771}, "_contentSize": {"__type__": "cc.Size", "width": 640.45, "height": 122}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d7BeQ8gMJFRZzmHQcBFMxA"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 769}, "_enabled": true, "__prefab": {"__id__": 773}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3ceDKA1TFLvpjrKwULvDSg"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "35rN97jb5Bx4uoSB/ku14U", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 756}, "_enabled": true, "__prefab": {"__id__": 776}, "_contentSize": {"__type__": "cc.Size", "width": 157, "height": 68}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ad6EywBt1NZ7NIfYKtwVHx"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 756}, "_enabled": true, "__prefab": {"__id__": 778}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0360c806-e77c-429d-8b2c-9582855ade50@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f1CP1XuCBNDqPV6M1cubND"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2cs2p3TVlOfrTQls0Whk8D", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 743}, "_enabled": true, "__prefab": {"__id__": 781}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 87}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "835Bz4TJBFZoSGJWu6EJa1"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 743}, "_enabled": true, "__prefab": {"__id__": 783}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5bzgAcfHJB2rXH2c8DtYhF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "13pLnUk+ZL6aX4V3vsR4kw", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 658}, "_enabled": true, "__prefab": {"__id__": 786}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d6c/hKefpL16E264bd3HCg"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "282ADzJ05LhaLBspSnSQKw", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "zi_shouchong", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 341}, "_children": [{"__id__": 789}, {"__id__": 795}], "_active": true, "_components": [{"__id__": 801}, {"__id__": 803}], "_prefab": {"__id__": 805}, "_lpos": {"__type__": "cc.Vec3", "x": -264, "y": 520, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 788}, "_children": [], "_active": true, "_components": [{"__id__": 790}, {"__id__": 792}], "_prefab": {"__id__": 794}, "_lpos": {"__type__": "cc.Vec3", "x": 138, "y": -33, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 789}, "_enabled": true, "__prefab": {"__id__": 791}, "_contentSize": {"__type__": "cc.Size", "width": 63, "height": 77.3}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b7jXFhZuhE6pLz4fAP225c"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 789}, "_enabled": true, "__prefab": {"__id__": 793}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "送", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 55, "_fontSize": 55, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 55, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 255, "g": 124, "b": 37, "a": 255}, "_outlineWidth": 4, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0coDtEl1RLRYjY3eunwMW/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0fqSIQjGxH4rFOAerVJclQ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 788}, "_children": [], "_active": true, "_components": [{"__id__": 796}, {"__id__": 798}], "_prefab": {"__id__": 800}, "_lpos": {"__type__": "cc.Vec3", "x": 307, "y": -29, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 795}, "_enabled": true, "__prefab": {"__id__": 797}, "_contentSize": {"__type__": "cc.Size", "width": 288, "height": 94.94}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "52GHLsYHNE/Zgux6sWQsOy"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 795}, "_enabled": true, "__prefab": {"__id__": 799}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 248, "b": 63, "a": 255}, "_string": "传奇战将", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 70, "_fontSize": 70, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 69, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 255, "g": 124, "b": 37, "a": 255}, "_outlineWidth": 4, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f9Tn3LRA9F64pFtBdxNEs7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "aczkZb2KFFebHUad2v+30x", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 788}, "_enabled": true, "__prefab": {"__id__": 802}, "_contentSize": {"__type__": "cc.Size", "width": 185, "height": 114}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a0dp1/h81NZ4YZcR0C0iaZ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 788}, "_enabled": true, "__prefab": {"__id__": 804}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "4131ef4a-8f48-4353-a410-3e3f486c17b5@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e8DMkH1ddNhYXOBfWo+BNg"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "27ovRRZIhE7rJr6Ft+I0Nf", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "zi_ch<PERSON><PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 341}, "_children": [{"__id__": 807}], "_active": true, "_components": [{"__id__": 813}, {"__id__": 815}], "_prefab": {"__id__": 817}, "_lpos": {"__type__": "cc.Vec3", "x": 223, "y": 354, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 806}, "_children": [], "_active": true, "_components": [{"__id__": 808}, {"__id__": 810}], "_prefab": {"__id__": 812}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 3, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 807}, "_enabled": true, "__prefab": {"__id__": 809}, "_contentSize": {"__type__": "cc.Size", "width": 256.79994201660156, "height": 89.16}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eenpKgu21JQIREd6uLhqjN"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 807}, "_enabled": true, "__prefab": {"__id__": 811}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 250, "b": 165, "a": 255}, "_string": "10000%", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 66, "_fontSize": 66, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 66, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 206, "g": 74, "b": 9, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "79zGpOdzFBZauZd4SahNV/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "31PvkgXVZL74NsKCO120OU", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 806}, "_enabled": true, "__prefab": {"__id__": 814}, "_contentSize": {"__type__": "cc.Size", "width": 244, "height": 197}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f6rYvWGRFHAZ+KJcv+HyDi"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 806}, "_enabled": true, "__prefab": {"__id__": 816}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "18bd5d8f-994c-48d3-ad8f-9e82e4645a37@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "df+i0bsN1FMIaWzPpXykR2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "16V3h5LbxPfI2gV8UiE7j4", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_sc6_jieshuo", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 341}, "_children": [{"__id__": 819}], "_active": true, "_components": [{"__id__": 825}], "_prefab": {"__id__": 827}, "_lpos": {"__type__": "cc.Vec3", "x": -3, "y": -154, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "RichText", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 818}, "_children": [], "_active": true, "_components": [{"__id__": 820}, {"__id__": 822}], "_prefab": {"__id__": 824}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -0.7, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 819}, "_enabled": true, "__prefab": {"__id__": 821}, "_contentSize": {"__type__": "cc.Size", "width": 371, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e5rCLf4r9D+rz9v59LL2ul"}, {"__type__": "cc.RichText", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 819}, "_enabled": true, "__prefab": {"__id__": 823}, "_lineHeight": 40, "_string": "<outline color=#d46b0e width=2><size=38><color=#fdd924>九</color></size>位<size=38><color=#fdd924>传奇战将</color></size>任选其一</outline>", "_horizontalAlign": 0, "_verticalAlign": 2, "_fontSize": 33, "_fontColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_maxWidth": 0, "_fontFamily": "<PERSON><PERSON>", "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_userDefinedFont": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_cacheMode": 0, "_imageAtlas": null, "_handleTouchEvent": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cb6AHArAtMkKsNgiCepXBH"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f2nPvbIqhCZK4d2K/B8v5f", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 818}, "_enabled": true, "__prefab": {"__id__": 826}, "_contentSize": {"__type__": "cc.Size", "width": 69.2999267578125, "height": 90.39999999999999}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1ekN05xfJJVpnzIulwB6SJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b9DqSIPA5NtYahCVO3X4Zt", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_next", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 341}, "_children": [], "_active": true, "_components": [{"__id__": 829}, {"__id__": 831}, {"__id__": 833}], "_prefab": {"__id__": 835}, "_lpos": {"__type__": "cc.Vec3", "x": 270, "y": 95, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": -1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 828}, "_enabled": true, "__prefab": {"__id__": 830}, "_contentSize": {"__type__": "cc.Size", "width": 90, "height": 110}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "67QvvFjK9E/on0DFhEahLA"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 828}, "_enabled": true, "__prefab": {"__id__": 832}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ab8d10d9-934d-48a5-8869-8482cb863493@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d5i2/zaFRP5KK+YYCTEZbZ"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 828}, "_enabled": true, "__prefab": {"__id__": 834}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c1/nPsnqpKVrqQ2Ws6VNR5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "31MnM9iR9C/q0IEcS4hf3w", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_last", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 341}, "_children": [], "_active": true, "_components": [{"__id__": 837}, {"__id__": 839}, {"__id__": 841}], "_prefab": {"__id__": 843}, "_lpos": {"__type__": "cc.Vec3", "x": -270, "y": 95, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 836}, "_enabled": true, "__prefab": {"__id__": 838}, "_contentSize": {"__type__": "cc.Size", "width": 90, "height": 110}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "48AZliQiBJ1I3TxEfWyAg8"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 836}, "_enabled": true, "__prefab": {"__id__": 840}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ab8d10d9-934d-48a5-8869-8482cb863493@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1b3hTqBeJHrL0bHBvwkOvp"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 836}, "_enabled": true, "__prefab": {"__id__": 842}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a3HZls4c9E6pRSacvUck0X"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dfNrk3pT5AvLUPeLAlH8mT", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 341}, "_enabled": true, "__prefab": {"__id__": 845}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aabATyaQhO67HVwyElHjTm"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 341}, "_enabled": true, "__prefab": {"__id__": 847}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b3wnqlHnxKe7YAP4eKNane"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f6PfDwnY9CI6PBD6bPUR/G", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_switch", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 850}, {"__id__": 856}, {"__id__": 864}], "_active": true, "_components": [{"__id__": 872}], "_prefab": {"__id__": 874}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -588, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_point", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 849}, "_children": [], "_active": true, "_components": [{"__id__": 851}, {"__id__": 853}], "_prefab": {"__id__": 855}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -82, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": -1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 850}, "_enabled": true, "__prefab": {"__id__": 852}, "_contentSize": {"__type__": "cc.Size", "width": 77, "height": 29}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fa4ksu6jxG8KI9k/i5J7NR"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 850}, "_enabled": true, "__prefab": {"__id__": 854}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "afa3d394-9e62-4863-93af-0fe0dac784b5@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "71G1KnnLFDq4Rphy2n/aH7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "477TtddGJNg6fIYRUB4JaY", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_arrow_right", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 849}, "_children": [], "_active": true, "_components": [{"__id__": 857}, {"__id__": 859}, {"__id__": 861}], "_prefab": {"__id__": 863}, "_lpos": {"__type__": "cc.Vec3", "x": 342, "y": 31, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1.2, "y": 1.2, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 856}, "_enabled": true, "__prefab": {"__id__": 858}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "baCkp4ei5C4alNkEW2q6eE"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 856}, "_enabled": true, "__prefab": {"__id__": 860}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "68781792-41c0-49b2-a84e-9f6233e8f290@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "05w3DZOzNBJLZ+PVSijMD5"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 856}, "_enabled": true, "__prefab": {"__id__": 862}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "baqwJEzp5HsK6DNswhjQZ8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "eeNh7OHD1NRo6EKNCMpGEq", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_arrow_left", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 849}, "_children": [], "_active": true, "_components": [{"__id__": 865}, {"__id__": 867}, {"__id__": 869}], "_prefab": {"__id__": 871}, "_lpos": {"__type__": "cc.Vec3", "x": -341, "y": 31, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": -1.2, "y": 1.2, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 864}, "_enabled": true, "__prefab": {"__id__": 866}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dc8T21hBlDeZiCtwP+QhUN"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 864}, "_enabled": true, "__prefab": {"__id__": 868}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "68781792-41c0-49b2-a84e-9f6233e8f290@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "08hik1SchDjIc39M7TyVj1"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 864}, "_enabled": true, "__prefab": {"__id__": 870}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8dTUvs+CFGxZUGbX8s3YJj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fe7DtsCIZAyL2EcS7aAyAV", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 849}, "_enabled": true, "__prefab": {"__id__": 873}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f578Tn3oJAQoGVYJB4vF3I"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e3Ql5JXERDSZcrpD2VGk2F", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "page_viewholder", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 876}, {"__id__": 880}], "_active": true, "_components": [{"__id__": 886}, {"__id__": 888}, {"__id__": 890}, {"__id__": 892}], "_prefab": {"__id__": 894}, "_lpos": {"__type__": "cc.Vec3", "x": -1050.694, "y": 155, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "hero_img", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 875}, "_children": [], "_active": true, "_components": [{"__id__": 877}], "_prefab": {"__id__": 879}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1.1, "y": 1.1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 876}, "_enabled": true, "__prefab": {"__id__": 878}, "_contentSize": {"__type__": "cc.Size", "width": 184.5, "height": 128.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "79yj/Tf4hCgKCWRJIOfIVs"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f50QAWjllPd7Jh9uHm8G8V", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 875}, "_children": [], "_active": false, "_components": [{"__id__": 881}, {"__id__": 883}], "_prefab": {"__id__": 885}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 880}, "_enabled": true, "__prefab": {"__id__": 882}, "_contentSize": {"__type__": "cc.Size", "width": 210.85671997070312, "height": 126}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ec9pvKtTVF24Kd2eF+SCv+"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 880}, "_enabled": true, "__prefab": {"__id__": 884}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "label", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 99.8, "_fontSize": 99.8, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 100, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ccJlzuWQhNxqvjesuRIk88"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a7Nbyegd1PqKLqM5UTcS2+", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 875}, "_enabled": true, "__prefab": {"__id__": 887}, "_contentSize": {"__type__": "cc.Size", "width": 688, "height": 790}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fcp2yWfVJFIaPxy6CF/vND"}, {"__type__": "14035bq8tdG36zkxv4WW7OP", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 875}, "_enabled": true, "__prefab": {"__id__": 889}, "lblPage": {"__id__": 883}, "heroImage": {"__id__": 876}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "46AJ5DhEtLfLlYUuMGlLPZ"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 875}, "_enabled": true, "__prefab": {"__id__": 891}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6b4yiUYptNZZ0jXdrQQIxm"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 875}, "_enabled": true, "__prefab": {"__id__": 893}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a3rtSIjL9Oy6/V0dGi2KuA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0bBqZ4TflIJ6GTlI6CtwL3", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_close", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 896}, {"__id__": 898}, {"__id__": 900}], "_prefab": {"__id__": 902}, "_lpos": {"__type__": "cc.Vec3", "x": 337, "y": 544, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 895}, "_enabled": true, "__prefab": {"__id__": 897}, "_contentSize": {"__type__": "cc.Size", "width": 78, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9dIBBJL9lHbrsCE/Nem7f5"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 895}, "_enabled": true, "__prefab": {"__id__": 899}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8d5aab16-760a-4075-aec2-264e4f26c6d7@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dbgLrMbytAa5uXSwkcV4K/"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 895}, "_enabled": true, "__prefab": {"__id__": 901}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "84ff+q+vZKuoG9pNhXI5MH"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "34WWpKybhP7KN81akAvFyn", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_item", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 904}, {"__id__": 952}, {"__id__": 958}], "_active": false, "_components": [{"__id__": 964}, {"__id__": 966}], "_prefab": {"__id__": 968}, "_lpos": {"__type__": "cc.Vec3", "x": -778, "y": -201, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 903}, "_prefab": {"__id__": 905}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 904}, "asset": {"__uuid__": "3416dcab-ee56-478f-b062-b1e826f7b707", "__expectedType__": "cc.Prefab"}, "fileId": "f5VJJTfHtB5aqbwW4jl16A", "instance": {"__id__": 906}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "78vU29Q5JKJa42Z/A1DHBP", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 907}, {"__id__": 909}, {"__id__": 910}, {"__id__": 911}, {"__id__": 912}, {"__id__": 914}, {"__id__": 916}, {"__id__": 917}, {"__id__": 918}, {"__id__": 919}, {"__id__": 921}, {"__id__": 923}, {"__id__": 924}, {"__id__": 925}, {"__id__": 926}, {"__id__": 928}, {"__id__": 930}, {"__id__": 932}, {"__id__": 934}, {"__id__": 935}, {"__id__": 937}, {"__id__": 939}, {"__id__": 940}, {"__id__": 942}, {"__id__": 944}, {"__id__": 945}, {"__id__": 947}, {"__id__": 948}, {"__id__": 950}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 908}, "propertyPath": ["_name"], "value": "<PERSON><PERSON>"}, {"__type__": "cc.TargetInfo", "localID": ["f5VJJTfHtB5aqbwW4jl16A"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 908}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 908}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 908}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 913}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 101, "height": 101}}, {"__type__": "cc.TargetInfo", "localID": ["7fVX9mvddJUaVADkQ2cBdp"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 915}, "propertyPath": ["_left"], "value": 0}, {"__type__": "cc.TargetInfo", "localID": ["d3SDf6g+VPSJ1nfJHYwdlU"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 915}, "propertyPath": ["_right"], "value": 0}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 915}, "propertyPath": ["_top"], "value": 0}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 915}, "propertyPath": ["_bottom"], "value": 0}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 920}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 114, "height": 114}}, {"__type__": "cc.TargetInfo", "localID": ["c3FKi/GdFIerw2SbrXGJ9M"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 922}, "propertyPath": ["_left"], "value": 0}, {"__type__": "cc.TargetInfo", "localID": ["0aBeGKL5ZEQYQ9QqfPUOsM"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 922}, "propertyPath": ["_right"], "value": 0}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 922}, "propertyPath": ["_top"], "value": 0}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 922}, "propertyPath": ["_bottom"], "value": 0}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 927}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 114, "height": 114}}, {"__type__": "cc.TargetInfo", "localID": ["76eqiBuHdJiZAdj+7P63VD"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 929}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 53, "y": -36.1, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["3f2CI9oKNLFK7+fzc6STXj"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 931}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 102, "height": 34}}, {"__type__": "cc.TargetInfo", "localID": ["07IRwvCO9Nyq7noluFiXK4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 933}, "propertyPath": ["_bottom"], "value": 3.8999999999999986}, {"__type__": "cc.TargetInfo", "localID": ["64bvRI4TpF94y4cA2dRx5b"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 908}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 936}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 101, "height": 101}}, {"__type__": "cc.TargetInfo", "localID": ["b8lliivRBLlLauY3fNUDkb"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 938}, "propertyPath": ["_sizeMode"], "value": 0}, {"__type__": "cc.TargetInfo", "localID": ["d1PcUCEbVCQ7t7NK/Q0EQM"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 908}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 941}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["83uRFCZDpOWIYKQdKjv3ks"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 943}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["eeCRBl0e1DboDDLjZnVY/4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 929}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 946}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"__type__": "cc.TargetInfo", "localID": ["05/dyc9JZPzqOEp1/yFyae"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 946}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 949}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["16SAIdCm9HC5vnRtxu66Bi"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 951}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["a1tsAamAJGy7Lb6gW02dNW"]}, {"__type__": "cc.Node", "_name": "icon_suo", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 903}, "_children": [], "_active": false, "_components": [{"__id__": 953}, {"__id__": 955}], "_prefab": {"__id__": 957}, "_lpos": {"__type__": "cc.Vec3", "x": 32, "y": 36, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 952}, "_enabled": true, "__prefab": {"__id__": 954}, "_contentSize": {"__type__": "cc.Size", "width": 43, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a0pENmG2hCbZEedQ99r4I+"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 952}, "_enabled": true, "__prefab": {"__id__": 956}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "33ea29d6-5c1c-4e07-8c6d-09c5154daa2c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "90199nhVRHQq8Co9Ep3weA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6eXrO+bJxOmLFb5PAmcW3/", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "icon_duihao", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 903}, "_children": [], "_active": false, "_components": [{"__id__": 959}, {"__id__": 961}], "_prefab": {"__id__": 963}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 958}, "_enabled": true, "__prefab": {"__id__": 960}, "_contentSize": {"__type__": "cc.Size", "width": 79, "height": 62}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8383/WASNJ97dwa4Y/ar4e"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 958}, "_enabled": true, "__prefab": {"__id__": 962}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "a5798517-ec85-41b4-9388-a3fe54eba80c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "74sqArUp1BioHkxpsX6oqz"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7baMk/PoxCfbuftxIMe2Dj", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 903}, "_enabled": true, "__prefab": {"__id__": 965}, "_contentSize": {"__type__": "cc.Size", "width": 114, "height": 114}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b2Ns+ohEBI7a3Y0UhgFy2V"}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 903}, "_enabled": true, "__prefab": {"__id__": 967}, "playOnLoad": false, "_clips": [{"__uuid__": "04d0353f-53fd-4672-8ebb-723eab0ca991", "__expectedType__": "cc.AnimationClip"}], "_defaultClip": {"__uuid__": "04d0353f-53fd-4672-8ebb-723eab0ca991", "__expectedType__": "cc.AnimationClip"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eaeP0muY5CRqM/2sgF25NJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "01lOzSDRlDUqY/MX3MbOF1", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 970}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1499.9999999999998}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c23XER97tDjqd4P7Fa7Clz"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 972}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f055HjNPRAOKGJBoOEobt8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": [{"__id__": 904}, {"__id__": 579}, {"__id__": 127}, {"__id__": 5}]}]