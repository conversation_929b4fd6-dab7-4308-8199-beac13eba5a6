{"skeleton": {"hash": "fnHmXR/+rorpfst4zZTIzt6DCt0", "spine": "3.8.75", "x": -348, "y": -663.3, "width": 730.86, "height": 1307.3, "images": "./images/", "audio": "E:/1/文件/PSD/动画拆分/小乔30106"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 207.04, "rotation": 179.53, "x": 596.75, "y": 23.2}, {"name": "bone2", "parent": "bone", "length": 118.98, "rotation": -89.53, "x": 571.98, "y": 55.92}, {"name": "bone3", "parent": "bone2", "length": 117.43, "rotation": 24.62, "x": 118.98}, {"name": "bone4", "parent": "bone3", "length": 78.96, "rotation": -25.43, "x": 117.43}, {"name": "bone5", "parent": "bone3", "x": 120.9, "y": 84.75}, {"name": "bone6", "parent": "bone3", "x": 75.33, "y": -56.79}, {"name": "bone10", "parent": "bone4", "x": 136.95, "y": -47.53}, {"name": "bone11", "parent": "bone4", "x": 105.34, "y": -39.78}, {"name": "bone12", "parent": "bone4", "x": 80.83, "y": -44.54, "color": "abe323ff"}, {"name": "bone23", "parent": "bone4", "x": 231.04, "y": -6.43}, {"name": "bone13", "parent": "bone23", "length": 45.62, "rotation": 98.49, "x": -21.8, "y": -34.67}, {"name": "bone14", "parent": "bone13", "length": 53.55, "rotation": 44.26, "x": 45.12, "y": 0.07}, {"name": "bone15", "parent": "bone14", "length": 51.94, "rotation": 34.14, "x": 53.64, "y": 0.71}, {"name": "bone16", "parent": "bone23", "length": 42.17, "rotation": -125.37, "x": -19.61, "y": -46.32}, {"name": "bone17", "parent": "bone16", "length": 50.76, "rotation": -35.75, "x": 42.17}, {"name": "bone18", "parent": "bone17", "length": 40.19, "rotation": -38.8, "x": 50.76}, {"name": "bone19", "parent": "bone23", "length": 34.6, "rotation": 131.04, "x": -29.97, "y": -31.73}, {"name": "bone20", "parent": "bone19", "length": 31.99, "rotation": 29.31, "x": 34.6}, {"name": "bone21", "parent": "bone23", "length": 33.67, "rotation": -167.88, "x": -33.37, "y": -43.47}, {"name": "bone22", "parent": "bone21", "length": 33.12, "rotation": -31.97, "x": 33.67}, {"name": "bone24", "parent": "bone23", "length": 93.58, "rotation": 154.06, "x": -43.17, "y": 75.07}, {"name": "bone25", "parent": "bone24", "length": 76.08, "rotation": -13.69, "x": 92.4, "y": -0.59}, {"name": "bone26", "parent": "bone25", "length": 71.02, "rotation": -26.08, "x": 76.08}, {"name": "bone27", "parent": "bone26", "length": 48.68, "rotation": -36.76, "x": 73.44, "y": -1.05}, {"name": "bone28", "parent": "bone23", "length": 65.33, "rotation": 154.5, "x": -125.36, "y": 69.96}, {"name": "bone29", "parent": "bone28", "length": 85.63, "rotation": -17.45, "x": 65.33}, {"name": "bone30", "parent": "bone29", "length": 80.24, "rotation": -15.68, "x": 85.63}, {"name": "bone31", "parent": "bone30", "length": 44.16, "rotation": -33.98, "x": 80.24}, {"name": "bone32", "parent": "bone23", "length": 60.81, "rotation": 145.05, "x": -164.77, "y": 18.08}, {"name": "bone33", "parent": "bone32", "length": 87.06, "rotation": -19.72, "x": 60.81}, {"name": "bone34", "parent": "bone33", "length": 78.18, "rotation": 1.57, "x": 86.89, "y": 0.92}, {"name": "bone35", "parent": "bone34", "length": 34.17, "rotation": 38.26, "x": 78.18}, {"name": "bone36", "parent": "bone4", "length": 79.28, "rotation": 179.11, "x": 167.16, "y": -99.53}, {"name": "bone37", "parent": "bone36", "length": 48.38, "rotation": -33.92, "x": 79.28}, {"name": "bone38", "parent": "bone37", "length": 39.33, "rotation": -16.66, "x": 48.38}, {"name": "bone39", "parent": "bone23", "length": 88.64, "rotation": 121.66, "x": -25.23, "y": 102.68, "color": "00ee85ff"}, {"name": "bone40", "parent": "bone39", "length": 67.38, "rotation": -0.11, "x": 88.64, "color": "00ee85ff"}, {"name": "bone41", "parent": "bone40", "length": 61.19, "rotation": 40.64, "x": 67.38, "color": "00ee85ff"}, {"name": "bone42", "parent": "bone41", "length": 34.38, "rotation": 103.23, "x": 61.19, "color": "00ee85ff"}, {"name": "bone43", "parent": "bone23", "length": 54.63, "rotation": 143.79, "x": -30.09, "y": 92.6, "color": "00ee85ff"}, {"name": "bone44", "parent": "bone43", "length": 46.01, "rotation": -20.03, "x": 54.63, "color": "00ee85ff"}, {"name": "bone45", "parent": "bone44", "length": 54.8, "rotation": -25.1, "x": 46.6, "y": 0.06, "color": "00ee85ff"}, {"name": "bone46", "parent": "bone23", "length": 56.03, "rotation": 51.21, "x": 42.99, "y": 7.12}, {"name": "bone47", "parent": "bone4", "length": 30, "rotation": -174.89, "x": 81.55, "y": 48.2, "color": "2bf379ff"}, {"name": "bone95", "parent": "bone47", "length": 50.32, "rotation": -27.8, "x": 28.66, "y": -0.48, "color": "2bf379ff"}, {"name": "bone48", "parent": "bone95", "length": 58.59, "rotation": 12.74, "x": 41.57, "y": 10.91, "color": "2bf379ff"}, {"name": "bone49", "parent": "bone48", "length": 50, "rotation": -7.6, "x": 58.59, "color": "2bf379ff"}, {"name": "bone50", "parent": "bone49", "length": 40, "rotation": 29.55, "x": 50.69, "color": "2bf379ff"}, {"name": "bone51", "parent": "bone", "length": 148.57, "rotation": 83.84, "x": 626.01, "y": 47.92}, {"name": "bone52", "parent": "bone51", "length": 130.74, "rotation": 22.37, "x": 148.57}, {"name": "bone53", "parent": "bone52", "length": 128.31, "rotation": 15.4, "x": 130.74}, {"name": "bone54", "parent": "bone53", "length": 168.27, "rotation": -19.37, "x": 128.31}, {"name": "bone55", "parent": "bone", "length": 128.44, "rotation": 108.1, "x": 567.51, "y": 65.74}, {"name": "bone56", "parent": "bone55", "length": 102.55, "rotation": 21.03, "x": 128.44}, {"name": "bone57", "parent": "bone56", "length": 102.14, "rotation": -23.07, "x": 102.55}, {"name": "bone58", "parent": "bone57", "length": 90.56, "rotation": 10.65, "x": 102.14}, {"name": "bone59", "parent": "bone58", "length": 131.84, "rotation": -10.64, "x": 90.56}, {"name": "bone64", "parent": "bone", "length": 103.65, "rotation": 138.88, "x": 476.32, "y": 210.37, "color": "ff7676ff"}, {"name": "bone65", "parent": "bone64", "length": 367.36, "rotation": -54.47, "x": 103.65, "color": "ff7676ff"}, {"name": "bone60", "parent": "bone", "length": 80.84, "rotation": 142.35, "x": 429.72, "y": 164.04, "color": "ff7676ff"}, {"name": "bone61", "parent": "bone60", "length": 420.98, "rotation": -34.95, "x": 77.09, "y": 1.39, "color": "ff7676ff"}, {"name": "bone62", "parent": "bone", "length": 100, "rotation": 140.75, "x": 533.95, "y": 81.62}, {"name": "bone63", "parent": "bone62", "length": 127.48, "rotation": -3.56, "x": 99.36, "y": 0.52}, {"name": "bone66", "parent": "bone63", "length": 115.82, "rotation": -23.34, "x": 127.48}, {"name": "bone67", "parent": "bone66", "length": 93.65, "rotation": 6.64, "x": 119.73, "y": 2.24}, {"name": "bone98", "parent": "bone", "x": 598.94, "y": 44.95, "color": "58ffb8ff"}, {"name": "bone68", "parent": "bone98", "length": 132.66, "rotation": 66.84, "x": 5.05, "y": 91.55, "color": "29ff8dff"}, {"name": "bone69", "parent": "bone68", "length": 146.45, "rotation": -11.16, "x": 132.66, "color": "29ff8dff"}, {"name": "bone70", "parent": "bone69", "length": 155.45, "rotation": -15.17, "x": 146.45, "color": "29ff8dff"}, {"name": "bone71", "parent": "bone70", "length": 94.54, "rotation": 29.58, "x": 153.51, "y": 1.63, "color": "29ff8dff"}, {"name": "bone72", "parent": "bone98", "length": 142.16, "rotation": 81.77, "x": -10.23, "y": 101.55, "color": "29ff8dff"}, {"name": "bone73", "parent": "bone72", "length": 160.56, "rotation": -19.7, "x": 142.16, "color": "29ff8dff"}, {"name": "bone74", "parent": "bone73", "length": 175.59, "rotation": 0.97, "x": 160.56, "color": "29ff8dff"}, {"name": "bone75", "parent": "bone74", "length": 196.77, "rotation": -19.44, "x": 175.59, "color": "29ff8dff"}, {"name": "bone76", "parent": "bone98", "length": 101.48, "rotation": 50.44, "x": 17.45, "y": 70.52, "color": "29ff8dff"}, {"name": "bone77", "parent": "bone76", "length": 90.46, "rotation": 26.79, "x": 101.48, "color": "29ff8dff"}, {"name": "bone78", "parent": "bone98", "length": 72.7, "rotation": -49.62, "x": -51.54, "y": 23.73, "color": "29ff8dff"}, {"name": "bone79", "parent": "bone3", "length": 99.85, "rotation": 16.82, "x": -90.25, "y": -132.04}, {"name": "bone80", "parent": "bone79", "length": 63.18, "rotation": 34.3, "x": 98.56, "y": -1.46}, {"name": "bone81", "parent": "bone80", "length": 48.11, "rotation": 8.58, "x": 63.18}, {"name": "bone82", "parent": "bone79", "length": 91.02, "rotation": 127.35, "x": 6.19, "y": -10.8}, {"name": "bone83", "parent": "bone82", "length": 70.11, "rotation": 25.06, "x": 91.02, "scaleX": 1.0975, "scaleY": 1.0975}, {"name": "bone84", "parent": "bone83", "length": 80.29, "rotation": 14.4, "x": 70.32, "y": 0.86, "scaleX": 1.0975, "scaleY": 1.0975}, {"name": "bone86", "parent": "root", "length": 132.28, "rotation": 121.92, "x": -168.77, "y": 43.33, "color": "143cd9ff"}, {"name": "bone87", "parent": "bone86", "length": 164, "rotation": 18.94, "x": 127.18, "y": -0.31, "color": "143cd9ff"}, {"name": "bone88", "parent": "root", "length": 100, "rotation": -55.91, "x": 217.82, "y": -51.26, "color": "143cd9ff"}, {"name": "bone89", "parent": "bone88", "length": 85, "rotation": 30.41, "x": 103.04, "y": 0.95, "color": "143cd9ff"}, {"name": "bone90", "parent": "root", "length": 59.85, "rotation": 39.61, "x": -318.67, "y": 507.76}, {"name": "bone91", "parent": "root", "x": -375.25, "y": 591.65}, {"name": "bone92", "parent": "root", "x": 397.34, "y": 317.71}, {"name": "bone93", "parent": "bone98", "length": 83.97, "rotation": 106.18, "x": -73.2, "y": 24.44, "color": "4effa5ff"}, {"name": "bone94", "parent": "bone93", "length": 64.19, "rotation": -11.19, "x": 83.97, "color": "4effa5ff"}, {"name": "bone97", "parent": "bone98", "rotation": 50.44, "x": 2.72, "y": 32.75, "color": "29ff8dff"}, {"name": "bone99", "parent": "bone3", "rotation": -114.62, "x": 54.28, "y": -16.98}, {"name": "bone100", "parent": "bone3", "rotation": -114.62, "x": -6.67, "y": -19.67, "color": "abe323ff"}, {"name": "bone85", "parent": "root", "x": -291.34, "y": -27.68}, {"name": "bone101", "parent": "root", "x": -212.75, "y": 11.08}, {"name": "bone102", "parent": "bone", "x": 572.85, "y": 442.84}, {"name": "bone103", "parent": "bone", "x": 483.34, "y": 504.51}, {"name": "bone104", "parent": "bone", "rotation": -179.53, "x": 489.73, "y": 350.08}, {"name": "bone105", "parent": "root", "x": 204.95, "y": 523.24}, {"name": "bone106", "parent": "bone", "x": 428.74, "y": -77.79}, {"name": "bone107", "parent": "bone", "x": 471.99, "y": 40.67}, {"name": "bone108", "parent": "bone", "x": 386.53, "y": 90.07}, {"name": "bone109", "parent": "bone", "x": 291.98, "y": 239.28}, {"name": "bone7", "parent": "bone3", "length": 190.54, "rotation": 149.08, "x": 110, "y": 95.2}, {"name": "bone8", "parent": "bone7", "length": 153.35, "rotation": -8.47, "x": 190.54}, {"name": "bone9", "parent": "bone8", "length": 118.26, "rotation": -9.46, "x": 153.35}, {"name": "bone96", "parent": "bone3", "x": -132.57, "y": 356.9}, {"name": "bone110", "parent": "bone3", "x": -194.53, "y": 469.12}, {"name": "bone111", "parent": "bone3", "length": 147.47, "rotation": 111.29, "x": -22.53, "y": 235.85}, {"name": "bone112", "parent": "bone9", "length": 24.54, "rotation": -11.95, "x": 87.13, "y": -5.06}, {"name": "bone113", "parent": "bone112", "length": 27.05, "rotation": 35.72, "x": 24.54}, {"name": "bone114", "parent": "bone", "x": 384.38, "y": 541.43}], "slots": [{"name": "bg", "bone": "root", "attachment": "bg"}, {"name": "bg1", "bone": "root", "attachment": "bg1"}, {"name": "yezi", "bone": "root", "attachment": "yezi"}, {"name": "piaodai2", "bone": "root", "attachment": "piaodai2"}, {"name": "p<PERSON><PERSON><PERSON>", "bone": "root", "attachment": "p<PERSON><PERSON><PERSON>"}, {"name": "huaban", "bone": "root", "attachment": "huaban"}, {"name": "huaban3", "bone": "root", "attachment": "huaban3"}, {"name": "huanan2", "bone": "root", "attachment": "huanan2"}, {"name": "toufa_hou", "bone": "root", "attachment": "toufa_hou"}, {"name": "toufa_you", "bone": "root", "attachment": "toufa_you"}, {"name": "toufa", "bone": "root", "attachment": "toufa"}, {"name": "shangshen", "bone": "root", "attachment": "shangshen"}, {"name": "shou_you", "bone": "root", "attachment": "shou_you"}, {"name": "qunzi", "bone": "root", "attachment": "qunzi"}, {"name": "tui_you", "bone": "root", "attachment": "tui_you"}, {"name": "tui_zuo", "bone": "root", "attachment": "tui_zuo"}, {"name": "shou_zuo", "bone": "root", "attachment": "shou_zuo"}, {"name": "ya<PERSON>i", "bone": "root", "attachment": "ya<PERSON>i"}, {"name": "yanjing", "bone": "root", "attachment": "yanjing"}, {"name": "lian", "bone": "root", "attachment": "lian"}, {"name": "liu<PERSON>", "bone": "root", "attachment": "liu<PERSON>"}, {"name": "shoude", "bone": "bone9", "attachment": "shoude"}, {"name": "shou", "bone": "root", "attachment": "shou"}], "transform": [{"name": "bone12", "bones": ["bone10"], "target": "bone12", "x": 56.12, "y": -2.99, "rotateMix": 0, "translateMix": -1, "scaleMix": 0, "shearMix": 0}, {"name": "bone100", "order": 1, "bones": ["bone99"], "target": "bone100", "x": -27.84, "y": 54.29, "rotateMix": -1, "translateMix": -1, "scaleMix": 0, "shearMix": 0}], "skins": [{"name": "default", "attachments": {"liuhai": {"liuhai": {"type": "mesh", "uvs": [0.29656, 0, 0.20406, 0.05666, 0.09548, 0.21405, 0.02912, 0.38248, 0, 0.56472, 0, 0.79942, 0.07738, 0.95681, 0.17792, 0.9789, 0.2282, 0.76352, 0.35287, 0.68897, 0.49162, 0.758, 0.65048, 0.78285, 0.74901, 0.73039, 0.75102, 0.84636, 0.73695, 1, 0.85358, 1, 0.96417, 0.95405, 1, 0.8436, 1, 0.68069, 0.98026, 0.47912, 0.93602, 0.33002, 0.87368, 0.16159, 0.78722, 0.02353, 0.6545, 0, 0.52379, 0, 0.3951, 0, 0.38504, 0.1036, 0.26238, 0.20024, 0.16586, 0.41009, 0.09548, 0.70278, 0.10352, 0.83255, 0.57205, 0.11188, 0.44537, 0.22785, 0.3207, 0.40181, 0.25434, 0.59785, 0.65048, 0.1533, 0.58814, 0.35211, 0.74901, 0.21129, 0.84754, 0.3742, 0.8757, 0.62547, 0.86564, 0.82151, 0.70477, 0.31897, 0.70879, 0.5233, 0.64445, 0.71106, 0.51374, 0.51226, 0.41722, 0.63651], "triangles": [14, 13, 15, 15, 40, 16, 15, 13, 40, 16, 40, 17, 13, 12, 40, 40, 18, 17, 40, 39, 18, 40, 12, 39, 12, 42, 39, 39, 19, 18, 42, 38, 39, 19, 38, 20, 19, 39, 38, 42, 41, 38, 38, 21, 20, 41, 37, 38, 38, 37, 21, 36, 31, 35, 41, 35, 37, 37, 22, 21, 37, 35, 22, 35, 23, 22, 35, 31, 23, 10, 43, 11, 11, 43, 12, 43, 10, 44, 43, 42, 12, 10, 45, 44, 43, 44, 42, 44, 36, 42, 36, 41, 42, 36, 35, 41, 44, 32, 36, 8, 34, 9, 9, 45, 10, 9, 34, 45, 34, 33, 45, 45, 33, 44, 33, 32, 44, 6, 30, 7, 7, 30, 8, 6, 5, 30, 5, 29, 30, 30, 29, 8, 5, 4, 29, 8, 29, 34, 29, 4, 28, 29, 28, 34, 28, 4, 3, 34, 28, 33, 3, 2, 28, 28, 27, 33, 28, 2, 27, 32, 33, 27, 2, 1, 27, 32, 27, 26, 32, 31, 36, 31, 32, 26, 31, 26, 24, 24, 26, 25, 27, 0, 26, 27, 1, 0, 31, 24, 23, 26, 0, 25], "vertices": [1, 11, 44.73, -26.45, 1, 2, 11, 63.15, -17.24, 0.77937, 12, 0.84, -24.98, 0.22063, 2, 11, 85.77, 0.99, 0.10407, 12, 29.76, -27.7, 0.89593, 3, 11, 100.88, 21.73, 0.00137, 12, 55.06, -23.4, 0.99579, 13, -12.36, -20.75, 0.00284, 2, 12, 79.76, -15.33, 0.46555, 13, 12.61, -27.94, 0.53445, 2, 12, 104.75, 3.76, 0.02979, 13, 44.01, -26.16, 0.97021, 1, 13, 63.81, -6.62, 1, 1, 13, 65.5, 12.04, 1, 2, 13, 36.07, 19.29, 0.79135, 18, 62.92, -9.6, 0.20865, 3, 13, 24.53, 41.49, 0.07777, 18, 45.54, 8.4, 0.81633, 20, 47.97, -38.28, 0.1059, 2, 18, 45.29, 35.55, 0.24829, 20, 47.62, -11.13, 0.75171, 3, 20, 40.42, 17.4, 0.88495, 15, 53.73, -47.47, 0.03048, 16, 32.06, -35.14, 0.08457, 3, 20, 27.45, 31.88, 0.35404, 15, 52.67, -28.06, 0.12649, 16, 19.07, -20.67, 0.51946, 3, 20, 41.86, 37.71, 0.03449, 15, 67.56, -32.53, 0.00683, 16, 33.47, -14.83, 0.95868, 1, 16, 53.64, -9.96, 1, 1, 16, 46.05, 10.11, 1, 1, 16, 33.09, 26.96, 1, 2, 15, 81.42, 11.14, 0.00116, 16, 16.91, 27.89, 0.99884, 2, 15, 60.67, 17.91, 0.32702, 16, -3.51, 20.16, 0.67298, 2, 15, 33.86, 22.84, 0.99622, 16, -27.48, 7.21, 0.00378, 2, 14, 64.63, 10.08, 0.01554, 15, 12.34, 21.3, 0.98446, 2, 14, 42.04, 21.52, 0.75539, 15, -12.67, 17.4, 0.24461, 1, 14, 18.28, 27.06, 1, 2, 11, -20.54, -17.65, 0.09476, 14, -5.65, 18.42, 0.90524, 2, 11, 3.3, -20.86, 0.8138, 14, -25.06, 4.22, 0.1862, 2, 11, 26.76, -24.03, 0.99989, 14, -44.18, -9.76, 0.00011, 1, 11, 30.99, -6.55, 1, 2, 11, 55.08, 3.26, 0.17315, 12, 9.37, -4.67, 0.82685, 2, 11, 76.44, 28.76, 0.00023, 12, 42.46, -1.31, 0.99977, 2, 12, 81.32, 12.68, 0.01863, 13, 29.62, -5.63, 0.98137, 2, 12, 94.1, 24.56, 0.00029, 13, 46.87, -2.97, 0.99971, 3, 11, -2.97, -0.86, 0.82423, 19, -13.82, -3.16, 0.00787, 14, -6.68, -5.86, 0.16791, 3, 11, 22.21, 11.43, 0.29422, 12, -8.48, 24.12, 0.02518, 17, 17.29, -6.55, 0.6806, 4, 12, 24.02, 20.44, 0.27663, 13, -13.45, 32.95, 0.01802, 18, 11.55, -10.59, 0.70196, 17, 49.86, -3.58, 0.0034, 3, 12, 52.23, 27.02, 0.01673, 13, 13.59, 22.57, 0.45898, 18, 40.43, -12.85, 0.52428, 4, 11, -16.53, 6.57, 0.00287, 17, -17.97, 10.2, 0.00055, 19, -5.54, 9.9, 0.0737, 14, 8.24, -1.83, 0.92287, 4, 18, -11.88, 33.19, 0.01424, 17, 7.99, 23.13, 0.13764, 19, 18.33, -6.57, 0.81212, 20, -9.54, -13.7, 0.036, 1, 14, 27.46, 2.6, 1, 1, 15, 12.92, 3.99, 1, 4, 19, 64.62, 38.13, 0, 20, 6.07, 48.73, 0.00572, 15, 46.54, -1.54, 0.92581, 16, -2.33, -3.85, 0.06847, 1, 16, 22.89, 3.72, 1, 4, 19, 18.18, 15.34, 0.48246, 20, -21.26, 4.81, 0.00083, 14, 29.41, -13.85, 0.40383, 15, -2.26, -18.69, 0.11289, 5, 19, 45.18, 10.7, 0.11665, 20, 4.09, 15.17, 0.56711, 14, 46.17, -35.51, 0.00356, 15, 23.99, -26.48, 0.27669, 16, -4.27, -37.41, 0.03598, 3, 20, 31.81, 12.96, 0.89207, 15, 44.24, -45.54, 0.03431, 16, 23.45, -39.58, 0.07362, 4, 18, 13.01, 27.86, 0.30267, 17, 32.31, 30.66, 0.09965, 19, 36.69, -24.2, 0.09663, 20, 15.37, -18.94, 0.50105, 5, 13, 16.71, 52.82, 0.00013, 18, 34.82, 17.04, 0.69876, 17, 56.62, 31.9, 0.00104, 19, 49.53, -44.89, 0.00098, 20, 37.22, -29.68, 0.29909], "hull": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 0, 50, 52, 54, 54, 56, 56, 58, 58, 60, 62, 64, 64, 66, 66, 68, 70, 72, 74, 76, 76, 78, 78, 80, 82, 84, 84, 86, 72, 88, 88, 90], "width": 134, "height": 97}}, "piaodai": {"piaodai": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [1, 85, -182.05, 21.3, 1, 2, 84, -227.64, -37.55, 0.31834, 85, 166.11, 214.32, 0.68166, 1, 84, 264.89, 43.79, 1, 2, 84, 504.86, -196.39, 0.27177, 85, -164.52, -95.43, 0.72823], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 173, "height": 164}}, "bg": {"bg": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [346, -644, -346, -644, -346, 644, 346, 644], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 302, "height": 563}}, "bg1": {"bg1": {"type": "mesh", "uvs": [0, 0.60864, 0.05267, 0.50505, 0.21149, 0.43277, 0.35922, 0.47855, 0.43678, 0.38459, 0.45894, 0.19908, 0.62145, 0.0497, 0.73779, 0.02485, 0.85414, 0, 1, 0.00634, 1, 0.17257, 0.96954, 0.28822, 0.93908, 0.40386, 0.86891, 0.5195, 0.79873, 0.63515, 0.69532, 0.69658, 0.5919, 0.75802, 0.37769, 0.84957, 0.17086, 0.94353, 0, 1, 0, 0.83993, 0.44544, 0.44827, 0.49691, 0.49101, 0.48287, 0.63294, 0.50627, 0.71382, 0.30506, 0.72756, 0.16702, 0.64973, 0.83005, 0.279, 0.29678, 0.55988, 0.65075, 0.36277, 0.73495, 0.17712, 0.72631, 0.47023, 0.20411, 0.80207, 0.67782, 0.5742, 0.62229, 0.2805], "triangles": [16, 24, 15, 24, 23, 15, 26, 0, 1, 25, 23, 24, 32, 26, 25, 32, 25, 17, 18, 32, 17, 8, 9, 10, 30, 6, 7, 30, 7, 8, 5, 6, 30, 8, 10, 30, 27, 30, 10, 34, 5, 30, 34, 30, 27, 11, 27, 10, 29, 34, 27, 4, 5, 34, 4, 34, 29, 12, 27, 11, 29, 27, 12, 21, 4, 29, 31, 29, 12, 22, 21, 29, 21, 3, 4, 31, 22, 29, 13, 31, 12, 33, 22, 31, 33, 31, 13, 22, 3, 21, 23, 22, 33, 14, 33, 13, 15, 33, 14, 23, 33, 15, 20, 26, 32, 18, 20, 32, 20, 0, 26, 19, 20, 18, 17, 24, 16, 17, 25, 24, 26, 28, 25, 23, 28, 22, 25, 28, 23, 28, 2, 3, 1, 2, 28, 22, 28, 3, 28, 26, 1], "vertices": [1, 97, -133.25, -52.89, 1, 1, 96, -43.5, 19.54, 1, 2, 96, -9.83, 43.03, 0.98812, 97, -88.42, 4.27, 0.01188, 1, 96, 21.49, 28.15, 1, 1, 97, -40.65, 19.93, 1, 1, 97, -35.96, 80.22, 1, 1, 97, -1.5, 128.76, 1, 1, 97, 23.16, 136.84, 1, 1, 97, 47.82, 144.92, 1, 1, 97, 78.75, 142.86, 1, 1, 97, 78.75, 88.83, 1, 1, 97, 72.29, 51.25, 1, 1, 97, 65.83, 13.66, 1, 2, 96, 129.54, 14.84, 0.02733, 97, 50.96, -23.92, 0.97267, 1, 97, 36.08, -61.51, 1, 2, 96, 92.74, -42.71, 0.032, 97, 14.16, -81.47, 0.968, 2, 96, 70.82, -62.68, 0.008, 97, -7.77, -101.44, 0.992, 1, 97, -53.18, -131.19, 1, 1, 97, -97.03, -161.73, 1, 1, 97, -133.25, -180.08, 1, 2, 96, -54.66, -89.3, 0.008, 97, -133.25, -128.06, 0.992, 1, 97, -38.82, -0.77, 1, 1, 97, -27.91, -14.66, 1, 1, 97, -30.88, -60.79, 1, 1, 97, -25.92, -87.08, 1, 1, 97, -68.58, -91.54, 1, 1, 97, -97.84, -66.24, 1, 1, 97, 42.72, 54.24, 1, 1, 96, 8.25, 1.72, 1, 1, 97, 4.71, 27.02, 1, 1, 97, 22.56, 87.35, 1, 2, 96, 99.31, 30.86, 0.01252, 97, 20.73, -7.91, 0.98748, 1, 97, -89.98, -115.76, 1, 1, 97, 10.45, -41.7, 1, 1, 97, -1.33, 53.75, 1], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 0], "width": 154, "height": 237}}, "piaodai2": {"piaodai2": {"type": "mesh", "uvs": [0.00579, 0.12486, 0.01368, 0, 0.28259, 0.14408, 0.44771, 0.32074, 0.65057, 0.17305, 0.82529, 0.15132, 1, 0.1296, 1, 0.34391, 1, 0.65958, 1, 0.96077, 0.97117, 0.93069, 0.92652, 0.88034, 0.82081, 0.78356, 0.85088, 0.85237, 0.85635, 0.93684, 0.86287, 1, 0.83084, 1, 0.58114, 0.90495, 0.47648, 0.83796, 0.44458, 0.75908, 0.39811, 0.68356, 0.33158, 0.60916, 0.24501, 0.55313, 0.21129, 0.50223, 0.13019, 0.453, 0.50083, 0.60458, 0.7184, 0.79385, 0.73603, 0.54328, 0.71805, 0.37428, 0.20432, 0.33752, 0.86391, 0.48072, 0.07392, 0.29944, 0.68594, 0.94467, 0.78709, 0.98271], "triangles": [16, 14, 15, 17, 32, 16, 32, 33, 16, 16, 33, 14, 14, 32, 13, 14, 33, 32, 10, 8, 9, 32, 26, 13, 26, 12, 13, 32, 17, 26, 10, 11, 8, 17, 18, 26, 8, 11, 12, 18, 19, 26, 27, 12, 26, 26, 25, 27, 12, 27, 8, 27, 30, 8, 30, 7, 8, 27, 28, 30, 30, 28, 7, 26, 19, 25, 19, 20, 25, 20, 21, 25, 21, 22, 25, 25, 28, 27, 25, 3, 28, 28, 5, 7, 28, 4, 5, 5, 6, 7, 25, 23, 3, 25, 22, 23, 24, 29, 23, 23, 29, 3, 0, 31, 24, 24, 31, 29, 3, 4, 28, 3, 29, 2, 29, 31, 2, 31, 0, 2, 2, 0, 1], "vertices": [1, 86, 5.15, -1.27, 1, 1, 86, -15.19, 13.68, 1, 1, 86, 27.6, 24.98, 1, 2, 86, 71.29, 21.96, 0.82667, 87, -44.21, 50, 0.17333, 2, 86, 69.53, 59.59, 0.404, 87, -26.68, 83.35, 0.596, 2, 86, 80.09, 80, 0.33182, 87, -7.23, 95.61, 0.66818, 2, 86, 89.42, 100.41, 0.30488, 87, 11.14, 108.49, 0.69512, 2, 86, 128.84, 76.18, 0.20615, 87, 32.87, 67.63, 0.79385, 2, 86, 195.33, 40.55, 3e-05, 87, 72.17, 3.25, 0.99997, 2, 86, 245.78, 6.45, 2e-05, 87, 98.42, -51.7, 0.99998, 2, 86, 238.74, 6.9, 2e-05, 87, 92.57, -47.75, 0.99998, 2, 86, 227.22, 8.01, 2e-05, 87, 83.2, -40.96, 0.99998, 2, 86, 203.52, 8.11, 0, 87, 62.81, -28.87, 1, 2, 86, 217.11, 3.41, 0, 87, 72.15, -39.81, 1, 2, 86, 231.65, -5.59, 0, 87, 80.13, -54.93, 1, 1, 87, 86.48, -66.17, 1, 1, 87, 82.83, -67.84, 1, 1, 87, 44.25, -62.48, 1, 1, 87, 19.64, -51.78, 1, 2, 86, 157.55, -27.88, 0.0038, 87, 4.95, -36.64, 0.9962, 2, 86, 132.7, -24.18, 0.08121, 87, -14.61, -20.87, 0.91879, 2, 86, 110.5, -22.63, 0.50372, 87, -32.97, -8.29, 0.49628, 2, 86, 93.52, -25.19, 0.94145, 87, -48.91, -1.91, 0.05855, 2, 86, 82.85, -22.89, 0.99945, 87, -56.95, 5.48, 0.00055, 1, 86, 68.69, -25.65, 1, 2, 86, 119.81, -4.74, 0.02894, 87, -15.89, 2.42, 0.97106, 2, 86, 198.08, -3.57, 0, 87, 52.21, -36.19, 1, 2, 86, 149.4, 26.55, 0.02064, 87, 25.48, 14.42, 0.97936, 2, 86, 109.49, 43.75, 0.3048, 87, -0.24, 49.46, 0.6952, 1, 86, 54.52, -4.96, 1, 2, 86, 147.88, 46.76, 0.07287, 87, 34.4, 32.62, 0.92713, 1, 86, 39.09, -14.04, 1, 1, 87, 60.42, -64.7, 1, 1, 87, 76, -66.78, 1], "hull": 25, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 2, 0, 0, 62, 62, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 64, 64, 66, 30, 32, 66, 32, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 32, 34, 48, 0], "width": 90, "height": 147}}, "shou_zuo": {"shou_zuo": {"type": "mesh", "uvs": [0.85021, 0, 0.92842, 0.03925, 1, 0.07558, 0.93624, 0.12784, 0.87591, 0.1801, 0.82283, 0.24444, 0.8083, 0.30435, 0.79015, 0.37387, 0.79816, 0.45794, 0.79656, 0.54931, 0.82859, 0.64434, 0.76865, 0.71659, 0.70938, 0.8171, 0.62448, 0.89385, 0.48512, 0.94777, 0.30391, 1, 0.09887, 1, 0, 0.98161, 0, 0.90212, 0, 0.80252, 0, 0.70201, 0, 0.60972, 0.13745, 0.5442, 0.27521, 0.4976, 0.38574, 0.44278, 0.48506, 0.37242, 0.53471, 0.27557, 0.57156, 0.18419, 0.59951, 0.1231, 0.66519, 0.05823, 0.78212, 0.03995, 0.84891, 0.08793, 0.75647, 0.10415, 0.68298, 0.20556, 0.64032, 0.30427, 0.57632, 0.42325, 0.46728, 0.49492, 0.3535, 0.57875, 0.1615, 0.65446, 0.67824, 0.43407, 0.62372, 0.53413, 0.50047, 0.63824, 0.39854, 0.74506, 0.18521, 0.79644, 0.53602, 0.79103, 0.34876, 0.88027, 0.08091, 0.91542], "triangles": [13, 44, 12, 10, 40, 9, 44, 41, 11, 12, 44, 11, 11, 41, 10, 13, 14, 44, 38, 37, 42, 38, 22, 37, 37, 36, 41, 22, 23, 37, 36, 23, 24, 36, 37, 23, 36, 24, 35, 20, 38, 43, 20, 21, 38, 21, 22, 38, 36, 35, 40, 24, 25, 35, 17, 46, 16, 15, 16, 46, 17, 18, 46, 46, 43, 45, 43, 46, 19, 46, 18, 19, 19, 20, 43, 43, 38, 42, 15, 45, 14, 45, 15, 46, 14, 45, 44, 45, 43, 42, 45, 42, 44, 42, 41, 44, 42, 37, 41, 40, 10, 41, 41, 36, 40, 9, 40, 8, 28, 29, 32, 27, 28, 33, 33, 32, 4, 33, 28, 32, 5, 33, 4, 40, 39, 8, 39, 7, 8, 7, 39, 34, 39, 35, 34, 7, 34, 6, 25, 26, 34, 6, 34, 5, 34, 33, 5, 34, 26, 33, 26, 27, 33, 40, 35, 39, 35, 25, 34, 1, 30, 0, 4, 31, 3, 32, 31, 4, 32, 30, 31, 32, 29, 30, 3, 31, 2, 2, 31, 1, 1, 31, 30], "vertices": [2, 3, 159.95, 28.83, 0.032, 4, 26.02, 44.3, 0.968, 4, 5, 4.8, -68.1, 0.26774, 3, 125.7, 16.65, 0.08, 4, 0.32, 18.59, 0.64768, 106, -53.83, 59.31, 0.00458, 2, 5, -23.62, -80.58, 0.94307, 106, -35.86, 84.63, 0.05693, 3, 5, -42, -49.44, 0.112, 3, 78.9, 35.32, 0.28416, 4, -49.96, 15.35, 0.60384, 4, 5, -60.84, -19.3, 0.10267, 3, 60.06, 65.45, 0.48, 4, -79.91, 34.48, 0.14976, 106, 27.56, 51.17, 0.26757, 4, 5, -86.89, 11.56, 0.27791, 3, 34.01, 96.32, 0.14528, 4, -116.7, 51.16, 0.25569, 106, 65.77, 38.09, 0.32113, 1, 106, 100.11, 37.14, 1, 2, 106, 140, 35.62, 0.99633, 107, -55.24, 27.79, 0.00367, 2, 106, 187.18, 43.44, 0.54311, 107, -9.73, 42.47, 0.45689, 1, 2, -63.4, 110.24, 1, 1, 2, -117.37, 99.86, 1, 1, 2, -158.41, 119.28, 1, 1, 2, -215.5, 138.48, 1, 3, 107, 244.04, 51.18, 0.00071, 108, 81.05, 65.38, 0.26329, 2, -259.1, 165.99, 0.736, 3, 108, 127.5, 36.77, 0.52782, 110, -89.03, -106.88, 0.00018, 2, -289.72, 211.14, 0.472, 3, 108, 178.65, -4.59, 0.59764, 110, -91.54, -41.15, 0.25836, 2, -319.39, 269.86, 0.144, 2, 108, 205.91, -65.17, 0.16292, 110, -63.86, 19.24, 0.83708, 2, 108, 211.17, -102.32, 0.03518, 110, -39.35, 47.65, 0.96482, 3, 109, -60.27, 141.05, 0.00118, 110, 1.7, 28.84, 0.99708, 111, 306.05, 63.54, 0.00174, 3, 109, -8.84, 117.48, 0.18037, 110, 53.13, 5.26, 0.6768, 111, 265.41, 24.18, 0.14282, 3, 109, 43.06, 93.69, 0.20784, 110, 105.02, -18.52, 0.24361, 111, 224.41, -15.55, 0.54854, 3, 109, 90.71, 71.85, 0.0305, 110, 152.68, -40.36, 0.05856, 111, 186.76, -52.02, 0.91094, 2, 110, 166.28, -99.99, 0.00016, 111, 126.26, -43.05, 0.99984, 1, 111, 76.19, -29.41, 1, 1, 111, 28.91, -25.36, 1, 2, 106, 150.02, -62.72, 0.35908, 111, -22.18, -30.05, 0.64092, 2, 106, 93.58, -52.76, 0.91781, 111, -72.89, -56.78, 0.08219, 2, 106, 40.68, -46.59, 0.99868, 111, -118.47, -84.31, 0.00132, 2, 5, 5.91, 48.62, 0.0742, 106, 5.2, -41.39, 0.9258, 4, 5, 30.54, 13.92, 0.58933, 3, 151.44, 98.68, 0.12288, 4, -11.66, 103.72, 0.04, 106, -33.76, -24.28, 0.24779, 3, 5, 24.19, -24.84, 0.592, 3, 145.09, 59.91, 0.05222, 4, -0.75, 65.98, 0.35578, 4, 5, -9.6, -33.16, 0.57303, 3, 111.3, 51.59, 0.26995, 4, -27.69, 43.96, 0.088, 106, -23.52, 36.74, 0.06902, 4, 5, -5.5, -2.09, 0.55005, 3, 115.4, 82.66, 0.18995, 4, -37.33, 73.78, 0.152, 106, -11.08, 7.98, 0.108, 2, 106, 48.79, -9.37, 0.99983, 111, -134.87, -49.94, 0.00017, 2, 106, 106.03, -16.96, 0.96267, 111, -84.99, -20.86, 0.03733, 3, 106, 175.48, -30.16, 0.45599, 107, -10.46, -32.06, 0.05902, 111, -22.02, 11.27, 0.48499, 3, 107, 37.91, -55.84, 0.14094, 109, 85.24, -96.58, 0.00054, 111, 31.8, 14.22, 0.85852, 3, 107, 93.35, -79.35, 0.10972, 109, 57.32, -43.23, 0.07137, 111, 91.65, 20.87, 0.81891, 3, 109, 44.14, 31.24, 0.17679, 110, 106.1, -80.98, 0.04296, 111, 165.82, 6.12, 0.78025, 1, 106, 177.96, 3.33, 1, 3, 107, 46.52, -1.15, 0.99444, 109, 43.88, -133.38, 1e-05, 111, 12.53, 66.12, 0.00554, 4, 107, 113.89, -24.69, 0.79248, 108, -34.87, -30.84, 0.00301, 109, 6.76, -72.44, 0.09738, 111, 82.79, 78.59, 0.10714, 4, 107, 180.97, -41.16, 0.02712, 108, 34.01, -36.06, 0.52906, 109, -34.64, -17.14, 0.43507, 110, 27.33, -129.36, 0.00875, 4, 108, 88.99, -87.11, 0.15632, 109, -32.37, 57.86, 0.39438, 110, 29.6, -54.36, 0.38883, 111, 218.4, 67.74, 0.06048, 3, 107, 194.87, 8.57, 0.00486, 108, 39.55, 15.28, 0.69115, 2, -200.69, 194.65, 0.304, 4, 108, 110.67, -19.25, 0.76171, 109, -97.73, 29.52, 0.0395, 110, -35.77, -82.7, 0.07879, 2, -251.38, 255.32, 0.12, 2, 108, 164.49, -90.19, 0.05336, 110, -17.76, 4.52, 0.94664], "hull": 31, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 0, 60, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 78, 80, 80, 82, 82, 84, 84, 86, 88, 90, 90, 92], "width": 236, "height": 414}}, "toufa": {"toufa": {"type": "mesh", "uvs": [0.84266, 0.16886, 0.88131, 0.14393, 0.8464, 0.11276, 0.81772, 0.06704, 0.7504, 0.02444, 0.6731, 0.01302, 0.57959, 0.01717, 0.50479, 0.05977, 0.47611, 0.12003, 0.49481, 0.17198, 0.47487, 0.2312, 0.43622, 0.28315, 0.35892, 0.30808, 0.27913, 0.33405, 0.18313, 0.34548, 0.10561, 0.36474, 0.08192, 0.40733, 0.02956, 0.44577, 0, 0.49149, 0, 0.54967, 0.06322, 0.56214, 0.1106, 0.56214, 0.17668, 0.5185, 0.15212, 0.48775, 0.15356, 0.44124, 0.23084, 0.40546, 0.3253, 0.37803, 0.40258, 0.34106, 0.477, 0.30528, 0.47026, 0.32666, 0.39325, 0.38259, 0.30777, 0.42557, 0.19685, 0.44618, 0.16647, 0.47444, 0.23029, 0.48526, 0.32846, 0.45621, 0.37706, 0.43612, 0.42933, 0.40501, 0.48366, 0.35313, 0.43241, 0.40933, 0.38098, 0.45035, 0.33005, 0.4879, 0.2839, 0.54967, 0.29387, 0.6172, 0.22655, 0.65461, 0.15299, 0.65357, 0.12307, 0.58707, 0.08192, 0.63071, 0.02083, 0.67539, 0.06198, 0.72721, 0.12307, 0.73864, 0.10312, 0.80409, 0.04078, 0.78123, 0.04951, 0.88305, 0.0919, 0.94123, 0.17418, 0.99526, 0.26146, 1, 0.34125, 0.99941, 0.40733, 0.9298, 0.48213, 0.86435, 0.52327, 0.80825, 0.60307, 0.775, 0.68286, 0.72201, 0.71741, 0.6344, 0.7037, 0.55648, 0.66131, 0.49726, 0.61518, 0.44739, 0.64136, 0.35908, 0.7012, 0.2957, 0.77351, 0.26973, 0.85081, 0.28323, 0.94183, 0.30609, 1, 0.34245, 1, 0.27388, 0.95305, 0.22609, 0.90692, 0.19388, 0.46841, 0.27427, 0.5185, 0.27188, 0.5414, 0.29335, 0.52566, 0.34941, 0.51994, 0.44005, 0.55858, 0.51757, 0.61869, 0.54977, 0.08343, 0.5128, 0.4744, 0.47911, 0.42445, 0.58317, 0.32767, 0.69375, 0.22308, 0.72497, 0.15283, 0.83554, 0.25742, 0.78611, 0.34484, 0.74448, 0.44943, 0.66123, 0.51031, 0.58317, 0.62426, 0.6248, 0.5587, 0.70416, 0.44162, 0.79782, 0.28396, 0.87587, 0.21995, 0.95262, 0.77436, 0.15086, 0.69421, 0.16353, 0.54359, 0.11517, 0.63894, 0.09905, 0.7177, 0.09789, 0.78126, 0.11171], "triangles": [78, 99, 68, 71, 73, 72, 71, 74, 73, 71, 70, 74, 68, 99, 69, 69, 0, 70, 70, 75, 74, 70, 0, 75, 69, 98, 0, 55, 97, 56, 57, 56, 96, 56, 97, 96, 55, 54, 97, 57, 96, 58, 97, 88, 96, 59, 58, 95, 58, 96, 95, 96, 90, 95, 59, 95, 60, 61, 60, 94, 60, 95, 94, 95, 91, 94, 61, 94, 62, 94, 93, 62, 62, 93, 63, 93, 64, 63, 93, 82, 64, 54, 88, 97, 54, 53, 88, 53, 51, 88, 53, 52, 51, 51, 50, 88, 88, 50, 87, 88, 89, 96, 96, 89, 90, 88, 87, 89, 89, 87, 90, 95, 90, 91, 87, 86, 90, 90, 86, 91, 91, 92, 94, 91, 86, 85, 91, 85, 92, 94, 92, 93, 92, 82, 93, 92, 81, 82, 81, 92, 84, 92, 85, 84, 82, 65, 64, 82, 81, 65, 81, 66, 65, 81, 80, 66, 87, 50, 45, 50, 49, 45, 49, 47, 45, 49, 48, 47, 47, 46, 45, 87, 44, 86, 87, 45, 44, 44, 43, 86, 86, 43, 85, 43, 42, 85, 42, 41, 85, 85, 41, 84, 84, 41, 40, 84, 80, 81, 40, 39, 84, 84, 39, 80, 67, 66, 79, 39, 38, 80, 66, 80, 79, 80, 38, 79, 79, 78, 67, 67, 78, 68, 38, 30, 29, 38, 29, 79, 29, 28, 79, 79, 28, 78, 28, 77, 78, 99, 78, 77, 35, 31, 36, 36, 30, 37, 36, 31, 30, 38, 37, 30, 33, 32, 34, 34, 31, 35, 34, 32, 31, 20, 83, 21, 22, 83, 23, 22, 21, 83, 20, 19, 83, 19, 18, 83, 23, 83, 24, 18, 17, 83, 16, 24, 83, 16, 83, 17, 16, 15, 24, 25, 13, 26, 24, 14, 25, 24, 15, 14, 25, 14, 13, 26, 12, 27, 26, 13, 12, 27, 12, 11, 28, 27, 11, 11, 76, 28, 28, 76, 77, 10, 76, 11, 76, 10, 77, 10, 9, 77, 100, 99, 9, 100, 101, 99, 99, 77, 9, 69, 99, 98, 9, 8, 100, 0, 98, 2, 0, 2, 1, 2, 98, 103, 99, 102, 98, 99, 101, 102, 98, 102, 103, 8, 7, 100, 101, 100, 6, 103, 3, 2, 3, 103, 4, 100, 7, 6, 101, 5, 102, 101, 6, 5, 103, 102, 4, 102, 5, 4], "vertices": [2, 43, -38.84, -12.46, 0.48428, 10, 28.37, -30.95, 0.51572, 2, 43, -42.96, -30.82, 0.70948, 10, 40.1, -45.67, 0.29052, 2, 43, -23.42, -33.35, 0.82981, 10, 54.31, -32.03, 0.17019, 2, 43, -1.46, -42.59, 0.97673, 10, 75.27, -20.69, 0.02327, 1, 43, 31.06, -41.23, 1, 1, 43, 57.36, -26.33, 1, 3, 43, 83.87, -1.91, 0.96898, 36, -71.51, -103.99, 0.02777, 40, -115.13, -57.91, 0.00325, 3, 43, 93.52, 31.61, 0.8332, 36, -37.82, -94.94, 0.14129, 40, -82.08, -69.06, 0.02551, 4, 43, 84.28, 60.1, 0.68225, 36, -18.19, -72.32, 0.25719, 40, -53.21, -61.11, 0.05435, 10, 48.94, 110.47, 0.00621, 5, 43, 63.44, 74.01, 0.45291, 36, -17.05, -47.29, 0.4189, 40, -38.38, -40.91, 0.09873, 21, -49.16, -54.9, 0.00564, 10, 25.05, 102.94, 0.02382, 4, 43, 51.92, 99.99, 0.115, 36, -0.76, -24.01, 0.79723, 40, -11.91, -30.57, 0.07636, 10, -2.42, 110.23, 0.0114, 3, 43, 48.09, 127.96, 0.00034, 36, 21.22, -6.28, 0.99966, 21, 6.86, -51.93, 0, 1, 36, 53.12, -5.25, 1, 2, 36, 86.08, -4.09, 0.66176, 37, -2.56, -4.09, 0.33824, 1, 37, 34.08, -11.26, 1, 2, 37, 65.19, -12.68, 0.88951, 38, -9.92, -8.19, 0.11049, 1, 38, 11.68, -6.19, 1, 1, 38, 37.16, -14.7, 1, 3, 38, 61.14, -13.93, 0.77953, 39, -13.55, 3.24, 0.22047, 21, 168.4, -158.59, 0, 3, 38, 84.38, -0.42, 0.13627, 39, -5.72, -22.47, 0.86373, 21, 192.4, -146.49, 0, 2, 39, 19.24, -20.9, 1, 21, 186.59, -122.17, 0, 2, 39, 36.69, -15.59, 1, 21, 178.38, -105.88, 0, 2, 38, 37.76, 51.15, 0.03274, 39, 55.16, 11.11, 0.96726, 2, 38, 30.22, 35.84, 0.24366, 39, 41.98, 21.95, 0.75634, 4, 37, 59.39, 26.77, 0.32624, 38, 11.36, 25.52, 0.58513, 39, 36.25, 42.66, 0.08863, 21, 121.06, -116.24, 0, 3, 36, 114.53, 20.9, 0.01327, 37, 25.85, 20.95, 0.98673, 21, 92.91, -97.11, 0, 6, 36, 76.02, 20.96, 0.5641, 37, -12.66, 20.94, 0.06866, 42, -1.14, -29.21, 0.008, 41, 33.18, -25.91, 0.35427, 40, 76.92, -35.7, 0.00497, 21, 65.22, -70.34, 0, 3, 36, 42.29, 14.68, 0.60284, 41, -1.08, -24.06, 0.12573, 40, 45.37, -22.23, 0.27143, 2, 36, 9.79, 8.56, 0.53061, 40, 14.92, -9.31, 0.46939, 4, 36, 15.5, 17.03, 0.35088, 41, -26.56, -15.46, 0.01538, 40, 24.37, -5.43, 0.55448, 21, 18.91, -31.18, 0.07926, 6, 36, 52.03, 31.61, 0.28262, 37, -36.67, 31.54, 0.01759, 42, -26.77, -23.54, 0.0337, 41, 12.37, -9.91, 0.57295, 40, 62.85, -13.54, 0.09314, 21, 55.33, -46.03, 0, 6, 36, 89.65, 39.46, 0.13178, 37, 0.94, 39.46, 0.11755, 42, 8.54, -8.37, 0.54584, 41, 50.79, -11.14, 0.20369, 40, 98.52, -27.87, 0.00115, 21, 87.88, -66.48, 0, 6, 36, 133.1, 34.32, 0.00167, 37, 44.4, 34.41, 0.19148, 38, 4.96, 41.08, 0.12094, 39, 52.86, 45.33, 0.02775, 42, 52.15, -4.78, 0.65817, 21, 115.59, -100.33, 0, 6, 36, 148.46, 42.78, 0.00053, 37, 59.74, 42.89, 0.06132, 38, 22.13, 37.53, 0.1231, 39, 45.47, 29.44, 0.2708, 42, 65.52, 6.56, 0.54424, 21, 132.52, -104.9, 0, 2, 39, 70.44, 31.81, 0.03003, 42, 41.86, 14.87, 0.96997, 2, 42, 2.58, 6.74, 0.77308, 41, 51.8, 5.07, 0.22692, 2, 41, 31.05, 7.46, 1, 21, 80.22, -40.47, 0, 2, 41, 6.35, 6.34, 0.93855, 40, 62.76, 3.78, 0.06145, 2, 40, 31.03, 6.05, 0.77698, 21, 27.52, -21.07, 0.22302, 3, 40, 63.64, 5.93, 0.05009, 21, 59.58, -27.01, 0.85078, 22, -25.64, -33.43, 0.09913, 3, 40, 90.69, 1.53, 0.00158, 21, 85.41, -36.16, 0.45356, 22, 1.63, -36.21, 0.54486, 2, 21, 109.73, -45.86, 0.10034, 22, 27.55, -39.88, 0.89966, 3, 21, 143.21, -48.89, 0.00036, 22, 60.8, -34.89, 0.9651, 23, 1.62, -38.06, 0.03455, 2, 22, 82.05, -11.73, 0.16962, 23, 10.52, -7.91, 0.83038, 2, 22, 112.02, -20.25, 0, 23, 41.18, -2.39, 1, 2, 23, 66.97, -14.11, 0.20703, 24, 2.63, -14.34, 0.79297, 1, 24, 20.91, -41.59, 1, 1, 24, 31.69, -18.33, 1, 1, 24, 49.84, 7.17, 1, 3, 24, 28.92, 26.83, 0.96239, 27, 86.63, -68.29, 0.02133, 28, 43.47, -53.05, 0.01628, 4, 23, 93.19, 17.35, 0.03301, 24, 4.81, 26.56, 0.67736, 27, 69.07, -51.78, 0.17975, 28, 19.68, -49.18, 0.10989, 5, 23, 112.28, 42.03, 0.00689, 24, 5.34, 57.76, 0.08451, 27, 91.06, -29.65, 0.11853, 28, 25.54, -18.54, 0.79003, 32, -25.69, -72.84, 4e-05, 3, 24, 31.12, 52.99, 0.00459, 28, 50.13, -27.65, 0.99438, 32, -29.38, -98.8, 0.00102, 4, 24, 17.05, 98, 0.00017, 27, 127.38, -8.73, 3e-05, 28, 43.97, 19.11, 0.93704, 32, 15.01, -82.88, 0.06276, 3, 24, -5.01, 120.41, 6e-05, 28, 26.08, 44.97, 0.65772, 32, 36.49, -59.91, 0.34223, 3, 24, -41.58, 137.43, 1e-05, 28, -7.03, 67.99, 0.18675, 32, 51.98, -22.68, 0.81324, 2, 31, 107.18, 35.95, 0.03838, 32, 45.03, 10.27, 0.96162, 2, 31, 82.2, 53.83, 0.32456, 32, 36.49, 39.78, 0.67544, 3, 30, 128.4, 44.9, 0.00113, 31, 42.7, 42.83, 0.82765, 32, -1.34, 55.61, 0.17121, 3, 30, 87.53, 36.31, 0.33386, 31, 1.61, 35.37, 0.66407, 32, -38.22, 75.19, 0.00207, 2, 30, 59.79, 23.94, 0.95156, 31, -26.46, 23.76, 0.04844, 2, 29, 94.76, 18.32, 0.01382, 30, 25.77, 28.7, 0.98618, 3, 25, 74.15, 83.01, 0, 29, 56.94, 28.95, 0.71939, 30, -13.41, 25.95, 0.28061, 2, 25, 31.98, 76.99, 0.00027, 29, 16.32, 16.09, 0.99973, 2, 25, 2.04, 56.31, 0.20599, 29, -9.81, -9.23, 0.79401, 3, 21, 56.2, 69.97, 0.00934, 25, -15.25, 29.55, 0.73953, 29, -22.48, -38.46, 0.25113, 4, 21, 43.62, 43.74, 0.39058, 25, -28.03, 3.42, 0.59688, 29, -30.8, -66.33, 0.01223, 10, -101.53, 54.81, 0.00031, 3, 21, 2.64, 34.39, 0.86517, 25, -69.07, -5.63, 0.0471, 10, -60.59, 45.3, 0.08774, 6, 43, -34.22, 67.41, 0.01007, 36, -73.14, 32.93, 0.00304, 40, -40.58, 56.95, 0.00887, 21, -33.87, 41.78, 0.42198, 25, -105.53, 2.05, 0.00042, 10, -30.99, 22.68, 0.55562, 3, 40, -66.93, 71.95, 0.0001, 21, -57.12, 61.25, 0.04233, 10, -18.6, -4.99, 0.95756, 1, 10, -24.42, -34.83, 1, 1, 10, -34.49, -70.02, 1, 1, 10, -50.97, -92.65, 1, 1, 10, -19.29, -92.2, 1, 2, 43, -88.44, -19.17, 0.01273, 10, 2.53, -73.82, 0.98727, 2, 43, -65.27, -19.32, 0.08398, 10, 17.16, -55.85, 0.91602, 3, 43, 41.15, 116.9, 0.0096, 36, 8.17, -6.05, 0.99006, 10, -22.35, 112.43, 0.00034, 5, 43, 26.99, 103.76, 0.02902, 36, -10.4, -0.72, 0.44201, 40, -7.02, -5.84, 0.5161, 21, -12.04, -25.98, 0.00248, 10, -20.98, 93.17, 0.0104, 4, 43, 13.88, 105.78, 0.00773, 40, -4.41, 7.17, 0.7277, 21, -7.15, -13.65, 0.25366, 10, -30.77, 84.21, 0.01091, 2, 40, 19.92, 17.93, 0.26113, 21, 18.7, -7.4, 0.73887, 2, 21, 57.09, 9.47, 0.86691, 25, -14.82, -30.95, 0.13309, 3, 21, 82.38, 38.88, 0.01887, 22, -19.09, 35.97, 0.00342, 25, 10.69, -1.75, 0.97772, 2, 25, 13.77, 25.59, 0.67404, 29, 6.8, -37.6, 0.32596, 3, 38, 53.51, 18.79, 0.09127, 39, 20.05, 3.18, 0.89827, 42, 99.61, 19.75, 0.01046, 2, 21, 81.09, 1.94, 0.96531, 25, 9.12, -38.67, 0.03469, 3, 22, 37.48, 16.33, 0.68924, 25, 60.75, -34.6, 0.1459, 26, 6, -34.39, 0.16486, 4, 22, 100.53, 21.11, 0.01023, 23, 12.68, 29.71, 0.41213, 26, 68.67, -25.97, 0.4548, 27, -9.31, -29.59, 0.12284, 5, 23, 55.36, 26.9, 0.57802, 24, -31.21, 11.57, 0.02074, 26, 106.94, -45.08, 0.0173, 27, 32.7, -37.64, 0.37305, 28, -18.38, -57.78, 0.0109, 4, 23, 100.52, 62.98, 0.00328, 24, -16.62, 67.5, 0.01505, 27, 81.96, -7.4, 0.26584, 28, 5.57, -5.17, 0.71583, 5, 23, 54.48, 58.07, 0.0616, 24, -50.57, 36.02, 0.00511, 26, 118.2, -16, 0.00137, 27, 35.68, -6.6, 0.93023, 28, -33.26, -30.37, 0.0017, 3, 23, 15.95, 53.84, 0.04192, 26, 81.03, -4.99, 0.72205, 27, -3.08, -6.04, 0.23603, 4, 22, 58.69, 47.04, 0.02018, 23, -36.3, 34.6, 0.00058, 25, 88.81, -10, 0.00215, 26, 25.4, -2.5, 0.97709, 3, 21, 117.81, 35.92, 0.00101, 22, 16.04, 41.49, 0.06167, 25, 46.1, -4.97, 0.93732, 4, 25, 43.89, 42.88, 0.23604, 26, -33.31, 34.48, 0.00206, 29, 33.68, -15.6, 0.74857, 30, -20.28, -23.84, 0.01333, 4, 25, 87.94, 36.5, 0.03083, 26, 10.63, 41.6, 0.07966, 29, 78.18, -14.66, 0.02823, 30, 21.29, -7.95, 0.86128, 2, 30, 82.96, 2.15, 0.73991, 31, -3.9, 1.34, 0.26009, 2, 27, 47.97, 34.31, 0.08992, 31, 66.39, -5.28, 0.91008, 4, 24, -54.21, 114.2, 0, 27, 87.21, 52.31, 0.01459, 28, -23.45, 47.28, 0.07825, 32, 28.26, -11.02, 0.90716, 2, 43, -13.27, -2.11, 0.81045, 10, 36.31, -4.54, 0.18955, 5, 43, 6.77, 22.07, 0.71431, 36, -90.79, -25.59, 0.02788, 40, -87.72, 18.03, 0.02209, 21, -87.19, 11.9, 0.03481, 10, 30.03, 26.23, 0.20092, 5, 43, 65.7, 41.81, 0.73904, 36, -43.46, -65.85, 0.19752, 40, -70.64, -41.72, 0.04812, 21, -81.05, -49.94, 0.00331, 10, 51.55, 84.53, 0.01201, 5, 43, 42.16, 12.68, 0.94014, 36, -80.56, -60.74, 0.03157, 40, -98.69, -16.9, 0.0119, 21, -104.22, -20.51, 0.00461, 10, 59.52, 47.93, 0.01179, 1, 43, 19.13, -7.06, 1, 2, 43, -3.79, -17.74, 0.95124, 10, 54.44, -6.94, 0.04876], "hull": 76, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 68, 70, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 0, 150, 22, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 128, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 168, 170, 170, 172, 172, 174, 176, 178, 178, 180, 180, 182, 182, 184, 186, 188, 188, 190, 190, 192, 192, 194, 0, 196, 196, 198, 18, 200, 200, 202, 202, 204, 204, 206, 58, 60, 60, 62, 62, 64, 64, 66, 46, 44, 56, 58, 66, 68], "width": 281, "height": 337}}, "shou_you": {"shou_you": {"type": "mesh", "uvs": [0.19237, 0, 0.3006, 0.02566, 0.41421, 0.04648, 0.50728, 0.08991, 0.60385, 0.15977, 0.66651, 0.25318, 0.65946, 0.33473, 0.68866, 0.42139, 0.78873, 0.49202, 0.88789, 0.56599, 0.99967, 0.63402, 0.99967, 0.71979, 1, 0.79754, 1, 0.88306, 1, 1, 0.90462, 0.96042, 0.85043, 0.88284, 0.81038, 0.78446, 0.7607, 0.67389, 0.69839, 0.56399, 0.56294, 0.49444, 0.38044, 0.44109, 0.26157, 0.39055, 0.18171, 0.32177, 0.20913, 0.28578, 0.23334, 0.25786, 0.24736, 0.22819, 0.27157, 0.18736, 0.29769, 0.15979, 0.30725, 0.14304, 0.29004, 0.11408, 0.26638, 0.08948, 0.26535, 0.08841, 0.26464, 0.08793, 0.23138, 0.09062, 0.18683, 0.08732, 0.13664, 0.08249, 0.05745, 0.08461, 0, 0.05796, 0, 0.00778, 0.05745, 0.00699, 0.12616, 0.01091, 0.14763, 0, 0.41105, 0.29794, 0.54869, 0.41328, 0.44786, 0.19644, 0.45207, 0.37536, 0.32382, 0.04954, 0.24129, 0.05987, 0.14934, 0.04407, 0.29801, 0.08554, 0.3205, 0.07646, 0.31274, 0.065], "triangles": [23, 24, 22, 22, 24, 43, 22, 43, 46, 43, 24, 25, 25, 26, 43, 43, 26, 45, 30, 31, 50, 45, 28, 29, 3, 45, 29, 3, 29, 51, 29, 30, 51, 15, 13, 14, 15, 16, 13, 16, 12, 13, 16, 17, 12, 17, 11, 12, 17, 18, 11, 18, 10, 11, 18, 9, 10, 18, 19, 9, 19, 8, 9, 19, 20, 8, 21, 44, 20, 20, 7, 8, 20, 44, 7, 44, 6, 7, 21, 46, 44, 21, 22, 46, 44, 46, 6, 46, 43, 6, 6, 43, 5, 43, 45, 5, 45, 4, 5, 45, 3, 4, 45, 26, 27, 45, 27, 28, 2, 3, 51, 51, 30, 50, 31, 32, 50, 32, 33, 50, 50, 52, 51, 2, 51, 47, 33, 34, 48, 34, 35, 48, 50, 33, 52, 48, 35, 49, 52, 33, 48, 51, 52, 47, 52, 48, 47, 49, 0, 48, 48, 1, 47, 48, 0, 1, 47, 1, 2, 36, 37, 49, 38, 40, 37, 49, 37, 40, 35, 36, 49, 41, 49, 40, 38, 39, 40, 41, 42, 49, 49, 42, 0], "vertices": [2, 80, -4.67, -29.22, 0.47918, 79, 61.65, -29.55, 0.52082, 1, 79, 19.91, -19.11, 1, 3, 79, -21.9, -16, 0.80933, 78, 89.49, -27.01, 0.094, 102, 26.06, -44.18, 0.09667, 4, 79, -60.31, 2.88, 0.05295, 78, 47.12, -33.06, 0.02946, 81, -42.54, -19.03, 0.00226, 102, -6.74, -16.7, 0.91533, 3, 81, -5.31, 22.8, 0.98277, 102, -40.91, 27.66, 0.01452, 104, 1.3, -140.2, 0.00271, 3, 81, 48.98, 55.9, 0.76238, 82, -14.4, 68.45, 0.04115, 104, -22.15, -81.21, 0.19647, 4, 81, 100.58, 63.61, 0.16669, 82, 35.6, 53.57, 0.05261, 83, -20.52, 59.69, 0.00082, 104, -24.29, -27.49, 0.77988, 2, 83, 33.09, 42.47, 0.65647, 104, -41.78, 34.99, 0.34353, 2, 83, 89.43, 51.95, 0.98715, 104, -85.05, 89.12, 0.01285, 1, 83, 147.5, 60.14, 1, 1, 83, 204.31, 74.02, 1, 1, 83, 252.58, 48.08, 1, 1, 83, 296.4, 24.66, 1, 1, 83, 344.53, -1.22, 1, 1, 83, 410.35, -36.59, 1, 1, 83, 372.27, -54.02, 1, 1, 83, 319.62, -47.26, 1, 1, 83, 257.62, -29.84, 1, 1, 83, 187.15, -11.71, 1, 1, 83, 114.97, 2.33, 1, 3, 82, 126.59, -3.68, 0.00037, 83, 53.38, -18.39, 0.99942, 103, -75.34, 141.17, 0.00021, 3, 82, 78.19, -57.53, 0.52278, 83, -6.89, -58.51, 0.25121, 103, -2.15, 99, 0.22601, 3, 82, 36.87, -90.19, 0.32321, 83, -55.03, -79.87, 0.02026, 103, 40.83, 62.22, 0.65653, 3, 78, 11.5, 150.42, 0.00668, 82, -12.5, -106.79, 0.04674, 103, 68.4, 14.67, 0.94659, 4, 78, 22.38, 128.01, 0.04725, 81, 100.51, -97.08, 0.00054, 82, -32.52, -91.97, 0.00734, 103, 57.16, -9.48, 0.94487, 3, 78, 30.15, 109.84, 0.14031, 81, 81.37, -92.24, 0.01549, 103, 47.63, -27.84, 0.8442, 3, 78, 41.11, 93.62, 0.28527, 81, 61.82, -91.11, 0.04038, 103, 41.88, -46.93, 0.67436, 3, 78, 55.05, 70, 0.53346, 81, 34.58, -87.86, 0.04319, 103, 32.73, -72.9, 0.42335, 1, 3, -45.61, -64.75, 1, 1, 3, -37.27, -72.25, 1, 1, 3, -17.94, -74.49, 1, 1, 3, -0.19, -73.53, 1, 2, 79, 21.98, 22.78, 0.32, 78, 103.89, 29.75, 0.68, 5, 80, -34.49, 29.46, 0.00255, 79, 24.68, 23.98, 0.61716, 78, 105.44, 32.26, 0.37627, 3, 1.34, -70.64, 0.00173, 103, 37.57, -134.91, 0.00229, 5, 80, -25.82, 31.26, 0.02308, 79, 32.99, 27.06, 0.79049, 78, 110.57, 39.48, 0.17875, 3, 4.16, -62.24, 0.00726, 103, 46.37, -133.89, 0.00042, 4, 80, -10.39, 30.72, 0.16717, 79, 48.32, 28.83, 0.75038, 78, 122.23, 49.58, 0.04244, 3, 12.4, -49.2, 0.04001, 4, 80, 7.08, 29.66, 0.50135, 79, 65.76, 30.38, 0.32403, 78, 135.76, 60.7, 0.00316, 3, 22.13, -34.64, 0.17146, 1, 80, 33.86, 34.95, 1, 1, 80, 57.1, 21.06, 1, 1, 80, 64.67, -10.25, 1, 1, 80, 40.87, -14.15, 1, 2, 80, 16.71, -15.08, 0.91921, 79, 81.41, -13.21, 0.08079, 2, 80, 10.83, -27, 0.75925, 79, 76.82, -25.69, 0.24075, 4, 78, -30.22, 80.18, 0.00842, 81, 94.4, -26.25, 0.21472, 82, -8.05, -25.21, 0.16737, 103, -16.3, -2.02, 0.6095, 3, 82, 75.05, 3.9, 0.08013, 83, 5.34, 1.77, 0.89214, 104, 16.7, 29.3, 0.02773, 3, 78, 9.86, 27.59, 0.61707, 81, 28.28, -26.21, 0.27904, 103, -29.76, -66.91, 0.10389, 3, 82, 43.42, -23.13, 0.80922, 83, -32.02, -16.54, 0.02511, 103, -30.95, 51.96, 0.16568, 1, 79, 8.28, -6.31, 1, 5, 80, -27.6, 10.19, 0.00112, 79, 34.65, 7.19, 0.94395, 78, 123.13, 24.01, 0.05369, 3, 20.67, -73.42, 0.00118, 103, 43.25, -153.59, 6e-05, 4, 80, 5.69, 4.75, 0.83715, 79, 68.33, 5.33, 0.14095, 78, 152, 41.45, 3e-05, 3, 42.97, -48.41, 0.02186, 4, 79, 11.37, 18.2, 0.21119, 78, 97.7, 19.99, 0.77468, 3, -2.52, -84.63, 0.008, 103, 23.27, -137.34, 0.00613, 3, 79, 5.17, 10.65, 0.02904, 78, 96.84, 10.25, 0.96864, 103, 15.45, -143.21, 0.00232, 3, 79, 9.6, 4.21, 0.7649, 78, 104.12, 7.44, 0.23457, 103, 18.23, -150.51, 0.00053], "hull": 43, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 0, 2, 94, 96, 98, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 44, 46, 48, 46, 74, 72, 72, 70, 70, 68, 68, 66, 64, 66, 66, 100, 100, 102, 94, 104, 102, 104, 60, 62, 62, 64], "width": 255, "height": 466}}, "huaban": {"huaban": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 89, 207.49, -424.91, 1, 1, 89, 49.49, -424.91, 1, 1, 89, 49.49, 33.09, 1, 1, 89, 207.49, 33.09, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 115, "height": 334}}, "tui_zuo": {"tui_zuo": {"type": "mesh", "uvs": [0.61469, 0, 0.86423, 0.03519, 1, 0.10391, 1, 0.20939, 0.95128, 0.31807, 0.89905, 0.42834, 0.82361, 0.54821, 0.75397, 0.65688, 0.69594, 0.75986, 0.64951, 0.88132, 0.6321, 1, 0.28391, 1, 0.01695, 1, 0.20266, 0.91168, 0.20266, 0.79502, 0.16784, 0.66876, 0.104, 0.53611, 0.07499, 0.39111, 0.20846, 0.29043, 0.28971, 0.20732, 0.16784, 0.18654, 0.50443, 0], "triangles": [9, 10, 13, 12, 13, 11, 10, 11, 13, 13, 14, 9, 9, 14, 8, 14, 15, 8, 8, 15, 7, 15, 16, 7, 7, 16, 6, 6, 16, 5, 16, 17, 5, 17, 18, 5, 5, 18, 4, 18, 19, 4, 4, 19, 3, 3, 19, 2, 19, 1, 2, 19, 21, 1, 21, 0, 1, 19, 20, 21], "vertices": [1, 58, 47.6, 44.73, 1, 2, 58, 80.72, 53.28, 0.99377, 59, -56.68, 12.3, 0.00623, 2, 58, 113.31, 41.5, 0.74498, 59, -28.16, 31.99, 0.25502, 2, 58, 144.33, 6.55, 0.00795, 59, 18.31, 36.92, 0.99205, 1, 59, 66.81, 36.09, 1, 1, 59, 116.07, 34.9, 1, 1, 59, 169.84, 31.35, 1, 1, 59, 218.61, 27.98, 1, 1, 59, 264.72, 25.76, 1, 1, 59, 318.83, 25.8, 1, 1, 59, 371.33, 29.24, 1, 1, 59, 375.81, -13.01, 1, 1, 59, 379.25, -45.39, 1, 1, 59, 337.95, -26.99, 1, 1, 59, 286.56, -32.44, 1, 1, 59, 231.39, -42.57, 1, 1, 59, 173.77, -56.51, 1, 2, 58, 113.36, -128.57, 9e-05, 59, 110.27, -66.81, 0.99991, 2, 58, 95.93, -84.4, 0.05657, 59, 64.2, -55.32, 0.94343, 2, 58, 78.91, -50.28, 0.47131, 59, 26.54, -49.35, 0.52869, 2, 58, 61.68, -53.27, 0.984, 59, 18.96, -65.11, 0.016, 1, 58, 37.54, 35.8, 1], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 0, 42], "width": 89, "height": 323}}, "yanjing": {"yanjing": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 8, -19.47, -44.62, 1, 1, 8, -21.06, 68.37, 1, 1, 8, 21.93, 68.98, 1, 1, 8, 23.53, -44.01, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 82, "height": 31}}, "shangshen": {"shangshen": {"type": "mesh", "uvs": [0.26709, 0, 0.26522, 0.07928, 0.26895, 0.15162, 0.16635, 0.189, 0.17381, 0.24447, 0.08427, 0.28305, 0, 0.29511, 0, 0.38434, 0, 0.46272, 0.10106, 0.52663, 0.13464, 0.62068, 0.15329, 0.70026, 0.23911, 0.77502, 0.22791, 0.87028, 0.32492, 0.94989, 0.47229, 1, 0.61033, 1, 0.75957, 0.98244, 0.84538, 0.91612, 0.90135, 0.82689, 0.95358, 0.73405, 0.99835, 0.64602, 1, 0.54266, 0.92199, 0.46187, 0.87348, 0.36541, 0.78581, 0.281, 0.63284, 0.24965, 0.58433, 0.19177, 0.55449, 0.14595, 0.51345, 0.07119, 0.42577, 0.00608, 0.33809, 0, 0.37882, 0.06077, 0.39178, 0.16492, 0.48068, 0.27386, 0.5955, 0.40913, 0.69181, 0.53004, 0.70107, 0.67967, 0.66958, 0.80178, 0.62884, 0.94184, 0.25103, 0.2571, 0.35104, 0.36124, 0.43623, 0.48694, 0.50475, 0.60306, 0.50475, 0.71798, 0.47882, 0.84487, 0.43067, 0.92388, 0.11213, 0.31097, 0.19361, 0.40314, 0.24917, 0.52884, 0.28621, 0.63179, 0.30288, 0.73594, 0.31029, 0.81016, 0.29918, 0.86044, 0.64365, 0.28343, 0.72514, 0.38758, 0.84552, 0.5037, 0.87886, 0.60785, 0.827, 0.7096, 0.78441, 0.82692, 0.76033, 0.92268], "triangles": [54, 26, 25, 55, 54, 25, 55, 25, 24, 32, 31, 30, 32, 30, 29, 1, 31, 32, 31, 1, 0, 2, 1, 32, 33, 32, 29, 33, 29, 28, 2, 32, 33, 40, 4, 3, 2, 40, 3, 40, 2, 33, 27, 34, 33, 27, 33, 28, 40, 33, 34, 40, 47, 4, 34, 27, 26, 34, 26, 54, 47, 5, 4, 41, 40, 34, 47, 7, 6, 47, 6, 5, 48, 40, 41, 48, 47, 40, 7, 47, 48, 35, 34, 54, 35, 54, 55, 41, 34, 35, 8, 7, 48, 42, 41, 35, 23, 56, 55, 23, 55, 24, 9, 8, 48, 42, 49, 48, 42, 48, 41, 9, 48, 49, 36, 35, 55, 36, 55, 56, 42, 35, 36, 56, 23, 22, 43, 42, 36, 36, 56, 57, 10, 9, 49, 50, 49, 42, 50, 42, 43, 10, 49, 50, 11, 10, 50, 57, 56, 22, 21, 57, 22, 37, 36, 57, 43, 36, 37, 58, 37, 57, 58, 57, 21, 44, 50, 43, 44, 43, 37, 20, 58, 21, 51, 50, 44, 11, 50, 51, 12, 11, 51, 38, 44, 37, 38, 37, 58, 52, 51, 44, 12, 51, 52, 19, 58, 20, 59, 38, 58, 19, 59, 58, 45, 52, 44, 45, 44, 38, 52, 13, 12, 53, 52, 45, 53, 13, 52, 18, 59, 19, 60, 38, 59, 60, 59, 18, 46, 53, 45, 39, 45, 38, 39, 38, 60, 46, 45, 39, 14, 53, 46, 13, 53, 14, 17, 39, 60, 17, 60, 18, 15, 46, 39, 14, 46, 15, 16, 15, 39, 16, 39, 17], "vertices": [1, 4, 96.46, 31.2, 1, 1, 4, 66.09, 31.18, 1, 2, 3, 167.47, 9.42, 0.00902, 4, 41.15, 30, 0.99098, 2, 3, 165.28, 35.6, 0.11926, 4, 27.93, 52.7, 0.88074, 2, 3, 147.2, 42.06, 0.3523, 4, 8.82, 50.76, 0.6477, 3, 2, 221.96, 119.53, 3e-05, 3, 143.41, 65.76, 0.69988, 4, -4.77, 70.54, 0.30009, 3, 2, 217.8, 142.32, 2e-05, 3, 149.13, 88.21, 0.78198, 4, -9.25, 93.27, 0.218, 3, 2, 187.01, 142.32, 0.01032, 3, 121.14, 101.04, 0.84799, 4, -40.03, 92.84, 0.14169, 3, 2, 159.97, 142.32, 0.03145, 3, 96.56, 112.3, 0.88848, 4, -67.07, 92.46, 0.08006, 4, 2, 137.93, 115.79, 0.10029, 3, 65.46, 97.37, 0.68076, 4, -88.74, 65.62, 0.01895, 94, -108.6, -37.48, 0.2, 4, 2, 105.48, 108.3, 0.28846, 3, 32.85, 104.08, 0.5111, 4, -121.08, 57.67, 0.00044, 94, -101.12, -69.92, 0.2, 3, 2, 78.02, 104.14, 0.44246, 3, 6.15, 111.74, 0.35754, 94, -96.96, -97.38, 0.2, 3, 2, 52.23, 85, 0.63465, 3, -25.27, 105.09, 0.16535, 94, -77.82, -123.17, 0.2, 3, 2, 19.37, 87.5, 0.75113, 3, -54.1, 121.05, 0.04887, 94, -80.32, -156.04, 0.2, 4, 2, -8.1, 65.87, 0.66975, 3, -88.08, 112.83, 0.01025, 95, -86.52, -129.21, 0.12, 94, -58.68, -183.5, 0.2, 4, 2, -25.39, 33, 0.63988, 3, -117.49, 90.15, 0.00012, 95, -53.66, -146.5, 0.16, 94, -25.82, -200.79, 0.2, 3, 2, -25.39, 2.22, 0.63074, 95, -22.88, -146.5, 0.144, 94, 4.96, -200.79, 0.22526, 3, 2, -19.33, -31.06, 0.64, 95, 10.4, -140.44, 0.16, 94, 38.24, -194.73, 0.2, 2, 2, 3.55, -50.2, 0.8, 94, 57.38, -171.85, 0.2, 4, 2, 34.33, -62.68, 0.71669, 3, -103.07, -21.71, 0.00011, 95, 42.02, -86.78, 0.104, 94, 69.86, -141.07, 0.1792, 4, 2, 66.37, -74.33, 0.6732, 3, -78.8, -45.64, 0.0244, 95, 53.67, -54.75, 0.128, 94, 81.51, -109.04, 0.1744, 4, 2, 96.73, -84.31, 0.55146, 3, -55.35, -67.37, 0.11414, 95, 63.65, -24.38, 0.168, 94, 91.49, -78.67, 0.1664, 4, 2, 132.4, -84.68, 0.37517, 3, -23.09, -82.56, 0.27123, 95, 64.02, 11.28, 0.192, 94, 91.86, -43.01, 0.1616, 3, 2, 160.27, -67.28, 0.22037, 3, 9.5, -78.36, 0.57963, 94, 74.46, -15.13, 0.2, 5, 2, 193.55, -56.46, 0.02791, 3, 44.26, -82.4, 0.52156, 4, -30.7, -105.83, 0.00093, 6, -31.07, -25.6, 0.312, 94, 63.65, 18.15, 0.1376, 1, 6, 3.55, -19.96, 1, 3, 3, 102.92, -50.25, 0.50413, 4, 8.48, -51.61, 0.19987, 6, 27.59, 6.54, 0.296, 2, 3, 125.58, -48.74, 0.27715, 4, 28.29, -40.52, 0.72285, 2, 3, 142.72, -49.27, 0.07229, 4, 44, -33.64, 0.92771, 2, 3, 169.98, -51.7, 0.00038, 4, 69.66, -24.12, 0.99962, 1, 4, 91.85, -4.26, 1, 1, 4, 97.67, 15.38, 1, 1, 4, 72.83, 5.95, 1, 1, 4, 36.95, 2.55, 1, 3, 3, 109.47, -15.93, 0.56638, 4, -0.35, -17.8, 0.29362, 95, -51.79, 104.02, 0.14, 3, 3, 56.37, -19.76, 0.84223, 4, -46.66, -44.06, 0.00177, 95, -26.18, 57.35, 0.156, 3, 2, 136.75, -15.95, 0.11685, 3, 9.51, -21.9, 0.59115, 95, -4.71, 15.64, 0.292, 3, 2, 85.13, -18.01, 0.71439, 3, -38.29, -2.27, 0.00161, 95, -2.64, -35.99, 0.284, 2, 2, 43, -10.99, 0.788, 95, -9.66, -78.11, 0.212, 2, 2, -5.32, -1.91, 0.876, 95, -18.75, -126.44, 0.124, 2, 3, 136.06, 28.22, 0.32205, 4, 4.71, 33.49, 0.67795, 4, 2, 194.98, 60.04, 0.00222, 3, 94.11, 22.92, 0.88503, 4, -30.91, 10.68, 0.02475, 95, -80.7, 73.87, 0.088, 3, 2, 151.62, 41.04, 0.03251, 3, 46.77, 23.71, 0.74349, 95, -61.7, 30.5, 0.224, 3, 2, 111.56, 25.76, 0.31856, 3, 3.98, 26.52, 0.32144, 95, -46.42, -9.56, 0.36, 3, 2, 71.91, 25.76, 0.75275, 3, -32.06, 43.03, 0.06325, 95, -46.42, -49.2, 0.184, 3, 2, 28.13, 31.55, 0.78591, 3, -69.45, 66.53, 0.01409, 95, -52.2, -92.98, 0.2, 3, 2, 0.87, 42.28, 0.79433, 3, -89.75, 87.65, 0.00567, 95, -62.94, -120.24, 0.2, 4, 2, 212.33, 113.32, 0.00079, 3, 132.07, 64.12, 0.60608, 4, -14.31, 64.2, 0.24314, 95, -133.98, 91.22, 0.15, 4, 2, 180.53, 95.15, 0.01885, 3, 95.59, 60.85, 0.75331, 4, -45.85, 45.58, 0.07784, 95, -115.8, 59.41, 0.15, 4, 2, 137.16, 82.76, 0.1428, 3, 51.01, 67.66, 0.70373, 4, -89.04, 32.58, 0.00348, 95, -103.41, 16.05, 0.15, 3, 2, 101.65, 74.5, 0.33801, 3, 15.28, 74.95, 0.40799, 95, -95.15, -19.47, 0.254, 3, 2, 65.71, 70.78, 0.66368, 3, -18.93, 86.54, 0.22432, 95, -91.44, -55.4, 0.112, 3, 2, 40.11, 69.13, 0.75831, 3, -42.9, 95.71, 0.09169, 95, -89.79, -81.01, 0.15, 3, 2, 22.76, 71.61, 0.80099, 3, -57.64, 105.19, 0.04901, 95, -92.26, -98.35, 0.15, 4, 3, 91.32, -47.59, 0.4927, 4, -3.14, -54.19, 0.08834, 6, 15.99, 9.2, 0.32936, 95, -15.45, 100.71, 0.0896, 5, 2, 185.9, -23.38, 0.01101, 3, 51.09, -49.14, 0.64597, 4, -38.82, -72.87, 0.00388, 6, -24.24, 7.66, 0.24954, 95, 2.73, 64.78, 0.0896, 3, 2, 145.84, -50.23, 0.26241, 3, 3.48, -56.85, 0.52959, 95, 29.57, 24.72, 0.208, 3, 2, 109.91, -57.66, 0.46354, 3, -32.28, -48.64, 0.15246, 95, 37.01, -11.21, 0.384, 3, 2, 74.8, -46.1, 0.757, 3, -59.37, -23.5, 0.019, 95, 25.44, -46.31, 0.224, 2, 2, 34.33, -36.6, 0.8, 95, 15.94, -86.79, 0.2, 2, 2, 1.29, -31.23, 0.8, 95, 10.57, -119.83, 0.2], "hull": 32, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 0, 62, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120], "width": 162, "height": 251}}, "toufa_hou": {"toufa_hou": {"type": "mesh", "uvs": [0.53089, 0, 0.73522, 0.17237, 0.91036, 0.27066, 1, 0.30169, 0.91523, 0.51379, 0.779, 0.68708, 0.58927, 0.9328, 0.43845, 1, 0.10276, 1, 0, 0.91728, 0, 0.75692, 0, 0.51896, 0, 0.31463, 0.16114, 0.13874, 0.33142, 0, 0.47552, 0.4238, 0.29872, 0.77897, 0.53063, 0.54595, 0.42143, 0.20034, 0.71314, 0.34799], "triangles": [18, 14, 0, 18, 0, 1, 13, 14, 18, 19, 18, 1, 19, 1, 2, 15, 18, 19, 3, 19, 2, 4, 19, 3, 12, 15, 11, 18, 12, 13, 15, 12, 18, 17, 15, 19, 17, 19, 4, 11, 15, 17, 5, 17, 4, 16, 10, 11, 17, 16, 11, 16, 17, 5, 9, 10, 16, 6, 16, 5, 8, 9, 16, 7, 8, 16, 6, 7, 16], "vertices": [-53.08, 113.58, -39.36, 35.15, -17.29, 11.85, -6, 4.5, -16.68, -45.77, -33.85, -86.84, -57.75, -145.07, -76.76, -161, -119.05, -161, -132, -141.4, -132, -103.39, -132, -46.99, -132, 1.43, -111.7, 43.12, -100.26, 113.07, -72.08, -24.44, -94.36, -108.62, -65.14, -53.39, -78.9, 28.52, -42.14, -6.47], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 0], "width": 91, "height": 173}}, "tui_you": {"tui_you": {"type": "mesh", "uvs": [0, 0.13879, 0.15303, 0.22979, 0.2494, 0.34119, 0.41699, 0.42435, 0.55107, 0.54987, 0.68515, 0.66127, 0.65582, 0.746, 0.57202, 0.82602, 0.71867, 0.92173, 0.82761, 1, 1, 1, 1, 0.89663, 1, 0.7915, 1, 0.70991, 0.92817, 0.59224, 0.88627, 0.49653, 0.81504, 0.38826, 0.74381, 0.27215, 0.63487, 0.16232, 0.54688, 0.04935, 0.46308, 0, 0.2075, 0, 0.05247, 0.03994], "triangles": [21, 20, 19, 9, 11, 10, 9, 8, 11, 11, 8, 12, 12, 8, 7, 7, 6, 12, 6, 13, 12, 6, 5, 13, 5, 14, 13, 5, 4, 14, 4, 15, 14, 15, 3, 16, 15, 4, 3, 16, 3, 17, 3, 2, 17, 17, 1, 18, 18, 1, 19, 1, 17, 2, 19, 1, 0, 21, 19, 22, 22, 19, 0], "vertices": [1, 60, 47.6, -58.53, 1, 2, 60, 96, -75.83, 0.05241, 61, 59.74, -52.46, 0.94759, 1, 61, 115.71, -51.46, 1, 1, 61, 162.47, -34.33, 1, 1, 61, 226.86, -28.83, 1, 1, 61, 284.79, -21.37, 1, 1, 61, 322.01, -38.18, 1, 1, 61, 354.23, -63.66, 1, 1, 61, 405.64, -51.87, 1, 1, 61, 447.11, -44.1, 1, 1, 61, 456.09, -14.58, 1, 1, 61, 408.82, -0.2, 1, 1, 61, 360.75, 14.43, 1, 1, 61, 323.44, 25.78, 1, 1, 61, 265.88, 29.85, 1, 1, 61, 219.93, 36, 1, 1, 61, 166.71, 38.86, 1, 2, 60, 191.7, -26.48, 0.00195, 61, 109.9, 42.82, 0.99805, 2, 60, 143.94, 2.79, 0.20918, 61, 54, 39.44, 0.79082, 2, 60, 98.22, 35.54, 0.25875, 61, -2.25, 40.09, 0.74125, 1, 61, -29.18, 32.61, 1, 1, 60, 35.86, 16.59, 1, 2, 60, 25.82, -15.56, 0.98367, 61, -32.32, -43.26, 0.01633], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44], "width": 130, "height": 348}}, "lian": {"lian": {"type": "mesh", "uvs": [0.51926, 0.31826, 0.52355, 0.25549, 0.54807, 0.17647, 0.58125, 0.10663, 0.63462, 0.0469, 0.69834, 0.0091, 0.76181, 0, 0.82239, 0, 0.87478, 0.00658, 0.92336, 0.03023, 0.96501, 0.08337, 0.992, 0.16033, 1, 0.22298, 0.99792, 0.29738, 0.97752, 0.35808, 0.97237, 0.37519, 0.95747, 0.42176, 0.93951, 0.49996, 0.91801, 0.56688, 0.86537, 0.6145, 0.82564, 0.6488, 0.77593, 0.67516, 0.72855, 0.64631, 0.68284, 0.61038, 0.64865, 0.57717, 0.61746, 0.53199, 0.58243, 0.5113, 0.56541, 0.50026, 0.55736, 0.52685, 0.52781, 0.56306, 0.57146, 0.56986, 0.56562, 0.62018, 0.52802, 0.6621, 0.50409, 0.71273, 0.47076, 0.74594, 0.4118, 0.76064, 0.3883, 0.71763, 0.38142, 0.72367, 0.34673, 0.75414, 0.29964, 0.79014, 0.25334, 0.82214, 0.19604, 0.85114, 0.14817, 0.86614, 0.0893, 0.89114, 0.03907, 0.92714, 0.03103, 0.96914, 0.05158, 1, 0.01016, 0.98914, 0.00623, 0.94214, 0.00788, 0.87714, 0.05399, 0.83914, 0.11363, 0.81914, 0.17093, 0.80614, 0.22351, 0.79614, 0.26766, 0.77014, 0.32653, 0.72714, 0.37205, 0.68214, 0.39324, 0.63314, 0.37754, 0.58814, 0.38304, 0.53314, 0.41835, 0.50114, 0.45681, 0.47214, 0.49056, 0.50312, 0.52823, 0.46511, 0.50077, 0.42612, 0.49056, 0.37214, 0.49605, 0.32314, 0.60827, 0.47958, 0.58473, 0.41758, 0.55098, 0.34758, 0.84246, 0.10214, 0.84089, 0.22614, 0.83119, 0.36314, 0.84061, 0.49214, 0.80764, 0.56714, 0.77527, 0.65946, 0.68785, 0.07614, 0.76555, 0.06636, 0.8929, 0.04714, 0.91488, 0.13514, 0.92272, 0.23714, 0.617, 0.27783, 0.65722, 0.25667, 0.71493, 0.25297, 0.78306, 0.27307, 0.80672, 0.30375, 0.60745, 0.30692, 0.64689, 0.29264, 0.7021, 0.28841, 0.75441, 0.29423, 0.79468, 0.31168, 0.60122, 0.33866, 0.6224, 0.33548, 0.64564, 0.31644, 0.68591, 0.30322, 0.72577, 0.30639, 0.75607, 0.32808, 0.77102, 0.36035, 0.76272, 0.39473, 0.73532, 0.42699, 0.71124, 0.43546, 0.66972, 0.44339, 0.64398, 0.42435, 0.62738, 0.3905, 0.60994, 0.36775, 0.64563, 0.37501, 0.6613, 0.3547, 0.68467, 0.34395, 0.71548, 0.34678, 0.73802, 0.35738, 0.75613, 0.39141, 0.74801, 0.40561, 0.73287, 0.42045, 0.66366, 0.41512, 0.6924, 0.42086, 0.67082, 0.38095, 0.70709, 0.38183, 0.73438, 0.38667, 0.86428, 0.30732, 0.88986, 0.28249, 0.9325, 0.27938, 0.97797, 0.30111, 0.86387, 0.32802, 0.89067, 0.31043, 0.92275, 0.3068, 0.95848, 0.31715, 0.97026, 0.32853, 0.86428, 0.36423, 0.8862, 0.33733, 0.91016, 0.32595, 0.9459, 0.33526, 0.9682, 0.3478, 0.95175, 0.41302, 0.93127, 0.44865, 0.91433, 0.46572, 0.88281, 0.45468, 0.86115, 0.43711, 0.85327, 0.40549, 0.8678, 0.42277, 0.87612, 0.3819, 0.89503, 0.36885, 0.92339, 0.36483, 0.94624, 0.37838, 0.93954, 0.4085, 0.91717, 0.44311, 0.90509, 0.44662, 0.88217, 0.44045, 0.86739, 0.4012, 0.891, 0.40774, 0.91986, 0.40039, 0.62051, 0.12107, 0.59883, 0.24692, 0.67432, 0.2152, 0.75623, 0.22646, 0.76119, 0.14155, 0.68148, 0.14153, 0.6088, 0.18908, 0.69077, 0.46505, 0.70555, 0.54151, 0.74904, 0.60578, 0.89862, 0.49054, 0.88123, 0.54816, 0.83514, 0.60689, 0.63076, 0.4717, 0.66033, 0.54373, 0.75599, 0.44289, 0.76034, 0.51492, 0.52708, 0.3742, 0.55817, 0.43362, 0.45246, 0.59879, 0.49305, 0.56491, 0.39454, 0.66412, 0.40436, 0.70304, 0.82369, 0.00659], "triangles": [36, 37, 172, 170, 29, 32, 56, 171, 172, 171, 57, 169, 57, 58, 169, 62, 60, 61, 59, 60, 169, 170, 60, 62, 33, 172, 169, 34, 172, 33, 32, 29, 31, 31, 29, 30, 32, 33, 169, 35, 172, 34, 58, 59, 169, 60, 170, 169, 169, 170, 32, 172, 171, 169, 56, 57, 171, 36, 172, 35, 173, 7, 8, 78, 8, 9, 173, 77, 6, 173, 6, 7, 5, 6, 77, 76, 4, 5, 76, 5, 77, 70, 173, 8, 70, 8, 78, 77, 173, 70, 150, 3, 4, 150, 4, 76, 79, 78, 9, 79, 9, 10, 70, 78, 79, 155, 150, 76, 154, 76, 77, 154, 77, 70, 155, 76, 154, 79, 10, 11, 156, 3, 150, 156, 150, 155, 2, 3, 156, 152, 156, 155, 71, 154, 70, 71, 70, 79, 153, 155, 154, 153, 154, 71, 152, 155, 153, 80, 79, 11, 80, 11, 12, 71, 79, 80, 151, 2, 156, 82, 151, 156, 83, 152, 153, 1, 2, 151, 152, 82, 156, 82, 152, 83, 84, 153, 71, 83, 153, 84, 81, 151, 82, 120, 80, 12, 119, 71, 80, 119, 80, 120, 88, 82, 83, 87, 81, 82, 87, 82, 88, 89, 83, 84, 88, 83, 89, 12, 121, 120, 13, 121, 12, 94, 87, 88, 85, 84, 71, 71, 72, 85, 95, 88, 89, 94, 88, 95, 124, 119, 120, 86, 151, 81, 86, 81, 87, 119, 118, 71, 123, 119, 124, 118, 119, 123, 90, 84, 85, 89, 84, 90, 93, 86, 87, 93, 87, 94, 125, 120, 121, 124, 120, 125, 129, 123, 124, 71, 118, 72, 122, 118, 123, 96, 89, 90, 95, 89, 96, 126, 125, 121, 130, 124, 125, 129, 124, 130, 92, 86, 93, 128, 122, 123, 128, 123, 129, 86, 1, 151, 91, 86, 92, 1, 69, 0, 1, 86, 69, 107, 93, 94, 108, 107, 94, 95, 108, 94, 109, 108, 95, 91, 69, 86, 126, 130, 125, 131, 130, 126, 106, 93, 107, 92, 93, 106, 96, 109, 95, 13, 14, 126, 131, 126, 14, 13, 126, 121, 97, 96, 90, 109, 96, 97, 122, 72, 118, 127, 122, 128, 72, 122, 127, 141, 129, 130, 104, 91, 92, 140, 128, 129, 140, 129, 141, 127, 128, 140, 167, 0, 69, 105, 92, 106, 104, 92, 105, 15, 131, 14, 142, 130, 131, 142, 131, 15, 141, 130, 142, 115, 106, 107, 105, 106, 115, 116, 107, 108, 116, 108, 109, 115, 107, 116, 139, 127, 140, 117, 116, 109, 103, 104, 105, 110, 109, 97, 117, 109, 110, 98, 110, 97, 149, 140, 141, 149, 141, 142, 147, 127, 139, 137, 72, 127, 137, 127, 147, 111, 117, 110, 148, 139, 140, 148, 140, 149, 147, 139, 148, 143, 149, 142, 132, 142, 15, 143, 142, 132, 113, 105, 115, 102, 103, 105, 104, 68, 69, 104, 69, 91, 68, 104, 103, 167, 69, 68, 112, 116, 117, 112, 117, 111, 114, 115, 116, 114, 116, 112, 113, 115, 114, 16, 132, 15, 138, 147, 148, 137, 147, 138, 113, 102, 105, 66, 167, 65, 167, 66, 0, 64, 65, 167, 99, 112, 111, 111, 110, 98, 99, 111, 98, 168, 167, 68, 64, 167, 168, 100, 114, 112, 100, 112, 99, 136, 137, 138, 146, 138, 148, 136, 138, 146, 165, 99, 98, 144, 148, 149, 144, 149, 143, 145, 146, 148, 101, 113, 114, 102, 113, 101, 101, 114, 100, 144, 145, 148, 133, 144, 143, 133, 143, 132, 133, 132, 16, 135, 146, 145, 136, 146, 135, 157, 101, 100, 63, 64, 168, 134, 145, 144, 134, 144, 133, 135, 145, 134, 102, 68, 103, 163, 102, 101, 163, 68, 102, 67, 68, 163, 160, 135, 134, 73, 72, 137, 73, 137, 136, 73, 136, 135, 73, 135, 160, 17, 133, 16, 134, 133, 17, 160, 134, 17, 67, 27, 168, 67, 168, 68, 63, 168, 27, 26, 27, 67, 73, 166, 165, 90, 85, 72, 97, 90, 72, 98, 97, 72, 98, 72, 73, 73, 165, 98, 28, 63, 27, 25, 67, 163, 26, 67, 25, 157, 166, 158, 100, 99, 165, 100, 166, 157, 165, 166, 100, 157, 164, 163, 157, 163, 101, 164, 157, 158, 25, 163, 164, 161, 73, 160, 18, 160, 17, 161, 160, 18, 74, 166, 73, 74, 73, 161, 24, 25, 164, 159, 158, 166, 159, 166, 74, 162, 74, 161, 23, 164, 158, 23, 158, 159, 24, 164, 23, 19, 162, 161, 19, 161, 18, 22, 23, 159, 20, 74, 162, 75, 159, 74, 20, 162, 19, 20, 75, 74, 22, 159, 75, 21, 75, 20, 22, 75, 21, 44, 49, 50, 48, 49, 44, 45, 48, 44, 47, 48, 45, 47, 45, 46, 41, 52, 53, 41, 53, 40, 42, 51, 52, 42, 52, 41, 43, 50, 51, 43, 51, 42, 44, 50, 43, 172, 37, 56, 55, 56, 37, 38, 55, 37, 39, 54, 55, 39, 55, 38, 40, 53, 54, 40, 54, 39, 62, 63, 28, 29, 62, 28, 170, 62, 29], "vertices": [3, 7, -12.33, 107.33, 0.2, 9, 43.79, 104.34, 0.04, 4, 124.62, 59.8, 0.76, 3, 7, 3.94, 106.14, 0.2, 9, 60.06, 103.15, 0.04, 4, 140.89, 58.61, 0.76, 2, 7, 24.52, 98.34, 0.2, 4, 161.47, 50.81, 0.8, 2, 7, 42.76, 87.65, 0.2, 4, 179.71, 40.12, 0.8, 2, 7, 58.48, 70.25, 0.2, 4, 195.43, 22.73, 0.8, 3, 7, 68.57, 49.36, 0.2, 9, 124.68, 46.37, 0.16, 4, 205.51, 1.84, 0.64, 3, 7, 72.19, 28.47, 0.2, 9, 128.31, 25.48, 0.16, 4, 209.14, -19.06, 0.64, 3, 7, 73.9, 8.5, 0.2, 9, 130.02, 5.51, 0.24, 4, 210.85, -39.03, 0.56, 3, 7, 70.07, -8.85, 0.2, 9, 126.19, -11.84, 0.16, 4, 207.02, -56.37, 0.64, 2, 7, 64.14, -24.96, 0.2, 4, 201.09, -72.49, 0.8, 2, 7, 50.57, -38.9, 0.2, 4, 187.52, -86.43, 0.8, 3, 7, 30.77, -48.08, 0.2, 9, 86.88, -51.07, 0.08, 4, 167.72, -95.61, 0.72, 3, 7, 14.58, -50.96, 0.2, 9, 70.7, -53.95, 0.08, 4, 151.53, -98.49, 0.72, 3, 7, -4.7, -50.54, 0.2, 9, 51.42, -53.53, 0.08, 4, 132.25, -98.07, 0.72, 3, 7, -20.51, -44.03, 0.2, 9, 35.6, -47.02, 0.08, 4, 116.43, -91.56, 0.72, 3, 7, -24.97, -42.39, 0.2, 9, 31.15, -45.38, 0.08, 4, 111.98, -89.92, 0.72, 3, 7, -37.1, -37.65, 0.2, 9, 19.02, -40.64, 0.08, 4, 99.85, -85.17, 0.72, 3, 7, -57.43, -32, 0.2, 9, -1.31, -34.99, 0.08, 4, 79.52, -79.53, 0.72, 3, 7, -74.86, -25.15, 0.2, 9, -18.75, -28.14, 0.12, 4, 62.09, -72.68, 0.68, 3, 7, -87.44, -7.96, 0.2, 9, -31.32, -10.95, 0.16, 4, 49.51, -55.49, 0.64, 3, 7, -96.51, 5.03, 0.2, 9, -40.39, 2.04, 0.2, 4, 40.44, -42.5, 0.6, 3, 7, -103.57, 21.33, 0.2, 9, -47.45, 18.34, 0.24, 4, 33.38, -26.2, 0.56, 3, 7, -96.31, 37.07, 0.2, 9, -40.2, 34.08, 0.16, 4, 40.63, -10.46, 0.64, 3, 7, -87.22, 52.29, 0.2, 9, -31.1, 49.3, 0.16, 4, 49.73, 4.76, 0.64, 3, 7, -78.78, 63.69, 0.2, 9, -22.66, 60.7, 0.12, 4, 58.17, 16.16, 0.68, 3, 7, -67.22, 74.14, 0.2, 9, -11.11, 71.15, 0.08, 4, 69.72, 26.62, 0.72, 2, 9, -5.91, 82.79, 0.05, 4, 74.92, 38.25, 0.95, 3, 7, -59.25, 91.44, 0.2, 9, -3.13, 88.45, 0.04, 4, 77.7, 43.91, 0.76, 2, 44, 6.78, 8.55, 0.9972, 9, -10.06, 91.01, 0.0028, 2, 44, 20.29, 7.88, 0.488, 45, -8.33, 8.39, 0.512, 1, 45, -16.87, 20.12, 1, 1, 45, -6.03, 27.61, 1, 1, 45, 10.37, 25.95, 1, 1, 45, 25.35, 29.12, 1, 1, 45, 39.14, 26.95, 1, 3, 44, 83.8, 15.07, 0.28515, 46, 0.49, 15.93, 0.02684, 45, 55.2, 15.33, 0.688, 3, 44, 81, 1.79, 0.20627, 46, 1.76, 2.42, 0.29773, 45, 52.35, 2.06, 0.496, 2, 44, 83.69, 1.21, 0.02298, 46, 4.5, 2.66, 0.97702, 2, 44, 97.28, -1.72, 0, 46, 18.35, 3.89, 1, 1, 46, 36.47, 4.35, 1, 1, 46, 53.85, 4.04, 1, 1, 47, 14.59, 5.15, 1, 1, 47, 30.85, 4.88, 1, 1, 47, 51.29, 6.2, 1, 2, 47, 80.43, 20.97, 0.06924, 48, 22.1, 7.7, 0.93076, 1, 48, 30.93, 16, 1, 1, 48, 31, 25.97, 1, 1, 48, 39.84, 15.75, 1, 1, 48, 36.53, 2.99, 1, 2, 47, 95.85, 6.5, 0.03958, 48, 22.98, -8.89, 0.96042, 1, 47, 59.13, -9.79, 1, 1, 47, 38.78, -9.79, 1, 1, 47, 19.63, -8.23, 1, 2, 46, 59.1, -6.67, 0.38788, 47, 2.19, -6.32, 0.61212, 1, 46, 43.1, -5.36, 1, 1, 46, 20.71, -5.48, 1, 2, 44, 77.98, -8.41, 0.45281, 46, 1.9, -8.22, 0.54719, 1, 45, 35.24, -11.69, 1, 1, 45, 30.26, -23.43, 1, 1, 45, 18.61, -31.82, 1, 1, 45, 4.59, -28.96, 1, 1, 45, -9.56, -24.81, 1, 2, 44, 17.41, -11.71, 0.464, 45, -11.29, -11.19, 0.536, 3, 7, -50.32, 103.83, 0.2, 9, 5.8, 100.84, 0.04, 4, 86.63, 56.3, 0.76, 3, 7, -40.35, 113.04, 0.2, 9, 15.77, 110.05, 0.04, 4, 96.6, 65.51, 0.76, 3, 7, -26.42, 116.6, 0.2, 9, 29.7, 113.61, 0.04, 4, 110.53, 69.07, 0.76, 3, 7, -13.7, 114.97, 0.2, 9, 42.42, 111.98, 0.04, 4, 123.25, 67.44, 0.76, 2, 9, 2.42, 74.38, 0.1, 4, 83.25, 29.84, 0.9, 2, 9, 18.37, 82.37, 0.1, 4, 99.2, 37.84, 0.9, 2, 9, 36.34, 93.76, 0.1, 4, 117.17, 49.23, 0.9, 2, 9, 101.26, -1.52, 0.3, 4, 182.09, -46.06, 0.7, 2, 9, 69.14, -1.45, 0.3, 4, 149.97, -45.99, 0.7, 2, 9, 33.61, 1.25, 0.3, 4, 114.45, -43.29, 0.7, 2, 9, 0.25, -2.33, 0.3, 4, 81.08, -46.87, 0.7, 2, 9, -19.33, 8.27, 0.3, 4, 61.51, -36.26, 0.7, 2, 9, -43.39, 18.62, 0.3, 4, 37.44, -25.92, 0.7, 2, 9, 107.27, 49.59, 0.2, 4, 188.1, 5.06, 0.8, 2, 9, 109.97, 23.99, 0.2, 4, 190.8, -20.55, 0.8, 2, 9, 115.74, -17.96, 0.2, 4, 196.57, -62.5, 0.8, 2, 9, 93.05, -25.54, 0.15, 4, 173.88, -70.07, 0.85, 2, 9, 66.67, -28.5, 0.15, 4, 147.5, -73.03, 0.85, 2, 9, 54.71, 72.23, 0.1, 4, 135.54, 27.7, 0.9, 2, 9, 60.38, 59.04, 0.2, 4, 141.21, 14.5, 0.8, 2, 9, 61.61, 40.01, 0.2, 4, 142.44, -4.53, 0.8, 2, 9, 56.72, 17.46, 0.25, 4, 137.55, -27.08, 0.75, 2, 9, 48.88, 9.54, 0.25, 4, 129.71, -35, 0.75, 2, 9, 47.13, 75.28, 0.1, 4, 127.96, 30.74, 0.9, 2, 9, 51.01, 62.32, 0.2, 4, 131.85, 17.78, 0.8, 2, 9, 52.37, 44.11, 0.2, 4, 133.2, -0.42, 0.8, 2, 9, 51.1, 26.83, 0.2, 4, 131.93, -17.7, 0.8, 2, 9, 46.77, 13.48, 0.25, 4, 127.6, -31.06, 0.75, 2, 9, 38.88, 77.22, 0.1, 4, 119.72, 32.68, 0.9, 2, 9, 39.81, 70.24, 0.1, 4, 120.64, 25.71, 0.9, 2, 9, 44.84, 62.64, 0.2, 4, 125.68, 18.1, 0.8, 2, 9, 48.46, 49.4, 0.2, 4, 129.29, 4.87, 0.8, 2, 9, 47.82, 36.24, 0.2, 4, 128.65, -8.3, 0.8, 2, 9, 42.34, 26.16, 0.2, 4, 123.18, -18.38, 0.8, 2, 9, 34.06, 21.11, 0.2, 4, 114.89, -23.43, 0.8, 2, 9, 25.11, 23.73, 0.2, 4, 105.95, -20.81, 0.8, 2, 9, 16.63, 32.65, 0.2, 4, 97.46, -11.89, 0.8, 2, 9, 14.33, 40.56, 0.2, 4, 95.16, -3.97, 0.8, 2, 9, 12.08, 54.23, 0.2, 4, 92.91, 9.7, 0.8, 2, 9, 16.89, 62.8, 0.1, 4, 97.72, 18.26, 0.9, 2, 9, 25.58, 68.4, 0.1, 4, 106.41, 23.86, 0.9, 2, 9, 31.39, 74.23, 0.1, 4, 112.22, 29.7, 0.9, 2, 9, 29.68, 62.43, 0.2, 4, 110.51, 17.9, 0.8, 2, 9, 35.01, 57.34, 0.2, 4, 115.84, 12.8, 0.8, 2, 9, 37.9, 49.66, 0.2, 4, 118.73, 5.13, 0.8, 2, 9, 37.31, 39.49, 0.2, 4, 118.14, -5.05, 0.8, 2, 9, 34.67, 32.01, 0.2, 4, 115.5, -12.52, 0.8, 2, 9, 25.94, 25.91, 0.2, 4, 106.78, -18.63, 0.8, 2, 9, 22.23, 28.54, 0.2, 4, 103.06, -16, 0.8, 2, 9, 18.31, 33.48, 0.2, 4, 99.15, -11.06, 0.8, 2, 9, 19.37, 56.34, 0.2, 4, 100.2, 11.8, 0.8, 2, 9, 18.02, 46.83, 0.2, 4, 98.85, 2.29, 0.8, 2, 9, 28.26, 54.1, 0.2, 4, 109.09, 9.56, 0.8, 2, 9, 28.2, 42.13, 0.2, 4, 109.03, -2.41, 0.8, 2, 9, 27.07, 33.1, 0.2, 4, 107.9, -11.43, 0.8, 2, 9, 48.22, -9.47, 0.25, 4, 129.05, -54, 0.75, 2, 9, 54.77, -17.82, 0.2, 4, 135.6, -62.35, 0.8, 2, 9, 55.78, -31.88, 0.15, 4, 136.61, -76.41, 0.85, 2, 9, 50.36, -46.96, 0.1, 4, 131.19, -91.5, 0.9, 2, 9, 42.86, -9.41, 0.25, 4, 123.69, -53.95, 0.75, 2, 9, 47.54, -18.19, 0.2, 4, 128.37, -62.72, 0.8, 2, 9, 48.63, -28.76, 0.15, 4, 129.46, -73.3, 0.85, 2, 9, 46.11, -40.59, 0.1, 4, 126.95, -85.12, 0.9, 2, 9, 43.22, -44.52, 0.1, 4, 124.05, -89.05, 0.9, 2, 9, 33.48, -9.68, 0.25, 4, 114.32, -54.21, 0.75, 2, 9, 40.55, -16.81, 0.2, 4, 121.38, -61.35, 0.8, 2, 9, 43.61, -24.68, 0.15, 4, 124.44, -69.21, 0.85, 2, 9, 41.37, -36.5, 0.1, 4, 122.2, -81.04, 0.9, 2, 9, 38.22, -43.91, 0.1, 4, 119.05, -88.44, 0.9, 3, 7, -34.86, -35.73, 0.216, 9, 21.26, -38.72, 0.0784, 4, 102.09, -83.25, 0.7056, 2, 9, 11.93, -32.09, 0.1, 4, 92.76, -76.62, 0.9, 2, 9, 7.43, -26.56, 0.15, 4, 88.27, -71.1, 0.85, 2, 9, 10.15, -16.12, 0.2, 4, 90.98, -60.66, 0.8, 2, 9, 14.6, -8.91, 0.25, 4, 95.43, -53.44, 0.75, 2, 9, 22.75, -6.19, 0.25, 4, 103.58, -50.73, 0.75, 2, 9, 18.34, -11.05, 0.25, 4, 99.17, -55.59, 0.75, 2, 9, 28.96, -13.65, 0.25, 4, 109.8, -58.18, 0.75, 2, 9, 32.43, -19.84, 0.2, 4, 113.26, -64.37, 0.8, 2, 9, 33.6, -29.18, 0.15, 4, 114.44, -73.72, 0.85, 2, 9, 30.2, -36.77, 0.1, 4, 111.03, -81.31, 0.9, 2, 9, 22.37, -34.67, 0.1, 4, 103.2, -79.21, 0.9, 2, 9, 13.3, -27.42, 0.15, 4, 94.13, -71.95, 0.85, 2, 9, 12.34, -23.44, 0.15, 4, 93.17, -67.98, 0.85, 2, 9, 13.83, -15.86, 0.2, 4, 94.66, -60.4, 0.8, 2, 9, 23.93, -10.84, 0.25, 4, 104.76, -55.37, 0.75, 2, 9, 22.34, -18.65, 0.2, 4, 103.17, -63.19, 0.8, 2, 9, 24.38, -28.15, 0.15, 4, 105.21, -72.68, 0.85, 2, 9, 95.32, 71.65, 0.1, 4, 176.15, 27.11, 0.9, 2, 9, 62.63, 78.34, 0.1, 4, 143.46, 33.8, 0.9, 2, 9, 71.2, 53.55, 0.2, 4, 152.03, 9.01, 0.8, 2, 9, 68.66, 26.48, 0.2, 4, 149.49, -18.06, 0.8, 2, 9, 90.67, 25.15, 0.2, 4, 171.51, -19.38, 0.8, 2, 9, 90.31, 51.45, 0.2, 4, 171.14, 6.92, 0.8, 2, 9, 77.66, 75.26, 0.1, 4, 158.49, 30.73, 0.9, 2, 9, 6.57, 47.21, 0.2, 4, 87.4, 2.67, 0.8, 2, 9, -13.16, 42.05, 0.2, 4, 67.67, -2.48, 0.8, 2, 9, -29.61, 27.47, 0.2, 4, 51.22, -17.07, 0.8, 2, 9, 0.93, -21.47, 0.2, 4, 81.76, -66.01, 0.8, 2, 9, -14.07, -15.94, 0.2, 4, 66.76, -60.48, 0.8, 2, 9, -29.49, -0.94, 0.25, 4, 51.34, -45.48, 0.75, 2, 9, 4.57, 66.99, 0.1, 4, 85.4, 22.45, 0.9, 2, 9, -13.95, 56.97, 0.15, 4, 66.88, 12.43, 0.85, 2, 9, 12.61, 25.77, 0.2, 4, 93.44, -18.77, 0.8, 2, 9, -6.02, 24.07, 0.2, 4, 74.81, -20.47, 0.8, 2, 9, 29.34, 101.55, 0.05, 4, 110.17, 57.02, 0.95, 2, 9, 14.09, 91.08, 0.05, 4, 94.92, 46.54, 0.95, 1, 45, 15.4, -3.47, 1, 2, 44, 28.5, -0.15, 0.72, 45, -0.15, 0.33, 0.28, 3, 44, 69.5, -6.21, 0.09255, 46, -6.86, -8.63, 0.00345, 45, 40.81, -5.9, 0.904, 1, 45, 45.97, 3.35, 1, 2, 9, 128.17, 5.06, 0.3, 4, 209, -39.48, 0.7], "hull": 67, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 0, 132, 50, 134, 134, 136, 136, 138, 138, 0, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 156, 158, 158, 160, 162, 164, 164, 166, 166, 168, 168, 170, 172, 174, 174, 176, 176, 178, 178, 180, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 208, 182, 210, 212, 212, 214, 214, 216, 216, 218, 218, 220, 220, 222, 222, 224, 210, 226, 226, 228, 228, 224, 210, 230, 230, 232, 232, 234, 234, 220, 236, 238, 238, 240, 240, 242, 244, 246, 246, 248, 248, 250, 250, 252, 254, 256, 256, 258, 258, 260, 260, 262, 26, 28, 262, 28, 28, 30, 30, 32, 30, 264, 264, 266, 266, 268, 268, 270, 270, 272, 272, 274, 274, 254, 278, 280, 280, 282, 282, 284, 284, 286, 286, 288, 288, 290, 290, 292, 292, 276, 276, 294, 294, 278, 294, 296, 296, 298, 298, 284, 154, 308, 308, 306, 152, 310, 310, 304, 300, 312, 312, 302, 314, 316, 316, 318, 320, 322, 322, 324, 326, 328, 330, 332, 124, 340, 340, 58, 114, 342, 342, 344, 72, 74, 74, 76, 344, 74, 346, 140], "width": 240, "height": 189}}, "huanan2": {"huanan2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 90, -159.2, -171.23, 1, 1, 90, -285.2, -171.23, 1, 1, 90, -285.2, 84.77, 1, 1, 90, -159.2, 84.77, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 91, "height": 186}}, "yaodai": {"yaodai": {"type": "mesh", "uvs": [0.90156, 0.05231, 0.96821, 0.03188, 0.98858, 0.11997, 0.99598, 0.19147, 1, 0.28595, 1, 0.3715, 0.95155, 0.35234, 0.9534, 0.25914, 0.89415, 0.14551, 0.85342, 0.1787, 0.88489, 0.28723, 0.82009, 0.30127, 0.80343, 0.3932, 0.74419, 0.47108, 0.67753, 0.56301, 0.59607, 0.66004, 0.52386, 0.75197, 0.46554, 0.80495, 0.40722, 0.85794, 0.33131, 0.92433, 0.2517, 1, 0.09988, 1, 0, 1, 0, 0.96487, 0.06864, 0.94065, 0.14825, 0.92087, 0.20327, 0.90311, 0.24718, 0.87203, 0.318, 0.81188, 0.39293, 0.75376, 0.45014, 0.6964, 0.36303, 0.74849, 0.25268, 0.78292, 0.20766, 0.82966, 0.18864, 0.89464, 0.14005, 0.88333, 0.08035, 0.88414, 0.05984, 0.9079, 0, 0.87001, 0, 0.81198, 0.06471, 0.74431, 0.16098, 0.67153, 0.29429, 0.61791, 0.40167, 0.55407, 0.50526, 0.48743, 0.48498, 0.3857, 0.54423, 0.29122, 0.60718, 0.24206, 0.67013, 0.19291, 0.62384, 0.15205, 0.61458, 0.07928, 0.63125, 0, 0.72937, 0.00395, 0.82195, 0, 0.34533, 0.72133, 0.63798, 0.35973, 0.76358, 0.10057, 0.72113, 0.28611, 0.54684, 0.55456, 0.23413, 0.72782, 0.75916, 0.18613, 0.45746, 0.60818, 0.49192, 0.65004, 0.53294, 0.59969, 0.56247, 0.56292, 0.56576, 0.51709, 0.65436, 0.43902, 0.69784, 0.35698, 0.73148, 0.30041, 0.75937, 0.23648, 0.7524, 0.27553, 0.72562, 0.34366, 0.70414, 0.39825, 0.66758, 0.44894, 0.61367, 0.49053, 0.62046, 0.49209, 0.5888, 0.52562, 0.60819, 0.42884, 0.56892, 0.42995, 0.57024, 0.37654, 0.59452, 0.3263, 0.65031, 0.26203, 0.68509, 0.23216, 0.7015, 0.25162, 0.67656, 0.29643, 0.63193, 0.35074, 0.59124, 0.40596, 0.54398, 0.4485], "triangles": [50, 56, 49, 56, 50, 52, 7, 3, 4, 7, 4, 6, 6, 4, 5, 0, 1, 2, 8, 0, 2, 8, 2, 3, 3, 7, 8, 19, 27, 18, 21, 22, 23, 21, 24, 25, 21, 23, 24, 19, 26, 27, 20, 26, 19, 25, 26, 20, 21, 25, 20, 32, 33, 40, 39, 40, 33, 39, 36, 38, 39, 33, 36, 34, 35, 33, 33, 35, 36, 37, 38, 36, 42, 43, 61, 59, 41, 42, 54, 59, 42, 40, 41, 59, 32, 59, 54, 32, 40, 59, 54, 42, 30, 30, 42, 61, 30, 61, 62, 31, 54, 30, 62, 61, 63, 16, 62, 15, 30, 62, 16, 29, 30, 16, 32, 54, 31, 17, 29, 16, 17, 28, 29, 18, 28, 17, 18, 27, 28, 58, 44, 65, 76, 65, 74, 58, 61, 43, 63, 61, 58, 64, 58, 65, 63, 58, 64, 76, 74, 75, 75, 74, 73, 73, 72, 13, 13, 75, 73, 14, 75, 13, 76, 75, 14, 64, 76, 14, 15, 64, 14, 63, 64, 15, 73, 66, 72, 62, 63, 15, 76, 64, 65, 69, 11, 70, 10, 11, 69, 11, 71, 70, 12, 71, 11, 72, 71, 12, 13, 72, 12, 86, 66, 77, 78, 86, 77, 44, 45, 87, 74, 77, 66, 74, 66, 73, 74, 65, 87, 44, 87, 65, 78, 77, 74, 74, 87, 78, 43, 44, 58, 79, 46, 80, 45, 46, 79, 78, 45, 79, 78, 79, 86, 87, 45, 78, 79, 80, 85, 69, 60, 9, 83, 69, 57, 69, 68, 57, 84, 83, 57, 69, 70, 68, 68, 84, 57, 68, 67, 84, 71, 68, 70, 85, 80, 84, 67, 85, 84, 68, 71, 67, 55, 85, 67, 72, 67, 71, 55, 86, 85, 66, 55, 67, 55, 66, 86, 66, 67, 72, 86, 79, 85, 82, 60, 69, 80, 81, 84, 56, 48, 49, 60, 48, 56, 82, 48, 60, 47, 48, 82, 83, 82, 69, 81, 47, 82, 81, 82, 83, 81, 80, 46, 81, 46, 47, 84, 81, 83, 56, 52, 53, 56, 53, 0, 8, 56, 0, 52, 50, 51, 9, 56, 8, 60, 56, 9, 10, 69, 9], "vertices": [3, 77, 20.32, -32.39, 0.59017, 91, -38.53, 0.6, 0.40983, 93, -76.95, 21.73, 0, 2, 77, 11.29, -64.02, 0.15094, 91, -43.25, 33.16, 0.84906, 1, 91, 14.7, 26.4, 1, 2, 75, -73.14, 100.19, 0, 91, 60.62, 16.96, 1, 3, 75, -27, 141.32, 0, 91, 120.59, 1.97, 0.00016, 92, 35.54, 9.04, 0.99984, 2, 75, 15.84, 177.3, 0, 92, 91.31, 4.64, 1, 3, 75, 20.31, 152.51, 0, 91, 156.47, -30.82, 0, 92, 77.11, -16.16, 1, 2, 91, 98.02, -13.51, 0.00501, 92, 16.41, -10.53, 0.99499, 5, 77, -24.29, 9.28, 0.35748, 67, -65.47, 42.97, 0.00896, 71, -59.91, 41.83, 0.05092, 91, 19.25, -19.11, 0.54117, 93, -28.13, 58.38, 0.04147, 6, 77, -29.16, 37.3, 0.664, 75, -38.18, 45.59, 0.00618, 67, -38.22, 34.84, 0.03206, 71, -35.67, 26.96, 0.21541, 91, 35.17, -42.68, 0.01207, 93, 0.31, 58.28, 0.07028, 2, 77, -92.71, 71.94, 0.37631, 71, 32.34, 51.73, 0.62369, 2, 77, -81, 100.25, 0.408, 71, 45.84, 24.24, 0.592, 1, 71, 106.41, 25.91, 1, 1, 72, 13.33, 14.88, 1, 1, 72, 79.7, 25.55, 1, 2, 72, 152.67, 32.74, 0.63779, 73, -7.33, 32.86, 0.36221, 1, 73, 60.63, 40.34, 1, 1, 73, 104.09, 38.46, 1, 2, 73, 147.56, 36.58, 0.95887, 74, -38.61, 25.16, 0.04113, 2, 73, 202.75, 33.17, 0.09236, 74, 14.57, 40.32, 0.90764, 1, 74, 72.65, 59.42, 1, 1, 74, 128.15, 19.33, 1, 2, 70, 192.76, -18.49, 0.0032, 74, 164.67, -7.05, 0.9968, 2, 69, 315.5, 63.11, 0.00254, 74, 151.22, -25.67, 0.99746, 2, 69, 281.61, 70.9, 0.00282, 74, 116.85, -20.39, 0.99718, 3, 73, 247.91, -35.98, 0.02878, 69, 245.8, 84.1, 0.00807, 74, 80.17, -9.85, 0.96314, 3, 73, 224.23, -22.2, 0.05227, 69, 219.33, 91.17, 0.00053, 74, 53.25, -4.74, 0.9472, 3, 73, 196.25, -17.5, 0.27051, 69, 191.09, 88.35, 0.06594, 74, 25.3, -9.63, 0.66355, 1, 73, 145.72, -13.65, 1, 1, 73, 95.22, -7.53, 1, 1, 73, 49.7, -7.69, 1, 4, 73, 100.07, -20.57, 0.66509, 69, 99.11, 60.11, 0.30163, 70, -18.45, 77.72, 0.03249, 74, -64.36, -44.53, 0.00079, 3, 73, 146.93, -48.66, 0.34943, 69, 151.7, 45.33, 0.31197, 70, 19.99, 38.9, 0.3386, 1, 70, 55.72, 30.51, 1, 1, 70, 98.54, 37.27, 1, 1, 70, 99.24, 14.15, 1, 1, 70, 109.11, -10.91, 1, 1, 70, 126.9, -14.17, 1, 1, 70, 113.07, -48.09, 1, 2, 69, 251.16, -13.44, 0.14779, 70, 77.49, -61.31, 0.85221, 2, 69, 200.35, -28.55, 0.67702, 70, 25.84, -49.36, 0.32298, 1, 69, 136.49, -37.05, 1, 1, 69, 67.9, -25.21, 1, 2, 68, 143.46, -26.15, 0.48353, 69, 3.96, -26.02, 0.51647, 5, 67, 209.69, -28.09, 0.00092, 76, 128.38, -2.96, 0.12072, 68, 81.01, -12.65, 0.8097, 71, 187.68, -97.69, 0.00395, 72, 84.89, -66.39, 0.06472, 1, 76, 65.71, -27.1, 1, 2, 75, 107.85, -13.86, 0.3848, 76, -0.56, -15.24, 0.6152, 1, 75, 64.97, -12.8, 1, 3, 75, 22.1, -11.74, 0.94875, 91, 21.73, -124.77, 0, 93, 60.59, 0.96, 0.05125, 4, 77, 50.64, 105.54, 0.00729, 75, 15.07, -44.91, 0.57606, 91, -9.64, -137.63, 0, 93, 53.56, -32.21, 0.41666, 1, 93, 19.8, -66.02, 1, 1, 77, 124.78, 39.18, 1, 1, 77, 94.4, 6.89, 1, 4, 77, 69.6, -26.79, 0.99927, 75, -118.54, -40.45, 0, 91, -81.18, -24.7, 0.00072, 93, -80.05, -27.76, 1e-05, 3, 73, 90.02, -37.25, 0.52509, 69, 93.79, 41.38, 0.44008, 70, -32.32, 64.05, 0.03483, 4, 75, 114.97, 47.34, 0.00361, 67, 109.19, -6.72, 0.89779, 76, 33.38, 36.18, 0.09804, 68, -21.72, -11.13, 0.00056, 2, 77, 36.04, 35.59, 0.36, 93, -12.76, -5.62, 0.64, 2, 67, 50.05, 8.34, 0.77179, 71, 42.8, -21.38, 0.22821, 1, 72, 109.6, -25.55, 1, 4, 73, 122.08, -76.05, 0.06312, 69, 134.92, 12.36, 0.70281, 70, -10.88, 18.51, 0.2335, 74, -25.15, -89.53, 0.00057, 5, 77, -5.61, 73.02, 0.03024, 75, -7.12, 16.16, 0.32208, 67, -16.73, -2.16, 0.41869, 71, -24.44, -14.33, 0.13019, 93, 31.37, 28.85, 0.0988, 4, 68, 158.17, 14.71, 0.29898, 72, 161.6, -37.82, 0.13427, 73, 0.39, -37.84, 0.35369, 69, 7.46, 17.26, 0.21305, 2, 72, 174.77, -9.23, 0.29413, 73, 14.05, -9.47, 0.70587, 2, 72, 137.23, -13.42, 0.65179, 73, -23.56, -13.02, 0.34821, 3, 72, 109.93, -16.63, 0.93035, 73, -50.91, -15.77, 0.05961, 69, -47.84, 25.07, 0.01004, 5, 67, 216.53, 4.69, 0.00123, 76, 141.01, 28.04, 0.00404, 68, 81.38, 20.83, 0.17098, 71, 202.72, -67.78, 0.00279, 72, 84.72, -32.92, 0.82095, 4, 67, 153.73, 20.83, 0.01391, 68, 16.65, 24.51, 0.61796, 71, 146.2, -36.01, 0.11611, 72, 19.94, -30.26, 0.25201, 5, 75, 96.22, 66.85, 0.00201, 67, 96.72, 17.29, 0.62641, 76, 25.44, 62.04, 0.05456, 68, -38.6, 10.01, 0.00031, 71, 90.2, -24.75, 0.31671, 6, 77, -54.93, 130.55, 0.05904, 75, 58.14, 54.67, 0.00034, 67, 56.74, 16.36, 0.63529, 76, -14.05, 68.34, 0.00932, 68, -77.64, 1.36, 5e-05, 71, 51.33, -15.35, 0.29596, 5, 77, -30.93, 94.07, 0.09127, 75, 18.03, 37.41, 0.16891, 67, 13.4, 11.13, 0.44396, 71, 8.1, -9.25, 0.24404, 93, 56.53, 50.1, 0.05182, 7, 77, -48.5, 112.87, 0.11111, 75, 39.61, 51.43, 0.06617, 67, 38.05, 18.48, 0.48866, 76, -32.05, 73.79, 0.00462, 68, -96.39, -0.18, 3e-05, 71, 33.82, -8.49, 0.30916, 93, 78.1, 64.12, 0.02025, 6, 77, -74.93, 150.72, 0.01981, 75, 81.49, 70.84, 0.00114, 67, 83.72, 25.28, 0.53151, 76, 14.09, 72.25, 0.03085, 68, -52.91, 15.33, 0.00018, 71, 79.69, -13.68, 0.41652, 5, 75, 115.06, 86.39, 0.00099, 67, 120.31, 30.71, 0.31699, 76, 51.06, 70.99, 0.02696, 71, 116.45, -17.85, 0.51963, 72, -14.81, -27.58, 0.13542, 4, 67, 157.29, 28.89, 0.022, 76, 87.11, 62.53, 0.0008, 71, 151.71, -29.14, 0.1766, 72, 21.73, -21.63, 0.8006, 5, 67, 191.95, 17.52, 0.01174, 76, 119.15, 45.1, 0.01535, 68, 54.78, 28.67, 0.77171, 71, 182.28, -49.05, 0.02667, 72, 58, -25.5, 0.17452, 4, 67, 191.66, 20.73, 0.01541, 76, 119.45, 48.31, 0.02014, 71, 182.82, -45.87, 0.035, 72, 57.05, -22.42, 0.92944, 4, 67, 217.47, 16.44, 0.00631, 76, 144.06, 39.43, 0.02018, 71, 206.66, -56.66, 0.01433, 72, 83.19, -21.22, 0.95918, 5, 67, 155.98, -0.92, 0.0101, 76, 80.45, 33.45, 0.23518, 68, 23.06, 3.61, 0.48749, 71, 142.77, -57.6, 0.08429, 72, 26.68, -51.05, 0.18295, 5, 67, 163.74, -16.85, 0.00681, 76, 85.21, 16.38, 0.35123, 68, 33.76, -10.51, 0.46172, 71, 146.17, -75, 0.05685, 72, 37.6, -65.01, 0.12339, 6, 75, 143.03, 31.01, 0.00302, 67, 131.51, -30.31, 0.10417, 76, 51.08, 8.95, 0.79636, 68, 4.74, -29.96, 0.0695, 71, 111.56, -79.7, 0.0085, 72, 8.89, -84.91, 0.01845, 4, 75, 110.83, 18.27, 0.32282, 67, 97.02, -33.44, 0.12701, 76, 16.58, 12.09, 0.54968, 68, -28.5, -39.71, 0.00049, 5, 75, 62.46, 10.5, 0.77235, 67, 48.42, -27.24, 0.09669, 76, -30.09, 26.95, 0.01619, 68, -77.37, -43.03, 1e-05, 71, 32.06, -55.34, 0.11475, 8, 77, -7.27, 117.96, 0.02823, 75, 37.41, 9.94, 0.72184, 67, 24.24, -20.7, 0.08919, 76, -52.7, 37.75, 0.006, 68, -102.37, -41.29, 1e-05, 71, 10.38, -42.79, 0.11798, 91, 48.27, -125.22, 0, 93, 75.91, 22.63, 0.03675, 8, 77, -21.78, 120.45, 0.01836, 75, 42.4, 23.8, 0.36611, 67, 32.93, -8.82, 0.44023, 76, -42.01, 47.87, 0.00297, 68, -96.14, -27.95, 0, 71, 21.84, -33.55, 0.15161, 91, 62.53, -121.54, 0, 93, 80.9, 36.49, 0.0207, 4, 75, 72.07, 34.03, 0.12065, 67, 64.29, -7.37, 0.71563, 68, -65.65, -20.47, 0.0001, 71, 52.51, -40.23, 0.16362, 4, 75, 112.22, 41.47, 0.05025, 67, 104.9, -11.58, 0.94121, 68, -25, -16.73, 0.00057, 71, 90.67, -54.75, 0.00797, 6, 75, 151.67, 50.64, 0.00113, 67, 145.34, -13.92, 0.28802, 76, 67.63, 22.58, 0.08083, 68, 15.13, -11.2, 0.4102, 71, 129.14, -67.42, 0.06933, 72, 18.99, -65.99, 0.15049, 5, 67, 179.37, -22.29, 0.00462, 76, 99.6, 8.21, 0.27626, 68, 50.14, -12.83, 0.59349, 71, 159.87, -84.28, 0.03823, 72, 54.02, -67.06, 0.08741], "hull": 54, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 78, 80, 80, 82, 82, 84, 84, 86, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 0, 66, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 66, 68, 68, 70, 70, 72, 72, 74, 76, 78, 74, 76, 52, 50, 50, 48, 44, 46, 48, 46, 60, 124, 124, 126, 126, 128, 128, 130, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 132, 148, 148, 130, 146, 150, 150, 152, 152, 128, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 156, 156, 174, 174, 88, 90, 88, 88, 86], "width": 329, "height": 477}}, "shou": {"shou": {"type": "mesh", "uvs": [1, 0.01152, 0.86896, 0.04452, 0.72934, 0.13724, 0.53388, 0.28495, 0.29527, 0.44209, 0.08711, 0.56781, 0, 0.65423, 0, 0.79095, 0, 0.90723, 0, 1, 0.11504, 1, 0.24196, 1, 0.37142, 0.92138, 0.56434, 0.81609, 0.73442, 0.65109, 0.88165, 0.48923, 0.97303, 0.29438, 1, 0.16552, 1, 0.00681, 0.57617, 0.52733, 0.44188, 0.59314, 0.27685, 0.74028, 0.22856, 0.88896, 0.1853, 0.96541, 0.50861, 0.64589, 0.39343, 0.74654, 0.30723, 0.89, 0.73014, 0.33289], "triangles": [22, 7, 21, 26, 23, 22, 23, 8, 22, 10, 23, 11, 23, 10, 8, 10, 9, 8, 8, 7, 22, 22, 21, 26, 6, 5, 21, 11, 23, 26, 11, 26, 12, 12, 26, 13, 26, 25, 13, 26, 21, 25, 25, 24, 13, 13, 24, 14, 7, 6, 21, 24, 25, 20, 21, 5, 20, 24, 19, 14, 14, 19, 15, 24, 20, 19, 5, 4, 20, 20, 4, 19, 4, 3, 19, 19, 27, 15, 19, 3, 27, 15, 27, 16, 16, 27, 2, 27, 3, 2, 16, 2, 17, 2, 1, 17, 1, 0, 17, 25, 21, 20], "vertices": [1, 108, 19.01, 7.72, 1, 1, 108, 25.8, 1.52, 1, 1, 108, 38.49, -2.49, 1, 1, 108, 57.96, -7.3, 1, 2, 108, 79.52, -14.21, 0.816, 112, -5.55, -10.54, 0.184, 2, 108, 97.25, -20.76, 0.672, 112, 13.15, -13.27, 0.328, 3, 108, 107.88, -21.97, 0.52941, 112, 23.8, -12.26, 0.296, 113, -7.75, -9.52, 0.17459, 2, 108, 120.84, -15.8, 0.4, 113, 6.59, -9.1, 0.6, 2, 108, 131.87, -10.56, 0.504, 113, 18.8, -8.74, 0.496, 1, 113, 28.54, -8.46, 1, 1, 113, 28.32, -0.98, 1, 2, 108, 133.91, 7.83, 0.656, 113, 28.08, 7.26, 0.344, 2, 108, 122.83, 11.88, 0.688, 113, 19.58, 15.43, 0.312, 2, 108, 107.46, 18.45, 0.84, 113, 8.16, 27.64, 0.16, 1, 108, 87.07, 20.99, 1, 1, 108, 67.61, 22.33, 1, 1, 108, 46.58, 18.9, 1, 1, 108, 33.61, 14.67, 1, 1, 108, 18.57, 7.51, 1, 2, 108, 79.76, 6.12, 0.928, 112, -9.53, 9.41, 0.072, 2, 108, 89.75, 1.21, 0.712, 112, 1.26, 6.67, 0.288, 3, 108, 108.31, -1.84, 0.13728, 112, 20.05, 7.53, 0.736, 113, 0.75, 8.73, 0.12672, 2, 108, 123.75, 2.03, 0.08, 113, 16.45, 6.05, 0.92, 2, 108, 132.21, 2.94, 0.216, 113, 24.55, 3.48, 0.784, 2, 108, 92.88, 7.5, 0.768, 112, 3.03, 13.48, 0.232, 2, 108, 105.64, 5.28, 0.808, 112, 15.97, 13.95, 0.192, 2, 108, 121.65, 6.7, 0.704, 113, 16.41, 11.17, 0.296, 1, 108, 57.02, 6.38, 1], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36, 38, 40, 40, 42, 42, 44, 44, 46, 38, 48, 48, 50, 50, 52], "width": 47, "height": 76}}, "toufa_you": {"toufa_you": {"type": "mesh", "uvs": [0.74435, 0, 0.65906, 0.1852, 0.55732, 0.34501, 0.41947, 0.50261, 0.1963, 0.6602, 0, 0.78894, 0, 0.98871, 0.22912, 1, 0.49168, 1, 0.72471, 0.94654, 0.75424, 0.8067, 0.95117, 0.73789, 1, 0.5692, 1, 0.33835, 0.92163, 0.15856, 0.80523, 0.17836, 0.76339, 0.4991, 0.57508, 0.7373, 0.30308, 0.86229], "triangles": [7, 18, 8, 9, 8, 17, 7, 6, 18, 6, 5, 18, 8, 18, 17, 9, 17, 10, 5, 4, 18, 18, 4, 17, 4, 3, 17, 10, 17, 11, 17, 16, 11, 11, 16, 12, 17, 3, 16, 16, 13, 12, 3, 2, 16, 15, 16, 2, 15, 14, 13, 15, 13, 16, 15, 2, 1, 1, 0, 15, 15, 0, 14], "vertices": [1, 33, -1.92, -7.24, 1, 2, 33, 30.4, -16.26, 0.99845, 34, -31.49, -40.77, 0.00155, 3, 33, 58.39, -27.34, 0.74245, 34, -2.08, -34.35, 0.2557, 35, -38.5, -47.37, 0.00185, 3, 33, 86.12, -42.65, 0.09658, 34, 29.48, -31.58, 0.68826, 35, -9.06, -35.67, 0.21516, 2, 34, 66.85, -36.93, 0.05754, 35, 28.28, -30.09, 0.94246, 1, 35, 60.07, -26.52, 1, 1, 35, 81.22, 0.81, 1, 1, 35, 61.21, 18.76, 1, 2, 34, 94.51, 25.4, 0.00172, 35, 36.91, 37.56, 0.99828, 2, 34, 71.11, 42.18, 0.09249, 35, 9.69, 46.92, 0.90751, 2, 34, 49.43, 30.9, 0.52855, 35, -7.85, 29.9, 0.47145, 2, 34, 26.34, 42.7, 0.96005, 35, -33.36, 34.59, 0.03995, 2, 33, 95.62, 25.58, 0.11701, 34, -0.72, 30.34, 0.88299, 2, 33, 55.7, 24.4, 0.9956, 34, -33.18, 7.09, 0.0044, 1, 33, 24.88, 14.31, 1, 1, 33, 28.71, 0.8, 1, 1, 34, 5.55, 0.78, 1, 2, 34, 51.88, 6.87, 0.30727, 35, 1.38, 7.58, 0.69273, 1, 35, 39.79, 5.21, 1], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28, 30, 32, 32, 34, 34, 36], "width": 85, "height": 126}}, "huaban3": {"huaban3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 101, 34.28, -33.1, 1, 1, 101, -20.72, -33.1, 1, 1, 101, -20.72, 29.9, 1, 1, 101, 34.28, 29.9, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 40, "height": 45}}, "qunzi": {"qunzi": {"type": "mesh", "uvs": [0.16506, 0, 0.08454, 0.10951, 0, 0.20872, 0, 0.3386, 0, 0.48291, 0.03481, 0.6164, 0.0656, 0.76793, 0.22189, 0.804, 0.23136, 0.91765, 0.25268, 1, 0.37227, 1, 0.49185, 1, 0.53922, 0.92667, 0.73103, 0.95914, 0.90864, 0.97718, 1, 0.93028, 1, 0.82565, 1, 0.70479, 1, 0.68095, 1, 0.57491, 1, 0.44323, 1, 0.34942, 0.86838, 0.26644, 0.73103, 0.15641, 0.57237, 0.09507, 0.40897, 0.0139, 0.26452, 0, 0.3773, 0.39241, 0.84417, 0.84942, 0.73131, 0.6207, 0.23435, 0.14535, 0.58319, 0.79289, 0.49719, 0.19788, 0.18896, 0.33717, 0.21207, 0.59642, 0.37067, 0.77276, 0.60452, 0.49105, 0.57413, 0.32437, 0.57782, 0.67445, 0.74444, 0.76697, 0.37703, 0.25232, 0.16737, 0.46771, 0.35903, 0.50976, 0.3921, 0.11732, 0.73577, 0.28629, 0.87548, 0.60918, 0.29419, 0.60972, 0.33843, 0.57603, 0.421, 0.54907, 0.49177, 0.51537, 0.55222, 0.45472, 0.58319, 0.3907, 0.63627, 0.31882, 0.66342, 0.26616, 0.70325, 0.23383, 0.77426, 0.23054, 0.82103, 0.27077, 0.84787, 0.31629, 0.86865, 0.39281, 0.88337, 0.45745, 0.91376, 0.53381, 0.9611, 0.63046, 0.35783, 0.61705, 0.4098, 0.64651, 0.45693, 0.68333, 0.47506, 0.75698, 0.47747, 0.7938, 0.43518, 0.83062, 0.33729, 0.84259, 0.29257, 0.87941, 0.72906, 0.42831, 0.87585, 0.74833], "triangles": [11, 10, 12, 67, 66, 12, 65, 38, 31, 13, 39, 28, 12, 31, 13, 31, 38, 39, 13, 31, 39, 12, 66, 31, 66, 65, 31, 10, 67, 12, 65, 64, 38, 49, 42, 27, 48, 42, 49, 48, 49, 38, 50, 49, 27, 38, 49, 36, 63, 48, 38, 5, 41, 34, 6, 5, 34, 7, 34, 46, 6, 34, 7, 46, 34, 47, 60, 59, 20, 60, 20, 19, 70, 60, 29, 61, 45, 60, 60, 45, 29, 19, 61, 60, 61, 19, 18, 71, 61, 18, 71, 45, 61, 17, 71, 18, 29, 45, 71, 57, 56, 22, 57, 22, 21, 58, 57, 21, 70, 44, 57, 70, 57, 58, 58, 21, 20, 59, 58, 20, 70, 58, 59, 60, 70, 59, 22, 55, 23, 54, 24, 23, 54, 23, 55, 53, 32, 54, 56, 55, 22, 44, 54, 55, 44, 55, 56, 53, 54, 44, 53, 37, 32, 52, 37, 53, 44, 52, 53, 70, 52, 44, 44, 56, 57, 43, 25, 24, 32, 43, 24, 32, 24, 54, 43, 26, 25, 30, 26, 43, 40, 30, 43, 40, 43, 32, 40, 32, 37, 27, 40, 37, 27, 37, 51, 51, 52, 70, 50, 27, 51, 36, 51, 70, 50, 51, 36, 49, 50, 36, 29, 36, 70, 51, 37, 52, 38, 36, 29, 38, 29, 39, 13, 28, 14, 39, 29, 71, 28, 39, 71, 71, 17, 16, 28, 71, 16, 28, 16, 15, 14, 28, 15, 64, 63, 38, 35, 62, 63, 35, 63, 64, 35, 64, 65, 46, 35, 7, 68, 35, 67, 68, 7, 35, 69, 7, 68, 8, 7, 69, 10, 9, 8, 10, 68, 67, 69, 68, 10, 10, 8, 69, 67, 35, 66, 35, 65, 66, 42, 41, 27, 42, 34, 41, 47, 42, 48, 47, 34, 42, 62, 47, 48, 46, 47, 62, 63, 62, 48, 35, 46, 62, 33, 30, 40, 3, 2, 33, 33, 40, 27, 41, 3, 33, 41, 33, 27, 4, 3, 41, 5, 4, 41, 30, 0, 26, 1, 0, 30, 30, 2, 1, 33, 2, 30], "vertices": [1, 49, -10.15, -18.08, 1, 1, 49, 61.33, -47.87, 1, 2, 49, 126.73, -80.27, 0.7834, 50, -50.75, -65.91, 0.2166, 4, 49, 206.33, -71.01, 0.09602, 50, 26.38, -87.65, 0.88844, 51, -123.89, -56.79, 0.00516, 98, 145.89, -197.4, 0.01038, 3, 50, 112.08, -111.8, 0.61764, 51, -47.68, -102.83, 0.21962, 98, 145.16, -108.37, 0.16274, 3, 50, 195.8, -118.4, 0.12152, 51, 31.28, -131.42, 0.29087, 98, 128.11, -26.14, 0.58761, 4, 50, 289.71, -129.83, 0.00115, 51, 118.78, -167.38, 0.03466, 52, 46.53, -161.07, 0.06133, 98, 112.87, 67.23, 0.90287, 2, 52, 83.3, -93.69, 0.48637, 98, 39.23, 88.88, 0.51363, 3, 52, 152.85, -103.64, 0.6501, 98, 34.2, 158.96, 0.0642, 114, 222.67, 60.37, 0.2857, 3, 52, 204.64, -104.19, 0.57978, 98, 23.77, 209.68, 0.01608, 114, 212.24, 111.1, 0.40414, 3, 52, 216.1, -49.17, 0.5248, 98, -32.44, 209.22, 0.00333, 114, 156.03, 110.63, 0.47186, 4, 52, 227.57, 5.86, 0.64338, 57, 172.15, -143.6, 0.00173, 99, 0.87, 147.09, 0.00016, 114, 99.83, 110.17, 0.35473, 4, 52, 187.81, 36.88, 0.17745, 57, 134.56, -109.98, 0.04728, 99, -21.02, 101.66, 0.02247, 114, 77.94, 64.74, 0.7528, 5, 52, 225.81, 121.05, 0.00519, 57, 178.1, -28.54, 0.12314, 65, 216.47, -196.29, 0, 99, -111.33, 120.95, 0.00257, 114, -12.37, 84.03, 0.8691, 2, 57, 211.28, 48.86, 0.95757, 65, 267.87, -129.58, 0.04243, 2, 57, 194.96, 98, 0.9125, 65, 264.29, -77.92, 0.0875, 2, 57, 132.78, 115.37, 0.76055, 65, 208.4, -45.63, 0.23945, 2, 57, 60.96, 135.43, 0.30335, 65, 143.83, -8.32, 0.69665, 2, 57, 46.79, 139.38, 0.18815, 65, 131.09, -0.96, 0.81185, 1, 65, 74.44, 31.77, 1, 2, 64, 115.42, 74.64, 0.36592, 65, 4.09, 72.42, 0.63408, 2, 64, 62.29, 97.6, 0.77979, 65, -46.02, 101.37, 0.22021, 3, 63, 143.21, 59.79, 0.35335, 64, -9.25, 61.13, 0.63205, 65, -121.3, 73.42, 0.0146, 2, 62, 152.96, 62.27, 0.01452, 63, 49.67, 64.96, 0.98548, 2, 62, 71.42, 43.72, 0.85518, 63, -30.56, 41.38, 0.14482, 2, 53, -8.91, 44.28, 0.21417, 62, -19.65, 33.15, 0.78583, 2, 49, -15.55, 28.35, 0.5118, 53, -37.64, -17.83, 0.4882, 5, 50, 106.44, 74.02, 0.15054, 51, -3.77, 77.82, 0.06689, 55, -12.24, -77.92, 0.01392, 54, 60.76, -66.9, 0.25439, 100, -50.81, 73.33, 0.51426, 3, 57, 127.21, 40.88, 0.62827, 65, 184.46, -116.38, 0.05214, 114, -64.99, 15.9, 0.31959, 5, 57, -22.98, 27.75, 0.1196, 56, 73.09, 31.52, 0.67897, 55, 168.15, 44.48, 0.00255, 64, 165.83, -84.72, 0.09344, 65, 35.72, -91.71, 0.10544, 3, 49, 75.17, 24.62, 0.73047, 50, -58.51, 50.71, 0.00102, 53, 43.53, -58.5, 0.26851, 6, 52, 111.22, 73.94, 0.00159, 57, 60.62, -67.88, 0.01097, 56, 137.6, -77.9, 0.00027, 100, 45.96, -173.76, 0, 99, -41, 18.95, 0.01656, 114, 57.96, -17.97, 0.97061, 4, 54, 2.24, 52.09, 0.1597, 53, 111.83, 49.42, 0.1758, 62, 84.79, -27.65, 0.51633, 63, -12.8, -29.02, 0.14817, 2, 50, 49.63, -1.93, 0.99992, 98, 57.09, -199.02, 8e-05, 3, 50, 206.53, -34.87, 0.00699, 51, 63.81, -53.74, 0.19419, 98, 44.91, -39.16, 0.79882, 3, 52, 78.69, -21.31, 0.7957, 98, -30.53, 69.03, 0.10003, 114, 157.94, -29.56, 0.10427, 4, 56, -25.01, 13.44, 0.0123, 55, 75.08, 8.59, 0.9544, 63, 147.94, -126.12, 0.00017, 64, 68.76, -107.68, 0.03312, 4, 55, -27.81, 22.47, 0.06279, 54, 85.77, 31.57, 0.64064, 63, 67.03, -61.04, 0.28596, 64, -31.31, -80, 0.0106, 6, 51, 193.91, 68.5, 0.01621, 52, 39.16, 86.38, 0.07669, 57, -10.45, -50.65, 0.12236, 56, 70.94, -47.85, 0.45412, 100, 43.43, -100.68, 0.03334, 99, -37.87, -54.1, 0.29728, 3, 57, 65.6, 9.42, 0.23394, 65, 116.96, -131.51, 0.00425, 114, -17.7, -34.58, 0.76182, 5, 49, 132.98, 98.86, 0.01892, 50, 23.21, 97.35, 0.0587, 54, -6.81, -12.99, 0.38105, 53, 126.74, -14.57, 0.54031, 100, -50.94, 159.77, 0.00102, 3, 50, 124.39, -33.55, 0.63465, 51, -15.03, -30.65, 0.30196, 98, 66.57, -118.4, 0.06339, 4, 50, 173.8, 46.12, 0.01149, 51, 53.76, 33.03, 0.67709, 54, 111.94, -118.83, 0.02944, 100, -59.4, 0.92, 0.28198, 2, 53, 49.5, 17.4, 0.58844, 62, 15.03, -20.99, 0.41156, 1, 63, 106.23, 8.15, 1, 4, 57, -11.6, 94.93, 0.09275, 56, 96.68, 95.44, 0.08209, 64, 186.19, -19.7, 0.04935, 65, 63.47, -29.48, 0.77582, 3, 51, 90.79, -24.95, 0.29001, 98, 6.24, -31.27, 0.54634, 99, 95.76, -92.94, 0.16365, 6, 51, 83.75, 3.6, 0.76984, 56, -33.28, -121.92, 0.00054, 54, 137.81, -151.93, 9e-05, 98, -14.37, -52.23, 0.216, 100, -69.08, -39.96, 0.0135, 99, 75.14, -113.9, 3e-05, 6, 51, 89.58, 45.42, 0.47053, 52, -51.6, 30, 0.01517, 56, -31.03, -79.76, 0.02902, 54, 149.07, -111.24, 0.00262, 100, -30.27, -23.33, 0.47832, 99, 36.47, -130.85, 0.00434, 5, 52, -65.17, 66.8, 0.00184, 56, -34.97, -40.73, 0.0109, 55, 75.3, -46.49, 0.02659, 100, 2.99, -2.54, 0.95994, 99, 3.38, -151.91, 0.00072, 4, 50, 165.75, 142.73, 0.00042, 55, 46.89, -9.06, 0.56238, 54, 142.14, -26.71, 0.00396, 100, 31.4, 34.89, 0.43324, 4, 55, 12.76, 15.57, 0.82021, 54, 120.39, 9.33, 0.08793, 63, 98.19, -87.92, 0.04992, 64, 7.95, -92.33, 0.04194, 4, 55, -23.26, 51.52, 0.12851, 54, 101.34, 56.52, 0.23565, 63, 85.95, -38.53, 0.58443, 64, -22.86, -51.83, 0.05141, 4, 55, -51.13, 72.55, 0.00509, 54, 83.94, 86.78, 0.02219, 63, 72.96, -6.12, 0.97197, 64, -47.63, -27.23, 0.00075, 1, 63, 72.92, 21.23, 1, 2, 63, 95.82, 45.59, 0.92794, 64, -47.13, 29.31, 0.07206, 3, 63, 128.84, 42.59, 0.55302, 64, -15.62, 39.64, 0.4432, 65, -130.12, 52.81, 0.00378, 3, 63, 157.28, 30.79, 0.12081, 64, 15.16, 40.08, 0.85636, 65, -99.49, 49.69, 0.02283, 2, 64, 62.37, 30.32, 0.91316, 65, -53.73, 34.53, 0.08684, 2, 64, 101.73, 20.84, 0.68194, 65, -15.73, 20.56, 0.31806, 1, 65, 32.21, 9.36, 1, 4, 57, 11.87, 130.15, 0.00354, 56, 126.25, 125.72, 0.00035, 64, 214.21, 12.03, 9e-05, 65, 94.97, -1.21, 0.99602, 5, 51, 110.13, -1.68, 0.66363, 52, -16.6, -7.62, 0.00018, 98, -23.7, -27, 0.01286, 100, -59.96, -65.27, 0.128, 99, 65.81, -88.66, 0.19533, 5, 51, 138.32, 9.82, 0.1357, 52, 6.18, 12.58, 0.38633, 56, 20.56, -111.06, 0.02105, 100, -35.54, -83.44, 0.25926, 99, 41.24, -70.69, 0.19766, 6, 51, 169.22, 17.03, 0.03106, 52, 32.94, 29.64, 0.61248, 57, -20.45, -106.85, 0.00259, 56, 50.73, -101.24, 0.09992, 100, -13.38, -106.16, 0.03128, 99, 18.9, -48.15, 0.22268, 5, 52, 79.16, 28.71, 0.20587, 56, 95.26, -113.69, 0.00412, 100, -4.86, -151.6, 0.00059, 99, 10, -2.79, 0.53696, 114, 108.96, -39.71, 0.25246, 4, 52, 101.64, 25.19, 0.12259, 57, 47.79, -115.88, 0.00518, 99, 8.68, 19.92, 0.45089, 114, 107.64, -17, 0.42135, 4, 52, 119.83, 1.09, 0.25893, 57, 64.33, -141.14, 0.00013, 99, 28.37, 42.81, 0.21359, 114, 127.33, 5.89, 0.52735, 3, 52, 117.67, -45.45, 0.59231, 98, -15.2, 112.24, 0.0824, 114, 173.27, 13.65, 0.32529, 3, 52, 135.63, -70.66, 0.57474, 98, 5.63, 135.13, 0.08153, 114, 194.1, 36.54, 0.34373, 5, 56, -33.85, 83.06, 0.02459, 55, 53.53, 75.37, 0.27933, 54, 181.34, 48.37, 0.00036, 63, 164.01, -57.81, 0.01846, 64, 56.45, -38.6, 0.67727, 4, 57, 71.14, 72, 0.66261, 56, 173.77, 57.63, 0.00169, 64, 265.07, -53.61, 0.00025, 65, 137.9, -72.28, 0.33545], "hull": 27, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 0, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 34, 36, 36, 38, 122, 36, 92, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138], "width": 343, "height": 450}}, "yezi": {"yezi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 88, 43.02, -37.01, 1, 1, 88, 9.12, -8.96, 1, 1, 88, 37.17, 24.94, 1, 1, 88, 71.07, -3.11, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}}, "shoude": {"shoude": {"type": "clipping", "end": "shoude", "vertexCount": 13, "vertices": [126.72, -27.51, 104.74, -14.89, 86.54, -7.81, 67.64, -2.6, 43.13, 3.48, 25.97, 12.67, 20.27, 29.98, 55.95, 42.35, 75.28, 55.19, 115.5, 52.06, 164.5, 15.63, 170.2, -18.3, 139.97, -36.82], "color": "ce3a3aff"}}}}], "animations": {"animation": {"slots": {"huaban3": {"color": [{"color": "ffffff7f", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.9, "color": "ffffff00", "curve": "stepped"}, {"time": 2, "color": "ffffff00", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "color": "ffffff7f", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4.5667, "color": "ffffff00", "curve": "stepped"}, {"time": 4.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "color": "ffffff7f", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 7.2333, "color": "ffffff00", "curve": "stepped"}, {"time": 7.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "color": "ffffff7f"}]}, "huanan2": {"color": [{"color": "ffffff71", "curve": 0.378, "c2": 0.52, "c3": 0.747}, {"time": 0.5667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffffff", "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 2.6667, "color": "ffffff71", "curve": 0.378, "c2": 0.52, "c3": 0.747}, {"time": 3.2333, "color": "ffffff00", "curve": "stepped"}, {"time": 3.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "color": "ffffffff", "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 5.3333, "color": "ffffff71", "curve": 0.378, "c2": 0.52, "c3": 0.747}, {"time": 5.9, "color": "ffffff00", "curve": "stepped"}, {"time": 6, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "color": "ffffffff", "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 8, "color": "ffffff71"}]}, "huaban": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 5.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 7, "color": "ffffff00"}]}}, "bones": {"bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.11, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 1.11, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 4.72, "curve": 0.25, "c3": 0.75}, {"time": 8}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "y": -1.46, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "y": -1.46, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "y": -1.46, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8}]}, "bone3": {"rotate": [{"angle": 0.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -0.06, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.6667, "angle": 1.06, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 0.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": -0.06, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 4.3333, "angle": 1.06, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 0.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667, "angle": -0.06, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 7, "angle": 1.06, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": 0.15}], "translate": [{"x": 0.17, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.42, "curve": 0.356, "c2": 0.41, "c3": 0.711, "c4": 0.82}, {"time": 2.6667, "x": 0.17, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 1.42, "curve": 0.356, "c2": 0.41, "c3": 0.711, "c4": 0.82}, {"time": 5.3333, "x": 0.17, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 7, "x": 1.42, "curve": 0.356, "c2": 0.41, "c3": 0.711, "c4": 0.82}, {"time": 8, "x": 0.17}]}, "bone4": {"rotate": [{"angle": 2.51, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": 1.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2, "angle": 3.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 2.51, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": 1.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": 3.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 2.51, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6, "angle": 1.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7.3333, "angle": 3.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": 2.51}], "translate": [{"x": 0.62, "y": -0.31, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.69, "y": -0.85, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 2.6667, "x": 0.62, "y": -0.31, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 1.69, "y": -0.85, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 5.3333, "x": 0.62, "y": -0.31, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": 1.69, "y": -0.85, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 8, "x": 0.62, "y": -0.31}]}, "bone5": {"translate": [{"x": 1.89, "y": 0.62}, {"time": 0.5}, {"time": 1.8333, "x": 5.04, "y": 1.67}, {"time": 2.6667, "x": 1.89, "y": 0.62}, {"time": 3.1667}, {"time": 4.5, "x": 5.04, "y": 1.67}, {"time": 5.3333, "x": 1.89, "y": 0.62}, {"time": 5.8333}, {"time": 7.1667, "x": 5.04, "y": 1.67}, {"time": 8, "x": 1.89, "y": 0.62}]}, "bone6": {"translate": [{"x": 0.14, "y": -2.18, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5}, {"time": 1.8333, "x": 0.4, "y": -6.5, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": 0.14, "y": -2.18, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667}, {"time": 4.5, "x": 0.4, "y": -6.5, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "x": 0.14, "y": -2.18, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 5.8333}, {"time": 7.1667, "x": 0.4, "y": -6.5, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8, "x": 0.14, "y": -2.18}]}, "bone11": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -0.01, "y": 0.73, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -0.01, "y": 0.73, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": -0.01, "y": 0.73, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone12": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 6.57, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "y": 6.57, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "y": 6.57, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone13": {"rotate": [{"angle": -4.12}]}, "bone17": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": -1.74, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -1.74, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "angle": -3.8, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8}]}, "bone18": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": -1.74, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -1.74, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "angle": -1.74, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8}]}, "bone19": {"rotate": [{"angle": -0.93, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 3.73, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -0.93, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 3.73, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -0.93, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 4.53, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -0.93}]}, "bone20": {"rotate": [{"angle": -0.93, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 3.73, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -0.93, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 3.73, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -0.93, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 3.73, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -0.93}]}, "bone21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.66, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 4.66, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 5.46, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone22": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.66, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 4.66, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 4.66, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone24": {"rotate": [{"angle": -1.34, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5}, {"time": 1.8333, "angle": -3.99, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -1.34, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667}, {"time": 4.5, "angle": -3.99, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "angle": -1.34, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 5.8333}, {"time": 7.1667, "angle": -10.24, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8, "angle": -1.34}]}, "bone25": {"rotate": [{"angle": -0.74, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333}, {"time": 1.6667, "angle": -3.99, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.74, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3}, {"time": 4.3333, "angle": -3.99, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.74, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667}, {"time": 7, "angle": -3.99, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -0.74}]}, "bone26": {"rotate": [{"angle": -1.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -3.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -3.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -1.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -3.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -1.99}]}, "bone27": {"rotate": [{"angle": -3.25, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1}, {"time": 2.3333, "angle": -3.99, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -3.25, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667}, {"time": 5, "angle": -3.99, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -3.25, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.3333}, {"time": 7.6667, "angle": -3.99, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8, "angle": -3.25}]}, "bone28": {"rotate": [{"angle": -1.34, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5}, {"time": 1.8333, "angle": -3.99, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -1.34, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667}, {"time": 4.5, "angle": -3.99, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "angle": -1.34, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 5.8333}, {"time": 7.1667, "angle": -11.87, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8, "angle": -1.34}]}, "bone29": {"rotate": [{"angle": -0.74, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -3.99, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.74, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -3.99, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.74, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -3.99, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -0.74}]}, "bone30": {"rotate": [{"angle": -1.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}, {"time": 2, "angle": -3.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333}, {"time": 4.6667, "angle": -3.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -1.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6}, {"time": 7.3333, "angle": -3.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -1.99}]}, "bone31": {"rotate": [{"angle": -3.25, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -3.99, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -3.25, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -3.99, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -3.25, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": -3.99, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8, "angle": -3.25}]}, "bone32": {"rotate": [{"angle": -1.34, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -3.99, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -1.34, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -3.99, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "angle": -1.34, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "angle": -6.19, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8, "angle": -1.34}]}, "bone33": {"rotate": [{"angle": -0.74, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333}, {"time": 1.6667, "angle": -3.99, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.74, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3}, {"time": 4.3333, "angle": -3.99, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.74, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667}, {"time": 7, "angle": -3.99, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -0.74}]}, "bone34": {"rotate": [{"angle": -1.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}, {"time": 2, "angle": -3.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333}, {"time": 4.6667, "angle": -3.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -1.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6}, {"time": 7.3333, "angle": -3.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -1.99}]}, "bone35": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.99, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -3.99, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -3.99, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone36": {"rotate": [{"angle": -0.62, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -3.38, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.62, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -3.38, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.62, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -7.06, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -0.62}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 0.907, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "y": 0.907, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "y": 0.907, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone37": {"rotate": [{"angle": -1.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -3.38, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -3.38, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -1.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -3.38, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -1.69}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 0.907, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "y": 0.907, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "y": 0.907, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone38": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.38, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -3.38, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -3.38, "curve": 0.25, "c3": 0.75}, {"time": 8}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 0.907, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "y": 0.907, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "y": 0.907, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone39": {"rotate": [{"angle": -3.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -7.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -3.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -7.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -3.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -12.12, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -3.65}]}, "bone40": {"rotate": [{"angle": -3.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -7.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -3.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -7.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -3.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -12.12, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -3.65}]}, "bone41": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -7.3, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -7.3, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -7.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.3333, "angle": -8.47, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8}]}, "bone42": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -7.3, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -7.3, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -7.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.3333, "angle": -8.47, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8}]}, "bone43": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -7.3, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -7.3, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -20.45, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone44": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -7.3, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -7.3, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -7.3, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone45": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -7.3, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -7.3, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -7.3, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone47": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -12.9, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -12.9, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -16.25, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone51": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.51, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -1.51, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -1.51, "curve": 0.25, "c3": 0.75}, {"time": 8}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "y": -4.12, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "y": -4.12, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "y": -4.12, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8}]}, "bone52": {"rotate": [{"angle": -0.28, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -1.51, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.28, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -1.51, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.28, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -1.51, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -0.28}]}, "bone53": {"rotate": [{"angle": -0.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}, {"time": 2, "angle": -1.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -0.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333}, {"time": 4.6667, "angle": -1.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -0.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6}, {"time": 7.3333, "angle": -1.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -0.75}]}, "bone54": {"rotate": [{"angle": -1.23, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1}, {"time": 2.3333, "angle": -1.51, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -1.23, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667}, {"time": 5, "angle": -1.51, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -1.23, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.3333}, {"time": 7.6667, "angle": -1.51, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8, "angle": -1.23}]}, "bone55": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.51, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -1.51, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -1.51, "curve": 0.25, "c3": 0.75}, {"time": 8}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "y": -4.12, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "y": -4.12, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "y": -4.12, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8}]}, "bone56": {"rotate": [{"angle": -0.28, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333}, {"time": 1.6667, "angle": -1.51, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.28, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3}, {"time": 4.3333, "angle": -1.51, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.28, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667}, {"time": 7, "angle": -1.51, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -0.28}]}, "bone57": {"rotate": [{"angle": -0.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}, {"time": 2, "angle": -1.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -0.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333}, {"time": 4.6667, "angle": -1.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -0.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6}, {"time": 7.3333, "angle": -1.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -0.75}]}, "bone58": {"rotate": [{"angle": -1.23, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -1.51, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -1.23, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -1.51, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -1.23, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": -1.51, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8, "angle": -1.23}]}, "bone59": {"rotate": [{"angle": -1.51, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -1.51, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -1.51, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -1.51}]}, "bone64": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.71, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -2.71, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -2.71, "curve": 0.25, "c3": 0.75}, {"time": 8}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 3.77, "y": -3.09, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 3.77, "y": -3.09, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": 3.77, "y": -3.09, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone65": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 5.64, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 5.64, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 16.3, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone60": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.42, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -2.42, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -2.42, "curve": 0.25, "c3": 0.75}, {"time": 8}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 2.47, "y": -1.21, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 2.47, "y": -1.21, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": 2.47, "y": -1.21, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone61": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -4.72, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -4.72, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -13.26, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone62": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.51, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -1.51, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -1.51, "curve": 0.25, "c3": 0.75}, {"time": 8}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "y": -4.12, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "y": -4.12, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "y": -4.12, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8}]}, "bone63": {"rotate": [{"angle": -0.28, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -1.51, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.28, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -1.51, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.28, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -1.51, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -0.28}]}, "bone66": {"rotate": [{"angle": -0.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -1.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -0.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -1.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -0.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -1.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -0.75}]}, "bone67": {"rotate": [{"angle": -1.23, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1}, {"time": 2.3333, "angle": -1.51, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -1.23, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667}, {"time": 5, "angle": -1.51, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -1.23, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.3333}, {"time": 7.6667, "angle": -1.51, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8, "angle": -1.23}]}, "bone68": {"rotate": [{"angle": -4.32, "curve": 0.269, "c2": 0.11, "c3": 0.664, "c4": 0.66}, {"time": 0.8333, "angle": -1.08, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.2333, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": -4.44, "curve": 0.311, "c3": 0.646, "c4": 0.35}, {"time": 2.6667, "angle": -4.32, "curve": 0.269, "c2": 0.11, "c3": 0.664, "c4": 0.66}, {"time": 3.5, "angle": -1.08, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.9, "curve": 0.25, "c3": 0.75}, {"time": 5.2333, "angle": -4.44, "curve": 0.311, "c3": 0.646, "c4": 0.35}, {"time": 5.3333, "angle": -4.32, "curve": 0.269, "c2": 0.11, "c3": 0.664, "c4": 0.66}, {"time": 6.1667, "angle": -1.08, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 6.5667, "curve": 0.25, "c3": 0.75}, {"time": 7.9, "angle": -4.44, "curve": 0.311, "c3": 0.646, "c4": 0.35}, {"time": 8, "angle": -4.32}]}, "bone69": {"rotate": [{"angle": -0.82, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333}, {"time": 1.6667, "angle": -4.44, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.82, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3}, {"time": 4.3333, "angle": -4.44, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.82, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667}, {"time": 7, "angle": -15.91, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -0.82}]}, "bone70": {"rotate": [{"angle": -2.22, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}, {"time": 2, "angle": -4.44, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -2.22, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333}, {"time": 4.6667, "angle": -4.44, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -2.22, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6}, {"time": 7.3333, "angle": -4.44, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -2.22}]}, "bone71": {"rotate": [{"angle": -3.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -4.44, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -3.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -4.44, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -3.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": -4.44, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8, "angle": -3.62}]}, "bone72": {"rotate": [{"angle": -4.26, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "angle": -4.55, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.8333, "angle": -2.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.5, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": -4.26, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 2.8333, "angle": -4.55, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.5, "angle": -2.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.1667, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.3333, "angle": -4.26, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 5.5, "angle": -4.55, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.1667, "angle": -2.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.8333, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 8, "angle": -4.26}]}, "bone73": {"rotate": [{"angle": -1.22, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -0.38, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.6667, "angle": -4.93, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -1.22, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": -0.38, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 4.3333, "angle": -4.93, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -1.22, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667, "angle": -0.38, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 7, "angle": -16.4, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -1.22}]}, "bone74": {"rotate": [{"angle": -3.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -1.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2, "angle": -5.59, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -3.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": -1.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": -5.59, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -3.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6, "angle": -1.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7.3333, "angle": -5.59, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -3.31}]}, "bone75": {"rotate": [{"angle": -5.4, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "angle": -1.69, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 2.3333, "angle": -6.24, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -5.4, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "angle": -1.69, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 5, "angle": -6.24, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -5.4, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.3333, "angle": -1.69, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 7.6667, "angle": -6.24, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8, "angle": -5.4}]}, "bone76": {"rotate": [{"angle": -4.38, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333}, {"time": 2.1667, "angle": -6.59, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": -4.38, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 3.5}, {"time": 4.8333, "angle": -6.59, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": -4.38, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 6.1667}, {"time": 7.5, "angle": -6.59, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 8, "angle": -4.38}]}, "bone77": {"rotate": [{"angle": -1.22, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333}, {"time": 1.6667, "angle": -6.59, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -1.22, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3}, {"time": 4.3333, "angle": -6.59, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -1.22, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667}, {"time": 7, "angle": -6.59, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -1.22}]}, "bone78": {"rotate": [{}, {"time": 1.3333, "angle": 4.12}, {"time": 2.6667}, {"time": 4, "angle": 4.12}, {"time": 5.3333}, {"time": 6.6667, "angle": 4.12}, {"time": 8}]}, "bone79": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.21, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -1.21, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -4.44, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone80": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.78, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -1.78, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -5.01, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone81": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.93, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -2.93, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -6.16, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone82": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 0.34, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}, {"time": 4, "angle": 0.34, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}, {"time": 6.6667, "angle": 0.34, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone83": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 0.34, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone84": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 0.34, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone88": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -6.62, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -6.62, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -6.62, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone89": {"rotate": [{"angle": -1.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": -1.24, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2, "angle": -2.69, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -1.24, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": -2.69, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -1.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "angle": -1.24, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7.3333, "angle": -2.69, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -1.34}]}, "bone90": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.32, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 7.32, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 7.32, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone91": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 56.21, "y": -138.09, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 56.21, "y": -138.09, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "x": 56.21, "y": -138.09, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone92": {"translate": [{"x": 51.87, "y": -127.43, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.3333, "x": 56.21, "y": -138.09, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 2.6667, "x": 51.87, "y": -127.43, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 3, "x": 56.21, "y": -138.09, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 5.3333, "x": 51.87, "y": -127.43, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 5.6667, "x": 56.21, "y": -138.09, "curve": 0.25, "c3": 0.75}, {"time": 6, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 8, "x": 51.87, "y": -127.43}]}, "bone95": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.82, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 7.82, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 7.82, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone49": {"rotate": [{"angle": -19.37, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.3333, "angle": -16.01, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "angle": -14.02, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2, "angle": -24.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -19.37, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 3, "angle": -16.01, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.3333, "angle": -14.02, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": -24.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -19.37, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -28.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -19.37}]}, "bone50": {"rotate": [{"angle": -31.67, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "angle": -28.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1, "angle": -22.87, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 2.3333, "angle": -33.64, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -31.67, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 3, "angle": -28.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.6667, "angle": -22.87, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 5, "angle": -33.64, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -31.67, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": -28.04, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8, "angle": -31.67}]}, "bone48": {"rotate": [{"angle": -7.16, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -5.17, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.6667, "angle": -15.94, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -7.16, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": -5.17, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 4.3333, "angle": -15.94, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -7.16, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -28.04, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -7.16}]}, "bone14": {"rotate": [{}, {"time": 1.3333, "angle": 4.68}, {"time": 2.6667}, {"time": 4, "angle": 4.68}, {"time": 5.3333}, {"time": 6.6667, "angle": 5.48}, {"time": 8}]}, "bone15": {"rotate": [{}, {"time": 1.3333, "angle": 4.68}, {"time": 2.6667}, {"time": 4, "angle": 4.68}, {"time": 5.3333}, {"time": 6.6667, "angle": 5.48}, {"time": 8}]}, "bone93": {"rotate": [{"angle": -3.72, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333}, {"time": 2.1667, "angle": -5.6, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": -3.72, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 3.5}, {"time": 4.8333, "angle": -5.6, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": -3.72, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 6.1667}, {"time": 7.5, "angle": -5.6, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 8, "angle": -3.72}]}, "bone94": {"rotate": [{"angle": -1.03, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -5.6, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -1.03, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -5.6, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -1.03, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": 28.69, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -1.03}]}, "bone100": {"translate": [{"x": 2.86, "y": -0.35, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 15.5, "y": -1.92, "curve": "stepped"}, {"time": 1.6667, "x": 15.5, "y": -1.92, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 2.86, "y": -0.35, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 15.5, "y": -1.92, "curve": "stepped"}, {"time": 4.3333, "x": 15.5, "y": -1.92, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 2.86, "y": -0.35, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": 15.5, "y": -1.92, "curve": "stepped"}, {"time": 7, "x": 15.5, "y": -1.92, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "x": 2.86, "y": -0.35}]}, "bone98": {"translate": [{"y": 0.15, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": -4.56, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "y": 0.15, "curve": 0.25, "c3": 0.75}, {"time": 4, "y": -4.56, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "y": 0.15, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "y": -4.56, "curve": 0.25, "c3": 0.75}, {"time": 8, "y": 0.15}]}, "bone85": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 4.2, "y": -0.13, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 4.2, "y": -0.13, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": 4.2, "y": -0.13, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone86": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 6, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 6, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 6, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone87": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 6, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 6, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 6, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone102": {"translate": [{}, {"time": 1.3333, "x": 15.71, "y": -7.99, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 15.71, "y": -7.99, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": 44.78, "y": -23.33, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone103": {"translate": [{}, {"time": 1.3333, "x": 4.95, "y": 13.08, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 4.95, "y": 13.08, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": 34.01, "y": -2.26, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone104": {"translate": [{}, {"time": 1.3333, "x": -7.07, "y": -19.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": -7.07, "y": -19.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": -2.1, "y": -26.86, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone105": {"translate": [{"x": 11.19, "y": -23.29, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 1.6667, "x": 49.85, "y": -103.8, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 2.6667, "x": 11.19, "y": -23.29, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 4.3333, "x": 49.85, "y": -103.8, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 5.3333, "x": 11.19, "y": -23.29, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 7, "x": 49.85, "y": -103.8, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 8, "x": 11.19, "y": -23.29}]}, "bone107": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 8.39, "y": -1.86, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 8.39, "y": -1.86, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": 8.39, "y": -1.86, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone106": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -5.78, "y": -1.98, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -5.78, "y": -1.98, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": -5.78, "y": -1.98, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone7": {"rotate": [{"angle": -1.05, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -5.71, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -1.05, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -5.71, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -1.05, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -5.71, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -1.05}]}, "bone8": {"rotate": [{"angle": -2.01, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -4.03, "curve": "stepped"}, {"time": 2, "angle": -4.03, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -2.01, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -4.03, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -2.01, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -4.03, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -2.01}]}, "bone9": {"rotate": [{"angle": -3.96, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.03, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -3.96, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 1.03, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -3.96, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 1.03, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -3.96}]}, "bone96": {"translate": [{"x": 10.51, "y": 14.54}, {"time": 1}, {"time": 2.3333, "x": 14.02, "y": 19.39}, {"time": 2.6667, "x": 10.51, "y": 14.54}, {"time": 3.6667}, {"time": 5, "x": 14.02, "y": 19.39}, {"time": 5.3333, "x": 10.51, "y": 14.54}, {"time": 6.3333}, {"time": 7.6667, "x": 14.02, "y": 19.39}, {"time": 8, "x": 10.51, "y": 14.54}]}, "bone110": {"translate": [{"x": 10.5, "y": 9.43}, {"time": 1}, {"time": 2.3333, "x": 14, "y": 12.58}, {"time": 2.6667, "x": 10.5, "y": 9.43}, {"time": 3.6667}, {"time": 5, "x": 14, "y": 12.58}, {"time": 5.3333, "x": 10.5, "y": 9.43}, {"time": 6.3333}, {"time": 7.6667, "x": 14, "y": 12.58}, {"time": 8, "x": 10.5, "y": 9.43}]}, "bone111": {"rotate": [{"angle": -2.6, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -7.67, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.1667, "angle": -6.25, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -2.6, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -7.67, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 4.8333, "angle": -6.25, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -2.6, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "angle": -7.67, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 7.5, "angle": -6.25, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -2.6}], "translate": [{"x": 2.66, "y": 1.84, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 7.85, "y": 5.43, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.1667, "x": 6.4, "y": 4.43, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 2.66, "y": 1.84, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 7.85, "y": 5.43, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 4.8333, "x": 6.4, "y": 4.43, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 2.66, "y": 1.84, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "x": 7.85, "y": 5.43, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 7.5, "x": 6.4, "y": 4.43, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 8, "x": 2.66, "y": 1.84}]}, "bone112": {"rotate": [{}, {"time": 1.3333, "angle": 12.66}, {"time": 2.6667}, {"time": 4, "angle": 12.66}, {"time": 5.3333}, {"time": 6.6667, "angle": 12.66}, {"time": 8}]}, "bone113": {"rotate": [{}, {"time": 1.3333, "angle": 12.66}, {"time": 2.6667}, {"time": 4, "angle": 12.66}, {"time": 5.3333}, {"time": 6.6667, "angle": 12.66}, {"time": 8}]}, "bone97": {"translate": [{"time": 5.3333}, {"time": 6.7, "x": 2.55, "y": -5}, {"time": 8}]}, "bone114": {"translate": [{"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": 61.13, "y": -31.66, "curve": 0.25, "c3": 0.75}, {"time": 8}]}}, "deform": {"default": {"shou_you": {"shou_you": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 88, "vertices": [-4.76096, -1.60536, 3.20605, 3.26791, -4.41202, 2.40361, -8.83014, 0.44887, 5.75358, 6.71324, 7.35945, 3.27721, -5.66839, 6.78536, -6.71349, -0.48834, 3.71801, 5.61098, -4.91823, 4.59548, -3.87497, -2.08192, 0.72145, 4.33923, -4.15997, 1.42967, -2.05534, 0.22545, 1.4352, 1.48753, -1.23007, 1.66183, 2.04311, 1.69477, 0.61453, 1.22215, 0.20713, 1.42702, 4.56727, 3.1331, 2.76766, 0.83965, 1.83162, 2.23764], "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "offset": 88, "vertices": [-4.76096, -1.60536, 3.20605, 3.26791, -4.41202, 2.40361, -8.83014, 0.44887, 5.75358, 6.71324, 7.35945, 3.27721, -5.66839, 6.78536, -6.71349, -0.48834, 3.71801, 5.61098, -4.91823, 4.59548, -3.87497, -2.08192, 0.72145, 4.33923, -4.15997, 1.42967, -2.05534, 0.22545, 1.4352, 1.48753, -1.23007, 1.66183, 2.04311, 1.69477, 0.61453, 1.22215, 0.20713, 1.42702, 4.56727, 3.1331, 2.76766, 0.83965, 1.83162, 2.23764], "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "offset": 128, "vertices": [11.54506, 3.68503, 8.26739, 8.86124, 3.02146, 0.51871, 2.99675, 0.64679, 2.29762, 2.03011, 1.80871, 2.4752, 3.02789, -0.47899], "curve": 0.334, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 8}]}, "shoude": {"shoude": [{"vertices": [2.10207, 7.81176, 1.86409, 2.77587, 0.57899, 4.12389, -2.02103, 4.91445, -0.42294, 3.0476, 2.2485, 1.35641, 1.40337, -7.41018, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.50038, 8.33173], "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "vertices": [1.74338, 5.69986, 2.19095, 1.31368, -0.3502, 2.26003, -2.64822, 2.92649, -2.24576, 2.88203, 2.50938, -0.12085, 2.28955, -7.92443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.74824, 4.25628], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "vertices": [3.09506, 18.12982, 6.6998, 7.01335, 10.75717, 5.63326, 13.58981, 6.64618, 16.97931, 5.73049, 4.13028, 4.62582, 1.91022, -6.60102, -18.43335, -3.46136, 0, 0, 0, 0, 0, 0, 0, 0, 13.70761, 25.26369], "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.6667, "vertices": [7.76385, 22.96776, 3.25214, 10.32595, 5.9749, 10.56561, 6.70148, 10.37255, 13.13797, 9.39945, 9.18139, 5.61749, 1.87506, -6.81718, -11.68837, -2.19481, 0, 0, 0, 0, 0, 0, 0, 0, 11.47688, 25.30138], "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 2, "vertices": [6.19345, 18.86413, 5.01094, 7.50755, 10.26088, 7.0386, 4.60565, 9.58471, 6.63943, 9.78791, 6.81813, 4.89733, 3.67123, -7.90888, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7.61126, 25.36669], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "vertices": [2.10207, 7.81176, 1.86409, 2.77587, 0.57899, 4.12389, -2.02103, 4.91445, -0.42294, 3.0476, 2.2485, 1.35641, 1.40337, -7.41018, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.50038, 8.33173], "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "vertices": [1.74338, 5.69986, 2.19095, 1.31368, -0.3502, 2.26003, -2.64822, 2.92649, -2.24576, 2.88203, 2.50938, -0.12085, 2.28955, -7.92443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.74824, 4.25628], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4, "vertices": [3.09506, 18.12982, 6.6998, 7.01335, 10.75717, 5.63326, 13.58981, 6.64618, 16.97931, 5.73049, 4.13028, 4.62582, 1.91022, -6.60102, -18.43335, -3.46136, 0, 0, 0, 0, 0, 0, 0, 0, 13.70761, 25.26369], "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 4.3333, "vertices": [7.76385, 22.96776, 3.25214, 10.32595, 5.9749, 10.56561, 6.70148, 10.37255, 13.13797, 9.39945, 9.18139, 5.61749, 1.87506, -6.81718, -11.68837, -2.19481, 0, 0, 0, 0, 0, 0, 0, 0, 11.47688, 25.30138], "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 4.6667, "vertices": [6.19345, 18.86413, 5.01094, 7.50755, 10.26088, 7.0386, 4.60565, 9.58471, 6.63943, 9.78791, 6.81813, 4.89733, 3.67123, -7.90888, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7.61126, 25.36669], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333, "vertices": [2.10207, 7.81176, 1.86409, 2.77587, 0.57899, 4.12389, -2.02103, 4.91445, -0.42294, 3.0476, 2.2485, 1.35641, 1.40337, -7.41018, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.50038, 8.33173], "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6, "vertices": [1.74338, 5.69986, 2.19095, 1.31368, -0.3502, 2.26003, -2.64822, 2.92649, -2.24576, 2.88203, 2.50938, -0.12085, 2.28955, -7.92443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.74824, 4.25628], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.6667, "vertices": [3.09506, 18.12982, 6.6998, 7.01335, 10.75717, 5.63326, 13.58981, 6.64618, 16.97931, 5.73049, 4.13028, 4.62582, 1.91022, -6.60102, -18.43335, -3.46136, 0, 0, 0, 0, 0, 0, 0, 0, 13.70761, 25.26369], "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 7, "vertices": [7.76385, 22.96776, 3.25214, 10.32595, 5.9749, 10.56561, 6.70148, 10.37255, 13.13797, 9.39945, 9.18139, 5.61749, 1.87506, -6.81718, -11.68837, -2.19481, 0, 0, 0, 0, 0, 0, 0, 0, 11.47688, 25.30138], "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 7.3333, "vertices": [6.19345, 18.86413, 5.01094, 7.50755, 10.26088, 7.0386, 4.60565, 9.58471, 6.63943, 9.78791, 6.81813, 4.89733, 3.67123, -7.90888, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7.61126, 25.36669], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8, "vertices": [2.10207, 7.81176, 1.86409, 2.77587, 0.57899, 4.12389, -2.02103, 4.91445, -0.42294, 3.0476, 2.2485, 1.35641, 1.40337, -7.41018, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.50038, 8.33173]}]}, "lian": {"lian": [{"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.0667, "offset": 74, "vertices": [-7.89819, -0.83334, -7.89929, -0.83324, -7.89911, -0.83323, -8.92383, 0.68704, -8.92523, 0.68718, -8.92519, 0.68716, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.564, 0.00677, -0.56393, 0.00677, -1.26733, 0.15621, -1.26718, 0.15623, -0.70496, 0.00846, -0.70493, 0.00847, 0, 0, 0, 0, 0, 0, 0, 0, -0.84772, -0.13084, -0.84761, -0.13083, -2.397, 0.02877, -2.3968, 0.02879, -1.974, 0.02369, -1.97383, 0.02371, -6.35492, 1.53087, -6.35466, 1.53087, -7.47122, -4.333, -7.47046, -4.33316, -8.94113, -6.44795, -8.94051, -6.44801, -13.02286, -2.9971, -13.02197, -2.99718, -13.84763, -0.4717, -13.8472, -0.47178, -11.8519, 0.89417, -11.85114, 0.89412, -6.62442, -0.82766, -6.62422, -0.82769, 0, 0, 0, 0, 2.0278, 0.0146, 2.02795, 0.01458, 2.88803, -0.29123, 2.88815, -0.29123, 6.71994, -1.66767, 6.72017, -1.6677, 3.83691, -2.07843, 3.83702, -2.07845, -1.09436, -2.02002, -1.09441, -2.02002, -3.07526, -1.03524, -3.07523, -1.03527, -1.94244, 0.73986, -1.9422, 0.7398, -8.07681, 0.63931, -8.07683, 0.63922, -13.52261, -0.25326, -13.52257, -0.25331, -12.93396, 1.611, -12.93402, 1.61097, -10.07855, -1.44221, -10.07861, -1.44225, 0, 0, 0, 0, 2.21228, 2.0379, 2.21239, 2.03787, 6.02057, 1.61938, 6.02063, 1.61934, 6.16357, -0.39737, 6.16367, -0.39741, 5.84814, -1.12193, 5.84828, -1.12197, -3.97394, -0.31259, -3.97383, -0.31259, -4.7464, -0.52415, -4.74637, -0.52417, -4.28436, -2.51188, -4.28398, -2.51191, -1.31995, 0.01584, -1.31992, 0.01585, -2.42664, -0.52091, -2.4265, -0.52089, -2.30991, 0.02773, -2.30986, 0.02774, -1.31995, 0.01584, -1.31993, 0.01585, -2.42123, -0.08094, -2.42117, -0.08093, -4.07788, -0.6111, -4.07774, -0.61108, -2.64664, -0.51827, -2.64651, -0.51825, -1.54395, -0.31149, -1.54393, -0.31147, 0, 0, 0, 0, -9.88, -5.14377, -9.87959, -5.14383, -15.92636, -5.34122, -15.92616, -5.34115, -19.10742, -4.74499, -19.10701, -4.74494, -16.10941, -0.95869, -16.10909, -0.95866, -12.49329, -0.65808, -12.49324, -0.65805, -2.43994, -0.31874, -2.44003, -0.31873, -2.44006, -0.31871, 5.32147, -1.22399, 5.32155, -1.22398, 8.68106, -1.61236, 8.68112, -1.61236, 6.82532, -3.65521, 6.8253, -3.65521, 5.37378, -3.8488, 5.3737, -3.84882, -0.52188, -2.63991, -0.5218, -2.63993, 2.87131, -2.35466, 2.87138, -2.35467, -7.27939, -2.66881, -7.27914, -2.66881, -12.11581, -1.33068, -12.11559, -1.33065, -14.26187, -1.5189, -14.26166, -1.51888, -10.91754, -1.67507, -10.91718, -1.67504, -3.22107, -1.39343, -3.22104, -1.39342, 6.17703, -1.27426, 6.17691, -1.27425, 7.40582, -1.21302, 7.40585, -1.21301, 6.50943, -4.23846, 6.50938, -4.23846, -2.08167, -2.40718, -2.08131, -2.40719, -2.31754, -1.18126, -2.31763, -1.18125, -4.42181, -1.10701, -4.42175, -1.107], "curve": 0.25, "c3": 0.75}, {"time": 7.7}]}}}}, "animation2": {"slots": {"bg": {"attachment": [{"name": null}]}, "piaodai2": {"attachment": [{"name": null}]}, "bg1": {"attachment": [{"name": null}]}, "yezi": {"attachment": [{"name": null}]}, "huaban3": {"color": [{"color": "ffffff7f", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.9, "color": "ffffff00", "curve": "stepped"}, {"time": 2, "color": "ffffff00", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "color": "ffffff7f", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4.5667, "color": "ffffff00", "curve": "stepped"}, {"time": 4.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "color": "ffffff7f", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 7.2333, "color": "ffffff00", "curve": "stepped"}, {"time": 7.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "color": "ffffff7f"}]}, "huanan2": {"color": [{"color": "ffffff71", "curve": 0.378, "c2": 0.52, "c3": 0.747}, {"time": 0.5667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffffff", "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 2.6667, "color": "ffffff71", "curve": 0.378, "c2": 0.52, "c3": 0.747}, {"time": 3.2333, "color": "ffffff00", "curve": "stepped"}, {"time": 3.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "color": "ffffffff", "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 5.3333, "color": "ffffff71", "curve": 0.378, "c2": 0.52, "c3": 0.747}, {"time": 5.9, "color": "ffffff00", "curve": "stepped"}, {"time": 6, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "color": "ffffffff", "curve": 0.248, "c3": 0.629, "c4": 0.52}, {"time": 8, "color": "ffffff71"}]}, "huaban": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 5.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 7, "color": "ffffff00"}]}, "piaodai": {"attachment": [{"name": null}]}}, "bones": {"bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.11, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 1.11, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 4.72, "curve": 0.25, "c3": 0.75}, {"time": 8}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "y": -1.46, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "y": -1.46, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "y": -1.46, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8}]}, "bone3": {"rotate": [{"angle": 0.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -0.06, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.6667, "angle": 1.06, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 0.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": -0.06, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 4.3333, "angle": 1.06, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 0.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667, "angle": -0.06, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 7, "angle": 1.06, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": 0.15}], "translate": [{"x": 0.17, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.42, "curve": 0.356, "c2": 0.41, "c3": 0.711, "c4": 0.82}, {"time": 2.6667, "x": 0.17, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 1.42, "curve": 0.356, "c2": 0.41, "c3": 0.711, "c4": 0.82}, {"time": 5.3333, "x": 0.17, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 7, "x": 1.42, "curve": 0.356, "c2": 0.41, "c3": 0.711, "c4": 0.82}, {"time": 8, "x": 0.17}]}, "bone4": {"rotate": [{"angle": 2.51, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": 1.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2, "angle": 3.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 2.51, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": 1.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": 3.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 2.51, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6, "angle": 1.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7.3333, "angle": 3.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": 2.51}], "translate": [{"x": 0.62, "y": -0.31, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.69, "y": -0.85, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 2.6667, "x": 0.62, "y": -0.31, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 1.69, "y": -0.85, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 5.3333, "x": 0.62, "y": -0.31, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": 1.69, "y": -0.85, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 8, "x": 0.62, "y": -0.31}]}, "bone5": {"translate": [{"x": 1.89, "y": 0.62}, {"time": 0.5}, {"time": 1.8333, "x": 5.04, "y": 1.67}, {"time": 2.6667, "x": 1.89, "y": 0.62}, {"time": 3.1667}, {"time": 4.5, "x": 5.04, "y": 1.67}, {"time": 5.3333, "x": 1.89, "y": 0.62}, {"time": 5.8333}, {"time": 7.1667, "x": 5.04, "y": 1.67}, {"time": 8, "x": 1.89, "y": 0.62}]}, "bone6": {"translate": [{"x": 0.14, "y": -2.18, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5}, {"time": 1.8333, "x": 0.4, "y": -6.5, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": 0.14, "y": -2.18, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667}, {"time": 4.5, "x": 0.4, "y": -6.5, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "x": 0.14, "y": -2.18, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 5.8333}, {"time": 7.1667, "x": 0.4, "y": -6.5, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8, "x": 0.14, "y": -2.18}]}, "bone11": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -0.01, "y": 0.73, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -0.01, "y": 0.73, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": -0.01, "y": 0.73, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone12": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 6.57, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "y": 6.57, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "y": 6.57, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone13": {"rotate": [{"angle": -4.12}]}, "bone17": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": -1.74, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -1.74, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "angle": -3.8, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8}]}, "bone18": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": -1.74, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -1.74, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "angle": -1.74, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8}]}, "bone19": {"rotate": [{"angle": -0.93, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 3.73, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -0.93, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 3.73, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -0.93, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 4.53, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -0.93}]}, "bone20": {"rotate": [{"angle": -0.93, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 3.73, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -0.93, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 3.73, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -0.93, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 3.73, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -0.93}]}, "bone21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.66, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 4.66, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 5.46, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone22": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.66, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 4.66, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 4.66, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone24": {"rotate": [{"angle": -1.34, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5}, {"time": 1.8333, "angle": -3.99, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -1.34, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667}, {"time": 4.5, "angle": -3.99, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "angle": -1.34, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 5.8333}, {"time": 7.1667, "angle": -10.24, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8, "angle": -1.34}]}, "bone25": {"rotate": [{"angle": -0.74, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333}, {"time": 1.6667, "angle": -3.99, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.74, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3}, {"time": 4.3333, "angle": -3.99, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.74, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667}, {"time": 7, "angle": -3.99, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -0.74}]}, "bone26": {"rotate": [{"angle": -1.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -3.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -3.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -1.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -3.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -1.99}]}, "bone27": {"rotate": [{"angle": -3.25, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1}, {"time": 2.3333, "angle": -3.99, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -3.25, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667}, {"time": 5, "angle": -3.99, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -3.25, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.3333}, {"time": 7.6667, "angle": -3.99, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8, "angle": -3.25}]}, "bone28": {"rotate": [{"angle": -1.34, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5}, {"time": 1.8333, "angle": -3.99, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -1.34, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667}, {"time": 4.5, "angle": -3.99, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "angle": -1.34, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 5.8333}, {"time": 7.1667, "angle": -11.87, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8, "angle": -1.34}]}, "bone29": {"rotate": [{"angle": -0.74, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -3.99, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.74, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -3.99, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.74, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -3.99, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -0.74}]}, "bone30": {"rotate": [{"angle": -1.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}, {"time": 2, "angle": -3.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333}, {"time": 4.6667, "angle": -3.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -1.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6}, {"time": 7.3333, "angle": -3.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -1.99}]}, "bone31": {"rotate": [{"angle": -3.25, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -3.99, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -3.25, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -3.99, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -3.25, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": -3.99, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8, "angle": -3.25}]}, "bone32": {"rotate": [{"angle": -1.34, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -3.99, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -1.34, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -3.99, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "angle": -1.34, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "angle": -6.19, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8, "angle": -1.34}]}, "bone33": {"rotate": [{"angle": -0.74, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333}, {"time": 1.6667, "angle": -3.99, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.74, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3}, {"time": 4.3333, "angle": -3.99, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.74, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667}, {"time": 7, "angle": -3.99, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -0.74}]}, "bone34": {"rotate": [{"angle": -1.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}, {"time": 2, "angle": -3.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333}, {"time": 4.6667, "angle": -3.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -1.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6}, {"time": 7.3333, "angle": -3.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -1.99}]}, "bone35": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.99, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -3.99, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -3.99, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone36": {"rotate": [{"angle": -0.62, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -3.38, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.62, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -3.38, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.62, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -7.06, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -0.62}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 0.907, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "y": 0.907, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "y": 0.907, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone37": {"rotate": [{"angle": -1.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -3.38, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -3.38, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -1.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -3.38, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -1.69}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 0.907, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "y": 0.907, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "y": 0.907, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone38": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.38, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -3.38, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -3.38, "curve": 0.25, "c3": 0.75}, {"time": 8}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 0.907, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "y": 0.907, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "y": 0.907, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone39": {"rotate": [{"angle": -3.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -7.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -3.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -7.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -3.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -12.12, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -3.65}]}, "bone40": {"rotate": [{"angle": -3.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -7.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -3.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -7.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -3.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -12.12, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -3.65}]}, "bone41": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -7.3, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -7.3, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -7.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.3333, "angle": -8.47, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8}]}, "bone42": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -7.3, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -7.3, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -7.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.3333, "angle": -8.47, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8}]}, "bone43": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -7.3, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -7.3, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -20.45, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone44": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -7.3, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -7.3, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -7.3, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone45": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -7.3, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -7.3, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -7.3, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone47": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -12.9, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -12.9, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -16.25, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone51": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.51, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -1.51, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -1.51, "curve": 0.25, "c3": 0.75}, {"time": 8}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "y": -4.12, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "y": -4.12, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "y": -4.12, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8}]}, "bone52": {"rotate": [{"angle": -0.28, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -1.51, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.28, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -1.51, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.28, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -1.51, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -0.28}]}, "bone53": {"rotate": [{"angle": -0.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}, {"time": 2, "angle": -1.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -0.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333}, {"time": 4.6667, "angle": -1.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -0.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6}, {"time": 7.3333, "angle": -1.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -0.75}]}, "bone54": {"rotate": [{"angle": -1.23, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1}, {"time": 2.3333, "angle": -1.51, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -1.23, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667}, {"time": 5, "angle": -1.51, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -1.23, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.3333}, {"time": 7.6667, "angle": -1.51, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8, "angle": -1.23}]}, "bone55": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.51, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -1.51, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -1.51, "curve": 0.25, "c3": 0.75}, {"time": 8}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "y": -4.12, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "y": -4.12, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "y": -4.12, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8}]}, "bone56": {"rotate": [{"angle": -0.28, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333}, {"time": 1.6667, "angle": -1.51, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.28, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3}, {"time": 4.3333, "angle": -1.51, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.28, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667}, {"time": 7, "angle": -1.51, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -0.28}]}, "bone57": {"rotate": [{"angle": -0.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}, {"time": 2, "angle": -1.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -0.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333}, {"time": 4.6667, "angle": -1.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -0.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6}, {"time": 7.3333, "angle": -1.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -0.75}]}, "bone58": {"rotate": [{"angle": -1.23, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -1.51, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -1.23, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -1.51, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -1.23, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": -1.51, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8, "angle": -1.23}]}, "bone59": {"rotate": [{"angle": -1.51, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -1.51, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -1.51, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -1.51}]}, "bone64": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.71, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -2.71, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -2.71, "curve": 0.25, "c3": 0.75}, {"time": 8}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 3.77, "y": -3.09, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 3.77, "y": -3.09, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": 3.77, "y": -3.09, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone65": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 5.64, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 5.64, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 16.3, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone60": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.42, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -2.42, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -2.42, "curve": 0.25, "c3": 0.75}, {"time": 8}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 2.47, "y": -1.21, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 2.47, "y": -1.21, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": 2.47, "y": -1.21, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone61": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -4.72, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -4.72, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -13.26, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone62": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.51, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -1.51, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -1.51, "curve": 0.25, "c3": 0.75}, {"time": 8}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "y": -4.12, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "y": -4.12, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "y": -4.12, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8}]}, "bone63": {"rotate": [{"angle": -0.28, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -1.51, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.28, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -1.51, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.28, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -1.51, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -0.28}]}, "bone66": {"rotate": [{"angle": -0.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -1.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -0.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -1.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -0.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -1.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -0.75}]}, "bone67": {"rotate": [{"angle": -1.23, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1}, {"time": 2.3333, "angle": -1.51, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -1.23, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667}, {"time": 5, "angle": -1.51, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -1.23, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.3333}, {"time": 7.6667, "angle": -1.51, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8, "angle": -1.23}]}, "bone68": {"rotate": [{"angle": -4.32, "curve": 0.269, "c2": 0.11, "c3": 0.664, "c4": 0.66}, {"time": 0.8333, "angle": -1.08, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.2333, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": -4.44, "curve": 0.311, "c3": 0.646, "c4": 0.35}, {"time": 2.6667, "angle": -4.32, "curve": 0.269, "c2": 0.11, "c3": 0.664, "c4": 0.66}, {"time": 3.5, "angle": -1.08, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.9, "curve": 0.25, "c3": 0.75}, {"time": 5.2333, "angle": -4.44, "curve": 0.311, "c3": 0.646, "c4": 0.35}, {"time": 5.3333, "angle": -4.32, "curve": 0.269, "c2": 0.11, "c3": 0.664, "c4": 0.66}, {"time": 6.1667, "angle": -1.08, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 6.5667, "curve": 0.25, "c3": 0.75}, {"time": 7.9, "angle": -4.44, "curve": 0.311, "c3": 0.646, "c4": 0.35}, {"time": 8, "angle": -4.32}]}, "bone69": {"rotate": [{"angle": -0.82, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333}, {"time": 1.6667, "angle": -4.44, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.82, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3}, {"time": 4.3333, "angle": -4.44, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.82, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667}, {"time": 7, "angle": -15.91, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -0.82}]}, "bone70": {"rotate": [{"angle": -2.22, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}, {"time": 2, "angle": -4.44, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -2.22, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333}, {"time": 4.6667, "angle": -4.44, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -2.22, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6}, {"time": 7.3333, "angle": -4.44, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -2.22}]}, "bone71": {"rotate": [{"angle": -3.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -4.44, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -3.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -4.44, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -3.62, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": -4.44, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8, "angle": -3.62}]}, "bone72": {"rotate": [{"angle": -4.26, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "angle": -4.55, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.8333, "angle": -2.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.5, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": -4.26, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 2.8333, "angle": -4.55, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.5, "angle": -2.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.1667, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.3333, "angle": -4.26, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 5.5, "angle": -4.55, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.1667, "angle": -2.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.8333, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 8, "angle": -4.26}]}, "bone73": {"rotate": [{"angle": -1.22, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -0.38, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.6667, "angle": -4.93, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -1.22, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": -0.38, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 4.3333, "angle": -4.93, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -1.22, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667, "angle": -0.38, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 7, "angle": -16.4, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -1.22}]}, "bone74": {"rotate": [{"angle": -3.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -1.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2, "angle": -5.59, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -3.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "angle": -1.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": -5.59, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -3.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6, "angle": -1.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7.3333, "angle": -5.59, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -3.31}]}, "bone75": {"rotate": [{"angle": -5.4, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "angle": -1.69, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 2.3333, "angle": -6.24, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -5.4, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "angle": -1.69, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 5, "angle": -6.24, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -5.4, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 6.3333, "angle": -1.69, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 7.6667, "angle": -6.24, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8, "angle": -5.4}]}, "bone76": {"rotate": [{"angle": -4.38, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333}, {"time": 2.1667, "angle": -6.59, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": -4.38, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 3.5}, {"time": 4.8333, "angle": -6.59, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": -4.38, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 6.1667}, {"time": 7.5, "angle": -6.59, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 8, "angle": -4.38}]}, "bone77": {"rotate": [{"angle": -1.22, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333}, {"time": 1.6667, "angle": -6.59, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -1.22, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3}, {"time": 4.3333, "angle": -6.59, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -1.22, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667}, {"time": 7, "angle": -6.59, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -1.22}]}, "bone78": {"rotate": [{}, {"time": 1.3333, "angle": 4.12}, {"time": 2.6667}, {"time": 4, "angle": 4.12}, {"time": 5.3333}, {"time": 6.6667, "angle": 4.12}, {"time": 8}]}, "bone79": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.21, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -1.21, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -4.44, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone80": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.78, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -1.78, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -5.01, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone81": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.93, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -2.93, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -6.16, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone82": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 0.34, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}, {"time": 4, "angle": 0.34, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}, {"time": 6.6667, "angle": 0.34, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone83": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 0.34, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone84": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 0.34, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone88": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -6.62, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -6.62, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": -6.62, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone89": {"rotate": [{"angle": -1.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": -1.24, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2, "angle": -2.69, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -1.24, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": -2.69, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -1.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "angle": -1.24, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7.3333, "angle": -2.69, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -1.34}]}, "bone90": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.32, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 7.32, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 7.32, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone91": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 56.21, "y": -138.09, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 56.21, "y": -138.09, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "x": 56.21, "y": -138.09, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone92": {"translate": [{"x": 51.87, "y": -127.43, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.3333, "x": 56.21, "y": -138.09, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 2.6667, "x": 51.87, "y": -127.43, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 3, "x": 56.21, "y": -138.09, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 5.3333, "x": 51.87, "y": -127.43, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 5.6667, "x": 56.21, "y": -138.09, "curve": 0.25, "c3": 0.75}, {"time": 6, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 8, "x": 51.87, "y": -127.43}]}, "bone95": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.82, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 7.82, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 7.82, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone49": {"rotate": [{"angle": -19.37, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.3333, "angle": -16.01, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "angle": -14.02, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2, "angle": -24.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -19.37, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 3, "angle": -16.01, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.3333, "angle": -14.02, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.6667, "angle": -24.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -19.37, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -28.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -14.02}]}, "bone50": {"rotate": [{"angle": -31.67, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "angle": -28.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1, "angle": -22.87, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 2.3333, "angle": -33.64, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -31.67, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 3, "angle": -28.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.6667, "angle": -22.87, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 5, "angle": -33.64, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -31.67, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": -28.04, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 8, "angle": -22.87}]}, "bone48": {"rotate": [{"angle": -7.16, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -5.17, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.6667, "angle": -15.94, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -7.16, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "angle": -5.17, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 4.3333, "angle": -15.94, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -7.16, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -28.04, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -5.17}]}, "bone14": {"rotate": [{}, {"time": 1.3333, "angle": 4.68}, {"time": 2.6667}, {"time": 4, "angle": 4.68}, {"time": 5.3333}, {"time": 6.6667, "angle": 5.48}, {"time": 8}]}, "bone15": {"rotate": [{}, {"time": 1.3333, "angle": 4.68}, {"time": 2.6667}, {"time": 4, "angle": 4.68}, {"time": 5.3333}, {"time": 6.6667, "angle": 5.48}, {"time": 8}]}, "bone93": {"rotate": [{"angle": -3.72, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333}, {"time": 2.1667, "angle": -5.6, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": -3.72, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 3.5}, {"time": 4.8333, "angle": -5.6, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": -3.72, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 6.1667}, {"time": 7.5, "angle": -5.6, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 8, "angle": -3.72}]}, "bone94": {"rotate": [{"angle": -1.03, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -5.6, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -1.03, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -5.6, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -1.03, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": 28.69, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -1.03}]}, "bone100": {"translate": [{"x": 2.86, "y": -0.35, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 15.5, "y": -1.92, "curve": "stepped"}, {"time": 1.6667, "x": 15.5, "y": -1.92, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 2.86, "y": -0.35, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 15.5, "y": -1.92, "curve": "stepped"}, {"time": 4.3333, "x": 15.5, "y": -1.92, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 2.86, "y": -0.35, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": 15.5, "y": -1.92, "curve": "stepped"}, {"time": 7, "x": 15.5, "y": -1.92, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "x": 2.86, "y": -0.35}]}, "bone98": {"translate": [{"y": 0.15, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": -4.56, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "y": 0.15, "curve": 0.25, "c3": 0.75}, {"time": 4, "y": -4.56, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "y": 0.15, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "y": -4.56, "curve": 0.25, "c3": 0.75}, {"time": 8, "y": 0.15}]}, "bone85": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 4.2, "y": -0.13, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 4.2, "y": -0.13, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": 4.2, "y": -0.13, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone86": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 6, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 6, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 6, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone87": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 6, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 6, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 6, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone102": {"translate": [{}, {"time": 1.3333, "x": 15.71, "y": -7.99, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 15.71, "y": -7.99, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": 44.78, "y": -23.33, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone103": {"translate": [{}, {"time": 1.3333, "x": 4.95, "y": 13.08, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 4.95, "y": 13.08, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": 34.01, "y": -2.26, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone104": {"translate": [{}, {"time": 1.3333, "x": -7.07, "y": -19.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": -7.07, "y": -19.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": -2.1, "y": -26.86, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone105": {"translate": [{"x": 11.19, "y": -23.29, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 1.6667, "x": 49.85, "y": -103.8, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 2.6667, "x": 11.19, "y": -23.29, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 4.3333, "x": 49.85, "y": -103.8, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 5.3333, "x": 11.19, "y": -23.29, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 7, "x": 49.85, "y": -103.8, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 8, "x": 11.19, "y": -23.29}]}, "bone107": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 8.39, "y": -1.86, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 8.39, "y": -1.86, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": 8.39, "y": -1.86, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone106": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -5.78, "y": -1.98, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -5.78, "y": -1.98, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": -5.78, "y": -1.98, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone7": {"rotate": [{"angle": -1.05, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -5.71, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -1.05, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -5.71, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -1.05, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -5.71, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -1.05}]}, "bone8": {"rotate": [{"angle": -2.01, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -4.03, "curve": "stepped"}, {"time": 2, "angle": -4.03, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -2.01, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -4.03, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -2.01, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -4.03, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -2.01}]}, "bone9": {"rotate": [{"angle": -3.96, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.03, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -3.96, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 1.03, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -3.96, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 1.03, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -3.96}]}, "bone96": {"translate": [{"x": 10.51, "y": 14.54}, {"time": 1}, {"time": 2.3333, "x": 14.02, "y": 19.39}, {"time": 2.6667, "x": 10.51, "y": 14.54}, {"time": 3.6667}, {"time": 5, "x": 14.02, "y": 19.39}, {"time": 5.3333, "x": 10.51, "y": 14.54}, {"time": 6.3333}, {"time": 7.6667, "x": 14.02, "y": 19.39}, {"time": 8, "x": 10.51, "y": 14.54}]}, "bone110": {"translate": [{"x": 10.5, "y": 9.43}, {"time": 1}, {"time": 2.3333, "x": 14, "y": 12.58}, {"time": 2.6667, "x": 10.5, "y": 9.43}, {"time": 3.6667}, {"time": 5, "x": 14, "y": 12.58}, {"time": 5.3333, "x": 10.5, "y": 9.43}, {"time": 6.3333}, {"time": 7.6667, "x": 14, "y": 12.58}, {"time": 8, "x": 10.5, "y": 9.43}]}, "bone111": {"rotate": [{"angle": -2.6, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -7.67, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.1667, "angle": -6.25, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -2.6, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -7.67, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 4.8333, "angle": -6.25, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -2.6, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "angle": -7.67, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 7.5, "angle": -6.25, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 8, "angle": -2.6}], "translate": [{"x": 2.66, "y": 1.84, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 7.85, "y": 5.43, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.1667, "x": 6.4, "y": 4.43, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 2.66, "y": 1.84, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 7.85, "y": 5.43, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 4.8333, "x": 6.4, "y": 4.43, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "x": 2.66, "y": 1.84, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 5.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "x": 7.85, "y": 5.43, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 7.5, "x": 6.4, "y": 4.43, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 8, "x": 2.66, "y": 1.84}]}, "bone112": {"rotate": [{}, {"time": 1.3333, "angle": 12.66}, {"time": 2.6667}, {"time": 4, "angle": 12.66}, {"time": 5.3333}, {"time": 6.6667, "angle": 12.66}, {"time": 8}]}, "bone113": {"rotate": [{}, {"time": 1.3333, "angle": 12.66}, {"time": 2.6667}, {"time": 4, "angle": 12.66}, {"time": 5.3333}, {"time": 6.6667, "angle": 12.66}, {"time": 8}]}, "bone97": {"translate": [{"time": 5.3333}, {"time": 6.7, "x": 2.55, "y": -5}, {"time": 8}]}, "bone114": {"translate": [{"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": 61.13, "y": -31.66, "curve": 0.25, "c3": 0.75}, {"time": 8}]}}, "deform": {"default": {"shou_you": {"shou_you": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 88, "vertices": [-4.76096, -1.60536, 3.20605, 3.26791, -4.41202, 2.40361, -8.83014, 0.44887, 5.75358, 6.71324, 7.35945, 3.27721, -5.66839, 6.78536, -6.71349, -0.48834, 3.71801, 5.61098, -4.91823, 4.59548, -3.87497, -2.08192, 0.72145, 4.33923, -4.15997, 1.42967, -2.05534, 0.22545, 1.4352, 1.48753, -1.23007, 1.66183, 2.04311, 1.69477, 0.61453, 1.22215, 0.20713, 1.42702, 4.56727, 3.1331, 2.76766, 0.83965, 1.83162, 2.23764], "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "offset": 88, "vertices": [-4.76096, -1.60536, 3.20605, 3.26791, -4.41202, 2.40361, -8.83014, 0.44887, 5.75358, 6.71324, 7.35945, 3.27721, -5.66839, 6.78536, -6.71349, -0.48834, 3.71801, 5.61098, -4.91823, 4.59548, -3.87497, -2.08192, 0.72145, 4.33923, -4.15997, 1.42967, -2.05534, 0.22545, 1.4352, 1.48753, -1.23007, 1.66183, 2.04311, 1.69477, 0.61453, 1.22215, 0.20713, 1.42702, 4.56727, 3.1331, 2.76766, 0.83965, 1.83162, 2.23764], "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.6667, "offset": 128, "vertices": [11.54506, 3.68503, 8.26739, 8.86124, 3.02146, 0.51871, 2.99675, 0.64679, 2.29762, 2.03011, 1.80871, 2.4752, 3.02789, -0.47899], "curve": 0.334, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 8}]}, "shoude": {"shoude": [{"vertices": [2.10207, 7.81176, 1.86409, 2.77587, 0.57899, 4.12389, -2.02103, 4.91445, -0.42294, 3.0476, 2.2485, 1.35641, 1.40337, -7.41018, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.50038, 8.33173], "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "vertices": [1.74338, 5.69986, 2.19095, 1.31368, -0.3502, 2.26003, -2.64822, 2.92649, -2.24576, 2.88203, 2.50938, -0.12085, 2.28955, -7.92443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.74824, 4.25628], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "vertices": [3.09506, 18.12982, 6.6998, 7.01335, 10.75717, 5.63326, 13.58981, 6.64618, 16.97931, 5.73049, 4.13028, 4.62582, 1.91022, -6.60102, -18.43335, -3.46136, 0, 0, 0, 0, 0, 0, 0, 0, 13.70761, 25.26369], "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.6667, "vertices": [7.76385, 22.96776, 3.25214, 10.32595, 5.9749, 10.56561, 6.70148, 10.37255, 13.13797, 9.39945, 9.18139, 5.61749, 1.87506, -6.81718, -11.68837, -2.19481, 0, 0, 0, 0, 0, 0, 0, 0, 11.47688, 25.30138], "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 2, "vertices": [6.19345, 18.86413, 5.01094, 7.50755, 10.26088, 7.0386, 4.60565, 9.58471, 6.63943, 9.78791, 6.81813, 4.89733, 3.67123, -7.90888, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7.61126, 25.36669], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "vertices": [2.10207, 7.81176, 1.86409, 2.77587, 0.57899, 4.12389, -2.02103, 4.91445, -0.42294, 3.0476, 2.2485, 1.35641, 1.40337, -7.41018, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.50038, 8.33173], "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "vertices": [1.74338, 5.69986, 2.19095, 1.31368, -0.3502, 2.26003, -2.64822, 2.92649, -2.24576, 2.88203, 2.50938, -0.12085, 2.28955, -7.92443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.74824, 4.25628], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4, "vertices": [3.09506, 18.12982, 6.6998, 7.01335, 10.75717, 5.63326, 13.58981, 6.64618, 16.97931, 5.73049, 4.13028, 4.62582, 1.91022, -6.60102, -18.43335, -3.46136, 0, 0, 0, 0, 0, 0, 0, 0, 13.70761, 25.26369], "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 4.3333, "vertices": [7.76385, 22.96776, 3.25214, 10.32595, 5.9749, 10.56561, 6.70148, 10.37255, 13.13797, 9.39945, 9.18139, 5.61749, 1.87506, -6.81718, -11.68837, -2.19481, 0, 0, 0, 0, 0, 0, 0, 0, 11.47688, 25.30138], "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 4.6667, "vertices": [6.19345, 18.86413, 5.01094, 7.50755, 10.26088, 7.0386, 4.60565, 9.58471, 6.63943, 9.78791, 6.81813, 4.89733, 3.67123, -7.90888, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7.61126, 25.36669], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333, "vertices": [2.10207, 7.81176, 1.86409, 2.77587, 0.57899, 4.12389, -2.02103, 4.91445, -0.42294, 3.0476, 2.2485, 1.35641, 1.40337, -7.41018, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.50038, 8.33173], "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6, "vertices": [1.74338, 5.69986, 2.19095, 1.31368, -0.3502, 2.26003, -2.64822, 2.92649, -2.24576, 2.88203, 2.50938, -0.12085, 2.28955, -7.92443, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.74824, 4.25628], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.6667, "vertices": [3.09506, 18.12982, 6.6998, 7.01335, 10.75717, 5.63326, 13.58981, 6.64618, 16.97931, 5.73049, 4.13028, 4.62582, 1.91022, -6.60102, -18.43335, -3.46136, 0, 0, 0, 0, 0, 0, 0, 0, 13.70761, 25.26369], "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 7, "vertices": [7.76385, 22.96776, 3.25214, 10.32595, 5.9749, 10.56561, 6.70148, 10.37255, 13.13797, 9.39945, 9.18139, 5.61749, 1.87506, -6.81718, -11.68837, -2.19481, 0, 0, 0, 0, 0, 0, 0, 0, 11.47688, 25.30138], "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 7.3333, "vertices": [6.19345, 18.86413, 5.01094, 7.50755, 10.26088, 7.0386, 4.60565, 9.58471, 6.63943, 9.78791, 6.81813, 4.89733, 3.67123, -7.90888, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7.61126, 25.36669], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8, "vertices": [2.10207, 7.81176, 1.86409, 2.77587, 0.57899, 4.12389, -2.02103, 4.91445, -0.42294, 3.0476, 2.2485, 1.35641, 1.40337, -7.41018, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.50038, 8.33173]}]}, "lian": {"lian": [{"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.0667, "offset": 74, "vertices": [-7.89819, -0.83334, -7.89929, -0.83324, -7.89911, -0.83323, -8.92383, 0.68704, -8.92523, 0.68718, -8.92519, 0.68716, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.564, 0.00677, -0.56393, 0.00677, -1.26733, 0.15621, -1.26718, 0.15623, -0.70496, 0.00846, -0.70493, 0.00847, 0, 0, 0, 0, 0, 0, 0, 0, -0.84772, -0.13084, -0.84761, -0.13083, -2.397, 0.02877, -2.3968, 0.02879, -1.974, 0.02369, -1.97383, 0.02371, -6.35492, 1.53087, -6.35466, 1.53087, -7.47122, -4.333, -7.47046, -4.33316, -8.94113, -6.44795, -8.94051, -6.44801, -13.02286, -2.9971, -13.02197, -2.99718, -13.84763, -0.4717, -13.8472, -0.47178, -11.8519, 0.89417, -11.85114, 0.89412, -6.62442, -0.82766, -6.62422, -0.82769, 0, 0, 0, 0, 2.0278, 0.0146, 2.02795, 0.01458, 2.88803, -0.29123, 2.88815, -0.29123, 6.71994, -1.66767, 6.72017, -1.6677, 3.83691, -2.07843, 3.83702, -2.07845, -1.09436, -2.02002, -1.09441, -2.02002, -3.07526, -1.03524, -3.07523, -1.03527, -1.94244, 0.73986, -1.9422, 0.7398, -8.07681, 0.63931, -8.07683, 0.63922, -13.52261, -0.25326, -13.52257, -0.25331, -12.93396, 1.611, -12.93402, 1.61097, -10.07855, -1.44221, -10.07861, -1.44225, 0, 0, 0, 0, 2.21228, 2.0379, 2.21239, 2.03787, 6.02057, 1.61938, 6.02063, 1.61934, 6.16357, -0.39737, 6.16367, -0.39741, 5.84814, -1.12193, 5.84828, -1.12197, -3.97394, -0.31259, -3.97383, -0.31259, -4.7464, -0.52415, -4.74637, -0.52417, -4.28436, -2.51188, -4.28398, -2.51191, -1.31995, 0.01584, -1.31992, 0.01585, -2.42664, -0.52091, -2.4265, -0.52089, -2.30991, 0.02773, -2.30986, 0.02774, -1.31995, 0.01584, -1.31993, 0.01585, -2.42123, -0.08094, -2.42117, -0.08093, -4.07788, -0.6111, -4.07774, -0.61108, -2.64664, -0.51827, -2.64651, -0.51825, -1.54395, -0.31149, -1.54393, -0.31147, 0, 0, 0, 0, -9.88, -5.14377, -9.87959, -5.14383, -15.92636, -5.34122, -15.92616, -5.34115, -19.10742, -4.74499, -19.10701, -4.74494, -16.10941, -0.95869, -16.10909, -0.95866, -12.49329, -0.65808, -12.49324, -0.65805, -2.43994, -0.31874, -2.44003, -0.31873, -2.44006, -0.31871, 5.32147, -1.22399, 5.32155, -1.22398, 8.68106, -1.61236, 8.68112, -1.61236, 6.82532, -3.65521, 6.8253, -3.65521, 5.37378, -3.8488, 5.3737, -3.84882, -0.52188, -2.63991, -0.5218, -2.63993, 2.87131, -2.35466, 2.87138, -2.35467, -7.27939, -2.66881, -7.27914, -2.66881, -12.11581, -1.33068, -12.11559, -1.33065, -14.26187, -1.5189, -14.26166, -1.51888, -10.91754, -1.67507, -10.91718, -1.67504, -3.22107, -1.39343, -3.22104, -1.39342, 6.17703, -1.27426, 6.17691, -1.27425, 7.40582, -1.21302, 7.40585, -1.21301, 6.50943, -4.23846, 6.50938, -4.23846, -2.08167, -2.40718, -2.08131, -2.40719, -2.31754, -1.18126, -2.31763, -1.18125, -4.42181, -1.10701, -4.42175, -1.107], "curve": 0.25, "c3": 0.75}, {"time": 7.7}]}}}}}}