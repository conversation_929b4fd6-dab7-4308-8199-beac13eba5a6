syntax = "proto3";
package sim;
import "Comm.proto";

// 
message CityAchieveResponse {
  repeated double resAddList = 1;
  // 下一个领取过建筑升级奖励的进度
  int32 nextCityLevelRewardIndex = 2;
}

// 
message CityAggregateMessage {
  // 下一个领取过建筑升级奖励的进度
  int32 nextCityLevelRewardIndex = 1;
  // 已经领取过的城池通关奖励
  repeated int64 rewardCityList = 2;
  // 已经打开过迷雾的城池
  repeated int64 openFogCityList = 3;
  // 额外需要建造的建筑
  repeated int64 otherCreateCityList = 4;
  // 解锁最大的五族荣耀建筑预览ID
  int32 unlockRaceShowId = 5;
  // 已经解锁的五族荣耀装饰ID结合
  repeated int64 decorationIdList = 6;
  // 已经领取过五族荣耀种族装饰攒齐的奖励ID集合
  repeated int32 raceRewardIdList = 7;
  // 已建三界小家 ID：等级 ID为一级时的各建筑ID 只包括1xx
  map<int64,int32> smallHomeLevelMap = 8;
  // 已经领取的三界小家升级奖励的集合 元素为配置表ID 包含1xx,2xx,3xx
  repeated int64 smallHomeRewardList = 9;
  // 所有建筑达到某个等级的奖励领取情况 如果已经领取完1,2级的奖励元素为{1,2}
  repeated int32 smallHomeAllLevelReward = 10;
  // 号召宝箱的等级 从1开始
  int32 boxLevel = 11;
  // 下一次自动召唤的冷却时间
  int64 nextAutoTimeStamp = 12;
  // 号召宝箱的总数量
  int32 boxTotalCount = 13;
  // 满足条件但需要手动解锁的装饰集合
  repeated int64 activeLockIdList = 14;
}

// 
message CityChapterResponse {
  repeated double resAddList = 1;
  // 已经领取过的城池通关奖励
  repeated int64 rewardCityList = 2;
}

// 
message CityCreateResponse {
  // 
  CityMessage cityMessage = 1;
  // 
  CityAggregateMessage cityAggregateMessage = 2;
  repeated double rewardList = 3;
}

// 
message CityHireResponse {
  int32 energyHire = 1;
  // 总的号召宝箱的数量
  int32 totalBoxCount = 2;
  // 下一次自动召唤的冷却时间
  int64 nextAutoTimeStamp = 3;
}

// 
message CityHireWorkerListMessage {
  repeated CityMessage cityList = 1;
}

// 
message CityHireWorkerMessage {
  // 据点id
  int64 cityId = 1;
  // 招募员工的人数
  int32 num = 2;
}

// 
message CityMessage {
  int64 cityId = 1;
  int32 level = 2;
  int32 energyHire = 3;
  int32 itemHire = 4;
}

// 
message DecorationManualReward {
  // 
  sim.RewardMessage rewardMessage = 1;
  repeated int64 decorationIdList = 2;
}

// 
message EnergyFactoryMessage {
  // 族运水晶等级
  int32 level = 1;
  // 外观
  string look = 2;
  // 是否已经领取过了自动点击奖励 领取完奖励后才算上自动点击
  bool autoTake = 3;
  // 自动点击解锁进度条
  int32 autoStateCount = 4;
}

// 
message HeroPictureMessage {
  // 已经激活的图鉴集合 key:图鉴ID val:等级
  map<int64,int32> pictureMap = 1;
}

