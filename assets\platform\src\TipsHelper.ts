import { Node } from "cc";
import { director } from "cc";
import { TipsCtrl } from "./ctrl/TipsCtrl";
import { RouteCtrl } from "./core/RouteCtrl";
import { LangMgr } from "../../GameScrpit/game/mgr/LangMgr";

export interface TipsItem {
  content: string;
  showTime?: number;
  createTime?: number;
  node?: Node;
}

export class TipsMgr {
  private static _tipsCtrl: TipsCtrl;

  private static _topRouteCtrl: RouteCtrl;

  private static _timeOutId: number = 0;

  private static _stopTouchSecond: number = 0;

  public static get tipsCtrl(): TipsCtrl {
    if (!TipsMgr._tipsCtrl) {
      TipsMgr._tipsCtrl = director.getScene().getChildByPath("canvas_top/top_root").getComponent(TipsCtrl);
    }
    return TipsMgr._tipsCtrl;
  }

  public static get topRouteCtrl(): RouteCtrl {
    if (!TipsMgr._topRouteCtrl) {
      TipsMgr._topRouteCtrl = director.getScene().getChildByPath("canvas_top/top_root").getComponent(RouteCtrl);
    }
    return TipsMgr._topRouteCtrl;
  }

  public static stopSecond(): number {
    return TipsMgr._stopTouchSecond;
  }

  public static setEnableTouch(value: boolean, lastSecond: number = 3, aniBool: boolean = true) {
    director.getScene().getChildByPath("canvas_top/TopStopTouch").active = !value;
    director.getScene().getChildByPath("canvas_top/TopStopTouch").getComponent("TopStopTouch").enabled = aniBool;

    if (!value && lastSecond > 0) {
      if (TipsMgr._timeOutId) {
        clearTimeout(TipsMgr._timeOutId);
        TipsMgr._timeOutId = 0;
      }

      TipsMgr._stopTouchSecond = lastSecond;
      TipsMgr._timeOutId = setTimeout(() => {
        TipsMgr._stopTouchSecond = 0;
        director.getScene().getChildByPath("canvas_top/TopStopTouch").active = false;
      }, lastSecond * 1000);
    }
  }

  public static getTipsRoot(): Node {
    return TipsMgr.tipsCtrl.node;
  }

  public static showTip(content: string) {
    TipsMgr.tipsCtrl.showTip(content);
  }

  /**
   *
   * @param node 要显示的提示节点层
   * @param liveTime 节点层的生存周期
   * @param nodeCd ? 倒计时节点
   */
  public static showTipNode(node: Node, lifeTime: number = 0, nodeCd?: Node) {
    TipsMgr.tipsCtrl.showTipNode(node, lifeTime, nodeCd);
  }

  // **会延迟自动释放资源
  public static showTipPage(bundle: string, url: string, callback?: Function) {
    TipsMgr.topRouteCtrl.showPrefab(bundle, url, {}, callback);
  }

  public static showTipX(id: number, args: any[] = [], defaultMsg: string = "") {
    let msg = LangMgr.txMsgCode(id, args, defaultMsg);
    TipsMgr.showTip(msg);
  }

  public static showErrX(id: number, args: any[] = [], defaultMsg: string = "") {
    let msg = LangMgr.txErrorCode(id, args, defaultMsg);
    TipsMgr.showTip(msg);
  }
}
