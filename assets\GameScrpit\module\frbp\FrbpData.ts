import GameHttpApi from "../../game/httpNet/GameHttpApi";
import { ProsperityMessage, SimpleRankMessage } from "../../game/net/protocol/Activity";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { activityId, FrbpModule } from "./FrbpModule";
import { UIMgr } from "../../lib/ui/UIMgr";
import TipMgr from "../../lib/tips/TipMgr";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class FrbpData {
  private _ProsperityMessage: ProsperityMessage = null;

  private _SimpleRankMessage: SimpleRankMessage = null;

  private _ProsperityVO: any = null;

  public get ProsperityMessage(): ProsperityMessage {
    return this._ProsperityMessage;
  }

  public set ProsperityMessage(value: ProsperityMessage) {
    this._ProsperityMessage = value;
  }

  public get SimpleRankMessage(): SimpleRankMessage {
    return this._SimpleRankMessage;
  }

  public set SimpleRankMessage(value: SimpleRankMessage) {
    this._SimpleRankMessage = value;
  }

  public get limitMap() {
    return this.ProsperityMessage.limitMap;
  }

  public set limitMap(val: { [key: number]: number }) {
    this.ProsperityMessage.limitMap = val;
  }

  public async getVO(activityId: number, suBack?, erBack?): Promise<any> {
    return new Promise((resolve, reject) => {
      if (this._ProsperityVO) {
        resolve(this._ProsperityVO);
      } else {
        GameHttpApi.getActivityConfig(activityId).then((resp: any) => {
          if (resp.code != 200) {
            log.error(resp);
            erBack && erBack();
          }
          suBack && suBack();
          this._ProsperityVO = JSON.parse(resp.msg);
          resolve(this._ProsperityVO);
        });
      }
    });
  }

  public upVO() {
    this._ProsperityVO = null;
    this.getVO(activityId);

    let rotueTables = FrbpModule.route.rotueTables;
    for (let i = 0; i < rotueTables.length; i++) {
      UIMgr.instance.closeByName(rotueTables[i].uiName);
    }
    TipMgr.showTip("活动内容已变更");
  }
}
