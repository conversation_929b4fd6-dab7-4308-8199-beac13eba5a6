import { _decorator, isValid, Label, settings, sp, tween, UITransform, Widget } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { ShengDianRouteItem } from "../../../module/sheng_dian/ShengDianRoute";
import { JsonMgr } from "../../mgr/JsonMgr";
import { ShengDianModule } from "../../../module/sheng_dian/ShengDianModule";
import { TempleLikeResponse, TempleMessage } from "../../net/protocol/Activity";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

/**
 * ivan_huang
 * Tue Jan 14 2025 16:30:30 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/ui_shengdian/UIShengDianMain.ts
 * https://docs.cocos.com/creator/3.8/manual/zh/
 *
 */

@ccclass("UIShengDianMain")
export class UIShengDianMain extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_SHENGDIAN}?prefab/ui/UIShengDianMain`;
  }

  //=================================================
  public init(args: any): void {
    super.init(args);
  }
  protected onEvtShow(): void {
    // do something
    let templeList = Object.values(JsonMgr.instance.jsonList.c_temple);
    let i = 0;
    log.log(templeList);
    for (; i < templeList.length && i < 3; i++) {
      let node_shengdian = this.getNode(`btn_gonddian${i + 1}`);
      node_shengdian.getComponentInChildren(Label).string = templeList[i].name;

      setTimeout(() => {
        if (!isValid(node_shengdian)) {
          return;
        }
        node_shengdian.active = true;
        node_shengdian.getComponent(sp.Skeleton).setAnimation(0, "animation1", false);
      }, i * 300 + 300);
    }
    for (; i < 3; i++) {
      this.getNode(`btn_gonddian${i + 1}`).active = false;
    }
    ShengDianModule.api.templeInfo((data: TempleMessage) => {
      if (data.likeList.length > 0) {
        this.getNode("btn_sd_zan").active = false;
      } else {
        this.getNode("btn_sd_zan").active = true;
      }
    });
  }
  private on_click_btn_back() {
    AudioMgr.instance.playEffect(520);
    UIMgr.instance.back();
  }
  private on_click_btn_help() {
    AudioMgr.instance.playEffect(522);
    UIMgr.instance.showDialog(PlayerRouteName.UIHelpPop, { titleId: -1, desId: 16 });
  }
  private on_click_btn_nixizhilu() {
    AudioMgr.instance.playEffect(1127);
    UIMgr.instance.showDialog(ShengDianRouteItem.UIShengDianChengjiu);
  }
  private on_click_btn_rongyaoge() {
    AudioMgr.instance.playEffect(1127);
    // 荣耀阁
    UIMgr.instance.showDialog(ShengDianRouteItem.UIShengDianRongyaoge);
  }
  private on_click_btn_shenji() {
    AudioMgr.instance.playEffect(1126);
    // 神迹
    UIMgr.instance.showDialog(ShengDianRouteItem.UIShengDianShenji);
  }
  private goShengDianGong(index: number) {
    let templeList = Object.values(JsonMgr.instance.jsonList.c_temple);
    UIMgr.instance.showDialog(ShengDianRouteItem.UIShengDianGong, { templeId: templeList[index].id });
  }

  private on_click_btn_gonddian1() {
    AudioMgr.instance.playEffect(1123);
    this.goShengDianGong(0);
  }
  private on_click_btn_gonddian2() {
    AudioMgr.instance.playEffect(1124);
    this.goShengDianGong(1);
  }
  private on_click_btn_gonddian3() {
    AudioMgr.instance.playEffect(1125);
    this.goShengDianGong(2);
  }

  private on_click_btn_test() {
    ShengDianModule.api.testInsertTemplate();
  }

  private on_click_btn_sd_zan() {
    AudioMgr.instance.playEffect(1122);
    ShengDianModule.api.templeLike((data: TempleLikeResponse) => {
      log.log("点赞日志", data);
      this.getNode("btn_sd_zan").active = false;
      if (data.likeList.length > 0 && ShengDianModule.data.templeMessage.placeMap[data.likeList[0]]) {
        // 展示宫殿人物效果
        UIMgr.instance.showDialog(ShengDianRouteItem.UIShengDianShow, { templeId: data.likeList[0] }, () => {
          MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardMessage.rewardList });
        });
      } else {
        MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardMessage.rewardList });
      }
      if (data.newMiracleId > 0) {
        MsgMgr.emit(MsgEnum.ON_TRIGGER_SHENJI, data.newMiracleId);
      }
    });
  }
}
