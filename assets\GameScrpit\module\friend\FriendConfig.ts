import { Sprite } from "cc";
import { IConfigFriend, IConfigFriendHeroSkill } from "../../game/JsonDefine";
import { JsonMgr } from "../../game/mgr/JsonMgr";

//id:number			des:string	costList:number[]	attrId:number	addList:number[][]
export const FriendAudioName = {
  Effect: {
    点击仙友图标: 1061,
    点击互动按钮: 1062,
    点击美名录: 1063,
    点击技能加成: 1064,
    点击页签: 1065,
    点击赠送: 1066,
    点击技能: 1067,
    点击美名: 1068,
    洗练成功: 1069,
    战将技能升级成功: 1070,
    晋升按钮成功: 1071,
    点击图鉴宝箱: 1072,
    点击宝箱领取按钮: 1073,
  },
};

export const HdFriendMubiaoAudioName = {
  Effect: {
    点击仙友目标图标: 1301,
    点击仙友切换: 1302,
    点击前往按钮: 1303,
  },
};

export class FriendConfig {
  constructor() {
    Object.values(JsonMgr.instance.jsonList.c_friend).forEach((val: IConfigFriend) => {
      this._friends.push(val);
    });
  }

  private _friends: IConfigFriend[] = [];

  public getFriendById(friendId: number): IConfigFriend {
    return JsonMgr.instance.jsonList.c_friend[`${friendId}`];
  }
  /**
   *
   * @param level
   * @returns
   */
  public getFriendHeroSkill(level: number): IConfigFriendHeroSkill {
    return JsonMgr.instance.jsonList.c_friendHeroSkill[`${10000 + level}`];
  }
  /**
   * 获取美名
   * @param id
   * @returns
   */
  public getFriendBellesByFameLevel(lv: number): {
    id: number;
    name: string;
    levelUpNeed1: number;
    levelUpNeed2: number;
    levelUpNeed3: number;
  } {
    return JsonMgr.instance.jsonList.c_friednFameLv[`${lv}`];
  }
  /**
   * 获取好友据点技能
   * @param skillId
   * @returns
   */
  public getFriendCitySkillById(skillId: number) {
    return JsonMgr.instance.jsonList.c_friendSkill[skillId];
  }
  /**
   * 获取好友目标
   */
  public obtainFriendTarget() {
    return this._friends
      .filter((value) => {
        return value.labelType && value.labelType.length > 0 && value.labelType[0] == 1;
      })
      .sort((a, b) => {
        return a.labelType[1] - b.labelType[1];
      });
  }
  public getFriendTiaojianString(key: number, finishCnt: number, total: number) {
    switch (key) {
      case 1:
        return `游历累计偶遇次数 ${finishCnt}/${total} 次`;
      case 2:
        return `VIP等级 ${finishCnt}/${total} 级`;
      case 3:
        return `主角等级 ${finishCnt}/${total} 级`;
      case 4:
        return `建筑总等级 ${finishCnt}/${total} 级`;
      case 5:
        return `繁荣度 ${finishCnt}/${total} `;
      case 6:
        return `累计徒弟结伴数量 ${finishCnt}/${total}`;
      case 7:
        return `累计获得灵兽数量 ${finishCnt}/${total}`;
      case 8:
        return `首充礼包次数 ${finishCnt}/${total}次`;
      case 9:
        return `累计徒弟成年数 ${finishCnt}/${total} `;
      case 10:
        return `演武场累计胜利 ${finishCnt}/${total} 场`;
      case 11:
        return `幸运商店 ${finishCnt}/${total}`;
      case 12:
        return `七日登录 ${finishCnt}/${total}天`;
      case 13:
        return `福地采集次数 ${finishCnt}/${total}`;
      case 14:
        return `活动`;
    }
  }
}
