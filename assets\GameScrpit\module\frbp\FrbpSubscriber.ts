import MsgEnum from "../../game/event/MsgEnum";
import { <PERSON>piHandler } from "../../game/mgr/ApiHandler";
import { FrbpCmd } from "../../game/net/cmd/CmdData";
import { RedeemBuyMessage, RedeemLimitUpdateMessage } from "../../game/net/protocol/Activity";
import { CommLongListMessage } from "../../game/net/protocol/Comm";
import MsgMgr from "../../lib/event/MsgMgr";
import { activityId, FrbpModule } from "./FrbpModule";

export class FrbpSubscriber {
  private FrlbbuyMessage(rs: RedeemBuyMessage) {
    if (rs.activityId != activityId) {
      return;
    }

    FrbpModule.data.ProsperityMessage.chosenMap = rs.chosenMap;
    FrbpModule.data.ProsperityMessage.redeemMap = rs.redeemMap;
    MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: rs.rewardList });
    MsgMgr.emit(MsgEnum.ON_FRBP_LB_UPDATE);
  }

  private FrlbAchieveFocusUp(rs: RedeemLimitUpdateMessage) {
    let list = [activityId];
    let is = list.indexOf(rs.activityId) > -1;
    if (is == false) {
      return;
    }

    FrbpModule.data.limitMap = rs.limitMap;
  }

  private upActivityDb(rs: CommLongListMessage) {
    for (let i = 0; i < rs.longList.length; i++) {
      if (rs.longList.includes(10901)) {
        FrbpModule.data.upVO();
        return;
      }
    }
  }

  public register() {
    //订阅服务器消息
    ApiHandler.instance.subscribe(RedeemBuyMessage, FrbpCmd.onAchievePayUp, this.FrlbbuyMessage);
    ApiHandler.instance.subscribe(RedeemLimitUpdateMessage, FrbpCmd.onAchieveFocusUp, this.FrlbAchieveFocusUp);
    ApiHandler.instance.subscribe(CommLongListMessage, FrbpCmd.ActivityUp, this.upActivityDb);
  }
  public unRegister() {
    //取消订阅服务器消息
    ApiHandler.instance.unSubscribe(FrbpCmd.onAchievePayUp, this.FrlbbuyMessage);
    ApiHandler.instance.unSubscribe(FrbpCmd.onAchieveFocusUp, this.FrlbAchieveFocusUp);
    ApiHandler.instance.unSubscribe(FrbpCmd.ActivityUp, this.upActivityDb);
  }
}
