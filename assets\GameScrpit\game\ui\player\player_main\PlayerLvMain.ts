import { _decorator, Color, instantiate, Label, Node, Prefab, ProgressBar, RichText, Sprite, SpriteFrame } from "cc";
import { PlayerModule } from "db://assets/GameScrpit/module/player/PlayerModule";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { BundleEnum } from "../../../bundleEnum/BundleEnum";
import { LangMgr } from "../../../mgr/LangMgr";
import { JsonMgr } from "../../../mgr/JsonMgr";
import { IConfigLeaderRecord, PlayerAudioName, PlayerMsgEnum } from "db://assets/GameScrpit/module/player/PlayerConfig";
import FmUtils from "db://assets/GameScrpit/lib/utils/FmUtils";
import Formate from "db://assets/GameScrpit/lib/utils/Formate";
import { ItemEnum } from "db://assets/GameScrpit/lib/common/ItemEnum";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { UIMgr } from "db://assets/GameScrpit/lib/ui/UIMgr";
import { PlayerRouteName } from "db://assets/GameScrpit/module/player/PlayerConstant";
import { RewardMessage } from "../../../net/protocol/Comm";
import { RewardRouteEnum } from "db://assets/GameScrpit/ext_reward/RewardDefine";
import { IConfigLeaderSkin } from "../../../JsonDefine";
import MsgMgr from "db://assets/GameScrpit/lib/event/MsgMgr";
import MsgEnum from "../../../event/MsgEnum";
import { CityRouteName } from "db://assets/GameScrpit/module/city/CityConstant";
const { ccclass, property } = _decorator;

class addAttrMap {
  [key: number]: attrInfo;
}

class attrInfo {
  hasAttr: number;
  addAttr: number;
}

const messageIdList = [503, 504, 505, 506, 507];

@ccclass("PlayerLvMain")
export class PlayerLvMain extends BaseCtrl {
  @property(Prefab)
  top_lv_add_attr_item: Prefab;

  lv_icon_layer1: Node;
  lv_icon_layer2: Node;
  lv_bg_1: Node;
  lv_bg_2: Node;
  lv_bg_3: Node;
  spr_lv_lab_bg_left: Node;
  spr_lv_lab_bg_right: Node;
  lbl_lv_1: Node;
  lbl_lv_2: Node;
  lbl_lv_3: Node;
  add_lv_atttr_content: Node;
  lay_upLv_award: Node;
  lab_left_lay: Node;
  lab_right_lay: Node;

  lab_none_lay: Node;
  progressBar_merit: Node;
  lbl_merit: Node;
  progressBar_prosperity: Node;

  lbl_prosperity: Node;
  lbl_lv_up_hit: Node;
  btn_post_lv: Node;
  lbl_btn_name: Node;

  spr_yi_da_c: Node;
  spr_wei_da_c: Node;

  private _nextShowLv: number;

  protected onLoad(): void {
    MsgMgr.on(PlayerMsgEnum.SKIN_UPDATA, this.setSkinYuLan, this);
    MsgMgr.on(MsgEnum.ON_PLAYER_NUM_UPDATE, this.upMain, this);

    this._nextShowLv = PlayerModule.data.getPlayerInfo().level + 1;
  }
  protected onDestroy(): void {
    MsgMgr.off(PlayerMsgEnum.SKIN_UPDATA, this.setSkinYuLan, this);
    MsgMgr.off(MsgEnum.ON_PLAYER_NUM_UPDATE, this.upMain, this);
  }

  start() {
    super.start();

    this.lv_icon_layer1 = this.getNode("lv_icon_layer1");
    this.lv_icon_layer2 = this.getNode("lv_icon_layer2");

    this.lv_bg_1 = this.getNode("lv_bg_1");
    this.lv_bg_2 = this.getNode("lv_bg_2");
    this.lv_bg_3 = this.getNode("lv_bg_3");

    this.spr_lv_lab_bg_left = this.getNode("spr_lv_lab_bg_left");
    this.spr_lv_lab_bg_right = this.getNode("spr_lv_lab_bg_right");

    this.lbl_lv_1 = this.getNode("lbl_lv_1");
    this.lbl_lv_2 = this.getNode("lbl_lv_2");
    this.lbl_lv_3 = this.getNode("lbl_lv_3");

    this.add_lv_atttr_content = this.getNode("add_lv_atttr_content");
    this.lay_upLv_award = this.getNode("lay_upLv_award");
    this.lab_left_lay = this.getNode("lab_left_lay");
    this.lab_right_lay = this.getNode("lab_right_lay");

    this.lab_none_lay = this.getNode("lab_none_lay");
    this.progressBar_merit = this.getNode("progressBar_merit");
    this.lbl_merit = this.getNode("lbl_merit");
    this.progressBar_prosperity = this.getNode("progressBar_prosperity");

    this.lbl_prosperity = this.getNode("lbl_prosperity");
    this.lbl_lv_up_hit = this.getNode("lbl_lv_up_hit");
    this.btn_post_lv = this.getNode("btn_post_lv");
    this.lbl_btn_name = this.getNode("lbl_btn_name");

    this.spr_yi_da_c = this.getNode("spr_yi_da_c");
    this.spr_wei_da_c = this.getNode("spr_wei_da_c");

    this.showLvMainIsState();
  }

  private upMain(id) {
    if (id == ItemEnum.功德_5) {
      this.showLvMainIsState();
    }
  }

  private showLvMainIsState() {
    this.setSkinYuLan();
    let playerLv = PlayerModule.data.getPlayerInfo().level;
    let maxLv = PlayerModule.data.getConfigLeaderData(this._nextShowLv).maxLv;
    if (this._nextShowLv >= maxLv && playerLv >= maxLv) {
      this._nextShowLv = maxLv;
      this.setMaxLvMain();
    } else if (this._nextShowLv == playerLv + 1) {
      //正常展示下一级
      this.setCurLvMain();
    } else if (this._nextShowLv <= playerLv) {
      //查看以往的等级
      this.setLastMain();
    } else if (this._nextShowLv > playerLv) {
      //查看未来等级
      this.setNextMain();
    }
  }

  private async setCurLvMain() {
    this.btn_post_lv.active = true;
    this.lbl_lv_up_hit.active = true;
    this.lv_icon_layer1.active = true;
    this.lv_icon_layer2.active = false;
    this.lab_right_lay.active = true;
    this.lab_left_lay.active = true;
    this.lab_none_lay.active = false;
    this.spr_yi_da_c.active = false;
    this.spr_wei_da_c.active = false;
    // 获取阶级配置
    const curDb = PlayerModule.data.getConfigLeaderData(this._nextShowLv - 1);
    const curJieji = curDb.jieji;
    const currentJiejiData = Object.values(JsonMgr.instance.jsonList.c_leader)
      .filter((item: IConfigLeaderRecord) => item.jieji === curJieji)
      .sort((a, b) => a.id - b.id)[0];

    const nextJiejiData = Object.values(JsonMgr.instance.jsonList.c_leader)
      .filter((item: IConfigLeaderRecord) => item.jieji > curJieji)
      .sort((a, b) => a.jieji - b.jieji)[0];

    this.assetMgr.loadSpriteFrame(
      BundleEnum.BUNDLE_G_PLAYER,
      `lv_bg_icon_image/${curDb.jingjieIcon}`,
      (spr: SpriteFrame) => {
        if (this.node.isValid == false) {
          return;
        }
        this.lv_bg_1.getComponent(Sprite).spriteFrame = spr;
      }
    );

    this.assetMgr.loadSpriteFrame(
      BundleEnum.BUNDLE_G_PLAYER,
      `lv_bg_icon_image/${curDb.jingjieBg}`,
      (spr: SpriteFrame) => {
        if (this.node.isValid == false) {
          return;
        }
        this.spr_lv_lab_bg_left.getComponent(Sprite).spriteFrame = spr;
      }
    );
    this.lv_bg_1.getChildByName("rich_text").getComponent(RichText).string = curDb.jingjie;
    this.lbl_lv_1.getComponent(Label).string = LangMgr.txMsgCode(129, [curDb.id]);

    //
    this.assetMgr.loadSpriteFrame(
      BundleEnum.BUNDLE_G_PLAYER,
      `lv_bg_icon_image/${nextJiejiData.jingjieIcon}`,
      (spr: SpriteFrame) => {
        if (this.node.isValid == false) {
          return;
        }
        this.lv_bg_2.getComponent(Sprite).spriteFrame = spr;
      }
    );

    console.log("测试====", this);

    this.assetMgr.loadSpriteFrame(
      BundleEnum.BUNDLE_G_PLAYER,
      `lv_bg_icon_image/${nextJiejiData.jinjieBg}`,
      (spr: SpriteFrame) => {
        if (this.node.isValid == false) {
          return;
        }
        this.spr_lv_lab_bg_right.getComponent(Sprite).spriteFrame = spr;
      }
    );

    this.lv_bg_2.getChildByName("rich_text").getComponent(RichText).string = nextJiejiData.jingjie;
    this.lbl_lv_2.getComponent(Label).string = LangMgr.txMsgCode(130, [nextJiejiData.id]);

    this.add_lv_atttr_content.destroyAllChildren();

    // 合并属性到attrObj
    let attrObj: addAttrMap = {};

    // 添加当前阶级属性到hasAttr
    currentJiejiData?.attrAdd?.forEach((attr) => {
      const [attrType, value] = attr;
      if (!attrObj[attrType]) {
        attrObj[attrType] = { hasAttr: 0, addAttr: 0 };
      }
      attrObj[attrType].hasAttr += value;
    });

    // 添加下个阶级属性到addAttr
    nextJiejiData?.attrAdd?.forEach((attr) => {
      const [attrType, value] = attr;
      if (!attrObj[attrType]) {
        attrObj[attrType] = { hasAttr: 0, addAttr: 0 };
      }
      attrObj[attrType].addAttr += value;
    });

    // 新增：遍历属性并创建条目
    for (const [attrType, attrInfo] of Object.entries(attrObj)) {
      const item = instantiate(this.top_lv_add_attr_item);
      item.parent = this.add_lv_atttr_content;
      item.walk((child) => (child.layer = this.node.layer));
      item.active = true;

      let strAttr = JsonMgr.instance.jsonList.c_attribute[attrType].name;

      // 设置当前属性值
      const lblCurVal = item.getChildByName("lbl_cur_val").getComponent(Label);
      lblCurVal.string = `${strAttr}+ ${attrInfo.hasAttr}`;

      // 设置提升属性值
      const lblAddVal = item.getChildByName("lbl_add_val").getComponent(Label);
      lblAddVal.string = `${strAttr}+ ${attrInfo.addAttr}`;
    }

    this.lay_upLv_award.destroyAllChildren();
    const nextDb = PlayerModule.data.getConfigLeaderData(this._nextShowLv);
    // 修改遍历方式（安全写法）
    nextDb.unlockList?.forEach((item) => {
      const [itemId, count] = item; // 显式解构元组

      // 添加类型保护
      if (!Array.isArray(item) || item.length !== 2) {
        console.error("Invalid unlockList item format:", item);
        return;
      }
      const itemNode = instantiate(this.getNode("Item"));

      // 设置层级和显示
      itemNode.parent = this.lay_upLv_award;
      itemNode.active = true;

      // 设置Layer属性
      itemNode.walk((child) => (child.layer = this.node.layer));
      FmUtils.setItemNode(itemNode, itemId, count);
      this.setUnItemState(itemNode, false);
      // let itemNodeCompLabel = itemNode.getChildByName("lbl_name").getComponent(Label);
      // itemNodeCompLabel.fontSize = 29;
      // itemNodeCompLabel.enableOutline = false;
      // itemNodeCompLabel.color = color().fromHEX("#3973ae");
    });

    const list = [
      Formate.format(curDb.dayReward),
      Formate.format(curDb.chatMax),
      Formate.format(curDb.chatTime),
      Formate.format(curDb.trainMax),
      Formate.format(curDb.trainReward),
    ];

    messageIdList.forEach((messageId, index) => {
      let itemNode = this.lab_left_lay.children[index];
      let itemNodeCompLabel = itemNode.getComponent(Label);
      itemNodeCompLabel.string = LangMgr.txMsgCode(messageId);

      let numNode = itemNode.children[0];
      numNode.getComponent(Label).string = list[index];
    });

    const list2 = [
      Formate.format(nextDb.dayReward),
      Formate.format(nextDb.chatMax),
      Formate.format(nextDb.chatTime),
      Formate.format(nextDb.trainMax),
      Formate.format(nextDb.trainReward),
    ];

    messageIdList.forEach((messageId, index) => {
      let itemNode = this.lab_right_lay.children[index];
      let itemNodeCompLabel = itemNode.getComponent(Label);
      itemNodeCompLabel.string = LangMgr.txMsgCode(messageId);

      let numNode = itemNode.children[0];
      numNode.getComponent(Label).string = list2[index];
    });

    /**我的功德 */
    let merit = PlayerModule.data.getItemNum(ItemEnum.功德_5);
    /**配置所需的功德 */
    let db_virtue = nextDb.virtue;
    this.lbl_merit.getComponent(Label).string = `${Formate.format(merit)}/${Formate.format(db_virtue)}`;
    this.progressBar_merit.getComponent(ProgressBar).progress = merit / db_virtue;
    /**我的繁荣度 */
    let bloom = PlayerModule.data.playerBattleAttrResponse.speed;
    /**配置所需的繁荣度 */
    let db_speed = nextDb.speed;
    this.lbl_prosperity.getComponent(Label).string = `${Formate.format(bloom)}/${Formate.format(db_speed)}`;
    this.progressBar_prosperity.getComponent(ProgressBar).progress = bloom / db_speed;

    // 新增：计算等级
    const currentLv = PlayerModule.data.getPlayerInfo().level;
    // 获取当前等级和下个等级的配置
    const currentConfig = PlayerModule.data.getConfigLeaderData(currentLv);
    const nextConfig = PlayerModule.data.getConfigLeaderData(PlayerModule.data.getPlayerInfo().level + 1);

    // 判断阶级是否变化
    const isJiejiChange = nextConfig?.jieji !== currentConfig.jieji;
    // 设置按钮文本
    const label = this.lbl_btn_name.getComponent(Label);
    label.string = isJiejiChange
      ? "进 阶" // 进阶
      : "升 级"; // 升级

    // // 获取当前阶级所有配置
    // const currentJiejiConfigs = Object.values(JsonMgr.instance.jsonList.c_leader)
    //   .filter((item: IConfigLeaderRecord) => item.jieji === currentConfig.jieji)
    //   .sort((a, b) => a.id - b.id);
    // // 获取当前等级在阶级配置数组中的索引
    // const currentIndex = currentJiejiConfigs.findIndex((c) => c.id === currentLv);
    // const needLevels = currentIndex + 1;
    // // 设置等级提升提示
    // if (needLevels < currentJiejiConfigs.length) {
    //   this.lbl_lv_up_hit.getComponent(Label).color = new Color().fromHEX("#ad7449");
    // } else {
    //   this.lbl_lv_up_hit.getComponent(Label).color = new Color().fromHEX("#41BD3E");
    // }

    let needLevel = nextJiejiData.id - curDb.id;
    this.lbl_lv_up_hit.getComponent(Label).string = LangMgr.txMsgCode(270, [needLevel]); // "再升%s/%s级境界进阶"
  }

  private async setNextMain() {
    this.btn_post_lv.active = false;
    this.lbl_lv_up_hit.active = false;
    this.lv_icon_layer1.active = false;
    this.lv_icon_layer2.active = true;
    this.lab_right_lay.active = false;
    this.lab_left_lay.active = false;
    this.lab_none_lay.active = true;
    this.spr_yi_da_c.active = false;
    this.spr_wei_da_c.active = true;

    const db = PlayerModule.data.getConfigLeaderData(this._nextShowLv);

    this.assetMgr.loadSpriteFrame(
      BundleEnum.BUNDLE_G_PLAYER,
      `lv_bg_icon_image/${db.jingjieIcon}`,
      (spr: SpriteFrame) => {
        if (this.node.isValid == false) {
          return;
        }
        this.lv_bg_3.getComponent(Sprite).spriteFrame = spr;
      }
    );

    this.assetMgr.loadSpriteFrame(BundleEnum.BUNDLE_G_PLAYER, `lv_bg_icon_image/${db.jinjieBg}`, (spr: SpriteFrame) => {
      if (this.node.isValid == false) {
        return;
      }
      this.spr_lv_lab_bg_right.getComponent(Sprite).spriteFrame = spr;
    });

    this.lv_bg_3.getChildByName("rich_text").getComponent(RichText).string = db.jingjie;
    this.lbl_lv_3.getComponent(Label).string = LangMgr.txMsgCode(129, [db.id]);

    this.add_lv_atttr_content.destroyAllChildren();
    const curJieji = db.jieji;

    // 获取阶级配置
    const currentJiejiData = Object.values(JsonMgr.instance.jsonList.c_leader)
      .filter((item: IConfigLeaderRecord) => item.jieji === curJieji)
      .sort((a, b) => a.id - b.id)[0];

    const nextJiejiData = Object.values(JsonMgr.instance.jsonList.c_leader)
      .filter((item: IConfigLeaderRecord) => item.jieji > curJieji)
      .sort((a, b) => a.jieji - b.jieji)[0];

    // 合并属性到attrObj
    let attrObj: addAttrMap = {};

    // 添加当前阶级属性到hasAttr
    currentJiejiData?.attrAdd?.forEach((attr) => {
      const [attrType, value] = attr;
      if (!attrObj[attrType]) {
        attrObj[attrType] = { hasAttr: 0, addAttr: 0 };
      }
      attrObj[attrType].hasAttr += value;
    });

    // 添加下个阶级属性到addAttr
    nextJiejiData?.attrAdd?.forEach((attr) => {
      const [attrType, value] = attr;
      if (!attrObj[attrType]) {
        attrObj[attrType] = { hasAttr: 0, addAttr: 0 };
      }
      attrObj[attrType].addAttr += value;
    });

    // 新增：遍历属性并创建条目
    for (const [attrType, attrInfo] of Object.entries(attrObj)) {
      const item = instantiate(this.top_lv_add_attr_item);
      item.parent = this.add_lv_atttr_content;
      item.walk((child) => (child.layer = this.node.layer));
      item.active = true;

      let strAttr = JsonMgr.instance.jsonList.c_attribute[attrType].name;

      // 设置当前属性值
      const lblCurVal = item.getChildByName("lbl_cur_val").getComponent(Label);
      lblCurVal.string = `${strAttr}+ ${attrInfo.hasAttr}`;

      // 设置提升属性值
      const lblAddVal = item.getChildByName("lbl_add_val").getComponent(Label);
      lblAddVal.string = `${strAttr}+ ${attrInfo.addAttr}`;
    }

    this.lay_upLv_award.destroyAllChildren();
    //const config = PlayerModule.data.getConfigLeaderData(this._nextShowLv);

    // 修改遍历方式（安全写法）
    db.unlockList?.forEach((item) => {
      const [itemId, count] = item; // 显式解构元组

      // 添加类型保护
      if (!Array.isArray(item) || item.length !== 2) {
        console.error("Invalid unlockList item format:", item);
        return;
      }
      const itemNode = instantiate(this.getNode("Item"));

      // 设置层级和显示
      itemNode.parent = this.lay_upLv_award;
      itemNode.active = true;

      // 设置Layer属性
      itemNode.walk((child) => (child.layer = this.node.layer));
      FmUtils.setItemNode(itemNode, itemId, count);
      itemNode.getChildByName("lbl_name").active = true;
      this.setUnItemState(itemNode, false);
    });

    let list = [
      Formate.format(db.dayReward),
      Formate.format(db.chatMax),
      Formate.format(db.chatTime),
      Formate.format(db.trainMax),
      Formate.format(db.trainReward),
    ];

    messageIdList.forEach((messageId, index) => {
      let itemNode = this.lab_none_lay.children[index];
      let itemNodeCompLabel = itemNode.getComponent(Label);
      itemNodeCompLabel.string = LangMgr.txMsgCode(messageId);

      let numNode = itemNode.children[0];
      numNode.getComponent(Label).string = list[index];
    });

    /**我的功德 */
    let merit = PlayerModule.data.getItemNum(ItemEnum.功德_5);
    /**配置所需的功德 */
    let db_virtue = db.virtue;
    this.lbl_merit.getComponent(Label).string = `${Formate.format(merit)}/${Formate.format(db_virtue)}`;
    this.progressBar_merit.getComponent(ProgressBar).progress = merit / db_virtue;
    /**我的繁荣度 */
    let bloom = PlayerModule.data.playerBattleAttrResponse.speed;
    /**配置所需的繁荣度 */
    let db_speed = db.speed;
    this.lbl_prosperity.getComponent(Label).string = `${Formate.format(bloom)}/${Formate.format(db_speed)}`;
    this.progressBar_prosperity.getComponent(ProgressBar).progress = bloom / db_speed;
  }

  private async setLastMain() {
    this.btn_post_lv.active = false;
    this.lbl_lv_up_hit.active = false;
    this.lv_icon_layer1.active = false;
    this.lv_icon_layer2.active = true;
    this.lab_right_lay.active = false;
    this.lab_left_lay.active = false;
    this.lab_none_lay.active = true;
    this.spr_yi_da_c.active = true;
    this.spr_wei_da_c.active = false;

    const db = PlayerModule.data.getConfigLeaderData(this._nextShowLv);
    this.assetMgr.loadSpriteFrame(BundleEnum.BUNDLE_G_PLAYER, `lv_bg_icon_image/${db.jingjieIcon}`, (spr) => {
      if (this.node.isValid == false) {
        return;
      }
      this.lv_bg_3.getComponent(Sprite).spriteFrame = spr;
    });

    this.assetMgr.loadSpriteFrame(BundleEnum.BUNDLE_G_PLAYER, `lv_bg_icon_image/${db.jinjieBg}`, (spr) => {
      if (this.node.isValid == false) {
        return;
      }
      this.spr_lv_lab_bg_right.getComponent(Sprite).spriteFrame;
    });

    this.lv_bg_3.getChildByName("rich_text").getComponent(RichText).string = db.jingjie;
    this.lbl_lv_3.getComponent(Label).string = LangMgr.txMsgCode(129, [db.id]);

    this.add_lv_atttr_content.destroyAllChildren();
    const curJieji = db.jieji;

    // 获取阶级配置
    const currentJiejiData = Object.values(JsonMgr.instance.jsonList.c_leader)
      .filter((item: IConfigLeaderRecord) => item.jieji === curJieji)
      .sort((a, b) => a.id - b.id)[0];

    const nextJiejiData = Object.values(JsonMgr.instance.jsonList.c_leader)
      .filter((item: IConfigLeaderRecord) => item.jieji > curJieji)
      .sort((a, b) => a.jieji - b.jieji)[0];

    // 合并属性到attrObj
    let attrObj: addAttrMap = {};

    // 添加当前阶级属性到hasAttr
    currentJiejiData?.attrAdd?.forEach((attr) => {
      const [attrType, value] = attr;
      if (!attrObj[attrType]) {
        attrObj[attrType] = { hasAttr: 0, addAttr: 0 };
      }
      attrObj[attrType].hasAttr += value;
    });

    // 添加下个阶级属性到addAttr
    nextJiejiData?.attrAdd?.forEach((attr) => {
      const [attrType, value] = attr;
      if (!attrObj[attrType]) {
        attrObj[attrType] = { hasAttr: 0, addAttr: 0 };
      }
      attrObj[attrType].addAttr += value;
    });

    // 新增：遍历属性并创建条目
    for (const [attrType, attrInfo] of Object.entries(attrObj)) {
      const item = instantiate(this.top_lv_add_attr_item);
      item.parent = this.add_lv_atttr_content;
      item.walk((child) => (child.layer = this.node.layer));
      item.active = true;

      let strAttr = JsonMgr.instance.jsonList.c_attribute[attrType].name;

      // 设置当前属性值
      const lblCurVal = item.getChildByName("lbl_cur_val").getComponent(Label);
      lblCurVal.string = `${strAttr}+ ${attrInfo.hasAttr}`;

      // 设置提升属性值
      const lblAddVal = item.getChildByName("lbl_add_val").getComponent(Label);
      lblAddVal.string = `${strAttr}+ ${attrInfo.addAttr}`;
    }

    this.lay_upLv_award.destroyAllChildren();
    //const config = PlayerModule.data.getConfigLeaderData(this._nextShowLv);

    // 修改遍历方式（安全写法）
    db.unlockList?.forEach((item) => {
      const [itemId, count] = item; // 显式解构元组

      // 添加类型保护
      if (!Array.isArray(item) || item.length !== 2) {
        console.error("Invalid unlockList item format:", item);
        return;
      }
      const itemNode = instantiate(this.getNode("Item"));

      // 设置层级和显示
      itemNode.parent = this.lay_upLv_award;
      itemNode.active = true;

      // 设置Layer属性
      itemNode.walk((child) => (child.layer = this.node.layer));
      FmUtils.setItemNode(itemNode, itemId, count);
      itemNode.getChildByName("lbl_name").active = true;
      this.setUnItemState(itemNode, true);
      // let itemNodeCompLabel = itemNode.getChildByName("lbl_name").getComponent(Label);
      // itemNodeCompLabel.fontSize = 29;
      // itemNodeCompLabel.enableOutline = false;
      // itemNodeCompLabel.color = color().fromHEX("#3973ae");
    });

    let list = [
      Formate.format(db.dayReward),
      Formate.format(db.chatMax),
      Formate.format(db.chatTime),
      Formate.format(db.trainMax),
      Formate.format(db.trainReward),
    ];
    messageIdList.forEach((messageId, index) => {
      let itemNode = this.lab_none_lay.children[index];
      let itemNodeCompLabel = itemNode.getComponent(Label);
      itemNodeCompLabel.string = LangMgr.txMsgCode(messageId);

      let numNode = itemNode.children[0];
      numNode.getComponent(Label).string = list[index];
    });

    /**我的功德 */
    let merit = PlayerModule.data.getItemNum(ItemEnum.功德_5);
    /**配置所需的功德 */
    let db_virtue = db.virtue;
    this.lbl_merit.getComponent(Label).string = `${Formate.format(merit)}/${Formate.format(db_virtue)}`;
    this.progressBar_merit.getComponent(ProgressBar).progress = merit / db_virtue;
    /**我的繁荣度 */
    let bloom = PlayerModule.data.playerBattleAttrResponse.speed;
    /**配置所需的繁荣度 */
    let db_speed = db.speed;
    this.lbl_prosperity.getComponent(Label).string = `${Formate.format(bloom)}/${Formate.format(db_speed)}`;
    this.progressBar_prosperity.getComponent(ProgressBar).progress = bloom / db_speed;
  }

  private async setMaxLvMain() {
    this.btn_post_lv.active = false;
    this.lbl_lv_up_hit.active = false;
    this.lv_icon_layer1.active = false;
    this.lv_icon_layer2.active = true;
    this.lab_right_lay.active = false;
    this.lab_left_lay.active = false;
    this.lab_none_lay.active = true;
    this.spr_yi_da_c.active = true;
    this.spr_wei_da_c.active = false;

    const db = PlayerModule.data.getConfigLeaderData(this._nextShowLv);
    this.assetMgr.loadSpriteFrame(BundleEnum.BUNDLE_G_PLAYER, `lv_bg_icon_image/${db.jingjieIcon}`, (spr) => {
      if (this.node.isValid == false) {
        return;
      }
      this.lv_bg_3.getComponent(Sprite).spriteFrame = spr;
    });

    this.assetMgr.loadSpriteFrame(BundleEnum.BUNDLE_G_PLAYER, `lv_bg_icon_image/${db.jinjieBg}`, (spr) => {
      if (this.node.isValid == false) {
        return;
      }
      this.spr_lv_lab_bg_right.getComponent(Sprite).spriteFrame = spr;
    });

    this.lv_bg_3.getChildByName("rich_text").getComponent(RichText).string = db.jingjie;
    this.lbl_lv_3.getComponent(Label).string = LangMgr.txMsgCode(129, [db.id]);

    this.add_lv_atttr_content.destroyAllChildren();
    const curJieji = db.jieji;

    // 获取阶级配置
    const currentJiejiData = Object.values(JsonMgr.instance.jsonList.c_leader)
      .filter((item: IConfigLeaderRecord) => item.jieji === curJieji)
      .sort((a, b) => a.id - b.id)[0];

    const nextJiejiData = Object.values(JsonMgr.instance.jsonList.c_leader)
      .filter((item: IConfigLeaderRecord) => item.jieji > curJieji)
      .sort((a, b) => a.jieji - b.jieji)[0];

    // 合并属性到attrObj
    let attrObj: addAttrMap = {};

    // 添加当前阶级属性到hasAttr
    currentJiejiData?.attrAdd?.forEach((attr) => {
      const [attrType, value] = attr;
      if (!attrObj[attrType]) {
        attrObj[attrType] = { hasAttr: 0, addAttr: 0 };
      }
      attrObj[attrType].hasAttr += value;
    });

    // 添加下个阶级属性到addAttr
    nextJiejiData?.attrAdd?.forEach((attr) => {
      const [attrType, value] = attr;
      if (!attrObj[attrType]) {
        attrObj[attrType] = { hasAttr: 0, addAttr: 0 };
      }
      attrObj[attrType].addAttr += value;
    });

    // 新增：遍历属性并创建条目
    for (const [attrType, attrInfo] of Object.entries(attrObj)) {
      const item = instantiate(this.top_lv_add_attr_item);
      item.parent = this.add_lv_atttr_content;
      item.walk((child) => (child.layer = this.node.layer));
      item.active = true;

      let strAttr = JsonMgr.instance.jsonList.c_attribute[attrType].name;

      // 设置当前属性值
      const lblCurVal = item.getChildByName("lbl_cur_val").getComponent(Label);
      lblCurVal.string = `${strAttr}+ ${attrInfo.hasAttr}`;

      // 设置提升属性值
      const lblAddVal = item.getChildByName("lbl_add_val").getComponent(Label);
      lblAddVal.string = `${strAttr}+ ${attrInfo.addAttr}`;
    }

    this.lay_upLv_award.destroyAllChildren();
    //const config = PlayerModule.data.getConfigLeaderData(this._nextShowLv);

    // 修改遍历方式（安全写法）
    db.unlockList?.forEach((item) => {
      const [itemId, count] = item; // 显式解构元组

      // 添加类型保护
      if (!Array.isArray(item) || item.length !== 2) {
        console.error("Invalid unlockList item format:", item);
        return;
      }
      const itemNode = instantiate(this.getNode("Item"));

      // 设置层级和显示
      itemNode.parent = this.lay_upLv_award;
      itemNode.active = true;

      // 设置Layer属性
      itemNode.walk((child) => (child.layer = this.node.layer));
      FmUtils.setItemNode(itemNode, itemId, count);
      this.setUnItemState(itemNode, true);
      // itemNode.getChildByName("lbl_name").active = true;
      // let itemNodeCompLabel = itemNode.getChildByName("lbl_name").getComponent(Label);
      // itemNodeCompLabel.fontSize = 29;
      // itemNodeCompLabel.enableOutline = false;
      // itemNodeCompLabel.color = color().fromHEX("#3973ae");
    });

    let list = [
      Formate.format(db.dayReward),
      Formate.format(db.chatMax),
      Formate.format(db.chatTime),
      Formate.format(db.trainMax),
      Formate.format(db.trainReward),
    ];
    messageIdList.forEach((messageId, index) => {
      let itemNode = this.lab_none_lay.children[index];
      let itemNodeCompLabel = itemNode.getComponent(Label);
      itemNodeCompLabel.string = LangMgr.txMsgCode(messageId);

      let numNode = itemNode.children[0];
      numNode.getComponent(Label).string = list[index];
    });

    /**我的功德 */
    let merit = PlayerModule.data.getItemNum(ItemEnum.功德_5);
    /**配置所需的功德 */
    let db_virtue = db.virtue;
    this.lbl_merit.getComponent(Label).string = `${Formate.format(merit)}/${Formate.format(db_virtue)}`;
    this.progressBar_merit.getComponent(ProgressBar).progress = merit / db_virtue;
    /**我的繁荣度 */
    let bloom = PlayerModule.data.playerBattleAttrResponse.speed;
    /**配置所需的繁荣度 */
    let db_speed = db.speed;
    this.lbl_prosperity.getComponent(Label).string = `${Formate.format(bloom)}/${Formate.format(db_speed)}`;
    this.progressBar_prosperity.getComponent(ProgressBar).progress = bloom / db_speed;
  }

  private setUnItemState(item: Node, colorBool: boolean) {
    let color = new Color(255, 255, 255, 255);
    if (colorBool == true) {
      color = new Color(100, 100, 100, 255);
      item.getChildByName("bg_yiyongyou").active = true;
    }
    item.getComponentsInChildren(Sprite).forEach((sprite) => {
      sprite.color = color;
    });
    item.getChildByName("lbl_num").getComponent(Label).color = color;
  }

  private on_click_btn_left() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    if (this._nextShowLv <= 1) {
      return;
    }
    this._nextShowLv--;
    if (this._nextShowLv <= 0) {
      this._nextShowLv = 1;
    }
    if (PlayerModule.data.getPlayerInfo().level == 1 && this._nextShowLv == 1) {
      this._nextShowLv = 2;
      return;
    }
    this.showLvMainIsState();
  }

  private on_click_btn_right() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let c_leader = JsonMgr.instance.jsonList.c_leader;
    let list = Object.keys(c_leader);
    if (this._nextShowLv >= list.length) {
      return;
    }
    this._nextShowLv++;
    if (this._nextShowLv > list.length) {
      this._nextShowLv = list.length;
    }
    this.showLvMainIsState();
  }

  private on_click_btn_post_lv() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    // 等待返回
    TipsMgr.setEnableTouch(false, 3, false);
    let c_leader_info = PlayerModule.data.getConfigLeaderData(this._nextShowLv);
    /**我的繁荣度 */
    let bloom = PlayerModule.data.playerBattleAttrResponse.speed;
    /**我的功德 */
    let merit = PlayerModule.data.getItemNum(ItemEnum.功德_5);
    if (bloom < c_leader_info.speed) {
      UIMgr.instance.showDialog(
        PlayerRouteName.UIItemFetch,
        {
          itemId: ItemEnum.繁荣度_2,
          needNum: c_leader_info.speed,
        },
        null,
        () => {
          TipsMgr.setEnableTouch(true);
        }
      );
      return;
    }
    if (merit < c_leader_info.virtue) {
      UIMgr.instance.showDialog(
        PlayerRouteName.UIItemFetch,
        {
          itemId: ItemEnum.功德_5,
          needNum: c_leader_info.virtue,
        },
        null,
        () => {
          TipsMgr.setEnableTouch(true);
        }
      );
      return;
    }
    PlayerModule.api.levelUp((data: RewardMessage) => {
      AudioMgr.instance.playEffect(PlayerAudioName.Effect.升级成功);
      this._nextShowLv = PlayerModule.data.getPlayerInfo().level + 1;
      this.showLvMainIsState();

      if (PlayerModule.data.getPlayerInfo().level == 2) {
        for (let i = 0; i < data.rewardList.length; i += 2) {
          if (data[i] === 10102) {
            data[i] = 0;
            data[i + 1] = 0;
          }
        }
        data.rewardList = data.rewardList.filter((e) => e > 0);
      }
      TipsMgr.topRouteCtrl.show(RewardRouteEnum.TopPlayerLevelUpRes, {}, () => {
        let level = PlayerModule.data.getPlayerInfo().level;
        let skin = this.getHasSkin(level);
        if (skin) {
          data.rewardList.push(skin.id, 1);
        }

        MsgMgr.emit(MsgEnum.ON_GET_AWARD, data);

        if (PlayerModule.data.getPlayerInfo().level == 2) {
          MsgMgr.once(
            MsgEnum.ON_GUIDE_NEXT,
            () => {
              UIMgr.instance.showPage(CityRouteName.UIGameMap);
            },
            this
          );
        }
      });
    });
  }

  private getHasSkin(level: number): IConfigLeaderSkin {
    let db = JsonMgr.instance.jsonList.c_leaderSkin;
    let sex = PlayerModule.data.getPlayerInfo().sex;
    let dbList = Object.keys(db);
    for (let i = 0; i < dbList.length; i++) {
      let info = db[dbList[i]];
      if (
        info.type == 1 &&
        info.sex == sex &&
        info.unlock.length >= 2 &&
        info.unlock[0] == 1 &&
        info.unlock[1] == level
      ) {
        return info;
      }
    }
    return null;
  }

  private setSkinYuLan() {
    let db = JsonMgr.instance.jsonList.c_leaderSkin;
    let sex = PlayerModule.data.getPlayerInfo().sex;
    let dbList = Object.keys(db);
    let bool = false;
    for (let i = 0; i < dbList.length; i++) {
      let info = db[dbList[i]];
      if (info.type == 1 && info.sex == sex) {
        if (!PlayerModule.data.skinMap[info.id]) {
          bool = true;
          break;
        }
      }
    }
    this.getNode("btn_jin_jie_yu_lan").active = bool;
  }

  private on_click_btn_jin_jie_yu_lan() {
    UIMgr.instance.showDialog(PlayerRouteName.UIPlayerJinJieYuLan);
  }

  update(deltaTime: number) {}
}
