import { _decorator } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { BadgeMgr, BadgeType } from "../../mgr/BadgeMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { FriendAudioName } from "../../../module/friend/FriendConfig";
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Fri Jun 21 2024 14:30:53 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/friend/UIFriendMain.ts
 *
 */

@ccclass("UIFriendMain")
export class UIFriendMain extends UINode {
  protected _openAct: boolean = true;
  //=================================================

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_FRIEND}?prefab/ui/UIFriendMain`;
  }
  //=================================================

  protected onEvtShow(): void {
    super.onEvtShow();
    BadgeMgr.instance.setBadgeId(this.getNode("tab_tujian"), BadgeType.UITerritory.btn_friend.tab_tujian.id);
    BadgeMgr.instance.setBadgeId(this.getNode("tab_friend"), BadgeType.UITerritory.btn_friend.tab_friend.id);
  }
  private on_click_tab_friend_unselect() {
    AudioMgr.instance.playEffect(FriendAudioName.Effect.点击页签);
    this.getNode("tab_friend_select").active = true;
    this.getNode("tab_tujian_select").active = false;
    this.getNode("dialog_friend").active = true;
    this.getNode("dialog_tujian").active = false;
  }
  private on_click_tab_tujian_unselect() {
    AudioMgr.instance.playEffect(FriendAudioName.Effect.点击页签);
    this.getNode("tab_friend_select").active = false;
    this.getNode("tab_tujian_select").active = true;
    this.getNode("dialog_friend").active = false;
    this.getNode("dialog_tujian").active = true;
  }
}
