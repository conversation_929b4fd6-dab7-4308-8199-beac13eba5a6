import { _decorator, Component, Node, RichText } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { JsonMgr } from "../../mgr/JsonMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { HuntModule } from "../../../module/hunt/HuntModule";
import TipMgr from "../../../lib/tips/TipMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
const { ccclass, property } = _decorator;

@ccclass("UIHuntHelp")
export class UIHuntHelp extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_HUNT}?prefab/ui/UIHuntHelp`;
  }
  protected _openAct: boolean = true; //打开动作

  protected onEvtShow(): void {
    // HuntModule.api.testResetHunt(() => {
    //   TipMgr.showTip("重置模块");
    // });

    // HuntModule.api.testSetStart(() => {
    //   TipMgr.showTip("修改洪荒出现时间");
    // });

    let c_help = JsonMgr.instance.jsonList.c_help;
    this.getNode("lab_help").getComponent(RichText).string = c_help[4].helpText;
  }

  private on_click_btn_closeMain() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  private on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
}
