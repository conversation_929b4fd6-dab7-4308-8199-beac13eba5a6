import MsgEnum from "../../../game/event/MsgEnum";
import { SystemOpenEnum } from "../../../game/GameDefine";
import { GameDirector } from "../../../game/GameDirector";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ApiHandlerSuccess } from "../../../game/mgr/ApiHandler";
import { PupilSubCmd } from "../../../game/net/cmd/CmdData";
import { BoolValue, IntValue, LongValue, LongValueList, StringValue } from "../../../game/net/protocol/ExternalMessage";
import {
  PupilAddResponse,
  PupilAdultMessage,
  PupilMarketRequest,
  PupilMarketResponse,
  PupilMarryPageRequest,
  PupilMarryPageResponse,
  PupilMarryRequest,
  PupilMarryResponse,
  PupilMessage,
  PupilRankResponse,
  PupilTrainMessage,
  PupilTrainResponse,
  PupilVitalityRequest,
  PupilVitalityResponse,
  PupilWorkRequest,
} from "../../../game/net/protocol/Pupil";
import MsgMgr from "../../../lib/event/MsgMgr";

import { PupilModule } from "./PupilModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class PupilApi {
  /** 获取弟子培养和出阵信息 */
  public getTrain(success?: ApiHandlerSuccess) {
    if (GameDirector.instance.isSystemOpen(SystemOpenEnum.PUPIL_弟子系统)) {
      ApiHandler.instance.request(PupilTrainMessage, PupilSubCmd.getTrain, null, (res: PupilTrainMessage) => {
        PupilModule.data.pupilTrainMsg = res;
        MsgMgr.emit(MsgEnum.ON_PUPIL_UPDATE);
        success && success(res);
      });
    } else {
      log.error("弟子模块未解锁");
    }
  }

  /** 获取所有的弟子信息 */
  public getAllPupil(success?: ApiHandlerSuccess) {
    ApiHandler.instance.list(PupilMessage, PupilSubCmd.getAllPupil, null, (res: PupilMessage[]) => {
      for (let i = 0; i < res.length; i++) {
        let pupil = res[i];
        PupilModule.data.allPupilMap[pupil.id] = pupil;
      }
      success && success(res);
    });
  }

  /** 获取指定的弟子信息 */
  public getPupil(pupilId: number, success?: ApiHandlerSuccess) {
    let param_data: LongValue = {
      value: pupilId,
    };
    ApiHandler.instance.request(
      PupilMessage,
      PupilSubCmd.getPupil,
      LongValue.encode(param_data),
      (data: PupilMessage) => {
        PupilModule.data.allPupilMap[data.id] = data;
        success && success(data);
      }
    );
  }

  /** 招募新的徒弟 */
  public addPupil(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(PupilAddResponse, PupilSubCmd.addPupil, null, (data: PupilAddResponse) => {
      PupilModule.data.allPupilMap[data.pupilMessage.id] = data.pupilMessage;
      PupilModule.data.pupilTrainMsg.trainSlotList[data.index] = {
        ...PupilModule.data.pupilTrainMsg.trainSlotList[data.index],
        pupilId: data.pupilMessage.id,
      };
      MsgMgr.emit(MsgEnum.ON_PUPIL_UPDATE);
      success && success(data);
    });
  }

  /** 增加活力值 */
  public addVitality(slotIndexAndCountMap: { [key: number]: number }, success?: ApiHandlerSuccess) {
    let data: PupilVitalityRequest = {
      slotIndexAndCountMap,
    };
    ApiHandler.instance.request(
      PupilVitalityResponse,
      PupilSubCmd.addVitality,
      PupilVitalityRequest.encode(data),
      (data: PupilVitalityResponse) => {
        let list = Object.keys(data.slotUpdateMap).map(Number);
        for (let i = 0; i < list.length; i++) {
          let key = list[i];
          PupilModule.data.pupilTrainMsg.trainSlotList[key] = data.slotUpdateMap[key];
        }
        MsgMgr.emit(MsgEnum.ON_PUPIL_UPDATE);
        success && success(data);
      }
    );
  }

  /** 培养徒弟一次 */
  public train(success?: ApiHandlerSuccess) {
    ApiHandler.instance.requestSync(PupilTrainResponse, PupilSubCmd.train, null, (data: PupilTrainResponse) => {
      // 新出师弟子的列表 如果列表不为空，本次培养出师弟子，否则就是培养一次弟子
      if (data.newAdultList.length > 0) {
        for (let i = 0; i < data.newAdultList.length; i++) {
          let pupilAdult: PupilAdultMessage = data.newAdultList[i];
          if (pupilAdult.pupilMessage) {
            PupilModule.data.allPupilMap[pupilAdult.pupilMessage.id] = pupilAdult.pupilMessage;
          }
        }
      }

      // index就是槽位索引，从0开始，如果当前位置没有委任，则为-1
      PupilModule.data.pupilTrainMsg.workSlotList = data.workSlotList;

      // 有更新的培养槽的信息 key：槽位索引 value:变化的槽位信息
      Object.keys(data.changeSlotMap)
        .map(Number)
        .forEach((key: number) => {
          PupilModule.data.pupilTrainMsg.trainSlotList[key] = data.changeSlotMap[key];
        });

      MsgMgr.emit(MsgEnum.ON_PUPIL_UPDATE);

      success && success(data);
    });
  }

  /** 培养徒弟一次 */
  public trainTen(success?: ApiHandlerSuccess) {
    ApiHandler.instance.requestSync(PupilTrainResponse, PupilSubCmd.trainTen, null, (data: PupilTrainResponse) => {
      // 新出师弟子的列表 如果列表不为空，本次培养出师弟子，否则就是培养一次弟子
      if (data.newAdultList.length > 0) {
        for (let i = 0; i < data.newAdultList.length; i++) {
          let pupilAdult: PupilAdultMessage = data.newAdultList[i];
          if (pupilAdult.pupilMessage) {
            PupilModule.data.allPupilMap[pupilAdult.pupilMessage.id] = pupilAdult.pupilMessage;
          }
        }
      }

      // index就是槽位索引，从0开始，如果当前位置没有委任，则为-1
      PupilModule.data.pupilTrainMsg.workSlotList = data.workSlotList;

      // 有更新的培养槽的信息 key：槽位索引 value:变化的槽位信息
      Object.keys(data.changeSlotMap)
        .map(Number)
        .forEach((key: number) => {
          PupilModule.data.pupilTrainMsg.trainSlotList[key] = data.changeSlotMap[key];
        });

      MsgMgr.emit(MsgEnum.ON_PUPIL_UPDATE);

      success && success(data);
    });
  }

  /**弟子上阵，返回上阵的徒弟的列表 */
  public work(pupilId: number, slotIndex: number, success?: ApiHandlerSuccess) {
    let data: PupilWorkRequest = {
      pupilId,
      slotIndex,
    };
    ApiHandler.instance.request(
      LongValueList,
      PupilSubCmd.work,
      PupilWorkRequest.encode(data),
      (data: LongValueList) => {
        PupilModule.data.pupilTrainMsg.workSlotList = data.values;
        MsgMgr.emit(MsgEnum.ON_PUPIL_UPDATE);
        success && success(data.values);
      }
    );
  }
  /**下阵 */
  public leaveWork(slotIndex: number, success?: ApiHandlerSuccess) {
    let data: IntValue = {
      value: slotIndex,
    };
    ApiHandler.instance.request(LongValueList, PupilSubCmd.leaveWork, IntValue.encode(data), (data: LongValueList) => {
      PupilModule.data.pupilTrainMsg.workSlotList = data.values;
      MsgMgr.emit(MsgEnum.ON_PUPIL_UPDATE);
      success && success(data);
    });
  }

  /**向全服发布弟子联姻信息 */
  public pushToMarket(pupilId: number, channelId: number, success?: ApiHandlerSuccess) {
    let data: PupilMarketRequest = {
      pupilId,
      channelId,
    };

    ApiHandler.instance.request(
      PupilMarketResponse,
      PupilSubCmd.pushToMarket,
      PupilMarketRequest.encode(data),
      (data: PupilMarketResponse) => {
        /** 对应存放联姻申请的截止时间 */
        /** CLUB(1,"妖盟"), LOCAL(2,"本地"),目前制作了本地 , CROSS(3,"跨服"),; 目前只做本地服，此参数可以不填 */
        switch (data.channelId) {
          case 1:
            PupilModule.data.pupilTrainMsg.clubMarryApplyMap = data.marryApplyMap;
            break;
          case 2:
            PupilModule.data.pupilTrainMsg.localMarryApplyMap = data.marryApplyMap;
            break;
          case 3:
            PupilModule.data.pupilTrainMsg.crossMarryApplyMap = data.marryApplyMap;
            break;
          default:
            break;
        }
        MsgMgr.emit(MsgEnum.ON_PUPIL_UPDATE, pupilId);
        success && success(data);
      }
    );
  }

  /**取消结伴的信息 如果返回False，则需要前端重新获取该弟子的信息，可能弟子已经被其他玩家联姻了 */
  public cancelMarket(pupilId: number, channelId: number, success?: ApiHandlerSuccess) {
    let data: PupilMarketRequest = {
      pupilId,
      channelId,
    };

    ApiHandler.instance.request(
      PupilMarketResponse,
      PupilSubCmd.cancelMarket,
      PupilMarketRequest.encode(data),
      (data: PupilMarketResponse) => {
        switch (data.channelId) {
          case 1:
            PupilModule.data.pupilTrainMsg.clubMarryApplyMap = data.marryApplyMap;
            break;
          case 2:
            PupilModule.data.pupilTrainMsg.localMarryApplyMap = data.marryApplyMap;
            break;
          case 3:
            PupilModule.data.pupilTrainMsg.crossMarryApplyMap = data.marryApplyMap;
            break;
          default:
            break;
        }
        MsgMgr.emit(MsgEnum.ON_PUPIL_UPDATE, pupilId);
        success && success(data);
      }
    );
  }

  /**获取全服可以联姻弟子的分页信息 */
  public marryPage(channelId: number, isCost: boolean, success?: ApiHandlerSuccess) {
    let d: PupilMarryPageRequest = {
      channelId,
      isCost,
    };
    ApiHandler.instance.request(
      PupilMarryPageResponse,
      PupilSubCmd.marryPage,
      PupilMarryPageRequest.encode(d),
      (res: PupilMarryPageResponse) => {
        PupilModule.data.localMarryRefreshCnt = res.localMarryRefreshCnt;
        success && success(res);
      }
    );
  }

  /**结伴 */
  public marry(data: PupilMarryRequest, success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(
      PupilMarryResponse,
      PupilSubCmd.marry,
      PupilMarryRequest.encode(data),
      (data: PupilMarryResponse) => {
        PupilModule.data.allPupilMap[data.pupilMessage.id] = data.pupilMessage;
        MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardList });
        MsgMgr.emit(MsgEnum.ON_PUPIL_UPDATE, data);
        success && success(data);
      }
    );
  }

  /** 当用户徒弟被联姻成功后，用户查看完弹窗后，发现请求给服务器 */
  public marryShow(pupilId: number, success?: ApiHandlerSuccess) {
    let data: LongValue = {
      value: pupilId,
    };
    ApiHandler.instance.request(BoolValue, PupilSubCmd.marryShow, LongValue.encode(data), (data: BoolValue) => {
      success && success(data);
    });
  }

  /**分页获取徒弟排行榜的徒弟 */
  public rank(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(PupilRankResponse, PupilSubCmd.rank, undefined, (data: PupilRankResponse) => {
      /** 排行 */
      success && success(data);
    });
  }

  /**获取徒弟的战力属性列表 */
  public testGetAllPupilCache(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(StringValue, PupilSubCmd.testGetAllPupilCache, undefined, (data: StringValue) => {
      success && success(data);
    });
  }
}
