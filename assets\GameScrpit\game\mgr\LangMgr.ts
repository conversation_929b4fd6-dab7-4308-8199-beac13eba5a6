import { Label, Node, RichText } from "cc";
import { JsonMgr } from "./JsonMgr";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
/**
 * 语言管理类
 */
export class LangMgr {
  /**
   * 错误消息码转换
   *
   * @param code 错误码
   * @param args 参数
   * @returns 翻译结果
   */
  public static txCode(jsonName: string, field: string, code: number, args: any[] = [], defaultMsg: string) {
    let rs = code + "-" + defaultMsg;

    if (!JsonMgr.instance.jsonList[jsonName]) {
      log.warn(`${jsonName}.json未加载`);
      return rs;
    }

    let configMsg = JsonMgr.instance.jsonList[jsonName][code];
    if (!configMsg) {
      log.warn(`${jsonName}.json中没有找到错误码：${code}`);
      if (!defaultMsg) {
        return rs;
      }
      rs = defaultMsg;
    } else {
      rs = configMsg[field];
    }

    for (let i = 0; i < args.length; i++) {
      if (args[i] === 0) {
        rs = rs.replace("s%", args[i]);
      } else {
        rs = rs.replace("s%", args[i] || "");
      }
    }
    return rs;
  }

  /**
   * 错误消息码转换
   *
   * @param code 错误码
   * @param args 参数
   * @returns 翻译结果
   */
  public static txErrorCode(code: number, args: any[] = [], defaultMsg: string) {
    return LangMgr.txCode("c_errorcode", "desc", code, args, defaultMsg);
  }

  /**转换消息码 */
  public static txMsgCode(code: number, args?: any[], defaultMsg: string = "") {
    return LangMgr.txCode("c_message", "text", code, args, defaultMsg);
  }

  public static txNode(node: Node, code: number, args: any[] = [], defaultMsg: string = "") {
    const lbl = node.getComponent(Label);
    if (lbl) {
      lbl.string = LangMgr.txMsgCode(code, args, defaultMsg);
      return;
    }

    const richText = node.getComponent(RichText);
    if (richText) {
      richText.string = LangMgr.txMsgCode(code, args, defaultMsg);
      return;
    }

    log.error(`没有找到 Label 或者 RichText 组件`);
  }
}
