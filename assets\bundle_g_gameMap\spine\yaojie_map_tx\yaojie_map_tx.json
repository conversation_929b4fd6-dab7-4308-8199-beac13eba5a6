{"skeleton": {"hash": "7DjcDQ46GtSvpIyo3eJP5Vtrh3s=", "spine": "3.8.75", "x": -254.67, "y": -121.48, "width": 558.89, "height": 237.53, "images": "", "audio": ""}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "x": 11.96, "y": -115.01}, {"name": "bone2", "parent": "bone", "length": 47.58, "rotation": 0.09, "x": -199.66, "y": 171.09}, {"name": "bone3", "parent": "bone", "length": 47.58, "rotation": 0.09, "x": -199.66, "y": 105.29}, {"name": "bone4", "parent": "bone", "length": 47.58, "rotation": 0.09, "x": -199.66, "y": 27.61}, {"name": "bone5", "parent": "bone", "length": 47.58, "rotation": 0.09, "x": -152.08, "y": 171.16}, {"name": "bone6", "parent": "bone", "length": 47.58, "rotation": 0.09, "x": -104.5, "y": 171.23}, {"name": "bone7", "parent": "bone", "length": 47.58, "rotation": 0.09, "x": -56.92, "y": 171.3}, {"name": "bone8", "parent": "bone", "length": 47.58, "rotation": 0.09, "x": -9.34, "y": 171.38}, {"name": "bone9", "parent": "bone", "length": 47.58, "rotation": 0.09, "x": 38.23, "y": 171.45}, {"name": "bone10", "parent": "bone", "length": 47.58, "rotation": 0.09, "x": 85.81, "y": 171.52}, {"name": "bone11", "parent": "bone", "length": 47.58, "rotation": 0.09, "x": 133.39, "y": 171.59}, {"name": "bone12", "parent": "bone", "length": 47.58, "rotation": 0.09, "x": 180.97, "y": 171.66}, {"name": "bone13", "parent": "bone", "length": 47.58, "rotation": 0.09, "x": -152.08, "y": 105.36}, {"name": "bone14", "parent": "bone", "length": 47.58, "rotation": 0.09, "x": -104.5, "y": 105.43}, {"name": "bone15", "parent": "bone", "length": 47.58, "rotation": 0.09, "x": -56.92, "y": 105.5}, {"name": "bone16", "parent": "bone", "length": 47.58, "rotation": 0.09, "x": -9.34, "y": 105.57}, {"name": "bone17", "parent": "bone", "length": 47.58, "rotation": 0.09, "x": 38.23, "y": 105.64}, {"name": "bone18", "parent": "bone", "length": 47.58, "rotation": 0.09, "x": 85.81, "y": 105.72}, {"name": "bone19", "parent": "bone", "length": 47.58, "rotation": 0.09, "x": 133.39, "y": 105.79}, {"name": "bone20", "parent": "bone", "length": 47.58, "rotation": 0.09, "x": 180.97, "y": 105.86}, {"name": "bone21", "parent": "bone", "length": 47.58, "rotation": 0.09, "x": -152.08, "y": 27.68}, {"name": "bone22", "parent": "bone", "length": 47.58, "rotation": 0.09, "x": -104.5, "y": 27.75}, {"name": "bone23", "parent": "bone", "length": 47.58, "rotation": 0.09, "x": -56.92, "y": 27.82}, {"name": "bone24", "parent": "bone", "length": 47.58, "rotation": 0.09, "x": -9.34, "y": 27.89}, {"name": "bone25", "parent": "bone", "length": 47.58, "rotation": 0.09, "x": 38.23, "y": 27.96}, {"name": "bone26", "parent": "bone", "length": 47.58, "rotation": 0.09, "x": 85.81, "y": 28.03}, {"name": "bone27", "parent": "bone", "length": 47.58, "rotation": 0.09, "x": 133.39, "y": 28.11}, {"name": "bone28", "parent": "bone", "length": 47.58, "rotation": 0.09, "x": 180.97, "y": 28.18}, {"name": "bone29", "parent": "root", "x": 22.61, "y": 35.04}, {"name": "bone31", "parent": "root", "x": 16.5, "y": -13.77, "scaleX": 0.9517, "scaleY": 0.9098}, {"name": "bone33", "parent": "root", "x": 16.5, "y": -66.42, "scaleX": 0.8854, "scaleY": 0.8298}, {"name": "11", "parent": "root", "x": 14.1, "y": 8.48}, {"name": "12", "parent": "11", "length": 28.81, "rotation": 1.19, "x": 65.25, "y": 10.68}, {"name": "13", "parent": "11", "length": 43.22, "rotation": 1.59, "x": 44.25, "y": -64.92}, {"name": "zhizhu2", "parent": "root", "x": 0.25, "y": 29.39, "scaleY": 0.8652}, {"name": "zhizhu", "parent": "zhizhu2", "x": -0.51, "y": -11.96}, {"name": "zhizhu3", "parent": "zhizhu", "length": 3.15, "rotation": -113.55, "x": -6.43, "y": -7.51}, {"name": "zhizhu4", "parent": "zhizhu3", "length": 4.24, "rotation": 69.26, "x": 3.15}, {"name": "zhizhu5", "parent": "zhizhu", "length": 3.18, "rotation": -62.24, "x": 6.81, "y": -7.07}, {"name": "zhizhu6", "parent": "zhizhu5", "length": 4.52, "rotation": -62.75, "x": 3.11, "y": 0.03}, {"name": "zhizhu7", "parent": "zhizhu", "length": 4.61, "rotation": -18.73, "x": 8.74, "y": -4.63}, {"name": "zhizhu8", "parent": "zhizhu7", "length": 5.92, "rotation": -50.8, "x": 4.61}, {"name": "zhizhu9", "parent": "zhizhu", "length": 3.95, "rotation": -173.54, "x": -8.06, "y": -5.07}, {"name": "zhizhu10", "parent": "zhizhu9", "length": 5.27, "rotation": 54.1, "x": 3.95}, {"name": "zhizhu11", "parent": "zhizhu", "length": 4.33, "rotation": 12.85, "x": 10.29, "y": -1.08}, {"name": "zhizhu12", "parent": "zhizhu11", "length": 4.53, "rotation": -51.22, "x": 4.33}, {"name": "zhizhu13", "parent": "zhizhu", "length": 4.67, "rotation": 27.38, "x": 9.62, "y": 3.07}, {"name": "zhizhu14", "parent": "zhizhu13", "length": 4.11, "rotation": -57.63, "x": 4.67}, {"name": "zhizhu15", "parent": "zhizhu", "length": 4.69, "rotation": 168.16, "x": -9.32, "y": -1.37}, {"name": "zhizhu16", "parent": "zhizhu15", "length": 4.99, "rotation": 51.43, "x": 4.69}, {"name": "zhizhu17", "parent": "zhizhu", "length": 4.58, "rotation": 145.54, "x": -9.32, "y": 2.7}, {"name": "zhizhu18", "parent": "zhizhu17", "length": 4.46, "rotation": 66.57, "x": 4.58}], "slots": [{"name": "<PERSON><PERSON>", "bone": "bone", "color": "7c7c7d93", "attachment": "<PERSON><PERSON>", "blend": "additive"}, {"name": "yun", "bone": "bone29", "attachment": "yun"}, {"name": "yun3", "bone": "bone31", "attachment": "yun"}, {"name": "yun5", "bone": "bone33", "attachment": "yun"}, {"name": "yun1", "bone": "12", "color": "ffffffa0", "attachment": "yun1"}, {"name": "yun2", "bone": "13", "color": "ffffffa0", "attachment": "yun1"}, {"name": "jingji", "bone": "root"}, {"name": "jingji2", "bone": "root"}, {"name": "jingji3", "bone": "root"}, {"name": "zhizhu2", "bone": "zhizhu2", "dark": "ffffff", "attachment": "zhizhu"}, {"name": "zhizhu", "bone": "zhizhu", "attachment": "zhizhu"}], "skins": [{"name": "default", "attachments": {"zhizhu2": {"zhizhu": {"type": "mesh", "uvs": [0.48989, 0.26458, 0.48989, 0.3379, 0.46575, 0.3379, 0.46575, 0.26458], "triangles": [2, 0, 1, 2, 3, 0], "vertices": [1, 35, -0.28, 0, 1, 1, 36, 0.22, -0.43, 1, 1, 36, -0.03, -0.35, 1, 1, 35, -0.54, 0, 1], "hull": 4, "edges": [6, 4, 4, 2, 6, 0, 0, 2], "width": 37, "height": 25}}, "yun": {"yun": {"x": 74.14, "y": 12.81, "width": 204, "height": 33}}, "humian": {"humian": {"type": "mesh", "uvs": [1, 1, 0.9, 1, 0.8, 1, 0.7, 1, 0.6, 1, 0.5, 1, 0.4, 1, 0.3, 1, 0.2, 1, 0.1, 1, 0, 1, 0, 0.66667, 0, 0.33333, 0, 0, 0.1, 0, 0.2, 0, 0.3, 0, 0.4, 0, 0.5, 0, 0.6, 0, 0.7, 0, 0.8, 0, 0.9, 0, 1, 0, 1, 0.33333, 1, 0.66667, 0.9, 0.66667, 0.8, 0.66667, 0.7, 0.66667, 0.6, 0.66667, 0.5, 0.66667, 0.4, 0.66667, 0.3, 0.66667, 0.2, 0.66667, 0.1, 0.66667, 0.9, 0.33333, 0.8, 0.33333, 0.7, 0.33333, 0.6, 0.33333, 0.5, 0.33333, 0.4, 0.33333, 0.3, 0.33333, 0.2, 0.33333, 0.1, 0.33333], "triangles": [0, 1, 25, 1, 26, 25, 1, 2, 26, 2, 27, 26, 2, 3, 27, 3, 28, 27, 3, 4, 28, 4, 29, 28, 4, 5, 29, 5, 30, 29, 5, 6, 30, 6, 31, 30, 6, 7, 31, 7, 32, 31, 7, 8, 32, 8, 33, 32, 8, 9, 33, 25, 26, 24, 26, 35, 24, 26, 27, 35, 27, 36, 35, 27, 28, 36, 28, 37, 36, 28, 29, 37, 29, 38, 37, 29, 30, 38, 31, 40, 39, 31, 32, 40, 30, 39, 38, 30, 31, 39, 32, 41, 40, 32, 33, 41, 24, 35, 23, 35, 22, 23, 35, 36, 22, 33, 42, 41, 33, 34, 42, 36, 21, 22, 36, 37, 21, 37, 20, 21, 37, 38, 20, 38, 19, 20, 38, 39, 19, 39, 18, 19, 39, 40, 18, 41, 16, 17, 41, 42, 16, 40, 17, 18, 40, 41, 17, 42, 15, 16, 42, 43, 15, 9, 34, 33, 9, 10, 34, 10, 11, 34, 34, 43, 42, 34, 11, 43, 11, 12, 43, 43, 14, 15, 43, 12, 14, 12, 13, 14], "vertices": [1, 28, 47.43, -6.6, 1, 2, 27, 51.51, -6.53, 0.12827, 28, 3.93, -6.53, 0.87173, 1, 27, 8.01, -6.47, 1, 1, 26, 12.09, -6.4, 1, 1, 25, 16.17, -6.33, 1, 1, 24, 20.25, -6.27, 1, 1, 23, 24.33, -6.2, 1, 1, 22, 28.41, -6.14, 1, 1, 21, 32.49, -6.07, 1, 1, 4, 36.57, -6.01, 1, 1, 4, -6.93, -5.94, 1, 2, 3, -6.97, -31.29, 0.641, 4, -6.85, 46.39, 0.359, 2, 2, -6.99, -44.76, 0.26285, 3, -6.89, 21.04, 0.73715, 1, 2, -6.91, 7.57, 1, 1, 2, 36.59, 7.51, 1, 1, 5, 32.51, 7.44, 1, 1, 6, 28.43, 7.38, 1, 1, 7, 24.35, 7.31, 1, 1, 8, 20.27, 7.25, 1, 1, 9, 16.19, 7.18, 1, 1, 10, 12.11, 7.12, 1, 1, 11, 8.03, 7.05, 1, 2, 11, 51.53, 6.99, 0.13977, 12, 3.95, 6.99, 0.86023, 1, 12, 47.45, 6.92, 1, 2, 12, 47.37, -45.41, 0.25273, 20, 47.47, 20.39, 0.74727, 3, 20, 47.39, -31.94, 0.63433, 27, 95.09, 45.74, 0.00031, 28, 47.51, 45.74, 0.36536, 4, 19, 51.47, -31.88, 0.23366, 20, 3.89, -31.88, 0.39995, 27, 51.59, 45.8, 0.15658, 28, 4.01, 45.8, 0.2098, 7, 18, 55.55, -31.81, 0.21539, 19, 7.97, -31.81, 0.41601, 20, -39.61, -31.81, 0.00902, 25, 103.25, 45.87, 0.0006, 26, 55.67, 45.87, 0.11471, 27, 8.09, 45.87, 0.23767, 28, -39.49, 45.87, 0.0066, 6, 17, 59.63, -31.75, 0.15528, 18, 12.05, -31.75, 0.47395, 19, -35.53, -31.75, 0.01247, 25, 59.75, 45.93, 0.11551, 26, 12.17, 45.93, 0.2251, 27, -35.41, 45.93, 0.01768, 6, 16, 63.71, -31.68, 0.09697, 17, 16.13, -31.68, 0.52156, 18, -31.45, -31.68, 0.01987, 24, 63.83, 46, 0.08336, 25, 16.25, 46, 0.26213, 26, -31.33, 46, 0.01611, 6, 16, 20.21, -31.62, 0.50095, 15, 67.79, -31.62, 0.0857, 17, -27.37, -31.62, 0.04679, 23, 67.91, 46.06, 0.05276, 24, 20.33, 46.06, 0.28581, 25, -27.25, 46.06, 0.02798, 6, 14, 71.87, -31.55, 0.05243, 16, -23.29, -31.55, 0.05561, 15, 24.29, -31.55, 0.52893, 22, 71.99, 46.13, 0.04167, 23, 24.41, 46.13, 0.2671, 24, -23.17, 46.13, 0.05426, 6, 13, 75.95, -31.49, 0.03813, 14, 28.37, -31.49, 0.51221, 15, -19.21, -31.49, 0.08962, 21, 76.07, 46.19, 0.03004, 22, 28.49, 46.19, 0.2668, 23, -19.09, 46.19, 0.0632, 6, 3, 80.03, -31.42, 0.01696, 4, 80.15, 46.26, 0.01369, 13, 32.45, -31.42, 0.51485, 14, -15.13, -31.42, 0.11162, 21, 32.57, 46.26, 0.2609, 22, -15.01, 46.26, 0.08198, 4, 3, 36.53, -31.36, 0.48498, 4, 36.65, 46.32, 0.24355, 13, -11.05, -31.36, 0.15996, 21, -10.93, 46.32, 0.11151, 4, 11, 51.45, -45.35, 0.09864, 12, 3.87, -45.35, 0.14861, 19, 51.55, 20.45, 0.27724, 20, 3.97, 20.45, 0.47551, 6, 10, 55.53, -45.28, 0.08996, 11, 7.95, -45.28, 0.16277, 12, -39.63, -45.28, 0.00165, 18, 55.63, 20.52, 0.20474, 19, 8.05, 20.52, 0.53992, 20, -39.53, 20.52, 0.00097, 5, 9, 59.61, -45.22, 0.06287, 10, 12.03, -45.22, 0.19163, 11, -35.55, -45.22, 0.00369, 17, 59.71, 20.58, 0.13139, 18, 12.13, 20.58, 0.61042, 6, 8, 63.69, -45.15, 0.0499, 9, 16.11, -45.15, 0.1987, 10, -31.47, -45.15, 0.00846, 16, 63.79, 20.65, 0.05937, 17, 16.21, 20.65, 0.6775, 18, -31.37, 20.65, 0.00608, 6, 7, 67.77, -45.09, 0.0314, 8, 20.19, -45.09, 0.21631, 9, -27.39, -45.09, 0.01645, 16, 20.29, 20.71, 0.67736, 15, 67.87, 20.71, 0.03757, 17, -27.29, 20.71, 0.02091, 6, 7, 24.27, -45.02, 0.2074, 6, 71.85, -45.02, 0.02329, 8, -23.31, -45.02, 0.02662, 14, 71.95, 20.78, 0.02232, 16, -23.21, 20.78, 0.01862, 15, 24.37, 20.78, 0.70177, 6, 5, 75.93, -44.96, 0.01092, 7, -19.23, -44.96, 0.03331, 6, 28.35, -44.96, 0.21857, 13, 76.03, 20.85, 0.0182, 14, 28.45, 20.85, 0.66999, 15, -19.13, 20.85, 0.04901, 6, 2, 80.01, -44.89, 0.00422, 3, 80.11, 20.91, 0.00116, 5, 32.43, -44.89, 0.20114, 6, -15.15, -44.89, 0.05479, 13, 32.53, 20.91, 0.65235, 14, -15.05, 20.91, 0.08633, 4, 2, 36.51, -44.83, 0.17995, 3, 36.61, 20.98, 0.60042, 5, -11.07, -44.83, 0.0759, 13, -10.97, 20.98, 0.14373], "hull": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 0], "width": 435, "height": 157}}, "yun1": {"yun1": {"x": -75.95, "y": 17.48, "scaleX": 2.1039, "rotation": -1.19, "width": 245, "height": 162}}, "yun2": {"yun1": {"x": 12.54, "y": 15.62, "scaleX": 1.9083, "rotation": -1.59, "width": 245, "height": 162}}, "yun3": {"yun": {"x": 74.14, "y": 12.81, "width": 204, "height": 33}}, "yun5": {"yun": {"x": 74.14, "y": 12.81, "width": 204, "height": 33}}, "zhizhu": {"zhizhu": {"type": "mesh", "uvs": [0.2564, 0.21828, 0.3104, 0.1038, 0.41985, 0.00228, 0.51618, 0, 0.6198, 0.00876, 0.68985, 0.08652, 0.74969, 0.20964, 0.82996, 0.15996, 0.91169, 0.16212, 0.99926, 0.2442, 0.9997, 0.31675, 1, 0.36732, 0.93942, 0.34572, 0.87958, 0.28092, 0.8431, 0.30468, 0.77888, 0.41484, 0.85185, 0.363, 0.91753, 0.38892, 1, 0.47316, 1, 0.55054, 1, 0.62436, 0.91461, 0.525, 0.84602, 0.49908, 0.77304, 0.55092, 0.84456, 0.5682, 0.90731, 0.67836, 0.94234, 0.80796, 0.9365, 0.93108, 0.8971, 0.92379, 0.86645, 0.91812, 0.86645, 0.79932, 0.82558, 0.71076, 0.80077, 0.66756, 0.73218, 0.66756, 0.75991, 0.741, 0.75407, 0.85116, 0.70007, 0.9246, 0.63731, 0.99588, 0.6049, 0.96087, 0.58331, 0.93756, 0.64753, 0.84684, 0.6811, 0.7734, 0.64899, 0.7302, 0.57748, 0.77124, 0.51034, 0.77124, 0.43153, 0.76476, 0.35272, 0.73668, 0.3425, 0.77124, 0.37315, 0.82956, 0.42131, 0.88356, 0.39313, 0.92917, 0.37461, 0.95916, 0.31331, 0.90948, 0.25785, 0.79716, 0.27099, 0.71724, 0.29872, 0.6762, 0.21845, 0.687, 0.17758, 0.77124, 0.14402, 0.91812, 0.10943, 0.91009, 0.06959, 0.90084, 0.08126, 0.741, 0.14256, 0.60924, 0.2345, 0.55308, 0.15131, 0.50988, 0.08272, 0.57036, 0, 0.64812, 0, 0.57681, 0, 0.52284, 0.05937, 0.43212, 0.14402, 0.37596, 0.2418, 0.39756, 0.18634, 0.30684, 0.13672, 0.2874, 0.07104, 0.33924, 0.01996, 0.35868, 0.02148, 0.31375, 0.02288, 0.27228, 0.11337, 0.17508, 0.18634, 0.1686, 0.2564, 0.32196, 0.19364, 0.24852, 0.12067, 0.2442, 0.07105, 0.27876, 0.2405, 0.46121, 0.14402, 0.44081, 0.07142, 0.49929, 0.27726, 0.61625, 0.17526, 0.64617, 0.12931, 0.76313, 0.32321, 0.70057, 0.29472, 0.77673, 0.31677, 0.85017, 0.68894, 0.68561, 0.71834, 0.78081, 0.6825, 0.87465, 0.75365, 0.60782, 0.82524, 0.63894, 0.88497, 0.72734, 0.90151, 0.82798, 0.7793, 0.4635, 0.84638, 0.43494, 0.92449, 0.4635, 0.76735, 0.28806, 0.83351, 0.23366, 0.90335, 0.2323, 0.97043, 0.28942, 0.50829, 0.19704, 0.50683, 0.35472, 0.50829, 0.51456, 0.50538, 0.64848, 0.66154, 0.53184, 0.65132, 0.32016, 0.63965, 0.16248, 0.39884, 0.156, 0.36527, 0.36984, 0.38278, 0.54048, 0.36673, 0.64632], "triangles": [82, 78, 79, 83, 77, 78, 82, 83, 78, 73, 82, 81, 76, 77, 83, 74, 76, 83, 73, 74, 83, 73, 83, 82, 75, 76, 74, 81, 79, 0, 82, 79, 81, 72, 73, 81, 72, 81, 80, 86, 69, 85, 68, 69, 86, 65, 86, 64, 86, 65, 67, 86, 67, 68, 66, 67, 65, 85, 70, 71, 69, 70, 85, 64, 85, 84, 86, 85, 64, 88, 62, 63, 89, 61, 62, 88, 89, 62, 57, 89, 88, 56, 57, 88, 60, 61, 89, 59, 60, 89, 58, 89, 57, 59, 89, 58, 56, 88, 87, 92, 47, 48, 52, 53, 92, 50, 48, 49, 92, 48, 50, 50, 52, 92, 51, 52, 50, 47, 90, 46, 90, 91, 54, 91, 90, 47, 53, 54, 91, 92, 91, 47, 92, 53, 91, 95, 94, 35, 40, 41, 95, 36, 95, 35, 38, 39, 40, 38, 40, 95, 37, 38, 95, 37, 95, 36, 94, 33, 34, 41, 93, 94, 35, 94, 34, 95, 41, 94, 98, 31, 97, 25, 98, 97, 30, 31, 98, 98, 25, 26, 99, 30, 98, 26, 99, 98, 29, 30, 99, 28, 29, 99, 27, 99, 26, 28, 99, 27, 97, 23, 24, 96, 23, 97, 32, 96, 97, 33, 96, 32, 97, 24, 25, 32, 97, 31, 17, 18, 102, 101, 17, 102, 21, 22, 102, 19, 21, 102, 18, 19, 102, 21, 19, 20, 101, 15, 16, 101, 16, 17, 100, 15, 101, 22, 100, 101, 22, 101, 102, 111, 15, 100, 23, 100, 22, 105, 7, 8, 105, 8, 9, 13, 104, 105, 106, 105, 9, 106, 9, 10, 12, 105, 106, 13, 105, 12, 12, 106, 10, 11, 12, 10, 104, 7, 105, 6, 7, 104, 14, 104, 13, 103, 104, 14, 15, 103, 14, 46, 90, 117, 90, 54, 55, 56, 87, 55, 90, 55, 117, 63, 64, 84, 84, 85, 71, 71, 72, 80, 71, 80, 115, 0, 115, 80, 112, 103, 15, 96, 111, 23, 23, 111, 100, 94, 93, 33, 33, 93, 96, 117, 87, 116, 110, 111, 93, 84, 115, 116, 111, 112, 15, 114, 115, 0, 107, 2, 3, 114, 2, 107, 107, 3, 4, 108, 114, 107, 107, 4, 113, 115, 114, 108, 107, 112, 108, 112, 107, 113, 109, 108, 112, 115, 108, 109, 109, 112, 111, 116, 115, 109, 110, 116, 109, 110, 109, 111, 113, 4, 5, 114, 1, 2, 113, 5, 6, 112, 113, 6, 43, 44, 110, 42, 43, 110, 117, 116, 110, 45, 117, 110, 45, 110, 44, 42, 110, 93, 46, 117, 45, 87, 84, 116, 96, 93, 111, 41, 42, 93, 112, 6, 103, 103, 6, 104, 114, 0, 1, 81, 0, 80, 115, 84, 71, 63, 84, 87, 88, 63, 87, 55, 87, 117], "vertices": [1, 36, -9.04, 4.83, 1, 1, 36, -7.04, 7.69, 1, 1, 36, -2.99, 10.23, 1, 1, 36, 0.57, 10.28, 1, 1, 36, 4.41, 10.07, 1, 1, 36, 7, 8.12, 1, 1, 36, 9.21, 5.04, 1, 4, 36, 12.18, 6.29, 7e-05, 47, 3.75, 1.68, 0.92593, 48, -1.91, 0.13, 0.07399, 51, -15.7, -15.13, 2e-05, 2, 47, 6.41, 0.24, 0.01053, 48, 0.73, 1.6, 0.98947, 1, 48, 4.56, 1.46, 1, 1, 48, 5.49, -0.09, 1, 1, 48, 6.14, -1.18, 1, 2, 47, 5.21, -4.31, 0.01313, 48, 3.93, -1.84, 0.98687, 3, 47, 3.99, -1.85, 0.40764, 48, 1.2, -1.56, 0.59113, 45, 4.6, 3.4, 0.00123, 3, 47, 2.52, -1.76, 0.88633, 48, 0.33, -2.75, 0.07534, 45, 3.15, 3.12, 0.03832, 3, 36, 10.29, -0.09, 0.00465, 47, -0.86, -3.11, 0.17508, 45, 0.22, 0.96, 0.82028, 2, 45, 3.14, 1.63, 0.97211, 46, -2.01, 0.1, 0.02789, 2, 45, 5.37, 0.46, 0.21358, 46, 0.3, 1.1, 0.78642, 1, 46, 4, 1.34, 1, 1, 46, 5.2, -0.18, 1, 1, 46, 6.34, -1.62, 1, 2, 45, 4.51, -2.84, 0.0989, 46, 2.33, -1.64, 0.9011, 3, 45, 2.18, -1.64, 0.83591, 46, -0.07, -2.7, 0.15156, 41, 3.05, 3.6, 0.01252, 1, 36, 10.08, -3.49, 1, 2, 45, 1.74, -3.31, 0.00469, 41, 3.55, 1.95, 0.99531, 2, 41, 6.63, 0.09, 0.03811, 42, 1.21, 1.62, 0.96189, 1, 42, 4.7, 1.7, 1, 1, 42, 7.51, 0.42, 1, 1, 42, 6.83, -0.88, 1, 1, 42, 6.3, -1.89, 1, 2, 41, 6.17, -3.26, 0.00362, 42, 3.51, -0.85, 0.99638, 2, 41, 4.03, -1.65, 0.40838, 42, 0.91, -1.49, 0.59162, 3, 41, 2.81, -0.92, 0.96085, 42, -0.42, -1.98, 0.03236, 39, 1.41, 4.11, 0.00679, 1, 36, 8.56, -6.4, 1, 2, 41, 1.97, -3.15, 0.05152, 39, 2.33, 1.91, 0.94848, 2, 39, 4.67, 0.44, 0.03937, 40, 0.35, 1.57, 0.96063, 1, 40, 3, 0.98, 1, 1, 40, 5.79, 0.1, 1, 1, 40, 5.76, -1.38, 1, 1, 40, 5.74, -2.37, 1, 2, 39, 2.73, -3, 0.07096, 40, 2.52, -1.72, 0.92904, 4, 36, 6.67, -9.05, 0.0005, 39, 1.69, -1.04, 0.75659, 40, 0.31, -1.76, 0.24178, 37, -3.83, 12.63, 0.00113, 1, 36, 5.49, -7.97, 1, 1, 36, 2.84, -9, 1, 1, 36, 0.36, -9, 1, 1, 36, -2.56, -8.83, 1, 4, 36, -5.48, -8.13, 0.03315, 39, -4.78, -11.37, 0.03077, 37, 0.18, 1.13, 0.91874, 38, 0, 3.17, 0.01734, 4, 36, -5.85, -9, 0.00374, 39, -4.19, -12.1, 0.0041, 37, 1.13, 1.12, 0.82295, 38, 0.34, 2.29, 0.16922, 2, 37, 2.01, 2.75, 0.15674, 38, 2.17, 2.04, 0.84326, 1, 38, 4.38, 2.32, 1, 1, 38, 4.43, 0.77, 1, 2, 37, 4.96, 4.09, 3e-05, 38, 4.47, -0.24, 0.99997, 1, 38, 1.98, -0.94, 1, 2, 37, 2.97, -1.49, 0.94924, 38, -1.45, -0.36, 0.05076, 2, 37, 0.95, -1.84, 0.92468, 43, 0.72, 2.51, 0.07532, 1, 36, -7.47, -6.62, 1, 3, 37, 1.03, -3.92, 0.00234, 43, 2.57, 1.54, 0.78003, 44, 0.44, 2.02, 0.21763, 1, 44, 3.02, 1.74, 1, 1, 44, 6.82, 2.46, 1, 1, 44, 7.28, 1.25, 1, 1, 44, 7.8, -0.15, 1, 1, 44, 4.11, -1.74, 1, 2, 43, 5.14, -0.71, 0.13132, 44, 0.13, -1.38, 0.86868, 1, 36, -9.85, -3.54, 1, 3, 43, 4.54, -3.14, 0.01264, 49, 3.31, 1.81, 0.74653, 50, 0.55, 2.21, 0.24084, 2, 49, 5.48, 3.81, 0.00684, 50, 3.47, 1.75, 0.99316, 1, 50, 7.07, 1.3, 1, 1, 50, 5.93, -0.07, 1, 1, 50, 5.07, -1.11, 1, 1, 50, 1.93, -1.46, 1, 3, 49, 4.26, -1.41, 0.98427, 50, -1.37, -0.55, 0.01359, 51, 2.17, 3.69, 0.00214, 1, 36, -9.58, 0.35, 1, 3, 49, 3.08, -3.43, 0.01061, 51, 1.86, 1.38, 0.94636, 52, 0.18, 3.04, 0.04303, 2, 51, 3.65, 2.02, 0.33968, 52, 1.48, 1.66, 0.66032, 1, 52, 4.23, 1.46, 1, 1, 52, 6.09, 0.87, 1, 1, 52, 5.44, -0.05, 1, 2, 51, 7.33, 4.09, 0, 52, 4.85, -0.9, 1, 2, 51, 5.95, 0.19, 0.00294, 52, 0.72, -1.18, 0.99706, 2, 51, 3.81, -1.47, 0.95047, 52, -1.65, 0.12, 0.04953, 1, 36, -9.04, 2.24, 1, 4, 36, -11.36, 4.07, 0.00024, 47, -18.17, 10.54, 4e-05, 51, 2.46, 0.02, 0.99441, 52, -0.82, 1.95, 0.00532, 2, 51, 4.75, 1.46, 0.05711, 52, 1.41, 0.43, 0.94289, 1, 52, 3.42, 0.18, 1, 1, 36, -9.63, -1.25, 1, 3, 43, 4.61, -4.89, 0.0002, 49, 3.93, 0.17, 0.95717, 50, -0.34, 0.7, 0.04263, 1, 50, 2.66, 0.12, 1, 1, 36, -8.27, -5.12, 1, 2, 43, 4.04, 0.35, 0.32001, 44, 0.34, 0.12, 0.67999, 1, 44, 3.72, 0.08, 1, 1, 36, -6.57, -7.23, 1, 2, 37, 1.96, -0.44, 0.99695, 43, 0.02, 4.09, 0.00305, 2, 37, 3.32, 1.04, 0.03256, 38, 1.03, 0.21, 0.96744, 1, 36, 6.96, -6.86, 1, 3, 41, 0.83, -4.58, 0.00114, 39, 2.49, 0.09, 0.92271, 40, -0.33, -0.52, 0.07615, 2, 39, 3.95, -2.18, 0.00601, 40, 2.35, -0.26, 0.99399, 1, 41, 0.68, -0.07, 1, 2, 45, 0.65, -4.88, 1e-05, 41, 3.44, 0.05, 0.99999, 1, 42, 2.07, 0.42, 1, 1, 42, 4.64, 0.11, 1, 3, 45, -0.03, -0.23, 0.97454, 46, -2.55, -3.54, 0.00068, 41, 0.42, 3.65, 0.02478, 3, 45, 2.55, -0.08, 0.99641, 46, -1.05, -1.44, 0.00349, 41, 2.54, 5.13, 9e-05, 2, 45, 5.21, -1.42, 0.00628, 46, 1.66, -0.2, 0.99372, 1, 36, 9.87, 3.08, 1, 2, 47, 3.02, -0.02, 0.99981, 45, 3.2, 4.93, 0.00019, 3, 47, 5.33, -1.17, 0.00175, 48, 1.35, -0.07, 0.99822, 45, 5.73, 4.39, 3e-05, 1, 48, 4.21, -0.05, 1, 1, 36, 0.28, 5.36, 1, 1, 36, 0.23, 1.42, 1, 1, 36, 0.28, -2.58, 1, 1, 36, 0.17, -5.93, 1, 1, 36, 5.95, -3.01, 1, 1, 36, 5.57, 2.28, 1, 1, 36, 5.14, 6.22, 1, 1, 36, -3.77, 6.38, 1, 1, 36, -5.01, 1.04, 1, 1, 36, -4.36, -3.23, 1, 1, 36, -4.96, -5.87, 1], "hull": 80, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 154, 156, 156, 158, 0, 158, 0, 160, 160, 142, 160, 162, 162, 164, 164, 166, 150, 152, 152, 154, 166, 152, 168, 170, 170, 172, 132, 134, 134, 136, 172, 134, 174, 176, 176, 178, 116, 118, 118, 120, 178, 118, 180, 182, 182, 184, 98, 100, 100, 102, 184, 100, 186, 188, 188, 190, 74, 76, 76, 78, 190, 76, 192, 194, 194, 196, 196, 198, 54, 56, 56, 58, 198, 56, 200, 202, 202, 204, 36, 38, 38, 40, 204, 38, 206, 208, 208, 210, 210, 212, 18, 20, 20, 22, 212, 20, 6, 214, 214, 216, 216, 218, 218, 220, 220, 88, 186, 220], "width": 37, "height": 25}}}}], "animations": {"animation1": {"slots": {"yun5": {"color": [{"time": 2.5333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 6.2667, "color": "ffffff00"}, {"time": 6.8, "color": "ffffff47"}, {"time": 8.1, "color": "ffffffff"}]}, "zhizhu": {"color": [{"color": "ffffff00"}]}, "zhizhu2": {"twoColor": [{"light": "ffffff00", "dark": "ffffff"}]}, "yun3": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.4333, "color": "ffffff00"}, {"time": 2.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff", "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 4.1667, "color": "ffffff00"}]}, "yun": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.8, "color": "ffffffff", "curve": "stepped"}, {"time": 9.5, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 11.2667, "color": "ffffff00"}]}}, "bones": {"bone28": {"translate": [{"y": -3.51, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5667, "y": -5.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.7, "y": -0.72, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.9667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.8333, "y": -3.51, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.4, "y": -5.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 4.5333, "y": -0.72, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.8, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.6667, "y": -3.51, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.2333, "y": -5.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 7.3333, "y": -0.72, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 7.6333, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8.4667, "y": -3.51, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 9.0333, "y": -5.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 10.1667, "y": -0.72, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 10.4667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 11.3, "y": -3.51}]}, "bone27": {"translate": [{"y": -4.84, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.1333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.7, "y": -2.05, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.5333, "y": -5.56, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.8333, "y": -4.84, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.9667, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4.5333, "y": -2.05, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 5.3667, "y": -5.56, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.6667, "y": -4.84, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 6.7667, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 7.3333, "y": -2.05, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 8.2, "y": -5.56, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 8.4667, "y": -4.84, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 9.6, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 10.1667, "y": -2.05, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 11.0333, "y": -5.56, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 11.3, "y": -4.84}]}, "bone26": {"translate": [{"y": -0.72, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "y": -5.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.8333, "y": -0.72, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.1, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "y": -5.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.6667, "y": -0.72, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.9333, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "y": -5.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 8.4667, "y": -0.72, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 8.7667, "curve": 0.25, "c3": 0.75}, {"time": 10.1667, "y": -5.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 11.3, "y": -0.72}]}, "bone25": {"translate": [{"y": -2.05, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8333, "y": -5.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.7, "y": -2.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.2667, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.8333, "y": -2.05, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.6667, "y": -5.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4.5333, "y": -2.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 5.1, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.6667, "y": -2.05, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.5, "y": -5.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 7.3333, "y": -2.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 7.9, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 8.4667, "y": -2.05, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 9.3333, "y": -5.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 10.1667, "y": -2.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 10.7333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 11.3, "y": -2.05}]}, "bone24": {"translate": [{"y": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.7, "y": -0.72, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 2.8333, "y": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 4.2333, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4.5333, "y": -0.72, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 5.6667, "y": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 7.0667, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 7.3333, "y": -0.72, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 8.4667, "y": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 9.9, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 10.1667, "y": -0.72, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 11.3, "y": -5.56}]}, "bone23": {"translate": [{"y": -2.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5667, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.7, "y": -4.84, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.9667, "y": -5.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.8333, "y": -2.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.4, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 4.5333, "y": -4.84, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.8, "y": -5.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.6667, "y": -2.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.2333, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 7.3333, "y": -4.84, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 7.6333, "y": -5.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8.4667, "y": -2.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 9.0333, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 10.1667, "y": -4.84, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 10.4667, "y": -5.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 11.3, "y": -2.05}]}, "bone22": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.4, "y": -5.56, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.7, "y": -4.84, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.2333, "y": -5.56, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4.5333, "y": -4.84, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.0667, "y": -5.56, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 7.3333, "y": -4.84, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 8.4667, "curve": 0.25, "c3": 0.75}, {"time": 9.9, "y": -5.56, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 10.1667, "y": -4.84, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 11.3}]}, "bone21": {"translate": [{"y": -2.05, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8333, "y": -5.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.7, "y": -2.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.2667, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.8333, "y": -2.05, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.6667, "y": -5.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4.5333, "y": -2.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 5.1, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.6667, "y": -2.05, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.5, "y": -5.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 7.3333, "y": -2.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 7.9, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 8.4667, "y": -2.05, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 9.3333, "y": -5.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 10.1667, "y": -2.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 10.7333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 11.3, "y": -2.05}]}, "bone20": {"translate": [{"y": -4.83, "curve": 0.306, "c2": 0.24, "c3": 0.694, "c4": 0.76}, {"time": 0.8333, "y": -0.72, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.1333, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "y": -5.56, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.8333, "y": -4.83, "curve": 0.306, "c2": 0.24, "c3": 0.694, "c4": 0.76}, {"time": 3.6667, "y": -0.72, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.9667, "curve": 0.25, "c3": 0.75}, {"time": 5.3667, "y": -5.56, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.6667, "y": -4.83, "curve": 0.306, "c2": 0.24, "c3": 0.694, "c4": 0.76}, {"time": 6.5, "y": -0.72, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 6.7667, "curve": 0.25, "c3": 0.75}, {"time": 8.2, "y": -5.56, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 8.4667, "y": -4.83, "curve": 0.306, "c2": 0.24, "c3": 0.694, "c4": 0.76}, {"time": 9.3333, "y": -0.72, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 9.6, "curve": 0.25, "c3": 0.75}, {"time": 11.0333, "y": -5.56, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 11.3, "y": -4.83}]}, "bone19": {"translate": [{"y": -0.72, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2667, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.8333, "y": -2.05, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.7, "y": -5.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.8333, "y": -0.72, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.1, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.6667, "y": -2.05, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.5333, "y": -5.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.6667, "y": -0.72, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.9333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 6.5, "y": -2.05, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 7.3333, "y": -5.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 8.4667, "y": -0.72, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 8.7667, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 9.3333, "y": -2.05, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 10.1667, "y": -5.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 11.3, "y": -0.72}]}, "bone18": {"translate": [{"y": -2.05, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8333, "y": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.8333, "y": -2.05, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.6667, "y": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.6667, "y": -2.05, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.5, "y": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 7.9, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 8.4667, "y": -2.05, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 9.3333, "y": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 10.7333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 11.3, "y": -2.05}]}, "bone17": {"translate": [{"y": -5.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.8333, "y": -2.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "y": -5.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.6667, "y": -2.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.2333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "y": -5.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6.5, "y": -2.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 7.0667, "curve": 0.25, "c3": 0.75}, {"time": 8.4667, "y": -5.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 9.3333, "y": -2.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 9.9, "curve": 0.25, "c3": 0.75}, {"time": 11.3, "y": -5.56}]}, "bone16": {"translate": [{"y": -2.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5667, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.8333, "y": -0.72, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.9667, "y": -5.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.8333, "y": -2.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.4, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 3.6667, "y": -0.72, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 4.8, "y": -5.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.6667, "y": -2.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.2333, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 6.5, "y": -0.72, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 7.6333, "y": -5.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8.4667, "y": -2.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 9.0333, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 9.3333, "y": -0.72, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 10.4667, "y": -5.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 11.3, "y": -2.05}]}, "bone15": {"translate": [{"y": -0.73, "curve": 0.306, "c2": 0.24, "c3": 0.694, "c4": 0.76}, {"time": 0.8333, "y": -4.84, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.1333, "y": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.8333, "y": -0.73, "curve": 0.306, "c2": 0.24, "c3": 0.694, "c4": 0.76}, {"time": 3.6667, "y": -4.84, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.9667, "y": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 5.3667, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.6667, "y": -0.73, "curve": 0.306, "c2": 0.24, "c3": 0.694, "c4": 0.76}, {"time": 6.5, "y": -4.84, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 6.7667, "y": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 8.2, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 8.4667, "y": -0.73, "curve": 0.306, "c2": 0.24, "c3": 0.694, "c4": 0.76}, {"time": 9.3333, "y": -4.84, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 9.6, "y": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 11.0333, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 11.3, "y": -0.73}]}, "bone14": {"translate": [{"y": -3.51, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5667, "y": -5.56, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.8333, "y": -4.84, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.9667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.8333, "y": -3.51, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.4, "y": -5.56, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 3.6667, "y": -4.84, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 4.8, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.6667, "y": -3.51, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.2333, "y": -5.56, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 6.5, "y": -4.84, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 7.6333, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8.4667, "y": -3.51, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 9.0333, "y": -5.56, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 9.3333, "y": -4.84, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 10.4667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 11.3, "y": -3.51}]}, "bone13": {"translate": [{"y": -5.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.8333, "y": -2.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "y": -5.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.6667, "y": -2.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.2333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "y": -5.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6.5, "y": -2.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 7.0667, "curve": 0.25, "c3": 0.75}, {"time": 8.4667, "y": -5.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 9.3333, "y": -2.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 9.9, "curve": 0.25, "c3": 0.75}, {"time": 11.3, "y": -5.56}]}, "bone12": {"translate": [{"y": -0.72, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "y": -5.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.8333, "y": -0.72, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.1, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "y": -5.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.6667, "y": -0.72, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.9333, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "y": -5.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 8.4667, "y": -0.72, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 8.7667, "curve": 0.25, "c3": 0.75}, {"time": 10.1667, "y": -5.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 11.3, "y": -0.72}]}, "bone11": {"translate": [{"y": -2.05, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8333, "y": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.8333, "y": -2.05, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.6667, "y": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.6667, "y": -2.05, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.5, "y": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 7.9, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 8.4667, "y": -2.05, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 9.3333, "y": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 10.7333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 11.3, "y": -2.05}]}, "bone10": {"translate": [{"y": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "y": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 4.2333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "y": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 7.0667, "curve": 0.25, "c3": 0.75}, {"time": 8.4667, "y": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 9.9, "curve": 0.25, "c3": 0.75}, {"time": 11.3, "y": -5.56}]}, "bone9": {"translate": [{"y": -2.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "y": -5.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.8333, "y": -2.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.4, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "y": -5.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.6667, "y": -2.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.2333, "curve": 0.25, "c3": 0.75}, {"time": 7.6333, "y": -5.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8.4667, "y": -2.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 9.0333, "curve": 0.25, "c3": 0.75}, {"time": 10.4667, "y": -5.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 11.3, "y": -2.05}]}, "bone8": {"translate": [{"y": -0.72, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.1333, "y": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.8333, "y": -0.72, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.9667, "y": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 5.3667, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.6667, "y": -0.72, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 6.7667, "y": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 8.2, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 8.4667, "y": -0.72, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 9.6, "y": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 11.0333, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 11.3, "y": -0.72}]}, "bone7": {"translate": [{"y": -4.84, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2667, "y": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.8333, "y": -4.84, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.1, "y": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.6667, "y": -4.84, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.9333, "y": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 8.4667, "y": -4.84, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 8.7667, "y": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 10.1667, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 11.3, "y": -4.84}]}, "bone6": {"translate": [{"y": -4.84, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.1333, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "y": -5.56, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.8333, "y": -4.84, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.9667, "curve": 0.25, "c3": 0.75}, {"time": 5.3667, "y": -5.56, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5.6667, "y": -4.84, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 6.7667, "curve": 0.25, "c3": 0.75}, {"time": 8.2, "y": -5.56, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 8.4667, "y": -4.84, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 9.6, "curve": 0.25, "c3": 0.75}, {"time": 11.0333, "y": -5.56, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 11.3, "y": -4.84}]}, "bone5": {"translate": [{"y": -2.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "y": -5.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.8333, "y": -2.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.4, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "y": -5.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.6667, "y": -2.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.2333, "curve": 0.25, "c3": 0.75}, {"time": 7.6333, "y": -5.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8.4667, "y": -2.05, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 9.0333, "curve": 0.25, "c3": 0.75}, {"time": 10.4667, "y": -5.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 11.3, "y": -2.05}]}, "bone4": {"translate": [{"y": -4.84, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2667, "y": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.8333, "y": -4.84, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.1, "y": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5.6667, "y": -4.84, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.9333, "y": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 8.4667, "y": -4.84, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 8.7667, "y": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 10.1667, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 11.3, "y": -4.84}]}, "bone3": {"translate": [{"y": -3.51, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "y": -5.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.8333, "y": -3.51, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "y": -5.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.6667, "y": -3.51, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 6.5, "curve": 0.25, "c3": 0.75}, {"time": 7.9, "y": -5.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 8.4667, "y": -3.51, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 9.3333, "curve": 0.25, "c3": 0.75}, {"time": 10.7333, "y": -5.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 11.3, "y": -3.51}]}, "bone2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.4, "y": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.2333, "y": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.0667, "y": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 8.4667, "curve": 0.25, "c3": 0.75}, {"time": 9.9, "y": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 11.3}]}, "bone29": {"translate": [{"x": 10.78}, {"time": 11.2667, "x": -177.26, "curve": "stepped"}, {"time": 11.3, "x": 10.78}]}, "bone31": {"translate": [{"x": 10.78, "curve": "stepped"}, {"time": 1.4333, "x": 10.78}, {"time": 6.1333, "x": -96}, {"time": 6.6333, "x": -130.01}, {"time": 7.0667, "x": -177.26}]}, "bone33": {"translate": [{"x": -73.32}, {"time": 1.3667, "x": -96}, {"time": 3.4, "x": -130.01}, {"time": 6.2333, "x": -177.26, "curve": "stepped"}, {"time": 6.2667, "x": 10.78}, {"time": 6.8, "x": 2.28}, {"time": 11.3, "x": -73.32}]}, "13": {"translate": [{"y": 25.56, "curve": 0.25, "c3": 0.75}, {"time": 3.0667, "y": -7.3, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "y": 25.56, "curve": 0.25, "c3": 0.75}, {"time": 8.7333, "y": -7.3, "curve": 0.25, "c3": 0.75}, {"time": 11.3, "y": 25.56}]}, "12": {"translate": [{"y": 3.43, "curve": 0.358, "c2": 0.42, "c3": 0.756}, {"time": 1.6333, "y": 25.56, "curve": 0.25, "c3": 0.75}, {"time": 4.7333, "y": -7.3, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 5.6667, "y": 3.43, "curve": 0.358, "c2": 0.42, "c3": 0.756}, {"time": 7.2667, "y": 25.56, "curve": 0.25, "c3": 0.75}, {"time": 10.3667, "y": -7.3, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 11.3, "y": 3.43}]}}}, "animation2": {"slots": {"humian": {"color": [{"color": "7c7c7d00"}]}, "jingji2": {"color": [{"color": "ffffff00"}]}, "jingji": {"color": [{"color": "ffffff00"}]}, "yun5": {"color": [{"color": "ffffff00"}]}, "yun2": {"color": [{"color": "ffffff00"}]}, "yun1": {"color": [{"color": "ffffff00"}]}, "yun3": {"color": [{"color": "ffffff00"}]}, "yun": {"color": [{"color": "ffffff00"}]}, "jingji3": {"color": [{"color": "ffffff00"}]}}, "bones": {"zhizhu": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": -45.15, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "y": -41.02, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "y": -45.15, "curve": "stepped"}, {"time": 2.3667, "y": -45.15, "curve": 0.25, "c3": 0.75}, {"time": 3.4333}]}, "zhizhu2": {"rotate": [{"time": 0.4667}, {"time": 1, "angle": -4.73}, {"time": 1.4667}, {"time": 1.9, "angle": 2.57}, {"time": 2.3}]}, "zhizhu17": {"rotate": [{"time": 2.3667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.4667, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.7667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.8667, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.9667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.0667, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.1667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.2667, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3667}]}, "zhizhu18": {"rotate": [{"time": 2.4333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.5333, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.7333, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.8333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.9333, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.0333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.1333, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.2333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.4333}]}, "zhizhu16": {"rotate": [{"time": 2.4333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.5333, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.7333, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.8333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.9333, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.0333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.1333, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.2333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.4333}]}, "zhizhu15": {"rotate": [{"time": 2.3667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.4667, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.7667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.8667, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.9667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.0667, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.1667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.2667, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3667}]}, "zhizhu10": {"rotate": [{"time": 2.4333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.5333, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.7333, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.8333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.9333, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.0333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.1333, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.2333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.4333}]}, "zhizhu9": {"rotate": [{"time": 2.3667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.4667, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.7667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.8667, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.9667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.0667, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.1667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.2667, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3667}]}, "zhizhu3": {"rotate": [{"time": 2.3667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.4667, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.7667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.8667, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.9667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.0667, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.1667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.2667, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3667}]}, "zhizhu4": {"rotate": [{"time": 2.4333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.5333, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.7333, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.8333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.9333, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.0333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.1333, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.2333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": -25.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.4333}]}, "zhizhu5": {"rotate": [{"time": 2.3667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.4667, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.7667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.8667, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.9667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.0667, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.1667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.2667, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3667}]}, "zhizhu6": {"rotate": [{"time": 2.4333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.5333, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.7333, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.8333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.9333, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.0333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.1333, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.2333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.4333}]}, "zhizhu7": {"rotate": [{"time": 2.3667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.4667, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.7667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.8667, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.9667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.0667, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.1667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.2667, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3667}]}, "zhizhu8": {"rotate": [{"time": 2.4333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.5333, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.7333, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.8333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.9333, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.0333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.1333, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.2333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.4333}]}, "zhizhu11": {"rotate": [{"time": 2.3667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.4667, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.7667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.8667, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.9667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.0667, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.1667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.2667, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3667}]}, "zhizhu12": {"rotate": [{"time": 2.4333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.5333, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.7333, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.8333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.9333, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.0333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.1333, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.2333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.4333}]}, "zhizhu13": {"rotate": [{"time": 2.3667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.4667, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.7667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.8667, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.9667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.0667, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.1667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.2667, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3667}]}, "zhizhu14": {"rotate": [{"time": 2.4333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.5333, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.7333, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.8333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.9333, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.0333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.1333, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.2333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": 29.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.4333}]}}}}}