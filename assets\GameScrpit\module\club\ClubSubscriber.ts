import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../game/mgr/ApiHandler";
import { ClubSubCmd } from "../../game/net/cmd/CmdData";
import {
  BargainRecordMessage,
  ClubApplyMessage,
  ClubBargainMessage,
  ClubBossTrainMessage,
  ClubLogMessage,
  ClubPopUpMessage,
} from "../../game/net/protocol/Club";
import { CommLongMapMessage } from "../../game/net/protocol/Comm";
// import { ClubNoticeMessage, ClubPositionUpdateMessage } from "../../game/net/protocol/Club";
import { LongValue } from "../../game/net/protocol/ExternalMessage";
import { ClubModule } from "./ClubModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class ClubSubscriber {
  private onUpdateClubInfo(data: any) {
    log.log(`--------仙盟通知--${data}--仙盟通知--------`);
    ClubModule.api.ownClub();
  }

  private onBargainOpen(data: ClubBargainMessage) {
    log.log(`--------仙盟砍价--${data}--仙盟砍价--------`);
    let clubmessage = ClubModule.data.clubMessage;
    clubmessage.bargain = data;
    ClubModule.data.clubMessage = clubmessage;
  }
  private onExitedNotice(data: ClubPopUpMessage) {
    ClubModule.data.clubFormMessage.popUpMessage = data;
    ClubModule.service.showExitedTips();
  }
  public register() {
    //订阅服务器消息
    ApiHandler.instance.subscribe(ClubLogMessage, ClubSubCmd.memberJoinedNotice_23_51, this.onUpdateClubInfo);
    ApiHandler.instance.subscribe(ClubPopUpMessage, ClubSubCmd.memberExitedNotice_23_52, this.onExitedNotice);
    ApiHandler.instance.subscribe(LongValue, ClubSubCmd.memberApplyNotice_23_54, this.onUpdateClubInfo);
    ApiHandler.instance.subscribe(ClubBossTrainMessage, ClubSubCmd.bossTrainNotice_23_55, this.onUpdateClubInfo);
    ApiHandler.instance.subscribe(CommLongMapMessage, ClubSubCmd.positionUpdate_23_56, this.onUpdateClubInfo);
    ApiHandler.instance.subscribe(ClubApplyMessage, ClubSubCmd.applyUpdate_23_57, this.onUpdateClubInfo);
    ApiHandler.instance.subscribe(ClubBargainMessage, ClubSubCmd.bargainNotice_23_58, this.onBargainOpen);
    ApiHandler.instance.subscribe(BargainRecordMessage, ClubSubCmd.bargainNotice_23_59, this.onUpdateClubInfo);
  }
  public unRegister() {
    //取消订阅服务器消息
    ApiHandler.instance.unSubscribe(ClubSubCmd.memberJoinedNotice_23_51, this.onUpdateClubInfo);
    ApiHandler.instance.unSubscribe(ClubSubCmd.memberExitedNotice_23_52, this.onExitedNotice);
    ApiHandler.instance.unSubscribe(ClubSubCmd.memberApplyNotice_23_54, this.onUpdateClubInfo);
    ApiHandler.instance.unSubscribe(ClubSubCmd.bossTrainNotice_23_55, this.onUpdateClubInfo);
    ApiHandler.instance.unSubscribe(ClubSubCmd.positionUpdate_23_56, this.onUpdateClubInfo);
    ApiHandler.instance.unSubscribe(ClubSubCmd.applyUpdate_23_57, this.onUpdateClubInfo);
    ApiHandler.instance.unSubscribe(ClubSubCmd.bargainNotice_23_58, this.onBargainOpen);
    ApiHandler.instance.unSubscribe(ClubSubCmd.bargainNotice_23_59, this.onUpdateClubInfo);
  }
}
