{"skeleton": {"hash": "2+ljBglJQsQxVkNV7C5TYwZOcXI", "spine": "3.8.75", "x": -1, "y": -1001, "width": 602, "height": 1002, "images": "./images/", "audio": "D:/spine/猫女"}, "bones": [{"name": "root"}, {"name": "st1", "parent": "root", "length": 90.44, "rotation": -25.71, "x": 293.08, "y": -547.02}, {"name": "st2", "parent": "st1", "length": 101.73, "rotation": 112.88, "x": 42.37, "y": 4.77}, {"name": "st3", "parent": "st2", "length": 66.16, "rotation": -41.55, "x": 115.89, "y": -21.46}, {"name": "boz1", "parent": "st3", "length": 50.87, "rotation": 62.1, "x": 70.43, "y": 1.94}, {"name": "t1", "parent": "boz1", "length": 94.92, "rotation": 18.25, "x": 48.28, "y": 1.52}, {"name": "t3", "parent": "t1", "length": 35.83, "rotation": 134.51, "x": 148.46, "y": -20.41}, {"name": "t4", "parent": "t1", "length": 45.55, "rotation": -38.73, "x": 102.33, "y": -74.26}, {"name": "t5", "parent": "t1", "length": 32.33, "rotation": 10.78, "x": 151.22, "y": 27.49}, {"name": "t6", "parent": "t1", "length": 18.02, "rotation": 56.82, "x": 109.42, "y": 55.11}, {"name": "t7", "parent": "t6", "length": 28.24, "rotation": 75.55, "x": 27.34, "y": 1.74}, {"name": "t8", "parent": "t7", "length": 29.96, "rotation": 66.8, "x": 36.56, "y": 5.3}, {"name": "t9", "parent": "t1", "length": 31.66, "rotation": -129.15, "x": 86.68, "y": -112.26}, {"name": "t10", "parent": "t9", "length": 31.81, "rotation": -36.22, "x": 39.67, "y": -1.75}, {"name": "t11", "parent": "t10", "length": 25.87, "rotation": -35.86, "x": 48.51, "y": -7.87}, {"name": "t12", "parent": "t11", "length": 29.1, "rotation": -48.67, "x": 37.92, "y": -7.71}, {"name": "t13", "parent": "t1", "length": 42.35, "rotation": -92.34, "x": 46.37, "y": 9.06}, {"name": "t14", "parent": "t1", "length": 26.57, "rotation": 43, "x": 50.49, "y": 43.12}, {"name": "qinz2", "parent": "st1", "length": 49.46, "rotation": -62.37, "x": 41.97, "y": -15.47}, {"name": "qunz1", "parent": "st1", "length": 265.94, "rotation": -177.41, "x": 84, "y": -59.11}, {"name": "qunz2", "parent": "qunz1", "length": 62.17, "rotation": 111.3, "x": 160.12, "y": 57.6}, {"name": "qunz3", "parent": "qunz2", "length": 53.21, "rotation": 8.25, "x": 70.03, "y": 2.23}, {"name": "qunz4", "parent": "qunz3", "length": 64.04, "rotation": 1.29, "x": 57.81, "y": -0.52}, {"name": "qunz5", "parent": "qunz4", "length": 45.89, "rotation": 3.92, "x": 75.7, "y": 2.42}, {"name": "qunz6", "parent": "qunz5", "length": 47.87, "rotation": 6.05, "x": 57.43, "y": 0.32}, {"name": "yt1", "parent": "qunz1", "length": 213.23, "rotation": 104.57, "x": 292.9, "y": 1.02}, {"name": "yt2", "parent": "yt1", "length": 76.31, "rotation": -5.48, "x": 216.6, "y": -0.16}, {"name": "yt3", "parent": "yt2", "length": 30.47, "rotation": 1.51, "x": 81.12, "y": -0.16}, {"name": "zt1", "parent": "qunz1", "length": 241.12, "rotation": 127.08, "x": 290.01, "y": -17.47}, {"name": "zt2", "parent": "zt1", "length": 95.72, "rotation": -0.79, "x": 248.33, "y": -1.11}, {"name": "zt3", "parent": "zt2", "length": 21.68, "rotation": 24.4, "x": 100.07, "y": -1.7}, {"name": "st4", "parent": "st3", "length": 75.5, "rotation": -62.58, "x": 50.18, "y": -16.01}, {"name": "st5", "parent": "st3", "length": 48.44, "rotation": 138.01, "x": 41.42, "y": 14.81}, {"name": "ys1", "parent": "st5", "length": 128.36, "rotation": 66.3, "x": 55.32, "y": 2.02}, {"name": "ys2", "parent": "ys1", "length": 151.52, "rotation": -116.56, "x": 129.83, "y": -4.02}, {"name": "ys4", "parent": "ys2", "length": 41.62, "rotation": 26.87, "x": 158.33, "y": -0.82}, {"name": "ys5", "parent": "ys4", "length": 18.85, "rotation": -15.99, "x": 41.61, "y": -16.26}, {"name": "ys6", "parent": "ys5", "length": 14.27, "rotation": -23.28, "x": 21.42, "y": -0.42}, {"name": "ys7", "parent": "ys4", "length": 27.55, "rotation": 19.37, "x": 48.12, "y": 1.43}, {"name": "ys8", "parent": "ys7", "length": 14.03, "rotation": 8.96, "x": 31.33, "y": 2.5}, {"name": "zs1", "parent": "st4", "length": 166.32, "rotation": -77.41, "x": 75.6, "y": -8.29}, {"name": "zs2", "parent": "zs1", "length": 106.55, "rotation": -8.59, "x": 181.62, "y": 5.67}, {"name": "zs4", "parent": "zs2", "length": 65.99, "rotation": 64.14, "x": 134.47, "y": -8.15}, {"name": "zs5", "parent": "zs4", "length": 30.42, "rotation": -75.26, "x": 74.59, "y": -5.98}, {"name": "zs6", "parent": "zs4", "length": 21.42, "rotation": -0.58, "x": 17.49, "y": -17.03}, {"name": "zuiz1", "parent": "qinz2", "length": 47.08, "rotation": -1.7, "x": 26.46, "y": 42.97}, {"name": "zuiz2", "parent": "zuiz1", "length": 28.97, "x": 47.08}, {"name": "zuiz3", "parent": "zuiz2", "length": 18.11, "x": 28.97}, {"name": "zuiz4", "parent": "zuiz3", "length": 10.86, "x": 18.11}, {"name": "zuiz5", "parent": "zuiz4", "length": 7.24, "x": 10.86}, {"name": "zuiz6", "parent": "zuiz5", "length": 3.62, "x": 7.24}, {"name": "zuiz7", "parent": "zs4", "length": 29.7, "rotation": -31.25, "x": 54.03, "y": 19.57}, {"name": "zuiz8", "parent": "zuiz7", "length": 23.03, "rotation": -19.93, "x": 31.22, "y": -0.92}, {"name": "zuiz9", "parent": "zuiz8", "length": 30.02, "rotation": -1.33, "x": 25.13}, {"name": "zuiz10", "parent": "zuiz9", "length": 22.35, "rotation": -0.46, "x": 34.93, "y": -0.93}, {"name": "zuiz11", "parent": "zuiz10", "length": 18.23, "rotation": -3.7, "x": 23.03, "y": 0.37}, {"name": "t15", "parent": "t4", "length": 27.33, "x": 45.55}, {"name": "t16", "parent": "t15", "length": 18.22, "x": 27.33}, {"name": "t17", "parent": "t16", "length": 9.11, "x": 18.22}, {"name": "t18", "parent": "t5", "length": 19.4, "x": 32.33}, {"name": "t19", "parent": "t18", "length": 12.93, "x": 19.4}, {"name": "t20", "parent": "t19", "length": 6.47, "x": 12.93}, {"name": "t21", "parent": "t3", "length": 37.2, "rotation": 20.66, "x": 19.71, "y": 14.29}, {"name": "t22", "parent": "t3", "length": 23.89, "x": 35.83}, {"name": "t23", "parent": "t22", "length": 11.94, "x": 23.89}, {"name": "qinz3", "parent": "qinz2", "length": 29.67, "x": 49.46}, {"name": "qinz4", "parent": "qinz3", "length": 19.78, "x": 29.67}, {"name": "qinz5", "parent": "qinz4", "length": 9.89, "x": 19.78}, {"name": "ys3", "parent": "ys2", "length": 14.5, "rotation": 125.33, "x": 118.31, "y": 26.15}, {"name": "ys9", "parent": "ys3", "length": 16.73, "rotation": -0.95, "x": 20.78, "y": 1.25}, {"name": "ys10", "parent": "ys9", "length": 14.31, "rotation": -14.3, "x": 21.05, "y": 0.21}, {"name": "zs7", "parent": "zs1", "length": 37.74, "rotation": 7.19, "x": 319.99, "y": 112.78}, {"name": "zs8", "parent": "zs7", "length": 37.82, "rotation": -7.5, "x": 42.77, "y": 1.61}, {"name": "zs9", "parent": "zs8", "length": 37.41, "rotation": -2.91, "x": 40.9, "y": 0.25}, {"name": "zs10", "parent": "zs9", "length": 40.03, "rotation": -1.29, "x": 43.29, "y": 2.65}, {"name": "zs11", "parent": "zs10", "length": 31.18, "rotation": -4.88, "x": 44.08, "y": -1.87}, {"name": "st6", "parent": "st3", "length": 26.99, "rotation": -50.75, "x": 95.77, "y": -34.27}, {"name": "st7", "parent": "st6", "length": 14.22, "rotation": 8.79, "x": 32.1, "y": 0.46}, {"name": "st8", "parent": "st7", "length": 15.17, "rotation": 9, "x": 22.17, "y": 1.31}, {"name": "weib", "parent": "qinz2", "length": 28.92, "rotation": 18.07, "x": 37.67, "y": 52.27}, {"name": "weib2", "parent": "weib", "length": 28.61, "rotation": -22.1, "x": 34.5, "y": -0.15}, {"name": "weib3", "parent": "weib2", "length": 19.18, "rotation": -10.64, "x": 32.88, "y": -0.9}, {"name": "st9", "parent": "st2", "length": 59, "rotation": 96.77, "x": 98.35, "y": -19.07}], "slots": [{"name": "bg", "bone": "root", "attachment": "bg"}, {"name": "ys1", "bone": "ys1", "attachment": "ys1"}, {"name": "ys2", "bone": "ys4", "attachment": "ys2"}, {"name": "qunz1", "bone": "qunz1", "attachment": "qunz1"}, {"name": "weib", "bone": "weib", "attachment": "weib"}, {"name": "qinz2", "bone": "qinz2", "attachment": "qinz2"}, {"name": "st1", "bone": "st1", "attachment": "st1"}, {"name": "boz1", "bone": "boz1", "attachment": "boz1"}, {"name": "yt1", "bone": "yt1", "attachment": "yt1"}, {"name": "zuiz1", "bone": "zuiz1", "attachment": "zuiz1"}, {"name": "zt1", "bone": "zt1", "attachment": "zt1"}, {"name": "zs1", "bone": "zs7", "attachment": "zs1"}, {"name": "zuiz2", "bone": "zuiz7", "attachment": "zuiz2"}, {"name": "zs2", "bone": "zs4", "attachment": "zs2"}, {"name": "t3", "bone": "t9", "attachment": "t3"}, {"name": "t1", "bone": "t1", "attachment": "t1"}, {"name": "t2", "bone": "t3", "attachment": "t2"}, {"name": "t4", "bone": "t4", "attachment": "t4"}, {"name": "t5", "bone": "t5", "attachment": "t5"}, {"name": "t6", "bone": "t6", "attachment": "t6"}, {"name": "t7", "bone": "t14", "attachment": "t7"}, {"name": "t8", "bone": "t13", "attachment": "t8"}], "skins": [{"name": "default", "attachments": {"t4": {"t4": {"type": "mesh", "uvs": [0.51717, 0.07444, 0.58159, 0.12342, 0.63089, 0.16089, 0.68266, 0.20025, 0.72791, 0.23465, 0.76719, 0.29907, 0.80177, 0.35577, 0.82927, 0.43064, 0.84829, 0.48239, 0.85577, 0.56616, 0.86091, 0.62372, 0.88739, 0.68126, 0.91395, 0.73897, 0.8616, 0.78956, 0.81024, 0.8392, 0.684, 0.90906, 0.60457, 0.96721, 0.50342, 0.97575, 0.34283, 0.91865, 0.17227, 0.85801, 0.1021, 0.83306, 0.01795, 0.76583, 0.01198, 0.70199, 0.0061, 0.63918, 0.04532, 0.56606, 0.08877, 0.48507, 0.0986, 0.46674, 0.13653, 0.39602, 0.18921, 0.29783, 0.24513, 0.19357, 0.29939, 0.09241, 0.35839, 0.01819, 0.42454, 0.00402, 0.42336, 0.09721, 0.43027, 0.17739, 0.41644, 0.28812, 0.58923, 0.26903, 0.42336, 0.39121, 0.64452, 0.37339, 0.40032, 0.47394, 0.67447, 0.45867, 0.36576, 0.57067, 0.67908, 0.56049, 0.3289, 0.65339, 0.67677, 0.65339, 0.34042, 0.72594, 0.6929, 0.72594, 0.33351, 0.79721, 0.66526, 0.80485, 0.34272, 0.86594, 0.63991, 0.86212, 0.5109, 0.91534], "triangles": [15, 50, 14, 16, 51, 15, 18, 49, 51, 19, 47, 49, 19, 20, 47, 20, 21, 47, 19, 49, 18, 17, 18, 51, 17, 51, 16, 34, 30, 33, 1, 33, 0, 1, 34, 33, 30, 31, 33, 33, 32, 0, 33, 31, 32, 38, 35, 36, 36, 4, 5, 28, 29, 35, 35, 34, 36, 35, 29, 34, 36, 34, 2, 36, 3, 4, 36, 2, 3, 2, 34, 1, 34, 29, 30, 39, 25, 26, 39, 40, 42, 40, 7, 8, 40, 37, 38, 40, 39, 37, 26, 27, 39, 39, 27, 37, 7, 38, 6, 7, 40, 38, 27, 28, 37, 28, 35, 37, 38, 37, 35, 38, 5, 6, 38, 36, 5, 51, 50, 15, 51, 49, 50, 49, 47, 50, 50, 48, 14, 50, 47, 48, 14, 48, 13, 47, 45, 48, 48, 46, 13, 48, 45, 46, 47, 21, 45, 13, 46, 12, 21, 22, 45, 46, 11, 12, 22, 43, 45, 45, 44, 46, 45, 43, 44, 46, 44, 11, 22, 23, 43, 44, 10, 11, 23, 24, 43, 43, 41, 44, 43, 24, 41, 10, 42, 9, 10, 44, 42, 44, 41, 42, 24, 25, 41, 41, 25, 39, 41, 39, 42, 42, 8, 9, 42, 40, 8], "vertices": [2, 57, 31.59, -4.6, 0.00172, 58, 13.37, -4.6, 0.99828, 3, 56, 52.17, -10.03, 0.00162, 57, 24.84, -10.03, 0.25441, 58, 6.62, -10.03, 0.74397, 3, 56, 47, -14.17, 0.03424, 57, 19.67, -14.17, 0.63078, 58, 1.45, -14.17, 0.33498, 3, 56, 41.58, -18.53, 0.1456, 57, 14.25, -18.53, 0.75872, 58, -3.97, -18.53, 0.09568, 4, 7, 82.39, -22.34, 0.00134, 56, 36.84, -22.34, 0.28672, 57, 9.51, -22.34, 0.68848, 58, -8.71, -22.34, 0.02346, 4, 7, 73.34, -25.88, 0.02472, 56, 27.79, -25.88, 0.54459, 57, 0.46, -25.88, 0.43055, 58, -17.76, -25.88, 0.00015, 3, 7, 65.37, -29, 0.09413, 56, 19.82, -29, 0.68181, 57, -7.51, -29, 0.22406, 3, 7, 54.78, -31.69, 0.28173, 56, 9.23, -31.69, 0.64517, 57, -18.1, -31.69, 0.07311, 3, 7, 47.46, -33.54, 0.45408, 56, 1.91, -33.54, 0.51932, 57, -25.42, -33.54, 0.02659, 3, 7, 35.52, -34.71, 0.72265, 56, -10.03, -34.71, 0.27536, 57, -37.36, -34.71, 0.002, 3, 7, 27.32, -35.51, 0.86234, 56, -18.23, -35.51, 0.13766, 57, -45.56, -35.51, 0, 2, 7, 19.2, -38, 0.9436, 56, -26.35, -38, 0.0564, 2, 7, 11.06, -40.49, 0.97575, 56, -34.49, -40.49, 0.02425, 2, 7, 3.64, -36.71, 0.99027, 56, -41.91, -36.71, 0.00973, 3, 7, -3.65, -33, 0.01597, 56, -49.2, -33, 3e-05, 5, 78.83, -97.72, 0.984, 1, 5, 76.6, -83.78, 1, 1, 5, 73.56, -73.82, 1, 2, 7, -24.32, -9.73, 0.008, 5, 77.26, -66.63, 0.992, 1, 5, 91.32, -61.16, 1, 1, 5, 106.26, -55.35, 1, 1, 5, 112.4, -52.96, 1, 1, 5, 124.09, -53.22, 1, 3, 7, 12.91, 30.93, 0.73869, 56, -32.64, 30.93, 0.00531, 5, 131.75, -58.2, 0.256, 2, 7, 21.86, 31.83, 0.96362, 56, -23.69, 31.83, 0.03638, 2, 7, 32.45, 29.24, 0.8563, 56, -13.1, 29.24, 0.1437, 3, 7, 44.19, 26.37, 0.55996, 56, -1.36, 26.37, 0.43985, 57, -28.69, 26.37, 0.00019, 3, 7, 46.84, 25.72, 0.47091, 56, 1.29, 25.72, 0.52756, 57, -26.04, 25.72, 0.00153, 3, 7, 57.09, 23.21, 0.15546, 56, 11.54, 23.21, 0.80251, 57, -15.79, 23.21, 0.04203, 4, 7, 71.31, 19.73, 0.00362, 56, 25.76, 19.73, 0.61434, 57, -1.57, 19.73, 0.38195, 58, -19.79, 19.73, 9e-05, 3, 56, 40.87, 16.04, 0.06914, 57, 13.54, 16.04, 0.75809, 58, -4.68, 16.04, 0.17278, 2, 57, 28.19, 12.46, 0.10295, 58, 9.97, 12.46, 0.89705, 1, 58, 20.8, 8.31, 1, 1, 58, 23.07, 3.19, 1, 2, 57, 27.98, 2.64, 0.00514, 58, 9.76, 2.64, 0.99486, 2, 57, 16.55, 1.54, 0.93897, 58, -1.67, 1.54, 0.06103, 2, 56, 28.01, 1.87, 0.29517, 57, 0.68, 1.87, 0.70483, 4, 7, 76.95, -11.63, 0.00048, 56, 31.4, -11.63, 0.30437, 57, 4.07, -11.63, 0.68991, 58, -14.15, -11.63, 0.00525, 1, 56, 13.32, 0.61, 1, 3, 7, 62.25, -16.71, 0.06574, 56, 16.7, -16.71, 0.77765, 57, -10.63, -16.71, 0.15661, 2, 7, 46.96, 1.86, 0.12932, 56, 1.41, 1.86, 0.87068, 3, 7, 50.19, -19.66, 0.32605, 56, 4.64, -19.66, 0.64122, 57, -22.69, -19.66, 0.03273, 2, 7, 33.01, 3.92, 0.99419, 56, -12.54, 3.92, 0.00581, 3, 7, 35.66, -20.73, 0.74902, 56, -9.89, -20.73, 0.25, 57, -37.22, -20.73, 0.00098, 2, 7, 21.06, 6.26, 0.99899, 56, -24.49, 6.26, 0.00101, 2, 7, 22.38, -21.19, 0.93504, 56, -23.17, -21.19, 0.06496, 1, 7, 10.74, 4.85, 1, 2, 7, 12.08, -22.96, 0.98216, 56, -33.47, -22.96, 0.01784, 1, 7, 0.53, 4.91, 1, 2, 7, 0.7, -21.32, 0.99894, 56, -44.84, -21.32, 0.00106, 2, 7, -9.25, 3.71, 0.216, 5, 97.43, -65.58, 0.784, 2, 7, -7.57, -19.72, 0.848, 5, 84.08, -84.9, 0.152, 2, 7, -15.66, -9.9, 0.92, 5, 83.91, -72.19, 0.08], "hull": 33, "edges": [28, 30, 30, 32, 32, 34, 40, 42, 60, 62, 62, 64, 0, 64, 58, 60, 56, 58, 54, 56, 46, 48, 48, 50, 16, 18, 18, 20, 12, 14, 14, 16, 8, 10, 10, 12, 4, 6, 6, 8, 0, 2, 2, 4, 20, 22, 22, 24, 24, 26, 26, 28, 42, 44, 44, 46, 60, 66, 66, 0, 58, 68, 68, 2, 56, 70, 70, 72, 72, 8, 54, 74, 74, 76, 76, 12, 50, 52, 52, 54, 52, 78, 78, 80, 80, 14, 48, 82, 82, 84, 84, 18, 46, 86, 86, 88, 88, 20, 44, 90, 90, 92, 92, 24, 42, 94, 94, 96, 96, 26, 38, 40, 38, 98, 98, 100, 100, 28, 34, 36, 36, 38, 36, 102, 102, 30], "width": 79, "height": 143}}, "qunz1": {"qunz1": {"type": "mesh", "uvs": [0.08622, 0.0147, 0.16333, 0.00714, 0.23611, 0, 0.33446, 0.03258, 0.38206, 0.06177, 0.45059, 0.10379, 0.51233, 0.14165, 0.57514, 0.18016, 0.64907, 0.2255, 0.72004, 0.22924, 0.77579, 0.25636, 0.86865, 0.30154, 0.94973, 0.34099, 0.99758, 0.39291, 0.99737, 0.45684, 0.99718, 0.51669, 0.99691, 0.60124, 0.96252, 0.63897, 0.93538, 0.66875, 0.84971, 0.66804, 0.77465, 0.66741, 0.74541, 0.67787, 0.70512, 0.73969, 0.67509, 0.78576, 0.64418, 0.83318, 0.61628, 0.87599, 0.58373, 0.92593, 0.55604, 0.96841, 0.53545, 1, 0.50592, 1, 0.4636, 0.95611, 0.41915, 0.91001, 0.3724, 0.86153, 0.32718, 0.81463, 0.28462, 0.77049, 0.2471, 0.73158, 0.21152, 0.69469, 0.17694, 0.65882, 0.15154, 0.63248, 0.13139, 0.5759, 0.11258, 0.52307, 0.09543, 0.47492, 0.07868, 0.42788, 0.06119, 0.37876, 0.04265, 0.3267, 0.02648, 0.28128, 0.01325, 0.24413, 0, 0.20692, 0, 0.17401, 0.00255, 0.13411, 0.0056, 0.08626, 0.00968, 0.0222, 0.14163, 0.05855, 0.16472, 0.11153, 0.2014, 0.17139, 0.24079, 0.23517, 0.27475, 0.30581, 0.31822, 0.36174, 0.35082, 0.40393, 0.37527, 0.46477, 0.42146, 0.5207, 0.44455, 0.57368, 0.46221, 0.61784, 0.50568, 0.65414, 0.56952, 0.67769, 0.64831, 0.66395, 0.73117, 0.65218, 0.22033, 0.20204, 0.17966, 0.20867, 0.16608, 0.28325, 0.17151, 0.35978, 0.17016, 0.4265, 0.17695, 0.50598, 0.20004, 0.58644, 0.22042, 0.64237, 0.26117, 0.71498, 0.15929, 0.57004, 0.25166, 0.07748, 0.28562, 0.14125, 0.33588, 0.2227, 0.39429, 0.30119, 0.44727, 0.38165, 0.48394, 0.44935, 0.53828, 0.52, 0.60484, 0.57495, 0.71615, 0.5814, 0.82346, 0.57355, 0.90632, 0.55196, 0.40126, 0.14993, 0.44337, 0.22254, 0.48005, 0.29123, 0.54661, 0.38738, 0.61317, 0.45313, 0.70962, 0.50022, 0.84681, 0.48649, 0.92288, 0.45116, 0.55204, 0.2196, 0.60095, 0.29123, 0.66207, 0.35795, 0.74358, 0.4129, 0.85904, 0.39916, 0.70962, 0.30594, 0.79519, 0.33244, 0.06438, 0.08254, 0.08204, 0.13846, 0.08884, 0.19832, 0.11057, 0.24247, 0.0997, 0.31214, 0.11736, 0.37003, 0.12823, 0.42806, 0.21909, 0.30091, 0.2449, 0.36959, 0.26392, 0.43337, 0.31554, 0.51187, 0.34814, 0.58742, 0.35493, 0.64924, 0.37666, 0.71596, 0.48533, 0.71792, 0.59129, 0.71989, 0.38889, 0.77876, 0.49484, 0.78661, 0.58585, 0.78955, 0.43915, 0.84292, 0.55869, 0.84783, 0.50707, 0.91161], "triangles": [100, 11, 12, 102, 10, 11, 28, 29, 27, 29, 30, 27, 30, 124, 27, 27, 124, 26, 30, 31, 124, 25, 26, 123, 31, 122, 124, 26, 124, 123, 124, 122, 123, 31, 32, 122, 25, 123, 24, 32, 33, 122, 122, 120, 123, 123, 121, 24, 123, 120, 121, 33, 119, 122, 122, 119, 120, 24, 121, 23, 33, 34, 119, 23, 121, 118, 119, 117, 120, 121, 120, 118, 120, 117, 118, 23, 118, 22, 118, 65, 22, 118, 64, 65, 34, 116, 119, 119, 116, 117, 35, 75, 34, 34, 75, 116, 35, 36, 75, 117, 64, 118, 117, 63, 64, 117, 116, 63, 75, 115, 116, 116, 115, 63, 36, 74, 75, 75, 74, 115, 36, 37, 74, 64, 63, 84, 74, 37, 73, 115, 62, 63, 63, 62, 84, 74, 114, 115, 62, 115, 114, 37, 38, 73, 74, 73, 114, 38, 76, 73, 38, 39, 76, 62, 114, 61, 84, 62, 61, 73, 113, 114, 114, 60, 61, 114, 113, 60, 76, 72, 73, 73, 72, 113, 39, 40, 76, 61, 60, 83, 113, 59, 60, 76, 40, 72, 71, 41, 109, 72, 41, 71, 72, 40, 41, 72, 112, 113, 113, 112, 59, 72, 71, 112, 41, 42, 109, 112, 58, 59, 59, 58, 81, 71, 111, 112, 112, 57, 58, 112, 111, 57, 71, 109, 108, 109, 42, 108, 42, 43, 108, 71, 70, 111, 71, 108, 70, 58, 57, 81, 43, 44, 108, 44, 107, 108, 108, 107, 70, 70, 110, 111, 111, 56, 57, 111, 110, 56, 57, 56, 80, 107, 69, 70, 70, 69, 110, 110, 55, 56, 60, 59, 82, 56, 79, 80, 60, 82, 83, 101, 9, 10, 83, 82, 92, 84, 83, 92, 65, 64, 84, 65, 84, 85, 21, 65, 66, 21, 66, 20, 18, 19, 17, 20, 86, 19, 17, 86, 87, 17, 19, 86, 20, 66, 86, 65, 85, 66, 66, 85, 86, 17, 87, 16, 16, 87, 15, 84, 93, 85, 85, 93, 86, 84, 92, 93, 86, 94, 87, 86, 93, 94, 87, 95, 15, 87, 94, 95, 15, 95, 14, 93, 99, 94, 93, 92, 99, 94, 100, 95, 94, 99, 100, 14, 95, 13, 82, 91, 92, 92, 98, 99, 92, 91, 98, 95, 100, 13, 99, 98, 102, 99, 102, 100, 102, 98, 101, 100, 12, 13, 100, 102, 11, 91, 97, 98, 91, 90, 97, 98, 97, 101, 101, 10, 102, 44, 45, 107, 107, 106, 69, 107, 45, 106, 97, 8, 101, 101, 8, 9, 56, 55, 79, 80, 89, 90, 80, 79, 89, 110, 69, 55, 90, 96, 97, 90, 89, 96, 97, 96, 8, 69, 68, 55, 68, 67, 55, 69, 106, 68, 45, 46, 106, 46, 105, 106, 46, 47, 105, 106, 105, 68, 55, 67, 79, 96, 7, 8, 67, 78, 79, 79, 88, 89, 79, 78, 88, 89, 6, 96, 89, 88, 6, 96, 6, 7, 68, 54, 67, 68, 105, 54, 47, 48, 105, 67, 54, 78, 48, 104, 105, 105, 104, 54, 48, 49, 104, 104, 53, 54, 54, 53, 78, 4, 88, 3, 88, 5, 6, 88, 4, 5, 53, 77, 78, 77, 3, 78, 78, 3, 88, 49, 103, 104, 104, 103, 53, 49, 50, 103, 103, 52, 53, 53, 52, 77, 77, 52, 1, 50, 51, 103, 103, 0, 52, 103, 51, 0, 1, 2, 77, 77, 2, 3, 52, 0, 1, 21, 22, 65, 81, 90, 91, 82, 81, 91, 81, 80, 90, 84, 61, 83, 59, 81, 82, 57, 80, 81], "vertices": [1, 19, 316.14, -41.89, 1, 1, 19, 293.02, -55.7, 1, 1, 19, 271.21, -68.73, 1, 1, 19, 233.78, -67.75, 1, 1, 19, 213.14, -61.36, 1, 1, 19, 183.43, -52.16, 1, 1, 19, 156.67, -43.87, 1, 1, 19, 129.44, -35.44, 1, 1, 19, 97.38, -25.51, 1, 2, 19, 74.1, -33.51, 0.30585, 2, -17.91, 44.35, 0.69415, 2, 19, 51.25, -29.13, 0.23397, 2, -29.93, 24.45, 0.76603, 1, 2, -49.96, -8.71, 1, 1, 2, -67.45, -37.67, 1, 1, 19, -45, 0.88, 1, 1, 19, -56.97, 29.07, 1, 1, 19, -68.16, 55.46, 1, 1, 19, -83.98, 92.74, 1, 1, 19, -80.14, 114.04, 1, 1, 19, -77.11, 130.85, 1, 1, 19, -49.71, 142.17, 1, 1, 19, -25.71, 152.1, 1, 1, 19, -18.37, 160.68, 1, 5, 19, -17.18, 193.38, 0.17935, 21, 135.93, 95.13, 0.06919, 22, 80.25, 93.86, 0.22315, 23, 10.79, 90.91, 0.50886, 24, -36.83, 95, 0.01945, 5, 19, -16.29, 217.76, 0.05778, 21, 156.69, 82.34, 0.02998, 22, 100.73, 80.6, 0.13746, 23, 30.31, 76.29, 0.68534, 24, -18.96, 78.4, 0.08945, 5, 19, -15.37, 242.84, 0.02096, 21, 178.07, 69.17, 0.00693, 22, 121.8, 66.96, 0.03933, 23, 50.4, 61.23, 0.65288, 24, -0.57, 61.31, 0.27989, 5, 19, -14.55, 265.49, 0.00575, 21, 197.37, 57.29, 0.00061, 22, 140.83, 54.64, 0.00359, 23, 68.54, 47.64, 0.39268, 24, 16.04, 45.88, 0.59737, 3, 19, -13.59, 291.92, 0.00038, 23, 89.7, 31.79, 0.06664, 24, 35.41, 27.89, 0.93298, 2, 23, 107.7, 18.3, 0.00045, 24, 51.88, 12.58, 0.99955, 2, 23, 121.08, 8.27, 0, 24, 64.14, 1.19, 1, 2, 23, 119.02, -1.74, 0, 24, 61.03, -8.54, 1, 1, 24, 36.55, -16.11, 1, 2, 23, 70.75, -22.45, 0.11024, 24, 10.85, -24.05, 0.88976, 3, 22, 122.64, -28.05, 0.03396, 23, 44.74, -33.61, 0.77901, 24, -16.19, -32.41, 0.18702, 3, 22, 98.28, -40.54, 0.28025, 23, 19.59, -44.41, 0.71714, 24, -42.35, -40.49, 0.0026, 4, 20, 210.3, -29.07, 0.00038, 21, 134.33, -51.1, 0.00586, 22, 75.35, -52.3, 0.67113, 23, -4.09, -54.57, 0.32263, 4, 20, 192.08, -42.64, 0.00758, 21, 114.35, -61.92, 0.04513, 22, 55.14, -62.66, 0.85244, 23, -24.97, -63.52, 0.09485, 4, 20, 174.81, -55.51, 0.03292, 21, 95.42, -72.17, 0.13747, 22, 35.98, -72.49, 0.81182, 23, -44.76, -72.02, 0.01779, 4, 20, 158.02, -68.02, 0.08266, 21, 77, -82.14, 0.25397, 22, 17.35, -82.04, 0.66274, 23, -64, -80.27, 0.00063, 3, 20, 145.69, -77.2, 0.13233, 21, 63.48, -89.47, 0.32226, 22, 3.66, -89.05, 0.54541, 4, 19, 196.19, 199.19, 0.00158, 20, 118.82, -85.03, 0.28048, 21, 35.77, -93.36, 0.39276, 22, -24.13, -92.33, 0.32518, 4, 19, 212.12, 178.47, 0.01522, 20, 93.74, -92.35, 0.48447, 21, 9.9, -97, 0.34502, 22, -50.08, -95.38, 0.15529, 4, 19, 226.63, 159.59, 0.05334, 20, 70.88, -99.01, 0.65039, 21, -13.68, -100.32, 0.23184, 22, -73.73, -98.16, 0.06443, 4, 19, 240.81, 141.15, 0.12913, 20, 48.54, -105.52, 0.7255, 21, -36.72, -103.56, 0.12446, 22, -96.84, -100.88, 0.0209, 4, 19, 255.62, 121.88, 0.25887, 20, 25.22, -112.32, 0.68604, 21, -60.78, -106.94, 0.05131, 22, -120.96, -103.72, 0.00378, 4, 19, 271.31, 101.47, 0.44351, 20, 0.5, -119.53, 0.54224, 21, -86.28, -110.53, 0.01419, 22, -146.54, -106.73, 6e-05, 3, 19, 285, 83.66, 0.61851, 20, -21.07, -125.81, 0.37884, 21, -108.52, -113.66, 0.00265, 3, 19, 296.2, 69.09, 0.74805, 20, -38.71, -130.96, 0.25173, 21, -126.72, -116.22, 0.00022, 2, 19, 307.41, 54.5, 0.84597, 20, -56.38, -136.11, 0.15403, 2, 19, 313.6, 40, 0.90817, 20, -72.13, -136.61, 0.09183, 2, 19, 320.3, 22.08, 0.96318, 20, -91.26, -136.34, 0.03682, 2, 19, 328.33, 0.59, 0.99392, 20, -114.21, -136.02, 0.00608, 1, 19, 339.08, -28.19, 1, 1, 19, 290.26, -30.1, 1, 1, 19, 272.94, -9.9, 1, 2, 19, 250.01, 11.48, 0.96759, 20, -75.61, -67, 0.03241, 2, 19, 225.48, 34.22, 0.75129, 20, -45.51, -52.41, 0.24871, 3, 19, 201.38, 60.73, 0.31, 20, -12.06, -39.58, 0.68988, 21, -87.24, -29.61, 0.00012, 3, 19, 177.03, 79.46, 0.11627, 20, 14.23, -23.7, 0.88358, 21, -58.94, -17.66, 0.00015, 2, 19, 158.72, 93.62, 0.2696, 20, 34.07, -11.78, 0.7304, 3, 19, 139.5, 117.09, 0.18727, 20, 62.93, -2.39, 0.81269, 22, -65.56, -1.56, 5e-05, 4, 19, 114.28, 135.46, 0.05106, 21, 20.72, 9.33, 0.94687, 22, -36.86, 10.68, 0.00101, 23, -111.74, 15.93, 0.00106, 4, 19, 96.96, 155.66, 0.01794, 21, 46.83, 14.43, 0.8255, 22, -10.64, 15.19, 0.14984, 23, -85.27, 18.64, 0.00671, 4, 19, 83.04, 172.71, 0.02089, 21, 68.53, 18.14, 0.1689, 22, 11.14, 18.41, 0.78704, 23, -63.32, 20.36, 0.02317, 4, 19, 62.38, 182.8, 0.04175, 21, 87.49, 31.14, 0.09061, 22, 30.39, 30.98, 0.74952, 23, -43.26, 31.59, 0.11812, 5, 19, 37.63, 184.5, 0.0834, 21, 101.17, 51.83, 0.09616, 22, 44.53, 51.35, 0.50082, 23, -27.75, 50.95, 0.31939, 24, -79.37, 59.32, 0.00024, 5, 19, 15.15, 167.74, 0.5897, 21, 97.68, 79.65, 0.07553, 22, 41.67, 79.25, 0.17364, 23, -28.7, 78.98, 0.16089, 24, -77.36, 87.29, 0.00024, 1, 19, -9.01, 151.29, 1, 2, 19, 238.22, 22.41, 0.89224, 20, -61.14, -59.99, 0.10776, 2, 19, 249.91, 30.86, 0.851, 20, -57.52, -73.95, 0.149, 3, 19, 240.21, 65.56, 0.53035, 20, -21.67, -77.51, 0.46751, 21, -102.19, -65.77, 0.00214, 4, 19, 224.08, 98.53, 0.22931, 20, 14.91, -74.46, 0.73765, 21, -65.55, -68, 0.03162, 22, -124.85, -64.68, 0.00142, 4, 19, 211.96, 128.11, 0.08566, 20, 46.87, -73.91, 0.75936, 21, -33.84, -72.04, 0.13374, 22, -93.24, -69.43, 0.02124, 4, 19, 194.85, 162.2, 0.01457, 20, 84.85, -70.35, 0.50962, 21, 4.25, -73.96, 0.35145, 22, -55.2, -72.21, 0.12436, 4, 19, 172.37, 194.5, 0.00015, 20, 123.11, -61.13, 0.1952, 21, 43.44, -70.32, 0.40368, 22, -15.94, -69.47, 0.40097, 4, 20, 149.66, -53.23, 0.07578, 21, 70.85, -66.31, 0.25553, 22, 11.55, -66.07, 0.66818, 23, -68.68, -63.95, 0.0005, 4, 20, 183.98, -38.03, 0.01009, 21, 106.99, -56.19, 0.0563, 22, 47.91, -56.77, 0.86426, 23, -31.77, -57.15, 0.06935, 4, 19, 188.42, 192.82, 0.00153, 20, 115.71, -75.47, 0.27999, 21, 34.06, -83.46, 0.39958, 22, -25.61, -82.38, 0.3189, 1, 19, 251.68, -36.72, 1, 1, 19, 228.88, -13.24, 1, 2, 19, 197.57, 15.81, 0.87604, 20, -52.53, -19.71, 0.12396, 3, 19, 164.22, 42.45, 0.72433, 20, -15.6, 1.69, 0.27565, 21, -84.82, 11.74, 2e-05, 1, 19, 132.22, 70.7, 1, 5, 19, 107.82, 95.54, 0.99813, 20, 54.35, 34.95, 0.0009, 21, -10.83, 34.63, 0.00094, 22, -67.83, 36.69, 1e-05, 23, -140.85, 44, 1e-05, 5, 19, 77.24, 119.28, 0.80063, 20, 87.57, 54.82, 0.01406, 21, 24.9, 49.53, 0.15221, 22, -31.77, 50.78, 0.02635, 23, -103.91, 55.59, 0.00675, 5, 19, 45.72, 134.44, 0.94226, 20, 113.14, 78.68, 0.00064, 21, 53.63, 69.47, 0.02829, 22, -2.6, 70.07, 0.021, 23, -73.49, 72.84, 0.00781, 1, 19, 9.09, 122.15, 1, 1, 19, -23.58, 104.11, 1, 1, 19, -45.89, 83.35, 1, 1, 19, 190.45, -25.13, 1, 1, 19, 163.39, 1.14, 1, 1, 19, 138.8, 26.41, 1, 1, 19, 99.53, 59.72, 1, 1, 19, 65.99, 79.64, 1, 1, 19, 26.44, 87.28, 1, 1, 19, -14.63, 62.59, 1, 1, 19, -32.2, 36.69, 1, 1, 19, 129.37, -14.93, 1, 1, 19, 100.33, 9.98, 1, 1, 19, 68.33, 31.07, 1, 1, 19, 32.06, 44.2, 1, 1, 19, -2.1, 22.46, 1, 1, 19, 62.98, 1.7, 1, 1, 19, 30.77, 1.74, 1, 2, 19, 310.32, -9.04, 0.99698, 20, -116.64, -115.74, 0.00302, 2, 19, 294.18, 13.2, 0.96938, 20, -90.06, -108.78, 0.03062, 2, 19, 280.76, 38.64, 0.86155, 20, -61.48, -105.52, 0.13845, 3, 19, 265.54, 55.14, 0.72046, 20, -40.58, -97.33, 0.2793, 21, -123.74, -82.67, 0.00023, 3, 19, 255.89, 87.3, 0.45743, 20, -7.1, -100.02, 0.53226, 21, -91, -90.14, 0.01031, 4, 19, 239.38, 110.41, 0.2475, 20, 20.42, -93.03, 0.70228, 21, -62.76, -87.16, 0.04704, 22, -122.5, -83.9, 0.00318, 4, 19, 225.01, 134.49, 0.10694, 20, 48.08, -88.39, 0.73297, 21, -34.72, -86.53, 0.13626, 22, -94.45, -83.91, 0.02382, 3, 19, 220.02, 66.13, 0.40353, 20, -13.8, -58.91, 0.59464, 21, -91.73, -48.48, 0.00183, 4, 19, 198.88, 92.88, 0.1176, 20, 18.8, -48.93, 0.86307, 21, -58.04, -43.29, 0.01876, 22, -116.79, -40.15, 0.00057, 4, 19, 180.84, 118.4, 0.02315, 20, 49.13, -41.38, 0.85347, 21, -26.94, -40.17, 0.1135, 22, -85.63, -37.73, 0.00988, 4, 19, 149.64, 145.96, 4e-05, 20, 86.14, -22.33, 0.25701, 21, 12.42, -26.62, 0.70118, 22, -45.97, -25.07, 0.04177, 3, 20, 121.95, -9.9, 0.02079, 21, 49.64, -19.46, 0.61332, 22, -8.6, -18.75, 0.36588, 3, 20, 151.47, -6.61, 0.00474, 21, 79.33, -20.43, 0.03917, 22, 21.06, -20.4, 0.95609, 4, 20, 183.17, 1.93, 2e-05, 21, 111.93, -16.53, 0.00061, 22, 53.74, -17.23, 0.94617, 23, -23.25, -18.11, 0.05321, 4, 19, 56.85, 213.66, 0.01264, 21, 117.07, 20.73, 0.0074, 22, 59.72, 19.9, 0.637, 23, -14.75, 18.53, 0.34297, 5, 19, 22.77, 200.13, 0.07047, 21, 122.1, 57.05, 0.05253, 22, 65.57, 56.1, 0.32432, 23, -6.44, 54.25, 0.5427, 24, -57.83, 60.36, 0.00998, 2, 22, 84.12, -17.08, 0.27117, 23, 7.06, -20.03, 0.72883, 5, 19, 40.91, 242.62, 0.00474, 21, 150.13, 20.32, 0.00088, 22, 92.76, 18.75, 0.01281, 23, 18.14, 15.12, 0.97261, 24, -37.52, 18.86, 0.00897, 5, 19, 11.39, 231.55, 0.02702, 21, 155.05, 51.45, 0.01124, 22, 98.39, 49.76, 0.07969, 23, 25.87, 45.68, 0.77106, 24, -26.61, 48.43, 0.11099, 3, 22, 116.91, -3.97, 0.00439, 23, 40.67, -9.19, 0.95197, 24, -17.67, -7.7, 0.04364, 5, 19, 9.08, 260.92, 0.00562, 21, 181.74, 38.99, 0.00064, 22, 124.78, 36.7, 0.00351, 23, 51.31, 30.84, 0.54661, 24, -2.87, 30.99, 0.44362, 3, 19, 13.5, 296.02, 3e-05, 23, 77.63, 7.19, 0.00756, 24, 20.81, 4.7, 0.9924], "hull": 52, "edges": [4, 6, 16, 18, 24, 26, 40, 42, 56, 58, 94, 96, 100, 102, 0, 102, 0, 2, 2, 4, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 18, 20, 20, 22, 22, 24, 26, 28, 28, 30, 30, 32, 36, 38, 38, 40, 32, 34, 34, 36, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 92, 94, 90, 92, 88, 90, 86, 88, 84, 86, 82, 84, 80, 82, 76, 78, 78, 80, 96, 98, 98, 100, 0, 104, 104, 106, 106, 108, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 40, 108, 134, 134, 110, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 68, 144, 152, 152, 76, 146, 74, 2, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 30, 6, 176, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 26, 12, 192, 192, 194, 194, 196, 196, 198, 198, 200, 200, 24, 16, 202, 202, 204, 204, 22, 104, 206, 206, 98, 106, 208, 208, 96, 108, 210, 210, 92, 136, 212, 212, 90, 138, 214, 214, 88, 140, 216, 216, 86, 142, 218, 218, 84, 138, 220, 220, 112, 140, 222, 222, 114, 142, 224, 224, 116, 144, 226, 226, 120, 146, 228, 228, 122, 148, 230, 230, 126, 150, 232, 232, 234, 234, 236, 236, 44, 68, 238, 238, 240, 240, 242, 242, 46, 66, 244, 244, 246, 246, 48, 62, 248, 248, 52], "width": 346, "height": 479}}, "t6": {"t6": {"type": "mesh", "uvs": [0.33948, 0.03203, 0.44234, 0.06143, 0.54841, 0.36313, 0.59879, 0.44331, 0.75298, 0.79772, 0.82347, 0.84099, 0.91699, 0.84309, 0.9636, 0.91893, 0.97214, 0.94151, 0.8314, 1, 0.79428, 1, 0.65001, 0.91627, 0.63255, 0.93604, 0.38725, 0.8876, 0.18872, 0.78247, 0.12651, 0.72623, 0.0323, 0.64106, 0.01402, 0.56425, 0.01473, 0.4528, 0.06748, 0.3315, 0.13989, 0.16497, 0.2613, 0.00969, 0.37829, 0.24704, 0.47024, 0.46434, 0.5658, 0.64439, 0.67218, 0.82238, 0.79479, 0.91758, 0.25208, 0.26359, 0.30797, 0.51194, 0.45221, 0.70441, 0.5658, 0.85342, 0.15291, 0.365, 0.19258, 0.56575, 0.28273, 0.74994, 0.08259, 0.54713], "triangles": [9, 7, 8, 26, 5, 6, 26, 4, 5, 24, 3, 4, 23, 2, 3, 22, 1, 2, 7, 26, 6, 28, 32, 31, 24, 23, 3, 29, 28, 23, 29, 23, 24, 15, 34, 32, 16, 34, 15, 33, 32, 28, 33, 28, 29, 15, 32, 33, 14, 15, 33, 25, 24, 4, 30, 29, 24, 30, 24, 25, 13, 33, 29, 13, 29, 30, 14, 33, 13, 11, 30, 25, 25, 4, 26, 12, 30, 11, 13, 30, 12, 26, 11, 25, 10, 11, 26, 9, 26, 7, 10, 26, 9, 27, 20, 21, 31, 19, 20, 27, 31, 20, 31, 27, 28, 19, 34, 18, 32, 34, 31, 31, 34, 19, 17, 18, 34, 16, 17, 34, 22, 0, 1, 27, 21, 0, 27, 0, 22, 23, 22, 2, 28, 27, 22, 28, 22, 23], "vertices": [2, 9, 10.44, -22.86, 0.95702, 10, -28.04, 10.22, 0.04298, 2, 9, -2.96, -18.82, 0.096, 5, 123.56, 42.33, 0.904, 1, 5, 87.25, 51.38, 1, 4, 9, -21.45, 26.05, 0.02658, 10, 11.36, 53.3, 4e-05, 11, 34.2, 42.07, 0.03738, 5, 75.89, 51.41, 0.936, 1, 5, 30.95, 58.88, 1, 1, 5, 21.45, 54.27, 1, 1, 5, 14.01, 44.42, 1, 1, 5, 3.34, 44.56, 1, 1, 5, 0.57, 45.18, 1, 1, 11, 95.98, 7.08, 1, 2, 9, -44.1, 91.25, 3e-05, 11, 91.96, 4.28, 0.99997, 2, 9, -25.55, 80.7, 0.00502, 11, 70.83, 1.3, 0.99498, 2, 9, -23.14, 82.86, 0.0044, 11, 70.24, -1.88, 0.9956, 1, 11, 40.48, -15.82, 1, 2, 10, 60.5, 8.17, 0.00012, 11, 12.07, -20.87, 0.99988, 2, 10, 55.82, -1.18, 0.05081, 11, 1.63, -20.26, 0.94919, 2, 10, 48.74, -15.33, 0.39856, 11, -14.17, -19.33, 0.60144, 2, 10, 40.58, -19.48, 0.632, 11, -21.2, -13.46, 0.368, 2, 10, 28.01, -21.98, 0.91987, 11, -28.44, -2.89, 0.08013, 1, 10, 12.94, -17.98, 1, 2, 9, 37.5, -8.87, 0.12041, 10, -7.75, -12.49, 0.87959, 2, 9, 20.62, -25.93, 0.85546, 10, -28.47, -0.4, 0.14454, 3, 9, 6.53, 2.08, 0.98547, 10, -4.86, 20.24, 0.00687, 11, -2.59, 43.96, 0.00766, 3, 9, -4.38, 27.64, 0.43919, 10, 17.16, 37.17, 0.04231, 11, 21.65, 30.39, 0.5185, 3, 9, -15.97, 48.93, 0.12685, 10, 34.89, 53.71, 9e-05, 11, 43.84, 20.6, 0.87307, 2, 9, -29, 70.06, 0.01735, 11, 67.06, 11.83, 0.98265, 2, 9, -44.63, 81.78, 0.00078, 11, 86.6, 12.1, 0.99922, 3, 9, 23.26, 3.17, 0.3907, 10, 0.37, 4.31, 0.60798, 11, -15.17, 32.87, 0.00132, 3, 9, 17.28, 32.06, 0.12699, 10, 26.85, 17.3, 0.271, 11, 7.21, 13.65, 0.60201, 3, 9, -0.66, 55.1, 0.03362, 10, 44.68, 40.42, 0.00017, 11, 35.48, 6.37, 0.96621, 2, 9, -14.8, 72.94, 0.00854, 11, 57.58, 0.88, 0.99146, 1, 10, 14.44, -6.16, 1, 2, 10, 35.99, 3.64, 0.08812, 11, -1.76, -0.13, 0.91188, 1, 11, 20.11, -10.71, 1, 2, 10, 36.82, -11.02, 0.64726, 11, -14.9, -6.67, 0.35274], "hull": 22, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 32, 34, 34, 36, 40, 42, 14, 16, 2, 0, 0, 42, 0, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 14, 42, 54, 54, 56, 56, 58, 58, 60, 60, 24, 40, 62, 62, 64, 64, 66, 66, 26, 36, 38, 38, 40, 38, 68, 28, 30, 30, 32, 68, 30], "width": 132, "height": 115}}, "t5": {"t5": {"type": "mesh", "uvs": [0.02939, 0, 0.03741, 0, 0.07714, 0.02638, 0.19956, 0.09757, 0.24758, 0.11272, 0.32033, 0.12811, 0.45343, 0.1526, 0.57912, 0.17572, 0.70943, 0.1997, 0.80599, 0.21746, 0.93856, 0.25947, 0.93843, 0.33447, 0.79282, 0.52458, 0.72208, 0.71124, 0.70652, 0.81812, 0.69119, 0.92345, 0.64306, 0.98077, 0.50988, 0.99221, 0.37189, 0.94417, 0.32472, 0.85102, 0.28197, 0.7666, 0.2378, 0.67936, 0.1949, 0.59464, 0.15285, 0.46904, 0.11505, 0.35613, 0.08962, 0.28016, 0.06042, 0.19826, 0.03083, 0.11528, 0.00015, 0.02923, 0.16108, 0.34545, 0.26052, 0.45395, 0.37685, 0.5712, 0.51382, 0.63245, 0.63953, 0.68145, 0.12168, 0.16345, 0.18735, 0.2177, 0.29617, 0.2527, 0.42376, 0.2877, 0.54947, 0.3682, 0.67706, 0.42245, 0.29242, 0.5992, 0.37685, 0.6832, 0.42188, 0.76895, 0.50444, 0.8267, 0.58324, 0.87395], "triangles": [14, 43, 13, 13, 33, 12, 12, 39, 11, 15, 44, 14, 11, 9, 10, 25, 26, 34, 26, 27, 34, 3, 34, 2, 27, 28, 2, 34, 27, 2, 1, 28, 0, 28, 1, 2, 24, 25, 29, 29, 25, 35, 25, 34, 35, 35, 4, 36, 36, 4, 5, 35, 3, 4, 35, 34, 3, 22, 23, 30, 38, 30, 37, 23, 29, 30, 23, 24, 29, 30, 36, 37, 30, 29, 36, 7, 37, 6, 29, 35, 36, 37, 36, 6, 36, 5, 6, 17, 44, 16, 17, 18, 44, 16, 44, 15, 18, 43, 44, 18, 19, 43, 44, 43, 14, 19, 42, 43, 19, 20, 42, 43, 42, 33, 13, 43, 33, 42, 32, 33, 20, 41, 42, 42, 41, 32, 20, 21, 41, 21, 40, 41, 40, 31, 41, 41, 31, 32, 12, 33, 39, 21, 22, 40, 33, 32, 39, 32, 38, 39, 32, 31, 38, 31, 40, 30, 40, 22, 30, 38, 31, 30, 39, 9, 11, 39, 8, 9, 39, 38, 8, 38, 37, 7, 38, 7, 8], "vertices": [1, 61, 20.31, -3.77, 1, 1, 61, 19.75, -4.3, 1, 1, 61, 15.06, -4.95, 1, 3, 59, 33.66, -7.69, 0.00295, 60, 14.27, -7.69, 0.45759, 61, 1.34, -7.69, 0.53946, 3, 59, 29.19, -9.73, 0.07087, 60, 9.8, -9.73, 0.81406, 61, -3.13, -9.73, 0.11507, 4, 8, 55.28, -13.4, 0.00424, 59, 22.96, -13.4, 0.39996, 60, 3.56, -13.4, 0.59508, 61, -9.37, -13.4, 0.00072, 3, 8, 44.13, -20.39, 0.17107, 59, 11.81, -20.39, 0.73124, 60, -7.59, -20.39, 0.09769, 3, 8, 33.61, -27, 0.54997, 59, 1.28, -27, 0.44475, 60, -18.12, -27, 0.00528, 2, 8, 22.69, -33.84, 0.82956, 59, -9.64, -33.84, 0.17044, 2, 8, 14.6, -38.91, 0.93033, 59, -17.73, -38.91, 0.06967, 3, 8, 2.24, -44.54, 0.50966, 59, -30.08, -44.54, 0.01034, 5, 161.75, -15.84, 0.48, 1, 5, 155.45, -11.25, 1, 1, 5, 147.74, 11.79, 1, 1, 5, 136.06, 28.75, 1, 1, 5, 127.95, 36.5, 1, 1, 5, 119.96, 44.14, 1, 1, 8, -28.28, 29.74, 1, 2, 8, -19.68, 39.46, 0.99947, 59, -52.01, 39.46, 0.00053, 2, 8, -6.51, 44.99, 0.9909, 59, -38.84, 44.99, 0.0091, 2, 8, 3.46, 41.07, 0.96774, 59, -28.87, 41.07, 0.03226, 2, 8, 12.49, 37.51, 0.9111, 59, -19.83, 37.51, 0.0889, 3, 8, 21.83, 33.84, 0.79351, 59, -10.49, 33.84, 0.20643, 60, -29.89, 33.84, 6e-05, 3, 8, 30.9, 30.27, 0.6027, 59, -1.43, 30.27, 0.38942, 60, -20.82, 30.27, 0.00788, 3, 8, 42.82, 23.55, 0.22968, 59, 10.49, 23.55, 0.66715, 60, -8.9, 23.55, 0.10317, 4, 8, 53.54, 17.51, 0.02337, 59, 21.21, 17.51, 0.51417, 60, 1.81, 17.51, 0.45402, 61, -11.12, 17.51, 0.00844, 4, 8, 60.75, 13.45, 0.00024, 59, 28.42, 13.45, 0.16113, 60, 9.02, 13.45, 0.6967, 61, -3.91, 13.45, 0.14193, 3, 59, 36.32, 9.19, 0.00447, 60, 16.92, 9.19, 0.27234, 61, 3.99, 9.19, 0.72319, 1, 61, 12, 4.87, 1, 1, 61, 20.3, 0.39, 1, 4, 8, 51.05, 13.65, 0.02195, 59, 18.72, 13.65, 0.56184, 60, -0.68, 13.65, 0.41244, 61, -13.61, 13.65, 0.00377, 3, 8, 36.29, 15.26, 0.30773, 59, 3.96, 15.26, 0.65465, 60, -15.43, 15.26, 0.03762, 2, 8, 19.72, 16.41, 0.84804, 59, -12.61, 16.41, 0.15196, 2, 8, 5.67, 11.94, 0.98681, 59, -26.65, 11.94, 0.01319, 2, 8, -6.7, 7.3, 0.99995, 59, -39.03, 7.3, 5e-05, 2, 60, 15.08, 2.48, 0.14901, 61, 2.15, 2.48, 0.85099, 3, 59, 25.97, 2.22, 0.0014, 60, 6.57, 2.22, 0.99194, 61, -6.36, 2.22, 0.00666, 2, 59, 15.78, -2.36, 0.94914, 60, -3.61, -2.36, 0.05086, 3, 8, 36.6, -8.19, 0.18059, 59, 4.28, -8.19, 0.81041, 60, -15.12, -8.19, 0.009, 2, 8, 21.98, -10.44, 0.88957, 59, -10.34, -10.44, 0.11043, 2, 8, 9.1, -14.81, 0.97737, 59, -23.22, -14.81, 0.02263, 3, 8, 23.69, 24.14, 0.73073, 59, -8.64, 24.14, 0.26814, 60, -28.04, 24.14, 0.00113, 2, 8, 11.73, 24.89, 0.91008, 59, -20.59, 24.89, 0.08992, 2, 8, 2.44, 28.39, 0.96661, 59, -29.88, 28.39, 0.03339, 2, 8, -7.51, 27.28, 0.9932, 59, -39.83, 27.28, 0.0068, 2, 8, -16.44, 25.62, 0.99956, 59, -48.77, 25.62, 0.00044], "hull": 29, "edges": [0, 56, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 18, 20, 20, 22, 22, 24, 24, 26, 30, 32, 32, 34, 34, 36, 54, 56, 50, 52, 52, 54, 10, 12, 12, 14, 14, 16, 16, 18, 48, 50, 44, 46, 46, 48, 42, 44, 40, 42, 36, 38, 38, 40, 50, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 26, 52, 68, 68, 6, 50, 70, 70, 8, 58, 72, 72, 10, 60, 74, 74, 12, 62, 76, 76, 14, 64, 78, 78, 16, 46, 60, 44, 80, 80, 62, 42, 82, 82, 64, 40, 84, 84, 66, 38, 86, 86, 26, 36, 88, 26, 28, 28, 30, 88, 28], "width": 97, "height": 104}}, "boz1": {"boz1": {"type": "mesh", "uvs": [0.64494, 0.13052, 0.79927, 0.30525, 0.86571, 0.47925, 0.93104, 0.65033, 1, 0.83091, 1, 0.83828, 0.51909, 0.99949, 0.10551, 0.97606, 0.024, 0.93532, 0.02963, 0.80474, 0.03971, 0.5712, 0.11862, 0.37021, 0.21093, 0.24138, 0.2663, 0.1641, 0.53403, 0.00494, 0.28892, 0.85053, 0.59189, 0.79762, 0.29405, 0.6437, 0.59189, 0.58357, 0.4096, 0.41281, 0.60987, 0.36952, 0.46351, 0.25408], "triangles": [6, 16, 5, 16, 4, 5, 7, 8, 9, 7, 15, 6, 6, 15, 16, 15, 7, 9, 15, 17, 16, 15, 9, 17, 16, 3, 4, 9, 10, 17, 16, 18, 3, 16, 17, 18, 18, 2, 3, 10, 11, 17, 17, 19, 18, 17, 11, 19, 18, 20, 2, 18, 19, 20, 20, 1, 2, 11, 12, 19, 19, 21, 20, 19, 12, 21, 20, 0, 1, 20, 21, 0, 12, 13, 21, 21, 14, 0, 21, 13, 14], "vertices": [1, 4, 53.44, -27.95, 1, 1, 4, 36.82, -34.63, 1, 1, 4, 22.23, -35.13, 1, 1, 4, 7.89, -35.63, 1, 1, 3, 98.98, -21.39, 1, 2, 4, -7.81, -35.97, 0.008, 3, 98.57, -21.79, 0.992, 2, 4, -9.11, 1.81, 0.28, 3, 64.57, -5.27, 0.72, 2, 4, 1.96, 30.4, 0.256, 3, 44.49, 17.9, 0.744, 1, 3, 42.57, 24.46, 1, 1, 4, 16.56, 31.63, 1, 1, 4, 33.91, 25.3, 1, 1, 4, 47.26, 14.91, 1, 1, 4, 54.88, 5.3, 1, 1, 4, 59.44, -0.46, 1, 1, 4, 65.39, -23.15, 1, 1, 4, 7.28, 14.45, 1, 1, 4, 4.44, -8.18, 1, 1, 4, 22.73, 9.12, 1, 1, 4, 20.55, -13.32, 1, 1, 4, 37.5, -4.58, 1, 1, 4, 36.25, -19.74, 1, 1, 4, 48.23, -12.2, 1], "hull": 15, "edges": [8, 10, 10, 12, 12, 14, 14, 16, 20, 22, 26, 28, 2, 0, 0, 28, 16, 18, 18, 20, 18, 30, 30, 32, 6, 8, 32, 6, 20, 34, 34, 36, 2, 4, 4, 6, 36, 4, 22, 38, 38, 40, 40, 2, 22, 24, 24, 26, 24, 42, 42, 0], "width": 74, "height": 79}}, "bg": {"bg": {"x": 300, "y": -500, "width": 602, "height": 1002}}, "zuiz2": {"zuiz2": {"type": "mesh", "uvs": [0.12517, 0.00384, 0.28364, 0.00971, 0.52527, 0.04204, 0.65169, 0.08174, 0.78518, 0.12365, 0.89612, 0.19582, 0.89618, 0.25019, 0.89625, 0.31248, 0.89631, 0.37132, 0.89638, 0.43189, 0.89644, 0.48553, 0.8965, 0.53745, 0.89657, 0.60321, 0.89663, 0.66032, 0.89668, 0.70011, 0.89673, 0.7431, 0.7916, 0.75958, 0.79149, 0.80914, 0.79136, 0.86278, 0.79122, 0.92335, 0.79109, 0.97856, 0.68479, 0.99315, 0.41678, 0.99301, 0.36559, 0.96326, 0.36765, 0.9219, 0.44993, 0.88545, 0.4836, 0.84564, 0.52491, 0.79681, 0.5779, 0.74514, 0.63051, 0.69384, 0.57637, 0.66081, 0.68287, 0.61693, 0.68298, 0.57379, 0.68312, 0.52188, 0.68326, 0.46996, 0.68341, 0.41285, 0.68352, 0.36959, 0.68366, 0.31557, 0.63628, 0.26705, 0.5783, 0.20768, 0.43976, 0.14695, 0.35259, 0.10873, 0.01413, 0.01922], "triangles": [20, 21, 23, 21, 22, 23, 20, 23, 19, 23, 24, 19, 24, 25, 19, 19, 25, 18, 25, 26, 18, 18, 26, 17, 26, 27, 17, 17, 27, 16, 27, 28, 16, 16, 28, 15, 15, 29, 14, 15, 28, 29, 29, 13, 14, 29, 30, 13, 30, 31, 13, 31, 12, 13, 31, 32, 12, 32, 11, 12, 32, 33, 11, 33, 10, 11, 33, 34, 10, 34, 9, 10, 34, 35, 9, 35, 8, 9, 35, 36, 8, 36, 37, 8, 37, 7, 8, 37, 38, 7, 38, 6, 7, 38, 39, 6, 39, 5, 6, 39, 40, 5, 40, 4, 5, 40, 41, 4, 41, 3, 4, 2, 41, 42, 42, 0, 1, 41, 2, 3, 2, 42, 1], "vertices": [1, 51, -3.28, -1.55, 1, 1, 51, -1.48, 1, 1, 1, 51, 4.37, 3.76, 1, 1, 51, 10.45, 4.11, 1, 1, 51, 16.87, 4.48, 1, 1, 51, 27.16, 3, 1, 2, 51, 34.37, 0.39, 0.00565, 52, 2.51, 2.3, 0.99435, 1, 52, 11.3, 2.31, 1, 1, 52, 19.59, 2.31, 1, 1, 53, 2.95, 2.38, 1, 1, 53, 10.51, 2.55, 1, 1, 53, 17.83, 2.73, 1, 1, 53, 27.1, 2.94, 1, 2, 53, 35.15, 3.13, 0.16041, 54, 0.19, 4.07, 0.83959, 1, 54, 5.79, 4.24, 1, 1, 54, 11.85, 4.43, 1, 1, 54, 14.24, 2.51, 1, 2, 54, 21.22, 2.72, 0.77685, 55, -1.96, 2.23, 0.22315, 1, 55, 5.57, 2.95, 1, 1, 55, 14.07, 3.77, 1, 1, 55, 21.82, 4.51, 1, 1, 55, 24.06, 2.7, 1, 1, 55, 24.53, -2.37, 1, 1, 55, 20.45, -3.74, 1, 1, 55, 14.64, -4.26, 1, 1, 55, 9.37, -3.2, 1, 2, 54, 26.55, -2.96, 0.04307, 55, 3.72, -3.1, 0.95693, 1, 54, 19.64, -2.39, 1, 1, 54, 12.33, -1.61, 1, 1, 54, 5.07, -0.84, 1, 1, 54, 0.45, -2.01, 1, 1, 53, 29.13, -1.07, 1, 1, 53, 23.05, -1.21, 1, 1, 53, 15.73, -1.38, 1, 1, 53, 8.41, -1.55, 1, 2, 52, 25.45, -1.74, 0.09976, 53, 0.36, -1.73, 0.90024, 1, 52, 19.35, -1.74, 1, 1, 52, 11.73, -1.73, 1, 1, 52, 4.89, -2.63, 1, 2, 51, 26.67, -3.25, 0.83892, 52, -3.48, -3.74, 0.16108, 1, 51, 17.73, -2.8, 1, 1, 51, 12.1, -2.53, 1, 1, 51, -1.96, -4.27, 1], "hull": 43, "edges": [0, 84, 0, 2, 2, 4, 8, 10, 30, 32, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 58, 60, 82, 84, 60, 62, 4, 6, 6, 8, 78, 80, 80, 82, 10, 12, 74, 76, 76, 78, 12, 14, 72, 74, 14, 16, 70, 72, 16, 18, 68, 70, 18, 20, 66, 68, 20, 22, 62, 64, 64, 66, 22, 24, 24, 26, 26, 28, 28, 30, 54, 56, 56, 58, 32, 34, 50, 52, 52, 54, 34, 36, 36, 38, 38, 40], "width": 19, "height": 141}}, "qinz2": {"qinz2": {"type": "mesh", "uvs": [0.12139, 0.13971, 0.17693, 0.10538, 0.21696, 0.08063, 0.34552, 0.08979, 0.4838, 0.12583, 0.60836, 0.18278, 0.73338, 0.23994, 0.86691, 0.30099, 0.969, 0.37062, 0.97532, 0.45689, 0.98164, 0.54307, 0.98637, 0.60756, 0.99732, 0.75699, 0.96903, 0.799, 0.92991, 0.85706, 0.90119, 0.89969, 0.83346, 0.90102, 0.84835, 0.85093, 0.86334, 0.80051, 0.88081, 0.74174, 0.89384, 0.6979, 0.83343, 0.74408, 0.77257, 0.79061, 0.72869, 0.82416, 0.66702, 0.8724, 0.58549, 0.9362, 0.50586, 0.99851, 0.47104, 0.93398, 0.42793, 0.8541, 0.37987, 0.76507, 0.35606, 0.69751, 0.32322, 0.60433, 0.29891, 0.54097, 0.26158, 0.48704, 0.21905, 0.42561, 0.18225, 0.37245, 0.12738, 0.31627, 0.0755, 0.2732, 0.02301, 0.22961, 0.01989, 0.17567, 0.06338, 0.17557, 0.32771, 0.14429, 0.47354, 0.21189, 0.65617, 0.30407, 0.79928, 0.3901, 0.89468, 0.46692, 0.29181, 0.20881, 0.45945, 0.29792, 0.60664, 0.37781, 0.74702, 0.49253, 0.8724, 0.58881, 0.93237, 0.67279, 0.92556, 0.76247, 0.90511, 0.80549, 0.88603, 0.86285, 0.19913, 0.24016, 0.35586, 0.32209, 0.52077, 0.43374, 0.67887, 0.56586, 0.7661, 0.6857, 0.2332, 0.36511, 0.38994, 0.5126, 0.51123, 0.63141, 0.5971, 0.73896, 0.64889, 0.8168], "triangles": [41, 3, 4, 43, 5, 6, 44, 7, 8, 44, 6, 7, 42, 4, 5, 41, 2, 3, 26, 27, 25, 24, 25, 64, 64, 25, 28, 25, 27, 28, 24, 64, 23, 28, 63, 64, 23, 64, 22, 28, 29, 63, 64, 63, 22, 63, 59, 22, 22, 59, 21, 29, 62, 63, 63, 58, 59, 16, 54, 15, 16, 17, 54, 15, 54, 14, 14, 54, 53, 54, 17, 53, 14, 53, 13, 17, 18, 53, 53, 52, 13, 53, 18, 52, 18, 19, 52, 13, 52, 12, 29, 30, 62, 51, 12, 52, 52, 19, 20, 52, 20, 51, 51, 11, 12, 20, 21, 59, 63, 62, 58, 51, 20, 50, 30, 31, 62, 20, 59, 50, 59, 58, 50, 51, 50, 11, 31, 61, 62, 62, 57, 58, 62, 61, 57, 50, 10, 11, 31, 32, 61, 58, 49, 50, 50, 45, 10, 50, 49, 45, 45, 9, 10, 58, 57, 49, 49, 57, 48, 32, 33, 61, 57, 61, 56, 49, 44, 45, 49, 48, 44, 60, 61, 33, 60, 56, 61, 34, 60, 33, 9, 45, 8, 45, 44, 8, 56, 47, 57, 57, 47, 48, 34, 35, 60, 48, 43, 44, 43, 6, 44, 48, 47, 43, 43, 47, 42, 35, 36, 60, 60, 37, 55, 60, 55, 56, 55, 46, 56, 56, 46, 47, 60, 36, 37, 42, 5, 43, 47, 46, 42, 55, 37, 40, 37, 38, 40, 40, 0, 55, 55, 0, 46, 38, 39, 40, 46, 41, 42, 42, 41, 4, 0, 1, 46, 46, 1, 41, 1, 2, 41], "vertices": [1, 18, -11.76, -48.76, 1, 1, 18, -17.19, -41.69, 1, 1, 2, -0.49, 46.45, 1, 1, 2, -1.22, 30.45, 1, 1, 2, -6.31, 13.03, 1, 1, 2, -14.93, -2.86, 1, 1, 2, -23.58, -18.81, 1, 1, 2, -32.82, -35.84, 1, 1, 2, -43.67, -49.05, 1, 3, 18, 44.09, 55.31, 0.50737, 65, -5.37, 55.31, 0.41769, 66, -35.04, 55.31, 0.07494, 3, 18, 58.33, 55.62, 0.30587, 65, 8.87, 55.62, 0.53774, 66, -20.8, 55.62, 0.15639, 4, 18, 68.98, 55.85, 0.17884, 65, 19.53, 55.85, 0.58874, 66, -10.15, 55.85, 0.23237, 67, -29.93, 55.85, 4e-05, 4, 18, 93.67, 56.38, 0.03239, 65, 44.21, 56.38, 0.63359, 66, 14.54, 56.38, 0.334, 67, -5.24, 56.38, 2e-05, 3, 18, 100.48, 52.65, 0.0175, 65, 51.02, 52.65, 0.64096, 66, 21.35, 52.65, 0.34153, 3, 18, 109.89, 47.48, 0.00365, 65, 60.44, 47.48, 0.64828, 66, 30.76, 47.48, 0.34806, 3, 18, 116.8, 43.68, 0.00031, 65, 67.35, 43.68, 0.65004, 66, 37.67, 43.68, 0.34965, 2, 65, 67.29, 35.28, 0.65017, 66, 37.61, 35.28, 0.34983, 3, 18, 108.54, 37.4, 0.00203, 65, 59.09, 37.4, 0.64912, 66, 29.41, 37.4, 0.34885, 4, 18, 100.29, 39.54, 0.01043, 65, 50.83, 39.54, 0.64446, 66, 21.16, 39.54, 0.3451, 67, 1.38, 39.54, 1e-05, 4, 18, 90.67, 42.03, 0.03251, 65, 41.21, 42.03, 0.63004, 66, 11.54, 42.03, 0.33625, 67, -8.24, 42.03, 0.0012, 4, 18, 83.5, 43.89, 0.06662, 65, 34.04, 43.89, 0.5929, 66, 4.37, 43.89, 0.33019, 67, -15.42, 43.89, 0.01029, 4, 18, 90.86, 36.14, 0.03441, 65, 41.4, 36.14, 0.44504, 66, 11.73, 36.14, 0.44101, 67, -8.05, 36.14, 0.07953, 4, 18, 98.28, 28.34, 0.00779, 65, 48.82, 28.34, 0.25121, 66, 19.15, 28.34, 0.50322, 67, -0.63, 28.34, 0.23778, 4, 18, 103.63, 22.72, 0.00094, 65, 54.17, 22.72, 0.11873, 66, 24.5, 22.72, 0.42677, 67, 4.72, 22.72, 0.45355, 3, 65, 61.88, 14.81, 0.01724, 66, 32.2, 14.81, 0.13041, 67, 12.42, 14.81, 0.85235, 1, 67, 22.6, 4.36, 1, 1, 67, 32.54, -5.86, 1, 2, 66, 41.54, -9.82, 0.00014, 67, 21.76, -9.82, 0.99986, 3, 65, 57.87, -14.72, 7e-05, 66, 28.19, -14.72, 0.18563, 67, 8.41, -14.72, 0.8143, 3, 65, 42.98, -20.18, 0.1239, 66, 13.31, -20.18, 0.70459, 67, -6.47, -20.18, 0.17151, 4, 18, 81.2, -22.76, 0.00383, 65, 31.74, -22.76, 0.47654, 66, 2.07, -22.76, 0.50334, 67, -17.71, -22.76, 0.01629, 3, 18, 65.7, -26.31, 0.13263, 65, 16.24, -26.31, 0.77177, 66, -13.43, -26.31, 0.0956, 3, 18, 55.15, -28.98, 0.41328, 65, 5.69, -28.98, 0.5738, 66, -23.98, -28.98, 0.01292, 3, 18, 46.1, -33.31, 0.68878, 65, -3.36, -33.31, 0.31102, 66, -33.03, -33.31, 0.0002, 2, 18, 35.79, -38.24, 0.87504, 65, -13.66, -38.24, 0.12496, 2, 18, 26.87, -42.51, 0.95596, 65, -22.58, -42.51, 0.04404, 2, 18, 17.38, -49, 0.99188, 65, -32.07, -49, 0.00812, 2, 18, 10.06, -55.19, 0.99917, 65, -39.39, -55.19, 0.00083, 1, 18, 2.66, -61.45, 1, 1, 18, -6.25, -61.54, 1, 1, 18, -6.09, -56.15, 1, 1, 18, -10.15, -23.22, 1, 1, 18, 1.61, -5.52, 1, 3, 18, 17.56, 16.6, 0.97045, 65, -31.89, 16.6, 0.02942, 66, -61.57, 16.6, 0.00013, 3, 18, 32.35, 33.87, 0.73959, 65, -17.11, 33.87, 0.24053, 66, -46.79, 33.87, 0.01988, 3, 18, 45.41, 45.26, 0.50019, 65, -4.05, 45.26, 0.42528, 66, -33.72, 45.26, 0.07453, 2, 18, 0.34, -28.03, 0.99997, 65, -49.11, -28.03, 3e-05, 1, 18, 15.74, -7.74, 1, 3, 18, 29.52, 10.06, 0.97109, 65, -19.94, 10.06, 0.02868, 66, -49.61, 10.06, 0.00023, 3, 18, 49.02, 26.82, 0.4747, 65, -0.44, 26.82, 0.47364, 66, -30.11, 26.82, 0.05166, 4, 18, 65.42, 41.83, 0.20932, 65, 15.96, 41.83, 0.58682, 66, -13.71, 41.83, 0.20297, 67, -33.5, 41.83, 0.0009, 4, 18, 79.52, 48.8, 0.08999, 65, 30.06, 48.8, 0.60541, 66, 0.38, 48.8, 0.30141, 67, -19.4, 48.8, 0.00319, 4, 18, 94.28, 47.46, 0.02594, 65, 44.82, 47.46, 0.63572, 66, 15.14, 47.46, 0.33806, 67, -4.64, 47.46, 0.00027, 3, 18, 101.29, 44.69, 0.01116, 65, 51.83, 44.69, 0.64416, 66, 22.15, 44.69, 0.34469, 3, 18, 110.67, 42.01, 0.00167, 65, 61.21, 42.01, 0.64932, 66, 31.53, 42.01, 0.34901, 2, 18, 5.13, -39.68, 0.99882, 65, -44.33, -39.68, 0.00118, 2, 18, 19.29, -20.71, 0.98731, 65, -30.17, -20.71, 0.01269, 1, 18, 38.39, -0.89, 1, 3, 18, 60.83, 17.97, 0.16639, 65, 11.37, 17.97, 0.75584, 66, -18.3, 17.97, 0.07777, 4, 18, 80.95, 28.12, 0.04556, 65, 31.5, 28.12, 0.51105, 66, 1.82, 28.12, 0.40156, 67, -17.96, 28.12, 0.04184, 2, 18, 25.88, -36.15, 0.94924, 65, -23.58, -36.15, 0.05076, 3, 18, 50.85, -17.54, 0.45966, 65, 1.39, -17.54, 0.53744, 66, -28.28, -17.54, 0.00289, 2, 65, 21.49, -3.16, 0.99663, 66, -8.19, -3.16, 0.00337, 4, 18, 89.04, 6.88, 1e-05, 65, 39.58, 6.88, 0.06934, 66, 9.9, 6.88, 0.89242, 67, -9.88, 6.88, 0.03824, 3, 65, 52.63, 12.87, 0.04331, 66, 22.96, 12.87, 0.3437, 67, 3.17, 12.87, 0.61298], "hull": 41, "edges": [4, 6, 6, 8, 30, 32, 62, 64, 70, 72, 78, 80, 14, 16, 76, 78, 8, 10, 10, 12, 12, 14, 16, 18, 22, 24, 24, 26, 26, 28, 28, 30, 38, 40, 36, 38, 32, 34, 34, 36, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 64, 66, 66, 68, 68, 70, 72, 74, 74, 76, 0, 80, 0, 2, 2, 4, 2, 82, 82, 84, 84, 86, 86, 88, 88, 90, 18, 20, 20, 22, 90, 20, 0, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 24, 38, 104, 104, 26, 36, 106, 106, 28, 34, 108, 108, 30, 80, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 42, 74, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 48], "width": 124, "height": 165}}, "t7": {"t7": {"type": "mesh", "uvs": [0.64127, 0.03451, 0.80815, 0.20247, 0.94906, 0.34428, 0.94919, 0.59033, 0.94932, 0.83234, 0.7839, 0.93849, 0.68805, 1, 0.58878, 1, 0.44702, 0.80933, 0.30701, 0.62102, 0.0629, 0.56713, 0.00421, 0.32786, 0.20432, 0.13759, 0.35986, 0.03301, 0.242, 0.44393, 0.49146, 0.36127, 0.75538, 0.41962], "triangles": [15, 13, 0, 16, 15, 0, 1, 16, 0, 16, 1, 2, 15, 14, 12, 15, 12, 13, 11, 12, 14, 10, 11, 14, 16, 2, 3, 9, 14, 15, 10, 14, 9, 8, 9, 15, 8, 15, 16, 5, 16, 3, 5, 3, 4, 8, 16, 5, 6, 7, 8, 5, 6, 8], "vertices": [10.9, -15.87, 3.58, -12.34, -2.6, -9.35, -3.97, -2.35, -5.32, 4.54, 0.42, 8.79, 3.75, 11.26, 7.55, 12, 14.03, 7.63, 20.44, 3.32, 30.08, 3.6, 33.66, -2.77, 27.05, -9.68, 21.68, -13.81, 23.91, -1.24, 14.82, -5.45, 4.39, -5.76], "hull": 14, "edges": [0, 26, 12, 14, 18, 20, 20, 22, 22, 24, 24, 26, 0, 2, 2, 4, 14, 16, 16, 18, 4, 6, 6, 8, 8, 10, 10, 12, 20, 28, 28, 30, 30, 32, 32, 6], "width": 39, "height": 29}}, "ys1": {"ys1": {"type": "mesh", "uvs": [0.97392, 0.00772, 0.99909, 0.00891, 0.9821, 0.05806, 0.87539, 0.2404, 0.86709, 0.25823, 0.84274, 0.35796, 0.84833, 0.39255, 0.84104, 0.48978, 0.8308, 0.62622, 0.82249, 0.73711, 0.81456, 0.84287, 0.71767, 0.97177, 0.5724, 0.99915, 0.44194, 0.96239, 0.36376, 0.89262, 0.27313, 0.81174, 0.20259, 0.74878, 0.11122, 0.65209, 0.06306, 0.60113, 0, 0.53695, 0, 0.50136, 0.04558, 0.44773, 0.11879, 0.36159, 0.14927, 0.30245, 0.13435, 0.20944, 0.13533, 0.14569, 0.20861, 0.07352, 0.28083, 0.16892, 0.41087, 0.34072, 0.43726, 0.32955, 0.54257, 0.38074, 0.62568, 0.34478, 0.7428, 0.27954, 0.82226, 0.09764, 0.28161, 0.25568, 0.28161, 0.32843, 0.254, 0.44229, 0.21176, 0.53718, 0.15328, 0.61942, 0.18577, 0.36164, 0.16141, 0.43439, 0.12079, 0.53402, 0.39695, 0.54667, 0.52528, 0.62575, 0.64711, 0.64947, 0.73645, 0.58463, 0.43106, 0.44229, 0.58051, 0.50555, 0.68285, 0.49765, 0.76407, 0.4249, 0.34172, 0.66054, 0.49929, 0.76334, 0.64549, 0.81711, 0.75107, 0.78232, 0.26537, 0.73329, 0.39045, 0.84241, 0.56102, 0.91674, 0.6796, 0.91042, 0.76245, 0.85506], "triangles": [3, 33, 2, 2, 0, 1, 3, 4, 32, 16, 54, 15, 17, 38, 16, 16, 38, 54, 54, 38, 50, 38, 37, 50, 17, 18, 38, 18, 41, 38, 38, 41, 37, 18, 19, 41, 19, 20, 41, 20, 21, 41, 41, 40, 37, 41, 21, 40, 21, 22, 40, 40, 22, 39, 22, 23, 39, 40, 39, 36, 39, 23, 35, 37, 40, 36, 36, 39, 35, 13, 56, 12, 12, 57, 11, 12, 56, 57, 11, 58, 10, 11, 57, 58, 14, 55, 13, 13, 55, 56, 55, 51, 56, 56, 52, 57, 56, 51, 52, 57, 52, 58, 14, 15, 55, 52, 53, 58, 51, 55, 50, 53, 52, 44, 50, 55, 54, 55, 15, 54, 52, 51, 44, 51, 43, 44, 51, 50, 43, 50, 42, 43, 50, 37, 42, 43, 47, 44, 44, 47, 48, 43, 42, 47, 37, 36, 42, 42, 36, 46, 42, 46, 47, 46, 36, 35, 46, 30, 47, 47, 30, 31, 35, 28, 46, 46, 29, 30, 46, 28, 29, 23, 34, 35, 34, 28, 35, 23, 24, 34, 24, 27, 34, 34, 27, 28, 24, 25, 27, 25, 26, 27, 10, 58, 9, 9, 58, 53, 8, 9, 53, 53, 44, 45, 8, 53, 45, 44, 48, 45, 8, 45, 7, 7, 48, 49, 7, 45, 48, 48, 47, 31, 48, 31, 49, 7, 49, 6, 31, 32, 49, 49, 5, 6, 49, 32, 5, 5, 32, 4, 3, 32, 33, 33, 0, 2], "vertices": [2, 33, -32.33, 12.09, 0.9962, 32, 31.26, -22.73, 0.0038, 1, 32, 25.72, -22.1, 1, 1, 32, 30.18, -11.21, 1, 1, 32, 56.33, 28.61, 1, 1, 32, 58.41, 32.53, 1, 1, 33, 52.3, 12.15, 1, 1, 33, 59.25, 16, 1, 1, 33, 80.53, 22.07, 1, 1, 33, 110.4, 30.57, 1, 3, 33, 134.67, 37.49, 0.8653, 34, -39.29, -14.24, 0.13141, 70, -23.9, 149.81, 0.00329, 3, 33, 157.82, 44.08, 0.59806, 34, -55.54, 3.52, 0.38169, 70, -1.65, 158.98, 0.02026, 3, 33, 192.65, 34.02, 0.28333, 34, -62.12, 39.18, 0.64966, 70, 34.1, 152.92, 0.06701, 3, 33, 209.51, 6, 0.11149, 34, -44.59, 66.79, 0.75809, 70, 54.02, 126.98, 0.13042, 3, 33, 211.57, -23.95, 0.02594, 34, -18.73, 82.02, 0.75883, 70, 59.45, 97.46, 0.21523, 3, 33, 202.63, -45.61, 0.00261, 34, 4.65, 83.71, 0.68203, 70, 53.01, 74.92, 0.31537, 2, 34, 31.75, 85.66, 0.48902, 70, 45.54, 48.8, 0.51098, 2, 34, 52.85, 87.18, 0.28886, 70, 39.73, 28.46, 0.71114, 3, 34, 82.67, 86.79, 0.02857, 69, 49.43, -6.42, 0.00056, 70, 29.13, 0.59, 0.97087, 3, 68, 60.84, -18.69, 0.00846, 69, 40.39, -19.27, 0.12276, 70, 23.55, -14.11, 0.86879, 3, 68, 49.28, -35.21, 0.06479, 69, 29.11, -35.99, 0.38638, 70, 16.75, -33.09, 0.54883, 3, 68, 41.36, -36.8, 0.09026, 69, 21.22, -37.7, 0.43332, 70, 9.52, -36.7, 0.47642, 3, 68, 27.45, -29.31, 0.25206, 69, 7.18, -30.45, 0.51888, 70, -5.87, -33.13, 0.22905, 4, 34, 129.46, 40.3, 0.00319, 68, 5.1, -17.28, 0.93002, 69, -15.37, -18.79, 0.06572, 70, -30.6, -27.41, 0.00107, 2, 34, 134.59, 26.18, 0.59026, 68, -9.38, -13.3, 0.40974, 1, 34, 152.21, 14.08, 1, 1, 34, 162.58, 3.99, 1, 1, 34, 163.37, -19.03, 1, 1, 34, 136.67, -15.77, 1, 2, 33, 81.39, -78.84, 0.00034, 34, 88.58, -9.88, 0.99966, 2, 33, 77, -74.23, 0.00311, 34, 86.42, -15.86, 0.99689, 2, 33, 79.93, -48.38, 0.20515, 34, 61.99, -24.81, 0.79485, 2, 33, 65.95, -33.93, 0.68559, 34, 55.32, -43.77, 0.31441, 2, 33, 43.16, -14.71, 0.99111, 34, 48.31, -72.75, 0.00889, 1, 33, -1.65, -12.39, 1, 1, 34, 122.23, -2.37, 1, 1, 34, 110.23, 8.97, 1, 1, 34, 95.62, 31.15, 1, 3, 34, 86.37, 52.73, 0.07287, 69, 19.22, 9.76, 0.42333, 70, -4.13, 8.8, 0.5038, 2, 34, 81.68, 74.94, 0.0344, 70, 18.34, 5.58, 0.9656, 2, 34, 119.29, 29.54, 0.62767, 68, 2.21, -2.76, 0.37233, 3, 68, 19.46, -4.8, 0.45493, 69, -1.22, -6.08, 0.54495, 70, -20.03, -11.59, 0.00013, 3, 68, 43.4, -9.17, 0.02318, 69, 22.79, -10.04, 0.32529, 70, 4.21, -9.51, 0.65153, 1, 34, 56.71, 24.45, 1, 1, 34, 24.18, 16.16, 1, 2, 34, 1.78, 0.28, 0.99979, 70, -24.36, 106.25, 0.00021, 1, 33, 108.69, 7.75, 1, 4, 34, 68.76, 2.7, 0.98703, 68, 9.53, 53.99, 0.00036, 69, -12.12, 52.54, 0.00851, 70, -45.07, 42.51, 0.0041, 2, 33, 103.66, -30.78, 0.17395, 34, 35.64, -11.45, 0.82605, 2, 33, 94.21, -10.16, 0.83737, 34, 21.41, -29.12, 0.16263, 1, 33, 72.54, 1.03, 1, 3, 34, 46.3, 51.08, 0.50705, 69, 40.48, 43.77, 0.04552, 70, 8.07, 47.01, 0.44743, 4, 33, 164.78, -27.55, 0.00616, 34, 5.42, 41.78, 0.808, 69, 55.89, 82.75, 0.00151, 70, 13.37, 88.59, 0.18433, 3, 33, 165.16, 6.98, 0.25095, 34, -25.64, 26.67, 0.68506, 70, 9.83, 122.95, 0.06399, 3, 33, 149.73, 26.19, 0.65198, 34, -35.92, 4.28, 0.33243, 70, -7.67, 140.29, 0.01558, 3, 34, 45.88, 74.68, 0.37831, 69, 60.2, 30.79, 0.00398, 70, 30.38, 39.3, 0.61771, 4, 33, 189.9, -43.98, 0.00157, 34, 8.89, 71.59, 0.67557, 69, 78.54, 63.06, 0.00018, 70, 40.18, 75.1, 0.32268, 3, 33, 192.81, -2.79, 0.09513, 34, -29.26, 55.77, 0.76575, 70, 38.41, 116.36, 0.13912, 3, 33, 182.46, 21.33, 0.27616, 34, -46.21, 35.73, 0.65475, 70, 25.41, 139.16, 0.06909, 3, 33, 164.37, 34.22, 0.50839, 34, -49.65, 13.8, 0.4616, 70, 5.98, 149.92, 0.03], "hull": 34, "edges": [0, 66, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 20, 22, 22, 24, 24, 26, 36, 38, 38, 40, 44, 46, 46, 48, 48, 50, 50, 52, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 52, 54, 54, 56, 54, 68, 68, 70, 70, 72, 72, 74, 74, 76, 32, 34, 34, 36, 76, 34, 40, 42, 42, 44, 46, 78, 78, 72, 44, 80, 80, 74, 42, 82, 82, 76, 30, 32, 26, 28, 28, 30, 72, 84, 84, 86, 86, 88, 88, 90, 12, 14, 90, 14, 70, 92, 92, 94, 94, 96, 96, 98, 98, 10, 74, 100, 100, 102, 102, 104, 104, 106, 14, 16, 106, 16, 76, 108, 108, 110, 110, 112, 112, 114, 114, 116, 16, 18, 18, 20, 116, 18], "width": 221, "height": 227}}, "ys2": {"ys2": {"type": "mesh", "uvs": [0.38458, 0, 0.39408, 0, 0.46341, 0.11353, 0.52682, 0.21736, 0.61836, 0.29763, 0.7294, 0.3628, 0.87779, 0.4499, 0.9933, 0.62586, 0.98965, 0.75527, 0.9469, 0.91537, 0.88561, 1, 0.53797, 0.90762, 0.48891, 0.89459, 0.42117, 0.74405, 0.37175, 0.71259, 0.27831, 0.71388, 0.18898, 0.71511, 0.08216, 0.71659, 0.00849, 0.69997, 0.04028, 0.60373, 0.06844, 0.5185, 0.11362, 0.42718, 0.16326, 0.32686, 0.26152, 0.36881, 0.36737, 0.32646, 0.41435, 0.45291, 0.47486, 0.4552, 0.57471, 0.4575, 0.50058, 0.41383, 0.42978, 0.30149, 0.39876, 0.18411, 0.35936, 0.03504, 0.48294, 0.25435, 0.64746, 0.57935, 0.80964, 0.64363, 0.24084, 0.54006, 0.43123, 0.58292, 0.52759, 0.74363, 0.70152, 0.80077, 0.8543, 0.80435], "triangles": [9, 39, 8, 8, 39, 7, 10, 39, 9, 18, 19, 17, 16, 17, 20, 17, 19, 20, 16, 35, 15, 16, 20, 35, 20, 21, 35, 21, 22, 35, 12, 13, 37, 37, 13, 36, 13, 14, 36, 37, 36, 27, 27, 36, 26, 15, 35, 14, 14, 35, 36, 36, 35, 25, 25, 35, 23, 23, 24, 25, 36, 25, 26, 35, 22, 23, 29, 32, 28, 29, 30, 32, 30, 2, 32, 32, 2, 3, 2, 30, 31, 31, 0, 1, 2, 31, 1, 33, 27, 5, 5, 27, 4, 27, 28, 4, 28, 3, 4, 28, 32, 3, 11, 38, 10, 38, 39, 10, 12, 37, 11, 11, 37, 38, 38, 34, 39, 37, 33, 38, 38, 33, 34, 7, 39, 34, 37, 27, 33, 33, 5, 34, 34, 6, 7, 34, 5, 6], "vertices": [1, 37, 22.26, -1.07, 1, 1, 37, 21.68, -2.02, 1, 1, 37, 10.01, -4.48, 1, 2, 36, 18.15, -6.34, 0.65125, 37, -0.66, -6.73, 0.34875, 1, 36, 5.85, -7.58, 1, 2, 35, 31.22, -24.83, 0.25339, 36, -7.63, -11.1, 0.74661, 2, 35, 12.62, -24.39, 0.77546, 36, -25.64, -15.8, 0.22454, 1, 34, 161.47, -17.4, 1, 1, 34, 154.52, -10.25, 1, 1, 34, 148.99, 1.86, 1, 1, 34, 149.18, 11.54, 1, 2, 35, 38.11, 22.22, 0.65279, 38, -2.54, 22.93, 0.34721, 2, 35, 43.85, 23.22, 0.51939, 38, 3.2, 21.97, 0.48061, 3, 35, 55.23, 14.99, 0.09774, 38, 11.21, 10.43, 0.90224, 39, -18.64, 10.98, 2e-05, 3, 35, 61.49, 14.67, 0.00736, 38, 17.01, 8.05, 0.96951, 39, -13.29, 7.72, 0.02313, 2, 38, 27.94, 8.22, 0.33811, 39, -2.46, 6.19, 0.66189, 1, 39, 7.89, 4.72, 1, 1, 39, 20.26, 2.97, 1, 1, 39, 28.6, 0.43, 1, 2, 38, 55.85, -0.06, 0.01478, 39, 23.81, -6.35, 0.98522, 2, 38, 52.6, -6.65, 0.09682, 39, 19.58, -12.35, 0.90318, 2, 38, 47.36, -13.72, 0.31486, 39, 13.3, -18.51, 0.68514, 2, 38, 41.61, -21.48, 0.52178, 39, 6.41, -25.29, 0.47822, 2, 38, 30.09, -18.33, 0.82338, 39, -4.48, -20.38, 0.17662, 2, 38, 17.72, -21.68, 0.99977, 39, -17.21, -21.76, 0.00023, 2, 36, 18.23, 16.07, 0.00482, 38, 12.16, -11.98, 0.99518, 3, 35, 56.84, -8.06, 0.00701, 36, 12.38, 12.08, 0.05874, 38, 5.08, -11.85, 0.93426, 3, 35, 45.79, -11.85, 0.1293, 36, 2.8, 5.39, 0.72345, 38, -6.6, -11.76, 0.14725, 2, 36, 11.8, 7.73, 0.99909, 38, 2.09, -15.06, 0.00091, 2, 36, 23.58, 5.55, 0.23287, 37, -0.37, 6.34, 0.76713, 1, 37, 9.25, 4.8, 1, 1, 37, 21.46, 2.85, 1, 2, 36, 20.65, -1.03, 0.17845, 37, -0.46, -0.86, 0.82155, 2, 35, 34.61, -5.9, 0.79215, 36, -9.59, 8.03, 0.20785, 2, 35, 15.07, -7.66, 0.92913, 36, -27.88, 0.96, 0.07087, 2, 38, 32.41, -5.13, 0.59979, 39, -0.12, -7.7, 0.40021, 2, 36, 10.78, 23.04, 0.00115, 38, 10.12, -1.98, 0.99885, 2, 35, 43.53, 10.75, 0.49367, 38, -1.24, 10.31, 0.50633, 2, 35, 22.89, 8.01, 0.99047, 38, -21.62, 14.57, 0.00953, 1, 35, 5.97, 2.22, 1], "hull": 32, "edges": [0, 62, 0, 2, 6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 24, 26, 26, 28, 34, 36, 44, 46, 58, 56, 56, 54, 54, 52, 52, 50, 46, 48, 50, 48, 6, 64, 64, 58, 2, 4, 4, 6, 58, 60, 60, 62, 54, 66, 66, 68, 68, 14, 40, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 18, 40, 42, 42, 44, 36, 38, 38, 40, 32, 34, 28, 30, 30, 32, 20, 22, 22, 24, 8, 10, 10, 12], "width": 117, "height": 77}}, "zs2": {"zs2": {"type": "mesh", "uvs": [0.45761, 0.06079, 0.71148, 0.09232, 0.96384, 0.23354, 0.97803, 0.55893, 0.98289, 0.67035, 0.90814, 0.76002, 0.8128, 0.87438, 0.77585, 0.9187, 0.70808, 1, 0.66977, 1, 0.59996, 0.92, 0.5446, 0.85657, 0.56736, 0.72653, 0.65773, 0.63587, 0.70066, 0.5385, 0.61029, 0.46798, 0.43858, 0.32528, 0.38436, 0.32192, 0.54703, 0.4428, 0.62836, 0.55025, 0.61028, 0.61909, 0.45891, 0.57879, 0.28115, 0.47858, 0.15041, 0.40488, 0, 0.23253, 0, 0.15465, 0.17591, 0.02581, 0.42954, 0.46127, 0.28043, 0.37061, 0.19458, 0.14732, 0.64869, 0.22455, 0.86106, 0.52842, 0.80458, 0.68288, 0.69614, 0.79537, 0.77973, 0.22622], "triangles": [25, 26, 29, 30, 0, 1, 29, 26, 0, 2, 34, 1, 31, 2, 3, 32, 14, 31, 32, 31, 3, 32, 3, 4, 13, 14, 32, 5, 32, 4, 33, 13, 32, 33, 32, 5, 12, 13, 33, 11, 12, 33, 6, 33, 5, 7, 33, 6, 10, 11, 33, 7, 10, 33, 9, 10, 7, 8, 9, 7, 28, 29, 17, 24, 29, 28, 23, 24, 28, 27, 17, 18, 28, 17, 27, 22, 28, 27, 23, 28, 22, 21, 27, 18, 21, 18, 19, 22, 27, 21, 20, 21, 19, 30, 1, 34, 24, 25, 29, 17, 29, 0, 16, 17, 0, 30, 16, 0, 30, 15, 16, 31, 34, 2, 30, 34, 31, 31, 15, 30, 14, 15, 31], "vertices": [1, 41, 124.92, 11.7, 1, 1, 41, 123.65, 32.51, 1, 1, 41, 134.07, 55.88, 1, 2, 42, 80.57, 1.37, 0.17754, 43, -5.58, 7.65, 0.82246, 1, 43, 5.35, 12.97, 1, 1, 43, 16.74, 11.42, 1, 1, 43, 31.27, 9.46, 1, 1, 43, 36.9, 8.7, 1, 1, 43, 47.23, 7.3, 1, 1, 43, 48.5, 4.47, 1, 1, 43, 42.85, -4.25, 1, 1, 43, 38.36, -11.17, 1, 1, 43, 24.67, -15.27, 1, 2, 42, 65.62, -21.43, 0.06056, 43, 12.66, -12.61, 0.93944, 2, 42, 61.67, -10.98, 0.622, 43, 1.55, -13.77, 0.378, 2, 42, 51.15, -9.58, 0.98829, 43, -2.48, -23.59, 0.01171, 2, 42, 30.56, -6.18, 0.89748, 44, 12.96, 10.98, 0.10252, 2, 42, 26.91, -8.65, 0.44998, 44, 9.34, 8.47, 0.55002, 1, 44, 27.88, 6.65, 1, 1, 44, 40.41, 1.79, 1, 1, 44, 44.04, -4.94, 1, 1, 44, 31.77, -9.33, 1, 1, 44, 13.71, -10.03, 1, 1, 44, 0.43, -10.54, 1, 2, 42, -3.45, -20.57, 0.54841, 44, -20.91, -3.76, 0.45159, 3, 42, -8.77, -13.96, 0.3317, 44, -26.29, 2.8, 0.1243, 41, 143.2, -22.13, 0.544, 1, 41, 126.32, -11.39, 1, 1, 44, 21.8, -0.94, 1, 1, 44, 6.2, -0.97, 1, 2, 42, 3.01, -3.46, 0.94004, 44, -14.62, 13.42, 0.05996, 1, 42, 36.94, 13.04, 1, 2, 42, 71.11, -1.98, 0.4989, 43, -4.75, -2.36, 0.5011, 1, 43, 12.49, 0.34, 1, 1, 43, 27.26, -2.68, 1, 1, 42, 45.33, 19.55, 1], "hull": 27, "edges": [2, 4, 16, 18, 48, 50, 50, 52, 46, 48, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 42, 44, 44, 46, 44, 54, 54, 36, 46, 56, 56, 34, 48, 58, 2, 0, 0, 52, 58, 0, 0, 60, 60, 62, 62, 64, 64, 66, 18, 20, 20, 22, 66, 20, 8, 10, 10, 12, 12, 14, 14, 16, 4, 6, 6, 8], "width": 81, "height": 109}}, "zs1": {"zs1": {"type": "mesh", "uvs": [0.56905, 0.00069, 0.57051, 0.05285, 0.57203, 0.10743, 0.57342, 0.15718, 0.57466, 0.20184, 0.59244, 0.23977, 0.61133, 0.28006, 0.6788, 0.33077, 0.74953, 0.38392, 0.82786, 0.44278, 0.92904, 0.51881, 0.99757, 0.57032, 0.99716, 0.63427, 0.99682, 0.68569, 0.9964, 0.74977, 0.99607, 0.80036, 0.99563, 0.8677, 0.91217, 0.91831, 0.8436, 0.95989, 0.77746, 1, 0.76304, 1, 0.7195, 0.9572, 0.67755, 0.91595, 0.6288, 0.86803, 0.61556, 0.80657, 0.60285, 0.74762, 0.58962, 0.68623, 0.45347, 0.6356, 0.23083, 0.64101, 0.10476, 0.64408, 0, 0.64663, 0, 0.63464, 0.03345, 0.58314, 0.07627, 0.51719, 0.1163, 0.45555, 0.15342, 0.3984, 0.18917, 0.34335, 0.23427, 0.27391, 0.26858, 0.22107, 0.25575, 0.19366, 0.2516, 0.14824, 0.24686, 0.09659, 0.32543, 0.0539, 0.41994, 0.00256, 0.46619, 0.06167, 0.44887, 0.11327, 0.44488, 0.16487, 0.45154, 0.20045, 0.45953, 0.24019, 0.35259, 0.2945, 0.51353, 0.2945, 0.34501, 0.34761, 0.51543, 0.34593, 0.33555, 0.40326, 0.56276, 0.40494, 0.30715, 0.46877, 0.4927, 0.47045, 0.67447, 0.46792, 0.27306, 0.52441, 0.53815, 0.52947, 0.77672, 0.53031, 0.30336, 0.58089, 0.50596, 0.59354, 0.63282, 0.65025, 0.78619, 0.65029, 0.69684, 0.59465, 0.91269, 0.57863, 0.75554, 0.7034, 0.90891, 0.70003, 0.76311, 0.75651, 0.9108, 0.75314, 0.77636, 0.81047, 0.91648, 0.80541, 0.77826, 0.86611, 0.9089, 0.86358, 0.79909, 0.91911], "triangles": [40, 41, 45, 44, 43, 0, 41, 42, 45, 42, 43, 44, 39, 40, 46, 21, 22, 75, 21, 75, 18, 19, 20, 21, 18, 19, 21, 73, 71, 74, 74, 72, 16, 22, 23, 73, 17, 74, 16, 75, 73, 74, 17, 75, 74, 22, 73, 75, 18, 75, 17, 72, 70, 15, 71, 69, 70, 24, 25, 69, 72, 71, 70, 24, 69, 71, 74, 71, 72, 16, 72, 15, 23, 24, 71, 23, 71, 73, 13, 68, 12, 14, 68, 13, 70, 68, 14, 67, 68, 70, 69, 67, 70, 25, 67, 69, 15, 70, 14, 60, 9, 10, 66, 60, 10, 66, 10, 11, 65, 59, 60, 65, 60, 66, 12, 66, 11, 63, 62, 65, 64, 65, 66, 64, 66, 12, 63, 65, 64, 12, 68, 64, 26, 27, 63, 67, 63, 64, 67, 64, 68, 26, 63, 67, 25, 26, 67, 52, 6, 7, 53, 36, 51, 35, 36, 53, 54, 52, 7, 54, 7, 8, 53, 51, 52, 54, 53, 52, 57, 54, 8, 57, 8, 9, 55, 35, 53, 34, 35, 55, 56, 53, 54, 56, 54, 57, 55, 53, 56, 58, 34, 55, 33, 34, 58, 59, 56, 57, 58, 55, 56, 59, 58, 56, 60, 57, 9, 59, 57, 60, 61, 58, 59, 32, 33, 58, 32, 58, 61, 62, 61, 59, 62, 59, 65, 27, 61, 62, 28, 32, 61, 28, 61, 27, 29, 31, 32, 28, 29, 32, 30, 31, 29, 27, 62, 63, 1, 44, 0, 44, 1, 2, 45, 42, 44, 45, 44, 2, 45, 2, 3, 46, 40, 45, 46, 45, 3, 4, 47, 46, 39, 46, 47, 4, 46, 3, 38, 39, 47, 48, 47, 4, 48, 4, 5, 38, 47, 48, 50, 48, 5, 50, 5, 6, 49, 38, 48, 49, 48, 50, 37, 38, 49, 36, 37, 49, 52, 50, 6, 51, 36, 49, 52, 51, 49, 52, 49, 50], "vertices": [1, 31, 90.69, 7.16, 1, 2, 40, 16.68, 20.63, 0.801, 31, 99.37, -20.08, 0.199, 1, 40, 46.48, 23.29, 1, 1, 40, 73.64, 25.7, 1, 2, 40, 98.01, 27.88, 0.9988, 41, -85.98, 9.48, 0.0012, 3, 40, 118.41, 33.79, 0.95989, 41, -66.7, 18.37, 0.04, 71, -209.88, -53.13, 0.00011, 3, 40, 140.07, 40.07, 0.73617, 41, -46.22, 27.81, 0.25873, 71, -187.6, -49.61, 0.0051, 3, 40, 166.52, 58.61, 0.19823, 41, -22.83, 50.09, 0.75456, 71, -159.04, -34.53, 0.04721, 3, 40, 194.25, 78.04, 0.01142, 41, 1.68, 73.45, 0.82299, 71, -129.1, -18.72, 0.16559, 2, 41, 28.83, 99.31, 0.60345, 71, -95.95, -1.22, 0.39655, 2, 41, 63.89, 132.71, 0.2162, 71, -53.12, 21.39, 0.7838, 3, 41, 87.64, 155.34, 0.04361, 71, -24.11, 36.71, 0.95526, 72, -70.89, 26.07, 0.00112, 2, 71, 10.89, 34.88, 0.86324, 72, -35.95, 28.83, 0.13676, 3, 71, 39.03, 33.42, 0.29647, 72, -7.86, 31.05, 0.70314, 73, -50.26, 28.28, 0.00038, 3, 72, 27.14, 33.82, 0.72046, 73, -15.45, 32.82, 0.26477, 74, -59.4, 28.84, 0.01477, 3, 72, 54.78, 36, 0.09135, 73, 12.05, 36.41, 0.70698, 74, -31.99, 33.04, 0.20167, 2, 73, 48.64, 41.18, 0.16604, 74, 4.48, 38.63, 0.83396, 2, 74, 35.03, 22.79, 0.96685, 75, -11.11, 23.8, 0.03315, 2, 74, 60.12, 9.78, 0.05286, 75, 15, 12.97, 0.94714, 2, 73, 127.54, -2.01, 0, 75, 40.19, 2.53, 1, 2, 73, 128, -5.5, 1e-05, 75, 41.02, -0.89, 0.99999, 2, 73, 106.16, -19.13, 0.01007, 75, 20.77, -16.79, 0.98993, 3, 73, 85.1, -32.26, 0.15111, 74, 42.59, -33.97, 0.22354, 75, 1.25, -32.11, 0.62534, 5, 71, 134.42, -61.18, 0.00536, 72, 99.06, -50.29, 0.00302, 73, 60.65, -47.52, 0.5329, 74, 18.48, -49.77, 0.25031, 75, -21.43, -49.91, 0.20841, 6, 41, 234.73, 93.55, 0.0017, 71, 100.63, -62.75, 0.06823, 72, 65.76, -56.26, 0.08587, 73, 27.69, -55.18, 0.76848, 74, -14.29, -58.17, 0.03879, 75, -53.37, -61.06, 0.03693, 5, 41, 203.94, 83.28, 0.02687, 71, 68.2, -64.26, 0.28391, 72, 33.81, -61.99, 0.23762, 73, -3.92, -62.52, 0.44957, 75, -84.02, -71.75, 0.00202, 4, 41, 171.88, 72.59, 0.17529, 71, 34.45, -65.83, 0.60434, 72, 0.55, -67.95, 0.11714, 73, -36.84, -70.16, 0.10323, 4, 41, 152.29, 33.99, 0.67328, 71, 5.1, -97.65, 0.31869, 72, -24.39, -103.33, 0.0036, 73, -59.95, -106.76, 0.00443, 2, 41, 167.37, -18.28, 0.98805, 71, 5.4, -152.06, 0.01195, 1, 41, 175.91, -47.88, 1, 1, 41, 183.01, -72.48, 1, 1, 41, 176.6, -73.95, 1, 1, 41, 147.27, -72.33, 1, 2, 40, 279.61, -80.18, 0.00288, 41, 109.71, -70.26, 0.99712, 2, 40, 245.18, -73.02, 0.04302, 41, 74.6, -68.32, 0.95698, 2, 40, 213.26, -66.38, 0.19562, 41, 42.04, -66.52, 0.80438, 2, 40, 182.52, -59.99, 0.53595, 41, 10.69, -64.79, 0.46405, 2, 40, 143.73, -51.92, 0.95281, 41, -28.86, -62.6, 0.04719, 1, 40, 114.23, -45.79, 1, 1, 31, 48.43, -116.3, 1, 1, 31, 40.2, -92.78, 1, 1, 31, 30.83, -66.04, 1, 1, 31, 42.34, -38.08, 1, 1, 31, 56.19, -4.44, 1, 2, 40, 23.45, -4.38, 0.77, 31, 76.44, -32.13, 0.23, 2, 40, 51.96, -6.43, 0.8165, 31, 80.65, -60.41, 0.1835, 2, 40, 80.23, -5.25, 0.87724, 31, 87.96, -87.74, 0.12276, 1, 40, 99.55, -2.14, 1, 2, 40, 121.11, 1.47, 0.99955, 41, -59.2, -13.18, 0.00045, 2, 40, 152.78, -22.27, 0.97105, 41, -24.34, -31.93, 0.02895, 3, 40, 149.78, 16.88, 0.84709, 41, -33.15, 6.34, 0.15128, 71, -180.87, -73.83, 0.00163, 2, 40, 181.94, -21.9, 0.51788, 41, 4.44, -27.2, 0.48212, 3, 40, 177.85, 19.49, 0.1553, 41, -5.79, 13.11, 0.83621, 71, -152.7, -74.76, 0.00848, 2, 40, 212.52, -21.87, 0.1019, 41, 34.67, -22.61, 0.8981, 3, 40, 209.21, 33.48, 0.00056, 41, 23.13, 31.62, 0.93271, 71, -119.83, -64.81, 0.06673, 2, 40, 248.85, -26.04, 0.01253, 41, 71.21, -21.31, 0.98747, 2, 41, 61.95, 23.02, 0.92987, 71, -84.82, -83.64, 0.07013, 2, 41, 50.65, 65.93, 0.69646, 71, -84.02, -39.28, 0.30354, 2, 40, 279.88, -32, 0.00071, 41, 102.79, -22.58, 0.99929, 2, 41, 90.98, 41.08, 0.75902, 71, -51.97, -74.16, 0.24098, 2, 41, 78.37, 97.91, 0.34703, 71, -48.65, -16.04, 0.65297, 2, 41, 131.3, -8.43, 0.97985, 71, -26.64, -132.76, 0.02015, 4, 41, 126.96, 41.3, 0.6582, 71, -17.29, -83.73, 0.33929, 72, -48.41, -92.45, 0.00079, 73, -84.49, -97.11, 0.00172, 4, 41, 150.3, 78.44, 0.2321, 71, 15.27, -54.34, 0.68727, 72, -19.96, -59.06, 0.04467, 73, -57.78, -62.32, 0.03596, 4, 41, 141.92, 114.91, 0.03588, 71, 17.13, -16.96, 0.94955, 72, -23, -21.76, 0.00807, 73, -62.7, -25.23, 0.00651, 4, 41, 117.1, 86.82, 0.26493, 71, -14.4, -37.24, 0.73337, 72, -51.61, -45.98, 0.00015, 73, -90.05, -50.87, 0.00155, 3, 41, 96.73, 136.18, 0.05466, 71, -20.58, 15.8, 0.94499, 72, -64.66, 5.8, 0.00035, 4, 41, 171.97, 114.15, 0.02561, 71, 45.83, -25.86, 0.50882, 72, 6.62, -26.84, 0.379, 73, -32.86, -28.79, 0.08658, 2, 71, 45.83, 11.61, 0.04756, 72, 1.72, 10.31, 0.95244, 5, 41, 199.92, 122.48, 0.00465, 71, 75, -25.45, 0.10782, 72, 35.48, -22.62, 0.43628, 73, -4.26, -23.11, 0.45073, 75, -88.58, -32.61, 0.00052, 3, 72, 30.69, 13.15, 0.8053, 73, -10.85, 12.36, 0.18858, 74, -54.35, 8.49, 0.00613, 5, 71, 104.69, -23.67, 0.01367, 72, 64.68, -16.98, 0.02198, 73, 24.62, -16, 0.94384, 74, -18.24, -19.07, 0.01008, 75, -60.63, -22.44, 0.01043, 3, 72, 59.13, 16.87, 0.01148, 73, 17.36, 17.52, 0.82507, 74, -26.26, 14.28, 0.16346, 4, 71, 135.16, -24.7, 0.00036, 73, 54.79, -11.51, 0.28651, 74, 11.81, -13.91, 0.64105, 75, -31.12, -14.74, 0.07208, 2, 73, 49.2, 19.9, 0.0677, 74, 5.52, 17.38, 0.9323, 3, 73, 82.9, -2.64, 0.00859, 74, 39.72, -4.4, 0.54227, 75, -4.13, -2.89, 0.44914], "hull": 44, "edges": [0, 86, 38, 40, 52, 54, 60, 62, 76, 78, 82, 84, 84, 86, 84, 88, 0, 2, 88, 2, 82, 90, 2, 4, 90, 4, 78, 80, 80, 82, 80, 92, 4, 6, 6, 8, 92, 6, 78, 94, 94, 8, 76, 96, 8, 10, 10, 12, 96, 10, 74, 76, 74, 98, 98, 100, 100, 12, 72, 74, 72, 102, 102, 104, 12, 14, 104, 14, 70, 72, 70, 106, 106, 108, 14, 16, 108, 16, 68, 70, 68, 110, 110, 112, 112, 114, 16, 18, 114, 18, 66, 68, 66, 116, 116, 118, 118, 120, 18, 20, 20, 22, 120, 20, 62, 64, 64, 66, 64, 122, 122, 124, 124, 126, 126, 128, 22, 24, 128, 24, 124, 130, 130, 132, 132, 22, 52, 134, 134, 136, 24, 26, 136, 26, 50, 52, 50, 138, 138, 140, 26, 28, 140, 28, 46, 48, 48, 50, 48, 142, 142, 144, 28, 30, 30, 32, 144, 30, 46, 146, 146, 148, 148, 32, 44, 46, 44, 150, 32, 34, 150, 34, 40, 42, 42, 44, 34, 36, 36, 38, 42, 36, 58, 60, 54, 56, 56, 58, 56, 122], "width": 244, "height": 548}}, "weib": {"weib": {"type": "mesh", "uvs": [1, 0.01378, 0.92676, 0.16569, 0.81932, 0.38854, 0.69573, 0.51983, 0.45918, 0.77115, 0.25554, 0.98749, 0.07237, 0.97722, 0.03373, 0.89134, 0.10986, 0.73329, 0.16408, 0.62073, 0.18271, 0.53169, 0.09023, 0.43435, 0.08984, 0.19091, 0.34372, 0.13795, 0.64283, 0.07555, 0.95265, 0.01092], "triangles": [2, 11, 12, 10, 11, 3, 4, 9, 3, 8, 9, 4, 7, 8, 4, 5, 6, 7, 4, 5, 7, 3, 11, 2, 3, 9, 10, 1, 14, 15, 1, 15, 0, 1, 13, 14, 2, 13, 1, 2, 12, 13], "vertices": [1, 79, -17.98, 52.63, 1, 2, 79, 3.34, 40.59, 0.99791, 80, -44.2, 26.02, 0.00209, 2, 79, 34.62, 22.92, 0.55331, 80, -8.57, 21.42, 0.44669, 2, 79, 51.92, 9.4, 0.00465, 80, 12.54, 15.4, 0.99535, 1, 81, 18.84, 8.41, 1, 1, 81, 54.87, 5.08, 1, 1, 81, 55.5, -5.11, 1, 2, 81, 42.65, -10.2, 0.856, 18, 141.93, 38.95, 0.144, 2, 81, 17.22, -11.66, 0.448, 18, 116.96, 43.97, 0.552, 3, 80, 29.66, -13.22, 0.19538, 81, -0.9, -12.7, 0.30062, 18, 99.17, 47.55, 0.504, 4, 79, 44.05, -17.76, 0.01375, 80, 15.47, -12.72, 0.22023, 81, -14.93, -14.83, 0.00602, 18, 85.06, 49.05, 0.76, 3, 79, 27.77, -17.25, 0.23785, 80, 0.19, -18.38, 0.13815, 18, 69.42, 44.48, 0.624, 2, 79, -8.62, -4.04, 0.32, 18, 30.73, 45.76, 0.68, 1, 79, -11.76, 11.96, 1, 1, 79, -15.46, 30.81, 1, 1, 79, -19.3, 50.33, 1], "hull": 16, "edges": [0, 30, 10, 12, 12, 14, 18, 20, 20, 22, 22, 24, 14, 16, 16, 18, 4, 6, 6, 8, 8, 10, 24, 26, 26, 28, 28, 30, 0, 2, 2, 4], "width": 55, "height": 159}}, "t8": {"t8": {"type": "mesh", "uvs": [0.98601, 0.15543, 0.73493, 0.81917, 0.52467, 0.9503, 0.24057, 0.95586, 0.0977, 0.9271, 0, 0.72161, 0, 0.68259, 0.09787, 0.44042, 0.35748, 0.22864, 0.67443, 0.09067, 0.955, 0.05634, 0.20195, 0.57064, 0.48443, 0.3462, 0.76691, 0.25225, 0.48015, 0.85249, 0.68559, 0.65937, 0.76263, 0.39839], "triangles": [13, 9, 10, 16, 13, 10, 12, 8, 9, 12, 9, 13, 16, 12, 13, 0, 16, 10, 11, 7, 8, 11, 8, 12, 15, 12, 16, 6, 7, 11, 5, 6, 11, 1, 15, 16, 1, 16, 0, 14, 11, 12, 14, 12, 15, 14, 15, 1, 4, 5, 11, 3, 4, 11, 2, 14, 1, 14, 3, 11, 3, 14, 2], "vertices": [48.04, -1.18, 22.51, -16.88, 10.78, -15.54, -1.17, -7.86, -6.47, -2.92, -5.87, 6.8, -4.98, 8.13, 4.59, 13.69, 20.21, 13.73, 36.53, 9.66, 48.99, 3.06, 5.97, 6.36, 22.82, 6.2, 36.72, 1.58, 11.15, -10.97, 24.09, -10.06, 33.22, -3.29], "hull": 11, "edges": [0, 20, 0, 2, 2, 4, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 8, 22, 22, 24, 24, 26, 26, 20, 4, 6, 6, 28, 28, 30, 30, 32, 32, 20], "width": 50, "height": 41}}, "st1": {"st1": {"type": "mesh", "uvs": [0.70762, 0.15264, 0.82148, 0.18287, 0.99669, 0.22939, 0.99754, 0.27364, 0.96653, 0.31634, 0.92624, 0.37183, 0.88824, 0.42416, 0.82895, 0.50582, 0.76961, 0.58753, 0.71374, 0.64593, 0.63691, 0.72623, 0.58732, 0.77805, 0.53426, 0.83352, 0.49098, 0.87876, 0.42639, 0.94626, 0.3353, 1, 0.31797, 1, 0.24936, 0.92156, 0.20662, 0.8853, 0.16024, 0.84595, 0.07775, 0.83032, 0.00347, 0.81624, 0.00725, 0.74999, 0.01199, 0.66704, 0.01602, 0.59641, 0.02025, 0.52229, 0.0637, 0.45507, 0.11812, 0.37089, 0.15337, 0.31634, 0.1995, 0.24497, 0.32342, 0.16644, 0.4694, 0.14719, 0.61481, 0.128, 0.15731, 0.44684, 0.21221, 0.45398, 0.3009, 0.44506, 0.42231, 0.47539, 0.59018, 0.50395, 0.12438, 0.50484, 0.18562, 0.51733, 0.2838, 0.51554, 0.41155, 0.55927, 0.58259, 0.56997, 0.11745, 0.55927, 0.15863, 0.58246, 0.26737, 0.58336, 0.40568, 0.62351, 0.5651, 0.63243, 0.12723, 0.63332, 0.25604, 0.65295, 0.40279, 0.72076, 0.11034, 0.71987, 0.24548, 0.74396, 0.41968, 0.81624, 0.10295, 0.79807, 0.23703, 0.81949, 0.36162, 0.87838, 0.19143, 0.38313, 0.259, 0.40098, 0.33185, 0.39652, 0.46805, 0.40901, 0.63803, 0.43667, 0.24633, 0.33227, 0.38253, 0.35458, 0.58313, 0.35726, 0.7678, 0.36529, 0.26342, 0.27428, 0.52948, 0.30104, 0.71993, 0.31889, 0.88674, 0.31978, 0.45607, 0.21539, 0.70524, 0.24126, 0.8805, 0.26178], "triangles": [21, 22, 54, 18, 19, 55, 20, 21, 54, 20, 54, 19, 17, 18, 55, 72, 2, 3, 4, 72, 3, 5, 69, 4, 72, 1, 2, 69, 72, 4, 68, 72, 69, 71, 32, 0, 71, 0, 1, 71, 70, 32, 71, 1, 72, 67, 70, 71, 68, 71, 72, 67, 71, 68, 64, 67, 68, 63, 67, 64, 65, 68, 69, 64, 68, 65, 65, 69, 5, 60, 63, 64, 6, 65, 5, 61, 64, 65, 60, 64, 61, 61, 65, 6, 37, 60, 61, 7, 61, 6, 37, 61, 7, 8, 37, 7, 42, 37, 8, 9, 42, 8, 47, 42, 9, 10, 47, 9, 70, 30, 31, 66, 29, 30, 66, 30, 70, 67, 66, 70, 62, 29, 66, 28, 29, 62, 62, 66, 63, 57, 28, 62, 27, 28, 57, 57, 62, 58, 70, 31, 32, 63, 66, 67, 59, 62, 63, 58, 62, 59, 59, 63, 60, 42, 41, 37, 23, 24, 48, 46, 45, 41, 46, 41, 47, 47, 41, 42, 35, 58, 59, 45, 40, 41, 44, 39, 45, 48, 43, 44, 35, 59, 36, 45, 39, 40, 33, 27, 57, 34, 57, 58, 34, 58, 35, 33, 57, 34, 26, 27, 33, 38, 26, 33, 40, 34, 35, 40, 35, 36, 39, 33, 34, 39, 34, 40, 38, 33, 39, 25, 26, 38, 43, 25, 38, 43, 38, 39, 41, 40, 36, 41, 36, 37, 44, 43, 39, 24, 25, 43, 24, 43, 48, 36, 60, 37, 49, 44, 45, 48, 44, 49, 36, 59, 60, 49, 45, 46, 50, 46, 47, 50, 49, 46, 51, 23, 48, 51, 48, 49, 52, 51, 49, 52, 49, 50, 22, 23, 51, 11, 47, 10, 50, 47, 11, 54, 22, 51, 54, 51, 52, 12, 53, 50, 52, 50, 53, 55, 54, 52, 55, 52, 53, 11, 12, 50, 19, 54, 55, 56, 55, 53, 13, 53, 12, 56, 53, 13, 56, 17, 55, 14, 56, 13, 16, 17, 56, 15, 16, 56, 14, 15, 56], "vertices": [3, 3, 120.22, -14.05, 0.18761, 76, -0.19, 31.73, 0.79725, 77, -27.13, 35.83, 0.01514, 4, 3, 133.79, -40.75, 0.00409, 76, 29.08, 25.34, 0.55297, 77, 0.81, 25.05, 0.3942, 78, -17.38, 26.8, 0.04875, 1, 78, 22.5, 3.68, 1, 3, 31, 110.6, 49.94, 0.00915, 77, 43.2, -4.66, 0.00542, 78, 19.83, -9.19, 0.98544, 3, 31, 106.86, 35.53, 0.10374, 77, 34.62, -16.83, 0.18748, 78, 9.46, -19.86, 0.70879, 3, 31, 102, 16.82, 0.45179, 77, 23.48, -32.63, 0.33812, 78, -4.02, -33.72, 0.21009, 5, 2, 118.24, -160.98, 0.00093, 82, -143.26, -3.02, 0.00159, 31, 97.41, -0.83, 0.84431, 77, 12.97, -47.53, 0.11671, 78, -16.73, -46.8, 0.03646, 3, 2, 93.28, -147.31, 0.03262, 82, -126.75, 20.16, 0.04561, 31, 90.25, -28.37, 0.92177, 3, 2, 68.3, -133.64, 0.11923, 82, -110.23, 43.34, 0.13248, 31, 83.09, -55.93, 0.74829, 3, 2, 50.28, -120.49, 0.22496, 82, -95.05, 59.69, 0.18953, 31, 74.74, -76.61, 0.58552, 3, 2, 25.51, -102.41, 0.42859, 82, -74.17, 82.15, 0.20676, 31, 63.25, -105.05, 0.36465, 3, 2, 9.52, -90.74, 0.57991, 82, -60.7, 96.66, 0.17398, 31, 55.84, -123.4, 0.24611, 3, 2, -7.59, -78.25, 0.73724, 82, -46.28, 112.18, 0.11798, 31, 47.91, -143.04, 0.14478, 3, 2, -21.54, -68.06, 0.84524, 82, -34.52, 124.83, 0.07186, 31, 41.43, -159.06, 0.08289, 4, 2, -42.37, -52.86, 0.62219, 82, -16.97, 143.72, 0.01606, 31, 31.78, -182.97, 0.01775, 18, 28.85, 58.91, 0.344, 4, 2, -59.44, -30.82, 0.55473, 82, 6.93, 158.08, 0.00253, 31, 14.57, -204.91, 0.00274, 18, 44.04, 35.53, 0.44, 4, 2, -59.66, -26.47, 0.47585, 82, 11.28, 157.78, 0.00199, 31, 10.41, -206.18, 0.00215, 18, 43.9, 31.18, 0.52, 2, 2, -37.24, -8.12, 0.2, 18, 20.04, 14.75, 0.8, 2, 2, -27.01, 3.13, 0.24, 18, 8.91, 4.38, 0.76, 1, 18, -3.15, -6.86, 1, 1, 18, -8.49, -27.4, 1, 1, 18, -13.29, -45.89, 1, 2, 2, 10.65, 55.09, 0.808, 18, -32.93, -44.29, 0.192, 2, 2, 35.31, 55.13, 0.7704, 82, 81.11, 53.85, 0.2296, 2, 2, 56.31, 55.15, 0.01794, 82, 78.66, 32.99, 0.98206, 1, 82, 76.09, 11.1, 1, 1, 82, 63.84, -8.07, 1, 4, 2, 124.48, 32.87, 0.14558, 82, 48.49, -32.07, 0.51649, 3, -29.6, 46.36, 0.01362, 32, 73.89, 24.08, 0.32431, 4, 2, 141.1, 24.83, 0.10774, 82, 38.55, -47.63, 0.13558, 3, -11.84, 51.36, 0.02077, 32, 64.03, 8.47, 0.73591, 1, 32, 51.14, -11.96, 1, 2, 3, 49.84, 52, 0.04342, 32, 18.63, -33.27, 0.95658, 3, 3, 79.55, 29.82, 0.63889, 32, -18.3, -36.66, 0.31195, 76, -59.89, 27.99, 0.04916, 3, 3, 109.15, 7.72, 0.51472, 32, -55.09, -40.04, 0.00465, 76, -24.05, 36.93, 0.48063, 1, 82, 40.23, -8.89, 1, 1, 82, 26.63, -5.83, 1, 1, 82, 4.24, -6.95, 1, 1, 82, -25.55, 4.13, 1, 4, 2, 90.87, -87.43, 0.10739, 82, -67, 15.49, 0.20183, 3, 25.03, -65.96, 0.02209, 31, 32.77, -45.32, 0.66869, 1, 82, 49.66, 7.73, 1, 1, 82, 34.58, 12.48, 1, 1, 82, 9.95, 13.65, 1, 1, 82, -21.14, 28.8, 1, 4, 2, 71.19, -86.5, 0.21568, 82, -63.75, 34.92, 0.24778, 3, 9.69, -78.32, 0.00856, 31, 36.67, -64.64, 0.52798, 1, 82, 52.5, 23.73, 1, 1, 82, 42.66, 31.32, 1, 1, 82, 15.45, 33.45, 1, 1, 82, -18.36, 47.73, 1, 4, 2, 52.44, -83.03, 0.3462, 82, -58.1, 53.13, 0.24754, 3, -6.64, -88.16, 0.00129, 31, 37.88, -83.66, 0.40498, 2, 2, 46.75, 26.73, 0.43131, 82, 51.56, 45.84, 0.56869, 3, 2, 42.52, -5.85, 0.50738, 82, 19.71, 53.88, 0.48995, 31, -34.54, -112.13, 0.00266, 3, 2, 24.23, -43.64, 0.69771, 82, -15.66, 76.5, 0.20126, 31, 6.57, -120.64, 0.10103, 1, 2, 20.86, 29.69, 1, 1, 2, 15.39, -4.54, 1, 3, 2, -3.88, -49.27, 0.8476, 82, -17.94, 105.08, 0.07279, 31, 18.9, -146.53, 0.07961, 2, 2, -2.43, 30.4, 0.664, 18, -17.85, -20.76, 0.336, 1, 2, -7.11, -3.53, 1, 1, 2, -23.04, -35.63, 1, 4, 2, 121.76, 14.31, 0.26214, 82, 30.38, -27.18, 0.34022, 3, -19.33, 30.66, 0.07791, 32, 55.75, 28.87, 0.31972, 4, 2, 117.3, -2.89, 0.27789, 82, 13.83, -20.73, 0.3552, 3, -11.25, 14.83, 0.24619, 32, 39.16, 35.23, 0.12071, 4, 2, 119.53, -21.09, 0.01283, 82, -4.5, -20.8, 0.2419, 3, 2.48, 2.69, 0.72578, 32, 20.83, 35.06, 0.0195, 4, 2, 117.52, -55.42, 0.00915, 82, -38.35, -14.75, 0.21951, 3, 23.74, -24.33, 0.38045, 31, -4.78, -27.3, 0.39089, 4, 2, 111.42, -98.44, 0.02948, 82, -80.35, -3.62, 0.07362, 3, 47.71, -60.57, 0.00369, 31, 38.42, -22.71, 0.89321, 4, 2, 137.53, 1.29, 0.11661, 82, 15.6, -41.3, 0.01746, 3, 1.1, 31.38, 0.16786, 32, 41.05, 14.66, 0.69807, 4, 2, 132.6, -33.18, 0.00047, 82, -18.05, -32.35, 0.00354, 3, 20.28, 2.31, 0.96029, 32, 7.35, 23.43, 0.0357, 3, 2, 134.3, -83.51, 0.00109, 82, -68.23, -28.1, 0.00749, 31, 18.36, -4.17, 0.99142, 3, 31, 63.39, 7.07, 0.87173, 76, 20.51, -29.82, 0.09439, 77, -16.08, -28.15, 0.03389, 1, 32, 35.68, -2.26, 1, 3, 3, 57.44, -12.92, 0.33622, 31, 0.61, 7.87, 0.65187, 76, -40.78, -16.17, 0.01191, 3, 31, 47.88, 16.75, 0.50903, 76, 7.31, -17.17, 0.49, 77, -27.2, -13.64, 0.00098, 4, 31, 88, 28.71, 0.23184, 76, 49.03, -13.69, 0.00471, 77, 14.57, -16.57, 0.56269, 78, -10.31, -16.47, 0.20076, 3, 3, 62.74, 18.04, 0.58968, 32, -13.69, -16.66, 0.40509, 76, -61.41, 7.52, 0.00524, 3, 3, 100.99, -32.03, 0.02972, 76, 1.57, 5.46, 0.96854, 77, -29.41, 9.61, 0.00174, 3, 76, 45.93, 3.33, 0.00151, 77, 14.1, 0.72, 0.97339, 78, -8.06, 0.68, 0.0251], "hull": 33, "edges": [4, 6, 28, 30, 30, 32, 32, 34, 58, 60, 0, 64, 0, 2, 2, 4, 6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 38, 40, 40, 42, 34, 36, 36, 38, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 60, 62, 62, 64, 52, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 14, 50, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 16, 48, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 18, 46, 96, 96, 98, 98, 100, 20, 22, 22, 24, 100, 22, 44, 102, 102, 104, 104, 106, 106, 24, 42, 108, 108, 110, 110, 112, 24, 26, 26, 28, 54, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 12, 54, 56, 56, 58, 56, 124, 124, 126, 126, 128, 128, 130, 8, 10, 10, 12, 130, 10, 58, 132, 132, 134, 134, 136, 136, 138, 138, 8, 60, 140, 140, 142, 142, 144, 144, 4], "width": 251, "height": 297}}, "zuiz1": {"zuiz1": {"type": "mesh", "uvs": [0.61397, 0.0074, 0.61385, 0.05857, 0.61377, 0.09522, 0.61371, 0.12008, 0.61361, 0.16066, 0.6135, 0.20909, 0.61336, 0.26931, 0.6133, 0.29548, 0.6132, 0.33999, 0.6131, 0.38318, 0.61296, 0.4406, 0.61283, 0.49853, 0.6127, 0.55222, 0.61258, 0.60695, 0.61247, 0.65173, 0.61238, 0.69154, 0.61228, 0.7334, 0.7186, 0.7785, 0.80908, 0.81688, 0.88941, 0.85095, 0.89008, 0.91375, 0.89046, 0.94861, 0.89075, 0.97577, 0.72362, 0.98389, 0.22413, 0.98406, 0.05447, 0.97227, 0.05443, 0.93867, 0.05438, 0.90254, 0.1149, 0.858, 0.17376, 0.81467, 0.29807, 0.7678, 0.38672, 0.73438, 0.38675, 0.69486, 0.38678, 0.65174, 0.38681, 0.60032, 0.38684, 0.55388, 0.38688, 0.49914, 0.38692, 0.43633, 0.38696, 0.38449, 0.38699, 0.33998, 0.38702, 0.29024, 0.38706, 0.23658, 0.38709, 0.19862, 0.38711, 0.15673, 0.38714, 0.11878, 0.38716, 0.0926, 0.38718, 0.05857, 0.38722, 0.00284, 0.54795, 0.78276, 0.50261, 0.82091, 0.49128, 0.86569, 0.38928, 0.91544, 0.67351, 0.91448, 0.40061, 0.95027, 0.69528, 0.95027], "triangles": [21, 54, 20, 53, 52, 54, 26, 51, 53, 25, 26, 53, 54, 21, 22, 23, 54, 22, 24, 25, 53, 23, 24, 53, 23, 53, 54, 20, 52, 19, 51, 50, 52, 27, 28, 51, 26, 27, 51, 54, 52, 20, 53, 51, 52, 49, 29, 30, 48, 49, 30, 18, 49, 48, 18, 48, 17, 49, 28, 29, 50, 28, 49, 19, 50, 49, 19, 49, 18, 52, 50, 19, 51, 28, 50, 14, 33, 13, 15, 33, 14, 32, 33, 15, 16, 32, 15, 31, 32, 16, 48, 31, 16, 48, 16, 17, 30, 31, 48, 10, 37, 9, 10, 36, 37, 11, 36, 10, 12, 36, 11, 35, 36, 12, 12, 34, 35, 13, 34, 12, 13, 33, 34, 46, 47, 0, 1, 46, 0, 45, 46, 1, 2, 45, 1, 44, 45, 2, 3, 44, 2, 43, 44, 3, 4, 43, 3, 42, 43, 4, 5, 42, 4, 41, 42, 5, 6, 41, 5, 40, 41, 6, 7, 40, 6, 39, 40, 7, 8, 39, 7, 9, 39, 8, 38, 39, 9, 9, 37, 38], "vertices": [1, 45, -1.26, 2.04, 1, 1, 45, 5.04, 2.01, 1, 1, 45, 9.54, 2, 1, 1, 45, 12.6, 1.98, 1, 1, 45, 17.59, 1.96, 1, 1, 45, 23.55, 1.94, 1, 1, 45, 30.96, 1.91, 1, 1, 45, 34.18, 1.9, 1, 1, 45, 39.65, 1.87, 1, 2, 45, 44.96, 1.85, 0.9781, 46, -2.11, 1.85, 0.0219, 1, 46, 4.95, 1.82, 1, 1, 46, 12.08, 1.79, 1, 1, 46, 18.68, 1.77, 1, 1, 46, 25.41, 1.74, 1, 2, 46, 30.92, 1.72, 0.0123, 47, 1.95, 1.72, 0.9877, 1, 47, 6.85, 1.7, 1, 1, 47, 11.99, 1.67, 1, 2, 47, 17.55, 3.57, 0.55215, 48, -0.56, 3.57, 0.44785, 3, 47, 22.28, 5.18, 0.0039, 48, 4.17, 5.18, 0.97341, 49, -6.69, 5.18, 0.02269, 2, 48, 8.37, 6.61, 0.73158, 49, -2.5, 6.61, 0.26842, 3, 48, 16.09, 6.59, 0.03739, 49, 5.23, 6.59, 0.71397, 50, -2.02, 6.59, 0.24864, 2, 49, 9.51, 6.58, 0.2386, 50, 2.27, 6.58, 0.7614, 2, 49, 12.86, 6.58, 0.05536, 50, 5.61, 6.58, 0.94464, 2, 49, 13.84, 3.56, 0.01082, 50, 6.6, 3.56, 0.98918, 2, 49, 13.83, -5.43, 0.07044, 50, 6.59, -5.43, 0.92956, 2, 49, 12.37, -8.48, 0.1551, 50, 5.13, -8.48, 0.8449, 3, 48, 19.1, -8.46, 0.00736, 49, 8.23, -8.46, 0.38516, 50, 0.99, -8.46, 0.60748, 3, 48, 14.65, -8.45, 0.11795, 49, 3.79, -8.45, 0.66648, 50, -3.45, -8.45, 0.21556, 3, 48, 9.18, -7.34, 0.62226, 49, -1.68, -7.34, 0.3669, 50, -8.93, -7.34, 0.01084, 3, 47, 21.96, -6.26, 0.02629, 48, 3.86, -6.26, 0.94885, 49, -7.01, -6.26, 0.02487, 2, 47, 16.21, -4, 0.75874, 48, -1.9, -4, 0.24126, 1, 47, 12.1, -2.39, 1, 1, 47, 7.24, -2.37, 1, 2, 46, 30.9, -2.35, 0.05269, 47, 1.93, -2.35, 0.94731, 1, 46, 24.58, -2.32, 1, 1, 46, 18.87, -2.3, 1, 1, 46, 12.14, -2.27, 1, 1, 46, 4.41, -2.24, 1, 2, 45, 45.11, -2.22, 0.96354, 46, -1.97, -2.22, 0.03646, 1, 45, 39.64, -2.2, 1, 1, 45, 33.52, -2.18, 1, 1, 45, 26.92, -2.15, 1, 1, 45, 22.25, -2.13, 1, 1, 45, 17.1, -2.11, 1, 1, 45, 12.43, -2.09, 1, 1, 45, 9.21, -2.08, 1, 1, 45, 5.02, -2.07, 1, 1, 45, -1.83, -2.04, 1, 2, 47, 18.06, 0.49, 0.50889, 48, -0.04, 0.49, 0.49111, 1, 48, 4.64, -0.34, 1, 2, 48, 10.15, -0.56, 0.794, 49, -0.71, -0.56, 0.206, 3, 48, 16.27, -2.42, 0.00544, 49, 5.4, -2.42, 0.81227, 50, -1.84, -2.42, 0.18228, 3, 48, 16.17, 2.69, 0.00607, 49, 5.3, 2.69, 0.8351, 50, -1.94, 2.69, 0.15883, 2, 49, 9.69, -2.23, 0.09914, 50, 2.44, -2.23, 0.90086, 2, 49, 9.71, 3.07, 0.13055, 50, 2.46, 3.07, 0.86945], "hull": 48, "edges": [0, 94, 44, 46, 46, 48, 48, 50, 92, 94, 0, 2, 92, 2, 90, 92, 2, 4, 90, 4, 88, 90, 4, 6, 88, 6, 86, 88, 6, 8, 86, 8, 84, 86, 8, 10, 84, 10, 82, 84, 10, 12, 82, 12, 80, 82, 12, 14, 80, 14, 78, 80, 14, 16, 78, 16, 76, 78, 16, 18, 76, 18, 74, 76, 18, 20, 74, 20, 72, 74, 20, 22, 72, 22, 70, 72, 22, 24, 70, 24, 68, 70, 24, 26, 68, 26, 66, 68, 26, 28, 66, 28, 62, 64, 64, 66, 28, 30, 30, 32, 64, 30, 58, 60, 60, 62, 60, 96, 32, 34, 96, 34, 58, 98, 34, 36, 36, 38, 98, 36, 54, 56, 56, 58, 56, 100, 100, 38, 54, 102, 38, 40, 40, 104, 104, 102, 50, 52, 52, 54, 52, 106, 106, 108, 40, 42, 42, 44, 108, 42], "width": 18, "height": 123}}, "yt1": {"yt1": {"type": "mesh", "uvs": [0.89113, 0.00435, 0.99467, 0.22841, 0.61848, 0.57084, 0.56636, 0.61829, 0.61828, 0.64377, 0.72753, 0.69741, 0.6684, 0.72608, 0.38929, 0.86143, 0.31573, 0.8971, 0.25678, 0.92569, 0.19762, 0.95437, 0.15277, 0.97612, 0.10352, 1, 0.01166, 1, 0, 0.98669, 0, 0.95236, 0, 0.92333, 0, 0.89429, 0.01986, 0.86835, 0.14109, 0.71, 0.16455, 0.67936, 0.20451, 0.62717, 0.2394, 0.58158, 0.36807, 0.08669, 0.4453, 0.00066, 0.38622, 0.60754, 0.4086, 0.57301, 0.38062, 0.65619, 0.417, 0.68679, 0.4198, 0.72524, 0.1344, 0.89037, 0.1316, 0.92176, 0.09803, 0.95315], "triangles": [0, 23, 24, 11, 12, 14, 12, 13, 14, 14, 32, 11, 14, 15, 32, 11, 32, 10, 32, 31, 10, 10, 31, 9, 31, 32, 16, 32, 15, 16, 8, 9, 30, 16, 17, 31, 9, 31, 30, 31, 17, 30, 8, 30, 7, 17, 18, 30, 30, 18, 7, 18, 19, 7, 7, 29, 6, 7, 19, 29, 5, 6, 28, 29, 19, 28, 19, 20, 28, 6, 29, 28, 28, 4, 5, 20, 27, 28, 28, 27, 4, 20, 21, 27, 27, 3, 4, 27, 21, 25, 27, 25, 3, 21, 22, 25, 25, 26, 3, 3, 26, 2, 25, 22, 26, 26, 22, 23, 1, 2, 26, 1, 26, 23, 23, 0, 1], "vertices": [1, 19, 280.29, -17.52, 1, 1, 25, 61.92, 38.77, 1, 1, 25, 187, 19.93, 1, 2, 25, 204.33, 17.32, 0.79118, 26, -13.89, 16.23, 0.20882, 2, 25, 212.46, 23.74, 0.36426, 26, -6.4, 23.39, 0.63574, 2, 25, 229.57, 37.25, 0.0367, 26, 9.34, 38.48, 0.9633, 2, 25, 240.45, 32.97, 0.00807, 26, 20.58, 35.26, 0.99193, 2, 26, 73.63, 20.04, 0.83598, 27, -6.95, 20.39, 0.16402, 2, 26, 87.61, 16.02, 0.23343, 27, 6.92, 16.01, 0.76657, 2, 26, 98.82, 12.81, 0.00953, 27, 18.03, 12.5, 0.99047, 1, 27, 29.19, 8.98, 1, 1, 27, 37.65, 6.31, 1, 1, 27, 46.93, 3.38, 1, 1, 27, 48.91, -5.5, 1, 1, 27, 44.57, -7.64, 1, 1, 27, 32.74, -10.27, 1, 1, 27, 22.74, -12.5, 1, 2, 26, 94.23, -14.54, 0.01293, 27, 12.73, -14.72, 0.98707, 2, 26, 84.87, -14.85, 0.20017, 27, 3.37, -14.79, 0.79983, 1, 26, 27.73, -16.77, 1, 2, 25, 231.56, -18.81, 0.00316, 26, 16.68, -17.14, 0.99684, 2, 25, 212.76, -17.64, 0.4689, 26, -2.16, -17.77, 0.5311, 2, 25, 196.33, -16.62, 0.96719, 26, -18.61, -18.32, 0.03281, 1, 25, 21.68, -30.02, 1, 2, 25, -9.49, -26.98, 0.288, 19, 321.4, -1.38, 0.712, 2, 25, 203.23, -0.88, 0.99567, 26, -13.24, -2, 0.00433, 1, 25, 190.85, -0.51, 1, 1, 26, 3.55, 1.63, 1, 2, 25, 230.44, 6.29, 0.0058, 26, 13.16, 7.75, 0.9942, 1, 26, 26.26, 11.31, 1, 1, 27, 8.49, -2.03, 1, 1, 27, 19.37, 0.1, 1, 1, 27, 30.91, -0.74, 1], "hull": 25, "edges": [0, 48, 0, 2, 24, 26, 26, 28, 46, 48, 44, 46, 40, 42, 6, 8, 8, 10, 2, 4, 44, 52, 52, 4, 50, 6, 42, 44, 4, 6, 42, 50, 40, 54, 54, 8, 40, 56, 56, 10, 38, 40, 38, 58, 10, 12, 58, 12, 34, 60, 60, 16, 32, 34, 32, 62, 16, 18, 62, 18, 28, 30, 30, 32, 30, 64, 18, 20, 64, 20, 20, 22, 22, 24, 34, 36, 36, 38, 12, 14, 14, 16], "width": 99, "height": 353}}, "zt1": {"zt1": {"type": "mesh", "uvs": [0.25593, 0, 0.34459, 0.05708, 0.45414, 0.1276, 0.5076, 0.16202, 0.6902, 0.27957, 0.70066, 0.46376, 0.7426, 0.56698, 0.83989, 0.59541, 0.92593, 0.62055, 0.97134, 0.63382, 0.92896, 0.68515, 0.90228, 0.71746, 0.85427, 0.77561, 0.92417, 0.83907, 0.95624, 0.88794, 0.97294, 0.91339, 0.99494, 0.94692, 0.98506, 0.96854, 0.97898, 0.98183, 0.97068, 1, 0.96076, 1, 0.91159, 0.98482, 0.85826, 0.96836, 0.80409, 0.95164, 0.74567, 0.93361, 0.71531, 0.8908, 0.64606, 0.79314, 0.62458, 0.76285, 0.59403, 0.71977, 0.56586, 0.68006, 0.53099, 0.63088, 0.48523, 0.56635, 0.43381, 0.49384, 0.0328, 0.1523, 0, 0.06289, 0, 0.05481, 0.18568, 0, 0.6294, 0.57907, 0.68211, 0.62084, 0.76596, 0.65175, 0.81148, 0.68433, 0.76357, 0.74196, 0.81148, 0.89096, 0.85221, 0.92604, 0.90732, 0.94776], "triangles": [36, 0, 1, 1, 34, 36, 2, 33, 1, 19, 20, 18, 20, 21, 18, 18, 21, 17, 44, 17, 21, 44, 21, 22, 17, 44, 16, 22, 23, 44, 23, 43, 44, 23, 24, 43, 16, 44, 15, 44, 43, 15, 24, 42, 43, 24, 25, 42, 43, 14, 15, 43, 42, 14, 14, 42, 13, 42, 25, 13, 13, 26, 12, 13, 25, 26, 26, 41, 12, 26, 27, 41, 12, 41, 11, 27, 28, 41, 41, 40, 11, 41, 28, 40, 40, 29, 39, 40, 28, 29, 11, 40, 10, 10, 40, 9, 40, 8, 9, 40, 39, 8, 29, 38, 39, 29, 30, 38, 39, 7, 8, 39, 38, 7, 30, 37, 38, 30, 31, 37, 38, 6, 7, 38, 37, 6, 6, 37, 5, 5, 37, 32, 37, 31, 32, 33, 4, 32, 32, 4, 5, 4, 33, 3, 3, 33, 2, 1, 33, 34, 34, 35, 36], "vertices": [1, 19, 304.84, -43.5, 1, 1, 19, 283.84, -26.83, 1, 1, 19, 257.89, -6.24, 1, 1, 28, 43.97, 22.9, 1, 1, 28, 97.43, 36.7, 1, 1, 28, 171.62, 19.81, 1, 2, 28, 214.44, 15.39, 0.99995, 29, -34.11, 16.04, 5e-05, 2, 28, 229.22, 26.15, 0.86669, 29, -19.49, 27, 0.13331, 2, 28, 242.29, 35.67, 0.57276, 29, -6.55, 36.7, 0.42724, 2, 28, 249.18, 40.7, 0.49194, 29, 0.28, 41.82, 0.50806, 2, 28, 268.28, 29.66, 0.18583, 29, 19.53, 31.04, 0.81417, 2, 28, 280.31, 22.71, 0.04088, 29, 31.64, 24.26, 0.95912, 1, 29, 53.45, 12.06, 1, 1, 29, 81.27, 15.88, 1, 2, 29, 101.97, 15.78, 0.48912, 30, 8.95, 15.14, 0.51088, 2, 29, 112.75, 15.73, 0.08708, 30, 18.74, 10.63, 0.91292, 1, 30, 31.65, 4.7, 1, 1, 30, 37.86, -1.87, 1, 1, 30, 41.68, -5.91, 1, 1, 30, 46.9, -11.43, 1, 1, 30, 46.03, -12.57, 1, 1, 30, 36.74, -14.36, 1, 1, 30, 26.67, -16.3, 1, 1, 30, 16.44, -18.27, 1, 1, 30, 5.41, -20.4, 1, 2, 29, 95.21, -18.27, 0.38093, 30, -11.27, -13.08, 0.61907, 1, 29, 53.67, -18.79, 1, 1, 29, 40.78, -18.95, 1, 1, 29, 22.46, -19.18, 1, 2, 28, 253.63, -20.57, 0.09201, 29, 5.56, -19.39, 0.90799, 2, 28, 232.71, -20.55, 0.81044, 29, -15.36, -19.65, 0.18956, 1, 28, 205.25, -20.51, 1, 1, 28, 174.4, -20.48, 1, 1, 28, 23.59, -42.49, 1, 1, 28, -13.39, -38.16, 1, 1, 28, -16.63, -37.36, 1, 1, 19, 314.14, -39.53, 1, 1, 28, 215.36, -1.63, 1, 1, 28, 233.93, 1.57, 1, 2, 28, 249.23, 10.21, 0.35602, 29, 0.75, 11.33, 0.64398, 2, 28, 263.87, 13.32, 0.09398, 29, 15.34, 14.65, 0.90602, 2, 28, 285.31, 0.89, 8e-05, 29, 36.95, 2.51, 0.99992, 2, 29, 98.43, -4.8, 0.20171, 30, -2.78, -2.15, 0.79829, 1, 30, 12.29, -6.33, 1, 1, 30, 24.23, -5.51, 1], "hull": 37, "edges": [0, 72, 8, 10, 10, 12, 24, 26, 38, 40, 68, 70, 70, 72, 66, 68, 64, 66, 62, 64, 60, 62, 12, 14, 58, 60, 18, 20, 14, 16, 16, 18, 26, 28, 48, 50, 50, 52, 46, 48, 44, 46, 40, 42, 42, 44, 28, 30, 30, 32, 36, 38, 32, 34, 34, 36, 56, 58, 62, 74, 74, 12, 60, 76, 76, 14, 58, 78, 78, 16, 56, 80, 80, 18, 52, 54, 54, 56, 54, 82, 20, 22, 22, 24, 82, 22, 50, 84, 84, 28, 48, 86, 86, 30, 46, 88, 88, 32, 0, 2, 2, 4, 4, 6, 6, 8], "width": 144, "height": 413}}, "t1": {"t1": {"type": "mesh", "uvs": [0.54867, 0.01712, 0.686, 0.08784, 0.77403, 0.23132, 0.80022, 0.24386, 0.88838, 0.24466, 0.93945, 0.34929, 0.99056, 0.45402, 0.99011, 0.57113, 0.95461, 0.62711, 0.92131, 0.67963, 0.84004, 0.75993, 0.65689, 0.7351, 0.58239, 0.8512, 0.453, 1, 0.4357, 1, 0.32118, 0.96141, 0.25438, 0.93148, 0.21581, 0.9142, 0.18863, 0.86683, 0.14685, 0.794, 0.0759, 0.67036, 0.0171, 0.51887, 0.02196, 0.3066, 0.08775, 0.16999, 0.22317, 0.06988, 0.32483, 0.02846, 0.20409, 0.15191, 0.30286, 0.19062, 0.41693, 0.30818, 0.52126, 0.42144, 0.59638, 0.5218, 0.63394, 0.62933, 0.69654, 0.70244, 0.36268, 0.11464, 0.48648, 0.22933, 0.56578, 0.33829, 0.66872, 0.43148, 0.71045, 0.49169, 0.75496, 0.57198, 0.76331, 0.62933, 0.8426, 0.658, 0.47953, 0.10173, 0.56578, 0.18775, 0.67984, 0.26947, 0.79252, 0.3813, 0.86208, 0.45729, 0.92328, 0.54474, 0.6395, 0.13041, 0.71184, 0.21499, 0.93441, 0.43148, 0.95806, 0.53757, 0.16236, 0.28381, 0.16375, 0.47592, 0.33625, 0.40567, 0.19018, 0.65083, 0.32094, 0.59492, 0.4837, 0.48596, 0.25139, 0.76983, 0.28895, 0.75119, 0.45588, 0.6537, 0.26808, 0.83147, 0.28895, 0.81714, 0.50596, 0.72395, 0.32373, 0.89312, 0.37102, 0.88309, 0.44614, 0.83721, 0.41693, 0.92896], "triangles": [41, 25, 0, 33, 25, 41, 24, 25, 33, 47, 0, 1, 41, 0, 47, 27, 26, 24, 26, 23, 24, 42, 41, 47, 33, 27, 24, 2, 48, 1, 47, 1, 48, 34, 41, 42, 33, 41, 34, 27, 33, 34, 43, 47, 48, 42, 47, 43, 51, 23, 26, 51, 26, 27, 22, 23, 51, 28, 27, 34, 35, 42, 43, 34, 42, 35, 28, 34, 35, 44, 2, 3, 43, 48, 2, 44, 43, 2, 53, 27, 28, 51, 27, 53, 29, 28, 35, 53, 28, 29, 36, 35, 43, 36, 43, 44, 29, 35, 36, 3, 4, 44, 49, 5, 6, 44, 4, 5, 49, 45, 5, 45, 44, 5, 52, 51, 53, 22, 51, 52, 56, 53, 29, 37, 36, 44, 37, 44, 45, 21, 22, 52, 30, 29, 36, 30, 36, 37, 56, 29, 30, 50, 49, 6, 46, 45, 49, 50, 46, 49, 7, 50, 6, 38, 37, 45, 38, 45, 46, 55, 52, 53, 55, 53, 56, 7, 8, 46, 7, 46, 50, 31, 30, 37, 31, 37, 38, 40, 39, 38, 46, 40, 38, 31, 38, 39, 54, 52, 55, 20, 21, 52, 59, 55, 56, 59, 56, 30, 59, 30, 31, 8, 40, 46, 54, 20, 52, 9, 40, 8, 32, 31, 39, 62, 59, 31, 11, 31, 32, 62, 31, 11, 58, 54, 55, 58, 55, 59, 10, 39, 40, 10, 40, 9, 32, 39, 10, 11, 32, 10, 57, 54, 58, 19, 20, 54, 19, 54, 57, 62, 61, 58, 57, 58, 61, 62, 58, 59, 60, 57, 61, 65, 61, 62, 65, 62, 11, 12, 65, 11, 18, 19, 57, 18, 57, 60, 64, 61, 65, 63, 61, 64, 60, 61, 63, 17, 18, 60, 16, 17, 60, 66, 64, 65, 66, 65, 12, 63, 16, 60, 15, 16, 63, 15, 63, 64, 15, 64, 66, 13, 14, 66, 15, 66, 14, 12, 13, 66], "vertices": [1, 5, 132.39, -93.12, 1, 1, 5, 104.88, -107.43, 1, 1, 5, 71.68, -105.31, 1, 1, 5, 66.58, -108.15, 1, 1, 5, 55.99, -122.47, 1, 1, 5, 33.34, -118.77, 1, 1, 5, 10.66, -115.07, 1, 1, 5, -7.86, -101.52, 1, 1, 5, -12.53, -89.27, 1, 1, 5, -16.91, -77.78, 1, 1, 5, -20.01, -55.25, 1, 1, 5, 5.66, -28.16, 1, 1, 5, -3.92, -2.62, 1, 1, 5, -12.17, 35.67, 1, 1, 5, -10.12, 38.49, 1, 1, 5, 9.59, 52.77, 1, 1, 5, 22.26, 60.25, 1, 1, 5, 29.58, 64.56, 1, 1, 5, 40.32, 63.55, 1, 1, 5, 56.83, 62, 1, 1, 5, 84.86, 59.37, 1, 1, 5, 115.87, 51.54, 1, 1, 5, 148.96, 26.31, 1, 1, 5, 162.83, -0.17, 1, 1, 5, 162.64, -33.84, 1, 1, 5, 157.15, -55.22, 1, 1, 5, 151.89, -21.27, 1, 1, 5, 134.03, -32.96, 1, 1, 5, 101.85, -38.08, 1, 1, 5, 71.51, -42.1, 1, 1, 5, 46.67, -42.83, 1, 1, 5, 25.16, -36.59, 1, 1, 5, 6.14, -38.4, 1, 1, 5, 138.99, -51.49, 1, 1, 5, 106.11, -58.53, 1, 1, 5, 79.42, -58.95, 1, 1, 5, 52.42, -65.05, 1, 1, 5, 37.92, -64.94, 1, 1, 5, 19.9, -62.98, 1, 1, 5, 9.81, -57.74, 1, 1, 5, -4.14, -67.4, 1, 1, 5, 127.17, -72.08, 1, 1, 5, 103.29, -76.28, 1, 1, 5, 76.8, -85.52, 1, 1, 5, 45.69, -91.07, 1, 1, 5, 25.39, -93.69, 1, 1, 5, 4.25, -93.63, 1, 1, 5, 103.64, -94.93, 1, 1, 5, 81.64, -97.02, 1, 1, 5, 20.9, -108.49, 1, 1, 5, 1.26, -100.14, 1, 1, 5, 135.92, 0.73, 1, 1, 5, 105.28, 22.62, 1, 1, 5, 95.96, -13.67, 1, 1, 5, 74.4, 38.44, 1, 1, 5, 67.75, 10.62, 1, 1, 5, 65.73, -28.53, 1, 1, 5, 48.26, 42.13, 1, 1, 5, 46.76, 33.84, 1, 1, 5, 42.42, -4.67, 1, 1, 5, 36.5, 46.5, 1, 1, 5, 36.3, 41.43, 1, 1, 5, 25.34, -4.77, 1, 1, 5, 20.12, 44.5, 1, 1, 5, 16.1, 35.61, 1, 1, 5, 14.47, 18.05, 1, 1, 5, 3.38, 33.38, 1], "hull": 26, "edges": [0, 50, 0, 2, 2, 4, 4, 6, 6, 8, 12, 14, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 46, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 20, 48, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 18, 50, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 14, 16, 16, 18, 92, 16, 0, 94, 94, 96, 96, 4, 8, 10, 10, 12, 10, 98, 98, 100, 100, 14, 44, 102, 102, 54, 42, 104, 104, 106, 106, 56, 40, 108, 108, 110, 110, 112, 112, 58, 38, 40, 38, 114, 114, 116, 116, 118, 118, 60, 34, 36, 36, 38, 36, 120, 120, 122, 122, 124, 124, 62, 30, 32, 32, 34, 32, 126, 126, 128, 128, 130, 130, 22, 30, 132, 132, 24], "width": 202, "height": 196}}, "t2": {"t2": {"type": "mesh", "uvs": [0.77623, 0.04787, 0.90118, 0.16257, 0.96189, 0.2183, 0.97393, 0.34634, 0.9859, 0.47376, 0.99509, 0.57154, 0.97026, 0.63771, 0.94493, 0.70518, 0.84857, 0.79432, 0.76541, 0.87123, 0.56533, 0.96493, 0.46336, 0.97862, 0.3338, 0.9787, 0.17954, 0.97879, 0.12419, 0.94818, 0.04864, 0.81695, 0.04908, 0.64568, 0.04925, 0.57974, 0.05555, 0.48027, 0.06447, 0.33951, 0.15308, 0.18268, 0.35422, 0.02124, 0.68361, 0.02137, 0.36418, 0.2013, 0.64359, 0.20659, 0.30344, 0.37235, 0.65371, 0.37235, 0.27712, 0.51166, 0.67801, 0.50108, 0.26295, 0.66861, 0.67598, 0.67037, 0.27914, 0.80792, 0.64764, 0.81674, 0.30749, 0.92078, 0.5788, 0.90315], "triangles": [24, 0, 1, 26, 24, 1, 26, 1, 2, 26, 2, 3, 23, 24, 26, 28, 26, 3, 4, 28, 3, 28, 25, 26, 28, 4, 5, 6, 28, 5, 30, 27, 28, 30, 28, 6, 7, 30, 6, 8, 30, 7, 30, 32, 29, 32, 30, 8, 9, 32, 8, 34, 32, 9, 32, 31, 29, 15, 16, 29, 15, 29, 31, 34, 31, 32, 33, 31, 34, 14, 15, 31, 14, 31, 33, 34, 11, 33, 10, 34, 9, 10, 11, 34, 12, 33, 11, 13, 14, 33, 13, 33, 12, 27, 18, 25, 28, 27, 25, 17, 18, 27, 29, 17, 27, 16, 17, 29, 29, 27, 30, 24, 23, 21, 20, 21, 23, 22, 24, 21, 24, 22, 0, 25, 20, 23, 19, 20, 25, 25, 23, 26, 18, 19, 25], "vertices": [2, 6, -4.95, 19.79, 0.44068, 62, -21.13, 13.85, 0.55932, 2, 6, 3.9, 31.53, 0.08045, 62, -8.71, 21.71, 0.91955, 2, 6, 8.19, 37.24, 0.01728, 62, -2.68, 25.54, 0.98272, 1, 62, 9.2, 24.19, 1, 1, 62, 21.01, 22.85, 1, 1, 62, 30.08, 21.83, 1, 1, 62, 35.72, 18.66, 1, 1, 62, 41.48, 15.44, 1, 2, 64, 2.82, 37.05, 0.04364, 62, 48.11, 6.18, 0.95636, 3, 63, 34.88, 31.59, 0.00295, 64, 10.99, 31.59, 0.21851, 62, 53.83, -1.81, 0.77854, 3, 63, 46.15, 17.04, 0.00073, 64, 22.26, 17.04, 0.74137, 62, 59.24, -19.4, 0.2579, 2, 64, 24.89, 9.11, 0.89687, 62, 58.9, -27.75, 0.10313, 2, 64, 26.63, -1.24, 0.99121, 62, 56.88, -38.05, 0.00879, 2, 63, 52.59, -13.56, 0.00729, 64, 28.7, -13.56, 0.99271, 2, 63, 50.53, -18.45, 0.0231, 64, 26.64, -18.45, 0.9769, 3, 6, 75.34, -26.51, 0.00062, 63, 39.5, -26.51, 0.16185, 64, 15.61, -26.51, 0.83754, 3, 6, 59.62, -29.11, 0.04898, 63, 23.79, -29.11, 0.50398, 64, -0.1, -29.11, 0.44703, 3, 6, 53.57, -30.11, 0.10986, 63, 17.74, -30.11, 0.60578, 64, -6.15, -30.11, 0.28436, 3, 6, 44.36, -31.13, 0.26621, 63, 8.53, -31.13, 0.62138, 64, -15.36, -31.13, 0.1124, 3, 6, 31.33, -32.59, 0.54182, 63, -4.5, -32.59, 0.4414, 64, -28.39, -32.59, 0.01678, 2, 6, 15.76, -27.92, 0.82626, 63, -20.07, -27.92, 0.17374, 2, 6, -1.74, -14.34, 0.99478, 63, -37.57, -14.34, 0.00522, 2, 6, -6.14, 11.98, 0.66816, 62, -25, 6.96, 0.33184, 2, 6, 14.64, -10.77, 0.96211, 63, -21.19, -10.77, 0.03789, 2, 6, 11.38, 11.63, 0.41044, 62, -8.73, 0.45, 0.58956, 3, 6, 31.14, -12.99, 0.65407, 63, -4.69, -12.99, 0.34381, 64, -28.58, -12.99, 0.00212, 3, 6, 26.45, 14.99, 0.04271, 63, -9.38, 14.99, 0.00296, 62, 6.56, -1.72, 0.95433, 3, 6, 44.27, -12.95, 0.15873, 63, 8.44, -12.95, 0.79127, 64, -15.45, -12.95, 0.05, 3, 6, 37.93, 18.91, 0.00639, 63, 2.1, 18.91, 0.04207, 62, 18.68, -2.11, 0.95154, 3, 6, 58.86, -11.67, 0.01012, 63, 23.03, -11.67, 0.52891, 64, -0.86, -11.67, 0.46097, 3, 63, 17.65, 21.35, 0.10977, 64, -6.23, 21.35, 0.03356, 62, 34.1, -5.31, 0.85667, 2, 63, 35.59, -8.23, 0.04939, 64, 11.7, -8.23, 0.95061, 3, 63, 31.46, 21.34, 0.05184, 64, 7.57, 21.34, 0.34618, 62, 47.01, -10.19, 0.60198, 3, 63, 45.56, -4.23, 0.00011, 64, 21.67, -4.23, 0.99801, 62, 51.18, -39.1, 0.00189, 3, 63, 40.31, 17.17, 0.01065, 64, 16.42, 17.17, 0.65866, 62, 53.82, -17.22, 0.33069], "hull": 23, "edges": [0, 44, 18, 20, 20, 22, 26, 28, 28, 30, 38, 40, 40, 42, 42, 44, 40, 46, 46, 48, 0, 2, 2, 4, 48, 2, 38, 50, 50, 52, 4, 6, 52, 6, 34, 36, 36, 38, 36, 54, 54, 56, 6, 8, 8, 10, 56, 8, 30, 32, 32, 34, 32, 58, 58, 60, 10, 12, 12, 14, 60, 12, 30, 62, 62, 64, 14, 16, 16, 18, 64, 16, 28, 66, 66, 68, 68, 18, 22, 24, 24, 26], "width": 81, "height": 93}}, "t3": {"t3": {"type": "mesh", "uvs": [0.50027, 0.00467, 0.74314, 0.0911, 0.73226, 0.21112, 0.46995, 0.18959, 0.68099, 0.28244, 0.82225, 0.3446, 0.91503, 0.38542, 0.95005, 0.47226, 0.98573, 0.56076, 0.98476, 0.65075, 0.91151, 0.70838, 0.82011, 0.7803, 0.71659, 0.86176, 0.64508, 0.91802, 0.56484, 0.98115, 0.45253, 0.98538, 0.33449, 0.98982, 0.22472, 0.99395, 0.12353, 0.99775, 0.04527, 0.94774, 0.01503, 0.95598, 0.00485, 0.861, 0.10947, 0.83814, 0.39243, 0.81245, 0.47992, 0.74433, 0.51886, 0.64607, 0.45161, 0.55538, 0.26107, 0.48601, 0.12004, 0.38456, 0.12586, 0.25544, 0.13254, 0.10705, 0.16679, 0.11972, 0.19179, 0.14775, 0.39687, 0.00452, 0.31758, 0.20432, 0.17765, 0.23759, 0.49779, 0.26447, 0.61535, 0.36665, 0.61703, 0.4876, 0.3668, 0.30201, 0.47764, 0.38959, 0.27779, 0.35414, 0.37856, 0.45319, 0.72787, 0.41253, 0.69092, 0.52305, 0.60023, 0.59395, 0.8471, 0.43063, 0.89077, 0.50779, 0.87901, 0.59641, 0.80848, 0.69025, 0.67917, 0.75907, 0.53138, 0.79348, 0.79672, 0.53698, 0.79001, 0.61935, 0.71611, 0.67566, 0.56329, 0.72049, 0.51459, 0.85208, 0.64894, 0.84791, 0.42894, 0.899, 0.54817, 0.91569, 0.19886, 0.92194, 0.33657, 0.94279, 0.12665, 0.93862], "triangles": [29, 35, 41, 32, 29, 30, 35, 29, 32, 21, 22, 62, 60, 22, 23, 23, 24, 51, 25, 26, 45, 27, 28, 41, 27, 42, 26, 25, 45, 54, 24, 25, 55, 18, 62, 17, 18, 19, 62, 62, 60, 17, 16, 17, 61, 16, 61, 15, 61, 17, 60, 61, 58, 15, 15, 59, 14, 15, 58, 59, 14, 59, 13, 20, 21, 19, 19, 21, 62, 58, 61, 23, 62, 22, 60, 61, 60, 23, 13, 57, 12, 13, 59, 57, 58, 56, 59, 59, 56, 57, 58, 23, 56, 12, 57, 11, 56, 51, 57, 56, 23, 51, 57, 50, 11, 57, 51, 50, 51, 55, 50, 51, 24, 55, 50, 49, 11, 11, 49, 10, 50, 54, 49, 50, 55, 54, 55, 25, 54, 45, 26, 38, 9, 10, 48, 54, 53, 49, 10, 49, 48, 49, 53, 48, 54, 45, 53, 9, 48, 8, 45, 44, 53, 53, 52, 48, 53, 44, 52, 48, 47, 8, 48, 52, 47, 45, 38, 44, 47, 7, 8, 47, 52, 46, 52, 44, 43, 47, 46, 7, 46, 6, 7, 38, 26, 40, 46, 52, 43, 44, 38, 43, 26, 42, 40, 40, 37, 38, 38, 37, 43, 43, 5, 46, 46, 5, 6, 5, 43, 4, 40, 36, 37, 43, 37, 4, 37, 36, 4, 4, 36, 3, 27, 41, 42, 42, 41, 40, 41, 39, 40, 40, 39, 36, 41, 35, 39, 35, 34, 39, 39, 34, 36, 34, 3, 36, 35, 32, 34, 31, 32, 30, 2, 3, 1, 3, 34, 33, 34, 32, 33, 3, 0, 1, 3, 33, 0, 28, 29, 41], "vertices": [1, 12, 36.52, 70.26, 1, 1, 12, 69.3, 53.82, 1, 1, 12, 69.28, 28.45, 1, 2, 12, 34.72, 31.08, 0.89523, 13, -23.39, 23.57, 0.10477, 2, 12, 63.41, 13.05, 0.02277, 13, 10.41, 25.98, 0.97723, 2, 13, 33.03, 27.59, 0.8912, 14, -33.32, 19.67, 0.1088, 2, 13, 47.89, 28.65, 0.65334, 14, -21.9, 29.24, 0.34666, 2, 13, 63.06, 17.4, 0.24839, 14, -3.01, 29.01, 0.75161, 2, 13, 78.53, 5.94, 0.01601, 14, 16.24, 28.78, 0.98399, 2, 14, 34.57, 23.82, 0.91413, 15, -25.89, 18.31, 0.08587, 2, 14, 43.89, 11.45, 0.51587, 15, -10.44, 17.13, 0.48413, 2, 14, 55.52, -3.99, 0.00237, 15, 8.83, 15.67, 0.99763, 1, 15, 30.66, 14.01, 1, 1, 15, 45.74, 12.86, 1, 1, 15, 62.66, 11.58, 1, 1, 15, 71.61, -0.13, 1, 1, 15, 81.02, -12.44, 1, 1, 15, 89.77, -23.88, 1, 1, 15, 97.83, -34.43, 1, 1, 15, 94.8, -48.83, 1, 1, 15, 98.46, -51.15, 1, 1, 5, 9.99, -26.29, 1, 1, 5, 5.85, -40.22, 1, 1, 5, -11.54, -73.4, 1, 1, 5, -6.64, -91.12, 1, 1, 5, 7.15, -107.43, 1, 5, 12, 36.6, -46.11, 0.01457, 13, 23.74, -37.6, 0.03254, 14, -2.66, -38.6, 0.02626, 15, -3.6, -50.87, 0.00664, 5, 27.81, -111.54, 0.92, 1, 5, 54.31, -99.93, 1, 4, 12, -8.76, -12.54, 0.01593, 13, -32.7, -37.32, 6e-05, 14, -48.57, -71.43, 1e-05, 5, 82.49, -97.55, 0.984, 1, 7, 26.35, -30.03, 1, 2, 12, -10.38, 46.02, 0.064, 7, 57.67, -29.4, 0.936, 1, 12, -5.75, 43.6, 1, 1, 12, -2.15, 37.88, 1, 1, 12, 23, 69.54, 1, 2, 12, 14.96, 26.87, 0.98967, 13, -36.85, 8.5, 0.01033, 2, 12, -2.95, 18.85, 0.272, 7, 30.44, -36.63, 0.728, 2, 12, 39.24, 15.51, 0.54065, 13, -10.55, 13.67, 0.45935, 1, 13, 15.04, 6.79, 1, 4, 12, 57.45, -30.63, 0.02834, 13, 31.41, -12.79, 0.64815, 14, -10.98, -14, 0.30857, 15, -27.56, -40.88, 0.01494, 2, 12, 22.55, 6.65, 0.99597, 13, -18.78, -3.34, 0.00403, 4, 12, 38.07, -11, 0.25276, 13, 4.17, -8.4, 0.73595, 14, -35.63, -26.4, 0.01092, 15, -34.53, -67.57, 0.00037, 3, 12, 11.51, -4.98, 0.98921, 13, -20.81, -19.24, 0.00967, 14, -49.52, -49.81, 0.00112, 4, 12, 25.85, -25.12, 0.58068, 13, 2.66, -27.01, 0.35428, 14, -25.95, -42.37, 0.06048, 15, -16.15, -70.85, 0.00456, 2, 13, 32.57, 8.66, 0.94564, 14, -22.6, 4.07, 0.05436, 4, 12, 67.53, -37.56, 0.00534, 13, 43.64, -12.43, 0.16864, 14, -1.28, -6.55, 0.81397, 15, -26.76, -28.67, 0.01204, 4, 12, 56.49, -53.16, 0.02453, 13, 43.95, -31.53, 0.14003, 14, 10.16, -21.84, 0.61628, 15, -7.72, -30.18, 0.21916, 2, 13, 47.07, 15.63, 0.57209, 14, -14.93, 18.2, 0.42791, 2, 13, 61.82, 6.68, 0.12571, 14, 2.27, 19.59, 0.87429, 3, 13, 72.5, -8.75, 0.0002, 14, 19.96, 13.34, 0.99837, 15, -27.67, 0.41, 0.00143, 2, 14, 36.75, -0.63, 0.33659, 15, -6.08, 3.8, 0.66341, 3, 13, 74.06, -51.89, 0.00013, 14, 46.49, -20.71, 0.00471, 15, 15.42, -2.15, 0.99516, 3, 13, 63.7, -69.79, 0.00016, 14, 48.58, -41.28, 0.01127, 15, 32.25, -14.16, 0.98857, 2, 13, 56.21, -5.9, 0.00919, 14, 5.09, 6.11, 0.99081, 1, 14, 21.67, 0.84, 1, 4, 12, 72.61, -69.53, 0.00031, 13, 66.62, -35.22, 0.00281, 14, 30.7, -11.55, 0.3295, 15, -1.89, -7.96, 0.66738, 4, 12, 53.14, -80.09, 0.001, 13, 57.16, -55.23, 0.00938, 14, 34.75, -33.32, 0.11856, 15, 17.14, -19.29, 0.87106, 2, 14, 59.98, -46.55, 9e-05, 15, 43.74, -9.09, 0.99991, 1, 15, 33.18, 5.03, 1, 1, 15, 58.21, -12.87, 1, 1, 15, 52.42, 2.06, 1, 1, 15, 79.05, -35.17, 1, 1, 15, 72.64, -17.75, 1, 1, 15, 87.25, -41.06, 1], "hull": 34, "edges": [0, 66, 0, 2, 2, 4, 4, 6, 16, 18, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 60, 62, 62, 64, 64, 66, 6, 68, 68, 70, 56, 58, 58, 60, 70, 58, 68, 72, 72, 74, 74, 76, 76, 52, 70, 78, 78, 80, 80, 52, 58, 82, 82, 84, 84, 52, 6, 8, 8, 86, 86, 88, 88, 90, 90, 50, 86, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 46, 88, 104, 104, 106, 106, 108, 108, 110, 110, 48, 8, 10, 10, 12, 12, 14, 14, 16, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 46, 112, 112, 114, 114, 22, 46, 116, 116, 118, 118, 26, 116, 30, 44, 120, 120, 122, 122, 116, 42, 124, 124, 34], "width": 131, "height": 211}}}}], "animations": {"animation": {"bones": {"st2": {"rotate": [{"angle": 0.03, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 1.2, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 2.5, "angle": 0.03, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 2.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "angle": 1.2, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 5, "angle": 0.03}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 3.6, "y": -2.66, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 3.6, "y": -2.66, "curve": 0.25, "c3": 0.75}, {"time": 5}]}, "st4": {"translate": [{"x": -2.86, "y": -2.8, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": -1.04, "y": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": -2.86, "y": -2.8, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "x": -1.04, "y": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": -2.86, "y": -2.8}]}, "st5": {"translate": [{"x": 0.42, "y": -0.08, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": 4.62, "y": -0.84, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 2.5, "x": 0.42, "y": -0.08, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "x": 4.62, "y": -0.84, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 5, "x": 0.42, "y": -0.08}]}, "zs1": {"rotate": [{"angle": 0.32, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 1.2, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 2.5, "angle": 0.32, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 2.9333, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": 1.2, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 5, "angle": 0.32}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": -2, "y": 5.66, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "x": -2, "y": 5.66, "curve": 0.25, "c3": 0.75}, {"time": 5}]}, "zs2": {"rotate": [{"angle": -1.15, "curve": 0.333, "c2": 0.33, "c3": 0.678, "c4": 0.7}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": -2.4, "curve": 0.257, "c3": 0.619, "c4": 0.46}, {"time": 2.5, "angle": -1.15, "curve": 0.333, "c2": 0.33, "c3": 0.678, "c4": 0.7}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.2333, "angle": -2.4, "curve": 0.257, "c3": 0.619, "c4": 0.46}, {"time": 5, "angle": -1.15}]}, "zs4": {"rotate": [{"angle": 0.88, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": 2.4, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.5, "angle": 0.88, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.2333, "angle": 2.4, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5, "angle": 0.88}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": -3.58, "y": 5.22, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": -3.58, "y": 5.22, "curve": 0.25, "c3": 0.75}, {"time": 5}], "scale": [{"x": 0.996, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "x": 0.98, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 2.5, "x": 0.996, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 2.8667, "curve": 0.25, "c3": 0.75}, {"time": 4.1, "x": 0.98, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 5, "x": 0.996}]}, "ys1": {"rotate": [{"angle": -1.7, "curve": 0.339, "c2": 0.35, "c3": 0.676, "c4": 0.7}, {"time": 0.2, "angle": -0.96, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": -3.6, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 2.5, "angle": -1.7, "curve": 0.339, "c2": 0.35, "c3": 0.676, "c4": 0.7}, {"time": 2.7, "angle": -0.96, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 3.0667, "curve": 0.25, "c3": 0.75}, {"time": 4.4, "angle": -3.6, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 5, "angle": -1.7}], "translate": [{"x": -3.63, "y": 0.23, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.2, "x": -3.99, "y": 0.25, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "curve": 0.243, "c3": 0.692, "c4": 0.76}, {"time": 2.5, "x": -3.63, "y": 0.23, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 2.7, "x": -3.99, "y": 0.25, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "curve": 0.243, "c3": 0.692, "c4": 0.76}, {"time": 5, "x": -3.63, "y": 0.23}]}, "ys2": {"rotate": [{"angle": 2.89, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 10.8, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 2.5, "angle": 2.89, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 2.9333, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": 10.8, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 5, "angle": 2.89}]}, "qunz1": {"rotate": [{"angle": 0.05, "curve": 0.35, "c2": 0.65, "c3": 0.684}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": 3.6, "curve": 0.247, "c3": 0.727, "c4": 0.9}, {"time": 2.5, "angle": 0.05, "curve": 0.35, "c2": 0.65, "c3": 0.684}, {"time": 2.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.8, "angle": 3.6, "curve": 0.247, "c3": 0.727, "c4": 0.9}, {"time": 5, "angle": 0.05}], "translate": [{"x": -0.02, "y": 0.03, "curve": 0.35, "c2": 0.65, "c3": 0.684}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "x": -0.91, "y": 1.79, "curve": 0.247, "c3": 0.727, "c4": 0.9}, {"time": 2.5, "x": -0.02, "y": 0.03, "curve": 0.35, "c2": 0.65, "c3": 0.684}, {"time": 2.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.8, "x": -0.91, "y": 1.79, "curve": 0.247, "c3": 0.727, "c4": 0.9}, {"time": 5, "x": -0.02, "y": 0.03}]}, "zt1": {"rotate": [{"angle": -14.4, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -14.4, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "angle": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -14.4}]}, "st1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 5}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": 2, "y": -2, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "x": 2, "y": -2, "curve": 0.25, "c3": 0.75}, {"time": 5}]}, "yt1": {"rotate": [{"angle": 16.14, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 16.14, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 16.14}]}, "boz1": {"rotate": [{"angle": -0.21, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -2.4, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 2.5, "angle": -0.21, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "angle": -2.4, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 5, "angle": -0.21}], "translate": [{"x": -0.03, "y": -0.03, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": 1.07, "y": -4.59, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 2.5, "x": -0.03, "y": -0.03, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "x": 1.07, "y": -4.59, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 5, "x": -0.03, "y": -0.03}]}, "ys4": {"rotate": [{"angle": 0.5, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 0.4, "angle": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": 15.6, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 2.5, "angle": 0.5, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 2.9, "angle": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "angle": 15.6, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 5, "angle": 0.5}], "shear": [{"curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "x": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 5}]}, "t1": {"rotate": [{"angle": -0.42, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -2.4, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 2.5, "angle": -0.42, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 2.8, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "angle": -2.4, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 5, "angle": -0.42}]}, "st3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 5}]}, "st9": {"rotate": [{"angle": -0.16, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2333, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 1.4333, "angle": -1.17, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 1.5, "angle": -1.2, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.5, "angle": -0.16, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.7333, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 3.9333, "angle": -1.17, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 4, "angle": -1.2, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5, "angle": -0.16}], "translate": [{"x": 1.65, "y": 1.83, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2333, "x": 1.9, "y": 2.1, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 1.4333, "x": 0.11, "y": 0.06, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 1.5, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.5, "x": 1.65, "y": 1.83, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.7333, "x": 1.9, "y": 2.1, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 3.9333, "x": 0.11, "y": 0.06, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 4, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5, "x": 1.65, "y": 1.83}], "scale": [{"x": 1.033, "y": 1.033, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.3, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 2.5, "x": 1.033, "y": 1.033, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 2.8, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 5, "x": 1.033, "y": 1.033}]}, "t6": {"rotate": [{"angle": -0.16, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -1.26, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.5, "angle": -0.16, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.7333, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -1.26, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5, "angle": -0.16}], "scale": [{"x": 0.992, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 0.94, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.5, "x": 0.992, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.7333, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 0.94, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5, "x": 0.992}], "shear": [{"y": 0.46, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "y": 4.74, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.5, "y": 0.46, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.7333, "curve": 0.25, "c3": 0.75}, {"time": 4, "y": 4.74, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5, "y": 0.46}]}, "t7": {"rotate": [{"angle": -0.4, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": -1.26, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 2.5, "angle": -0.4, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 2.9667, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "angle": -1.26, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 5, "angle": -0.4}], "shear": [{"y": 1.12, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "y": 3.54, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 2.5, "y": 1.12, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 2.9667, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "y": 3.54, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 5, "y": 1.12}]}, "t8": {"rotate": [{"angle": -0.73, "curve": 0.368, "c2": 0.47, "c3": 0.753}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": -1.26, "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 2.5, "angle": -0.73, "curve": 0.368, "c2": 0.47, "c3": 0.753}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 4.4667, "angle": -1.26, "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 5, "angle": -0.73}], "shear": [{"y": 2.05, "curve": 0.368, "c2": 0.47, "c3": 0.753}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "y": 3.54, "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 2.5, "y": 2.05, "curve": 0.368, "c2": 0.47, "c3": 0.753}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 4.4667, "y": 3.54, "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 5, "y": 2.05}]}, "t9": {"rotate": [{"angle": 0.06, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 0.7, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 2.5, "angle": 0.06, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "angle": 0.7, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 5, "angle": 0.06}], "shear": [{"x": 0.11, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": 1.2, "y": 1.2, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 2.5, "x": 0.11, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "x": 1.2, "y": 1.2, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 5, "x": 0.11}]}, "t10": {"rotate": [{"angle": 0.19, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 0.7, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 2.5, "angle": 0.19, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 2.9333, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": 0.7, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 5, "angle": 0.19}], "shear": [{"x": 0.32, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.2, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 2.5, "x": 0.32, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 2.9333, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "x": 1.2, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 5, "x": 0.32}]}, "t11": {"rotate": [{"angle": 0.37, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": 0.7, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 2.5, "angle": 0.37, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "angle": 0.7, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 5, "angle": 0.37}], "shear": [{"x": 0.63, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "x": 1.2, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 2.5, "x": 0.63, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "x": 1.2, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 5, "x": 0.63}]}, "t12": {"rotate": [{"angle": 0.52, "curve": 0.349, "c2": 0.39, "c3": 0.757}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "angle": 0.7, "curve": 0.266, "c3": 0.618, "c4": 0.42}, {"time": 2.5, "angle": 0.52, "curve": 0.349, "c2": 0.39, "c3": 0.757}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6, "angle": 0.7, "curve": 0.266, "c3": 0.618, "c4": 0.42}, {"time": 5, "angle": 0.52}], "shear": [{"x": 0.88, "curve": 0.349, "c2": 0.39, "c3": 0.757}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "x": 1.2, "curve": 0.266, "c3": 0.618, "c4": 0.42}, {"time": 2.5, "x": 0.88, "curve": 0.349, "c2": 0.39, "c3": 0.757}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6, "x": 1.2, "curve": 0.266, "c3": 0.618, "c4": 0.42}, {"time": 5, "x": 0.88}]}, "t5": {"rotate": [{"angle": -0.58, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -4.45, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.5, "angle": -0.58, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.7333, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -4.45, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5, "angle": -0.58}], "shear": [{"y": -0.62, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "y": -4.8, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.5, "y": -0.62, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.7333, "curve": 0.25, "c3": 0.75}, {"time": 4, "y": -4.8, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5, "y": -0.62}]}, "t18": {"rotate": [{"angle": -1.19, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": -3.25, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.5, "angle": -1.19, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.2333, "angle": -3.25, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5, "angle": -1.19}], "shear": [{"y": -1.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "y": -4.8, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.5, "y": -1.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.2333, "y": -4.8, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5, "y": -1.77}]}, "t19": {"rotate": [{"angle": -1.29, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -2.05, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.5, "angle": -1.29, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.2333, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -2.05, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5, "angle": -1.29}], "shear": [{"y": -3.03, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 2, "y": -4.8, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.5, "y": -3.03, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.2333, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "y": -4.8, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5, "y": -3.03}]}, "t20": {"rotate": [{"angle": -0.74, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": -0.85, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.5, "angle": -0.74, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 4.7333, "angle": -0.85, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5, "angle": -0.74}], "shear": [{"y": -4.18, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "y": -4.8, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.5, "y": -4.18, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 4.7333, "y": -4.8, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5, "y": -4.18}]}, "t4": {"rotate": [{"angle": -1.84, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 0.1667, "angle": -1.02, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": -5.85, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 2.5, "angle": -1.84, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 2.6667, "angle": -1.02, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 2.9667, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "angle": -5.85, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 5, "angle": -1.84}], "shear": [{"y": -1.13, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 0.1667, "y": -0.62, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "y": -3.6, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 2.5, "y": -1.13, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 2.6667, "y": -0.62, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 2.9667, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "y": -3.6, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 5, "y": -1.13}]}, "t15": {"rotate": [{"angle": -3.7, "curve": 0.33, "c2": 0.32, "c3": 0.666, "c4": 0.66}, {"time": 0.1667, "angle": -2.77, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -5.85, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.5, "angle": -3.7, "curve": 0.33, "c2": 0.32, "c3": 0.666, "c4": 0.66}, {"time": 2.6667, "angle": -2.77, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 3.2333, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -5.85, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5, "angle": -3.7}], "shear": [{"y": -2.28, "curve": 0.33, "c2": 0.32, "c3": 0.666, "c4": 0.66}, {"time": 0.1667, "y": -1.7, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 2, "y": -3.6, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.5, "y": -2.28, "curve": 0.33, "c2": 0.32, "c3": 0.666, "c4": 0.66}, {"time": 2.6667, "y": -1.7, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 3.2333, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "y": -3.6, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5, "y": -2.28}]}, "t16": {"rotate": [{"angle": -5.33, "curve": 0.315, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 0.1667, "angle": -4.58, "curve": 0.34, "c2": 0.36, "c3": 0.757}, {"time": 1.0333, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -5.85, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 2.5, "angle": -5.33, "curve": 0.315, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 2.6667, "angle": -4.58, "curve": 0.34, "c2": 0.36, "c3": 0.757}, {"time": 3.5333, "curve": 0.25, "c3": 0.75}, {"time": 4.7667, "angle": -5.85, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 5, "angle": -5.33}], "shear": [{"y": -3.28, "curve": 0.315, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 0.1667, "y": -2.82, "curve": 0.34, "c2": 0.36, "c3": 0.757}, {"time": 1.0333, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "y": -3.6, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 2.5, "y": -3.28, "curve": 0.315, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 2.6667, "y": -2.82, "curve": 0.34, "c2": 0.36, "c3": 0.757}, {"time": 3.5333, "curve": 0.25, "c3": 0.75}, {"time": 4.7667, "y": -3.6, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 5, "y": -3.28}]}, "t17": {"rotate": [{"angle": -5.38}, {"time": 0.0667, "angle": -5.85, "curve": 0.321, "c3": 0.655, "c4": 0.34}, {"time": 0.1667, "angle": -5.77, "curve": 0.267, "c2": 0.07, "c3": 0.752}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -5.38}, {"time": 2.5667, "angle": -5.85, "curve": 0.321, "c3": 0.655, "c4": 0.34}, {"time": 2.6667, "angle": -5.77, "curve": 0.267, "c2": 0.07, "c3": 0.752}, {"time": 3.9, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -5.38}], "shear": [{"y": -3.5, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.0667, "y": -3.6, "curve": 0.321, "c3": 0.655, "c4": 0.34}, {"time": 0.1667, "y": -3.55, "curve": 0.267, "c2": 0.07, "c3": 0.752}, {"time": 1.4, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 2.5, "y": -3.5, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 2.5667, "y": -3.6, "curve": 0.321, "c3": 0.655, "c4": 0.34}, {"time": 2.6667, "y": -3.55, "curve": 0.267, "c2": 0.07, "c3": 0.752}, {"time": 3.9, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 5, "y": -3.5}]}, "t3": {"rotate": [{"angle": 0.24, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 1.41, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 2.5, "angle": 0.24, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "angle": 1.41, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 5, "angle": 0.24}]}, "t22": {"rotate": [{"angle": 0.7, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 2.61, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 2.5, "angle": 0.7, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 2.9333, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": 2.61, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 5, "angle": 0.7}]}, "t23": {"rotate": [{"angle": 1.24, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": 2.61, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 2.5, "angle": 1.24, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 3.0667, "curve": 0.25, "c3": 0.75}, {"time": 4.4, "angle": 2.61, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 5, "angle": 1.24}]}, "t21": {"rotate": [{"angle": 0.57, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": 2.61, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 2.5, "angle": 0.57, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "angle": 2.61, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 5, "angle": 0.57}]}, "t13": {"translate": [{"x": -0.04, "y": -0.18, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": -0.63, "y": -2.56, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2.5, "x": -0.04, "y": -0.18, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "x": -0.63, "y": -2.56, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 5, "x": -0.04, "y": -0.18}], "scale": [{"x": 0.944, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1667, "x": 0.94, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2.5, "x": 0.944, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 2.6667, "x": 0.94, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 5, "x": 0.944}]}, "t14": {"translate": [{"x": -0.08, "y": -0.14, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": -1.15, "y": -1.96, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2.5, "x": -0.08, "y": -0.14, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "x": -1.15, "y": -1.96, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 5, "x": -0.08, "y": -0.14}], "scale": [{"x": 1.004, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": 1.06, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2.5, "x": 1.004, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "x": 1.06, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 5, "x": 1.004}]}, "yt2": {"rotate": [{"angle": -11.69, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5, "angle": -6.62, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.2333, "angle": -15.33, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.7333, "angle": -20.4, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.5, "angle": -11.69, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3, "angle": -6.62, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.7333, "angle": -15.33, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.2333, "angle": -20.4, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5, "angle": -11.69}], "scale": [{"x": 0.92}]}, "zt2": {"rotate": [{"angle": -6.41, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 1.0667, "angle": -29.35, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": -4.92, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 2.5, "angle": -6.41, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 3.5667, "angle": -29.35, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "angle": -4.92, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 5, "angle": -6.41}], "scale": [{"x": 0.82}]}, "zs5": {"rotate": [{"angle": -1.26, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -9.67, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.5, "angle": -1.26, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.7333, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -9.67, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5, "angle": -1.26}]}, "zs6": {"rotate": [{"angle": 1.28, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": 5.88, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 2.5, "angle": 1.28, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "angle": 5.88, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 5, "angle": 1.28}]}, "zuiz7": {"rotate": [{"angle": 1.92, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": -1.86, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 1.92, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "angle": -1.86, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 1.92}]}, "zuiz8": {"rotate": [{"angle": 1.58, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.2, "angle": 1.92, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -1.86, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 2.5, "angle": 1.58, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 2.7, "angle": 1.92, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "angle": -1.86, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 5, "angle": 1.58}]}, "zuiz9": {"rotate": [{"angle": 0.91, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 0.4333, "angle": 1.92, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -1.86, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 2.5, "angle": 0.91, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 2.9333, "angle": 1.92, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": -1.86, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 5, "angle": 0.91}]}, "zuiz10": {"rotate": [{"angle": 0.13, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 0.5667, "angle": 1.92, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": -1.86, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 2.5, "angle": 0.13, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 3.0667, "angle": 1.92, "curve": 0.25, "c3": 0.75}, {"time": 4.4, "angle": -1.86, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 5, "angle": 0.13}]}, "zuiz11": {"rotate": [{"angle": -0.66, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 0.7667, "angle": 1.92, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": -1.86, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 2.5, "angle": -0.66, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 3.2667, "angle": 1.92, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "angle": -1.86, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 5, "angle": -0.66}]}, "zs7": {"rotate": [{"angle": 4.82, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.2, "angle": 5.37, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 1.2333, "angle": -0.58, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 1.4667, "angle": -0.15, "curve": 0.243, "c3": 0.688, "c4": 0.74}, {"time": 2.5, "angle": 4.82, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 2.7, "angle": 5.37, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 3.7333, "angle": -0.58, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 3.9667, "angle": -0.15, "curve": 0.243, "c3": 0.688, "c4": 0.74}, {"time": 5, "angle": 4.82}], "translate": [{"x": -0.23, "y": 2.67, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 0.6667, "x": -0.5, "y": 5.98, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 2.5, "x": -0.23, "y": 2.67, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 3.1667, "x": -0.5, "y": 5.98, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 5, "x": -0.23, "y": 2.67}], "scale": [{"y": 1.057, "curve": 0.286, "c2": 0.29, "c3": 0.75}, {"time": 0.6667, "y": 1.12, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "curve": 0.25, "c3": 0.821, "c4": 0.81}, {"time": 2.5, "y": 1.057, "curve": 0.286, "c2": 0.29, "c3": 0.75}, {"time": 3.1667, "y": 1.12, "curve": 0.25, "c3": 0.75}, {"time": 4.4, "curve": 0.25, "c3": 0.821, "c4": 0.81}, {"time": 5, "y": 1.057}]}, "zs8": {"rotate": [{"angle": 2.88}, {"time": 0.4, "angle": 5.37, "curve": 0.243, "c3": 0.655, "c4": 0.62}, {"time": 1.2333, "angle": -0.66, "curve": 0.349, "c2": 0.39, "c3": 0.686, "c4": 0.74}, {"time": 1.4333, "angle": -1.78, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 1.6333, "angle": -2.55}, {"time": 2.5, "angle": 2.88}, {"time": 2.9, "angle": 5.37, "curve": 0.243, "c3": 0.655, "c4": 0.62}, {"time": 3.7333, "angle": -0.66, "curve": 0.349, "c2": 0.39, "c3": 0.686, "c4": 0.74}, {"time": 3.9333, "angle": -1.78, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1333, "angle": -2.55}, {"time": 5, "angle": 2.88}]}, "zs9": {"rotate": [{"angle": 1.98}, {"time": 0.5333, "angle": 5.37, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 1.2333, "angle": 0.66, "curve": 0.353, "c2": 0.4, "c3": 0.698, "c4": 0.77}, {"time": 1.5667, "angle": -1.78, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 1.7667, "angle": -2.55}, {"time": 2.5, "angle": 1.98}, {"time": 3.0333, "angle": 5.37, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 3.7333, "angle": 0.66, "curve": 0.353, "c2": 0.4, "c3": 0.698, "c4": 0.77}, {"time": 4.0667, "angle": -1.78, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.2667, "angle": -2.55}, {"time": 5, "angle": 1.98}]}, "zs10": {"rotate": [{"angle": 0.85}, {"time": 0.7, "angle": 5.37, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 1.2333, "angle": 2.07, "curve": 0.346, "c2": 0.37, "c3": 0.704, "c4": 0.79}, {"time": 1.7333, "angle": -1.78, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 1.9667, "angle": -2.55}, {"time": 2.5, "angle": 0.85}, {"time": 3.2, "angle": 5.37, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 3.7333, "angle": 2.07, "curve": 0.346, "c2": 0.37, "c3": 0.704, "c4": 0.79}, {"time": 4.2333, "angle": -1.78, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.4667, "angle": -2.55}, {"time": 5, "angle": 0.85}]}, "zs11": {"rotate": [{"angle": -0.22}, {"time": 0.8667, "angle": 5.37, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.2333, "angle": 3.43, "curve": 0.328, "c2": 0.32, "c3": 0.705, "c4": 0.79}, {"time": 1.9333, "angle": -1.78, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1333, "angle": -2.55}, {"time": 2.5, "angle": -0.22}, {"time": 3.3667, "angle": 5.37, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.7333, "angle": 3.43, "curve": 0.328, "c2": 0.32, "c3": 0.705, "c4": 0.79}, {"time": 4.4333, "angle": -1.78, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.6333, "angle": -2.55}, {"time": 5, "angle": -0.22}]}, "qinz2": {"rotate": [{"angle": -1, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1667, "angle": -1.16, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": 2.34, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2.5, "angle": -1, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 2.6667, "angle": -1.16, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "angle": 2.34, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 5, "angle": -1}]}, "qinz3": {"rotate": [{"angle": -0.61, "curve": 0.355, "c2": 0.42, "c3": 0.693, "c4": 0.77}, {"time": 0.2, "angle": -1, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.4, "angle": -1.16, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": 1.14, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.5, "angle": -0.61, "curve": 0.355, "c2": 0.42, "c3": 0.693, "c4": 0.77}, {"time": 2.7, "angle": -1, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 2.9, "angle": -1.16, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "angle": 1.14, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 5, "angle": -0.61}]}, "qinz4": {"rotate": [{"angle": -0.22, "curve": 0.357, "c2": 0.42, "c3": 0.705, "c4": 0.8}, {"time": 0.4, "angle": -1, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.5333, "angle": -1.16, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": 1.14, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2.5, "angle": -0.22, "curve": 0.357, "c2": 0.42, "c3": 0.705, "c4": 0.8}, {"time": 2.9, "angle": -1, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 3.0333, "angle": -1.16, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "angle": 1.14, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 5, "angle": -0.22}]}, "qinz5": {"rotate": [{"angle": 0.18, "curve": 0.349, "c2": 0.39, "c3": 0.712, "c4": 0.82}, {"time": 0.5333, "angle": -1, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.7, "angle": -1.16, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": 1.14, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 2.5, "angle": 0.18, "curve": 0.349, "c2": 0.39, "c3": 0.712, "c4": 0.82}, {"time": 3.0333, "angle": -1, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 3.2, "angle": -1.16, "curve": 0.25, "c3": 0.75}, {"time": 4.4667, "angle": 1.14, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 5, "angle": 0.18}]}, "zuiz1": {"rotate": [{"angle": 0.08, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 0.4, "angle": -1.41, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.6333, "angle": -1.95, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": 2.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.5, "angle": 0.08, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 2.9, "angle": -1.41, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.1333, "angle": -1.95, "curve": 0.25, "c3": 0.75}, {"time": 4.4, "angle": 2.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5, "angle": 0.08}]}, "zuiz2": {"rotate": [{"angle": 0.97, "curve": 0.324, "c2": 0.31, "c3": 0.671, "c4": 0.68}, {"time": 0.4, "angle": -0.64, "curve": 0.348, "c2": 0.39, "c3": 0.686, "c4": 0.73}, {"time": 0.5667, "angle": -1.41, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.8333, "angle": -1.95, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": 2.14, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2.5, "angle": 0.97, "curve": 0.324, "c2": 0.31, "c3": 0.671, "c4": 0.68}, {"time": 2.9, "angle": -0.64, "curve": 0.348, "c2": 0.39, "c3": 0.686, "c4": 0.73}, {"time": 3.0667, "angle": -1.41, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.3333, "angle": -1.95, "curve": 0.25, "c3": 0.75}, {"time": 4.5667, "angle": 2.14, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 5, "angle": 0.97}]}, "zuiz3": {"rotate": [{"angle": 1.75, "curve": 0.3, "c2": 0.22, "c3": 0.648, "c4": 0.6}, {"time": 0.4, "angle": 0.26, "curve": 0.345, "c2": 0.37, "c3": 0.696, "c4": 0.76}, {"time": 0.7667, "angle": -1.41, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.0333, "angle": -1.95, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 2.14, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2.5, "angle": 1.75, "curve": 0.3, "c2": 0.22, "c3": 0.648, "c4": 0.6}, {"time": 2.9, "angle": 0.26, "curve": 0.345, "c2": 0.37, "c3": 0.696, "c4": 0.76}, {"time": 3.2667, "angle": -1.41, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.5333, "angle": -1.95, "curve": 0.25, "c3": 0.75}, {"time": 4.7667, "angle": 2.14, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 5, "angle": 1.75}]}, "zuiz4": {"rotate": [{"angle": 2.14, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.4, "angle": 1.14, "curve": 0.326, "c2": 0.31, "c3": 0.697, "c4": 0.76}, {"time": 1, "angle": -1.41, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.2333, "angle": -1.95, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 2.14, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.9, "angle": 1.14, "curve": 0.326, "c2": 0.31, "c3": 0.697, "c4": 0.76}, {"time": 3.5, "angle": -1.41, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.7333, "angle": -1.95, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 2.14}]}, "zuiz5": {"rotate": [{"angle": 1.46}, {"time": 0.2, "angle": 2.14, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 0.4, "angle": 1.87, "curve": 0.288, "c2": 0.18, "c3": 0.691, "c4": 0.75}, {"time": 1.2, "angle": -1.41, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.4667, "angle": -1.95, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 1.46}, {"time": 2.7, "angle": 2.14, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 2.9, "angle": 1.87, "curve": 0.288, "c2": 0.18, "c3": 0.691, "c4": 0.75}, {"time": 3.7, "angle": -1.41, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.9667, "angle": -1.95, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 1.46}]}, "zuiz6": {"rotate": [{"angle": 0.98, "curve": 0.375, "c2": 0.52, "c3": 0.724, "c4": 0.92}, {"time": 0.4, "angle": 2.09, "curve": 0.344, "c2": 0.66, "c3": 0.677}, {"time": 0.4333, "angle": 2.14, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.4333, "angle": -1.41, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.6667, "angle": -1.95, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2.5, "angle": 0.98, "curve": 0.375, "c2": 0.52, "c3": 0.724, "c4": 0.92}, {"time": 2.9, "angle": 2.09, "curve": 0.344, "c2": 0.66, "c3": 0.677}, {"time": 2.9333, "angle": 2.14, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.9333, "angle": -1.41, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.1667, "angle": -1.95, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 5, "angle": 0.98}]}, "weib": {"shear": [{"y": -6.23, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.3333, "y": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "y": -2.4, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 2.5, "y": -6.23, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 2.8333, "y": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "y": -2.4, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 5, "y": -6.23}]}, "weib2": {"shear": [{"y": -5.45, "curve": 0.342, "c2": 0.36, "c3": 0.678, "c4": 0.7}, {"time": 0.1667, "y": -6.23, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.5, "y": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "y": -2.4, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.5, "y": -5.45, "curve": 0.342, "c2": 0.36, "c3": 0.678, "c4": 0.7}, {"time": 2.6667, "y": -6.23, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 3, "y": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 4.2333, "y": -2.4, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5, "y": -5.45}]}, "weib3": {"shear": [{"y": -4.6, "curve": 0.34, "c2": 0.35, "c3": 0.684, "c4": 0.72}, {"time": 0.3333, "y": -6.23, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.6667, "y": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "y": -2.4, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 2.5, "y": -4.6, "curve": 0.34, "c2": 0.35, "c3": 0.684, "c4": 0.72}, {"time": 2.8333, "y": -6.23, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 3.1667, "y": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "y": -2.4, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 5, "y": -4.6}]}, "qunz2": {"rotate": [{"angle": -0.27, "curve": 0.348, "c2": 0.39, "c3": 0.683, "c4": 0.73}, {"time": 0.1333, "angle": 0.09, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.3667, "angle": 0.39, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": -2.66, "curve": 0.243, "c3": 0.659, "c4": 0.64}, {"time": 2.5, "angle": -0.27, "curve": 0.348, "c2": 0.39, "c3": 0.683, "c4": 0.73}, {"time": 2.6333, "angle": 0.09, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.8667, "angle": 0.39, "curve": 0.25, "c3": 0.75}, {"time": 4.1, "angle": -2.66, "curve": 0.243, "c3": 0.659, "c4": 0.64}, {"time": 5, "angle": -0.27}]}, "qunz3": {"rotate": [{"angle": -0.79, "curve": 0.34, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 0.1333, "angle": -0.34, "curve": 0.349, "c2": 0.39, "c3": 0.686, "c4": 0.74}, {"time": 0.3333, "angle": 0.09, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.5333, "angle": 0.39, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": -2.66, "curve": 0.246, "c3": 0.635, "c4": 0.55}, {"time": 2.5, "angle": -0.79, "curve": 0.34, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 2.6333, "angle": -0.34, "curve": 0.349, "c2": 0.39, "c3": 0.686, "c4": 0.74}, {"time": 2.8333, "angle": 0.09, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 3.0333, "angle": 0.39, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "angle": -2.66, "curve": 0.246, "c3": 0.635, "c4": 0.55}, {"time": 5, "angle": -0.79}]}, "qunz4": {"rotate": [{"angle": -1.31, "curve": 0.333, "c2": 0.33, "c3": 0.668, "c4": 0.67}, {"time": 0.1333, "angle": -0.85, "curve": 0.353, "c2": 0.4, "c3": 0.698, "c4": 0.77}, {"time": 0.4667, "angle": 0.09, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.6667, "angle": 0.39, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": -2.66, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 2.5, "angle": -1.31, "curve": 0.333, "c2": 0.33, "c3": 0.668, "c4": 0.67}, {"time": 2.6333, "angle": -0.85, "curve": 0.353, "c2": 0.4, "c3": 0.698, "c4": 0.77}, {"time": 2.9667, "angle": 0.09, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 3.1667, "angle": 0.39, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "angle": -2.66, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 5, "angle": -1.31}]}, "qunz5": {"rotate": [{"angle": -1.85, "curve": 0.326, "c2": 0.31, "c3": 0.661, "c4": 0.65}, {"time": 0.1333, "angle": -1.39, "curve": 0.346, "c2": 0.37, "c3": 0.704, "c4": 0.79}, {"time": 0.6333, "angle": 0.09, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.8667, "angle": 0.39, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "angle": -2.66, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 2.5, "angle": -1.85, "curve": 0.326, "c2": 0.31, "c3": 0.661, "c4": 0.65}, {"time": 2.6333, "angle": -1.39, "curve": 0.346, "c2": 0.37, "c3": 0.704, "c4": 0.79}, {"time": 3.1333, "angle": 0.09, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 3.3667, "angle": 0.39, "curve": 0.25, "c3": 0.75}, {"time": 4.6, "angle": -2.66, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 5, "angle": -1.85}]}, "qunz6": {"rotate": [{"angle": -2.29, "curve": 0.319, "c2": 0.28, "c3": 0.654, "c4": 0.62}, {"time": 0.1333, "angle": -1.91, "curve": 0.328, "c2": 0.32, "c3": 0.705, "c4": 0.79}, {"time": 0.8333, "angle": 0.09, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 1.0333, "angle": 0.39, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": -2.66, "curve": 0.286, "c3": 0.626, "c4": 0.38}, {"time": 2.5, "angle": -2.29, "curve": 0.319, "c2": 0.28, "c3": 0.654, "c4": 0.62}, {"time": 2.6333, "angle": -1.91, "curve": 0.328, "c2": 0.32, "c3": 0.705, "c4": 0.79}, {"time": 3.3333, "angle": 0.09, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 3.5333, "angle": 0.39, "curve": 0.25, "c3": 0.75}, {"time": 4.7333, "angle": -2.66, "curve": 0.286, "c3": 0.626, "c4": 0.38}, {"time": 5, "angle": -2.29}]}, "st6": {"rotate": [{"angle": 4.35}, {"time": 0.1667, "angle": 5.02, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1.2333, "angle": 0.35, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 1.4333}, {"time": 2.5, "angle": 4.35}, {"time": 2.6667, "angle": 5.02, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 3.7333, "angle": 0.35, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 3.9333}, {"time": 5, "angle": 4.35}]}, "st7": {"rotate": [{"angle": 3.52}, {"time": 0.4, "angle": 5.02, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.2333, "angle": 1.2, "curve": 0.355, "c2": 0.42, "c3": 0.693, "c4": 0.77}, {"time": 1.4667, "angle": 0.35, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 1.6333}, {"time": 2.5, "angle": 3.52}, {"time": 2.9, "angle": 5.02, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.7333, "angle": 1.2, "curve": 0.355, "c2": 0.42, "c3": 0.693, "c4": 0.77}, {"time": 3.9667, "angle": 0.35, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 4.1333}, {"time": 5, "angle": 3.52}]}, "st8": {"rotate": [{"angle": 2.73, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.5667, "angle": 5.02, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1.2333, "angle": 2.26, "curve": 0.356, "c2": 0.41, "c3": 0.707, "c4": 0.81}, {"time": 1.6667, "angle": 0.35, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 1.8, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2.5, "angle": 2.73, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 3.0667, "angle": 5.02, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 3.7333, "angle": 2.26, "curve": 0.356, "c2": 0.41, "c3": 0.707, "c4": 0.81}, {"time": 4.1667, "angle": 0.35, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 4.3, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 5, "angle": 2.73}]}, "yt3": {"rotate": [{"angle": -13.76, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.1333, "angle": -11.69, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6333, "angle": -6.62, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.4, "angle": -15.33, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.9, "angle": -20.4, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 2.5, "angle": -13.76, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 2.6333, "angle": -11.69, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.1333, "angle": -6.62, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.9, "angle": -15.33, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.4, "angle": -20.4, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 5, "angle": -13.76}]}, "zt3": {"rotate": [{"angle": -5, "curve": 0.306, "c2": 0.12, "c3": 0.642, "c4": 0.47}, {"time": 0.1333, "angle": -6.41, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 1.2, "angle": -26.4, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": -4.92, "curve": 0.325, "c3": 0.658, "c4": 0.34}, {"time": 2.5, "angle": -5, "curve": 0.306, "c2": 0.12, "c3": 0.642, "c4": 0.47}, {"time": 2.6333, "angle": -6.41, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 3.7, "angle": -26.4, "curve": 0.25, "c3": 0.75}, {"time": 4.9667, "angle": -4.92, "curve": 0.325, "c3": 0.658, "c4": 0.34}, {"time": 5, "angle": -5}]}, "ys5": {"rotate": [{"angle": -2.65, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": -7.2, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.5, "angle": -2.65, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.2333, "angle": -7.2, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5, "angle": -2.65}]}, "ys7": {"rotate": [{"angle": -18.02, "curve": 0.376, "c2": 0.61, "c3": 0.718}, {"time": 0.2667, "angle": -21.6, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 3.7, "curve": 0.243, "c3": 0.677, "c4": 0.7}, {"time": 2.5, "angle": -18.02, "curve": 0.376, "c2": 0.61, "c3": 0.718}, {"time": 2.7667, "angle": -21.6, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 3.7, "curve": 0.243, "c3": 0.677, "c4": 0.7}, {"time": 5, "angle": -18.02}]}, "ys6": {"rotate": [{"angle": 13.08, "curve": 0.374, "c2": 0.49, "c3": 0.751}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": 25.2, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 2.5, "angle": 13.08, "curve": 0.374, "c2": 0.49, "c3": 0.751}, {"time": 3.1333, "curve": 0.25, "c3": 0.75}, {"time": 4.4, "angle": 25.2, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 5, "angle": 13.08}]}, "ys8": {"rotate": [{"angle": -1.71, "curve": 0.344, "c2": 0.37, "c3": 0.68, "c4": 0.71}, {"time": 0.1333, "angle": -2.57, "curve": 0.376, "c2": 0.61, "c3": 0.718}, {"time": 0.4, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": 3.7, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 2.5, "angle": -1.71, "curve": 0.344, "c2": 0.37, "c3": 0.68, "c4": 0.71}, {"time": 2.6333, "angle": -2.57, "curve": 0.376, "c2": 0.61, "c3": 0.718}, {"time": 2.9, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "angle": 3.7, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 5, "angle": -1.71}]}}, "deform": {"default": {"t5": {"t5": [{"offset": 68, "vertices": [-0.19183, -0.27384, -0.16756, -0.28062, -0.19183, -0.27384, -0.16756, -0.28062, 0, 0, 0, 0, -0.5679, 0.04068, -0.5701, 0.02134, -0.57069, 0.01318, -0.5679, 0.04068, -0.5701, 0.02134, -0.57069, 0.01318, -0.5679, 0.04068, -0.5701, 0.02134, -0.57069, 0.01318, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.4105, -0.07517, -0.40299, -0.08924, -0.3987, -0.09504, -0.4105, -0.07517, -0.40299, -0.08924, -0.4105, -0.07517, -0.40299, -0.08924, -0.4105, -0.07517, -0.40299, -0.08924, -0.4105, -0.07517, -0.40299, -0.08924], "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "offset": 68, "vertices": [-1.10507, -1.57751, -0.96527, -1.61653, -1.10507, -1.57751, -0.96527, -1.61653, 0, 0, 0, 0, -3.27148, 0.23433, -3.28415, 0.12291, -3.28751, 0.07592, -3.27148, 0.23433, -3.28415, 0.12291, -3.28751, 0.07592, -3.27148, 0.23433, -3.28415, 0.12291, -3.28751, 0.07592, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.36475, -0.43303, -2.3215, -0.51407, -2.29675, -0.5475, -2.36475, -0.43303, -2.3215, -0.51407, -2.36475, -0.43303, -2.3215, -0.51407, -2.36475, -0.43303, -2.3215, -0.51407, -2.36475, -0.43303, -2.3215, -0.51407], "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 2.5, "offset": 68, "vertices": [-0.19183, -0.27384, -0.16756, -0.28062, -0.19183, -0.27384, -0.16756, -0.28062, 0, 0, 0, 0, -0.5679, 0.04068, -0.5701, 0.02134, -0.57069, 0.01318, -0.5679, 0.04068, -0.5701, 0.02134, -0.57069, 0.01318, -0.5679, 0.04068, -0.5701, 0.02134, -0.57069, 0.01318, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.4105, -0.07517, -0.40299, -0.08924, -0.3987, -0.09504, -0.4105, -0.07517, -0.40299, -0.08924, -0.4105, -0.07517, -0.40299, -0.08924, -0.4105, -0.07517, -0.40299, -0.08924, -0.4105, -0.07517, -0.40299, -0.08924], "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 2.8, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "offset": 68, "vertices": [-1.10507, -1.57751, -0.96527, -1.61653, -1.10507, -1.57751, -0.96527, -1.61653, 0, 0, 0, 0, -3.27148, 0.23433, -3.28415, 0.12291, -3.28751, 0.07592, -3.27148, 0.23433, -3.28415, 0.12291, -3.28751, 0.07592, -3.27148, 0.23433, -3.28415, 0.12291, -3.28751, 0.07592, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.36475, -0.43303, -2.3215, -0.51407, -2.29675, -0.5475, -2.36475, -0.43303, -2.3215, -0.51407, -2.36475, -0.43303, -2.3215, -0.51407, -2.36475, -0.43303, -2.3215, -0.51407, -2.36475, -0.43303, -2.3215, -0.51407], "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 5, "offset": 68, "vertices": [-0.19183, -0.27384, -0.16756, -0.28062, -0.19183, -0.27384, -0.16756, -0.28062, 0, 0, 0, 0, -0.5679, 0.04068, -0.5701, 0.02134, -0.57069, 0.01318, -0.5679, 0.04068, -0.5701, 0.02134, -0.57069, 0.01318, -0.5679, 0.04068, -0.5701, 0.02134, -0.57069, 0.01318, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.4105, -0.07517, -0.40299, -0.08924, -0.3987, -0.09504, -0.4105, -0.07517, -0.40299, -0.08924, -0.4105, -0.07517, -0.40299, -0.08924, -0.4105, -0.07517, -0.40299, -0.08924, -0.4105, -0.07517, -0.40299, -0.08924]}]}, "ys2": {"ys2": [{"curve": 0.25, "c3": 0.75}, {"time": 1.2333, "offset": 114, "vertices": [-0.29518, 2.57922, -1.18005, 2.31308, -0.29518, 2.57922, -1.18005, 2.31308, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.0882, 0.77316, 0.17263, 0.75958, -0.26624, 2.32217], "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "offset": 114, "vertices": [-0.29518, 2.57922, -1.18005, 2.31308, -0.29518, 2.57922, -1.18005, 2.31308, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.0882, 0.77316, 0.17263, 0.75958, -0.26624, 2.32217], "curve": 0.25, "c3": 0.75}, {"time": 5}]}, "t1": {"t1": [{"offset": 22, "vertices": [0.0107, -0.09726, 0.07999, -0.04725, -0.03774, -0.06386, -0.03774, -0.06386, -0.04313, -0.07298, 0, 0, -0.01495, -0.02528, -0.01495, -0.02528, 0, 0, -0.01498, -0.02527, -0.02988, -0.05056, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.03984, -0.06741, -0.03984, -0.06741, -0.03984, -0.06741, -0.03984, -0.06741, 0, 0, 0, 0, -0.0249, -0.04213, -0.0249, -0.04213, -0.02988, -0.05056, -0.02988, -0.05056, -0.03984, -0.06741, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.02659, -0.10737, -0.02659, -0.10737, -0.02159, -0.03648, 0.27862, 1.99621, 0.27862, 1.99621, 0, 0, 0.29478, 2.02359, -0.41019, 1.03176, 0, 0, 0.32176, 2.0692, 0.27862, 1.99621, 0.3056, 2.04183, -0.02659, -0.10737], "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1333, "offset": 114, "vertices": [0.34695, 2.24472, 0.34695, 2.24472, 0, 0, 0.34695, 2.24472, -0.38934, 1.20882, 0, 0, 0.34695, 2.24472, 0.34695, 2.24472, 0.34695, 2.24472], "curve": 0.25, "c3": 0.75}, {"time": 1.4, "offset": 22, "vertices": [0.09689, 1.32834, 2.34161, -0.28244, -0.88684, -1.50067, -0.88684, -1.50067, -1.01364, -1.71501, 0, 0, -0.35126, -0.59404, -0.35126, -0.59404, 0, 0, -0.35199, -0.59375, -0.70215, -1.18808, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.57861, -1.03737, -1.51486, -2.62155, -0.93625, -1.58418, -1.51358, -2.619, -2.09103, -3.65382, -1.30206, -2.3343, -1.30206, -2.3343, -3.04453, -5.39923, -1.88718, -3.32434, -2.1489, -3.78177, -1.28079, -2.22559, -1.80438, -3.1404, -0.57867, -1.03745, 0, 0, 0, 0, -1.15735, -2.07489, -1.30194, -2.3343, -1.0127, -1.81554, -1.0127, -1.81554, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.29401, -5.51611, -0.90491, -5.77899, -2.29437, -4.9789, -1.25882, -3.59517, -1.25882, -3.59517, 0, 0, -0.87921, -2.95193, -0.87921, -2.95193, 0, 0, -0.24518, -1.88004, -1.25882, -3.59517, -0.15619, 0.45737, -0.62482, -2.52328], "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2.5, "offset": 22, "vertices": [0.0107, -0.09726, 0.07999, -0.04725, -0.03774, -0.06386, -0.03774, -0.06386, -0.04313, -0.07298, 0, 0, -0.01495, -0.02528, -0.01495, -0.02528, 0, 0, -0.01498, -0.02527, -0.02988, -0.05056, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.03984, -0.06741, -0.03984, -0.06741, -0.03984, -0.06741, -0.03984, -0.06741, 0, 0, 0, 0, -0.0249, -0.04213, -0.0249, -0.04213, -0.02988, -0.05056, -0.02988, -0.05056, -0.03984, -0.06741, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.02659, -0.10737, -0.02659, -0.10737, -0.02159, -0.03648, 0.27862, 1.99621, 0.27862, 1.99621, 0, 0, 0.29478, 2.02359, -0.41019, 1.03176, 0, 0, 0.32176, 2.0692, 0.27862, 1.99621, 0.3056, 2.04183, -0.02659, -0.10737], "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 2.6333, "offset": 114, "vertices": [0.34695, 2.24472, 0.34695, 2.24472, 0, 0, 0.34695, 2.24472, -0.38934, 1.20882, 0, 0, 0.34695, 2.24472, 0.34695, 2.24472, 0.34695, 2.24472], "curve": 0.25, "c3": 0.75}, {"time": 3.9, "offset": 22, "vertices": [0.09689, 1.32834, 2.34161, -0.28244, -0.88684, -1.50067, -0.88684, -1.50067, -1.01364, -1.71501, 0, 0, -0.35126, -0.59404, -0.35126, -0.59404, 0, 0, -0.35199, -0.59375, -0.70215, -1.18808, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.57861, -1.03737, -1.51486, -2.62155, -0.93625, -1.58418, -1.51358, -2.619, -2.09103, -3.65382, -1.30206, -2.3343, -1.30206, -2.3343, -3.04453, -5.39923, -1.88718, -3.32434, -2.1489, -3.78177, -1.28079, -2.22559, -1.80438, -3.1404, -0.57867, -1.03745, 0, 0, 0, 0, -1.15735, -2.07489, -1.30194, -2.3343, -1.0127, -1.81554, -1.0127, -1.81554, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.29401, -5.51611, -0.90491, -5.77899, -2.29437, -4.9789, -1.25882, -3.59517, -1.25882, -3.59517, 0, 0, -0.87921, -2.95193, -0.87921, -2.95193, 0, 0, -0.24518, -1.88004, -1.25882, -3.59517, -0.15619, 0.45737, -0.62482, -2.52328], "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 5, "offset": 22, "vertices": [0.0107, -0.09726, 0.07999, -0.04725, -0.03774, -0.06386, -0.03774, -0.06386, -0.04313, -0.07298, 0, 0, -0.01495, -0.02528, -0.01495, -0.02528, 0, 0, -0.01498, -0.02527, -0.02988, -0.05056, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.03984, -0.06741, -0.03984, -0.06741, -0.03984, -0.06741, -0.03984, -0.06741, 0, 0, 0, 0, -0.0249, -0.04213, -0.0249, -0.04213, -0.02988, -0.05056, -0.02988, -0.05056, -0.03984, -0.06741, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.02659, -0.10737, -0.02659, -0.10737, -0.02159, -0.03648, 0.27862, 1.99621, 0.27862, 1.99621, 0, 0, 0.29478, 2.02359, -0.41019, 1.03176, 0, 0, 0.32176, 2.0692, 0.27862, 1.99621, 0.3056, 2.04183, -0.02659, -0.10737]}]}, "t8": {"t8": [{"time": 4.2667, "curve": 0.25, "c3": 0.75}, {"time": 4.4667, "vertices": [-1.67767, -2.75119, 0, 0, 0, 0, 0, 0, 0.80493, -2.76355, 2.17273, -4.48917, 2.17273, -4.48917, 1.28178, -6.25882, 1.28178, -6.25882, -2.44203, -4.00494, -1.67767, -2.75119, 0.76987, -13.90353, -1.08292, -14.22025, -2.27963, -4.32135, -0.20671, 2.18784, -0.20671, 2.18784, -1.62814, -2.67029], "curve": 0.25, "c3": 0.75}, {"time": 4.6333}]}, "zs1": {"zs1": [{"offset": 8, "vertices": [3.69751, 1.65552, 3.69751, 1.65552, 3.34976, 2.27795, 3.69751, 1.65552, 3.34976, 2.27795, 3.96185, 0.84821, 8.08096, 0.50394, 7.86713, 1.91339, 8.00861, -1.19226, 8.08096, 0.50394, 7.86713, 1.91339, 8.00861, -1.19226, 8.08096, 0.50394, 7.86713, 1.91339, 8.00861, -1.19226, 7.86713, 1.91339, 8.00861, -1.19226, 6.4368, 1.56549, 6.55255, -0.97556, 0, 0, 0, 0, 0, 0, 5.71613, -1.59509, 5.82324, -1.14081, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.17523, -0.71515, -0.10669, -0.72839, -1.73865, 7.15143, 0, 0, 0, 0, 4.78589, -3.73157, 3.07236, -3.61563, 3.64914, -3.04218, 2.53772, -2.71149, 2.96912, -2.2417, 3.54114, -3.5827, 4.10562, -2.93036, 0.23141, -3.28192, 0.78323, -3.19562, 0, 0, 0, 0, -0.17563, 2.8382, 2.71918, 0.83154, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.69751, 1.65552, 2.46936, -3.21057, 3.69751, 1.65552, 3.69751, 1.65552, 3.34976, 2.27795, -4.06235, 2.41354, -4.47046, 1.69562, 2.52744, 6.79456, 1.29626, 7.13208, 3.88947, 6.11816, 2.52744, 6.79456, 1.29626, 7.13208, 2.52744, 6.79456, 1.29626, 7.13208, 3.88947, 6.11816, 2.39038, 8.99826, 0.77478, 9.27747, 2.39038, 8.99826, 0.77478, 9.27747, 4.21515, 8.302, 2.52744, 6.79456, 1.29626, 7.13208, 0.60092, 9.99255, 4.32367, 9.02991, 0.60092, 9.99255, 4.32367, 9.02991, 2.52744, 6.79456, 1.29626, 7.13208, 0.60092, 9.99255, 4.32367, 9.02991, 0.60092, 9.99255, 4.32367, 9.02991, -0.34625, 1.43024, 0.21899, 1.45544, -0.52148, 2.14539, 0.32568, 2.18384, 0.15295, 2.20294, 0.11578, 2.2049, 0, 0, 0, 0, 0, 0, 0, 0, 5.89554, 0.67639, 5.71613, -1.59509, 5.82324, -1.14081, 5.84155, -1.04309, 5.72141, 1.39154, 5.82428, -0.86716, 5.87402, -0.4068, 5.88, -0.30811, 5.72141, 1.39154, 5.82428, -0.86716, 5.87402, -0.4068, -1.39081, 5.72101, 0.86816, 5.82355, 0.40771, 5.87439, 0.30859, 5.8797, 0.86816, 5.82355, 0.40771, 5.87439, -1.39081, 5.72101, 0.86816, 5.82355, 0.40771, 5.87439, 0.30859, 5.8797, -0.25146, 5.88208, 0.40771, 5.87439, 0.30859, 5.8797, 0.26654, 5.8822, 0.86816, 5.82355, 0.40771, 5.87439, 0.30859, 5.8797, 0.26654, 5.8822, -0.25146, 5.88208, 0.40771, 5.87439, 0.30859, 5.8797, 0.26654, 5.8822, 0.86816, 5.82355, 0.30859, 5.8797, 0.26654, 5.8822, -0.25146, 5.88208, 0.30859, 5.8797, 0.26654, 5.8822], "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "offset": 260, "vertices": [-2.513, 8.7005, 0.0896, 8.49213, 0, 0, 0, 0, -1.72751, 5.98145, 0.06042, 5.83838, -2.73492, 5.39502, -1.07452, 5.58331, -1.85474, 5.37634, -2.06403, 5.29907], "curve": 0.25, "c3": 0.75}, {"time": 2.5, "offset": 8, "vertices": [3.69751, 1.65552, 3.69751, 1.65552, 3.34976, 2.27795, 3.69751, 1.65552, 3.34976, 2.27795, 3.96185, 0.84821, 8.08096, 0.50394, 7.86713, 1.91339, 8.00861, -1.19226, 8.08096, 0.50394, 7.86713, 1.91339, 8.00861, -1.19226, 8.08096, 0.50394, 7.86713, 1.91339, 8.00861, -1.19226, 7.86713, 1.91339, 8.00861, -1.19226, 6.4368, 1.56549, 6.55255, -0.97556, 0, 0, 0, 0, 0, 0, 5.71613, -1.59509, 5.82324, -1.14081, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.17523, -0.71515, -0.10669, -0.72839, -1.73865, 7.15143, 0, 0, 0, 0, 4.78589, -3.73157, 3.07236, -3.61563, 3.64914, -3.04218, 2.53772, -2.71149, 2.96912, -2.2417, 3.54114, -3.5827, 4.10562, -2.93036, 0.23141, -3.28192, 0.78323, -3.19562, 0, 0, 0, 0, -0.17563, 2.8382, 2.71918, 0.83154, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.69751, 1.65552, 2.46936, -3.21057, 3.69751, 1.65552, 3.69751, 1.65552, 3.34976, 2.27795, -4.06235, 2.41354, -4.47046, 1.69562, 2.52744, 6.79456, 1.29626, 7.13208, 3.88947, 6.11816, 2.52744, 6.79456, 1.29626, 7.13208, 2.52744, 6.79456, 1.29626, 7.13208, 3.88947, 6.11816, 2.39038, 8.99826, 0.77478, 9.27747, 2.39038, 8.99826, 0.77478, 9.27747, 4.21515, 8.302, 2.52744, 6.79456, 1.29626, 7.13208, 0.60092, 9.99255, 4.32367, 9.02991, 0.60092, 9.99255, 4.32367, 9.02991, 2.52744, 6.79456, 1.29626, 7.13208, 0.60092, 9.99255, 4.32367, 9.02991, 0.60092, 9.99255, 4.32367, 9.02991, -0.34625, 1.43024, 0.21899, 1.45544, -0.52148, 2.14539, 0.32568, 2.18384, 0.15295, 2.20294, 0.11578, 2.2049, 0, 0, 0, 0, 0, 0, 0, 0, 5.89554, 0.67639, 5.71613, -1.59509, 5.82324, -1.14081, 5.84155, -1.04309, 5.72141, 1.39154, 5.82428, -0.86716, 5.87402, -0.4068, 5.88, -0.30811, 5.72141, 1.39154, 5.82428, -0.86716, 5.87402, -0.4068, -1.39081, 5.72101, 0.86816, 5.82355, 0.40771, 5.87439, 0.30859, 5.8797, 0.86816, 5.82355, 0.40771, 5.87439, -1.39081, 5.72101, 0.86816, 5.82355, 0.40771, 5.87439, 0.30859, 5.8797, -0.25146, 5.88208, 0.40771, 5.87439, 0.30859, 5.8797, 0.26654, 5.8822, 0.86816, 5.82355, 0.40771, 5.87439, 0.30859, 5.8797, 0.26654, 5.8822, -0.25146, 5.88208, 0.40771, 5.87439, 0.30859, 5.8797, 0.26654, 5.8822, 0.86816, 5.82355, 0.30859, 5.8797, 0.26654, 5.8822, -0.25146, 5.88208, 0.30859, 5.8797, 0.26654, 5.8822], "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "offset": 260, "vertices": [-2.513, 8.7005, 0.0896, 8.49213, 0, 0, 0, 0, -1.72751, 5.98145, 0.06042, 5.83838, -2.73492, 5.39502, -1.07452, 5.58331, -1.85474, 5.37634, -2.06403, 5.29907], "curve": 0.25, "c3": 0.75}, {"time": 5, "offset": 8, "vertices": [3.69751, 1.65552, 3.69751, 1.65552, 3.34976, 2.27795, 3.69751, 1.65552, 3.34976, 2.27795, 3.96185, 0.84821, 8.08096, 0.50394, 7.86713, 1.91339, 8.00861, -1.19226, 8.08096, 0.50394, 7.86713, 1.91339, 8.00861, -1.19226, 8.08096, 0.50394, 7.86713, 1.91339, 8.00861, -1.19226, 7.86713, 1.91339, 8.00861, -1.19226, 6.4368, 1.56549, 6.55255, -0.97556, 0, 0, 0, 0, 0, 0, 5.71613, -1.59509, 5.82324, -1.14081, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.17523, -0.71515, -0.10669, -0.72839, -1.73865, 7.15143, 0, 0, 0, 0, 4.78589, -3.73157, 3.07236, -3.61563, 3.64914, -3.04218, 2.53772, -2.71149, 2.96912, -2.2417, 3.54114, -3.5827, 4.10562, -2.93036, 0.23141, -3.28192, 0.78323, -3.19562, 0, 0, 0, 0, -0.17563, 2.8382, 2.71918, 0.83154, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.69751, 1.65552, 2.46936, -3.21057, 3.69751, 1.65552, 3.69751, 1.65552, 3.34976, 2.27795, -4.06235, 2.41354, -4.47046, 1.69562, 2.52744, 6.79456, 1.29626, 7.13208, 3.88947, 6.11816, 2.52744, 6.79456, 1.29626, 7.13208, 2.52744, 6.79456, 1.29626, 7.13208, 3.88947, 6.11816, 2.39038, 8.99826, 0.77478, 9.27747, 2.39038, 8.99826, 0.77478, 9.27747, 4.21515, 8.302, 2.52744, 6.79456, 1.29626, 7.13208, 0.60092, 9.99255, 4.32367, 9.02991, 0.60092, 9.99255, 4.32367, 9.02991, 2.52744, 6.79456, 1.29626, 7.13208, 0.60092, 9.99255, 4.32367, 9.02991, 0.60092, 9.99255, 4.32367, 9.02991, -0.34625, 1.43024, 0.21899, 1.45544, -0.52148, 2.14539, 0.32568, 2.18384, 0.15295, 2.20294, 0.11578, 2.2049, 0, 0, 0, 0, 0, 0, 0, 0, 5.89554, 0.67639, 5.71613, -1.59509, 5.82324, -1.14081, 5.84155, -1.04309, 5.72141, 1.39154, 5.82428, -0.86716, 5.87402, -0.4068, 5.88, -0.30811, 5.72141, 1.39154, 5.82428, -0.86716, 5.87402, -0.4068, -1.39081, 5.72101, 0.86816, 5.82355, 0.40771, 5.87439, 0.30859, 5.8797, 0.86816, 5.82355, 0.40771, 5.87439, -1.39081, 5.72101, 0.86816, 5.82355, 0.40771, 5.87439, 0.30859, 5.8797, -0.25146, 5.88208, 0.40771, 5.87439, 0.30859, 5.8797, 0.26654, 5.8822, 0.86816, 5.82355, 0.40771, 5.87439, 0.30859, 5.8797, 0.26654, 5.8822, -0.25146, 5.88208, 0.40771, 5.87439, 0.30859, 5.8797, 0.26654, 5.8822, 0.86816, 5.82355, 0.30859, 5.8797, 0.26654, 5.8822, -0.25146, 5.88208, 0.30859, 5.8797, 0.26654, 5.8822]}]}, "qunz1": {"qunz1": [{"offset": 2, "vertices": [-1.27246, 3.02515, -3.63829, 8.66964, -3.00188, 7.15734, -1.56559, 3.74782, -0.29306, 0.72286, 0.7453, 0.58289, 0, 0, 0.60205, 0.24431, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.88428, -0.35582, 0.0619, 0.95118, 0.06115, 0.95124, 0.11067, 0.94674, 0.20425, 0.93105, -0.7369, -0.29652, 0.05156, 0.79263, 0.05093, 0.79268, 0.09221, 0.78893, 0.17023, 0.77586, -0.7369, -0.29652, 0.05156, 0.79263, 0.05093, 0.79268, 0.09221, 0.78893, 0.17023, 0.77586, -1.17902, -0.47447, 0.0825, 1.26823, 0.08149, 1.26831, 0.14755, 1.26231, 0.27227, 1.2414, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.1702, 0.77587, 0.11071, 0.94675, 0.20434, 0.93105, 0.06111, 0.95126, 0.11071, 0.94675, 0.20434, 0.93105, 0.06111, 0.95126, 0.11071, 0.94675, 0.20434, 0.93105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.1575, 0.1842, 0.59196, 1.01157, -1.1575, 0.1842, 0.59196, 1.01157, -1.1575, 0.1842, 0.59196, 1.01157, 0.73091, 0.91621, -1.23254, 0.37071, 0.79313, 1.01374, 0.93016, 0.8895, -1.23254, 0.37071, 0.79313, 1.01374, -1.23254, 0.37071, 0.79313, 1.01374, 0.94958, 0.86831, -1.23254, 0.37071, 0.93016, 0.8895, 0.94958, 0.86831, 1.00758, 0.80124, -1.23254, 0.37071, 0.93016, 0.8895, 0.94958, 0.86831, 1.00758, 0.80124, -1.23254, 0.37071, 0.93016, 0.8895, 0.94958, 0.86831, 1.00758, 0.80124, -1.23254, 0.37071, 0.93016, 0.8895, 0.94958, 0.86831, 1.00758, 0.80124, -1.1575, 0.1842, 0.73091, 0.91621, 0.75132, 0.89949, 0.81128, 0.84598, 0.8958, 0.75581, -1.1575, 0.1842, 0.73091, 0.91621, 0.75132, 0.89949, 0.81128, 0.84598, 0.8958, 0.75581, -1.1575, 0.1842, -1.1575, 0.1842, 0.59196, 1.01157, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.45033, 1.11883, -0.75053, 1.86474, 2.01004, 0.02205, -0.73531, 1.22329, 1.43098, 0.24154, 1.46902, 0.03217, -0.07361, 0.62375, -0.55553, 0.42856, 0.62112, 0.36252, 0.6819, 0.26833, 0.69905, 0.25183, 0.72137, 0.2025, -0.67577, 0.37958, 0.61522, 0.49223, 0.69163, 0.39782, 0.70946, 0.38129, 0.73925, 0.33113, -1.1575, 0.1842, 0.59196, 1.01157, 0.73091, 0.91621, 0.75132, 0.89949, 0.81128, 0.84598, 0, 0, 0, 0, 0, 0, 0.84294, 0.34194, 0.84294, 0.34194, 0.67721, 0.05778, -0.30022, 0.74593, 0, 0, 0, 0, 0, 0, 0, 0, 0.61793, 0.90133, 1.31176, 0.09648, -0.45033, 1.11888, 0, 0, 0, 0, 0.60205, 0.24431, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.7369, -0.29655, -0.03846, 0.7934, 0.05156, 0.79267, 0.05093, 0.79271, -0.03055, 0.63471, 0.04123, 0.63415, 0.04067, 0.63419, 1.1926, 1.64836, 1.37131, 1.50273, 1.37019, 1.50384, 1.1926, 1.64836, 1.37131, 1.50273, 1.37019, 1.50384, 1.44686, 1.43036, -0.83044, 2.06324, 2.21938, -0.14438, 2.21956, -0.1426, 2.20916, -0.25809, -1.86207, 1.64806, 2.29155, 0.96529, 2.29092, 0.96715, 2.33838, 0.84643, 2.411, 0.60993, 1.37019, 1.50384, 1.44686, 1.43036, -1.94828, 0.58592, 1.37131, 1.50273, 1.37019, 1.50384, 1.44686, 1.43036, 1.58191, 1.27954, -1.71469, 1.70739, 2.28124, 0.80674, 2.28074, 0.80859, 2.31994, 0.68862, 2.37696, 0.45474, 1.4313, 2.4551, 1.55757, 2.37711, 1.78625, 2.21059, -0.14737, -0.05929, 0.01024, 0.15849, 0.01004, 0.1585, 0.01842, 0.15773, 0.03425, 0.1551], "curve": 0.382, "c2": 0.57, "c3": 0.736}, {"time": 0.4333, "offset": 2, "vertices": [-1.27246, 3.02515, -3.39349, 8.06686, -2.75708, 6.55457, -1.27252, 3.02496], "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "offset": 2, "vertices": [-1.27246, 3.02515, -4.22406, 10.112, -3.58765, 8.5997, -2.26685, 5.47751, -0.99432, 2.45255, 2.52869, 1.97766, 0, 0, 2.04266, 0.82892, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.00024, -1.20724, 0.21002, 3.22722, 0.20746, 3.22742, 0.37549, 3.21217, 0.69299, 3.15893, -2.50018, -1.00604, 0.17493, 2.68929, 0.17279, 2.68946, 0.31287, 2.67674, 0.57758, 2.63237, -2.50018, -1.00604, 0.17493, 2.68929, 0.17279, 2.68946, 0.31287, 2.67674, 0.57758, 2.63237, -4.00024, -1.6098, 0.27991, 4.30293, 0.27649, 4.30318, 0.50061, 4.28285, 0.92377, 4.21189, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.57745, 2.63243, 0.37561, 3.21217, 0.6933, 3.15893, 0.20734, 3.22749, 0.37561, 3.21217, 0.6933, 3.15893, 0.20734, 3.22749, 0.37561, 3.21217, 0.6933, 3.15893, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.92725, 0.62497, 2.00842, 3.43211, -3.92725, 0.62497, 2.00842, 3.43211, -3.92725, 0.62497, 2.00842, 3.43211, 2.47986, 3.10858, -4.18182, 1.25775, 2.69098, 3.43947, 3.15588, 3.01795, -4.18182, 1.25775, 2.69098, 3.43947, -4.18182, 1.25775, 2.69098, 3.43947, 3.2218, 2.94605, -4.18182, 1.25775, 3.15588, 3.01795, 3.2218, 2.94605, 3.41858, 2.7185, -4.18182, 1.25775, 3.15588, 3.01795, 3.2218, 2.94605, 3.41858, 2.7185, -4.18182, 1.25775, 3.15588, 3.01795, 3.2218, 2.94605, 3.41858, 2.7185, -4.18182, 1.25775, 3.15588, 3.01795, 3.2218, 2.94605, 3.41858, 2.7185, -3.92725, 0.62497, 2.47986, 3.10858, 2.54913, 3.05183, 2.75256, 2.8703, 3.03931, 2.56436, -3.92725, 0.62497, 2.47986, 3.10858, 2.54913, 3.05183, 2.75256, 2.8703, 3.03931, 2.56436, -3.92725, 0.62497, -3.92725, 0.62497, 2.00842, 3.43211, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.52789, 3.79602, -2.54645, 6.32678, 6.81976, 0.0748, -2.49481, 4.15045, 4.8551, 0.81952, 4.98419, 0.10915, -0.24976, 2.1163, -1.88483, 1.45404, 2.10736, 1.22998, 2.3136, 0.91042, 2.37177, 0.85441, 2.44751, 0.68705, -2.29279, 1.28787, 2.08734, 1.67007, 2.34662, 1.34975, 2.4071, 1.29367, 2.50818, 1.12346, -3.92725, 0.62497, 2.00842, 3.43211, 2.47986, 3.10858, 2.54913, 3.05183, 2.75256, 2.8703, 0, 0, 0, 0, 0, 0, 2.85999, 1.16016, 2.85999, 1.16016, 2.29767, 0.19604, -1.01862, 2.53082, 0, 0, 0, 0, 0, 0, 0, 0, 2.09656, 3.05807, 4.45062, 0.32733, -1.52789, 3.7962, 0, 0, 0, 0, 2.04266, 0.82892, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.50018, -1.00616, -0.13049, 2.69189, 0.17493, 2.6894, 0.17279, 2.68956, -0.10364, 2.15347, 0.13989, 2.15157, 0.138, 2.15173, 4.04633, 5.59264, 4.65265, 5.09853, 4.64886, 5.10233, 4.04633, 5.59264, 4.65265, 5.09853, 4.64886, 5.10233, 4.909, 4.85301, -2.81757, 7.00027, 7.53003, -0.48987, 7.53064, -0.48382, 7.49536, -0.87567, -6.31775, 5.59164, 7.7749, 3.27509, 7.77277, 3.28139, 7.93378, 2.87182, 8.18018, 2.0694, 4.64886, 5.10233, 4.909, 4.85301, -6.61023, 1.98795, 4.65265, 5.09853, 4.64886, 5.10233, 4.909, 4.85301, 5.36719, 4.3413, -5.81769, 5.79294, 7.73993, 2.73717, 7.73822, 2.74344, 7.87122, 2.3364, 8.0647, 1.54287, 4.8562, 8.32981, 5.28461, 8.06519, 6.06049, 7.50023, -0.5, -0.20117, 0.03473, 0.53773, 0.03406, 0.53777, 0.0625, 0.53516, 0.11621, 0.52624], "curve": 0.243, "c3": 0.647, "c4": 0.59}, {"time": 2.5, "offset": 2, "vertices": [-1.27246, 3.02515, -3.63829, 8.66964, -3.00188, 7.15734, -1.56559, 3.74782, -0.29306, 0.72286, 0.7453, 0.58289, 0, 0, 0.60205, 0.24431, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.88428, -0.35582, 0.0619, 0.95118, 0.06115, 0.95124, 0.11067, 0.94674, 0.20425, 0.93105, -0.7369, -0.29652, 0.05156, 0.79263, 0.05093, 0.79268, 0.09221, 0.78893, 0.17023, 0.77586, -0.7369, -0.29652, 0.05156, 0.79263, 0.05093, 0.79268, 0.09221, 0.78893, 0.17023, 0.77586, -1.17902, -0.47447, 0.0825, 1.26823, 0.08149, 1.26831, 0.14755, 1.26231, 0.27227, 1.2414, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.1702, 0.77587, 0.11071, 0.94675, 0.20434, 0.93105, 0.06111, 0.95126, 0.11071, 0.94675, 0.20434, 0.93105, 0.06111, 0.95126, 0.11071, 0.94675, 0.20434, 0.93105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.1575, 0.1842, 0.59196, 1.01157, -1.1575, 0.1842, 0.59196, 1.01157, -1.1575, 0.1842, 0.59196, 1.01157, 0.73091, 0.91621, -1.23254, 0.37071, 0.79313, 1.01374, 0.93016, 0.8895, -1.23254, 0.37071, 0.79313, 1.01374, -1.23254, 0.37071, 0.79313, 1.01374, 0.94958, 0.86831, -1.23254, 0.37071, 0.93016, 0.8895, 0.94958, 0.86831, 1.00758, 0.80124, -1.23254, 0.37071, 0.93016, 0.8895, 0.94958, 0.86831, 1.00758, 0.80124, -1.23254, 0.37071, 0.93016, 0.8895, 0.94958, 0.86831, 1.00758, 0.80124, -1.23254, 0.37071, 0.93016, 0.8895, 0.94958, 0.86831, 1.00758, 0.80124, -1.1575, 0.1842, 0.73091, 0.91621, 0.75132, 0.89949, 0.81128, 0.84598, 0.8958, 0.75581, -1.1575, 0.1842, 0.73091, 0.91621, 0.75132, 0.89949, 0.81128, 0.84598, 0.8958, 0.75581, -1.1575, 0.1842, -1.1575, 0.1842, 0.59196, 1.01157, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.45033, 1.11883, -0.75053, 1.86474, 2.01004, 0.02205, -0.73531, 1.22329, 1.43098, 0.24154, 1.46902, 0.03217, -0.07361, 0.62375, -0.55553, 0.42856, 0.62112, 0.36252, 0.6819, 0.26833, 0.69905, 0.25183, 0.72137, 0.2025, -0.67577, 0.37958, 0.61522, 0.49223, 0.69163, 0.39782, 0.70946, 0.38129, 0.73925, 0.33113, -1.1575, 0.1842, 0.59196, 1.01157, 0.73091, 0.91621, 0.75132, 0.89949, 0.81128, 0.84598, 0, 0, 0, 0, 0, 0, 0.84294, 0.34194, 0.84294, 0.34194, 0.67721, 0.05778, -0.30022, 0.74593, 0, 0, 0, 0, 0, 0, 0, 0, 0.61793, 0.90133, 1.31176, 0.09648, -0.45033, 1.11888, 0, 0, 0, 0, 0.60205, 0.24431, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.7369, -0.29655, -0.03846, 0.7934, 0.05156, 0.79267, 0.05093, 0.79271, -0.03055, 0.63471, 0.04123, 0.63415, 0.04067, 0.63419, 1.1926, 1.64836, 1.37131, 1.50273, 1.37019, 1.50384, 1.1926, 1.64836, 1.37131, 1.50273, 1.37019, 1.50384, 1.44686, 1.43036, -0.83044, 2.06324, 2.21938, -0.14438, 2.21956, -0.1426, 2.20916, -0.25809, -1.86207, 1.64806, 2.29155, 0.96529, 2.29092, 0.96715, 2.33838, 0.84643, 2.411, 0.60993, 1.37019, 1.50384, 1.44686, 1.43036, -1.94828, 0.58592, 1.37131, 1.50273, 1.37019, 1.50384, 1.44686, 1.43036, 1.58191, 1.27954, -1.71469, 1.70739, 2.28124, 0.80674, 2.28074, 0.80859, 2.31994, 0.68862, 2.37696, 0.45474, 1.4313, 2.4551, 1.55757, 2.37711, 1.78625, 2.21059, -0.14737, -0.05929, 0.01024, 0.15849, 0.01004, 0.1585, 0.01842, 0.15773, 0.03425, 0.1551], "curve": 0.382, "c2": 0.57, "c3": 0.736}, {"time": 2.9333, "offset": 2, "vertices": [-1.27246, 3.02515, -3.39349, 8.06686, -2.75708, 6.55457, -1.27252, 3.02496], "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "offset": 2, "vertices": [-1.27246, 3.02515, -4.22406, 10.112, -3.58765, 8.5997, -2.26685, 5.47751, -0.99432, 2.45255, 2.52869, 1.97766, 0, 0, 2.04266, 0.82892, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.00024, -1.20724, 0.21002, 3.22722, 0.20746, 3.22742, 0.37549, 3.21217, 0.69299, 3.15893, -2.50018, -1.00604, 0.17493, 2.68929, 0.17279, 2.68946, 0.31287, 2.67674, 0.57758, 2.63237, -2.50018, -1.00604, 0.17493, 2.68929, 0.17279, 2.68946, 0.31287, 2.67674, 0.57758, 2.63237, -4.00024, -1.6098, 0.27991, 4.30293, 0.27649, 4.30318, 0.50061, 4.28285, 0.92377, 4.21189, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.57745, 2.63243, 0.37561, 3.21217, 0.6933, 3.15893, 0.20734, 3.22749, 0.37561, 3.21217, 0.6933, 3.15893, 0.20734, 3.22749, 0.37561, 3.21217, 0.6933, 3.15893, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.92725, 0.62497, 2.00842, 3.43211, -3.92725, 0.62497, 2.00842, 3.43211, -3.92725, 0.62497, 2.00842, 3.43211, 2.47986, 3.10858, -4.18182, 1.25775, 2.69098, 3.43947, 3.15588, 3.01795, -4.18182, 1.25775, 2.69098, 3.43947, -4.18182, 1.25775, 2.69098, 3.43947, 3.2218, 2.94605, -4.18182, 1.25775, 3.15588, 3.01795, 3.2218, 2.94605, 3.41858, 2.7185, -4.18182, 1.25775, 3.15588, 3.01795, 3.2218, 2.94605, 3.41858, 2.7185, -4.18182, 1.25775, 3.15588, 3.01795, 3.2218, 2.94605, 3.41858, 2.7185, -4.18182, 1.25775, 3.15588, 3.01795, 3.2218, 2.94605, 3.41858, 2.7185, -3.92725, 0.62497, 2.47986, 3.10858, 2.54913, 3.05183, 2.75256, 2.8703, 3.03931, 2.56436, -3.92725, 0.62497, 2.47986, 3.10858, 2.54913, 3.05183, 2.75256, 2.8703, 3.03931, 2.56436, -3.92725, 0.62497, -3.92725, 0.62497, 2.00842, 3.43211, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.52789, 3.79602, -2.54645, 6.32678, 6.81976, 0.0748, -2.49481, 4.15045, 4.8551, 0.81952, 4.98419, 0.10915, -0.24976, 2.1163, -1.88483, 1.45404, 2.10736, 1.22998, 2.3136, 0.91042, 2.37177, 0.85441, 2.44751, 0.68705, -2.29279, 1.28787, 2.08734, 1.67007, 2.34662, 1.34975, 2.4071, 1.29367, 2.50818, 1.12346, -3.92725, 0.62497, 2.00842, 3.43211, 2.47986, 3.10858, 2.54913, 3.05183, 2.75256, 2.8703, 0, 0, 0, 0, 0, 0, 2.85999, 1.16016, 2.85999, 1.16016, 2.29767, 0.19604, -1.01862, 2.53082, 0, 0, 0, 0, 0, 0, 0, 0, 2.09656, 3.05807, 4.45062, 0.32733, -1.52789, 3.7962, 0, 0, 0, 0, 2.04266, 0.82892, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.50018, -1.00616, -0.13049, 2.69189, 0.17493, 2.6894, 0.17279, 2.68956, -0.10364, 2.15347, 0.13989, 2.15157, 0.138, 2.15173, 4.04633, 5.59264, 4.65265, 5.09853, 4.64886, 5.10233, 4.04633, 5.59264, 4.65265, 5.09853, 4.64886, 5.10233, 4.909, 4.85301, -2.81757, 7.00027, 7.53003, -0.48987, 7.53064, -0.48382, 7.49536, -0.87567, -6.31775, 5.59164, 7.7749, 3.27509, 7.77277, 3.28139, 7.93378, 2.87182, 8.18018, 2.0694, 4.64886, 5.10233, 4.909, 4.85301, -6.61023, 1.98795, 4.65265, 5.09853, 4.64886, 5.10233, 4.909, 4.85301, 5.36719, 4.3413, -5.81769, 5.79294, 7.73993, 2.73717, 7.73822, 2.74344, 7.87122, 2.3364, 8.0647, 1.54287, 4.8562, 8.32981, 5.28461, 8.06519, 6.06049, 7.50023, -0.5, -0.20117, 0.03473, 0.53773, 0.03406, 0.53777, 0.0625, 0.53516, 0.11621, 0.52624], "curve": 0.243, "c3": 0.647, "c4": 0.59}, {"time": 5, "offset": 2, "vertices": [-1.27246, 3.02515, -3.63829, 8.66964, -3.00188, 7.15734, -1.56559, 3.74782, -0.29306, 0.72286, 0.7453, 0.58289, 0, 0, 0.60205, 0.24431, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.88428, -0.35582, 0.0619, 0.95118, 0.06115, 0.95124, 0.11067, 0.94674, 0.20425, 0.93105, -0.7369, -0.29652, 0.05156, 0.79263, 0.05093, 0.79268, 0.09221, 0.78893, 0.17023, 0.77586, -0.7369, -0.29652, 0.05156, 0.79263, 0.05093, 0.79268, 0.09221, 0.78893, 0.17023, 0.77586, -1.17902, -0.47447, 0.0825, 1.26823, 0.08149, 1.26831, 0.14755, 1.26231, 0.27227, 1.2414, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.1702, 0.77587, 0.11071, 0.94675, 0.20434, 0.93105, 0.06111, 0.95126, 0.11071, 0.94675, 0.20434, 0.93105, 0.06111, 0.95126, 0.11071, 0.94675, 0.20434, 0.93105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.1575, 0.1842, 0.59196, 1.01157, -1.1575, 0.1842, 0.59196, 1.01157, -1.1575, 0.1842, 0.59196, 1.01157, 0.73091, 0.91621, -1.23254, 0.37071, 0.79313, 1.01374, 0.93016, 0.8895, -1.23254, 0.37071, 0.79313, 1.01374, -1.23254, 0.37071, 0.79313, 1.01374, 0.94958, 0.86831, -1.23254, 0.37071, 0.93016, 0.8895, 0.94958, 0.86831, 1.00758, 0.80124, -1.23254, 0.37071, 0.93016, 0.8895, 0.94958, 0.86831, 1.00758, 0.80124, -1.23254, 0.37071, 0.93016, 0.8895, 0.94958, 0.86831, 1.00758, 0.80124, -1.23254, 0.37071, 0.93016, 0.8895, 0.94958, 0.86831, 1.00758, 0.80124, -1.1575, 0.1842, 0.73091, 0.91621, 0.75132, 0.89949, 0.81128, 0.84598, 0.8958, 0.75581, -1.1575, 0.1842, 0.73091, 0.91621, 0.75132, 0.89949, 0.81128, 0.84598, 0.8958, 0.75581, -1.1575, 0.1842, -1.1575, 0.1842, 0.59196, 1.01157, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.45033, 1.11883, -0.75053, 1.86474, 2.01004, 0.02205, -0.73531, 1.22329, 1.43098, 0.24154, 1.46902, 0.03217, -0.07361, 0.62375, -0.55553, 0.42856, 0.62112, 0.36252, 0.6819, 0.26833, 0.69905, 0.25183, 0.72137, 0.2025, -0.67577, 0.37958, 0.61522, 0.49223, 0.69163, 0.39782, 0.70946, 0.38129, 0.73925, 0.33113, -1.1575, 0.1842, 0.59196, 1.01157, 0.73091, 0.91621, 0.75132, 0.89949, 0.81128, 0.84598, 0, 0, 0, 0, 0, 0, 0.84294, 0.34194, 0.84294, 0.34194, 0.67721, 0.05778, -0.30022, 0.74593, 0, 0, 0, 0, 0, 0, 0, 0, 0.61793, 0.90133, 1.31176, 0.09648, -0.45033, 1.11888, 0, 0, 0, 0, 0.60205, 0.24431, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.7369, -0.29655, -0.03846, 0.7934, 0.05156, 0.79267, 0.05093, 0.79271, -0.03055, 0.63471, 0.04123, 0.63415, 0.04067, 0.63419, 1.1926, 1.64836, 1.37131, 1.50273, 1.37019, 1.50384, 1.1926, 1.64836, 1.37131, 1.50273, 1.37019, 1.50384, 1.44686, 1.43036, -0.83044, 2.06324, 2.21938, -0.14438, 2.21956, -0.1426, 2.20916, -0.25809, -1.86207, 1.64806, 2.29155, 0.96529, 2.29092, 0.96715, 2.33838, 0.84643, 2.411, 0.60993, 1.37019, 1.50384, 1.44686, 1.43036, -1.94828, 0.58592, 1.37131, 1.50273, 1.37019, 1.50384, 1.44686, 1.43036, 1.58191, 1.27954, -1.71469, 1.70739, 2.28124, 0.80674, 2.28074, 0.80859, 2.31994, 0.68862, 2.37696, 0.45474, 1.4313, 2.4551, 1.55757, 2.37711, 1.78625, 2.21059, -0.14737, -0.05929, 0.01024, 0.15849, 0.01004, 0.1585, 0.01842, 0.15773, 0.03425, 0.1551]}]}, "ys1": {"ys1": [{"curve": 0.25, "c3": 0.75}, {"time": 1.2333, "offset": 64, "vertices": [-7.67194, -1.17795, 3.36017, 6.99843, 1.52756, 7.61179, 0, 0, 0, 0, 0, 0, -0.89825, 6.31931, -1.00388, 6.30359, -2.5296, 5.8605, -0.89825, 6.31931, -1.00388, 6.30359, -2.5296, 5.8605, -0.89825, 6.31931, -1.00388, 6.30359, -2.5296, 5.8605, -5.85956, 0.4782, 3.78, 4.50479, 3.70395, 4.56749, 2.46118, 5.34122, -3.58221, 0.66739, 2.61734, 2.53745, -5.38556, -0.6199, 0, 0, 0, 0, -6.36542, 2.82944, 0.31853, 5.14511, -4.96509, -1.3799, 1.61969, 5.73419, -5.94666, -0.34276, 2.6839, 1.21475, -2.0246, 2.13974, 3.13116, 1.41721, -2.36194, 2.49637, -1.76846, 3.90622, -3.11316, -2.94766, -1.76846, 3.90622, -4.89178, 1.27193, -3.59265, 0.28838, -4.36084, -0.06952, -2.85236, -2.69997, -0.61783, 3.879, -1.55676, 3.60626, -7.13116, -6.74994, -3.89182, 9.01596, -3.74561, 1.61972, 3.48877, 2.11993, 2.65497, 2.27206, 2.61633, 2.31631, 1.96335, 2.89114, 0.55191, 3.83524, 0.48593, 3.84454, -0.47775, 3.84641, -7.30304, -1.5051, -5.55182, 0.15295, -2.69946, 2.85306, 3.60657, 1.55688, -1.87885, 4.15051, -8.47003, -2.61046, 2.771, 8.42146, 2.62903, 8.46698, 0.45685, 8.85492, 1.95883, 5.19827, -5.55182, 0.15295, -1.65785, 3.66217, -2.9187, -2.76337, -1.65785, 3.66217, -9.83038, -3.89703, 2.33469, 10.31557, -0.28516, 10.57303, 1.95883, 5.19827, -5.55182, 0.15295, 3.26138, 4.49696, 2.04996, 5.16351, 2.68323, 1.21521, -2.02283, 2.13899, 2.70581, 1.16943, -1.87885, 4.15051, -3.30798, -3.13168, -1.80573, 4.18317], "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "offset": 64, "vertices": [-7.67194, -1.17795, 3.36017, 6.99843, 1.52756, 7.61179, 0, 0, 0, 0, 0, 0, -0.89825, 6.31931, -1.00388, 6.30359, -2.5296, 5.8605, -0.89825, 6.31931, -1.00388, 6.30359, -2.5296, 5.8605, -0.89825, 6.31931, -1.00388, 6.30359, -2.5296, 5.8605, -5.85956, 0.4782, 3.78, 4.50479, 3.70395, 4.56749, 2.46118, 5.34122, -3.58221, 0.66739, 2.61734, 2.53745, -5.38556, -0.6199, 0, 0, 0, 0, -6.36542, 2.82944, 0.31853, 5.14511, -4.96509, -1.3799, 1.61969, 5.73419, -5.94666, -0.34276, 2.6839, 1.21475, -2.0246, 2.13974, 3.13116, 1.41721, -2.36194, 2.49637, -1.76846, 3.90622, -3.11316, -2.94766, -1.76846, 3.90622, -4.89178, 1.27193, -3.59265, 0.28838, -4.36084, -0.06952, -2.85236, -2.69997, -0.61783, 3.879, -1.55676, 3.60626, -7.13116, -6.74994, -3.89182, 9.01596, -3.74561, 1.61972, 3.48877, 2.11993, 2.65497, 2.27206, 2.61633, 2.31631, 1.96335, 2.89114, 0.55191, 3.83524, 0.48593, 3.84454, -0.47775, 3.84641, -7.30304, -1.5051, -5.55182, 0.15295, -2.69946, 2.85306, 3.60657, 1.55688, -1.87885, 4.15051, -8.47003, -2.61046, 2.771, 8.42146, 2.62903, 8.46698, 0.45685, 8.85492, 1.95883, 5.19827, -5.55182, 0.15295, -1.65785, 3.66217, -2.9187, -2.76337, -1.65785, 3.66217, -9.83038, -3.89703, 2.33469, 10.31557, -0.28516, 10.57303, 1.95883, 5.19827, -5.55182, 0.15295, 3.26138, 4.49696, 2.04996, 5.16351, 2.68323, 1.21521, -2.02283, 2.13899, 2.70581, 1.16943, -1.87885, 4.15051, -3.30798, -3.13168, -1.80573, 4.18317], "curve": 0.25, "c3": 0.75}, {"time": 5}]}, "qinz2": {"qinz2": [{"curve": 0.25, "c3": 0.75}, {"time": 1.2333, "offset": 236, "vertices": [1.13794, -2.01651, 1.12128, -2.02518, 1.11743, -2.02859, 1.12463, -2.02307, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.13794, -2.01651, 1.12128, -2.02518, 1.13794, -2.01651, 1.12128, -2.02518, 1.13794, -2.01651, 1.13794, -2.01651, 1.12128, -2.02518, 1.11743, -2.02859, 1.13794, -2.01651, 1.12128, -2.02518, 1.11743, -2.02859, 1.12463, -2.02307], "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "offset": 236, "vertices": [1.13794, -2.01651, 1.12128, -2.02518, 1.11743, -2.02859, 1.12463, -2.02307, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.13794, -2.01651, 1.12128, -2.02518, 1.13794, -2.01651, 1.12128, -2.02518, 1.13794, -2.01651, 1.13794, -2.01651, 1.12128, -2.02518, 1.11743, -2.02859, 1.13794, -2.01651, 1.12128, -2.02518, 1.11743, -2.02859, 1.12463, -2.02307], "curve": 0.25, "c3": 0.75}, {"time": 5}]}, "t7": {"t7": [{"time": 4.2667, "curve": 0.25, "c3": 0.75}, {"time": 4.4667, "vertices": [0.48193, 5.56453, 0.47052, 3.13812, -0.07095, 1.47635, -1.2388, -0.63583, 0.0892, -7.20482, 0.4339, -8.91943, 0.14893, -8.9234, 0.35468, -9.05009, 0.15088, -5.55176, -0.25354, -1.62202, -0.54221, 0.45064, 0.74747, 1.37123, -1.10941, 6.42686, 0.45355, 5.08586, -1.10052, 2.80098, -3.07034, 7.51564, -2.80078, 5.36053], "curve": 0.25, "c3": 0.75}, {"time": 4.6333}]}, "st1": {"st1": [{"offset": 122, "vertices": [1.63336, 0.78384, 0.11768, -2.42749, 0.03937, 2.42984, 0.20416, -4.20618, -4.06451, 0.27023, 0.20416, -4.20618, -4.06451, 0.27023, 0, 0, 0, 0, 2.40527, 0.11777, -0.15408, -2.32623, 1.72208, 1.68427, -0.15308, -2.40366, 1.09332, 0.0535, -0.07007, -1.0575, 0.78284, 0.76556, -0.06958, -1.09262, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.94394, -1.7019, -0.12106, -1.82327, -3.40207, -2.97845, 0.11493, -2.35721, -2.27853, 0.15173, 1.6496, -1.68744, 2.25769, 0.68713, 0, 0, -0.91144, 0.06067, 0, 0, -3.40207, -2.97845, 0.11493, -2.35721, -2.27853, 0.15173, 1.6496, -1.68744, 2.25769, 0.68713, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.15649, -3.23682, 0, 0, 0, 0, 0, 0, 0, 0, 0.15649, -3.23682, 0.05267, 3.23972, 0, 0, 0, 0, 3.27997, 0.16043, -0.21027, -3.1723, 2.34849, 2.29657, -0.20886, -3.2778, 0, 0, 0, 0, 0, 0, 0, 0, 2.35693, 0.11536, -0.15155, -2.27914, 1.68748, 1.65009, -0.15018, -2.35516, 3.01215, -3.63324, -3.82761, -2.49194, 4.66428, -0.72003, 2.78754, 3.8085, 0, 0, 0, 0, 0, 0, 0, 0, 1.45319, 1.60577, 1.38409, -1.57959, 0.02305, 2.16779, 1.43329, -1.62613, 1.88525, 0.09222, -0.12125, -1.82306, 1.34985, 1.3197, -0.12015, -1.88385], "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "offset": 238, "vertices": [-4.48145, 2.16006, 1.33112, 2.45374, -0.21881, 2.37534, 2.38644, 0.00656, -2.336, -0.81787, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.44714, -0.07257, 0.07132, 1.43784, -1.03465, -1.01447, 0.42346, -1.38538, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.41193, -0.12103, 0.11887, 2.39636, 0.70593, -2.30908, 0.14105, -0.46179, 0.00702, -0.48288, -0.09601, -0.47339, 0, 0, -1.72449, -1.6908, 0.70593, -2.30908, 0.03513, -2.41458, 0.70593, -2.30908, 0.03513, -2.41458, -0.48007, -2.36676, 0.42371, -1.38553, 0.02109, -1.44879, -0.28809, -1.42014, -0.57086, -1.3316, 0, 0, 0, 0, 0, 0, -1.72449, -1.6908, 0.03513, -2.41458, -0.48007, -2.36676], "curve": 0.25, "c3": 0.75}, {"time": 2.5, "offset": 122, "vertices": [1.63336, 0.78384, 0.11768, -2.42749, 0.03937, 2.42984, 0.20416, -4.20618, -4.06451, 0.27023, 0.20416, -4.20618, -4.06451, 0.27023, 0, 0, 0, 0, 2.40527, 0.11777, -0.15408, -2.32623, 1.72208, 1.68427, -0.15308, -2.40366, 1.09332, 0.0535, -0.07007, -1.0575, 0.78284, 0.76556, -0.06958, -1.09262, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.94394, -1.7019, -0.12106, -1.82327, -3.40207, -2.97845, 0.11493, -2.35721, -2.27853, 0.15173, 1.6496, -1.68744, 2.25769, 0.68713, 0, 0, -0.91144, 0.06067, 0, 0, -3.40207, -2.97845, 0.11493, -2.35721, -2.27853, 0.15173, 1.6496, -1.68744, 2.25769, 0.68713, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.15649, -3.23682, 0, 0, 0, 0, 0, 0, 0, 0, 0.15649, -3.23682, 0.05267, 3.23972, 0, 0, 0, 0, 3.27997, 0.16043, -0.21027, -3.1723, 2.34849, 2.29657, -0.20886, -3.2778, 0, 0, 0, 0, 0, 0, 0, 0, 2.35693, 0.11536, -0.15155, -2.27914, 1.68748, 1.65009, -0.15018, -2.35516, 3.01215, -3.63324, -3.82761, -2.49194, 4.66428, -0.72003, 2.78754, 3.8085, 0, 0, 0, 0, 0, 0, 0, 0, 1.45319, 1.60577, 1.38409, -1.57959, 0.02305, 2.16779, 1.43329, -1.62613, 1.88525, 0.09222, -0.12125, -1.82306, 1.34985, 1.3197, -0.12015, -1.88385], "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "offset": 238, "vertices": [-4.48145, 2.16006, 1.33112, 2.45374, -0.21881, 2.37534, 2.38644, 0.00656, -2.336, -0.81787, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.44714, -0.07257, 0.07132, 1.43784, -1.03465, -1.01447, 0.42346, -1.38538, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.41193, -0.12103, 0.11887, 2.39636, 0.70593, -2.30908, 0.14105, -0.46179, 0.00702, -0.48288, -0.09601, -0.47339, 0, 0, -1.72449, -1.6908, 0.70593, -2.30908, 0.03513, -2.41458, 0.70593, -2.30908, 0.03513, -2.41458, -0.48007, -2.36676, 0.42371, -1.38553, 0.02109, -1.44879, -0.28809, -1.42014, -0.57086, -1.3316, 0, 0, 0, 0, 0, 0, -1.72449, -1.6908, 0.03513, -2.41458, -0.48007, -2.36676], "curve": 0.25, "c3": 0.75}, {"time": 5, "offset": 122, "vertices": [1.63336, 0.78384, 0.11768, -2.42749, 0.03937, 2.42984, 0.20416, -4.20618, -4.06451, 0.27023, 0.20416, -4.20618, -4.06451, 0.27023, 0, 0, 0, 0, 2.40527, 0.11777, -0.15408, -2.32623, 1.72208, 1.68427, -0.15308, -2.40366, 1.09332, 0.0535, -0.07007, -1.0575, 0.78284, 0.76556, -0.06958, -1.09262, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.94394, -1.7019, -0.12106, -1.82327, -3.40207, -2.97845, 0.11493, -2.35721, -2.27853, 0.15173, 1.6496, -1.68744, 2.25769, 0.68713, 0, 0, -0.91144, 0.06067, 0, 0, -3.40207, -2.97845, 0.11493, -2.35721, -2.27853, 0.15173, 1.6496, -1.68744, 2.25769, 0.68713, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.15649, -3.23682, 0, 0, 0, 0, 0, 0, 0, 0, 0.15649, -3.23682, 0.05267, 3.23972, 0, 0, 0, 0, 3.27997, 0.16043, -0.21027, -3.1723, 2.34849, 2.29657, -0.20886, -3.2778, 0, 0, 0, 0, 0, 0, 0, 0, 2.35693, 0.11536, -0.15155, -2.27914, 1.68748, 1.65009, -0.15018, -2.35516, 3.01215, -3.63324, -3.82761, -2.49194, 4.66428, -0.72003, 2.78754, 3.8085, 0, 0, 0, 0, 0, 0, 0, 0, 1.45319, 1.60577, 1.38409, -1.57959, 0.02305, 2.16779, 1.43329, -1.62613, 1.88525, 0.09222, -0.12125, -1.82306, 1.34985, 1.3197, -0.12015, -1.88385]}]}, "t4": {"t4": [{"curve": 0.25, "c3": 0.75}, {"time": 1.2333, "offset": 176, "vertices": [-1.53706, -0.39621, 0, 0, 0, 0, 0, 0, -1.56096, -0.33258, -1.53702, -0.3963, -1.56096, -0.33258, -1.53702, -0.3963, -1.52774, -0.42084, 0.91454, -3.60355, 1.14288, -3.56525, -3.12203, -0.66516, -3.0741, -0.79251, -3.05562, -0.84201, 0.91454, -3.60355, 1.14288, -3.56525, -2.23003, -0.47513, -2.19579, -0.5661, 0.91454, -3.60355, -0.45682, 1.80203, -0.57129, 1.78204, 0.91454, -3.60355, -0.79953, 3.15366, -0.99991, 3.11874, 0, 0, 0, 0, -0.57106, 2.25259, 1.16907, 1.98047], "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "offset": 176, "vertices": [-1.53706, -0.39621, 0, 0, 0, 0, 0, 0, -1.56096, -0.33258, -1.53702, -0.3963, -1.56096, -0.33258, -1.53702, -0.3963, -1.52774, -0.42084, 0.91454, -3.60355, 1.14288, -3.56525, -3.12203, -0.66516, -3.0741, -0.79251, -3.05562, -0.84201, 0.91454, -3.60355, 1.14288, -3.56525, -2.23003, -0.47513, -2.19579, -0.5661, 0.91454, -3.60355, -0.45682, 1.80203, -0.57129, 1.78204, 0.91454, -3.60355, -0.79953, 3.15366, -0.99991, 3.11874, 0, 0, 0, 0, -0.57106, 2.25259, 1.16907, 1.98047], "curve": 0.25, "c3": 0.75}, {"time": 5}]}}}}, "animation2": {"slots": {"bg": {"attachment": [{"name": null}]}}, "bones": {"st2": {"rotate": [{"angle": 0.03, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 1.2, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 2.5, "angle": 0.03, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 2.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "angle": 1.2, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 5, "angle": 0.03}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 3.6, "y": -2.66, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 3.6, "y": -2.66, "curve": 0.25, "c3": 0.75}, {"time": 5}]}, "st4": {"translate": [{"x": -2.86, "y": -2.8, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": -1.04, "y": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": -2.86, "y": -2.8, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "x": -1.04, "y": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": -2.86, "y": -2.8}]}, "st5": {"translate": [{"x": 0.42, "y": -0.08, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": 4.62, "y": -0.84, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 2.5, "x": 0.42, "y": -0.08, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "x": 4.62, "y": -0.84, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 5, "x": 0.42, "y": -0.08}]}, "zs1": {"rotate": [{"angle": 0.32, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 1.2, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 2.5, "angle": 0.32, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 2.9333, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": 1.2, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 5, "angle": 0.32}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": -2, "y": 5.66, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "x": -2, "y": 5.66, "curve": 0.25, "c3": 0.75}, {"time": 5}]}, "zs2": {"rotate": [{"angle": -1.15, "curve": 0.333, "c2": 0.33, "c3": 0.678, "c4": 0.7}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": -2.4, "curve": 0.257, "c3": 0.619, "c4": 0.46}, {"time": 2.5, "angle": -1.15, "curve": 0.333, "c2": 0.33, "c3": 0.678, "c4": 0.7}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.2333, "angle": -2.4, "curve": 0.257, "c3": 0.619, "c4": 0.46}, {"time": 5, "angle": -1.15}]}, "zs4": {"rotate": [{"angle": 0.88, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": 2.4, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.5, "angle": 0.88, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.2333, "angle": 2.4, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5, "angle": 0.88}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": -3.58, "y": 5.22, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": -3.58, "y": 5.22, "curve": 0.25, "c3": 0.75}, {"time": 5}], "scale": [{"x": 0.996, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "x": 0.98, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 2.5, "x": 0.996, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 2.8667, "curve": 0.25, "c3": 0.75}, {"time": 4.1, "x": 0.98, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 5, "x": 0.996}]}, "ys1": {"rotate": [{"angle": -1.7, "curve": 0.339, "c2": 0.35, "c3": 0.676, "c4": 0.7}, {"time": 0.2, "angle": -0.96, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": -3.6, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 2.5, "angle": -1.7, "curve": 0.339, "c2": 0.35, "c3": 0.676, "c4": 0.7}, {"time": 2.7, "angle": -0.96, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 3.0667, "curve": 0.25, "c3": 0.75}, {"time": 4.4, "angle": -3.6, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 5, "angle": -1.7}], "translate": [{"x": -3.63, "y": 0.23, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.2, "x": -3.99, "y": 0.25, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "curve": 0.243, "c3": 0.692, "c4": 0.76}, {"time": 2.5, "x": -3.63, "y": 0.23, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 2.7, "x": -3.99, "y": 0.25, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "curve": 0.243, "c3": 0.692, "c4": 0.76}, {"time": 5, "x": -3.63, "y": 0.23}]}, "ys2": {"rotate": [{"angle": 2.89, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 10.8, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 2.5, "angle": 2.89, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 2.9333, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": 10.8, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 5, "angle": 2.89}]}, "qunz1": {"rotate": [{"angle": 0.05, "curve": 0.35, "c2": 0.65, "c3": 0.684}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": 2.4, "curve": 0.247, "c3": 0.727, "c4": 0.9}, {"time": 2.5, "angle": 0.05, "curve": 0.35, "c2": 0.65, "c3": 0.684}, {"time": 2.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.8, "angle": 2.4, "curve": 0.247, "c3": 0.727, "c4": 0.9}, {"time": 5, "angle": 0.05}], "translate": [{"x": -0.02, "y": 0.03, "curve": 0.35, "c2": 0.65, "c3": 0.684}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "x": -0.91, "y": 1.79, "curve": 0.247, "c3": 0.727, "c4": 0.9}, {"time": 2.5, "x": -0.02, "y": 0.03, "curve": 0.35, "c2": 0.65, "c3": 0.684}, {"time": 2.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.8, "x": -0.91, "y": 1.79, "curve": 0.247, "c3": 0.727, "c4": 0.9}, {"time": 5, "x": -0.02, "y": 0.03}]}, "zt1": {"rotate": [{"angle": -14.4, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -14.4, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "angle": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -14.4}]}, "st1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 5}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": 2, "y": -2, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "x": 2, "y": -2, "curve": 0.25, "c3": 0.75}, {"time": 5}]}, "yt1": {"rotate": [{"angle": 16.14, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 16.14, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 16.14}]}, "boz1": {"rotate": [{"angle": -0.21, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -2.4, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 2.5, "angle": -0.21, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "angle": -2.4, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 5, "angle": -0.21}], "translate": [{"x": -0.03, "y": -0.03, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": 1.07, "y": -4.59, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 2.5, "x": -0.03, "y": -0.03, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "x": 1.07, "y": -4.59, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 5, "x": -0.03, "y": -0.03}]}, "ys4": {"rotate": [{"angle": 0.5, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 0.4, "angle": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": 15.6, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 2.5, "angle": 0.5, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 2.9, "angle": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "angle": 15.6, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 5, "angle": 0.5}], "shear": [{"curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "x": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 5}]}, "t1": {"rotate": [{"angle": -0.42, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -2.4, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 2.5, "angle": -0.42, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 2.8, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "angle": -2.4, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 5, "angle": -0.42}]}, "st3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 5}]}, "st9": {"rotate": [{"angle": -0.16, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2333, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 1.4333, "angle": -1.17, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 1.5, "angle": -1.2, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.5, "angle": -0.16, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.7333, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 3.9333, "angle": -1.17, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 4, "angle": -1.2, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5, "angle": -0.16}], "translate": [{"x": 1.65, "y": 1.83, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2333, "x": 1.9, "y": 2.1, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 1.4333, "x": 0.11, "y": 0.06, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 1.5, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.5, "x": 1.65, "y": 1.83, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.7333, "x": 1.9, "y": 2.1, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 3.9333, "x": 0.11, "y": 0.06, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 4, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5, "x": 1.65, "y": 1.83}], "scale": [{"x": 1.033, "y": 1.033, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.3, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 2.5, "x": 1.033, "y": 1.033, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 2.8, "x": 1.04, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 5, "x": 1.033, "y": 1.033}]}, "t6": {"rotate": [{"angle": -0.16, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -1.26, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.5, "angle": -0.16, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.7333, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -1.26, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5, "angle": -0.16}], "scale": [{"x": 0.992, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 0.94, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.5, "x": 0.992, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.7333, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 0.94, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5, "x": 0.992}], "shear": [{"y": 0.46, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "y": 4.74, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.5, "y": 0.46, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.7333, "curve": 0.25, "c3": 0.75}, {"time": 4, "y": 4.74, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5, "y": 0.46}]}, "t7": {"rotate": [{"angle": -0.4, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": -1.26, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 2.5, "angle": -0.4, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 2.9667, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "angle": -1.26, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 5, "angle": -0.4}], "shear": [{"y": 1.12, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "y": 3.54, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 2.5, "y": 1.12, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 2.9667, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "y": 3.54, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 5, "y": 1.12}]}, "t8": {"rotate": [{"angle": -0.73, "curve": 0.368, "c2": 0.47, "c3": 0.753}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": -1.26, "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 2.5, "angle": -0.73, "curve": 0.368, "c2": 0.47, "c3": 0.753}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 4.4667, "angle": -1.26, "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 5, "angle": -0.73}], "shear": [{"y": 2.05, "curve": 0.368, "c2": 0.47, "c3": 0.753}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "y": 3.54, "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 2.5, "y": 2.05, "curve": 0.368, "c2": 0.47, "c3": 0.753}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 4.4667, "y": 3.54, "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 5, "y": 2.05}]}, "t9": {"rotate": [{"angle": 0.06, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 0.7, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 2.5, "angle": 0.06, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "angle": 0.7, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 5, "angle": 0.06}], "shear": [{"x": 0.11, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": 1.2, "y": 1.2, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 2.5, "x": 0.11, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "x": 1.2, "y": 1.2, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 5, "x": 0.11}]}, "t10": {"rotate": [{"angle": 0.19, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 0.7, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 2.5, "angle": 0.19, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 2.9333, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": 0.7, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 5, "angle": 0.19}], "shear": [{"x": 0.32, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.2, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 2.5, "x": 0.32, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 2.9333, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "x": 1.2, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 5, "x": 0.32}]}, "t11": {"rotate": [{"angle": 0.37, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": 0.7, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 2.5, "angle": 0.37, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "angle": 0.7, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 5, "angle": 0.37}], "shear": [{"x": 0.63, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "x": 1.2, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 2.5, "x": 0.63, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "x": 1.2, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 5, "x": 0.63}]}, "t12": {"rotate": [{"angle": 0.52, "curve": 0.349, "c2": 0.39, "c3": 0.757}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "angle": 0.7, "curve": 0.266, "c3": 0.618, "c4": 0.42}, {"time": 2.5, "angle": 0.52, "curve": 0.349, "c2": 0.39, "c3": 0.757}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6, "angle": 0.7, "curve": 0.266, "c3": 0.618, "c4": 0.42}, {"time": 5, "angle": 0.52}], "shear": [{"x": 0.88, "curve": 0.349, "c2": 0.39, "c3": 0.757}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "x": 1.2, "curve": 0.266, "c3": 0.618, "c4": 0.42}, {"time": 2.5, "x": 0.88, "curve": 0.349, "c2": 0.39, "c3": 0.757}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6, "x": 1.2, "curve": 0.266, "c3": 0.618, "c4": 0.42}, {"time": 5, "x": 0.88}]}, "t5": {"rotate": [{"angle": -0.58, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -4.45, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.5, "angle": -0.58, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.7333, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -4.45, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5, "angle": -0.58}], "shear": [{"y": -0.62, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "y": -4.8, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.5, "y": -0.62, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.7333, "curve": 0.25, "c3": 0.75}, {"time": 4, "y": -4.8, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5, "y": -0.62}]}, "t18": {"rotate": [{"angle": -1.19, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": -3.25, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.5, "angle": -1.19, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.2333, "angle": -3.25, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5, "angle": -1.19}], "shear": [{"y": -1.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "y": -4.8, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.5, "y": -1.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.2333, "y": -4.8, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5, "y": -1.77}]}, "t19": {"rotate": [{"angle": -1.29, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -2.05, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.5, "angle": -1.29, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.2333, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -2.05, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5, "angle": -1.29}], "shear": [{"y": -3.03, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 2, "y": -4.8, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.5, "y": -3.03, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.2333, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "y": -4.8, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5, "y": -3.03}]}, "t20": {"rotate": [{"angle": -0.74, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": -0.85, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.5, "angle": -0.74, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 4.7333, "angle": -0.85, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5, "angle": -0.74}], "shear": [{"y": -4.18, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "y": -4.8, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.5, "y": -4.18, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 4.7333, "y": -4.8, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 5, "y": -4.18}]}, "t4": {"rotate": [{"angle": -1.84, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 0.1667, "angle": -1.02, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": -5.85, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 2.5, "angle": -1.84, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 2.6667, "angle": -1.02, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 2.9667, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "angle": -5.85, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 5, "angle": -1.84}], "shear": [{"y": -1.13, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 0.1667, "y": -0.62, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "y": -3.6, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 2.5, "y": -1.13, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 2.6667, "y": -0.62, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 2.9667, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "y": -3.6, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 5, "y": -1.13}]}, "t15": {"rotate": [{"angle": -3.7, "curve": 0.33, "c2": 0.32, "c3": 0.666, "c4": 0.66}, {"time": 0.1667, "angle": -2.77, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -5.85, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.5, "angle": -3.7, "curve": 0.33, "c2": 0.32, "c3": 0.666, "c4": 0.66}, {"time": 2.6667, "angle": -2.77, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 3.2333, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -5.85, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5, "angle": -3.7}], "shear": [{"y": -2.28, "curve": 0.33, "c2": 0.32, "c3": 0.666, "c4": 0.66}, {"time": 0.1667, "y": -1.7, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 2, "y": -3.6, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.5, "y": -2.28, "curve": 0.33, "c2": 0.32, "c3": 0.666, "c4": 0.66}, {"time": 2.6667, "y": -1.7, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 3.2333, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "y": -3.6, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5, "y": -2.28}]}, "t16": {"rotate": [{"angle": -5.33, "curve": 0.315, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 0.1667, "angle": -4.58, "curve": 0.34, "c2": 0.36, "c3": 0.757}, {"time": 1.0333, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -5.85, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 2.5, "angle": -5.33, "curve": 0.315, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 2.6667, "angle": -4.58, "curve": 0.34, "c2": 0.36, "c3": 0.757}, {"time": 3.5333, "curve": 0.25, "c3": 0.75}, {"time": 4.7667, "angle": -5.85, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 5, "angle": -5.33}], "shear": [{"y": -3.28, "curve": 0.315, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 0.1667, "y": -2.82, "curve": 0.34, "c2": 0.36, "c3": 0.757}, {"time": 1.0333, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "y": -3.6, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 2.5, "y": -3.28, "curve": 0.315, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 2.6667, "y": -2.82, "curve": 0.34, "c2": 0.36, "c3": 0.757}, {"time": 3.5333, "curve": 0.25, "c3": 0.75}, {"time": 4.7667, "y": -3.6, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 5, "y": -3.28}]}, "t17": {"rotate": [{"angle": -5.38}, {"time": 0.0667, "angle": -5.85, "curve": 0.321, "c3": 0.655, "c4": 0.34}, {"time": 0.1667, "angle": -5.77, "curve": 0.267, "c2": 0.07, "c3": 0.752}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -5.38}, {"time": 2.5667, "angle": -5.85, "curve": 0.321, "c3": 0.655, "c4": 0.34}, {"time": 2.6667, "angle": -5.77, "curve": 0.267, "c2": 0.07, "c3": 0.752}, {"time": 3.9, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -5.38}], "shear": [{"y": -3.5, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.0667, "y": -3.6, "curve": 0.321, "c3": 0.655, "c4": 0.34}, {"time": 0.1667, "y": -3.55, "curve": 0.267, "c2": 0.07, "c3": 0.752}, {"time": 1.4, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 2.5, "y": -3.5, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 2.5667, "y": -3.6, "curve": 0.321, "c3": 0.655, "c4": 0.34}, {"time": 2.6667, "y": -3.55, "curve": 0.267, "c2": 0.07, "c3": 0.752}, {"time": 3.9, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 5, "y": -3.5}]}, "t3": {"rotate": [{"angle": 0.24, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 1.41, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 2.5, "angle": 0.24, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "angle": 1.41, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 5, "angle": 0.24}]}, "t22": {"rotate": [{"angle": 0.7, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 2.61, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 2.5, "angle": 0.7, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 2.9333, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": 2.61, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 5, "angle": 0.7}]}, "t23": {"rotate": [{"angle": 1.24, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": 2.61, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 2.5, "angle": 1.24, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 3.0667, "curve": 0.25, "c3": 0.75}, {"time": 4.4, "angle": 2.61, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 5, "angle": 1.24}]}, "t21": {"rotate": [{"angle": 0.57, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": 2.61, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 2.5, "angle": 0.57, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "angle": 2.61, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 5, "angle": 0.57}]}, "t13": {"translate": [{"x": -0.04, "y": -0.18, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": -0.63, "y": -2.56, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2.5, "x": -0.04, "y": -0.18, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "x": -0.63, "y": -2.56, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 5, "x": -0.04, "y": -0.18}], "scale": [{"x": 0.944, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1667, "x": 0.94, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2.5, "x": 0.944, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 2.6667, "x": 0.94, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 5, "x": 0.944}]}, "t14": {"translate": [{"x": -0.08, "y": -0.14, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": -1.15, "y": -1.96, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2.5, "x": -0.08, "y": -0.14, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "x": -1.15, "y": -1.96, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 5, "x": -0.08, "y": -0.14}], "scale": [{"x": 1.004, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": 1.06, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2.5, "x": 1.004, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "x": 1.06, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 5, "x": 1.004}]}, "yt2": {"rotate": [{"angle": -11.69, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5, "angle": -6.62, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.2333, "angle": -15.33, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.7333, "angle": -20.4, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.5, "angle": -11.69, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3, "angle": -6.62, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.7333, "angle": -15.33, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.2333, "angle": -20.4, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5, "angle": -11.69}], "scale": [{"x": 0.92}]}, "zt2": {"rotate": [{"angle": -6.41, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 1.0667, "angle": -29.35, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": -4.92, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 2.5, "angle": -6.41, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 3.5667, "angle": -29.35, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "angle": -4.92, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 5, "angle": -6.41}], "scale": [{"x": 0.82}]}, "zs5": {"rotate": [{"angle": -1.26, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -9.67, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.5, "angle": -1.26, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.7333, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -9.67, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5, "angle": -1.26}]}, "zs6": {"rotate": [{"angle": 1.28, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": 5.88, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 2.5, "angle": 1.28, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "angle": 5.88, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 5, "angle": 1.28}]}, "zuiz7": {"rotate": [{"angle": 1.92, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": -1.86, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 1.92, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "angle": -1.86, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 1.92}]}, "zuiz8": {"rotate": [{"angle": 1.58, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.2, "angle": 1.92, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -1.86, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 2.5, "angle": 1.58, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 2.7, "angle": 1.92, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "angle": -1.86, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 5, "angle": 1.58}]}, "zuiz9": {"rotate": [{"angle": 0.91, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 0.4333, "angle": 1.92, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -1.86, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 2.5, "angle": 0.91, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 2.9333, "angle": 1.92, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": -1.86, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 5, "angle": 0.91}]}, "zuiz10": {"rotate": [{"angle": 0.13, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 0.5667, "angle": 1.92, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": -1.86, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 2.5, "angle": 0.13, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 3.0667, "angle": 1.92, "curve": 0.25, "c3": 0.75}, {"time": 4.4, "angle": -1.86, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 5, "angle": 0.13}]}, "zuiz11": {"rotate": [{"angle": -0.66, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 0.7667, "angle": 1.92, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": -1.86, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 2.5, "angle": -0.66, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 3.2667, "angle": 1.92, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "angle": -1.86, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 5, "angle": -0.66}]}, "zs7": {"rotate": [{"angle": 4.82, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.2, "angle": 5.37, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 1.2333, "angle": -0.58, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 1.4667, "angle": -0.15, "curve": 0.243, "c3": 0.688, "c4": 0.74}, {"time": 2.5, "angle": 4.82, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 2.7, "angle": 5.37, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 3.7333, "angle": -0.58, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 3.9667, "angle": -0.15, "curve": 0.243, "c3": 0.688, "c4": 0.74}, {"time": 5, "angle": 4.82}], "translate": [{"x": -0.23, "y": 2.67, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 0.6667, "x": -0.5, "y": 5.98, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 2.5, "x": -0.23, "y": 2.67, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 3.1667, "x": -0.5, "y": 5.98, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 5, "x": -0.23, "y": 2.67}], "scale": [{"y": 1.057, "curve": 0.286, "c2": 0.29, "c3": 0.75}, {"time": 0.6667, "y": 1.12, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "curve": 0.25, "c3": 0.821, "c4": 0.81}, {"time": 2.5, "y": 1.057, "curve": 0.286, "c2": 0.29, "c3": 0.75}, {"time": 3.1667, "y": 1.12, "curve": 0.25, "c3": 0.75}, {"time": 4.4, "curve": 0.25, "c3": 0.821, "c4": 0.81}, {"time": 5, "y": 1.057}]}, "zs8": {"rotate": [{"angle": 2.88}, {"time": 0.4, "angle": 5.37, "curve": 0.243, "c3": 0.655, "c4": 0.62}, {"time": 1.2333, "angle": -0.66, "curve": 0.349, "c2": 0.39, "c3": 0.686, "c4": 0.74}, {"time": 1.4333, "angle": -1.78, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 1.6333, "angle": -2.55}, {"time": 2.5, "angle": 2.88}, {"time": 2.9, "angle": 5.37, "curve": 0.243, "c3": 0.655, "c4": 0.62}, {"time": 3.7333, "angle": -0.66, "curve": 0.349, "c2": 0.39, "c3": 0.686, "c4": 0.74}, {"time": 3.9333, "angle": -1.78, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1333, "angle": -2.55}, {"time": 5, "angle": 2.88}]}, "zs9": {"rotate": [{"angle": 1.98}, {"time": 0.5333, "angle": 5.37, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 1.2333, "angle": 0.66, "curve": 0.353, "c2": 0.4, "c3": 0.698, "c4": 0.77}, {"time": 1.5667, "angle": -1.78, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 1.7667, "angle": -2.55}, {"time": 2.5, "angle": 1.98}, {"time": 3.0333, "angle": 5.37, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 3.7333, "angle": 0.66, "curve": 0.353, "c2": 0.4, "c3": 0.698, "c4": 0.77}, {"time": 4.0667, "angle": -1.78, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.2667, "angle": -2.55}, {"time": 5, "angle": 1.98}]}, "zs10": {"rotate": [{"angle": 0.85}, {"time": 0.7, "angle": 5.37, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 1.2333, "angle": 2.07, "curve": 0.346, "c2": 0.37, "c3": 0.704, "c4": 0.79}, {"time": 1.7333, "angle": -1.78, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 1.9667, "angle": -2.55}, {"time": 2.5, "angle": 0.85}, {"time": 3.2, "angle": 5.37, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 3.7333, "angle": 2.07, "curve": 0.346, "c2": 0.37, "c3": 0.704, "c4": 0.79}, {"time": 4.2333, "angle": -1.78, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.4667, "angle": -2.55}, {"time": 5, "angle": 0.85}]}, "zs11": {"rotate": [{"angle": -0.22}, {"time": 0.8667, "angle": 5.37, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.2333, "angle": 3.43, "curve": 0.328, "c2": 0.32, "c3": 0.705, "c4": 0.79}, {"time": 1.9333, "angle": -1.78, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1333, "angle": -2.55}, {"time": 2.5, "angle": -0.22}, {"time": 3.3667, "angle": 5.37, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 3.7333, "angle": 3.43, "curve": 0.328, "c2": 0.32, "c3": 0.705, "c4": 0.79}, {"time": 4.4333, "angle": -1.78, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.6333, "angle": -2.55}, {"time": 5, "angle": -0.22}]}, "qinz2": {"rotate": [{"angle": -1, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1667, "angle": -1.16, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": 2.34, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2.5, "angle": -1, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 2.6667, "angle": -1.16, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "angle": 2.34, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 5, "angle": -1}]}, "qinz3": {"rotate": [{"angle": -0.61, "curve": 0.355, "c2": 0.42, "c3": 0.693, "c4": 0.77}, {"time": 0.2, "angle": -1, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.4, "angle": -1.16, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": 1.14, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.5, "angle": -0.61, "curve": 0.355, "c2": 0.42, "c3": 0.693, "c4": 0.77}, {"time": 2.7, "angle": -1, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 2.9, "angle": -1.16, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "angle": 1.14, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 5, "angle": -0.61}]}, "qinz4": {"rotate": [{"angle": -0.22, "curve": 0.357, "c2": 0.42, "c3": 0.705, "c4": 0.8}, {"time": 0.4, "angle": -1, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.5333, "angle": -1.16, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": 1.14, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2.5, "angle": -0.22, "curve": 0.357, "c2": 0.42, "c3": 0.705, "c4": 0.8}, {"time": 2.9, "angle": -1, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 3.0333, "angle": -1.16, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "angle": 1.14, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 5, "angle": -0.22}]}, "qinz5": {"rotate": [{"angle": 0.18, "curve": 0.349, "c2": 0.39, "c3": 0.712, "c4": 0.82}, {"time": 0.5333, "angle": -1, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.7, "angle": -1.16, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": 1.14, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 2.5, "angle": 0.18, "curve": 0.349, "c2": 0.39, "c3": 0.712, "c4": 0.82}, {"time": 3.0333, "angle": -1, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 3.2, "angle": -1.16, "curve": 0.25, "c3": 0.75}, {"time": 4.4667, "angle": 1.14, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 5, "angle": 0.18}]}, "zuiz1": {"rotate": [{"angle": 0.08, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 0.4, "angle": -1.41, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.6333, "angle": -1.95, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": 2.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.5, "angle": 0.08, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 2.9, "angle": -1.41, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.1333, "angle": -1.95, "curve": 0.25, "c3": 0.75}, {"time": 4.4, "angle": 2.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5, "angle": 0.08}]}, "zuiz2": {"rotate": [{"angle": 0.97, "curve": 0.324, "c2": 0.31, "c3": 0.671, "c4": 0.68}, {"time": 0.4, "angle": -0.64, "curve": 0.348, "c2": 0.39, "c3": 0.686, "c4": 0.73}, {"time": 0.5667, "angle": -1.41, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.8333, "angle": -1.95, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": 2.14, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2.5, "angle": 0.97, "curve": 0.324, "c2": 0.31, "c3": 0.671, "c4": 0.68}, {"time": 2.9, "angle": -0.64, "curve": 0.348, "c2": 0.39, "c3": 0.686, "c4": 0.73}, {"time": 3.0667, "angle": -1.41, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.3333, "angle": -1.95, "curve": 0.25, "c3": 0.75}, {"time": 4.5667, "angle": 2.14, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 5, "angle": 0.97}]}, "zuiz3": {"rotate": [{"angle": 1.75, "curve": 0.3, "c2": 0.22, "c3": 0.648, "c4": 0.6}, {"time": 0.4, "angle": 0.26, "curve": 0.345, "c2": 0.37, "c3": 0.696, "c4": 0.76}, {"time": 0.7667, "angle": -1.41, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.0333, "angle": -1.95, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 2.14, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2.5, "angle": 1.75, "curve": 0.3, "c2": 0.22, "c3": 0.648, "c4": 0.6}, {"time": 2.9, "angle": 0.26, "curve": 0.345, "c2": 0.37, "c3": 0.696, "c4": 0.76}, {"time": 3.2667, "angle": -1.41, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.5333, "angle": -1.95, "curve": 0.25, "c3": 0.75}, {"time": 4.7667, "angle": 2.14, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 5, "angle": 1.75}]}, "zuiz4": {"rotate": [{"angle": 2.14, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.4, "angle": 1.14, "curve": 0.326, "c2": 0.31, "c3": 0.697, "c4": 0.76}, {"time": 1, "angle": -1.41, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.2333, "angle": -1.95, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 2.14, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.9, "angle": 1.14, "curve": 0.326, "c2": 0.31, "c3": 0.697, "c4": 0.76}, {"time": 3.5, "angle": -1.41, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.7333, "angle": -1.95, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 2.14}]}, "zuiz5": {"rotate": [{"angle": 1.46}, {"time": 0.2, "angle": 2.14, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 0.4, "angle": 1.87, "curve": 0.288, "c2": 0.18, "c3": 0.691, "c4": 0.75}, {"time": 1.2, "angle": -1.41, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.4667, "angle": -1.95, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 1.46}, {"time": 2.7, "angle": 2.14, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 2.9, "angle": 1.87, "curve": 0.288, "c2": 0.18, "c3": 0.691, "c4": 0.75}, {"time": 3.7, "angle": -1.41, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.9667, "angle": -1.95, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 1.46}]}, "zuiz6": {"rotate": [{"angle": 0.98, "curve": 0.375, "c2": 0.52, "c3": 0.724, "c4": 0.92}, {"time": 0.4, "angle": 2.09, "curve": 0.344, "c2": 0.66, "c3": 0.677}, {"time": 0.4333, "angle": 2.14, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.4333, "angle": -1.41, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.6667, "angle": -1.95, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2.5, "angle": 0.98, "curve": 0.375, "c2": 0.52, "c3": 0.724, "c4": 0.92}, {"time": 2.9, "angle": 2.09, "curve": 0.344, "c2": 0.66, "c3": 0.677}, {"time": 2.9333, "angle": 2.14, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.9333, "angle": -1.41, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.1667, "angle": -1.95, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 5, "angle": 0.98}]}, "weib": {"shear": [{"y": -6.23, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.3333, "y": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "y": -2.4, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 2.5, "y": -6.23, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 2.8333, "y": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "y": -2.4, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 5, "y": -6.23}]}, "weib2": {"shear": [{"y": -5.45, "curve": 0.342, "c2": 0.36, "c3": 0.678, "c4": 0.7}, {"time": 0.1667, "y": -6.23, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.5, "y": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "y": -2.4, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.5, "y": -5.45, "curve": 0.342, "c2": 0.36, "c3": 0.678, "c4": 0.7}, {"time": 2.6667, "y": -6.23, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 3, "y": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 4.2333, "y": -2.4, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5, "y": -5.45}]}, "weib3": {"shear": [{"y": -4.6, "curve": 0.34, "c2": 0.35, "c3": 0.684, "c4": 0.72}, {"time": 0.3333, "y": -6.23, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.6667, "y": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "y": -2.4, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 2.5, "y": -4.6, "curve": 0.34, "c2": 0.35, "c3": 0.684, "c4": 0.72}, {"time": 2.8333, "y": -6.23, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 3.1667, "y": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "y": -2.4, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 5, "y": -4.6}]}, "qunz2": {"rotate": [{"angle": -0.27, "curve": 0.348, "c2": 0.39, "c3": 0.683, "c4": 0.73}, {"time": 0.1333, "angle": 0.09, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.3667, "angle": 0.39, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": -2.66, "curve": 0.243, "c3": 0.659, "c4": 0.64}, {"time": 2.5, "angle": -0.27, "curve": 0.348, "c2": 0.39, "c3": 0.683, "c4": 0.73}, {"time": 2.6333, "angle": 0.09, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.8667, "angle": 0.39, "curve": 0.25, "c3": 0.75}, {"time": 4.1, "angle": -2.66, "curve": 0.243, "c3": 0.659, "c4": 0.64}, {"time": 5, "angle": -0.27}]}, "qunz3": {"rotate": [{"angle": -0.79, "curve": 0.34, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 0.1333, "angle": -0.34, "curve": 0.349, "c2": 0.39, "c3": 0.686, "c4": 0.74}, {"time": 0.3333, "angle": 0.09, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.5333, "angle": 0.39, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": -2.66, "curve": 0.246, "c3": 0.635, "c4": 0.55}, {"time": 2.5, "angle": -0.79, "curve": 0.34, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 2.6333, "angle": -0.34, "curve": 0.349, "c2": 0.39, "c3": 0.686, "c4": 0.74}, {"time": 2.8333, "angle": 0.09, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 3.0333, "angle": 0.39, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "angle": -2.66, "curve": 0.246, "c3": 0.635, "c4": 0.55}, {"time": 5, "angle": -0.79}]}, "qunz4": {"rotate": [{"angle": -1.31, "curve": 0.333, "c2": 0.33, "c3": 0.668, "c4": 0.67}, {"time": 0.1333, "angle": -0.85, "curve": 0.353, "c2": 0.4, "c3": 0.698, "c4": 0.77}, {"time": 0.4667, "angle": 0.09, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.6667, "angle": 0.39, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": -2.66, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 2.5, "angle": -1.31, "curve": 0.333, "c2": 0.33, "c3": 0.668, "c4": 0.67}, {"time": 2.6333, "angle": -0.85, "curve": 0.353, "c2": 0.4, "c3": 0.698, "c4": 0.77}, {"time": 2.9667, "angle": 0.09, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 3.1667, "angle": 0.39, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "angle": -2.66, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 5, "angle": -1.31}]}, "qunz5": {"rotate": [{"angle": -1.85, "curve": 0.326, "c2": 0.31, "c3": 0.661, "c4": 0.65}, {"time": 0.1333, "angle": -1.39, "curve": 0.346, "c2": 0.37, "c3": 0.704, "c4": 0.79}, {"time": 0.6333, "angle": 0.09, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.8667, "angle": 0.39, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "angle": -2.66, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 2.5, "angle": -1.85, "curve": 0.326, "c2": 0.31, "c3": 0.661, "c4": 0.65}, {"time": 2.6333, "angle": -1.39, "curve": 0.346, "c2": 0.37, "c3": 0.704, "c4": 0.79}, {"time": 3.1333, "angle": 0.09, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 3.3667, "angle": 0.39, "curve": 0.25, "c3": 0.75}, {"time": 4.6, "angle": -2.66, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 5, "angle": -1.85}]}, "qunz6": {"rotate": [{"angle": -2.29, "curve": 0.319, "c2": 0.28, "c3": 0.654, "c4": 0.62}, {"time": 0.1333, "angle": -1.91, "curve": 0.328, "c2": 0.32, "c3": 0.705, "c4": 0.79}, {"time": 0.8333, "angle": 0.09, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 1.0333, "angle": 0.39, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": -2.66, "curve": 0.286, "c3": 0.626, "c4": 0.38}, {"time": 2.5, "angle": -2.29, "curve": 0.319, "c2": 0.28, "c3": 0.654, "c4": 0.62}, {"time": 2.6333, "angle": -1.91, "curve": 0.328, "c2": 0.32, "c3": 0.705, "c4": 0.79}, {"time": 3.3333, "angle": 0.09, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 3.5333, "angle": 0.39, "curve": 0.25, "c3": 0.75}, {"time": 4.7333, "angle": -2.66, "curve": 0.286, "c3": 0.626, "c4": 0.38}, {"time": 5, "angle": -2.29}]}, "st6": {"rotate": [{"angle": 4.35}, {"time": 0.1667, "angle": 5.02, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1.2333, "angle": 0.35, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 1.4333}, {"time": 2.5, "angle": 4.35}, {"time": 2.6667, "angle": 5.02, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 3.7333, "angle": 0.35, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 3.9333}, {"time": 5, "angle": 4.35}]}, "st7": {"rotate": [{"angle": 3.52}, {"time": 0.4, "angle": 5.02, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.2333, "angle": 1.2, "curve": 0.355, "c2": 0.42, "c3": 0.693, "c4": 0.77}, {"time": 1.4667, "angle": 0.35, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 1.6333}, {"time": 2.5, "angle": 3.52}, {"time": 2.9, "angle": 5.02, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.7333, "angle": 1.2, "curve": 0.355, "c2": 0.42, "c3": 0.693, "c4": 0.77}, {"time": 3.9667, "angle": 0.35, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 4.1333}, {"time": 5, "angle": 3.52}]}, "st8": {"rotate": [{"angle": 2.73, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.5667, "angle": 5.02, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1.2333, "angle": 2.26, "curve": 0.356, "c2": 0.41, "c3": 0.707, "c4": 0.81}, {"time": 1.6667, "angle": 0.35, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 1.8, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2.5, "angle": 2.73, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 3.0667, "angle": 5.02, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 3.7333, "angle": 2.26, "curve": 0.356, "c2": 0.41, "c3": 0.707, "c4": 0.81}, {"time": 4.1667, "angle": 0.35, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 4.3, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 5, "angle": 2.73}]}, "yt3": {"rotate": [{"angle": -13.76, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.1333, "angle": -11.69, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6333, "angle": -6.62, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.4, "angle": -15.33, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.9, "angle": -20.4, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 2.5, "angle": -13.76, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 2.6333, "angle": -11.69, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.1333, "angle": -6.62, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.9, "angle": -15.33, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.4, "angle": -20.4, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 5, "angle": -13.76}]}, "zt3": {"rotate": [{"angle": -5, "curve": 0.306, "c2": 0.12, "c3": 0.642, "c4": 0.47}, {"time": 0.1333, "angle": -6.41, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 1.2, "angle": -26.4, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": -4.92, "curve": 0.325, "c3": 0.658, "c4": 0.34}, {"time": 2.5, "angle": -5, "curve": 0.306, "c2": 0.12, "c3": 0.642, "c4": 0.47}, {"time": 2.6333, "angle": -6.41, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 3.7, "angle": -26.4, "curve": 0.25, "c3": 0.75}, {"time": 4.9667, "angle": -4.92, "curve": 0.325, "c3": 0.658, "c4": 0.34}, {"time": 5, "angle": -5}]}, "ys5": {"rotate": [{"angle": -2.65, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": -7.2, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.5, "angle": -2.65, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.2333, "angle": -7.2, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5, "angle": -2.65}]}, "ys7": {"rotate": [{"angle": -18.02, "curve": 0.376, "c2": 0.61, "c3": 0.718}, {"time": 0.2667, "angle": -21.6, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 3.7, "curve": 0.243, "c3": 0.677, "c4": 0.7}, {"time": 2.5, "angle": -18.02, "curve": 0.376, "c2": 0.61, "c3": 0.718}, {"time": 2.7667, "angle": -21.6, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 3.7, "curve": 0.243, "c3": 0.677, "c4": 0.7}, {"time": 5, "angle": -18.02}]}, "ys6": {"rotate": [{"angle": 13.08, "curve": 0.374, "c2": 0.49, "c3": 0.751}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": 25.2, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 2.5, "angle": 13.08, "curve": 0.374, "c2": 0.49, "c3": 0.751}, {"time": 3.1333, "curve": 0.25, "c3": 0.75}, {"time": 4.4, "angle": 25.2, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 5, "angle": 13.08}]}, "ys8": {"rotate": [{"angle": -1.71, "curve": 0.344, "c2": 0.37, "c3": 0.68, "c4": 0.71}, {"time": 0.1333, "angle": -2.57, "curve": 0.376, "c2": 0.61, "c3": 0.718}, {"time": 0.4, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": 3.7, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 2.5, "angle": -1.71, "curve": 0.344, "c2": 0.37, "c3": 0.68, "c4": 0.71}, {"time": 2.6333, "angle": -2.57, "curve": 0.376, "c2": 0.61, "c3": 0.718}, {"time": 2.9, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "angle": 3.7, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 5, "angle": -1.71}]}}, "deform": {"default": {"t5": {"t5": [{"offset": 68, "vertices": [-0.19183, -0.27384, -0.16756, -0.28062, -0.19183, -0.27384, -0.16756, -0.28062, 0, 0, 0, 0, -0.5679, 0.04068, -0.5701, 0.02134, -0.57069, 0.01318, -0.5679, 0.04068, -0.5701, 0.02134, -0.57069, 0.01318, -0.5679, 0.04068, -0.5701, 0.02134, -0.57069, 0.01318, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.4105, -0.07517, -0.40299, -0.08924, -0.3987, -0.09504, -0.4105, -0.07517, -0.40299, -0.08924, -0.4105, -0.07517, -0.40299, -0.08924, -0.4105, -0.07517, -0.40299, -0.08924, -0.4105, -0.07517, -0.40299, -0.08924], "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "offset": 68, "vertices": [-1.10507, -1.57751, -0.96527, -1.61653, -1.10507, -1.57751, -0.96527, -1.61653, 0, 0, 0, 0, -3.27148, 0.23433, -3.28415, 0.12291, -3.28751, 0.07592, -3.27148, 0.23433, -3.28415, 0.12291, -3.28751, 0.07592, -3.27148, 0.23433, -3.28415, 0.12291, -3.28751, 0.07592, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.36475, -0.43303, -2.3215, -0.51407, -2.29675, -0.5475, -2.36475, -0.43303, -2.3215, -0.51407, -2.36475, -0.43303, -2.3215, -0.51407, -2.36475, -0.43303, -2.3215, -0.51407, -2.36475, -0.43303, -2.3215, -0.51407], "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 2.5, "offset": 68, "vertices": [-0.19183, -0.27384, -0.16756, -0.28062, -0.19183, -0.27384, -0.16756, -0.28062, 0, 0, 0, 0, -0.5679, 0.04068, -0.5701, 0.02134, -0.57069, 0.01318, -0.5679, 0.04068, -0.5701, 0.02134, -0.57069, 0.01318, -0.5679, 0.04068, -0.5701, 0.02134, -0.57069, 0.01318, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.4105, -0.07517, -0.40299, -0.08924, -0.3987, -0.09504, -0.4105, -0.07517, -0.40299, -0.08924, -0.4105, -0.07517, -0.40299, -0.08924, -0.4105, -0.07517, -0.40299, -0.08924, -0.4105, -0.07517, -0.40299, -0.08924], "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 2.8, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "offset": 68, "vertices": [-1.10507, -1.57751, -0.96527, -1.61653, -1.10507, -1.57751, -0.96527, -1.61653, 0, 0, 0, 0, -3.27148, 0.23433, -3.28415, 0.12291, -3.28751, 0.07592, -3.27148, 0.23433, -3.28415, 0.12291, -3.28751, 0.07592, -3.27148, 0.23433, -3.28415, 0.12291, -3.28751, 0.07592, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.36475, -0.43303, -2.3215, -0.51407, -2.29675, -0.5475, -2.36475, -0.43303, -2.3215, -0.51407, -2.36475, -0.43303, -2.3215, -0.51407, -2.36475, -0.43303, -2.3215, -0.51407, -2.36475, -0.43303, -2.3215, -0.51407], "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 5, "offset": 68, "vertices": [-0.19183, -0.27384, -0.16756, -0.28062, -0.19183, -0.27384, -0.16756, -0.28062, 0, 0, 0, 0, -0.5679, 0.04068, -0.5701, 0.02134, -0.57069, 0.01318, -0.5679, 0.04068, -0.5701, 0.02134, -0.57069, 0.01318, -0.5679, 0.04068, -0.5701, 0.02134, -0.57069, 0.01318, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.4105, -0.07517, -0.40299, -0.08924, -0.3987, -0.09504, -0.4105, -0.07517, -0.40299, -0.08924, -0.4105, -0.07517, -0.40299, -0.08924, -0.4105, -0.07517, -0.40299, -0.08924, -0.4105, -0.07517, -0.40299, -0.08924]}]}, "ys2": {"ys2": [{"curve": 0.25, "c3": 0.75}, {"time": 1.2333, "offset": 114, "vertices": [-0.29518, 2.57922, -1.18005, 2.31308, -0.29518, 2.57922, -1.18005, 2.31308, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.0882, 0.77316, 0.17263, 0.75958, -0.26624, 2.32217], "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "offset": 114, "vertices": [-0.29518, 2.57922, -1.18005, 2.31308, -0.29518, 2.57922, -1.18005, 2.31308, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.0882, 0.77316, 0.17263, 0.75958, -0.26624, 2.32217], "curve": 0.25, "c3": 0.75}, {"time": 5}]}, "t1": {"t1": [{"offset": 22, "vertices": [0.0107, -0.09726, 0.07999, -0.04725, -0.03774, -0.06386, -0.03774, -0.06386, -0.04313, -0.07298, 0, 0, -0.01495, -0.02528, -0.01495, -0.02528, 0, 0, -0.01498, -0.02527, -0.02988, -0.05056, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.03984, -0.06741, -0.03984, -0.06741, -0.03984, -0.06741, -0.03984, -0.06741, 0, 0, 0, 0, -0.0249, -0.04213, -0.0249, -0.04213, -0.02988, -0.05056, -0.02988, -0.05056, -0.03984, -0.06741, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.02659, -0.10737, -0.02659, -0.10737, -0.02159, -0.03648, 0.27862, 1.99621, 0.27862, 1.99621, 0, 0, 0.29478, 2.02359, -0.41019, 1.03176, 0, 0, 0.32176, 2.0692, 0.27862, 1.99621, 0.3056, 2.04183, -0.02659, -0.10737], "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1333, "offset": 114, "vertices": [0.34695, 2.24472, 0.34695, 2.24472, 0, 0, 0.34695, 2.24472, -0.38934, 1.20882, 0, 0, 0.34695, 2.24472, 0.34695, 2.24472, 0.34695, 2.24472], "curve": 0.25, "c3": 0.75}, {"time": 1.4, "offset": 22, "vertices": [0.09689, 1.32834, 2.34161, -0.28244, -0.88684, -1.50067, -0.88684, -1.50067, -1.01364, -1.71501, 0, 0, -0.35126, -0.59404, -0.35126, -0.59404, 0, 0, -0.35199, -0.59375, -0.70215, -1.18808, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.57861, -1.03737, -1.51486, -2.62155, -0.93625, -1.58418, -1.51358, -2.619, -2.09103, -3.65382, -1.30206, -2.3343, -1.30206, -2.3343, -3.04453, -5.39923, -1.88718, -3.32434, -2.1489, -3.78177, -1.28079, -2.22559, -1.80438, -3.1404, -0.57867, -1.03745, 0, 0, 0, 0, -1.15735, -2.07489, -1.30194, -2.3343, -1.0127, -1.81554, -1.0127, -1.81554, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.29401, -5.51611, -0.90491, -5.77899, -2.29437, -4.9789, -1.25882, -3.59517, -1.25882, -3.59517, 0, 0, -0.87921, -2.95193, -0.87921, -2.95193, 0, 0, -0.24518, -1.88004, -1.25882, -3.59517, -0.15619, 0.45737, -0.62482, -2.52328], "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2.5, "offset": 22, "vertices": [0.0107, -0.09726, 0.07999, -0.04725, -0.03774, -0.06386, -0.03774, -0.06386, -0.04313, -0.07298, 0, 0, -0.01495, -0.02528, -0.01495, -0.02528, 0, 0, -0.01498, -0.02527, -0.02988, -0.05056, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.03984, -0.06741, -0.03984, -0.06741, -0.03984, -0.06741, -0.03984, -0.06741, 0, 0, 0, 0, -0.0249, -0.04213, -0.0249, -0.04213, -0.02988, -0.05056, -0.02988, -0.05056, -0.03984, -0.06741, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.02659, -0.10737, -0.02659, -0.10737, -0.02159, -0.03648, 0.27862, 1.99621, 0.27862, 1.99621, 0, 0, 0.29478, 2.02359, -0.41019, 1.03176, 0, 0, 0.32176, 2.0692, 0.27862, 1.99621, 0.3056, 2.04183, -0.02659, -0.10737], "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 2.6333, "offset": 114, "vertices": [0.34695, 2.24472, 0.34695, 2.24472, 0, 0, 0.34695, 2.24472, -0.38934, 1.20882, 0, 0, 0.34695, 2.24472, 0.34695, 2.24472, 0.34695, 2.24472], "curve": 0.25, "c3": 0.75}, {"time": 3.9, "offset": 22, "vertices": [0.09689, 1.32834, 2.34161, -0.28244, -0.88684, -1.50067, -0.88684, -1.50067, -1.01364, -1.71501, 0, 0, -0.35126, -0.59404, -0.35126, -0.59404, 0, 0, -0.35199, -0.59375, -0.70215, -1.18808, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.57861, -1.03737, -1.51486, -2.62155, -0.93625, -1.58418, -1.51358, -2.619, -2.09103, -3.65382, -1.30206, -2.3343, -1.30206, -2.3343, -3.04453, -5.39923, -1.88718, -3.32434, -2.1489, -3.78177, -1.28079, -2.22559, -1.80438, -3.1404, -0.57867, -1.03745, 0, 0, 0, 0, -1.15735, -2.07489, -1.30194, -2.3343, -1.0127, -1.81554, -1.0127, -1.81554, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.29401, -5.51611, -0.90491, -5.77899, -2.29437, -4.9789, -1.25882, -3.59517, -1.25882, -3.59517, 0, 0, -0.87921, -2.95193, -0.87921, -2.95193, 0, 0, -0.24518, -1.88004, -1.25882, -3.59517, -0.15619, 0.45737, -0.62482, -2.52328], "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 5, "offset": 22, "vertices": [0.0107, -0.09726, 0.07999, -0.04725, -0.03774, -0.06386, -0.03774, -0.06386, -0.04313, -0.07298, 0, 0, -0.01495, -0.02528, -0.01495, -0.02528, 0, 0, -0.01498, -0.02527, -0.02988, -0.05056, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.03984, -0.06741, -0.03984, -0.06741, -0.03984, -0.06741, -0.03984, -0.06741, 0, 0, 0, 0, -0.0249, -0.04213, -0.0249, -0.04213, -0.02988, -0.05056, -0.02988, -0.05056, -0.03984, -0.06741, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.02659, -0.10737, -0.02659, -0.10737, -0.02159, -0.03648, 0.27862, 1.99621, 0.27862, 1.99621, 0, 0, 0.29478, 2.02359, -0.41019, 1.03176, 0, 0, 0.32176, 2.0692, 0.27862, 1.99621, 0.3056, 2.04183, -0.02659, -0.10737]}]}, "t8": {"t8": [{"time": 4.2667, "curve": 0.25, "c3": 0.75}, {"time": 4.4667, "vertices": [-1.67767, -2.75119, 0, 0, 0, 0, 0, 0, 0.80493, -2.76355, 2.17273, -4.48917, 2.17273, -4.48917, 1.28178, -6.25882, 1.28178, -6.25882, -2.44203, -4.00494, -1.67767, -2.75119, 0.76987, -13.90353, -1.08292, -14.22025, -2.27963, -4.32135, -0.20671, 2.18784, -0.20671, 2.18784, -1.62814, -2.67029], "curve": 0.25, "c3": 0.75}, {"time": 4.6333}]}, "zs1": {"zs1": [{"offset": 8, "vertices": [3.69751, 1.65552, 3.69751, 1.65552, 3.34976, 2.27795, 3.69751, 1.65552, 3.34976, 2.27795, 3.96185, 0.84821, 8.08096, 0.50394, 7.86713, 1.91339, 8.00861, -1.19226, 8.08096, 0.50394, 7.86713, 1.91339, 8.00861, -1.19226, 8.08096, 0.50394, 7.86713, 1.91339, 8.00861, -1.19226, 7.86713, 1.91339, 8.00861, -1.19226, 6.4368, 1.56549, 6.55255, -0.97556, 0, 0, 0, 0, 0, 0, 5.71613, -1.59509, 5.82324, -1.14081, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.17523, -0.71515, -0.10669, -0.72839, -1.73865, 7.15143, 0, 0, 0, 0, 4.78589, -3.73157, 3.07236, -3.61563, 3.64914, -3.04218, 2.53772, -2.71149, 2.96912, -2.2417, 3.54114, -3.5827, 4.10562, -2.93036, 0.23141, -3.28192, 0.78323, -3.19562, 0, 0, 0, 0, -0.17563, 2.8382, 2.71918, 0.83154, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.69751, 1.65552, 2.46936, -3.21057, 3.69751, 1.65552, 3.69751, 1.65552, 3.34976, 2.27795, -4.06235, 2.41354, -4.47046, 1.69562, 2.52744, 6.79456, 1.29626, 7.13208, 3.88947, 6.11816, 2.52744, 6.79456, 1.29626, 7.13208, 2.52744, 6.79456, 1.29626, 7.13208, 3.88947, 6.11816, 2.39038, 8.99826, 0.77478, 9.27747, 2.39038, 8.99826, 0.77478, 9.27747, 4.21515, 8.302, 2.52744, 6.79456, 1.29626, 7.13208, 0.60092, 9.99255, 4.32367, 9.02991, 0.60092, 9.99255, 4.32367, 9.02991, 2.52744, 6.79456, 1.29626, 7.13208, 0.60092, 9.99255, 4.32367, 9.02991, 0.60092, 9.99255, 4.32367, 9.02991, -0.34625, 1.43024, 0.21899, 1.45544, -0.52148, 2.14539, 0.32568, 2.18384, 0.15295, 2.20294, 0.11578, 2.2049, 0, 0, 0, 0, 0, 0, 0, 0, 5.89554, 0.67639, 5.71613, -1.59509, 5.82324, -1.14081, 5.84155, -1.04309, 5.72141, 1.39154, 5.82428, -0.86716, 5.87402, -0.4068, 5.88, -0.30811, 5.72141, 1.39154, 5.82428, -0.86716, 5.87402, -0.4068, -1.39081, 5.72101, 0.86816, 5.82355, 0.40771, 5.87439, 0.30859, 5.8797, 0.86816, 5.82355, 0.40771, 5.87439, -1.39081, 5.72101, 0.86816, 5.82355, 0.40771, 5.87439, 0.30859, 5.8797, -0.25146, 5.88208, 0.40771, 5.87439, 0.30859, 5.8797, 0.26654, 5.8822, 0.86816, 5.82355, 0.40771, 5.87439, 0.30859, 5.8797, 0.26654, 5.8822, -0.25146, 5.88208, 0.40771, 5.87439, 0.30859, 5.8797, 0.26654, 5.8822, 0.86816, 5.82355, 0.30859, 5.8797, 0.26654, 5.8822, -0.25146, 5.88208, 0.30859, 5.8797, 0.26654, 5.8822], "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "offset": 260, "vertices": [-2.513, 8.7005, 0.0896, 8.49213, 0, 0, 0, 0, -1.72751, 5.98145, 0.06042, 5.83838, -2.73492, 5.39502, -1.07452, 5.58331, -1.85474, 5.37634, -2.06403, 5.29907], "curve": 0.25, "c3": 0.75}, {"time": 2.5, "offset": 8, "vertices": [3.69751, 1.65552, 3.69751, 1.65552, 3.34976, 2.27795, 3.69751, 1.65552, 3.34976, 2.27795, 3.96185, 0.84821, 8.08096, 0.50394, 7.86713, 1.91339, 8.00861, -1.19226, 8.08096, 0.50394, 7.86713, 1.91339, 8.00861, -1.19226, 8.08096, 0.50394, 7.86713, 1.91339, 8.00861, -1.19226, 7.86713, 1.91339, 8.00861, -1.19226, 6.4368, 1.56549, 6.55255, -0.97556, 0, 0, 0, 0, 0, 0, 5.71613, -1.59509, 5.82324, -1.14081, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.17523, -0.71515, -0.10669, -0.72839, -1.73865, 7.15143, 0, 0, 0, 0, 4.78589, -3.73157, 3.07236, -3.61563, 3.64914, -3.04218, 2.53772, -2.71149, 2.96912, -2.2417, 3.54114, -3.5827, 4.10562, -2.93036, 0.23141, -3.28192, 0.78323, -3.19562, 0, 0, 0, 0, -0.17563, 2.8382, 2.71918, 0.83154, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.69751, 1.65552, 2.46936, -3.21057, 3.69751, 1.65552, 3.69751, 1.65552, 3.34976, 2.27795, -4.06235, 2.41354, -4.47046, 1.69562, 2.52744, 6.79456, 1.29626, 7.13208, 3.88947, 6.11816, 2.52744, 6.79456, 1.29626, 7.13208, 2.52744, 6.79456, 1.29626, 7.13208, 3.88947, 6.11816, 2.39038, 8.99826, 0.77478, 9.27747, 2.39038, 8.99826, 0.77478, 9.27747, 4.21515, 8.302, 2.52744, 6.79456, 1.29626, 7.13208, 0.60092, 9.99255, 4.32367, 9.02991, 0.60092, 9.99255, 4.32367, 9.02991, 2.52744, 6.79456, 1.29626, 7.13208, 0.60092, 9.99255, 4.32367, 9.02991, 0.60092, 9.99255, 4.32367, 9.02991, -0.34625, 1.43024, 0.21899, 1.45544, -0.52148, 2.14539, 0.32568, 2.18384, 0.15295, 2.20294, 0.11578, 2.2049, 0, 0, 0, 0, 0, 0, 0, 0, 5.89554, 0.67639, 5.71613, -1.59509, 5.82324, -1.14081, 5.84155, -1.04309, 5.72141, 1.39154, 5.82428, -0.86716, 5.87402, -0.4068, 5.88, -0.30811, 5.72141, 1.39154, 5.82428, -0.86716, 5.87402, -0.4068, -1.39081, 5.72101, 0.86816, 5.82355, 0.40771, 5.87439, 0.30859, 5.8797, 0.86816, 5.82355, 0.40771, 5.87439, -1.39081, 5.72101, 0.86816, 5.82355, 0.40771, 5.87439, 0.30859, 5.8797, -0.25146, 5.88208, 0.40771, 5.87439, 0.30859, 5.8797, 0.26654, 5.8822, 0.86816, 5.82355, 0.40771, 5.87439, 0.30859, 5.8797, 0.26654, 5.8822, -0.25146, 5.88208, 0.40771, 5.87439, 0.30859, 5.8797, 0.26654, 5.8822, 0.86816, 5.82355, 0.30859, 5.8797, 0.26654, 5.8822, -0.25146, 5.88208, 0.30859, 5.8797, 0.26654, 5.8822], "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "offset": 260, "vertices": [-2.513, 8.7005, 0.0896, 8.49213, 0, 0, 0, 0, -1.72751, 5.98145, 0.06042, 5.83838, -2.73492, 5.39502, -1.07452, 5.58331, -1.85474, 5.37634, -2.06403, 5.29907], "curve": 0.25, "c3": 0.75}, {"time": 5, "offset": 8, "vertices": [3.69751, 1.65552, 3.69751, 1.65552, 3.34976, 2.27795, 3.69751, 1.65552, 3.34976, 2.27795, 3.96185, 0.84821, 8.08096, 0.50394, 7.86713, 1.91339, 8.00861, -1.19226, 8.08096, 0.50394, 7.86713, 1.91339, 8.00861, -1.19226, 8.08096, 0.50394, 7.86713, 1.91339, 8.00861, -1.19226, 7.86713, 1.91339, 8.00861, -1.19226, 6.4368, 1.56549, 6.55255, -0.97556, 0, 0, 0, 0, 0, 0, 5.71613, -1.59509, 5.82324, -1.14081, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.17523, -0.71515, -0.10669, -0.72839, -1.73865, 7.15143, 0, 0, 0, 0, 4.78589, -3.73157, 3.07236, -3.61563, 3.64914, -3.04218, 2.53772, -2.71149, 2.96912, -2.2417, 3.54114, -3.5827, 4.10562, -2.93036, 0.23141, -3.28192, 0.78323, -3.19562, 0, 0, 0, 0, -0.17563, 2.8382, 2.71918, 0.83154, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.69751, 1.65552, 2.46936, -3.21057, 3.69751, 1.65552, 3.69751, 1.65552, 3.34976, 2.27795, -4.06235, 2.41354, -4.47046, 1.69562, 2.52744, 6.79456, 1.29626, 7.13208, 3.88947, 6.11816, 2.52744, 6.79456, 1.29626, 7.13208, 2.52744, 6.79456, 1.29626, 7.13208, 3.88947, 6.11816, 2.39038, 8.99826, 0.77478, 9.27747, 2.39038, 8.99826, 0.77478, 9.27747, 4.21515, 8.302, 2.52744, 6.79456, 1.29626, 7.13208, 0.60092, 9.99255, 4.32367, 9.02991, 0.60092, 9.99255, 4.32367, 9.02991, 2.52744, 6.79456, 1.29626, 7.13208, 0.60092, 9.99255, 4.32367, 9.02991, 0.60092, 9.99255, 4.32367, 9.02991, -0.34625, 1.43024, 0.21899, 1.45544, -0.52148, 2.14539, 0.32568, 2.18384, 0.15295, 2.20294, 0.11578, 2.2049, 0, 0, 0, 0, 0, 0, 0, 0, 5.89554, 0.67639, 5.71613, -1.59509, 5.82324, -1.14081, 5.84155, -1.04309, 5.72141, 1.39154, 5.82428, -0.86716, 5.87402, -0.4068, 5.88, -0.30811, 5.72141, 1.39154, 5.82428, -0.86716, 5.87402, -0.4068, -1.39081, 5.72101, 0.86816, 5.82355, 0.40771, 5.87439, 0.30859, 5.8797, 0.86816, 5.82355, 0.40771, 5.87439, -1.39081, 5.72101, 0.86816, 5.82355, 0.40771, 5.87439, 0.30859, 5.8797, -0.25146, 5.88208, 0.40771, 5.87439, 0.30859, 5.8797, 0.26654, 5.8822, 0.86816, 5.82355, 0.40771, 5.87439, 0.30859, 5.8797, 0.26654, 5.8822, -0.25146, 5.88208, 0.40771, 5.87439, 0.30859, 5.8797, 0.26654, 5.8822, 0.86816, 5.82355, 0.30859, 5.8797, 0.26654, 5.8822, -0.25146, 5.88208, 0.30859, 5.8797, 0.26654, 5.8822]}]}, "qunz1": {"qunz1": [{"offset": 2, "vertices": [-1.27246, 3.02515, -3.63829, 8.66964, -3.00188, 7.15734, -1.56559, 3.74782, -0.29306, 0.72286, 0.7453, 0.58289, 0, 0, 0.60205, 0.24431, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.88428, -0.35582, 0.0619, 0.95118, 0.06115, 0.95124, 0.11067, 0.94674, 0.20425, 0.93105, -0.7369, -0.29652, 0.05156, 0.79263, 0.05093, 0.79268, 0.09221, 0.78893, 0.17023, 0.77586, -0.7369, -0.29652, 0.05156, 0.79263, 0.05093, 0.79268, 0.09221, 0.78893, 0.17023, 0.77586, -1.17902, -0.47447, 0.0825, 1.26823, 0.08149, 1.26831, 0.14755, 1.26231, 0.27227, 1.2414, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.1702, 0.77587, 0.11071, 0.94675, 0.20434, 0.93105, 0.06111, 0.95126, 0.11071, 0.94675, 0.20434, 0.93105, 0.06111, 0.95126, 0.11071, 0.94675, 0.20434, 0.93105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.1575, 0.1842, 0.59196, 1.01157, -1.1575, 0.1842, 0.59196, 1.01157, -1.1575, 0.1842, 0.59196, 1.01157, 0.73091, 0.91621, -1.23254, 0.37071, 0.79313, 1.01374, 0.93016, 0.8895, -1.23254, 0.37071, 0.79313, 1.01374, -1.23254, 0.37071, 0.79313, 1.01374, 0.94958, 0.86831, -1.23254, 0.37071, 0.93016, 0.8895, 0.94958, 0.86831, 1.00758, 0.80124, -1.23254, 0.37071, 0.93016, 0.8895, 0.94958, 0.86831, 1.00758, 0.80124, -1.23254, 0.37071, 0.93016, 0.8895, 0.94958, 0.86831, 1.00758, 0.80124, -1.23254, 0.37071, 0.93016, 0.8895, 0.94958, 0.86831, 1.00758, 0.80124, -1.1575, 0.1842, 0.73091, 0.91621, 0.75132, 0.89949, 0.81128, 0.84598, 0.8958, 0.75581, -1.1575, 0.1842, 0.73091, 0.91621, 0.75132, 0.89949, 0.81128, 0.84598, 0.8958, 0.75581, -1.1575, 0.1842, -1.1575, 0.1842, 0.59196, 1.01157, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.45033, 1.11883, -0.75053, 1.86474, 2.01004, 0.02205, -0.73531, 1.22329, 1.43098, 0.24154, 1.46902, 0.03217, -0.07361, 0.62375, -0.55553, 0.42856, 0.62112, 0.36252, 0.6819, 0.26833, 0.69905, 0.25183, 0.72137, 0.2025, -0.67577, 0.37958, 0.61522, 0.49223, 0.69163, 0.39782, 0.70946, 0.38129, 0.73925, 0.33113, -1.1575, 0.1842, 0.59196, 1.01157, 0.73091, 0.91621, 0.75132, 0.89949, 0.81128, 0.84598, 0, 0, 0, 0, 0, 0, 0.84294, 0.34194, 0.84294, 0.34194, 0.67721, 0.05778, -0.30022, 0.74593, 0, 0, 0, 0, 0, 0, 0, 0, 0.61793, 0.90133, 1.31176, 0.09648, -0.45033, 1.11888, 0, 0, 0, 0, 0.60205, 0.24431, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.7369, -0.29655, -0.03846, 0.7934, 0.05156, 0.79267, 0.05093, 0.79271, -0.03055, 0.63471, 0.04123, 0.63415, 0.04067, 0.63419, 1.1926, 1.64836, 1.37131, 1.50273, 1.37019, 1.50384, 1.1926, 1.64836, 1.37131, 1.50273, 1.37019, 1.50384, 1.44686, 1.43036, -0.83044, 2.06324, 2.21938, -0.14438, 2.21956, -0.1426, 2.20916, -0.25809, -1.86207, 1.64806, 2.29155, 0.96529, 2.29092, 0.96715, 2.33838, 0.84643, 2.411, 0.60993, 1.37019, 1.50384, 1.44686, 1.43036, -1.94828, 0.58592, 1.37131, 1.50273, 1.37019, 1.50384, 1.44686, 1.43036, 1.58191, 1.27954, -1.71469, 1.70739, 2.28124, 0.80674, 2.28074, 0.80859, 2.31994, 0.68862, 2.37696, 0.45474, 1.4313, 2.4551, 1.55757, 2.37711, 1.78625, 2.21059, -0.14737, -0.05929, 0.01024, 0.15849, 0.01004, 0.1585, 0.01842, 0.15773, 0.03425, 0.1551], "curve": 0.382, "c2": 0.57, "c3": 0.736}, {"time": 0.4333, "offset": 2, "vertices": [-1.27246, 3.02515, -3.39349, 8.06686, -2.75708, 6.55457, -1.27252, 3.02496], "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "offset": 2, "vertices": [-1.27246, 3.02515, -4.22406, 10.112, -3.58765, 8.5997, -2.26685, 5.47751, -0.99432, 2.45255, 2.52869, 1.97766, 0, 0, 2.04266, 0.82892, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.00024, -1.20724, 0.21002, 3.22722, 0.20746, 3.22742, 0.37549, 3.21217, 0.69299, 3.15893, -2.50018, -1.00604, 0.17493, 2.68929, 0.17279, 2.68946, 0.31287, 2.67674, 0.57758, 2.63237, -2.50018, -1.00604, 0.17493, 2.68929, 0.17279, 2.68946, 0.31287, 2.67674, 0.57758, 2.63237, -4.00024, -1.6098, 0.27991, 4.30293, 0.27649, 4.30318, 0.50061, 4.28285, 0.92377, 4.21189, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.57745, 2.63243, 0.37561, 3.21217, 0.6933, 3.15893, 0.20734, 3.22749, 0.37561, 3.21217, 0.6933, 3.15893, 0.20734, 3.22749, 0.37561, 3.21217, 0.6933, 3.15893, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.92725, 0.62497, 2.00842, 3.43211, -3.92725, 0.62497, 2.00842, 3.43211, -3.92725, 0.62497, 2.00842, 3.43211, 2.47986, 3.10858, -4.18182, 1.25775, 2.69098, 3.43947, 3.15588, 3.01795, -4.18182, 1.25775, 2.69098, 3.43947, -4.18182, 1.25775, 2.69098, 3.43947, 3.2218, 2.94605, -4.18182, 1.25775, 3.15588, 3.01795, 3.2218, 2.94605, 3.41858, 2.7185, -4.18182, 1.25775, 3.15588, 3.01795, 3.2218, 2.94605, 3.41858, 2.7185, -4.18182, 1.25775, 3.15588, 3.01795, 3.2218, 2.94605, 3.41858, 2.7185, -4.18182, 1.25775, 3.15588, 3.01795, 3.2218, 2.94605, 3.41858, 2.7185, -3.92725, 0.62497, 2.47986, 3.10858, 2.54913, 3.05183, 2.75256, 2.8703, 3.03931, 2.56436, -3.92725, 0.62497, 2.47986, 3.10858, 2.54913, 3.05183, 2.75256, 2.8703, 3.03931, 2.56436, -3.92725, 0.62497, -3.92725, 0.62497, 2.00842, 3.43211, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.52789, 3.79602, -2.54645, 6.32678, 6.81976, 0.0748, -2.49481, 4.15045, 4.8551, 0.81952, 4.98419, 0.10915, -0.24976, 2.1163, -1.88483, 1.45404, 2.10736, 1.22998, 2.3136, 0.91042, 2.37177, 0.85441, 2.44751, 0.68705, -2.29279, 1.28787, 2.08734, 1.67007, 2.34662, 1.34975, 2.4071, 1.29367, 2.50818, 1.12346, -3.92725, 0.62497, 2.00842, 3.43211, 2.47986, 3.10858, 2.54913, 3.05183, 2.75256, 2.8703, 0, 0, 0, 0, 0, 0, 2.85999, 1.16016, 2.85999, 1.16016, 2.29767, 0.19604, -1.01862, 2.53082, 0, 0, 0, 0, 0, 0, 0, 0, 2.09656, 3.05807, 4.45062, 0.32733, -1.52789, 3.7962, 0, 0, 0, 0, 2.04266, 0.82892, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.50018, -1.00616, -0.13049, 2.69189, 0.17493, 2.6894, 0.17279, 2.68956, -0.10364, 2.15347, 0.13989, 2.15157, 0.138, 2.15173, 4.04633, 5.59264, 4.65265, 5.09853, 4.64886, 5.10233, 4.04633, 5.59264, 4.65265, 5.09853, 4.64886, 5.10233, 4.909, 4.85301, -2.81757, 7.00027, 7.53003, -0.48987, 7.53064, -0.48382, 7.49536, -0.87567, -6.31775, 5.59164, 7.7749, 3.27509, 7.77277, 3.28139, 7.93378, 2.87182, 8.18018, 2.0694, 4.64886, 5.10233, 4.909, 4.85301, -6.61023, 1.98795, 4.65265, 5.09853, 4.64886, 5.10233, 4.909, 4.85301, 5.36719, 4.3413, -5.81769, 5.79294, 7.73993, 2.73717, 7.73822, 2.74344, 7.87122, 2.3364, 8.0647, 1.54287, 4.8562, 8.32981, 5.28461, 8.06519, 6.06049, 7.50023, -0.5, -0.20117, 0.03473, 0.53773, 0.03406, 0.53777, 0.0625, 0.53516, 0.11621, 0.52624], "curve": 0.243, "c3": 0.647, "c4": 0.59}, {"time": 2.5, "offset": 2, "vertices": [-1.27246, 3.02515, -3.63829, 8.66964, -3.00188, 7.15734, -1.56559, 3.74782, -0.29306, 0.72286, 0.7453, 0.58289, 0, 0, 0.60205, 0.24431, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.88428, -0.35582, 0.0619, 0.95118, 0.06115, 0.95124, 0.11067, 0.94674, 0.20425, 0.93105, -0.7369, -0.29652, 0.05156, 0.79263, 0.05093, 0.79268, 0.09221, 0.78893, 0.17023, 0.77586, -0.7369, -0.29652, 0.05156, 0.79263, 0.05093, 0.79268, 0.09221, 0.78893, 0.17023, 0.77586, -1.17902, -0.47447, 0.0825, 1.26823, 0.08149, 1.26831, 0.14755, 1.26231, 0.27227, 1.2414, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.1702, 0.77587, 0.11071, 0.94675, 0.20434, 0.93105, 0.06111, 0.95126, 0.11071, 0.94675, 0.20434, 0.93105, 0.06111, 0.95126, 0.11071, 0.94675, 0.20434, 0.93105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.1575, 0.1842, 0.59196, 1.01157, -1.1575, 0.1842, 0.59196, 1.01157, -1.1575, 0.1842, 0.59196, 1.01157, 0.73091, 0.91621, -1.23254, 0.37071, 0.79313, 1.01374, 0.93016, 0.8895, -1.23254, 0.37071, 0.79313, 1.01374, -1.23254, 0.37071, 0.79313, 1.01374, 0.94958, 0.86831, -1.23254, 0.37071, 0.93016, 0.8895, 0.94958, 0.86831, 1.00758, 0.80124, -1.23254, 0.37071, 0.93016, 0.8895, 0.94958, 0.86831, 1.00758, 0.80124, -1.23254, 0.37071, 0.93016, 0.8895, 0.94958, 0.86831, 1.00758, 0.80124, -1.23254, 0.37071, 0.93016, 0.8895, 0.94958, 0.86831, 1.00758, 0.80124, -1.1575, 0.1842, 0.73091, 0.91621, 0.75132, 0.89949, 0.81128, 0.84598, 0.8958, 0.75581, -1.1575, 0.1842, 0.73091, 0.91621, 0.75132, 0.89949, 0.81128, 0.84598, 0.8958, 0.75581, -1.1575, 0.1842, -1.1575, 0.1842, 0.59196, 1.01157, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.45033, 1.11883, -0.75053, 1.86474, 2.01004, 0.02205, -0.73531, 1.22329, 1.43098, 0.24154, 1.46902, 0.03217, -0.07361, 0.62375, -0.55553, 0.42856, 0.62112, 0.36252, 0.6819, 0.26833, 0.69905, 0.25183, 0.72137, 0.2025, -0.67577, 0.37958, 0.61522, 0.49223, 0.69163, 0.39782, 0.70946, 0.38129, 0.73925, 0.33113, -1.1575, 0.1842, 0.59196, 1.01157, 0.73091, 0.91621, 0.75132, 0.89949, 0.81128, 0.84598, 0, 0, 0, 0, 0, 0, 0.84294, 0.34194, 0.84294, 0.34194, 0.67721, 0.05778, -0.30022, 0.74593, 0, 0, 0, 0, 0, 0, 0, 0, 0.61793, 0.90133, 1.31176, 0.09648, -0.45033, 1.11888, 0, 0, 0, 0, 0.60205, 0.24431, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.7369, -0.29655, -0.03846, 0.7934, 0.05156, 0.79267, 0.05093, 0.79271, -0.03055, 0.63471, 0.04123, 0.63415, 0.04067, 0.63419, 1.1926, 1.64836, 1.37131, 1.50273, 1.37019, 1.50384, 1.1926, 1.64836, 1.37131, 1.50273, 1.37019, 1.50384, 1.44686, 1.43036, -0.83044, 2.06324, 2.21938, -0.14438, 2.21956, -0.1426, 2.20916, -0.25809, -1.86207, 1.64806, 2.29155, 0.96529, 2.29092, 0.96715, 2.33838, 0.84643, 2.411, 0.60993, 1.37019, 1.50384, 1.44686, 1.43036, -1.94828, 0.58592, 1.37131, 1.50273, 1.37019, 1.50384, 1.44686, 1.43036, 1.58191, 1.27954, -1.71469, 1.70739, 2.28124, 0.80674, 2.28074, 0.80859, 2.31994, 0.68862, 2.37696, 0.45474, 1.4313, 2.4551, 1.55757, 2.37711, 1.78625, 2.21059, -0.14737, -0.05929, 0.01024, 0.15849, 0.01004, 0.1585, 0.01842, 0.15773, 0.03425, 0.1551], "curve": 0.382, "c2": 0.57, "c3": 0.736}, {"time": 2.9333, "offset": 2, "vertices": [-1.27246, 3.02515, -3.39349, 8.06686, -2.75708, 6.55457, -1.27252, 3.02496], "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "offset": 2, "vertices": [-1.27246, 3.02515, -4.22406, 10.112, -3.58765, 8.5997, -2.26685, 5.47751, -0.99432, 2.45255, 2.52869, 1.97766, 0, 0, 2.04266, 0.82892, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.00024, -1.20724, 0.21002, 3.22722, 0.20746, 3.22742, 0.37549, 3.21217, 0.69299, 3.15893, -2.50018, -1.00604, 0.17493, 2.68929, 0.17279, 2.68946, 0.31287, 2.67674, 0.57758, 2.63237, -2.50018, -1.00604, 0.17493, 2.68929, 0.17279, 2.68946, 0.31287, 2.67674, 0.57758, 2.63237, -4.00024, -1.6098, 0.27991, 4.30293, 0.27649, 4.30318, 0.50061, 4.28285, 0.92377, 4.21189, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.57745, 2.63243, 0.37561, 3.21217, 0.6933, 3.15893, 0.20734, 3.22749, 0.37561, 3.21217, 0.6933, 3.15893, 0.20734, 3.22749, 0.37561, 3.21217, 0.6933, 3.15893, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.92725, 0.62497, 2.00842, 3.43211, -3.92725, 0.62497, 2.00842, 3.43211, -3.92725, 0.62497, 2.00842, 3.43211, 2.47986, 3.10858, -4.18182, 1.25775, 2.69098, 3.43947, 3.15588, 3.01795, -4.18182, 1.25775, 2.69098, 3.43947, -4.18182, 1.25775, 2.69098, 3.43947, 3.2218, 2.94605, -4.18182, 1.25775, 3.15588, 3.01795, 3.2218, 2.94605, 3.41858, 2.7185, -4.18182, 1.25775, 3.15588, 3.01795, 3.2218, 2.94605, 3.41858, 2.7185, -4.18182, 1.25775, 3.15588, 3.01795, 3.2218, 2.94605, 3.41858, 2.7185, -4.18182, 1.25775, 3.15588, 3.01795, 3.2218, 2.94605, 3.41858, 2.7185, -3.92725, 0.62497, 2.47986, 3.10858, 2.54913, 3.05183, 2.75256, 2.8703, 3.03931, 2.56436, -3.92725, 0.62497, 2.47986, 3.10858, 2.54913, 3.05183, 2.75256, 2.8703, 3.03931, 2.56436, -3.92725, 0.62497, -3.92725, 0.62497, 2.00842, 3.43211, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.52789, 3.79602, -2.54645, 6.32678, 6.81976, 0.0748, -2.49481, 4.15045, 4.8551, 0.81952, 4.98419, 0.10915, -0.24976, 2.1163, -1.88483, 1.45404, 2.10736, 1.22998, 2.3136, 0.91042, 2.37177, 0.85441, 2.44751, 0.68705, -2.29279, 1.28787, 2.08734, 1.67007, 2.34662, 1.34975, 2.4071, 1.29367, 2.50818, 1.12346, -3.92725, 0.62497, 2.00842, 3.43211, 2.47986, 3.10858, 2.54913, 3.05183, 2.75256, 2.8703, 0, 0, 0, 0, 0, 0, 2.85999, 1.16016, 2.85999, 1.16016, 2.29767, 0.19604, -1.01862, 2.53082, 0, 0, 0, 0, 0, 0, 0, 0, 2.09656, 3.05807, 4.45062, 0.32733, -1.52789, 3.7962, 0, 0, 0, 0, 2.04266, 0.82892, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.50018, -1.00616, -0.13049, 2.69189, 0.17493, 2.6894, 0.17279, 2.68956, -0.10364, 2.15347, 0.13989, 2.15157, 0.138, 2.15173, 4.04633, 5.59264, 4.65265, 5.09853, 4.64886, 5.10233, 4.04633, 5.59264, 4.65265, 5.09853, 4.64886, 5.10233, 4.909, 4.85301, -2.81757, 7.00027, 7.53003, -0.48987, 7.53064, -0.48382, 7.49536, -0.87567, -6.31775, 5.59164, 7.7749, 3.27509, 7.77277, 3.28139, 7.93378, 2.87182, 8.18018, 2.0694, 4.64886, 5.10233, 4.909, 4.85301, -6.61023, 1.98795, 4.65265, 5.09853, 4.64886, 5.10233, 4.909, 4.85301, 5.36719, 4.3413, -5.81769, 5.79294, 7.73993, 2.73717, 7.73822, 2.74344, 7.87122, 2.3364, 8.0647, 1.54287, 4.8562, 8.32981, 5.28461, 8.06519, 6.06049, 7.50023, -0.5, -0.20117, 0.03473, 0.53773, 0.03406, 0.53777, 0.0625, 0.53516, 0.11621, 0.52624], "curve": 0.243, "c3": 0.647, "c4": 0.59}, {"time": 5, "offset": 2, "vertices": [-1.27246, 3.02515, -3.63829, 8.66964, -3.00188, 7.15734, -1.56559, 3.74782, -0.29306, 0.72286, 0.7453, 0.58289, 0, 0, 0.60205, 0.24431, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.88428, -0.35582, 0.0619, 0.95118, 0.06115, 0.95124, 0.11067, 0.94674, 0.20425, 0.93105, -0.7369, -0.29652, 0.05156, 0.79263, 0.05093, 0.79268, 0.09221, 0.78893, 0.17023, 0.77586, -0.7369, -0.29652, 0.05156, 0.79263, 0.05093, 0.79268, 0.09221, 0.78893, 0.17023, 0.77586, -1.17902, -0.47447, 0.0825, 1.26823, 0.08149, 1.26831, 0.14755, 1.26231, 0.27227, 1.2414, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.1702, 0.77587, 0.11071, 0.94675, 0.20434, 0.93105, 0.06111, 0.95126, 0.11071, 0.94675, 0.20434, 0.93105, 0.06111, 0.95126, 0.11071, 0.94675, 0.20434, 0.93105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.1575, 0.1842, 0.59196, 1.01157, -1.1575, 0.1842, 0.59196, 1.01157, -1.1575, 0.1842, 0.59196, 1.01157, 0.73091, 0.91621, -1.23254, 0.37071, 0.79313, 1.01374, 0.93016, 0.8895, -1.23254, 0.37071, 0.79313, 1.01374, -1.23254, 0.37071, 0.79313, 1.01374, 0.94958, 0.86831, -1.23254, 0.37071, 0.93016, 0.8895, 0.94958, 0.86831, 1.00758, 0.80124, -1.23254, 0.37071, 0.93016, 0.8895, 0.94958, 0.86831, 1.00758, 0.80124, -1.23254, 0.37071, 0.93016, 0.8895, 0.94958, 0.86831, 1.00758, 0.80124, -1.23254, 0.37071, 0.93016, 0.8895, 0.94958, 0.86831, 1.00758, 0.80124, -1.1575, 0.1842, 0.73091, 0.91621, 0.75132, 0.89949, 0.81128, 0.84598, 0.8958, 0.75581, -1.1575, 0.1842, 0.73091, 0.91621, 0.75132, 0.89949, 0.81128, 0.84598, 0.8958, 0.75581, -1.1575, 0.1842, -1.1575, 0.1842, 0.59196, 1.01157, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.45033, 1.11883, -0.75053, 1.86474, 2.01004, 0.02205, -0.73531, 1.22329, 1.43098, 0.24154, 1.46902, 0.03217, -0.07361, 0.62375, -0.55553, 0.42856, 0.62112, 0.36252, 0.6819, 0.26833, 0.69905, 0.25183, 0.72137, 0.2025, -0.67577, 0.37958, 0.61522, 0.49223, 0.69163, 0.39782, 0.70946, 0.38129, 0.73925, 0.33113, -1.1575, 0.1842, 0.59196, 1.01157, 0.73091, 0.91621, 0.75132, 0.89949, 0.81128, 0.84598, 0, 0, 0, 0, 0, 0, 0.84294, 0.34194, 0.84294, 0.34194, 0.67721, 0.05778, -0.30022, 0.74593, 0, 0, 0, 0, 0, 0, 0, 0, 0.61793, 0.90133, 1.31176, 0.09648, -0.45033, 1.11888, 0, 0, 0, 0, 0.60205, 0.24431, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.7369, -0.29655, -0.03846, 0.7934, 0.05156, 0.79267, 0.05093, 0.79271, -0.03055, 0.63471, 0.04123, 0.63415, 0.04067, 0.63419, 1.1926, 1.64836, 1.37131, 1.50273, 1.37019, 1.50384, 1.1926, 1.64836, 1.37131, 1.50273, 1.37019, 1.50384, 1.44686, 1.43036, -0.83044, 2.06324, 2.21938, -0.14438, 2.21956, -0.1426, 2.20916, -0.25809, -1.86207, 1.64806, 2.29155, 0.96529, 2.29092, 0.96715, 2.33838, 0.84643, 2.411, 0.60993, 1.37019, 1.50384, 1.44686, 1.43036, -1.94828, 0.58592, 1.37131, 1.50273, 1.37019, 1.50384, 1.44686, 1.43036, 1.58191, 1.27954, -1.71469, 1.70739, 2.28124, 0.80674, 2.28074, 0.80859, 2.31994, 0.68862, 2.37696, 0.45474, 1.4313, 2.4551, 1.55757, 2.37711, 1.78625, 2.21059, -0.14737, -0.05929, 0.01024, 0.15849, 0.01004, 0.1585, 0.01842, 0.15773, 0.03425, 0.1551]}]}, "ys1": {"ys1": [{"curve": 0.25, "c3": 0.75}, {"time": 1.2333, "offset": 64, "vertices": [-7.67194, -1.17795, 3.36017, 6.99843, 1.52756, 7.61179, 0, 0, 0, 0, 0, 0, -0.89825, 6.31931, -1.00388, 6.30359, -2.5296, 5.8605, -0.89825, 6.31931, -1.00388, 6.30359, -2.5296, 5.8605, -0.89825, 6.31931, -1.00388, 6.30359, -2.5296, 5.8605, -5.85956, 0.4782, 3.78, 4.50479, 3.70395, 4.56749, 2.46118, 5.34122, -3.58221, 0.66739, 2.61734, 2.53745, -5.38556, -0.6199, 0, 0, 0, 0, -6.36542, 2.82944, 0.31853, 5.14511, -4.96509, -1.3799, 1.61969, 5.73419, -5.94666, -0.34276, 2.6839, 1.21475, -2.0246, 2.13974, 3.13116, 1.41721, -2.36194, 2.49637, -1.76846, 3.90622, -3.11316, -2.94766, -1.76846, 3.90622, -4.89178, 1.27193, -3.59265, 0.28838, -4.36084, -0.06952, -2.85236, -2.69997, -0.61783, 3.879, -1.55676, 3.60626, -7.13116, -6.74994, -3.89182, 9.01596, -3.74561, 1.61972, 3.48877, 2.11993, 2.65497, 2.27206, 2.61633, 2.31631, 1.96335, 2.89114, 0.55191, 3.83524, 0.48593, 3.84454, -0.47775, 3.84641, -7.30304, -1.5051, -5.55182, 0.15295, -2.69946, 2.85306, 3.60657, 1.55688, -1.87885, 4.15051, -8.47003, -2.61046, 2.771, 8.42146, 2.62903, 8.46698, 0.45685, 8.85492, 1.95883, 5.19827, -5.55182, 0.15295, -1.65785, 3.66217, -2.9187, -2.76337, -1.65785, 3.66217, -9.83038, -3.89703, 2.33469, 10.31557, -0.28516, 10.57303, 1.95883, 5.19827, -5.55182, 0.15295, 3.26138, 4.49696, 2.04996, 5.16351, 2.68323, 1.21521, -2.02283, 2.13899, 2.70581, 1.16943, -1.87885, 4.15051, -3.30798, -3.13168, -1.80573, 4.18317], "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "offset": 64, "vertices": [-7.67194, -1.17795, 3.36017, 6.99843, 1.52756, 7.61179, 0, 0, 0, 0, 0, 0, -0.89825, 6.31931, -1.00388, 6.30359, -2.5296, 5.8605, -0.89825, 6.31931, -1.00388, 6.30359, -2.5296, 5.8605, -0.89825, 6.31931, -1.00388, 6.30359, -2.5296, 5.8605, -5.85956, 0.4782, 3.78, 4.50479, 3.70395, 4.56749, 2.46118, 5.34122, -3.58221, 0.66739, 2.61734, 2.53745, -5.38556, -0.6199, 0, 0, 0, 0, -6.36542, 2.82944, 0.31853, 5.14511, -4.96509, -1.3799, 1.61969, 5.73419, -5.94666, -0.34276, 2.6839, 1.21475, -2.0246, 2.13974, 3.13116, 1.41721, -2.36194, 2.49637, -1.76846, 3.90622, -3.11316, -2.94766, -1.76846, 3.90622, -4.89178, 1.27193, -3.59265, 0.28838, -4.36084, -0.06952, -2.85236, -2.69997, -0.61783, 3.879, -1.55676, 3.60626, -7.13116, -6.74994, -3.89182, 9.01596, -3.74561, 1.61972, 3.48877, 2.11993, 2.65497, 2.27206, 2.61633, 2.31631, 1.96335, 2.89114, 0.55191, 3.83524, 0.48593, 3.84454, -0.47775, 3.84641, -7.30304, -1.5051, -5.55182, 0.15295, -2.69946, 2.85306, 3.60657, 1.55688, -1.87885, 4.15051, -8.47003, -2.61046, 2.771, 8.42146, 2.62903, 8.46698, 0.45685, 8.85492, 1.95883, 5.19827, -5.55182, 0.15295, -1.65785, 3.66217, -2.9187, -2.76337, -1.65785, 3.66217, -9.83038, -3.89703, 2.33469, 10.31557, -0.28516, 10.57303, 1.95883, 5.19827, -5.55182, 0.15295, 3.26138, 4.49696, 2.04996, 5.16351, 2.68323, 1.21521, -2.02283, 2.13899, 2.70581, 1.16943, -1.87885, 4.15051, -3.30798, -3.13168, -1.80573, 4.18317], "curve": 0.25, "c3": 0.75}, {"time": 5}]}, "qinz2": {"qinz2": [{"curve": 0.25, "c3": 0.75}, {"time": 1.2333, "offset": 236, "vertices": [1.13794, -2.01651, 1.12128, -2.02518, 1.11743, -2.02859, 1.12463, -2.02307, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.13794, -2.01651, 1.12128, -2.02518, 1.13794, -2.01651, 1.12128, -2.02518, 1.13794, -2.01651, 1.13794, -2.01651, 1.12128, -2.02518, 1.11743, -2.02859, 1.13794, -2.01651, 1.12128, -2.02518, 1.11743, -2.02859, 1.12463, -2.02307], "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "offset": 236, "vertices": [1.13794, -2.01651, 1.12128, -2.02518, 1.11743, -2.02859, 1.12463, -2.02307, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.13794, -2.01651, 1.12128, -2.02518, 1.13794, -2.01651, 1.12128, -2.02518, 1.13794, -2.01651, 1.13794, -2.01651, 1.12128, -2.02518, 1.11743, -2.02859, 1.13794, -2.01651, 1.12128, -2.02518, 1.11743, -2.02859, 1.12463, -2.02307], "curve": 0.25, "c3": 0.75}, {"time": 5}]}, "t7": {"t7": [{"time": 4.2667, "curve": 0.25, "c3": 0.75}, {"time": 4.4667, "vertices": [0.48193, 5.56453, 0.47052, 3.13812, -0.07095, 1.47635, -1.2388, -0.63583, 0.0892, -7.20482, 0.4339, -8.91943, 0.14893, -8.9234, 0.35468, -9.05009, 0.15088, -5.55176, -0.25354, -1.62202, -0.54221, 0.45064, 0.74747, 1.37123, -1.10941, 6.42686, 0.45355, 5.08586, -1.10052, 2.80098, -3.07034, 7.51564, -2.80078, 5.36053], "curve": 0.25, "c3": 0.75}, {"time": 4.6333}]}, "st1": {"st1": [{"offset": 122, "vertices": [1.63336, 0.78384, 0.11768, -2.42749, 0.03937, 2.42984, 0.20416, -4.20618, -4.06451, 0.27023, 0.20416, -4.20618, -4.06451, 0.27023, 0, 0, 0, 0, 2.40527, 0.11777, -0.15408, -2.32623, 1.72208, 1.68427, -0.15308, -2.40366, 1.09332, 0.0535, -0.07007, -1.0575, 0.78284, 0.76556, -0.06958, -1.09262, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.94394, -1.7019, -0.12106, -1.82327, -3.40207, -2.97845, 0.11493, -2.35721, -2.27853, 0.15173, 1.6496, -1.68744, 2.25769, 0.68713, 0, 0, -0.91144, 0.06067, 0, 0, -3.40207, -2.97845, 0.11493, -2.35721, -2.27853, 0.15173, 1.6496, -1.68744, 2.25769, 0.68713, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.15649, -3.23682, 0, 0, 0, 0, 0, 0, 0, 0, 0.15649, -3.23682, 0.05267, 3.23972, 0, 0, 0, 0, 3.27997, 0.16043, -0.21027, -3.1723, 2.34849, 2.29657, -0.20886, -3.2778, 0, 0, 0, 0, 0, 0, 0, 0, 2.35693, 0.11536, -0.15155, -2.27914, 1.68748, 1.65009, -0.15018, -2.35516, 3.01215, -3.63324, -3.82761, -2.49194, 4.66428, -0.72003, 2.78754, 3.8085, 0, 0, 0, 0, 0, 0, 0, 0, 1.45319, 1.60577, 1.38409, -1.57959, 0.02305, 2.16779, 1.43329, -1.62613, 1.88525, 0.09222, -0.12125, -1.82306, 1.34985, 1.3197, -0.12015, -1.88385], "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "offset": 238, "vertices": [-4.48145, 2.16006, 1.33112, 2.45374, -0.21881, 2.37534, 2.38644, 0.00656, -2.336, -0.81787, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.44714, -0.07257, 0.07132, 1.43784, -1.03465, -1.01447, 0.42346, -1.38538, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.41193, -0.12103, 0.11887, 2.39636, 0.70593, -2.30908, 0.14105, -0.46179, 0.00702, -0.48288, -0.09601, -0.47339, 0, 0, -1.72449, -1.6908, 0.70593, -2.30908, 0.03513, -2.41458, 0.70593, -2.30908, 0.03513, -2.41458, -0.48007, -2.36676, 0.42371, -1.38553, 0.02109, -1.44879, -0.28809, -1.42014, -0.57086, -1.3316, 0, 0, 0, 0, 0, 0, -1.72449, -1.6908, 0.03513, -2.41458, -0.48007, -2.36676], "curve": 0.25, "c3": 0.75}, {"time": 2.5, "offset": 122, "vertices": [1.63336, 0.78384, 0.11768, -2.42749, 0.03937, 2.42984, 0.20416, -4.20618, -4.06451, 0.27023, 0.20416, -4.20618, -4.06451, 0.27023, 0, 0, 0, 0, 2.40527, 0.11777, -0.15408, -2.32623, 1.72208, 1.68427, -0.15308, -2.40366, 1.09332, 0.0535, -0.07007, -1.0575, 0.78284, 0.76556, -0.06958, -1.09262, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.94394, -1.7019, -0.12106, -1.82327, -3.40207, -2.97845, 0.11493, -2.35721, -2.27853, 0.15173, 1.6496, -1.68744, 2.25769, 0.68713, 0, 0, -0.91144, 0.06067, 0, 0, -3.40207, -2.97845, 0.11493, -2.35721, -2.27853, 0.15173, 1.6496, -1.68744, 2.25769, 0.68713, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.15649, -3.23682, 0, 0, 0, 0, 0, 0, 0, 0, 0.15649, -3.23682, 0.05267, 3.23972, 0, 0, 0, 0, 3.27997, 0.16043, -0.21027, -3.1723, 2.34849, 2.29657, -0.20886, -3.2778, 0, 0, 0, 0, 0, 0, 0, 0, 2.35693, 0.11536, -0.15155, -2.27914, 1.68748, 1.65009, -0.15018, -2.35516, 3.01215, -3.63324, -3.82761, -2.49194, 4.66428, -0.72003, 2.78754, 3.8085, 0, 0, 0, 0, 0, 0, 0, 0, 1.45319, 1.60577, 1.38409, -1.57959, 0.02305, 2.16779, 1.43329, -1.62613, 1.88525, 0.09222, -0.12125, -1.82306, 1.34985, 1.3197, -0.12015, -1.88385], "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "offset": 238, "vertices": [-4.48145, 2.16006, 1.33112, 2.45374, -0.21881, 2.37534, 2.38644, 0.00656, -2.336, -0.81787, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.44714, -0.07257, 0.07132, 1.43784, -1.03465, -1.01447, 0.42346, -1.38538, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.41193, -0.12103, 0.11887, 2.39636, 0.70593, -2.30908, 0.14105, -0.46179, 0.00702, -0.48288, -0.09601, -0.47339, 0, 0, -1.72449, -1.6908, 0.70593, -2.30908, 0.03513, -2.41458, 0.70593, -2.30908, 0.03513, -2.41458, -0.48007, -2.36676, 0.42371, -1.38553, 0.02109, -1.44879, -0.28809, -1.42014, -0.57086, -1.3316, 0, 0, 0, 0, 0, 0, -1.72449, -1.6908, 0.03513, -2.41458, -0.48007, -2.36676], "curve": 0.25, "c3": 0.75}, {"time": 5, "offset": 122, "vertices": [1.63336, 0.78384, 0.11768, -2.42749, 0.03937, 2.42984, 0.20416, -4.20618, -4.06451, 0.27023, 0.20416, -4.20618, -4.06451, 0.27023, 0, 0, 0, 0, 2.40527, 0.11777, -0.15408, -2.32623, 1.72208, 1.68427, -0.15308, -2.40366, 1.09332, 0.0535, -0.07007, -1.0575, 0.78284, 0.76556, -0.06958, -1.09262, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.94394, -1.7019, -0.12106, -1.82327, -3.40207, -2.97845, 0.11493, -2.35721, -2.27853, 0.15173, 1.6496, -1.68744, 2.25769, 0.68713, 0, 0, -0.91144, 0.06067, 0, 0, -3.40207, -2.97845, 0.11493, -2.35721, -2.27853, 0.15173, 1.6496, -1.68744, 2.25769, 0.68713, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.15649, -3.23682, 0, 0, 0, 0, 0, 0, 0, 0, 0.15649, -3.23682, 0.05267, 3.23972, 0, 0, 0, 0, 3.27997, 0.16043, -0.21027, -3.1723, 2.34849, 2.29657, -0.20886, -3.2778, 0, 0, 0, 0, 0, 0, 0, 0, 2.35693, 0.11536, -0.15155, -2.27914, 1.68748, 1.65009, -0.15018, -2.35516, 3.01215, -3.63324, -3.82761, -2.49194, 4.66428, -0.72003, 2.78754, 3.8085, 0, 0, 0, 0, 0, 0, 0, 0, 1.45319, 1.60577, 1.38409, -1.57959, 0.02305, 2.16779, 1.43329, -1.62613, 1.88525, 0.09222, -0.12125, -1.82306, 1.34985, 1.3197, -0.12015, -1.88385]}]}, "t4": {"t4": [{"curve": 0.25, "c3": 0.75}, {"time": 1.2333, "offset": 176, "vertices": [-1.53706, -0.39621, 0, 0, 0, 0, 0, 0, -1.56096, -0.33258, -1.53702, -0.3963, -1.56096, -0.33258, -1.53702, -0.3963, -1.52774, -0.42084, 0.91454, -3.60355, 1.14288, -3.56525, -3.12203, -0.66516, -3.0741, -0.79251, -3.05562, -0.84201, 0.91454, -3.60355, 1.14288, -3.56525, -2.23003, -0.47513, -2.19579, -0.5661, 0.91454, -3.60355, -0.45682, 1.80203, -0.57129, 1.78204, 0.91454, -3.60355, -0.79953, 3.15366, -0.99991, 3.11874, 0, 0, 0, 0, -0.57106, 2.25259, 1.16907, 1.98047], "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "offset": 176, "vertices": [-1.53706, -0.39621, 0, 0, 0, 0, 0, 0, -1.56096, -0.33258, -1.53702, -0.3963, -1.56096, -0.33258, -1.53702, -0.3963, -1.52774, -0.42084, 0.91454, -3.60355, 1.14288, -3.56525, -3.12203, -0.66516, -3.0741, -0.79251, -3.05562, -0.84201, 0.91454, -3.60355, 1.14288, -3.56525, -2.23003, -0.47513, -2.19579, -0.5661, 0.91454, -3.60355, -0.45682, 1.80203, -0.57129, 1.78204, 0.91454, -3.60355, -0.79953, 3.15366, -0.99991, 3.11874, 0, 0, 0, 0, -0.57106, 2.25259, 1.16907, 1.98047], "curve": 0.25, "c3": 0.75}, {"time": 5}]}}}}}}