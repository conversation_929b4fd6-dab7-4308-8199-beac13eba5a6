import { _decorator, instantiate, Label } from "cc";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { ItemCtrl } from "db://assets/GameScrpit/game/common/ItemCtrl";
import { JsonMgr } from "db://assets/GameScrpit/game/mgr/JsonMgr";
import ResMgr from "db://assets/GameScrpit/lib/common/ResMgr";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { RouteManager } from "db://assets/platform/src/core/managers/RouteManager";
import { routeConfig, RouteShowArgs } from "db://assets/platform/src/core/managers/RouteTableManager";
import { MessageComponent } from "db://assets/platform/src/core/ui/components/MessageComponent";
import { UIHuntingAtk } from "./UIHuntingAtk";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;
@ccclass("UIHunting")
@routeConfig({
  bundle: BundleEnum.BUNDLE_G_EVENT_ACTION,
  url: "prefab/ui/UIHunting",
  nextHop: [],
  exit: "dialog_close",
})
export class UIHunting extends BaseCtrl {
  public playShowAni: boolean = true;
  private _eventId: number;
  private _eventNode: Node;
  init(args: RouteShowArgs): void {
    this._eventId = args.payload.eventId;
    this._eventNode = args.payload.eventNode;
  }
  protected start(): void {
    super.start();
    let event = JsonMgr.instance.jsonList.c_event2[this._eventId];
    for (let i = 0; i < event.rewardRateList.length; i++) {
      let node = this.getNode("node_award_list").children[i];
      if (!node) {
        node = instantiate(this.getNode("node_award_list").children[0]);
        node.parent = this.getNode("node_award_list");
      }
      node.getComponent(ItemCtrl).setItemId(event.rewardRateList[i][0], event.rewardRateList[i][1]);
      //
    }
    this.getNode("lbl_name").getComponent(Label).string = event.name;
    this.getNode("lbl_desc").getComponent(MessageComponent).messageKey = event.word02[0];
    ResMgr.setNodePrefab(event.prefabPath[0], event.prefabPath[1], this.getNode("node_image"), (node) => {
      node.setPosition(0, 0);
      node.getChildByName("render").setScale(1, 1);
      node.getChildByName("hp").active = false;
      node.getChildByName("node_atk").active = false;
      return false;
    });
  }
  onClickHunting() {
    RouteManager.uiRouteCtrl.showRoute(UIHuntingAtk, {
      payload: {
        eventId: this._eventId,
        eventNode: this._eventNode,
      },
    });
    this.closeBack();
  }
}
