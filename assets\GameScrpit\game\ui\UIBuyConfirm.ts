import { _decorator } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { Label } from "cc";
import { Sprite } from "cc";
import { UIMgr } from "../../lib/ui/UIMgr";
import { PlayerModule } from "../../module/player/PlayerModule";
import { JsonMgr } from "../mgr/JsonMgr";
import FmUtils from "../../lib/utils/FmUtils";
import { Slider } from "cc";
import { FmColor } from "../common/FmConstant";
import Formate from "../../lib/utils/Formate";
import { PlayerRouteName } from "../../module/player/PlayerConstant";
const { ccclass, property } = _decorator;

/**
 *
 * hopewsw
 * Thu Aug 29 2024 20:22:03 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/UIBuyConfirm.ts
 *
 */
export interface BuyConfirm {
  itemInfo: number[];
  moneyInfo: number[];
  maxNum: number; // 商店限制最大数量
  increase?: number; // 涨价
}

@ccclass("UIBuyConfirm")
export class UIBuyConfirm extends UINode {
  private _args: BuyConfirm = null;

  private _max = 1;
  private _num = 1;
  private _increase = 0;

  private cost = 0;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_COMMON_MAIN}?prefab/ui/UIBuyConfirm`;
  }

  //=================================================
  public init(args: any): void {
    super.init(args);
    this._args = args;
  }

  protected onRegEvent(): void {
    super.onRegEvent();

    this.getNode("slider").on("slide", this.onSliderChange, this);
  }

  protected onEvtShow(): void {
    super.onEvtShow();

    // 涨价
    this._increase = this._args.increase || 0;

    // 配置信息
    let info = JsonMgr.instance.getConfigItem(this._args.itemInfo[0]);

    // 名称
    ///this.getNode("lbl_item_name_main").getComponent(Label).string = info.name;

    // 设置物品
    FmUtils.setItemNode(this.getNode("Item"), this._args.itemInfo[0], this._args.itemInfo[1], true);

    // 设置已有的数量
    this.getNode("lbl_have_num").getComponent(Label).string =
      "拥有:" + PlayerModule.data.getItemNum(this._args.itemInfo[0]);

    // 设置说明
    this.getNode("lbl_detail").getComponent(Label).string = info.des;

    // 货币图标
    FmUtils.setItemIcon(this.getNode("bg_money_icon"), this._args.moneyInfo[0]);

    // 货币数量
    let money = PlayerModule.data.getItemNum(this._args.moneyInfo[0]);

    // 商店限制最大数量
    this._max = this._args.maxNum;
    // 自己可买最大数量
    // let
    let buyMax = 0;
    if (this._increase > 0) {
      // 等差数列求解 `
      let b = (2 * this._args.moneyInfo[1] - this._increase) / 2 / this._increase;
      let max = Math.sqrt(b * b + (2 * money) / this._increase) - b;
      buyMax = Math.floor(max);
    } else {
      buyMax = Math.floor(money / this._args.moneyInfo[1]);
    }

    if (this._max > 0) {
      this._max = buyMax > this._max ? this._max : buyMax;
    } else {
      this._max = buyMax;
    }

    this.changeNum(this._num);
  }

  private on_click_btn_yes() {
    // 货币不足跳转物品界面
    let num = PlayerModule.data.getItemNum(this._args.moneyInfo[0]);
    if (this.cost > num) {
      UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, { itemId: this._args.moneyInfo[0] });
      return;
    }

    UIMgr.instance.back({ resp: { ok: true, num: this._num } });
  }

  private onSliderChange(event) {
    const progress = event.progress;

    this._num = Math.floor(progress * this._max);

    this.changeNum(this._num);
  }

  private changeNum(value: number) {
    this._num = value;

    if (this._num > this._max) {
      this._num = this._max;
    }

    if (this._num < 1) {
      this._num = 1;
    }

    const progress = this._num / this._max;
    this.getNode("slider").getComponent(Slider).progress = progress;
    this.getNode("bg_bar").getComponent(Sprite).fillRange = progress;

    // money
    const money = PlayerModule.data.getItemNum(this._args.moneyInfo[0]);

    // 总花费
    this.cost = 0;
    if (this._increase <= 0) {
      this.cost = this._num * this._args.moneyInfo[1];
    } else {
      this.cost = ((this._args.moneyInfo[1] * 2 + (this._num - 1) * this._increase) * this._num) / 2;
    }
    this.getNode("lbl_cost").getComponent(Label).string = `${Formate.format(money)}/${Formate.format(this.cost)}`;

    // 颜色不同
    this.getNode("lbl_cost").getComponent(Label).color =
      this.cost <= money ? FmColor.COLOR_GREEN_LIGHT : FmColor.COLOR_RED_LIGHT;

    // 数量
    this.getNode("lbl_num").getComponent(Label).string = `数量：${this._num}`;
  }

  private on_click_btn_add() {
    this.changeNum(this._num + 1);
  }

  private on_click_btn_subtract() {
    this.changeNum(this._num - 1);
  }

  private on_click_btn_close() {
    UIMgr.instance.back();
  }
}
