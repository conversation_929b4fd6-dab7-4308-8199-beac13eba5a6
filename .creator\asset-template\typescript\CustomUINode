import { _decorator, Component, Node } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
const { ccclass, property } = _decorator;

/**
 * <%Author%>
 * <%DateTime%>
 * <%URL%>
 * <%ManualUrl%>
 *
 */

@ccclass("<%CamelCaseClassName%>")
export class <%CamelCaseClassName%> extends UINode {
  protected prefab(): string {
    return `${BundleEnum.UNKNOW}?prefab/ui/<%CamelCaseClassName%>`;
  }

  //=================================================
  public init(args: any): void {
    super.init(args);
  }
  protected onEvtShow(): void {
    // do something
  }
}
