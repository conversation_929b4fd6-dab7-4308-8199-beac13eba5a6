import { _decorator } from "cc";
import { PlayerBackInfo } from "../FightDefine";
import { JsonMgr } from "../../mgr/JsonMgr";
import { GOMonster } from "./GOMonster";
import HPSection from "../../../lib/object/HpSection";

const { ccclass, property } = _decorator;

@ccclass("GOClubMirrBoss")
export class GOClubMirrBoss extends GOMonster {
  /**
   * 初始化怪物细节信息
   * @param detail 玩家返回信息
   */
  public onInitDetail(detail: PlayerBackInfo, renderName: string = ""): void {
    super.onInitDetail(detail);
    this._renderName = renderName;
    const db = JsonMgr.instance.jsonList.c_monsterShow[detail.dbId];
    this._dbScale = this.getSceneScale(detail.sceneId, db);
    this._spineId = Number(db.spineId);
  }

  protected async createHPSection(): Promise<void> {
    return new Promise<void>((resolve) => {
      const param = {
        callBack: resolve,
        mirroring: true, // 特定于 GOClubMirrBoss 的逻辑
      };
      this.createSection(HPSection, param);
    });
  }
}
