{"skeleton": {"hash": "6CBWo2rkAfZ49jEPjeScRRNlExA=", "spine": "3.8.75", "x": -189.16, "y": -11.99, "width": 320.6, "height": 332.89}, "bones": [{"name": "root", "x": -14.16, "y": 21.24}, {"name": "bone7", "parent": "root", "x": 176.73, "y": -356.74}, {"name": "bone6", "parent": "bone7", "length": 365.08, "rotation": 3.7, "x": -127.45, "y": 107.32, "scaleX": 0.4145, "scaleY": 0.4145}, {"name": "bone", "parent": "bone6", "length": 166.25, "rotation": -1.03, "x": 21.62, "y": 163.45, "scaleX": 1.2174, "scaleY": 1.2174}, {"name": "bone2", "parent": "bone", "length": 166.25, "x": 166.25}, {"name": "bone3", "parent": "bone2", "length": 166.25, "x": 166.25}, {"name": "bone4", "parent": "bone3", "length": 166.25, "x": 166.25}, {"name": "bone5", "parent": "bone4", "length": 166.25, "x": 166.25}, {"name": "bone8", "parent": "bone7", "x": 670.24, "y": 256.09}, {"name": "bone9", "parent": "root", "x": 314.72, "y": -78.86, "scaleX": 0.2919, "scaleY": 0.2919}, {"name": "bone10", "parent": "bone9", "length": 201.38, "rotation": 90.71, "x": 41.06, "y": 86.57}, {"name": "bone11", "parent": "bone10", "length": 201.38, "x": 201.38}, {"name": "bone12", "parent": "bone11", "length": 201.38, "x": 201.38}, {"name": "bone13", "parent": "bone12", "length": 201.38, "x": 201.38}, {"name": "bone14", "parent": "root", "x": 363.05, "y": -71.61, "scaleX": 0.2919, "scaleY": 0.2919}, {"name": "bone15", "parent": "bone14", "length": 201.38, "rotation": 90.71, "x": 41.06, "y": 86.57}, {"name": "bone16", "parent": "bone15", "length": 201.38, "x": 201.38}, {"name": "bone17", "parent": "bone16", "length": 201.38, "x": 201.38}, {"name": "bone18", "parent": "bone17", "length": 201.38, "x": 201.38}, {"name": "bone19", "parent": "root", "x": 425.2, "y": -96.37, "scaleX": 0.399, "scaleY": 0.399}, {"name": "bone20", "parent": "bone19", "length": 201.38, "rotation": 90.71, "x": 41.06, "y": 86.57}, {"name": "bone21", "parent": "bone20", "length": 201.38, "x": 201.38}, {"name": "bone22", "parent": "bone21", "length": 201.38, "x": 201.38}, {"name": "bone23", "parent": "bone22", "length": 201.38, "x": 201.38}, {"name": "bone53", "parent": "root", "length": 261.18, "rotation": 178.91, "x": 35.73, "y": -71.9}, {"name": "bone54", "parent": "bone53", "length": 38.78, "x": 3.7, "y": -136.73}, {"name": "bone24", "parent": "bone54", "length": 207.14, "rotation": -178.56, "x": -3.37, "y": -0.06, "scaleX": 0.4095, "scaleY": 0.4095, "transform": "noRotationOrReflection"}, {"name": "bone25", "parent": "bone24", "length": 105.83, "rotation": 145.35, "x": -31, "y": 63.75}, {"name": "bone26", "parent": "bone25", "length": 76.51, "rotation": -48.95, "x": 105.83}, {"name": "bone27", "parent": "bone24", "length": 128.62, "rotation": 26.87, "x": 82.48, "y": 49.98}, {"name": "bone28", "parent": "bone27", "length": 92.94, "rotation": 48.01, "x": 128.62}, {"name": "bone29", "parent": "bone28", "length": 86.22, "rotation": -62.64, "x": 92.94}, {"name": "bone30", "parent": "bone26", "length": 64.87, "rotation": 66.42, "x": 76.42, "y": 4.33}, {"name": "bone31", "parent": "bone24", "length": 67.95, "rotation": -108.57, "x": 13.76, "y": 35.7}, {"name": "bone32", "parent": "bone31", "length": 121.78, "rotation": 19.75, "x": 67.95}, {"name": "bone33", "parent": "bone32", "length": 86.28, "rotation": 35.77, "x": 191.9, "y": 24.27}, {"name": "bone34", "parent": "bone33", "length": 100.58, "rotation": 9.31, "x": 86.28}, {"name": "bone35", "parent": "bone32", "length": 151.43, "rotation": -134.79, "x": 45.9, "y": -55.99}, {"name": "bone36", "parent": "bone35", "length": 136.09, "rotation": 111.77, "x": 151.43}, {"name": "bone37", "parent": "bone32", "length": 156.01, "rotation": 123.05, "x": 44.89, "y": 109.59}, {"name": "bone38", "parent": "bone37", "length": 168.96, "rotation": -102.74, "x": 156.01}, {"name": "bone39", "parent": "bone33", "length": 99.84, "rotation": -75.78, "x": 21.16, "y": -44.13}, {"name": "bone40", "parent": "bone39", "length": 99.72, "rotation": 28.45, "x": 99.84}, {"name": "bone41", "parent": "bone40", "length": 115.68, "rotation": 21.02, "x": 99.72}, {"name": "bone42", "parent": "bone40", "length": 65.52, "rotation": -77.63, "x": 62.39, "y": -22.88}, {"name": "bone43", "parent": "bone39", "length": 89.92, "rotation": -38.21, "x": 81.63, "y": -17.89}, {"name": "bone44", "parent": "bone33", "length": 159.31, "rotation": 48.71, "x": 91.76, "y": 93.3}, {"name": "bone45", "parent": "bone44", "length": 127.18, "rotation": -31.55, "x": 159.31}, {"name": "bone46", "parent": "bone45", "length": 114.75, "rotation": -39.53, "x": 127.18}, {"name": "bone47", "parent": "bone44", "length": 107.21, "rotation": -96.28, "x": 142.72, "y": -38.06}, {"name": "bone48", "parent": "bone45", "length": 88.27, "rotation": 29.55, "x": 34.25, "y": 32.3}, {"name": "bone49", "parent": "bone45", "length": 75.91, "rotation": 15.92, "x": 149.06, "y": 23.82}, {"name": "bone50", "parent": "bone46", "length": 72.13, "rotation": 16.42, "x": 76.78, "y": 22.54}, {"name": "bone51", "parent": "bone46", "length": 36.05, "rotation": -59.93, "x": 17.59, "y": -18.09}, {"name": "bone52", "parent": "bone46", "length": 49.59, "rotation": -61.67, "x": 104.19, "y": -14.81}, {"name": "txall", "parent": "root", "x": 420.82, "y": 110.86}, {"name": "tx2", "parent": "txall", "x": 17.14, "y": -1.56, "scaleX": 2.4288, "scaleY": 2.4288}, {"name": "tx3", "parent": "txall", "x": -48.3, "y": -4.67, "scaleX": 2.4288, "scaleY": 2.4288}, {"name": "tx4", "parent": "txall", "rotation": 136.14, "x": -191.76, "y": 118.6, "scaleX": -4.4462, "scaleY": 4.4463}, {"name": "tx5", "parent": "txall"}, {"name": "chong<PERSON>", "parent": "root", "rotation": -91.31, "x": 144.4, "y": 136.05, "scaleX": 0.798, "scaleY": 3.5191}, {"name": "chongji2", "parent": "root", "rotation": 85.72, "x": 368.49, "y": 177.71, "scaleX": 0.9119, "scaleY": 2.3596}, {"name": "chongji3", "parent": "root", "rotation": 78.27, "x": 411.52, "y": 134.14, "scaleX": 0.9119, "scaleY": 2.3596}, {"name": "chongji4", "parent": "root", "rotation": 112.76, "x": 456.39, "y": 112.82, "scaleX": 0.9119, "scaleY": 2.3596}, {"name": "jio3", "parent": "bone53", "rotation": -178.91, "x": 88.6, "y": -53.06}, {"name": "jio2", "parent": "bone53", "rotation": -178.91, "x": -55.65, "y": -58.17}, {"name": "jio4", "parent": "jio3", "x": -34.96, "y": -7.39}, {"name": "jio5", "parent": "jio2", "x": 27.14, "y": -6.46}], "slots": [{"name": "shuren01", "bone": "root", "attachment": "shuren01"}, {"name": "shuren02", "bone": "bone35", "attachment": "shuren02"}, {"name": "shuren03", "bone": "bone36", "attachment": "shuren03"}, {"name": "shuren04", "bone": "root", "attachment": "shuren04"}, {"name": "shuren05", "bone": "bone37", "attachment": "shuren05"}, {"name": "shuren06", "bone": "bone38", "attachment": "shuren06"}, {"name": "shuren07", "bone": "root", "attachment": "shuren07"}, {"name": "shuren8", "bone": "bone10"}, {"name": "shuren9", "bone": "bone15"}, {"name": "shuren10", "bone": "bone20"}, {"name": "bone7", "bone": "bone7", "attachment": "bone7"}, {"name": "shuren08", "bone": "root"}, {"name": "qxbd_02/baodian02_00", "bone": "tx2", "color": "ffda00ff", "dark": "d27f13"}, {"name": "renoutuowei/tuiwei_01", "bone": "tx4", "color": "f9a400ff", "dark": "cd8405"}, {"name": "qxbd_03/baodian03_00", "bone": "tx3", "color": "f9a400ff"}, {"name": "glow02_00", "bone": "tx5", "color": "ffda00ff", "dark": "ffc300"}, {"name": "chongji/chang<PERSON>ci (1)", "bone": "chong<PERSON>"}, {"name": "chongji/changcici (1)2", "bone": "chongji2"}, {"name": "chongji/changcici (1)3", "bone": "chongji3"}, {"name": "chongji/changcici (1)4", "bone": "chongji4"}, {"name": "eye", "bone": "bone32"}], "ik": [{"name": "jio1", "bones": ["bone27", "bone28"], "target": "jio3"}, {"name": "jio2", "order": 1, "bones": ["bone25", "bone26"], "target": "jio2", "bendPositive": false}, {"name": "jio3", "order": 2, "bones": ["bone29"], "target": "jio4"}, {"name": "jio4", "order": 3, "bones": ["bone30"], "target": "jio5"}], "skins": [{"name": "default", "attachments": {"chongji/changcici (1)3": {"chongji/changcici (1)": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [329, -61, -329, -61, -329, 61, 329, 61], "hull": 4}, "chongji/changcici (3)": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [329, -61, -329, -61, -329, 61, 329, 61], "hull": 4}, "chongji/changcici (5)": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [329, -61, -329, -61, -329, 61, 329, 61], "hull": 4}, "chongji/changcici (7)": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [329, -61, -329, -61, -329, 61, 329, 61], "hull": 4}}, "chongji/changcici (1)": {"chongji/changcici (1)": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [329, -61, -329, -61, -329, 61, 329, 61], "hull": 4}, "chongji/changcici (3)": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [329, -61, -329, -61, -329, 61, 329, 61], "hull": 4}, "chongji/changcici (5)": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [329, -61, -329, -61, -329, 61, 329, 61], "hull": 4}, "chongji/changcici (7)": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [329, -61, -329, -61, -329, 61, 329, 61], "hull": 4}}, "shuren01": {"shuren01": {"type": "mesh", "uvs": [0.0229, 1, 0, 0.89866, 0.02102, 0.7864, 0.16227, 0.70064, 0.19806, 0.63001, 0.25833, 0.60857, 0.26963, 0.6464, 0.33178, 0.58208, 0.29034, 0.51649, 0.26209, 0.46983, 0.20371, 0.41938, 0.22443, 0.35758, 0.26963, 0.29704, 0.32236, 0.36262, 0.33931, 0.41938, 0.3412, 0.45091, 0.34921, 0.49782, 0.35829, 0.50921, 0.36736, 0.46742, 0.35375, 0.39829, 0.35261, 0.35194, 0.36623, 0.32459, 0.32425, 0.29344, 0.29362, 0.27445, 0.35148, 0.26077, 0.38551, 0.26685, 0.40594, 0.21692, 0.3719, 0.15842, 0.32312, 0.10752, 0.25504, 0.09764, 0.18697, 0.07788, 0.14499, 0.04218, 0.09621, 0.01482, 0.20739, 0, 0.30723, 0.00115, 0.36509, 0.05585, 0.3719, 0.08548, 0.42069, 0.11739, 0.45812, 0.17513, 0.4878, 0.21663, 0.45741, 0.27477, 0.51059, 0.2159, 0.5681, 0.19337, 0.64298, 0.15849, 0.64732, 0.26895, 0.64841, 0.35761, 0.5833, 0.39904, 0.48888, 0.41503, 0.46935, 0.43029, 0.47586, 0.47462, 0.51493, 0.48116, 0.605, 0.44918, 0.65166, 0.46008, 0.6853, 0.40122, 0.75584, 0.35471, 0.86376, 0.36296, 1, 0.37151, 0.96593, 0.43886, 0.87653, 0.48483, 0.77915, 0.49873, 0.89888, 0.5308, 0.84141, 0.5725, 0.75999, 0.59388, 0.65782, 0.56395, 0.59396, 0.53829, 0.53011, 0.58212, 0.48062, 0.57677, 0.47115, 0.62418, 0.41961, 0.65305, 0.36806, 0.69447, 0.35119, 0.72648, 0.40086, 0.71518, 0.46459, 0.67878, 0.49646, 0.64426, 0.54144, 0.68004, 0.63798, 0.70138, 0.71577, 0.65493, 0.79824, 0.65054, 0.88634, 0.69134, 0.86478, 0.7246, 0.78418, 0.74908, 0.7645, 0.7723, 0.65953, 0.77606, 0.583, 0.75718, 0.50867, 0.74396, 0.4657, 0.77974, 0.42737, 0.80385, 0.36233, 0.83107, 0.26361, 0.8054, 0.21483, 0.82874, 0.28568, 0.84429, 0.30194, 0.87774, 0.2729, 0.88707, 0.22528, 0.90807, 0.18463, 1, 0.09074, 0.91102, 0.24329, 0.86277, 0.15713, 0.88264, 0.14442, 0.81642, 0.22352, 0.74642, 0.31675, 0.67737, 0.37608, 0.61588, 0.41563, 0.53358, 0.4128, 0.4371, 0.41845, 0.33777, 0.47354, 0.35669, 0.53852, 0.2971, 0.58513, 0.23088, 0.42128, 0.2744, 0.3902, 0.29142, 0.42834, 0.2148, 0.40998, 0.15995, 0.34924, 0.10035, 0.27013, 0.05306, 0.19668, 0.02941, 0.53287, 0.51373, 0.61338, 0.48914, 0.70237, 0.53265, 0.7956, 0.53738, 0.69672, 0.48157, 0.773, 0.43522, 0.89307, 0.39738, 0.30121, 0.77008, 0.39585, 0.76724, 0.45094, 0.73319, 0.50179, 0.70387, 0.59361, 0.72752, 0.67412, 0.73224, 0.74475, 0.7067, 0.81679, 0.68873], "triangles": [104, 105, 103, 104, 103, 19, 104, 40, 105, 104, 108, 40, 47, 105, 46, 45, 46, 106, 19, 20, 104, 46, 105, 106, 106, 44, 45, 105, 40, 106, 20, 21, 104, 21, 109, 104, 104, 109, 108, 21, 22, 109, 109, 24, 25, 24, 109, 22, 40, 41, 106, 106, 107, 44, 106, 41, 107, 22, 23, 24, 109, 25, 108, 39, 40, 110, 40, 108, 110, 25, 26, 108, 108, 26, 110, 107, 43, 44, 41, 42, 107, 107, 42, 43, 26, 111, 110, 26, 27, 111, 39, 110, 38, 110, 111, 38, 38, 111, 37, 37, 111, 112, 28, 112, 27, 111, 27, 112, 37, 112, 36, 112, 28, 113, 28, 29, 113, 112, 35, 36, 112, 113, 35, 29, 30, 113, 30, 114, 113, 30, 31, 114, 113, 34, 35, 114, 33, 113, 113, 33, 34, 114, 31, 33, 33, 31, 32, 63, 117, 62, 62, 118, 61, 62, 117, 118, 64, 65, 115, 65, 66, 115, 66, 102, 115, 61, 118, 60, 63, 64, 117, 64, 116, 117, 64, 115, 116, 117, 59, 118, 118, 59, 60, 115, 49, 50, 117, 116, 119, 116, 52, 119, 117, 119, 59, 115, 50, 116, 119, 120, 59, 59, 120, 58, 50, 51, 116, 116, 51, 52, 58, 121, 57, 58, 120, 121, 120, 119, 53, 119, 52, 53, 57, 121, 56, 53, 54, 120, 120, 55, 121, 120, 54, 55, 121, 55, 56, 87, 123, 86, 87, 122, 123, 86, 123, 85, 123, 124, 85, 85, 124, 84, 83, 126, 82, 82, 127, 81, 82, 126, 127, 81, 127, 80, 80, 127, 128, 122, 70, 123, 123, 71, 124, 123, 70, 71, 83, 84, 126, 80, 129, 79, 80, 128, 129, 124, 125, 84, 84, 125, 126, 124, 72, 125, 124, 71, 72, 126, 75, 127, 127, 75, 128, 125, 74, 126, 126, 74, 75, 79, 129, 78, 75, 76, 128, 128, 77, 129, 128, 76, 77, 125, 72, 74, 78, 129, 77, 72, 73, 74, 69, 100, 68, 100, 101, 68, 100, 6, 101, 68, 101, 67, 6, 7, 101, 67, 101, 66, 101, 102, 66, 101, 7, 102, 7, 17, 102, 17, 8, 16, 17, 7, 8, 115, 102, 49, 17, 18, 102, 49, 102, 103, 103, 102, 18, 8, 9, 16, 9, 15, 16, 103, 48, 49, 9, 10, 14, 9, 14, 15, 13, 10, 11, 10, 13, 14, 19, 103, 18, 47, 48, 103, 47, 103, 105, 11, 12, 13, 0, 95, 94, 94, 95, 93, 93, 95, 97, 0, 1, 95, 97, 95, 98, 93, 96, 92, 93, 97, 96, 95, 1, 98, 1, 2, 98, 92, 96, 91, 97, 89, 96, 97, 98, 89, 96, 90, 91, 96, 89, 90, 88, 122, 87, 88, 89, 99, 89, 98, 99, 99, 98, 3, 88, 99, 122, 98, 2, 3, 122, 99, 70, 99, 100, 70, 99, 6, 100, 99, 3, 6, 70, 100, 69, 3, 4, 6, 4, 5, 6], "vertices": [2, 41, -25.86, -5.57, 1, 44, -113.66, -154.8, 0, 1, 41, -2.62, 19.43, 1, 1, 41, 29.56, 39.02, 1, 2, 41, 71.52, 32.13, 0.91134, 42, -9.59, 41.74, 0.08866, 2, 41, 94.9, 40.52, 0.66281, 42, 14.96, 37.98, 0.33719, 2, 41, 108.85, 34.44, 0.60028, 42, 24.33, 25.99, 0.39972, 2, 41, 100.55, 24.63, 0.51047, 42, 12.36, 21.31, 0.48953, 2, 41, 125.95, 27.11, 0.00867, 42, 35.87, 11.4, 0.99133, 1, 42, 55.76, 24.3, 1, 1, 42, 69.97, 33.21, 1, 1, 42, 84.25, 48.96, 1, 1, 42, 105.32, 47.99, 1, 1, 42, 126.92, 41.58, 1, 1, 42, 107.46, 26.19, 1, 1, 42, 89.51, 19.18, 1, 1, 42, 79.25, 16.94, 1, 1, 42, 64.18, 12.46, 1, 1, 42, 60.79, 9.8, 1, 1, 42, 74.85, 10.23, 1, 2, 42, 96.99, 17.23, 0.46222, 43, 3.63, 17.06, 0.53778, 2, 42, 112.14, 20.17, 0.03247, 43, 18.83, 14.37, 0.96753, 2, 42, 121.64, 18.77, 0.00053, 43, 27.19, 9.66, 0.99947, 1, 43, 39.16, 16.87, 1, 1, 43, 46.67, 22.37, 1, 1, 43, 48.68, 8.84, 1, 1, 43, 45.25, 1.77, 1, 1, 43, 60.7, -5.87, 1, 1, 43, 81.27, -2.13, 1, 1, 43, 99.98, 5.32, 1, 1, 43, 106.11, 19.59, 1, 1, 43, 115.46, 33.24, 1, 1, 43, 128.91, 40.17, 1, 1, 43, 139.93, 49.11, 1, 1, 43, 140.05, 23.83, 1, 1, 43, 135.43, 2.05, 1, 1, 43, 115.09, -7.15, 1, 1, 43, 105.11, -6.76, 1, 1, 43, 92.61, -15.41, 1, 1, 43, 72.14, -19.95, 1, 1, 43, 57.32, -23.81, 1, 1, 43, 39.6, -13.47, 1, 1, 43, 56.59, -28.84, 1, 1, 43, 61.51, -42.86, 1, 1, 43, 69.73, -61.47, 1, 1, 43, 33.43, -55.41, 1, 2, 42, 121.77, -45.11, 0.01915, 43, 4.4, -50.02, 0.98085, 2, 42, 105.66, -33.22, 0.08876, 43, -6.37, -33.14, 0.91124, 3, 42, 96.75, -13.41, 0.53956, 44, -1.89, 35.59, 0.00443, 43, -7.58, -11.46, 0.45601, 3, 42, 90.99, -10.01, 0.88856, 44, -6.45, 30.69, 0.02352, 43, -11.74, -6.21, 0.08792, 2, 42, 76.7, -14.01, 0.62512, 44, -5.6, 15.88, 0.37488, 2, 42, 76.07, -22.97, 0.16547, 44, 3.01, 13.35, 0.83453, 1, 44, 23.52, 23.17, 1, 1, 44, 33.77, 19.12, 1, 1, 44, 42.06, 38.4, 1, 1, 44, 58.41, 53.23, 1, 1, 44, 82.34, 49.51, 1, 1, 44, 112.58, 45.42, 1, 1, 44, 104.08, 23.32, 1, 1, 44, 83.53, 8.84, 1, 1, 44, 61.65, 5.1, 1, 1, 44, 87.89, -6.66, 1, 1, 44, 74.52, -20.01, 1, 1, 44, 56.09, -26.38, 1, 1, 44, 33.73, -15.49, 1, 2, 45, 67.25, 63.49, 0, 44, 19.85, -6.38, 1, 3, 42, 43.56, -32.16, 0.11401, 45, 50.03, 52.57, 0, 44, 5.03, -20.38, 0.88599, 3, 42, 43.39, -20.98, 0.47918, 45, 39.7, 56.84, 1e-05, 44, -5.93, -18.15, 0.52081, 3, 42, 27.48, -21.65, 0.86172, 45, 34.01, 41.96, 5e-05, 44, -8.68, -33.84, 0.13823, 3, 42, 16.01, -12.01, 0.98014, 45, 20.61, 35.26, 9e-05, 44, -20.56, -42.97, 0.01977, 3, 41, 101.68, -2.52, 0.40488, 42, 0.42, -3.09, 0.59511, 44, -32.61, -56.29, 2e-05, 2, 41, 91, -6.2, 0.72766, 45, 0.13, 14.97, 0.27234, 3, 41, 100.83, -12.53, 0.16275, 45, 11.78, 16.08, 0.83725, 44, -25.58, -63.48, 0, 3, 41, 119.17, -16.12, 0.00873, 45, 28.4, 24.61, 0.99127, 44, -10.88, -51.95, 0, 2, 45, 37.96, 34.16, 1, 44, -3.32, -40.75, 0, 2, 45, 44.98, 20.25, 1, 44, 6.22, -53.06, 0, 2, 45, 64.29, 8.38, 1, 44, 27.44, -61.04, 0, 1, 45, 84.74, 19.44, 1, 1, 45, 102.97, 16.63, 1, 1, 45, 118.96, -1.12, 1, 3, 41, 162.75, -95.47, 0, 45, 111.73, -10.79, 1, 44, 77.66, -70.83, 0, 2, 41, 145.19, -86.47, 0, 45, 92.36, -14.58, 1, 2, 41, 136.4, -87.85, 0, 45, 86.31, -21.1, 1, 2, 41, 120.86, -70.3, 0, 45, 63.24, -16.93, 1, 2, 41, 115.16, -53.03, 0, 45, 48.08, -6.88, 1, 2, 45, 32.97, 1.22, 1, 44, -1.95, -74.03, 0, 1, 45, 20.9, -8.16, 1, 1, 45, 10.73, -14.01, 1, 2, 41, 65.28, -29.82, 0.04797, 45, -5.47, -19.49, 0.95203, 3, 41, 58.27, -7.27, 0.86059, 45, -24.92, -6.1, 0.13941, 44, -57.39, -92.25, 0, 1, 41, 45.42, -3.59, 1, 1, 41, 51.19, -19.18, 1, 1, 41, 44.73, -28.95, 1, 1, 41, 38.27, -25.81, 1, 1, 41, 26.19, -21.85, 1, 1, 41, -3.42, -33.8, 1, 1, 41, 6.75, 1.03, 1, 1, 41, 40.5, -15.61, 1, 1, 41, 23.36, -4.68, 1, 1, 41, 38.86, 11.26, 1, 2, 41, 68.08, 11.95, 0.98475, 42, -22.23, 25.64, 0.01525, 2, 41, 99.02, 9.99, 0.43383, 42, 4.04, 9.17, 0.56617, 3, 42, 26.5, -0.29, 0.9974, 45, 14.02, 49.54, 0, 44, -29.75, -30.21, 0.0026, 3, 42, 55.03, -4.2, 0.90438, 45, 28.91, 74.17, 0, 44, -19.82, -3.19, 0.09562, 1, 42, 86.56, 2.01, 1, 1, 43, 20.66, -0.94, 1, 2, 42, 115.28, -6.66, 0.03272, 43, 12.14, -11.8, 0.96728, 1, 43, 28.86, -29.81, 1, 1, 43, 48.52, -44.21, 1, 1, 43, 41.26, -5.58, 1, 1, 43, 37.01, 2.3, 1, 1, 43, 60.44, -10.91, 1, 1, 43, 79.15, -10.37, 1, 1, 43, 101.21, -0.85, 1, 1, 43, 120.04, 13.46, 1, 1, 43, 130.89, 28.04, 1, 1, 44, 6.57, 2.35, 1, 1, 44, 24.84, 9.8, 1, 1, 44, 44.08, -5.49, 1, 1, 44, 64.79, -7.91, 1, 1, 44, 43.52, 11.56, 1, 1, 44, 61.14, 26.29, 1, 1, 44, 88.41, 37.79, 1, 3, 41, 72.7, -6.51, 0.7461, 45, -14.06, 3.41, 0.2539, 44, -48.53, -80.84, 0, 1, 45, 6.7, -0.53, 1, 3, 41, 103.09, -25.01, 0.02473, 45, 21.27, 7.68, 0.97527, 44, -14.67, -69.92, 0, 3, 41, 117.79, -27.81, 0.00246, 45, 34.55, 14.57, 0.99754, 44, -2.94, -60.63, 0, 2, 45, 52.66, 2.19, 1, 44, 17.2, -69.33, 0, 2, 41, 134.3, -63.77, 0, 45, 69.77, -3.48, 1, 1, 45, 87.06, 1.17, 1, 1, 45, 104.07, 3.3, 1], "hull": 95}}, "shuren02": {"shuren02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [194.41, 27.28, 64.03, -95.37, -31.9, 6.6, 98.48, 129.25], "hull": 4}}, "shuren03": {"shuren03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [7.86, -92.54, -32.43, 9.81, 128.55, 73.17, 168.84, -29.19], "hull": 4}}, "shuren04": {"shuren04": {"type": "mesh", "uvs": [0.2436, 0.24762, 0.19087, 0.20532, 0.23042, 0.09536, 0.34546, 0.02663, 0.43294, 0, 0.52402, 0.01817, 0.57915, 0.08267, 0.58753, 0.12073, 0.6127, 0.20215, 0.65464, 0.273, 0.70046, 0.32275, 0.71696, 0.34067, 0.7613, 0.43266, 0.75051, 0.52783, 0.74212, 0.60185, 0.71696, 0.68432, 0.70857, 0.72344, 0.76489, 0.72027, 0.83399, 0.79155, 0.86024, 0.87734, 0.90789, 0.87648, 0.97207, 0.92196, 1, 0.95627, 1, 0.98287, 0.91956, 0.98116, 0.86608, 0.96829, 0.78537, 0.98545, 0.72023, 0.97687, 0.76495, 0.93568, 0.73189, 0.91338, 0.693, 0.87305, 0.65313, 0.86361, 0.64074, 0.82584, 0.63803, 0.85213, 0.65699, 0.88457, 0.69221, 0.93511, 0.68834, 0.96072, 0.58534, 0.97645, 0.47409, 0.96881, 0.45028, 0.92172, 0.45462, 0.84742, 0.46407, 0.8303, 0.44616, 0.84435, 0.43322, 0.88649, 0.40244, 0.91037, 0.35081, 0.92682, 0.3085, 0.93188, 0.33217, 0.96732, 0.33001, 1, 0.23091, 1, 0.12581, 1, 0, 1, 0, 0.94191, 0.07065, 0.91528, 0.12789, 0.86937, 0.20178, 0.84825, 0.25589, 0.80326, 0.28191, 0.74816, 0.34643, 0.74173, 0.40158, 0.6995, 0.45778, 0.63522, 0.44425, 0.55625, 0.40366, 0.51402, 0.40054, 0.44699, 0.32665, 0.43321, 0.31424, 0.31592, 0.43151, 0.07778, 0.454, 0.1203, 0.51023, 0.20535, 0.58252, 0.31166, 0.39296, 0.37829, 0.63715, 0.38963, 0.65642, 0.4605, 0.66767, 0.53705, 0.65482, 0.60934, 0.59698, 0.68872, 0.5777, 0.76526, 0.56164, 0.84039, 0.57449, 0.91127, 0.65, 0.744, 0.72068, 0.78653, 0.7978, 0.82905, 0.81868, 0.90985, 0.87973, 0.92686, 0.93596, 0.94529, 0.51344, 0.72416, 0.4524, 0.76101, 0.37368, 0.79503, 0.30621, 0.81204, 0.27568, 0.87866, 0.23231, 0.93536, 0.14877, 0.94812, 0.07005, 0.95521, 0.46081, 0.35541, 0.47714, 0.43066, 0.48985, 0.49071, 0.49801, 0.55555, 0.48894, 0.60199, 0.53249, 0.33139, 0.61414, 0.35678, 0.66587, 0.32739], "triangles": [64, 65, 70, 66, 3, 4, 66, 4, 5, 67, 66, 5, 67, 5, 6, 67, 6, 7, 68, 67, 7, 68, 7, 8, 2, 67, 0, 66, 2, 3, 67, 2, 66, 65, 0, 67, 1, 2, 0, 68, 65, 67, 68, 93, 65, 69, 68, 8, 69, 8, 9, 69, 9, 100, 69, 98, 68, 64, 70, 63, 99, 69, 100, 63, 70, 94, 70, 93, 94, 98, 69, 99, 94, 93, 98, 99, 94, 98, 71, 99, 100, 71, 12, 72, 100, 10, 11, 11, 71, 100, 12, 71, 11, 95, 94, 72, 71, 94, 99, 72, 94, 71, 63, 94, 95, 62, 63, 95, 13, 72, 12, 73, 72, 13, 95, 72, 73, 96, 95, 73, 61, 62, 95, 96, 61, 95, 14, 73, 13, 97, 61, 96, 74, 96, 73, 74, 73, 14, 97, 96, 74, 60, 61, 97, 15, 74, 14, 98, 93, 68, 70, 65, 93, 100, 9, 10, 75, 97, 74, 75, 74, 15, 85, 60, 97, 15, 79, 75, 75, 85, 97, 76, 85, 75, 76, 75, 79, 83, 19, 20, 84, 83, 20, 82, 19, 83, 21, 84, 20, 84, 21, 22, 25, 82, 83, 24, 25, 83, 84, 24, 83, 84, 22, 23, 24, 84, 23, 82, 81, 19, 82, 29, 81, 28, 29, 82, 26, 28, 82, 26, 82, 25, 27, 28, 26, 16, 79, 15, 80, 16, 17, 79, 16, 80, 80, 17, 18, 32, 76, 79, 32, 79, 80, 81, 80, 18, 77, 76, 32, 33, 77, 32, 30, 31, 32, 80, 30, 32, 30, 80, 81, 19, 81, 18, 29, 30, 81, 78, 77, 33, 78, 33, 34, 35, 78, 34, 36, 37, 35, 91, 54, 55, 90, 91, 55, 53, 54, 91, 92, 52, 53, 92, 53, 91, 51, 52, 92, 50, 92, 91, 51, 92, 50, 49, 91, 90, 50, 91, 49, 46, 49, 90, 48, 49, 47, 89, 56, 88, 55, 56, 89, 45, 89, 88, 44, 45, 88, 46, 89, 45, 90, 55, 89, 90, 89, 46, 46, 47, 49, 59, 60, 85, 86, 59, 85, 58, 59, 86, 87, 58, 86, 88, 57, 58, 88, 58, 87, 56, 57, 88, 76, 41, 86, 76, 86, 85, 87, 86, 41, 77, 41, 76, 42, 87, 41, 43, 87, 42, 44, 87, 43, 88, 87, 44, 35, 37, 78, 40, 77, 78, 77, 40, 41, 78, 39, 40, 38, 39, 78, 38, 78, 37], "vertices": [3, 34, 263.73, 230.16, 0.00257, 35, 178.62, 125.08, 0.05212, 36, 104.35, 115.25, 0.94532, 2, 35, 221.72, 133.01, 0.00926, 36, 148.03, 118.84, 0.99074, 1, 36, 187.47, 48.77, 1, 1, 36, 174.22, -35.64, 1, 1, 36, 151.07, -87.64, 1, 2, 35, 201.32, -109.02, 0.0023, 36, 103.6, -119.95, 0.9977, 2, 35, 145.01, -108.74, 0.06402, 36, 47.6, -114.06, 0.93598, 2, 35, 120.78, -96.77, 0.15453, 36, 24.69, -99.72, 0.84547, 2, 35, 66.26, -74.68, 0.68867, 36, -27.36, -72.32, 0.31133, 3, 34, 239.26, -22.06, 0.04288, 35, 11.35, -65.28, 0.94757, 36, -81.06, -57.48, 0.00956, 3, 34, 203.84, -49.3, 0.91454, 35, -33.31, -66.67, 0.08493, 36, -125.64, -54.42, 0.00053, 2, 34, 191.08, -59.11, 0.8892, 35, -49.39, -67.17, 0.1108, 2, 34, 126.25, -84.64, 0.93006, 35, -116.92, -50.01, 0.06994, 3, 27, -45.01, 156.29, 0.00152, 33, 150.3, -51.42, 0.00535, 34, 60.13, -76.23, 0.99313, 2, 33, 99.69, -62.65, 0.19971, 34, 8.69, -69.69, 0.80029, 2, 33, 40.24, -65.92, 0.80973, 34, -48.36, -52.68, 0.19027, 3, 27, 10.52, 29.12, 0.85076, 33, 12.73, -69.55, 0.14172, 34, -75.48, -46.79, 0.00752, 2, 27, 37.89, 50.47, 0.99659, 33, 25.66, -101.76, 0.00341, 2, 27, 101, 33.38, 0.89454, 28, -28.35, 18.28, 0.10546, 2, 28, 28.79, 33.57, 0.46947, 32, 7.71, 42.22, 0.53053, 2, 28, 34.16, 67.23, 0.03192, 32, 40.73, 50.64, 0.96808, 1, 32, 83.3, 22.92, 1, 1, 32, 102.64, 0.71, 1, 1, 32, 104.37, -17.75, 1, 2, 28, 105.99, 51.54, 0.00793, 32, 55.03, -21.19, 0.99207, 2, 28, 89.66, 22.9, 0.27169, 32, 22.29, -17.65, 0.72831, 1, 28, 102.29, -27.5, 1, 1, 28, 91.57, -66.57, 1, 2, 27, 122.39, -73.57, 0.05028, 28, 66.36, -35.82, 0.94972, 3, 29, -77.79, 183.11, 0.00097, 27, 96.87, -72.13, 0.26212, 28, 48.52, -54.13, 0.73691, 3, 29, -69.42, 147.23, 0.02481, 27, 61.34, -62.39, 0.68152, 28, 17.84, -74.52, 0.29367, 4, 29, -50.63, 130.19, 0.06969, 27, 37.4, -70.78, 0.79272, 28, 8.44, -98.08, 0.13697, 33, -90.71, -67.69, 0.00061, 4, 29, -55.89, 103.31, 0.20794, 27, 16.28, -53.34, 0.73298, 28, -18.58, -102.56, 0.03156, 33, -68.1, -52.23, 0.02752, 4, 29, -46.02, 118.85, 0.39449, 27, 25.23, -69.42, 0.57949, 28, -0.57, -106.37, 0.00553, 33, -86.03, -56.38, 0.02049, 4, 29, -46.05, 144.29, 0.46408, 27, 47.61, -81.53, 0.53116, 28, 23.25, -97.44, 0.00038, 33, -103.87, -74.52, 0.00438, 2, 29, -49.2, 185.52, 0.48371, 27, 85.35, -98.42, 0.51629, 2, 29, -38.92, 200.3, 0.48522, 27, 93.45, -114.51, 0.51478, 2, 29, 22.42, 181.09, 0.52401, 27, 47.3, -159.26, 0.47599, 2, 29, 80.83, 145.06, 0.58689, 27, -12.22, -193.42, 0.41311, 2, 29, 78.84, 109.17, 0.61369, 27, -42.81, -174.56, 0.38631, 4, 29, 52.78, 64.34, 0.71546, 30, -2.91, 99.41, 0.0153, 27, -69.79, -130.28, 0.26274, 33, -118.18, 51.79, 0.00651, 4, 29, 42.15, 56.39, 0.77952, 30, -15.93, 101.99, 0.03641, 27, -71.72, -117.14, 0.17466, 33, -105.02, 50, 0.00941, 4, 29, 56.43, 60.06, 0.78386, 30, -3.65, 93.84, 0.14292, 27, -75.3, -131.44, 0.07169, 33, -117.77, 57.4, 0.00153, 3, 29, 76.94, 82.54, 0.61902, 30, 26.78, 93.63, 0.3663, 27, -65.31, -160.19, 0.01468, 3, 29, 101.39, 88.68, 0.47758, 30, 47.7, 79.57, 0.51836, 27, -71.57, -184.61, 0.00406, 3, 29, 134.87, 84.36, 0.23099, 30, 66.88, 51.79, 0.76897, 27, -91.34, -211.98, 4e-05, 3, 29, 159.62, 75.6, 0.04385, 30, 76.93, 27.53, 0.89006, 31, -31.81, -1.57, 0.0661, 3, 29, 157.97, 104.21, 0.00276, 30, 97.1, 47.89, 0.59423, 31, -40.63, 25.7, 0.40301, 2, 30, 119.46, 52.42, 0.51501, 31, -34.37, 47.65, 0.48499, 2, 30, 135, -6.51, 0.00542, 31, 25.11, 34.36, 0.99458, 1, 31, 88.2, 20.28, 1, 1, 31, 163.71, 3.41, 1, 1, 31, 154.88, -36.1, 1, 2, 30, 103.03, -116.87, 0.03205, 31, 108.43, -44.75, 0.96795, 2, 30, 63.11, -90.99, 0.23467, 31, 67.1, -68.31, 0.76533, 2, 30, 37.29, -50.8, 0.7163, 31, 19.55, -72.77, 0.2837, 3, 29, 147.39, -18.93, 0.2052, 30, -1.51, -26.62, 0.78267, 31, -19.77, -96.12, 0.01213, 2, 29, 115.6, -45.76, 0.93727, 30, -42.72, -20.93, 0.06273, 1, 29, 78.26, -31.6, 1, 4, 29, 34.64, -42.27, 0.97675, 33, -30.44, 115.01, 0.00914, 34, -53.72, 141.49, 0.0137, 35, -130.79, 238.68, 0.00041, 4, 29, -16.59, -66.3, 0.10396, 33, 22.92, 96.19, 0.45332, 34, -9.87, 105.74, 0.41072, 35, -116.1, 184.04, 0.032, 4, 33, 72.6, 121.29, 0.17155, 34, 45.37, 112.58, 0.77932, 35, -67.27, 157.3, 0.04512, 36, -137.11, 171.82, 0.004, 4, 33, 92.76, 154.21, 0.07964, 34, 75.47, 136.75, 0.86958, 35, -28.73, 159.31, 0.02403, 36, -98.55, 169.98, 0.02675, 4, 33, 136.54, 170.64, 0.01928, 34, 122.23, 137.41, 0.66792, 35, 9.6, 132.52, 0.13025, 36, -63.09, 139.51, 0.18255, 5, 29, -9.27, -228.39, 0.00817, 33, 131.45, 216.8, 0.00081, 34, 133.04, 182.58, 0.32781, 35, 44.77, 162.85, 0.32148, 36, -25.06, 166.18, 0.34173, 4, 29, -39.87, -304.59, 0.00019, 34, 214.98, 188.02, 0.05158, 35, 114.43, 119.38, 0.32986, 36, 39.91, 115.97, 0.61837, 1, 36, 112.04, -50.01, 1, 2, 35, 170.79, -31.63, 0.00131, 36, 80.94, -39.89, 0.99869, 2, 35, 102.68, -23.21, 0.21221, 36, 14.01, -24.73, 0.78779, 1, 35, 16.79, -13.66, 1, 4, 33, 180.57, 190.04, 0.00053, 34, 170.22, 140.79, 0.21374, 35, 50.51, 107.21, 0.50212, 36, -24.9, 110.24, 0.28361, 1, 34, 158.29, -9.12, 1, 1, 34, 108.59, -19.65, 1, 1, 34, 55.07, -25.13, 1, 2, 33, 77.94, -13.28, 0.28699, 34, 4.91, -15.88, 0.71301, 4, 29, -75.67, 6.01, 0.01419, 33, 14.26, 3.21, 0.9833, 34, -49.44, 21.16, 0.00247, 35, -197.64, 138.54, 4e-05, 3, 29, -40.72, 48.04, 0.31793, 27, -39.53, -40.31, 0.31012, 33, -40.12, -2.21, 0.37194, 4, 29, -7.99, 90.09, 0.51515, 27, -18.18, -89.14, 0.43332, 28, -14.21, -152.06, 0.00019, 33, -92.95, -9.2, 0.05134, 3, 29, 7.58, 137.63, 0.52548, 27, 16.19, -125.49, 0.47063, 33, -137.4, -32.15, 0.00389, 3, 29, -87.04, 55.19, 0.01348, 27, -11.16, -3.02, 0.81199, 33, -12.14, -39.81, 0.17453, 3, 29, -112.14, 101.43, 0.00177, 27, 41.46, -3, 0.99439, 28, -40.01, -50.51, 0.00384, 2, 27, 97.33, -0.76, 0.83877, 28, -5.01, -6.9, 0.16123, 2, 27, 139.6, -40.11, 0.00122, 28, 52.43, -0.87, 0.99878, 1, 32, 27.08, 14.23, 1, 1, 32, 62.71, 4.66, 1, 5, 29, -18.68, 4.48, 0.7253, 27, -88.33, -38.92, 0.01491, 33, -25.26, 44.29, 0.25302, 34, -72.75, 73.18, 0.00664, 35, -186.15, 194.37, 0.00012, 4, 29, 26.45, 10.16, 0.98132, 30, -60.79, 82.74, 0.00169, 27, -104.86, -81.3, 0.01618, 33, -61.4, 71.92, 0.00081, 3, 29, 80.35, 9.1, 0.97932, 30, -25.52, 41.97, 0.01819, 27, -131.49, -128.17, 0.00249, 2, 29, 122.67, 0.67, 0.75465, 30, -3.48, 4.87, 0.24535, 2, 30, 46.21, -1.45, 0.99508, 31, -20.19, -42.17, 0.00492, 2, 30, 91.22, -17.17, 0.21032, 31, 14.46, -9.42, 0.78968, 2, 30, 112.92, -64.58, 0.02605, 31, 66.54, -11.94, 0.97395, 2, 30, 130.03, -110.13, 0.00525, 31, 114.86, -17.66, 0.99475, 4, 33, 208.76, 155.38, 9e-05, 34, 185.04, 98.64, 0.8436, 35, 37.9, 64.36, 0.12363, 36, -41.72, 68.86, 0.03268, 4, 33, 162.07, 129.44, 0.00044, 34, 132.34, 90.01, 0.9621, 35, -9.9, 88.15, 0.03057, 36, -86.92, 97.3, 0.0069, 4, 33, 124.76, 108.94, 0.01252, 34, 90.29, 83.32, 0.91882, 35, -47.93, 107.3, 0.0582, 36, -122.84, 120.15, 0.01046, 4, 33, 83.4, 90.04, 0.18999, 34, 44.98, 79.52, 0.79198, 35, -86.92, 130.7, 0.00506, 36, -159.31, 147.31, 0.01297, 4, 29, -44.23, -78.14, 0.05111, 33, 50.92, 85.22, 0.3944, 34, 12.77, 85.96, 0.54873, 36, -179.16, 173.48, 0.00576, 4, 33, 238.44, 118.74, 2e-05, 34, 200.59, 54.13, 0.95316, 35, 24.5, 19.15, 0.04319, 36, -59.56, 25.21, 0.00364, 2, 34, 181.55, 4.41, 0.88275, 35, -20, -10.07, 0.11725, 3, 34, 201.18, -27.94, 0.8921, 35, -22.99, -47.79, 0.10732, 36, -113.49, -36.66, 0.00059], "hull": 66}}, "shuren05": {"shuren05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [35.05, 87.45, 173.36, -7.91, 103.54, -109.18, -34.77, -13.81], "hull": 4}}, "shuren06": {"shuren06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-24.73, -9.72, 17.32, 95.16, 202.96, 20.75, 160.91, -84.14], "hull": 4}}, "shuren07": {"shuren07": {"type": "mesh", "uvs": [0.9494, 0.99431, 1, 0.92824, 1, 0.79453, 0.91055, 0.72059, 0.78645, 0.697, 0.7012, 0.74576, 0.61594, 0.697, 0.61702, 0.60419, 0.69148, 0.51295, 0.68393, 0.35564, 0.64724, 0.19519, 0.50587, 0.37609, 0.4825, 0.48495, 0.46162, 0.58216, 0.52529, 0.63565, 0.52745, 0.71115, 0.48105, 0.72689, 0.42062, 0.60261, 0.31918, 0.52239, 0.32889, 0.46575, 0.38177, 0.44688, 0.40443, 0.33676, 0.31654, 0.32938, 0.27161, 0.39636, 0.26447, 0.46482, 0.23485, 0.41571, 0.22669, 0.26537, 0.25936, 0.18202, 0.32471, 0.16564, 0.35943, 0, 0.24915, 0.00043, 0.20728, 0.1195, 0.12866, 0.07485, 0.04697, 0, 0.01634, 0.16267, 0.03165, 0.32193, 0.14398, 0.35319, 0.1644, 0.43654, 0.1256, 0.45887, 0.06229, 0.43803, 0, 0.38445, 0.00715, 0.51841, 0.06433, 0.64642, 0.17155, 0.6613, 0.20626, 0.69554, 0.27876, 0.65386, 0.36658, 0.74614, 0.2757, 0.78187, 0.15623, 0.73275, 0.12458, 0.80866, 0.24507, 0.85778, 0.33901, 0.96941, 0.42274, 0.94857, 0.4595, 0.8935, 0.56263, 0.88606, 0.63513, 0.90987, 0.62185, 0.98876, 0.72239, 1, 0.82291, 0.92468, 0.88457, 0.96181, 0.90468, 0.84262, 0.79879, 0.82699, 0.69156, 0.82308, 0.75054, 0.9071, 0.59103, 0.80159, 0.46504, 0.79768, 0.4141, 0.71758, 0.34306, 0.61988, 0.2546, 0.54368, 0.20098, 0.41668, 0.19562, 0.29164, 0.1983, 0.17832, 0.166, 0.20429, 0.07898, 0.16898, 0.14895, 0.26967, 0.27454, 0.07744, 0.22341, 0.16898, 0.32568, 0.39783, 0.28441, 0.49198, 0.04578, 0.49983, 0.14536, 0.56129, 0.23148, 0.5796, 0.20726, 0.8006, 0.28531, 0.84113, 0.3813, 0.85552, 0.43243, 0.82021, 0.6271, 0.36644, 0.59122, 0.48675, 0.57238, 0.62536, 0.57328, 0.7339], "triangles": [1, 0, 60, 75, 30, 29, 31, 30, 75, 28, 75, 29, 76, 31, 75, 27, 76, 75, 28, 27, 75, 26, 76, 27, 73, 34, 33, 32, 73, 33, 72, 73, 32, 74, 73, 72, 70, 74, 72, 35, 34, 73, 35, 73, 74, 36, 35, 74, 36, 74, 70, 79, 40, 39, 41, 40, 79, 80, 38, 37, 79, 39, 38, 80, 79, 38, 80, 37, 81, 42, 79, 80, 41, 79, 42, 43, 80, 81, 44, 43, 81, 42, 80, 43, 77, 22, 21, 23, 22, 77, 20, 77, 21, 19, 77, 20, 78, 24, 77, 23, 77, 24, 77, 19, 78, 18, 78, 19, 31, 72, 32, 71, 31, 76, 31, 71, 72, 71, 76, 26, 70, 72, 71, 71, 26, 70, 25, 70, 26, 69, 36, 70, 25, 69, 70, 37, 36, 69, 24, 69, 25, 68, 24, 78, 68, 69, 24, 68, 37, 69, 81, 37, 68, 9, 86, 10, 86, 11, 10, 87, 11, 86, 12, 11, 87, 86, 9, 8, 87, 86, 8, 7, 87, 8, 87, 14, 12, 88, 87, 7, 14, 13, 12, 87, 88, 14, 6, 88, 7, 15, 14, 88, 89, 15, 88, 6, 89, 88, 64, 89, 6, 15, 89, 64, 82, 48, 47, 49, 48, 82, 83, 47, 46, 82, 47, 83, 84, 46, 85, 83, 46, 84, 50, 82, 83, 49, 82, 50, 53, 52, 84, 53, 84, 85, 51, 83, 84, 51, 84, 52, 50, 83, 51, 67, 18, 17, 68, 78, 18, 67, 68, 18, 45, 68, 67, 81, 68, 45, 45, 44, 81, 66, 67, 17, 66, 17, 16, 46, 67, 66, 45, 67, 46, 65, 66, 16, 65, 16, 15, 64, 65, 15, 85, 66, 65, 46, 66, 85, 53, 85, 65, 53, 65, 54, 64, 6, 5, 62, 64, 5, 61, 4, 3, 5, 4, 61, 62, 5, 61, 60, 61, 3, 60, 3, 2, 54, 65, 64, 63, 62, 61, 55, 64, 62, 55, 62, 63, 54, 64, 55, 58, 61, 60, 63, 61, 58, 60, 2, 1, 59, 58, 60, 0, 59, 60, 57, 55, 63, 56, 55, 57, 57, 63, 58], "vertices": [1, 35, 68.18, 113.67, 1, 2, 46, -18.19, 12.69, 0.232, 35, 70.23, 88, 0.768, 2, 46, -15.77, -21.99, 0.432, 35, 97.88, 66.94, 0.568, 2, 46, 19.39, -38.81, 0.92, 35, 133.72, 82.25, 0.08, 2, 46, 66.74, -41.66, 0.99915, 49, 11.89, -75.13, 0.00085, 2, 46, 98.09, -26.76, 0.92266, 49, -6.35, -45.6, 0.07734, 2, 46, 131.2, -37.16, 0.19021, 49, 0.37, -11.55, 0.80979, 1, 49, 24.18, -7.65, 1, 1, 49, 52.56, -31.18, 1, 1, 49, 92.29, -21.07, 1, 1, 49, 130.86, 0.05, 1, 1, 49, 75.03, 44.38, 1, 1, 49, 45.6, 48.05, 1, 2, 47, 60.43, -36.68, 0.001, 49, 19.32, 51.33, 0.999, 2, 47, 32.71, -39.39, 0.05329, 49, 9.94, 25.1, 0.94671, 3, 46, 164.4, -31.15, 0.05881, 47, 20.64, -23.89, 0.42099, 49, -9.23, 20.8, 0.5202, 2, 47, 32.57, -10.34, 0.96041, 49, -16.39, 37.37, 0.03959, 1, 47, 69.99, -23.32, 1, 3, 47, 113.4, -17.94, 0.58429, 48, 0.79, -22.61, 0.05596, 53, -4.5, -16.8, 0.35975, 2, 47, 118.96, -32.07, 0.05921, 53, 8.51, -8.99, 0.94079, 1, 53, 26.15, -19.69, 1, 1, 53, 52.47, -5.52, 1, 1, 53, 30.27, 19.39, 1, 2, 48, 37.1, -13.64, 0.05033, 53, 5.92, 19.12, 0.94967, 2, 48, 20.6, -6.43, 0.62865, 53, -8.58, 8.45, 0.37135, 2, 48, 35.83, 1.12, 0.99957, 51, -6.34, -45.78, 0.00043, 2, 48, 74.39, -5.96, 0.96616, 54, -21.93, -22.03, 0.03384, 2, 48, 92.14, -23.51, 0.12922, 54, 1.94, -14.73, 0.87078, 1, 54, 22.89, -28.61, 1, 2, 52, 22.4, -105.52, 0, 54, 61.99, -6.27, 1, 2, 52, 44.05, -69.77, 0.00033, 54, 31.48, 22.3, 0.99967, 2, 48, 112.93, -8.63, 0.4976, 54, -1.3, 10.63, 0.5024, 3, 48, 131.83, 17.18, 0.30286, 52, 51.28, -20.71, 0.67604, 54, -15.04, 39.51, 0.0211, 1, 52, 84.01, -4.39, 1, 1, 52, 53.93, 27.53, 1, 3, 48, 79.23, 69.25, 1e-05, 51, 74.39, -42.89, 0, 52, 15.55, 44.11, 0.99999, 3, 48, 60.41, 30.21, 0.20552, 51, 31.57, -49.53, 0.02237, 52, -13.54, 11.99, 0.7721, 3, 48, 37.47, 28.32, 0.54894, 51, 17, -31.71, 0.35443, 52, -36.07, 16.65, 0.09663, 3, 48, 35.65, 44.02, 0.16707, 51, 28.9, -21.3, 0.81663, 52, -33.38, 32.23, 0.0163, 3, 48, 47.07, 65.81, 0.00424, 51, 53.32, -18.35, 0.99556, 52, -16.27, 49.9, 0.00019, 2, 48, 66.62, 85.03, 0, 51, 80.24, -23.55, 1, 1, 51, 65.99, 8.35, 1, 1, 51, 34.4, 32.42, 1, 2, 47, 137.93, 43.97, 0.1493, 51, -5.17, 22.43, 0.8507, 2, 47, 122.05, 43.56, 0.35176, 51, -20.56, 26.39, 0.64824, 2, 47, 105.99, 18.77, 0.8554, 51, -42.8, 6.96, 0.1446, 2, 47, 64.96, 18.95, 0.61244, 50, 20.14, -26.76, 0.38756, 2, 47, 87.59, 46.53, 0.00589, 50, 53.43, -13.93, 0.99411, 1, 50, 99.79, -21.91, 1, 1, 50, 109.66, -1.03, 1, 1, 50, 62.91, 6.91, 1, 1, 50, 24.48, 32.06, 1, 2, 47, 17.05, 49.41, 0.0201, 50, -6.52, 23.37, 0.9799, 3, 46, 186.8, 17.93, 0.00361, 47, 14.04, 29.66, 0.37059, 50, -18.88, 7.67, 0.6258, 3, 46, 147.94, 13.28, 0.94013, 47, -16.64, 5.37, 0.05342, 50, -57.55, 1.67, 0.00645, 1, 46, 120.1, 17.55, 1, 1, 46, 123.69, 38.36, 1, 1, 46, 85.48, 38.62, 1, 1, 46, 48.83, 16.44, 1, 1, 46, 24.85, 24.44, 1, 1, 46, 19.4, -7.01, 1, 1, 46, 59.72, -8.27, 1, 2, 46, 100.33, -6.45, 0.98089, 49, -26.78, -45.59, 0.01911, 1, 46, 76.51, 13.78, 1, 3, 46, 138.73, -9.37, 0.83837, 47, -12.64, -18.76, 0.00839, 49, -28.08, -7.1, 0.15325, 2, 47, 26.81, 8.17, 0.83949, 50, -18.37, -17.32, 0.16051, 2, 47, 54.62, 2.44, 0.97012, 50, 3, -36.02, 0.02988, 2, 47, 91.3, -2.58, 0.99694, 53, -16.03, -41.12, 0.00306, 3, 47, 130.09, 0.79, 0.3479, 48, 1.75, 2.46, 0.61683, 51, -24.56, -16.95, 0.03526, 3, 48, 38.89, 13.59, 0.83502, 51, 5.67, -41.23, 0.09577, 52, -38.88, 2.12, 0.06922, 3, 48, 70.83, 7.18, 0.76092, 51, 18.5, -71.17, 0.0005, 52, -10.06, -13.05, 0.23858, 2, 48, 99.03, -1.4, 0.94287, 54, -14.25, 1.83, 0.05713, 2, 48, 95.67, 12.17, 0.60098, 52, 15.18, -15.28, 0.39902, 1, 52, 40.18, 8.11, 1, 2, 48, 80.9, 22.8, 0.02186, 52, 4.02, -0.92, 0.97814, 2, 52, 21.94, -67.57, 2e-05, 54, 24.76, 1.12, 0.99998, 2, 48, 98.93, -11.22, 0.32669, 54, -5.66, -2.93, 0.67331, 1, 53, 20.14, 4.36, 1, 3, 47, 128.71, -16.72, 0.10726, 48, 11.83, -11.92, 0.36068, 53, -8.23, -1.89, 0.53206, 3, 48, 33.16, 75.99, 0.00016, 51, 53.82, -1.12, 0.99984, 52, -26.74, 63.6, 0, 2, 47, 161.12, 28.58, 0.00012, 51, 12.91, 1.26, 0.99988, 3, 47, 131.8, 13.48, 0.36137, 48, -5.02, 13.34, 0.2663, 51, -19.44, -5.21, 0.37233, 1, 50, 78.71, -6.38, 1, 1, 50, 48.2, 1.02, 1, 1, 50, 11.63, 0.94, 1, 2, 47, 33.46, 20.12, 0.42188, 50, -6.69, -10.21, 0.57812, 1, 49, 85.69, -0.38, 1, 1, 49, 52.48, 7.42, 1, 2, 47, 19.75, -51.94, 0.0123, 49, 15.75, 8.02, 0.9877, 3, 46, 146.67, -26.46, 0.23716, 47, 3.07, -29.17, 0.1284, 49, -11.96, 2.65, 0.63443], "hull": 60}}, "shuren08": {"shuren08": {"type": "mesh", "uvs": [0.852, 0, 0.85227, 0.01977, 0.85111, 0.10994, 0.84138, 0.18276, 0.83164, 0.25558, 0.81406, 0.31716, 0.79923, 0.35517, 0.77354, 0.39772, 0.82502, 0.44832, 0.86615, 0.50038, 0.88292, 0.52051, 0.92121, 0.546, 0.95598, 0.55067, 0.99279, 0.5498, 0.99895, 0.55026, 1, 0.56017, 0.9934, 0.57819, 0.93054, 0.63341, 0.90911, 0.63329, 0.86031, 0.61763, 0.8105, 0.5667, 0.76364, 0.54614, 0.71678, 0.52557, 0.73806, 0.55044, 0.75434, 0.56946, 0.7919, 0.61334, 0.82946, 0.65722, 0.85096, 0.68056, 0.8701, 0.70134, 0.8566, 0.75352, 0.8881, 0.82525, 0.89133, 0.83827, 0.8849, 0.84473, 0.83048, 0.80827, 0.79725, 0.776, 0.77841, 0.77439, 0.74529, 0.7892, 0.72907, 0.78901, 0.70563, 0.8054, 0.6765, 0.84488, 0.66387, 0.86502, 0.65204, 0.89044, 0.62381, 0.93141, 0.61608, 0.96642, 0.60168, 0.97835, 0.60007, 1, 0.56665, 1, 0.5533, 0.97282, 0.53963, 0.97616, 0.49175, 0.96639, 0.45695, 0.92217, 0.44199, 0.89361, 0.39677, 0.82787, 0.38148, 0.81979, 0.3687, 0.81986, 0.3388, 0.88216, 0.31839, 0.91791, 0.30111, 0.92569, 0.26782, 0.91778, 0.26265, 0.93248, 0.24992, 0.95113, 0.23368, 0.96006, 0.21315, 0.94643, 0.1764, 0.88199, 0.16833, 0.88143, 0.15186, 0.92994, 0.1226, 0.9686, 0.11474, 0.97921, 0.08581, 0.95911, 0.04487, 0.89845, 0.02769, 0.86732, 0, 0.84383, 0.01517, 0.77132, 0.02827, 0.70795, 0.04076, 0.65794, 0.05325, 0.60793, 0.06534, 0.52697, 0.06378, 0.45412, 0.07366, 0.46504, 0.09885, 0.47392, 0.14115, 0.45491, 0.17315, 0.46099, 0.19829, 0.44375, 0.21363, 0.4342, 0.24131, 0.43415, 0.26107, 0.41251, 0.27057, 0.3853, 0.27969, 0.33643, 0.27978, 0.32593, 0.27324, 0.27594, 0.27482, 0.25684, 0.29485, 0.26928, 0.30807, 0.30459, 0.30456, 0.34197, 0.30834, 0.39137, 0.32618, 0.37968, 0.32942, 0.39054, 0.32384, 0.41242, 0.30911, 0.43221, 0.35568, 0.4496, 0.40226, 0.46698, 0.44121, 0.45716, 0.4693, 0.41042, 0.49152, 0.32546, 0.49917, 0.27683, 0.50744, 0.21516, 0.51129, 0.18461, 0.52408, 0.188, 0.52411, 0.23237, 0.53395, 0.25763, 0.55148, 0.31916, 0.58734, 0.2797, 0.61009, 0.2605, 0.63057, 0.25537, 0.63729, 0.28123, 0.66409, 0.28127, 0.69137, 0.29168, 0.70587, 0.29084, 0.73972, 0.26574, 0.77199, 0.21981, 0.78161, 0.18283, 0.81748, 0.11029, 0.83281, 0.0354, 0.84207, 0.0071, 0.63278, 0.63905, 0.10969, 0.68507, 0.49856, 0.70863, 0.30222, 0.66435, 0.54294, 0.40881, 0.1839, 0.65727, 0.58643, 0.88818, 0.40756, 0.67463, 0.5546, 0.57486, 0.59753, 0.39376, 0.22463, 0.67226, 0.66368, 0.38625, 0.60348, 0.80401, 0.69167, 0.71995, 0.35797, 0.61159, 0.45984, 0.53653, 0.25501, 0.87701, 0.54857, 0.85781, 0.44453, 0.76138, 0.74179, 0.70122, 0.24184, 0.51882, 0.64981, 0.52903, 0.59567, 0.50918, 0.20364, 0.81931, 0.14783, 0.53264, 0.13535, 0.83732, 0.28677, 0.83671, 0.05933, 0.80093, 0.4998, 0.5067, 0.57074, 0.74542, 0.27939, 0.57878, 0.25864, 0.74118, 0.3302, 0.55073, 0.72297, 0.37446, 0.76978, 0.46268, 0.48894, 0.86939, 0.68657, 0.62296, 0.59145, 0.65451, 0.34907, 0.75843, 0.16288, 0.79367, 0.65332, 0.79373, 0.54395, 0.67465, 0.46206, 0.63758, 0.09246, 0.8577, 0.19816, 0.53186, 0.1413, 0.62844, 0.41964, 0.55106, 0.30898, 0.7999, 0.67949, 0.47803, 0.53012, 0.77125, 0.06397, 0.71958, 0.63723, 0.42963, 0.82284, 0.50836, 0.86429, 0.55606, 0.91736, 0.57992, 0.89737, 0.56878, 0.96714, 0.57117, 0.99249, 0.56322, 0.81827, 0.73178, 0.77802, 0.70793, 0.37322, 0.54414, 0.37793, 0.71913, 0.09441, 0.60403, 0.84495, 0.04699, 0.83098, 0.12858, 0.80302, 0.25166, 0.76715, 0.29668, 0.74103, 0.33536], "triangles": [16, 180, 181, 16, 17, 180, 18, 178, 17, 17, 178, 180, 18, 19, 179, 179, 19, 177, 177, 10, 179, 18, 179, 178, 19, 20, 177, 177, 20, 176, 178, 12, 180, 178, 11, 12, 178, 179, 11, 16, 181, 15, 14, 181, 13, 181, 14, 15, 181, 180, 13, 179, 10, 11, 176, 20, 158, 13, 180, 12, 177, 9, 10, 177, 176, 9, 20, 21, 158, 176, 8, 9, 176, 158, 8, 158, 7, 8, 41, 164, 40, 41, 136, 164, 40, 164, 39, 38, 39, 137, 33, 29, 32, 32, 30, 31, 32, 29, 30, 34, 182, 33, 33, 182, 29, 39, 164, 137, 38, 137, 37, 136, 124, 164, 137, 124, 160, 137, 164, 124, 37, 143, 36, 36, 183, 35, 36, 143, 183, 37, 137, 143, 182, 34, 183, 34, 35, 183, 182, 27, 29, 29, 27, 28, 182, 26, 27, 26, 182, 25, 143, 160, 23, 23, 160, 22, 160, 143, 137, 182, 183, 25, 25, 183, 24, 183, 143, 24, 143, 23, 24, 124, 146, 145, 124, 145, 160, 146, 175, 145, 145, 172, 160, 160, 172, 22, 21, 22, 158, 22, 172, 157, 116, 157, 135, 145, 175, 172, 22, 157, 158, 157, 116, 117, 175, 133, 114, 114, 133, 112, 175, 135, 172, 157, 172, 135, 158, 157, 7, 7, 157, 191, 175, 114, 135, 114, 112, 113, 191, 190, 7, 7, 190, 6, 135, 115, 116, 135, 114, 115, 157, 117, 191, 5, 6, 189, 117, 118, 191, 191, 118, 190, 6, 190, 189, 5, 189, 4, 190, 119, 189, 190, 118, 119, 4, 189, 3, 119, 120, 189, 189, 188, 3, 189, 121, 188, 189, 120, 121, 3, 188, 2, 188, 187, 2, 188, 122, 187, 188, 121, 122, 2, 187, 1, 122, 123, 187, 1, 187, 0, 187, 123, 0, 45, 46, 44, 44, 46, 130, 46, 47, 130, 44, 130, 43, 47, 48, 141, 141, 48, 159, 47, 141, 130, 43, 130, 42, 50, 159, 49, 48, 49, 159, 41, 42, 136, 50, 51, 159, 51, 142, 159, 51, 52, 142, 42, 130, 136, 136, 130, 153, 159, 173, 141, 159, 126, 173, 159, 142, 126, 130, 141, 153, 141, 173, 153, 153, 161, 136, 136, 161, 124, 173, 165, 153, 173, 126, 165, 142, 166, 126, 142, 131, 166, 153, 165, 161, 165, 126, 152, 126, 166, 152, 165, 132, 161, 165, 152, 132, 131, 170, 166, 161, 146, 124, 161, 132, 146, 175, 146, 133, 170, 139, 166, 166, 139, 152, 152, 128, 132, 132, 128, 146, 170, 101, 139, 170, 100, 101, 139, 102, 152, 139, 101, 102, 146, 128, 133, 102, 103, 152, 152, 103, 128, 110, 128, 103, 109, 110, 103, 104, 108, 109, 104, 105, 108, 105, 107, 108, 105, 106, 107, 128, 110, 133, 109, 103, 104, 110, 111, 133, 133, 111, 112, 60, 61, 140, 59, 60, 140, 140, 61, 62, 59, 140, 58, 56, 57, 150, 57, 58, 150, 150, 171, 56, 56, 171, 55, 58, 140, 150, 55, 162, 54, 55, 171, 162, 140, 155, 150, 140, 147, 155, 150, 155, 171, 142, 52, 131, 53, 54, 185, 54, 162, 185, 131, 52, 185, 52, 53, 185, 155, 127, 171, 171, 127, 162, 162, 138, 185, 162, 127, 138, 155, 154, 127, 155, 134, 154, 185, 138, 131, 138, 184, 131, 131, 184, 170, 134, 144, 154, 144, 83, 84, 127, 156, 138, 127, 154, 156, 138, 156, 184, 144, 85, 154, 154, 85, 98, 154, 98, 156, 98, 86, 94, 93, 94, 87, 87, 94, 86, 98, 85, 86, 184, 100, 170, 156, 99, 184, 156, 98, 99, 184, 99, 100, 144, 84, 85, 98, 94, 97, 97, 95, 96, 97, 94, 95, 87, 88, 93, 93, 88, 92, 88, 91, 92, 88, 89, 91, 89, 90, 91, 66, 67, 167, 67, 68, 167, 66, 149, 65, 66, 167, 149, 68, 69, 167, 63, 147, 62, 62, 147, 140, 65, 149, 64, 69, 151, 167, 69, 70, 151, 147, 63, 163, 149, 163, 64, 63, 64, 163, 71, 72, 70, 70, 72, 151, 151, 174, 167, 167, 125, 149, 167, 174, 125, 149, 125, 163, 163, 129, 147, 147, 134, 155, 147, 129, 134, 72, 73, 151, 151, 73, 174, 125, 169, 163, 163, 169, 129, 73, 74, 174, 74, 75, 174, 174, 186, 125, 174, 75, 186, 125, 186, 169, 129, 168, 134, 134, 168, 144, 83, 144, 168, 82, 83, 168, 169, 148, 129, 129, 148, 168, 169, 186, 148, 148, 186, 79, 75, 76, 186, 79, 80, 148, 186, 76, 79, 79, 76, 78, 148, 81, 168, 148, 80, 81, 168, 81, 82, 76, 77, 78], "vertices": [2, 6, 217.91, 190.16, 0.66978, 7, 51.66, 190.16, 0.33022, 2, 6, 217.84, 183.7, 0.66977, 7, 51.59, 183.7, 0.33023, 2, 6, 215.46, 154.29, 0.66966, 7, 49.21, 154.29, 0.33034, 2, 6, 205.87, 130.9, 0.66919, 7, 39.62, 130.9, 0.33081, 2, 6, 196.27, 107.51, 0.6674, 7, 30.03, 107.51, 0.3326, 2, 6, 180.03, 88.11, 0.66277, 7, 13.78, 88.11, 0.33723, 2, 6, 166.53, 76.29, 0.63762, 7, 0.28, 76.29, 0.36238, 2, 6, 143.51, 63.44, 0.46795, 7, -22.74, 63.44, 0.53205, 2, 6, 187.57, 44.82, 0.25233, 7, 21.32, 44.82, 0.74767, 2, 6, 222.61, 26.14, 0.0547, 7, 56.36, 26.14, 0.9453, 2, 6, 236.91, 18.89, 0.00291, 7, 70.66, 18.89, 0.99709, 1, 7, 103.62, 9, 1, 1, 7, 133.84, 6.07, 1, 1, 7, 165.92, 4.85, 1, 1, 7, 171.28, 4.45, 1, 1, 7, 172.04, 1.18, 1, 1, 7, 166.02, -4.44, 1, 1, 7, 110.42, -19.93, 1, 1, 7, 91.75, -19.02, 1, 2, 6, 215.74, -11.92, 0.04752, 7, 49.49, -11.92, 0.95248, 2, 6, 173.12, 6.74, 0.21267, 7, 6.87, 6.74, 0.78733, 3, 5, 298.87, 15.36, 4e-05, 6, 132.62, 15.36, 0.50873, 7, -33.63, 15.36, 0.49123, 3, 5, 258.36, 23.98, 4e-05, 6, 92.12, 23.98, 0.79135, 7, -74.13, 23.98, 0.20861, 3, 5, 276.52, 15, 4e-05, 6, 110.27, 15, 0.95937, 7, -55.97, 15, 0.04059, 2, 6, 124.17, 8.12, 0.99663, 7, -42.08, 8.12, 0.00337, 2, 6, 156.21, -7.74, 0.99982, 7, -10.03, -7.74, 0.00018, 2, 6, 188.26, -23.6, 1, 7, 22.02, -23.6, 0, 2, 6, 206.63, -32.09, 1, 7, 40.38, -32.09, 0, 2, 6, 222.99, -39.66, 1, 7, 56.74, -39.66, 0, 2, 6, 210.43, -56.16, 1, 7, 44.18, -56.16, 0, 2, 6, 236.77, -80.87, 1, 7, 70.53, -80.87, 0, 2, 6, 239.39, -85.25, 1, 7, 73.14, -85.25, 0, 2, 6, 233.69, -87.1, 1, 7, 67.45, -87.1, 0, 2, 6, 186.85, -72.98, 1, 7, 20.6, -72.98, 0, 2, 6, 158.39, -61.09, 1, 7, -7.86, -61.09, 0, 3, 5, 308.25, -59.8, 0.00019, 6, 142.01, -59.8, 0.99981, 7, -24.24, -59.8, 0, 3, 5, 279.18, -63.29, 0.00231, 6, 112.93, -63.29, 0.99769, 7, -53.31, -63.29, 0, 3, 5, 265.06, -62.57, 0.01644, 6, 98.81, -62.57, 0.98356, 7, -67.44, -62.57, 0, 3, 5, 244.39, -66.97, 0.07775, 6, 78.14, -66.97, 0.92225, 7, -88.1, -66.97, 0, 3, 5, 218.41, -78.68, 0.17136, 6, 52.16, -78.68, 0.82864, 7, -114.09, -78.68, 0, 2, 5, 207.1, -84.75, 0.28947, 6, 40.86, -84.75, 0.71053, 2, 5, 196.41, -92.57, 0.4384, 6, 30.16, -92.57, 0.5616, 2, 5, 171.2, -104.8, 0.57651, 6, 4.95, -104.8, 0.42349, 2, 5, 163.93, -115.93, 0.69689, 6, -2.31, -115.93, 0.30311, 2, 5, 151.2, -119.24, 0.74355, 6, -15.04, -119.24, 0.25645, 2, 5, 149.47, -126.25, 0.791, 6, -16.77, -126.25, 0.209, 3, 4, 286.61, -124.89, 4e-05, 5, 120.36, -124.89, 0.83369, 6, -45.88, -124.89, 0.16627, 3, 4, 275.39, -115.47, 0.00059, 5, 109.15, -115.47, 0.88442, 6, -57.1, -115.47, 0.11499, 3, 4, 263.44, -116, 0.01112, 5, 97.19, -116, 0.92205, 6, -69.05, -116, 0.06683, 3, 4, 221.88, -110.87, 0.05098, 5, 55.63, -110.87, 0.91999, 6, -110.61, -110.87, 0.02903, 3, 4, 192.24, -95.01, 0.11984, 5, 25.99, -95.01, 0.87608, 6, -140.25, -95.01, 0.00408, 3, 4, 179.65, -85.07, 0.34634, 5, 13.4, -85.07, 0.65347, 6, -152.84, -85.07, 0.00019, 2, 4, 141.26, -61.76, 0.59647, 5, -24.99, -61.76, 0.40353, 3, 3, 294.31, -58.5, 0, 4, 128.06, -58.5, 0.84327, 5, -38.19, -58.5, 0.15673, 3, 3, 283.17, -58.01, 0.00592, 4, 116.93, -58.01, 0.93359, 5, -49.32, -58.01, 0.06049, 3, 3, 256.18, -77.14, 0.0228, 4, 89.94, -77.14, 0.96, 5, -76.31, -77.14, 0.0172, 3, 3, 237.86, -87.99, 0.05237, 4, 71.61, -87.99, 0.94756, 5, -94.64, -87.99, 8e-05, 2, 3, 222.68, -89.83, 0.13573, 4, 56.44, -89.83, 0.86427, 2, 3, 193.82, -85.89, 0.22801, 4, 27.57, -85.89, 0.77199, 2, 3, 189.08, -90.48, 0.33409, 4, 22.83, -90.48, 0.66591, 2, 3, 177.71, -96.06, 0.40617, 4, 11.47, -96.06, 0.59383, 2, 3, 163.43, -98.31, 0.49243, 4, -2.82, -98.31, 0.50757, 2, 3, 145.75, -93.03, 0.64802, 4, -20.49, -93.03, 0.35198, 2, 3, 114.72, -70.49, 0.79669, 4, -51.52, -70.49, 0.20331, 2, 3, 107.71, -69.98, 0.92957, 4, -58.54, -69.98, 0.07043, 2, 3, 92.62, -85.15, 0.97151, 4, -73.63, -85.15, 0.02849, 2, 3, 66.54, -96.59, 0.99482, 4, -99.71, -96.59, 0.00518, 2, 3, 59.53, -99.74, 0.99985, 4, -106.72, -99.74, 0.00015, 1, 3, 34.64, -92, 1, 1, 3, -0.1, -70.52, 1, 1, 3, -14.59, -59.65, 1, 1, 3, -38.35, -50.85, 1, 1, 3, -24.03, -27.79, 1, 2, 3, -11.65, -7.62, 1, 7, -676.64, -7.62, 0, 2, 3, -0.01, 8.21, 1, 7, -665, 8.21, 0, 2, 3, 11.63, 24.04, 1, 7, -653.36, 24.04, 0, 2, 3, 23.39, 49.99, 1, 7, -641.59, 49.99, 0, 2, 3, 23.15, 73.85, 1, 7, -641.84, 73.85, 0, 2, 3, 31.59, 69.88, 1, 7, -633.4, 69.88, 0, 3, 3, 53.4, 65.96, 0.998, 4, -112.85, 65.96, 0.002, 7, -611.59, 65.96, 0, 3, 3, 90.53, 70.45, 0.97086, 4, -75.72, 70.45, 0.02914, 7, -574.46, 70.45, 0, 2, 3, 118.31, 67.16, 0.88534, 4, -47.94, 67.16, 0.11466, 2, 3, 140.47, 71.77, 0.75517, 4, -25.78, 71.77, 0.24483, 2, 3, 153.97, 74.27, 0.55208, 4, -12.27, 74.27, 0.44792, 2, 3, 178.09, 73.16, 0.34278, 4, 11.84, 73.16, 0.65722, 2, 3, 195.63, 79.43, 0.15876, 4, 29.39, 79.43, 0.84124, 2, 3, 204.32, 87.93, 0.05912, 4, 38.07, 87.93, 0.94088, 2, 3, 213.01, 103.52, 0.0227, 4, 46.76, 103.52, 0.9773, 2, 3, 213.25, 106.95, 0.00558, 4, 47, 106.95, 0.99442, 2, 3, 208.31, 123.54, 0.00213, 4, 42.07, 123.54, 0.99787, 2, 3, 209.98, 129.72, 2e-05, 4, 43.74, 129.72, 0.99998, 2, 3, 227.23, 124.84, 0.00022, 4, 60.99, 124.84, 0.99978, 2, 3, 238.21, 112.77, 0.0015, 4, 71.96, 112.77, 0.9985, 3, 3, 234.59, 100.7, 0.00386, 4, 68.34, 100.7, 0.99614, 7, -430.4, 100.7, 0, 3, 3, 237.12, 84.41, 0.00364, 4, 70.88, 84.41, 0.99636, 7, -427.86, 84.41, 0, 3, 3, 252.84, 87.51, 0.00236, 4, 86.6, 87.51, 0.99764, 7, -412.14, 87.51, 0, 3, 3, 255.5, 83.83, 0.00033, 4, 89.25, 83.83, 0.99967, 7, -409.49, 83.83, 0, 3, 3, 250.31, 76.91, 0.00267, 4, 84.06, 76.91, 0.99733, 7, -414.68, 76.91, 0, 4, 3, 237.17, 71.04, 0.00267, 4, 70.92, 71.04, 0.99378, 5, -95.32, 71.04, 0.00356, 7, -427.82, 71.04, 0, 4, 3, 277.48, 63.47, 0.00234, 4, 111.23, 63.47, 0.90326, 5, -55.02, 63.47, 0.0944, 7, -387.51, 63.47, 0, 3, 4, 151.54, 55.9, 0.64004, 5, -14.71, 55.9, 0.35996, 7, -347.2, 55.9, 0, 4, 4, 185.62, 57.52, 0.31754, 5, 19.37, 57.52, 0.68234, 6, -146.88, 57.52, 0.00011, 7, -313.12, 57.52, 0, 4, 4, 210.8, 71.65, 0.07505, 5, 44.55, 71.65, 0.92094, 6, -121.7, 71.65, 0.00401, 7, -287.94, 71.65, 0, 4, 4, 231.45, 98.5, 0.00727, 5, 65.2, 98.5, 0.98362, 6, -101.05, 98.5, 0.00911, 7, -267.29, 98.5, 0, 3, 5, 72.6, 114.07, 0.98756, 6, -93.65, 114.07, 0.01244, 7, -259.9, 114.07, 0, 2, 5, 80.75, 133.88, 0.9885, 6, -85.5, 133.88, 0.0115, 2, 5, 84.56, 143.7, 0.99079, 6, -81.69, 143.7, 0.00921, 2, 5, 95.66, 142.08, 0.98894, 6, -70.59, 142.08, 0.01106, 2, 5, 95, 127.58, 0.9804, 6, -71.25, 127.58, 0.0196, 3, 5, 103.18, 118.93, 0.93846, 6, -63.06, 118.93, 0.06136, 7, -229.31, 118.93, 0.00018, 3, 5, 117.52, 98.12, 0.80535, 6, -48.73, 98.12, 0.19182, 7, -214.98, 98.12, 0.00283, 3, 5, 149.36, 109.55, 0.63346, 6, -16.89, 109.55, 0.35803, 7, -183.14, 109.55, 0.00851, 3, 5, 169.47, 114.9, 0.46814, 6, 3.22, 114.9, 0.51486, 7, -163.02, 114.9, 0.017, 3, 5, 187.38, 115.74, 0.379, 6, 21.14, 115.74, 0.59514, 7, -145.11, 115.74, 0.02586, 3, 5, 192.84, 107.02, 0.28347, 6, 26.6, 107.02, 0.67089, 7, -139.65, 107.02, 0.04564, 3, 5, 216.19, 105.92, 0.1821, 6, 49.94, 105.92, 0.73397, 7, -116.3, 105.92, 0.08394, 3, 5, 239.79, 101.41, 0.08714, 6, 73.54, 101.41, 0.77763, 7, -92.7, 101.41, 0.13523, 3, 5, 252.43, 101.1, 0.03383, 6, 86.19, 101.1, 0.76148, 7, -80.06, 101.1, 0.20469, 3, 5, 282.3, 107.92, 0.01195, 6, 116.06, 107.92, 0.72212, 7, -50.19, 107.92, 0.26593, 3, 5, 311.11, 121.61, 0.00112, 6, 144.86, 121.61, 0.68636, 7, -21.39, 121.61, 0.31252, 2, 6, 153.81, 133.3, 0.67233, 7, -12.44, 133.3, 0.32767, 2, 6, 186.15, 155.54, 0.67047, 7, 19.91, 155.54, 0.32953, 2, 6, 200.65, 179.38, 0.66979, 7, 34.4, 179.38, 0.33021, 2, 6, 209.14, 188.25, 0.66978, 7, 42.9, 188.25, 0.33022, 2, 5, 183.46, -9.67, 0.03549, 6, 17.22, -9.67, 0.96451, 1, 3, 59.62, -3.45, 1, 2, 4, 231.74, -26.95, 0.00242, 5, 65.49, -26.95, 0.99758, 1, 4, 61.39, -4.51, 1, 2, 5, 108.72, 69.18, 0.91724, 6, -57.53, 69.18, 0.08276, 1, 3, 124.68, 2.61, 1, 2, 5, 139.3, -89.17, 0.77942, 6, -26.95, -89.17, 0.22058, 2, 4, 152.99, -12.15, 0.89332, 5, -13.26, -12.15, 0.10668, 2, 5, 116.34, 14.47, 0.99584, 6, -49.91, 14.47, 0.00416, 3, 5, 156.5, 71.88, 0.5816, 6, -9.75, 71.88, 0.41162, 7, -176, 71.88, 0.00678, 3, 3, 159.93, -3.94, 0.8113, 4, -6.32, -3.94, 0.1887, 7, -505.06, -3.94, 0, 3, 5, 214.23, 71.65, 0.12956, 6, 47.98, 71.65, 0.80336, 7, -118.26, 71.65, 0.06708, 2, 5, 155.43, -62.37, 0.65973, 6, -10.81, -62.37, 0.34027, 2, 5, 233.53, -38.49, 0.02512, 6, 67.29, -38.49, 0.97488, 2, 4, 110.75, 10.46, 1, 7, -387.99, 10.46, 0, 3, 4, 200.64, 30.84, 0.05006, 5, 34.39, 30.84, 0.94994, 7, -298.1, 30.84, 0, 2, 3, 183.27, -72.05, 0.3315, 4, 17.03, -72.05, 0.6685, 3, 4, 273.03, -77.71, 0.0001, 5, 106.78, -77.71, 0.92387, 6, -59.47, -77.71, 0.07603, 2, 4, 183.87, -41.98, 0.23443, 5, 17.63, -41.98, 0.76557, 2, 6, 111.22, -34.41, 1, 7, -55.02, -34.41, 0, 2, 3, 177.26, 45.48, 0.31468, 4, 11.02, 45.48, 0.68532, 3, 5, 199.97, 25.57, 0.04552, 6, 33.73, 25.57, 0.94303, 7, -132.52, 25.57, 0.01145, 3, 5, 153.11, 34.26, 0.70829, 6, -13.13, 34.26, 0.29041, 7, -179.38, 34.26, 0.0013, 2, 3, 139.41, -51.12, 0.76716, 4, -26.84, -51.12, 0.23284, 2, 3, 95.16, 44.79, 0.99471, 4, -71.08, 44.79, 0.00529, 2, 3, 79.65, -54.23, 0.99299, 4, -86.6, -54.23, 0.00701, 2, 3, 211.55, -60.18, 0.11461, 4, 45.31, -60.18, 0.88539, 1, 3, 13.99, -39.25, 1, 4, 4, 235.9, 38.96, 0.0004, 5, 69.65, 38.96, 0.99875, 6, -96.6, 38.96, 0.00085, 7, -262.84, 38.96, 0, 2, 5, 127.8, -41.9, 0.91133, 6, -38.45, -41.9, 0.08867, 3, 3, 209.05, 24.37, 0.01174, 4, 42.8, 24.37, 0.98826, 7, -455.94, 24.37, 0, 2, 3, 188.51, -27.83, 0.15436, 4, 22.26, -27.83, 0.84564, 3, 4, 87.5, 31.47, 0.98794, 5, -78.75, 31.47, 0.01206, 7, -411.24, 31.47, 0, 3, 5, 266.06, 73.09, 0.0099, 6, 99.81, 73.09, 0.75745, 7, -66.43, 73.09, 0.23265, 2, 6, 139.24, 42.37, 0.51083, 7, -27.01, 42.37, 0.48917, 3, 4, 220.91, -79.07, 0.04615, 5, 54.66, -79.07, 0.94886, 6, -111.59, -79.07, 0.00499, 2, 5, 230.56, -6.6, 0.00019, 6, 64.31, -6.6, 0.99981, 2, 5, 147.23, -13.04, 0.93085, 6, -19.02, -13.04, 0.06915, 3, 3, 267.01, -37.14, 6e-05, 4, 100.77, -37.14, 0.98092, 5, -65.48, -37.14, 0.01902, 2, 3, 104.29, -41.09, 0.96882, 4, -61.95, -41.09, 0.03118, 2, 5, 199, -61.03, 0.24782, 6, 32.75, -61.03, 0.75218, 2, 5, 105.55, -17.69, 0.99803, 6, -60.7, -17.69, 0.00197, 1, 5, 34.78, -2.26, 1, 1, 3, 41.98, -59.14, 1, 2, 3, 139.02, 43, 0.83085, 4, -27.23, 43, 0.16915, 2, 3, 88.02, 13.76, 1, 7, -576.97, 13.76, 0, 3, 4, 165.39, 27.73, 0.53402, 5, -0.86, 27.73, 0.46598, 7, -333.35, 27.73, 0, 2, 3, 231.46, -49.06, 0.02961, 4, 65.21, -49.06, 0.97039, 3, 5, 226.6, 41.03, 0.02735, 6, 60.36, 41.03, 0.91119, 7, -105.89, 41.03, 0.06146, 3, 4, 258.28, -48.68, 0.0004, 5, 92.03, -48.68, 0.98232, 6, -74.22, -48.68, 0.01728, 1, 3, 19.27, -12.87, 1, 3, 5, 190.53, 58.55, 0.2467, 6, 24.28, 58.55, 0.72859, 7, -141.96, 58.55, 0.02471, 2, 6, 184.76, 25.3, 0.11711, 7, 18.51, 25.3, 0.88289, 2, 6, 220.14, 8.03, 0.00143, 7, 53.89, 8.03, 0.99857, 1, 7, 99.76, -1.92, 1, 1, 7, 82.52, 2.53, 1, 1, 7, 143.25, -1.08, 1, 1, 7, 165.45, 0.48, 1, 2, 6, 177.38, -47.5, 1, 7, 11.13, -47.5, 0, 2, 6, 142.68, -38.07, 1, 7, -23.56, -38.07, 0, 3, 4, 125.06, 31.87, 0.98794, 5, -41.18, 31.87, 0.01206, 7, -373.68, 31.87, 0, 3, 3, 292.75, -25.48, 6e-05, 4, 126.5, -25.48, 0.98092, 5, -39.74, -25.48, 0.01902, 2, 3, 47.54, 23.64, 1, 7, -617.45, 23.64, 0, 2, 6, 211.05, 175.1, 0.66976, 7, 44.81, 175.1, 0.33024, 2, 6, 197.63, 149.02, 0.66943, 7, 31.39, 149.02, 0.33057, 3, 5, 337.65, 109.95, 1e-05, 6, 171.4, 109.95, 0.67159, 7, 5.16, 109.95, 0.3284, 3, 5, 305.72, 96.7, 0.00115, 6, 139.47, 96.7, 0.68543, 7, -26.77, 96.7, 0.31341, 3, 5, 282.38, 85.13, 0.00173, 6, 116.14, 85.13, 0.69375, 7, -50.11, 85.13, 0.30452], "hull": 124}}, "chongji/changcici (1)4": {"chongji/changcici (1)": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [329, -61, -329, -61, -329, 61, 329, 61], "hull": 4}, "chongji/changcici (3)": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [329, -61, -329, -61, -329, 61, 329, 61], "hull": 4}, "chongji/changcici (5)": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [329, -61, -329, -61, -329, 61, 329, 61], "hull": 4}, "chongji/changcici (7)": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [329, -61, -329, -61, -329, 61, 329, 61], "hull": 4}}, "chongji/changcici (1)2": {"chongji/changcici (1)": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [329, -61, -329, -61, -329, 61, 329, 61], "hull": 4}, "chongji/changcici (3)": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [329, -61, -329, -61, -329, 61, 329, 61], "hull": 4}, "chongji/changcici (5)": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [329, -61, -329, -61, -329, 61, 329, 61], "hull": 4}, "chongji/changcici (7)": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [329, -61, -329, -61, -329, 61, 329, 61], "hull": 4}}, "renoutuowei/tuiwei_01": {"renoutuowei/tuiwei_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [90, -47, -90, -47, -90, 47, 90, 47], "hull": 4}, "renoutuowei/tuiwei_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [90, -47, -90, -47, -90, 47, 90, 47], "hull": 4}, "renoutuowei/tuiwei_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [90, -47, -90, -47, -90, 47, 90, 47], "hull": 4}}, "glow02_00": {"glow02_00": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [25, -23, -25, -23, -25, 23, 25, 23], "hull": 4}}, "qxbd_03/baodian03_00": {"qxbd_03/baodian03_00": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [95, -74, -95, -74, -95, 75, 95, 75], "hull": 4}, "qxbd_03/baodian03_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [95, -74, -95, -74, -95, 75, 95, 75], "hull": 4}, "qxbd_03/baodian03_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [95, -74, -95, -74, -95, 75, 95, 75], "hull": 4}, "qxbd_03/baodian03_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [95, -74, -95, -74, -95, 75, 95, 75], "hull": 4}, "qxbd_03/baodian03_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [95, -74, -95, -74, -95, 75, 95, 75], "hull": 4}, "qxbd_03/baodian03_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [95, -74, -95, -74, -95, 75, 95, 75], "hull": 4}}, "eye": {"eye": {"type": "mesh", "uvs": [1, 0.79231, 0.8647, 0.92102, 0.74511, 0.97848, 0.42711, 1, 0.22166, 0.98986, 0.06786, 0.81724, 0, 0.5, 0.06058, 0.14712, 0.27446, 0.03876, 0.50711, 0.00014, 0.73227, 1e-05, 0.89965, 0.07233, 1, 0.5, 0.85818, 0.52612, 0.70723, 0.57357, 0.17651, 0.55112, 0.37169, 0.57971], "triangles": [5, 6, 15, 15, 8, 16, 16, 8, 9, 14, 9, 10, 6, 7, 15, 15, 7, 8, 4, 16, 3, 3, 14, 2, 3, 16, 14, 5, 15, 4, 4, 15, 16, 2, 14, 1, 14, 13, 1, 1, 13, 0, 0, 13, 12, 16, 9, 14, 14, 10, 13, 13, 11, 12, 13, 10, 11], "vertices": [2, 34, 100.28, -79, 0.97356, 35, -134.7, -30.25, 0.02644, 2, 34, 82.43, -52.06, 0.9983, 35, -133.43, 2.04, 0.0017, 1, 34, 74.82, -28.49, 1, 2, 34, 73.61, 33.58, 0.93904, 35, -90.54, 76.69, 0.06096, 2, 34, 76.31, 73.58, 0.78567, 35, -64.97, 107.57, 0.21433, 2, 34, 102.25, 102.79, 0.61489, 35, -26.85, 116.11, 0.38511, 2, 34, 148.64, 114.6, 0.35168, 35, 17.69, 98.58, 0.64832, 2, 34, 199.42, 101.22, 0.11393, 35, 51.08, 58.04, 0.88607, 2, 34, 213.84, 59.05, 0.01486, 35, 38.13, 15.4, 0.98514, 2, 34, 218.04, 13.53, 0.01426, 35, 14.94, -23.99, 0.98574, 2, 34, 216.71, -30.35, 0.24699, 35, -11.79, -58.83, 0.75301, 2, 34, 205.24, -62.65, 0.42177, 35, -39.98, -78.33, 0.57823, 2, 34, 142.64, -80.31, 0.83691, 35, -101.09, -56.07, 0.16309, 2, 34, 139.71, -52.55, 0.84235, 35, -87.25, -31.83, 0.15765, 2, 34, 133.74, -22.92, 0.8975, 35, -74.77, -4.29, 0.1025, 2, 34, 140.17, 80.43, 0.4393, 35, -9.15, 75.8, 0.5607, 2, 34, 134.86, 42.51, 0.62594, 35, -35.62, 48.14, 0.37406], "hull": 13}}, "qxbd_02/baodian02_00": {"qxbd_02/baodian02_00": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [119, -85, -118, -85, -118, 85, 119, 85], "hull": 4}, "qxbd_02/baodian02_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [119, -85, -118, -85, -118, 85, 119, 85], "hull": 4}, "qxbd_02/baodian02_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [119, -85, -118, -85, -118, 85, 119, 85], "hull": 4}, "qxbd_02/baodian02_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [119, -85, -118, -85, -118, 85, 119, 85], "hull": 4}, "qxbd_02/baodian02_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [119, -85, -118, -85, -118, 85, 119, 85], "hull": 4}, "qxbd_02/baodian02_10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [119, -85, -118, -85, -118, 85, 119, 85], "hull": 4}}, "bone7": {"bone7": {"type": "clipping", "end": "shuren08", "vertexCount": 5, "vertices": [-357.5, 383.3, -120.43, 303.49, 406.14, 360.72, 405.62, 1235.59, -364.01, 1232.73]}}, "shuren10": {"shuren08": {"type": "mesh", "uvs": [0.99978, 0.6866, 0.875, 1, 0.75714, 0.85081, 0.625, 1, 0.5, 1, 0.37657, 0.90491, 0.25, 1, 0.125, 1, 0.00159, 0.91004, 0.03482, 0.5376, 0.04614, 0.38673, 0.13126, 0.35976, 0.25073, 0.19265, 0.37514, 0.29029, 0.50041, 0.10788, 0.62563, 0.16696, 0.74102, 0.17236, 0.8515, 0, 0.99999, 0.33084, 0.99929, 0.56681, 0.875, 0.5, 0.75, 0.5, 0.625, 0.5, 0.5, 0.5, 0.375, 0.5, 0.25, 0.5, 0.125, 0.5], "triangles": [0, 20, 19, 2, 20, 1, 0, 1, 20, 2, 21, 20, 19, 20, 18, 18, 20, 17, 17, 20, 16, 20, 21, 16, 2, 22, 21, 4, 22, 3, 2, 3, 22, 4, 23, 22, 22, 16, 21, 22, 15, 16, 22, 23, 15, 5, 23, 4, 5, 25, 24, 5, 24, 23, 24, 13, 23, 23, 14, 15, 23, 13, 14, 24, 25, 13, 7, 25, 6, 5, 6, 25, 8, 9, 7, 9, 26, 7, 7, 26, 25, 9, 10, 26, 26, 11, 25, 25, 12, 13, 25, 11, 12, 26, 10, 11], "vertices": [1, 23, 219.11, -34.52, 1, 2, 22, 309.42, -134.54, 0.00787, 23, 108.04, -134.54, 0.99213, 2, 22, 207.77, -83.46, 0.54918, 23, 6.38, -83.46, 0.45082, 2, 21, 292.85, -129.65, 0.09844, 22, 91.47, -129.65, 0.90156, 2, 21, 183.88, -127.2, 0.65041, 22, -17.5, -127.2, 0.34959, 3, 20, 278.35, -93.7, 0.05295, 21, 76.97, -93.7, 0.94449, 22, -124.41, -93.7, 0.00256, 2, 20, 167.32, -122.31, 0.77294, 21, -34.06, -122.31, 0.22706, 2, 20, 58.35, -119.86, 0.99909, 21, -143.04, -119.86, 0.00091, 1, 20, -48.58, -88.04, 1, 1, 20, -16.88, 33.07, 1, 1, 20, -5.9, 82.17, 1, 2, 20, 68.5, 89.32, 0.98568, 21, -132.88, 89.32, 0.01432, 2, 20, 173.88, 141.61, 0.64896, 21, -27.5, 141.61, 0.35104, 3, 20, 281.62, 107.26, 0.07914, 21, 80.24, 107.26, 0.88615, 22, -121.14, 107.26, 0.03471, 2, 21, 190.79, 164.44, 0.60406, 22, -10.6, 164.44, 0.39594, 3, 21, 299.52, 142.67, 0.18282, 22, 98.14, 142.67, 0.7962, 23, -103.24, 142.67, 0.02098, 3, 21, 400.07, 138.65, 0.0116, 22, 198.69, 138.65, 0.54545, 23, -2.69, 138.65, 0.44295, 2, 22, 296.27, 192.84, 0.13528, 23, 94.89, 192.84, 0.86472, 2, 22, 423.29, 81.77, 0.00025, 23, 221.91, 81.77, 0.99975, 1, 23, 219.57, 4.64, 1, 2, 22, 313.09, 28.92, 0.00512, 23, 111.71, 28.92, 0.99488, 3, 21, 405.5, 31.37, 0.00044, 22, 204.12, 31.37, 0.55783, 23, 2.73, 31.37, 0.44173, 2, 21, 296.52, 33.81, 0.03549, 22, 95.14, 33.81, 0.96451, 2, 21, 187.55, 36.26, 0.7183, 22, -13.83, 36.26, 0.2817, 3, 20, 279.96, 38.7, 0.01765, 21, 78.58, 38.7, 0.97766, 22, -122.8, 38.7, 0.0047, 2, 20, 170.99, 41.15, 0.81925, 21, -30.39, 41.15, 0.18075, 2, 20, 62.01, 43.6, 0.99894, 21, -139.37, 43.6, 0.00106], "hull": 20}}, "shuren8": {"shuren08": {"type": "mesh", "uvs": [0.99978, 0.6866, 0.875, 1, 0.75714, 0.85081, 0.625, 1, 0.5, 1, 0.37657, 0.90491, 0.25, 1, 0.125, 1, 0.00159, 0.91004, 0.03482, 0.5376, 0.04614, 0.38673, 0.13126, 0.35976, 0.25073, 0.19265, 0.37514, 0.29029, 0.50041, 0.10788, 0.62563, 0.16696, 0.74102, 0.17236, 0.8515, 0, 0.99999, 0.33084, 0.99929, 0.56681, 0.875, 0.5, 0.75, 0.5, 0.625, 0.5, 0.5, 0.5, 0.375, 0.5, 0.25, 0.5, 0.125, 0.5], "triangles": [0, 20, 19, 2, 20, 1, 0, 1, 20, 2, 21, 20, 19, 20, 18, 18, 20, 17, 17, 20, 16, 20, 21, 16, 2, 22, 21, 4, 22, 3, 2, 3, 22, 4, 23, 22, 22, 16, 21, 22, 15, 16, 22, 23, 15, 5, 23, 4, 5, 25, 24, 5, 24, 23, 24, 13, 23, 23, 14, 15, 23, 13, 14, 24, 25, 13, 7, 25, 6, 5, 6, 25, 8, 9, 7, 9, 26, 7, 7, 26, 25, 9, 10, 26, 26, 11, 25, 25, 12, 13, 25, 11, 12, 26, 10, 11], "vertices": [1, 13, 219.11, -34.52, 1, 2, 12, 309.42, -134.54, 0.00787, 13, 108.04, -134.54, 0.99213, 2, 12, 207.77, -83.46, 0.54918, 13, 6.38, -83.46, 0.45082, 2, 11, 292.85, -129.65, 0.09844, 12, 91.47, -129.65, 0.90156, 2, 11, 183.88, -127.2, 0.65041, 12, -17.5, -127.2, 0.34959, 3, 10, 278.35, -93.7, 0.05295, 11, 76.97, -93.7, 0.94449, 12, -124.41, -93.7, 0.00256, 2, 10, 167.32, -122.31, 0.77294, 11, -34.06, -122.31, 0.22706, 2, 10, 58.35, -119.86, 0.99909, 11, -143.04, -119.86, 0.00091, 1, 10, -48.58, -88.04, 1, 1, 10, -16.88, 33.07, 1, 1, 10, -5.9, 82.17, 1, 2, 10, 68.5, 89.32, 0.98568, 11, -132.88, 89.32, 0.01432, 2, 10, 173.88, 141.61, 0.64896, 11, -27.5, 141.61, 0.35104, 3, 10, 281.62, 107.26, 0.07914, 11, 80.24, 107.26, 0.88615, 12, -121.14, 107.26, 0.03471, 2, 11, 190.79, 164.44, 0.60406, 12, -10.6, 164.44, 0.39594, 3, 11, 299.52, 142.67, 0.18282, 12, 98.14, 142.67, 0.7962, 13, -103.24, 142.67, 0.02098, 3, 11, 400.07, 138.65, 0.0116, 12, 198.69, 138.65, 0.54545, 13, -2.69, 138.65, 0.44295, 2, 12, 296.27, 192.84, 0.13528, 13, 94.89, 192.84, 0.86472, 2, 12, 423.29, 81.77, 0.00025, 13, 221.91, 81.77, 0.99975, 1, 13, 219.57, 4.64, 1, 2, 12, 313.09, 28.92, 0.00512, 13, 111.71, 28.92, 0.99488, 3, 11, 405.5, 31.37, 0.00044, 12, 204.12, 31.37, 0.55783, 13, 2.73, 31.37, 0.44173, 2, 11, 296.52, 33.81, 0.03549, 12, 95.14, 33.81, 0.96451, 2, 11, 187.55, 36.26, 0.7183, 12, -13.83, 36.26, 0.2817, 3, 10, 279.96, 38.7, 0.01765, 11, 78.58, 38.7, 0.97766, 12, -122.8, 38.7, 0.0047, 2, 10, 170.99, 41.15, 0.81925, 11, -30.39, 41.15, 0.18075, 2, 10, 62.01, 43.6, 0.99894, 11, -139.37, 43.6, 0.00106], "hull": 20}}, "shuren9": {"shuren08": {"type": "mesh", "uvs": [0.99978, 0.6866, 0.875, 1, 0.75714, 0.85081, 0.625, 1, 0.5, 1, 0.37657, 0.90491, 0.25, 1, 0.125, 1, 0.00159, 0.91004, 0.03482, 0.5376, 0.04614, 0.38673, 0.13126, 0.35976, 0.25073, 0.19265, 0.37514, 0.29029, 0.50041, 0.10788, 0.62563, 0.16696, 0.74102, 0.17236, 0.8515, 0, 0.99999, 0.33084, 0.99929, 0.56681, 0.875, 0.5, 0.75, 0.5, 0.625, 0.5, 0.5, 0.5, 0.375, 0.5, 0.25, 0.5, 0.125, 0.5], "triangles": [0, 20, 19, 2, 20, 1, 0, 1, 20, 2, 21, 20, 19, 20, 18, 18, 20, 17, 17, 20, 16, 20, 21, 16, 2, 22, 21, 4, 22, 3, 2, 3, 22, 4, 23, 22, 22, 16, 21, 22, 15, 16, 22, 23, 15, 5, 23, 4, 5, 25, 24, 5, 24, 23, 24, 13, 23, 23, 14, 15, 23, 13, 14, 24, 25, 13, 7, 25, 6, 5, 6, 25, 8, 9, 7, 9, 26, 7, 7, 26, 25, 9, 10, 26, 26, 11, 25, 25, 12, 13, 25, 11, 12, 26, 10, 11], "vertices": [1, 18, 219.11, -34.52, 1, 2, 17, 309.42, -134.54, 0.00787, 18, 108.04, -134.54, 0.99213, 2, 17, 207.77, -83.46, 0.54918, 18, 6.38, -83.46, 0.45082, 2, 16, 292.85, -129.65, 0.09844, 17, 91.47, -129.65, 0.90156, 2, 16, 183.88, -127.2, 0.65041, 17, -17.5, -127.2, 0.34959, 3, 15, 278.35, -93.7, 0.05295, 16, 76.97, -93.7, 0.94449, 17, -124.41, -93.7, 0.00256, 2, 15, 167.32, -122.31, 0.77294, 16, -34.06, -122.31, 0.22706, 2, 15, 58.35, -119.86, 0.99909, 16, -143.04, -119.86, 0.00091, 1, 15, -48.58, -88.04, 1, 1, 15, -16.88, 33.07, 1, 1, 15, -5.9, 82.17, 1, 2, 15, 68.5, 89.32, 0.98568, 16, -132.88, 89.32, 0.01432, 2, 15, 173.88, 141.61, 0.64896, 16, -27.5, 141.61, 0.35104, 3, 15, 281.62, 107.26, 0.07914, 16, 80.24, 107.26, 0.88615, 17, -121.14, 107.26, 0.03471, 2, 16, 190.79, 164.44, 0.60406, 17, -10.6, 164.44, 0.39594, 3, 16, 299.52, 142.67, 0.18282, 17, 98.14, 142.67, 0.7962, 18, -103.24, 142.67, 0.02098, 3, 16, 400.07, 138.65, 0.0116, 17, 198.69, 138.65, 0.54545, 18, -2.69, 138.65, 0.44295, 2, 17, 296.27, 192.84, 0.13528, 18, 94.89, 192.84, 0.86472, 2, 17, 423.29, 81.77, 0.00025, 18, 221.91, 81.77, 0.99975, 1, 18, 219.57, 4.64, 1, 2, 17, 313.09, 28.92, 0.00512, 18, 111.71, 28.92, 0.99488, 3, 16, 405.5, 31.37, 0.00044, 17, 204.12, 31.37, 0.55783, 18, 2.73, 31.37, 0.44173, 2, 16, 296.52, 33.81, 0.03549, 17, 95.14, 33.81, 0.96451, 2, 16, 187.55, 36.26, 0.7183, 17, -13.83, 36.26, 0.2817, 3, 15, 279.96, 38.7, 0.01765, 16, 78.58, 38.7, 0.97766, 17, -122.8, 38.7, 0.0047, 2, 15, 170.99, 41.15, 0.81925, 16, -30.39, 41.15, 0.18075, 2, 15, 62.01, 43.6, 0.99894, 16, -139.37, 43.6, 0.00106], "hull": 20}}}}], "events": {"atk": {}}, "animations": {"boss_attack1": {"slots": {"renoutuowei/tuiwei_01": {"attachment": [{"time": 0.2333, "name": null}, {"time": 0.2667, "name": "renoutuowei/tuiwei_01"}, {"time": 0.3, "name": "renoutuowei/tuiwei_02"}, {"time": 0.3333, "name": "renoutuowei/tuiwei_03"}, {"time": 0.3667, "name": null}]}, "glow02_00": {"attachment": [{"time": 0.3333, "name": "glow02_00"}, {"time": 0.3667, "name": null}]}, "qxbd_03/baodian03_00": {"attachment": [{"time": 0.3, "name": null}, {"time": 0.3333, "name": "qxbd_03/baodian03_00"}, {"time": 0.3667, "name": "qxbd_03/baodian03_01"}, {"time": 0.4, "name": "qxbd_03/baodian03_02"}, {"time": 0.4333, "name": "qxbd_03/baodian03_04"}, {"time": 0.4667, "name": "qxbd_03/baodian03_06"}, {"time": 0.5, "name": "qxbd_03/baodian03_08"}, {"time": 0.5333, "name": null}]}, "qxbd_02/baodian02_00": {"attachment": [{"time": 0.3333, "name": null}, {"time": 0.3667, "name": "qxbd_02/baodian02_00"}, {"time": 0.4, "name": "qxbd_02/baodian02_01"}, {"time": 0.4333, "name": "qxbd_02/baodian02_02"}, {"time": 0.4667, "name": "qxbd_02/baodian02_04"}, {"time": 0.5, "name": "qxbd_02/baodian02_06"}, {"time": 0.5333, "name": "qxbd_02/baodian02_10"}, {"time": 0.5667, "name": null}]}}, "bones": {"root": {"rotate": [{"curve": "stepped"}, {"time": 0.5333}], "translate": [{"curve": "stepped"}, {"time": 0.5333}], "scale": [{"curve": "stepped"}, {"time": 0.5333}], "shear": [{"curve": "stepped"}, {"time": 0.5333}]}, "bone54": {"rotate": [{"curve": "stepped"}, {"time": 0.5333}], "translate": [{"curve": "stepped"}, {"time": 0.5333}], "scale": [{"curve": "stepped"}, {"time": 0.5333}], "shear": [{"curve": "stepped"}, {"time": 0.5333}]}, "bone24": {"rotate": [{"curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.1667, "angle": 12.62, "curve": 0.326, "c2": 0.31, "c3": 0.662, "c4": 0.65}, {"time": 0.3, "angle": 6.34, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5333}], "translate": [{"curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.1667, "x": 9.41, "curve": 0.311, "c2": 0.25, "c3": 0.649, "c4": 0.6}, {"time": 0.3, "x": -9.75, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5333}], "scale": [{"curve": "stepped"}, {"time": 0.5333}], "shear": [{"curve": "stepped"}, {"time": 0.5333}]}, "bone25": {"rotate": [{"angle": 4.5, "curve": "stepped"}, {"time": 0.5333, "angle": 4.5}], "translate": [{"curve": "stepped"}, {"time": 0.5333}], "scale": [{"curve": "stepped"}, {"time": 0.5333}], "shear": [{"curve": "stepped"}, {"time": 0.5333}]}, "bone26": {"rotate": [{"angle": -2.94, "curve": "stepped"}, {"time": 0.5333, "angle": -2.94}], "translate": [{"curve": "stepped"}, {"time": 0.5333}], "scale": [{"curve": "stepped"}, {"time": 0.5333}], "shear": [{"curve": "stepped"}, {"time": 0.5333}]}, "bone27": {"rotate": [{"angle": -2.57, "curve": "stepped"}, {"time": 0.5333, "angle": -2.57}], "translate": [{"curve": "stepped"}, {"time": 0.5333}], "scale": [{"curve": "stepped"}, {"time": 0.5333}], "shear": [{"curve": "stepped"}, {"time": 0.5333}]}, "bone28": {"rotate": [{"angle": 2.77, "curve": "stepped"}, {"time": 0.5333, "angle": 2.77}], "translate": [{"curve": "stepped"}, {"time": 0.5333}], "scale": [{"curve": "stepped"}, {"time": 0.5333}], "shear": [{"curve": "stepped"}, {"time": 0.5333}]}, "bone29": {"rotate": [{"angle": -1.74, "curve": "stepped"}, {"time": 0.5333, "angle": -1.74}], "translate": [{"curve": "stepped"}, {"time": 0.5333}], "scale": [{"curve": "stepped"}, {"time": 0.5333}], "shear": [{"curve": "stepped"}, {"time": 0.5333}]}, "bone30": {"rotate": [{"angle": -5.56, "curve": "stepped"}, {"time": 0.5333, "angle": -5.56}], "translate": [{"curve": "stepped"}, {"time": 0.5333}], "scale": [{"curve": "stepped"}, {"time": 0.5333}], "shear": [{"curve": "stepped"}, {"time": 0.5333}]}, "bone31": {"rotate": [{"curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.1667, "angle": 21.18, "curve": 0.326, "c2": 0.31, "c3": 0.662, "c4": 0.65}, {"time": 0.3, "angle": -28.9, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5333}], "translate": [{"curve": "stepped"}, {"time": 0.5333}], "scale": [{"curve": "stepped"}, {"time": 0.5333}], "shear": [{"curve": "stepped"}, {"time": 0.5333}]}, "bone32": {"rotate": [{"curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.1667, "angle": 1.54, "curve": 0.67, "c2": -2.81, "c3": 0.75}, {"time": 0.3, "angle": -53.69, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}], "translate": [{"curve": "stepped"}, {"time": 0.5333}], "scale": [{"curve": "stepped"}, {"time": 0.5333}], "shear": [{"curve": "stepped"}, {"time": 0.5333}]}, "bone33": {"rotate": [{"curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.1667, "angle": 1.54, "curve": 0.67, "c2": -2.81, "c3": 0.75}, {"time": 0.3, "angle": -53.49, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": 0.67, "c2": -2.81, "c3": 0.75}, {"time": 0.3, "x": -4.27, "y": -37.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}], "scale": [{"curve": "stepped"}, {"time": 0.5333}], "shear": [{"curve": "stepped"}, {"time": 0.5333}]}, "bone34": {"rotate": [{"curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.1667, "angle": 1.54, "curve": 0.67, "c2": -2.81, "c3": 0.75}, {"time": 0.3, "angle": -34.65, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}], "translate": [{"curve": "stepped"}, {"time": 0.5333}], "scale": [{"curve": "stepped"}, {"time": 0.5333}], "shear": [{"curve": "stepped"}, {"time": 0.5333}]}, "bone35": {"rotate": [{"curve": "stepped"}, {"time": 0.5333}], "translate": [{"curve": "stepped"}, {"time": 0.5333}], "scale": [{"curve": "stepped"}, {"time": 0.5333}], "shear": [{"curve": "stepped"}, {"time": 0.5333}]}, "bone36": {"rotate": [{"curve": "stepped"}, {"time": 0.5333}], "translate": [{"curve": "stepped"}, {"time": 0.5333}], "scale": [{"curve": "stepped"}, {"time": 0.5333}], "shear": [{"curve": "stepped"}, {"time": 0.5333}]}, "bone37": {"rotate": [{"curve": "stepped"}, {"time": 0.5333}], "translate": [{"curve": "stepped"}, {"time": 0.5333}], "scale": [{"curve": "stepped"}, {"time": 0.5333}], "shear": [{"curve": "stepped"}, {"time": 0.5333}]}, "bone38": {"rotate": [{"curve": "stepped"}, {"time": 0.5333}], "translate": [{"curve": "stepped"}, {"time": 0.5333}], "scale": [{"curve": "stepped"}, {"time": 0.5333}], "shear": [{"curve": "stepped"}, {"time": 0.5333}]}, "bone39": {"rotate": [{"curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.1667, "angle": 13.78, "curve": 0.67, "c2": -2.81, "c3": 0.75}, {"time": 0.3, "angle": 35.72, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.5333}], "scale": [{"curve": "stepped"}, {"time": 0.2333, "curve": 0.322, "c2": 0.29, "c3": 0.657, "c4": 0.63}, {"time": 0.3, "x": 1.554, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}], "shear": [{"curve": "stepped"}, {"time": 0.5333}]}, "bone40": {"rotate": [{"curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.1667, "angle": 13.78, "curve": 0.326, "c2": 0.31, "c3": 0.662, "c4": 0.65}, {"time": 0.3, "angle": -17.99, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5333}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.5333}], "scale": [{"curve": "stepped"}, {"time": 0.5333}], "shear": [{"curve": "stepped"}, {"time": 0.5333}]}, "bone41": {"rotate": [{"curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.1667, "angle": 13.78, "curve": 0.326, "c2": 0.31, "c3": 0.662, "c4": 0.65}, {"time": 0.3, "angle": -16.97, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5333}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.5333}], "scale": [{"curve": "stepped"}, {"time": 0.2333, "curve": 0.322, "c2": 0.29, "c3": 0.657, "c4": 0.63}, {"time": 0.3, "x": 1.554, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5333}], "shear": [{"curve": "stepped"}, {"time": 0.5333}]}, "bone42": {"rotate": [{"curve": "stepped"}, {"time": 0.5333}], "translate": [{"curve": "stepped"}, {"time": 0.5333}], "scale": [{"curve": "stepped"}, {"time": 0.5333}], "shear": [{"curve": "stepped"}, {"time": 0.5333}]}, "bone43": {"rotate": [{"curve": "stepped"}, {"time": 0.5333}], "translate": [{"curve": "stepped"}, {"time": 0.5333}], "scale": [{"curve": "stepped"}, {"time": 0.5333}], "shear": [{"curve": "stepped"}, {"time": 0.5333}]}, "bone44": {"rotate": [{"curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.1667, "angle": 13.78, "curve": 0.67, "c2": -2.81, "c3": 0.75}, {"time": 0.3, "angle": -77.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.5333}], "scale": [{"curve": "stepped"}, {"time": 0.2333, "curve": 0.322, "c2": 0.29, "c3": 0.657, "c4": 0.63}, {"time": 0.3, "x": 1.554, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}], "shear": [{"curve": "stepped"}, {"time": 0.5333}]}, "bone45": {"rotate": [{"curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.1667, "angle": 13.78, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5333}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.5333}], "scale": [{"curve": "stepped"}, {"time": 0.5333}], "shear": [{"curve": "stepped"}, {"time": 0.5333}]}, "bone46": {"rotate": [{"curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.1667, "angle": 13.78, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5333}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.5333}], "scale": [{"curve": "stepped"}, {"time": 0.5333}], "shear": [{"curve": "stepped"}, {"time": 0.5333}]}, "bone47": {"rotate": [{"curve": "stepped"}, {"time": 0.5333}], "translate": [{"curve": "stepped"}, {"time": 0.5333}], "scale": [{"curve": "stepped"}, {"time": 0.5333}], "shear": [{"curve": "stepped"}, {"time": 0.5333}]}, "bone48": {"rotate": [{"curve": "stepped"}, {"time": 0.5333}], "translate": [{"curve": "stepped"}, {"time": 0.5333}], "scale": [{"curve": "stepped"}, {"time": 0.5333}], "shear": [{"curve": "stepped"}, {"time": 0.5333}]}, "bone49": {"rotate": [{"curve": "stepped"}, {"time": 0.5333}], "translate": [{"curve": "stepped"}, {"time": 0.5333}], "scale": [{"curve": "stepped"}, {"time": 0.5333}], "shear": [{"curve": "stepped"}, {"time": 0.5333}]}, "bone50": {"rotate": [{"curve": "stepped"}, {"time": 0.5333}], "translate": [{"curve": "stepped"}, {"time": 0.5333}], "scale": [{"curve": "stepped"}, {"time": 0.5333}], "shear": [{"curve": "stepped"}, {"time": 0.5333}]}, "bone51": {"rotate": [{"curve": "stepped"}, {"time": 0.5333}], "translate": [{"curve": "stepped"}, {"time": 0.5333}], "scale": [{"curve": "stepped"}, {"time": 0.5333}], "shear": [{"curve": "stepped"}, {"time": 0.5333}]}, "bone52": {"rotate": [{"curve": "stepped"}, {"time": 0.5333}], "translate": [{"curve": "stepped"}, {"time": 0.5333}], "scale": [{"curve": "stepped"}, {"time": 0.5333}], "shear": [{"curve": "stepped"}, {"time": 0.5333}]}, "bone53": {"rotate": [{"time": 1.1333}], "translate": [{"time": 0.2, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.3, "x": 33.7, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.4333}], "scale": [{"curve": "stepped"}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 1.209, "y": 1.209, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}}, "deform": {"default": {"glow02_00": {"glow02_00": [{"time": 0.3333, "vertices": [336.0097, -309.1297, -336.01123, -309.1297, -336.0113, 309.1295, 336.00964, 309.1295]}]}}}, "events": [{"time": 0.3333, "name": "atk"}]}, "boss_attack3": {"slots": {"chongji/changcici (1)4": {"attachment": [{"time": 0.4667, "name": null}, {"time": 0.5, "name": "chongji/chang<PERSON>ci (1)"}, {"time": 0.5333, "name": "chongji/chang<PERSON>ci (3)"}, {"time": 0.5667, "name": "chongji/chang<PERSON>ci (5)"}, {"time": 0.6, "name": "chongji/chang<PERSON>ci (7)"}, {"time": 0.6333, "name": "chongji/chang<PERSON>ci (7)"}, {"time": 0.6667, "name": null}]}, "shuren8": {"color": [{"time": 0.8, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 0.4, "name": "shuren08"}]}, "renoutuowei/tuiwei_01": {"attachment": [{"time": 0.3667, "name": "renoutuowei/tuiwei_01"}, {"time": 0.4, "name": "renoutuowei/tuiwei_03"}, {"time": 0.4333, "name": null}]}, "shuren10": {"color": [{"time": 0.8667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 0.4667, "name": "shuren08"}]}, "shuren08": {"color": [{"color": "ffffff00", "curve": 0.289, "c3": 0.628, "c4": 0.38}, {"time": 0.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "color": "ffffff00"}], "attachment": [{"name": "shuren08"}]}, "chongji/changcici (1)3": {"attachment": [{"time": 0.4, "name": null}, {"time": 0.4333, "name": "chongji/chang<PERSON>ci (1)"}, {"time": 0.4667, "name": "chongji/chang<PERSON>ci (3)"}, {"time": 0.5, "name": "chongji/chang<PERSON>ci (5)"}, {"time": 0.5333, "name": "chongji/chang<PERSON>ci (7)"}, {"time": 0.5667, "name": "chongji/chang<PERSON>ci (7)"}, {"time": 0.6, "name": null}]}, "qxbd_03/baodian03_00": {"attachment": [{"time": 0.4333, "name": null}, {"time": 0.4667, "name": "qxbd_03/baodian03_00"}, {"time": 0.5, "name": "qxbd_03/baodian03_01"}, {"time": 0.5333, "name": "qxbd_03/baodian03_02"}, {"time": 0.5667, "name": "qxbd_03/baodian03_04"}, {"time": 0.6, "name": "qxbd_03/baodian03_06"}, {"time": 0.6333, "name": "qxbd_03/baodian03_08"}, {"time": 0.6667, "name": null}]}, "shuren9": {"color": [{"time": 0.9333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 0.5333, "name": "shuren08"}]}, "chongji/changcici (1)": {"attachment": [{"time": 0.3667, "name": null}, {"time": 0.4, "name": "chongji/chang<PERSON>ci (3)"}, {"time": 0.4333, "name": "chongji/chang<PERSON>ci (5)"}, {"time": 0.4667, "name": "chongji/chang<PERSON>ci (7)"}, {"time": 0.5, "name": "chongji/chang<PERSON>ci (7)"}, {"time": 0.5333, "name": null}]}, "qxbd_02/baodian02_00": {"attachment": [{"time": 0.4667, "name": null}, {"time": 0.5, "name": "qxbd_02/baodian02_00"}, {"time": 0.5333, "name": "qxbd_02/baodian02_01"}, {"time": 0.5667, "name": "qxbd_02/baodian02_02"}, {"time": 0.6, "name": "qxbd_02/baodian02_04"}, {"time": 0.6333, "name": "qxbd_02/baodian02_06"}, {"time": 0.6667, "name": "qxbd_02/baodian02_10"}, {"time": 0.7, "name": null}]}, "chongji/changcici (1)2": {"attachment": [{"time": 0.4333, "name": null}, {"time": 0.4667, "name": "chongji/chang<PERSON>ci (1)"}, {"time": 0.5, "name": "chongji/chang<PERSON>ci (3)"}, {"time": 0.5333, "name": "chongji/chang<PERSON>ci (5)"}, {"time": 0.5667, "name": "chongji/chang<PERSON>ci (7)"}, {"time": 0.6, "name": "chongji/chang<PERSON>ci (7)"}, {"time": 0.6333, "name": null}]}}, "bones": {"bone": {"rotate": [{"angle": -40.84, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 64.86, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -85.51, "curve": "stepped"}, {"time": 0.5667, "angle": -85.51, "curve": "stepped"}, {"time": 0.6333, "angle": -85.51, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 3.32}], "translate": [{"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": -138.67, "y": 280.13}], "scale": [{"x": 0.548, "y": 0.548, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "x": 1.231, "y": 1.231, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": 0.737}]}, "bone2": {"rotate": [{"angle": -40.84, "curve": "stepped"}, {"time": 0.0333, "angle": -40.84, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 64.86, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 0.3, "curve": "stepped"}, {"time": 0.5667, "angle": 0.3, "curve": "stepped"}, {"time": 0.6333, "angle": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 0.97}], "scale": [{"x": 0.548, "y": 0.548, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": 0.737}]}, "bone3": {"rotate": [{"angle": -40.84, "curve": "stepped"}, {"time": 0.0667, "angle": -40.84, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 64.86, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -4.56, "curve": "stepped"}, {"time": 0.5667, "angle": -4.56, "curve": "stepped"}, {"time": 0.6333, "angle": -4.56, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 6.27}], "scale": [{"x": 0.548, "y": 0.548, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": 0.737}]}, "bone4": {"rotate": [{"angle": -40.84, "curve": "stepped"}, {"time": 0.1, "angle": -40.84, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 64.86, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -6.21, "curve": "stepped"}, {"time": 0.5667, "angle": -6.21, "curve": "stepped"}, {"time": 0.6333, "angle": -6.21, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -4.29}], "scale": [{"x": 0.548, "y": 0.548, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": 0.737}]}, "bone5": {"rotate": [{"angle": -40.84, "curve": "stepped"}, {"time": 0.1333, "angle": -40.84, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 64.86, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 6.73, "curve": "stepped"}, {"time": 0.6333, "angle": 6.73, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -4.08}], "scale": [{"x": 0.548, "y": 0.548, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": 0.737}]}, "bone6": {"rotate": [{"angle": -6.55, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 14.94, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -10.95}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": 618.67, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "y": 277.21, "curve": "stepped"}, {"time": 0.6667, "y": 277.21, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}], "scale": [{"time": 1.2}]}, "root": {"rotate": [{"curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.2667}], "translate": [{"curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.2667}], "scale": [{"curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.2667}], "shear": [{"curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.2667}]}, "bone10": {"rotate": [{"time": 0.4, "angle": 32.64, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -29.3, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 9.33}], "scale": [{"x": 0.236, "y": 0.236, "curve": "stepped"}, {"time": 0.4, "x": 0.236, "y": 0.236, "curve": 0, "c2": 0.93, "c3": 0.75}, {"time": 0.5, "x": 0.957, "y": 0.957}]}, "bone11": {"rotate": [{"time": 0.4, "angle": 32.64, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -29.3, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 9.33}], "scale": [{"x": 0.236, "y": 0.236, "curve": "stepped"}, {"time": 0.4, "x": 0.236, "y": 0.236, "curve": 0, "c2": 0.93, "c3": 0.75}, {"time": 0.5, "x": 0.957, "y": 0.957}]}, "bone12": {"rotate": [{"time": 0.4333, "angle": 32.64, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -29.3, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 9.33}], "scale": [{"x": 0.236, "y": 0.236, "curve": "stepped"}, {"time": 0.4, "x": 0.236, "y": 0.236, "curve": 0, "c2": 0.93, "c3": 0.75}, {"time": 0.5, "x": 0.957, "y": 0.957}]}, "bone13": {"rotate": [{"time": 0.4667, "angle": 32.64, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -29.3, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 9.33}], "scale": [{"x": 0.236, "y": 0.236, "curve": "stepped"}, {"time": 0.4, "x": 0.236, "y": 0.236, "curve": 0, "c2": 0.93, "c3": 0.75}, {"time": 0.5, "x": 0.957, "y": 0.957}]}, "bone15": {"rotate": [{"time": 0.5, "angle": 32.64, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -29.3, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 9.33}], "scale": [{"x": 0.236, "y": 0.236, "curve": "stepped"}, {"time": 0.5333, "x": 0.236, "y": 0.236, "curve": 0, "c2": 0.98, "c3": 0.75}, {"time": 0.6333, "x": 0.957, "y": 0.957}]}, "bone18": {"rotate": [{"time": 0.6, "angle": 32.64, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -29.3, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 9.33}], "scale": [{"x": 0.236, "y": 0.236, "curve": "stepped"}, {"time": 0.5333, "x": 0.236, "y": 0.236, "curve": 0, "c2": 0.98, "c3": 0.75}, {"time": 0.6333, "x": 0.957, "y": 0.957}]}, "bone17": {"rotate": [{"time": 0.5667, "angle": 32.64, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -29.3, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 9.33}], "scale": [{"x": 0.236, "y": 0.236, "curve": "stepped"}, {"time": 0.5333, "x": 0.236, "y": 0.236, "curve": 0, "c2": 0.98, "c3": 0.75}, {"time": 0.6333, "x": 0.957, "y": 0.957}]}, "bone16": {"rotate": [{"time": 0.5333, "angle": 32.64, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -29.3, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 9.33}], "scale": [{"x": 0.236, "y": 0.236, "curve": "stepped"}, {"time": 0.5333, "x": 0.236, "y": 0.236, "curve": 0, "c2": 0.98, "c3": 0.75}, {"time": 0.6333, "x": 0.957, "y": 0.957}]}, "bone20": {"rotate": [{"time": 0.4333, "angle": 32.64, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -29.3, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 9.33}], "scale": [{"x": 0.236, "y": 0.236, "curve": "stepped"}, {"time": 0.4667, "x": 0.236, "y": 0.236, "curve": 0, "c2": 0.98, "c3": 0.75}, {"time": 0.5667, "x": 0.957, "y": 0.957}]}, "bone23": {"rotate": [{"time": 0.5333, "angle": 32.64, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -29.3, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 9.33}], "scale": [{"x": 0.236, "y": 0.236, "curve": "stepped"}, {"time": 0.4667, "x": 0.236, "y": 0.236, "curve": 0, "c2": 0.98, "c3": 0.75}, {"time": 0.5667, "x": 0.957, "y": 0.957}]}, "bone22": {"rotate": [{"time": 0.5, "angle": 32.64, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -29.3, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 9.33}], "scale": [{"x": 0.236, "y": 0.236, "curve": "stepped"}, {"time": 0.4667, "x": 0.236, "y": 0.236, "curve": 0, "c2": 0.98, "c3": 0.75}, {"time": 0.5667, "x": 0.957, "y": 0.957}]}, "bone21": {"rotate": [{"time": 0.4667, "angle": 32.64, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -29.3, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 9.33}], "scale": [{"x": 0.236, "y": 0.236, "curve": "stepped"}, {"time": 0.4667, "x": 0.236, "y": 0.236, "curve": 0, "c2": 0.98, "c3": 0.75}, {"time": 0.5667, "x": 0.957, "y": 0.957}]}, "bone19": {"scale": [{"time": 0.4, "curve": 0, "c2": 1.22, "c3": 0.75}, {"time": 0.5, "x": 1.903, "y": 1.903}]}, "bone9": {"scale": [{"time": 0.4, "curve": 0, "c2": 1.22, "c3": 0.75}, {"time": 0.5, "x": 1.903, "y": 1.903}]}, "bone14": {"scale": [{"time": 0.4, "curve": 0, "c2": 1.22, "c3": 0.75}, {"time": 0.5, "x": 1.903, "y": 1.903}]}, "bone37": {"rotate": [{"angle": -3.16, "curve": 0.368, "c2": 0.48, "c3": 0.713, "c4": 0.87}, {"time": 0.1333, "angle": 112.48, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.1667, "angle": 173.27, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 176.77, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 99.3, "curve": "stepped"}, {"time": 0.6, "angle": 99.3, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -8.87, "curve": 0.315, "c2": 0.28, "c3": 0.673, "c4": 0.69}, {"time": 1.2667, "angle": -3.16}], "translate": [{"curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.2667}], "scale": [{"curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.8667}]}, "bone54": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 72, "curve": 0.25, "c3": 0.75}, {"time": 1.2667}], "translate": [{"curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.8667}]}, "bone27": {"rotate": [{"angle": -2.57, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 1.15}], "translate": [{"curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.8667}]}, "bone41": {"rotate": [{"angle": 1.01, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.8667, "angle": 2.83, "curve": 0.315, "c2": 0.28, "c3": 0.673, "c4": 0.69}, {"time": 1.2667, "angle": 1.01}], "translate": [{"curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.2667}], "scale": [{"curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.8667}]}, "bone31": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 8.44, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 0.68, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.2667}], "translate": [{"curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.2667}], "scale": [{"curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.8667}]}, "bone26": {"rotate": [{"angle": -2.94, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 17.85}], "translate": [{"curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.8667}]}, "bone45": {"rotate": [{"angle": 0.34, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.8667, "angle": 2.07, "curve": 0.346, "c2": 0.37, "c3": 0.704, "c4": 0.79}, {"time": 1.2667, "angle": 0.34}], "translate": [{"curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.2667}], "scale": [{"curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.8667}]}, "bone29": {"rotate": [{"angle": -1.74, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -2.86}], "translate": [{"curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.8667}]}, "bone34": {"rotate": [{"angle": 0.92, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": -7.01, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 7.14, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 1.77, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 1.2667, "angle": 0.92}], "translate": [{"curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.2667}], "scale": [{"curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.8667}]}, "bone42": {"rotate": [{"angle": 1.78, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8667, "angle": 3.41, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 1.2667, "angle": 1.78}], "translate": [{"curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.2667}], "scale": [{"curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.8667}]}, "bone39": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 1.31, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.2667}], "translate": [{"curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.2667}], "scale": [{"curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.8667}]}, "bone38": {"rotate": [{"angle": -7.05, "curve": 0.347, "c2": 0.38, "c3": 0.716, "c4": 0.83}, {"time": 0.1333, "angle": 154.32, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1667, "angle": 158.33, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 154.34, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 125.98, "curve": "stepped"}, {"time": 0.6, "angle": 125.98, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -11.15, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.2667, "angle": -7.05}], "translate": [{"curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.2667}], "scale": [{"curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.8667}]}, "bone48": {"rotate": [{"angle": 0.59, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.8667, "angle": 2.39, "curve": 0.333, "c2": 0.33, "c3": 0.691, "c4": 0.74}, {"time": 1.2667, "angle": 0.59}], "translate": [{"curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.2667}], "scale": [{"curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.8667}]}, "bone40": {"rotate": [{"angle": 0.34, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.8667, "angle": 2.07, "curve": 0.346, "c2": 0.37, "c3": 0.704, "c4": 0.79}, {"time": 1.2667, "angle": 0.34}], "translate": [{"curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.2667}], "scale": [{"curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.8667}]}, "bone47": {"rotate": [{"angle": 0.59, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.8667, "angle": 2.39, "curve": 0.333, "c2": 0.33, "c3": 0.691, "c4": 0.74}, {"time": 1.2667, "angle": 0.59}], "translate": [{"curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.2667}], "scale": [{"curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.8667}]}, "bone43": {"rotate": [{"angle": 0.59, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.8667, "angle": 2.39, "curve": 0.333, "c2": 0.33, "c3": 0.691, "c4": 0.74}, {"time": 1.2667, "angle": 0.59}], "translate": [{"curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.2667}], "scale": [{"curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.8667}]}, "bone30": {"rotate": [{"angle": -5.56, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -21.86}], "translate": [{"curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.8667}]}, "bone50": {"rotate": [{"angle": 2.55, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.8667, "angle": 3.47, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.9333, "angle": 3.55, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.2667, "angle": 2.55}], "translate": [{"curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.2667}], "scale": [{"curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.8667}]}, "bone52": {"rotate": [{"angle": 2.55, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.8667, "angle": 3.47, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.9333, "angle": 3.55, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.2667, "angle": 2.55}], "translate": [{"curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.2667}], "scale": [{"curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.8667}]}, "bone46": {"rotate": [{"angle": 1.01, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.8667, "angle": 2.83, "curve": 0.315, "c2": 0.28, "c3": 0.673, "c4": 0.69}, {"time": 1.2667, "angle": 1.01}], "translate": [{"curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.2667}], "scale": [{"curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.8667}]}, "bone32": {"rotate": [{"angle": 0.24, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "angle": -9.59, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.11, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 1.16, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 1.2667, "angle": 0.24}], "translate": [{"curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.2667}], "scale": [{"curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.8667}]}, "bone36": {"rotate": [{"angle": 7.52, "curve": 0.347, "c2": 0.38, "c3": 0.716, "c4": 0.83}, {"time": 0.1333, "angle": -53.94, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1667, "angle": -53.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -48.75, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -87.67, "curve": "stepped"}, {"time": 0.6, "angle": -87.67, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 11.89, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.2667, "angle": 7.52}], "translate": [{"curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.2667}], "scale": [{"curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.8667}]}, "bone33": {"rotate": [{"angle": 0.52, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "angle": -25.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -11.35, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 1.47, "curve": 0.315, "c2": 0.28, "c3": 0.673, "c4": 0.69}, {"time": 1.2667, "angle": 0.52}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -6.83, "y": -38.16, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -21.8, "y": -17.8, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.2667}], "scale": [{"curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.8667}]}, "bone44": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 1.31, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.2667}], "translate": [{"curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.2667}], "scale": [{"curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.8667}]}, "bone28": {"rotate": [{"angle": 2.77, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 0.18}], "translate": [{"curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.8667}]}, "bone51": {"rotate": [{"angle": 1.78, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8667, "angle": 3.41, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 1.2667, "angle": 1.78}], "translate": [{"curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.2667}], "scale": [{"curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.8667}]}, "bone25": {"rotate": [{"angle": 4.5, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -2.76}], "translate": [{"curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.8667}]}, "bone35": {"rotate": [{"angle": 3.37, "curve": 0.368, "c2": 0.48, "c3": 0.713, "c4": 0.87}, {"time": 0.1333, "angle": 35.69, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.1667, "angle": 93.28, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 89.63, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -9.34, "curve": "stepped"}, {"time": 0.6, "angle": -9.34, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 9.46, "curve": 0.315, "c2": 0.28, "c3": 0.673, "c4": 0.69}, {"time": 1.2667, "angle": 3.37}], "translate": [{"curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.2667}], "scale": [{"curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.8667}]}, "bone24": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -23.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 41.19, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -47.44, "curve": "stepped"}, {"time": 0.6, "angle": -47.44, "curve": 0.25, "c3": 0.75}, {"time": 0.8667}], "translate": [{"y": -1.44, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 12.83, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 7.12, "y": -4.9, "curve": 0.25, "c3": 0.75}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.8667}]}, "bone49": {"rotate": [{"angle": 1.78, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8667, "angle": 3.41, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 1.2667, "angle": 1.78}], "translate": [{"curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.2667}], "scale": [{"curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.8667}]}, "bone53": {"rotate": [{"curve": "stepped"}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 20.39, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -76.2, "curve": "stepped"}, {"time": 0.6, "angle": -76.2, "curve": 0.25, "c3": 0.75}, {"time": 0.8667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 96.88, "y": 343.83, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -135.72, "y": 143.17, "curve": "stepped"}, {"time": 0.6, "x": -135.72, "y": 143.17, "curve": 0.25, "c3": 0.75}, {"time": 0.8667}]}, "tx4": {"rotate": [{"time": 0.3667, "angle": -23.21}], "translate": [{"time": 0.3667, "x": -113.77, "y": 11.01}]}}, "events": [{"time": 0.4333, "name": "atk"}]}, "boss_idle": {"slots": {"eye": {"attachment": [{"time": 0.8333, "name": "eye"}, {"time": 1, "name": null}]}}, "bones": {"bone54": {"rotate": [{}, {"time": 0.6667, "angle": -120}, {"time": 1.3333, "angle": 120}, {"time": 2}]}, "bone31": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 1.85, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone32": {"rotate": [{"angle": 0.24, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 1.85, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2, "angle": 0.24}]}, "bone33": {"rotate": [{"angle": 0.52, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.85, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 0.52}]}, "bone34": {"rotate": [{"angle": 0.92, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 1.85, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 0.92}]}, "bone39": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 3.55, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone40": {"rotate": [{"angle": 0.34, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 3.55, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 0.34}]}, "bone43": {"rotate": [{"angle": 0.59, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 3.55, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 2, "angle": 0.59}]}, "bone42": {"rotate": [{"angle": 1.78, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 3.55, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 1.78}]}, "bone41": {"rotate": [{"angle": 1.01, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 3.55, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 1.01}]}, "bone44": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 3.55, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone45": {"rotate": [{"angle": 0.34, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 3.55, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 0.34}]}, "bone48": {"rotate": [{"angle": 0.59, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 3.55, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 2, "angle": 0.59}]}, "bone47": {"rotate": [{"angle": 0.59, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 3.55, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 2, "angle": 0.59}]}, "bone49": {"rotate": [{"angle": 1.78, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 3.55, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 1.78}]}, "bone46": {"rotate": [{"angle": 1.01, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 3.55, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 1.01}]}, "bone51": {"rotate": [{"angle": 1.78, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 3.55, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 1.78}]}, "bone52": {"rotate": [{"angle": 2.55, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 3.55, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 2.55}]}, "bone50": {"rotate": [{"angle": 2.55, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 3.55, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 2.55}]}, "bone35": {"rotate": [{"angle": 3.37, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 11.89, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 3.37}]}, "bone36": {"rotate": [{"angle": 7.52, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 11.89, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "angle": 7.52}]}, "bone37": {"rotate": [{"angle": -3.16, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -11.15, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -3.16}]}, "bone38": {"rotate": [{"angle": -7.05, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": -11.15, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "angle": -7.05}]}}}, "die": {"slots": {"shuren8": {"color": [{"time": 0.1667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "989898ff"}]}, "shuren10": {"color": [{"time": 0.1667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "989898ff"}]}, "shuren02": {"color": [{"time": 0.1667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "989898ff"}]}, "shuren05": {"color": [{"time": 0.1667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "989898ff"}]}, "eye": {"attachment": [{"time": 0.1667, "name": "eye"}]}, "shuren03": {"color": [{"time": 0.1667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "989898ff"}]}, "shuren9": {"color": [{"time": 0.1667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "989898ff"}]}, "shuren06": {"color": [{"time": 0.1667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "989898ff"}]}}, "bones": {"root": {"rotate": [{}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone54": {"rotate": [{}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone24": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 87.06, "curve": "stepped"}, {"time": 0.4, "angle": 87.06}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone25": {"rotate": [{"angle": 4.5}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone26": {"rotate": [{"angle": -2.94}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone27": {"rotate": [{"angle": -2.57}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone28": {"rotate": [{"angle": 2.77}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone29": {"rotate": [{"angle": -1.74}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone30": {"rotate": [{"angle": -5.56}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone31": {"rotate": [{}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone32": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 6.29}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -11.51, "y": 21.23}], "scale": [{}], "shear": [{}]}, "bone33": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -17.84}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -14.35, "y": -15.94}], "scale": [{}], "shear": [{}]}, "bone34": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -15.35}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone35": {"rotate": [{"curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.1333, "angle": 19.62, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.2333, "angle": -56.34}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone36": {"rotate": [{"curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.1333, "angle": -76.58, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.2333, "angle": -89.33}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone37": {"rotate": [{"curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.1333, "angle": 114.64, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.2333, "angle": 63.32}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone38": {"rotate": [{"curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.1333, "angle": 173.69, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.2333, "angle": -84.83}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone39": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 48.87}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone40": {"rotate": [{}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone41": {"rotate": [{}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone42": {"rotate": [{}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone43": {"rotate": [{}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone44": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -66.08}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone45": {"rotate": [{}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone46": {"rotate": [{}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone47": {"rotate": [{}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone48": {"rotate": [{}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone49": {"rotate": [{}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone50": {"rotate": [{}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone51": {"rotate": [{}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone52": {"rotate": [{}], "translate": [{}], "scale": [{}], "shear": [{}]}, "chongji": {"rotate": [{}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone53": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 87.06, "curve": "stepped"}, {"time": 0.4, "angle": 87.06}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "x": 83.52, "y": 134.48, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2333, "x": 167.04, "y": 116.93, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3, "x": 167.04, "y": 142.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4, "x": 167.04, "y": 116.93}], "scale": [{}], "shear": [{}]}, "jio2": {"rotate": [{}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 44.48, "y": 27.04}], "scale": [{}], "shear": [{}]}, "jio3": {"rotate": [{}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -53.52, "y": 40.23}], "scale": [{}], "shear": [{}]}}}, "hurt": {"slots": {"eye": {"attachment": [{"time": 0.0667, "name": "eye"}, {"time": 0.2667, "name": null}]}}, "bones": {"root": {"rotate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}]}, "bone54": {"rotate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1, "x": 13.19, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}]}, "bone24": {"rotate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.1, "x": 14.87, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}]}, "bone25": {"rotate": [{"angle": 4.5, "curve": "stepped"}, {"time": 0.2333, "angle": 4.5, "curve": "stepped"}, {"time": 0.3333, "angle": 4.5}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}]}, "bone26": {"rotate": [{"angle": -2.94, "curve": "stepped"}, {"time": 0.2333, "angle": -2.94, "curve": "stepped"}, {"time": 0.3333, "angle": -2.94}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}]}, "bone27": {"rotate": [{"angle": -2.57, "curve": "stepped"}, {"time": 0.2333, "angle": -2.57, "curve": "stepped"}, {"time": 0.3333, "angle": -2.57}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}]}, "bone28": {"rotate": [{"angle": 2.77, "curve": "stepped"}, {"time": 0.2333, "angle": 2.77, "curve": "stepped"}, {"time": 0.3333, "angle": 2.77}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}]}, "bone29": {"rotate": [{"angle": -1.74, "curve": "stepped"}, {"time": 0.2333, "angle": -1.74, "curve": "stepped"}, {"time": 0.3333, "angle": -1.74}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}]}, "bone30": {"rotate": [{"angle": -5.56, "curve": "stepped"}, {"time": 0.2333, "angle": -5.56, "curve": "stepped"}, {"time": 0.3333, "angle": -5.56}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}]}, "bone31": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1, "angle": 9.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}]}, "bone32": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1, "angle": 9.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}]}, "bone33": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1, "angle": 9.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}]}, "bone34": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1, "angle": 9.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}]}, "bone35": {"rotate": [{"curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.1, "angle": 16.79, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}]}, "bone36": {"rotate": [{"curve": "stepped"}, {"time": 0.0667, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.1667, "angle": 16.79, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.3, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.3333}]}, "bone37": {"rotate": [{"curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.1, "angle": 16.79, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}]}, "bone38": {"rotate": [{"curve": "stepped"}, {"time": 0.0667, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.1667, "angle": 16.79, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.3, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.3333}]}, "bone39": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 11.97, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}]}, "bone40": {"rotate": [{"curve": "stepped"}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 11.97, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.3333}]}, "bone41": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 11.97, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}]}, "bone42": {"rotate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}]}, "bone43": {"rotate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}]}, "bone44": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 11.97, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}]}, "bone45": {"rotate": [{"curve": "stepped"}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 11.97, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.3333}]}, "bone46": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 11.97, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}]}, "bone47": {"rotate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}]}, "bone48": {"rotate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}]}, "bone49": {"rotate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}]}, "bone50": {"rotate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}]}, "bone51": {"rotate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}]}, "bone52": {"rotate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}]}, "tx4": {"rotate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}]}, "chongji": {"rotate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333}]}}}}}