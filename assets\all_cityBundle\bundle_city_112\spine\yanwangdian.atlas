
yanwangdian.png
size: 512,494
format: RGBA8888
filter: Linear,Linear
repeat: none
1d1
  rotate: 180
  xy: 217, 15
  size: 243, 101
  orig: 245, 102
  offset: 0, 1
  index: -1
1f1
  rotate: false
  xy: 142, 144
  size: 57, 27
  orig: 57, 27
  offset: 0, 0
  index: -1
1s2
  rotate: true
  xy: 447, 55
  size: 198, 48
  orig: 198, 48
  offset: 0, 0
  index: -1
1z1
  rotate: true
  xy: 376, 347
  size: 145, 122
  orig: 145, 122
  offset: 0, 0
  index: -1
1z2
  rotate: false
  xy: 106, 12
  size: 106, 94
  orig: 106, 94
  offset: 0, 0
  index: -1
2d2
  rotate: 180
  xy: 177, 102
  size: 240, 121
  orig: 249, 126
  offset: 0, 5
  index: -1
2f2
  rotate: false
  xy: 305, 213
  size: 57, 27
  orig: 57, 27
  offset: 0, 0
  index: -1
2zy1
  rotate: false
  xy: 2, 267
  size: 38, 109
  orig: 38, 109
  offset: 0, 0
  index: -1
2zz2
  rotate: false
  xy: 2, 22
  size: 102, 99
  orig: 102, 99
  offset: 0, 0
  index: -1
3d1
  rotate: false
  xy: 241, 143
  size: 237, 177
  orig: 238, 178
  offset: 1, 1
  index: -1
3s2
  rotate: false
  xy: 87, 339
  size: 68, 44
  orig: 68, 44
  offset: 0, 0
  index: -1
4s2
  rotate: false
  xy: 87, 339
  size: 68, 44
  orig: 68, 44
  offset: 0, 0
  index: -1
3z2
  rotate: false
  xy: 234, 321
  size: 140, 130
  orig: 140, 130
  offset: 0, 0
  index: -1
4d1
  rotate: 180
  xy: 32, 218
  size: 234, 126
  orig: 238, 127
  offset: 3, 1
  index: -1
d1
  rotate: false
  xy: 272, 456
  size: 95, 37
  orig: 95, 37
  offset: 0, 0
  index: -1
f1
  rotate: false
  xy: 142, 173
  size: 68, 43
  orig: 68, 43
  offset: 0, 0
  index: -1
f2
  rotate: false
  xy: 136, 310
  size: 36, 27
  orig: 36, 27
  offset: 0, 0
  index: -1
g3
  rotate: false
  xy: 2, 476
  size: 17, 17
  orig: 17, 17
  offset: 0, 0
  index: -1
h1
  rotate: false
  xy: 2, 124
  size: 138, 95
  orig: 138, 95
  offset: 0, 0
  index: -1
lizi12
  rotate: false
  xy: 6, 473
  size: 2, 2
  orig: 2, 2
  offset: 0, 0
  index: -1
lizi31
  rotate: false
  xy: 2, 472
  size: 2, 2
  orig: 2, 2
  offset: 0, 0
  index: -1
liziyun4
  rotate: false
  xy: 21, 486
  size: 6, 6
  orig: 6, 6
  offset: 0, 0
  index: -1
s1
  rotate: false
  xy: 2, 332
  size: 268, 161
  orig: 269, 166
  offset: 1, 4
  index: -1
z1
  rotate: true
  xy: 461, 3
  size: 50, 48
  orig: 50, 48
  offset: 0, 0
  index: -1

yanwangdian2.png
size: 498,294
format: RGBA8888
filter: Linear,Linear
repeat: none
1z3
  rotate: false
  xy: 401, 38
  size: 73, 73
  orig: 73, 73
  offset: 0, 0
  index: -1
2s3
  rotate: false
  xy: 2, 244
  size: 198, 48
  orig: 198, 48
  offset: 0, 0
  index: -1
2zz1
  rotate: true
  xy: 2, 97
  size: 63, 106
  orig: 64, 106
  offset: 0, 0
  index: -1
3f1
  rotate: false
  xy: 2, 33
  size: 70, 62
  orig: 70, 62
  offset: 0, 0
  index: -1
3s1
  rotate: false
  xy: 74, 28
  size: 74, 38
  orig: 74, 38
  offset: 0, 0
  index: -1
4s1
  rotate: false
  xy: 74, 28
  size: 74, 38
  orig: 74, 38
  offset: 0, 0
  index: -1
3z1
  rotate: false
  xy: 403, 200
  size: 92, 92
  orig: 92, 92
  offset: 0, 0
  index: -1
4f1
  rotate: true
  xy: 308, 37
  size: 69, 72
  orig: 69, 72
  offset: 0, 0
  index: -1
4z1
  rotate: true
  xy: 110, 68
  size: 59, 99
  orig: 59, 105
  offset: 0, 0
  index: -1
4z2
  rotate: true
  xy: 2, 162
  size: 80, 108
  orig: 80, 108
  offset: 0, 0
  index: -1
4z3
  rotate: true
  xy: 202, 201
  size: 91, 102
  orig: 92, 102
  offset: 1, 0
  index: -1
4z4
  rotate: false
  xy: 403, 113
  size: 90, 85
  orig: 90, 85
  offset: 0, 0
  index: -1
C112-0
  rotate: false
  xy: 190, 129
  size: 120, 66
  orig: 133, 133
  offset: 8, 0
  index: -1
h2
  rotate: false
  xy: 211, 71
  size: 95, 57
  orig: 95, 57
  offset: 0, 0
  index: -1
m1
  rotate: false
  xy: 211, 3
  size: 66, 65
  orig: 66, 65
  offset: 0, 0
  index: -1
q1
  rotate: true
  xy: 306, 197
  size: 95, 95
  orig: 95, 95
  offset: 0, 0
  index: -1
q2
  rotate: false
  xy: 312, 108
  size: 87, 87
  orig: 87, 87
  offset: 0, 0
  index: -1
y1
  rotate: false
  xy: 150, 10
  size: 49, 56
  orig: 49, 56
  offset: 0, 0
  index: -1
z2
  rotate: true
  xy: 112, 132
  size: 110, 76
  orig: 110, 76
  offset: 0, 0
  index: -1
