import { sys, director } from "cc";
import ISocket from "./ISocket";
import { ExternalMessage } from "../../game/net/protocol/ExternalMessage";
import { SysConfig } from "../common/SysConfig";
import TickerMgr from "../ticker/TickerMgr";
import CmdMgr from "../../game/mgr/CmdMgr";
import { UIMgr } from "../ui/UIMgr";
import { PlayerRouteName } from "../../module/player/PlayerConstant";
import { TipsMgr } from "../../../platform/src/TipsHelper";
import { PlayerSubCmd } from "../../game/net/cmd/CmdData";
import { TimeUtils } from "../utils/TimeUtils";
import { HeartBeatMaxSec, MaxHeatBeatLostTimes } from "../../game/GameDefine";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.INFO);
// Socket连接状态定义，与 https://developer.mozilla.org/zh-CN/docs/Web/API/WebSocket/readyState 相同
// 后期根据自己需要在加状态
enum WsStateEnum {
  Connecting,
  Open,
  Error,
  Closing,
  Closed,
}

export default class SocketClient implements ISocket {
  private static _ins: SocketClient = null;

  //获得实例对象
  public static get ins(): SocketClient {
    return this._ins == null ? (this._ins = new SocketClient()) : this._ins;
  }

  private constructor() {
    this.socketState = WsStateEnum.Closed;
    this.webSocket = null;
  }

  private _apiHandlerInstance: any = null;
  public set apiHandlerInstance(value: any) {
    this._apiHandlerInstance = value;
  }

  private wsUrl: string;
  private reTryTime: number = 0;
  public socketState: WsStateEnum = WsStateEnum.Closed;
  public webSocket: any = null;
  public recvBuf: Uint8Array; //接收缓冲

  // 心跳最后成功时间
  private _lastHeartSuccessTime = 0;

  private _heartBeatLostTimes = 0;

  public get lastHeartSuccessTime() {
    return this._lastHeartSuccessTime;
  }

  // 心跳
  public set lastHeartSuccessTime(value) {
    this._lastHeartSuccessTime = value;
  }

  public get isOpen() {
    return this.socketState == WsStateEnum.Open;
  }

  private resetWebsocket() {
    if (!this.webSocket) {
      return;
    }
    if (sys.platform == sys.Platform.WECHAT_GAME) {
      this.webSocket.onClose(null);
      this.webSocket.onError(null);
      this.webSocket.onMessage(null);
      this.webSocket.onOpen(null);
    } else {
      this.webSocket.binaryType = "arraybuffer";
      this.webSocket.onclose = null;
      this.webSocket.onerror = null;
      this.webSocket.onmessage = null;
      this.webSocket.onopen = null;
    }
  }

  public async connect(ws: string = SysConfig.wsUrl): Promise<void> {
    log.info("发起连接", this.socketState);
    if (this.socketState == WsStateEnum.Connecting) {
      return;
    }

    if (this.socketState != WsStateEnum.Open) {
      this.resetWebsocket();
      this.socketState = WsStateEnum.Connecting;
      this.wsUrl = ws;
      log.info("连接中。。。");
      if (sys.platform == sys.Platform.WECHAT_GAME) {
        this.webSocket = wx.connectSocket({
          url: ws,
          timeout: 7000,
          header: {
            "content-type": "application/octet-stream",
          },
        });
        this.webSocket.onClose(this.onclose);
        this.webSocket.onError(this.onerror);
        this.webSocket.onMessage(this.onmessage);
        this.webSocket.onOpen(this.onopen);
      } else {
        this.webSocket = new WebSocket(ws);
        this.webSocket.binaryType = "arraybuffer";
        this.webSocket.onclose = this.onclose.bind(this);
        this.webSocket.onerror = this.onerror.bind(this);
        this.webSocket.onmessage = this.onmessage.bind(this);
        this.webSocket.onopen = this.onopen.bind(this);
      }

      // 超时就重置
      setTimeout(() => {
        if (this.socketState == WsStateEnum.Connecting) {
          this.socketState = WsStateEnum.Closed;
          log.error("连接超时，60秒未连接成功");
        }
      }, 60000);
    } else {
      this.onopen(null);
    }
  }

  public onOpenCallback() {
    log.info("onOpenCallback not init");
  }

  public onopen(event: Event): void {
    SocketClient.ins.socketState = WsStateEnum.Open;
    SocketClient.ins.reTryTime = 10;
    SocketClient.ins.onOpenCallback?.();
    log.info("连接成功");
    this._lastHeartSuccessTime = TimeUtils.serverTime;
    this._heartBeatLostTimes = 0;
  }

  //
  public testClose() {
    this.webSocket.close(4000);
  }

  //发送消息
  public send(mergeCmd: number, pbBuff: Uint8Array): boolean {
    if (this.socketState == WsStateEnum.Connecting) {
      log.error("连接中，不能发送消息");
      return false;
    }

    if (!SocketClient.ins.isOpen) {
      this.goLogin("连接未打开" + this.socketState);
      return;
    }

    if (!SocketClient.ins.webSocket) {
      this.goLogin("webSocket 连接未打开");
      return;
    }

    if (TimeUtils.serverTime - this._lastHeartSuccessTime > HeartBeatMaxSec) {
      log.warn("心跳时间过长", TimeUtils.serverTime, this._lastHeartSuccessTime);
      this.socketState = WsStateEnum.Error;
      if (this.webSocket.readyState === WebSocket.OPEN) {
        this.webSocket.close(4000);
        log.warn("心跳时间-关闭websocket");
      } else {
        SocketClient.ins.connect(SocketClient.ins.wsUrl);
      }
      return false;
    }

    if (this._heartBeatLostTimes > MaxHeatBeatLostTimes) {
      // 重连
      this.socketState = WsStateEnum.Error;
      if (this.webSocket.readyState === WebSocket.OPEN) {
        this.webSocket.close(4000);
        log.error("心跳丢失-关闭websocket");
      } else {
        SocketClient.ins.connect(SocketClient.ins.wsUrl);
      }
      return false;
    }

    if (mergeCmd == PlayerSubCmd.updateEnergy) {
      // 每次发送次数加1，接收到心跳就重置
      this._heartBeatLostTimes++;
    }

    // 正常连接发送
    if (!pbBuff) {
      log.error("没有数据");
      return false;
    }
    if (sys.platform == sys.Platform.WECHAT_GAME) {
      SocketClient.ins.webSocket.send({
        data: pbBuff.buffer.slice(pbBuff.byteOffset, pbBuff.byteOffset + pbBuff.length),
      });
    } else {
      SocketClient.ins.webSocket.send(pbBuff);
    }
    return true;
  }

  public onmessage(event: MessageEvent): void {
    let recvData = new Uint8Array(<ArrayBuffer>event.data);
    // log.info("收到消息", recvData);
    let data: ExternalMessage = ExternalMessage.decode(recvData);

    if (data.cmdMerge == PlayerSubCmd.updateEnergy) {
      // 心跳包
      this._heartBeatLostTimes = 0;
    }

    // 心跳最后成功时间记录
    this._lastHeartSuccessTime = TimeUtils.serverTime;
    if (this._apiHandlerInstance.handleMessage(data)) {
      return;
    }

    if (data.responseStatus === 1007) {
      TipsMgr.showTipX(data.responseStatus, (data.validMsg || "").split("#"));
      UIMgr.clear();
      director.loadScene("SceneLogin");
    } else if (data.responseStatus == 10001) {
      let msg = data.validMsg;
      let list = msg.split("-");

      let itemId = 0;
      if (list.length > 0) {
        itemId = Number(list[0]);
        UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
          itemId: itemId,
        });
      }
    } else if (data.responseStatus != 0) {
      TipsMgr.showErrX(data.responseStatus, (data.validMsg || "").split["#"], data.validMsg);
      log.error(
        `[${CmdMgr.getCmd(data.cmdMerge)}-${CmdMgr.getSubCmd(data.cmdMerge)}] 服务端错误 ${data.responseStatus}`,
        data.validMsg
      );
    } else {
      log.warn(`[${CmdMgr.getCmd(data.cmdMerge)}-${CmdMgr.getSubCmd(data.cmdMerge)}] 接口未实现`);
    }
  }

  //关闭close
  public close(): void {
    SocketClient.ins.socketState = WsStateEnum.Closed;
    SocketClient.ins.reTryTime = 0;

    if (SocketClient.ins.webSocket) SocketClient.ins.webSocket.close();
  }

  public onclose(event: CloseEvent): void {
    log.info("onclose", event);
    SocketClient.ins.socketState = WsStateEnum.Closed;
    if ([1000, 1006, 4000].includes(event.code)) {
      // 服务器无法连接
      this.goLogin("1000, 1006, 4000");
      return;
    }

    if (SocketClient.ins.reTryTime > 0) {
      SocketClient.ins.reTryTime--;
      log.info("断线重连中，剩余次数" + SocketClient.ins.reTryTime);
      SocketClient.ins.connect(SocketClient.ins.wsUrl);
    } else {
      this.goLogin("重连超过最大重试次数");
      return;
    }
  }

  public onerror(event: Event): void {
    // TipMgr.showTip("断线了3");
    log.error(event);
    SocketClient.ins.socketState = WsStateEnum.Closed;
  }

  private goLogin(desc: string) {
    log.info("返回登录页面", desc);
    UIMgr.clear();
    TickerMgr.clearAllTicker();
    director.loadScene("SceneLogin");
  }
}
