import { GameData } from "../../game/GameData";
import { Net_Code } from "../../game/mgr/ApiHandler";
import { ClubFormMessage } from "../../game/net/protocol/Club";
import data from "../../lib/data/data";
import { ClubApi } from "./ClubApi";
import { ClubConfig } from "./ClubConfig";
import { ClubData } from "./ClubData";
import { ClubRoute } from "./ClubRoute";
import { ClubService } from "./ClubService";
import { ClubSubscriber } from "./ClubSubscriber";
import { ClubViewModel } from "./ClubViewModel";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class ClubModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): ClubModule {
    if (!GameData.instance.ClubModule) {
      GameData.instance.ClubModule = new ClubModule();
    }
    return GameData.instance.ClubModule;
  }

  private _subscriber = new ClubSubscriber();
  private _data = new ClubData();
  private _api = new ClubApi();
  private _config = new ClubConfig();
  private _route = new ClubRoute();
  private _viewModel = new ClubViewModel();
  private _service = new ClubService();

  public static get data() {
    return this.instance._data;
  }
  public static get api() {
    return this.instance._api;
  }
  public static get config() {
    return this.instance._config;
  }
  public static get viewModel() {
    return this.instance._viewModel;
  }
  public static get service() {
    return this.instance._service;
  }

  public init(data?: any, completedCallback?: Function) {
    if (this._subscriber) {
      this._subscriber.unRegister();
    }
    this._subscriber = new ClubSubscriber();
    this._data = new ClubData();
    this._api = new ClubApi();
    this._config = new ClubConfig();
    this._route = new ClubRoute();
    this._viewModel = new ClubViewModel();
    this._service = new ClubService();

    // 模块初始化
    this._route.init();
    this._data.init();
    this._subscriber.register();

    // 模块数据初始化
    ClubModule.api.ownClub(
      (data: ClubFormMessage) => {
        completedCallback && completedCallback();
        ClubModule.service.init();
        if (data?.clubMessage) {
          ClubModule.api.getBossTrain();
        }
      },
      (errorCode: Net_Code, msg: string[], data: any) => {
        completedCallback && completedCallback();
        log.error(msg);
        return false;
      }
    );
  }

  protected saveKey(): string {
    return this.constructor.name;
  }
}
