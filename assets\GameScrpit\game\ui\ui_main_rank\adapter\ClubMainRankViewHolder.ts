import { instantiate, Node, _decorator, Label, RichText, math, Sprite } from "cc";
import { ListAdapter, ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { PlayerRankMessage, PlayerSimpleMessage } from "../../../net/protocol/Player";
import FmUtils from "db://assets/GameScrpit/lib/utils/FmUtils";
import Formate from "db://assets/GameScrpit/lib/utils/Formate";
import { PlayerModule } from "db://assets/GameScrpit/module/player/PlayerModule";
import { ClubRankMessage, ClubSimplePowerMessage } from "../../../net/protocol/Club";
import { UIClubAssistList } from "../../club/UIClubAssistList";
import { UIClubAvatar } from "../../club/UIClubAvatar";
const { ccclass, property } = _decorator;

@ccclass("ClubMainRankViewHolder")
export class ClubMainRankViewHolder extends ViewHolder {
  public updateData(data: ClubSimplePowerMessage, type: number) {
    //

    if (this.position % 2 == 0) {
      this.node.getComponent(Sprite).color = math.color("#d7f1ff");
    } else {
      this.node.getComponent(Sprite).color = math.color("#bedff6");
    }

    this.getNode("node_rank1").active = false;
    this.getNode("node_rank2").active = false;
    this.getNode("node_rank3").active = false;
    this.getNode("lbl_rank").active = false;
    if (this.position == 0) {
      this.getNode("node_rank1").active = true;
    } else if (this.position == 1) {
      this.getNode("node_rank2").active = true;
    } else if (this.position == 2) {
      this.getNode("node_rank3").active = true;
    } else {
      this.getNode("lbl_rank").active = true;
      this.getNode("lbl_rank").getComponent(Label).string = (this.position + 1).toString();
    }
    this.getNode("lbl_name").getComponent(Label).string = data.name;

    if (type == 1) {
      // 本服
      this.getNode("lbl_rank_val").getComponent(Label).string = Formate.format(data.totalPower);
      this.getNode("rich_club_lv").getComponent(RichText).string = `LV.${data.level}`;
    } else {
      // 跨服
    }
    this.getNode("UIClubAvatar").getComponent(UIClubAvatar).setAvatar(data.avatar);
  }
}

export class ClubMainRankAdapter extends ListAdapter {
  private _type: number;
  private _item: Node;
  private _data: ClubSimplePowerMessage[] = [];
  constructor(type: number, item: Node) {
    super();
    this._type = type;
    this._item = item;
  }
  setData(data: ClubSimplePowerMessage[]) {
    this._data = data;
    this.notifyDataSetChanged();
  }
  onCreateView(viewType: number): Node {
    let item = instantiate(this._item);
    item.active = true;
    return item;
  }
  onBindData(node: Node, position: number): void {
    node.getComponent(ClubMainRankViewHolder).updateData(this._data[position], this._type);
  }
  getCount(): number {
    return this._data.length;
  }
}
