syntax = "proto3";
package sim;

// 
message GoodsAddRequest {
  int64 resId = 1;
  int32 num = 2;
}

// 
message GoodsRedeemMessage {
  // 是否有购买过商城商店-仙玉
  repeated int64 virtualMoneySet = 1;
  // 总的兑换物料ID和兑换次数
  map<int64,int32> redeemMap = 2;
}

// 
message GoodsRedeemRequest {
  // 兑换的商品主键ID
  int64 redeemId = 1;
  // 购买数量
  int32 count = 2;
}

// 
message GoodsRedeemResponse {
  // 兑换情况
  GoodsRedeemMessage redeemMessage = 1;
  // 兑换后得到的道具
  repeated double rewardList = 2;
}

