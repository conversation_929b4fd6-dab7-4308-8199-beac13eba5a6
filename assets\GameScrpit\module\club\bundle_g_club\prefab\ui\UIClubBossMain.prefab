[{"__type__": "cc.Prefab", "_name": "UIClubBossMain", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "UIClubBossMain", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 273}, {"__id__": 321}, {"__id__": 433}], "_active": true, "_components": [{"__id__": 645}], "_prefab": {"__id__": 647}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 3}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 2}, "asset": {"__uuid__": "83425a7d-905e-4982-b252-c50b3e262134", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 4}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "901oXuLrVPTb7WtdedPjYg", "prefabRootNode": {"__id__": 1}, "mountedChildren": [{"__id__": 5}], "mountedComponents": [], "propertyOverrides": [{"__id__": 227}, {"__id__": 229}, {"__id__": 230}, {"__id__": 231}, {"__id__": 232}, {"__id__": 234}, {"__id__": 236}, {"__id__": 238}, {"__id__": 240}, {"__id__": 242}, {"__id__": 244}, {"__id__": 246}, {"__id__": 248}, {"__id__": 249}, {"__id__": 251}, {"__id__": 253}, {"__id__": 254}, {"__id__": 256}, {"__id__": 258}], "removedComponents": [{"__id__": 259}, {"__id__": 260}, {"__id__": 261}, {"__id__": 262}, {"__id__": 263}, {"__id__": 264}, {"__id__": 265}, {"__id__": 266}, {"__id__": 267}, {"__id__": 268}, {"__id__": 269}, {"__id__": 270}, {"__id__": 271}, {"__id__": 272}]}, {"__type__": "cc.MountedChildrenInfo", "targetInfo": {"__id__": 6}, "nodes": [{"__id__": 7}]}, {"__type__": "cc.TargetInfo", "localID": ["9ffoAr1CFKQoFFCc+XwXS3"]}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 2}}, "_parent": {"__id__": 8}, "_children": [{"__id__": 18}, {"__id__": 96}, {"__id__": 124}, {"__id__": 132}, {"__id__": 198}], "_active": true, "_components": [{"__id__": 222}, {"__id__": 224}], "_prefab": {"__id__": 226}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "dialog_content", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 7}], "_active": true, "_components": [{"__id__": 9}, {"__id__": 11}, {"__id__": 13}, {"__id__": 15}], "_prefab": {"__id__": 17}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -76.37349999999992, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 8}, "_enabled": true, "__prefab": {"__id__": 10}, "_contentSize": {"__type__": "cc.Size", "width": 682, "height": 1168.1890000000003}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eaWQ3o6/tFYq6wOv7pEwoG"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 8}, "_enabled": true, "__prefab": {"__id__": 12}, "_alignFlags": 45, "_target": null, "_left": 33, "_right": 33, "_top": 169.27899999999988, "_bottom": 16.531999999999954, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dbRQCjte9Ggq+1E5cOcWvh"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 8}, "_enabled": true, "__prefab": {"__id__": 14}, "_type": 2, "_inverted": false, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "20uWiQq9FLEYGXjb7BwnRi"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 8}, "_enabled": true, "__prefab": {"__id__": 16}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "64zm3XnLdEhI5aGLaZp1vO"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9ffoAr1CFKQoFFCc+XwXS3", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_boss_list", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 7}, "_children": [{"__id__": 19}], "_active": true, "_components": [{"__id__": 85}, {"__id__": 87}, {"__id__": 89}, {"__id__": 91}, {"__id__": 93}], "_prefab": {"__id__": 95}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 18}, "_children": [{"__id__": 20}], "_active": true, "_components": [{"__id__": 80}, {"__id__": 82}], "_prefab": {"__id__": 84}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -584.0945000000002, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "B<PERSON>_zhanmeng_boss", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 19}, "_children": [{"__id__": 21}, {"__id__": 25}, {"__id__": 29}, {"__id__": 33}, {"__id__": 37}, {"__id__": 41}, {"__id__": 45}, {"__id__": 51}, {"__id__": 57}, {"__id__": 63}, {"__id__": 69}], "_active": true, "_components": [{"__id__": 75}, {"__id__": 77}], "_prefab": {"__id__": 79}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "boss_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [], "_active": true, "_components": [{"__id__": 22}], "_prefab": {"__id__": 24}, "_lpos": {"__type__": "cc.Vec3", "x": -155.846, "y": 252.23, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 21}, "_enabled": true, "__prefab": {"__id__": 23}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "39u0Jt/J1GWb2XBXKljpm6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "57qmKSRzFB8Ir/FYt2T906", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "boss_2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [], "_active": true, "_components": [{"__id__": 26}], "_prefab": {"__id__": 28}, "_lpos": {"__type__": "cc.Vec3", "x": 146.154, "y": 518.23, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": {"__id__": 27}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fb6iH/WS5O9alVnSiwp5x0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f2fBiM/49A9afPeeXX3/Hw", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "boss_3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [], "_active": true, "_components": [{"__id__": 30}], "_prefab": {"__id__": 32}, "_lpos": {"__type__": "cc.Vec3", "x": -159.846, "y": 868.23, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 29}, "_enabled": true, "__prefab": {"__id__": 31}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "30oM8holpEfaFLUT20Dc9d"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3d9ksuiKtGAYy+d+Z+emKF", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "boss_4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [], "_active": true, "_components": [{"__id__": 34}], "_prefab": {"__id__": 36}, "_lpos": {"__type__": "cc.Vec3", "x": 146.154, "y": 1162.23, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 33}, "_enabled": true, "__prefab": {"__id__": 35}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0e00dwhtFL96rez+lobI6A"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "67cW6QdNhGcqk7L2JW0fU3", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "boss_5", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [], "_active": true, "_components": [{"__id__": 38}], "_prefab": {"__id__": 40}, "_lpos": {"__type__": "cc.Vec3", "x": -140.846, "y": 1567.824, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 37}, "_enabled": true, "__prefab": {"__id__": 39}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a0RIYIVgVM/I5CcLL4CiHs"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ddlPlYp4pDL4ETV9NRT7ZJ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "boss_6", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [], "_active": true, "_components": [{"__id__": 42}], "_prefab": {"__id__": 44}, "_lpos": {"__type__": "cc.Vec3", "x": 146.154, "y": 1928.733, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 41}, "_enabled": true, "__prefab": {"__id__": 43}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3dtMRM4sROS79W5xmezuQi"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "71gFyl2E5FJLEo+8h+5wYi", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "zm_line_luxian", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [], "_active": true, "_components": [{"__id__": 46}, {"__id__": 48}], "_prefab": {"__id__": 50}, "_lpos": {"__type__": "cc.Vec3", "x": -32.308, "y": 380.437, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 45}, "_enabled": true, "__prefab": {"__id__": 47}, "_contentSize": {"__type__": "cc.Size", "width": 63, "height": 94}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9fth7IwV9MoZst+8/jMpIu"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 45}, "_enabled": true, "__prefab": {"__id__": 49}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3cb911eb-e28f-4c29-984f-a311072ad665@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "db7LWx4vVOH7ahA2CnJGAz"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5ersSCAglCYZaIGsMJ8Q2M", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "zm_line_luxian-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [], "_active": true, "_components": [{"__id__": 52}, {"__id__": 54}], "_prefab": {"__id__": 56}, "_lpos": {"__type__": "cc.Vec3", "x": -26.235, "y": 962.106, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 51}, "_enabled": true, "__prefab": {"__id__": 53}, "_contentSize": {"__type__": "cc.Size", "width": 63, "height": 94}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a3MhQeFYhBpbvv/FgU2EQH"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 51}, "_enabled": true, "__prefab": {"__id__": 55}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3cb911eb-e28f-4c29-984f-a311072ad665@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b7jinUrNxFL6K11qfl517i"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e4rOkD5Y9E/7nVAs74hro3", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "zm_line_luxian-002", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [], "_active": true, "_components": [{"__id__": 58}, {"__id__": 60}], "_prefab": {"__id__": 62}, "_lpos": {"__type__": "cc.Vec3", "x": 17.115, "y": 1735.464, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 57}, "_enabled": true, "__prefab": {"__id__": 59}, "_contentSize": {"__type__": "cc.Size", "width": 63, "height": 94}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "73yGqBJRhKNqizjrT3Quz0"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 57}, "_enabled": true, "__prefab": {"__id__": 61}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3cb911eb-e28f-4c29-984f-a311072ad665@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "23UJED3bpJObAWBNaiqxrS"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0dxykEOolFEbSSgne//0Gs", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "zm_line_luxian-003", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [], "_active": true, "_components": [{"__id__": 64}, {"__id__": 66}], "_prefab": {"__id__": 68}, "_lpos": {"__type__": "cc.Vec3", "x": 28.594, "y": 1383.182, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0.573576436351046, "w": 0.8191520442889918}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 70}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 63}, "_enabled": true, "__prefab": {"__id__": 65}, "_contentSize": {"__type__": "cc.Size", "width": 63, "height": 94}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d03Z14GQpFsIerJY73Qp/i"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 63}, "_enabled": true, "__prefab": {"__id__": 67}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3cb911eb-e28f-4c29-984f-a311072ad665@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "23nrj2GiFHjLu3fNpwGxUh"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f7VDt+4PZG8boFiJq02xeQ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "zm_line_luxian-004", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [], "_active": true, "_components": [{"__id__": 70}, {"__id__": 72}], "_prefab": {"__id__": 74}, "_lpos": {"__type__": "cc.Vec3", "x": 28.977, "y": 766.85, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0.573576436351046, "w": 0.8191520442889918}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 70}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 69}, "_enabled": true, "__prefab": {"__id__": 71}, "_contentSize": {"__type__": "cc.Size", "width": 63, "height": 94}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6fXlGPnoZPA5gXRvB0Ydbj"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 69}, "_enabled": true, "__prefab": {"__id__": 73}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3cb911eb-e28f-4c29-984f-a311072ad665@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b6I7eqfPtGlYGZvWHvpbUi"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cc2JjIIo1EBIQReKDj3thr", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 20}, "_enabled": true, "__prefab": {"__id__": 76}, "_contentSize": {"__type__": "cc.Size", "width": 682, "height": 2326}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7bHr147gZAMLDeAR0tWxg9"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 20}, "_enabled": true, "__prefab": {"__id__": 78}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "024ff75e-08ac-4bc1-9a56-6def43d43271@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "51VLsiFy5Ia7/isbX32BCM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c6Tk7C5yBKAZOEO2fslWvU", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": {"__id__": 81}, "_contentSize": {"__type__": "cc.Size", "width": 684, "height": 2326}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "94n1c9vApD54wA3ot967JG"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": {"__id__": 83}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dbm54BQ8JDIKWe5HvoIELj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dbf7v8K8FDyJmm0ZLDpP0E", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 86}, "_contentSize": {"__type__": "cc.Size", "width": 682, "height": 1168.1890000000003}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7bYdG+WSFAqKwdaQ3rvK3X"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 88}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b730527c-3233-41c2-aaf7-7cdab58f9749@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bddBiXA0JOkJe9+aOLCQ5a"}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": false, "__prefab": {"__id__": 90}, "bounceDuration": 0.23, "brake": 0.45, "elastic": false, "inertia": true, "horizontal": false, "vertical": true, "cancelInnerEvents": true, "scrollEvents": [], "_content": {"__id__": 19}, "_horizontalScrollBar": null, "_verticalScrollBar": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f6PdNYtntKx4ex05Qm0P/l"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 92}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 240, "_originalHeight": 250, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eavtChfANI1bT8byoWP/NQ"}, {"__type__": "71013ZuoIZLg7KgCSSgoQcY", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 94}, "content": {"__id__": 19}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c0MKTQaGpArYPDMCdAD31L"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8enMiqGV5JYL03hQvkh5cp", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "tipS", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 7}, "_children": [{"__id__": 97}], "_active": true, "_components": [{"__id__": 119}, {"__id__": 121}], "_prefab": {"__id__": 123}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -551, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 96}, "_children": [{"__id__": 98}, {"__id__": 106}], "_active": true, "_components": [{"__id__": 114}, {"__id__": 116}], "_prefab": {"__id__": 118}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 97}, "_children": [], "_active": true, "_components": [{"__id__": 99}, {"__id__": 101}, {"__id__": 103}], "_prefab": {"__id__": 105}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 98}, "_enabled": true, "__prefab": {"__id__": 100}, "_contentSize": {"__type__": "cc.Size", "width": 125, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c4Pc/bAIBIvJg0aPZUFDHX"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 98}, "_enabled": true, "__prefab": {"__id__": 102}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "挑战次数：", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25, "_fontSize": 25, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b7VNkXgpNEMIXKqLzkgNiG"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 98}, "_enabled": true, "__prefab": {"__id__": 104}, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 1101.8005, "_bottom": 24.8, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b0zIJK5UtLaYcULvoDNBNL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cfC4PR0qhKl6q4v6Imj/bp", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "remain_challenge", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 97}, "_children": [], "_active": true, "_components": [{"__id__": 107}, {"__id__": 109}, {"__id__": 111}], "_prefab": {"__id__": 113}, "_lpos": {"__type__": "cc.Vec3", "x": 62.5, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 106}, "_enabled": true, "__prefab": {"__id__": 108}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aash2B1oBKmbljBaoqYioN"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 106}, "_enabled": true, "__prefab": {"__id__": 110}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 175, "b": 4, "a": 255}, "_string": "", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2d73e/vFtOnqFJuV7n308y"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 106}, "_enabled": true, "__prefab": {"__id__": 112}, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 1101.8005, "_bottom": 24.8, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "64p+3Y1oxIa6f2iXHoHpbg"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "00U2+XNqpCUogYdF8VTlen", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 97}, "_enabled": true, "__prefab": {"__id__": 115}, "_contentSize": {"__type__": "cc.Size", "width": 125, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "65aj6RR2pG+peg9ybMZ/QM"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 97}, "_enabled": true, "__prefab": {"__id__": 117}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fdkhhr9k1EU7RbqFL6eXsM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "80pZ6xp/hLALRus+1qmCtk", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 96}, "_enabled": true, "__prefab": {"__id__": 120}, "_contentSize": {"__type__": "cc.Size", "width": 645, "height": 45}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c6IW2DuvpN6ZTgLSqKGTzJ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 96}, "_enabled": true, "__prefab": {"__id__": 122}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "dfafc098-**************-c4874f4fcf51@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "17GMz61KFPKoCypxNu8598"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1aMIpvwFFF84Y6i6jAEt6L", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_jian<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_bai", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 7}, "_children": [], "_active": true, "_components": [{"__id__": 125}, {"__id__": 127}, {"__id__": 129}], "_prefab": {"__id__": 131}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 483.511, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 1, "w": 0}, "_lscale": {"__type__": "cc.Vec3", "x": -1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": -180, "y": -180, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 124}, "_enabled": true, "__prefab": {"__id__": 126}, "_contentSize": {"__type__": "cc.Size", "width": 688, "height": 201.16700000000003}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9dCuR8S/FAP4McNjDsM63q"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 124}, "_enabled": true, "__prefab": {"__id__": 128}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 233, "g": 221, "b": 194, "a": 255}, "_spriteFrame": {"__uuid__": "61e0d1c3-82f5-4a17-88ee-2c528ee901d0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "58IEbwV9tCoYkvQ4Dbw+LL"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 124}, "_enabled": true, "__prefab": {"__id__": 130}, "_alignFlags": 1, "_target": null, "_left": 0, "_right": 0, "_top": 1.1368683772161603e-13, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d2DcIngwpPA6ZcbjcpNZZg"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f58fzjQtpCfKdDGulqvF7f", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ScrollView", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 7}, "_children": [{"__id__": 133}, {"__id__": 151}], "_active": true, "_components": [{"__id__": 193}, {"__id__": 148}, {"__id__": 195}], "_prefab": {"__id__": 197}, "_lpos": {"__type__": "cc.Vec3", "x": -332, "y": 481.20199999999994, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "scrollBar", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 132}, "_children": [{"__id__": 134}], "_active": false, "_components": [{"__id__": 140}, {"__id__": 142}, {"__id__": 144}, {"__id__": 146}], "_prefab": {"__id__": 192}, "_lpos": {"__type__": "cc.Vec3", "x": 120, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bar", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 133}, "_children": [], "_active": true, "_components": [{"__id__": 135}, {"__id__": 137}], "_prefab": {"__id__": 139}, "_lpos": {"__type__": "cc.Vec3", "x": -11, "y": -31.25, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 134}, "_enabled": true, "__prefab": {"__id__": 136}, "_contentSize": {"__type__": "cc.Size", "width": 10, "height": 156.25}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f7ROHw6GVAjoxjb/4cl6x3"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 134}, "_enabled": true, "__prefab": {"__id__": 138}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_spriteFrame": {"__uuid__": "afc47931-f066-46b0-90be-9fe61f213428@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c5vmewVzlA1oE+J/XQ85Zc"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6aYTNGsqtKHq9wrBZbqqMo", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 133}, "_enabled": true, "__prefab": {"__id__": 141}, "_contentSize": {"__type__": "cc.Size", "width": 12, "height": 250}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2cBeznYu9MArUjiTHcC8N7"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 133}, "_enabled": true, "__prefab": {"__id__": 143}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_spriteFrame": {"__uuid__": "ffb88a8f-af62-48f4-8f1d-3cb606443a43@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5fScoN26JHPoyIJJH1cp2C"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 133}, "_enabled": true, "__prefab": {"__id__": 145}, "_alignFlags": 37, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 250, "_alignMode": 1, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "52Nk9kstlEX5lyae9lN7+0"}, {"__type__": "<PERSON>.<PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 133}, "_enabled": true, "__prefab": {"__id__": 147}, "_scrollView": {"__id__": 148}, "_handle": {"__id__": 137}, "_direction": 1, "_enableAutoHide": false, "_autoHideTime": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c9appZ8xNEOI9+VzOA/J3l"}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 132}, "_enabled": true, "__prefab": {"__id__": 149}, "bounceDuration": 0.23, "brake": 0.75, "elastic": true, "inertia": true, "horizontal": true, "vertical": false, "cancelInnerEvents": true, "scrollEvents": [], "_content": {"__id__": 150}, "_horizontalScrollBar": null, "_verticalScrollBar": {"__id__": 146}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "52ZJ3ZI9BICKmbAZTDShJy"}, {"__type__": "cc.Node", "_name": "task_head", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 151}, "_children": [{"__id__": 161}, {"__id__": 183}], "_active": true, "_components": [{"__id__": 189}], "_prefab": {"__id__": 191}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 132}, "_children": [{"__id__": 150}], "_active": true, "_components": [{"__id__": 152}, {"__id__": 154}, {"__id__": 156}, {"__id__": 158}], "_prefab": {"__id__": 160}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 11.423999999999985, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 151}, "_enabled": true, "__prefab": {"__id__": 153}, "_contentSize": {"__type__": "cc.Size", "width": 666, "height": 140.13699999999997}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "efyuaI56FG5JcG01sh9o2j"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 151}, "_enabled": true, "__prefab": {"__id__": 155}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b2nhNs55pC2qAby7h02W4v"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 151}, "_enabled": true, "__prefab": {"__id__": 157}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "23lBRCzphBx6xK4GubA4v/"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 151}, "_enabled": true, "__prefab": {"__id__": 159}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": -33.59999999999999, "_bottom": -10.752, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 240, "_originalHeight": 250, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4094ztmwJPCqfwckSqSIO0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a9BH44ARJP0ot8nyUdCSfP", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "task_process_bkg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 150}, "_children": [{"__id__": 162}], "_active": true, "_components": [{"__id__": 178}, {"__id__": 180}], "_prefab": {"__id__": 182}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -14, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "task_process", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 161}, "_children": [{"__id__": 163}], "_active": true, "_components": [{"__id__": 171}, {"__id__": 173}, {"__id__": 175}], "_prefab": {"__id__": 177}, "_lpos": {"__type__": "cc.Vec3", "x": 2, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_task_process", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 162}, "_children": [], "_active": true, "_components": [{"__id__": 164}, {"__id__": 166}, {"__id__": 168}], "_prefab": {"__id__": 170}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 163}, "_enabled": true, "__prefab": {"__id__": 165}, "_contentSize": {"__type__": "cc.Size", "width": -4, "height": 11}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c9kV0wnDlPFIU8lf7psPeO"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 163}, "_enabled": true, "__prefab": {"__id__": 167}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bf86acea-74e5-4a6b-917c-92ac4b297221@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0.4, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7fyUrSoNBE4LBL2KhqcuEM"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 163}, "_enabled": true, "__prefab": {"__id__": 169}, "_alignFlags": 45, "_target": {"__id__": 161}, "_left": 2, "_right": 2, "_top": 2, "_bottom": 2, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 509.3, "_originalHeight": 15, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b0b92Soe5IELo4zAOxt0/C"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0bqRHn1KdHrouc26X1DkIA", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 162}, "_enabled": true, "__prefab": {"__id__": 172}, "_contentSize": {"__type__": "cc.Size", "width": 120.8, "height": 15}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bdh5Z+4zVGzr2YqzhKu6K5"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 162}, "_enabled": true, "__prefab": {"__id__": 174}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "85IOpTYRdGQ6oJWY3F6BaF"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 162}, "_enabled": true, "__prefab": {"__id__": 176}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0beiWbNg5Lo6k2I4WmrICJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "adJGlA6iVGU5szhObNgDdU", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 161}, "_enabled": true, "__prefab": {"__id__": 179}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 15}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f9CPJka75IkLIHFdKp5zYt"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 161}, "_enabled": true, "__prefab": {"__id__": 181}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d5044c2b-c868-40e0-a216-15d68060012e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "52paTnwV5HYKcdUcW9XE3D"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9bcW6eI/RGrIhP8Knf/sy1", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "boss<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 150}, "_children": [], "_active": true, "_components": [{"__id__": 184}, {"__id__": 186}], "_prefab": {"__id__": 188}, "_lpos": {"__type__": "cc.Vec3", "x": 12, "y": -6, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 183}, "_enabled": true, "__prefab": {"__id__": 185}, "_contentSize": {"__type__": "cc.Size", "width": -15, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a5MyZvIOVFUZ8lZALFLZ9d"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 183}, "_enabled": true, "__prefab": {"__id__": 187}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 85, "_paddingRight": 10, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 110, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "19iiWHYW1M2KN3n3biwrXy"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7556P5h2tCZLO3Ws/6YnUA", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 150}, "_enabled": true, "__prefab": {"__id__": 190}, "_contentSize": {"__type__": "cc.Size", "width": 574.963, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e1pcXoYxNF04dR14U24WBf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "74twvHXWxL543h3nfn0gmg", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "285kgvVG9EYJGTMv6m5a+g", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 132}, "_enabled": true, "__prefab": {"__id__": 194}, "_contentSize": {"__type__": "cc.Size", "width": 666, "height": 95.785}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f2LL/A9NlCVZY0VQ8/1Aay"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 132}, "_enabled": true, "__prefab": {"__id__": 196}, "_alignFlags": 1, "_target": null, "_left": 0, "_right": 0, "_top": 55.00000000000017, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2a+vImxKhEVZWpi7E3lvvC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2ePi3XGwNPQpOSOHOXB426", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 7}, "_children": [{"__id__": 199}, {"__id__": 205}, {"__id__": 211}], "_active": true, "_components": [{"__id__": 217}, {"__id__": 219}], "_prefab": {"__id__": 221}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 553, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "zm_icon_jiedian", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 198}, "_children": [], "_active": true, "_components": [{"__id__": 200}, {"__id__": 202}], "_prefab": {"__id__": 204}, "_lpos": {"__type__": "cc.Vec3", "x": -75, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 199}, "_enabled": true, "__prefab": {"__id__": 201}, "_contentSize": {"__type__": "cc.Size", "width": 9, "height": 9}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b4eDTNfrpHVox4uSNYXTuT"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 199}, "_enabled": true, "__prefab": {"__id__": 203}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 203, "g": 100, "b": 0, "a": 255}, "_spriteFrame": {"__uuid__": "4b27d06d-98d8-48a2-b22b-81c54014b244@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6dBdXX+thKqJ9r2TTA9lgB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0a0bYYUvNG8aG8UmatQ6Zr", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_total_hurt", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 198}, "_children": [], "_active": true, "_components": [{"__id__": 206}, {"__id__": 208}], "_prefab": {"__id__": 210}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 205}, "_enabled": true, "__prefab": {"__id__": 207}, "_contentSize": {"__type__": "cc.Size", "width": 125, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "acU3sH9r1HjqZpotGEtWFe"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 205}, "_enabled": true, "__prefab": {"__id__": 209}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 203, "g": 100, "b": 0, "a": 255}, "_string": "当前总伤害", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25, "_fontSize": 25, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2aYzY05sNDHpbPHYVfCQxi"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8cEIjQCOBGQKJ/wZXl5AjV", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "zm_icon_jiedian", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 198}, "_children": [], "_active": true, "_components": [{"__id__": 212}, {"__id__": 214}], "_prefab": {"__id__": 216}, "_lpos": {"__type__": "cc.Vec3", "x": 75, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 211}, "_enabled": true, "__prefab": {"__id__": 213}, "_contentSize": {"__type__": "cc.Size", "width": 9, "height": 9}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5ek+VkfmBLe44iFhqSHh7L"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 211}, "_enabled": true, "__prefab": {"__id__": 215}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 203, "g": 100, "b": 0, "a": 255}, "_spriteFrame": {"__uuid__": "4b27d06d-98d8-48a2-b22b-81c54014b244@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "88UJT/n0lNUbH6a7spUspb"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1eMAm5Iv5EKrPJEt/6/+On", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 198}, "_enabled": true, "__prefab": {"__id__": 218}, "_contentSize": {"__type__": "cc.Size", "width": 159, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e5c/SxFshPFps99Ixe4uHU"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 198}, "_enabled": true, "__prefab": {"__id__": 220}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 8, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "88B0nuc4RJXbtSI5rZ/fwV"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a8TtdmeRhI5bZ5fJ80q6EM", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 7}, "_enabled": true, "__prefab": {"__id__": 223}, "_contentSize": {"__type__": "cc.Size", "width": 682, "height": 1168.1890000000003}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2d8M8PmKtCDLJM8Xhu+X+4"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 7}, "_enabled": true, "__prefab": {"__id__": 225}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "00EXdNhIhCj6oE7RIxdU1H"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3bv5MNfr5FWZtO0uSYauwV", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 228}, "propertyPath": ["_name"], "value": "DialogPrimary"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 228}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 47, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 228}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 228}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 233}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 748, "height": 1354}}, {"__type__": "cc.TargetInfo", "localID": ["dcMfg1dj1MdZqre7xUgJqz"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 235}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 714, "height": 1204}}, {"__type__": "cc.TargetInfo", "localID": ["44g2VAc1VIU42rC+WZOf2b"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 237}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 570.5, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["8d9SnxGQhLO52jTVFOf8SD"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 239}, "propertyPath": ["_string"], "value": "战盟首领"}, {"__type__": "cc.TargetInfo", "localID": ["bb43HmgHRIsbkUVIAcdjyS"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 241}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 188, "height": 64.7}}, {"__type__": "cc.TargetInfo", "localID": ["beMyl2ExxOuKrkO90pFIia"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 243}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 78, "height": 78}}, {"__type__": "cc.TargetInfo", "localID": ["5dEGHE89tAEp4WR9Ug5/LE"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 245}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 331, "y": -53.5, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["1fOxWCfPxPAqI5OgnftRyd"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 247}, "propertyPath": ["_type"], "value": 0}, {"__type__": "cc.TargetInfo", "localID": ["caeUIgmr9I5KI1BHdh4pGs"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 247}, "propertyPath": ["_sizeMode"], "value": 0}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 250}, "propertyPath": ["_type"], "value": 2}, {"__type__": "cc.TargetInfo", "localID": ["20uWiQq9FLEYGXjb7BwnRi"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 252}, "propertyPath": ["_userRadius"], "value": 10}, {"__type__": "cc.TargetInfo", "localID": ["f2ofLYMWBPU4DygV2KdYZ4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 250}, "propertyPath": ["_enabled"], "value": true}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 255}, "propertyPath": ["_enabled"], "value": true}, {"__type__": "cc.TargetInfo", "localID": ["64zm3XnLdEhI5aGLaZp1vO"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 257}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 682, "height": 1168.1890000000003}}, {"__type__": "cc.TargetInfo", "localID": ["eaWQ3o6/tFYq6wOv7pEwoG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 237}, "propertyPath": ["_active"], "value": true}, {"__type__": "cc.TargetInfo", "localID": ["69SNtKW51PM5TKk6/rENSg"]}, {"__type__": "cc.TargetInfo", "localID": ["35/HKwwVVI2Ic71U0QxjCt"]}, {"__type__": "cc.TargetInfo", "localID": ["5c0jdp1RpAIp/40swHg/OC"]}, {"__type__": "cc.TargetInfo", "localID": ["d2k3Z3smxF157W0JJct868"]}, {"__type__": "cc.TargetInfo", "localID": ["00DZ8FcRRLibJHCRrrirRY"]}, {"__type__": "cc.TargetInfo", "localID": ["84JLIfK8hBRoCvACS/0yCr"]}, {"__type__": "cc.TargetInfo", "localID": ["02gNoHhdRG+IHfWoBd5x1L"]}, {"__type__": "cc.TargetInfo", "localID": ["526X1B07BDwK8NP8KInuT8"]}, {"__type__": "cc.TargetInfo", "localID": ["a0phqDYzRBHrtHGg2OvjML"]}, {"__type__": "cc.TargetInfo", "localID": ["6c1VnJIeZMmKgPhdjWpV3L"]}, {"__type__": "cc.TargetInfo", "localID": ["30uqGpqZ5CR5MGvNCIiIQP"]}, {"__type__": "cc.TargetInfo", "localID": ["3d/w530EJFVpjht+ZBUxHk"]}, {"__type__": "cc.TargetInfo", "localID": ["c5nYrBy6tGlKbNqHCimPFc"]}, {"__type__": "cc.TargetInfo", "localID": ["84KU9Nix1MGYCDDC5ATEzB"]}, {"__type__": "cc.Node", "_name": "p_normal", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 274}, {"__id__": 280}, {"__id__": 286}, {"__id__": 292}, {"__id__": 298}, {"__id__": 304}, {"__id__": 310}], "_active": true, "_components": [{"__id__": 314}, {"__id__": 316}, {"__id__": 318}], "_prefab": {"__id__": 320}, "_lpos": {"__type__": "cc.Vec3", "x": -602.5, "y": 459, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "unselect", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 273}, "_children": [], "_active": true, "_components": [{"__id__": 275}, {"__id__": 277}], "_prefab": {"__id__": 279}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 1, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 274}, "_enabled": true, "__prefab": {"__id__": 276}, "_contentSize": {"__type__": "cc.Size", "width": 92, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a1POxHeJlDTb1/PdRAMzkJ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 274}, "_enabled": true, "__prefab": {"__id__": 278}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "02923e31-2595-4260-ab94-2d43467f6d84@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "12EKzBk1pAtYv5jgSe4vNv"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "39K7b/fEdJOKF6rpKQ8V0F", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "select", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 273}, "_children": [], "_active": false, "_components": [{"__id__": 281}, {"__id__": 283}], "_prefab": {"__id__": 285}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 2, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 280}, "_enabled": true, "__prefab": {"__id__": 282}, "_contentSize": {"__type__": "cc.Size", "width": 93, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "28YZCWvDJB3Zpu4Yb8jelL"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 280}, "_enabled": true, "__prefab": {"__id__": 284}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5de31b8b-9410-4bc4-b84a-f4970d4e0743@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1cb/HwyuVFIpEr2EcCdpgW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "98Ykbu6ZxG2r3wmmrcu3Dt", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "light", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 273}, "_children": [], "_active": false, "_components": [{"__id__": 287}, {"__id__": 289}], "_prefab": {"__id__": 291}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1.2, "y": 1.2, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 286}, "_enabled": true, "__prefab": {"__id__": 288}, "_contentSize": {"__type__": "cc.Size", "width": 79.25, "height": 80.37000274658203}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.518107272849098, "y": 0.4792833039862232}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "86pApF4l5CsKn1vzz1PGSN"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 286}, "_enabled": true, "__prefab": {"__id__": 290}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "0b62697d-61cd-4ab8-b27c-f8d6a619a177", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "animation", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5c7B0ZzzFGU6qaBowNhC83"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e43NVx5fBELK7Cw0IZ4tiA", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "close", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 273}, "_children": [], "_active": true, "_components": [{"__id__": 293}, {"__id__": 295}], "_prefab": {"__id__": 297}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.9, "y": 0.9, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 292}, "_enabled": true, "__prefab": {"__id__": 294}, "_contentSize": {"__type__": "cc.Size", "width": 81, "height": 76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "80QJ2dYxpP/45jT7FoENge"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 292}, "_enabled": true, "__prefab": {"__id__": 296}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9f0f47d4-653e-47c4-9ce8-ebcf97a3a9b1@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "87sSXATENP+YJPmyBoUg+X"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a8fsWmK9RLGpYKU20DQ5R6", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "open", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 273}, "_children": [], "_active": true, "_components": [{"__id__": 299}, {"__id__": 301}], "_prefab": {"__id__": 303}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 6, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.9, "y": 0.9, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 298}, "_enabled": true, "__prefab": {"__id__": 300}, "_contentSize": {"__type__": "cc.Size", "width": 84, "height": 89}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "76XRgl3jdL+IlnaVT/3XAK"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 298}, "_enabled": true, "__prefab": {"__id__": 302}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "34064cb8-9ed6-45b8-a60d-3867d3d1e62f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "feTdrPl1REAbtHvQMcg/qv"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9cP08EZHRHJpLnSrJnWy+J", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 273}, "_children": [], "_active": true, "_components": [{"__id__": 305}, {"__id__": 307}], "_prefab": {"__id__": 309}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -49.46900000000005, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 304}, "_enabled": true, "__prefab": {"__id__": 306}, "_contentSize": {"__type__": "cc.Size", "width": 45, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d5K+sOfXJENYUdwt/9YEAm"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 304}, "_enabled": true, "__prefab": {"__id__": 308}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 57, "g": 115, "b": 174, "a": 255}, "_string": "100", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25, "_fontSize": 25, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3fopZBXXBKlK9TXEWe0PnH"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "79MlDUSZZI7pw0CxUtOeAf", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_click", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 273}, "_children": [], "_active": true, "_components": [{"__id__": 311}], "_prefab": {"__id__": 313}, "_lpos": {"__type__": "cc.Vec3", "x": -0.09500000000000064, "y": 1.665000000000001, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 310}, "_enabled": true, "__prefab": {"__id__": 312}, "_contentSize": {"__type__": "cc.Size", "width": 81.93, "height": 80.36999999999999}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "367LW9lgVNN4wU4IsUYOIk"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "00RJQNe1hHkqYzOfUwIFMN", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 273}, "_enabled": true, "__prefab": {"__id__": 315}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 120}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "03DuKScK5MO4P4qT5CFn+C"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 273}, "_enabled": true, "__prefab": {"__id__": 317}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "164cEIHWxG3pSir1aFXoNR"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 273}, "_enabled": true, "__prefab": {"__id__": 319}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a92f7xlvBIcKHH8M9ikOa1"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f7hoT/N1VNp6QOh0P/fbnv", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 322}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 321}, "asset": {"__uuid__": "89b64f36-c02b-43d5-b79f-998781162d58", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 323}, "targetOverrides": [{"__id__": 399}, {"__id__": 403}, {"__id__": 406}, {"__id__": 409}, {"__id__": 412}, {"__id__": 415}, {"__id__": 418}, {"__id__": 421}, {"__id__": 424}, {"__id__": 427}, {"__id__": 430}]}, {"__type__": "cc.PrefabInstance", "fileId": "e4DbZPZFNDnI23qtVB1WDu", "prefabRootNode": {"__id__": 1}, "mountedChildren": [{"__id__": 324}], "mountedComponents": [], "propertyOverrides": [{"__id__": 346}, {"__id__": 348}, {"__id__": 349}, {"__id__": 350}, {"__id__": 351}, {"__id__": 353}, {"__id__": 355}, {"__id__": 357}, {"__id__": 359}, {"__id__": 360}, {"__id__": 362}, {"__id__": 364}, {"__id__": 366}, {"__id__": 368}, {"__id__": 370}, {"__id__": 372}, {"__id__": 374}, {"__id__": 375}, {"__id__": 377}, {"__id__": 378}, {"__id__": 379}, {"__id__": 380}, {"__id__": 381}, {"__id__": 383}, {"__id__": 385}, {"__id__": 387}, {"__id__": 388}, {"__id__": 390}, {"__id__": 392}, {"__id__": 394}, {"__id__": 396}, {"__id__": 397}], "removedComponents": []}, {"__type__": "cc.MountedChildrenInfo", "targetInfo": {"__id__": 325}, "nodes": [{"__id__": 326}]}, {"__type__": "cc.TargetInfo", "localID": ["2aF+mNuEpO0pNy+KTQUZPD"]}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 321}}, "_parent": {"__id__": 327}, "_children": [], "_active": true, "_components": [{"__id__": 341}, {"__id__": 343}], "_prefab": {"__id__": 345}, "_lpos": {"__type__": "cc.Vec3", "x": -0.0004999999999881766, "y": -24.491999999999962, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "tip_content", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 321}, "_children": [{"__id__": 328}, {"__id__": 326}], "_active": true, "_components": [{"__id__": 336}, {"__id__": 338}], "_prefab": {"__id__": 340}, "_lpos": {"__type__": "cc.Vec3", "x": -941.659, "y": 61.74000000000001, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_tip_content", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 327}, "_children": [], "_active": true, "_components": [{"__id__": 329}, {"__id__": 331}, {"__id__": 333}], "_prefab": {"__id__": 335}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -91.21699999999998, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 328}, "_enabled": true, "__prefab": {"__id__": 330}, "_contentSize": {"__type__": "cc.Size", "width": 10, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c0bZC0yJxG2pl61qVkcNhg"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 328}, "_enabled": true, "__prefab": {"__id__": 332}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1aivy9cIFJyItuNFJU+vzk"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 328}, "_enabled": true, "__prefab": {"__id__": 334}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 10, "_paddingRight": 10, "_paddingTop": 10, "_paddingBottom": 10, "_spacingX": 10, "_spacingY": 10, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ecSrUEsZdFKoCsqLZmSAaB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "55JdBM3vFCA5EpmgIEIuB/", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 327}, "_enabled": true, "__prefab": {"__id__": 337}, "_contentSize": {"__type__": "cc.Size", "width": 309.303, "height": 148.142}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8a6QGMiBVMhrRJS/9ZLDgc"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 327}, "_enabled": true, "__prefab": {"__id__": 339}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e2efc2c1-2d71-491c-8c2e-b7e8309ea73c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d7FoDw85lKNLjnnqsm5G9H"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2aF+mNuEpO0pNy+KTQUZPD", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 326}, "_enabled": true, "__prefab": {"__id__": 342}, "_contentSize": {"__type__": "cc.Size", "width": 210, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d7FoRRzpZFCbDj7+3ct+vh"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 326}, "_enabled": true, "__prefab": {"__id__": 344}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 223, "g": 234, "b": 255, "a": 255}, "_string": "几率获得以下道具之一", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 21, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9b40AbzIhJK7v4J73EMxoK"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fbrHdG455JDbXCQo4xx4pd", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 347}, "propertyPath": ["_name"], "value": "UIClubTips"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 347}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 347}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 347}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 352}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 309.303, "height": 148.142}}, {"__type__": "cc.TargetInfo", "localID": ["8a6QGMiBVMhrRJS/9ZLDgc"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 354}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -1039.129, "y": -30.03800000000001, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["4dpZ8WKpBACYHar+3RCucw", "f5VJJTfHtB5aqbwW4jl16A"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 356}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 10, "height": 70}}, {"__type__": "cc.TargetInfo", "localID": ["c0bZC0yJxG2pl61qVkcNhg"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 358}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -91.21699999999998, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["55JdBM3vFCA5EpmgIEIuB/"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 347}, "propertyPath": ["_active"], "value": true}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 361}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"__type__": "cc.TargetInfo", "localID": ["4dpZ8WKpBACYHar+3RCucw", "3f2CI9oKNLFK7+fzc6STXj"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 363}, "propertyPath": ["_active"], "value": true}, {"__type__": "cc.TargetInfo", "localID": ["2aF+mNuEpO0pNy+KTQUZPD"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 365}, "propertyPath": ["_string"], "value": "99"}, {"__type__": "cc.TargetInfo", "localID": ["4dpZ8WKpBACYHar+3RCucw", "8aXJ8fJdZOsa3oa+CmpHN6"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 367}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 81, "height": 32.980000000000004}}, {"__type__": "cc.TargetInfo", "localID": ["4dpZ8WKpBACYHar+3RCucw", "07IRwvCO9Nyq7noluFiXK4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 369}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 685, "height": 1225}}, {"__type__": "cc.TargetInfo", "localID": ["82np0Pq5JHso2E+BFt167N"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 371}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 93, "height": 93}}, {"__type__": "cc.TargetInfo", "localID": ["4dpZ8WKpBACYHar+3RCucw", "c3FKi/GdFIerw2SbrXGJ9M"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 373}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 80, "height": 80}}, {"__type__": "cc.TargetInfo", "localID": ["4dpZ8WKpBACYHar+3RCucw", "7fVX9mvddJUaVADkQ2cBdp"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 361}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 42.5, "y": -30.009999999999998, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 376}, "propertyPath": ["_bottom"], "value": 0}, {"__type__": "cc.TargetInfo", "localID": ["4dpZ8WKpBACYHar+3RCucw", "64bvRI4TpF94y4cA2dRx5b"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 354}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 365}, "propertyPath": ["_fontSize"], "value": 23}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 365}, "propertyPath": ["_actualFontSize"], "value": 23}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 365}, "propertyPath": ["_lineHeight"], "value": 23}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 382}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 80, "height": 80}}, {"__type__": "cc.TargetInfo", "localID": ["4dpZ8WKpBACYHar+3RCucw", "b8lliivRBLlLauY3fNUDkb"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 384}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 0.8157894736842105, "y": 0.8157894736842105, "z": 1}}, {"__type__": "cc.TargetInfo", "localID": ["4dpZ8WKpBACYHar+3RCucw", "05/dyc9JZPzqOEp1/yFyae"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 386}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -941.64, "y": 59.74000000000001, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["d3Z6eBZoRMPZk6SOEqpwzN"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 363}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -941.659, "y": 61.74000000000001, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 389}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 93, "height": 93}}, {"__type__": "cc.TargetInfo", "localID": ["4dpZ8WKpBACYHar+3RCucw", "76eqiBuHdJiZAdj+7P63VD"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 391}, "propertyPath": ["_isAlign"], "value": true}, {"__type__": "cc.TargetInfo", "localID": ["ecSrUEsZdFKoCsqLZmSAaB"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 393}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["4dpZ8WKpBACYHar+3RCucw", "83uRFCZDpOWIYKQdKjv3ks"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 395}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["4dpZ8WKpBACYHar+3RCucw", "a1tsAamAJGy7Lb6gW02dNW"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 361}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 398}, "propertyPath": ["_spriteFrame"], "value": {"__uuid__": "e2efc2c1-2d71-491c-8c2e-b7e8309ea73c@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["d7FoDw85lKNLjnnqsm5G9H"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 400}, "sourceInfo": {"__id__": 401}, "propertyPath": ["bgColor"], "target": {"__id__": 400}, "targetInfo": {"__id__": 402}}, {"__type__": "cc.Node", "__editorExtras__": {}}, {"__type__": "cc.TargetInfo", "localID": ["66mTC3DbNCcIAV99gOJC6M"]}, {"__type__": "cc.TargetInfo", "localID": ["ef0OK1CglK8Y/O9Qla7khp"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 400}, "sourceInfo": {"__id__": 404}, "propertyPath": ["bgIcon"], "target": {"__id__": 400}, "targetInfo": {"__id__": 405}}, {"__type__": "cc.TargetInfo", "localID": ["66mTC3DbNCcIAV99gOJC6M"]}, {"__type__": "cc.TargetInfo", "localID": ["d1PcUCEbVCQ7t7NK/Q0EQM"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 400}, "sourceInfo": {"__id__": 407}, "propertyPath": ["lblHas"], "target": {"__id__": 400}, "targetInfo": {"__id__": 408}}, {"__type__": "cc.TargetInfo", "localID": ["66mTC3DbNCcIAV99gOJC6M"]}, {"__type__": "cc.TargetInfo", "localID": ["8aXJ8fJdZOsa3oa+CmpHN6"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 400}, "sourceInfo": {"__id__": 410}, "propertyPath": ["lblName"], "target": {"__id__": 400}, "targetInfo": {"__id__": 411}}, {"__type__": "cc.TargetInfo", "localID": ["66mTC3DbNCcIAV99gOJC6M"]}, {"__type__": "cc.TargetInfo", "localID": ["2bAKtKLnZKCYBH2GWUXLMe"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 400}, "sourceInfo": {"__id__": 413}, "propertyPath": ["nodeTips"], "target": {"__id__": 400}, "targetInfo": {"__id__": 414}}, {"__type__": "cc.TargetInfo", "localID": ["66mTC3DbNCcIAV99gOJC6M"]}, {"__type__": "cc.TargetInfo", "localID": ["92Zd3+BPtDyqBDJ5KJAHiv"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 400}, "sourceInfo": {"__id__": 416}, "propertyPath": ["bgT<PERSON>s<PERSON><PERSON>nt"], "target": {"__id__": 400}, "targetInfo": {"__id__": 417}}, {"__type__": "cc.TargetInfo", "localID": ["66mTC3DbNCcIAV99gOJC6M"]}, {"__type__": "cc.TargetInfo", "localID": ["9bNy6qjUJAlYXrFfJFfxs0"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 400}, "sourceInfo": {"__id__": 419}, "propertyPath": ["bgItemColor"], "target": {"__id__": 400}, "targetInfo": {"__id__": 420}}, {"__type__": "cc.TargetInfo", "localID": ["66mTC3DbNCcIAV99gOJC6M"]}, {"__type__": "cc.TargetInfo", "localID": ["3eIOGCiW5HbYMZaWwXgGaX"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 400}, "sourceInfo": {"__id__": 422}, "propertyPath": ["bgItemIcon"], "target": {"__id__": 400}, "targetInfo": {"__id__": 423}}, {"__type__": "cc.TargetInfo", "localID": ["66mTC3DbNCcIAV99gOJC6M"]}, {"__type__": "cc.TargetInfo", "localID": ["35RZGvJ0hBD5629CGQ6j+D"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 400}, "sourceInfo": {"__id__": 425}, "propertyPath": ["lblItemName"], "target": {"__id__": 400}, "targetInfo": {"__id__": 426}}, {"__type__": "cc.TargetInfo", "localID": ["66mTC3DbNCcIAV99gOJC6M"]}, {"__type__": "cc.TargetInfo", "localID": ["43L6/IG/9I/pZCDUJhm1Cm"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 400}, "sourceInfo": {"__id__": 428}, "propertyPath": ["lblItemNum"], "target": {"__id__": 400}, "targetInfo": {"__id__": 429}}, {"__type__": "cc.TargetInfo", "localID": ["66mTC3DbNCcIAV99gOJC6M"]}, {"__type__": "cc.TargetInfo", "localID": ["c4pyc25U9Dxaj24eKfnB/4"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 400}, "sourceInfo": {"__id__": 431}, "propertyPath": ["lblItemInfo"], "target": {"__id__": 400}, "targetInfo": {"__id__": 432}}, {"__type__": "cc.TargetInfo", "localID": ["66mTC3DbNCcIAV99gOJC6M"]}, {"__type__": "cc.TargetInfo", "localID": ["9aY8Z0t2hGpYCt3Bxc17wV"]}, {"__type__": "cc.Node", "_name": "boss_item", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 434}, {"__id__": 440}, {"__id__": 444}, {"__id__": 462}, {"__id__": 468}, {"__id__": 482}], "_active": true, "_components": [{"__id__": 642}], "_prefab": {"__id__": 644}, "_lpos": {"__type__": "cc.Vec3", "x": -1077.846, "y": 282.23, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "zm_image_boss<PERSON>tai", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 433}, "_children": [], "_active": true, "_components": [{"__id__": 435}, {"__id__": 437}], "_prefab": {"__id__": 439}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 434}, "_enabled": true, "__prefab": {"__id__": 436}, "_contentSize": {"__type__": "cc.Size", "width": 194, "height": 112}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3dpNSvuSdLo7LdLZUSOcmb"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 434}, "_enabled": true, "__prefab": {"__id__": 438}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b625ced0-4a75-4444-94f3-b3fa9422777f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2fV6KD1SxEm56vApML6gb0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cdMym+8zRMHLmqJHokmaOX", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_boss_img", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 433}, "_children": [], "_active": true, "_components": [{"__id__": 441}], "_prefab": {"__id__": 443}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 15, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 440}, "_enabled": true, "__prefab": {"__id__": 442}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2bWxyabH9GeJNm+TwWid4s"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a1H0hkCXZDt4zwchG2KgAJ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_progress_under", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 433}, "_children": [{"__id__": 445}, {"__id__": 451}], "_active": true, "_components": [{"__id__": 457}, {"__id__": 459}], "_prefab": {"__id__": 461}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -21.615, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_progress", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 444}, "_children": [], "_active": true, "_components": [{"__id__": 446}, {"__id__": 448}], "_prefab": {"__id__": 450}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 445}, "_enabled": true, "__prefab": {"__id__": 447}, "_contentSize": {"__type__": "cc.Size", "width": 225, "height": 14}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "38Pd6AySNLZKDVpuCzUHpP"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 445}, "_enabled": true, "__prefab": {"__id__": 449}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "f4e261d7-2c51-4bbc-b615-e5bd31c7a217@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 3, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0.5, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "25lCYH7fVM3bga2WO0Nnia"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "687cEFx6pGs50YYXnoBkG6", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_progress", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 444}, "_children": [], "_active": true, "_components": [{"__id__": 452}, {"__id__": 454}], "_prefab": {"__id__": 456}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 1.098, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 451}, "_enabled": true, "__prefab": {"__id__": 453}, "_contentSize": {"__type__": "cc.Size", "width": 51.29998779296875, "height": 54.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "69fC5vZY5DA7Iv7fZPT2Jl"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 451}, "_enabled": true, "__prefab": {"__id__": 455}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "1/11", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 22, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 121, "g": 27, "b": 18, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "63CAFkGSdAi7TcHZqaE+XS"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4eBdyH96dAhb2nQ8AJc/h7", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 444}, "_enabled": true, "__prefab": {"__id__": 458}, "_contentSize": {"__type__": "cc.Size", "width": 229, "height": 18}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b4EfkjlQlIBIn7QQQYAS7a"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 444}, "_enabled": true, "__prefab": {"__id__": 460}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3d48b5b8-fe9f-4032-92ca-f111d5ae107c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1crYaryoNGzJfCI+h503dX"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "eekakgngRI4rnPL/MICONX", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_defeat", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 433}, "_children": [], "_active": true, "_components": [{"__id__": 463}, {"__id__": 465}], "_prefab": {"__id__": 467}, "_lpos": {"__type__": "cc.Vec3", "x": 1.693, "y": 58.822, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 462}, "_enabled": true, "__prefab": {"__id__": 464}, "_contentSize": {"__type__": "cc.Size", "width": 129, "height": 102}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4cZ8o40RlJrIw684KCbiFq"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 462}, "_enabled": true, "__prefab": {"__id__": 466}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "494d20dc-07bb-4ea6-8ace-65236e848012@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a6Vr5KJSZF/KHUa7h2nEPT"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "29ZE2fNmRF8rOtvNuNCDCI", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_boss_name", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 433}, "_children": [{"__id__": 469}], "_active": true, "_components": [{"__id__": 475}, {"__id__": 477}, {"__id__": 479}], "_prefab": {"__id__": 481}, "_lpos": {"__type__": "cc.Vec3", "x": -110.461, "y": 136.231, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lbl_boss_name", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 468}, "_children": [], "_active": true, "_components": [{"__id__": 470}, {"__id__": 472}], "_prefab": {"__id__": 474}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 10, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 469}, "_enabled": true, "__prefab": {"__id__": 471}, "_contentSize": {"__type__": "cc.Size", "width": 27, "height": 142.01999999999998}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "beirx4KyBKrLbDz77N0PR+"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 469}, "_enabled": true, "__prefab": {"__id__": 473}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "BOOS1", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 27, "_fontSize": 27, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 27, "_overflow": 3, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ee5JkCSElN2I01ljfNRgQ9"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "46E3n3FNBHG5oh8NaNlPXh", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 468}, "_enabled": true, "__prefab": {"__id__": 476}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 182.01999999999998}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f5mFCkPD5OW4N/lsrOmTFw"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 468}, "_enabled": true, "__prefab": {"__id__": 478}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "c02c7452-bea6-441f-99ae-e2073f2fd6b6@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c2LQpHRcxCNqFGubIV5hFK"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 468}, "_enabled": true, "__prefab": {"__id__": 480}, "_resizeMode": 1, "_layoutType": 2, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 10, "_paddingBottom": 30, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "28dwIi2vNGiI/ngp3382A9"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ceNVB+FvFIu5+jyepbT6c7", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_info", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 433}, "_children": [{"__id__": 483}, {"__id__": 531}, {"__id__": 547}, {"__id__": 563}, {"__id__": 579}], "_active": true, "_components": [{"__id__": 637}, {"__id__": 639}], "_prefab": {"__id__": 641}, "_lpos": {"__type__": "cc.Vec3", "x": 4.897999999999996, "y": -92.52600000000001, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "node_award", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 482}, "_children": [{"__id__": 484}, {"__id__": 490}, {"__id__": 496}, {"__id__": 502}, {"__id__": 508}, {"__id__": 520}], "_active": true, "_components": [{"__id__": 528}], "_prefab": {"__id__": 530}, "_lpos": {"__type__": "cc.Vec3", "x": -131.36899999999997, "y": 0.1209999999999809, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 483}, "_children": [], "_active": false, "_components": [{"__id__": 485}, {"__id__": 487}], "_prefab": {"__id__": 489}, "_lpos": {"__type__": "cc.Vec3", "x": -31.539, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 484}, "_enabled": true, "__prefab": {"__id__": 486}, "_contentSize": {"__type__": "cc.Size", "width": 66, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6fu84VQmlFVbA4wRlfny5O"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 484}, "_enabled": true, "__prefab": {"__id__": 488}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 215, "b": 108, "a": 255}, "_string": "奖励：", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 22, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "29O+lFyGhOULMsphyLKKm4"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0bSP92LX5MN7ov5fBVevTr", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_award0", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 483}, "_children": [], "_active": true, "_components": [{"__id__": 491}, {"__id__": 493}], "_prefab": {"__id__": 495}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 490}, "_enabled": true, "__prefab": {"__id__": 492}, "_contentSize": {"__type__": "cc.Size", "width": 91, "height": 67}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "41Q5YkNMJCWIV/eM2qtVlB"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 490}, "_enabled": true, "__prefab": {"__id__": 494}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9a73c28d-e1d6-4418-8cd8-eacb189014cb@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e9rED4OwhAPbRB7nEPsIWD"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4bV/2oPFpAjYUM4wSPMxPc", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_award1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 483}, "_children": [], "_active": true, "_components": [{"__id__": 497}, {"__id__": 499}], "_prefab": {"__id__": 501}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 496}, "_enabled": true, "__prefab": {"__id__": 498}, "_contentSize": {"__type__": "cc.Size", "width": 90, "height": 67}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f9iBih+0lP94yY8FpOBUTh"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 496}, "_enabled": true, "__prefab": {"__id__": 500}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "67eca2d4-cc0d-4bb0-971f-8f603ae4958d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "12M1fr00xPGasGOD9Gelw1"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "63sgyckLRFaYLHOVmNXD63", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_award2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 483}, "_children": [], "_active": true, "_components": [{"__id__": 503}, {"__id__": 505}], "_prefab": {"__id__": 507}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 502}, "_enabled": true, "__prefab": {"__id__": 504}, "_contentSize": {"__type__": "cc.Size", "width": 90, "height": 67}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6b1yLr5AFP953V13NVnXPs"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 502}, "_enabled": true, "__prefab": {"__id__": 506}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9bea0feb-ff68-4d4c-aefa-c1b3d9afb1c9@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "12szh5GHROIZwzvSKnz6/8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "44+/EsJwdOw4cvpQR5JxrA", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_yilingqu", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 483}, "_children": [{"__id__": 509}], "_active": true, "_components": [{"__id__": 515}, {"__id__": 517}], "_prefab": {"__id__": 519}, "_lpos": {"__type__": "cc.Vec3", "x": -1.4210854715202004e-14, "y": -2.498999999999967, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 508}, "_children": [], "_active": true, "_components": [{"__id__": 510}, {"__id__": 512}], "_prefab": {"__id__": 514}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0.08715574274765817, "w": 0.9961946980917455}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 10}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 509}, "_enabled": true, "__prefab": {"__id__": 511}, "_contentSize": {"__type__": "cc.Size", "width": 63, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "26oOjJ7OlDtqm7drisC5D4"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 509}, "_enabled": true, "__prefab": {"__id__": 513}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 116, "g": 255, "b": 119, "a": 255}, "_string": "已领取", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 21, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "54lILXZVlJ0Zcp2XQlkKGw"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bfxq5A2A1LorsDLUfBi5mO", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 508}, "_enabled": true, "__prefab": {"__id__": 516}, "_contentSize": {"__type__": "cc.Size", "width": 98, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "45UTxR51lKpJTg1qBRzeVm"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 508}, "_enabled": true, "__prefab": {"__id__": 518}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 116, "g": 255, "b": 119, "a": 255}, "_spriteFrame": {"__uuid__": "708360ec-54a8-4eea-b30a-6bf5772f2f64@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b5B7Kq6E5A67BTdmjsDJv7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c1FhmUw75Be4WB8REjXUUm", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 483}, "_prefab": {"__id__": 521}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 520}, "asset": {"__uuid__": "21255ee7-bab1-4ea5-9b60-556815704f09", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 522}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "c3ciqx6JRCrIbspPeSMfi6", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 523}, {"__id__": 525}, {"__id__": 526}, {"__id__": 527}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 524}, "propertyPath": ["_name"], "value": "Badge"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 524}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 44, "y": 34, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 524}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 524}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 483}, "_enabled": true, "__prefab": {"__id__": 529}, "_contentSize": {"__type__": "cc.Size", "width": 93.431, "height": 70.255}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "02OvAHBQxIM6BDQczxA+Nf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7chahNl09ESrFog28qQn5Y", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_killer", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 482}, "_children": [{"__id__": 532}, {"__id__": 538}], "_active": false, "_components": [{"__id__": 544}], "_prefab": {"__id__": 546}, "_lpos": {"__type__": "cc.Vec3", "x": 31.194000000000017, "y": 32.858000000000004, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 531}, "_children": [], "_active": true, "_components": [{"__id__": 533}, {"__id__": 535}], "_prefab": {"__id__": 537}, "_lpos": {"__type__": "cc.Vec3", "x": -42.307, "y": -7, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 532}, "_enabled": true, "__prefab": {"__id__": 534}, "_contentSize": {"__type__": "cc.Size", "width": 104, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b3Cg5S3TVF0Kyc9vLGb4Z/"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 532}, "_enabled": true, "__prefab": {"__id__": 536}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 249, "b": 207, "a": 255}, "_string": "击杀者：", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1f53M9/ylJMZb+1mOJ11GB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8eUd8zsaNPebkbuOfDlL3B", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_killer", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 531}, "_children": [], "_active": true, "_components": [{"__id__": 539}, {"__id__": 541}], "_prefab": {"__id__": 543}, "_lpos": {"__type__": "cc.Vec3", "x": -0.769, "y": -7, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 538}, "_enabled": true, "__prefab": {"__id__": 540}, "_contentSize": {"__type__": "cc.Size", "width": 93.59994506835938, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "21/qNIqhhACKXQfs38EF5i"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 538}, "_enabled": true, "__prefab": {"__id__": 542}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 255, "b": 255, "a": 255}, "_string": "123123", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "01CCnTWL1Gj6aeF9SI6Bmw"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f5UN87jzZOmb7V1QeWfRJw", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 531}, "_enabled": true, "__prefab": {"__id__": 545}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 11}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3fBcCnMNBC0ZsDkv+JlALg"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0007pr8G1Bq4o1lk7oKPTM", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_go_fight", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 482}, "_children": [{"__id__": 548}], "_active": true, "_components": [{"__id__": 560}], "_prefab": {"__id__": 562}, "_lpos": {"__type__": "cc.Vec3", "x": 33.096000000000004, "y": -19.037000000000035, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btn_", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 547}, "_children": [{"__id__": 549}], "_active": true, "_components": [{"__id__": 555}, {"__id__": 557}], "_prefab": {"__id__": 559}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 548}, "_children": [], "_active": true, "_components": [{"__id__": 550}, {"__id__": 552}], "_prefab": {"__id__": 554}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 549}, "_enabled": true, "__prefab": {"__id__": 551}, "_contentSize": {"__type__": "cc.Size", "width": 67.44999694824219, "height": 54.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "20sSVz8qZPlIF2dISwvtq0"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 549}, "_enabled": true, "__prefab": {"__id__": 553}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "挑 战", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 27, "_fontSize": 27, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 48, "g": 103, "b": 180, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d1Sx3RY4tHnp5N3MseErrz"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ac+ZiuRlJJhLq+kWiJZnBy", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 548}, "_enabled": true, "__prefab": {"__id__": 556}, "_contentSize": {"__type__": "cc.Size", "width": 144, "height": 49}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "86nf8S9rFDfruRaMr5m06a"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 548}, "_enabled": true, "__prefab": {"__id__": 558}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d305d5d2-e829-43ba-8678-c09c43c159a4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b8q6S6ZWJEzpuyB/bhRjb0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "07g6JcATdHUIuTt6+EAror", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 547}, "_enabled": true, "__prefab": {"__id__": 561}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "daCtcujOpMMrAv8nWUCmwo"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "23cAO30DJGsKuCCY1X2dhj", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_haozhao", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 482}, "_children": [{"__id__": 564}], "_active": false, "_components": [{"__id__": 576}], "_prefab": {"__id__": 578}, "_lpos": {"__type__": "cc.Vec3", "x": 32, "y": -5.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btn_huangse", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 563}, "_children": [{"__id__": 565}], "_active": true, "_components": [{"__id__": 571}, {"__id__": 573}], "_prefab": {"__id__": 575}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 564}, "_children": [], "_active": true, "_components": [{"__id__": 566}, {"__id__": 568}], "_prefab": {"__id__": 570}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 565}, "_enabled": true, "__prefab": {"__id__": 567}, "_contentSize": {"__type__": "cc.Size", "width": 112, "height": 54.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "88JUIzYLVFH7kZ4zuUKxRN"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 565}, "_enabled": true, "__prefab": {"__id__": 569}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "号召战斗", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 27, "_fontSize": 27, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 183, "g": 110, "b": 56, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dcCWrGE3lLK70U0KtrONo5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "06rkHNIWJEPrY12kPyanAi", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 564}, "_enabled": true, "__prefab": {"__id__": 572}, "_contentSize": {"__type__": "cc.Size", "width": 144, "height": 49}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "55+2l9GaNGm7HJ17n03m5u"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 564}, "_enabled": true, "__prefab": {"__id__": 574}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8aac8aa5-631a-436e-92ba-84e56018ec61@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "baB13LhvpK5ZFtkEgDrtOj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "21xhdoV6pNNqPZx7ZNBVfj", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 563}, "_enabled": true, "__prefab": {"__id__": 577}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1b6RfJHC5L3pyWUGcYFHcf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d1wwKW/GxCn5/jCcsbHcb/", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_jiesuo", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 482}, "_children": [{"__id__": 580}, {"__id__": 586}, {"__id__": 610}], "_active": true, "_components": [{"__id__": 634}], "_prefab": {"__id__": 636}, "_lpos": {"__type__": "cc.Vec3", "x": 18, "y": -4.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 579}, "_children": [], "_active": true, "_components": [{"__id__": 581}, {"__id__": 583}], "_prefab": {"__id__": 585}, "_lpos": {"__type__": "cc.Vec3", "x": -29, "y": 30, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 580}, "_enabled": true, "__prefab": {"__id__": 582}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f32ZcuBX9B+IQnE23XxCBh"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 580}, "_enabled": true, "__prefab": {"__id__": 584}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 249, "b": 207, "a": 255}, "_string": "解锁条件：", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "865aTJ83BFzr1VoMk2qlTu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "65t2puE1RLP5JlhnZunaDK", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "tiaojian1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 579}, "_children": [{"__id__": 587}, {"__id__": 593}, {"__id__": 599}], "_active": true, "_components": [{"__id__": 605}, {"__id__": 607}], "_prefab": {"__id__": 609}, "_lpos": {"__type__": "cc.Vec3", "x": -89, "y": 2, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "icon_no", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 586}, "_children": [], "_active": true, "_components": [{"__id__": 588}, {"__id__": 590}], "_prefab": {"__id__": 592}, "_lpos": {"__type__": "cc.Vec3", "x": 9, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 587}, "_enabled": true, "__prefab": {"__id__": 589}, "_contentSize": {"__type__": "cc.Size", "width": 18, "height": 18}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "42uu7ihvZI/I9vE3fmd5o9"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 587}, "_enabled": true, "__prefab": {"__id__": 591}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "eb002a77-f364-4848-9fa9-c3079b4596f3@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4edKSaWJRIhaf+aMlaPIYW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "29ym8d0zFI5YsbKdqKj1So", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "icon_yes", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 586}, "_children": [], "_active": false, "_components": [{"__id__": 594}, {"__id__": 596}], "_prefab": {"__id__": 598}, "_lpos": {"__type__": "cc.Vec3", "x": -63.51299285888672, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 593}, "_enabled": true, "__prefab": {"__id__": 595}, "_contentSize": {"__type__": "cc.Size", "width": 18, "height": 18}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "adbQcYxG5PmpK2nyEekUf2"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 593}, "_enabled": true, "__prefab": {"__id__": 597}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "217ca067-8de6-44dd-86ce-7bfe065b41ef@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a6MLOQqnZFOqnnJ9zzCgR0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "da7hkfdu1FjaMFTwr8cKsQ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 586}, "_children": [], "_active": true, "_components": [{"__id__": 600}, {"__id__": 602}], "_prefab": {"__id__": 604}, "_lpos": {"__type__": "cc.Vec3", "x": 98.12747955322266, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 599}, "_enabled": true, "__prefab": {"__id__": 601}, "_contentSize": {"__type__": "cc.Size", "width": 150.2549591064453, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fdfIosdFFDv7HtyKqI1Wi6"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 599}, "_enabled": true, "__prefab": {"__id__": 603}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 249, "b": 207, "a": 255}, "_string": "击败上一只boss", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 21, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d2NtxenKFE8L2kF/v0n+Mu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ecbGT/MoZPToSDLLtOcHMm", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 586}, "_enabled": true, "__prefab": {"__id__": 606}, "_contentSize": {"__type__": "cc.Size", "width": 173.2549591064453, "height": 18}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a8E0T6zbVGDbUDdq3q0ue8"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 586}, "_enabled": true, "__prefab": {"__id__": 608}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 5, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "648wBZtplFWIf2VHJrvjsy"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "78hWEBGiNCZ50S4jJoFONS", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "tiaojian2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 579}, "_children": [{"__id__": 611}, {"__id__": 617}, {"__id__": 623}], "_active": true, "_components": [{"__id__": 629}, {"__id__": 631}], "_prefab": {"__id__": 633}, "_lpos": {"__type__": "cc.Vec3", "x": -89, "y": -25, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "icon_yes", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 610}, "_children": [], "_active": false, "_components": [{"__id__": 612}, {"__id__": 614}], "_prefab": {"__id__": 616}, "_lpos": {"__type__": "cc.Vec3", "x": -92.70699462890624, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 611}, "_enabled": true, "__prefab": {"__id__": 613}, "_contentSize": {"__type__": "cc.Size", "width": 18, "height": 18}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "96Qktzdb9GcZod9SNruPFo"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 611}, "_enabled": true, "__prefab": {"__id__": 615}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "217ca067-8de6-44dd-86ce-7bfe065b41ef@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "78xsw6kTBIo6UXAGi5GboW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1d5s7VUWBIR67sU4f8MF6M", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "icon_no", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 610}, "_children": [], "_active": true, "_components": [{"__id__": 618}, {"__id__": 620}], "_prefab": {"__id__": 622}, "_lpos": {"__type__": "cc.Vec3", "x": 9, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 617}, "_enabled": true, "__prefab": {"__id__": 619}, "_contentSize": {"__type__": "cc.Size", "width": 18, "height": 18}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "79PQUkO6hDPJwJMd+kzpEZ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 617}, "_enabled": true, "__prefab": {"__id__": 621}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "eb002a77-f364-4848-9fa9-c3079b4596f3@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c3gzBRayNJPqphxFwMaag+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d5sQn6EnlN+ZKw+M2dzSwi", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 610}, "_children": [], "_active": true, "_components": [{"__id__": 624}, {"__id__": 626}], "_prefab": {"__id__": 628}, "_lpos": {"__type__": "cc.Vec3", "x": 102.39999542236328, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 623}, "_enabled": true, "__prefab": {"__id__": 625}, "_contentSize": {"__type__": "cc.Size", "width": 159.59999084472656, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "593DNks7FI8ZuXsx5oDTa7"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 623}, "_enabled": true, "__prefab": {"__id__": 627}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 249, "b": 207, "a": 255}, "_string": "战盟等级达到5级", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 21, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e4L5JYUQNIHKqdexbo7MkT"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1fMIZbBFdG4Zhnwo+TNnpi", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 610}, "_enabled": true, "__prefab": {"__id__": 630}, "_contentSize": {"__type__": "cc.Size", "width": 182.19999084472656, "height": 18}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "01FFteK9tOxZaRUf4bo9vN"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 610}, "_enabled": true, "__prefab": {"__id__": 632}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 4.6, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5d3raNpOdPprpSxbb6MyE/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b1NDZPy0hFeIv9bdL61+XC", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 579}, "_enabled": true, "__prefab": {"__id__": 635}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2dEYlCFB1F45L1GK0Ux1YV"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "76KYm15fpAC5fGF2zAoYjo", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 482}, "_enabled": true, "__prefab": {"__id__": 638}, "_contentSize": {"__type__": "cc.Size", "width": 385, "height": 113}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "60yoFoXR1CHrvVC5QHRlzE"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 482}, "_enabled": true, "__prefab": {"__id__": 640}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6ee60a48-5ccd-415c-878e-cf63129f43be@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "71tS0x62JKL5OCm9ZVoQo7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "95UJQ+rwNPQLoImP2BlTSa", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 433}, "_enabled": true, "__prefab": {"__id__": 643}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bd5Gv+7RpEOYIwnleFilDJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2eSGCHbtZBXLxmJqCy6YOA", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 646}, "_contentSize": {"__type__": "cc.Size", "width": 685, "height": 1225}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "99dqvoF0RBrIW7r0AdmgSK"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": [{"__id__": 648}, {"__id__": 651}, {"__id__": 654}], "nestedPrefabInstanceRoots": [{"__id__": 520}, {"__id__": 321}, {"__id__": 2}]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 2}, "sourceInfo": {"__id__": 649}, "propertyPath": ["graphics"], "target": {"__id__": 2}, "targetInfo": {"__id__": 650}}, {"__type__": "cc.TargetInfo", "localID": ["f2ofLYMWBPU4DygV2KdYZ4"]}, {"__type__": "cc.TargetInfo", "localID": ["64zm3XnLdEhI5aGLaZp1vO"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 2}, "sourceInfo": {"__id__": 652}, "propertyPath": ["target"], "target": {"__id__": 2}, "targetInfo": {"__id__": 653}}, {"__type__": "cc.TargetInfo", "localID": ["b0b92Soe5IELo4zAOxt0/C"]}, {"__type__": "cc.TargetInfo", "localID": ["9bcW6eI/RGrIhP8Knf/sy1"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 2}, "sourceInfo": {"__id__": 655}, "propertyPath": ["content"], "target": {"__id__": 2}, "targetInfo": {"__id__": 656}}, {"__type__": "cc.TargetInfo", "localID": ["c0MKTQaGpArYPDMCdAD31L"]}, {"__type__": "cc.TargetInfo", "localID": ["dbf7v8K8FDyJmm0ZLDpP0E"]}]