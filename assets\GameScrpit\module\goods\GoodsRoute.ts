import { UICollect } from "../../game/ui/ui_collect/UICollect";
import { UIPortraitDetail } from "../../game/ui/ui_collect/UIPortraitDetail";
import { UIPortraitGroup } from "../../game/ui/ui_collect/UIPortraitGroup";
import { UIPortraitIntroduce } from "../../game/ui/ui_collect/UIPortraitIntroduce";
import { UIPay } from "../../game/ui/ui_shop/UIPay";
import { Recording, RecordingMap } from "../../lib/ui/recordingMap";

export enum GoodsRouteName {
  UICollect = "UICollect",

  UIPortraitGroup = "UIPortraitGroup",
  UIPortraitDetail = "UIPortraitDetail",
  UIPortraitIntroduce = "UIPortraitIntroduce",
  UIPay = "UIPay",
}

export class GoodsRoute {

  rotueTables: Recording[] = [
    {
      node: UICollect,
      uiName: GoodsRouteName.UICollect,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UIPortraitGroup,
      uiName: GoodsRouteName.UIPortraitGroup,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIPortraitDetail,
      uiName: GoodsRouteName.UIPortraitDetail,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIPortraitIntroduce,
      uiName: GoodsRouteName.UIPortraitIntroduce,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIPay,
      uiName: GoodsRouteName.UIPay,
      keep: false,
      relevanceUIList: [],
    },
  ];
  public async init() {
    this.rotueTables.forEach((item) => {
      RecordingMap.instance.addRecording(item.uiName, item);
    });
  }
}
