import MsgEnum from "../../game/event/MsgEnum";
import { ApiHandler } from "../../game/mgr/ApiHandler";
import { ActivityCmd } from "../../game/net/cmd/CmdData";
import { FirstSubRechargeMessage } from "../../game/net/protocol/Activity";
import MsgMgr from "../../lib/event/MsgMgr";
import { HdShouChongModule } from "./HdShouChongModule";

export class HdShouChongSubscriber {
  private firstCardBuyNotice(data: FirstSubRechargeMessage) {
    HdShouChongModule.data.firstRechargeMessage[data.id] = data;
    MsgMgr.emit(MsgEnum.ON_ACTIVITY_FIRST_RECHARGE, data);
  }
  public register() {
    //订阅服务器消息
    ApiHandler.instance.subscribe(FirstSubRechargeMessage, ActivityCmd.firstCardBuyNotice, this.firstCardBuyNotice);
  }
  public unRegister() {
    //取消订阅服务器消息
    ApiHandler.instance.unSubscribe(ActivityCmd.firstCardBuyNotice, this.firstCardBuyNotice);
  }
}
