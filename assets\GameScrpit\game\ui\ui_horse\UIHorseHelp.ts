import { _decorator, Component, Label, Node, RichText } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { JsonMgr } from "../../mgr/JsonMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
const { ccclass, property } = _decorator;

@ccclass("UIHorseHelp")
export class UIHorseHelp extends UINode {
  protected _openAct: boolean = true;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_HORSE}?prefab/ui/UIHorseHelp`;
  }

  protected onEvtShow(): void {
    this.getNode("titleLab").getComponent(Label).string = "帮助说明";
    this.getNode("des_lab").getComponent(RichText).string = JsonMgr.instance.jsonList.c_help[3].helpText;
  }

  private on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  private on_click_btn_closeMain() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
}
