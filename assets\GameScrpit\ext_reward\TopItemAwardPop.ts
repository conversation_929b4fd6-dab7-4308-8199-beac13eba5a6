import { _decorator, Node, UITransform } from "cc";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
import { v3, sp, tween, instantiate } from "cc";
import { tweenTagEnum } from "../game/GameDefine";
import FmUtils from "../lib/utils/FmUtils";
import { AudioMgr } from "../../platform/src/AudioHelper";
import { TipsMgr } from "../../platform/src/TipsHelper";
import { ItemCtrl } from "../game/common/ItemCtrl";
import MsgEnum from "../game/event/MsgEnum";
import MsgMgr from "../lib/event/MsgMgr";

const { ccclass, property } = _decorator;

@ccclass("TopItemAwardPop")
export class TopItemAwardPop extends BaseCtrl {
  @property(Node)
  nodeBg: Node;

  @property(sp.Skeleton)
  aniTytanchuang: sp.Skeleton;

  @property(Node)
  nodeLayoutGridItem: Node;

  @property(Node)
  nodeLayoutRemainder: Node;

  private _lineNum: number = 0;

  private itemMap: number[][];

  private nodeShowItemList: Node[] = [];

  // 动画是否播放结束，结束了才可以关闭
  private isAniFinish = false;

  /** 物品出现间隔 */
  private itemShowSpace = 0.08;

  public init(args) {
    super.init(args);
    TipsMgr.setEnableTouch(false, 0.3);

    this.itemMap = args;
  }

  start() {
    super.start();
    this.playeAction();

    AudioMgr.instance.playEffect(111);
    TipsMgr.setEnableTouch(false, 0.2);
  }

  private playeAction() {
    let col = 5;
    this.nodeBg.scale = v3(0, 0, 0);

    // 行数
    this._lineNum = Math.floor(this.itemMap.length / col);
    // 余数
    let remainder = this.itemMap.length % col;

    // 超过5行，缩短间隔
    if (this._lineNum > 5) {
      this.itemShowSpace = 0.03;
    }

    this.aniTytanchuang.getComponent(sp.Skeleton).setCompleteListener(() => {
      this.aniTytanchuang.getComponent(sp.Skeleton).setAnimation(0, "zi_gongxihuode_1", true);
    });

    this.aniTytanchuang.setAnimation(0, "zi_gongxihuode", false);

    let nodeLayoutGridItem = this.nodeLayoutGridItem;
    nodeLayoutGridItem.children.forEach((child) => {
      child.active = false;
    });

    this.nodeLayoutRemainder.children.forEach((nodeItem) => {
      nodeItem.active = false;
    });

    tween(this.nodeBg)
      .tag(tweenTagEnum.TopItemAwardPop_Tag)
      .to(0.1, { scale: v3(1, 1, 1) })
      .call(() => {
        // grid填充
        for (let i = 0; i < this._lineNum * col; i++) {
          let nodeItem = nodeLayoutGridItem.children[i];
          if (!nodeItem || !nodeItem.name.startsWith("item")) {
            nodeItem = instantiate(nodeLayoutGridItem.children[0]);
            nodeLayoutGridItem.addChild(nodeItem);
          }
          nodeItem.setSiblingIndex(i);

          this.itemAct(nodeItem.getChildByName("Item"), this.itemMap[i], i);
          nodeItem.active = true;
          this.nodeShowItemList.push(nodeItem);
        }

        // 剩余数量填充
        this.nodeLayoutRemainder.parent.active = remainder > 0;

        for (let i = 0; i < remainder; i++) {
          let nodeItem = this.nodeLayoutRemainder.children[i];
          if (!nodeItem || !nodeItem.name.startsWith("item")) {
            nodeItem = instantiate(this.nodeLayoutRemainder.children[0]);
            this.nodeLayoutRemainder.addChild(nodeItem);
          }
          nodeItem.setSiblingIndex(i);

          this.itemAct(nodeItem.getChildByName("Item"), this.itemMap[this._lineNum * col + i], this._lineNum * col + i);
          nodeItem.active = true;
          this.nodeShowItemList.push(nodeItem);
        }
      })
      .start();

    // 可关闭状态设置
    tween(this.node)
      .delay(this.itemShowSpace * this.itemMap.length)
      .call(() => {
        this.isAniFinish = true;
      })
      .start();
  }

  private itemFly() {
    AudioMgr.instance.playEffect(1584);
    //let maxNum = this.itemMap.length > 25 ? 25 : this.itemMap.length;

    let maxNum = this.itemMap.length;
    for (let i = 0; i < maxNum; i++) {
      let nodeItem = this.nodeShowItemList[i].getChildByName("Item");
      nodeItem.getComponent(ItemCtrl).flyEffect();
    }
  }

  private itemAct(node: Node, info: any, index: number) {
    FmUtils.setItemNode(node, info.id, info.num);
    node.scale = v3(0, 0, 0);

    tween(node)
      .tag(tweenTagEnum.TopItemAwardPop_Tag)
      .delay(0.1 + this.itemShowSpace * index)
      .to(0.16, { scale: v3(1, 1, 1) })
      .start();
  }

  onBtnClose() {
    if (!this.isAniFinish) {
      return;
    }
    this.isAniFinish = false;
    this.itemFly();

    tween(this.node)
      .delay(0.8)
      .call(() => {
        this.nodeShowItemList.forEach((nodeItem) => {
          tween(nodeItem).hide().start();
        });
      })
      .delay(0.2)
      .call(() => {
        setTimeout(() => {
          MsgMgr.emit(MsgEnum.ON_CLOSE_GET_AWARD);
          this.closeBack();
        }, 1);
      })
      .start();
  }

  protected update(dt: number): void {
    let height = this.nodeLayoutGridItem.getComponent(UITransform).height + 2;
    let newHeight = 140 + height;
    if (newHeight > 820) {
      newHeight = 820;
    }
    this.node.getChildByName("node_bg").getComponent(UITransform).height = newHeight;
  }
}
