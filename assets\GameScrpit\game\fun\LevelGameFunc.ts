import { _decorator } from "cc";
import Func from "./Func";
import { UIMgr } from "../../lib/ui/UIMgr";
import { UILevelGame } from "../ui/ui_fight/UILevelGame";
import { FightRouteItem } from "../../module/fight/src/FightModule";
const { ccclass, property } = _decorator;

@ccclass("LevelGameFunc")
export class LevelGameFunc extends Func {
  public showUI(args?: any) {
    return UIMgr.instance.showPage(FightRouteItem.UILevelGame, args);
  }
  public funcName() {
    return "LevelGameFunc";
  }
  public uiName() {
    return new UILevelGame().name;
  }
}
