import { _decorator, Label, Node, Sprite } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { PetMessage } from "../../net/protocol/Pet";
import { UIMgr } from "../../../lib/ui/UIMgr";
import ResMgr from "../../../lib/common/ResMgr";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { PetModule } from "../../../module/pet/PetModule";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import { PetRouteItem } from "../../../module/pet/PetRoute";
import { HeroColorBord, HeroColorCard } from "../../../module/hero/HeroConstant";
import { JsonMgr } from "../../mgr/JsonMgr";
import { ItemCost } from "../../common/ItemCost";
import { IConfigPet } from "../../JsonDefine";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { PetAudioName } from "../../../module/pet/PetConfig";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UIHeroPetAwake")
export class UIHeroPetAwake extends UINode {
  protected _openAct: boolean = true;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_PET}?prefab/ui/UIHeroPetAwake`;
  }
  //data
  petInfo: PetMessage = null;
  pet: IConfigPet = null;

  public init(args: any): void {
    super.init(args);
    this.petInfo = args;
    this.pet = PetModule.config.getHeroPet(this.petInfo.petId);
  }
  protected onEvtShow(): void {
    this.refreshUI();
  }
  private on_click_btn_wake() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let balance = PlayerModule.data.getItemNum(this.pet.wakeCostId);
    let costIndex = Math.min(this.petInfo.awakeCount, this.pet.wakeCostNumList.length - 1);
    let cost = this.pet.wakeCostNumList[costIndex];
    if (balance < cost) {
      UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, { itemId: this.pet.wakeCostId });
      return;
    }
    PetModule.api.awakeUp(this.petInfo.petId, (data: PetMessage) => {
      AudioMgr.instance.playEffect(PetAudioName.Effect.觉醒成功);
      this.onWakeUpResponse(data);
    });
  }
  private onWakeUpResponse(data: any) {
    UIMgr.instance.back();
    UIMgr.instance.showDialog(PetRouteItem.UIHeroPetAwakeSuccess, data); //
  }
  private refreshUI() {
    let spirit = PetModule.config.getHeroPet(this.pet.id);
    let petSkinList = [];
    petSkinList.push(spirit.firstSkin);
    petSkinList = petSkinList.concat(spirit.skin);
    let skinId = spirit.firstSkin;
    if (petSkinList.includes(this.petInfo.chosenSkinId)) {
      skinId = this.petInfo.chosenSkinId;
    }
    let petSkin = JsonMgr.instance.jsonList.c_petSkin[skinId];
    if (!petSkin) {
      log.log(`没有找到皮肤配置表信息：${skinId}`);
    }
    this.getNode("node_wake_original").getChildByName("lbl_name").getComponent(Label).string = petSkin.name;
    this.getNode("node_wake_original").getChildByName("lbl_level").getComponent(Label).string = `${this.petInfo.level}`;
    this.getNode("node_wake_original")
      .getChildByName("lbl_wake_count")
      .getComponent(Label).string = `${this.petInfo.awakeCount}`;
    this.getNode("node_wake_improved").getChildByName("lbl_name").getComponent(Label).string = petSkin.name;
    this.getNode("node_wake_improved").getChildByName("lbl_level").getComponent(Label).string = `${this.petInfo.level}`;
    this.getNode("node_wake_improved").getChildByName("lbl_wake_count").getComponent(Label).string = `${
      this.petInfo.awakeCount + 1
    }`;
    this.getNode("lbl_level_before").getComponent(Label).string = `${PetModule.service.getPetMaxLevel(this.pet.id)}`;
    this.getNode("lbl_level_after").getComponent(Label).string = `${
      PetModule.service.getPetMaxLevel(this.pet.id) + spirit.wakeLvAdd
    }`;

    let costIndex = Math.min(this.petInfo.awakeCount, this.pet.wakeCostNumList.length - 1);
    let cost = this.pet.wakeCostNumList[costIndex];
    // this.getNode("lbl_cost").getComponent(Label).string = `${balance}/${cost}`;
    this.getNode("ItemCost").getComponent(ItemCost).setItemId(this.pet.wakeCostId, cost);
    this.getNode("lbl_wake_skill_add").getComponent(Label).string = `1%`;
    let index = this.petInfo.petSkillList.length;
    let petSkill = PetModule.data.getHeroPetSkillList();
    ResMgr.setSpriteFrame(
      BundleEnum.BUNDLE_G_PET,
      `atlas_pet_skill/petskill_${petSkill[index % petSkill.length].iconId}`,
      this.getNode("bg_icon").getComponent(Sprite)
    );

    ResMgr.setSpriteFrame(
      BundleEnum.BUNDLE_COMMON_HERO_ICON,
      `images/${HeroColorCard[`color_${petSkin.color}`]}`,
      this.getNode("node_wake_original").getChildByName("bg_pet_color").getComponent(Sprite)
    );
    ResMgr.setSpriteFrame(
      BundleEnum.BUNDLE_COMMON_HERO_ICON,
      `images/${HeroColorBord[`color_${petSkin.color}`]}`,
      this.getNode("node_wake_original").getChildByName("bg_border").getComponent(Sprite)
    );

    ResMgr.setSpriteFrame(
      BundleEnum.BUNDLE_COMMON_HERO_ICON,
      `images/${HeroColorCard[`color_${petSkin.color}`]}`,
      this.getNode("node_wake_improved").getChildByName("bg_pet_color").getComponent(Sprite)
    );
    ResMgr.setSpriteFrame(
      BundleEnum.BUNDLE_COMMON_HERO_ICON,
      `images/${HeroColorBord[`color_${petSkin.color}`]}`,
      this.getNode("node_wake_improved").getChildByName("bg_border").getComponent(Sprite)
    );
    ResMgr.setSpriteFrame(
      BundleEnum.BUNDLE_COMMON_PET,
      `pet_card/${petSkin.cardId}`,
      this.getNode("bg_pet_img_1").getComponent(Sprite)
    );
    ResMgr.setSpriteFrame(
      BundleEnum.BUNDLE_COMMON_PET,
      `pet_card/${petSkin.cardId}`,
      this.getNode("bg_pet_img_2").getComponent(Sprite)
    );
  }
}
