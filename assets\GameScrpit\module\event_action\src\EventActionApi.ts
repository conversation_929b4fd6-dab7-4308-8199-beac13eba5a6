import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ApiH<PERSON>lerFail, ApiHandlerSuccess } from "../../../game/mgr/ApiHandler";
import { EventSubCmd } from "../../../game/net/cmd/CmdData";
import { EventRepelResponse, EventTrainMessage } from "../../../game/net/protocol/WorldEvent";
import { LongValue } from "../../../game/net/protocol/ExternalMessage";
import { EventActionModule } from "./EventActionModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.DEBUG);
export class EventActionApi {
  public getEventInfo(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(EventTrainMessage, EventSubCmd.eventInfo, null, (data: EventTrainMessage) => {
      log.log("三界事件信息======", data);
      EventActionModule.data.eventTrainMessage = data;
      success && success(data);
    });
  }

  /**驱赶贼人 */
  public postExpel(eventId: number, success?: ApiHandlerSuccess, fail?: ApiHandlerFail) {
    let data: LongValue = {
      value: eventId,
    };
    ApiHandler.instance.request(
      EventRepelResponse,
      EventSubCmd.expel,
      LongValue.encode(data),
      async (data: EventRepelResponse) => {
        log.log("驱赶贼人======", data);
        EventActionModule.data.setEventProgressMap({ id: eventId, progress: data.progress });

        let max = EventActionModule.data.eventTrainMessage.eventMap[eventId].totalProgress;

        await new Promise(async (resolve) => {
          (await success) && success(data);
          resolve(true);
        });
      },
      (errorCode: any, msg: string[], data: any) => {
        log.log(`${errorCode}`);
        log.log(data);
        fail && fail(errorCode, msg, data);
        return true;
      }
    );
  }

  /**
   * 测试接口：申请测试事件
   * @param success - 请求成功的回调函数
   * @param fail - 请求失败的回调函数
   */
  public test_申请_测试事件(eventId: number, success?: ApiHandlerSuccess, fail?: ApiHandlerFail) {
    // 假设这里有一个测试用的子命令
    let data: LongValue = {
      value: eventId,
    };

    const testSubCmd = EventSubCmd.test_申请_测试事件;
    ApiHandler.instance.request(EventTrainMessage, testSubCmd, LongValue.encode(data), (data: EventTrainMessage) => {
      log.log("测试事件信息======", data);
      EventActionModule.data.eventTrainMessage = data;
      if (success) {
        success(data);
      }
    });
  }
}
