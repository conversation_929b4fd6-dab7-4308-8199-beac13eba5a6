import { <PERSON>pi<PERSON><PERSON><PERSON>, ApiHandlerFail, ApiHandlerSuccess } from "../../game/mgr/ApiHandler";
import { ActivityCmd } from "../../game/net/cmd/CmdData";
import { FundMessage, AchieveRewardRequest, AchieveRewardResponse } from "../../game/net/protocol/Activity";
import { LongValue } from "../../game/net/protocol/ExternalMessage";
import { FundModule } from "./FundModule";

export class FundApi {
  /**获取基金领取进度信息 */
  public fund(id: number, success?: ApiHandlerSuccess) {
    let data: LongValue = {
      value: id,
    };
    ApiHandler.instance.request(FundMessage, ActivityCmd.fund, LongValue.encode(data), (res: FundMessage) => {
      FundModule.data.setFundData(res.achieveMap, res.activityId, () => {
        success && success(res);
      });
    });
  }

  // 领取基金进度奖励
  public takeFundReward(req: AchieveRewardRequest, success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(
      AchieveRewardResponse,
      ActivityCmd.takeFundReward,
      AchieveRewardRequest.encode(req),
      (data: AchieveRewardResponse) => {
        let obj = Object.create(null);
        obj[data.achieve.id] = data.achieve;
        FundModule.data.setFundData(obj, data.activityId);
        success && success(data);
      }
    );
  }
}
