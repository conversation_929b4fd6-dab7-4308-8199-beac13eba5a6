import { _decorator, Component, EditBox, Node } from "cc";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { routeConfig } from "db://assets/platform/src/core/managers/RouteTableManager";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { ClubModule } from "../../../module/club/ClubModule";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
const { ccclass, property } = _decorator;

@ccclass("UIClubEditSlogan")
@routeConfig({
  bundle: BundleEnum.BUNDLE_G_CLUB,
  url: "prefab/ui/UIClubEditSlogan",
  nextHop: [],
  exit: "dialog_close",
})
export class UIClubEditSlogan extends BaseCtrl {
  public playShowAni: boolean = true;
  start() {
    super.start();
    this.getNode("edit_notice").getComponent(EditBox).string = ClubModule.data.clubMessage.slogan;
  }

  update(deltaTime: number) {}
  private on_click_btn_cancel() {
    //
    this.closeBack();
  }
  private on_click_btn_commit() {
    //
    let editbox = this.getNode("edit_notice").getComponent(EditBox);
    if (editbox.string == ClubModule.data.clubMessage.slogan || !editbox.string || editbox.string.length == 0) {
      TipsMgr.showTip("请输入公告");
      return;
    }
    ClubModule.api.modifySlogan(editbox.string, (data) => {
      this.closeBack();
    });
  }
}
