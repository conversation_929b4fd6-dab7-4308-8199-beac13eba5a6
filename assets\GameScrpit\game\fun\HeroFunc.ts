import { _decorator } from "cc";
import Func from "./Func";
import { UIMgr } from "../../lib/ui/UIMgr";
import { UIHeroMain } from "../ui/hero/UIHeroMain";
import { HeroRouteItem } from "../../module/hero/HeroRoute";
const { ccclass, property } = _decorator;

@ccclass("HeroFunc")
export class HeroFunc extends Func {
  public showUI(args?: any) {
    return UIMgr.instance.showDialog(HeroRouteItem.UIHeroMain, args);
  }
  public funcName() {
    return "HeroFunc";
  }

  public uiName() {
    return new UIHeroMain().name;
  }
}
