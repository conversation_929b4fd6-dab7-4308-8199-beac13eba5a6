import { JsonMgr } from "../../game/mgr/JsonMgr";
import { FriendModule } from "../friend/FriendModule";
import { HeroModule } from "../hero/HeroModule";
import { PlayerModule } from "../player/PlayerModule";

export class GoodsService {
  /**
   * 转换为符号与金额
   * @param id 传入的id
   * @returns [符号，金额]
   */
  public getPayInfo(id: number): string {
    if (id > 10000 && id < 20000) {
      return "￥" + (id % 10000);
    }
  }
}
