import CmdMgr from "../../game/mgr/CmdMgr";
import { HeroSubCmd } from "../../game/net/cmd/CmdData";

/** 常量放这这里 **/
HeroSubCmd;

// 模块事件
export enum ClubEvent {
  CLUB_DATA_CHANGE = "CLUB_DATA_CHANGE", //数据变动
  CLUB_POSITION_CHANGE = "CLUB_POSITION_CHANGE", //职位变动
  CLUB_APPLY_STATE_CHANGE = "CLUB_APPLY_STATE_CHANGE", //申请状态变动
  CLUB_ACTIVITE_CHANGE = "CLUB_ACTIVITE_CHANGE",
  CLUB_BOSS_STATE_CHANGE = "CLUB_BOSS_STATE_CHANGE",
}

export enum CLUB_POSITION {
  盟主 = 1,
  副盟主 = 2,
  成员 = 3,
}
