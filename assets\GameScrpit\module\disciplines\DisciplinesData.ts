import GameHttpApi from "../../game/httpNet/GameHttpApi";
import { DisciplinesModule } from "./DisciplinesModule";
import {
  AchieveMessage,
  DaySignMessage,
  DayTaskMessage,
  LeaderFundMessage,
  LeaderRechargeMessage,
  LeaderSignResponse,
} from "../../game/net/protocol/Activity";
import MsgMgr from "../../lib/event/MsgMgr";
import MsgEnum from "../../game/event/MsgEnum";
import { CommIntegerListMessage } from "../../game/net/protocol/Comm";
import { DisciplinesMsgEnum } from "./DisciplinesConfig";
import { UIMgr } from "../../lib/ui/UIMgr";
import TipMgr from "../../lib/tips/TipMgr";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { LeaderFundVO, AchieveVO, DayTaskVO, RedeemPackVO } from "../activity/ActivityConfig";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class DisciplinesData {
  private _leaderFundInfo: LeaderFundMessage = null;
  public get leaderFundInfo(): LeaderFundMessage {
    if (!this._leaderFundInfo) {
      this._leaderFundInfo = {
        activityId: 11001,
        /** 物料ID:兑换次数 */
        redeemMap: null,
        /** 物料ID:广告次数 */
        adMap: null,
        /** 解锁条件：值 */
        limitMap: null,
        /** 自选礼包的选中情况 */
        chosenMap: null,
        /** 成就列表 key:成就ID */
        achieveMap: null,
        /** 签到 */
        daySign: null,
        dayTask: null,
        /** 充值进度 */
        leaderRecharge: null,
      };
    }

    return this._leaderFundInfo;
  }
  public get limitMap(): { [key: number]: number } {
    return this.leaderFundInfo.limitMap;
  }
  /** 解锁条件：值 */
  public set limitMap(value: { [key: number]: number }) {
    this.leaderFundInfo.limitMap = value;
    MsgMgr.emit(DisciplinesMsgEnum.DISCIPLINESMSGENUM_RED_DOT_UPDATE);
  }
  public set leaderFundInfo(value: LeaderFundMessage) {
    this._leaderFundInfo = value;
    MsgMgr.emit(DisciplinesMsgEnum.DISCIPLINESMSGENUM_RED_DOT_UPDATE);
  }

  public getAchieveData(id: number): AchieveMessage {
    if (!this.achieveMap) {
      this.achieveMap = Object.create(null);
    }

    if (!this.achieveMap[id]) {
      this.achieveMap[id] = {
        /** 成就互动ID */
        id: id,
        /** 当前成就值 */
        targetVal: 0,
        /** 是否支付 */
        paid: false,
        /** 已经领取基础奖励的列表 */
        basicTakeList: [],
        /** 已经领取奖励的列表 */
        paidTakeList: [],
      };
    }

    return this.achieveMap[id];
  }

  public setAchieveData(id: number, data: AchieveMessage) {
    this.achieveMap[id] = data;
    MsgMgr.emit(DisciplinesMsgEnum.DISCIPLINESMSGENUM_RED_DOT_UPDATE);
  }

  public get achieveMap() {
    if (!this.leaderFundInfo.achieveMap) {
      this.leaderFundInfo.achieveMap = Object.create(null);
    }
    return this.leaderFundInfo.achieveMap;
  }

  public set achieveMap(val) {
    this.leaderFundInfo.achieveMap = val;
  }

  public getDayTask(): DayTaskMessage {
    if (!this.leaderFundInfo.dayTask) {
      this.leaderFundInfo.dayTask = {
        /** 任务完成数量 */
        targetValList: [],
        /** 已领取奖励的索引列表 */
        takeList: [],
      };
    }

    return this.leaderFundInfo.dayTask;
  }

  public addNewOkTask(index: number) {
    let dayTask = this.leaderFundInfo.dayTask;
    let is = dayTask.takeList.indexOf(index);
    if (is == -1) {
      dayTask.takeList.push(index);
    }
    this.leaderFundInfo.dayTask = dayTask;
    MsgMgr.emit(DisciplinesMsgEnum.DISCIPLINESMSGENUM_RED_DOT_UPDATE);
  }

  public get redeemMap() {
    if (!this.leaderFundInfo.redeemMap) {
      this.leaderFundInfo.redeemMap = Object.create(null);
    }
    return this.leaderFundInfo.redeemMap;
  }

  public set redeemMap(val) {
    this.leaderFundInfo.redeemMap = val;
    MsgMgr.emit(DisciplinesMsgEnum.DISCIPLINESMSGENUM_RED_DOT_UPDATE);
  }

  public get chosenMap() {
    if (!this.leaderFundInfo.chosenMap) {
      this.leaderFundInfo.chosenMap = {};
    }
    return this.leaderFundInfo.chosenMap;
  }

  public set chosenMap(val) {
    this.leaderFundInfo.chosenMap = val;
  }

  public getChosenMap(id: number) {
    if (!this.leaderFundInfo.chosenMap) {
      this.leaderFundInfo.chosenMap = Object.create(null);
    }

    if (!this.leaderFundInfo.chosenMap[id]) {
      this.leaderFundInfo.chosenMap[id] = {
        intList: [],
      };
    }

    return this.leaderFundInfo.chosenMap[id];
  }

  public setChosen(id: number, data: { intList: Array<number> }) {
    this.leaderFundInfo.chosenMap[id] = data;
    MsgMgr.emit(MsgEnum.ON_ACTIVITY_FUND_GIFT_UPDATE, this);
    MsgMgr.emit(DisciplinesMsgEnum.DISCIPLINESMSGENUM_RED_DOT_UPDATE);
  }

  public setChoseMap(obj: { [key: number]: CommIntegerListMessage }) {
    this.leaderFundInfo.chosenMap = obj;
    MsgMgr.emit(MsgEnum.ON_ACTIVITY_FUND_GIFT_UPDATE, this);
    MsgMgr.emit(DisciplinesMsgEnum.DISCIPLINESMSGENUM_RED_DOT_UPDATE);
  }

  public async getLeaderRecharge(): Promise<LeaderRechargeMessage> {
    if (!this.leaderFundInfo.leaderRecharge) {
      // if (ActivityModule.service.checkActivityUnlock(11001)) {
      // }
      DisciplinesModule.api.leaderFundInfo(11001);
      let rechargeVO = await this.getRechargeVO();
      this.leaderFundInfo.leaderRecharge = {
        /** 分子 */
        numerator: 0,
        /** 分母 */
        denominator: rechargeVO.round,
        /** 是否领取过奖励 */
        take: false,
      };
    }
    return this.leaderFundInfo.leaderRecharge;
  }

  public set leaderRecharge(data: LeaderRechargeMessage) {
    this.leaderFundInfo.leaderRecharge = data;
    MsgMgr.emit(DisciplinesMsgEnum.DISCIPLINESMSGENUM_RED_DOT_UPDATE);
  }
  public get leaderRecharge() {
    return this.leaderFundInfo.leaderRecharge;
  }

  public set numerator(val) {
    this.leaderRecharge.numerator = val;
    MsgMgr.emit(DisciplinesMsgEnum.DISCIPLINESMSGENUM_RED_DOT_UPDATE);
  }

  public get daySign(): DaySignMessage {
    if (!this.leaderFundInfo.daySign) {
      this.leaderFundInfo.daySign = {
        /** 今日是否已经签到 */
        sign: false,
        /** 是否已充值 */
        paid: false,
        /** 领取基础签到奖励 0-6 */
        basicList: [],
        /** 领取付费签到奖励 0-6 */
        paidList: [],
      };
    }
    return this._leaderFundInfo.daySign;
  }

  public setDaySign(data: LeaderSignResponse) {
    this.leaderFundInfo.daySign.sign = data.sign;
    this.leaderFundInfo.daySign.basicList = data.basicList;
    this.leaderFundInfo.daySign.paidList = data.paidList;
    MsgMgr.emit(DisciplinesMsgEnum.DISCIPLINESMSGENUM_RED_DOT_UPDATE);
  }

  private _disciplinesdb: LeaderFundVO = null;
  public getdb(): Promise<LeaderFundVO> {
    return new Promise((resolve, reject) => {
      if (this._disciplinesdb) {
        resolve(this._disciplinesdb);
      } else {
        GameHttpApi.getActivityConfig(11001).then((resp: any) => {
          if (resp.code != 200) {
            log.error(resp);
          }
          let db = JSON.parse(resp.msg);
          this._disciplinesdb = db;
          resolve(this._disciplinesdb);
        });
      }
    });
  }

  public upVO() {
    this._disciplinesdb = null;
    this.getdb();

    let rotueTables = DisciplinesModule.route.rotueTables;
    for (let i = 0; i < rotueTables.length; i++) {
      UIMgr.instance.closeByName(rotueTables[i].uiName);
    }
    TipMgr.showTip("活动内容已变更");
  }

  public async getDisFundOV(): Promise<AchieveVO> {
    let db = await this.getdb();
    return db.achieveVOList[0];
  }

  public async getDisTaskVO(): Promise<DayTaskVO> {
    let db = await this.getdb();
    return db.taskVO;
  }

  public async getRedeemList(): Promise<Array<RedeemPackVO>> {
    let db = await this.getdb();
    return db.redeemList[0];
  }

  public async getRechargeVO(): Promise<{ id: number; rewardList: Array<number>; round: number }> {
    let db = await this.getdb();
    return db.rechargeVO;
  }
}
