import { EventTouch, Input, Node, NodeEventType, Vec2, _decorator, v3 } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import ToolExt from "../../common/ToolExt";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { tween } from "cc";
import { Vec3 } from "cc";
import { PupilRouteName } from "../../../module/pupil/src/PupilRoute";
import { FarmRouteName } from "../../../module/farm/FarmRoute";
import { UITransform } from "cc";
import { NodeTool } from "../../../lib/utils/NodeTool";
import { StartUp } from "../../../lib/StartUp";
import { GameDirector } from "../../GameDirector";
import { SystemOpenEnum } from "../../GameDefine";
import { AudioMgr, AudioName } from "../../../../platform/src/AudioHelper";
import { BadgeMgr, BadgeType } from "../../mgr/BadgeMgr";
import { UIGameMapCtrl } from "./UIGameMapCtrl";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { PupilAudioName } from "../../../module/pupil/src/PupilConstant";
import { CityModule } from "../../../module/city/CityModule";
import { OtherBuildIdEnum } from "../../../module/city/CityConstant";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { PetRouteItem } from "../../../module/pet/PetRoute";
import { RouteManager } from "db://assets/platform/src/core/managers/RouteManager";
import { UIFractureSearch } from "../ui_fracture/UIFractureSearch";
import { FRACTURE_ACTIVITYID } from "../../../module/fracture/FractureConstant";
import GuideMgr from "../../../ext_guide/GuideMgr";

const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UIGameMap")
export class UIGameMap extends UINode {
  protected _resetLayer: boolean = false;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_GAME_MAP}?prefab/ui/UIGameMap`;
  }

  touchPointId: number = -1;
  touchStartPoint: Vec2 = new Vec2(0, 0);
  isMove: boolean = false;

  cd: number = 0;

  public init(args: any): void {
    super.init(args);
  }

  protected onRegEvent(): void {
    MsgMgr.on(MsgEnum.ON_ZHAOGE_CITY_FOCOUS, this.moveToCity, this);
  }

  protected onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_ZHAOGE_CITY_FOCOUS, this.moveToCity, this);
  }

  protected onEvtShow(): void {
    AudioMgr.instance.playMusic(AudioName.Sound.府邸和三界通用);
    this.on_drag_bg();

    const mapNode: Node = this.getNode("btn_map").getChildByName("node_ground");

    //EventDisplayManager.instance.init(mapNode);
    BadgeMgr.instance.setBadgeId(this.getNode("btn_pupil"), BadgeType.UIMajorCity.btn_pupil.id);
    BadgeMgr.instance.setBadgeId(this.getNode("btn_farm"), BadgeType.UIMajorCity.btn_farm.id);
  }

  /**地图移动 */
  private on_drag_bg() {
    let myTouch: Node = this.getNode("btn_map");

    let screenWidth = StartUp.instance.getVisibleSize().width;
    let screenHeight = StartUp.instance.getVisibleSize().height;

    myTouch.on(
      Input.EventType.TOUCH_CANCEL,
      (event: EventTouch) => {
        try {
          if (event && event.simulate) {
            return;
          }
          if (this.touchPointId == event.getID()) {
            this.touchStartPoint = new Vec2(0, 0);
            this.touchPointId = -1;
          }
        } catch (error) {
          log.error(error);
        }
      },
      this,
      true
    );

    myTouch.on(
      Input.EventType.TOUCH_END,
      (event: EventTouch) => {
        try {
          if (this.touchPointId == event.getID()) {
            this.touchStartPoint = new Vec2(0, 0);
            this.touchPointId = -1;
          }

          if (this.isMove) {
            event.propagationStopped = true;
          }
          this.isMove = false;
        } catch (error) {
          log.error(error);
        }
      },
      this,
      true
    );

    myTouch.on(
      Node.EventType.TOUCH_MOVE,
      (event: EventTouch) => {
        try {
          // 放大状态不允许移动
          if (this.getNode("btn_map").getScale().x > 1) {
            return;
          }

          // 引导状态不能移动
          if (GuideMgr.isGuiding()) {
            return;
          }

          let node: Node = event.currentTarget;
          let pos = new Vec2();
          if (this.touchPointId != event.getID()) {
            return;
          }

          let shit = pos.set(event.getUILocation());
          let x = shit.x - screenWidth / 2 - this.touchStartPoint.x;
          let y = shit.y - screenHeight / 2 - this.touchStartPoint.y;
          node.setPosition(ToolExt.mapBeyondSize(v3(x, y, 1), node));

          let length = Vec2.distance(event.getUILocation(), event.getUIStartLocation());
          if (length > 20) {
            // 移动了10个像素以上，才触发移动事件
            if (!this.isMove && event.target !== myTouch) {
              // Simulate touch cancel for target node
              const cancelEvent = new EventTouch(event.getTouches(), event.bubbles, NodeEventType.TOUCH_CANCEL);
              cancelEvent.touch = event.touch;
              cancelEvent.simulate = true;
              (event.target as Node).dispatchEvent(cancelEvent);
              this.isMove = true;
            }
          }

          // 优化-隐藏城市节点
          this.node.getComponent(UIGameMapCtrl).hideCityNode();
        } catch (error) {
          log.error(error);
        }
      },
      this,
      true
    );

    myTouch.on(
      Input.EventType.TOUCH_START,
      (event: EventTouch) => {
        try {
          this.isMove = false;
          let node: Node = event.currentTarget;
          if (this.touchPointId > 0) {
            return;
          }
          this.touchPointId = event.getID();
          this.touchStartPoint.set(event.getUILocation());
          let x = this.touchStartPoint.x - screenWidth / 2 - node.getPosition().x;
          let y = this.touchStartPoint.y - screenHeight / 2 - node.getPosition().y;

          this.touchStartPoint = new Vec2(x, y);
        } catch (error) {
          log.error(error);
        }
      },
      this,
      true
    );
  }

  /**
   * 地图移动到指定建筑位置
   *
   * @param builderName 建筑节点
   * @returns
   */
  private moveToCity(builderName: string, clickName: string) {
    const mapNode: Node = this.getNode("btn_map");

    let cityNode = mapNode.getChildByPath("node_ground/" + builderName);

    // 节点在地图上的坐标，孙节及以下需要转换坐标系
    let x = 0;
    let y = 0;
    if (!cityNode) {
      cityNode = NodeTool.findByName(mapNode, builderName);
      if (!cityNode) {
        log.warn("gamemap节点没找到");
        return;
      }

      // 转换坐标系
      let posWorld = cityNode.getWorldPosition();
      let posOut: Vec3 = mapNode.getComponent(UITransform).convertToNodeSpaceAR(posWorld);
      x = posOut.x;
      y = posOut.y;
    } else {
      // node_groud 节点下，直接取坐标
      x = cityNode.getPosition().x;
      y = cityNode.getPosition().y;
    }

    // 兼容子节点
    if (clickName) {
      let clickNode = NodeTool.findByName(cityNode, clickName);
      if (clickNode) {
        x += clickNode.getPosition().x;
        y += clickNode.getPosition().y;
        while (clickNode.getParent().name != cityNode.name) {
          clickNode = clickNode.getParent();
          x += clickNode.getPosition().x;
          y += clickNode.getPosition().y;
        }
      }
    }

    // 中心点偏移
    let anchor = cityNode.getComponent(UITransform).anchorPoint;
    let size = cityNode.getComponent(UITransform).contentSize;
    x += size.width * (0.5 - anchor.x);
    y += size.height * (0.5 - anchor.y);

    // 设置地图最终位置
    let pos = ToolExt.mapBeyondSize(v3(-x, -y, 1), mapNode);

    // 移动ani
    tween(mapNode)
      .to(
        0.5,
        { position: pos },
        {
          easing: "fade",
          onUpdate: () => {},
        }
      )
      .call(() => {
        this.node.getComponent(UIGameMapCtrl).hideCityNode();
      })
      .start();
  }

  /**弟子入口 */
  private on_click_btn_pupil(event) {
    log.log("on_click_btn_pupil");
    if (!CityModule.data.cityAggregateMessage.otherCreateCityList.includes(OtherBuildIdEnum.弟子)) {
      if (GameDirector.instance.isSystemOpen(SystemOpenEnum.PUPIL_弟子系统)) {
        CityModule.api.createOtherCity(OtherBuildIdEnum.弟子, () => {
          this.node.getComponent(UIGameMapCtrl).onBuildPupil();
        });
      } else {
        TipsMgr.showTip(GameDirector.instance.getUnLockMsg(SystemOpenEnum.PUPIL_弟子系统));
      }
      return;
    }

    AudioMgr.instance.playEffect(PupilAudioName.Effect.点击三界地图弟子图标);

    UIMgr.instance.showDialog(PupilRouteName.UIPupilPage);
  }

  /** 福地洞天入口 */
  private on_click_btn_farm(event) {
    if (!CityModule.data.cityAggregateMessage.otherCreateCityList.includes(OtherBuildIdEnum.福地)) {
      if (GameDirector.instance.isSystemOpen(SystemOpenEnum.FARM_福地洞天)) {
        CityModule.api.createOtherCity(OtherBuildIdEnum.福地, () => {
          AudioMgr.instance.playEffect(1578);
          this.node.getComponent(UIGameMapCtrl).onBuildFarm();
        });
      } else {
        TipsMgr.showTip(GameDirector.instance.getUnLockMsg(SystemOpenEnum.FARM_福地洞天));
      }
      return;
    }
    AudioMgr.instance.playEffect(1588);
    UIMgr.instance.showDialog(FarmRouteName.UIFarmMain);
  }

  // public tick(dt: any): void {
  //   // 1秒执行一次
  //   this.cd += dt;
  //   if (this.cd > 0.5) {
  //     this.cd = 0;
  //     this.node.getComponent(UIGameMapCtrl).hideCityNode();
  //   }
  // }

  private on_click_btn_pet() {
    // if (!GameDirector.instance.isSystemOpen(SystemOpenEnum.PET_宠物系统)) {
    //   TipsMgr.showTip("该功能暂未开放");
    //   return;
    // }
    if (!CityModule.data.cityAggregateMessage.otherCreateCityList.includes(OtherBuildIdEnum.灵兽)) {
      if (GameDirector.instance.isSystemOpen(SystemOpenEnum.PET_宠物系统)) {
        CityModule.api.createOtherCity(OtherBuildIdEnum.灵兽, () => {
          this.node.getComponent(UIGameMapCtrl).onBuildPet();
        });
      } else {
        TipsMgr.showTip(GameDirector.instance.getUnLockMsg(SystemOpenEnum.PET_宠物系统));
      }
      return;
    }
    AudioMgr.instance.playEffect(1920);

    UIMgr.instance.showDialog(PetRouteItem.UIPetList);
  }

  private on_click_btn_fracture() {
    AudioMgr.instance.playEffect(1761);

    if (GameDirector.instance.isSystemOpen(FRACTURE_ACTIVITYID)) {
      RouteManager.uiRouteCtrl.showRoute(UIFractureSearch);
    } else {
      if (GameDirector.instance.isSystemOpen(SystemOpenEnum.FRACTURE_时空裂隙)) {
        TipsMgr.showTip("活动未开启");
      } else {
        TipsMgr.showTip(GameDirector.instance.getUnLockMsg(SystemOpenEnum.FRACTURE_时空裂隙));
      }
    }
  }

  //
  private on_click_btn_test() {
    TipsMgr.topRouteCtrl.showPrefab(BundleEnum.BUNDLE_EXT_GUIDE, "prefab/top/TopFusu", {});
  }
}
