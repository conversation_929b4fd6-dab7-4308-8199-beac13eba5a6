import { _decorator, Component, Node } from "cc";
import { Section } from "../../../lib/object/Section";
import GameObject from "../../../lib/object/GameObject";
import FightManager from "../manager/FightManager";
import { RecoverManager } from "../manager/RecoverManager";
import { RecoverDetail } from "../FightDefine";
import { TipManager } from "../manager/TipManager";
const { ccclass, property } = _decorator;

@ccclass("recoverSection")
export class recoverSection extends Section {
  public static sectionName(): string {
    return "recoverSection";
  }

  public onInit(go: GameObject, args) {
    super.onInit(go, args);
    this.ready();
    this.onMsg("onRoleRecover", this.onRoleRecover.bind(this));
  }

  public onStart() {
    super.onStart();
  }

  private onRoleRecover(detail: RecoverDetail) {
    let recoverRole = detail.goRole;
    let recover = detail.recover;
    recoverRole.getRoundDetail().a += recover;
    recoverRole.emitMsg("OnHurt");
    FightManager.instance.getSection(RecoverManager).callRecover(detail);
  }

  public onRemove(): void {
    this.offMsg("onRoleRecover", this.onRoleRecover.bind(this));
  }
}
