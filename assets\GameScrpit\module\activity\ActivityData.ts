import MsgEnum from "../../game/event/MsgEnum";
import { LeaderFundMessage } from "../../game/net/protocol/Activity";
import MsgMgr from "../../lib/event/MsgMgr";
import { BaseActivityConfigVO } from "./ActivityConfig";

export class ActivityData {
  // @persistent
  private _allActivityConfig: { [key: number]: BaseActivityConfigVO };

  public get allActivityConfig(): { [key: number]: BaseActivityConfigVO } {
    return this._allActivityConfig;
  }
  public set allActivityConfig(value: { [key: number]: BaseActivityConfigVO }) {
    this._allActivityConfig = value;
    MsgMgr.emit(MsgEnum.ON_ACTIVITY_UPDATE, value);
  }

  //修行基金
  private _leaderFundMessage: LeaderFundMessage;

  public get leaderFundMessage(): LeaderFundMessage {
    return this._leaderFundMessage;
  }
  public set leaderFundMessage(value: LeaderFundMessage) {
    this._leaderFundMessage = value;
  }
}
