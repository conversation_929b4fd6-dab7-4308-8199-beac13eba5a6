import { _decorator, EventTouch, Node } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { UIClubAvatar } from "./UIClubAvatar";
import { ClubModule } from "../../../module/club/ClubModule";
import { UIMgr } from "../../../lib/ui/UIMgr";
import TipMgr from "../../../lib/tips/TipMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

/**
 *
 * <PERSON><PERSON>_huang
 * Mon Sep 09 2024 17:15:36 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/club/UIClubEditAvatar.ts
 *
 */

@ccclass("UIClubEditAvatar")
export class UIClubEditAvatar extends UINode {
  protected _openAct: boolean = true; //打开动作
  private color: number = 1;
  private pic: number = 1;
  callback: any;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_CLUB}?prefab/ui/UIClubEditAvatar`;
  }
  //=================================================
  public init(args: any): void {
    super.init(args);
    this.callback = args?.callback;
    if (args?.avatar) {
      this.color = args.avatar.split("_")[0];
      this.pic = args.avatar.split("_")[1];
    }
  }
  protected onEvtShow(): void {
    super.onEvtShow();

    // if (ClubModule.data.clubMessage?.avatar) {
    //   // log.log(ClubModule.data.clubMessage.avatar);
    //   let ava = ClubModule.data.clubMessage.avatar.split("_");
    //   if (ava.length == 2) {
    //     this.color = Number(ava[0]);
    //     this.pic = Number(ava[1]);
    //   } else {
    //     this.color = 1;
    //     this.pic = 1;
    //   }
    //   this.getNode("UIClubAvatar").getComponent(UIClubAvatar).setAvatar(`${this.color}_${this.pic}`);
    // }
    this.getNode("UIClubAvatar").getComponent(UIClubAvatar).setAvatar(`${this.color}_${this.pic}`);
    this.getNode("node_pic_select").children.forEach((child) => {
      child.on(Node.EventType.TOUCH_END, this.onPicSelect, this);
      if (Number(child.name.split("_")[2]) == this.pic) {
        child.getChildByName("used").active = true;
        child.getChildByName("used_check").active = true;
      } else {
        child.getChildByName("used").active = false;
        child.getChildByName("used_check").active = false;
      }
      child.getChildByName("select").active = false;
    });

    this.getNode("node_color_select").children.forEach((child) => {
      child.on(Node.EventType.TOUCH_END, this.onColorSelect, this);
      let color = child.name.split("_")[2];
      if (Number(color) == this.color) {
        child.getChildByName("used").active = true;
        child.getChildByName("used_check").active = true;
      } else {
        child.getChildByName("used").active = false;
        child.getChildByName("used_check").active = false;
      }
      child.getChildByName("select").active = false;
    });
  }
  private onColorSelect(event: EventTouch) {
    this.color = event.target.name.split("_")[2];
    this.getNode("node_color_select").children.forEach((child) => {
      // child.getChildByName("used").active = false;
      // child.getChildByName("used_check").active = true;
      child.getChildByName("select").active = false;
    });
    // event.target.getChildByName("used").active = true;
    // event.target.getChildByName("used_check").active = false;
    event.target.getChildByName("select").active = true;
    log.log("onColorSelect", event.target.name);
    this.getNode("UIClubAvatar").getComponent(UIClubAvatar).setAvatar(`${this.color}_${this.pic}`);
  }
  private onPicSelect(event: EventTouch) {
    this.pic = event.target.name.split("_")[2];
    this.getNode("node_pic_select").children.forEach((child) => {
      // child.getChildByName("used").active = false;
      // child.getChildByName("used_check").active = true;
      child.getChildByName("select").active = false;
    });
    // event.target.getChildByName("used").active = true;
    // event.target.getChildByName("used_check").active = false;
    event.target.getChildByName("select").active = true;
    log.log("onPicSelect", event.target.name);
    this.getNode("UIClubAvatar").getComponent(UIClubAvatar).setAvatar(`${this.color}_${this.pic}`);
  }
  private on_click_btn_commit(event: EventTouch) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    if (this.callback) {
      this.callback(`${this.color}_${this.pic}`);
    } else {
      ClubModule.api.modifyAvatar(`${this.color}_${this.pic}`, () => {
        TipMgr.showTip("修改成功");
      });
    }
    UIMgr.instance.back();
  }
}
