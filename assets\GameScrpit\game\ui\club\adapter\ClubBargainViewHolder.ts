import { _decorator, instantiate, Label, Node } from "cc";
import { ListAdapter, ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { BargainRecordMessage } from "../../../net/protocol/Club";

const { ccclass, property } = _decorator;
@ccclass("ClubBargainViewHolder")
export class ClubBargainViewHolder extends ViewHolder {
  @property(Label)
  private lblPlayerName: Label;
  @property(Label)
  private lblBargain: Label;
  public init() {}
  public updateData(position: number, data: BargainRecordMessage) {
    this.lblPlayerName.string = `${position + 1}.${data.nickname}`;
    this.lblBargain.string = `${data.deductPrice}`;
  }
}
export class ClubBargainAdapter extends ListAdapter {
  private item: Node;
  private data: any[];
  public constructor(item: Node) {
    super();
    this.item = item;
  }
  public setData(data: any[]) {
    this.data = data;
    this.notifyDataSetChanged();
  }

  getViewType(position: number): number {
    return 0;
  }
  onCreateView(viewType: number): Node {
    let item = instantiate(this.item);
    item.active = true;
    item.getComponent(ClubBargainViewHolder).init();
    return item;
  }
  onBindData(view: Node, position: number): void {
    view.getComponent(ClubBargainViewHolder).updateData(position, this.data[position]);
  }
  getCount(): number {
    return this.data.length;
  }
}
