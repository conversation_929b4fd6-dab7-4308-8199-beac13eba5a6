import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import MsgEnum from "../../game/event/MsgEnum";
import { GameDirector } from "../../game/GameDirector";
import { ApiHandler, ApiHandlerFail, ApiHandlerSuccess } from "../../game/mgr/ApiHandler";
import { FriendSubCmd } from "../../game/net/cmd/CmdData";
import { DoubleValueList, IntValue, LongValue, LongValueList } from "../../game/net/protocol/ExternalMessage";
import {
  FriendCitySkillRequest,
  FriendCitySkillMessage,
  FriendMessage,
  FriendHeroSkillRequest,
  FriendHeroSkillResponse,
  FriendChatRequest,
  FriendChatResponse,
  FriendGiftRequest,
  FriendGiftResponse,
  FriendVitalityMessage,
  FriendLabelMessage,
} from "../../game/net/protocol/Friend";
import MsgMgr from "../../lib/event/MsgMgr";
import { FriendModule } from "./FriendModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);

export class FriendApi {
  public getAll(success?: ApiHandlerSuccess) {
    ApiHandler.instance.list(FriendMessage, FriendSubCmd.getAllFriend, null, (data: FriendMessage[]) => {
      FriendModule.data.setFriendMessageList(data);
      success && success(data);
    });
  }
  /**
   * 技能洗练
   * @param friendId 挚友id
   * @param rank
   * @param consumeitem
   * @param success
   */
  public improveSkill(friendId: number, rank: number, consumeitem: boolean, success?: ApiHandlerSuccess) {
    let data: FriendCitySkillRequest = {
      friendId: friendId,
      rank: rank,
      consumeItem: consumeitem,
    };
    log.log("apiImproveSkill", friendId, rank, consumeitem);
    ApiHandler.instance.requestSync(
      FriendCitySkillMessage,
      FriendSubCmd.improveSkill,
      FriendCitySkillRequest.encode(data),
      (data: FriendCitySkillMessage) => {
        log.log(data);
        FriendModule.data.updateFriendCitySkill(friendId, rank, data);
        MsgMgr.emit(MsgEnum.ON_FRIEND_CITY_SKILL_UPDATE);
        success && success(data);
      },
      (errorCode: any, msg: string[], data: any) => {
        log.log(`${errorCode}`);
        log.log(data);
        return true;
      }
    );
  }
  public levelUpHeroSkill(friendId: number, rank: number, success?: (data: FriendHeroSkillResponse) => void) {
    let data: FriendHeroSkillRequest = {
      friendId: friendId,
      rank: rank,
    };
    ApiHandler.instance.requestSync(
      FriendHeroSkillResponse,
      FriendSubCmd.levelUpHeroSkill,
      FriendHeroSkillRequest.encode(data),
      (data: FriendHeroSkillResponse) => {
        log.log(data);
        let friend = FriendModule.data.getFriendMessage(friendId);
        friend.heroSkillList[rank] = data.level;
        friend.friendShip = data.friendShip;
        FriendModule.data.setFriendMessage(friendId, friend);
        MsgMgr.emit(MsgEnum.ON_FRIEND_HERO_SKILL_UPDATE);
        success && success(data);
      },
      (errorCode: any, msg: string[], data: any) => {
        log.log(`${errorCode}`);
        log.log(data);
        return true;
      }
    );
  }
  /**
   * 谈心
   * @param isOneKey 是否一键谈心
   * @param success 谈心成功回调
   */
  public chat(isOneKey: boolean, success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    let data: FriendChatRequest = {
      chatMore: isOneKey,
    };
    log.log(data);
    ApiHandler.instance.requestSync(
      FriendChatResponse,
      FriendSubCmd.chat,
      FriendChatRequest.encode(data),
      (data: FriendChatResponse) => {
        log.log(data);
        for (let key in data.chatStockMap) {
          //更新每一个挚友的 friendship
          let friend = FriendModule.data.getFriendMessage(Number(key));
          friend.friendShip = data.chatStockMap[key];
          FriendModule.data.setFriendMessage(Number(key), friend);
        }
        FriendModule.data.vitalityMessage = data.vitalityMessage;
        success && success(data);
        log.log(data);
      },
      (errorCode: any, msg: string[], data: any) => {
        log.log(`${errorCode}`);
        log.log(data);
        error && error(errorCode, msg, data);
        return true;
      }
    );
  }
  /**
   * 赠送礼物
   * @param friendId
   * @param itemId
   * @param giftMore
   * @param success
   */
  public giveGift(friendId: number, itemId: number, giftMore: boolean, success?: ApiHandlerSuccess) {
    let data: FriendGiftRequest = {
      friendId: friendId,
      itemId: itemId,
      giftMore: giftMore,
    };
    log.log(data);
    ApiHandler.instance.requestSync(
      FriendGiftResponse,
      FriendSubCmd.giveAGift,
      FriendGiftRequest.encode(data),
      (data: FriendGiftResponse) => {
        log.log(data);
        let friend = FriendModule.data.getFriendMessage(friendId);
        friend.karma = data.karma;
        friend.destiny = data.destiny;
        FriendModule.data.setFriendMessage(friendId, friend);
        success && success(data);
      },
      (errorCode: any, msg: string[], data: any) => {
        log.log(`${errorCode}`);
        log.log(data);
        TipsMgr.showErrX(errorCode, msg);
        return true;
      }
    );
  }
  /**
   * 使用精力丹恢复挚友的精力
   * @param num 数量
   * @param success
   */
  public addvitality(num: number, success?: (data: any) => void) {
    let data: IntValue = {
      value: num,
    };
    ApiHandler.instance.request(
      FriendVitalityMessage,
      FriendSubCmd.addVitality,
      IntValue.encode(data),
      (data: FriendVitalityMessage) => {
        FriendModule.data.vitalityMessage = data;
        success && success(data);
      }
    );
  }
  public getVitality(success?: (data: any) => void) {
    ApiHandler.instance.request(
      FriendVitalityMessage,
      FriendSubCmd.getVitality,
      null,
      (data: FriendVitalityMessage) => {
        log.log(data);
        FriendModule.data.vitalityMessage = data;
        success && success(data);
      }
    );
  }

  public levelUpFame(friendId: number, success?: (data: LongValue) => void) {
    log.log("levelUpFame", friendId);
    ApiHandler.instance.requestSync(
      LongValue,
      FriendSubCmd.levelUpFame,
      LongValue.encode({
        value: friendId,
      }),
      (data: LongValue) => {
        log.log(data);
        let firend = FriendModule.data.getFriendMessage(friendId);
        firend.fameLv = data.value;
        FriendModule.data.setFriendMessage(friendId, firend);
        FriendModule.data.updateFriendBells();
        success && success(data);
      },
      (errorCode: any, msg: string[], data: any) => {
        log.error(`${errorCode}`);
        log.error(data);
        return false;
      }
    );
  }

  public getOneFriend(friendId: number, success?: (data: FriendMessage) => void) {
    let data: LongValue = {
      value: friendId,
    };
    ApiHandler.instance.request(
      FriendMessage,
      FriendSubCmd.getFriend,
      LongValue.encode(data),
      (data: FriendMessage) => {
        FriendModule.data.addFriendMessage(data.friendId, data);
        success && success(data);

        // 检查模块开启
        GameDirector.instance.checkAndLoadModule(true);
      },
      (errorCode: any, msg: string[], data: any) => {
        log.error(`${errorCode}`);
        log.error(data);
        return true;
      }
    );
  }

  public unLockLabelFriend(friendId: number, success?: (data: LongValue) => void) {
    log.log("unLockLabelFriend", friendId);
    ApiHandler.instance.request(
      LongValue,
      FriendSubCmd.unLockLabelFriend,
      LongValue.encode({ value: friendId }),
      (data: LongValue) => {
        // 仙友会通过10-1推送
        // FriendModule.api.getOneFriend(friendId, () => {
        // });
        success && success(data);
        MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: [data.value, 1] }, this);
        // ToolExt.setResStock([data.value, 1]);
      }
    );
  }
  public getPicture(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(LongValueList, FriendSubCmd.getPicture, null, (data: LongValueList) => {
      log.log(data);
      FriendModule.data.pictureMessage = data.values;
      MsgMgr.emit(MsgEnum.ON_FRIEND_UPDATE);
      success && success(data);
    });
  }
  public takePictureReward(pictureId: number, success?: ApiHandlerSuccess) {
    ApiHandler.instance.requestSync(
      DoubleValueList,
      FriendSubCmd.takePictureReward,
      LongValue.encode({ value: pictureId }),
      (data: DoubleValueList) => {
        log.log(data);
        //领取成功后更新数据
        this.getPicture(() => {
          success && success(data);
        });
      }
    );
  }
  public testDelFriend(friendId: number, success?: ApiHandlerSuccess) {
    ApiHandler.instance.requestSync(
      LongValue,
      FriendSubCmd.testDelFriend,
      LongValue.encode({ value: friendId }),
      (data: LongValue) => {
        log.log(data);
        FriendModule.data.delFriendMessage(friendId);
        success && success(data);
      }
    );
  }
}
