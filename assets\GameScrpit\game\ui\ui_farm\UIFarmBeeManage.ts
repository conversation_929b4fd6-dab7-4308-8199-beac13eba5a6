import { _decorator, instantiate, Label, Node } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { FarmModule } from "../../../module/farm/FarmModule";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { JsonMgr } from "../../mgr/JsonMgr";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { PlayerRouteName, PublicRouteName } from "../../../module/player/PlayerConstant";
import MsgMgr from "../../../lib/event/MsgMgr";
import { FarmEvent } from "../../../module/farm/FarmEvent";
import { FarmBeeMetricMessage, FarmDispatchMessage } from "../../net/protocol/Farm";
import TickerMgr from "../../../lib/ticker/TickerMgr";
import { ItemEnum } from "../../../lib/common/ItemEnum";
import { FarmSlotAdapter } from "./adapter/FarmSlotAdapter";
import { FmColor } from "../../common/FmConstant";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { IConfigBlessLand, IConfigBlessLandBee } from "../../JsonDefine";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { FarmAudioName } from "../../../module/farm/FarmConfig";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { LangMgr } from "../../mgr/LangMgr";
import { ConfirmMsg } from "../UICostConfirm";
import { AdapterView } from "../../../../platform/src/core/ui/adapter_view/AdapterView";
import { FarmRouteName } from "../../../module/farm/FarmRoute";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UIFarmBeeManage")
export class UIFarmBeeManage extends UINode {
  protected _openAct: boolean = true;

  // bee所在工作情况
  private _slotAdapter: FarmSlotAdapter;

  // 是否有购买
  private _isBuy: boolean = false;

  // 今日可采集次数
  lbl_title: Label;
  // 已解锁 2/7
  lbl_work_unlock_num: Label;
  //
  lbl_next_unlock: Label;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_FARM}?prefab/ui/UIFarmBeeManage`;
  }
  protected dependOn(): BundleEnum[] {
    return [BundleEnum.BUNDLE_COMMON_UI, BundleEnum.BUNDLE_COMMON_ITEM];
  }

  /**定时器id */
  private _recoverTickId: number;

  protected onRegEvent() {
    MsgMgr.on(FarmEvent.FARM_REFRESH, this.refresh, this);

    this._slotAdapter = new FarmSlotAdapter(this.getNode("slot_item_viewholder"));
    this.getNode("list_my_hulu").getComponent(AdapterView).setAdapter(this._slotAdapter);
  }

  protected onDelEvent() {
    MsgMgr.off(FarmEvent.FARM_REFRESH, this.refresh, this);
  }

  protected onEvtShow(): void {
    this.refresh();
    this.getNode("lbl_start_work").getComponent(Label).string = LangMgr.txMsgCode(457);
  }

  protected onEvtClose(): void {
    TickerMgr.clearInterval(this._recoverTickId);
  }

  private refresh() {
    // 取通用配置
    const cfg1: IConfigBlessLand = FarmModule.data.getConfigBlessLand(1);

    // 今日可采集次数
    let remainFetch = FarmModule.data.farmTrainMessage.remainFetch;
    this.getNode("lbl_title").getComponent(Label).string = LangMgr.txMsgCode(456, [remainFetch, cfg1.fetchMax]);

    // 总采集次数
    const totalColCnt: number = FarmModule.data.farmTrainMessage.totalColCnt || 0;
    // 已解锁数量-客户端计算
    let unlockWorker = 0;
    // 下一只需要的数量
    let nextNeedCount = 0;
    let keys = Object.keys(JsonMgr.instance.jsonList.c_blessLandBee).map(Number);
    for (let key of keys) {
      let cfg2: IConfigBlessLandBee = JsonMgr.instance.jsonList.c_blessLandBee[key];
      if (cfg2.unlock <= totalColCnt) {
        unlockWorker = cfg2.id;
      } else {
        nextNeedCount = cfg2.unlock;
        break;
      }
    }
    this.getNode("lbl_work_unlock_num").getComponent(Label).string = LangMgr.txMsgCode(
      999999,
      [unlockWorker, keys.length],
      "999999 已解锁 s%/s%"
    );

    // 下一只采集状态
    this.getNode("lbl_next_unlock").getComponent(Label).string = LangMgr.txMsgCode(
      999999,
      [unlockWorker + 1, totalColCnt, nextNeedCount],
      "999999-第s%只猴子累计采集 s%/s%次解锁"
    );

    // 员工状态
    let beeMetric = FarmModule.data.farmTrainMessage.beeMetric;
    this.getNode("lbl_relax").getComponent(Label).string = `【${beeMetric.relaxBeeCnt}】`;
    this.getNode("lbl_total").getComponent(Label).string = `【${beeMetric.totalBeeCnt}】`;

    // 可派遣
    this.getNode("lbl_collect_idle").getComponent(Label).string = LangMgr.txMsgCode(
      999999,
      [beeMetric.relaxBeeCnt, beeMetric.totalBeeCnt],
      "999999-可派遣[s%/s%]"
    );

    // 已雇傭数量
    const hireCount = cfg1.hireMax - FarmModule.data.farmTrainMessage.beeMetric.canHireBeeCnt;
    // 可雇傭总数
    const maxHire = cfg1.hireMax;
    this.getNode("lbl_hire_info").getComponent(Label).string = `${hireCount}/${maxHire}`;

    // 新雇佣消耗
    let costIdx = hireCount >= cfg1.hireCostList.length ? cfg1.hireCostList.length - 1 : hireCount;
    const hireCose = cfg1.hireCostList[costIdx];

    // 仙玉数量
    const xianyuNum = PlayerModule.data.getItemNum(ItemEnum.仙玉_6);
    const lblHireCost = this.getNode("lbl_hire_cost").getComponent(Label);
    lblHireCost.string = `${xianyuNum}/${hireCose}`;
    if (hireCose > xianyuNum) {
      lblHireCost.color = FmColor.COLOR_RED_LIGHT;
    } else {
      lblHireCost.color = FmColor.COLOR_BLUE_LIGHT;
    }

    // 可雇佣情况
    this.getNode("lbl_hire_count").getComponent(Label).string = LangMgr.txMsgCode(
      999999,
      [beeMetric.canHireBeeCnt, cfg1.hireMax],
      "999999-已雇佣[s%/s%]"
    );

    const huluList: { key: string; dispatch: FarmDispatchMessage }[] = [];
    // 福地列表，被他人采，自己没采的
    for (let key in FarmModule.data.farmTrainMessage.slotList) {
      let slot = FarmModule.data.farmTrainMessage.slotList[key];
      if (slot.otherCollectorMessage?.beeNum && !slot.ownCollectorMessage?.beeNum) {
        const slot1: FarmDispatchMessage = {
          otherMessage: slot.otherCollectorMessage.playerBaseMessage,
          gourdId: slot.gourdId,
          farmerMessage: {
            userId: PlayerModule.data.playerId,
            nickname: PlayerModule.data.getPlayerInfo().nickname,
            avatarList: [],
            sex: 0,
            level: 0,
            vipLevel: 0,
            hiddenVip: false,
          },
          ownBeeCnt: 0,
          otherBeeCnt: slot.otherCollectorMessage.beeNum,
          win: false,
          endTime: slot.otherCollectorMessage.endTime,
          startTime: 0,
          remainTime: 0,
        };
        huluList.push({ key: key, dispatch: slot1 });
      }
    }

    // 猴子列表
    for (let key in FarmModule.data.farmTrainMessage.dispatchMap) {
      huluList.push({ key: key, dispatch: FarmModule.data.farmTrainMessage.dispatchMap[key] });
    }

    this._slotAdapter.setDatas(huluList);

    this.getNode("lbl_no_working").active = huluList.length <= 0;
  }

  // 解锁帮助信息
  on_click_btn_help() {
    // todo 提示信息
    TipsMgr.topRouteCtrl.showPrefab(BundleEnum.BUNDLE_G_FARM, "prefab/top/TopUnlockHint", {
      initWorldPos: this.getNode("btn_help").getWorldPosition(),
    });
  }

  /**
   * 全部招回事件
   */
  private on_click_btn_call_back_all(event) {
    AudioMgr.instance.playEffect(FarmAudioName.Effect.管理点击全部召回);
    let msg: ConfirmMsg = {
      msg: LangMgr.txMsgCode(482, [], "是否召回当前所有猴子？"),
      itemList: [],
      stopHintOption: false,
    };
    UIMgr.instance.showDialog(PublicRouteName.UICostConfirm, msg, (resp) => {
      if (resp?.ok) {
        FarmModule.api.recallAll();
      }
    });
  }

  on_click_btn_start_work() {
    AudioMgr.instance.playEffect(FarmAudioName.Effect.探寻点击前往);
    UIMgr.instance.showDialog(FarmRouteName.UIFarmFind);
  }

  private on_click_dialog_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    UIMgr.instance.back({ resp: { ok: this._isBuy } });
  }

  private on_click_btn_close_bg() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    UIMgr.instance.back({ resp: { ok: this._isBuy } });
  }

  private on_click_btn_hire() {
    AudioMgr.instance.playEffect(FarmAudioName.Effect.管理点击雇佣);
    // 取第一条，用到的数据都是通用的数据
    let config: IConfigBlessLand = FarmModule.data.getConfigBlessLand(1);

    if (FarmModule.data.farmTrainMessage.beeMetric.canHireBeeCnt <= 0) {
      TipsMgr.showTip(LangMgr.txMsgCode(460)); //没有可雇佣的猴子
      return;
    }

    // 已雇傭数量
    let hireBee = config.hireMax - FarmModule.data.farmTrainMessage.beeMetric.canHireBeeCnt;

    if (PlayerModule.data.getItemNum(ItemEnum.仙玉_6) < config.hireCostList[hireBee]) {
      UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
        itemId: ItemEnum.仙玉_6,
        needNum: config.hireCostList[hireBee],
      });
      return;
    }

    if (FarmModule.data.stopHintHire) {
      this.hireWorker();
      return;
    }

    // 提示不能开启
    const msg: ConfirmMsg = {
      msg: LangMgr.txMsgCode(451, [config.hireCostList[hireBee]], "是否花费s%仙玉雇佣一只猴子？"),
      okText: LangMgr.txMsgCode(140, [], "确认"),
      itemList: [ItemEnum.仙玉_6, config.hireCostList[hireBee]],
      stopHintOption: true,
    };

    UIMgr.instance.showDialog(PublicRouteName.UICostConfirm, msg, (resp) => {
      if (resp?.ok) {
        FarmModule.data.stopHintHire = resp.stopHint;

        this.hireWorker();
      }
    });
  }

  hireWorker() {
    TipsMgr.setEnableTouch(false, 7);
    FarmModule.api.hireBee(1, (data: FarmBeeMetricMessage) => {
      this._isBuy = true;

      // 更新所有数据
      FarmModule.data.farmTrainMessage.beeMetric = data;

      // 通知相关模块
      MsgMgr.emit(FarmEvent.FARM_REFRESH);

      this.assetMgr.loadPrefab(BundleEnum.BUNDLE_G_FARM, "prefab/top/FarmRewardWorker", (prefab) => {
        const nodeShow = instantiate(prefab);
        TipsMgr.showTipNode(nodeShow);
      });
    });
  }
}
