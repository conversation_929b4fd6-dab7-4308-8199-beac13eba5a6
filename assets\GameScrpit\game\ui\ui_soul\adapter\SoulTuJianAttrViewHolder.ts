import { _decorator, instantiate, Label, Layout, Node, UITransform } from "cc";
import { ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { divide } from "db://assets/GameScrpit/lib/utils/NumbersUtils";
import { SoulTujianAttr } from "../SoulTujianAttr";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;
@ccclass("SoulTuJianAttrViewHolder")
export class SoulTuJianAttrViewHolder extends ViewHolder {
  @property(Label)
  private lblAttrTitle: Label;
  @property(Node)
  private nodeAttrs: Node;
  updateData(data: any, position: number) {
    this.lblAttrTitle.string = `洗练时,有${divide(data.refreshRate[position], 100)}% 几率出现如下之一属性:`;
    let index = 0;
    for (let i = 0; i < data.refreshAttr[position].length; ) {
      let child = this.nodeAttrs.children[index];
      if (!child) {
        child = instantiate(this.nodeAttrs.children[0]);
        child.parent = this.nodeAttrs;
      }
      child.active = true;
      child
        .getComponent(SoulTujianAttr)
        .setAttrId(data.id, data.refreshAttr[position][i++], divide(data.refreshAttr[position][i++], 10000));
      index++;
    }
    for (; index < this.nodeAttrs.children.length; index++) {
      this.nodeAttrs.children[index].active = false;
    }
    this.nodeAttrs.getComponent(Layout).updateLayout();
    this.node.getComponent(Layout).updateLayout();
    let height = this.node.getComponent(UITransform).contentSize.height;
    log.log("height", height);
  }
}
