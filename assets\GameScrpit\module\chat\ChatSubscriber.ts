import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../game/mgr/ApiHandler";
import { ChatPackMessage } from "../../game/net/protocol/Chat";
import { ChatSubCmd } from "./ChatConstant";
import { ChatModule } from "./ChatModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class ChatSubscriber {
  private onNewMessage(data: ChatPackMessage) {
    log.log("收到消息 ", data);
    ChatModule.data.addNewMessage(data.marketType, data);
  }
  public register() {
    //订阅服务器消息
    ApiHandler.instance.subscribe(ChatPackMessage, ChatSubCmd.newMessage, this.onNewMessage);
  }
  public unRegister() {
    //取消订阅服务器消息
    ApiHandler.instance.unSubscribe(ChatSubCmd.newMessage, this.onNewMessage);
  }
}
