import { _decorator } from "cc";
import { JsonMgr } from "../../../game/mgr/JsonMgr";
import { LangMgr } from "../../../game/mgr/LangMgr";
const { ccclass, property } = _decorator;

@ccclass("fightService")
export class fightService {
  /**
   * 根据ID获取关卡名称
   *
   * @param buildId 关卡id
   * @returns
   */
  public getLevelName(buildId: number): string {
    let levelName = JsonMgr.instance.jsonList.c_build[buildId]?.name;
    if (!levelName) {
      levelName = LangMgr.txMsgCode(196, [], "关卡");
    }
    return levelName;
  }
}
