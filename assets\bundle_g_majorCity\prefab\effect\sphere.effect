
CCEffect %{
techniques:
  - passes:
      - vert: sprite-vs:vert
        frag: sprite-fs:frag
        depthStencilState:
          depthTest: false
          depthWrite: false
        blendState:
          targets:
            - blend: true
              blendSrc: src_alpha
              blendDst: one_minus_src_alpha
              blendDstAlpha: one_minus_src_alpha
        rasterizerState:
          cullMode: none
        properties:
          map_1: { value: white }
          map_2: { value: white }
          map_3: { value: white }
          #
          fov: { value: [0.5, 0.5] }
          center: { value: [0.5, 0.5] }
          radian: { value: [0.5, 0.5] }
          # 偏移
          offset: { value: [0, 0] }
          # 强度
          strength: { value: 1.0 }

}%

CCProgram sprite-vs %{
precision highp float;
#include <builtin/uniforms/cc-global>
#if USE_LOCAL
  #include <builtin/uniforms/cc-local>
#endif

in vec3 a_position;
in vec2 a_texCoord;
in vec4 a_color;

out vec4 v_color;
out vec2 v_uv0;

#if USE_TEXTURE
  in vec2 a_uv0;
#endif

vec4 vert() {
  vec4 pos = vec4(a_position, 1);
  
  #if USE_LOCAL
    pos = cc_matWorld * pos;
  #endif
  
  #if USE_PIXEL_ALIGNMENT
    pos = cc_matView * pos;
    pos.xyz = floor(pos.xyz);
    pos = cc_matProj * pos;
  #else
    pos = cc_matViewProj * pos;
  #endif
  
  #if USE_TEXTURE
    v_uv0 = a_uv0;
  #endif
  
  v_color = a_color;
  v_uv0 = a_texCoord;
  
  return pos;
}
}%

CCProgram sprite-fs %{
precision highp float;
#include <builtin/internal/embedded-alpha>
#include <builtin/internal/alpha-test>
#include <builtin/uniforms/cc-global>

in vec4 v_color;

#if USE_TEXTURE
  in vec2 v_uv0;
  uniform sampler2D map_1;
  uniform sampler2D map_2;
  uniform sampler2D map_3;
  #pragma builtin(local)
  layout(set = 2, binding = 11)uniform sampler2D cc_spriteTexture;
#endif

uniform ARGS {
  vec2 fov;
  vec2 radian;
  vec2 center;
  vec2 offset;
  float strength;
};
const float PI = 3.1415926535;

vec4 frag() {
  
  // float dist = distance(v_uv0, center);
  // if (dist > 0.5) {
  //   discard;
  // }
  // vec2 uv = applyFishEye(v_uv0, 0.5);
  // float factor = asin(dist * (PI / 2.0) * strength);
  // vec2 uv = (v_uv0 - center) * factor + center ;
  vec2 uv = v_uv0;
  uv -= center;
  uv /= radian;
  
  // 如果当前像素距离屏幕中心过远，则设置为黑色
  if (length(uv) > 1.0) {
    // fragColor = vec4(0.0, 0.0, 0.0, 1.0);
    discard;
  }
  // uv = mix(uv, normalize(uv) * (2.0 * asin(length(uv)) / 3.1415926), 0.5);
  uv = normalize(uv) * (2.0 * asin(length(uv)) / 3.1415926);
  // vec3 n = vec3(uv, sqrt(1.0 - uv.x * uv.x - uv.y * uv.y));
  // uv = normalize(uv) * (2.0 * asin(length(uv)) / 3.1415926);
  
  uv *= fov;
  uv = uv + offset;
  
  // uv *= 2.0;
  // float d = length(uv );
  // float z = sqrt(1.0 - d * d);
  // float r = atan(d, z) / 3.14159;
  // float phi = atan(uv.y, uv.x);
  
  // uv = vec2(r * cos(phi) + 0.5, r * sin(phi) + 0.5);
  
  // 将纹理坐标转换为球面坐标系下的单位向量
  // vec2 uv = v_uv0 * 2.0 - vec2(1.0); // 纹理坐标转换为[-1, 1]范围
  // float r = length(uv);
  // if (r > 1.0) {
  //   discard; // 超出单位圆范围的部分丢弃
  // }
  if (uv.y > 1.0||uv.y < 0.0) {
    discard;
  }
  
  // vec3 normal = vec3(uv, sqrt(1.0 - r * r)); // 计算单位球面坐标系下的法线向量
  // return texture(cc_spriteTexture, normal.xy * 0.5 + 0.5);
  if (uv.x >= 2.333) {
    uv.x -= 2.333;
    // if (uv.y > 1.0) {
    //   uv.y = uv.y - 1.0;
    // }else if (uv.y < 0.0) {
    //   uv.y = uv.y + 1.0;
    // }
    // uv.y /= 2.0;
    return texture(map_1, uv);
  }else if (uv.x >= 0.0&&uv.x < 1.0) {
    // if (uv.y > 1.0) {
    //   uv.y = uv.y - 1.0;
    // }else if (uv.y < 0.0) {
    //   uv.y = uv.y + 1.0;
    // }
    // uv.y /= 2.0;
    return texture(map_1, uv);
  }else if (uv.x >= 1.0&&uv.x < 2.0) {
    uv.x = uv.x - 1.0;
    // if (uv.y > 1.0) {
    //   uv.y = uv.y - 1.0;
    // }else if (uv.y < 0.0) {
    //   uv.y = uv.y + 1.0;
    // }
    // uv.y /= 2.0;
    return texture(map_2, uv);
  }else if (uv.x >= 2.0&&uv.x < 2.333) {
    uv.x = (uv.x - 2.0) * 3.0;
    // if (uv.y > 1.0) {
    //   uv.y = uv.y - 1.0;
    // }else if (uv.y < 0.0) {
    //   uv.y = uv.y + 1.0;
    // }
    // uv.y /= 2.0;
    return texture(map_3, uv);
  }else if (uv.x < 0.0) {
    uv.x = uv.x * 3.0 + 1.0;
    // if (uv.y > 1.0) {
    //   uv.y = uv.y - 1.0;
    // }else if (uv.y < 0.0) {
    //   uv.y = uv.y + 1.0;
    // }
    // uv.y /= 2.0;
    return texture(map_3, uv);
  }
  // return texture(cc_spriteTexture, uv);
}


}%
