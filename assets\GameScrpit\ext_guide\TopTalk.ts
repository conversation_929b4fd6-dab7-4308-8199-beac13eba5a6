import { Label, RichText, instantiate, Node } from "cc";
import { _decorator } from "cc";
import { JsonMgr } from "../game/mgr/JsonMgr";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
import { sp } from "cc";
import { IConfigGuideTalk } from "../game/JsonDefine";
import { LayerEnum } from "../game/GameDefine";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
const log = Logger.getLoger(LOG_LEVEL.DEBUG);
const { ccclass, property } = _decorator;

const enum TalkEnum {
  INIT = 0,
  LOADING_SPINE = 1, // 加载中
  PLAY_TALK = 2, // 播放中
  COMPLETE = 3, // 播放完成
}

@ccclass("TipTalk")
export class TopTalk extends BaseCtrl {
  @property(Node)
  private nodeLeft: Node;

  @property(Node)
  private nodeRight: Node;

  @property(Label)
  private lblName: Label;

  @property(Node)
  private nodeMsg: Node;

  @property(RichText)
  private richtextMsg: RichText;

  @property(RichText)
  private richtextMsgShadow: RichText;

  @property(Node)
  private nodeSkip: Node;

  @property(Node)
  private bgShadow: Node;

  // 对话脚本列表
  private configTalkList: IConfigGuideTalk[];

  // 当前进入到哪个阶段
  private curTalkIdx: number = -1;

  // 当前状态
  private curState: TalkEnum = TalkEnum.INIT;

  // 循环间隔
  private cd = 0;

  // 已显示文字
  private strList: string[] = [];

  // 全部要显示的文字
  private strAllList: string[] = [];

  // 样式模板
  private tagList: string[] = [];

  init(args: { talkId: number; showShadow?: boolean }) {
    this.configTalkList = [];
    this.bgShadow.active = args.showShadow;
    Object.keys(JsonMgr.instance.jsonList.c_guideTalk).forEach((key) => {
      let talkConfig = JsonMgr.instance.jsonList.c_guideTalk[Number(key)];
      if (talkConfig.type == args.talkId) {
        this.configTalkList.push(talkConfig);
      }
    });
  }

  async start() {
    super.start();

    this.curTalkIdx = 0;
  }

  /**
   * 加载资源
   * @param nodeParent 父节点
   * @param spineId spine配置ID
   */
  async showPrefab(nodeParent: Node, spineId: number) {
    this.curState = TalkEnum.LOADING_SPINE;

    let nodeName = "role" + spineId;

    let nodeExist = nodeParent.getChildByName(nodeName);
    if (!nodeExist) {
      const configSpine = JsonMgr.instance.getConfigSpineShow(spineId);
      let rs = configSpine.prefabPath.split("?");

      let pb = await this.assetMgr.loadPrefabSync(rs[0], rs[1]);

      let nodeRole = instantiate(pb);
      nodeRole.walk((child) => {
        child.layer = LayerEnum.TOP;
      });

      nodeParent.addChild(nodeRole);
      nodeParent.active = true;

      // todo 抽象接口调用
      nodeRole.getChildByName("render").getComponent(sp.Skeleton).setAnimation(0, "boss_idle", true);

      nodeRole.name = nodeName;
    }
  }

  private splitMsg(msg: string) {
    this.strAllList = [];
    this.strList = [];
    this.tagList = [];

    let isTag = false;

    let str = "";

    for (let i = 0; i < msg.length; i++) {
      if (msg[i] == "<" && isTag == false) {
        this.strAllList.push(str);
        this.strList.push("");

        str = msg[i];
        isTag = true;
      } else if (msg[i] == ">") {
        str += msg[i];
        this.tagList.push(str);

        str = "";
        isTag = false;
      } else {
        str += msg[i];
      }
    }

    if (isTag) {
      log.error("对话脚本消息格式不正确");
    } else {
      this.strAllList.push(str);
      this.strList.push("");
    }
  }

  private addWords(): boolean {
    let isFinish = true;

    let isAdd = false;

    let msg = "";
    for (let i = 0; i < this.strAllList.length; i++) {
      if (!isAdd && this.strAllList[i].length != this.strList[i].length) {
        isAdd = true;
        isFinish = false;
        this.strList[i] = this.strAllList[i].substring(0, this.strList[i].length + 1);
      }

      msg += this.strList[i];
      if (this.tagList.length > i) {
        msg += this.tagList[i];
      }
    }

    this.richtextMsg.string = msg;
    this.richtextMsgShadow.string = msg;
    return isFinish;
  }

  update(deltaTime: number) {
    this.cd += deltaTime;
    if (this.cd < 0.1) {
      return;
    }
    this.cd = 0;

    if (this.curTalkIdx == -1 || this.curTalkIdx >= this.configTalkList.length) {
      return;
    }

    let configTalk = this.configTalkList[this.curTalkIdx];

    if (this.curState == TalkEnum.INIT) {
      // 说话位置界面调整
      let node = this.nodeLeft;
      this.nodeMsg.setPosition(95, -160);
      if (configTalk.position == 2) {
        this.nodeMsg.setPosition(0, -160);
        node = this.nodeRight;
      }

      this.showPrefab(node, configTalk.skinId).then(() => {
        this.splitMsg(configTalk.msg);
        this.nodeLeft.active = configTalk.position == 1;
        this.nodeRight.active = configTalk.position == 2;
        this.curState = TalkEnum.PLAY_TALK;
      });

      this.lblName.string = configTalk.name;
    } else if (this.curState == TalkEnum.PLAY_TALK) {
      if (this.addWords()) {
        this.curState = TalkEnum.COMPLETE;
      }
    } else if (this.curState == TalkEnum.COMPLETE) {
      this.nodeSkip.active = true;
    }
  }

  onBtnClose() {
    if (this.curState == TalkEnum.COMPLETE) {
      this.curTalkIdx++;
      this.nodeSkip.active = false;
      this.curState = TalkEnum.INIT;
    } else if (this.curState == TalkEnum.PLAY_TALK) {
      let configTalk = this.configTalkList[this.curTalkIdx];
      this.richtextMsg.string = configTalk.msg;
      this.richtextMsgShadow.string = configTalk.msg;
      this.curState = TalkEnum.COMPLETE;
    }

    if (this.curTalkIdx >= this.configTalkList.length) {
      this.closeBack();
    }
  }
}
