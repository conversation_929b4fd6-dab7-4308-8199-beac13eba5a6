export class ArrayUtils {
  public static isArraySubset(subset: any[], superset: any[]): boolean {
    return subset?.every((element) => superset?.includes(element)) ?? false;
  }
  public static removeElement<T>(array: T[], element: T): T[] {
    return array.filter((item) => item !== element);
  }

  /**
   * 交换位置
   * @param arr 原数组
   * @param index1 交换位置1
   * @param index2 交换位置2
   * @returns 改变后的数组
   */
  public static swapArrayElements<T>(arr: T[], index1: number, index2: number): T[] {
    // 检查索引是否有效
    if (index1 < 0 || index1 >= arr.length || index2 < 0 || index2 >= arr.length) {
      throw new Error("索引超出数组范围");
    }

    // 交换两个元素
    [arr[index1], arr[index2]] = [arr[index2], arr[index1]];

    return arr;
  }

  public static moveToLast<T>(arr: T[], index: number): T[] {
    // 检查索引是否有效
    if (index < 0 || index >= arr.length) {
      throw new Error("索引超出数组范围");
    }

    // 从数组中移除指定索引的元素
    const [element] = arr.splice(index, 1);

    // 将移除的元素添加到数组末尾
    arr.push(element);

    return arr;
  }
}
