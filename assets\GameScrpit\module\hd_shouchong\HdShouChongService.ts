import { FirstRechargeMessage } from "../../game/net/protocol/Activity";
import { HdShouChongModule } from "./HdShouChongModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class HdShouChongService {
  private _tickId: number;
  public async init() {}

  public canShowShouChong(callback: Function) {
    // log.log("====canShowShouChong====");
    HdShouChongModule.api.firstRecharge((data: FirstRechargeMessage) => {
      log.log("canShowShouChong", data);
      let values: any[] = Object.values(data.subRechargeMap);
      let isShow = 2; //首充档位 2档
      for (let i = 0; i < values.length; i++) {
        // let values = data.subRechargeMap[values[i]];
        if (values[i].stateList.every((v) => v == 1)) {
          isShow--;
        }
      }
      log.log("canShowShouChong", isShow);
      callback(isShow != 0);
    });
  }
}
