import { _decorator, Node } from "cc";
import { ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { PupilModule } from "../PupilModule";
import { Label } from "cc";
import { Sprite } from "cc";
import ToolExt from "../../../../game/common/ToolExt";
import { UIMgr } from "../../../../lib/ui/UIMgr";
import { PupilRouteName } from "../PupilRoute";
import { PupilMarketResponse } from "../../../../game/net/protocol/Pupil";
import FmUtils from "../../../../lib/utils/FmUtils";
import { Layout } from "cc";
import { TimeUtils } from "../../../../lib/utils/TimeUtils";
import { PupilAni } from "../prefab/ui/PupilAni";
import { AssetMgr, BundleEnum } from "../../../../../platform/src/ResHelper";
import { SpriteFrame } from "cc";
import { PupilAudioName } from "db://assets/GameScrpit/module/pupil/src/PupilConstant";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";

const { ccclass, property } = _decorator;
@ccclass("PupilWaitItemViewHolder")
export class PupilWaitItemViewHolder extends ViewHolder {
  @property(Label)
  private lblName: Label;

  @property(Node)
  private btnCancel: Node;

  @property(Node)
  private btnCd: Node;

  @property(Node)
  private btnMarry: Node;

  @property(Sprite)
  private spTalent: Sprite;

  @property(Sprite)
  private bg2: Sprite;

  @property(Label)
  private lbl2: Label;

  @property(Label)
  private lblRewardNum: Label;

  @property(Layout)
  private layoutAttr: Layout;

  @property(Node)
  private nodeJob: Node;

  private _pupilId: number;

  private _assetMgr: AssetMgr;

  onLoad() {
    this._assetMgr = AssetMgr.create();
  }

  onDestroy() {
    this._assetMgr.release();
  }

  public init() {}

  public updateData(position: number, args: any) {
    this._pupilId = args;
    this.refresh();
  }

  public refresh() {
    const pupilMsg = PupilModule.data.allPupilMap[this._pupilId];

    // 形象
    this.node
      .getChildByPath("btn_head/PupilAni")
      .getComponent(PupilAni)
      .setAniByNameId(pupilMsg.ownInfo.nameId, pupilMsg.ownInfo.adultAttrList.length);

    // 设置名称
    this.lblName.string = PupilModule.service.getPupilName(pupilMsg.ownInfo.nameId);

    // 天资背景
    let config = PupilModule.data.getConfigPupil(pupilMsg.ownInfo.talentId);

    this._assetMgr.loadSpriteFrame(BundleEnum.BUNDLE_G_PUPIL, `images/${config.talentBg}`, (spf: SpriteFrame) => {
      this.spTalent.spriteFrame = spf;
    });

    // 设置奖励图标
    FmUtils.setItemIcon(this.node.getChildByName("bg_reward_item"), config.rewardList[0]);

    // 出师属性
    let adult_attr = ToolExt.traAwardItemMapList(pupilMsg.ownInfo.adultAttrList);
    PupilModule.service.setPupilAdultAttrBgNode(this.bg2.node, config.color);
    PupilModule.service.setPupilAttrNode(this.lbl2.node, adult_attr[0]);

    // 天生属性
    PupilModule.service.setLayoutAttr(this.layoutAttr, pupilMsg.ownInfo.initAttrList);

    // 结伴到期时间，差1秒到期，直接算他到期吧，反正也来不及操作
    const timeEnd = PupilModule.service.getMarryTimeEnd(this._pupilId) - 1000;
    // 时间没到就是在市场上
    const isOnMarket = timeEnd > TimeUtils.serverTime;
    // 取消按钮显示
    this.btnCancel.active = isOnMarket;
    // 结伴按钮显示
    this.btnMarry.active = !isOnMarket;

    if (isOnMarket) {
      // 倒计时，如果时间到了，重新刷新这个组件
      FmUtils.setCd(this.btnCd, timeEnd, false, this.updateData);
    }

    // 任职显示
    this.nodeJob.getChildByName("bg_job3").active = PupilModule.data.pupilTrainMsg.workSlotList[0] == this._pupilId;
    this.nodeJob.getChildByName("bg_job2").active =
      PupilModule.data.pupilTrainMsg.workSlotList[1] == this._pupilId ||
      PupilModule.data.pupilTrainMsg.workSlotList[2] == this._pupilId;
    this.nodeJob.getChildByName("bg_job1").active =
      PupilModule.data.pupilTrainMsg.workSlotList[3] == this._pupilId ||
      PupilModule.data.pupilTrainMsg.workSlotList[4] == this._pupilId;
  }

  // 打开结伴界面
  public onMarry() {
    AudioMgr.instance.playEffect(PupilAudioName.Effect.点击结伴按钮);
    UIMgr.instance.showDialog(PupilRouteName.UIPupilMarryPage, { pupilId: this._pupilId }, this.refresh.bind(this));
  }

  // 取消结伴
  public onCancel() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    PupilModule.api.cancelMarket(
      this._pupilId,
      PupilModule.service.getMarryChannelId(this._pupilId),
      (resp: PupilMarketResponse) => {
        if (resp.channelId == 1) {
          PupilModule.data.pupilTrainMsg.clubMarryApplyMap = resp.marryApplyMap;
        } else if (resp.channelId == 2) {
          PupilModule.data.pupilTrainMsg.localMarryApplyMap = resp.marryApplyMap;
        } else if (resp.channelId == 3) {
          PupilModule.data.pupilTrainMsg.crossMarryApplyMap = resp.marryApplyMap;
        }
        this.refresh();
      }
    );
  }

  public onShowDetail() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    UIMgr.instance.showDialog(PupilRouteName.UIPupilDetailPop, {
      pupilId: this._pupilId,
    });
  }
}
