{"skeleton": {"hash": "xzlTWrDjMmSR9It1Asm7UBSH9h8=", "spine": "3.8.75", "x": -163.83, "y": 39.02, "width": 311.4, "height": 245.94, "images": "./output/1/", "audio": "D:/spine导出/灵兽动画/耳鼠"}, "bones": [{"name": "root", "scaleX": -1}, {"name": "bone", "parent": "root", "x": -26.91, "y": 51.61, "scaleX": 0.4363, "scaleY": 0.4363}, {"name": "bone2", "parent": "bone", "length": 70, "rotation": -1.71, "x": 60.84, "y": 151.84}, {"name": "xz", "parent": "bone2", "rotation": 34.04, "x": 24.35, "y": 52.5, "shearX": -24.44, "shearY": 4.38}, {"name": "xie", "parent": "xz", "rotation": 51.18}, {"name": "bone3", "parent": "bone2", "length": 48.2, "rotation": 143.16, "x": -48.26, "y": -18.67}, {"name": "bone4", "parent": "bone3", "length": 59, "rotation": -19.5, "x": 48.2}, {"name": "bone5", "parent": "bone4", "length": 43.32, "rotation": -20.18, "x": 59}, {"name": "bone6", "parent": "bone4", "x": 24.55, "y": -58.33}, {"name": "bone7", "parent": "bone4", "x": 38.21, "y": -25.46}, {"name": "bone8", "parent": "bone4", "x": 73.39, "y": 26.85}, {"name": "bone9", "parent": "bone4", "x": 31.61, "y": -39.88}, {"name": "bone10", "parent": "bone9", "length": 43.13, "rotation": 143.42, "x": -9.78, "y": 7.01}, {"name": "bone11", "parent": "bone10", "length": 45.68, "rotation": -13.65, "x": 43.13}, {"name": "bone71", "parent": "bone5", "rotation": -9.76, "x": 58.67, "y": 18.07}, {"name": "bone12", "parent": "bone71", "rotation": -1.07, "x": 17.29, "y": -4.29}, {"name": "bone13", "parent": "bone71", "rotation": -1.07, "x": 48.62, "y": -6.15}, {"name": "bone14", "parent": "bone5", "length": 57.26, "rotation": -76.08, "x": 133.62, "y": -82.75}, {"name": "bone15", "parent": "bone14", "length": 59.32, "rotation": 2.57, "x": 57.54, "y": 0.59}, {"name": "bone16", "parent": "bone15", "length": 54.22, "rotation": 25.56, "x": 59.32}, {"name": "bone17", "parent": "bone16", "length": 64.09, "rotation": 33.26, "x": 54.22}, {"name": "bone18", "parent": "bone5", "length": 55.92, "rotation": 55.56, "x": 142.81, "y": 25.42}, {"name": "bone19", "parent": "bone18", "length": 62.92, "rotation": -18.96, "x": 55.92}, {"name": "bone20", "parent": "bone19", "length": 62.55, "rotation": -19.66, "x": 62.92}, {"name": "bone21", "parent": "bone20", "length": 50.98, "rotation": -30.91, "x": 62.55}, {"name": "bone22", "parent": "bone11", "length": 57.76, "rotation": -83.39, "x": 32.52, "y": 26.01, "color": "30ff24ff"}, {"name": "bone23", "parent": "bone22", "length": 51.27, "rotation": -17.23, "x": 57.76, "color": "30ff24ff"}, {"name": "bone24", "parent": "bone23", "length": 59.49, "rotation": 5.76, "x": 51.27, "color": "30ff24ff"}, {"name": "bone25", "parent": "bone24", "length": 65.49, "rotation": 4.83, "x": 59.49, "color": "30ff24ff"}, {"name": "bone26", "parent": "bone22", "length": 39.71, "rotation": 23.91, "x": 81.59, "y": 2.54, "color": "30ff24ff"}, {"name": "bone27", "parent": "bone26", "length": 58.42, "rotation": 0.24, "x": 40.16, "y": -0.1, "color": "30ff24ff"}, {"name": "bone28", "parent": "bone22", "length": 52.08, "rotation": -53.37, "x": 63.59, "y": -7.39, "color": "30ff24ff"}, {"name": "bone29", "parent": "bone28", "length": 47.84, "rotation": -3.15, "x": 52.08, "color": "30ff24ff"}, {"name": "bone30", "parent": "bone23", "length": 42.17, "rotation": 32.72, "x": 39.44, "y": 7.89, "color": "30ff24ff"}, {"name": "bone31", "parent": "bone30", "length": 60.36, "rotation": -6.03, "x": 42.17, "color": "30ff24ff"}, {"name": "bone32", "parent": "bone23", "length": 46.44, "rotation": -28.85, "x": 35.26, "y": -7.77, "color": "30ff24ff"}, {"name": "bone33", "parent": "bone32", "length": 50.51, "rotation": -1.65, "x": 46.44, "color": "30ff24ff"}, {"name": "bone34", "parent": "bone23", "length": 27.28, "rotation": 24.47, "x": 36.59, "y": 2.8, "color": "c50000ff"}, {"name": "bone35", "parent": "bone34", "length": 23.87, "rotation": 7.98, "x": 27.28, "color": "c50000ff"}, {"name": "bone36", "parent": "bone35", "length": 30.37, "rotation": -2.35, "x": 23.87, "color": "c50000ff"}, {"name": "bone37", "parent": "bone23", "length": 24.84, "rotation": -24.03, "x": 38.57, "y": -6.46, "color": "c50000ff"}, {"name": "bone38", "parent": "bone37", "length": 21.52, "rotation": -4.46, "x": 24.84, "color": "c50000ff"}, {"name": "bone39", "parent": "bone38", "length": 21.7, "rotation": 1.71, "x": 21.52, "color": "c50000ff"}, {"name": "bone40", "parent": "bone22", "length": 40.18, "rotation": -163.85, "x": -9.76, "y": -1.97, "color": "30ff24ff"}, {"name": "bone41", "parent": "bone40", "length": 34.12, "rotation": 22.87, "x": 40.21, "y": 0.35, "color": "30ff24ff"}, {"name": "bone42", "parent": "bone41", "length": 38.1, "rotation": -22.63, "x": 34.12, "color": "30ff24ff"}, {"name": "bone43", "parent": "bone41", "length": 30.38, "rotation": 111.84, "x": 13.96, "y": 6.51, "color": "30ff24ff"}, {"name": "bone44", "parent": "bone43", "length": 27.66, "rotation": -12.89, "x": 30.38, "color": "30ff24ff"}, {"name": "bone45", "parent": "bone41", "length": 23.03, "rotation": 170.26, "x": -3.38, "y": -8.06, "color": "30ff24ff"}, {"name": "bone46", "parent": "bone45", "length": 27.24, "rotation": 3.37, "x": 22.92, "y": -0.33, "color": "30ff24ff"}, {"name": "bone47", "parent": "bone22", "length": 25.23, "rotation": 66.23, "x": 49.56, "y": 7.08, "color": "30ff24ff"}, {"name": "bone48", "parent": "bone47", "length": 31.59, "rotation": -13.13, "x": 25.23, "color": "30ff24ff"}, {"name": "bone49", "parent": "bone2", "length": 36.27, "rotation": -82.49, "x": -11.12, "y": 15.02}, {"name": "bone50", "parent": "bone49", "length": 39.74, "rotation": 52.54, "x": 36.27}, {"name": "bone51", "parent": "bone50", "length": 42.66, "rotation": -102.8, "x": 39.5, "y": -0.15}, {"name": "bone52", "parent": "bone2", "length": 49.22, "rotation": -124.43, "x": -48.55, "y": -6.4}, {"name": "bone53", "parent": "bone52", "length": 46.39, "rotation": 99.43, "x": 48.6, "y": -0.1}, {"name": "bone54", "parent": "bone53", "length": 45.68, "rotation": -110.53, "x": 46.26, "y": 0.25}, {"name": "bone55", "parent": "bone50", "length": 35.35, "rotation": 37.92, "x": 52.08, "y": 11.76}, {"name": "bone56", "parent": "bone55", "length": 51.17, "rotation": 47.25, "x": 35.35}, {"name": "bone57", "parent": "bone56", "length": 59.88, "rotation": 23.25, "x": 51.17}, {"name": "bone58", "parent": "bone57", "length": 54.35, "rotation": -28.24, "x": 59.88}, {"name": "bone59", "parent": "bone58", "length": 60.04, "rotation": -13.69, "x": 55.88, "y": 0.21}, {"name": "bone60", "parent": "bone59", "length": 59.09, "rotation": -16.65, "x": 60.04}, {"name": "bone61", "parent": "bone60", "length": 58.2, "rotation": -27.5, "x": 59.09}, {"name": "bone62", "parent": "bone61", "length": 44.92, "rotation": -38.77, "x": 58.2}, {"name": "bone63", "parent": "bone50", "length": 47, "rotation": 21.68, "x": 60.33, "y": 16.34}, {"name": "bone64", "parent": "bone63", "length": 43.17, "rotation": 34, "x": 46.92, "y": 0.42}, {"name": "bone65", "parent": "bone64", "length": 54.01, "rotation": 27.75, "x": 43.17}, {"name": "bone66", "parent": "bone65", "length": 56.39, "rotation": -27.54, "x": 54.01}, {"name": "bone67", "parent": "bone66", "length": 54.91, "rotation": -16.6, "x": 56.39}, {"name": "bone68", "parent": "bone67", "length": 53.62, "rotation": -15.67, "x": 54.98, "y": 1.03}, {"name": "bone69", "parent": "bone68", "length": 34.51, "rotation": -24.9, "x": 53.62}, {"name": "bone70", "parent": "bone69", "length": 25.8, "rotation": -36.36, "x": 34.51}, {"name": "bone72", "parent": "bone5", "length": 35.1, "rotation": -80.16, "x": 187.27, "y": -39.44}, {"name": "bone73", "parent": "bone72", "length": 40.55, "rotation": 27.6, "x": 35.1}, {"name": "bone74", "parent": "bone5", "length": 24.85, "rotation": 91, "x": 185.82, "y": -30.56}, {"name": "bone75", "parent": "bone74", "length": 29.5, "rotation": -37.37, "x": 25.17, "y": -0.07}, {"name": "bone76", "parent": "bone4", "x": 41.5, "y": 8.31}, {"name": "bone77", "parent": "bone76", "length": 39.84, "rotation": 116.09, "x": -0.9, "y": 12.35}, {"name": "bone78", "parent": "bone77", "length": 41.11, "rotation": 32.34, "x": 39.99, "y": -0.23}, {"name": "bone79", "parent": "bone78", "length": 21.64, "rotation": 71.41, "x": 40.84}, {"name": "bone80", "parent": "bone2", "length": 84.06, "rotation": 25.52, "x": -7.26, "y": 10.4}], "slots": [{"name": "xz", "bone": "xie", "attachment": "xz"}, {"name": "wb1", "bone": "bone80"}, {"name": "x2", "bone": "bone", "attachment": "x2"}, {"name": "x1", "bone": "bone", "attachment": "x1"}, {"name": "e2", "bone": "bone", "attachment": "e2"}, {"name": "e1", "bone": "bone", "attachment": "e1"}, {"name": "t2", "bone": "bone", "attachment": "t2"}, {"name": "s3", "bone": "bone", "attachment": "s3"}, {"name": "bd1", "bone": "bone", "attachment": "bd1"}, {"name": "t1", "bone": "bone", "attachment": "t1"}, {"name": "y6", "bone": "bone", "attachment": "y6"}, {"name": "s2", "bone": "bone", "attachment": "s2"}, {"name": "y5", "bone": "bone", "attachment": "y5"}, {"name": "y4", "bone": "bone", "attachment": "y4"}, {"name": "y3", "bone": "bone", "attachment": "y3"}, {"name": "s1", "bone": "bone", "attachment": "s1"}, {"name": "mm1", "bone": "bone", "attachment": "mm1"}, {"name": "tou1", "bone": "bone5", "attachment": "tou1"}, {"name": "m1", "bone": "bone13", "attachment": "m1"}, {"name": "biyan", "bone": "bone12", "attachment": "biyan"}, {"name": "eye", "bone": "bone12", "attachment": "eye"}, {"name": "y2", "bone": "bone", "attachment": "y2"}, {"name": "y1", "bone": "bone", "attachment": "y1"}], "skins": [{"name": "default", "attachments": {"xz": {"xz": {"type": "mesh", "uvs": [0.54758, 0.30035, 0.69192, 0.35985, 0.80651, 0.48876, 0.82083, 0.65073, 0.73489, 0.81049, 0.57403, 0.89313, 0.39884, 0.86889, 0.24569, 0.75099, 0.21704, 0.58131, 0.31731, 0.36536], "triangles": [6, 7, 8, 3, 4, 1, 3, 1, 2, 0, 8, 9, 1, 8, 0, 5, 6, 1, 8, 1, 6, 4, 5, 1], "vertices": [16.58, 260.38, 144.18, 207.78, 245.47, 93.82, 258.13, -49.36, 182.16, -190.59, 39.96, -263.64, -114.91, -242.21, -250.29, -137.99, -275.62, 12.01, -186.98, 202.91], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 884, "height": 884}}, "wb1": {"wb1": {"x": 196.71, "y": 24.55, "rotation": -23.81, "width": 333, "height": 265}}, "mm1": {"mm1": {"type": "mesh", "uvs": [0.47184, 0, 0.59515, 0.00907, 0.71405, 0.03197, 0.81974, 0.10067, 0.92984, 0.15792, 0.96214, 0.28005, 1, 0.53195, 0.96654, 0.69607, 0.92837, 0.80675, 0.79626, 0.9098, 0.67148, 0.96705, 0.57166, 1, 0.40138, 0.94797, 0.26192, 0.94033, 0.10192, 0.89453, 0, 0.84492, 0.01971, 0.64263, 0.08284, 0.3373, 0.16357, 0.1541, 0.26633, 0.07395, 0.38376, 0.02052, 0.1513, 0.6016, 0.29661, 0.52964, 0.44422, 0.52964, 0.60106, 0.51764, 0.80172, 0.49965, 0.91474, 0.46967], "triangles": [21, 17, 18, 21, 18, 22, 16, 17, 21, 14, 16, 21, 15, 16, 14, 13, 21, 22, 14, 21, 13, 22, 19, 20, 18, 19, 22, 23, 20, 0, 22, 20, 23, 12, 22, 23, 13, 22, 12, 12, 23, 11, 24, 1, 2, 24, 23, 0, 24, 0, 1, 25, 10, 24, 11, 23, 24, 11, 24, 10, 26, 3, 4, 26, 4, 5, 25, 2, 3, 25, 3, 26, 24, 2, 25, 26, 5, 6, 7, 26, 6, 8, 26, 7, 25, 26, 8, 9, 25, 8, 9, 10, 25], "vertices": [5, 8, 52.61, 30.59, 0.02325, 9, 38.95, -2.28, 0.30288, 6, 77.16, -27.74, 0.01423, 7, 26.61, -19.78, 0.65926, 10, 3.77, -54.59, 0.00037, 5, 8, 41.97, 14.56, 0.16471, 9, 28.31, -18.31, 0.58788, 6, 66.52, -43.78, 0.00172, 7, 22.16, -38.5, 0.24551, 10, -6.87, -70.63, 0.00017, 4, 8, 30.99, -0.46, 0.48139, 9, 17.33, -33.33, 0.45195, 7, 17.03, -56.38, 0.06658, 10, -17.85, -85.64, 7e-05, 4, 8, 18.77, -12.27, 0.84702, 9, 5.1, -45.14, 0.14149, 7, 9.63, -71.68, 0.01147, 10, -30.08, -97.45, 2e-05, 3, 8, 6.77, -25.02, 0.99729, 9, -6.9, -57.89, 0.00263, 7, 2.77, -87.79, 7e-05, 1, 8, -2.11, -25.42, 1, 1, 8, -18.06, -22.44, 1, 2, 8, -23.66, -12.8, 0.9997, 9, -37.33, -45.67, 0.0003, 2, 8, -26.14, -4.23, 0.98735, 9, -39.81, -37.1, 0.01265, 3, 8, -20.49, 16.53, 0.72516, 9, -34.15, -16.34, 0.26481, 6, 4.06, -41.8, 0.01003, 3, 8, -13.1, 34.86, 0.24966, 9, -26.77, 1.99, 0.57706, 6, 11.44, -23.47, 0.17328, 3, 8, -6.54, 49.12, 0.03575, 9, -20.21, 16.25, 0.35202, 6, 18.01, -9.21, 0.61223, 2, 6, 34.71, 11.68, 0.93628, 10, -38.68, -15.17, 0.06372, 3, 6, 46.61, 29.9, 0.47597, 7, -21.94, 23.79, 1e-05, 10, -26.78, 3.05, 0.52403, 2, 6, 62.15, 49.62, 0.04133, 10, -11.24, 22.77, 0.95867, 1, 10, -0.31, 34.69, 1, 1, 10, 8.37, 25.66, 1, 3, 9, 53.88, 59.92, 7e-05, 7, 19.18, 43.76, 0.04471, 10, 18.7, 7.61, 0.95522, 4, 8, 70.21, 76.29, 1e-05, 9, 56.54, 43.42, 0.00026, 7, 27.37, 29.19, 0.3149, 10, 21.36, -8.89, 0.68484, 4, 8, 65.81, 60.14, 1e-05, 9, 52.14, 27.27, 0.0005, 7, 28.81, 12.52, 0.76912, 10, 16.96, -25.04, 0.23038, 5, 8, 58.84, 42.9, 0.00107, 9, 45.17, 10.03, 0.03963, 6, 83.38, -15.43, 0.00093, 7, 28.21, -6.07, 0.9578, 10, 9.99, -42.28, 0.00057, 2, 6, 72.99, 33.79, 0.01351, 10, -0.41, 6.94, 0.98649, 4, 9, 26.44, 37.73, 1e-05, 6, 64.66, 12.27, 0.28683, 7, 1.08, 13.47, 0.36243, 10, -8.74, -14.58, 0.35072, 4, 9, 14.26, 18.19, 0.13228, 6, 52.47, -7.27, 0.73403, 7, -3.62, -9.08, 0.13368, 10, -20.92, -34.12, 1e-05, 4, 8, 15.59, 29.92, 0.04041, 9, 1.92, -2.95, 0.94168, 7, -7.9, -33.18, 0.0179, 10, -33.25, -55.27, 1e-05, 3, 8, -0.05, 2.78, 0.94561, 9, -13.72, -30.08, 0.05408, 7, -13.23, -64.04, 0.0003, 1, 8, -7.86, -13.13, 1], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 32, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52], "width": 156, "height": 60}}, "tou1": {"tou1": {"type": "mesh", "uvs": [0.44709, 0.07452, 0.53144, 0.07155, 0.63594, 0.09677, 0.74295, 0.14424, 0.8273, 0.24364, 0.90535, 0.37123, 0.94186, 0.49437, 0.97837, 0.62047, 1, 0.7347, 0.99474, 0.8074, 0.93809, 0.86971, 0.85122, 0.91273, 0.73917, 0.95279, 0.6095, 0.98691, 0.47857, 1, 0.40303, 1, 0.29979, 0.98839, 0.17138, 0.96169, 0.07822, 0.89938, 0.01401, 0.84745, 0, 0.76883, 0, 0.62937, 0.01904, 0.50178, 0.05555, 0.34749, 0.12354, 0.22584, 0.22174, 0.12941, 0.35645, 0.08193, 0.37055, 0.18258, 0.32617, 0.33655, 0.30398, 0.52973, 0.31138, 0.71711, 0.32494, 0.88415, 0.58872, 0.17387, 0.64665, 0.31767, 0.68856, 0.50068, 0.70212, 0.71421, 0.6824, 0.87979, 0.26331, 0.15353, 0.17826, 0.30605, 0.12649, 0.50068, 0.108, 0.68515, 0.12772, 0.83767, 0.47409, 0.17968, 0.49504, 0.33945, 0.50983, 0.537, 0.516, 0.70404, 0.51106, 0.88996, 0.69473, 0.15353, 0.76498, 0.29152, 0.84634, 0.47309, 0.86606, 0.68515, 0.8488, 0.81733], "triangles": [13, 36, 12, 11, 12, 51, 12, 36, 51, 11, 51, 10, 36, 35, 51, 10, 51, 9, 9, 51, 8, 51, 35, 50, 8, 51, 50, 20, 21, 40, 50, 7, 8, 35, 34, 50, 34, 49, 50, 50, 6, 7, 50, 49, 6, 39, 21, 22, 28, 27, 43, 27, 42, 43, 34, 48, 49, 34, 33, 48, 49, 5, 6, 49, 48, 5, 5, 48, 4, 23, 24, 38, 43, 32, 33, 43, 42, 32, 38, 37, 28, 28, 37, 27, 33, 47, 48, 33, 32, 47, 38, 25, 37, 38, 24, 25, 47, 3, 48, 48, 3, 4, 37, 26, 27, 27, 0, 42, 27, 26, 0, 42, 1, 32, 42, 0, 1, 32, 2, 47, 32, 1, 2, 47, 2, 3, 37, 25, 26, 14, 46, 13, 14, 15, 46, 16, 31, 15, 16, 17, 31, 13, 46, 36, 18, 41, 17, 18, 19, 41, 28, 43, 29, 36, 45, 35, 19, 20, 41, 35, 44, 34, 44, 33, 34, 44, 43, 33, 29, 38, 28, 22, 23, 39, 39, 23, 38, 44, 30, 43, 35, 45, 44, 43, 30, 29, 40, 39, 29, 17, 41, 31, 46, 31, 45, 41, 30, 31, 20, 40, 41, 41, 40, 30, 40, 29, 30, 45, 30, 44, 31, 30, 45, 15, 31, 46, 46, 45, 36, 40, 21, 39, 39, 38, 29], "vertices": [2, 14, 141.24, -37.18, 0.072, 7, 191.57, -42.51, 0.928, 2, 14, 141.13, -58.36, 0.072, 7, 187.87, -63.37, 0.928, 2, 14, 134.84, -84.38, 0.072, 7, 177.27, -87.95, 0.928, 2, 14, 123.79, -110.87, 0.072, 7, 161.89, -112.18, 0.928, 2, 14, 101.89, -131.29, 0.072, 7, 136.84, -128.59, 0.928, 1, 7, 106.24, -142.23, 1, 2, 14, 47.51, -158.15, 0.072, 7, 78.7, -145.85, 0.928, 2, 14, 20.34, -166.37, 0.072, 7, 50.53, -149.34, 0.928, 2, 14, -4.16, -170.94, 0.072, 7, 25.6, -149.69, 0.928, 2, 14, -19.59, -169.07, 0.072, 7, 10.72, -145.24, 0.928, 2, 14, -32.36, -154.4, 0.072, 7, 0.62, -128.62, 0.928, 2, 14, -40.75, -132.29, 0.144, 7, -3.9, -105.4, 0.856, 2, 14, -48.29, -103.88, 0.168, 7, -6.52, -76.13, 0.832, 2, 14, -54.41, -71.1, 0.176, 7, -7, -42.78, 0.824, 2, 14, -56.04, -38.15, 0.176, 7, -3.02, -10.04, 0.824, 2, 14, -55.38, -19.21, 0.176, 7, 0.84, 8.52, 0.824, 2, 14, -51.99, 6.6, 0.176, 7, 8.55, 33.38, 0.824, 2, 14, -45.18, 38.62, 0.176, 7, 20.69, 63.78, 0.824, 2, 14, -31.09, 61.52, 0.176, 7, 38.45, 83.96, 0.824, 2, 14, -19.47, 77.24, 0.176, 7, 52.57, 97.49, 0.824, 2, 14, -2.61, 80.16, 0.176, 7, 69.68, 97.51, 0.824, 1, 7, 98.76, 91.45, 1, 2, 14, 54.06, 73.39, 0.176, 7, 124.39, 81.23, 0.824, 2, 14, 86.59, 63.08, 0.176, 7, 154.7, 65.56, 0.824, 2, 14, 111.88, 45.11, 0.168, 7, 176.58, 43.57, 0.832, 2, 14, 131.54, 19.76, 0.072, 7, 191.66, 15.25, 0.928, 1, 7, 194.67, -19.91, 1, 2, 14, 118.91, -17.17, 0.168, 7, 172.96, -19.01, 0.832, 2, 14, 86.53, -4.89, 0.176, 7, 143.12, -1.41, 0.824, 1, 14, 45.73, 5.73, 1, 1, 14, 5.95, 10.24, 1, 1, 14, -29.84, 4.88, 1, 2, 14, 118.85, -71.96, 0.168, 7, 163.61, -73, 0.832, 2, 14, 87.73, -85.42, 0.176, 7, 130.66, -80.98, 0.824, 2, 14, 48.4, -94.57, 0.176, 7, 90.35, -83.33, 0.824, 2, 14, 2.83, -96.37, 0.176, 7, 45.13, -77.39, 0.824, 2, 14, -32.25, -90.18, 0.176, 7, 11.61, -65.35, 0.824, 2, 14, 126.04, 9.51, 0.168, 7, 184.51, 6.08, 0.832, 2, 14, 94.33, 31.99, 0.176, 7, 157.06, 33.61, 0.824, 1, 14, 53.35, 46.43, 1, 1, 14, 14.25, 52.45, 1, 1, 14, -18.39, 48.64, 1, 2, 14, 118.62, -43.17, 0.168, 7, 168.26, -44.58, 0.832, 2, 14, 84.42, -47.23, 0.176, 7, 133.87, -42.78, 0.824, 1, 14, 42.24, -49.46, 1, 1, 14, 6.88, -42.54, 1, 1, 14, -32.9, -47.13, 1, 2, 14, 122.24, -98.71, 0.072, 7, 162.42, -99.93, 0.928, 2, 14, 92.25, -115.3, 0.168, 7, 130.05, -111.2, 0.832, 2, 14, 52.88, -134.35, 0.168, 7, 88.02, -123.3, 0.832, 2, 14, 7.56, -137.71, 0.168, 7, 42.79, -118.93, 0.832, 2, 14, -20.42, -132.39, 0.168, 7, 16.11, -108.95, 0.832], "hull": 27, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 0, 52, 0, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 30, 2, 64, 64, 66, 66, 68, 68, 70, 70, 72, 52, 74, 74, 76, 76, 78, 78, 80, 80, 82, 84, 86, 86, 88, 88, 90, 90, 92, 94, 96, 96, 98, 98, 100, 100, 102], "width": 251, "height": 213}}, "y1": {"y1": {"type": "mesh", "uvs": [0.0022, 0.56942, 0.08913, 0.5581, 0.18499, 0.56942, 0.2835, 0.5535, 0.38748, 0.4247, 0.53059, 0.3281, 0.65693, 0.2978, 0.78327, 0.2959, 0.85735, 0.24602, 0.88658, 0.10823, 0.89801, 0, 0.96029, 0, 1, 0.06518, 1, 0.19435, 0.95901, 0.34936, 0.9031, 0.53021, 0.83066, 0.67446, 0.74679, 0.8144, 0.6591, 0.93066, 0.54727, 1, 0.41383, 1, 0.2931, 1, 0.17618, 0.91128, 0.07579, 0.78856, 0.0097, 0.67446, 0.09549, 0.66928, 0.23824, 0.73439, 0.39198, 0.7592, 0.56219, 0.70959, 0.69763, 0.58866, 0.83856, 0.44603, 0.90994, 0.2817, 0.94288, 0.12357, 0.381, 0.59796, 0.50912, 0.51115, 0.68848, 0.44603], "triangles": [32, 10, 11, 32, 11, 12, 9, 10, 32, 32, 12, 13, 31, 9, 32, 31, 32, 13, 8, 9, 31, 14, 31, 13, 35, 6, 7, 30, 7, 8, 30, 8, 31, 30, 31, 14, 35, 7, 30, 15, 30, 14, 16, 29, 30, 16, 30, 15, 34, 4, 5, 35, 34, 5, 35, 5, 6, 29, 35, 30, 34, 35, 29, 28, 34, 29, 28, 33, 34, 17, 29, 16, 28, 29, 17, 18, 28, 17, 28, 19, 27, 18, 19, 28, 33, 3, 4, 33, 4, 34, 28, 27, 33, 26, 3, 33, 27, 26, 33, 21, 26, 27, 19, 20, 27, 21, 27, 20, 25, 1, 2, 0, 1, 25, 24, 0, 25, 26, 2, 3, 25, 2, 26, 23, 24, 25, 26, 23, 25, 22, 23, 26, 22, 26, 21], "vertices": [1, 77, 42.8, -0.24, 1, 2, 77, 31.82, -6.32, 0.99993, 76, 46.61, -24.41, 7e-05, 2, 77, 18.87, -11.19, 0.8725, 76, 33.36, -20.42, 0.1275, 3, 77, 6.53, -18.33, 0.20554, 76, 19.23, -18.6, 0.74933, 74, -25.15, 21.42, 0.04513, 2, 76, 2.21, -25.97, 0.55347, 74, -7.19, 26.09, 0.44653, 3, 76, -19.71, -29.42, 0.06494, 74, 14.99, 26.14, 0.8834, 75, -5.72, 32.48, 0.05166, 3, 76, -38.02, -27.91, 6e-05, 74, 32.85, 21.83, 0.56474, 75, 8.12, 20.39, 0.4352, 2, 74, 49.83, 15.28, 0.01937, 75, 20.13, 6.72, 0.98063, 1, 75, 30.31, 1.42, 1, 1, 75, 41.92, 5.89, 1, 1, 75, 49.96, 10.65, 1, 1, 75, 55.82, 3.86, 1, 1, 75, 55.37, -4.09, 1, 1, 75, 47.05, -11.26, 1, 1, 75, 33.22, -15.4, 1, 2, 74, 58.54, -9.59, 0.00633, 75, 16.33, -19.35, 0.99367, 2, 74, 44.32, -17.15, 0.34962, 75, 0.23, -19.47, 0.65038, 3, 76, -40.93, 17.77, 0.00228, 74, 28.71, -23.76, 0.92005, 75, -16.67, -18.1, 0.07766, 2, 76, -26.43, 24.62, 0.08164, 74, 13.34, -28.3, 0.91836, 2, 76, -9.42, 26.81, 0.41167, 74, -3.81, -27.85, 0.58833, 3, 77, -26.33, 8.37, 0.00051, 76, 9.32, 22.56, 0.91094, 74, -21.67, -20.77, 0.08854, 2, 77, -10.52, 15.6, 0.25051, 76, 26.27, 18.72, 0.74949, 2, 77, 7.92, 15.75, 0.97982, 76, 41.02, 7.64, 0.02018, 1, 77, 25.41, 12.29, 1, 1, 77, 38.1, 7.43, 1, 1, 77, 27.05, 1.89, 1, 2, 77, 6.06, -1.64, 0.88912, 76, 28.98, -5.05, 0.11088, 1, 76, 7.86, 1.9, 1, 2, 76, -16.98, 3.21, 0.01775, 74, 7.28, -5.69, 0.98225, 1, 74, 29.2, -3.31, 1, 1, 75, 15.67, -7.64, 1, 1, 75, 32.96, -6.3, 1, 1, 75, 46.23, -1.1, 1, 3, 77, -7.81, -20.74, 0.00184, 76, 6.37, -11.81, 0.82781, 74, -13.48, 12.74, 0.17035, 3, 76, -13.25, -14.93, 0.11785, 74, 6.39, 12.81, 0.87844, 75, -19.52, 24.65, 0.00371, 2, 74, 32.44, 8.44, 0.5722, 75, 1.55, 8.72, 0.4278], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48, 0, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 66, 68, 68, 70], "width": 144, "height": 85}}, "y2": {"y2": {"type": "mesh", "uvs": [0.11298, 0, 0.26007, 0.0902, 0.43888, 0.18595, 0.66095, 0.30986, 0.85707, 0.46568, 0.95513, 0.65717, 0.93782, 0.83552, 0.83688, 0.96693, 0.71863, 1, 0.51675, 0.96318, 0.30333, 0.82801, 0.15913, 0.6459, 0.04953, 0.46004, 0.01492, 0.26104, 0.02646, 0.09584, 0.14181, 0.14168, 0.24757, 0.29657, 0.4062, 0.47727, 0.57804, 0.68379, 0.70363, 0.86019], "triangles": [18, 17, 4, 18, 4, 5, 11, 17, 18, 10, 11, 18, 5, 19, 18, 6, 19, 5, 10, 18, 19, 9, 10, 19, 7, 19, 6, 8, 9, 19, 7, 8, 19, 15, 14, 0, 1, 15, 0, 13, 14, 15, 2, 16, 15, 2, 15, 1, 13, 15, 16, 12, 13, 16, 3, 17, 16, 3, 16, 2, 17, 3, 4, 12, 16, 17, 11, 12, 17], "vertices": [1, 32, 55.74, -1.08, 1, 1, 32, 42.9, -6.88, 1, 2, 32, 28.9, -14.57, 0.99982, 31, 80.14, -16.13, 0.00018, 2, 32, 11.01, -23.91, 0.79771, 31, 61.76, -24.48, 0.20229, 2, 32, -9.35, -30.34, 0.2171, 31, 41.08, -29.78, 0.7829, 2, 32, -30.71, -29.09, 0.01027, 31, 19.82, -27.35, 0.98973, 1, 31, 3.18, -18.3, 1, 1, 31, -6.51, -6.1, 1, 1, 31, -6.24, 2.77, 1, 1, 31, 3.17, 13.75, 1, 1, 31, 22.38, 21.06, 1, 2, 32, -9.2, 21.46, 0.20264, 31, 44.08, 21.94, 0.79736, 2, 32, 11.9, 21.17, 0.91675, 31, 65.13, 20.48, 0.08325, 1, 32, 32.37, 15.55, 1, 1, 32, 48.34, 8.31, 1, 1, 32, 40.87, 2.72, 1, 1, 32, 22.91, 2.05, 1, 2, 32, 1.07, -1, 0.9255, 31, 53.09, -1.06, 0.0745, 1, 31, 28.24, -2.57, 1, 1, 31, 7.63, -2.54, 1], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28, 0, 30, 30, 32, 32, 34, 34, 36, 36, 38], "width": 69, "height": 106}}, "y3": {"y3": {"type": "mesh", "uvs": [1, 0.62767, 1, 0.68184, 0.96473, 0.73228, 0.91744, 0.70799, 0.86103, 0.74161, 0.81623, 0.79578, 0.80918, 0.84092, 0.80047, 0.89665, 0.76701, 0.92791, 0.72248, 0.9695, 0.68441, 0.98246, 0.63289, 1, 0.62542, 0.93588, 0.65529, 0.8649, 0.62273, 0.86313, 0.58643, 0.86116, 0.54809, 0.84321, 0.50264, 0.82194, 0.43959, 0.75095, 0.38365, 0.87669, 0.3338, 0.95478, 0.25576, 0.97674, 0.16798, 0.95234, 0.09536, 0.89622, 0.08127, 0.83033, 0.13113, 0.77908, 0.1864, 0.70832, 0.23767, 0.65024, 0.27551, 0.63254, 0.30382, 0.62786, 0.34019, 0.6302, 0.37058, 0.63839, 0.38721, 0.65009, 0.36617, 0.62201, 0.35058, 0.60622, 0.33136, 0.6185, 0.31248, 0.62385, 0.28824, 0.62504, 0.24432, 0.62751, 0.20369, 0.62232, 0.16043, 0.60101, 0.12257, 0.57209, 0.08168, 0.51959, 0.04889, 0.44957, 0.02253, 0.37728, 0.00495, 0.29585, 0, 0.23269, 0.01982, 0.22812, 0.05531, 0.23193, 0.0952, 0.22584, 0.13508, 0.22203, 0.1753, 0.21747, 0.21586, 0.21975, 0.25439, 0.2403, 0.28819, 0.28063, 0.32368, 0.3404, 0.34946, 0.40381, 0.3685, 0.48061, 0.37882, 0.5467, 0.40377, 0.59604, 0.43488, 0.64215, 0.4658, 0.67643, 0.49982, 0.71429, 0.49591, 0.64737, 0.50177, 0.56637, 0.52055, 0.48888, 0.54049, 0.53203, 0.55066, 0.62096, 0.54284, 0.67819, 0.51937, 0.73366, 0.56612, 0.75275, 0.61725, 0.76396, 0.67974, 0.76424, 0.73294, 0.75774, 0.76301, 0.74992, 0.73063, 0.7317, 0.69882, 0.66791, 0.67627, 0.58068, 0.67107, 0.48304, 0.68032, 0.37108, 0.70981, 0.41274, 0.73197, 0.43922, 0.77015, 0.45975, 0.80206, 0.5162, 0.81573, 0.57393, 0.82045, 0.6359, 0.8155, 0.68042, 0.84715, 0.62922, 0.89605, 0.59228, 0.95737, 0.59792, 0.05075, 0.30156, 0.1036, 0.3638, 0.17026, 0.40224, 0.24343, 0.45167, 0.32229, 0.50475, 0.37514, 0.59262, 0.40642, 0.64419, 0.45205, 0.70888, 0.5112, 0.78054, 0.52416, 0.56174, 0.52584, 0.63404, 0.51035, 0.72727, 0.51732, 0.68535, 0.56162, 0.80083, 0.61626, 0.81288, 0.67285, 0.81035, 0.73566, 0.80147, 0.77876, 0.77991, 0.81622, 0.74439, 0.85791, 0.68604, 0.90663, 0.65307, 0.95057, 0.65941, 0.76158, 0.83954, 0.71628, 0.88262, 0.66161, 0.92131, 0.69324, 0.45339, 0.71394, 0.52285, 0.74869, 0.58704, 0.78111, 0.67935, 0.80063, 0.73035, 0.13054, 0.85456, 0.19122, 0.84678, 0.24922, 0.82948, 0.30146, 0.79229, 0.35178, 0.74646, 0.41093, 0.69976], "triangles": [76, 116, 117, 76, 77, 116, 117, 116, 82, 82, 116, 81, 116, 78, 115, 116, 77, 78, 116, 115, 81, 81, 115, 80, 78, 79, 115, 115, 79, 80, 74, 118, 119, 74, 75, 118, 75, 117, 118, 75, 76, 117, 119, 118, 86, 86, 118, 85, 118, 84, 85, 118, 117, 84, 117, 83, 84, 117, 82, 83, 97, 61, 62, 39, 40, 92, 40, 41, 92, 42, 91, 41, 41, 91, 92, 42, 43, 91, 93, 92, 52, 92, 51, 52, 43, 90, 91, 43, 44, 90, 91, 50, 92, 92, 50, 51, 44, 45, 90, 91, 90, 49, 90, 48, 49, 91, 49, 50, 90, 46, 47, 90, 47, 48, 90, 45, 46, 37, 38, 93, 94, 36, 37, 93, 38, 39, 36, 94, 35, 94, 37, 93, 93, 39, 92, 35, 94, 34, 58, 94, 57, 94, 56, 57, 94, 55, 56, 94, 93, 55, 93, 54, 55, 93, 53, 54, 93, 52, 53, 22, 121, 21, 121, 122, 21, 21, 123, 20, 21, 122, 123, 23, 120, 22, 22, 120, 121, 23, 24, 120, 120, 25, 121, 120, 24, 25, 25, 26, 121, 121, 26, 122, 26, 27, 122, 123, 27, 28, 123, 122, 27, 123, 28, 29, 17, 98, 16, 17, 97, 98, 19, 123, 124, 19, 20, 123, 19, 125, 18, 19, 124, 125, 124, 29, 30, 124, 123, 29, 125, 124, 32, 124, 31, 32, 124, 30, 31, 18, 97, 17, 18, 125, 97, 125, 32, 96, 97, 96, 60, 97, 60, 61, 97, 125, 96, 32, 33, 95, 32, 95, 96, 95, 59, 96, 96, 59, 60, 33, 34, 95, 34, 94, 95, 95, 58, 59, 95, 94, 58, 15, 104, 14, 13, 14, 104, 16, 103, 15, 15, 103, 104, 16, 98, 103, 103, 70, 104, 104, 71, 105, 104, 70, 71, 98, 69, 103, 103, 69, 70, 98, 97, 62, 62, 101, 98, 98, 101, 69, 101, 102, 69, 69, 102, 68, 101, 62, 102, 62, 63, 102, 102, 100, 68, 102, 63, 100, 68, 100, 67, 63, 64, 100, 64, 99, 100, 100, 99, 67, 99, 66, 67, 99, 64, 65, 99, 65, 66, 11, 114, 10, 11, 12, 114, 9, 10, 113, 10, 114, 113, 12, 13, 114, 114, 13, 113, 13, 105, 113, 113, 105, 106, 105, 13, 104, 112, 106, 107, 106, 72, 73, 106, 105, 72, 105, 71, 72, 106, 74, 107, 106, 73, 74, 9, 113, 8, 113, 112, 8, 8, 112, 7, 7, 112, 6, 113, 106, 112, 112, 107, 6, 6, 107, 5, 107, 108, 5, 5, 108, 4, 107, 119, 108, 107, 74, 119, 119, 86, 108, 108, 109, 4, 108, 86, 109, 86, 87, 109, 3, 109, 110, 3, 4, 109, 3, 111, 2, 2, 111, 1, 3, 110, 111, 109, 88, 110, 109, 87, 88, 111, 0, 1, 111, 89, 0, 111, 110, 89, 110, 88, 89], "vertices": [1, 45, 50.04, 5.26, 1, 1, 45, 49.33, -3.32, 1, 2, 45, 36.09, -10.28, 0.99999, 44, 63.48, -23.37, 1e-05, 2, 45, 19.53, -5.03, 0.99992, 44, 50.21, -12.16, 8e-05, 2, 45, -1.03, -8.7, 0.25266, 44, 29.82, -7.63, 0.74734, 2, 44, 11.62, -7.92, 0.88711, 48, -14.76, -2.67, 0.11289, 3, 44, 6.08, -13.13, 0.42928, 48, -10.18, 3.4, 0.56405, 43, 50.91, -9.39, 0.00668, 2, 44, -0.76, -19.57, 0.02249, 48, -4.53, 10.9, 0.97751, 2, 48, 8.39, 12.02, 0.96242, 49, -13.78, 13.18, 0.03758, 2, 48, 25.59, 13.5, 0.27419, 49, 3.47, 13.65, 0.72581, 2, 48, 39.2, 11.34, 0.03093, 49, 16.94, 10.69, 0.96907, 1, 49, 35.16, 6.69, 1, 2, 43, -15.85, -19.31, 0.00096, 49, 34, -3.79, 0.99904, 3, 43, -4.31, -8.89, 0.34028, 49, 19.98, -10.5, 0.45684, 25, -8.09, 7.77, 0.20287, 3, 43, -15.91, -7.7, 0.01826, 49, 30.76, -14.93, 0.03533, 25, 3.38, 9.85, 0.94641, 2, 25, 16.17, 12.17, 0.99808, 50, -8.8, 32.6, 0.00192, 3, 25, 30.19, 12.16, 0.89161, 26, -29.94, 3.44, 1e-05, 50, -3.16, 19.77, 0.10838, 1, 50, 3.52, 4.55, 1, 4, 25, 71.2, 5.65, 0.01811, 26, 11.16, 9.38, 0.56862, 29, -8.23, 7.06, 0.39211, 50, 7.41, -20.39, 0.02116, 2, 29, 15.57, 22.35, 0.97181, 30, -24.49, 22.56, 0.02819, 2, 29, 35.65, 30.7, 0.62073, 30, -4.39, 30.82, 0.37927, 2, 29, 63.69, 28.2, 0.06089, 30, 23.65, 28.19, 0.93911, 1, 30, 53.49, 17.62, 1, 1, 30, 76.95, 3.28, 1, 1, 30, 79.61, -8.03, 1, 1, 30, 60.42, -12.13, 1, 1, 30, 38.67, -18.84, 1, 2, 29, 59.02, -23.91, 0.04622, 30, 18.75, -23.89, 0.95378, 3, 26, 71.68, 21.28, 0.00368, 29, 45.18, -23.79, 0.2986, 30, 4.92, -23.71, 0.69772, 3, 26, 63.17, 15.73, 0.02359, 29, 35.12, -22.37, 0.61375, 30, -5.14, -22.25, 0.36266, 3, 26, 51.59, 9.77, 0.12284, 29, 22.47, -19.25, 0.82161, 30, -17.77, -19.07, 0.05555, 3, 26, 41.44, 5.65, 0.374, 29, 12.11, -15.67, 0.62541, 30, -28.11, -15.45, 0.00059, 2, 26, 35.33, 4.4, 0.75824, 29, 6.69, -12.59, 0.24176, 3, 26, 44.08, 4.13, 0.98713, 29, 13.11, -18.55, 0.00351, 27, -6.74, 4.83, 0.00936, 2, 26, 50.18, 4.63, 0.52078, 27, -0.62, 4.72, 0.47922, 3, 26, 55.26, 9.67, 0.0986, 29, 25.17, -21.73, 0.00012, 27, 4.94, 9.22, 0.90128, 3, 26, 60.76, 13.68, 0.00504, 29, 31.95, -22.34, 9e-05, 27, 10.82, 12.65, 0.99488, 2, 29, 40.48, -23.99, 4e-05, 27, 18.73, 16.24, 0.99996, 3, 29, 55.92, -26.94, 1e-05, 27, 33.03, 22.78, 0.95056, 28, -24.44, 24.93, 0.04943, 2, 27, 46.73, 27.74, 0.72545, 28, -10.37, 28.71, 0.27455, 2, 27, 62.3, 30.71, 0.32179, 28, 5.4, 30.36, 0.67821, 2, 27, 76.57, 31.8, 0.08246, 28, 19.71, 30.25, 0.91754, 2, 27, 93.32, 29.88, 0.00143, 28, 36.23, 26.92, 0.99857, 1, 28, 50.87, 20.04, 1, 1, 28, 63.44, 12.09, 1, 1, 28, 73.48, 1.78, 1, 1, 28, 78.32, -7.2, 1, 1, 28, 71.81, -10.12, 1, 1, 28, 59.55, -13.54, 1, 1, 28, 46.3, -18.94, 1, 2, 27, 94.33, -21.14, 0.00498, 28, 32.94, -24, 0.99502, 2, 27, 81.37, -27.46, 0.10271, 28, 19.49, -29.21, 0.89729, 2, 27, 67.88, -32.84, 0.36971, 28, 5.6, -33.43, 0.63029, 2, 27, 53.91, -35.25, 0.67264, 28, -8.53, -34.66, 0.32736, 2, 27, 40.26, -34.11, 0.88874, 28, -22.03, -32.37, 0.11126, 2, 27, 24.84, -30.36, 0.98874, 28, -37.08, -27.34, 0.01126, 2, 26, 66.08, -23.35, 0.00908, 27, 12.39, -24.72, 0.99092, 2, 26, 54.21, -15.95, 0.16653, 27, 1.33, -16.17, 0.83347, 2, 26, 45.9, -8.54, 0.73128, 27, -6.2, -7.96, 0.26872, 1, 26, 34.29, -5.99, 1, 3, 25, 76.35, -10.95, 0.00276, 26, 20.99, -4.95, 0.98446, 31, 10.47, 8.12, 0.01278, 1, 31, 0.86, 0.38, 1, 2, 25, 51.26, -4.42, 0.87649, 31, -9.74, -8.12, 0.12351, 2, 25, 54.79, -14.56, 0.89427, 31, 0.5, -11.34, 0.10573, 2, 25, 55.34, -27.6, 0.88518, 31, 11.29, -18.68, 0.11482, 2, 25, 51.25, -41.02, 0.88178, 31, 19.63, -29.97, 0.11822, 2, 25, 42.87, -35.75, 0.88321, 31, 10.4, -33.55, 0.11679, 2, 25, 36.44, -22.64, 0.89275, 31, -3.96, -30.88, 0.10725, 2, 25, 37.34, -13.16, 0.90481, 31, -11.03, -24.5, 0.09519, 2, 25, 43.78, -2.82, 0.97938, 31, -15.48, -13.17, 0.02062, 1, 25, 26.78, -3.24, 1, 1, 25, 8.49, -5.19, 1, 1, 43, 5.67, 6.38, 1, 2, 43, 24.74, 5.93, 0.99631, 46, 14, 22.45, 0.00369, 3, 44, -1.96, 7.32, 0.14058, 43, 35.56, 6.33, 0.55944, 46, 6.67, 14.47, 0.29997, 4, 44, -10.92, 15.22, 0.02119, 43, 24.23, 10.12, 0.26894, 46, 17.33, 19.85, 0.65718, 47, -17.14, 16.44, 0.0527, 3, 43, 13.67, 21.12, 0.08858, 46, 32.58, 19.62, 0.46779, 47, -2.23, 19.62, 0.44363, 3, 43, 6.71, 35.58, 0.01459, 46, 47.76, 14.4, 0.04641, 47, 13.73, 17.91, 0.939, 2, 43, 6.06, 51.2, 0.00022, 47, 27.34, 10.23, 0.99978, 1, 47, 39.73, -2.98, 1, 1, 47, 28.14, -7.57, 1, 2, 46, 47.37, -15.66, 0.01801, 47, 20.05, -11.47, 0.98199, 2, 46, 34.89, -22.12, 0.29808, 47, 9.33, -20.56, 0.70192, 2, 46, 20.38, -22.79, 0.72412, 47, -4.66, -24.45, 0.27588, 2, 46, 10.67, -19.05, 0.91338, 47, -14.96, -22.96, 0.08662, 3, 44, 24.64, 13.97, 0.00677, 46, 2.95, -12.69, 0.98092, 47, -23.9, -18.49, 0.0123, 2, 44, 19.81, 8.5, 0.18816, 46, -0.33, -6.18, 0.81184, 3, 45, -4.52, 9.52, 0.33503, 44, 33.62, 10.52, 0.62018, 46, -3.58, -19.74, 0.0448, 2, 45, 13.42, 13.93, 0.99412, 44, 51.86, 7.7, 0.00588, 1, 45, 35.22, 11.23, 1, 1, 28, 57.63, -2.51, 1, 1, 28, 36.56, 0.94, 1, 1, 28, 11.98, -0.75, 1, 1, 27, 44.31, -2.81, 1, 1, 27, 15.03, -6.14, 1, 1, 26, 43.53, -1.51, 1, 2, 26, 29.76, 0.25, 0.9746, 29, -0.23, -12.05, 0.0254, 3, 26, 10.49, 1.36, 0.95881, 29, -14.01, 1.47, 0.04015, 50, -0.62, -20.63, 0.00103, 2, 25, 45.14, 5.07, 0.47758, 50, -3.62, 3.23, 0.52242, 2, 25, 47.64, -29.94, 0.88454, 31, 8.58, -26.25, 0.11546, 2, 25, 44.72, -18.8, 0.89508, 31, -2.1, -21.95, 0.10492, 2, 25, 47.15, -3.16, 0.95471, 31, -13.2, -10.67, 0.04529, 2, 25, 46.06, -10.2, 0.91205, 31, -8.21, -15.74, 0.08795, 2, 25, 26.81, 4.58, 0.9753, 50, -11.46, 19.8, 0.0247, 2, 49, 30.06, -23.22, 0.0024, 25, 7.27, 2.5, 0.9976, 2, 43, 2.64, -0.73, 0.97622, 49, 11, -16.34, 0.02378, 2, 48, 13.01, -10.54, 0.08644, 43, 25.16, -1.08, 0.91356, 3, 44, 0.86, 0.49, 0.55937, 43, 40.81, 1.13, 0.42518, 46, -0.72, 14.39, 0.01546, 2, 44, 15.37, -0.66, 0.99712, 48, -17.23, -10.46, 0.00288, 3, 45, -1.42, 0.2, 0.38007, 44, 32.89, 0.73, 0.61495, 46, -12.41, -15.42, 0.00498, 1, 45, 16.4, 3.99, 1, 1, 45, 31.99, 1.69, 1, 3, 44, -8.96, -5.11, 0.00578, 48, 5.99, -1.96, 0.87064, 43, 33.94, -7.84, 0.12359, 3, 48, 23.52, -0.34, 0.44404, 43, 17.24, -13.4, 0.00098, 49, 0.6, -0.05, 0.55498, 3, 43, -2.75, -18.01, 0.02172, 49, 21.08, -1.31, 0.97078, 25, -12.12, 16.09, 0.0075, 1, 47, 26.44, 1.04, 1, 2, 43, 20.87, 43.69, 0.00067, 47, 13.16, 1.6, 0.99933, 2, 46, 27.47, -1.78, 0.72835, 47, -2.43, -2.39, 0.27165, 3, 44, 8.95, 14.3, 0.02243, 43, 42.9, 17.01, 0.03243, 46, 9.1, 1.74, 0.94513, 3, 44, 11.44, 3.89, 0.39678, 43, 49.23, 8.38, 0.01218, 46, -1.49, 3.31, 0.59104, 1, 30, 63.22, -0.46, 1, 1, 30, 41.74, 3.03, 1, 1, 30, 20.87, 4.83, 1, 2, 29, 41.49, 3, 0.39877, 30, 1.34, 3.09, 0.60123, 2, 26, 39.03, 23.94, 0.00153, 29, 22.34, -0.3, 0.99847, 2, 26, 24.08, 7.21, 0.25663, 29, 0.07, -3.07, 0.74337], "hull": 90, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 22, 24, 24, 26, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 0, 178, 30, 32, 32, 34, 26, 28, 28, 30, 18, 20, 20, 22, 14, 16, 16, 18, 10, 12, 12, 14, 92, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 130, 198, 198, 200, 200, 204, 204, 202, 196, 206, 206, 208, 208, 210, 210, 212, 212, 214, 214, 216, 216, 218, 218, 220, 220, 222, 222, 2, 214, 224, 224, 226, 226, 228, 158, 230, 230, 232, 232, 234, 234, 236, 236, 238, 48, 240, 240, 242, 242, 244, 244, 246, 246, 248, 248, 250], "width": 358, "height": 159}}, "y4": {"y4": {"type": "mesh", "uvs": [0.44196, 0.15995, 0.52366, 0.31215, 0.6194, 0.45849, 0.72281, 0.60044, 0.82621, 0.73507, 0.92323, 0.82873, 0.80834, 0.77312, 0.65132, 0.74678, 0.45983, 0.73947, 0.2543, 0.73947, 0.10493, 0.75264, 0.00919, 0.80825, 0, 0.91068, 0.05132, 0.97507, 0.16366, 0.99849, 0.30664, 0.95312, 0.4611, 0.91946, 0.62578, 0.90483, 0.7777, 0.92532, 0.94621, 0.95166, 1, 0.92971, 1, 0.80093, 0.93344, 0.67215, 0.87472, 0.52434, 0.81344, 0.34727, 0.73685, 0.14386, 0.66281, 0, 0.53132, 0, 0.45344, 0.04873, 0.60264, 0.18654, 0.72708, 0.40375, 0.83314, 0.63069, 0.93778, 0.79441, 0.9703, 0.88033, 0.90808, 0.88681, 0.78364, 0.86087, 0.64082, 0.82683, 0.45134, 0.82683, 0.26044, 0.84629, 0.1261, 0.85925], "triangles": [38, 9, 37, 39, 10, 9, 38, 39, 9, 11, 10, 39, 12, 11, 39, 15, 38, 37, 13, 12, 39, 14, 39, 38, 14, 38, 15, 13, 39, 14, 37, 9, 8, 37, 8, 36, 16, 37, 17, 15, 37, 16, 36, 8, 7, 35, 7, 6, 34, 35, 6, 36, 7, 35, 5, 32, 33, 5, 34, 6, 34, 5, 33, 17, 37, 36, 18, 36, 35, 18, 35, 34, 17, 36, 18, 33, 21, 20, 19, 34, 33, 19, 33, 20, 18, 34, 19, 31, 3, 30, 23, 31, 30, 31, 23, 22, 4, 3, 31, 32, 4, 31, 21, 32, 22, 32, 31, 22, 5, 4, 32, 33, 32, 21, 30, 25, 24, 2, 1, 30, 30, 24, 23, 3, 2, 30, 29, 27, 26, 29, 26, 25, 28, 27, 29, 0, 28, 29, 1, 0, 29, 30, 29, 25, 1, 29, 30], "vertices": [1, 42, 18.54, 9.04, 1, 2, 42, 3.91, 9.73, 0.80707, 41, 25.14, 9.84, 0.19293, 2, 42, -11.08, 9.06, 0.00112, 41, 10.18, 8.73, 0.99888, 2, 41, -4.86, 6.81, 0.08895, 40, 20.52, 7.17, 0.91105, 1, 40, 5.85, 6.06, 1, 2, 40, -5.77, 3.42, 0.36734, 37, -3.3, -2.66, 0.63266, 1, 37, 7.82, -6.38, 1, 2, 37, 22.7, -7.39, 0.93908, 38, -5.56, -6.68, 0.06092, 2, 38, 12.37, -8.39, 0.99848, 39, -11.15, -8.86, 0.00152, 2, 38, 31.65, -9.59, 0.02175, 39, 8.17, -9.26, 0.97825, 1, 39, 22.23, -8.47, 1, 1, 39, 31.32, -4.1, 1, 1, 39, 32.36, 4.28, 1, 1, 39, 27.65, 9.66, 1, 1, 39, 17.13, 11.8, 1, 2, 38, 27.83, 8.2, 0.11622, 39, 3.62, 8.36, 0.88378, 1, 38, 13.16, 6.35, 1, 2, 37, 24.09, 5.72, 0.86504, 38, -2.36, 6.11, 0.13496, 1, 37, 9.73, 6.29, 1, 2, 40, -15.12, 7.77, 0.0649, 37, -6.23, 7.22, 0.9351, 2, 40, -16.73, 2.65, 0.13415, 37, -11.13, 5.03, 0.86585, 2, 40, -8.3, -3.72, 0.58844, 37, -10.32, -5.49, 0.41156, 1, 40, 3.89, -5.09, 1, 2, 41, -7.3, -8.58, 0.05857, 40, 16.89, -7.99, 0.94143, 3, 42, -13.83, -11.15, 0.00972, 41, 8.04, -11.55, 0.95223, 40, 31.95, -12.14, 0.03805, 2, 42, 4.01, -14.6, 0.76725, 41, 25.97, -14.48, 0.23275, 2, 42, 17.68, -15.5, 0.99915, 41, 39.65, -14.97, 0.00085, 1, 42, 24.64, -5.29, 1, 1, 42, 25.47, 3.01, 1, 2, 42, 8.23, -2.21, 0.9977, 41, 29.82, -1.96, 0.0023, 1, 41, 8.51, -2.22, 1, 1, 40, 12.29, 0.39, 1, 2, 40, -4.35, 0.63, 0.71769, 37, -4.45, -5.57, 0.28231, 2, 40, -11.82, 2.43, 0.21139, 37, -8.04, 1.21, 0.78861, 2, 40, -8.72, 7.42, 0.04232, 37, -2.25, 2.19, 0.95768, 1, 37, 9.58, 0.98, 1, 2, 37, 23.18, -0.77, 0.99851, 38, -4.17, -0.19, 0.00149, 1, 38, 13.61, -1.29, 1, 1, 39, 7.77, -0.49, 1, 1, 39, 20.42, 0.31, 1], "hull": 29, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 0, 56, 54, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78], "width": 94, "height": 82}}, "y5": {"y5": {"type": "mesh", "uvs": [0.49568, 0.0165, 0.58474, 0.07044, 0.68039, 0.14005, 0.7876, 0.21314, 0.8882, 0.37323, 0.95912, 0.55769, 1, 0.79609, 0.87336, 0.87962, 0.69194, 0.95444, 0.49898, 1, 0.31756, 0.99621, 0.16088, 0.93182, 0.07842, 0.88484, 0, 0.87091, 0.0042, 0.77521, 0.15923, 0.68124, 0.28952, 0.64295, 0.46434, 0.62381, 0.64081, 0.64469, 0.73647, 0.66906, 0.64081, 0.60293, 0.5534, 0.4846, 0.47589, 0.32799, 0.44785, 0.16093, 0.45115, 0.06174, 0.55563, 0.14931, 0.62953, 0.28644, 0.74676, 0.44507, 0.87927, 0.64404, 0.89456, 0.74353, 0.70344, 0.79461, 0.49447, 0.78924, 0.3008, 0.78117, 0.11733, 0.7973], "triangles": [32, 16, 17, 15, 16, 32, 31, 17, 18, 32, 17, 31, 33, 14, 15, 33, 15, 32, 13, 14, 33, 12, 13, 33, 11, 33, 32, 12, 33, 11, 10, 32, 31, 11, 32, 10, 9, 31, 8, 10, 31, 9, 30, 18, 19, 30, 19, 29, 31, 18, 30, 7, 30, 29, 8, 31, 30, 8, 30, 7, 27, 3, 4, 27, 4, 5, 20, 21, 27, 28, 27, 5, 19, 20, 27, 27, 28, 19, 6, 29, 28, 19, 28, 29, 6, 28, 5, 7, 29, 6, 0, 25, 24, 25, 1, 2, 1, 25, 0, 23, 24, 25, 26, 25, 2, 26, 2, 3, 22, 23, 25, 22, 25, 26, 27, 26, 3, 21, 22, 26, 21, 26, 27], "vertices": [1, 36, 60.85, 3.93, 1, 1, 36, 48.88, -2.86, 1, 1, 36, 34.74, -9.39, 1, 2, 36, 19.44, -17.03, 0.98906, 35, 65.38, -17.58, 0.01094, 2, 36, -4.92, -18.28, 0.25061, 35, 40.99, -18.13, 0.74939, 1, 35, 16.11, -13.67, 1, 2, 35, -12.42, -2.15, 0.81632, 33, -19.77, -1.01, 0.18368, 2, 35, -12.34, 17.87, 0.01749, 33, -2.14, 8.44, 0.98251, 2, 33, 22.75, 16.31, 0.98955, 34, -21.02, 14.17, 0.01045, 2, 33, 48.94, 20.36, 0.22068, 34, 4.59, 20.96, 0.77932, 1, 34, 28.9, 21.42, 1, 1, 34, 50.19, 14.06, 1, 1, 34, 61.47, 8.52, 1, 1, 34, 72.04, 7.17, 1, 1, 34, 71.94, -5, 1, 1, 34, 51.65, -17.73, 1, 2, 33, 73.92, -26.75, 0.0005, 34, 34.39, -23.27, 0.9995, 3, 35, 44.38, 46.88, 0.00015, 33, 50.39, -27.62, 0.18643, 34, 11.08, -26.6, 0.81342, 3, 35, 29.52, 28.3, 0.07894, 33, 26.97, -23.4, 0.78251, 34, -12.66, -24.87, 0.13855, 4, 36, -26.91, 18.34, 0.00443, 35, 20.07, 19.11, 0.51175, 33, 14.39, -19.46, 0.47914, 34, -25.58, -22.27, 0.00468, 3, 36, -13.15, 25.1, 0.16108, 35, 34.01, 25.47, 0.77976, 33, 26.62, -28.69, 0.05916, 3, 36, 5.74, 27.53, 0.6615, 35, 52.97, 27.36, 0.33709, 33, 37.31, -44.47, 0.00142, 2, 36, 28.15, 26.35, 0.98016, 35, 75.33, 25.53, 0.01984, 1, 36, 48.32, 18.78, 1, 1, 36, 58.94, 11.99, 1, 1, 36, 42.24, 5.6, 1, 2, 36, 22.21, 5.94, 0.99642, 35, 68.81, 5.3, 0.00358, 3, 36, -3.13, 2.68, 0.09865, 35, 43.39, 2.77, 0.90081, 33, 11.12, -47.75, 0.00054, 2, 35, 12.54, 1.23, 0.98326, 33, -4.92, -21.36, 0.01674, 2, 35, 0.76, 6.24, 0.69414, 33, -6.12, -8.62, 0.30586, 3, 35, 8.94, 31.36, 0.02765, 33, 19.86, -3.85, 0.97223, 34, -21.78, -6.17, 0.00012, 2, 33, 47.76, -6.39, 0.08655, 34, 6.23, -5.77, 0.91345, 1, 34, 32.2, -5.79, 1, 1, 34, 56.69, -2.79, 1], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48, 0, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 26], "width": 134, "height": 127}}, "y6": {"y6": {"type": "mesh", "uvs": [0, 0.94134, 0, 0.86446, 0.07785, 0.76836, 0.12449, 0.55144, 0.23985, 0.34826, 0.38712, 0.18626, 0.5933, 0.0572, 0.82648, 0, 0.89521, 0.14232, 0.89767, 0.36473, 0.8363, 0.57615, 0.72094, 0.7711, 0.51721, 0.89741, 0.30121, 0.98527, 0.09749, 1, 0.14568, 0.87589, 0.37631, 0.6997, 0.54507, 0.42911, 0.70258, 0.19629], "triangles": [18, 6, 7, 18, 7, 8, 18, 8, 9, 18, 17, 5, 18, 5, 6, 17, 18, 9, 4, 5, 17, 10, 17, 9, 11, 17, 10, 16, 4, 17, 3, 4, 16, 2, 3, 16, 16, 17, 11, 15, 2, 16, 1, 2, 15, 12, 16, 11, 0, 1, 15, 13, 15, 16, 13, 16, 12, 14, 0, 15, 14, 15, 13], "vertices": [1, 51, 44.46, -3.49, 1, 1, 51, 41.46, -6.89, 1, 1, 51, 33.86, -7.74, 1, 2, 51, 23.08, -15.3, 0.99359, 50, 44.23, -20.15, 0.00641, 2, 51, 9.44, -19.25, 0.80702, 50, 30.05, -20.9, 0.19298, 2, 51, -4.17, -19.99, 0.32876, 50, 16.63, -18.52, 0.67124, 2, 51, -19.41, -16.7, 0.01643, 50, 2.53, -11.85, 0.98357, 1, 50, -9.14, -1.27, 1, 1, 50, -4.93, 7.3, 1, 2, 51, -22.47, 10.2, 0.00164, 50, 5.66, 15.04, 0.99836, 2, 51, -11.18, 16.87, 0.15694, 50, 18.17, 18.97, 0.84306, 2, 51, 2.14, 20.46, 0.64848, 50, 31.96, 19.44, 0.35152, 2, 51, 17.15, 17.15, 0.98019, 50, 45.83, 12.81, 0.01981, 1, 51, 31.27, 11.61, 1, 1, 51, 41.93, 3.36, 1, 1, 51, 34.7, -0.02, 1, 1, 51, 16.41, 2.25, 1, 2, 51, -2.51, -2.35, 0.12935, 50, 22.25, -1.72, 0.87065, 2, 51, -19.39, -5.77, 0.00032, 50, 5.03, -1.22, 0.99968], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28, 0, 30, 30, 32, 32, 34, 34, 36], "width": 66, "height": 59}}, "s1": {"s1": {"type": "mesh", "uvs": [0.69111, 0, 0.84346, 0.01543, 0.94568, 0.08712, 0.98039, 0.21146, 0.98425, 0.37119, 0.97461, 0.53183, 0.95095, 0.69412, 0.88952, 0.84443, 0.78509, 0.9485, 0.67452, 0.98175, 0.42266, 1, 0.21381, 0.99186, 0.08174, 0.91815, 0, 0.82998, 0.02952, 0.71001, 0.16159, 0.63052, 0.25066, 0.5279, 0.34281, 0.39492, 0.38273, 0.25617, 0.39195, 0.13186, 0.52095, 0.01912, 0.70392, 0.13259, 0.67609, 0.26682, 0.6587, 0.41578, 0.63087, 0.55656, 0.56826, 0.69407, 0.4639, 0.81684, 0.3178, 0.91342], "triangles": [24, 23, 5, 16, 17, 24, 25, 16, 24, 15, 16, 25, 6, 24, 5, 25, 24, 6, 26, 15, 25, 14, 15, 26, 13, 14, 26, 7, 25, 6, 26, 25, 7, 27, 13, 26, 12, 13, 27, 8, 26, 7, 9, 27, 26, 8, 9, 26, 11, 12, 27, 10, 27, 9, 11, 27, 10, 21, 0, 1, 21, 1, 2, 20, 0, 21, 19, 20, 21, 21, 2, 3, 22, 19, 21, 22, 21, 3, 18, 19, 22, 22, 3, 4, 22, 17, 18, 23, 22, 4, 23, 17, 22, 5, 23, 4, 24, 17, 23], "vertices": [1, 12, -15.07, -2.76, 1, 1, 12, -13.93, 5.9, 1, 1, 12, -5.89, 12.29, 1, 1, 12, 8.7, 15.43, 1, 2, 12, 27.63, 17.18, 0.97764, 13, -19.11, 13.03, 0.02236, 2, 12, 46.73, 18.18, 0.34699, 13, -0.79, 18.52, 0.65301, 2, 12, 66.09, 18.42, 0.00161, 13, 17.96, 23.32, 0.99839, 1, 13, 36.02, 25.66, 1, 1, 13, 49.62, 24, 1, 1, 13, 55.31, 19.36, 1, 1, 13, 61.8, 6.65, 1, 1, 13, 64.55, -4.76, 1, 1, 13, 58.54, -14.54, 1, 1, 13, 50.02, -22.18, 1, 1, 13, 35.94, -25.08, 1, 2, 12, 62.11, -26.25, 0.00164, 13, 24.64, -21.03, 0.99836, 2, 12, 49.54, -22.26, 0.11691, 13, 11.48, -20.12, 0.88309, 2, 12, 33.35, -18.4, 0.76201, 13, -5.17, -20.19, 0.23799, 2, 12, 16.71, -17.5, 0.99789, 13, -21.55, -23.24, 0.00211, 1, 12, 1.93, -18.19, 1, 1, 12, -12.03, -12.07, 1, 1, 12, 0.6, -0.77, 1, 1, 12, 16.65, -1.03, 1, 2, 12, 34.39, -0.57, 0.99845, 13, -8.36, -2.61, 0.00155, 1, 13, 8.04, 1.16, 1, 1, 13, 24.68, 2.97, 1, 1, 13, 40.38, 2, 1, 1, 13, 53.86, -2.16, 1], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 0, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54], "width": 56, "height": 119}}, "s2": {"s2": {"type": "mesh", "uvs": [0.00509, 0.62792, 0.06632, 0.35475, 0.22142, 0.12792, 0.4153, 0.00109, 0.62958, 0.02548, 0.81122, 0.14743, 0.95815, 0.34499, 1, 0.53279, 0.95203, 0.78157, 0.78264, 0.95231, 0.52142, 1, 0.27244, 0.95231, 0.08264, 0.81328, 0.30588, 0.66459, 0.54909, 0.53044, 0.82504, 0.42982], "triangles": [15, 5, 6, 14, 3, 4, 14, 4, 5, 14, 5, 15, 15, 6, 7, 2, 14, 13, 8, 15, 7, 8, 9, 14, 8, 14, 15, 10, 13, 14, 10, 14, 9, 11, 13, 10, 2, 3, 14, 13, 1, 2, 13, 0, 1, 12, 0, 13, 11, 12, 13], "vertices": [2, 80, 46.25, -9.57, 0.92311, 81, -7.35, -8.18, 0.07689, 2, 80, 35.07, -6.49, 0.45721, 81, -8, 3.4, 0.54279, 2, 80, 25.82, 1.17, 0.98175, 81, -3.68, 14.61, 0.01825, 2, 80, 20.68, 10.7, 0.69753, 81, 3.72, 22.52, 0.30247, 2, 80, 21.75, 21.19, 0.40398, 81, 14, 24.85, 0.59602, 2, 80, 26.81, 30.06, 0.1812, 81, 24.02, 22.88, 0.8188, 2, 80, 34.95, 37.21, 0.04938, 81, 33.39, 17.43, 0.95062, 2, 80, 42.67, 39.21, 0.01201, 81, 37.74, 10.76, 0.98799, 1, 81, 38.7, 0.34, 1, 2, 80, 59.8, 28.44, 0.00158, 81, 33, -8.91, 0.99842, 2, 80, 61.67, 15.63, 0.10492, 81, 21.45, -14.76, 0.89508, 2, 80, 59.63, 3.44, 0.52925, 81, 9.25, -16.72, 0.47075, 2, 80, 53.87, -5.82, 0.97211, 81, -1.36, -14.21, 0.02789, 2, 80, 47.85, 5.16, 0.34051, 81, 7.12, -5, 0.65949, 2, 80, 42.42, 17.11, 0.02366, 81, 16.73, 3.95, 0.97634, 2, 80, 38.39, 30.66, 0.04757, 81, 28.28, 12.09, 0.95243], "hull": 13, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 0, 24, 24, 26, 26, 28, 28, 30], "width": 49, "height": 41}}, "s3": {"s3": {"type": "mesh", "uvs": [0.71039, 0.25712, 0.84902, 0.26066, 0.963, 0.2023, 1, 0.10326, 0.95068, 0.03429, 0.81205, 0, 0.67034, 0.00422, 0.4855, 0.09442, 0.25138, 0.24651, 0.10658, 0.41098, 0.01109, 0.59137, 0, 0.76822, 0.03265, 0.90086, 0.21133, 0.98398, 0.4547, 1, 0.63954, 0.95392, 0.73195, 0.83543, 0.69191, 0.69748, 0.63029, 0.56131, 0.63029, 0.43397, 0.66418, 0.31548, 0.7839, 0.12415, 0.60056, 0.1632, 0.44088, 0.28542, 0.36399, 0.43651, 0.34034, 0.60288, 0.33738, 0.77264, 0.35512, 0.89826], "triangles": [16, 26, 17, 16, 27, 26, 15, 27, 16, 14, 27, 15, 13, 27, 14, 25, 9, 24, 10, 9, 25, 11, 10, 25, 26, 11, 25, 25, 18, 17, 17, 26, 25, 12, 11, 26, 12, 26, 27, 13, 12, 27, 21, 6, 5, 21, 5, 4, 21, 4, 3, 22, 7, 6, 21, 22, 6, 2, 21, 3, 23, 8, 7, 0, 22, 21, 1, 0, 21, 2, 1, 21, 22, 23, 7, 23, 22, 0, 20, 23, 0, 19, 23, 20, 24, 8, 23, 24, 23, 19, 9, 8, 24, 24, 19, 18, 25, 24, 18], "vertices": [1, 79, 3.11, 10.39, 1, 1, 79, -1.12, 17.88, 1, 1, 79, -10.21, 20.54, 1, 1, 79, -20.49, 16.82, 1, 1, 79, -25.2, 10.29, 1, 1, 79, -23.79, 1.03, 1, 1, 79, -18.75, -6.18, 1, 1, 79, -4.42, -10.74, 1, 2, 79, 17.2, -14.36, 0.97601, 80, -26.81, 0.25, 0.02399, 2, 79, 37.02, -12.58, 0.2675, 80, -9.11, -8.85, 0.7325, 1, 80, 10.33, -14.89, 1, 1, 80, 29.43, -15.71, 1, 1, 80, 43.77, -13.78, 1, 2, 80, 52.82, -2.76, 0.7952, 81, 1.2, -12.24, 0.2048, 2, 80, 54.65, 12.32, 0.04972, 81, 16.08, -9.16, 0.95028, 3, 79, 69.28, 46.5, 0.00033, 80, 49.74, 23.81, 0.00028, 81, 25.41, -0.86, 0.99939, 3, 79, 55.39, 44.59, 0.04307, 80, 36.98, 29.63, 0.0963, 81, 26.85, 13.09, 0.86063, 3, 79, 44.06, 34.59, 0.21588, 80, 22.07, 27.24, 0.32339, 81, 19.84, 26.47, 0.46073, 3, 79, 33.61, 23.57, 0.67545, 80, 7.34, 23.52, 0.21592, 81, 11.61, 39.24, 0.10863, 3, 79, 21.94, 16.29, 0.9832, 80, -6.41, 23.61, 0.00635, 81, 7.32, 52.31, 0.01045, 2, 79, 9.97, 11.29, 0.99997, 81, 5.31, 65.12, 3e-05, 1, 79, -11.49, 6.65, 1, 1, 79, -1.89, -0.76, 1, 2, 79, 14.55, -2.17, 0.99961, 80, -22.53, 11.97, 0.00039, 2, 79, 30.91, 2.42, 0.99895, 81, -8.28, 46.89, 0.00105, 3, 79, 46.93, 10.69, 0.20829, 80, 11.71, 5.51, 0.76765, 81, -4.06, 29.36, 0.02406, 3, 79, 62.59, 20.24, 0.01232, 80, 30.04, 5.21, 0.77391, 81, 1.49, 11.89, 0.21377, 2, 80, 43.62, 6.22, 0.02117, 81, 6.78, -0.66, 0.97883], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54], "width": 62, "height": 108}}, "m1": {"m1": {"x": 2.5, "y": -1.28, "rotation": -90.95, "width": 69, "height": 20}}, "e1": {"e1": {"type": "mesh", "uvs": [0, 0.96872, 0, 0.86872, 0.00498, 0.74872, 0.02487, 0.65939, 0.09119, 0.58205, 0.17298, 0.52872, 0.27024, 0.48605, 0.37082, 0.41005, 0.44155, 0.31939, 0.49903, 0.21139, 0.52113, 0.08072, 0.57861, 0, 0.6825, 0, 0.78971, 0.03939, 0.87371, 0.15139, 0.92802, 0.31953, 0.91994, 0.41374, 0.98862, 0.4706, 1, 0.55506, 1, 0.63141, 0.89975, 0.68338, 0.82569, 0.67526, 0.78126, 0.76135, 0.6695, 0.85394, 0.53081, 0.91566, 0.39347, 0.97414, 0.23727, 0.98551, 0.09051, 0.97414, 0.66816, 0.21153, 0.56497, 0.38032, 0.41106, 0.55966, 0.23441, 0.70313, 0.10149, 0.81496, 0.14871, 0.88669, 0.31836, 0.85083, 0.5125, 0.73478, 0.66116, 0.547, 0.78709, 0.32757], "triangles": [28, 11, 12, 28, 12, 13, 28, 13, 14, 10, 11, 28, 9, 10, 28, 37, 28, 14, 37, 14, 15, 29, 9, 28, 29, 28, 37, 8, 9, 29, 16, 37, 15, 36, 29, 37, 29, 7, 8, 30, 29, 36, 18, 16, 17, 16, 36, 37, 18, 21, 16, 18, 20, 21, 21, 36, 16, 19, 20, 18, 22, 36, 21, 23, 35, 36, 22, 23, 36, 29, 30, 7, 6, 7, 30, 31, 5, 6, 31, 6, 30, 4, 5, 31, 35, 30, 36, 34, 31, 30, 34, 30, 35, 24, 35, 23, 34, 35, 24, 32, 4, 31, 3, 4, 32, 2, 3, 32, 33, 32, 31, 1, 2, 32, 34, 33, 31, 1, 27, 0, 32, 27, 1, 32, 33, 27, 25, 34, 24, 26, 33, 34, 26, 34, 25, 27, 33, 26], "vertices": [1, 17, -23.05, -0.78, 1, 2, 17, -14.85, 16.25, 0.96678, 18, -71.62, 18.9, 0.03322, 2, 17, -4, 36.2, 0.80231, 18, -59.88, 38.34, 0.19769, 2, 17, 7.41, 49.45, 0.63358, 18, -47.89, 51.06, 0.36642, 3, 17, 27.37, 56.07, 0.39459, 18, -27.66, 56.78, 0.605, 19, -53.97, 88.75, 0.00041, 4, 17, 48.54, 57.07, 0.14864, 18, -6.46, 56.83, 0.84001, 19, -34.82, 79.65, 0.01111, 20, -30.77, 115.43, 0.00024, 4, 17, 72.02, 54.72, 0.01047, 18, 16.89, 53.43, 0.90127, 19, -15.22, 66.51, 0.07722, 20, -21.59, 93.7, 0.01105, 3, 18, 43.89, 55.23, 0.60019, 19, 9.91, 56.48, 0.30206, 20, -6.07, 71.52, 0.09775, 3, 18, 66.21, 62.68, 0.2353, 19, 33.26, 53.58, 0.40827, 20, 11.86, 56.29, 0.35643, 3, 18, 87.42, 74.46, 0.0467, 19, 57.47, 55.05, 0.18059, 20, 32.91, 44.24, 0.77271, 3, 18, 103.55, 93.83, 0.00312, 19, 80.38, 65.56, 0.0259, 20, 57.83, 40.47, 0.97098, 2, 19, 100.43, 63.99, 0.00177, 20, 73.74, 28.16, 0.99823, 1, 20, 74.95, 4.5, 1, 1, 20, 68.76, -20.29, 1, 2, 19, 117.06, -7.21, 0.00823, 20, 48.59, -40.5, 0.99177, 2, 19, 98.72, -35.97, 0.20497, 20, 17.48, -54.48, 0.79503, 2, 19, 83.26, -44.99, 0.49292, 20, -0.39, -53.55, 0.50708, 2, 19, 83.83, -63.97, 0.69817, 20, -10.33, -69.73, 0.30183, 2, 19, 72.47, -75.49, 0.76717, 20, -26.14, -73.14, 0.23283, 2, 19, 60.83, -84.01, 0.80405, 20, -40.55, -73.87, 0.19595, 3, 18, 125.66, -47.37, 0.00649, 19, 39.4, -71.36, 0.85307, 20, -51.52, -51.55, 0.14044, 4, 17, 170.65, -32.4, 0.00014, 18, 111.51, -38.03, 0.04874, 19, 30.67, -56.82, 0.88383, 20, -50.85, -34.61, 0.06729, 4, 17, 154.46, -42.67, 0.00535, 18, 94.89, -47.56, 0.18388, 19, 11.56, -58.25, 0.80476, 20, -67.62, -25.32, 0.00601, 3, 17, 123.92, -47.4, 0.06177, 18, 64.16, -50.91, 0.51851, 19, -17.61, -48.01, 0.41972, 3, 17, 90.36, -44.2, 0.34433, 18, 30.78, -46.22, 0.61264, 19, -45.69, -29.38, 0.04303, 2, 17, 57.35, -40.59, 0.88785, 18, -2.03, -41.13, 0.11215, 1, 17, 24.33, -27.09, 1, 1, 17, -4.89, -10.65, 1, 3, 18, 121.37, 56.18, 0.00079, 19, 80.22, 23.91, 0.00742, 20, 34.85, 5.73, 0.99179, 3, 18, 85.54, 39.22, 0.07185, 19, 40.58, 24.06, 0.56191, 20, 1.79, 27.6, 0.36624, 3, 18, 38.59, 25.98, 0.81114, 19, -7.5, 32.38, 0.16883, 20, -33.85, 60.92, 0.02004, 3, 17, 46.88, 21.29, 0.22248, 18, -9.73, 21.16, 0.77619, 19, -53.16, 48.88, 0.00134, 2, 17, 10.4, 15.38, 0.90431, 18, -46.43, 16.89, 0.09569, 1, 17, 14.23, -1.5, 1, 1, 17, 52.02, -12.16, 1, 3, 17, 101.42, -11.58, 0.0659, 18, 43.29, -14.13, 0.91304, 19, -20.56, -5.83, 0.02106, 2, 18, 89.95, 1.09, 0.00332, 19, 28.1, -12.23, 0.99668, 2, 19, 78.52, -10.93, 0.09942, 20, 14.33, -22.47, 0.90058], "hull": 28, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 0, 54, 24, 56, 56, 58, 58, 60, 60, 62, 62, 64, 66, 68, 68, 70, 70, 72, 72, 74], "width": 228, "height": 189}}, "e2": {"e2": {"type": "mesh", "uvs": [0.23614, 0, 0.33359, 0.02592, 0.41796, 0.13673, 0.45723, 0.27201, 0.54014, 0.40441, 0.65796, 0.51667, 0.80341, 0.54113, 0.90668, 0.59582, 0.95468, 0.72822, 0.9765, 0.84335, 1, 0.90812, 0.94159, 0.96712, 0.79614, 1, 0.62014, 1, 0.50378, 0.91531, 0.33359, 0.86638, 0.18232, 0.79298, 0.0805, 0.66922, 0.01941, 0.51523, 0, 0.34541, 0.01796, 0.17991, 0.07178, 0.06621, 0.16196, 0.00145, 0.27043, 0.18458, 0.3201, 0.3601, 0.40998, 0.53796, 0.59447, 0.65498, 0.7837, 0.73221, 0.86885, 0.82816, 0.75295, 0.87964, 0.63468, 0.82582, 0.44546, 0.75795, 0.27279, 0.64562, 0.15926, 0.4584, 0.12141, 0.24543], "triangles": [27, 6, 7, 27, 7, 8, 27, 26, 6, 30, 26, 27, 28, 27, 8, 28, 8, 9, 29, 30, 27, 29, 27, 28, 11, 28, 9, 11, 9, 10, 13, 14, 30, 13, 30, 29, 12, 29, 28, 12, 28, 11, 13, 29, 12, 32, 33, 25, 5, 25, 4, 26, 25, 5, 6, 26, 5, 31, 25, 26, 32, 25, 31, 16, 17, 32, 15, 16, 32, 31, 26, 30, 31, 15, 32, 14, 31, 30, 15, 31, 14, 23, 0, 1, 23, 1, 2, 23, 34, 21, 20, 21, 34, 22, 0, 23, 23, 21, 22, 23, 2, 3, 19, 20, 34, 24, 23, 3, 34, 23, 24, 24, 3, 4, 33, 34, 24, 19, 34, 33, 18, 19, 33, 25, 24, 4, 33, 24, 25, 17, 18, 33, 17, 33, 32], "vertices": [2, 23, 100.54, -28.98, 0.00239, 24, 47.48, -5.35, 0.99761, 2, 23, 87.49, -42.61, 0.0584, 24, 43.29, -23.75, 0.9416, 3, 23, 61.55, -46.39, 0.34279, 24, 22.96, -40.31, 0.65533, 22, 105.27, -64.39, 0.00188, 3, 23, 35.59, -40.55, 0.71915, 24, -2.3, -48.64, 0.20258, 22, 82.8, -50.16, 0.07827, 4, 23, 6.2, -42.13, 0.44508, 24, -26.71, -65.09, 0.01016, 22, 54.58, -41.76, 0.53329, 21, 93.97, -57.23, 0.01147, 3, 23, -22.99, -51.26, 0.03977, 22, 24.02, -40.54, 0.69585, 21, 65.47, -46.14, 0.26438, 3, 23, -40.11, -72.9, 4e-05, 22, 0.62, -55.15, 0.27878, 21, 38.59, -52.36, 0.72118, 2, 22, -20.68, -60.26, 0.11079, 21, 16.78, -50.27, 0.88921, 2, 22, -44.01, -47.52, 0.01516, 21, -1.14, -30.63, 0.98484, 1, 21, -13.3, -12.13, 1, 1, 21, -22.07, -2.53, 1, 1, 21, -16.29, 11.97, 1, 1, 21, 6.41, 28.19, 1, 2, 22, -31.38, 32.44, 0.07968, 21, 36.78, 40.88, 0.92032, 2, 22, -4.48, 34.93, 0.59987, 21, 63.03, 34.49, 0.40013, 3, 23, -51.83, 33.69, 0.03689, 22, 25.45, 49.16, 0.95629, 21, 95.96, 38.23, 0.00681, 2, 23, -26.08, 51.84, 0.28223, 22, 55.8, 57.59, 0.71777, 2, 23, 3.59, 57.3, 0.67124, 22, 85.58, 52.75, 0.32876, 3, 23, 34.6, 53.34, 0.94041, 24, -51.38, 31.41, 0.00775, 22, 113.45, 38.59, 0.05183, 2, 23, 64.5, 41.11, 0.7617, 24, -19.45, 36.27, 0.2383, 2, 23, 90.32, 23.14, 0.14408, 24, 11.94, 34.12, 0.85592, 2, 23, 104.33, 3.99, 0.0004, 24, 33.79, 24.89, 0.9996, 2, 23, 106.97, -16.68, 1e-05, 24, 46.67, 8.5, 0.99999, 2, 23, 66.87, -17.84, 0.21437, 24, 12.87, -13.09, 0.78563, 3, 23, 33.31, -10.06, 0.95623, 24, -19.93, -23.65, 0.02954, 22, 90.9, -20.68, 0.01423, 3, 23, -4.25, -8.65, 0.14849, 22, 56.01, -6.72, 0.85148, 21, 106.7, -24.55, 3e-05, 3, 23, -40.22, -28.29, 0.00149, 22, 15.53, -13.11, 0.86528, 21, 66.35, -17.44, 0.13323, 2, 22, -20.62, -25.71, 0.05498, 21, 28.07, -17.62, 0.94502, 2, 22, -44.56, -22.73, 0.0003, 21, 6.39, -7.02, 0.9997, 1, 21, 22.63, 10.31, 1, 2, 22, -11.54, 6.03, 0.05294, 21, 46.96, 9.45, 0.94706, 3, 23, -43.9, 5.5, 0.00109, 22, 23.43, 19.95, 0.99455, 21, 84.56, 11.25, 0.00435, 2, 23, -9.77, 23.62, 0.34761, 22, 61.66, 25.53, 0.65239, 3, 23, 31.46, 25.25, 0.98243, 24, -39.65, 5.69, 0.00192, 22, 101.04, 13.19, 0.01565, 2, 23, 70.16, 12.12, 0.34349, 24, 0.3, 14.31, 0.65651], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44, 0, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 42], "width": 187, "height": 189}}, "bd1": {"bd1": {"type": "mesh", "uvs": [0.13066, 0.02915, 0.20927, 0, 0.36021, 0, 0.52845, 0.04383, 0.71398, 0.13682, 0.85234, 0.26405, 0.97341, 0.42228, 1, 0.59683, 0.99227, 0.75995, 0.90422, 0.88719, 0.78001, 0.9867, 0.60706, 1, 0.38537, 0.98017, 0.18569, 0.86598, 0.07406, 0.6996, 0.02374, 0.49243, 0.00802, 0.26242, 0.04576, 0.10256, 0.12924, 0.24465, 0.15795, 0.45022, 0.26133, 0.67664, 0.47096, 0.7988, 0.67771, 0.87328, 0.25846, 0.13739, 0.47957, 0.22975, 0.67484, 0.39361, 0.82991, 0.61408, 0.32163, 0.33998, 0.47957, 0.54556, 0.65474, 0.69452, 0.79545, 0.76602], "triangles": [16, 17, 18, 12, 21, 11, 11, 22, 10, 11, 21, 22, 10, 30, 9, 10, 22, 30, 12, 13, 21, 9, 30, 8, 21, 29, 22, 22, 29, 30, 13, 20, 21, 13, 14, 20, 21, 28, 29, 21, 20, 28, 30, 26, 8, 30, 29, 26, 8, 26, 7, 14, 19, 20, 26, 29, 25, 20, 27, 28, 29, 28, 25, 26, 6, 7, 6, 25, 5, 6, 26, 25, 28, 24, 25, 28, 27, 24, 25, 4, 5, 25, 24, 4, 27, 23, 24, 23, 2, 24, 24, 3, 4, 24, 2, 3, 23, 1, 2, 23, 0, 1, 18, 17, 0, 14, 15, 19, 20, 19, 27, 19, 16, 18, 19, 15, 16, 27, 19, 18, 27, 18, 23, 0, 23, 18], "vertices": [3, 6, 68.17, -16.34, 0.01002, 7, 14.24, -12.17, 0.81379, 11, 36.56, 23.54, 0.17619, 3, 6, 65.22, -29.88, 0.03082, 7, 16.15, -25.9, 0.52205, 11, 33.61, 9.99, 0.44713, 3, 7, 11.03, -50.43, 0.10996, 11, 20.36, -11.27, 0.88982, 5, 80.1, -65.56, 0.00022, 3, 7, -1.53, -76.34, 0.00021, 11, -0.37, -31.25, 0.89323, 5, 53.89, -77.48, 0.10657, 2, 11, -27.31, -54.29, 0.62175, 5, 20.81, -90.19, 0.37825, 2, 11, -56.7, -69.75, 0.40529, 5, -12.06, -94.96, 0.59471, 2, 11, -93.05, -71.12, 0.24435, 5, -46.78, -84.11, 0.75565, 2, 11, -119.03, -56.59, 0.1451, 5, -66.41, -61.74, 0.8549, 2, 11, -138.34, -35.96, 0.07922, 5, -77.73, -35.85, 0.92078, 2, 11, -147.88, -12.79, 0.0379, 5, -78.98, -10.82, 0.9621, 2, 11, -150.48, 13.13, 0.01052, 5, -72.78, 14.48, 0.98948, 2, 11, -137.09, 38.62, 0.00031, 5, -51.66, 34.04, 0.99969, 1, 5, -20.9, 54.49, 1, 2, 6, -50.28, 46.75, 0.03059, 5, 16.41, 60.86, 0.96941, 2, 6, -17.89, 48.39, 0.2555, 5, 47.49, 51.59, 0.7445, 3, 6, 14.66, 37.94, 0.60245, 5, 74.68, 30.87, 0.12555, 78, -26.84, 29.63, 0.272, 1, 78, 5.77, 12.38, 1, 4, 6, 65.66, 1.83, 0.01135, 7, 5.62, 4.02, 0.83656, 11, 34.05, 41.71, 8e-05, 78, 24.16, -6.47, 0.152, 2, 6, 39.03, 2.1, 0.856, 78, -2.47, -6.2, 0.144, 2, 6, 8.6, 15.46, 0.89476, 5, 61.47, 11.7, 0.10524, 2, 6, -31.22, 20.07, 0.04951, 5, 25.47, 29.34, 0.95049, 1, 5, -13.92, 22.94, 1, 2, 11, -126.09, 17.94, 0.00837, 5, -48.19, 10.87, 0.99163, 3, 6, 42.25, -25.18, 0.2951, 7, -7.04, -29.41, 0.16101, 11, 10.64, 14.7, 0.54389, 3, 6, 10.29, -48.51, 0.03438, 11, -21.32, -8.63, 0.73235, 5, 41.7, -49.16, 0.23328, 3, 6, -29.11, -62.14, 3e-05, 11, -60.72, -22.26, 0.34819, 5, 0.01, -48.85, 0.65178, 2, 11, -104.27, -25.44, 0.12995, 5, -42.1, -37.31, 0.87005, 3, 6, 9.19, -16.93, 0.56506, 11, -22.41, 22.95, 0.22585, 5, 51.21, -19.02, 0.20909, 3, 6, -32.59, -21.77, 0.00046, 11, -64.2, 18.1, 0.0447, 5, 10.21, -9.64, 0.95484, 2, 11, -99.81, 6.04, 0.04152, 5, -27.38, -9.12, 0.95848, 2, 11, -121.87, -7.73, 0.04958, 5, -52.78, -14.73, 0.95042], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 0, 36, 36, 38, 38, 40, 40, 42, 42, 44, 46, 48, 48, 50, 50, 52, 54, 56, 56, 58, 58, 60], "width": 166, "height": 160}}, "eye": {"eye": {"x": 11.78, "y": -8.01, "rotation": -90.95, "width": 144, "height": 62}}, "x1": {"x1": {"type": "mesh", "uvs": [0, 0.79733, 0.04486, 0.83689, 0.1136, 0.86161, 0.17646, 0.85996, 0.22065, 0.79898, 0.25994, 0.68527, 0.28154, 0.54024, 0.30609, 0.40015, 0.37975, 0.25842, 0.46127, 0.16942, 0.53689, 0.08867, 0.62331, 0.02439, 0.70875, 0, 0.8, 0.01576, 0.87774, 0.0946, 0.93155, 0.21071, 0.92984, 0.27378, 0.90593, 0.32969, 0.84955, 0.36266, 0.86236, 0.29242, 0.86578, 0.22074, 0.8504, 0.18204, 0.79488, 0.1333, 0.72312, 0.10893, 0.64881, 0.13044, 0.56765, 0.18777, 0.49419, 0.25801, 0.42244, 0.33829, 0.37375, 0.44293, 0.3447, 0.54471, 0.32933, 0.68662, 0.29174, 0.79557, 0.24391, 0.88731, 0.2029, 0.93461, 0.13969, 0.94465, 0.07477, 0.91598, 0.01668, 0.86581, 0, 0.83571, 0.88533, 0.30839, 0.89841, 0.22911, 0.86425, 0.13764, 0.79448, 0.08031, 0.70799, 0.05104, 0.63095, 0.073, 0.54664, 0.13276, 0.47105, 0.20838, 0.39837, 0.29375, 0.33732, 0.42547, 0.31189, 0.54378, 0.29226, 0.6877, 0.25447, 0.79869, 0.21958, 0.85845, 0.17961, 0.89992, 0.12292, 0.90236, 0.07858, 0.88284, 0.03352, 0.85479], "triangles": [14, 40, 41, 21, 22, 40, 40, 14, 15, 20, 21, 40, 40, 39, 20, 15, 39, 40, 16, 39, 15, 19, 20, 39, 38, 19, 39, 38, 39, 16, 17, 38, 16, 18, 19, 38, 18, 38, 17, 42, 11, 12, 42, 12, 13, 41, 42, 13, 41, 13, 14, 23, 42, 41, 24, 42, 23, 40, 22, 41, 23, 41, 22, 43, 11, 42, 10, 11, 43, 24, 43, 42, 44, 10, 43, 9, 10, 44, 25, 44, 43, 25, 43, 24, 26, 44, 25, 45, 9, 44, 26, 45, 44, 8, 9, 45, 46, 8, 45, 27, 45, 26, 27, 46, 45, 47, 7, 8, 46, 47, 8, 28, 47, 46, 27, 28, 46, 47, 48, 7, 29, 47, 28, 48, 6, 7, 48, 47, 29, 49, 5, 6, 30, 48, 29, 48, 49, 6, 30, 49, 48, 49, 50, 5, 31, 49, 30, 31, 50, 49, 50, 4, 5, 51, 4, 50, 51, 3, 4, 32, 51, 50, 32, 50, 31, 33, 51, 32, 0, 55, 37, 1, 55, 0, 36, 37, 55, 54, 1, 2, 55, 1, 54, 52, 3, 51, 53, 2, 3, 53, 3, 52, 54, 2, 53, 35, 55, 54, 35, 54, 53, 36, 55, 35, 33, 52, 51, 34, 53, 52, 34, 52, 33, 35, 53, 34], "vertices": [1, 58, -40.76, 30.89, 1, 1, 58, -24.33, 19.82, 1, 1, 58, 1.76, 11.16, 1, 2, 58, 26.23, 8.86, 0.87748, 59, 0.32, 12.71, 0.12252, 1, 59, 22.01, 7.26, 1, 2, 59, 52.45, 10.67, 0.24302, 60, 5.39, 9.3, 0.75698, 1, 60, 40.21, 8.82, 1, 1, 61, 9.32, 12.89, 1, 2, 61, 53.14, 13.19, 0.45897, 62, -5.74, 11.96, 0.54103, 1, 62, 32.27, 10.78, 1, 2, 62, 67.29, 9.35, 0.22403, 63, 4.27, 11.03, 0.77597, 1, 63, 41.05, 14.72, 1, 2, 63, 74.56, 9.7, 0.0229, 64, 9.24, 15.75, 0.9771, 1, 64, 45.05, 17.91, 1, 1, 65, 12.51, 16.08, 1, 1, 65, 46.7, 13.67, 1, 1, 65, 57.19, 3.36, 1, 1, 65, 60.63, -12.3, 1, 1, 65, 51.63, -33.84, 1, 1, 65, 42.79, -19.18, 1, 1, 65, 31.26, -7.03, 1, 1, 65, 20.53, -5.48, 1, 2, 64, 47.51, -9.44, 0.90566, 65, -2.42, -14.06, 0.09434, 2, 63, 71.98, -16.17, 0.00953, 64, 18.9, -8.39, 0.99047, 2, 63, 42.81, -11.86, 0.97933, 64, -8.96, -18.04, 0.02067, 2, 62, 63.98, -16.48, 0.13316, 63, 8.5, -14.66, 0.86684, 2, 61, 82.85, -20.27, 0.00681, 62, 31.05, -13.51, 0.99319, 1, 61, 50.25, -11.64, 1, 2, 60, 70.54, -21.09, 0.01891, 61, 19.37, -13.53, 0.98109, 2, 60, 44.86, -15.46, 0.87016, 61, -5.91, -20.73, 0.12984, 2, 59, 68.33, -11.33, 0.01237, 60, 11.29, -17.18, 0.98763, 2, 59, 39.18, -14.61, 0.98199, 60, -16.78, -8.69, 0.01801, 1, 59, 10.87, -12.28, 1, 2, 58, 34.62, -9.56, 0.82846, 59, -7.52, -5.95, 0.17154, 1, 58, 9.79, -9.19, 1, 1, 58, -14.71, 0.22, 1, 1, 58, -36.01, 14.32, 1, 1, 58, -41.73, 22, 1, 1, 65, 51.56, -14.98, 1, 1, 65, 41.23, 1.16, 1, 1, 65, 16.45, 5.46, 1, 1, 64, 45.35, 2.71, 1, 1, 64, 10.88, 3.97, 1, 1, 63, 40.35, 3.03, 1, 1, 63, 4.69, 0.08, 1, 1, 62, 30.23, 1.15, 1, 1, 61, 51.79, 2.28, 1, 1, 61, 12.99, -0.17, 1, 2, 60, 42.13, -2.92, 0.99014, 61, -14.25, -10.97, 0.00986, 1, 60, 7.73, -3.13, 1, 1, 59, 29.93, -3.33, 1, 1, 59, 10.62, -0.64, 1, 1, 58, 26.44, -0.53, 1, 1, 58, 4.35, 1.32, 1, 1, 58, -12.39, 7.73, 1, 1, 58, -29.19, 16.15, 1], "hull": 38, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 0, 74, 36, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 0], "width": 391, "height": 233}}, "x2": {"x2": {"type": "mesh", "uvs": [0, 0.69814, 0, 0.65575, 0.04798, 0.69449, 0.10873, 0.73323, 0.1818, 0.77198, 0.24419, 0.79135, 0.30577, 0.74154, 0.3501, 0.61148, 0.39525, 0.45928, 0.4363, 0.29324, 0.49705, 0.16872, 0.57258, 0.07187, 0.64565, 0.00822, 0.72403, 0, 0.80949, 0.03065, 0.8707, 0.09055, 0.92993, 0.21034, 0.9635, 0.40334, 0.95955, 0.5431, 0.93684, 0.59634, 0.91058, 0.59103, 0.88748, 0.58636, 0.90821, 0.46989, 0.90821, 0.37339, 0.8549, 0.28687, 0.78777, 0.22698, 0.71077, 0.22365, 0.62785, 0.28022, 0.55677, 0.33679, 0.48569, 0.41665, 0.4383, 0.5198, 0.41658, 0.56972, 0.39585, 0.72279, 0.38006, 0.85256, 0.34846, 0.93908, 0.29614, 0.9657, 0.23987, 0.94241, 0.1836, 0.91246, 0.12536, 0.8692, 0.05921, 0.80598, 0, 0.73277, 0.04904, 0.74469, 0.11427, 0.79491, 0.18182, 0.83648, 0.24705, 0.86552, 0.29834, 0.86414, 0.32706, 0.84754, 0.35127, 0.77978, 0.37712, 0.66361, 0.40461, 0.52255, 0.45917, 0.36074, 0.51244, 0.27588, 0.58576, 0.19231, 0.65698, 0.13363, 0.73083, 0.09273, 0.81259, 0.12474, 0.87489, 0.20653, 0.9192, 0.29366, 0.93714, 0.38612, 0.93028, 0.49103], "triangles": [17, 58, 16, 23, 57, 58, 22, 23, 58, 59, 22, 58, 17, 59, 58, 18, 59, 17, 20, 22, 59, 21, 22, 20, 19, 59, 18, 20, 59, 19, 16, 56, 15, 55, 15, 56, 24, 55, 56, 57, 56, 16, 23, 56, 57, 24, 56, 23, 57, 16, 58, 54, 13, 14, 55, 14, 15, 54, 14, 55, 53, 13, 54, 25, 54, 55, 26, 54, 25, 25, 55, 24, 53, 12, 13, 52, 11, 12, 52, 12, 53, 26, 53, 54, 52, 51, 11, 27, 52, 53, 27, 53, 26, 28, 52, 27, 51, 10, 11, 50, 9, 10, 28, 51, 52, 51, 50, 10, 29, 50, 51, 29, 51, 28, 8, 9, 50, 30, 50, 29, 50, 49, 8, 30, 49, 50, 31, 49, 30, 7, 8, 49, 48, 7, 49, 48, 49, 31, 32, 48, 31, 47, 7, 48, 47, 48, 32, 6, 7, 47, 46, 6, 47, 33, 47, 32, 45, 5, 6, 45, 6, 46, 34, 46, 47, 34, 47, 33, 35, 44, 45, 35, 45, 46, 35, 46, 34, 0, 1, 2, 41, 40, 0, 41, 2, 3, 41, 0, 2, 42, 3, 4, 41, 3, 42, 39, 41, 42, 40, 41, 39, 43, 4, 5, 42, 4, 43, 44, 5, 45, 43, 5, 44, 38, 42, 43, 39, 42, 38, 37, 43, 44, 38, 43, 37, 36, 37, 44, 36, 44, 35], "vertices": [1, 66, -55.01, 5.03, 1, 1, 66, -55.87, 9.87, 1, 1, 66, -36.61, 8.69, 1, 1, 66, -12.44, 8.38, 1, 1, 66, 16.48, 8.91, 1, 2, 66, 40.89, 10.92, 0.67729, 67, 0.87, 12.07, 0.32271, 2, 67, 25.22, 7.55, 0.99684, 68, -12.37, 15.04, 0.00316, 2, 67, 47.19, 14.28, 0.09248, 68, 10.21, 10.76, 0.90752, 1, 68, 35, 7.82, 1, 2, 68, 60.06, 7.13, 0.13529, 69, 2.07, 9.12, 0.86471, 1, 69, 29.66, 12.55, 1, 2, 69, 61.2, 10.67, 0.24985, 70, 1.55, 11.6, 0.75015, 2, 70, 30.85, 15.13, 0.92621, 71, -27.05, 7.06, 0.07379, 1, 71, 3.17, 12.29, 1, 1, 71, 36.75, 13.45, 1, 2, 71, 61.42, 9.92, 0.20115, 72, 2.9, 12.28, 0.79885, 2, 72, 29.89, 13.22, 0.95089, 73, -11.55, 7.91, 0.04911, 1, 73, 14.03, 12.27, 1, 1, 73, 28.65, 5.1, 1, 1, 73, 31.29, -5.39, 1, 1, 73, 27.08, -14.78, 1, 1, 73, 23.38, -23.03, 1, 2, 72, 39.14, -16.67, 0.00195, 73, 13.61, -10.68, 0.99805, 2, 72, 33.05, -7.27, 0.4244, 73, 3.14, -6.72, 0.5756, 2, 71, 58.49, -13.49, 0.11046, 72, 10.1, -10.19, 0.88954, 2, 70, 82.56, -17.4, 0.00401, 71, 31.53, -10.29, 0.99599, 1, 70, 52.77, -13.02, 1, 2, 69, 70.99, -20.23, 0.00665, 70, 19.76, -15.22, 0.99335, 2, 69, 42.95, -14.81, 0.89283, 70, -8.65, -18.04, 0.10717, 2, 68, 60.77, -16.9, 0.05111, 69, 13.8, -11.86, 0.94889, 2, 68, 39.9, -9.75, 0.96585, 69, -8, -15.17, 0.03415, 1, 68, 30.1, -6.66, 1, 2, 67, 58.27, -4.8, 0.02405, 68, 11.14, -11.28, 0.97595, 2, 67, 46.51, -16.04, 0.66376, 68, -4.51, -15.75, 0.33624, 2, 67, 31.14, -20.17, 0.99258, 68, -20.04, -12.26, 0.00742, 1, 67, 11.19, -14.67, 1, 2, 66, 42.27, -6.63, 0.98536, 67, -7.8, -3.24, 0.01464, 1, 66, 19.99, -7.02, 1, 1, 66, -3.3, -6.02, 1, 1, 66, -30.05, -3.28, 1, 1, 66, -54.32, 1.07, 1, 1, 66, -35.19, 3.03, 1, 1, 66, -9.06, 1.71, 1, 1, 66, 17.78, 1.54, 1, 2, 66, 43.49, 2.64, 0.85846, 67, -1.61, 3.76, 0.14154, 1, 67, 16.78, -4.26, 1, 1, 67, 27.82, -7.07, 1, 2, 67, 39.66, -3.74, 0.91855, 68, -4.84, -1.68, 0.08145, 1, 68, 12, -1.28, 1, 1, 68, 31.5, 0.4, 1, 2, 68, 59.45, -4.74, 0.01917, 69, 7.01, -1.69, 0.98083, 1, 69, 30.04, -1.26, 1, 2, 69, 60.16, -4.18, 0.06748, 70, 4.81, -2.93, 0.93252, 2, 70, 33.31, 0.12, 0.99908, 71, -20.62, -6.73, 0.00092, 1, 71, 7.3, 2.01, 1, 1, 71, 39.48, 2.81, 1, 1, 72, 11.59, 1.88, 1, 2, 72, 31.63, 2.83, 0.9074, 73, -4, 0.57, 0.0926, 1, 73, 8.52, 3.34, 1, 1, 73, 18.95, -3.47, 1], "hull": 41, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 2, 0, 0, 80, 0, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 38, 40, 40, 42, 118, 40], "width": 391, "height": 116}}, "t1": {"t1": {"type": "mesh", "uvs": [0.33953, 0, 0.55358, 0.04433, 0.74745, 0.10575, 0.89083, 0.21045, 0.95141, 0.35982, 0.95545, 0.51616, 0.9817, 0.5399, 0.95545, 0.59434, 0.92112, 0.64459, 0.97564, 0.66693, 1, 0.71439, 0.98978, 0.75627, 0.87265, 0.83445, 0.71716, 0.90983, 0.54349, 0.97544, 0.44655, 1, 0.36376, 0.97265, 0.36376, 0.8833, 0.41626, 0.78698, 0.34558, 0.75627, 0.17797, 0.69345, 0.03056, 0.57061, 0.00632, 0.41147, 0.02652, 0.23976, 0.0871, 0.09179, 0.19817, 0.02757, 0.41501, 0.14326, 0.61547, 0.26743, 0.70398, 0.45099, 0.72741, 0.64534, 0.20155, 0.20265, 0.21977, 0.379, 0.34212, 0.55356, 0.54778, 0.70653, 0.57121, 0.76591, 0.69356, 0.74792, 0.81331, 0.69213, 0.4801, 0.89368, 0.61026, 0.86309, 0.77166, 0.7947, 0.90963, 0.72272], "triangles": [14, 15, 37, 15, 16, 37, 14, 38, 13, 14, 37, 38, 16, 17, 37, 13, 39, 12, 13, 38, 39, 17, 18, 37, 37, 34, 38, 37, 18, 34, 38, 35, 39, 38, 34, 35, 39, 35, 36, 34, 18, 33, 18, 19, 33, 34, 33, 35, 12, 40, 11, 12, 39, 40, 39, 36, 40, 11, 40, 10, 33, 19, 32, 35, 29, 36, 35, 33, 29, 40, 9, 10, 40, 8, 9, 40, 36, 8, 19, 20, 32, 33, 32, 29, 36, 29, 8, 32, 28, 29, 8, 29, 7, 5, 7, 29, 29, 28, 5, 7, 5, 6, 28, 4, 5, 20, 21, 32, 21, 31, 32, 21, 22, 31, 32, 31, 28, 31, 27, 28, 28, 27, 4, 22, 23, 31, 23, 30, 31, 31, 26, 27, 31, 30, 26, 27, 3, 4, 26, 1, 27, 27, 2, 3, 27, 1, 2, 23, 24, 30, 24, 25, 30, 30, 25, 26, 25, 0, 26, 26, 0, 1], "vertices": [1, 52, -46.28, -4.09, 1, 2, 52, -37.48, 17.18, 0.99098, 53, -31.22, 68.99, 0.00902, 2, 52, -26.36, 36.12, 0.92981, 53, -9.42, 71.68, 0.07019, 2, 52, -9.34, 49.23, 0.80093, 53, 11.34, 66.15, 0.19907, 2, 52, 13.43, 53.19, 0.55375, 53, 28.33, 50.48, 0.44625, 2, 52, 36.65, 51.25, 0.19728, 53, 40.92, 30.87, 0.80272, 2, 52, 40.44, 53.59, 0.15421, 53, 45.07, 29.28, 0.84579, 2, 52, 48.24, 50.08, 0.09327, 53, 47.03, 20.96, 0.90673, 2, 52, 55.33, 45.8, 0.02612, 53, 47.95, 12.73, 0.97388, 2, 52, 59.21, 51.05, 0.00488, 53, 54.48, 12.84, 0.99512, 3, 52, 66.5, 52.83, 0.00032, 53, 60.33, 8.14, 0.98487, 54, -12.7, 18.47, 0.0148, 2, 53, 62.7, 2.28, 0.93267, 54, -7.5, 22.09, 0.06733, 2, 53, 58.55, -13.97, 0.41588, 54, 9.26, 21.64, 0.58412, 2, 53, 50.81, -31.94, 0.01522, 54, 28.49, 18.08, 0.98478, 1, 54, 48, 12.16, 1, 1, 54, 57.61, 7.59, 1, 1, 54, 60.67, -1.35, 1, 2, 53, 17.76, -47.68, 0.01498, 54, 51.17, -10.67, 0.98502, 3, 52, 71.18, -8.08, 0.01378, 53, 14.83, -32.63, 0.30644, 54, 37.14, -16.86, 0.67978, 3, 52, 65.89, -14.86, 0.09094, 53, 6.23, -32.55, 0.55923, 54, 38.97, -25.27, 0.34983, 3, 52, 54.84, -31.09, 0.43594, 53, -13.38, -33.65, 0.4824, 54, 44.39, -44.14, 0.08167, 3, 52, 35.09, -44.34, 0.81024, 53, -35.91, -26.04, 0.18404, 54, 41.96, -67.8, 0.00572, 2, 52, 11.25, -44.43, 0.97283, 53, -50.48, -7.16, 0.02717, 1, 52, -13.99, -39.77, 1, 1, 52, -35.3, -31.34, 1, 1, 52, -43.66, -18.99, 1, 2, 52, -24.25, 1.49, 0.99887, 53, -35.63, 48.95, 0.00113, 2, 52, -3.76, 20.16, 0.91285, 53, -8.34, 44.04, 0.08715, 2, 52, 24.37, 26.47, 0.47956, 53, 13.77, 25.54, 0.52044, 2, 52, 53.42, 25.94, 0.00454, 53, 31.03, 2.16, 0.99546, 1, 52, -17.67, -21.28, 1, 2, 52, 8.66, -22.07, 0.99549, 53, -34.31, 8.49, 0.00451, 3, 52, 35.81, -12.16, 0.70179, 53, -9.93, -7.03, 0.28161, 54, 17.67, -46.68, 0.0166, 2, 53, 20.06, -15.31, 0.54901, 54, 19.09, -15.59, 0.45099, 2, 53, 26.76, -21.58, 0.17731, 54, 23.72, -7.67, 0.82269, 2, 53, 36.08, -12.68, 0.01186, 54, 12.98, -0.56, 0.98814, 2, 52, 61.25, 34.04, 0.00075, 53, 42.22, 0.87, 0.99925, 2, 53, 28.77, -42.71, 0.00201, 54, 43.88, -1.04, 0.99799, 1, 54, 31.24, 5.34, 1, 2, 53, 46.59, -14.39, 0.15551, 54, 12.32, 10.07, 0.84449, 3, 52, 66.79, 43.45, 0.0004, 53, 53.05, 2.2, 0.93721, 54, -5.29, 12.7, 0.06239], "hull": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 0, 50, 50, 52, 52, 54, 54, 56, 56, 58, 60, 62, 62, 64, 64, 66, 68, 70, 70, 72, 74, 76, 76, 78, 78, 80], "width": 103, "height": 149}}, "t2": {"t2": {"type": "mesh", "uvs": [0.39459, 0.04442, 0.53606, 0.00442, 0.68595, 0, 0.85943, 0.03728, 0.97395, 0.14728, 1, 0.26871, 0.96048, 0.37585, 0.87122, 0.463, 0.94532, 0.49157, 1, 0.55442, 0.97058, 0.64157, 0.8308, 0.75014, 0.67416, 0.85871, 0.49901, 0.95728, 0.37606, 1, 0.28343, 0.99014, 0.23964, 0.92014, 0.26153, 0.77157, 0.34911, 0.67157, 0.1908, 0.61585, 0.04932, 0.52014, 0, 0.41871, 0.03585, 0.27442, 0.15543, 0.15585, 0.28174, 0.09014, 0.50418, 0.10808, 0.695, 0.1497, 0.79586, 0.26069, 0.73316, 0.41098, 0.30246, 0.20519, 0.24249, 0.3578, 0.31336, 0.4711, 0.55597, 0.53353, 0.4851, 0.263, 0.56415, 0.39711, 0.65956, 0.52428, 0.36515, 0.84799, 0.51236, 0.76938, 0.6841, 0.67226, 0.86946, 0.57284], "triangles": [13, 14, 36, 14, 15, 36, 15, 16, 36, 13, 37, 12, 13, 36, 37, 16, 17, 36, 12, 38, 11, 12, 37, 38, 17, 18, 36, 36, 18, 37, 38, 37, 32, 37, 18, 32, 11, 38, 39, 39, 7, 8, 7, 28, 6, 28, 27, 6, 6, 27, 5, 38, 32, 35, 11, 39, 10, 38, 35, 39, 18, 31, 32, 10, 39, 9, 39, 35, 7, 39, 8, 9, 7, 35, 28, 32, 34, 35, 32, 31, 34, 35, 34, 28, 28, 34, 27, 22, 23, 30, 19, 31, 18, 19, 20, 31, 20, 30, 31, 20, 21, 30, 31, 33, 34, 31, 30, 33, 21, 22, 30, 27, 33, 26, 27, 34, 33, 30, 29, 33, 30, 23, 29, 27, 4, 5, 33, 25, 26, 33, 29, 25, 27, 3, 4, 27, 26, 3, 23, 24, 29, 29, 0, 25, 29, 24, 0, 26, 25, 2, 25, 1, 2, 26, 2, 3, 25, 0, 1], "vertices": [2, 55, 15.11, -18.9, 0.90903, 56, -13.06, 36.12, 0.09097, 2, 55, 3.57, -10.69, 0.99642, 56, -3.07, 46.16, 0.00358, 2, 55, -5.23, 0.52, 0.98267, 57, -36.49, -53, 0.01733, 2, 55, -11.58, 16.29, 0.77867, 57, -45.75, -38.74, 0.22133, 2, 55, -8.05, 32.34, 0.58047, 57, -45.38, -22.31, 0.41953, 2, 55, 1.47, 42.36, 0.43734, 57, -37.97, -10.64, 0.56266, 2, 55, 13.37, 46.41, 0.31273, 57, -27.06, -4.38, 0.68727, 3, 55, 26.26, 45.32, 0.11504, 56, 48.47, 14.6, 0.02413, 57, -14.21, -2.97, 0.86082, 3, 55, 24.69, 52.89, 0.02324, 56, 56.19, 14.91, 0.32143, 57, -17.21, 4.16, 0.65533, 3, 55, 27.31, 61.24, 0.00263, 56, 64, 10.96, 0.65931, 57, -16.24, 12.85, 0.33806, 2, 56, 65.89, 0.98, 0.94936, 57, -7.57, 18.12, 0.05064, 2, 56, 59.5, -15.85, 0.54612, 57, 10.44, 18.04, 0.45388, 2, 56, 51.67, -33.4, 0.01697, 57, 29.62, 16.87, 0.98303, 1, 57, 49.33, 13.68, 1, 1, 57, 61.16, 9.27, 1, 1, 57, 66.87, 2.48, 1, 2, 55, 102.99, 27.07, 0.00031, 57, 64.6, -6.1, 0.99969, 3, 55, 88.33, 18.93, 0.03065, 56, 12.27, -42.31, 0.01755, 57, 51.78, -16.91, 0.9518, 3, 55, 74.38, 19.04, 0.24995, 56, 14.67, -28.57, 0.16082, 57, 38.07, -19.48, 0.58923, 3, 55, 78.21, 3.22, 0.85003, 56, -1.57, -29.76, 0.02631, 57, 44.88, -34.28, 0.12366, 3, 55, 77.48, -13.96, 0.9354, 56, -18.4, -26.22, 0.04528, 57, 47.47, -51.27, 0.01932, 3, 55, 71.07, -24.44, 0.79062, 56, -27.69, -18.18, 0.20733, 57, 43.2, -62.79, 0.00205, 2, 55, 56.02, -31.23, 0.48642, 56, -31.91, -2.22, 0.51358, 2, 55, 38.59, -29.89, 0.41419, 56, -27.74, 14.75, 0.58581, 2, 55, 25.57, -24.54, 0.686, 56, -20.33, 26.72, 0.314, 2, 55, 14.73, -6.29, 0.98968, 56, -0.55, 34.43, 0.01032, 3, 55, 7.8, 11.1, 0.85688, 56, 17.74, 38.42, 0.01517, 57, -25.74, -40.1, 0.12795, 3, 55, 12.18, 26.17, 0.52185, 56, 31.88, 31.62, 0.04813, 57, -24.33, -24.47, 0.43002, 3, 55, 29.29, 31.29, 0.19603, 56, 34.13, 13.91, 0.33115, 57, -8.53, -16.15, 0.47282, 2, 55, 34.81, -15.35, 0.60844, 56, -12.78, 16.1, 0.39156, 3, 55, 51.98, -9.87, 0.55882, 56, -10.18, -1.73, 0.4405, 57, 21.65, -52.17, 0.00068, 3, 55, 58.25, 3.06, 0.81294, 56, 1.54, -10.04, 0.14034, 57, 25.32, -38.28, 0.04672, 3, 55, 50.3, 25.79, 0.00652, 56, 25.27, -5.92, 0.85865, 57, 13.14, -17.5, 0.13483, 3, 55, 29.81, 2.48, 0.96833, 56, 5.63, 18.12, 0.0275, 57, -2.48, -44.32, 0.00417, 3, 55, 37.51, 17.41, 0.21899, 56, 19.09, 8.08, 0.74731, 57, 2.2, -28.19, 0.0337, 2, 56, 33.59, -0.57, 0.99196, 57, 5.21, -11.58, 0.00804, 3, 55, 89.43, 31.93, 0.00447, 56, 24.91, -45.53, 0.00172, 57, 50.36, -3.94, 0.99381, 3, 55, 74.08, 38.03, 0.00175, 56, 33.45, -31.38, 0.00298, 57, 34.12, -0.91, 0.99527, 1, 57, 14.75, 2.17, 1, 3, 55, 36.29, 52.44, 0.00481, 56, 53.85, 3.54, 0.69923, 57, -5.74, 5.95, 0.29596], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48, 50, 52, 52, 54, 54, 56, 58, 60, 60, 62, 62, 64, 66, 68, 72, 74, 74, 76, 76, 78], "width": 95, "height": 112}}, "biyan": {"biyan": {"x": 4.73, "y": -10.73, "rotation": -90.95, "width": 150, "height": 34}}}}], "events": {"atk": {}}, "animations": {"boss_attack1": {"slots": {"biyan": {"attachment": [{"name": null}, {"time": 0.4667, "name": "biyan"}]}, "eye": {"attachment": [{"time": 0.4667, "name": null}, {"time": 0.6, "name": "eye"}]}}, "bones": {"bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -7.99, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 10.09, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 110.58, "y": 36.88, "curve": 0.25, "c3": 0.685, "c4": 1.73}, {"time": 0.4333, "x": -52.86, "y": -27.83, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "xie": {"rotate": [{"angle": -51.18}, {"time": 0.1, "angle": 128.82}, {"time": 0.2333, "angle": -50.18}, {"time": 0.2667, "angle": -103.39}, {"time": 0.3333, "angle": 128.82}, {"time": 0.4333, "angle": -51.18}, {"time": 0.5667, "angle": 128.82}, {"time": 0.6333, "angle": -50.18}, {"time": 0.6667, "angle": -103.39}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -4.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 1.27, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone4": {"rotate": [{"angle": 0.29, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -4.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 1.27, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.6667, "angle": 0.29}]}, "bone5": {"rotate": [{"angle": 0.76, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1333, "angle": -4.24, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 5.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 1.27, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.6667, "angle": 0.76}]}, "bone10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.89, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 4.78, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone11": {"rotate": [{"angle": 1.94, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -6.89, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 4.78, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.6667, "angle": 1.94}]}, "bone14": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -11.71, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 9.08, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone15": {"rotate": [{"angle": 2.58, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -11.71, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 9.08, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "angle": 2.58}]}, "bone16": {"rotate": [{"angle": 6.51, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -11.71, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 9.08, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "angle": 6.51}]}, "bone17": {"rotate": [{"angle": 9.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -11.71, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 9.08}]}, "bone18": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -11.71, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 9.08, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone19": {"rotate": [{"angle": 2.58, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -11.71, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 9.08, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "angle": 2.58}]}, "bone20": {"rotate": [{"angle": 6.51, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -11.71, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 9.08, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "angle": 6.51}]}, "bone21": {"rotate": [{"angle": 9.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -11.71, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 9.08}]}, "bone22": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 2.97, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone23": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 2.97, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone24": {"rotate": [{"angle": 1.47, "curve": 0.351, "c2": 0.39, "c3": 0.701, "c4": 0.78}, {"time": 0.0667, "angle": 0.29, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -5.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 2.97, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 1.47}]}, "bone25": {"rotate": [{"angle": 2.97, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.0667, "angle": 2.13, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -5.44, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 2.97}]}, "bone26": {"rotate": [{"angle": 0.29, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -5.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 2.97, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.6667, "angle": 0.29}]}, "bone27": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 2.97, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone28": {"rotate": [{"angle": 0.29, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -5.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 2.97, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.6667, "angle": 0.29}]}, "bone29": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 2.97, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone30": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 2.97, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone31": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 2.97, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone32": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 2.97, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone33": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 2.97, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone34": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 2.97, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone35": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 2.97, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone36": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 2.97, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone37": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 2.97, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone38": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 2.97, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone39": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 2.97, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone43": {"rotate": [{"angle": 0.84, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -5.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 2.97, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "angle": 0.84}]}, "bone44": {"rotate": [{"angle": 2.13, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -5.44, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 2.97, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "angle": 2.13}]}, "bone45": {"rotate": [{"angle": 0.84, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -5.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 2.97, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "angle": 0.84}]}, "bone46": {"rotate": [{"angle": 2.13, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -5.44, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 2.97, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "angle": 2.13}]}, "bone47": {"rotate": [{"angle": 0.84, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -5.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 2.97, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "angle": 0.84}]}, "bone48": {"rotate": [{"angle": 1.49, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -5.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 2.97, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 1.49}]}, "bone49": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -8.5, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone50": {"rotate": [{"angle": 0.93, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -8.5, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 3.26, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "angle": 0.93}]}, "bone51": {"rotate": [{"angle": 2.34, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -8.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 3.26, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "angle": 2.34}]}, "bone52": {"rotate": [{"angle": 0.93, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -8.5, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 3.26, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "angle": 0.93}]}, "bone53": {"rotate": [{"angle": 2.34, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.0667, "angle": 0.93, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -8.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 3.26, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "angle": 2.34}]}, "bone54": {"rotate": [{"angle": 3.26, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.0667, "angle": 2.34, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -8.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 3.26}]}, "bone55": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.93, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 10.83, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone56": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.93, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 10.83, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone57": {"rotate": [{"angle": 3.07, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -6.93, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 10.83, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "angle": 3.07}]}, "bone58": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.93, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 10.83, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone59": {"rotate": [{"angle": 7.76, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -6.93, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 10.83, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "angle": 7.76}]}, "bone60": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.93, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 10.83, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone61": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.93, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 10.83, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone62": {"rotate": [{"angle": 10.83, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -6.93, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 10.83}]}, "bone63": {"rotate": [{"angle": 5.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -6.93, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 10.83, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 5.42}]}, "bone64": {"rotate": [{"angle": 5.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -6.93, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 10.83, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 5.42}]}, "bone65": {"rotate": [{"angle": 9.78, "curve": 0.295, "c2": 0.21, "c3": 0.667, "c4": 0.67}, {"time": 0.1, "angle": 3.07, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -6.93, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 10.83, "curve": 0.29, "c3": 0.629, "c4": 0.37}, {"time": 0.6667, "angle": 9.78}]}, "bone66": {"rotate": [{"angle": 5.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -6.93, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 10.83, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 5.42}]}, "bone67": {"rotate": [{"angle": 7.55, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": 10.83, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1, "angle": 7.76, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -6.93, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.6667, "angle": 7.55}]}, "bone68": {"rotate": [{"angle": 5.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -6.93, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 10.83, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 5.42}]}, "bone69": {"rotate": [{"angle": -3.65, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1, "angle": 10.83, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -6.93, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.6667, "angle": -3.65}]}, "bone70": {"rotate": [{"angle": 5.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -6.93, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 10.83, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 5.42}]}, "bone72": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -13.74, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 12.17, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone73": {"rotate": [{"angle": 8.72, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -13.74, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 12.17, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "angle": 8.72}]}, "bone74": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -13.74, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 12.17, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone75": {"rotate": [{"angle": 8.72, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -13.74, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 12.17, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "angle": 8.72}]}}, "events": [{"time": 0.4, "name": "atk"}]}, "boss_idle": {"slots": {"biyan": {"attachment": [{"name": null}, {"time": 0.6667, "name": "biyan"}]}, "eye": {"attachment": [{"time": 0.6667, "name": null}, {"time": 0.7, "name": "eye"}]}}, "bones": {"bone12": {"scale": [{"time": 0.6, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 0.6333, "x": 0.188, "curve": "stepped"}, {"time": 0.6667, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 0.7, "x": 0.188, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.7667}]}, "bone2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": 69.78, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "xz": {"rotate": [{"angle": -7, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 12.16, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -7}]}, "xie": {"rotate": [{"angle": -51.18}, {"time": 0.1, "angle": 128.82}, {"time": 0.2333, "angle": -50.18}, {"time": 0.2667, "angle": -103.39}, {"time": 0.3333, "angle": 128.82}, {"time": 0.4333, "angle": -51.18}, {"time": 0.5667, "angle": 128.82}, {"time": 0.6333, "angle": -50.18}, {"time": 0.6667, "angle": -103.39}, {"time": 0.7667, "angle": 128.82}, {"time": 0.9, "angle": -50.18}, {"time": 0.9333, "angle": -103.39}, {"time": 1, "angle": 128.82}, {"time": 1.1333, "angle": -51.18}, {"time": 1.2333, "angle": 128.82}, {"time": 1.3, "angle": -50.18}, {"time": 1.3333, "angle": -103.39}]}, "bone3": {"rotate": [{"angle": -0.49, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -1.72, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.3333, "angle": -0.49}]}, "bone4": {"rotate": [{"angle": -1.23, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.2333, "angle": -0.49, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -1.72, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "angle": -1.23}], "translate": [{"x": 2.21, "y": -1.96, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.2333, "x": 0.88, "y": -0.78, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": 3.09, "y": -2.74, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "x": 2.21, "y": -1.96}]}, "bone5": {"rotate": [{"angle": -1.72, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2333, "angle": -1.23, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.72}], "translate": [{"x": 2.57, "y": -1.39, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2333, "x": 1.84, "y": -1, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 2.57, "y": -1.39}]}, "bone6": {"translate": [{"x": -2, "y": -4.14, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": -2.8, "y": -5.78, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "x": -2, "y": -4.14}]}, "bone7": {"translate": [{"x": -0.79, "y": -1.64, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": -2.8, "y": -5.78, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.3333, "x": -0.79, "y": -1.64}]}, "bone8": {"translate": [{"x": -0.27, "y": -0.56, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.5667, "x": -2.8, "y": -5.78, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 1.3333, "x": -0.27, "y": -0.56}]}, "bone10": {"rotate": [{"angle": -1.52, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -5.34, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.3333, "angle": -1.52}]}, "bone11": {"rotate": [{"angle": -3.82, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -5.34, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "angle": -3.82}]}, "bone71": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 4.02, "y": 13.66, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "bone14": {"rotate": [{"angle": -1.27, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "angle": -9.74, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1, "angle": -2.77, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.2, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": -1.27}]}, "bone15": {"rotate": [{"angle": -0.66, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -9.74, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1, "angle": -6.98, "curve": 0.336, "c2": 0.34, "c3": 0.714, "c4": 0.82}, {"time": 1.3333, "angle": -0.66}]}, "bone16": {"rotate": [{"angle": -4.44, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -9.74, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1.3333, "angle": -4.44}]}, "bone17": {"rotate": [{"angle": -8.48, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1, "angle": -6.98, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.2, "angle": -9.74, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": -8.48}]}, "bone18": {"rotate": [{"angle": 1.65, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "angle": 12.69, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1, "angle": 3.6, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.2, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": 1.65}]}, "bone19": {"rotate": [{"angle": 0.86, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 12.69, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1, "angle": 9.09, "curve": 0.336, "c2": 0.34, "c3": 0.714, "c4": 0.82}, {"time": 1.3333, "angle": 0.86}]}, "bone20": {"rotate": [{"angle": 5.78, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 12.69, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1.3333, "angle": 5.78}]}, "bone21": {"rotate": [{"angle": 11.03, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1, "angle": 9.09, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.2, "angle": 12.69, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": 11.03}]}, "bone23": {"rotate": [{"angle": -1.91, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -6.74, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.3333, "angle": -1.91}]}, "bone24": {"rotate": [{"angle": -4.83, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "angle": -6.74, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.3333, "angle": -4.83}]}, "bone25": {"rotate": [{"angle": -1.91, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333, "angle": -6.74, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "angle": -1.91}]}, "bone26": {"rotate": [{"angle": -4.83, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -6.74, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "angle": -4.83}]}, "bone27": {"rotate": [{"angle": -6.74, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -6.74}]}, "bone28": {"rotate": [{"angle": -1.91, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -6.74, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.3333, "angle": -1.91}]}, "bone29": {"rotate": [{"angle": -4.83, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -6.74, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "angle": -4.83}]}, "bone30": {"rotate": [{"angle": -6.74, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -6.74}]}, "bone31": {"rotate": [{"angle": -4.83, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "angle": -6.74, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.3333, "angle": -4.83}]}, "bone32": {"rotate": [{"angle": -6.74, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -6.74}]}, "bone33": {"rotate": [{"angle": -4.83, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "angle": -6.74, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.3333, "angle": -4.83}]}, "bone34": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -7.37, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "bone35": {"rotate": [{"angle": -2.09, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -7.37, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.3333, "angle": -2.09}]}, "bone36": {"rotate": [{"angle": -5.28, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -7.37, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "angle": -5.28}]}, "bone37": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -7.37, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "bone38": {"rotate": [{"angle": -2.09, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -7.37, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.3333, "angle": -2.09}]}, "bone39": {"rotate": [{"angle": -5.28, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -7.37, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "angle": -5.28}]}, "bone40": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 5.83, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "bone41": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 5.83, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "bone42": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 5.83, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "bone43": {"rotate": [{"angle": 13.03, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.2, "angle": 16.46, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.7, "angle": 0.72, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.9, "angle": -3.83, "curve": 0.356, "c2": 0.41, "c3": 0.704, "c4": 0.8}, {"time": 1.3333, "angle": 13.03}]}, "bone44": {"rotate": [{"angle": -4.48, "curve": 0.307, "c2": 0.25, "c3": 0.657, "c4": 0.64}, {"time": 0.4667, "angle": 11.82, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.7, "angle": 6.06, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.1667, "angle": -8.47, "curve": 0.311, "c2": 0.23, "c3": 0.647, "c4": 0.57}, {"time": 1.3333, "angle": -4.48}]}, "bone45": {"rotate": [{"angle": -3.83, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -8.83, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 1.3333, "angle": -3.83}]}, "bone46": {"rotate": [{"angle": -8.47, "curve": 0.28, "c2": 0.15, "c3": 0.645, "c4": 0.59}, {"time": 0.3, "angle": -3.83, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -8.83, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.3333, "angle": -8.47}]}, "bone47": {"rotate": [{"angle": -1.91, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -6.74, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.3333, "angle": -1.91}]}, "bone48": {"rotate": [{"angle": -4.83, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -6.74, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "angle": -4.83}]}, "bone49": {"rotate": [{"angle": -2, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -7.05, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.3333, "angle": -2}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": -7.64, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "bone50": {"rotate": [{"angle": -5.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -7.05, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "angle": -5.05}]}, "bone51": {"rotate": [{"angle": -7.05, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -7.05}]}, "bone52": {"rotate": [{"angle": -2, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -7.05, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.3333, "angle": -2}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": 12.64, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "bone53": {"rotate": [{"angle": -5.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -7.05, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "angle": -5.05}]}, "bone54": {"rotate": [{"angle": -7.05, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -7.05}]}, "bone55": {"rotate": [{"angle": -3.9, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.9}]}, "bone56": {"rotate": [{"angle": -3.52, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1, "angle": -3.9, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.6667, "angle": -0.38, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.7667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 1.3333, "angle": -3.52}]}, "bone57": {"rotate": [{"angle": -2.79, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "angle": -3.9, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "angle": -1.11, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.9, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.3333, "angle": -2.79}]}, "bone58": {"rotate": [{"angle": -1.95, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": -3.9, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": -1.95, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": -1.95}]}, "bone59": {"rotate": [{"angle": -1.11, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333, "angle": -3.9, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "angle": -2.79, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.1333, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "angle": -1.11}]}, "bone60": {"rotate": [{"angle": -0.38, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.5667, "angle": -3.9, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.6667, "angle": -3.52, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 1.2333, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 1.3333, "angle": -0.38}]}, "bone61": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -3.9, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "bone62": {"rotate": [{"angle": -0.38, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.6667, "angle": -3.52, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.7667, "angle": -3.9, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 1.3333, "angle": -0.38}]}, "bone63": {"rotate": [{"angle": -3.9, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.9}]}, "bone64": {"rotate": [{"angle": -3.52, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1, "angle": -3.9, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.6667, "angle": -0.38, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.7667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 1.3333, "angle": -3.52}]}, "bone65": {"rotate": [{"angle": -2.79, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "angle": -3.9, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "angle": -1.11, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.9, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.3333, "angle": -2.79}]}, "bone66": {"rotate": [{"angle": -1.95, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": -3.9, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": -1.95, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": -1.95}]}, "bone67": {"rotate": [{"angle": -1.11, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333, "angle": -3.9, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "angle": -2.79, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.1333, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "angle": -1.11}]}, "bone68": {"rotate": [{"angle": -0.38, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.5667, "angle": -3.9, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.6667, "angle": -3.52, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 1.2333, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 1.3333, "angle": -0.38}]}, "bone69": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -3.9, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "bone70": {"rotate": [{"angle": -0.38, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.6667, "angle": -3.52, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.7667, "angle": -3.9, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 1.3333, "angle": -0.38}]}, "bone72": {"rotate": [{"angle": 2.81, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 9.9, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.3333, "angle": 2.81}]}, "bone73": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 9.9, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "bone74": {"rotate": [{"angle": -2.33, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -8.2, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.3333, "angle": -2.33}]}, "bone75": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -8.2, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "bone77": {"rotate": [{"angle": 2.83, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 9.96, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.3333, "angle": 2.83}]}, "bone78": {"rotate": [{"angle": 7.14, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 9.96, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "angle": 7.14}]}, "bone79": {"rotate": [{"angle": 9.96, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 9.96}]}}}, "die": {"slots": {"xz": {"attachment": [{"name": null}]}, "biyan": {"attachment": [{"name": null}, {"time": 0.0667, "name": "biyan"}]}, "wb1": {"attachment": [{"name": "wb1"}]}, "eye": {"attachment": [{"time": 0.0667, "name": null}]}}, "bones": {"bone80": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 75.74}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -101.61}], "translate": [{"curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2667, "x": 2.81, "y": 196.25, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4333, "y": -201.53, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.5, "y": -153.78, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5667, "y": -201.53}]}, "bone55": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 55, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 82.67}]}, "bone63": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 55, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 82.67}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 6.76, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -4.86, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone4": {"rotate": [{"angle": -0.47, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 6.76, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -4.86, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.5, "angle": -0.47}]}, "bone5": {"rotate": [{"angle": -1.38, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 6.76, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -4.86, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5, "angle": -1.38}]}, "bone14": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 58.85}]}, "bone18": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -43.19}]}, "bone10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 92.9}]}}}, "hurt": {"slots": {"biyan": {"attachment": [{"name": null}, {"time": 0.1, "name": "biyan"}]}, "eye": {"attachment": [{"time": 0.1, "name": null}]}}, "bones": {"bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 9.37, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -25.73, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 21.64, "curve": 0.003, "c2": 0.35, "c3": 0.499, "c4": 2.88}, {"time": 0.2667, "x": 86.55, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 6.39, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -4.03, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}]}, "bone4": {"rotate": [{"angle": -1.15, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 6.39, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -4.03, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.4667, "angle": -1.15}]}, "bone5": {"rotate": [{"angle": -2.89, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "angle": 12.4, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 6.39, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -4.03, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.4667, "angle": -2.89}]}, "bone23": {"rotate": [{"curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 0.1333, "angle": 5.65, "curve": 0.319, "c2": 0.29, "c3": 0.681, "c4": 0.71}, {"time": 0.3333, "angle": -18.57, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.4667}]}, "bone24": {"rotate": [{"curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 0.1333, "angle": 5.65, "curve": 0.319, "c2": 0.29, "c3": 0.681, "c4": 0.71}, {"time": 0.3333, "angle": -18.57, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.4667}]}, "bone25": {"rotate": [{"curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 0.1333, "angle": 5.65, "curve": 0.319, "c2": 0.29, "c3": 0.681, "c4": 0.71}, {"time": 0.3333, "angle": -18.57, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.4667}]}, "bone28": {"rotate": [{"curve": 0.282, "c3": 0.624, "c4": 0.39}, {"time": 0.1, "angle": 10.88, "curve": 0.306, "c2": 0.25, "c3": 0.678, "c4": 0.7}, {"time": 0.3333, "angle": -10.73, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.4667}]}, "bone29": {"rotate": [{"curve": 0.282, "c3": 0.624, "c4": 0.39}, {"time": 0.1, "angle": 10.88, "curve": 0.306, "c2": 0.25, "c3": 0.678, "c4": 0.7}, {"time": 0.3333, "angle": -10.73, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.4667}]}, "bone26": {"rotate": [{"curve": 0.282, "c3": 0.624, "c4": 0.39}, {"time": 0.1, "angle": 10.88, "curve": 0.306, "c2": 0.25, "c3": 0.678, "c4": 0.7}, {"time": 0.3333, "angle": -10.73, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.4667}]}, "bone27": {"rotate": [{"curve": 0.282, "c3": 0.624, "c4": 0.39}, {"time": 0.1, "angle": 10.88, "curve": 0.306, "c2": 0.25, "c3": 0.678, "c4": 0.7}, {"time": 0.3333, "angle": -10.73, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.4667}]}, "bone47": {"rotate": [{"curve": 0.282, "c3": 0.624, "c4": 0.39}, {"time": 0.1, "angle": 10.88, "curve": 0.306, "c2": 0.25, "c3": 0.678, "c4": 0.7}, {"time": 0.3333, "angle": -10.73, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.4667}]}, "bone48": {"rotate": [{"curve": 0.282, "c3": 0.624, "c4": 0.39}, {"time": 0.1, "angle": 10.88, "curve": 0.306, "c2": 0.25, "c3": 0.678, "c4": 0.7}, {"time": 0.3333, "angle": -10.73, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.4667}]}, "bone43": {"rotate": [{"curve": 0.282, "c3": 0.624, "c4": 0.39}, {"time": 0.1, "angle": 10.88, "curve": 0.306, "c2": 0.25, "c3": 0.678, "c4": 0.7}, {"time": 0.3333, "angle": -10.73, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.4667}]}, "bone44": {"rotate": [{"curve": 0.282, "c3": 0.624, "c4": 0.39}, {"time": 0.1, "angle": 10.88, "curve": 0.306, "c2": 0.25, "c3": 0.678, "c4": 0.7}, {"time": 0.3333, "angle": -10.73, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.4667}]}, "bone63": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 10.5, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -3.27, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}]}, "bone55": {"rotate": [{"angle": -1.2, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 10.5, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -3.27, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.4667, "angle": -1.2}]}, "bone56": {"rotate": [{"angle": -1.2, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 10.5, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -3.27, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.4667, "angle": -1.2}]}, "bone64": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 10.5, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -3.27, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}]}, "bone57": {"rotate": [{"angle": -2.84, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.0667, "angle": -1.2, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 10.5, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -3.27, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.4667, "angle": -2.84}]}, "bone65": {"rotate": [{"angle": -1.2, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 10.5, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -3.27, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.4667, "angle": -1.2}]}, "bone58": {"rotate": [{"angle": -1.2, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 10.5, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -3.27, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.4667, "angle": -1.2}]}, "bone66": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 10.5, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -3.27, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}]}, "bone59": {"rotate": [{"angle": -1.2, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 10.5, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -3.27, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.4667, "angle": -1.2}]}, "bone67": {"rotate": [{"angle": -2.84, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 10.5, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -3.27, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.4667, "angle": -2.84}]}, "bone60": {"rotate": [{"angle": -1.94, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "angle": -3.27, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.0667, "angle": -2.84, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 10.5, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.4667, "angle": -1.94}]}, "bone68": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 10.5, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -3.27, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}]}, "bone61": {"rotate": [{"angle": -1.2, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 10.5, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -3.27, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.4667, "angle": -1.2}]}, "bone69": {"rotate": [{"angle": -3.27, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 10.5, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -3.27}]}, "bone62": {"rotate": [{"angle": 0.64, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "angle": -3.27, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 10.5, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.4667, "angle": 0.64}]}, "bone70": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 10.5, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -3.27, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}]}, "bone14": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 12.8, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -16.05, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}]}, "bone15": {"rotate": [{"angle": -2.96, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 12.8, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -16.05, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": -2.96}]}, "bone16": {"rotate": [{"angle": -13.09, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 12.8, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -16.05, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5, "angle": -13.09}]}, "bone17": {"rotate": [{"angle": -13.82, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "angle": -16.05, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 12.8, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.5, "angle": -13.82}]}, "bone18": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 12.8, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -16.05, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}]}, "bone19": {"rotate": [{"angle": -2.96, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 12.8, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -16.05, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": -2.96}]}, "bone20": {"rotate": [{"angle": -13.09, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 12.8, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -16.05, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5, "angle": -13.09}]}, "bone21": {"rotate": [{"angle": -13.82, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "angle": -16.05, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 12.8, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.5, "angle": -13.82}]}, "xie": {"rotate": [{"angle": -51.18}, {"time": 0.0667, "angle": 128.82}, {"time": 0.1667, "angle": -50.18}, {"time": 0.2, "angle": -103.39}, {"time": 0.2333, "angle": 128.82}, {"time": 0.3333, "angle": -51.18}, {"time": 0.4333, "angle": 128.82}, {"time": 0.4667, "angle": -50.18}, {"time": 0.5, "angle": -103.39}]}}}}}