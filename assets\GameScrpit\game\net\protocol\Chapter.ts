// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v4.25.1
// source: Chapter.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "sim";

/**  */
export interface BossChapterPassResponse {
  /** 收回的宝箱ID列表 */
  treasureIdList: number[];
  /** Boss关卡是否通关 */
  win: boolean;
  /** Boss关卡战斗回放 */
  replay: string;
  /** 气运鼓舞次数 */
  energyEncourage: number;
  /** 道具鼓舞次数 */
  itemEncourage: number;
}

/**  */
export interface BossChapterRewardResponse {
  /** 最新的关卡ID 如果值为-1，则关卡已经全部通关 */
  chapterId: number;
  /** 最新的子关卡ID */
  chapterChildId: number;
  /** 偶数索引为道具的ID，奇数为数量 */
  resAddList: number[];
  /** 气运鼓舞次数 */
  energyEncourage: number;
  /** 道具鼓舞次数 */
  itemEncourage: number;
  /** 繁荣度参数 */
  speedParam: number;
}

/**  */
export interface ChapterMessage {
  /** 当前关卡ID 如果值为-1，则关卡已经全部通关 */
  chapterId: number;
  /** 当前是第几个子关卡 */
  chapterChildId: number;
  /** 气运鼓舞次数 */
  energyEncourage: number;
  /** 道具鼓舞次数 */
  itemEncourage: number;
  /** 繁荣度参数 */
  speedParam: number;
  /** 宝箱集合 */
  treasureMap: { [key: number]: ChapterTreasureMessage };
}

export interface ChapterMessage_TreasureMapEntry {
  key: number;
  value: ChapterTreasureMessage | undefined;
}

/**  */
export interface ChapterTreasureMessage {
  /** 箱子的编号 */
  id: number;
  /** 是否展示在战斗界面上 */
  show: boolean;
  /** 创建的时间戳 */
  createTime: number;
}

/**  */
export interface ChapterTreasureResponse {
  /** 获取到的资源 */
  addResList: number[];
  /** 剩余的宝箱 */
  treasureMap: { [key: number]: ChapterTreasureMessage };
}

export interface ChapterTreasureResponse_TreasureMapEntry {
  key: number;
  value: ChapterTreasureMessage | undefined;
}

/**  */
export interface RegularChapterPassResponse {
  /** 偶数索引为道具的ID，奇数为数量 */
  resAddList: number[];
  /** 新增的宝箱 */
  treasureList: ChapterTreasureMessage[];
  /** 最新的关卡ID 如果值为-1，则关卡已经全部通关 */
  chapterId: number;
  /** 最新的子关卡ID */
  chapterChildId: number;
  /** 繁荣度参数 */
  speedParam: number;
}

function createBaseBossChapterPassResponse(): BossChapterPassResponse {
  return { treasureIdList: [], win: false, replay: "", energyEncourage: 0, itemEncourage: 0 };
}

export const BossChapterPassResponse: MessageFns<BossChapterPassResponse> = {
  encode(message: BossChapterPassResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.treasureIdList) {
      writer.int64(v);
    }
    writer.join();
    if (message.win !== false) {
      writer.uint32(16).bool(message.win);
    }
    if (message.replay !== "") {
      writer.uint32(26).string(message.replay);
    }
    if (message.energyEncourage !== 0) {
      writer.uint32(32).int32(message.energyEncourage);
    }
    if (message.itemEncourage !== 0) {
      writer.uint32(40).int32(message.itemEncourage);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BossChapterPassResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBossChapterPassResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 8) {
            message.treasureIdList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.treasureIdList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.win = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.replay = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.energyEncourage = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.itemEncourage = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<BossChapterPassResponse>, I>>(base?: I): BossChapterPassResponse {
    return BossChapterPassResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BossChapterPassResponse>, I>>(object: I): BossChapterPassResponse {
    const message = createBaseBossChapterPassResponse();
    message.treasureIdList = object.treasureIdList?.map((e) => e) || [];
    message.win = object.win ?? false;
    message.replay = object.replay ?? "";
    message.energyEncourage = object.energyEncourage ?? 0;
    message.itemEncourage = object.itemEncourage ?? 0;
    return message;
  },
};

function createBaseBossChapterRewardResponse(): BossChapterRewardResponse {
  return { chapterId: 0, chapterChildId: 0, resAddList: [], energyEncourage: 0, itemEncourage: 0, speedParam: 0 };
}

export const BossChapterRewardResponse: MessageFns<BossChapterRewardResponse> = {
  encode(message: BossChapterRewardResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.chapterId !== 0) {
      writer.uint32(8).int64(message.chapterId);
    }
    if (message.chapterChildId !== 0) {
      writer.uint32(16).int32(message.chapterChildId);
    }
    writer.uint32(26).fork();
    for (const v of message.resAddList) {
      writer.double(v);
    }
    writer.join();
    if (message.energyEncourage !== 0) {
      writer.uint32(32).int32(message.energyEncourage);
    }
    if (message.itemEncourage !== 0) {
      writer.uint32(40).int32(message.itemEncourage);
    }
    if (message.speedParam !== 0) {
      writer.uint32(49).double(message.speedParam);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BossChapterRewardResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBossChapterRewardResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.chapterId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.chapterChildId = reader.int32();
          continue;
        }
        case 3: {
          if (tag === 25) {
            message.resAddList.push(reader.double());

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.resAddList.push(reader.double());
            }

            continue;
          }

          break;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.energyEncourage = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.itemEncourage = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 49) {
            break;
          }

          message.speedParam = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<BossChapterRewardResponse>, I>>(base?: I): BossChapterRewardResponse {
    return BossChapterRewardResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BossChapterRewardResponse>, I>>(object: I): BossChapterRewardResponse {
    const message = createBaseBossChapterRewardResponse();
    message.chapterId = object.chapterId ?? 0;
    message.chapterChildId = object.chapterChildId ?? 0;
    message.resAddList = object.resAddList?.map((e) => e) || [];
    message.energyEncourage = object.energyEncourage ?? 0;
    message.itemEncourage = object.itemEncourage ?? 0;
    message.speedParam = object.speedParam ?? 0;
    return message;
  },
};

function createBaseChapterMessage(): ChapterMessage {
  return { chapterId: 0, chapterChildId: 0, energyEncourage: 0, itemEncourage: 0, speedParam: 0, treasureMap: {} };
}

export const ChapterMessage: MessageFns<ChapterMessage> = {
  encode(message: ChapterMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.chapterId !== 0) {
      writer.uint32(8).int64(message.chapterId);
    }
    if (message.chapterChildId !== 0) {
      writer.uint32(16).int32(message.chapterChildId);
    }
    if (message.energyEncourage !== 0) {
      writer.uint32(24).int32(message.energyEncourage);
    }
    if (message.itemEncourage !== 0) {
      writer.uint32(32).int32(message.itemEncourage);
    }
    if (message.speedParam !== 0) {
      writer.uint32(41).double(message.speedParam);
    }
    Object.entries(message.treasureMap).forEach(([key, value]) => {
      ChapterMessage_TreasureMapEntry.encode({ key: key as any, value }, writer.uint32(50).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ChapterMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseChapterMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.chapterId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.chapterChildId = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.energyEncourage = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.itemEncourage = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 41) {
            break;
          }

          message.speedParam = reader.double();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          const entry6 = ChapterMessage_TreasureMapEntry.decode(reader, reader.uint32());
          if (entry6.value !== undefined) {
            message.treasureMap[entry6.key] = entry6.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ChapterMessage>, I>>(base?: I): ChapterMessage {
    return ChapterMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ChapterMessage>, I>>(object: I): ChapterMessage {
    const message = createBaseChapterMessage();
    message.chapterId = object.chapterId ?? 0;
    message.chapterChildId = object.chapterChildId ?? 0;
    message.energyEncourage = object.energyEncourage ?? 0;
    message.itemEncourage = object.itemEncourage ?? 0;
    message.speedParam = object.speedParam ?? 0;
    message.treasureMap = Object.entries(object.treasureMap ?? {}).reduce<{ [key: number]: ChapterTreasureMessage }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = ChapterTreasureMessage.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBaseChapterMessage_TreasureMapEntry(): ChapterMessage_TreasureMapEntry {
  return { key: 0, value: undefined };
}

export const ChapterMessage_TreasureMapEntry: MessageFns<ChapterMessage_TreasureMapEntry> = {
  encode(message: ChapterMessage_TreasureMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== undefined) {
      ChapterTreasureMessage.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ChapterMessage_TreasureMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseChapterMessage_TreasureMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = ChapterTreasureMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ChapterMessage_TreasureMapEntry>, I>>(base?: I): ChapterMessage_TreasureMapEntry {
    return ChapterMessage_TreasureMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ChapterMessage_TreasureMapEntry>, I>>(
    object: I,
  ): ChapterMessage_TreasureMapEntry {
    const message = createBaseChapterMessage_TreasureMapEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? ChapterTreasureMessage.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseChapterTreasureMessage(): ChapterTreasureMessage {
  return { id: 0, show: false, createTime: 0 };
}

export const ChapterTreasureMessage: MessageFns<ChapterTreasureMessage> = {
  encode(message: ChapterTreasureMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).int64(message.id);
    }
    if (message.show !== false) {
      writer.uint32(16).bool(message.show);
    }
    if (message.createTime !== 0) {
      writer.uint32(24).int64(message.createTime);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ChapterTreasureMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseChapterTreasureMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.id = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.show = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.createTime = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ChapterTreasureMessage>, I>>(base?: I): ChapterTreasureMessage {
    return ChapterTreasureMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ChapterTreasureMessage>, I>>(object: I): ChapterTreasureMessage {
    const message = createBaseChapterTreasureMessage();
    message.id = object.id ?? 0;
    message.show = object.show ?? false;
    message.createTime = object.createTime ?? 0;
    return message;
  },
};

function createBaseChapterTreasureResponse(): ChapterTreasureResponse {
  return { addResList: [], treasureMap: {} };
}

export const ChapterTreasureResponse: MessageFns<ChapterTreasureResponse> = {
  encode(message: ChapterTreasureResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.addResList) {
      writer.double(v);
    }
    writer.join();
    Object.entries(message.treasureMap).forEach(([key, value]) => {
      ChapterTreasureResponse_TreasureMapEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ChapterTreasureResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseChapterTreasureResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 9) {
            message.addResList.push(reader.double());

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.addResList.push(reader.double());
            }

            continue;
          }

          break;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = ChapterTreasureResponse_TreasureMapEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.treasureMap[entry2.key] = entry2.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ChapterTreasureResponse>, I>>(base?: I): ChapterTreasureResponse {
    return ChapterTreasureResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ChapterTreasureResponse>, I>>(object: I): ChapterTreasureResponse {
    const message = createBaseChapterTreasureResponse();
    message.addResList = object.addResList?.map((e) => e) || [];
    message.treasureMap = Object.entries(object.treasureMap ?? {}).reduce<{ [key: number]: ChapterTreasureMessage }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = ChapterTreasureMessage.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBaseChapterTreasureResponse_TreasureMapEntry(): ChapterTreasureResponse_TreasureMapEntry {
  return { key: 0, value: undefined };
}

export const ChapterTreasureResponse_TreasureMapEntry: MessageFns<ChapterTreasureResponse_TreasureMapEntry> = {
  encode(message: ChapterTreasureResponse_TreasureMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== undefined) {
      ChapterTreasureMessage.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ChapterTreasureResponse_TreasureMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseChapterTreasureResponse_TreasureMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = ChapterTreasureMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ChapterTreasureResponse_TreasureMapEntry>, I>>(
    base?: I,
  ): ChapterTreasureResponse_TreasureMapEntry {
    return ChapterTreasureResponse_TreasureMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ChapterTreasureResponse_TreasureMapEntry>, I>>(
    object: I,
  ): ChapterTreasureResponse_TreasureMapEntry {
    const message = createBaseChapterTreasureResponse_TreasureMapEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? ChapterTreasureMessage.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseRegularChapterPassResponse(): RegularChapterPassResponse {
  return { resAddList: [], treasureList: [], chapterId: 0, chapterChildId: 0, speedParam: 0 };
}

export const RegularChapterPassResponse: MessageFns<RegularChapterPassResponse> = {
  encode(message: RegularChapterPassResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.resAddList) {
      writer.double(v);
    }
    writer.join();
    for (const v of message.treasureList) {
      ChapterTreasureMessage.encode(v!, writer.uint32(18).fork()).join();
    }
    if (message.chapterId !== 0) {
      writer.uint32(24).int64(message.chapterId);
    }
    if (message.chapterChildId !== 0) {
      writer.uint32(32).int32(message.chapterChildId);
    }
    if (message.speedParam !== 0) {
      writer.uint32(41).double(message.speedParam);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RegularChapterPassResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRegularChapterPassResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 9) {
            message.resAddList.push(reader.double());

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.resAddList.push(reader.double());
            }

            continue;
          }

          break;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.treasureList.push(ChapterTreasureMessage.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.chapterId = longToNumber(reader.int64());
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.chapterChildId = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 41) {
            break;
          }

          message.speedParam = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<RegularChapterPassResponse>, I>>(base?: I): RegularChapterPassResponse {
    return RegularChapterPassResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RegularChapterPassResponse>, I>>(object: I): RegularChapterPassResponse {
    const message = createBaseRegularChapterPassResponse();
    message.resAddList = object.resAddList?.map((e) => e) || [];
    message.treasureList = object.treasureList?.map((e) => ChapterTreasureMessage.fromPartial(e)) || [];
    message.chapterId = object.chapterId ?? 0;
    message.chapterChildId = object.chapterChildId ?? 0;
    message.speedParam = object.speedParam ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
