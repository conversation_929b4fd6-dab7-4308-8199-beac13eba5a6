import { _decorator, Component, instantiate, isValid, Label, Node, RichText, Sprite, v3, Widget } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { JsonMgr } from "../../mgr/JsonMgr";
import { FriendModule } from "../../../module/friend/FriendModule";
import { TravelModule } from "../../../module/travel/TravelModule";
import ToolExt from "../../common/ToolExt";
import ResMgr from "../../../lib/common/ResMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
const { ccclass, property } = _decorator;
const log = Logger.getLoger(LOG_LEVEL.DEBUG);
@ccclass("UITravelFriend")
export class UITravelFriend extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_TRAVEL}?prefab/ui/UITravelFriend`;
  }
  protected _openAct: boolean = true; //打开动作
  private _resAddList: number = null;

  private _close_Is_bool: boolean = false;

  public init(args: any): void {
    super.init(args);
    this._resAddList = args.resAddList;
  }
  protected onEvtShow(): void {
    TravelModule.api.friendUnlockStatistics((res) => {
      log.log("12-13", res);
      let c_friend = JsonMgr.instance.jsonList.c_friend;
      let friendInfo = c_friend[this._resAddList[0] % 1000000];

      ResMgr.loadPrefab(
        `${BundleEnum.BUNDLE_COMMON_FRIEND}?prefab/friend_${friendInfo.id}`,
        (prefab) => {
          if (isValid(this.node) == false) {
            return;
          }
          let node = instantiate(prefab);
          this.getNode("friendImage").addChild(node);
          let compWid = node.addComponent(Widget);
          compWid.isAlignTop = true;
          compWid.top = 0;
          this.getNode("friendImage").walk((val) => {
            val.layer = this.node.layer;
          });
          this.getNode("bg").active = true;
        },
        this
      );

      this.getNode("title").getComponent(Label).string = friendInfo.name;
      this.getNode("layer1").active = false;
      if (FriendModule.data.getFriendMessage(friendInfo.id)) {
        //有
        this._close_Is_bool = true;
        this.getNode("layer1").active = true;
        this.getNode("rich_text").getComponent(RichText).string = "<color=#527c52>已结识仙友,再次偶遇,因果值+1</color>";
      } else {
        //没有
        log.log("仙友解锁类型====", friendInfo.unlock);
        if (friendInfo.unlock == 1) {
          this._close_Is_bool = true;
          if (this.node.isValid == false) {
            return;
          }

          let unlockNum = friendInfo.unlockNum == 0 ? 1 : friendInfo.unlockNum;
          if (friendInfo["isTarget"] == 1) {
            unlockNum = 1;
          }
          let count = ToolExt.friendUnlockOkNum(res, friendInfo.id);
          let str = count + "/" + unlockNum;
          this.getNode("layer1").active = true;
          this.getNode("rich_text").getComponent(
            RichText
          ).string = `<color=#527c52>游历偶遇次数达到</color><color=#df7676>${str}</color><color=#527c52>次结识</color>`;
        } else {
          this._close_Is_bool = true;
        }
      }
    });
  }

  on_click_btn_close() {
    if (this._close_Is_bool == false) {
      return;
    }
    UIMgr.instance.back();
  }

  on_click_btn_close_main() {
    if (this._close_Is_bool == false) {
      return;
    }
    UIMgr.instance.back();
  }

  protected onEvtClose(): void {}
}
