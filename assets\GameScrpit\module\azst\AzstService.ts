import MsgEnum from "../../game/event/MsgEnum";
import { BadgeMgr, BadgeType } from "../../game/mgr/BadgeMgr";
import { JsonMgr } from "../../game/mgr/JsonMgr";
import { ItemEnum } from "../../lib/common/ItemEnum";
import MsgMgr from "../../lib/event/MsgMgr";
import TickerMgr from "../../lib/ticker/TickerMgr";
import { PlayerModule } from "../player/PlayerModule";

export class AzstService {
  public init() {
    this.updateBadge();

    MsgMgr.on(MsgEnum.ON_EXIST_ITEM_CHANGE, this.updateBadge, this);

    TickerMgr.setInterval(
      3,
      () => {
        this.updateBadge();
      },
      false
    );
  }

  public updateBadge() {
    const itemNum = PlayerModule.data.getItemNum(ItemEnum.挑战券_1082);

    const c_pupilDrop = JsonMgr.instance.jsonList.c_pupilDrop;
    const list = Object.keys(c_pupilDrop);
    const oneIndex = list[0];
    const info = c_pupilDrop[oneIndex];

    const rs = info.max <= itemNum;
    BadgeMgr.instance.setShowById(BadgeType.btn_tiao_zhan.btn_yan_wu_chang.btn_showPk.id, rs);

    // todo 日志红点未做处理
  }
}
