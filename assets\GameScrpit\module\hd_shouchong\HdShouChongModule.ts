import MsgEnum from "../../game/event/MsgEnum";
import { GameData } from "../../game/GameData";
import data from "../../lib/data/data";
import MsgMgr from "../../lib/event/MsgMgr";
import { HdShouChongApi } from "./HdShouChongApi";
import { HdShouChongConfig } from "./HdShouChongConfig";
import { HdShouChongData } from "./HdShouChongData";
import { HdShouChongRoute } from "./HdShouChongRoute";
import { HdShouChongService } from "./HdShouChongService";
import { HdShouChongSubscriber } from "./HdShouChongSubscriber";
import { HdShouChongViewModel } from "./HdShouChongViewModel";

export class HdShouChongModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): HdShouChongModule {
    if (!GameData.instance.HdShouChongModule) {
      GameData.instance.HdShouChongModule = new HdShouChongModule();
    }
    return GameData.instance.HdShouChongModule;
  }

  private _data = new HdShouChongData();
  private _api = new HdShouChongApi();
  private _service = new HdShouChongService();
  private _config = new HdShouChongConfig();
  private _viewModel = new HdShouChongViewModel();
  private _route = new HdShouChongRoute();
  private _subscriber = new HdShouChongSubscriber();
  public static get data() {
    return this.instance._data;
  }
  public static get api() {
    return this.instance._api;
  }
  public static get service() {
    return this.instance._service;
  }
  public static get config() {
    return this.instance._config;
  }
  public static get viewModel() {
    return this.instance._viewModel;
  }

  public init(data?: any) {
    if (this._subscriber) {
      this._subscriber.unRegister();
    }
    this._data = new HdShouChongData();
    this._api = new HdShouChongApi();
    this._service = new HdShouChongService();
    this._config = new HdShouChongConfig();
    this._viewModel = new HdShouChongViewModel();
    this._route = new HdShouChongRoute();
    this._subscriber = new HdShouChongSubscriber();

    // 初始化模块
    this._data.init();
    this._subscriber.register();
    this._route.init();
  }

  protected saveKey(): string {
    return this.constructor.name;
  }
}
