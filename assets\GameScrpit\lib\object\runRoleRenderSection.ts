import { Layers, Node, instantiate, isValid, sp } from "cc";
import { DIRECT } from "./DirectSection";
import GameObject from "./GameObject";
import { Section } from "./Section";
import ResMgr from "../common/ResMgr";
import MsgMgr from "../event/MsgMgr";
import { PlayerModule } from "../../module/player/PlayerModule";
import FightManager, { scaleList } from "../../game/fight/manager/FightManager";
import { JsonMgr } from "../../game/mgr/JsonMgr";

export class PlayAnimationDetail {
  public name: string;
  public isLoop: boolean;
  public speed: number;
}

export default class runRoleRenderSection extends Section {
  /**上层节点 */
  private _Node: Node;
  /**这是具体实例形象 带spine组件的 */
  private _render: Node;

  /**动画速率 */
  protected _timeScale: number = 1;

  private _spineId: number;

  private _dir: number;

  /**统一加载完成回调 */
  private _initcallBack;

  public onStart(): void {
    super.onStart();
    this.emitMsg("OnRenderLoaded", this._render);
  }

  public onInit(go: GameObject, args) {
    super.onInit(go, args);
    this.onMsg("OnDirectChange", this.onDirectChange.bind(this));
    this.onMsg("OnPlayAnimation", this.onPlayAnimation.bind(this));

    this._timeScale = scaleList[FightManager.instance.speed];

    this._initcallBack = args.callBack;
    this._spineId = args.bulletId;

    let db = JsonMgr.instance.jsonList.c_bulletShow[this._spineId];
    this.initRender(db.res);
  }

  public onRemove(): void {
    super.onRemove();
    this.offMsg("OnDirectChange", this.onDirectChange.bind(this));
    this.offMsg("OnPlayAnimation", this.onPlayAnimation.bind(this));
  }

  /**更改形象的朝向 --- 朝向是更改上层节点，不更改render*/
  protected onDirectChange(direct) {
    if (direct == DIRECT.LEFT) {
      this._Node.setScale(-1, 1, 1);
    } else if (direct == DIRECT.RIGHT) {
      this._Node.setScale(1, 1, 1);
    }
  }

  /**播放形象的动画 */
  protected onPlayAnimation(detail) {
    let name = (detail as PlayAnimationDetail).name;
    let isLoop = (detail as PlayAnimationDetail).isLoop;

    let ske = this._render.getComponent(sp.Skeleton);

    /**动画开始播放消息事件 */
    ske.setStartListener((animation) => {
      this.emitMsg("OnAnimationStart", detail);
    });

    /**动画播放中特殊的消息事件 */
    ske.setEventListener((animation, event) => {
      this.emitMsg("OnAnimationEvent", event);
      if (event["data"].name == "PlaySound") {
        MsgMgr.emit("ON_PLAY_SOUND", event);
      }
    });

    /**动画播放结束的消息事件 */
    ske.setCompleteListener((data) => {
      this.emitMsg("OnAnimationCompleted", data);
    });

    ske.setAnimation(0, name, isLoop);
    this.setTimeScale(scaleList[FightManager.instance.speed]);
  }

  public setTimeScale(scale) {
    this._timeScale = scale;
    this._render.getComponent(sp.Skeleton).timeScale = scale;
  }

  protected async initRender(path: string) {
    ResMgr.loadPrefab(path, this.onLoadRender.bind(this));
  }

  protected onLoadRender(prefab) {
    if (isValid(this.getSub()) == false) return;
    this._Node = instantiate(prefab);
    // this._Node.layer = Layers.Enum["UILayer"];
    this.getSub().addChild(this._Node);
    this._Node.walk((child: Node) => (child.layer = this.getSub().layer));

    this._render = this._Node.getChildByName("render");
    // this._render.layer = Layers.Enum["UILayer"];
    this._Node.walk((child: Node) => (child.layer = this.getSub().layer));

    this.ready();
    this._initcallBack && this._initcallBack();
  }

  public updateSelf(dt: any): void {
    this._render.getComponent(sp.Skeleton).timeScale = this._timeScale;
  }

  public onPause() {
    if (this._render.getComponent(sp.Skeleton)) {
      this._render.getComponent(sp.Skeleton).timeScale = 0;
    }
  }
  public onResume() {
    if (this._render.getComponent(sp.Skeleton)) {
      this._render.getComponent(sp.Skeleton).timeScale = this._timeScale;
    }
  }

  public static sectionName(): string {
    return "runRoleRenderSection";
  }

  public getNode() {
    return this._Node;
  }

  public getRender(): Node {
    return this._render;
  }
  public getRenderScale() {
    return this._render.getScale();
  }
}
