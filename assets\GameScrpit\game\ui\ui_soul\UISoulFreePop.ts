import { _decorator, Label } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import Formate from "../../../lib/utils/Formate";
import { SoulModule } from "../../../module/soul/SoulModule";
import { DialogZero } from "../../GameDefine";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { SoulAudioName } from "../../../module/soul/SoulConstant";
const { ccclass, property } = _decorator;

@ccclass("UISoulFreePop")
export class UISoulFreePop extends UINode {
  protected _openAct: boolean = true;
  protected _isAddToBottom: boolean = true;
  public zOrder(): number {
    return DialogZero.UISoulFreePop;
  }
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_SOUL}?prefab/ui/UISoulFreePop`;
  }
  protected dependOn(): BundleEnum[] {
    return [BundleEnum.BUNDLE_COMMON_UI, BundleEnum.BUNDLE_COMMON_ITEM];
  }

  private _soulId: number = null;
  public init(param: any) {
    super.init(param);
    this._soulId = param.soulId;
  }
  protected onEvtShow(): void {
    let soulMsg = SoulModule.data.warriorSoulManageMsg.soulMap[this._soulId];
    let configSoul = SoulModule.data.getConfigSoul(soulMsg.soulTemplateId);
    // let level = soulMsg.stage * 5 + soulMsg.grade;

    let num = 0;
    // 升阶消耗
    for (let i = 0; i <= soulMsg.stage; i++) {
      let cost1 = 0;

      if (i < configSoul.costList.length) {
        cost1 = configSoul.costList[i];
      } else {
        cost1 = configSoul.costList[configSoul.costList.length - 1];
      }

      if (i < soulMsg.stage) {
        num += cost1 * 5;
      } else if (i == soulMsg.stage) {
        num += cost1 * soulMsg.grade;
      }
    }

    // 升级消耗返回比例
    num *= configSoul.returnRate / 10000;
    num += configSoul.freeRewardList[1];

    this.getNode("soul_level_lab").getComponent(Label).string = `${soulMsg.stage}阶${soulMsg.grade}级的`;
    this.getNode("soul_name_lab").getComponent(Label).string = `${configSoul.name}`;
    this.getNode("item_cost_lab").getComponent(Label).string = `x${Formate.format(num)}`;
  }

  private on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  private on_click_btn_cancel() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this.on_click_btn_close();
  }

  /**放生 */
  private on_click_btn_sure() {
    AudioMgr.instance.playEffect(SoulAudioName.Effect.点击放生按钮);
    SoulModule.api.freeSoul(this._soulId, (data: number[]) => {
      this.on_click_btn_close();
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data });
    });
  }
}
