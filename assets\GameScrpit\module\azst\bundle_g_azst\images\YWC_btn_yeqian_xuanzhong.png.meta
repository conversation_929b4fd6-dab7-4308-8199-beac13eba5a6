{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "a5e8d4e5-938b-41a0-8c03-4dc447998c05", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "a5e8d4e5-938b-41a0-8c03-4dc447998c05@6c48a", "displayName": "YWC_btn_yeqian_xuanzhong", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "a5e8d4e5-938b-41a0-8c03-4dc447998c05", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "a5e8d4e5-938b-41a0-8c03-4dc447998c05@f9941", "displayName": "YWC_btn_yeqian_xuanzhong", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 178, "height": 75, "rawWidth": 178, "rawHeight": 75, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-89, -37.5, 0, 89, -37.5, 0, -89, 37.5, 0, 89, 37.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 75, 178, 75, 0, 0, 178, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-89, -37.5, 0], "maxPos": [89, 37.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "a5e8d4e5-938b-41a0-8c03-4dc447998c05@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "a5e8d4e5-938b-41a0-8c03-4dc447998c05@6c48a"}}