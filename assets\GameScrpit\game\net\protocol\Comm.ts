// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v4.25.1
// source: Comm.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "sim";

/**  */
export interface BattleReplayMessage {
  /** 是否胜利 */
  win: boolean;
  /** 录像回放 */
  replay: string;
}

/**  */
export interface CommIntegerListMessage {
  intList: number[];
}

/**  */
export interface CommIntegerMapMessage {
  intMap: { [key: number]: number };
}

export interface CommIntegerMapMessage_IntMapEntry {
  key: number;
  value: number;
}

/**  */
export interface CommLongListMessage {
  longList: number[];
}

/**  */
export interface CommLongMapMessage {
  map: { [key: number]: number };
}

export interface CommLongMapMessage_MapEntry {
  key: number;
  value: number;
}

/**  */
export interface ErrorMessage {
  /** 错误码 */
  code: number;
  /** 异常参数 如果有多个参数，以#分割，同异常处理机制 */
  errorMgr: string;
}

/**  */
export interface RewardMessage {
  /** {30302, -1, 1036, 2,10601,-1,1002,5,3001,20} */
  transformList: number[];
  /** 获取道具列表 {30302, 1.6,10,10601,1} */
  rewardList: number[];
}

/**  */
export interface SystemOpenMessage {
  newList: number[];
  totalList: number[];
}

function createBaseBattleReplayMessage(): BattleReplayMessage {
  return { win: false, replay: "" };
}

export const BattleReplayMessage: MessageFns<BattleReplayMessage> = {
  encode(message: BattleReplayMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.win !== false) {
      writer.uint32(8).bool(message.win);
    }
    if (message.replay !== "") {
      writer.uint32(18).string(message.replay);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BattleReplayMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBattleReplayMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.win = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.replay = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<BattleReplayMessage>, I>>(base?: I): BattleReplayMessage {
    return BattleReplayMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BattleReplayMessage>, I>>(object: I): BattleReplayMessage {
    const message = createBaseBattleReplayMessage();
    message.win = object.win ?? false;
    message.replay = object.replay ?? "";
    return message;
  },
};

function createBaseCommIntegerListMessage(): CommIntegerListMessage {
  return { intList: [] };
}

export const CommIntegerListMessage: MessageFns<CommIntegerListMessage> = {
  encode(message: CommIntegerListMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.intList) {
      writer.int32(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CommIntegerListMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCommIntegerListMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 8) {
            message.intList.push(reader.int32());

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.intList.push(reader.int32());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CommIntegerListMessage>, I>>(base?: I): CommIntegerListMessage {
    return CommIntegerListMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CommIntegerListMessage>, I>>(object: I): CommIntegerListMessage {
    const message = createBaseCommIntegerListMessage();
    message.intList = object.intList?.map((e) => e) || [];
    return message;
  },
};

function createBaseCommIntegerMapMessage(): CommIntegerMapMessage {
  return { intMap: {} };
}

export const CommIntegerMapMessage: MessageFns<CommIntegerMapMessage> = {
  encode(message: CommIntegerMapMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.intMap).forEach(([key, value]) => {
      CommIntegerMapMessage_IntMapEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CommIntegerMapMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCommIntegerMapMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = CommIntegerMapMessage_IntMapEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.intMap[entry1.key] = entry1.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CommIntegerMapMessage>, I>>(base?: I): CommIntegerMapMessage {
    return CommIntegerMapMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CommIntegerMapMessage>, I>>(object: I): CommIntegerMapMessage {
    const message = createBaseCommIntegerMapMessage();
    message.intMap = Object.entries(object.intMap ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    return message;
  },
};

function createBaseCommIntegerMapMessage_IntMapEntry(): CommIntegerMapMessage_IntMapEntry {
  return { key: 0, value: 0 };
}

export const CommIntegerMapMessage_IntMapEntry: MessageFns<CommIntegerMapMessage_IntMapEntry> = {
  encode(message: CommIntegerMapMessage_IntMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int32(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CommIntegerMapMessage_IntMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCommIntegerMapMessage_IntMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CommIntegerMapMessage_IntMapEntry>, I>>(
    base?: I,
  ): CommIntegerMapMessage_IntMapEntry {
    return CommIntegerMapMessage_IntMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CommIntegerMapMessage_IntMapEntry>, I>>(
    object: I,
  ): CommIntegerMapMessage_IntMapEntry {
    const message = createBaseCommIntegerMapMessage_IntMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseCommLongListMessage(): CommLongListMessage {
  return { longList: [] };
}

export const CommLongListMessage: MessageFns<CommLongListMessage> = {
  encode(message: CommLongListMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.longList) {
      writer.int64(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CommLongListMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCommLongListMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 8) {
            message.longList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.longList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CommLongListMessage>, I>>(base?: I): CommLongListMessage {
    return CommLongListMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CommLongListMessage>, I>>(object: I): CommLongListMessage {
    const message = createBaseCommLongListMessage();
    message.longList = object.longList?.map((e) => e) || [];
    return message;
  },
};

function createBaseCommLongMapMessage(): CommLongMapMessage {
  return { map: {} };
}

export const CommLongMapMessage: MessageFns<CommLongMapMessage> = {
  encode(message: CommLongMapMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.map).forEach(([key, value]) => {
      CommLongMapMessage_MapEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CommLongMapMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCommLongMapMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = CommLongMapMessage_MapEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.map[entry1.key] = entry1.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CommLongMapMessage>, I>>(base?: I): CommLongMapMessage {
    return CommLongMapMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CommLongMapMessage>, I>>(object: I): CommLongMapMessage {
    const message = createBaseCommLongMapMessage();
    message.map = Object.entries(object.map ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    return message;
  },
};

function createBaseCommLongMapMessage_MapEntry(): CommLongMapMessage_MapEntry {
  return { key: 0, value: 0 };
}

export const CommLongMapMessage_MapEntry: MessageFns<CommLongMapMessage_MapEntry> = {
  encode(message: CommLongMapMessage_MapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int64(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CommLongMapMessage_MapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCommLongMapMessage_MapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CommLongMapMessage_MapEntry>, I>>(base?: I): CommLongMapMessage_MapEntry {
    return CommLongMapMessage_MapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CommLongMapMessage_MapEntry>, I>>(object: I): CommLongMapMessage_MapEntry {
    const message = createBaseCommLongMapMessage_MapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseErrorMessage(): ErrorMessage {
  return { code: 0, errorMgr: "" };
}

export const ErrorMessage: MessageFns<ErrorMessage> = {
  encode(message: ErrorMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.code !== 0) {
      writer.uint32(8).int32(message.code);
    }
    if (message.errorMgr !== "") {
      writer.uint32(18).string(message.errorMgr);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ErrorMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseErrorMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.code = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.errorMgr = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ErrorMessage>, I>>(base?: I): ErrorMessage {
    return ErrorMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ErrorMessage>, I>>(object: I): ErrorMessage {
    const message = createBaseErrorMessage();
    message.code = object.code ?? 0;
    message.errorMgr = object.errorMgr ?? "";
    return message;
  },
};

function createBaseRewardMessage(): RewardMessage {
  return { transformList: [], rewardList: [] };
}

export const RewardMessage: MessageFns<RewardMessage> = {
  encode(message: RewardMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.transformList) {
      writer.int64(v);
    }
    writer.join();
    writer.uint32(18).fork();
    for (const v of message.rewardList) {
      writer.double(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RewardMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRewardMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 8) {
            message.transformList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.transformList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
        case 2: {
          if (tag === 17) {
            message.rewardList.push(reader.double());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.rewardList.push(reader.double());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<RewardMessage>, I>>(base?: I): RewardMessage {
    return RewardMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RewardMessage>, I>>(object: I): RewardMessage {
    const message = createBaseRewardMessage();
    message.transformList = object.transformList?.map((e) => e) || [];
    message.rewardList = object.rewardList?.map((e) => e) || [];
    return message;
  },
};

function createBaseSystemOpenMessage(): SystemOpenMessage {
  return { newList: [], totalList: [] };
}

export const SystemOpenMessage: MessageFns<SystemOpenMessage> = {
  encode(message: SystemOpenMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.newList) {
      writer.int64(v);
    }
    writer.join();
    writer.uint32(18).fork();
    for (const v of message.totalList) {
      writer.int64(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SystemOpenMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSystemOpenMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 8) {
            message.newList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.newList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
        case 2: {
          if (tag === 16) {
            message.totalList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.totalList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<SystemOpenMessage>, I>>(base?: I): SystemOpenMessage {
    return SystemOpenMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SystemOpenMessage>, I>>(object: I): SystemOpenMessage {
    const message = createBaseSystemOpenMessage();
    message.newList = object.newList?.map((e) => e) || [];
    message.totalList = object.totalList?.map((e) => e) || [];
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
