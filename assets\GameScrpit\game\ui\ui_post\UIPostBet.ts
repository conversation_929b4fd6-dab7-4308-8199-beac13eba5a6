import { _decorator, instantiate, Label, Node, Tween, tween, UITransform, v3, Vec3 } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import ToolExt from "../../common/ToolExt";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { PostModule } from "../../../module/post/postModule";
import { JsonMgr } from "../../mgr/JsonMgr";
import TipMgr from "../../../lib/tips/TipMgr";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import { ItemEnum } from "../../../lib/common/ItemEnum";
import { tweenTagEnum } from "../../GameDefine";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { PostAudioName } from "../../../module/post/postConfig";
import Formate from "../../../lib/utils/Formate";

const { ccclass, property } = _decorator;

const item_1: number = 1;

@ccclass("UIPostBet")
export class UIPostBet extends UINode {
  protected _openAct: boolean = true; // 打开动作

  private _curItemNum = 0; // 当前投放次数
  private _costTimeMax: number = null; // 每次挂机气运消耗次数上限
  private _costList: number[] = []; // 气运投放数量

  private _labItemNumNode: Node;
  private _labNeedNumNode: Node;
  private _labAddCountNode: Node;
  private _dotEndLayerNode: Node;
  private _btnAddItemNode: Node;
  private _flyDotNode: Node;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_POST}?prefab/ui/UIPostBet`;
  }

  protected onEvtShow(): void {
    this._labItemNumNode = this.getNode("lab_item_num");
    this._labNeedNumNode = this.getNode("lab_need_num");
    this._labAddCountNode = this.getNode("lab_add_count");
    this._dotEndLayerNode = this.getNode("dot_end_layer");
    this._btnAddItemNode = this.getNode("btn_add_Item");
    this._flyDotNode = this.getNode("fly_dot");

    this.getParam();
    this.set_item_1_icon();
    this.updateUI();
  }

  private getParam() {
    const level = PostModule.data.level;
    const db = JsonMgr.instance.jsonList.c_post[level];

    this._costTimeMax = db.costTimeMax;
    this._costList = db.costList;
  }

  private set_item_1_icon() {
    ToolExt.setItemIcon(this.getNode("item_1"), item_1, this);
  }

  private updateUI() {
    this.set_lab_item_num();
    this.set_lab_need_num();
    this.set_lab_add_count();
  }

  private set_lab_item_num() {
    this._labItemNumNode.getComponent(Label).string = `x${this._curItemNum}`;
  }

  private set_lab_need_num() {
    this._labNeedNumNode.getComponent(Label).string = `x${Formate.format(this.getNeedNum())}`;
  }

  private set_lab_add_count() {
    if (this._costTimeMax == 0) {
      this._labAddCountNode.getComponent(Label).string = "当前等级可无限捐献";
      return;
    }

    this._labAddCountNode.getComponent(Label).string = `当前等级可捐献次数：${this._curItemNum}/${this._costTimeMax}`;
  }

  private getNeedNum() {
    const newIndex = this._curItemNum + PostModule.data.message.energyDropCount;
    const num =
      newIndex >= this._costList.length ? this._costList[this._costList.length - 1] : this._costList[newIndex];
    return num;
  }

  private on_click_btn_add_Item() {
    AudioMgr.instance.playEffect(PostAudioName.Effect.点击捐献按钮);
    if (this._costTimeMax > 0 && this._curItemNum >= this._costTimeMax) {
      TipMgr.showTip("达到本次上限");
      return;
    }

    const num = this.getNeedNum() + this.getNeedAll();

    if (num > PlayerModule.data.getItemNum(ItemEnum.气运_1)) {
      UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
        itemId: ItemEnum.气运_1,
        needNum: num,
      });
      return;
    }

    this._curItemNum++;
    this.flyDot();
    this.updateUI();
  }

  private flyDot() {
    const children = this._dotEndLayerNode.children;
    const availableNodes = children.filter((node) => node.children.length === 0);

    if (availableNodes.length === 0) {
      return;
    }

    const randomIndex = Math.floor(Math.random() * availableNodes.length);
    const endNode = availableNodes[randomIndex];

    this.flyEnergyFun(ToolExt.getWorldPos(this._btnAddItemNode), endNode, this._flyDotNode, endNode);
  }

  /**飞行气运 */
  private flyEnergyFun(startWorldPos: Vec3, experienceTweenLayer: Node, expNode: Node, end: Node) {
    const startPos = experienceTweenLayer.getComponent(UITransform).convertToNodeSpaceAR(startWorldPos);
    const endPos = ToolExt.transferOfAxes(end, experienceTweenLayer);
    const nCount = 1;

    for (let i = 0; i < nCount; i++) {
      const node = instantiate(expNode);
      end.addChild(node);
      node.setPosition(startPos);
      node.active = true;

      const midPos = ToolExt.createRandomPos(startPos, {
        area: new Vec3(600, 300, 0),
        space: new Vec3(10, 0, 0),
        offset: new Vec3(0, -0.0, 0),
      });

      const midPos2 = ToolExt.createRandomPos(startPos, {
        area: new Vec3(400, 300, 0),
        space: new Vec3(0, 0, 0),
        offset: new Vec3(0, 1, 0),
      });

      const tempVec3 = v3();

      tween(node)
        .tag(tweenTagEnum.UIPostBet_Tag)
        .to(
          1,
          { position: endPos },
          {
            onUpdate: (target, ratio) => {
              ToolExt.bezierCurve(ratio, startPos, midPos, midPos2, endPos, tempVec3);
              node.setPosition(tempVec3);
            },
          }
        )
        .delay(0.1)
        .call(() => {
          tween(node)
            .by(0.6, { position: v3(0, 20, 0) })
            .by(0.6, { position: v3(0, -20, 0) })
            .union()
            .repeatForever()
            .start();
        })
        .start();
    }
  }

  private getNeedAll() {
    let num = 0;
    for (
      let i = PostModule.data.message.energyDropCount;
      i < this._curItemNum + PostModule.data.message.energyDropCount;
      i++
    ) {
      const index = i >= this._costList.length ? this._costList.length - 1 : i;
      num += this._costList[index];
    }
    return num;
  }

  private on_click_btn_close() {
    // 关闭窗口 effect_guanbiyinxiao
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  private on_click_btn_go() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    if (PostModule.data.dailyMaxCount > 0 && PostModule.data.dailyCount >= PostModule.data.dailyMaxCount) {
      TipMgr.showTip("今日已到达上限");
      return;
    }

    if (this._curItemNum < 1) {
      TipMgr.showTip("先添加投放次数");
      return;
    }

    PostModule.api.deliver(this._curItemNum, () => {
      UIMgr.instance.back();
    });
  }

  protected onEvtClose(): void {
    Tween.stopAllByTag(tweenTagEnum.UIPostBet_Tag);
  }
}
