import { instantiate, Node, _decorator, Label } from "cc";
import { ListAdapter, ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import ToolExt from "../../../common/ToolExt";
import FmUtils from "db://assets/GameScrpit/lib/utils/FmUtils";
import { AfterModule } from "db://assets/GameScrpit/module/after/AfterModule";
import MsgMgr from "db://assets/GameScrpit/lib/event/MsgMgr";
import { UIMgr } from "db://assets/GameScrpit/lib/ui/UIMgr";
import { avId1 } from "db://assets/GameScrpit/module/day/DayActivityModule";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import MsgEnum from "../../../event/MsgEnum";
import { RedeemChosenRequest, RedeemRequest, RedeemResponse } from "../../../net/protocol/Activity";
import { PlayerModule } from "db://assets/GameScrpit/module/player/PlayerModule";
import { GoodsRouteName } from "db://assets/GameScrpit/module/goods/GoodsRoute";
import GameHttpApi from "../../../httpNet/GameHttpApi";
import { RouteManager } from "db://assets/platform/src/core/managers/RouteManager";
import { CommIntegerListMessage } from "../../../net/protocol/Comm";
import { UIOptionalItem } from "../UIOptionalItem";
import { ActivityID } from "db://assets/GameScrpit/module/activity/ActivityConstant";
import { ActivityModule } from "db://assets/GameScrpit/module/activity/ActivityModule";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { TopFingerControl } from "db://assets/GameScrpit/ext_guide/TopFingerControl";
import { RedeemPackVO } from "db://assets/GameScrpit/module/activity/ActivityConfig";
const { ccclass, property } = _decorator;

//累计充值
@ccclass("TopUpGiftViewHolder")
export class TopUpGiftViewHolder extends ViewHolder {
  private _data: RedeemPackVO;
  public updateData(data: RedeemPackVO) {
    this._data = data;
    let rewardList = ToolExt.traAwardItemMapList(data.rewardList);
    this.getNode("item_content").children.forEach((item) => {
      item.active = false;
    });
    for (let i = 0; i < rewardList.length; i++) {
      let item = this.getNode("item_content").children[i];
      if (!item) {
        item = instantiate(this.getNode("item_content").children[0]);
        item.parent = this.getNode("item_content");
      }
      item.active = true;
      FmUtils.setItemNode(item.getChildByName("Item"), rewardList[i].id, rewardList[i].num);
      item.getChildByName("btn_add").active = false;
      item.getChildByName("icon_add").active = false;
      item.getChildByName("Item").active = true;
    }

    // 自选礼包===START
    let item = this.getNode("item_content").children[rewardList.length];
    if (!item) {
      item = instantiate(this.getNode("item_content").children[0]);
      item.parent = this.getNode("item_content");
    }
    item.active = true;
    item.getChildByName("btn_add").active = true;
    if (AfterModule.data.topUpMessage.chosenMap[data.id]) {
      let itemId = AfterModule.data.topUpMessage.chosenMap[data.id];
      item.getChildByName("icon_add").active = false;
      let list = ToolExt.traAwardItemMapList(data.chosenList[0]);
      FmUtils.setItemNode(item.getChildByName("Item"), list[itemId.intList[0]].id, list[itemId.intList[0]].num);
      item.getChildByName("Item").active = true;
    } else {
      item.getChildByName("icon_add").active = true;
      item.getChildByName("Item").active = false;
    }
    // 自选礼包===END

    /**次数判断，是否显示购买按钮售空状态 */
    let redem_num = AfterModule.data.redeemMap[data.id] || 0;
    let show_bool = redem_num >= data.max ? true : false; //true 是代表已经售空

    this.getNode("btn_yigoumai").active = show_bool;
    this.getNode("btn_type3_goumai").active = !show_bool;
    this.getNode("lbl_buy_max").active = !show_bool;
    if (show_bool == false) {
      this.getNode("lbl_buy_max").getComponent(Label).string =
        ToolExt.getMaxtypeLab(data.maxtype) + `(${data.max - redem_num}/${data.max})`;
    }

    let lbl_day_item_name: Node = this.getNode("lbl_day_item_name");
    lbl_day_item_name.getComponent(Label).string = data.name || "";

    /**代表是免费礼包 ， 不要钱免费送 */
    if (data.adNum == 0 && data.price == 0) {
      this.getNode("lbl_money").getComponent(Label).string = "免费";
    } else if (data.price > 0) {
      /**收费礼包，要人民币 */
      this.getNode("lbl_money").getComponent(Label).string = (data.price % 10000) + "元";
    }
  }
  private onClickAdd(event) {
    RouteManager.uiRouteCtrl.showRoute(UIOptionalItem, {
      payload: {
        chosenList: this._data.chosenList[0],
        activityId: ActivityID.AFTERID,
        redeemId: this._data.id,
      },
      onCloseBack: (args) => {
        if (!args || !args.activityId) {
          return;
        }
        let param: RedeemChosenRequest = args;
        ActivityModule.api.recordRedeemChosen(param, (res: CommIntegerListMessage) => {
          console.log("确认预选道具", res);
          this.updateData(this._data);
        });
      },
    });
  }
  private onClickBuy(event) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let info: RedeemPackVO = this._data;
    if (!AfterModule.data.topUpMessage.chosenMap[this._data.id]) {
      let index = this.getNode("item_content").children.length - 1;
      let clickNode = this.getNode("item_content").children[index];
      TipsMgr.topRouteCtrl.showRoute(TopFingerControl, { payload: { clickNode: clickNode } });
      return;
    }
    if (info.price == 0) {
      let param: RedeemRequest = {
        activityId: avId1,
        redeemId: info.id,
        count: 1,
      };

      AfterModule.api.buyFixedPack(param, (res: RedeemResponse) => {
        let rewardList = res.rewardList;
        MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: rewardList });
      });
      return;
    }

    let goodsId = Number(info.id);
    let goodsType = 10;
    let playerId = PlayerModule.data.playerId;
    let orderAmount = info.price % 10000;
    let goodsName = info.name || "";
    let platformType = "TEST";
    let params = AfterModule.data.topUpMessage.chosenMap[this._data.id].intList;
    this.onPay(goodsId, goodsType, playerId, orderAmount, goodsName, platformType, params);
  }
  private onPay(
    goodsId: number,
    goodsType: number,
    playerId: number,
    orderAmount: number,
    goodsName: string,
    platformType: string,
    params: number[]
  ) {
    GameHttpApi.pay({
      goodsId: goodsId,
      goodsType: goodsType,
      playerId: playerId,
      orderAmount: orderAmount,
      goodsName: goodsName,
      platformType: platformType,
      param: params,
    }).then((resp: any) => {
      // window.open(resp.data.url);
      if (!resp || resp.code != 200) {
        console.error(resp);
        return;
      }
      UIMgr.instance.showDialog(GoodsRouteName.UIPay, { url: resp.data.url });
    });
  }
}

export class TopUpGiftAdapter extends ListAdapter {
  private _type: number;
  private _item: Node;
  private _data: RedeemPackVO[] = [];
  constructor(item: Node) {
    super();
    this._item = item;
  }
  setData(data: RedeemPackVO[]) {
    this._data = data;
    this.notifyDataSetChanged();
  }
  onCreateView(viewType: number): Node {
    let item = instantiate(this._item);
    item.active = true;
    return item;
  }
  onBindData(node: Node, position: number): void {
    node.getComponent(TopUpGiftViewHolder).updateData(this._data[position]);
  }
  getCount(): number {
    return this._data.length;
  }
}
