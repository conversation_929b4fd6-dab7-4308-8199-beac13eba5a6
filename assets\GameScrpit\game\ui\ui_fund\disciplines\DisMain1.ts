import {
  _decorator,
  Animation,
  Color,
  Component,
  find,
  Input,
  instantiate,
  Label,
  Layout,
  Node,
  ScrollView,
  Sprite,
  SpriteFrame,
  Tween,
  tween,
  UIOpacity,
  UITransform,
  Vec2,
} from "cc";
import { DisciplinesModule } from "db://assets/GameScrpit/module/disciplines/DisciplinesModule";
import { Sleep } from "../../../GameDefine";
import { dtTime } from "../../../BoutStartUp";
import { AchieveRewardRequest, AchieveRewardResponse } from "../../../net/protocol/Activity";
import MsgMgr from "db://assets/GameScrpit/lib/event/MsgMgr";
import { AssetMgr, BundleEnum } from "db://assets/platform/src/ResHelper";
import MsgEnum from "../../../event/MsgEnum";
import { JsonMgr } from "../../../mgr/JsonMgr";
import { IConfigItem } from "../../../JsonDefine";
import { PlayerModule } from "db://assets/GameScrpit/module/player/PlayerModule";
import GameHttpApi from "../../../httpNet/GameHttpApi";
import { GoodsRouteName } from "db://assets/GameScrpit/module/goods/GoodsRoute";
import { UIMgr } from "db://assets/GameScrpit/lib/ui/UIMgr";
import { DisciplinesAudioName } from "db://assets/GameScrpit/module/disciplines/DisciplinesConfig";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "db://assets/GameScrpit/lib/utils/FmUtils";
import { AchieveVO, LeaderFundVO } from "db://assets/GameScrpit/module/activity/ActivityConfig";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("DisMain1")
export class DisMain1 extends Component {
  private assetMgr: AssetMgr = null;

  private _achieveVO: AchieveVO = null;
  private _achievementId1: number = null;
  private _disciplinesdb: LeaderFundVO;

  @property(Node)
  fund_item: Node = null;

  @property(Node)
  content_list: Node = null;

  @property(Node)
  pengz: Node = null;

  private _btn_main1_goumai: Node = null;
  private _btn_yigoumai1: Node = null;
  private _lbl_main1_money: Node = null;
  private _scroll_list: Node = null;
  private _content_list: Node = null;

  protected onEnable(): void {}

  protected onDisable(): void {}

  protected onLoad(): void {
    MsgMgr.on(MsgEnum.ON_ACTIVITY_FUND_BUT_UP, this.initMain1, this);
    this.assetMgr = AssetMgr.create();
    this._btn_main1_goumai = find("btn_main1_goumai", this.node);
    this._btn_yigoumai1 = find("bg_xiuxing/btn_yigoumai1", this.node);
    this._lbl_main1_money = find("lbl_main1_money", this._btn_main1_goumai);
    this._scroll_list = find("scroll_list", this.node);
    this._content_list = find("view_list/content_list", this._scroll_list);
  }

  start() {
    this.initMain1();
  }

  private async initMain1() {
    this._disciplinesdb = await DisciplinesModule.data.getdb();
    this._achieveVO = await DisciplinesModule.data.getDisFundOV();
    this._achievementId1 = this._achieveVO.id;
    let AchieveData = DisciplinesModule.data.getAchieveData(this._achievementId1);
    this._btn_main1_goumai.active = !AchieveData.paid;
    this._btn_yigoumai1.active = AchieveData.paid;
    this._lbl_main1_money.getComponent(Label).string = (this._achieveVO.price % 10000) + "元";
    this.loadListMain1(this._achieveVO);
  }

  private async loadListMain1(db) {
    let basicRewardList = db.basicRewardList;
    let paidRewardList = db.paidRewardList;
    let requireList = db.requireList;
    let num = this._content_list.children.length - basicRewardList.length;
    if (num > 0) {
      for (let j = 0; j < num; j++) {
        let node = this._content_list.children[0];
        node.removeFromParent();
        node.destroy();
      }
    }
    for (let i = 0; i < basicRewardList.length; i++) {
      await Sleep(dtTime);

      if (this.isValid == false) {
        return;
      }

      let node = this._content_list.children[i];
      if (!node) {
        node = instantiate(this.fund_item);
        this._content_list.addChild(node);

        this.on_Touch_get_award(node);
        node.active = true;
      }
      node["itemIndex"] = i;
      let basic = basicRewardList[i];
      let paid = paidRewardList[i] || [];
      let require = requireList[i] || 0;
      let list = [
        node.getChildByName("basics_Item1"),
        node.getChildByName("pay_Item1"),
        node.getChildByName("pay_Item2"),
      ];
      list.forEach((val) => {
        this.hide_node_mask(val);
        val.getChildByName("btn_get_award")["itemIndex"] = i;
      });
      this.set_state(node.getChildByName("state_has"), require);
      this.set_bar_yellow(node, require);
      this.set_require(node.getChildByName("state_has").getChildByName("no_has"), require, db.otherParam[0] || 1);
      this.set_require(node.getChildByName("state_has").getChildByName("has"), require, db.otherParam[0] || 1);
      this.set_basics_Item1(node.getChildByName("basics_Item1"), basic[0], basic[1], require, i);
      this.set_pay_Item1(node.getChildByName("pay_Item1"), paid[0], paid[1], require, i);
      this.set_pay_Item2(node.getChildByName("pay_Item2"), paid[2], paid[3], require, i);
      this.check_is_get(node, i);
    }
    let AchieveData = DisciplinesModule.data.getAchieveData(this._achievementId1);
    let basicTakeList = AchieveData.basicTakeList;
    let index = basicTakeList[basicTakeList.length - 1];
    if (AchieveData.paid == true) {
      let paidTakeList = AchieveData.paidTakeList;
      let paidInddex = paidTakeList[paidTakeList.length - 1];
      if (index > paidInddex) {
        index = paidInddex;
      }
    }
    tween(this.node)
      .delay(0.16)
      .call(() => {
        let spacingY = this._content_list.getComponent(Layout).spacingY;
        let height = this.fund_item.getComponent(UITransform).height;
        let y = (height + spacingY) * index;
        this._scroll_list.getComponent(ScrollView).scrollToOffset(new Vec2(0, y), 0.35);
      })
      .start();
  }

  private on_Touch_get_award(node: Node) {
    find("basics_Item1/btn_get_award", node).on(Input.EventType.TOUCH_END, this.click_get_award, this);
    find("pay_Item1/btn_get_award", node).on(Input.EventType.TOUCH_END, this.click_get_award, this);
    find("pay_Item2/btn_get_award", node).on(Input.EventType.TOUCH_END, this.click_get_award, this);
  }

  private hide_node_mask(node: Node) {
    node.getChildByName("spr_gou").active = false;
    node.getChildByName("spr_suo").active = false;
    node.getChildByName("btn_get_award").active = false;
  }

  private set_state(node: Node, require: number) {
    let AchieveData = DisciplinesModule.data.getAchieveData(this._achievementId1);
    let my = AchieveData.targetVal;
    let bool = false;
    if (my >= require) bool = true;
    node.getChildByName("no_has").active = !bool;
    node.getChildByName("has").active = bool;
  }

  private set_bar_yellow(node: Node, require: number) {
    let AchieveData = DisciplinesModule.data.getAchieveData(this._achievementId1);
    let my = AchieveData.targetVal;
    if (my > require) {
      node.getChildByName("bar_yellow1").active = true;
      node.getChildByName("bar_yellow2").active = false;
      return;
    }
    if (my >= require) {
      node.getChildByName("bar_yellow1").active = false;
      node.getChildByName("bar_yellow2").active = true;
      return;
    }
    node.getChildByName("bar_yellow1").active = false;
    node.getChildByName("bar_yellow2").active = false;
  }

  private click_get_award(event) {
    AudioMgr.instance.playEffect(DisciplinesAudioName.Effect.点击道具图标);
    let fundRequest_data: AchieveRewardRequest = {
      /** 基金的ID */
      activityId: this._disciplinesdb.id,
      /** 成就ID */
      achieveId: this._achieveVO.id,
      /** 领取的索引处对应的奖励 从0开始 */
      index: event.target.itemIndex,
      takeAll: false,
    };
    DisciplinesModule.api.takeFundReward(fundRequest_data, (res: AchieveRewardResponse) => {
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: res.rewardList });
      this._content_list.children.forEach((val) => {
        this.check_is_get(val, val["itemIndex"]);
      });
    });
  }

  private set_require(node: Node, require: number, itemId: number) {
    let lab_require = find("lay/lab_require", node);
    lab_require.getComponent(Label).string = String(require);
    let item = find("lay/item", node);

    let cfg: IConfigItem = JsonMgr.instance.getConfigItem(itemId);
    this.assetMgr.loadSpriteFrame(BundleEnum.BUNDLE_COMMON_ITEM, `autoItem/${cfg.iconId}`, (sp: SpriteFrame) => {
      if (this.isValid == false) {
        return;
      }
      item.getComponent(Sprite).spriteFrame = sp;
    });
  }

  private set_basics_Item1(item: Node, id: number, num: number, require: number, itemIndex: number) {
    FmUtils.setItemNode(item, id, num);
    this.set_is_gray(item, require);
    let AchieveData = DisciplinesModule.data.getAchieveData(this._achievementId1);
    let my = AchieveData.targetVal;
    if (my >= require) {
      let bool = AchieveData.basicTakeList.indexOf(itemIndex) == -1 ? true : false;
      this.award_snake(item, bool);
      item.getChildByName("btn_get_award").active = true;
    }
  }

  private set_is_gray(node: Node, require: number) {
    let AchieveData = DisciplinesModule.data.getAchieveData(this._achievementId1);
    let my = AchieveData.targetVal;
    let bool = false;
    if (my >= require) bool = true;
    let color = new Color(100, 100, 100, 255);
    if (bool == true) {
      color = new Color(255, 255, 255, 255);
    }
    node.getComponentsInChildren(Sprite).forEach((sprite) => {
      sprite.color = color;
    });
    node.getComponentsInChildren(Label).forEach((label) => {
      label.color = color;
    });
    if (bool == false) {
      node.getChildByName("btn_get_award").active = false;
    }
  }

  private award_snake(targetNode: Node, is: boolean) {
    // 获取目标节点的初始位置
    const nodeStartPos = targetNode.getPosition();
    if (!targetNode["nodeStartPos"]) {
      targetNode["nodeStartPos"] = nodeStartPos;
    }
    let ani = targetNode.getComponent(Animation);
    if (is == false) {
      ani.stop();
      targetNode.setRotation(0, 0, 0, 0);
      return;
    }
    targetNode.setRotation(0, 0, 0, 0);
    ani.play("ani_dou");
  }

  private check_is_get(node: Node, index: number) {
    let AchieveData = DisciplinesModule.data.getAchieveData(this._achievementId1);
    let is1 = AchieveData.basicTakeList.indexOf(index);
    let is2 = AchieveData.paidTakeList.indexOf(index);
    if (is1 != -1) {
      node.getChildByName("basics_Item1").getChildByName("spr_gou").active = true;
      node.getChildByName("basics_Item1").getChildByName("spr_suo").active = false;
      node.getChildByName("basics_Item1").getChildByName("btn_get_award").active = false;
      this.award_snake(node.getChildByName("basics_Item1"), false);
    }
    if (is2 != -1) {
      node.getChildByName("pay_Item1").getChildByName("spr_gou").active = true;
      node.getChildByName("pay_Item1").getChildByName("btn_get_award").active = false;
      this.award_snake(node.getChildByName("pay_Item1"), false);
      node.getChildByName("pay_Item2").getChildByName("spr_gou").active = true;
      node.getChildByName("pay_Item2").getChildByName("btn_get_award").active = false;
      this.award_snake(node.getChildByName("pay_Item2"), false);
    }
  }

  private set_pay_Item1(item: Node, id: number, num: number, require: number, itemIndex: number) {
    FmUtils.setItemNode(item, id, num);
    this.set_is_gray(item, require);
    let AchieveData = DisciplinesModule.data.getAchieveData(this._achievementId1);
    let my = AchieveData.targetVal;
    if (my >= require && AchieveData.paid == true) {
      let bool = AchieveData.paidTakeList.indexOf(itemIndex) == -1 ? true : false;
      this.award_snake(item, bool);
      item.getChildByName("btn_get_award").active = AchieveData.paid;
    }
    this.set_pay_suo(item);
  }

  private set_pay_Item2(item: Node, id: number, num: number, require: number, itemIndex: number) {
    FmUtils.setItemNode(item, id, num);
    this.set_is_gray(item, require);
    let AchieveData = DisciplinesModule.data.getAchieveData(this._achievementId1);
    let my = AchieveData.targetVal;
    if (my >= require && AchieveData.paid == true) {
      let bool = AchieveData.paidTakeList.indexOf(itemIndex) == -1 ? true : false;
      this.award_snake(item, bool);
      item.getChildByName("btn_get_award").active = AchieveData.paid;
    }
    this.set_pay_suo(item);
  }

  private set_pay_suo(node: Node) {
    let AchieveData = DisciplinesModule.data.getAchieveData(this._achievementId1);
    node.getChildByName("spr_suo").active = !AchieveData.paid;
  }

  private on_click_btn_main1_goumai() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let data = {
      goodsId: this._achieveVO.id,
      goodsType: this._disciplinesdb.buyType,
      playerId: PlayerModule.data.playerId,
      orderAmount: (this._achieveVO.price % 10000) % 10000,
      goodsName: this._disciplinesdb.name,
      platformType: "TEST",
    };

    GameHttpApi.pay(data).then((resp: any) => {
      if (resp.code != 200) {
        let err = JSON.parse(resp.msg);
        log.log(err);
        return;
      }
      UIMgr.instance.showDialog(GoodsRouteName.UIPay, { url: resp.data.url });
    });
  }

  private _tickIndex: number = 0;
  update(deltaTime: number) {
    let pengz = this.pengz;

    let content_list = this.content_list;

    const Box1 = pengz.getComponent(UITransform).getBoundingBoxToWorld();

    const index = this._tickIndex;
    for (let i = index; i < index + 5 && i < content_list.children.length; i++) {
      const Box2 = content_list.children[i].getComponent(UITransform).getBoundingBoxToWorld();

      if (Box1.intersects(Box2)) {
        content_list.children[i].walk((val) => {
          if (val.getComponent(Label)) {
            val.getComponent(Label).enabled = true;
          }
          if (val.getComponent(Sprite)) {
            val.getComponent(Sprite).enabled = true;
          }
        });
      } else {
        content_list.children[i].walk((val) => {
          if (val.getComponent(Label)) {
            val.getComponent(Label).enabled = false;
          }
          if (val.getComponent(Sprite)) {
            val.getComponent(Sprite).enabled = false;
          }
        });
      }
      this._tickIndex = i;
    }

    if (this._tickIndex >= content_list.children.length - 1) {
      this._tickIndex = 0;
    }
  }
  protected onDestroy(): void {
    Tween.stopAllByTarget(this.node);
    MsgMgr.off(MsgEnum.ON_ACTIVITY_FUND_BUT_UP, this.initMain1, this);
  }
}
