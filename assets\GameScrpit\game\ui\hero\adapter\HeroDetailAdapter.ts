import { _decorator, instantiate, Node } from "cc";
import { ListAdapter } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { HeroSkillViewHolder } from "./HeroSkillViewHolder";
import { FriendViewHolder } from "./FriendViewHolder";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

export class HeroSkillAdapter extends ListAdapter {
  private item1: Node;
  private expand1: Node;
  private item2: Node;
  private expand2: Node;
  private heroId: number;
  private viewType: number = 1;
  private heroDatas: number[];
  private friendDatas: number[];

  public constructor(item1: Node, expand1: Node, item2: Node, expand2: Node) {
    super();
    this.item1 = item1;
    this.expand1 = expand1;
    this.item2 = item2;
    this.expand2 = expand2;
  }
  public setHeroData(data: any[], heroId: number) {
    this.heroDatas = [];
    this.notifyDataSetChanged();
    this.viewType = 1;
    this.heroId = heroId;
    this.heroDatas = data;
    this.notifyDataSetChanged();
    // log.error("setHeroData");
  }
  public setFriendData(data: any[], heroId: number) {
    this.friendDatas = [];
    this.notifyDataSetChanged();
    this.viewType = 2;
    this.heroId = heroId;
    //
    this.friendDatas = data;
    this.notifyDataSetChanged();
    // log.error("setFriendData");
  }
  public setDataOnly(data?: any[]) {
    // if (this.viewType == 1) {
    //   this.heroDatas = data;
    // }
    // log.error("setDataOnly");
    this.notifyDataSetChanged(true);
  }
  getViewType(position: number): number {
    return this.viewType;
  }
  onCreateView(viewType: number): Node {
    if (this.viewType == 1) {
      if (viewType == -1) {
        let itemExpand = instantiate(this.expand1);
        // log.log(itemExpand);
        return itemExpand;
      }
      let item = instantiate(this.item1);
      item.active = true;
      item.getComponent(HeroSkillViewHolder).init();
      return item;
    } else {
      if (viewType == -1) {
        let itemExpand = instantiate(this.expand2);
        // log.log(itemExpand);
        return itemExpand;
      }
      let item = instantiate(this.item2);
      item.active = true;
      // item.getComponent(HeroSkillViewHolder).init(this);
      return item;
    }
  }
  onBindData(view: Node, position: number): void {
    if (this.viewType == 1) {
      view.getComponent(HeroSkillViewHolder).updateData(this.heroId, this.heroDatas[position]);
    } else {
      view.getComponent(FriendViewHolder).updateData(this.heroId, this.friendDatas[position]);
    }
  }
  getCount(): number {
    if (this.viewType == 1) {
      return this.heroDatas.length;
    }
    return this.friendDatas.length;
  }
}
