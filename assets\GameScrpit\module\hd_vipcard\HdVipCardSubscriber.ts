import MsgEnum from "../../game/event/MsgEnum";
import { <PERSON><PERSON><PERSON>and<PERSON> } from "../../game/mgr/ApiHandler";
import { ActivityCmd } from "../../game/net/cmd/CmdData";
import { LifeCardBuyResponse, MonthCardBuyResponse } from "../../game/net/protocol/Activity";
import MsgMgr from "../../lib/event/MsgMgr";
import { HdVipCardModule } from "./HdVipCardModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class HdVipCardSubscriber {
  private monthCardBuyNotice(data: MonthCardBuyResponse) {
    log.log(data);
    HdVipCardModule.data.vipCardMessage = data.vipCardMessage;
    MsgMgr.emit(MsgEnum.ON_ACTIVITY_MONTH_CARD_RECHARGE, data);
    MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardList });
  }
  private lifeCardBuyNotice(data: LifeCardBuyResponse) {
    HdVipCardModule.data.vipCardMessage = data.vipCardMessage;
    MsgMgr.emit(MsgEnum.ON_ACTIVITY_LIFE_CARD_RECHARGE, data);
    MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardList });
    log.log(data);
  }
  public register() {
    //订阅服务器消息
    ApiHandler.instance.subscribe(MonthCardBuyResponse, ActivityCmd.monthCardBuyNotice, this.monthCardBuyNotice);
    ApiHandler.instance.subscribe(LifeCardBuyResponse, ActivityCmd.lifeCardBuyNotice, this.lifeCardBuyNotice);
  }
  public unRegister() {
    //取消订阅服务器消息
    ApiHandler.instance.unSubscribe(ActivityCmd.monthCardBuyNotice, this.monthCardBuyNotice);
    ApiHandler.instance.unSubscribe(ActivityCmd.lifeCardBuyNotice, this.lifeCardBuyNotice);
  }
}
