import { _decorator, Component, instantiate, Node } from "cc";
import { ListAdapter } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { DisTaskViewHolder } from "./DisTaskViewHolder";

const { ccclass, property } = _decorator;

@ccclass("DisTaskAdapter")
export class DisTaskAdapter extends ListAdapter {
  private data: any[] = [];

  private item: Node;
  public constructor(item: Node) {
    super();
    this.item = item;
  }
  public setData(data: any[]) {
    this.data = data;
    this.notifyDataSetChanged();
  }

  onCreateView(viewType: number): Node {
    let item = instantiate(this.item);
    item.active = true;
    item.getComponent(DisTaskViewHolder).init();
    return item;
  }

  onBindData(view: Node, position: number): void {
    view.getComponent(DisTaskViewHolder).updateData(this.data[position], position);
  }

  getCount(): number {
    return this.data.length;
  }
}
