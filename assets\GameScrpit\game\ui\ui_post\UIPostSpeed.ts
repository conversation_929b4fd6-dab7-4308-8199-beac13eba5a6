import { _decorator, Component, Label, Node } from "cc";
import MsgMgr from "../../../lib/event/MsgMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import MsgEnum from "../../event/MsgEnum";
import { PostModule } from "../../../module/post/postModule";
import { DialogZero } from "../../GameDefine";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";

const { ccclass, property } = _decorator;

@ccclass("UIPostSpeed")
export class UIPostSpeed extends UINode {
  protected _openAct: boolean = true; //打开动作
  public zOrder(): number {
    return DialogZero.UIPostSpeed;
  }

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_POST}?prefab/ui/UIPostSpeed`;
  }

  private _num: number = 0;

  protected onRegEvent(): void {
    MsgMgr.on(MsgEnum.ON_POST_SPEED_COST, this.onPostSpeedCost, this);
  }
  protected onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_POST_SPEED_COST, this.onPostSpeedCost, this);
  }

  public init(args: any): void {
    super.init(args);
    this._num = args.num;
  }

  protected onEvtShow(): void {
    this.onPostSpeedCost(null);
  }

  private onPostSpeedCost(num: number) {
    if (this._num == num) {
      return;
    }
    if (num) {
      this._num = num;
    }
    this["lab2"].getComponent(Label).string = "x" + this._num + "进行加速";
  }

  private on_click_btn_yes() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    PostModule.api.speed(() => {
      UIMgr.instance.back();
    });
  }

  private on_click_btn_no() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    UIMgr.instance.back();
  }

  private on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
}
