import { _decorator, Component, Node, SpriteFrame, UIOpacity, UITransform } from "cc";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
import { HeroModule } from "../module/hero/HeroModule";
import { Sprite } from "cc";
import { Prefab, instantiate, sp, Label } from "cc";
import { BundleEnum } from "../game/bundleEnum/BundleEnum";
import ResMgr from "../lib/common/ResMgr";
import { tween } from "cc";
import { AudioItem, AudioMgr, AudioName } from "../../platform/src/AudioHelper";
import { IConfigHero } from "../game/JsonDefine";
import { JsonMgr } from "../game/mgr/JsonMgr";
import { FriendModule } from "../module/friend/FriendModule";
import { TipsMgr } from "../../platform/src/TipsHelper";
const { ccclass, property } = _decorator;

@ccclass("TopHeroAwardPop")
export class TopHeroAwardPop extends BaseCtrl {
  private _heroInfo: IConfigHero;

  @property(Node)
  heroRace: Node;

  @property(Node)
  nodeHeroImg: Node;

  @property(Node)
  txtHeroName: Node;

  @property(Node)
  heroColor: Node;

  @property(Node)
  nodeHeroGuadian: Node;
  @property(Node)
  nodeHeroGuadian2: Node;
  @property(Node)
  nodeTitle: Node;
  @property(Node)
  nodeFateLayout: Node;

  private _audioItem: AudioItem;

  private isAniFinish = false;

  init(args: any) {
    super.init(args);

    TipsMgr.setEnableTouch(false, 1);
    let heroId = args;
    this._heroInfo = HeroModule.config.getHeroInfo(heroId);
  }

  private playVoice() {
    if (this._audioItem) {
      this._audioItem.release();
    }
    if (this._heroInfo.voice && this._heroInfo.voice.length > 0) {
      this._audioItem = AudioMgr.instance.playVoice(this._heroInfo.voice[0]);
    }
  }

  start() {
    TipsMgr.setEnableTouch(true, 1);
    super.start();
    this.refreshUI(this._heroInfo);

    tween(this.node)
      .delay(0.5)
      .call(() => {
        this.isAniFinish = true;
      })
      .start();

    AudioMgr.instance.playEffect(AudioName.Effect.获得奖励);
    this.playVoice();

    // 动画效果
    this.nodeHeroGuadian2.on(Node.EventType.TRANSFORM_CHANGED, this.nodeHeroGuadian2Transform, this);
    this.node
      .getChildByName("node_dialog")
      .getComponent(sp.Skeleton)
      .setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
        if (trackEntry.animation.name == "animation") {
          this.nodeHeroGuadian2.off(Node.EventType.TRANSFORM_CHANGED, this.nodeHeroGuadian2Transform, this);
          tween(this.node.getChildByName("btn_close_huang").getComponent(UIOpacity)).to(0.5, { opacity: 255 }).start();
          this.node.getChildByName("node_dialog").getComponent(sp.Skeleton).setAnimation(0, "animation2", true);
        }
      });
    this.nodeTitle.on(Node.EventType.TRANSFORM_CHANGED, () => {
      let opacity = ((0.69 - (this.nodeTitle.scale.x - 1)) / 0.69) * 255;
      this.nodeTitle.getComponent(UIOpacity).opacity = opacity;
    });
    this.node
      .getChildByName("node_dialog")
      .getComponent(sp.Skeleton)
      .setEventListener((animation, event) => {
        if (event["data"].name == "pinzhichuxian") {
          tween(this.heroColor.getComponent(UIOpacity)).to(0.5, { opacity: 255 }).start();
        }
        if (event["data"].name == "jinengchuxian") {
          for (let i = 0; i < this.nodeFateLayout.children.length; i++) {
            let child = this.nodeFateLayout.children[i];
            if (child.active) {
              tween(child.getComponent(UIOpacity))
                .delay(0.1 * i)
                .to(0.5, { opacity: 255 })
                .start();
            }
          }
        }
      });
  }

  private nodeHeroGuadian2Transform() {
    let height = this.nodeHeroGuadian.position.y - this.nodeHeroGuadian2.position.y;
    let contentSize = this.nodeHeroGuadian.getComponent(UITransform).contentSize;
    this.nodeHeroGuadian.getComponent(UITransform).setContentSize(contentSize.width, height);
  }

  private refreshUI(heroInfo: IConfigHero) {
    this.txtHeroName.getComponent(Label).string = `${heroInfo.name}`;

    HeroModule.service.updateHeroRaceIcon(this.heroRace.getComponent(Sprite), heroInfo.id);
    HeroModule.service.updateHeroColorBg(this.heroColor.getComponent(Sprite), heroInfo.id);
    this.assetMgr.loadPrefab(BundleEnum.BUNDLE_COMMON_HERO_FULL, `prefabs/hero_${heroInfo.id}`, (prefab: Prefab) => {
      this.nodeHeroImg.removeAllChildren();
      let node = instantiate(prefab);
      this.nodeHeroImg.addChild(node);
      node.walk((child) => (child.layer = this.node.layer));
      this.nodeHeroImg.getComponentInChildren(sp.Skeleton).setAnimation(0, "animation1", true);
    });

    // 加载缘分
    let i = 0;
    let fateList = FriendModule.data.getHeroFriends(heroInfo.id);
    for (; i < fateList.length; i++) {
      const fateId = fateList[i];
      if (!this.nodeFateLayout.children[i]) {
        this.nodeFateLayout.addChild(instantiate(this.nodeFateLayout.children[0]));
      }
      let fateNode = this.nodeFateLayout.children[i];
      fateNode.active = true;
      this.assetMgr.loadSpriteFrame(BundleEnum.BUNDLE_COMMON_ITEM, `autoItem/item_fr_${fateId}`, (sp: SpriteFrame) => {
        fateNode.getChildByPath("mask/bg_friend_head").getComponent(Sprite).spriteFrame = sp;
      });
      fateNode.getComponent(UIOpacity).opacity = 0;
      fateNode
        .getChildByName("lbl_name")
        .getComponent(Label).string = `${JsonMgr.instance.jsonList.c_friend[fateId].name}`;
    }
    for (; i < this.nodeFateLayout.children.length; i++) {
      this.nodeFateLayout.children[i].active = false;
    }
  }

  private onBtnClose() {
    if (!this.isAniFinish) {
      return;
    }

    this.closeBack();
  }
}
