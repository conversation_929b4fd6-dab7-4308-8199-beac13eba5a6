import { GameData } from "../../game/GameData";
import data from "../../lib/data/data";
import { ActivityModule } from "../activity/ActivityModule";
import { DayActivityApi } from "./DayActivityApi";
import { DayActivityConfig } from "./DayActivityConfig";
import { DayActivityData } from "./DayActivityData";
import { DayActivityRoute } from "./DayActivityRoute";
import { DayActivityService } from "./DayActivityService";
import { DayActivitySubscriber } from "./DayActivitySubscriber";
import { DayActivityViewModel } from "./DayActivityViewModel";
export const avId1: number = 10701;
export class DayActivityModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): DayActivityModule {
    if (!GameData.instance.DayActivityModule) {
      GameData.instance.DayActivityModule = new DayActivityModule();
    }
    return GameData.instance.DayActivityModule;
  }
  private _data = new DayActivityData();
  private _api = new DayActivityApi();
  private _config = new DayActivityConfig();
  private _viewModel = new DayActivityViewModel();
  private _service = new DayActivityService();
  private _route = new DayActivityRoute();
  private _subscriber = new DayActivitySubscriber();

  public static get data() {
    return this.instance._data;
  }

  public static get api() {
    return this.instance._api;
  }

  public static get config() {
    return this.instance._config;
  }

  public static get viewModel() {
    return this.instance._viewModel;
  }

  public static get service() {
    return this.instance._service;
  }

  public static get route() {
    return this.instance._route;
  }

  public init(data?: any) {
    if (this._subscriber) {
      this._subscriber.unRegister();
    }
    this._data = new DayActivityData();
    this._api = new DayActivityApi();
    this._config = new DayActivityConfig();
    this._viewModel = new DayActivityViewModel();
    this._service = new DayActivityService();
    this._route = new DayActivityRoute();
    this._subscriber = new DayActivitySubscriber();

    // 初始化模块
    this._subscriber.register();
    this._route.init();
    this._service.init();

    // if (ActivityModule.service.checkActivityUnlock(10701)) {
    // }
    DayActivityModule.api.fixedPack(10701);
  }

  protected saveKey(): string {
    return this.constructor.name;
  }
}
