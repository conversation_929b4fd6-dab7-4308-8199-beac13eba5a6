import { <PERSON><PERSON>oll<PERSON>, <PERSON>Collider2D, Component, Rect, _decorator } from "cc";
import { DIRECT } from "./DirectSection";
import GameObject from "./GameObject";
import { Section } from "./Section";

const { ccclass, property } = _decorator;

@ccclass("ColliderEventReceiver")
export class ColliderEventReceiver extends Component {
  public delegate: ColliderSection = null;
  onCollisionEnter(other, self) {
    if (this.delegate == null) {
      return;
    }
    this.delegate.onCollisionEnter(other, self);
  }
  onCollisionStay(other, self) {
    if (this.delegate == null) {
      return;
    }
    this.delegate.onCollisionStay(other, self);
  }
  onCollisionExit(other, self) {
    if (this.delegate == null) {
      return;
    }
    this.delegate.onCollisionExit(other, self);
  }
}

export default class ColliderSection extends Section {
  public collider: BoxCollider2D = null;
  public dict = {};
  public static sectionName(): string {
    return "ColliderSection";
  }
  public onInit(go: GameObject, args) {
    super.onInit(go, args);
    this.onInitCollider();
    this.ready();
  }

  public onStart(): void {
    this.onMsg("OnDirectChange", this.onDirectChange.bind(this));
  }

  onDirectChange(direct) {
    if (direct == DIRECT.LEFT) {
      this.collider.offset.x = -Math.abs(this.collider.offset.x);
    } else if (direct == DIRECT.RIGHT) {
      this.collider.offset.x = Math.abs(this.collider.offset.x);
    }
  }

  protected onInitCollider() {
    this.collider = this.getSub().addComponent(BoxCollider2D);
    this.getSub().addComponent(ColliderEventReceiver).delegate = this;
  }

  public setColliderRange(rect: Rect) {
    if (this.collider == null) {
      return;
    }
    this.collider.offset = rect.origin;
    this.collider.size = rect.size;
  }

  public getColliderRange(): Rect {
    if (this.collider == null) {
      return;
    }
    let x = this.collider.offset.x;
    let y = this.collider.offset.y;
    let width = this.collider.size.width;
    let height = this.collider.size.height;
    return new Rect(x, y, width, height);
  }

  public onCollisionEnter(other, self) {
    if (!this.dict[other.node.uuid]) {
      this.dict[other.node.uuid] = other.node;
    }
  }

  public onCollisionStay(other, self) {}

  public onCollisionExit(other, self) {
    if (this.dict[other.node.uuid]) {
      delete this.dict[other.node.uuid];
    }
  }

  public onRemove(): void {
    super.onRemove();
    this.offMsg("OnDirectChange", this.onDirectChange.bind(this));
    this.getSub().getComponent(BoxCollider2D).enabled = false;
  }
}
