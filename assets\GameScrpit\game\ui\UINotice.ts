import { _decorator, find, instantiate, Label, Layout, tween, v3 } from "cc";
import { UINode } from "../../lib/ui/UINode";
import { BundleEnum } from "../bundleEnum/BundleEnum";
import { UIMgr } from "../../lib/ui/UIMgr";
import { PlayerModule } from "../../module/player/PlayerModule";
import CenterHttpApi from "../httpNet/CenterHttpApi";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UINotice")
export class UINotice extends UINode {
  protected _openAct: boolean = true; //打开动作
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_COMMON_MAIN}?prefab/ui/UINotice`;
  }

  public init(args: any): void {
    super.init(args);
  }

  protected onEvtShow(): void {
    CenterHttpApi.announcement(PlayerModule.data.getPlayerInfo().serverId).then((resp: any) => {
      if (resp.code != 200) {
        log.error(resp);
        return;
      }
      this.loadNoticeList(resp.data);
      //log.log("公告请求的内容", resp);
    });
  }

  private loadNoticeList(data: any) {
    for (let i = 0; i < data.length; i++) {
      let node = instantiate(this.getNode("bg_gonggao"));

      this.getNode("content").addChild(node);
      node.getChildByName("lbl_title").getComponent(Label).string = data[i].title;
      let des = find("lay/des", node);
      des.getComponent(Label).string = data[i].content;
      node.active = true;

      /**强更刷新 */
      des.getComponent(Label).updateRenderData(true);
      this.getNode("content").getComponent(Layout).updateLayout(true);

      tween(node)
        .delay(0.1 * i)
        .by(0.1, { position: v3(-700, 0, 0) })
        .start();
    }
  }

  private on_click_btn_close_lan() {
    UIMgr.instance.back();
  }
}
