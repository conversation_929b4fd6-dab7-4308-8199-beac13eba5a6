import { _decorator, Component, instantiate, Label, Node, tween, v3 } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { ClubModule } from "../../../module/club/ClubModule";
import { ClubShopViewHolder } from "./adapter/ClubShopViewHolder";
import { PlayerModule } from "../../../module/player/PlayerModule";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { ItemEnum } from "../../../lib/common/ItemEnum";
import { GoodsModule } from "../../../module/goods/GoodsModule";
const { ccclass, property } = _decorator;

/**
 *
 * i<PERSON>_huang
 * Wed Aug 21 2024 20:21:34 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/club/UIClubShop.ts
 *
 */

@ccclass("UIClubShop")
export class UIClubShop extends UINode {
  protected _openAct: boolean = true; //打开动作
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_CLUB}?prefab/ui/UIClubShop`;
  }
  //=================================================
  public init(args: any): void {
    super.init(args);
  }
  private _index: number = 0;
  private _canUpdate: boolean = false;
  public tick(dt: any): void {
    super.tick(dt);
    let shopList = ClubModule.config.getClubShopList();
    if (this._index < shopList.length && this._canUpdate) {
      let item = instantiate(this.getNode("shop_viewholder"));
      this.getNode("shop_list_content").addChild(item);
      item.getComponent(ClubShopViewHolder).updateData(shopList[this._index]);
      tween(item)
        .set({ scale: v3(0.1, 0.1, 1) })
        .to(0.3, { scale: v3(1, 1, 1) }, { easing: "sineOut" })
        .start();
      this._index++;
    }
  }
  protected onEvtShow(): void {
    super.onEvtShow();
    let clubCoin = PlayerModule.data.getItemNum(ItemEnum.战盟贡献_1105);
    this.getNode("club_coin").getComponent(Label).string = `${clubCoin}`;
    MsgMgr.on(MsgEnum.ON_EXIST_ITEM_CHANGE, this.onItemChange, this);
    GoodsModule.api.buyInfo((data) => {
      this._canUpdate = true;
    });
  }
  protected onEvtHide(): void {
    super.onEvtHide();
    MsgMgr.off(MsgEnum.ON_EXIST_ITEM_CHANGE, this.onItemChange, this);
  }
  private onItemChange() {
    let clubCoin = PlayerModule.data.getItemNum(ItemEnum.战盟贡献_1105);
    this.getNode("club_coin").getComponent(Label).string = `${clubCoin}`;
  }
}
