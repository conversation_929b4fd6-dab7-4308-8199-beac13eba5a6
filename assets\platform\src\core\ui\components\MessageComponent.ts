import { _decorator, Component, Node, Label, CCString, CCInteger, instantiate } from "cc";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import { LangMgr } from "../../../../../GameScrpit/game/mgr/LangMgr";

const log = Logger.getLoger(LOG_LEVEL.STOP);
const { ccclass, property, executeInEditMode, requireComponent } = _decorator;
const Editor = window["Editor"];

@ccclass("MessageComponent")
@executeInEditMode
@requireComponent(Label)
export class MessageComponent extends Component {
  // 用于显示文本的Label组件
  private _label: Label;

  @property({ serializable: true })
  private _args: string[] = [];
  @property({ type: CCInteger, serializable: true })
  private _messageKey: number = 0;

  // 初始化时创建/获取Label组件
  protected onLoad() {
    this._label = this.node.getComponent(Label);
  }

  @property({ type: CCString, displayName: "参数", tooltip: "参数" })
  public set args(val: string[]) {
    this._args = val;
    this.updateMessage();
  }
  public get args() {
    return this._args;
  }

  @property({ type: CCInteger, displayName: "消息ID", tooltip: "消息ID", step: 1 })
  public set messageKey(val: number) {
    this._messageKey = val;
    this.updateMessage();
  }
  public get messageKey() {
    return this._messageKey;
  }

  public setMessageId(messageId: number, args: string[] = []) {
    this._messageKey = messageId;
    this._args = args;
    this.updateMessage();
  }
  /**
   *
   * @param args
   * @param filter 条件成立时使用args参数，否则使用defaultArgs参数
   * @param defaultArgs 数组长度需要与args一致
   */
  public setArgs(args: string[] = [], filter: (arg: string) => boolean = () => true, defaultArgs: string[] = args) {
    let realArgs: string[] = [];
    args.forEach((arg, index) => {
      if (filter(arg)) {
        realArgs.push(arg);
      } else {
        realArgs.push(defaultArgs[index]);
      }
    });
    this._args = realArgs;
    this.updateMessage();
  }

  onFocusInEditor(): void {
    this.updateMessage();
  }

  updateMessage() {
    if (this._messageKey === 0) {
      return;
    }

    if (typeof Editor !== "undefined" && Editor.Project) {
      const projectSourcePath = Editor.Project.path;
      const filePath = `${projectSourcePath}/assets/bundle_common_json/json/c_message.json`;
      const fs = require("fs");
      const path = require("path");

      if (fs.existsSync(filePath)) {
        fs.readFile(filePath, "utf8", (err, data) => {
          if (err) {
            log.error("读取文件时出错:", err);
            return;
          }
          try {
            const jsonData = JSON.parse(data);
            let text = jsonData[this._messageKey]?.text || "";
            // 替换参数
            this._args.forEach((arg, index) => {
              text = text.replace(`s%`, arg || "");
            });
            this.getComponent(Label).string = text;
          } catch (parseError) {
            log.error("解析JSON数据时出错:", parseError);
          }
        });
      } else {
        log.log("文件不存在:", filePath);
        this.getComponent(Label).string = "";
      }
    } else {
      // 运行时使用LangMgr获取文本
      this.getComponent(Label).string = LangMgr.txMsgCode(this._messageKey, this._args, "");
    }
  }

  start() {
    this.updateMessage();
  }
}
