import {
  _decorator,
  Color,
  Component,
  instantiate,
  Label,
  Node,
  Sprite,
  Tween,
  tween,
  UIOpacity,
  UITransform,
} from "cc";
import { DisciplinesModule } from "db://assets/GameScrpit/module/disciplines/DisciplinesModule";
import { Sleep } from "../../../GameDefine";
import { ActivityTakeRequest, DaySignMessage } from "../../../net/protocol/Activity";
import { dtTime } from "../../../BoutStartUp";
import TipMgr from "db://assets/GameScrpit/lib/tips/TipMgr";
import MsgMgr from "db://assets/GameScrpit/lib/event/MsgMgr";
import MsgEnum from "../../../event/MsgEnum";
import { PlayerModule } from "db://assets/GameScrpit/module/player/PlayerModule";
import GameHttpApi from "../../../httpNet/GameHttpApi";
import { GoodsRouteName } from "db://assets/GameScrpit/module/goods/GoodsRoute";
import { UIMgr } from "db://assets/GameScrpit/lib/ui/UIMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "db://assets/GameScrpit/lib/utils/FmUtils";
import { LeaderFundVO, DaySignVO } from "db://assets/GameScrpit/module/activity/ActivityConfig";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("DisMain2")
export class DisMain2 extends Component {
  private _disciplinesdb: LeaderFundVO;
  private _signVO: DaySignVO = null;
  private _daySign: DaySignMessage = null;
  /**普通签到的最大天数 -- 顺序签到 */
  private _maxDay: number = null;

  /**奖励map */
  private _rewardMap: Map<number, Node> = new Map<number, Node>();

  @property(Node)
  fuze_item: Node = null;
  @property(Node)
  lbl_fuze_day: Node = null;
  @property(Node)
  btn_yigoumai2: Node = null;
  @property(Node)
  btn_main2_goumai: Node = null;
  @property(Node)
  lbl_main2_money: Node = null;
  @property(Node)
  content_list2: Node = null;

  @property(Node)
  pengz: Node = null;

  protected onEnable(): void {}

  protected onDisable(): void {}

  protected onLoad(): void {
    MsgMgr.on(MsgEnum.ON_ACTIVITY_FUND_RECHARGE, this.initMain2, this);
  }

  start() {
    this.initMain2();
  }

  private async initMain2() {
    this._disciplinesdb = await DisciplinesModule.data.getdb();
    this._signVO = await DisciplinesModule.data.getdb().then((res) => res.signVO);
    this._daySign = DisciplinesModule.data.daySign;
    this._maxDay = this._daySign.basicList.length;
    if (this._daySign.sign == true) {
      this._maxDay -= 1;
    }
    this.lbl_fuze_day.getComponent(Label).string = this._maxDay.toString();
    if (this._daySign.sign == true) {
      let day = this._maxDay + 1;
      this.lbl_fuze_day.getComponent(Label).string = String(day);
    }
    this.btn_main2_goumai.active = !this._daySign.paid;
    this.btn_yigoumai2.active = this._daySign.paid;
    this.lbl_main2_money.getComponent(Label).string = (this._signVO.price % 10000) + "元";
    this.loadListMain2();
  }

  private async loadListMain2() {
    let basicRewardList = this._signVO.basicRewardList;
    let paidRewardList = this._signVO.paidRewardList;
    let num = this.content_list2.children.length - basicRewardList.length;
    if (num > 0) {
      for (let j = 0; j < num; j++) {
        let node = this.content_list2.children[0];
        node.removeFromParent();
        node.destroy();
      }
    }
    for (let i = 0; i < basicRewardList.length; i++) {
      await Sleep(dtTime);
      if (this.isValid == false) {
        return;
      }
      let node = this.content_list2.children[i];
      if (!node) {
        node = instantiate(this.fuze_item);
        this.content_list2.addChild(node);
        node.active = true;
      }
      node["itemIndex"] = i;
      this._rewardMap.set(i, node);
      this.initFuzeItem(node, basicRewardList[i], paidRewardList[i]);
      this.setCurDay(node, i);
      this.setIsGetAward(node, i);
      this.setSuoIs(node);
      this.setGetBtn(node, i);
    }
  }

  private initFuzeItem(node: Node, basList: Array<number>, payList: Array<number>) {
    node.getChildByName("bg_biaoqxx").getChildByName("lbl_day").getComponent(Label).string =
      "签到" + (node["itemIndex"] + 1) + "天";
    let basics_Item1 = node.getChildByName("basics_Item1");
    FmUtils.setItemNode(basics_Item1, basList[0], basList[1]);
    let pay_Item1 = node.getChildByName("pay_Item1");
    FmUtils.setItemNode(pay_Item1, payList[0], payList[1]);
  }

  private setCurDay(node: Node, day: number) {
    if (this._maxDay == day) {
      this.set_is_day_gary(node.getChildByName("basics_Item1"), true);
      this.set_is_day_gary(node.getChildByName("pay_Item1"), true);
    } else if (this._maxDay > day) {
      this.set_is_day_gary(node.getChildByName("basics_Item1"), true);
      this.set_is_day_gary(node.getChildByName("pay_Item1"), true);
    } else {
      this.set_is_day_gary(node.getChildByName("basics_Item1"), false);
      this.set_is_day_gary(node.getChildByName("pay_Item1"), false);
    }
  }

  private set_is_day_gary(node: Node, bool: boolean) {
    let color = new Color(100, 100, 100, 255);
    if (bool == true) {
      color = new Color(255, 255, 255, 255);
    }
    node.getComponentsInChildren(Sprite).forEach((sprite) => {
      sprite.color = color;
    });
    node.getComponentsInChildren(Label).forEach((label) => {
      label.color = color;
    });
  }

  private setIsGetAward(node: Node, day: number) {
    if (this._daySign.basicList.indexOf(day) == -1) {
      node.getChildByName("basics_Item1").getChildByName("spr_gou").active = false;
    } else {
      node.getChildByName("basics_Item1").getChildByName("spr_gou").active = true;
    }
    if (this._daySign.paidList.indexOf(day) == -1) {
      node.getChildByName("pay_Item1").getChildByName("spr_gou").active = false;
    } else {
      node.getChildByName("pay_Item1").getChildByName("spr_gou").active = true;
    }
  }

  private setSuoIs(node: Node) {
    node.getChildByName("pay_Item1").getChildByName("spr_suo").active = !this._daySign.paid;
  }

  private setGetBtn(node: Node, day: number) {
    if (this._maxDay == day) {
      if (this._daySign.sign == false) {
        node.getChildByName("btn_qiandao1").active = true;
        node.getChildByName("btn_qiandao2").active = false;
        node.getChildByName("weiqiandao").active = false;
        node.getChildByName("btn_yiqiandaou").active = false;
      }
      if (this._daySign.sign == true) {
        node.getChildByName("btn_qiandao1").active = false;
        node.getChildByName("btn_qiandao2").active = true;
        node.getChildByName("weiqiandao").active = false;
        node.getChildByName("btn_yiqiandaou").active = false;
      }
      if (this._daySign.paidList.indexOf(day) != -1) {
        node.getChildByName("btn_qiandao1").active = false;
        node.getChildByName("btn_qiandao2").active = false;
        node.getChildByName("weiqiandao").active = false;
        node.getChildByName("btn_yiqiandaou").active = true;
      }
      return;
    }
    /**未来 */
    if (day > this._maxDay) {
      node.getChildByName("btn_qiandao1").active = false;
      node.getChildByName("btn_qiandao2").active = false;
      node.getChildByName("weiqiandao").active = true;
      node.getChildByName("btn_yiqiandaou").active = false;
      return;
    }
    /**过去 */
    if (day < this._maxDay) {
      if (this._daySign.paidList.indexOf(day) != -1) {
        node.getChildByName("btn_qiandao1").active = false;
        node.getChildByName("btn_qiandao2").active = false;
        node.getChildByName("weiqiandao").active = false;
        node.getChildByName("btn_yiqiandaou").active = true;
      } else {
        node.getChildByName("btn_qiandao1").active = false;
        node.getChildByName("btn_qiandao2").active = true;
        node.getChildByName("weiqiandao").active = false;
        node.getChildByName("btn_yiqiandaou").active = false;
      }
    }
  }

  private on_click_btn_qiandao1(event) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    if (this._daySign.sign == true) {
      TipMgr.showTip("今日已签到");
      return;
    }
    this.getDayAward(event.target.parent["itemIndex"]);
  }
  private on_click_btn_qiandao2(event) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    if (this._daySign.paid == false) {
      this.on_click_btn_main2_goumai();
      return;
    }
    this.getDayAward(event.target.parent["itemIndex"]);
  }
  private getDayAward(day: number) {
    let param: ActivityTakeRequest = {
      activityId: this._disciplinesdb.id,
      index: day,
      takeAll: false,
    };
    DisciplinesModule.api.takeLeaderSignPaidReward(param, (rs) => {
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: rs.rewardList });
      this.initMain2();
    });
  }
  private on_click_btn_main2_goumai() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let data = {
      goodsId: this._signVO.id,
      goodsType: this._disciplinesdb.buyType,
      playerId: PlayerModule.data.playerId,
      orderAmount: (this._signVO.price % 10000) % 10000,
      goodsName: this._disciplinesdb.name,
      platformType: "TEST",
    };

    GameHttpApi.pay(data).then((resp: any) => {
      if (resp.code != 200) {
        let err = JSON.parse(resp.msg);
        log.log(err);
        return;
      }
      UIMgr.instance.showDialog(GoodsRouteName.UIPay, { url: resp.data.url });
    });
  }

  private _tickIndex: number = 0;
  update(deltaTime: number) {
    let pengz = this.pengz;

    let content_list = this.content_list2;

    const Box1 = pengz.getComponent(UITransform).getBoundingBoxToWorld();

    const index = this._tickIndex;
    for (let i = index; i < index + 5 && i < content_list.children.length; i++) {
      const Box2 = content_list.children[i].getComponent(UITransform).getBoundingBoxToWorld();

      if (Box1.intersects(Box2)) {
        content_list.children[i].walk((val) => {
          if (val.getComponent(Label)) {
            val.getComponent(Label).enabled = true;
          }
          if (val.getComponent(Sprite)) {
            val.getComponent(Sprite).enabled = true;
          }
        });
      } else {
        content_list.children[i].walk((val) => {
          if (val.getComponent(Label)) {
            val.getComponent(Label).enabled = false;
          }
          if (val.getComponent(Sprite)) {
            val.getComponent(Sprite).enabled = false;
          }
        });
      }
      this._tickIndex = i;
    }

    if (this._tickIndex >= content_list.children.length - 1) {
      this._tickIndex = 0;
    }
  }

  protected onDestroy(): void {
    Tween.stopAllByTarget(this.node);
    MsgMgr.off(MsgEnum.ON_ACTIVITY_FUND_RECHARGE, this.initMain2, this);
  }
}
