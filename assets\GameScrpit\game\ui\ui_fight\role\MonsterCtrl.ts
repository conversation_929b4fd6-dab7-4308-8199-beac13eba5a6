import { tween } from "cc";
import { Vec3 } from "cc";
import { sp } from "cc";
import { UIOpacity } from "cc";
import { _decorator, Component, Node } from "cc";
import { RoleStatus } from "../../../GameDefine";
import { math } from "cc";
import { v3 } from "cc";
import { FightAudioName } from "db://assets/GameScrpit/module/fight/src/FightConfig";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
const { ccclass, property } = _decorator;

@ccclass("MonsterCtrl")
export class MonsterCtrl extends Component {
  @property(Node)
  public nodeMonster: Node;

  @property(Node)
  public nodeBoom: Node;

  public status: RoleStatus;

  // 怪物移动速度
  public moveSpeed = 200;

  // 移动的目标坐标点
  public targetPos: Vec3;

  start() {
    this.playIdle();
  }

  update(deltaTime: number) {
    if (!this.targetPos) {
      return;
    }
    // 移动到某个位置
    let x = this.targetPos.x - this.node.position.x;
    let y = this.targetPos.y - this.node.position.y;
    let distance = Math.sqrt(x * x + y * y);

    let dt = deltaTime * this.moveSpeed;
    if (distance < dt) {
      this.node.setPosition(this.targetPos);
    } else {
      let rate = dt / distance;
      let posEnd = v3(this.node.position.x + rate * x, this.node.position.y + rate * y);
      this.node.setPosition(posEnd);
    }

    // 切换状态
  }

  public init(skinId: number) {
    this.playIdle();

    this.nodeMonster.active = true;
    setTimeout(() => {
      this.nodeMonster
        .getChildByName("spine_monster")
        .getComponent(sp.Skeleton)
        .setAnimation(0, `xiaoguai${skinId}`, true);
    }, 1);
  }

  /**
   * 移动到某个位置
   * @param pos 移动到某个位置
   */
  public moveTo(pos: Vec3) {
    this.targetPos = pos;

    if (this.status == RoleStatus.WALK) {
      return;
    }
    this.status = RoleStatus.WALK;
  }

  // 闲置状态
  public playIdle() {
    this.status = RoleStatus.IDLE;

    this.nodeMonster.active = true;
    this.nodeBoom.active = false;
    this.nodeMonster.getComponent(UIOpacity).opacity = 255;
    return;
  }

  // 打飞动作
  public playDie(callBack?: Function) {
    AudioMgr.instance.playEffect(FightAudioName.Effect.撞击);
    if (this.status == RoleStatus.DIE) {
      return;
    }
    this.status = RoleStatus.DIE;

    // 渐隐怪物
    tween(this.nodeMonster.getComponent(UIOpacity)).to(0.3, { opacity: 0 }).start();

    // 显示爆炸动效
    this.nodeBoom.active = true;
    this.nodeBoom.getComponentInChildren(sp.Skeleton).setAnimation(0, "animation", false);

    tween(this.node)
      .delay(2)
      .call(() => {
        callBack && callBack();
      })
      .start();
  }
}
