import { _decorator, instantiate, Layers, math, Node, Sprite, UITransform, v3 } from "cc";
import { Section } from "./Section";
import GameObject from "./GameObject";
import FightManager from "../../game/fight/manager/FightManager";
import { BuffLayerManager } from "../../game/fight/manager/BuffLayerManager";
import { BuffSpecialManager } from "../../game/fight/manager/BuffSpecialManager";
import { BuffDetail } from "../../game/fight/FightDefine";
import { GOBuff } from "../../game/fight/buff/GOBuff";

const { ccclass, property } = _decorator;

@ccclass
export default class buffSection extends Section {
  private _initcallBack;

  private _buffMap: Map<number, GOBuff> = new Map<number, GOBuff>();

  public static sectionName(): string {
    return "buffSection";
  }

  public onInit(go: GameObject, args) {
    super.onInit(go, args);
    this.onMsg("OnBuff", this.OnBuff.bind(this));
    this.onMsg("OnChangeBuff", this.OnChangeBuff.bind(this));
    this.ready();
  }

  public onRemove(): void {
    super.onRemove();
    this.offMsg("OnBuff", this.OnBuff.bind(this));
    this.offMsg("OnChangeBuff", this.OnChangeBuff.bind(this));
  }

  private async OnBuff(buffParam: BuffDetail) {
    if (this._buffMap.has(buffParam.buffId)) {
      let buff = this._buffMap.get(buffParam.buffId);
      buff.setRoundCount(buffParam.roundCount);
      return;
    }
    let goBuff: GOBuff = await FightManager.instance.getSection(BuffSpecialManager).doCallObject(buffParam);
    this._buffMap.set(buffParam.buffId, goBuff);
  }

  private OnChangeBuff(detail: { id: number; roundCount: number }) {
    if (this._buffMap.has(detail.id)) {
      let buff: GOBuff = this._buffMap.get(detail.id);
      if (detail.roundCount > 0) {
        buff.setRoundCount(detail.roundCount);
      } else {
        buff.remove();
        this._buffMap.delete(detail.id);
      }
    }
  }
}
