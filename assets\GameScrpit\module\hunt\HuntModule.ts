import { GameData } from "../../game/GameData";
import data from "../../lib/data/data";
import { <PERSON><PERSON><PERSON> } from "./HuntA<PERSON>";
import { HuntData } from "./HuntData";
import { <PERSON>Route } from "./HuntRoute";
import { HuntSubscriber } from "./HuntService";

export class HuntModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): HuntModule {
    if (!GameData.instance.HuntModule) {
      GameData.instance.HuntModule = new HuntModule();
      GameData.instance.HuntModule.onViewLoad();
    }
    return GameData.instance.HuntModule;
  }
  private _data = new HuntData();
  private _api = new HuntApi();
  private _route = new HuntRoute();
  private _subscriber = new HuntSubscriber();

  public static get data() {
    return this.instance._data;
  }
  public static get api() {
    return this.instance._api;
  }
  protected saveKey(): string {
    return this.constructor.name;
  }
  public init(data?: any, completedCallback?: Function) {
    if (this._subscriber) {
      this._subscriber.unRegister();
    }
    this._data = new HuntData();
    this._api = new HuntApi();
    this._route = new HuntRoute();
    this._subscriber = new HuntSubscriber();

    // 初始化模块
    HuntModule.api.getTrain((data) => {
      completedCallback && completedCallback();
    });

    this._route.init();
    this._subscriber.register();
  }
}
