import { sys } from "cc";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export const PlatformHttp = {
  baseUrl: "https://client-1317648075.cos.ap-guangzhou.myqcloud.com/res/",
  tokenInfo: {
    id: null,
    token: "",
    refreshToken: "",
  },
  timeout: 50000,
  createXhr: function (options: any = {}) {
    var xhr = new XMLHttpRequest();
    xhr.timeout = options.timeout || this.timeout;
    xhr.setRequestHeader("Content-Type", "application/json");
    xhr.setRequestHeader("Token", this.tokenInfo.token);
    xhr.setRequestHeader("Id", this.tokenInfo.id);
    return xhr;
  },
  GET: function (options: any = {}) {
    return new Promise((resolve, reject) => {
      let query = options.params ? "?" : "";

      let url = "";
      if (options.url.indexOf("http://") == 0 || options.url.indexOf("https://") == 0) {
        url = options.url + query;
      } else {
        url = this.baseUrl + options.url + query;
      }

      if (options.params) {
        Object.entries(options.params).forEach(([key, value], index) => {
          query = index === 0 ? query : query + "&";
          query += `${key}=${value}`;
        });
      }

      if (sys.platform == sys.Platform.WECHAT_GAME) {
        // log.log("微信小游戏");
        let xhr = this.createXhr(options);

        xhr.onreadystatechange = function () {
          if (xhr.readyState === 4) {
            if (xhr.status >= 200 && xhr.status < 300) {
              if (xhr.responseText.startsWith("{")) {
                var res = JSON.parse(xhr.responseText);
                resolve(res);
              } else {
                resolve(xhr.responseText);
              }
            } else {
              reject(xhr.status);
            }
          }
        };
        xhr.open("GET", url + query);
        xhr.send();
      } else {
        fetch(url + query, {
          method: "GET",
          headers: {
            Token: this.tokenInfo.token,
            Id: this.tokenInfo.id,
          },
        })
          .then((res: Response) => {
            return res.text();
          })
          .then((data) => {
            if (data.startsWith("{")) {
              resolve(JSON.parse(data));
            } else {
              resolve(data);
            }
          })
          .catch((error) => {
            log.error(error);
            reject(error);
          });
      }
    });
  },
  POST: function (options) {
    return new Promise((resolve, reject) => {
      let url = "";
      if (options.url.indexOf("http://") == 0 || options.url.indexOf("https://") == 0) {
        url = options.url;
      } else {
        url = this.baseUrl + options.url;
      }

      if (sys.platform == sys.Platform.WECHAT_GAME) {
        // log.log("微信小游戏");
        let xhr = this.createXhr(options);

        xhr.onreadystatechange = function () {
          if (xhr.readyState === 4) {
            if (xhr.status >= 200 && xhr.status < 300) {
              var res = JSON.parse(xhr.responseText);
              resolve(res);
            } else {
              reject(xhr.status);
            }
          }
        };
        xhr.open("POST", url);
        xhr.send(JSON.stringify(options.params));
      } else {
        fetch(url, {
          method: "POST",
          headers: {
            Accept: "application/json",
            "Content-Type": "application/json",
            Token: this.tokenInfo.token,
            Id: this.tokenInfo.id,
          },
          body: JSON.stringify(options.params),
        })
          .then((res: Response) => {
            return res.json();
          })
          .then((data) => {
            resolve(data);
          })
          .catch((error) => {
            reject(error);
          });
      }
    });
  },
  isLogin: function () {
    return this.tokenInfo.token ? true : false;
  },
};
