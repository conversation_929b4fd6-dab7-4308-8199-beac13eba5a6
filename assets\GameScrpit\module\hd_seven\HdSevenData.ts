import MsgEnum from "../../game/event/MsgEnum";
import { JsonMgr } from "../../game/mgr/JsonMgr";
import { SevenDaySignMessage } from "../../game/net/protocol/Activity";
import MsgMgr from "../../lib/event/MsgMgr";
import { times } from "../../lib/utils/NumbersUtils";
import { HdSevenModule } from "./HdSevenModule";

export class HdSevenData {
  private _sevenDaySignMessage: SevenDaySignMessage = {
    activityId: 0,
    count: 0,
    sign: false,
    endTime: 0,
  };

  public get sevenDaySignMessage(): SevenDaySignMessage {
    return this._sevenDaySignMessage;
  }

  // 七日签到类活动更新
  public set sevenDaySignMessage(value: SevenDaySignMessage) {
    this._sevenDaySignMessage = value;
    HdSevenModule.service.updatePopover();
    MsgMgr.emit(MsgEnum.ON_ACTIVITY_SEVEN_UPDATE, this._sevenDaySignMessage);
  }
}
