{"skeleton": {"hash": "zgiGH95FGablXqdq6dQdq/lMJz4=", "spine": "3.8.75", "x": -137.88, "y": -13.67, "width": 255.16, "height": 233}, "bones": [{"name": "root"}, {"name": "bone44", "parent": "root", "length": 110.01, "rotation": 0.69, "x": -1.81, "y": -4.34}, {"name": "bone", "parent": "bone44", "length": 65.33, "rotation": 1.52, "x": 1.68, "y": 94.5}, {"name": "bone2", "parent": "bone", "length": 30.59, "rotation": 58.9, "x": -0.81, "y": 0.59}, {"name": "bone3", "parent": "bone2", "length": 28.14, "rotation": 6.15, "x": 30.59}, {"name": "bone4", "parent": "bone3", "length": 9.38, "x": 28.14}, {"name": "bone5", "parent": "bone4", "length": 30, "rotation": -143.52, "x": 9.65, "y": -11.43}, {"name": "bone6", "parent": "bone4", "length": 23.48, "rotation": -83.98, "x": 17.13, "y": -23.54}, {"name": "bone7", "parent": "bone4", "length": 10.95, "rotation": 52.66, "x": 52.45, "y": -10.28}, {"name": "bone8", "parent": "bone7", "length": 12.74, "rotation": -21.7, "x": 10.95}, {"name": "bone9", "parent": "bone4", "length": 15.81, "rotation": 91.04, "x": 19.92, "y": 0.04}, {"name": "bone10", "parent": "bone9", "length": 15.79, "rotation": -11.19, "x": 15.81}, {"name": "bone11", "parent": "bone4", "length": 11.93, "rotation": 129.21, "x": 8.41, "y": -3.09}, {"name": "bone12", "parent": "bone11", "length": 13.9, "rotation": 12.02, "x": 11.93}, {"name": "bone13", "parent": "bone", "length": 41.88, "rotation": -31.36, "x": 15.91, "y": -10.88}, {"name": "bone14", "parent": "bone13", "length": 42.73, "rotation": -92.66, "x": 41.88}, {"name": "bone15", "parent": "bone14", "length": 25.41, "rotation": 71.32, "x": 42.22, "y": 0.31}, {"name": "bone16", "parent": "bone15", "length": 22.31, "rotation": 34.65, "x": 25.41}, {"name": "bone17", "parent": "bone", "length": 34.86, "rotation": -137.82, "x": -26.11, "y": -7.14}, {"name": "bone18", "parent": "bone17", "length": 42.86, "rotation": 86.1, "x": 34.86}, {"name": "bone19", "parent": "bone18", "length": 28.68, "rotation": -84.36, "x": 42.86}, {"name": "bone20", "parent": "bone19", "length": 22.81, "rotation": -43.46, "x": 28.68}, {"name": "target1", "parent": "bone44", "rotation": -0.69, "x": 31.11, "y": 27.96}, {"name": "target2", "parent": "target1", "x": 38.42, "y": -24.91}, {"name": "target3", "parent": "bone44", "rotation": -0.69, "x": -22.4, "y": 30.19}, {"name": "target4", "parent": "target3", "x": -42.4, "y": -20.93}, {"name": "bone21", "parent": "bone3", "x": 21.75, "y": 50.21}, {"name": "bone22", "parent": "bone21", "length": 36.87, "rotation": 156.44, "x": -4.84, "y": 4.16}, {"name": "bone23", "parent": "bone22", "length": 36.85, "rotation": 107.76, "x": 35.62, "y": 2.83}, {"name": "bone24", "parent": "bone23", "length": 25.23, "rotation": 28.55, "x": 37.06, "y": 0.11}, {"name": "bone25", "parent": "bone3", "x": 28.76, "y": -9.57}, {"name": "bone26", "parent": "bone25", "length": 31.42, "rotation": -117.22, "x": 14.73, "y": 4.89}, {"name": "bone27", "parent": "bone26", "length": 32.54, "rotation": 23.18, "x": 30.98, "y": -0.17}, {"name": "bone28", "parent": "bone27", "length": 20.12, "rotation": 19.38, "x": 32.54}, {"name": "bone29", "parent": "bone", "length": 46.11, "rotation": 164.68, "x": -32.25, "y": 1.52}, {"name": "bone30", "parent": "bone29", "length": 42.83, "rotation": -54.76, "x": 46.21, "y": -0.42}, {"name": "bone31", "parent": "bone30", "length": 36.63, "rotation": 68.56, "x": 42.83}, {"name": "bone32", "parent": "bone4", "length": 20.78, "rotation": 49.64, "x": 42.34, "y": -2.26}, {"name": "bone33", "parent": "bone32", "length": 19.9, "rotation": -10.12, "x": 20.78}, {"name": "bone34", "parent": "bone4", "length": 24.9, "rotation": 79.73, "x": 35.33, "y": 8.39}, {"name": "bone35", "parent": "bone34", "length": 24.31, "rotation": 18.09, "x": 24.9}, {"name": "bone36", "parent": "bone4", "length": 21.86, "rotation": 101.04, "x": 11.22, "y": 11.87}, {"name": "bone37", "parent": "bone36", "length": 19.57, "rotation": -41.42, "x": 21.86}, {"name": "bone38", "parent": "bone37", "length": 18.64, "rotation": 16.1, "x": 19.37, "y": 0.16}, {"name": "bone39", "parent": "bone4", "length": 10.77, "rotation": 132.73, "x": -2.1, "y": 18.53}, {"name": "bone40", "parent": "bone4", "length": 13.26, "rotation": 50.42, "x": 38.73, "y": 5.71}, {"name": "bone41", "parent": "bone40", "length": 16.15, "rotation": -22, "x": 13.16, "y": 0.05}, {"name": "bone42", "parent": "bone4", "length": 10.48, "rotation": -23.13, "x": 48.84, "y": -18.97}, {"name": "bone43", "parent": "bone42", "length": 13.76, "rotation": 11.01, "x": 10.48}, {"name": "<PERSON><PERSON><PERSON>", "parent": "root", "rotation": -10.59, "x": 11.71, "y": 138.47, "scaleY": -1}, {"name": "zhuaji2", "parent": "root", "rotation": -10.59, "x": 83.98, "y": 143.42, "scaleY": -1}, {"name": "ci", "parent": "bone44", "rotation": -0.69, "x": -6.75, "y": 136.43, "scaleX": 2.184, "scaleY": 2.184}], "slots": [{"name": "langyao_01", "bone": "bone29", "attachment": "langyao_01"}, {"name": "langyao_02", "bone": "bone26", "attachment": "langyao_02"}, {"name": "langyao_03", "bone": "bone2", "attachment": "langyao_03"}, {"name": "langyao_04", "bone": "bone", "attachment": "langyao_04"}, {"name": "sc1", "bone": "bone21", "attachment": "sc1"}, {"name": "sc2", "bone": "bone22", "attachment": "sc2"}, {"name": "sc3", "bone": "bone21", "attachment": "sc3"}, {"name": "langyao_07", "bone": "bone42", "attachment": "langyao_07"}, {"name": "langyao_08", "bone": "bone4", "attachment": "langyao_08"}, {"name": "langyao_09", "bone": "bone40", "attachment": "langyao_09"}, {"name": "langyao_010", "bone": "bone4", "attachment": "langyao_010"}, {"name": "langyao_011", "bone": "bone4", "attachment": "langyao_011"}, {"name": "langyao_012", "bone": "bone6", "attachment": "langyao_012"}, {"name": "langyao_06", "bone": "bone23", "attachment": "langyao_06"}, {"name": "zhuaji/qf_boss_fsf2_pg_bjzh_00", "bone": "<PERSON><PERSON><PERSON>", "color": "ff6969ff"}, {"name": "zhuaji/qf_boss_fsf2_pg_bjzh_0", "bone": "zhuaji2", "color": "ff6969ff"}, {"name": "z<PERSON>ji/qf_boss_fsf2_pg_bjzh_2", "bone": "<PERSON><PERSON><PERSON>", "blend": "additive"}, {"name": "zhuaji/qf_boss_fsf2_pg_bjzh_1", "bone": "zhuaji2", "blend": "additive"}, {"name": "ci/moxie_skill_ci_30", "bone": "ci", "color": "ff770000"}, {"name": "ci/moxie_skill_ci_31", "bone": "ci", "color": "ff7800ff", "blend": "additive"}, {"name": "bone44", "bone": "bone44", "attachment": "bone44"}], "ik": [{"name": "target1", "bones": ["bone13", "bone14"], "target": "target1", "bendPositive": false}, {"name": "target2", "order": 1, "bones": ["bone15", "bone16"], "target": "target2"}, {"name": "target3", "order": 2, "bones": ["bone17", "bone18"], "target": "target3"}, {"name": "target4", "order": 3, "bones": ["bone19", "bone20"], "target": "target4", "bendPositive": false}], "skins": [{"name": "default", "attachments": {"bone44": {"bone44": {"type": "boundingbox", "vertexCount": 4, "vertices": [-115.5, 222.17, -117.46, -4.26, 103.62, -6.15, 101.74, 219.56]}}, "langyao_01": {"langyao_01": {"type": "mesh", "uvs": [0, 0.41308, 0.14514, 0.3029, 0.25758, 0.41308, 0.27866, 0.68065, 0.42975, 0.96397, 0.66518, 1, 0.8514, 0.96922, 1, 0.94298, 0.98844, 0.82231, 0.88303, 0.6072, 0.70734, 0.44981, 0.55976, 0.13501, 0.41218, 0, 0.19433, 0, 0.01513, 0.19797, 0.06451, 0.25604, 0.20432, 0.15862, 0.40706, 0.263, 0.49328, 0.55528, 0.67504, 0.76404, 0.84981, 0.84755], "triangles": [1, 16, 2, 2, 16, 17, 0, 15, 1, 0, 14, 15, 1, 15, 16, 17, 16, 12, 15, 14, 16, 14, 13, 16, 16, 13, 12, 4, 3, 18, 19, 18, 10, 3, 2, 18, 2, 17, 18, 18, 11, 10, 18, 17, 11, 17, 12, 11, 5, 20, 6, 5, 19, 20, 5, 4, 19, 7, 20, 8, 7, 6, 20, 4, 18, 19, 20, 9, 8, 20, 19, 9, 19, 10, 9], "vertices": [1, 36, 43.47, 15.34, 1, 3, 34, 95.9, -21.56, 0.00037, 35, 45.94, 28.38, 0.00918, 36, 27.55, 7.48, 0.99044, 3, 34, 82.13, -16.51, 0.04016, 35, 33.87, 20.06, 0.37907, 36, 15.39, 15.67, 0.58078, 3, 34, 75.46, 1.99, 0.39005, 35, 14.91, 25.29, 0.54338, 36, 13.33, 35.23, 0.06657, 2, 34, 54.73, 18.39, 0.99833, 35, -10.45, 17.82, 0.00167, 1, 34, 29.14, 15.13, 1, 1, 34, 9.88, 8.34, 1, 1, 34, -5.46, 2.8, 1, 1, 34, -2.23, -5.5, 1, 2, 34, 12.52, -18.18, 0.92875, 35, -4.94, -37.76, 0.07125, 2, 34, 33.78, -25.03, 0.31437, 35, 12.92, -24.35, 0.68563, 2, 35, 40.27, -18.1, 0.99557, 36, -17.78, -4.23, 0.00443, 2, 35, 55.46, -6.91, 0.27699, 36, -1.82, -14.28, 0.72301, 1, 36, 21.93, -14.56, 1, 1, 36, 41.63, -0.34, 1, 1, 36, 36.3, 3.96, 1, 1, 36, 20.98, -2.97, 1, 3, 34, 68.75, -30.88, 0.00015, 35, 37.88, 0.84, 0.94972, 36, -1.03, 4.91, 0.05013, 3, 34, 54.76, -12.23, 0.00193, 35, 14.58, 0.17, 0.9979, 36, -10.17, 26.36, 0.00017, 2, 34, 32, -1.89, 0.96602, 35, -7, -12.45, 0.03398, 2, 34, 12.07, -0.27, 0.99982, 35, -19.82, -27.8, 0.00018], "hull": 15}}, "langyao_02": {"langyao_02": {"type": "mesh", "uvs": [0.00163, 0.05691, 0.08886, 0, 0.2332, 0, 0.38816, 0.06405, 0.54735, 0.08548, 0.63226, 0.06762, 0.7575, 0.18548, 0.71292, 0.38905, 0.73255, 0.4015, 0.84155, 0.35305, 0.9526, 0.36343, 1, 0.52953, 0.96391, 0.73369, 0.84977, 0.77867, 0.83949, 0.94477, 0.75414, 0.99321, 0.6801, 0.94823, 0.60812, 0.80981, 0.57933, 0.75272, 0.584, 0.66238, 0.5855, 0.63334, 0.4837, 0.64026, 0.39938, 0.68178, 0.31197, 0.65237, 0.19236, 0.50417, 0.05037, 0.32842, 0, 0.19661, 0.10749, 0.15268, 0.33761, 0.28174, 0.50245, 0.36412, 0.62649, 0.4822, 0.75706, 0.6552, 0.85988, 0.5536, 0.73584, 0.82271], "triangles": [16, 33, 15, 15, 33, 14, 16, 17, 33, 14, 33, 13, 33, 31, 13, 33, 17, 31, 18, 19, 17, 17, 19, 31, 20, 31, 19, 13, 32, 12, 13, 31, 32, 12, 32, 11, 31, 30, 8, 31, 8, 32, 8, 9, 32, 32, 10, 11, 32, 9, 10, 31, 20, 30, 21, 22, 29, 30, 7, 8, 22, 28, 29, 21, 29, 20, 28, 22, 23, 20, 29, 30, 7, 30, 29, 7, 29, 5, 5, 29, 4, 7, 5, 6, 29, 3, 4, 23, 24, 28, 25, 27, 24, 24, 27, 28, 28, 3, 29, 25, 26, 27, 27, 2, 28, 28, 2, 3, 26, 0, 27, 0, 1, 27, 27, 1, 2], "vertices": [1, 31, -12.32, 2.64, 1, 1, 31, -5.01, 9.34, 1, 1, 31, 9.24, 14.91, 1, 2, 31, 26.01, 17.12, 0.97155, 32, -4.74, 17.36, 0.02845, 2, 31, 42.22, 22, 0.39614, 32, 11.53, 22.02, 0.60386, 2, 31, 50.2, 26.32, 0.20827, 32, 19.57, 26.23, 0.79173, 3, 31, 65.26, 24.22, 0.09355, 32, 34.6, 23.94, 0.8802, 33, -0.2, 24.03, 0.02625, 3, 31, 65.52, 10.56, 0.02501, 32, 34.68, 10.27, 0.54168, 33, 1.17, 10.43, 0.43331, 3, 31, 67.75, 10.58, 0.00942, 32, 36.9, 10.27, 0.25863, 33, 3.38, 10.63, 0.73195, 2, 32, 46.65, 17.18, 2e-05, 33, 12.44, 18.44, 0.99998, 1, 33, 23.31, 23, 1, 1, 33, 32.4, 15.79, 1, 1, 33, 34.59, 2.55, 1, 1, 33, 24.96, -5.29, 1, 1, 33, 28.56, -15.18, 1, 2, 32, 52.14, -23.83, 0.00077, 33, 21.76, -21.88, 0.99923, 2, 32, 43.79, -23.93, 0.02118, 33, 13.46, -22.77, 0.97882, 2, 32, 33.59, -18.45, 0.19091, 33, 2.78, -18.27, 0.80909, 2, 32, 29.47, -16.15, 0.3085, 33, -1.53, -16.37, 0.6915, 2, 32, 27.93, -10.65, 0.55662, 33, -3.58, -11.04, 0.44338, 2, 32, 27.44, -8.88, 0.74101, 33, -4.24, -9.32, 0.25899, 1, 32, 17.49, -13.08, 1, 1, 32, 10.04, -18.67, 1, 2, 31, 31.96, -20.35, 0.03565, 32, 0.71, -20.18, 0.96435, 2, 31, 16.76, -16.26, 0.62036, 32, -14.44, -15.89, 0.37964, 2, 31, -1.29, -11.41, 0.99399, 32, -32.42, -10.81, 0.00601, 1, 31, -9.28, -5.62, 1, 1, 31, 0.32, 1.1, 1, 1, 31, 26, 2.39, 1, 2, 31, 44.17, 3.91, 0.08635, 32, 13.24, 3.91, 0.91365, 2, 31, 59.12, 1.76, 0.00268, 32, 28.16, 1.56, 0.99732, 1, 33, 12.72, -2.6, 1, 1, 33, 19.71, 7.93, 1, 2, 32, 46.55, -14.46, 0.0111, 33, 15.31, -13.07, 0.9889], "hull": 27}}, "langyao_03": {"langyao_03": {"type": "mesh", "uvs": [0.39528, 0.97948, 0.52649, 0.96678, 0.65174, 0.90687, 0.71735, 0.79796, 0.80483, 0.70176, 0.96785, 0.59103, 1, 0.44399, 0.98375, 0.22798, 0.84061, 0.07914, 0.61397, 0, 0.36546, 0, 0.13882, 0.04283, 0, 0.19168, 0, 0.42584, 0.04736, 0.63822, 0.08911, 0.80704, 0.2243, 0.93592, 0.80085, 0.31148, 0.78693, 0.5166, 0.66367, 0.61826, 0.54836, 0.75258, 0.46884, 0.87057, 0.47878, 0.21709, 0.29985, 0.34053, 0.27003, 0.52749, 0.26208, 0.7072, 0.27798, 0.8379, 0.5762, 0.39317, 0.47878, 0.57288, 0.4251, 0.73988, 0.38136, 0.85423], "triangles": [22, 10, 9, 23, 11, 10, 23, 10, 22, 12, 11, 23, 13, 12, 23, 24, 13, 23, 17, 7, 6, 8, 22, 9, 17, 8, 7, 17, 22, 8, 27, 22, 17, 23, 22, 27, 18, 27, 17, 18, 17, 6, 5, 18, 6, 18, 19, 27, 4, 18, 5, 28, 24, 23, 27, 28, 23, 19, 28, 27, 14, 13, 24, 19, 18, 4, 25, 14, 24, 25, 24, 28, 29, 25, 28, 20, 28, 19, 29, 28, 20, 3, 19, 4, 20, 19, 3, 15, 14, 25, 26, 25, 29, 15, 25, 26, 30, 26, 29, 21, 29, 20, 30, 29, 21, 2, 20, 3, 21, 20, 2, 16, 15, 26, 1, 21, 2, 0, 30, 21, 0, 21, 1, 16, 26, 30, 0, 16, 30], "vertices": [2, 3, -15.58, 2.32, 0.98559, 26, -67.41, -42.96, 0.01441, 2, 3, -9.23, -6.76, 0.99969, 26, -62.07, -52.67, 0.00031, 1, 3, 0.68, -13.31, 1, 3, 3, 12.12, -13.29, 0.99539, 4, -19.79, -11.23, 0.00294, 30, -48.55, -1.66, 0.00167, 3, 3, 23.42, -15.45, 0.76772, 4, -8.79, -14.59, 0.18907, 30, -37.55, -5.01, 0.04321, 3, 3, 38.96, -22.51, 0.12485, 4, 5.9, -23.28, 0.57651, 30, -22.86, -13.7, 0.29865, 3, 3, 52.1, -18.34, 0.00499, 4, 19.42, -20.54, 0.33476, 30, -9.34, -10.96, 0.66025, 2, 4, 37.22, -11.59, 0.12686, 30, 8.46, -2.02, 0.87314, 3, 4, 45.2, 4.79, 0.86593, 30, 16.44, 14.37, 0.06559, 26, 23.45, -45.42, 0.06848, 2, 4, 44.55, 25.16, 0.6647, 26, 22.8, -25.05, 0.3353, 2, 4, 36.48, 44.42, 0.21931, 26, 14.72, -5.8, 0.78069, 2, 3, 49.45, 62.83, 0.00123, 26, 3.73, 10.23, 0.99877, 2, 3, 31.83, 66.42, 0.08263, 26, -13.41, 15.69, 0.91737, 3, 3, 12.97, 56.01, 0.34327, 4, -11.53, 57.57, 0.00095, 26, -33.28, 7.36, 0.65578, 3, 3, -2.22, 43.08, 0.66109, 4, -28.01, 46.35, 0.00014, 26, -49.76, -3.87, 0.33877, 2, 3, -14.12, 32.51, 0.83194, 26, -62.72, -13.11, 0.16806, 2, 3, -19.01, 16.83, 0.93126, 26, -69.27, -28.16, 0.06874, 2, 4, 24.19, -0.39, 0.98929, 30, -4.57, 9.18, 0.01071, 3, 3, 37.6, -5.9, 0.05494, 4, 6.34, -6.61, 0.86238, 30, -22.42, 2.96, 0.08268, 2, 3, 24.41, -1.35, 0.99867, 30, -35.05, 8.89, 0.00133, 2, 3, 8.91, 1.15, 0.99804, 26, -43.18, -46.74, 0.00196, 2, 3, -3.82, 1.76, 0.99467, 26, -55.78, -44.78, 0.00533, 3, 3, 49.22, 30.08, 0.03956, 4, 21.74, 27.91, 0.49564, 26, -0.01, -22.3, 0.4648, 3, 3, 32.01, 37.75, 0.22559, 4, 5.45, 37.38, 0.16352, 26, -16.3, -12.83, 0.61089, 3, 3, 15.74, 31.63, 0.57052, 4, -11.38, 33.04, 0.06582, 26, -33.13, -17.17, 0.36366, 3, 3, 0.94, 24.22, 0.82528, 4, -26.88, 27.26, 0.0044, 26, -48.63, -22.95, 0.17031, 2, 3, -8.94, 17.24, 0.91994, 26, -59.21, -28.83, 0.08006, 3, 3, 38.99, 15.09, 0.12834, 4, 9.96, 14.1, 0.72304, 26, -11.79, -36.11, 0.14863, 3, 3, 20.56, 14.26, 0.76132, 4, -8.45, 15.26, 0.12365, 26, -30.2, -34.96, 0.11503, 3, 3, 4.93, 10.78, 0.94634, 4, -24.36, 13.47, 0.00146, 26, -46.11, -36.74, 0.0522, 2, 3, -6.05, 8.92, 0.96749, 26, -57.23, -37.42, 0.03251], "hull": 17}}, "langyao_04": {"langyao_04": {"type": "mesh", "uvs": [0.44349, 0.40013, 0.49059, 0.39173, 0.51582, 0.36864, 0.54105, 0.38753, 0.55115, 0.4568, 0.64542, 0.53424, 0.66392, 0.54303, 0.66921, 0.56501, 0.66691, 0.60225, 0.66568, 0.62217, 0.65776, 0.64086, 0.61724, 0.70681, 0.62076, 0.76176, 0.69149, 0.78405, 0.74426, 0.86019, 0.77889, 0.94044, 0.86299, 0.9816, 0.95698, 1, 0.99161, 0.93221, 1, 0.81491, 0.94214, 0.75729, 0.83166, 0.7717, 0.78219, 0.70996, 0.76334, 0.66803, 0.76234, 0.64556, 0.77534, 0.62934, 0.86434, 0.51204, 0.92634, 0.39723, 0.91533, 0.33608, 0.89834, 0.2949, 0.80334, 0.17385, 0.69634, 0.09023, 0.68334, 0.05654, 0.66134, 0, 0.61734, 0.01286, 0.56334, 0.07027, 0.48534, 0.11644, 0.36234, 0.1102, 0.25034, 0.01037, 0.22034, 0.05779, 0.19734, 0.13017, 0.12234, 0.22002, 0.06834, 0.36228, 0.07634, 0.45588, 0.09134, 0.49207, 0.21734, 0.62934, 0.2354, 0.64007, 0.23365, 0.65643, 0.21005, 0.71698, 0.18907, 0.74588, 0.08369, 0.70388, 0.00458, 0.7437, 0.0037, 0.86697, 0.04829, 0.94225, 0.16791, 0.94947, 0.2281, 0.91245, 0.27048, 0.81512, 0.34423, 0.74954, 0.39594, 0.71569, 0.40357, 0.67231, 0.38322, 0.64481, 0.34166, 0.61017, 0.31663, 0.58376, 0.31782, 0.56666, 0.36997, 0.52761, 0.40573, 0.48801, 0.4251, 0.40619, 0.48016, 0.24402, 0.55759, 0.21589, 0.6948, 0.26971, 0.7791, 0.44339, 0.74035, 0.36354, 0.71343, 0.63909, 0.69676, 0.71493, 0.76145, 0.78709, 0.85359, 0.87883, 0.93102, 0.88984, 0.389, 0.23301, 0.25179, 0.26726, 0.19984, 0.36756, 0.27433, 0.61952, 0.22319, 0.44656, 0.29981, 0.68924, 0.23316, 0.77731, 0.14005, 0.84214, 0.0538, 0.82134], "triangles": [77, 37, 36, 37, 39, 38, 68, 36, 35, 32, 35, 34, 34, 33, 32, 78, 37, 77, 37, 40, 39, 78, 40, 37, 41, 40, 78, 79, 41, 78, 42, 41, 79, 0, 77, 67, 66, 77, 0, 65, 81, 79, 43, 42, 79, 66, 78, 77, 65, 79, 66, 78, 66, 79, 67, 77, 36, 44, 43, 79, 81, 44, 79, 64, 81, 65, 63, 81, 64, 63, 80, 81, 62, 80, 63, 45, 44, 81, 45, 81, 80, 46, 45, 80, 61, 82, 80, 61, 80, 62, 82, 61, 60, 47, 46, 80, 80, 82, 47, 58, 60, 59, 57, 82, 60, 58, 57, 60, 82, 83, 48, 49, 48, 83, 47, 82, 48, 56, 83, 82, 56, 82, 57, 84, 49, 83, 55, 83, 56, 85, 51, 50, 84, 85, 50, 49, 84, 50, 52, 51, 85, 55, 84, 83, 53, 52, 85, 53, 85, 84, 54, 84, 55, 53, 84, 54, 75, 21, 20, 76, 75, 20, 76, 20, 19, 18, 76, 19, 15, 14, 75, 16, 75, 76, 15, 75, 16, 17, 76, 18, 16, 76, 17, 22, 73, 23, 74, 22, 21, 22, 74, 73, 13, 73, 74, 14, 13, 74, 74, 21, 75, 75, 14, 74, 70, 28, 27, 26, 70, 27, 6, 5, 71, 70, 6, 71, 7, 6, 70, 70, 72, 7, 25, 70, 26, 72, 8, 7, 72, 9, 8, 72, 70, 25, 24, 72, 25, 72, 10, 9, 73, 10, 72, 11, 10, 73, 12, 11, 73, 13, 12, 73, 72, 23, 73, 23, 72, 24, 68, 67, 36, 69, 31, 30, 35, 32, 31, 31, 68, 35, 69, 68, 31, 71, 69, 30, 71, 30, 29, 2, 67, 68, 3, 2, 68, 3, 68, 69, 3, 69, 71, 1, 67, 2, 0, 67, 1, 70, 71, 29, 70, 29, 28, 4, 3, 71, 5, 4, 71], "vertices": [4, 14, -10.17, -29.29, 0.34495, 15, 32.93, -49.83, 0.00422, 19, 28.47, 34.38, 0.18652, 18, 1.87, 30.06, 0.46432, 4, 14, -4.39, -25.03, 0.54522, 15, 28.27, -44.36, 0.01711, 19, 32.34, 40.43, 0.08904, 18, -4, 34.22, 0.34863, 4, 14, -2.35, -20.77, 0.6846, 15, 23.87, -42.63, 0.043, 19, 32.71, 45.14, 0.04634, 18, -8.68, 34.81, 0.22605, 4, 14, 2.09, -20.98, 0.77341, 15, 23.77, -38.19, 0.10928, 19, 36.92, 46.54, 0.0185, 18, -9.88, 39.09, 0.09881, 4, 14, 7.39, -27.64, 0.73065, 15, 30.03, -32.42, 0.23999, 19, 44.27, 42.22, 0.00456, 18, -5.22, 46.22, 0.02481, 4, 14, 24.37, -29.16, 0.35527, 15, 30.34, -15.38, 0.64448, 19, 60.66, 46.9, 5e-05, 18, -9.12, 62.81, 0.0002, 2, 14, 27.33, -28.78, 0.23602, 15, 29.74, -12.45, 0.76398, 2, 14, 29.29, -30.74, 0.12362, 15, 31.56, -10.36, 0.87638, 2, 14, 31.12, -34.88, 0.03374, 15, 35.56, -8.24, 0.96626, 2, 14, 32.1, -37.09, 0.01252, 15, 37.69, -7.11, 0.98748, 2, 14, 32.11, -39.65, 0.00372, 15, 40.24, -6.91, 0.99628, 1, 15, 50.26, -7.81, 1, 2, 15, 55.59, -3.81, 0.98278, 16, 0.15, -13.99, 0.01722, 2, 15, 52.16, 6.66, 0.29372, 16, 9.08, -7.53, 0.70628, 3, 15, 55.68, 18.32, 0.00084, 16, 21.25, -7.34, 0.93244, 17, -7.63, -3.58, 0.06672, 2, 16, 32.06, -9.56, 0.26066, 17, -0.1, -11.64, 0.73934, 1, 17, 13.44, -13.22, 1, 1, 17, 27.74, -11.76, 1, 1, 17, 30.7, -2.5, 1, 1, 17, 28.31, 11.55, 1, 2, 16, 30.89, 23.57, 0.00178, 17, 18.09, 16.07, 0.99822, 2, 16, 21.52, 9.66, 0.51582, 17, 2.4, 10.13, 0.48418, 3, 15, 37.26, 13.44, 0.02834, 16, 10.99, 8.72, 0.96319, 17, -6.73, 15.45, 0.00847, 2, 15, 34.49, 8.32, 0.37663, 16, 5.28, 9.8, 0.62337, 2, 15, 32.28, 6.74, 0.7003, 16, 3.1, 11.43, 0.2997, 2, 15, 29.57, 7.35, 0.88657, 16, 2.85, 14.19, 0.11343, 1, 15, 10.39, 11.11, 1, 2, 14, 53.9, 5.51, 0.38652, 15, -6.36, 11.59, 0.61348, 2, 14, 48.93, 11.25, 0.70061, 15, -11.72, 6.23, 0.29939, 2, 14, 44.31, 14.42, 0.88286, 15, -14.56, 1.4, 0.11714, 1, 14, 24.74, 20.54, 1, 1, 14, 5.72, 21.81, 1, 1, 14, 2.07, 24.47, 1, 1, 2, 27.03, 16.13, 1, 1, 2, 20.33, 14.83, 1, 1, 2, 11.92, 8.21, 1, 1, 2, -0.07, 3.08, 1, 1, 2, -18.6, 4.54, 1, 1, 2, -35.03, 17.27, 1, 1, 18, -2.15, -23.18, 1, 1, 18, 6.4, -19.21, 1, 1, 18, 22.1, -19.1, 1, 2, 19, -11.96, -5.57, 0.27135, 18, 39.85, -12.21, 0.72865, 2, 19, -2.59, -12.04, 0.81656, 18, 46.76, -3.16, 0.18344, 2, 19, 2.21, -13.18, 0.94557, 18, 48.13, 1.58, 0.05443, 2, 20, 7.85, -16.59, 0.10165, 19, 27.21, -9.6, 0.89835, 2, 20, 6.94, -13.71, 0.22258, 19, 29.97, -8.38, 0.77742, 3, 21, -4.55, -23.27, 0.0017, 20, 8.57, -12.55, 0.4229, 19, 31.3, -9.87, 0.5754, 3, 21, -0.99, -15.94, 0.08989, 20, 16.35, -10.15, 0.80393, 19, 34.53, -17.35, 0.10618, 3, 21, 2.17, -12.44, 0.38426, 20, 21.07, -10.08, 0.59988, 19, 35.11, -22.03, 0.01586, 2, 21, 18.09, -17.51, 0.97482, 20, 28.23, -25.17, 0.02518, 1, 21, 30.03, -12.69, 1, 1, 21, 30.16, 2.23, 1, 1, 21, 23.42, 11.33, 1, 2, 21, 5.36, 12.2, 0.96316, 20, 41.24, 4.41, 0.03684, 2, 21, -3.73, 7.71, 0.56428, 20, 31.76, 7.99, 0.43572, 1, 20, 18.79, 4.62, 1, 2, 20, 5.39, 7.34, 0.99467, 19, 50.73, -4.57, 0.00533, 2, 20, -2.94, 10.24, 0.73239, 19, 52.72, 4.02, 0.26761, 2, 20, -7.56, 7.5, 0.58413, 19, 49.5, 8.32, 0.41587, 2, 20, -7.89, 2.98, 0.40664, 19, 44.97, 8.16, 0.59336, 3, 14, -11.69, -58.96, 0.00028, 19, 37.7, 6.14, 0.9976, 18, 30.51, 37.95, 0.00213, 3, 14, -16.54, -57.93, 0.00331, 19, 32.81, 5.36, 0.97094, 18, 31.05, 33.03, 0.02575, 3, 14, -17.36, -56.02, 0.00924, 19, 31.36, 6.85, 0.92007, 18, 29.5, 31.65, 0.07069, 3, 14, -12.65, -48.13, 0.04779, 19, 32.92, 15.9, 0.68269, 18, 20.53, 33.63, 0.26952, 3, 14, -10.16, -41.36, 0.0893, 19, 32.81, 23.12, 0.53548, 18, 13.32, 33.86, 0.37522, 4, 14, -12.27, -31.25, 0.24649, 15, 35.04, -51.78, 0.00155, 19, 27.21, 31.79, 0.26097, 18, 4.39, 28.69, 0.49099, 4, 14, -14.23, -10.03, 0.60415, 15, 14.01, -55.25, 0.00155, 19, 17.76, 50.89, 0.02377, 18, -15.14, 20.15, 0.37053, 4, 14, -5.54, -1.5, 0.91683, 15, 4.88, -47.19, 0.00066, 19, 22.81, 61.98, 0.00283, 18, -25.97, 25.72, 0.07968, 1, 14, 15.79, 2.57, 1, 2, 14, 36.95, -9.93, 0.21994, 15, 10.25, -4.21, 0.78006, 2, 14, 27.22, -4.18, 0.89043, 15, 5.22, -14.32, 0.10957, 1, 15, 35.57, 0.08, 1, 2, 15, 44.67, 2.86, 0.42304, 16, 3.17, -1.55, 0.57696, 2, 16, 16.14, 0.33, 0.99934, 17, -7.38, 5.63, 0.00066, 1, 17, 8.9, -1.56, 1, 1, 17, 20.55, 0.13, 1, 3, 14, -26.99, -15.37, 0.21135, 19, 7.78, 41.33, 0.02686, 18, -6.06, 9.72, 0.76179, 1, 18, 11.83, -1.51, 1, 3, 14, -44.45, -43.23, 0.00014, 19, 1.48, 9.06, 0.0764, 18, 25.87, 1.91, 0.92346, 2, 20, 1.11, -11.12, 0.06467, 19, 31.92, -2.3, 0.93533, 3, 14, -36.82, -49.98, 0.00167, 19, 11.02, 5.5, 0.76528, 18, 29.88, 11.27, 0.23305, 2, 20, 4.64, -2.55, 0.81288, 19, 40.83, -4.89, 0.18712, 3, 21, -4.49, -8.64, 0.04659, 20, 19.3, -2.62, 0.9464, 19, 42.34, -19.47, 0.00701, 2, 21, 9.57, -0.79, 0.99231, 20, 34.63, -7.53, 0.00769, 1, 21, 22.59, -3.3, 1], "hull": 67}}, "langyao_012": {"langyao_012": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [20.79, 8.53, -10.82, -0.97, -15.42, 14.35, 16.18, 23.85], "hull": 4}}, "langyao_06": {"langyao_06": {"type": "mesh", "uvs": [0.14634, 0, 0.25969, 0.10292, 0.38076, 0.15643, 0.57139, 0.46224, 0.58685, 0.5234, 0.62807, 0.53869, 0.83158, 0.40108, 0.97584, 0.49664, 1, 0.79098, 0.92689, 0.96682, 0.78263, 1, 0.66671, 1, 0.61261, 0.8789, 0.56624, 0.85214, 0.42971, 0.77569, 0.2262, 0.81774, 0.07163, 0.726, 0, 0.5234, 0.01496, 0.14497, 0.22105, 0.39726, 0.592, 0.66866, 0.80839, 0.69159], "triangles": [20, 4, 5, 20, 19, 4, 21, 5, 6, 21, 6, 7, 20, 5, 21, 21, 7, 8, 13, 14, 20, 12, 20, 21, 13, 20, 12, 9, 21, 8, 10, 11, 12, 12, 21, 10, 9, 10, 21, 1, 18, 0, 19, 1, 2, 19, 18, 1, 19, 2, 3, 17, 18, 19, 19, 3, 4, 16, 17, 19, 14, 19, 20, 15, 16, 19, 14, 15, 19], "vertices": [1, 28, -18.15, 17.28, 1, 1, 28, -5.94, 16.66, 1, 2, 28, 5.43, 19.07, 0.99996, 29, -18.72, 31.76, 4e-05, 2, 28, 29.9, 10.79, 0.667, 29, -1.18, 12.8, 0.333, 2, 28, 32.96, 8.14, 0.36688, 29, 0.24, 9.01, 0.63312, 2, 28, 36.75, 9.12, 0.04028, 29, 4.03, 8.06, 0.95972, 1, 29, 22.75, 16.59, 1, 1, 29, 36.03, 10.67, 1, 1, 29, 38.25, -7.58, 1, 1, 29, 31.52, -18.48, 1, 1, 29, 18.25, -20.54, 1, 1, 29, 7.59, -20.54, 1, 2, 28, 45.58, -10.09, 0.08251, 29, 2.61, -13.03, 0.91749, 2, 28, 41.04, -10.67, 0.37097, 29, -1.66, -11.37, 0.62903, 1, 28, 27.74, -12.51, 1, 1, 28, 12.54, -23.75, 1, 1, 28, -2.67, -25.55, 1, 1, 28, -14.46, -17.66, 1, 1, 28, -24.47, 3.61, 1, 1, 28, -0.34, -1.07, 1, 1, 29, 0.71, 0, 1, 1, 29, 20.62, -1.42, 1], "hull": 19}}, "langyao_07": {"langyao_07": {"type": "mesh", "uvs": [0.00842, 0.52437, 0.01677, 0.76971, 0.13781, 0.94748, 0.42581, 1, 0.69294, 0.9617, 0.80564, 0.73415, 0.90998, 0.45326, 0.97677, 0.19371, 0.97677, 0, 0.77642, 0.07282, 0.47172, 0.23993, 0.16285, 0.4106, 0.83903, 0.15104, 0.65955, 0.37148, 0.48842, 0.59904, 0.30894, 0.78748], "triangles": [12, 9, 8, 12, 8, 7, 12, 13, 10, 12, 10, 9, 6, 12, 7, 13, 12, 6, 14, 10, 13, 5, 13, 6, 11, 10, 14, 14, 13, 5, 0, 15, 1, 11, 15, 0, 11, 14, 15, 2, 1, 15, 4, 14, 5, 15, 14, 4, 3, 15, 4, 2, 15, 3], "vertices": [2, 47, 2.25, 8.97, 0.99399, 48, -6.36, 10.37, 0.00601, 1, 47, -2.22, 4.08, 1, 1, 47, -3.56, -1.3, 1, 1, 47, 0.2, -6.93, 1, 2, 47, 5.34, -10.47, 0.99411, 48, -7.05, -9.29, 0.00589, 2, 47, 11.47, -7.86, 0.72058, 48, -0.53, -7.91, 0.27942, 2, 47, 18.48, -4.09, 0.02945, 48, 7.07, -5.54, 0.97055, 1, 48, 13.69, -2.79, 1, 1, 48, 17.99, 0.2, 1, 1, 48, 13.74, 2.85, 1, 2, 47, 15.25, 7.06, 0.13814, 48, 6.03, 6.02, 0.86186, 2, 47, 6.94, 8.7, 0.88621, 48, -1.81, 9.22, 0.11379, 1, 48, 12.83, 0.47, 1, 2, 47, 15.88, 1.51, 0.00319, 48, 5.59, 0.45, 0.99681, 1, 47, 8.77, -0.16, 1, 1, 47, 2.27, -0.94, 1], "hull": 12}}, "langyao_08": {"langyao_08": {"type": "mesh", "uvs": [1, 0.48093, 0.99027, 0.35879, 0.89991, 0.19807, 0.76241, 0.09521, 0.71724, 0, 0.66224, 0.0695, 0.64849, 0.12093, 0.55616, 0.12736, 0.48152, 0.07593, 0.44027, 0.02021, 0.41277, 0.13593, 0.42456, 0.2195, 0.33224, 0.23879, 0.23992, 0.22379, 0.17509, 0.14236, 0.15349, 0.2645, 0.1692, 0.35879, 0.19081, 0.39521, 0.11813, 0.41021, 0.05331, 0.38878, 0, 0.34379, 0.01009, 0.46593, 0.06706, 0.5495, 0.10242, 0.66307, 0.14956, 0.78307, 0.25563, 0.85807, 0.32831, 0.8795, 0.25956, 0.96307, 0.34599, 1, 0.50902, 1, 0.68777, 0.96307, 0.80956, 0.87735, 0.90188, 0.73378, 0.96277, 0.60307, 0.71134, 0.09093, 0.76634, 0.22164, 0.89402, 0.38236, 0.47956, 0.17236, 0.59938, 0.29664, 0.73884, 0.41021, 0.85473, 0.57093, 0.20063, 0.26664, 0.30474, 0.33736, 0.46777, 0.41878, 0.60331, 0.56021, 0.69956, 0.74235, 0.05527, 0.44021, 0.14956, 0.50664, 0.32045, 0.56664, 0.45206, 0.6845, 0.56795, 0.8495, 0.41081, 0.93307], "triangles": [49, 51, 26, 27, 26, 51, 28, 27, 51, 29, 51, 50, 28, 51, 29, 46, 19, 18, 20, 19, 46, 21, 20, 46, 47, 18, 17, 46, 18, 47, 22, 46, 47, 21, 46, 22, 47, 17, 48, 23, 22, 47, 48, 23, 47, 24, 23, 48, 25, 24, 48, 25, 48, 49, 49, 48, 43, 45, 40, 32, 49, 44, 45, 50, 49, 45, 26, 25, 49, 31, 45, 32, 50, 51, 49, 30, 50, 45, 30, 45, 31, 29, 50, 30, 37, 10, 9, 8, 37, 9, 37, 8, 7, 11, 10, 37, 41, 15, 14, 13, 41, 14, 37, 7, 38, 42, 13, 12, 41, 13, 42, 16, 15, 41, 16, 41, 42, 17, 16, 42, 38, 43, 11, 38, 11, 37, 42, 12, 11, 43, 42, 11, 48, 42, 43, 17, 42, 48, 44, 38, 39, 43, 38, 44, 44, 39, 40, 49, 43, 44, 45, 44, 40, 34, 5, 4, 34, 4, 3, 6, 5, 34, 35, 3, 2, 34, 3, 35, 38, 7, 6, 6, 34, 35, 35, 38, 6, 39, 38, 35, 36, 35, 2, 36, 2, 1, 39, 35, 36, 36, 1, 0, 40, 39, 36, 40, 36, 0, 33, 40, 0, 32, 40, 33], "vertices": [2, 37, -3.98, -9.32, 0.9986, 41, -29.59, -30.54, 0.0014, 1, 37, 4.78, -12.85, 1, 2, 37, 19.25, -11.67, 0.76, 38, 0.54, -11.76, 0.24, 2, 37, 31.54, -4.95, 0.00596, 38, 11.46, -2.99, 0.99404, 1, 38, 19.58, -1.47, 1, 3, 38, 15.78, 4.5, 0.97068, 39, 23.71, -25.97, 0.01469, 40, -9.19, -24.31, 0.01463, 3, 38, 12.33, 6.75, 0.81952, 39, 22.52, -22.02, 0.0901, 40, -9.1, -20.19, 0.09038, 3, 38, 14.09, 14.32, 0.3893, 39, 28.76, -17.38, 0.1536, 40, -1.73, -17.71, 0.45711, 3, 38, 19.69, 19.18, 0.23806, 39, 36.17, -17.28, 0.02444, 40, 5.35, -19.92, 0.73749, 2, 38, 24.8, 21.26, 0.22519, 40, 9.8, -23.18, 0.77481, 3, 38, 16.93, 26.04, 0.20494, 39, 38.5, -10.26, 0.01625, 40, 9.73, -13.97, 0.77881, 3, 38, 10.49, 26.95, 0.09919, 39, 34.16, -5.4, 0.02079, 40, 7.12, -8.01, 0.88002, 2, 38, 11.3, 34.8, 0.0025, 40, 14.23, -4.58, 0.9975, 1, 40, 22.02, -3.69, 1, 1, 40, 28.9, -8.35, 1, 3, 40, 28.23, 1.21, 0.98879, 42, 37.62, -10.04, 0.0107, 43, 14.71, -14.86, 0.00052, 3, 40, 25.08, 7.88, 0.72871, 42, 31.02, -6.74, 0.22855, 43, 9.28, -9.86, 0.04274, 3, 40, 22.61, 10.12, 0.43491, 42, 27.69, -6.51, 0.40869, 43, 6.14, -8.72, 0.1564, 3, 40, 28.21, 12.81, 0.03572, 42, 30.43, -0.94, 0.08943, 43, 10.32, -4.12, 0.87485, 1, 43, 15.66, -2.16, 1, 1, 43, 21.32, -2.23, 1, 1, 43, 14.98, 4.77, 1, 2, 42, 24.42, 8.93, 0.00061, 43, 7.29, 7.03, 0.99939, 2, 42, 15.64, 11.8, 0.38083, 43, -0.35, 12.22, 0.61917, 4, 41, 35.64, 6.75, 0.01263, 42, 5.87, 14.18, 0.81661, 43, -9.07, 17.21, 0.16464, 44, 19.94, -18.25, 0.00613, 4, 41, 25.74, 10.59, 0.30066, 42, -4.09, 10.51, 0.53031, 43, -19.67, 16.46, 0.01926, 44, 13.54, -9.78, 0.14978, 4, 41, 19.43, 10.97, 0.30306, 42, -9.07, 6.62, 0.09338, 43, -25.53, 14.1, 0.00079, 44, 8.36, -6.14, 0.60277, 1, 44, 15.99, -2.07, 1, 1, 44, 10.14, 3.08, 1, 2, 41, 2.68, 16.97, 0.13554, 44, -2.73, 7.76, 0.86446, 4, 37, -25.23, 30.86, 0.01288, 39, -15.58, 30.55, 0.03598, 41, -11.44, 11.14, 0.77128, 44, -17.81, 10.22, 0.17986, 4, 37, -23.97, 18.75, 0.0874, 39, -20.56, 19.44, 0.19306, 41, -20.12, 2.6, 0.70124, 44, -29.68, 7.52, 0.0183, 3, 37, -17.62, 6.84, 0.36479, 39, -21.04, 5.95, 0.30455, 41, -25.47, -9.8, 0.33066, 3, 37, -10.95, -2.28, 0.82382, 39, -19.84, -5.28, 0.10463, 41, -28.43, -20.69, 0.07155, 3, 38, 13.01, 1.03, 0.99322, 39, 19.36, -26.83, 0.00351, 40, -13.61, -23.78, 0.00327, 1, 38, 2.04, -0.49, 1, 1, 37, 6.82, -4.81, 1, 3, 38, 12.63, 21.48, 0.21922, 39, 32.26, -10.96, 0.07904, 40, 3.59, -12.71, 0.70174, 4, 37, 23.9, 14.28, 0.05217, 38, 0.56, 14.6, 0.2362, 39, 18.61, -8.42, 0.64065, 40, -8.6, -6.05, 0.07097, 3, 37, 10.8, 7.78, 0.47633, 38, -11.19, 5.91, 0.00528, 39, 4.02, -7.48, 0.51839, 3, 37, -4.64, 4.7, 0.64975, 39, -10.89, -2.41, 0.27822, 41, -19.04, -21.27, 0.07204, 3, 40, 24.36, 0.35, 0.99584, 42, 35.11, -13.11, 0.00389, 43, 11.45, -17.12, 0.00027, 4, 39, 37.65, 7.69, 0.00089, 40, 14.51, 3.35, 0.88486, 41, 29.84, -29.51, 0.0014, 42, 25.51, -16.84, 0.11284, 4, 39, 22.75, 5.49, 0.53772, 40, -0.34, 5.88, 0.34019, 41, 15.16, -26.15, 0.04523, 42, 12.28, -24.04, 0.07686, 4, 39, 7.27, 8.41, 0.73705, 40, -14.15, 13.47, 0.00435, 41, 1.8, -17.79, 0.24438, 42, -3.27, -26.61, 0.01423, 4, 37, -10.52, 22.29, 0.04308, 39, -7.15, 15.76, 0.29585, 41, -8.96, -5.7, 0.65979, 44, -24.55, -5.41, 0.00129, 1, 43, 13.15, 0.91, 1, 1, 43, 3.74, 0.22, 1, 4, 39, 26.92, 21.78, 0.05208, 40, 8.68, 20.07, 0.11117, 41, 24.97, -12.49, 0.09688, 42, 10.59, -7.31, 0.73987, 4, 39, 12.71, 23.36, 0.09444, 40, -4.34, 25.99, 0.01505, 41, 12.3, -5.84, 0.80769, 42, -3.3, -10.7, 0.08282, 3, 37, -12.88, 35.88, 0.00022, 41, 0.19, 4.62, 0.80087, 44, -11.34, -1.43, 0.19891, 1, 44, 3.26, 0.1, 1], "hull": 34}}, "langyao_09": {"langyao_09": {"type": "mesh", "uvs": [0.06664, 0.89054, 0.29635, 0.99173, 0.65635, 0.98654, 0.89978, 0.87757, 1, 0.62589, 0.76606, 0.43389, 0.51921, 0.23671, 0.29978, 0, 0.07349, 0.23671, 0.03578, 0.52989, 0.02549, 0.77638, 0.27235, 0.21335, 0.35121, 0.52211, 0.51235, 0.7686], "triangles": [11, 8, 7, 11, 7, 6, 12, 11, 6, 8, 11, 12, 9, 8, 12, 12, 6, 5, 13, 12, 5, 13, 5, 4, 3, 13, 4, 12, 10, 9, 13, 10, 12, 13, 0, 10, 2, 13, 3, 1, 0, 13, 1, 13, 2], "vertices": [2, 45, 4.38, 12.69, 0.79649, 46, -12.87, 8.44, 0.20351, 2, 45, -1.92, 8.74, 0.9505, 46, -17.23, 2.4, 0.0495, 1, 45, -6.43, -0.28, 1, 1, 45, -6.02, -8.19, 1, 1, 45, 0.92, -15, 1, 2, 45, 10.25, -12.5, 0.91151, 46, 2.01, -12.72, 0.08849, 2, 45, 19.92, -9.76, 0.1812, 46, 9.95, -6.57, 0.8188, 1, 46, 19.27, -1.32, 1, 1, 46, 11.18, 5.85, 1, 2, 45, 16.6, 7.26, 0.06384, 46, 0.49, 7.98, 0.93616, 2, 45, 8.66, 11.75, 0.65455, 46, -8.55, 9.16, 0.34545, 1, 46, 11.49, 0.23, 1, 1, 45, 12.76, -0.69, 1, 1, 45, 2.58, -0.45, 1], "hull": 11}}, "zhuaji/qf_boss_fsf2_pg_bjzh_2": {"zhuaji/qf_boss_fsf2_pg_bjzh_00": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [175, -125, -175, -125, -175, 125, 175, 125], "hull": 4}, "zhuaji/qf_boss_fsf2_pg_bjzh_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [175, -125, -175, -125, -175, 125, 175, 125], "hull": 4}, "zhuaji/qf_boss_fsf2_pg_bjzh_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [175, -125, -175, -125, -175, 125, 175, 125], "hull": 4}, "zhuaji/qf_boss_fsf2_pg_bjzh_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [175, -125, -175, -125, -175, 125, 175, 125], "hull": 4}}, "zhuaji/qf_boss_fsf2_pg_bjzh_0": {"zhuaji/qf_boss_fsf2_pg_bjzh_00": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [175, -125, -175, -125, -175, 125, 175, 125], "hull": 4}, "zhuaji/qf_boss_fsf2_pg_bjzh_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [175, -125, -175, -125, -175, 125, 175, 125], "hull": 4}, "zhuaji/qf_boss_fsf2_pg_bjzh_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [175, -125, -175, -125, -175, 125, 175, 125], "hull": 4}, "zhuaji/qf_boss_fsf2_pg_bjzh_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [175, -125, -175, -125, -175, 125, 175, 125], "hull": 4}}, "sc1": {"sc1": {"type": "mesh", "uvs": [0, 0.2138, 0.19428, 0.2069, 0.28105, 0.18393, 0.2899, 0.08285, 0.3625, 0.00015, 0.51303, 0, 0.68657, 0.04609, 0.92563, 0.19771, 0.99646, 0.49177, 0.94865, 0.87541, 0.92386, 1, 0.73084, 1, 0.46344, 0.87082, 0.23678, 0.65258, 0.08626, 0.45271, 0.23678, 0.39758, 0.35542, 0.2965, 0.40323, 0.17244, 0.54136, 0.14258], "triangles": [18, 5, 6, 17, 4, 5, 17, 5, 18, 3, 4, 17, 2, 3, 17, 16, 2, 17, 15, 1, 2, 15, 2, 16, 14, 0, 1, 14, 1, 15, 13, 15, 16, 14, 15, 13, 18, 16, 17, 12, 16, 18, 13, 16, 12, 6, 7, 18, 18, 7, 8, 9, 11, 8, 8, 12, 18, 11, 12, 8, 10, 11, 9], "vertices": [3.92, 37.12, 6.6, 27.21, 8.49, 24.23, 8.02, 23.39, 12.19, 21.35, 14.99, 14.69, 16.64, 6.35, 15.9, -6.4, 7.18, -13.74, -6.8, -17.12, -11.51, -17.8, -15.09, -9.26, -15.65, 4.43, -12.41, 17.58, -7.27, 29.51, -3.71, 21.23, 1.94, 17.43, 7.06, 17.09, 10.65, 11.4], "hull": 15}}, "sc2": {"sc2": {"x": 6.37, "y": 5.93, "rotation": 136.31, "width": 66, "height": 61}}, "sc3": {"sc3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-5.24, -36.25, -30, 22.77, 18.88, 43.27, 43.63, -15.75], "hull": 4}}, "langyao_011": {"langyao_011": {"type": "mesh", "uvs": [0.02143, 0.23402, 0.14233, 0.12204, 0.31748, 0.07269, 0.48798, 0.04612, 0.68793, 0.00057, 0.75148, 0.10685, 0.89717, 0.14102, 1, 0.23971, 0.97313, 0.36118, 0.88788, 0.41053, 0.81968, 0.5263, 0.63678, 0.52251, 0.46938, 0.47506, 0.49701, 0.53983, 0.52363, 0.60222, 0.51123, 0.79961, 0.48643, 0.92108, 0.39188, 1, 0.26943, 1, 0.15163, 0.88312, 0.04003, 0.82239, 0.01833, 0.62879, 0.00748, 0.42192, 0.71427, 0.38775, 0.57012, 0.27767, 0.46628, 0.19606, 0.30508, 0.23022, 0.22448, 0.38585, 0.21673, 0.58134, 0.26323, 0.75975, 0.36552, 0.85465, 0.11443, 0.31753, 0.10978, 0.50353, 0.12373, 0.61551, 0.15163, 0.77304, 0.24153, 0.85085, 0.35468, 0.92867, 0.83982, 0.27767, 0.69722, 0.19606, 0.54222, 0.13343, 0.40893, 0.14102, 0.26633, 0.16759, 0.18418, 0.22453, 0.41513, 0.52061, 0.30663, 0.42951, 0.25853, 0.3201, 0.26828, 0.44635, 0.40988, 0.57639], "triangles": [26, 45, 42, 18, 36, 17, 17, 36, 16, 19, 35, 18, 18, 35, 36, 36, 30, 16, 36, 35, 30, 16, 30, 15, 20, 34, 19, 19, 34, 35, 35, 29, 30, 15, 30, 47, 35, 34, 29, 34, 21, 33, 34, 20, 21, 30, 29, 47, 15, 47, 14, 14, 47, 13, 34, 28, 29, 34, 33, 28, 29, 28, 47, 21, 32, 33, 21, 22, 32, 33, 32, 28, 28, 46, 47, 27, 28, 32, 27, 46, 28, 46, 44, 47, 47, 43, 13, 32, 31, 27, 32, 22, 31, 44, 46, 45, 46, 27, 45, 22, 0, 31, 45, 27, 42, 27, 31, 42, 31, 0, 42, 0, 1, 42, 42, 1, 41, 41, 1, 2, 43, 12, 13, 45, 26, 44, 11, 23, 10, 10, 23, 9, 12, 24, 11, 11, 24, 23, 44, 25, 12, 12, 25, 24, 44, 26, 25, 23, 37, 9, 9, 37, 8, 24, 38, 23, 23, 38, 37, 8, 37, 7, 38, 5, 37, 37, 6, 7, 37, 5, 6, 25, 39, 24, 24, 39, 38, 26, 40, 25, 26, 2, 40, 26, 41, 2, 5, 38, 4, 39, 25, 3, 25, 40, 3, 40, 2, 3, 38, 39, 4, 39, 3, 4, 43, 44, 12, 26, 42, 41, 47, 44, 43], "vertices": [3, 7, -20.79, -17.23, 0.08802, 6, -6.36, -11.74, 0.70398, 12, 5.48, 9.21, 0.208, 3, 7, -16.24, -8.29, 0.23908, 6, -11.76, -3.28, 0.55292, 12, -3.23, 4.22, 0.208, 2, 7, -8.48, 0.22, 0.712, 6, -15.16, 7.72, 0.288, 2, 7, -0.42, 7.1, 0.9994, 6, -17, 18.16, 0.0006, 1, 7, 10.43, 12.69, 1, 1, 7, 15.58, 8.8, 1, 1, 7, 24.43, 9.72, 1, 1, 7, 31.73, 6.86, 1, 1, 7, 31.9, 0.7, 1, 1, 7, 27.7, -3.09, 1, 2, 7, 25.41, -9.7, 0.99994, 6, 10.57, 31.91, 6e-05, 2, 7, 15.09, -13.17, 0.95423, 6, 8.33, 21.26, 0.04577, 2, 7, 5.12, -14.45, 0.896, 6, 4.38, 12.01, 0.104, 2, 7, 11.53, -24.72, 0.16862, 6, 16.48, 12.33, 0.83138, 2, 7, 14.39, -28.08, 0.08477, 6, 20.83, 13.09, 0.91523, 2, 7, 16.9, -38.41, 0.00379, 6, 31.01, 10.02, 0.99621, 1, 6, 36.48, 7.16, 1, 1, 6, 38.89, 0.73, 1, 1, 6, 37.15, -6.41, 1, 1, 6, 29.91, -11.92, 1, 1, 6, 25.43, -17.71, 1, 1, 6, 15.9, -16.73, 1, 3, 7, -18.55, -27.07, 0.01469, 6, 3.26, -14.79, 0.77731, 12, 8.99, 18.67, 0.208, 2, 7, 17.43, -5.07, 0.99487, 6, 2.53, 27.37, 0.00513, 2, 7, 7.67, -2.54, 0.98109, 6, -4.6, 20.24, 0.01891, 2, 7, 0.47, -0.35, 0.99629, 6, -10.14, 15.15, 0.00371, 2, 7, -7.77, -5.8, 0.896, 6, -9.62, 5.28, 0.104, 2, 7, -5.9, -22.99, 0.09463, 6, 6.15, -1.82, 0.90537, 1, 6, 16.47, -4.61, 1, 1, 6, 25.62, -3.98, 1, 1, 6, 31.6, 0.88, 1, 3, 7, -14.23, -19.63, 0.0799, 6, -0.96, -7.3, 0.7121, 12, 1.3, 14.82, 0.208, 2, 7, -10.33, -31.47, 0.00219, 6, 11.22, -9.94, 0.99781, 1, 6, 16.77, -10.43, 1, 1, 6, 24.67, -10.64, 1, 1, 6, 29.65, -6.3, 1, 1, 6, 34.96, -0.61, 1, 1, 7, 23.06, 2.31, 1, 1, 7, 13.72, 3.68, 1, 1, 7, 3.93, 3.94, 1, 2, 7, -3.25, 0.55, 0.93093, 6, -12.8, 12.39, 0.06907, 2, 7, -9.36, -6.52, 0.61339, 6, -9.8, 3.54, 0.38661, 2, 7, -11.5, -14.12, 0.28265, 6, -4.33, -2.15, 0.71735, 2, 7, 2.65, -17.52, 0.896, 6, 5.77, 8.32, 0.104, 2, 7, -4.87, -15.12, 0.896, 6, -0.11, 3.05, 0.104, 2, 7, -5.78, -17.52, 0.26412, 6, 1.49, 1.06, 0.73588, 2, 7, -2.22, -25.68, 0.03718, 6, 10.33, -0.01, 0.96282, 2, 7, 7.42, -28.69, 0.0983, 6, 17.82, 6.78, 0.9017], "hull": 23}}, "ci/moxie_skill_ci_30": {"ci/moxie_skill_ci_30": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [153, -61, -153, -61, -153, 61, 153, 61], "hull": 4}, "ci/moxie_skill_ci_32": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [153, -61, -153, -61, -153, 61, 153, 61], "hull": 4}, "ci/moxie_skill_ci_34": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [153, -61, -153, -61, -153, 61, 153, 61], "hull": 4}, "ci/moxie_skill_ci_36": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [153, -61, -153, -61, -153, 61, 153, 61], "hull": 4}, "ci/moxie_skill_ci_38": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [153, -61, -153, -61, -153, 61, 153, 61], "hull": 4}}, "ci/moxie_skill_ci_31": {"ci/moxie_skill_ci_30": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [153, -61, -153, -61, -153, 61, 153, 61], "hull": 4}, "ci/moxie_skill_ci_32": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [153, -61, -153, -61, -153, 61, 153, 61], "hull": 4}, "ci/moxie_skill_ci_34": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [153, -61, -153, -61, -153, 61, 153, 61], "hull": 4}, "ci/moxie_skill_ci_36": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [153, -61, -153, -61, -153, 61, 153, 61], "hull": 4}, "ci/moxie_skill_ci_38": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [153, -61, -153, -61, -153, 61, 153, 61], "hull": 4}}, "zhuaji/qf_boss_fsf2_pg_bjzh_1": {"zhuaji/qf_boss_fsf2_pg_bjzh_00": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [175, -125, -175, -125, -175, 125, 175, 125], "hull": 4}, "zhuaji/qf_boss_fsf2_pg_bjzh_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [175, -125, -175, -125, -175, 125, 175, 125], "hull": 4}, "zhuaji/qf_boss_fsf2_pg_bjzh_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [175, -125, -175, -125, -175, 125, 175, 125], "hull": 4}, "zhuaji/qf_boss_fsf2_pg_bjzh_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [175, -125, -175, -125, -175, 125, 175, 125], "hull": 4}}, "langyao_010": {"langyao_010": {"type": "mesh", "uvs": [0.87778, 0.75032, 0.94705, 0.77937, 1, 0.75479, 1, 0.63636, 1, 0.47547, 0.96492, 0.36821, 0.90459, 0.40173, 0.85096, 0.27659, 0.76158, 0.16487, 0.6789, 0.0375, 0.62527, 0, 0.53812, 0.07101, 0.50684, 0.2185, 0.51801, 0.2833, 0.42192, 0.2319, 0.30796, 0.24755, 0.17389, 0.3414, 0.06886, 0.36598, 0, 0.3481, 0.0063, 0.48217, 0.04205, 0.54921, 0.02864, 0.66764, 0.11579, 0.77267, 0.03311, 0.87099, 0.02864, 0.96037, 0.15378, 1, 0.31243, 1, 0.45768, 1, 0.61186, 0.97601, 0.74147, 0.87099, 0.81297, 0.80172, 0.63547, 0.0897, 0.65226, 0.20723, 0.7127, 0.34323, 0.81008, 0.45908, 0.84534, 0.59508, 0.85374, 0.67903, 0.7211, 0.74955, 0.59181, 0.63034, 0.45078, 0.47083, 0.34164, 0.3533, 0.05957, 0.43557, 0.18717, 0.47587, 0.32149, 0.59676, 0.43902, 0.75626, 0.56159, 0.84693, 0.13008, 0.89898, 0.26608, 0.85197, 0.39537, 0.86708, 0.52633, 0.92417, 0.16534, 0.6337, 0.29966, 0.72604, 0.41887, 0.81671, 0.92258, 0.48762, 0.92593, 0.59004, 0.94105, 0.69078], "triangles": [45, 44, 38, 27, 49, 28, 26, 48, 27, 27, 48, 49, 49, 45, 28, 28, 45, 29, 49, 52, 45, 53, 6, 4, 38, 39, 33, 38, 33, 34, 45, 37, 29, 29, 37, 30, 45, 38, 37, 0, 30, 36, 2, 1, 55, 1, 0, 55, 55, 3, 2, 30, 37, 36, 0, 36, 55, 36, 37, 35, 36, 54, 55, 55, 54, 3, 37, 38, 35, 36, 35, 54, 54, 4, 3, 38, 34, 35, 35, 53, 54, 35, 34, 53, 54, 53, 4, 34, 6, 53, 6, 5, 4, 6, 34, 7, 25, 47, 26, 26, 47, 48, 24, 46, 25, 25, 46, 47, 24, 23, 46, 23, 22, 46, 46, 22, 47, 47, 22, 51, 22, 50, 51, 48, 52, 49, 48, 47, 52, 47, 51, 52, 52, 44, 45, 52, 51, 44, 51, 43, 44, 22, 21, 50, 21, 20, 50, 50, 42, 43, 50, 20, 42, 20, 41, 42, 20, 19, 41, 19, 18, 41, 42, 41, 16, 41, 17, 16, 42, 16, 40, 41, 18, 17, 16, 15, 40, 44, 43, 38, 51, 50, 43, 43, 39, 38, 43, 40, 39, 43, 42, 40, 39, 40, 13, 40, 15, 14, 12, 11, 31, 31, 9, 8, 11, 10, 31, 31, 10, 9, 33, 39, 13, 13, 40, 14, 34, 33, 7, 13, 32, 33, 33, 8, 7, 33, 32, 8, 32, 12, 31, 32, 13, 12, 32, 31, 8], "vertices": [2, 10, -37.38, -3.81, 0.0011, 7, 14.4, 5.8, 0.9989, 1, 7, 20.45, 5.16, 1, 2, 8, -43.71, -2.47, 0.008, 7, 23.99, 8.3, 0.992, 1, 7, 21.23, 17.49, 1, 1, 7, 17.48, 29.97, 1, 1, 7, 12.25, 37.47, 1, 2, 10, -28.95, -30.84, 0.01201, 7, 8.36, 33.47, 0.98799, 3, 8, -4.11, -11.32, 0.92429, 9, -9.81, -16.08, 0.00179, 7, 1.28, 41.92, 0.07392, 2, 8, 7.34, -9.56, 0.67435, 9, 0.18, -10.21, 0.32565, 2, 8, 19.63, -8.9, 0.0009, 9, 11.35, -5.06, 0.9991, 1, 9, 14.98, -1.19, 1, 4, 8, 22.96, 2.34, 0.00464, 9, 10.29, 6.62, 0.9951, 10, 8.54, -44.75, 0.0002, 11, 1.55, -45.31, 6e-05, 4, 8, 13.87, 10.5, 0.56844, 9, -1.17, 10.83, 0.33642, 10, 6.48, -32.71, 0.07431, 11, -2.81, -33.9, 0.02083, 5, 8, 8.87, 12.33, 0.6346, 9, -6.49, 10.69, 0.04669, 10, 3.69, -28.17, 0.23844, 11, -6.42, -29.99, 0.07805, 7, -24.39, 33.64, 0.00222, 4, 8, 16.36, 17, 0.39442, 9, -1.26, 17.79, 0, 10, 12.46, -29.16, 0.3678, 11, 2.37, -29.25, 0.23778, 3, 8, 19.86, 25.63, 0.26314, 10, 20.57, -24.57, 0.34321, 11, 9.44, -23.17, 0.39365, 3, 8, 18.69, 38.84, 0.06518, 10, 27.85, -13.48, 0.1153, 11, 14.42, -10.89, 0.81952, 3, 8, 21.2, 47.2, 0.00302, 10, 35.01, -8.49, 0.0027, 11, 20.48, -4.6, 0.99428, 1, 11, 25.95, -2.78, 1, 2, 11, 19.63, 6.06, 0.98884, 13, 6.16, -33.65, 0.01116, 2, 11, 14.24, 9.04, 0.91363, 13, 6.2, -27.49, 0.08637, 3, 10, 29, 15.42, 0.00905, 11, 9.95, 17.69, 0.6671, 13, 11.73, -19.58, 0.32385, 4, 10, 19.3, 20.71, 0.02848, 11, -0.6, 20.99, 0.18654, 12, 23.12, -6.55, 0.00373, 13, 9.58, -8.73, 0.78125, 2, 11, 0.69, 31.32, 0.00169, 13, 19.27, -4.93, 0.99831, 1, 13, 23.04, 1.26, 1, 3, 12, 25.41, 12.17, 0.00927, 13, 15.72, 9.1, 0.97457, 6, -2.45, -31.51, 0.01616, 3, 12, 13.74, 21.74, 0.16612, 13, 6.3, 20.89, 0.31608, 6, 6.55, -19.39, 0.5178, 1, 6, 15.06, -8.32, 1, 1, 6, 16.14, 4.28, 1, 2, 7, 7.44, -8.3, 0.85255, 6, 0.25, 17.12, 0.14745, 2, 8, -39.45, 12.56, 0.00588, 7, 10.57, 0.3, 0.99412, 2, 8, 17.71, -3.74, 0.00061, 9, 7.67, -0.97, 0.99939, 2, 8, 8.78, -0.17, 0.99266, 9, -1.95, -0.96, 0.00734, 4, 8, -3.21, 1.08, 0.91663, 10, -12.75, -29.49, 0.03355, 11, -22.3, -34.48, 0.00066, 7, -7.89, 33.53, 0.04917, 3, 8, -15.27, -1.08, 0.12062, 10, -23.55, -23.69, 0.00933, 7, 2.36, 26.81, 0.87004, 1, 7, 8.27, 17.09, 1, 2, 10, -33.43, -8.45, 0.00136, 7, 10.88, 10.77, 0.99864, 3, 8, -32.07, 16.9, 0.01549, 10, -25.56, 0.83, 0.01134, 7, 2.23, 2.21, 0.97317, 4, 8, -18.48, 21.17, 0.01577, 10, -12.26, -4.26, 0.05227, 12, -17.12, -6.67, 0.00618, 7, -10.58, 8.44, 0.92578, 4, 8, -1.58, 24.62, 0.16316, 10, 3.13, -12.04, 0.45705, 11, -10.1, -14.27, 0.04523, 7, -25.24, 17.52, 0.33455, 3, 8, 11.08, 27.54, 0.23817, 10, 14.87, -17.62, 0.40639, 11, 2.49, -17.46, 0.35544, 2, 11, 18.05, 0.54, 0.99942, 13, 0.56, -34.91, 0.00058, 3, 8, 8.71, 43.34, 0.00849, 10, 22.82, -3.76, 0.02656, 11, 7.6, -2.33, 0.96495, 3, 10, 9.09, 1.31, 0.9712, 12, 3.1, -15.49, 0.02442, 13, -11.86, -13.31, 0.00438, 4, 10, -4.71, 10.06, 0.0971, 12, -2.33, -0.08, 0.8409, 7, -19.35, -5.18, 0.03474, 6, -16.02, -4.38, 0.02727, 1, 6, 5.02, 2.8, 1, 3, 12, 24.91, 3.6, 2e-05, 13, 13.45, 0.82, 0.99926, 6, -11.04, -31.42, 0.00072, 3, 12, 13.29, 3.33, 0.1977, 13, 2.03, 2.97, 0.77985, 6, -11.87, -19.83, 0.02246, 4, 12, 3.89, 10.09, 0.67374, 13, -5.76, 11.54, 0.08108, 7, -18.98, -17.09, 0.00093, 6, -5.56, -10.11, 0.24425, 1, 6, 10.42, -1.46, 1, 4, 10, 19.73, 8.77, 0.13381, 11, 2.14, 9.36, 0.59431, 12, 16.08, -16.21, 0.03922, 13, 0.69, -16.71, 0.23266, 4, 10, 6.86, 11.69, 0.25559, 11, -11.06, 9.73, 0.02244, 12, 7.77, -5.95, 0.6397, 13, -5.31, -4.95, 0.08226, 4, 12, 0.76, 5.41, 0.8456, 13, -9.79, 7.62, 0.01074, 7, -18.93, -11.47, 0.00971, 6, -10.38, -7.21, 0.13396, 2, 10, -32.88, -24.92, 0.01218, 7, 11.75, 27.22, 0.98782, 2, 10, -36.2, -17.31, 0.00015, 7, 14.4, 19.36, 0.99985, 2, 10, -40.35, -10.18, 0.00015, 7, 17.92, 11.89, 0.99985], "hull": 31}}, "zhuaji/qf_boss_fsf2_pg_bjzh_00": {"zhuaji/qf_boss_fsf2_pg_bjzh_00": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [175, -125, -175, -125, -175, 125, 175, 125], "hull": 4}, "zhuaji/qf_boss_fsf2_pg_bjzh_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [175, -125, -175, -125, -175, 125, 175, 125], "hull": 4}, "zhuaji/qf_boss_fsf2_pg_bjzh_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [175, -125, -175, -125, -175, 125, 175, 125], "hull": 4}, "zhuaji/qf_boss_fsf2_pg_bjzh_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [175, -125, -175, -125, -175, 125, 175, 125], "hull": 4}}}}], "events": {"atk": {}}, "animations": {"boss_attack1": {"slots": {"zhuaji/qf_boss_fsf2_pg_bjzh_1": {"attachment": [{"time": 0.2667, "name": "zhuaji/qf_boss_fsf2_pg_bjzh_00"}, {"time": 0.3, "name": "zhuaji/qf_boss_fsf2_pg_bjzh_02"}, {"time": 0.3333, "name": "zhuaji/qf_boss_fsf2_pg_bjzh_04"}, {"time": 0.3667, "name": "zhuaji/qf_boss_fsf2_pg_bjzh_06"}, {"time": 0.4, "name": null}]}, "zhuaji/qf_boss_fsf2_pg_bjzh_00": {"attachment": [{"time": 0.2, "name": "zhuaji/qf_boss_fsf2_pg_bjzh_00"}, {"time": 0.2333, "name": "zhuaji/qf_boss_fsf2_pg_bjzh_02"}, {"time": 0.2667, "name": "zhuaji/qf_boss_fsf2_pg_bjzh_04"}, {"time": 0.3, "name": "zhuaji/qf_boss_fsf2_pg_bjzh_06"}, {"time": 0.3333, "name": null}]}, "zhuaji/qf_boss_fsf2_pg_bjzh_0": {"attachment": [{"time": 0.2667, "name": "zhuaji/qf_boss_fsf2_pg_bjzh_00"}, {"time": 0.3, "name": "zhuaji/qf_boss_fsf2_pg_bjzh_02"}, {"time": 0.3333, "name": "zhuaji/qf_boss_fsf2_pg_bjzh_04"}, {"time": 0.3667, "name": "zhuaji/qf_boss_fsf2_pg_bjzh_06"}, {"time": 0.4, "name": null}]}, "zhuaji/qf_boss_fsf2_pg_bjzh_2": {"attachment": [{"time": 0.2, "name": "zhuaji/qf_boss_fsf2_pg_bjzh_00"}, {"time": 0.2333, "name": "zhuaji/qf_boss_fsf2_pg_bjzh_02"}, {"time": 0.2667, "name": "zhuaji/qf_boss_fsf2_pg_bjzh_04"}, {"time": 0.3, "name": "zhuaji/qf_boss_fsf2_pg_bjzh_06"}, {"time": 0.3333, "name": null}]}}, "bones": {"root": {"rotate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}]}, "bone44": {"rotate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}]}, "bone": {"rotate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -33.61, "y": -0.89, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 8.07, "y": 0.21, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 29.83, "y": 0.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 10.87, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -5.91, "curve": "stepped"}, {"time": 0.3333, "angle": -5.91, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 0.13, "y": -0.64, "curve": "stepped"}, {"time": 0.3333, "x": 0.13, "y": -0.64, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 10.87, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -5.91, "curve": "stepped"}, {"time": 0.3333, "angle": -5.91, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}]}, "bone4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 4.43, "curve": "stepped"}, {"time": 0.3333, "angle": 4.43, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}]}, "bone5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 33.46, "curve": "stepped"}, {"time": 0.2333, "angle": 33.46, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}]}, "bone6": {"rotate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}]}, "bone7": {"rotate": [{"angle": -0.88, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 9.13, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -11.34, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.5667, "angle": -0.88}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667}]}, "bone8": {"rotate": [{"angle": -4.56, "curve": 0.341, "c2": 0.36, "c3": 0.678, "c4": 0.7}, {"time": 0.0333, "angle": -2.54, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 9.13, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -11.34, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.5667, "angle": -4.56}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}]}, "bone9": {"rotate": [{"angle": 1.19, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": 9.13, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -11.34, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.5667, "angle": 1.19}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.5667}]}, "bone10": {"rotate": [{"angle": -0.83, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 9.13, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -11.34, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.5333, "angle": -2.54, "curve": 0.352, "c2": 0.41, "c3": 0.689, "c4": 0.76}, {"time": 0.5667, "angle": -0.83}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.5667}]}, "bone11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 9.13, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -11.34, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}]}, "bone12": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 9.13, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -11.34, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}]}, "bone13": {"rotate": [{"angle": 0.82, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 10.63, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 7.27, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 0.82}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}]}, "bone14": {"rotate": [{"angle": -1.49, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 10.14, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 6.48, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -1.49}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}]}, "bone15": {"rotate": [{"angle": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -19.64, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -12.87, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 1.1}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}]}, "bone16": {"rotate": [{"angle": 0.66, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -1.03, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -0.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 0.66}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}]}, "bone17": {"rotate": [{"angle": -1.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -10.67, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -3.79, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -1.08}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}]}, "bone18": {"rotate": [{"angle": 1.19, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 0.84, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 1.19}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}]}, "bone19": {"rotate": [{"angle": 0.69, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 10.63, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 4.94, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 0.69}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}]}, "bone20": {"rotate": [{"angle": -3.48, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -3.48, "curve": "stepped"}, {"time": 0.3333, "angle": -3.48, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -3.48}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}]}, "target1": {"rotate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": 0.32, "c2": 0.29, "c3": 0.661, "c4": 0.65}, {"time": 0.3, "x": 16.51, "y": -0.2, "curve": 0.378, "c2": 0.51, "c3": 0.748}, {"time": 0.5667}]}, "target2": {"rotate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}]}, "target3": {"rotate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -27.27, "y": -0.11, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -22.12, "y": -0.17, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 4.48, "y": -0.49, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5667}]}, "target4": {"rotate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}]}, "bone21": {"rotate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}]}, "bone22": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -27.81, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2333, "angle": 136.97, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2813, "angle": 69.74, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -36.71, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}]}, "bone23": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 66.57, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2333, "angle": -55.6, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2813, "angle": -55.9, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -54.26, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}]}, "bone24": {"rotate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}]}, "bone25": {"rotate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}]}, "bone26": {"rotate": [{"curve": "stepped"}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -58.65, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3, "angle": 47.2, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "angle": -23.43, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -122.65, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5667}]}, "bone27": {"rotate": [{"curve": "stepped"}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 107.77, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3, "angle": 26.97, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "angle": 30.61, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 35.41, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5667}]}, "bone28": {"rotate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}]}, "bone29": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 28.78, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -18.9, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}]}, "bone30": {"rotate": [{"angle": -4.24, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 28.78, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -18.9, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.5667, "angle": -4.24}], "translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5667}]}, "bone31": {"rotate": [{"angle": -11.23, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 28.78, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -18.9, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.5667, "angle": -11.23}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.5667}]}, "bone32": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 9.13, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -11.34, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}]}, "bone33": {"rotate": [{"angle": -2.54, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 9.13, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -11.34, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.5667, "angle": -2.54}], "translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5667}]}, "bone34": {"rotate": [{"angle": -0.88, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 9.13, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -11.34, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.5667, "angle": -0.88}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667}]}, "bone35": {"rotate": [{"angle": -4.56, "curve": 0.341, "c2": 0.36, "c3": 0.678, "c4": 0.7}, {"time": 0.0333, "angle": -2.54, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 9.13, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -11.34, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.5667, "angle": -4.56}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}]}, "bone36": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 9.13, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -11.34, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}]}, "bone37": {"rotate": [{"angle": -2.54, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 9.13, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -11.34, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.5667, "angle": -2.54}], "translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5667}]}, "bone38": {"rotate": [{"angle": -6.74, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 9.13, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -11.34, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.5667, "angle": -6.74}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.5667}]}, "bone39": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 9.13, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -11.34, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}]}, "bone40": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 9.13, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -11.34, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667}]}, "bone41": {"rotate": [{"angle": -2.54, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 9.13, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -11.34, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.5667, "angle": -2.54}], "translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5667}]}, "bone42": {"rotate": [{"angle": -2.54, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 9.13, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -11.34, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.5667, "angle": -2.54}], "translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5667}]}, "bone43": {"rotate": [{"angle": -6.7, "curve": 0.336, "c2": 0.34, "c3": 0.682, "c4": 0.71}, {"time": 0.0667, "angle": -2.54, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 9.13, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -11.34, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.5667, "angle": -6.7}], "translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.5667}]}}, "events": [{"time": 0.2333, "name": "atk"}]}, "boss_attack3": {"slots": {"ci/moxie_skill_ci_30": {"attachment": [{"time": 0.3333, "name": "ci/moxie_skill_ci_30"}, {"time": 0.3667, "name": "ci/moxie_skill_ci_32"}, {"time": 0.4, "name": "ci/moxie_skill_ci_34"}, {"time": 0.4333, "name": "ci/moxie_skill_ci_36"}, {"time": 0.4667, "name": "ci/moxie_skill_ci_38"}, {"time": 0.5, "name": null}]}, "ci/moxie_skill_ci_31": {"attachment": [{"time": 0.3333, "name": "ci/moxie_skill_ci_30"}, {"time": 0.3667, "name": "ci/moxie_skill_ci_32"}, {"time": 0.4, "name": "ci/moxie_skill_ci_34"}, {"time": 0.4333, "name": "ci/moxie_skill_ci_36"}, {"time": 0.4667, "name": "ci/moxie_skill_ci_38"}, {"time": 0.5, "name": null}]}}, "bones": {"root": {"rotate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "bone44": {"rotate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.2333, "curve": 0.679, "c2": 0.04, "c3": 0, "c4": 1.47}, {"time": 0.4333, "x": 283.88, "y": 3.41, "curve": 0.888, "c2": 0.04, "c3": 0.438, "c4": 0.96}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.2333}]}, "bone": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -15.49, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 40.76, "curve": "stepped"}, {"time": 0.3, "angle": 40.76, "curve": "stepped"}, {"time": 0.4333, "angle": 40.76, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 11.92, "y": -19.83, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -2.63, "y": 29.15, "curve": "stepped"}, {"time": 0.3, "x": -2.63, "y": 29.15, "curve": "stepped"}, {"time": 0.4333, "x": -2.63, "y": 29.15, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.4333}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -8.29, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -2.81, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "bone3": {"rotate": [{"angle": 0.94, "curve": 0.373, "c2": 0.62, "c3": 0.713}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -14.98, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 8.56, "curve": 0.243, "c3": 0.685, "c4": 0.73}, {"time": 0.6667, "angle": 0.94}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "bone4": {"rotate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "bone5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 42.02, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 28.71, "curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "bone6": {"rotate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "bone7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -22.19, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 17.57, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -13.98, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "bone8": {"rotate": [{"angle": -2.58, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -22.19, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 17.57, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -13.98, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.6667, "angle": -2.58}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "bone9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -22.19, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 17.57, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -13.98, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "bone10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -22.19, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 17.57, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -13.98, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "bone11": {"rotate": [{"angle": -2.58, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -22.19, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 17.57, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -13.98, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.6667, "angle": -2.58}], "translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "bone12": {"rotate": [{"angle": -6.94, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.0667, "angle": -2.58, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -22.19, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 17.57, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -13.98, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": -6.94}], "translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "bone13": {"rotate": [{"angle": 0.82, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 32.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 0.82}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "bone14": {"rotate": [{"angle": -1.49, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -41.96, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 67.73, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -1.49}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "bone15": {"rotate": [{"angle": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 25.14, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -72.25, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 1.1}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "bone16": {"rotate": [{"angle": 0.66, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 2.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 17.99, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 0.66}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "bone17": {"rotate": [{"angle": -1.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -13.44, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -28.52, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -1.08}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "bone18": {"rotate": [{"angle": 1.19, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 24.45, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -17.63, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 1.19}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "bone19": {"rotate": [{"angle": 0.69, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 5.28, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 6.19, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 0.69}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "bone20": {"rotate": [{"angle": -3.48, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -3.48, "curve": "stepped"}, {"time": 0.3, "angle": -3.48, "curve": "stepped"}, {"time": 0.6667, "angle": -3.48}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "target1": {"rotate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 12.47, "y": 45.38, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 68.84, "y": 104.63, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 71.2, "y": 109.92, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.4333}]}, "target2": {"rotate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 4.67, "y": 39.46, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 0.4333, "x": 3.63, "y": 40.2, "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "target3": {"rotate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "target4": {"rotate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "bone21": {"rotate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "bone25": {"rotate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "bone32": {"rotate": [{"angle": -2.58, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -22.19, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 17.57, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -13.98, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.6667, "angle": -2.58}], "translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "bone33": {"rotate": [{"angle": -6.94, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.0667, "angle": -2.58, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -22.19, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 17.57, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -13.98, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": -6.94}], "translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "bone34": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -22.19, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 17.57, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -13.98, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "bone35": {"rotate": [{"angle": -2.58, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -22.19, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 17.57, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -13.98, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.6667, "angle": -2.58}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "bone36": {"rotate": [{"angle": -2.58, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -22.19, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 17.57, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -13.98, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.6667, "angle": -2.58}], "translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "bone37": {"rotate": [{"angle": -6.94, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.0667, "angle": -2.58, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -22.19, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 17.57, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -13.98, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": -6.94}], "translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "bone38": {"rotate": [{"angle": -11.42, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.0667, "angle": -6.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -22.19, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 17.57, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -13.98, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.6667, "angle": -11.42}], "translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "bone39": {"rotate": [{"angle": -6.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -22.19, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 17.57, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -13.98, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": -6.99}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "bone40": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -22.19, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 17.57, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -13.98, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "bone41": {"rotate": [{"angle": -2.58, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -22.19, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 17.57, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -13.98, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.6667, "angle": -2.58}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "bone42": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -22.19, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 17.57, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -13.98, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "bone43": {"rotate": [{"angle": -2.58, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -22.19, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 17.57, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -13.98, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.6667, "angle": -2.58}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"time": 0.1667, "curve": "stepped"}, {"time": 0.3}]}, "bone29": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -14.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 14.89, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -16.93, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone30": {"rotate": [{"angle": -3.8, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -14.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 14.89, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -16.93, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.6667, "angle": -3.8}]}, "bone31": {"rotate": [{"angle": -10.06, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -14.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 14.89, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -16.93, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.6667, "angle": -10.06}]}, "bone22": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 51.31, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -87.4, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone23": {"rotate": [{"angle": -5.72, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -12.5, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -44, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -5.72}]}, "bone26": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -137.88, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -15.55, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone27": {"rotate": [{"angle": 8.74, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 64.97, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 67.17, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": 8.74}]}, "bone24": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 9.03, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 5.58, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone28": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 13.11, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 8.09, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}}, "events": [{"time": 0.3333, "name": "atk"}]}, "boss_idle": {"bones": {"root": {"rotate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "translate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}]}, "bone": {"rotate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "translate": [{"x": 3.04, "y": -3.59, "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 0.7667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.1, "x": 1.03, "y": -1.22, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.7667, "x": 3.64, "y": -4.3, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 2, "x": 3.04, "y": -3.59}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}]}, "bone2": {"rotate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "translate": [{"x": 0.72, "y": 1.61, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "x": 0.75, "y": 1.68, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2, "x": 0.72, "y": 1.61}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}]}, "bone3": {"rotate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "translate": [{"x": 0.96, "y": 0.88, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.4333, "x": 1.64, "y": 1.49, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.1, "x": 0.46, "y": 0.42, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.4333, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2, "x": 0.96, "y": 0.88}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}]}, "bone4": {"rotate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.7667, "curve": "stepped"}, {"time": 2}], "translate": [{"x": 0.23, "y": -0.18, "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 0.7667, "x": 1.38, "y": -1.06, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.1, "x": 0.98, "y": -0.76, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.7667, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 2, "x": 0.23, "y": -0.18}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.7667, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.7667, "curve": "stepped"}, {"time": 2}]}, "bone5": {"rotate": [{"angle": 9.07, "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 0.7667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.1, "angle": 3.09, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.7667, "angle": 10.87, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 2, "angle": 9.07}], "translate": [{"curve": "stepped"}, {"time": 0.7667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.7667, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 0.7667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.7667, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 0.7667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.7667, "curve": "stepped"}, {"time": 2}]}, "bone6": {"rotate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "translate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}]}, "bone7": {"rotate": [{"angle": 3.25, "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 0.6333, "angle": 10, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1.1, "angle": 5.43, "curve": 0.336, "c2": 0.34, "c3": 0.676, "c4": 0.69}, {"time": 1.3, "angle": 2.84, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.6333, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 2, "angle": 3.25}], "translate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.3, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.3, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.3, "curve": "stepped"}, {"time": 2}]}, "bone8": {"rotate": [{"angle": 0.11, "curve": 0.264, "c2": 0.06, "c3": 0.752}, {"time": 0.9667, "angle": 10, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 1.1, "angle": 9.35, "curve": 0.304, "c2": 0.22, "c3": 0.644, "c4": 0.58}, {"time": 1.3, "angle": 7.16, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.9667, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 2, "angle": 0.11}], "translate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.3, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.3, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.3, "curve": "stepped"}, {"time": 2}]}, "bone9": {"rotate": [{"angle": 0.23, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 0.9333, "angle": 10, "curve": 0.29, "c3": 0.629, "c4": 0.37}, {"time": 1.1, "angle": 9.04, "curve": 0.295, "c2": 0.21, "c3": 0.667, "c4": 0.67}, {"time": 1.6, "angle": 2.84, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.9333, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 2, "angle": 0.23}], "translate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.6, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.6, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.6, "curve": "stepped"}, {"time": 2}]}, "bone10": {"rotate": [{"angle": 2.04, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 1.1, "angle": 9.04, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 1.2667, "angle": 10, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.6, "angle": 7.16, "curve": 0.327, "c2": 0.31, "c3": 0.685, "c4": 0.72}, {"time": 2, "angle": 2.04}], "translate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.6, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.6, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.6, "curve": "stepped"}, {"time": 2}]}, "bone11": {"rotate": [{"angle": 12.29, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "angle": 13.2, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.8, "angle": 3.75, "curve": 0.375, "c2": 0.52, "c3": 0.724, "c4": 0.92}, {"time": 1.1, "angle": 0.08, "curve": 0.344, "c2": 0.66, "c3": 0.677}, {"time": 1.1333, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2, "angle": 12.29}], "translate": [{"curve": "stepped"}, {"time": 0.8, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.1333, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 0.8, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.1333, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 0.8, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.1333, "curve": "stepped"}, {"time": 2}]}, "bone12": {"rotate": [{"angle": 7.18, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.4667, "angle": 13.2, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.8, "angle": 9.46, "curve": 0.324, "c2": 0.31, "c3": 0.671, "c4": 0.68}, {"time": 1.1, "angle": 4.31, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 1.4667, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "angle": 7.18}], "translate": [{"curve": "stepped"}, {"time": 0.8, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.4667, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 0.8, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.4667, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 0.8, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.4667, "curve": "stepped"}, {"time": 2}]}, "bone13": {"rotate": [{"angle": 0.82, "curve": "stepped"}, {"time": 1.1, "angle": 0.82, "curve": "stepped"}, {"time": 2, "angle": 0.82}], "translate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}]}, "bone14": {"rotate": [{"angle": -1.49, "curve": "stepped"}, {"time": 1.1, "angle": -1.49, "curve": "stepped"}, {"time": 2, "angle": -1.49}], "translate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}]}, "bone15": {"rotate": [{"angle": 1.1, "curve": "stepped"}, {"time": 1.1, "angle": 1.1, "curve": "stepped"}, {"time": 2, "angle": 1.1}], "translate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}]}, "bone16": {"rotate": [{"angle": 0.66, "curve": "stepped"}, {"time": 1.1, "angle": 0.66, "curve": "stepped"}, {"time": 2, "angle": 0.66}], "translate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}]}, "bone17": {"rotate": [{"angle": -1.08, "curve": "stepped"}, {"time": 1.1, "angle": -1.08, "curve": "stepped"}, {"time": 2, "angle": -1.08}], "translate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}]}, "bone18": {"rotate": [{"angle": 1.19, "curve": "stepped"}, {"time": 1.1, "angle": 1.19, "curve": "stepped"}, {"time": 2, "angle": 1.19}], "translate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}]}, "bone19": {"rotate": [{"angle": 0.69, "curve": "stepped"}, {"time": 1.1, "angle": 0.69, "curve": "stepped"}, {"time": 2, "angle": 0.69}], "translate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}]}, "bone20": {"rotate": [{"angle": -3.48, "curve": "stepped"}, {"time": 1.1, "angle": -3.48, "curve": "stepped"}, {"time": 2, "angle": -3.48}], "translate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}]}, "target1": {"rotate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "translate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}]}, "target2": {"rotate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "translate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}]}, "target3": {"rotate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "translate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}]}, "target4": {"rotate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "translate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}]}, "bone21": {"rotate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "translate": [{"x": 0.07, "y": 1.99, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.6, "x": 0.2, "y": 5.4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.1, "x": 0.1, "y": 2.7, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "x": 0.07, "y": 1.99}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}]}, "bone22": {"rotate": [{"angle": -3.18, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.4333, "angle": -5.41, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.1, "angle": -1.53, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.4333, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2, "angle": -3.18}], "translate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.4333, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.4333, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.4333, "curve": "stepped"}, {"time": 2}]}, "bone23": {"rotate": [{"angle": -0.9, "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 0.7667, "angle": -5.41, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.1, "angle": -3.87, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.7667, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 2, "angle": -0.9}], "translate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.7667, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.7667, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.7667, "curve": "stepped"}, {"time": 2}]}, "bone24": {"rotate": [{"angle": -0.23, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -5.41, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2, "angle": -0.23}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}]}, "bone25": {"rotate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "translate": [{"x": 1.09, "y": -0.59, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.6, "x": 2.95, "y": -1.61, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.1, "x": 1.48, "y": -0.8, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "x": 1.09, "y": -0.59}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}]}, "bone26": {"rotate": [{"angle": -5.12, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.4333, "angle": -8.7, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.1, "angle": -2.47, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.4333, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2, "angle": -5.12}], "translate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.4333, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.4333, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.4333, "curve": "stepped"}, {"time": 2}]}, "bone27": {"rotate": [{"angle": -1.45, "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 0.7667, "angle": -8.7, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.1, "angle": -6.23, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.7667, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 2, "angle": -1.45}], "translate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.7667, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.7667, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.7667, "curve": "stepped"}, {"time": 2}]}, "bone28": {"rotate": [{"angle": -0.37, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -8.7, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2, "angle": -0.37}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}]}, "bone29": {"rotate": [{"angle": -10.07, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.4333, "angle": -17.12, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.1, "angle": -4.86, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.4333, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2, "angle": -10.07}], "translate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.4333, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.4333, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.4333, "curve": "stepped"}, {"time": 2}]}, "bone30": {"rotate": [{"angle": -2.85, "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 0.7667, "angle": -17.12, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.1, "angle": -12.26, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.7667, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 2, "angle": -2.85}], "translate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.7667, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.7667, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.7667, "curve": "stepped"}, {"time": 2}]}, "bone31": {"rotate": [{"angle": -0.73, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -17.12, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2, "angle": -0.73}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}]}, "bone32": {"rotate": [{"angle": 7.77, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.4333, "angle": 13.2, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.1, "angle": 3.75, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.4333, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2, "angle": 7.77}], "translate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.4333, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.4333, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.4333, "curve": "stepped"}, {"time": 2}]}, "bone33": {"rotate": [{"angle": 2.2, "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 0.7667, "angle": 13.2, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.1, "angle": 9.46, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.7667, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 2, "angle": 2.2}], "translate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.7667, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.7667, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.7667, "curve": "stepped"}, {"time": 2}]}, "bone34": {"rotate": [{"angle": 0.56, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.9, "angle": 13.2, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.1, "angle": 11.48, "curve": 0.302, "c2": 0.23, "c3": 0.669, "c4": 0.67}, {"time": 1.5667, "angle": 3.75, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.9, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2, "angle": 0.56}], "translate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.5667, "curve": "stepped"}, {"time": 1.9, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.5667, "curve": "stepped"}, {"time": 1.9, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.5667, "curve": "stepped"}, {"time": 1.9, "curve": "stepped"}, {"time": 2}]}, "bone35": {"rotate": [{"angle": 2.19, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.2333, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1.1, "angle": 12.29, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 1.2333, "angle": 13.2, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.5667, "angle": 9.46, "curve": 0.329, "c2": 0.32, "c3": 0.691, "c4": 0.74}, {"time": 2, "angle": 2.19}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.5667, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.5667, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.5667, "curve": "stepped"}, {"time": 2}]}, "bone36": {"rotate": [{"angle": 2.67, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 1.1, "angle": 11.93, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 1.2667, "angle": 13.2, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.9333, "angle": 3.75, "curve": 0.339, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 2, "angle": 2.67}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.9333, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.9333, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.9333, "curve": "stepped"}, {"time": 2}]}, "bone37": {"rotate": [{"angle": 8.35, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.1, "angle": 6.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6, "angle": 13.2, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.9333, "angle": 9.46, "curve": 0.329, "c2": 0.32, "c3": 0.663, "c4": 0.65}, {"time": 2, "angle": 8.35}], "translate": [{"curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.9333, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.9333, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.9333, "curve": "stepped"}, {"time": 2}]}, "bone38": {"rotate": [{"angle": 12.9, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 0.9333, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 1.1, "angle": 1.27, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 1.9333, "angle": 13.2, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 2, "angle": 12.9}], "translate": [{"curve": "stepped"}, {"time": 0.9333, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.9333, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 0.9333, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.9333, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 0.9333, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.9333, "curve": "stepped"}, {"time": 2}]}, "bone39": {"rotate": [{"angle": 7.77, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.4333, "angle": 13.2, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.1, "angle": 3.75, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.4333, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2, "angle": 7.77}], "translate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.4333, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.4333, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.4333, "curve": "stepped"}, {"time": 2}]}, "bone40": {"rotate": [{"angle": 7.45, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.4333, "angle": 12.67, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.1, "angle": 3.6, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.4333, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2, "angle": 7.45}], "translate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}]}, "bone41": {"rotate": [{"angle": 1.65, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.8, "angle": 12.67, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.1, "angle": 9.6, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.8, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "angle": 1.65}], "translate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 2}]}, "bone42": {"rotate": [{"angle": 10.66, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.5667, "angle": 25.87, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1.1, "angle": 11.76, "curve": 0.338, "c2": 0.35, "c3": 0.674, "c4": 0.69}, {"time": 1.2333, "angle": 7.34, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.5667, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 2, "angle": 10.66}], "translate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2333, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2333, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2333, "curve": "stepped"}, {"time": 2}]}, "bone43": {"rotate": [{"angle": 0.59, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 0.9333, "angle": 25.87, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 1.1, "angle": 23.43, "curve": 0.314, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 1.2333, "angle": 19.61, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.9333, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 2, "angle": 0.59}], "translate": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2333, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2333, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.2333, "curve": "stepped"}, {"time": 2}]}}}, "die": {"bones": {"root": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone44": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 14.15, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 80.53, "curve": "stepped"}, {"time": 0.4667, "angle": 80.53}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 48.53, "y": 57.84, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 91.57, "y": 32.99, "curve": "stepped"}, {"time": 0.4667, "x": 91.57, "y": 32.99}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 19.93, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 2.21, "curve": "stepped"}, {"time": 0.4667, "angle": 2.21}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -13.7, "y": -3.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "x": -1.18, "y": -2.48, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4667, "x": -13.7, "y": -3.3}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 19.93, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 36.21, "curve": "stepped"}, {"time": 0.4667, "angle": 36.21}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 19.93, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -27.1, "curve": "stepped"}, {"time": 0.4667, "angle": -27.1}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 19.93, "curve": "stepped"}, {"time": 0.3333, "angle": 19.93, "curve": "stepped"}, {"time": 0.4667, "angle": 19.93}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 19.93, "curve": "stepped"}, {"time": 0.3333, "angle": 19.93, "curve": "stepped"}, {"time": 0.4667, "angle": 19.93}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 19.93, "curve": "stepped"}, {"time": 0.3333, "angle": 19.93, "curve": "stepped"}, {"time": 0.4667, "angle": 19.93}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 19.93, "curve": "stepped"}, {"time": 0.3333, "angle": 19.93, "curve": "stepped"}, {"time": 0.4667, "angle": 19.93}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 19.93, "curve": "stepped"}, {"time": 0.3333, "angle": 19.93, "curve": "stepped"}, {"time": 0.4667, "angle": 19.93}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 19.93, "curve": "stepped"}, {"time": 0.3333, "angle": 19.93, "curve": "stepped"}, {"time": 0.4667, "angle": 19.93}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 19.93, "curve": "stepped"}, {"time": 0.3333, "angle": 19.93, "curve": "stepped"}, {"time": 0.4667, "angle": 19.93}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 19.93, "curve": "stepped"}, {"time": 0.3333, "angle": 19.93, "curve": "stepped"}, {"time": 0.4667, "angle": 19.93}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone12": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 19.93, "curve": "stepped"}, {"time": 0.3333, "angle": 19.93, "curve": "stepped"}, {"time": 0.4667, "angle": 19.93}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone13": {"rotate": [{"angle": 0.82, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -46.24, "curve": "stepped"}, {"time": 0.4667, "angle": -46.24}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -29.37, "y": 5.4, "curve": "stepped"}, {"time": 0.4667, "x": -29.37, "y": 5.4}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone14": {"rotate": [{"angle": -1.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 80.57, "curve": "stepped"}, {"time": 0.4667, "angle": 80.57}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone15": {"rotate": [{"angle": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -34.77, "curve": "stepped"}, {"time": 0.4667, "angle": -34.77}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone16": {"rotate": [{"angle": 0.66, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -2.41, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -2.41}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone17": {"rotate": [{"angle": -1.08, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 41.95, "curve": "stepped"}, {"time": 0.4667, "angle": 41.95}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone18": {"rotate": [{"angle": 1.19, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -63.06, "curve": "stepped"}, {"time": 0.4667, "angle": -63.06}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone19": {"rotate": [{"angle": 0.69, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 78.13, "curve": "stepped"}, {"time": 0.4667, "angle": 78.13}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone20": {"rotate": [{"angle": -3.48, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 17.45, "curve": "stepped"}, {"time": 0.4667, "angle": 17.45}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "target1": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "x": 7.9, "y": 17.81, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "x": -38.86, "y": -9.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "x": -29.72, "y": -26.1, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4667, "x": -41.57, "y": -25.95}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "target2": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "target3": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -1.23, "y": -22.68, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "x": 10.61, "y": -22.83, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4667, "x": -1.23, "y": -22.68}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "target4": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 45.78, "y": -29.14, "curve": "stepped"}, {"time": 0.4667, "x": 45.78, "y": -29.14}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 19.93, "curve": "stepped"}, {"time": 0.3333, "angle": 19.93, "curve": "stepped"}, {"time": 0.4667, "angle": 19.93}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone22": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 19.93, "curve": "stepped"}, {"time": 0.3333, "angle": 19.93, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "angle": 43.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4667, "angle": 19.93}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone23": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -93.9, "curve": "stepped"}, {"time": 0.4667, "angle": -93.9}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone24": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 19.93, "curve": "stepped"}, {"time": 0.3333, "angle": 19.93, "curve": "stepped"}, {"time": 0.4667, "angle": 19.93}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone25": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 19.93, "curve": "stepped"}, {"time": 0.3333, "angle": 19.93, "curve": "stepped"}, {"time": 0.4667, "angle": 19.93}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone26": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -75.33, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "angle": -57.95, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4667, "angle": -75.33}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone27": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 19.93, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -9.76, "curve": "stepped"}, {"time": 0.5, "angle": -9.76}], "translate": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5}], "scale": [{"curve": "stepped"}, {"time": 0.5}], "shear": [{"curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5}]}, "bone28": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 19.93, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -26, "curve": "stepped"}, {"time": 0.4667, "angle": -26}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone29": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -66.93, "curve": "stepped"}, {"time": 0.4667, "angle": -66.93}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone30": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 19.93, "curve": "stepped"}, {"time": 0.3333, "angle": 19.93, "curve": "stepped"}, {"time": 0.4667, "angle": 19.93}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone31": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 19.93, "curve": "stepped"}, {"time": 0.3333, "angle": 19.93, "curve": "stepped"}, {"time": 0.4667, "angle": 19.93}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone32": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 19.93, "curve": "stepped"}, {"time": 0.3333, "angle": 19.93, "curve": "stepped"}, {"time": 0.4667, "angle": 19.93}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone33": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 19.93, "curve": "stepped"}, {"time": 0.3333, "angle": 19.93, "curve": "stepped"}, {"time": 0.4667, "angle": 19.93}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone34": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 19.93, "curve": "stepped"}, {"time": 0.3333, "angle": 19.93, "curve": "stepped"}, {"time": 0.4667, "angle": 19.93}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone35": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 19.93, "curve": "stepped"}, {"time": 0.3333, "angle": 19.93, "curve": "stepped"}, {"time": 0.4667, "angle": 19.93}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone36": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 19.93, "curve": "stepped"}, {"time": 0.3333, "angle": 19.93, "curve": "stepped"}, {"time": 0.4667, "angle": 19.93}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone37": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 19.93, "curve": "stepped"}, {"time": 0.3333, "angle": 19.93, "curve": "stepped"}, {"time": 0.4667, "angle": 19.93}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone38": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 19.93, "curve": "stepped"}, {"time": 0.3333, "angle": 19.93, "curve": "stepped"}, {"time": 0.4667, "angle": 19.93}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone39": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 19.93, "curve": "stepped"}, {"time": 0.3333, "angle": 19.93, "curve": "stepped"}, {"time": 0.4667, "angle": 19.93}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone40": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 19.93, "curve": "stepped"}, {"time": 0.3333, "angle": 19.93, "curve": "stepped"}, {"time": 0.4667, "angle": 19.93}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone41": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 19.93, "curve": "stepped"}, {"time": 0.3333, "angle": 19.93, "curve": "stepped"}, {"time": 0.4667, "angle": 19.93}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone42": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 19.93, "curve": "stepped"}, {"time": 0.3333, "angle": 19.93, "curve": "stepped"}, {"time": 0.4667, "angle": 19.93}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}, "bone43": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 19.93, "curve": "stepped"}, {"time": 0.3333, "angle": 19.93, "curve": "stepped"}, {"time": 0.4667, "angle": 19.93}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.4667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667}]}}}, "hurt": {"slots": {"langyao_012": {"attachment": [{"time": 0.1, "name": null}]}}, "bones": {"root": {"rotate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 7.14, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -14.28, "y": -0.55, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 10.64, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone3": {"rotate": [{"angle": 0.82, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 10.64, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.3333, "angle": 0.82}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3333}]}, "bone4": {"rotate": [{"angle": 2.39, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 10.64, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.3333, "angle": 2.39}], "translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333}]}, "bone5": {"rotate": [{"curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.0667, "angle": 32.42, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3333}]}, "bone6": {"rotate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone7": {"rotate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone8": {"rotate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone9": {"rotate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone10": {"rotate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone11": {"rotate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone12": {"rotate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone13": {"rotate": [{"angle": 0.82, "curve": "stepped"}, {"time": 0.1, "angle": 0.82, "curve": "stepped"}, {"time": 0.3333, "angle": 0.82}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone14": {"rotate": [{"angle": -1.49, "curve": "stepped"}, {"time": 0.1, "angle": -1.49, "curve": "stepped"}, {"time": 0.3333, "angle": -1.49}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone15": {"rotate": [{"angle": 1.1, "curve": "stepped"}, {"time": 0.1, "angle": 1.1, "curve": "stepped"}, {"time": 0.3333, "angle": 1.1}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone16": {"rotate": [{"angle": 0.66, "curve": "stepped"}, {"time": 0.1, "angle": 0.66, "curve": "stepped"}, {"time": 0.3333, "angle": 0.66}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone17": {"rotate": [{"angle": -1.08, "curve": "stepped"}, {"time": 0.1, "angle": -1.08, "curve": "stepped"}, {"time": 0.3333, "angle": -1.08}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone18": {"rotate": [{"angle": 1.19, "curve": "stepped"}, {"time": 0.1, "angle": 1.19, "curve": "stepped"}, {"time": 0.3333, "angle": 1.19}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone19": {"rotate": [{"angle": 0.69, "curve": "stepped"}, {"time": 0.1, "angle": 0.69, "curve": "stepped"}, {"time": 0.3333, "angle": 0.69}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone20": {"rotate": [{"angle": -3.48, "curve": "stepped"}, {"time": 0.1, "angle": -3.48, "curve": "stepped"}, {"time": 0.3333, "angle": -3.48}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "target1": {"rotate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "target2": {"rotate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "target3": {"rotate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "target4": {"rotate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone21": {"rotate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone22": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 12.65, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone23": {"rotate": [{"angle": 0.98, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 12.65, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.3333, "angle": 0.98}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3333}]}, "bone24": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 12.65, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone25": {"rotate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone26": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 11.63, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone27": {"rotate": [{"angle": 0.9, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 11.63, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.3333, "angle": 0.9}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3333}]}, "bone28": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 11.63, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone29": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 26.57, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone30": {"rotate": [{"angle": 2.05, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 26.57, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.3333, "angle": 2.05}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3333}]}, "bone31": {"rotate": [{"angle": 5.96, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 26.57, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.3333, "angle": 5.96}], "translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333}]}, "bone32": {"rotate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone33": {"rotate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone34": {"rotate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone35": {"rotate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone36": {"rotate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone37": {"rotate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone38": {"rotate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone39": {"rotate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone40": {"rotate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone41": {"rotate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone42": {"rotate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone43": {"rotate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.3333}]}, "bone44": {"translate": [{"curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1, "x": -24.3, "y": -0.29, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3333}]}}}}}