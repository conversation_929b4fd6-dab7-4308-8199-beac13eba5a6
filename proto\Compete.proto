syntax = "proto3";
package sim;
import "Player.proto";

// 
message CompeteAllRankResponse {
  // 排行榜
  repeated CompeteBattlerMessage rankList = 1;
}

// 
message CompeteBattleResponse {
  // 是否胜利
  bool win = 1;
  // 是否触发了圣殿技能
  bool trigger = 2;
  // 玩家自身积分的变动情况
  int32 ownPointChange = 3;
  // 玩家积分
  int64 point = 4;
  // 玩家位次
  int32 rank = 5;
  // 被挑战方积分的变动情况
  int32 challengerPointChange = 6;
  // 被挑战人员的总积分
  int64 challengerPoint = 7;
  // 获取的道具奖励
  repeated double resAddList = 8;
  // 当数据不为空的话，就是新的对手,前端需要覆盖
  repeated CompeteBattlerMessage battlerList = 9;
}

// 
message CompeteBattlerMessage {
  // 用户信息
  sim.PlayerSimpleMessage simpleMessage = 1;
  // 积分
  double point = 2;
  // 是否是机器人
  bool isRobot = 3;
}

// 
message CompeteExtraMessage {
  // 挑战券槽位大小
  int32 ticketSize = 1;
  // 今日剩余的免费刷新次数
  int32 freeFreshCount = 2;
  // 上一次触发修正剩余的免费刷新次数的时间  这个时间计算要满足以下规范：1.如果剩余的免费刷新次数的时间已经达到最大值，lastUpdateTime会更新为当前最新的时间; 2.如果使用免费刷新，刷新后的剩余免费剩余次数 ==  配置文件中配置的最大值 - 1，则lastUpdateTime更新为当前最新的时间；否则不变; 3.在1、2的基础上，如果剩余免费刷新次数 < 配置文件中配置的最大值 && 如果系统当前时间 - lastUpdateTime > 恢复一次免费刷新次数的时间的话，则lastUpdateTime =  lastUpdateTime + 新增加的次数 * 谈心精力值恢复一次的时间；
  int64 lastUpdateTime = 3;
  // 距离日榜结束还有的时间
  int64 dailyRewardDeadline = 4;
  // 距离日周结束还有的时间
  int64 weekRewardDeadline = 5;
  // 对手的名单
  repeated CompeteBattlerMessage battlerList = 6;
}

// 
message CompeteLogMessage {
  // 本次记录的标识
  int64 id = 1;
  // 用户信息
  sim.PlayerSimpleMessage simpleMessage = 2;
  // 对战的时间
  int64 timeStamp = 3;
  // 胜负情况
  bool win = 4;
  // 输掉|获得的分数
  int32 losePoint = 5;
  // 是否切磋过
  bool revenge = 6;
}

// 
message CompeteOwnRankMessage {
  int64 point = 1;
  int32 rank = 2;
  repeated CompeteBattlerMessage rankList = 3;
}

// 
message CompeteReplayMessage {
  // 是否胜利
  bool win = 1;
  // 录像回放
  string replay = 2;
}

// 
message CompeteSyncRefreshCountResponse {
  // 今日剩余的免费刷新次数
  int32 freeFreshCount = 1;
  // 上一次触发修正剩余的免费刷新次数的时间
  int64 lastUpdateTime = 2;
}

