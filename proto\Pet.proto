syntax = "proto3";
package sim;

// 
message PetMessage {
  // 宠物模版ID
  int64 petId = 1;
  // 宠物等级
  int32 level = 2;
  // 觉醒次数
  int32 awakeCount = 3;
  // 技能栏
  repeated PetSkillMessage petSkillList = 4;
  // 生效的皮肤ID
  int64 chosenSkinId = 5;
  // 已经解锁的皮肤列表
  repeated int64 skinList = 6;
}

// 
message PetSkillMessage {
  // 当前技能值
  double skillAdd = 1;
  // 最近一次洗练出来的值
  double backUpSkillAdd = 2;
  // 气运洗练的次数
  int32 energyWashCount = 3;
  // 道具洗练的次数
  int32 itemWashCount = 4;
}

// 用于洗练技能和替换技能
message PetSkillRequest {
  // 宠物id
  int64 petId = 1;
  // 技能在技能表中的顺序
  int32 rank = 2;
  // 是否通过消耗道具洗练技能 false表示通过气运洗练
  bool consumeItem = 3;
}

// 
message PetSkinRequest {
  // 宠物id
  int64 petId = 1;
  // 穿戴的皮肤
  int64 skinId = 2;
}

// 
message PetSkinUnlockResponse {
  // 灵兽ID
  int64 petId = 1;
  // 新解锁的皮肤ID
  int64 unlockSkinId = 2;
  // 新增的灵兽技能栏，如果长度大于0，前端需要加入到灵兽技能列表中
  repeated PetSkillMessage petSkillAddList = 3;
  // 已经解锁的皮肤列表
  repeated int64 skinList = 4;
}

