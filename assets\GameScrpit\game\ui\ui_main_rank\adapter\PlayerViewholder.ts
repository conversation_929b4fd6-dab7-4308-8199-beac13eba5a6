import { instantiate, Node, _decorator, Label, RichText, math, Sprite } from "cc";
import { ListAdapter, ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { PlayerRankMessage, PlayerSimpleMessage } from "../../../net/protocol/Player";
import FmUtils from "db://assets/GameScrpit/lib/utils/FmUtils";
import Formate from "db://assets/GameScrpit/lib/utils/Formate";
import { PlayerModule } from "db://assets/GameScrpit/module/player/PlayerModule";
const { ccclass, property } = _decorator;

@ccclass("PlayerViewholder")
export class PlayerViewholder extends ViewHolder {
  public updateData(data: PlayerSimpleMessage, type: number) {
    //

    if (this.position % 2 == 0) {
      this.node.getComponent(Sprite).color = math.color("#d7f1ff");
    } else {
      this.node.getComponent(Sprite).color = math.color("#bedff6");
    }

    this.getNode("node_rank1").active = false;
    this.getNode("node_rank2").active = false;
    this.getNode("node_rank3").active = false;
    this.getNode("lbl_rank").active = false;
    if (this.position == 0) {
      this.getNode("node_rank1").active = true;
    } else if (this.position == 1) {
      this.getNode("node_rank2").active = true;
    } else if (this.position == 2) {
      this.getNode("node_rank3").active = true;
    } else {
      this.getNode("lbl_rank").active = true;
      this.getNode("lbl_rank").getComponent(Label).string = (this.position + 1).toString();
    }
    FmUtils.setHeaderNode(this.getNode("BtnHeader"), data);
    this.getNode("lbl_name").getComponent(Label).string = data.nickname;
    if (type == 1) {
      this.getNode("lbl_rank_val").getComponent(Label).string = Formate.format(data.power);
    } else {
      this.getNode("lbl_rank_val").getComponent(Label).string = Formate.format(data.energySpeed);
    }
    let configLeader = PlayerModule.data.getConfigLeaderData(data.level);
    try {
      this.getNode("rich_title").active = false;
      this.getNode("node_title").destroyAllChildren();
      if (data.avatarList[3] != -1) {
        PlayerModule.service.createTitle(this.getNode("node_title"), data.avatarList[3]);
      } else {
        this.getNode("rich_title").active = true;
        this.getNode("rich_title").getComponent(RichText).string = `${configLeader.jingjie2}`;
      }
    } catch (error) {}
  }
}

export class PlayerRankAdapter extends ListAdapter {
  private _type: number;
  private _item: Node;
  private _data: PlayerSimpleMessage[] = [];
  constructor(type: number, item: Node) {
    super();
    this._type = type;
    this._item = item;
  }
  setData(data: PlayerSimpleMessage[]) {
    this._data = data;
    this.notifyDataSetChanged();
  }
  onCreateView(viewType: number): Node {
    let item = instantiate(this._item);
    item.active = true;
    return item;
  }
  onBindData(node: Node, position: number): void {
    node.getComponent(PlayerViewholder).updateData(this._data[position], this._type);
  }
  getCount(): number {
    return this._data.length;
  }
}
