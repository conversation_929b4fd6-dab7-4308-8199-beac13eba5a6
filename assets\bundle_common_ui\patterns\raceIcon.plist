<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>S0162.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{132,130}</string>
                <key>spriteSourceSize</key>
                <string>{132,130}</string>
                <key>textureRect</key>
                <string>{{1,135},{132,130}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S0163.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{74,73}</string>
                <key>spriteSourceSize</key>
                <string>{74,73}</string>
                <key>textureRect</key>
                <string>{{1,380},{74,73}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S0164.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{69,69}</string>
                <key>spriteSourceSize</key>
                <string>{69,69}</string>
                <key>textureRect</key>
                <string>{{72,526},{69,69}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S0165.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{71,69}</string>
                <key>spriteSourceSize</key>
                <string>{71,69}</string>
                <key>textureRect</key>
                <string>{{77,453},{71,69}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S0166.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{71,69}</string>
                <key>spriteSourceSize</key>
                <string>{71,69}</string>
                <key>textureRect</key>
                <string>{{1,455},{71,69}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S0167.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{71,69}</string>
                <key>spriteSourceSize</key>
                <string>{71,69}</string>
                <key>textureRect</key>
                <string>{{150,453},{71,69}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S0168.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{71,69}</string>
                <key>spriteSourceSize</key>
                <string>{71,69}</string>
                <key>textureRect</key>
                <string>{{1,526},{71,69}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>S1277.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{57,53}</string>
                <key>spriteSourceSize</key>
                <string>{57,53}</string>
                <key>textureRect</key>
                <string>{{72,597},{57,53}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S1278.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{44,53}</string>
                <key>spriteSourceSize</key>
                <string>{44,53}</string>
                <key>textureRect</key>
                <string>{{1,599},{44,53}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>S1279.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{57,53}</string>
                <key>spriteSourceSize</key>
                <string>{57,53}</string>
                <key>textureRect</key>
                <string>{{131,597},{57,53}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S1280.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{57,53}</string>
                <key>spriteSourceSize</key>
                <string>{57,53}</string>
                <key>textureRect</key>
                <string>{{190,524},{57,53}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>S1281.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{57,53}</string>
                <key>spriteSourceSize</key>
                <string>{57,53}</string>
                <key>textureRect</key>
                <string>{{190,583},{57,53}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>bg_judianjineng_xuanze.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{132,132}</string>
                <key>spriteSourceSize</key>
                <string>{132,132}</string>
                <key>textureRect</key>
                <string>{{1,1},{132,132}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icon_judianjineng_1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{111,111}</string>
                <key>spriteSourceSize</key>
                <string>{111,111}</string>
                <key>textureRect</key>
                <string>{{135,1},{111,111}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icon_judianjineng_2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{111,111}</string>
                <key>spriteSourceSize</key>
                <string>{111,111}</string>
                <key>textureRect</key>
                <string>{{135,114},{111,111}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icon_judianjineng_3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{111,111}</string>
                <key>spriteSourceSize</key>
                <string>{111,111}</string>
                <key>textureRect</key>
                <string>{{135,227},{111,111}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icon_judianjineng_4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{111,111}</string>
                <key>spriteSourceSize</key>
                <string>{111,111}</string>
                <key>textureRect</key>
                <string>{{1,267},{111,111}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icon_judianjineng_5.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{111,111}</string>
                <key>spriteSourceSize</key>
                <string>{111,111}</string>
                <key>textureRect</key>
                <string>{{114,340},{111,111}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>raceIcon.png</string>
            <key>size</key>
            <string>{247,651}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:dcaaf7f9dbd6584a5d8a7f836bf2572d:dc5b02d65ca526794c3c59120fe3cdf3:8bc2e38f4c339846852ef5be263e4882$</string>
            <key>textureFileName</key>
            <string>raceIcon.png</string>
        </dict>
    </dict>
</plist>
