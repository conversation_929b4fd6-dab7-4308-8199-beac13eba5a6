[{"__type__": "cc.Prefab", "_name": "UIClubLog", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "UIClubLog", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 62}], "_active": true, "_components": [{"__id__": 84}, {"__id__": 86}, {"__id__": 88}], "_prefab": {"__id__": 90}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 3.1204999999999927, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 3}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 2}, "asset": {"__uuid__": "468968c7-ca41-40f2-9e5f-74ee660b4fd8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 4}, "targetOverrides": []}, {"__type__": "cc.PrefabInstance", "fileId": "0b+ub9u3NGVpL0alKPLytC", "prefabRootNode": {"__id__": 1}, "mountedChildren": [{"__id__": 5}], "mountedComponents": [], "propertyOverrides": [{"__id__": 37}, {"__id__": 39}, {"__id__": 40}, {"__id__": 41}, {"__id__": 42}, {"__id__": 44}, {"__id__": 46}, {"__id__": 48}, {"__id__": 50}, {"__id__": 52}, {"__id__": 54}, {"__id__": 56}, {"__id__": 58}, {"__id__": 60}, {"__id__": 61}], "removedComponents": []}, {"__type__": "cc.MountedChildrenInfo", "targetInfo": {"__id__": 6}, "nodes": [{"__id__": 7}]}, {"__type__": "cc.TargetInfo", "localID": ["ccATktvIZPJY5yZnIE9AmP"]}, {"__type__": "cc.Node", "_name": "dialog_content", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 2}}, "_parent": {"__id__": 8}, "_children": [{"__id__": 18}], "_active": true, "_components": [{"__id__": 32}, {"__id__": 34}], "_prefab": {"__id__": 36}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "dialog_content", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 7}], "_active": true, "_components": [{"__id__": 9}, {"__id__": 11}, {"__id__": 13}, {"__id__": 15}], "_prefab": {"__id__": 17}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -16.074499999999944, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 8}, "_enabled": true, "__prefab": {"__id__": 10}, "_contentSize": {"__type__": "cc.Size", "width": 659.0970000000001, "height": 1073.8400000000001}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d8DuFrkIFIJrvRcNSr2YgG"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 8}, "_enabled": true, "__prefab": {"__id__": 12}, "_alignFlags": 45, "_target": null, "_left": 18.95149999999997, "_right": 18.951499999999964, "_top": 51.177000000000056, "_bottom": 19.027999999999977, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a2m76nTfpD5q+4RNdfiIOx"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 8}, "_enabled": true, "__prefab": {"__id__": 14}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "babhDkmCNDYqH1Zm6vqUdq"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 8}, "_enabled": true, "__prefab": {"__id__": 16}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9ddIeszclP2KkZe+N/mFmL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ccATktvIZPJY5yZnIE9AmP", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "log_list", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 7}, "_children": [], "_active": true, "_components": [{"__id__": 19}, {"__id__": 21}, {"__id__": 23}, {"__id__": 25}, {"__id__": 27}, {"__id__": 29}], "_prefab": {"__id__": 31}, "_lpos": {"__type__": "cc.Vec3", "x": -329.548, "y": 521.6165, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 20}, "_contentSize": {"__type__": "cc.Size", "width": 660.0970000000001, "height": 1045.728}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fdzTyNX7FK2o7sXgmRhxvh"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 22}, "_alignFlags": 45, "_target": null, "_left": 0.00050000000004502, "_right": -1.000500000000045, "_top": 15.303499999999985, "_bottom": 12.808499999999981, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 298.99, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "21PGVeUKZNs4wT9PtfkaWO"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 24}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "573zGz9yVC/rxllyU2j8nq"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 26}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d0mfxkA/dPNL8Qu5eSaCRf"}, {"__type__": "fec9cWgxLJGCIOIh1/BnbUr", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": false, "__prefab": {"__id__": 28}, "spaceY": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7akd013EVN8pjByPRbWJYQ"}, {"__type__": "fe48a55/2VHg5MmeyH/Ap7U", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 30}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "51BOTMnl9Miq8/T9ZhF/9i"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "741HYKMilCFpNEcmd7gUFb", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 7}, "_enabled": true, "__prefab": {"__id__": 33}, "_contentSize": {"__type__": "cc.Size", "width": 659.0970000000001, "height": 1073.8400000000001}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8eEldmillHjawKCEXuVGqU"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 7}, "_enabled": true, "__prefab": {"__id__": 35}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 620, "_originalHeight": 700, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a3fTM2/XhGwpQx5snq4wg+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d9y9Wzf4BEwJhfwSo1+qrZ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 38}, "propertyPath": ["_name"], "value": "DialogSub"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 38}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -1.2949999999998472, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 38}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 38}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 43}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 697, "height": 1144.045}}, {"__type__": "cc.TargetInfo", "localID": ["dcMfg1dj1MdZqre7xUgJqz"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 45}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 697, "height": 1144.045}}, {"__type__": "cc.TargetInfo", "localID": ["44g2VAc1VIU42rC+WZOf2b"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 47}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 659.0970000000001, "height": 1073.8400000000001}}, {"__type__": "cc.TargetInfo", "localID": ["d8DuFrkIFIJrvRcNSr2YgG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 49}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 5, "y": 544.0225, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["8d9SnxGQhLO52jTVFOf8SD"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 51}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 697, "height": 112}}, {"__type__": "cc.TargetInfo", "localID": ["c81ZpQbBZMrp/EQrEebYzT"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 53}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -219.627, "y": -29.578999999999994, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["b6eeINGDpEZLVcaNWdjJaK"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 55}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 327.7, "y": 2.544000000000004, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["1fOxWCfPxPAqI5OgnftRyd"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 57}, "propertyPath": ["strTitle"], "value": "日志"}, {"__type__": "cc.TargetInfo", "localID": ["f2ofLYMWBPU4DygV2KdYZ4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 59}, "propertyPath": ["clickEvents", "0", "target"], "value": {"__id__": 1}}, {"__type__": "cc.TargetInfo", "localID": ["630kdrTEhE3r2bQfHu5867"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 59}, "propertyPath": ["clickEvents", "0", "_componentId"], "value": "cdb8d/bMRtJGog3QeHOHrMm"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 59}, "propertyPath": ["clickEvents", "0", "handler"], "value": "closeBack"}, {"__type__": "cc.Node", "_name": "log_viewholder", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 63}, {"__id__": 71}], "_active": false, "_components": [{"__id__": 79}, {"__id__": 81}], "_prefab": {"__id__": 83}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 393.0285000000002, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lbl_log_date", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 62}, "_children": [], "_active": true, "_components": [{"__id__": 64}, {"__id__": 66}, {"__id__": 68}], "_prefab": {"__id__": 70}, "_lpos": {"__type__": "cc.Vec3", "x": -233.00001525878906, "y": 40.43, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 63}, "_enabled": true, "__prefab": {"__id__": 65}, "_contentSize": {"__type__": "cc.Size", "width": 87.99998474121094, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d90iNePkJBxJAjty8YF0pQ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 63}, "_enabled": true, "__prefab": {"__id__": 67}, "_alignFlags": 1, "_target": null, "_left": 9, "_right": 0, "_top": 4.57, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "efoP0uHQhL/68gNr17V9Mw"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 63}, "_enabled": true, "__prefab": {"__id__": 69}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 57, "g": 115, "b": 174, "a": 255}, "_string": "00:00", "_horizontalAlign": 1, "_verticalAlign": 0, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "06uLUSRwZBbKQEz6oVoKop"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5c9KXaKClKIJYcXKimVwQZ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "log_txt", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 62}, "_children": [], "_active": true, "_components": [{"__id__": 72}, {"__id__": 74}, {"__id__": 76}], "_prefab": {"__id__": 78}, "_lpos": {"__type__": "cc.Vec3", "x": -215.378, "y": 40.43, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 71}, "_enabled": true, "__prefab": {"__id__": 73}, "_contentSize": {"__type__": "cc.Size", "width": 540, "height": 49.14}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fd2Ox4xM9MOrFj2dRmqCSY"}, {"__type__": "cc.RichText", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 71}, "_enabled": true, "__prefab": {"__id__": 75}, "_lineHeight": 39, "_string": "212122121212212111111", "_horizontalAlign": 0, "_verticalAlign": 0, "_fontSize": 32, "_fontColor": {"__type__": "cc.Color", "r": 57, "g": 115, "b": 174, "a": 255}, "_maxWidth": 540, "_fontFamily": "<PERSON><PERSON>", "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_userDefinedFont": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_cacheMode": 0, "_imageAtlas": null, "_handleTouchEvent": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c5KLW43zdBtL62rOKn8nU+"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 71}, "_enabled": true, "__prefab": {"__id__": 77}, "_alignFlags": 9, "_target": null, "_left": 114.62200000000001, "_right": 0, "_top": 4.57, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8694QKpWtHq5gYloDuJvJV"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "49jBMScnZK5b23IdJT7m5X", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 62}, "_enabled": true, "__prefab": {"__id__": 80}, "_contentSize": {"__type__": "cc.Size", "width": 660, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d0eee+f1tJB6dVQf0hcDQH"}, {"__type__": "168571VdcNGz5zy3j5MKmq2", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 62}, "_enabled": true, "__prefab": {"__id__": 82}, "log_txt": {"__id__": 74}, "lblLogDate": {"__id__": 68}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9bX1z5T/RFoYWbK+ZHlo7i"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "76BAdIyOVIeZDybEx0yDHF", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 85}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1163.2150000000001}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "84XzmDPTdA8644eJ4iK2WX"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 87}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 165.272, "_bottom": 171.51299999999998, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "08uZvDJnRNDr2Ee8XfchpS"}, {"__type__": "cdb8d/bMRtJGog3QeHOHrMm", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 89}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9bFCeAN6dB3oRfXvqE2saO"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": [{"__id__": 91}, {"__id__": 94}], "nestedPrefabInstanceRoots": [{"__id__": 2}]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 2}, "sourceInfo": {"__id__": 92}, "propertyPath": ["graphics"], "target": {"__id__": 2}, "targetInfo": {"__id__": 93}}, {"__type__": "cc.TargetInfo", "localID": ["f2ofLYMWBPU4DygV2KdYZ4"]}, {"__type__": "cc.TargetInfo", "localID": ["9ddIeszclP2KkZe+N/mFmL"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 2}, "sourceInfo": {"__id__": 95}, "propertyPath": ["dialogTitle"], "target": {"__id__": 2}, "targetInfo": {"__id__": 96}}, {"__type__": "cc.TargetInfo", "localID": ["f2ofLYMWBPU4DygV2KdYZ4"]}, {"__type__": "cc.TargetInfo", "localID": ["e9cwQVQARBJrTgOq5ECZIX"]}]