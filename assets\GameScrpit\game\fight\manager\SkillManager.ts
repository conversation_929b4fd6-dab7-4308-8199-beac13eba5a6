import { _decorator, isValid } from "cc";
import ManagerSection from "./ManagerSection";
import GameObject from "../../../lib/object/GameObject";
import FightManager from "./FightManager";
import { CallSkillDetail } from "../FightDefine";

import { JsonMgr } from "../../mgr/JsonMgr";
import { GOSkillCommonAtk } from "../skill/GOSkillCommonAtk";
import { GOSkillNoumenonAtk } from "../skill/GOSkillNoumenonAtk";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.STOP);
const { ccclass, property } = _decorator;

@ccclass("SkillManager")
export default class SkillManager extends ManagerSection {
  private _skillScrpitMap: Map<string, any> = new Map();

  public static sectionName(): string {
    return "SkillManager";
  }
  public onInit(go: GameObject, args) {
    super.onInit(go, args);
    this.createRoot("SkillRoot", FightManager.instance);
    this.ready();
  }

  public onStart(): void {
    this._skillScrpitMap.set("GOSkillCommonAtk", GOSkillCommonAtk);
    this._skillScrpitMap.set("GOSkillNoumenonAtk", GOSkillNoumenonAtk);
  }

  public callSkill(detail: CallSkillDetail) {
    if (FightManager.instance.fightOver == true) {
      return;
    }
    if (isValid(this.root) == false) {
      return;
    }
    let db = JsonMgr.instance.jsonList.c_skillShow[detail.skillId];
    if (db == null) {
      log.error("技能id:" + detail.skillId + "为空");
      return;
    }

    let type = this._skillScrpitMap.get(db.scrpitName);
    let skill = new type(detail);
    this.addChild(skill);
    skill.onInitDetail(detail);
    return skill;
  }

  public updateSelf(dt) {
    super.updateSelf(dt);
  }
}

// // Learn TypeScript:
// //  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// // Learn Attribute:
// //  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// // Learn life-cycle callbacks:
// //  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

// import DBMgr from "../../../lib/db/DBMgr";
// import GameObject from "../../../lib/object/GameObject";
// import GOBullet from "../bullet/GOBullet";
// import { CallBulletDetail } from "../FightDefine";
// import FightManager from "../FightManager";
// import ManagerSection from "../ManagerSection";

// const { ccclass, property } = cc._decorator;

// @ccclass
// export default class BulletManager extends ManagerSection {
//     private _callArray: Array<any> = new Array<any>();
//     private _bulletBottom: GameObject = null;
//     public static sectionName(): string {
//         return "BulletManager";
//     }
//     public onInit(go: GameObject, args) {
//         super.onInit(go, args);
//         this.createRoot("BulletRoot", FightManager.instance);
//         this.root.zIndex = 6;
//         this._bulletBottom = new GameObject("BulletBottomRoot");
//         FightManager.instance.addChild(this._bulletBottom, -1);
//         this.ready();
//     }

//     public onStart() {
//         super.onStart();
//     }

//     public callObject(detail: CallBulletDetail, delay: number) {
//         this._callArray.push({ "detail": detail, "time": delay });
//     }

//     public doCallObject(detail: CallBulletDetail): GOBullet {
//         let isOnBottom = DBMgr.data.bulletDB[detail.bulletId]["isBottom"];
//         let bulletDB = DBMgr.data.bulletDB[detail.bulletId];
//         if (bulletDB == null) {
//             log.error("技能id:" + detail.bulletId + "为空");
//             return;
//         }
//         let type = cc.js.getClassByName(bulletDB["script"]) as any;
//         let bullet = new type(detail);
//         this.addChild(bullet, isOnBottom ? this._bulletBottom : null);
//         return bullet;
//     }

//     public updateSelf(dt) {
//         super.updateSelf(dt);
//         this.updateCallObject(dt);
//     }

//     private updateCallObject(dt) {
//         for (let i = this._callArray.length - 1; i >= 0; i--) {
//             let info = this._callArray[i];
//             info["time"] -= dt;
//             if (info["time"] <= 0) {
//                 this.doCallObject(info["detail"]);
//                 this._callArray.splice(i, 1);
//             }
//         }
//     }

//     public onRemove() {
//         super.onRemove();
//     }
// }
