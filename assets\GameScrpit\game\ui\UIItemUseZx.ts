import { _decorator, Button, EditBox, Input, Label, Node, Sprite, v3 } from "cc";
import { UINode } from "../../lib/ui/UINode";
import { BundleEnum } from "../bundleEnum/BundleEnum";
import { JsonMgr } from "../mgr/JsonMgr";
import ToolExt from "../common/ToolExt";
import { UIMgr } from "../../lib/ui/UIMgr";
import Formate from "../../lib/utils/Formate";
import { PlayerModule } from "../../module/player/PlayerModule";
import { ItemUseMessage } from "../net/protocol/Item";
import MsgMgr from "../../lib/event/MsgMgr";
import MsgEnum from "../event/MsgEnum";
import { DoubleValueList } from "../net/protocol/ExternalMessage";
import TickerMgr from "../../lib/ticker/TickerMgr";
import { LangMgr } from "../mgr/LangMgr";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "../../lib/utils/FmUtils";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

class ZxInfo {
  item: Node;
  id: number;
  num: number;
  index: number;
}

const itemIdKey = "itemIdKey";

@ccclass("UIItemUseZx")
export class UIItemUseZx extends UINode {
  protected _openAct: boolean = true; //打开动作
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_COMMON_MAIN}?prefab/ui/UIItemUseZx`;
  }

  private _itemId: number = null;

  private _zxMap: Map<number, ZxInfo> = new Map();

  private _maxNum: number = 0;

  private _ticker_timeout_Id: number = null;

  private _ticker_interval_Id: number = null;

  public init(args: any): void {
    super.init(args);
    this._itemId = args.itemId;
  }

  protected onEvtShow(): void {
    this.setTitle();
    this.loadItemZx();
  }

  private setTitle() {
    this._maxNum = PlayerModule.data.getItemNum(this._itemId);
    let num = Formate.format(this._maxNum);

    this.getNode("use_title1").getComponent(Label).string = LangMgr.txMsgCode(193, []);
    this.getNode("use_title2").getComponent(Label).string = LangMgr.txMsgCode(195, [num]);
    this.getNode("use_title3").getComponent(Label).string = LangMgr.txMsgCode(194, []);
  }

  private setCurUse() {
    let num = Formate.format(this.getCurAllNum());

    this.getNode("lbl_cur_ues1").getComponent(Label).string = LangMgr.txMsgCode(185, []);
    this.getNode("lbl_cur_ues2").getComponent(Label).string = LangMgr.txMsgCode(215, [num, this._maxNum]);
    this.getNode("lbl_cur_ues3").getComponent(Label).string = LangMgr.txMsgCode(192, []);
  }

  private upZxItemList() {
    this._zxMap.forEach((value, key) => {
      let id = key;
      this.setItemState(id);
    });
    this.setCurUse();
  }

  private loadItemZx() {
    let db = JsonMgr.instance.getConfigItem(this._itemId);
    for (let i = 0; i < db.type1Son1List.length; i++) {
      let node = ToolExt.clone(this.getNode("zx_item"), this);
      node.setPosition(v3(0, 0, 0));
      node.active = true;

      this.getNode("content").addChild(node);

      let info = db.type1Son1List[i];
      FmUtils.setItemNode(node["Item"], info[0], info[1]);

      this._zxMap.set(info[0], {
        id: info[0],
        num: 0,
        item: node,
        index: i,
      });

      node["EditBox_item"].getComponent(EditBox).string = "0";
      node["EditBox_item"].getComponent(EditBox).Placeholder = "0";

      node["btn_jian"][itemIdKey] = info[0];
      node["btn_jia"][itemIdKey] = info[0];
      node["EditBox_item"][itemIdKey] = info[0];

      node["EditBox_item"].on("editing-did-ended", this.onEditDidBegan, this);

      node["btn_jian"].on(Input.EventType.TOUCH_START, ToolExt.tryFunc(this.touch_jian_start.bind(this)), this);
      node["btn_jian"].on(Input.EventType.TOUCH_CANCEL, ToolExt.tryFunc(this.touch_jian_cancel.bind(this)), this);
      node["btn_jian"].on(Input.EventType.TOUCH_END, ToolExt.tryFunc(this.touch_jian_end.bind(this)), this);

      node["btn_jia"].on(Input.EventType.TOUCH_START, ToolExt.tryFunc(this.touch_jia_start.bind(this)), this);
      node["btn_jia"].on(Input.EventType.TOUCH_CANCEL, ToolExt.tryFunc(this.touch_jia_cancel.bind(this)), this);
      node["btn_jia"].on(Input.EventType.TOUCH_END, ToolExt.tryFunc(this.touch_jia_end.bind(this)), this);
    }

    this.upZxItemList();
  }

  private touch_jia_start(event) {
    this.upJia(event);
    this._ticker_timeout_Id = TickerMgr.setTimeout(0.15, () => {
      this._ticker_interval_Id = TickerMgr.setInterval(
        0.05,
        () => {
          this.upJia(event);
        },
        true
      );
    });
  }

  private touch_jia_cancel(event) {
    if (this._ticker_timeout_Id) {
      TickerMgr.clearTimeout(this._ticker_timeout_Id);
      this._ticker_timeout_Id = null;
    }

    if (this._ticker_interval_Id) {
      TickerMgr.clearInterval(this._ticker_interval_Id);
      this._ticker_interval_Id = null;
    }
  }

  private touch_jia_end(event) {
    if (this._ticker_timeout_Id) {
      TickerMgr.clearTimeout(this._ticker_timeout_Id);
      this._ticker_timeout_Id = null;
    }

    if (this._ticker_interval_Id) {
      TickerMgr.clearInterval(this._ticker_interval_Id);
      this._ticker_interval_Id = null;
    }
  }

  //======

  private touch_jian_start(event) {
    this.upJian(event);
    this._ticker_timeout_Id = TickerMgr.setTimeout(0.15, () => {
      this._ticker_interval_Id = TickerMgr.setInterval(
        0.05,
        () => {
          this.upJian(event);
        },
        true
      );
    });
  }

  private touch_jian_cancel(event) {
    if (this._ticker_timeout_Id) {
      TickerMgr.clearTimeout(this._ticker_timeout_Id);
      this._ticker_timeout_Id = null;
    }

    if (this._ticker_interval_Id) {
      TickerMgr.clearInterval(this._ticker_interval_Id);
      this._ticker_interval_Id = null;
    }
  }

  private touch_jian_end(event) {
    if (this._ticker_timeout_Id) {
      TickerMgr.clearTimeout(this._ticker_timeout_Id);
      this._ticker_timeout_Id = null;
    }

    if (this._ticker_interval_Id) {
      TickerMgr.clearInterval(this._ticker_interval_Id);
      this._ticker_interval_Id = null;
    }
  }

  private setItemState(id: number) {
    let obj = this._zxMap.get(id);
    let num = this.getCurAllNum();

    if (obj.num == 0) {
      obj.item["btn_jian"].getComponent(Button).interactable = false;
      obj.item["btn_jian"].getComponent(Sprite).grayscale = true;
    } else {
      obj.item["btn_jian"].getComponent(Button).interactable = true;
      obj.item["btn_jian"].getComponent(Sprite).grayscale = false;
    }

    if (num >= this._maxNum) {
      obj.item["btn_jia"].getComponent(Button).interactable = false;
      obj.item["btn_jia"].getComponent(Sprite).grayscale = true;
    } else {
      obj.item["btn_jia"].getComponent(Button).interactable = true;
      obj.item["btn_jia"].getComponent(Sprite).grayscale = false;
    }

    obj.item["EditBox_item"].getComponent(EditBox).string = obj.num + "";
  }

  onEditDidBegan(param: EditBox) {
    let str: string = param.string;
    let num = Math.abs(Number(str));
    //log.log("输入结束=====", num);
    if (isNaN(num)) {
      num = 0;
    }

    let editNode = param.node;
    let id = editNode[itemIdKey];

    let allNum = this.getCurAllNum();
    allNum -= this._zxMap.get(id).num;

    if (allNum + num > this._maxNum) {
      num = this._maxNum - allNum;
    }

    this._zxMap.get(id).num = num;

    //log.log("查看所有道具的自选情况=====", this._zxMap);

    this.upZxItemList();
  }

  upJia(event) {
    let id = event.target[itemIdKey];
    let obj = this._zxMap.get(id);
    let allNum = this.getCurAllNum();
    if (allNum + 1 > this._maxNum) {
      return;
    }
    obj.num += 1;
    this._zxMap.set(id, obj);

    this.upZxItemList();
  }

  upJian(event) {
    let id = event.target[itemIdKey];
    let obj = this._zxMap.get(id);
    if (obj.num <= 0) {
      return;
    }

    obj.num -= 1;
    this._zxMap.set(id, obj);

    this.upZxItemList();
  }

  private getCurAllNum() {
    let num = 0;
    this._zxMap.forEach((value, key) => {
      num += value.num;
    });

    return num;
  }

  private on_click_btn_use() {
    let num = this.getCurAllNum();
    let obj = Object.create(null);
    this._zxMap.forEach((val, key) => {
      if (val.num > 0) {
        obj[val.index] = val.num;
      }
    });

    let info: ItemUseMessage = {
      itemId: this._itemId,
      num: num,
      heroId: 0,
      cityId: 0,
      choiceItemMap: obj,
    };
    PlayerModule.api.useItem(info, (res: DoubleValueList) => {
      //log.log("自选道具使用成功=====", res);

      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: res.values });

      UIMgr.instance.back();
    });
  }

  on_click_btn_close() {
    UIMgr.instance.back();
  }

  protected onEvtClose(): void {
    if (this._ticker_timeout_Id) {
      TickerMgr.clearTimeout(this._ticker_timeout_Id);
      this._ticker_timeout_Id = null;
    }

    if (this._ticker_interval_Id) {
      TickerMgr.clearInterval(this._ticker_interval_Id);
      this._ticker_interval_Id = null;
    }
  }
}
