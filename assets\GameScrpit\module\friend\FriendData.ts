import MsgEnum from "../../game/event/MsgEnum";
import { IConfigFriednFame, IConfigFriend } from "../../game/JsonDefine";
import { JsonMgr } from "../../game/mgr/JsonMgr";
import { FriendCitySkillMessage, FriendMessage, FriendVitalityMessage } from "../../game/net/protocol/Friend";
import MsgMgr from "../../lib/event/MsgMgr";
import { times } from "../../lib/utils/NumbersUtils";
import { FriendSort, FriendType } from "./FriendConstant";
import { FriendModule } from "./FriendModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class FriendData {
  private _friendBellesMap: { [key: number]: number[] } = {}; //美名录分类信息
  private _data: { [key: number]: FriendMessage } = {};
  private _friends: { [key: number]: number[] } = {};
  private _ownedFriendIds: number[] = [];
  private _unOwnedFriendsIds: number[] = [];
  private _vitalityMessage: FriendVitalityMessage;
  private _pictureMessage: { [key: number]: number } = {};
  // 美名对应的资质
  private _fameQuality: { [key: number]: number } = {};

  public init() {
    this._data = {};
    this._friendBellesMap = {};
    this._friends = {};
    this._ownedFriendIds = [];
    this._unOwnedFriendsIds = [];
    this._vitalityMessage = null;
    this._pictureMessage = {};
    this._fameQuality = {};
  }
  public get vitality(): number {
    return this._vitalityMessage?.vitality || 0;
  }

  public get vitalityMessage(): FriendVitalityMessage {
    return this._vitalityMessage;
  }

  // 已拥有好友数量
  public get ownedFriendNum() {
    return this._ownedFriendIds.length;
  }
  // 所有好友数量
  public get totalFriendNum() {
    return this._ownedFriendIds.length + this._unOwnedFriendsIds.length;
  }

  public set vitalityMessage(data: FriendVitalityMessage) {
    this._vitalityMessage = data;
    MsgMgr.emit(MsgEnum.ON_FRIEND_UPDATE);
  }
  public set pictureMessage(vals: number[]) {
    vals?.forEach((val) => {
      this._pictureMessage[val] = val;
    });
  }
  public get pictureMessage(): { [key: number]: number } {
    return this._pictureMessage;
  }

  private updateFriendList() {
    this._ownedFriendIds = [];
    this._unOwnedFriendsIds = [];
    this._friends = [];
    Object.values(JsonMgr.instance.jsonList.c_friend).forEach((val: IConfigFriend) => {
      for (let i = 0; i < val.heroList.length; i++) {
        if (this._friends[val.heroList[i]]) {
          this._friends[val.heroList[i]].push(val.id);
        } else {
          this._friends[val.heroList[i]] = [val.id];
        }
      }
      if (this._data[val.id]) {
        this._ownedFriendIds.push(val.id);
      } else {
        this._unOwnedFriendsIds.push(val.id);
      }
    });
    FriendModule.service.createBadgeList();
  }

  //默认排序
  private sortDefault(a: number, b: number) {
    // if (FriendModule.config.getFriendById(b).color == FriendModule.config.getFriendById(a).color) {
    //   return b - a;
    // }
    return FriendModule.config.getFriendById(a).order - FriendModule.config.getFriendById(b).order;
  }
  private sortTalent(a: number, b: number) {
    if (this.getFriendMessage(b).destiny == this.getFriendMessage(a).destiny) {
      return b - a;
    }
    return this.getFriendMessage(b).destiny - this.getFriendMessage(a).destiny;
  }
  private sortFriendship(a: number, b: number) {
    if (this.getFriendKarma(b) == this.getFriendKarma(a)) {
      return b - a;
    }
    return this.getFriendKarma(b) - this.getFriendKarma(a);
  }

  public setFriendMessageList(friend: FriendMessage[]) {
    this._data = {};
    friend.forEach((element) => {
      this._data[element.friendId] = element;
    });
    this.updateFriendBells();
    this.updateFriendList();
    // log.log(friend);
  }
  public updateFriendBells() {
    this._friendBellesMap = {};
    this._fameQuality = {};
    Object.values(this._data).forEach((element) => {
      if (!this._friendBellesMap[element.fameLv]) {
        this._friendBellesMap[element.fameLv] = [element.friendId];
      } else {
        this._friendBellesMap[element.fameLv].push(element.friendId);
      }

      // 初始化美名效果对应的资质
      FriendModule.config.getFriendById(element.friendId).fameIdList.forEach((fameId) => {
        let fameEffect = JsonMgr.instance.jsonList.c_friednFame[fameId];
        let index: number = this.findFameIndex(element.fameLv, fameEffect);
        if (index >= 0 && fameEffect.attrId >= 601 && fameEffect.attrId <= 605) {
          // 美名对应的资质
          if (this._fameQuality[fameEffect.attrId]) {
            this._fameQuality[fameEffect.attrId] += fameEffect.addList[0][index];
          } else {
            this._fameQuality[fameEffect.attrId] = fameEffect.addList[0][index];
          }
        }
      });
    });
  }

  private findFameIndex(fameLv: number, fameEffect: IConfigFriednFame) {
    let index: number = -1;
    for (let i = 0; i < fameEffect.costList.length; i++) {
      if (fameEffect.costList[i] <= fameLv) {
        index = i;
      }
    }
    return index;
  }

  public getFriendIds(
    isOwned: boolean,
    sort: FriendSort = FriendSort.DEFAULT,
    type: FriendType = FriendType.ALL
  ): number[] {
    if (isOwned) {
      if (!sort) {
        return this._ownedFriendIds.filter((vaule) => {
          return type == FriendType.ALL || FriendModule.config.getFriendById(vaule).type == type;
        });
      }
      switch (sort) {
        case FriendSort.DEFAULT:
          return this._ownedFriendIds
            .filter((vaule) => {
              return type == FriendType.ALL || FriendModule.config.getFriendById(vaule).type == type;
            })
            .sort((a, b) => this.sortDefault(a, b));
        case FriendSort.TALENT:
          return this._ownedFriendIds
            .filter((vaule) => {
              return type == FriendType.ALL || FriendModule.config.getFriendById(vaule).type == type;
            })
            .sort((a, b) => this.sortTalent(a, b));
        case FriendSort.FRIENDSHIP:
          return this._ownedFriendIds
            .filter((vaule) => {
              return type == FriendType.ALL || FriendModule.config.getFriendById(vaule).type == type;
            })
            .sort((a, b) => this.sortFriendship(a, b));
      }
    }
    return this._unOwnedFriendsIds
      .filter((vaule) => {
        return type == FriendType.ALL || FriendModule.config.getFriendById(vaule).type == type;
      })
      .sort((a, b) => this.sortDefault(a, b));
  }

  // 获取美名效果资质加成
  public getFriendFameQuality(attrId: number) {
    return this._fameQuality[attrId] ?? 0;
  }

  /**
   * 更新挚友技能洗练信息
   * @param friendId
   * @param index
   * @param data
   */
  public updateFriendCitySkill(friendId: number, index: number, data: FriendCitySkillMessage) {
    this._data[friendId].citySkillList[index] = data;
  }

  /**
   * 根据挚友id获取挚友数据
   * @param friendId
   * @returns
   */
  public getFriendMessage(friendId: number): FriendMessage {
    return this._data[friendId];
  }
  public setFriendMessage(friendId: number, data: FriendMessage) {
    this._data[friendId] = data;
    MsgMgr.emit(MsgEnum.ON_FRIEND_UPDATE);
  }
  public addFriendMessage(friendId: number, data: FriendMessage) {
    this._data[friendId] = data;
    this.updateFriendList();
    MsgMgr.emit(MsgEnum.ON_FRIEND_UPDATE);
  }
  public delFriendMessage(friendId: number) {
    delete this._data[friendId];
    this.updateFriendList();
  }

  /**
   * 根据英雄id获取挚友id列表
   * @param heroId
   * @returns
   */
  public getHeroFriends(heroId: number): number[] {
    return this._friends[heroId] || [];
  }

  /**
   * 根据挚友id获取英雄列表
   * @param friendId
   * @returns
   */
  public getFriendHeros(friendId: number): number[] {
    let friend: IConfigFriend = JsonMgr.instance.jsonList.c_friend[friendId];
    return friend.heroList;
  }

  /**
   * 获取对应美名的挚友ID列表
   * @param level
   * @returns
   */
  public getFriendBellesByLv(level: number): number[] {
    return this._friendBellesMap[level];
  }
  /**
   * 更新因果值
   * @param friendId
   * @param karma
   */
  public updateFriendKarma(friendId: number, karma: number) {
    let friend: FriendMessage = this._data[friendId];
    friend.karma = karma;
    this._data[friendId] = friend;
  }

  /**
   * 获取挚友因果值
   * @param friendId
   * @returns
   */
  public getFriendKarma(friendId: number) {
    return this._data[friendId]?.karma || 0;
  }

  /**
   * 获取友总因果值
   * @returns
   */
  public getTotalKarma(): number {
    let total = 0;
    Object.values(this._data).forEach((val) => {
      total += val.karma;
    });
    return total;
  }
  /**
   * 获取挚友才华
   * @param friendId
   * @returns
   */
  public getFriendDestiny(friendId: number): number {
    return this._data[friendId]?.destiny || 0;
  }
  /**
   * 获取总才华
   * @returns
   */
  public getTotalDestiny(): number {
    let total = 0;
    Object.values(this._data).forEach((val) => {
      total += val.destiny;
    });
    return total;
  }

  /**
   * 获取指定仙友据点属性加成
   */
  public getFriendCityAttrAdds(friendId: number) {
    const attrAdds = {
      1: 0,
      2: 0,
      3: 0,
      4: 0,
      5: 0,
    };
    let friendMessage = this.getFriendMessage(friendId);
    for (let i = 0; i < friendMessage.citySkillList.length; i++) {
      let citySkill = friendMessage.citySkillList[i];
      let friendSkill = JsonMgr.instance.jsonList.c_friendSkill[i + 1];
      attrAdds[friendSkill.type] += times(citySkill.skillAdd, 100);
    }
    return attrAdds;
  }
  /**
   * 获取仙友技能总数量
   * @returns
   */
  public getAllFriendCitySkillNums(): number {
    let numbers = 0;
    Object.values(this._data).forEach((element) => {
      let friendMessage = this.getFriendMessage(element.friendId);
      numbers += friendMessage.citySkillList.length;
    });
    return numbers;
  }
  /**
   * 获取所有仙友据点属性加成
   */
  public getAllFriendCityAttrAdds() {
    const attrAdds = {
      1: 0,
      2: 0,
      3: 0,
      4: 0,
      5: 0,
    };
    Object.values(this._data).forEach((element) => {
      let attr = this.getFriendCityAttrAdds(element.friendId);
      attrAdds[1] += attr[1];
      attrAdds[2] += attr[2];
      attrAdds[3] += attr[3];
      attrAdds[4] += attr[4];
      attrAdds[5] += attr[5];
    });
    return attrAdds;
  }
}
