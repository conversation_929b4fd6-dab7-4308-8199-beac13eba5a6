import { _decorator, instantiate, is<PERSON><PERSON><PERSON>, Node, Tween, tween } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { BadgeMgr, BadgeType } from "../../mgr/BadgeMgr";
import TickerMgr from "../../../lib/ticker/TickerMgr";
import { dtTime } from "../../BoutStartUp";
import ResMgr from "../../../lib/common/ResMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { DisciplinesAudioName } from "../../../module/disciplines/DisciplinesConfig";
const { ccclass, property } = _decorator;

enum Tag_Enum {
  Tag1 = 0,
  Tag2 = 1,
  Tag3 = 2,
  Tag4 = 3,
}

const mainList: Array<string> = ["main1", "main2", "main3", "main4"];

@ccclass("UIDisciplines")
export class UIDisciplines extends UINode {
  protected _openAct: boolean = true; //打开动作
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_HD_FUND}?prefab/ui/UIDisciplines`;
  }
  private _curTagIndex: Tag_Enum = Tag_Enum.Tag1;

  private _main_map: Map<Tag_Enum, Node> = new Map();
  private _main_load: Map<Tag_Enum, boolean> = new Map();
  private _main_get_Ticker: Map<Tag_Enum, number> = new Map();

  private on_click_btn_close() {
    // 关闭窗口 effect_guanbiyinxiao
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  private on_click_btn_type1() {
    AudioMgr.instance.playEffect(DisciplinesAudioName.Effect.点击下方页签);
    this.changeTag(Tag_Enum.Tag1);
  }
  private on_click_btn_type2() {
    AudioMgr.instance.playEffect(DisciplinesAudioName.Effect.点击下方页签);
    this.changeTag(Tag_Enum.Tag2);
  }
  private on_click_btn_type3() {
    AudioMgr.instance.playEffect(DisciplinesAudioName.Effect.点击下方页签);
    this.changeTag(Tag_Enum.Tag3);
  }
  private on_click_btn_type4() {
    AudioMgr.instance.playEffect(DisciplinesAudioName.Effect.点击下方页签);
    this.changeTag(Tag_Enum.Tag4);
  }
  private changeTag(tag) {
    if (this._curTagIndex == tag) {
      return;
    }
    this._curTagIndex = tag;
    this.setTagMain();
  }

  private setTagMain() {
    switch (this._curTagIndex) {
      case Tag_Enum.Tag1:
        this.startMain1();
        break;
      case Tag_Enum.Tag2:
        this.startMain2();
        break;
      case Tag_Enum.Tag3:
        this.startMain3();
        break;
      case Tag_Enum.Tag4:
        this.startMain4();
        break;
    }
    this.change_btn();
  }

  private change_btn() {
    this.getNode("lay_bottom_btn_spr").children.forEach((val) => {
      val.getChildByName("no_pich").active = true;
      val.getChildByName("pich").active = false;
    });
    this.getNode("lay_bottom_btn_lbl").children.forEach((val) => {
      val.getChildByName("no_pich_lbl").active = true;
      val.getChildByName("pich_lbl").active = false;
    });

    let btn_type_spr = null;
    let btn_type = null;
    switch (this._curTagIndex) {
      case Tag_Enum.Tag1:
        btn_type_spr = this.getNode("btn_type1_spr");
        btn_type = this.getNode("btn_type1");
        break;
      case Tag_Enum.Tag2:
        btn_type_spr = this.getNode("btn_type2_spr");
        btn_type = this.getNode("btn_type2");
        break;
      case Tag_Enum.Tag3:
        btn_type_spr = this.getNode("btn_type3_spr");
        btn_type = this.getNode("btn_type3");
        break;
      case Tag_Enum.Tag4:
        btn_type_spr = this.getNode("btn_type4_spr");
        btn_type = this.getNode("btn_type4");
        break;
    }
    btn_type_spr.getChildByName("pich").active = true;
    btn_type_spr.getChildByName("no_pich").active = false;

    btn_type.getChildByName("pich_lbl").active = true;
    btn_type.getChildByName("no_pich_lbl").active = false;
  }

  private async startMain1() {
    this._main_map.forEach((val, key) => {
      val.active = false;
    });
    let main: Node = null;
    if (this._main_map.has(Tag_Enum.Tag1)) {
      main = this._main_map.get(Tag_Enum.Tag1);
      main.active = true;
    } else if (this._main_load.has(Tag_Enum.Tag1)) {
      this._main_get_Ticker.forEach((val) => {
        TickerMgr.clearInterval(val);
      });

      let id = TickerMgr.setInterval(
        dtTime,
        () => {
          main = this._main_map.get(Tag_Enum.Tag1);
          if (this._curTagIndex == Tag_Enum.Tag1) {
            main.active = true;
          }
        },
        true
      );
      this._main_get_Ticker.set(Tag_Enum.Tag1, id);
    } else {
      this._main_load.set(Tag_Enum.Tag1, true);
      ResMgr.loadPrefab(`${BundleEnum.BUNDLE_HD_FUND}?prefab/disciplines/main1`, (prefab) => {
        if (isValid(this.node) == false) {
          return;
        }
        let main: Node = instantiate(prefab);
        this.getNode("main_layer").addChild(main);
        main.walk((child) => (child.layer = this.node.layer));
        this._main_map.set(Tag_Enum.Tag1, main);

        if (this._curTagIndex == Tag_Enum.Tag1) {
          main.active = true;
        }
      });
    }
  }

  private startMain2() {
    this._main_map.forEach((val, key) => {
      val.active = false;
    });
    let main: Node = null;
    if (this._main_map.has(Tag_Enum.Tag2)) {
      main = this._main_map.get(Tag_Enum.Tag2);
      main.active = true;
    } else if (this._main_load.has(Tag_Enum.Tag2)) {
      this._main_get_Ticker.forEach((val) => {
        TickerMgr.clearInterval(val);
      });

      let id = TickerMgr.setInterval(
        dtTime,
        () => {
          main = this._main_map.get(Tag_Enum.Tag2);
          if (this._curTagIndex == Tag_Enum.Tag2) {
            main.active = true;
          }
        },
        true
      );
      this._main_get_Ticker.set(Tag_Enum.Tag2, id);
    } else {
      this._main_load.set(Tag_Enum.Tag2, true);
      ResMgr.loadPrefab(`${BundleEnum.BUNDLE_HD_FUND}?prefab/disciplines/main2`, (prefab) => {
        if (isValid(this.node) == false) {
          return;
        }
        let main: Node = instantiate(prefab);
        this.getNode("main_layer").addChild(main);
        main.walk((child) => (child.layer = this.node.layer));
        this._main_map.set(Tag_Enum.Tag2, main);

        if (this._curTagIndex == Tag_Enum.Tag2) {
          main.active = true;
        }
      });
    }
  }

  private startMain3() {
    this._main_map.forEach((val, key) => {
      val.active = false;
    });
    let main: Node = null;
    if (this._main_map.has(Tag_Enum.Tag3)) {
      main = this._main_map.get(Tag_Enum.Tag3);
      main.active = true;
    } else if (this._main_load.has(Tag_Enum.Tag3)) {
      this._main_get_Ticker.forEach((val) => {
        TickerMgr.clearInterval(val);
      });

      let id = TickerMgr.setInterval(
        dtTime,
        () => {
          main = this._main_map.get(Tag_Enum.Tag3);
          if (this._curTagIndex == Tag_Enum.Tag3) {
            main.active = true;
          }
        },
        true
      );
      this._main_get_Ticker.set(Tag_Enum.Tag3, id);
    } else {
      this._main_load.set(Tag_Enum.Tag3, true);
      ResMgr.loadPrefab(`${BundleEnum.BUNDLE_HD_FUND}?prefab/disciplines/main3`, (prefab) => {
        if (isValid(this.node) == false) {
          return;
        }
        let main: Node = instantiate(prefab);
        this.getNode("main_layer").addChild(main);
        main.walk((child) => (child.layer = this.node.layer));
        this._main_map.set(Tag_Enum.Tag3, main);

        if (this._curTagIndex == Tag_Enum.Tag3) {
          main.active = true;
        }
      });
    }
  }

  private startMain4() {
    this._main_map.forEach((val, key) => {
      val.active = false;
    });
    let main: Node = null;
    if (this._main_map.has(Tag_Enum.Tag4)) {
      main = this._main_map.get(Tag_Enum.Tag4);
      main.active = true;
    } else if (this._main_load.has(Tag_Enum.Tag4)) {
      this._main_get_Ticker.forEach((val) => {
        TickerMgr.clearInterval(val);
      });

      let id = TickerMgr.setInterval(
        dtTime,
        () => {
          main = this._main_map.get(Tag_Enum.Tag4);
          if (this._curTagIndex == Tag_Enum.Tag4) {
            main.active = true;
          }
        },
        true
      );
      this._main_get_Ticker.set(Tag_Enum.Tag4, id);
    } else {
      this._main_load.set(Tag_Enum.Tag4, true);
      ResMgr.loadPrefab(`${BundleEnum.BUNDLE_HD_FUND}?prefab/disciplines/main4`, (prefab) => {
        if (isValid(this.node) == false) {
          return;
        }
        let main: Node = instantiate(prefab);
        this.getNode("main_layer").addChild(main);
        main.walk((child) => (child.layer = this.node.layer));
        this._main_map.set(Tag_Enum.Tag4, main);

        if (this._curTagIndex == Tag_Enum.Tag4) {
          main.active = true;
        }
      });
    }
  }

  protected async onEvtShow() {
    this.setTagMain();
    BadgeMgr.instance.setBadgeId(this.getNode("btn_type1_red"), BadgeType.UITerritory.btn_xiuxing.btn_type1.id);
    BadgeMgr.instance.setBadgeId(this.getNode("btn_type2_red"), BadgeType.UITerritory.btn_xiuxing.btn_type2.id);
    BadgeMgr.instance.setBadgeId(this.getNode("btn_type3_red"), BadgeType.UITerritory.btn_xiuxing.btn_type3.id);
    BadgeMgr.instance.setBadgeId(this.getNode("btn_type4_red"), BadgeType.UITerritory.btn_xiuxing.btn_type4.id);
  }

  protected onEvtClose(): void {
    Tween.stopAllByTarget(this.node);
  }
}
