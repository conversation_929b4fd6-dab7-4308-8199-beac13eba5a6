import { _decorator, instantiate, Node } from "cc";
import { ListAdapter } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { FriendCityViewHolder } from "./FriendCityViewHolder";
import { FriendCitySkillMessage } from "../../../net/protocol/Friend";
import { FriendCityExpandViewHolder } from "./FriendCityExpandViewHolder";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

export class FriendCityAdapter extends ListAdapter {
  private item: Node;
  private expand: Node;
  private _datas: FriendCitySkillMessage[];
  private _friendId: number;
  private item2: Node;
  public constructor(item: Node, expand: Node, item2: Node) {
    super();
    this.item = item;
    this.expand = expand;
    this.item2 = item2;
  }

  public setFriendData(data: FriendCitySkillMessage[], friendId: number) {
    this._datas = [];
    this.notifyDataSetChanged();
    this._datas = data;
    this._friendId = friendId;
    this.notifyDataSetChanged();
  }
  public setDataOnly(data?: any[]) {
    this.notifyDataSetChanged(true);
  }
  getViewType(position: number): number {
    if (this.getItem(position)) {
      return 1;
    } else {
      return 2;
    }
  }
  getItem(position: number) {
    return this._datas[position];
  }
  onCreateView(viewType: number): Node {
    if (viewType == -1) {
      let itemExpand = instantiate(this.expand);
      itemExpand.getComponent(FriendCityExpandViewHolder).setContext(this);
      // log.log(itemExpand);
      return itemExpand;
    } else if (viewType == 1) {
      let item = instantiate(this.item);
      item.active = true;
      item.getComponent(FriendCityViewHolder).init();
      return item;
    } else {
      let item2 = instantiate(this.item2);
      item2.getComponent(FriendCityViewHolder).init();
      item2.active = true;
      return item2;
    }
  }
  onBindData(view: Node, position: number): void {
    view.getComponent(FriendCityViewHolder).updateData(this._friendId, position);
  }
  getCount(): number {
    // log.log("getCount", this._datas.length);
    return this._datas.length;
  }
}
