import { _decorator, Component, Node, RichText } from "cc";
import { AssetMgr, BundleEnum, ResHelper } from "../ResHelper";
import { AssetManager } from "cc";
import { Prefab } from "cc";
import { instantiate } from "cc";
import { Label, UIOpacity, v3 } from "cc";
import { Tween } from "cc";
import { director } from "cc";
import { BaseCtrl } from "../core/BaseCtrl";
import Tool from "../../../GameScrpit/lib/common/Tool";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

export interface TipsItem {
  content: string;
  showTime?: number;
  createTime?: number;
  node?: Node;
}

@ccclass("TipsCtrl")
export class TipsCtrl extends BaseCtrl {
  // 提示信息
  private _stack: TipsItem[] = [];

  // 提示起始位置
  private _startY = -250;

  // 自动释放间隔
  private _autoReleaseInterval = 7;

  protected onLoad(): void {}

  start() {
    director.addPersistRootNode(this.node.parent);
  }

  update(deltaTime: number) {
    this.updateMsgTips(deltaTime);
  }

  /**
   * 显示普通文本内容
   * @param content  文本内容
   */
  public showTip(content: string) {
    let itemFind = this._stack.find((item) => {
      return item.content == content;
    });

    if (itemFind) {
      itemFind.createTime = Date.now();
      if (itemFind.node) {
        itemFind.node.position = v3(0, this._startY, 0);
      }
      Tool.moveElementToEnd(this._stack, itemFind);
      return;
    }

    log.log(content);
    var item: TipsItem = {
      content: content,
      showTime: 2000,
      createTime: Date.now(),
    };

    this._stack.push(item);

    ResHelper.loadBundle(BundleEnum.BUNDLE_PUB, (bundle: AssetManager.Bundle) => {
      bundle.load("prefab/Tip", (err: any, prefab: Prefab) => {
        item.node = instantiate(prefab);
        item.node.getComponentInChildren(RichText).string = content;
        item.node.getComponent(UIOpacity).opacity = 0;
        item.node.position = v3(0, this._startY, 0);
        this.node.addChild(item.node);
      });
    });
  }

  // 更新普通的文字层提示
  private updateMsgTips(dt: number) {
    let currentTs = Date.now();
    for (let idx = this._stack.length - 1; idx >= 0; idx--) {
      let item = this._stack[idx];
      if (!item.node) {
        continue;
      }

      // 移除
      if (currentTs - item.createTime > item.showTime + 600) {
        this._stack.splice(idx, 1);
        item.node && item.node.destroy();
        continue;
      }

      // 更新位置
      let pos = item.node.position;
      let yMax = Math.max((this._stack.length - idx) * 80 + this._startY, pos.y);

      let y = dt * 400 + pos.y;
      y = Math.min(y, yMax);
      item.node.position = v3(pos.x, y, 0);

      // 透明度
      let overTime = currentTs - item.createTime - item.showTime;
      if (overTime > 0) {
        let opacity = item.node.getComponent(UIOpacity).opacity;
        opacity -= dt * 255 * 2;
        opacity = Math.max(opacity, 0);
        item.node.getComponent(UIOpacity).opacity = opacity;
      } else {
        let opacity = item.node.getComponent(UIOpacity).opacity;
        opacity += dt * 255 * 4;
        opacity = Math.min(opacity, 255);
        item.node.getComponent(UIOpacity).opacity = opacity;
      }
    }
  }

  /**
   *
   * @param node 要显示的提示节点层
   * @param liveTime 节点层的生存周期
   * @param nodeCd ? 倒计时节点
   */
  public showTipNode(node: Node, lifeTime: number, nodeCd?: Node) {
    this.node.addChild(node);
    node.walk((child) => (child.layer = this.node.layer));
    if (lifeTime > 0) {
      new Tween(node).delay(lifeTime).destroySelf().start();

      // 倒计时
      if (nodeCd && nodeCd.getComponent(Label)) {
        let tweenCd = new Tween(nodeCd.getComponent(Label));
        for (let i = 0; i < lifeTime; i++) {
          tweenCd
            .call(() => {
              nodeCd.getComponent(Label).string = `${lifeTime - i}秒后自动关闭`;
            })
            .delay(1);
        }
        tweenCd.start();
      }
    }
  }
}
