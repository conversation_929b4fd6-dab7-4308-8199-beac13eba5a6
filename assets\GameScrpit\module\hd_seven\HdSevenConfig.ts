import { ActivityID } from "../activity/ActivityConstant";
/**
 * 本地配置文件数据结构
 * 数据基础处理，过滤等，按id取
 */
export class HdSevenConfig {
  public activityConfig = {
    id: ActivityID.SEVEN_DAY,
    durationDay: 10,
    isLateSign: false,
    extentDayPerLateSign: 1,
    signList: [
      {
        rewardList: [6, 50, 1005, 20],
      },
      {
        rewardList: [1016, 10, 1049, 3],
      },
      {
        rewardList: [6, 50, 1035, 1],
      },
      {
        rewardList: [1015, 5, 1049, 6],
      },
      {
        rewardList: [6, 100, 1048, 2],
      },
      {
        rewardList: [1002, 3, 1033, 5],
      },
      {
        rewardList: [30302, 1, 1061, 5, 1064, 5, 1001, 5],
      },
    ],
  };
}
