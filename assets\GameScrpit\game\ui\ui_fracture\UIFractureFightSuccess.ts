import { _decorator, instantiate, sp } from "cc";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import FmUtils from "../../../lib/utils/FmUtils";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { routeConfig, RouteShowArgs } from "db://assets/platform/src/core/managers/RouteTableManager";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
const { ccclass, property } = _decorator;

/**
 * ivan_huang
 * Sun May 25 2025 10:39:50 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/ui_fracture/UIFractureFightSuccess.ts
 * https://docs.cocos.com/creator/3.8/manual/zh/
 *
 */

@ccclass("UIFractureFightSuccess")
@routeConfig({
  bundle: BundleEnum.BUNDLE_G_FRACTURE,
  url: "prefab/ui/UIFractureFightSuccess",
  nextHop: [],
  exit: "btn_close",
})
export class UIFractureFightSuccess extends BaseCtrl {
  private payload: { enemyName; fractureId; rewardList };
  init(args: RouteShowArgs): void {
    super.init(args);
    this.payload = args.payload;
  }
  protected start(): void {
    super.start();
    AudioMgr.instance.playEffect(AudioName.Effect.战斗胜利);
    this.getNode("spine_tiaozhancg").getComponent(sp.Skeleton).setAnimation(0, "zi_tiaozhanchenggong", false);
    this.getNode("spine_tiaozhancg")
      .getComponent(sp.Skeleton)
      .setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
        if ("zi_tiaozhanchenggong" == trackEntry.animation.name) {
          this.getNode("spine_tiaozhancg").getComponent(sp.Skeleton).setAnimation(0, "zi_tiaozhanchenggong_1", true);
        }
      });
    let i = 0;
    let childIndex = 0;
    for (; i < (this.payload.rewardList?.length ?? 0); i += 2) {
      let node = this.getNode("item_layout").children[childIndex];
      if (!node) {
        node = instantiate(this.getNode("item_layout").children[0]);
        this.getNode("item_layout").addChild(node);
      }
      node.active = true;
      FmUtils.setItemNode(node, this.payload.rewardList[i], this.payload.rewardList[i + 1]);
      childIndex++;
    }

    if (childIndex == 0) {
      this.getNode("node_award").active = false;
      this.getNode("spine_tiaozhancg").setPosition(0, 0);
    }

    for (; childIndex < this.getNode("item_layout").children.length; childIndex++) {
      this.getNode("item_layout").children[childIndex].active = false;
    }
    this.getNode("lbl_des");
  }
  private onClickClose() {
    this.closeBack();
  }
}
