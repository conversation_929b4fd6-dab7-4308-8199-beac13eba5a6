import { <PERSON>pi<PERSON><PERSON><PERSON>, ApiHandlerFail, ApiHandlerSuccess } from "../../game/mgr/ApiHandler";
import { AssistSubCmd, ClubSubCmd } from "../../game/net/cmd/CmdData";
import { AssistMessage, AssistResponse } from "../../game/net/protocol/Assist";
import {
  AuditOptionMessage,
  BargainRecordMessage,
  ClubAdjustPositionMessage,
  ClubApplyMessage,
  ClubBossBuddyMessage,
  ClubBossResponse,
  ClubBossTrainMessage,
  ClubCreateRequest,
  ClubDeadRewardResponse,
  ClubExamineMessage,
  ClubExamineRequest,
  ClubFormMessage,
  ClubHurtRewardResponse,
  ClubJoinResponse,
  ClubLogMessage,
  ClubMessage,
  ClubRewardMessage,
} from "../../game/net/protocol/Club";
import { RewardMessage } from "../../game/net/protocol/Comm";
import { BoolValue, IntValue, LongValue, StringValue } from "../../game/net/protocol/ExternalMessage";
import MsgMgr from "../../lib/event/MsgMgr";
import { ClubEvent } from "./ClubConstant";
import { ClubModule } from "./ClubModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);

export class ClubApi {
  // 接口方法，数据模块在这里改好，然后通知上层，下面这个是个例子
  // public improveSkill(friendId: number, rank: number, consumeitem: boolean, success?: ApiHandlerSuccess) {
  //   let data: FriendCitySkillRequest = {
  //     friendId: friendId,
  //     rank: rank,
  //     consumeItem: consumeitem,
  //   };
  //   log.log("apiImproveSkill", friendId, rank, consumeitem);
  //   ApiHandler.instance.request(
  //     FriendCitySkillMessage,
  //     FriendSubCmd.improveSkill,
  //     FriendCitySkillRequest.encode(data),
  //     (data: FriendCitySkillMessage) => {
  //       log.log(data);
  //       FriendModule.data.updateFriendCitySkill(friendId, rank, data);
  //       success && success(data);
  //     },
  //     (errorCode: any, msg: string, data: any) => {
  //       log.log(`${errorCode}`);
  //       log.log(data);
  //     }
  //   );
  // }
  public ownClub(success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    ApiHandler.instance.request(
      ClubFormMessage,
      ClubSubCmd.ownClub,
      null,
      (data: ClubFormMessage) => {
        //设置数据
        ClubModule.service.updatePlayerClubInfo(data.clubMessage);
        ClubModule.data.clubFormMessage = data;
        success && success(data);
      },
      error
    );
  }
  /**
   *
   * @param clubName 联盟名字
   * @param clubDesc 联盟描述
   * @param avatar 联盟头像
   * @param success
   * @param error
   */
  public createClub(
    clubName: string,
    clubDesc: string,
    avatar: string,
    success?: ApiHandlerSuccess,
    error?: ApiHandlerFail
  ) {
    let data: ClubCreateRequest = {
      name: clubName,
      slogan: clubDesc,
      avatar: avatar,
    };
    ApiHandler.instance.request(
      ClubMessage,
      ClubSubCmd.createClub,
      ClubCreateRequest.encode(data),
      (data: ClubMessage) => {
        log.log(data);
        ClubModule.service.updatePlayerClubInfo(data);
        ClubModule.data.clubMessage = data;
        success && success(data);
      },
      (errorCode: any, msg: string[], data: any) => {
        log.log(`${errorCode}`);
        log.log(data);
        error && error(errorCode, msg, data);
        return true;
      }
    );
  }
  //获取联盟列表
  public listClub(success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    ApiHandler.instance.list(
      ClubMessage,
      ClubSubCmd.listClub,
      null,
      (data: ClubMessage[]) => {
        //
        success && success(data);
      },
      error
    );
  }
  //加入仙盟
  public joinClub(clubId: number, success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    let data: LongValue = {
      value: clubId,
    };
    ApiHandler.instance.requestSync(
      ClubJoinResponse,
      ClubSubCmd.joinClub,
      LongValue.encode(data),
      (data: ClubJoinResponse) => {
        if (data?.clubMessage) {
          ClubModule.data.clubMessage = data.clubMessage;
          ClubModule.data.clubFormMessage.clubId = data.clubMessage.id;
        } else {
          ClubModule.service.updateClubApplyState(data);
        }
        success && success(data);
      },
      error
    );
  }
  //获取仙盟申请列表
  public listApply(success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    ApiHandler.instance.list(
      ClubApplyMessage,
      ClubSubCmd.listApply,
      null,
      (data: ClubApplyMessage[]) => {
        success && success(data);
      },
      error
    );
  }

  /**
   * 审批加入仙盟的请求,前端需要将申请记录删除
   * @param userId
   * @param pass
   * @param success
   * @param error
   */
  public examineApply(userId: number, pass: boolean, success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    let data: ClubExamineRequest = {
      userId: userId,
      pass: pass,
    };
    ApiHandler.instance.request(
      ClubExamineMessage,
      ClubSubCmd.examineApply,
      ClubExamineRequest.encode(data),
      (data: ClubExamineMessage) => {
        if (data.code == 1) {
          this.ownClub(); //有新成员加入
        }
        success && success(data);
      },
      error
    );
  }

  /**
   * 修改仙盟的审核配置
   * @param manual
   * @param autoRefuse
   * @param minLevel
   * @param minPower
   * @param success
   * @param error
   */
  public updateAudit(
    manual: boolean,
    autoRefuse: boolean,
    minLevel: number,
    minPower: number,
    success?: ApiHandlerSuccess,
    error?: ApiHandlerFail
  ) {
    let data: AuditOptionMessage = {
      manual: manual,
      autoRefuse: autoRefuse,
      levelLowerLimit: minLevel,
      powerLowerLimit: minPower,
    };
    ApiHandler.instance.request(
      BoolValue,
      ClubSubCmd.updateAudit,
      AuditOptionMessage.encode(data),
      (data: BoolValue) => {
        ClubModule.data.clubMessage.auditOption.manual = manual;
        ClubModule.data.clubMessage.auditOption.autoRefuse = autoRefuse;
        success && success(data);
      },
      error
    );
  }
  /**
   * 主动退盟
   * @param success
   * @param error
   */
  public leaveBySelf(success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    ApiHandler.instance.request(
      ClubFormMessage,
      ClubSubCmd.leaveBySelf,
      null,
      (data: ClubFormMessage) => {
        data.clubMessage = null;
        ClubModule.data.clubFormMessage = data;
        success && success(data);
      },
      error
    );
  }
  /**
   * 踢出仙盟
   * @param userId
   * @param success
   * @param error
   */
  public kickOutClub(userId: number, success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    let data: LongValue = {
      value: userId,
    };
    ApiHandler.instance.request(
      BoolValue,
      ClubSubCmd.kickOutClub,
      LongValue.encode(data),
      (data: BoolValue) => {
        if (data) {
          this.ownClub(() => {
            success && success(data);
          });
        }
      },
      error
    );
  }
  /**
   * 委任职务 返回职务变动的集合
   * @param userId
   * @param position
   * @param success
   * @param error
   */
  public adjustPosition(userId: number, position: number, success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    let data: ClubAdjustPositionMessage = {
      userId: userId,
      position: position,
    };
    ApiHandler.instance.list(
      ClubAdjustPositionMessage,
      ClubSubCmd.adjustPosition,
      ClubAdjustPositionMessage.encode(data),
      (data: ClubAdjustPositionMessage[]) => {
        this.ownClub(() => {
          success && success(data);
        });
        ClubModule.service.updateAllMemberPosition(data);
      },
      error
    );
  }
  /**
   * 查看仙盟名称是否已存在 false不存在 true存在
   * @param clubName
   * @param success
   * @param error
   */
  public clubNameExist(clubName: string, success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    let data: StringValue = {
      value: clubName,
    };
    ApiHandler.instance.request(
      BoolValue,
      ClubSubCmd.clubNameExist,
      StringValue.encode(data),
      (data: BoolValue) => {
        success && success(data);
      },
      error
    );
  }

  /**
   * 修改名称
   * @param clubName
   * @param success
   * @param error
   */
  public modifyName(clubName: string, success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    let data: StringValue = {
      value: clubName,
    };
    ApiHandler.instance.requestSync(
      StringValue,
      ClubSubCmd.renameClubName,
      StringValue.encode(data),
      (data: StringValue) => {
        let clubMessage = ClubModule.data.clubMessage;
        clubMessage.name = data.value;
        ClubModule.data.clubMessage = clubMessage;
        success && success(data);
      },
      (errorCode: any, msg: [string], data: any) => {
        log.log(`${errorCode}`);
        log.log(data);
        error && error(errorCode, msg, data);
        return true;
      }
    );
  }
  /**
   * 修改头像
   * @param avatar
   * @param success
   * @param error
   */
  public modifyAvatar(avatar: string, success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    let data: StringValue = {
      value: avatar,
    };
    ApiHandler.instance.request(StringValue, ClubSubCmd.modifyAvatar, StringValue.encode(data), (data: StringValue) => {
      let clubmessage = ClubModule.data.clubMessage;
      clubmessage.avatar = avatar;
      ClubModule.data.clubMessage = clubmessage;
      success && success(data);
    });
  }

  /**
   * 修改公告
   * @param slogan
   * @param success
   * @param error
   */
  public modifySlogan(slogan: string, success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    let data: StringValue = {
      value: slogan,
    };
    ApiHandler.instance.request(StringValue, ClubSubCmd.modifySlogan, StringValue.encode(data), (data: StringValue) => {
      let clubmessage = ClubModule.data.clubMessage;
      clubmessage.slogan = slogan;
      ClubModule.data.clubMessage = clubmessage;
      success && success(data);
    });
  }
  /**
   * 通过关键字查询
   * @param key
   * @param success
   * @param error
   */
  public searchByKey(key: string, success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    let data: StringValue = {
      value: key,
    };
    ApiHandler.instance.list(
      ClubMessage,
      ClubSubCmd.searchByKey,
      StringValue.encode(data),
      (data: ClubMessage[]) => {
        success && success(data);
      },
      error
    );
  }
  /**
   * 一键拒绝全部入会请求
   * @param success
   * @param error
   */
  public oneClickRefuse(success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    ApiHandler.instance.request(
      BoolValue,
      ClubSubCmd.oneClickRefuse,
      null,
      (data: BoolValue) => {
        success && success(data);
      },
      error
    );
  }
  /**
   * 领取仙盟任务奖励
   * @param taskId
   * @param success
   * @param error
   */
  public takeTaskReward(taskId: number, success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    let data: LongValue = {
      value: taskId,
    };
    ApiHandler.instance.requestSync(
      ClubRewardMessage,
      ClubSubCmd.takeTaskReward,
      LongValue.encode(data),
      (data: ClubRewardMessage) => {
        ClubModule.service.updateClubTaskState(data);
        success && success(data);
      },
      error
    );
  }
  /**
   * 领取活跃度奖励
   * @param activeIndex 活跃度任务的索引，即配置表的第几个，从索引0开始
   * @param success
   * @param error
   */
  public takeActiveReward(activeIndex: number, success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    let data: IntValue = {
      value: activeIndex,
    };
    ApiHandler.instance.requestSync(
      ClubRewardMessage,
      ClubSubCmd.takeActiveReward,
      IntValue.encode(data),
      (data: ClubRewardMessage) => {
        ClubModule.service.updateClubTaskState(data);
        success && success(data);
      },
      error
    );
  }
  /**
   * 捐献
   * @param donateId  配置捐献ID
   * @param success
   * @param error
   */
  public donate(donateId: number, success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    let data: LongValue = {
      value: donateId,
    };
    ApiHandler.instance.requestSync(
      ClubRewardMessage,
      ClubSubCmd.donate,
      LongValue.encode(data),
      (data: ClubRewardMessage) => {
        log.log(data);
        if (data?.donateMap) {
          ClubModule.data.clubFormMessage.donateMap = data.donateMap;
        }
        if (data?.dailyTask) {
          ClubModule.data.clubFormMessage.dailyTask = data.dailyTask;
        }
        ClubModule.service.updateClubDonateState(data);
        let clubMesssage = ClubModule.data.clubMessage;
        clubMesssage.exp = data.clubExp;
        clubMesssage.level = data.clubLevel;
        ClubModule.data.clubMessage = clubMesssage;
        success && success(data);
      },
      error
    );
  }
  /**
   * 获取仙盟日志
   * @param success
   * @param error
   */
  public log(success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    ApiHandler.instance.list(
      ClubLogMessage,
      ClubSubCmd.log,
      null,
      (data: ClubLogMessage[]) => {
        success && success(data);
      },
      error
    );
  }
  /**
   *  获取所有仙盟解锁的BOSS的战斗情况
   * @param success
   * @param error
   */
  public getBossTrain(success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    ApiHandler.instance.request(
      ClubBossBuddyMessage,
      ClubSubCmd.getBossTrain,
      null,
      (data: ClubBossBuddyMessage) => {
        ClubModule.data.bossMessage = data;
        log.log(data);
        success && success(data);
      },
      error
    );
  }
  /**
   * BOSS战斗
   * @param bossId
   * @param isItem
   * @param success
   * @param error
   */
  public killBoss(isItem: boolean, success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    let data: BoolValue = {
      value: isItem,
    };
    ApiHandler.instance.requestSync(
      ClubBossResponse,
      ClubSubCmd.killBoss,
      BoolValue.encode(data),
      (data: ClubBossResponse) => {
        // ClubModule.service.updateBossMessage(data.bossMessage);
        ClubModule.data.bossChance = data.bossChance;
        let message = ClubModule.data.clubMessage;
        message.unlockBossCnt = data.unlockBossCnt;
        ClubModule.data.clubMessage = message;
        this.ownClub(() => {
          success && success(data);
        });
      },
      error
    );
  }
  /**
   * 召唤boss
   * @param bossId
   * @param success
   * @param error
   */
  public callBoss(bossId: number, success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    let data: LongValue = {
      value: bossId,
    };
    log.log(data);
    ApiHandler.instance.requestSync(
      ClubBossTrainMessage,
      ClubSubCmd.callBoss,
      LongValue.encode(data),
      (data: ClubBossTrainMessage) => {
        log.log(data);
        success && success(data);
      },
      error
    );
  }
  /**
   * 当BOSS死亡后用于领取奖励
   * @param bossId
   * @param success
   * @param error
   */
  public takeBossDeadReward(bossId: number, success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    let data: LongValue = {
      value: bossId,
    };
    ApiHandler.instance.requestSync(
      ClubDeadRewardResponse,
      ClubSubCmd.takeBossDeadReward,
      LongValue.encode(data),
      (data: ClubDeadRewardResponse) => {
        success && success(data);
      },
      error
    );
  }
  /**
   * 当BOSS伤害达成后用于领取奖励
   *
   */
  public takeBossHurtReward(index: number, success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    let data: LongValue = {
      value: index,
    };
    log.log(data);
    ApiHandler.instance.requestSync(
      ClubHurtRewardResponse,
      ClubSubCmd.takeBossHurtReward,
      LongValue.encode(data),
      (data: ClubHurtRewardResponse) => {
        log.log(data);
        ClubModule.data.bossMessage.bossTrain.hurtRewardIndexList = data.hurtRewardTakeList;
        MsgMgr.emit(ClubEvent.CLUB_DATA_CHANGE);
        success && success(data);
      },
      error
    );
  }

  /**
   * 显示被踢出信息后通知服务端
   * @param success
   * @param error
   */
  public clubLeaveShow(success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    ApiHandler.instance.request(
      BoolValue,
      ClubSubCmd.clubLeaveShow,
      null,
      (data: BoolValue) => {
        ClubModule.data.clubFormMessage.clubMessage = null;
        ClubModule.data.clubFormMessage.popUpMessage = null;
        success && success(data);
      },
      error
    );
  }

  public testCorrectClub(success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    ApiHandler.instance.request(
      IntValue,
      ClubSubCmd.testCorrectClub,
      null,
      (data: IntValue) => {
        log.log("======联盟砍价测试接口========");
        log.log(data);
        success && success(data);
      },
      error
    );
  }

  public addBargain(success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    ApiHandler.instance.requestSync(
      BargainRecordMessage,
      ClubSubCmd.addBargain,
      null,
      (data: BargainRecordMessage) => {
        let clubMessage = ClubModule.data.clubMessage;
        clubMessage.bargain.recordMap[data.userId] = data;
        ClubModule.data.clubMessage = clubMessage;
        success && success(data);
      },
      error
    );
  }

  public buyBargain(success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    ApiHandler.instance.requestSync(
      RewardMessage,
      ClubSubCmd.buyBargain,
      null,
      (data: RewardMessage) => {
        this.ownClub(() => {
          success && success(data);
        });
      },
      error
    );
  }

  /**
   * 获取所有的求助列表
   * @param success
   * @param error
   */
  public allAssistList(success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    ApiHandler.instance.list(
      AssistMessage,
      AssistSubCmd.allAssistList,
      null,
      (data: AssistMessage[]) => {
        success && success(data);
      },
      error
    );
  }
  public doAssist(id: number, success?: ApiHandlerSuccess, fail?: ApiHandlerFail) {
    ApiHandler.instance.requestSync(
      AssistResponse,
      AssistSubCmd.doAssist,
      LongValue.encode({ value: id }),
      (data: AssistResponse) => {
        success?.(data);
      }
    );
  }
}
