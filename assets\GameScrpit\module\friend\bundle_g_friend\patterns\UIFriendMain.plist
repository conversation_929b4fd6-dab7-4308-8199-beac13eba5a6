<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>XY_bg_9g_lansetanchaung_nei.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{54,49}</string>
                <key>spriteSourceSize</key>
                <string>{54,49}</string>
                <key>textureRect</key>
                <string>{{1427,330},{54,49}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>bg_toanmingyinguodi.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{361,54}</string>
                <key>spriteSourceSize</key>
                <string>{361,54}</string>
                <key>textureRect</key>
                <string>{{1,303},{361,54}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_xianyou_dibu.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{750,300}</string>
                <key>spriteSourceSize</key>
                <string>{750,300}</string>
                <key>textureRect</key>
                <string>{{1,1},{750,300}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn_hudong.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{137,137}</string>
                <key>spriteSourceSize</key>
                <string>{137,137}</string>
                <key>textureRect</key>
                <string>{{364,303},{137,137}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn_jiahao_jingli.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{40,41}</string>
                <key>spriteSourceSize</key>
                <string>{40,41}</string>
                <key>textureRect</key>
                <string>{{1404,386},{40,41}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>btn_jinengjiacheng.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{97,97}</string>
                <key>spriteSourceSize</key>
                <string>{97,97}</string>
                <key>textureRect</key>
                <string>{{1206,339},{97,97}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn_meiminglu.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{97,97}</string>
                <key>spriteSourceSize</key>
                <string>{97,97}</string>
                <key>textureRect</key>
                <string>{{1305,339},{97,97}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icon_tianming.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{40,35}</string>
                <key>spriteSourceSize</key>
                <string>{40,41}</string>
                <key>textureRect</key>
                <string>{{1447,386},{40,35}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icon_tianming_big.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{1,-14}</string>
                <key>spriteSize</key>
                <string>{451,425}</string>
                <key>spriteSourceSize</key>
                <string>{453,453}</string>
                <key>textureRect</key>
                <string>{{753,1},{451,425}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icon_yinguo_big.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{-9,-3}</string>
                <key>spriteSize</key>
                <string>{305,327}</string>
                <key>spriteSourceSize</key>
                <string>{453,453}</string>
                <key>textureRect</key>
                <string>{{1206,1},{305,327}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>line_fengexian_xianyou.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{219,7}</string>
                <key>spriteSourceSize</key>
                <string>{219,7}</string>
                <key>textureRect</key>
                <string>{{1206,330},{219,7}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>UIFriendMain.png</string>
            <key>size</key>
            <string>{1512,441}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:073bf069ca7fec701ae3cfce8e810954:85788d00da2e9881745a57410334453d:1725ef4ed301ed597b97b8e64c5ed9ff$</string>
            <key>textureFileName</key>
            <string>UIFriendMain.png</string>
        </dict>
    </dict>
</plist>
