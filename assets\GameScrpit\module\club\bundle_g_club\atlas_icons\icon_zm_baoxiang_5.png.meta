{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "0fdaf2a0-a180-4dbf-8a61-75e79f66f5a9", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "0fdaf2a0-a180-4dbf-8a61-75e79f66f5a9@6c48a", "displayName": "icon_zm_baoxiang_5", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "0fdaf2a0-a180-4dbf-8a61-75e79f66f5a9", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "0fdaf2a0-a180-4dbf-8a61-75e79f66f5a9@f9941", "displayName": "icon_zm_baoxiang_5", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 81, "height": 76, "rawWidth": 81, "rawHeight": 76, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-40.5, -38, 0, 40.5, -38, 0, -40.5, 38, 0, 40.5, 38, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 76, 81, 76, 0, 0, 81, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-40.5, -38, 0], "maxPos": [40.5, 38, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "0fdaf2a0-a180-4dbf-8a61-75e79f66f5a9@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "0fdaf2a0-a180-4dbf-8a61-75e79f66f5a9@6c48a"}}