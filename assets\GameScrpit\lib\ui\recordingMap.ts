import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class Recording {
  node: any;
  uiName: string;
  keep: boolean;
  relevanceUIList: any[];
  isNoHide?: boolean = false;
  music?: number = 0;
}

export class RecordingMap {
  private static _instance: RecordingMap = null;
  public static get instance(): RecordingMap {
    if (RecordingMap._instance == null) {
      RecordingMap._instance = new RecordingMap();
    }
    return RecordingMap._instance;
  }

  private _UIMap: Map<string, Recording> = new Map<string, Recording>();

  public addRecording(key: string, data: Recording) {
    this._UIMap.set(key, data);
  }

  public clear() {
    this._UIMap.clear();
  }

  public get UIMap(): Map<string, any> {
    return this._UIMap;
  }

  public getMapKeyInfo(key: string) {
    if (this._UIMap.has(key)) {
      return this._UIMap.get(key);
    }
    log.error("getMapKeyInfo 界面没有注册======" + key);
    // let data = new Recording();
    // data = {
    //   node: null,
    //   uiName: key,
    //   keep: false,
    //   relevanceUIList: [],
    // };
    return null;
  }
}
