import { _decorator, director, is<PERSON><PERSON><PERSON>, <PERSON><PERSON>, sp } from "cc";

import { Vec3 } from "cc";
import { v3 } from "cc";
import { tween } from "cc";
import { UITransform } from "cc";
import { instantiate } from "cc";
import { view } from "cc";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { GuideRouteEnum } from "db://assets/GameScrpit/ext_guide/GuideDefine";
import { CityCtrl } from "db://assets/GameScrpit/game/ui/ui_gameMap/CityCtrl";
import MsgMgr from "../../../lib/event/MsgMgr";
import { StartUp } from "../../../lib/StartUp";
import { NodeTool } from "../../../lib/utils/NodeTool";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import MsgEnum from "../../event/MsgEnum";
import { Sleep, SystemOpenEnum } from "../../GameDefine";
import { GameDirector } from "../../GameDirector";
import { JsonMgr } from "../../mgr/JsonMgr";
import { BaseCtrl } from "../../../../platform/src/core/BaseCtrl";
import { GameMapTrim } from "./prefab_ctrl/GameMapTrim";
import { CityRouteName } from "../../../module/city/CityConstant";
import { RewardRouteEnum } from "../../../ext_reward/RewardDefine";
import { CityModule } from "../../../module/city/CityModule";
import { OtherBuildIdEnum } from "../../../module/city/CityConstant";
import { BadgeMgr, BadgeType } from "../../mgr/BadgeMgr";
import { FightModule } from "../../../module/fight/src/FightModule";
import { CityEvent } from "../../../module/city/CityEvent";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { MainTaskModule } from "../../../module/mainTask/MainTaskModule";

import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { TopEventRoute } from "../../../ext_guide/TopEventRoute";
import { EventCtrlManager } from "./event_ctrl/EventCtrlManager";
import { SpGuide103002 } from "../../../module/fight/src/FightConstant";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { HdShouChongRouteItem } from "../../../module/hd_shouchong/HdShouChongRoute";
import GuideMgr from "../../../ext_guide/GuideMgr";
import { EventActionModule } from "../../../module/event_action/src/EventActionModule";

const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UIGameMapCtrl")
export class UIGameMapCtrl extends BaseCtrl {
  @property(Node)
  private nodeGround: Node = null;

  @property(Node)
  private btnMap: Node = null;

  // 自动设置层级
  protected autoSetLayer: boolean = false;

  // 待加载的结点列表
  private _nodePrefabList: { name: string; parent: Node; dependence?: string[]; afterNode?: string }[] = [];

  // 天空特效结点
  private _nodeAir: Node;

  // 关注的城市id
  private _cityId: number = 0;

  // 是否关注中
  private isFocus: boolean = false;

  // 是否在显示新功能
  private isPlaySystemOpen = false;

  private _isEventCheckBool: boolean = false;

  protected onLoad(): void {
    super.onLoad();

    this._nodeAir = this.getNode("node_air");

    MsgMgr.on(MsgEnum.ON_CITY_FOCOUS, this.onCityFocus, this);
    MsgMgr.on(MsgEnum.ON_CITY_UN_FOCOUS, this.onCityUnFocus, this);
    MsgMgr.on(MsgEnum.UIReady, this.onSystemOpenCheck, this);
    MsgMgr.on(MsgEnum.UIReady, this.cloudCheck, this);
    MsgMgr.on(MsgEnum.UIReady, this.checkGuide, this);
    MsgMgr.on(MsgEnum.ON_THIEF_FOCOUS, this.onFocusThief, this);
    MsgMgr.on(MsgEnum.ON_THIEF_UNFOCOUS, this.onUnFocusThief, this);

    if (!GameDirector.instance.isSystemOpen(SystemOpenEnum.PUPIL_弟子系统)) {
      MsgMgr.on(MsgEnum.ON_RIGHT_UPDATE, this.loadPupil, this);
    }
    if (!GameDirector.instance.isSystemOpen(SystemOpenEnum.FARM_福地洞天)) {
      MsgMgr.on(MsgEnum.ON_RIGHT_UPDATE, this.loadFarm, this);
    }
    if (!GameDirector.instance.isSystemOpen(SystemOpenEnum.PET_宠物系统)) {
      MsgMgr.on(MsgEnum.ON_RIGHT_UPDATE, this.loadPet, this);
    }
  }
  protected onDestroy(): void {
    MsgMgr.off(MsgEnum.ON_CITY_FOCOUS, this.onCityFocus, this);
    MsgMgr.off(MsgEnum.ON_CITY_UN_FOCOUS, this.onCityUnFocus, this);
    MsgMgr.off(MsgEnum.UIReady, this.onSystemOpenCheck, this);
    MsgMgr.off(MsgEnum.UIReady, this.cloudCheck, this);
    MsgMgr.off(MsgEnum.UIReady, this.checkGuide, this);
    MsgMgr.off(MsgEnum.UIReady, this.eventCheck, this);
    MsgMgr.off(MsgEnum.ON_RIGHT_UPDATE, this.loadPupil, this);
    MsgMgr.off(MsgEnum.ON_RIGHT_UPDATE, this.loadFarm, this);
    MsgMgr.off(MsgEnum.ON_RIGHT_UPDATE, this.loadPet, this);
    MsgMgr.off(MsgEnum.ON_THIEF_FOCOUS, this.onFocusThief, this);
    MsgMgr.off(MsgEnum.ON_THIEF_UNFOCOUS, this.onUnFocusThief, this);
    this.assetMgr.release();
    EventCtrlManager.instance.remove();
  }

  async start() {
    super.start();

    this.getNode("node_cloud").active = true;
    this.getNode("UITalk").active = true;

    this._nodePrefabList = [
      { name: "prefab/trim/trim_1003", parent: this.nodeGround },
      { name: "prefab/trim/trim_1002", parent: this.nodeGround },
      { name: "prefab/trim/trim_1211", parent: this.nodeGround },

      { name: "prefab/trim/trim_102", parent: this.nodeGround },
      { name: "prefab/trim/trim_1214", parent: this.nodeGround },

      { name: "prefab/trim/trim_101", parent: this.nodeGround },

      { name: "prefab/trim/trim_103", parent: this.nodeGround },
      { name: "prefab/trim/trim_104", parent: this.nodeGround },

      { name: "prefab/building/city_110", parent: this.nodeGround, dependence: [BundleEnum.BUNDLE_CITY_110] },
      { name: "prefab/building/city_101", parent: this.nodeGround, dependence: [BundleEnum.BUNDLE_CITY_101] },
      { name: "prefab/building/city_102", parent: this.nodeGround, dependence: [BundleEnum.BUNDLE_CITY_102] },
      { name: "prefab/building/city_103", parent: this.nodeGround, dependence: [BundleEnum.BUNDLE_CITY_103] },

      { name: "prefab/trim/trim_1212", parent: this.nodeGround },
      { name: "prefab/trim/trim_1215", parent: this.nodeGround },
      { name: "prefab/building/city_105", parent: this.nodeGround, dependence: [BundleEnum.BUNDLE_CITY_105] },

      { name: "prefab/building/city_104", parent: this.nodeGround, dependence: [BundleEnum.BUNDLE_CITY_106] },
      { name: "prefab/trim/trim_2201", parent: this.nodeGround },
      { name: "prefab/trim/trim_1213", parent: this.nodeGround },

      { name: "prefab/building/city_106", parent: this.nodeGround, dependence: [BundleEnum.BUNDLE_CITY_104] },
      { name: "prefab/building/city_107", parent: this.nodeGround, dependence: [BundleEnum.BUNDLE_CITY_107] },
      { name: "prefab/building/city_108", parent: this.nodeGround, dependence: [BundleEnum.BUNDLE_CITY_108] },
      { name: "prefab/building/city_109", parent: this.nodeGround, dependence: [BundleEnum.BUNDLE_CITY_109] },

      { name: "prefab/trim/trim_105", parent: this.nodeGround },

      { name: "prefab/building/city_111", parent: this.nodeGround, dependence: [BundleEnum.BUNDLE_CITY_111] },
      { name: "prefab/building/city_112", parent: this.nodeGround, dependence: [BundleEnum.BUNDLE_CITY_112] },
      { name: "prefab/building/city_113", parent: this.nodeGround, dependence: [BundleEnum.BUNDLE_CITY_113] },
      { name: "prefab/building/city_114", parent: this.nodeGround, dependence: [BundleEnum.BUNDLE_CITY_114] },
      { name: "prefab/building/city_115", parent: this.nodeGround, dependence: [BundleEnum.BUNDLE_CITY_115] },

      { name: "prefab/trim/trim_107", parent: this.nodeGround },

      { name: "prefab/trim/trim_110", parent: this.nodeGround },
      { name: "prefab/trim/trim_111", parent: this.nodeGround },
      { name: "prefab/trim/trim_1111", parent: this.nodeGround },

      { name: "prefab/trim/trim_201", parent: this.nodeGround },
      { name: "prefab/trim/trim_2011", parent: this.nodeGround },
      { name: "prefab/trim/trim_202", parent: this.nodeGround },
      { name: "prefab/trim/trim_2021", parent: this.nodeGround },
      { name: "prefab/trim/trim_203", parent: this.nodeGround },
      { name: "prefab/trim/trim_204", parent: this.nodeGround },
      { name: "prefab/trim/trim_2041", parent: this.nodeGround },
      { name: "prefab/trim/trim_205", parent: this.nodeGround },
      { name: "prefab/trim/trim_206", parent: this.nodeGround },
      { name: "prefab/trim/trim_2061", parent: this.nodeGround },

      { name: "prefab/trim/trim_302", parent: this.nodeGround },
      { name: "prefab/trim/trim_303", parent: this.nodeGround },

      { name: "prefab/trim/trim_305", parent: this.nodeGround },

      { name: "prefab/trim/trim_401", parent: this.nodeGround },

      { name: "prefab/trim/trim_403", parent: this.nodeGround },
      { name: "prefab/trim/trim_404", parent: this.nodeGround },
      { name: "prefab/trim/trim_405", parent: this.nodeGround },
      { name: "prefab/trim/trim_406", parent: this.nodeGround },
      { name: "prefab/trim/trim_501", parent: this.nodeGround },
      { name: "prefab/trim/trim_502", parent: this.nodeGround },
      { name: "prefab/trim/trim_503", parent: this.nodeGround },
      { name: "prefab/trim/trim_504", parent: this.nodeGround },
      { name: "prefab/trim/trim_505", parent: this.nodeGround },
      { name: "prefab/trim/trim_506", parent: this.nodeGround },

      { name: "prefab/trim/trim_2202", parent: this.nodeGround },
      { name: "prefab/trim/trim_3201", parent: this.nodeGround },
      { name: "prefab/trim/trim_3202", parent: this.nodeGround },
      { name: "prefab/trim/trim_1001", parent: this.nodeGround },

      { name: "prefab/trim/trim_4201", parent: this.nodeGround },
      { name: "prefab/trim/trim_4202", parent: this.nodeGround },
      { name: "prefab/trim/trim_4203", parent: this.nodeGround },
      { name: "prefab/trim/trim_402", parent: this.nodeGround },

      { name: "prefab/effect/zcj_yunwu", parent: this._nodeAir },
      { name: "prefab/effect/zjm_xiaoyunwu", parent: this._nodeAir },
      { name: "prefab/effect/zcj_yunwu-001", parent: this._nodeAir },
      { name: "prefab/effect2/magma4", parent: this._nodeAir },

      { name: "prefab/effect_ground/ming_zu_li_zi_1", parent: this._nodeAir },
      { name: "prefab/effect_ground/ming_zu_li_zi_2", parent: this._nodeAir },
      { name: "prefab/effect_ground/rong_yan_animation", parent: this.nodeGround },
      { name: "prefab/effect_ground/ren_zu_shui_liu1", parent: this.nodeGround },
      { name: "prefab/effect_ground/ren_zu_shui_liu2", parent: this.nodeGround },
      { name: "prefab/effect_ground/yao_jie_shui_liu", parent: this.nodeGround },
      { name: "prefab/effect_ground/yao_zu_shui_jing", parent: this.nodeGround },
      { name: "prefab/effect_ground/ren_zu_he_hua", parent: this.nodeGround },
      { name: "prefab/effect_ground/ming_zu_bi_an_hua", parent: this.nodeGround },
      { name: "prefab/effect_ground/shen_zu_dian_light", parent: this.nodeGround },
      { name: "prefab/effect_ground/ren_pubu", parent: this.nodeGround },

      { name: "prefab/air/ming_zu_huo_yan", parent: this._nodeAir },
      { name: "prefab/air/wanyao_city_lizi", parent: this._nodeAir },
      { name: "prefab/air/wanyao_city_wind", parent: this._nodeAir },
      { name: "prefab/air/wu_zu_lei_dian", parent: this._nodeAir },

      { name: "prefab/air/wu_zu_lei_dian", parent: this._nodeAir },
      { name: "prefab/trim/trim_306", parent: this.nodeGround },
    ];

    // 装饰配置表同步
    const cfgBuildTrimReward = JsonMgr.instance.jsonList.c_buildTrimReward;
    const keys = Object.keys(cfgBuildTrimReward);
    for (let i = 0; i < keys.length; i++) {
      // 隐藏的建筑不加载
      const cfg = cfgBuildTrimReward[keys[i]];
      if (cfg.hide) {
        let idxHide = this._nodePrefabList.findIndex((item) => item.name === `prefab/trim/trim_${cfg.id}`);
        this._nodePrefabList.splice(idxHide, 1);
        continue;
      }

      // 已存在不加
      if (this._nodePrefabList.find((item) => item.name === `prefab/trim/trim_${cfg.id}`)) {
        continue;
      }

      // 新增的建筑，需要加载
      this._nodePrefabList.push({
        name: `prefab/trim/trim_${cfg.id}`,
        parent: this.nodeGround,
      });
    }

    // 加载新功能
    this.loadPupil();

    this.loadFarm();

    this.loadPet();

    // 引导检查
    const guideIdNow = PlayerModule.data.getPlayerInfo().guideId;
    if (guideIdNow < 401) {
      MsgMgr.once(MsgEnum.ON_SANJIE_EVENT_DATA_UPDATE, this.checkGuide, this);
      MsgMgr.on(MsgEnum.UIReady, this.eventCheck, this);
    }

    // 动态加载城市、特效
    await this.loadPrefab();

    this._isEventCheckBool = true;
    // EventActionModule.service.attachEvent()
    MsgMgr.on(MsgEnum.ON_SANJIE_EVENT_DATA_UPDATE, this.onEventUpdate, this);
    this.onEventUpdate();
    EventCtrlManager.instance.init(this.nodeGround);
  }

  private onEventUpdate() {
    let eventMap = EventActionModule.data.eventTrainMessage.eventMap;
    for (let id in eventMap) {
      EventActionModule.service.attachEvent(this.nodeGround, Number(id));
    }
  }

  /**
   * 加载弟子按钮
   */
  private loadPupil() {
    let aniSpine = this.getNode("spine_dizi").getComponent(sp.Skeleton);
    const nodeBtnPupil = this.getNode("btn_pupil");
    if (CityModule.data.cityAggregateMessage.otherCreateCityList.includes(OtherBuildIdEnum.弟子)) {
      aniSpine.setAnimation(0, "a01-1", true);
      nodeBtnPupil.getChildByName("btn_build").active = false;
    } else {
      aniSpine.setAnimation(0, "a0-1", true);
      nodeBtnPupil.getChildByName("btn_build").active = true;

      const isOpen = GameDirector.instance.isSystemOpen(SystemOpenEnum.PUPIL_弟子系统);
      nodeBtnPupil.getChildByPath("btn_build/disable").active = !isOpen;
      nodeBtnPupil.getChildByPath("btn_build/enable").active = isOpen;
      BadgeMgr.instance.setShowById(BadgeType.UIMajorCity.btn_pupil.btn_build.id, isOpen);
    }
  }

  public onBuildPupil() {
    this.getNode("btn_pupil").getChildByName("btn_build").active = false;
    let aniSpine = this.getNode("spine_dizi").getComponent(sp.Skeleton);
    aniSpine.node.active = true;
    aniSpine.setAnimation(0, "a01", false);
    aniSpine.setCompleteListener(() => {
      aniSpine.setCompleteListener(null);
      this.loadPupil();
    });

    let spineJianzaodonghua = this.getNode("btn_pupil")
      .getChildByName("spine_jianzaodonghua")
      .getComponent(sp.Skeleton);
    spineJianzaodonghua.node.active = true;
    spineJianzaodonghua.setAnimation(0, "jianzao", false);
    MsgMgr.off(MsgEnum.ON_RIGHT_UPDATE, this.loadPupil, this);
  }

  private loadFarm() {
    let aniSpine = this.getNode("spine_farm").getComponent(sp.Skeleton);
    const nodeBtnFarm = this.getNode("btn_farm");
    if (CityModule.data.cityAggregateMessage.otherCreateCityList.includes(OtherBuildIdEnum.福地)) {
      aniSpine.setAnimation(0, "a01-1", true);
      nodeBtnFarm.getChildByName("btn_build").active = false;
    } else {
      aniSpine.setAnimation(0, "a0-1", true);
      nodeBtnFarm.getChildByName("btn_build").active = true;

      const isOpen = GameDirector.instance.isSystemOpen(SystemOpenEnum.FARM_福地洞天);
      nodeBtnFarm.getChildByPath("btn_build/disable").active = !isOpen;
      nodeBtnFarm.getChildByPath("btn_build/enable").active = isOpen;
      BadgeMgr.instance.setShowById(BadgeType.UIMajorCity.btn_farm.btn_build.id, isOpen);
    }
  }

  public onBuildFarm() {
    this.getNode("btn_farm").getChildByName("btn_build").active = false;
    let aniSpine = this.getNode("spine_farm").getComponent(sp.Skeleton);
    aniSpine.node.active = true;
    aniSpine.setAnimation(0, "a01", false);
    aniSpine.setCompleteListener(() => {
      aniSpine.setCompleteListener(null);
      this.loadFarm();
    });

    let spineJianzaodonghua = this.getNode("btn_farm").getChildByName("spine_jianzaodonghua").getComponent(sp.Skeleton);
    spineJianzaodonghua.node.active = true;
    spineJianzaodonghua.setAnimation(0, "jianzao", false);
    MsgMgr.off(MsgEnum.ON_RIGHT_UPDATE, this.loadFarm, this);
  }
  private loadPet() {
    let aniSpine = this.getNode("spine_pet").getComponent(sp.Skeleton);
    const nodeBtnPet = this.getNode("btn_pet");
    if (CityModule.data.cityAggregateMessage.otherCreateCityList.includes(OtherBuildIdEnum.灵兽)) {
      aniSpine.setAnimation(0, "lingshou_appear", true);
      nodeBtnPet.getChildByName("btn_build").active = false;
    } else {
      aniSpine.setAnimation(0, "lingshou_disappear", true);
      nodeBtnPet.getChildByName("btn_build").active = true;

      const isOpen = GameDirector.instance.isSystemOpen(SystemOpenEnum.PET_宠物系统);
      nodeBtnPet.getChildByPath("btn_build/disable").active = !isOpen;
      nodeBtnPet.getChildByPath("btn_build/enable").active = isOpen;
      BadgeMgr.instance.setShowById(BadgeType.UIMajorCity.btn_pet.btn_build.id, isOpen);
    }
  }
  public onBuildPet() {
    this.getNode("btn_pet").getChildByName("btn_build").active = false;
    let aniSpine = this.getNode("spine_pet").getComponent(sp.Skeleton);
    aniSpine.node.active = true;
    // aniSpine.setAnimation(0, "lingshow_appear", false);
    // aniSpine.setCompleteListener(() => {
    //   aniSpine.setCompleteListener(null);
    //   this.loadPet();
    // });
    this.loadPet();

    let spineJianzaodonghua = this.getNode("btn_pet").getChildByName("spine_jianzaodonghua").getComponent(sp.Skeleton);
    spineJianzaodonghua.node.active = true;
    spineJianzaodonghua.setAnimation(0, "jianzao", false);
    MsgMgr.off(MsgEnum.ON_RIGHT_UPDATE, this.loadPet, this);
  }
  // private async loadTrim() {
  //   const keyList = Object.keys(JsonMgr.instance.jsonList.c_buildTrimReward);

  //   const orderList = [505, 1212, 1211, 1214, 105, 107, 1002, 2011, 1003, 402];
  //   for (let i = 0; i < keyList.length; i++) {
  //     if (!orderList.includes(Number(keyList[i]))) {
  //       orderList.push(Number(keyList[i]));
  //     }
  //   }

  //   for (let i = 0; i < orderList.length; i++) {
  //     while (isValid(this.node) == false) {
  //       await Sleep(1);
  //     }

  //     const cfgBuildTrimReward = JsonMgr.instance.jsonList.c_buildTrimReward[Number(orderList[i])];

  //     // 加载组件
  //     const nodeName = `trim_${cfgBuildTrimReward.id}`;
  //     let nodeTrim = this.nodeGround.getChildByName(nodeName);
  //     if (!nodeTrim) {
  //       // 实例化节点
  //       let pb = await this.assetMgr.loadPrefabSync(BundleEnum.BUNDLE_G_GAME_MAP, `prefab/trim/${nodeName}`);
  //       nodeTrim = instantiate(pb);
  //       this.nodeGround.addChild(nodeTrim);
  //     }

  //     nodeTrim.getComponent(GameMapTrim).init(cfgBuildTrimReward);
  //     await nodeTrim.getComponent(GameMapTrim).show();
  //   }
  // }

  /**
   * 按顺序加载prefab
   */
  public async loadPrefab() {
    for (let i = 0; this._nodePrefabList && i < this._nodePrefabList.length; i++) {
      let item = this._nodePrefabList[i];

      const sList = item.name.split("/");
      const nodeName = sList[sList.length - 1];

      let nodeAdd = item.parent.getChildByName(nodeName);
      if (nodeAdd == null) {
        if (nodeName.startsWith("trim_")) {
          const trimId = Number(nodeName.split("_")[1]);
          const cfg = JsonMgr.instance.jsonList.c_buildTrimReward[trimId];
          if (cfg.hide > 0) {
            continue;
          }
        }

        const pb = await this.assetMgr.loadPrefabSync(BundleEnum.BUNDLE_G_GAME_MAP, item.name, item.dependence);

        // 切换其他界面等1秒在来
        while (isValid(this.node) == false) {
          await Sleep(1);
        }

        nodeAdd = instantiate(pb);
        item.parent.addChild(nodeAdd);
      }

      nodeAdd.active = true;

      if (nodeName.startsWith("trim_")) {
        const trimId = Number(nodeName.split("_")[1]);
        const cfg = JsonMgr.instance.jsonList.c_buildTrimReward[trimId];
        nodeAdd.getComponent(GameMapTrim).init(cfg);
        await nodeAdd.getComponent(GameMapTrim).show();
      }
    }
  }

  // 移到坐标位置，并放大
  onCityFocus(cityId: number, worldPos: Vec3) {
    this._cityId = cityId;
    let nodeFocus = this.btnMap.getChildByPath("node_ground/city_" + cityId);

    // 节点原来被缩放的倍数
    let btnManage = nodeFocus.getChildByName("btn_manage");

    // 节点相对坐标
    let pos = this.btnMap.getComponent(UITransform).convertToNodeSpaceAR(btnManage.getWorldPosition());

    // 缩放倍数
    const x = btnManage.getComponent(UITransform).contentSize.x;
    let scaleOrigin = v3(x / 300, x / 300, 1);

    // 放大倍数
    let scaleTo = v3(1 / scaleOrigin.x, 1 / scaleOrigin.y, 1);

    // 缩放后的目标位置
    let posExp = v3(pos.x * scaleTo.x, pos.y * scaleTo.y);

    // 应该移动的位置
    let posEnd = v3(-posExp.x + worldPos.x, -posExp.y + worldPos.y, 1);

    // 移动最大距离修复
    let size = this.btnMap.getComponent(UITransform).contentSize;
    let maxX = (scaleTo.x * size.x) / 2 - view.getVisibleSize().x / 2;
    let minX = -maxX;
    let maxY = (scaleTo.y * size.y) / 2 - view.getVisibleSize().y / 2;
    let minY = -maxY;

    posEnd.x = Math.min(maxX, posEnd.x);
    posEnd.x = Math.max(minX, posEnd.x);
    posEnd.y = Math.max(minY, posEnd.y);
    posEnd.y = Math.max(minY, posEnd.y);

    tween(this.btnMap).to(0.5, { position: posEnd, scale: scaleTo }, { easing: "fade" }).start();

    this.switchNode(true, cityId);
  }

  onFocusThief(thiefNode: Node) {
    const scaleRate = 2;

    const posThief = thiefNode.getPosition();
    let parentNode = thiefNode.getParent();
    for (let i = 0; parentNode.name != this.btnMap.name && i < 32; i++) {
      posThief.x += parentNode.getPosition().x;
      posThief.y += parentNode.getPosition().y;
      parentNode = parentNode.getParent();
    }

    posThief.x *= -scaleRate;
    posThief.y *= -scaleRate;

    // 移动最大距离修复
    let size = this.btnMap.getComponent(UITransform).contentSize;
    let maxX = (scaleRate * size.x) / 2 - view.getVisibleSize().x / 2;
    let minX = -maxX;
    let maxY = (scaleRate * size.y) / 2 - view.getVisibleSize().y / 2;
    let minY = -maxY;

    posThief.x = Math.min(maxX, posThief.x);
    posThief.x = Math.max(minX, posThief.x);
    posThief.y = Math.max(minY, posThief.y);
    posThief.y = Math.max(minY, posThief.y);

    tween(this.btnMap)
      .to(0.3, { position: posThief, scale: v3(scaleRate, scaleRate, 1) }, { easing: "fade" })
      .call(() => {
        this.hideCityNode();
      })
      .start();
  }

  onUnFocusThief(thiefNode: Node): void {
    const posThief = thiefNode.getPosition();
    let parentNode = thiefNode.getParent();
    for (let i = 0; parentNode.name != this.btnMap.name && i < 32; i++) {
      posThief.x += parentNode.getPosition().x;
      posThief.y += parentNode.getPosition().y;
      parentNode = parentNode.getParent();
    }
    posThief.x *= -1;
    posThief.y *= -1;

    // 移动最大距离修复
    let size = this.btnMap.getComponent(UITransform).contentSize;
    let maxX = size.x / 2 - view.getVisibleSize().x / 2;
    let minX = -maxX;
    let maxY = size.y / 2 - view.getVisibleSize().y / 2;
    let minY = -maxY;

    posThief.x = Math.min(maxX, posThief.x);
    posThief.x = Math.max(minX, posThief.x);
    posThief.y = Math.max(minY, posThief.y);
    posThief.y = Math.max(minY, posThief.y);

    tween(this.btnMap)
      .to(0.5, { position: posThief, scale: v3(1, 1, 1) }, { easing: "fade" })
      .call(() => {
        this.hideCityNode();
      })
      .start();
  }

  async switchNode(isFocus: boolean, cityId: number) {
    this.isFocus = isFocus;

    for (let i = 0; i < this.nodeGround.children.length; i++) {
      await Sleep(0.01);
      let child = this.nodeGround.children[i];
      if (child.name === "city_" + cityId) {
        continue;
      }
      child.active = !isFocus;
    }

    // 福地图标
    this.btnMap.getChildByName("btn_farm").active = !isFocus;
    this.btnMap.getChildByName("btn_pupil").active = !isFocus;

    this.nodeGround.getChildByName(`city_${cityId}`).getComponent(CityCtrl).showBgNum = !isFocus;
  }

  public hideCityNode() {
    if (this.isFocus) {
      return;
    }

    let screenWidth = StartUp.instance.getVisibleSize().width;
    const nodeGround = this.nodeGround;
    for (let idx in nodeGround.children) {
      let nodeChild = nodeGround.children[idx];
      if (!nodeChild.getComponent(UITransform) || nodeChild.name.split("_")[0] == "path") {
        continue;
      }
      const rect = nodeChild.getComponent(UITransform).getBoundingBox();
      if (
        nodeChild.worldPosition.x + rect.width / 2 < -50 ||
        nodeChild.worldPosition.x - rect.width / 2 > screenWidth + 50
      ) {
        nodeChild.active = false;
      } else {
        nodeChild.active = true;
      }
    }
  }

  onCityUnFocus() {
    // 移动的世界节点
    const mapNode: Node = this.btnMap;

    const pos = mapNode.position;

    // 节点原来被缩放的倍数
    let scaleOrigin = mapNode.scale;

    // 缩放后的目标位置
    let posTo = v3(pos.x / scaleOrigin.x, pos.y / scaleOrigin.y, 1);

    // 坐标修复，不能跑到屏幕外
    let size = mapNode.getComponent(UITransform).contentSize;
    const visibleSize = StartUp.instance.getVisibleSize();

    let maxX = size.x / 2 - visibleSize.width / 2;
    let maxY = size.y / 2 - visibleSize.height / 2;

    if (posTo.x > maxX) {
      posTo.x = maxX;
    } else if (posTo.x < -maxX) {
      posTo.x = -maxX;
    }

    if (posTo.y > maxY) {
      posTo.y = maxY;
    } else if (posTo.y < -maxY) {
      posTo.y = -maxY;
    }

    tween(mapNode)
      .to(0.2, { position: posTo, scale: v3(1, 1, 1) }, { easing: "fade" })
      .call(() => {
        this.hideCityNode();
      })
      .start();

    if (this._cityId) {
      this.switchNode(false, this._cityId);
    }
  }

  // 引导逻辑
  private playGuide(
    stepId: number,
    nodeIconIn: Node,
    worldPos: Vec3,
    name: string,
    delayTime: number = 0
  ): Promise<boolean> {
    return new Promise((resolve) => {
      if (delayTime > 0) {
        tween(this.node)
          .delay(delayTime)
          .call(() => {
            TipsMgr.topRouteCtrl.show(
              GuideRouteEnum.TopOpen,
              { nodeIconIn: nodeIconIn, worldPos: worldPos, name: name },
              () => {
                GuideMgr.startGuide({ stepId });
                GuideMgr.addCallBack(() => {
                  resolve(true);
                });
              }
            );
          })
          .start();
      } else {
        TipsMgr.topRouteCtrl.show(
          GuideRouteEnum.TopOpen,
          { nodeIconIn: nodeIconIn, worldPos: worldPos, name: name },
          () => {
            GuideMgr.startGuide({ stepId });
            GuideMgr.addCallBack(() => {
              resolve(true);
            });
          }
        );
      }
    });
  }

  private playOpenAni(id: number, nodeShow: Node, list: any[]) {
    TipsMgr.topRouteCtrl.show(
      RewardRouteEnum.TopSystemOpen,
      {
        toWorldPos: nodeShow.getWorldPosition(),
        nodeIconAdd: instantiate(nodeShow),
      },
      () => {
        list = list.filter((item) => item != id);
        this.playOpenAniCheck(list);
        GameDirector.instance.saveSystemOpenWaitAniList(list);
      }
    );
  }

  private playOpenAniCheck(list) {
    // 待开启动画列表
    if (list.includes(25)) {
      // 五族荣耀
      let uiMain = NodeTool.findByName(director.getScene(), "UIMain");
      if (!isValid(uiMain)) {
        this.isPlaySystemOpen = false;
        return;
      }
      let nodeShow = NodeTool.findByName(uiMain, "btn_wu_zu_rong_yao", false);
      nodeShow.active = true;

      this.playOpenAni(25, nodeShow, list);
    } else if (list.includes(27)) {
      // 五族预览
      let uiMain = NodeTool.findByName(director.getScene(), "UIMain");
      if (!isValid(uiMain)) {
        this.isPlaySystemOpen = false;
        return;
      }
      let nodeShow = NodeTool.findByName(uiMain, "btn_wu_zu_yu_lan", false);
      nodeShow.active = true;

      this.playOpenAni(27, nodeShow, list);
    } else {
      this.isPlaySystemOpen = false;
    }
  }

  // 新功能解锁触发判断
  private async onSystemOpenCheck(name) {
    if (name != CityRouteName.UIGameMap) {
      return;
    }

    if (this.isPlaySystemOpen) {
      log.log("正在播放新功能动画");
      return;
    }
    this.isPlaySystemOpen = true;

    let list = GameDirector.instance.getSystemOpenWaitAniList();
    this.playOpenAniCheck(list);
  }

  private eventCheck(name) {
    if (name != CityRouteName.UIGameMap) {
      return;
    }
    if (this._isEventCheckBool == false) {
      return;
    }

    EventCtrlManager.instance.checkAndCreatePrefabs();
  }

  private async cloudCheck(name) {
    if (name != CityRouteName.UIGameMap) {
      return;
    }
    let db = JsonMgr.instance.jsonList.c_build;
    const cfg = JsonMgr.instance.jsonList.c_copyMain[FightModule.data.chapterId];
    let obj = { cityId: null };
    let list = [];
    for (let i in db) {
      if (cfg && db[i].id <= cfg.buildId) {
        list.push(db[i].id);
        obj.cityId = db[i].id;
      }
    }

    while (this.isPlaySystemOpen) {
      await Sleep(0.5);
    }
    MsgMgr.emit(CityEvent.ON_CITY_BUILD_ANI, list);

    if (FightModule.data.stepPass1030002 == SpGuide103002.SECOND_FIGHT_FINISH) {
      TipsMgr.setEnableTouch(false, 1);
      FightModule.data.stepPass1030002 = SpGuide103002.END;
      UIMgr.instance.showDialog(HdShouChongRouteItem.UIShouChong, {}, () => {
        TipsMgr.setEnableTouch(true);
      });
    }
  }

  private async checkGuide(name) {
    if (name != CityRouteName.UIGameMap) {
      return;
    }

    if (TopEventRoute.instance) {
      return;
    }

    let guideIdNow = PlayerModule.data.getPlayerInfo().guideId;

    if (guideIdNow >= 401) {
      MsgMgr.off(MsgEnum.UIReady, this.checkGuide, this);
      return;
    }

    if (guideIdNow < 202) {
      const cfgTaskMain = JsonMgr.instance.jsonList.c_taskMain[MainTaskModule.data.mainTaskMsg.taskTimelineId];
      if (cfgTaskMain.guide == 1) {
        log.info("引导201-202");
        PlayerModule.data.getPlayerInfo().guideId = 202;
        TipsMgr.topRouteCtrl.show(GuideRouteEnum.TopEventRoute, { from: 201, to: 202 }, () => {
          let needCount = MainTaskModule.service.getNeedCount();
          let complateNum = MainTaskModule.service.getCompleteNum();
          if (needCount <= complateNum) {
            GuideMgr.startGuide({ stepId: 50, isForce: true });
          } else {
            GuideMgr.startGuide({ stepId: 49, isForce: true });
          }
        });
      } else {
        PlayerModule.data.getPlayerInfo().guideId = 202;
        PlayerModule.api.updateGuideId(202);
      }
    } else if (guideIdNow == 202) {
      if (PlayerModule.data.getPlayerInfo().level == 2) {
        PlayerModule.data.getPlayerInfo().guideId = 301;
        log.info("引导301");
        TipsMgr.topRouteCtrl.show(GuideRouteEnum.TopEventRoute, { from: 301, to: 301 }, () => {
          TipsMgr.topRouteCtrl.show(GuideRouteEnum.TopEventHeroCome);
        });
      }
    } else if (guideIdNow == 304) {
      const cfgEvent2 = JsonMgr.instance.jsonList.c_event2[101];
      if (cfgEvent2.unlock[0] == 2) {
        if (MainTaskModule.data.mainTaskMsg.totalSuccessCount >= cfgEvent2.unlock[1]) {
          PlayerModule.api.updateGuideId(401);
          TipsMgr.topRouteCtrl.show(GuideRouteEnum.TopEventRoute, { from: 401, to: 401 });
        }
      }
    }
  }
}
