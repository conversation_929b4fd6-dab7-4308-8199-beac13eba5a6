import { _decorator, Label, Node, RichText } from "cc";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import ToolExt from "../../common/ToolExt";
import FmUtils from "../../../lib/utils/FmUtils";
import { AudioMgr, AudioName } from "../../../../platform/src/AudioHelper";
import { AzstAudioName } from "../../../module/azst/AzstConfig";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";

const { ccclass, property } = _decorator;

export class AzstSettleArgs {
  heName: string;
  hePoint: number;
  hePointCh: number;
  myPoint: number;
  myPointCh: number;
  win: boolean;
  resAddList: number[];
}

@ccclass("UIAzstLose")
export class UIAzstLose extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_AZST}?prefab/ui/UIAzstLose`;
  }

  private _heInfo: { name: string; point: number; pointCh: number; avatarList: number[] } = null;
  private _myInfo: { name: string; point: number; pointCh: number; avatarList: number[] } = null;

  public init(args: any): void {
    super.init(args);
    this._myInfo = args.myInfo;
    this._heInfo = args.heInfo;
  }

  protected onEvtShow(): void {
    TipsMgr.setEnableTouch(true);
    this.setInfo(this.getNode("spr_right_info"), this._heInfo, true);
    this.setInfo(this.getNode("spr_left_info"), this._myInfo, false);
    AudioMgr.instance.playEffect(AzstAudioName.Effect.战斗失败);
  }

  private setInfo(
    node: Node,
    info: { name: string; point: number; pointCh: number; avatarList: number[] },
    isWin: boolean
  ) {
    node.getChildByName("name").getComponent(Label).string = info.name;

    let str = `<color=#fff9cf>积分：${info.point}</color><color=#74ff77>（+${info.pointCh}）</color>`;
    if (isWin == false) str = `<color=#fff9cf>积分：${info.point}</color><color=#74ff77>（+${info.pointCh}）</color>`;
    node.getChildByName("point").getComponent(RichText).string = str;

    let param = ToolExt.newPlayerBaseMessage();
    param.avatarList = info.avatarList;
    param.nickname = info.name;
    param.userId = 0;
    FmUtils.setHeaderNode(node.getChildByName("head"), param);
  }

  // private on_click_btn_close() {
  //   AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
  //   UIMgr.instance.back();
  // }
}
