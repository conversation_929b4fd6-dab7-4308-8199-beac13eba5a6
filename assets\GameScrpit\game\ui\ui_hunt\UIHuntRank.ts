import { _decorator, Label } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { HuntModule } from "../../../module/hunt/HuntModule";
import { HuntRankMessage, HuntPlayerMessage } from "../../net/protocol/Hunt";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import { ListView } from "../../common/ListView";
import { HuntRankAdapter } from "./adapter_hunt_rank/HuntRankAdapter";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
const { ccclass, property } = _decorator;

@ccclass("UIHuntRank")
export class UIHuntRank extends UINode {
  protected _openAct: boolean = true; //打开动作
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_HUNT}?prefab/ui/UIHuntRank`;
  }

  private _data: HuntRankMessage = null;

  protected onEvtShow(): void {
    HuntModule.api.petRank((res: HuntRankMessage) => {
      this._data = res;

      this.loadRankMessage();
      this.setMyInfo();
    });
  }

  private loadRankMessage() {
    let adapter = new HuntRankAdapter(this.getNode("rankMessage"));

    this.getNode("list_view_level").getComponent(ListView).setAdapter(adapter);
    let rankList: HuntPlayerMessage[] = this._data.rankList;
    adapter.setData(rankList);
  }

  private setMyInfo() {
    if (this._data.rank == -1) {
      this.getNode("lab_myRank").getComponent(Label).string = "我的排名：未上榜";
    } else {
      this.getNode("lab_myRank").getComponent(Label).string = "我的排名：" + this._data.rank;
    }

    this.getNode("lab_myScore2").getComponent(Label).string = String(this._data.point);
  }

  private on_click_btn_header(event) {
    AudioMgr.instance.playEffect(518);
    let userId = event.node.parent.userInfo.userId;
    UIMgr.instance.showDialog(PlayerRouteName.UIPlayerOtherMsg, { userId: userId });
  }

  private on_click_btn_close() {
    // 关闭窗口 effect_guanbiyinxiao
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);

    UIMgr.instance.back();
  }
}
