{"skeleton": {"hash": "XU8e+43htsdur7GqWSxvxZDJyt0=", "spine": "3.8.75", "x": -279.55, "y": -127.88, "width": 586.63, "height": 532.42, "images": "../山膏/images/", "audio": "D:/spine导出/灵兽动画/山膏"}, "bones": [{"name": "root"}, {"name": "bone15", "parent": "root", "x": 2.39, "y": -141.09}, {"name": "bone28", "parent": "bone15", "length": 165.44, "rotation": 1.19, "x": 23.45, "y": 4.29}, {"name": "bone29", "parent": "bone15", "length": 149.98, "rotation": 179.67, "x": -18, "y": 1.71}, {"name": "bone30", "parent": "bone28", "length": 90.02, "rotation": 90.45, "x": 172.81, "y": 24.71}, {"name": "bone31", "parent": "bone29", "length": 90.02, "rotation": -88.04, "x": 146.3, "y": -12.02}, {"name": "bone32", "parent": "root", "x": 117.02, "y": 456.52}, {"name": "bone33", "parent": "bone32", "length": 157.14, "rotation": -49.24, "x": -367.43, "y": -55.79}, {"name": "bone34", "parent": "bone32", "length": 157.14, "rotation": 42.42, "x": 167.11, "y": -124.53, "scaleX": -1}], "slots": [{"name": "qt1", "bone": "bone30", "color": "ffffff00", "dark": "000000", "attachment": "qt1"}, {"name": "qt2", "bone": "bone31", "color": "ffffff00", "dark": "000000", "attachment": "qt1"}, {"name": "cmcm", "bone": "bone33", "color": "ffffff00", "attachment": "cmcm"}, {"name": "cmcm2", "bone": "bone34", "color": "ffffff00", "attachment": "cmcm"}], "skins": [{"name": "default", "attachments": {"cmcm": {"cmcm": {"x": 82.66, "y": -0.92, "scaleX": 0.7044, "scaleY": 0.7044, "rotation": 18.1, "width": 219, "height": 90}}, "cmcm2": {"cmcm": {"x": 82.66, "y": -0.92, "scaleX": 0.7044, "scaleY": 0.7044, "rotation": 18.1, "width": 219, "height": 90}}, "qt1": {"qt1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [9.04, 47.58, -15.56, -18.83, 62.97, -47.92, 87.57, 18.49], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 115, "height": 136}}, "qt2": {"qt1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.48, -45.51, -0.71, 22.93, 80.23, 44.44, 98.41, -24.01], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 115, "height": 136}}}}], "events": {"atk": {}}, "animations": {"wuqi1": {"slots": {"qt1": {"twoColor": [{"light": "ffffff00", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 0.0333, "light": "ffffffff", "dark": "000000", "curve": "stepped"}, {"time": 0.1667, "light": "ffffffff", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 0.2, "light": "ffffff00", "dark": "000000"}]}, "qt2": {"twoColor": [{"time": 0.0667, "light": "ffffff00", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 0.1, "light": "ffffffff", "dark": "000000", "curve": "stepped"}, {"time": 0.2333, "light": "ffffffff", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "light": "ffffff00", "dark": "000000"}]}}, "bones": {"bone28": {"rotate": [{"curve": 0.804, "c2": 0.03, "c3": 0.272}, {"time": 0.2, "angle": 70.98}]}, "bone29": {"rotate": [{"time": 0.0667, "curve": 0.804, "c2": 0.03, "c3": 0.272}, {"time": 0.2667, "angle": -63.38}]}, "bone31": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 42.9}]}, "bone30": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -39.94}]}, "bone15": {"translate": [{"y": -80.65}]}}}, "wuqi2": {"slots": {"cmcm2": {"color": [{"time": 0.0667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "color": "ffffff00"}]}, "cmcm": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.1333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffff00"}]}}, "bones": {"bone34": {"translate": [{"time": 0.0667, "curve": 0.603, "c2": 0.01, "c3": 0.366, "c4": 0.97}, {"time": 0.2333, "x": -202.12, "y": -186.73}]}, "bone33": {"translate": [{"curve": 0.603, "c2": 0.01, "c3": 0.366, "c4": 0.97}, {"time": 0.1667, "x": 154.93, "y": -242.14}]}, "bone15": {"translate": [{"x": 1.34}]}, "bone32": {"translate": [{"y": -60.16}]}}}}}