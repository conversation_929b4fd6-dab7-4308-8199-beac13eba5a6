import { ApiHandler, ApiHandlerFail, ApiHandlerSuccess } from "../../game/mgr/ApiHandler";
import { ActivityCmd } from "../../game/net/cmd/CmdData";
import {
  RedeemMessage,
  RedeemRequest,
  RedeemResponse,
  TopUpMessage,
  TopUpRewardRequest,
  TopUpRewardResponse,
  TopUpSignRequest,
  TopUpSignResponse,
} from "../../game/net/protocol/Activity";
import { LongValue } from "../../game/net/protocol/ExternalMessage";
import { AfterModule } from "./AfterModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class AfterApi {
  /**消耗道具兑换或免费领取固定礼包道具 -----  每日礼包，累计回馈都有使用 */
  public buyFixedPack(param: RedeemRequest, success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(
      RedeemResponse,
      ActivityCmd.buyFixedPack,
      RedeemRequest.encode(param),
      (res: RedeemResponse) => {
        AfterModule.data.redeemMap = res.redeemMap;
        AfterModule.data.adMap = res.adMap;
        log.log("消耗道具兑换或免费领取固定礼包道具=======", res);
        success && success(res);
      }
    );
  }

  /**获取累充回馈信息 */
  public topUpInfo(id: number, success?: ApiHandlerSuccess) {
    let data: LongValue = {
      value: id,
    };

    ApiHandler.instance.request(TopUpMessage, ActivityCmd.topUpInfo, LongValue.encode(data), (res: TopUpMessage) => {
      log.log("获取累充回馈信息=====", res);
      AfterModule.data.topUpMessage = res;
      success && success(res);
    });
  }

  /**1.领取累充奖励 --- 这个是日*/
  public topUpSign(param: TopUpSignRequest, success?) {
    ApiHandler.instance.request(
      TopUpSignResponse,
      ActivityCmd.topUpSign,
      TopUpSignRequest.encode(param),
      (res: TopUpSignResponse) => {
        AfterModule.data.deadline = res.deadline;
        AfterModule.data.signMap = res.signMap;
        log.log("领取累天奖励=====", res);
        success && success(res);
      }
    );
  }

  /**3.领取达到累充金额获得的奖励 */
  public takeTopUpCostReward(param: TopUpRewardRequest, success?) {
    ApiHandler.instance.request(
      TopUpRewardResponse,
      ActivityCmd.takeTopUpCostReward,
      TopUpRewardRequest.encode(param),
      (res: TopUpRewardResponse) => {
        AfterModule.data.takeList = res.takeList;
        log.log("领取达到累充金额获得的奖励=====", res);
        success && success(res);
      }
    );
  }
}
