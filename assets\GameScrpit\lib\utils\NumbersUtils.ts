import BigNumber from "bignumber.js";

export function times(n1: number, n2: number): number {
  const bigNumber1 = new BigNumber(n1);
  const result = bigNumber1.times(n2).toNumber();
  return result;
}
/**
 *
 * @param dividend 被除数
 * @param divisor 除数
 * @returns
 */
export function divide(dividend: number, divisor: number) {
  if (!dividend || !divisor) {
    return 0;
  }
  const bigNumber1 = new BigNumber(dividend);
  const result = bigNumber1.div(divisor).toNumber();
  return result;
}

export function formatNumber(num: number, precision: number = 2) {
  let n = num * 10 ** precision;
  n = Math.floor(n);
  return n / 10 ** precision;
}

// 生成随机整数
export function getRandomInt(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}
