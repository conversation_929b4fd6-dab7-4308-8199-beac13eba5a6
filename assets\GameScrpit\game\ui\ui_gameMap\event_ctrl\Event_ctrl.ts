import { _decorator, Component, Node } from "cc";
import { JsonMgr } from "../../../mgr/JsonMgr";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("Event_ctrl")
export class Event_ctrl extends BaseCtrl {
  protected autoSetLayer = false;

  protected _params: { eventId: number };

  /**
   * 初始化事件控制器
   * @param params - 初始化所需的参数，可以根据实际需求定义类型
   */
  public initialize(params: any) {
    this._params = params;
    // 示例：如果有需要，可以根据 params 做一些初始化操作 // 这里可以放置原本 start 方法里的公共逻辑，并且使用传入的参数
    log.log("Event_ctrl 初始化，传入参数:", params);
  }

  /**
   * 将一个数字平均分成三份，并将结果累加返回
   * @param num 要进行分配的数字
   * @returns 包含三个元素的数组，表示累加后的结果
   */
  public divideNumberIntoThreePartsWithAccumulation(num: number): number[] {
    const quotient = Math.floor(num / 3);
    const remainder = num % 3;
    let parts: number[];

    if (remainder === 0) {
      parts = [quotient, quotient, quotient];
    } else if (remainder === 1) {
      parts = [quotient, quotient, quotient + 1];
    } else {
      parts = [quotient, quotient + 1, quotient + 1];
    }

    // 进行累加处理
    for (let i = 1; i < parts.length; i++) {
      parts[i] += parts[i - 1];
    }

    return parts;
  }

  /**
   * 判断给定数字大于等于数组中最大的符合条件元素，并返回对应的索引（从 1 开始计数，不满足返回 0）
   * @param num 要进行判断的数字
   * @param arr 用于比较的数组，数组元素需按升序排列
   * @returns 满足条件的最大索引（从 1 开始），若没有则返回 0
   */
  public findIndexGreaterOrEqual(num: number, arr: number[]): number {
    let resultIndex = 0;
    for (let i = 0; i < arr.length; i++) {
      if (num >= arr[i]) {
        resultIndex = i + 1;
      } else {
        // 由于数组是升序排列，一旦不满足条件，后续元素肯定也不满足，可提前退出循环
        break;
      }
    }
    return resultIndex;
  }

  /**
   * 获取事件配置
   * @param mapBaseEvent 包含事件 ID 和事件实例的结构，用于从配置表获取信息
   */
  protected getEventConfig(id: number) {
    const db = JsonMgr.instance.jsonList.c_event2;
    const data = db[id];
    if (!data) {
      log.error(`未找到事件 ID ${id} 对应的事件配置`);
    }
    return data;
  }
}
