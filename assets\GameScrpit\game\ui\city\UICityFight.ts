import { _decorator, instantiate, isValid, Label, Node, Prefab, ProgressBar, ScrollView, Size, UITransform } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { JsonMgr } from "../../mgr/JsonMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
import TipMgr from "../../../lib/tips/TipMgr";
import { CityCtrl } from "../ui_gameMap/CityCtrl";
import { CityModule } from "../../../module/city/CityModule";
import { FightModule } from "../../../module/fight/src/FightModule";
import ToolExt from "../../common/ToolExt";
import ResMgr from "../../../lib/common/ResMgr";
import { NodeTool } from "../../../lib/utils/NodeTool";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
import { CityAudioName } from "../../../module/city/CityConstant";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "../../../lib/utils/FmUtils";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UICityFight")
export class UICityFight extends UINode {
  protected _openAct: boolean = true;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_MAJORCITY}?prefab/ui/UICityFight`;
  }

  private _city_Id: number;

  private _setOnlyShow: Node;

  public init(args: any): void {
    super.init(args);
    this._city_Id = args.cityId;
  }

  protected onEvtShow(): void {
    let c_build = JsonMgr.instance.jsonList.c_build;
    let db = c_build[this._city_Id];
    if (!db) {
      TipMgr.showTip("建筑信息错误");
      UIMgr.instance.back();
      return;
    }
    this.setCityDes();
    this.setProgress();
    this.addCityPrefab();
    this.setDialogTitle(db);
    this.loadRewardList();
  }

  private setDialogTitle(db: any) {
    FmUtils.setDialogTitle(this.getNode("DialogSub"), db.name);
  }

  private addCityPrefab() {
    ResMgr.loadPrefab(`${BundleEnum.BUNDLE_G_GAME_MAP}?prefab/building/city_${this._city_Id}`, (pb: Prefab) => {
      if (isValid(this.node) == false) {
        return;
      }

      this._setOnlyShow = instantiate(pb);
      //log.log("展示建筑的uuid===", this._setOnlyShow.uuid);

      this._setOnlyShow.getComponent(CityCtrl).setOnlyShow({
        hideUI: true,
        cityLevel: 1,
      });
      this.getNode("node_dity").addChild(this._setOnlyShow);
      this._setOnlyShow.setPosition(0, 0);
      this._setOnlyShow.walk((val) => {
        val.layer = this.node.layer;
      });
      let aniNode = NodeTool.findByName(this._setOnlyShow, "ani");
      CityModule.service.setBuildSize(aniNode, this._city_Id, 1);
    });
  }

  private setProgress() {
    let copyDb = JsonMgr.instance.jsonList.c_copyMain;
    let copyList = Object.keys(copyDb);
    let chapterId = FightModule.data.chapterId;
    let index1: number = copyList.indexOf(chapterId.toString());

    let cityDb = JsonMgr.instance.jsonList.c_build[this._city_Id];
    let needLevel = cityDb.unlockLv;
    let index2: number = copyList.indexOf(needLevel.toString()) + 1;

    log.log("index1===", index1, "index2===", index2);

    this.getNode("lbl_progress").getComponent(Label).string = "通关关卡数：" + index1 + "/" + index2;
    this.getNode("progress_level").getComponent(ProgressBar).progress = index1 / index2;

    // let arr = CityModule.service.getCityLevelList(this._city_Id);
    // let chapterId = FightModule.data.chapterId;
    // let index = 0;
    // for (let i = 0; i < arr.length; i++) {
    //   if (chapterId == arr[i].id) {
    //     index = i;
    //     break;
    //   }
    // }
    // this.getNode("lbl_progress").getComponent(Label).string = "通关关卡数：" + index + "/" + arr.length;
    // this.getNode("progress_level").getComponent(ProgressBar).progress = index / arr.length;
  }

  private loadRewardList() {
    let chapterId = FightModule.data.chapterId;
    let db = JsonMgr.instance.jsonList.c_copyMain;
    let data = db[chapterId] || db[1030001];

    if (data.reward1List.length >= 5) {
      this.getNode("layout_reward_ScrollView").getComponent(ScrollView).enabled = true;
    }

    for (let i = 0; i < data.reward1List.length; i++) {
      let node = ToolExt.clone(this.getNode("Item"));
      this.getNode("layout_reward_content").addChild(node);
      node.active = true;

      FmUtils.setItemNode(node, data.reward1List[i][0], data.reward1List[i][1]);
    }
  }

  private setCityDes() {
    let c_build = JsonMgr.instance.jsonList.c_build;
    let db = c_build[this._city_Id];
    let str = db.des;

    this.getNode("lbl_des").getComponent(Label).string = str;
    this.getNode("lbl_des_2").getComponent(Label).string = `该建筑每号召1人口，繁荣度：+ ${db.goldProduce}/秒`;
    // let des_lab = this.getNode("des_lab");
    // let max = Math.ceil(str.length / 20);
    // max = max > 3 ? 3 : max;
    // for (let i = 0; i < max; i++) {
    //   let node = instantiate(des_lab);
    //   let str1 = str.substring(i * 20, (i + 1) * 20);
    //   node.getComponent(Label).string = `${str1}`;
    //   this.getNode("des_lay").addChild(node);
    //   node.setSiblingIndex(i + 1);
    //   node.active = true;
    //   if (i == max - 1) {
    //     let mask = this.getNode("des_lay").getChildByName("Mask");
    //     let contentSize = mask.getComponent(UITransform).contentSize;
    //     // 动态更新label渲染，获取当前帧的高度或宽度
    //     node.getComponent(Label).updateRenderData(true);
    //     let contentSize1 = node.getComponent(UITransform).contentSize;
    //     mask.getComponent(UITransform).setContentSize(new Size(contentSize.width, contentSize1.y + 10));
    //   }
    // }
  }

  private on_click_btn_fight() {
    AudioMgr.instance.playEffect(CityAudioName.Effect.未解锁点击前往挑战);
    UIMgr.instance.back();
    ToolExt.showLevelMain();
  }
}
