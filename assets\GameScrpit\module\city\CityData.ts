import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  BloomBase<PERSON>,
  Bloom<PERSON>dd,
  HeroAdd<PERSON>ttr,
} from "../../game/GameDefine";

import MsgEnum from "../../game/event/MsgEnum";
import { JsonMgr } from "../../game/mgr/JsonMgr";
import { CityAggregateMessage, CityMessage, EnergyFactoryMessage } from "../../game/net/protocol/City";
import MsgMgr from "../../lib/event/MsgMgr";
import {
  ConfigBuildCrystalRecord,
  ConfigBuildRecord,
  ConfigBuildWorkerReward,
  typeIconMap,
  typeMap,
} from "./CityConstant";
import { HeroModule } from "../hero/HeroModule";
import { FriendModule } from "../friend/FriendModule";
import { addAttrMap } from "../../lib/utils/AttrTool";
import { IConfigBuildLv, IConfigBuildLvR<PERSON>ard, IConfigBuildTrimR<PERSON>ard } from "../../game/JsonDefine";
import { CityEvent } from "./CityEvent";
import { BadgeMgr, BadgeType } from "../../game/mgr/BadgeMgr";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class CityData {
  /** =================================== 本地数据 ============================================= */
  /**
   * 获取据点种族图标
   * @param type 种族
   * @returns
   */
  public getTypeIcon(type: number) {
    return typeIconMap[`color_${type > 5 ? 1 : type}`];
  }

  /**
   * 获取据点种族名称
   * @param type 种族
   * @returns
   */
  public getTypeName(type: number) {
    return typeMap[`color_${type > 5 ? 1 : type}`];
  }

  /**
   * 设置据点信息
   * @param data  据点信息
   */
  public setCityData(data: CityMessage) {
    this.cityMessageMap.set(data.cityId, data);
    MsgMgr.emit(MsgEnum.ON_CITY_UPDATE, data.cityId);
  }

  /**
   *
   * @param id 据点id
   * @param energyHire 招募人数
   */
  public updateEnergyHire(id: number, energyHire: number) {
    let cityMsg = this.cityMessageMap.get(id);
    if (cityMsg.energyHire !== energyHire) {
      cityMsg.energyHire = energyHire;
      MsgMgr.emit(MsgEnum.ON_CITY_UPDATE, id);
    }
  }

  /**
   * 更新宝箱数量
   * @param boxCount 宝箱数量
   */
  public updateBoxCount(id: number, boxCount: number) {
    if (!boxCount) {
      boxCount = 0;
    }

    if (boxCount < 0) {
      log.error("宝箱数量不能为负数 ", boxCount);
      boxCount = 0;
    }

    if (this.cityAggregateMessage.boxTotalCount !== boxCount) {
      if (boxCount > this.cityAggregateMessage.boxTotalCount) {
        // 动画消息
        MsgMgr.emit(MsgEnum.ON_CITY_HAO_ZHAO_BOX_ADD, id);
      }

      this.cityAggregateMessage.boxTotalCount = boxCount || 0;
      MsgMgr.emit(MsgEnum.ON_CITY_HAO_ZHAO_UPDATE);
    }
  }

  /**获取据点总等级 */
  public get cityTotalLevel() {
    let totalLevel = 0;
    this.cityMessageMap.forEach((value, key) => {
      totalLevel += value.level;
    });
    return totalLevel;
  }

  /**获取据点总人数 */
  public get cityTotalNum() {
    let totalNum = 0;
    this.cityMessageMap.forEach((value, key) => {
      totalNum += value.energyHire + value.itemHire;
    });
    return totalNum;
  }

  /**
   * 建筑伙计成就 对 武将的加成
   * @returns
   */
  public getCityBuildWorkerRewadToHero() {
    let attr = new HeroAddAttr();
    let attrList = Object.keys(attr).map(Number);
    let list = Object.keys(JsonMgr.instance.jsonList.c_buildWorkerReward)
      .map(Number)
      .sort((a, b) => a - b);
    for (let i = 0; i < list.length; i++) {
      let config = JsonMgr.instance.jsonList.c_buildWorkerReward[list[i]];
      if (config["workerNum"] <= this.cityTotalNum) {
        let attrAdd: number[][] = config["attrAdd"];
        attrAdd.forEach((attrItem) => {
          if (attrList.indexOf(attrItem[0]) != -1) {
            attr[attrItem[0]] += attrItem[1];
          }
        });
      }
    }
    return attr;
  }

  /** =================================== 繁荣度 ============================================= */

  // /**获取某一据点的繁荣度 */
  public getCityBloom(cityId: number) {
    if (!this.cityMessageMap.get(cityId)) {
      return 0;
    }

    let cityMsg = this.cityMessageMap.get(cityId);
    let configBuild: ConfigBuildRecord = this.getConfigBuild(cityId);
    let configBuildLv: IConfigBuildLv = this.getConfigBuildLv(cityId, cityMsg.level);
    let configBuildCrystal: ConfigBuildCrystalRecord = this.getConfigBuildCrystal(this.energyFactoryMsg.level);
    if (!configBuildCrystal) {
      log.warn("数据未准备好");
      return;
    }

    // 基础赚速
    let baseBloom = 0;
    /**伙计 */
    baseBloom += (cityMsg.energyHire + cityMsg.itemHire) * configBuild.goldProduce;

    /** 战将 （生命+攻击+防御+敏捷）/1000 */
    let playerAttr = HeroModule.service.getAllHeroAttrMap();
    let heroBaseAttrAll =
      (playerAttr[AttrEnum.生命_1] || 0) +
      (playerAttr[AttrEnum.攻击_2] || 0) +
      (playerAttr[AttrEnum.防御_3] || 0) +
      (playerAttr[AttrEnum.敏捷_4] || 0);
    baseBloom += heroBaseAttrAll / 1000;

    // 英雄基础加成
    let rateHero = 0;
    const heroIdList = configBuild.heroIdList;
    for (let i = 0; i < heroIdList.length; i++) {
      let heroMessage = HeroModule.data.getHeroMessage(heroIdList[i]);
      if (heroMessage) {
        const configHero = HeroModule.config.getHeroInfo(heroIdList[i]);
        rateHero += (configBuildLv.heroRate * configBuildLv.heroRateList[configHero.color - 1]) / 10000;
      }
    }
    baseBloom *= 1 + rateHero;

    // 加成
    let rate = 1;

    // 升级加成
    rate += configBuildLv.rateAdd / 10000;

    // 水晶加成
    rate += configBuildCrystal.rateAdd / 10000;

    // 挚友美名加成
    const friendRateList = FriendModule.data.getAllFriendCityAttrAdds();
    rate += friendRateList[configBuild.type] / 100;

    return baseBloom * rate;
  }

  /**获取总的繁荣度 */
  public getTotalBloom() {
    let baseBloom = 0;
    this.cityMessageMap.forEach((value, key) => {
      let bloom = this.getCityBloom(value.cityId);
      baseBloom += bloom;
    });
    return baseBloom;
  }

  /** 获取某一据点武将的繁荣度加成 */
  public getCityHeroAddBloom(cityId: number) {
    let heroAddBloom = 0;
    let cityMap = this.cityMessageMap.get(cityId);
    let configBuild: ConfigBuildRecord = this.getConfigBuild(cityId);
    let configBuildLv: IConfigBuildLv = this.getConfigBuildLv(cityId, cityMap.level);
    /**该据点已拥有的武将列表 */
    let heroHasNum = 0;
    configBuild.heroIdList.forEach((heroId) => {
      if (HeroModule.data.getHeroMessage(heroId)) {
        heroHasNum += 1;
      }
    });
    heroAddBloom = (configBuildLv.heroRate / 10000) * heroHasNum;
    return heroAddBloom;
  }

  /**获取某一据点的繁荣度组成 */
  public getBloomItem(cityId: number): BloomBaseAndOther {
    let cityMap = this.cityMessageMap.get(cityId);
    let configBuild: ConfigBuildRecord = JsonMgr.instance.jsonList.c_build[cityId];
    let configBuildLv: IConfigBuildLv = this.getConfigBuildLv(cityId, cityMap.level);
    let configBuildCrystal: ConfigBuildCrystalRecord =
      JsonMgr.instance.jsonList.c_buildCrystal[this.energyFactoryMsg.level + 1000];

    let baseAdd = new BloomBaseAdd();
    let otherAdd = new BloomOtherAdd();
    baseAdd.basis = configBuild.goldProduce;

    otherAdd.upgrade = 1 + configBuildLv.rateAdd / 10000;
    otherAdd.bank = configBuildCrystal.rateAdd / 10000;

    let bloomBaseAndOther: BloomBaseAndOther = new BloomBaseAndOther();
    bloomBaseAndOther.baseAdd = baseAdd;
    bloomBaseAndOther.otherAdd = otherAdd;

    return bloomBaseAndOther;
  }

  /** ======================================= 属性 ======================================== */
  /**获取某一据点的属性 */
  public getCityAttrMap(cityId: number) {
    let attr = new Map();
    let cityMap = this.cityMessageMap.get(cityId);
    if (!cityMap) {
      return attr;
    }
    let configBuildLv = this.getConfigBuildLv(cityMap.cityId, cityMap.level);

    for (let i = 0; i < configBuildLv.attrFit.length; i++) {
      let attrFit = configBuildLv.attrFit[i];
      let key = attrFit[0];
      let value = BaseAttrList.indexOf(key) !== -1 ? attrFit[1] : attrFit[1] / 10000;
      attr[key] = value;
    }
    return attr;
  }

  /**获取所有据点的属性 */
  public getCityTotalAttrMap() {
    let attrMap = new Map();
    this.cityMessageMap.forEach((value, key) => {
      addAttrMap(attrMap, this.getCityAttrMap(value.cityId));
    });
    return attrMap;
  }

  /** ======================================= 重写 20240920 只保存数据 ======================================== */

  /** ============服务端数据 开始================== */
  /** 据点信息 */
  public cityAggregateMessage: CityAggregateMessage;

  public addCityTrimAward(id: number) {
    this.cityAggregateMessage.raceRewardIdList.push(id);
    MsgMgr.emit(CityEvent.ON_CITY_TRIM_AWARD);
  }

  public addDecorationIdList(id: number) {
    this.cityAggregateMessage.decorationIdList.push(id);
  }

  // 水晶状态
  public energyFactoryMsg: EnergyFactoryMessage = {
    level: 0,
    look: "",
    autoTake: false,
    autoStateCount: 0,
  };

  // 服务端城市信息map
  public cityMessageMap: Map<number, CityMessage> = new Map();

  public set cityMessageList(value: CityMessage[]) {
    this.cityMessageMap = new Map();
    for (let idx in value) {
      let item = value[idx];
      this.cityMessageMap.set(item.cityId, item);
    }
  }

  public get cityMessageList(): CityMessage[] {
    return Array.from(this.cityMessageMap.values());
  }

  /** ============服务端数据 结束 ================== */

  /** ============ 策划配置 开始 ================== */
  /**
   * 获取水晶等级对应的配置
   * @param level
   * @returns
   */
  public getConfigBuildCrystal(level: number): ConfigBuildCrystalRecord {
    let maxLv = JsonMgr.instance.jsonList.c_buildCrystal[1001].lvMax;
    if (level >= maxLv) {
      level = maxLv;
    }
    return JsonMgr.instance.jsonList.c_buildCrystal[1000 + level];
  }

  /**
   * 获取c_build配置
   * @param cityId
   * @returns
   */
  public getConfigBuild(cityId: number): ConfigBuildRecord {
    return JsonMgr.instance.jsonList.c_build[cityId];
  }

  /**
   * 获取c_buildLv数据
   * @param cityId
   * @param level
   * @returns
   */
  public getConfigBuildLv(cityId: number, level: number): IConfigBuildLv {
    let lvMax = JsonMgr.instance.jsonList.c_buildLv[10101].lvMax;
    if (level > lvMax) {
      log.warn("获取的建筑等级 超过最大等级", cityId, level, lvMax);
    }

    level = Math.min(level, lvMax);
    return JsonMgr.instance.jsonList.c_buildLv[cityId * 100 + level];
  }

  // 获取c_buildLvReward数据
  public getConfigBuildLvReward(cityId: number): IConfigBuildLvReward {
    let config = JsonMgr.instance.jsonList.c_buildLvReward[cityId];
    return config;
  }

  // c_buildWorkerReward(建筑伙计成就)
  public getConfigBuildWorkerRewardData(id: number): ConfigBuildWorkerReward {
    return JsonMgr.instance.jsonList.c_buildWorkerReward[id];
  }

  public getConfigBuildTrimList(): IConfigBuildTrimReward[] {
    let trimList = Object.values(JsonMgr.instance.jsonList.c_buildTrimReward);
    return trimList.sort((a, b) => {
      return a.buildLv - b.buildLv;
    });
  }

  /** ============ 策划配置 结束  ================== */

  /** ============ 临时数据 开始 ================== */
  // 已点击次数，每次提交后应该要清0，每3秒提交一次
  public clickHomeCount: number = 0;
}
