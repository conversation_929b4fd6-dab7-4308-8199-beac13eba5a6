import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>lerSuccess } from "../../game/mgr/ApiHandler";
import { LongValue } from "../../game/net/protocol/ExternalMessage";
import { PetMessage, PetSkillMessage, PetSkillRequest, PetSkinRequest } from "../../game/net/protocol/Pet";
import { PetSubCmd } from "./PetConstant";
import { PetModule } from "./PetModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class PetApi {
  public getAllPet(success?: ApiHandlerSuccess) {
    ApiHandler.instance.list(PetMessage, PetSubCmd.getAllPet, null, (data: PetMessage[]) => {
      PetModule.data.setPetMessageList(data);
      success && success(data);
    });
  }
  public levelUpOne(id: number, success?: ApiHandlerSuccess) {
    log.log("send levelUpOne");
    let data: LongValue = {
      value: id,
    };
    ApiHandler.instance.requestSync(PetMessage, PetSubCmd.levelUpOne, LongValue.encode(data), (data: PetMessage) => {
      PetModule.data.setPet(id, data);
      success && success(data);
    });
  }
  public levelUpTen(id: number, success?: ApiHandlerSuccess) {
    log.log("send levelUpTen");
    let data: LongValue = {
      value: id,
    };
    ApiHandler.instance.requestSync(PetMessage, PetSubCmd.levelUpTen, LongValue.encode(data), (data: PetMessage) => {
      PetModule.data.setPet(id, data);
      success && success(data);
    });
  }
  public awakeUp(id: number, success?: ApiHandlerSuccess) {
    log.log("send awakeUp");
    let data: LongValue = {
      value: id,
    };
    ApiHandler.instance.requestSync(PetMessage, PetSubCmd.awakeUp, LongValue.encode(data), (data: PetMessage) => {
      PetModule.data.setPet(id, data);
      success && success(data);
    });
  }

  public reFreshSkill(petId: number, rank: number, consumeItem: boolean, success?: ApiHandlerSuccess) {
    log.log("send reFreshSkill", petId, rank, consumeItem);
    let data: PetSkillRequest = {
      petId,
      rank,
      consumeItem,
    };
    ApiHandler.instance.requestSync(
      PetSkillMessage,
      PetSubCmd.reFreshSkill,
      PetSkillRequest.encode(data),
      (data: PetSkillMessage) => {
        log.log(data);
        let petMessage = PetModule.data.getPet(petId);
        petMessage.petSkillList[rank] = data;
        PetModule.data.setPet(petId, petMessage);
        success && success(data);
      }
    );
  }
  public getOnePet(petId: number, success?: ApiHandlerSuccess) {
    let data: LongValue = {
      value: petId,
    };
    ApiHandler.instance.request(PetMessage, PetSubCmd.getOnePet, LongValue.encode(data), (data: PetMessage) => {
      PetModule.data.setPet(petId, data);
      success && success(data);
    });
  }

  //选择皮肤
  public chooseSkin(petId: number, skinId: number, success?: ApiHandlerSuccess) {
    let data: PetSkinRequest = {
      petId: petId,
      skinId: skinId,
    };
    ApiHandler.instance.request(PetMessage, PetSubCmd.chooseSkin, PetSkinRequest.encode(data), (data: PetMessage) => {
      PetModule.data.setPet(petId, data);
      success && success(data);
    });
  }
}
