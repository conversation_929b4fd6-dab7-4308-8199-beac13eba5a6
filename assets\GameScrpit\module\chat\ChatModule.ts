import MsgEnum from "../../game/event/MsgEnum";
import { GameData } from "../../game/GameData";
import { ChatPackMessage } from "../../game/net/protocol/Chat";
import data from "../../lib/data/data";
import MsgMgr from "../../lib/event/MsgMgr";
import { ChatApi } from "./ChatApi";
import { ChatConfig } from "./ChatConfig";
import { ChatData } from "./ChatData";
import { ChatRoute } from "./ChatRoute";
import { ChatSubscriber } from "./ChatSubscriber";
import { ChatViewModel } from "./ChatViewModel";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class ChatModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): ChatModule {
    if (!GameData.instance.ChatModule) {
      GameData.instance.ChatModule = new ChatModule();
    }
    return GameData.instance.ChatModule;
  }
  private _data = new ChatData();
  private _api = new ChatApi();
  private _config = new ChatConfig();
  private _viewModel = new ChatViewModel();
  private _subscriber = new ChatSubscriber();
  private _route = new ChatRoute();

  public static get data() {
    return this.instance._data;
  }
  public static get api() {
    return this.instance._api;
  }
  public static get config() {
    return this.instance._config;
  }
  public static get viewModel() {
    return this.instance._viewModel;
  }

  public init(data?: any, completedCallback?: Function) {
    if (this._subscriber) {
      this._subscriber.unRegister();
    }

    // 初始化模块
    this._data = new ChatData();
    this._api = new ChatApi();
    this._config = new ChatConfig();
    this._viewModel = new ChatViewModel();
    this._subscriber = new ChatSubscriber();
    this._route = new ChatRoute();

    // 初始化模块数据
    this._subscriber.register();
    this._route.init();

    // 数据加载
    ChatModule.api.messageList(0, 0, 1, (data: ChatPackMessage[]) => {
      // completedCallback?.();
      log.log("战盟", data);
    });
    ChatModule.api.messageList(0, 0, 2, (data: ChatPackMessage[]) => {
      // completedCallback?.();
      log.log("世界", data);
    });
    completedCallback?.();
  }

  protected saveKey(): string {
    return this.constructor.name;
  }
}
