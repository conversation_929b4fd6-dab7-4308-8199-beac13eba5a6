// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v4.25.1
// source: Hunt.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { PlayerBaseMessage, PlayerSimpleMessage } from "./Player";

export const protobufPackage = "sim";

/**  */
export interface HuntBossLogMessage {
  userId: number;
  /** BOSS打掉的血量 */
  deductHp: number;
  /** 是否成功杀死BOSS */
  win: boolean;
  /** 打斗的时间戳 */
  timeStamp: number;
}

/**  */
export interface HuntBossRankMessage {
  point: number;
  rank: number;
  rankList: HuntPlayerMessage[];
}

/**  */
export interface HuntBossResponse {
  /** 是否胜利 */
  win: boolean;
  /** 录像回放 */
  replay: string;
  /** 造成的伤害 */
  damageHp: number;
  /** 道具 */
  resAddList: number[];
  /**  */
  huntTrainMessage: HuntTrainMessage | undefined;
}

/**  */
export interface HuntBossUpdateMessage {
  /** BOSS剩余血量 */
  bossRemainHp: number;
  /** BOSS是否死亡 */
  died: boolean;
  /** 杀死BOSS的人员信息 当died为true时有值否则为null */
  simpleMessage:
    | PlayerSimpleMessage
    | undefined;
  /** 时间戳 */
  timeStamp: number;
  /** 日志 */
  logMessage: HuntBossLogMessage | undefined;
}

/**  */
export interface HuntKillMessage {
  /** 击杀的玩家信息 */
  simpleMessage:
    | PlayerSimpleMessage
    | undefined;
  /** 击杀的时间戳 */
  timeStamp: number;
}

/**  */
export interface HuntPetResponse {
  /** 是否胜利 */
  win: boolean;
  /** 录像回放 */
  replay: string;
  /** 造成的伤害 */
  damageHp: number;
  /** 道具 */
  resAddList: number[];
  /** 进度等信息 */
  huntTrain: HuntTrainMessage | undefined;
}

/**  */
export interface HuntPlayerMessage {
  /** 用户信息 */
  playerSimpleMessage:
    | PlayerSimpleMessage
    | undefined;
  /** 分值 */
  point: number;
}

/**  */
export interface HuntRankMessage {
  point: number;
  rank: number;
  rankList: HuntPlayerMessage[];
}

/**  */
export interface HuntTrainMessage {
  /** 剩余的参数次数 */
  chance: number;
  /** 目前的战斗进度，打到第几只灵兽(值从0开始) */
  progress: number;
  /** 当前狩猎的灵兽剩余的血量 */
  petRemainHp: number;
  /** 玩家显示的战力 */
  power: number;
  /** 玩家剩余的血量 */
  remainHp: number;
  /** 玩家的血槽大小 */
  totalHp: number;
  bossRemainHp: number;
  bossHp: number;
  /** BOSS战力 */
  bossPower: number;
  /** 战友信息 */
  buddyList: PlayerBaseMessage[];
  /** 猎杀BOSS的玩家信息 当BOSS血量为0时，有实际值 */
  killMessage:
    | HuntKillMessage
    | undefined;
  /** 剩余的BOSS次数 */
  bossChance: number;
  /** 当天距离洪荒活动最近开启的时间   curStamp <= 上半场结束时间 ，则值为上半场开始时间，否则为下半场开始时间 */
  bossStartStamp: number;
  /** 当天距离洪荒活动最晚开启的时间 curStamp <= 上半场结束时间 ，则值为上半场结束时间，否则为下半场结束时间 */
  bossEndStamp: number;
}

function createBaseHuntBossLogMessage(): HuntBossLogMessage {
  return { userId: 0, deductHp: 0, win: false, timeStamp: 0 };
}

export const HuntBossLogMessage: MessageFns<HuntBossLogMessage> = {
  encode(message: HuntBossLogMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== 0) {
      writer.uint32(8).int64(message.userId);
    }
    if (message.deductHp !== 0) {
      writer.uint32(17).double(message.deductHp);
    }
    if (message.win !== false) {
      writer.uint32(24).bool(message.win);
    }
    if (message.timeStamp !== 0) {
      writer.uint32(32).int64(message.timeStamp);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HuntBossLogMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHuntBossLogMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.userId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.deductHp = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.win = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.timeStamp = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<HuntBossLogMessage>, I>>(base?: I): HuntBossLogMessage {
    return HuntBossLogMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HuntBossLogMessage>, I>>(object: I): HuntBossLogMessage {
    const message = createBaseHuntBossLogMessage();
    message.userId = object.userId ?? 0;
    message.deductHp = object.deductHp ?? 0;
    message.win = object.win ?? false;
    message.timeStamp = object.timeStamp ?? 0;
    return message;
  },
};

function createBaseHuntBossRankMessage(): HuntBossRankMessage {
  return { point: 0, rank: 0, rankList: [] };
}

export const HuntBossRankMessage: MessageFns<HuntBossRankMessage> = {
  encode(message: HuntBossRankMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.point !== 0) {
      writer.uint32(9).double(message.point);
    }
    if (message.rank !== 0) {
      writer.uint32(16).int32(message.rank);
    }
    for (const v of message.rankList) {
      HuntPlayerMessage.encode(v!, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HuntBossRankMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHuntBossRankMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.point = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.rank = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.rankList.push(HuntPlayerMessage.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<HuntBossRankMessage>, I>>(base?: I): HuntBossRankMessage {
    return HuntBossRankMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HuntBossRankMessage>, I>>(object: I): HuntBossRankMessage {
    const message = createBaseHuntBossRankMessage();
    message.point = object.point ?? 0;
    message.rank = object.rank ?? 0;
    message.rankList = object.rankList?.map((e) => HuntPlayerMessage.fromPartial(e)) || [];
    return message;
  },
};

function createBaseHuntBossResponse(): HuntBossResponse {
  return { win: false, replay: "", damageHp: 0, resAddList: [], huntTrainMessage: undefined };
}

export const HuntBossResponse: MessageFns<HuntBossResponse> = {
  encode(message: HuntBossResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.win !== false) {
      writer.uint32(8).bool(message.win);
    }
    if (message.replay !== "") {
      writer.uint32(18).string(message.replay);
    }
    if (message.damageHp !== 0) {
      writer.uint32(25).double(message.damageHp);
    }
    writer.uint32(34).fork();
    for (const v of message.resAddList) {
      writer.double(v);
    }
    writer.join();
    if (message.huntTrainMessage !== undefined) {
      HuntTrainMessage.encode(message.huntTrainMessage, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HuntBossResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHuntBossResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.win = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.replay = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.damageHp = reader.double();
          continue;
        }
        case 4: {
          if (tag === 33) {
            message.resAddList.push(reader.double());

            continue;
          }

          if (tag === 34) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.resAddList.push(reader.double());
            }

            continue;
          }

          break;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.huntTrainMessage = HuntTrainMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<HuntBossResponse>, I>>(base?: I): HuntBossResponse {
    return HuntBossResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HuntBossResponse>, I>>(object: I): HuntBossResponse {
    const message = createBaseHuntBossResponse();
    message.win = object.win ?? false;
    message.replay = object.replay ?? "";
    message.damageHp = object.damageHp ?? 0;
    message.resAddList = object.resAddList?.map((e) => e) || [];
    message.huntTrainMessage = (object.huntTrainMessage !== undefined && object.huntTrainMessage !== null)
      ? HuntTrainMessage.fromPartial(object.huntTrainMessage)
      : undefined;
    return message;
  },
};

function createBaseHuntBossUpdateMessage(): HuntBossUpdateMessage {
  return { bossRemainHp: 0, died: false, simpleMessage: undefined, timeStamp: 0, logMessage: undefined };
}

export const HuntBossUpdateMessage: MessageFns<HuntBossUpdateMessage> = {
  encode(message: HuntBossUpdateMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bossRemainHp !== 0) {
      writer.uint32(9).double(message.bossRemainHp);
    }
    if (message.died !== false) {
      writer.uint32(16).bool(message.died);
    }
    if (message.simpleMessage !== undefined) {
      PlayerSimpleMessage.encode(message.simpleMessage, writer.uint32(26).fork()).join();
    }
    if (message.timeStamp !== 0) {
      writer.uint32(32).int64(message.timeStamp);
    }
    if (message.logMessage !== undefined) {
      HuntBossLogMessage.encode(message.logMessage, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HuntBossUpdateMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHuntBossUpdateMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.bossRemainHp = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.died = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.simpleMessage = PlayerSimpleMessage.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.timeStamp = longToNumber(reader.int64());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.logMessage = HuntBossLogMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<HuntBossUpdateMessage>, I>>(base?: I): HuntBossUpdateMessage {
    return HuntBossUpdateMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HuntBossUpdateMessage>, I>>(object: I): HuntBossUpdateMessage {
    const message = createBaseHuntBossUpdateMessage();
    message.bossRemainHp = object.bossRemainHp ?? 0;
    message.died = object.died ?? false;
    message.simpleMessage = (object.simpleMessage !== undefined && object.simpleMessage !== null)
      ? PlayerSimpleMessage.fromPartial(object.simpleMessage)
      : undefined;
    message.timeStamp = object.timeStamp ?? 0;
    message.logMessage = (object.logMessage !== undefined && object.logMessage !== null)
      ? HuntBossLogMessage.fromPartial(object.logMessage)
      : undefined;
    return message;
  },
};

function createBaseHuntKillMessage(): HuntKillMessage {
  return { simpleMessage: undefined, timeStamp: 0 };
}

export const HuntKillMessage: MessageFns<HuntKillMessage> = {
  encode(message: HuntKillMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.simpleMessage !== undefined) {
      PlayerSimpleMessage.encode(message.simpleMessage, writer.uint32(10).fork()).join();
    }
    if (message.timeStamp !== 0) {
      writer.uint32(16).int64(message.timeStamp);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HuntKillMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHuntKillMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.simpleMessage = PlayerSimpleMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.timeStamp = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<HuntKillMessage>, I>>(base?: I): HuntKillMessage {
    return HuntKillMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HuntKillMessage>, I>>(object: I): HuntKillMessage {
    const message = createBaseHuntKillMessage();
    message.simpleMessage = (object.simpleMessage !== undefined && object.simpleMessage !== null)
      ? PlayerSimpleMessage.fromPartial(object.simpleMessage)
      : undefined;
    message.timeStamp = object.timeStamp ?? 0;
    return message;
  },
};

function createBaseHuntPetResponse(): HuntPetResponse {
  return { win: false, replay: "", damageHp: 0, resAddList: [], huntTrain: undefined };
}

export const HuntPetResponse: MessageFns<HuntPetResponse> = {
  encode(message: HuntPetResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.win !== false) {
      writer.uint32(8).bool(message.win);
    }
    if (message.replay !== "") {
      writer.uint32(18).string(message.replay);
    }
    if (message.damageHp !== 0) {
      writer.uint32(25).double(message.damageHp);
    }
    writer.uint32(34).fork();
    for (const v of message.resAddList) {
      writer.double(v);
    }
    writer.join();
    if (message.huntTrain !== undefined) {
      HuntTrainMessage.encode(message.huntTrain, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HuntPetResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHuntPetResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.win = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.replay = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.damageHp = reader.double();
          continue;
        }
        case 4: {
          if (tag === 33) {
            message.resAddList.push(reader.double());

            continue;
          }

          if (tag === 34) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.resAddList.push(reader.double());
            }

            continue;
          }

          break;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.huntTrain = HuntTrainMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<HuntPetResponse>, I>>(base?: I): HuntPetResponse {
    return HuntPetResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HuntPetResponse>, I>>(object: I): HuntPetResponse {
    const message = createBaseHuntPetResponse();
    message.win = object.win ?? false;
    message.replay = object.replay ?? "";
    message.damageHp = object.damageHp ?? 0;
    message.resAddList = object.resAddList?.map((e) => e) || [];
    message.huntTrain = (object.huntTrain !== undefined && object.huntTrain !== null)
      ? HuntTrainMessage.fromPartial(object.huntTrain)
      : undefined;
    return message;
  },
};

function createBaseHuntPlayerMessage(): HuntPlayerMessage {
  return { playerSimpleMessage: undefined, point: 0 };
}

export const HuntPlayerMessage: MessageFns<HuntPlayerMessage> = {
  encode(message: HuntPlayerMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.playerSimpleMessage !== undefined) {
      PlayerSimpleMessage.encode(message.playerSimpleMessage, writer.uint32(10).fork()).join();
    }
    if (message.point !== 0) {
      writer.uint32(17).double(message.point);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HuntPlayerMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHuntPlayerMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.playerSimpleMessage = PlayerSimpleMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.point = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<HuntPlayerMessage>, I>>(base?: I): HuntPlayerMessage {
    return HuntPlayerMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HuntPlayerMessage>, I>>(object: I): HuntPlayerMessage {
    const message = createBaseHuntPlayerMessage();
    message.playerSimpleMessage = (object.playerSimpleMessage !== undefined && object.playerSimpleMessage !== null)
      ? PlayerSimpleMessage.fromPartial(object.playerSimpleMessage)
      : undefined;
    message.point = object.point ?? 0;
    return message;
  },
};

function createBaseHuntRankMessage(): HuntRankMessage {
  return { point: 0, rank: 0, rankList: [] };
}

export const HuntRankMessage: MessageFns<HuntRankMessage> = {
  encode(message: HuntRankMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.point !== 0) {
      writer.uint32(9).double(message.point);
    }
    if (message.rank !== 0) {
      writer.uint32(16).int32(message.rank);
    }
    for (const v of message.rankList) {
      HuntPlayerMessage.encode(v!, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HuntRankMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHuntRankMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.point = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.rank = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.rankList.push(HuntPlayerMessage.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<HuntRankMessage>, I>>(base?: I): HuntRankMessage {
    return HuntRankMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HuntRankMessage>, I>>(object: I): HuntRankMessage {
    const message = createBaseHuntRankMessage();
    message.point = object.point ?? 0;
    message.rank = object.rank ?? 0;
    message.rankList = object.rankList?.map((e) => HuntPlayerMessage.fromPartial(e)) || [];
    return message;
  },
};

function createBaseHuntTrainMessage(): HuntTrainMessage {
  return {
    chance: 0,
    progress: 0,
    petRemainHp: 0,
    power: 0,
    remainHp: 0,
    totalHp: 0,
    bossRemainHp: 0,
    bossHp: 0,
    bossPower: 0,
    buddyList: [],
    killMessage: undefined,
    bossChance: 0,
    bossStartStamp: 0,
    bossEndStamp: 0,
  };
}

export const HuntTrainMessage: MessageFns<HuntTrainMessage> = {
  encode(message: HuntTrainMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.chance !== 0) {
      writer.uint32(8).int32(message.chance);
    }
    if (message.progress !== 0) {
      writer.uint32(16).int32(message.progress);
    }
    if (message.petRemainHp !== 0) {
      writer.uint32(25).double(message.petRemainHp);
    }
    if (message.power !== 0) {
      writer.uint32(33).double(message.power);
    }
    if (message.remainHp !== 0) {
      writer.uint32(41).double(message.remainHp);
    }
    if (message.totalHp !== 0) {
      writer.uint32(49).double(message.totalHp);
    }
    if (message.bossRemainHp !== 0) {
      writer.uint32(57).double(message.bossRemainHp);
    }
    if (message.bossHp !== 0) {
      writer.uint32(65).double(message.bossHp);
    }
    if (message.bossPower !== 0) {
      writer.uint32(73).double(message.bossPower);
    }
    for (const v of message.buddyList) {
      PlayerBaseMessage.encode(v!, writer.uint32(82).fork()).join();
    }
    if (message.killMessage !== undefined) {
      HuntKillMessage.encode(message.killMessage, writer.uint32(90).fork()).join();
    }
    if (message.bossChance !== 0) {
      writer.uint32(96).int32(message.bossChance);
    }
    if (message.bossStartStamp !== 0) {
      writer.uint32(104).int64(message.bossStartStamp);
    }
    if (message.bossEndStamp !== 0) {
      writer.uint32(112).int64(message.bossEndStamp);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HuntTrainMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHuntTrainMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.chance = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.progress = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.petRemainHp = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.power = reader.double();
          continue;
        }
        case 5: {
          if (tag !== 41) {
            break;
          }

          message.remainHp = reader.double();
          continue;
        }
        case 6: {
          if (tag !== 49) {
            break;
          }

          message.totalHp = reader.double();
          continue;
        }
        case 7: {
          if (tag !== 57) {
            break;
          }

          message.bossRemainHp = reader.double();
          continue;
        }
        case 8: {
          if (tag !== 65) {
            break;
          }

          message.bossHp = reader.double();
          continue;
        }
        case 9: {
          if (tag !== 73) {
            break;
          }

          message.bossPower = reader.double();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.buddyList.push(PlayerBaseMessage.decode(reader, reader.uint32()));
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.killMessage = HuntKillMessage.decode(reader, reader.uint32());
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.bossChance = reader.int32();
          continue;
        }
        case 13: {
          if (tag !== 104) {
            break;
          }

          message.bossStartStamp = longToNumber(reader.int64());
          continue;
        }
        case 14: {
          if (tag !== 112) {
            break;
          }

          message.bossEndStamp = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<HuntTrainMessage>, I>>(base?: I): HuntTrainMessage {
    return HuntTrainMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HuntTrainMessage>, I>>(object: I): HuntTrainMessage {
    const message = createBaseHuntTrainMessage();
    message.chance = object.chance ?? 0;
    message.progress = object.progress ?? 0;
    message.petRemainHp = object.petRemainHp ?? 0;
    message.power = object.power ?? 0;
    message.remainHp = object.remainHp ?? 0;
    message.totalHp = object.totalHp ?? 0;
    message.bossRemainHp = object.bossRemainHp ?? 0;
    message.bossHp = object.bossHp ?? 0;
    message.bossPower = object.bossPower ?? 0;
    message.buddyList = object.buddyList?.map((e) => PlayerBaseMessage.fromPartial(e)) || [];
    message.killMessage = (object.killMessage !== undefined && object.killMessage !== null)
      ? HuntKillMessage.fromPartial(object.killMessage)
      : undefined;
    message.bossChance = object.bossChance ?? 0;
    message.bossStartStamp = object.bossStartStamp ?? 0;
    message.bossEndStamp = object.bossEndStamp ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
