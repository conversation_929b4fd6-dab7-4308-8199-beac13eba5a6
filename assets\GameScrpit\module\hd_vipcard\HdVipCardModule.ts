import { GameData } from "../../game/GameData";
import data from "../../lib/data/data";
import { HdVipCardApi } from "./HdVipCardApi";
import { HdVipCardConfig } from "./HdVipCardConfig";
import { HdVipCardData } from "./HdVipCardData";
import { HdVipCardRoute } from "./HdVipCardRoute";
import { HdVipCardSevice } from "./HdVipCardSevice";
import { HdVipCardSubscriber } from "./HdVipCardSubscriber";
import { HdVipCardViewModel } from "./HdVipCardViewModel";

export class HdVipCardModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): HdVipCardModule {
    if (!GameData.instance.HdVipCardModule) {
      GameData.instance.HdVipCardModule = new HdVipCardModule();
    }
    return GameData.instance.HdVipCardModule;
  }
  private _data = new HdVipCardData();
  private _api = new HdVipCardApi();
  private _route = new HdVipCardRoute();
  private _subscriber = new HdVipCardSubscriber();
  private _config = new HdVipCardConfig();
  private _viewModel = new HdVipCardViewModel();
  private _service = new HdVipCardSevice();
  public static get data() {
    return this.instance._data;
  }
  public static get api() {
    return this.instance._api;
  }
  public static get config() {
    return this.instance._config;
  }
  public static get viewModel() {
    return this.instance._viewModel;
  }

  public static get service() {
    return this.instance._service;
  }

  public init(data?: any) {
    if (this._subscriber) {
      this._subscriber.unRegister();
    }
    /**先创建这个才能走红点的消息事件 */
    this._service = new HdVipCardSevice();

    this._data = new HdVipCardData();
    this._api = new HdVipCardApi();
    this._route = new HdVipCardRoute();
    this._subscriber = new HdVipCardSubscriber();
    this._config = new HdVipCardConfig();
    this._viewModel = new HdVipCardViewModel();

    // 初始化模块
    this._service.init();
    this._data.init();
    this._subscriber.register();
    this._route.init();

    HdVipCardModule.api.vipCard();
  }

  protected saveKey(): string {
    return this.constructor.name;
  }
}
