import { _decorator, Label } from "cc";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UINode } from "../../../lib/ui/UINode";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { CityModule } from "../../../module/city/CityModule";
import GuideMgr from "../../../ext_guide/GuideMgr";
import { LangMgr } from "../../mgr/LangMgr";

const { ccclass, property } = _decorator;

@ccclass("UIHuntPrepareLose")
export class UIHuntPrepareLose extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_HUNT}?prefab/ui/UIHuntPrepareLose`;
  }

  protected dependOn(): Array<BundleEnum> {
    return [BundleEnum.BUNDLE_COMMON_UI, BundleEnum.BUNDLE_COMMON_ITEM];
  }

  protected onEvtShow(): void {
    TipsMgr.setEnableTouch(true);
    AudioMgr.instance.playEffect(AudioName.Effect.战斗失败);
  }
  private on_click_btn_close() {
    UIMgr.instance.back();
  }
  protected onEvtClose(): void {}
}
