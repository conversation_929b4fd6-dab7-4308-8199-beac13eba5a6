import { _decorator, Input, instantiate, isValid, Label, Node, ScrollView } from "cc";
import { ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { Sleep } from "../../../GameDefine";
import { RedeemMessage, RedeemRequest, RedeemResponse } from "../../../net/protocol/Activity";
import ToolExt from "../../../common/ToolExt";
import FmUtils from "db://assets/GameScrpit/lib/utils/FmUtils";
import { avId1, DayActivityModule } from "db://assets/GameScrpit/module/day/DayActivityModule";
import MsgMgr from "db://assets/GameScrpit/lib/event/MsgMgr";
import { UIMgr } from "db://assets/GameScrpit/lib/ui/UIMgr";
import { PublicRouteName } from "db://assets/GameScrpit/module/player/PlayerConstant";
import MsgEnum from "../../../event/MsgEnum";
import { JsonMgr } from "../../../mgr/JsonMgr";
import { ConfirmMsg } from "../../UICostConfirm";
import { DayActivityMsgEnum } from "db://assets/GameScrpit/module/day/DayActivityConfig";
import { dtTime } from "../../../BoutStartUp";
import { RedeemPackVO } from "db://assets/GameScrpit/module/activity/ActivityConfig";

const { ccclass, property } = _decorator;
const db_info: string = "db_info";
@ccclass("DatRechargeXianYuViewHolder")
export class DatRechargeXianYuViewHolder extends ViewHolder {
  private _redeemMessage: RedeemMessage = null;

  private _ad_bar: Node = null;
  private _type2_item: Node = null;

  private _lbl_day_item_name: Node = null;
  private _lbl_ad_hit_count: Node = null;
  private _btn_item6_buy: Node = null;
  private _lbl_item6_num: Node = null;

  private _btn_ad: Node = null;
  private _lay_ad_count: Node = null;

  private _lbl_buy_max: Node = null;
  private _lab_ad_free: Node = null;
  private _yishouqin: Node = null;

  private _ScrollView: Node = null;
  private _item_content: Node = null;

  public init() {
    this._redeemMessage = DayActivityModule.data.dayMessage;
    this._ad_bar = this.node.getChildByName("ad_bar");
    this._type2_item = this.node.getChildByName("type2_item");

    this._lbl_day_item_name = this.node.getChildByName("lbl_day_item_name");
    this._lbl_ad_hit_count = this.node.getChildByName("lbl_ad_hit_count");
    this._btn_item6_buy = this.node.getChildByName("btn_item6_buy");
    this._lbl_item6_num = this._btn_item6_buy.getChildByName("lbl_item6_num");
    this._btn_ad = this.node.getChildByName("btn_ad");
    this._lay_ad_count = this._btn_ad.getChildByName("lay_ad_count");
    this._lbl_buy_max = this.node.getChildByName("lbl_buy_max");
    this._lab_ad_free = this._btn_ad.getChildByName("lab_ad_free");
    this._yishouqin = this.node.getChildByName("yishouqin");

    this._ScrollView = this.node.getChildByName("ScrollView");
    this._item_content = this._ScrollView.getChildByPath("view/item_content");

    this._btn_item6_buy.on(Input.EventType.TOUCH_END, this.on_click_btn_item6_buy, this);
    this._btn_ad.on(Input.EventType.TOUCH_END, this.on_click_btn_ad, this);
  }

  protected onDestroy(): void {}

  updateData(data: RedeemPackVO, position: number) {
    this.set_day_item_type2(data);
    this.load_item_type2(data.rewardList);

    this.node[db_info] = data;
    this._btn_ad[db_info] = data;
    this._btn_item6_buy[db_info] = data;
  }

  private set_day_item_type2(info: RedeemPackVO) {
    this._lbl_day_item_name.getComponent(Label).string = info.name;

    let redeem = this._redeemMessage.redeemMap[info.id] || 0;
    if (redeem >= info.max) {
      this._yishouqin.active = true;
      this._lbl_ad_hit_count.active = false;
      this._btn_item6_buy.active = false;
      this._lbl_buy_max.active = false;
      this._btn_ad.active = false;
      return;
    }
    let count = info.max - redeem;
    this._lbl_buy_max.getComponent(Label).string = ToolExt.getMaxtypeLab(info.maxtype) + `(${count}/${info.max})`;
    this._yishouqin.active = false;

    if (info.adNum == 0) {
      this._btn_ad.active = false;
      this._lbl_ad_hit_count.active = false;
      this._btn_item6_buy.active = true;
      this._lbl_item6_num.getComponent(Label).string = "x" + info.cost[1];
    } else if (info.adNum == 1) {
      this._btn_ad.active = true;
      this._lbl_ad_hit_count.active = false;
      this._lab_ad_free.active = true;
      this._lay_ad_count.active = false;
      this._btn_item6_buy.active = false;
    } else {
      this._btn_ad.active = true;
      this._lbl_ad_hit_count.active = true;
      this._lbl_ad_hit_count.getComponent(Label).string = "观看" + info.adNum + "次视频";
      this._btn_item6_buy.active = false;
      this._lab_ad_free.active = false;
      this._lay_ad_count.active = true;
      let ad_num = this._redeemMessage.adMap[info.id] || 0;
      let childrenNum = this._lay_ad_count.children.length;
      for (let i = 0; i < info.adNum && childrenNum < info.adNum; i++) {
        let ad_bar_clone = instantiate(this._ad_bar);
        this._lay_ad_count.addChild(ad_bar_clone);
        ad_bar_clone.active = true;
      }

      this._lay_ad_count.children.forEach((val) => {
        val.getChildByName("has").active = false;
      });

      for (let i = 0; i < ad_num; i++) {
        this._lay_ad_count.children[i].getChildByName("has").active = true;
      }
    }
  }

  private async load_item_type2(list: number[]) {
    let rewardList = ToolExt.traAwardItemMapList(list);
    this._item_content.destroyAllChildren();
    for (let i = 0; i < rewardList.length; i++) {
      await Sleep(dtTime);
      if (isValid(this.node) == false) return;
      let node = instantiate(this._type2_item);
      node.active = true;

      this._item_content.addChild(node);
      FmUtils.setItemNode(node, rewardList[i].id, rewardList[i].num);
    }

    if (rewardList.length <= 3) {
      this._ScrollView.getComponent(ScrollView).enabled = false;
    }
  }

  private on_click_btn_item6_buy(event) {
    let node = event.target.parent;
    let info: RedeemPackVO = node[db_info];
    let msg: ConfirmMsg = {
      msg: "是否花费" + info.cost[1] + JsonMgr.instance.getConfigItem(info.cost[0]).name + "购买该商品？",
      itemList: [],
      stopHintOption: false,
    };
    UIMgr.instance.showDialog(PublicRouteName.UICostConfirm, msg, (resp) => {
      if (resp?.ok) {
        let param: RedeemRequest = {
          activityId: avId1,
          redeemId: info.id,
          count: 1,
        };

        DayActivityModule.api.buyFixedPack(param, (res: RedeemResponse) => {
          let rewardList = res.rewardList;
          MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: rewardList });
          this._redeemMessage.redeemMap = res.redeemMap;
          this._redeemMessage.adMap = res.adMap;
          MsgMgr.emit(DayActivityMsgEnum.REFRESH_DAY_XIANYU_GIFT_LIST, res.redeemMap);
        });
      }
    });
  }

  private on_click_btn_ad(event) {
    let node = event.target.parent;
    let info: RedeemPackVO = node[db_info];

    let param: RedeemRequest = {
      activityId: avId1,
      redeemId: info.id,
      count: 1,
    };

    DayActivityModule.api.watchAdFixedPack(param, (res: RedeemResponse) => {
      let rewardList = res.rewardList;
      if (rewardList.length > 0) {
        MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: rewardList });
      }
      this._redeemMessage.redeemMap = res.redeemMap;
      this._redeemMessage.adMap = res.adMap;
      MsgMgr.emit(DayActivityMsgEnum.REFRESH_DAY_XIANYU_GIFT_LIST, res.redeemMap);
    });
  }
}
