syntax = "proto3";
package sim;

// 
message BossChapterPassResponse {
  // 收回的宝箱ID列表
  repeated int64 treasureIdList = 1;
  // Boss关卡是否通关
  bool win = 2;
  // Boss关卡战斗回放
  string replay = 3;
  // 气运鼓舞次数
  int32 energyEncourage = 4;
  // 道具鼓舞次数
  int32 itemEncourage = 5;
}

// 
message BossChapterRewardResponse {
  // 最新的关卡ID 如果值为-1，则关卡已经全部通关
  int64 chapterId = 1;
  // 最新的子关卡ID
  int32 chapterChildId = 2;
  // 偶数索引为道具的ID，奇数为数量
  repeated double resAddList = 3;
  // 气运鼓舞次数
  int32 energyEncourage = 4;
  // 道具鼓舞次数
  int32 itemEncourage = 5;
  // 繁荣度参数
  double speedParam = 6;
}

// 
message ChapterMessage {
  // 当前关卡ID 如果值为-1，则关卡已经全部通关
  int64 chapterId = 1;
  // 当前是第几个子关卡
  int32 chapterChildId = 2;
  // 气运鼓舞次数
  int32 energyEncourage = 3;
  // 道具鼓舞次数
  int32 itemEncourage = 4;
  // 繁荣度参数
  double speedParam = 5;
  // 宝箱集合
  map<int64,ChapterTreasureMessage> treasureMap = 6;
}

// 
message ChapterTreasureMessage {
  // 箱子的编号
  int64 id = 1;
  // 是否展示在战斗界面上
  bool show = 2;
  // 创建的时间戳
  int64 createTime = 3;
}

// 
message ChapterTreasureResponse {
  // 获取到的资源
  repeated double addResList = 1;
  // 剩余的宝箱
  map<int64,ChapterTreasureMessage> treasureMap = 2;
}

// 
message RegularChapterPassResponse {
  // 偶数索引为道具的ID，奇数为数量
  repeated double resAddList = 1;
  // 新增的宝箱
  repeated ChapterTreasureMessage treasureList = 2;
  // 最新的关卡ID 如果值为-1，则关卡已经全部通关
  int64 chapterId = 3;
  // 最新的子关卡ID
  int32 chapterChildId = 4;
  // 繁荣度参数
  double speedParam = 5;
}

