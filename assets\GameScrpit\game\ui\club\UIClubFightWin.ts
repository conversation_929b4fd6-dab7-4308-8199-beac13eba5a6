import { _decorator, Component, instantiate, Label, Node, sp, tween, v3 } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import ToolExt from "../../common/ToolExt";
import { JsonMgr } from "../../mgr/JsonMgr";
import Formate from "../../../lib/utils/Formate";
import { tweenTagEnum } from "../../GameDefine";
import ResMgr from "../../../lib/common/ResMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { ItemCtrl } from "../../common/ItemCtrl";
import { AudioMgr, AudioName } from "../../../../platform/src/AudioHelper";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
const { ccclass, property } = _decorator;

@ccclass("UIClubFightWin")
export class UIClubFightWin extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_CLUB}?prefab/ui/UIClubFightWin`;
  }

  // private _bossId: number;
  private _monsterId: number;

  private _resAddList: number[] = [];

  private _hurt: number;
  public init(args: any): void {
    super.init(args);
    this._resAddList = args.resAddList;
    // this._bossId = JsonMgr.instance.jsonList.c_unionBoss[args.bossId].showId;
    this._monsterId = JsonMgr.instance.jsonList.c_unionBoss[args.bossId].monsterId;

    this._hurt = args.hurt;
    // this._bossId = args.bossId;
  }

  protected onEvtShow(): void {
    TipsMgr.setEnableTouch(true);
    this.node.on(
      Node.EventType.TOUCH_END,
      () => {
        UIMgr.instance.back();
      },
      this
    );
    AudioMgr.instance.playEffect(AudioName.Effect.战斗胜利);
    this.getNode("bg").scale = v3(0, 0, 0);
    this.getNode("tytanchuang")
      .getComponent(sp.Skeleton)
      .setCompleteListener(() => {
        this.getNode("tytanchuang").getComponent(sp.Skeleton).setAnimation(0, "zi_chenggongjisha_1", true);
      });
    this.getNode("tytanchuang").getComponent(sp.Skeleton).setAnimation(0, "zi_chenggongjisha", false);

    // let bossInfo = JsonMgr.instance.jsonList.c_monsterShow[this._bossId];
    let monster = JsonMgr.instance.jsonList.c_monsterShow[this._monsterId];
    let spineId = monster.spineId;
    let path = JsonMgr.instance.jsonList.c_spineShow[spineId].spineRes;
    // let bossInfo = JsonMgr.instance.jsonList.c_roleShow[this._bossId];

    this.getNode("boss_name").getComponent(Label).string = `${monster.name}`;
    this.getNode("boss_hurt").getComponent(Label).string = `您对${monster.name}造成了${this._hurt}的伤害`;

    // ResMgr.loadSpine(path, this.getNode("boss_spine").getComponent(sp.Skeleton), this, () => {
    //   this.getNode("boss_spine").getComponent(sp.Skeleton).setAnimation(0, "die", false);
    // });

    this.loadRole();

    // ResMgr.loadPrefab(
    //   path,
    //   (prefab) => {
    //     let node = instantiate(prefab);
    //     this.getNode(`boss_${datas[i].id}`).getChildByName("bg_boss_img").addChild(node);
    //     // node.setScale(0.5, 0.5);
    //     node.setPosition(0, 0);
    //     node.getChildByName("render").getComponent(sp.Skeleton).setAnimation(0, "boss_idle", true);
    //   },
    //   this
    // );
    tween(this.getNode("bg"))
      .tag(tweenTagEnum.TopItemAwardPop_Tag)
      .to(0.1, { scale: v3(1, 1, 1) })
      .call(() => {
        let list = ToolExt.traAwardItemMapList(this._resAddList);
        for (let i = 0; i < list.length; i++) {
          let node = instantiate(this.getNode("Item"));
          node.active = true;
          let info = list[i];
          let c_item_info = JsonMgr.instance.getConfigItem(info.id);

          this.getNode("itemContent").addChild(node);
          node.getComponent(ItemCtrl).setItemId(c_item_info.id, info.num);
        }
      })
      .start();
  }

  private async loadRole() {
    let node = await ToolExt.loadUIRole(this.getNode("boss_spine"), this._monsterId, -1, "", this);
    node.getChildByName("renderPoint").getChildByName("render").getComponent(sp.Skeleton).setAnimation(0, "die", false);
  }
}
