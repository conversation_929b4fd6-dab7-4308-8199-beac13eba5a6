import { _decorator, instantiate, Label, Node } from "cc";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { FractureModule } from "../../../module/fracture/FractureModule";
import FmUtils from "../../../lib/utils/FmUtils";
import { routeConfig, RouteShowArgs } from "db://assets/platform/src/core/managers/RouteTableManager";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { FractureState } from "../../../module/fracture/FractureService";
import { JsonMgr } from "../../mgr/JsonMgr";
const { ccclass, property } = _decorator;

@ccclass("UIFractureDrawPreview")
@routeConfig({
  bundle: BundleEnum.BUNDLE_G_FRACTURE,
  url: "prefab/ui/UIFractureDrawPreview",
  nextHop: [],
  exit: "dialog_close",
})
export class UIFractureDrawPreview extends BaseCtrl {
  public playShowAni: boolean = true;
  private drawItemList: any[] = [];
  private drawNodes: Node[] = [];

  init(args: RouteShowArgs): void {
    super.init(args);
  }

  start() {
    super.start();
    if (FractureModule.service.getFractureState() == FractureState.DRAW) {
      let floor = FractureModule.data.fractureData.floorId;
      this.drawItemList = FractureModule.config.getFractureDrawConfig(floor);

      // 初始化节点池
      this.drawNodes = [];
      for (let i = 0; i < this.drawItemList.length; i++) {
        let child = this.getNode("node_draws").children[i];
        if (!child) {
          child = instantiate(this.getNode("node_draws").children[0]);
          this.getNode("node_draws").addChild(child);
        }
        child.getChildByName("lbl_prob").getComponent(Label).string = `${this.drawItemList[i].rate / 100}%`;
        FmUtils.setItemNode(child.getChildByName("item"), this.drawItemList[i].rewardList[0]);
        this.drawNodes.push(child);
      }
    } else {
      let fractureId = FractureModule.data.fractureData.fractureId;
      let fracture = JsonMgr.instance.jsonList.c_fracture[fractureId];
      for (let i = 0; i < fracture.reward02List.length; i++) {
        let child = this.getNode("node_draws").children[i];
        if (!child) {
          child = instantiate(this.getNode("node_draws").children[0]);
          this.getNode("node_draws").addChild(child);
        }
        child.getChildByName("lbl_prob").getComponent(Label).string = `${fracture.reward02List[i][2] / 100}%`;
        FmUtils.setItemNode(child.getChildByName("item"), fracture.reward02List[i][0]);
      }
    }
  }

  update(deltaTime: number) {}
}
