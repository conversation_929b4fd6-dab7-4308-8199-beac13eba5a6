{"skeleton": {"hash": "FsZ14uIexJB8yKN318EpyVZcpQ8", "spine": "3.8.75", "x": -366.54, "y": -516.6, "width": 678.76, "height": 1195.14, "images": "./images/", "audio": "D:/仙友spine/蔡文姬"}, "bones": [{"name": "root", "scaleX": 0.82, "scaleY": 0.82}, {"name": "bone", "parent": "root", "length": 337.62, "rotation": -88.77, "x": 546.39, "y": 183.05}, {"name": "st", "parent": "bone", "length": 285.93, "rotation": 69.1, "x": 253.65, "y": -502.55}, {"name": "st2", "parent": "bone", "length": 137.21, "rotation": -160.88, "x": 220.53, "y": -500.32}, {"name": "st3", "parent": "st2", "x": 190.52, "y": -54.46}, {"name": "st4", "parent": "st2", "length": 50.77, "rotation": -42.57, "x": 358.98, "y": 28.52}, {"name": "st5", "parent": "st", "length": 103.16, "rotation": -68.2, "x": 270.02, "y": -33.17}, {"name": "st6", "parent": "st5", "length": 118.5, "rotation": -4.89, "x": 112.41, "y": -7.98}, {"name": "st7", "parent": "st6", "length": 118.36, "rotation": 2.77, "x": 145.1, "y": 3.2}, {"name": "st8", "parent": "st7", "length": 94.3, "rotation": -21.37, "x": -196.63, "y": -104.99}, {"name": "st9", "parent": "st8", "length": 85.14, "rotation": 1.72, "x": 111.69, "y": 2.71}, {"name": "st10", "parent": "st9", "length": 85.37, "rotation": 9.35, "x": 98.36, "y": 2.7}, {"name": "s1", "parent": "st2", "length": 188.19, "rotation": 157.92, "x": 271.34, "y": 158.91}, {"name": "s2", "parent": "s1", "length": 219.6, "rotation": 84.28, "x": 188.12, "y": 2.28}, {"name": "s3", "parent": "s1", "length": 66.16, "rotation": 2.86, "x": 190.14, "y": -48.29}, {"name": "s4", "parent": "s3", "length": 71.52, "rotation": -5.28, "x": 87.09, "y": 8.67}, {"name": "s5", "parent": "s4", "length": 67.87, "rotation": -59.76, "x": 102.57, "y": 2.26}, {"name": "s6", "parent": "st2", "length": 142.9, "rotation": 173.95, "x": 169.48, "y": -75.09}, {"name": "s7", "parent": "s6", "length": 179.89, "rotation": 138.26, "x": 140.8, "y": 9.64}, {"name": "s8", "parent": "s7", "length": 37.87, "rotation": 16.02, "x": 182.69, "y": 32.97}, {"name": "s10", "parent": "s7", "length": 43.4, "rotation": 37.4, "x": 202.95, "y": -30.45}, {"name": "s11", "parent": "s10", "length": 24.14, "rotation": 33.16, "x": 46.44, "y": 2.13}, {"name": "s12", "parent": "s7", "length": 34.29, "rotation": 40.72, "x": 184.5, "y": -30.74}, {"name": "s13", "parent": "s12", "length": 15.66, "rotation": 34.63, "x": 38.24, "y": 3.69}, {"name": "s14", "parent": "s7", "length": 21.08, "rotation": -11.06, "x": 154.51, "y": -32.37}, {"name": "s15", "parent": "s14", "length": 21.54, "rotation": 121.5, "x": 18.36, "y": 8.85}, {"name": "yq", "parent": "s7", "length": 280.13, "rotation": -163.34, "x": 226.36, "y": 15.65}, {"name": "s16", "parent": "s2", "length": 55.6, "rotation": -5.02, "x": 233.93, "y": -10.01}, {"name": "s17", "parent": "s16", "length": 31.35, "rotation": -42.93, "x": 70.04, "y": 6.84}, {"name": "s18", "parent": "s17", "length": 32.05, "rotation": -9.09, "x": 37.01, "y": -1.2}, {"name": "s9", "parent": "s16", "length": 30.47, "rotation": -48.71, "x": 65.92, "y": -9.57}, {"name": "s19", "parent": "s9", "length": 32.81, "rotation": -8.16, "x": 34.98, "y": -1.97}, {"name": "s21", "parent": "s16", "length": 24.81, "rotation": -44.97, "x": 55.63, "y": -22.78}, {"name": "s22", "parent": "s21", "length": 30.16, "rotation": -10.91, "x": 27.54, "y": -0.69}, {"name": "s23", "parent": "s16", "length": 14.22, "rotation": -57.38, "x": 46.06, "y": -37.21}, {"name": "s24", "parent": "s23", "length": 19.56, "rotation": -2.94, "x": 18.59, "y": 0.29}, {"name": "t1", "parent": "st4", "length": 58.17, "rotation": 58.21, "x": 32.04, "y": -44.5}, {"name": "t2", "parent": "t1", "x": 16.91, "y": -60.58}, {"name": "t3", "parent": "t1", "length": 127.48, "rotation": 52.41, "x": 96.36, "y": -174.82}, {"name": "t5", "parent": "t1", "length": 33.77, "rotation": 129.97, "x": 119.67, "y": 5.89}, {"name": "t6", "parent": "t5", "length": 35.61, "rotation": -0.23, "x": 40.72, "y": -1.28}, {"name": "t7", "parent": "t6", "length": 22.03, "rotation": 3.55, "x": 40.87, "y": -1.68}, {"name": "t8", "parent": "t3", "length": 30.71, "rotation": 51.23, "x": 168.56, "y": 6.64}, {"name": "t9", "parent": "t8", "length": 31.19, "rotation": 17.17, "x": 34.66, "y": 2.59}, {"name": "t10", "parent": "t9", "length": 34.59, "rotation": 4.46, "x": 36.56, "y": -2.15}, {"name": "t11", "parent": "t1", "length": 23.37, "rotation": 144, "x": 83.04, "y": 5.47}, {"name": "t12", "parent": "t11", "length": 16.25, "rotation": 7.13, "x": 25.39, "y": -1.61}, {"name": "t13", "parent": "t12", "length": 16.97, "rotation": -3.04, "x": 19.04, "y": -0.35}, {"name": "t14", "parent": "t1", "length": 14.92, "rotation": 173.06, "x": -27.45, "y": -37.6}, {"name": "t15", "parent": "t14", "length": 19.83, "rotation": 5.02, "x": 17.92, "y": -0.56}, {"name": "t16", "parent": "t3", "length": 42.75, "rotation": -66.47, "x": -3, "y": -71.81}, {"name": "t17", "parent": "t16", "length": 32.46, "rotation": 69.89, "x": 53.39, "y": 14.03}, {"name": "t18", "parent": "t17", "length": 35.14, "rotation": 53.33, "x": 44.01, "y": 6.85}, {"name": "st12", "parent": "st2", "length": 25.41, "rotation": -151.16, "x": 302.99, "y": 1.73}, {"name": "t19", "parent": "t1", "length": 17.52, "rotation": -177.11, "x": 68.32, "y": -177.75}, {"name": "t20", "parent": "t19", "length": 19.62, "rotation": -19.23, "x": 20.68, "y": -0.29}, {"name": "t21", "parent": "t20", "length": 15.31, "rotation": -1.22, "x": 26.28, "y": -0.98}, {"name": "t22", "parent": "t21", "length": 15.67, "rotation": 33.41, "x": 19.2, "y": 1.95}, {"name": "t23", "parent": "t1", "length": 22.82, "rotation": 163.14, "x": 43.69, "y": -167.48}, {"name": "t24", "parent": "t23", "length": 23.32, "rotation": -19.13, "x": 26.56, "y": -1.3}, {"name": "t25", "parent": "t24", "length": 23.86, "rotation": -5.29, "x": 25.52, "y": -0.44}, {"name": "t26", "parent": "t25", "length": 21.85, "rotation": -3.98, "x": 30, "y": 0.57}, {"name": "t27", "parent": "t26", "length": 23.32, "rotation": -22.62, "x": 27.63, "y": 0.05}, {"name": "target2", "parent": "root", "x": 144.34, "y": -64.75, "color": "ff3f00ff"}], "slots": [{"name": "bg", "bone": "root", "attachment": "bg"}, {"name": "zt", "bone": "root", "attachment": "zt"}, {"name": "s6", "bone": "s6", "attachment": "s6"}, {"name": "st", "bone": "bone", "attachment": "st"}, {"name": "st2", "bone": "st12", "attachment": "st2"}, {"name": "t8", "bone": "t14", "attachment": "t8"}, {"name": "yq", "bone": "yq", "attachment": "yq"}, {"name": "s7", "bone": "s10", "attachment": "s7"}, {"name": "s8", "bone": "s12", "attachment": "s8"}, {"name": "s9", "bone": "s14", "attachment": "s9"}, {"name": "s1", "bone": "s1", "attachment": "s1"}, {"name": "s5", "bone": "s9", "attachment": "s5"}, {"name": "s4", "bone": "s21", "attachment": "s4"}, {"name": "s3", "bone": "s23", "attachment": "s3"}, {"name": "s2", "bone": "s16", "attachment": "s2"}, {"name": "t1", "bone": "t1", "attachment": "t1"}, {"name": "t3", "bone": "t5", "attachment": "t3"}, {"name": "t2", "bone": "t19", "attachment": "t2"}, {"name": "t4", "bone": "t23", "attachment": "t4"}, {"name": "t5", "bone": "t11", "attachment": "t5"}, {"name": "t6", "bone": "t16", "attachment": "t6"}, {"name": "t7", "bone": "t8", "attachment": "t7"}], "ik": [{"name": "target", "bones": ["s7", "yq"], "target": "target2", "bendPositive": false}], "skins": [{"name": "default", "attachments": {"t4": {"t4": {"type": "mesh", "uvs": [0.73688, 0, 0.8635, 0.13199, 1, 0.2743, 1, 0.3384, 0.94532, 0.44001, 0.89512, 0.53329, 0.84487, 0.62666, 0.79014, 0.72837, 0.73728, 0.82658, 0.69919, 0.89736, 0.50776, 0.96346, 0.40192, 1, 0.3109, 1, 0.243, 0.9392, 0.10174, 0.81269, 0.00701, 0.72786, 0.12013, 0.70735, 0.27514, 0.67923, 0.46024, 0.64566, 0.5397, 0.54661, 0.6106, 0.45824, 0.67165, 0.38213, 0.66229, 0.27768, 0.65206, 0.1636, 0.63872, 0.01493, 0.64099, 0, 0.85038, 0.27869, 0.83178, 0.37023, 0.79991, 0.45463, 0.74413, 0.53785, 0.69366, 0.63177, 0.60335, 0.72541, 0.44397, 0.79317, 0.49178, 0.90254], "triangles": [16, 17, 32, 14, 15, 16, 14, 16, 32, 32, 31, 8, 8, 33, 32, 9, 33, 8, 14, 32, 33, 13, 14, 33, 10, 33, 9, 13, 33, 10, 11, 12, 13, 10, 11, 13, 30, 29, 6, 18, 19, 30, 31, 18, 30, 17, 18, 31, 7, 30, 6, 31, 30, 7, 32, 17, 31, 8, 31, 7, 28, 21, 27, 28, 27, 4, 20, 21, 28, 5, 28, 4, 29, 20, 28, 29, 28, 5, 19, 20, 29, 6, 29, 5, 30, 19, 29, 26, 22, 23, 2, 26, 1, 26, 2, 3, 27, 22, 26, 27, 26, 3, 21, 22, 27, 4, 27, 3, 0, 24, 25, 1, 24, 0, 1, 23, 24, 1, 26, 23], "vertices": [1, 58, -4.67, 4.46, 1, 1, 58, 15.82, 5.93, 1, 2, 58, 37.91, 7.51, 0.00175, 59, 7.83, 12.04, 0.99825, 2, 59, 17, 12.04, 0.97772, 60, -9.64, 11.64, 0.02228, 2, 59, 31.53, 8.54, 0.14809, 60, 5.15, 9.49, 0.85192, 2, 60, 18.73, 7.53, 0.99493, 61, -11.72, 6.16, 0.00507, 2, 60, 32.32, 5.55, 0.01875, 61, 1.98, 5.14, 0.98125, 1, 61, 16.9, 4.02, 1, 1, 62, 2.27, 4.08, 1, 1, 62, 12.16, 7.36, 1, 1, 62, 26.65, 1.95, 1, 2, 61, 59.23, -14.24, 0.00016, 62, 34.67, -1.04, 0.99984, 2, 61, 60.17, -19.99, 0.00336, 62, 37.75, -5.99, 0.99664, 2, 61, 52.29, -25.68, 0.04542, 62, 32.66, -14.27, 0.95458, 2, 61, 35.9, -37.52, 0.29723, 62, 22.08, -31.5, 0.70277, 2, 61, 24.9, -45.45, 0.37815, 62, 14.98, -43.06, 0.62185, 2, 61, 20.84, -38.78, 0.4042, 62, 8.66, -38.46, 0.5958, 3, 60, 43.17, -30.06, 0.00059, 61, 15.27, -29.64, 0.53547, 62, 0.01, -32.16, 0.46394, 3, 60, 37.3, -18.71, 0.06967, 61, 8.63, -18.72, 0.78096, 62, -10.32, -24.64, 0.14937, 3, 60, 22.73, -14.95, 0.68541, 61, -6.17, -15.98, 0.31332, 62, -25.04, -27.81, 0.00126, 2, 60, 9.72, -11.6, 0.99714, 61, -19.38, -13.54, 0.00286, 2, 59, 23.25, -8.98, 0.41624, 60, -1.47, -8.71, 0.58376, 3, 58, 31.28, -13.07, 0.00988, 59, 8.31, -9.58, 0.98992, 60, -16.29, -10.68, 0.00019, 2, 58, 15.65, -8.34, 0.88011, 59, -8, -10.23, 0.11989, 1, 58, -4.71, -2.18, 1, 1, 58, -6.68, -1.34, 1, 1, 59, 8.46, 2.46, 1, 1, 59, 21.55, 1.27, 1, 2, 59, 33.62, -0.77, 0.00039, 60, 8.09, 0.42, 0.99961, 1, 60, 20.27, -2.04, 1, 3, 60, 33.94, -4.01, 0.05466, 61, 4.26, -4.3, 0.94284, 62, -19.9, -13.01, 0.0025, 3, 60, 47.81, -8.54, 0.00075, 61, 18.4, -7.84, 0.83862, 62, -5.48, -10.84, 0.16063, 2, 61, 29.61, -16.35, 0.31169, 62, 8.14, -14.38, 0.68831, 2, 61, 44.55, -10.81, 0.01204, 62, 19.8, -3.52, 0.98796], "hull": 26, "edges": [0, 50, 4, 6, 22, 24, 48, 50, 46, 48, 0, 2, 2, 4, 46, 2, 42, 44, 44, 46, 44, 52, 52, 4, 42, 54, 54, 6, 40, 42, 40, 56, 6, 8, 56, 8, 36, 38, 38, 40, 38, 58, 8, 10, 58, 10, 36, 60, 10, 12, 60, 12, 34, 36, 34, 62, 12, 14, 62, 14, 30, 32, 32, 34, 32, 64, 14, 16, 16, 18, 64, 16, 28, 30, 28, 66, 66, 18, 24, 26, 26, 28, 18, 20, 20, 22, 26, 20], "width": 64, "height": 143}}, "t5": {"t5": {"type": "mesh", "uvs": [0.49404, 0, 0.54466, 0.16186, 0.69564, 0.36307, 0.81166, 0.51769, 0.9322, 0.67833, 1, 0.76869, 1, 0.78482, 0.80214, 0.87971, 0.55132, 1, 0.54651, 1, 0.23408, 0.89288, 0.00125, 0.81305, 0.05517, 0.70473, 0.12593, 0.56257, 0.23431, 0.34484, 0.31855, 0.1756, 0.3999, 0.01219], "triangles": [4, 12, 13, 7, 4, 5, 4, 10, 12, 6, 7, 5, 10, 11, 12, 4, 7, 10, 9, 10, 7, 8, 9, 7, 13, 14, 2, 13, 2, 3, 4, 13, 3, 16, 0, 1, 15, 16, 1, 14, 15, 1, 2, 14, 1], "vertices": [1, 45, -5.69, 1.33, 1, 1, 45, 6.94, 3.36, 1, 2, 45, 22.63, 9.4, 0.83996, 46, -1.37, 11.27, 0.16004, 3, 45, 34.69, 14.04, 0.15708, 46, 11.17, 14.38, 0.67962, 47, -8.64, 14.29, 0.1633, 3, 45, 47.22, 18.86, 0.00669, 46, 24.2, 17.61, 0.20767, 47, 4.2, 18.21, 0.78565, 3, 45, 54.27, 21.57, 5e-05, 46, 31.53, 19.42, 0.0731, 47, 11.42, 20.41, 0.92685, 2, 46, 32.78, 19.27, 0.0678, 47, 12.68, 20.32, 0.9322, 2, 46, 39.14, 10.5, 0.00671, 47, 19.5, 11.9, 0.99329, 1, 47, 28.14, 1.22, 1, 1, 47, 28.13, 1.03, 1, 1, 47, 18.9, -10.84, 1, 2, 46, 30.01, -20.65, 0.00096, 47, 12.03, -19.69, 0.99904, 2, 46, 21.9, -17.46, 0.07389, 47, 3.75, -16.93, 0.92611, 2, 46, 11.24, -13.27, 0.60038, 47, -7.11, -13.32, 0.39962, 3, 45, 21.21, -9.05, 0.40471, 46, -5.07, -6.87, 0.59509, 47, -23.74, -7.79, 0.0002, 2, 45, 8.01, -5.68, 0.99656, 46, -17.75, -1.89, 0.00344, 1, 45, -4.74, -2.43, 1], "hull": 17, "edges": [0, 32, 0, 2, 10, 12, 16, 18, 30, 32, 28, 30, 2, 4, 28, 4, 26, 28, 4, 6, 26, 6, 22, 24, 24, 26, 6, 8, 8, 10, 24, 8, 18, 20, 20, 22, 12, 14, 14, 16, 20, 14], "width": 40, "height": 78}}, "t6": {"t6": {"type": "mesh", "uvs": [0.71143, 0.00833, 0.84625, 0.1901, 0.99684, 0.39315, 0.99517, 0.54704, 0.99185, 0.85188, 0.92596, 1, 0.88149, 1, 0.63481, 0.73756, 0.42559, 0.68711, 0, 0.83692, 0, 0.73909, 0.01144, 0.48896, 0.02045, 0.29188, 0.19359, 0.15309, 0.37434, 0.0082, 0.38362, 0.11923, 0.6959, 0.1372, 0.31126, 0.26296, 0.67432, 0.27374, 0.30745, 0.40848, 0.69971, 0.41926, 0.29602, 0.59174, 0.68956, 0.54862], "triangles": [17, 13, 15, 17, 15, 18, 12, 13, 17, 19, 12, 17, 19, 17, 18, 11, 12, 19, 21, 11, 19, 21, 19, 22, 8, 21, 22, 10, 11, 21, 9, 10, 21, 9, 21, 8, 16, 15, 14, 0, 16, 14, 13, 14, 15, 16, 0, 1, 18, 15, 16, 18, 16, 1, 18, 1, 2, 20, 18, 2, 20, 19, 18, 3, 20, 2, 22, 19, 20, 22, 20, 3, 7, 8, 22, 4, 22, 3, 7, 22, 4, 6, 7, 4, 5, 6, 4], "vertices": [2, 50, 68.06, -13.75, 0.58296, 51, -21.05, -23.32, 0.41704, 2, 50, 39.75, -26.63, 0.99761, 51, -42.87, -1.17, 0.00239, 1, 50, 8.13, -41.01, 1, 1, 50, -8.61, -33.97, 1, 1, 50, -41.77, -20.02, 1, 1, 50, -53.87, -3.28, 1, 1, 50, -51.1, 3.61, 1, 3, 50, -6.99, 30.25, 0.82698, 51, -5.53, 62.28, 0.07331, 52, 14.87, 72.85, 0.09971, 3, 50, 11.58, 60.44, 0.22589, 51, 29.2, 55.22, 0.14472, 52, 29.95, 40.77, 0.62938, 1, 52, 85.08, -7.45, 1, 1, 52, 75.6, -14.05, 1, 1, 52, 50.29, -29.35, 1, 1, 52, 30.35, -41.4, 1, 2, 51, 65.93, -8.99, 0.05802, 52, 0.38, -27.03, 0.94198, 2, 51, 35.21, -25.12, 0.91682, 52, -30.9, -12.03, 0.08318, 2, 51, 34.08, -11.98, 0.91404, 52, -21.03, -3.28, 0.08596, 2, 50, 54.92, -5.67, 0.6988, 51, -17.98, -8.2, 0.3012, 2, 51, 46.7, 4.59, 0.01317, 52, -0.21, -3.5, 0.98683, 3, 50, 41.32, 3.69, 0.88431, 51, -13.86, 7.78, 0.11426, 52, -33.82, 46.98, 0.00142, 3, 50, 49.45, 66.47, 0.00923, 51, 47.88, 21.73, 0.02895, 52, 14.25, 5.79, 0.96183, 3, 50, 23.8, 6.17, 0.94136, 51, -17.56, 25.08, 0.04795, 52, -22.15, 60.28, 0.01069, 3, 50, 30.1, 76.31, 0.03748, 51, 50.47, 43.29, 0.0325, 52, 33.08, 16.58, 0.93002, 3, 50, 10.28, 13.45, 0.90165, 51, -15.38, 40.29, 0.05815, 52, -8.65, 67.61, 0.0402], "hull": 15, "edges": [0, 28, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 24, 26, 26, 28, 26, 30, 30, 32, 0, 2, 2, 4, 32, 2, 24, 34, 34, 36, 36, 4, 20, 22, 22, 24, 22, 38, 38, 40, 4, 6, 6, 8, 40, 6, 20, 42, 42, 44], "width": 167, "height": 118}}, "t7": {"t7": {"type": "mesh", "uvs": [1, 0.00278, 1, 0.06864, 0.92607, 0.18215, 0.84069, 0.31323, 0.73359, 0.47766, 0.62628, 0.64242, 0.52551, 0.74217, 0.41467, 0.85191, 0.27825, 0.98695, 0, 1, 0, 0.96211, 0.03109, 0.81157, 0.06557, 0.64463, 0.09395, 0.50725, 0.26756, 0.36768, 0.42944, 0.23755, 0.58745, 0.11054, 0.71175, 0.01062], "triangles": [12, 13, 5, 6, 12, 5, 7, 11, 12, 6, 7, 12, 8, 10, 11, 7, 8, 11, 9, 10, 8, 4, 15, 3, 14, 15, 4, 5, 14, 4, 13, 14, 5, 17, 0, 1, 2, 17, 1, 16, 17, 2, 3, 16, 2, 15, 16, 3], "vertices": [1, 42, -14.44, 3.76, 1, 1, 42, -8.62, 8.71, 1, 2, 42, 4.77, 13.29, 0.97853, 43, -25.4, 19.05, 0.02147, 2, 42, 20.22, 18.59, 0.62851, 43, -9.07, 19.55, 0.37149, 3, 42, 39.61, 25.23, 0.02215, 43, 11.42, 20.17, 0.97777, 44, -23.33, 24.21, 8e-05, 2, 43, 31.94, 20.79, 0.76752, 44, -2.82, 23.23, 0.23248, 2, 43, 45.36, 18.87, 0.28416, 44, 10.4, 20.27, 0.71584, 2, 43, 60.11, 16.75, 0.024, 44, 24.95, 17.01, 0.976, 1, 44, 42.85, 13, 1, 1, 44, 50.54, -4.95, 1, 1, 44, 46.38, -6.37, 1, 1, 44, 29.15, -9.92, 1, 2, 43, 47.64, -15.18, 0.00136, 44, 10.03, -13.85, 0.99865, 2, 43, 32.21, -19.63, 0.31486, 44, -5.7, -17.09, 0.68514, 3, 42, 51.02, -7.89, 0.0302, 43, 12.54, -14.84, 0.91379, 44, -24.93, -10.78, 0.05601, 2, 42, 32.18, -9.03, 0.8466, 43, -5.8, -10.37, 0.1534, 1, 42, 13.79, -10.15, 1, 1, 42, -0.68, -11.03, 1], "hull": 18, "edges": [0, 34, 0, 2, 16, 18, 18, 20, 32, 34, 2, 4, 32, 4, 30, 32, 4, 6, 30, 6, 26, 28, 28, 30, 6, 8, 8, 10, 28, 8, 24, 26, 10, 12, 24, 12, 20, 22, 22, 24, 12, 14, 14, 16, 22, 14], "width": 70, "height": 116}}, "t8": {"t8": {"type": "mesh", "uvs": [0.33638, 0, 0.55318, 0.11492, 0.76324, 0.22627, 0.84213, 0.46194, 0.89795, 0.62869, 0.95499, 0.79909, 1, 0.93354, 1, 0.99325, 0.77577, 0.97083, 0.42032, 0.9353, 0.03377, 0.89665, 0.03152, 0.69992, 0.02924, 0.50047, 0.02708, 0.31186, 0.02438, 0.07527, 0.3174, 0, 0.35617, 0.20521, 0.48289, 0.38834, 0.52762, 0.57694, 0.63571, 0.68901, 0.83698, 0.83934], "triangles": [8, 6, 7, 8, 20, 6, 8, 9, 20, 9, 19, 20, 9, 10, 19, 20, 5, 6, 19, 11, 18, 19, 10, 11, 5, 20, 4, 20, 19, 4, 11, 12, 18, 4, 19, 3, 19, 18, 3, 18, 17, 3, 12, 17, 18, 17, 13, 16, 17, 12, 13, 17, 2, 3, 17, 1, 2, 17, 16, 1, 16, 14, 15, 15, 0, 16, 16, 13, 14, 16, 0, 1], "vertices": [1, 48, -4.03, 6.68, 1, 2, 48, 3.97, 10.42, 0.99895, 49, -12.94, 12.17, 0.00105, 2, 48, 11.71, 14.05, 0.8956, 49, -4.91, 15.1, 0.1044, 2, 48, 22.25, 11.17, 0.31645, 49, 5.34, 11.31, 0.68355, 2, 48, 29.7, 9.14, 0.03122, 49, 12.58, 8.63, 0.96878, 1, 49, 19.99, 5.9, 1, 1, 49, 25.83, 3.74, 1, 1, 49, 28.06, 2.23, 1, 1, 49, 23.08, -3.33, 1, 1, 49, 15.18, -12.15, 1, 2, 48, 26.39, -21.64, 0.00385, 49, 6.59, -21.74, 0.99615, 2, 48, 18.62, -17.41, 0.11189, 49, -0.78, -16.84, 0.88811, 2, 48, 10.73, -13.12, 0.55332, 49, -8.26, -11.88, 0.44668, 2, 48, 3.28, -9.06, 0.93988, 49, -15.33, -7.18, 0.06012, 1, 48, -6.07, -3.97, 1, 1, 48, -4.33, 6.13, 1, 1, 48, 4.36, 2.77, 1, 2, 48, 13.59, 2.42, 0.96176, 49, -4.05, 3.35, 0.03824, 1, 49, 3.81, -0.18, 1, 1, 49, 9.98, -0.05, 1, 1, 49, 19.31, 1.66, 1], "hull": 16, "edges": [0, 30, 12, 14, 28, 30, 26, 28, 26, 32, 0, 2, 2, 4, 32, 2, 24, 26, 24, 34, 34, 4, 20, 22, 22, 24, 22, 36, 4, 6, 36, 6, 20, 38, 6, 8, 38, 8, 18, 20, 18, 40, 8, 10, 10, 12, 40, 10, 14, 16, 16, 18], "width": 33, "height": 45}}, "bg": {"bg": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [380.76, -611.19, -375.24, -611.19, -375.24, 827.49, 380.76, 827.49], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 189, "height": 315}}, "yq": {"yq": {"type": "mesh", "uvs": [0.50126, 0, 0.75698, 0.01041, 1, 0.09095, 1, 0.11044, 0.94976, 0.1871, 0.72269, 0.30781, 0.66708, 0.4724, 0.88369, 0.71064, 0.72701, 0.96883, 0.46121, 1, 0.30946, 1, 0.03391, 0.91463, 0, 0.78582, 0, 0.72044, 0.09379, 0.59763, 0.4626, 0.45401, 0.51217, 0.40227, 0.6216, 0.21238, 0.62752, 0.12698, 0.45063, 0.09782, 0.47615, 0, 0.49686, 0.56243, 0.44941, 0.65546, 0.40853, 0.7507, 0.37418, 0.84442, 0.55419, 0.56668, 0.51335, 0.66415, 0.4741, 0.75863, 0.42509, 0.85613, 0.59848, 0.57395, 0.56252, 0.66917, 0.52005, 0.76664, 0.47925, 0.86633], "triangles": [18, 1, 2, 18, 0, 1, 3, 18, 2, 19, 20, 0, 18, 19, 0, 4, 18, 3, 17, 18, 4, 5, 17, 4, 16, 17, 5, 6, 16, 5, 15, 16, 6, 21, 15, 6, 25, 21, 6, 29, 25, 6, 14, 15, 21, 22, 14, 21, 22, 21, 25, 26, 22, 25, 29, 26, 25, 30, 26, 29, 29, 6, 7, 30, 29, 7, 23, 13, 14, 22, 23, 14, 23, 22, 26, 27, 23, 26, 27, 26, 30, 31, 27, 30, 31, 30, 7, 12, 13, 23, 24, 12, 23, 24, 23, 27, 28, 24, 27, 31, 28, 27, 32, 28, 31, 11, 12, 24, 8, 31, 7, 32, 31, 8, 10, 11, 24, 10, 24, 28, 9, 10, 28, 32, 9, 28, 8, 9, 32], "vertices": [-167.46, -83.34, -175.12, -6.19, -136.23, 75.93, -123.5, 78.36, -70.58, 72.99, 21.1, 20.65, 131.76, 24.63, 275.15, 118.53, 452.67, 104.18, 488.05, 29.2, 496.63, -15.82, 456.43, -108.19, 374.2, -134.27, 331.49, -142.41, 245.96, -129.87, 131.3, -38.32, 94.7, -30.05, -35.53, -21.22, -91.65, -30.09, -100.7, -86.2, -166.04, -90.79, 200.19, -14.67, 263.65, -17.17, 328.17, -17.45, 391.34, -15.98, 199.73, 2.87, 265.71, 2.88, 329.65, 2.99, 396.11, 0.58, 201.98, 16.91, 266.21, 18.09, 332.29, 17.62, 399.72, 17.92], "hull": 21, "edges": [0, 40, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 42, 44, 44, 46, 46, 48, 50, 52, 52, 54, 54, 56, 58, 60, 60, 62, 62, 64], "width": 302, "height": 665}}, "s1": {"s1": {"type": "mesh", "uvs": [0.56589, 0.12576, 0.62728, 0.23262, 0.68301, 0.32429, 0.80864, 0.33196, 0.8639, 0.33534, 0.9609, 0.34126, 1, 0.39403, 1, 0.46034, 0.9194, 0.57789, 0.86239, 0.66103, 0.79252, 0.76293, 0.70984, 0.88351, 0.60873, 0.98595, 0.48511, 0.99351, 0.37908, 1, 0.36085, 1, 0.15748, 0.95191, 0.00139, 0.91501, 0.00141, 0.84654, 0.00143, 0.74589, 0.00146, 0.63116, 0.14876, 0.61261, 0.17254, 0.56643, 0.20636, 0.50076, 0.22837, 0.44151, 0.26186, 0.35134, 0.29408, 0.26459, 0.32463, 0.18235, 0.34949, 0.11542, 0.39237, 0, 0.42547, 0, 0.49977, 0.01068, 0.46226, 0.13588, 0.42838, 0.20232, 0.55325, 0.22773, 0.42257, 0.2805, 0.55422, 0.29907, 0.44096, 0.3919, 0.64424, 0.39678, 0.79815, 0.39678, 0.43806, 0.47691, 0.63843, 0.49743, 0.77104, 0.51111, 0.42644, 0.54434, 0.60552, 0.57756, 0.72845, 0.58733, 0.40805, 0.60786, 0.55809, 0.64206, 0.6907, 0.66551, 0.38873, 0.6827, 0.51554, 0.69931, 0.66558, 0.73645, 0.17287, 0.71299, 0.41487, 0.7726, 0.62105, 0.82146, 0.1448, 0.7967, 0.38873, 0.85338, 0.59491, 0.92178, 0.12157, 0.87683, 0.38389, 0.93351], "triangles": [20, 21, 52, 19, 20, 52, 52, 49, 53, 55, 19, 52, 54, 50, 51, 53, 50, 54, 18, 19, 55, 56, 52, 53, 55, 52, 56, 58, 18, 55, 54, 51, 11, 17, 18, 58, 57, 53, 54, 57, 54, 11, 56, 53, 57, 56, 58, 55, 13, 59, 56, 59, 58, 56, 16, 58, 59, 17, 58, 16, 12, 57, 11, 57, 13, 56, 13, 57, 12, 15, 16, 59, 14, 15, 59, 13, 14, 59, 43, 40, 44, 46, 23, 43, 22, 23, 46, 47, 43, 44, 46, 43, 47, 49, 22, 46, 21, 22, 49, 50, 46, 47, 49, 46, 50, 52, 21, 49, 50, 47, 51, 53, 49, 50, 25, 26, 35, 40, 25, 37, 24, 25, 40, 43, 24, 40, 23, 24, 43, 36, 1, 2, 39, 2, 3, 39, 3, 4, 6, 39, 4, 6, 4, 5, 38, 36, 2, 38, 2, 39, 37, 36, 38, 39, 6, 7, 38, 40, 37, 41, 40, 38, 42, 38, 39, 42, 39, 7, 41, 38, 42, 44, 40, 41, 8, 42, 7, 45, 41, 42, 44, 41, 45, 45, 42, 8, 9, 45, 8, 48, 44, 45, 48, 45, 9, 47, 44, 48, 51, 47, 48, 10, 48, 9, 51, 48, 10, 11, 51, 10, 32, 30, 31, 32, 31, 0, 28, 29, 30, 32, 28, 30, 33, 28, 32, 27, 28, 33, 34, 32, 0, 33, 32, 34, 34, 0, 1, 35, 27, 33, 35, 33, 34, 26, 27, 35, 36, 34, 1, 35, 34, 36, 37, 35, 36, 37, 25, 35], "vertices": [2, 12, 47.56, 53.44, 0.95702, 13, 36.89, 144.96, 0.04298, 2, 12, 102.76, 87.73, 0.57031, 13, 76.51, 93.45, 0.42969, 2, 12, 150.06, 118.77, 0.08421, 13, 112.11, 49.48, 0.91579, 1, 13, 178.78, 54.14, 1, 1, 13, 208.11, 56.19, 1, 1, 13, 259.58, 59.78, 1, 1, 13, 283.77, 34.95, 1, 3, 13, 288.3, 0.37, 0.99955, 15, -73.87, 321.63, 0.00015, 16, -364.77, 8.38, 0.0003, 3, 13, 253.88, -66.49, 0.93515, 15, -9.09, 283.43, 0.03039, 16, -299.15, 45.11, 0.03446, 3, 13, 229.55, -113.78, 0.78541, 15, 36.72, 256.41, 0.09606, 16, -252.74, 71.08, 0.11853, 3, 13, 199.71, -171.74, 0.54072, 15, 92.87, 223.29, 0.16517, 16, -195.85, 102.92, 0.29412, 3, 13, 164.41, -240.33, 0.28747, 15, 159.31, 184.1, 0.13875, 16, -128.54, 140.59, 0.57378, 3, 13, 118.17, -300.73, 0.13989, 15, 216.95, 134.47, 0.05735, 16, -56.63, 165.39, 0.80276, 3, 13, 53.6, -313.19, 0.05886, 15, 225.67, 69.29, 0.01073, 16, 4.08, 140.11, 0.93041, 2, 13, -1.79, -323.88, 0.01126, 16, 56.15, 118.43, 0.98874, 2, 13, -11.38, -325.14, 0.00769, 16, 64.85, 114.17, 0.99231, 1, 16, 150.73, 43.99, 1, 3, 14, 271.86, -199.11, 0.00121, 15, 203.12, -189.89, 0.00128, 16, 216.64, -9.88, 0.99751, 3, 14, 235.85, -198.4, 0.00798, 15, 167.2, -192.49, 0.01219, 16, 200.8, -42.23, 0.97983, 3, 14, 182.92, -197.35, 0.03845, 15, 114.4, -196.32, 0.05981, 16, 177.52, -89.78, 0.90175, 3, 14, 122.58, -196.15, 0.07125, 15, 54.21, -200.68, 0.10574, 16, 150.98, -143.97, 0.82301, 3, 14, 114.36, -117.76, 0.23187, 15, 38.8, -123.38, 0.25082, 16, 76.43, -118.36, 0.51731, 3, 14, 90.33, -104.66, 0.44171, 15, 13.66, -112.55, 0.28642, 16, 54.42, -134.62, 0.27188, 3, 14, 56.14, -86.03, 0.75966, 15, -22.09, -97.14, 0.15946, 16, 23.11, -157.76, 0.08088, 3, 14, 25.21, -73.73, 0.95093, 15, -54.03, -87.75, 0.0326, 16, -1.09, -180.62, 0.01647, 3, 12, 171.05, -104.33, 0.05159, 14, -21.86, -55.02, 0.94839, 16, -37.92, -215.4, 2e-05, 2, 12, 124.93, -88.61, 0.46277, 14, -67.15, -37.02, 0.53723, 2, 12, 81.2, -73.71, 0.82629, 14, -110.08, -19.95, 0.17371, 2, 12, 45.61, -61.58, 0.95958, 14, -145.02, -6.06, 0.04042, 1, 12, -15.77, -40.66, 1, 1, 12, -16.3, -23.09, 1, 1, 12, -11.88, 16.51, 1, 2, 12, 54.55, -1.4, 0.99966, 14, -133.09, 53.59, 0.00034, 2, 12, 90.03, -18.33, 0.96571, 14, -98.5, 34.92, 0.03429, 2, 12, 101.38, 48.35, 0.79079, 13, 37.2, 90.9, 0.20921, 2, 12, 131.22, -20.16, 0.88865, 14, -57.44, 31.03, 0.11135, 2, 12, 138.87, 50, 0.50321, 13, 42.58, 53.76, 0.49679, 3, 12, 189.5, -8.63, 0.88687, 14, 1.33, 39.65, 0.112, 15, -88.24, 22.95, 0.00113, 2, 12, 188.8, 99.34, 0.00963, 13, 96.65, 9, 0.99037, 1, 13, 177.68, 19.61, 1, 4, 12, 234.24, -8.81, 0.04866, 13, -6.44, -47, 0.36084, 14, 46.01, 37.23, 0.41112, 15, -43.53, 24.66, 0.17938, 3, 13, 100.46, -43.89, 0.86516, 15, -40.48, 131.56, 0.1119, 16, -183.74, -58.48, 0.02294, 3, 13, 171.22, -41.89, 0.93161, 15, -38.41, 202.31, 0.04045, 16, -243.83, -21.07, 0.02795, 5, 12, 269.88, -13.91, 0.00026, 13, -7.95, -82.97, 0.14697, 14, 81.35, 30.37, 0.13279, 15, -7.71, 21.08, 0.71947, 16, -71.79, -85.8, 0.00051, 3, 13, 88.6, -87.95, 0.61647, 15, 2.82, 117.18, 0.30312, 16, -149.52, -28.31, 0.08041, 3, 13, 153.99, -84.58, 0.76876, 15, 3.22, 182.66, 0.13482, 16, -205.89, 5, 0.09641, 3, 13, -13.3, -117.36, 0.03332, 15, 26.32, 13.76, 0.96554, 16, -48.33, -60.08, 0.00114, 3, 13, 68.03, -124.86, 0.37772, 15, 38.49, 94.52, 0.45946, 16, -111.98, -8.91, 0.16282, 3, 13, 139.45, -127.95, 0.57277, 15, 45.68, 165.65, 0.21781, 16, -169.81, 33.13, 0.20941, 3, 13, -18.36, -157.73, 0.00582, 15, 66.33, 6.38, 0.98465, 16, -21.81, -29.23, 0.00953, 3, 13, 49.54, -157.65, 0.20635, 15, 70.16, 74.17, 0.4803, 16, -78.45, 8.21, 0.31335, 3, 13, 131.07, -166.68, 0.42076, 15, 83.86, 155.05, 0.22565, 16, -141.43, 60.78, 0.35359, 3, 14, 167.41, -105.99, 0.08603, 15, 90.54, -106.78, 0.16809, 16, 88.15, -65.3, 0.74588, 3, 13, 1.54, -202.82, 0.02404, 15, 112.49, 23.66, 0.08245, 16, -13.49, 19.34, 0.89351, 3, 13, 113.43, -214.09, 0.25654, 15, 130.18, 134.71, 0.16586, 16, -100.54, 90.55, 0.57759, 3, 14, 211.14, -121.76, 0.02116, 15, 135.53, -118.45, 0.04009, 16, 120.89, -32.3, 0.93875, 3, 13, -6.71, -246.75, 0.01318, 15, 155.87, 12.9, 0.00027, 16, 17.65, 51.41, 0.98656, 3, 13, 106.52, -268.21, 0.15534, 15, 183.82, 124.7, 0.07379, 16, -64.87, 131.85, 0.77086, 3, 14, 253.03, -134.92, 0.00271, 15, 178.46, -127.7, 0.00404, 16, 150.5, 0.13, 0.99325, 2, 13, -3.79, -288.87, 0.01267, 16, 38.48, 88.14, 0.98733], "hull": 32, "edges": [2, 4, 10, 12, 12, 14, 22, 24, 28, 30, 40, 42, 58, 60, 60, 62, 56, 58, 56, 64, 2, 0, 0, 62, 64, 0, 54, 56, 54, 66, 66, 68, 68, 2, 52, 54, 52, 70, 70, 72, 72, 4, 50, 52, 50, 74, 74, 76, 76, 78, 78, 12, 4, 6, 6, 8, 8, 10, 46, 48, 48, 50, 48, 80, 80, 82, 82, 84, 84, 14, 46, 86, 86, 88, 88, 90, 14, 16, 90, 16, 42, 44, 44, 46, 44, 92, 92, 94, 94, 96, 16, 18, 96, 18, 42, 98, 98, 100, 100, 102, 18, 20, 20, 22, 102, 20, 40, 104, 104, 106, 106, 108, 108, 22, 38, 40, 38, 110, 110, 112, 112, 114, 114, 24, 34, 36, 36, 38, 36, 116, 116, 118, 24, 26, 26, 28, 118, 26, 30, 32, 32, 34], "width": 531, "height": 526}}, "s2": {"s2": {"type": "mesh", "uvs": [0.27583, 0, 0.3794, 0.0548, 0.4702, 0.10285, 0.59087, 0.1667, 0.71753, 0.23372, 0.84897, 0.30326, 0.94668, 0.35497, 0.96543, 0.58198, 0.98284, 0.79286, 0.99607, 0.95303, 0.98281, 0.99783, 0.91584, 0.9634, 0.83191, 0.92024, 0.72817, 0.86691, 0.54263, 0.77151, 0.41155, 0.70411, 0.28375, 0.59784, 0.17012, 0.50337, 0.07673, 0.42571, 0, 0.36191, 0, 0.31348, 0.23421, 0, 0.30675, 0.16088, 0.21689, 0.27402, 0.39936, 0.2131, 0.28739, 0.34191, 0.5141, 0.29143, 0.3731, 0.45854, 0.62192, 0.35758, 0.44222, 0.5508, 0.76431, 0.47073, 0.82651, 0.57517, 0.88595, 0.74925, 0.94125, 0.86239], "triangles": [10, 11, 9, 11, 33, 9, 11, 12, 33, 33, 8, 9, 12, 32, 33, 32, 13, 31, 33, 32, 8, 32, 7, 8, 32, 31, 7, 12, 13, 32, 14, 30, 31, 31, 6, 7, 6, 30, 5, 6, 31, 30, 28, 4, 30, 30, 4, 5, 15, 29, 14, 30, 29, 28, 30, 14, 29, 15, 16, 29, 16, 27, 29, 27, 16, 25, 29, 26, 28, 29, 27, 26, 16, 17, 25, 17, 23, 25, 17, 18, 23, 27, 24, 26, 27, 25, 24, 23, 18, 20, 18, 19, 20, 26, 3, 28, 28, 3, 4, 25, 22, 24, 25, 23, 22, 23, 20, 21, 24, 2, 26, 26, 2, 3, 23, 21, 22, 21, 0, 22, 22, 1, 24, 24, 1, 2, 22, 0, 1, 13, 14, 31], "vertices": [2, 27, 6.99, 27.54, 0.99997, 29, -91.85, -41.63, 3e-05, 2, 27, 22.02, 24.81, 0.99995, 29, -80.45, -31.46, 5e-05, 3, 27, 35.2, 22.41, 0.99475, 28, -36.11, -12.33, 0.0052, 29, -70.45, -22.55, 5e-05, 3, 27, 52.71, 19.22, 0.79287, 28, -21.12, -2.74, 0.20713, 29, -57.16, -10.71, 1e-05, 2, 27, 71.1, 15.88, 0.04663, 28, -5.38, 7.34, 0.95337, 1, 28, 10.95, 17.79, 1, 2, 28, 23.09, 25.56, 0.98917, 29, -17.97, 24.22, 0.01083, 2, 28, 44.72, 13.73, 0.26964, 29, 5.25, 15.97, 0.73036, 1, 29, 26.83, 8.3, 1, 1, 29, 43.22, 2.48, 1, 1, 29, 46.81, -1.23, 1, 2, 27, 114.46, -55.24, 0.10248, 29, 39.53, -7.85, 0.89752, 2, 27, 102.31, -53.16, 0.33111, 29, 30.41, -16.15, 0.66889, 3, 27, 87.29, -50.58, 0.58683, 28, 51.74, -30.3, 0.00121, 29, 19.14, -26.4, 0.41196, 1, 27, 60.42, -45.97, 1, 1, 27, 41.44, -42.72, 1, 1, 27, 21.99, -35.27, 1, 2, 27, 4.7, -28.64, 0.99689, 29, -48.97, -78.01, 0.00311, 2, 27, -9.51, -23.2, 0.99999, 29, -62.01, -85.86, 1e-05, 1, 27, -21.19, -18.73, 1, 1, 27, -22.32, -13.62, 1, 2, 27, 1.46, 26.32, 0.99998, 29, -94.29, -46.74, 2e-05, 2, 27, 14.85, 11.49, 0.99998, 29, -74.36, -45.31, 2e-05, 2, 27, 5.56, -3.09, 1, 29, -68.59, -61.6, 0, 2, 27, 28.37, 8.7, 0.99995, 29, -63.85, -36.37, 5e-05, 2, 27, 16.51, -8.17, 0.99875, 29, -57.84, -56.1, 0.00125, 1, 27, 45.43, 3.81, 1, 1, 27, 30.61, -17.95, 1, 3, 27, 61.29, 0.01, 0.99074, 28, -1.75, -10.96, 0.00919, 29, -36.73, -15.77, 8e-05, 1, 27, 41.94, -25.65, 1, 3, 27, 82.84, -7.74, 0.01387, 28, 19.3, -1.96, 0.97894, 29, -17.37, -3.55, 0.00719, 3, 27, 93.54, -16.93, 0.00172, 28, 33.39, -1.4, 0.65286, 29, -3.54, -0.77, 0.34541, 2, 27, 105.49, -33.54, 0.00132, 29, 16.91, -1.57, 0.99868, 2, 27, 115.48, -43.84, 1e-05, 29, 31.17, -0.04, 0.99999], "hull": 22, "edges": [0, 42, 18, 20, 38, 40, 40, 42, 0, 2, 2, 44, 44, 46, 36, 38, 46, 36, 2, 4, 4, 48, 48, 50, 34, 36, 50, 34, 4, 6, 6, 52, 52, 54, 30, 32, 32, 34, 54, 32, 6, 8, 8, 56, 56, 58, 58, 30, 8, 10, 10, 12, 10, 60, 28, 30, 60, 28, 12, 62, 26, 28, 62, 26, 12, 14, 14, 64, 24, 26, 64, 24, 14, 16, 16, 18, 16, 66, 20, 22, 22, 24, 66, 22], "width": 136, "height": 108}}, "s3": {"s3": {"type": "mesh", "uvs": [0.71078, 0.16585, 0.77129, 0.31445, 0.83217, 0.46399, 0.89596, 0.62065, 0.95313, 0.76104, 1, 0.87616, 1, 0.98012, 0.67722, 0.99663, 0.57662, 0.86887, 0.46842, 0.73144, 0.33991, 0.56823, 0.2101, 0.40337, 0.10977, 0.27593, 0, 0.13652, 0, 0.01777, 0.65012, 0.01687], "triangles": [6, 7, 5, 7, 8, 5, 8, 4, 5, 8, 9, 4, 9, 3, 4, 9, 10, 3, 10, 2, 3, 10, 11, 2, 11, 1, 2, 11, 12, 1, 12, 0, 1, 12, 13, 0, 13, 15, 0, 13, 14, 15], "vertices": [2, 34, 7.07, 9.23, 0.99145, 35, -11.96, 8.33, 0.00855, 2, 34, 15.26, 7.84, 0.6199, 35, -3.71, 7.36, 0.3801, 2, 34, 23.51, 6.43, 0.01521, 35, 4.6, 6.38, 0.98479, 1, 35, 13.3, 5.36, 1, 1, 35, 21.1, 4.44, 1, 1, 35, 27.49, 3.69, 1, 1, 35, 32.96, 2, 1, 1, 35, 31.44, -5.98, 1, 1, 35, 23.98, -6.3, 1, 1, 35, 15.96, -6.65, 1, 2, 34, 24.66, -7.09, 0.01292, 35, 6.44, -7.07, 0.98708, 2, 34, 15.03, -7.02, 0.68986, 35, -3.19, -7.49, 0.31014, 2, 34, 7.58, -6.96, 0.99916, 35, -10.62, -7.81, 0.00084, 1, 34, -0.56, -6.9, 1, 1, 34, -6.69, -4.65, 1, 1, 34, -1.14, 10.63, 1], "hull": 16, "edges": [10, 12, 12, 14, 26, 28, 28, 30, 0, 30, 24, 26, 0, 24, 0, 2, 22, 24, 2, 22, 2, 4, 20, 22, 4, 20, 4, 6, 18, 20, 6, 18, 6, 8, 8, 10, 14, 16, 16, 18, 8, 16], "width": 25, "height": 55}}, "st": {"st": {"type": "mesh", "uvs": [0.53057, 0.13791, 0.57795, 0.16286, 0.5934, 0.19894, 0.60995, 0.23762, 0.63341, 0.29243, 0.64759, 0.32554, 0.66408, 0.36408, 0.66948, 0.37668, 0.65453, 0.40565, 0.7197, 0.43264, 0.79401, 0.46343, 0.85928, 0.49047, 0.92162, 0.51629, 0.92154, 0.57297, 0.92147, 0.61668, 0.92149, 0.64732, 0.92131, 0.65242, 0.92171, 0.75709, 0.92202, 0.83825, 0.92216, 0.87631, 0.92226, 0.90226, 0.87267, 0.90229, 0.76198, 0.90236, 0.75479, 0.90236, 0.66129, 0.90242, 0.60391, 0.90246, 0.59353, 0.86656, 0.57717, 0.81001, 0.53131, 0.78391, 0.53484, 0.78159, 0.53674, 0.73665, 0.51226, 0.6859, 0.48938, 0.63849, 0.46799, 0.59414, 0.44148, 0.53919, 0.44974, 0.48996, 0.45767, 0.44271, 0.46204, 0.41668, 0.44839, 0.39434, 0.43112, 0.36608, 0.40282, 0.31976, 0.38882, 0.29684, 0.30873, 0.27052, 0.29896, 0.22271, 0.29306, 0.19384, 0.28698, 0.16407, 0.30574, 0.14529, 0.32868, 0.12232, 0.44035, 0.09841, 0.45116, 0.09609, 0.43663, 0.12304, 0.42726, 0.15149, 0.41014, 0.17358, 0.50064, 0.15149, 0.48515, 0.16488, 0.50227, 0.17994, 0.37508, 0.13241, 0.36896, 0.15149, 0.3445, 0.16957, 0.35143, 0.19333, 0.43786, 0.20404, 0.51124, 0.23082, 0.55567, 0.25994, 0.5357, 0.20772, 0.47577, 0.19266, 0.54059, 0.17191, 0.56097, 0.20471, 0.58829, 0.24689, 0.36692, 0.22747, 0.44153, 0.25258, 0.50105, 0.275, 0.53692, 0.30948, 0.34491, 0.25994, 0.43052, 0.28672, 0.48066, 0.3068, 0.49819, 0.32923, 0.43304, 0.3282, 0.46723, 0.35226, 0.57201, 0.29968, 0.59958, 0.30034, 0.49049, 0.37098, 0.54963, 0.33752, 0.60198, 0.32735, 0.51943, 0.39243, 0.59256, 0.37144, 0.5478, 0.42065, 0.62253, 0.4026, 0.5506, 0.46166, 0.6445, 0.44591, 0.53981, 0.51898, 0.69445, 0.49371, 0.56419, 0.56819, 0.72842, 0.53308, 0.82712, 0.51668, 0.57857, 0.61773, 0.78356, 0.57705, 0.86508, 0.5508, 0.58856, 0.66122, 0.80714, 0.62645, 0.87068, 0.60479, 0.59935, 0.71437, 0.81154, 0.68255, 0.88506, 0.66057, 0.61973, 0.77119, 0.70484, 0.75971, 0.79595, 0.75873, 0.70425, 0.69864, 0.68595, 0.64573, 0.63771, 0.82841, 0.72722, 0.83596, 0.78197, 0.8399, 0.62852, 0.87008, 0.74201, 0.87631, 0.77637, 0.87631], "triangles": [25, 111, 24, 25, 26, 111, 24, 112, 23, 24, 111, 112, 112, 111, 109, 111, 108, 109, 111, 26, 108, 26, 27, 108, 108, 104, 109, 109, 104, 105, 27, 103, 108, 108, 103, 104, 28, 29, 27, 27, 29, 103, 29, 30, 103, 30, 100, 103, 103, 100, 104, 100, 106, 104, 104, 106, 105, 30, 31, 100, 31, 97, 100, 100, 107, 106, 100, 97, 107, 106, 107, 101, 31, 32, 97, 107, 98, 101, 32, 94, 97, 97, 94, 107, 107, 95, 98, 107, 94, 95, 94, 32, 91, 94, 92, 95, 32, 33, 91, 94, 91, 92, 91, 90, 92, 23, 112, 22, 22, 113, 21, 22, 112, 113, 21, 19, 20, 21, 113, 19, 113, 110, 19, 110, 18, 19, 113, 112, 110, 110, 112, 109, 18, 110, 105, 110, 109, 105, 105, 17, 18, 105, 101, 17, 105, 106, 101, 101, 102, 17, 101, 98, 102, 102, 16, 17, 98, 99, 102, 16, 102, 15, 14, 102, 99, 102, 14, 15, 98, 95, 99, 14, 99, 13, 95, 96, 99, 99, 96, 13, 95, 93, 96, 95, 92, 93, 13, 96, 12, 93, 11, 96, 96, 11, 12, 92, 10, 93, 92, 90, 10, 93, 10, 11, 42, 72, 41, 41, 72, 73, 72, 68, 73, 73, 69, 70, 73, 68, 69, 70, 69, 61, 42, 43, 72, 72, 43, 68, 68, 60, 69, 69, 60, 61, 60, 64, 61, 64, 55, 61, 61, 55, 63, 43, 59, 68, 68, 59, 60, 43, 44, 59, 63, 55, 65, 66, 65, 1, 59, 52, 60, 64, 60, 51, 44, 58, 59, 44, 45, 58, 59, 58, 52, 60, 52, 51, 64, 51, 54, 64, 54, 55, 54, 51, 50, 54, 53, 55, 55, 53, 65, 58, 57, 52, 53, 54, 50, 50, 48, 49, 52, 57, 51, 53, 0, 65, 65, 0, 1, 45, 46, 58, 58, 46, 57, 53, 50, 49, 53, 49, 0, 46, 47, 57, 57, 56, 51, 57, 47, 56, 51, 56, 50, 50, 56, 48, 48, 56, 47, 82, 79, 4, 71, 62, 78, 71, 70, 62, 4, 79, 67, 79, 78, 67, 78, 62, 67, 67, 3, 4, 70, 61, 62, 61, 63, 62, 62, 66, 67, 62, 63, 66, 67, 2, 3, 67, 66, 2, 63, 65, 66, 66, 1, 2, 33, 89, 91, 33, 34, 89, 91, 89, 90, 34, 35, 89, 89, 88, 90, 89, 87, 88, 89, 35, 87, 90, 9, 10, 90, 88, 9, 35, 36, 87, 36, 85, 87, 88, 85, 86, 88, 87, 85, 88, 8, 9, 88, 86, 8, 85, 37, 83, 85, 36, 37, 85, 84, 86, 85, 83, 84, 37, 80, 83, 37, 38, 80, 8, 86, 7, 86, 6, 7, 86, 84, 6, 38, 77, 80, 38, 39, 77, 83, 81, 84, 83, 80, 81, 6, 82, 5, 6, 84, 82, 84, 81, 82, 80, 75, 81, 80, 77, 75, 39, 76, 77, 39, 40, 76, 77, 74, 75, 77, 76, 74, 82, 78, 79, 82, 81, 78, 75, 71, 81, 81, 71, 78, 75, 74, 71, 40, 73, 76, 76, 73, 74, 82, 4, 5, 40, 41, 73, 74, 70, 71, 74, 73, 70], "vertices": [2, 4, 171.34, -7.1, 0.21941, 5, 63.06, -64.38, 0.78059, 2, 4, 126.78, -40.65, 0.47554, 5, 52.93, -119.24, 0.52446, 2, 4, 80.63, -39.86, 0.67237, 5, 18.4, -149.87, 0.32763, 2, 4, 31.16, -39.01, 0.90592, 5, -18.61, -182.71, 0.09408, 2, 3, 151.56, -92.26, 0.28953, 4, -38.96, -37.8, 0.71047, 2, 3, 109.21, -91.53, 0.66787, 4, -81.31, -37.08, 0.33213, 3, 3, 59.9, -90.69, 0.89846, 4, -130.62, -36.23, 0.09953, 6, -249.41, -168.25, 0.00202, 4, 3, 43.79, -90.41, 0.92437, 4, -146.73, -35.95, 0.07079, 6, -234.02, -163.47, 0.00444, 9, -247.39, -160.58, 0.0004, 4, 3, 16.16, -64.37, 0.93292, 4, -174.36, -9.91, 0.01589, 6, -199.63, -179.56, 0.03138, 9, -209.44, -161.63, 0.01982, 3, 3, -36.85, -113.59, 0.61249, 6, -164.68, -116.23, 0.22881, 9, -202.63, -89.61, 0.15871, 3, 3, -97.29, -169.72, 0.25624, 6, -124.82, -44.01, 0.57571, 9, -194.86, -7.5, 0.16805, 3, 3, -150.38, -219.01, 0.06046, 6, -89.82, 19.41, 0.89896, 9, -188.04, 64.63, 0.04058, 2, 3, -201.09, -266.1, 0.00348, 6, -56.38, 80, 0.99652, 1, 6, 11.98, 77.38, 1, 2, 6, 64.7, 75.37, 0.99659, 7, -54.65, 78.98, 0.00341, 2, 6, 101.66, 74.02, 0.9102, 7, -17.71, 80.79, 0.0898, 3, 6, 107.8, 73.6, 0.84929, 7, -11.55, 80.9, 0.15047, 8, -152.71, 85.18, 0.00024, 3, 6, 234.07, 69.33, 0.02613, 7, 114.62, 87.4, 0.52331, 8, -26.37, 85.58, 0.45057, 2, 7, 212.45, 92.44, 0.0092, 8, 71.59, 85.89, 0.9908, 1, 8, 117.53, 86.03, 1, 1, 8, 148.85, 86.13, 1, 1, 8, 148.88, 36.99, 1, 2, 8, 148.97, -72.71, 0.64863, 11, 126.62, 125.69, 0.35137, 2, 8, 148.97, -79.83, 0.6016, 11, 127.9, 118.68, 0.3984, 2, 8, 149.04, -172.5, 0.08249, 11, 144.54, 27.52, 0.91751, 2, 8, 149.09, -229.35, 0.00059, 11, 154.76, -28.41, 0.99941, 2, 10, 218.34, -24.46, 0.00226, 11, 113.97, -46.29, 0.99774, 4, 3, -414.79, 177.23, 0.00309, 9, 273, -55.17, 0.01897, 10, 159.51, -62.69, 0.23577, 11, 49.71, -74.45, 0.74216, 4, 3, -369.45, 208.88, 0.01333, 9, 260.23, -108.98, 0.0721, 10, 145.13, -116.08, 0.49359, 11, 26.85, -124.8, 0.42098, 4, 3, -368.04, 204.63, 0.01333, 9, 256.34, -106.74, 0.07209, 10, 141.31, -113.73, 0.49309, 11, 23.46, -121.86, 0.42148, 4, 3, -317.84, 184, 0.04699, 9, 205.15, -124.75, 0.20611, 10, 89.6, -130.19, 0.5863, 11, -30.24, -129.7, 0.1606, 4, 3, -251.97, 185.45, 0.14525, 9, 156.94, -169.67, 0.41839, 10, 40.07, -173.65, 0.42068, 11, -86.17, -164.54, 0.01567, 4, 3, -190.43, 186.8, 0.28124, 9, 111.91, -211.63, 0.47636, 10, -6.2, -214.24, 0.24229, 11, -138.42, -197.07, 0.00012, 3, 3, -132.88, 188.07, 0.43977, 9, 69.79, -250.88, 0.42469, 10, -49.47, -252.21, 0.13553, 3, 3, -61.55, 189.64, 0.6235, 9, 17.6, -299.51, 0.30795, 10, -103.1, -299.26, 0.06854, 3, 3, -8.69, 161.3, 0.76701, 9, -40.72, -313.54, 0.19659, 10, -161.81, -311.54, 0.0364, 4, 3, 42.05, 134.1, 0.9038, 5, -304.83, -136.62, 0.00238, 9, -96.7, -327, 0.08038, 10, -218.17, -323.32, 0.01344, 4, 3, 70, 119.12, 0.94911, 5, -274.12, -128.75, 0.0146, 9, -127.52, -334.42, 0.03106, 10, -249.2, -329.81, 0.00522, 4, 3, 99.99, 122.43, 0.94411, 5, -254.27, -106.02, 0.04631, 9, -147.71, -356.85, 0.00811, 10, -270.05, -351.62, 0.00147, 5, 3, 137.92, 126.61, 0.87472, 4, -52.6, 181.07, 0.00361, 5, -229.16, -77.28, 0.12087, 9, -173.24, -385.21, 0.00064, 10, -296.42, -379.21, 0.00015, 3, 3, 200.1, 133.47, 0.60568, 4, 9.57, 187.93, 0.03571, 5, -188.01, -30.17, 0.35861, 3, 3, 230.86, 136.86, 0.41173, 4, 40.34, 191.32, 0.03857, 5, -167.65, -6.87, 0.5497, 3, 3, 288.24, 200.23, 0.16671, 4, 97.71, 254.69, 0.00169, 5, -168.25, 78.62, 0.8316, 2, 3, 345.71, 189.24, 0.09606, 5, -118.49, 109.4, 0.90394, 2, 3, 380.41, 182.61, 0.05273, 5, -88.44, 127.98, 0.94727, 2, 3, 416.2, 175.76, 0.02832, 5, -57.45, 147.15, 0.97168, 2, 3, 430.99, 150.45, 0.01758, 5, -29.44, 138.51, 0.98242, 2, 3, 449.08, 119.49, 0.00624, 5, 4.82, 127.95, 0.99376, 1, 5, 73.39, 36.42, 1, 1, 5, 80.03, 27.56, 1, 1, 5, 44.47, 28.58, 1, 2, 3, 382.1, 40.14, 0.00066, 5, 9.17, 24.2, 0.99934, 3, 3, 363, 65.32, 0.02073, 4, 172.47, 119.78, 0.00205, 5, -21.93, 29.82, 0.97722, 3, 3, 356.81, -28.04, 0.00066, 4, 166.29, 26.41, 0.13811, 5, 36.66, -43.12, 0.86123, 3, 3, 347, -8.03, 0.00383, 4, 156.48, 46.43, 0.11731, 5, 15.9, -35.02, 0.87886, 3, 3, 324.05, -17.62, 0.01284, 4, 133.53, 36.84, 0.24549, 5, 5.49, -57.6, 0.74168, 2, 3, 421.67, 80.62, 0.00234, 5, 10.93, 80.78, 0.99766, 2, 3, 402.19, 94.31, 0.01079, 5, -12.68, 77.68, 0.98921, 2, 3, 390.16, 124.62, 0.02823, 5, -42.04, 91.87, 0.97177, 3, 3, 360.88, 128.15, 0.05888, 4, 170.35, 182.61, 0.00078, 5, -66, 74.67, 0.94035, 3, 3, 318.98, 52.35, 0.07905, 4, 128.45, 106.8, 0.07482, 5, -45.58, -9.51, 0.84614, 3, 3, 263.39, -4.6, 0.10858, 4, 72.86, 49.86, 0.45973, 5, -48, -89.05, 0.43169, 3, 3, 215.12, -33.66, 0.07929, 4, 24.6, 20.79, 0.80609, 5, -63.89, -143.11, 0.11462, 3, 3, 281.1, -37.02, 0.02207, 4, 90.57, 17.44, 0.52491, 5, -13.03, -100.95, 0.45302, 3, 3, 318.79, 12.34, 0.03833, 4, 128.27, 66.8, 0.16892, 5, -18.66, -39.1, 0.79274, 3, 3, 319.94, -56.59, 0.0016, 4, 129.42, -2.14, 0.38884, 5, 28.82, -89.09, 0.60956, 3, 3, 275.79, -61.77, 0.00369, 4, 85.27, -7.31, 0.62066, 5, -0.19, -122.76, 0.37565, 2, 4, 28.13, -14.99, 0.91427, 5, -37.08, -167.07, 0.08573, 3, 3, 316.9, 128.09, 0.13544, 4, 126.38, 182.55, 0.01186, 5, -98.34, 44.87, 0.85269, 3, 3, 262.79, 69.31, 0.28269, 4, 72.26, 123.76, 0.13497, 5, -98.44, -35.03, 0.58234, 3, 3, 216.9, 23.42, 0.40591, 4, 26.37, 77.87, 0.33806, 5, -101.19, -99.86, 0.25603, 3, 3, 165.52, 4.55, 0.75317, 4, -25, 59.01, 0.20219, 5, -126.27, -148.51, 0.04464, 3, 3, 287.75, 162.17, 0.18814, 4, 97.22, 216.63, 0.00777, 5, -142.87, 50.25, 0.80409, 3, 3, 227.94, 93.86, 0.47018, 4, 37.42, 148.32, 0.08133, 5, -140.71, -40.51, 0.4485, 3, 3, 187.94, 55.7, 0.68237, 4, -2.59, 110.16, 0.1148, 5, -144.36, -95.68, 0.20283, 3, 3, 156.52, 48.82, 0.86544, 4, -34.01, 103.28, 0.04063, 5, -162.85, -121.99, 0.09393, 3, 3, 180.13, 108.93, 0.72162, 4, -10.4, 163.39, 0.02973, 5, -186.12, -61.75, 0.24866, 5, 3, 141.12, 87.27, 0.88734, 4, -49.4, 141.72, 0.00755, 5, -200.19, -104.1, 0.1048, 9, -201.81, -357.97, 0.00024, 10, -324.16, -351.12, 8e-05, 3, 3, 164.52, -32.16, 0.45123, 4, -26.01, 22.29, 0.54412, 5, -102.18, -176.23, 0.00466, 2, 3, 154.27, -57.51, 0.35603, 4, -36.25, -3.05, 0.64397, 4, 3, 111.92, 73.51, 0.95528, 5, -212.39, -133.99, 0.04226, 9, -189.17, -328.27, 0.00201, 10, -310.64, -321.81, 0.00045, 2, 3, 129.41, 4.51, 0.9985, 5, -152.84, -172.97, 0.0015, 2, 3, 122.88, -48.4, 0.7062, 4, -67.64, 6.06, 0.2938, 4, 3, 77.67, 55.62, 0.98442, 5, -225.52, -170.33, 0.00851, 9, -175.51, -292.12, 0.0059, 10, -295.9, -286.09, 0.00117, 2, 3, 76.23, -21.14, 0.97492, 4, -114.29, 33.32, 0.02508, 4, 3, 35.96, 41.1, 0.9826, 5, -246.41, -209.23, 0.00019, 9, -154.04, -253.53, 0.01476, 10, -273.28, -248.16, 0.00245, 4, 3, 30.63, -35.91, 0.97674, 4, -159.89, 18.55, 0.0129, 6, -204.48, -211.12, 0.00847, 9, -201.3, -192.51, 0.0019, 3, 3, -11.41, 55.71, 0.89753, 9, -108.95, -232.91, 0.0916, 10, -227.6, -228.9, 0.01087, 5, 3, -25.95, -38.15, 0.86104, 4, -216.47, 16.31, 0.00014, 6, -151.44, -191.29, 0.05911, 9, -160.56, -153.18, 0.07962, 10, -276.8, -147.65, 9e-05, 4, 3, -72.56, 89.79, 0.66047, 6, -67.15, -298.24, 0.00041, 9, -40.63, -217.66, 0.29559, 10, -158.85, -215.7, 0.04353, 4, 3, -97.26, -64.5, 0.49424, 6, -91.94, -143.96, 0.19877, 9, -124.87, -86.05, 0.30688, 10, -239.11, -81.63, 0.00012, 4, 3, -136.65, 87.8, 0.43064, 6, -6.89, -276.3, 0.00053, 9, 5.89, -173.52, 0.4842, 10, -111.04, -172.97, 0.08464, 3, 3, -153.52, -79.53, 0.24122, 6, -43.21, -112.08, 0.29759, 9, -92.88, -37.39, 0.46119, 3, 3, -168.97, -178.13, 0.07722, 6, -59.38, -13.61, 0.84031, 9, -146.96, 46.48, 0.08248, 3, 3, -197.68, 95.22, 0.24103, 9, 56.37, -138.45, 0.58691, 10, -59.52, -139.44, 0.17206, 4, 3, -222.27, -112.32, 0.03402, 6, 11.84, -59.44, 0.56036, 7, -95.82, -59.84, 0.02082, 9, -63.38, 32.83, 0.3848, 2, 3, -220.66, -199.08, 0.00467, 6, -16.83, 22.46, 0.99533, 4, 3, -250.34, 104.2, 0.12532, 9, 101.65, -110.1, 0.53761, 10, -13.41, -112.46, 0.33562, 11, -129, -95.47, 0.00145, 4, 6, 72.29, -38.3, 0.49742, 7, -37.39, -33.62, 0.27417, 9, -16.37, 76.32, 0.228, 10, -125.79, 77.42, 0.00041, 1, 6, 48.5, 25.59, 1, 4, 3, -314.21, 116.48, 0.04201, 9, 157.5, -76.77, 0.25962, 10, 43.41, -80.81, 0.65577, 11, -67.79, -73.47, 0.0426, 3, 7, 30.03, -26, 0.88115, 9, 45.1, 105.05, 0.09374, 10, -63.49, 104.3, 0.02511, 3, 6, 116.3, 37.35, 0.72414, 7, 0.01, 45.5, 0.27563, 8, -142.88, 49.26, 0.00024, 4, 3, -385.53, 121.39, 0.00476, 9, 214.01, -32.97, 0.0287, 10, 101.2, -38.72, 0.50679, 11, -3.93, -41.33, 0.45975, 4, 7, 128.17, -127.11, 0.07588, 8, -23.21, -129.33, 0.06524, 10, 59.78, 36.05, 0.67699, 11, -32.65, 39.18, 0.18189, 5, 7, 122.62, -36.98, 0.57907, 8, -24.4, -39.05, 0.25134, 9, 136.36, 124.18, 0.00803, 10, 28.3, 120.68, 0.11708, 11, -49.97, 127.79, 0.04448, 5, 7, 54.57, -131.26, 0.04173, 8, -96.92, -129.92, 0.00074, 9, 101.93, 13.12, 0.40983, 10, -9.44, 10.7, 0.54723, 11, -105.07, 25.41, 0.00046, 3, 3, -266.36, 7.2, 0.02588, 9, 49.07, -27.04, 0.96641, 10, -63.48, -27.85, 0.00771, 3, 9, 271.83, 8.79, 6e-05, 10, 160.25, 1.29, 0.00536, 11, 60.83, -11.45, 0.99458, 4, 7, 219.02, -100.51, 0.008, 8, 68.82, -107.16, 0.37334, 10, 138.99, 87.89, 0.02277, 11, 53.93, 77.46, 0.59589, 4, 7, 221.14, -46.09, 0.00256, 8, 73.57, -52.91, 0.76574, 10, 125.22, 140.57, 0.01464, 11, 48.9, 131.68, 0.21706, 2, 8, 110.01, -204.97, 0.00385, 11, 111.94, -11.41, 0.99615, 3, 8, 117.53, -92.51, 0.51245, 10, 179.93, 118.07, 0.00052, 11, 99.23, 100.59, 0.48703, 3, 8, 117.53, -58.45, 0.74035, 10, 168.48, 150.14, 0.00062, 11, 93.14, 134.09, 0.25903], "hull": 50, "edges": [14, 16, 30, 32, 54, 56, 58, 60, 82, 84, 94, 96, 96, 98, 96, 100, 100, 102, 102, 104, 2, 0, 0, 98, 0, 106, 106, 108, 108, 110, 94, 112, 112, 100, 90, 92, 92, 94, 92, 114, 114, 102, 90, 116, 116, 104, 88, 90, 88, 118, 118, 120, 120, 122, 122, 124, 110, 126, 126, 124, 104, 120, 120, 128, 128, 110, 122, 126, 110, 130, 130, 2, 126, 132, 2, 4, 132, 4, 124, 134, 4, 6, 134, 6, 84, 86, 86, 88, 86, 136, 136, 138, 138, 140, 140, 142, 84, 144, 144, 146, 146, 148, 148, 150, 80, 82, 80, 152, 152, 154, 78, 80, 124, 156, 134, 158, 158, 156, 6, 8, 158, 8, 156, 142, 142, 150, 150, 154, 154, 78, 74, 76, 76, 78, 76, 160, 160, 162, 162, 164, 8, 10, 164, 10, 140, 124, 146, 140, 74, 166, 166, 168, 10, 12, 12, 14, 168, 12, 72, 74, 72, 170, 170, 172, 172, 16, 68, 70, 70, 72, 70, 174, 174, 176, 16, 18, 176, 18, 68, 178, 178, 180, 18, 20, 180, 20, 66, 68, 66, 182, 182, 184, 184, 186, 20, 22, 22, 24, 186, 22, 64, 66, 64, 188, 188, 190, 190, 192, 192, 24, 60, 62, 62, 64, 62, 194, 196, 198, 24, 26, 26, 28, 198, 26, 60, 200, 202, 204, 204, 30, 58, 206, 206, 208, 208, 210, 32, 34, 210, 34, 200, 212, 212, 202, 194, 214, 214, 196, 54, 216, 216, 218, 218, 220, 34, 36, 220, 36, 50, 52, 52, 54, 52, 222, 222, 224, 224, 226, 36, 38, 38, 40, 226, 38, 48, 50, 46, 48, 44, 46, 40, 42, 42, 44, 56, 58, 28, 30], "width": 991, "height": 1207}}, "s5": {"s5": {"type": "mesh", "uvs": [0.25134, 0, 0.37886, 0.10754, 0.5074, 0.21594, 0.69164, 0.37132, 0.76027, 0.49831, 0.81869, 0.60641, 0.876, 0.71245, 0.94422, 0.83867, 1, 0.94189, 1, 0.98141, 0.77479, 0.99849, 0.69922, 0.91497, 0.61149, 0.81799, 0.4854, 0.67862, 0.39141, 0.57473, 0.27413, 0.44509, 0.1891, 0.3511, 0.09779, 0.25017, 0, 0.14208, 0, 0.11645, 0.11325, 0], "triangles": [9, 10, 8, 10, 11, 8, 11, 7, 8, 11, 12, 7, 12, 6, 7, 12, 13, 6, 13, 5, 6, 13, 14, 5, 14, 4, 5, 14, 15, 4, 15, 3, 4, 15, 16, 3, 16, 2, 3, 2, 17, 1, 2, 16, 17, 0, 1, 19, 19, 20, 0, 18, 19, 1, 1, 17, 18], "vertices": [1, 30, -5.16, 7.14, 1, 1, 30, 5.4, 7.59, 1, 1, 30, 16.04, 8.05, 1, 2, 30, 31.29, 8.7, 0.87397, 31, -5.16, 10.04, 0.12603, 2, 30, 42.06, 6.15, 0.09983, 31, 5.86, 9.04, 0.90017, 1, 31, 15.24, 8.19, 1, 1, 31, 24.45, 7.35, 1, 1, 31, 35.4, 6.36, 1, 1, 31, 44.36, 5.55, 1, 1, 31, 47.47, 4.38, 1, 1, 31, 45.4, -5.19, 1, 1, 31, 37.69, -5.76, 1, 1, 31, 28.73, -6.42, 1, 1, 31, 15.87, -7.36, 1, 2, 30, 40.04, -10.84, 0.00147, 31, 6.28, -8.07, 0.99853, 2, 30, 28.07, -10.02, 0.63083, 31, -5.69, -8.95, 0.36917, 2, 30, 19.39, -9.41, 0.97316, 31, -14.37, -9.58, 0.02684, 1, 30, 10.07, -8.77, 1, 1, 30, 0.09, -8.08, 1, 1, 30, -1.8, -7.04, 1, 1, 30, -8.02, 1.94, 1], "hull": 21, "edges": [0, 40, 16, 18, 18, 20, 36, 38, 38, 40, 0, 2, 34, 36, 2, 34, 2, 4, 4, 6, 32, 34, 4, 32, 30, 32, 6, 30, 6, 8, 28, 30, 8, 28, 8, 10, 26, 28, 10, 26, 10, 12, 24, 26, 12, 24, 12, 14, 14, 16, 20, 22, 22, 24, 14, 22], "width": 43, "height": 84}}, "s6": {"s6": {"type": "mesh", "uvs": [0.16156, 0.60676, 0.2123, 0.64576, 0.27235, 0.69191, 0.34078, 0.7119, 0.40146, 0.67496, 0.46858, 0.6341, 0.53526, 0.5935, 0.62065, 0.54151, 0.72505, 0.59251, 0.78797, 0.62324, 0.88925, 0.67272, 0.91702, 0.70505, 0.9536, 0.74764, 1, 0.80165, 1, 0.85089, 1, 0.88086, 0.91758, 0.94084, 0.85307, 0.98779, 0.68486, 1, 0.62274, 1, 0.36131, 0.96826, 0.33922, 0.93826, 0.31213, 0.90145, 0.27528, 0.85139, 0.23815, 0.80094, 0.18427, 0.72773, 0.15066, 0.68207, 0.11623, 0.6353, 0.30185, 0.75324, 0.58871, 0.63667, 0.6162, 0.59517, 0.65227, 0.68061, 0.64368, 0.71723, 0.67288, 0.75812, 0.68147, 0.8039, 0.68834, 0.85211, 0.69693, 0.89605, 0.70552, 0.94066], "triangles": [30, 6, 7, 8, 30, 7, 29, 6, 30, 5, 6, 29, 29, 30, 8, 29, 8, 9, 4, 5, 29, 31, 29, 9, 31, 9, 10, 31, 4, 29, 32, 4, 31, 3, 4, 32, 11, 32, 31, 11, 31, 10, 33, 32, 11, 33, 11, 12, 33, 28, 32, 34, 33, 12, 34, 12, 13, 28, 33, 34, 35, 34, 13, 14, 35, 13, 23, 34, 35, 35, 14, 15, 36, 35, 15, 22, 35, 36, 21, 22, 36, 16, 37, 36, 21, 36, 37, 15, 16, 36, 20, 21, 37, 17, 37, 16, 19, 20, 37, 18, 19, 37, 17, 18, 37, 27, 0, 1, 26, 27, 1, 26, 1, 2, 25, 26, 2, 3, 25, 2, 28, 25, 3, 28, 3, 32, 24, 25, 28, 24, 28, 34, 23, 24, 34, 22, 23, 35], "vertices": [1, 17, -53.03, 4.93, 1, 1, 17, -23.55, 10.41, 1, 3, 17, 11.34, 16.88, 0.97368, 18, 101.43, 80.78, 0.02296, 19, -64.91, 68.38, 0.00336, 3, 17, 29.05, 29.88, 0.64793, 18, 96.87, 59.28, 0.30315, 19, -75.22, 48.98, 0.04892, 3, 17, 7.78, 50.83, 0.16858, 18, 126.68, 57.81, 0.53917, 19, -46.97, 39.34, 0.29225, 3, 17, -15.75, 74.01, 0.02593, 18, 159.67, 56.19, 0.20773, 19, -15.72, 28.67, 0.76634, 2, 17, -39.13, 97.03, 0.00026, 19, 15.34, 18.07, 0.99974, 1, 19, 55.1, 4.5, 1, 2, 18, 214.75, 13.12, 0.32966, 19, 25.33, -27.93, 0.67034, 2, 18, 202.9, -10.62, 0.90161, 19, 7.39, -47.47, 0.09839, 1, 18, 183.83, -48.82, 1, 1, 18, 166.97, -65.34, 1, 1, 18, 144.78, -87.09, 1, 1, 18, 116.62, -114.67, 1, 1, 18, 86.12, -130.52, 1, 1, 18, 67.55, -140.16, 1, 2, 17, 219.22, 129.05, 0.00102, 18, 20.98, -141.31, 0.99898, 2, 17, 247.03, 105.46, 0.00908, 18, -15.48, -142.22, 0.99092, 2, 17, 244.99, 62.93, 0.04498, 18, -42.27, -109.13, 0.95502, 2, 17, 241.19, 48, 0.06869, 18, -49.37, -95.45, 0.93131, 2, 17, 203.71, -9.36, 0.34677, 18, -59.59, -27.71, 0.65323, 2, 17, 182.06, -9.5, 0.45436, 18, -43.53, -13.19, 0.54564, 2, 17, 155.51, -9.67, 0.83156, 18, -23.82, 4.61, 0.16844, 1, 17, 119.39, -9.89, 1, 1, 17, 83, -10.12, 1, 1, 17, 30.18, -10.46, 1, 1, 17, -2.76, -10.66, 1, 1, 17, -36.51, -10.88, 1, 3, 17, 54.63, 13.41, 0.87932, 18, 66.81, 54.55, 0.11649, 19, -105.42, 52.73, 0.0042, 3, 17, -6.65, 102.43, 0.00304, 18, 171.81, 28.92, 0.24596, 19, -11.58, -0.89, 0.751, 2, 18, 200.66, 36.22, 0.01164, 19, 18.17, -1.84, 0.98836, 3, 17, 26.96, 110.14, 0.00014, 18, 151.85, 0.79, 0.99416, 19, -38.52, -22.42, 0.0057, 1, 18, 128.19, -9.1, 1, 1, 18, 106.2, -28.68, 1, 1, 18, 78.82, -45.29, 1, 1, 18, 49.74, -62.32, 1, 2, 17, 175.42, 83.74, 0.00377, 18, 23.5, -78.34, 0.99623, 2, 17, 206.12, 78.12, 0.02118, 18, -3.15, -94.58, 0.97882], "hull": 28, "edges": [4, 6, 34, 36, 36, 38, 38, 40, 54, 0, 0, 2, 2, 4, 52, 54, 2, 52, 50, 52, 4, 50, 6, 56, 48, 50, 56, 48, 10, 58, 18, 20, 58, 18, 10, 12, 12, 14, 12, 60, 14, 16, 16, 18, 60, 16, 6, 8, 8, 10, 8, 62, 62, 20, 6, 64, 20, 22, 64, 22, 56, 66, 22, 24, 24, 26, 66, 24, 48, 68, 68, 26, 46, 48, 46, 70, 26, 28, 28, 30, 70, 28, 44, 46, 44, 72, 40, 42, 42, 44, 42, 74, 30, 32, 32, 34, 74, 32], "width": 248, "height": 698}}, "s7": {"s7": {"type": "mesh", "uvs": [0.13611, 0, 0.31675, 0.07633, 0.51276, 0.15916, 0.73042, 0.25114, 0.87747, 0.31328, 0.93769, 0.47135, 1, 0.63489, 1, 0.6767, 0.84603, 0.8585, 0.7262, 1, 0.71234, 1, 0.60392, 0.81704, 0.55796, 0.73949, 0.51565, 0.66809, 0.44539, 0.59521, 0.35032, 0.49661, 0.24454, 0.38689, 0.143, 0.28157, 0, 0.13326, 0, 0.06199, 0.73911, 0.54863, 0.81995, 0.67465, 0.70344, 0.80067], "triangles": [16, 2, 3, 16, 17, 2, 17, 1, 2, 17, 18, 1, 1, 19, 0, 1, 18, 19, 9, 10, 8, 10, 11, 8, 8, 11, 22, 22, 21, 8, 8, 21, 7, 11, 12, 22, 22, 12, 21, 12, 13, 21, 21, 6, 7, 13, 20, 21, 21, 20, 6, 13, 14, 20, 20, 5, 6, 20, 14, 4, 5, 20, 4, 4, 14, 15, 15, 3, 4, 15, 16, 3], "vertices": [1, 21, 28.91, -6.8, 1, 1, 21, 18.33, -8.03, 1, 2, 20, 57.32, -1.97, 0.06419, 21, 6.86, -9.38, 0.93581, 2, 20, 47.47, -10.18, 0.86521, 21, -5.87, -10.87, 0.13479, 1, 20, 40.82, -15.73, 1, 1, 20, 26.34, -15.94, 1, 1, 20, 11.36, -16.16, 1, 1, 20, 7.65, -15.51, 1, 1, 20, -7.27, -5.86, 1, 1, 20, -18.88, 1.65, 1, 1, 20, -18.77, 2.27, 1, 1, 20, -1.71, 4.23, 1, 1, 20, 5.52, 5.06, 1, 1, 20, 12.18, 5.82, 1, 2, 20, 19.19, 7.8, 0.99984, 21, -19.71, 19.65, 0.00016, 2, 20, 28.67, 10.48, 0.93678, 21, -10.31, 16.71, 0.06322, 2, 20, 39.22, 13.46, 0.44759, 21, 0.15, 13.44, 0.55241, 2, 20, 49.34, 16.33, 0.01843, 21, 10.19, 10.3, 0.98157, 1, 21, 24.34, 5.87, 1, 1, 21, 29.02, 1.49, 1, 1, 20, 21.03, -5.94, 1, 1, 20, 9.23, -7.56, 1, 1, 20, -1.03, -0.44, 1], "hull": 20, "edges": [0, 38, 12, 14, 18, 20, 36, 38, 34, 36, 0, 2, 34, 2, 32, 34, 2, 4, 32, 4, 30, 32, 4, 6, 6, 8, 30, 6, 26, 28, 28, 30, 28, 8, 26, 40, 8, 10, 10, 12, 40, 10, 24, 26, 24, 42, 42, 12, 20, 22, 22, 24, 22, 44, 14, 16, 16, 18, 44, 16], "width": 45, "height": 90}}, "s8": {"s8": {"type": "mesh", "uvs": [0.13775, 0, 0.25531, 0.05879, 0.38131, 0.12179, 0.51011, 0.1862, 0.72399, 0.29316, 0.77842, 0.42015, 0.83434, 0.55065, 0.8887, 0.6775, 0.94082, 0.79911, 1, 0.93721, 1, 0.99863, 0.57857, 0.91771, 0.52538, 0.84586, 0.46131, 0.7593, 0.38837, 0.66077, 0.31391, 0.56019, 0.24177, 0.46274, 0.17589, 0.37374, 0.12164, 0.30045, 0.05327, 0.2081, 0, 0.13613, 0, 0.06994, 0.07834, 0], "triangles": [16, 17, 3, 3, 18, 2, 3, 17, 18, 2, 19, 1, 2, 18, 19, 1, 20, 0, 22, 0, 21, 20, 1, 19, 0, 20, 21, 11, 9, 10, 11, 8, 9, 11, 12, 8, 8, 12, 7, 12, 13, 7, 7, 13, 6, 13, 14, 6, 14, 15, 6, 15, 5, 6, 15, 16, 5, 16, 4, 5, 16, 3, 4], "vertices": [1, 23, 22.28, -5.81, 1, 1, 23, 16.05, -6.29, 1, 1, 23, 9.38, -6.8, 1, 2, 22, 44.51, -0.88, 0.12305, 23, 2.56, -7.32, 0.87695, 2, 22, 35.68, -8.02, 0.97188, 23, -8.77, -8.18, 0.02812, 1, 22, 27.12, -8.35, 1, 1, 22, 18.32, -8.69, 1, 1, 22, 9.77, -9.02, 1, 1, 22, 1.58, -9.33, 1, 1, 22, -7.73, -9.69, 1, 1, 22, -11.62, -8.77, 1, 1, 22, -2.43, 7.25, 1, 1, 22, 2.63, 8.35, 1, 2, 22, 8.72, 9.67, 0.99955, 23, -20.89, 21.7, 0.00045, 2, 22, 15.66, 11.18, 0.97376, 23, -14.33, 19, 0.02624, 2, 22, 22.74, 12.73, 0.82622, 23, -7.62, 16.25, 0.17378, 2, 22, 29.6, 14.22, 0.46933, 23, -1.13, 13.58, 0.53067, 2, 22, 35.87, 15.59, 0.11971, 23, 4.8, 11.14, 0.88029, 2, 22, 41.03, 16.71, 0.00963, 23, 9.69, 9.13, 0.99037, 1, 23, 15.84, 6.6, 1, 1, 23, 20.64, 4.63, 1, 1, 23, 23.52, 1.44, 1, 1, 23, 24.13, -4.14, 1], "hull": 23, "edges": [0, 44, 18, 20, 20, 22, 40, 42, 42, 44, 0, 2, 38, 40, 2, 38, 2, 4, 36, 38, 4, 36, 4, 6, 6, 8, 34, 36, 6, 34, 32, 34, 8, 32, 8, 10, 30, 32, 10, 30, 10, 12, 28, 30, 12, 28, 12, 14, 26, 28, 14, 26, 14, 16, 16, 18, 22, 24, 24, 26, 16, 24], "width": 42, "height": 65}}, "s9": {"s9": {"type": "mesh", "uvs": [0.27303, 0, 0.42425, 0.05501, 0.65163, 0.13772, 0.81412, 0.19683, 0.98041, 0.25732, 0.98391, 0.52442, 0.91084, 0.5932, 0.75965, 0.73549, 0.64156, 0.84664, 0.47862, 1, 0.47478, 1, 0.36658, 0.8044, 0.26578, 0.62221, 0.18995, 0.48512, 0.08865, 0.30202, 0, 0.14176, 0, 0.06192, 0.15781, 0.0245, 0.26112, 0, 0.47865, 0.63258, 0.53613, 0.51356, 0.8937, 0.58956], "triangles": [13, 1, 20, 20, 1, 2, 13, 0, 1, 0, 14, 18, 0, 13, 14, 18, 14, 17, 14, 15, 17, 15, 16, 17, 8, 9, 11, 9, 10, 11, 11, 19, 8, 8, 19, 7, 11, 12, 19, 7, 21, 6, 19, 20, 7, 7, 20, 21, 19, 12, 20, 12, 13, 20, 6, 21, 5, 21, 20, 3, 20, 2, 3, 21, 4, 5, 21, 3, 4], "vertices": [1, 25, 16.84, -8.13, 1, 1, 25, 8.76, -6.85, 1, 2, 24, 24.33, 8.53, 0.19484, 25, -3.39, -4.92, 0.80516, 2, 24, 27.69, 0.41, 0.94044, 25, -12.07, -3.55, 0.05956, 1, 24, 31.13, -7.9, 1, 1, 24, 22.67, -14.86, 1, 1, 24, 18.1, -13.64, 1, 1, 24, 8.64, -11.12, 1, 1, 24, 1.25, -9.15, 1, 1, 24, -8.94, -6.44, 1, 1, 24, -9.07, -6.28, 1, 2, 24, -6.29, 3.12, 0.95644, 25, 7.99, 24.01, 0.04356, 2, 24, -3.71, 11.87, 0.58827, 25, 14.1, 17.24, 0.41173, 2, 24, -1.77, 18.45, 0.24001, 25, 18.7, 12.14, 0.75999, 2, 24, 0.83, 27.25, 0.017, 25, 24.84, 5.33, 0.983, 1, 25, 30.22, -0.63, 1, 1, 25, 30.62, -3.88, 1, 1, 25, 22.66, -6.4, 1, 1, 25, 17.45, -8.05, 1, 2, 24, 2.85, 2.94, 0.93364, 25, 3.06, 16.31, 0.06636, 2, 24, 8.53, 3.64, 0.84944, 25, 0.69, 11.1, 0.15056, 1, 24, 17.66, -12.85, 1], "hull": 19, "edges": [0, 36, 8, 10, 18, 20, 30, 32, 20, 22, 16, 18, 22, 16, 22, 24, 24, 38, 14, 16, 38, 14, 24, 26, 26, 40, 10, 12, 12, 14, 32, 34, 34, 36, 26, 28, 28, 30, 34, 28, 0, 2, 2, 26, 2, 4, 4, 40, 4, 6, 6, 8, 12, 42, 42, 40, 6, 42], "width": 52, "height": 41}}, "st2": {"st2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 53, 39.11, -2.34, 1, 1, 53, 15.65, -22.6, 1, 1, 53, -4.61, 0.86, 1, 1, 53, 18.85, 21.12, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 31, "height": 31}}, "s4": {"s4": {"type": "mesh", "uvs": [0.24476, 0, 0.50775, 0.0684, 0.57155, 0.18009, 0.63874, 0.29774, 0.72637, 0.45116, 0.81341, 0.60355, 0.90736, 0.76804, 1, 0.93023, 1, 0.94658, 0.89476, 1, 0.78739, 1, 0.64388, 0.85414, 0.50872, 0.71676, 0.36859, 0.57434, 0.22595, 0.42935, 0.10308, 0.30448, 0, 0.19971, 0, 0.14008, 0.08679, 0], "triangles": [8, 9, 7, 7, 9, 10, 10, 11, 7, 11, 6, 7, 11, 12, 6, 12, 5, 6, 12, 13, 5, 13, 4, 5, 13, 14, 4, 14, 3, 4, 14, 15, 3, 16, 2, 15, 15, 2, 3, 2, 16, 1, 1, 16, 0, 18, 0, 17, 0, 16, 17], "vertices": [1, 32, -3.36, 7.09, 1, 1, 32, 6.11, 12.9, 1, 1, 32, 14.1, 10.68, 1, 2, 32, 22.52, 8.34, 0.97527, 33, -6.64, 7.91, 0.02473, 2, 32, 33.49, 5.28, 0.10329, 33, 4.71, 6.99, 0.89671, 1, 33, 15.99, 6.07, 1, 1, 33, 28.16, 5.08, 1, 1, 33, 40.16, 4.11, 1, 1, 33, 41.24, 3.68, 1, 1, 33, 43.29, -1.44, 1, 1, 33, 41.79, -5.23, 1, 1, 33, 30.15, -6.48, 1, 1, 33, 19.19, -7.66, 1, 2, 32, 33.55, -10.88, 0.01487, 33, 7.83, -8.88, 0.98513, 2, 32, 21.95, -9.91, 0.6943, 33, -3.74, -10.12, 0.3057, 2, 32, 11.97, -9.08, 0.99561, 33, -13.7, -11.19, 0.00439, 1, 32, 3.59, -8.38, 1, 1, 32, 0.02, -6.1, 1, 1, 32, -6.59, 2.03, 1], "hull": 19, "edges": [0, 36, 0, 2, 14, 16, 16, 18, 18, 20, 32, 34, 34, 36, 2, 4, 30, 32, 4, 30, 4, 6, 28, 30, 6, 28, 6, 8, 26, 28, 8, 26, 8, 10, 24, 26, 10, 24, 10, 12, 12, 14, 20, 22, 22, 24, 12, 22], "width": 38, "height": 71}}, "zt": {"zt": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [371, -630, -447, -630, -447, -157, 371, -157], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 818, "height": 473}}, "t1": {"t1": {"type": "mesh", "uvs": [0.57911, 0.00307, 0.77443, 0.0025, 0.83841, 0.08572, 0.92003, 0.19188, 1, 0.29591, 1, 0.46947, 0.94504, 0.57825, 0.89162, 0.68396, 0.85283, 0.76075, 0.80895, 0.84758, 0.77148, 0.92175, 0.68344, 0.94115, 0.58502, 0.96284, 0.49825, 0.98197, 0.41644, 1, 0.34482, 1, 0.30592, 0.93782, 0.25524, 0.85681, 0.22555, 0.80935, 0.15619, 0.69849, 0.11975, 0.64025, 0.08516, 0.58496, 0.00097, 0.45038, 0.04856, 0.27234, 0.08289, 0.1439, 0.22199, 0.07779, 0.37795, 0.00366, 0.78806, 0.36996, 0.83859, 0.59256, 0.57171, 0.4248, 0.36219, 0.53059, 0.10954, 0.50304, 0.83915, 0.21763, 0.81084, 0.30205, 0.65551, 0.24077, 0.42874, 0.32011, 0.15267, 0.34766, 0.27715, 0.24298, 0.16007, 0.20661, 0.72207, 0.16694, 0.393, 0.13168, 0.47188, 0.06997, 0.64689, 0.05674, 0.75411, 0.07658, 0.88598, 0.25069, 0.91063, 0.30358, 0.91063, 0.35207, 0.89584, 0.44243, 0.60868, 0.45809, 0.69249, 0.51208, 0.49178, 0.46515, 0.56185, 0.51208, 0.70112, 0.56828, 0.41517, 0.50384, 0.54213, 0.53743, 0.61115, 0.57489, 0.72207, 0.62889, 0.80957, 0.65313, 0.46941, 0.55285, 0.53843, 0.59473, 0.54952, 0.66966, 0.43244, 0.66966, 0.3437, 0.62669, 0.32398, 0.56277, 0.2853, 0.5222, 0.37821, 0.55726, 0.45832, 0.57049, 0.51008, 0.61236, 0.51748, 0.64983, 0.45462, 0.65203, 0.37452, 0.62228, 0.63456, 0.70382, 0.70851, 0.66746, 0.80218, 0.69831, 0.6358, 0.75121, 0.70605, 0.79198, 0.71097, 0.6884, 0.77629, 0.72366, 0.64887, 0.74542, 0.70792, 0.7751, 0.77315, 0.76268, 0.10082, 0.54417, 0.1674, 0.59507, 0.2967, 0.67358, 0.49933, 0.75813, 0.65468, 0.82456, 0.70678, 0.85821, 0.76757, 0.85303, 0.20986, 0.73915, 0.51863, 0.84095, 0.67494, 0.89617, 0.75117, 0.89789, 0.39608, 0.8444, 0.48775, 0.86338, 0.53214, 0.89444, 0.46459, 0.89789], "triangles": [27, 47, 28, 6, 47, 5, 35, 53, 64, 53, 35, 50, 35, 64, 36, 47, 46, 5, 46, 4, 5, 50, 35, 29, 48, 29, 27, 22, 23, 36, 47, 27, 46, 29, 34, 27, 29, 35, 34, 27, 33, 46, 27, 34, 33, 33, 45, 46, 46, 45, 4, 36, 37, 35, 36, 38, 37, 36, 23, 38, 35, 37, 34, 33, 44, 45, 45, 44, 4, 34, 39, 33, 33, 32, 44, 33, 39, 32, 44, 3, 4, 23, 24, 38, 44, 32, 3, 37, 40, 34, 38, 25, 37, 37, 25, 40, 39, 2, 32, 39, 43, 2, 34, 40, 39, 32, 2, 3, 38, 24, 25, 40, 41, 39, 39, 41, 42, 41, 0, 42, 39, 42, 43, 25, 26, 40, 40, 26, 41, 43, 1, 2, 43, 42, 1, 41, 26, 0, 42, 0, 1, 83, 84, 88, 51, 50, 29, 17, 92, 95, 95, 94, 12, 95, 92, 93, 95, 93, 94, 93, 92, 89, 63, 64, 30, 70, 63, 65, 58, 53, 54, 88, 84, 89, 94, 90, 11, 11, 91, 10, 11, 90, 91, 10, 91, 9, 9, 91, 87, 90, 86, 91, 91, 86, 87, 90, 89, 85, 90, 85, 86, 93, 89, 94, 90, 94, 89, 85, 75, 86, 86, 75, 87, 87, 75, 9, 9, 75, 8, 89, 84, 85, 84, 74, 85, 84, 60, 74, 60, 71, 74, 85, 74, 75, 75, 80, 8, 75, 79, 80, 74, 78, 75, 75, 78, 79, 77, 80, 79, 79, 78, 76, 80, 77, 8, 77, 79, 76, 77, 73, 8, 8, 73, 7, 84, 61, 60, 74, 71, 78, 78, 71, 76, 77, 76, 73, 71, 72, 76, 72, 71, 55, 73, 57, 7, 57, 73, 72, 57, 72, 56, 72, 73, 76, 57, 28, 7, 7, 28, 6, 61, 68, 60, 61, 69, 68, 61, 70, 69, 68, 59, 60, 71, 60, 55, 60, 59, 55, 72, 55, 56, 57, 56, 28, 69, 67, 68, 69, 66, 67, 69, 70, 66, 68, 67, 59, 55, 52, 56, 56, 52, 28, 59, 67, 58, 67, 66, 58, 59, 54, 55, 59, 58, 54, 54, 51, 55, 55, 51, 52, 52, 51, 49, 28, 52, 49, 84, 83, 61, 54, 50, 51, 66, 65, 58, 51, 48, 49, 53, 50, 54, 70, 65, 66, 65, 30, 58, 30, 53, 58, 62, 70, 61, 63, 30, 65, 62, 63, 70, 30, 64, 53, 28, 49, 27, 83, 62, 61, 83, 63, 62, 89, 92, 88, 83, 82, 63, 51, 29, 48, 49, 48, 27, 6, 28, 47, 31, 22, 36, 81, 22, 31, 21, 22, 81, 13, 95, 12, 13, 14, 16, 14, 15, 16, 16, 95, 13, 16, 17, 95, 17, 18, 92, 19, 88, 18, 88, 92, 18, 88, 19, 83, 19, 82, 83, 19, 20, 82, 63, 82, 64, 20, 21, 82, 21, 81, 82, 81, 31, 82, 82, 31, 64, 12, 94, 11, 64, 31, 36], "vertices": [1, 38, 47.14, -60.87, 1, 1, 38, 2.63, -62.26, 1, 1, 38, -12.54, -41.45, 1, 2, 37, 71.81, -148.61, 0.01168, 38, -31.9, -14.91, 0.98832, 2, 37, 39.63, -147.77, 0.09113, 38, -50.87, 11.1, 0.90887, 2, 37, 3.82, -121.76, 0.30685, 38, -52.1, 55.34, 0.69315, 2, 37, -11.25, -95.32, 0.50951, 38, -40.35, 83.41, 0.49049, 2, 37, -25.91, -69.63, 0.9648, 38, -28.92, 110.7, 0.0352, 2, 37, -36.55, -50.96, 0.90851, 38, -20.63, 130.52, 0.09149, 3, 36, -31.68, -90.43, 0.00424, 37, -48.59, -29.86, 0.95919, 38, -11.24, 152.93, 0.03657, 3, 36, -41.96, -72.41, 0.41843, 37, -58.87, -11.83, 0.57801, 38, -3.23, 172.08, 0.00356, 1, 36, -34.16, -53.26, 1, 1, 36, -25.45, -31.85, 1, 1, 36, -17.77, -12.98, 1, 1, 36, -10.52, 4.82, 1, 1, 36, -0.93, 18.03, 1, 1, 36, 17.11, 15.88, 1, 1, 36, 40.62, 13.09, 1, 1, 36, 54.39, 11.46, 1, 1, 36, 86.55, 7.64, 1, 1, 36, 103.45, 5.63, 1, 1, 36, 119.49, 3.72, 1, 2, 36, 158.54, -0.91, 0.98521, 38, 175.73, 56.82, 0.01479, 2, 36, 188.9, -36.38, 0.06896, 38, 166.15, 11.13, 0.93104, 2, 36, 210.79, -61.96, 0.002, 38, 159.23, -21.82, 0.998, 1, 38, 128, -39.56, 1, 1, 38, 92.98, -59.44, 1, 3, 36, 69.66, -158.16, 2e-05, 37, 52.75, -97.58, 0.15724, 38, -3.09, 31.32, 0.84274, 2, 37, 0.06, -73.54, 0.93721, 38, -16.19, 87.74, 0.06279, 3, 36, 87.34, -110.03, 0.03537, 37, 70.43, -49.45, 0.24469, 38, 45.83, 46.67, 0.71994, 3, 36, 93.59, -55.53, 0.13254, 37, 76.68, 5.05, 0.68736, 38, 92.83, 74.97, 0.1801, 1, 36, 133.13, -13.05, 1, 2, 37, 77.33, -129.84, 0.00495, 38, -13.65, -7.83, 0.99505, 2, 37, 63.71, -111.96, 0.05353, 38, -7.8, 13.86, 0.94647, 1, 38, 28.04, -0.77, 1, 3, 36, 128.1, -99.35, 0.02963, 37, 111.18, -38.77, 0.04118, 38, 79.16, 20.9, 0.92919, 3, 36, 159.41, -44.29, 0.1322, 37, 142.49, 16.28, 0.00552, 38, 141.88, 29.67, 0.86229, 3, 36, 164.32, -82.94, 0.00159, 37, 147.41, -22.37, 0.00038, 38, 114.26, 2.2, 0.99803, 2, 36, 187.51, -66.8, 0.00528, 38, 141.2, -6.33, 0.99472, 1, 38, 13.39, -20.01, 1, 1, 38, 88.64, -26.91, 1, 1, 38, 71.1, -43.14, 1, 1, 38, 31.31, -47.62, 1, 1, 38, 6.73, -43.25, 1, 2, 37, 64.24, -133.52, 0.02898, 38, -24.56, 0.3, 0.97102, 2, 37, 50.02, -130.14, 0.07945, 38, -30.55, 13.62, 0.92055, 2, 37, 40.02, -122.87, 0.13928, 38, -30.9, 25.98, 0.86072, 2, 37, 23.36, -106.6, 0.28841, 38, -28.17, 49.11, 0.71159, 3, 36, 75.52, -111.86, 0.02543, 37, 58.61, -51.28, 0.39086, 38, 37.17, 54.92, 0.5837, 1, 37, 36.24, -58.65, 1, 3, 36, 89.72, -89.24, 0.0858, 37, 72.81, -28.66, 0.4022, 38, 63.76, 57.47, 0.512, 1, 37, 53.74, -34.55, 1, 1, 37, 23.49, -51.82, 1, 3, 36, 92.01, -69.31, 0.12448, 37, 75.1, -8.73, 0.58443, 38, 80.95, 67.81, 0.29109, 1, 37, 51.16, -27.12, 1, 1, 37, 34.18, -34.23, 1, 1, 37, 8.18, -46.6, 1, 1, 37, -8.55, -59.11, 1, 1, 37, 57.72, -11.39, 1, 1, 37, 39.83, -17.85, 1, 1, 37, 22.88, -8.66, 1, 1, 37, 38.57, 12.93, 1, 1, 37, 59.33, 22.86, 1, 3, 36, 92.07, -43.66, 0.10958, 37, 75.16, 16.92, 0.799, 38, 101.31, 83.42, 0.09142, 3, 36, 105.62, -42.6, 0.40402, 37, 88.71, 17.98, 0.11386, 38, 110.41, 73.32, 0.48212, 1, 37, 69.03, 6.09, 1, 1, 37, 55.57, -6.7, 1, 1, 37, 39.99, -9.98, 1, 1, 37, 31.27, -5.73, 1, 1, 37, 39.24, 6.2, 1, 1, 37, 56.11, 16.52, 1, 1, 37, 4.44, -19.23, 1, 1, 37, 2.03, -38.32, 1, 1, 37, -16.88, -50.98, 1, 1, 37, -5.5, -12.36, 1, 1, 37, -23.33, -19.21, 1, 1, 37, -2.62, -35.64, 1, 1, 37, -18.64, -42.4, 1, 1, 37, -6.06, -15.64, 1, 1, 37, -20.09, -22.08, 1, 1, 37, -26.27, -35.98, 1, 1, 36, 125.81, -5.28, 1, 1, 36, 106.39, -9.93, 1, 3, 36, 72.87, -22.02, 0.17188, 37, 55.95, 38.56, 0.79892, 38, 106.74, 111.83, 0.0292, 1, 37, 11.36, 13.85, 1, 1, 37, -23.16, -4.85, 1, 1, 37, -37.09, -9.42, 1, 3, 36, -27.25, -81.98, 0.01072, 37, -44.17, -21.41, 0.96568, 38, -1.85, 154.58, 0.02359, 1, 36, 70.97, 3.83, 1, 1, 37, -8.31, 22.71, 1, 1, 37, -40.65, 2.15, 1, 3, 36, -34.31, -72.23, 0.03144, 37, -51.22, -11.66, 0.9615, 38, 1.57, 166.12, 0.00705, 1, 37, 7.39, 45.83, 1, 1, 37, -8.8, 31.76, 1, 1, 37, -21.16, 28.23, 1, 1, 37, -12.82, 41.21, 1], "hull": 27, "edges": [8, 10, 28, 30, 54, 56, 56, 14, 54, 58, 62, 44, 2, 4, 4, 64, 54, 66, 66, 64, 66, 68, 68, 70, 70, 72, 44, 46, 46, 48, 72, 46, 68, 74, 74, 76, 76, 48, 66, 78, 78, 80, 48, 50, 50, 52, 80, 50, 78, 82, 82, 52, 78, 84, 2, 0, 0, 52, 84, 0, 78, 86, 86, 2, 66, 88, 4, 6, 6, 8, 88, 6, 66, 90, 90, 8, 66, 92, 92, 10, 54, 94, 10, 12, 12, 14, 94, 12, 58, 96, 96, 98, 98, 56, 58, 100, 100, 102, 102, 104, 104, 56, 60, 106, 106, 100, 106, 108, 108, 110, 110, 112, 112, 114, 114, 14, 60, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 60, 128, 128, 62, 126, 128, 126, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 126, 120, 142, 142, 144, 144, 146, 14, 16, 146, 16, 142, 148, 148, 150, 150, 16, 142, 152, 152, 154, 154, 16, 142, 156, 156, 158, 158, 160, 160, 16, 62, 162, 42, 44, 162, 42, 128, 164, 40, 42, 164, 40, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 16, 18, 18, 20, 174, 18, 38, 40, 38, 176, 176, 178, 178, 180, 180, 182, 182, 20, 36, 38, 36, 184, 184, 186, 186, 188, 20, 22, 188, 22, 34, 36, 34, 190, 22, 24, 190, 24, 30, 32, 32, 34, 24, 26, 26, 28, 32, 26], "width": 228, "height": 255}}, "t2": {"t2": {"type": "mesh", "uvs": [0.16918, 0, 0.45333, 0.08248, 0.52773, 0.1981, 0.62769, 0.35343, 0.72359, 0.50246, 0.79412, 0.61205, 0.85378, 0.70477, 0.91977, 0.80731, 0.96954, 0.88465, 1, 0.93199, 1, 0.99043, 0.80861, 0.98834, 0.6602, 0.98672, 0.59377, 0.93982, 0.48682, 0.77574, 0.40418, 0.64894, 0.34909, 0.56442, 0.25091, 0.41379, 0.1614, 0.27646, 0.08338, 0.15675, 0, 0.02883, 0.29122, 0.13928, 0.38354, 0.24301, 0.45517, 0.37673, 0.51963, 0.51173, 0.53356, 0.65526, 0.57537, 0.77179, 0.68163, 0.88831], "triangles": [11, 9, 10, 12, 27, 11, 11, 8, 9, 11, 7, 8, 11, 27, 7, 12, 13, 27, 13, 26, 27, 27, 6, 7, 27, 26, 6, 14, 26, 13, 14, 25, 26, 14, 15, 25, 26, 5, 6, 26, 25, 5, 15, 24, 25, 25, 4, 5, 25, 24, 4, 15, 16, 24, 16, 23, 24, 16, 17, 23, 24, 3, 4, 24, 23, 3, 17, 22, 23, 23, 2, 3, 23, 22, 2, 17, 18, 22, 18, 21, 22, 18, 19, 21, 22, 1, 2, 22, 21, 1, 19, 0, 21, 19, 20, 0, 21, 0, 1], "vertices": [1, 54, -3.72, 3.22, 1, 2, 54, 12.22, 12.99, 0.91825, 55, -12.37, 9.75, 0.08175, 2, 54, 21.95, 11.07, 0.38138, 55, -2.54, 11.14, 0.61862, 4, 54, 35.03, 8.48, 7e-05, 55, 10.66, 13.01, 0.98308, 56, -15.91, 13.65, 0.01678, 57, -22.87, 29.1, 7e-05, 3, 55, 23.33, 14.8, 0.53712, 56, -3.29, 15.71, 0.41313, 57, -11.2, 23.87, 0.04975, 3, 55, 32.64, 16.11, 0.13421, 56, 6, 17.22, 0.59433, 57, -2.61, 20.02, 0.27146, 3, 55, 40.52, 17.23, 0.01866, 56, 13.85, 18.5, 0.35339, 57, 4.65, 16.77, 0.62795, 3, 55, 49.24, 18.46, 5e-05, 56, 22.54, 19.92, 0.07414, 57, 12.68, 13.17, 0.92581, 2, 56, 29.09, 20.99, 0.00729, 57, 18.74, 10.45, 0.99271, 2, 56, 33.1, 21.64, 0.0003, 57, 22.45, 8.79, 0.9997, 1, 57, 25.19, 5.3, 1, 1, 57, 15.76, -1.91, 1, 1, 57, 8.45, -7.5, 1, 2, 56, 25.7, -2.44, 0.00207, 57, 3.01, -7.24, 0.99793, 1, 56, 11.77, -4.79, 1, 2, 55, 27.15, -7.6, 0.10863, 56, 1.01, -6.6, 0.89137, 2, 55, 19.95, -8.65, 0.73421, 56, -6.16, -7.81, 0.26579, 2, 54, 23.94, -12.58, 0.12835, 55, 7.13, -10.54, 0.87165, 2, 54, 12.33, -10.35, 0.85568, 55, -4.57, -12.25, 0.14432, 1, 54, 2.21, -8.4, 1, 1, 54, -8.6, -6.33, 1, 1, 54, 9.27, 2.46, 1, 2, 54, 19, 1.97, 0.54791, 55, -2.33, 1.57, 0.45209, 1, 55, 8.73, 2.34, 1, 3, 55, 19.74, 2.65, 0.90858, 56, -6.62, 3.49, 0.09025, 57, -20.71, 15.5, 0.00117, 3, 55, 30.3, -0.2, 0.00574, 56, 4, 0.86, 0.99116, 57, -13.29, 7.46, 0.0031, 2, 56, 13.22, 0.52, 0.98161, 57, -5.78, 2.1, 0.01839, 1, 57, 4.87, -0.8, 1], "hull": 21, "edges": [0, 40, 0, 2, 18, 20, 24, 26, 38, 40, 38, 42, 42, 2, 36, 38, 36, 44, 2, 4, 44, 4, 34, 36, 34, 46, 4, 6, 46, 6, 32, 34, 32, 48, 6, 8, 48, 8, 30, 32, 30, 50, 8, 10, 50, 10, 26, 28, 28, 30, 28, 52, 10, 12, 52, 12, 26, 54, 12, 14, 54, 14, 20, 22, 22, 24, 14, 16, 16, 18, 22, 16], "width": 62, "height": 76}}, "t3": {"t3": {"type": "mesh", "uvs": [0.90876, 0.21926, 1, 0.40449, 1, 0.50479, 0.93923, 0.6264, 0.87337, 0.75819, 0.81285, 0.8793, 0.75253, 1, 0.6969, 1, 0.52982, 0.97453, 0.29223, 0.93831, 0.05123, 0.90156, 0, 0.84413, 0, 0.78354, 0.08515, 0.67485, 0.18333, 0.54953, 0.28088, 0.42501, 0.37389, 0.30628, 0.46915, 0.18469, 0.54456, 0.08842, 0.61383, 0, 0.65044, 0, 0.83021, 0.05979, 0.68797, 0.15422, 0.65586, 0.26238, 0.86844, 0.3279, 0.6008, 0.39238, 0.82103, 0.47558, 0.54727, 0.53278, 0.78433, 0.5983, 0.50444, 0.65025, 0.71856, 0.71993, 0.46468, 0.78337, 0.65891, 0.83849], "triangles": [29, 13, 14, 31, 13, 29, 32, 31, 30, 12, 10, 11, 13, 9, 12, 5, 32, 4, 12, 9, 10, 13, 31, 9, 8, 31, 32, 9, 31, 8, 7, 32, 5, 8, 32, 7, 6, 7, 5, 27, 15, 25, 27, 25, 26, 14, 15, 27, 28, 27, 26, 3, 28, 26, 29, 14, 27, 29, 27, 28, 30, 29, 28, 4, 28, 3, 30, 28, 4, 31, 29, 30, 32, 30, 4, 22, 20, 21, 18, 19, 20, 22, 18, 20, 17, 18, 22, 22, 21, 0, 23, 17, 22, 23, 22, 0, 16, 17, 23, 24, 23, 0, 25, 16, 23, 25, 23, 24, 24, 0, 1, 15, 16, 25, 26, 25, 24, 26, 24, 1, 26, 1, 2, 2, 3, 26], "vertices": [1, 39, -3.97, 11.23, 1, 2, 39, 20.73, 27, 0.91508, 40, -20.1, 28.2, 0.08492, 2, 39, 35.33, 30.65, 0.64485, 40, -5.52, 31.91, 0.35515, 3, 39, 54.53, 29.06, 0.19844, 40, 13.69, 30.39, 0.79817, 41, -25.14, 33.7, 0.00339, 3, 39, 75.34, 27.34, 0.01211, 40, 34.5, 28.75, 0.7752, 41, -4.47, 30.77, 0.21269, 2, 40, 53.63, 27.25, 0.27617, 41, 14.53, 28.08, 0.72383, 2, 40, 72.69, 25.75, 0.04734, 41, 33.46, 25.4, 0.95266, 2, 40, 74.09, 20.25, 0.03656, 41, 34.51, 19.83, 0.96344, 2, 40, 74.58, 2.79, 0.00064, 41, 33.93, 2.37, 0.99936, 1, 41, 33.09, -22.45, 1, 2, 40, 76.01, -47.22, 0.00393, 41, 32.25, -47.63, 0.99607, 2, 40, 68.94, -54.41, 0.01601, 41, 24.75, -54.36, 0.98399, 2, 40, 60.14, -56.65, 0.03876, 41, 15.83, -56.05, 0.96124, 3, 39, 82.71, -53.69, 4e-05, 40, 42.19, -52.25, 0.17879, 41, -1.81, -50.55, 0.82117, 3, 39, 62.04, -48.53, 0.02777, 40, 21.51, -47.17, 0.48688, 41, -22.14, -44.2, 0.48535, 3, 39, 41.51, -43.41, 0.2239, 40, 0.95, -42.13, 0.6078, 41, -42.34, -37.9, 0.1683, 3, 39, 21.93, -38.53, 0.62814, 40, -18.64, -37.32, 0.34019, 41, -61.61, -31.89, 0.03166, 3, 39, 1.88, -33.52, 0.92484, 40, -38.71, -32.4, 0.07411, 41, -81.33, -25.73, 0.00105, 2, 39, -14, -29.56, 0.99169, 40, -54.6, -28.5, 0.00831, 2, 39, -28.58, -25.93, 1, 40, -69.2, -24.93, 0, 1, 39, -29.48, -22.3, 1, 1, 39, -25.23, -2.34, 1, 2, 39, -7.97, -12.98, 0.99599, 40, -48.64, -11.9, 0.00401, 3, 39, 8.57, -12.22, 0.96903, 40, -32.11, -11.07, 0.03086, 41, -73.42, -4.85, 0.00011, 1, 39, 12.84, 11.2, 1, 3, 39, 28.84, -12.94, 0.7198, 40, -11.83, -11.71, 0.27157, 41, -53.22, -6.75, 0.00863, 2, 39, 35.5, 11.88, 0.69625, 40, -5.27, 13.14, 0.30375, 3, 39, 50.6, -13.13, 0.03061, 40, 9.93, -11.81, 0.9105, 41, -31.51, -8.2, 0.05889, 2, 39, 54.27, 12.71, 0.10165, 40, 13.49, 14.04, 0.89835, 2, 40, 28.08, -11.71, 0.70624, 41, -13.39, -9.21, 0.29376, 3, 39, 73.6, 10.63, 0.0034, 40, 32.83, 12.04, 0.85853, 41, -7.18, 14.19, 0.13806, 2, 40, 48.43, -10.72, 0.01386, 41, 6.99, -9.49, 0.98614, 2, 40, 51.56, 10.52, 0.16948, 41, 11.43, 11.52, 0.83052], "hull": 22, "edges": [2, 4, 12, 14, 20, 22, 22, 24, 38, 40, 40, 42, 36, 38, 36, 44, 2, 0, 0, 42, 44, 0, 34, 36, 34, 46, 46, 48, 48, 2, 32, 34, 32, 50, 50, 52, 52, 4, 30, 32, 30, 54, 54, 56, 4, 6, 56, 6, 28, 30, 28, 58, 58, 60, 6, 8, 60, 8, 24, 26, 26, 28, 26, 62, 62, 64, 8, 10, 10, 12, 64, 10, 14, 16, 16, 18, 18, 20], "width": 102, "height": 150}}}}], "animations": {"animation1": {"bones": {"st2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s2": {"rotate": [{"angle": 0.89, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 4.8, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 0.89}]}, "s1": {"rotate": [{"angle": 0.15, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 2.4, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": 0.15}]}, "st4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "t1": {"rotate": [{"angle": 0.66, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 3.6, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 0.66}]}, "t2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}], "shear": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s11": {"rotate": [{"angle": 3.64, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 19.73, "curve": "stepped"}, {"time": 1.8333, "angle": 19.73, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 3.64}]}, "s13": {"rotate": [{"angle": 13.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 27.07, "curve": "stepped"}, {"time": 2.3333, "angle": 27.07, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 13.53}]}, "s15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 2.52, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 6.52, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "t3": {"shear": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "t16": {"shear": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 2.4, "y": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "t17": {"shear": [{"x": 0.15, "y": -0.15, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 2.4, "y": -2.4, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "x": 0.15, "y": -0.15}]}, "t18": {"shear": [{"x": 0.44, "y": -0.44, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 2.4, "y": -2.4, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 0.44, "y": -0.44}]}, "t23": {"rotate": [{"angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 0.78, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 7.08}]}, "t24": {"rotate": [{"angle": 6.43, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.2333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": 0.78, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 2.6667, "angle": 6.43}]}, "t25": {"rotate": [{"angle": 5.16, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.4667, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 0.78, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "angle": 5.16}]}, "t26": {"rotate": [{"angle": 3.72, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 0.7, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": 0.78, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 2.6667, "angle": 3.72}]}, "t27": {"rotate": [{"angle": 2.31, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.9333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 0.78, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "angle": 2.31}]}, "t19": {"rotate": [{"angle": 1.91, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.2333, "angle": 2.27, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": -1.2, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 2.6667, "angle": 1.91}]}, "t20": {"rotate": [{"angle": 0.89, "curve": 0.344, "c2": 0.37, "c3": 0.682, "c4": 0.72}, {"time": 0.2333, "angle": 1.63, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5667, "angle": 2.27, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": -1.2, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 2.6667, "angle": 0.89}]}, "t21": {"rotate": [{"angle": -0.25, "curve": 0.324, "c2": 0.3, "c3": 0.662, "c4": 0.65}, {"time": 0.2333, "angle": 0.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.9, "angle": 2.27, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": -1.2, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "angle": -0.25}]}, "t22": {"rotate": [{"angle": -1.11, "curve": 0.3, "c2": 0.17, "c3": 0.638, "c4": 0.53}, {"time": 0.2333, "angle": -0.56, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.2333, "angle": 2.27, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": -1.2, "curve": 0.311, "c3": 0.646, "c4": 0.35}, {"time": 2.6667, "angle": -1.11}]}, "t8": {"rotate": [{"angle": 4.6, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4, "angle": 6.21, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": -0.45, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.6667, "angle": 4.6}]}, "t9": {"rotate": [{"angle": 2.46, "curve": 0.34, "c2": 0.35, "c3": 0.687, "c4": 0.73}, {"time": 0.4, "angle": 4.98, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.7333, "angle": 6.21, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": -0.45, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 2.6667, "angle": 2.46}]}, "t10": {"rotate": [{"angle": 0.41, "curve": 0.306, "c2": 0.24, "c3": 0.653, "c4": 0.62}, {"time": 0.4, "angle": 2.88, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.0667, "angle": 6.21, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "angle": -0.45, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "angle": 0.41}]}, "st": {"shear": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "st8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.27, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "st9": {"rotate": [{"angle": -0.6, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -3.27, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.6}]}, "st10": {"rotate": [{"angle": -1.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -3.27, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.63}]}, "st5": {"rotate": [{"angle": 0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 0.78, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 0.14}]}, "st6": {"rotate": [{"angle": 0.39, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.3333, "angle": 0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 0.78, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 0.39}]}, "st7": {"rotate": [{"angle": 0.63, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 0.6667, "angle": 0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 0.78, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 0.63}]}, "t11": {"rotate": [{"angle": -1.44, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -7.78, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -1.44}]}, "t12": {"rotate": [{"angle": -3.86, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.3333, "angle": -1.44, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -7.78, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -3.86}]}, "t13": {"rotate": [{"angle": -6.33, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 0.6667, "angle": -1.44, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -7.78, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -6.33}]}, "st3": {"translate": [{"x": 0.41, "y": -0.83, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "x": 4, "y": -8, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 2.6667, "x": 0.41, "y": -0.83}]}, "t14": {"rotate": [{"angle": -2.05, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -11.12, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -2.05}]}, "t15": {"rotate": [{"angle": -5.52, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.3333, "angle": -2.05, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -11.12, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -5.52}]}, "s16": {"rotate": [{"angle": 7.2, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 14.4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 7.2}]}, "s9": {"rotate": [{"angle": -6.86, "curve": 0.343, "c2": 0.37, "c3": 0.715, "c4": 0.83}, {"time": 0.6667, "angle": -0.65, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -10.4, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": -6.86}]}, "s21": {"rotate": [{"angle": -8.47, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 0.6667, "angle": -1.92, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -10.4, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -8.47}]}, "s23": {"rotate": [{"angle": -9.77, "curve": 0.285, "c2": 0.17, "c3": 0.657, "c4": 0.63}, {"time": 0.6667, "angle": -3.49, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -10.4, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.6667, "angle": -9.77}]}, "s17": {"rotate": [{"angle": -5.2, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -10.4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -5.2}]}, "s18": {"rotate": [{"angle": -28.25, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 1.1667, "angle": -1.77, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -28.25}]}, "s19": {"rotate": [{"angle": -24.62, "curve": 0.346, "c2": 0.39, "c3": 0.68, "c4": 0.73}, {"time": 0.1, "angle": -26.47, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.2667, "angle": -28.25, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.2667, "angle": -5.21, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.6, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.6667, "angle": -24.62}]}, "s22": {"rotate": [{"angle": -17.92, "curve": 0.344, "c2": 0.37, "c3": 0.681, "c4": 0.71}, {"time": 0.2, "angle": -23.04, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5333, "angle": -28.25, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 1.3667, "angle": -9.48, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1.8667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": -17.92}]}, "s24": {"rotate": [{"angle": -10.43, "curve": 0.331, "c2": 0.33, "c3": 0.672, "c4": 0.68}, {"time": 0.3, "angle": -18.76, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.8, "angle": -28.25, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.4667, "angle": -14.12, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.1333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "angle": -10.43}]}, "s3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.77, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s4": {"rotate": [{"angle": 0.33, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 1.77, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 0.33}]}, "s5": {"rotate": [{"angle": 0.88, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 1.77, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 0.88}]}, "t6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "target2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "st12": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.92, "y": -3.51, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}}, "deform": {"default": {"t1": {"t1": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 30, "vertices": [2.63029, -4.21324, 2.45578, -4.21474, -1.73169, -4.58997, -5.75548, -11.08018, -6.21777, -11.0885, -12.29852, -1.6904, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.33524, 0.96954, -0.04575, 1.65091, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.46722, -2.655, 0.08121, -4.45834, 1.98798, 2.10689, 2.07651, 2.10895, 2.88515, -0.41159, -1.34003, 0.97334, -1.29916, 0.97426, -0.04609, 1.65729, -1.71538, 1.24577, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.76273, 0.89192, -1.72421, 0.89355, -0.36734, 1.95337, -0.78532, 0.95668, -0.74399, 0.95839, 0.28069, 1.19092, 0.50607, -3.27786, 0.3689, -3.28046, -2.28994, -2.30563, 0.50607, -3.27786, -2.28994, -2.30563, 0, 0, 7.46066, 0.229, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.40195, 0.97452, 1.44299, 0.97566, 1.62944, -0.58572, 3.00424, -4.57019, 0, 0, 0, 0, 0, 0, -0.12308, -0.16002, 4.77377, -7.38686, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.78806, 0.41069, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.3707, 0.99533, -1.32831, 0.99638, -0.04696, 1.6944, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.67825, 1.5798, 1.67825, 1.5798, 0, 0, 0, 0, 1.34586, 1.81337, 1.34586, 1.81337, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.89459, -1.80695, 1.81989, -1.80765, -0.27476, -2.5957, 0, 0, 0, 0, 0, 0, -1.80614, 1.50856, -1.74213, 1.51086, 0.0954, 2.34415], "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.6667, "offset": 30, "vertices": [2.14509, -3.43604, 2.00277, -3.43726, -1.41225, -3.74327, -4.69379, -9.03627, -5.07081, -9.04305, -10.02986, -1.37858, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.08893, 0.7907, -0.03731, 1.34637, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.82764, -2.16524, 0.06623, -3.63593, 1.62126, 1.71824, 1.69346, 1.71992, 2.35294, -0.33567, -1.09284, 0.79379, -1.05951, 0.79454, -0.03759, 1.35157, -1.39895, 1.01597, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.43756, 0.72739, -1.40615, 0.72872, -0.29958, 1.59304, -0.64046, 0.78021, -0.60675, 0.7816, 0.22891, 0.97123, 0.41272, -2.67321, 0.30085, -2.67532, -1.86753, -1.88032, 0.41272, -2.67321, -1.86753, -1.88032, 0, 0, 6.08442, 0.18676, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.14334, 0.79475, 1.17681, 0.79569, 1.32886, -0.47768, 2.45006, -3.72715, 0, 0, 0, 0, 0, 0, -0.10037, -0.1305, 3.89317, -6.02423, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.64269, 0.33493, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.11785, 0.81173, -1.08328, 0.81258, -0.0383, 1.38184, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.36867, 1.28838, 1.36867, 1.28838, 0, 0, 0, 0, 1.09759, 1.47886, 1.09759, 1.47886, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.5451, -1.47363, 1.48418, -1.4742, -0.22407, -2.11688, 0, 0, 0, 0, 0, 0, -1.47297, 1.23028, -1.42076, 1.23216, 0.0778, 1.91173], "curve": 0.318, "c2": 0.28, "c3": 0.656, "c4": 0.63}, {"time": 1.9, "offset": 30, "vertices": [1.58238, -2.53468, 1.4774, -2.53558, -1.04178, -2.76132, -3.46249, -6.66583, -3.74061, -6.67083, -7.39878, -1.01694, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.80328, 0.58328, -0.02752, 0.99319, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.08588, -1.59725, 0.04886, -2.68214, 1.19596, 1.2675, 1.24923, 1.26874, 1.7357, -0.24761, -2.53097, 1.83875, -2.47487, 1.83953, -0.08719, 3.12956, -1.03197, 0.74946, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.06045, 0.53658, -1.03729, 0.53756, -0.22099, 1.17515, -0.47245, 0.57554, -0.44758, 0.57657, 0.16886, 0.71646, 0.30445, -1.97196, 0.22193, -1.97352, -1.37763, -1.38707, 0.30445, -1.97196, -1.37763, -1.38707, 0, 0, 4.48833, 0.13777, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.84341, 0.58627, 0.8681, 0.58696, 0.98027, -0.35237, 1.80735, -2.74942, 0, 0, 0, 0, 0, 0, -0.07404, -0.09627, 2.8719, -4.44393, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.31592, 1.71422, 0.10697, 4.18044, 1.36041, 7.49406, -2.16138, 2.59839, 0, 0, 2.16907, -1.60512, -0.80991, -2.33984, 0, 0, 0, 0, 0, 0, -0.82461, 0.59879, -0.79911, 0.59942, -0.02825, 1.01935, -4.93079, 2.96042, -3.2258, 6.7822, -2.08832, 4.71928, 0, 0, -0.04306, -4.10591, 2.34561, -3.19986, 0, 0, -2.33021, 7.45677, 0.76275, 6.97064, 0, 0, 0, 0, -1.17443, 8.65219, 1.99665, 7.75822, 0.19763, -2.88411, 2.79901, -3.52499, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.13979, -1.08706, 1.09484, -1.08748, -0.16529, -1.56157, 0, 0, 0, 0, 0, 0, -1.08657, 0.90755, -1.04806, 0.90893, 0.05739, 1.41024], "curve": 0.333, "c2": 0.33, "c3": 0.672, "c4": 0.68}, {"time": 2.1667, "offset": 30, "vertices": [0.89183, -1.42855, 0.83266, -1.42905, -0.58715, -1.55628, -1.95146, -3.75686, -2.10821, -3.75968, -4.16995, -0.57315, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.45273, 0.32873, -0.01551, 0.55976, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.1756, -0.90021, 0.02754, -1.51165, 0.67405, 0.71436, 0.70406, 0.71506, 0.97824, -0.13955, -0.45435, 0.33002, -0.4405, 0.33033, -0.01563, 0.56192, -0.58162, 0.42239, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.59767, 0.30242, -0.58461, 0.30297, -0.12455, 0.66231, -0.26627, 0.32437, -0.25226, 0.32495, 0.09517, 0.40379, 0.17159, -1.1114, 0.12508, -1.11228, -0.77643, -0.78175, 0.17159, -1.1114, -0.77643, -0.78175, 0, 0, 2.52962, 0.07765, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.47535, 0.33042, 0.48926, 0.33081, 0.55248, -0.1986, 1.01862, -1.54958, 0, 0, 0, 0, 0, 0, -0.04173, -0.05426, 1.6186, -2.5046, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.2672, 0.13925, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.46475, 0.33748, -0.45038, 0.33784, -0.01592, 0.5745, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.56903, 0.53565, 0.56903, 0.53565, 0, 0, 0, 0, 0.45633, 0.61484, 0.45633, 0.61484, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.64238, -0.61267, 0.61705, -0.6129, -0.09316, -0.8801, 0, 0, 0, 0, 0, 0, -0.61239, 0.51149, -0.59069, 0.51228, 0.03235, 0.79481], "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 2.6667}]}, "s6": {"s6": [{"offset": 10, "vertices": [3.17508, 12.46196, 6.03729, -11.35484, 2.66853, -12.58005, 5.20566, 20.43136, 9.89809, -18.61626, 4.37489, -20.62486, 3.66663, 14.39038, 6.97153, -13.11207, 3.08153, -14.52647], "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 4, "vertices": [-0.84396, -3.31187, -1.21342, 3.19498, -0.28427, 3.40591, 6.49487, -2.64906, -6.83748, -1.56612, -7.00374, 0.3823, 5.76701, 14.51054, 3.62209, -15.18874, -0.71097, -15.59801, 9.11761, 9.97561, -1.72675, -13.40428, -5.35915, -12.40634, 1.17892, 3.89458, 0.15779, -4.06596, -3.04259, -2.68658, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.61749, -1.32998, -6.80609, -4.14925, -15.80534, -0.46769, -9.51844, 5.34453, -13.35006, 9.72358, 5.3338, -15.63065, -13.35006, 9.72358, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.87197, -1.24133, -4.70019, -1.78503, -5.01018, -0.41813, 0, 0, 0, 0, 0, 0, -0.16971, -0.06439, -0.18045, -0.01476, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -7.13938, -5.94661, -7.13938, -5.94661, 9.25905, 0.7617, -7.13938, -5.94661, 9.25905, 0.7617, -7.13938, -5.94661], "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "offset": 10, "vertices": [3.17508, 12.46196, 6.03729, -11.35484, 2.66853, -12.58005, 5.20566, 20.43136, 9.89809, -18.61626, 4.37489, -20.62486, 3.66663, 14.39038, 6.97153, -13.11207, 3.08153, -14.52647]}]}, "st": {"st": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 4, "vertices": [2.78534, 4.60161, -0.9502, 5.29482, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.32373, -3.72094, -2.32373, -3.72077, 0.71513, -4.32805, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.58651, 14.74967, -10.18753, 10.68262, -0.58651, 14.74967, -10.18753, 10.68262, 5.48685, 12.78247, -4.32941, 13.21986, -14.44643, -9.11397, -14.44638, -9.1138, -4.81918, -16.38683, -0.15503, 0.09942, -0.15486, 0.09995, -0.18196, -0.0265, 0.46295, 0.13874, 0.46323, 0.13935, 0.25589, 0.41151, -3.97382, 3.90627, -3.97351, 3.90656, -5.56374, 0.30612, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -8.9767, -1.69003, -8.97641, -1.68983, -5.62039, -7.20026, -8.9767, -1.69003, -8.97641, -1.68983, -5.62039, -7.20026, -8.97641, -1.68983, -5.62039, -7.20026, -5.97792, -1.89519, -5.97794, -1.89511, -3.23396, -5.37268, -5.97792, -1.89519, -5.97794, -1.89511, -3.23396, -5.37268, -5.97792, -1.89519, -5.97794, -1.89511, -3.23396, -5.37268, 1.76553, 0.44957, 1.76587, 0.4506, 1.02846, 1.5067, 0, 0, 0, 0, 0, 0, -9.01582, 2.9203, -9.01579, 2.92036, -8.69638, -3.76634, -6.44678, -0.97701, -6.44676, -0.97696, -4.19273, -4.99353, -1.76606, -0.44999, -1.76576, -0.44899, -1.02821, -1.50227, -0.67403, -2.08075, -0.67415, -2.08065, 0.86923, -2.0067, -2.47104, -7.62878, -2.47105, -7.62874, 3.18694, -7.35841, -3.2048, 7.4093, -3.32709, 7.35498, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.7525, 7.14008, 2.56363, -6.78568, 5.30075, -4.95349, -1.08398, -8.07813, -4.54398, 6.85617, -4.65695, 6.77972, 0, 0, 0, 0, 0, 0, 0, 0, -1.70984, 11.2815, 4.55092, -10.59384, -2.49651, -0.83014, 1.32428, 2.24924, 1.28552, 2.26425, 1.58447, 2.07318, 12.78629, -5.69836, 13.1954, -4.6796, 13.99062, 0.53922, 13.97888, 0.76846, 14.05731, -9.01648, 4.47229, 0.69915, -2.89226, -3.4155, -2.83707, -3.46997, -3.27023, -3.05559, 13.59726, -9.25504, 16.05743, -3.56247, 16.11322, -3.29855, 0, 0, 0, 0, 0, 0, -12.48345, 4.04351, 12.05179, 4.97949, 11.96786, 5.17749, 12.55502, 3.52419, -0.25653, 8.8028, 0.18994, 8.8035, -3.6304, 8.02386, -2.51733, 8.43883, 12.66025, -15.54443, 11.8522, -16.16855, 17.51482, -9.75192, 17.67264, -9.4631, 16.24515, -11.74962, -0.3595, 5.65793, -0.07391, 5.66739, -2.42728, 5.13077, -2.51379, 5.08298, -1.80099, 5.3764, -6.78311, 0.00646, 5.09666, 4.37955, 5.02213, 4.45673, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11.71887, -22.23117, 10.57233, -22.79892, 19.38162, -15.99606, 17.06161, -18.45285, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10.00626, -25.75528, 20.14777, -18.90698, 17.43015, -21.4402], "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s1": {"s1": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 32, "vertices": [-12.49506, -20.14153, 19.55704, -13.39177, 21.57138, 9.82211, -10.187, -6.8741, 6.40733, -10.48715, 12.28864, 0.06558, -5.40359, -3.44925, 3.20186, -5.55411, 6.40872, -0.12927, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.50276, 9.96518, -0.16638, 9.97662, -8.62735, 5.01343, 0.50276, 9.96518, -0.16638, 9.97662, -8.62735, 5.01343, 0.50276, 9.96518, -0.16638, 9.97662, -8.62735, 5.01343, -0.30244, 9.97327, 0.50276, 9.96518, -8.62735, 5.01343, -0.30244, 9.97327, 0.50276, 9.96518, -0.22682, 7.47906, 0.37705, 7.47299, -0.15109, 4.98161, 0.25111, 4.97758, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11.358, -4.64734, 10.94682, -5.54784, 4.09477, -11.7186, -11.58328, -4.46222, 11.358, -4.64734, 10.94682, -5.54784, 8.81731, -2.85596, -2.57588, -8.90314, 11.358, -4.64734, 10.94682, -5.54784, 11.29401, -4.80092, 11.8364, 0.3586, 0.73207, -11.81929, 0, 0, 13.95155, -7.68004, -7.23604, -14.1868, 13.28714, -8.77982, 13.84572, -7.86913, 0.61656, -9.95547, 9.97317, 0.1664, 5.01147, 8.62412, -0.39551, -3.75969, 3.73804, -0.56532, 2.41461, 2.90805, 13.95155, -7.68004, -7.23604, -14.1868, 13.28714, -8.77982, 13.84572, -7.86913, 13.89191, 7.78669, 0.61656, -9.95547, 9.97317, 0.1664, 5.01147, 8.62412, -8.67169, -11.12985, 10.72709, -9.1656, 13.38956, 4.44688, -7.23604, -14.1868, 13.84572, -7.86913, 13.89191, 7.78669, 0.61656, -9.95547, 9.97317, 0.1664, 5.01147, 8.62412, -6.25385, -9.74677, 9.45458, -6.68785, 10.61081, 4.63795, -7.23604, -14.1868, 13.84572, -7.86913, 13.89191, 7.78669, 0.61656, -9.95547, 9.97317, 0.1664, 5.01147, 8.62412, -6.92059, -9.16378, 8.8421, -7.32761, 10.84186, 3.78291, 6.96905, -10.33742, 7.64627, -9.84666, 12.38092, 1.45725, -9.49136, -8.0829, 7.64627, -9.84666, 12.38092, 1.45725, -10.22804, -16.23795, 15.75975, -10.9509, 17.5191, 7.8327, 6.96905, -10.33742, 7.64627, -9.84666, 12.38092, 1.45725, -9.49136, -8.0829, 7.64627, -9.84666, 12.38092, 1.45725, -6.83983, -0.42341, 0.11427, -6.85197, 5.92493, -3.4433, 6.96905, -10.33742, 7.64627, -9.84666, 12.38092, 1.45725, -9.49136, -8.0829, 12.38092, 1.45725], "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "yq": {"yq": [{"offset": 44, "vertices": [-0.07216, 0.37881, -0.15411, 0.80914, 0, 0, 0, 0, -0.07216, 0.37882, -0.09749, 0.51226], "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "offset": 44, "vertices": [-0.39117, 2.05357, -0.83542, 4.38638, 0, 0, 0, 0, -0.39116, 2.0536, -0.52847, 2.77699], "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "offset": 44, "vertices": [-0.07216, 0.37881, -0.15411, 0.80914, 0, 0, 0, 0, -0.07216, 0.37882, -0.09749, 0.51226]}]}}}}, "animation2": {"slots": {"bg": {"attachment": [{"name": null}]}}, "bones": {"st2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s2": {"rotate": [{"angle": 0.89, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 4.8, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 0.89}]}, "s1": {"rotate": [{"angle": 0.15, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 2.4, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": 0.15}]}, "st4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "t1": {"rotate": [{"angle": 0.66, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 3.6, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 0.66}]}, "t2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}], "shear": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s11": {"rotate": [{"angle": 3.64, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 19.73, "curve": "stepped"}, {"time": 1.8333, "angle": 19.73, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 3.64}]}, "s13": {"rotate": [{"angle": 13.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 27.07, "curve": "stepped"}, {"time": 2.3333, "angle": 27.07, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 13.53}]}, "s15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 2.52, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 6.52, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "t3": {"shear": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "t16": {"shear": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 2.4, "y": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "t17": {"shear": [{"x": 0.15, "y": -0.15, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 2.4, "y": -2.4, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "x": 0.15, "y": -0.15}]}, "t18": {"shear": [{"x": 0.44, "y": -0.44, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 2.4, "y": -2.4, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 0.44, "y": -0.44}]}, "t23": {"rotate": [{"angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 0.78, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 7.08}]}, "t24": {"rotate": [{"angle": 6.43, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.2333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": 0.78, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 2.6667, "angle": 6.43}]}, "t25": {"rotate": [{"angle": 5.16, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.4667, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 0.78, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "angle": 5.16}]}, "t26": {"rotate": [{"angle": 3.72, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 0.7, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": 0.78, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 2.6667, "angle": 3.72}]}, "t27": {"rotate": [{"angle": 2.31, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.9333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 0.78, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "angle": 2.31}]}, "t19": {"rotate": [{"angle": 1.91, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.2333, "angle": 2.27, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": -1.2, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 2.6667, "angle": 1.91}]}, "t20": {"rotate": [{"angle": 0.89, "curve": 0.344, "c2": 0.37, "c3": 0.682, "c4": 0.72}, {"time": 0.2333, "angle": 1.63, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5667, "angle": 2.27, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": -1.2, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 2.6667, "angle": 0.89}]}, "t21": {"rotate": [{"angle": -0.25, "curve": 0.324, "c2": 0.3, "c3": 0.662, "c4": 0.65}, {"time": 0.2333, "angle": 0.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.9, "angle": 2.27, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": -1.2, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "angle": -0.25}]}, "t22": {"rotate": [{"angle": -1.11, "curve": 0.3, "c2": 0.17, "c3": 0.638, "c4": 0.53}, {"time": 0.2333, "angle": -0.56, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.2333, "angle": 2.27, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": -1.2, "curve": 0.311, "c3": 0.646, "c4": 0.35}, {"time": 2.6667, "angle": -1.11}]}, "t8": {"rotate": [{"angle": 4.6, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4, "angle": 6.21, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": -0.45, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.6667, "angle": 4.6}]}, "t9": {"rotate": [{"angle": 2.46, "curve": 0.34, "c2": 0.35, "c3": 0.687, "c4": 0.73}, {"time": 0.4, "angle": 4.98, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.7333, "angle": 6.21, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": -0.45, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 2.6667, "angle": 2.46}]}, "t10": {"rotate": [{"angle": 0.41, "curve": 0.306, "c2": 0.24, "c3": 0.653, "c4": 0.62}, {"time": 0.4, "angle": 2.88, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.0667, "angle": 6.21, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "angle": -0.45, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "angle": 0.41}]}, "st": {"shear": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "st8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.27, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "st9": {"rotate": [{"angle": -0.6, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -3.27, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.6}]}, "st10": {"rotate": [{"angle": -1.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -3.27, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.63}]}, "st5": {"rotate": [{"angle": 0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 0.78, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 0.14}]}, "st6": {"rotate": [{"angle": 0.39, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.3333, "angle": 0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 0.78, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 0.39}]}, "st7": {"rotate": [{"angle": 0.63, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 0.6667, "angle": 0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 0.78, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 0.63}]}, "t11": {"rotate": [{"angle": -1.44, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -7.78, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -1.44}]}, "t12": {"rotate": [{"angle": -3.86, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.3333, "angle": -1.44, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -7.78, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -3.86}]}, "t13": {"rotate": [{"angle": -6.33, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 0.6667, "angle": -1.44, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -7.78, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -6.33}]}, "st3": {"translate": [{"x": 0.41, "y": -0.83, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "x": 4, "y": -8, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 2.6667, "x": 0.41, "y": -0.83}]}, "t14": {"rotate": [{"angle": -2.05, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -11.12, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -2.05}]}, "t15": {"rotate": [{"angle": -5.52, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.3333, "angle": -2.05, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -11.12, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -5.52}]}, "s16": {"rotate": [{"angle": 7.2, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 14.4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 7.2}]}, "s9": {"rotate": [{"angle": -6.86, "curve": 0.343, "c2": 0.37, "c3": 0.715, "c4": 0.83}, {"time": 0.6667, "angle": -0.65, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -10.4, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": -6.86}]}, "s21": {"rotate": [{"angle": -8.47, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 0.6667, "angle": -1.92, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -10.4, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -8.47}]}, "s23": {"rotate": [{"angle": -9.77, "curve": 0.285, "c2": 0.17, "c3": 0.657, "c4": 0.63}, {"time": 0.6667, "angle": -3.49, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -10.4, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.6667, "angle": -9.77}]}, "s17": {"rotate": [{"angle": -5.2, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -10.4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -5.2}]}, "s18": {"rotate": [{"angle": -28.25, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 1.1667, "angle": -1.77, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -28.25}]}, "s19": {"rotate": [{"angle": -24.62, "curve": 0.346, "c2": 0.39, "c3": 0.68, "c4": 0.73}, {"time": 0.1, "angle": -26.47, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.2667, "angle": -28.25, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.2667, "angle": -5.21, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.6, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.6667, "angle": -24.62}]}, "s22": {"rotate": [{"angle": -17.92, "curve": 0.344, "c2": 0.37, "c3": 0.681, "c4": 0.71}, {"time": 0.2, "angle": -23.04, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5333, "angle": -28.25, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 1.3667, "angle": -9.48, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1.8667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": -17.92}]}, "s24": {"rotate": [{"angle": -10.43, "curve": 0.331, "c2": 0.33, "c3": 0.672, "c4": 0.68}, {"time": 0.3, "angle": -18.76, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.8, "angle": -28.25, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.4667, "angle": -14.12, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.1333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "angle": -10.43}]}, "s3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.77, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s4": {"rotate": [{"angle": 0.33, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 1.77, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 0.33}]}, "s5": {"rotate": [{"angle": 0.88, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 1.77, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 0.88}]}, "t6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "target2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "st12": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.92, "y": -3.51, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}}, "deform": {"default": {"t1": {"t1": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 30, "vertices": [2.63029, -4.21324, 2.45578, -4.21474, -1.73169, -4.58997, -5.75548, -11.08018, -6.21777, -11.0885, -12.29852, -1.6904, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.33524, 0.96954, -0.04575, 1.65091, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.46722, -2.655, 0.08121, -4.45834, 1.98798, 2.10689, 2.07651, 2.10895, 2.88515, -0.41159, -1.34003, 0.97334, -1.29916, 0.97426, -0.04609, 1.65729, -1.71538, 1.24577, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.76273, 0.89192, -1.72421, 0.89355, -0.36734, 1.95337, -0.78532, 0.95668, -0.74399, 0.95839, 0.28069, 1.19092, 0.50607, -3.27786, 0.3689, -3.28046, -2.28994, -2.30563, 0.50607, -3.27786, -2.28994, -2.30563, 0, 0, 7.46066, 0.229, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.40195, 0.97452, 1.44299, 0.97566, 1.62944, -0.58572, 3.00424, -4.57019, 0, 0, 0, 0, 0, 0, -0.12308, -0.16002, 4.77377, -7.38686, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.78806, 0.41069, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.3707, 0.99533, -1.32831, 0.99638, -0.04696, 1.6944, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.67825, 1.5798, 1.67825, 1.5798, 0, 0, 0, 0, 1.34586, 1.81337, 1.34586, 1.81337, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.89459, -1.80695, 1.81989, -1.80765, -0.27476, -2.5957, 0, 0, 0, 0, 0, 0, -1.80614, 1.50856, -1.74213, 1.51086, 0.0954, 2.34415], "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.6667, "offset": 30, "vertices": [2.14509, -3.43604, 2.00277, -3.43726, -1.41225, -3.74327, -4.69379, -9.03627, -5.07081, -9.04305, -10.02986, -1.37858, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.08893, 0.7907, -0.03731, 1.34637, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.82764, -2.16524, 0.06623, -3.63593, 1.62126, 1.71824, 1.69346, 1.71992, 2.35294, -0.33567, -1.09284, 0.79379, -1.05951, 0.79454, -0.03759, 1.35157, -1.39895, 1.01597, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.43756, 0.72739, -1.40615, 0.72872, -0.29958, 1.59304, -0.64046, 0.78021, -0.60675, 0.7816, 0.22891, 0.97123, 0.41272, -2.67321, 0.30085, -2.67532, -1.86753, -1.88032, 0.41272, -2.67321, -1.86753, -1.88032, 0, 0, 6.08442, 0.18676, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.14334, 0.79475, 1.17681, 0.79569, 1.32886, -0.47768, 2.45006, -3.72715, 0, 0, 0, 0, 0, 0, -0.10037, -0.1305, 3.89317, -6.02423, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.64269, 0.33493, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.11785, 0.81173, -1.08328, 0.81258, -0.0383, 1.38184, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.36867, 1.28838, 1.36867, 1.28838, 0, 0, 0, 0, 1.09759, 1.47886, 1.09759, 1.47886, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.5451, -1.47363, 1.48418, -1.4742, -0.22407, -2.11688, 0, 0, 0, 0, 0, 0, -1.47297, 1.23028, -1.42076, 1.23216, 0.0778, 1.91173], "curve": 0.318, "c2": 0.28, "c3": 0.656, "c4": 0.63}, {"time": 1.9, "offset": 30, "vertices": [1.58238, -2.53468, 1.4774, -2.53558, -1.04178, -2.76132, -3.46249, -6.66583, -3.74061, -6.67083, -7.39878, -1.01694, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.80328, 0.58328, -0.02752, 0.99319, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.08588, -1.59725, 0.04886, -2.68214, 1.19596, 1.2675, 1.24923, 1.26874, 1.7357, -0.24761, -2.53097, 1.83875, -2.47487, 1.83953, -0.08719, 3.12956, -1.03197, 0.74946, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.06045, 0.53658, -1.03729, 0.53756, -0.22099, 1.17515, -0.47245, 0.57554, -0.44758, 0.57657, 0.16886, 0.71646, 0.30445, -1.97196, 0.22193, -1.97352, -1.37763, -1.38707, 0.30445, -1.97196, -1.37763, -1.38707, 0, 0, 4.48833, 0.13777, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.84341, 0.58627, 0.8681, 0.58696, 0.98027, -0.35237, 1.80735, -2.74942, 0, 0, 0, 0, 0, 0, -0.07404, -0.09627, 2.8719, -4.44393, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.31592, 1.71422, 0.10697, 4.18044, 1.36041, 7.49406, -2.16138, 2.59839, 0, 0, 2.16907, -1.60512, -0.80991, -2.33984, 0, 0, 0, 0, 0, 0, -0.82461, 0.59879, -0.79911, 0.59942, -0.02825, 1.01935, -4.93079, 2.96042, -3.2258, 6.7822, -2.08832, 4.71928, 0, 0, -0.04306, -4.10591, 2.34561, -3.19986, 0, 0, -2.33021, 7.45677, 0.76275, 6.97064, 0, 0, 0, 0, -1.17443, 8.65219, 1.99665, 7.75822, 0.19763, -2.88411, 2.79901, -3.52499, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.13979, -1.08706, 1.09484, -1.08748, -0.16529, -1.56157, 0, 0, 0, 0, 0, 0, -1.08657, 0.90755, -1.04806, 0.90893, 0.05739, 1.41024], "curve": 0.333, "c2": 0.33, "c3": 0.672, "c4": 0.68}, {"time": 2.1667, "offset": 30, "vertices": [0.89183, -1.42855, 0.83266, -1.42905, -0.58715, -1.55628, -1.95146, -3.75686, -2.10821, -3.75968, -4.16995, -0.57315, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.45273, 0.32873, -0.01551, 0.55976, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.1756, -0.90021, 0.02754, -1.51165, 0.67405, 0.71436, 0.70406, 0.71506, 0.97824, -0.13955, -0.45435, 0.33002, -0.4405, 0.33033, -0.01563, 0.56192, -0.58162, 0.42239, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.59767, 0.30242, -0.58461, 0.30297, -0.12455, 0.66231, -0.26627, 0.32437, -0.25226, 0.32495, 0.09517, 0.40379, 0.17159, -1.1114, 0.12508, -1.11228, -0.77643, -0.78175, 0.17159, -1.1114, -0.77643, -0.78175, 0, 0, 2.52962, 0.07765, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.47535, 0.33042, 0.48926, 0.33081, 0.55248, -0.1986, 1.01862, -1.54958, 0, 0, 0, 0, 0, 0, -0.04173, -0.05426, 1.6186, -2.5046, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.2672, 0.13925, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.46475, 0.33748, -0.45038, 0.33784, -0.01592, 0.5745, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.56903, 0.53565, 0.56903, 0.53565, 0, 0, 0, 0, 0.45633, 0.61484, 0.45633, 0.61484, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.64238, -0.61267, 0.61705, -0.6129, -0.09316, -0.8801, 0, 0, 0, 0, 0, 0, -0.61239, 0.51149, -0.59069, 0.51228, 0.03235, 0.79481], "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 2.6667}]}, "s6": {"s6": [{"offset": 10, "vertices": [3.17508, 12.46196, 6.03729, -11.35484, 2.66853, -12.58005, 5.20566, 20.43136, 9.89809, -18.61626, 4.37489, -20.62486, 3.66663, 14.39038, 6.97153, -13.11207, 3.08153, -14.52647], "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 4, "vertices": [-0.84396, -3.31187, -1.21342, 3.19498, -0.28427, 3.40591, 6.49487, -2.64906, -6.83748, -1.56612, -7.00374, 0.3823, 5.76701, 14.51054, 3.62209, -15.18874, -0.71097, -15.59801, 9.11761, 9.97561, -1.72675, -13.40428, -5.35915, -12.40634, 1.17892, 3.89458, 0.15779, -4.06596, -3.04259, -2.68658, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.61749, -1.32998, -6.80609, -4.14925, -15.80534, -0.46769, -9.51844, 5.34453, -13.35006, 9.72358, 5.3338, -15.63065, -13.35006, 9.72358, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.87197, -1.24133, -4.70019, -1.78503, -5.01018, -0.41813, 0, 0, 0, 0, 0, 0, -0.16971, -0.06439, -0.18045, -0.01476, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -7.13938, -5.94661, -7.13938, -5.94661, 9.25905, 0.7617, -7.13938, -5.94661, 9.25905, 0.7617, -7.13938, -5.94661], "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "offset": 10, "vertices": [3.17508, 12.46196, 6.03729, -11.35484, 2.66853, -12.58005, 5.20566, 20.43136, 9.89809, -18.61626, 4.37489, -20.62486, 3.66663, 14.39038, 6.97153, -13.11207, 3.08153, -14.52647]}]}, "st": {"st": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 4, "vertices": [2.78534, 4.60161, -0.9502, 5.29482, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.32373, -3.72094, -2.32373, -3.72077, 0.71513, -4.32805, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.58651, 14.74967, -10.18753, 10.68262, -0.58651, 14.74967, -10.18753, 10.68262, 5.48685, 12.78247, -4.32941, 13.21986, -14.44643, -9.11397, -14.44638, -9.1138, -4.81918, -16.38683, -0.15503, 0.09942, -0.15486, 0.09995, -0.18196, -0.0265, 0.46295, 0.13874, 0.46323, 0.13935, 0.25589, 0.41151, -3.97382, 3.90627, -3.97351, 3.90656, -5.56374, 0.30612, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -8.9767, -1.69003, -8.97641, -1.68983, -5.62039, -7.20026, -8.9767, -1.69003, -8.97641, -1.68983, -5.62039, -7.20026, -8.97641, -1.68983, -5.62039, -7.20026, -5.97792, -1.89519, -5.97794, -1.89511, -3.23396, -5.37268, -5.97792, -1.89519, -5.97794, -1.89511, -3.23396, -5.37268, -5.97792, -1.89519, -5.97794, -1.89511, -3.23396, -5.37268, 1.76553, 0.44957, 1.76587, 0.4506, 1.02846, 1.5067, 0, 0, 0, 0, 0, 0, -9.01582, 2.9203, -9.01579, 2.92036, -8.69638, -3.76634, -6.44678, -0.97701, -6.44676, -0.97696, -4.19273, -4.99353, -1.76606, -0.44999, -1.76576, -0.44899, -1.02821, -1.50227, -0.67403, -2.08075, -0.67415, -2.08065, 0.86923, -2.0067, -2.47104, -7.62878, -2.47105, -7.62874, 3.18694, -7.35841, -3.2048, 7.4093, -3.32709, 7.35498, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.7525, 7.14008, 2.56363, -6.78568, 5.30075, -4.95349, -1.08398, -8.07813, -4.54398, 6.85617, -4.65695, 6.77972, 0, 0, 0, 0, 0, 0, 0, 0, -1.70984, 11.2815, 4.55092, -10.59384, -2.49651, -0.83014, 1.32428, 2.24924, 1.28552, 2.26425, 1.58447, 2.07318, 12.78629, -5.69836, 13.1954, -4.6796, 13.99062, 0.53922, 13.97888, 0.76846, 14.05731, -9.01648, 4.47229, 0.69915, -2.89226, -3.4155, -2.83707, -3.46997, -3.27023, -3.05559, 13.59726, -9.25504, 16.05743, -3.56247, 16.11322, -3.29855, 0, 0, 0, 0, 0, 0, -12.48345, 4.04351, 12.05179, 4.97949, 11.96786, 5.17749, 12.55502, 3.52419, -0.25653, 8.8028, 0.18994, 8.8035, -3.6304, 8.02386, -2.51733, 8.43883, 12.66025, -15.54443, 11.8522, -16.16855, 17.51482, -9.75192, 17.67264, -9.4631, 16.24515, -11.74962, -0.3595, 5.65793, -0.07391, 5.66739, -2.42728, 5.13077, -2.51379, 5.08298, -1.80099, 5.3764, -6.78311, 0.00646, 5.09666, 4.37955, 5.02213, 4.45673, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11.71887, -22.23117, 10.57233, -22.79892, 19.38162, -15.99606, 17.06161, -18.45285, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10.00626, -25.75528, 20.14777, -18.90698, 17.43015, -21.4402], "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s1": {"s1": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 32, "vertices": [-12.49506, -20.14153, 19.55704, -13.39177, 21.57138, 9.82211, -10.187, -6.8741, 6.40733, -10.48715, 12.28864, 0.06558, -5.40359, -3.44925, 3.20186, -5.55411, 6.40872, -0.12927, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.50276, 9.96518, -0.16638, 9.97662, -8.62735, 5.01343, 0.50276, 9.96518, -0.16638, 9.97662, -8.62735, 5.01343, 0.50276, 9.96518, -0.16638, 9.97662, -8.62735, 5.01343, -0.30244, 9.97327, 0.50276, 9.96518, -8.62735, 5.01343, -0.30244, 9.97327, 0.50276, 9.96518, -0.22682, 7.47906, 0.37705, 7.47299, -0.15109, 4.98161, 0.25111, 4.97758, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11.358, -4.64734, 10.94682, -5.54784, 4.09477, -11.7186, -11.58328, -4.46222, 11.358, -4.64734, 10.94682, -5.54784, 8.81731, -2.85596, -2.57588, -8.90314, 11.358, -4.64734, 10.94682, -5.54784, 11.29401, -4.80092, 11.8364, 0.3586, 0.73207, -11.81929, 0, 0, 13.95155, -7.68004, -7.23604, -14.1868, 13.28714, -8.77982, 13.84572, -7.86913, 0.61656, -9.95547, 9.97317, 0.1664, 5.01147, 8.62412, -0.39551, -3.75969, 3.73804, -0.56532, 2.41461, 2.90805, 13.95155, -7.68004, -7.23604, -14.1868, 13.28714, -8.77982, 13.84572, -7.86913, 13.89191, 7.78669, 0.61656, -9.95547, 9.97317, 0.1664, 5.01147, 8.62412, -8.67169, -11.12985, 10.72709, -9.1656, 13.38956, 4.44688, -7.23604, -14.1868, 13.84572, -7.86913, 13.89191, 7.78669, 0.61656, -9.95547, 9.97317, 0.1664, 5.01147, 8.62412, -6.25385, -9.74677, 9.45458, -6.68785, 10.61081, 4.63795, -7.23604, -14.1868, 13.84572, -7.86913, 13.89191, 7.78669, 0.61656, -9.95547, 9.97317, 0.1664, 5.01147, 8.62412, -6.92059, -9.16378, 8.8421, -7.32761, 10.84186, 3.78291, 6.96905, -10.33742, 7.64627, -9.84666, 12.38092, 1.45725, -9.49136, -8.0829, 7.64627, -9.84666, 12.38092, 1.45725, -10.22804, -16.23795, 15.75975, -10.9509, 17.5191, 7.8327, 6.96905, -10.33742, 7.64627, -9.84666, 12.38092, 1.45725, -9.49136, -8.0829, 7.64627, -9.84666, 12.38092, 1.45725, -6.83983, -0.42341, 0.11427, -6.85197, 5.92493, -3.4433, 6.96905, -10.33742, 7.64627, -9.84666, 12.38092, 1.45725, -9.49136, -8.0829, 12.38092, 1.45725], "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "yq": {"yq": [{"offset": 44, "vertices": [-0.07216, 0.37881, -0.15411, 0.80914, 0, 0, 0, 0, -0.07216, 0.37882, -0.09749, 0.51226], "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "offset": 44, "vertices": [-0.39117, 2.05357, -0.83542, 4.38638, 0, 0, 0, 0, -0.39116, 2.0536, -0.52847, 2.77699], "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "offset": 44, "vertices": [-0.07216, 0.37881, -0.15411, 0.80914, 0, 0, 0, 0, -0.07216, 0.37882, -0.09749, 0.51226]}]}}}}}}