import { _decorator, Label, Node, v3 } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { AzstModule } from "../../../module/azst/AzstModule";
import MsgMgr from "../../../lib/event/MsgMgr";
import { AzstMsgEnum } from "../../../module/azst/AzstConfig";
import ToolExt from "../../common/ToolExt";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { CompeteLogMessage, CompeteReplayMessage } from "../../net/protocol/Compete";
import { PlayerSimpleMessage } from "../../net/protocol/Player";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { JsonMgr } from "../../mgr/JsonMgr";
import Formate from "../../../lib/utils/Formate";
import MsgEnum from "../../event/MsgEnum";
import { FightData } from "../../fight/FightDefine";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { Sleep } from "../../GameDefine";
import { dtTime } from "../../BoutStartUp";
import FmUtils from "../../../lib/utils/FmUtils";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;
const item_1082: number = 1082;
const maxLog: number = 50;
@ccclass("UIAzstLog")
export class UIAzstLog extends UINode {
  protected _openAct: boolean = true; //打开动作
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_AZST}?prefab/ui/UIAzstLog`;
  }

  protected onRegEvent(): void {
    MsgMgr.on(MsgEnum.ON_EXIST_ITEM_CHANGE, this.setPkNeedItemNum, this);
    MsgMgr.on(AzstMsgEnum.LOG_REFRESH, this.refresh_log_list, this);
  }

  protected onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_EXIST_ITEM_CHANGE, this.setPkNeedItemNum, this);
    MsgMgr.off(AzstMsgEnum.LOG_REFRESH, this.refresh_log_list, this);
  }

  protected async onEvtShow() {
    this.setPkNeedItemNum();

    await Sleep(dtTime);
    AzstModule.api.getLogMessage(() => {});
  }

  /**设置当前的挑战券数量 */
  private setPkNeedItemNum(id?: number) {
    if (id && id != item_1082) {
      return;
    }
    let myItem = PlayerModule.data.getItemNum(item_1082);
    this["needItemLab"].getComponent(Label).string =
      Formate.format(myItem) + "/" + AzstModule.data.azstMessage.ticketSize;
  }

  private async refresh_log_list() {
    let logMessage = AzstModule.data.logMessage;
    if (logMessage.length < this.getNode("content").children.length) {
      let num = this.getNode("content").children.length - logMessage.length;
      for (let i = 0; i < num; i++) {
        let item: Node = this.getNode("content").children[0];
        item.removeFromParent();
        item.destroy();
      }
    }

    for (let i = 0; i < logMessage.length; i++) {
      await Sleep(dtTime);
      let node = null;
      node = this.getNode("content").children[i];
      if (!node) {
        node = ToolExt.clone(this["logMessage"], this);
        node.setPosition(v3(0, 0, 0));
        this.getNode("content").addChild(node);
      }
      node.active = true;
      let info = logMessage[i];
      this.setMessage(node, info);
    }
  }

  private setMessage(node: Node, info: CompeteLogMessage) {
    node["win_state"].active = info.win;
    node["lose_state"].active = !info.win;
    node["losePoint"].getComponent(Label).string = `-${info.losePoint}`;

    let simpleMessage: PlayerSimpleMessage = info.simpleMessage;

    node["playerName"].getComponent(Label).string = simpleMessage.nickname;
    node["playerPower"].getComponent(Label).string = Formate.format(simpleMessage.power);

    node["logTime"].getComponent(Label).string = ToolExt.formatTimeAgo(info.timeStamp);

    node["btn_log_pk"]["info"] = info;
    if (info.revenge == true) {
      node["btn_log_pk"].active = false;
    }

    let param = ToolExt.newPlayerBaseMessage();
    param.avatarList = info.simpleMessage.avatarList;
    param.userId = info.simpleMessage.userId;
    param.nickname = info.simpleMessage.nickname;
    param.sex = info.simpleMessage.sex;
    FmUtils.setHeaderNode(node["btn_header"], param);
  }

  private on_click_btn_close() {
    // 关闭窗口 effect_guanbiyinxiao
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  private on_click_btn_add() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    UIMgr.instance.showDialog("UIItemFetch", {
      itemId: item_1082,
    });
  }

  private on_click_btn_log_pk(event) {
    AudioMgr.instance.playEffect(1091);
    TipsMgr.setEnableTouch(false, 3);
    let myItem = PlayerModule.data.getItemNum(item_1082);
    if (myItem < 1) {
      UIMgr.instance.showDialog("UIItemFetch", {
        itemId: item_1082,
      });
      return;
    }
    let node = event.node;
    let logId = node["info"].id;
    let userInfo = node["info"].simpleMessage;
    AzstModule.api.revenge(logId, (res: CompeteReplayMessage) => {
      let data = JSON.parse(res.replay);
      log.log("战斗录像====", data);
      let args: FightData = {
        fightData: data,
        win: res.win,
        userInfo: userInfo,
        logId: logId,
      };
      UIMgr.instance.showDialog("UIAzstLogFight", { data: args }, null, () => {
        TipsMgr.setEnableTouch(true);
      });
    });
  }
}
