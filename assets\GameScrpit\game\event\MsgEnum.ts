import { _decorator } from "cc";
const { ccclass, property } = _decorator;

@ccclass("MsgEnum")
export default class MsgEnum {
  public static ON_ERROR: string = "ON_ERROR";

  // 系统(模块/功能)开启权限
  public static ON_RIGHT_UPDATE: string = "ON_RIGHT_UPDATE";

  // 用户点击操作通知
  public static ON_USER_CLICK: string = "ON_USER_CLICK";

  // 引导结束
  public static ON_GUIDE_FINGER_END: string = "ON_GUIDE_FINGER_END";

  // 下一步引导
  public static ON_GUIDE_NEXT: string = "ON_GUIDE_NEXT";

  // 游戏启动完成
  public static ON_GAME_START: string = "ON_GAME_START";

  /** 系统模块开启 */
  public static ON_SYSTEM_OPEN: string = "ON_SYSTEM_OPEN";

  /**获得奖励界面消息事件 */
  public static ON_GET_AWARD: string = "ON_GET_AWARD";
  /**关闭奖励界面消息 */
  public static ON_CLOSE_GET_AWARD: string = "ON_CLOSE_GET_AWARD";

  /**消息事件回调注册监听 */
  public static ON_SOCKET_MSGID = "ON_SOCKET_MSGID";

  /**UIPlayer 获取玩家的战斗属性和总战力 */
  public static ON_UIPLAYER_GETPOWER: string = "ON_UIPLAYER_GETPOWER";

  /**UIPlayer 展示计算各模块的战力及赚速信息 */
  public static ON_UIPLAYER_NEWINFO: string = "ON_UIPLAYER_NEWINFO";

  /**UIPlayer 主动控制UI界面显示 */
  public static ON_UI_TOP_SHOW: string = "ON_UI_TOP_SHOW";

  /** 神迹触发*/
  public static ON_TRIGGER_SHENJI: string = "ON_TRIGGER_SHENJI";

  /** =========================城市信息更新 ===================== */

  /** 城市更新 */
  public static ON_CITY_UPDATE: string = "ON_CITY_UPDATE";

  /** 城市号召宝箱更新 */
  public static ON_CITY_HAO_ZHAO_UPDATE: string = "ON_CITY_HAO_ZHAO_UPDATE";

  /** 城市号召宝箱动画 */
  public static ON_CITY_HAO_ZHAO_BOX_ADD: string = "ON_CITY_HAO_ZHAO_BOX_ADD";

  /** 城市升级 */
  public static ON_CITY_LEVEL_UP: string = "ON_CITY_LEVEL_UP";

  /** 城市焦点 */
  public static ON_CITY_FOCOUS: string = "ON_CITY_FOCOUS";

  /** 小偷事件放大 */
  public static ON_THIEF_FOCOUS: string = "ON_THIEF_FOCOUS";

  /** 小偷事件返回 */
  public static ON_THIEF_UNFOCOUS: string = "ON_THIEF_UNFOCOUS";

  /** 城市取消焦点 */
  public static ON_CITY_UN_FOCOUS: string = "ON_CITY_UN_FOCOUS";

  public static ON_CITY_TRIM_UPDATE: string = "ON_CITY_TRIM_UPDATE";

  /**水晶数据更新，发起通知 */
  public static ON_ENERGYFACTORY_UPDATE: string = "ON_ENERGYFACTORY_UPDATE";

  /**水晶变化动画 */
  public static ON_ENERGYFACTORY_CHANGE_ANI: string = "ON_ENERGYFACTORY_CHANGE_ANI";

  /**关卡通关，通知对应据点更新状态 */
  public static ON_CHAPTER_PASS: string = "ON_CHAPTER_PASS";

  /** 界面存在时道具变化，进行刷新 */
  public static ON_EXIST_ITEM_CHANGE: string = "ON_EXIST_ITEM_CHANGE";

  /**导航栏的界面点击返回按钮，统一回到朝歌城 */
  public static ON_UINAVIGATE_MAIN_CLOSE_GO_ZHAOGE = "ON_UINAVIGATE_MAIN_CLOSE_GO_ZHAOGE";

  /** 玩家升级通知 */
  public static ON_PLAYER_LEVEL_UP: string = "ON_PLAYER_LEVEL_UP";

  /**玩家的大数信息更新了，发起通知更新 */
  public static ON_PLAYER_NUM_UPDATE: string = "ON_PLAYER_NUM_UPDATE";

  /**玩家的气运更新了（频繁更新），发起通知更新 */
  public static ON_PLAYER_ENERGY_UPDATE: string = "ON_PLAYER_ENERGY_UPDATE";

  /** 主线任务数据更新提醒  */
  public static ON_MAINTASK_UPDATE: string = "ON_MAINTASK_UPDATE";

  /** 点击水晶生成动效，通知 */
  public static ON_ENERGYFACTORY_FLY: string = "ON_ENERGYFACTORY_FLY";

  /** 关闭GOOD弹窗后，通知回调，处理剩余GOOD弹窗 */
  public static ON_GOODSAWARD_CLOSE: string = "ON_GOODSAWARD_CLOSE";

  /**切换坐骑 */
  public static ON_HORSE_UPDATE = "ON_HORSE_UPDATE";

  /** =======================徒弟开始========================== */
  /**弟子信息改变*/
  public static ON_PUPIL_UPDATE: string = "ON_PUPIL_UPDATE";

  /** 获取到请求的弟子结伴信息 */
  public static ON_PUPIL_MARRY_SEND: string = "ON_PUPIL_MARRY_SEND";

  /** =======================徒弟结束========================== */

  /*驿站加速消耗传递 */
  public static ON_POST_SPEED_COST: string = "ON_POST_SPEED_COST";

  /**驿站操作后，通知上层界面刷新显示 需带上数据 ---  PostTrainMessage */
  public static ON_POST_SPEED_UPDATE: string = "ON_POST_SPEED_UPDATE";

  /**游历数据模块定时器，数据更新 */
  public static ON_TRAVEL_UPDATE: string = "ON_TRAVEL_UPDATE";

  /**繁荣度更新，通知界面 */
  public static ON_BLOOM_UPDATE: string = "ON_BLOOM_UPDATE";

  /**关卡界面数据更新，通知刷更新  ----- 废弃不使用*/
  //public static ON_CHAPTER_UPDATE: string = "ON_CHAPTER_UPDATE";

  /**挑战界面业务逻辑事件===================== */

  /**挑战界面地图背景变速 */
  public static ON_CHALLENGE_MAP_SPEED: string = "ON_CHALLENGE_MAP_SPEED";

  /**飞行气运到道具栏上 */
  public static ON_CHALLENGE_ENERGY_ITEM_UP: string = "ON_CHALLENGE_ENERGY_ITEM_UP";

  /**宝箱掉落到狗车身上 */
  public static ON_CHALLENGE_BOX_DROP_ON_DOG: string = "ON_CHALLENGE_BOX_DROP_ON_DOG";

  /**检测是否到达boss关卡 */
  public static ON_CHALLENGE_CHECK_BOSS: string = "ON_CHALLENGE_CHECK_BOSS";

  /**主界面业务逻辑事件====================== */

  /**心跳包每次请求心跳通知建筑层，飘动建筑增加气运 */
  public static ON_HEART_BEAT: string = "ON_HEART_BEAT";

  /**号召人数变化后，据点进行更新 */
  public static ON_CALL_NUM_CHANGE: string = "ON_CALL_NUM_CHANGE";

  /** 移到指定建筑消息 */
  public static ON_ZHAOGE_CITY_FOCOUS = "ON_ZHAOGE_CITY_FOCOUS";

  /** 邮件更新通知 */
  public static ON_MAIL_UPDATE: string = "ON_MAIL_UPDATE";

  /** UI 消息前缀 */
  public static UIReady: string = "UIReady";

  /** 红点更新通知 */
  public static ON_BADGE_UPDATE: string = "ON_BADGE_UPDATE";

  /**关闭天荒古镜召唤入口 */
  public static ON_CLOSE_CALL_ENTRY: string = "ON_CLOSE_CALL_ENTRY";

  /** 福地偷菜 */
  public static ON_FARM_FIND: string = "ON_FARM_FIND";

  /** 福地基金购买成功 */
  public static FARM_FUND_BUY_SUCCESS: string = "FARM_FUND_BUY_SUCCESS";

  /**战斗类 */

  /**战斗打击震动UI */
  public static ON_SHAKE_WORLD = "ON_SHAKE_WORLD";

  /**战盟战斗累计伤害 */
  public static ON_ClUB_FIGHT_DAMAGE = "ON_ClUB_FIGHT_DAMAGE";

  /**战斗结束 */
  public static ON_FIGHT_END = "ON_FIGHT_END";

  /**跳过战斗 */
  public static ON_FIGHT_SKIP = "ON_FIGHT_SKIP";

  /**更新回合数 */
  public static ON_FIGHT_ROUND_UPDATE = "ON_FIGHT_ROUND_UPDATE";

  /**更新战斗倍数 */
  public static ON_FIGHT_SPEED = "ON_FIGHT_SPEED";

  /** ++++++++++++++++++++++++++ 商店模块 ++++++++++++++++++++++++++ */
  public static ON_SHOP_ORDER_NOTIFY: string = "ON_SHOP_ORDER_NOTIFY";

  /** +++++++++++++++++++++++++英雄模块 +++++++++++++++++++++++++*/
  /** 英雄数据更新  */
  public static ON_HERO_UPDATE: string = "ON_HERO_UPDATE";
  /** -------------------------英雄模块 -------------------------*/

  /** +++++++++++++++++++++++++灵兽模块 +++++++++++++++++++++++++*/
  /** 灵兽数据更新  */
  public static ON_PET_UPDATE: string = "ON_PET_UPDATE";
  /** -------------------------灵兽模块 -------------------------*/

  /** +++++++++++++++++++++++++仙友模块 +++++++++++++++++++++++++*/
  /** 仙友模块数据更新  */
  public static ON_FRIEND_UPDATE: string = "ON_FRIEND_UPDATE";
  public static ON_FRIEND_CITY_SKILL_UPDATE: string = "ON_FRIEND_CITY_SKILL_UPDATE";
  public static ON_FRIEND_HERO_SKILL_UPDATE: string = "ON_FRIEND_HERO_SKILL_UPDATE";
  /** -------------------------仙友模块 -------------------------*/

  /** =========================兽魂信息更新 开始 ===================== */

  /** 武魂更新 */
  public static ON_SOUL_UPDATE = "ON_SOUL_UPDATE";
  public static ON_SOUL_TUJIAN_UPDATE = "ON_SOUL_TUJIAN_UPDATE";

  /** 上任的武魂状态更新 */
  public static ON_SOUL_WORK_UPDATE = "ON_SOUL_WORK_UPDATE";

  /** =========================兽魂信息更新 结束 ===================== */

  /** =========================活动 开始 ===================== */
  // 七日签到玩家数据更新
  public static ON_ACTIVITY_SEVEN_UPDATE: string = "ON_ACTIVITY_SEVEN_UPDATE";
  // 弹窗礼包事件
  public static ON_ACTIVITY_TANCHUANG: string = "ON_ACTIVITY_TANCHUANG";
  // 弹窗礼包事件更新
  public static ON_ACTIVITY_TIAOJIAN_UPDATE: string = "ON_ACTIVITY_TIAOJIAN_UPDATE";

  /***基金购买后刷新推送===== */
  public static ON_ACTIVITY_FUND_BUT_UP: string = "ON_ACTIVITY_FUND_BUT_UP";
  /**每日礼包购买后推送刷新 */
  public static ON_ACTIVITY_DAILY_GIFT_BUT_UP: string = "ON_ACTIVITY_DAILY_GIFT_BUT_UP";
  /**充钱后，累计充值推送 */
  public static ON_ACTIVITY_RECHARGE_UP: string = "ON_ACTIVITY_RECHARGE_UP";

  /**条件礼包购买后推送刷新 */
  public static ON_ACTIVITY_TIAOJIANLIBAO_BUY: string = "ON_ACTIVITY_TANCHUANGLIBAO_BUY";

  /**当修行基金签到活动充值后进行推送 */
  public static ON_ACTIVITY_FUND_RECHARGE: string = "ON_ACTIVITY_FUND_RECHARGE";

  /**月卡充值回调 */
  public static ON_ACTIVITY_MONTH_CARD_RECHARGE: string = "ON_ACTIVITY_MONTH_CARD_RECHARGE";

  /**年卡充值回调 */
  public static ON_ACTIVITY_LIFE_CARD_RECHARGE: string = "ON_ACTIVITY_LIFE_CARD_RECHARGE";

  /**首充成功回调 */
  public static ON_ACTIVITY_FIRST_RECHARGE: string = "ON_ACTIVITY_FIRST_RECHARGE";

  /**当修行基金的日常任务完成情况变更时推送 */
  public static ON_ACTIVITY_FUND_DAILY_TASK_UPDATE: string = "ON_ACTIVITY_FUND_DAILY_TASK_UPDATE";
  /**当修行基金的礼包界面发生变化 */
  public static ON_ACTIVITY_FUND_GIFT_UPDATE: string = "ON_ACTIVITY_FUND_GIFT_UPDATE";

  /**活动配置数据更新 */
  public static ON_ACTIVITY_UPDATE: string = "ON_ACTIVITY_UPDATE";

  public static ON_FRBP_LB_UPDATE: string = "ON_FRBP_LB_UPDATE";

  /** 时空裂隙支付信息更新 */
  public static ON_FRACTURE_BUY_UPDATE: string = "ON_FRACTURE_BUY_UPDATE";

  public static ON_FRACTURE_UPDATE: string = "ON_FRACTURE_UPDATE";

  public static ON_FRACTURE_FLOOR_UNLOCK: string = "ON_FRACTURE_FLOOR_UNLOCK";

  /** =========================活动 结束 ===================== */

  /** +++++++++++++++++++++++++聊天模块 +++++++++++++++++++++++++*/
  /** 聊天模块数据更新  */
  public static ON_CHAT_WORLD_MESSAGE: string = "ON_CHAT_WORLD_MESSAGE";
  public static ON_CHAT_UNION_MESSAGE: string = "ON_CHAT_UNION_MESSAGE";
  /** -------------------------聊天模块 -------------------------*/

  /** ------------------------- 引导模块 -------------------------*/
  public static ON_PLAYER_GUIDE_ID_UPDATE: string = "ON_PLAYER_GUIDE_ID_UPDATE";

  /** ------------------------- 引导参数 -------------------------*/
  public static ON_GAME_MAP_SHOW100: string = "ON_GAME_MAP_SHOW100";

  /**三界小家地图背景更新 */
  public static ON_SANJIE_XIAJIA_BG_UPDATE: string = "ON_SANJIE_XIAJIA_BG_UPDATE";

  /** ------------------------- 三界事件 -------------------------*/

  /**三界事件数据更新了 */
  public static ON_SANJIE_EVENT_DATA_UPDATE: string = "ON_SANJIE_EVENT_DATA_UPDATE";

  /**更新三界贼人的的Hp */
  public static ON_SANJIE_THIEF_HP_UPDATE: string = "ON_SANJIE_THIEF_HP_UPDATE";

  /** ui隐藏动画 */
  public static ON_UIMAIN_HIDE_ANI: string = "ON_UIMAIN_HIDE_ANI";

  /** ui显示动画 */
  public static ON_UIMAIN_SHOW_ANI: string = "ON_UIMAIN_SHOW_ANI";

  /**------------------------------------------------------------ */

  /** 主排行榜更新 */
  public static ON_MAIN_RANK_UPDATE: string = "ON_MAIN_RANK_UPDATE";
}

/**
 * 节点消息事件枚举
 * 使用方式 node.emit(NodeMsgEnum.UIGuide.setFingerPosSize, pos, size)
 *
 * */
export const NodeMsgEnum = {
  // 引导按钮
  UIGuide: {
    // 显示手指并设置大小坐标
    setFingerPosSize: "setFingerPosSize",
    // 设置强制引导
    setForce: "setForce",
  },
};
