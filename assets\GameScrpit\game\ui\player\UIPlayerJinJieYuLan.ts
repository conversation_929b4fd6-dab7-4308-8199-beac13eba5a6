import { _decorator, Label, Node, RichText } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { JsonMgr } from "../../mgr/JsonMgr";
import { IConfigLeaderSkin } from "../../JsonDefine";
import ToolExt from "../../common/ToolExt";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UIPlayerJinJieYuLan")
export class UIPlayerJinJieYuLan extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_PLAYER}?prefab/ui/UIPlayerJinJieYuLan`;
  }

  protected onEvtShow(): void {
    let skinList = this.getSkinList();
    let curLv = PlayerModule.data.getPlayerInfo().level;

    let skinLv1: number = null;
    let skinLv2: number = null;
    for (let i = 0; i < skinList.length; i++) {
      if (skinList[i].unlock.length < 2 || skinList[i].unlock[0] != 1) {
        log.error("异常的错误配置，主角身份皮肤配置不是等级解锁");
        continue;
      }
      if (skinList[i].unlock[1] <= curLv) {
        skinLv1 = skinList[i].id;
        if (i + 1 < skinList.length) skinLv2 = skinList[i + 1].id;
      } else {
        break;
      }
    }

    log.log("皮肤等级", skinLv1 + "===" + skinLv2);
    this.setSkinDetail(this.getNode("spr1"), skinLv1);
    this.setSkinDetail(this.getNode("spr2"), skinLv2);
  }

  private setSkinDetail(node: Node, id: number) {
    ToolExt.loadUIRole(node.getChildByName("rolePoint"), id, 0, "", this);

    let dbLeaderSkin = JsonMgr.instance.jsonList.c_leaderSkin[id];

    let lbl_role_name = node.getChildByPath("spr_role_name_bg/lbl_role_name");
    let name = "";
    for (let i = 0; i < dbLeaderSkin.name.length; i++) {
      name += dbLeaderSkin.name[i];
      if (i != dbLeaderSkin.name.length - 1) {
        name += "\n";
      }
    }
    lbl_role_name.getComponent(Label).string = name;

    let dbleader = JsonMgr.instance.jsonList.c_leader[dbLeaderSkin.unlock[1]];
    let rich_role_lv = node.getChildByPath("rich_role_lv");
    rich_role_lv.getComponent(RichText).string = dbleader.jingjie2;
  }

  private getSkinList(): IConfigLeaderSkin[] {
    let skinList: IConfigLeaderSkin[] = [];
    let db = JsonMgr.instance.jsonList.c_leaderSkin;
    let sex = PlayerModule.data.getPlayerInfo().sex;
    let dbList = Object.keys(db);

    for (let i = 0; i < dbList.length; i++) {
      let info = db[dbList[i]];
      if (info.type == 1 && info.sex == sex) {
        skinList.push(info);
      }
    }

    return skinList;
  }
}
