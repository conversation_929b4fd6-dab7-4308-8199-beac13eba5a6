{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "1754ae21-0b87-478c-805b-e0f53d13b202", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "1754ae21-0b87-478c-805b-e0f53d13b202@6c48a", "displayName": "bg_xuanzhong_yuan2", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "1754ae21-0b87-478c-805b-e0f53d13b202", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "1754ae21-0b87-478c-805b-e0f53d13b202@f9941", "displayName": "bg_xuanzhong_yuan2", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 80, "height": 80, "rawWidth": 80, "rawHeight": 80, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-40, -40, 0, 40, -40, 0, -40, 40, 0, 40, 40, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 80, 80, 80, 0, 0, 80, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-40, -40, 0], "maxPos": [40, 40, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "1754ae21-0b87-478c-805b-e0f53d13b202@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "1754ae21-0b87-478c-805b-e0f53d13b202@6c48a"}}