{"skeleton": {"hash": "yMl3Bc4zMqcCNqEX01iE630zptg=", "spine": "3.8.75", "x": -402.37, "y": -847.73, "width": 812.32, "height": 1692.44, "images": "./images/", "audio": "D:/spine导出/剧情动画new/image/版5"}, "bones": [{"name": "root", "scaleX": 1.05, "scaleY": 1.05}, {"name": "all", "parent": "root", "y": -800}, {"name": "bone", "parent": "all", "length": 147.19, "rotation": -3.77, "x": -3.82, "y": 813.77}, {"name": "bone2", "parent": "bone", "length": 153.39, "rotation": 85.3, "x": -8.26, "y": 14.82}, {"name": "bone3", "parent": "bone2", "length": 43.91, "rotation": -8.63, "x": 153.39}, {"name": "bone4", "parent": "bone2", "x": 137.97, "y": 59.71}, {"name": "bone5", "parent": "bone4", "length": 116.37, "rotation": 170.29, "x": -10.8, "y": 12.26}, {"name": "bone6", "parent": "bone5", "length": 129.94, "rotation": 17.47, "x": 116.37}, {"name": "bone7", "parent": "bone6", "length": 77.61, "rotation": -8.26, "x": 129.94}, {"name": "bone8", "parent": "bone6", "length": 72.53, "rotation": -42.03, "x": 60.11, "y": -31.54}, {"name": "bone9", "parent": "bone8", "length": 67.16, "rotation": 22.35, "x": 72.53}, {"name": "bone10", "parent": "bone9", "length": 73.08, "rotation": 26.74, "x": 67.16}, {"name": "bone11", "parent": "bone2", "x": 145.26, "y": -47.12}, {"name": "bone12", "parent": "bone11", "length": 112.82, "rotation": -132.67, "x": -11.49, "y": -21.52}, {"name": "bone15", "parent": "bone12", "length": 81.35, "rotation": 43.75, "x": 139.16, "y": 13.52}, {"name": "bone13", "parent": "bone15", "length": 97.88, "rotation": 106.3, "x": 4.29, "y": 12.05}, {"name": "bone14", "parent": "bone13", "length": 52.38, "rotation": 61.34, "x": 97.88}, {"name": "bone16", "parent": "bone15", "length": 77.99, "rotation": -12.78, "x": 81.35}, {"name": "bone17", "parent": "bone16", "length": 56.82, "rotation": -41.58, "x": 78.69, "y": 0.26}, {"name": "bone18", "parent": "bone", "length": 56.07, "rotation": -46.18, "x": 34.38, "y": -30.24}, {"name": "bone19", "parent": "bone18", "length": 61.7, "rotation": 12.73, "x": 56.54, "y": -0.4}, {"name": "bone20", "parent": "bone", "length": 53.19, "rotation": -75.44, "x": -1.7, "y": -31.37}, {"name": "bone21", "parent": "bone20", "length": 63.23, "rotation": 4.62, "x": 53.19}, {"name": "bone22", "parent": "bone", "length": 62.75, "rotation": -104.12, "x": -59.41, "y": -16.48}, {"name": "bone23", "parent": "bone22", "length": 66.37, "rotation": 7.64, "x": 62.75}, {"name": "bone24", "parent": "bone", "length": 140.85, "rotation": -67.57, "x": -34.25, "y": -55.24}, {"name": "bone25", "parent": "bone24", "length": 163.38, "rotation": -52.84, "x": 140.85}, {"name": "bone26", "parent": "bone", "length": 143.29, "rotation": -72.85, "x": 38.63, "y": -62.36}, {"name": "bone27", "parent": "bone26", "length": 145.05, "rotation": -48.84, "x": 143.29}, {"name": "bone28", "parent": "bone", "length": 165.96, "rotation": 139.41, "x": -46.15, "y": 23.08, "color": "ff7b00ff"}, {"name": "bone29", "parent": "bone28", "length": 164.06, "rotation": -41.7, "x": 165.96, "color": "ff7b00ff"}, {"name": "bone30", "parent": "bone29", "length": 164.74, "rotation": 34.02, "x": 164.06, "color": "ff7b00ff"}, {"name": "bone31", "parent": "bone", "length": 165.96, "rotation": 169.96, "x": -46.15, "y": 23.08, "color": "ff7b00ff"}, {"name": "bone32", "parent": "bone31", "length": 164.06, "rotation": -41.7, "x": 165.96, "color": "ff7b00ff"}, {"name": "bone33", "parent": "bone32", "length": 164.74, "rotation": 34.02, "x": 164.06, "color": "ff7b00ff"}, {"name": "bone34", "parent": "bone", "length": 165.96, "rotation": -146.85, "x": -46.15, "y": 23.08, "color": "ff7b00ff"}, {"name": "bone35", "parent": "bone34", "length": 164.06, "rotation": -41.7, "x": 165.96, "color": "ff7b00ff"}, {"name": "bone36", "parent": "bone35", "length": 164.74, "rotation": 34.02, "x": 164.06, "color": "ff7b00ff"}, {"name": "bone37", "parent": "bone", "length": 137.69, "rotation": 42.78, "x": -9.2, "y": 23.36, "color": "e9ff00ff"}, {"name": "bone38", "parent": "bone37", "length": 171.26, "rotation": 34.88, "x": 138.27, "y": 0.88, "color": "e9ff00ff"}, {"name": "bone39", "parent": "bone38", "length": 157.09, "rotation": -36.77, "x": 170.28, "y": -0.39, "color": "e9ff00ff"}, {"name": "bone40", "parent": "bone", "length": 137.69, "rotation": 18.04, "x": -7.42, "y": 18.4, "color": "e9ff00ff"}, {"name": "bone41", "parent": "bone40", "length": 171.26, "rotation": 34.88, "x": 138.27, "y": 0.88, "color": "e9ff00ff"}, {"name": "bone42", "parent": "bone41", "length": 157.09, "rotation": -36.77, "x": 170.28, "y": -0.39, "color": "e9ff00ff"}, {"name": "bone43", "parent": "bone", "length": 137.69, "rotation": -13.1, "x": -10.31, "y": 18.2, "color": "e9ff00ff"}, {"name": "bone44", "parent": "bone43", "length": 171.26, "rotation": 34.88, "x": 138.27, "y": 0.88, "color": "e9ff00ff"}, {"name": "bone45", "parent": "bone44", "length": 157.09, "rotation": -36.77, "x": 170.28, "y": -0.39, "color": "e9ff00ff"}, {"name": "bone46", "parent": "bone", "length": 184.67, "rotation": 93.77, "x": 757.9, "y": 63.92, "color": "abe323ff"}, {"name": "bone47", "parent": "bone46", "length": 184.67, "x": 184.67, "color": "abe323ff"}, {"name": "bone48", "parent": "bone47", "length": 184.67, "x": 184.67, "color": "abe323ff"}, {"name": "bone49", "parent": "bone", "length": 183.91, "rotation": -86.23, "x": 757.98, "y": 63.86, "color": "54ff00ff"}, {"name": "bone50", "parent": "bone49", "length": 183.91, "x": 183.91, "color": "54ff00ff"}, {"name": "bone51", "parent": "bone50", "length": 183.91, "x": 183.91, "color": "54ff00ff"}, {"name": "bone52", "parent": "bone2", "x": 34.34, "y": -21.47, "color": "abe323ff"}, {"name": "bone53", "parent": "bone2", "x": 88.54, "y": -13.4, "color": "abe323ff"}, {"name": "bone54", "parent": "bone3", "length": 54.46, "rotation": 21.6, "x": 163.52, "y": 87.81}, {"name": "bone55", "parent": "bone54", "length": 60.24, "rotation": 11.37, "x": 54.46}, {"name": "bone56", "parent": "bone55", "length": 66.36, "rotation": 13.29, "x": 60.24}, {"name": "bone57", "parent": "bone3", "length": 49.61, "rotation": -27.4, "x": 194.29, "y": -57.81}, {"name": "bone58", "parent": "bone57", "length": 51.88, "rotation": 3.32, "x": 49.61}, {"name": "bone59", "parent": "bone58", "length": 52.11, "rotation": 15.52, "x": 51.88}, {"name": "bone60", "parent": "bone3", "length": 43.85, "rotation": -156.56, "x": 214.23, "y": -9.27}, {"name": "bone61", "parent": "bone60", "length": 46.83, "rotation": -8.32, "x": 43.85}, {"name": "bone62", "parent": "bone61", "length": 33.03, "rotation": -10.25, "x": 46.85, "y": -0.54}, {"name": "bone63", "parent": "bone3", "length": 43.27, "rotation": -124.46, "x": 223.61, "y": -37.91}, {"name": "bone64", "parent": "bone63", "length": 50.64, "rotation": -35.39, "x": 43.27}, {"name": "bone65", "parent": "bone64", "length": 31.96, "rotation": -9.81, "x": 50.64}, {"name": "bone66", "parent": "bone3", "length": 52.7, "rotation": -112.93, "x": 111.63, "y": -103.89}, {"name": "bone67", "parent": "bone66", "length": 53.94, "rotation": -11.04, "x": 52.7}, {"name": "bone68", "parent": "bone67", "length": 35.52, "rotation": -37.19, "x": 53.94}, {"name": "bone69", "parent": "bone3", "length": 49.08, "rotation": 151.66, "x": 213.12, "y": 16.28}, {"name": "bone70", "parent": "bone69", "length": 38.16, "rotation": 23.94, "x": 49.46, "y": -0.38}, {"name": "bone71", "parent": "bone70", "length": 33.88, "rotation": 11.44, "x": 38.16}, {"name": "bone72", "parent": "bone3", "length": 47.65, "rotation": 171.7, "x": 174.16, "y": 75.21}, {"name": "bone73", "parent": "bone72", "length": 53.33, "rotation": 28.3, "x": 47.65}, {"name": "bone74", "parent": "bone73", "length": 45.35, "rotation": 1.87, "x": 53.33}, {"name": "bone75", "parent": "bone74", "length": 44.04, "rotation": -30.08, "x": 45.8, "y": -1.12}, {"name": "bone76", "parent": "bone3", "length": 85.93, "rotation": 151.6, "x": 225.18, "y": 57.65, "color": "ffc900ff"}, {"name": "bone77", "parent": "bone76", "length": 81.89, "rotation": 3.4, "x": 85.93, "color": "ffc900ff"}, {"name": "bone78", "parent": "bone77", "length": 96.78, "rotation": 15.25, "x": 81.1, "y": -0.71, "color": "ffc900ff"}, {"name": "bone79", "parent": "bone78", "length": 66.58, "rotation": 12.48, "x": 94.16, "y": -0.73, "color": "ffc900ff"}, {"name": "bone80", "parent": "bone3", "length": 69.48, "rotation": -128.84, "x": 238.07, "y": 3.06, "color": "ffc900ff"}, {"name": "bone81", "parent": "bone80", "length": 81.71, "rotation": -26.56, "x": 67.25, "y": -2.35, "color": "ffc900ff"}, {"name": "bone82", "parent": "bone81", "length": 86.39, "rotation": 24.13, "x": 81.12, "y": -0.46, "color": "ffc900ff"}, {"name": "bone83", "parent": "bone82", "length": 62.23, "rotation": -15.21, "x": 85.37, "y": -0.63, "color": "ffc900ff"}, {"name": "<PERSON>hil<PERSON>o", "parent": "all", "x": 1188.07, "y": 1107.37}, {"name": "zhiliao1", "parent": "<PERSON>hil<PERSON>o", "rotation": -59.88, "x": 3.09, "y": -521.11, "scaleX": 1.6607, "scaleY": 1.6607}, {"name": "zhiliao2", "parent": "<PERSON>hil<PERSON>o", "x": -5.09, "y": -223.46}, {"name": "zhiliao3", "parent": "zhiliao2", "x": -68.27, "y": -181.57, "scaleX": 1.9538, "scaleY": 1.9538, "color": "ff0b0bff"}, {"name": "zhiliao4", "parent": "zhiliao1", "rotation": 59.88, "x": -5.63, "y": -6.09}, {"name": "zhiliao5", "parent": "zhiliao2", "x": 66.76, "y": -185.17, "scaleX": 1.9538, "scaleY": 1.9538, "color": "ff0b0bff"}, {"name": "zhiliao6", "parent": "zhiliao1", "rotation": 59.88, "x": 1.48, "y": 9.67}, {"name": "zhiliao7", "parent": "zhiliao2", "x": -8.12, "y": -153.81, "scaleX": 1.9538, "scaleY": 1.9538, "color": "ff0b0bff"}, {"name": "zhiliao8", "parent": "zhiliao1", "rotation": 59.88, "x": -6.02, "y": 4.4}, {"name": "zhiliao9", "parent": "zhiliao1", "scaleX": 1.3152, "scaleY": 1.3152}, {"name": "zhiliao10", "parent": "zhiliao1", "rotation": 59.88, "x": -0.34, "y": 0.2, "scaleX": 12.1359, "scaleY": 12.1359}, {"name": "zhiliao11", "parent": "zhiliao1", "rotation": 59.88, "x": -0.34, "y": 0.2, "scaleX": 12.1359, "scaleY": 12.1359}, {"name": "zhakai", "parent": "all", "x": 6.84, "y": -790.82, "scaleX": 3.6388, "scaleY": 3.6388}, {"name": "zhakai2", "parent": "zhakai"}, {"name": "zhakai3", "parent": "zhakai", "scaleX": 6.4939, "scaleY": 6.4939}, {"name": "quanp", "parent": "all", "y": 800, "scaleX": 52.902, "scaleY": 110.1145}, {"name": "bone84", "parent": "root", "y": -686.85}], "slots": [{"name": "rootcut", "bone": "all", "attachment": "rootcut"}, {"name": "danjifuhuo01", "bone": "all", "attachment": "danjifuhuo01"}, {"name": "danjifuhuo02", "bone": "all", "attachment": "danjifuhuo02"}, {"name": "danjifuhuo5", "bone": "bone37", "attachment": "danjifuhuo03"}, {"name": "danjifuhuo6", "bone": "bone40", "attachment": "danjifuhuo03"}, {"name": "danjifuhuo7", "bone": "bone43", "attachment": "danjifuhuo03"}, {"name": "danjifuhuo03", "bone": "bone28", "attachment": "danjifuhuo03"}, {"name": "danjifuhuo3", "bone": "bone31", "attachment": "danjifuhuo03"}, {"name": "danjifuhuo4", "bone": "bone34", "attachment": "danjifuhuo03"}, {"name": "danjifuhuo09", "bone": "all", "attachment": "danjifuhuo09"}, {"name": "danjifuhuo010", "bone": "all", "attachment": "danjifuhuo010"}, {"name": "danjifuhuo011", "bone": "all", "attachment": "danjifuhuo011"}, {"name": "danjifuhuo012", "bone": "all", "attachment": "danjifuhuo012"}, {"name": "danjifuhuo013", "bone": "all", "attachment": "danjifuhuo013"}, {"name": "danjifuhuo014", "bone": "all", "attachment": "danjifuhuo014"}, {"name": "danjifuhuo015", "bone": "all", "attachment": "danjifuhuo015"}, {"name": "danjifuhuo016", "bone": "bone3", "attachment": "danjifuhuo016"}, {"name": "danjifuhuo017", "bone": "bone3", "attachment": "danjifuhuo017"}, {"name": "danjifuhuo018", "bone": "all", "attachment": "danjifuhuo018"}, {"name": "ma<PERSON>a", "bone": "bone3", "attachment": "ma<PERSON>a"}, {"name": "danjifuhuo019", "bone": "all", "attachment": "danjifuhuo019"}, {"name": "zhiliao1/light", "bone": "zhiliao9", "color": "67ff0000", "attachment": "zhiliao1/light", "blend": "additive"}, {"name": "zhiliao1/yzzl_skill_zd_00000", "bone": "zhiliao1"}, {"name": "zhiliao1/yzzl_skill_zd_0", "bone": "zhiliao1", "color": "ffffff5d", "blend": "additive"}, {"name": "zhiliao2/xfr_skill_xuli_td1_45", "bone": "zhiliao3", "blend": "additive"}, {"name": "zhiliao2/xfr_skill_xuli_td1_46", "bone": "zhiliao5", "blend": "additive"}, {"name": "zhiliao2/xfr_skill_xuli_td1_47", "bone": "zhiliao7", "blend": "additive"}, {"name": "tx_qua", "bone": "zhiliao10", "color": "ffffff00", "attachment": "tx_qua", "blend": "additive"}, {"name": "tx_qua2", "bone": "zhiliao11", "color": "ffffff00", "attachment": "tx_qua", "blend": "additive"}, {"name": "zha/quan2", "bone": "zhakai2", "color": "87ff00ff", "blend": "additive"}, {"name": "zha/dd", "bone": "zhakai3", "color": "87ff00ff", "blend": "additive"}, {"name": "zik<PERSON><PERSON>", "bone": "bone84"}, {"name": "zikzizi2", "bone": "bone84", "blend": "additive"}], "transform": [{"name": "111s", "order": 19, "bones": ["bone4"], "target": "bone53", "x": 49.43, "y": 73.11, "rotateMix": 0.31, "translateMix": 0.31, "scaleMix": 0.31, "shearMix": 0.31}, {"name": "body1", "order": 18, "bones": ["bone53"], "target": "bone52", "x": 54.2, "y": 8.07, "rotateMix": -1, "translateMix": -1, "scaleMix": -1, "shearMix": -1}, {"name": "q1", "order": 9, "bones": ["bone37"], "target": "bone49", "rotation": 129.05, "x": -24.01, "y": -740.95, "rotateMix": 0.35, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "q2", "order": 10, "bones": ["bone38"], "target": "bone50", "rotation": 163.91, "x": -295.66, "y": -634.09, "rotateMix": 0.35, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "q3", "order": 11, "bones": ["bone39"], "target": "bone51", "rotation": 127.13, "x": -643.07, "y": -586.51, "rotateMix": 0.35, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "q4", "order": 12, "bones": ["bone40"], "target": "bone49", "rotation": 104.26, "x": -24.01, "y": -740.95, "rotateMix": 0.45, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "q5", "order": 13, "bones": ["bone41"], "target": "bone50", "rotation": 139.15, "x": -242.84, "y": -607.16, "rotateMix": 0.45, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "q6", "order": 14, "bones": ["bone42"], "target": "bone51", "rotation": 102.37, "x": -555.29, "y": -495.48, "rotateMix": 0.45, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "q7", "order": 15, "bones": ["bone43"], "target": "bone49", "rotation": 73.13, "x": -24.01, "y": -740.95, "rotateMix": 0.55, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "q8", "order": 16, "bones": ["bone44"], "target": "bone50", "rotation": 108.01, "x": -168.64, "y": -608.38, "rotateMix": 0.55, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "q9", "order": 17, "bones": ["bone45"], "target": "bone51", "rotation": 71.24, "x": -404.83, "y": -446.33, "rotateMix": 0.55, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "sk2", "order": 20, "bones": ["bone11"], "target": "bone52", "x": 110.93, "y": -25.64, "rotateMix": -0.284, "translateMix": -0.284, "scaleMix": -0.284, "shearMix": -0.284}, {"name": "w1", "bones": ["bone28"], "target": "bone46", "rotation": 45.63, "x": 12.15, "y": 804.99, "shearY": -360, "rotateMix": 0.35, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "w2", "order": 1, "bones": ["bone31"], "target": "bone46", "rotation": 76.18, "x": 12.15, "y": 804.99, "shearY": -360, "rotateMix": 0.45, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "w3", "order": 2, "bones": ["bone34"], "target": "bone46", "rotation": 119.38, "x": 12.15, "y": 804.99, "rotateMix": 0.55, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "w4", "order": 3, "bones": ["bone29"], "target": "bone47", "rotation": 3.93, "x": -56.47, "y": 923.63, "shearY": -360, "rotateMix": 0.35, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "w5", "order": 4, "bones": ["bone32"], "target": "bone47", "rotation": 34.48, "x": -132.88, "y": 966.15, "shearY": -360, "rotateMix": 0.45, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "w6", "order": 5, "bones": ["bone35"], "target": "bone47", "rotation": 77.68, "x": -253.94, "y": 949.61, "shearY": -360, "rotateMix": 0.55, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "w7", "order": 6, "bones": ["bone30"], "target": "bone48", "rotation": 37.95, "x": -77.47, "y": 934.89, "shearY": -360, "rotateMix": 0.35, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "w8", "order": 7, "bones": ["bone33"], "target": "bone48", "rotation": 68.5, "x": -182.31, "y": 1059.03, "shearY": -360, "rotateMix": 0.45, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "w9", "order": 8, "bones": ["bone36"], "target": "bone48", "rotation": 111.7, "x": -403.6, "y": 1109.89, "rotateMix": 0.55, "translateMix": 0, "scaleMix": 0, "shearMix": 0}], "skins": [{"name": "default", "attachments": {"tx_qua": {"tx_qua": {"color": "6cff00ff", "width": 134, "height": 134}}, "zhiliao1/light": {"zhiliao1/light": {"x": 0.5, "y": 0.5, "scaleX": 1.428, "scaleY": 1.428, "width": 189, "height": 189}}, "danjifuhuo01": {"danjifuhuo01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [374.77, 1.44, -374.77, 1.44, -374.77, 1598.56, 374.77, 1598.56], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 451, "height": 961}}, "danjifuhuo02": {"danjifuhuo02": {"x": -0.46, "y": 349.5, "width": 752, "height": 571}}, "danjifuhuo03": {"danjifuhuo03": {"type": "mesh", "uvs": [1, 1, 1, 0.90282, 0.93279, 0.78301, 0.83151, 0.68687, 0.80035, 0.59665, 0.82956, 0.45022, 0.80814, 0.31119, 0.70102, 0.20322, 0.56078, 0.13222, 0.39913, 0.09968, 0.24721, 0.10855, 0.3368, 0.17955, 0.38744, 0.25794, 0.38939, 0.3526, 0.35239, 0.44283, 0.31343, 0.54488, 0.31928, 0.65581, 0.35044, 0.76231, 0.43419, 0.85401, 0.56468, 0.91169, 0.71465, 0.92944, 0.84514, 0.96346, 0.94642, 1, 0.9114, 0.92035, 0.78849, 0.8408, 0.67257, 0.75913, 0.6153, 0.62548, 0.61391, 0.49078, 0.59435, 0.36031, 0.53569, 0.25425, 0.41398, 0.15863], "triangles": [14, 13, 28, 13, 29, 28, 28, 7, 6, 28, 29, 7, 13, 12, 29, 12, 30, 29, 12, 11, 30, 29, 8, 7, 29, 30, 8, 11, 10, 30, 10, 9, 30, 30, 9, 8, 17, 26, 25, 26, 16, 27, 14, 27, 15, 16, 26, 17, 3, 26, 4, 14, 28, 27, 27, 16, 15, 26, 27, 4, 4, 27, 5, 27, 28, 5, 28, 6, 5, 0, 23, 1, 21, 23, 22, 0, 22, 23, 23, 21, 24, 21, 20, 24, 24, 20, 25, 1, 23, 2, 20, 19, 25, 19, 18, 25, 23, 24, 2, 18, 17, 25, 24, 3, 2, 24, 25, 3, 3, 25, 26], "vertices": [1, 29, -17.49, -0.55, 1, 2, 29, 14.99, -33.75, 0.99343, 30, -90.26, -125.63, 0.00657, 2, 29, 72.48, -57.63, 0.86072, 30, -31.46, -105.22, 0.13928, 2, 29, 130.89, -64.78, 0.56456, 30, 16.91, -71.7, 0.43544, 3, 29, 169.13, -87.7, 0.2378, 30, 60.71, -63.37, 0.739, 31, -121.11, 5.3, 0.0232, 3, 29, 210.49, -145.15, 0.03718, 30, 129.81, -78.75, 0.7427, 31, -72.45, -46.11, 0.22012, 2, 30, 196.65, -75.55, 0.45656, 31, -15.26, -80.85, 0.54344, 2, 30, 250.81, -40.3, 0.14643, 31, 49.35, -81.94, 0.85357, 2, 30, 288.15, 8.15, 0.01002, 31, 107.42, -62.67, 0.98998, 1, 31, 155.77, -25.97, 1, 1, 31, 186.34, 20.13, 1, 2, 30, 271.16, 90.82, 0.00126, 31, 139.58, 15.35, 0.99874, 2, 30, 232.52, 75.05, 0.0654, 31, 98.73, 23.91, 0.9346, 2, 30, 187.33, 77.45, 0.27671, 31, 62.62, 51.18, 0.72329, 3, 29, 336.8, -26.56, 0.00146, 30, 145.23, 93.81, 0.57245, 31, 36.87, 88.29, 0.42609, 3, 29, 312.79, 18.2, 0.02923, 30, 97.53, 111.27, 0.80833, 31, 7.11, 129.45, 0.16244, 3, 29, 274.2, 54.62, 0.13829, 30, 44.48, 112.79, 0.82125, 31, -36.01, 160.39, 0.04046, 3, 29, 230.52, 83.1, 0.37149, 30, -7.08, 105, 0.62292, 31, -83.1, 182.77, 0.00559, 3, 29, 178.14, 93.18, 0.66826, 30, -52.89, 77.68, 0.3317, 31, -136.37, 185.76, 4e-05, 2, 29, 124.99, 79.77, 0.89253, 30, -83.65, 32.31, 0.10747, 2, 29, 80.14, 47.77, 0.99121, 30, -95.85, -21.42, 0.00879, 1, 29, 34.91, 26.27, 1, 1, 29, -3.59, 13.05, 1, 2, 29, 32.12, -5.28, 0.9999, 30, -96.42, -92.97, 0.0001, 2, 29, 90.61, -1.26, 0.9999, 30, -55.42, -51.07, 0.0001, 3, 29, 147.99, 0.25, 0.66657, 30, -13.58, -11.77, 0.33342, 31, -153.82, 89.64, 0, 3, 29, 207.52, -30.88, 0.33333, 30, 51.57, 4.59, 0.6665, 31, -90.67, 66.74, 0.00016, 2, 30, 115.85, 0.68, 0.6665, 31, -39.59, 27.54, 0.3335, 2, 30, 178.55, 3.48, 0.33317, 31, 13.95, -5.22, 0.66683, 1, 31, 67.03, -19.61, 1, 1, 31, 130.24, -12.89, 1], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44, 0, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 20], "width": 363, "height": 478}}, "danjifuhuo09": {"danjifuhuo09": {"type": "mesh", "uvs": [0.44258, 0.00266, 0.60963, 0, 0.79449, 0.0469, 0.94817, 0.14518, 1, 0.28504, 1, 0.41482, 0.99272, 0.51688, 0.85908, 0.60004, 0.72099, 0.67816, 0.60241, 0.75743, 0.56363, 0.81046, 0.56039, 0.922, 0.54746, 1, 0.46507, 0.99771, 0.37292, 0.99514, 0.31797, 0.92383, 0.29534, 0.81594, 0.31474, 0.73183, 0.39555, 0.69343, 0.45373, 0.62029, 0.31797, 0.67514, 0.25895, 0.7337, 0.20493, 0.82272, 0.19319, 0.88516, 0.1744, 0.93963, 0.09455, 0.9383, 0.00531, 0.89313, 0, 0.81076, 0.00296, 0.72042, 0.04289, 0.66462, 0.12274, 0.62609, 0.15327, 0.53973, 0.19789, 0.46002, 0.27069, 0.41086, 0.32001, 0.39492, 0.23781, 0.35506, 0.12274, 0.26605, 0.07812, 0.17438, 0.11569, 0.08802, 0.2519, 0.01096, 0.52433, 0.08935, 0.60652, 0.20759, 0.67228, 0.32982, 0.7216, 0.43478, 0.68402, 0.49855, 0.54311, 0.56232, 0.3294, 0.10263, 0.42569, 0.23283, 0.49615, 0.35108, 0.54546, 0.44939, 0.42334, 0.52113, 0.2613, 0.61679, 0.1744, 0.7244, 0.11099, 0.82936, 0.11099, 0.89446, 0.75213, 0.11459, 0.82493, 0.24346, 0.85546, 0.38429, 0.86251, 0.48792, 0.76857, 0.5663, 0.60887, 0.66329, 0.49615, 0.74035, 0.4116, 0.83202, 0.44213, 0.93033], "triangles": [44, 43, 58, 6, 58, 5, 59, 44, 58, 45, 44, 59, 7, 59, 58, 7, 58, 6, 60, 45, 59, 19, 45, 60, 8, 60, 59, 8, 59, 7, 18, 19, 60, 61, 18, 60, 9, 61, 60, 9, 60, 8, 10, 61, 9, 61, 17, 18, 62, 61, 10, 62, 17, 61, 16, 17, 62, 11, 62, 10, 15, 16, 62, 63, 15, 62, 11, 63, 62, 14, 15, 63, 13, 63, 11, 14, 63, 13, 12, 13, 11, 55, 1, 2, 40, 1, 55, 55, 2, 3, 41, 40, 55, 56, 55, 3, 41, 55, 56, 56, 3, 4, 42, 41, 56, 57, 56, 4, 42, 56, 57, 57, 4, 5, 43, 42, 57, 58, 57, 5, 43, 57, 58, 49, 42, 43, 44, 49, 43, 50, 34, 49, 33, 34, 50, 32, 33, 50, 31, 32, 50, 45, 50, 49, 45, 49, 44, 51, 31, 50, 19, 50, 45, 51, 50, 19, 30, 31, 51, 20, 51, 19, 52, 30, 51, 52, 51, 20, 29, 30, 52, 28, 29, 52, 20, 21, 52, 53, 27, 28, 22, 52, 21, 52, 53, 28, 22, 53, 52, 23, 53, 22, 26, 27, 53, 54, 26, 53, 23, 54, 53, 25, 26, 54, 24, 54, 23, 25, 54, 24, 40, 0, 1, 46, 39, 0, 46, 0, 40, 38, 39, 46, 47, 46, 40, 47, 40, 41, 46, 37, 38, 36, 46, 47, 36, 37, 46, 48, 47, 41, 48, 41, 42, 35, 36, 47, 35, 47, 48, 34, 35, 48, 49, 48, 42, 34, 48, 49], "vertices": [2, 25, -3.93, 23.27, 0.81663, 27, -34.34, -44.73, 0.18337, 2, 25, 5.77, 54.91, 0.28042, 27, -27.6, -12.33, 0.71958, 1, 27, -3.16, 19.48, 1, 1, 27, 37.35, 41.12, 1, 1, 27, 87.34, 39.78, 1, 2, 27, 131.53, 29.27, 0.94843, 28, -29.77, 10.42, 0.05157, 2, 27, 165.95, 19.61, 0.24484, 28, 0.16, 29.97, 0.75516, 1, 28, 39.22, 25.3, 1, 1, 28, 77.35, 18.89, 1, 1, 28, 113.57, 15.86, 1, 1, 28, 133.14, 20.38, 1, 1, 28, 165.31, 42.5, 1, 1, 28, 189.03, 56.26, 1, 1, 28, 197.84, 42.5, 1, 1, 28, 207.7, 27.12, 1, 1, 28, 193.68, 3.78, 1, 1, 28, 165.52, -21.78, 1, 2, 26, 119.34, 21.14, 0.00337, 28, 139.31, -35.73, 0.99663, 2, 26, 99.23, 26.82, 0.05153, 28, 119.08, -30.49, 0.94847, 2, 26, 71.58, 21.97, 0.59756, 28, 91.55, -35.96, 0.40244, 1, 26, 102.57, 10.52, 1, 1, 26, 126.09, 12.37, 1, 1, 26, 157.87, 21.03, 1, 1, 26, 177.26, 31.38, 1, 1, 26, 195.12, 39.02, 1, 1, 26, 203.62, 25.68, 1, 1, 26, 200.47, 2.18, 1, 1, 26, 177.21, -14.89, 1, 1, 26, 150.72, -32.17, 1, 1, 26, 130.13, -36.61, 1, 1, 26, 110.09, -31.11, 1, 2, 25, 155.85, -91.13, 0.00722, 26, 81.69, -43.09, 0.99278, 2, 25, 132.24, -73.83, 0.08584, 26, 53.64, -51.46, 0.91416, 2, 25, 120.55, -54.67, 0.27632, 26, 31.31, -49.2, 0.72368, 2, 25, 118.39, -43.64, 0.52821, 26, 21.21, -44.26, 0.47179, 2, 25, 99.97, -54.59, 0.87598, 26, 18.81, -65.56, 0.12402, 2, 25, 63.16, -66.22, 0.99348, 26, 5.85, -101.91, 0.00652, 1, 25, 29.94, -64.33, 1, 1, 25, 3.68, -47.61, 1, 1, 25, -13.25, -13.43, 1, 2, 25, 30, 28.9, 0.60735, 27, -1.08, -36, 0.39265, 2, 25, 74.41, 31.08, 0.49091, 27, 42.95, -29.74, 0.50909, 4, 25, 119.11, 29.74, 0.49141, 26, -36.83, 0.64, 0.00165, 27, 87.58, -26.97, 0.50595, 28, -16.36, -59.69, 0.00099, 4, 25, 157.04, 27.24, 0.15141, 26, -11.93, 29.36, 0.19816, 27, 125.58, -25.97, 0.39053, 28, 7.89, -30.43, 0.2599, 4, 25, 175.81, 13.05, 0.01791, 26, 10.72, 35.74, 0.33331, 27, 145.57, -38.37, 0.04636, 28, 30.39, -23.54, 0.60241, 2, 26, 44.86, 25.21, 0.59422, 28, 64.76, -33.32, 0.40578, 1, 25, 22.06, -9.15, 1, 2, 25, 71.33, -5.67, 0.99959, 26, -37.48, -58.82, 0.00041, 2, 25, 115.01, -5.69, 0.96478, 26, -11.08, -24.03, 0.03522, 3, 26, 11.9, 3.38, 0.97608, 27, 122.48, -61.08, 0.00385, 28, 32.29, -55.87, 0.02007, 2, 25, 166.79, -38.38, 0.00234, 26, 46.26, -2.51, 0.99766, 1, 26, 91.98, -10.24, 1, 1, 26, 132.8, -3.31, 1, 1, 26, 170.25, 6.95, 1, 1, 26, 189.09, 19.75, 1, 1, 27, 17.95, 5.84, 1, 1, 27, 65.17, 9.43, 1, 1, 27, 114.52, 3.9, 1, 1, 28, 6.86, 3.09, 1, 1, 28, 39.99, 3.85, 1, 2, 26, 66.77, 55.83, 0.00918, 28, 85.99, -2.21, 0.99082, 2, 26, 101.62, 52.52, 0.00335, 28, 120.9, -4.74, 0.99665, 1, 28, 156.75, 0.24, 1, 1, 28, 181.27, 25.12, 1], "hull": 40, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 0, 78, 0, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 38, 78, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 2, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 24, 26, 26, 28], "width": 198, "height": 350}}, "danjifuhuo3": {"danjifuhuo03": {"type": "mesh", "uvs": [1, 1, 1, 0.90282, 0.93279, 0.78301, 0.83151, 0.68687, 0.80035, 0.59665, 0.82956, 0.45022, 0.80814, 0.31119, 0.70102, 0.20322, 0.56078, 0.13222, 0.39913, 0.09968, 0.24721, 0.10855, 0.3368, 0.17955, 0.38744, 0.25794, 0.38939, 0.3526, 0.35239, 0.44283, 0.31343, 0.54488, 0.31928, 0.65581, 0.35044, 0.76231, 0.43419, 0.85401, 0.56468, 0.91169, 0.71465, 0.92944, 0.84514, 0.96346, 0.94642, 1, 0.9114, 0.92035, 0.78849, 0.8408, 0.67257, 0.75913, 0.6153, 0.62548, 0.61391, 0.49078, 0.59435, 0.36031, 0.53569, 0.25425, 0.41398, 0.15863], "triangles": [14, 13, 28, 13, 29, 28, 28, 7, 6, 28, 29, 7, 13, 12, 29, 12, 30, 29, 12, 11, 30, 29, 8, 7, 29, 30, 8, 11, 10, 30, 10, 9, 30, 30, 9, 8, 17, 26, 25, 26, 16, 27, 14, 27, 15, 16, 26, 17, 3, 26, 4, 14, 28, 27, 27, 16, 15, 26, 27, 4, 4, 27, 5, 27, 28, 5, 28, 6, 5, 0, 23, 1, 21, 23, 22, 0, 22, 23, 23, 21, 24, 21, 20, 24, 24, 20, 25, 1, 23, 2, 20, 19, 25, 19, 18, 25, 23, 24, 2, 18, 17, 25, 24, 3, 2, 24, 25, 3, 3, 25, 26], "vertices": [1, 32, -17.49, -0.55, 1, 2, 32, 14.99, -33.75, 0.99343, 33, -90.26, -125.63, 0.00657, 2, 32, 72.48, -57.63, 0.86072, 33, -31.46, -105.22, 0.13928, 2, 32, 130.89, -64.78, 0.56456, 33, 16.91, -71.7, 0.43544, 3, 32, 169.13, -87.7, 0.2378, 33, 60.71, -63.37, 0.739, 34, -121.11, 5.3, 0.0232, 3, 32, 210.49, -145.15, 0.03718, 33, 129.81, -78.75, 0.7427, 34, -72.45, -46.11, 0.22012, 2, 33, 196.65, -75.55, 0.45656, 34, -15.26, -80.85, 0.54344, 2, 33, 250.81, -40.3, 0.14643, 34, 49.35, -81.94, 0.85357, 2, 33, 288.15, 8.15, 0.01002, 34, 107.42, -62.67, 0.98998, 1, 34, 155.77, -25.97, 1, 1, 34, 186.34, 20.13, 1, 2, 33, 271.16, 90.82, 0.00126, 34, 139.58, 15.35, 0.99874, 2, 33, 232.52, 75.05, 0.0654, 34, 98.73, 23.91, 0.9346, 2, 33, 187.33, 77.45, 0.27671, 34, 62.62, 51.18, 0.72329, 3, 32, 336.8, -26.56, 0.00146, 33, 145.23, 93.81, 0.57245, 34, 36.87, 88.29, 0.42609, 3, 32, 312.79, 18.2, 0.02923, 33, 97.53, 111.27, 0.80833, 34, 7.11, 129.45, 0.16244, 3, 32, 274.2, 54.62, 0.13829, 33, 44.48, 112.79, 0.82125, 34, -36.01, 160.39, 0.04046, 3, 32, 230.52, 83.1, 0.37149, 33, -7.08, 105, 0.62292, 34, -83.1, 182.77, 0.00559, 3, 32, 178.14, 93.18, 0.66826, 33, -52.89, 77.68, 0.3317, 34, -136.37, 185.76, 4e-05, 2, 32, 124.99, 79.77, 0.89253, 33, -83.65, 32.31, 0.10747, 2, 32, 80.14, 47.77, 0.99121, 33, -95.85, -21.42, 0.00879, 1, 32, 34.91, 26.27, 1, 1, 32, -3.59, 13.05, 1, 2, 32, 32.12, -5.28, 0.9999, 33, -96.42, -92.97, 0.0001, 2, 32, 90.61, -1.26, 0.9999, 33, -55.42, -51.07, 0.0001, 3, 32, 147.99, 0.25, 0.66657, 33, -13.58, -11.77, 0.33342, 34, -153.82, 89.64, 0, 3, 32, 207.52, -30.88, 0.33333, 33, 51.57, 4.59, 0.6665, 34, -90.67, 66.74, 0.00016, 2, 33, 115.85, 0.68, 0.6665, 34, -39.59, 27.54, 0.3335, 2, 33, 178.55, 3.48, 0.33317, 34, 13.95, -5.22, 0.66683, 1, 34, 67.03, -19.61, 1, 1, 34, 130.24, -12.89, 1], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44, 0, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 20], "width": 363, "height": 478}}, "danjifuhuo4": {"danjifuhuo03": {"type": "mesh", "uvs": [1, 1, 1, 0.90282, 0.93279, 0.78301, 0.83151, 0.68687, 0.80035, 0.59665, 0.82956, 0.45022, 0.80814, 0.31119, 0.70102, 0.20322, 0.56078, 0.13222, 0.39913, 0.09968, 0.24721, 0.10855, 0.3368, 0.17955, 0.38744, 0.25794, 0.38939, 0.3526, 0.35239, 0.44283, 0.31343, 0.54488, 0.31928, 0.65581, 0.35044, 0.76231, 0.43419, 0.85401, 0.56468, 0.91169, 0.71465, 0.92944, 0.84514, 0.96346, 0.94642, 1, 0.9114, 0.92035, 0.78849, 0.8408, 0.67257, 0.75913, 0.6153, 0.62548, 0.61391, 0.49078, 0.59435, 0.36031, 0.53569, 0.25425, 0.41398, 0.15863], "triangles": [14, 13, 28, 13, 29, 28, 28, 7, 6, 28, 29, 7, 13, 12, 29, 12, 30, 29, 12, 11, 30, 29, 8, 7, 29, 30, 8, 11, 10, 30, 10, 9, 30, 30, 9, 8, 17, 26, 25, 26, 16, 27, 14, 27, 15, 16, 26, 17, 3, 26, 4, 14, 28, 27, 27, 16, 15, 26, 27, 4, 4, 27, 5, 27, 28, 5, 28, 6, 5, 0, 23, 1, 21, 23, 22, 0, 22, 23, 23, 21, 24, 21, 20, 24, 24, 20, 25, 1, 23, 2, 20, 19, 25, 19, 18, 25, 23, 24, 2, 18, 17, 25, 24, 3, 2, 24, 25, 3, 3, 25, 26], "vertices": [1, 35, -17.49, -0.55, 1, 2, 35, 14.99, -33.75, 0.99343, 36, -90.26, -125.63, 0.00657, 2, 35, 72.48, -57.63, 0.86072, 36, -31.46, -105.22, 0.13928, 2, 35, 130.89, -64.78, 0.56456, 36, 16.91, -71.7, 0.43544, 3, 35, 169.13, -87.7, 0.2378, 36, 60.71, -63.37, 0.739, 37, -121.11, 5.3, 0.0232, 3, 35, 210.49, -145.15, 0.03718, 36, 129.81, -78.75, 0.7427, 37, -72.45, -46.11, 0.22012, 2, 36, 196.65, -75.55, 0.45656, 37, -15.26, -80.85, 0.54344, 2, 36, 250.81, -40.3, 0.14643, 37, 49.35, -81.94, 0.85357, 2, 36, 288.15, 8.15, 0.01002, 37, 107.42, -62.67, 0.98998, 1, 37, 155.77, -25.97, 1, 1, 37, 186.34, 20.13, 1, 2, 36, 271.16, 90.82, 0.00126, 37, 139.58, 15.35, 0.99874, 2, 36, 232.52, 75.05, 0.0654, 37, 98.73, 23.91, 0.9346, 2, 36, 187.33, 77.45, 0.27671, 37, 62.62, 51.18, 0.72329, 3, 35, 336.8, -26.56, 0.00146, 36, 145.23, 93.81, 0.57245, 37, 36.87, 88.29, 0.42609, 3, 35, 312.79, 18.2, 0.02923, 36, 97.53, 111.27, 0.80833, 37, 7.11, 129.45, 0.16244, 3, 35, 274.2, 54.62, 0.13829, 36, 44.48, 112.79, 0.82125, 37, -36.01, 160.39, 0.04046, 3, 35, 230.52, 83.1, 0.37149, 36, -7.08, 105, 0.62292, 37, -83.1, 182.77, 0.00559, 3, 35, 178.14, 93.18, 0.66826, 36, -52.89, 77.68, 0.3317, 37, -136.37, 185.76, 4e-05, 2, 35, 124.99, 79.77, 0.89253, 36, -83.65, 32.31, 0.10747, 2, 35, 80.14, 47.77, 0.99121, 36, -95.85, -21.42, 0.00879, 1, 35, 34.91, 26.27, 1, 1, 35, -3.59, 13.05, 1, 2, 35, 32.12, -5.28, 0.9999, 36, -96.42, -92.97, 0.0001, 2, 35, 90.61, -1.26, 0.9999, 36, -55.42, -51.07, 0.0001, 3, 35, 147.99, 0.25, 0.66657, 36, -13.58, -11.77, 0.33342, 37, -153.82, 89.64, 0, 3, 35, 207.52, -30.88, 0.33333, 36, 51.57, 4.59, 0.6665, 37, -90.67, 66.74, 0.00016, 2, 36, 115.85, 0.68, 0.6665, 37, -39.59, 27.54, 0.3335, 2, 36, 178.55, 3.48, 0.33317, 37, 13.95, -5.22, 0.66683, 1, 37, 67.03, -19.61, 1, 1, 37, 130.24, -12.89, 1], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44, 0, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 20], "width": 363, "height": 478}}, "danjifuhuo5": {"danjifuhuo03": {"type": "mesh", "uvs": [0.95186, 1, 0.88256, 0.97324, 0.79372, 0.94355, 0.68711, 0.92061, 0.5414, 0.90037, 0.42413, 0.85314, 0.36016, 0.77623, 0.3264, 0.66828, 0.3193, 0.56033, 0.34595, 0.46857, 0.39037, 0.38761, 0.40103, 0.29855, 0.36905, 0.21489, 0.30153, 0.14472, 0.24644, 0.10019, 0.36372, 0.10424, 0.47033, 0.11369, 0.56272, 0.13123, 0.65334, 0.16766, 0.73508, 0.22703, 0.80438, 0.30665, 0.83992, 0.39166, 0.82925, 0.48476, 0.80971, 0.57382, 0.79905, 0.64804, 0.84525, 0.71146, 0.9181, 0.77893, 0.97318, 0.85449, 1, 0.93815, 1, 1, 0.97789, 1, 0.95357, 0.95146, 0.8892, 0.89336, 0.80159, 0.84301, 0.70413, 0.78892, 0.62408, 0.71967, 0.5914, 0.61243, 0.62124, 0.51476, 0.63549, 0.41411, 0.6024, 0.30349, 0.53017, 0.22111, 0.45072, 0.17155, 0.34094, 0.12597], "triangles": [10, 11, 39, 38, 10, 39, 39, 19, 20, 11, 40, 39, 39, 18, 19, 39, 40, 18, 40, 12, 41, 40, 11, 12, 40, 41, 17, 40, 17, 18, 17, 41, 16, 13, 42, 12, 12, 42, 41, 42, 15, 41, 41, 15, 16, 13, 14, 42, 42, 14, 15, 34, 24, 25, 7, 36, 35, 35, 36, 24, 7, 8, 36, 24, 36, 23, 36, 8, 37, 37, 8, 9, 36, 37, 23, 37, 9, 10, 23, 37, 22, 37, 10, 38, 37, 38, 22, 22, 38, 21, 38, 20, 21, 38, 39, 20, 30, 28, 29, 0, 31, 30, 30, 31, 28, 0, 1, 31, 1, 32, 31, 1, 2, 32, 28, 31, 27, 2, 33, 32, 2, 3, 33, 31, 32, 27, 3, 34, 33, 3, 4, 34, 34, 4, 35, 27, 32, 26, 32, 33, 26, 4, 5, 35, 5, 6, 35, 33, 25, 26, 33, 34, 25, 34, 35, 24, 6, 7, 35], "vertices": [1, 38, -12.91, -20.66, 1, 1, 38, 13.3, -31.13, 1, 1, 38, 45.23, -46.01, 1, 3, 38, 79, -67.87, 0.98306, 39, -87.93, -22.5, 0.01693, 40, -193.6, -172.29, 1e-05, 3, 38, 121.16, -101.24, 0.91663, 39, -72.44, -73.99, 0.08189, 40, -150.36, -204.25, 0.00148, 3, 38, 166.2, -118.35, 0.78838, 39, -45.27, -113.78, 0.20291, 40, -104.78, -219.87, 0.00871, 3, 38, 209.15, -111.56, 0.58825, 39, -6.15, -132.77, 0.37475, 40, -62.08, -211.66, 0.037, 3, 38, 256.02, -86.73, 0.37279, 39, 46.49, -139.21, 0.518, 40, -16.05, -185.31, 0.10921, 3, 38, 296.51, -54.64, 0.18741, 39, 98.06, -136.04, 0.56753, 40, 23.35, -151.89, 0.24506, 3, 38, 323.09, -18.44, 0.07563, 39, 140.57, -121.55, 0.46169, 40, 48.73, -114.84, 0.46268, 3, 38, 341.55, 19.21, 0.02437, 39, 177.24, -101.22, 0.262, 40, 65.94, -76.6, 0.71363, 3, 38, 370.99, 50.19, 0.00468, 39, 219.11, -92.64, 0.09145, 40, 94.34, -44.66, 0.90388, 3, 38, 408.71, 67.84, 0.00018, 39, 260.14, -99.73, 0.00852, 40, 131.46, -25.78, 0.99129, 1, 40, 172.69, -20.72, 1, 1, 40, 201.89, -20.75, 1, 1, 40, 171.36, 8.99, 1, 2, 39, 304.13, -57.82, 0.00175, 40, 141.6, 34.13, 0.99825, 2, 39, 292.07, -25.42, 0.02378, 40, 112.54, 52.86, 0.97622, 2, 39, 271.11, 5.34, 0.13008, 40, 77.34, 64.95, 0.86992, 2, 39, 239.61, 31.67, 0.41115, 40, 36.34, 67.18, 0.58885, 2, 39, 198.99, 52.43, 0.72246, 40, -8.63, 59.5, 0.27754, 2, 39, 157.17, 60.74, 0.94949, 40, -47.09, 41.12, 0.05051, 1, 39, 113.37, 51.94, 1, 2, 38, 174.25, 74.92, 0.00299, 39, 71.85, 40.16, 0.99701, 2, 38, 150.13, 48.61, 0.14763, 39, 37.03, 32.37, 0.85237, 2, 38, 116.29, 41.22, 0.46256, 39, 5.04, 45.66, 0.53744, 2, 38, 74.6, 39.83, 0.7929, 39, -29.95, 68.36, 0.2071, 2, 38, 34.26, 31.03, 0.9816, 39, -68.07, 84.22, 0.0184, 1, 38, -2.21, 11.98, 1, 1, 38, -24.44, -7.52, 1, 1, 38, -19.14, -13.56, 1, 1, 38, 4.12, -4.89, 1, 1, 38, 40.41, -4.13, 1, 2, 38, 79.47, -12.16, 0.99977, 39, -55.69, 22.93, 0.00023, 3, 38, 122.24, -21.71, 0.82647, 39, -26.06, -9.36, 0.17244, 40, -151.9, -124.73, 0.00108, 3, 38, 166.28, -21.71, 0.52482, 39, 10.06, -34.56, 0.46374, 40, -107.88, -123.28, 0.01144, 3, 38, 212.64, 3.18, 0.19485, 39, 62.33, -40.65, 0.77463, 40, -62.37, -96.87, 0.03052, 3, 38, 240.59, 42.11, 0.03494, 39, 107.52, -24.69, 0.85304, 40, -35.72, -57.04, 0.11202, 3, 38, 273.34, 77.73, 0.00325, 39, 154.76, -14.2, 0.5668, 40, -4.16, -20.36, 0.42995, 3, 38, 321.01, 103.58, 0.00012, 39, 208.64, -20.26, 0.25662, 40, 42.63, 7.05, 0.74326, 2, 39, 250.69, -41.94, 0.00599, 40, 89.28, 14.85, 0.99401, 2, 39, 277.44, -67.97, 0.00094, 40, 126.29, 10.02, 0.99906, 1, 40, 169.44, -4.15, 1], "hull": 31, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 0, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 28], "width": 363, "height": 478}}, "danjifuhuo6": {"danjifuhuo03": {"type": "mesh", "uvs": [0.95186, 1, 0.88256, 0.97324, 0.79372, 0.94355, 0.68711, 0.92061, 0.5414, 0.90037, 0.42413, 0.85314, 0.36016, 0.77623, 0.3264, 0.66828, 0.3193, 0.56033, 0.34595, 0.46857, 0.39037, 0.38761, 0.40103, 0.29855, 0.36905, 0.21489, 0.30153, 0.14472, 0.24644, 0.10019, 0.36372, 0.10424, 0.47033, 0.11369, 0.56272, 0.13123, 0.65334, 0.16766, 0.73508, 0.22703, 0.80438, 0.30665, 0.83992, 0.39166, 0.82925, 0.48476, 0.80971, 0.57382, 0.79905, 0.64804, 0.84525, 0.71146, 0.9181, 0.77893, 0.97318, 0.85449, 1, 0.93815, 1, 1, 0.97789, 1, 0.95357, 0.95146, 0.8892, 0.89336, 0.80159, 0.84301, 0.70413, 0.78892, 0.62408, 0.71967, 0.5914, 0.61243, 0.62124, 0.51476, 0.63549, 0.41411, 0.6024, 0.30349, 0.53017, 0.22111, 0.45072, 0.17155, 0.34094, 0.12597, 0.72794, 0.67039, 0.65358, 0.61187], "triangles": [10, 11, 39, 38, 10, 39, 39, 19, 20, 11, 40, 39, 39, 18, 19, 39, 40, 18, 40, 12, 41, 40, 11, 12, 40, 41, 17, 40, 17, 18, 17, 41, 16, 13, 42, 12, 12, 42, 41, 42, 15, 41, 41, 15, 16, 13, 14, 42, 42, 14, 15, 25, 43, 24, 7, 36, 35, 35, 44, 43, 35, 36, 44, 43, 44, 24, 7, 8, 36, 24, 44, 23, 36, 8, 37, 37, 8, 9, 36, 37, 44, 37, 9, 10, 44, 37, 23, 23, 37, 22, 37, 10, 38, 37, 38, 22, 22, 38, 21, 38, 20, 21, 38, 39, 20, 30, 28, 29, 0, 31, 30, 30, 31, 28, 0, 1, 31, 1, 32, 31, 1, 2, 32, 28, 31, 27, 2, 33, 32, 2, 3, 33, 31, 32, 27, 3, 34, 33, 3, 4, 34, 34, 4, 35, 27, 32, 26, 32, 33, 26, 4, 5, 35, 5, 6, 35, 33, 34, 25, 25, 34, 43, 33, 25, 26, 34, 35, 43, 6, 7, 35], "vertices": [1, 41, -12.91, -20.66, 1, 1, 41, 13.3, -31.13, 1, 1, 41, 45.23, -46.01, 1, 3, 41, 79, -67.87, 0.98306, 42, -87.93, -22.5, 0.01693, 43, -193.6, -172.29, 1e-05, 3, 41, 121.16, -101.24, 0.91663, 42, -72.44, -73.99, 0.08189, 43, -150.36, -204.25, 0.00148, 3, 41, 166.2, -118.35, 0.78838, 42, -45.27, -113.78, 0.20291, 43, -104.78, -219.87, 0.00871, 3, 41, 209.15, -111.56, 0.58825, 42, -6.15, -132.77, 0.37475, 43, -62.08, -211.66, 0.037, 3, 41, 256.02, -86.73, 0.37279, 42, 46.49, -139.21, 0.518, 43, -16.05, -185.31, 0.10921, 3, 41, 296.51, -54.64, 0.18741, 42, 98.06, -136.04, 0.56753, 43, 23.35, -151.89, 0.24506, 3, 41, 323.09, -18.44, 0.07563, 42, 140.57, -121.55, 0.46169, 43, 48.73, -114.84, 0.46268, 3, 41, 341.55, 19.21, 0.02437, 42, 177.24, -101.22, 0.262, 43, 65.94, -76.6, 0.71363, 3, 41, 370.99, 50.19, 0.00468, 42, 219.11, -92.64, 0.09145, 43, 94.34, -44.66, 0.90388, 3, 41, 408.71, 67.84, 0.00018, 42, 260.14, -99.73, 0.00852, 43, 131.46, -25.78, 0.99129, 1, 43, 172.69, -20.72, 1, 1, 43, 201.89, -20.75, 1, 1, 43, 171.36, 8.99, 1, 2, 42, 304.13, -57.82, 0.00175, 43, 141.6, 34.13, 0.99825, 2, 42, 292.07, -25.42, 0.02378, 43, 112.54, 52.86, 0.97622, 2, 42, 271.11, 5.34, 0.13008, 43, 77.34, 64.95, 0.86992, 2, 42, 239.61, 31.67, 0.41115, 43, 36.34, 67.18, 0.58885, 2, 42, 198.99, 52.43, 0.72246, 43, -8.63, 59.5, 0.27754, 2, 42, 157.17, 60.74, 0.94949, 43, -47.09, 41.12, 0.05051, 1, 42, 113.37, 51.94, 1, 2, 41, 174.25, 74.92, 0.00299, 42, 71.85, 40.16, 0.99701, 2, 41, 150.13, 48.61, 0.14763, 42, 37.03, 32.37, 0.85237, 2, 41, 116.29, 41.22, 0.46256, 42, 5.04, 45.66, 0.53744, 2, 41, 74.6, 39.83, 0.7929, 42, -29.95, 68.36, 0.2071, 2, 41, 34.26, 31.03, 0.9816, 42, -68.07, 84.22, 0.0184, 1, 41, -2.21, 11.98, 1, 1, 41, -24.44, -7.52, 1, 1, 41, -19.14, -13.56, 1, 1, 41, 4.12, -4.89, 1, 1, 41, 40.41, -4.13, 1, 2, 41, 79.47, -12.16, 0.99977, 42, -55.69, 22.93, 0.00023, 3, 41, 122.24, -21.71, 0.82647, 42, -26.06, -9.36, 0.17244, 43, -151.9, -124.73, 0.00108, 3, 41, 166.28, -21.71, 0.52482, 42, 10.06, -34.56, 0.46374, 43, -107.88, -123.28, 0.01144, 3, 41, 212.64, 3.18, 0.19485, 42, 62.33, -40.65, 0.77463, 43, -62.37, -96.87, 0.03052, 3, 41, 240.59, 42.11, 0.03494, 42, 107.52, -24.69, 0.85304, 43, -35.72, -57.04, 0.11202, 3, 41, 273.34, 77.73, 0.00325, 42, 154.76, -14.2, 0.5668, 43, -4.16, -20.36, 0.42995, 3, 41, 321.01, 103.58, 0.00012, 42, 208.64, -20.26, 0.25662, 43, 42.63, 7.05, 0.74326, 2, 42, 250.69, -41.94, 0.00599, 43, 89.28, 14.85, 0.99401, 2, 42, 277.44, -67.97, 0.00094, 43, 126.29, 10.02, 0.99906, 1, 43, 169.44, -4.15, 1, 3, 41, 159.13, 22.16, 0.27848, 42, 29.28, 5.53, 0.71592, 43, -116.48, -79.67, 0.0056, 3, 41, 197.96, 20.32, 0.16609, 42, 60.08, -18.19, 0.81283, 43, -77.62, -80.22, 0.02108], "hull": 31, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 0, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 28], "width": 363, "height": 478}}, "danjifuhuo7": {"danjifuhuo03": {"type": "mesh", "uvs": [0.95186, 1, 0.88256, 0.97324, 0.79372, 0.94355, 0.68711, 0.92061, 0.5414, 0.90037, 0.42413, 0.85314, 0.36016, 0.77623, 0.3264, 0.66828, 0.3193, 0.56033, 0.34595, 0.46857, 0.39037, 0.38761, 0.40103, 0.29855, 0.36905, 0.21489, 0.30153, 0.14472, 0.24644, 0.10019, 0.36372, 0.10424, 0.47033, 0.11369, 0.56272, 0.13123, 0.65334, 0.16766, 0.73508, 0.22703, 0.80438, 0.30665, 0.83992, 0.39166, 0.82925, 0.48476, 0.80971, 0.57382, 0.79905, 0.64804, 0.84525, 0.71146, 0.9181, 0.77893, 0.97318, 0.85449, 1, 0.93815, 1, 1, 0.97789, 1, 0.95357, 0.95146, 0.8892, 0.89336, 0.80159, 0.84301, 0.70413, 0.78892, 0.62408, 0.71967, 0.5914, 0.61243, 0.62124, 0.51476, 0.63549, 0.41411, 0.6024, 0.30349, 0.53017, 0.22111, 0.45072, 0.17155, 0.34094, 0.12597], "triangles": [10, 11, 39, 38, 10, 39, 39, 19, 20, 11, 40, 39, 39, 18, 19, 39, 40, 18, 40, 12, 41, 40, 11, 12, 40, 41, 17, 40, 17, 18, 17, 41, 16, 13, 42, 12, 12, 42, 41, 42, 15, 41, 41, 15, 16, 13, 14, 42, 42, 14, 15, 34, 24, 25, 7, 36, 35, 35, 36, 24, 7, 8, 36, 24, 36, 23, 36, 8, 37, 37, 8, 9, 36, 37, 23, 37, 9, 10, 23, 37, 22, 37, 10, 38, 37, 38, 22, 22, 38, 21, 38, 20, 21, 38, 39, 20, 30, 28, 29, 0, 31, 30, 30, 31, 28, 0, 1, 31, 1, 32, 31, 1, 2, 32, 28, 31, 27, 2, 33, 32, 2, 3, 33, 31, 32, 27, 3, 34, 33, 3, 4, 34, 34, 4, 35, 27, 32, 26, 32, 33, 26, 4, 5, 35, 5, 6, 35, 33, 25, 26, 33, 34, 25, 34, 35, 24, 6, 7, 35], "vertices": [1, 44, -12.91, -20.66, 1, 1, 44, 13.3, -31.13, 1, 1, 44, 45.23, -46.01, 1, 3, 44, 79, -67.87, 0.98306, 45, -87.93, -22.5, 0.01693, 46, -193.6, -172.29, 1e-05, 3, 44, 121.16, -101.24, 0.91663, 45, -72.44, -73.99, 0.08189, 46, -150.36, -204.25, 0.00148, 3, 44, 166.2, -118.35, 0.78838, 45, -45.27, -113.78, 0.20291, 46, -104.78, -219.87, 0.00871, 3, 44, 209.15, -111.56, 0.58825, 45, -6.15, -132.77, 0.37475, 46, -62.08, -211.66, 0.037, 3, 44, 256.02, -86.73, 0.37279, 45, 46.49, -139.21, 0.518, 46, -16.05, -185.31, 0.10921, 3, 44, 296.51, -54.64, 0.18741, 45, 98.06, -136.04, 0.56753, 46, 23.35, -151.89, 0.24506, 3, 44, 323.09, -18.44, 0.07563, 45, 140.57, -121.55, 0.46169, 46, 48.73, -114.84, 0.46268, 3, 44, 341.55, 19.21, 0.02437, 45, 177.24, -101.22, 0.262, 46, 65.94, -76.6, 0.71363, 3, 44, 370.99, 50.19, 0.00468, 45, 219.11, -92.64, 0.09145, 46, 94.34, -44.66, 0.90388, 3, 44, 408.71, 67.84, 0.00018, 45, 260.14, -99.73, 0.00852, 46, 131.46, -25.78, 0.99129, 1, 46, 172.69, -20.72, 1, 1, 46, 201.89, -20.75, 1, 1, 46, 171.36, 8.99, 1, 2, 45, 304.13, -57.82, 0.00175, 46, 141.6, 34.13, 0.99825, 2, 45, 292.07, -25.42, 0.02378, 46, 112.54, 52.86, 0.97622, 2, 45, 271.11, 5.34, 0.13008, 46, 77.34, 64.95, 0.86992, 2, 45, 239.61, 31.67, 0.41115, 46, 36.34, 67.18, 0.58885, 2, 45, 198.99, 52.43, 0.72246, 46, -8.63, 59.5, 0.27754, 2, 45, 157.17, 60.74, 0.94949, 46, -47.09, 41.12, 0.05051, 1, 45, 113.37, 51.94, 1, 2, 44, 174.25, 74.92, 0.00299, 45, 71.85, 40.16, 0.99701, 2, 44, 150.13, 48.61, 0.14763, 45, 37.03, 32.37, 0.85237, 2, 44, 116.29, 41.22, 0.46256, 45, 5.04, 45.66, 0.53744, 2, 44, 74.6, 39.83, 0.7929, 45, -29.95, 68.36, 0.2071, 2, 44, 34.26, 31.03, 0.9816, 45, -68.07, 84.22, 0.0184, 1, 44, -2.21, 11.98, 1, 1, 44, -24.44, -7.52, 1, 1, 44, -19.14, -13.56, 1, 1, 44, 4.12, -4.89, 1, 1, 44, 40.41, -4.13, 1, 2, 44, 79.47, -12.16, 0.99977, 45, -55.69, 22.93, 0.00023, 3, 44, 122.24, -21.71, 0.82647, 45, -26.06, -9.36, 0.17244, 46, -151.9, -124.73, 0.00108, 3, 44, 166.28, -21.71, 0.52482, 45, 10.06, -34.56, 0.46374, 46, -107.88, -123.28, 0.01144, 3, 44, 212.64, 3.18, 0.19485, 45, 62.33, -40.65, 0.77463, 46, -62.37, -96.87, 0.03052, 3, 44, 240.59, 42.11, 0.03494, 45, 107.52, -24.69, 0.85304, 46, -35.72, -57.04, 0.11202, 3, 44, 273.34, 77.73, 0.00325, 45, 154.76, -14.2, 0.5668, 46, -4.16, -20.36, 0.42995, 3, 44, 321.01, 103.58, 0.00012, 45, 208.64, -20.26, 0.25662, 46, 42.63, 7.05, 0.74326, 2, 45, 250.69, -41.94, 0.00599, 46, 89.28, 14.85, 0.99401, 2, 45, 277.44, -67.97, 0.00094, 46, 126.29, 10.02, 0.99906, 1, 46, 169.44, -4.15, 1], "hull": 31, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 0, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 28], "width": 363, "height": 478}}, "maofa": {"maofa": {"x": 112.87, "y": 49.45, "rotation": -75.1, "width": 103, "height": 155}}, "zikzizi": {"zikzizi": {"width": 194, "height": 103}}, "tx_qua2": {"tx_qua": {"color": "6cff00ff", "width": 134, "height": 134}}, "zha/dd": {"zha/dd": {"width": 50, "height": 50}}, "zikzizi2": {"zikzizi": {"width": 194, "height": 103}}, "rootcut": {"rootcut": {"type": "clipping", "end": "rootcut", "vertexCount": 4, "vertices": [-383.21, 1604.34, 390.42, 1604.48, 379.95, -7.36, -377.27, -6.47], "color": "ce3a3aff"}}, "zhiliao1/yzzl_skill_zd_0": {"zhiliao1/yzzl_skill_zd_00000": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [80, -80, -80, -80, -80, 80, 80, 80], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 160, "height": 160}, "zhiliao1/yzzl_skill_zd_00001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [80, -80, -80, -80, -80, 80, 80, 80], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 160, "height": 160}, "zhiliao1/yzzl_skill_zd_00002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [80, -80, -80, -80, -80, 80, 80, 80], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 160, "height": 160}, "zhiliao1/yzzl_skill_zd_00003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [80, -80, -80, -80, -80, 80, 80, 80], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 160, "height": 160}, "zhiliao1/yzzl_skill_zd_00004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [80, -80, -80, -80, -80, 80, 80, 80], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 160, "height": 160}, "zhiliao1/yzzl_skill_zd_00005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [80, -80, -80, -80, -80, 80, 80, 80], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 160, "height": 160}, "zhiliao1/yzzl_skill_zd_00006": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [80, -80, -80, -80, -80, 80, 80, 80], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 160, "height": 160}, "zhiliao1/yzzl_skill_zd_00007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [80, -80, -80, -80, -80, 80, 80, 80], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 160, "height": 160}, "zhiliao1/yzzl_skill_zd_00008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [80, -80, -80, -80, -80, 80, 80, 80], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 160, "height": 160}, "zhiliao1/yzzl_skill_zd_00009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [80, -80, -80, -80, -80, 80, 80, 80], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 160, "height": 160}}, "zha/quan2": {"zha/quan2": {"width": 200, "height": 200}}, "zhiliao1/yzzl_skill_zd_00000": {"zhiliao1/yzzl_skill_zd_00000": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [80, -80, -80, -80, -80, 80, 80, 80], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 160, "height": 160}, "zhiliao1/yzzl_skill_zd_00001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [80, -80, -80, -80, -80, 80, 80, 80], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 160, "height": 160}, "zhiliao1/yzzl_skill_zd_00002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [80, -80, -80, -80, -80, 80, 80, 80], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 160, "height": 160}, "zhiliao1/yzzl_skill_zd_00003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [80, -80, -80, -80, -80, 80, 80, 80], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 160, "height": 160}, "zhiliao1/yzzl_skill_zd_00004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [80, -80, -80, -80, -80, 80, 80, 80], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 160, "height": 160}, "zhiliao1/yzzl_skill_zd_00005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [80, -80, -80, -80, -80, 80, 80, 80], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 160, "height": 160}, "zhiliao1/yzzl_skill_zd_00006": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [80, -80, -80, -80, -80, 80, 80, 80], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 160, "height": 160}, "zhiliao1/yzzl_skill_zd_00007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [80, -80, -80, -80, -80, 80, 80, 80], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 160, "height": 160}, "zhiliao1/yzzl_skill_zd_00008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [80, -80, -80, -80, -80, 80, 80, 80], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 160, "height": 160}, "zhiliao1/yzzl_skill_zd_00009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [80, -80, -80, -80, -80, 80, 80, 80], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 160, "height": 160}}, "danjifuhuo010": {"danjifuhuo010": {"type": "mesh", "uvs": [0.33123, 0.12366, 0.3792, 0.05888, 0.46204, 0.00905, 0.5696, 0, 0.66698, 0.00905, 0.76581, 0.05888, 0.84429, 0.15356, 0.88354, 0.26983, 0.8908, 0.37614, 0.87627, 0.46916, 0.92569, 0.53726, 0.98818, 0.6253, 1, 0.72663, 0.98528, 0.81632, 0.88935, 0.88277, 0.79924, 0.94755, 0.68587, 0.99904, 0.59866, 1, 0.46495, 0.98077, 0.32251, 0.95419, 0.21641, 0.91931, 0.11467, 0.83792, 0.01584, 0.76317, 0, 0.63527, 0.01875, 0.51567, 0.09142, 0.42763, 0.16409, 0.36285, 0.20188, 0.25322, 0.276, 0.17847, 0.62422, 0.13423, 0.57196, 0.27756, 0.52885, 0.47165, 0.50403, 0.62841, 0.47006, 0.80907, 0.76792, 0.19097, 0.74701, 0.33429, 0.71044, 0.49554, 0.71044, 0.6747, 0.71697, 0.82997, 0.8463, 0.66872, 0.86459, 0.79563, 0.44524, 0.09691, 0.41258, 0.23725, 0.37078, 0.38207, 0.31591, 0.53137, 0.26366, 0.70306, 0.0873, 0.62095, 0.18528, 0.46418, 0.25713, 0.2895], "triangles": [34, 5, 6, 29, 4, 5, 29, 3, 4, 3, 41, 2, 18, 19, 33, 33, 19, 45, 19, 20, 45, 20, 21, 45, 22, 46, 21, 21, 46, 45, 22, 23, 46, 23, 24, 46, 46, 24, 25, 33, 45, 32, 46, 47, 45, 45, 44, 32, 45, 47, 44, 46, 25, 47, 44, 47, 43, 25, 26, 47, 47, 26, 48, 26, 27, 48, 32, 31, 36, 32, 44, 31, 44, 43, 31, 31, 43, 30, 30, 43, 42, 47, 48, 43, 43, 48, 42, 48, 28, 42, 48, 27, 28, 28, 0, 42, 42, 41, 30, 30, 41, 29, 3, 29, 41, 42, 0, 41, 41, 0, 1, 41, 1, 2, 38, 16, 17, 17, 18, 33, 38, 17, 33, 16, 38, 15, 37, 33, 32, 33, 37, 38, 15, 40, 14, 15, 38, 40, 14, 40, 13, 40, 37, 39, 40, 38, 37, 13, 40, 12, 40, 39, 12, 39, 11, 12, 37, 36, 39, 32, 36, 37, 39, 10, 11, 39, 9, 10, 39, 36, 9, 36, 35, 9, 35, 36, 30, 36, 31, 30, 9, 35, 8, 35, 7, 8, 35, 30, 34, 35, 34, 7, 34, 30, 29, 34, 6, 7, 29, 5, 34], "vertices": [4, 77, 80.33, -40.89, 0.4601, 78, -8.02, -40.48, 0.30697, 79, -96.44, -14.93, 0.00092, 4, 173.96, 131.82, 0.232, 3, 77, 50.17, -43.67, 0.62594, 78, -38.3, -41.47, 0.03006, 4, 201.82, 119.92, 0.344, 1, 4, 228.76, 92.85, 1, 1, 4, 244.75, 51.85, 1, 1, 4, 253.35, 12.93, 1, 1, 4, 248.2, -30.84, 1, 1, 4, 225.32, -71.39, 1, 4, 81, 109.34, 26.78, 0.34903, 82, 24.63, 44.88, 0.41029, 83, -33.01, 64.47, 0.00068, 4, 190.35, -98.9, 0.24, 3, 81, 142.45, 7.98, 0.07991, 82, 62.64, 42.87, 0.76336, 83, 0.85, 47.09, 0.15673, 3, 81, 166.64, -15.53, 0.00277, 82, 94.79, 32.65, 0.15069, 83, 26.02, 24.63, 0.84654, 1, 83, 57.29, 29.04, 1, 2, 83, 97.43, 34.27, 0.7369, 84, 2.47, 36.84, 0.2631, 2, 83, 130.75, 19.41, 0.08636, 84, 38.53, 31.24, 0.91364, 1, 84, 67.55, 16.43, 1, 4, 84, 79.24, -27.82, 0.96228, 78, 40.41, 310.16, 0.0124, 79, 42.51, 310.63, 0.01249, 80, 16.84, 315.16, 0.01283, 5, 83, 154.95, -91.7, 0.01111, 84, 91.04, -69.62, 0.85574, 78, 82.21, 298.38, 0.04249, 79, 79.75, 288.27, 0.04387, 80, 48.37, 285.29, 0.04679, 5, 83, 146.34, -140.72, 0.03656, 84, 95.6, -119.19, 0.70602, 78, 126.86, 276.39, 0.07588, 79, 117.04, 255.3, 0.08433, 80, 77.66, 245.04, 0.09721, 6, 82, 267.91, -104.36, 0.00076, 83, 127.98, -171.2, 0.0518, 84, 85.87, -153.41, 0.59149, 78, 150.97, 250.22, 0.09633, 79, 133.42, 223.71, 0.11544, 80, 86.82, 210.66, 0.14418, 6, 82, 253.98, -157.56, 0.0035, 83, 93.52, -214.05, 0.05666, 84, 63.86, -203.8, 0.38788, 78, 182.45, 205.13, 0.11385, 79, 151.93, 171.94, 0.1684, 80, 93.71, 156.11, 0.26972, 6, 82, 236.99, -213.94, 0.00216, 83, 54.96, -258.55, 0.03495, 84, 38.34, -256.87, 0.19414, 78, 214.37, 155.65, 0.08547, 79, 169.71, 115.8, 0.17743, 80, 98.94, 97.46, 0.50585, 6, 82, 219, -255.23, 0.00021, 83, 21.66, -288.88, 0.01556, 84, 14.16, -294.87, 0.08509, 78, 234.15, 115.18, 0.04182, 79, 178.15, 71.56, 0.1123, 80, 97.62, 52.43, 0.74502, 5, 83, -24.85, -308.99, 0.0009, 84, -25.45, -326.48, 0.0053, 78, 240.42, 64.9, 0.00239, 79, 170.97, 21.4, 0.00683, 80, 79.78, 5.01, 0.98459, 1, 80, 63.93, -40.68, 1, 2, 79, 127.55, -53.02, 0.0098, 80, 21.31, -58.27, 0.9902, 2, 79, 86, -65.48, 0.2555, 80, -21.95, -61.46, 0.7445, 2, 79, 44.57, -53.22, 0.73035, 80, -59.76, -40.53, 0.26965, 3, 78, 101.06, -33.84, 0.01821, 79, 10.55, -37.21, 0.95182, 80, -89.52, -17.55, 0.02997, 2, 78, 61.68, -48.63, 0.60814, 79, -31.34, -41.13, 0.39186, 3, 77, 110.12, -42.72, 0.10637, 78, 21.61, -44.08, 0.84933, 79, -68.8, -26.19, 0.0443, 4, 81, 9.98, -33.77, 0.50012, 82, -37.17, -53.71, 0.13416, 77, -2.28, 45.58, 0.36146, 78, -85.36, 50.74, 0.00426, 7, 81, 40.44, -80.09, 0.05207, 82, 10.78, -81.52, 0.41278, 83, -97.33, -45.21, 0.00846, 84, -164.61, -90.97, 0.00498, 77, 48.79, 67.14, 0.37997, 78, -33.1, 69.23, 0.14143, 79, -91.78, 97.52, 0.00032, 7, 82, 77.18, -108, 0.28336, 83, -47.56, -96.53, 0.11424, 84, -103.11, -127.42, 0.07204, 77, 109.89, 104.24, 0.07723, 78, 30.11, 102.63, 0.38729, 79, -22.01, 113.12, 0.05762, 80, -88.83, 136.25, 0.00821, 7, 82, 131.35, -125.34, 0.11279, 83, -5.22, -134.5, 0.14773, 84, -52.29, -152.96, 0.1964, 77, 156.34, 137.06, 0.00995, 78, 78.42, 132.63, 0.29332, 79, 32.5, 129.36, 0.17467, 80, -32.1, 140.33, 0.06514, 7, 82, 193.48, -147.49, 0.0227, 83, 42.42, -180.12, 0.08383, 84, 5.66, -184.48, 0.31902, 77, 211.43, 173.35, 0, 78, 135.57, 165.59, 0.15643, 79, 96.3, 146.12, 0.20205, 80, 33.81, 142.91, 0.21596, 1, 81, 59.6, 3.46, 1, 5, 82, 40.18, -13.35, 0.97398, 84, -124.99, -28.18, 0.00065, 77, 12.04, 131.64, 0.01457, 78, -65.95, 135.8, 0.01066, 79, -105.96, 170.39, 0.00014, 7, 82, 95.3, -35.66, 0.34881, 83, -1.45, -37.92, 0.46919, 84, -73.99, -58.77, 0.07078, 77, 63.03, 162.24, 0.01721, 78, -13.23, 163.32, 0.07546, 79, -47.86, 183.07, 0.01426, 80, -98.95, 210.14, 0.00428, 7, 82, 158.71, -44, 0.03285, 83, 53.01, -71.46, 0.35227, 84, -12.64, -76.85, 0.4547, 77, 107.85, 207.87, 0.00151, 78, 34.23, 206.2, 0.08404, 79, 9.21, 211.95, 0.04556, 80, -37, 226.01, 0.02908, 6, 82, 214.02, -48.59, 0.00323, 83, 101.61, -98.27, 0.08286, 84, 41.29, -89.96, 0.72668, 78, 73.57, 245.34, 0.06758, 79, 57.46, 239.37, 0.0609, 80, 16.04, 242.35, 0.05874, 5, 83, 80.27, -23.15, 0.28689, 84, 0.98, -23.07, 0.69479, 78, -4.52, 245.9, 0.00899, 79, -17.73, 260.45, 0.00545, 80, -52.82, 279.18, 0.00388, 5, 83, 122.76, -40.55, 0.00315, 84, 46.55, -28.72, 0.96664, 78, 24.1, 281.81, 0.01102, 79, 19.32, 287.57, 0.00985, 80, -10.78, 297.65, 0.00933, 2, 77, 40.46, -15.1, 1, 78, -46.29, -12.37, 0, 6, 81, -7.9, -125.9, 0.00013, 82, -11.97, -144.11, 0.02089, 83, -143.69, -93.03, 0.00106, 84, -196.79, -149.28, 0.00061, 77, 85.08, 11.3, 0.54058, 78, -0.18, 11.33, 0.43672, 6, 82, 37.06, -167.77, 0.05292, 83, -108.61, -134.67, 0.01411, 84, -152.02, -180.25, 0.0112, 77, 133.48, 36.22, 0.01935, 78, 49.62, 33.34, 0.8923, 79, -21.42, 41.13, 0.01011, 7, 82, 86.98, -196.92, 0.03194, 83, -74.97, -181.68, 0.02336, 84, -107.22, -216.79, 0.03183, 77, 186.81, 58.55, 0.00051, 78, 104.17, 52.46, 0.37948, 79, 36.25, 45.23, 0.52423, 80, -46.61, 57.38, 0.00865, 6, 82, 144.97, -226.05, 0.00921, 83, -33.96, -231.98, 0.01914, 84, -54.45, -254.56, 0.05575, 78, 163.95, 77.73, 0.08766, 79, 100.56, 53.89, 0.45519, 80, 18.05, 51.94, 0.37305, 2, 79, 106.9, -23.55, 0.05315, 80, 7.51, -25.04, 0.94685, 2, 79, 38.92, -13.16, 0.95973, 80, -56.62, -0.2, 0.04027, 2, 78, 56.18, -23.23, 0.79027, 79, -29.96, -15.16, 0.20973], "hull": 29, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 0, 56, 8, 58, 58, 60, 60, 62, 62, 64, 64, 66, 10, 68, 68, 70, 70, 72, 72, 74, 74, 76, 18, 78, 78, 80, 4, 82, 82, 84, 84, 86, 86, 88, 88, 90, 44, 92, 92, 94, 94, 96], "width": 408, "height": 357}}, "danjifuhuo011": {"danjifuhuo011": {"type": "mesh", "uvs": [0.40666, 0.083, 0.45235, 0.05244, 0.48953, 0.01277, 0.57347, 0, 0.64572, 0, 0.6946, 0.02642, 0.69141, 0.06024, 0.67228, 0.096, 0.77322, 0.10901, 0.87628, 0.14347, 0.8901, 0.20525, 0.82953, 0.27028, 0.87505, 0.32641, 0.85947, 0.39888, 0.79248, 0.45514, 0.71457, 0.47135, 0.67123, 0.53182, 0.66354, 0.57551, 0.72238, 0.64813, 0.82315, 0.71954, 0.94513, 0.79636, 1, 0.86885, 1, 0.93918, 0.95827, 0.95261, 0.89917, 0.97164, 0.81256, 0.98415, 0.70294, 1, 0.54913, 1, 0.35113, 0.99219, 0.14606, 0.98246, 0.02938, 0.97596, 0, 0.87426, 0, 0.77364, 0.03822, 0.69465, 0.08772, 0.59728, 0.17965, 0.52695, 0.25213, 0.49665, 0.25567, 0.44796, 0.21324, 0.3798, 0.18849, 0.31488, 0.12662, 0.24564, 0.12662, 0.16557, 0.17965, 0.1039, 0.26804, 0.0855, 0.3476, 0.09957, 0.58625, 0.04554, 0.5495, 0.0865, 0.54031, 0.14755, 0.5705, 0.20539, 0.62562, 0.2817, 0.62037, 0.37729, 0.57575, 0.44316, 0.50094, 0.50581, 0.45894, 0.57891, 0.46681, 0.67049, 0.50246, 0.76413, 0.56898, 0.86287, 0.27745, 0.1821, 0.36094, 0.24879, 0.40057, 0.34753, 0.38925, 0.43847, 0.3666, 0.50343, 0.3284, 0.54934, 0.25622, 0.62815, 0.24207, 0.71909, 0.22367, 0.8187, 0.21235, 0.89665, 0.76569, 0.18123, 0.73172, 0.26698, 0.77418, 0.35706, 0.71898, 0.42288, 0.64256, 0.46619, 0.59162, 0.52595, 0.57181, 0.59091, 0.61002, 0.65933, 0.67511, 0.74508, 0.77701, 0.83775, 0.83644, 0.9209], "triangles": [29, 30, 31, 65, 32, 64, 31, 32, 65, 29, 66, 28, 66, 31, 65, 65, 56, 66, 66, 29, 31, 62, 35, 36, 64, 32, 33, 64, 33, 63, 64, 63, 54, 63, 62, 53, 63, 35, 62, 63, 33, 34, 34, 35, 63, 77, 26, 56, 28, 66, 56, 56, 75, 76, 27, 28, 56, 26, 27, 56, 54, 53, 73, 63, 53, 54, 55, 54, 74, 64, 54, 55, 55, 65, 64, 56, 65, 55, 22, 23, 21, 76, 19, 20, 77, 25, 26, 21, 76, 20, 21, 77, 76, 77, 56, 76, 23, 77, 21, 24, 77, 23, 25, 77, 24, 74, 17, 18, 76, 75, 19, 74, 73, 17, 54, 73, 74, 75, 74, 18, 55, 74, 75, 56, 55, 75, 75, 18, 19, 67, 8, 9, 67, 9, 10, 48, 7, 67, 68, 48, 67, 11, 68, 67, 67, 7, 8, 10, 11, 67, 46, 0, 1, 45, 1, 2, 47, 0, 46, 46, 1, 45, 46, 45, 7, 47, 46, 7, 45, 3, 4, 45, 4, 5, 2, 3, 45, 6, 45, 5, 7, 45, 6, 57, 44, 58, 58, 40, 57, 57, 43, 44, 42, 43, 57, 41, 42, 57, 40, 41, 57, 47, 44, 0, 58, 39, 40, 13, 69, 12, 48, 47, 7, 47, 58, 44, 58, 47, 48, 49, 48, 68, 49, 59, 58, 49, 58, 48, 39, 58, 59, 50, 59, 49, 49, 68, 69, 50, 49, 69, 70, 50, 69, 70, 69, 13, 60, 38, 59, 51, 60, 59, 50, 51, 59, 71, 51, 50, 70, 71, 50, 15, 71, 70, 61, 37, 60, 52, 60, 51, 61, 60, 52, 72, 51, 71, 52, 51, 72, 72, 71, 16, 62, 36, 61, 53, 61, 52, 73, 53, 52, 62, 61, 53, 72, 73, 52, 69, 68, 11, 73, 72, 17, 37, 38, 60, 38, 39, 59, 14, 70, 13, 15, 70, 14, 36, 37, 61, 16, 71, 15, 17, 72, 16, 69, 11, 12], "vertices": [5, 3, 161.5, 32.44, 0.25848, 5, 23.53, -27.28, 0.22857, 4, 3.15, 33.29, 0.30491, 12, 16.24, 79.55, 4e-05, 54, 72.96, 45.84, 0.208, 5, 3, 174.07, 23.96, 0.09928, 5, 36.1, -35.75, 0.066, 4, 16.85, 26.79, 0.62663, 12, 28.81, 71.08, 9e-05, 54, 85.53, 37.36, 0.208, 5, 3, 189.66, 17.86, 0.00665, 5, 51.69, -41.85, 0.01159, 4, 33.18, 23.1, 0.77373, 12, 44.4, 64.98, 4e-05, 54, 101.12, 31.26, 0.208, 3, 5, 59.08, -59.76, 0.00018, 4, 43.17, 6.51, 0.79182, 54, 108.51, 13.35, 0.208, 3, 4, 47.93, -8.96, 0.78982, 12, 54.17, 31.06, 0.00218, 54, 110.89, -2.66, 0.208, 3, 4, 41.91, -22.27, 0.77347, 12, 46.22, 18.81, 0.01853, 54, 102.94, -14.91, 0.208, 4, 3, 179.14, -29.43, 0.00296, 4, 29.87, -25.23, 0.72533, 12, 33.87, 17.69, 0.06371, 54, 90.6, -16.03, 0.208, 4, 3, 165.56, -27.12, 0.05809, 4, 16.1, -24.98, 0.47092, 12, 20.3, 20, 0.26299, 54, 77.02, -13.72, 0.208, 4, 3, 164.18, -50.18, 0.0013, 4, 18.2, -47.99, 0.0448, 12, 18.92, -3.07, 0.7459, 54, 75.64, -36.78, 0.208, 3, 4, 12.93, -73.77, 0, 12, 9.84, -27.76, 0.792, 54, 66.56, -61.47, 0.208, 3, 3, 133.2, -81.26, 0.04417, 12, -12.07, -34.15, 0.74783, 54, 44.66, -67.86, 0.208, 4, 3, 107.66, -71.35, 0.29657, 12, -37.6, -24.24, 0.49473, 19, -79.88, 128.36, 0.0007, 54, 19.12, -57.95, 0.208, 4, 3, 88.84, -84.46, 0.46188, 12, -56.42, -37.35, 0.3263, 19, -57.6, 122.94, 0.00382, 54, 0.3, -71.06, 0.208, 4, 3, 62.09, -84.92, 0.55226, 12, -83.17, -37.8, 0.22829, 19, -39.54, 103.2, 0.01145, 54, -26.45, -71.52, 0.208, 4, 3, 39.51, -73.11, 0.61314, 12, -105.75, -25.99, 0.15152, 19, -33.43, 78.47, 0.02734, 54, -49.03, -59.71, 0.208, 4, 3, 31.07, -56.72, 0.64407, 12, -114.19, -9.61, 0.08538, 19, -40.12, 61.29, 0.06255, 54, -57.47, -43.32, 0.208, 5, 3, 7.75, -50.38, 0.54829, 12, -137.51, -3.26, 0.01968, 19, -29.43, 39.62, 0.22123, 21, -37.07, 55.38, 0.0028, 54, -80.79, -36.98, 0.208, 5, 3, -8.31, -51.03, 0.32246, 12, -153.58, -3.92, 0.00463, 19, -18.29, 28.01, 0.45076, 21, -21.68, 50.7, 0.01415, 54, -96.85, -37.63, 0.208, 4, 3, -32.66, -67.98, 0.01728, 12, -177.92, -20.87, 4e-05, 19, 10.53, 21, 0.77468, 54, -121.2, -54.58, 0.208, 3, 19, 45.06, 21.46, 0.64851, 20, -6.38, 23.85, 0.14349, 54, -143.73, -80.76, 0.208, 3, 19, 84.17, 24.29, 0.00106, 20, 32.39, 17.99, 0.79094, 54, -167.51, -111.93, 0.208, 2, 20, 58.22, 4.29, 0.792, 54, -191.94, -127.99, 0.208, 3, 20, 73.79, -16.2, 0.7566, 22, 77.8, 93.87, 0.0354, 54, -217.4, -131.78, 0.208, 3, 20, 69.32, -25.77, 0.7278, 22, 80.06, 83.55, 0.0642, 54, -223.64, -123.26, 0.208, 3, 20, 62.99, -39.33, 0.64342, 22, 83.25, 68.94, 0.14858, 54, -232.48, -111.19, 0.208, 3, 20, 50.31, -54.71, 0.47157, 22, 82.52, 49.02, 0.32043, 54, -239.87, -92.68, 0.208, 3, 20, 34.26, -74.18, 0.20507, 22, 81.59, 23.81, 0.58693, 54, -249.22, -69.25, 0.208, 3, 22, 72.43, -9.41, 0.78132, 24, 65.1, 109.34, 0.01068, 54, -254.3, -35.17, 0.208, 3, 22, 57.9, -51.41, 0.50817, 24, 70.18, 65.19, 0.28383, 54, -258.01, 9.12, 0.208, 3, 22, 42.26, -94.75, 0.08284, 24, 74.86, 19.35, 0.70916, 54, -261.25, 55.08, 0.208, 2, 24, 77.17, -6.79, 0.792, 54, -262.75, 81.28, 0.208, 2, 24, 41.72, -19.89, 0.792, 54, -226.9, 93.27, 0.208, 3, 23, 71.69, -25.49, 0.20778, 24, 5.48, -26.45, 0.58422, 54, -190.47, 98.7, 0.208, 3, 23, 41.55, -26.22, 0.7649, 24, -24.49, -23.17, 0.0271, 54, -160.62, 94.49, 0.208, 3, 3, -35.2, 75.37, 0.00021, 23, 4.23, -26.62, 0.79179, 54, -123.74, 88.77, 0.208, 4, 3, -6.7, 58.79, 0.18094, 5, -144.67, -0.92, 0.00046, 23, -26.59, -14.94, 0.6106, 54, -95.24, 72.19, 0.208, 5, 3, 6.66, 44.37, 0.48099, 5, -131.31, -15.34, 0.00684, 21, -67.28, -34.43, 0.00117, 23, -42.13, -2.89, 0.303, 54, -81.88, 57.77, 0.208, 4, 3, 24.4, 46.21, 0.6615, 5, -113.57, -13.5, 0.03736, 23, -59.34, -7.62, 0.09314, 54, -64.14, 59.61, 0.208, 4, 3, 47.67, 59.29, 0.63027, 5, -90.3, -0.43, 0.14732, 23, -80.16, -24.33, 0.01442, 54, -40.87, 72.69, 0.208, 4, 3, 70.36, 68.27, 0.4556, 5, -67.61, 8.56, 0.33524, 23, -101.06, -36.9, 0.00116, 54, -18.18, 81.67, 0.208, 3, 3, 93.38, 85.71, 0.19726, 5, -44.59, 26, 0.59474, 54, 4.84, 99.11, 0.208, 3, 3, 122.37, 90.03, 0.03282, 5, -15.6, 30.32, 0.75918, 54, 33.83, 103.43, 0.208, 2, 5, 8.47, 21.89, 0.792, 54, 57.9, 95, 0.208, 5, 3, 156.02, 63.01, 0.00731, 5, 18.05, 3.3, 0.77853, 4, -6.86, 62.69, 0.00616, 12, 10.76, 110.13, 0, 54, 67.48, 76.41, 0.208, 5, 3, 153.55, 44.63, 0.17566, 5, 15.58, -15.08, 0.53317, 4, -6.54, 44.15, 0.08316, 12, 8.29, 91.74, 1e-05, 54, 65.01, 58.03, 0.208, 3, 4, 28.09, -1.13, 0.71076, 12, 35.73, 41.78, 0.00124, 53, 146.65, 16.14, 0.288, 3, 5, 26.98, -59.11, 0.00293, 4, 11.33, 2.33, 0.70907, 53, 130.61, 22.07, 0.288, 4, 3, 142.54, -0.66, 0.70863, 4, -10.63, -2.28, 0.00151, 12, -2.72, 46.46, 0.00186, 53, 108.21, 20.82, 0.288, 3, 3, 122.6, -10.46, 0.64048, 12, -22.66, 36.65, 0.07152, 53, 88.26, 11.01, 0.288, 4, 3, 96.79, -26.79, 0.55918, 12, -48.47, 20.32, 0.15255, 19, -106.07, 90.7, 0.00026, 53, 62.46, -5.32, 0.288, 4, 3, 62.02, -30.78, 0.62651, 12, -83.25, 16.33, 0.07655, 19, -80.05, 67.29, 0.00894, 53, 27.68, -9.31, 0.288, 4, 3, 36.7, -24.45, 0.65655, 12, -108.56, 22.67, 0.02594, 19, -68.03, 44.13, 0.02951, 53, 2.36, -2.97, 0.288, 5, 3, 11.55, -11.25, 0.67187, 12, -133.71, 35.87, 0.00237, 19, -61.26, 16.54, 0.03526, 21, -53.55, 19.69, 0.0025, 53, -22.79, 10.22, 0.288, 6, 3, -16.3, -5.88, 0.43384, 12, -161.56, 41.23, 4e-05, 19, -46.83, -7.87, 0.05415, 21, -29.03, 5.44, 0.19976, 23, -27.72, 50.44, 0.02422, 53, -50.64, 15.59, 0.288, 3, 19, -20.04, -28.09, 0.00688, 21, 4.22, 0.91, 0.70512, 53, -83.53, 8.91, 0.288, 4, 19, 11.34, -44.03, 0.01232, 21, 39.39, 2.34, 0.69657, 22, -13.57, 3.44, 0.00312, 53, -116.25, -4.04, 0.288, 5, 19, 48.59, -55.88, 0.0248, 20, -19.99, -52.36, 0.01865, 21, 77.67, 10.21, 0.00027, 22, 25.23, 8.21, 0.66828, 53, -149.8, -24.1, 0.288, 3, 3, 121.36, 55.72, 0.19414, 5, -16.61, -3.99, 0.80574, 4, -40.03, 50.28, 0.00012, 3, 3, 99.97, 33.63, 0.55941, 5, -38, -26.09, 0.26459, 53, 65.64, 55.1, 0.176, 4, 3, 65.54, 19.52, 0.76701, 5, -72.43, -40.19, 0.05597, 23, -104.29, 11.98, 0.00101, 53, 31.2, 41, 0.176, 4, 3, 32.24, 17.13, 0.7873, 5, -105.73, -42.58, 0.01319, 23, -71.84, 19.79, 0.0235, 53, -2.09, 38.6, 0.176, 5, 3, 7.98, 18.64, 0.70815, 5, -129.99, -41.07, 0.0029, 21, -60.04, -9.7, 0.0042, 23, -47.65, 22.27, 0.10875, 53, -26.36, 40.11, 0.176, 5, 3, -9.9, 24.63, 0.46156, 5, -147.87, -35.08, 0.00054, 21, -45.14, -21.25, 0.06615, 23, -29.04, 19.29, 0.29575, 53, -44.24, 46.11, 0.176, 4, 3, -40.81, 36.37, 0.03536, 21, -19.82, -42.53, 0.11522, 23, 3.38, 12.77, 0.67342, 53, -75.15, 57.85, 0.176, 5, 21, 12.28, -51.88, 0.18501, 22, -44.95, -48.41, 0.00714, 23, 36.03, 19.98, 0.60195, 24, -23.82, 23.35, 0.0299, 53, -108.54, 56.08, 0.176, 5, 21, 47.32, -62.75, 0.1036, 22, -10.9, -62.07, 0.09346, 23, 71.99, 27.26, 0.13208, 24, 12.78, 25.79, 0.49486, 53, -145.2, 54.78, 0.176, 5, 21, 74.87, -70.57, 0.02493, 22, 15.93, -72.09, 0.16456, 23, 99.92, 33.61, 0.00542, 24, 41.31, 28.37, 0.62909, 53, -173.79, 53.09, 0.176, 2, 3, 137.79, -52.41, 0.05312, 12, -7.48, -5.29, 0.94688, 4, 3, 105.62, -49.5, 0.39769, 12, -39.64, -2.39, 0.42589, 19, -94.91, 112.36, 0.00042, 53, 71.29, -28.03, 0.176, 4, 3, 74.42, -63.77, 0.55728, 12, -70.85, -16.65, 0.25892, 19, -63.55, 98.43, 0.00781, 53, 40.08, -42.29, 0.176, 4, 3, 48.77, -55.09, 0.6586, 12, -96.5, -7.97, 0.13547, 19, -53.06, 73.46, 0.02993, 53, 14.43, -33.61, 0.176, 4, 3, 30.57, -40.49, 0.6963, 12, -114.69, 6.62, 0.05176, 19, -51.95, 50.16, 0.07595, 53, -3.77, -19.02, 0.176, 5, 3, 7.25, -32.43, 0.61482, 12, -138.01, 14.69, 0.01182, 19, -42.54, 27.35, 0.18558, 21, -42.51, 38.27, 0.01179, 53, -27.08, -10.95, 0.176, 5, 3, -16.92, -31.54, 0.32662, 12, -162.18, 15.58, 0.0016, 19, -27.2, 8.66, 0.37465, 21, -19.99, 29.46, 0.12112, 53, -51.25, -10.07, 0.176, 5, 3, -40.43, -43.69, 0.00768, 12, -185.69, 3.42, 1e-05, 19, -2.52, -0.91, 0.7868, 21, 6.21, 33.18, 0.02951, 53, -74.76, -22.22, 0.176, 4, 19, 30.88, -9.94, 0.72726, 21, 39.77, 41.63, 0.05983, 22, -10.02, 42.58, 0.0369, 53, -103.65, -41.27, 0.176, 5, 19, 71.53, -14.29, 0.07069, 20, 11.56, -16.85, 0.62456, 21, 77.36, 57.71, 0.00015, 22, 28.74, 55.57, 0.12861, 53, -133.84, -68.84, 0.176, 3, 19, 103.4, -23.68, 0.00011, 20, 40.57, -33.04, 0.75781, 22, 61.62, 60.33, 0.24208], "hull": 45, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 48, 50, 50, 52, 44, 46, 46, 48, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 0, 88, 8, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154], "width": 224, "height": 366}}, "danjifuhuo012": {"danjifuhuo012": {"type": "mesh", "uvs": [0.67976, 0.1326, 0.69439, 0.07719, 0.74481, 0.02008, 0.82774, 0, 0.93182, 0, 1, 0.01155, 1, 0.07037, 1, 0.12663, 0.97248, 0.15902, 0.92369, 0.20505, 0.88629, 0.25876, 0.87653, 0.29285, 0.87653, 0.31587, 0.8375, 0.36872, 0.78384, 0.40708, 0.78709, 0.47442, 0.78793, 0.54449, 0.77768, 0.61614, 0.75034, 0.63227, 0.77426, 0.67884, 0.82381, 0.73706, 0.8221, 0.80603, 0.83065, 0.8741, 0.62902, 0.9359, 0.51795, 0.94486, 0.42738, 0.87589, 0.46327, 0.80513, 0.51935, 0.76336, 0.54585, 0.72631, 0.52507, 0.73152, 0.48224, 0.75503, 0.41333, 0.79207, 0.33969, 0.84989, 0.30968, 0.9071, 0.33287, 0.96072, 0.38334, 1, 0.27422, 1, 0.15145, 0.97145, 0.06824, 0.92783, 0.01777, 0.85704, 0, 0.78054, 0, 0.68401, 0.04505, 0.59821, 0.14463, 0.51455, 0.25785, 0.4645, 0.38471, 0.43662, 0.48019, 0.40444, 0.54968, 0.35272, 0.60877, 0.29161, 0.62953, 0.21292, 0.6487, 0.14092, 0.51933, 0.51094, 0.39317, 0.57205, 0.29095, 0.64405, 0.18075, 0.76376, 0.16478, 0.8525, 0.20311, 0.94459, 0.31517, 0.97988, 0.58949, 0.68634, 0.57403, 0.63833, 0.54905, 0.57288, 0.5443, 0.54607, 0.52417, 0.58136, 0.54915, 0.64195, 0.56413, 0.68459, 0.53488, 0.71377], "triangles": [36, 57, 35, 57, 34, 35, 37, 56, 36, 36, 56, 57, 57, 56, 34, 37, 38, 56, 56, 33, 34, 33, 56, 55, 56, 38, 55, 38, 39, 55, 33, 55, 32, 39, 40, 55, 55, 54, 32, 55, 40, 54, 32, 54, 31, 30, 31, 53, 40, 41, 54, 31, 54, 53, 54, 41, 53, 30, 53, 65, 29, 30, 65, 41, 42, 53, 52, 65, 53, 52, 63, 65, 63, 52, 62, 42, 43, 53, 53, 43, 52, 63, 62, 59, 60, 62, 61, 62, 52, 61, 43, 44, 52, 44, 45, 52, 52, 51, 61, 52, 45, 51, 61, 51, 15, 45, 46, 51, 58, 28, 65, 24, 25, 23, 25, 26, 23, 26, 27, 23, 23, 27, 21, 21, 27, 28, 23, 21, 22, 20, 21, 58, 58, 21, 28, 20, 58, 19, 29, 65, 28, 65, 63, 64, 58, 65, 64, 64, 59, 58, 58, 18, 19, 58, 59, 18, 62, 60, 59, 59, 60, 18, 18, 60, 17, 17, 60, 16, 60, 61, 16, 16, 61, 15, 15, 46, 14, 14, 46, 47, 46, 15, 51, 64, 63, 59, 13, 14, 48, 14, 47, 48, 13, 48, 12, 48, 11, 12, 11, 48, 10, 10, 48, 49, 9, 10, 49, 49, 50, 0, 9, 49, 0, 9, 0, 8, 8, 0, 1, 8, 1, 2, 7, 2, 6, 2, 7, 8, 3, 6, 2, 6, 3, 4, 4, 5, 6], "vertices": [1, 6, 32.05, -25.15, 1, 1, 6, 10.42, -29.08, 1, 1, 6, -14.14, -26.22, 1, 1, 6, -26.97, -12.45, 1, 1, 6, -33.66, 7.92, 1, 1, 6, -33.73, 22.68, 1, 1, 6, -11.77, 29.89, 1, 1, 6, 9.23, 36.79, 1, 1, 6, 23.1, 35.38, 1, 1, 6, 43.42, 31.47, 1, 2, 6, 65.88, 30.74, 0.99949, 7, -38.93, 44.48, 0.00051, 2, 6, 79.23, 33.01, 0.97868, 7, -25.51, 42.64, 0.02132, 2, 6, 87.83, 35.84, 0.93559, 7, -16.47, 42.75, 0.06441, 2, 6, 110.07, 34.68, 0.64809, 7, 4.4, 34.97, 0.35191, 2, 6, 127.84, 28.88, 0.19078, 7, 19.61, 24.1, 0.80922, 2, 6, 152.78, 37.77, 0.00031, 7, 46.07, 25.1, 0.99969, 1, 7, 73.6, 25.62, 1, 2, 7, 101.79, 23.85, 0.99949, 8, -31.29, 19.56, 0.00051, 2, 7, 108.19, 18.3, 0.98109, 8, -24.15, 14.99, 0.01891, 2, 7, 126.43, 23.46, 0.55532, 8, -6.84, 22.71, 0.44468, 2, 7, 149.18, 33.95, 0.0557, 8, 14.17, 36.36, 0.9443, 1, 8, 41, 40.24, 1, 1, 8, 67.15, 46.15, 1, 1, 8, 97.62, 8.91, 1, 1, 8, 104.66, -13.14, 1, 1, 8, 80.8, -35.79, 1, 1, 8, 52.18, -32.83, 1, 4, 7, 160.3, -28.64, 0.00164, 8, 34.16, -23.98, 0.99124, 9, 72.47, 69.23, 0.00706, 11, -7.69, 75.59, 6e-05, 2, 7, 145.67, -23.36, 0.26296, 8, 18.93, -20.86, 0.73704, 3, 9, 62.48, 61.6, 0.25023, 10, 14.14, 60.8, 0.71466, 11, -20, 78.15, 0.03511, 3, 9, 75.25, 61.4, 0.15189, 10, 25.87, 55.75, 0.79304, 11, -11.79, 68.36, 0.05507, 4, 7, 171.85, -50.34, 0.00899, 9, 95.58, 60.85, 0.03359, 10, 44.46, 47.51, 0.76206, 11, 1.11, 52.64, 0.19536, 3, 9, 122.57, 65.14, 4e-05, 10, 71.05, 41.21, 0.31903, 11, 22.02, 35.06, 0.68093, 2, 10, 94.27, 43.25, 0.02812, 11, 43.68, 26.43, 0.97188, 1, 11, 65.15, 28.85, 1, 1, 11, 81.64, 37.48, 1, 1, 11, 79.16, 15.14, 1, 1, 11, 65.21, -8.76, 1, 1, 11, 46.29, -23.9, 1, 2, 10, 96.79, -19.96, 0.05971, 11, 17.49, -31.16, 0.94029, 2, 10, 69.89, -33.87, 0.67258, 11, -12.8, -31.48, 0.32742, 3, 9, 122.19, -30.5, 0.0289, 10, 34.33, -47.09, 0.97003, 11, -50.5, -27.29, 0.00107, 2, 9, 91.12, -46.57, 0.3653, 10, -0.51, -50.15, 0.6347, 2, 9, 53.06, -53.82, 0.88835, 10, -38.47, -42.38, 0.11165, 2, 9, 22.78, -50.05, 0.99754, 10, -65.04, -27.37, 0.00246, 2, 7, 32.24, -57.97, 0.0935, 9, -3, -38.29, 0.9065, 3, 6, 146.38, -30.87, 0.00494, 7, 19.36, -38.46, 0.50857, 9, -25.64, -32.43, 0.48649, 3, 6, 122.6, -23.61, 0.31108, 7, -1.15, -24.39, 0.60605, 9, -50.28, -35.72, 0.08287, 3, 6, 95.98, -19.54, 0.99175, 7, -25.31, -12.52, 0.00785, 9, -76.18, -43.08, 0.00041, 1, 6, 65.27, -25.13, 1, 1, 6, 37.15, -30.21, 1, 2, 7, 61.11, -29.87, 0.02731, 9, -0.38, 1.9, 0.97269, 1, 9, 34.9, -0.89, 1, 4, 7, 114, -76.27, 0.00226, 8, -4.82, -77.77, 0.00315, 9, 69.97, 2.85, 0.70066, 10, -1.28, 3.61, 0.29393, 1, 10, 50.73, -1.27, 1, 1, 11, 19.06, -0.87, 1, 1, 11, 55.9, 2.98, 1, 1, 11, 72.23, 24.4, 1, 3, 7, 129.85, -14.57, 0.57636, 8, 2.01, -14.43, 0.41808, 11, -36.18, 93.3, 0.00555, 5, 7, 111.03, -17.98, 0.83379, 8, -16.13, -20.52, 0.15628, 9, 28.74, 44.15, 0.00738, 10, -23.7, 57.49, 0.0025, 11, -55.28, 92.22, 5e-05, 1, 7, 85.37, -23.45, 1, 5, 7, 74.85, -24.56, 0.13464, 8, -50.99, -32.22, 0.05052, 9, 6.27, 15.05, 0.77937, 10, -55.56, 39.11, 0.03481, 11, -92, 90.13, 0.00067, 3, 9, 19.27, 21.41, 0.91842, 10, -41.11, 40.06, 0.08004, 11, -78.67, 84.48, 0.00154, 4, 7, 112.51, -23.09, 0.448, 9, 33.27, 41.36, 0.3934, 10, -20.58, 53.18, 0.15511, 11, -54.44, 86.97, 0.00349, 4, 7, 129.23, -19.8, 0.68, 9, 43.48, 55, 0.16864, 10, -5.95, 61.91, 0.14839, 11, -37.44, 88.18, 0.00297, 4, 8, 14.41, -23.86, 0.74548, 9, 55.99, 58.35, 0.08159, 10, 6.89, 60.26, 0.16478, 11, -26.71, 80.93, 0.00815], "hull": 51, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 0, 100, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 56, 116, 116, 118, 118, 120, 120, 122, 102, 122, 122, 124, 124, 126, 126, 128, 128, 130, 58, 130, 56, 58, 58, 60], "width": 206, "height": 393}}, "danjifuhuo013": {"danjifuhuo013": {"type": "mesh", "uvs": [0.02301, 0, 0.06011, 0, 0.10703, 0.03116, 0.14195, 0.089, 0.17032, 0.15117, 0.19542, 0.10635, 0.21615, 0.18876, 0.26635, 0.22201, 0.30345, 0.22491, 0.36237, 0.19743, 0.43439, 0.17286, 0.50423, 0.1931, 0.57188, 0.24948, 0.64499, 0.28418, 0.7301, 0.30009, 0.81194, 0.32467, 0.88287, 0.37383, 0.94507, 0.43744, 0.98435, 0.53576, 0.99745, 0.64998, 1, 0.75697, 0.98653, 0.86685, 0.96362, 0.9536, 0.91779, 1, 0.90142, 0.92903, 0.85668, 0.84806, 0.78466, 0.80324, 0.70064, 0.7801, 0.60789, 0.783, 0.5075, 0.79023, 0.41366, 0.7801, 0.30999, 0.73384, 0.26089, 0.65143, 0.21179, 0.53865, 0.18451, 0.48371, 0.11685, 0.40563, 0.07648, 0.31888, 0.0241, 0.21623, 0.00664, 0.14249, 0, 0.07454, 0.00773, 0.02249, 0.03283, 0.04851, 0.06011, 0.10779, 0.09285, 0.17285, 0.13104, 0.24659, 0.17578, 0.3131, 0.22925, 0.36804, 0.29472, 0.40274, 0.36783, 0.42732, 0.42893, 0.44901, 0.50204, 0.4707, 0.56206, 0.50829, 0.64281, 0.53865, 0.71374, 0.56757, 0.79885, 0.63118, 0.88287, 0.69335, 0.93088, 0.76854, 0.94398, 0.87119, 0.93197, 0.94204, 0.34582, 0.32573, 0.48307, 0.31646], "triangles": [60, 9, 10, 60, 49, 59, 9, 60, 59, 49, 48, 59, 60, 10, 11, 49, 60, 50, 49, 32, 48, 47, 59, 48, 59, 8, 9, 23, 58, 22, 23, 24, 58, 58, 57, 22, 22, 57, 21, 58, 24, 57, 24, 25, 57, 21, 57, 56, 57, 25, 56, 21, 56, 20, 25, 55, 56, 25, 26, 55, 20, 56, 19, 56, 55, 19, 19, 55, 18, 55, 54, 18, 54, 17, 18, 26, 54, 55, 26, 27, 54, 27, 28, 53, 27, 53, 54, 53, 28, 52, 54, 16, 17, 16, 53, 15, 16, 54, 53, 53, 14, 15, 53, 52, 14, 28, 29, 51, 51, 29, 50, 28, 51, 52, 29, 30, 50, 30, 49, 50, 30, 31, 49, 49, 31, 32, 52, 13, 14, 52, 51, 13, 51, 12, 13, 51, 50, 12, 50, 60, 12, 60, 11, 12, 48, 33, 47, 48, 32, 33, 47, 34, 46, 47, 33, 34, 34, 35, 46, 46, 35, 45, 35, 36, 45, 47, 46, 59, 59, 7, 8, 7, 59, 46, 46, 45, 7, 36, 44, 45, 44, 37, 43, 44, 36, 37, 45, 6, 7, 45, 44, 6, 44, 4, 6, 44, 43, 4, 43, 38, 42, 43, 37, 38, 4, 5, 6, 43, 3, 4, 43, 42, 3, 42, 39, 41, 42, 38, 39, 42, 41, 2, 42, 2, 3, 2, 41, 1, 39, 40, 41, 40, 0, 41, 41, 0, 1], "vertices": [1, 13, -33.12, 1.44, 1, 1, 13, -25.72, 10.63, 1, 1, 13, -10.53, 17.55, 1, 1, 13, 7.24, 17.49, 1, 1, 13, 24.52, 15.16, 1, 2, 13, 21.15, 28.12, 1, 14, -75.14, 92.16, 0, 2, 13, 40.69, 20.84, 0.99682, 14, -66.06, 73.39, 0.00318, 3, 13, 56.92, 28.27, 0.85628, 14, -49.21, 67.53, 0.04772, 15, 68.26, 35.78, 0.096, 3, 13, 64.87, 37.02, 0.63587, 14, -37.42, 68.35, 0.11613, 15, 65.75, 24.23, 0.248, 3, 13, 71.49, 55.74, 0.2642, 14, -19.68, 77.3, 0.1518, 15, 69.35, 4.7, 0.584, 4, 13, 81.26, 77.28, 0.31062, 14, 2.27, 86.1, 0.39333, 17, -96.17, 66.46, 5e-05, 15, 71.63, -18.84, 0.296, 5, 13, 98.98, 91.52, 0.20979, 14, 24.92, 84.13, 0.48941, 17, -73.65, 69.56, 0.00453, 18, -159.95, -49.26, 0.00027, 15, 63.39, -40.03, 0.296, 4, 13, 123.02, 99.78, 0.14147, 14, 47.99, 73.48, 0.79329, 17, -48.79, 64.28, 0.05699, 18, -137.85, -36.72, 0.00825, 4, 13, 144.09, 112.66, 0.0399, 14, 72.12, 68.21, 0.69105, 17, -24.09, 64.47, 0.22384, 18, -119.51, -20.18, 0.0452, 4, 13, 164.04, 131.34, 0.00466, 14, 99.45, 67.9, 0.38025, 17, 2.63, 70.22, 0.46269, 18, -103.33, 1.86, 0.1524, 4, 13, 184.96, 147.91, 1e-05, 14, 126.02, 65.4, 0.1474, 17, 29.09, 73.66, 0.52132, 18, -85.82, 21.99, 0.33127, 3, 14, 149.91, 56.6, 0.03865, 17, 54.33, 70.36, 0.39416, 18, -64.75, 36.28, 0.56718, 3, 14, 171.48, 44, 0.00345, 17, 78.16, 62.85, 0.18673, 18, -41.94, 46.47, 0.80982, 2, 17, 98.02, 45.01, 0.02462, 18, -15.24, 46.31, 0.97538, 1, 18, 10.88, 37, 1, 1, 18, 33.88, 25.56, 1, 1, 18, 55.09, 9.31, 1, 1, 18, 69.98, -6.97, 1, 1, 18, 72.89, -25.07, 1, 2, 17, 105.81, -52.68, 0.00976, 18, 55.42, -21.6, 0.99024, 2, 17, 85.76, -39.34, 0.27492, 18, 31.57, -24.93, 0.72508, 3, 14, 132.18, -49.62, 0.00793, 17, 60.55, -37.14, 0.88962, 18, 11.25, -40.01, 0.10245, 2, 14, 104.97, -47.55, 0.15819, 17, 33.56, -41.14, 0.84181, 2, 14, 75.81, -52.03, 0.63006, 17, 6.11, -51.97, 0.36994, 2, 14, 44.37, -57.85, 0.9442, 17, -23.26, -64.6, 0.0558, 3, 13, 190.61, -19.3, 0.02464, 14, 14.47, -59.28, 0.97419, 17, -52.11, -72.61, 0.00117, 2, 13, 161.28, -38, 0.22546, 14, -19.65, -52.51, 0.77454, 2, 13, 136.08, -37.75, 0.53765, 14, -37.68, -34.9, 0.46235, 2, 13, 105.21, -32.92, 0.96133, 14, -56.64, -10.07, 0.03867, 1, 13, 89.5, -31.41, 1, 1, 13, 61.41, -36.4, 1, 1, 13, 37.14, -33.34, 1, 1, 13, 7.51, -30.85, 1, 1, 13, -9.76, -24.07, 1, 1, 13, -23.78, -15.48, 1, 1, 13, -31.96, -5.73, 1, 1, 13, -22.09, -3.43, 1, 1, 13, -5.57, -5.6, 1, 1, 13, 13.12, -7.29, 1, 1, 13, 34.52, -8.94, 1, 1, 13, 55.87, -7.88, 1, 1, 13, 76.81, -2.91, 1, 2, 13, 96.36, 8.08, 0.96425, 14, -34.68, 25.67, 0.03575, 3, 13, 115.54, 22.48, 0.21081, 14, -10.87, 22.81, 0.17318, 15, 14.58, 11.53, 0.616, 3, 13, 131.78, 34.34, 0.06228, 14, 9.07, 20.15, 0.32972, 15, 6.43, -6.86, 0.608, 5, 13, 150.42, 49.18, 0.01542, 14, 32.8, 17.97, 0.44049, 17, -51.33, 6.78, 6e-05, 18, -101.59, -81.41, 3e-05, 15, -2.32, -29.02, 0.544, 4, 13, 169.42, 58.38, 0.00507, 14, 52.88, 11.48, 0.99265, 17, -30.3, 4.9, 0.00155, 18, -84.61, -68.87, 0.00073, 4, 13, 191.21, 73.8, 0.00025, 14, 79.28, 7.55, 0.7379, 17, -3.69, 6.91, 0.25751, 18, -66.04, -49.69, 0.00434, 3, 14, 102.54, 3.57, 0.0076, 17, 19.88, 8.17, 0.97632, 18, -49.25, -33.11, 0.01608, 2, 17, 50.55, 3.17, 0.97563, 18, -22.99, -16.5, 0.02437, 1, 18, 2.8, -0.02, 1, 1, 18, 25.93, 4.89, 1, 2, 17, 113.73, -34.98, 0.00146, 18, 49.6, -3.11, 0.99854, 2, 17, 116.01, -52.26, 0.00068, 18, 62.77, -14.52, 0.99932, 3, 13, 92.16, 32.33, 0.07189, 14, -20.94, 46.09, 0.03348, 15, 39.75, 14.67, 0.89463, 5, 13, 117.81, 67.71, 0.04667, 14, 22.05, 53.91, 0.17842, 17, -69.76, 39.45, 0.00362, 18, -137.05, -69.2, 0.00048, 15, 35.19, -28.79, 0.77081], "hull": 41, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 0, 80, 0, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116], "width": 318, "height": 240}}, "danjifuhuo014": {"danjifuhuo014": {"type": "mesh", "uvs": [0.6786, 0.88025, 0.6523, 0.77189, 0.62337, 0.62147, 0.60497, 0.45829, 0.5813, 0.35631, 0.53133, 0.32826, 0.50503, 0.31041, 0.46032, 0.36268, 0.40509, 0.39073, 0.292, 0.39455, 0.16182, 0.35758, 0.01718, 0.19823, 0.08687, 0.13704, 0.12895, 0.06438, 0.1776, 0.00701, 0.292, 0.00446, 0.44191, 0.01466, 0.54579, 0.0427, 0.63784, 0.1039, 0.74567, 0.13959, 0.82456, 0.20843, 0.88505, 0.32061, 0.93896, 0.45574, 0.98762, 0.65716, 1, 0.83436, 1, 0.96439, 0.94422, 1, 0.84429, 0.97459, 0.74172, 0.93252, 0.81536, 0.87133, 0.80352, 0.72345, 0.76933, 0.5437, 0.73515, 0.39455, 0.70096, 0.29002, 0.60365, 0.20461, 0.44322, 0.15999, 0.30384, 0.12939, 0.17366, 0.12429, 0.10923, 0.22118, 0.25913, 0.23648, 0.41167, 0.26962], "triangles": [37, 13, 14, 36, 15, 16, 37, 14, 15, 36, 37, 15, 12, 13, 37, 35, 16, 17, 36, 16, 35, 34, 17, 18, 34, 18, 19, 35, 17, 34, 38, 12, 37, 11, 12, 38, 39, 37, 36, 38, 37, 39, 40, 36, 35, 6, 40, 35, 39, 36, 40, 33, 34, 19, 34, 6, 35, 5, 6, 34, 34, 4, 5, 10, 38, 39, 11, 38, 10, 7, 40, 6, 40, 9, 39, 8, 40, 7, 9, 10, 39, 40, 8, 9, 33, 19, 20, 33, 20, 21, 33, 4, 34, 32, 33, 21, 4, 33, 32, 32, 21, 22, 3, 4, 32, 31, 32, 22, 3, 32, 31, 2, 3, 31, 31, 22, 23, 30, 31, 23, 2, 31, 30, 1, 2, 30, 30, 23, 24, 29, 30, 24, 1, 30, 29, 0, 1, 29, 28, 0, 29, 29, 24, 25, 27, 29, 25, 28, 29, 27, 26, 27, 25], "vertices": [1, 15, 19.05, 12.64, 1, 1, 15, 33.59, 13.74, 1, 1, 15, 53.63, 14.32, 1, 2, 15, 75.11, 13.31, 0.97988, 16, 0.76, 26.37, 0.02012, 2, 15, 88.77, 14.21, 0.55719, 16, 8.1, 14.81, 0.44281, 2, 15, 93.39, 19.91, 0.16547, 16, 15.32, 13.49, 0.83453, 2, 15, 96.21, 22.85, 0.04861, 16, 19.25, 12.42, 0.95139, 2, 15, 90.33, 29.52, 0.00161, 16, 22.28, 20.78, 0.99839, 1, 16, 27.64, 26.61, 1, 1, 16, 40.99, 31.94, 1, 1, 16, 58.19, 32.96, 1, 1, 16, 82.53, 19.52, 1, 1, 16, 76.91, 8.99, 1, 1, 16, 75.09, -1.78, 1, 1, 16, 71.82, -10.94, 1, 1, 16, 58.26, -16.16, 1, 1, 16, 39.89, -21.34, 1, 1, 16, 26.23, -22.34, 1, 1, 16, 12.52, -18.74, 1, 2, 15, 113.58, -10.81, 0.16324, 16, -1.95, -18.97, 0.83676, 2, 15, 103.12, -19.31, 0.64824, 16, -14.43, -13.86, 0.35176, 2, 15, 87.41, -24.62, 0.98274, 16, -26.63, -2.63, 0.01726, 1, 15, 68.86, -28.64, 1, 1, 15, 41.84, -30.66, 1, 1, 15, 18.66, -28.62, 1, 1, 15, 1.83, -25.98, 1, 1, 15, -1.68, -18.26, 1, 1, 15, 3.58, -6.23, 1, 1, 15, 11.04, 5.78, 1, 1, 15, 17.51, -4.7, 1, 1, 15, 36.88, -6.22, 1, 1, 15, 60.82, -5.58, 1, 1, 15, 80.79, -4.31, 1, 1, 15, 95, -2.15, 1, 1, 16, 12.15, -4.86, 1, 1, 16, 33.3, -3.47, 1, 1, 16, 51.31, -1.27, 1, 1, 16, 67.1, 3.69, 1, 1, 16, 70.51, 18.4, 1, 1, 16, 51.92, 13.86, 1, 1, 16, 32.22, 11.4, 1], "hull": 29, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 0, 56, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 78, 80], "width": 127, "height": 131}}, "danjifuhuo015": {"danjifuhuo015": {"type": "mesh", "uvs": [1, 1e-05, 0.98932, 0.1108, 0.97674, 0.2414, 0.9021, 0.44899, 0.80823, 0.71007, 0.6813, 0.8499, 0.54505, 1, 0.37076, 1, 0.09313, 0.93063, 0, 0.7087, 0, 0.56153, 0.22033, 0.40932, 0.49142, 0.30476, 0.73324, 0.20135, 0.90379, 1e-05, 0.3059, 0.78909, 0.50069, 0.60919, 0.68132, 0.42667, 0.83007, 0.27284, 0.92215, 0.12943], "triangles": [17, 18, 3, 3, 18, 2, 17, 13, 18, 2, 18, 19, 18, 13, 19, 2, 19, 1, 13, 14, 19, 1, 19, 0, 0, 19, 14, 5, 16, 4, 16, 17, 4, 4, 17, 3, 16, 10, 11, 11, 12, 16, 16, 12, 17, 17, 12, 13, 5, 6, 15, 8, 15, 7, 6, 7, 15, 8, 9, 15, 15, 16, 5, 16, 15, 10, 10, 15, 9], "vertices": [1, 60, 72.8, -1.29, 1, 1, 60, 55.97, -7.96, 1, 1, 60, 36.13, -15.82, 1, 2, 59, 59.56, -21.12, 0.4606, 60, 1.75, -22.4, 0.5394, 2, 58, 72.04, -39.43, 0.15695, 59, 20.11, -40.66, 0.84305, 2, 58, 45.1, -44.54, 0.64159, 59, -7.07, -44.21, 0.35841, 2, 58, 16.19, -50.03, 0.94711, 59, -36.25, -48.01, 0.05289, 2, 58, 1.53, -35.11, 0.99334, 59, -50.02, -32.27, 0.00666, 1, 58, -13.75, -3.43, 1, 2, 58, 4.21, 29.9, 0.94992, 59, -43.59, 32.48, 0.05008, 2, 58, 21.32, 46.71, 0.79883, 59, -25.53, 48.27, 0.20117, 3, 58, 57.55, 45.25, 0.31205, 59, 10.55, 44.71, 0.6879, 60, -27.85, 54.14, 5e-05, 3, 58, 92.51, 33.99, 0.00713, 59, 44.8, 31.45, 0.78359, 60, 1.6, 32.2, 0.20928, 2, 59, 76.59, 20.72, 0.02833, 60, 29.36, 13.35, 0.97167, 1, 60, 67.8, 9.11, 1, 1, 58, 20.6, -5.47, 1, 1, 59, 8.19, -2.06, 1, 2, 59, 44.85, 1.22, 0.98946, 60, -6.44, 3.05, 0.01054, 1, 60, 23.89, -2.17, 1, 1, 60, 49.74, -2.01, 1], "hull": 15, "edges": [0, 28, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 8, 10, 10, 12, 4, 6, 6, 8, 0, 2, 2, 4, 16, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 0], "width": 120, "height": 163}}, "danjifuhuo016": {"danjifuhuo016": {"x": 93.55, "y": -17.8, "rotation": -72.9, "width": 171, "height": 180}}, "danjifuhuo017": {"danjifuhuo017": {"x": 69.03, "y": 83.99, "rotation": -72.9, "width": 48, "height": 69}}, "danjifuhuo018": {"danjifuhuo018": {"type": "mesh", "uvs": [0.64822, 0.20923, 0.87928, 0.336, 1, 0.55296, 1, 0.72583, 0.85301, 1, 0.63987, 1, 0.28292, 0.96286, 0.1126, 0.77947, 0, 0.65823, 0.03277, 0.38014, 0.05984, 0.17493, 0.08291, 0, 0.0962, 0, 0.36951, 0.05632, 0.65091, 0.75307, 0.57469, 0.49155, 0.44765, 0.28289, 0.20881, 0.10483], "triangles": [9, 16, 15, 1, 16, 0, 9, 10, 16, 10, 17, 16, 17, 13, 16, 16, 13, 0, 10, 11, 17, 11, 12, 17, 17, 12, 13, 14, 7, 15, 7, 8, 15, 14, 15, 2, 8, 9, 15, 15, 1, 2, 1, 15, 16, 5, 14, 4, 4, 14, 3, 5, 6, 14, 6, 7, 14, 3, 14, 2], "vertices": [2, 56, 77.08, -22.59, 0.23126, 57, 11.19, -25.85, 0.76874, 2, 56, 49.06, -38.16, 0.95785, 57, -19.66, -34.57, 0.04215, 2, 55, 70.44, -36.49, 0.17646, 56, 8.47, -38.93, 0.82354, 2, 55, 39.59, -34.07, 0.79266, 56, -21.29, -30.47, 0.20734, 1, 55, -8.21, -15.86, 1, 2, 55, -6.57, 4.97, 0.99854, 56, -58.85, 16.9, 0.00146, 2, 55, 2.8, 39.32, 0.89757, 56, -42.89, 48.73, 0.10243, 3, 55, 36.84, 53.38, 0.49421, 56, -6.75, 55.81, 0.50454, 57, -52.37, 69.72, 0.00125, 3, 55, 59.34, 62.68, 0.2041, 56, 17.14, 60.49, 0.76861, 57, -28.04, 68.78, 0.02729, 3, 55, 108.71, 55.58, 0.00099, 56, 64.14, 43.79, 0.49197, 57, 13.86, 41.72, 0.50704, 2, 56, 98.75, 31.2, 0.01984, 57, 44.65, 21.51, 0.98016, 1, 57, 70.89, 4.28, 1, 1, 57, 70.26, 3.14, 1, 1, 57, 48.4, -15.33, 1, 2, 55, 37.41, 0.42, 0.99914, 56, -16.63, 3.77, 0.00086, 1, 56, 30.44, -1.84, 1, 1, 57, 9.26, -2.26, 1, 2, 56, 106.83, 13.73, 7e-05, 57, 48.49, 2.65, 0.99993], "hull": 14, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 22, 24, 24, 26, 20, 22, 18, 20, 12, 14, 14, 16, 16, 18, 10, 28, 28, 30, 30, 32, 32, 34, 2, 0, 0, 26], "width": 98, "height": 179}}, "danjifuhuo019": {"danjifuhuo019": {"type": "mesh", "uvs": [0.14276, 0.45982, 0.21941, 0.48571, 0.31192, 0.52311, 0.42954, 0.56195, 0.53791, 0.57202, 0.6357, 0.57633, 0.68724, 0.67559, 0.76125, 0.74895, 0.84054, 0.8108, 0.89076, 0.90142, 0.91058, 1, 0.9423, 1, 1, 0.96472, 1, 0.85539, 0.98724, 0.74463, 0.91984, 0.67271, 0.83393, 0.62812, 0.7705, 0.58352, 0.73218, 0.53174, 0.76125, 0.4555, 0.76918, 0.34905, 0.73746, 0.24261, 0.69253, 0.1376, 0.61059, 0.04554, 0.49165, 0.0067, 0.38329, 0, 0.25113, 0.00095, 0.1573, 0.04986, 0.08594, 0.13904, 0.03307, 0.23685, 0.01722, 0.34042, 0.02647, 0.44399, 0.05554, 0.54469, 0.06479, 0.63962, 0.041, 0.75326, 0.00136, 0.83094, 0.05422, 0.82519, 0.11369, 0.76621, 0.13483, 0.67702, 0.13351, 0.57202, 0.12955, 0.49002, 0.5078, 0.14298, 0.50516, 0.29258, 0.47476, 0.44793, 0.63731, 0.18469, 0.65185, 0.33573, 0.61749, 0.48389, 0.68885, 0.56589, 0.73114, 0.63637, 0.6981, 0.46088, 0.71132, 0.33717, 0.70075, 0.21922, 0.81969, 0.71261, 0.90294, 0.78165, 0.94523, 0.87515, 0.40075, 0.13291, 0.38489, 0.271, 0.35582, 0.40909, 0.27521, 0.11709, 0.26596, 0.24511, 0.24878, 0.37026, 0.13512, 0.16312, 0.09944, 0.27676, 0.09151, 0.34724, 0.15362, 0.36162, 0.16155, 0.25374, 0.0849, 0.44793, 0.09151, 0.55438, 0.09812, 0.65507, 0.07301, 0.76008], "triangles": [54, 53, 13, 9, 53, 54, 54, 13, 12, 11, 10, 9, 54, 11, 9, 12, 11, 54, 52, 16, 15, 53, 52, 15, 53, 15, 14, 8, 52, 53, 7, 52, 8, 53, 14, 13, 9, 8, 53, 47, 5, 46, 17, 48, 47, 17, 47, 18, 6, 5, 47, 6, 47, 48, 16, 48, 17, 52, 48, 16, 7, 48, 52, 6, 48, 7, 49, 50, 19, 49, 46, 45, 18, 49, 19, 47, 46, 49, 47, 49, 18, 5, 4, 46, 21, 51, 22, 45, 44, 51, 50, 45, 51, 21, 50, 51, 50, 21, 20, 19, 50, 20, 49, 45, 50, 44, 23, 22, 41, 23, 44, 44, 22, 51, 42, 41, 44, 46, 43, 45, 3, 57, 43, 2, 57, 3, 4, 43, 46, 3, 43, 4, 42, 56, 41, 42, 44, 45, 42, 57, 56, 43, 42, 45, 43, 57, 42, 41, 24, 23, 55, 24, 41, 41, 56, 55, 58, 26, 25, 27, 26, 58, 55, 25, 24, 58, 25, 55, 59, 58, 55, 56, 59, 55, 60, 65, 59, 60, 59, 56, 64, 65, 60, 57, 60, 56, 1, 64, 60, 1, 60, 57, 0, 64, 1, 2, 1, 57, 69, 34, 68, 37, 69, 38, 69, 35, 34, 36, 69, 37, 36, 35, 69, 67, 32, 66, 67, 40, 39, 33, 32, 67, 68, 33, 67, 39, 68, 67, 68, 39, 38, 34, 33, 68, 69, 68, 38, 63, 30, 62, 63, 62, 64, 31, 30, 63, 66, 31, 63, 66, 63, 64, 0, 66, 64, 40, 66, 0, 32, 31, 66, 40, 67, 66, 61, 28, 27, 61, 27, 58, 59, 61, 58, 65, 61, 59, 61, 29, 28, 62, 61, 65, 62, 29, 61, 30, 29, 62, 64, 62, 65], "vertices": [3, 74, 41.9, 18.45, 0.47658, 75, -10.82, 18.81, 0.02118, 72, 36.58, -11.99, 0.50224, 4, 72, 39.42, 10.54, 0.90504, 71, 74.71, 18.15, 0.00261, 62, 63.13, -73.46, 0.01674, 63, 28.99, -68.86, 0.0756, 4, 72, 44.42, 38.02, 0.47333, 71, 74.16, 46.07, 0.01922, 62, 71.94, -46.96, 0.0598, 63, 32.95, -41.22, 0.44766, 3, 72, 48.55, 72.55, 0.03145, 71, 71.35, 80.74, 0.00071, 63, 35.77, -6.55, 0.96784, 5, 72, 45.77, 103.21, 0, 71, 62.55, 110.24, 0, 63, 31.84, 23.98, 0.64664, 66, 47.1, -35.03, 0.35091, 67, -30.86, -26.82, 0.00245, 5, 72, 42.04, 130.65, 0, 71, 53.45, 136.4, 0, 63, 27.07, 51.26, 0.08189, 66, 44.96, -7.42, 0.44689, 67, -8.94, -9.87, 0.47122, 1, 67, 18.82, -20.25, 1, 3, 71, 82.18, 185.9, 0, 67, 47.13, -21.39, 0.65914, 68, -1.37, -22.06, 0.34086, 4, 71, 88.92, 212.68, 0, 67, 74.65, -19.27, 0.00293, 68, 25.24, -14.7, 0.98294, 69, -13.98, -29.06, 0.01413, 3, 71, 105.64, 234.54, 0, 68, 52.5, -18.45, 0.23294, 69, 10.01, -15.57, 0.76706, 2, 71, 127.43, 249.15, 0, 69, 35.79, -10.74, 1, 2, 71, 124.14, 257.5, 0, 69, 36.07, -1.77, 1, 2, 72, 123.45, 249.81, 0, 69, 27.39, 14.83, 1, 2, 68, 62.62, 13.12, 0.45805, 69, -1.02, 15.69, 0.54195, 1, 68, 37.94, 28.4, 1, 2, 67, 68.74, 22.66, 0.03997, 68, 11.41, 25.31, 0.96003, 2, 67, 42.67, 15.9, 0.89123, 68, -12.88, 13.69, 0.10877, 3, 71, 41.21, 172.58, 0, 66, 42.32, 30.69, 0.0173, 67, 21.47, 13.23, 0.9827, 3, 71, 32.66, 157.55, 0, 66, 30.23, 18.33, 0.49454, 67, 4.5, 16.56, 0.50546, 4, 72, 4.9, 160.15, 0, 65, 64.2, 22.18, 0.15668, 66, 9.57, 24.17, 0.82388, 67, -1.94, 37.03, 0.01944, 3, 72, -22.74, 157.52, 0, 65, 36.68, 25.89, 0.90727, 66, -18.17, 23.13, 0.09273, 2, 64, 60.91, 10.04, 0.02685, 65, 8.56, 18.4, 0.97315, 3, 71, -58.58, 109.55, 0, 64, 31.62, 17.05, 0.99177, 65, -19.37, 7.15, 0.00823, 2, 71, -72.35, 79.2, 0, 64, -1.54, 13.77, 1, 3, 70, -31.9, 11.84, 0.08839, 61, -19.59, 8.51, 0.7362, 64, -30.38, -6.32, 0.17541, 3, 73, -61.4, 46.29, 0.00396, 70, -11.27, -10.92, 0.91337, 61, -24.7, -21.78, 0.08267, 3, 73, -45.13, 12.62, 0.22806, 72, -86.25, -2.64, 0, 70, 15.55, -36.98, 0.77194, 3, 73, -22.25, -5.91, 0.69362, 72, -69.09, -26.56, 0, 70, 43.39, -46.55, 0.30638, 1, 73, 7.36, -14.21, 1, 3, 73, 36.75, -16.81, 0.977, 74, -17.56, -9.63, 0.023, 72, -15.08, -52.69, 0, 2, 73, 63, -9.31, 0.03377, 74, 9.1, -15.47, 0.96623, 1, 74, 36.13, -14.22, 1, 2, 74, 62.69, -7.32, 0.00341, 75, 9.12, -7.62, 0.99659, 3, 75, 33.93, -7.06, 0.86777, 76, -7.28, -11.09, 0.13223, 72, 86.46, -25.55, 0, 2, 76, 22.3, -4.54, 1, 72, 116.73, -27.02, 0, 2, 76, 45.36, -6.05, 1, 72, 138.58, -34.54, 0, 1, 76, 37.61, 6.83, 1, 2, 75, 67.88, 3.99, 0.16922, 76, 16.55, 15.49, 0.83078, 1, 75, 45.27, 11.88, 1, 2, 75, 18.03, 13.78, 0.99996, 72, 65.76, -9.48, 4e-05, 3, 74, 49.55, 14.31, 0.58298, 75, -3.31, 14.43, 0.28857, 72, 44.97, -14.31, 0.12845, 4, 72, -62.58, 75.33, 0, 61, 16.13, 9.14, 0.81045, 64, 0.21, -24.77, 0.18824, 65, -20.75, -45.13, 0.00131, 5, 61, 54.71, 4.1, 0.01179, 62, 10.16, 5.62, 0.93085, 64, 30.22, -49.53, 0.01217, 65, 18.05, -47.94, 0.04405, 66, -23.95, -52.79, 0.00114, 5, 72, 17.12, 79.98, 0.00029, 71, 39.08, 81.78, 0.00012, 70, 51.99, 90.23, 0, 62, 50.82, -1.58, 0.04093, 63, 4.09, -0.32, 0.95866, 5, 72, -58.3, 113.31, 0, 61, 30.96, 44.37, 0.01345, 62, -19.16, 42.04, 0.00595, 64, 31.5, -2.8, 0.94596, 65, -7.98, -9.1, 0.03463, 6, 61, 70.45, 44.12, 0.00179, 62, 19.94, 47.5, 0.06901, 63, -35.03, 42.48, 0.00078, 64, 64.81, -23.99, 0.00086, 65, 31.46, -7.08, 0.9137, 66, -17.7, -10.25, 0.01385, 6, 72, 19.27, 121.38, 0, 71, 32.98, 122.79, 0, 62, 58.77, 39.11, 0.05638, 63, 4.68, 41.14, 0.14952, 65, 69.41, -18.84, 0.01971, 66, 21.7, -15.37, 0.77439, 2, 66, 40.49, 7.2, 0.05672, 67, 0.83, 1.88, 0.94328, 1, 67, 21.78, -4.45, 1, 2, 66, 13.07, 6.58, 0.98658, 67, -14.73, 24.47, 0.01342, 3, 72, -22.93, 140.86, 0, 65, 32.72, 9.7, 0.99371, 66, -19.31, 6.51, 0.00629, 2, 64, 49.69, 5.68, 0.2122, 65, 1.94, 8.35, 0.7878, 3, 71, 67.33, 197.83, 0, 67, 53.71, -3.51, 0.35636, 68, 1.67, -3.25, 0.64364, 1, 68, 30.44, 3.8, 1, 2, 68, 56.87, -2.17, 0.03229, 69, 3.65, 0.04, 0.96771, 4, 71, -29.45, 32.28, 0.0088, 70, 9.45, 17.17, 0.53311, 61, 10.19, -20.68, 0.45109, 62, -30.31, -25.33, 0.007, 6, 72, -23.73, 46.9, 0.04007, 71, 5.6, 41.26, 0.20239, 70, 37.84, 39.61, 0.15868, 61, 45.38, -29.11, 0.21033, 62, 5.72, -28.58, 0.37742, 63, -35.48, -34.92, 0.01112, 6, 72, 13.06, 45.07, 0.29412, 71, 42.03, 46.77, 0.1238, 70, 68.89, 59.42, 0.00518, 61, 80.15, -41.25, 0.00425, 62, 41.89, -35.57, 0.28674, 63, 1.35, -35.35, 0.2859, 3, 73, -20.78, 31.73, 0.0895, 72, -57.71, 9.34, 0, 70, 31.88, -10.69, 0.9105, 6, 72, -24.48, 12.58, 0.00719, 71, 11.68, 7.48, 0.90834, 70, 57.1, 11.19, 0.0429, 61, 34.97, -61.82, 0.01237, 62, 0.16, -62.45, 0.02737, 63, -34.93, -69.24, 0.00183, 6, 72, 8.41, 13.48, 0.70149, 71, 43.73, 14.88, 0.20289, 70, 83.39, 30.97, 0.00055, 61, 66.77, -70.25, 0.00167, 62, 32.84, -66.19, 0.05105, 63, -2.1, -67.1, 0.04235, 3, 73, 7.04, 1.05, 0.98688, 71, 5.41, -34.79, 0.01086, 70, 68.53, -29.97, 0.00226, 4, 73, 38.06, 4.61, 0.88983, 74, -6.25, 8.6, 0.0448, 72, -8.14, -32.38, 0.02227, 71, 36.61, -33.35, 0.04309, 4, 73, 55.58, 10.44, 0.05884, 74, 11.93, 5.44, 0.86042, 72, 10.29, -31.39, 0.06787, 71, 54.48, -28.72, 0.01286, 4, 73, 51.42, 27.93, 0.05782, 74, 16.56, 22.8, 0.28172, 72, 10.9, -13.43, 0.63259, 71, 51.52, -11, 0.02786, 4, 73, 25.12, 17.92, 0.48978, 74, -11.34, 26.46, 0.01656, 72, -17.11, -16.12, 0.02283, 71, 24.6, -19.19, 0.47083, 3, 74, 37.99, 2.25, 0.97399, 75, -15.26, 2.75, 0.00014, 72, 36.4, -28.66, 0.02587, 2, 75, 12.47, 2.31, 0.99989, 72, 63.32, -21.98, 0.00011, 1, 75, 38.72, 2, 1, 2, 75, 65.34, -7.35, 0.02924, 76, 20.03, 4.4, 0.97076], "hull": 41, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 0, 80, 48, 82, 82, 84, 84, 86, 46, 88, 88, 90, 90, 92, 94, 96, 94, 98, 98, 100, 100, 102, 96, 104, 104, 106, 106, 108, 50, 110, 110, 112, 112, 114, 52, 116, 116, 118, 118, 120, 54, 122, 122, 124, 124, 126, 0, 128, 128, 130, 130, 122, 126, 132, 132, 134, 134, 136, 132, 80, 136, 138, 138, 70], "width": 283, "height": 260}}, "zhiliao2/xfr_skill_xuli_td1_45": {"zhiliao2/xfr_skill_xuli_td1_45": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [2, 88, 16.34, -108.96, 0.00221, 89, 15.85, -4.31, 0.99779, 2, 88, -17.66, -108.96, 0.00305, 89, -18.15, -4.31, 0.99695, 2, 88, -17.66, 1.04, 0.99263, 89, -18.15, 105.69, 0.00737, 2, 88, 16.34, 1.04, 0.99459, 89, 15.85, 105.69, 0.00541], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 34, "height": 110}, "zhiliao2/xfr_skill_xuli_td1_46": {"type": "<PERSON><PERSON><PERSON>", "path": "zhiliao2/xfr_skill_xuli_td1_45", "parent": "zhiliao2/xfr_skill_xuli_td1_45", "width": 34, "height": 110}, "zhiliao2/xfr_skill_xuli_td1_47": {"type": "<PERSON><PERSON><PERSON>", "parent": "zhiliao2/xfr_skill_xuli_td1_45", "width": 34, "height": 110}, "zhiliao2/xfr_skill_xuli_td1_49": {"type": "<PERSON><PERSON><PERSON>", "parent": "zhiliao2/xfr_skill_xuli_td1_45", "width": 34, "height": 110}, "zhiliao2/xfr_skill_xuli_td1_51": {"type": "<PERSON><PERSON><PERSON>", "parent": "zhiliao2/xfr_skill_xuli_td1_45", "width": 34, "height": 110}, "zhiliao2/xfr_skill_xuli_td1_53": {"type": "<PERSON><PERSON><PERSON>", "parent": "zhiliao2/xfr_skill_xuli_td1_45", "width": 34, "height": 110}, "zhiliao2/xfr_skill_xuli_td1_55": {"type": "<PERSON><PERSON><PERSON>", "parent": "zhiliao2/xfr_skill_xuli_td1_45", "width": 34, "height": 110}, "zhiliao2/xfr_skill_xuli_td1_57": {"type": "<PERSON><PERSON><PERSON>", "parent": "zhiliao2/xfr_skill_xuli_td1_45", "width": 34, "height": 110}, "zhiliao2/xfr_skill_xuli_td1_59": {"type": "<PERSON><PERSON><PERSON>", "parent": "zhiliao2/xfr_skill_xuli_td1_45", "width": 34, "height": 110}, "zhiliao2/xfr_skill_xuli_td1_61": {"type": "<PERSON><PERSON><PERSON>", "parent": "zhiliao2/xfr_skill_xuli_td1_45", "width": 34, "height": 110}}, "zhiliao2/xfr_skill_xuli_td1_46": {"zhiliao2/xfr_skill_xuli_td1_45": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [2, 90, 16.34, -108.96, 0.00221, 89, 15.85, -4.31, 0.99779, 2, 90, -17.66, -108.96, 0.00305, 89, -18.15, -4.31, 0.99695, 2, 90, -17.66, 1.04, 0.99263, 89, -18.15, 105.69, 0.00737, 2, 90, 16.34, 1.04, 0.99459, 89, 15.85, 105.69, 0.00541], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 34, "height": 110}, "zhiliao2/xfr_skill_xuli_td1_46": {"type": "<PERSON><PERSON><PERSON>", "path": "zhiliao2/xfr_skill_xuli_td1_45", "parent": "zhiliao2/xfr_skill_xuli_td1_45", "width": 34, "height": 110}, "zhiliao2/xfr_skill_xuli_td1_47": {"type": "<PERSON><PERSON><PERSON>", "parent": "zhiliao2/xfr_skill_xuli_td1_45", "width": 34, "height": 110}, "zhiliao2/xfr_skill_xuli_td1_49": {"type": "<PERSON><PERSON><PERSON>", "parent": "zhiliao2/xfr_skill_xuli_td1_45", "width": 34, "height": 110}, "zhiliao2/xfr_skill_xuli_td1_51": {"type": "<PERSON><PERSON><PERSON>", "parent": "zhiliao2/xfr_skill_xuli_td1_45", "width": 34, "height": 110}, "zhiliao2/xfr_skill_xuli_td1_53": {"type": "<PERSON><PERSON><PERSON>", "parent": "zhiliao2/xfr_skill_xuli_td1_45", "width": 34, "height": 110}, "zhiliao2/xfr_skill_xuli_td1_55": {"type": "<PERSON><PERSON><PERSON>", "parent": "zhiliao2/xfr_skill_xuli_td1_45", "width": 34, "height": 110}, "zhiliao2/xfr_skill_xuli_td1_57": {"type": "<PERSON><PERSON><PERSON>", "parent": "zhiliao2/xfr_skill_xuli_td1_45", "width": 34, "height": 110}, "zhiliao2/xfr_skill_xuli_td1_59": {"type": "<PERSON><PERSON><PERSON>", "parent": "zhiliao2/xfr_skill_xuli_td1_45", "width": 34, "height": 110}, "zhiliao2/xfr_skill_xuli_td1_61": {"type": "<PERSON><PERSON><PERSON>", "parent": "zhiliao2/xfr_skill_xuli_td1_45", "width": 34, "height": 110}}, "zhiliao2/xfr_skill_xuli_td1_47": {"zhiliao2/xfr_skill_xuli_td1_45": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [2, 92, 16.34, -108.96, 0.00221, 89, 15.85, -4.31, 0.99779, 2, 92, -17.66, -108.96, 0.00305, 89, -18.15, -4.31, 0.99695, 2, 92, -17.66, 1.04, 0.99263, 89, -18.15, 105.69, 0.00737, 2, 92, 16.34, 1.04, 0.99459, 89, 15.85, 105.69, 0.00541], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 34, "height": 110}, "zhiliao2/xfr_skill_xuli_td1_46": {"type": "<PERSON><PERSON><PERSON>", "path": "zhiliao2/xfr_skill_xuli_td1_45", "parent": "zhiliao2/xfr_skill_xuli_td1_45", "width": 34, "height": 110}, "zhiliao2/xfr_skill_xuli_td1_47": {"type": "<PERSON><PERSON><PERSON>", "parent": "zhiliao2/xfr_skill_xuli_td1_45", "width": 34, "height": 110}, "zhiliao2/xfr_skill_xuli_td1_49": {"type": "<PERSON><PERSON><PERSON>", "parent": "zhiliao2/xfr_skill_xuli_td1_45", "width": 34, "height": 110}, "zhiliao2/xfr_skill_xuli_td1_51": {"type": "<PERSON><PERSON><PERSON>", "parent": "zhiliao2/xfr_skill_xuli_td1_45", "width": 34, "height": 110}, "zhiliao2/xfr_skill_xuli_td1_53": {"type": "<PERSON><PERSON><PERSON>", "parent": "zhiliao2/xfr_skill_xuli_td1_45", "width": 34, "height": 110}, "zhiliao2/xfr_skill_xuli_td1_55": {"type": "<PERSON><PERSON><PERSON>", "parent": "zhiliao2/xfr_skill_xuli_td1_45", "width": 34, "height": 110}, "zhiliao2/xfr_skill_xuli_td1_57": {"type": "<PERSON><PERSON><PERSON>", "parent": "zhiliao2/xfr_skill_xuli_td1_45", "width": 34, "height": 110}, "zhiliao2/xfr_skill_xuli_td1_59": {"type": "<PERSON><PERSON><PERSON>", "parent": "zhiliao2/xfr_skill_xuli_td1_45", "width": 34, "height": 110}, "zhiliao2/xfr_skill_xuli_td1_61": {"type": "<PERSON><PERSON><PERSON>", "parent": "zhiliao2/xfr_skill_xuli_td1_45", "width": 34, "height": 110}}}}], "animations": {"daji_daiji": {"slots": {"tx_qua": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.3, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.7, "color": "ffffff00", "curve": "stepped"}, {"time": 1.7333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.6, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.4333, "color": "ffffff00", "curve": "stepped"}, {"time": 3.4667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 3.9, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4.3, "color": "ffffff00"}]}, "zhiliao1/yzzl_skill_zd_00000": {"attachment": [{"name": "zhiliao1/yzzl_skill_zd_00000"}, {"time": 0.0667, "name": "zhiliao1/yzzl_skill_zd_00001"}, {"time": 0.1333, "name": "zhiliao1/yzzl_skill_zd_00002"}, {"time": 0.2, "name": "zhiliao1/yzzl_skill_zd_00003"}, {"time": 0.2667, "name": "zhiliao1/yzzl_skill_zd_00004"}, {"time": 0.3333, "name": "zhiliao1/yzzl_skill_zd_00005"}, {"time": 0.4, "name": "zhiliao1/yzzl_skill_zd_00006"}, {"time": 0.4667, "name": "zhiliao1/yzzl_skill_zd_00007"}, {"time": 0.5333, "name": "zhiliao1/yzzl_skill_zd_00008"}, {"time": 0.6, "name": "zhiliao1/yzzl_skill_zd_00009"}, {"time": 0.6667, "name": "zhiliao1/yzzl_skill_zd_00000"}, {"time": 0.7333, "name": "zhiliao1/yzzl_skill_zd_00001"}, {"time": 0.8, "name": "zhiliao1/yzzl_skill_zd_00002"}, {"time": 0.8667, "name": "zhiliao1/yzzl_skill_zd_00003"}, {"time": 0.9333, "name": "zhiliao1/yzzl_skill_zd_00004"}, {"time": 1, "name": "zhiliao1/yzzl_skill_zd_00005"}, {"time": 1.0667, "name": "zhiliao1/yzzl_skill_zd_00006"}, {"time": 1.1333, "name": "zhiliao1/yzzl_skill_zd_00007"}, {"time": 1.2, "name": "zhiliao1/yzzl_skill_zd_00008"}, {"time": 1.2667, "name": "zhiliao1/yzzl_skill_zd_00009"}, {"time": 1.3333, "name": "zhiliao1/yzzl_skill_zd_00000"}, {"time": 1.4, "name": "zhiliao1/yzzl_skill_zd_00001"}, {"time": 1.4667, "name": "zhiliao1/yzzl_skill_zd_00002"}, {"time": 1.5333, "name": "zhiliao1/yzzl_skill_zd_00003"}, {"time": 1.6, "name": "zhiliao1/yzzl_skill_zd_00004"}, {"time": 1.6667, "name": "zhiliao1/yzzl_skill_zd_00005"}, {"time": 1.7333, "name": "zhiliao1/yzzl_skill_zd_00006"}, {"time": 1.8, "name": "zhiliao1/yzzl_skill_zd_00007"}, {"time": 1.8667, "name": "zhiliao1/yzzl_skill_zd_00008"}, {"time": 1.9333, "name": "zhiliao1/yzzl_skill_zd_00009"}, {"time": 1.9667, "name": "zhiliao1/yzzl_skill_zd_00000"}, {"time": 2.0333, "name": "zhiliao1/yzzl_skill_zd_00001"}, {"time": 2.1, "name": "zhiliao1/yzzl_skill_zd_00002"}, {"time": 2.1667, "name": "zhiliao1/yzzl_skill_zd_00003"}, {"time": 2.2333, "name": "zhiliao1/yzzl_skill_zd_00004"}, {"time": 2.3, "name": "zhiliao1/yzzl_skill_zd_00005"}, {"time": 2.3667, "name": "zhiliao1/yzzl_skill_zd_00006"}, {"time": 2.4333, "name": "zhiliao1/yzzl_skill_zd_00007"}, {"time": 2.5, "name": "zhiliao1/yzzl_skill_zd_00008"}, {"time": 2.5667, "name": "zhiliao1/yzzl_skill_zd_00009"}, {"time": 2.6333, "name": "zhiliao1/yzzl_skill_zd_00000"}, {"time": 2.7, "name": "zhiliao1/yzzl_skill_zd_00001"}, {"time": 2.7667, "name": "zhiliao1/yzzl_skill_zd_00002"}, {"time": 2.8333, "name": "zhiliao1/yzzl_skill_zd_00003"}, {"time": 2.9, "name": "zhiliao1/yzzl_skill_zd_00004"}, {"time": 2.9667, "name": "zhiliao1/yzzl_skill_zd_00005"}, {"time": 3.0333, "name": "zhiliao1/yzzl_skill_zd_00006"}, {"time": 3.1, "name": "zhiliao1/yzzl_skill_zd_00007"}, {"time": 3.1667, "name": "zhiliao1/yzzl_skill_zd_00008"}, {"time": 3.2333, "name": "zhiliao1/yzzl_skill_zd_00009"}, {"time": 3.3, "name": "zhiliao1/yzzl_skill_zd_00000"}, {"time": 3.3667, "name": "zhiliao1/yzzl_skill_zd_00001"}, {"time": 3.4333, "name": "zhiliao1/yzzl_skill_zd_00002"}, {"time": 3.5, "name": "zhiliao1/yzzl_skill_zd_00003"}, {"time": 3.5667, "name": "zhiliao1/yzzl_skill_zd_00004"}, {"time": 3.6333, "name": "zhiliao1/yzzl_skill_zd_00005"}, {"time": 3.7, "name": "zhiliao1/yzzl_skill_zd_00006"}, {"time": 3.7667, "name": "zhiliao1/yzzl_skill_zd_00007"}, {"time": 3.8333, "name": "zhiliao1/yzzl_skill_zd_00008"}, {"time": 3.9, "name": "zhiliao1/yzzl_skill_zd_00009"}, {"time": 3.9333, "name": "zhiliao1/yzzl_skill_zd_00000"}, {"time": 4, "name": "zhiliao1/yzzl_skill_zd_00001"}, {"time": 4.0667, "name": "zhiliao1/yzzl_skill_zd_00002"}, {"time": 4.1333, "name": "zhiliao1/yzzl_skill_zd_00003"}, {"time": 4.2, "name": "zhiliao1/yzzl_skill_zd_00004"}, {"time": 4.2667, "name": "zhiliao1/yzzl_skill_zd_00005"}, {"time": 4.3333, "name": "zhiliao1/yzzl_skill_zd_00006"}, {"time": 4.4, "name": "zhiliao1/yzzl_skill_zd_00007"}, {"time": 4.4667, "name": "zhiliao1/yzzl_skill_zd_00008"}]}, "zhiliao2/xfr_skill_xuli_td1_46": {"attachment": [{"time": 0.0333, "name": "zhiliao2/xfr_skill_xuli_td1_61"}, {"time": 0.0667, "name": "zhiliao2/xfr_skill_xuli_td1_59"}, {"time": 0.1, "name": "zhiliao2/xfr_skill_xuli_td1_57"}, {"time": 0.1333, "name": "zhiliao2/xfr_skill_xuli_td1_55"}, {"time": 0.1667, "name": "zhiliao2/xfr_skill_xuli_td1_53"}, {"time": 0.2, "name": "zhiliao2/xfr_skill_xuli_td1_51"}, {"time": 0.2333, "name": "zhiliao2/xfr_skill_xuli_td1_49"}, {"time": 0.2667, "name": "zhiliao2/xfr_skill_xuli_td1_47"}, {"time": 0.3, "name": "zhiliao2/xfr_skill_xuli_td1_46"}, {"time": 0.3333, "name": "zhiliao2/xfr_skill_xuli_td1_45"}, {"time": 0.3667, "name": null}, {"time": 0.8667, "name": "zhiliao2/xfr_skill_xuli_td1_61"}, {"time": 0.9, "name": "zhiliao2/xfr_skill_xuli_td1_59"}, {"time": 0.9333, "name": "zhiliao2/xfr_skill_xuli_td1_57"}, {"time": 0.9667, "name": "zhiliao2/xfr_skill_xuli_td1_55"}, {"time": 1, "name": "zhiliao2/xfr_skill_xuli_td1_53"}, {"time": 1.0333, "name": "zhiliao2/xfr_skill_xuli_td1_51"}, {"time": 1.0667, "name": "zhiliao2/xfr_skill_xuli_td1_49"}, {"time": 1.1, "name": "zhiliao2/xfr_skill_xuli_td1_47"}, {"time": 1.1333, "name": "zhiliao2/xfr_skill_xuli_td1_46"}, {"time": 1.1667, "name": "zhiliao2/xfr_skill_xuli_td1_45"}, {"time": 1.2, "name": null}, {"time": 1.5, "name": "zhiliao2/xfr_skill_xuli_td1_61"}, {"time": 1.5333, "name": "zhiliao2/xfr_skill_xuli_td1_59"}, {"time": 1.5667, "name": "zhiliao2/xfr_skill_xuli_td1_57"}, {"time": 1.6, "name": "zhiliao2/xfr_skill_xuli_td1_55"}, {"time": 1.6333, "name": "zhiliao2/xfr_skill_xuli_td1_53"}, {"time": 1.6667, "name": "zhiliao2/xfr_skill_xuli_td1_51"}, {"time": 1.7, "name": "zhiliao2/xfr_skill_xuli_td1_49"}, {"time": 1.7333, "name": "zhiliao2/xfr_skill_xuli_td1_47"}, {"time": 1.7667, "name": "zhiliao2/xfr_skill_xuli_td1_46"}, {"time": 1.8, "name": "zhiliao2/xfr_skill_xuli_td1_45"}, {"time": 1.8333, "name": null}, {"time": 2, "name": "zhiliao2/xfr_skill_xuli_td1_61"}, {"time": 2.0333, "name": "zhiliao2/xfr_skill_xuli_td1_59"}, {"time": 2.0667, "name": "zhiliao2/xfr_skill_xuli_td1_57"}, {"time": 2.1, "name": "zhiliao2/xfr_skill_xuli_td1_55"}, {"time": 2.1333, "name": "zhiliao2/xfr_skill_xuli_td1_53"}, {"time": 2.1667, "name": "zhiliao2/xfr_skill_xuli_td1_51"}, {"time": 2.2, "name": "zhiliao2/xfr_skill_xuli_td1_49"}, {"time": 2.2333, "name": "zhiliao2/xfr_skill_xuli_td1_47"}, {"time": 2.2667, "name": "zhiliao2/xfr_skill_xuli_td1_46"}, {"time": 2.3, "name": "zhiliao2/xfr_skill_xuli_td1_45"}, {"time": 2.3333, "name": null}, {"time": 2.8333, "name": "zhiliao2/xfr_skill_xuli_td1_61"}, {"time": 2.8667, "name": "zhiliao2/xfr_skill_xuli_td1_59"}, {"time": 2.9, "name": "zhiliao2/xfr_skill_xuli_td1_57"}, {"time": 2.9333, "name": "zhiliao2/xfr_skill_xuli_td1_55"}, {"time": 2.9667, "name": "zhiliao2/xfr_skill_xuli_td1_53"}, {"time": 3, "name": "zhiliao2/xfr_skill_xuli_td1_51"}, {"time": 3.0333, "name": "zhiliao2/xfr_skill_xuli_td1_49"}, {"time": 3.0667, "name": "zhiliao2/xfr_skill_xuli_td1_47"}, {"time": 3.1, "name": "zhiliao2/xfr_skill_xuli_td1_46"}, {"time": 3.1333, "name": "zhiliao2/xfr_skill_xuli_td1_45"}, {"time": 3.1667, "name": null}, {"time": 3.4667, "name": "zhiliao2/xfr_skill_xuli_td1_61"}, {"time": 3.5, "name": "zhiliao2/xfr_skill_xuli_td1_59"}, {"time": 3.5333, "name": "zhiliao2/xfr_skill_xuli_td1_57"}, {"time": 3.5667, "name": "zhiliao2/xfr_skill_xuli_td1_55"}, {"time": 3.6, "name": "zhiliao2/xfr_skill_xuli_td1_53"}, {"time": 3.6333, "name": "zhiliao2/xfr_skill_xuli_td1_51"}, {"time": 3.6667, "name": "zhiliao2/xfr_skill_xuli_td1_49"}, {"time": 3.7, "name": "zhiliao2/xfr_skill_xuli_td1_47"}, {"time": 3.7333, "name": "zhiliao2/xfr_skill_xuli_td1_46"}, {"time": 3.7667, "name": "zhiliao2/xfr_skill_xuli_td1_45"}, {"time": 3.8, "name": null}, {"time": 3.9667, "name": "zhiliao2/xfr_skill_xuli_td1_61"}, {"time": 4, "name": "zhiliao2/xfr_skill_xuli_td1_59"}, {"time": 4.0333, "name": "zhiliao2/xfr_skill_xuli_td1_57"}, {"time": 4.0667, "name": "zhiliao2/xfr_skill_xuli_td1_55"}, {"time": 4.1, "name": "zhiliao2/xfr_skill_xuli_td1_53"}, {"time": 4.1333, "name": "zhiliao2/xfr_skill_xuli_td1_51"}, {"time": 4.1667, "name": "zhiliao2/xfr_skill_xuli_td1_49"}, {"time": 4.2, "name": "zhiliao2/xfr_skill_xuli_td1_47"}, {"time": 4.2333, "name": "zhiliao2/xfr_skill_xuli_td1_46"}, {"time": 4.2667, "name": "zhiliao2/xfr_skill_xuli_td1_45"}, {"time": 4.3, "name": null}]}, "zikzizi2": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "color": "ffffff8a", "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.4, "color": "ffffff8a", "curve": 0.25, "c3": 0.75}, {"time": 4.5, "color": "ffffffff"}]}, "zhiliao1/light": {"color": [{"color": "67ff0000", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.5667, "color": "67ff0074", "curve": "stepped"}, {"time": 3.6667, "color": "67ff0074", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4, "color": "67ff0000"}]}, "zhiliao2/xfr_skill_xuli_td1_45": {"attachment": [{"time": 0.0333, "name": "zhiliao2/xfr_skill_xuli_td1_61"}, {"time": 0.0667, "name": "zhiliao2/xfr_skill_xuli_td1_59"}, {"time": 0.1, "name": "zhiliao2/xfr_skill_xuli_td1_57"}, {"time": 0.1333, "name": "zhiliao2/xfr_skill_xuli_td1_55"}, {"time": 0.1667, "name": "zhiliao2/xfr_skill_xuli_td1_53"}, {"time": 0.2, "name": "zhiliao2/xfr_skill_xuli_td1_51"}, {"time": 0.2333, "name": "zhiliao2/xfr_skill_xuli_td1_49"}, {"time": 0.2667, "name": "zhiliao2/xfr_skill_xuli_td1_47"}, {"time": 0.3, "name": "zhiliao2/xfr_skill_xuli_td1_46"}, {"time": 0.3333, "name": "zhiliao2/xfr_skill_xuli_td1_45"}, {"time": 0.3667, "name": null}, {"time": 0.7333, "name": "zhiliao2/xfr_skill_xuli_td1_61"}, {"time": 0.7667, "name": "zhiliao2/xfr_skill_xuli_td1_59"}, {"time": 0.8, "name": "zhiliao2/xfr_skill_xuli_td1_57"}, {"time": 0.8333, "name": "zhiliao2/xfr_skill_xuli_td1_55"}, {"time": 0.8667, "name": "zhiliao2/xfr_skill_xuli_td1_53"}, {"time": 0.9, "name": "zhiliao2/xfr_skill_xuli_td1_51"}, {"time": 0.9333, "name": "zhiliao2/xfr_skill_xuli_td1_49"}, {"time": 0.9667, "name": "zhiliao2/xfr_skill_xuli_td1_47"}, {"time": 1, "name": "zhiliao2/xfr_skill_xuli_td1_46"}, {"time": 1.0333, "name": "zhiliao2/xfr_skill_xuli_td1_45"}, {"time": 1.0667, "name": null}, {"time": 1.4, "name": "zhiliao2/xfr_skill_xuli_td1_61"}, {"time": 1.4333, "name": "zhiliao2/xfr_skill_xuli_td1_59"}, {"time": 1.4667, "name": "zhiliao2/xfr_skill_xuli_td1_57"}, {"time": 1.5, "name": "zhiliao2/xfr_skill_xuli_td1_55"}, {"time": 1.5333, "name": "zhiliao2/xfr_skill_xuli_td1_53"}, {"time": 1.5667, "name": "zhiliao2/xfr_skill_xuli_td1_51"}, {"time": 1.6, "name": "zhiliao2/xfr_skill_xuli_td1_49"}, {"time": 1.6333, "name": "zhiliao2/xfr_skill_xuli_td1_47"}, {"time": 1.6667, "name": "zhiliao2/xfr_skill_xuli_td1_46"}, {"time": 1.7, "name": "zhiliao2/xfr_skill_xuli_td1_45"}, {"time": 1.7333, "name": null}, {"time": 2, "name": "zhiliao2/xfr_skill_xuli_td1_61"}, {"time": 2.0333, "name": "zhiliao2/xfr_skill_xuli_td1_59"}, {"time": 2.0667, "name": "zhiliao2/xfr_skill_xuli_td1_57"}, {"time": 2.1, "name": "zhiliao2/xfr_skill_xuli_td1_55"}, {"time": 2.1333, "name": "zhiliao2/xfr_skill_xuli_td1_53"}, {"time": 2.1667, "name": "zhiliao2/xfr_skill_xuli_td1_51"}, {"time": 2.2, "name": "zhiliao2/xfr_skill_xuli_td1_49"}, {"time": 2.2333, "name": "zhiliao2/xfr_skill_xuli_td1_47"}, {"time": 2.2667, "name": "zhiliao2/xfr_skill_xuli_td1_46"}, {"time": 2.3, "name": "zhiliao2/xfr_skill_xuli_td1_45"}, {"time": 2.3333, "name": null}, {"time": 2.7, "name": "zhiliao2/xfr_skill_xuli_td1_61"}, {"time": 2.7333, "name": "zhiliao2/xfr_skill_xuli_td1_59"}, {"time": 2.7667, "name": "zhiliao2/xfr_skill_xuli_td1_57"}, {"time": 2.8, "name": "zhiliao2/xfr_skill_xuli_td1_55"}, {"time": 2.8333, "name": "zhiliao2/xfr_skill_xuli_td1_53"}, {"time": 2.8667, "name": "zhiliao2/xfr_skill_xuli_td1_51"}, {"time": 2.9, "name": "zhiliao2/xfr_skill_xuli_td1_49"}, {"time": 2.9333, "name": "zhiliao2/xfr_skill_xuli_td1_47"}, {"time": 2.9667, "name": "zhiliao2/xfr_skill_xuli_td1_46"}, {"time": 3, "name": "zhiliao2/xfr_skill_xuli_td1_45"}, {"time": 3.0333, "name": null}, {"time": 3.3667, "name": "zhiliao2/xfr_skill_xuli_td1_61"}, {"time": 3.4, "name": "zhiliao2/xfr_skill_xuli_td1_59"}, {"time": 3.4333, "name": "zhiliao2/xfr_skill_xuli_td1_57"}, {"time": 3.4667, "name": "zhiliao2/xfr_skill_xuli_td1_55"}, {"time": 3.5, "name": "zhiliao2/xfr_skill_xuli_td1_53"}, {"time": 3.5333, "name": "zhiliao2/xfr_skill_xuli_td1_51"}, {"time": 3.5667, "name": "zhiliao2/xfr_skill_xuli_td1_49"}, {"time": 3.6, "name": "zhiliao2/xfr_skill_xuli_td1_47"}, {"time": 3.6333, "name": "zhiliao2/xfr_skill_xuli_td1_46"}, {"time": 3.6667, "name": "zhiliao2/xfr_skill_xuli_td1_45"}, {"time": 3.7, "name": null}, {"time": 3.9667, "name": "zhiliao2/xfr_skill_xuli_td1_61"}, {"time": 4, "name": "zhiliao2/xfr_skill_xuli_td1_59"}, {"time": 4.0333, "name": "zhiliao2/xfr_skill_xuli_td1_57"}, {"time": 4.0667, "name": "zhiliao2/xfr_skill_xuli_td1_55"}, {"time": 4.1, "name": "zhiliao2/xfr_skill_xuli_td1_53"}, {"time": 4.1333, "name": "zhiliao2/xfr_skill_xuli_td1_51"}, {"time": 4.1667, "name": "zhiliao2/xfr_skill_xuli_td1_49"}, {"time": 4.2, "name": "zhiliao2/xfr_skill_xuli_td1_47"}, {"time": 4.2333, "name": "zhiliao2/xfr_skill_xuli_td1_46"}, {"time": 4.2667, "name": "zhiliao2/xfr_skill_xuli_td1_45"}, {"time": 4.3, "name": null}]}, "zhiliao2/xfr_skill_xuli_td1_47": {"attachment": [{"time": 0.2333, "name": "zhiliao2/xfr_skill_xuli_td1_61"}, {"time": 0.2667, "name": "zhiliao2/xfr_skill_xuli_td1_59"}, {"time": 0.3, "name": "zhiliao2/xfr_skill_xuli_td1_57"}, {"time": 0.3333, "name": "zhiliao2/xfr_skill_xuli_td1_55"}, {"time": 0.3667, "name": "zhiliao2/xfr_skill_xuli_td1_53"}, {"time": 0.4, "name": "zhiliao2/xfr_skill_xuli_td1_51"}, {"time": 0.4333, "name": "zhiliao2/xfr_skill_xuli_td1_49"}, {"time": 0.4667, "name": "zhiliao2/xfr_skill_xuli_td1_47"}, {"time": 0.5, "name": "zhiliao2/xfr_skill_xuli_td1_46"}, {"time": 0.5333, "name": "zhiliao2/xfr_skill_xuli_td1_45"}, {"time": 0.5667, "name": null}, {"time": 0.9, "name": "zhiliao2/xfr_skill_xuli_td1_61"}, {"time": 0.9333, "name": "zhiliao2/xfr_skill_xuli_td1_59"}, {"time": 0.9667, "name": "zhiliao2/xfr_skill_xuli_td1_57"}, {"time": 1, "name": "zhiliao2/xfr_skill_xuli_td1_55"}, {"time": 1.0333, "name": "zhiliao2/xfr_skill_xuli_td1_53"}, {"time": 1.0667, "name": "zhiliao2/xfr_skill_xuli_td1_51"}, {"time": 1.1, "name": "zhiliao2/xfr_skill_xuli_td1_49"}, {"time": 1.1333, "name": "zhiliao2/xfr_skill_xuli_td1_47"}, {"time": 1.1667, "name": "zhiliao2/xfr_skill_xuli_td1_46"}, {"time": 1.2, "name": "zhiliao2/xfr_skill_xuli_td1_45"}, {"time": 1.2333, "name": null}, {"time": 1.5667, "name": "zhiliao2/xfr_skill_xuli_td1_61"}, {"time": 1.6, "name": "zhiliao2/xfr_skill_xuli_td1_59"}, {"time": 1.6333, "name": "zhiliao2/xfr_skill_xuli_td1_57"}, {"time": 1.6667, "name": "zhiliao2/xfr_skill_xuli_td1_55"}, {"time": 1.7, "name": "zhiliao2/xfr_skill_xuli_td1_53"}, {"time": 1.7333, "name": "zhiliao2/xfr_skill_xuli_td1_51"}, {"time": 1.7667, "name": "zhiliao2/xfr_skill_xuli_td1_49"}, {"time": 1.8, "name": "zhiliao2/xfr_skill_xuli_td1_47"}, {"time": 1.8333, "name": "zhiliao2/xfr_skill_xuli_td1_46"}, {"time": 1.8667, "name": "zhiliao2/xfr_skill_xuli_td1_45"}, {"time": 1.9, "name": null}, {"time": 2.2, "name": "zhiliao2/xfr_skill_xuli_td1_61"}, {"time": 2.2333, "name": "zhiliao2/xfr_skill_xuli_td1_59"}, {"time": 2.2667, "name": "zhiliao2/xfr_skill_xuli_td1_57"}, {"time": 2.3, "name": "zhiliao2/xfr_skill_xuli_td1_55"}, {"time": 2.3333, "name": "zhiliao2/xfr_skill_xuli_td1_53"}, {"time": 2.3667, "name": "zhiliao2/xfr_skill_xuli_td1_51"}, {"time": 2.4, "name": "zhiliao2/xfr_skill_xuli_td1_49"}, {"time": 2.4333, "name": "zhiliao2/xfr_skill_xuli_td1_47"}, {"time": 2.4667, "name": "zhiliao2/xfr_skill_xuli_td1_46"}, {"time": 2.5, "name": "zhiliao2/xfr_skill_xuli_td1_45"}, {"time": 2.5333, "name": null}, {"time": 2.8667, "name": "zhiliao2/xfr_skill_xuli_td1_61"}, {"time": 2.9, "name": "zhiliao2/xfr_skill_xuli_td1_59"}, {"time": 2.9333, "name": "zhiliao2/xfr_skill_xuli_td1_57"}, {"time": 2.9667, "name": "zhiliao2/xfr_skill_xuli_td1_55"}, {"time": 3, "name": "zhiliao2/xfr_skill_xuli_td1_53"}, {"time": 3.0333, "name": "zhiliao2/xfr_skill_xuli_td1_51"}, {"time": 3.0667, "name": "zhiliao2/xfr_skill_xuli_td1_49"}, {"time": 3.1, "name": "zhiliao2/xfr_skill_xuli_td1_47"}, {"time": 3.1333, "name": "zhiliao2/xfr_skill_xuli_td1_46"}, {"time": 3.1667, "name": "zhiliao2/xfr_skill_xuli_td1_45"}, {"time": 3.2, "name": null}, {"time": 3.5333, "name": "zhiliao2/xfr_skill_xuli_td1_61"}, {"time": 3.5667, "name": "zhiliao2/xfr_skill_xuli_td1_59"}, {"time": 3.6, "name": "zhiliao2/xfr_skill_xuli_td1_57"}, {"time": 3.6333, "name": "zhiliao2/xfr_skill_xuli_td1_55"}, {"time": 3.6667, "name": "zhiliao2/xfr_skill_xuli_td1_53"}, {"time": 3.7, "name": "zhiliao2/xfr_skill_xuli_td1_51"}, {"time": 3.7333, "name": "zhiliao2/xfr_skill_xuli_td1_49"}, {"time": 3.7667, "name": "zhiliao2/xfr_skill_xuli_td1_47"}, {"time": 3.8, "name": "zhiliao2/xfr_skill_xuli_td1_46"}, {"time": 3.8333, "name": "zhiliao2/xfr_skill_xuli_td1_45"}, {"time": 3.8667, "name": null}, {"time": 4.1667, "name": "zhiliao2/xfr_skill_xuli_td1_61"}, {"time": 4.2, "name": "zhiliao2/xfr_skill_xuli_td1_59"}, {"time": 4.2333, "name": "zhiliao2/xfr_skill_xuli_td1_57"}, {"time": 4.2667, "name": "zhiliao2/xfr_skill_xuli_td1_55"}, {"time": 4.3, "name": "zhiliao2/xfr_skill_xuli_td1_53"}, {"time": 4.3333, "name": "zhiliao2/xfr_skill_xuli_td1_51"}, {"time": 4.3667, "name": "zhiliao2/xfr_skill_xuli_td1_49"}, {"time": 4.4, "name": "zhiliao2/xfr_skill_xuli_td1_47"}, {"time": 4.4333, "name": "zhiliao2/xfr_skill_xuli_td1_46"}, {"time": 4.4667, "name": "zhiliao2/xfr_skill_xuli_td1_45"}, {"time": 4.5, "name": null}]}, "zikzizi": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "color": "ffffff8a", "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.4, "color": "ffffff8a", "curve": 0.25, "c3": 0.75}, {"time": 4.5, "color": "ffffffff"}], "attachment": [{"name": "zik<PERSON><PERSON>"}]}, "zhiliao1/yzzl_skill_zd_0": {"attachment": [{"name": "zhiliao1/yzzl_skill_zd_00000"}, {"time": 0.0667, "name": "zhiliao1/yzzl_skill_zd_00001"}, {"time": 0.1333, "name": "zhiliao1/yzzl_skill_zd_00002"}, {"time": 0.2, "name": "zhiliao1/yzzl_skill_zd_00003"}, {"time": 0.2667, "name": "zhiliao1/yzzl_skill_zd_00004"}, {"time": 0.3333, "name": "zhiliao1/yzzl_skill_zd_00005"}, {"time": 0.4, "name": "zhiliao1/yzzl_skill_zd_00006"}, {"time": 0.4667, "name": "zhiliao1/yzzl_skill_zd_00007"}, {"time": 0.5333, "name": "zhiliao1/yzzl_skill_zd_00008"}, {"time": 0.6, "name": "zhiliao1/yzzl_skill_zd_00009"}, {"time": 0.6667, "name": "zhiliao1/yzzl_skill_zd_00000"}, {"time": 0.7333, "name": "zhiliao1/yzzl_skill_zd_00001"}, {"time": 0.8, "name": "zhiliao1/yzzl_skill_zd_00002"}, {"time": 0.8667, "name": "zhiliao1/yzzl_skill_zd_00003"}, {"time": 0.9333, "name": "zhiliao1/yzzl_skill_zd_00004"}, {"time": 1, "name": "zhiliao1/yzzl_skill_zd_00005"}, {"time": 1.0667, "name": "zhiliao1/yzzl_skill_zd_00006"}, {"time": 1.1333, "name": "zhiliao1/yzzl_skill_zd_00007"}, {"time": 1.2, "name": "zhiliao1/yzzl_skill_zd_00008"}, {"time": 1.2667, "name": "zhiliao1/yzzl_skill_zd_00009"}, {"time": 1.3333, "name": "zhiliao1/yzzl_skill_zd_00000"}, {"time": 1.4, "name": "zhiliao1/yzzl_skill_zd_00001"}, {"time": 1.4667, "name": "zhiliao1/yzzl_skill_zd_00002"}, {"time": 1.5333, "name": "zhiliao1/yzzl_skill_zd_00003"}, {"time": 1.6, "name": "zhiliao1/yzzl_skill_zd_00004"}, {"time": 1.6667, "name": "zhiliao1/yzzl_skill_zd_00005"}, {"time": 1.7333, "name": "zhiliao1/yzzl_skill_zd_00006"}, {"time": 1.8, "name": "zhiliao1/yzzl_skill_zd_00007"}, {"time": 1.8667, "name": "zhiliao1/yzzl_skill_zd_00008"}, {"time": 1.9333, "name": "zhiliao1/yzzl_skill_zd_00009"}, {"time": 1.9667, "name": "zhiliao1/yzzl_skill_zd_00000"}, {"time": 2.0333, "name": "zhiliao1/yzzl_skill_zd_00001"}, {"time": 2.1, "name": "zhiliao1/yzzl_skill_zd_00002"}, {"time": 2.1667, "name": "zhiliao1/yzzl_skill_zd_00003"}, {"time": 2.2333, "name": "zhiliao1/yzzl_skill_zd_00004"}, {"time": 2.3, "name": "zhiliao1/yzzl_skill_zd_00005"}, {"time": 2.3667, "name": "zhiliao1/yzzl_skill_zd_00006"}, {"time": 2.4333, "name": "zhiliao1/yzzl_skill_zd_00007"}, {"time": 2.5, "name": "zhiliao1/yzzl_skill_zd_00008"}, {"time": 2.5667, "name": "zhiliao1/yzzl_skill_zd_00009"}, {"time": 2.6333, "name": "zhiliao1/yzzl_skill_zd_00000"}, {"time": 2.7, "name": "zhiliao1/yzzl_skill_zd_00001"}, {"time": 2.7667, "name": "zhiliao1/yzzl_skill_zd_00002"}, {"time": 2.8333, "name": "zhiliao1/yzzl_skill_zd_00003"}, {"time": 2.9, "name": "zhiliao1/yzzl_skill_zd_00004"}, {"time": 2.9667, "name": "zhiliao1/yzzl_skill_zd_00005"}, {"time": 3.0333, "name": "zhiliao1/yzzl_skill_zd_00006"}, {"time": 3.1, "name": "zhiliao1/yzzl_skill_zd_00007"}, {"time": 3.1667, "name": "zhiliao1/yzzl_skill_zd_00008"}, {"time": 3.2333, "name": "zhiliao1/yzzl_skill_zd_00009"}, {"time": 3.3, "name": "zhiliao1/yzzl_skill_zd_00000"}, {"time": 3.3667, "name": "zhiliao1/yzzl_skill_zd_00001"}, {"time": 3.4333, "name": "zhiliao1/yzzl_skill_zd_00002"}, {"time": 3.5, "name": "zhiliao1/yzzl_skill_zd_00003"}, {"time": 3.5667, "name": "zhiliao1/yzzl_skill_zd_00004"}, {"time": 3.6333, "name": "zhiliao1/yzzl_skill_zd_00005"}, {"time": 3.7, "name": "zhiliao1/yzzl_skill_zd_00006"}, {"time": 3.7667, "name": "zhiliao1/yzzl_skill_zd_00007"}, {"time": 3.8333, "name": "zhiliao1/yzzl_skill_zd_00008"}, {"time": 3.9, "name": "zhiliao1/yzzl_skill_zd_00009"}, {"time": 3.9333, "name": "zhiliao1/yzzl_skill_zd_00000"}, {"time": 4, "name": "zhiliao1/yzzl_skill_zd_00001"}, {"time": 4.0667, "name": "zhiliao1/yzzl_skill_zd_00002"}, {"time": 4.1333, "name": "zhiliao1/yzzl_skill_zd_00003"}, {"time": 4.2, "name": "zhiliao1/yzzl_skill_zd_00004"}, {"time": 4.2667, "name": "zhiliao1/yzzl_skill_zd_00005"}, {"time": 4.3333, "name": "zhiliao1/yzzl_skill_zd_00006"}, {"time": 4.4, "name": "zhiliao1/yzzl_skill_zd_00007"}, {"time": 4.4667, "name": "zhiliao1/yzzl_skill_zd_00008"}]}, "tx_qua2": {"color": [{"color": "ffffffff", "curve": 0.246, "c3": 0.635, "c4": 0.55}, {"time": 0.2667, "color": "ffffff63", "curve": 0.381, "c2": 0.54, "c3": 0.744}, {"time": 0.4333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.9, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.3667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.8, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.2667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.7, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 3.1667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 3.6, "color": "ffffffff", "curve": 0.246, "c3": 0.635, "c4": 0.55}, {"time": 3.8667, "color": "ffffff63", "curve": 0.381, "c2": 0.54, "c3": 0.744}, {"time": 4.0333, "color": "ffffff00", "curve": "stepped"}, {"time": 4.0667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 4.5, "color": "ffffffff"}]}}, "bones": {"bone": {"translate": [{"x": 1.02, "y": 15.46, "curve": 0.381, "c2": 0.54, "c3": 0.743}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "x": 2.72, "y": 41.32, "curve": 0.245, "c3": 0.637, "c4": 0.55}, {"time": 2.2333, "x": 1.02, "y": 15.46, "curve": 0.381, "c2": 0.54, "c3": 0.743}, {"time": 2.7333, "curve": 0.25, "c3": 0.75}, {"time": 3.8, "x": 2.72, "y": 41.32, "curve": 0.245, "c3": 0.637, "c4": 0.55}, {"time": 4.5, "x": 1.02, "y": 15.46}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 3.63, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "angle": 3.63, "curve": 0.25, "c3": 0.75}, {"time": 4.5}]}, "bone3": {"rotate": [{"angle": 1.17, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 3.63, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 2.2333, "angle": 1.17, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "angle": 3.63, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 4.5, "angle": 1.17}]}, "bone4": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": 0.86, "y": 4.01, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "x": 0.86, "y": 4.01, "curve": 0.25, "c3": 0.75}, {"time": 4.5}]}, "bone8": {"rotate": [{"angle": -4.38, "curve": 0.38, "c2": 0.6, "c3": 0.726}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -22.44, "curve": 0.242, "c3": 0.664, "c4": 0.66}, {"time": 2.2333, "angle": -4.38, "curve": 0.38, "c2": 0.6, "c3": 0.726}, {"time": 2.5333, "curve": 0.25, "c3": 0.75}, {"time": 3.7, "angle": -22.44, "curve": 0.242, "c3": 0.664, "c4": 0.66}, {"time": 4.5, "angle": -4.38}]}, "bone9": {"rotate": [{"angle": -12.94, "curve": 0.336, "c2": 0.34, "c3": 0.68, "c4": 0.71}, {"time": 0.2667, "angle": -5.44, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": -22.44, "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 2.2333, "angle": -12.94, "curve": 0.336, "c2": 0.34, "c3": 0.68, "c4": 0.71}, {"time": 2.5333, "angle": -5.44, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "angle": -22.44, "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 4.5, "angle": -12.94}]}, "bone10": {"rotate": [{"angle": -20.86, "curve": 0.299, "c2": 0.21, "c3": 0.643, "c4": 0.58}, {"time": 0.2667, "angle": -14.19, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.9333, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": -22.44, "curve": 0.296, "c3": 0.633, "c4": 0.37}, {"time": 2.2333, "angle": -20.86, "curve": 0.299, "c2": 0.21, "c3": 0.643, "c4": 0.58}, {"time": 2.5333, "angle": -14.19, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -22.44, "curve": 0.296, "c3": 0.633, "c4": 0.37}, {"time": 4.5, "angle": -20.86}]}, "bone11": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": 0.35, "y": -2.37, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "x": 0.35, "y": -2.37, "curve": 0.25, "c3": 0.75}, {"time": 4.5}]}, "bone12": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 5.26, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "angle": 5.26, "curve": 0.25, "c3": 0.75}, {"time": 4.5}]}, "bone15": {"rotate": [{"angle": 12.46, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 0.9667, "angle": 9.14, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 2.0667, "angle": 12.76, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 2.2333, "angle": 12.46, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 3.2, "angle": 9.14, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 4.3333, "angle": 12.76, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 4.5, "angle": 12.46}]}, "bone13": {"rotate": [{"angle": -1.48}]}, "bone14": {"rotate": [{"angle": -4.99, "curve": 0.382, "c2": 0.57, "c3": 0.736}, {"time": 0.3667, "angle": -7.07, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.8667, "angle": -4.13, "curve": 0.368, "c2": 0.47, "c3": 0.753}, {"time": 1.5333, "curve": 0.243, "c3": 0.647, "c4": 0.59}, {"time": 2.2333, "angle": -4.99, "curve": 0.382, "c2": 0.57, "c3": 0.736}, {"time": 2.6, "angle": -7.07, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 3.1333, "angle": -4.13, "curve": 0.368, "c2": 0.47, "c3": 0.753}, {"time": 3.8, "curve": 0.243, "c3": 0.647, "c4": 0.59}, {"time": 4.5, "angle": -4.99}]}, "bone16": {"rotate": [{"angle": 18.09, "curve": 0.335, "c2": 0.34, "c3": 0.758}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": 22.33, "curve": 0.275, "c3": 0.62, "c4": 0.4}, {"time": 2.2333, "angle": 18.09, "curve": 0.335, "c2": 0.34, "c3": 0.758}, {"time": 3.0667, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "angle": 22.33, "curve": 0.275, "c3": 0.62, "c4": 0.4}, {"time": 4.5, "angle": 18.09}]}, "bone17": {"rotate": [{"angle": 21.88, "curve": 0.274, "c2": 0.11, "c3": 0.753}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 22.33, "curve": 0.316, "c3": 0.65, "c4": 0.35}, {"time": 2.2333, "angle": 21.88, "curve": 0.274, "c2": 0.11, "c3": 0.753}, {"time": 3.2667, "curve": 0.25, "c3": 0.75}, {"time": 4.4, "angle": 22.33, "curve": 0.316, "c3": 0.65, "c4": 0.35}, {"time": 4.5, "angle": 21.88}]}, "bone18": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 11.89, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "curve": 0.25, "c3": 0.75}, {"time": 3.4333, "angle": 11.89, "curve": 0.25, "c3": 0.75}, {"time": 4.5}]}, "bone19": {"rotate": [{"angle": 2.88, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 11.89, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.2333, "angle": 2.88, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "angle": 11.89, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4.5, "angle": 2.88}]}, "bone20": {"rotate": [{"angle": 2.88, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 11.89, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.2333, "angle": 2.88, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "angle": 11.89, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4.5, "angle": 2.88}]}, "bone21": {"rotate": [{"angle": 7.48, "curve": 0.333, "c2": 0.33, "c3": 0.68, "c4": 0.71}, {"time": 0.3, "angle": 2.88, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 11.89, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.2333, "angle": 7.48, "curve": 0.333, "c2": 0.33, "c3": 0.68, "c4": 0.71}, {"time": 2.5667, "angle": 2.88, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.9, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "angle": 11.89, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4.5, "angle": 7.48}]}, "bone22": {"rotate": [{"angle": -14.76, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": -19.48, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.2333, "angle": -14.76, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": -19.48, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 4.5, "angle": -14.76}]}, "bone23": {"rotate": [{"angle": -19.48, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.7667, "angle": -4.72, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": -19.48, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3, "angle": -4.72, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.3, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -19.48}]}, "bone24": {"rotate": [{"angle": 0.55, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 0.6333, "angle": 2.81, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.7333, "angle": -1.11, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 2.2333, "angle": 0.55, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 2.8667, "angle": 2.81, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -1.11, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 4.5, "angle": 0.55}]}, "bone25": {"rotate": [{"angle": -0.77, "curve": 0.292, "c2": 0.2, "c3": 0.673, "c4": 0.69}, {"time": 0.6333, "angle": 1.92, "curve": 0.381, "c2": 0.59, "c3": 0.73}, {"time": 0.9333, "angle": 2.81, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.0667, "angle": -1.11, "curve": 0.293, "c3": 0.631, "c4": 0.37}, {"time": 2.2333, "angle": -0.77, "curve": 0.292, "c2": 0.2, "c3": 0.673, "c4": 0.69}, {"time": 2.8667, "angle": 1.92, "curve": 0.381, "c2": 0.59, "c3": 0.73}, {"time": 3.1667, "angle": 2.81, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.3333, "angle": -1.11, "curve": 0.293, "c3": 0.631, "c4": 0.37}, {"time": 4.5, "angle": -0.77}]}, "bone26": {"rotate": [{"angle": 2.81, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -1.11, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": 2.81, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": -1.11, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": 2.81}]}, "bone27": {"rotate": [{"angle": 1.92, "curve": 0.381, "c2": 0.59, "c3": 0.73}, {"time": 0.3, "angle": 2.81, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.4667, "angle": -1.11, "curve": 0.243, "c3": 0.658, "c4": 0.63}, {"time": 2.2333, "angle": 1.92, "curve": 0.381, "c2": 0.59, "c3": 0.73}, {"time": 2.5667, "angle": 2.81, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.7, "angle": -1.11, "curve": 0.243, "c3": 0.658, "c4": 0.63}, {"time": 4.5, "angle": 1.92}]}, "bone28": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -4.71, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "angle": -4.71, "curve": 0.25, "c3": 0.75}, {"time": 4.5}]}, "bone29": {"rotate": [{"angle": -1.52, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -4.71, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 2.2333, "angle": -1.52, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "angle": -4.71, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 4.5, "angle": -1.52}]}, "bone30": {"rotate": [{"angle": -3.73, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": -4.71, "curve": 0.273, "c3": 0.619, "c4": 0.41}, {"time": 2.2333, "angle": -3.73, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 3.1, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": -4.71, "curve": 0.273, "c3": 0.619, "c4": 0.41}, {"time": 4.5, "angle": -3.73}]}, "bone31": {"rotate": [{"angle": -1.52, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -4.71, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 2.2333, "angle": -1.52, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "angle": -4.71, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 4.5, "angle": -1.52}]}, "bone32": {"rotate": [{"angle": -3.73, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": -4.71, "curve": 0.273, "c3": 0.619, "c4": 0.41}, {"time": 2.2333, "angle": -3.73, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 3.1, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": -4.71, "curve": 0.273, "c3": 0.619, "c4": 0.41}, {"time": 4.5, "angle": -3.73}]}, "bone33": {"rotate": [{"angle": -4.51, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "angle": -4.71, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2.2333, "angle": -4.51, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 2.3667, "angle": -4.71, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 4.5, "angle": -4.51}]}, "bone34": {"rotate": [{"angle": -3.73, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": -4.71, "curve": 0.273, "c3": 0.619, "c4": 0.41}, {"time": 2.2333, "angle": -3.73, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 3.1, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": -4.71, "curve": 0.273, "c3": 0.619, "c4": 0.41}, {"time": 4.5, "angle": -3.73}]}, "bone35": {"rotate": [{"angle": -4.51, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "angle": -4.71, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2.2333, "angle": -4.51, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 2.3667, "angle": -4.71, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 4.5, "angle": -4.51}]}, "bone36": {"rotate": [{"angle": -2.36, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5333, "angle": -4.71, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.2333, "angle": -2.36, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.7667, "angle": -4.71, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.5, "angle": -2.36}]}, "bone37": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 9.86, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": 9.86, "curve": 0.25, "c3": 0.75}, {"time": 4.5}]}, "bone38": {"rotate": [{"angle": 4.93, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": 9.86, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.2333, "angle": 4.93, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.8, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "angle": 9.86, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.5, "angle": 4.93}]}, "bone39": {"rotate": [{"angle": 9.86, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": 9.86, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": 9.86}]}, "bone40": {"rotate": [{"angle": 4.93, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": 9.86, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.2333, "angle": 4.93, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.8, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "angle": 9.86, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.5, "angle": 4.93}]}, "bone41": {"rotate": [{"angle": 9.86, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": 9.86, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": 9.86}]}, "bone42": {"rotate": [{"angle": 4.93, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5667, "angle": 9.86, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.2333, "angle": 4.93, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.8, "angle": 9.86, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.5, "angle": 4.93}]}, "bone43": {"rotate": [{"angle": 9.86, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": 9.86, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": 9.86}]}, "bone44": {"rotate": [{"angle": 4.93, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5667, "angle": 9.86, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.2333, "angle": 4.93, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.8, "angle": 9.86, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.5, "angle": 4.93}]}, "bone45": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 9.86, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": 9.86, "curve": 0.25, "c3": 0.75}, {"time": 4.5}]}, "bone52": {"translate": [{"x": -21.69, "y": -17.63, "curve": 0.353, "c2": 0.4, "c3": 0.757}, {"time": 0.7, "x": -30, "y": -10, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "x": -18.26, "y": -20.78, "curve": 0.264, "c3": 0.618, "c4": 0.43}, {"time": 2.2333, "x": -21.69, "y": -17.63, "curve": 0.353, "c2": 0.4, "c3": 0.757}, {"time": 2.9667, "x": -30, "y": -10, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "x": -18.26, "y": -20.78, "curve": 0.264, "c3": 0.618, "c4": 0.43}, {"time": 4.5, "x": -21.69, "y": -17.63}]}, "bone54": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 14.25, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "angle": 14.25, "curve": 0.25, "c3": 0.75}, {"time": 4.5}]}, "bone55": {"rotate": [{"angle": 3.94, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 14.25, "curve": 0.243, "c3": 0.65, "c4": 0.6}, {"time": 2.2333, "angle": 3.94, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.6333, "curve": 0.25, "c3": 0.75}, {"time": 3.7, "angle": 14.25, "curve": 0.243, "c3": 0.65, "c4": 0.6}, {"time": 4.5, "angle": 3.94}]}, "bone56": {"rotate": [{"angle": 9.99, "curve": 0.354, "c2": 0.41, "c3": 0.757}, {"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 14.25, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 2.2333, "angle": 9.99, "curve": 0.354, "c2": 0.41, "c3": 0.757}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "angle": 14.25, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 4.5, "angle": 9.99}]}, "bone57": {"rotate": [{"angle": -3.04, "curve": 0.38, "c2": 0.6, "c3": 0.725}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": -16.06, "curve": 0.242, "c3": 0.666, "c4": 0.66}, {"time": 2.2333, "angle": -3.04, "curve": 0.38, "c2": 0.6, "c3": 0.725}, {"time": 2.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.6333, "angle": -16.06, "curve": 0.242, "c3": 0.666, "c4": 0.66}, {"time": 4.5, "angle": -3.04}]}, "bone58": {"rotate": [{"angle": -9.74, "curve": 0.334, "c2": 0.33, "c3": 0.677, "c4": 0.7}, {"time": 0.3, "angle": -4.44, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": -16.06, "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 2.2333, "angle": -9.74, "curve": 0.334, "c2": 0.33, "c3": 0.677, "c4": 0.7}, {"time": 2.5667, "angle": -4.44, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.9333, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -16.06, "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 4.5, "angle": -9.74}]}, "bone59": {"rotate": [{"angle": -15.53, "curve": 0.292, "c2": 0.17, "c3": 0.636, "c4": 0.54}, {"time": 0.3, "angle": -11.26, "curve": 0.354, "c2": 0.41, "c3": 0.757}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -16.06, "curve": 0.307, "c3": 0.642, "c4": 0.35}, {"time": 2.2333, "angle": -15.53, "curve": 0.292, "c2": 0.17, "c3": 0.636, "c4": 0.54}, {"time": 2.5667, "angle": -11.26, "curve": 0.354, "c2": 0.41, "c3": 0.757}, {"time": 3.3, "curve": 0.25, "c3": 0.75}, {"time": 4.3667, "angle": -16.06, "curve": 0.307, "c3": 0.642, "c4": 0.35}, {"time": 4.5, "angle": -15.53}]}, "bone60": {"rotate": [{"angle": 1.69, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": 12.98, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.2333, "angle": 1.69, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.4667, "curve": 0.25, "c3": 0.75}, {"time": 3.6333, "angle": 12.98, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 4.5, "angle": 1.69}]}, "bone61": {"rotate": [{"angle": 6.46, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 0.2333, "angle": 3.14, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": 12.98, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.2333, "angle": 6.46, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 2.4667, "angle": 3.14, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.7667, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "angle": 12.98, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.5, "angle": 6.46}]}, "bone62": {"rotate": [{"angle": 8.19, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.2333, "angle": 4.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 12.98, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.2333, "angle": 8.19, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 2.4667, "angle": 4.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.9, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "angle": 12.98, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4.5, "angle": 8.19}]}, "bone63": {"rotate": [{"angle": 3.4, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": 12.98, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 2.2333, "angle": 3.4, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 2.6, "curve": 0.25, "c3": 0.75}, {"time": 3.7667, "angle": 12.98, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 4.5, "angle": 3.4}]}, "bone64": {"rotate": [{"angle": 8.36, "curve": 0.332, "c2": 0.33, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": 3.14, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 12.98, "curve": 0.259, "c3": 0.618, "c4": 0.45}, {"time": 2.2333, "angle": 8.36, "curve": 0.332, "c2": 0.33, "c3": 0.68, "c4": 0.71}, {"time": 2.6, "angle": 3.14, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.9, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "angle": 12.98, "curve": 0.259, "c3": 0.618, "c4": 0.45}, {"time": 4.5, "angle": 8.36}]}, "bone65": {"rotate": [{"angle": 10.03, "curve": 0.318, "c2": 0.29, "c3": 0.666, "c4": 0.66}, {"time": 0.3333, "angle": 4.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": 12.98, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 2.2333, "angle": 10.03, "curve": 0.318, "c2": 0.29, "c3": 0.666, "c4": 0.66}, {"time": 2.6, "angle": 4.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "angle": 12.98, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 4.5, "angle": 10.03}]}, "bone69": {"rotate": [{"angle": 5.12, "curve": 0.381, "c2": 0.54, "c3": 0.744}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": 12.98, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 2.2333, "angle": 5.12, "curve": 0.381, "c2": 0.54, "c3": 0.744}, {"time": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "angle": 12.98, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 4.5, "angle": 5.12}]}, "bone70": {"rotate": [{"angle": 10.12, "curve": 0.318, "c2": 0.29, "c3": 0.678, "c4": 0.7}, {"time": 0.4333, "angle": 3.14, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": 12.98, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 2.2333, "angle": 10.12, "curve": 0.318, "c2": 0.29, "c3": 0.678, "c4": 0.7}, {"time": 2.7, "angle": 3.14, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "angle": 12.98, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 4.5, "angle": 10.12}]}, "bone71": {"rotate": [{"angle": 11.55, "curve": 0.298, "c2": 0.22, "c3": 0.659, "c4": 0.64}, {"time": 0.4333, "angle": 4.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": 12.98, "curve": 0.288, "c3": 0.627, "c4": 0.38}, {"time": 2.2333, "angle": 11.55, "curve": 0.298, "c2": 0.22, "c3": 0.659, "c4": 0.64}, {"time": 2.7, "angle": 4.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.1333, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "angle": 12.98, "curve": 0.288, "c3": 0.627, "c4": 0.38}, {"time": 4.5, "angle": 11.55}]}, "bone72": {"rotate": [{"angle": 6.49, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": 12.98, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.2333, "angle": 6.49, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.7667, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "angle": 12.98, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.5, "angle": 6.49}]}, "bone73": {"rotate": [{"angle": 11.28, "curve": 0.303, "c2": 0.24, "c3": 0.674, "c4": 0.69}, {"time": 0.5333, "angle": 3.14, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": 12.98, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.2333, "angle": 11.28, "curve": 0.303, "c2": 0.24, "c3": 0.674, "c4": 0.69}, {"time": 2.7667, "angle": 3.14, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 3.1, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "angle": 12.98, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4.5, "angle": 11.28}]}, "bone74": {"rotate": [{"angle": 12.45, "curve": 0.278, "c2": 0.15, "c3": 0.651, "c4": 0.61}, {"time": 0.5333, "angle": 4.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 12.98, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2.2333, "angle": 12.45, "curve": 0.278, "c2": 0.15, "c3": 0.651, "c4": 0.61}, {"time": 2.7667, "angle": 4.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 4.3667, "angle": 12.98, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 4.5, "angle": 12.45}]}, "bone75": {"rotate": [{"angle": 12.52, "curve": 0.358, "c2": 0.65, "c3": 0.693}, {"time": 0.1, "angle": 12.98, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.5333, "angle": 8.2, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.1667, "curve": 0.245, "c3": 0.714, "c4": 0.85}, {"time": 2.2333, "angle": 12.52, "curve": 0.358, "c2": 0.65, "c3": 0.693}, {"time": 2.3667, "angle": 12.98, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.7667, "angle": 8.2, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.4333, "curve": 0.245, "c3": 0.714, "c4": 0.85}, {"time": 4.5, "angle": 12.52}]}, "bone46": {"rotate": [{"angle": -14.92, "curve": 0.316, "c2": 0.27, "c3": 0.757}, {"time": 0.8667, "angle": 22.29, "curve": 0.242, "c3": 0.661, "c4": 0.65}, {"time": 1.7333, "angle": -10.88, "curve": 0.364, "c2": 0.47, "c3": 0.704, "c4": 0.84}, {"time": 1.9667, "angle": -18.69, "curve": 0.354, "c2": 0.65, "c3": 0.689}, {"time": 2.0667, "angle": -19.66, "curve": 0.287, "c3": 0.627, "c4": 0.38}, {"time": 2.2333, "angle": -14.92, "curve": 0.316, "c2": 0.27, "c3": 0.757}, {"time": 3.1333, "angle": 22.29, "curve": 0.242, "c3": 0.661, "c4": 0.65}, {"time": 4, "angle": -10.88, "curve": 0.364, "c2": 0.47, "c3": 0.704, "c4": 0.84}, {"time": 4.2, "angle": -18.69, "curve": 0.354, "c2": 0.65, "c3": 0.689}, {"time": 4.3, "angle": -19.66, "curve": 0.287, "c3": 0.627, "c4": 0.38}, {"time": 4.5, "angle": -14.92}]}, "bone47": {"rotate": [{"angle": -14.11, "curve": 0.375, "c2": 0.61, "c3": 0.717}, {"time": 0.2667, "angle": -19.66, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 22.29, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 1.7333, "angle": 8.77, "curve": 0.327, "c2": 0.31, "c3": 0.666, "c4": 0.67}, {"time": 1.9667, "angle": -2.14, "curve": 0.336, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 2.0667, "angle": -6.17, "curve": 0.348, "c2": 0.38, "c3": 0.685, "c4": 0.73}, {"time": 2.2333, "angle": -14.11, "curve": 0.375, "c2": 0.61, "c3": 0.717}, {"time": 2.5, "angle": -19.66, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": 22.29, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 4, "angle": 8.77, "curve": 0.327, "c2": 0.31, "c3": 0.666, "c4": 0.67}, {"time": 4.2, "angle": -2.14, "curve": 0.336, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 4.3, "angle": -6.17, "curve": 0.348, "c2": 0.38, "c3": 0.685, "c4": 0.73}, {"time": 4.5, "angle": -14.11}]}, "bone48": {"rotate": [{"angle": 4.97, "curve": 0.368, "c2": 0.47, "c3": 0.753}, {"time": 0.6667, "angle": -19.66, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": 22.29, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.9667, "angle": 17, "curve": 0.323, "c2": 0.3, "c3": 0.657, "c4": 0.63}, {"time": 2.0667, "angle": 13.52, "curve": 0.32, "c2": 0.29, "c3": 0.658, "c4": 0.64}, {"time": 2.2333, "angle": 4.97, "curve": 0.368, "c2": 0.47, "c3": 0.753}, {"time": 2.9333, "angle": -19.66, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 22.29, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4.2, "angle": 17, "curve": 0.323, "c2": 0.3, "c3": 0.657, "c4": 0.63}, {"time": 4.3, "angle": 13.52, "curve": 0.32, "c2": 0.29, "c3": 0.658, "c4": 0.64}, {"time": 4.5, "angle": 4.97}]}, "bone49": {"rotate": [{"angle": 14.93, "curve": 0.282, "c2": 0.14, "c3": 0.754}, {"time": 1, "angle": -11.96, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 1.7333, "angle": 6.87, "curve": 0.34, "c2": 0.36, "c3": 0.675, "c4": 0.69}, {"time": 1.8667, "angle": 9.9, "curve": 0.346, "c2": 0.38, "c3": 0.681, "c4": 0.72}, {"time": 1.9667, "angle": 12.88, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 2.1667, "angle": 15.68, "curve": 0.31, "c3": 0.645, "c4": 0.35}, {"time": 2.2333, "angle": 14.93, "curve": 0.282, "c2": 0.14, "c3": 0.754}, {"time": 3.2333, "angle": -11.96, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 4, "angle": 6.87, "curve": 0.34, "c2": 0.36, "c3": 0.675, "c4": 0.69}, {"time": 4.1, "angle": 9.9, "curve": 0.346, "c2": 0.38, "c3": 0.681, "c4": 0.72}, {"time": 4.2, "angle": 12.88, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 4.4, "angle": 15.68, "curve": 0.31, "c3": 0.645, "c4": 0.35}, {"time": 4.5, "angle": 14.93}]}, "bone50": {"rotate": [{"angle": 9.31, "curve": 0.381, "c2": 0.59, "c3": 0.73}, {"time": 0.3333, "angle": 15.68, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -11.96, "curve": 0.273, "c3": 0.619, "c4": 0.41}, {"time": 1.7333, "angle": -6.18, "curve": 0.325, "c2": 0.31, "c3": 0.66, "c4": 0.64}, {"time": 1.8667, "angle": -3.05, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 1.9667, "angle": 0.51, "curve": 0.335, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 2.1667, "angle": 6.79, "curve": 0.339, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 2.2333, "angle": 9.31, "curve": 0.381, "c2": 0.59, "c3": 0.73}, {"time": 2.6, "angle": 15.68, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -11.96, "curve": 0.273, "c3": 0.619, "c4": 0.41}, {"time": 4, "angle": -6.18, "curve": 0.325, "c2": 0.31, "c3": 0.66, "c4": 0.64}, {"time": 4.1, "angle": -3.05, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 4.2, "angle": 0.51, "curve": 0.335, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 4.4, "angle": 6.79, "curve": 0.339, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 4.5, "angle": 9.31}]}, "bone51": {"rotate": [{"angle": -3.86, "curve": 0.353, "c2": 0.41, "c3": 0.757}, {"time": 0.8, "angle": 15.68, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 1.7333, "angle": -10.78, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 1.8667, "angle": -11.96, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.9667, "angle": -10.89, "curve": 0.303, "c2": 0.2, "c3": 0.641, "c4": 0.56}, {"time": 2.1667, "angle": -6.18, "curve": 0.327, "c2": 0.31, "c3": 0.661, "c4": 0.65}, {"time": 2.2333, "angle": -3.86, "curve": 0.353, "c2": 0.41, "c3": 0.757}, {"time": 3.0333, "angle": 15.68, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 4, "angle": -10.78, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 4.1, "angle": -11.96, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 4.2, "angle": -10.89, "curve": 0.303, "c2": 0.2, "c3": 0.641, "c4": 0.56}, {"time": 4.4, "angle": -6.18, "curve": 0.327, "c2": 0.31, "c3": 0.661, "c4": 0.65}, {"time": 4.5, "angle": -3.86}]}, "bone5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 2.96, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "angle": 2.96, "curve": 0.25, "c3": 0.75}, {"time": 4.5}]}, "bone6": {"rotate": [{"angle": 0.77, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 0.3333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.5, "angle": 2.96, "curve": 0.243, "c3": 0.653, "c4": 0.61}, {"time": 2.2333, "angle": 0.77, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 2.6, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 3.7333, "angle": 2.96, "curve": 0.243, "c3": 0.653, "c4": 0.61}, {"time": 4.5, "angle": 0.77}]}, "bone7": {"rotate": [{"angle": 1.82, "curve": 0.364, "c2": 0.45, "c3": 0.754}, {"time": 0.6333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.8, "angle": 2.96, "curve": 0.257, "c3": 0.619, "c4": 0.46}, {"time": 2.2333, "angle": 1.82, "curve": 0.364, "c2": 0.45, "c3": 0.754}, {"time": 2.9, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.0333, "angle": 2.96, "curve": 0.257, "c3": 0.619, "c4": 0.46}, {"time": 4.5, "angle": 1.82}]}, "bone66": {"rotate": [{"angle": 5.94, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.4333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.5333, "angle": 17.68, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.2333, "angle": 5.94, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 2.6667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 3.8, "angle": 17.68, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 4.5, "angle": 5.94}]}, "bone67": {"rotate": [{"angle": 14.42, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 0.4333, "angle": 5.94, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.8333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.9667, "angle": 17.68, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.2333, "angle": 14.42, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 5.94, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.2, "angle": 17.68, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 4.5, "angle": 14.42}]}, "bone68": {"rotate": [{"angle": 17.24, "curve": 0.286, "c2": 0.64, "c3": 0.615}, {"time": 0.1333, "angle": 17.68, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.4333, "angle": 14.42, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.2667, "curve": 0.606, "c2": 0.01, "c3": 0.594, "c4": 0.8}, {"time": 2.2333, "angle": 17.24, "curve": 0.286, "c2": 0.64, "c3": 0.615}, {"time": 2.4, "angle": 17.68, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 14.42, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.5, "curve": 0.606, "c2": 0.01, "c3": 0.594, "c4": 0.8}, {"time": 4.5, "angle": 17.24}]}, "bone80": {"rotate": [{"angle": -1.86, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 0.3667, "angle": -4.89, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.5, "angle": 6.18, "curve": 0.243, "c3": 0.65, "c4": 0.61}, {"time": 2.2333, "angle": -1.86, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 2.6, "angle": -4.89, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 3.7333, "angle": 6.18, "curve": 0.243, "c3": 0.65, "c4": 0.61}, {"time": 4.5, "angle": -1.86}]}, "bone81": {"rotate": [{"angle": 1.71, "curve": 0.338, "c2": 0.35, "c3": 0.688, "c4": 0.73}, {"time": 0.3667, "angle": -2.84, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6333, "angle": -4.89, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.7667, "angle": 6.18, "curve": 0.256, "c3": 0.62, "c4": 0.47}, {"time": 2.2333, "angle": 1.71, "curve": 0.338, "c2": 0.35, "c3": 0.688, "c4": 0.73}, {"time": 2.6, "angle": -2.84, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 2.9, "angle": -4.89, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.0333, "angle": 6.18, "curve": 0.256, "c3": 0.62, "c4": 0.47}, {"time": 4.5, "angle": 1.71}]}, "bone82": {"rotate": [{"angle": 5.04, "curve": 0.3, "c2": 0.22, "c3": 0.65, "c4": 0.61}, {"time": 0.3667, "angle": 0.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.9333, "angle": -4.89, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.0667, "angle": 6.18, "curve": 0.289, "c3": 0.628, "c4": 0.38}, {"time": 2.2333, "angle": 5.04, "curve": 0.3, "c2": 0.22, "c3": 0.65, "c4": 0.61}, {"time": 2.6, "angle": 0.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.1667, "angle": -4.89, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.3, "angle": 6.18, "curve": 0.289, "c3": 0.628, "c4": 0.38}, {"time": 4.5, "angle": 5.04}]}, "bone83": {"rotate": [{"angle": 6.02, "curve": 0.304, "c2": 0.65, "c3": 0.636}, {"time": 0.1, "angle": 6.18, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3667, "angle": 4.14, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.2, "angle": -4.89, "curve": 0.596, "c2": 0.01, "c3": 0.55, "c4": 0.87}, {"time": 2.2333, "angle": 6.02, "curve": 0.304, "c2": 0.65, "c3": 0.636}, {"time": 2.3333, "angle": 6.18, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6, "angle": 4.14, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.4667, "angle": -4.89, "curve": 0.596, "c2": 0.01, "c3": 0.55, "c4": 0.87}, {"time": 4.5, "angle": 6.02}]}, "bone76": {"rotate": [{"angle": 1.04, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 0.3667, "angle": 3.43, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -5.29, "curve": 0.243, "c3": 0.65, "c4": 0.61}, {"time": 2.2333, "angle": 1.04, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 2.6, "angle": 3.43, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "angle": -5.29, "curve": 0.243, "c3": 0.65, "c4": 0.61}, {"time": 4.5, "angle": 1.04}]}, "bone77": {"rotate": [{"angle": -3.17, "curve": 0.32, "c2": 0.29, "c3": 0.669, "c4": 0.67}, {"time": 0.3667, "angle": 0.5, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.8, "angle": 3.43, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": -5.29, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.2333, "angle": -3.17, "curve": 0.32, "c2": 0.29, "c3": 0.669, "c4": 0.67}, {"time": 2.6, "angle": 0.5, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.0333, "angle": 3.43, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": -5.29, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 4.5, "angle": -3.17}]}, "bone78": {"rotate": [{"angle": -5.07, "curve": 0.354, "c2": 0.65, "c3": 0.689}, {"time": 0.1, "angle": -5.29, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3667, "angle": -3.68, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.2, "angle": 3.43, "curve": 0.246, "c3": 0.72, "c4": 0.87}, {"time": 2.2333, "angle": -5.07, "curve": 0.354, "c2": 0.65, "c3": 0.689}, {"time": 2.3333, "angle": -5.29, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6, "angle": -3.68, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.4667, "angle": 3.43, "curve": 0.246, "c3": 0.72, "c4": 0.87}, {"time": 4.5, "angle": -5.07}]}, "bone79": {"rotate": [{"angle": -1.55, "curve": 0.358, "c2": 0.42, "c3": 0.708, "c4": 0.81}, {"time": 0.3667, "angle": -4.74, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.5, "angle": -5.29, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": 3.43, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 2.2333, "angle": -1.55, "curve": 0.358, "c2": 0.42, "c3": 0.708, "c4": 0.81}, {"time": 2.6, "angle": -4.74, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 2.7667, "angle": -5.29, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "angle": 3.43, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 4.5, "angle": -1.55}]}, "zhiliao1": {"translate": [{"x": 3.19, "y": 158.91}]}, "zhiliao5": {"translate": [{"x": 69.5, "y": 150.23}], "scale": [{"x": 2.564, "y": 2.564}]}, "zhiliao7": {"translate": [{"x": 37.18, "y": 179.08}], "scale": [{"x": 2.564, "y": 2.564}]}, "zhiliao10": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -0.031, "y": -0.031, "curve": "stepped"}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "x": -0.031, "y": -0.031, "curve": "stepped"}, {"time": 1.7333, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "x": -0.031, "y": -0.031, "curve": "stepped"}, {"time": 2.6, "curve": 0.25, "c3": 0.75}, {"time": 3.4333, "x": -0.031, "y": -0.031, "curve": "stepped"}, {"time": 3.4667, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "x": -0.031, "y": -0.031}]}, "zhiliao2": {"translate": [{"x": 2.66, "y": 134.13}]}, "zhiliao3": {"translate": [{"x": -42.87, "y": 100.54}], "scale": [{"x": 2.564, "y": 2.564}]}, "zhiliao": {"translate": [{"x": -1172.82, "y": -28.95}]}, "zhiliao9": {"scale": [{"time": 0.5667, "curve": 0.552, "c2": 0.01, "c3": 0.783, "c4": 0.5}, {"time": 0.7333, "x": 1.27, "y": 1.27, "curve": 0.236, "c2": 0.5, "c3": 0.458}, {"time": 0.9, "curve": 0.552, "c2": 0.01, "c3": 0.783, "c4": 0.5}, {"time": 1.0667, "x": 1.27, "y": 1.27, "curve": 0.236, "c2": 0.5, "c3": 0.458}, {"time": 1.2333, "curve": 0.552, "c2": 0.01, "c3": 0.783, "c4": 0.5}, {"time": 1.4, "x": 1.27, "y": 1.27, "curve": 0.236, "c2": 0.5, "c3": 0.458}, {"time": 1.5667, "curve": 0.552, "c2": 0.01, "c3": 0.783, "c4": 0.5}, {"time": 1.7333, "x": 1.27, "y": 1.27, "curve": 0.236, "c2": 0.5, "c3": 0.458}, {"time": 1.9, "curve": 0.552, "c2": 0.01, "c3": 0.783, "c4": 0.5}, {"time": 2.0667, "x": 1.27, "y": 1.27, "curve": 0.236, "c2": 0.5, "c3": 0.458}, {"time": 2.2333, "curve": 0.552, "c2": 0.01, "c3": 0.783, "c4": 0.5}, {"time": 2.4, "x": 1.27, "y": 1.27, "curve": 0.236, "c2": 0.5, "c3": 0.458}, {"time": 2.5667, "curve": 0.552, "c2": 0.01, "c3": 0.783, "c4": 0.5}, {"time": 2.7333, "x": 1.27, "y": 1.27, "curve": 0.236, "c2": 0.5, "c3": 0.458}, {"time": 2.9, "curve": 0.552, "c2": 0.01, "c3": 0.783, "c4": 0.5}, {"time": 3.0667, "x": 1.27, "y": 1.27, "curve": 0.236, "c2": 0.5, "c3": 0.458}, {"time": 3.2333, "curve": 0.552, "c2": 0.01, "c3": 0.783, "c4": 0.5}, {"time": 3.4, "x": 1.27, "y": 1.27, "curve": 0.236, "c2": 0.5, "c3": 0.458}, {"time": 3.5667}]}, "zhiliao11": {"scale": [{"x": 0.484, "y": 0.484, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4333, "x": -0.031, "y": -0.031, "curve": "stepped"}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -0.031, "y": -0.031, "curve": "stepped"}, {"time": 1.3667, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "x": -0.031, "y": -0.031, "curve": "stepped"}, {"time": 2.2667, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "x": -0.031, "y": -0.031, "curve": "stepped"}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "x": -0.031, "y": -0.031, "curve": "stepped"}, {"time": 4.0667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.5, "x": 0.484, "y": 0.484}]}, "zhakai": {"rotate": [{"time": 3.6333, "angle": -14.31}], "translate": [{"time": 3.6333, "y": 1211.38}], "scale": [{"time": 3.6333, "curve": 0, "c2": 1, "c3": 0.75}, {"time": 4.2333, "x": 6.073, "y": 6.073}]}, "bone84": {"translate": [{"x": 17.88, "y": -10.73}], "scale": [{"x": 1.884, "y": 1.884}]}}}, "dianji_fuhuo": {"slots": {"danjifuhuo011": {"color": [{"time": 0.4, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.8333, "color": "ffffff00"}]}, "danjifuhuo02": {"color": [{"time": 0.4, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.8333, "color": "ffffff00"}]}, "tx_qua": {"color": [{"color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.1, "color": "ffffff00"}]}, "zhiliao1/yzzl_skill_zd_00000": {"color": [{"time": 0.3333, "color": "ffffffff", "curve": 0.418, "c2": 0.01, "c3": 0.739, "c4": 0.39}, {"time": 0.4, "color": "fffffff0", "curve": 0.396, "c2": 0.29, "c3": 0.426}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{"name": "zhiliao1/yzzl_skill_zd_00001"}, {"time": 0.0667, "name": "zhiliao1/yzzl_skill_zd_00002"}, {"time": 0.1333, "name": "zhiliao1/yzzl_skill_zd_00003"}, {"time": 0.2, "name": "zhiliao1/yzzl_skill_zd_00004"}, {"time": 0.2667, "name": "zhiliao1/yzzl_skill_zd_00005"}, {"time": 0.3333, "name": "zhiliao1/yzzl_skill_zd_00006"}, {"time": 0.4, "name": "zhiliao1/yzzl_skill_zd_00007"}, {"time": 0.4667, "name": "zhiliao1/yzzl_skill_zd_00008"}, {"time": 0.5333, "name": "zhiliao1/yzzl_skill_zd_00009"}, {"time": 0.5667, "name": "zhiliao1/yzzl_skill_zd_00000"}, {"time": 0.6333, "name": "zhiliao1/yzzl_skill_zd_00001"}, {"time": 0.7, "name": "zhiliao1/yzzl_skill_zd_00002"}, {"time": 0.7667, "name": "zhiliao1/yzzl_skill_zd_00003"}, {"time": 0.8333, "name": "zhiliao1/yzzl_skill_zd_00004"}, {"time": 0.9, "name": "zhiliao1/yzzl_skill_zd_00005"}, {"time": 0.9667, "name": "zhiliao1/yzzl_skill_zd_00006"}, {"time": 1.0333, "name": "zhiliao1/yzzl_skill_zd_00007"}, {"time": 1.1, "name": "zhiliao1/yzzl_skill_zd_00008"}]}, "danjifuhuo03": {"color": [{"time": 0.4, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.8333, "color": "ffffff00"}]}, "danjifuhuo017": {"color": [{"time": 0.4, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.8333, "color": "ffffff00"}]}, "zhiliao2/xfr_skill_xuli_td1_47": {"color": [{"time": 0.3333, "color": "ffffffff", "curve": 0.418, "c2": 0.01, "c3": 0.739, "c4": 0.39}, {"time": 0.4, "color": "fffffff0", "curve": 0.396, "c2": 0.29, "c3": 0.426}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{"time": 0.2, "name": "zhiliao2/xfr_skill_xuli_td1_61"}, {"time": 0.2333, "name": "zhiliao2/xfr_skill_xuli_td1_59"}, {"time": 0.2667, "name": "zhiliao2/xfr_skill_xuli_td1_57"}, {"time": 0.3, "name": "zhiliao2/xfr_skill_xuli_td1_55"}, {"time": 0.3333, "name": "zhiliao2/xfr_skill_xuli_td1_53"}, {"time": 0.3667, "name": "zhiliao2/xfr_skill_xuli_td1_51"}, {"time": 0.4, "name": "zhiliao2/xfr_skill_xuli_td1_49"}, {"time": 0.4333, "name": "zhiliao2/xfr_skill_xuli_td1_47"}, {"time": 0.4667, "name": "zhiliao2/xfr_skill_xuli_td1_46"}, {"time": 0.5, "name": "zhiliao2/xfr_skill_xuli_td1_45"}, {"time": 0.5333, "name": null}, {"time": 0.8333, "name": "zhiliao2/xfr_skill_xuli_td1_61"}, {"time": 0.8667, "name": "zhiliao2/xfr_skill_xuli_td1_59"}, {"time": 0.9, "name": "zhiliao2/xfr_skill_xuli_td1_57"}, {"time": 0.9333, "name": "zhiliao2/xfr_skill_xuli_td1_55"}, {"time": 0.9667, "name": "zhiliao2/xfr_skill_xuli_td1_53"}, {"time": 1, "name": "zhiliao2/xfr_skill_xuli_td1_51"}, {"time": 1.0333, "name": "zhiliao2/xfr_skill_xuli_td1_49"}, {"time": 1.0667, "name": "zhiliao2/xfr_skill_xuli_td1_47"}, {"time": 1.1, "name": "zhiliao2/xfr_skill_xuli_td1_46"}, {"time": 1.1333, "name": "zhiliao2/xfr_skill_xuli_td1_45"}, {"time": 1.1667, "name": null}]}, "zha/dd": {"color": [{"time": 0.6333, "color": "87ff00ff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.9, "color": "86ff0000"}], "attachment": [{"time": 0.3, "name": "zha/dd"}]}, "zhiliao2/xfr_skill_xuli_td1_46": {"color": [{"time": 0.3333, "color": "ffffffff", "curve": 0.418, "c2": 0.01, "c3": 0.739, "c4": 0.39}, {"time": 0.4, "color": "fffffff0", "curve": 0.396, "c2": 0.29, "c3": 0.426}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{"time": 0.1333, "name": "zhiliao2/xfr_skill_xuli_td1_61"}, {"time": 0.1667, "name": "zhiliao2/xfr_skill_xuli_td1_59"}, {"time": 0.2, "name": "zhiliao2/xfr_skill_xuli_td1_57"}, {"time": 0.2333, "name": "zhiliao2/xfr_skill_xuli_td1_55"}, {"time": 0.2667, "name": "zhiliao2/xfr_skill_xuli_td1_53"}, {"time": 0.3, "name": "zhiliao2/xfr_skill_xuli_td1_51"}, {"time": 0.3333, "name": "zhiliao2/xfr_skill_xuli_td1_49"}, {"time": 0.3667, "name": "zhiliao2/xfr_skill_xuli_td1_47"}, {"time": 0.4, "name": "zhiliao2/xfr_skill_xuli_td1_46"}, {"time": 0.4333, "name": "zhiliao2/xfr_skill_xuli_td1_45"}, {"time": 0.4667, "name": null}, {"time": 0.6333, "name": "zhiliao2/xfr_skill_xuli_td1_61"}, {"time": 0.6667, "name": "zhiliao2/xfr_skill_xuli_td1_59"}, {"time": 0.7, "name": "zhiliao2/xfr_skill_xuli_td1_57"}, {"time": 0.7333, "name": "zhiliao2/xfr_skill_xuli_td1_55"}, {"time": 0.7667, "name": "zhiliao2/xfr_skill_xuli_td1_53"}, {"time": 0.8, "name": "zhiliao2/xfr_skill_xuli_td1_51"}, {"time": 0.8333, "name": "zhiliao2/xfr_skill_xuli_td1_49"}, {"time": 0.8667, "name": "zhiliao2/xfr_skill_xuli_td1_47"}, {"time": 0.9, "name": "zhiliao2/xfr_skill_xuli_td1_46"}, {"time": 0.9333, "name": "zhiliao2/xfr_skill_xuli_td1_45"}, {"time": 0.9667, "name": null}]}, "danjifuhuo6": {"color": [{"time": 0.4, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.8333, "color": "ffffff00"}]}, "danjifuhuo016": {"color": [{"time": 0.4, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.8333, "color": "ffffff00"}]}, "rootcut": {"color": [{"time": 0.4, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.8333, "color": "ffffff00"}]}, "danjifuhuo4": {"color": [{"time": 0.4, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.8333, "color": "ffffff00"}]}, "danjifuhuo012": {"color": [{"time": 0.4, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.8333, "color": "ffffff00"}]}, "danjifuhuo7": {"color": [{"time": 0.4, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.8333, "color": "ffffff00"}]}, "danjifuhuo013": {"color": [{"time": 0.4, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.8333, "color": "ffffff00"}]}, "danjifuhuo018": {"color": [{"time": 0.4, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.8333, "color": "ffffff00"}]}, "danjifuhuo014": {"color": [{"time": 0.4, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.8333, "color": "ffffff00"}]}, "zha/quan2": {"color": [{"time": 0.6333, "color": "87ff00ff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.9, "color": "86ff0000"}], "attachment": [{"time": 0.3, "name": "zha/quan2"}]}, "zhiliao1/light": {"color": [{"time": 0.3333, "color": "67ff0074", "curve": 0.418, "c2": 0.01, "c3": 0.739, "c4": 0.39}, {"time": 0.4, "color": "67ff006d", "curve": 0.396, "c2": 0.29, "c3": 0.426}, {"time": 0.6667, "color": "67ff0000"}]}, "danjifuhuo01": {"color": [{"time": 0.4, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.8333, "color": "ffffff00"}]}, "danjifuhuo010": {"color": [{"time": 0.4, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.8333, "color": "ffffff00"}]}, "danjifuhuo3": {"color": [{"time": 0.4, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.8333, "color": "ffffff00"}]}, "danjifuhuo015": {"color": [{"time": 0.4, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.8333, "color": "ffffff00"}]}, "danjifuhuo5": {"color": [{"time": 0.4, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.8333, "color": "ffffff00"}]}, "zhiliao2/xfr_skill_xuli_td1_45": {"color": [{"time": 0.3333, "color": "ffffffff", "curve": 0.418, "c2": 0.01, "c3": 0.739, "c4": 0.39}, {"time": 0.4, "color": "fffffff0", "curve": 0.396, "c2": 0.29, "c3": 0.426}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{"time": 0.0333, "name": "zhiliao2/xfr_skill_xuli_td1_61"}, {"time": 0.0667, "name": "zhiliao2/xfr_skill_xuli_td1_59"}, {"time": 0.1, "name": "zhiliao2/xfr_skill_xuli_td1_57"}, {"time": 0.1333, "name": "zhiliao2/xfr_skill_xuli_td1_55"}, {"time": 0.1667, "name": "zhiliao2/xfr_skill_xuli_td1_53"}, {"time": 0.2, "name": "zhiliao2/xfr_skill_xuli_td1_51"}, {"time": 0.2333, "name": "zhiliao2/xfr_skill_xuli_td1_49"}, {"time": 0.2667, "name": "zhiliao2/xfr_skill_xuli_td1_47"}, {"time": 0.3, "name": "zhiliao2/xfr_skill_xuli_td1_46"}, {"time": 0.3333, "name": "zhiliao2/xfr_skill_xuli_td1_45"}, {"time": 0.3667, "name": null}, {"time": 0.6333, "name": "zhiliao2/xfr_skill_xuli_td1_61"}, {"time": 0.6667, "name": "zhiliao2/xfr_skill_xuli_td1_59"}, {"time": 0.7, "name": "zhiliao2/xfr_skill_xuli_td1_57"}, {"time": 0.7333, "name": "zhiliao2/xfr_skill_xuli_td1_55"}, {"time": 0.7667, "name": "zhiliao2/xfr_skill_xuli_td1_53"}, {"time": 0.8, "name": "zhiliao2/xfr_skill_xuli_td1_51"}, {"time": 0.8333, "name": "zhiliao2/xfr_skill_xuli_td1_49"}, {"time": 0.8667, "name": "zhiliao2/xfr_skill_xuli_td1_47"}, {"time": 0.9, "name": "zhiliao2/xfr_skill_xuli_td1_46"}, {"time": 0.9333, "name": "zhiliao2/xfr_skill_xuli_td1_45"}, {"time": 0.9667, "name": null}]}, "danjifuhuo019": {"color": [{"time": 0.4, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.8333, "color": "ffffff00"}]}, "maofa": {"color": [{"time": 0.4, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.8333, "color": "ffffff00"}]}, "zhiliao1/yzzl_skill_zd_0": {"color": [{"color": "ffffff4f", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff4f", "curve": 0.418, "c2": 0.01, "c3": 0.739, "c4": 0.39}, {"time": 0.4, "color": "ffffff4b", "curve": 0.396, "c2": 0.29, "c3": 0.426}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{"name": "zhiliao1/yzzl_skill_zd_00001"}, {"time": 0.0667, "name": "zhiliao1/yzzl_skill_zd_00002"}, {"time": 0.1333, "name": "zhiliao1/yzzl_skill_zd_00003"}, {"time": 0.2, "name": "zhiliao1/yzzl_skill_zd_00004"}, {"time": 0.2667, "name": "zhiliao1/yzzl_skill_zd_00005"}, {"time": 0.3333, "name": "zhiliao1/yzzl_skill_zd_00006"}, {"time": 0.4, "name": "zhiliao1/yzzl_skill_zd_00007"}, {"time": 0.4667, "name": "zhiliao1/yzzl_skill_zd_00008"}, {"time": 0.5333, "name": "zhiliao1/yzzl_skill_zd_00009"}, {"time": 0.5667, "name": "zhiliao1/yzzl_skill_zd_00000"}, {"time": 0.6333, "name": "zhiliao1/yzzl_skill_zd_00001"}, {"time": 0.7, "name": "zhiliao1/yzzl_skill_zd_00002"}, {"time": 0.7667, "name": "zhiliao1/yzzl_skill_zd_00003"}, {"time": 0.8333, "name": "zhiliao1/yzzl_skill_zd_00004"}, {"time": 0.9, "name": "zhiliao1/yzzl_skill_zd_00005"}, {"time": 0.9667, "name": "zhiliao1/yzzl_skill_zd_00006"}, {"time": 1.0333, "name": "zhiliao1/yzzl_skill_zd_00007"}, {"time": 1.1, "name": "zhiliao1/yzzl_skill_zd_00008"}]}, "tx_qua2": {"color": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.1, "color": "ffffffff", "curve": 0.246, "c3": 0.635, "c4": 0.55}, {"time": 0.3333, "color": "ffffff63", "curve": 0.344, "c2": 0.37, "c3": 0.682, "c4": 0.72}, {"time": 0.4, "color": "ffffff2e", "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5, "color": "ffffff00"}]}, "danjifuhuo09": {"color": [{"time": 0.4, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.8333, "color": "ffffff00"}]}}, "bones": {"bone": {"translate": [{"x": 1.02, "y": 15.46, "curve": 0.381, "c2": 0.54, "c3": 0.743}, {"time": 0.5667, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 1.2667, "x": 1.55, "y": 23.53}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 3.63}]}, "bone3": {"rotate": [{"angle": 1.17, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.5}]}, "bone4": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": 0.86, "y": 4.01}]}, "bone8": {"rotate": [{"angle": -4.38, "curve": 0.38, "c2": 0.6, "c3": 0.726}, {"time": 0.3333}]}, "bone9": {"rotate": [{"angle": -12.94, "curve": 0.336, "c2": 0.34, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": -5.44, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.7}]}, "bone10": {"rotate": [{"angle": -20.86, "curve": 0.299, "c2": 0.21, "c3": 0.643, "c4": 0.58}, {"time": 0.3333, "angle": -14.19, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.1}]}, "bone11": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": 0.35, "y": -2.37}]}, "bone12": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 5.26}]}, "bone15": {"rotate": [{"angle": 12.46, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 1.1333, "angle": 9.14}]}, "bone13": {"rotate": [{"angle": -1.48}]}, "bone14": {"rotate": [{"angle": -4.99, "curve": 0.382, "c2": 0.57, "c3": 0.736}, {"time": 0.4333, "angle": -7.07, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 1.0333, "angle": -4.13}]}, "bone16": {"rotate": [{"angle": 18.09, "curve": 0.335, "c2": 0.34, "c3": 0.758}, {"time": 0.9667}]}, "bone17": {"rotate": [{"angle": 21.88, "curve": 0.274, "c2": 0.11, "c3": 0.753}, {"time": 1.2}]}, "bone19": {"rotate": [{"angle": 2.88, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3667}]}, "bone20": {"rotate": [{"angle": 2.88, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3667}]}, "bone21": {"rotate": [{"angle": 7.48, "curve": 0.333, "c2": 0.33, "c3": 0.68, "c4": 0.71}, {"time": 0.3667, "angle": 2.88, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.7667}]}, "bone22": {"rotate": [{"angle": -14.76, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.9}]}, "bone23": {"rotate": [{"angle": -19.48, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.9, "angle": -4.72, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.2667}]}, "bone24": {"rotate": [{"angle": 0.55, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 0.7333, "angle": 2.81}]}, "bone25": {"rotate": [{"angle": -0.77, "curve": 0.292, "c2": 0.2, "c3": 0.673, "c4": 0.69}, {"time": 0.7333, "angle": 1.92, "curve": 0.381, "c2": 0.59, "c3": 0.73}, {"time": 1.1, "angle": 2.81}]}, "bone26": {"rotate": [{"angle": 2.81}]}, "bone27": {"rotate": [{"angle": 1.92, "curve": 0.381, "c2": 0.59, "c3": 0.73}, {"time": 0.3667, "angle": 2.81}]}, "bone28": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -4.71}]}, "bone29": {"rotate": [{"angle": -1.52, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.5}]}, "bone30": {"rotate": [{"angle": -3.73, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 1}]}, "bone31": {"rotate": [{"angle": -1.52, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.5}]}, "bone32": {"rotate": [{"angle": -3.73, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 1}]}, "bone33": {"rotate": [{"angle": -4.51, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1333, "angle": -4.71}]}, "bone34": {"rotate": [{"angle": -3.73, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 1}]}, "bone35": {"rotate": [{"angle": -4.51, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1333, "angle": -4.71}]}, "bone36": {"rotate": [{"angle": -2.36, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6333, "angle": -4.71}]}, "bone38": {"rotate": [{"angle": 4.93, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}]}, "bone39": {"rotate": [{"angle": 9.86}]}, "bone40": {"rotate": [{"angle": 4.93, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}]}, "bone41": {"rotate": [{"angle": 9.86}]}, "bone42": {"rotate": [{"angle": 4.93, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": 9.86}]}, "bone43": {"rotate": [{"angle": 9.86}]}, "bone44": {"rotate": [{"angle": 4.93, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": 9.86}]}, "bone52": {"translate": [{"x": -21.69, "y": -17.63, "curve": 0.353, "c2": 0.4, "c3": 0.757}, {"time": 0.8333, "x": -30, "y": -10}]}, "bone54": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 14.25}]}, "bone55": {"rotate": [{"angle": 3.94, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4667}]}, "bone56": {"rotate": [{"angle": 9.99, "curve": 0.354, "c2": 0.41, "c3": 0.757}, {"time": 0.9}]}, "bone57": {"rotate": [{"angle": -3.04, "curve": 0.38, "c2": 0.6, "c3": 0.725}, {"time": 0.3667}]}, "bone58": {"rotate": [{"angle": -9.74, "curve": 0.334, "c2": 0.33, "c3": 0.677, "c4": 0.7}, {"time": 0.3667, "angle": -4.44, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.8}]}, "bone59": {"rotate": [{"angle": -15.53, "curve": 0.292, "c2": 0.17, "c3": 0.636, "c4": 0.54}, {"time": 0.3667, "angle": -11.26, "curve": 0.354, "c2": 0.41, "c3": 0.757}, {"time": 1.2667}]}, "bone60": {"rotate": [{"angle": 1.69, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2667}]}, "bone61": {"rotate": [{"angle": 6.46, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 0.2667, "angle": 3.14, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.6333}]}, "bone62": {"rotate": [{"angle": 8.19, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.2667, "angle": 4.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.7667}]}, "bone63": {"rotate": [{"angle": 3.4, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 0.4}]}, "bone64": {"rotate": [{"angle": 8.36, "curve": 0.332, "c2": 0.33, "c3": 0.68, "c4": 0.71}, {"time": 0.4, "angle": 3.14, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.7667}]}, "bone65": {"rotate": [{"angle": 10.03, "curve": 0.318, "c2": 0.29, "c3": 0.666, "c4": 0.66}, {"time": 0.4, "angle": 4.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.9}]}, "bone69": {"rotate": [{"angle": 5.12, "curve": 0.381, "c2": 0.54, "c3": 0.744}, {"time": 0.5333}]}, "bone70": {"rotate": [{"angle": 10.12, "curve": 0.318, "c2": 0.29, "c3": 0.678, "c4": 0.7}, {"time": 0.5333, "angle": 3.14, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.9}]}, "bone71": {"rotate": [{"angle": 11.55, "curve": 0.298, "c2": 0.22, "c3": 0.659, "c4": 0.64}, {"time": 0.5333, "angle": 4.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.0333}]}, "bone72": {"rotate": [{"angle": 6.49, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6333}]}, "bone73": {"rotate": [{"angle": 11.28, "curve": 0.303, "c2": 0.24, "c3": 0.674, "c4": 0.69}, {"time": 0.6333, "angle": 3.14, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1}]}, "bone74": {"rotate": [{"angle": 12.45, "curve": 0.278, "c2": 0.15, "c3": 0.651, "c4": 0.61}, {"time": 0.6333, "angle": 4.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.1333}]}, "bone75": {"rotate": [{"angle": 12.52, "curve": 0.358, "c2": 0.65, "c3": 0.693}, {"time": 0.1333, "angle": 12.98, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6333, "angle": 8.2}]}, "bone46": {"rotate": [{"angle": -14.92, "curve": 0.316, "c2": 0.27, "c3": 0.757}, {"time": 1.0333, "angle": 22.29}]}, "bone47": {"rotate": [{"angle": -14.11, "curve": 0.375, "c2": 0.61, "c3": 0.717}, {"time": 0.3, "angle": -19.66}]}, "bone48": {"rotate": [{"angle": 4.97, "curve": 0.368, "c2": 0.47, "c3": 0.753}, {"time": 0.8, "angle": -19.66}]}, "bone49": {"rotate": [{"angle": 14.93, "curve": 0.282, "c2": 0.14, "c3": 0.754}, {"time": 1.1667, "angle": -11.96}]}, "bone50": {"rotate": [{"angle": 9.31, "curve": 0.381, "c2": 0.59, "c3": 0.73}, {"time": 0.4, "angle": 15.68}]}, "bone51": {"rotate": [{"angle": -3.86, "curve": 0.353, "c2": 0.41, "c3": 0.757}, {"time": 0.9333, "angle": 15.68}]}, "bone6": {"rotate": [{"angle": 0.77, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 0.4}]}, "bone7": {"rotate": [{"angle": 1.82, "curve": 0.364, "c2": 0.45, "c3": 0.754}, {"time": 0.7667}]}, "bone66": {"rotate": [{"angle": 5.94, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5}]}, "bone67": {"rotate": [{"angle": 14.42, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": 5.94, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1}]}, "bone68": {"rotate": [{"angle": 17.24, "curve": 0.286, "c2": 0.64, "c3": 0.615}, {"time": 0.1667, "angle": 17.68, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5, "angle": 14.42}]}, "bone80": {"rotate": [{"angle": -1.86, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 0.4333, "angle": -4.89}]}, "bone81": {"rotate": [{"angle": 1.71, "curve": 0.338, "c2": 0.35, "c3": 0.688, "c4": 0.73}, {"time": 0.4333, "angle": -2.84, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.7667, "angle": -4.89}]}, "bone82": {"rotate": [{"angle": 5.04, "curve": 0.3, "c2": 0.22, "c3": 0.65, "c4": 0.61}, {"time": 0.4333, "angle": 0.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.1, "angle": -4.89}]}, "bone83": {"rotate": [{"angle": 6.02, "curve": 0.304, "c2": 0.65, "c3": 0.636}, {"time": 0.1, "angle": 6.18, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.4333, "angle": 4.14}]}, "bone76": {"rotate": [{"angle": 1.04, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 0.4333, "angle": 3.43}]}, "bone77": {"rotate": [{"angle": -3.17, "curve": 0.32, "c2": 0.29, "c3": 0.669, "c4": 0.67}, {"time": 0.4333, "angle": 0.5, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.9333, "angle": 3.43}]}, "bone78": {"rotate": [{"angle": -5.07, "curve": 0.354, "c2": 0.65, "c3": 0.689}, {"time": 0.1, "angle": -5.29, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.4333, "angle": -3.68}]}, "bone79": {"rotate": [{"angle": -1.55, "curve": 0.358, "c2": 0.42, "c3": 0.708, "c4": 0.81}, {"time": 0.4333, "angle": -4.74, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.6, "angle": -5.29}]}, "zhiliao3": {"translate": [{"x": -42.87, "y": 100.54}], "scale": [{"x": 2.564, "y": 2.564}]}, "zhiliao10": {"scale": [{"x": 0.029, "y": 0.029, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "x": -0.031, "y": -0.031}]}, "zhiliao9": {"scale": [{"x": 1.078, "y": 1.078, "curve": 0.387, "c2": 0.3, "c3": 0.697, "c4": 0.65}, {"time": 0.0667, "x": 1.27, "y": 1.27, "curve": 0.236, "c2": 0.5, "c3": 0.458}, {"time": 0.2333}]}, "zhiliao1": {"translate": [{"x": 3.19, "y": 158.91, "curve": 0.755, "c2": -0.7, "c3": 0.737}, {"time": 0.3333, "x": 3.19, "y": -146.45}]}, "zhiliao2": {"translate": [{"x": 2.66, "y": 134.13}]}, "zhiliao11": {"scale": [{"x": 0.621, "y": 0.621, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.5, "x": -0.031, "y": -0.031}]}, "zhiliao": {"translate": [{"x": -1172.82, "y": -28.95}]}, "zhiliao7": {"translate": [{"x": 37.18, "y": 179.08}], "scale": [{"x": 2.564, "y": 2.564}]}, "zhiliao5": {"translate": [{"x": 69.5, "y": 150.23}], "scale": [{"x": 2.564, "y": 2.564}]}, "zhakai": {"rotate": [{"curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.3, "angle": -14.31}], "translate": [{"curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.3, "y": 1211.38}], "scale": [{"time": 0.3, "curve": 0, "c2": 1, "c3": 0.75}, {"time": 0.9, "x": 6.073, "y": 6.073}]}}}}}