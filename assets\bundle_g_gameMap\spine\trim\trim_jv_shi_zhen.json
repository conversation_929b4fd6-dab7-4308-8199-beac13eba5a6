{"skeleton": {"hash": "DuAZync0FT7Gq8iePvlO6TK04Ww=", "spine": "3.8.75", "x": -209.71, "y": -244.19, "width": 472, "height": 513, "images": "./images/", "audio": "E:/1/文件/PSD/动画拆分/大地图建筑/妖族-三柱"}, "bones": [{"name": "root"}, {"name": "1", "parent": "root", "x": -108.12, "y": -88.23}, {"name": "bone1", "parent": "1", "x": -37.59, "y": -29.73}, {"name": "bone2", "parent": "1", "x": -41.91, "y": 70.61}, {"name": "bone3", "parent": "1", "x": 41.99, "y": -75.58}, {"name": "bone4", "parent": "1", "x": 36.8, "y": -3.78}, {"name": "bone5", "parent": "1", "x": 79.19, "y": -121.42}, {"name": "2", "parent": "root", "x": -28.54, "y": 138.4}, {"name": "bone6", "parent": "2", "x": -8.18, "y": -59.14}, {"name": "bone7", "parent": "2", "x": 0.47, "y": -24.54}, {"name": "bone8", "parent": "2", "x": 0.47, "y": 23.9}, {"name": "bone9", "parent": "2", "x": 3.07, "y": 82.72}, {"name": "3", "parent": "root", "x": 192.03, "y": 121.96}, {"name": "bone10", "parent": "3", "x": -11.64, "y": -76.44}, {"name": "bone11", "parent": "3", "x": -9.04, "y": -28}, {"name": "bone12", "parent": "3", "x": 8.26, "y": 22.17}, {"name": "bone13", "parent": "3", "x": 5.66, "y": 68.88}, {"name": "bone", "parent": "root", "x": 26.29, "y": 12.31}, {"name": "bone14", "parent": "root", "x": 26.29, "y": 12.31}, {"name": "bone15", "parent": "bone", "x": -143.78, "y": -122.99}, {"name": "bone16", "parent": "bone", "x": -52.67, "y": 89.18}, {"name": "bone17", "parent": "bone", "x": 174.95, "y": 45.03}], "slots": [{"name": "1_1", "bone": "bone1", "attachment": "1_1"}, {"name": "1_2", "bone": "bone2", "attachment": "1_2"}, {"name": "1_3", "bone": "bone3", "attachment": "1_3"}, {"name": "1_4", "bone": "bone4", "attachment": "1_4"}, {"name": "1_5", "bone": "bone5", "attachment": "1_5"}, {"name": "2_1", "bone": "bone6", "attachment": "2_1"}, {"name": "2_2", "bone": "bone7", "attachment": "2_2"}, {"name": "2_3", "bone": "bone8", "attachment": "2_3"}, {"name": "2_4", "bone": "bone9", "attachment": "2_4"}, {"name": "3_1", "bone": "bone10", "attachment": "3_1"}, {"name": "3_2", "bone": "bone11", "attachment": "3_2"}, {"name": "3_3", "bone": "bone12", "attachment": "3_3"}, {"name": "3_4", "bone": "bone13", "attachment": "3_4"}, {"name": "zz", "bone": "bone", "attachment": "zz", "blend": "additive"}, {"name": "zz_guang", "bone": "bone14", "attachment": "zz_gung", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"zz": {"zz": {"type": "mesh", "color": "ffffff7d", "uvs": [0.33897, 0, 0.44542, 0.00422, 0.48397, 0.02694, 0.48615, 0.16332, 0.45043, 0.24625, 0.46656, 0.26035, 0.5142, 0.32704, 0.52262, 0.34311, 0.74294, 0.39152, 0.75355, 0.37772, 0.79461, 0.29261, 0.82211, 0.26475, 0.76704, 0.20652, 0.8056, 0.17314, 0.8125, 0.16093, 0.80033, 0.07749, 0.86728, 0.03982, 0.94313, 0.0599, 0.97649, 0.07399, 0.99901, 0.19856, 0.9433, 0.29829, 0.93972, 0.33651, 0.98992, 0.36607, 0.98593, 0.42515, 0.90433, 0.49024, 0.81229, 0.50662, 0.73593, 0.44847, 0.73404, 0.41805, 0.74033, 0.39115, 0.52094, 0.34289, 0.49069, 0.34165, 0.41797, 0.4363, 0.33173, 0.41115, 0.243, 0.36209, 0.22611, 0.37675, 0.16019, 0.45401, 0.18158, 0.47631, 0.24716, 0.68789, 0.25851, 0.67656, 0.31118, 0.62128, 0.36615, 0.6847, 0.37401, 0.84065, 0.44381, 0.90426, 0.44373, 0.96077, 0.39345, 1, 0.38794, 1, 0.32933, 0.9865, 0.30761, 0.91746, 0.26698, 0.91779, 0.11803, 0.85571, 0.06394, 0.80716, 0, 0.71038, 0, 0.69086, 0.01419, 0.66357, 0.01755, 0.60682, 0.01776, 0.49403, 0.06137, 0.4484, 0.12704, 0.44754, 0.16133, 0.45249, 0.21767, 0.38648, 0.22711, 0.36802, 0.25513, 0.27935, 0.3059, 0.26019, 0.3099, 0.23526, 0.27401, 0.15901, 0.28351, 0.0309, 0.32242, 0], "triangles": [24, 25, 9, 9, 25, 26, 9, 27, 8, 24, 9, 21, 21, 9, 10, 24, 21, 23, 21, 10, 11, 9, 26, 27, 23, 21, 22, 27, 28, 8, 8, 28, 7, 21, 11, 20, 20, 11, 19, 18, 19, 17, 17, 19, 14, 12, 13, 11, 13, 14, 11, 17, 14, 16, 11, 14, 19, 14, 15, 16, 29, 7, 28, 31, 32, 30, 32, 33, 62, 33, 61, 62, 30, 32, 62, 5, 62, 4, 62, 5, 30, 4, 62, 63, 34, 59, 60, 34, 60, 33, 33, 60, 61, 30, 6, 29, 7, 29, 6, 30, 5, 6, 4, 63, 3, 0, 3, 63, 66, 0, 65, 3, 1, 2, 0, 1, 3, 0, 63, 64, 65, 0, 64, 35, 59, 34, 44, 45, 43, 43, 45, 47, 45, 46, 47, 43, 47, 42, 41, 47, 48, 48, 49, 37, 41, 48, 37, 47, 41, 42, 40, 37, 38, 37, 40, 41, 49, 50, 37, 51, 53, 50, 50, 53, 37, 37, 53, 54, 36, 54, 55, 54, 36, 37, 51, 52, 53, 38, 39, 40, 36, 57, 35, 57, 36, 55, 55, 56, 57, 35, 58, 59, 35, 57, 58], "vertices": [1, 20, -19.95, 156.33, 1, 1, 20, 28.05, 154.26, 1, 3, 19, 136.55, 433.21, 0, 20, 45.44, 143.1, 0.99999, 21, -182.18, 209.68, 1e-05, 3, 19, 137.54, 366.23, 0.00021, 20, 46.43, 76.12, 0.99968, 21, -181.2, 142.7, 0.00011, 3, 19, 121.43, 325.49, 0.00048, 20, 30.32, 35.38, 0.99925, 21, -197.31, 101.96, 0.00027, 3, 19, 128.7, 318.52, 0.00081, 20, 37.59, 28.41, 0.99852, 21, -190.03, 94.99, 0.00067, 3, 19, 150.18, 285.69, 0.00093, 20, 59.07, -4.42, 0.99459, 21, -168.55, 62.16, 0.00448, 1, 20, 62.87, -12.14, 1, 2, 19, 253.35, 231.73, 0.00049, 21, -65.38, 8.2, 0.99951, 3, 19, 258.13, 238.54, 0, 20, 167.02, -51.57, 0.00035, 21, -60.6, 15.01, 0.99965, 3, 19, 276.65, 280.33, 0, 20, 185.54, -9.78, 0.00023, 21, -42.08, 56.8, 0.99977, 3, 19, 289.05, 294.01, 0, 20, 197.94, 3.9, 4e-05, 21, -29.68, 70.48, 0.99996, 3, 19, 264.22, 322.59, 0, 20, 173.11, 32.48, 1e-05, 21, -54.51, 99.06, 0.99999, 1, 21, -37.12, 115.45, 1, 1, 21, -34.01, 121.45, 1, 1, 21, -39.5, 162.42, 1, 1, 21, -9.31, 180.91, 1, 1, 21, 24.9, 171.05, 1, 1, 21, 39.95, 164.14, 1, 2, 19, 368.83, 326.5, 0, 21, 50.1, 102.97, 1, 3, 19, 343.71, 277.54, 0, 20, 252.6, -12.57, 0, 21, 24.98, 54.01, 1, 3, 19, 342.1, 258.77, 0, 20, 250.99, -31.34, 0, 21, 23.36, 35.24, 1, 3, 19, 364.73, 244.26, 0, 20, 273.62, -45.85, 1e-05, 21, 46, 20.73, 0.99999, 3, 19, 362.93, 215.25, 0, 20, 271.83, -74.86, 5e-05, 21, 44.2, -8.28, 0.99995, 3, 19, 326.14, 183.29, 0, 20, 235.03, -106.82, 0.00016, 21, 7.4, -40.24, 0.99984, 3, 19, 284.62, 175.26, 0, 20, 193.51, -114.85, 0.00036, 21, -34.11, -48.27, 0.99964, 3, 19, 250.18, 203.81, 0, 20, 159.07, -86.3, 0.00045, 21, -68.55, -19.72, 0.99955, 3, 19, 249.33, 218.81, 0, 20, 158.22, -71.3, 0.00343, 21, -69.4, -4.72, 0.99657, 2, 19, 252.17, 231.91, 0.0005, 21, -66.56, 8.38, 0.9995, 1, 20, 62.12, -12.04, 1, 3, 19, 139.58, 278.53, 0.00137, 20, 48.47, -11.58, 0.99637, 21, -179.15, 55, 0.00226, 3, 19, 106.79, 232.03, 0.00216, 20, 15.68, -58.08, 0.99715, 21, -211.94, 8.5, 0.00069, 3, 19, 67.89, 239.57, 0.064, 20, -23.22, -50.54, 0.93577, 21, -250.84, 16.04, 0.00023, 2, 20, -63.23, -21.46, 0.99985, 21, -290.86, 45.12, 0.00015, 2, 20, -70.85, -28.66, 0.9999, 21, -298.47, 37.92, 0.0001, 1, 19, -9.47, 145.57, 1, 1, 19, 0.18, 134.62, 1, 3, 19, 29.75, 30.99, 0.99666, 20, -61.36, -259.12, 0.00334, 21, -288.98, -192.54, 0, 2, 19, 34.87, 36.31, 0.99987, 20, -56.24, -253.8, 0.00013, 2, 19, 58.62, 63.44, 0.99996, 20, -32.49, -226.67, 4e-05, 1, 19, 83.41, 32.3, 1, 1, 19, 86.96, -44.27, 1, 1, 19, 118.44, -75.5, 1, 1, 19, 118.4, -103.25, 1, 1, 19, 95.73, -122.51, 1, 1, 19, 93.24, -122.51, 1, 1, 19, 66.81, -115.88, 1, 1, 19, 57.01, -81.98, 1, 1, 19, 38.69, -82.14, 1, 2, 19, -28.49, -51.66, 0.99999, 20, -119.6, -341.77, 1e-05, 2, 19, -52.88, -27.81, 0.99979, 20, -143.99, -317.92, 0.00021, 2, 19, -81.72, 19.72, 0.99957, 20, -172.83, -270.38, 0.00043, 2, 19, -81.72, 29.34, 0.99923, 20, -172.83, -260.77, 0.00077, 2, 19, -75.32, 42.78, 0.99869, 20, -166.43, -247.33, 0.00131, 2, 19, -73.8, 70.74, 0.99737, 20, -164.91, -219.36, 0.00263, 2, 19, -73.71, 126.25, 0.99581, 20, -164.82, -163.86, 0.00419, 2, 19, -54.04, 148.78, 0.99418, 20, -145.15, -141.33, 0.00582, 3, 19, -24.43, 149.9, 0.98513, 20, -115.54, -140.21, 0.01487, 21, -343.16, -73.63, 0, 1, 19, -8.96, 146.32, 1, 2, 20, -74.66, -33.44, 0.9999, 21, -302.28, 33.14, 0.0001, 2, 20, -70.4, -24.38, 0.9999, 21, -298.03, 42.2, 0.0001, 2, 20, -57.77, 19.16, 0.9999, 21, -285.39, 85.74, 0.0001, 3, 19, 56.24, 318.45, 0.00291, 20, -34.87, 28.34, 0.997, 21, -262.49, 94.92, 9e-05, 3, 19, 58.04, 330.82, 0.0013, 20, -33.07, 40.71, 0.99865, 21, -260.69, 107.29, 6e-05, 3, 19, 41.86, 368.33, 0.00046, 20, -49.25, 78.22, 0.99952, 21, -276.87, 144.8, 2e-05, 3, 19, 46.14, 431.26, 3e-05, 20, -44.97, 141.15, 0.99997, 21, -272.59, 207.73, 0, 1, 20, -27.42, 156.33, 1], "hull": 67, "edges": [0, 132, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132], "width": 225, "height": 245}}, "2_2": {"2_2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [34.55, -14.91, -33.45, -14.91, -33.45, 60.09, 34.55, 60.09], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 68, "height": 75}}, "3_3": {"3_3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [52.19, -26.18, -48.81, -26.18, -48.81, 34.82, 52.19, 34.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 101, "height": 61}}, "2_1": {"2_1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [60.2, -32.31, -58.8, -32.31, -58.8, 66.69, 60.2, 66.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 119, "height": 99}}, "3_2": {"3_2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [44.49, -38.01, -35.51, -38.01, -35.51, 38.99, 44.49, 38.99], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 80, "height": 77}}, "2_4": {"2_4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [45.95, -48.17, -48.05, -48.17, -48.05, 37.83, 45.95, 37.83], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 94, "height": 86}}, "2_3": {"2_3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [48.55, -20.35, -49.45, -20.35, -49.45, 52.65, 48.55, 52.65], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 98, "height": 73}}, "1_1": {"1_1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [66.19, -57.09, -54.81, -57.09, -54.81, 76.91, 66.19, 76.91], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 121, "height": 134}}, "1_2": {"1_2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [52.51, -80.43, -43.49, -80.43, -43.49, 56.57, 52.51, 56.57], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 96, "height": 137}}, "1_3": {"1_3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [38.61, -43.24, -41.39, -43.24, -41.39, 63.76, 38.61, 63.76], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 80, "height": 107}}, "1_4": {"1_4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [38.8, -37.04, -24.2, -37.04, -24.2, 43.96, 38.8, 43.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 63, "height": 81}}, "1_5": {"1_5": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [31.41, -24.4, -27.59, -24.4, -27.59, 46.6, 31.41, 46.6], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 59, "height": 71}}, "3_1": {"3_1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [67.08, -37.17, -49.92, -37.17, -49.92, 52.83, 67.08, 52.83], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 90}}, "zz_guang": {"zz_gung": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [236, -256.5, -236, -256.5, -236, 256.5, 236, 256.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 236, "height": 256}}, "3_4": {"3_4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [45.78, -32.89, -42.22, -32.89, -42.22, 47.11, 45.78, 47.11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 88, "height": 80}}}}], "animations": {"san_shi_zhu": {"slots": {"zz_guang": {"attachment": [{"name": null}]}, "zz": {"color": [{"color": "ffffff00"}], "attachment": [{"name": null}]}}, "bones": {"bone16": {"scale": [{"x": 1.318, "y": 1.185}]}, "bone17": {"scale": [{"x": 1.318, "y": 1.185}]}}}, "san_shi_zhu_jiesuo": {"slots": {"zz_guang": {"color": [{"time": 1.0667, "color": "ffffff00"}, {"time": 1.2333, "color": "ffffffff"}, {"time": 1.4, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 1.0667, "name": "zz_gung"}]}, "zz": {"color": [{"time": 1.1333, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff35"}, {"time": 1.5333, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 1.1333, "name": "zz"}]}}, "bones": {"bone1": {"translate": [{"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "y": 44, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": 1.34, "y": 1.14, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": 0.96, "y": 0.94, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": 1.07, "y": 1.03, "curve": 0.25, "c3": 0.75}, {"time": 1.0333}]}, "bone2": {"translate": [{"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "y": 30, "curve": 0.25, "c3": 0.75}, {"time": 0.9667}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7667, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 1.3, "y": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 0.96, "y": 0.94, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "x": 1.07, "y": 1.03, "curve": 0.25, "c3": 0.75}, {"time": 1.2}]}, "bone3": {"translate": [{"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "y": 44, "curve": 0.25, "c3": 0.75}, {"time": 0.9667}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7667, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 1.34, "y": 1.14, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 0.96, "y": 0.94, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "x": 1.07, "y": 1.03, "curve": 0.25, "c3": 0.75}, {"time": 1.2}]}, "bone4": {"translate": [{"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "y": 30, "curve": 0.25, "c3": 0.75}, {"time": 1.0667}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8667, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "x": 1.3, "y": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "x": 0.96, "y": 0.94, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": 1.07, "y": 1.03, "curve": 0.25, "c3": 0.75}, {"time": 1.3}]}, "bone5": {"translate": [{"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "y": 44, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7333, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": 1.34, "y": 1.14, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "x": 0.96, "y": 0.94, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": 1.07, "y": 1.03, "curve": 0.25, "c3": 0.75}, {"time": 1.1667}]}, "bone6": {"translate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": 44, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2333, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 1.34, "y": 1.14, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 0.96, "y": 0.94, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 1.07, "y": 1.03, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone10": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "y": 44, "curve": 0.25, "c3": 0.75}, {"time": 0.2}], "scale": [{"x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 1.34, "y": 1.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 0.96, "y": 0.94, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 1.07, "y": 1.03, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "bone7": {"translate": [{"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "y": 30, "curve": 0.25, "c3": 0.75}, {"time": 0.6}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 1.3, "y": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 0.96, "y": 0.94, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": 1.07, "y": 1.03, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}]}, "bone8": {"translate": [{"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "y": 30, "curve": 0.25, "c3": 0.75}, {"time": 0.7}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4667, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 1.3, "y": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": 0.96, "y": 0.94, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1.07, "y": 1.03, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "bone9": {"translate": [{"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "y": 30, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": 1.3, "y": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": 0.96, "y": 0.94, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "x": 1.07, "y": 1.03, "curve": 0.25, "c3": 0.75}, {"time": 1.0333}]}, "bone11": {"translate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "y": 30, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1333, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 1.3, "y": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 0.96, "y": 0.94, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 1.07, "y": 1.03, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone12": {"translate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": 30, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2333, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 1.3, "y": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.96, "y": 0.94, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 1.07, "y": 1.03, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone13": {"translate": [{"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "y": 30, "curve": 0.25, "c3": 0.75}, {"time": 0.6}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3667, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 1.3, "y": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 0.96, "y": 0.94, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": 1.07, "y": 1.03, "curve": 0.25, "c3": 0.75}, {"time": 0.8}]}, "bone17": {"scale": [{"time": 0.7}, {"time": 0.9, "x": 1.318, "y": 1.185}]}, "bone16": {"scale": [{"time": 1.1333}, {"time": 1.3333, "x": 1.318, "y": 1.185}]}}}}}