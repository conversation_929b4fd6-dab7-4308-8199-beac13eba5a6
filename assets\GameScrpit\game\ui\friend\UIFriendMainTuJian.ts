import { _decorator, Label, Node, Component } from "cc";
import { FriendTuJianAdapter } from "./adapter/FriendTuJianAdapter";
import { JsonMgr } from "../../mgr/JsonMgr";
import { ItemCtrl } from "../../common/ItemCtrl";
import { FriendModule } from "../../../module/friend/FriendModule";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { IConfigFriendPicture } from "../../JsonDefine";
import { FriendAudioName } from "../../../module/friend/FriendConfig";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { AdapterView } from "../../../../platform/src/core/ui/adapter_view/AdapterView";
const { ccclass, property } = _decorator;
@ccclass("UIFriendMainTuJian")
export class UIFriendMainTuJian extends Component {
  @property(Node)
  private tujianListView: Node;
  @property(Node)
  private tujianViewHolder: Node;
  @property(Node)
  private nodeAwardDetail: Node;
  @property(Label)
  private lblTipTitle: Label;
  @property(Node)
  private nodeAwards: Node;
  @property(Node)
  private btnGet: Node;
  @property(Node)
  private btnGetDisable: Node;
  @property(Node)
  private bgYiLingQu: Node;

  @property(Label)
  private lblFriendNum: Label;
  @property(Label)
  private lblActivateTujianNum: Label;

  private _adapter: FriendTuJianAdapter;
  private _curData: IConfigFriendPicture;
  protected start(): void {
    this._adapter = new FriendTuJianAdapter(this.tujianViewHolder, this);
    this.tujianListView.getComponent(AdapterView).setAdapter(this._adapter);
    let datas: IConfigFriendPicture[] = Object.values(JsonMgr.instance.jsonList.c_friendPicture);
    this._adapter.setData(datas);
    this.refreshData();
    MsgMgr.on(MsgEnum.ON_FRIEND_UPDATE, this.refreshData, this);
  }
  private refreshData() {
    let datas: IConfigFriendPicture[] = Object.values(JsonMgr.instance.jsonList.c_friendPicture);
    let activateNum = 0;
    for (let i = 0; i < datas.length; i++) {
      let data = datas[i];
      let collectNum = 0;
      for (let index = 0; index < data.friendId.length; index++) {
        let friendMessage = FriendModule.data.getFriendMessage(data.friendId[index]);
        if (friendMessage) {
          collectNum++;
        }
      }
      if (collectNum == data.friendId.length) {
        activateNum++;
      }
    }
    this.lblFriendNum.string = `拥有仙友：${FriendModule.data.ownedFriendNum}/${FriendModule.data.totalFriendNum}`;
    this.lblActivateTujianNum.string = `激活图鉴：${activateNum}/${datas.length}`;
  }
  protected onDestroy(): void {
    MsgMgr.off(MsgEnum.ON_FRIEND_UPDATE, this.refreshData, this);
  }
  protected update(dt: number): void {}

  public showDetail(data: IConfigFriendPicture) {
    //
    this._curData = data;
    this.nodeAwardDetail.active = true;
    this.lblTipTitle.string = data.name;
    let index = 0;
    for (; index < data.rewardList.length && index < this.nodeAwards.children.length; index++) {
      let item = this.nodeAwards.children[index];
      let itemCtrl = item.getComponent(ItemCtrl);
      itemCtrl.setItemId(data.rewardList[index][0], data.rewardList[index][1]);
    }
    for (; index < this.nodeAwards.children.length; index++) {
      this.nodeAwards.children[index].active = false;
    }
    let collectNum = 0;
    for (let index = 0; index < data.friendId.length; index++) {
      let friendMessage = FriendModule.data.getFriendMessage(data.friendId[index]);
      if (friendMessage) {
        collectNum++;
      }
    }
    this.btnGet.active = false;
    this.btnGetDisable.active = false;
    this.bgYiLingQu.active = false;
    if (collectNum < data.friendId.length) {
      this.btnGetDisable.active = true;
    } else if (FriendModule.data.pictureMessage[data.id]) {
      this.bgYiLingQu.active = true;
    } else {
      this.btnGet.active = true;
    }
    // TipsMgr.showTipNode(this.nodeAwardDetail, -1);
  }
  private onClickBlank() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this.nodeAwardDetail.active = false;
  }
  private onClickBtnGet() {
    AudioMgr.instance.playEffect(FriendAudioName.Effect.点击宝箱领取按钮);
    if (this._curData) {
      FriendModule.api.takePictureReward(this._curData.id, (data) => {
        MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.values });
        this.showDetail(this._curData);
        this._adapter.notifyDataSetChanged("DATAONLY");
      });
    }
  }
}
