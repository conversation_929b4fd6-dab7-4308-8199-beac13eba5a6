// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import { Vec2 } from "cc";
import GameObject from "./GameObject";
import NotifySection from "./NotifySection";
import { Section } from "./Section";

export enum DIRECT {
  RIGHT = 1,
  LEFT = 2,
}

export default class DirectSection extends Section {
  public direct: DIRECT = DIRECT.LEFT;
  public static sectionName(): string {
    return "DirectSection";
  }
  public onInit(go: GameObject, args) {
    super.onInit(go, args);
    if (args == 1) {
      this.setDirect(DIRECT.RIGHT);
    } else {
      this.setDirect(DIRECT.LEFT);
    }
    this.ready();
  }
  public onStart() {
    //this.setDirect(DIRECT.LEFT);
  }
  public setDirect(direct: DIRECT) {
    this.direct = direct;
    this.emitMsg("OnDirectChange", this.direct);
  }
  public getDirect(): DIRECT {
    return this.direct;
  }
  public getForward(): Vec2 {
    if (this.direct == DIRECT.LEFT) {
      return new Vec2(-1, 0);
    } else if (this.direct == DIRECT.RIGHT) {
      return new Vec2(1, 0);
    }
  }
}
