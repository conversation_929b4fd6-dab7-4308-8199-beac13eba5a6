import MsgEnum from "../event/MsgEnum";
import MsgMgr from "../../lib/event/MsgMgr";
import { Node } from "cc";
import { GameData } from "../GameData";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.ERROR);
let autoIncreaseId = 0;
function getId() {
  return autoIncreaseId++;
}

/**
 * 插入结构体树
 */
export interface IBadgeCreate {
  name: string;
  children?: IBadgeCreate[];
}

export const BadgeType = {
  UITerritory: {
    id: getId(),
    btn_card: {
      id: getId(),
      btn_month_select: { id: getId() },
      btn_year_unselect: { id: getId() },
    },
    btn_shengji: {
      id: getId(),
      btn_go_lv_main: {
        id: getId(),
        btn_post_lv: { id: getId() }, // 主角满足升级条件
      },
      btn_go_skin_main: {
        id: getId(),
        btn_skin_activity: { id: getId() },
        btn_skin_identity: { id: getId() },
      },
    },
    btn_chongzhihaoli: {
      id: getId(),
      FundBanner001: {
        id: getId(),
        btn_type1: {
          id: getId(),
        },
      },
      FundBanner002: { id: getId() },
      FundBanner003: { id: getId() },
      FundBanner004: { id: getId() },
      FundBanner005: { id: getId() },
      FundBanner006: { id: getId() },
      FundBanner007: { id: getId() },
    },
    btn_xiuxing: {
      id: getId(),
      btn_type1: { id: getId() },
      btn_type2: { id: getId() },
      btn_type3: { id: getId() },
      btn_type4: { id: getId() },
    },
    btn_horse: {
      id: getId(),
      btn_upgrade: { id: getId() }, // 坐骑用资源可以进行升级和晋升操作时
    },
    btn_friend: {
      id: getId(),
      heart_talk: { id: getId() }, // 主角精力存储达到上限时
      tab_tujian: {
        id: getId(),
        // btn_award: {
        //   id: getId(),
        // },
      }, // 图鉴收集完成有奖励未领取的时候
      tab_friend: {
        id: getId(),
      },
    },
    btn_award: { id: getId() }, // 有每日奖励可以领取时
    btn_soul: {
      id: getId(),
      btn_tab_soul: {
        id: getId(),
        btn_get_soul: {
          id: getId(),
          btn_free_refresh: { id: getId() },
        },
      },
      btn_tab_tujian: {
        id: getId(),
        tab1: { id: getId() },
        tab2: { id: getId() },
        tab3: { id: getId() },
        tab4: { id: getId() },
        tab5: { id: getId() },
      },
    }, // 今日武魂有免费刷新次数时
    btn_fd_shtx: {
      id: getId(),
      btn_xslb: { id: getId() },
      btn_shtx_renwu: { id: getId() },
      leiji_award_item: { id: getId() },
      xiezhi: { id: getId() },
    },
    btn_lchk: {
      id: getId(),
      btn_type1: { id: getId() },
      btn_type2: { id: getId() },
      btn_type3: { id: getId() },
    },
    btn_activity_7: {
      id: getId(),
      btn_qiandaolingqu: { id: getId() }, // 签到按钮
    },
  },

  UIMajorCity: {
    id: getId(),
    btn_pupil: {
      id: getId(),
      btn_build: { id: getId() },
      btn_train: { id: getId() },
      btn_add: { id: getId() },
      btn_renming: { id: getId() },
    },
    btn_farm: {
      id: getId(),
      btn_build: { id: getId() },
    },
    btn_pet: {
      id: getId(),
      btn_build: { id: getId() },
    },
    btn_level_up: {
      id: getId(),
      btn_upgrade: { id: getId() }, // 主城可以进行升级时
    },
    build101: { id: getId() }, // 有建筑可以建造时
    build102: { id: getId() },
    build103: { id: getId() },
    build104: { id: getId() },
    build105: { id: getId() },
    build106: { id: getId() },
    build107: { id: getId() },
    build108: { id: getId() },
    build109: { id: getId() },
    build110: { id: getId() },
    build111: { id: getId() },
    build112: { id: getId() },
    build113: { id: getId() },
    build114: { id: getId() },
    build115: { id: getId() },
    btn_name101: {
      id: getId(),
      btn_shengji_node: { id: getId() }, // 升级
    },
    btn_name102: {
      id: getId(),
      btn_shengji_node: { id: getId() }, // 升级
    },
    btn_name103: {
      id: getId(),
      btn_shengji_node: { id: getId() }, // 升级
    },
    btn_name104: {
      id: getId(),
      btn_shengji_node: { id: getId() }, // 升级
    },
    btn_name105: {
      id: getId(),
      btn_shengji_node: { id: getId() }, // 升级
    },
    btn_name106: {
      id: getId(),
      btn_shengji_node: { id: getId() }, // 升级
    },
    btn_name107: {
      id: getId(),
      btn_shengji_node: { id: getId() }, // 升级
    },
    btn_name108: {
      id: getId(),
      btn_shengji_node: { id: getId() }, // 升级
    },
    btn_name109: {
      id: getId(),
      btn_shengji_node: { id: getId() }, // 升级
    },
    btn_name110: {
      id: getId(),
      btn_shengji_node: { id: getId() }, // 升级
    },
    btn_name111: {
      id: getId(),
      btn_shengji_node: { id: getId() }, // 升级
    },
    btn_name112: {
      id: getId(),
      btn_shengji_node: { id: getId() }, // 升级
    },
    btn_name113: {
      id: getId(),
      btn_shengji_node: { id: getId() }, // 升级
    },
    btn_name114: {
      id: getId(),
      btn_shengji_node: { id: getId() }, // 升级
    },
    btn_name115: {
      id: getId(),
      btn_shengji_node: { id: getId() }, // 升级
    },
    btn_reward: {
      id: getId(),
      btn_city_level: { id: getId() }, // 有奖励可以领取
      btn_worker_num: { id: getId() }, // 有奖励可以领取
    },
    btn_wuzurongyao: {
      id: getId(),
      btn_tab1: { id: getId() },
    },

    btn_wu_zu_yu_lan: {
      id: getId(),
      item01: { id: getId(), open: { id: getId() } },
      item02: { id: getId(), open: { id: getId() } },
      item03: { id: getId(), open: { id: getId() } },
      item04: { id: getId(), open: { id: getId() } },
      item05: { id: getId(), open: { id: getId() } },
    },

    btn_sanjiexiaojia: {
      id: getId(),
      btn_SJXJ_btn_chengjiu: { id: getId() },
      btn_SJXJ_btn_zhanjiang: { id: getId() },
      home_101: { id: getId() },
      home_102: { id: getId() },
      home_103: { id: getId() },
      home_104: { id: getId() },
      home_105: { id: getId() },
      home_106: { id: getId() },
      home_107: { id: getId() },
      home_108: { id: getId() },
      home_109: { id: getId() },
      home_110: { id: getId() },
    },
    UICityDetail: {
      id: getId(),
      btn_tab_box: {
        id: getId(),
        btn_open: { id: getId() },
      },
      btn_tab_level_up: {
        id: getId(),
        btn_level_up: { id: getId() },
      },
    },
    btn_pai_hang: {
      id: getId(),
      btn_zan: {
        id: getId(),
      },
    },
  },

  UIHeroMain: {
    id: getId(),
    tab_tujian: {
      id: getId(),
    },
  },

  UIClubMain: {
    id: getId(),
    btn_donate: {
      id: getId(),
    },
    btn_apply: {
      id: getId(),
    },
    btn_boss: {
      id: getId(),
      btn_hurt: { id: getId() },
      btn_remaind: { id: getId() },
    },
    btn_task: {
      id: getId(),
    },
  },

  btn_tiao_zhan: {
    id: getId(),
    btn_yan_wu_chang: {
      id: getId(),
      btn_showPk: { id: getId() },
      btn_showLog: { id: getId(), __stop: true }, // 不向上冒泡
    },
    btn_youli: {
      id: getId(),
      btn_travel: { id: getId() },
    },
    btn_post: {
      id: getId(),
      btn_post_award_get: { id: getId() },
      btn_go_post: { id: getId() },
      btn_lv_add: { id: getId() },
    },
  },

  UIMain: {
    id: getId(),
    btn_email: {
      id: getId(),
      btn_tab_system: { id: getId() },
      btn_tab_daily: { id: getId() },
    },
  },
};

export class BadgeMgr {
  public static get instance(): BadgeMgr {
    if (!GameData.instance.BadgeMgr) {
      GameData.instance.BadgeMgr = new BadgeMgr();
    }
    return GameData.instance.BadgeMgr;
  }

  private _idShowMap: Map<number, boolean>;
  private _idPathMap: Map<number, string[]>;

  private genIdPath(obj: any, pathParent: string[]) {
    for (const key in obj) {
      if (key == "id") {
        continue;
      }
      const id = obj[key].id;
      const path: string[] = pathParent.concat([key]);
      this._idPathMap.set(id, path);

      this.genIdPath(obj[key], path);
    }
  }

  private refreshIdShow(obj: any): boolean {
    if (!this._idShowMap) {
      this._idShowMap = new Map<number, boolean>();
    }

    let isUpdate = false;

    let rs = false;
    let childrenCount = 0;
    for (const key in obj) {
      if (key == "id") {
        continue;
      }

      // 停止红点冒泡
      if (key == "__stop") {
        return this._idShowMap.get(obj.id) || false;
      }

      childrenCount++;
      const id = obj[key].id;
      let childrenShow = this.refreshIdShow(obj[key]);

      if (this._idShowMap.get(id) != childrenShow) {
        // 状态变化通知
        isUpdate = true;
        this._idShowMap.set(id, childrenShow);
      }
      rs = rs || childrenShow;
    }

    // 没有子节点,取当前
    if (childrenCount == 0) {
      rs = this._idShowMap.get(obj.id) || false;
    }
    return rs;
  }

  public get idShowMap() {
    if (!this._idShowMap) {
      this.refreshIdShow(BadgeType);
    }
    return this._idShowMap;
  }

  public get idPathMap() {
    if (!this._idPathMap) {
      this._idPathMap = new Map<number, string[]>();
      this.genIdPath(BadgeType, []);
    }
    return this._idPathMap;
  }

  public setShowById(id: number, isShow: boolean) {
    if (BadgeMgr.instance.idShowMap.get(id) === isShow) {
      return;
    }
    BadgeMgr.instance.idShowMap.set(id, isShow);
    BadgeMgr.instance.refreshIdShow(BadgeType);
    MsgMgr.emit(MsgEnum.ON_BADGE_UPDATE, id, isShow);
  }

  // 是否显示
  public isShow(id: number) {
    return this.idShowMap.get(id);
  }

  public setBadgeId(node: Node, id: number) {
    if (!node) {
      log.error("BadgeMgr setBadgeId node is null", id);
      return;
    }

    const cpt = node.getChildByName("Badge").getComponent("Badge");
    cpt["setId"] && cpt["setId"](id);
  }

  // 动态批量加红点, 传入列表
  public addListBadgeItem(badge: any, nameList: string[]) {
    for (let i = 0; i < nameList.length; i++) {
      let name = nameList[i];
      if (!badge[name]) {
        badge[name] = { id: getId() };
      }
    }
    this._idPathMap = null;
    this.refreshIdShow(BadgeType);
  }

  /**
   * 批量加复杂红点提示层
   * @param badge 上级节点
   * @param itemExtList 要加的节点列表
   */
  public addBadgeItemExt(badge: any, itemExtList: IBadgeCreate[]) {
    for (let i = 0; i < itemExtList.length; i++) {
      let itemExt = itemExtList[i];
      if (!badge[itemExt.name]) {
        badge[itemExt.name] = { id: getId() };
      }
      if (itemExt.children) {
        this.addBadgeItemExt(badge[itemExt.name], itemExt.children);
      }
    }

    this._idPathMap = null;
    this.refreshIdShow(BadgeType);
  }
}
