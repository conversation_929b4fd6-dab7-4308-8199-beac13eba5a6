import { Layers, Prefab, _decorator, instantiate, isValid } from "cc";
import ManagerSection from "./ManagerSection";
import GameObject from "../../../lib/object/GameObject";
import FightManager from "./FightManager";
import ResMgr from "../../../lib/common/ResMgr";
const { ccclass, property } = _decorator;

@ccclass("BuffLayerManager")
export class BuffLayerManager extends ManagerSection {
  public static sectionName(): string {
    return "BuffManager";
  }
  public onInit(go: GameObject, args) {
    super.onInit(go, args);
    this.createRoot("BuffLayerRoot", FightManager.instance);
    this.ready();
  }

  public onStart(): void {
    super.onStart();
  }

  public callBuffLayer(callback) {
    if (FightManager.instance.fightOver == true) {
      return;
    }
    ResMgr.loadPrefab("resources?prefab/effect/buffLayer", (prefab: Prefab) => {
      if (isValid(this.root) == false) {
        return;
      }
      let node = instantiate(prefab);
      this.root.addChild(node);
      // node.layer = Layers.Enum["UILayer"];
      node.walk((child) => (child.layer = this.root.layer));
      callback && callback(node);
    });
  }
}
