import { _decorator, Component, Label, math, Node, Sprite } from "cc";
import { PetModule } from "../../../module/pet/PetModule";
import ResMgr from "../../../lib/common/ResMgr";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { JsonMgr } from "../../mgr/JsonMgr";
import { HeroColorBord, HeroColorCard } from "../../../module/hero/HeroConstant";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { HeroModule } from "../../../module/hero/HeroModule";
import { HeroRouteItem } from "../../../module/hero/HeroRoute";
import { HeroSort, HeroType } from "../../../module/hero/HeroConfig";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UIPetCard")
export class UIPetCard extends Component {
  @property(Sprite)
  private bgPet: Sprite = null;
  @property(Sprite)
  private spPet: Sprite = null;
  @property(Sprite)
  private bgColorBord: Sprite = null;
  @property(Sprite)
  private bgHeroHead: Sprite = null; // 用于显示英雄头像的Sprite组件
  @property(Label)
  private lblName: Label = null;
  @property(Label)
  private lblLevel: Label = null;
  @property(Label)
  private lblAwakeLevel: Label = null;

  private _petId: number = 0;
  private _position: number = 0;
  start() {}

  update(deltaTime: number) {}

  public initInfo(petId: number, position: number) {
    this._petId = petId;
    this._position = position;
    // 获取宠物配置和当前状态信息
    const petInfo = PetModule.data.getPet(petId); // 当前宠物状态（等级、觉醒次数等）
    const petConfig = PetModule.config.getHeroPet(petId); // 宠物基础配置
    // 确定皮肤ID（参考UIHeroPetAwake的皮肤逻辑）
    const spirit = PetModule.config.getHeroPet(petConfig.id);
    const petSkinList = [spirit.firstSkin, ...spirit.skin];
    const skinId = petSkinList.includes(petInfo?.chosenSkinId ?? -1) ? petInfo.chosenSkinId : spirit.firstSkin;
    const petSkin = JsonMgr.instance.jsonList.c_petSkin[skinId];
    // 设置文本信息
    this.lblName.string = petSkin.name;
    if (petInfo) {
      this.lblLevel.node.active = true;
      this.lblAwakeLevel.node.active = true;
      this.lblLevel.string = `${petInfo.level}`;
      this.lblAwakeLevel.string = `${petInfo.awakeCount}阶`;
      if (petInfo.awakeCount > 0) {
        this.lblAwakeLevel.node.active = true;
      } else {
        this.lblAwakeLevel.node.active = false;
      }
    } else {
      this.lblLevel.node.active = false;
      this.lblAwakeLevel.node.active = false;
      this.node.getComponentsInChildren(Sprite).forEach((sprite) => {
        sprite.color = math.color("#6e6e6e");
      });
    }
    // 新增：获取宠物对应的英雄ID（通过PetData的映射方法）
    const heroId = PetModule.data.getHeroIdByPetId(petId);
    if (heroId) {
      // 加载英雄头像（参考UIFriendPreview的实现）
      ResMgr.setSpriteFrame(BundleEnum.BUNDLE_COMMON_ITEM, `autoItem/item_${heroId}`, this.bgHeroHead);
      this.bgHeroHead.node.active = true;
    } else {
      this.bgHeroHead.node.active = false; // 无对应英雄时隐藏头像
      log.log(`未找到宠物petId=${petId}对应的英雄ID`);
    }

    if (!petSkin) {
      log.log(`未找到皮肤配置：skinId=${skinId}`);
      return;
    }

    // 加载背景颜色边框（参考HeroColorBord）
    ResMgr.loadSpriteFrame(
      `${BundleEnum.BUNDLE_COMMON_HERO_ICON}?patterns/heroIcon`,
      HeroColorBord[`color_${petSkin.color}`],
      this.bgColorBord
    );

    // 加载宠物主图（参考pet_card资源路径）
    ResMgr.setSpriteFrame(BundleEnum.BUNDLE_COMMON_PET, `pet_card/${petSkin.cardId}`, this.spPet);
  }

  private onClickCard() {
    AudioMgr.instance.playEffect(1921);
    // 获取当前宠物ID
    const petId = this._petId;
    // 通过PetData获取对应的heroId
    const heroId = PetModule.data.getHeroIdByPetId(petId);

    if (!heroId) {
      log.log(`未找到宠物petId=${petId}对应的英雄ID`);
      return;
    }

    // 检查英雄是否存在
    const heroExists = HeroModule.data.getHeroMessage(heroId);

    if (heroExists) {
      // 跳转到英雄详情界面（假设路由名称为HeroRouteItem.UIHeroDetail）
      let ownedHeros = HeroModule.data.getOwnedHeros(HeroSort.DEFAULT, HeroType.ALL);
      let index = ownedHeros.findIndex((hero) => hero.id === heroId);
      UIMgr.instance.showDialog(HeroRouteItem.UIHeroDetail, [ownedHeros, index, 2]);
    } else {
      // 跳转到英雄预览界面（假设路由名称为HeroRouteItem.UIHeroPreview）
      let unOwnedHeros = HeroModule.data.getUnOwnedHeros();
      let index = unOwnedHeros.findIndex((hero) => hero.id === heroId);
      UIMgr.instance.showDialog(HeroRouteItem.UIHeroDetail, [unOwnedHeros, index, 2]);
    }
  }
}
