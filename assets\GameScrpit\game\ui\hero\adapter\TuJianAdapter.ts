import { instantiate, Node } from "cc";
import { ListAdapter } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { TuJianViewHolder } from "./TuJianViewHolder";
import { UIHeroMainTuJian } from "../UIHeroMainTuJian";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class TuJianAdapter extends ListAdapter {
  private _datas: any[];
  private viewHolder: Node;
  private context: UIHeroMainTuJian;

  constructor(viewHolder: Node, context: UIHeroMainTuJian) {
    super();
    this.viewHolder = viewHolder;
    this.context = context;
  }
  public setData(data: any[]) {
    this._datas = data;
    log.log("setData---", data);
    this.notifyDataSetChanged();
  }
  onCreateView(viewType: number): Node {
    let item = instantiate(this.viewHolder);
    item.active = true;
    return item;
  }
  onBindData(node: Node, position: number): void {
    // log.log("updateView---", position);
    node.getComponent(TuJianViewHolder).updateData(this._datas[position], this.context);
  }
  getCount(): number {
    return this._datas.length;
  }
}
