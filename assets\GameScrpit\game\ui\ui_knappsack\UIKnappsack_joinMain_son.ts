import { _decorator, is<PERSON><PERSON><PERSON>, <PERSON>de, tween, v3 } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { JsonMgr } from "../../mgr/JsonMgr";
import { PlayerModule } from "../../../module/player/PlayerModule";
import ToolExt from "../../common/ToolExt";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { ItemType1Enum, ItemType2Enum } from "../../../module/player/PlayerData";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import { dtTime } from "../../BoutStartUp";
import { Sleep } from "../../GameDefine";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
import { KnappsackAudioName } from "../../../module/player/PlayerConfig";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "../../../lib/utils/FmUtils";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UIKnappsack_joinMain_son")
export class UIKnappsack_joinMain_son extends UINode {
  protected _isAddToBottom: boolean = true;
  protected _isSetParent: boolean = true;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_KNAPSACK}?prefab/ui/UIKnappsack_joinMain_son`;
  }

  private _joinMainIndex = 0;

  protected onRegEvent(): void {
    MsgMgr.on(MsgEnum.ON_EXIST_ITEM_CHANGE, this.upList, this);
  }

  protected onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_EXIST_ITEM_CHANGE, this.upList, this);
  }
  protected onEvtShow(): void {
    this.initJoinMain();
  }
  /**合成 */
  private async initJoinMain() {
    let data = JsonMgr.instance.getItemBagTypeList(3);
    for (let i = 0; i < data.length; i++) {
      if (i > 2 && i % 4 == 0) {
        await Sleep(0.01);
        if (isValid(this.node) == false) {
          return;
        }
      }

      let node = ToolExt.clone(this["btn_item"], this);
      this["joinContent"].addChild(node);
      node.active = true;
      /**设置品质和图标 */
      let info = data[i];

      let myItemNum = PlayerModule.data.getItemNum(info.id);
      FmUtils.setItemNode(node, info.id, myItemNum, true);

      node.setScale(0, 0, 1);
      tween(node)
        .to(0.2, { scale: v3(1, 1, 1) })
        .start();

      /**设置node的类型界面 */
      node["itemType"] = info.type1;
      node["itemId"] = info.id;
      this._joinMainIndex++;
    }
  }

  private on_click_btn_item(event) {
    AudioMgr.instance.playEffect(KnappsackAudioName.Effect.点击道具图标);
    let ItemType = event.node["itemType"];
    let itemId = event.node["itemId"];
    log.log(ItemType);
    this.btnItemSwitch(ItemType, itemId);
  }

  private btnItemSwitch(ItemType: ItemType1Enum, itemId: number) {
    switch (ItemType) {
      case ItemType1Enum.Look_Item:
        UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, { itemId: itemId });
        break;

      case ItemType1Enum.Add_Item:
        UIMgr.instance.showDialog(PlayerRouteName.UIItemUse, { itemId: itemId });
        break;

      case ItemType1Enum.Join_Item:
        UIMgr.instance.showDialog(PlayerRouteName.UIItemJoinPop, { itemId: itemId });
        break;

      case ItemType1Enum.Get_GD_Item:
        UIMgr.instance.showDialog(PlayerRouteName.UIItemUse, { itemId: itemId });
        break;

      case ItemType1Enum.Get_SJ_Item:
        UIMgr.instance.showDialog(PlayerRouteName.UIItemUse, { itemId: itemId });
        break;

      case ItemType1Enum.Get_ZX_Item:
        UIMgr.instance.showDialog(PlayerRouteName.UIItemUseZx, { itemId: itemId });
        break;
    }
  }

  private upList(id: number) {
    let info = JsonMgr.instance.getConfigItem(id);
    for (let i = 0; i < info.type2List.length; i++) {
      if (info.type2List[i] == ItemType2Enum.Join_Item) {
        this.upItemMain(id);
        break;
      }
    }
  }

  private upItemMain(id: number) {
    let node: Node = null;
    for (let i = 0; i < this["joinContent"].children.length; i++) {
      if (id == this["joinContent"].children[i]["itemId"]) {
        node = this["joinContent"].children[i];
        break;
      }
    }

    if (!node) {
      return;
    }

    let num = PlayerModule.data.getItemNum(id);
    if (num <= 0 && node) {
      node.removeFromParent();
      node.destroy();
      return;
    }

    if (num > 0 && node == null) {
      node = this.addNewItem(id, this["joinContent"]);
    }

    let c_item_info = JsonMgr.instance.getConfigItem(id);
    if (ItemType1Enum.Look_Item == c_item_info.type1) {
      FmUtils.setItemNode(node, id, num, false);
    } else {
      FmUtils.setItemNode(node, id, num, true);
    }
  }

  /**添加新获得的道具种类 */
  public addNewItem(itemId: number, parent: Node) {
    let node = ToolExt.clone(this["btn_item"], this);
    parent.addChild(node);
    node.active = true;
    let c_item_info = JsonMgr.instance.getConfigItem(itemId);
    /**设置node的类型界面 */
    node["itemType"] = c_item_info.type1;
    node["itemId"] = c_item_info.id;
    /**设置品质和图标 */
    let myItemNum = PlayerModule.data.getItemNum(c_item_info.id);
    FmUtils.setItemNode(node, c_item_info.id, myItemNum, true);
    return node;
  }
}
