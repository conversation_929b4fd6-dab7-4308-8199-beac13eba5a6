import { _decorator, isValid } from "cc";
import { FSMState } from "../fight/FSM/FSMState";
import { FSMBoard, STATE } from "../fight/section/StateSection";
import { AnimationSection } from "../fight/section/AnimationSection";
const { ccclass, property } = _decorator;

@ccclass
export default class Idle extends FSMState {
  public async onEnter(board: FSMBoard) {
    board.sub.getSection(AnimationSection).playAction(1, true);
  }

  public update(board: FSMBoard, dt) {}

  public onExit(board: FSMBoard) {}
}
