import { EventTarget } from "cc";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
// import EventMgr from "../../game/mgr/EventMgr";
const log = Logger.getLoger(LOG_LEVEL.WARN);

export default class MsgMgr {
  static EventMgr = new EventTarget();
  // private static _list: Object = Object.create(null);
  // private static _targets: Set<any> = new Set<any>();
  // private static _once: Set<any> = new Set<any>();
  // private static _onceTarget: Object = Object.create(null);

  public static removeAll() {
    // MsgMgr._list = Object.create(null);
    // MsgMgr._targets = Object.create(null);
    // MsgMgr._once = Object.create(null);
    // MsgMgr._onceTarget = Object.create(null);
    MsgMgr.EventMgr = new EventTarget();
  }

  public static on<T extends (...args: any[]) => void>(type: string, callback: T, target: any) {
    MsgMgr.EventMgr.on(type, callback, target);
    // if (!this._list[type]) {
    //   this._list[type] = [callback];
    //   this._targets[type] = [target];
    // } else {
    //   if (this._list[type].indexOf(callback) == -1 || this._targets[type].indexOf(target) == -1) {
    //     this._list[type].push(callback);
    //     this._targets[type].push(target);
    //   }
    // }
  }
  public static once<T extends (...args: any[]) => void>(type: string, callback: T, target: any) {
    MsgMgr.EventMgr.once(type, callback, target);
    // if (!this._once[type]) {
    //   this._once[type] = [callback];
    //   this._onceTarget[type] = [target];
    // } else {
    //   if (this._once[type].indexOf(callback) == -1 || this._onceTarget[type].indexOf(target) == -1) {
    //     this._once[type].push(callback);
    //     this._onceTarget[type].push(target);
    //   }
    // }
  }
  public static off<T extends (...args: any[]) => void>(type: string, callback?: T, target?: any): void {
    MsgMgr.EventMgr.off(type, callback, target);
    // if (callback) {
    //   if (this._list[type]) {
    //     for (var i: number = 0; i < this._list[type].length; i++) {
    //       if (this._list[type][i] == callback && this._targets[type][i] == target) {
    //         this._list[type].splice(i, 1);
    //         this._targets[type].splice(i, 1);
    //         break;
    //       }
    //     }
    //   }
    //   if (this._once[type]) {
    //     for (var i: number = 0; i < this._once[type].length; i++) {
    //       if (this._once[type][i] == callback && this._onceTarget[type][i] == target) {
    //         this._once[type].splice(i, 1);
    //         this._onceTarget[type].splice(i, 1);
    //         break;
    //       }
    //     }
    //   }
    // } else {
    //   delete this._list[type];
    //   delete this._once[type];
    //   delete this._targets[type];
    //   delete this._onceTarget[type];
    // }
  }

  public static emit(type: string, arg1?: any, arg2?: any, arg3?: any, arg4?: any, arg5?: any, arg6?: any): void {
    MsgMgr.EventMgr.emit(type, arg1, arg2, arg3, arg4, arg5);
    // if (this._list[type]) {
    //   if (type == "UIReady") {
    //     console.warn("打断点");
    //     console.warn("arg1====", arg1);
    //     console.warn("UIReady 的 this._list[type][i] ====", this._list[type]);
    //   }
    //   let len: number = this._list[type].length;
    //   for (let i: number = len - 1; i >= 0; i--) {
    //     try {
    //       if (type == "UIReady") {
    //         console.error("UIReady 的 i =====", i);
    //       }
    //       this._list[type][i].call(this._targets[type][i], arg1, arg2, arg3, arg4, arg5, arg6);
    //     } catch (e) {
    //       log.error("emit err", type);
    //       log.error("emit arg1", arg1);
    //       log.error("emit err", e);
    //     }
    //   }
    // }
    // if (this._once[type]) {
    //   let len: number = this._once[type].length;
    //   for (let i: number = 0; i < len; i++) {
    //     this._once[type][i].call(this._onceTarget[type][i], arg1, arg2, arg3, arg4, arg5, arg6);
    //   }
    //   delete this._once[type];
    //   delete this._onceTarget[type];
    // }
  }
}
