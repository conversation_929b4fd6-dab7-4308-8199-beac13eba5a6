import { JsonMgr } from "../../game/mgr/JsonMgr";
import { FirstSubRechargeMessage } from "../../game/net/protocol/Activity";
import { times } from "../../lib/utils/NumbersUtils";
import { HdShouChongModule } from "./HdShouChongModule";

export class HdShouChongData {

  private _firstRechargeMessage: { [key: number]: FirstSubRechargeMessage } = {};
  public init() {
    this._firstRechargeMessage = {};
  }

  public get firstRechargeMessage(): { [key: number]: FirstSubRechargeMessage } {
    return this._firstRechargeMessage;
  }
  public set firstRechargeMessage(value: { [key: number]: FirstSubRechargeMessage }) {
    this._firstRechargeMessage = value;
    // MsgMgr.emit(MsgEnum.ON_ACTIVITY_FIRST_UPDATE, this._firstRechargeMessage);
  }
}
