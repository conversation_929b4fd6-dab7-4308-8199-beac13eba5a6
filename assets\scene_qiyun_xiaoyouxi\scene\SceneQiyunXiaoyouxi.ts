import { _decorator, Camera, Component, director, Node } from "cc";
import { PlayerModule } from "../../GameScrpit/module/player/PlayerModule";
import { QiyunXiaoyouxiMgr } from "../QiyunXiaoyouxiMgr";
import { XiaoyouxiUIMain } from "../scripts/XiaoyouxiUIMain";
import { XiaoyouxiUIRun } from "../scripts/XiaoyouxiUIRun";
import { ISceneCtrl } from "../interface/ISceneCtrl";
import { XiaoyouxiUIFight } from "../scripts/XiaoyouxiUIFight";
import TickerMgr from "../../GameScrpit/lib/ticker/TickerMgr";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
import { UIMgr } from "../../GameScrpit/lib/ui/UIMgr";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("SceneQiyunXiaoyouxi")
export class SceneQiyunXiaoyouxi extends BaseCtrl implements ISceneCtrl {
  @property(Node)
  private nodeFight: Node;
  @property(Node)
  private nodeRun: Node;
  @property(Node)
  private nodeUI: Node;

  @property(Node)
  private nodeCamera: Node;

  protected autoSetLayer: boolean = false;

  start() {
    super.start();

    UIMgr.instance.uiRoot.active = false;
    UIMgr.instance.gameRoot.active = false;

    // 获取top层相机,同步cavas_top的相机配置
    let orthoHeight = director.getScene().getChildByPath("canvas_top/Camera").getComponent(Camera).orthoHeight;
    this.nodeCamera.getComponent(Camera).orthoHeight = orthoHeight;

    // 要有Z轴，否则要显示的node的Z轴大于相机的将不会显示
    this.nodeCamera.setPosition(0, 0, 100);

    QiyunXiaoyouxiMgr.instance.setCamera(this.nodeCamera);
    QiyunXiaoyouxiMgr.instance.startGame(
      this,
      this.nodeRun.getComponent(XiaoyouxiUIRun),
      this.nodeUI.getComponent(XiaoyouxiUIMain)
    );
    //this.scheduleOnce(this.showFight.bind(this), 1);
  }

  update(deltaTime: number) {
    // QiyunXiaoyouxiMgr.instance.update(deltaTime);

    try {
      TickerMgr.instance.doTick(deltaTime);
    } catch (erro) {
      log.error("StartUp.update---erro", erro);
    }
  }

  protected onDestroy(): void {
    super.onDestroy();
    UIMgr.instance.uiRoot.active = true;
    UIMgr.instance.gameRoot.active = true;
  }

  showFight(show: boolean): void {
    this.nodeFight.active = show;
    if (show) {
      // 男1 女2
      let playerId = PlayerModule.data.getPlayerInfo().sex == 1 ? 1706 : 1716;
      let bossId = 10005;
      this.nodeFight.getComponent(XiaoyouxiUIFight).init({ playerId: playerId, bossId: bossId });
    }
  }

  showUI(show: boolean): void {
    this.nodeUI.active = show;
  }

  showRun(show: boolean): void {
    this.nodeRun.active = show;
  }

  //测试接口
  private onClickClose() {
    director.loadScene("SceneLoading", () => {
      PlayerModule.data.subEvent();
      PlayerModule.data.onPlayerAttrUpdate();
    });
  }
}
