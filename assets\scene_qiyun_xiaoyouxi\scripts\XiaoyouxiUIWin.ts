import { _decorator, Component, instantiate, Node, Prefab, sp, tween, v3 } from "cc";
import { GuideRouteEnum } from "../../GameScrpit/ext_guide/GuideDefine";
import { TipsMgr } from "../../platform/src/TipsHelper";
import { UIMgr } from "../../GameScrpit/lib/ui/UIMgr";
import { CityRouteName } from "../../GameScrpit/module/city/CityConstant";
import { PlayerModule } from "../../GameScrpit/module/player/PlayerModule";
import FightManager from "../../GameScrpit/game/fight/manager/FightManager";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
import ResMgr from "../../GameScrpit/lib/common/ResMgr";
import { BundleEnum } from "../../platform/src/ResHelper";
const { ccclass, property } = _decorator;

@ccclass("XiaoyouxiUIWin")
export class XiaoyouxiUIWin extends BaseCtrl {
  public init() {
    TipsMgr.setEnableTouch(false, 1);
    this.getNode("spine_ji_bai_shi_ji").getComponent(sp.Skeleton).setAnimation(0, "animation", false);

    let t1 = tween(this.getNode("lbl_go"))
      .to(0.5, { scale: v3(1.2, 1.2, 1) }, { easing: "sineInOut" })
      .to(0.5, { scale: v3(1, 1, 1) }, { easing: "sineInOut" });

    tween(this.getNode("lbl_go")).repeatForever(t1).start();
  }

  on_click_btn_close() {
    TipsMgr.setEnableTouch(false, 2);
    FightManager.instance.exit();

    // 场景跳转
    TipsMgr.topRouteCtrl.show(GuideRouteEnum.TopLoading, () => {
      return new Promise((reslove) => {
        TipsMgr.topRouteCtrl.closeByName(GuideRouteEnum.TopQiYunGame);
        UIMgr.instance.showPage(CityRouteName.UIGameMap, null, null, () => {
          PlayerModule.api.updateGuideId(11);

          UIMgr.instance.addToUIRoot(BundleEnum.BUNDLE_G_COMMON_MAIN, "prefab/ui/UIMain", "UIMain", () => {
            reslove(true);
            TipsMgr.setEnableTouch(true);
          });
        });
      });
    });
  }
}
