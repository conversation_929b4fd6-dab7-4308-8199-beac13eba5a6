import { JsonMgr } from "../../game/mgr/JsonMgr";
import { FractureModule } from "./FractureModule";

export enum FractureState {
  SEARCH, // 表示搜索状态
  BARRIER, // 表示障碍物状态
  MONSTER, // 表示非玩家角色状态
  PLAYER, // 表示玩家状态
  DRAW, // 表示抽奖状态
  BAOXIANG, // 表示宝箱状态
  OLD_MAN, // 表示老人状态
}
/**
 * 模块逻辑处理
 */
export class FractureService {
  /**
   * 获取当前解锁的最高难度等级
   * @returns 难度等级（1/2/3）
   */
  public getUnlockedDifficulty(): number {
    const idList = Object.keys(FractureModule.data.fractureData.remainFloorCountMap).map(Number) || [];
    if (idList.includes(301)) return 3; // 存在202则解锁到难度3
    if (idList.includes(201)) return 2; // 存在102则解锁到难度2
    return 1; // 默认解锁难度1
  }

  public getSelectedDifficulty(): number {
    const floorId = FractureModule.data.fractureData.floorId;
    if (floorId == 101 || floorId == 102) {
      return 1;
    } else if (floorId == 201 || floorId == 202) {
      return 2;
    } else if (floorId == 301 || floorId == 302) {
      return 3;
    }
    return 1;
  }

  public getCurrentSearchCount(): number {
    let floorId = FractureModule.data.fractureData.floorId;
    let fractures = Object.values(JsonMgr.instance.jsonList.c_fracture);
    let currentFractures = fractures.filter((val) => {
      return val.floor == floorId;
    });
    let maxCount = 0;
    currentFractures.forEach((val) => {
      maxCount += val.max;
    });
    return maxCount;
  }

  public getFractureState(): FractureState {
    const fractureId = FractureModule.data.fractureData.fractureId;
    if (fractureId <= 0) {
      return FractureState.SEARCH;
    }
    let type = JsonMgr.instance.jsonList.c_fracture[fractureId].type;
    switch (type) {
      case 1:
        let eventId = FractureModule.data.fractureData.choiceId;
        if (eventId === 5508) {
          return FractureState.BARRIER;
        }
        return FractureState.OLD_MAN;
      case 2:
        return FractureState.DRAW;
      case 3:
      case 4:
        return FractureState.MONSTER;
      case 5:
        if (FractureModule.data.fractureData.fightMessage.npcMessage) {
          return FractureState.PLAYER;
        } else {
          return FractureState.MONSTER;
        }
      case 6:
      case 7:
      case 8:
        return FractureState.BAOXIANG;
    }
  }
}
