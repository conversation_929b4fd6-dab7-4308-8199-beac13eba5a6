/**
 * 观察者系统
 *
 * <AUTHOR>
 * @created 2024-08-30 10:40:53
 */
export abstract class GGObserverSystem<T> {
    private _observers: Set<T> | null = null;

    /**
     * 观察者
     */
    get observers(): Set<T> {
        if (this._observers == null) {
            this._observers = new Set();
        }
        return this._observers;
    }

    /**
     * 注册观察者
     */
    register(obserber: T) {
        this.observers.add(obserber);
    }
    /**
     * 注销观察者
     */
    unregister(observer: T) {
        this.observers.delete(observer);
    }
    /**
     * 注销所有观察者
     */
    unregisterAll() {
        this.observers.clear();
    }
}
