import { _decorator, isValid, Label, Node } from "cc";
import { UIMgr } from "../../GameScrpit/lib/ui/UIMgr";
import { PlayerRouteName } from "../../GameScrpit/module/player/PlayerConstant";
import { BundleEnum } from "../../GameScrpit/game/bundleEnum/BundleEnum";
import { Sprite } from "cc";
import { PlayerBaseMessage } from "../../GameScrpit/game/net/protocol/Player";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
import TipMgr from "../../GameScrpit/lib/tips/TipMgr";
import { PlayerModule } from "../../GameScrpit/module/player/PlayerModule";
import { AudioMgr } from "../../platform/src/AudioHelper";
const { ccclass, property } = _decorator;

/**
 * args: FarmSimpleMessage
 */
@ccclass("BtnHeaderCtrl")
export class BtnHeaderCtrl extends BaseCtrl {
  @property(Sprite)
  private bgHead: Sprite = null;
  @property(Node)
  private bgGuizu: Node = null;
  @property(Label)
  private lblVipLv: Label = null;

  private playerBaseMessage: PlayerBaseMessage = null;
  private isRobot: boolean = false;

  protected onLoad(): void {
    super.onLoad();
  }

  public async setHeader(args: PlayerBaseMessage, isRobot: boolean = false) {
    this.playerBaseMessage = args;
    this.isRobot = isRobot;
    // 设置头像
    let db = PlayerModule.service.getAvatarConfig(this.playerBaseMessage.avatarList[1]);

    const sp = await this.assetMgr.loadSpriteFrameSync(
      BundleEnum.BUNDLE_COMMON_PLAYERHEAD,
      `atlas_head_imgs/${db.iconSpr}`
    );

    if (isValid(this.node)) {
      this.bgHead.spriteFrame = sp;
      this.bgHead.node.active = true;
      if (args.vipLevel > 0) {
        this.bgGuizu.active = true;
        this.lblVipLv.string = `${args.vipLevel}`;
      } else {
        this.bgGuizu.active = false;
      }
    }
  }

  public onClick() {
    AudioMgr.instance.playEffect(518);
    if (this.isRobot == true) {
      TipMgr.showTip("不可查看");
      return;
    }
    if (this.playerBaseMessage.userId && this.playerBaseMessage.userId > 0) {
      UIMgr.instance.showDialog(PlayerRouteName.UIPlayerOtherMsg, this.playerBaseMessage);
    }
  }
}
