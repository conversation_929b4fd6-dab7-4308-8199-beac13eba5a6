import { _decorator, instantiate, Label, Node, Prefab, ProgressBar, sp, tween, v3 } from "cc";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
import { IConfigGuideV2 } from "../game/JsonDefine";
import { JsonMgr } from "../game/mgr/JsonMgr";
import { TipsMgr } from "../../platform/src/TipsHelper";
import { GuideRouteEnum } from "./GuideDefine";
import { UIMgr } from "../lib/ui/UIMgr";
import { CityRouteName } from "../module/city/CityConstant";
import { PlayerModule } from "../module/player/PlayerModule";
import ResMgr from "../lib/common/ResMgr";
import { BundleEnum } from "../game/bundleEnum/BundleEnum";
import { AudioMgr } from "../../platform/src/AudioHelper";
const { ccclass, property } = _decorator;

const enum TalkStatusEnum {
  Start = 0,
  Playing = 1,
  End = 2,
}

@ccclass("TopFusu")
export class TopFusu extends BaseCtrl {
  @property(sp.Skeleton)
  spineFinger: sp.Skeleton;

  @property(Node)
  roleSudaji: Node;

  @property(Node)
  rolePlayer: Node;

  @property(sp.Skeleton)
  spineFusu: sp.Skeleton;

  @property(Node)
  btnFusu: Node;

  @property(Node)
  nodeLayoutWord1: Node;

  @property(Node)
  nodeLayoutWord2: Node;

  @property(Label)
  lblName1: Label;

  @property(Label)
  lblName2: Label;

  @property(Label)
  lblWord1: Label;

  @property(Label)
  lblWord2: Label;

  @property(ProgressBar)
  progressBar: ProgressBar;

  @property(Node)
  nodeBtnGo: Node;

  // 对话列表
  configGuideV2List: IConfigGuideV2[];

  // 当前
  idx: number = 0;

  // 对话状态
  talkStatus: TalkStatusEnum = TalkStatusEnum.Start;

  // 占击时间
  touchTime: number = -1;

  // 冷却时间
  cd: number = 1;

  // 是否长按
  isTouch: boolean = false;

  async start() {
    this.btnFusu.getChildByName("spine_fusu").getComponent(sp.Skeleton).paused = true;

    this.rolePlayer.getChildByName("spine_player").getComponent(sp.Skeleton).setAnimation(0, "fusu1", true);
    this.roleSudaji.getChildByName("spine_sudaji").getComponent(sp.Skeleton).setAnimation(0, "boss_idle", true);

    tween(this.roleSudaji.getChildByName("spine_sudaji"))
      .by(0.8, { position: v3(0, 15, 0) }, { easing: "sineInOut" })
      .by(0.8, { position: v3(0, -15, 0) }, { easing: "sineInOut" })
      .union()
      .repeatForever()
      .start();

    this.spineFusu.node.active = false;

    this.btnFusu.on(Node.EventType.TOUCH_START, this.onBtnFusuStart, this);
    this.btnFusu.on(Node.EventType.TOUCH_END, this.onBtnFusuEnd, this);
    this.btnFusu.on(Node.EventType.TOUCH_CANCEL, this.onBtnFusuCancel, this);

    this.configGuideV2List = JsonMgr.instance.getConfigGuideV2List(1, 10);
    this.nodeLayoutWord1.active = false;
    this.nodeLayoutWord2.active = false;

    this.progressBar.progress = 0;
    this.progressBar.node.active = false;

    this.btnFusu.active = false;

    this.lblWord1.string = "";
    this.lblWord2.string = "";

    this.nodeBtnGo.active = false;
    this.spineFinger.node.active = false;

    // 切换男女角
    this.rolePlayer.getChildByName("spine_player").active = false;
    let spinePlayer = this.rolePlayer.getChildByName("spine_player").getComponent(sp.Skeleton);
    if (PlayerModule.data.getPlayerInfo().sex == 2) {
      spinePlayer.skeletonData = await this.assetMgr.loadSpineSync("resources", "spine/role/nvzhujue1");
    } else {
      spinePlayer.skeletonData = await this.assetMgr.loadSpineSync("resources", "spine/role/zhujue1");
    }
    this.rolePlayer.getChildByName("spine_player").active = true;
    spinePlayer.setAnimation(0, "fusu1", true);

    // 开始对话
    this.idx = 0;
    this.onBtnNext();

    // // 测试代码，不能提交
    // TipsMgr.topRouteCtrl.show(GuideRouteEnum.TopQiYunGame, null, null, () => {
    //   TipsMgr.topRouteCtrl.closeByName(GuideRouteEnum.TopFusu);
    // });
  }

  update(deltaTime: number) {
    let configGuideV2 = this.configGuideV2List[this.idx];

    if (!configGuideV2) {
      return;
    }

    // 更新对话
    if (configGuideV2.type === 1) {
      // 对话播放文字
      if (this.talkStatus === TalkStatusEnum.Playing) {
        // 0.1秒显示一个字
        this.cd += deltaTime;
        if (this.cd < 0.1) {
          return;
        }

        this.cd = 0;

        let strWordNow = "";
        if (configGuideV2.position === "1") {
          strWordNow = this.lblWord1.string;
        } else if (configGuideV2.position === "2") {
          strWordNow = this.lblWord2.string;
        }

        if (strWordNow.length < configGuideV2.args.length) {
          strWordNow = configGuideV2.args.substring(0, strWordNow.length + 1);
        } else {
          strWordNow = configGuideV2.args;
          this.talkStatus = TalkStatusEnum.End;
        }

        if (configGuideV2.position === "1") {
          this.lblWord1.string = strWordNow;
        } else if (configGuideV2.position === "2") {
          this.lblWord2.string = strWordNow;
        }
      }
    } else if (configGuideV2.id == 3) {
      // 复苏长按
      let max = Number(configGuideV2.args);
      if (this.touchTime >= max) {
        return;
      } else if (this.touchTime > 0) {
        if (this.isTouch) {
          this.touchTime += deltaTime;

          if (this.touchTime >= max) {
            this.progressBar.progress = 1;
            this.startFusu();
          } else {
            this.progressBar.progress = this.touchTime / max;
          }
        } else {
          this.touchTime -= deltaTime / 2;
          if (this.touchTime < 0) {
            this.touchTime = 0;
            this.updateBtnStatus(false);

            this.spineFusu.node.active = false;
            this.spineFinger.node.active = true;
          }
          this.progressBar.progress = this.touchTime / max;
        }
      }
    }
  }

  updateBtnStatus(isFusu: boolean = false) {
    let spineFusu = this.btnFusu.getChildByName("spine_fusu").getComponent(sp.Skeleton);
    spineFusu.setAnimation(0, "anniu", false);
    setTimeout(() => {
      spineFusu.paused = !isFusu;
    }, 1);
  }

  onBtnFusuStart() {
    AudioMgr.instance.playEffect(1661);

    this.spineFusu.node.active = true;

    this.spineFinger.node.active = false;
    this.spineFusu.setAnimation(0, "fusu2", true);
    if (this.touchTime <= 0) {
      this.touchTime = 0.01;
    }
    this.isTouch = true;

    // 按钮动画
    this.updateBtnStatus(true);
  }

  onBtnFusuEnd() {
    this.isTouch = false;

    this.updateBtnStatus(false);
  }

  onBtnFusuCancel() {
    this.isTouch = false;

    this.updateBtnStatus(false);
  }

  startFusu() {
    AudioMgr.instance.playEffect(1662);
    this.node.getChildByName("btn_next").active = false;
    setTimeout(() => {
      this.node.getChildByName("btn_next").active = true;

      this.idx++;
      this.onBtnNext();
    }, 5000);

    // 隐藏手指
    this.spineFinger.node.active = false;
    // 隐藏按钮
    this.btnFusu.active = false;

    // 对话框
    this.nodeLayoutWord1.active = false;
    this.nodeLayoutWord2.active = false;

    // 进度条隐藏
    this.progressBar.node.active = false;

    // 妲己播放复苏动画
    let spineSudaji = this.roleSudaji.getChildByName("spine_sudaji").getComponent(sp.Skeleton);
    spineSudaji.setAnimation(0, "boss_fusu", false);

    spineSudaji.setEventListener((animation, event) => {
      if (event["data"].name === "atk") {
        spineSudaji.setEventListener(null);

        // 绿光出现
        this.spineFusu.node.active = true;
        this.spineFusu.setAnimation(0, "fusu", false);

        // 玩家开始复苏
        let spinePlayer = this.rolePlayer.getChildByName("spine_player").getComponent(sp.Skeleton);
        spinePlayer.setAnimation(0, "fusu2", false);

        spinePlayer.setCompleteListener(() => {
          spinePlayer.setCompleteListener(null);
          spinePlayer.setAnimation(0, "boss_idle", true);
        });
      }
    });

    spineSudaji.setCompleteListener(() => {
      spineSudaji.setCompleteListener(null);
      spineSudaji.setAnimation(0, "boss_idle", true);
    });
  }

  onBtnNext() {
    let configGuideV2 = this.configGuideV2List[this.idx];
    if (!configGuideV2) {
      return;
    }

    if (configGuideV2.id === 3) {
      // 复苏事件
      this.btnFusu.active = true;
      this.spineFinger.node.active = true;

      let spineFusu = this.btnFusu.getChildByName("spine_fusu").getComponent(sp.Skeleton);
      spineFusu.setAnimation(0, "anniu", false);
      setTimeout(() => {
        spineFusu.paused = true;
      }, 1);

      this.progressBar.node.active = true;
    } else if (configGuideV2.id === 6) {
      // 主角转向
      let nodeSpinePlayer = this.rolePlayer.getChildByName("spine_player");
      let scale = nodeSpinePlayer.getScale();
      nodeSpinePlayer.setScale(scale.x * -1, scale.y);

      this.idx++;
      this.onBtnNext();
    } else if (configGuideV2.id === 10) {
      this.nodeBtnGo.active = true;
      this.spineFinger.node.active = true;
    } else if (configGuideV2.type === 1) {
      // 对话运行方式
      if (this.talkStatus === TalkStatusEnum.Start) {
        if (configGuideV2.position === "1") {
          this.nodeLayoutWord1.active = true;
          this.lblWord1.string = "";
        } else if (configGuideV2.position === "2") {
          this.nodeLayoutWord2.active = true;
          this.lblWord2.string = "";
        }

        this.talkStatus = TalkStatusEnum.Playing;
      } else if (this.talkStatus === TalkStatusEnum.End) {
        this.idx++;

        this.talkStatus = TalkStatusEnum.Start;
        this.onBtnNext();
      } else if (this.talkStatus === TalkStatusEnum.Playing) {
        if (configGuideV2.position === "1") {
          this.lblWord1.string = configGuideV2.args;
        } else if (configGuideV2.position === "2") {
          this.lblWord2.string = configGuideV2.args;
        }

        this.talkStatus = TalkStatusEnum.End;
      }
    }
  }

  onBtnRestart() {
    this.btnFusu.getChildByName("spine_fusu").getComponent(sp.Skeleton).paused = true;

    this.rolePlayer.getChildByName("spine_player").getComponent(sp.Skeleton).setAnimation(0, "fusu1", true);
    this.roleSudaji.getChildByName("spine_sudaji").getComponent(sp.Skeleton).setAnimation(0, "boss_idle", true);

    tween(this.roleSudaji.getChildByName("spine_sudaji"))
      .by(0.8, { position: v3(0, 15, 0) }, { easing: "sineInOut" })
      .by(0.8, { position: v3(0, -15, 0) }, { easing: "sineInOut" })
      .union()
      .repeatForever()
      .start();

    this.spineFusu.node.active = false;

    this.btnFusu.on(Node.EventType.TOUCH_START, this.onBtnFusuStart, this);
    this.btnFusu.on(Node.EventType.TOUCH_END, this.onBtnFusuEnd, this);
    this.btnFusu.on(Node.EventType.TOUCH_CANCEL, this.onBtnFusuCancel, this);

    this.configGuideV2List = JsonMgr.instance.getConfigGuideV2List(1, 10);
    this.nodeLayoutWord1.active = false;
    this.nodeLayoutWord2.active = false;

    this.progressBar.progress = 0;
    this.progressBar.node.active = false;

    this.btnFusu.active = false;

    this.lblWord1.string = "";
    this.lblWord2.string = "";

    // 开始对话
    this.idx = 0;
    this.onBtnNext();
    this.touchTime = 0;
    this.nodeBtnGo.active = false;
    this.spineFinger.node.active = false;
  }

  onBtnGo() {
    this.getNode("btn_go").active = false;
    // 场景跳转
    TipsMgr.topRouteCtrl.show(GuideRouteEnum.TopLoading, () => {
      return new Promise(async (reslove) => {
        TipsMgr.topRouteCtrl.closeByName(GuideRouteEnum.TopFusu);
        // UIMgr.instance.showPage(CityRouteName.UIGameMap, null, null, () => {
        //   PlayerModule.api.updateGuideId(11);

        //   reslove(true);
        // });

        TipsMgr.topRouteCtrl.show(
          GuideRouteEnum.TopQiYunGame,
          null,
          () => {
            UIMgr.instance.showPage(CityRouteName.UIGameMap, null, null, () => {
              PlayerModule.api.updateGuideId(11);

              // UI界面
              UIMgr.instance.addToUIRoot(BundleEnum.BUNDLE_G_COMMON_MAIN, "prefab/ui/UIMain", "UIMain", () => {
                PlayerModule.api.getOfflineEnergy();
              });
            });
          },
          () => {
            reslove(true);
          }
        );
      });
    });
    this.idx++;
  }
}
