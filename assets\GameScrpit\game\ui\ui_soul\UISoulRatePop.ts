import { _decorator, Label } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { ConfigSoulRecord } from "../../bundleDefine/bundle_soul_define";
import { JsonMgr } from "../../mgr/JsonMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { DialogZero } from "../../GameDefine";
import Formate from "../../../lib/utils/Formate";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
const { ccclass, property } = _decorator;

@ccclass("UISoulRatePop")
export class UISoulRatePop extends UINode {
  protected _openAct: boolean = true;
  protected _isAddToBottom: boolean = true;
  public zOrder(): number {
    return DialogZero.UISoulRatePop;
  }
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_SOUL}?prefab/ui/UISoulRatePop`;
  }
  protected dependOn(): BundleEnum[] {
    return [BundleEnum.BUNDLE_COMMON_UI];
  }

  _freeRateList: number[] = [0, 0, 0, 0, 0];
  _costRateList: number[] = [0, 0, 0, 0, 0];

  protected onEvtShow(): void {
    let list = Object.keys(JsonMgr.instance.jsonList.c_soul).map(Number);
    let freeRefreshRate = 0;
    let costRefreshRate = 0;

    for (let i = 0; i < list.length; i++) {
      let configSoul: ConfigSoulRecord = JsonMgr.instance.jsonList.c_soul[list[i]];
      this._freeRateList[configSoul.color - 1] += configSoul.freeRefreshRate;
      freeRefreshRate += configSoul.freeRefreshRate;

      this._costRateList[configSoul.color - 1] += configSoul.costRefreshRate;
      costRefreshRate += configSoul.costRefreshRate;
    }

    // for (let i = 0; i < list.length; i++) {
    //   let configSoul: ConfigSoulRecord = JsonMgr.instance.jsonList.c_soul[list[i]];
    //   if (this._freeRateList.indexOf(configSoul.freeRefreshRate) == -1) {
    //     this._freeRateList[configSoul.color - 1] = configSoul.freeRefreshRate;
    //     freeRefreshRate += configSoul.freeRefreshRate;
    //   }

    //   if (this._costRateList.indexOf(configSoul.costRefreshRate) == -1) {
    //     this._costRateList[configSoul.color - 1] = configSoul.costRefreshRate;
    //     costRefreshRate += configSoul.costRefreshRate;
    //   }
    // }
    for (let i = 0; i < this.getNode("free_lay").children.length; i++) {
      let num = (this._freeRateList[i] * 100) / freeRefreshRate;
      let str = Formate.formatDecimal(num, 2);
      this.getNode("free_lay").children[i].getChildByName("Label").getComponent(Label).string = `${str}%`;
    }

    for (let j = 0; j < this.getNode("cost_lay").children.length; j++) {
      let num = (this._costRateList[j] * 100) / costRefreshRate;
      let str = Formate.formatDecimal(num, 2);
      this.getNode("cost_lay").children[j].getChildByName("Label").getComponent(Label).string = `${str}%`;
    }
  }

  private on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
}
