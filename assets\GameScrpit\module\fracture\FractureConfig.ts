import { IConfigFracture_draw } from "../../game/JsonDefine";
import { JsonMgr } from "../../game/mgr/JsonMgr";
import { persistent } from "../../lib/decorators/persistent";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import { TimeUtils } from "../../lib/utils/TimeUtils";
import { ActivityModule } from "../activity/ActivityModule";
import { ShopConfig } from "../club/ClubConfig";
import { ActivityRedeemItem, FRACTURE_ACTIVITYID, FractureActivityConfig } from "./FractureConstant";
/**
 * 本地配置文件数据结构
 * 数据基础处理，过滤等，按id取
 */
const log = Logger.getLoger(LOG_LEVEL.DEBUG);
export class FractureConfig {
  @persistent
  private _fracture_skip_tips_date: string;
  private _shopList: ShopConfig[] = [];

  public set fracture_skip_tips(value: boolean) {
    let timeNow = "";
    if (value) {
      timeNow = TimeUtils.formatTimestamp(Date.now(), "YYYY_MM_DD");
    }
    this._fracture_skip_tips_date = timeNow;
  }
  public get fracture_skip_tips(): boolean {
    let timeNow = TimeUtils.formatTimestamp(Date.now(), "YYYY_MM_DD");
    return this._fracture_skip_tips_date === timeNow;
  }

  public getFractureShopConfig(): ActivityRedeemItem[] {
    let activity = ActivityModule.data.allActivityConfig[FRACTURE_ACTIVITYID] as FractureActivityConfig;
    return activity.redeemList[1];
    // if (this._shopList.length == 0) {
    //   Object.values(JsonMgr.instance.jsonList.c_shop).forEach((val: ShopConfig) => {
    //     if (val.type == 11) {
    //       this._shopList.push(val);
    //     }
    //   });
    // }
    // return this._shopList;
  }

  /**
   * 获取抽奖楼层配置
   * @param floor 楼层
   * @returns/
   */
  public getFractureDrawConfig(floor: number): IConfigFracture_draw[] {
    let drawList = Object.values(JsonMgr.instance.jsonList.c_fracture_draw);
    log.log("drawList", drawList);
    let list = drawList.filter((val: IConfigFracture_draw) => {
      return val.floor == floor;
    });
    return list;
  }

  public getSearchItemCost(floor: number): number[] {
    let fractureList = Object.values(JsonMgr.instance.jsonList.c_fracture);
    let currentList = fractureList.filter((val) => {
      return val.floor == floor;
    });
    if (currentList.length > 0) {
      return currentList[0].cost01;
    }
    return [];
  }
}
