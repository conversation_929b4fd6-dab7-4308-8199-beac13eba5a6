import { GameData } from "../../game/GameData";
import { JsonMgr } from "../../game/mgr/JsonMgr";
import { UISiteDetail } from "../../game/ui/ui_travel/UISiteDetail";
import { UITravel } from "../../game/ui/ui_travel/UITravel";
import { UITravelDialogue } from "../../game/ui/ui_travel/UITravelDialogue";
import { UITravelFriend } from "../../game/ui/ui_travel/UITravelFriend";
import { UITravelGet } from "../../game/ui/ui_travel/UITravelGet";

import data from "../../lib/data/data";
import TickerMgr from "../../lib/ticker/TickerMgr";
import { Recording, RecordingMap } from "../../lib/ui/recordingMap";
import { TimeUtils } from "../../lib/utils/TimeUtils";
import { TravelApi } from "./TravelApi";
import { TravelData } from "./TravelData";
import { TravelService } from "./TravelService";

export class TravelModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): TravelModule {
    if (!GameData.instance.TravelModule) {
      GameData.instance.TravelModule = new TravelModule();
      GameData.instance.TravelModule.onViewLoad();
    }
    return GameData.instance.TravelModule;
  }
  private _data = new TravelData();
  private _api = new TravelApi();
  private _service = new TravelService();

  public static get data() {
    return this.instance._data;
  }
  public static get api() {
    return this.instance._api;
  }
  protected saveKey(): string {
    return this.constructor.name;
  }
  public init(data?: any, completedCallback?: Function) {
    this._data = new TravelData();
    this._api = new TravelApi();

    // 初始化模块
    TravelModule.api.getTravelInfo((data) => {
      TickerMgr.setInterval(1, this.recover.bind(this), false);
      completedCallback && completedCallback();
    });

    this._service.init();
  }

  private recover() {
    if (!TravelModule.data) {
      return;
    }
    let c_travle = JsonMgr.instance.jsonList.c_travle;
    let renew = c_travle[1].renew;
    let newTime = TravelModule.data.lastUpdateTime + renew * 1000;
    let chaTime = (newTime - TimeUtils.serverTime) / 1000;

    if (chaTime <= 0) {
      TravelModule.api.getTravelInfo((data) => {});
    }
  }

  protected onViewLoad(): void {
    let data = new Recording();
    data = {
      node: UITravel,
      uiName: "UITravel",
      keep: false,
      relevanceUIList: [],
    };
    RecordingMap.instance.addRecording(data.uiName, data);

    data = {
      node: UITravelFriend,
      uiName: "UITravelFriend",
      keep: false,
      relevanceUIList: [],
    };
    RecordingMap.instance.addRecording(data.uiName, data);

    data = new Recording();
    data = {
      node: UITravelDialogue,
      uiName: "UITravelDialogue",
      keep: false,
      relevanceUIList: [],
    };
    RecordingMap.instance.addRecording(data.uiName, data);

    data = new Recording();
    data = {
      node: UITravelGet,
      uiName: "UITravelGet",
      keep: false,
      relevanceUIList: [],
    };
    RecordingMap.instance.addRecording(data.uiName, data);

    data = new Recording();
    data = {
      node: UISiteDetail,
      uiName: "UISiteDetail",
      keep: false,
      relevanceUIList: [],
    };
    RecordingMap.instance.addRecording(data.uiName, data);
  }
}
