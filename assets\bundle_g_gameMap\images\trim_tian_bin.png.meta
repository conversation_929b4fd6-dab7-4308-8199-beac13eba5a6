{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "5618b2fd-ea2c-4636-b1e5-c178645bd1c4", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "5618b2fd-ea2c-4636-b1e5-c178645bd1c4@6c48a", "displayName": "trim_tian_bin", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "5618b2fd-ea2c-4636-b1e5-c178645bd1c4", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "5618b2fd-ea2c-4636-b1e5-c178645bd1c4@f9941", "displayName": "trim_tian_bin", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 441, "height": 116, "rawWidth": 441, "rawHeight": 116, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-220.5, -58, 0, 220.5, -58, 0, -220.5, 58, 0, 220.5, 58, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 116, 441, 116, 0, 0, 441, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-220.5, -58, 0], "maxPos": [220.5, 58, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "5618b2fd-ea2c-4636-b1e5-c178645bd1c4@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "5618b2fd-ea2c-4636-b1e5-c178645bd1c4@6c48a"}}