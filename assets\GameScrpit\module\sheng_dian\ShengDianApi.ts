import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ApiHandlerFail, ApiHandlerSuccess } from "../../game/mgr/ApiHandler";
import { ShengDianCmd } from "../../game/net/cmd/CmdData";
import { ActivityTakeResponse, TempleLikeResponse, TempleMessage } from "../../game/net/protocol/Activity";
import { LongValue } from "../../game/net/protocol/ExternalMessage";
import { ShengDianModule } from "./ShengDianModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class ShengDianApi {
  // 接口方法，数据模块在这里改好，然后通知上层，下面这个是个例子
  // public improveSkill(friendId: number, rank: number, consumeitem: boolean, success?: ApiHandlerSuccess) {
  //   let data: FriendCitySkillRequest = {
  //     friendId: friendId,
  //     rank: rank,
  //     consumeItem: consumeitem,
  //   };
  //   log.log("apiImproveSkill", friendId, rank, consumeitem);
  //   ApiHandler.instance.request(
  //     FriendCitySkillMessage,
  //     FriendSubCmd.improveSkill,
  //     FriendCitySkillRequest.encode(data),
  //     (data: FriendCitySkillMessage) => {
  //       log.log(data);
  //       FriendModule.data.updateFriendCitySkill(friendId, rank, data);
  //       success && success(data);
  //     },
  //     (errorCode: any, msg: string, data: any) => {
  //       log.log(`${errorCode}`);
  //       log.log(data);
  //     }
  //   );
  // }

  public templeInfo(success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    ApiHandler.instance.request(TempleMessage, ShengDianCmd.templeInfo, null, (data: TempleMessage) => {
      ShengDianModule.data.templeMessage = data;
      success && success(data);
    });
  }

  public testInsertTemplate(success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    ApiHandler.instance.request(LongValue, ShengDianCmd.testInsertTemplate, null, (data: LongValue) => {
      success && success(data);
    });
  }

  public takeTreeReward(index: number, success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    ApiHandler.instance.requestSync(
      ActivityTakeResponse,
      ShengDianCmd.takeTreeReward,
      LongValue.encode({ value: index }),
      (data: ActivityTakeResponse) => {
        ShengDianModule.data.templeMessage.treeTakeList = data.takeList;
        success && success(data);
      }
    );
  }

  public templeLike(success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    ApiHandler.instance.requestSync(TempleLikeResponse, ShengDianCmd.templeLike, null, (data: TempleLikeResponse) => {
      ShengDianModule.data.templeMessage.likeList = data.likeList;
      ShengDianModule.data.templeMessage.miracleRemainMap = data.miracleRemainMap;
      success && success(data);
    });
  }
}
