import { UIDayRecharge } from "../../game/ui/ui_fund/UIDayRecharge";
import { UIFundMain } from "../../game/ui/ui_fund/UIFundMain";
import { UIHdSeven } from "../../game/ui/ui_hd/UIHdSeven";
import { Recording, RecordingMap } from "../../lib/ui/recordingMap";

export enum ActivityRouteItem {
  UIHdSeven = "UIHdSeven",
  UIFundMain = "UIFundMain",
}
export class ActivityRoute {
  rotueTables: Recording[] = [
    {
      node: UIHdSeven,
      uiName: ActivityRouteItem.UIHdSeven,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UIFundMain,
      uiName: ActivityRouteItem.UIFundMain,
      keep: false,
      relevanceUIList: [],
    },
  ];

  public async init() {
    this.rotueTables.forEach((item) => {
      RecordingMap.instance.addRecording(item.uiName, item);
    });
  }
}
