import { _decorator } from "cc";
import { FSMState } from "./FSMState";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.STOP);
const { ccclass, property } = _decorator;

@ccclass("FSMMachine")
export default class FSMMachine {
  private _stateDict: Map<number, FSMState> = new Map<number, FSMState>();
  private _currentState: FSMState;
  private _previousState: FSMState;
  private _board: any;
  public constructor(board) {
    this._previousState = null;
    this._board = board;
  }

  //设置一开始的状态
  public async setBeginState(stateId: number) {
    if (!this._stateDict[stateId]) return;
    if (this._currentState == null) {
      this._currentState = this._stateDict[stateId];
      await this._currentState.onEnter(this._board);
    }
  }

  //添加状态
  public addState(state: FSMState) {
    if (state == null) return;
    if (!this._stateDict[state.getId()]) {
      this._stateDict[state.getId()] = state;
      state.addMachine(this);
    }
  }

  //获取状态
  public getState(id: number): FSMState {
    return this._stateDict[id];
  }

  //转化状态
  public async translateState(id: number, args = null) {
    if (
      this._stateDict[id] &&
      this._currentState &&
      this._currentState.isExistTranslate(id) &&
      this._currentState.extSwitchStateCheck()
    ) {
      this._currentState.onExit(this._board);
      this._previousState = this._currentState;
      this._currentState = this._stateDict[id];
      await this._currentState.onEnter(this._board);
    } else {
      log.error("状态切换失败");
      log.error(
        "当前状态：",
        this._currentState.getId(),
        "目标状态：",
        id,
        "当前状态是否存在目标状态：",
        this._currentState.isExistTranslate(id)
      );
    }
  }

  public update(dt) {
    if (this._currentState) {
      this._currentState.update(this._board, dt);
      this._currentState.translate();
    } else {
    }
  }

  //获取当前状态ID
  public getCurrentStateId(): number {
    if (this._currentState) {
      return this._currentState.getId();
    }
    return -1;
  }
}
