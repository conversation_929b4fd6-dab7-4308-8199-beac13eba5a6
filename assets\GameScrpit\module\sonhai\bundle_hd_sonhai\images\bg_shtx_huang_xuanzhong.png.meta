{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "e9bc75d1-f756-48f0-9816-e39e8296fdfe", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "e9bc75d1-f756-48f0-9816-e39e8296fdfe@6c48a", "displayName": "bg_shtx_huang_xuanzhong", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "e9bc75d1-f756-48f0-9816-e39e8296fdfe", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "e9bc75d1-f756-48f0-9816-e39e8296fdfe@f9941", "displayName": "bg_shtx_huang_xuanzhong", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 188, "height": 251, "rawWidth": 188, "rawHeight": 251, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-94, -125.5, 0, 94, -125.5, 0, -94, 125.5, 0, 94, 125.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 251, 188, 251, 0, 0, 188, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-94, -125.5, 0], "maxPos": [94, 125.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "e9bc75d1-f756-48f0-9816-e39e8296fdfe@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "e9bc75d1-f756-48f0-9816-e39e8296fdfe@6c48a"}}