const { convertExcelToJson } = require("./excel-to-json");
const { GenJsonDefineUtil } = require("./gen-json-define");
const deleteJsonFiles = require("./delete-file");

const path = require("path");
const fs = require("fs");

console.log("Excel转JSON工具 - 快速启动");
console.log("==========================");
console.log("");

const args = process.argv.slice(2);

// 确保输入输出目录存在
const inputDir = args[0]; // "D:/feimeng/design_config/2.0"; // path.join(__dirname, 'input');
const outputDir = "../../assets/bundle_common_json/json"; // path.join(__dirname, 'output');
const outputDefine = "../../assets/GameScrpit/game";
// const inputDir = "./input"; // path.join(__dirname, 'input');
// const outputDir = "./output"; // path.join(__dirname, 'input');
// const outputDefine = "./output";

if (!fs.existsSync(inputDir)) {
  fs.mkdirSync(inputDir, { recursive: true });
  console.log(`已创建输入目录: ${inputDir}`);
}

if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
  console.log(`已创建输出目录:${outputDir}`);
}

// 检查输入目录是否有Excel文件
const files = fs.readdirSync(inputDir);

// 开始转换

(async () => {
  try {
    console.log("删除现有json");
    await deleteJsonFiles(outputDir);

    console.log("\n开始转换excel to json...");
    const results = await convertExcelToJson(inputDir, outputDir);

    console.log("转换完成！");

    // 显示详细结果
    results.forEach((result) => {
      if (result.success && !result.skipped) {
        // console.log(`✓ ${result.filename} -> ${result.outputPath}`);
      } else if (result.skipped) {
        // console.log(`- ${result.filename} (跳过: ${result.reason})`);
      } else {
        console.log(`✗ ${result.filename} (错误: ${result.error})`);
      }
    });

    console.log("\n开始生成定义ts文件");

    let genJsonDefine = new GenJsonDefineUtil(inputDir);
    const rsList = await genJsonDefine.readAllFilesRowData(2);

    fs.writeFileSync(outputDefine + "/JsonDefine.ts", rsList[0], "utf8");
    fs.writeFileSync(outputDefine + "/JsonConst.ts", rsList[1], "utf8");
  } catch (error) {
    console.error("转换过程中发生错误:", error.message);
  }
})();
