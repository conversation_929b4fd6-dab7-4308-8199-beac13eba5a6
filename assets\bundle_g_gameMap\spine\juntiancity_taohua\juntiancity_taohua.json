{"skeleton": {"hash": "VobZK9rCWXWB+LCube9dkJS9e7I", "spine": "3.8.75", "x": -128.15, "y": -70.47, "width": 291.52, "height": 120.73, "images": "", "audio": ""}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 4.39, "rotation": 178.43, "x": -26.04, "y": 8.83, "scaleX": 3.2829, "scaleY": 3.2829}, {"name": "bone2", "parent": "bone", "x": -0.35, "y": -3.87}, {"name": "bone3", "parent": "root", "length": 4.62, "rotation": 179.75, "x": -57.14, "y": 0.46, "scaleX": 2.8376, "scaleY": 2.8376}, {"name": "bone4", "parent": "bone3", "x": -0.01, "y": -7.72}, {"name": "bone5", "parent": "root", "length": 4.44, "rotation": -2.06, "x": 75.25, "y": -27.99, "scaleX": 2.956, "scaleY": 2.956}, {"name": "bone6", "parent": "bone5", "x": -0.66, "y": 7.24}, {"name": "bone7", "parent": "root", "length": 4.4, "rotation": -4.96, "x": 87.68, "y": -34.97, "scaleX": 2.9179, "scaleY": 2.9179}, {"name": "bone8", "parent": "bone7", "x": -0.54, "y": 6.92}, {"name": "bone11", "parent": "root", "x": 1.03, "y": -72.11}, {"name": "bone9", "parent": "bone11", "length": 4.65, "rotation": -0.69, "x": 0.6, "y": 3.11}, {"name": "bone10", "parent": "bone9", "x": -2.28, "y": 3.22}, {"name": "bone12", "parent": "root", "x": 1.03, "y": -72.11}, {"name": "bone13", "parent": "bone12", "length": 4.65, "rotation": -0.69, "x": 0.6, "y": 3.11}, {"name": "bone14", "parent": "bone13", "x": -2.28, "y": 3.22}, {"name": "bone16", "parent": "root", "length": 8.76, "rotation": -0.9, "x": 20.49, "y": -1.14, "scaleX": 1.5, "scaleY": 1.5}, {"name": "bone17", "parent": "bone16", "length": 8.19, "rotation": 96.37, "x": -0.57, "y": 2.95}, {"name": "bone18", "parent": "bone17", "length": 10.19, "rotation": -2.4, "x": 8.23}, {"name": "bone19", "parent": "bone18", "length": 5.64, "rotation": 57.53, "x": -0.16, "y": 3.56}, {"name": "bone20", "parent": "bone19", "length": 5.88, "rotation": 3.01, "x": 5.64}, {"name": "bone21", "parent": "bone20", "length": 5.27, "rotation": 5.14, "x": 5.88}, {"name": "bone22", "parent": "bone21", "length": 4.36, "rotation": 4.6, "x": 5.27}, {"name": "bone23", "parent": "bone22", "rotation": 163.35, "x": 3.97, "y": -0.28, "transform": "noRotationOrReflection"}, {"name": "bone24", "parent": "bone23", "rotation": 163.35, "x": -5.13, "y": 17.03, "transform": "onlyTranslation"}], "slots": [{"name": "t2", "bone": "root", "attachment": "t2"}, {"name": "t1", "bone": "root", "attachment": "t1"}, {"name": "r1", "bone": "root", "attachment": "r1"}, {"name": "tuzi", "bone": "bone11", "dark": "5e5d47", "attachment": "tuzi"}, {"name": "tuzi2", "bone": "bone12", "dark": "5e5d47", "attachment": "tuzi"}, {"name": "r2", "bone": "root", "attachment": "r2"}, {"name": "r3", "bone": "bone", "dark": "312b2b", "attachment": "r3"}, {"name": "r4", "bone": "bone5", "dark": "362d2d", "attachment": "r4"}, {"name": "dyl", "bone": "bone16", "attachment": "dyl"}, {"name": "yuxian", "bone": "bone23", "attachment": "yuxian"}], "skins": [{"name": "default", "attachments": {"r2": {"r2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0.5, 0, 0, 1, 0, 1, 0.5], "triangles": [2, 3, 4, 5, 2, 4, 1, 2, 5, 0, 1, 5], "vertices": [2, 7, 2.51, -1.41, 0.99959, 8, 3.05, -8.33, 0.00041, 2, 7, -2.47, -1.84, 0.99986, 8, -1.93, -8.76, 0.00014, 2, 7, -2.99, 4.13, 0.37621, 8, -2.45, -2.78, 0.62379, 1, 8, -2.96, 3.19, 1, 1, 8, 2.02, 3.63, 1, 2, 7, 2, 4.57, 0.39518, 8, 2.54, -2.35, 0.60482], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 0], "width": 30, "height": 72}}, "r3": {"r3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0.5, 0, 0, 1, 0, 1, 0.5], "triangles": [2, 3, 4, 0, 1, 5, 1, 2, 5, 5, 2, 4], "vertices": [1, 1, -3.77, 5.01, 1, 2, 1, 3.23, 5.2, 0.99996, 2, 3.58, 9.08, 4e-05, 2, 1, 3.39, -0.79, 0.91916, 2, 3.74, 3.08, 0.08084, 2, 1, 3.56, -6.79, 0.03687, 2, 3.91, -2.92, 0.96313, 1, 2, -3.09, -3.11, 1, 2, 1, -3.61, -0.98, 0.65145, 2, -3.25, 2.89, 0.34855], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 0], "width": 30, "height": 52}}, "tuzi2": {"tuzi": {"type": "mesh", "uvs": [1, 0.52886, 1, 1, 0.5, 1, 0, 1, 0, 0.52886, 0, 0, 0.5, 0, 1, 0, 0.48247, 0.52186], "triangles": [8, 5, 6, 4, 5, 8, 6, 7, 0, 8, 6, 0, 3, 4, 8, 2, 8, 0, 3, 8, 2, 2, 0, 1], "vertices": [2, 13, 3.96, 2.34, 0.81577, 14, 6.24, -0.88, 0.18423, 1, 13, 4, -1.43, 1, 2, 13, -0.5, -1.48, 0.96977, 14, 1.78, -4.7, 0.03023, 2, 13, -5, -1.53, 0.48865, 14, -2.72, -4.75, 0.51135, 2, 13, -5.04, 2.24, 0.10735, 14, -2.76, -0.99, 0.89265, 1, 14, -2.81, 3.25, 1, 2, 13, -0.59, 6.52, 0.13413, 14, 1.69, 3.3, 0.86587, 2, 13, 3.91, 6.57, 0.47171, 14, 6.19, 3.35, 0.52829, 2, 13, -0.7, 2.34, 0.43115, 14, 1.58, -0.88, 0.56885], "hull": 8, "edges": [2, 4, 4, 6, 10, 12, 12, 14, 6, 8, 8, 10, 2, 0, 0, 14], "width": 9, "height": 8}}, "r4": {"r4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0.5, 0, 0, 1, 0, 1, 0.5], "triangles": [5, 2, 4, 2, 3, 4, 0, 1, 5, 1, 2, 5], "vertices": [1, 5, 3.52, -1.25, 1, 2, 5, -3.48, -1.5, 0.98347, 6, -2.82, -8.74, 0.01653, 2, 5, -3.7, 4.5, 0.35737, 6, -3.03, -2.74, 0.64263, 1, 6, -3.25, 3.25, 1, 2, 5, 3.08, 10.75, 2e-05, 6, 3.75, 3.5, 0.99998, 2, 5, 3.3, 4.75, 0.41853, 6, 3.96, -2.49, 0.58147], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 0], "width": 30, "height": 52}}, "tuzi": {"tuzi": {"type": "mesh", "uvs": [1, 0.52886, 1, 1, 0.5, 1, 0, 1, 0, 0.52886, 0, 0, 0.5, 0, 1, 0, 0.48247, 0.52186], "triangles": [8, 5, 6, 4, 5, 8, 6, 7, 0, 8, 6, 0, 3, 4, 8, 2, 8, 0, 3, 8, 2, 2, 0, 1], "vertices": [2, 10, 3.96, 2.34, 0.81577, 11, 6.24, -0.88, 0.18423, 1, 10, 4, -1.43, 1, 2, 10, -0.5, -1.48, 0.96977, 11, 1.78, -4.7, 0.03023, 2, 10, -5, -1.53, 0.48865, 11, -2.72, -4.75, 0.51135, 2, 10, -5.04, 2.24, 0.10735, 11, -2.76, -0.99, 0.89265, 1, 11, -2.81, 3.25, 1, 2, 10, -0.59, 6.52, 0.13413, 11, 1.69, 3.3, 0.86587, 2, 10, 3.91, 6.57, 0.47171, 11, 6.19, 3.35, 0.52829, 2, 10, -0.7, 2.34, 0.43115, 11, 1.58, -0.88, 0.56885], "hull": 8, "edges": [2, 4, 4, 6, 10, 12, 12, 14, 6, 8, 8, 10, 2, 0, 0, 14], "width": 9, "height": 8}}, "dyl": {"dyl": {"type": "mesh", "uvs": [0, 0.0407, 0.07966, 0.06615, 0.19321, 0.12433, 0.31449, 0.19342, 0.43707, 0.28251, 0.53772, 0.36251, 0.60739, 0.42797, 0.52739, 0.30433, 0.46417, 0.15888, 0.4874, 0, 0.60481, 0, 0.77771, 0, 0.92739, 0, 1, 0.06797, 1, 0.23342, 1, 0.46615, 0.86287, 0.46251, 0.91191, 0.54069, 0.89513, 0.68796, 0.96223, 0.76433, 0.96094, 0.8916, 0.88997, 0.90978, 0.83965, 0.87705, 0.84868, 1, 0.63965, 1, 0.6332, 0.86251, 0.58933, 0.67524, 0.53901, 0.64069, 0.52739, 0.53342, 0.46288, 0.47706, 0.38933, 0.42433, 0.28611, 0.35524, 0.17901, 0.28433, 0.08224, 0.23706, 0, 0.19161, 0, 0.11706, 0.08224, 0.15342, 0.19191, 0.20979, 0.30933, 0.27888, 0.40998, 0.34615, 0.50159, 0.42615, 0.57901, 0.48251], "triangles": [34, 36, 33, 33, 36, 37, 34, 35, 36, 35, 1, 36, 36, 1, 2, 35, 0, 1, 32, 37, 38, 32, 33, 37, 37, 2, 3, 37, 36, 2, 29, 30, 39, 39, 4, 40, 30, 31, 39, 31, 38, 39, 31, 32, 38, 39, 38, 4, 38, 3, 4, 38, 37, 3, 16, 26, 6, 27, 41, 26, 26, 41, 6, 27, 28, 41, 29, 40, 28, 28, 40, 41, 40, 5, 41, 41, 5, 6, 40, 29, 39, 40, 4, 5, 16, 14, 15, 6, 11, 16, 14, 11, 12, 14, 16, 11, 6, 7, 10, 6, 10, 11, 7, 8, 10, 10, 8, 9, 12, 13, 14, 24, 22, 23, 24, 25, 22, 20, 21, 19, 19, 21, 22, 22, 18, 19, 22, 25, 18, 25, 26, 18, 26, 16, 18, 18, 16, 17], "vertices": [1, 21, 4.45, -1.68, 1, 2, 20, 7.34, -1.69, 0.00967, 21, 1.93, -1.85, 0.99033, 1, 20, 3.6, -1.77, 1, 2, 19, 5.58, -1.75, 0.69368, 20, -0.46, -1.72, 0.30632, 2, 18, 7.03, -1.61, 0.05299, 19, 1.3, -1.69, 0.94701, 2, 17, 3.05, 5.6, 0.00012, 18, 3.45, -1.61, 0.99988, 2, 17, 1.5, 3.52, 0.30231, 18, 0.86, -1.42, 0.69769, 2, 17, 4.35, 5.85, 0.86296, 18, 4.35, -2.57, 0.13704, 2, 17, 7.65, 7.64, 0.95545, 18, 7.63, -4.4, 0.04455, 2, 17, 11.1, 6.73, 0.98047, 18, 8.72, -7.8, 0.01953, 2, 17, 10.9, 3.1, 0.99222, 18, 5.55, -9.58, 0.00778, 1, 17, 10.62, -2.25, 1, 1, 17, 10.37, -6.89, 1, 1, 17, 8.75, -9.05, 1, 2, 16, 12.97, -9.07, 0.00683, 17, 5.12, -8.86, 0.99317, 2, 16, 7.87, -8.58, 0.06, 17, 0.01, -8.58, 0.94, 2, 16, 8.36, -4.36, 0.37174, 17, 0.31, -4.34, 0.62826, 2, 16, 6.5, -5.71, 0.77561, 17, -1.49, -5.77, 0.22439, 2, 16, 3.33, -4.88, 0.97404, 17, -4.69, -5.08, 0.02596, 2, 16, 1.46, -6.79, 1, 17, -6.48, -7.06, 0, 1, 16, -1.33, -6.48, 1, 1, 16, -1.52, -4.26, 1, 1, 16, -0.65, -2.77, 1, 1, 16, -3.37, -2.79, 1, 1, 16, -2.75, 3.66, 1, 2, 16, 0.28, 3.57, 0.96344, 18, -4.53, 6.52, 0.03656, 2, 16, 4.51, 4.53, 0.45304, 18, -1.33, 3.6, 0.54696, 2, 16, 5.41, 6.01, 0.18546, 18, 0.41, 3.7, 0.81454, 2, 16, 7.8, 6.14, 0.0148, 18, 1.88, 1.82, 0.9852, 2, 18, 4.23, 1.72, 0.93523, 19, -1.32, 1.79, 0.06477, 2, 18, 6.79, 1.83, 0.08403, 19, 1.24, 1.77, 0.91597, 2, 19, 4.78, 1.83, 0.89732, 20, -0.93, 1.92, 0.10268, 2, 19, 8.45, 1.91, 0.00051, 20, 2.73, 1.67, 0.99949, 2, 20, 5.9, 1.79, 0.24204, 21, 0.77, 1.73, 0.75796, 2, 20, 8.64, 1.78, 0, 21, 3.5, 1.5, 1, 1, 21, 3.97, -0.07, 1, 1, 21, 1.3, -0.03, 1, 1, 20, 2.95, 0, 1, 1, 19, 4.88, 0, 1, 1, 19, 1.43, -0.06, 1, 1, 18, 3.73, 0.16, 1, 2, 16, 8.76, 4.44, 0.00019, 18, 1.03, 0.06, 0.99981], "hull": 36, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 0, 70], "width": 31, "height": 22}}, "yuxian": {"yuxian": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [1, 23, -0.15, 0.01, 1, 1, 23, 0.08, 0.08, 1, 1, 22, 0.11, -0.13, 1, 1, 22, -0.12, -0.2, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 1, "height": 18}}, "t1": {"t1": {"x": -83.94, "y": 21.4, "scaleX": 1.2282, "scaleY": 1.2282, "width": 72, "height": 47}}, "t2": {"t2": {"x": 114.43, "y": -21.15, "scaleX": 1.3408, "scaleY": 1.3408, "width": 73, "height": 39}}, "r1": {"r1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0.5, 0, 0, 1, 0, 1, 0.5], "triangles": [2, 3, 4, 5, 2, 4, 1, 2, 5, 0, 1, 5], "vertices": [2, 3, -2.77, 1.11, 0.99263, 4, -2.77, 8.83, 0.00737, 2, 3, 3.23, 1.14, 1, 4, 3.23, 8.86, 0, 2, 3, 3.25, -4.86, 0.40068, 4, 3.26, 2.86, 0.59932, 1, 4, 3.28, -3.14, 1, 1, 4, -2.72, -3.17, 1, 2, 3, -2.75, -4.89, 0.36496, 4, -2.74, 2.83, 0.63504], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 0], "width": 30, "height": 60}}}}], "animations": {"animation": {"slots": {"r3": {"twoColor": [{"light": "ffffff00", "dark": "312b2b"}, {"time": 0.3333, "light": "ffffffff", "dark": "312b2b", "curve": "stepped"}, {"time": 7.4333, "light": "ffffffff", "dark": "312b2b"}, {"time": 7.7333, "light": "ffffff00", "dark": "312b2b"}]}, "yuxian": {"attachment": [{"name": null}]}, "tuzi": {"twoColor": [{"light": "ffffff00", "dark": "5e5d47"}, {"time": 0.3333, "light": "ffffffff", "dark": "5e5d47", "curve": "stepped"}, {"time": 3.3333, "light": "ffffffff", "dark": "5e5d47"}, {"time": 3.6667, "light": "ffffff00", "dark": "5e5d47"}]}, "r4": {"twoColor": [{"light": "ffffffff", "dark": "302b2b", "curve": "stepped"}, {"time": 3.4, "light": "ffffffff", "dark": "302b2b"}, {"time": 3.7333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 3.7667, "light": "ffffff00", "dark": "000000"}, {"time": 4.1, "light": "ffffffff", "dark": "302b2b"}]}, "tuzi2": {"twoColor": [{"time": 5.9, "light": "ffffffff", "dark": "5e5d47"}, {"time": 6.2667, "light": "ffffff00", "dark": "5e5d47", "curve": "stepped"}, {"time": 7.7333, "light": "ffffff00", "dark": "5e5d47"}, {"time": 8.0667, "light": "ffffffff", "dark": "5e5d47"}]}, "dyl": {"attachment": [{"name": null}]}}, "bones": {"bone9": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "y": 2.26, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "y": 2.26, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": 2.26, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "curve": "stepped"}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "y": 2.26, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "y": 2.26, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "y": 2.26, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "curve": 0.25, "c3": 0.75}, {"time": 3.0667, "y": 2.26, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "y": 2.26, "curve": 0.25, "c3": 0.75}, {"time": 3.6667}]}, "bone10": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -1.34, "y": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -0.42, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -1.34, "y": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": -0.42, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": -1.34, "y": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "x": -0.42, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "curve": "stepped"}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": -1.34, "y": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "x": -0.42, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": -1.34, "y": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "x": -0.42, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "x": -1.34, "y": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "x": -0.42, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "x": -1.34, "y": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": -0.42, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "x": -1.34, "y": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "x": -0.42, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 3.6667}]}, "bone11": {"translate": [{"x": 45.59, "y": -10.43}, {"time": 1, "x": 8.82, "y": -24.69, "curve": "stepped"}, {"time": 1.6667, "x": 8.82, "y": -24.69}, {"time": 2.4333, "x": -19.38, "y": -35.63}, {"time": 3.6333, "x": -80.24, "y": -40.8}]}, "bone4": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 0.48, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.48, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.48, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 0.48, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 0.48, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 0.48, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 0.48, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": 0.48, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "x": 0.48, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": 0.48, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 0.48, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 0.48, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "x": 0.48, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 0.48, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "x": 0.48, "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": 0.48, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": 0.48, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "x": 0.48, "curve": 0.25, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "x": 0.48, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "x": 0.48, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "x": 0.48, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "x": 0.48, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "x": 0.48, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": 0.48, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "bone3": {"translate": [{"x": -23.9, "y": 7.8}]}, "bone": {"translate": [{"x": -67.04, "y": 37.76}, {"time": 5.1, "x": -16.16, "y": 13.13}, {"time": 6.2333, "x": -16.16, "y": 2.18}, {"time": 7.7333, "x": -37.56, "y": -0.96}], "scale": [{"time": 5.0667, "curve": "stepped"}, {"time": 5.1, "x": -1}]}, "bone2": {"translate": [{}, {"time": 0.1667, "x": -0.52, "y": -0.01}, {"time": 0.3333}, {"time": 0.5, "x": -0.52, "y": -0.01}, {"time": 0.6667}, {"time": 0.8333, "x": -0.52, "y": -0.01}, {"time": 1}, {"time": 1.1667, "x": -0.52, "y": -0.01}, {"time": 1.3333}, {"time": 1.5, "x": -0.52, "y": -0.01}, {"time": 1.6667}, {"time": 1.8333, "x": -0.52, "y": -0.01}, {"time": 2}, {"time": 2.1667, "x": -0.52, "y": -0.01}, {"time": 2.3333}, {"time": 2.5, "x": -0.52, "y": -0.01}, {"time": 2.6667}, {"time": 2.8333, "x": -0.52, "y": -0.01}, {"time": 3}, {"time": 3.1667, "x": -0.52, "y": -0.01}, {"time": 3.3333}, {"time": 3.5, "x": -0.52, "y": -0.01}, {"time": 3.6667}, {"time": 3.8333, "x": -0.52, "y": -0.01}, {"time": 4}, {"time": 4.1667, "x": -0.52, "y": -0.01}, {"time": 4.3333}, {"time": 4.5, "x": -0.52, "y": -0.01}, {"time": 4.6667}, {"time": 4.8333, "x": -0.52, "y": -0.01}, {"time": 5}, {"time": 5.1667, "x": -0.52, "y": -0.01}, {"time": 5.3333}, {"time": 5.5, "x": -0.52, "y": -0.01}, {"time": 5.6667}, {"time": 5.8333, "x": -0.52, "y": -0.01}, {"time": 6}, {"time": 6.1667, "x": -0.52, "y": -0.01}, {"time": 6.3333}, {"time": 6.5, "x": -0.52, "y": -0.01}, {"time": 6.6667}, {"time": 6.8333, "x": -0.52, "y": -0.01}, {"time": 7}, {"time": 7.1667, "x": -0.52, "y": -0.01}, {"time": 7.3333}, {"time": 7.5, "x": -0.52, "y": -0.01}, {"time": 7.6667}, {"time": 7.8333, "x": -0.52, "y": -0.01}, {"time": 8}]}, "bone7": {"translate": [{"x": 14.87}, {"time": 1.4667, "x": 13.67}, {"time": 7.9667, "x": 14.87}]}, "bone8": {"translate": [{}, {"time": 0.1667, "x": 0.38, "y": 0.03}, {"time": 0.3333}, {"time": 0.5, "x": 0.38, "y": 0.03}, {"time": 0.6667}, {"time": 0.8333, "x": 0.38, "y": 0.03}, {"time": 1}, {"time": 1.1667, "x": 0.38, "y": 0.03}, {"time": 1.3333}, {"time": 1.5, "x": 0.38, "y": 0.03}, {"time": 1.6667}, {"time": 1.8333, "x": 0.38, "y": 0.03}, {"time": 2}, {"time": 2.1667, "x": 0.38, "y": 0.03}, {"time": 2.3333}, {"time": 2.5, "x": 0.38, "y": 0.03}, {"time": 2.6667}, {"time": 2.8333, "x": 0.38, "y": 0.03}, {"time": 3}, {"time": 3.1667, "x": 0.38, "y": 0.03}, {"time": 3.3333}, {"time": 3.5, "x": 0.38, "y": 0.03}, {"time": 3.6667}, {"time": 3.8333, "x": 0.38, "y": 0.03}, {"time": 4}, {"time": 4.1667, "x": 0.38, "y": 0.03}, {"time": 4.3333}, {"time": 4.5, "x": 0.38, "y": 0.03}, {"time": 4.6667}, {"time": 4.8333, "x": 0.38, "y": 0.03}, {"time": 5}, {"time": 5.1667, "x": 0.38, "y": 0.03}, {"time": 5.3333}, {"time": 5.5, "x": 0.38, "y": 0.03}, {"time": 5.6667}, {"time": 5.8333, "x": 0.38, "y": 0.03}, {"time": 6}, {"time": 6.1667, "x": 0.38, "y": 0.03}, {"time": 6.3333}, {"time": 6.5, "x": 0.38, "y": 0.03}, {"time": 6.6667}, {"time": 6.8333, "x": 0.38, "y": 0.03}, {"time": 7}, {"time": 7.1667, "x": 0.38, "y": 0.03}, {"time": 7.3333}, {"time": 7.5, "x": 0.38, "y": 0.03}, {"time": 7.6667}, {"time": 7.8333, "x": 0.38, "y": 0.03}, {"time": 8}]}, "bone5": {"rotate": [{"angle": 4.59}], "translate": [{"x": 3.32, "y": -7.71}, {"time": 0.1, "x": 5.53, "y": -6.98, "curve": "stepped"}, {"time": 1.4, "x": 5.53, "y": -6.98}, {"time": 2.5667, "x": 19.09, "y": 14.86}, {"time": 3.7, "x": 42.35, "y": 27.67, "curve": "stepped"}, {"time": 3.7333, "x": -135.9, "y": -53.25}, {"time": 10.0333, "x": 3.32, "y": -7.71}], "scale": [{"x": -1}]}, "bone6": {"translate": [{}, {"time": 0.3333, "x": 0.48, "y": 0.02}, {"time": 0.6667}, {"time": 1, "x": 0.48, "y": 0.02}, {"time": 1.3333}, {"time": 1.6667, "x": 0.48, "y": 0.02}, {"time": 2}, {"time": 2.3333, "x": 0.48, "y": 0.02}, {"time": 2.6667}, {"time": 3, "x": 0.48, "y": 0.02}, {"time": 3.3333}, {"time": 3.6667, "x": 0.48, "y": 0.02}, {"time": 4}, {"time": 4.3333, "x": 0.48, "y": 0.02}, {"time": 4.6667}, {"time": 5, "x": 0.48, "y": 0.02}, {"time": 5.3333}, {"time": 5.6667, "x": 0.48, "y": 0.02}, {"time": 6}, {"time": 6.3333, "x": 0.48, "y": 0.02}, {"time": 6.6667}, {"time": 7, "x": 0.48, "y": 0.02}, {"time": 7.3333}, {"time": 7.6667, "x": 0.48, "y": 0.02}, {"time": 8}, {"time": 8.3333, "x": 0.48, "y": 0.02}, {"time": 8.6667}, {"time": 9, "x": 0.48, "y": 0.02}, {"time": 9.3333}, {"time": 9.6667, "x": 0.48, "y": 0.02}, {"time": 10}]}, "bone12": {"translate": [{"x": -59.65, "y": 45.58}, {"time": 1.0333, "x": -30.31, "y": 45.58, "curve": "stepped"}, {"time": 2, "x": -30.31, "y": 45.58}, {"time": 2.8, "x": -30.31, "y": 35.92, "curve": "stepped"}, {"time": 3.3667, "x": -30.31, "y": 35.92}, {"time": 6.2667, "x": -97.33, "y": 3.4}, {"time": 6.3, "x": -124.95, "y": 45.58, "curve": "stepped"}, {"time": 7.7333, "x": -124.95, "y": 45.58}, {"time": 10.0333, "x": -59.65, "y": 45.58}], "scale": [{"x": -1, "curve": "stepped"}, {"time": 2, "x": -1, "curve": "stepped"}, {"time": 2.0333, "curve": "stepped"}, {"time": 6.2667}, {"time": 6.3, "x": -1}]}, "bone14": {"translate": [{"x": -0.42, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -1.34, "y": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -0.42, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -1.34, "y": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": -0.42, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": "stepped"}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "x": -1.34, "y": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "x": -0.42, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": -1.34, "y": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "x": -0.42, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "curve": "stepped"}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "x": -1.34, "y": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": -0.42, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 3.7667, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "x": -1.34, "y": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "x": -0.42, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "x": -1.34, "y": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 4.4667, "x": -0.42, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 4.5667, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": -1.34, "y": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "x": -0.42, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 4.9667, "curve": 0.25, "c3": 0.75}, {"time": 5.0667, "x": -1.34, "y": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "x": -0.42, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 5.3667, "curve": 0.25, "c3": 0.75}, {"time": 5.4667, "x": -1.34, "y": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": -0.42, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 5.7667}, {"time": 5.8667, "x": -1.34, "y": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 6.0667, "x": -0.42, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "curve": "stepped"}, {"time": 7.7333}, {"time": 7.8333, "x": -1.34, "y": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 8.0333, "x": -0.42, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 8.1333, "curve": 0.25, "c3": 0.75}, {"time": 8.2333, "x": -1.34, "y": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 8.4333, "x": -0.42, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "curve": 0.25, "c3": 0.75}, {"time": 8.6333, "x": -1.34, "y": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 8.8333, "x": -0.42, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 8.9333, "curve": 0.25, "c3": 0.75}, {"time": 9.0333, "x": -1.34, "y": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 9.2333, "x": -0.42, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "curve": 0.25, "c3": 0.75}, {"time": 9.4333, "x": -1.34, "y": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 9.6333, "x": -0.42, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 9.7333, "curve": 0.25, "c3": 0.75}, {"time": 9.8333, "x": -1.34, "y": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 10.0333, "x": -0.42, "y": -1.2}]}, "bone13": {"translate": [{"y": 1.13, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "y": 2.26, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "y": 2.26, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": "stepped"}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "y": 2.26, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "y": 2.26, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "curve": "stepped"}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "y": 2.26, "curve": 0.25, "c3": 0.75}, {"time": 3.7667, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "y": 2.26, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.3667, "y": 2.26, "curve": 0.25, "c3": 0.75}, {"time": 4.5667, "curve": 0.25, "c3": 0.75}, {"time": 4.7667, "y": 2.26, "curve": 0.25, "c3": 0.75}, {"time": 4.9667, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "y": 2.26, "curve": 0.25, "c3": 0.75}, {"time": 5.3667, "curve": 0.25, "c3": 0.75}, {"time": 5.5667, "y": 2.26, "curve": 0.25, "c3": 0.75}, {"time": 5.7667}, {"time": 5.9667, "y": 2.26, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "curve": "stepped"}, {"time": 7.7333}, {"time": 7.9333, "y": 2.26, "curve": 0.25, "c3": 0.75}, {"time": 8.1333, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "y": 2.26, "curve": 0.25, "c3": 0.75}, {"time": 8.5333, "curve": 0.25, "c3": 0.75}, {"time": 8.7333, "y": 2.26, "curve": 0.25, "c3": 0.75}, {"time": 8.9333, "curve": 0.25, "c3": 0.75}, {"time": 9.1333, "y": 2.26, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "curve": 0.25, "c3": 0.75}, {"time": 9.5333, "y": 2.26, "curve": 0.25, "c3": 0.75}, {"time": 9.7333, "curve": 0.25, "c3": 0.75}, {"time": 9.9333, "y": 2.26, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.0333, "y": 1.13}]}}}, "animtion1": {"slots": {"r3": {"attachment": [{"name": null}]}, "tuzi": {"attachment": [{"name": null}]}, "r1": {"attachment": [{"name": null}]}, "r2": {"attachment": [{"name": null}]}, "r4": {"attachment": [{"name": null}]}, "tuzi2": {"attachment": [{"name": null}]}, "t1": {"attachment": [{"name": null}]}, "t2": {"attachment": [{"name": null}]}}, "bones": {"bone17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -3.46, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -3.46, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": -3.46, "curve": 0.25, "c3": 0.75}, {"time": 5}, {"time": 5.2333, "angle": 5.59}, {"time": 5.4333, "angle": -40.51}, {"time": 5.5, "angle": -39.57}, {"time": 5.5667, "angle": -41.26}, {"time": 5.6333, "angle": -39.57}, {"time": 5.7333, "angle": -40.51}, {"time": 5.8, "angle": -39.57}, {"time": 5.8667, "angle": -41.26}, {"time": 5.9333, "angle": -39.57}, {"time": 6.1333, "angle": -40.51}, {"time": 6.2, "angle": -39.57}, {"time": 6.2667, "angle": -41.26}, {"time": 6.3333, "angle": -39.57}, {"time": 6.4667, "angle": -40.51}, {"time": 6.5333, "angle": -39.57}, {"time": 6.6, "angle": -41.26}, {"time": 6.6667, "angle": -39.57}, {"time": 6.7, "angle": -40.51}, {"time": 6.7667, "angle": -39.57}, {"time": 6.8333, "angle": -41.26}, {"time": 6.8667, "angle": -40.51}, {"time": 6.9, "angle": -39.57}, {"time": 6.9333, "angle": -40.51}, {"time": 7.3}]}, "bone18": {"rotate": [{"angle": -2.36, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": -3.46, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 1.6667, "angle": -2.36, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 2.2, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "angle": -3.46, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 3.3333, "angle": -2.36, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 3.8667, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "angle": -3.46, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 5, "angle": -2.36, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 5.2333, "angle": 25.5, "curve": "stepped"}, {"time": 6.9333, "angle": 25.5}, {"time": 7.3, "angle": -2.36}]}, "bone19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -3.46, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -3.46, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": -3.46, "curve": 0.25, "c3": 0.75}, {"time": 5}, {"time": 5.4333, "angle": -29.39}, {"time": 5.5333, "angle": -31.82}, {"time": 5.6333, "angle": -29.39}, {"time": 5.7333, "angle": -31.82}, {"time": 5.8333, "angle": -29.39}, {"time": 5.9333, "angle": -31.82}, {"time": 6.0333, "angle": -29.39}, {"time": 6.1333, "angle": -31.82}, {"time": 6.1667, "angle": -29.39}, {"time": 6.2667, "angle": -31.82}, {"time": 6.3667, "angle": -29.39}, {"time": 6.4667, "angle": -31.82}, {"time": 6.5667, "angle": -29.39}, {"time": 6.6667, "angle": -31.82}, {"time": 6.7667, "angle": -29.39}, {"time": 6.8667, "angle": -31.82}, {"time": 6.9333, "angle": -29.39}, {"time": 7.3}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 7.12, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 7.12, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": 7.12, "curve": 0.25, "c3": 0.75}, {"time": 5}, {"time": 5.4333, "angle": 31.68, "curve": "stepped"}, {"time": 6.9333, "angle": 31.68}, {"time": 7.3}]}, "bone21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 7.8, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 7.8, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": 7.8, "curve": 0.25, "c3": 0.75}, {"time": 5}, {"time": 5.4333, "angle": 38.46}, {"time": 5.6333, "angle": 46.9}, {"time": 5.8333, "angle": 38.46}, {"time": 6.0333, "angle": 46.9}, {"time": 6.1667, "angle": 38.46}, {"time": 6.3667, "angle": 46.9}, {"time": 6.5667, "angle": 38.46}, {"time": 6.7667, "angle": 46.9}, {"time": 6.9333, "angle": 38.46}, {"time": 7.3}]}, "bone22": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 7.47, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 7.47, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": 7.47, "curve": 0.25, "c3": 0.75}, {"time": 5}, {"time": 5.4333, "angle": 33.02}, {"time": 5.6333, "angle": 37.76}, {"time": 5.8333, "angle": 33.02}, {"time": 6.0333, "angle": 37.76}, {"time": 6.1667, "angle": 33.02}, {"time": 6.3667, "angle": 37.76}, {"time": 6.5667, "angle": 33.02}, {"time": 6.7667, "angle": 37.76}, {"time": 6.9333, "angle": 33.02}, {"time": 7.3}]}, "bone24": {"translate": [{}, {"time": 1.6, "x": 5.75, "y": 1.72}, {"time": 3.5333, "x": -3.78, "y": -1.13}, {"time": 5}, {"time": 5.2333, "x": 5.67, "y": -8.78}, {"time": 5.4, "x": 9.06, "y": 0.52}, {"time": 5.5667, "x": 12.31, "y": 3.68, "curve": "stepped"}, {"time": 6.9333, "x": 12.31, "y": 3.68}, {"time": 7.3}]}}}}}