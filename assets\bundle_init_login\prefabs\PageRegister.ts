import { _decorator, sys } from "cc";
import CenterHttpApi from "../../GameScrpit/game/httpNet/CenterHttpApi";
import { EditBox } from "cc";
import StorageMgr, { StorageKeyEnum } from "../../platform/src/StorageHelper";
import { SceneLogin } from "../../script_game/scene_login/SceneLogin";
import { RouteSceneLogin } from "../../script_game/scene_login/RouteSceneLogin";
import { TipsMgr } from "../../platform/src/TipsHelper";
import { FmConfig } from "../../GameScrpit/game/GameDefine";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";

const { ccclass, property } = _decorator;

@ccclass("PageRegister")
export class PageRegister extends BaseCtrl {
  playShowAni: boolean = true;

  @property(EditBox)
  editAccount: EditBox;

  @property(EditBox)
  editPsd: EditBox;

  start() {
    super.start();
  }

  onGoLogin() {
    SceneLogin.routeMgr.replaceDailog(RouteSceneLogin.PageLogin);
  }

  onRegister() {
    if (
      !this.editAccount.string ||
      this.editAccount.string.length <= 0 ||
      !this.editPsd.string ||
      this.editPsd.string.length <= 0
    ) {
      TipsMgr.showTipX(250, []);
      return;
    }

    TipsMgr.setEnableTouch(false, 3);
    CenterHttpApi.register(this.editAccount.string, this.editPsd.string, sys.platform + "_" + FmConfig.origin).then(
      (res: any) => {
        TipsMgr.setEnableTouch(true, 3);
        if (res.code === 200) {
          TipsMgr.showTipX(254, []);

          StorageMgr.remove(StorageKeyEnum.LoginName);
          StorageMgr.remove(StorageKeyEnum.Psd);

          StorageMgr.saveItem(StorageKeyEnum.LoginName, this.editAccount.string);
          StorageMgr.saveItem(StorageKeyEnum.Psd, this.editPsd.string);

          SceneLogin.routeMgr.replaceDailog(RouteSceneLogin.PageLogin);
        } else {
          TipsMgr.showTip(res.msg);
        }
      }
    );
  }

  on_close() {
    SceneLogin.routeMgr.back();
  }
}
