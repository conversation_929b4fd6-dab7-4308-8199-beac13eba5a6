import { _decorator, director, instantiate, Label, tween, v3, Vec3 } from "cc";
import { UINode } from "../../lib/ui/UINode";
import { BundleEnum } from "../bundleEnum/BundleEnum";
import { UIMgr } from "../../lib/ui/UIMgr";
import { EventActionModule } from "../../module/event_action/src/EventActionModule";
import { JsonMgr } from "../mgr/JsonMgr";
import { PlayerModule } from "../../module/player/PlayerModule";
import { ItemEnum } from "../../lib/common/ItemEnum";
import { LangMgr } from "../mgr/LangMgr";
import Formate from "../../lib/utils/Formate";
import MsgEnum from "../event/MsgEnum";
import MsgMgr from "../../lib/event/MsgMgr";
import { NodeTool } from "../../lib/utils/NodeTool";
import { path_thief_ctrl } from "./ui_gameMap/event_ctrl/Path_thief_ctrl";
import { Sleep } from "../GameDefine";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";

const { ccclass, property } = _decorator;

@ccclass("UIThief")
export class UIThief extends UINode {
  private _hartbeatTween: any;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_COMMON_MAIN}?prefab/ui/UIThief`;
  }

  protected onRegEvent(): void {
    MsgMgr.on(MsgEnum.ON_HEART_BEAT, this.onHartBeat, this);
  }

  protected onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_HEART_BEAT, this.onHartBeat, this);
  }

  on_click_btn_guanbi_zl() {
    UIMgr.instance.back();
  }

  async on_click_btn_lvse() {
    AudioMgr.instance.playEffect(1725);
    UIMgr.instance.back();
    await Sleep(0.01);
    const totalProgressMap = EventActionModule.data.eventTrainMessage.eventMap;
    let db = JsonMgr.instance.jsonList.c_event2;
    for (let i in totalProgressMap) {
      let id = Number(i);
      let info = db[id];
      if (info.type == 1) {
        const nodeGround = NodeTool.findByName(director.getScene(), "node_ground");
        for (let i = 0; i < nodeGround.children.length; i++) {
          if (nodeGround.children[i].name.startsWith("path_thief")) {
            nodeGround.children[i].getComponent(path_thief_ctrl).showUI();
            break;
          }
        }
        return;
      }
    }
  }

  private onHartBeat() {
    const totalProgressMap = EventActionModule.data.eventTrainMessage.eventMap;
    let db = JsonMgr.instance.jsonList.c_event2;
    let info = null;
    for (let i in totalProgressMap) {
      let id = Number(i);
      info = db[id];
      if (info.type == 1) {
        break;
      }
    }

    if (!info) {
      return;
    }

    let debuff = info.debuff;
    // debuff索引0是1代表气运，索引1代表的是万分比
    let fan_rong_du = PlayerModule.data.getItemNum(ItemEnum.繁荣度_2);

    // 计算扣除值（万分比转换）
    const reduceValue = Math.floor((fan_rong_du * debuff[1]) / 10000);

    // 克隆扣除数值节点
    const debuffNumNode = this.getNode("debuff_num");
    if (debuffNumNode) {
      const cloneNode = instantiate(debuffNumNode);
      cloneNode.active = true;

      const Label_lbl_message = cloneNode.getChildByName("lbl_message").getComponent(Label);
      if (Label_lbl_message) Label_lbl_message.string = LangMgr.txMsgCode(246, []);

      // 设置数值显示
      const label_num = cloneNode.getChildByName("num").getComponent(Label);
      if (label_num) label_num.string = `-${Formate.format(reduceValue)}`;

      this.getNode("point").addChild(cloneNode);
      cloneNode.setPosition(v3(0, 0, 0));
      cloneNode.active = true;

      // 执行缓动动画
      this._hartbeatTween = tween(cloneNode)
        .parallel(tween().by(1.5, { position: new Vec3(0, 100, 0) }, { easing: "sineOut" }))
        .call(() => {
          cloneNode.destroy();
          this._hartbeatTween = null;
        })
        .start();
    }
  }

  protected onEvtClose(): void {
    if (this._hartbeatTween) {
      this._hartbeatTween.stop();
    }
  }
}
