import { _decorator, sp } from "cc";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
import { Sleep } from "../game/GameDefine";

const { ccclass, property } = _decorator;

@ccclass("TopLoading")
export class TopLoading extends BaseCtrl {
  @property(sp.Skeleton)
  spineYunwuZc: sp.Skeleton = null;

  // 事件
  waitFunc: Function = null;

  init(args: Function) {
    super.init(args);
    this.waitFunc = args;
  }

  start() {
    this.spineYunwuZc.setAnimation(0, "action1", false);
    this.spineYunwuZc.setCompleteListener(async () => {
      // 执行等待事件
      await this.waitFunc();
      this.spineYunwuZc.setAnimation(0, "action2", false);
      this.spineYunwuZc.setCompleteListener(async () => {
        await Sleep(0.5);
        this.closeBack();
      });
    });
  }

  update(deltaTime: number) {}
}
