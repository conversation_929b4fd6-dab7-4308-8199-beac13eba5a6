syntax = "proto3";
package sim;

// 
message PostHarvestResponse {
  // 偶数索引为道具的ID，奇数为数量
  repeated double resAddList = 1;
  // 驿站信息
  PostTrainMessage postTrain = 2;
}

// 
message PostTrainMessage {
  // 等级
  int32 level = 1;
  // 最近一次货运结束的时间戳，如果小于当前时间，并且当日挂机次数不为0，且没有待领取的奖励，就可以随时开始下一次货运
  int64 lastDeadline = 2;
  // 挂机获得奖励，需要到时间才能领取
  repeated double rewardList = 3;
  // 当日已经挂机的次数
  int32 dailyCount = 4;
  // 当日气运已投放的次数
  int32 energyDropCount = 5;
  // 当日最大挂机次数
  int32 dailyMaxCount = 6;
  // 历史货运次数
  int32 historyCount = 7;
}

