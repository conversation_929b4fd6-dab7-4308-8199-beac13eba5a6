import { Label } from "cc";
import { _decorator, Component, Node } from "cc";
const { ccclass, property } = _decorator;

@ccclass("Confirm")
export class Confirm extends Component {
  public cancel: Function;
  public confirm: Function;
  public msg: string;

  @property(Label)
  public lblMsg: Label;

  start() {
    this.lblMsg.string = this.msg;
  }

  onCancel() {
    this.cancel && this.cancel();
  }

  onConfirm() {
    this.confirm && this.confirm();
  }
}
