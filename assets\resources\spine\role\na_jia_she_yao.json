{"skeleton": {"hash": "3UTI6xHV6Os39mcOvHmOrgrh8rs=", "spine": "3.8.75", "x": -109.39, "y": -22.93, "width": 220.98, "height": 259.93, "images": "./images/", "audio": "D:/spine导出/S_时空裂隙_怪/美女蛇"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 80, "rotation": 1.7, "x": 3.23, "y": -27.76, "color": "6cff00ff"}, {"name": "bone2", "parent": "bone", "length": 16.47, "rotation": 88.01, "x": -304.97, "y": -63.13, "color": "ff0000ff"}, {"name": "bone3", "parent": "bone2", "length": 16.47, "x": 16.47, "color": "ff0000ff"}, {"name": "bone4", "parent": "bone3", "length": 16.47, "x": 16.47, "color": "ff0000ff"}, {"name": "bone5", "parent": "bone4", "length": 16.47, "x": 16.47, "color": "ff0000ff"}, {"name": "bone6", "parent": "bone5", "length": 16.47, "x": 16.47, "color": "ff0000ff"}, {"name": "bone7", "parent": "bone6", "length": 16.47, "x": 16.47, "color": "ff0000ff"}, {"name": "bone8", "parent": "bone7", "length": 16.47, "x": 16.47, "color": "ff0000ff"}, {"name": "bone9", "parent": "bone8", "length": 16.47, "x": 16.47, "color": "ff0000ff"}, {"name": "bone10", "parent": "bone9", "length": 16.47, "x": 16.47, "color": "ff0000ff"}, {"name": "bone11", "parent": "bone10", "length": 16.47, "x": 16.47, "color": "ff0000ff"}, {"name": "bone12", "parent": "bone11", "length": 16.47, "x": 16.47, "color": "ff0000ff"}, {"name": "bone13", "parent": "bone12", "length": 16.47, "x": 16.47, "color": "ff0000ff"}, {"name": "bone14", "parent": "bone13", "length": 16.47, "x": 16.47, "color": "ff0000ff"}, {"name": "bone15", "parent": "bone14", "length": 16.47, "x": 16.47, "color": "ff0000ff"}, {"name": "bone16", "parent": "bone15", "length": 16.47, "x": 16.47, "color": "ff0000ff"}, {"name": "bone17", "parent": "bone16", "length": 16.47, "x": 16.47, "color": "ff0000ff"}, {"name": "bone18", "parent": "bone17", "length": 16.47, "x": 16.47, "color": "ff0000ff"}, {"name": "bone19", "parent": "bone18", "length": 16.47, "x": 16.47, "color": "ff0000ff"}, {"name": "bone20", "parent": "bone19", "length": 16.47, "x": 16.47, "color": "ff0000ff"}, {"name": "bone21", "parent": "bone20", "length": 16.47, "x": 16.47, "color": "ff0000ff"}, {"name": "bone22", "parent": "bone21", "length": 16.47, "x": 16.47, "color": "ff0000ff"}, {"name": "bone23", "parent": "bone22", "length": 16.47, "x": 16.47, "color": "ff0000ff"}, {"name": "bone24", "parent": "bone23", "length": 16.47, "x": 16.47, "color": "ff0000ff"}, {"name": "bone25", "parent": "bone24", "length": 16.47, "x": 16.47, "color": "ff0000ff"}, {"name": "bone26", "parent": "bone25", "length": 16.47, "x": 16.47, "color": "ff0000ff"}, {"name": "bone27", "parent": "bone26", "length": 16.47, "x": 16.47, "color": "ff0000ff"}, {"name": "bone28", "parent": "bone27", "length": 16.47, "x": 16.47, "color": "ff0000ff"}, {"name": "bone29", "parent": "bone28", "length": 16.47, "x": 16.47, "color": "ff0000ff"}, {"name": "bone30", "parent": "bone29", "length": 16.47, "x": 16.47, "color": "ff0000ff"}, {"name": "bone31", "parent": "bone30", "length": 16.47, "x": 16.47, "color": "ff0000ff"}, {"name": "bone32", "parent": "bone", "length": 36.96, "rotation": -2.07, "x": 1.92, "y": 106.71, "color": "0029ffff"}, {"name": "bone33", "parent": "bone32", "rotation": 2.07, "x": 8.56, "y": -12.65, "color": "fdff00ff"}, {"name": "bone34", "parent": "bone", "x": 37.44, "y": 40.51, "color": "fdff00ff"}, {"name": "bone35", "parent": "bone", "x": -45.83, "y": 62.74, "color": "fdff00ff"}, {"name": "bone36", "parent": "bone", "x": -113.44, "y": 34.18, "color": "fdff00ff"}, {"name": "bone37", "parent": "bone", "x": -11.4, "y": 7.56, "color": "fdff00ff"}, {"name": "bone38", "parent": "bone", "x": 99.46, "y": -0.77, "color": "fdff00ff"}, {"name": "bone39", "parent": "bone32", "length": 22.2, "rotation": 95.49, "x": -0.79, "y": 1.61, "color": "ca00ffff"}, {"name": "bone40", "parent": "bone39", "length": 25.78, "rotation": 18.66, "x": 22.2, "color": "ca00ffff"}, {"name": "bone41", "parent": "bone40", "length": 15.51, "rotation": -23.17, "x": 25.78, "color": "ca00ffff"}, {"name": "bone42", "parent": "bone40", "rotation": -20.02, "x": 27.49, "y": 16.56, "color": "6cff00ff"}, {"name": "bone43", "parent": "bone40", "rotation": -20.02, "x": 17.16, "y": -12.61, "color": "6cff00ff"}, {"name": "bone44", "parent": "bone42", "length": 38.82, "rotation": 166.53, "x": 0.69, "y": -0.74, "color": "ff6a00ff"}, {"name": "bone45", "parent": "bone44", "length": 35.67, "rotation": -86.68, "x": 38.82, "color": "ff6a00ff"}, {"name": "bone46", "parent": "bone45", "length": 11.16, "rotation": 16.06, "x": 37.34, "y": 0.89, "color": "6cff00ff"}, {"name": "bone47", "parent": "bone46", "length": 13.19, "rotation": -37.13, "x": 11.16, "color": "6cff00ff"}, {"name": "bone48", "parent": "bone40", "length": 22.3, "rotation": 89.31, "x": 21.34, "y": 10.67, "color": "ca00ffff"}, {"name": "bone49", "parent": "bone48", "length": 38.95, "rotation": -12.74, "x": 22.3, "color": "ca00ffff"}, {"name": "bone50", "parent": "bone49", "length": 19.98, "rotation": -24.16, "x": 38.95, "color": "ca00ffff"}, {"name": "bone51", "parent": "bone40", "length": 36.6, "rotation": 68.71, "x": 31.58, "y": 13.8, "color": "ca00ffff"}, {"name": "bone52", "parent": "bone51", "length": 38.24, "rotation": -70.51, "x": 36.6, "color": "ca00ffff"}, {"name": "bone53", "parent": "bone52", "length": 17.96, "rotation": -32.69, "x": 38.24, "color": "ca00ffff"}, {"name": "bone54", "parent": "bone40", "length": 30.24, "rotation": -91.53, "x": 12.12, "y": -13.42, "color": "ca00ffff"}, {"name": "bone55", "parent": "bone54", "length": 38.26, "rotation": 20.39, "x": 30.24, "color": "ca00ffff"}, {"name": "bone56", "parent": "bone55", "length": 18.13, "rotation": 20.79, "x": 38.26, "color": "ca00ffff"}, {"name": "bone57", "parent": "bone43", "length": 34.59, "rotation": -154.6, "x": -3.28, "y": 0.99, "color": "ff6a00ff"}, {"name": "bone58", "parent": "bone57", "length": 34.88, "rotation": 7.5, "x": 34.59, "color": "ff6a00ff"}, {"name": "bone59", "parent": "bone58", "length": 11.28, "rotation": 31.84, "x": 34.88, "color": "6cff00ff"}, {"name": "bone60", "parent": "bone59", "length": 9.02, "rotation": -27.79, "x": 11.28, "color": "6cff00ff"}, {"name": "bone61", "parent": "bone40", "length": 32, "rotation": -147.29, "x": 5.23, "y": -12.84, "color": "ca00ffff"}, {"name": "bone62", "parent": "bone61", "length": 40.34, "rotation": 56.47, "x": 32.01, "color": "ca00ffff"}, {"name": "bone63", "parent": "bone62", "length": 17.07, "rotation": -24.58, "x": 40.34, "color": "ca00ffff"}, {"name": "bone68", "parent": "bone63", "length": 34.92, "rotation": 71.32, "x": 17.33, "y": -6.72, "color": "10ff00ff"}, {"name": "bone64", "parent": "bone68", "length": 70.37, "rotation": 153.17, "x": 5.01, "y": 2.8, "color": "fff900ff"}, {"name": "bone65", "parent": "bone68", "length": 77.84, "rotation": 20.75, "x": 8.04, "y": 2.63, "color": "fff900ff"}, {"name": "bone66", "parent": "bone65", "length": 72.45, "rotation": 155.53, "x": 76.92, "y": 0.26, "color": "fff900ff"}, {"name": "bone67", "parent": "bone64", "length": 64.81, "rotation": -155.75, "x": 71.41, "y": -2.61, "color": "fff900ff"}, {"name": "gong", "parent": "bone68", "rotation": -70.63, "x": 7.48, "y": 34.5, "color": "ff3f00ff"}, {"name": "bone69", "parent": "bone41", "x": 19.23, "y": -9.86, "color": "0600ffff"}, {"name": "bone70", "parent": "bone41", "x": 31.33, "y": -11.89, "color": "0600ffff"}, {"name": "toufa", "parent": "bone41", "length": 12.31, "rotation": -8.16, "x": 45.52, "y": -10.46, "color": "ca00ffff"}, {"name": "toufa2", "parent": "toufa", "length": 12.7, "rotation": -80.58, "x": 4.56, "y": -4.83, "color": "ff0000ff"}, {"name": "toufa3", "parent": "toufa2", "length": 14.63, "rotation": -40.36, "x": 12.7, "color": "ff0000ff"}, {"name": "toufa4", "parent": "toufa3", "length": 13.84, "rotation": 43.06, "x": 14.74, "y": 0.09, "color": "ff0000ff"}, {"name": "toufa5", "parent": "toufa", "length": 12.18, "rotation": 84.46, "x": 7.16, "y": 7.07, "color": "ff0000ff"}, {"name": "toufa6", "parent": "toufa5", "length": 19.73, "rotation": 55.82, "x": 12.18, "color": "ff0000ff"}, {"name": "toufa7", "parent": "toufa6", "length": 18.92, "rotation": -52.81, "x": 19.73, "color": "ff0000ff"}, {"name": "toufa8", "parent": "toufa", "length": 15.31, "rotation": 125.5, "x": -14.28, "y": 14.39, "color": "ff0000ff"}, {"name": "toufa9", "parent": "toufa8", "length": 15.38, "rotation": -22.8, "x": 15.31, "color": "ff0000ff"}, {"name": "toufa10", "parent": "toufa9", "length": 10.64, "rotation": -18.64, "x": 15.38, "color": "ff0000ff"}, {"name": "toufa11", "parent": "toufa", "length": 14.51, "rotation": -140.27, "x": -13.25, "y": -11.22, "color": "ff0000ff"}, {"name": "toufa12", "parent": "toufa11", "length": 13.97, "rotation": 37.6, "x": 14.58, "y": 0.12, "color": "ff0000ff"}, {"name": "toufa13", "parent": "toufa", "length": 7.94, "rotation": -164.46, "x": -30.72, "y": -5.46, "color": "ff0000ff"}, {"name": "toufa14", "parent": "toufa13", "length": 11.44, "rotation": 40.43, "x": 7.94, "color": "ff0000ff"}, {"name": "toufa15", "parent": "toufa", "length": 11.4, "rotation": 149.43, "x": -25.5, "y": 10.82, "color": "ff0000ff"}, {"name": "toufa16", "parent": "toufa15", "length": 14.25, "rotation": -20.35, "x": 11.4, "color": "ff0000ff"}, {"name": "toufa17", "parent": "toufa", "length": 12.16, "rotation": -169.2, "x": -28.37, "y": 3.06, "color": "ff0000ff"}, {"name": "toufa18", "parent": "toufa17", "length": 9.8, "rotation": -1.64, "x": 12.16, "color": "ff0000ff"}, {"name": "toufa19", "parent": "toufa", "length": 11.62, "rotation": 20.59, "x": 11.05, "y": -3.41, "color": "ff0000ff"}, {"name": "toufa20", "parent": "toufa19", "length": 17.2, "rotation": 38.8, "x": 11.62, "color": "ff0000ff"}, {"name": "toufa21", "parent": "toufa20", "length": 18.25, "rotation": 21.91, "x": 17.2, "color": "ff0000ff"}, {"name": "bone71", "parent": "bone32", "length": 23.98, "rotation": -113.98, "x": -18.09, "y": -5.82, "color": "6cff00ff"}, {"name": "bone72", "parent": "bone71", "length": 25.31, "rotation": -1.51, "x": 23.98, "color": "6cff00ff"}, {"name": "bone73", "parent": "bone72", "length": 23.43, "rotation": -17.54, "x": 25.31, "color": "6cff00ff"}, {"name": "bone74", "parent": "bone73", "length": 15.18, "rotation": -45.72, "x": 23.43, "color": "6cff00ff"}, {"name": "bone75", "parent": "bone32", "length": 22.38, "rotation": -111.98, "x": -10.95, "y": -8.3, "color": "6cff00ff"}, {"name": "bone76", "parent": "bone75", "length": 24.29, "rotation": 3.57, "x": 22.38, "color": "6cff00ff"}, {"name": "bone77", "parent": "bone76", "length": 28.17, "rotation": 9.85, "x": 24.29, "color": "6cff00ff"}, {"name": "bone78", "parent": "bone32", "length": 26.44, "rotation": -68.2, "x": 10.64, "y": -3.1, "color": "6cff00ff"}, {"name": "bone79", "parent": "bone78", "length": 26.51, "rotation": -5.83, "x": 26.27, "y": -0.43, "color": "6cff00ff"}, {"name": "bone80", "parent": "bone79", "length": 24.73, "rotation": 27.89, "x": 26.51, "color": "6cff00ff"}, {"name": "bone81", "parent": "root", "length": 140, "rotation": 89.64, "x": -141.31, "y": 46.52, "color": "ff0000ff"}, {"name": "ef1", "parent": "root", "x": 83.77, "y": 106.63}, {"name": "xuanz", "parent": "bone63", "x": 10.02, "y": 4.4, "scaleX": 1.9339, "scaleY": 1.9339, "transform": "noRotationOrReflection"}], "slots": [{"name": "toufa2", "bone": "toufa", "attachment": "toufa2"}, {"name": "jiao", "bone": "toufa", "attachment": "jiao"}, {"name": "touf1", "bone": "toufa", "attachment": "touf1"}, {"name": "mv_she_02", "bone": "bone51", "attachment": "mv_she_02"}, {"name": "mv_she_03", "bone": "bone48", "attachment": "mv_she_03"}, {"name": "mv_she_04", "bone": "bone54", "attachment": "mv_she_04"}, {"name": "mv_she_05", "bone": "bone68", "attachment": "mv_she_05"}, {"name": "mv_she_06", "bone": "bone61", "attachment": "mv_she_06"}, {"name": "mv_she_07", "bone": "bone57", "attachment": "mv_she_07"}, {"name": "mv_she_08", "bone": "bone39", "attachment": "mv_she_08"}, {"name": "mv_she_09", "bone": "bone32", "attachment": "mv_she_09"}, {"name": "mv_she_010", "bone": "bone75", "attachment": "mv_she_010"}, {"name": "mv_she_011", "bone": "bone32", "attachment": "mv_she_011"}, {"name": "mv_she_012", "bone": "bone75", "attachment": "mv_she_012"}, {"name": "mv_she_013", "bone": "bone78", "attachment": "mv_she_013"}, {"name": "mv_she_014", "bone": "bone32", "attachment": "mv_she_014"}, {"name": "mv_she_015", "bone": "bone32", "attachment": "mv_she_015"}, {"name": "mv_she_017", "bone": "bone41", "attachment": "mv_she_017"}, {"name": "mv_she_z<PERSON>yan", "bone": "bone41", "attachment": "mv_she_018"}, {"name": "mv_she_biyan", "bone": "bone41"}, {"name": "mv_she_022", "bone": "bone68", "attachment": "mv_she_022"}, {"name": "mv_she_020", "bone": "bone81"}, {"name": "shewei", "bone": "bone", "attachment": "shewei"}, {"name": "ss2", "bone": "bone44", "attachment": "ss2"}, {"name": "ss1", "bone": "bone45", "attachment": "ss1"}, {"name": "ef1/qianghen_add_02", "bone": "ef1"}, {"name": "ef2/qf_njs_jn2_xl_02", "bone": "xuanz"}], "ik": [{"name": "gong", "bones": ["bone65", "bone66"], "target": "gong"}, {"name": "gong1", "order": 1, "bones": ["bone64", "bone67"], "target": "gong", "bendPositive": false}], "transform": [{"name": "face", "order": 3, "bones": ["bone70"], "target": "bone69", "x": 12.1, "y": -2.03, "rotateMix": -1, "translateMix": -1, "scaleMix": -1, "shearMix": -1}, {"name": "toufa1", "order": 4, "bones": ["toufa"], "target": "bone69", "rotation": -8.16, "x": 26.29, "y": -0.6, "shearY": 360, "rotateMix": -0.255, "translateMix": -0.255, "scaleMix": -0.255, "shearMix": -0.255}], "path": [{"name": "shewei", "order": 2, "bones": ["bone2", "bone3", "bone4", "bone5", "bone6", "bone7", "bone8", "bone9", "bone10", "bone11", "bone12", "bone13", "bone14", "bone15", "bone16", "bone17", "bone18", "bone19", "bone20", "bone21", "bone22", "bone23", "bone24", "bone25", "bone26", "bone27", "bone28", "bone29", "bone30", "bone31"], "target": "shewei", "spacingMode": "percent", "rotateMode": "chainScale", "position": 0.2244, "spacing": 0.021}], "skins": [{"name": "default", "attachments": {"shewei": {"shewei": {"type": "path", "lengths": [201.31, 291.48, 351.82, 448.77, 534.59, 618.16, 741.63, 881.1, 1341.18], "vertexCount": 27, "vertices": [2, 38, -50.57, 356.1, 0.25, 33, 39.25, 261.27, 0.75, 1, 33, 21.11, 207.65, 1, 1, 33, 13.15, 184.12, 1, 1, 33, -9.88, 48.92, 1, 4, 38, -99.63, 103.79, 0.05529, 37, 11.23, 95.17, 0.00044, 34, -37.62, 62.22, 0.1637, 33, -9.82, 8.96, 0.78057, 4, 38, -99.57, 63.84, 0.14399, 37, 11.29, 55.22, 0.00114, 34, -37.56, 22.26, 0.42629, 33, -9.75, -30.99, 0.42857, 2, 34, -41.27, 0.39, 0.57143, 33, -13.46, -52.86, 0.42857, 4, 38, -66.45, 28.55, 0.00113, 37, 44.41, 19.92, 0.00114, 34, -4.44, -13.03, 0.56915, 33, 23.36, -66.28, 0.42857, 2, 34, 13.12, -19.43, 0.57143, 33, 40.92, -72.68, 0.42857, 4, 38, -5.58, 37.39, 0.00113, 37, 105.28, 28.77, 0.00114, 35, 139.7, -26.41, 0.35886, 34, 56.43, -4.18, 0.63887, 4, 38, -22.6, 50.76, 0.00113, 37, 88.25, 42.14, 0.00114, 35, 122.68, -13.04, 0.35886, 34, 39.4, 9.18, 0.63887, 4, 38, -39.63, 64.13, 0.00113, 37, 71.23, 55.5, 0.00114, 35, 105.65, 0.33, 0.35886, 34, 22.38, 22.55, 0.63887, 3, 36, 122.78, 33.55, 0.28571, 35, 55.16, 5, 0.216, 34, -28.11, 27.22, 0.49829, 3, 36, 96.21, 35.42, 0.28571, 35, 28.59, 6.86, 0.28571, 34, -54.68, 29.09, 0.42857, 3, 36, 70.22, 37.25, 0.28571, 35, 2.6, 8.69, 0.28571, 34, -80.67, 30.91, 0.42857, 4, 37, -79.25, 67.87, 0.32343, 36, 22.79, 41.25, 0.10514, 35, -44.82, 12.7, 0.50171, 34, -128.1, 34.92, 0.06971, 4, 37, -86.05, 46.08, 0.32343, 36, 15.99, 19.46, 0.248, 35, -51.63, -9.09, 0.35886, 34, -134.9, 13.13, 0.06971, 4, 37, -92.86, 24.3, 0.32343, 36, 9.18, -2.32, 0.248, 35, -58.43, -30.88, 0.35886, 34, -141.7, -8.66, 0.06971, 4, 38, -184.21, 21.01, 0.42857, 37, -73.36, 12.39, 0.03771, 36, 28.68, -14.23, 0.39086, 35, -38.93, -42.79, 0.14286, 4, 38, -133.56, 20.14, 0.42857, 37, -22.7, 11.52, 0.14286, 36, 79.34, -15.1, 0.28571, 35, 11.72, -43.66, 0.14286, 4, 38, -118.04, 19.87, 0.42857, 37, -7.18, 11.25, 0.14286, 36, 94.86, -15.37, 0.28571, 35, 27.24, -43.92, 0.14286, 3, 38, -57.28, 38.56, 0.57143, 37, 53.58, 29.93, 0.32343, 36, 155.62, 3.31, 0.10514, 3, 38, -11.77, 16.85, 0.57143, 37, 99.09, 8.23, 0.32343, 36, 201.13, -18.39, 0.10514, 3, 38, 35.76, -5.81, 0.57143, 37, 146.62, -14.44, 0.32343, 36, 248.66, -41.06, 0.10514, 1, 38, 64.23, -60.12, 1, 1, 38, 81.87, -84.47, 1, 1, 38, 105.46, -117.06, 1]}}, "mv_she_02": {"mv_she_02": {"type": "mesh", "uvs": [0.04553, 0.34158, 0.0309, 0.19627, 0.04431, 0.09583, 0.08331, 0.0189, 0.18934, 0, 0.271, 0, 0.32218, 0.09797, 0.3234, 0.1781, 0.36606, 0.24221, 0.33925, 0.29991, 0.30512, 0.31166, 0.28318, 0.36188, 0.20762, 0.38111, 0.21128, 0.40569, 0.23565, 0.41531, 0.30512, 0.51895, 0.29172, 0.5681, 0.37703, 0.70166, 0.39653, 0.69739, 0.58178, 0.73372, 0.6159, 0.76684, 0.78851, 0.75174, 0.9307, 0.72681, 1, 0.80927, 1, 0.89174, 0.93289, 0.92818, 0.7732, 0.93968, 0.63101, 0.93009, 0.53661, 1, 0.37692, 0.96462, 0.23692, 0.90325, 0.23911, 0.85722, 0.1363, 0.64434, 0.0488, 0.62516, 0.00942, 0.48325, 0.05755, 0.42188, 0.3463, 0.81119, 0.62192, 0.8553, 0.83192, 0.83037, 0.20848, 0.6079, 0.13411, 0.41804, 0.14723, 0.33749, 0.16911, 0.18215, 0.1888, 0.07859], "triangles": [43, 3, 4, 2, 3, 43, 42, 2, 43, 43, 4, 5, 6, 43, 5, 1, 2, 42, 8, 10, 7, 43, 7, 42, 7, 43, 6, 9, 10, 8, 10, 42, 7, 41, 1, 42, 41, 42, 10, 0, 1, 41, 10, 12, 41, 11, 12, 10, 40, 0, 41, 40, 41, 12, 40, 12, 13, 35, 0, 40, 15, 39, 14, 14, 39, 40, 14, 40, 13, 16, 39, 15, 40, 33, 34, 40, 34, 35, 39, 33, 40, 32, 33, 39, 17, 39, 16, 36, 39, 17, 36, 18, 19, 36, 17, 18, 36, 32, 39, 31, 32, 36, 30, 31, 36, 29, 30, 36, 38, 21, 22, 38, 22, 23, 37, 20, 21, 37, 21, 38, 19, 20, 37, 19, 37, 36, 25, 38, 23, 24, 25, 23, 26, 27, 37, 38, 26, 37, 26, 38, 25, 29, 36, 37, 28, 29, 37, 27, 28, 37], "vertices": [2, 52, 40.41, 4.89, 0.38609, 53, -0.81, 5.29, 0.61392, 1, 53, 9.43, 8.18, 1, 1, 53, 16.8, 8.7, 1, 1, 53, 22.78, 7.29, 1, 1, 53, 25.4, 0.88, 1, 1, 53, 26.37, -4.26, 1, 1, 53, 19.95, -8.8, 1, 2, 52, 44.82, -16.07, 0.00964, 53, 14.22, -9.97, 0.99036, 2, 52, 39.46, -16.85, 0.04504, 53, 10.13, -13.52, 0.95496, 2, 52, 36.19, -13.68, 0.09796, 53, 5.67, -12.62, 0.90204, 2, 52, 36.21, -11.33, 0.16211, 53, 4.42, -10.63, 0.83789, 2, 52, 33.34, -8.66, 0.34422, 53, 0.56, -9.93, 0.65578, 2, 52, 33.85, -3.65, 0.78103, 53, -1.72, -5.44, 0.21897, 2, 52, 32.1, -3.2, 0.95711, 53, -3.44, -6.01, 0.04289, 2, 52, 30.86, -4.38, 0.9955, 53, -3.84, -7.67, 0.0045, 1, 52, 22.18, -5.67, 1, 1, 52, 19.17, -3.53, 1, 2, 51, 34.64, -9.28, 0.20088, 52, 8.09, -4.95, 0.79912, 2, 51, 33.38, -9.53, 0.33961, 52, 7.91, -6.22, 0.66039, 2, 51, 21.65, -6.37, 0.98906, 52, 1.02, -16.22, 0.01094, 2, 51, 19.57, -3.86, 0.99959, 52, -2.04, -17.34, 0.00041, 1, 51, 8.49, -4.48, 1, 1, 51, -0.68, -5.9, 1, 1, 51, -4.85, 0.3, 1, 1, 51, -4.59, 6.32, 1, 1, 51, -0.18, 8.79, 1, 1, 51, 10.06, 9.18, 1, 1, 51, 19.13, 8.09, 1, 1, 51, 25.38, 12.93, 1, 2, 51, 35.48, 9.9, 0.93392, 52, -9.71, 2.24, 0.06608, 2, 51, 44.24, 5.04, 0.26598, 52, -2.2, 8.88, 0.73402, 2, 51, 43.95, 1.68, 0.12135, 52, 0.86, 7.49, 0.87865, 1, 52, 17.74, 7.77, 1, 1, 52, 21.13, 12.44, 1, 1, 52, 31.68, 10.9, 1, 2, 52, 34.68, 6.37, 0.98202, 53, -6.43, 3.44, 0.01798, 1, 52, 1.41, -0.13, 1, 1, 51, 19.47, 2.61, 1, 1, 51, 5.96, 1.37, 1, 1, 52, 18.47, 2.5, 1, 1, 52, 33.11, 1.72, 1, 2, 52, 38.25, -1.26, 0.34143, 53, 0.69, -1.05, 0.65857, 2, 52, 48.24, -6.8, 0, 53, 12.09, -0.32, 1, 1, 53, 19.75, -0.15, 1], "hull": 36, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 0, 70, 72, 74, 74, 76, 76, 46, 72, 78, 78, 80, 80, 82, 82, 84, 84, 86], "width": 64, "height": 73}}, "mv_she_03": {"mv_she_03": {"type": "mesh", "uvs": [0.99863, 0.06289, 1, 0.28013, 0.75279, 0.68069, 0.71309, 0.74502, 0.688, 0.75995, 0.53504, 0.84061, 0.48574, 0.93185, 0.40493, 0.9753, 0.30633, 1, 0.26935, 0.91882, 0.19265, 0.90144, 0.1365, 0.86668, 0.11322, 0.75806, 0.00091, 0.72764, 0.00639, 0.49302, 0.03241, 0.26275, 0.13239, 0.26275, 0.168, 0.04551, 0.21867, 0.12371, 0.2803, 0.24971, 0.31454, 0.42785, 0.41589, 0.34964, 0.48574, 0.28013, 0.52956, 0.35833, 0.64933, 0.27604, 0.68159, 0.26275, 0.71356, 0.23012, 0.93906, 0, 0.94865, 0.16282, 0.70487, 0.50171, 0.53504, 0.59296, 0.73394, 0.48817, 0.6734, 0.52219, 0.32003, 0.68127, 0.20742, 0.57989, 0.1009, 0.51713], "triangles": [35, 15, 16, 14, 15, 35, 18, 16, 17, 34, 18, 19, 34, 19, 20, 34, 16, 18, 35, 16, 34, 34, 20, 33, 13, 14, 35, 12, 35, 34, 13, 35, 12, 11, 12, 34, 10, 11, 34, 9, 10, 34, 33, 9, 34, 29, 25, 26, 31, 29, 26, 32, 24, 25, 32, 25, 29, 30, 23, 24, 30, 24, 32, 33, 20, 21, 2, 3, 29, 2, 29, 31, 32, 29, 3, 4, 32, 3, 4, 5, 30, 23, 30, 22, 4, 30, 32, 22, 30, 21, 30, 6, 21, 5, 6, 30, 7, 33, 21, 6, 7, 21, 8, 9, 33, 7, 8, 33, 28, 27, 0, 26, 27, 28, 28, 0, 1, 31, 26, 28, 2, 31, 28, 2, 28, 1], "vertices": [1, 48, -5.64, -1.15, 1, 1, 48, -3.29, 4.69, 1, 2, 48, 22.19, 6.46, 0.59571, 49, -1.53, 6.28, 0.40429, 2, 48, 26.28, 6.74, 0.17533, 49, 2.4, 7.46, 0.82467, 2, 48, 28.58, 6.24, 0.05935, 49, 4.75, 7.47, 0.94065, 1, 49, 19.01, 7.24, 1, 1, 49, 23.95, 9.03, 1, 2, 49, 31.49, 8.93, 0.99942, 50, -10.47, 5.1, 0.00058, 2, 49, 40.54, 8.01, 0.62914, 50, -1.83, 7.96, 0.37086, 2, 49, 43.47, 5.08, 0.21417, 50, 2.04, 6.49, 0.78583, 1, 50, 9.01, 7.68, 1, 1, 50, 14.27, 7.94, 1, 1, 50, 17.1, 5.39, 1, 1, 50, 27.34, 7, 1, 1, 50, 28.48, 0.27, 1, 1, 50, 27.75, -6.79, 1, 1, 50, 18.82, -8.98, 1, 1, 50, 17.14, -15.88, 1, 2, 49, 43.91, -18.44, 0.00237, 50, 12.07, -14.79, 0.99763, 2, 49, 38.99, -13.82, 0.06421, 50, 5.69, -12.6, 0.93579, 2, 49, 36.82, -8.18, 0.44948, 50, 1.4, -8.33, 0.55052, 2, 49, 27.24, -8.73, 0.99586, 50, -7.11, -12.76, 0.00414, 1, 49, 20.55, -9.56, 1, 1, 49, 17, -6.61, 1, 2, 48, 26.35, -8.07, 0.05868, 49, 5.73, -6.98, 0.94132, 2, 48, 23.46, -7.26, 0.26466, 49, 2.74, -6.82, 0.73534, 2, 48, 20.39, -6.97, 0.63441, 49, -0.32, -7.22, 0.36559, 1, 48, -1.31, -4.98, 1, 1, 48, -0.27, -0.29, 1, 1, 49, 1.88, 0.38, 1, 1, 49, 17.72, 0.18, 1, 2, 48, 21.6, 0.64, 0.85904, 49, -0.83, 0.47, 0.14096, 1, 49, 4.83, 0.44, 1, 2, 49, 37.64, -0.86, 0.93602, 50, -0.85, -1.32, 0.06398, 1, 50, 9.92, -1.7, 1, 1, 50, 19.87, -1.13, 1], "hull": 28, "edges": [0, 2, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 0, 54, 0, 56, 50, 52, 52, 54, 2, 4, 4, 6, 48, 50, 58, 64, 6, 8, 46, 48, 8, 10, 66, 68, 68, 70, 70, 28], "width": 92, "height": 29}}, "mv_she_04": {"mv_she_04": {"type": "mesh", "uvs": [0.07244, 0.76483, 0, 0.84173, 0.00383, 0.94218, 0.10472, 1, 0.22714, 1, 0.32669, 0.92178, 0.47197, 0.85586, 0.49585, 0.83989, 0.51318, 0.81841, 0.68435, 0.64146, 0.7461, 0.62251, 0.8965, 0.44334, 0.887, 0.39532, 0.92393, 0.32282, 0.95192, 0.26786, 0.94875, 0.19397, 1, 0.08129, 0.944, 0.01848, 0.86009, 0, 0.79834, 0.06466, 0.71917, 0.08129, 0.70967, 0.18473, 0.70017, 0.27155, 0.73342, 0.31958, 0.68592, 0.32143, 0.58934, 0.44334, 0.56717, 0.508, 0.45634, 0.62068, 0.43259, 0.65023, 0.393, 0.6724, 0.26159, 0.74813, 0.17292, 0.78138, 0.08584, 0.9033, 0.19984, 0.88852, 0.46425, 0.74444, 0.63367, 0.56895, 0.80784, 0.36022, 0.849, 0.20136, 0.89809, 0.09422, 0.76034, 0.14964, 0.77142, 0.22722, 0.83101, 0.27079], "triangles": [38, 18, 17, 38, 17, 16, 19, 18, 38, 39, 20, 19, 21, 20, 39, 15, 38, 16, 37, 19, 38, 37, 38, 15, 39, 19, 37, 40, 39, 37, 21, 39, 40, 37, 15, 14, 41, 40, 37, 41, 37, 14, 22, 21, 40, 23, 22, 40, 23, 40, 41, 13, 41, 14, 36, 23, 41, 12, 36, 41, 13, 12, 41, 11, 36, 12, 35, 25, 24, 26, 25, 35, 35, 24, 23, 35, 23, 36, 10, 35, 36, 11, 10, 36, 9, 35, 10, 35, 34, 27, 35, 27, 26, 34, 35, 9, 28, 27, 34, 8, 34, 9, 29, 28, 34, 7, 34, 8, 6, 34, 7, 33, 31, 30, 34, 33, 30, 34, 30, 29, 32, 0, 31, 32, 31, 33, 1, 0, 32, 5, 33, 34, 5, 34, 6, 2, 1, 32, 3, 32, 33, 2, 32, 3, 4, 33, 5, 3, 33, 4], "vertices": [1, 54, -1.14, 9.92, 1, 1, 54, -8.87, 7.1, 1, 1, 54, -11.31, 0.29, 1, 1, 54, -5.04, -6.78, 1, 1, 54, 4.48, -10.67, 1, 1, 54, 14.35, -8.62, 1, 2, 54, 27.44, -8.85, 0.85886, 55, -5.7, -7.32, 0.14114, 2, 54, 29.73, -8.55, 0.71704, 55, -3.45, -7.84, 0.28296, 2, 54, 31.67, -7.67, 0.52248, 55, -1.33, -7.68, 0.47752, 1, 55, 17.88, -8.05, 1, 1, 55, 22.62, -10.56, 1, 2, 55, 40.65, -9.63, 0.57062, 56, -1.18, -9.85, 0.42938, 2, 55, 42.41, -6.55, 0.30212, 56, 1.55, -7.59, 0.69788, 2, 55, 48.22, -4.81, 0.00452, 56, 7.61, -8.03, 0.99548, 1, 56, 12.2, -8.37, 1, 1, 56, 16.84, -5.75, 1, 1, 56, 26.02, -5.97, 1, 1, 56, 27.96, 0.26, 1, 1, 56, 26, 7.16, 1, 1, 56, 19.52, 9.72, 1, 1, 56, 15.47, 15.13, 1, 2, 55, 41.72, 14.7, 0.0086, 56, 8.45, 12.51, 0.9914, 2, 55, 36.9, 10.64, 0.11249, 56, 2.51, 10.43, 0.88751, 2, 55, 36.61, 6.2, 0.49758, 56, 0.66, 6.38, 0.50242, 2, 55, 33.59, 8.81, 0.90198, 56, -1.24, 9.89, 0.09802, 1, 55, 21.67, 7.85, 1, 1, 55, 17.15, 5.68, 1, 2, 54, 32.64, 7.32, 0.09244, 55, 4.8, 6.02, 0.90756, 2, 54, 29.99, 6.11, 0.37988, 55, 1.89, 5.81, 0.62012, 2, 54, 26.31, 5.89, 0.84581, 55, -1.63, 6.89, 0.15419, 1, 54, 14.02, 5.02, 1, 1, 54, 6.22, 5.62, 1, 1, 54, -3.87, 0.27, 1, 1, 54, 5.4, -2.37, 1, 2, 54, 29.88, -1.18, 0.6996, 55, -0.74, -0.98, 0.3004, 1, 55, 18.28, -1.33, 1, 2, 55, 39.23, -0.18, 0.11373, 56, 0.84, -0.52, 0.88627, 1, 56, 12.62, 1.51, 1, 1, 56, 21.36, 1.27, 1, 1, 56, 12.62, 9.83, 1, 2, 55, 43.46, 8.93, 0.01586, 56, 8.04, 6.5, 0.98414, 2, 55, 45.02, 3.23, 0.00015, 56, 7.47, 0.62, 0.99985], "hull": 32, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 0, 62, 64, 66, 66, 68, 68, 70, 70, 72, 74, 76, 72, 82, 82, 74, 24, 26, 26, 28], "width": 84, "height": 72}}, "mv_she_05": {"mv_she_05": {"type": "mesh", "uvs": [0.73747, 0, 0.79722, 0.02081, 0.82112, 0.07304, 1, 0.22102, 1, 0.43691, 0.85996, 0.65627, 0.65082, 0.85997, 0.40882, 0.94006, 0.08915, 1, 0, 0.97488, 0.01445, 0.92787, 0.31919, 0.85126, 0.48053, 0.7764, 0.6329, 0.58489, 0.82112, 0.37597, 0.80618, 0.20883, 0.66576, 0.06781, 0.68369, 0, 0.74793, 0.07392, 0.9186, 0.22465, 0.9026, 0.40491, 0.7266, 0.62401, 0.54527, 0.81826, 0.35594, 0.89596], "triangles": [12, 13, 21, 22, 12, 21, 6, 22, 21, 6, 21, 5, 22, 23, 11, 22, 11, 12, 7, 23, 22, 7, 22, 6, 23, 9, 10, 23, 10, 11, 8, 9, 23, 8, 23, 7, 21, 13, 20, 18, 0, 1, 18, 1, 2, 17, 0, 18, 19, 15, 18, 16, 17, 18, 15, 16, 18, 2, 19, 18, 3, 19, 2, 14, 15, 19, 20, 14, 19, 20, 19, 3, 4, 20, 3, 13, 14, 20, 21, 20, 4, 5, 21, 4], "vertices": [1, 66, 81.32, -1.51, 1, 1, 66, 78.3, -6.27, 1, 1, 66, 70.99, -8.01, 1, 2, 66, 50.05, -21.95, 0.92124, 65, -52.5, -23.9, 0.07876, 2, 66, 20.05, -21.16, 0.73136, 65, -31.91, -2.07, 0.26864, 2, 66, -10.13, -9.02, 0.49438, 65, -2.73, 12.33, 0.50562, 2, 66, -37.99, 8.66, 0.29581, 65, 29.02, 21.31, 0.70419, 2, 66, -48.6, 28.55, 0.03235, 65, 50.91, 15.96, 0.96765, 1, 65, 75.47, 4.25, 1, 1, 65, 78.33, -3.24, 1, 1, 65, 72.99, -7.19, 1, 2, 66, -36.07, 35.48, 0.03355, 65, 47.73, 2, 0.96645, 2, 66, -26.01, 22.14, 0.23421, 65, 31.08, 3.39, 0.76579, 2, 66, 0.27, 9.1, 0.48999, 65, 3.84, -7.51, 0.51001, 2, 66, 28.9, -6.9, 0.73377, 65, -27.18, -18.17, 0.26623, 2, 66, 52.16, -6.31, 0.92244, 65, -42.24, -35.91, 0.07756, 1, 66, 72.05, 4.55, 1, 1, 66, 81.44, 2.85, 1, 1, 66, 71.03, -2.08, 1, 2, 66, 49.72, -15.35, 0.88889, 65, -47.35, -28.06, 0.11111, 2, 66, 24.71, -13.39, 0.66667, 65, -29.22, -10.72, 0.33333, 2, 66, -5.36, 1.66, 0.50133, 65, 2.05, 1.66, 0.49867, 2, 66, -31.97, 17.06, 0.21511, 65, 31.26, 11.22, 0.78489, 1, 65, 49.82, 8.56, 1], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 18, 46], "width": 81, "height": 139}}, "mv_she_06": {"mv_she_06": {"type": "mesh", "uvs": [0.0149, 0.08365, 0.078, 0.04401, 0.22506, 0.28312, 0.32583, 0.44698, 0.35337, 0.49653, 0.3832, 0.4668, 0.44746, 0.37432, 0.64251, 0.10017, 0.67463, 0.15962, 0.74118, 0.11008, 0.82264, 0.03741, 0.88919, 0.00768, 0.96148, 0.10677, 1, 0.27192, 1, 0.41726, 0.96721, 0.53286, 0.89149, 0.64186, 0.82609, 0.55268, 0.77101, 0.52956, 0.73315, 0.4668, 0.69643, 0.52295, 0.6884, 0.59892, 0.54383, 0.87307, 0.49909, 0.86977, 0.41877, 0.98538, 0.3832, 1, 0.34304, 0.98538, 0.1652, 0.65507, 0.01146, 0.3512, 0, 0.22238, 0.04014, 0.19926, 0.19733, 0.48662, 0.36829, 0.75086, 0.45778, 0.64186, 0.64021, 0.38092, 0.69758, 0.30165, 0.81461, 0.28513, 0.91457, 0.29504], "triangles": [36, 9, 10, 37, 11, 12, 37, 12, 13, 36, 10, 11, 37, 36, 11, 35, 9, 36, 37, 13, 14, 18, 19, 36, 15, 37, 14, 17, 36, 37, 16, 17, 37, 18, 36, 17, 15, 16, 37, 35, 8, 9, 34, 6, 7, 34, 7, 8, 34, 8, 35, 19, 35, 36, 20, 34, 35, 20, 35, 19, 21, 34, 20, 33, 6, 34, 5, 6, 33, 32, 4, 5, 32, 5, 33, 3, 4, 32, 22, 23, 33, 34, 22, 33, 21, 22, 34, 24, 32, 33, 24, 33, 23, 25, 26, 32, 24, 25, 32, 30, 0, 1, 29, 0, 30, 28, 29, 30, 2, 30, 1, 31, 2, 3, 31, 30, 2, 28, 30, 31, 27, 28, 31, 31, 3, 32, 32, 27, 31, 26, 27, 32], "vertices": [1, 61, -7.32, 0.67, 1, 1, 61, -3.04, 5.07, 1, 1, 61, 12.96, 6.2, 1, 2, 61, 23.93, 6.98, 0.87226, 62, 1.36, 10.59, 0.12774, 2, 61, 27.01, 7.06, 0.57675, 62, 3.13, 8.06, 0.42325, 2, 61, 28.84, 9.45, 0.22249, 62, 6.12, 7.86, 0.77751, 2, 61, 32.24, 15.36, 0.00646, 62, 12.93, 8.29, 0.99354, 1, 62, 33.53, 9.4, 1, 2, 62, 35.57, 6.4, 0.98444, 63, -7, 3.84, 0.01556, 2, 62, 42.03, 5.44, 0.35498, 63, -0.73, 5.65, 0.64502, 1, 63, 6.94, 8.27, 1, 1, 63, 13.23, 9.43, 1, 1, 63, 20.19, 6.35, 1, 1, 63, 24, 1.01, 1, 1, 63, 24.14, -3.78, 1, 1, 63, 21.13, -7.69, 1, 1, 63, 14.04, -11.49, 1, 2, 62, 43.76, -11.15, 0.033, 63, 7.75, -8.72, 0.967, 2, 62, 39.24, -8.41, 0.25993, 63, 2.5, -8.11, 0.74007, 2, 62, 36.74, -5.1, 0.74064, 63, -1.16, -6.14, 0.25936, 2, 62, 32.8, -5.45, 0.98887, 63, -4.59, -8.09, 0.01113, 1, 62, 31.12, -7.46, 1, 1, 62, 14.95, -10.43, 1, 1, 62, 11.08, -8.68, 1, 2, 61, 41.1, -2.96, 0.0867, 62, 2.56, -9.21, 0.9133, 2, 61, 38.55, -5.22, 0.26168, 62, -0.74, -8.34, 0.73832, 2, 61, 35.1, -6.93, 0.56209, 62, -4.06, -6.41, 0.43791, 1, 61, 15, -7.17, 1, 1, 61, -2.71, -6.87, 1, 1, 61, -5.97, -3.93, 1, 1, 61, -3.21, -1.19, 1, 1, 61, 14.47, -0.85, 1, 1, 62, 1.16, -0.22, 1, 1, 62, 10.39, -0.22, 1, 1, 62, 29.71, 0.95, 1, 1, 62, 35.75, 1.24, 1, 1, 63, 6.41, 0.07, 1, 1, 63, 15.91, 0.02, 1], "hull": 30, "edges": [0, 2, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 2, 4, 4, 6, 54, 56, 56, 58, 0, 58, 0, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74], "width": 95, "height": 33}}, "mv_she_07": {"mv_she_07": {"type": "mesh", "uvs": [0.03634, 0.01804, 0.11706, 0.00701, 0.20762, 0.05111, 0.36709, 0.32674, 0.39268, 0.35824, 0.42418, 0.40391, 0.69981, 0.73308, 0.72147, 0.75671, 0.7569, 0.76143, 0.877, 0.77718, 0.97937, 0.85908, 1, 0.99768, 0.91637, 1, 0.81006, 1, 0.71162, 0.94571, 0.64862, 0.86538, 0.62893, 0.83861, 0.58956, 0.81341, 0.30409, 0.54251, 0.27259, 0.49369, 0.23715, 0.45274, 0.01075, 0.15349, 0, 0.06686, 0.08359, 0.09206, 0.34937, 0.46376, 0.67618, 0.80081, 0.76281, 0.85751, 0.88684, 0.91263], "triangles": [11, 12, 27, 12, 13, 27, 13, 14, 27, 27, 10, 11, 27, 9, 10, 14, 26, 27, 14, 15, 26, 26, 9, 27, 15, 25, 26, 26, 25, 8, 26, 8, 9, 15, 16, 25, 25, 7, 8, 16, 17, 25, 25, 17, 24, 25, 6, 7, 6, 25, 24, 24, 17, 18, 24, 5, 6, 18, 19, 24, 19, 20, 24, 5, 24, 4, 23, 24, 20, 21, 23, 20, 4, 24, 3, 23, 3, 24, 3, 23, 2, 21, 22, 23, 22, 0, 23, 23, 1, 2, 23, 0, 1], "vertices": [1, 57, -6.7, -0.87, 1, 1, 57, -4.95, 4.07, 1, 1, 57, 0.95, 7.41, 1, 1, 57, 25.18, 5.58, 1, 2, 57, 28.18, 5.78, 0.98778, 58, -5.6, 6.57, 0.01222, 2, 57, 32.35, 5.76, 0.74683, 58, -1.47, 6.01, 0.25317, 2, 58, 30.19, 4.43, 0.92683, 59, -1.65, 6.24, 0.07317, 2, 58, 32.53, 4.42, 0.65571, 59, 0.33, 4.99, 0.34429, 2, 58, 34.19, 6.01, 0.26943, 59, 2.58, 5.47, 0.73057, 2, 59, 10.19, 7.12, 0.81972, 60, -4.28, 5.79, 0.18028, 2, 59, 18.69, 3.42, 0.01206, 60, 4.96, 6.48, 0.98794, 1, 60, 14.23, 0.25, 1, 2, 59, 19.07, -8.54, 0.0256, 60, 10.88, -3.93, 0.9744, 2, 59, 12.74, -11.04, 0.29935, 60, 6.44, -9.08, 0.70065, 3, 58, 44.28, -5.12, 0.00073, 59, 5.29, -9.31, 0.77599, 60, -0.96, -11.03, 0.22328, 3, 58, 36.72, -4.51, 0.39904, 59, -0.82, -4.8, 0.59337, 60, -8.46, -9.89, 0.0076, 3, 58, 34.25, -4.25, 0.8441, 59, -2.78, -3.27, 0.15574, 60, -10.91, -9.45, 0.00016, 1, 58, 31.13, -5.06, 1, 2, 57, 38.29, -6.35, 0.13465, 58, 2.84, -6.78, 0.86535, 2, 57, 33.9, -6.21, 0.64, 58, -1.5, -6.07, 0.36, 2, 57, 29.93, -6.59, 0.96249, 58, -5.48, -5.93, 0.03751, 1, 57, 1.97, -7.58, 1, 1, 57, -4.42, -4.81, 1, 1, 57, -0.05, -1.12, 1, 2, 57, 34.2, -0.75, 0.7298, 58, -0.49, -0.69, 0.2702, 1, 58, 33.63, -0.02, 1, 2, 59, 5.75, -1.54, 0.97692, 60, -4.18, -3.94, 0.02308, 2, 59, 14.75, -2.73, 0.02121, 60, 4.34, -0.8, 0.97879], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44, 46, 48, 48, 50, 50, 52, 52, 54], "width": 64, "height": 80}}, "mv_she_08": {"mv_she_08": {"type": "mesh", "uvs": [0.29862, 0.18103, 0.29862, 0.10711, 0.30002, 0.03627, 0.41202, 0, 0.53521, 0.02703, 0.53661, 0.11122, 0.53381, 0.18309, 0.53241, 0.25495, 0.65001, 0.29602, 0.72841, 0.33606, 0.77461, 0.43051, 0.89064, 0.49221, 1, 0.56565, 1, 0.65421, 0.98489, 0.74277, 0.96133, 0.76437, 0.95542, 0.84889, 0.96506, 0.92097, 0.89278, 0.96337, 0.781, 0.99022, 0.63865, 0.98764, 0.49132, 0.97596, 0.36258, 0.93995, 0.34134, 0.85332, 0.29489, 0.75404, 0.19667, 0.65476, 0.08783, 0.54867, 0, 0.44355, 0.00023, 0.33453, 0.09181, 0.26737, 0.198, 0.25375, 0.22454, 0.20216, 0.4223, 0.11261, 0.42894, 0.18561, 0.42761, 0.25277, 0.48867, 0.34037, 0.636, 0.4416, 0.7435, 0.53893, 0.79925, 0.64113, 0.78067, 0.7628, 0.77403, 0.86208, 0.16747, 0.38125, 0.27498, 0.47469, 0.39576, 0.55645, 0.4754, 0.67715, 0.5046, 0.7774, 0.52318, 0.87668, 0.24047, 0.32383, 0.39045, 0.40461, 0.69838, 0.40559, 0.38249, 0.3248, 0.30418, 0.26543, 0.62538, 0.37152], "triangles": [17, 18, 16, 21, 22, 46, 19, 20, 40, 21, 46, 20, 18, 19, 40, 52, 35, 8, 52, 8, 9, 49, 52, 9, 49, 9, 10, 36, 52, 49, 10, 36, 49, 37, 10, 11, 47, 41, 29, 47, 29, 30, 28, 29, 41, 27, 28, 41, 41, 47, 42, 26, 27, 41, 26, 41, 42, 25, 26, 42, 32, 3, 4, 32, 4, 5, 2, 3, 32, 1, 2, 32, 0, 1, 32, 6, 32, 5, 33, 32, 6, 0, 32, 33, 34, 0, 33, 7, 33, 6, 34, 33, 7, 51, 0, 34, 31, 0, 51, 30, 31, 51, 47, 30, 51, 50, 51, 34, 47, 51, 50, 35, 34, 7, 50, 34, 35, 35, 7, 8, 48, 50, 35, 35, 52, 36, 48, 42, 47, 48, 47, 50, 37, 36, 10, 36, 43, 48, 36, 48, 35, 42, 48, 43, 25, 42, 43, 37, 44, 43, 37, 43, 36, 25, 43, 44, 38, 37, 11, 38, 11, 12, 38, 12, 13, 44, 37, 38, 14, 38, 13, 24, 25, 44, 39, 44, 38, 15, 39, 38, 14, 15, 38, 45, 44, 39, 24, 44, 45, 16, 39, 15, 23, 24, 45, 40, 45, 39, 40, 39, 16, 46, 45, 40, 23, 45, 46, 18, 40, 16, 22, 23, 46, 20, 46, 40], "vertices": [2, 41, 10.22, 6.58, 0.91506, 42, 14.63, -10.14, 0.08494, 2, 41, 15.76, 6.52, 0.99605, 42, 20.16, -10.5, 0.00395, 1, 41, 21.07, 6.38, 1, 1, 41, 23.73, 0.19, 1, 1, 41, 21.63, -6.56, 1, 1, 41, 15.31, -6.57, 1, 3, 40, 32.41, -9.75, 0.00083, 41, 9.92, -6.36, 0.98936, 43, 13.34, 7.91, 0.00981, 3, 40, 27.5, -7.51, 0.13022, 41, 4.53, -6.22, 0.72748, 43, 7.97, 8.34, 0.14231, 3, 40, 22.08, -12.18, 0.11956, 41, 1.39, -12.66, 0.11227, 43, 4.47, 2.08, 0.76817, 3, 39, 43.64, -8.51, 0.00039, 41, -1.66, -16.94, 0.00091, 43, 1.19, -2.02, 0.99871, 3, 39, 36.36, -10.41, 0.09082, 40, 10.08, -14.39, 0.29585, 43, -6.04, -4.09, 0.61333, 3, 39, 31.18, -16.35, 0.39079, 40, 3.28, -18.36, 0.30729, 43, -11.08, -10.16, 0.30191, 3, 39, 25.29, -22.01, 0.6356, 40, -4.11, -21.84, 0.17549, 43, -16.83, -15.96, 0.18891, 3, 39, 19.29, -22.81, 0.73975, 40, -10.05, -20.68, 0.11531, 43, -22.81, -16.9, 0.14494, 3, 39, 13.21, -19.42, 0.85527, 40, -14.73, -15.53, 0.05401, 43, -28.97, -13.66, 0.09073, 3, 39, 10.47, -16.15, 0.93283, 40, -16.28, -11.55, 0.01839, 43, -31.78, -10.46, 0.04877, 3, 39, 3.86, -16.16, 0.99122, 40, -22.54, -9.44, 3e-05, 43, -38.39, -10.62, 0.00875, 1, 32, 16.8, 2.08, 1, 1, 32, 12.85, -1.13, 1, 1, 32, 6.71, -3.18, 1, 1, 32, -1.12, -3.04, 1, 1, 32, -9.23, -2.21, 1, 1, 32, -16.33, 0.44, 1, 3, 39, 6.9, 16.16, 0.87382, 40, -9.33, 20.2, 0.09747, 42, -35.84, -9.18, 0.02871, 3, 39, 14.54, 18.04, 0.55145, 40, -1.48, 19.54, 0.33066, 42, -28.24, -7.12, 0.11789, 3, 39, 22.44, 22.76, 0.17472, 40, 7.51, 21.48, 0.47833, 42, -20.46, -2.21, 0.34695, 3, 39, 30.9, 28.01, 0.02778, 40, 17.2, 23.75, 0.26088, 42, -12.13, 3.24, 0.71134, 3, 39, 39.18, 32.12, 0.00102, 40, 26.37, 24.99, 0.02374, 42, -3.94, 7.54, 0.97523, 2, 41, -1.12, 23.11, 0.01298, 42, 4.21, 6.99, 0.98702, 2, 41, 3.86, 18.02, 0.14631, 42, 8.91, 1.64, 0.85369, 3, 40, 35, 9.29, 0.01275, 41, 4.82, 12.17, 0.5002, 42, 9.55, -4.26, 0.48705, 3, 40, 37.95, 6.39, 3e-05, 41, 8.67, 10.67, 0.75556, 42, 13.31, -5.97, 0.2444, 1, 41, 15.27, -0.28, 1, 2, 41, 9.8, -0.59, 0.99945, 43, 13.53, 13.67, 0.00055, 3, 40, 29.98, -2.3, 0.00256, 41, 4.76, -0.46, 0.99439, 43, 8.51, 14.08, 0.00306, 3, 40, 22.61, -2.72, 0.77473, 41, -1.85, -3.75, 0.13412, 43, 1.73, 11.16, 0.09115, 3, 39, 36.21, -2.74, 0.00786, 40, 12.4, -7.08, 0.57214, 43, -6.37, 3.57, 0.42, 3, 39, 28.41, -7.98, 0.24694, 40, 3.33, -9.54, 0.51214, 43, -14.04, -1.85, 0.24092, 3, 39, 20.5, -10.35, 0.73489, 40, -4.92, -9.26, 0.16309, 43, -21.89, -4.41, 0.10202, 3, 39, 11.58, -9.26, 0.96886, 40, -13.02, -5.38, 0.00528, 43, -30.83, -3.54, 0.02587, 2, 39, 4.19, -8.38, 0.99548, 43, -38.25, -2.84, 0.00452, 4, 39, 43.01, 22.53, 9e-05, 40, 26.93, 14.68, 0.06581, 41, -4.72, 13.95, 0.03915, 42, 0.12, -1.95, 0.89495, 4, 39, 35.51, 17.26, 0.01256, 40, 18.13, 12.1, 0.52859, 41, -11.79, 8.11, 0.00211, 42, -7.26, -7.4, 0.45674, 3, 39, 28.81, 11.19, 0.06253, 40, 9.84, 8.49, 0.78415, 42, -13.82, -13.62, 0.15332, 3, 39, 19.4, 7.64, 0.53466, 40, -0.21, 8.13, 0.41672, 42, -23.14, -17.4, 0.04862, 3, 39, 11.77, 6.71, 0.89768, 40, -7.74, 9.69, 0.08455, 42, -30.75, -18.51, 0.01777, 3, 39, 4.26, 6.35, 0.98332, 40, -14.96, 11.76, 0.0128, 42, -38.24, -19.04, 0.00389, 3, 40, 29.25, 9.27, 0.13937, 41, -0.46, 9.89, 0.33464, 42, 4.15, -6.24, 0.52599, 3, 40, 20.38, 4.17, 0.88182, 41, -6.61, 1.7, 0.00412, 42, -2.44, -14.08, 0.11406, 3, 39, 38.59, -6.4, 0.01613, 40, 13.49, -11.3, 0.21712, 43, -3.9, -0.03, 0.76676, 3, 40, 26.03, 2.15, 0.50223, 41, -0.62, 2.08, 0.43464, 42, 3.57, -14.03, 0.06314, 3, 40, 31.85, 4.3, 0.0365, 41, 3.88, 6.34, 0.76018, 42, 8.29, -10.03, 0.20332, 3, 40, 17.44, -8.66, 0.30911, 41, -4.26, -11.24, 0.02584, 43, -1.09, 3.81, 0.66506], "hull": 32, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 0, 62, 6, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92], "width": 55, "height": 75}}, "mv_she_09": {"mv_she_09": {"type": "mesh", "uvs": [0.45139, 0.01532, 0.54123, 0.01616, 0.6694, 0.09103, 0.78106, 0.15624, 0.86861, 0.20738, 0.97178, 0.30405, 0.99507, 0.41251, 0.99492, 0.61577, 0.98259, 0.66254, 0.95898, 0.69863, 0.90259, 0.69884, 0.84599, 0.65849, 0.75705, 0.59508, 0.68303, 0.57605, 0.57626, 0.54859, 0.44742, 0.54365, 0.34866, 0.53986, 0.23704, 0.54028, 0.1952, 0.57941, 0.15197, 0.65729, 0.104, 0.80825, 0.09241, 0.96844, 0.07208, 0.96939, 0.04178, 0.876, 0.02549, 0.73787, 0.02549, 0.47767, 0.04327, 0.3102, 0.08688, 0.17317, 0.1769, 0.07218, 0.2513, 0.03425, 0.35934, 0.01446, 0.95419, 0.52922, 0.89441, 0.45465, 0.84266, 0.40218, 0.76859, 0.38008, 0.67936, 0.3138, 0.60352, 0.2917, 0.51161, 0.26684, 0.41613, 0.24475, 0.31798, 0.21989, 0.23856, 0.27513, 0.15915, 0.34418, 0.08027, 0.47122, 0.07313, 0.65903, 0.06778, 0.8358], "triangles": [20, 43, 19, 44, 43, 20, 23, 24, 44, 21, 44, 20, 22, 44, 21, 23, 44, 22, 25, 26, 42, 19, 42, 41, 43, 25, 42, 43, 42, 19, 24, 25, 43, 44, 24, 43, 41, 27, 28, 42, 26, 27, 42, 27, 41, 18, 19, 41, 40, 28, 29, 41, 28, 40, 17, 41, 40, 18, 41, 17, 39, 29, 30, 40, 29, 39, 17, 40, 39, 17, 39, 16, 38, 30, 0, 39, 30, 38, 16, 39, 38, 16, 38, 15, 37, 0, 1, 38, 0, 37, 15, 38, 37, 15, 37, 14, 36, 1, 2, 37, 1, 36, 14, 37, 36, 14, 36, 13, 35, 2, 3, 36, 2, 35, 13, 35, 34, 36, 35, 13, 34, 35, 3, 33, 34, 3, 12, 13, 34, 12, 34, 33, 4, 33, 3, 32, 4, 5, 33, 4, 32, 11, 33, 32, 11, 12, 33, 31, 32, 5, 31, 5, 6, 7, 31, 6, 8, 31, 7, 9, 31, 8, 10, 32, 31, 10, 31, 9, 11, 32, 10], "vertices": [2, 15, 14.35, -10.53, 0.70834, 16, -2.33, -10.47, 0.29166, 2, 14, 15.46, -11.28, 0.57121, 15, -1.25, -11.25, 0.42879, 2, 13, 9.83, -8.55, 0.9687, 14, -6.89, -8.27, 0.0313, 2, 11, 24.18, -6.47, 0.0022, 12, 7.06, -7.35, 0.9978, 2, 11, 8.82, -7.85, 0.99631, 12, -8.32, -6.9, 0.00369, 2, 10, 9.28, -10.6, 0.99597, 11, -9.81, -7.26, 0.00403, 1, 10, 2.32, -7.64, 1, 2, 10, -4.37, 2.83, 0.99927, 11, -18.24, 10.51, 0.00073, 2, 10, -4.14, 6.63, 0.99189, 11, -16.87, 13.98, 0.00811, 3, 10, -1.91, 11.14, 0.9621, 11, -13.46, 17.39, 0.03785, 12, -28.11, 20.82, 5e-05, 3, 10, 6.25, 17.5, 0.72168, 11, -4.06, 20.29, 0.25828, 12, -18.5, 22.6, 0.02005, 4, 10, 15.78, 21.79, 0.29351, 11, 5.98, 20.73, 0.5439, 12, -8.49, 21.86, 0.16143, 13, -23.91, 23.41, 0.00116, 5, 10, 30.75, 28.53, 0.0096, 11, 21.75, 21.43, 0.19305, 12, 7.23, 20.69, 0.627, 13, -8.22, 21.25, 0.16865, 14, -23.89, 22.26, 0.0017, 5, 11, 34.38, 24.07, 0.00893, 12, 20.01, 21.83, 0.28699, 13, 4.64, 21.56, 0.60163, 14, -11.07, 22.04, 0.10245, 15, -27.25, 22.78, 1e-05, 5, 12, 38.45, 23.46, 0.00307, 13, 23.2, 22.02, 0.21783, 14, 7.42, 21.73, 0.62684, 15, -8.64, 21.96, 0.14993, 16, -24.4, 22.67, 0.00232, 4, 14, 29.63, 23.12, 0.09126, 15, 13.74, 22.76, 0.52082, 16, -2.19, 22.83, 0.36229, 17, -17.17, 24.25, 0.02563, 7, 14, 46.65, 24.19, 0.0001, 15, 30.9, 23.36, 0.07105, 16, 14.83, 22.94, 0.50524, 17, -0.21, 23.02, 0.37851, 18, -13.45, 25.61, 0.04455, 19, -18.28, 37.31, 0.00022, 21, 31.16, 43.23, 0.00032, 6, 16, 34.06, 23.37, 0.03845, 17, 18.97, 21.92, 0.30609, 18, 5.41, 21.24, 0.47171, 19, -2.36, 24.51, 0.11494, 20, 5.15, 31.65, 0.03824, 21, 16.11, 29.05, 0.03056, 6, 16, 41.23, 25.99, 0.00351, 17, 26.27, 23.96, 0.09137, 18, 12.91, 22.01, 0.39163, 19, 4.92, 21.7, 0.26976, 20, 7.83, 23.65, 0.14402, 21, 11.93, 21.88, 0.0997, 5, 17, 33.92, 28.43, 0.01095, 18, 21.09, 25.11, 0.12348, 19, 13.7, 20.7, 0.21158, 20, 12.72, 15.53, 0.3269, 21, 9, 12.67, 0.32708, 1, 21, 8.16, -0.64, 1, 5, 17, 45.04, 47.4, 0, 18, 34.65, 41.96, 0.00012, 19, 32.63, 29.7, 0.00026, 20, 31.09, 5.1, 0, 21, 12.57, -9.78, 0.99962, 4, 17, 48.54, 47.26, 0, 18, 38.1, 41.22, 0.00011, 19, 35.56, 27.42, 0.00024, 21, 9.87, -12.4, 0.99965, 5, 17, 53.48, 41.08, 0, 18, 42.16, 34.27, 4e-05, 19, 36.74, 19.18, 0.0001, 20, 26.35, -5.1, 0.05327, 21, 2.29, -11.77, 0.94659, 5, 17, 55.88, 32.22, 0, 18, 43.36, 25.1, 0, 19, 34.43, 10.28, 1e-05, 20, 18.65, -8.79, 0.55702, 21, -5.06, -7.23, 0.44297, 2, 19, 25.7, -2.97, 0.02024, 20, 3.81, -9.77, 0.97976, 2, 19, 17.55, -9.45, 0.76563, 20, -5.93, -6.93, 0.23437, 2, 18, 26.64, -7.32, 0.00504, 19, 6.73, -11.42, 0.99496, 3, 17, 27.96, -8.14, 9e-05, 18, 10.3, -10.01, 0.98136, 19, -9.49, -6.23, 0.01856, 2, 17, 15.06, -9.77, 0.69887, 18, -2.7, -9.42, 0.30113, 2, 16, 13.53, -10.19, 0.80496, 17, -3.56, -9.93, 0.19504, 2, 10, 4.38, 2.96, 0.98555, 11, -10.18, 7.33, 0.01445, 3, 10, 15.5, 5.85, 0.47539, 11, 0.89, 5.86, 0.51579, 12, -14.92, 7.67, 0.00882, 3, 10, 24.73, 8.98, 0.02902, 11, 10.3, 5.32, 0.88085, 12, -5.63, 6.02, 0.09013, 4, 10, 36.18, 16.17, 0.0005, 11, 22.98, 7.77, 0.06677, 12, 7.18, 6.96, 0.88729, 13, -8.95, 7.54, 0.04545, 3, 12, 22.98, 5.63, 0.0367, 13, 6.81, 5.21, 0.94656, 14, -9.45, 5.61, 0.01674, 3, 13, 20, 5.37, 0.16093, 14, 3.7, 5.22, 0.83409, 15, -12.74, 5.56, 0.00498, 4, 13, 35.98, 5.68, 0.00012, 14, 19.62, 4.87, 0.18107, 15, 3.28, 4.78, 0.81418, 16, -12.97, 5.16, 0.00463, 4, 14, 36.14, 4.74, 0.00046, 15, 19.91, 4.21, 0.14395, 16, 3.51, 4.11, 0.85145, 17, -12.65, 5.13, 0.00415, 4, 16, 20.44, 2.9, 0.06628, 17, 4.14, 2.58, 0.93079, 18, -11.87, 4.66, 0.00292, 21, 15.08, 54.65, 1e-05, 6, 16, 34.07, 6.67, 0.00486, 17, 17.94, 5.25, 0.28968, 18, 2.16, 4.94, 0.69046, 19, -11.47, 11.19, 0.01031, 20, -9.98, 30.95, 0.00209, 21, 6.43, 41.94, 0.00259, 5, 17, 31.79, 8.8, 0.00728, 18, 16.35, 6.07, 0.38578, 19, 2.17, 5.58, 0.56332, 20, -5.21, 15.75, 0.02982, 21, -1.71, 28.56, 0.0138, 4, 18, 31.11, 10.81, 0.00127, 19, 17.67, 2.99, 0.03753, 20, 2.86, 0.87, 0.95878, 21, -7.61, 12.48, 0.00242, 3, 18, 34.43, 22.11, 0.00016, 19, 24.99, 11.74, 0.00048, 20, 13.65, 0.19, 0.99936, 5, 17, 48.9, 38.81, 0, 18, 37.32, 32.81, 4e-05, 19, 31.69, 20.12, 8e-05, 20, 23.79, -0.19, 0.01776, 21, 4.3, -6.55, 0.98212], "hull": 31, "edges": [8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 2, 0, 0, 60, 28, 30, 30, 32, 2, 4, 24, 26, 26, 28, 4, 6, 6, 8, 20, 22, 22, 24, 14, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88], "width": 195, "height": 63}}, "ss1": {"ss1": {"type": "mesh", "uvs": [0.29984, 0.31742, 0.39018, 0.29301, 0.52846, 0.29708, 0.67228, 0.32963, 0.69071, 0.25639, 0.97465, 0.26046, 1, 0.44356, 1, 0.64701, 0.98571, 0.85452, 0.90643, 0.9237, 0.67043, 0.80163, 0.64093, 0.6877, 0.52846, 0.63073, 0.47684, 0.61446, 0.44181, 0.72025, 0.38097, 0.88301, 0.28693, 1, 0.20581, 1, 0.20212, 0.80163, 0.09703, 0.82604, 0, 0.67142, 0, 0.36625, 0.02328, 0.17094, 0.1044, 0, 0.18184, 0, 0.2224, 0.21977, 0.10625, 0.23604, 0.121, 0.45577, 0.26481, 0.5819, 0.40678, 0.49646, 0.5469, 0.4639, 0.69993, 0.52494], "triangles": [26, 23, 24, 26, 24, 25, 22, 23, 26, 21, 22, 26, 27, 26, 25, 21, 26, 27, 27, 25, 28, 20, 21, 27, 19, 20, 27, 18, 19, 27, 28, 25, 0, 29, 28, 0, 29, 0, 1, 14, 29, 13, 18, 27, 28, 14, 15, 28, 14, 28, 29, 18, 28, 15, 16, 17, 18, 15, 16, 18, 30, 2, 3, 29, 1, 2, 29, 2, 30, 31, 4, 5, 31, 5, 6, 3, 4, 31, 30, 3, 31, 13, 29, 30, 12, 13, 30, 31, 6, 7, 11, 30, 31, 12, 30, 11, 10, 11, 31, 7, 9, 31, 9, 10, 31, 7, 8, 9], "vertices": [3, 45, 44.23, -3.1, 0.02478, 46, 5.52, -5.75, 0.80347, 47, -1.02, -7.98, 0.17176, 3, 45, 38.57, -4.45, 0.55475, 46, -0.3, -5.47, 0.4442, 47, -5.83, -11.28, 0.00106, 1, 45, 29.76, -5.32, 1, 1, 45, 20.51, -5.41, 1, 1, 45, 19.57, -7.65, 1, 1, 45, 1.5, -9.56, 1, 1, 45, -0.7, -4.46, 1, 1, 45, -1.36, 1.4, 1, 1, 45, -1.12, 7.48, 1, 1, 45, 3.69, 10.04, 1, 1, 45, 19.1, 8.21, 1, 1, 45, 21.34, 5.14, 1, 2, 45, 28.68, 4.3, 0.97779, 46, -7.38, 5.67, 0.02221, 2, 45, 32.02, 4.2, 0.72363, 46, -4.2, 4.65, 0.27637, 2, 45, 33.9, 7.49, 0.1873, 46, -1.48, 7.29, 0.8127, 3, 45, 37.25, 12.62, 0.00221, 46, 3.15, 11.29, 0.98725, 47, -13.2, 4.17, 0.01054, 2, 46, 9.66, 13.63, 0.89285, 47, -9.42, 9.96, 0.10715, 2, 46, 14.78, 12.76, 0.82329, 47, -4.82, 12.36, 0.17671, 2, 46, 14.04, 7.05, 0.51173, 47, -1.95, 7.36, 0.48827, 2, 46, 20.79, 6.62, 0.0161, 47, 3.69, 11.09, 0.9839, 1, 47, 11.27, 9.98, 1, 1, 47, 15.35, 2.13, 1, 1, 47, 16.64, -3.59, 1, 2, 46, 16.31, -16.92, 0.00217, 47, 14.32, -10.38, 0.99783, 2, 46, 11.42, -16.09, 0.0183, 47, 9.92, -12.67, 0.9817, 2, 46, 9.93, -9.37, 0.21809, 47, 4.68, -8.21, 0.78191, 2, 46, 17.34, -10.15, 0.00809, 47, 11.06, -4.36, 0.99191, 1, 47, 7.28, 0.86, 1, 1, 46, 9.02, 1.44, 1, 2, 45, 36.85, 1.3, 0.14027, 46, -0.35, 0.52, 0.85973, 1, 45, 28.05, -0.64, 1, 1, 45, 18.12, 0.02, 1], "hull": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 0, 50, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62], "width": 64, "height": 29}}, "ss2": {"ss2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [44, 13.58, 47.21, -5.15, -5.03, -14.1, -8.24, 4.63], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 19, "height": 53}}, "mv_she_020": {"mv_she_020": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-27.39, -20.2, -27.63, 17.8, 178.37, 19.09, 178.6, -18.9], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 38, "height": 206}}, "mv_she_biyan": {"mv_she_019": {"type": "mesh", "uvs": [0.53023, 0.96523, 0.674, 1, 0.83553, 1, 0.97398, 0.92529, 1, 0.68567, 0.9864, 0.3573, 0.86038, 0.19311, 0.7095, 0.24192, 0.56395, 0.26411, 0.43438, 0.15761, 0.3048, 0.05999, 0.14506, 0.03336, 0.03678, 0.12211, 0, 0.3928, 0.01726, 0.64573, 0.13086, 0.81879, 0.27463, 0.92973, 0.08648, 0.31736, 0.2196, 0.48599, 0.39888, 0.61911, 0.56573, 0.6768, 0.73613, 0.67236, 0.87813, 0.59692], "triangles": [0, 20, 1, 0, 19, 20, 19, 8, 20, 20, 8, 7, 1, 21, 2, 2, 22, 3, 2, 21, 22, 1, 20, 21, 16, 19, 0, 15, 18, 16, 16, 18, 19, 3, 22, 4, 14, 17, 15, 15, 17, 18, 22, 5, 4, 20, 7, 21, 21, 7, 22, 14, 13, 17, 18, 10, 19, 19, 9, 8, 19, 10, 9, 7, 6, 22, 22, 6, 5, 17, 11, 18, 18, 11, 10, 13, 12, 17, 17, 12, 11], "vertices": [2, 41, 26.18, -9.38, 0.7, 70, 6.95, 0.48, 0.3, 2, 41, 25.56, -15.13, 0.8, 70, 6.33, -5.26, 0.2, 2, 41, 25.49, -21.59, 0.9, 70, 6.27, -11.72, 0.1, 2, 41, 26.63, -27.14, 0.7, 71, -4.7, -15.25, 0.3, 2, 41, 30.45, -28.22, 0.7, 71, -0.88, -16.33, 0.3, 2, 41, 35.71, -27.73, 0.7, 71, 4.38, -15.84, 0.3, 2, 41, 38.39, -22.72, 0.9, 70, 19.16, -12.86, 0.1, 2, 41, 37.68, -16.67, 0.8, 70, 18.45, -6.81, 0.2, 2, 41, 37.38, -10.85, 0.7, 70, 18.15, -0.99, 0.3, 2, 41, 39.14, -5.68, 0.8, 70, 19.91, 4.18, 0.2, 2, 41, 40.76, -0.52, 0.9, 70, 21.53, 9.34, 0.1, 2, 41, 41.25, 5.87, 0.95, 70, 22.02, 15.73, 0.05, 2, 41, 39.88, 10.21, 0.7, 71, 8.55, 22.1, 0.3, 2, 41, 35.56, 11.73, 0.7, 71, 4.23, 23.62, 0.3, 3, 41, 31.51, 11.08, 0.665, 70, 12.28, 20.94, 0.035, 71, 0.18, 22.97, 0.3, 2, 41, 28.69, 6.57, 0.9, 70, 9.46, 16.43, 0.1, 2, 41, 26.86, 0.84, 0.8, 70, 7.63, 10.7, 0.2, 2, 41, 36.73, 8.26, 0.95, 70, 17.51, 18.12, 0.05, 2, 41, 33.98, 2.96, 0.9, 70, 14.75, 12.82, 0.1, 2, 41, 31.77, -4.19, 0.8, 70, 12.54, 5.68, 0.2, 2, 41, 30.78, -10.85, 0.7, 70, 11.55, -0.99, 0.3, 2, 41, 30.78, -17.67, 0.8, 70, 11.55, -7.8, 0.2, 2, 41, 31.92, -23.36, 0.9, 70, 12.7, -13.5, 0.1], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44], "width": 40, "height": 16}}, "mv_she_zhenyan": {"mv_she_018": {"type": "mesh", "uvs": [0, 0.22688, 0.09786, 0, 0.27536, 0, 0.45286, 0.19926, 0.60669, 0.31365, 0.74531, 0.27421, 0.88562, 0.17165, 1, 0.20321, 1, 0.5306, 0.95493, 0.85009, 0.87041, 1, 0.72503, 1, 0.57626, 0.96054, 0.38862, 0.93293, 0.20774, 0.89348, 0.07081, 0.71204, 0, 0.46354, 0.07927, 0.30971, 0.24324, 0.45171, 0.43427, 0.58976, 0.60331, 0.65682, 0.74024, 0.64104, 0.87379, 0.55821], "triangles": [11, 21, 10, 10, 22, 9, 10, 21, 22, 12, 20, 11, 11, 20, 21, 13, 19, 12, 12, 19, 20, 19, 13, 18, 13, 14, 18, 14, 15, 18, 9, 22, 8, 15, 17, 18, 15, 16, 17, 20, 4, 21, 20, 19, 4, 21, 5, 22, 21, 4, 5, 19, 3, 4, 19, 18, 3, 22, 6, 8, 22, 5, 6, 6, 7, 8, 16, 0, 17, 18, 17, 2, 18, 2, 3, 2, 17, 1, 17, 0, 1], "vertices": [3, 41, 37.79, 13.71, 0.5712, 70, 18.56, 23.57, 0.184, 71, 6.45, 25.59, 0.2448, 3, 41, 41.83, 9.55, 0.5712, 70, 22.6, 19.41, 0.184, 71, 10.49, 21.44, 0.2448, 2, 41, 41.75, 2.1, 0.852, 70, 22.52, 11.96, 0.148, 2, 41, 38.08, -5.32, 0.8, 70, 18.85, 4.54, 0.2, 2, 41, 35.95, -11.76, 0.8, 70, 16.72, -1.9, 0.2, 2, 41, 36.6, -17.59, 0.8, 70, 17.37, -7.72, 0.2, 3, 41, 38.38, -23.5, 0.7632, 70, 19.16, -13.64, 0.0848, 71, 7.05, -11.61, 0.152, 2, 41, 37.77, -28.3, 0.7, 71, 6.43, -16.41, 0.3, 2, 41, 31.87, -28.23, 0.7, 71, 0.54, -16.35, 0.3, 2, 41, 26.14, -26.28, 0.7, 71, -5.19, -14.39, 0.3, 3, 41, 23.48, -22.7, 0.7488, 70, 4.25, -12.84, 0.0832, 71, -7.85, -10.81, 0.168, 2, 41, 23.55, -16.59, 0.8, 70, 4.32, -6.73, 0.2, 2, 41, 24.32, -10.35, 0.8, 70, 5.1, -0.49, 0.2, 2, 41, 24.9, -2.48, 0.8, 70, 5.68, 7.38, 0.2, 2, 41, 25.7, 5.11, 0.852, 70, 6.47, 14.97, 0.148, 3, 41, 29.02, 10.83, 0.4928, 70, 9.79, 20.69, 0.296, 71, -2.31, 22.71, 0.2112, 3, 41, 33.53, 13.75, 0.5712, 70, 14.3, 23.61, 0.184, 71, 2.19, 25.64, 0.2448, 3, 41, 36.26, 10.39, 0.51658, 70, 17.03, 20.25, 0.4036, 71, 4.93, 22.28, 0.07982, 2, 41, 33.63, 3.53, 0.852, 70, 14.4, 13.4, 0.148, 2, 41, 31.06, -4.46, 0.8, 70, 11.83, 5.4, 0.2, 2, 41, 29.78, -11.55, 0.8, 70, 10.55, -1.69, 0.2, 2, 41, 30, -17.3, 0.8, 70, 10.77, -7.44, 0.2, 3, 41, 31.43, -22.93, 0.8136, 70, 12.2, -13.07, 0.0904, 71, 0.1, -11.04, 0.096], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44], "width": 42, "height": 18}}, "ef2/qf_njs_jn2_xl_02": {"ef2/qf_njs_jn2_xl_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [188, -187, -187, -187, -187, 188, 188, 188], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 200}, "ef2/qf_njs_jn2_xl_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [188, -187, -187, -187, -187, 188, 188, 188], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 200}, "ef2/qf_njs_jn2_xl_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [188, -187, -187, -187, -187, 188, 188, 188], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 200}, "ef2/qf_njs_jn2_xl_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [188, -187, -187, -187, -187, 188, 188, 188], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 200}, "ef2/qf_njs_jn2_xl_10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [188, -187, -187, -187, -187, 188, 188, 188], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 200}, "ef2/qf_njs_jn2_xl_12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [188, -187, -187, -187, -187, 188, 188, 188], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 200}, "ef2/qf_njs_jn2_xl_16": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [188, -187, -187, -187, -187, 188, 188, 188], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 375, "height": 375}}, "touf1": {"touf1": {"type": "mesh", "uvs": [0.32075, 0.03313, 0.38284, 0.13755, 0.4329, 0.4128, 0.48173, 0.41224, 0.5671, 0.17257, 0.703, 0.17109, 0.88977, 0.51686, 0.92532, 0.5166, 0.968, 0.34132, 0.988, 0.44294, 0.98779, 0.69006, 0.95692, 0.8622, 0.90194, 0.96588, 0.742, 0.9686, 0.64647, 0.76871, 0.56006, 0.58789, 0.4832, 0.5873, 0.43018, 0.58689, 0.3863, 0.50229, 0.29221, 0.52091, 0.21394, 0.53641, 0.13437, 0.55216, 0.01186, 0.55213, 0.01207, 0.48239, 0.05872, 0.39292, 0.14366, 0.14335, 0.22168, 0.0341, 0.53153, 0.45325, 0.64725, 0.42128, 0.73753, 0.67345, 0.83163, 0.72673, 0.91429, 0.69831, 0.95625, 0.58466, 0.42345, 0.47811, 0.3764, 0.3609, 0.29629, 0.25435, 0.18566, 0.30408, 0.11445, 0.42128, 0.05596, 0.49587], "triangles": [22, 38, 21, 38, 37, 21, 20, 21, 36, 22, 23, 38, 21, 37, 36, 35, 20, 36, 38, 24, 37, 38, 23, 24, 36, 37, 25, 37, 24, 25, 36, 25, 26, 18, 33, 17, 19, 20, 35, 19, 34, 18, 19, 35, 34, 18, 34, 33, 33, 34, 2, 2, 34, 1, 34, 35, 1, 36, 26, 35, 35, 0, 1, 35, 26, 0, 13, 30, 12, 12, 31, 11, 12, 30, 31, 11, 31, 10, 30, 6, 31, 30, 29, 6, 31, 32, 10, 31, 7, 32, 31, 6, 7, 10, 32, 9, 9, 32, 8, 32, 7, 8, 14, 29, 13, 13, 29, 30, 14, 28, 29, 6, 29, 5, 29, 28, 5, 14, 15, 28, 16, 27, 15, 15, 27, 28, 3, 16, 33, 33, 2, 3, 16, 3, 27, 16, 17, 33, 28, 27, 4, 27, 3, 4, 28, 4, 5], "vertices": [1, 76, 5.76, -6.98, 1, 1, 76, 0.18, -5.17, 1, 2, 73, -6.36, 1.44, 0.42186, 76, -5.58, 1.68, 0.57814, 2, 73, -2.4, 1.33, 0.90245, 76, -9.43, 0.77, 0.09755, 2, 73, 4.74, 8.05, 0.99968, 74, -11.29, 0.98, 0.00032, 3, 73, 15.74, 7.74, 0.42433, 74, -2.7, 7.86, 0.57266, 75, -7.43, 17.59, 0.00301, 2, 74, 15.39, 9.43, 0.06754, 75, 6.85, 6.39, 0.93246, 2, 74, 17.63, 11.23, 0.00328, 75, 9.72, 6.17, 0.99672, 1, 75, 13.57, 10.96, 1, 1, 75, 14.95, 7.89, 1, 1, 75, 14.36, 0.75, 1, 1, 75, 11.47, -4.03, 1, 1, 75, 6.79, -6.67, 1, 3, 73, 18.14, -15.48, 0.00157, 74, 14.17, -8.27, 0.94042, 75, -6.13, -5.72, 0.05801, 2, 73, 10.6, -9.43, 0.2801, 74, 4.51, -8.55, 0.7199, 2, 73, 3.77, -3.97, 0.97156, 74, -4.24, -8.8, 0.02844, 2, 73, -2.45, -3.75, 0.87924, 76, -10.7, 5.69, 0.12076, 2, 73, -6.74, -3.59, 0.58912, 76, -6.51, 6.65, 0.41088, 3, 73, -10.21, -1.03, 0.21397, 76, -2.49, 5.07, 0.78562, 77, -4.05, 14.99, 0.00042, 3, 73, -17.85, -1.32, 0.00029, 76, 4.81, 7.32, 0.83545, 77, 1.91, 10.21, 0.16426, 2, 76, 10.88, 9.19, 0.25765, 77, 6.87, 6.24, 0.74235, 2, 76, 17.06, 11.1, 0.0021, 77, 11.92, 2.2, 0.9979, 1, 77, 19.21, -4.53, 1, 1, 77, 17.82, -6.01, 1, 1, 77, 13.28, -5.35, 1, 2, 76, 19.01, -0.62, 0.00292, 77, 3.32, -5.99, 0.99708, 2, 76, 13.57, -5.14, 0.68916, 77, -3.47, -4.03, 0.31084, 1, 73, 1.59, 0.01, 1, 1, 73, 10.99, 0.63, 1, 2, 73, 18.06, -6.91, 0.00702, 74, 8.56, -1.8, 0.99298, 1, 75, 1.67, 0.7, 1, 1, 75, 8.41, 0.98, 1, 1, 75, 12.06, 4, 1, 2, 73, -7.18, -0.42, 0.4462, 76, -5.26, 3.7, 0.5538, 2, 73, -10.88, 3.1, 0.04422, 76, -0.78, 1.25, 0.95578, 1, 76, 6.24, -0.29, 1, 1, 77, 3.98, -0.26, 1, 1, 77, 10.53, -1.68, 1, 1, 77, 15.47, -3.31, 1], "hull": 27, "edges": [0, 52, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 34, 36, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 26, 28, 28, 30, 30, 32, 32, 34, 32, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 16, 32, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 40, 42, 36, 38, 38, 40], "width": 81, "height": 29}}, "mv_she_010": {"mv_she_010": {"type": "mesh", "uvs": [0.38716, 0, 0.57649, 0.01841, 0.67116, 0.13459, 0.70534, 0.31855, 0.73953, 0.52671, 0.79738, 0.68404, 0.89731, 0.78085, 1, 0.86557, 1, 0.9043, 0.87364, 0.91156, 0.71849, 0.97207, 0.56072, 1, 0.34772, 1, 0.15575, 1, 0, 1, 0.06635, 0.84379, 0.06897, 0.66709, 0.04005, 0.4783, 0.07423, 0.29192, 0.14523, 0.11039, 0.26357, 0.01841, 0.40297, 0.11708, 0.40529, 0.28798, 0.40761, 0.4418, 0.43082, 0.60844, 0.44707, 0.77935, 0.44242, 0.88617, 0.23353, 0.90539, 0.24746, 0.79217, 0.25442, 0.63194, 0.25674, 0.45248, 0.25442, 0.30294, 0.26371, 0.14912, 0.54919, 0.14058, 0.57936, 0.3008, 0.60257, 0.42898, 0.61882, 0.59776, 0.64435, 0.76653, 0.73023, 0.85412, 0.58865, 0.89044], "triangles": [30, 18, 31, 30, 31, 23, 31, 22, 23, 23, 22, 34, 31, 18, 32, 31, 32, 22, 32, 18, 19, 32, 21, 22, 19, 20, 32, 32, 20, 21, 20, 0, 21, 29, 24, 25, 16, 17, 29, 29, 30, 24, 30, 23, 24, 29, 17, 30, 18, 30, 17, 12, 26, 11, 13, 27, 12, 12, 27, 26, 27, 13, 15, 13, 14, 15, 27, 28, 26, 27, 15, 28, 26, 28, 25, 15, 16, 28, 28, 29, 25, 28, 16, 29, 11, 39, 10, 11, 26, 39, 10, 38, 9, 10, 39, 38, 8, 9, 7, 9, 38, 6, 9, 6, 7, 26, 25, 39, 39, 37, 38, 39, 25, 37, 38, 5, 6, 38, 37, 5, 37, 36, 5, 25, 36, 37, 25, 24, 36, 36, 4, 5, 24, 35, 36, 24, 23, 35, 36, 35, 4, 35, 3, 4, 23, 34, 35, 35, 34, 3, 34, 2, 3, 22, 33, 34, 34, 33, 2, 22, 21, 33, 33, 1, 2, 33, 21, 1, 21, 0, 1], "vertices": [2, 100, -12.96, -10.52, 0.36576, 97, -14.98, 1.51, 0.63424, 2, 100, -5.84, 3.16, 0.96708, 97, -19.31, 16.31, 0.03292, 1, 100, 6.48, 6.57, 1, 1, 100, 22.56, 3.23, 1, 1, 101, 14.32, 1, 1, 1, 102, 2.97, 0.46, 1, 1, 102, 14.72, 0.46, 1, 1, 102, 25.85, 1.37, 1, 1, 102, 28.32, -0.98, 1, 3, 102, 21.74, -8.84, 0.98135, 99, 13.21, 65.67, 0.01851, 98, 26.07, 66.97, 0.00015, 4, 101, 51.61, -11.18, 0.0274, 102, 16.96, -21.63, 0.82664, 99, 20.42, 54.09, 0.13731, 98, 35.16, 56.78, 0.00865, 4, 101, 50.54, -24.15, 0.08763, 102, 9.94, -32.59, 0.56849, 99, 24.83, 41.84, 0.31969, 98, 41.6, 45.47, 0.02419, 4, 101, 45.9, -40.77, 0.08079, 102, -1.93, -45.11, 0.23426, 99, 27.51, 24.8, 0.66191, 98, 47.15, 29.14, 0.02304, 4, 101, 41.72, -55.75, 0.01458, 102, -12.63, -56.39, 0.0395, 99, 29.92, 9.44, 0.94563, 98, 52.16, 14.42, 0.00029, 1, 99, 31.88, -3.03, 1, 1, 99, 17.47, 0.15, 1, 2, 99, 2.07, -2.05, 0.97033, 98, 26.69, -1.67, 0.02967, 2, 98, 11.71, -9.23, 0.99052, 97, 34.64, -8.49, 0.00948, 2, 98, -4.71, -11.89, 0.13898, 97, 18.42, -12.16, 0.86102, 1, 97, 1.46, -12.92, 1, 2, 100, -15.1, -20.43, 0.06357, 97, -9.67, -7.13, 0.93643, 3, 100, -2.9, -13.09, 0.36581, 101, -27.73, -15.56, 0.00686, 97, -5.93, 6.62, 0.62733, 4, 100, 11.17, -18.41, 0.19955, 101, -13.19, -19.42, 0.17712, 98, -13.67, 13.39, 0.02655, 97, 7.9, 12.51, 0.59678, 6, 100, 23.84, -23.18, 0.02191, 101, -0.11, -22.88, 0.42207, 102, -34.23, -7.78, 0.00121, 99, -21.77, 21.97, 0.01545, 98, -0.91, 17.92, 0.26097, 97, 20.35, 17.83, 0.27838, 5, 101, 14.52, -25.02, 0.46186, 102, -22.29, -16.51, 0.05429, 99, -7.57, 26.1, 0.13914, 98, 12.36, 24.42, 0.30521, 97, 33.2, 25.14, 0.03949, 5, 101, 29.36, -27.8, 0.26379, 102, -10.48, -25.9, 0.24916, 99, 7.08, 29.74, 0.35521, 98, 26.18, 30.51, 0.13083, 97, 46.61, 32.08, 0.00101, 4, 101, 38.31, -30.69, 0.14338, 102, -3.92, -32.65, 0.34008, 99, 16.42, 30.82, 0.46012, 98, 35.2, 33.18, 0.05642, 4, 101, 35.39, -47.44, 0.04711, 102, -14.33, -46.08, 0.0924, 99, 20.72, 14.37, 0.84252, 98, 42.25, 17.7, 0.01797, 4, 101, 26.1, -43.67, 0.08346, 102, -20.79, -38.41, 0.07278, 99, 10.7, 13.94, 0.74128, 98, 32.45, 15.56, 0.10248, 5, 101, 12.67, -39.33, 0.13043, 102, -30.63, -28.29, 0.0239, 99, -3.31, 12.31, 0.26306, 98, 18.92, 11.56, 0.57818, 97, 40.55, 12.71, 0.00443, 6, 100, 20.25, -34.9, 0.00075, 101, -2.49, -34.91, 0.09232, 102, -41.96, -17.29, 0.00063, 99, -18.94, 10.04, 0.0062, 98, 3.91, 6.65, 0.73277, 97, 25.87, 6.88, 0.16733, 3, 100, 7.93, -30.27, 0.00432, 101, -15.21, -31.55, 0.01218, 97, 13.77, 1.71, 0.9835, 1, 97, 0.96, -2.75, 1, 3, 100, 3.36, -2.82, 0.93501, 101, -22.55, -4.71, 0.00548, 97, -8.53, 18.36, 0.05951, 4, 100, 17.37, -5.7, 0.63069, 101, -8.32, -6.15, 0.27861, 98, -17.14, 27.1, 0.0106, 97, 3.59, 25.98, 0.08011, 4, 101, 3.05, -7.37, 0.90676, 99, -25.33, 37.4, 0.0037, 98, -7.07, 32.51, 0.04005, 97, 13.3, 32.01, 0.04949, 5, 101, 17.71, -10.1, 0.83865, 102, -12.5, -4.81, 0.04183, 99, -10.86, 41, 0.03962, 98, 6.57, 38.54, 0.06779, 97, 26.54, 38.87, 0.01211, 5, 101, 32.57, -12.1, 0.27456, 102, -0.3, -13.53, 0.57289, 99, 3.49, 45.35, 0.11387, 98, 19.97, 45.27, 0.03854, 97, 39.49, 46.43, 0.00014, 4, 101, 41.87, -7.47, 0.03458, 102, 10.08, -13.79, 0.87222, 99, 10.02, 53.41, 0.08454, 98, 25.03, 54.34, 0.00865, 4, 101, 41.86, -19.38, 0.11834, 102, 4.51, -24.31, 0.60232, 99, 14.96, 42.58, 0.24781, 98, 31.74, 44.51, 0.03152], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 0, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76], "width": 81, "height": 88}}, "mv_she_011": {"mv_she_011": {"type": "mesh", "uvs": [0.18081, 0, 0.26325, 0.01754, 0.34156, 0.08074, 0.36079, 0.17388, 0.3498, 0.30194, 0.34018, 0.41005, 0.34705, 0.50817, 0.40064, 0.60131, 0.47895, 0.6695, 0.56697, 0.70555, 0.6549, 0.70056, 0.74008, 0.67395, 0.7813, 0.59745, 0.77168, 0.52926, 0.71123, 0.49267, 0.65627, 0.44278, 0.73047, 0.43113, 0.82389, 0.4644, 0.91182, 0.50764, 0.98052, 0.5725, 1, 0.67395, 0.99013, 0.78871, 0.93655, 0.87686, 0.85412, 0.93839, 0.77718, 0.97332, 0.67139, 0.98496, 0.58483, 0.99993, 0.46805, 0.99993, 0.36638, 0.96999, 0.26883, 0.90014, 0.18263, 0.82259, 0.1208, 0.73943, 0.06859, 0.63465, 0.03425, 0.50992, 0.01364, 0.38684, 0, 0.27208, 0.00952, 0.15733, 0.04112, 0.0592, 0.09882, 0.02095, 0.17988, 0.10411, 0.29117, 0.13737, 0.09882, 0.13238, 0.17851, 0.22718, 0.17851, 0.35524, 0.17851, 0.46834, 0.20873, 0.58309, 0.26369, 0.68787, 0.35162, 0.77768, 0.45604, 0.82924, 0.57007, 0.85086, 0.658, 0.8492, 0.7899, 0.82259, 0.86134, 0.75107, 0.88744, 0.64297, 0.85584, 0.54484, 0.11256, 0.24048, 0.25682, 0.26044], "triangles": [14, 15, 16, 13, 16, 17, 14, 16, 13, 13, 17, 54, 12, 13, 54, 12, 54, 53, 53, 54, 18, 53, 18, 19, 53, 19, 20, 54, 17, 18, 20, 52, 53, 21, 52, 20, 22, 52, 21, 11, 12, 52, 51, 11, 52, 51, 10, 11, 23, 51, 22, 24, 50, 51, 24, 51, 23, 52, 12, 53, 51, 52, 22, 9, 10, 50, 49, 48, 9, 50, 49, 9, 25, 50, 24, 26, 49, 50, 26, 50, 25, 27, 49, 26, 51, 50, 10, 48, 47, 8, 48, 8, 9, 28, 47, 48, 29, 47, 28, 27, 48, 49, 28, 48, 27, 46, 6, 7, 31, 45, 46, 47, 46, 7, 47, 7, 8, 30, 31, 46, 30, 46, 47, 29, 30, 47, 44, 5, 6, 33, 34, 44, 45, 44, 6, 32, 33, 44, 32, 44, 45, 46, 45, 6, 31, 32, 45, 4, 56, 3, 43, 55, 42, 43, 42, 56, 34, 35, 55, 34, 55, 43, 5, 56, 4, 43, 56, 5, 44, 34, 43, 44, 43, 5, 39, 38, 0, 39, 0, 1, 41, 37, 38, 41, 38, 39, 40, 1, 2, 39, 1, 40, 36, 37, 41, 40, 2, 3, 42, 41, 39, 42, 39, 40, 55, 41, 42, 36, 41, 55, 56, 42, 40, 56, 40, 3, 35, 36, 55], "vertices": [2, 2, -3.74, -4.34, 0.99883, 3, -19.94, -3.64, 0.00117, 1, 2, -2.03, 5.1, 1, 2, 2, 3.6, 13.96, 0.96699, 3, -12.24, 14.39, 0.03301, 2, 2, 11.64, 15.96, 0.7588, 3, -4.29, 16.12, 0.2412, 3, 2, 22.62, 14.4, 0.16192, 3, 6.43, 14.19, 0.80164, 4, -7.53, 16.05, 0.03644, 4, 2, 31.88, 13.05, 0.00163, 3, 15.48, 12.52, 0.54244, 4, 1.12, 12.5, 0.44947, 5, -8.96, 18.79, 0.00646, 4, 3, 23.78, 12.79, 0.05191, 4, 9.35, 11.02, 0.74481, 5, -2.09, 13.41, 0.20232, 6, -12.06, 20.64, 0.00095, 4, 4, 18.33, 14.87, 0.14665, 5, 7.53, 12.45, 0.72233, 6, -3.64, 15.42, 0.12937, 7, -12.73, 23.12, 0.00165, 4, 4, 25.87, 22.05, 0.00145, 5, 17.17, 15.2, 0.26291, 6, 6.12, 13.58, 0.62359, 7, -4.57, 17.08, 0.11205, 5, 5, 25.35, 20.76, 0.01449, 6, 15.58, 14.98, 0.33304, 7, 4.44, 14.13, 0.60464, 8, -7, 17.97, 0.04512, 10, 29.29, 28.06, 0.00271, 4, 6, 23.49, 19.87, 0.03717, 7, 13.28, 15.08, 0.5957, 8, 1.5, 15.42, 0.35775, 9, -5.83, 22.26, 0.00937, 5, 7, 21.75, 18.07, 0.13049, 8, 10.28, 14.92, 0.66752, 9, 1.88, 16.41, 0.1822, 10, 16.09, 14.12, 0.008, 11, 3.94, 13.41, 0.01179, 3, 7, 25.61, 25.55, 0.01202, 8, 16.08, 20.45, 0.34811, 11, 1.58, 5.16, 0.63987, 2, 2, 43.19, 62.39, 0, 11, 4.03, -0.8, 1, 5, 2, 39.9, 55.53, 0, 7, 18.14, 35.11, 0.0001, 8, 11.97, 32.35, 0.00219, 9, 12.18, 30.06, 0.0017, 11, 10.79, -2.33, 0.99601, 5, 2, 35.47, 49.32, 0, 7, 12.4, 39.55, 0.00013, 8, 7.95, 38.75, 0.00267, 9, 11.75, 37.95, 0.00207, 11, 17.3, -5.25, 0.99514, 5, 2, 34.66, 57.88, 0, 7, 19.83, 41.06, 0.0001, 8, 15.31, 37.28, 0.00214, 9, 17.69, 32.15, 0.00166, 11, 10.26, -8.55, 0.9961, 6, 2, 37.75, 68.54, 0, 7, 29.38, 38.41, 1e-05, 8, 23.41, 31.07, 0.0002, 9, 21.91, 21.9, 0.00015, 10, 19.38, -7.7, 0.3427, 11, 0.33, -8.33, 0.65693, 3, 2, 41.68, 78.55, 0, 10, 9.71, -10.18, 0.98615, 11, -9.28, -7.02, 0.01385, 3, 2, 47.42, 86.3, 0, 9, 25.45, 1.51, 0.2134, 10, 0.62, -9.7, 0.7866, 3, 2, 56.18, 88.31, 0, 9, 19.92, -6.42, 0.73871, 10, -6.1, -3.12, 0.26129, 2, 9, 11.45, -12.59, 0.84, 10, -10.98, 6.43, 0.16, 4, 2, 73.46, 80.56, 0, 8, 24.04, -10.2, 0.16378, 9, 1.75, -13.29, 0.69222, 10, -10.8, 16.83, 0.144, 3, 2, 78.54, 70.94, 0, 8, 14.67, -12.96, 0.82761, 9, -8.14, -9.82, 0.17239, 4, 2, 81.35, 62.02, 0, 7, 26.67, -10.14, 0.0317, 8, 6.48, -13.5, 0.96652, 9, -15.85, -5.2, 0.00178, 3, 2, 82.09, 49.83, 0, 7, 16.05, -11.82, 0.66729, 8, -3.89, -10.95, 0.33271, 4, 2, 83.16, 39.85, 0, 6, 28.24, -9.21, 0.05508, 7, 7.39, -13.71, 0.93685, 8, -12.51, -9.35, 0.00808, 3, 2, 82.87, 26.42, 0, 6, 17.48, -15.14, 0.66185, 7, -4.38, -14.34, 0.33815, 4, 2, 80.04, 14.8, 0, 5, 29.34, -13.06, 0.0746, 6, 7, -17.75, 0.90559, 7, -14.74, -12.05, 0.01981, 4, 2, 73.8, 3.74, 0, 4, 39.65, -6.67, 0.00164, 5, 18.32, -17.4, 0.55742, 6, -4.59, -16.75, 0.44094, 4, 2, 66.93, -5.99, 0, 4, 31.14, -14.52, 0.11135, 5, 7.55, -20.27, 0.82781, 6, -15.43, -14.51, 0.06084, 5, 2, 59.63, -12.91, 0, 3, 41.99, -14.39, 0.00586, 4, 22.78, -19.52, 0.41948, 5, -1.97, -20.65, 0.57376, 6, -24.24, -10.56, 0.0009, 4, 2, 50.5, -18.68, 0, 3, 32.87, -19.84, 0.0872, 4, 12.87, -22.95, 0.72116, 5, -12.27, -18.86, 0.19164, 4, 2, 39.7, -22.34, 0.008, 3, 22.18, -23.13, 0.36552, 4, 1.77, -23.93, 0.60833, 5, -22.68, -14.28, 0.01815, 3, 2, 29.08, -24.44, 0.10771, 3, 11.7, -24.86, 0.63526, 4, -8.86, -23.41, 0.25703, 3, 2, 19.19, -25.74, 0.35984, 3, 1.96, -25.83, 0.57676, 4, -18.63, -22.31, 0.06341, 3, 2, 9.35, -24.39, 0.67613, 3, -7.65, -24.13, 0.31829, 4, -27.83, -18.62, 0.00558, 2, 2, 1, -20.54, 0.87123, 3, -15.74, -19.99, 0.12877, 2, 2, -2.14, -13.81, 0.94999, 3, -18.64, -13.17, 0.05001, 2, 2, 5.2, -4.68, 0.98669, 3, -11.18, -4.29, 0.01331, 2, 2, 8.34, 8.04, 0.94385, 3, -7.75, 8.31, 0.05615, 3, 2, 7.43, -14.07, 0.83144, 3, -9.25, -13.75, 0.16846, 4, -27.67, -8.08, 0.0001, 3, 2, 15.77, -5.12, 0.57654, 3, -0.82, -5.09, 0.42337, 4, -17.91, -1.36, 9e-05, 3, 2, 26.77, -5.41, 0.01488, 3, 9.96, -5.76, 0.93575, 4, -7.38, -4.29, 0.04937, 2, 3, 19.48, -6.35, 0.2779, 4, 1.92, -6.88, 0.7221, 4, 2, 46.42, -2.45, 0, 3, 29.32, -3.48, 0.00893, 4, 12.1, -6.13, 0.91871, 5, -6.36, -3.36, 0.07235, 3, 2, 55.56, 3.63, 0, 4, 22.07, -2.39, 0.04606, 5, 4.12, -4.9, 0.95394, 3, 2, 63.49, 13.54, 0, 5, 15.8, -2.63, 0.68182, 6, -1.55, -2.1, 0.31818, 2, 2, 68.18, 25.43, 0, 6, 9.99, -1.2, 1, 4, 6, 21.3, 2.75, 0.00208, 7, 5.32, 0.36, 0.99779, 8, -10.27, 4.68, 0.00012, 10, 21.79, 39.13, 1e-05, 4, 6, 29.33, 7.35, 0.0002, 7, 14.17, 0.99, 0.97972, 8, -1.84, 1.83, 0.01934, 10, 14.36, 33.17, 0.00074, 2, 2, 68.43, 63.82, 0, 8, 11.39, -0.26, 1, 4, 7, 34.27, 11.4, 2e-05, 8, 19.95, 3.78, 0.63997, 9, 5.07, 1.03, 0.35363, 10, 1.89, 12.06, 0.00638, 2, 8, 25.09, 12.7, 0.2, 10, 5.05, 1.94, 0.8, 4, 2, 44.74, 72.03, 0, 8, 24.48, 22.68, 0.064, 10, 12.64, -3.58, 0.89212, 11, -4.59, -1.92, 0.04388, 3, 2, 16.75, -12.73, 0.45498, 3, -0.07, -12.74, 0.52919, 4, -18.44, -9.02, 0.01583, 2, 2, 18.82, 3.81, 0.15173, 3, 2.42, 3.73, 0.84827], "hull": 39, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 0, 76, 0, 78, 78, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108], "width": 115, "height": 95}}, "mv_she_012": {"mv_she_012": {"type": "mesh", "uvs": [0.71159, 0.15011, 0.75266, 0.06543, 0.85623, 0.01167, 1, 0, 1, 0.09366, 0.9598, 0.20253, 0.8723, 0.35038, 0.80623, 0.49554, 0.77051, 0.65952, 0.70801, 0.81274, 0.68659, 1, 0.53123, 0.9364, 0.3848, 0.85575, 0.19373, 0.9243, 0.11695, 0.83021, 0, 0.73613, 0.21694, 0.75091, 0.36159, 0.66489, 0.45623, 0.53317, 0.53123, 0.40145, 0.62587, 0.27645, 0.88123, 0.08425, 0.81873, 0.17699, 0.73123, 0.31812, 0.66873, 0.45522, 0.6098, 0.60575, 0.54909, 0.77242, 0.20253, 0.80747, 0.38733, 0.75147, 0.53373, 0.56541, 0.61773, 0.4227, 0.67773, 0.29444, 0.76413, 0.15534, 0.82652, 0.07405], "triangles": [27, 15, 16, 27, 16, 28, 14, 15, 27, 13, 14, 27, 13, 27, 12, 28, 29, 26, 29, 28, 17, 16, 17, 28, 12, 27, 28, 12, 28, 26, 30, 19, 20, 24, 30, 23, 29, 19, 30, 29, 30, 24, 18, 19, 29, 25, 29, 24, 17, 18, 29, 25, 26, 29, 33, 1, 2, 33, 2, 21, 32, 1, 33, 0, 1, 32, 22, 32, 33, 22, 33, 21, 31, 20, 0, 31, 0, 32, 22, 23, 31, 22, 31, 32, 30, 20, 31, 30, 31, 23, 26, 25, 8, 9, 26, 8, 11, 12, 26, 11, 26, 9, 10, 11, 9, 7, 24, 6, 25, 24, 7, 8, 25, 7, 21, 2, 3, 21, 3, 4, 5, 21, 4, 22, 21, 5, 6, 22, 5, 23, 22, 6, 6, 24, 23], "vertices": [1, 93, 5.54, -4.44, 1, 2, 97, -2.63, -12.52, 0.02197, 93, -2.82, -5.06, 0.97803, 2, 97, -10.01, -7.71, 0.45285, 93, -10.37, -0.52, 0.54715, 2, 97, -14.84, 1.18, 0.7978, 93, -15.51, 8.2, 0.2022, 2, 97, -6.78, 4.49, 0.93053, 93, -7.57, 11.79, 0.06947, 1, 97, 3.65, 5.74, 1, 2, 97, 18.7, 5.3, 0.91147, 98, -3.35, 5.52, 0.08853, 1, 98, 10.92, 5.49, 1, 2, 98, 26.17, 8.03, 0.26747, 99, 3.22, 7.59, 0.73253, 1, 99, 17.98, 5.48, 1, 1, 99, 35.41, 6.7, 1, 2, 99, 31.26, -4.96, 0.93307, 95, 21.68, 23.24, 0.06693, 3, 99, 25.44, -16.25, 0.27473, 95, 23.28, 10.64, 0.57341, 96, -7.72, 7.32, 0.15186, 3, 99, 33.81, -28.47, 3e-05, 95, 37.1, 5.3, 0.00348, 96, 5.75, 13.49, 0.9965, 1, 96, 10.99, 4.66, 1, 1, 96, 19.04, -4.21, 1, 2, 95, 24.27, -4.6, 0.10192, 96, 3.88, -2.61, 0.89808, 1, 95, 11.5, -2.74, 1, 2, 94, 21.53, -5.46, 0.86855, 95, -1.95, -6.35, 0.13145, 2, 93, 32.04, -6.3, 0.00048, 94, 8.22, -6.08, 0.99952, 2, 93, 18.71, -5.06, 0.97352, 94, -5.13, -5.19, 0.02648, 2, 97, -4.43, -3.53, 0.58264, 93, -4.94, 3.86, 0.41736, 2, 97, 5.21, -4.3, 0.43239, 93, 4.72, 3.43, 0.56761, 4, 97, 19.68, -4.97, 0.32112, 98, -3.01, -4.79, 0.04192, 93, 19.2, 3.26, 0.63461, 94, -4.86, 3.13, 0.00234, 2, 98, 10.47, -4.83, 0.49289, 94, 8.52, 4.76, 0.50711, 4, 98, 25.05, -4.23, 0.28639, 99, 0.03, -4.3, 0.36346, 94, 22.91, 7.15, 0.28008, 95, -4.44, 6.1, 0.07007, 2, 99, 16, -6.09, 0.73592, 95, 9.74, 13.66, 0.26408, 2, 99, 22.98, -29.55, 0.00015, 96, 4.97, 2.63, 0.99985, 2, 99, 15.83, -17.58, 0.10197, 95, 16.11, 4.1, 0.89803, 3, 98, 23.22, -10.48, 0.01895, 99, -2.85, -10.14, 0.00899, 94, 21.86, 0.73, 0.97206, 2, 98, 8.76, -9.18, 0.01075, 94, 7.35, 0.23, 0.98925, 2, 93, 18.74, -1.06, 0.99832, 94, -5.21, -1.2, 0.00168, 1, 93, 4.46, -0.88, 1, 2, 97, -3.85, -7.43, 0.22046, 93, -4.23, -0.02, 0.77954], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 6, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 30, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66], "width": 70, "height": 93}}, "jiao": {"jiao": {"x": 31.81, "y": 11.86, "rotation": -82.45, "width": 101, "height": 64}}, "mv_she_014": {"mv_she_014": {"type": "mesh", "uvs": [0.09163, 0.03723, 0.09822, 0.05833, 0.09826, 0.12537, 0.10524, 0.26966, 0.11307, 0.34927, 0.12444, 0.46484, 0.16313, 0.55385, 0.20959, 0.62045, 0.25997, 0.66031, 0.32982, 0.67626, 0.38853, 0.65143, 0.44303, 0.62838, 0.4768, 0.61005, 0.51875, 0.58729, 0.57, 0.5643, 0.61563, 0.54384, 0.67442, 0.5439, 0.74122, 0.54397, 0.80526, 0.55877, 0.8496, 0.5941, 0.89926, 0.63367, 0.95023, 0.72317, 0.97398, 0.78068, 0.98926, 0.83883, 0.9954, 0.89595, 0.9954, 0.98548, 0.98644, 0.98711, 0.96835, 0.93066, 0.92679, 0.86032, 0.86033, 0.79486, 0.788, 0.75313, 0.70918, 0.75288, 0.67747, 0.76709, 0.62647, 0.78994, 0.57822, 0.81826, 0.53937, 0.84107, 0.50075, 0.86373, 0.45997, 0.88767, 0.37662, 0.92657, 0.28627, 0.92699, 0.21024, 0.92735, 0.1379, 0.88076, 0.09693, 0.82711, 0.065, 0.7644, 0.02313, 0.63185, 0.00989, 0.55383, 0.00419, 0.46407, 0.01107, 0.31245, 0.03072, 0.21195, 0.07469, 0.07341, 0.97904, 0.88197, 0.93162, 0.77321, 0.87455, 0.69733, 0.82552, 0.66444, 0.77891, 0.63915, 0.72586, 0.63915, 0.67362, 0.65433, 0.61575, 0.66444, 0.5418, 0.69227, 0.44984, 0.7378, 0.35581, 0.7985, 0.4043, 0.7672, 0.57685, 0.67908, 0.49426, 0.71581, 0.30597, 0.7808, 0.23364, 0.78586, 0.17738, 0.74033, 0.13317, 0.67456, 0.08012, 0.5835, 0.05923, 0.42162, 0.06405, 0.25974, 0.08093, 0.14592], "triangles": [27, 50, 26, 26, 50, 25, 50, 24, 25, 27, 28, 50, 50, 28, 51, 50, 23, 24, 50, 51, 22, 51, 21, 22, 50, 22, 23, 28, 29, 51, 51, 20, 21, 51, 29, 52, 30, 53, 29, 29, 53, 52, 52, 20, 51, 53, 19, 52, 52, 19, 20, 53, 18, 19, 55, 54, 30, 30, 54, 53, 54, 18, 53, 55, 17, 54, 54, 17, 18, 33, 56, 32, 32, 56, 31, 31, 55, 30, 31, 56, 55, 56, 16, 55, 56, 15, 16, 55, 16, 17, 58, 62, 34, 33, 62, 57, 33, 34, 62, 33, 57, 56, 58, 14, 62, 62, 14, 57, 14, 15, 57, 57, 15, 56, 36, 37, 63, 36, 63, 35, 35, 58, 34, 35, 63, 58, 59, 12, 63, 63, 13, 58, 63, 12, 13, 58, 13, 14, 37, 61, 59, 37, 38, 61, 37, 59, 63, 60, 10, 61, 61, 11, 59, 61, 10, 11, 59, 11, 12, 39, 60, 38, 39, 64, 60, 38, 60, 61, 64, 9, 60, 60, 9, 10, 40, 65, 39, 40, 66, 65, 39, 65, 64, 65, 8, 64, 65, 7, 8, 64, 8, 9, 41, 66, 40, 41, 67, 66, 66, 7, 65, 67, 6, 66, 66, 6, 7, 42, 67, 41, 42, 43, 67, 43, 68, 67, 43, 44, 68, 68, 5, 67, 67, 5, 6, 44, 45, 68, 5, 68, 69, 45, 69, 68, 5, 69, 4, 4, 70, 3, 70, 4, 69, 45, 46, 69, 46, 47, 69, 69, 47, 70, 70, 47, 48, 70, 71, 3, 71, 2, 3, 70, 48, 71, 48, 49, 71, 1, 71, 49, 1, 49, 0, 71, 1, 2], "vertices": [2, 19, 5.84, -9.94, 0.9996, 21, -16.32, 28.2, 0.0004, 2, 19, 5.58, -7.95, 0.99961, 21, -14.49, 28.03, 0.00039, 2, 19, 8, -4.26, 0.99972, 21, -11.79, 24.57, 0.00028, 3, 19, 12.13, 4.55, 0.63103, 20, 0.42, 6.59, 0.35877, 21, -4.95, 18.08, 0.0102, 3, 19, 13.79, 9.91, 0.14828, 20, 5.23, 8.59, 0.73469, 21, -0.58, 15.06, 0.11703, 4, 19, 16.2, 17.7, 0.00411, 20, 12.21, 11.48, 0.33519, 21, 5.76, 10.67, 0.62292, 22, -5.18, 14.77, 0.03777, 3, 20, 17.25, 20.11, 0.01144, 21, 15.07, 11.46, 0.418, 22, 3.27, 10.96, 0.57056, 3, 21, 24.63, 14.48, 0.01416, 22, 12.8, 9.03, 0.78666, 23, -2.27, 9.59, 0.19918, 3, 22, 22.66, 9.08, 0.02404, 23, 7.35, 7.81, 0.97083, 24, -8.51, 8.83, 0.00513, 2, 23, 20.48, 8, 0.0942, 24, 4.78, 7.49, 0.9058, 2, 24, 16, 8.96, 0.48175, 25, 0.82, 8.92, 0.51825, 3, 24, 26.4, 10.32, 0.00077, 25, 11.22, 8.38, 0.93169, 26, -5.38, 8.3, 0.06754, 2, 25, 17.71, 8.32, 0.35306, 26, 1.15, 8.34, 0.64694, 2, 26, 9.26, 8.38, 0.95657, 27, -7.59, 7.89, 0.04343, 2, 26, 19.12, 8.11, 0.22384, 27, 2.21, 8.27, 0.77616, 2, 27, 10.93, 8.6, 0.95448, 28, -6.17, 8.03, 0.04552, 2, 27, 22.01, 7.24, 0.05147, 28, 4.97, 7.75, 0.94853, 2, 28, 17.63, 7.44, 0.41407, 29, 0.4, 7.54, 0.58593, 2, 29, 12.6, 7.82, 0.91961, 30, -4.88, 7.07, 0.08039, 2, 29, 21.19, 6.31, 0.08053, 30, 3.82, 7.05, 0.91947, 2, 30, 13.57, 7.03, 0.9262, 31, -3.98, 6.32, 0.0738, 1, 31, 7.07, 5.32, 1, 1, 31, 12.62, 3.87, 1, 1, 31, 16.72, 1.64, 1, 1, 31, 19.21, -1.35, 1, 1, 31, 21.46, -6.89, 1, 1, 31, 19.95, -7.79, 1, 1, 31, 15.41, -5.9, 1, 2, 30, 21.98, -6.49, 0.00336, 31, 6.46, -5.25, 0.99664, 1, 30, 8.76, -5.63, 1, 2, 29, 10.43, -5.69, 0.97431, 30, -5.2, -6.65, 0.02569, 2, 28, 11.28, -6.62, 0.95854, 29, -4.49, -7.25, 0.04146, 2, 27, 21.14, -7.92, 0.06795, 28, 5.26, -7.43, 0.93205, 2, 27, 11.38, -8.29, 0.89058, 28, -4.44, -8.75, 0.10942, 2, 26, 18.12, -9.22, 0.29386, 27, 2.12, -9.09, 0.70614, 3, 25, 26.88, -9.53, 0.00036, 26, 10.59, -9.37, 0.91638, 27, -5.35, -9.74, 0.08326, 2, 25, 19.44, -9.57, 0.22165, 26, 3.11, -9.52, 0.77835, 2, 25, 11.59, -9.61, 0.87336, 26, -4.79, -9.68, 0.12664, 2, 24, 13.43, -9.71, 0.84067, 25, -4.36, -9.03, 0.15933, 2, 23, 13.61, -9.78, 0.828, 24, -3.78, -9.4, 0.172, 2, 22, 17.51, -11.15, 0.49713, 23, -0.59, -11.19, 0.50287, 2, 21, 24.5, -8.94, 0.12229, 22, 3.51, -11.92, 0.87771, 2, 21, 16.27, -11.87, 0.6945, 22, -4.82, -10.56, 0.3055, 3, 20, 31.34, 0, 0.00417, 21, 9.02, -13.07, 0.9681, 22, -11.63, -8.12, 0.02773, 2, 20, 23.66, -9.48, 0.37253, 21, -2.52, -12.05, 0.62747, 2, 20, 19.01, -12.63, 0.68329, 21, -7.62, -9.86, 0.31671, 2, 20, 13.56, -14.21, 0.91194, 21, -12.08, -6.02, 0.08806, 2, 19, 28.41, -4.97, 0.01683, 20, 4.14, -13.35, 0.98317, 3, 19, 21.7, -8.01, 0.29106, 20, -2.27, -9.56, 0.70893, 21, -18.3, 10.7, 0, 3, 19, 9.8, -10.09, 0.99392, 20, -11.31, -0.73, 0.00576, 21, -17.37, 23.97, 0.00032, 1, 31, 16.03, -1.94, 1, 1, 31, 5.11, 0.57, 1, 1, 30, 9.95, 1.54, 1, 2, 29, 17.03, 1.07, 0.38038, 30, 0.42, 1.15, 0.61962, 2, 28, 24.64, 0.79, 0.0001, 29, 8.07, 1.85, 0.9999, 2, 28, 14.59, 1.04, 0.9424, 29, -1.97, 0.79, 0.0576, 1, 28, 4.67, 0.25, 1, 1, 27, 10.18, 0.44, 1, 1, 26, 12.54, 0.52, 1, 1, 25, 11.31, 0.8, 1, 1, 24, 9.61, -0.92, 1, 2, 24, 18.88, 1.03, 0.0023, 25, 2.51, 0.58, 0.9977, 1, 27, 2.76, 0.35, 1, 1, 26, 3.37, 0.63, 1, 2, 23, 16.55, 0.48, 0.47036, 24, 0.13, 0.47, 0.52964, 1, 23, 3.06, -1.18, 1, 1, 22, 8.67, -0.57, 1, 3, 20, 25.02, 14.2, 0.00013, 21, 15.49, 1.05, 0.73096, 22, -0.44, 1.42, 0.26892, 1, 21, 3.97, -1.62, 1, 1, 20, 10.31, -2.62, 1, 2, 19, 18.22, -1.19, 0.30692, 20, 0.29, -2.25, 0.69308, 3, 19, 11.45, -5.31, 0.98135, 20, -6.92, 0.9, 0.01848, 21, -13.53, 21.09, 0.00017], "hull": 50, "edges": [0, 98, 0, 2, 2, 4, 4, 6, 10, 12, 12, 14, 14, 16, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 74, 76, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 34, 36, 16, 18, 76, 78, 78, 80, 6, 8, 8, 10, 36, 38, 38, 40, 50, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 30, 32, 32, 34, 112, 114, 62, 64, 64, 66, 18, 20, 20, 22, 118, 122, 122, 120, 26, 28, 28, 30, 114, 124, 124, 116, 66, 68, 68, 70, 22, 24, 24, 26, 116, 126, 126, 118, 70, 72, 72, 74, 120, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 2], "width": 214, "height": 68}}, "mv_she_015": {"mv_she_015": {"type": "mesh", "uvs": [0.06199, 0.09798, 0.05541, 0.28298, 0.07843, 0.38164, 0.03568, 0.52964, 0.01595, 0.74548, 0.13106, 0.88114, 0.32839, 0.97364, 0.51586, 0.99214, 0.70332, 0.94897, 0.89408, 0.86264, 1, 0.70231, 1, 0.47414, 0.93025, 0.38781, 0.95985, 0.20898, 0.91052, 0.00548, 0.7395, 0.09798, 0.55532, 0.11648, 0.35799, 0.11648, 0.20012, 0.11031, 0.24288, 0.49264, 0.50928, 0.52348, 0.71648, 0.49264, 0.80857, 0.41248, 0.77897, 0.29531, 0.60794, 0.33848, 0.4139, 0.33848, 0.23959, 0.33231, 0.22314, 0.70231, 0.49941, 0.72697, 0.69674, 0.70231, 0.8579, 0.62831], "triangles": [30, 11, 10, 9, 30, 10, 9, 8, 29, 9, 29, 30, 28, 29, 8, 6, 27, 28, 5, 27, 6, 7, 28, 8, 6, 28, 7, 30, 12, 11, 29, 21, 30, 28, 20, 29, 27, 19, 28, 4, 3, 27, 5, 4, 27, 27, 3, 19, 23, 15, 14, 23, 14, 13, 26, 18, 17, 1, 0, 18, 26, 1, 18, 24, 16, 15, 24, 15, 23, 25, 17, 16, 25, 16, 24, 26, 17, 25, 2, 1, 26, 12, 23, 13, 22, 23, 12, 21, 24, 23, 21, 23, 22, 19, 26, 25, 2, 26, 19, 20, 25, 24, 19, 25, 20, 20, 24, 21, 30, 22, 12, 21, 22, 30, 29, 20, 21, 19, 3, 2, 28, 19, 20], "vertices": [1, 39, 10.8, 18.81, 1, 1, 39, 6.4, 19.5, 1, 2, 39, 3.95, 18.68, 0.75, 32, -19.76, 3.75, 0.25, 2, 39, 0.58, 20.91, 0.5, 32, -21.66, 0.19, 0.5, 2, 39, -4.5, 22.26, 0.33333, 32, -22.52, -5, 0.66667, 2, 39, -8.2, 17.39, 0.33333, 32, -17.31, -8.22, 0.66667, 1, 32, -8.42, -10.38, 1, 1, 32, 0.02, -10.77, 1, 1, 32, 8.45, -9.68, 1, 1, 32, 17.02, -7.55, 1, 1, 32, 21.76, -3.67, 1, 2, 39, -1.96, -22.43, 0.33333, 32, 21.72, 1.8, 0.66667, 2, 39, 0.38, -19.49, 0.66667, 32, 18.57, 3.86, 0.33333, 1, 39, 4.54, -21.2, 1, 1, 39, 9.6, -19.42, 1, 1, 39, 8.08, -11.56, 1, 1, 39, 8.37, -3.27, 1, 1, 39, 9.17, 5.58, 1, 1, 39, 9.95, 12.64, 1, 1, 39, 0.64, 11.54, 1, 1, 39, -1.17, -0.33, 1, 1, 39, -1.27, -9.68, 1, 1, 39, 0.28, -13.98, 1, 1, 39, 3.2, -12.91, 1, 1, 39, 2.86, -5.15, 1, 1, 39, 3.63, 3.55, 1, 1, 39, 4.48, 11.35, 1, 1, 32, -13.2, -3.9, 1, 1, 32, -0.76, -4.41, 1, 1, 32, 8.11, -3.76, 1, 1, 32, 15.35, -1.94, 1], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36, 4, 38, 38, 40, 40, 42, 26, 46, 46, 48, 48, 50, 50, 52, 52, 2, 6, 54, 54, 56, 56, 58, 58, 60], "width": 45, "height": 24}}, "mv_she_017": {"mv_she_017": {"type": "mesh", "uvs": [0.79452, 0.07221, 0.94023, 0.17434, 1, 0.32312, 0.9954, 0.54251, 0.91194, 0.73668, 0.75067, 0.8842, 0.60779, 0.96616, 0.50594, 0.99012, 0.35599, 0.96994, 0.20887, 0.85773, 0.06741, 0.70012, 0, 0.46686, 0.02638, 0.25125, 0.18623, 0.04447, 0.4154, 0, 0.60638, 0.0016, 0.63043, 0.18317, 0.62194, 0.47064, 0.59506, 0.66481, 0.55687, 0.82621, 0.63609, 0.30295, 0.79735, 0.1983, 0.82565, 0.3206, 0.80018, 0.4946, 0.77047, 0.68625, 0.72945, 0.80099, 0.43521, 0.16299, 0.38994, 0.26638, 0.36731, 0.42525, 0.33477, 0.62447, 0.33194, 0.77325, 0.24282, 0.14786, 0.19189, 0.26512, 0.15228, 0.41769, 0.17209, 0.63455], "triangles": [31, 13, 14, 26, 14, 15, 31, 14, 26, 16, 15, 0, 26, 15, 16, 21, 0, 1, 16, 0, 21, 12, 13, 31, 32, 12, 31, 27, 31, 26, 32, 31, 27, 20, 16, 21, 22, 21, 1, 20, 21, 22, 22, 1, 2, 33, 12, 32, 28, 32, 27, 33, 32, 28, 11, 12, 33, 26, 20, 27, 20, 26, 16, 17, 20, 22, 20, 28, 27, 17, 28, 20, 23, 17, 22, 2, 23, 22, 3, 23, 2, 29, 33, 28, 29, 28, 17, 34, 33, 29, 11, 33, 34, 18, 29, 17, 18, 17, 23, 24, 18, 23, 24, 23, 3, 10, 11, 34, 4, 24, 3, 30, 34, 29, 30, 29, 18, 25, 18, 24, 25, 24, 4, 19, 30, 18, 19, 18, 25, 9, 34, 30, 10, 34, 9, 5, 25, 4, 5, 6, 19, 5, 19, 25, 8, 30, 19, 7, 8, 19, 9, 30, 8, 6, 7, 19], "vertices": [3, 41, 50.19, -20, 0.56, 70, 30.96, -10.14, 0.14, 71, 18.86, -8.12, 0.3, 2, 41, 45.43, -25.93, 0.7, 71, 14.1, -14.04, 0.3, 2, 41, 38.56, -28.3, 0.7, 71, 7.23, -16.42, 0.3, 2, 41, 28.47, -28.01, 0.7, 71, -2.86, -16.12, 0.3, 2, 41, 19.58, -24.49, 0.7, 71, -11.76, -12.6, 0.3, 2, 41, 12.86, -17.81, 0.7, 71, -18.47, -5.92, 0.3, 3, 41, 9.15, -11.91, 0.56, 70, -10.08, -2.05, 0.14, 71, -22.18, -0.02, 0.3, 3, 41, 8.09, -7.72, 0.49, 70, -11.13, 2.14, 0.21, 71, -23.24, 4.16, 0.3, 3, 41, 9.09, -1.58, 0.56, 70, -10.14, 8.28, 0.14, 71, -22.24, 10.3, 0.3, 3, 41, 14.31, 4.39, 0.63, 70, -4.91, 14.25, 0.07, 71, -17.02, 16.28, 0.3, 2, 41, 21.63, 10.11, 0.7, 71, -9.71, 22, 0.3, 2, 41, 32.38, 12.76, 0.7, 71, 1.05, 24.65, 0.3, 2, 41, 42.29, 11.58, 0.7, 71, 10.96, 23.46, 0.3, 2, 41, 51.73, 4.92, 0.7, 71, 20.4, 16.81, 0.3, 3, 41, 53.68, -4.49, 0.56, 70, 34.45, 5.37, 0.14, 71, 22.34, 7.39, 0.3, 3, 41, 53.52, -12.32, 0.49, 70, 34.29, -2.46, 0.21, 71, 22.19, -0.44, 0.3, 2, 41, 45.16, -13.22, 0.7, 70, 25.93, -3.36, 0.3, 2, 41, 31.94, -12.73, 0.7, 70, 12.71, -2.87, 0.3, 2, 41, 23.02, -11.54, 0.7, 70, 3.79, -1.67, 0.3, 2, 41, 15.61, -9.89, 0.7, 70, -3.62, -0.03, 0.3, 2, 41, 39.65, -13.39, 0.7, 70, 20.42, -3.53, 0.3, 2, 41, 44.39, -20.06, 0.8, 70, 25.16, -10.2, 0.2, 2, 41, 38.75, -21.16, 0.8, 70, 19.52, -11.3, 0.2, 2, 41, 30.76, -20.03, 0.8, 70, 11.53, -10.17, 0.2, 2, 41, 21.96, -18.72, 0.8, 70, 2.73, -8.86, 0.2, 3, 41, 16.7, -16.98, 0.6656, 70, -2.53, -7.12, 0.1664, 71, -14.64, -5.09, 0.168, 2, 41, 46.17, -5.23, 0.8, 70, 26.94, 4.63, 0.2, 2, 41, 41.44, -3.32, 0.8, 70, 22.21, 6.54, 0.2, 2, 41, 34.14, -2.32, 0.8, 70, 14.91, 7.55, 0.2, 2, 41, 24.99, -0.88, 0.8, 70, 5.76, 8.98, 0.2, 2, 41, 18.15, -0.69, 0.8, 70, -1.08, 9.17, 0.2, 2, 41, 46.95, 2.65, 0.9, 70, 27.72, 12.51, 0.1, 2, 41, 41.58, 4.8, 0.9, 70, 22.35, 14.66, 0.1, 2, 41, 34.58, 6.5, 0.9, 70, 15.35, 16.36, 0.1, 2, 41, 24.6, 5.79, 0.9, 70, 5.37, 15.65, 0.1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 0, 30, 32, 34, 36, 36, 38, 38, 14, 34, 40, 40, 32, 42, 44, 44, 46, 46, 48, 48, 50, 52, 54, 54, 56, 56, 58, 58, 60, 62, 64, 64, 66, 66, 68], "width": 41, "height": 46}}, "ef1/qianghen_add_02": {"ef1/qianghen_add_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [120, -49, -119, -49, -119, 49, 120, 49], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 239, "height": 98}, "ef1/qianghen_add_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [120, -49, -119, -49, -119, 49, 120, 49], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 239, "height": 98}, "ef1/qianghen_add_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [120, -49, -119, -49, -119, 49, 120, 49], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 239, "height": 98}, "ef1/qianghen_add_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [120, -49, -119, -49, -119, 49, 120, 49], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 239, "height": 98}}, "mv_she_022": {"mv_she_022": {"type": "mesh", "uvs": [0.83161, 0.09909, 0.75085, 0.18453, 0.65904, 0.28164, 0.56599, 0.38007, 0.4579, 0.49442, 0.35625, 0.60194, 0.25649, 0.70747, 0.16402, 0.80529, 0.10055, 0.87243, 0.02428, 0.95311, 0.01667, 1, 0.12535, 0.99034, 0.20644, 0.90157, 0.26276, 0.8399, 0.34885, 0.74565, 0.44957, 0.63538, 0.55006, 0.52535, 0.65965, 0.40537, 0.75528, 0.30066, 0.84447, 0.20301, 0.92632, 0.1134, 1, 0.03273, 1, 0, 0.91645, 0.00935, 0.9504, 0.02678, 0.8784, 0.10756, 0.80041, 0.19502, 0.7064, 0.29189, 0.613, 0.39381, 0.50506, 0.50812, 0.40233, 0.61963, 0.29897, 0.7282, 0.21554, 0.82275, 0.15576, 0.88822, 0.07482, 0.96871], "triangles": [10, 34, 11, 10, 9, 34, 11, 34, 12, 34, 33, 12, 34, 9, 33, 9, 8, 33, 12, 33, 13, 33, 32, 13, 33, 8, 32, 8, 7, 32, 13, 32, 14, 14, 32, 31, 32, 7, 31, 7, 6, 31, 14, 31, 15, 15, 31, 30, 31, 6, 30, 6, 5, 30, 15, 30, 16, 30, 5, 29, 16, 30, 29, 5, 4, 29, 16, 29, 17, 17, 29, 28, 29, 4, 28, 4, 3, 28, 17, 28, 18, 28, 27, 18, 28, 3, 27, 3, 2, 27, 18, 27, 19, 27, 26, 19, 27, 2, 26, 2, 1, 26, 20, 19, 25, 19, 26, 25, 26, 1, 25, 1, 0, 25, 21, 20, 24, 20, 25, 24, 25, 0, 24, 0, 23, 24, 24, 22, 21, 24, 23, 22], "vertices": [1, 67, 17.55, -3.51, 1, 1, 67, 28.98, -3.37, 1, 1, 67, 41.97, -3.21, 1, 1, 67, 55.13, -3.05, 1, 2, 67, 70.42, -2.86, 0.95727, 68, 66.86, 2.85, 0.04273, 1, 68, 52.48, 2.76, 1, 1, 68, 38.36, 2.67, 1, 1, 68, 25.28, 2.59, 1, 1, 68, 16.3, 2.53, 1, 1, 68, 5.51, 2.46, 1, 1, 68, -0.02, 0.62, 1, 1, 68, 3.28, -4.25, 1, 1, 68, 15.1, -4.04, 1, 1, 68, 23.3, -3.89, 1, 1, 68, 35.85, -3.66, 1, 1, 68, 50.53, -3.39, 1, 2, 67, 72.07, 3.12, 0.51688, 68, 65.17, -3.12, 0.48312, 1, 67, 56.11, 2.74, 1, 1, 67, 42.17, 2.4, 1, 1, 67, 29.18, 2.09, 1, 1, 67, 17.25, 1.81, 1, 1, 67, 6.52, 1.55, 1, 1, 67, 2.77, -0.02, 1, 1, 67, 5.55, -3.66, 1, 1, 67, 6.85, -1.16, 1, 1, 67, 17.57, -0.82, 1, 1, 67, 29.17, -0.45, 1, 1, 67, 42.17, -0.41, 1, 1, 67, 55.74, -0.1, 1, 1, 67, 71.02, 0.1, 1, 1, 68, 51.38, -0.33, 1, 1, 68, 36.84, -0.39, 1, 1, 68, 24.32, -0.76, 1, 1, 68, 15.61, -0.92, 1, 1, 68, 4.74, -0.76, 1], "hull": 24, "edges": [18, 20, 20, 22, 42, 44, 44, 46, 0, 46, 40, 42, 0, 2, 38, 40, 2, 4, 36, 38, 4, 6, 34, 36, 6, 8, 32, 34, 8, 10, 30, 32, 10, 12, 28, 30, 12, 14, 26, 28, 14, 16, 16, 18, 22, 24, 24, 26, 44, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68], "width": 53, "height": 124}}, "mv_she_013": {"mv_she_013": {"type": "mesh", "uvs": [0.03379, 0, 0.16362, 0, 0.31167, 0.0282, 0.42556, 0.1677, 0.50984, 0.3327, 0.58729, 0.5022, 0.66701, 0.6822, 0.77342, 0.79444, 0.92009, 0.88575, 1, 0.99288, 0.80809, 0.9437, 0.59209, 0.90683, 0.42676, 0.82605, 0.34409, 0.65922, 0.29609, 0.46429, 0.24809, 0.27639, 0.12809, 0.13415, 0.21609, 0.07444, 0.33076, 0.21844, 0.39743, 0.36595, 0.42676, 0.49941, 0.52009, 0.67327, 0.63209, 0.7997, 0.79476, 0.88927, 0.90409, 0.93492], "triangles": [22, 21, 6, 22, 6, 7, 12, 21, 22, 23, 7, 8, 22, 7, 23, 11, 12, 22, 11, 22, 23, 24, 23, 8, 10, 23, 24, 11, 23, 10, 24, 8, 9, 10, 24, 9, 14, 19, 20, 20, 4, 5, 13, 14, 20, 21, 20, 5, 13, 20, 21, 21, 5, 6, 12, 13, 21, 17, 1, 2, 0, 1, 17, 16, 0, 17, 17, 2, 3, 18, 17, 3, 16, 17, 18, 15, 16, 18, 18, 3, 4, 19, 15, 18, 4, 19, 18, 14, 15, 19, 20, 19, 4], "vertices": [1, 100, -9.1, -6.16, 1, 1, 100, -6.54, 0.36, 1, 1, 100, -1.46, 6.96, 1, 1, 100, 11.43, 8.51, 1, 2, 100, 25.69, 7.8, 0.96254, 101, -1.41, 8.13, 0.03746, 2, 100, 40.16, 6.61, 0.04162, 101, 13.1, 8.41, 0.95838, 2, 101, 28.47, 8.59, 0.24184, 102, 5.76, 6.67, 0.75816, 1, 102, 16.39, 4.51, 1, 1, 102, 27.27, 5.1, 1, 1, 102, 36.61, 2.18, 1, 1, 102, 26.56, -2.56, 1, 1, 102, 16.34, -8.94, 1, 2, 101, 36.35, -7.08, 0.03829, 102, 5.39, -10.86, 0.96171, 2, 101, 21.97, -7.7, 0.98109, 102, -7.61, -4.68, 0.01891, 1, 101, 5.88, -5.89, 1, 2, 100, 16.23, -3.67, 0.98688, 101, -9.66, -4.25, 0.01312, 1, 100, 3, -5.44, 1, 1, 100, 0.18, 0.77, 1, 1, 100, 13.44, 2.22, 1, 1, 100, 26.01, 1.15, 1, 2, 100, 36.78, -1.37, 0.00086, 101, 10.55, 0.13, 0.99914, 2, 101, 25.64, 1.15, 0.80525, 102, -0.23, 1.42, 0.19475, 1, 102, 11.45, -1.33, 1, 1, 102, 22.82, -0.01, 1, 1, 102, 29.6, 1.7, 1], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32, 0, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48], "width": 54, "height": 82}}, "toufa2": {"toufa2": {"type": "mesh", "uvs": [0.28137, 0, 0.41905, 0.03226, 0.48419, 0.01282, 0.60558, 0.06216, 0.72993, 0.10402, 0.80987, 0.15485, 0.88537, 0.22362, 0.92682, 0.31632, 0.94163, 0.39555, 1, 0.42396, 1, 0.48974, 0.98604, 0.563, 0.93719, 0.60337, 0.95051, 0.64523, 0.90462, 0.69008, 0.94755, 0.7125, 0.91942, 0.76483, 0.88241, 0.79473, 0.83504, 0.8052, 0.87797, 0.82762, 0.84096, 0.89639, 0.80247, 0.93228, 0.71513, 0.94424, 0.63519, 0.91434, 0.58929, 0.86948, 0.55377, 0.93228, 0.48567, 1, 0.41017, 0.93377, 0.38204, 0.86201, 0.32727, 0.89191, 0.26065, 0.91134, 0.19847, 0.91583, 0.22512, 0.86948, 0.22808, 0.79623, 0.16886, 0.79772, 0.12297, 0.78277, 0.15702, 0.75138, 0.09929, 0.72895, 0.04303, 0.67513, 0.01342, 0.59141, 0.00306, 0.54057, 0.05191, 0.53908, 0.01342, 0.48227, 0, 0.40004, 0.02231, 0.31781, 0.083, 0.26698, 0.0756, 0.33874, 0.12445, 0.36416, 0.16738, 0.31333, 0.22512, 0.25353, 0.27841, 0.21017, 0.26509, 0.15336, 0.26509, 0.06216, 0.59077, 0.40452, 0.59522, 0.31333, 0.55228, 0.19223, 0.46198, 0.12196, 0.35687, 0.10552, 0.58633, 0.10851, 0.68108, 0.16532, 0.71069, 0.23409, 0.70032, 0.30735, 0.66924, 0.38808, 0.71365, 0.42545, 0.79359, 0.39107, 0.82024, 0.28791, 0.79951, 0.21316, 0.73141, 0.48376, 0.81431, 0.51665, 0.90314, 0.49273, 0.95643, 0.45386, 0.71217, 0.55253, 0.78915, 0.60785, 0.86465, 0.63177, 0.687, 0.6243, 0.73881, 0.69008, 0.81431, 0.74091, 0.88093, 0.74689, 0.62927, 0.67662, 0.65147, 0.75138, 0.6944, 0.81865, 0.75214, 0.85005, 0.80691, 0.85453, 0.77138, 0.78726, 0.71069, 0.73194, 0.66035, 0.66466, 0.57153, 0.69606, 0.56857, 0.77679, 0.50343, 0.66616, 0.47382, 0.72147, 0.47234, 0.78875, 0.48123, 0.86649, 0.47975, 0.93676, 0.4531, 0.63925, 0.38352, 0.68709, 0.33763, 0.77081, 0.2947, 0.8336, 0.24733, 0.88443, 0.4383, 0.55552, 0.35687, 0.58991, 0.29618, 0.66765, 0.23548, 0.73642, 0.17923, 0.77231, 0.08152, 0.6243, 0.17331, 0.6243, 0.2414, 0.57347, 0.31394, 0.5316, 0.42793, 0.48675, 0.04895, 0.34173, 0.06968, 0.40751, 0.14962, 0.46433, 0.24288, 0.4419, 0.28878, 0.37462, 0.38056, 0.31333, 0.49011, 0.31333, 0.52564, 0.38808, 0.58162, 0.54233, 0.50962, 0.45572, 0.64938, 0.48994, 0.64726, 0.59901, 0.5202, 0.60007, 0.58267, 0.61932, 0.3635, 0.20764, 0.45032, 0.23758], "triangles": [80, 79, 84, 80, 84, 83, 81, 80, 83, 81, 83, 82, 24, 79, 80, 20, 82, 19, 23, 24, 80, 21, 81, 82, 21, 82, 20, 22, 80, 81, 22, 81, 21, 23, 80, 22, 86, 121, 78, 79, 78, 85, 79, 85, 84, 86, 78, 79, 87, 86, 79, 24, 87, 79, 28, 90, 91, 91, 87, 24, 25, 91, 24, 27, 28, 91, 92, 27, 91, 92, 91, 25, 26, 92, 25, 27, 92, 26, 88, 120, 121, 86, 88, 121, 86, 89, 88, 87, 89, 86, 90, 89, 87, 91, 90, 87, 93, 98, 120, 88, 93, 120, 94, 99, 93, 89, 93, 88, 94, 93, 89, 90, 94, 89, 95, 100, 94, 101, 100, 95, 102, 36, 101, 35, 36, 102, 95, 94, 90, 33, 102, 101, 33, 101, 95, 34, 35, 102, 34, 102, 33, 96, 33, 95, 28, 95, 90, 96, 95, 28, 32, 33, 96, 97, 32, 96, 29, 96, 28, 97, 96, 29, 30, 97, 29, 31, 32, 97, 31, 97, 30, 117, 107, 115, 107, 106, 112, 98, 107, 117, 120, 98, 117, 99, 106, 107, 98, 99, 107, 116, 120, 117, 99, 98, 93, 99, 105, 106, 100, 99, 94, 103, 39, 40, 104, 103, 41, 103, 40, 41, 38, 39, 103, 37, 103, 104, 38, 103, 37, 105, 104, 110, 99, 100, 105, 104, 105, 100, 101, 104, 100, 36, 37, 104, 101, 36, 104, 45, 108, 44, 46, 108, 45, 43, 44, 108, 109, 108, 46, 109, 46, 47, 43, 108, 109, 111, 48, 112, 47, 48, 111, 110, 47, 111, 109, 47, 110, 42, 43, 109, 106, 111, 112, 110, 41, 42, 110, 42, 109, 105, 110, 111, 105, 111, 106, 110, 104, 41, 113, 122, 123, 113, 112, 50, 113, 50, 122, 49, 50, 112, 48, 49, 112, 115, 107, 113, 112, 113, 107, 114, 123, 55, 54, 114, 55, 113, 123, 114, 115, 114, 54, 53, 115, 54, 114, 115, 113, 117, 115, 53, 57, 0, 1, 52, 0, 57, 56, 1, 2, 57, 1, 56, 51, 52, 57, 122, 57, 56, 51, 57, 122, 50, 51, 122, 123, 122, 56, 58, 2, 3, 56, 2, 58, 59, 3, 4, 58, 3, 59, 55, 56, 58, 55, 58, 59, 123, 56, 55, 5, 59, 4, 66, 59, 5, 66, 5, 6, 60, 59, 66, 60, 55, 59, 60, 54, 55, 61, 54, 60, 60, 65, 61, 65, 60, 66, 70, 8, 9, 70, 9, 10, 69, 64, 8, 69, 8, 70, 10, 69, 70, 11, 69, 10, 12, 69, 11, 73, 68, 69, 73, 69, 12, 65, 66, 6, 65, 6, 7, 64, 65, 7, 64, 61, 65, 64, 7, 8, 64, 63, 61, 67, 63, 64, 68, 67, 64, 69, 68, 64, 62, 54, 61, 63, 62, 61, 62, 53, 54, 118, 53, 62, 118, 62, 63, 118, 63, 67, 116, 117, 53, 116, 53, 118, 73, 12, 13, 14, 73, 13, 76, 72, 73, 76, 73, 14, 75, 72, 76, 77, 76, 14, 77, 14, 15, 16, 77, 15, 83, 75, 76, 84, 75, 83, 17, 77, 16, 18, 76, 77, 18, 77, 17, 83, 76, 18, 82, 83, 18, 19, 82, 18, 71, 118, 67, 71, 67, 68, 119, 116, 118, 119, 118, 71, 72, 71, 68, 121, 116, 119, 120, 116, 121, 74, 119, 71, 74, 71, 72, 72, 68, 73, 85, 119, 74, 78, 121, 119, 85, 78, 119, 75, 74, 72, 85, 74, 75, 84, 85, 75], "vertices": [1, 92, 21.96, -3.64, 1, 1, 92, 7.57, -4.44, 1, 2, 91, 21.87, -6.95, 0.24722, 92, 1.74, -8.19, 0.75278, 5, 73, -2.65, 32.03, 0.00025, 74, -32.44, 14.47, 0.00341, 90, 25.36, -2.65, 0.02238, 91, 9.05, -10.68, 0.96968, 92, -11.54, -6.87, 0.00427, 4, 73, 9.89, 27.39, 0.04801, 74, -19.89, 19.05, 0.09255, 90, 18.38, -14.05, 0.45627, 91, -3.53, -15.19, 0.40317, 5, 73, 17.87, 22, 0.1153, 74, -10.31, 20.11, 0.27577, 75, -4.63, 31.73, 0.00183, 90, 11.54, -20.84, 0.50341, 91, -13.12, -16.19, 0.10369, 5, 73, 25.34, 14.8, 0.09826, 74, 0.04, 19.46, 0.56216, 75, 2.49, 24.19, 0.03373, 90, 3.03, -26.77, 0.29524, 91, -23.46, -15.49, 0.01061, 5, 83, 3.34, 40.7, 0, 73, 29.26, 5.31, 0.01552, 74, 9.18, 14.77, 0.70333, 75, 5.96, 14.52, 0.18606, 90, -7.04, -28.78, 0.09508, 4, 83, 7.52, 33.71, 2e-05, 74, 15.34, 9.44, 0.30503, 75, 6.83, 6.43, 0.68324, 90, -15.18, -28.44, 0.01171, 4, 83, 14.1, 33.08, 0.00043, 74, 21.79, 10.9, 0.00735, 75, 12.53, 3.09, 0.99214, 90, -19.32, -33.59, 8e-05, 4, 83, 16.4, 26.84, 0.0183, 74, 25.92, 5.7, 0.00012, 75, 12, -3.53, 0.98158, 90, -25.79, -32.09, 0, 4, 82, 16.71, 26.24, 0.00903, 83, 17.62, 19.41, 0.14452, 75, 9.99, -10.79, 0.84646, 90, -32.68, -29.04, 0, 5, 82, 17.5, 19.86, 0.03979, 83, 14.35, 13.86, 0.40266, 74, 28.05, -7.27, 0.01613, 75, 4.7, -14.46, 0.54141, 90, -35.52, -23.26, 0, 5, 82, 21.81, 18.75, 0.03011, 83, 17.09, 10.36, 0.6084, 74, 31.75, -9.73, 0.00858, 75, 5.72, -18.78, 0.35292, 90, -39.95, -23.63, 0, 5, 82, 23.15, 12.38, 0.01154, 83, 14.27, 4.49, 0.89437, 74, 30.9, -16.19, 0.0036, 75, 0.69, -22.92, 0.09048, 90, -43.31, -18.05, 0, 3, 83, 19.16, 3.88, 0.99471, 75, 4.88, -25.53, 0.00529, 90, -46.5, -21.8, 0, 2, 83, 18.29, -2.07, 0.99187, 85, 20.78, 23.62, 0.00813, 2, 83, 15.79, -6.21, 0.92187, 85, 19.96, 18.85, 0.07813, 3, 83, 11.63, -8.87, 0.66347, 84, 11.28, 22.36, 0.00336, 85, 17.04, 14.85, 0.33316, 2, 83, 16.52, -9.48, 0.50624, 85, 21.82, 16.07, 0.49376, 2, 83, 15.38, -17.31, 0.34726, 85, 23.61, 8.36, 0.65274, 2, 83, 12.95, -22.06, 0.22056, 85, 23.08, 3.05, 0.77944, 2, 83, 5, -26.28, 0.02608, 85, 17.21, -3.77, 0.97392, 2, 89, 10.69, 10.48, 0.05384, 85, 9.11, -6.92, 0.94616, 3, 89, 6.03, 5.93, 0.48777, 84, 14.23, -3.36, 0.00222, 85, 2.6, -6.64, 0.51001, 2, 89, 12.27, 2.13, 0.99178, 85, 4.1, -13.79, 0.00822, 2, 87, 4.63, 27.76, 0.0361, 89, 18.91, -5.01, 0.9639, 3, 87, 7.7, 18.03, 0.19817, 88, 23.81, -12.85, 0.01964, 89, 12.01, -12.52, 0.78219, 4, 87, 6.35, 10.35, 0.62012, 86, 20.96, 7.5, 0.01048, 88, 16.41, -15.31, 0.05495, 89, 4.68, -15.18, 0.31445, 3, 87, 12.69, 10, 0.93298, 88, 19.11, -21.06, 0.00631, 89, 7.54, -20.85, 0.0607, 2, 87, 19.51, 8.12, 0.9955, 89, 9.32, -27.7, 0.0045, 1, 87, 25.16, 5.19, 1, 2, 87, 20.39, 2.63, 0.99992, 89, 4.99, -31.2, 8e-05, 3, 80, 8.81, 18.17, 0.15676, 81, -12.03, 15.12, 0.03662, 87, 16.26, -3.52, 0.80662, 3, 80, 14.84, 17.78, 0.34905, 81, -6.19, 16.68, 0.14612, 87, 21.49, -6.55, 0.50483, 3, 80, 19.37, 15.86, 0.36314, 81, -1.29, 16.3, 0.17177, 87, 24.69, -10.29, 0.46509, 3, 80, 15.63, 13.01, 0.38442, 81, -3.93, 12.41, 0.23305, 87, 20.07, -11.18, 0.38253, 3, 80, 21.29, 10.22, 0.21766, 81, 2.33, 11.58, 0.65854, 87, 23.91, -16.19, 0.1238, 3, 80, 26.52, 4.3, 0.00986, 81, 9.18, 7.63, 0.97289, 87, 25.95, -23.82, 0.01725, 2, 78, 23.25, 19.41, 0.00949, 81, 14.09, 0.11, 0.99051, 2, 78, 25.18, 14.54, 0.0497, 81, 16.31, -4.63, 0.9503, 3, 78, 20.31, 13.52, 0.26422, 80, 24.38, -9.31, 0.0087, 81, 11.5, -5.94, 0.72708, 2, 78, 25.17, 8.55, 0.7728, 81, 16.66, -10.61, 0.2272, 2, 78, 27.98, 0.62, 0.97241, 81, 19.93, -18.36, 0.02759, 1, 78, 27.19, -7.96, 1, 1, 78, 21.99, -14.1, 1, 3, 92, 32.54, 35.07, 0, 77, 27.26, -21.23, 0.00022, 78, 21.47, -6.83, 0.99978, 3, 92, 27.04, 36.14, 0.00244, 77, 25.34, -15.96, 0.04077, 78, 16.11, -5.17, 0.95679, 3, 92, 24.27, 29.99, 0.02612, 77, 18.64, -16.76, 0.30839, 78, 12.7, -10.99, 0.66549, 4, 92, 20.31, 22.54, 0.11637, 76, 32.16, -1.21, 0.00015, 77, 10.22, -17.2, 0.60304, 78, 7.96, -17.97, 0.28043, 5, 91, 26.06, 21.69, 0.00254, 92, 16.31, 16.82, 0.36459, 76, 27.86, -6.71, 0.02153, 77, 3.26, -16.73, 0.53267, 78, 3.37, -23.24, 0.07867, 5, 91, 30.67, 18.02, 0.0001, 92, 19.22, 11.69, 0.69764, 76, 30.48, -11.99, 0.01785, 77, 0.36, -21.87, 0.26584, 78, 5.72, -28.65, 0.01858, 4, 92, 21.8, 2.85, 0.96985, 76, 32.56, -20.96, 0.00062, 77, -5.89, -28.63, 0.02872, 78, 7.33, -37.72, 0.00081, 8, 82, -18.31, 0.64, 0.07406, 73, -5.29, -2.48, 0.55458, 90, -7.98, 6.63, 0.0181, 76, -7.62, 5.2, 0.27741, 77, -6.83, 19.31, 0.00149, 79, -20.74, -4.27, 0.06906, 86, -28.54, -4.27, 0.00035, 88, -28.51, 8.57, 0.00495, 5, 73, -4.54, 6.71, 0.04244, 90, 0.89, 4.11, 0.65955, 91, -5.79, 9.92, 0.00895, 76, -5.98, -3.88, 0.28865, 79, -25.46, -12.2, 0.00041, 4, 90, 13.79, 5.61, 0.08185, 91, 5.21, 3.01, 0.82023, 92, -10, 7.27, 0.0018, 76, 1.06, -14.8, 0.09612, 4, 91, 16.84, 3.12, 0.41317, 92, 0.83, 3.03, 0.5306, 76, 11.64, -19.62, 0.04285, 77, -16.54, -10.57, 0.01338, 5, 91, 26.3, 8.44, 0.00666, 92, 11.59, 4.43, 0.89891, 76, 22.45, -18.81, 0.0214, 77, -9.8, -19.06, 0.07121, 78, -2.66, -35.04, 0.00182, 4, 73, -4.77, 27.42, 1e-05, 74, -31.07, 9.58, 0.0019, 90, 21.25, 0.32, 0.00964, 91, 7.7, -5.78, 0.98846, 4, 73, 4.7, 21.37, 0.03718, 74, -19.93, 11.1, 0.05274, 90, 13.47, -7.8, 0.52195, 91, -3.44, -7.24, 0.38813, 4, 73, 7.5, 14.33, 0.15009, 74, -13.25, 7.55, 0.09401, 90, 6.03, -9.17, 0.7076, 91, -10.11, -3.65, 0.0483, 3, 73, 6.2, 6.97, 0.43733, 74, -9.47, 1.1, 0.02665, 90, -0.94, -6.47, 0.53602, 5, 82, -15.46, 8.3, 0.01898, 73, 2.76, -1.08, 0.97397, 74, -6.87, -7.26, 0.00522, 79, -28.59, -1.99, 0.0017, 88, -29.72, 16.65, 0.00013, 4, 82, -9.85, 10.13, 0.1385, 73, 7.17, -5, 0.6776, 74, -0.98, -7.39, 0.18216, 79, -30.82, 3.47, 0.00175, 3, 74, 3.24, 0.4, 0.9972, 75, -8.19, 8.08, 0.001, 90, -11.33, -13.83, 0.0018, 5, 73, 18.49, 8.53, 0.1651, 74, -1.12, 10.25, 0.56382, 75, -4.65, 18.25, 0.02533, 90, -1.79, -18.83, 0.242, 91, -22.25, -6.28, 0.00375, 5, 73, 16.62, 16.15, 0.14717, 74, -7.47, 14.84, 0.33792, 75, -6.15, 25.95, 0.00522, 90, 6.04, -18.48, 0.45905, 91, -15.92, -10.91, 0.05064, 6, 82, -3.9, 8.52, 0.41348, 83, -9.52, 17.94, 0.00281, 73, 8.79, -10.94, 0.25757, 74, 4.1, -10.87, 0.32428, 75, -15.26, -0.74, 0.00063, 79, -29.67, 9.52, 0.00123, 6, 82, 3.42, 13.91, 0.2753, 83, -0.43, 17.74, 0.06946, 73, 17.13, -14.54, 0.01629, 74, 12.79, -8.21, 0.51647, 75, -7.09, -4.73, 0.12249, 90, -24.16, -13.03, 0, 4, 82, 6.2, 22.87, 0.01637, 83, 7.23, 23.14, 0.03895, 74, 18.38, -0.68, 0.03176, 75, 2.13, -3.04, 0.91293, 3, 74, 20.19, 5.77, 0.00432, 75, 7.86, 0.44, 0.99558, 90, -21.26, -28.58, 0.0001, 6, 82, 0.93, 3.16, 0.86834, 83, -8.96, 10.74, 0.00448, 73, 6.6, -17.82, 0.03977, 74, 6.89, -17.53, 0.08425, 75, -17.77, -7.51, 0.00317, 90, -25.34, -2.06, 0, 5, 82, 9.84, 6.83, 0.57352, 83, 0.34, 8.21, 0.25025, 74, 16.51, -17.02, 0.10095, 75, -10.39, -13.71, 0.07528, 90, -32.56, -8.45, 0, 5, 82, 15.99, 12.06, 0.12001, 83, 8.4, 8.61, 0.60855, 74, 24.04, -14.12, 0.04672, 75, -2.9, -16.73, 0.22472, 90, -36.65, -15.41, 0, 4, 82, 5.7, -2.87, 0.88656, 79, -19.01, 19.94, 0.00212, 88, -5.79, 17.11, 0.01207, 84, -8.91, 9.95, 0.09925, 4, 82, 14.14, -1.93, 0.63344, 83, -1.6, -1.35, 0.27562, 84, -1.6, 14.26, 0.06648, 85, 1.99, 17.04, 0.02446, 3, 83, 7.4, -3.51, 0.90539, 84, 4.56, 21.17, 0.00657, 85, 11.15, 18.31, 0.08805, 2, 83, 13.98, -1.73, 0.98222, 85, 16.64, 22.37, 0.01778, 8, 82, 7.04, -10.67, 0.35361, 83, -12.56, -3.94, 0.00219, 76, -17.67, 31.08, 0.00017, 79, -11.33, 21.84, 0.00634, 86, -9.34, 15.78, 0.00051, 88, -0.85, 10.93, 0.0706, 84, -4.49, 3.38, 0.56433, 85, -7.27, 10.64, 0.00225, 4, 82, 14.64, -12.77, 0.11117, 83, -7.82, -10.24, 0.05356, 84, 3.3, 4.57, 0.69034, 85, -0.57, 6.49, 0.14492, 4, 82, 22.72, -12.68, 0.02263, 83, -1.37, -15.11, 0.11859, 84, 10.63, 7.97, 0.09592, 85, 7.21, 4.32, 0.76286, 4, 82, 28.54, -9.39, 0.00264, 83, 5.26, -16.05, 0.24679, 84, 14.59, 13.36, 0.0157, 85, 13.72, 5.85, 0.73487, 3, 83, 10.66, -14.54, 0.39836, 84, 15.82, 18.83, 0.00249, 85, 18.2, 9.22, 0.59916, 4, 82, 24.22, -4.35, 0.01592, 83, 4.91, -9.42, 0.56408, 84, 8.59, 16.18, 0.04787, 85, 10.98, 11.9, 0.37213, 4, 82, 16.19, -6.61, 0.29158, 83, -2.83, -6.31, 0.26986, 84, 2.19, 10.83, 0.27687, 85, 2.65, 11.97, 0.16169, 7, 82, 7.7, -7.34, 0.58796, 83, -10, -1.71, 0.00381, 76, -20.49, 29.18, 0, 79, -14.7, 22.26, 0.00348, 88, -1.88, 14.17, 0.02705, 84, -5.25, 6.69, 0.37247, 85, -5.7, 13.65, 0.00524, 6, 82, 5.56, -16.7, 0.05551, 73, -8.21, -31.84, 0.00026, 76, -12.38, 34.32, 0.00011, 79, -5.21, 20.82, 0.00443, 88, 0.77, 4.94, 0.47299, 84, -3.37, -2.72, 0.4667, 3, 88, 8.9, 4.18, 0.46704, 89, -3.38, 4.08, 0.01723, 84, 4.66, -4.16, 0.51573, 7, 82, -0.69, -20.97, 0.01992, 73, -15.05, -28.6, 0.00191, 76, -4.93, 32.95, 0.00044, 79, -0.49, 14.89, 0.01859, 86, -2.25, 5.03, 0.31043, 88, -2.64, -1.82, 0.63648, 84, -7.33, -9.18, 0.01223, 4, 87, -9.05, 3.15, 0.00351, 86, 4.01, 6.1, 0.43043, 88, 2.77, -5.16, 0.56109, 89, -9.24, -5.42, 0.00498, 4, 87, -5.37, 8.86, 0.10622, 86, 9.45, 10.18, 0.16378, 88, 9.55, -5.69, 0.52918, 89, -2.45, -5.76, 0.20082, 4, 87, -2.03, 16.03, 0.10866, 86, 15.07, 15.74, 0.01625, 88, 17.44, -5.23, 0.0728, 89, 5.42, -5.08, 0.80229, 3, 87, 1.81, 22, 0.06474, 88, 24.51, -5.79, 0.00307, 89, 12.51, -5.43, 0.93218, 7, 82, -5.73, -23.87, 0.00518, 73, -20.1, -25.71, 0.00079, 76, 0.68, 31.47, 3e-05, 79, 2.77, 10.09, 0.08152, 86, -1.22, -0.69, 0.90753, 88, -5.64, -6.8, 0.00445, 84, -10.73, -13.89, 0.00049, 4, 80, -7.97, 8.62, 0.02239, 79, 11.31, 11.03, 0.1381, 87, -3.02, -4.63, 0.06365, 86, 6.97, -3.29, 0.77587, 3, 87, 5.4, 0.13, 0.99899, 88, 6.96, -19.31, 0.00029, 89, -4.65, -19.45, 0.00072, 3, 87, 12.45, 3.25, 0.98312, 88, 13.04, -24.04, 0.0011, 89, 1.56, -24, 0.01578, 2, 87, 19.25, 5.1, 0.99798, 89, 6.56, -28.98, 0.00202, 6, 82, -13.69, -20.64, 0.00941, 73, -21.33, -17.21, 0.00286, 79, 0.14, 1.91, 0.88604, 86, -6.94, -7.1, 0.09218, 88, -14.17, -7.82, 0.00902, 84, -19.31, -14.21, 0.0005, 3, 79, 9.11, 1.09, 0.95479, 87, -5.83, -14.41, 0.00119, 86, 0.92, -11.49, 0.04402, 4, 80, 0.73, 5.86, 0.51458, 79, 18.26, 5.12, 0.15527, 87, 3.55, -10.96, 0.24209, 86, 10.92, -11.51, 0.08806, 5, 80, 7.52, 12.22, 0.39746, 81, -11.35, 9.07, 0.0468, 79, 26.98, 8.35, 0.00114, 87, 12.46, -8.28, 0.5534, 86, 20.21, -12.09, 0.00119, 3, 80, 13.56, 15.32, 0.35713, 81, -6.62, 13.93, 0.14476, 87, 19.25, -8.19, 0.49811, 2, 81, 6.56, 1.72, 0.9971, 87, 19.92, -26.14, 0.0029, 2, 80, 12.82, 0.38, 0.99602, 87, 11.94, -21.25, 0.00398, 5, 77, 30.93, 7.66, 0.01208, 78, 0.67, 13.55, 0.13802, 80, 5.44, -4.11, 0.75844, 81, -8.1, -7.08, 0.00243, 79, 18.73, -5.9, 0.08902, 4, 77, 22.62, 9.57, 0.18552, 78, -5.88, 8.09, 0.10582, 80, -2.31, -7.66, 0.08401, 79, 10.21, -6.17, 0.62465, 6, 82, -20.13, -17.84, 0.00886, 73, -22.16, -10.24, 0.0191, 76, 6.67, 17.05, 0.09619, 77, 11.01, 14.14, 0.18154, 79, -2.18, -4.72, 0.69383, 88, -21.16, -8.48, 0.00048, 1, 78, 24.09, -6.06, 1, 2, 78, 20.85, 0.12, 0.98914, 81, 12.84, -19.29, 0.01086, 3, 78, 11.81, 4.34, 0.85489, 80, 13.78, -15.93, 0.06315, 81, 3.57, -15.61, 0.08196, 4, 78, 2.84, 0.44, 0.99002, 80, 4.1, -17.34, 0.00699, 81, -5.15, -20.03, 0.00041, 79, 12.37, -17.57, 0.00257, 3, 92, 10.65, 32.47, 0.01021, 77, 13.75, -3.81, 0.85103, 78, -0.57, -7.07, 0.13876, 4, 91, 11.43, 23.45, 0.00245, 92, 3.4, 23.91, 0.02225, 77, 2.67, -2.01, 0.97046, 78, -8.71, -14.8, 0.00485, 4, 90, 3.31, 14.55, 0.01226, 91, 2.64, 16.55, 0.02251, 92, -7.33, 20.78, 0.00177, 76, 4.46, -1.45, 0.96347, 7, 82, -23.26, -4.09, 0.03152, 73, -11.88, -0.6, 0.1578, 90, -4.87, 12.72, 0.00212, 76, -0.78, 5.08, 0.66661, 77, -3.07, 13.58, 0.02944, 79, -15.65, -8.85, 0.10979, 88, -30.55, 2.03, 0.00272, 8, 82, -7.03, -7.56, 0.41945, 73, -6.68, -16.36, 0.14492, 76, -9.87, 18.97, 0.06142, 77, 3.3, 28.9, 0, 79, -13.4, 7.59, 0.19037, 86, -17.01, 3.58, 0.04198, 88, -14.67, 6.85, 0.10038, 84, -18.6, 0.45, 0.04147, 8, 82, -18.35, -9.12, 0.08964, 73, -13.73, -7.38, 0.16487, 76, -0.73, 12.11, 0.31751, 77, 2.76, 17.49, 0.05672, 79, -11.01, -3.58, 0.34961, 86, -19.36, -7.6, 0.00526, 88, -23.82, 0.01, 0.01555, 84, -28.29, -5.6, 0.00085, 8, 82, -7.83, 1.11, 0.4784, 73, 0.4, -11.29, 0.37979, 74, -2.06, -16.57, 0.03559, 76, -15.4, 12.25, 0.03523, 79, -21.98, 6.15, 0.05297, 86, -25.44, 5.75, 0.00297, 88, -19.56, 14.05, 0.01317, 84, -22.88, 8.03, 0.00188, 7, 82, 1.38, -4.94, 0.7676, 73, -0.17, -22.3, 0.00827, 76, -17.69, 23.02, 0.00445, 79, -16.63, 15.78, 0.02649, 86, -16.64, 12.38, 0.0065, 88, -8.57, 13.21, 0.06351, 84, -12, 6.29, 0.12318, 7, 82, -5.43, -15.97, 0.15786, 73, -13.13, -21.98, 0.03161, 76, -5.09, 26.06, 0.01655, 79, -5.13, 9.8, 0.23527, 86, -8.55, 2.25, 0.2443, 88, -9.2, 0.26, 0.26498, 84, -13.7, -6.56, 0.04943, 7, 82, -0.39, -11.61, 0.36141, 73, -6.82, -24.13, 0.02145, 76, -11.73, 26.51, 0.00961, 79, -9.85, 14.5, 0.07885, 86, -10.96, 8.47, 0.05045, 88, -6.9, 6.51, 0.27316, 84, -10.89, -0.52, 0.20507, 5, 91, 19.39, 16.13, 0.0569, 92, 8.05, 14.15, 0.38586, 76, 19.46, -8.92, 0.14687, 77, -3.29, -11.03, 0.3946, 78, -5.13, -25.01, 0.01577, 6, 90, 11.68, 16.78, 0.01134, 91, 10.56, 13.04, 0.2254, 92, -1.29, 14.57, 0.11414, 76, 10.15, -7.98, 0.57714, 77, -7.75, -2.8, 0.07196, 78, -14.38, -23.58, 3e-05], "hull": 53, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 0, 104, 106, 108, 108, 110, 110, 112, 112, 114, 114, 0, 116, 118, 118, 120, 120, 122, 122, 124, 126, 128, 128, 130, 130, 132, 134, 136, 136, 138, 138, 140, 142, 144, 144, 146, 148, 150, 150, 152, 152, 154, 156, 158, 158, 160, 160, 162, 162, 164, 36, 166, 166, 168, 168, 170, 172, 174, 174, 48, 176, 178, 178, 180, 180, 182, 182, 184, 186, 188, 188, 190, 190, 192, 192, 194, 196, 198, 198, 200, 200, 202, 202, 204, 204, 70, 80, 206, 206, 208, 208, 210, 210, 212, 212, 214, 90, 216, 216, 218, 218, 220, 220, 222, 222, 224, 224, 226, 226, 228, 228, 230, 100, 244, 244, 246], "width": 102, "height": 101}}}}], "events": {"atk": {}}, "animations": {"boss_attack1": {"slots": {"mv_she_020": {"color": [{"time": 0.2333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff"}], "attachment": [{"time": 0.2333, "name": "mv_she_020"}, {"time": 0.4667, "name": null}]}, "ef1/qianghen_add_02": {"attachment": [{"time": 0.3667, "name": "ef1/qianghen_add_02"}, {"time": 0.4, "name": "ef1/qianghen_add_04"}, {"time": 0.4333, "name": "ef1/qianghen_add_05"}, {"time": 0.4667, "name": null}]}}, "bones": {"bone2": {"rotate": [{"angle": -178.51}], "translate": [{"x": 305.13, "y": 168.97}]}, "bone3": {"rotate": [{"angle": 1.99}]}, "bone4": {"rotate": [{"angle": 12.11}]}, "bone5": {"rotate": [{"angle": 28.69}]}, "bone6": {"rotate": [{"angle": 26.34}]}, "bone7": {"rotate": [{"angle": 25.92}]}, "bone8": {"rotate": [{"angle": 22.65}]}, "bone9": {"rotate": [{"angle": 36.5}]}, "bone10": {"rotate": [{"angle": 94.49}]}, "bone11": {"rotate": [{"angle": 21.76}]}, "bone12": {"rotate": [{"angle": 6.75}]}, "bone13": {"rotate": [{"angle": 3.65}]}, "bone14": {"rotate": [{"angle": 2.37}]}, "bone15": {"rotate": [{"angle": 1.55}]}, "bone16": {"rotate": [{"angle": 1.62}]}, "bone17": {"rotate": [{"angle": 4.54}]}, "bone18": {"rotate": [{"angle": 9.81}]}, "bone19": {"rotate": [{"angle": 27.12}]}, "bone20": {"rotate": [{"angle": 53.28}]}, "bone21": {"rotate": [{"angle": 55.68}]}, "bone22": {"rotate": [{"angle": 28.39}]}, "bone23": {"rotate": [{"angle": 10.58}]}, "bone24": {"rotate": [{"angle": 6.67}]}, "bone25": {"rotate": [{"angle": 10.41}]}, "bone26": {"rotate": [{"angle": -0.85}]}, "bone27": {"rotate": [{"angle": -3.77}]}, "bone28": {"rotate": [{"angle": -5.55}]}, "bone29": {"rotate": [{"angle": -7.47}]}, "bone30": {"rotate": [{"angle": -9.79}]}, "bone31": {"rotate": [{"angle": -11.75}]}, "bone32": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -11.48, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -7.32, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 5.01, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone38": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -13.29, "y": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 6.13, "y": -2.57, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone39": {"rotate": [{"angle": -0.29, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 3.81, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -1.59, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.7667, "angle": -0.29}]}, "bone40": {"rotate": [{"angle": -0.8, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 3.81, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -1.59, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.7667, "angle": -0.8}]}, "bone41": {"rotate": [{"angle": 4.61, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -13.81, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 5.65, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 4.61}]}, "bone44": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 68.79, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -58.17, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 0.4667, "angle": -39.77, "curve": 0.292, "c2": 0.2, "c3": 0.656, "c4": 0.63}, {"time": 0.7667}]}, "bone45": {"rotate": [{"curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.1, "angle": -144.32, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2333, "angle": 132.63, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -115.35, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 0.4667, "angle": -150.93, "curve": 0.292, "c2": 0.2, "c3": 0.656, "c4": 0.63}, {"time": 0.7667}]}, "bone46": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -30.55, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone47": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 57.28, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone48": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -14.15, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 13.95, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone49": {"rotate": [{"angle": 3.13, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -14.15, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 13.95, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.7667, "angle": 3.13}]}, "bone51": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -14.15, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 13.95, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone52": {"rotate": [{"angle": 3.13, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -14.15, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 13.95, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.7667, "angle": 3.13}]}, "bone54": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -9.32, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 10.08, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone55": {"rotate": [{"angle": 2.26, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -9.32, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 10.08, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.7667, "angle": 2.26}]}, "bone57": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -9.32, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 10.08, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone58": {"rotate": [{"angle": 2.26, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -9.32, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 10.08, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.7667, "angle": 2.26}]}, "bone61": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 40.6, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 37.04, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 14.89, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone62": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -56.13, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -52.67, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -18.84, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone68": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 42.61, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": 50.6, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4667, "angle": 22.69, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone64": {"rotate": [{"angle": -0.47}]}, "bone65": {"rotate": [{"angle": 0.14}]}, "bone66": {"rotate": [{"angle": 0.26}]}, "bone67": {"rotate": [{"angle": 0.06}]}, "gong": {"rotate": [{"curve": 0.252, "c3": 0.622, "c4": 0.49}, {"time": 0.3667, "angle": -1.33, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.7667}], "translate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 12.39, "y": 42.53, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.4667, "x": 26.24, "y": 75.75, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.5, "x": -2.6, "y": 13.98, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -2.73, "y": -7.63, "curve": 0.331, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 0.6, "x": -1.76, "y": 6.5, "curve": 0.37, "c2": 0.48, "c3": 0.753}, {"time": 0.6667}]}, "toufa2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -11.18, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 8.23, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "toufa3": {"rotate": [{"angle": 2.34, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -11.18, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 8.23, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.7667, "angle": 2.34}]}, "toufa4": {"rotate": [{"angle": 5.89, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -11.18, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 8.23, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.7667, "angle": 5.89}]}, "toufa5": {"rotate": [{"angle": -4.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.84, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -8.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.7667, "angle": -4.25}]}, "toufa6": {"rotate": [{"angle": -7.68, "curve": 0.295, "c2": 0.21, "c3": 0.667, "c4": 0.67}, {"time": 0.1333, "angle": -2.41, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 9.84, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -8.5, "curve": 0.29, "c3": 0.629, "c4": 0.37}, {"time": 0.7667, "angle": -7.68}]}, "toufa7": {"rotate": [{"angle": -7.09, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "angle": -8.5, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1333, "angle": -6.09, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 9.84, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.7667, "angle": -7.09}]}, "toufa8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 9.84, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -8.5, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "toufa9": {"rotate": [{"angle": -2.41, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 9.84, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -8.5, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.7667, "angle": -2.41}]}, "toufa10": {"rotate": [{"angle": -6.09, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 9.84, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -8.5, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.7667, "angle": -6.09}]}, "toufa11": {"rotate": [{"angle": 4.11, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -11.18, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 8.23, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.7667, "angle": 4.11}]}, "toufa12": {"rotate": [{"angle": 7.43, "curve": 0.295, "c2": 0.21, "c3": 0.667, "c4": 0.67}, {"time": 0.1333, "angle": 2.34, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -11.18, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 8.23, "curve": 0.29, "c3": 0.629, "c4": 0.37}, {"time": 0.7667, "angle": 7.43}]}, "toufa13": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -11.18, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 8.23, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "toufa14": {"rotate": [{"angle": 2.34, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -11.18, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 8.23, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.7667, "angle": 2.34}]}, "toufa15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 9.84, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -8.5, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "toufa16": {"rotate": [{"angle": -2.41, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 9.84, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -8.5, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.7667, "angle": -2.41}]}, "toufa17": {"rotate": [{"angle": -4.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.84, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -8.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.7667, "angle": -4.25}]}, "toufa18": {"rotate": [{"angle": -4.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.84, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -8.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.7667, "angle": -4.25}]}, "toufa19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 9.84, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -8.5, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "toufa20": {"rotate": [{"angle": -2.41, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 9.84, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -8.5, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.7667, "angle": -2.41}]}, "toufa21": {"rotate": [{"angle": -6.09, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 9.84, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -8.5, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.7667, "angle": -6.09}]}, "bone71": {"rotate": [{"angle": 2.77, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -9.02, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 9.75, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.7667, "angle": 2.77}]}, "bone72": {"rotate": [{"angle": 6.97, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.1, "angle": 2.77, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -9.02, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 9.75, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.7667, "angle": 6.97}]}, "bone73": {"rotate": [{"angle": 9.75, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1, "angle": 6.98, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -9.02, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 9.75}]}, "bone74": {"rotate": [{"angle": 5.54, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.1, "angle": 9.75, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -9.02, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.7667, "angle": 5.54}]}, "bone75": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -9.02, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 9.75, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone76": {"rotate": [{"angle": 2.77, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -9.02, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 9.75, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.7667, "angle": 2.77}]}, "bone77": {"rotate": [{"angle": 6.98, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -9.02, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 9.75, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.7667, "angle": 6.98}]}, "bone78": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -8.11, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 8.04, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "bone79": {"rotate": [{"angle": 2.28, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -8.11, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 8.04, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.7667, "angle": 2.28}]}, "bone80": {"rotate": [{"angle": 5.76, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -8.11, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 8.04, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.7667, "angle": 5.76}]}, "bone81": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -89.58}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 205.28, "y": 68.39, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 152.06, "y": 61.99, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 743.7, "y": 62.61}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 0.711, "y": 0.711}]}, "ef1": {"rotate": [{"time": 0.3667, "angle": 0.26}], "translate": [{"time": 0.3667, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.4667, "x": 555.4, "y": 2.48, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.7, "x": 1240.58, "y": 5.53}]}, "root": {"rotate": [{"angle": 0.08}]}}, "events": [{"time": 0.4333, "name": "atk"}]}, "boss_attack3": {"slots": {"mv_she_020": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.2333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff"}], "attachment": [{"name": "mv_she_020"}, {"time": 0.8667, "name": null}]}, "ef2/qf_njs_jn2_xl_02": {"attachment": [{"time": 0.3667, "name": "ef2/qf_njs_jn2_xl_02"}, {"time": 0.4333, "name": "ef2/qf_njs_jn2_xl_04"}, {"time": 0.5, "name": "ef2/qf_njs_jn2_xl_06"}, {"time": 0.5333, "name": "ef2/qf_njs_jn2_xl_08"}, {"time": 0.6, "name": "ef2/qf_njs_jn2_xl_10"}, {"time": 0.6667, "name": "ef2/qf_njs_jn2_xl_12"}, {"time": 0.7333, "name": "ef2/qf_njs_jn2_xl_16"}, {"time": 0.8, "name": null}]}, "ef1/qianghen_add_02": {"attachment": [{"time": 0.7667, "name": "ef1/qianghen_add_02"}, {"time": 0.8, "name": "ef1/qianghen_add_04"}, {"time": 0.8333, "name": "ef1/qianghen_add_05"}, {"time": 0.8667, "name": null}]}}, "bones": {"bone2": {"rotate": [{"angle": -178.51, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3667, "angle": 177.43, "curve": "stepped"}, {"time": 0.7667, "angle": 177.43, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.1667, "angle": -178.51}], "translate": [{"x": 305.13, "y": 168.97, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3667, "x": 304.87, "y": 169.13, "curve": "stepped"}, {"time": 0.7667, "x": 304.87, "y": 169.13, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.1667, "x": 305.13, "y": 168.97}]}, "bone3": {"rotate": [{"angle": 1.99, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3667, "angle": 3.2, "curve": "stepped"}, {"time": 0.7667, "angle": 3.2, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.1667, "angle": 1.99}]}, "bone4": {"rotate": [{"angle": 12.11, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3667, "angle": 12.58, "curve": "stepped"}, {"time": 0.7667, "angle": 12.58, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.1667, "angle": 12.11}]}, "bone5": {"rotate": [{"angle": 28.69, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3667, "angle": 28.57, "curve": "stepped"}, {"time": 0.7667, "angle": 28.57, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.1667, "angle": 28.69}]}, "bone6": {"rotate": [{"angle": 26.34, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3667, "angle": 25.92, "curve": "stepped"}, {"time": 0.7667, "angle": 25.92, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.1667, "angle": 26.34}]}, "bone7": {"rotate": [{"angle": 25.92, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3667, "angle": 28.12, "curve": "stepped"}, {"time": 0.7667, "angle": 28.12, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.1667, "angle": 25.92}]}, "bone8": {"rotate": [{"angle": 22.65, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3667, "angle": 22.84, "curve": "stepped"}, {"time": 0.7667, "angle": 22.84, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.1667, "angle": 22.65}]}, "bone9": {"rotate": [{"angle": 36.5, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3667, "angle": 23.92, "curve": "stepped"}, {"time": 0.7667, "angle": 23.92, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.1667, "angle": 36.5}]}, "bone10": {"rotate": [{"angle": 94.49, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3667, "angle": 90.67, "curve": "stepped"}, {"time": 0.7667, "angle": 90.67, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.1667, "angle": 94.49}]}, "bone11": {"rotate": [{"angle": 21.76, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3667, "angle": 34.71, "curve": "stepped"}, {"time": 0.7667, "angle": 34.71, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.1667, "angle": 21.76}]}, "bone12": {"rotate": [{"angle": 6.75, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3667, "angle": 8.06, "curve": "stepped"}, {"time": 0.7667, "angle": 8.06, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.1667, "angle": 6.75}]}, "bone13": {"rotate": [{"angle": 3.65, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3667, "angle": 4.08, "curve": "stepped"}, {"time": 0.7667, "angle": 4.08, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.1667, "angle": 3.65}]}, "bone14": {"rotate": [{"angle": 2.37, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3667, "angle": 2.59, "curve": "stepped"}, {"time": 0.7667, "angle": 2.59, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.1667, "angle": 2.37}]}, "bone15": {"rotate": [{"angle": 1.55, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3667, "angle": 1.76, "curve": "stepped"}, {"time": 0.7667, "angle": 1.76, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.1667, "angle": 1.55}]}, "bone16": {"rotate": [{"angle": 1.62, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3667, "angle": 1.28, "curve": "stepped"}, {"time": 0.7667, "angle": 1.28, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.1667, "angle": 1.62}]}, "bone17": {"rotate": [{"angle": 4.54, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3667, "angle": 3.85, "curve": "stepped"}, {"time": 0.7667, "angle": 3.85, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.1667, "angle": 4.54}]}, "bone18": {"rotate": [{"angle": 9.81, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3667, "angle": 8.26, "curve": "stepped"}, {"time": 0.7667, "angle": 8.26, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.1667, "angle": 9.81}]}, "bone19": {"rotate": [{"angle": 27.12, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3667, "angle": 21.37, "curve": "stepped"}, {"time": 0.7667, "angle": 21.37, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.1667, "angle": 27.12}]}, "bone20": {"rotate": [{"angle": 53.28, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3667, "angle": 49.78, "curve": "stepped"}, {"time": 0.7667, "angle": 49.78, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.1667, "angle": 53.28}]}, "bone21": {"rotate": [{"angle": 55.68, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3667, "angle": 55.82, "curve": "stepped"}, {"time": 0.7667, "angle": 55.82, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.1667, "angle": 55.68}]}, "bone22": {"rotate": [{"angle": 28.39, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3667, "angle": 37.03, "curve": "stepped"}, {"time": 0.7667, "angle": 37.03, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.1667, "angle": 28.39}]}, "bone23": {"rotate": [{"angle": 10.58, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3667, "angle": 12.82, "curve": "stepped"}, {"time": 0.7667, "angle": 12.82, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.1667, "angle": 10.58}]}, "bone24": {"rotate": [{"angle": 6.67, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3667, "angle": 6.33, "curve": "stepped"}, {"time": 0.7667, "angle": 6.33, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.1667, "angle": 6.67}]}, "bone25": {"rotate": [{"angle": 10.41, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3667, "angle": 11.32, "curve": "stepped"}, {"time": 0.7667, "angle": 11.32, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.1667, "angle": 10.41}]}, "bone26": {"rotate": [{"angle": -0.85, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3667, "angle": 0.02, "curve": "stepped"}, {"time": 0.7667, "angle": 0.02, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.1667, "angle": -0.85}]}, "bone27": {"rotate": [{"angle": -3.77, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3667, "angle": -3.65, "curve": "stepped"}, {"time": 0.7667, "angle": -3.65, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.1667, "angle": -3.77}]}, "bone28": {"rotate": [{"angle": -5.55, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3667, "angle": -5.44, "curve": "stepped"}, {"time": 0.7667, "angle": -5.44, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.1667, "angle": -5.55}]}, "bone29": {"rotate": [{"angle": -7.47, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3667, "angle": -7.37, "curve": "stepped"}, {"time": 0.7667, "angle": -7.37, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.1667, "angle": -7.47}]}, "bone30": {"rotate": [{"angle": -9.79, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3667, "angle": -9.64, "curve": "stepped"}, {"time": 0.7667, "angle": -9.64, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.1667, "angle": -9.79}]}, "bone31": {"rotate": [{"angle": -11.75, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3667, "angle": -11.76, "curve": "stepped"}, {"time": 0.7667, "angle": -11.76, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.1667, "angle": -11.75}]}, "bone32": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -11.48, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 4.1, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -7.32, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 5.01, "curve": 0.25, "c3": 0.75}, {"time": 1.1667}]}, "bone38": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -13.29, "y": 2.4, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 0.3667, "x": -5.01, "y": 0.28, "curve": "stepped"}, {"time": 0.7667, "x": -5.01, "y": 0.28, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 0.9333, "x": 6.13, "y": -2.57, "curve": 0.25, "c3": 0.75}, {"time": 1.1667}]}, "bone39": {"rotate": [{"angle": -0.29, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "angle": 16.6, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.7667, "angle": -8.06, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.8667, "angle": -1.59, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.1667, "angle": -0.29}]}, "bone40": {"rotate": [{"angle": -0.8, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667, "angle": 17.38, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.7667, "angle": -7.04, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.9, "angle": -1.59, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.1667, "angle": -0.8}]}, "bone41": {"rotate": [{"angle": 4.61, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -13.81, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.3667, "angle": -7.28, "curve": "stepped"}, {"time": 0.7667, "angle": -7.28, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.9333, "angle": 5.65, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 4.61}]}, "bone44": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 68.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3, "angle": 29.46, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667, "angle": -37.99, "curve": "stepped"}, {"time": 0.7667, "angle": -37.99, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 0.8333, "angle": -65.2, "curve": 0.321, "c2": 0.23, "c3": 0.654, "c4": 0.57}, {"time": 0.9, "angle": -15.16, "curve": 0.278, "c2": 0.15, "c3": 0.651, "c4": 0.61}, {"time": 1.1667}]}, "bone45": {"rotate": [{"curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.1, "angle": -144.32, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2333, "angle": 132.63, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3, "angle": -155.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667, "angle": -125.27, "curve": "stepped"}, {"time": 0.7667, "angle": -125.27, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 0.8333, "angle": -125.2, "curve": 0.321, "c2": 0.23, "c3": 0.654, "c4": 0.57}, {"time": 0.9, "angle": -122.49, "curve": 0.278, "c2": 0.15, "c3": 0.651, "c4": 0.61}, {"time": 1.1667}]}, "bone46": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -30.55, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3667, "angle": -24.91, "curve": "stepped"}, {"time": 0.7667, "angle": -24.91, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.1667}]}, "bone47": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 57.28, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3667, "angle": 46.72, "curve": "stepped"}, {"time": 0.7667, "angle": 46.72, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.1667}]}, "bone48": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -14.15, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.3667, "angle": 2.55, "curve": "stepped"}, {"time": 0.7667, "angle": 2.55, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.8667, "angle": 13.95, "curve": 0.25, "c3": 0.75}, {"time": 1.1667}]}, "bone49": {"rotate": [{"angle": 3.13, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -14.15, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.5667, "angle": -11.44, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.9333, "angle": 13.95, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 1.1667, "angle": 3.13}]}, "bone51": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -14.15, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.5667, "angle": 2.55, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.8667, "angle": 13.95, "curve": 0.25, "c3": 0.75}, {"time": 1.1667}]}, "bone52": {"rotate": [{"angle": 3.13, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -14.15, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.5667, "angle": -11.44, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.9333, "angle": 13.95, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 1.1667, "angle": 3.13}]}, "bone54": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -9.32, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.5667, "angle": 2.21, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.8667, "angle": 10.08, "curve": 0.25, "c3": 0.75}, {"time": 1.1667}]}, "bone55": {"rotate": [{"angle": 2.26, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -9.32, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.5667, "angle": -7.45, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.9333, "angle": 10.08, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 1.1667, "angle": 2.26}]}, "bone57": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -9.32, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.5667, "angle": 2.21, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.8667, "angle": 10.08, "curve": 0.25, "c3": 0.75}, {"time": 1.1667}]}, "bone58": {"rotate": [{"angle": 2.26, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -9.32, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.5667, "angle": -7.45, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.9333, "angle": 10.08, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 1.1667, "angle": 2.26}]}, "bone61": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 40.6, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 60.05, "curve": "stepped"}, {"time": 0.7667, "angle": 60.05, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 14.89, "curve": 0.25, "c3": 0.75}, {"time": 1.1667}]}, "bone62": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -56.13, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -53.58, "curve": "stepped"}, {"time": 0.7667, "angle": -53.58, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -18.84, "curve": 0.25, "c3": 0.75}, {"time": 1.1667}]}, "bone68": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 42.61, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": 50.6, "curve": "stepped"}, {"time": 0.7667, "angle": 50.6, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.8667, "angle": 22.69, "curve": 0.25, "c3": 0.75}, {"time": 1.1667}]}, "bone64": {"rotate": [{"angle": -0.47, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3667, "angle": -38.3, "curve": "stepped"}, {"time": 0.7667, "angle": -38.3, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.1667, "angle": -0.47}]}, "bone65": {"rotate": [{"angle": 0.14, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3667, "angle": 21.61, "curve": "stepped"}, {"time": 0.7667, "angle": 21.61, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.1667, "angle": 0.14}]}, "bone66": {"rotate": [{"angle": 0.26, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3667, "angle": -73.2, "curve": "stepped"}, {"time": 0.7667, "angle": -73.2, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.1667, "angle": 0.26}]}, "bone67": {"rotate": [{"angle": 0.06, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3667, "angle": 85.5, "curve": "stepped"}, {"time": 0.7667, "angle": 85.5, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.1667, "angle": 0.06}]}, "gong": {"rotate": [{"curve": 0.252, "c3": 0.622, "c4": 0.49}, {"time": 0.3667, "angle": -1.33, "curve": "stepped"}, {"time": 0.7667, "angle": -1.33, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 1.1667}], "translate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 26.64, "y": 74.03, "curve": 0.269, "c2": 0.07, "c3": 0.625, "c4": 0.5}, {"time": 0.7667, "x": 26.24, "y": 75.75, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.8333, "x": -2.73, "y": -7.63, "curve": 0.331, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 0.9, "x": -1.76, "y": 6.5, "curve": 0.37, "c2": 0.48, "c3": 0.753}, {"time": 0.9667}]}, "toufa2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -11.18, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.4333, "angle": 1.09, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.9, "angle": 8.23, "curve": 0.25, "c3": 0.75}, {"time": 1.1667}]}, "toufa3": {"rotate": [{"angle": 2.34, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -11.18, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.4333, "angle": -6.48, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1, "angle": 8.23, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.1667, "angle": 2.34}]}, "toufa4": {"rotate": [{"angle": 5.89, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -11.18, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 8.23, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.1667, "angle": 5.89}]}, "toufa5": {"rotate": [{"angle": -4.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.84, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 0.4333, "angle": 8.89, "curve": 0.293, "c2": 0.18, "c3": 0.755}, {"time": 1.0333, "angle": -8.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.1667, "angle": -4.25}]}, "toufa6": {"rotate": [{"angle": -7.68, "curve": 0.295, "c2": 0.21, "c3": 0.667, "c4": 0.67}, {"time": 0.1333, "angle": -2.41, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.4333, "angle": 8.56, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.8, "angle": 9.84, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -8.5, "curve": 0.29, "c3": 0.629, "c4": 0.37}, {"time": 1.1667, "angle": -7.68}]}, "toufa7": {"rotate": [{"angle": -7.09, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "angle": -8.5, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1333, "angle": -6.09, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.4333, "angle": 1.28, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.9, "angle": 9.84, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 1.1667, "angle": -7.09}]}, "toufa8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 9.84, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.4333, "angle": -1.76, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.9, "angle": -8.5, "curve": 0.25, "c3": 0.75}, {"time": 1.1667}]}, "toufa9": {"rotate": [{"angle": -2.41, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 9.84, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.4333, "angle": 5.39, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1, "angle": -8.5, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.1667, "angle": -2.41}]}, "toufa10": {"rotate": [{"angle": -6.09, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 9.84, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -8.5, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.1667, "angle": -6.09}]}, "toufa11": {"rotate": [{"angle": 4.11, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -11.18, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 0.4333, "angle": -10.18, "curve": 0.293, "c2": 0.18, "c3": 0.755}, {"time": 1.0333, "angle": 8.23, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.1667, "angle": 4.11}]}, "toufa12": {"rotate": [{"angle": 7.43, "curve": 0.295, "c2": 0.21, "c3": 0.667, "c4": 0.67}, {"time": 0.1333, "angle": 2.34, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.4333, "angle": -9.73, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.8, "angle": -11.18, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 8.23, "curve": 0.29, "c3": 0.629, "c4": 0.37}, {"time": 1.1667, "angle": 7.43}]}, "toufa13": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -11.18, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.4333, "angle": 1.09, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.9, "angle": 8.23, "curve": 0.25, "c3": 0.75}, {"time": 1.1667}]}, "toufa14": {"rotate": [{"angle": 2.34, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -11.18, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.4333, "angle": -6.48, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1, "angle": 8.23, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.1667, "angle": 2.34}]}, "toufa15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 9.84, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.4333, "angle": -1.76, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.9, "angle": -8.5, "curve": 0.25, "c3": 0.75}, {"time": 1.1667}]}, "toufa16": {"rotate": [{"angle": -2.41, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 9.84, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.4333, "angle": 5.39, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1, "angle": -8.5, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.1667, "angle": -2.41}]}, "toufa17": {"rotate": [{"angle": -4.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.84, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 0.4333, "angle": 8.89, "curve": 0.293, "c2": 0.18, "c3": 0.755}, {"time": 1.0333, "angle": -8.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.1667, "angle": -4.25}]}, "toufa18": {"rotate": [{"angle": -4.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.84, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 0.4333, "angle": 8.89, "curve": 0.293, "c2": 0.18, "c3": 0.755}, {"time": 1.0333, "angle": -8.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.1667, "angle": -4.25}]}, "toufa19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 9.84, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.4333, "angle": -1.76, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.9, "angle": -8.5, "curve": 0.25, "c3": 0.75}, {"time": 1.1667}]}, "toufa20": {"rotate": [{"angle": -2.41, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 9.84, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.4333, "angle": 5.39, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1, "angle": -8.5, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.1667, "angle": -2.41}]}, "toufa21": {"rotate": [{"angle": -6.09, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 9.84, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -8.5, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.1667, "angle": -6.09}]}, "bone71": {"rotate": [{"angle": 2.77, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -9.02, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.4333, "angle": -4.47, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1, "angle": 9.75, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.1667, "angle": 2.77}]}, "bone72": {"rotate": [{"angle": 6.97, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.1, "angle": 2.77, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -9.02, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 9.75, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.1667, "angle": 6.97}]}, "bone73": {"rotate": [{"angle": 9.75, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1, "angle": 6.98, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4333, "angle": -4.51, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8667, "angle": -9.02, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 9.75}]}, "bone74": {"rotate": [{"angle": 5.54, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.1, "angle": 9.75, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -9.02, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 1.1667, "angle": 5.54}]}, "bone75": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -9.02, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.4333, "angle": 2.84, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.9, "angle": 9.75, "curve": 0.25, "c3": 0.75}, {"time": 1.1667}]}, "bone76": {"rotate": [{"angle": 2.77, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -9.02, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.4333, "angle": -4.47, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1, "angle": 9.75, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.1667, "angle": 2.77}]}, "bone77": {"rotate": [{"angle": 6.98, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -9.02, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 9.75, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.1667, "angle": 6.98}]}, "bone78": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -8.11, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.4333, "angle": 2.1, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.9, "angle": 8.04, "curve": 0.25, "c3": 0.75}, {"time": 1.1667}]}, "bone79": {"rotate": [{"angle": 2.28, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -8.11, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.4333, "angle": -4.2, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1, "angle": 8.04, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.1667, "angle": 2.28}]}, "bone80": {"rotate": [{"angle": 5.76, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -8.11, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 8.04, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.1667, "angle": 5.76}]}, "bone81": {"rotate": [{"angle": -89.58, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -68.8, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3, "angle": -52.48, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667, "angle": -28.03, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -88.56}], "translate": [{"x": 205.28, "y": 68.39, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 190.02, "y": 83.65, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3, "x": 150.84, "y": 80.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667, "x": 122.33, "y": 50.9, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 135.86, "y": 61.6, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": 743.7, "y": 62.61}], "scale": [{"x": 0.926, "y": 0.926}]}, "ef1": {"rotate": [{"time": 0.7667, "angle": 0.26}], "translate": [{"time": 0.7667, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.8667, "x": 555.4, "y": 2.48}]}}, "events": [{"time": 0.8, "name": "atk"}]}, "boss_idle": {"bones": {"bone2": {"rotate": [{"angle": -178.51}], "translate": [{"x": 305.13, "y": 168.97}]}, "bone3": {"rotate": [{"angle": 1.99}]}, "bone4": {"rotate": [{"angle": 12.11}]}, "bone5": {"rotate": [{"angle": 28.69}]}, "bone6": {"rotate": [{"angle": 26.34}]}, "bone7": {"rotate": [{"angle": 25.92}]}, "bone8": {"rotate": [{"angle": 22.65}]}, "bone9": {"rotate": [{"angle": 36.5}]}, "bone10": {"rotate": [{"angle": 94.49}]}, "bone11": {"rotate": [{"angle": 21.76}]}, "bone12": {"rotate": [{"angle": 6.75}]}, "bone13": {"rotate": [{"angle": 3.65}]}, "bone14": {"rotate": [{"angle": 2.37}]}, "bone15": {"rotate": [{"angle": 1.55}]}, "bone16": {"rotate": [{"angle": 1.62}]}, "bone17": {"rotate": [{"angle": 4.54}]}, "bone18": {"rotate": [{"angle": 9.81}]}, "bone19": {"rotate": [{"angle": 27.12}]}, "bone20": {"rotate": [{"angle": 53.28}]}, "bone21": {"rotate": [{"angle": 55.68}]}, "bone22": {"rotate": [{"angle": 28.39}]}, "bone23": {"rotate": [{"angle": 10.58}]}, "bone24": {"rotate": [{"angle": 6.67}]}, "bone25": {"rotate": [{"angle": 10.41}]}, "bone26": {"rotate": [{"angle": -0.85}]}, "bone27": {"rotate": [{"angle": -3.77}]}, "bone28": {"rotate": [{"angle": -5.55}]}, "bone29": {"rotate": [{"angle": -7.47}]}, "bone30": {"rotate": [{"angle": -9.79}]}, "bone31": {"rotate": [{"angle": -11.75}]}, "bone32": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 0.01, "y": 0.14, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone33": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": -0.02, "y": 3.29, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone34": {"translate": [{"x": 0.6, "y": 1.11, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 2.11, "y": 3.92, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.6, "y": 1.11}]}, "bone35": {"translate": [{"x": 2.56, "y": -5.25, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 3.57, "y": -7.33, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 2.56, "y": -5.25}]}, "bone36": {"translate": [{"x": 18.47, "y": 7.41, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 18.47, "y": 7.41}]}, "bone37": {"translate": [{"x": -0.45, "y": -4.32, "curve": 0.325, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 0.3333, "x": -0.8, "y": -7.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -0.45, "y": -4.32}]}, "bone38": {"translate": [{"x": 5.43, "y": -1.9, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": 19.13, "y": -6.69, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 5.43, "y": -1.9}]}, "bone39": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": -0.12, "y": 1.27, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone40": {"translate": [{"x": 0.41, "y": -0.25, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.46, "y": -0.87, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.41, "y": -0.25}]}, "bone41": {"translate": [{"x": 0.52, "y": -0.22, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.73, "y": -0.31, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 0.52, "y": -0.22}]}, "bone42": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 1.92, "y": 0.69, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone43": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 1.02, "y": -1.78, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone44": {"rotate": [{"angle": -1.85, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -6.52, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -1.85}]}, "bone45": {"rotate": [{"angle": -4.67, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -6.52, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -4.67}]}, "bone46": {"rotate": [{"angle": -6.52, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -6.52}]}, "bone47": {"rotate": [{"angle": -4.67, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -6.52, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -4.67}]}, "bone48": {"rotate": [{"angle": -2, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -7.06, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -2}]}, "bone49": {"rotate": [{"angle": -5.06, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -7.06, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -5.06}]}, "bone50": {"rotate": [{"angle": -7.06, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -7.06}]}, "bone51": {"rotate": [{"angle": -5.89, "curve": 0.309, "c2": 0.26, "c3": 0.671, "c4": 0.68}, {"time": 0.4333, "angle": -2, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": -7.06, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 2, "angle": -5.89}]}, "bone52": {"rotate": [{"angle": -6.76, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "angle": -7.06, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.4333, "angle": -5.06, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.1, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2, "angle": -6.76}]}, "bone53": {"rotate": [{"angle": -4.15, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.4333, "angle": -7.06, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2, "angle": -4.15}]}, "bone54": {"rotate": [{"angle": 2.93, "curve": 0.309, "c2": 0.26, "c3": 0.671, "c4": 0.68}, {"time": 0.4333, "angle": 1, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": 3.51, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 2, "angle": 2.93}]}, "bone55": {"rotate": [{"angle": 3.36, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "angle": 3.51, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.4333, "angle": 2.51, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.1, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2, "angle": 3.36}]}, "bone56": {"rotate": [{"angle": 2.06, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.4333, "angle": 3.51, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2, "angle": 2.06}]}, "bone57": {"rotate": [{"angle": 2.01, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.08, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 2.01}]}, "bone58": {"rotate": [{"angle": 5.07, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 7.08, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 5.07}]}, "bone59": {"rotate": [{"angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 7.08}]}, "bone60": {"rotate": [{"angle": 5.07, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 5.07}]}, "bone61": {"rotate": [{"angle": 1, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 3.51, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 1}]}, "bone62": {"rotate": [{"angle": 2.51, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 3.51, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 2.51}]}, "bone63": {"rotate": [{"angle": 3.51, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 3.51}]}, "bone64": {"rotate": [{"angle": -0.47}]}, "bone65": {"rotate": [{"angle": 0.14}]}, "bone66": {"rotate": [{"angle": 0.26}]}, "bone67": {"rotate": [{"angle": 0.06}]}, "bone69": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 1.29, "y": -3.19, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "toufa2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -9.48, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "toufa3": {"rotate": [{"angle": -2.69, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -9.48, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -2.69}]}, "toufa4": {"rotate": [{"angle": -6.79, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -9.48, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -6.79}]}, "toufa5": {"rotate": [{"angle": 0.25, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 5.97, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2, "angle": 0.25}]}, "toufa6": {"rotate": [{"angle": 2.46, "curve": 0.338, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 0.1, "angle": 1.7, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": 5.97, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2, "angle": 2.46}]}, "toufa7": {"rotate": [{"angle": 4.99, "curve": 0.322, "c2": 0.29, "c3": 0.657, "c4": 0.63}, {"time": 0.1, "angle": 4.28, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": 5.97, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 2, "angle": 4.99}]}, "toufa8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 5.97, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "toufa9": {"rotate": [{"angle": 1.7, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 5.97, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 1.7}]}, "toufa10": {"rotate": [{"angle": 4.28, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 5.97, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 4.28}]}, "toufa11": {"rotate": [{"angle": -1.92, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 0.7333, "angle": -9.48, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 2, "angle": -1.92}]}, "toufa12": {"rotate": [{"angle": -0.19, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -9.48, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.7333, "angle": -2.69, "curve": 0.368, "c2": 0.48, "c3": 0.713, "c4": 0.87}, {"time": 2, "angle": -0.19}]}, "toufa13": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -9.48, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "toufa14": {"rotate": [{"angle": -2.69, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -9.48, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -2.69}]}, "toufa15": {"rotate": [{"angle": 1.21, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 0.7333, "angle": 5.97, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 2, "angle": 1.21}]}, "toufa16": {"rotate": [{"angle": 0.12, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 5.97, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.7333, "angle": 1.7, "curve": 0.368, "c2": 0.48, "c3": 0.713, "c4": 0.87}, {"time": 2, "angle": 0.12}]}, "toufa17": {"rotate": [{"angle": -1.23, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -9.48, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2, "angle": -1.23}]}, "toufa18": {"rotate": [{"angle": -5.14, "curve": 0.336, "c2": 0.34, "c3": 0.676, "c4": 0.69}, {"time": 0.2, "angle": -2.69, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -9.48, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 2, "angle": -5.14}]}, "toufa19": {"rotate": [{"angle": 1.21, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 0.7333, "angle": 5.97, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 2, "angle": 1.21}]}, "toufa20": {"rotate": [{"angle": 0.12, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 5.97, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.7333, "angle": 1.7, "curve": 0.368, "c2": 0.48, "c3": 0.713, "c4": 0.87}, {"time": 2, "angle": 0.12}]}, "toufa21": {"rotate": [{"angle": 2.21, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 5.97, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.7333, "angle": 4.28, "curve": 0.324, "c2": 0.3, "c3": 0.668, "c4": 0.67}, {"time": 2, "angle": 2.21}]}, "bone71": {"rotate": [{"angle": -1.15, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": -6.89, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 2, "angle": -1.15}]}, "bone72": {"rotate": [{"angle": -4.04, "curve": 0.334, "c2": 0.34, "c3": 0.676, "c4": 0.7}, {"time": 0.2333, "angle": -1.96, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": -6.89, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 2, "angle": -4.04}]}, "bone73": {"rotate": [{"angle": -6.62, "curve": 0.296, "c2": 0.18, "c3": 0.638, "c4": 0.55}, {"time": 0.2333, "angle": -4.94, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": -6.89, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2, "angle": -6.62}]}, "bone74": {"rotate": [{"angle": -5.75, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.2333, "angle": -6.89, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 2, "angle": -5.75}]}, "bone75": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -6.89, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone76": {"rotate": [{"angle": -1.96, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -6.89, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -1.96}]}, "bone77": {"rotate": [{"angle": -4.94, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -6.89, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -4.94}]}, "bone78": {"rotate": [{"angle": 0.52, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.8667, "angle": 7.42, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 2, "angle": 0.52}]}, "bone79": {"rotate": [{"angle": 0.95, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 7.42, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.8667, "angle": 2.11, "curve": 0.347, "c2": 0.38, "c3": 0.683, "c4": 0.73}, {"time": 2, "angle": 0.95}]}, "bone80": {"rotate": [{"angle": 4.05, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": 7.42, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.8667, "angle": 5.32, "curve": 0.326, "c2": 0.31, "c3": 0.662, "c4": 0.65}, {"time": 2, "angle": 4.05}]}}}, "die": {"slots": {"mv_she_biyan": {"attachment": [{"time": 0.0667, "name": "mv_she_019"}]}, "mv_she_zhenyan": {"attachment": [{"time": 0.0667, "name": null}]}}, "bones": {"bone2": {"rotate": [{"angle": -178.51, "curve": "stepped"}, {"time": 0.0667, "angle": -178.51, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -105.33}], "translate": [{"x": 305.13, "y": 168.97, "curve": "stepped"}, {"time": 0.0667, "x": 305.13, "y": 168.97, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 291.29, "y": 135.44}]}, "bone3": {"rotate": [{"angle": 1.99, "curve": "stepped"}, {"time": 0.0667, "angle": 1.99, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -25.79}]}, "bone4": {"rotate": [{"angle": 12.11, "curve": "stepped"}, {"time": 0.0667, "angle": 12.11, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 13}]}, "bone5": {"rotate": [{"angle": 28.69, "curve": "stepped"}, {"time": 0.0667, "angle": 28.69, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 27.35}]}, "bone6": {"rotate": [{"angle": 26.34, "curve": "stepped"}, {"time": 0.0667, "angle": 26.34, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 7.06}]}, "bone7": {"rotate": [{"angle": 25.92, "curve": "stepped"}, {"time": 0.0667, "angle": 25.92, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -12.09}]}, "bone8": {"rotate": [{"angle": 22.65, "curve": "stepped"}, {"time": 0.0667, "angle": 22.65, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 147.33}]}, "bone9": {"rotate": [{"angle": 36.5, "curve": "stepped"}, {"time": 0.0667, "angle": 36.5, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 20.89}]}, "bone10": {"rotate": [{"angle": 94.49, "curve": "stepped"}, {"time": 0.0667, "angle": 94.49, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 7.27}]}, "bone11": {"rotate": [{"angle": 21.76, "curve": "stepped"}, {"time": 0.0667, "angle": 21.76, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 3.77}]}, "bone12": {"rotate": [{"angle": 6.75, "curve": "stepped"}, {"time": 0.0667, "angle": 6.75, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 2.42}]}, "bone13": {"rotate": [{"angle": 3.65, "curve": "stepped"}, {"time": 0.0667, "angle": 3.65, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 1.71}]}, "bone14": {"rotate": [{"angle": 2.37, "curve": "stepped"}, {"time": 0.0667, "angle": 2.37, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 1.03}]}, "bone15": {"rotate": [{"angle": 1.55, "curve": "stepped"}, {"time": 0.0667, "angle": 1.55, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 2.4}]}, "bone16": {"rotate": [{"angle": 1.62, "curve": "stepped"}, {"time": 0.0667, "angle": 1.62, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 5.33}]}, "bone17": {"rotate": [{"angle": 4.54, "curve": "stepped"}, {"time": 0.0667, "angle": 4.54, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 11.55}]}, "bone18": {"rotate": [{"angle": 9.81, "curve": "stepped"}, {"time": 0.0667, "angle": 9.81, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 32.18}]}, "bone19": {"rotate": [{"angle": 27.12, "curve": "stepped"}, {"time": 0.0667, "angle": 27.12, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 49.48}]}, "bone20": {"rotate": [{"angle": 53.28, "curve": "stepped"}, {"time": 0.0667, "angle": 53.28, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 49.96}]}, "bone21": {"rotate": [{"angle": 55.68, "curve": "stepped"}, {"time": 0.0667, "angle": 55.68, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 22.87}]}, "bone22": {"rotate": [{"angle": 28.39, "curve": "stepped"}, {"time": 0.0667, "angle": 28.39, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 9.1}]}, "bone23": {"rotate": [{"angle": 10.58, "curve": "stepped"}, {"time": 0.0667, "angle": 10.58, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 5.4}]}, "bone24": {"rotate": [{"angle": 6.67, "curve": "stepped"}, {"time": 0.0667, "angle": 6.67, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 10.14}]}, "bone25": {"rotate": [{"angle": 10.41, "curve": "stepped"}, {"time": 0.0667, "angle": 10.41, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 0.11}]}, "bone26": {"rotate": [{"angle": -0.85, "curve": "stepped"}, {"time": 0.0667, "angle": -0.85, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -3}]}, "bone27": {"rotate": [{"angle": -3.77, "curve": "stepped"}, {"time": 0.0667, "angle": -3.77, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -4.43}]}, "bone28": {"rotate": [{"angle": -5.55, "curve": "stepped"}, {"time": 0.0667, "angle": -5.55, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -5.97}]}, "bone29": {"rotate": [{"angle": -7.47, "curve": "stepped"}, {"time": 0.0667, "angle": -7.47, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -7.79}]}, "bone30": {"rotate": [{"angle": -9.79, "curve": "stepped"}, {"time": 0.0667, "angle": -9.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -9.92}]}, "bone31": {"rotate": [{"angle": -11.75, "curve": "stepped"}, {"time": 0.0667, "angle": -11.75, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -9.84}]}, "bone32": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -22.07, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 67.73, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 73.36}], "translate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 4.72, "y": 30.75, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -7.04, "y": -42.66, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3, "x": -6.47, "y": -35.3, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667, "x": -7.04, "y": -42.66}]}, "bone39": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -6.66, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 9.71}]}, "bone40": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -6.66, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 9.71, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -7.81}]}, "bone41": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -6.66, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 9.71, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 12.37}]}, "bone44": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 20.54, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3, "angle": 33.43, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667, "angle": 20.54}]}, "bone45": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 88.65, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3, "angle": 101.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667, "angle": 88.65}]}, "bone48": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 9.24}]}, "bone49": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 95.83}]}, "bone51": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 52.78}]}, "bone52": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -72.61}]}, "bone54": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -90.06, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3, "angle": -80.24, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667, "angle": -90.06}]}, "bone55": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -21.46, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3, "angle": -11.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667, "angle": -21.46}]}, "bone57": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -15.01, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3, "angle": -5.18, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667, "angle": -15.01}]}, "bone58": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -10.85, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3, "angle": -1.02, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667, "angle": -10.85}]}, "bone61": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -41.36, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3, "angle": -31.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667, "angle": -41.36}]}, "bone62": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -52.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3, "angle": -42.79, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667, "angle": -52.62}]}, "bone68": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -33.46}]}, "bone64": {"rotate": [{"angle": -0.47}]}, "bone65": {"rotate": [{"angle": 0.14}]}, "bone66": {"rotate": [{"angle": 0.26}]}, "bone67": {"rotate": [{"angle": 0.06}]}, "bone71": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 26.31}]}, "bone72": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 3.67}]}, "bone73": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 17.6}]}, "bone74": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 40.09}]}, "bone75": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 34.96}]}, "bone76": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -1.3}]}, "bone77": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -10.84}]}, "bone78": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -12.1}]}, "bone79": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 4.86}]}, "bone80": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -28.11}]}}, "drawOrder": [{"time": 0.2, "offsets": [{"slot": "mv_she_09", "offset": -6}, {"slot": "mv_she_014", "offset": -15}]}, {"time": 0.2333, "offsets": [{"slot": "mv_she_09", "offset": -9}, {"slot": "mv_she_014", "offset": -15}]}]}, "hurt": {"slots": {"mv_she_biyan": {"attachment": [{"time": 0.0667, "name": "mv_she_019"}]}, "mv_she_zhenyan": {"attachment": [{"time": 0.0667, "name": null}]}}, "bones": {"bone": {"translate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -16.82, "y": -0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone2": {"rotate": [{"angle": -178.51}], "translate": [{"x": 305.13, "y": 168.97}]}, "bone3": {"rotate": [{"angle": 1.99}]}, "bone4": {"rotate": [{"angle": 12.11}]}, "bone5": {"rotate": [{"angle": 28.69}]}, "bone6": {"rotate": [{"angle": 26.34}]}, "bone7": {"rotate": [{"angle": 25.92}]}, "bone8": {"rotate": [{"angle": 22.65}]}, "bone9": {"rotate": [{"angle": 36.5}]}, "bone10": {"rotate": [{"angle": 94.49}]}, "bone11": {"rotate": [{"angle": 21.76}]}, "bone12": {"rotate": [{"angle": 6.75}]}, "bone13": {"rotate": [{"angle": 3.65}]}, "bone14": {"rotate": [{"angle": 2.37}]}, "bone15": {"rotate": [{"angle": 1.55}]}, "bone16": {"rotate": [{"angle": 1.62}]}, "bone17": {"rotate": [{"angle": 4.54}]}, "bone18": {"rotate": [{"angle": 9.81}]}, "bone19": {"rotate": [{"angle": 27.12}]}, "bone20": {"rotate": [{"angle": 53.28}]}, "bone21": {"rotate": [{"angle": 55.68}]}, "bone22": {"rotate": [{"angle": 28.39}]}, "bone23": {"rotate": [{"angle": 10.58}]}, "bone24": {"rotate": [{"angle": 6.67}]}, "bone25": {"rotate": [{"angle": 10.41}]}, "bone26": {"rotate": [{"angle": -0.85}]}, "bone27": {"rotate": [{"angle": -3.77}]}, "bone28": {"rotate": [{"angle": -5.55}]}, "bone29": {"rotate": [{"angle": -7.47}]}, "bone30": {"rotate": [{"angle": -9.79}]}, "bone31": {"rotate": [{"angle": -11.75}]}, "bone32": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -4.29, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 3.48, "curve": 0.25, "c3": 0.75}, {"time": 0.3}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 3.97, "y": -1.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -12.24, "y": -0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone34": {"translate": [{"x": -2.33, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 4.56, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -8.2, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3, "x": -2.33}]}, "bone35": {"translate": [{"x": -5.87, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 4.56, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -8.2, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3, "x": -5.87}]}, "bone36": {"translate": [{"x": -8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 4.56, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -8.2}]}, "bone37": {"translate": [{"x": -4.58, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "x": -8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 4.56, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3, "x": -4.58}]}, "bone38": {"translate": [{"x": 0.94, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "x": -8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 4.56, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3, "x": 0.94}]}, "bone39": {"rotate": [{"curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1, "angle": -5.86, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.2, "angle": 7.06, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3}]}, "bone40": {"rotate": [{"angle": 2, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -5.86, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 7.06, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3, "angle": 2}]}, "bone41": {"rotate": [{"angle": 3.77, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -5.86, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.2667, "angle": 7.06, "curve": 0.344, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 0.3, "angle": 3.77}]}, "bone44": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -12.44, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 13.55, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone45": {"rotate": [{"angle": 3.85, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -12.44, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 13.55, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3, "angle": 3.85}]}, "bone48": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -12.44, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 13.55, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone49": {"rotate": [{"angle": 3.85, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -12.44, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 13.55, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3, "angle": 3.85}]}, "bone51": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -12.44, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 13.55, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone52": {"rotate": [{"angle": 3.85, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -12.44, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 13.55, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3, "angle": 3.85}]}, "bone54": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -4.5, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 12.52, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone55": {"rotate": [{"angle": 3.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -4.5, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 12.52, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3, "angle": 3.55}]}, "bone56": {"rotate": [{"angle": 8.97, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -4.5, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 12.52, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3, "angle": 8.97}]}, "bone57": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -4.5, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 12.52, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone58": {"rotate": [{"angle": 3.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -4.5, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 12.52, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3, "angle": 3.55}]}, "bone59": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -4.5, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 12.52, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone61": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -4.5, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 12.52, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone62": {"rotate": [{"angle": 3.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -4.5, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 12.52, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3, "angle": 3.55}]}, "bone63": {"rotate": [{"angle": 8.97, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -4.5, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 12.52, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3, "angle": 8.97}]}, "bone64": {"rotate": [{"angle": -0.47}]}, "bone65": {"rotate": [{"angle": 0.14}]}, "bone66": {"rotate": [{"angle": 0.26}]}, "bone67": {"rotate": [{"angle": 0.06}]}, "toufa2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -13.26, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 4.61, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "toufa3": {"rotate": [{"angle": 1.31, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -13.26, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 4.61, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3, "angle": 1.31}]}, "toufa4": {"rotate": [{"angle": 3.3, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -13.26, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 4.61, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3, "angle": 3.3}]}, "toufa5": {"rotate": [{"angle": 7.73, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -7.4, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 10.79, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3, "angle": 7.73}]}, "toufa6": {"rotate": [{"angle": 10.79, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.0667, "angle": 3.06, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -7.4, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 10.79}]}, "toufa7": {"rotate": [{"angle": 5.63, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "angle": 10.79, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.0667, "angle": 7.73, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -7.4, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3, "angle": 5.63}]}, "toufa8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -7.4, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 10.79, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "toufa9": {"rotate": [{"angle": 3.06, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -7.4, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 10.79, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3, "angle": 3.06}]}, "toufa10": {"rotate": [{"angle": 7.73, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -7.4, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 10.79, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3, "angle": 7.73}]}, "toufa11": {"rotate": [{"angle": 3.3, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -13.26, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 4.61, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3, "angle": 3.3}]}, "toufa12": {"rotate": [{"angle": 4.61, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.0667, "angle": 1.31, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -13.26, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 4.61}]}, "toufa13": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -13.26, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 4.61, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "toufa14": {"rotate": [{"angle": 1.31, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -13.26, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 4.61, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3, "angle": 1.31}]}, "toufa15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -7.4, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 10.79, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "toufa16": {"rotate": [{"angle": 3.06, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -7.4, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 10.79, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3, "angle": 3.06}]}, "toufa17": {"rotate": [{"angle": 7.73, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -7.4, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 10.79, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3, "angle": 7.73}]}, "toufa18": {"rotate": [{"angle": 10.79, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.0667, "angle": 3.06, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -7.4, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 10.79}]}, "toufa19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -7.4, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 10.79, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "toufa20": {"rotate": [{"angle": 3.06, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -7.4, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 10.79, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3, "angle": 3.06}]}, "toufa21": {"rotate": [{"angle": 7.73, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -7.4, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 10.79, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3, "angle": 7.73}]}, "bone71": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 3.29, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone72": {"rotate": [{"angle": 0.93, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 3.29, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3, "angle": 0.93}]}, "bone73": {"rotate": [{"angle": 2.35, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 3.29, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3, "angle": 2.35}]}, "bone74": {"rotate": [{"angle": 3.29, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 3.29}]}, "bone75": {"rotate": [{"angle": 0.93, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 3.29, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3, "angle": 0.93}]}, "bone76": {"rotate": [{"angle": 2.35, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.0333, "angle": 0.93, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 3.29, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3, "angle": 2.35}]}, "bone77": {"rotate": [{"angle": 3.29, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.0333, "angle": 2.35, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 3.29}]}, "bone78": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -6.05, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 10.85, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone79": {"rotate": [{"angle": 3.08, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -6.05, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 10.85, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3, "angle": 3.08}]}, "bone80": {"rotate": [{"angle": 7.77, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -6.05, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 10.85, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3, "angle": 7.77}]}}}}}