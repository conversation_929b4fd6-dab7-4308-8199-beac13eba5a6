import { UISoulDetail } from "../../game/ui/ui_soul/UISoulDetail";
import { UISoulFreePop } from "../../game/ui/ui_soul/UISoulFreePop";
import { UISoulGetPop } from "../../game/ui/ui_soul/UISoulGetPop";
import { UISoulList } from "../../game/ui/ui_soul/UISoulList";
import { UISoulRatePop } from "../../game/ui/ui_soul/UISoulRatePop";
import { UISoulSlotPop } from "../../game/ui/ui_soul/UISoulSlotPop";
import { UISoulTujianConfirm } from "../../game/ui/ui_soul/UISoulTujianConfirm";
import { Recording, RecordingMap } from "../../lib/ui/recordingMap";

export enum SoulRouteName {
  UISoulDetail = "UISoulDetail",
  UISoulFreePop = "UISoulFreePop",
  UISoulSlotPop = "UISoulSlotPop",
  UISoulList = "UISoulList",
  UISoulRatePop = "UISoulRatePop",
  UISoulGetPop = "UISoulGetPop",
  UISoulTujianConfirm = "UISoulTujianConfirm",
}

export class SoulRoute {
  rotueTables: Recording[] = [
    {
      node: UISoulDetail,
      uiName: SoulRouteName.UISoulDetail,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UISoulFreePop,
      uiName: SoulRouteName.UISoulFreePop,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UISoulSlotPop,
      uiName: SoulRouteName.UISoulSlotPop,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UISoulList,
      uiName: SoulRouteName.UISoulList,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UISoulRatePop,
      uiName: SoulRouteName.UISoulRatePop,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UISoulGetPop,
      uiName: SoulRouteName.UISoulGetPop,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UISoulTujianConfirm,
      uiName: SoulRouteName.UISoulTujianConfirm,
      keep: false,
      relevanceUIList: [],
    },
  ];

  public async init() {
    this.rotueTables.forEach((item) => {
      RecordingMap.instance.addRecording(item.uiName, item);
    });
  }
}
