import { _decorator, instantiate, Label, math, v3, Vec3, Node, tween, isValid } from "cc";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { JsonMgr } from "../../mgr/JsonMgr";
import { LangMgr } from "../../mgr/LangMgr";
import { FarmModule } from "../../../module/farm/FarmModule";
import { Sleep } from "../../GameDefine";
const { ccclass, property } = _decorator;

@ccclass("TopUnlockHint")
export class TopUnlockHint extends BaseCtrl {
  public playShowAni: boolean = true;

  initWorldPos: Vec3 = v3(0, 0, 0);

  init(args: { initWorldPos: Vec3 }) {
    this.initWorldPos = args.initWorldPos;
  }

  async start() {
    super.start();

    this.node.setWorldPosition(this.initWorldPos);

    this.getNode("lbl_line").getComponent(Label).string = LangMgr.txMsgCode(432);

    const layout = this.getNode("layout");
    const keys = Object.keys(JsonMgr.instance.jsonList.c_blessLandBee).map(Number);
    const totalColCnt: number = FarmModule.data.farmTrainMessage.totalColCnt || 0;

    layout.children.forEach((child) => (child.active = false));
    for (let idx = 0; idx < keys.length; idx++) {
      const cfg = JsonMgr.instance.jsonList.c_blessLandBee[keys[idx]];
      let item = layout.children[idx];
      if (!item) {
        item = instantiate(layout.children[0]);
        item.parent = layout;
      }

      item.active = true;

      const lbl = item.getComponent(Label);
      lbl.string = LangMgr.txMsgCode(999999, [cfg.id, totalColCnt, cfg.unlock], "9999第%s只猴子，累计采集s%/s%解锁");

      if (totalColCnt < cfg.unlock) {
        lbl.color = math.color("dfeaff");
      } else {
        lbl.color = math.color("74ff77");
      }

      item.setScale(0, 0, 1);
      tween(item)
        .to(0.2, { scale: v3(1, 1, 1) })
        .start();
      await Sleep(0.01);
      if (isValid(this.node) == false) {
        return;
      }
    }

    this.getNode("btn_close_transfer").on(Node.EventType.TOUCH_START, (event) => {
      event.preventSwallow = true;
      this.closeBack();
    });

    this.getNode("btn_close_transfer").on(Node.EventType.TOUCH_END, (event) => {
      event.preventSwallow = true;
    });
  }
}
