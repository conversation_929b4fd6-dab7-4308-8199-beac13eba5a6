import { _decorator, instantiate, Node } from "cc";
import { ListAdapter } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { FarmCollectTitleViewHolder } from "./FarmCollectTitleViewHolder";
import { FarmCollectItemViewHolder } from "./FarmCollectItemViewHolder";

export class FarmCollectAdapter extends ListAdapter {
  // 节点类型标识key
  typeKey = "__viewType";

  // 常规节点
  item: Node;

  // 标题节点
  title: Node;

  constructor(item: Node, title: Node) {
    super();
    this.item = item;
    this.title = title;
  }

  // 所有缓存节点
  datas: any[] = [];
  setDatas(data: any[]) {
    if (!data) {
      return;
    }
    this.datas = data;
    this.notifyDataSetChanged();
  }

  getCount(): number {
    return this.datas.length;
  }

  getViewType(position: number): number {
    return position > 0 ? 1 : 2;
  }

  onCreateView(viewType: number): Node {
    if (viewType == 1) {
      let item = instantiate(this.item);
      item.getComponent(FarmCollectItemViewHolder).init();
      item.active = true;
      item[this.typeKey] = 1;
      return item;
    } else {
      let item = instantiate(this.title);
      item.getComponent(FarmCollectTitleViewHolder).init();
      item.active = true;
      item[this.typeKey] = 2;
      return item;
    }
  }

  onBindData(node: Node, position: number): void {
    let itemData = this.datas[position];
    if (node[this.typeKey] == 1) {
      node.getComponent(FarmCollectItemViewHolder).updateData(position, itemData);
    } else {
      node.getComponent(FarmCollectTitleViewHolder).updateData(position, itemData);
    }
  }
}
