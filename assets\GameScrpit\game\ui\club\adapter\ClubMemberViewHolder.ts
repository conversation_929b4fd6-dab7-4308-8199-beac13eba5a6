import { _decorator, EventTouch, instantiate, Label, Node, RichText, UITransform } from "cc";
import { ListAdapter, ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { ClubMemberMessage } from "../../../net/protocol/Club";
import Formate from "../../../../lib/utils/Formate";
import FmUtils from "../../../../lib/utils/FmUtils";
import { MessageComponent } from "../../../../../platform/src/core/ui/components/MessageComponent";
import { TimeUtils } from "db://assets/GameScrpit/lib/utils/TimeUtils";
import { ClubModule } from "db://assets/GameScrpit/module/club/ClubModule";
import { CLUB_POSITION } from "db://assets/GameScrpit/module/club/ClubConstant";
import { PlayerModule } from "db://assets/GameScrpit/module/player/PlayerModule";
import { RouteManager } from "db://assets/platform/src/core/managers/RouteManager";
import { UIClubMembersTop } from "../UIClubMembersTop";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import { RouteShowArgs } from "db://assets/platform/src/core/managers/RouteTableManager";
import { NodeTool } from "db://assets/GameScrpit/lib/utils/NodeTool";
import { UIMgr } from "db://assets/GameScrpit/lib/ui/UIMgr";
import { PlayerRouteName } from "db://assets/GameScrpit/module/player/PlayerConstant";

const log = Logger.getLoger(LOG_LEVEL.DEBUG);
const { ccclass, property } = _decorator;
@ccclass("ClubMemberViewHolder")
export class ClubMemberViewHolder extends ViewHolder {
  @property(Label)
  private lblHuoyue: Label;
  @property(Label)
  private members_name: Label;
  @property(Label)
  private members_power: Label;
  @property(Label)
  private members_donate: Label;
  @property(Node)
  nodeAvatar: Node;

  @property(Node)
  lblPosition1: Node;
  @property(Node)
  lblPosition2: Node;

  private _data: ClubMemberMessage;
  public init() {}
  public updateData(position: number, data: ClubMemberMessage) {
    this._data = data;
    this.lblHuoyue.getComponent(MessageComponent).setMessageId(548, [data.activeVal + ""]);
    this.members_name.string = data.simpleMessage.nickname;
    this.members_power.string = `${Formate.format(data.simpleMessage.power)}`;
    this.members_donate.string = `${TimeUtils.getTimeAgo(data.simpleMessage.lastLoginTime)}`;
    FmUtils.setHeaderNode(this.nodeAvatar, data.simpleMessage);
    this.lblPosition1.active = false;
    this.lblPosition2.active = false;
    if (data.position == 1) {
      this.lblPosition1.active = true;
    } else if (data.position == 2) {
      this.lblPosition2.active = true;
    }
    this.getNode("btn_opration").active = false;
    this.getNode("btn_detail").active = false;
    if (ClubModule.data.position == CLUB_POSITION.盟主) {
      if (data.simpleMessage.userId == PlayerModule.data.playerId) {
        this.getNode("btn_detail").active = true;
      } else {
        this.getNode("btn_opration").active = true;
      }
    } else if (ClubModule.data.position == CLUB_POSITION.副盟主) {
      if (data.position == CLUB_POSITION.副盟主 || data.position == CLUB_POSITION.盟主) {
        this.getNode("btn_detail").active = true;
      } else {
        this.getNode("btn_opration").active = true;
      }
    } else {
      this.getNode("btn_detail").active = true;
    }
    let configLeader = PlayerModule.data.getConfigLeaderData(data.simpleMessage.level);
    this.getNode("rich_title_lv").getComponent(RichText).string = configLeader.jingjie2;
  }
  private onClickBtnOpration(e: EventTouch) {
    let args: RouteShowArgs = {
      payload: {
        target: e.target,
        parent: this.node.parent,
        data: this._data,
      },
    };
    RouteManager.uiRouteCtrl.showRoute(UIClubMembersTop, args);
  }
  private onClickBtnDetail(e: EventTouch) {
    if (this._data.simpleMessage.userId && this._data.simpleMessage.userId > 0) {
      UIMgr.instance.showDialog(PlayerRouteName.UIPlayerOtherMsg, this._data.simpleMessage);
    }
  }
}
export class ClubMembersAdapter extends ListAdapter {
  item: Node;
  constructor(item: Node) {
    super();
    this.item = item;
  }
  datas: ClubMemberMessage[] = [];
  setDatas(data: any[]) {
    if (!data) {
      return;
    }
    this.datas = data;
    this.notifyDataSetChanged();
  }
  getCount(): number {
    return this.datas.length;
  }
  onCreateView(viewType: number): Node {
    let item = instantiate(this.item);
    item.active = true;
    return item;
  }
  onBindData(node: Node, position: number): void {
    let itemData = this.datas[position];
    node.getComponent(ClubMemberViewHolder).updateData(position, itemData);
  }
}
