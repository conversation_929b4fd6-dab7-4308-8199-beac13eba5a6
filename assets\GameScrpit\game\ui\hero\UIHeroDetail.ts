import { _decorator, Color, EventTouch, Label, Node, UITransform } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { Widget } from "cc";
import { HeroModule } from "../../../module/hero/HeroModule";
import { PageState, UIHeroPage } from "./UIHeroPage";
import { IConfigHero } from "../../JsonDefine";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { HeroAudioName } from "../../../module/hero/HeroConfig";
import MsgMgr from "../../../lib/event/MsgMgr";
import { HeroEvent } from "../../../module/hero/HeroConstant";
import { PetModule } from "../../../module/pet/PetModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { FmButton } from "../../../../platform/src/core/ui/components/FmButton";
import { MessageComponent } from "../../../../platform/src/core/ui/components/MessageComponent";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Thu May 23 2024 14:52:35 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/hero/UIHeroDetail.ts
 *
 */

enum TabState {
  SKILL = 1, //技能
  SPIRIT_BEAST, //灵兽
  FATE, //缘分
  TALENT, //法宝
}

export enum HeroTabEvent {
  Switch_Hero = "Switch_Hero",
}
@ccclass("UIHeroDetail")
export class UIHeroDetail extends UINode {
  // protected _bind: boolean = false;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_HERO}?prefab/ui/UIHeroDetail`;
  }

  heros: IConfigHero[];
  position: number;
  currentState: TabState = TabState.SKILL;

  public init(args: any): void {
    super.init(args);
    let param = args;
    this.position = param[1];
    this.heros = param[0];
    if (param[2] == 2) {
      this.currentState = TabState.SPIRIT_BEAST;
    }
    HeroModule.viewModel.currentPosition = this.position;
    HeroModule.viewModel.currentHeros = this.heros;
  }
  protected onEvtShow(): void {
    this.node.getComponent(Widget).updateAlignment();
    let visibleHeight = this.node.getComponent(UITransform).height;
    let dialogHeight = this["background"].getComponent(UITransform).height;
    if (dialogHeight > visibleHeight - 100) {
      this.getNode("background").setScale(0.9, 0.9);
    }
    this.refreshUI(this.currentState);
    MsgMgr.on(HeroEvent.HERO_VIEWMODEL, this.onHeroChange, this);
    this.onHeroChange();
  }
  private onHeroChange() {
    //
    let petId = this.heros[this.position].petId;
    let pet = PetModule.data.getPet(petId);
    if (pet) {
      this.getNode("bg_owned").active = true;
      this.getNode("bg_unowned").active = false;
    } else {
      this.getNode("bg_owned").active = false;
      this.getNode("bg_unowned").active = true;
    }
  }
  protected onEvtHide(): void {
    MsgMgr.off(HeroEvent.HERO_VIEWMODEL, this.onHeroChange, this);
  }
  private updateChildWidget(node: Node) {
    if (!node.getComponent(Widget)) {
      return;
    }
    if (node.name === "UIHeroPetSkill") {
      return;
    }
    node.getComponent(Widget)?.updateAlignment();
    node.children.forEach((child) => {
      this.updateChildWidget(child);
    });
  }

  private on_click_btn_left(e: EventTouch) {
    AudioMgr.instance.playEffect(HeroAudioName.Effect.点击前后按钮切换战将);
    this.position = (this.position == 0 ? this.heros.length : this.position) - 1;
    HeroModule.viewModel.currentPosition = this.position;
    this.refreshUI(this.currentState);
  }
  private on_click_btn_right(e: EventTouch) {
    AudioMgr.instance.playEffect(HeroAudioName.Effect.点击前后按钮切换战将);
    this.position = (this.position + 1) % this.heros.length;
    HeroModule.viewModel.currentPosition = this.position;
    this.refreshUI(this.currentState);
  }

  private on_click_btn_back(e: EventTouch) {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  private on_click_tab1() {
    AudioMgr.instance.playEffect(HeroAudioName.Effect.点击页签切换);
    this.refreshUI(TabState.SKILL);
  }
  private on_click_tab2() {
    AudioMgr.instance.playEffect(HeroAudioName.Effect.点击页签切换);
    this.refreshUI(TabState.SPIRIT_BEAST);
  }
  private on_click_tab3() {
    AudioMgr.instance.playEffect(HeroAudioName.Effect.点击页签切换);
    this.refreshUI(TabState.FATE);
  }
  private on_click_tab4() {
    AudioMgr.instance.playEffect(HeroAudioName.Effect.点击页签切换);
    this.refreshUI(TabState.TALENT);
  }

  private refreshUI(state: TabState) {
    this.currentState = state;
    this.getNode("UIHeroPage").active = false;
    this.getNode("UIHeroPet").active = false;

    this.getNode("tab1").getComponent(FmButton).selected = false;
    this.getNode("tab2").getComponent(FmButton).selected = false;
    this.getNode("tab3").getComponent(FmButton).selected = false;
    this.getNode("tab4").getComponent(FmButton).selected = false;

    switch (state) {
      case TabState.SKILL:
        this.getNode("tab1").getComponent(FmButton).selected = true;
        this.getNode("UIHeroPage").active = true;
        this.getNode("UIHeroPage").getComponent(UIHeroPage).setTab(PageState.HERO);
        this.getNode("lbl_title").getComponent(MessageComponent).setMessageId(581);
        break;
      case TabState.SPIRIT_BEAST:
        this.getNode("tab2").getComponent(FmButton).selected = true;
        this.getNode("UIHeroPet").active = true;
        this.getNode("lbl_title").getComponent(MessageComponent).setMessageId(566);
        break;
      case TabState.FATE:
        this.getNode("tab3").getComponent(FmButton).selected = true;
        this.getNode("UIHeroPage").active = true;
        this.getNode("UIHeroPage").getComponent(UIHeroPage).setTab(PageState.FATE);
        this.getNode("lbl_title").getComponent(MessageComponent).setMessageId(567);
        break;
      case TabState.TALENT:
        this.getNode("tab4").getComponent(FmButton).selected = true;
        this.getNode("UIHeroPage").active = true;
        this.getNode("UIHeroPage").getComponent(UIHeroPage).setTab(PageState.HALO);
        this.getNode("lbl_title").getComponent(MessageComponent).setMessageId(568);
        break;
    }
  }
  public onClose(): void {
    super.onClose();
    // this.unRegisterMsg();
    log.log("HeroDetail====onClose");
  }
}
