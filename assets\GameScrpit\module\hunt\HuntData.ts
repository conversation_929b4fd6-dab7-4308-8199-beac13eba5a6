import { HuntBossRankMessage, HuntTrainMessage } from "../../game/net/protocol/Hunt";

export class HuntData {


  private _message: HuntTrainMessage = {
    chance: 0,
    progress: 0,
    petRemainHp: 0,
    power: 0,
    remainHp: 0,
    totalHp: 0,
    bossRemainHp: 0,
    bossHp: 0,
    bossPower: 0,
    buddyList: [],
    killMessage: undefined,
    bossChance: 0,
    bossStartStamp: 0,
    bossEndStamp: 0,
  };

  public get message(): HuntTrainMessage {
    return this._message;
  }

  public set message(value: HuntTrainMessage) {
    this._message = value;
  }

  public get power() {
    return this._message.power;
  }

  public get bossPower() {
    return this._message.bossPower;
  }

  public get bossStartStamp() {
    return this._message.bossStartStamp;
  }

  public set bossStartStamp(time) {
    this._message.bossStartStamp = time;
  }

  public get bossEndStamp() {
    return this._message.bossEndStamp;
  }

  public set bossEndStamp(time) {
    this._message.bossEndStamp = time;
  }

  public get chance() {
    return this._message.chance;
  }

  public get bossChance() {
    return this._message.bossChance;
  }

  /**灵兽关卡进度 */
  public get progress() {
    return this._message.progress;
  }

  /**玩家血槽大小 */
  public get totalHp() {
    return this._message.totalHp;
  }

  /**玩家剩余血量 */
  public get remainHp() {
    return this._message.remainHp;
  }

  /**灵兽剩余血量 */
  public get petRemainHp() {
    return this._message.petRemainHp;
  }

  /**Boss剩余血量 */
  public get bossRemainHp() {
    return this._message.bossRemainHp;
  }

  /**战友列表 */
  public get buddyList() {
    return this._message.buddyList;
  }

  public get killMessage() {
    return this._message.killMessage;
  }

  /**boss排行榜 */
  private _bossRank: HuntBossRankMessage;

  public get bossRank(): HuntBossRankMessage {
    return this._bossRank;
  }

  public set bossRank(info: HuntBossRankMessage) {
    this._bossRank = info;
  }
}
