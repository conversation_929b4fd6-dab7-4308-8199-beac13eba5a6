import { _decorator, isValid } from "cc";
import { FSMState } from "../fight/FSM/FSMState";
import { FSMBoard, STATE } from "../fight/section/StateSection";
import { AnimationSection } from "../fight/section/AnimationSection";
import { CallSkillDetail } from "../fight/FightDefine";
import FightManager from "../fight/manager/FightManager";
import SkillManager from "../fight/manager/SkillManager";
import { JsonMgr } from "../mgr/JsonMgr";
import HPSection from "../../lib/object/HpSection";
import { AudioMgr, AudioName } from "../../../platform/src/AudioHelper";
interface TimeHandle {
  time: number;
  handle: Function;
  isHandleOver: boolean;
  skillId: number;
}
const { ccclass, property } = _decorator;

@ccclass
export default class Atk2_1 extends FSMState {
  private timeHandle: Array<TimeHandle> = [];
  public async onEnter(board: FSMBoard) {
    //AudioMgr.instance.playEffect(AudioName.Effect.攻击1);
    let roundMovementInfo = board.roundMovementInfo;
    let detail: CallSkillDetail = {
      src: board.sub,
      target: roundMovementInfo.hurtRole,
      skillId: roundMovementInfo.skillId,
      resolveBack: () => {
        this.onSkillOver(board);
        roundMovementInfo.resolveBack(true);
      },
      actionType: roundMovementInfo.actionType,
      movementInfo: roundMovementInfo.movementInfo,
    };
    if (FightManager.instance.fightOver == true) {
      return;
    }
    FightManager.instance.getSection(SkillManager).callSkill(detail);
    let skill_db = JsonMgr.instance.jsonList.c_skillShow[roundMovementInfo.skillId];
    if (skill_db["isHp"] == true) {
      board.sub.getSection(HPSection).setRenderActive(false);
    }
    board.sub.getSection(AnimationSection).playAction(3, false);
    // let detail = new CallSkillDetail();
    // detail.skillId = board.skiiId;
    // detail.src = board.sub;
    // detail.target = board.nearest;
    // detail.atkInfo = board.atkInfo;
    // detail.fhurtNum = board.fhurtNum;
    // detail.skillOverCallback = () => {
    //   this.onSkillOver(board);
    // };

    // FightManager.instance.getSection(SkillManager).callSkill(detail);
  }

  private playSound(board: FSMBoard) {}

  private onSkillOver(board: FSMBoard) {
    board.sub.emitMsg("OnSwitchState", STATE.ATK2_2);
  }

  public update(board: FSMBoard, dt) {
    this.updateHandle(dt);
  }

  public onExit(board: any): void {}

  public updateHandle(dt) {}

  private getSkillObj(roleId: number) {}
}
