import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../game/mgr/ApiHandler";
import { HuntActionSubCmd } from "../../game/net/cmd/CmdData";
import { LongValueList } from "../../game/net/protocol/ExternalMessage";
import {
  HuntBossRankMessage,
  HuntBossResponse,
  HuntPetResponse,
  HuntRankMessage,
  HuntTrainMessage,
} from "../../game/net/protocol/Hunt";
import { HuntModule } from "./HuntModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class HuntApi {
  /**返回狩猎灵兽的基本信息 */
  public getTrain(success) {
    ApiHandler.instance.request(HuntTrainMessage, HuntActionSubCmd.getTrain, null, (data: HuntTrainMessage) => {
      log.log("请求狩猎基本信息===", data);
      HuntModule.data.message = data;
      success && success(data);
    });
  }

  /**获取狩猎灵兽积分的总排行榜 */
  public petRank(success) {
    ApiHandler.instance.request(HuntRankMessage, HuntActionSubCmd.petRank, null, (data: HuntRankMessage) => {
      log.log("获取狩猎灵兽积分的总排行榜===", data);
      success && success(data);
    });
  }

  /**开始狩猎灵兽 */
  public petHunt(success) {
    ApiHandler.instance.requestSync(HuntPetResponse, HuntActionSubCmd.petHunt, null, (data: HuntPetResponse) => {
      log.log("开始狩猎灵兽===", data);
      HuntModule.data.message = data.huntTrain;
      success && success(data);
    });
  }

  /**获取狩猎Boss积分的总排行榜 */
  public bossRank(success) {
    ApiHandler.instance.request(HuntBossRankMessage, HuntActionSubCmd.bossRank, null, (data: HuntBossRankMessage) => {
      log.log("获取狩猎Boss积分的总排行榜===", data);
      HuntModule.data.bossRank = data;
      success && success(data);
    });
  }

  /**开始狩猎boss */
  public bossHunt(success) {
    ApiHandler.instance.requestSync(HuntBossResponse, HuntActionSubCmd.bossHunt, null, (data: HuntBossResponse) => {
      log.log("开始狩猎boss===", data);
      HuntModule.data.message = data.huntTrainMessage;
      success && success(data);
    });
  }

  public testResetHunt() {
    ApiHandler.instance.request(HuntTrainMessage, HuntActionSubCmd.testResetHunt, null, (data: HuntTrainMessage) => {
      log.log("重置模块===", data);
    });
  }

  /**设置洪荒出现的时间为当前一个小时内 */
  public testSetStart() {
    ApiHandler.instance.request(LongValueList, HuntActionSubCmd.testSetStart, null, (data: LongValueList) => {
      log.log("设置洪荒出现的时间为当前一个小时内===", data);
    });
  }

  /**复活boss */
  public reviveBoss() {
    ApiHandler.instance.request(HuntTrainMessage, HuntActionSubCmd.testResetBoss, null, (data: HuntTrainMessage) => {
      log.log("复活boss===", data);
    });
  }
}
