// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v4.25.1
// source: Pupil.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "sim";

/**  */
export interface BasePupilInfoMessage {
  /** 玩家名称 */
  userName: string;
  /** 服务器名称 */
  serverName: string;
  /** 种族 */
  raceType: number;
  /** 弟子天赋ID */
  talentId: number;
  /** 弟子昵称Id */
  nameId: number;
  /** 挚友ID */
  friendId: number;
  /** 徒弟初始化的属性集合 */
  initAttrList: number[];
  /** 生命 攻击 防御 */
  basicAttrList: number[];
  /** 出师领悟的属性集合 */
  adultAttrList: number[];
}

/**  */
export interface PupilAddResponse {
  /** 槽位的索引 */
  index: number;
  /** 新招募到的徒弟信息 */
  pupilMessage: PupilMessage | undefined;
}

/** 弟子成年信息 */
export interface PupilAdultMessage {
  /** 徒弟信息 */
  pupilMessage:
    | PupilMessage
    | undefined;
  /** 奖励的书籍 和 数量 偶数索引处值为道具ID 奇数索引值为数量 */
  rewardList: number[];
}

/**  */
export interface PupilMarketRequest {
  pupilId: number;
  /** CLUB(1,"妖盟"), LOCAL(2,"本地"),目前制作了本地 , CROSS(3,"跨服"),; */
  channelId: number;
}

/**  */
export interface PupilMarketResponse {
  /** CLUB(1,"妖盟"), LOCAL(2,"本地"),目前制作了本地 , CROSS(3,"跨服"),; 目前只做本地服，此参数可以不填 */
  channelId: number;
  /** 对应存放联姻申请的截止时间 */
  marryApplyMap: { [key: number]: number };
}

export interface PupilMarketResponse_MarryApplyMapEntry {
  key: number;
  value: number;
}

/**  */
export interface PupilMarryPageRequest {
  channelId: number;
  /** 是否扣减剩余刷新次数 */
  isCost: boolean;
}

/**  */
export interface PupilMarryPageResponse {
  /** 获取的结拜页的信息列表 */
  reqMessageList: PupilMarryReqMessage[];
  /** 每日本服结拜刷新的剩余次数 */
  localMarryRefreshCnt: number;
}

/**  */
export interface PupilMarryReqMessage {
  id: number;
  /** 徒弟信息 */
  pupil:
    | PupilMessage
    | undefined;
  /** 申请联姻的结束时间 */
  endTime: number;
}

/**  */
export interface PupilMarryRequest {
  /** 己方的弟子 */
  pupilId: number;
  /** 分页得到的待联姻记录的ID */
  marryId: number;
  channelId: number;
}

/**  */
export interface PupilMarryResponse {
  /** 徒弟信息 */
  pupilMessage: PupilMessage | undefined;
  rewardList: number[];
}

/** 徒弟信息 */
export interface PupilMessage {
  /** 唯一标识 */
  id: number;
  /** 获取的时间 */
  addStamp: number;
  /** 结伴的时间 */
  marryStamp: number;
  /** 是否有展示过联姻弹窗 */
  isMarryShow: boolean;
  /** 自身的信息 */
  ownInfo:
    | BasePupilInfoMessage
    | undefined;
  /** 联姻的对象信息 */
  partnerInfo: BasePupilInfoMessage | undefined;
}

/**  */
export interface PupilRankResponse {
  rank: number;
  pupilList: PupilMessage[];
}

/** 槽位信息 */
export interface PupilSlotMessage {
  /** 目前在培养的徒弟，-1表示当前没有槽位没有正在培养的徒弟 */
  pupilId: number;
  /** 已经训练的次数 */
  train: number;
  /** 体力值 */
  vitality: number;
  /** 体力槽位大小 */
  vitalitySize: number;
  /** 最近一次更新体力值的时间 */
  lastUpdateTime: number;
}

/**  */
export interface PupilTrainMessage {
  /** 发起的妖盟联姻申请 key:徒弟ID val:剩余时长 */
  clubMarryApplyMap: { [key: number]: number };
  /** 发起的本地联姻申请 */
  localMarryApplyMap: { [key: number]: number };
  /** 发起的跨服联姻申请 */
  crossMarryApplyMap: { [key: number]: number };
  /** index就是槽位索引，从0开始 */
  trainSlotList: PupilSlotMessage[];
  /** index就是槽位索引，从0开始，如果当前位置没有委任，则为-1 */
  workSlotList: number[];
  /** 每日本服结拜刷新的剩余次数 */
  localMarryRefreshCnt: number;
}

export interface PupilTrainMessage_ClubMarryApplyMapEntry {
  key: number;
  value: number;
}

export interface PupilTrainMessage_LocalMarryApplyMapEntry {
  key: number;
  value: number;
}

export interface PupilTrainMessage_CrossMarryApplyMapEntry {
  key: number;
  value: number;
}

/** 培养后的返回信息 */
export interface PupilTrainResponse {
  /** 新出师弟子的列表 如果列表不为空，本次培养出师弟子，否则就是培养一次弟子 */
  newAdultList: PupilAdultMessage[];
  /** index就是槽位索引，从0开始，如果当前位置没有委任，则为-1 */
  workSlotList: number[];
  /** 掉落的经验 */
  heroExp: number;
  /** 掉落的道具列表，为空就没有道具掉落，偶数位为道具ID，奇数位为数量 */
  dropItemList: number[];
  /** 有更新的培养槽的信息 key：槽位索引 value:变化的槽位信息 */
  changeSlotMap: { [key: number]: PupilSlotMessage };
}

export interface PupilTrainResponse_ChangeSlotMapEntry {
  key: number;
  value: PupilSlotMessage | undefined;
}

/**  */
export interface PupilVitalityRequest {
  /** key : 槽位索引 val : 活力丹数量 */
  slotIndexAndCountMap: { [key: number]: number };
}

export interface PupilVitalityRequest_SlotIndexAndCountMapEntry {
  key: number;
  value: number;
}

/**  */
export interface PupilVitalityResponse {
  /** 体力值有变化的槽位信息 */
  slotUpdateMap: { [key: number]: PupilSlotMessage };
}

export interface PupilVitalityResponse_SlotUpdateMapEntry {
  key: number;
  value: PupilSlotMessage | undefined;
}

/**  */
export interface PupilWorkRequest {
  pupilId: number;
  slotIndex: number;
}

function createBaseBasePupilInfoMessage(): BasePupilInfoMessage {
  return {
    userName: "",
    serverName: "",
    raceType: 0,
    talentId: 0,
    nameId: 0,
    friendId: 0,
    initAttrList: [],
    basicAttrList: [],
    adultAttrList: [],
  };
}

export const BasePupilInfoMessage: MessageFns<BasePupilInfoMessage> = {
  encode(message: BasePupilInfoMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userName !== "") {
      writer.uint32(10).string(message.userName);
    }
    if (message.serverName !== "") {
      writer.uint32(18).string(message.serverName);
    }
    if (message.raceType !== 0) {
      writer.uint32(24).int32(message.raceType);
    }
    if (message.talentId !== 0) {
      writer.uint32(32).int64(message.talentId);
    }
    if (message.nameId !== 0) {
      writer.uint32(40).int64(message.nameId);
    }
    if (message.friendId !== 0) {
      writer.uint32(48).int64(message.friendId);
    }
    writer.uint32(58).fork();
    for (const v of message.initAttrList) {
      writer.double(v);
    }
    writer.join();
    writer.uint32(66).fork();
    for (const v of message.basicAttrList) {
      writer.double(v);
    }
    writer.join();
    writer.uint32(74).fork();
    for (const v of message.adultAttrList) {
      writer.double(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BasePupilInfoMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBasePupilInfoMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userName = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.serverName = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.raceType = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.talentId = longToNumber(reader.int64());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.nameId = longToNumber(reader.int64());
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.friendId = longToNumber(reader.int64());
          continue;
        }
        case 7: {
          if (tag === 57) {
            message.initAttrList.push(reader.double());

            continue;
          }

          if (tag === 58) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.initAttrList.push(reader.double());
            }

            continue;
          }

          break;
        }
        case 8: {
          if (tag === 65) {
            message.basicAttrList.push(reader.double());

            continue;
          }

          if (tag === 66) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.basicAttrList.push(reader.double());
            }

            continue;
          }

          break;
        }
        case 9: {
          if (tag === 73) {
            message.adultAttrList.push(reader.double());

            continue;
          }

          if (tag === 74) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.adultAttrList.push(reader.double());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<BasePupilInfoMessage>, I>>(base?: I): BasePupilInfoMessage {
    return BasePupilInfoMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BasePupilInfoMessage>, I>>(object: I): BasePupilInfoMessage {
    const message = createBaseBasePupilInfoMessage();
    message.userName = object.userName ?? "";
    message.serverName = object.serverName ?? "";
    message.raceType = object.raceType ?? 0;
    message.talentId = object.talentId ?? 0;
    message.nameId = object.nameId ?? 0;
    message.friendId = object.friendId ?? 0;
    message.initAttrList = object.initAttrList?.map((e) => e) || [];
    message.basicAttrList = object.basicAttrList?.map((e) => e) || [];
    message.adultAttrList = object.adultAttrList?.map((e) => e) || [];
    return message;
  },
};

function createBasePupilAddResponse(): PupilAddResponse {
  return { index: 0, pupilMessage: undefined };
}

export const PupilAddResponse: MessageFns<PupilAddResponse> = {
  encode(message: PupilAddResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.index !== 0) {
      writer.uint32(8).int32(message.index);
    }
    if (message.pupilMessage !== undefined) {
      PupilMessage.encode(message.pupilMessage, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PupilAddResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePupilAddResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.index = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.pupilMessage = PupilMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PupilAddResponse>, I>>(base?: I): PupilAddResponse {
    return PupilAddResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PupilAddResponse>, I>>(object: I): PupilAddResponse {
    const message = createBasePupilAddResponse();
    message.index = object.index ?? 0;
    message.pupilMessage = (object.pupilMessage !== undefined && object.pupilMessage !== null)
      ? PupilMessage.fromPartial(object.pupilMessage)
      : undefined;
    return message;
  },
};

function createBasePupilAdultMessage(): PupilAdultMessage {
  return { pupilMessage: undefined, rewardList: [] };
}

export const PupilAdultMessage: MessageFns<PupilAdultMessage> = {
  encode(message: PupilAdultMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pupilMessage !== undefined) {
      PupilMessage.encode(message.pupilMessage, writer.uint32(10).fork()).join();
    }
    writer.uint32(18).fork();
    for (const v of message.rewardList) {
      writer.double(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PupilAdultMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePupilAdultMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.pupilMessage = PupilMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag === 17) {
            message.rewardList.push(reader.double());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.rewardList.push(reader.double());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PupilAdultMessage>, I>>(base?: I): PupilAdultMessage {
    return PupilAdultMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PupilAdultMessage>, I>>(object: I): PupilAdultMessage {
    const message = createBasePupilAdultMessage();
    message.pupilMessage = (object.pupilMessage !== undefined && object.pupilMessage !== null)
      ? PupilMessage.fromPartial(object.pupilMessage)
      : undefined;
    message.rewardList = object.rewardList?.map((e) => e) || [];
    return message;
  },
};

function createBasePupilMarketRequest(): PupilMarketRequest {
  return { pupilId: 0, channelId: 0 };
}

export const PupilMarketRequest: MessageFns<PupilMarketRequest> = {
  encode(message: PupilMarketRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pupilId !== 0) {
      writer.uint32(8).int64(message.pupilId);
    }
    if (message.channelId !== 0) {
      writer.uint32(16).int32(message.channelId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PupilMarketRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePupilMarketRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.pupilId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.channelId = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PupilMarketRequest>, I>>(base?: I): PupilMarketRequest {
    return PupilMarketRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PupilMarketRequest>, I>>(object: I): PupilMarketRequest {
    const message = createBasePupilMarketRequest();
    message.pupilId = object.pupilId ?? 0;
    message.channelId = object.channelId ?? 0;
    return message;
  },
};

function createBasePupilMarketResponse(): PupilMarketResponse {
  return { channelId: 0, marryApplyMap: {} };
}

export const PupilMarketResponse: MessageFns<PupilMarketResponse> = {
  encode(message: PupilMarketResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.channelId !== 0) {
      writer.uint32(8).int32(message.channelId);
    }
    Object.entries(message.marryApplyMap).forEach(([key, value]) => {
      PupilMarketResponse_MarryApplyMapEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PupilMarketResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePupilMarketResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.channelId = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = PupilMarketResponse_MarryApplyMapEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.marryApplyMap[entry2.key] = entry2.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PupilMarketResponse>, I>>(base?: I): PupilMarketResponse {
    return PupilMarketResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PupilMarketResponse>, I>>(object: I): PupilMarketResponse {
    const message = createBasePupilMarketResponse();
    message.channelId = object.channelId ?? 0;
    message.marryApplyMap = Object.entries(object.marryApplyMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBasePupilMarketResponse_MarryApplyMapEntry(): PupilMarketResponse_MarryApplyMapEntry {
  return { key: 0, value: 0 };
}

export const PupilMarketResponse_MarryApplyMapEntry: MessageFns<PupilMarketResponse_MarryApplyMapEntry> = {
  encode(message: PupilMarketResponse_MarryApplyMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int64(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PupilMarketResponse_MarryApplyMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePupilMarketResponse_MarryApplyMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PupilMarketResponse_MarryApplyMapEntry>, I>>(
    base?: I,
  ): PupilMarketResponse_MarryApplyMapEntry {
    return PupilMarketResponse_MarryApplyMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PupilMarketResponse_MarryApplyMapEntry>, I>>(
    object: I,
  ): PupilMarketResponse_MarryApplyMapEntry {
    const message = createBasePupilMarketResponse_MarryApplyMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBasePupilMarryPageRequest(): PupilMarryPageRequest {
  return { channelId: 0, isCost: false };
}

export const PupilMarryPageRequest: MessageFns<PupilMarryPageRequest> = {
  encode(message: PupilMarryPageRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.channelId !== 0) {
      writer.uint32(8).int32(message.channelId);
    }
    if (message.isCost !== false) {
      writer.uint32(16).bool(message.isCost);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PupilMarryPageRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePupilMarryPageRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.channelId = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.isCost = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PupilMarryPageRequest>, I>>(base?: I): PupilMarryPageRequest {
    return PupilMarryPageRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PupilMarryPageRequest>, I>>(object: I): PupilMarryPageRequest {
    const message = createBasePupilMarryPageRequest();
    message.channelId = object.channelId ?? 0;
    message.isCost = object.isCost ?? false;
    return message;
  },
};

function createBasePupilMarryPageResponse(): PupilMarryPageResponse {
  return { reqMessageList: [], localMarryRefreshCnt: 0 };
}

export const PupilMarryPageResponse: MessageFns<PupilMarryPageResponse> = {
  encode(message: PupilMarryPageResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.reqMessageList) {
      PupilMarryReqMessage.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.localMarryRefreshCnt !== 0) {
      writer.uint32(16).int32(message.localMarryRefreshCnt);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PupilMarryPageResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePupilMarryPageResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.reqMessageList.push(PupilMarryReqMessage.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.localMarryRefreshCnt = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PupilMarryPageResponse>, I>>(base?: I): PupilMarryPageResponse {
    return PupilMarryPageResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PupilMarryPageResponse>, I>>(object: I): PupilMarryPageResponse {
    const message = createBasePupilMarryPageResponse();
    message.reqMessageList = object.reqMessageList?.map((e) => PupilMarryReqMessage.fromPartial(e)) || [];
    message.localMarryRefreshCnt = object.localMarryRefreshCnt ?? 0;
    return message;
  },
};

function createBasePupilMarryReqMessage(): PupilMarryReqMessage {
  return { id: 0, pupil: undefined, endTime: 0 };
}

export const PupilMarryReqMessage: MessageFns<PupilMarryReqMessage> = {
  encode(message: PupilMarryReqMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).int64(message.id);
    }
    if (message.pupil !== undefined) {
      PupilMessage.encode(message.pupil, writer.uint32(18).fork()).join();
    }
    if (message.endTime !== 0) {
      writer.uint32(24).int64(message.endTime);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PupilMarryReqMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePupilMarryReqMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.id = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.pupil = PupilMessage.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.endTime = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PupilMarryReqMessage>, I>>(base?: I): PupilMarryReqMessage {
    return PupilMarryReqMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PupilMarryReqMessage>, I>>(object: I): PupilMarryReqMessage {
    const message = createBasePupilMarryReqMessage();
    message.id = object.id ?? 0;
    message.pupil = (object.pupil !== undefined && object.pupil !== null)
      ? PupilMessage.fromPartial(object.pupil)
      : undefined;
    message.endTime = object.endTime ?? 0;
    return message;
  },
};

function createBasePupilMarryRequest(): PupilMarryRequest {
  return { pupilId: 0, marryId: 0, channelId: 0 };
}

export const PupilMarryRequest: MessageFns<PupilMarryRequest> = {
  encode(message: PupilMarryRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pupilId !== 0) {
      writer.uint32(8).int64(message.pupilId);
    }
    if (message.marryId !== 0) {
      writer.uint32(16).int64(message.marryId);
    }
    if (message.channelId !== 0) {
      writer.uint32(24).int32(message.channelId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PupilMarryRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePupilMarryRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.pupilId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.marryId = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.channelId = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PupilMarryRequest>, I>>(base?: I): PupilMarryRequest {
    return PupilMarryRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PupilMarryRequest>, I>>(object: I): PupilMarryRequest {
    const message = createBasePupilMarryRequest();
    message.pupilId = object.pupilId ?? 0;
    message.marryId = object.marryId ?? 0;
    message.channelId = object.channelId ?? 0;
    return message;
  },
};

function createBasePupilMarryResponse(): PupilMarryResponse {
  return { pupilMessage: undefined, rewardList: [] };
}

export const PupilMarryResponse: MessageFns<PupilMarryResponse> = {
  encode(message: PupilMarryResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pupilMessage !== undefined) {
      PupilMessage.encode(message.pupilMessage, writer.uint32(10).fork()).join();
    }
    writer.uint32(18).fork();
    for (const v of message.rewardList) {
      writer.double(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PupilMarryResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePupilMarryResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.pupilMessage = PupilMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag === 17) {
            message.rewardList.push(reader.double());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.rewardList.push(reader.double());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PupilMarryResponse>, I>>(base?: I): PupilMarryResponse {
    return PupilMarryResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PupilMarryResponse>, I>>(object: I): PupilMarryResponse {
    const message = createBasePupilMarryResponse();
    message.pupilMessage = (object.pupilMessage !== undefined && object.pupilMessage !== null)
      ? PupilMessage.fromPartial(object.pupilMessage)
      : undefined;
    message.rewardList = object.rewardList?.map((e) => e) || [];
    return message;
  },
};

function createBasePupilMessage(): PupilMessage {
  return { id: 0, addStamp: 0, marryStamp: 0, isMarryShow: false, ownInfo: undefined, partnerInfo: undefined };
}

export const PupilMessage: MessageFns<PupilMessage> = {
  encode(message: PupilMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).int64(message.id);
    }
    if (message.addStamp !== 0) {
      writer.uint32(16).int64(message.addStamp);
    }
    if (message.marryStamp !== 0) {
      writer.uint32(24).int64(message.marryStamp);
    }
    if (message.isMarryShow !== false) {
      writer.uint32(32).bool(message.isMarryShow);
    }
    if (message.ownInfo !== undefined) {
      BasePupilInfoMessage.encode(message.ownInfo, writer.uint32(42).fork()).join();
    }
    if (message.partnerInfo !== undefined) {
      BasePupilInfoMessage.encode(message.partnerInfo, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PupilMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePupilMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.id = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.addStamp = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.marryStamp = longToNumber(reader.int64());
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.isMarryShow = reader.bool();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.ownInfo = BasePupilInfoMessage.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.partnerInfo = BasePupilInfoMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PupilMessage>, I>>(base?: I): PupilMessage {
    return PupilMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PupilMessage>, I>>(object: I): PupilMessage {
    const message = createBasePupilMessage();
    message.id = object.id ?? 0;
    message.addStamp = object.addStamp ?? 0;
    message.marryStamp = object.marryStamp ?? 0;
    message.isMarryShow = object.isMarryShow ?? false;
    message.ownInfo = (object.ownInfo !== undefined && object.ownInfo !== null)
      ? BasePupilInfoMessage.fromPartial(object.ownInfo)
      : undefined;
    message.partnerInfo = (object.partnerInfo !== undefined && object.partnerInfo !== null)
      ? BasePupilInfoMessage.fromPartial(object.partnerInfo)
      : undefined;
    return message;
  },
};

function createBasePupilRankResponse(): PupilRankResponse {
  return { rank: 0, pupilList: [] };
}

export const PupilRankResponse: MessageFns<PupilRankResponse> = {
  encode(message: PupilRankResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.rank !== 0) {
      writer.uint32(8).int32(message.rank);
    }
    for (const v of message.pupilList) {
      PupilMessage.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PupilRankResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePupilRankResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.rank = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.pupilList.push(PupilMessage.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PupilRankResponse>, I>>(base?: I): PupilRankResponse {
    return PupilRankResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PupilRankResponse>, I>>(object: I): PupilRankResponse {
    const message = createBasePupilRankResponse();
    message.rank = object.rank ?? 0;
    message.pupilList = object.pupilList?.map((e) => PupilMessage.fromPartial(e)) || [];
    return message;
  },
};

function createBasePupilSlotMessage(): PupilSlotMessage {
  return { pupilId: 0, train: 0, vitality: 0, vitalitySize: 0, lastUpdateTime: 0 };
}

export const PupilSlotMessage: MessageFns<PupilSlotMessage> = {
  encode(message: PupilSlotMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pupilId !== 0) {
      writer.uint32(8).int64(message.pupilId);
    }
    if (message.train !== 0) {
      writer.uint32(16).int32(message.train);
    }
    if (message.vitality !== 0) {
      writer.uint32(24).int32(message.vitality);
    }
    if (message.vitalitySize !== 0) {
      writer.uint32(32).int32(message.vitalitySize);
    }
    if (message.lastUpdateTime !== 0) {
      writer.uint32(40).int64(message.lastUpdateTime);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PupilSlotMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePupilSlotMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.pupilId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.train = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.vitality = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.vitalitySize = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.lastUpdateTime = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PupilSlotMessage>, I>>(base?: I): PupilSlotMessage {
    return PupilSlotMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PupilSlotMessage>, I>>(object: I): PupilSlotMessage {
    const message = createBasePupilSlotMessage();
    message.pupilId = object.pupilId ?? 0;
    message.train = object.train ?? 0;
    message.vitality = object.vitality ?? 0;
    message.vitalitySize = object.vitalitySize ?? 0;
    message.lastUpdateTime = object.lastUpdateTime ?? 0;
    return message;
  },
};

function createBasePupilTrainMessage(): PupilTrainMessage {
  return {
    clubMarryApplyMap: {},
    localMarryApplyMap: {},
    crossMarryApplyMap: {},
    trainSlotList: [],
    workSlotList: [],
    localMarryRefreshCnt: 0,
  };
}

export const PupilTrainMessage: MessageFns<PupilTrainMessage> = {
  encode(message: PupilTrainMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.clubMarryApplyMap).forEach(([key, value]) => {
      PupilTrainMessage_ClubMarryApplyMapEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join();
    });
    Object.entries(message.localMarryApplyMap).forEach(([key, value]) => {
      PupilTrainMessage_LocalMarryApplyMapEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    Object.entries(message.crossMarryApplyMap).forEach(([key, value]) => {
      PupilTrainMessage_CrossMarryApplyMapEntry.encode({ key: key as any, value }, writer.uint32(26).fork()).join();
    });
    for (const v of message.trainSlotList) {
      PupilSlotMessage.encode(v!, writer.uint32(34).fork()).join();
    }
    writer.uint32(42).fork();
    for (const v of message.workSlotList) {
      writer.int64(v);
    }
    writer.join();
    if (message.localMarryRefreshCnt !== 0) {
      writer.uint32(48).int32(message.localMarryRefreshCnt);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PupilTrainMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePupilTrainMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = PupilTrainMessage_ClubMarryApplyMapEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.clubMarryApplyMap[entry1.key] = entry1.value;
          }
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = PupilTrainMessage_LocalMarryApplyMapEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.localMarryApplyMap[entry2.key] = entry2.value;
          }
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          const entry3 = PupilTrainMessage_CrossMarryApplyMapEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.crossMarryApplyMap[entry3.key] = entry3.value;
          }
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.trainSlotList.push(PupilSlotMessage.decode(reader, reader.uint32()));
          continue;
        }
        case 5: {
          if (tag === 40) {
            message.workSlotList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 42) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.workSlotList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.localMarryRefreshCnt = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PupilTrainMessage>, I>>(base?: I): PupilTrainMessage {
    return PupilTrainMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PupilTrainMessage>, I>>(object: I): PupilTrainMessage {
    const message = createBasePupilTrainMessage();
    message.clubMarryApplyMap = Object.entries(object.clubMarryApplyMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.localMarryApplyMap = Object.entries(object.localMarryApplyMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.crossMarryApplyMap = Object.entries(object.crossMarryApplyMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.trainSlotList = object.trainSlotList?.map((e) => PupilSlotMessage.fromPartial(e)) || [];
    message.workSlotList = object.workSlotList?.map((e) => e) || [];
    message.localMarryRefreshCnt = object.localMarryRefreshCnt ?? 0;
    return message;
  },
};

function createBasePupilTrainMessage_ClubMarryApplyMapEntry(): PupilTrainMessage_ClubMarryApplyMapEntry {
  return { key: 0, value: 0 };
}

export const PupilTrainMessage_ClubMarryApplyMapEntry: MessageFns<PupilTrainMessage_ClubMarryApplyMapEntry> = {
  encode(message: PupilTrainMessage_ClubMarryApplyMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int64(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PupilTrainMessage_ClubMarryApplyMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePupilTrainMessage_ClubMarryApplyMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PupilTrainMessage_ClubMarryApplyMapEntry>, I>>(
    base?: I,
  ): PupilTrainMessage_ClubMarryApplyMapEntry {
    return PupilTrainMessage_ClubMarryApplyMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PupilTrainMessage_ClubMarryApplyMapEntry>, I>>(
    object: I,
  ): PupilTrainMessage_ClubMarryApplyMapEntry {
    const message = createBasePupilTrainMessage_ClubMarryApplyMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBasePupilTrainMessage_LocalMarryApplyMapEntry(): PupilTrainMessage_LocalMarryApplyMapEntry {
  return { key: 0, value: 0 };
}

export const PupilTrainMessage_LocalMarryApplyMapEntry: MessageFns<PupilTrainMessage_LocalMarryApplyMapEntry> = {
  encode(message: PupilTrainMessage_LocalMarryApplyMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int64(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PupilTrainMessage_LocalMarryApplyMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePupilTrainMessage_LocalMarryApplyMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PupilTrainMessage_LocalMarryApplyMapEntry>, I>>(
    base?: I,
  ): PupilTrainMessage_LocalMarryApplyMapEntry {
    return PupilTrainMessage_LocalMarryApplyMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PupilTrainMessage_LocalMarryApplyMapEntry>, I>>(
    object: I,
  ): PupilTrainMessage_LocalMarryApplyMapEntry {
    const message = createBasePupilTrainMessage_LocalMarryApplyMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBasePupilTrainMessage_CrossMarryApplyMapEntry(): PupilTrainMessage_CrossMarryApplyMapEntry {
  return { key: 0, value: 0 };
}

export const PupilTrainMessage_CrossMarryApplyMapEntry: MessageFns<PupilTrainMessage_CrossMarryApplyMapEntry> = {
  encode(message: PupilTrainMessage_CrossMarryApplyMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int64(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PupilTrainMessage_CrossMarryApplyMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePupilTrainMessage_CrossMarryApplyMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PupilTrainMessage_CrossMarryApplyMapEntry>, I>>(
    base?: I,
  ): PupilTrainMessage_CrossMarryApplyMapEntry {
    return PupilTrainMessage_CrossMarryApplyMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PupilTrainMessage_CrossMarryApplyMapEntry>, I>>(
    object: I,
  ): PupilTrainMessage_CrossMarryApplyMapEntry {
    const message = createBasePupilTrainMessage_CrossMarryApplyMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBasePupilTrainResponse(): PupilTrainResponse {
  return { newAdultList: [], workSlotList: [], heroExp: 0, dropItemList: [], changeSlotMap: {} };
}

export const PupilTrainResponse: MessageFns<PupilTrainResponse> = {
  encode(message: PupilTrainResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.newAdultList) {
      PupilAdultMessage.encode(v!, writer.uint32(10).fork()).join();
    }
    writer.uint32(18).fork();
    for (const v of message.workSlotList) {
      writer.int64(v);
    }
    writer.join();
    if (message.heroExp !== 0) {
      writer.uint32(25).double(message.heroExp);
    }
    writer.uint32(34).fork();
    for (const v of message.dropItemList) {
      writer.double(v);
    }
    writer.join();
    Object.entries(message.changeSlotMap).forEach(([key, value]) => {
      PupilTrainResponse_ChangeSlotMapEntry.encode({ key: key as any, value }, writer.uint32(42).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PupilTrainResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePupilTrainResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.newAdultList.push(PupilAdultMessage.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag === 16) {
            message.workSlotList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.workSlotList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.heroExp = reader.double();
          continue;
        }
        case 4: {
          if (tag === 33) {
            message.dropItemList.push(reader.double());

            continue;
          }

          if (tag === 34) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.dropItemList.push(reader.double());
            }

            continue;
          }

          break;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          const entry5 = PupilTrainResponse_ChangeSlotMapEntry.decode(reader, reader.uint32());
          if (entry5.value !== undefined) {
            message.changeSlotMap[entry5.key] = entry5.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PupilTrainResponse>, I>>(base?: I): PupilTrainResponse {
    return PupilTrainResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PupilTrainResponse>, I>>(object: I): PupilTrainResponse {
    const message = createBasePupilTrainResponse();
    message.newAdultList = object.newAdultList?.map((e) => PupilAdultMessage.fromPartial(e)) || [];
    message.workSlotList = object.workSlotList?.map((e) => e) || [];
    message.heroExp = object.heroExp ?? 0;
    message.dropItemList = object.dropItemList?.map((e) => e) || [];
    message.changeSlotMap = Object.entries(object.changeSlotMap ?? {}).reduce<{ [key: number]: PupilSlotMessage }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = PupilSlotMessage.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBasePupilTrainResponse_ChangeSlotMapEntry(): PupilTrainResponse_ChangeSlotMapEntry {
  return { key: 0, value: undefined };
}

export const PupilTrainResponse_ChangeSlotMapEntry: MessageFns<PupilTrainResponse_ChangeSlotMapEntry> = {
  encode(message: PupilTrainResponse_ChangeSlotMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int32(message.key);
    }
    if (message.value !== undefined) {
      PupilSlotMessage.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PupilTrainResponse_ChangeSlotMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePupilTrainResponse_ChangeSlotMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = PupilSlotMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PupilTrainResponse_ChangeSlotMapEntry>, I>>(
    base?: I,
  ): PupilTrainResponse_ChangeSlotMapEntry {
    return PupilTrainResponse_ChangeSlotMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PupilTrainResponse_ChangeSlotMapEntry>, I>>(
    object: I,
  ): PupilTrainResponse_ChangeSlotMapEntry {
    const message = createBasePupilTrainResponse_ChangeSlotMapEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? PupilSlotMessage.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBasePupilVitalityRequest(): PupilVitalityRequest {
  return { slotIndexAndCountMap: {} };
}

export const PupilVitalityRequest: MessageFns<PupilVitalityRequest> = {
  encode(message: PupilVitalityRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.slotIndexAndCountMap).forEach(([key, value]) => {
      PupilVitalityRequest_SlotIndexAndCountMapEntry.encode({ key: key as any, value }, writer.uint32(10).fork())
        .join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PupilVitalityRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePupilVitalityRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = PupilVitalityRequest_SlotIndexAndCountMapEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.slotIndexAndCountMap[entry1.key] = entry1.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PupilVitalityRequest>, I>>(base?: I): PupilVitalityRequest {
    return PupilVitalityRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PupilVitalityRequest>, I>>(object: I): PupilVitalityRequest {
    const message = createBasePupilVitalityRequest();
    message.slotIndexAndCountMap = Object.entries(object.slotIndexAndCountMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBasePupilVitalityRequest_SlotIndexAndCountMapEntry(): PupilVitalityRequest_SlotIndexAndCountMapEntry {
  return { key: 0, value: 0 };
}

export const PupilVitalityRequest_SlotIndexAndCountMapEntry: MessageFns<
  PupilVitalityRequest_SlotIndexAndCountMapEntry
> = {
  encode(
    message: PupilVitalityRequest_SlotIndexAndCountMapEntry,
    writer: BinaryWriter = new BinaryWriter(),
  ): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int32(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PupilVitalityRequest_SlotIndexAndCountMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePupilVitalityRequest_SlotIndexAndCountMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PupilVitalityRequest_SlotIndexAndCountMapEntry>, I>>(
    base?: I,
  ): PupilVitalityRequest_SlotIndexAndCountMapEntry {
    return PupilVitalityRequest_SlotIndexAndCountMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PupilVitalityRequest_SlotIndexAndCountMapEntry>, I>>(
    object: I,
  ): PupilVitalityRequest_SlotIndexAndCountMapEntry {
    const message = createBasePupilVitalityRequest_SlotIndexAndCountMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBasePupilVitalityResponse(): PupilVitalityResponse {
  return { slotUpdateMap: {} };
}

export const PupilVitalityResponse: MessageFns<PupilVitalityResponse> = {
  encode(message: PupilVitalityResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.slotUpdateMap).forEach(([key, value]) => {
      PupilVitalityResponse_SlotUpdateMapEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PupilVitalityResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePupilVitalityResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = PupilVitalityResponse_SlotUpdateMapEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.slotUpdateMap[entry1.key] = entry1.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PupilVitalityResponse>, I>>(base?: I): PupilVitalityResponse {
    return PupilVitalityResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PupilVitalityResponse>, I>>(object: I): PupilVitalityResponse {
    const message = createBasePupilVitalityResponse();
    message.slotUpdateMap = Object.entries(object.slotUpdateMap ?? {}).reduce<{ [key: number]: PupilSlotMessage }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = PupilSlotMessage.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBasePupilVitalityResponse_SlotUpdateMapEntry(): PupilVitalityResponse_SlotUpdateMapEntry {
  return { key: 0, value: undefined };
}

export const PupilVitalityResponse_SlotUpdateMapEntry: MessageFns<PupilVitalityResponse_SlotUpdateMapEntry> = {
  encode(message: PupilVitalityResponse_SlotUpdateMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int32(message.key);
    }
    if (message.value !== undefined) {
      PupilSlotMessage.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PupilVitalityResponse_SlotUpdateMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePupilVitalityResponse_SlotUpdateMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = PupilSlotMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PupilVitalityResponse_SlotUpdateMapEntry>, I>>(
    base?: I,
  ): PupilVitalityResponse_SlotUpdateMapEntry {
    return PupilVitalityResponse_SlotUpdateMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PupilVitalityResponse_SlotUpdateMapEntry>, I>>(
    object: I,
  ): PupilVitalityResponse_SlotUpdateMapEntry {
    const message = createBasePupilVitalityResponse_SlotUpdateMapEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? PupilSlotMessage.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBasePupilWorkRequest(): PupilWorkRequest {
  return { pupilId: 0, slotIndex: 0 };
}

export const PupilWorkRequest: MessageFns<PupilWorkRequest> = {
  encode(message: PupilWorkRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pupilId !== 0) {
      writer.uint32(8).int64(message.pupilId);
    }
    if (message.slotIndex !== 0) {
      writer.uint32(16).int32(message.slotIndex);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PupilWorkRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePupilWorkRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.pupilId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.slotIndex = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PupilWorkRequest>, I>>(base?: I): PupilWorkRequest {
    return PupilWorkRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PupilWorkRequest>, I>>(object: I): PupilWorkRequest {
    const message = createBasePupilWorkRequest();
    message.pupilId = object.pupilId ?? 0;
    message.slotIndex = object.slotIndex ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
