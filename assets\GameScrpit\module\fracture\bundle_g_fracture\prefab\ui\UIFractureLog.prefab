[{"__type__": "cc.Prefab", "_name": "UIFractureLog", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "UIFractureLog", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 60}, {"__id__": 566}], "_active": true, "_components": [{"__id__": 690}, {"__id__": 692}], "_prefab": {"__id__": 694}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 21.379499999999993, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 3}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 2}, "asset": {"__uuid__": "468968c7-ca41-40f2-9e5f-74ee660b4fd8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 4}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "37fFx5Ze5ACZ63M0sqHF6W", "prefabRootNode": {"__id__": 1}, "mountedChildren": [{"__id__": 5}], "mountedComponents": [], "propertyOverrides": [{"__id__": 33}, {"__id__": 35}, {"__id__": 36}, {"__id__": 37}, {"__id__": 38}, {"__id__": 40}, {"__id__": 42}, {"__id__": 44}, {"__id__": 46}, {"__id__": 48}, {"__id__": 50}, {"__id__": 52}, {"__id__": 54}, {"__id__": 56}, {"__id__": 58}, {"__id__": 59}], "removedComponents": []}, {"__type__": "cc.MountedChildrenInfo", "targetInfo": {"__id__": 6}, "nodes": [{"__id__": 7}]}, {"__type__": "cc.TargetInfo", "localID": ["ccATktvIZPJY5yZnIE9AmP"]}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 2}}, "_parent": {"__id__": 8}, "_children": [{"__id__": 18}], "_active": true, "_components": [{"__id__": 30}], "_prefab": {"__id__": 32}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "dialog_content", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 7}], "_active": true, "_components": [{"__id__": 9}, {"__id__": 11}, {"__id__": 13}, {"__id__": 15}], "_prefab": {"__id__": 17}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -16.074500000000057, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 8}, "_enabled": true, "__prefab": {"__id__": 10}, "_contentSize": {"__type__": "cc.Size", "width": 648.4170000000001, "height": 1084.4789999999998}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d8DuFrkIFIJrvRcNSr2YgG"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 8}, "_enabled": true, "__prefab": {"__id__": 12}, "_alignFlags": 45, "_target": null, "_left": 18.95149999999997, "_right": 18.951499999999964, "_top": 51.177000000000056, "_bottom": 19.027999999999977, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a2m76nTfpD5q+4RNdfiIOx"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 8}, "_enabled": true, "__prefab": {"__id__": 14}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "babhDkmCNDYqH1Zm6vqUdq"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 8}, "_enabled": true, "__prefab": {"__id__": 16}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9ddIeszclP2KkZe+N/mFmL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ccATktvIZPJY5yZnIE9AmP", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_log_list", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 7}, "_children": [], "_active": true, "_components": [{"__id__": 19}, {"__id__": 21}, {"__id__": 23}, {"__id__": 25}, {"__id__": 27}], "_prefab": {"__id__": 29}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -8.2405, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 20}, "_contentSize": {"__type__": "cc.Size", "width": 615, "height": 1028.447}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d93aLI9zFGEYF6R4mw/ChJ"}, {"__type__": "fe48a55/2VHg5MmeyH/Ap7U", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 22}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "70U+ycMZdAOa0buYDxnxe+"}, {"__type__": "a6dafXbo3BJfLrODGZdcqPR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 24}, "spaceY": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d6+XPCBKFFjpg9bT5LXu6E"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 26}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a2LMKMTwJP7IqEPLgcFL0s"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 28}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7eMeyf9cxCiIgaB+NGGnef"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "deNfDUnkNPoYa+CEa4puXn", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 7}, "_enabled": true, "__prefab": {"__id__": 31}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f6sg8UoSZDmJo0QR/a7ty1"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "87B6UsTk1IBpvZxDGC//BS", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 34}, "propertyPath": ["_name"], "value": "DialogSub"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 34}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -2.9410000000000025, "y": -23.432999999999993, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 34}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 34}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 39}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 140, "height": 85.68}}, {"__type__": "cc.TargetInfo", "localID": ["72cV1bU/9PTrznXTmFb0cY"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 41}, "propertyPath": ["_strTitle"], "value": "日志"}, {"__type__": "cc.TargetInfo", "localID": ["f2ofLYMWBPU4DygV2KdYZ4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 43}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 686.32, "height": 1154.684}}, {"__type__": "cc.TargetInfo", "localID": ["dcMfg1dj1MdZqre7xUgJqz"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 45}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 686.32, "height": 1154.684}}, {"__type__": "cc.TargetInfo", "localID": ["44g2VAc1VIU42rC+WZOf2b"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 47}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 648.4170000000001, "height": 1084.4789999999998}}, {"__type__": "cc.TargetInfo", "localID": ["d8DuFrkIFIJrvRcNSr2YgG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 49}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 549.342, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["8d9SnxGQhLO52jTVFOf8SD"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 51}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 686.32, "height": 112}}, {"__type__": "cc.TargetInfo", "localID": ["c81ZpQbBZMrp/EQrEebYzT"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 53}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -216.81950000000006, "y": -29.579000000000008, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["b6eeINGDpEZLVcaNWdjJaK"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 55}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 324.63950000000006, "y": 8.123999999999995, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["1fOxWCfPxPAqI5OgnftRyd"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 57}, "propertyPath": ["clickEvents", "0", "target"], "value": {"__id__": 1}}, {"__type__": "cc.TargetInfo", "localID": ["630kdrTEhE3r2bQfHu5867"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 57}, "propertyPath": ["clickEvents", "0", "_componentId"], "value": "ae31ejR75NGnYuLfaSBCqlV"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 57}, "propertyPath": ["clickEvents", "0", "handler"], "value": "closeBack"}, {"__type__": "cc.Node", "_name": "log_viewholder", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 61}, {"__id__": 67}, {"__id__": 73}, {"__id__": 81}, {"__id__": 95}, {"__id__": 101}, {"__id__": 107}, {"__id__": 115}, {"__id__": 178}, {"__id__": 264}, {"__id__": 276}, {"__id__": 282}, {"__id__": 288}, {"__id__": 294}, {"__id__": 373}, {"__id__": 553}], "_active": false, "_components": [{"__id__": 561}, {"__id__": 563}], "_prefab": {"__id__": 565}, "_lpos": {"__type__": "cc.Vec3", "x": -761.22, "y": 324.399, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "TY_bg_9g_lansedi_miaobian", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 60}, "_children": [], "_active": true, "_components": [{"__id__": 62}, {"__id__": 64}], "_prefab": {"__id__": 66}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 61}, "_enabled": true, "__prefab": {"__id__": 63}, "_contentSize": {"__type__": "cc.Size", "width": 615, "height": 330}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "35/Y/oeYlOU7CXypRF5lec"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 61}, "_enabled": true, "__prefab": {"__id__": 65}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5fca5cce-83b1-4ac1-a0a6-9abd0b00b04f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "54ql7qGPxBap1n/WD4ra3J"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "15H14vdRVHZ4XpH1bhqea0", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "TY_bg_9g_tanchuangdi", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 60}, "_children": [], "_active": true, "_components": [{"__id__": 68}, {"__id__": 70}], "_prefab": {"__id__": 72}, "_lpos": {"__type__": "cc.Vec3", "x": 0.274, "y": -76.242, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 67}, "_enabled": true, "__prefab": {"__id__": 69}, "_contentSize": {"__type__": "cc.Size", "width": 583.966, "height": 140.618}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a9kFM6KelC0IzS+oXi8qmm"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 67}, "_enabled": true, "__prefab": {"__id__": 71}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2f256584-d6a2-44d0-8d3d-d805e825c218@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a1YxINdRhNaLD4O/9rY3Qy"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c5LZZMAShEVofDpvxWd23G", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 60}, "_prefab": {"__id__": 74}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 73}, "asset": {"__uuid__": "9f3bd2e8-623f-4363-815e-8dbdf1ced8ba", "__expectedType__": "cc.Prefab"}, "fileId": "e3t6Sl+oNAFp8fYboS8QNH", "instance": {"__id__": 75}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "73xtX7AIRP8bWDG/6+KYgt", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 76}, {"__id__": 78}, {"__id__": 79}, {"__id__": 80}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 77}, "propertyPath": ["_name"], "value": "btn_header"}, {"__type__": "cc.TargetInfo", "localID": ["e3t6Sl+oNAFp8fYboS8QNH"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 77}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -227.751, "y": 78.602, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 77}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 77}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_name": "node_header_mask", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 60}, "_children": [{"__id__": 82}], "_active": true, "_components": [{"__id__": 88}, {"__id__": 90}, {"__id__": 92}], "_prefab": {"__id__": 94}, "_lpos": {"__type__": "cc.Vec3", "x": -227.751, "y": 78.602, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_monster_header", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 81}, "_children": [], "_active": true, "_components": [{"__id__": 83}, {"__id__": 85}], "_prefab": {"__id__": 87}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 82}, "_enabled": true, "__prefab": {"__id__": 84}, "_contentSize": {"__type__": "cc.Size", "width": 114, "height": 114}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cc7enQ4t1MWaKel1eVLtyZ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 82}, "_enabled": true, "__prefab": {"__id__": 86}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "06oP8VFsBF2LvnotkdinDp"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "932ba0WIBLZaLjNWK8GmRg", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 81}, "_enabled": true, "__prefab": {"__id__": 89}, "_contentSize": {"__type__": "cc.Size", "width": 114, "height": 114}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "360arOSfBIpLFexUT+sBg3"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 81}, "_enabled": true, "__prefab": {"__id__": 91}, "_type": 1, "_inverted": false, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ceL+85iFxDOZfD5iGv674Y"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 81}, "_enabled": true, "__prefab": {"__id__": 93}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6ataMAtF5A158H8ns+GMxj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "10t4AtOKJKML5gLlXqAuO6", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_name", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 60}, "_children": [], "_active": true, "_components": [{"__id__": 96}, {"__id__": 98}], "_prefab": {"__id__": 100}, "_lpos": {"__type__": "cc.Vec3", "x": -157, "y": 131, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 95}, "_enabled": true, "__prefab": {"__id__": 97}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b9Q0XbrIpLQLCyf39G8pMl"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 95}, "_enabled": true, "__prefab": {"__id__": 99}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 57, "g": 115, "b": 174, "a": 255}, "_string": "名字", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cbQSveFKpKcp2JOFMQCR6c"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "65r629yEhHO6JkpPE+bOy0", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "line_fengexian_bai", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 60}, "_children": [], "_active": true, "_components": [{"__id__": 102}, {"__id__": 104}], "_prefab": {"__id__": 106}, "_lpos": {"__type__": "cc.Vec3", "x": -9.738500000000002, "y": 107.602, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": -1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 101}, "_enabled": true, "__prefab": {"__id__": 103}, "_contentSize": {"__type__": "cc.Size", "width": 288.025, "height": 5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fdM8RAlbFOpJuXiFCLrhGE"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 101}, "_enabled": true, "__prefab": {"__id__": 105}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 174, "g": 198, "b": 219, "a": 255}, "_spriteFrame": {"__uuid__": "897fa295-fcb3-4615-9572-5d2cd1056943@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "50mLwuMF9Mb4Jreg7ZzDd6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ca/Z2MbuFG0Yl5NH12FZde", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 60}, "_prefab": {"__id__": 108}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 107}, "asset": {"__uuid__": "2a112965-2a1f-4c01-9648-855941dd48b0", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 109}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "70hJbPXfRIEZotAzDAYOiK", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 110}, {"__id__": 112}, {"__id__": 113}, {"__id__": 114}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 111}, "propertyPath": ["_name"], "value": "item_fight_cost"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 111}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 229, "y": 129, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 111}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 111}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_name": "btn_help", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 60}, "_children": [{"__id__": 116}, {"__id__": 134}, {"__id__": 152}], "_active": true, "_components": [{"__id__": 170}, {"__id__": 172}, {"__id__": 174}], "_prefab": {"__id__": 177}, "_lpos": {"__type__": "cc.Vec3", "x": 230, "y": 27, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "node_enable", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 115}, "_children": [{"__id__": 117}, {"__id__": 123}], "_active": false, "_components": [{"__id__": 131}], "_prefab": {"__id__": 133}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "wj_btn_07_lan", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 116}, "_children": [], "_active": true, "_components": [{"__id__": 118}, {"__id__": 120}], "_prefab": {"__id__": 122}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 117}, "_enabled": true, "__prefab": {"__id__": 119}, "_contentSize": {"__type__": "cc.Size", "width": 119, "height": 40.7}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0bPk/oBdVEHb7BgxthLHk2"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 117}, "_enabled": true, "__prefab": {"__id__": 121}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "cf56348d-b49e-4dfb-ab0c-7ec88fe4870a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "51Cbn0LAtFKaflDL7zPDVT"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "87bCp7ABZMkKdjFqdzlv6v", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 116}, "_children": [], "_active": true, "_components": [{"__id__": 124}, {"__id__": 126}, {"__id__": 128}], "_prefab": {"__id__": 130}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 123}, "_enabled": true, "__prefab": {"__id__": 125}, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 54.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0eaFYdqV5Jv4s5nf0uRG0a"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 123}, "_enabled": true, "__prefab": {"__id__": 127}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "求助", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25, "_fontSize": 25, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 48, "g": 103, "b": 180, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6cqpKOgDpJZK2eaoF9aWDY"}, {"__type__": "4b414FkqlpD0ac92djm25UF", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 123}, "_enabled": true, "__prefab": {"__id__": 129}, "_args": [], "_messageKey": 444, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "05kxeGyahCw5qIXTIgCy5x"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9cyRDZhuBPzY0dt2GC4Tai", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 116}, "_enabled": true, "__prefab": {"__id__": 132}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "43jJsBuOJOB4BIGX9WNrFS"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "61yD657pxMi4oXmf4ZzH6K", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_disable", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 115}, "_children": [{"__id__": 135}, {"__id__": 141}], "_active": true, "_components": [{"__id__": 149}], "_prefab": {"__id__": 151}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "wj_btn_07_lan", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 134}, "_children": [], "_active": true, "_components": [{"__id__": 136}, {"__id__": 138}], "_prefab": {"__id__": 140}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 135}, "_enabled": true, "__prefab": {"__id__": 137}, "_contentSize": {"__type__": "cc.Size", "width": 119, "height": 40.7}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "426xQ69jBExaaloBKWpFzx"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 135}, "_enabled": true, "__prefab": {"__id__": 139}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "cf56348d-b49e-4dfb-ab0c-7ec88fe4870a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c8fOB0BStD8b5g2DOiPRZ/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "54PSNGNnxIRI8sRKaCJ7Cy", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 134}, "_children": [], "_active": true, "_components": [{"__id__": 142}, {"__id__": 144}, {"__id__": 146}], "_prefab": {"__id__": 148}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 141}, "_enabled": true, "__prefab": {"__id__": 143}, "_contentSize": {"__type__": "cc.Size", "width": 79, "height": 54.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "deoL1EwLVF0JXbXFmSYavx"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 141}, "_enabled": true, "__prefab": {"__id__": 145}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "已求助", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25, "_fontSize": 25, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 135, "g": 134, "b": 131, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ddh/4wf29B8rdcIkczPdM6"}, {"__type__": "4b414FkqlpD0ac92djm25UF", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 141}, "_enabled": true, "__prefab": {"__id__": 147}, "_args": [], "_messageKey": 446, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "13Trl5/jFCiqrqe4P2x6rm"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2b0wHS99BBSanMYrsTC4tR", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 134}, "_enabled": true, "__prefab": {"__id__": 150}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "169wpUVdNGdoDBKgrYxz8o"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "75UtYQtXRBNpqK0hzjXCVr", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_selected", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 115}, "_children": [{"__id__": 153}, {"__id__": 159}], "_active": false, "_components": [{"__id__": 167}], "_prefab": {"__id__": 169}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "wj_btn_07_lan", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 152}, "_children": [], "_active": true, "_components": [{"__id__": 154}, {"__id__": 156}], "_prefab": {"__id__": 158}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 153}, "_enabled": true, "__prefab": {"__id__": 155}, "_contentSize": {"__type__": "cc.Size", "width": 119, "height": 40.7}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ddmovYC+FMr5rxNrUd2bOS"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 153}, "_enabled": true, "__prefab": {"__id__": 157}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "cf56348d-b49e-4dfb-ab0c-7ec88fe4870a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dcRcHk4f5EpJ5nbf6nENep"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f7+KjVUWRDyYt7rXNNhTWt", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 152}, "_children": [], "_active": true, "_components": [{"__id__": 160}, {"__id__": 162}, {"__id__": 164}], "_prefab": {"__id__": 166}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 159}, "_enabled": true, "__prefab": {"__id__": 161}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9cuLQOI8NAZK7SzvmlrqP/"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 159}, "_enabled": true, "__prefab": {"__id__": 163}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "求助", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25, "_fontSize": 25, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fdtsiTMcVFZ6qndLpahTHa"}, {"__type__": "4b414FkqlpD0ac92djm25UF", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 159}, "_enabled": true, "__prefab": {"__id__": 165}, "_args": [], "_messageKey": 444, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b4Xbu7BnNJCI5jo0Mtp8oL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "afEV7L7hRPmIfaWj9avmKo", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 152}, "_enabled": true, "__prefab": {"__id__": 168}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "17e5UGvBZAa4ZNDR4pYW6p"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a91a5ZPlROcY3feEv6m7tE", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 115}, "_enabled": true, "__prefab": {"__id__": 171}, "_contentSize": {"__type__": "cc.Size", "width": 127.1, "height": 44.3}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "56BZYeO69NB4BPwr/Juhrg"}, {"__type__": "59b1da6uDxL2r5VRR8PWhCO", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 115}, "_enabled": true, "__prefab": {"__id__": 173}, "enableNode": {"__id__": 116}, "disableNode": {"__id__": 134}, "selectedNode": {"__id__": 152}, "_messageKey": 0, "_args": [], "_selected": false, "_btnEnable": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "25JmVoX15KTKTx+dD9XxAY"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 115}, "_enabled": true, "__prefab": {"__id__": 175}, "clickEvents": [{"__id__": 176}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "54aO2aLOFNS58Z2g0AtaCg"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 60}, "component": "", "_componentId": "ec544cF2t9PO6BO3UJfxAHI", "handler": "onClickHelp", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6c5q7v9H5NF6ZU63xwJNf1", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_fight", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 60}, "_children": [{"__id__": 179}, {"__id__": 187}, {"__id__": 205}, {"__id__": 223}, {"__id__": 241}], "_active": true, "_components": [{"__id__": 256}, {"__id__": 258}, {"__id__": 260}], "_prefab": {"__id__": 263}, "_lpos": {"__type__": "cc.Vec3", "x": 227.24900000000002, "y": 85.221, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 178}, "_children": [], "_active": true, "_components": [{"__id__": 180}, {"__id__": 182}, {"__id__": 184}], "_prefab": {"__id__": 186}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 1, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 179}, "_enabled": true, "__prefab": {"__id__": 181}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 41.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8d9kGYwIxLw6/XF3FGZG95"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 179}, "_enabled": true, "__prefab": {"__id__": 183}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 251, "b": 229, "a": 255}, "_string": "挑战", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 183, "g": 110, "b": 56, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4be+PqT21FiZQ8hPerxUw2"}, {"__type__": "4b414FkqlpD0ac92djm25UF", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 179}, "_enabled": true, "__prefab": {"__id__": 185}, "_args": [], "_messageKey": 423, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e8sxuAb+lP0JFVqrKk4OJK"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "aeg+UTLS1A4ZrxZNSrHNbw", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_enable", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 178}, "_children": [{"__id__": 188}, {"__id__": 194}], "_active": false, "_components": [{"__id__": 202}], "_prefab": {"__id__": 204}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btn_huangse", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 187}, "_children": [], "_active": true, "_components": [{"__id__": 189}, {"__id__": 191}], "_prefab": {"__id__": 193}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 188}, "_enabled": true, "__prefab": {"__id__": 190}, "_contentSize": {"__type__": "cc.Size", "width": 139, "height": 47}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "86zt0rGDFOJIPzmyIDmZCa"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 188}, "_enabled": true, "__prefab": {"__id__": 192}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8aac8aa5-631a-436e-92ba-84e56018ec61@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b0hSAz3T9PnZoI3akofA0h"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f2WnejxoBBqbkcnsL2N8vj", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 187}, "_children": [], "_active": true, "_components": [{"__id__": 195}, {"__id__": 197}, {"__id__": 199}], "_prefab": {"__id__": 201}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 194}, "_enabled": true, "__prefab": {"__id__": 196}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 54.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "97qXwtO6dH3pusoCd3pWJG"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 194}, "_enabled": true, "__prefab": {"__id__": 198}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "挑战", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 183, "g": 110, "b": 56, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2cICxQCi1CFp8lZPgj+F67"}, {"__type__": "4b414FkqlpD0ac92djm25UF", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 194}, "_enabled": true, "__prefab": {"__id__": 200}, "_args": [], "_messageKey": 423, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2fqK2zsZxPTqX/FKt4s6k5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "89NTcGKZlEHqqijlNxarRn", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 187}, "_enabled": true, "__prefab": {"__id__": 203}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c5BPB39/lPno1SeTrpGoJh"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ffih0LuCJP0onAgso06S5f", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_disable", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 178}, "_children": [{"__id__": 206}, {"__id__": 212}], "_active": true, "_components": [{"__id__": 220}], "_prefab": {"__id__": 222}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btn_huangse", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 205}, "_children": [], "_active": true, "_components": [{"__id__": 207}, {"__id__": 209}], "_prefab": {"__id__": 211}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 206}, "_enabled": true, "__prefab": {"__id__": 208}, "_contentSize": {"__type__": "cc.Size", "width": 139, "height": 47}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "baN3T1VQFE15VQb7nHBdoT"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 206}, "_enabled": true, "__prefab": {"__id__": 210}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8aac8aa5-631a-436e-92ba-84e56018ec61@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "45Z7DVHzhDm4l509j81Y4i"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "adtr5YxjNDW5cgoS1Txde6", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 205}, "_children": [], "_active": true, "_components": [{"__id__": 213}, {"__id__": 215}, {"__id__": 217}], "_prefab": {"__id__": 219}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 212}, "_enabled": true, "__prefab": {"__id__": 214}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 54.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "04bz316apAm76j9HtGq4EC"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 212}, "_enabled": true, "__prefab": {"__id__": 216}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "挑战", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 135, "g": 134, "b": 131, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f5byVCjgREYqqa9ga1saQk"}, {"__type__": "4b414FkqlpD0ac92djm25UF", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 212}, "_enabled": true, "__prefab": {"__id__": 218}, "_args": [], "_messageKey": 423, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ecAnY9vJ1NmaF5kdQlQNAT"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a97BsA7wZIFY2T0ryG+0Dv", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 205}, "_enabled": true, "__prefab": {"__id__": 221}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "54QzajRH5Dg5k8nU5oT219"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fdsPYPrptBn4wo2sT/JOT7", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_selected", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 178}, "_children": [{"__id__": 224}, {"__id__": 232}], "_active": false, "_components": [{"__id__": 238}], "_prefab": {"__id__": 240}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 223}, "_children": [], "_active": true, "_components": [{"__id__": 225}, {"__id__": 227}, {"__id__": 229}], "_prefab": {"__id__": 231}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 224}, "_enabled": true, "__prefab": {"__id__": 226}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 54.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "34Wra65mBK9qV5axWcFIvl"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 224}, "_enabled": true, "__prefab": {"__id__": 228}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "挑战", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aaiC+JGcFFsq/ojB7Qobhl"}, {"__type__": "4b414FkqlpD0ac92djm25UF", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 224}, "_enabled": true, "__prefab": {"__id__": 230}, "_args": [], "_messageKey": 423, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bcdyCaOeVIDK9C8FlwYpji"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1b5bLbjFxLP4+cjNIGMvbT", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_huangse", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 223}, "_children": [], "_active": true, "_components": [{"__id__": 233}, {"__id__": 235}], "_prefab": {"__id__": 237}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 232}, "_enabled": true, "__prefab": {"__id__": 234}, "_contentSize": {"__type__": "cc.Size", "width": 139, "height": 47}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0aYZ1FLe5OQ54WMcgQ3vfB"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 232}, "_enabled": true, "__prefab": {"__id__": 236}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8aac8aa5-631a-436e-92ba-84e56018ec61@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "77JklZqt5HapsVOep4Ka8q"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1dlH9xYCpDt7Z2WDRYaj85", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 223}, "_enabled": true, "__prefab": {"__id__": 239}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c6ietRwzlExqMFJ19YyltA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4ekdoP8e5PQY+mdunFThib", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 178}, "_prefab": {"__id__": 242}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 241}, "asset": {"__uuid__": "ddfa3a35-1513-4b06-970d-3018496ac69e", "__expectedType__": "cc.Prefab"}, "fileId": "ddw48bgTVJQaI4eRLCZMBo", "instance": {"__id__": 243}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "11t/R7gVBHAKdWLx0QsVdq", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 244}, {"__id__": 246}, {"__id__": 247}, {"__id__": 248}, {"__id__": 249}, {"__id__": 251}, {"__id__": 253}, {"__id__": 254}, {"__id__": 255}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 245}, "propertyPath": ["_name"], "value": "lbl_fight_cd"}, {"__type__": "cc.TargetInfo", "localID": ["ddw48bgTVJQaI4eRLCZMBo"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 245}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 245}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 245}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 250}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 72.75, "height": 54.4}}, {"__type__": "cc.TargetInfo", "localID": ["94RD/5HzNB4bdksbVCxbeX"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 252}, "propertyPath": ["_actualFontSize"], "value": 25}, {"__type__": "cc.TargetInfo", "localID": ["73icu5TNJLZaYTvRIxQDjK"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 252}, "propertyPath": ["_fontSize"], "value": 25}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 252}, "propertyPath": ["_outlineColor"], "value": {"__type__": "cc.Color", "r": 183, "g": 110, "b": 56, "a": 255}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 245}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 178}, "_enabled": true, "__prefab": {"__id__": 257}, "_contentSize": {"__type__": "cc.Size", "width": 139, "height": 47}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "511NufNQZEtpcIlYFRbtKI"}, {"__type__": "59b1da6uDxL2r5VRR8PWhCO", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 178}, "_enabled": true, "__prefab": {"__id__": 259}, "enableNode": {"__id__": 187}, "disableNode": {"__id__": 205}, "selectedNode": {"__id__": 223}, "_messageKey": 423, "_args": [], "_selected": false, "_btnEnable": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "93Lf0NEApKhqMHbMkhs5sG"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 178}, "_enabled": true, "__prefab": {"__id__": 261}, "clickEvents": [{"__id__": 262}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e6UiX/GclI96H9JwLeAY6O"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 60}, "component": "", "_componentId": "ec544cF2t9PO6BO3UJfxAHI", "handler": "onClickFight", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "68N2gPrWVN2JWVxKeBt9GA", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_zhanli", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 60}, "_children": [{"__id__": 265}], "_active": true, "_components": [{"__id__": 271}, {"__id__": 273}], "_prefab": {"__id__": 275}, "_lpos": {"__type__": "cc.Vec3", "x": -31, "y": 72, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lbl_power", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 264}, "_children": [], "_active": true, "_components": [{"__id__": 266}, {"__id__": 268}], "_prefab": {"__id__": 270}, "_lpos": {"__type__": "cc.Vec3", "x": -34, "y": -4, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 265}, "_enabled": true, "__prefab": {"__id__": 267}, "_contentSize": {"__type__": "cc.Size", "width": 46.5, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f59bku+5FM4ajYhaexY150"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 265}, "_enabled": true, "__prefab": {"__id__": 269}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "123", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "e01d7349-fb8f-45ba-825c-b03a50fe22f1", "__expectedType__": "cc.BitmapFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "90jKocdBhIrY4yZwh7yzNE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "327byqc61OVb+OqgZfSELR", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 264}, "_enabled": true, "__prefab": {"__id__": 272}, "_contentSize": {"__type__": "cc.Size", "width": 230.4, "height": 52.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "48sGvwgNxDuq8qU1pj2l+O"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 264}, "_enabled": true, "__prefab": {"__id__": 274}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "da85f0d4-ec05-436a-8076-6e68610dd0c3@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "88qJd7o5lFKb5E5Ey4j68a"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c72ebymMtIlazjwhrV1UXP", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_sklx_rizhi_jind<PERSON><PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 60}, "_children": [], "_active": true, "_components": [{"__id__": 277}, {"__id__": 279}], "_prefab": {"__id__": 281}, "_lpos": {"__type__": "cc.Vec3", "x": -32, "y": 21, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 276}, "_enabled": true, "__prefab": {"__id__": 278}, "_contentSize": {"__type__": "cc.Size", "width": 241, "height": 28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "87I782649BLZ3cVoPAjIEK"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 276}, "_enabled": true, "__prefab": {"__id__": 280}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "7e8c6b5e-6fd9-4996-add7-192ced357d65@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1duGO9095GUpcK3ABxwYm6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "79NxlGRfRNNJnwbdiC4k2T", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_process", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 60}, "_children": [], "_active": true, "_components": [{"__id__": 283}, {"__id__": 285}], "_prefab": {"__id__": 287}, "_lpos": {"__type__": "cc.Vec3", "x": -32, "y": 21, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 282}, "_enabled": true, "__prefab": {"__id__": 284}, "_contentSize": {"__type__": "cc.Size", "width": 229, "height": 16}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "30IvtM+JtLPIapfO0IyrnL"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 282}, "_enabled": true, "__prefab": {"__id__": 286}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9ad8ff54-e269-405e-9a4b-2084839b35af@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 3, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0.4, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "06flnFhD1MjpmPxhs4L2VB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dcoIlXRCxGcINo87vpCBk/", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_process", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 60}, "_children": [], "_active": true, "_components": [{"__id__": 289}, {"__id__": 291}], "_prefab": {"__id__": 293}, "_lpos": {"__type__": "cc.Vec3", "x": -21, "y": 22, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 288}, "_enabled": true, "__prefab": {"__id__": 290}, "_contentSize": {"__type__": "cc.Size", "width": 51.99998474121094, "height": 54.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8dTh9JDrxMkrnLwzIf8xcC"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 288}, "_enabled": true, "__prefab": {"__id__": 292}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "62%", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 106, "g": 88, "b": 82, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "00KOxSuN5KGZPq8JoPQP/a"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f29rCvNDJLPrYPn3xmgWKk", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_award", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 60}, "_children": [{"__id__": 295}, {"__id__": 322}, {"__id__": 346}], "_active": true, "_components": [{"__id__": 370}], "_prefab": {"__id__": 372}, "_lpos": {"__type__": "cc.Vec3", "x": 239.274, "y": -65.24199999999996, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btn_claimable", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 294}, "_children": [{"__id__": 296}, {"__id__": 302}, {"__id__": 308}], "_active": false, "_components": [{"__id__": 316}, {"__id__": 318}], "_prefab": {"__id__": 321}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "zm_bg_baoxiangdi_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 295}, "_children": [], "_active": true, "_components": [{"__id__": 297}, {"__id__": 299}], "_prefab": {"__id__": 301}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 296}, "_enabled": true, "__prefab": {"__id__": 298}, "_contentSize": {"__type__": "cc.Size", "width": 93, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "52yJsK1K1O176q74IWN1X9"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 296}, "_enabled": true, "__prefab": {"__id__": 300}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5de31b8b-9410-4bc4-b84a-f4970d4e0743@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1f3vRgXX5GnboWK6RBoqhc"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bfF4H3uzhAipQQPO3oCzG6", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "icon_zhiji_tujianjianglibaoxiang", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 295}, "_children": [], "_active": true, "_components": [{"__id__": 303}, {"__id__": 305}], "_prefab": {"__id__": 307}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 302}, "_enabled": true, "__prefab": {"__id__": 304}, "_contentSize": {"__type__": "cc.Size", "width": 71, "height": 54}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d4gudmfNJOxKG6qxPZZCOK"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 302}, "_enabled": true, "__prefab": {"__id__": 306}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "59dd4ae0-0649-4b2e-8bed-741fecaf9d5c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "94rzLoJERPnr6b65RhOm7y"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c7kMansaFJX5njGhNuu6IU", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_des", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 295}, "_children": [], "_active": true, "_components": [{"__id__": 309}, {"__id__": 311}, {"__id__": 313}], "_prefab": {"__id__": 315}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -29, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 308}, "_enabled": true, "__prefab": {"__id__": 310}, "_contentSize": {"__type__": "cc.Size", "width": 56, "height": 54.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4dyKhwt5VIKaMgQGYBJ6IA"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 308}, "_enabled": true, "__prefab": {"__id__": 312}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "领取", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 186, "g": 97, "b": 17, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d9ybFuqmdJko7C2qlNCQj2"}, {"__type__": "4b414FkqlpD0ac92djm25UF", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 308}, "_enabled": true, "__prefab": {"__id__": 314}, "_args": [], "_messageKey": 445, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "60Eye0RjhCKLWqCiPomfgv"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "70f8Wm1rRF3I9LLWC8CH2W", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 295}, "_enabled": true, "__prefab": {"__id__": 317}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3dBrxuLnZI3Jm52D2gHP9m"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 295}, "_enabled": true, "__prefab": {"__id__": 319}, "clickEvents": [{"__id__": 320}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4d9ydKieJExpj9JDzR+Tm0"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 60}, "component": "", "_componentId": "ec544cF2t9PO6BO3UJfxAHI", "handler": "onClickClaimable", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8dc2cKu+FDpK0MzMbueWQF", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_unclaimable", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 294}, "_children": [{"__id__": 323}, {"__id__": 329}, {"__id__": 335}], "_active": true, "_components": [{"__id__": 343}], "_prefab": {"__id__": 345}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "zm_bg_bao<PERSON><PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 322}, "_children": [], "_active": true, "_components": [{"__id__": 324}, {"__id__": 326}], "_prefab": {"__id__": 328}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 323}, "_enabled": true, "__prefab": {"__id__": 325}, "_contentSize": {"__type__": "cc.Size", "width": 92, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "adiJdK0/dEU6F2r7Mnj9oq"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 323}, "_enabled": true, "__prefab": {"__id__": 327}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "02923e31-2595-4260-ab94-2d43467f6d84@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e7MGbJa/VGxa0DMJPeFiYY"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "77nBK26x1FdI5oSHPs99WO", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "icon_zhiji_tujianjianglibaoxiang", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 322}, "_children": [], "_active": true, "_components": [{"__id__": 330}, {"__id__": 332}], "_prefab": {"__id__": 334}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 329}, "_enabled": true, "__prefab": {"__id__": 331}, "_contentSize": {"__type__": "cc.Size", "width": 71, "height": 54}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "971tb/V75GSapZK75tA13Z"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 329}, "_enabled": true, "__prefab": {"__id__": 333}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "59dd4ae0-0649-4b2e-8bed-741fecaf9d5c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "20ajrK3jJKHZVOcR5uHlEG"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "faZ3qLr95A66SOJtr1Vdz7", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_des", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 322}, "_children": [], "_active": true, "_components": [{"__id__": 336}, {"__id__": 338}, {"__id__": 340}], "_prefab": {"__id__": 342}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -29, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 335}, "_enabled": true, "__prefab": {"__id__": 337}, "_contentSize": {"__type__": "cc.Size", "width": 108, "height": 54.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "20S+SBhMpPB6Cs93fo5nc0"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 335}, "_enabled": true, "__prefab": {"__id__": 339}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "挑战奖励", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 186, "g": 97, "b": 17, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "38+MB4O5pHjpbBM6/dEGnQ"}, {"__type__": "4b414FkqlpD0ac92djm25UF", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 335}, "_enabled": true, "__prefab": {"__id__": 341}, "_args": [], "_messageKey": 441, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2cj6lF1v1JoJqRqhb79gbJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "50/4dhOx5IapkVeY5O7WLO", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 322}, "_enabled": true, "__prefab": {"__id__": 344}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "321VzvSrFA7JlfR1kivfrP"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "55SPkA44VDebZIpI3TQv1M", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_claimed", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 294}, "_children": [{"__id__": 347}, {"__id__": 353}, {"__id__": 359}], "_active": false, "_components": [{"__id__": 367}], "_prefab": {"__id__": 369}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "zm_bg_bao<PERSON><PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 346}, "_children": [], "_active": true, "_components": [{"__id__": 348}, {"__id__": 350}], "_prefab": {"__id__": 352}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 347}, "_enabled": true, "__prefab": {"__id__": 349}, "_contentSize": {"__type__": "cc.Size", "width": 92, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f1+CU3BkxLfYovB6xDUIjX"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 347}, "_enabled": true, "__prefab": {"__id__": 351}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "02923e31-2595-4260-ab94-2d43467f6d84@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "284McGyRRDHq/lA9XLROtb"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "62ImosrqJEoKeaJ7005Omk", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "icon_zhiji_tujianjianglibaoxiang", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 346}, "_children": [], "_active": true, "_components": [{"__id__": 354}, {"__id__": 356}], "_prefab": {"__id__": 358}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 353}, "_enabled": true, "__prefab": {"__id__": 355}, "_contentSize": {"__type__": "cc.Size", "width": 71, "height": 54}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5cfD/Dj8pFI441Y0LptAlh"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 353}, "_enabled": true, "__prefab": {"__id__": 357}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "59dd4ae0-0649-4b2e-8bed-741fecaf9d5c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "06RnsucQhMeJxNQYoaFOVj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d8Jjh+RyBLXrQcWMgfs5m8", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_des", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 346}, "_children": [], "_active": true, "_components": [{"__id__": 360}, {"__id__": 362}, {"__id__": 364}], "_prefab": {"__id__": 366}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -29, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 359}, "_enabled": true, "__prefab": {"__id__": 361}, "_contentSize": {"__type__": "cc.Size", "width": 82, "height": 54.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f7Aj6DXCdA75iWOk36umeM"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 359}, "_enabled": true, "__prefab": {"__id__": 363}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "已领取", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 186, "g": 97, "b": 17, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5bHmMnIIJBDK5BSsytPK7X"}, {"__type__": "4b414FkqlpD0ac92djm25UF", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 359}, "_enabled": true, "__prefab": {"__id__": 365}, "_args": [], "_messageKey": 167, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d6DYxyhhJF76uSTelNVSTT"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5d3hAv1RRCJrTxYA4x3E88", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 346}, "_enabled": true, "__prefab": {"__id__": 368}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "62vJkhKxVAvLFtiU7bLfae"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "73LZjPV0dGlp2SGibCEcnF", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 294}, "_enabled": true, "__prefab": {"__id__": 371}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "38ojsXH9hNK751yJNU7s+Y"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "72zgPdpU1EQZn+d/IIySq3", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ScrollView", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 60}, "_children": [{"__id__": 374}], "_active": true, "_components": [{"__id__": 546}, {"__id__": 548}, {"__id__": 550}], "_prefab": {"__id__": 552}, "_lpos": {"__type__": "cc.Vec3", "x": -49.82650000000001, "y": -62.047000000000025, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 373}, "_children": [{"__id__": 375}], "_active": true, "_components": [{"__id__": 537}, {"__id__": 539}, {"__id__": 541}, {"__id__": 543}], "_prefab": {"__id__": 545}, "_lpos": {"__type__": "cc.Vec3", "x": -224.8995, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "item_layout", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 374}, "_children": [{"__id__": 376}, {"__id__": 406}, {"__id__": 437}, {"__id__": 468}, {"__id__": 499}], "_active": true, "_components": [{"__id__": 530}, {"__id__": 532}, {"__id__": 534}], "_prefab": {"__id__": 536}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 375}, "_prefab": {"__id__": 377}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 376}, "asset": {"__uuid__": "3416dcab-ee56-478f-b062-b1e826f7b707", "__expectedType__": "cc.Prefab"}, "fileId": "f5VJJTfHtB5aqbwW4jl16A", "instance": {"__id__": 378}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "0aVY08j6hDiZ97jPf76n9R", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 379}, {"__id__": 381}, {"__id__": 382}, {"__id__": 383}, {"__id__": 384}, {"__id__": 386}, {"__id__": 388}, {"__id__": 390}, {"__id__": 392}, {"__id__": 394}, {"__id__": 396}, {"__id__": 398}, {"__id__": 400}, {"__id__": 402}, {"__id__": 403}, {"__id__": 404}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 380}, "propertyPath": ["_name"], "value": "<PERSON><PERSON>"}, {"__type__": "cc.TargetInfo", "localID": ["f5VJJTfHtB5aqbwW4jl16A"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 380}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 46, "y": -3.262, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 380}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 380}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 385}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["83uRFCZDpOWIYKQdKjv3ks"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 387}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["eeCRBl0e1DboDDLjZnVY/4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 389}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["a1tsAamAJGy7Lb6gW02dNW"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 391}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["3f2CI9oKNLFK7+fzc6STXj"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 393}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 92, "height": 92}}, {"__type__": "cc.TargetInfo", "localID": ["76eqiBuHdJiZAdj+7P63VD"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 395}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 92, "height": 92}}, {"__type__": "cc.TargetInfo", "localID": ["c3FKi/GdFIerw2SbrXGJ9M"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 397}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 79, "height": 79}}, {"__type__": "cc.TargetInfo", "localID": ["b8lliivRBLlLauY3fNUDkb"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 399}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 79, "height": 79}}, {"__type__": "cc.TargetInfo", "localID": ["7fVX9mvddJUaVADkQ2cBdp"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 401}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 80, "height": 41.8}}, {"__type__": "cc.TargetInfo", "localID": ["07IRwvCO9Nyq7noluFiXK4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 391}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 42, "y": -23.905, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 380}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 405}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 0.8070175438596491, "y": 0.8070175438596491, "z": 1}}, {"__type__": "cc.TargetInfo", "localID": ["05/dyc9JZPzqOEp1/yFyae"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 375}, "_prefab": {"__id__": 407}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 406}, "asset": {"__uuid__": "3416dcab-ee56-478f-b062-b1e826f7b707", "__expectedType__": "cc.Prefab"}, "fileId": "f5VJJTfHtB5aqbwW4jl16A", "instance": {"__id__": 408}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "08oBNmh+FLZKdrdaY6Cjut", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 409}, {"__id__": 411}, {"__id__": 412}, {"__id__": 413}, {"__id__": 414}, {"__id__": 416}, {"__id__": 418}, {"__id__": 420}, {"__id__": 422}, {"__id__": 424}, {"__id__": 426}, {"__id__": 428}, {"__id__": 430}, {"__id__": 432}, {"__id__": 433}, {"__id__": 434}, {"__id__": 436}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 410}, "propertyPath": ["_name"], "value": "Item-001"}, {"__type__": "cc.TargetInfo", "localID": ["f5VJJTfHtB5aqbwW4jl16A"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 410}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 143.2, "y": -3.262, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 410}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 410}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 415}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["83uRFCZDpOWIYKQdKjv3ks"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 417}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["eeCRBl0e1DboDDLjZnVY/4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 419}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["a1tsAamAJGy7Lb6gW02dNW"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 421}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["3f2CI9oKNLFK7+fzc6STXj"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 423}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 92, "height": 92}}, {"__type__": "cc.TargetInfo", "localID": ["76eqiBuHdJiZAdj+7P63VD"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 425}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 92, "height": 92}}, {"__type__": "cc.TargetInfo", "localID": ["c3FKi/GdFIerw2SbrXGJ9M"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 427}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 79, "height": 79}}, {"__type__": "cc.TargetInfo", "localID": ["b8lliivRBLlLauY3fNUDkb"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 429}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 79, "height": 79}}, {"__type__": "cc.TargetInfo", "localID": ["7fVX9mvddJUaVADkQ2cBdp"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 431}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 80, "height": 41.8}}, {"__type__": "cc.TargetInfo", "localID": ["07IRwvCO9Nyq7noluFiXK4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 421}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 42, "y": -23.905, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 410}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 435}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 0.8070175438596491, "y": 0.8070175438596491, "z": 1}}, {"__type__": "cc.TargetInfo", "localID": ["05/dyc9JZPzqOEp1/yFyae"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 435}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 375}, "_prefab": {"__id__": 438}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 437}, "asset": {"__uuid__": "3416dcab-ee56-478f-b062-b1e826f7b707", "__expectedType__": "cc.Prefab"}, "fileId": "f5VJJTfHtB5aqbwW4jl16A", "instance": {"__id__": 439}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "aaJQDA4OJGva3vYI19az6n", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 440}, {"__id__": 442}, {"__id__": 443}, {"__id__": 444}, {"__id__": 445}, {"__id__": 447}, {"__id__": 449}, {"__id__": 451}, {"__id__": 453}, {"__id__": 455}, {"__id__": 457}, {"__id__": 459}, {"__id__": 461}, {"__id__": 463}, {"__id__": 464}, {"__id__": 465}, {"__id__": 467}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 441}, "propertyPath": ["_name"], "value": "Item-002"}, {"__type__": "cc.TargetInfo", "localID": ["f5VJJTfHtB5aqbwW4jl16A"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 441}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 240.39999999999998, "y": -3.262, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 441}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 441}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 446}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["83uRFCZDpOWIYKQdKjv3ks"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 448}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["eeCRBl0e1DboDDLjZnVY/4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 450}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["a1tsAamAJGy7Lb6gW02dNW"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 452}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["3f2CI9oKNLFK7+fzc6STXj"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 454}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 92, "height": 92}}, {"__type__": "cc.TargetInfo", "localID": ["76eqiBuHdJiZAdj+7P63VD"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 456}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 92, "height": 92}}, {"__type__": "cc.TargetInfo", "localID": ["c3FKi/GdFIerw2SbrXGJ9M"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 458}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 79, "height": 79}}, {"__type__": "cc.TargetInfo", "localID": ["b8lliivRBLlLauY3fNUDkb"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 460}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 79, "height": 79}}, {"__type__": "cc.TargetInfo", "localID": ["7fVX9mvddJUaVADkQ2cBdp"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 462}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 80, "height": 41.8}}, {"__type__": "cc.TargetInfo", "localID": ["07IRwvCO9Nyq7noluFiXK4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 452}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 42, "y": -23.905, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 441}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 466}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 0.8070175438596491, "y": 0.8070175438596491, "z": 1}}, {"__type__": "cc.TargetInfo", "localID": ["05/dyc9JZPzqOEp1/yFyae"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 466}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 375}, "_prefab": {"__id__": 469}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 468}, "asset": {"__uuid__": "3416dcab-ee56-478f-b062-b1e826f7b707", "__expectedType__": "cc.Prefab"}, "fileId": "f5VJJTfHtB5aqbwW4jl16A", "instance": {"__id__": 470}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "525IejGbdH6p7DjT0VP9K9", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 471}, {"__id__": 473}, {"__id__": 474}, {"__id__": 475}, {"__id__": 476}, {"__id__": 478}, {"__id__": 480}, {"__id__": 482}, {"__id__": 484}, {"__id__": 486}, {"__id__": 488}, {"__id__": 490}, {"__id__": 492}, {"__id__": 494}, {"__id__": 495}, {"__id__": 496}, {"__id__": 498}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 472}, "propertyPath": ["_name"], "value": "Item-003"}, {"__type__": "cc.TargetInfo", "localID": ["f5VJJTfHtB5aqbwW4jl16A"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 472}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 337.59999999999997, "y": -3.262, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 472}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 472}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 477}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["83uRFCZDpOWIYKQdKjv3ks"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 479}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["eeCRBl0e1DboDDLjZnVY/4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 481}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["a1tsAamAJGy7Lb6gW02dNW"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 483}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["3f2CI9oKNLFK7+fzc6STXj"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 485}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 92, "height": 92}}, {"__type__": "cc.TargetInfo", "localID": ["76eqiBuHdJiZAdj+7P63VD"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 487}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 92, "height": 92}}, {"__type__": "cc.TargetInfo", "localID": ["c3FKi/GdFIerw2SbrXGJ9M"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 489}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 79, "height": 79}}, {"__type__": "cc.TargetInfo", "localID": ["b8lliivRBLlLauY3fNUDkb"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 491}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 79, "height": 79}}, {"__type__": "cc.TargetInfo", "localID": ["7fVX9mvddJUaVADkQ2cBdp"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 493}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 80, "height": 41.8}}, {"__type__": "cc.TargetInfo", "localID": ["07IRwvCO9Nyq7noluFiXK4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 483}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 42, "y": -23.905, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 472}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 497}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 0.8070175438596491, "y": 0.8070175438596491, "z": 1}}, {"__type__": "cc.TargetInfo", "localID": ["05/dyc9JZPzqOEp1/yFyae"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 497}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 375}, "_prefab": {"__id__": 500}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 499}, "asset": {"__uuid__": "3416dcab-ee56-478f-b062-b1e826f7b707", "__expectedType__": "cc.Prefab"}, "fileId": "f5VJJTfHtB5aqbwW4jl16A", "instance": {"__id__": 501}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "adVdes0y5Cn7YVlczdMUGz", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 502}, {"__id__": 504}, {"__id__": 505}, {"__id__": 506}, {"__id__": 507}, {"__id__": 509}, {"__id__": 511}, {"__id__": 513}, {"__id__": 515}, {"__id__": 517}, {"__id__": 519}, {"__id__": 521}, {"__id__": 523}, {"__id__": 525}, {"__id__": 526}, {"__id__": 527}, {"__id__": 529}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 503}, "propertyPath": ["_name"], "value": "Item-004"}, {"__type__": "cc.TargetInfo", "localID": ["f5VJJTfHtB5aqbwW4jl16A"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 503}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 434.79999999999995, "y": -3.262, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 503}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 503}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 508}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["83uRFCZDpOWIYKQdKjv3ks"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 510}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["eeCRBl0e1DboDDLjZnVY/4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 512}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["a1tsAamAJGy7Lb6gW02dNW"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 514}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["3f2CI9oKNLFK7+fzc6STXj"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 516}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 92, "height": 92}}, {"__type__": "cc.TargetInfo", "localID": ["76eqiBuHdJiZAdj+7P63VD"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 518}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 92, "height": 92}}, {"__type__": "cc.TargetInfo", "localID": ["c3FKi/GdFIerw2SbrXGJ9M"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 520}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 79, "height": 79}}, {"__type__": "cc.TargetInfo", "localID": ["b8lliivRBLlLauY3fNUDkb"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 522}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 79, "height": 79}}, {"__type__": "cc.TargetInfo", "localID": ["7fVX9mvddJUaVADkQ2cBdp"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 524}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 80, "height": 41.8}}, {"__type__": "cc.TargetInfo", "localID": ["07IRwvCO9Nyq7noluFiXK4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 514}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 42, "y": -23.905, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 503}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 528}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 0.8070175438596491, "y": 0.8070175438596491, "z": 1}}, {"__type__": "cc.TargetInfo", "localID": ["05/dyc9JZPzqOEp1/yFyae"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 528}, "propertyPath": ["_layer"], "value": 1}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 375}, "_enabled": true, "__prefab": {"__id__": 531}, "_contentSize": {"__type__": "cc.Size", "width": 480.8, "height": 106.73}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "96dNiQoyRKjp30hc7Z7wZO"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 375}, "_enabled": true, "__prefab": {"__id__": 533}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 5.2, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "36LZjYCVJGS67yVDf94HCb"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 375}, "_enabled": true, "__prefab": {"__id__": 535}, "_alignFlags": 5, "_target": null, "_left": 0, "_right": 140, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "94FWHxLjpIuK05qz7s9QA6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5aCDMM7+1KiYQfgExJcU9E", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 374}, "_enabled": true, "__prefab": {"__id__": 538}, "_contentSize": {"__type__": "cc.Size", "width": 449.799, "height": 106.73}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "12FFjuyCdBM5Vzc+LKleED"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 374}, "_enabled": true, "__prefab": {"__id__": 540}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b4hwXnxG5I4Lr78dpRj6/i"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 374}, "_enabled": true, "__prefab": {"__id__": 542}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "20/Zf4em5HspPNCsgcgA3p"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 374}, "_enabled": true, "__prefab": {"__id__": 544}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 240, "_originalHeight": 250, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "148FRABchNL7SDqgRPiSQN"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "49U0louq1K/a5QNOj6ItaU", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 373}, "_enabled": true, "__prefab": {"__id__": 547}, "_contentSize": {"__type__": "cc.Size", "width": 449.799, "height": 106.73}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a4Cb+aICBKwpwIX1KQtc6j"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 373}, "_enabled": true, "__prefab": {"__id__": 549}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "83Y33RuoNPebmhL32mWAAy"}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 373}, "_enabled": true, "__prefab": {"__id__": 551}, "bounceDuration": 0.23, "brake": 0.75, "elastic": true, "inertia": true, "horizontal": true, "vertical": false, "cancelInnerEvents": true, "scrollEvents": [], "_content": {"__id__": 375}, "_horizontalScrollBar": null, "_verticalScrollBar": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "21O78zPrdJTKFIeh7mVuQE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2eBNHOoaFBfI3LfzKbDEdD", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 60}, "_children": [], "_active": true, "_components": [{"__id__": 554}, {"__id__": 556}, {"__id__": 558}], "_prefab": {"__id__": 560}, "_lpos": {"__type__": "cc.Vec3", "x": -142.726, "y": -128.24199999999996, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 553}, "_enabled": true, "__prefab": {"__id__": 555}, "_contentSize": {"__type__": "cc.Size", "width": 264, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "58bT4sc4VL0o7nJqJZABlv"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 553}, "_enabled": true, "__prefab": {"__id__": 557}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 89, "g": 136, "b": 188, "a": 255}, "_string": "以上奖励中随机获得一个", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "67knHnBMlOD7I1VJa8wSKH"}, {"__type__": "4b414FkqlpD0ac92djm25UF", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 553}, "_enabled": true, "__prefab": {"__id__": 559}, "_args": [], "_messageKey": 447, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "981QVvIaxOobWL8xsd92RL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "32DahIQmJJnL7opSetMLmV", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 60}, "_enabled": true, "__prefab": {"__id__": 562}, "_contentSize": {"__type__": "cc.Size", "width": 615, "height": 330}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d581i+FhZF/6iCIPjszSWH"}, {"__type__": "ec544cF2t9PO6BO3UJfxAHI", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 60}, "_enabled": true, "__prefab": {"__id__": 564}, "lblName": {"__id__": 95}, "bgProcess": {"__id__": 282}, "lblProcess": {"__id__": 288}, "lblPower": {"__id__": 265}, "itemFightCost": {"__id__": 107}, "itemLayout": {"__id__": 375}, "btnClaimable": {"__id__": 295}, "btnUnclaimable": {"__id__": 322}, "btnClaimed": {"__id__": 346}, "btnFight": {"__id__": 178}, "btnHelp": {"__id__": 115}, "btnHeader": {"__id__": 73}, "lblFightCd": {"__id__": 241}, "bgMonsterHeader": {"__id__": 82}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0f/sY8rVlHsJOXIFuWC8N9"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "deuIRNfz5CcbVNe9yFoY+y", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_tab", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 567}, {"__id__": 627}], "_active": false, "_components": [{"__id__": 687}], "_prefab": {"__id__": 689}, "_lpos": {"__type__": "cc.Vec3", "x": 220.796, "y": -618.39, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "node_tab1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 566}, "_children": [{"__id__": 568}, {"__id__": 586}, {"__id__": 604}], "_active": true, "_components": [{"__id__": 622}, {"__id__": 624}], "_prefab": {"__id__": 626}, "_lpos": {"__type__": "cc.Vec3", "x": -198.057, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "node_enable", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 567}, "_children": [{"__id__": 569}, {"__id__": 575}], "_active": false, "_components": [{"__id__": 583}], "_prefab": {"__id__": 585}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_yeqi<PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 568}, "_children": [], "_active": true, "_components": [{"__id__": 570}, {"__id__": 572}], "_prefab": {"__id__": 574}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 569}, "_enabled": true, "__prefab": {"__id__": 571}, "_contentSize": {"__type__": "cc.Size", "width": 191, "height": 79}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a9qkjD3ctEepd4IIi76ysL"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 569}, "_enabled": true, "__prefab": {"__id__": 573}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9da6cec5-5a17-43bd-b5b7-cc393f54ff00@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dbZIlo4SBGMYznA2gLpjaP"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b3QTNDQjlEaJcNg3rG+A+Z", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 568}, "_children": [], "_active": true, "_components": [{"__id__": 576}, {"__id__": 578}, {"__id__": 580}], "_prefab": {"__id__": 582}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 7.213, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 575}, "_enabled": true, "__prefab": {"__id__": 577}, "_contentSize": {"__type__": "cc.Size", "width": 74, "height": 56.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1ePd/oi9ZOP5sRm4p7N7j4"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 575}, "_enabled": true, "__prefab": {"__id__": 579}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "怪物", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 34, "_fontSize": 34, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 48, "g": 103, "b": 180, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3195ufyqpONrclTW36YwlH"}, {"__type__": "4b414FkqlpD0ac92djm25UF", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 575}, "_enabled": true, "__prefab": {"__id__": 581}, "_args": [], "_messageKey": 448, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c6uzTrG6NPSrszZjNfKFP3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "78w5kkCt9I6Z0kRCDFT32O", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 568}, "_enabled": true, "__prefab": {"__id__": 584}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "31uptMXTVM9KyW2ayVOMw5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ffSfBopolJ1Id9KjrPXKbu", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_disable", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 567}, "_children": [{"__id__": 587}, {"__id__": 593}], "_active": false, "_components": [{"__id__": 601}], "_prefab": {"__id__": 603}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_yeqi<PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 586}, "_children": [], "_active": true, "_components": [{"__id__": 588}, {"__id__": 590}], "_prefab": {"__id__": 592}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 587}, "_enabled": true, "__prefab": {"__id__": 589}, "_contentSize": {"__type__": "cc.Size", "width": 191, "height": 79}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d7w/o4L+dF/ptFXvbarV6m"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 587}, "_enabled": true, "__prefab": {"__id__": 591}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9da6cec5-5a17-43bd-b5b7-cc393f54ff00@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e9YcP4Yo1FjIIqOPO9ehsg"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f1FmNx18BM460liWYoITBE", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 586}, "_children": [], "_active": true, "_components": [{"__id__": 594}, {"__id__": 596}, {"__id__": 598}], "_prefab": {"__id__": 600}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 7.213, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 593}, "_enabled": true, "__prefab": {"__id__": 595}, "_contentSize": {"__type__": "cc.Size", "width": 74, "height": 56.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "563Q4aP7dDR5ZezQowDfLN"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 593}, "_enabled": true, "__prefab": {"__id__": 597}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "怪物", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 34, "_fontSize": 34, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "82b1GD1CRAmLS0RkADFu6S"}, {"__type__": "4b414FkqlpD0ac92djm25UF", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 593}, "_enabled": true, "__prefab": {"__id__": 599}, "_args": [], "_messageKey": 448, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e1EQrAI0BG9ZynLmHqdmdU"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a5gh2uDz9K6LDo1ZGJ353T", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 586}, "_enabled": true, "__prefab": {"__id__": 602}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1e8HMb7pND85/Jt4EZaPUt"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8dRE88JQJC+ZLLhsiEEnve", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_selected", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 567}, "_children": [{"__id__": 605}, {"__id__": 611}], "_active": true, "_components": [{"__id__": 619}], "_prefab": {"__id__": 621}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_yeqi<PERSON>_xuanzhong", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 604}, "_children": [], "_active": true, "_components": [{"__id__": 606}, {"__id__": 608}], "_prefab": {"__id__": 610}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 605}, "_enabled": true, "__prefab": {"__id__": 607}, "_contentSize": {"__type__": "cc.Size", "width": 191, "height": 79}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c6YfOR925DnqJkDpLAcagd"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 605}, "_enabled": true, "__prefab": {"__id__": 609}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5d4f6079-50dd-47ea-982e-ad20f3c3c525@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ddUiStifxE04oNjEdZ2bVr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "71sGnTmzVIaKMBt2Wj1nnE", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 604}, "_children": [], "_active": true, "_components": [{"__id__": 612}, {"__id__": 614}, {"__id__": 616}], "_prefab": {"__id__": 618}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 7.213, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 611}, "_enabled": true, "__prefab": {"__id__": 613}, "_contentSize": {"__type__": "cc.Size", "width": 74, "height": 56.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "benr8worRPd5iUnOIXqjcU"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 611}, "_enabled": true, "__prefab": {"__id__": 615}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "怪物", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 34, "_fontSize": 34, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 183, "g": 110, "b": 56, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f6fDpn3AtGr6G8+PAVkJjr"}, {"__type__": "4b414FkqlpD0ac92djm25UF", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 611}, "_enabled": true, "__prefab": {"__id__": 617}, "_args": [], "_messageKey": 448, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0d464KC0tOxI7i0SPzUx7g"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "19hPbggj5L35ftqbGHQaUK", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 604}, "_enabled": true, "__prefab": {"__id__": 620}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bekrqVI3BJjY+V7h77lDX/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "118uBD2K9G3p0Wb9/9Cn0O", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 567}, "_enabled": true, "__prefab": {"__id__": 623}, "_contentSize": {"__type__": "cc.Size", "width": 186.56799999999998, "height": 75.953}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "25A6tmYZRIUZturIMZiQR+"}, {"__type__": "59b1da6uDxL2r5VRR8PWhCO", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 567}, "_enabled": true, "__prefab": {"__id__": 625}, "enableNode": {"__id__": 568}, "disableNode": {"__id__": 586}, "selectedNode": {"__id__": 604}, "_messageKey": 448, "_args": [], "_selected": true, "_btnEnable": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6eGfvSJctP55dgCPrTUriX"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "28WJl7+7tH67OBd/HjUJB4", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_tab2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 566}, "_children": [{"__id__": 628}, {"__id__": 646}, {"__id__": 664}], "_active": true, "_components": [{"__id__": 682}, {"__id__": 684}], "_prefab": {"__id__": 686}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "node_enable", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 627}, "_children": [{"__id__": 629}, {"__id__": 635}], "_active": true, "_components": [{"__id__": 643}], "_prefab": {"__id__": 645}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_yeqi<PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 628}, "_children": [], "_active": true, "_components": [{"__id__": 630}, {"__id__": 632}], "_prefab": {"__id__": 634}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 629}, "_enabled": true, "__prefab": {"__id__": 631}, "_contentSize": {"__type__": "cc.Size", "width": 191, "height": 79}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c3wLi16EVPYrNR7TmupBc6"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 629}, "_enabled": true, "__prefab": {"__id__": 633}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9da6cec5-5a17-43bd-b5b7-cc393f54ff00@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "80DErFofJANJOj5PhTYGKC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "37IvPBY/FDKpxSLFcwfrk4", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 628}, "_children": [], "_active": true, "_components": [{"__id__": 636}, {"__id__": 638}, {"__id__": 640}], "_prefab": {"__id__": 642}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 7.213, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 635}, "_enabled": true, "__prefab": {"__id__": 637}, "_contentSize": {"__type__": "cc.Size", "width": 74, "height": 56.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "55xc0mtPZKU5Y0kVnLxNQl"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 635}, "_enabled": true, "__prefab": {"__id__": 639}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "角色", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 34, "_fontSize": 34, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 48, "g": 103, "b": 180, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dednheCJVI8YdPppOozviX"}, {"__type__": "4b414FkqlpD0ac92djm25UF", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 635}, "_enabled": true, "__prefab": {"__id__": 641}, "_args": [], "_messageKey": 449, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7f9+olPSVP2auYdDiGqqUV"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "beHiv5iB1Ko5TI4WDBxrMN", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 628}, "_enabled": true, "__prefab": {"__id__": 644}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7ag05PLn9FvKNZOVJbQ/aO"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c7zAERljdLp6QP/LgVPWAc", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_disable", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 627}, "_children": [{"__id__": 647}, {"__id__": 653}], "_active": false, "_components": [{"__id__": 661}], "_prefab": {"__id__": 663}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_yeqi<PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 646}, "_children": [], "_active": true, "_components": [{"__id__": 648}, {"__id__": 650}], "_prefab": {"__id__": 652}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 647}, "_enabled": true, "__prefab": {"__id__": 649}, "_contentSize": {"__type__": "cc.Size", "width": 191, "height": 79}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0axduzSvVJM4jjOIYkwDrA"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 647}, "_enabled": true, "__prefab": {"__id__": 651}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9da6cec5-5a17-43bd-b5b7-cc393f54ff00@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b0NpHMCPRGwLFi1dbC/FBh"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "775DjVOsRKipxQ3yBo/EsJ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 646}, "_children": [], "_active": true, "_components": [{"__id__": 654}, {"__id__": 656}, {"__id__": 658}], "_prefab": {"__id__": 660}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 7.213, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 653}, "_enabled": true, "__prefab": {"__id__": 655}, "_contentSize": {"__type__": "cc.Size", "width": 74, "height": 56.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b2D7/LUNZELbC0HMVrM8k8"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 653}, "_enabled": true, "__prefab": {"__id__": 657}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "角色", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 34, "_fontSize": 34, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "29k41fqwZDfqPKOKEAQqB3"}, {"__type__": "4b414FkqlpD0ac92djm25UF", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 653}, "_enabled": true, "__prefab": {"__id__": 659}, "_args": [], "_messageKey": 449, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eaCfLF3x9Dn648Mavp0xx2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "67/TuRskpJNJ3amKTo39Oi", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 646}, "_enabled": true, "__prefab": {"__id__": 662}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "59sL/19jFFVa2XGr0MHoGl"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0ec0w+NxJIebdghl9gBsbP", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_selected", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 627}, "_children": [{"__id__": 665}, {"__id__": 671}], "_active": false, "_components": [{"__id__": 679}], "_prefab": {"__id__": 681}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_yeqi<PERSON>_xuanzhong", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 664}, "_children": [], "_active": true, "_components": [{"__id__": 666}, {"__id__": 668}], "_prefab": {"__id__": 670}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 665}, "_enabled": true, "__prefab": {"__id__": 667}, "_contentSize": {"__type__": "cc.Size", "width": 191, "height": 79}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "81o0s/KvhMk6AX8iU+SEpS"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 665}, "_enabled": true, "__prefab": {"__id__": 669}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5d4f6079-50dd-47ea-982e-ad20f3c3c525@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2bGeH+MCxNYLaEuFco2wi5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0f+dGQPClP5aurnb3CyrVh", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 664}, "_children": [], "_active": true, "_components": [{"__id__": 672}, {"__id__": 674}, {"__id__": 676}], "_prefab": {"__id__": 678}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 7.213, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 671}, "_enabled": true, "__prefab": {"__id__": 673}, "_contentSize": {"__type__": "cc.Size", "width": 74, "height": 56.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5bmpIeWVtM/4Z6FArRK4T9"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 671}, "_enabled": true, "__prefab": {"__id__": 675}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "角色", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 34, "_fontSize": 34, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 183, "g": 110, "b": 56, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b3/TQ/TPdErrSqquGQrkkO"}, {"__type__": "4b414FkqlpD0ac92djm25UF", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 671}, "_enabled": true, "__prefab": {"__id__": 677}, "_args": [], "_messageKey": 449, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "04xYNxWcVFG4Q7ZH5aCeMP"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "70RhVuxpRHRJlZ9ewqYmVr", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 664}, "_enabled": true, "__prefab": {"__id__": 680}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9fcBtloYpHmKejei0tBqCd"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ecm3/Zz9pFMpPSK3DHhH8x", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 627}, "_enabled": true, "__prefab": {"__id__": 683}, "_contentSize": {"__type__": "cc.Size", "width": 186.56799999999998, "height": 75.953}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cdRJ1ZJ/BHaLjh9yktucae"}, {"__type__": "59b1da6uDxL2r5VRR8PWhCO", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 627}, "_enabled": true, "__prefab": {"__id__": 685}, "enableNode": {"__id__": 628}, "disableNode": {"__id__": 646}, "selectedNode": {"__id__": 664}, "_messageKey": 449, "_args": [], "_selected": false, "_btnEnable": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7chO7BBQpFT4m6W+Dp8ORJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "59lqYkIQdFf63KR/plYWEA", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 566}, "_enabled": true, "__prefab": {"__id__": 688}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ceOGsd2gdEq6Atb+NlJdTS"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "36iOKwkQtLh7PVO1nQ1fHI", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 691}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1167.363}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "147cyhK3tGMIi0vCsdsXOf"}, {"__type__": "ae31ejR75NGnYuLfaSBCqlV", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 693}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d8ZVPhpitPlpEjPCfRrYI3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": [{"__id__": 695}, {"__id__": 698}], "nestedPrefabInstanceRoots": [{"__id__": 499}, {"__id__": 468}, {"__id__": 437}, {"__id__": 406}, {"__id__": 376}, {"__id__": 241}, {"__id__": 107}, {"__id__": 73}, {"__id__": 2}]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 2}, "sourceInfo": {"__id__": 696}, "propertyPath": ["graphics"], "target": {"__id__": 2}, "targetInfo": {"__id__": 697}}, {"__type__": "cc.TargetInfo", "localID": ["f2ofLYMWBPU4DygV2KdYZ4"]}, {"__type__": "cc.TargetInfo", "localID": ["9ddIeszclP2KkZe+N/mFmL"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 2}, "sourceInfo": {"__id__": 699}, "propertyPath": ["dialogTitle"], "target": {"__id__": 2}, "targetInfo": {"__id__": 700}}, {"__type__": "cc.TargetInfo", "localID": ["f2ofLYMWBPU4DygV2KdYZ4"]}, {"__type__": "cc.TargetInfo", "localID": ["e9cwQVQARBJrTgOq5ECZIX"]}]