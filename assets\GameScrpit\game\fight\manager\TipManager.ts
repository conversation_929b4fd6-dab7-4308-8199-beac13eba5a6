import { Animation, Node, UIOpacity, UITransform, _decorator, instantiate, isValid, tween, v3 } from "cc";
import ManagerSection from "./ManagerSection";
import GameObject from "../../../lib/object/GameObject";
import FightManager, { scaleList } from "./FightManager";
import ResMgr from "../../../lib/common/ResMgr";
import { GORole } from "../role/GORole";
const { ccclass, property } = _decorator;

@ccclass("TipManager")
export class TipManager extends ManagerSection {
  public static sectionName(): string {
    return "TipManager";
  }
  public onInit(go: GameObject, args) {
    super.onInit(go, args);
    this.createRoot("TipRoot", FightManager.instance);
    this.root.getComponent(UITransform).setContentSize(FightManager.instance.getComponent(UITransform).contentSize);
    this.ready();
  }

  public onStart(): void {
    super.onStart();
  }

  public callTip(path: string, target: GORole) {
    if (FightManager.instance.fightOver == true) {
      return;
    }
    ResMgr.loadPrefab(path, (prefab) => {
      if (isValid(this.root) == false) {
        return;
      }
      let node = instantiate(prefab);
      // node.layer = Layers.Enum["UILayer"];

      node.walk((child: Node) => (child.layer = target.layer));
      let actName = this.setParent(node, target);

      node.addComponent(UIOpacity).opacity = 0;

      // 假设你已经有一个动画实例，这里命名为animation
      let animation: Animation = node.getComponent(Animation);

      animation.play(actName);
      let defaultClip = animation.defaultClip;
      defaultClip.speed = scaleList[FightManager.instance.speed];
      // 监听动画结束事件
      animation.on(
        Animation.EventType.FINISHED,
        function () {
          // 动画播放结束时的处理逻辑
          node.removeFromParent();
          node.destroy();
        },
        this
      );
    });
  }

  private setParent(node: Node, target: GORole) {
    this.root.addChild(node);
    let actName = "hintAni";
    let labNode = node;
    switch (node.name) {
      case "hint_lab_stun":
        target.getStunPos(labNode);
        break;
      case "hint_lab_miss":
        target.getMissPos(labNode);
        break;
      case "hint_lab_double":
        target.getDoublePos(labNode);
        break;
      case "hint_lab_suck":
        target.getSuckPos(labNode);
        break;
      case "hint_lab_backpunch":
        target.getBackPos(labNode);
        break;
    }
    return actName;
  }
}
