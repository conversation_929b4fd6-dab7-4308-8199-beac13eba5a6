import { _decorator, Color, isValid, Label, Sprite } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { JsonMgr } from "../../mgr/JsonMgr";
import ToolExt from "../../common/ToolExt";
import { HuntModule } from "../../../module/hunt/HuntModule";
import ResMgr from "../../../lib/common/ResMgr";
import FmUtils from "../../../lib/utils/FmUtils";
import Formate from "../../../lib/utils/Formate";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { Sleep } from "../../GameDefine";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { HuntAudioName } from "../../../module/hunt/HuntConfig";
import { FmButton } from "../../../../platform/src/core/ui/components/FmButton";

const { ccclass, property } = _decorator;

enum Main_Enum {
  AWARD = 1,
  RANK = 2,
}

@ccclass("UIHuntBossRank")
export class UIHuntBossRank extends UINode {
  protected _openAct: boolean = true; //打开动作
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_HUNT}?prefab/ui/UIHuntBossRank`;
  }

  /**0是奖励,1是排行榜 */
  private _curIndexMain: Main_Enum = Main_Enum.AWARD;

  private _initRank: boolean = false;

  protected onEvtShow(): void {
    this.changeMain();
    HuntModule.api.bossRank((res) => {
      this.getNode("main").active = true;
      this.loadRepel();
      this.loadRankAward();
      this.setMyRankInfo();
    });

    let tenTwoTime = this.getMillisecondsToNextMidnight();
    FmUtils.setCd(this.getNode("LblCd"), tenTwoTime);
  }

  getMillisecondsToNextMidnight() {
    const now = new Date();
    const nextMidnight = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1, 0, 0, 0, 0);
    const nextMidnightMilliseconds = nextMidnight.getTime();
    return nextMidnightMilliseconds;
  }

  private loadRepel() {
    let c_huntBossRank = JsonMgr.instance.jsonList.c_huntBossRank;
    let list = Object.keys(c_huntBossRank);
    let info = c_huntBossRank[list[0]];

    let node = ToolExt.clone(this.getNode("awardMessage"), this);
    this.getNode("awardContent").addChild(node);
    node.active = true;

    node["lab_awardRank"].getComponent(Label).string = "击退奖";

    let rewardList = info.reward2List;
    for (let i = 0; i < rewardList.length; i++) {
      let item = ToolExt.clone(this.getNode("btn_item"), this);
      node["lay_award"].addChild(item);
      item.active = true;

      let itemInId = rewardList[i][0];
      let num = rewardList[i][1];
      FmUtils.setItemNode(item, itemInId, num);
    }
  }

  private loadRankAward() {
    let list = this.getAwardList();

    for (let i = 0; i < list.length; i++) {
      let node = ToolExt.clone(this.getNode("awardMessage"), this);
      this.getNode("awardContent").addChild(node);
      node.active = true;
      //node["spr_2"].getComponent(Sprite).color = new Color().fromHEX("#fff3b0");
      //node["lab_awardRank"].getComponent(Label).color = new Color().fromHEX("#fff3b0");

      node["lab_awardRank"].getComponent(Label).string = "第" + list[i].rank + "名";

      let rewardList = list[i].reward1List;
      for (let i = 0; i < rewardList.length; i++) {
        let item = ToolExt.clone(this.getNode("btn_item"), this);
        node["lay_award"].addChild(item);
        item.active = true;

        let itemInId = rewardList[i][0];
        let num = rewardList[i][1];
        FmUtils.setItemNode(item, itemInId, num);
      }
    }
  }

  private setMyRankInfo() {
    if (HuntModule.data.bossRank.rank == -1) {
      this.getNode("lab_myRank2").getComponent(Label).string = "未上榜";
      this.getNode("lab_myRank2").getComponent(Label).color = new Color().fromHEX("#ce2424");
    } else {
      this.getNode("lab_myRank2").getComponent(Label).string = String(HuntModule.data.bossRank.rank);
      this.getNode("lab_myRank2").getComponent(Label).color = new Color().fromHEX("#3973ae");
    }

    this.getNode("lab_myHurt2").getComponent(Label).string = Formate.format(HuntModule.data.bossRank.point);
  }

  private async loadRankList() {
    let rankList = HuntModule.data.bossRank.rankList;
    if (rankList.length <= 0) {
      this.getNode("rankScroll").getChildByName("bg").active = true;
      return;
    }
    this.getNode("rankScroll").getChildByName("bg").active = false;

    for (let i = 0; i < rankList.length; i++) {
      await Sleep(0.01);
      if (isValid(this.node) == false) return;
      let node = ToolExt.clone(this.getNode("rankMessage"), this);
      this.getNode("rankContent").addChild(node);
      node.active = true;

      let playerSimpleMessage = rankList[i].playerSimpleMessage;

      node["lab_Name"].getComponent(Label).string = playerSimpleMessage.nickname;

      node["lab_level"].getComponent(Label).string = PlayerModule.data.getConfigLeaderData(
        playerSimpleMessage.level
      ).name;

      node["lab_score"].getComponent(Label).string = Formate.format(rankList[i].point);
      FmUtils.setHeaderNode(node["btn_header"], playerSimpleMessage);

      if (i < 3) {
        node["spr_rank"].active = true;
        node["spr_lab_rank_bg"].active = false;

        ResMgr.loadImage(
          `${BundleEnum.BUNDLE_COMMON_UI}?atlas_imgs/YWC_icon_paiming${i + 1}`,
          node["spr_rank"].getComponent(Sprite),
          this
        );
      } else {
        node["spr_rank"].active = false;
        node["spr_lab_rank_bg"].active = true;
        node["lab_rank"].getComponent(Label).string = String(i + 1);
      }
    }
  }

  private on_click_btn_award() {
    AudioMgr.instance.playEffect(HuntAudioName.Effect.点击奖励里的页签);
    if (this._curIndexMain == Main_Enum.AWARD) {
      return;
    }
    this._curIndexMain = Main_Enum.AWARD;

    this.changeMain();
  }

  private on_click_btn_rank() {
    AudioMgr.instance.playEffect(HuntAudioName.Effect.点击奖励里的页签);
    if (this._curIndexMain == Main_Enum.RANK) {
      return;
    }
    this._curIndexMain = Main_Enum.RANK;

    if (this._initRank == false) {
      this._initRank = true;
      this.loadRankList();
    }

    this.changeMain();
  }

  private changeMain() {
    if (this._curIndexMain == Main_Enum.AWARD) {
      this.getNode("awardScroll").active = true;
      this.getNode("awardScroll_time").active = true;
      this.getNode("rankScroll").active = false;
      this.getNode("btn_rank").getComponent(FmButton).selected = false;
      this.getNode("btn_award").getComponent(FmButton).selected = true;
      return;
    }
    if (this._curIndexMain == Main_Enum.RANK) {
      this.getNode("awardScroll").active = false;
      this.getNode("awardScroll_time").active = false;
      this.getNode("rankScroll").active = true;

      this.getNode("btn_rank").getComponent(FmButton).selected = true;
      this.getNode("btn_award").getComponent(FmButton).selected = false;
      return;
    }
  }
  private on_click_btn_close() {
    // 关闭窗口 effect_guanbiyinxiao
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  private getAwardList() {
    let c_huntBossRank = JsonMgr.instance.jsonList.c_huntBossRank;
    let arr = [];
    let list = Object.keys(c_huntBossRank);
    for (let i = 0; i < list.length; i++) {
      if (list[i] == "101") {
        let obj = {
          rank: String(1) + "-" + c_huntBossRank[list[i]].order,
          reward1List: c_huntBossRank[list[i]].reward1List,
        };
        arr.push(obj);
      } else {
        let lastInfo = c_huntBossRank[list[i - 1]];
        let obj = {
          rank: lastInfo.order + 1 + "-" + c_huntBossRank[list[i]].order,
          reward1List: c_huntBossRank[list[i]].reward1List,
        };
        arr.push(obj);
      }
    }
    return arr;
  }

  public tick(dt: any): void {}
}
