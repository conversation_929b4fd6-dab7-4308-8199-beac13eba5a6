import { Node, Vec3 } from "cc";

export enum UIState {
  GO_RUN, // 前往冲击
  UPGRADE, // 升级主角
}
type UIStateCallback = (state: UIState) => void;
export interface IUIModule {
  /**
   * 显示UI
   */
  onShowUI(isShow: boolean);

  /**
   * 显示女娲
   */
  onShowNvwa(isShow: boolean);

  /**
   * 显示隐藏冲
   */
  onShowGo(isShow: boolean);

  /**
   * 显示隐藏任务
   */
  onShowTask(isShow: boolean);

  /**
   *
   * @param callback 状态变更的时候对接口进行回调
   */
  onStart(callback: UIStateCallback, nodePlayer: Node): void;

  // 显示手指
  showFinger(to: Vec3, from: number): void;
}
