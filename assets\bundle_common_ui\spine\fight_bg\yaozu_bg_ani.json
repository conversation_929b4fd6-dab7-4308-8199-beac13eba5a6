{"skeleton": {"hash": "y5KdpzdKSeyKk02zj+imLV30+BI=", "spine": "3.8.75", "x": -376, "y": -473.31, "width": 752, "height": 1040.31, "images": "./images/", "audio": "D:/spine导出/G_关卡场景动画/妖族场景"}, "bones": [{"name": "root"}, {"name": "all", "parent": "root"}, {"name": "all2", "parent": "all", "length": 709.34, "rotation": -4.24, "x": 1483.21, "y": -500.62}, {"name": "bone", "parent": "all2", "length": 51.94, "rotation": 95.19, "x": 858.76, "y": 15.81}, {"name": "bone2", "parent": "bone", "length": 51.94, "x": 51.94}, {"name": "bone3", "parent": "bone2", "length": 51.94, "x": 51.94}, {"name": "bone4", "parent": "bone3", "length": 51.94, "x": 51.94}, {"name": "bone5", "parent": "bone4", "length": 51.94, "x": 51.94}, {"name": "bone6", "parent": "bone5", "length": 51.94, "x": 51.94}, {"name": "bone7", "parent": "bone6", "length": 51.94, "x": 51.94}, {"name": "bone8", "parent": "bone7", "length": 51.94, "x": 51.94}, {"name": "bone9", "parent": "bone8", "length": 51.94, "x": 51.94}, {"name": "bone10", "parent": "bone9", "length": 51.94, "x": 51.94}, {"name": "bone11", "parent": "bone10", "length": 51.94, "x": 51.94}, {"name": "bone12", "parent": "bone11", "length": 51.94, "x": 51.94}, {"name": "bone13", "parent": "bone12", "length": 51.94, "x": 51.94}, {"name": "bone14", "parent": "bone13", "length": 51.94, "x": 51.94}, {"name": "bone15", "parent": "bone14", "length": 51.94, "x": 51.94}, {"name": "bone16", "parent": "bone15", "length": 51.94, "x": 51.94}, {"name": "bone17", "parent": "bone16", "length": 51.94, "x": 51.94}, {"name": "bone18", "parent": "bone17", "length": 51.94, "x": 51.94}, {"name": "bone19", "parent": "bone18", "length": 51.94, "x": 51.94}, {"name": "bone20", "parent": "bone19", "length": 51.94, "x": 51.94}, {"name": "lie_light", "parent": "all", "length": 216.99, "rotation": 0.73, "x": 386.62, "y": -299.85}, {"name": "shitouren", "parent": "all", "length": 74.98, "rotation": -0.94, "x": -194.8, "y": 26.92}, {"name": "all3", "parent": "shitouren", "length": 62.59, "rotation": -0.75, "x": 6.22, "y": 52.99, "color": "abe323ff"}, {"name": "all4", "parent": "all3", "length": 45.23, "rotation": 87.07, "x": -10.85, "y": -2.85}, {"name": "all5", "parent": "all4", "x": 23.28, "y": -6.55, "color": "abe323ff"}, {"name": "all6", "parent": "all4", "x": 58.05, "y": -5.71}, {"name": "all7", "parent": "all3", "length": 24.36, "rotation": -50.79, "x": 5.76, "y": -5.44}, {"name": "all8", "parent": "all7", "length": 23.79, "rotation": -85.39, "x": 24.36}, {"name": "all9", "parent": "all8", "length": 21.97, "rotation": 126.84, "x": 23.79}, {"name": "all10", "parent": "all3", "length": 21.04, "rotation": -115.22, "x": -32.56, "y": -7.69}, {"name": "all11", "parent": "all10", "length": 20.47, "rotation": 57.87, "x": 20.61, "y": -0.36}, {"name": "all12", "parent": "all11", "length": 16.38, "rotation": -112.43, "x": 20.47}, {"name": "all21", "parent": "all3", "x": -58.34, "y": 53.46}, {"name": "all13", "parent": "all21", "length": 38, "rotation": -122.74, "x": -3.13, "y": -2.7}, {"name": "all14", "parent": "all13", "length": 43.21, "rotation": 33.27, "x": 36.46, "y": -0.41}, {"name": "all15", "parent": "all14", "length": 22.66, "rotation": 16.35, "x": 41.18, "y": -0.35}, {"name": "all16", "parent": "all3", "x": -16.01, "y": 112.06}, {"name": "all20", "parent": "all3", "x": 25.83, "y": 58.38}, {"name": "all17", "parent": "all20", "length": 38.25, "rotation": -57.77, "x": 2.25, "y": -3.37}, {"name": "all18", "parent": "all17", "length": 37.87, "rotation": -18.2, "x": 38.25}, {"name": "all19", "parent": "all18", "length": 22.95, "rotation": -11.68, "x": 37.81, "y": -0.26}, {"name": "target1", "parent": "shitouren", "rotation": 0.94, "x": -25.48, "y": 10.7, "color": "ff3f00ff"}, {"name": "target2", "parent": "target1", "x": -16.94, "y": -2.36, "color": "ff3f00ff"}, {"name": "target3", "parent": "shitouren", "rotation": 0.94, "x": 9.16, "y": 12.45, "color": "ff3f00ff"}, {"name": "target4", "parent": "target3", "x": 21.47, "y": -4.73, "color": "ff3f00ff"}, {"name": "all22", "parent": "all", "x": -239.19, "y": 217.03}, {"name": "all23", "parent": "all", "x": -239.19, "y": 217.03}, {"name": "all24", "parent": "all", "x": -18.5, "y": 130.32}, {"name": "all25", "parent": "all", "x": -18.5, "y": 130.32}, {"name": "all26", "parent": "all", "x": 182.01, "y": 146.78}, {"name": "all27", "parent": "all", "x": 182.01, "y": 146.78}, {"name": "all28", "parent": "all", "x": 52.63, "y": 100.54}, {"name": "all29", "parent": "all", "x": 52.63, "y": 100.54}], "slots": [{"name": "all", "bone": "all", "attachment": "all"}, {"name": "yaoz_bg_01", "bone": "all", "attachment": "yaoz_bg_01"}, {"name": "yaoz_bg_02", "bone": "all28", "attachment": "yaoz_bg_02"}, {"name": "yaoz_bg_2", "bone": "all29", "attachment": "yaoz_bg_02"}, {"name": "yaoz_bg_03", "bone": "all26", "attachment": "yaoz_bg_03"}, {"name": "yaoz_bg_3", "bone": "all27", "attachment": "yaoz_bg_03"}, {"name": "yaoz_bg_04", "bone": "all24", "attachment": "yaoz_bg_04"}, {"name": "yaoz_bg_4", "bone": "all25", "attachment": "yaoz_bg_04"}, {"name": "yaoz_bg_05", "bone": "all22", "attachment": "yaoz_bg_05"}, {"name": "yaoz_bg_5", "bone": "all23", "attachment": "yaoz_bg_05"}, {"name": "yaoz_bg_06", "bone": "all", "attachment": "yaoz_bg_06"}, {"name": "yaoz_bg_07", "bone": "all", "attachment": "yaoz_bg_07"}, {"name": "yaoz_bg_08", "bone": "lie_light", "attachment": "yaoz_bg_08"}, {"name": "yaoz_bg_09", "bone": "all16", "dark": "000000", "attachment": "yaoz_bg_09"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "shitouren", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "yaoz_bg_010", "bone": "all", "dark": "000000", "attachment": "yaoz_bg_010"}, {"name": "yaoz_bg_011", "bone": "all", "dark": "000000", "attachment": "yaoz_bg_011"}, {"name": "yaoz_bg_012", "bone": "all17", "dark": "000000", "attachment": "yaoz_bg_012"}, {"name": "yaoz_bg_013", "bone": "all", "dark": "000000", "attachment": "yaoz_bg_013"}, {"name": "yaoz_bg_014", "bone": "all", "dark": "000000", "attachment": "yaoz_bg_014"}, {"name": "yaoz_bg_015", "bone": "all13", "dark": "000000", "attachment": "yaoz_bg_015"}, {"name": "yaoz_bg_016", "bone": "all", "dark": "000000", "attachment": "yaoz_bg_016"}, {"name": "kunyao", "bone": "all2"}], "ik": [{"name": "target1", "bones": ["all10", "all11"], "target": "target1"}, {"name": "target2", "order": 3, "bones": ["all11", "all12"], "target": "target2", "bendPositive": false}, {"name": "target3", "order": 1, "bones": ["all7", "all8"], "target": "target3", "bendPositive": false}, {"name": "target4", "order": 2, "bones": ["all8", "all9"], "target": "target4"}], "transform": [{"name": "shiface", "order": 5, "bones": ["all6"], "target": "all5", "x": 34.77, "y": 0.84, "rotateMix": -1, "translateMix": -1, "scaleMix": -1, "shearMix": -1}, {"name": "shou1", "order": 6, "bones": ["all20"], "target": "all5", "rotation": -87.07, "x": 39.74, "y": -26.95, "rotateMix": -0.3, "translateMix": -0.3, "scaleMix": -0.3, "shearMix": -0.3}, {"name": "shou2", "order": 7, "bones": ["all21"], "target": "all5", "rotation": -87.07, "x": 30.52, "y": 56.86, "rotateMix": -0.3, "translateMix": -0.3, "scaleMix": -0.3, "shearMix": -0.3}], "path": [{"name": "kunyao", "order": 4, "bones": ["bone", "bone2", "bone3", "bone4", "bone5", "bone6", "bone7", "bone8", "bone9", "bone10", "bone11", "bone12", "bone13", "bone14", "bone15", "bone16", "bone17", "bone18", "bone19", "bone20"], "target": "kunyao", "position": 0.2425}], "skins": [{"name": "default", "attachments": {"yaoz_bg_2": {"yaoz_bg_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [154.37, -25.54, -2.63, -25.54, -2.63, 7.46, 154.37, 7.46], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 157, "height": 33}}, "yaoz_bg_3": {"yaoz_bg_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [193.99, -28.78, -2.01, -28.78, -2.01, 81.22, 193.99, 81.22], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 196, "height": 110}}, "yaoz_bg_4": {"yaoz_bg_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [5.5, -40.32, -237.5, -40.32, -237.5, 40.68, 5.5, 40.68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 243, "height": 81}}, "yaoz_bg_5": {"yaoz_bg_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [5.19, -49.03, -136.81, -49.03, -136.81, 47.97, 5.19, 47.97], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 142, "height": 97}}, "kunyao": {"kunyao": {"type": "path", "lengths": [1811.19, 2255.02, 2573.88, 2998.88, 4540.38, 9479.95], "vertexCount": 18, "vertices": [1448.92, 752.26, 1036.96, 650.5, 613.98, 546.03, -474.87, 484.96, -764.12, 475.21, -1053.37, 465.45, -1016.84, 509.44, -1206.29, 502.17, -1385.17, 495.29, -1394.86, 423.27, -1512.02, 420.33, -1726.23, 414.95, -1695.99, 336.11, -1921.52, 317.6, -2147.04, 299.09, -3246.22, 185.21, -3453.72, 149.52, -3677.37, 111.05]}}, "all": {"all": {"type": "clipping", "end": "all", "vertexCount": 4, "vertices": [-375.97, 567.49, -376.5, -473.31, 376.08, -473.29, 376.08, 566.64], "color": "ce3a3aff"}}, "yaoz_bg_011": {"yaoz_bg_011": {"type": "mesh", "uvs": [0.73145, 0.08543, 0.99009, 0.44708, 0.9466, 0.60651, 0.93411, 0.6523, 0.91999, 0.70406, 0.84307, 0.98599, 0.49312, 0.98716, 0.33611, 0.98769, 0.12676, 0.88174, 0.07937, 0.73677, 0.068, 0.70199, 0.0538, 0.65852, 0.01117, 0.52811, 0.17227, 0.07539, 0.47632, 0.00822, 0.5496, 0.60094], "triangles": [11, 12, 15, 4, 15, 3, 3, 15, 2, 2, 15, 1, 12, 13, 15, 13, 14, 15, 15, 0, 1, 15, 14, 0, 10, 11, 15, 9, 10, 15, 6, 7, 15, 4, 5, 6, 15, 7, 8, 4, 6, 15, 8, 9, 15], "vertices": [1, 42, 2.67, 16.43, 1, 1, 42, 30.63, 24.08, 1, 1, 42, 41.21, 19.45, 1, 2, 43, 2.58, 19.3, 0.10632, 42, 44.24, 18.12, 0.89368, 2, 43, 6.25, 18.53, 0.91708, 42, 47.68, 16.62, 0.08292, 2, 43, 26.22, 14.29, 0.92457, 42, 66.37, 8.43, 0.07543, 1, 43, 26.08, -3.9, 1, 1, 43, 26.03, -12.07, 1, 1, 43, 18.38, -22.87, 1, 2, 43, 8.05, -25.21, 0.96583, 42, 40.59, -26.58, 0.03417, 2, 43, 5.58, -25.77, 0.09595, 42, 38.06, -26.63, 0.90405, 1, 42, 34.88, -26.69, 1, 1, 42, 25.36, -26.88, 1, 2, 43, -38.84, -19.83, 0.00725, 42, -4.24, -11.82, 0.99275, 1, 42, -5.52, 4.65, 1, 1, 42, 36.4, -0.63, 1], "hull": 15, "edges": [0, 28, 0, 2, 14, 16, 24, 26, 26, 28, 28, 30, 10, 12, 12, 14, 30, 12, 30, 20, 30, 6, 20, 22, 22, 24, 22, 30, 2, 4, 4, 6, 30, 4, 6, 8, 8, 10, 30, 8, 16, 18, 18, 20, 30, 18], "width": 52, "height": 71}}, "yaoz_bg_012": {"yaoz_bg_012": {"type": "mesh", "uvs": [0.38437, 0, 0.93822, 0.28096, 0.97944, 0.81722, 0.7935, 0.95569, 0.50069, 0.99569, 0.02437, 0.52148, 0.02173, 0.18143, 0.20672, 0], "triangles": [0, 5, 6, 0, 1, 5, 2, 4, 1, 2, 3, 4, 0, 6, 7, 1, 4, 5], "vertices": [-9.14, 12.12, 15.71, 23.82, 41.97, 10.28, 44.65, -0.15, 40.45, -11.61, 8.06, -15.17, -8.1, -5.76, -12.84, 5.84], "hull": 8, "edges": [0, 14, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14], "width": 41, "height": 55}}, "yaoz_bg_013": {"yaoz_bg_013": {"type": "mesh", "uvs": [0.65284, 0.03032, 0.79025, 0.09406, 0.95693, 0.28448, 0.99717, 0.46505, 0.97486, 0.68889, 0.92934, 0.78508, 0.79419, 0.89978, 0.56349, 0.9913, 0.46196, 0.99217, 0.32282, 0.95831, 0.18205, 0.88854, 0.08088, 0.80199, 0.04308, 0.73874, 0.0019, 0.55982, 0.04217, 0.32647, 0.1528, 0.1652, 0.32765, 0.05272, 0.51494, 0, 0.54578, 0.12053, 0.71369, 0.1556, 0.84702, 0.30431, 0.87732, 0.45043, 0.77884, 0.57586, 0.59399, 0.61982, 0.40157, 0.58232, 0.28338, 0.46207, 0.2652, 0.30172, 0.38338, 0.16465, 0.60005, 0.79181, 0.33187, 0.76077, 0.79702, 0.76077, 0.16369, 0.60431, 0.11369, 0.38448, 0.51768, 0.17426, 0.55206, 0.5736, 0.71351, 0.53532, 0.81218, 0.43708, 0.79125, 0.29801, 0.66717, 0.19212, 0.37566, 0.22912, 0.30091, 0.32736, 0.31885, 0.45367, 0.41602, 0.53915, 0.53473, 0.37235, 0.19903, 0.2134, 0.52803, 0.29444, 0.61499, 0.32896, 0.66816, 0.3873, 0.65867, 0.46104, 0.54749, 0.5205, 0.43744, 0.49427, 0.37382, 0.43106, 0.38142, 0.35489, 0.44693, 0.31761, 0.90773, 0.60764, 0.89091, 0.72767], "triangles": [7, 8, 28, 29, 28, 8, 29, 8, 9, 7, 28, 6, 23, 29, 24, 29, 23, 28, 9, 10, 29, 6, 30, 5, 30, 55, 5, 6, 28, 30, 10, 11, 29, 29, 11, 31, 28, 23, 30, 5, 55, 4, 23, 22, 30, 30, 22, 55, 11, 12, 31, 29, 31, 24, 12, 13, 31, 55, 54, 4, 55, 22, 54, 4, 54, 3, 24, 34, 23, 23, 35, 22, 23, 34, 35, 22, 21, 54, 54, 21, 3, 13, 32, 31, 31, 25, 24, 31, 32, 25, 24, 25, 42, 24, 42, 34, 42, 25, 41, 22, 36, 21, 22, 35, 36, 34, 42, 49, 42, 50, 49, 35, 49, 48, 35, 34, 49, 13, 14, 32, 42, 41, 50, 50, 41, 51, 35, 48, 36, 48, 49, 43, 50, 51, 43, 49, 50, 43, 43, 52, 53, 52, 43, 51, 21, 2, 3, 40, 25, 26, 25, 40, 41, 26, 25, 32, 48, 43, 47, 48, 47, 36, 47, 43, 46, 41, 40, 51, 36, 20, 21, 21, 20, 2, 47, 37, 36, 36, 37, 20, 51, 40, 52, 47, 46, 37, 26, 32, 44, 53, 45, 43, 43, 45, 46, 53, 52, 39, 46, 38, 37, 46, 45, 38, 39, 52, 40, 32, 14, 44, 14, 15, 44, 40, 26, 39, 53, 39, 45, 2, 20, 1, 39, 26, 27, 38, 19, 37, 19, 1, 20, 39, 33, 45, 45, 33, 38, 20, 37, 19, 39, 27, 33, 27, 26, 44, 27, 44, 16, 33, 18, 38, 38, 18, 19, 33, 27, 18, 44, 15, 16, 27, 16, 18, 18, 0, 19, 19, 0, 1, 16, 17, 18, 18, 17, 0], "vertices": [3, 26, 96.96, -5.48, 0.56, 27, 73.68, 1.07, 0.14, 28, 38.92, 0.23, 0.3, 2, 26, 90.69, -19.64, 0.7, 28, 32.64, -13.93, 0.3, 2, 26, 70, -37.86, 0.7, 28, 11.95, -32.16, 0.3, 2, 26, 49.44, -43.52, 0.7, 28, -8.61, -37.81, 0.3, 2, 26, 23.38, -43.41, 0.7, 28, -34.67, -37.7, 0.3, 3, 26, 11.9, -39.81, 0.63, 27, -11.39, -33.27, 0.07, 28, -46.15, -34.11, 0.3, 3, 26, -2.44, -27.55, 0.56, 27, -25.72, -21, 0.14, 28, -60.49, -21.84, 0.3, 3, 26, -14.86, -5.64, 0.49, 27, -38.14, 0.91, 0.21, 28, -72.91, 0.07, 0.3, 3, 26, -15.77, 4.37, 0.525, 27, -39.05, 10.92, 0.175, 28, -73.82, 10.08, 0.3, 3, 26, -12.96, 18.42, 0.56, 27, -36.25, 24.97, 0.14, 28, -71.01, 24.13, 0.3, 3, 26, -6.02, 32.96, 0.595, 27, -29.3, 39.51, 0.105, 28, -64.07, 38.67, 0.3, 3, 26, 3.18, 43.75, 0.63, 27, -20.1, 50.3, 0.07, 28, -54.87, 49.46, 0.3, 2, 26, 10.19, 48.07, 0.7, 28, -47.85, 53.78, 0.3, 2, 26, 30.55, 53.81, 0.7, 28, -27.49, 59.52, 0.3, 2, 26, 57.85, 52.01, 0.7, 28, -0.19, 57.72, 0.3, 2, 26, 77.38, 42.6, 0.7, 28, 19.34, 48.31, 0.3, 3, 26, 91.78, 26.4, 0.56, 27, 68.5, 32.95, 0.14, 28, 33.73, 32.1, 0.3, 3, 26, 99.37, 8.41, 0.49, 27, 76.09, 14.96, 0.21, 28, 41.32, 14.12, 0.3, 2, 26, 85.68, 4.24, 0.7, 27, 62.4, 10.79, 0.3, 2, 26, 82.96, -12.66, 0.75, 27, 59.68, -6.11, 0.25, 2, 26, 66.83, -27.2, 0.75, 27, 43.55, -20.65, 0.25, 2, 26, 50.18, -31.56, 0.75, 27, 26.89, -25.01, 0.25, 2, 26, 34.89, -23.01, 0.75, 27, 11.61, -16.46, 0.25, 2, 26, 28.33, -5.18, 0.7, 27, 5.05, 1.37, 0.3, 2, 26, 31.14, 14.16, 0.7, 27, 7.85, 20.71, 0.3, 2, 26, 44.1, 26.94, 0.7, 27, 20.82, 33.49, 0.3, 2, 26, 62.49, 30.24, 0.7, 27, 39.21, 36.78, 0.3, 2, 26, 79.28, 19.85, 0.7, 27, 56, 26.4, 0.3, 2, 26, 8.5, -7.38, 0.7, 27, -14.79, -0.83, 0.3, 2, 26, 9.95, 19.37, 0.8, 27, -13.33, 25.92, 0.2, 2, 26, 13.65, -26.53, 0.8, 27, -9.63, -19.98, 0.2, 2, 26, 26.7, 37.43, 0.88, 27, 3.42, 43.98, 0.12, 2, 26, 51.72, 44.41, 0.88, 27, 28.44, 50.96, 0.12, 2, 26, 79.24, 6.51, 0.9, 27, 55.96, 13.06, 0.1, 2, 26, 33.34, -0.61, 0.9, 27, 10.06, 5.94, 0.1, 2, 26, 39.06, -16.18, 0.9, 27, 15.77, -9.64, 0.1, 2, 26, 51.2, -25, 0.9, 27, 27.92, -18.46, 0.1, 2, 26, 67.11, -21.64, 0.9, 27, 43.83, -15.09, 0.1, 2, 26, 78.37, -8.41, 0.9, 27, 55.09, -1.86, 0.1, 2, 26, 71.77, 20.01, 0.9, 27, 48.49, 26.56, 0.1, 2, 26, 59.81, 26.47, 0.9, 27, 36.53, 33.02, 0.1, 2, 26, 45.35, 23.52, 0.9, 27, 22.07, 30.07, 0.1, 2, 26, 36.24, 13.14, 0.9, 27, 12.96, 19.68, 0.1, 1, 26, 56.47, 2.98, 1, 2, 26, 72.18, 37.59, 0.88, 27, 48.9, 44.14, 0.12, 2, 26, 65.43, 4.37, 0.95, 27, 42.15, 10.92, 0.05, 2, 26, 62.13, -4.54, 0.95, 27, 38.85, 2.01, 0.05, 2, 26, 55.81, -10.33, 0.95, 27, 32.53, -3.78, 0.05, 2, 26, 47.21, -10.08, 0.95, 27, 23.92, -3.53, 0.05, 2, 26, 39.45, 0.34, 0.95, 27, 16.16, 6.89, 0.05, 2, 26, 41.6, 11.44, 0.95, 27, 18.32, 17.99, 0.05, 2, 26, 48.4, 18.31, 0.95, 27, 25.12, 24.86, 0.05, 2, 26, 57.27, 18.27, 0.95, 27, 33.99, 24.82, 0.05, 2, 26, 62.1, 12.15, 0.95, 27, 38.82, 18.7, 0.05, 2, 26, 32.24, -36.02, 0.88784, 27, 8.96, -29.48, 0.11216, 2, 26, 18.23, -35.48, 0.88578, 27, -5.05, -28.94, 0.11422], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 0, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 36, 46, 56, 56, 14, 48, 58, 58, 18, 44, 60, 60, 12, 50, 62, 62, 22, 52, 64, 64, 26, 36, 66, 68, 46, 68, 70, 70, 72, 72, 74, 74, 76, 76, 66, 66, 78, 78, 80, 80, 82, 82, 84, 84, 68, 54, 88, 66, 90, 90, 86, 90, 92, 92, 94, 94, 96, 68, 98, 98, 86, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 90, 42, 108, 108, 110], "width": 99, "height": 116}}, "yaoz_bg_014": {"yaoz_bg_014": {"type": "mesh", "uvs": [0.5346, 0.01885, 0.65146, 0.0189, 0.89545, 0.12001, 0.97729, 0.21966, 0.97719, 0.32759, 0.93198, 0.42658, 0.90735, 0.48049, 0.93036, 0.53721, 0.93115, 0.66405, 0.89026, 0.71296, 0.93992, 0.87708, 0.86035, 0.94273, 0.7215, 0.98114, 0.09344, 0.9813, 0.02223, 0.95202, 0.02301, 0.80145, 0.23135, 0.68592, 0.23179, 0.56639, 0.23211, 0.48068, 0.26012, 0.39598, 0.30207, 0.26911, 0.36196, 0.14533, 0.44249, 0.05624, 0.56289, 0.15908, 0.50754, 0.30325, 0.45773, 0.46573, 0.50478, 0.63508, 0.5795, 0.77467, 0.41068, 0.84104, 0.16438, 0.85019, 0.78429, 0.83417], "triangles": [12, 28, 27, 12, 13, 28, 13, 29, 28, 13, 14, 29, 12, 30, 11, 12, 27, 30, 14, 15, 29, 11, 30, 10, 29, 16, 28, 29, 15, 16, 28, 26, 27, 28, 16, 26, 30, 9, 10, 30, 27, 9, 27, 26, 9, 8, 9, 7, 16, 17, 26, 9, 26, 7, 26, 25, 7, 18, 19, 25, 24, 19, 20, 17, 25, 26, 17, 18, 25, 25, 6, 7, 6, 25, 5, 23, 4, 5, 23, 5, 24, 24, 5, 25, 23, 1, 2, 25, 19, 24, 3, 4, 23, 3, 23, 2, 20, 21, 24, 24, 21, 23, 21, 22, 23, 22, 0, 23, 23, 0, 1], "vertices": [2, 32, -1.92, -8.62, 0.97629, 33, -18.51, 15.28, 0.02371, 2, 32, -4.41, -4.26, 0.99722, 33, -15.99, 19.63, 0.00278, 2, 32, -5.05, 7.46, 0.98872, 33, -6.2, 26.09, 0.01128, 2, 32, -2.29, 13.09, 0.94417, 33, 0.05, 26.55, 0.05583, 2, 32, 2.58, 15.87, 0.85601, 33, 4.91, 23.75, 0.14399, 2, 32, 8.02, 16.73, 0.66752, 33, 8.4, 19.49, 0.33248, 2, 32, 10.98, 17.2, 0.48257, 33, 10.3, 17.17, 0.51743, 2, 32, 13.05, 19.52, 0.3126, 33, 13.35, 16.56, 0.6874, 3, 32, 18.76, 22.82, 0.12111, 33, 19.08, 13.29, 0.87187, 34, -11.68, -6.5, 0.00702, 3, 32, 21.84, 22.56, 0.0609, 33, 20.4, 10.5, 0.88546, 34, -9.63, -4.18, 0.05364, 3, 32, 28.19, 28.64, 0.00041, 33, 28.86, 8.09, 0.5526, 34, -10.73, 4.55, 0.44698, 2, 33, 30.11, 3.42, 0.37326, 34, -6.93, 7.53, 0.62674, 2, 33, 28.86, -2.75, 0.07831, 34, -0.76, 8.8, 0.92169, 1, 34, 26.05, 5.58, 1, 2, 32, 51.14, -3.69, 0.00038, 34, 28.91, 3.71, 0.99962, 2, 32, 44.33, -7.55, 0.01937, 34, 27.94, -4.06, 0.98063, 3, 32, 34.67, -2.75, 0.29003, 33, 5.03, -13.34, 0.06413, 34, 18.33, -8.96, 0.64585, 3, 32, 29.26, -5.81, 0.49486, 33, -0.34, -10.22, 0.31338, 34, 17.57, -15.13, 0.19175, 3, 32, 25.38, -8.01, 0.22375, 33, -4.2, -7.99, 0.71835, 34, 17.02, -19.55, 0.05791, 3, 32, 20.96, -9.15, 0.00016, 33, -7.41, -4.74, 0.9916, 34, 15.3, -23.78, 0.00823, 2, 32, 14.34, -10.85, 0.39268, 33, -12.23, 0.12, 0.60732, 2, 32, 7.47, -11.81, 0.79322, 33, -16.52, 5.56, 0.20678, 2, 32, 1.73, -11.1, 0.93216, 33, -18.8, 10.88, 0.06784, 2, 32, 3.81, -3.95, 0.97158, 33, -11.58, 12.69, 0.02842, 2, 32, 11.5, -2.3, 0.91483, 33, -6.27, 6.88, 0.08517, 1, 32, 19.9, 0.03, 1, 3, 32, 26.54, 6.15, 0.05432, 33, 8.61, -1.84, 0.88441, 34, 6.34, -10.18, 0.06127, 3, 32, 31.25, 12.54, 0.00129, 33, 16.51, -2.68, 0.48125, 34, 4.02, -2.59, 0.51745, 3, 32, 37.85, 7.95, 2e-05, 33, 15.87, -10.69, 9e-05, 34, 11.64, -0.03, 0.99989, 2, 32, 43.51, -1.01, 0.01796, 34, 22.21, -0.82, 0.98204, 3, 32, 29.57, 21.72, 0.0018, 33, 23.59, 3.4, 0.56393, 34, -4.35, 1.53, 0.43426], "hull": 23, "edges": [0, 44, 0, 2, 2, 4, 4, 6, 6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 40, 42, 42, 44, 2, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 36, 38, 38, 40, 32, 34, 34, 36, 8, 10, 10, 12, 50, 10, 14, 50, 34, 50, 50, 38, 36, 50], "width": 43, "height": 52}}, "yaoz_bg_015": {"yaoz_bg_015": {"type": "mesh", "uvs": [1, 0.01726, 1, 0.08415, 0.91486, 0.54474, 0.52287, 0.98308, 0.07476, 0.98308, 0.01347, 0.6465, 0.389, 0.19258, 0.69618, 0.01447], "triangles": [7, 0, 1, 2, 7, 1, 6, 7, 2, 5, 6, 2, 3, 5, 2, 4, 5, 3], "vertices": [-21.47, 1.85, -18.33, 4.01, 5.73, 15.34, 37.42, 13.3, 50.09, -5.19, 36, -18.56, 4.04, -17.7, -13.02, -10.77], "hull": 8, "edges": [0, 14, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14], "width": 50, "height": 57}}, "yaoz_bg_016": {"yaoz_bg_016": {"type": "mesh", "uvs": [0.69138, 0, 0.77063, 0.19758, 0.96357, 0.38979, 0.96357, 0.50501, 0.96357, 0.55366, 0.96357, 0.61042, 0.96357, 0.82559, 0.73684, 0.98954, 0.2669, 0.95192, 0.14037, 0.75167, 0.10668, 0.69835, 0.0755, 0.64899, 0.00858, 0.54308, 0.09531, 0.27861, 0.16516, 0.0656, 0.30993, 0.0401, 0.53764, 0, 0.38938, 0.15907, 0.4116, 0.42663, 0.44123, 0.60501, 0.58568, 0.81042], "triangles": [9, 19, 20, 20, 5, 6, 8, 9, 20, 7, 20, 6, 8, 20, 7, 17, 15, 16, 14, 15, 17, 1, 18, 17, 16, 0, 1, 1, 17, 16, 13, 14, 17, 13, 17, 18, 18, 3, 19, 2, 18, 1, 3, 18, 2, 19, 3, 4, 19, 4, 5, 18, 11, 12, 18, 12, 13, 11, 18, 19, 10, 11, 19, 9, 10, 19, 20, 19, 5], "vertices": [1, 37, -4.74, 11.87, 1, 2, 37, 9.8, 16.44, 0.99942, 38, -31.79, 19.14, 0.00058, 1, 37, 23.81, 27.14, 1, 1, 37, 32.33, 27.32, 1, 2, 37, 36.36, 27.49, 0.93752, 38, -3.79, 28.41, 0.06248, 2, 37, 43.38, 27.67, 0.55466, 38, 2.87, 27.9, 0.44534, 2, 37, 62.31, 25.04, 0.16832, 38, 21.13, 23.72, 0.83168, 2, 37, 73.22, 10.03, 0.01042, 38, 30.66, 7.9, 0.98958, 2, 37, 65.63, -14.38, 0.00299, 38, 21.36, -15.92, 0.99701, 2, 37, 50.03, -17.76, 0.29207, 38, 5.95, -18.41, 0.70793, 2, 37, 47.31, -18.7, 0.91784, 38, 3.88, -19.69, 0.08216, 1, 37, 43.95, -20.42, 1, 1, 37, 36.19, -24.19, 1, 1, 37, 16.53, -19.9, 1, 1, 37, 0.69, -16.45, 1, 1, 37, -1.35, -8.67, 1, 1, 37, -4.57, 3.57, 1, 1, 37, 7.36, -4.2, 1, 1, 37, 27.13, -2.6, 1, 1, 37, 40.3, -0.74, 1, 2, 37, 58.61, 4.59, 0.0179, 38, 15.72, 3.49, 0.9821], "hull": 17, "edges": [0, 32, 0, 2, 2, 4, 12, 14, 14, 16, 28, 30, 30, 32, 34, 36, 36, 38, 38, 8, 4, 6, 6, 8, 6, 38, 8, 10, 10, 12, 38, 10, 22, 24, 38, 22, 20, 22, 38, 20, 16, 18, 18, 20, 38, 18, 38, 40, 24, 26, 26, 28], "width": 54, "height": 74}}, "yaoz_bg_01": {"yaoz_bg_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [376, 39, -376, 39, -376, 567, 376, 567], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 752, "height": 528}}, "yaoz_bg_02": {"yaoz_bg_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [154.37, -25.54, -2.63, -25.54, -2.63, 7.46, 154.37, 7.46], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 157, "height": 33}}, "yaoz_bg_03": {"yaoz_bg_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [193.99, -28.78, -2.01, -28.78, -2.01, 81.22, 193.99, 81.22], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 196, "height": 110}}, "yaoz_bg_04": {"yaoz_bg_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [5.5, -40.32, -237.5, -40.32, -237.5, 40.68, 5.5, 40.68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 243, "height": 81}}, "yaoz_bg_05": {"yaoz_bg_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [5.19, -49.03, -136.81, -49.03, -136.81, 47.97, 5.19, 47.97], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 142, "height": 97}}, "yaoz_bg_06": {"yaoz_bg_06": {"type": "mesh", "uvs": [1, 1, 0.9, 1, 0.8, 1, 0.7, 1, 0.6, 1, 0.5, 1, 0.4, 1, 0.3, 1, 0.2, 1, 0.1, 1, 0, 1, 0, 0.5, 0, 0, 0.1, 0, 0.2, 0, 0.3, 0, 0.4, 0, 0.5, 0, 0.6, 0, 0.7, 0, 0.8, 0, 0.9, 0, 1, 0, 1, 0.5, 0.9, 0.5, 0.8, 0.5, 0.7, 0.5, 0.6, 0.5, 0.5, 0.5, 0.4, 0.5, 0.3, 0.5, 0.2, 0.5, 0.1, 0.5], "triangles": [0, 1, 23, 1, 24, 23, 23, 24, 22, 24, 21, 22, 1, 2, 24, 2, 25, 24, 24, 25, 21, 25, 20, 21, 25, 26, 20, 2, 3, 25, 3, 26, 25, 26, 19, 20, 3, 4, 26, 26, 27, 19, 4, 27, 26, 27, 18, 19, 4, 5, 27, 5, 28, 27, 27, 28, 18, 28, 17, 18, 5, 6, 28, 28, 29, 17, 6, 29, 28, 29, 16, 17, 6, 7, 29, 29, 30, 16, 7, 30, 29, 30, 15, 16, 7, 8, 30, 30, 31, 15, 8, 31, 30, 31, 14, 15, 8, 9, 31, 9, 32, 31, 31, 32, 14, 32, 13, 14, 9, 10, 32, 10, 11, 32, 32, 11, 13, 11, 12, 13], "vertices": [8, 12, -443.12, 291.03, 2e-05, 9, -286.09, 292.68, 0.0096, 8, -233.96, 292.5, 0.04387, 7, -182.79, 292.17, 0.09394, 6, -131.45, 291.78, 0.11758, 5, -80.44, 291.43, 0.27944, 4, -29.78, 291.21, 0.10722, 3, 21.04, 291.19, 0.34832, 10, 12, -339.94, 272.13, 0.00599, 11, -287.32, 272.92, 0.00216, 10, -234.79, 273.28, 0.00593, 9, -182.99, 273.34, 0.02796, 8, -130.85, 273.24, 0.08093, 7, -79.64, 273.07, 0.12665, 6, -28.26, 272.94, 0.1451, 5, 22.82, 272.93, 0.26652, 4, 73.55, 273.14, 0.08654, 3, 124.46, 273.63, 0.25223, 11, 13, -289.58, 252.22, 0.00013, 12, -236.76, 253.22, 0.03548, 11, -184.19, 253.75, 0.02027, 10, -131.68, 253.97, 0.03896, 9, -79.88, 254.01, 0.08907, 8, -27.73, 253.97, 0.17923, 7, 23.51, 253.97, 0.16981, 6, 74.93, 254.1, 0.15281, 5, 126.08, 254.44, 0.17826, 4, 176.88, 255.07, 0.03572, 3, 227.88, 256.06, 0.10025, 13, 15, -292.84, 230.81, 0.00016, 14, -239.7, 232.59, 0.00334, 13, -186.33, 233.7, 0.00968, 12, -133.57, 234.31, 0.1249, 11, -81.05, 234.59, 0.07996, 10, -28.57, 234.67, 0.11729, 9, 23.22, 234.67, 0.155, 8, 75.39, 234.71, 0.22346, 7, 126.65, 234.88, 0.11435, 6, 178.13, 235.26, 0.08015, 5, 229.33, 235.94, 0.0654, 4, 280.22, 237, 0.00492, 3, 331.3, 238.5, 0.02141, 13, 16, -243.57, 211.66, 0.00301, 15, -189.37, 213.52, 0.01302, 14, -136.35, 214.61, 0.04212, 13, -83.07, 215.19, 0.06618, 12, -30.39, 215.4, 0.29706, 11, 22.08, 215.42, 0.1468, 10, 74.53, 215.36, 0.14652, 9, 126.32, 215.33, 0.10032, 8, 178.5, 215.45, 0.12322, 7, 229.8, 215.78, 0.03007, 6, 281.32, 216.42, 0.019, 5, 332.59, 217.45, 0.01155, 3, 434.72, 220.94, 0.00113, 14, 18, -248.4, 190.68, 0.00331, 17, -193.35, 193.52, 0.00206, 16, -139.97, 195.25, 0.04593, 15, -85.91, 196.22, 0.09032, 14, -33, 196.63, 0.17921, 13, 20.18, 196.67, 0.16226, 12, 72.79, 196.5, 0.32917, 11, 125.22, 196.25, 0.0782, 10, 177.64, 196.05, 0.05541, 9, 229.43, 196, 0.01883, 8, 281.62, 196.18, 0.03183, 7, 332.95, 196.68, 0.00194, 6, 384.52, 197.58, 0.00122, 5, 435.85, 198.95, 0.00031, 13, 20, -254.24, 169.83, 0, 19, -198.44, 174.06, 0.00217, 18, -144.44, 176.67, 0.05195, 17, -89.58, 178.18, 0.04978, 16, -36.36, 178.84, 0.20697, 15, 17.56, 178.93, 0.21804, 14, 70.34, 178.64, 0.24194, 13, 123.43, 178.15, 0.09386, 12, 175.97, 177.59, 0.11921, 11, 228.35, 177.09, 0.00769, 10, 280.75, 176.75, 0.00511, 9, 332.53, 176.66, 0.0003, 8, 384.73, 176.92, 0.00298, 10, 21, -204.61, 155.47, 0.00056, 20, -149.87, 159.34, 0.01887, 19, -94.28, 161.63, 0.05686, 18, -40.48, 162.66, 0.24808, 17, 14.19, 162.84, 0.18751, 16, 67.25, 162.43, 0.28183, 15, 121.02, 161.63, 0.10811, 14, 173.69, 160.66, 0.07563, 13, 226.68, 159.63, 0.00916, 12, 279.15, 158.68, 0.01338, 10, 22, -156.53, 143.81, 0.01009, 21, -100.03, 147.25, 0.05687, 20, -45.5, 148.85, 0.1611, 19, 9.88, 149.2, 0.24009, 18, 63.48, 148.65, 0.34202, 17, 117.97, 147.5, 0.11176, 16, 170.86, 146.02, 0.06699, 15, 224.48, 144.34, 0.00594, 14, 277.04, 142.68, 0.00513, 12, 382.34, 139.77, 0, 7, 22, -51.77, 138.38, 0.20384, 21, 4.54, 139.03, 0.3399, 20, 58.88, 138.36, 0.23137, 19, 114.04, 136.76, 0.14024, 18, 167.44, 134.64, 0.07372, 17, 221.74, 132.15, 0.00947, 16, 274.47, 129.6, 0.00146, 6, 22, 52.99, 132.95, 0.56891, 21, 109.12, 130.81, 0.37484, 20, 163.25, 127.87, 0.04463, 19, 218.2, 124.33, 0.01128, 18, 271.4, 120.64, 0.00033, 16, 378.07, 113.19, 0, 6, 22, 43.49, -50.3, 0.78169, 21, 94.74, -52.12, 0.14651, 20, 144.9, -54.71, 0.02818, 19, 196.45, -57.88, 0.02808, 18, 246.9, -61.22, 0.01456, 16, 349.36, -68.05, 0.00098, 8, 22, 34, -233.56, 0.28613, 21, 80.36, -235.06, 0.23069, 20, 126.55, -237.29, 0.15045, 19, 174.71, -240.08, 0.18157, 18, 222.4, -243.08, 0.12644, 17, 271.83, -246.24, 0.00284, 16, 320.66, -249.29, 0.02181, 15, 370.91, -252.23, 8e-05, 11, 22, -70.76, -228.13, 0.17041, 21, -24.21, -226.84, 0.16559, 20, 22.17, -226.8, 0.14591, 19, 70.55, -227.65, 0.21816, 18, 118.44, -229.07, 0.19447, 17, 168.06, -230.9, 0.03246, 16, 217.05, -232.88, 0.05858, 15, 267.44, -234.93, 0.01369, 13, 368.4, -238.65, 0.00013, 12, 419.37, -240.12, 0.00059, 6, 731.38, -238.81, 0, 13, 22, -175.52, -222.7, 0.04036, 21, -128.79, -218.62, 0.05286, 20, -82.2, -216.31, 0.06537, 19, -33.61, -215.22, 0.17785, 18, 14.48, -215.06, 0.26373, 17, 64.29, -215.56, 0.1183, 16, 113.44, -216.46, 0.1738, 15, 163.98, -217.64, 0.08294, 14, 214.12, -218.89, 0.00263, 13, 265.14, -220.13, 0.00923, 12, 316.19, -221.22, 0.01286, 11, 367.56, -222.07, 7e-05, 6, 628.19, -219.97, 0, 13, 22, -280.28, -217.27, 0.00271, 21, -233.37, -210.4, 0.00573, 20, -186.57, -205.82, 0.00745, 19, -137.78, -202.79, 0.05551, 18, -89.49, -201.05, 0.13867, 17, -39.48, -200.21, 0.12048, 16, 9.83, -200.05, 0.25073, 15, 60.51, -200.34, 0.23267, 14, 110.77, -200.91, 0.04279, 13, 161.89, -201.61, 0.06203, 12, 213, -202.31, 0.07078, 11, 264.43, -202.9, 0.01042, 6, 524.99, -201.13, 1e-05, 12, 19, -241.94, -190.35, 0.00554, 18, -193.45, -187.05, 0.02599, 17, -143.25, -184.87, 0.03057, 16, -93.78, -183.64, 0.11532, 15, -42.95, -183.05, 0.22868, 14, 7.43, -182.92, 0.10088, 13, 58.64, -183.09, 0.17349, 12, 109.82, -183.4, 0.22862, 11, 161.29, -183.73, 0.08084, 10, 213.2, -183.98, 0.00924, 9, 264.88, -184.05, 0.0008, 6, 421.8, -182.29, 2e-05, 13, 18, -297.41, -173.04, 0.00085, 17, -247.03, -169.53, 0.00115, 16, -197.38, -167.23, 0.01623, 15, -146.41, -165.75, 0.06581, 14, -95.92, -164.94, 0.03363, 13, -44.61, -164.57, 0.12408, 12, 6.64, -164.49, 0.33448, 11, 58.16, -164.57, 0.27255, 10, 110.1, -164.68, 0.10105, 9, 161.78, -164.71, 0.03453, 8, 214.22, -164.58, 0.01557, 7, 266.13, -164.19, 5e-05, 6, 318.61, -163.45, 4e-05, 13, 18, -401.37, -159.03, 0, 17, -350.8, -154.19, 0, 16, -300.99, -150.82, 9e-05, 15, -249.88, -148.46, 0.00565, 14, -199.27, -146.96, 0.00034, 13, -147.87, -146.05, 0.01141, 12, -96.54, -145.58, 0.11207, 11, -44.97, -145.4, 0.25217, 10, 6.99, -145.37, 0.24556, 9, 58.67, -145.38, 0.1783, 8, 111.1, -145.31, 0.1615, 7, 162.99, -145.09, 0.03232, 6, 215.41, -144.61, 0.00058, 10, 18, -505.33, -145.02, 0, 17, -454.57, -138.84, 0, 12, -199.72, -126.68, 0.0052, 11, -148.11, -126.24, 0.04281, 10, -96.12, -126.06, 0.07152, 9, -44.43, -126.04, 0.1073, 8, 7.99, -126.05, 0.3711, 7, 59.84, -125.99, 0.28462, 6, 112.22, -125.77, 0.09491, 5, 164.63, -125.31, 0.02254, 11, 18, -609.29, -131.01, 0, 17, -558.34, -123.5, 0, 11, -251.24, -107.07, 0.00037, 10, -199.23, -106.76, 0.00083, 9, -147.53, -106.7, 0.00064, 8, -95.13, -106.78, 0.05746, 7, -43.31, -106.89, 0.17734, 6, 9.02, -106.93, 0.39762, 5, 61.37, -106.81, 0.31667, 4, 113.66, -106.45, 0.04274, 3, 166.44, -105.76, 0.00633, 6, 17, -662.11, -108.16, 0, 7, -146.46, -87.8, 0.00101, 6, -94.17, -88.09, 0.05286, 5, -41.89, -88.32, 0.24373, 4, 10.33, -88.38, 0.27419, 3, 63.02, -88.19, 0.42821, 2, 4, -93, -70.31, 0.00167, 3, -40.4, -70.63, 0.99833, 7, 9, -319.91, 112.32, 0.00046, 8, -267.66, 112.12, 0.00705, 7, -216.2, 111.74, 0.02782, 6, -164.41, 111.26, 0.03557, 5, -112.79, 110.81, 0.15152, 4, -61.39, 110.45, 0.12077, 3, -9.68, 110.28, 0.6568, 9, 12, -373.01, 91.63, 0.00055, 10, -268.56, 92.91, 0.00011, 9, -216.81, 92.99, 0.00378, 8, -164.55, 92.86, 0.01585, 7, -113.05, 92.64, 0.05496, 6, -61.22, 92.42, 0.10065, 5, -9.53, 92.31, 0.33162, 4, 41.94, 92.38, 0.22856, 3, 93.74, 92.72, 0.26393, 10, 12, -269.83, 72.73, 0.00511, 11, -217.71, 73.34, 0.00143, 10, -165.46, 73.61, 0.00428, 9, -113.71, 73.65, 0.02153, 8, -61.43, 73.59, 0.08363, 7, -9.9, 73.54, 0.31016, 6, 41.98, 73.58, 0.3594, 5, 93.72, 73.81, 0.16796, 4, 145.27, 74.31, 0.01748, 3, 197.16, 75.15, 0.02902, 9, 12, -166.65, 53.82, 0.01577, 11, -114.58, 54.18, 0.01237, 10, -62.35, 54.3, 0.05013, 9, -10.6, 54.32, 0.29617, 8, 41.69, 54.33, 0.49832, 7, 93.25, 54.44, 0.0888, 6, 145.17, 54.75, 0.02659, 5, 196.98, 55.32, 0.00987, 3, 300.58, 57.59, 0.00197, 9, 13, -115.47, 34.57, 0.00131, 12, -63.47, 34.91, 0.04022, 11, -11.45, 35.01, 0.3503, 10, 40.76, 34.99, 0.54381, 9, 92.5, 34.98, 0.03417, 8, 144.8, 35.07, 0.02846, 7, 196.39, 35.35, 0.0004, 6, 248.37, 35.91, 0.001, 5, 300.24, 36.82, 0.00035, 6, 15, -116.16, 15.23, 0.00075, 13, -12.22, 16.05, 0.09313, 12, 39.72, 16, 0.88725, 11, 91.69, 15.84, 0.01656, 10, 143.87, 15.69, 0.00122, 8, 247.92, 15.8, 0.0011, 8, 17, -116.42, -3.34, 5e-05, 16, -65.07, -2.4, 0.00071, 15, -12.7, -2.06, 0.01547, 14, 38.89, -2.14, 0.9796, 13, 91.03, -2.47, 0.00316, 12, 142.9, -2.91, 0.00091, 11, 194.82, -3.32, 0.00011, 6, 454.76, -1.77, 0, 8, 19, -116.03, -20.58, 0.00077, 18, -64.98, -19.2, 0.00488, 17, -12.64, -18.69, 0.17872, 16, 38.54, -18.81, 0.80069, 15, 90.77, -19.35, 0.01159, 13, 194.29, -20.99, 0.0013, 12, 246.08, -21.81, 0.00204, 6, 557.95, -20.61, 0, 10, 22, -166.02, -39.45, 0.00107, 21, -114.41, -35.68, 0.0043, 20, -63.85, -33.73, 0.01258, 19, -11.87, -33.01, 0.2412, 18, 38.98, -33.2, 0.67467, 17, 91.13, -34.03, 0.04211, 16, 142.15, -35.22, 0.02126, 15, 194.23, -36.65, 0.00246, 12, 349.26, -40.72, 0.00034, 6, 661.14, -39.45, 0, 8, 22, -61.26, -44.87, 0.04473, 21, -9.84, -43.9, 0.28449, 20, 40.53, -44.22, 0.52152, 19, 92.29, -45.44, 0.10967, 18, 142.94, -47.21, 0.03498, 16, 245.76, -51.64, 0.00457, 15, 297.7, -53.94, 4e-05, 6, 764.34, -58.29, 0], "hull": 24, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 0], "width": 419, "height": 146}}, "yaoz_bg_07": {"yaoz_bg_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [376, -489, -376, -489, -376, 567, 376, 567], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 752, "height": 1056}}, "yaoz_bg_08": {"yaoz_bg_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-11.81, -93.01, -754.75, -83.54, -749.8, 304.43, -6.86, 294.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 743, "height": 388}}, "yaoz_bg_09": {"yaoz_bg_09": {"type": "mesh", "uvs": [0.7068, 0.056, 0.90167, 0.29469, 1, 0.59565, 1, 0.59977, 0.64228, 0.97152, 0.30643, 0.96436, 0.00358, 0.72783, 0.07414, 0.22743, 0.43545, 0.01533, 0.52543, 0.47766, 0.2684, 0.5496, 0.80388, 0.533], "triangles": [9, 8, 0, 9, 0, 1, 7, 8, 9, 11, 9, 1, 10, 7, 9, 11, 1, 2, 11, 2, 3, 6, 7, 10, 5, 10, 9, 6, 10, 5, 4, 9, 11, 5, 9, 4, 4, 11, 3], "vertices": [5.94, 13.21, 12.19, 6.23, 15.51, -2.7, 15.51, -2.83, 4.75, -14.3, -5.66, -14.39, -15.25, -7.58, -13.51, 7.49, -2.5, 14.18, 0.7, 0.4, -7.2, -1.99, 9.37, -1], "hull": 9, "edges": [0, 16, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18], "width": 31, "height": 30}}, "yingzi": {"yingzi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [62.19, -26.72, -86.81, -26.72, -86.81, 39.28, 62.19, 39.28], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 149, "height": 66}}, "yaoz_bg_010": {"yaoz_bg_010": {"type": "mesh", "uvs": [0.22902, 0.01986, 0.51929, 0.02021, 0.62721, 0.10858, 0.79598, 0.3264, 0.79594, 0.42428, 0.79592, 0.49228, 0.79585, 0.67373, 0.98015, 0.78322, 0.97812, 0.95792, 0.4895, 0.98313, 0.10881, 0.91376, 0.02143, 0.83819, 0.11303, 0.67368, 0.11313, 0.51917, 0.18142, 0.42861, 0.08975, 0.34648, 0.03883, 0.27909, 0.10981, 0.12044, 0.64587, 0.44129, 0.28617, 0.74243, 0.62964, 0.84686, 0.58367, 0.69629, 0.38561, 0.17816], "triangles": [9, 20, 8, 10, 19, 9, 9, 19, 20, 8, 20, 7, 7, 20, 6, 19, 21, 20, 20, 21, 6, 10, 12, 19, 10, 11, 12, 18, 21, 19, 19, 12, 13, 6, 21, 5, 18, 19, 13, 5, 21, 18, 13, 14, 18, 5, 18, 4, 18, 15, 22, 22, 2, 18, 4, 18, 3, 18, 14, 15, 3, 18, 2, 15, 17, 22, 15, 16, 17, 17, 0, 22, 22, 1, 2, 22, 0, 1], "vertices": [2, 29, -3.35, 0.27, 1, 30, -2.09, -27.63, 0, 1, 29, 4.37, 10.44, 1, 1, 29, 10.69, 11.62, 1, 3, 29, 23.68, 11.11, 0.91346, 30, -11.13, 0.05, 0.08646, 31, 20.94, 27.95, 9e-05, 3, 29, 27.5, 8.22, 0.63247, 30, -7.99, 3.68, 0.3516, 31, 21.96, 23.26, 0.01593, 3, 29, 30.16, 6.2, 0.31586, 30, -5.81, 6.2, 0.61893, 31, 22.67, 20.01, 0.06521, 3, 29, 37.25, 0.84, 0.00066, 30, 0.01, 12.92, 0.47506, 31, 24.58, 11.32, 0.52428, 2, 30, -2.61, 22.28, 0.07403, 31, 33.65, 7.82, 0.92597, 2, 30, 3.06, 28.69, 0.00212, 31, 35.39, -0.56, 0.99788, 1, 31, 14.66, -6.38, 1, 3, 29, 28.4, -30.37, 0, 30, 30.57, 2.03, 0.11825, 31, -2.43, -6.65, 0.88175, 3, 29, 23.13, -31.2, 0, 30, 31.05, -3.29, 0.4508, 31, -6.98, -3.85, 0.5492, 3, 29, 19.13, -23.12, 0.02916, 30, 22.73, -6.74, 0.96798, 31, -4.77, 4.88, 0.00286, 2, 29, 13.09, -18.56, 0.20673, 30, 17.77, -12.47, 0.79327, 2, 29, 11.37, -13.48, 0.5337, 30, 12.6, -13.86, 0.4663, 2, 29, 5.72, -14.27, 0.84556, 30, 13.01, -19.54, 0.15444, 2, 29, 1.74, -14.07, 0.90775, 30, 12.55, -23.5, 0.09225, 2, 29, -2.58, -6.89, 0.98267, 30, 5.1, -27.34, 0.01733, 3, 29, 24.19, 2.45, 0.75214, 30, -2.45, -0.01, 0.24637, 31, 15.69, 21.03, 0.00149, 2, 30, 19.17, 0.79, 0.87971, 31, 3.39, 3.23, 0.12029, 2, 30, 11.09, 14.55, 0.04053, 31, 19.25, 1.47, 0.95947, 2, 30, 7.79, 7.64, 0.56796, 31, 15.69, 8.24, 0.43204, 1, 29, 6.99, 1.08, 1], "hull": 18, "edges": [0, 34, 0, 2, 2, 4, 4, 6, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 36, 38, 38, 40, 28, 36, 6, 8, 36, 8, 8, 10, 10, 12, 10, 36, 30, 36, 36, 26, 12, 42, 0, 44, 44, 36], "width": 44, "height": 49}}}}], "animations": {"yz_bg_defeat": {"slots": {"yaoz_bg_015": {"twoColor": [{"light": "ffffffff", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "light": "9a9a9aff", "dark": "1a1b1b"}]}, "yaoz_bg_014": {"twoColor": [{"light": "ffffffff", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "light": "9a9a9aff", "dark": "1a1b1b"}]}, "yaoz_bg_016": {"twoColor": [{"light": "ffffffff", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "light": "9a9a9aff", "dark": "1a1b1b"}]}, "yaoz_bg_4": {"color": [{"color": "ffffffd4", "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.4667, "color": "ffffffff"}]}, "yaoz_bg_09": {"twoColor": [{"time": 0.6333, "light": "ffffffff", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "light": "9a9a9aff", "dark": "1a1b1b"}]}, "yaoz_bg_012": {"twoColor": [{"light": "ffffffff", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "light": "9a9a9aff", "dark": "1a1b1b"}]}, "yaoz_bg_013": {"twoColor": [{"light": "ffffffff", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "light": "9a9a9aff", "dark": "1a1b1b"}]}, "yaoz_bg_2": {"color": [{"color": "ffffff18", "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.3333, "color": "ffffff00"}]}, "yaoz_bg_011": {"twoColor": [{"light": "ffffffff", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "light": "9a9a9aff", "dark": "1a1b1b"}]}, "yaoz_bg_010": {"twoColor": [{"light": "ffffffff", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "light": "9a9a9aff", "dark": "1a1b1b"}]}, "yaoz_bg_04": {"color": [{"color": "ffffff2a", "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.4667, "color": "ffffff00"}]}, "yaoz_bg_05": {"color": [{"color": "ffffff00"}]}, "yaoz_bg_02": {"color": [{"color": "ffffffe6", "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.3333, "color": "ffffffff"}]}, "yaoz_bg_3": {"color": [{"color": "ffffff2a", "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 1.5333, "color": "ffffffff"}]}, "yaoz_bg_03": {"color": [{"color": "ffffffd4", "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 1.5333, "color": "ffffff00"}]}}, "bones": {"all3": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 8.79, "y": -16.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 8.79, "y": -17.38}]}, "all5": {"translate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0667, "x": 5.35, "y": -0.23, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2667, "x": -18.53, "y": -1.13}]}, "all7": {"rotate": [{"angle": -0.44, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -8.99}]}, "all8": {"rotate": [{"angle": -0.82, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -41.61}]}, "all9": {"rotate": [{"angle": -0.08, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": 50.36}]}, "all10": {"rotate": [{"angle": -2.82, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -27.11}]}, "all11": {"rotate": [{"angle": 1.82, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": 70.32}]}, "all12": {"rotate": [{"angle": -0.68, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -52.7}]}, "all13": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.09}]}, "all14": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -3.03}]}, "all16": {"rotate": [{"time": 0.3}, {"time": 0.4333, "angle": -120}, {"time": 0.6, "angle": 120}, {"time": 0.7333}, {"time": 0.9333, "angle": -120}, {"time": 1.1333, "angle": 120, "curve": 0.492, "c2": 0.51, "c3": 0.496, "c4": 0.71}, {"time": 1.4333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -2.5, "y": 6.98, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 16.34, "y": -143.66, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.4667, "x": 41.32, "y": -114.68, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.5667, "x": 58.36, "y": -143.22, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 0.6333, "x": 65.68, "y": -130.74, "curve": 0.303, "c2": 0.23, "c3": 0.646, "c4": 0.59}, {"time": 0.7333, "x": 88.65, "y": -139.67, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": 173.6, "y": -139.67}]}, "all17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -10.94}]}, "all18": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -10.94}]}, "target1": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 11.45, "y": 2.95}]}, "all27": {"translate": [{"x": 0.39, "curve": 0.287, "c2": 0.17, "c3": 0.644, "c4": 0.59}, {"time": 1.5333, "x": 3.63}], "scale": [{"x": 1.004, "y": 1.004, "curve": 0.287, "c2": 0.17, "c3": 0.644, "c4": 0.59}, {"time": 1.5333, "x": 1.039, "y": 1.039}]}, "all28": {"translate": [{"x": 2.88, "curve": 0.342, "c2": 0.36, "c3": 0.698, "c4": 0.77}, {"time": 1.5333, "x": 6.4}], "scale": [{"x": 1.052, "y": 1.052, "curve": 0.342, "c2": 0.36, "c3": 0.698, "c4": 0.77}, {"time": 1.5333, "x": 1.116, "y": 1.116}]}, "all22": {"translate": [{"curve": 0.259, "c3": 0.618, "c4": 0.45}, {"time": 1.5333, "x": -6.89}], "scale": [{"curve": 0.259, "c3": 0.618, "c4": 0.45}, {"time": 1.5333, "x": 1.074, "y": 1.074}]}, "all23": {"translate": [{"x": -9.95, "curve": 0.358, "c2": 0.42, "c3": 0.715, "c4": 0.84}, {"time": 1.5333, "x": -18.75}], "scale": [{"x": 1.107, "y": 1.107, "curve": 0.358, "c2": 0.42, "c3": 0.715, "c4": 0.84}, {"time": 1.5333, "x": 1.201, "y": 1.201}]}, "all26": {"translate": [{"x": 4.74, "curve": 0.382, "c2": 0.55, "c3": 0.74}, {"time": 1.5, "x": 7.18, "curve": "stepped"}, {"time": 1.5333}], "scale": [{"x": 1.05, "y": 1.05, "curve": 0.382, "c2": 0.55, "c3": 0.74}, {"time": 1.5, "x": 1.076, "y": 1.076, "curve": "stepped"}, {"time": 1.5333}]}, "all24": {"translate": [{"x": -9.13, "curve": 0.362, "c2": 0.64, "c3": 0.697}, {"time": 0.4333, "x": -9.61, "curve": "stepped"}, {"time": 0.4667, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 1.5333, "x": -2}], "scale": [{"x": 1.1, "y": 1.1, "curve": 0.362, "c2": 0.64, "c3": 0.697}, {"time": 0.4333, "x": 1.105, "y": 1.105, "curve": "stepped"}, {"time": 0.4667, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 1.5333, "x": 1.022, "y": 1.022}]}, "all25": {"translate": [{"x": -3.37, "curve": 0.329, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 0.4667, "x": -4.86, "curve": 0.345, "c2": 0.37, "c3": 0.689, "c4": 0.74}, {"time": 1.5333, "x": -8.11}], "scale": [{"x": 1.037, "y": 1.037, "curve": 0.329, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 0.4667, "x": 1.053, "y": 1.053, "curve": 0.345, "c2": 0.37, "c3": 0.689, "c4": 0.74}, {"time": 1.5333, "x": 1.089, "y": 1.089}]}, "all29": {"translate": [{"x": 7.11, "curve": 0.354, "c2": 0.65, "c3": 0.689}, {"time": 0.3, "x": 7.29, "curve": "stepped"}, {"time": 0.3333, "curve": 0.268, "c3": 0.618, "c4": 0.42}, {"time": 1.5333, "x": 1.78}], "scale": [{"x": 1.129, "y": 1.129, "curve": 0.354, "c2": 0.65, "c3": 0.689}, {"time": 0.3, "x": 1.132, "y": 1.132, "curve": "stepped"}, {"time": 0.3333, "curve": 0.268, "c3": 0.618, "c4": 0.42}, {"time": 1.5333, "x": 1.032, "y": 1.032}]}}, "drawOrder": [{"time": 0.1, "offsets": [{"slot": "yaoz_bg_09", "offset": 8}]}]}, "yz_bg_idle": {"slots": {"yaoz_bg_5": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 6, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 8, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 10, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 12, "color": "ffffffff"}]}, "yaoz_bg_4": {"color": [{"color": "ffffffd4", "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.4667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "color": "ffffff00", "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 4, "color": "ffffffd4", "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 4.4667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "color": "ffffff00", "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 8, "color": "ffffffd4", "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 8.4667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 10.4667, "color": "ffffff00", "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 12, "color": "ffffffd4"}]}, "yaoz_bg_04": {"color": [{"color": "ffffff2a", "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.4667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "color": "ffffffff", "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 4, "color": "ffffff2a", "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 4.4667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6.4667, "color": "ffffffff", "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 8, "color": "ffffff2a", "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 8.4667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 10.4667, "color": "ffffffff", "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 12, "color": "ffffff2a"}]}, "kunyao": {"attachment": [{"name": "kunyao"}]}, "yaoz_bg_02": {"color": [{"color": "ffffffe6", "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.3333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "color": "ffffff00", "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "color": "ffffffe6", "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.3333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "color": "ffffff00", "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 8, "color": "ffffffe6", "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 8.3333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "color": "ffffff00", "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 12, "color": "ffffffe6"}]}, "yaoz_bg_2": {"color": [{"color": "ffffff18", "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "color": "ffffffff", "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "color": "ffffff18", "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "color": "ffffffff", "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 8, "color": "ffffff18", "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 8.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 10.3333, "color": "ffffffff", "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 12, "color": "ffffff18"}]}, "yaoz_bg_08": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffff42", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 6, "color": "ffffff42", "curve": 0.25, "c3": 0.75}, {"time": 8, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 10, "color": "ffffff42", "curve": 0.25, "c3": 0.75}, {"time": 12, "color": "ffffffff"}]}, "yaoz_bg_05": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 8, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 10, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 12, "color": "ffffff00"}]}, "yaoz_bg_3": {"color": [{"color": "ffffff2a", "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 1.5333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "color": "ffffff00", "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 4, "color": "ffffff2a", "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 5.5333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 7.5333, "color": "ffffff00", "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 8, "color": "ffffff2a", "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 9.5333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 11.5333, "color": "ffffff00", "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 12, "color": "ffffff2a"}]}, "yaoz_bg_03": {"color": [{"color": "ffffffd4", "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 1.5333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "color": "ffffffff", "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 4, "color": "ffffffd4", "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 5.5333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 7.5333, "color": "ffffffff", "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 8, "color": "ffffffd4", "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 9.5333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 11.5333, "color": "ffffffff", "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 12, "color": "ffffffd4"}]}}, "bones": {"all28": {"translate": [{"x": 2.88, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 2.3, "x": 7.29, "curve": "stepped"}, {"time": 2.3333, "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 4, "x": 2.88, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 6.3, "x": 7.29, "curve": "stepped"}, {"time": 6.3333, "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 8, "x": 2.88, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 10.3, "x": 7.29, "curve": "stepped"}, {"time": 10.3333, "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 12, "x": 2.88}], "scale": [{"x": 1.052, "y": 1.052, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 2.3, "x": 1.132, "y": 1.132, "curve": "stepped"}, {"time": 2.3333, "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 4, "x": 1.052, "y": 1.052, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 6.3, "x": 1.132, "y": 1.132, "curve": "stepped"}, {"time": 6.3333, "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 8, "x": 1.052, "y": 1.052, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 10.3, "x": 1.132, "y": 1.132, "curve": "stepped"}, {"time": 10.3333, "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 12, "x": 1.052, "y": 1.052}]}, "all22": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 3.9667, "x": -19.68, "curve": "stepped"}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "x": -19.68, "curve": "stepped"}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 11.9667, "x": -19.68, "curve": "stepped"}, {"time": 12}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 3.9667, "x": 1.211, "y": 1.211, "curve": "stepped"}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "x": 1.211, "y": 1.211, "curve": "stepped"}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 11.9667, "x": 1.211, "y": 1.211, "curve": "stepped"}, {"time": 12}]}, "all23": {"translate": [{"x": -9.95, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.9667, "x": -19.68, "curve": "stepped"}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": -9.95, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.9667, "x": -19.68, "curve": "stepped"}, {"time": 6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": -9.95, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 9.9667, "x": -19.68, "curve": "stepped"}, {"time": 10, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 12, "x": -9.95}], "scale": [{"x": 1.107, "y": 1.107, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.9667, "x": 1.211, "y": 1.211, "curve": "stepped"}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 1.107, "y": 1.107, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.9667, "x": 1.211, "y": 1.211, "curve": "stepped"}, {"time": 6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": 1.107, "y": 1.107, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 9.9667, "x": 1.211, "y": 1.211, "curve": "stepped"}, {"time": 10, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 12, "x": 1.107, "y": 1.107}]}, "all24": {"translate": [{"x": -9.13, "curve": 0.362, "c2": 0.64, "c3": 0.697}, {"time": 0.4333, "x": -9.61, "curve": "stepped"}, {"time": 0.4667, "curve": 0.245, "c3": 0.708, "c4": 0.82}, {"time": 4, "x": -9.13, "curve": 0.362, "c2": 0.64, "c3": 0.697}, {"time": 4.4333, "x": -9.61, "curve": "stepped"}, {"time": 4.4667, "curve": 0.245, "c3": 0.708, "c4": 0.82}, {"time": 8, "x": -9.13, "curve": 0.362, "c2": 0.64, "c3": 0.697}, {"time": 8.4333, "x": -9.61, "curve": "stepped"}, {"time": 8.4667, "curve": 0.245, "c3": 0.708, "c4": 0.82}, {"time": 12, "x": -9.13}], "scale": [{"x": 1.1, "y": 1.1, "curve": 0.362, "c2": 0.64, "c3": 0.697}, {"time": 0.4333, "x": 1.105, "y": 1.105, "curve": "stepped"}, {"time": 0.4667, "curve": 0.245, "c3": 0.708, "c4": 0.82}, {"time": 4, "x": 1.1, "y": 1.1, "curve": 0.362, "c2": 0.64, "c3": 0.697}, {"time": 4.4333, "x": 1.105, "y": 1.105, "curve": "stepped"}, {"time": 4.4667, "curve": 0.245, "c3": 0.708, "c4": 0.82}, {"time": 8, "x": 1.1, "y": 1.1, "curve": 0.362, "c2": 0.64, "c3": 0.697}, {"time": 8.4333, "x": 1.105, "y": 1.105, "curve": "stepped"}, {"time": 8.4667, "curve": 0.245, "c3": 0.708, "c4": 0.82}, {"time": 12, "x": 1.1, "y": 1.1}]}, "all27": {"translate": [{"x": 0.39, "curve": 0.287, "c2": 0.17, "c3": 0.644, "c4": 0.59}, {"time": 1.5333, "x": 3.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.5, "x": 7.18, "curve": "stepped"}, {"time": 3.5333, "curve": 0.301, "c3": 0.637, "c4": 0.36}, {"time": 4, "x": 0.39, "curve": 0.287, "c2": 0.17, "c3": 0.644, "c4": 0.59}, {"time": 5.5333, "x": 3.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7.5, "x": 7.18, "curve": "stepped"}, {"time": 7.5333, "curve": 0.301, "c3": 0.637, "c4": 0.36}, {"time": 8, "x": 0.39, "curve": 0.287, "c2": 0.17, "c3": 0.644, "c4": 0.59}, {"time": 9.5333, "x": 3.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 11.5, "x": 7.18, "curve": "stepped"}, {"time": 11.5333, "curve": 0.301, "c3": 0.637, "c4": 0.36}, {"time": 12, "x": 0.39}], "scale": [{"x": 1.004, "y": 1.004, "curve": 0.287, "c2": 0.17, "c3": 0.644, "c4": 0.59}, {"time": 1.5333, "x": 1.039, "y": 1.039, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.5, "x": 1.076, "y": 1.076, "curve": "stepped"}, {"time": 3.5333, "curve": 0.301, "c3": 0.637, "c4": 0.36}, {"time": 4, "x": 1.004, "y": 1.004, "curve": 0.287, "c2": 0.17, "c3": 0.644, "c4": 0.59}, {"time": 5.5333, "x": 1.039, "y": 1.039, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7.5, "x": 1.076, "y": 1.076, "curve": "stepped"}, {"time": 7.5333, "curve": 0.301, "c3": 0.637, "c4": 0.36}, {"time": 8, "x": 1.004, "y": 1.004, "curve": 0.287, "c2": 0.17, "c3": 0.644, "c4": 0.59}, {"time": 9.5333, "x": 1.039, "y": 1.039, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 11.5, "x": 1.076, "y": 1.076, "curve": "stepped"}, {"time": 11.5333, "curve": 0.301, "c3": 0.637, "c4": 0.36}, {"time": 12, "x": 1.004, "y": 1.004}]}, "all25": {"translate": [{"x": -3.37, "curve": 0.329, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 0.4667, "x": -4.86, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.4333, "x": -9.61, "curve": "stepped"}, {"time": 2.4667, "curve": 0.259, "c3": 0.618, "c4": 0.45}, {"time": 4, "x": -3.37, "curve": 0.329, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 4.4667, "x": -4.86, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.4333, "x": -9.61, "curve": "stepped"}, {"time": 6.4667, "curve": 0.259, "c3": 0.618, "c4": 0.45}, {"time": 8, "x": -3.37, "curve": 0.329, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 8.4667, "x": -4.86, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 10.4333, "x": -9.61, "curve": "stepped"}, {"time": 10.4667, "curve": 0.259, "c3": 0.618, "c4": 0.45}, {"time": 12, "x": -3.37}], "scale": [{"x": 1.037, "y": 1.037, "curve": 0.329, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 0.4667, "x": 1.053, "y": 1.053, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.4333, "x": 1.105, "y": 1.105, "curve": "stepped"}, {"time": 2.4667, "curve": 0.259, "c3": 0.618, "c4": 0.45}, {"time": 4, "x": 1.037, "y": 1.037, "curve": 0.329, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 4.4667, "x": 1.053, "y": 1.053, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.4333, "x": 1.105, "y": 1.105, "curve": "stepped"}, {"time": 6.4667, "curve": 0.259, "c3": 0.618, "c4": 0.45}, {"time": 8, "x": 1.037, "y": 1.037, "curve": 0.329, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 8.4667, "x": 1.053, "y": 1.053, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 10.4333, "x": 1.105, "y": 1.105, "curve": "stepped"}, {"time": 10.4667, "curve": 0.259, "c3": 0.618, "c4": 0.45}, {"time": 12, "x": 1.037, "y": 1.037}]}, "all26": {"translate": [{"x": 4.74, "curve": 0.382, "c2": 0.55, "c3": 0.74}, {"time": 1.5, "x": 7.18, "curve": "stepped"}, {"time": 1.5333, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 4, "x": 4.74, "curve": 0.382, "c2": 0.55, "c3": 0.74}, {"time": 5.5, "x": 7.18, "curve": "stepped"}, {"time": 5.5333, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8, "x": 4.74, "curve": 0.382, "c2": 0.55, "c3": 0.74}, {"time": 9.5, "x": 7.18, "curve": "stepped"}, {"time": 9.5333, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 12, "x": 4.74}], "scale": [{"x": 1.05, "y": 1.05, "curve": 0.382, "c2": 0.55, "c3": 0.74}, {"time": 1.5, "x": 1.076, "y": 1.076, "curve": "stepped"}, {"time": 1.5333, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 4, "x": 1.05, "y": 1.05, "curve": 0.382, "c2": 0.55, "c3": 0.74}, {"time": 5.5, "x": 1.076, "y": 1.076, "curve": "stepped"}, {"time": 5.5333, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8, "x": 1.05, "y": 1.05, "curve": 0.382, "c2": 0.55, "c3": 0.74}, {"time": 9.5, "x": 1.076, "y": 1.076, "curve": "stepped"}, {"time": 9.5333, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 12, "x": 1.05, "y": 1.05}]}, "all29": {"translate": [{"x": 7.11, "curve": 0.354, "c2": 0.65, "c3": 0.689}, {"time": 0.3, "x": 7.29, "curve": "stepped"}, {"time": 0.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.3333, "x": 3.69, "curve": 0.363, "c2": 0.44, "c3": 0.724, "c4": 0.88}, {"time": 4, "x": 7.11, "curve": 0.354, "c2": 0.65, "c3": 0.689}, {"time": 4.3, "x": 7.29, "curve": "stepped"}, {"time": 4.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.3333, "x": 3.69, "curve": 0.363, "c2": 0.44, "c3": 0.724, "c4": 0.88}, {"time": 8, "x": 7.11, "curve": 0.354, "c2": 0.65, "c3": 0.689}, {"time": 8.3, "x": 7.29, "curve": "stepped"}, {"time": 8.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.3333, "x": 3.69, "curve": 0.363, "c2": 0.44, "c3": 0.724, "c4": 0.88}, {"time": 12, "x": 7.11}], "scale": [{"x": 1.129, "y": 1.129, "curve": 0.354, "c2": 0.65, "c3": 0.689}, {"time": 0.3, "x": 1.132, "y": 1.132, "curve": "stepped"}, {"time": 0.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.3333, "x": 1.067, "y": 1.067, "curve": 0.363, "c2": 0.44, "c3": 0.724, "c4": 0.88}, {"time": 4, "x": 1.129, "y": 1.129, "curve": 0.354, "c2": 0.65, "c3": 0.689}, {"time": 4.3, "x": 1.132, "y": 1.132, "curve": "stepped"}, {"time": 4.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.3333, "x": 1.067, "y": 1.067, "curve": 0.363, "c2": 0.44, "c3": 0.724, "c4": 0.88}, {"time": 8, "x": 1.129, "y": 1.129, "curve": 0.354, "c2": 0.65, "c3": 0.689}, {"time": 8.3, "x": 1.132, "y": 1.132, "curve": "stepped"}, {"time": 8.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.3333, "x": 1.067, "y": 1.067, "curve": 0.363, "c2": 0.44, "c3": 0.724, "c4": 0.88}, {"time": 12, "x": 1.129, "y": 1.129}]}, "all18": {"rotate": [{"angle": 1.83, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 6.44, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 1.83, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 6.44, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 1.83, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 6.44, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "angle": 1.83, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": 6.44, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "angle": 1.83, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": 6.44, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 10, "angle": 1.83, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 10.3333, "curve": 0.25, "c3": 0.75}, {"time": 11.3333, "angle": 6.44, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 12, "angle": 1.83}]}, "all3": {"translate": [{"y": -1.71, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "y": -1.89, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 0.83, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "y": -1.71, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "y": -1.89, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": 0.83, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "y": -1.71, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "y": -1.89, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": 0.83, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 6, "y": -1.71, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 6.1667, "y": -1.89, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "x": 0.83, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 8, "y": -1.71, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 8.1667, "y": -1.89, "curve": 0.25, "c3": 0.75}, {"time": 9.1667, "x": 0.83, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 10, "y": -1.71, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 10.1667, "y": -1.89, "curve": 0.25, "c3": 0.75}, {"time": 11.1667, "x": 0.83, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 12, "y": -1.71}]}, "all5": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": -7.54, "y": 5.22, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -7.54, "y": 5.22, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": -7.54, "y": 5.22, "curve": 0.25, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7, "x": -7.54, "y": 5.22, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9, "x": -7.54, "y": 5.22, "curve": 0.25, "c3": 0.75}, {"time": 10, "curve": 0.25, "c3": 0.75}, {"time": 11, "x": -7.54, "y": 5.22, "curve": 0.25, "c3": 0.75}, {"time": 12}]}, "all13": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -6.5, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -6.5, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -6.5, "curve": 0.25, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -6.5, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9, "angle": -6.5, "curve": 0.25, "c3": 0.75}, {"time": 10, "curve": 0.25, "c3": 0.75}, {"time": 11, "angle": -6.5, "curve": 0.25, "c3": 0.75}, {"time": 12}]}, "all14": {"rotate": [{"angle": -1.84, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -6.5, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -1.84, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -6.5, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -1.84, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -6.5, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "angle": -1.84, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -6.5, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "angle": -1.84, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "angle": -6.5, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 10, "angle": -1.84, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 10.3333, "curve": 0.25, "c3": 0.75}, {"time": 11.3333, "angle": -6.5, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 12, "angle": -1.84}]}, "all15": {"rotate": [{"angle": -4.66, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -6.5, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -4.66, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -6.5, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -4.66, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": -6.5, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "angle": -4.66, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": -6.5, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "angle": -4.66, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 8.6667, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "angle": -6.5, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 10, "angle": -4.66, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 10.6667, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "angle": -6.5, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 12, "angle": -4.66}]}, "all16": {"translate": [{"y": 0.75, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "y": 4.53, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 2, "y": 0.75, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 2.2333, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "y": 4.53, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 4, "y": 0.75, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 4.2333, "curve": 0.25, "c3": 0.75}, {"time": 5.2333, "y": 4.53, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 6, "y": 0.75, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 6.2333, "curve": 0.25, "c3": 0.75}, {"time": 7.2333, "y": 4.53, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 8, "y": 0.75, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 8.2333, "curve": 0.25, "c3": 0.75}, {"time": 9.2333, "y": 4.53, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 10, "y": 0.75, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 10.2333, "curve": 0.25, "c3": 0.75}, {"time": 11.2333, "y": 4.53, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 12, "y": 0.75}]}, "all17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 6.44, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 6.44, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 6.44, "curve": 0.25, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": 6.44, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9, "angle": 6.44, "curve": 0.25, "c3": 0.75}, {"time": 10, "curve": 0.25, "c3": 0.75}, {"time": 11, "angle": 6.44, "curve": 0.25, "c3": 0.75}, {"time": 12}]}, "all19": {"rotate": [{"angle": 4.61, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 6.44, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 4.61, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 6.44, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 4.61, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": 6.44, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "angle": 4.61, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": 6.44, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "angle": 4.61, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 8.6667, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "angle": 6.44, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 10, "angle": 4.61, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 10.6667, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "angle": 6.44, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 12, "angle": 4.61}]}}, "path": {"kunyao": {"position": [{"position": 0.2425, "curve": 0.25, "c3": 0.75}, {"time": 4, "position": 0.6582}]}}}, "yz_bg_win": {"slots": {"yaoz_bg_4": {"color": [{"color": "ffffffd4", "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.4667, "color": "ffffffff"}]}, "yaoz_bg_04": {"color": [{"color": "ffffff2a", "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.4667, "color": "ffffff00"}]}, "yaoz_bg_02": {"color": [{"color": "ffffffe6", "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.3333, "color": "ffffffff"}]}, "yaoz_bg_2": {"color": [{"color": "ffffff18", "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.3333, "color": "ffffff00"}]}, "yaoz_bg_05": {"color": [{"color": "ffffff00"}]}, "yaoz_bg_3": {"color": [{"color": "ffffff2a"}]}, "yaoz_bg_03": {"color": [{"color": "ffffffd4"}]}}, "bones": {"all3": {"translate": [{"curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.1667, "y": -10.37, "curve": 0.311, "c2": 0.25, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "y": 24.39, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5667, "y": -9.11, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "y": 24.39, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.8667, "y": -9.11, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "all5": {"translate": [{"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -17.92, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 6.87, "y": -0.23, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": -17.92, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 8.99, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": -17.92, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "all7": {"rotate": [{"angle": -0.44}]}, "all8": {"rotate": [{"angle": -0.82}]}, "all9": {"rotate": [{"angle": -0.08}]}, "all10": {"rotate": [{"angle": -2.82}]}, "all11": {"rotate": [{"angle": 1.82}]}, "all12": {"rotate": [{"angle": -0.68}]}, "all13": {"rotate": [{"time": 0.2667, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.3667, "angle": -98.62, "curve": 0.341, "c2": 0.36, "c3": 0.675, "c4": 0.69}, {"time": 0.6, "angle": -90.68, "curve": 0.349, "c2": 0.4, "c3": 0.684, "c4": 0.74}, {"time": 0.7667, "angle": -100.08, "curve": 0.25, "c3": 0.75}, {"time": 0.9667}]}, "all14": {"rotate": [{"time": 0.3, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.4, "angle": -98.62, "curve": 0.341, "c2": 0.36, "c3": 0.675, "c4": 0.69}, {"time": 0.6, "angle": -90.68, "curve": 0.349, "c2": 0.4, "c3": 0.684, "c4": 0.74}, {"time": 0.8, "angle": -100.08, "curve": 0.25, "c3": 0.75}, {"time": 0.9667}]}, "all17": {"rotate": [{"time": 0.2667, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.3667, "angle": 90.45, "curve": 0.341, "c2": 0.36, "c3": 0.675, "c4": 0.69}, {"time": 0.6, "angle": 85.74, "curve": 0.349, "c2": 0.4, "c3": 0.684, "c4": 0.74}, {"time": 0.7667, "angle": 94.25, "curve": 0.25, "c3": 0.75}, {"time": 0.9667}]}, "all18": {"rotate": [{"time": 0.3, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.4, "angle": 90.45, "curve": 0.341, "c2": 0.36, "c3": 0.675, "c4": 0.69}, {"time": 0.6, "angle": 85.74, "curve": 0.349, "c2": 0.4, "c3": 0.684, "c4": 0.74}, {"time": 0.8, "angle": 94.25, "curve": 0.25, "c3": 0.75}, {"time": 0.9667}]}, "target1": {"translate": [{"time": 0.1667, "curve": 0.311, "c2": 0.25, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "y": 20.88, "curve": 0.327, "c2": 0.31, "c3": 0.685, "c4": 0.72}, {"time": 0.4667, "y": -0.99, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.6333, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.7333, "y": 20.88, "curve": 0.324, "c2": 0.3, "c3": 0.666, "c4": 0.66}, {"time": 0.8, "y": -1.17, "curve": 0.343, "c2": 0.36, "c3": 0.68, "c4": 0.71}, {"time": 0.8667}]}, "target3": {"translate": [{"time": 0.1667, "curve": 0.311, "c2": 0.25, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "y": 20.88, "curve": 0.327, "c2": 0.31, "c3": 0.685, "c4": 0.72}, {"time": 0.4667, "y": -0.99, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.6333, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.7333, "y": 20.88, "curve": 0.324, "c2": 0.3, "c3": 0.666, "c4": 0.66}, {"time": 0.8, "y": -1.17, "curve": 0.343, "c2": 0.36, "c3": 0.68, "c4": 0.71}, {"time": 0.8667}]}, "all16": {"translate": [{"y": -0.61, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 0.2, "y": -4.68, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "y": 2.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "y": -4.68, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "y": 2.2, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "y": -4.68, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1, "y": -0.61}]}, "all27": {"translate": [{"x": 0.39, "curve": 0.296, "c2": 0.19, "c3": 0.64, "c4": 0.57}, {"time": 1, "x": 2.34}], "scale": [{"x": 1.004, "y": 1.004, "curve": 0.296, "c2": 0.19, "c3": 0.64, "c4": 0.57}, {"time": 1, "x": 1.025, "y": 1.025}]}, "all28": {"translate": [{"x": 2.88, "curve": 0.334, "c2": 0.33, "c3": 0.677, "c4": 0.7}, {"time": 1, "x": 5.28}], "scale": [{"x": 1.052, "y": 1.052, "curve": 0.334, "c2": 0.33, "c3": 0.677, "c4": 0.7}, {"time": 1, "x": 1.095, "y": 1.095}]}, "all22": {"translate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1, "x": -3.68}], "scale": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1, "x": 1.039, "y": 1.039}]}, "all23": {"translate": [{"x": -9.95, "curve": 0.344, "c2": 0.37, "c3": 0.687, "c4": 0.73}, {"time": 1, "x": -16.22}], "scale": [{"x": 1.107, "y": 1.107, "curve": 0.344, "c2": 0.37, "c3": 0.687, "c4": 0.73}, {"time": 1, "x": 1.174, "y": 1.174}]}, "all26": {"translate": [{"x": 4.74, "curve": 0.359, "c2": 0.43, "c3": 0.702, "c4": 0.8}, {"time": 1, "x": 6.76}], "scale": [{"x": 1.05, "y": 1.05, "curve": 0.359, "c2": 0.43, "c3": 0.702, "c4": 0.8}, {"time": 1, "x": 1.072, "y": 1.072}]}, "all24": {"translate": [{"x": -9.13, "curve": 0.362, "c2": 0.64, "c3": 0.697}, {"time": 0.4333, "x": -9.61, "curve": "stepped"}, {"time": 0.4667, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 1, "x": -0.67}], "scale": [{"x": 1.1, "y": 1.1, "curve": 0.362, "c2": 0.64, "c3": 0.697}, {"time": 0.4333, "x": 1.105, "y": 1.105, "curve": "stepped"}, {"time": 0.4667, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 1, "x": 1.007, "y": 1.007}]}, "all25": {"translate": [{"x": -3.37, "curve": 0.329, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 0.4667, "x": -4.86, "curve": 0.336, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 1, "x": -6.56}], "scale": [{"x": 1.037, "y": 1.037, "curve": 0.329, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 0.4667, "x": 1.053, "y": 1.053, "curve": 0.336, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 1, "x": 1.072, "y": 1.072}]}, "all29": {"translate": [{"x": 7.11, "curve": 0.354, "c2": 0.65, "c3": 0.689}, {"time": 0.3, "x": 7.29, "curve": "stepped"}, {"time": 0.3333, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 1, "x": 0.71}], "scale": [{"x": 1.129, "y": 1.129, "curve": 0.354, "c2": 0.65, "c3": 0.689}, {"time": 0.3, "x": 1.132, "y": 1.132, "curve": "stepped"}, {"time": 0.3333, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 1, "x": 1.013, "y": 1.013}]}}}}}