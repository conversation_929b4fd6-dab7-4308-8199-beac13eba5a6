{"skeleton": {"hash": "4t2ITeFQOi8DGG30aNPHg/wVqEI", "spine": "3.8.75", "x": -1, "y": -1.81, "width": 360, "height": 265.81, "images": "./images/", "audio": "D:/spine/二柱"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "x": 310.23, "y": 9.96}, {"name": "201", "parent": "bone", "x": -8.85, "y": 44.99}, {"name": "203", "parent": "201", "x": -2.53, "y": 56.88}, {"name": "205", "parent": "203", "x": -17.7, "y": 37.92}, {"name": "bone2", "parent": "root", "x": 16.04, "y": 70.75}, {"name": "101", "parent": "bone2", "x": 41.08, "y": 34.76}, {"name": "103", "parent": "101", "x": -4.42, "y": 48.66}, {"name": "105", "parent": "103", "x": -7.58, "y": 46.77}, {"name": "bone3", "parent": "root", "x": 159.32, "y": 85.51}], "slots": [{"name": "101", "bone": "101", "attachment": "101"}, {"name": "102", "bone": "103", "attachment": "102"}, {"name": "103", "bone": "105", "attachment": "103"}, {"name": "201", "bone": "201", "attachment": "201"}, {"name": "202", "bone": "203", "attachment": "202"}, {"name": "203", "bone": "205", "attachment": "203"}, {"name": "guang", "bone": "bone3", "attachment": "guang", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"201": {"201": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [57.62, -55.95, -97.38, -55.95, -97.38, 82.05, 57.62, 82.05], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 155, "height": 138}}, "202": {"202": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [58.14, -34.83, -69.86, -34.83, -69.86, 66.17, 58.14, 66.17], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 128, "height": 101}}, "guang": {"guang": {"type": "mesh", "color": "ffffffc7", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [198.97, -87.32, -159.03, -87.32, -159.03, 175.68, 198.97, 175.68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 358, "height": 263}}, "101": {"101": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [57.88, -34.51, -58.12, -34.51, -58.12, 55.49, 57.88, 55.49], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 116, "height": 90}}, "102": {"102": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [21.3, -22.17, -35.7, -22.17, -35.7, 45.83, 21.3, 45.83], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 57, "height": 68}}, "103": {"103": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.89, -21.94, -35.11, -21.94, -35.11, 63.06, 23.89, 63.06], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 59, "height": 85}}, "203": {"203": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [48.84, -16.75, -26.16, -16.75, -26.16, 65.25, 48.84, 65.25], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 75, "height": 82}}}}], "animations": {"tu_teng": {"slots": {"guang": {"color": [{"color": "ffffff00"}]}}}, "tu_teng_jiesuo": {"slots": {"guang": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.1, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "color": "ffffff8a", "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "color": "ffffff00"}]}}, "bones": {"105": {"translate": [{"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "y": 14, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": 1.12, "y": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "x": 0.97, "y": 0.96, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "x": 1.04, "y": 1.02, "curve": 0.25, "c3": 0.75}, {"time": 1.1}]}, "201": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "y": 34, "curve": 0.25, "c3": 0.75}, {"time": 0.2}], "scale": [{"x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 1.285, "y": 1.205, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 0.96, "y": 0.92, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "203": {"translate": [{"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "y": 26, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 1.087, "y": 1.167, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 0.94, "y": 0.92, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "205": {"translate": [{"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "y": 14, "curve": 0.25, "c3": 0.75}, {"time": 0.6}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4667, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 1.16, "y": 1.12, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 0.97, "y": 0.96, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": 1.06, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 0.8}]}, "103": {"translate": [{"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "y": 18, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5667, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 1.187, "y": 1.167, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 0.96, "y": 0.96, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1.04, "y": 1.02, "curve": 0.25, "c3": 0.75}, {"time": 0.9}]}, "101": {"translate": [{"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "y": 20, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3667, "x": 0, "y": 0, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.4667, "x": 1.225, "y": 1.165, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 0.98, "y": 0.96, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 1.04, "y": 1.02, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone3": {"scale": [{"time": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": 1.04, "y": 1.06}]}}}}}