{"skeleton": {"hash": "L48DF/UxkaF9HIB5PWaSL9H9qd0=", "spine": "3.8.75", "x": -273.48, "y": 44.86, "width": 675.38, "height": 448, "images": "./images/", "audio": "D:/spine导出/z主角换皮动画/哪吒/哪吒/./images/音效"}, "bones": [{"name": "root"}, {"name": "nzbone", "parent": "root"}, {"name": "nzbone2", "parent": "nzbone", "x": 12.08, "y": 211.52, "color": "ff0000ff"}, {"name": "nzbone3", "parent": "nzbone2", "length": 55.44, "rotation": 3.04, "color": "003dffff"}, {"name": "nzbone4", "parent": "nzbone3", "length": 27.39, "rotation": 96.69, "x": 0.26, "y": -1.33}, {"name": "nzbone5", "parent": "nzbone4", "length": 61.81, "rotation": 5.3, "x": 26.82, "y": 0.44}, {"name": "nzbone6", "parent": "nzbone5", "length": 22.43, "rotation": -19.87, "x": 61.42, "y": -0.19}, {"name": "nzbone7", "parent": "nzbone6", "length": 28.12, "rotation": -5.56, "x": 22.11, "y": 0.05}, {"name": "nzbone8", "parent": "nzbone7", "x": 7.56, "y": -12.01, "color": "abe323ff"}, {"name": "nzbone9", "parent": "nzbone7", "x": 47.73, "y": -13.46}, {"name": "nzbone18", "parent": "nzbone5", "x": 9.7, "y": -11.21, "color": "abe323ff"}, {"name": "nzbone19", "parent": "nzbone5", "x": 40.17, "y": -10.81, "color": "abe323ff"}, {"name": "nzbone44", "parent": "nzbone3", "length": 33.33, "rotation": -91.41, "x": 0.17, "y": -2.27}, {"name": "nzbone73", "parent": "nzbone44", "x": 22.75, "y": 10.33}, {"name": "nzbone67", "parent": "nzbone73", "length": 54.61, "rotation": 15.63, "x": -0.52, "y": 0.18, "color": "f3a9ffff"}, {"name": "nzbone68", "parent": "nzbone67", "length": 41.24, "rotation": -0.9, "x": 54.28, "y": 0.38, "color": "f3a9ffff"}, {"name": "nzbone65", "parent": "nzbone", "x": -102.95, "y": 11.44}, {"name": "nzbone80", "parent": "nzbone65", "length": 35.53, "rotation": 1.23, "x": 159.72, "y": -9.04}, {"name": "nzbone81", "parent": "nzbone80", "length": 29.8, "rotation": 88.77, "x": 0.37, "y": -0.77}, {"name": "zj1", "parent": "nzbone81", "rotation": -163.64, "x": 94.42, "y": 5.55, "color": "f3a9ffff"}, {"name": "nzbone69", "parent": "zj1", "length": 18.9, "rotation": 7.98, "x": -0.11, "y": 0.03, "color": "f3a9ffff"}, {"name": "nzbone66", "parent": "nzbone44", "x": 16.76, "y": -21.59}, {"name": "nzbone70", "parent": "nzbone66", "length": 61.3, "rotation": -27.64, "x": 0.07, "y": -0.06, "color": "f3a9ffff"}, {"name": "nzbone71", "parent": "nzbone70", "length": 50.83, "rotation": 3.91, "x": 61.33, "y": -0.15, "color": "f3a9ffff"}, {"name": "nzbone78", "parent": "nzbone65", "length": 32.48, "rotation": -1.35, "x": 46.48, "y": -11.7}, {"name": "nzbone79", "parent": "nzbone78", "length": 34.38, "rotation": 91.98, "x": -0.51, "y": 5.34}, {"name": "yj1", "parent": "nzbone79", "rotation": 157.27, "x": 84.56, "y": -2.86, "color": "f3a9ffff"}, {"name": "nzbone72", "parent": "yj1", "length": 25.34, "rotation": -2.83, "x": -0.93, "y": -0.04, "color": "f3a9ffff"}, {"name": "nzbone10", "parent": "nzbone7", "length": 33.31, "rotation": 12.59, "x": 95.46, "y": 26.87}, {"name": "nzbone11", "parent": "nzbone7", "length": 26.87, "rotation": 29.69, "x": 127.96, "y": 34.13}, {"name": "nzbone12", "parent": "nzbone7", "length": 12.78, "rotation": -56.21, "x": 151.3, "y": 47.44}, {"name": "nzbone13", "parent": "nzbone7", "length": 10.82, "rotation": 5.36, "x": 158.41, "y": 36.82}, {"name": "nzbone14", "parent": "nzbone7", "length": 18.84, "rotation": 23.04, "x": 99.22, "y": -2.74}, {"name": "nzbone15", "parent": "nzbone7", "length": 15.77, "rotation": 40.58, "x": 116.56, "y": 4.64}, {"name": "nzbone16", "parent": "nzbone7", "length": 18.91, "rotation": 45.16, "x": 72.14, "y": 62.25}, {"name": "nzbone17", "parent": "nzbone7", "length": 14.24, "rotation": -1.16, "x": 85.48, "y": 75.65}, {"name": "nzbone20", "parent": "nzbone5", "length": 37.97, "rotation": -127.98, "x": 31.44, "y": -35.9}, {"name": "nzbone21", "parent": "nzbone20", "length": 38.65, "rotation": 5.4, "x": 37.97}, {"name": "nzbone22", "parent": "nzbone21", "length": 15.82, "rotation": -12.65, "x": 38.65, "transform": "noRotationOrReflection"}, {"name": "a14", "parent": "nzbone22", "length": 55.91, "rotation": 107.57, "x": -130.31, "y": -17.01, "color": "ff1900ff"}, {"name": "nzbone23", "parent": "nzbone5", "length": 38.43, "rotation": 135.29, "x": 60.29, "y": 42.51}, {"name": "nzbone24", "parent": "nzbone23", "length": 47.88, "rotation": 1, "x": 38.43}, {"name": "nzbone25", "parent": "nzbone24", "length": 26.97, "rotation": -116.91, "x": 47.88, "transform": "noRotationOrReflection"}, {"name": "a7", "parent": "nzbone25", "length": 84.82, "rotation": 87.49, "x": 21.5, "y": 22.59}, {"name": "nzbone45", "parent": "nzbone44", "length": 23.86, "rotation": 50.96, "x": 1.09, "y": 28.24}, {"name": "nzbone46", "parent": "nzbone45", "length": 15.72, "rotation": 5.74, "x": 23.86}, {"name": "nzbone47", "parent": "nzbone44", "length": 29.66, "rotation": -55.2, "x": -3.13, "y": -33.7}, {"name": "nzbone48", "parent": "nzbone47", "length": 22.17, "rotation": -0.03, "x": 29.66}, {"name": "a8", "parent": "a7", "x": 231.65, "y": 1.07}, {"name": "nzbone26", "parent": "nzbone5", "length": 71.28, "rotation": -73.82, "x": 45.92, "y": -23.63}, {"name": "nzbone27", "parent": "nzbone26", "length": 77.39, "rotation": 25.28, "x": 71.28}, {"name": "nzbone28", "parent": "nzbone27", "length": 45.64, "rotation": 9.94, "x": 77.39}, {"name": "nzbone29", "parent": "nzbone5", "length": 83.46, "rotation": 45.94, "x": 59.04, "y": 19.08}, {"name": "nzbone30", "parent": "nzbone29", "length": 80.47, "rotation": -27.02, "x": 83.46}, {"name": "nzbone31", "parent": "nzbone30", "length": 46.57, "rotation": -7.88, "x": 80.47}, {"name": "nzbone32", "parent": "nzbone5", "length": 66.08, "rotation": 102.22, "x": 39.43, "y": 13.36}, {"name": "nzbone33", "parent": "nzbone32", "length": 79.76, "rotation": -33.98, "x": 66.08}, {"name": "nzbone34", "parent": "nzbone33", "length": 57.57, "rotation": -22.41, "x": 79.76}, {"name": "nzbone35", "parent": "nzbone5", "length": 62.66, "rotation": -132.25, "x": 28.5, "y": -0.58}, {"name": "nzbone36", "parent": "nzbone35", "length": 69.71, "rotation": 33.77, "x": 62.66}, {"name": "nzbone37", "parent": "nzbone36", "length": 53.88, "rotation": 16.03, "x": 69.71}, {"name": "nzbone38", "parent": "nzbone5", "length": 31.02, "rotation": 101.15, "x": 74.41, "y": 43.68, "color": "006dffff"}, {"name": "nzbone39", "parent": "nzbone38", "length": 20.94, "rotation": -13.38, "x": 31.02, "color": "006dffff"}, {"name": "nzbone40", "parent": "nzbone5", "length": 17.03, "rotation": -124.95, "x": 52.86, "y": -36.57, "color": "006dffff"}, {"name": "nzbone41", "parent": "nzbone40", "length": 12.99, "rotation": 19.92, "x": 17.03, "color": "006dffff"}, {"name": "nzbone42", "parent": "nzbone5", "length": 25.8, "rotation": 176.38, "x": 38.83, "y": 45.09, "color": "006dffff"}, {"name": "nzbone43", "parent": "nzbone42", "length": 15.03, "rotation": -2.53, "x": 25.8, "color": "006dffff"}, {"name": "nzbone49", "parent": "nzbone5", "length": 21.16, "rotation": -150.48, "x": 31.81, "y": -41.91, "color": "006dffff"}, {"name": "nzbone50", "parent": "nzbone49", "length": 13.6, "rotation": 8.77, "x": 21, "y": 0.16, "color": "006dffff"}, {"name": "nzbone51", "parent": "nzbone", "x": -199.16, "y": 155.17}, {"name": "nzbone52", "parent": "nzbone51", "x": 33.59, "y": 12.31, "color": "ff7f00ff"}, {"name": "nzbone53", "parent": "nzbone51", "x": 118.36, "y": 41.54, "color": "ff7f00ff"}, {"name": "nzbone54", "parent": "nzbone51", "x": 161.01, "y": 99.31, "color": "ff7f00ff"}, {"name": "nzbone55", "parent": "nzbone51", "x": 108.96, "y": 162.24, "color": "ff7f00ff"}, {"name": "nzbone56", "parent": "nzbone51", "x": 44.09, "y": 172.13, "color": "ff7f00ff"}, {"name": "nzbone57", "parent": "nzbone51", "x": 47.77, "y": 230.23, "color": "ff7f00ff"}, {"name": "nzbone58", "parent": "nzbone51", "x": 135, "y": 222.26, "color": "ff7f00ff"}, {"name": "nzbone59", "parent": "nzbone51", "x": 235.37, "y": 240.34, "color": "ff7f00ff"}, {"name": "nzbone60", "parent": "nzbone51", "x": 291.27, "y": 159.34, "color": "ff7f00ff"}, {"name": "nzbone61", "parent": "nzbone51", "x": 264.51, "y": 90.97, "color": "ff7f00ff"}, {"name": "nzbone62", "parent": "nzbone51", "x": 299.42, "y": 40.82, "color": "ff7f00ff"}, {"name": "nzbone63", "parent": "nzbone51", "x": 331.04, "y": 85.32, "color": "ff7f00ff"}, {"name": "nzbone64", "parent": "nzbone51", "x": 373.83, "y": 80.41, "color": "ff7f00ff"}, {"name": "nzbone74", "parent": "nzbone66", "length": 62.19, "rotation": -44.15, "x": 0.1, "y": -0.76}, {"name": "nzbone75", "parent": "nzbone74", "length": 55.12, "rotation": 38.93, "x": 62.19, "color": "abe323ff"}, {"name": "nzbone76", "parent": "nzbone73", "length": 55.64, "rotation": 33.58, "x": 0.01, "y": -0.36}, {"name": "nzbone77", "parent": "nzbone76", "length": 45.21, "rotation": -39.58, "x": 55.64, "color": "abe323ff"}, {"name": "a23", "parent": "nzbone79", "x": 27.77, "y": -11.01}, {"name": "a22", "parent": "nzbone81", "x": 30.56, "y": 13.75}, {"name": "yj", "parent": "nzbone79", "rotation": 153.36, "x": 131.41, "y": -22.64, "color": "f3a9ffff"}, {"name": "zj", "parent": "nzbone81", "rotation": -162.74, "x": 133.56, "y": 17.44, "color": "f3a9ffff"}, {"name": "a11", "parent": "nzbone", "length": 84.82, "rotation": -12.75, "x": 179.37, "y": 203.08}, {"name": "a12", "parent": "a11", "x": 231.65, "y": 1.07}, {"name": "a13", "parent": "a12", "length": 99.74, "rotation": 173.17, "x": -6.02, "y": -3.4}, {"name": "a15", "parent": "a12", "length": 99.74, "rotation": 177.23, "x": -6.02, "y": -3.4, "color": "fdff00ff"}, {"name": "a16", "parent": "a12", "length": 99.74, "rotation": 169.23, "x": -6.02, "y": -3.4, "color": "fdff00ff"}, {"name": "nzbone82", "parent": "nzbone", "x": -199.16, "y": 155.17}, {"name": "nzbone83", "parent": "nzbone82", "x": 33.59, "y": 12.31, "color": "ff7f00ff"}, {"name": "nzbone84", "parent": "nzbone82", "x": 118.36, "y": 41.54, "color": "ff7f00ff"}, {"name": "nzbone85", "parent": "nzbone82", "x": 161.01, "y": 99.31, "color": "ff7f00ff"}, {"name": "nzbone86", "parent": "nzbone82", "x": 108.96, "y": 162.24, "color": "ff7f00ff"}, {"name": "nzbone87", "parent": "nzbone82", "x": 44.09, "y": 172.13, "color": "ff7f00ff"}, {"name": "nzbone88", "parent": "nzbone82", "x": 47.77, "y": 230.23, "color": "ff7f00ff"}, {"name": "nzbone89", "parent": "nzbone82", "x": 135, "y": 222.26, "color": "ff7f00ff"}, {"name": "nzbone90", "parent": "nzbone82", "x": 235.37, "y": 240.34, "color": "ff7f00ff"}, {"name": "nzbone91", "parent": "nzbone82", "x": 291.27, "y": 159.34, "color": "ff7f00ff"}, {"name": "nzbone92", "parent": "nzbone82", "x": 264.51, "y": 90.97, "color": "ff7f00ff"}, {"name": "nzbone93", "parent": "nzbone82", "x": 299.42, "y": 40.82, "color": "ff7f00ff"}, {"name": "nzbone94", "parent": "nzbone82", "x": 331.04, "y": 85.32, "color": "ff7f00ff"}, {"name": "nzbone95", "parent": "nzbone82", "x": 373.83, "y": 80.41, "color": "ff7f00ff"}, {"name": "tx", "parent": "a8", "rotation": 29.42, "x": -16.2, "y": -14.33, "scaleX": 1.9509, "scaleY": 1.9509}, {"name": "huo<PERSON>", "parent": "tx", "rotation": -133.75, "x": -8.27, "y": 11.04, "scaleX": -0.7962, "scaleY": 0.6102, "shearY": 17.26}, {"name": "huo", "parent": "huo<PERSON>", "rotation": 180, "x": 6.09, "y": -0.16, "scaleX": -0.4347, "scaleY": 0.2702, "shearY": 8.2}, {"name": "huo2", "parent": "huo<PERSON>", "rotation": 180, "x": -1.98, "y": -3.64, "scaleX": -0.4347, "scaleY": 0.4347}, {"name": "huo3", "parent": "huo<PERSON>", "rotation": 180, "x": 10.66, "y": -15.47, "scaleX": -0.4347, "scaleY": 0.4347}, {"name": "huo4", "parent": "huo<PERSON>", "rotation": 180, "x": -0.31, "y": -16.49, "scaleX": -0.4347, "scaleY": 0.4347}, {"name": "tx2", "parent": "tx", "rotation": 37.13, "x": -14.25, "y": 9.89, "scaleX": 0.4915, "scaleY": 0.4915}, {"name": "tx3", "parent": "tx", "rotation": 37.13, "x": -14.25, "y": 9.89, "scaleX": 0.4915, "scaleY": 0.4915}, {"name": "zong", "parent": "root", "x": -28.7, "y": -66.19}, {"name": "golw_add/glow_01", "parent": "zong", "x": 16.7, "y": 262.68, "scaleX": 3.3181, "scaleY": 3.3181}, {"name": "heilizi/lizi", "parent": "zong", "x": -369.45, "y": 138.29, "scaleX": 1.6591, "scaleY": 1.6591}, {"name": "heilizi/lizi2", "parent": "zong", "x": -369.45, "y": 138.29, "scaleX": 1.6591, "scaleY": 1.6591}, {"name": "heilizi/lizi3", "parent": "zong", "x": -369.45, "y": 138.29, "scaleX": 1.6591, "scaleY": 1.6591}, {"name": "hou/huoyan_0001", "parent": "zong", "x": 31.86, "y": 151.86, "scaleX": 9.9544, "scaleY": 9.9544}, {"name": "huo-ho<PERSON>an", "parent": "zong", "x": 93.13, "y": 341.38, "scaleX": 1.6591, "scaleY": 1.6591}, {"name": "huo<PERSON><PERSON><PERSON><PERSON>", "parent": "huo-ho<PERSON>an", "x": 13.89, "y": -90.57, "scaleX": 2, "scaleY": 1.4793}, {"name": "huoyankuosna2", "parent": "huo-ho<PERSON>an", "rotation": 124.8, "x": -41.51, "y": -3.26, "scaleX": 2, "scaleY": 1.4793}, {"name": "huoyankuosna3", "parent": "huo-ho<PERSON>an", "rotation": -80, "x": -137.67, "y": -89.81, "scaleX": 1.4533, "scaleY": 1.5942}, {"name": "lizizong", "parent": "zong", "x": 100.46, "y": 116.72, "scaleX": 1.6591, "scaleY": 1.6591}, {"name": "huoxing3", "parent": "lizizong", "x": -38.26, "y": 4.88}, {"name": "huoxing-add/huoxing_1", "parent": "huoxing3", "x": -18.72, "y": 74.89, "scaleX": 2, "scaleY": 2}, {"name": "huoxing-add/huoxing_2", "parent": "huoxing3", "x": -18.72, "y": 74.89, "scaleX": 2, "scaleY": 2}, {"name": "huoxing-add/huoxing_3", "parent": "huoxing3", "x": -18.72, "y": 74.89, "scaleX": 2, "scaleY": 2}, {"name": "huoxing-add/huoxing_4", "parent": "huoxing3", "x": -18.72, "y": 74.89, "scaleX": 2, "scaleY": 2}, {"name": "huoxing-add/huoxing_5", "parent": "huoxing3", "x": -18.72, "y": 74.89, "scaleX": 2, "scaleY": 2}, {"name": "huoxing-add/huoxing_6", "parent": "huoxing3", "x": -18.72, "y": 74.89, "scaleX": 2, "scaleY": 2}, {"name": "lizi2tiao", "parent": "lizizong", "x": -66.49, "y": 210.11, "scaleX": 1.7, "scaleY": 1.7}, {"name": "xlt", "parent": "lizi2tiao", "x": 135.56, "y": 72.37, "scaleX": 2, "scaleY": 2}, {"name": "xlt2", "parent": "lizi2tiao", "x": 135.56, "y": 72.37, "scaleX": 2, "scaleY": 2}, {"name": "xlt3", "parent": "lizi2tiao", "x": 135.56, "y": 72.37, "scaleX": 2, "scaleY": 2}, {"name": "xlt4", "parent": "lizi2tiao", "x": 135.56, "y": 72.37, "scaleX": 2, "scaleY": 2}, {"name": "xlt5", "parent": "lizi2tiao", "x": 135.56, "y": 72.37, "scaleX": 2, "scaleY": 2}, {"name": "xlt6", "parent": "lizi2tiao", "x": 135.56, "y": 72.37, "scaleX": 2, "scaleY": 2}, {"name": "xlt7", "parent": "lizi2tiao", "x": 135.56, "y": 72.37, "scaleX": 2, "scaleY": 2}, {"name": "xlt8", "parent": "lizi2tiao", "x": 135.56, "y": 72.37, "scaleX": 2, "scaleY": 2}, {"name": "xlt9", "parent": "lizi2tiao", "x": 135.56, "y": 72.37, "scaleX": 2, "scaleY": 2}, {"name": "xlt10", "parent": "lizi2tiao", "x": 135.56, "y": 72.37, "scaleX": 2, "scaleY": 2}, {"name": "xlt11", "parent": "lizi2tiao", "x": 135.56, "y": 72.37, "scaleX": 2, "scaleY": 2}, {"name": "xlt12", "parent": "lizi2tiao", "x": 135.56, "y": 72.37, "scaleX": 2, "scaleY": 2}, {"name": "xlt13", "parent": "lizi2tiao", "x": 135.56, "y": 72.37, "scaleX": 2, "scaleY": 2}, {"name": "xlt14", "parent": "lizi2tiao", "x": 135.56, "y": 72.37, "scaleX": 2, "scaleY": 2}, {"name": "xlt15", "parent": "lizi2tiao", "x": 135.56, "y": 72.37, "scaleX": 2, "scaleY": 2}, {"name": "xlt16", "parent": "lizi2tiao", "x": 135.56, "y": 72.37, "scaleX": 2, "scaleY": 2}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parent": "zong", "x": 39.41, "y": 304.64, "scaleX": 2, "scaleY": 2}, {"name": "ks", "parent": "zong", "x": 7.39, "y": 247.22, "scaleX": 3.3181, "scaleY": 3.3181}, {"name": "nq02", "parent": "zong", "x": 60.03, "y": 229.39, "scaleX": 6.6363, "scaleY": 6.6363}, {"name": "nq2", "parent": "zong", "x": -55.7, "y": 229.39, "scaleX": 6.6363, "scaleY": 6.6363}, {"name": "daoqi", "parent": "root", "x": 205.79, "y": 145.32, "scaleX": 1.6509, "scaleY": 1.6509}, {"name": "chong<PERSON>", "parent": "root", "x": 430.57, "y": -313.49}, {"name": "hundun", "parent": "chong<PERSON>", "x": -369.38, "y": 419.04, "scaleX": -1.5933, "scaleY": 1.2763}, {"name": "huoyan", "parent": "chong<PERSON>", "x": -464.1, "y": 419.36, "scaleX": 2.4877, "scaleY": 2.4877, "shearX": -2.69}, {"name": "a9", "parent": "nzbone80", "rotation": -30.65, "x": 56, "y": 31.28}, {"name": "tx4", "parent": "a9", "rotation": 29.42, "x": -16.2, "y": -14.33, "scaleX": 1.9509, "scaleY": 1.9509}, {"name": "huoall2", "parent": "tx4", "rotation": -133.75, "x": -8.27, "y": 11.04, "scaleX": -0.7962, "scaleY": 0.6102, "shearY": 17.26}, {"name": "huo5", "parent": "huoall2", "rotation": 180, "x": 6.09, "y": -0.16, "scaleX": -0.4347, "scaleY": 0.2702, "shearY": 8.2}, {"name": "huo6", "parent": "huoall2", "rotation": 180, "x": -1.98, "y": -3.64, "scaleX": -0.4347, "scaleY": 0.4347}, {"name": "huo7", "parent": "huoall2", "rotation": 180, "x": 10.66, "y": -15.47, "scaleX": -0.4347, "scaleY": 0.4347}, {"name": "huo8", "parent": "huoall2", "rotation": 180, "x": -0.31, "y": -16.49, "scaleX": -0.4347, "scaleY": 0.4347}, {"name": "tx5", "parent": "tx4", "rotation": 37.13, "x": -14.25, "y": 9.89, "scaleX": 0.4915, "scaleY": 0.4915}, {"name": "tx6", "parent": "tx4", "rotation": 37.13, "x": -14.25, "y": 9.89, "scaleX": 0.4915, "scaleY": 0.4915}, {"name": "a10", "parent": "nzbone78", "rotation": -28.07, "x": 56.82, "y": 33.47}, {"name": "tx7", "parent": "a10", "rotation": 29.42, "x": -16.2, "y": -14.33, "scaleX": 1.9509, "scaleY": 1.9509}, {"name": "huoall3", "parent": "tx7", "rotation": -133.75, "x": -8.27, "y": 11.04, "scaleX": -0.7962, "scaleY": 0.6102, "shearY": 17.26}, {"name": "huo9", "parent": "huoall3", "rotation": 180, "x": 6.09, "y": -0.16, "scaleX": -0.4347, "scaleY": 0.2702, "shearY": 8.2}, {"name": "huo10", "parent": "huoall3", "rotation": 180, "x": -1.98, "y": -3.64, "scaleX": -0.4347, "scaleY": 0.4347}, {"name": "huo11", "parent": "huoall3", "rotation": 180, "x": 10.66, "y": -15.47, "scaleX": -0.4347, "scaleY": 0.4347}, {"name": "huo12", "parent": "huoall3", "rotation": 180, "x": -0.31, "y": -16.49, "scaleX": -0.4347, "scaleY": 0.4347}, {"name": "tx8", "parent": "tx7", "rotation": 37.13, "x": -14.25, "y": 9.89, "scaleX": 0.4915, "scaleY": 0.4915}, {"name": "tx9", "parent": "tx7", "rotation": 37.13, "x": -14.25, "y": 9.89, "scaleX": 0.4915, "scaleY": 0.4915}, {"name": "bone", "parent": "a11", "rotation": 1.69, "x": 53.87, "y": 5.19, "scaleX": 2.433, "scaleY": 2.433}, {"name": "xuanzhuan", "parent": "root", "x": -3.43, "y": 228.56, "scaleX": 2.6008, "scaleY": 2.6008}, {"name": "jushouyun2", "parent": "zong", "x": 39.41, "y": 304.64, "scaleX": 2, "scaleY": 2}, {"name": "lighttt", "parent": "root", "y": 227.1}], "slots": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON><PERSON>", "color": "ffffff00", "blend": "additive"}, {"name": "jushouyun2", "bone": "jushouyun2", "color": "ffffff00", "blend": "additive"}, {"name": "a29", "bone": "nzbone26", "color": "ffffff00", "attachment": "a29"}, {"name": "a28", "bone": "nzbone35", "color": "ffffff00", "attachment": "a28"}, {"name": "a27", "bone": "nzbone29", "color": "ffffff00", "attachment": "a27"}, {"name": "a26", "bone": "nzbone32", "color": "ffffff00", "attachment": "a26"}, {"name": "a25", "bone": "nzbone81"}, {"name": "a24", "bone": "nzbone79"}, {"name": "a23", "bone": "a23"}, {"name": "a22", "bone": "a22"}, {"name": "a21", "bone": "nzbone44", "attachment": "a21"}, {"name": "a20", "bone": "nzbone68", "attachment": "a20"}, {"name": "a19", "bone": "nzbone67", "attachment": "a19"}, {"name": "a18", "bone": "nzbone71", "attachment": "a18"}, {"name": "a17", "bone": "nzbone70", "attachment": "a17"}, {"name": "a16", "bone": "nzbone20", "attachment": "a16"}, {"name": "a15", "bone": "nzbone21", "attachment": "a15"}, {"name": "a14", "bone": "a14", "attachment": "a14"}, {"name": "a13", "bone": "nzbone22", "attachment": "a13"}, {"name": "a12", "bone": "nzbone60", "attachment": "a12"}, {"name": "a39", "bone": "nzbone91", "color": "ff0000ff", "dark": "ff0200", "blend": "additive"}, {"name": "a30", "bone": "nzbone44", "attachment": "a21"}, {"name": "a10", "bone": "nzbone5", "attachment": "a10"}, {"name": "a9", "bone": "nzbone4", "attachment": "a9"}, {"name": "a8", "bone": "nzbone23", "attachment": "a8"}, {"name": "a11", "bone": "nzbone53", "attachment": "a11"}, {"name": "a38", "bone": "nzbone84", "color": "ff0000ff", "dark": "ff0200", "blend": "additive"}, {"name": "a4", "bone": "nzbone38", "attachment": "a4"}, {"name": "a3", "bone": "nzbone7", "attachment": "a3"}, {"name": "a2", "bone": "nzbone7", "attachment": "a2"}, {"name": "a1", "bone": "nzbone7", "attachment": "a1"}, {"name": "a0", "bone": "nzbone55", "attachment": "a0"}, {"name": "a5", "bone": "nzbone86", "color": "ff0000ff", "dark": "ff0200", "blend": "additive"}, {"name": "a7", "bone": "a7", "attachment": "a7"}, {"name": "a33", "bone": "nzbone24", "attachment": "a5"}, {"name": "a34", "bone": "a11", "color": "ffffff00"}, {"name": "a36", "bone": "a15", "color": "ffffff00", "attachment": "a6"}, {"name": "huoyan_add/huoyan_00058", "bone": "huo"}, {"name": "huoyan_add/huoyan_61", "bone": "huo5"}, {"name": "huoyan_add/huoyan_65", "bone": "huo9"}, {"name": "huoyan_add/huoyan_58", "bone": "huo2"}, {"name": "huoyan_add/huoyan_62", "bone": "huo6"}, {"name": "huoyan_add/huoyan_66", "bone": "huo10"}, {"name": "huoyan_add/huoyan_59", "bone": "huo4"}, {"name": "huoyan_add/huoyan_64", "bone": "huo8"}, {"name": "huoyan_add/huoyan_68", "bone": "huo12"}, {"name": "huoyan_add/huoyan_60", "bone": "huo3"}, {"name": "huoyan_add/huoyan_63", "bone": "huo7"}, {"name": "huoyan_add/huoyan_67", "bone": "huo11"}, {"name": "xialuo/xialuo_00000", "bone": "tx2", "blend": "additive"}, {"name": "xialuo/xialuo_1", "bone": "tx5", "blend": "additive"}, {"name": "xialuo/xialuo_3", "bone": "tx8", "blend": "additive"}, {"name": "xialuo/xialuo_0", "bone": "tx3", "blend": "additive"}, {"name": "xialuo/xialuo_2", "bone": "tx6", "blend": "additive"}, {"name": "xialuo/xialuo_4", "bone": "tx9", "blend": "additive"}, {"name": "huoyankuosna-add/huokuo_0001", "bone": "huo<PERSON><PERSON><PERSON><PERSON>", "color": "ffffff00", "blend": "additive"}, {"name": "huoyankuosna-add/huokuo_1", "bone": "huoyankuosna2", "color": "ffffff00", "blend": "additive"}, {"name": "huoyankuosna-add/huokuo_2", "bone": "huoyankuosna3", "color": "ffffff00", "blend": "additive"}, {"name": "hou/huoyan_0001", "bone": "hou/huoyan_0001", "color": "ffffff00", "blend": "additive"}, {"name": "ks/ks_01", "bone": "ks", "color": "ffffff00", "blend": "additive"}, {"name": "heilizi/lizi", "bone": "heilizi/lizi", "color": "ffffff00", "blend": "additive"}, {"name": "heilizi/lizi2", "bone": "heilizi/lizi2", "color": "ffffff00", "blend": "additive"}, {"name": "heilizi/lizi3", "bone": "heilizi/lizi3", "color": "ffffff00"}, {"name": "nq_add_02/nq_b_01", "bone": "nq02", "color": "ffffff00", "blend": "additive"}, {"name": "nq_add_02/nq_b_1", "bone": "nq2", "color": "ffffff00", "blend": "additive"}, {"name": "huoxing-add/huoxing_0001", "bone": "huoxing-add/huoxing_6", "color": "ffffff00", "blend": "additive"}, {"name": "huoxing-add/huoxing_1", "bone": "huoxing-add/huoxing_1", "color": "ffffff00", "attachment": "huoxing-add/huoxing_0021", "blend": "additive"}, {"name": "huoxing-add/huoxing_2", "bone": "huoxing-add/huoxing_2", "color": "ffffff00", "blend": "additive"}, {"name": "huoxing-add/huoxing_3", "bone": "huoxing-add/huoxing_3", "color": "ffffff00", "blend": "additive"}, {"name": "huoxing-add/huoxing_4", "bone": "huoxing-add/huoxing_4", "color": "ffffff00", "blend": "additive"}, {"name": "huoxing-add/huoxing_5", "bone": "huoxing-add/huoxing_5", "color": "ffffff00", "blend": "additive"}, {"name": "xlt_add/lizi_01", "bone": "xlt", "color": "ffffff00", "blend": "additive"}, {"name": "xlt_add/lizi_1", "bone": "xlt2", "color": "ffffff00", "blend": "additive"}, {"name": "xlt_add/lizi_2", "bone": "xlt3", "color": "ffffff00", "blend": "additive"}, {"name": "xlt_add/lizi_3", "bone": "xlt4", "color": "ffffff00", "blend": "additive"}, {"name": "xlt_add/lizi_4", "bone": "xlt5", "color": "ffffff00", "blend": "additive"}, {"name": "xlt_add/lizi_5", "bone": "xlt6", "color": "ffffff00", "blend": "additive"}, {"name": "xlt_add/lizi_6", "bone": "xlt7", "color": "ffffff00", "blend": "additive"}, {"name": "xlt_add/lizi_7", "bone": "xlt8", "color": "ffffff00", "blend": "additive"}, {"name": "xlt_add/lizi_8", "bone": "xlt9", "color": "ffffff00", "blend": "additive"}, {"name": "xlt_add/lizi_9", "bone": "xlt10", "color": "ffffff00", "blend": "additive"}, {"name": "xlt_add/lizi_10", "bone": "xlt11", "color": "ffffff00", "blend": "additive"}, {"name": "xlt_add/lizi_11", "bone": "xlt12", "color": "ffffff00", "blend": "additive"}, {"name": "xlt_add/lizi_12", "bone": "xlt13", "color": "ffffff00", "blend": "additive"}, {"name": "xlt_add/lizi_13", "bone": "xlt14", "color": "ffffff00", "blend": "additive"}, {"name": "xlt_add/lizi_14", "bone": "xlt15", "color": "ffffff00", "blend": "additive"}, {"name": "xlt_add/lizi_15", "bone": "xlt16", "color": "ffffff00", "blend": "additive"}, {"name": "golw_add/glow_01", "bone": "golw_add/glow_01", "color": "ffffff00", "blend": "additive"}, {"name": "daoqi/nanjian_zmr_dg_00051", "bone": "daoqi", "blend": "additive"}, {"name": "chongji/gsxm_hudun_00", "bone": "hundun"}, {"name": "chongji/tw_0001", "bone": "huoyan", "blend": "additive"}, {"name": "quan_chongji/quanshuaxiao_00016", "bone": "bone"}, {"name": "xuanzhuan/linxiangru_skill_xl_00000", "bone": "xuanzhuan"}, {"name": "lightttt", "bone": "lighttt", "color": "ff7100ff", "blend": "additive"}], "ik": [{"name": "yj", "order": 2, "bones": ["nzbone70"], "target": "yj", "compress": true, "stretch": true}, {"name": "yj1", "order": 3, "bones": ["nzbone71"], "target": "yj1", "compress": true, "stretch": true}, {"name": "yj2", "bones": ["nzbone74", "nzbone75"], "target": "yj1", "stretch": true}, {"name": "zj", "order": 6, "bones": ["nzbone67"], "target": "zj", "compress": true, "stretch": true}, {"name": "zj1", "order": 7, "bones": ["nzbone68"], "target": "zj1", "compress": true, "stretch": true}, {"name": "zj2", "order": 4, "bones": ["nzbone76", "nzbone77"], "target": "zj1", "bendPositive": false, "stretch": true}], "transform": [{"name": "q1", "order": 10, "bones": ["nzbone23"], "target": "nzbone18", "rotation": 135.29, "x": 50.59, "y": 53.72, "shearY": 360, "rotateMix": 0, "translateMix": 0.2, "scaleMix": 0, "shearMix": 0}, {"name": "q2", "order": 11, "bones": ["nzbone20"], "target": "nzbone19", "rotation": -127.98, "x": -8.73, "y": -25.09, "shearY": 360, "rotateMix": 0, "translateMix": 0.2, "scaleMix": 0, "shearMix": 0}, {"name": "q3", "order": 12, "bones": ["nzbone38"], "target": "nzbone18", "rotation": 101.15, "x": 64.71, "y": 54.89, "shearY": 360, "rotateMix": 0, "translateMix": 0.2, "scaleMix": 0, "shearMix": 0}, {"name": "s01", "order": 9, "bones": ["nzbone19"], "target": "nzbone18", "x": 30.47, "y": 0.4, "rotateMix": 0, "translateMix": -1, "scaleMix": 0, "shearMix": 0}, {"name": "t01", "order": 8, "bones": ["nzbone9"], "target": "nzbone8", "x": 40.16, "y": -1.45, "rotateMix": 0, "translateMix": -1, "scaleMix": 0, "shearMix": 0}, {"name": "yj", "order": 1, "bones": ["yj"], "target": "nzbone75", "rotation": -20.83, "x": 6.51, "y": 14.94, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}, {"name": "zj", "order": 5, "bones": ["zj"], "target": "nzbone77", "rotation": 21.31, "x": 7, "y": -14.62, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}], "skins": [{"name": "default", "attachments": {"chongji/gsxm_hudun_00": {"chongji/gsxm_hudun_00": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -90, -50, -90, -50, 90, 50, 90], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 180}, "chongji/gsxm_hudun_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -90, -50, -90, -50, 90, 50, 90], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 180}, "chongji/gsxm_hudun_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -90, -50, -90, -50, 90, 50, 90], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 180}, "chongji/gsxm_hudun_12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -90, -50, -90, -50, 90, 50, 90], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 180}, "chongji/gsxm_hudun_16": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -90, -50, -90, -50, 90, 50, 90], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 180}, "chongji/gsxm_hudun_20": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -90, -50, -90, -50, 90, 50, 90], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 180}, "chongji/gsxm_hudun_24": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -90, -50, -90, -50, 90, 50, 90], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 180}, "chongji/gsxm_hudun_28": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -90, -50, -90, -50, 90, 50, 90], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 180}, "chongji/gsxm_hudun_32": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -90, -50, -90, -50, 90, 50, 90], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 180}, "chongji/gsxm_hudun_36": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -90, -50, -90, -50, 90, 50, 90], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 180}, "chongji/gsxm_hudun_40": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -90, -50, -90, -50, 90, 50, 90], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 180}}, "a33": {"a5": {"type": "mesh", "uvs": [0.82625, 0.03631, 0.98341, 0.14322, 0.98321, 0.17956, 0.98241, 0.32652, 0.93288, 0.40468, 0.59111, 0.6049, 0.55495, 0.64823, 0.54108, 0.69041, 0.5415, 0.72617, 0.58181, 0.8119, 0.57685, 0.89594, 0.41675, 0.85506, 0.45076, 0.97272, 0.32331, 0.99694, 0.10056, 0.9212, 0.01032, 0.81247, 0.01375, 0.77072, 0.18105, 0.64927, 0.22742, 0.60743, 0.28853, 0.55631, 0.32178, 0.51161, 0.42474, 0.19975, 0.65166, 0.04498, 0.73381, 0.00444, 0.79747, 0.15547, 0.40473, 0.61202], "triangles": [0, 24, 23, 5, 25, 20, 20, 21, 5, 4, 21, 24, 4, 5, 21, 4, 24, 3, 3, 24, 2, 21, 22, 24, 2, 24, 1, 24, 0, 1, 23, 24, 22, 13, 14, 11, 17, 11, 16, 13, 11, 12, 25, 17, 18, 17, 25, 11, 18, 19, 25, 14, 15, 11, 16, 11, 15, 10, 11, 9, 11, 8, 9, 8, 25, 7, 8, 11, 25, 7, 25, 6, 6, 25, 5, 25, 19, 20], "vertices": [2, 41, -4.72, -2.7, 0.06509, 40, 33.75, -2.78, 0.93491, 2, 41, -1.14, 9.47, 0.62003, 40, 37.12, 9.45, 0.37997, 2, 41, 1.54, 10.92, 0.75025, 40, 39.78, 10.95, 0.24975, 2, 41, 12.39, 16.81, 0.99206, 40, 50.53, 17.02, 0.00794, 1, 41, 19.51, 17.48, 1, 2, 42, -4, 8.59, 0.11226, 41, 43.61, 8.46, 0.88774, 2, 42, 0.18, 8.4, 0.48814, 41, 47.8, 8.4, 0.51186, 2, 42, 3.7, 9.3, 0.82485, 41, 51.28, 9.41, 0.17515, 2, 42, 6.36, 10.68, 0.95028, 41, 53.91, 10.87, 0.04972, 2, 42, 11.75, 15.98, 0.99998, 41, 59.12, 16.34, 2e-05, 1, 42, 18.17, 18.93, 1, 1, 42, 19.24, 9.23, 1, 1, 42, 27.17, 15.44, 1, 1, 42, 32.27, 9.88, 1, 1, 42, 32.35, -4.32, 1, 1, 42, 26.53, -13.04, 1, 1, 42, 23.31, -14.46, 1, 2, 42, 9.9, -10.57, 0.99085, 41, 58.1, -10.26, 0.00915, 2, 42, 5.57, -9.8, 0.90475, 41, 53.75, -9.63, 0.09525, 2, 42, 0.17, -8.64, 0.49145, 41, 48.31, -8.63, 0.50855, 2, 42, -4.04, -8.65, 0.11927, 41, 44.11, -8.77, 0.88073, 1, 41, 18.31, -16.19, 1, 2, 41, 0.69, -11.08, 0.64583, 40, 39.31, -11.07, 0.35417, 2, 41, -4.54, -8.61, 0.32695, 40, 34.04, -8.69, 0.67305, 1, 41, 4.85, 0.66, 1, 2, 42, 1.34, -0.62, 0.9981, 41, 49.24, -0.57, 0.0019], "hull": 24, "edges": [0, 46, 0, 2, 6, 8, 8, 10, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 40, 42, 42, 44, 44, 46, 32, 34, 34, 36, 36, 38, 38, 40, 10, 12, 12, 14, 14, 16, 46, 48, 2, 4, 4, 6, 48, 4, 12, 50], "width": 57, "height": 84}}, "xlt_add/lizi_8": {"xlt_add/lizi_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28.39, -19.18, -28.61, -19.18, -28.61, 18.82, 28.39, 18.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 57, "height": 38}}, "a36": {"a6": {"type": "mesh", "uvs": [0.04196, 0.00024, 0.08949, 0, 0.12891, 0.01972, 0.16087, 0.06222, 0.1882, 0.15918, 0.27334, 0.19668, 0.34166, 0.15433, 0.39569, 0.09201, 0.46583, 0.09334, 0.58776, 0.20955, 0.72199, 0.26293, 0.83417, 0.32381, 0.92175, 0.37134, 0.95711, 0.4124, 0.97951, 0.5019, 0.93915, 0.54626, 0.9146, 0.65319, 0.89384, 0.7728, 0.94181, 0.82931, 0.99461, 0.89109, 0.99379, 0.91742, 0.95589, 0.89829, 0.96431, 0.95731, 0.91491, 0.99505, 0.85355, 1, 0.79905, 0.97668, 0.73715, 0.91841, 0.70883, 0.84661, 0.66071, 0.87679, 0.5718, 0.87689, 0.48409, 0.78056, 0.47005, 0.66587, 0.42293, 0.62321, 0.44002, 0.54952, 0.49227, 0.60079, 0.52264, 0.57379, 0.37041, 0.41216, 0.30593, 0.41123, 0.24147, 0.33629, 0.16292, 0.27775, 0.11434, 0.19339, 0.0973, 0.10361, 0.05277, 0.05701, 0, 0.06382, 0.00453, 0.02575, 0.46505, 0.29922, 0.61405, 0.44573, 0.70373, 0.58278, 0.78763, 0.75449, 0.82524, 0.46778, 0.85128, 0.89943, 0.33486, 0.32339, 0.59995, 0.74531], "triangles": [42, 0, 1, 44, 0, 42, 43, 44, 42, 2, 42, 1, 41, 2, 3, 41, 42, 2, 41, 3, 4, 40, 41, 4, 39, 40, 4, 39, 4, 5, 7, 45, 6, 45, 8, 9, 8, 45, 7, 51, 5, 6, 51, 6, 45, 38, 39, 5, 51, 38, 5, 37, 38, 51, 36, 51, 45, 37, 51, 36, 46, 9, 10, 45, 9, 46, 49, 10, 11, 49, 11, 12, 46, 10, 49, 49, 12, 13, 14, 49, 13, 15, 49, 14, 36, 46, 35, 45, 46, 36, 47, 46, 49, 35, 46, 47, 32, 33, 34, 16, 49, 15, 47, 49, 16, 31, 32, 34, 52, 35, 47, 34, 35, 52, 31, 34, 52, 48, 47, 16, 52, 47, 48, 17, 48, 16, 30, 31, 52, 27, 52, 48, 28, 52, 27, 29, 30, 52, 29, 52, 28, 21, 18, 19, 50, 48, 17, 50, 17, 18, 50, 18, 21, 27, 48, 50, 20, 21, 19, 26, 27, 50, 50, 21, 22, 25, 26, 50, 23, 50, 22, 24, 25, 50, 23, 24, 50], "vertices": [1, 94, 197.73, -17.1, 1, 1, 94, 191.66, -21.6, 1, 1, 94, 184.9, -22.98, 1, 1, 94, 177.13, -20.98, 1, 1, 94, 165.26, -12.13, 1, 1, 94, 151.11, -15.72, 1, 1, 94, 146, -27.13, 1, 1, 94, 144.45, -39.54, 1, 1, 94, 135.34, -45.98, 1, 1, 94, 109.68, -43.76, 1, 1, 94, 87.85, -50.09, 1, 1, 94, 68.21, -53.47, 1, 1, 94, 52.88, -56.1, 1, 1, 94, 44.8, -54.59, 1, 1, 94, 34.2, -46.16, 1, 1, 94, 35.55, -37.14, 1, 1, 94, 29.47, -22.24, 1, 1, 94, 21.81, -6.21, 1, 1, 94, 10.78, -4.06, 1, 1, 94, -1.33, -1.75, 1, 1, 94, -3.49, 1.43, 1, 1, 94, 3.02, 2.74, 1, 1, 94, -3.16, 8.89, 1, 1, 94, -0.08, 17.98, 1, 1, 94, 7.36, 24.33, 1, 1, 94, 16.36, 26.71, 1, 1, 94, 29.33, 25.67, 1, 1, 94, 39.16, 19.88, 1, 1, 94, 42.73, 27.95, 1, 1, 94, 54.12, 36.32, 1, 1, 94, 73.68, 33.22, 1, 1, 94, 85.38, 21.04, 1, 1, 94, 95.11, 20.44, 1, 1, 94, 99.28, 10.16, 1, 1, 94, 88.15, 11.29, 1, 1, 94, 86.59, 5.25, 1, 1, 94, 120.06, 0.53, 1, 1, 94, 128.41, 6.48, 1, 1, 94, 143.14, 3.72, 1, 1, 94, 158.27, 4.21, 1, 1, 94, 171.78, -1.16, 1, 1, 94, 181.71, -10.13, 1, 1, 94, 191.45, -11.43, 1, 1, 94, 197.63, -5.67, 1, 1, 94, 200.33, -10.58, 1, 1, 94, 117.67, -21.67, 1, 1, 94, 85.92, -18.42, 1, 1, 94, 62.59, -10.71, 1, 1, 94, 37.01, 1.62, 1, 1, 94, 56.93, -35.67, 1, 1, 94, 16.33, 12.7, 1, 1, 94, 132.28, -6.58, 1, 1, 94, 61.87, 18.18, 1], "hull": 45, "edges": [0, 88, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 24, 26, 26, 28, 28, 30, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 14, 90, 90, 92, 92, 94, 94, 96, 28, 98, 44, 100, 30, 32, 32, 34, 76, 102, 20, 22, 22, 24, 34, 36, 36, 38, 58, 104], "width": 159, "height": 146}}, "xialuo/xialuo_00000": {"xialuo/xialuo_00000": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00006": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00010": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00012": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00014": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00016": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00018": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00020": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00022": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00024": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}}, "a38": {"a11": {"type": "mesh", "uvs": [0.97542, 0.04018, 0.99225, 0.04086, 0.99196, 0.09566, 0.96791, 0.20622, 0.93673, 0.31458, 0.90531, 0.43277, 0.85436, 0.50962, 0.78955, 0.57839, 0.71546, 0.63736, 0.64144, 0.68141, 0.54903, 0.71509, 0.46235, 0.719, 0.39165, 0.70635, 0.32002, 0.67924, 0.25597, 0.71119, 0.191, 0.78191, 0.12696, 0.8573, 0.08027, 0.93104, 0.04497, 0.99999, 0.01613, 0.95622, 0.00943, 0.84399, 0.02318, 0.73259, 0.05152, 0.62039, 0.10513, 0.50416, 0.1767, 0.40431, 0.25569, 0.31419, 0.35165, 0.25847, 0.44679, 0.26403, 0.53556, 0.31424, 0.62396, 0.32585, 0.7169, 0.32586, 0.79844, 0.31008, 0.87641, 0.24748, 0.93061, 0.15875, 0.9672, 0.08177], "triangles": [33, 34, 3, 3, 34, 2, 2, 34, 1, 1, 34, 0, 13, 26, 12, 18, 19, 17, 19, 20, 17, 17, 20, 16, 20, 21, 16, 15, 16, 22, 16, 21, 22, 14, 15, 23, 15, 22, 23, 23, 24, 14, 14, 24, 13, 24, 25, 13, 13, 25, 26, 10, 11, 28, 28, 11, 27, 10, 29, 9, 10, 28, 29, 11, 12, 27, 12, 26, 27, 9, 29, 8, 8, 30, 7, 8, 29, 30, 7, 31, 6, 7, 30, 31, 6, 31, 5, 31, 32, 5, 5, 32, 4, 32, 33, 4, 4, 33, 3], "vertices": [2, 98, 27.32, 30.22, 0.088, 99, -15.33, -27.55, 0.912, 2, 98, 29.37, 30.17, 0.088, 99, -13.27, -27.6, 0.912, 2, 98, 29.34, 26.17, 0.22459, 99, -13.31, -31.6, 0.77541, 2, 98, 26.4, 18.1, 0.45956, 99, -16.24, -39.67, 0.54044, 3, 98, 22.6, 10.19, 0.70601, 97, 107.37, 39.42, 7e-05, 99, -20.05, -47.58, 0.29393, 3, 98, 18.77, 1.56, 0.88868, 97, 103.54, 30.79, 0.0014, 99, -23.88, -56.21, 0.10993, 3, 98, 12.55, -4.05, 0.96816, 97, 97.32, 25.18, 0.00754, 99, -30.1, -61.82, 0.0243, 2, 98, 4.64, -9.07, 0.97428, 97, 89.42, 20.16, 0.02572, 2, 98, -4.39, -13.37, 0.93604, 97, 80.38, 15.85, 0.06396, 2, 98, -13.42, -16.59, 0.87269, 97, 71.35, 12.64, 0.12731, 2, 98, -24.7, -19.05, 0.78493, 97, 60.07, 10.18, 0.21507, 2, 98, -35.27, -19.33, 0.67674, 97, 49.5, 9.89, 0.32326, 2, 98, -43.9, -18.41, 0.55189, 97, 40.87, 10.82, 0.44811, 2, 98, -52.64, -16.43, 0.41623, 97, 32.13, 12.8, 0.58377, 2, 98, -60.45, -18.76, 0.28102, 97, 24.32, 10.46, 0.71898, 2, 98, -68.38, -23.92, 0.16362, 97, 16.39, 5.3, 0.83638, 2, 98, -76.19, -29.43, 0.07844, 97, 8.58, -0.2, 0.92156, 2, 98, -81.89, -34.81, 0.02914, 97, 2.88, -5.59, 0.97086, 2, 98, -86.19, -39.84, 0.0084, 97, -1.42, -10.62, 0.9916, 2, 98, -89.71, -36.65, 0.00629, 97, -4.94, -7.42, 0.99371, 2, 98, -90.53, -28.46, 0.01923, 97, -5.76, 0.77, 0.98077, 2, 98, -88.85, -20.32, 0.05098, 97, -4.08, 8.9, 0.94902, 2, 98, -85.39, -12.13, 0.1076, 97, -0.62, 17.09, 0.8924, 2, 98, -78.85, -3.65, 0.19058, 97, 5.92, 25.58, 0.80942, 2, 98, -70.12, 3.64, 0.29442, 97, 14.65, 32.87, 0.70558, 2, 98, -60.49, 10.22, 0.41023, 97, 24.28, 39.44, 0.58977, 2, 98, -48.78, 14.29, 0.53054, 97, 35.99, 43.51, 0.46946, 2, 98, -37.17, 13.88, 0.65128, 97, 47.6, 43.11, 0.34872, 2, 98, -26.34, 10.21, 0.76635, 97, 58.43, 39.44, 0.23365, 2, 98, -15.56, 9.37, 0.86544, 97, 69.21, 38.59, 0.13456, 3, 98, -4.22, 9.37, 0.91271, 97, 80.55, 38.59, 0.06299, 99, -46.87, -48.41, 0.0243, 3, 98, 5.73, 10.52, 0.868, 97, 90.5, 39.74, 0.02207, 99, -36.92, -47.26, 0.10993, 3, 98, 15.24, 15.09, 0.70095, 97, 100.01, 44.31, 0.00512, 99, -27.41, -42.69, 0.29393, 3, 98, 21.85, 21.57, 0.45903, 97, 106.63, 50.79, 0.00053, 99, -20.79, -36.21, 0.54044, 2, 98, 26.32, 27.19, 0.22459, 99, -16.33, -30.59, 0.77541], "hull": 35, "edges": [0, 68, 0, 2, 2, 4, 4, 6, 10, 12, 42, 44, 48, 50, 54, 56, 64, 66, 66, 68, 44, 46, 46, 48, 56, 58, 58, 60, 28, 30, 30, 32, 16, 18, 18, 20, 12, 14, 14, 16, 6, 8, 8, 10, 20, 22, 22, 24, 32, 34, 34, 36, 38, 40, 40, 42, 36, 38, 24, 26, 26, 28, 50, 52, 52, 54, 60, 62, 62, 64], "width": 122, "height": 73}}, "a2": {"a2": {"type": "mesh", "uvs": [1, 1, 0.75, 1, 0.5, 1, 0.25, 1, 0, 1, 0, 0, 0.25, 0, 0.5, 0, 0.75, 0, 1, 0], "triangles": [4, 5, 6, 3, 4, 6, 3, 6, 7, 2, 3, 7, 2, 7, 8, 1, 2, 8, 1, 8, 9, 0, 1, 9], "vertices": [3, 7, 13.21, -39.27, 0.81963, 8, 5.65, -27.27, 0.08171, 9, -34.52, -25.81, 0.09867, 3, 7, 10.46, -24.27, 0.76703, 8, 2.89, -12.27, 0.16363, 9, -37.27, -10.81, 0.06933, 3, 7, 7.7, -9.27, 0.71424, 8, 0.14, 2.73, 0.24576, 9, -40.02, 4.19, 0.04, 2, 7, 4.95, 5.73, 0.76533, 8, -2.62, 17.73, 0.23467, 2, 7, 2.19, 20.72, 0.78667, 8, -5.37, 32.73, 0.21333, 2, 7, 20.88, 24.16, 0.78667, 8, 13.32, 36.16, 0.21333, 2, 7, 23.64, 9.16, 0.76533, 8, 16.07, 21.16, 0.23467, 3, 7, 26.39, -5.84, 0.71424, 8, 18.83, 6.16, 0.24576, 9, -21.34, 7.62, 0.04, 3, 7, 29.14, -20.84, 0.76703, 8, 21.58, -8.84, 0.16363, 9, -18.58, -7.38, 0.06933, 3, 7, 31.9, -35.84, 0.81963, 8, 24.33, -23.84, 0.08171, 9, -15.83, -22.38, 0.09867], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 0], "width": 61, "height": 19}}, "huoxing-add/huoxing_2": {"huoxing-add/huoxing_0001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0017": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0021": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}}, "a34": {"a7": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [271.76, -0.74, -101.94, -211.44, -221.78, 1.11, 151.92, 211.81], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 429, "height": 244}}, "xlt_add/lizi_9": {"xlt_add/lizi_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28.39, -19.18, -28.61, -19.18, -28.61, 18.82, 28.39, 18.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 57, "height": 38}}, "huoxing-add/huoxing_1": {"huoxing-add/huoxing_0001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0017": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0021": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}}, "xlt_add/lizi_7": {"xlt_add/lizi_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28.39, -19.18, -28.61, -19.18, -28.61, 18.82, 28.39, 18.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 57, "height": 38}}, "huoxing-add/huoxing_3": {"huoxing-add/huoxing_0001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0017": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0021": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}}, "huoxing-add/huoxing_4": {"huoxing-add/huoxing_0001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}}, "huoxing-add/huoxing_5": {"huoxing-add/huoxing_0001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}}, "huoxing-add/huoxing_0001": {"huoxing-add/huoxing_0001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}, "huoxing-add/huoxing_0007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.61, -25.21, -43.39, -25.21, -43.39, 34.79, 23.61, 34.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 60}}, "daoqi/nanjian_zmr_dg_00051": {"daoqi/nanjian_zmr_dg_00051": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [248, -163, -247, -163, -247, 164, 248, 164], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 247, "height": 163}, "daoqi/nanjian_zmr_dg_00052": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [248, -163, -247, -163, -247, 164, 248, 164], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 247, "height": 163}, "daoqi/nanjian_zmr_dg_00053": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [248, -163, -247, -163, -247, 164, 248, 164], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 247, "height": 163}, "daoqi/nanjian_zmr_dg_00055": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [248, -163, -247, -163, -247, 164, 248, 164], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 247, "height": 163}, "daoqi/nanjian_zmr_dg_00057": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [248, -163, -247, -163, -247, 164, 248, 164], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 247, "height": 163}, "daoqi/nanjian_zmr_dg_00059": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [248, -163, -247, -163, -247, 164, 248, 164], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 247, "height": 163}, "daoqi/nanjian_zmr_dg_00061": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [248, -163, -247, -163, -247, 164, 248, 164], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 247, "height": 163}}, "a0": {"a0": {"type": "mesh", "uvs": [0.50162, 0.00811, 0.61341, 0.04397, 0.7046, 0.10273, 0.79789, 0.18318, 0.86489, 0.26916, 0.93193, 0.37447, 0.97306, 0.47851, 0.99717, 0.57747, 0.98486, 0.67752, 0.94614, 0.76155, 0.88863, 0.83759, 0.82509, 0.89577, 0.74287, 0.95231, 0.67444, 0.99568, 0.64997, 0.97857, 0.69577, 0.90428, 0.7322, 0.83001, 0.76172, 0.73089, 0.76384, 0.63129, 0.74793, 0.53384, 0.70437, 0.43332, 0.63483, 0.34635, 0.54349, 0.2734, 0.42413, 0.2276, 0.32591, 0.21794, 0.34742, 0.25428, 0.2926, 0.28359, 0.22207, 0.27829, 0.15108, 0.25496, 0.09846, 0.21228, 0.04625, 0.16291, 0.01163, 0.11695, 0, 0.06642, 0.00775, 0.04086, 0.04554, 0.08518, 0.09723, 0.13146, 0.16934, 0.16107, 0.22352, 0.15556, 0.22918, 0.09902, 0.2977, 0.03538, 0.39398, 0.00181], "triangles": [12, 13, 15, 13, 14, 15, 12, 15, 11, 37, 28, 36, 28, 29, 36, 30, 35, 29, 29, 35, 36, 31, 34, 30, 30, 34, 35, 31, 32, 34, 32, 33, 34, 21, 3, 4, 3, 21, 2, 25, 26, 24, 27, 28, 37, 26, 27, 24, 27, 37, 24, 21, 22, 2, 22, 1, 2, 22, 23, 1, 24, 40, 23, 23, 0, 1, 23, 40, 0, 37, 38, 24, 38, 39, 24, 24, 39, 40, 15, 16, 11, 11, 16, 10, 16, 17, 10, 10, 17, 9, 8, 9, 18, 9, 17, 18, 8, 18, 7, 18, 19, 7, 19, 6, 7, 19, 5, 6, 19, 20, 5, 20, 4, 5, 20, 21, 4], "vertices": [2, 72, -48.61, 82.22, 0.04426, 73, 3.44, 19.29, 0.95574, 2, 72, -33.52, 77.09, 0.11344, 73, 18.54, 14.17, 0.88656, 2, 72, -21.21, 68.69, 0.23108, 73, 30.85, 5.76, 0.76892, 2, 72, -8.61, 57.19, 0.39143, 73, 43.44, -5.74, 0.60857, 2, 72, 0.43, 44.89, 0.57291, 73, 52.49, -18.04, 0.42709, 2, 72, 9.48, 29.83, 0.74188, 73, 61.54, -33.1, 0.25812, 2, 72, 15.03, 14.95, 0.87125, 73, 67.09, -47.97, 0.12875, 2, 72, 18.29, 0.8, 0.94958, 73, 70.34, -62.12, 0.05042, 2, 72, 16.63, -13.51, 0.98593, 73, 68.68, -76.43, 0.01407, 3, 72, 11.4, -25.52, 0.96056, 73, 63.45, -88.45, 0.0024, 71, 54.05, 32.25, 0.03704, 2, 72, 3.64, -36.4, 0.85183, 71, 46.28, 21.38, 0.14817, 2, 72, -4.94, -44.71, 0.62963, 71, 37.71, 13.06, 0.37037, 2, 72, -16.04, -52.8, 0.37037, 71, 26.61, 4.97, 0.62963, 2, 72, -25.28, -59, 0.18519, 71, 17.37, -1.23, 0.81481, 2, 72, -28.58, -56.56, 0.18519, 71, 14.07, 1.22, 0.81481, 2, 72, -22.4, -45.93, 0.37031, 71, 20.25, 11.84, 0.62969, 2, 72, -17.48, -35.31, 0.62848, 71, 25.17, 22.46, 0.37152, 3, 72, -13.5, -21.14, 0.83172, 73, 38.56, -84.06, 0.02013, 71, 29.15, 36.64, 0.14815, 3, 72, -13.21, -6.89, 0.89012, 73, 38.84, -69.82, 0.07284, 71, 29.44, 50.88, 0.03704, 2, 72, -15.36, 7.04, 0.81521, 73, 36.7, -55.89, 0.18479, 2, 72, -21.24, 21.41, 0.64056, 73, 30.82, -41.51, 0.35944, 2, 72, -30.63, 33.85, 0.43174, 73, 21.43, -29.07, 0.56826, 2, 72, -42.96, 44.28, 0.23919, 73, 9.1, -18.64, 0.76081, 2, 72, -59.07, 50.83, 0.10301, 73, -7.02, -12.09, 0.89699, 2, 72, -72.33, 52.21, 0.0315, 73, -20.28, -10.71, 0.9685, 3, 72, -69.43, 47.02, 0.00567, 73, -17.37, -15.91, 0.95729, 74, 47.49, -25.8, 0.03704, 3, 72, -76.83, 42.83, 0.0002, 73, -24.77, -20.1, 0.85166, 74, 40.09, -29.99, 0.14815, 2, 73, -34.3, -19.34, 0.62963, 74, 30.57, -29.23, 0.37037, 2, 73, -43.88, -16.01, 0.37037, 74, 20.99, -25.89, 0.62963, 2, 73, -50.98, -9.9, 0.14815, 74, 13.88, -19.79, 0.85185, 2, 73, -58.03, -2.84, 0.03704, 74, 6.84, -12.73, 0.96296, 1, 74, 2.16, -6.16, 1, 1, 74, 0.59, 1.07, 1, 1, 74, 1.64, 4.72, 1, 2, 73, -58.13, 8.27, 0.03704, 74, 6.74, -1.62, 0.96296, 2, 73, -51.15, 1.65, 0.14815, 74, 13.72, -8.23, 0.85185, 2, 73, -41.41, -2.58, 0.37037, 74, 23.45, -12.47, 0.62963, 2, 73, -34.1, -1.79, 0.62963, 74, 30.77, -11.68, 0.37037, 3, 72, -85.39, 69.22, 9e-05, 73, -33.33, 6.29, 0.85176, 74, 31.53, -3.6, 0.14815, 3, 72, -76.14, 78.32, 0.00215, 73, -24.08, 15.39, 0.96081, 74, 40.78, 5.5, 0.03704, 2, 72, -63.14, 83.12, 0.01241, 73, -11.09, 20.19, 0.98759], "hull": 41, "edges": [0, 80, 12, 14, 14, 16, 20, 22, 26, 28, 42, 44, 48, 50, 62, 64, 64, 66, 66, 68, 74, 76, 76, 78, 78, 80, 0, 2, 2, 4, 8, 10, 10, 12, 38, 40, 40, 42, 34, 36, 36, 38, 32, 34, 28, 30, 30, 32, 22, 24, 24, 26, 50, 52, 60, 62, 56, 58, 58, 60, 44, 46, 46, 48, 68, 70, 70, 72, 72, 74, 52, 54, 54, 56, 4, 6, 6, 8, 16, 18, 18, 20], "width": 135, "height": 143}}, "xuanzhuan/linxiangru_skill_xl_00000": {"xuanzhuan/linxiangru_skill_xl_00000": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [172, -171, -171, -171, -171, 172, 172, 172], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 343, "height": 343}, "xuanzhuan/linxiangru_skill_xl_00002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [172, -171, -171, -171, -171, 172, 172, 172], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 343, "height": 343}, "xuanzhuan/linxiangru_skill_xl_00004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [172, -171, -171, -171, -171, 172, 172, 172], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 343, "height": 343}, "xuanzhuan/linxiangru_skill_xl_00006": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [172, -171, -171, -171, -171, 172, 172, 172], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 343, "height": 343}, "xuanzhuan/linxiangru_skill_xl_00008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [172, -171, -171, -171, -171, 172, 172, 172], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 343, "height": 343}, "xuanzhuan/linxiangru_skill_xl_00010": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [172, -171, -171, -171, -171, 172, 172, 172], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 343, "height": 343}, "xuanzhuan/linxiangru_skill_xl_00012": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [172, -171, -171, -171, -171, 172, 172, 172], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 343, "height": 343}, "xuanzhuan/linxiangru_skill_xl_00014": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [172, -171, -171, -171, -171, 172, 172, 172], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 343, "height": 343}, "xuanzhuan/linxiangru_skill_xl_00016": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [172, -171, -171, -171, -171, 172, 172, 172], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 343, "height": 343}, "xuanzhuan/linxiangru_skill_xl_00032": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [172, -171, -171, -171, -171, 172, 172, 172], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 343, "height": 343}}, "chongji/tw_0001": {"chongji/tw_0001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [65.24, -42.5, -64.76, -42.5, -64.76, 42.5, 65.24, 42.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 130, "height": 85}, "chongji/tw_0003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [65.24, -42.5, -64.76, -42.5, -64.76, 42.5, 65.24, 42.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 130, "height": 85}, "chongji/tw_0005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [65.24, -42.5, -64.76, -42.5, -64.76, 42.5, 65.24, 42.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 130, "height": 85}, "chongji/tw_0007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [65.24, -42.5, -64.76, -42.5, -64.76, 42.5, 65.24, 42.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 130, "height": 85}, "chongji/tw_0009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [65.24, -42.5, -64.76, -42.5, -64.76, 42.5, 65.24, 42.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 130, "height": 85}}, "xlt_add/lizi_01": {"xlt_add/lizi_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28.39, -19.18, -28.61, -19.18, -28.61, 18.82, 28.39, 18.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 57, "height": 38}}, "xialuo/xialuo_0": {"xialuo/xialuo_00000": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00006": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00010": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00012": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00014": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00016": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00018": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00020": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00022": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00024": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}}, "xialuo/xialuo_1": {"xialuo/xialuo_00000": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00006": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00010": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00012": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00014": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00016": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00018": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00020": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00022": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00024": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}}, "xialuo/xialuo_2": {"xialuo/xialuo_00000": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00006": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00010": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00012": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00014": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00016": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00018": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00020": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00022": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00024": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}}, "xlt_add/lizi_3": {"xlt_add/lizi_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28.39, -19.18, -28.61, -19.18, -28.61, 18.82, 28.39, 18.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 57, "height": 38}}, "xialuo/xialuo_4": {"xialuo/xialuo_00000": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00006": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00010": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00012": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00014": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00016": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00018": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00020": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00022": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00024": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}}, "xlt_add/lizi_10": {"xlt_add/lizi_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28.39, -19.18, -28.61, -19.18, -28.61, 18.82, 28.39, 18.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 57, "height": 38}}, "a9": {"a9": {"type": "mesh", "uvs": [0.71266, 1e-05, 0.74464, 0.10403, 0.76438, 0.27362, 0.76562, 0.44865, 0.81274, 0.54225, 0.8505, 0.60337, 0.8925, 0.66729, 0.92744, 0.72019, 0.96586, 0.77327, 0.99654, 0.82019, 0.99218, 0.87804, 0.95276, 0.87778, 0.89781, 0.87902, 0.83553, 0.87736, 0.77929, 0.85534, 0.73798, 0.80105, 0.6981, 0.73165, 0.64961, 0.8317, 0.53581, 0.83207, 0.49107, 0.73369, 0.44978, 0.81198, 0.39651, 0.8866, 0.32892, 0.91717, 0.25667, 0.91855, 0.18889, 0.90961, 0.11901, 0.90225, 0.0653, 0.91093, 0.02153, 0.98479, 0.00687, 0.9861, 0.00558, 0.93059, 0.03019, 0.8607, 0.07492, 0.77847, 0.12025, 0.72138, 0.18972, 0.64725, 0.24339, 0.55463, 0.31093, 0.40571, 0.31636, 0.16765, 0.42256, 0.22385, 0.4266, 0, 0.45996, 0, 0.55294, 0.1698, 0.58869, 0.01783, 0.66138, 0.14, 0.49637, 0.50681, 0.68464, 0.48346], "triangles": [25, 31, 32, 26, 30, 31, 25, 32, 24, 25, 26, 31, 26, 27, 29, 26, 29, 30, 28, 29, 27, 21, 34, 35, 24, 32, 33, 22, 34, 21, 23, 33, 34, 23, 24, 33, 34, 22, 23, 11, 7, 8, 11, 8, 9, 7, 12, 6, 10, 11, 9, 11, 12, 7, 14, 15, 5, 13, 14, 5, 6, 13, 5, 12, 13, 6, 15, 4, 5, 17, 44, 16, 19, 20, 43, 18, 19, 43, 17, 18, 43, 20, 21, 35, 15, 16, 4, 16, 3, 4, 16, 44, 3, 43, 35, 37, 42, 0, 1, 40, 41, 42, 2, 44, 42, 2, 42, 1, 40, 38, 39, 44, 43, 40, 44, 40, 42, 38, 40, 43, 38, 43, 37, 17, 43, 44, 44, 2, 3, 35, 36, 37, 20, 35, 43], "vertices": [1, 4, 28.48, -27.09, 1, 1, 4, 21.06, -30.36, 1, 1, 4, 9.73, -31.22, 1, 1, 4, -1.51, -29.47, 1, 2, 12, 2.67, 35.87, 0.664, 44, 6.92, 3.58, 0.336, 1, 44, 13.53, 3.63, 1, 2, 44, 20.73, 3.9, 0.92065, 45, -2.73, 4.2, 0.07935, 2, 44, 26.7, 4.14, 0.07095, 45, 3.24, 3.84, 0.92905, 1, 45, 9.63, 3.73, 1, 1, 45, 14.88, 3.38, 1, 1, 45, 16.34, -0.14, 1, 1, 45, 11.63, -3.02, 1, 2, 44, 29.68, -6.58, 0.15692, 45, 5.13, -7.13, 0.84308, 3, 12, 24.54, 38.44, 0.00471, 44, 22.68, -11.79, 0.79157, 45, -2.35, -11.61, 0.20371, 3, 12, 22.88, 30.61, 0.03285, 44, 15.56, -15.43, 0.94985, 45, -9.8, -14.52, 0.0173, 1, 12, 19.19, 24.93, 1, 1, 12, 14.52, 19.48, 1, 1, 12, 20.83, 12.5, 1, 1, 12, 20.39, -3.42, 1, 1, 12, 13.82, -9.5, 1, 1, 12, 18.74, -15.42, 1, 2, 12, 23.38, -23.02, 0.29873, 46, 6.36, 27.87, 0.70127, 3, 12, 25.1, -32.53, 0.16308, 46, 15.15, 23.84, 0.83651, 47, -14.52, 23.84, 0.00041, 3, 12, 24.9, -42.65, 0.06127, 46, 23.35, 17.91, 0.89325, 47, -6.32, 17.9, 0.04547, 3, 12, 24.05, -52.12, 0.01175, 46, 30.63, 11.8, 0.6342, 47, 0.97, 11.8, 0.35405, 3, 12, 23.29, -61.88, 0.00014, 46, 38.22, 5.61, 0.03418, 47, 8.56, 5.61, 0.96567, 1, 47, 14.94, 1.6, 1, 1, 47, 22.73, 1.83, 1, 1, 47, 24.43, 0.68, 1, 1, 47, 22.43, -2.33, 1, 1, 47, 16.96, -3.94, 1, 1, 47, 8.75, -4.53, 1, 2, 46, 31.1, -3.75, 0.13354, 47, 1.44, -3.75, 0.86646, 1, 46, 20.41, -1.85, 1, 2, 12, 1.2, -43.83, 0.488, 46, 10.79, -2.23, 0.512, 1, 4, 12, 32.8, 1, 1, 4, 27.13, 29.44, 1, 1, 4, 21.01, 15.4, 1, 1, 4, 35.26, 12.38, 1, 1, 4, 34.47, 7.78, 1, 1, 4, 21.39, -3.18, 1, 1, 4, 30.28, -9.79, 1, 1, 4, 20.73, -18.47, 1, 1, 4, 1.14, 8.33, 1, 1, 4, -1.82, -17.91, 1], "hull": 43, "edges": [0, 84, 0, 2, 2, 4, 4, 6, 18, 20, 32, 34, 34, 36, 36, 38, 52, 54, 54, 56, 56, 58, 70, 72, 76, 78, 78, 80, 80, 82, 82, 84, 46, 48, 66, 68, 68, 70, 6, 8, 8, 10, 10, 12, 12, 14, 28, 30, 30, 32, 20, 22, 22, 24, 72, 74, 74, 76, 38, 86, 86, 88, 62, 64, 64, 66, 48, 50, 50, 52, 42, 44, 44, 46, 58, 60, 60, 62, 38, 40, 40, 42, 14, 16, 16, 18, 24, 26, 26, 28], "width": 140, "height": 65}}, "xlt_add/lizi_12": {"xlt_add/lizi_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28.39, -19.18, -28.61, -19.18, -28.61, 18.82, 28.39, 18.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 57, "height": 38}}, "xlt_add/lizi_13": {"xlt_add/lizi_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28.39, -19.18, -28.61, -19.18, -28.61, 18.82, 28.39, 18.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 57, "height": 38}}, "xlt_add/lizi_14": {"xlt_add/lizi_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28.39, -19.18, -28.61, -19.18, -28.61, 18.82, 28.39, 18.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 57, "height": 38}}, "huoyan_add/huoyan_67": {"huoyan_add/huoyan_00058": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00060": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00062": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00064": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00066": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00068": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00070": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00072": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}}, "nq_add_02/nq_b_1": {"nq_add_02/nq_b_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15.5, -37, -15.5, -37, -15.5, 37, 15.5, 37], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 31, "height": 74}, "nq_add_02/nq_b_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15.5, -37, -15.5, -37, -15.5, 37, 15.5, 37], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 31, "height": 74}, "nq_add_02/nq_b_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15.5, -37, -15.5, -37, -15.5, 37, 15.5, 37], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 31, "height": 74}, "nq_add_02/nq_b_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15.5, -37, -15.5, -37, -15.5, 37, 15.5, 37], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 31, "height": 74}, "nq_add_02/nq_b_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15.5, -37, -15.5, -37, -15.5, 37, 15.5, 37], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 31, "height": 74}, "nq_add_02/nq_b_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15.5, -37, -15.5, -37, -15.5, 37, 15.5, 37], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 31, "height": 74}, "nq_add_02/nq_b_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15.5, -37, -15.5, -37, -15.5, 37, 15.5, 37], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 31, "height": 74}, "nq_add_02/nq_b_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15.5, -37, -15.5, -37, -15.5, 37, 15.5, 37], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 31, "height": 74}, "nq_add_02/nq_b_09": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15.5, -37, -15.5, -37, -15.5, 37, 15.5, 37], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 31, "height": 74}, "nq_add_02/nq_b_10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15.5, -37, -15.5, -37, -15.5, 37, 15.5, 37], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 31, "height": 74}, "nq_add_02/nq_b_11": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15.5, -37, -15.5, -37, -15.5, 37, 15.5, 37], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 31, "height": 74}, "nq_add_02/nq_b_12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15.5, -37, -15.5, -37, -15.5, 37, 15.5, 37], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 31, "height": 74}}, "heilizi/lizi": {"heilizi/lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83.61, -75.39, -84.39, -75.39, -84.39, 74.61, 83.61, 74.61], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 168, "height": 150}}, "lightttt": {"lightttt": {"width": 129, "height": 130}}, "heilizi/lizi3": {"heilizi/lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83.61, -75.39, -84.39, -75.39, -84.39, 74.61, 83.61, 74.61], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 168, "height": 150}}, "xlt_add/lizi_11": {"xlt_add/lizi_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28.39, -19.18, -28.61, -19.18, -28.61, 18.82, 28.39, 18.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 57, "height": 38}}, "a23": {"a23": {"x": 0.91, "y": 1.32, "rotation": -90.64, "width": 26, "height": 28}}, "a8": {"a8": {"type": "mesh", "uvs": [0.70767, 0.01328, 0.85678, 0.10483, 0.98441, 0.28622, 1, 0.42075, 0.86974, 0.48881, 0.82756, 0.59251, 0.7173, 0.73758, 0.51432, 0.87049, 0.51179, 0.92992, 0.45214, 0.98462, 0.29053, 0.98322, 0.19449, 0.94935, 0.024, 0.83123, 0.02308, 0.71207, 0.29221, 0.33536, 0.29234, 0.11511, 0.34921, 0.06627, 0.5114, 0.01672, 0.27059, 0.83519, 0.62983, 0.26077], "triangles": [18, 12, 13, 7, 18, 6, 8, 18, 7, 11, 12, 18, 10, 11, 18, 18, 9, 10, 8, 9, 18, 19, 17, 0, 19, 0, 1, 16, 17, 19, 15, 16, 19, 19, 1, 2, 14, 15, 19, 19, 2, 3, 4, 19, 3, 4, 14, 19, 4, 5, 14, 6, 14, 5, 18, 13, 14, 14, 6, 18], "vertices": [1, 40, -9.61, -8.02, 1, 1, 40, -7.94, -0.04, 1, 1, 40, -1.23, 9.81, 1, 1, 40, 5.35, 14.29, 1, 1, 40, 11.48, 11.64, 1, 1, 40, 17.65, 13.17, 1, 2, 40, 27.33, 13.48, 0.98339, 41, -10.87, 13.67, 0.01661, 2, 40, 38.26, 10.13, 0.42777, 41, 0.01, 10.13, 0.57223, 2, 40, 41.36, 11.78, 0.17786, 41, 3.13, 11.72, 0.82214, 2, 40, 45.37, 11.25, 0.06816, 41, 7.14, 11.13, 0.93184, 2, 40, 48.58, 5.45, 0.00106, 41, 10.25, 5.28, 0.99894, 1, 41, 10.38, 0.86, 1, 2, 40, 46.2, -8.48, 0.2045, 41, 7.62, -8.62, 0.7955, 2, 40, 40.11, -12, 0.58883, 41, 1.47, -12.02, 0.41117, 1, 40, 15.34, -13.41, 1, 1, 40, 4.04, -19.84, 1, 1, 40, 0.39, -19.24, 1, 1, 40, -5.45, -14.91, 1, 1, 41, 2.98, 0.37, 1, 1, 40, 4.66, -3.57, 1], "hull": 18, "edges": [0, 34, 0, 2, 2, 4, 4, 6, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 20, 36, 16, 18, 34, 38, 6, 8, 8, 10, 10, 12, 38, 6, 12, 14, 14, 16], "width": 41, "height": 59}}, "golw_add/glow_01": {"golw_add/glow": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [116.25, -112.5, -116.25, -112.5, -116.25, 112.5, 116.25, 112.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 93, "height": 90}}, "xlt_add/lizi_2": {"xlt_add/lizi_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28.39, -19.18, -28.61, -19.18, -28.61, 18.82, 28.39, 18.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 57, "height": 38}}, "heilizi/lizi2": {"heilizi/lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83.61, -75.39, -84.39, -75.39, -84.39, 74.61, 83.61, 74.61], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 168, "height": 150}}, "a10": {"a10": {"type": "mesh", "uvs": [0.3269, 0.00429, 0.38747, 0.01095, 0.44239, 0.06368, 0.49253, 0.12564, 0.49264, 0.18758, 0.54467, 0.19654, 0.59942, 0.18839, 0.65073, 0.18883, 0.70117, 0.20078, 0.74688, 0.19737, 0.78591, 0.18035, 0.77385, 0.2412, 0.81091, 0.26675, 0.84953, 0.28736, 0.89391, 0.29077, 0.93166, 0.29007, 0.931, 0.31934, 0.90178, 0.34228, 0.87571, 0.36669, 0.84545, 0.40176, 0.80307, 0.41043, 0.83047, 0.44533, 0.85364, 0.47484, 0.8904, 0.51011, 0.92964, 0.54777, 0.96014, 0.57631, 0.99304, 0.58558, 0.99605, 0.63968, 0.96853, 0.67792, 0.93313, 0.71579, 0.877, 0.75454, 0.81089, 0.78391, 0.75162, 0.80425, 0.75636, 0.90975, 0.69902, 0.9541, 0.61878, 0.99609, 0.46252, 0.99785, 0.34975, 0.95954, 0.33641, 0.8935, 0.26473, 0.90876, 0.15548, 0.91435, 0.11721, 0.88739, 0.12459, 0.80285, 0.10841, 0.74875, 0.07068, 0.65554, 0.03473, 0.56226, 0.00718, 0.47715, 0, 0.3692, 0.02966, 0.2798, 0.0819, 0.24723, 0.1393, 0.21565, 0.20629, 0.16474, 0.29043, 0.15644, 0.29414, 0.10815, 0.29998, 0.03218, 0.39394, 0.13882, 0.4689, 0.32598, 0.54653, 0.44149, 0.60007, 0.57309, 0.6081, 0.69591, 0.61077, 0.79827, 0.61613, 0.88746, 0.5673, 0.23051, 0.67446, 0.3064, 0.72805, 0.4083, 0.75881, 0.52429, 0.74889, 0.58066, 0.74407, 0.67577, 0.74407, 0.74081, 0.28896, 0.22102, 0.23819, 0.26514, 0.30165, 0.4038, 0.32357, 0.53237, 0.35588, 0.63069, 0.38588, 0.69749, 0.13319, 0.35086, 0.17704, 0.50716, 0.21396, 0.6269, 0.26588, 0.72396, 0.32242, 0.80211, 0.46586, 0.55294, 0.47047, 0.6916, 0.46586, 0.8, 0.46701, 0.89328, 0.4197, 0.43732, 0.64932, 0.41716, 0.35047, 0.3327, 0.7932, 0.59592, 0.81974, 0.6476, 0.84051, 0.68289, 0.90166, 0.62743, 0.18896, 0.78789], "triangles": [79, 39, 91, 39, 40, 91, 40, 41, 91, 41, 42, 91, 42, 43, 91, 87, 23, 90, 31, 89, 30, 30, 89, 29, 89, 90, 29, 29, 90, 28, 89, 88, 90, 28, 90, 27, 27, 25, 26, 25, 27, 90, 88, 87, 90, 90, 24, 25, 90, 23, 24, 87, 22, 23, 19, 13, 18, 13, 20, 12, 17, 13, 14, 17, 18, 13, 17, 14, 16, 16, 14, 15, 12, 63, 11, 11, 8, 9, 8, 11, 63, 11, 9, 10, 55, 53, 54, 54, 0, 55, 0, 1, 55, 55, 2, 3, 55, 1, 2, 91, 43, 77, 63, 7, 8, 83, 60, 61, 55, 3, 4, 55, 52, 53, 44, 45, 76, 77, 44, 76, 66, 65, 87, 65, 64, 20, 67, 66, 87, 12, 64, 63, 63, 62, 7, 62, 6, 7, 60, 68, 32, 60, 59, 68, 59, 67, 68, 89, 68, 67, 76, 75, 71, 47, 48, 75, 48, 49, 75, 77, 76, 72, 78, 77, 73, 67, 58, 66, 66, 58, 85, 63, 85, 57, 63, 57, 62, 55, 69, 52, 56, 4, 62, 38, 79, 82, 81, 60, 82, 79, 74, 82, 79, 78, 74, 82, 74, 81, 78, 73, 74, 74, 73, 81, 81, 58, 59, 73, 80, 81, 81, 80, 58, 77, 72, 73, 73, 72, 80, 80, 57, 58, 58, 57, 85, 72, 84, 80, 80, 84, 57, 76, 71, 72, 72, 71, 84, 84, 56, 57, 71, 86, 84, 84, 86, 56, 57, 56, 62, 75, 70, 71, 71, 70, 86, 75, 50, 70, 75, 49, 50, 70, 69, 86, 86, 69, 55, 86, 55, 56, 50, 51, 70, 70, 51, 69, 69, 51, 52, 59, 60, 81, 82, 60, 83, 59, 58, 67, 56, 55, 4, 4, 5, 62, 85, 63, 64, 62, 5, 6, 65, 66, 85, 65, 85, 64, 79, 91, 78, 45, 46, 76, 46, 75, 76, 46, 47, 75, 78, 91, 77, 61, 60, 32, 32, 68, 31, 20, 64, 12, 67, 87, 88, 31, 68, 89, 89, 67, 88, 65, 20, 21, 13, 19, 20, 65, 21, 22, 87, 65, 22, 43, 44, 77, 39, 79, 38, 38, 82, 83, 36, 83, 35, 36, 37, 83, 83, 61, 35, 35, 61, 34, 34, 61, 33, 61, 32, 33, 37, 38, 83], "vertices": [5, 5, 95.5, 1.71, 0.25972, 6, 31.41, 13.37, 0.70346, 63, -55.81, 13.02, 0.00043, 65, -59.3, 39.71, 0.0024, 10, 85.8, 12.93, 0.034, 4, 5, 92.69, -5.68, 0.1378, 6, 31.28, 5.46, 0.85952, 63, -48.13, 14.96, 0.00171, 65, -56.96, 47.27, 0.00097, 4, 5, 84.78, -10.95, 0.18267, 6, 25.63, -2.18, 0.81395, 63, -39.28, 11.49, 0.00327, 65, -49.4, 53.02, 0.00011, 4, 5, 75.97, -15.33, 0.41523, 6, 18.83, -9.3, 0.58131, 63, -30.65, 6.78, 0.00345, 65, -40.88, 57.95, 1e-05, 4, 5, 68.85, -13.44, 0.64918, 6, 11.49, -9.94, 0.25244, 63, -28.12, -0.14, 0.02139, 11, 28.68, -2.62, 0.077, 4, 5, 66.06, -19.69, 0.6472, 6, 11, -16.77, 0.10786, 63, -21.4, 1.16, 0.10628, 11, 25.9, -8.88, 0.13867, 4, 5, 65.16, -26.82, 0.47985, 6, 12.57, -23.78, 0.04334, 63, -15.04, 4.5, 0.26881, 11, 24.99, -16.01, 0.208, 4, 5, 63.38, -33.25, 0.29708, 6, 13.08, -30.43, 0.02671, 63, -8.75, 6.72, 0.46821, 11, 23.21, -22.43, 0.208, 4, 5, 60.3, -39.21, 0.12056, 6, 12.22, -37.08, 0.02397, 63, -2.1, 7.62, 0.64747, 11, 20.14, -28.4, 0.208, 5, 5, 59.15, -45.06, 0.03097, 6, 13.12, -42.97, 0.01342, 63, 3.35, 10.02, 0.74411, 64, -9.44, 14.08, 0.0035, 11, 18.99, -34.24, 0.208, 5, 5, 59.79, -50.48, 0.00056, 6, 15.57, -47.85, 0.00506, 63, 7.43, 13.65, 0.74815, 64, -4.37, 16.1, 0.03823, 11, 19.63, -39.67, 0.208, 5, 5, 53.21, -47.09, 1e-05, 6, 8.22, -46.9, 0.00058, 63, 8.42, 6.31, 0.65786, 64, -5.94, 8.86, 0.13355, 11, 13.04, -36.28, 0.208, 4, 6, 5.6, -51.96, 0, 63, 13.99, 5.09, 0.48372, 64, -1.12, 5.82, 0.30828, 11, 8.85, -40.14, 0.208, 4, 6, 3.58, -57.17, 0, 63, 19.54, 4.5, 0.28347, 64, 3.9, 3.37, 0.50853, 11, 5.18, -44.35, 0.208, 4, 63, 25.11, 6.08, 0.11638, 64, 9.67, 2.97, 0.67538, 67, -0.91, 22.04, 0.00024, 11, 3.29, -49.82, 0.208, 4, 63, 29.69, 7.83, 0.0306, 64, 14.58, 3.05, 0.75828, 67, 2.47, 25.59, 0.00312, 11, 2.1, -54.58, 0.208, 5, 5, 38.93, -64.41, 0.02605, 63, 30.8, 4.53, 0.00176, 64, 14.49, -0.43, 0.75454, 67, 4.89, 23.09, 0.00965, 11, -1.24, -53.6, 0.208, 5, 5, 37.28, -60.03, 0.10748, 63, 28.16, 0.67, 0.00053, 64, 10.69, -3.16, 0.66489, 67, 4.17, 18.47, 0.0191, 11, -2.89, -49.22, 0.208, 5, 5, 35.35, -56, 0.27034, 63, 25.96, -3.22, 0.00106, 64, 7.3, -6.07, 0.49302, 67, 3.86, 14.01, 0.02759, 11, -4.82, -45.19, 0.208, 5, 5, 32.34, -51.12, 0.43648, 63, 23.68, -8.48, 0.00123, 64, 3.37, -10.24, 0.29289, 67, 4.08, 8.28, 0.06139, 11, -7.83, -40.31, 0.208, 6, 5, 32.77, -45.53, 0.51791, 63, 18.86, -11.33, 0.00106, 64, -2.14, -11.27, 0.12247, 67, 0.95, 3.63, 0.14962, 68, -19.29, 6.49, 0.00094, 11, -7.39, -34.72, 0.208, 6, 5, 27.84, -47.9, 0.43648, 63, 23.62, -14.02, 0.00053, 64, 1.42, -15.43, 0.03323, 67, 6.41, 3.26, 0.29024, 68, -13.95, 5.28, 0.03152, 11, -12.33, -37.09, 0.208, 6, 5, 23.66, -49.9, 0.27034, 63, 27.65, -16.3, 0.00018, 64, 4.43, -18.94, 0.00211, 67, 11.02, 2.94, 0.39832, 68, -9.44, 4.26, 0.12107, 11, -16.5, -39.08, 0.208, 5, 5, 18.37, -53.42, 0.10748, 64, 9.21, -23.14, 1e-05, 67, 17.36, 3.4, 0.38844, 68, -3.1, 3.75, 0.29608, 11, -21.8, -42.61, 0.208, 6, 4, 44.76, -55.33, 7e-05, 5, 12.72, -57.19, 0.02605, 64, 14.31, -27.62, 0, 67, 24.14, 3.89, 0.26612, 68, 3.67, 3.2, 0.49976, 11, -27.45, -46.37, 0.208, 4, 4, 40.74, -58.66, 0.0011, 67, 29.34, 4.33, 0.11846, 68, 8.88, 2.85, 0.67244, 11, -31.75, -49.32, 0.208, 4, 4, 38.93, -62.69, 0.00595, 67, 33.13, 6.61, 0.03183, 68, 12.97, 4.52, 0.75421, 11, -33.93, -53.17, 0.208, 4, 4, 32.52, -61.99, 0.02118, 67, 37.99, 2.37, 0.0086, 68, 17.13, -0.41, 0.76222, 11, -40.25, -51.88, 0.208, 5, 4, 28.64, -57.69, 0.06521, 5, -3.55, -58.05, 0, 67, 38.72, -3.37, 0.02165, 68, 16.98, -6.2, 0.70514, 11, -43.72, -47.24, 0.208, 5, 4, 24.98, -52.39, 0.12948, 5, -6.71, -52.44, 0.02934, 67, 38.7, -9.82, 0.03898, 68, 15.98, -12.56, 0.59421, 11, -46.87, -41.63, 0.208, 5, 4, 21.67, -44.42, 0.25192, 5, -9.27, -44.2, 0.10341, 67, 36.87, -18.25, 0.05304, 68, 12.88, -20.62, 0.45296, 11, -49.43, -33.38, 0.13867, 5, 4, 19.67, -35.36, 0.36255, 5, -10.42, -34.99, 0.20341, 67, 33.33, -26.83, 0.04922, 68, 8.07, -28.55, 0.28215, 11, -50.58, -24.18, 0.10267, 5, 4, 18.59, -27.36, 0.51744, 5, -10.75, -26.92, 0.24075, 67, 29.65, -34.01, 0.03336, 68, 3.34, -35.1, 0.14179, 11, -50.92, -16.11, 0.06667, 5, 4, 6.11, -25.84, 0.64637, 5, -23.04, -24.26, 0.2, 67, 39.03, -42.38, 0.01286, 68, 11.33, -44.8, 0.04077, 11, -63.21, -13.45, 0.1, 5, 4, 2.17, -17.6, 0.79068, 5, -26.2, -15.69, 0.1, 67, 37.56, -51.4, 0.00288, 68, 8.5, -53.48, 0.00644, 11, -66.37, -4.88, 0.1, 3, 4, -0.99, -6.48, 0.86667, 5, -28.32, -4.32, 0.03333, 11, -68.49, 6.49, 0.1, 5, 4, 2.24, 13.58, 0.89315, 5, -23.26, 15.35, 0.00134, 66, 32.77, 35.08, 0.00544, 65, 60.09, 33.6, 7e-05, 11, -63.42, 26.16, 0.1, 5, 4, 9.21, 27.26, 0.8461, 5, -15.05, 28.33, 0.00403, 66, 26.01, 21.3, 0.04966, 65, 52.72, 20.13, 0.00021, 11, -55.22, 39.14, 0.1, 5, 4, 17.25, 27.64, 0.70241, 5, -7.01, 27.96, 0.00754, 66, 17.97, 20.8, 0.15098, 65, 44.67, 19.98, 0.0004, 11, -47.18, 38.78, 0.13867, 5, 4, 17.04, 37.13, 0.48444, 5, -6.35, 37.43, 0.00932, 66, 18.33, 11.31, 0.3217, 65, 44.61, 10.49, 0.00721, 11, -46.52, 48.25, 0.17733, 5, 4, 18.78, 51.24, 0.24879, 5, -3.31, 51.32, 0.00929, 66, 16.79, -2.83, 0.4848, 65, 42.45, -3.56, 0.04112, 11, -43.48, 62.14, 0.216, 5, 4, 22.79, 55.6, 0.06938, 5, 1.08, 55.3, 0.00759, 66, 12.86, -7.25, 0.58721, 65, 38.32, -7.81, 0.11982, 11, -39.09, 66.11, 0.216, 5, 4, 32.54, 52.95, 0.01731, 5, 10.55, 51.76, 0.03248, 66, 3.07, -4.74, 0.45947, 65, 28.65, -4.88, 0.27474, 11, -29.62, 62.57, 0.216, 5, 4, 39.24, 53.94, 0, 5, 17.31, 52.12, 0.11694, 66, -3.62, -5.83, 0.28251, 65, 21.92, -5.66, 0.38455, 11, -22.86, 62.93, 0.216, 4, 5, 29.29, 53.98, 0.29037, 66, -15.34, -8.96, 0.1201, 65, 10.08, -8.28, 0.37353, 11, -10.87, 64.79, 0.216, 4, 5, 41.23, 55.62, 0.49363, 66, -27.02, -11.87, 0.03266, 65, -1.72, -10.66, 0.25771, 11, 1.06, 66.43, 0.216, 4, 5, 51.94, 56.45, 0.66785, 66, -37.58, -13.84, 0.0023, 65, -12.36, -12.17, 0.11385, 11, 11.77, 67.26, 0.216, 3, 5, 64.59, 54.02, 0.75496, 65, -25.14, -10.54, 0.02904, 11, 24.42, 64.83, 0.216, 2, 5, 73.86, 47.54, 0.813, 11, 33.69, 58.35, 0.187, 2, 5, 75.84, 39.97, 0.856, 11, 35.68, 50.78, 0.144, 4, 5, 77.54, 31.79, 0.90572, 6, 4.28, 35.55, 0.02159, 65, -39.47, 10.82, 0.00069, 11, 37.37, 42.6, 0.072, 3, 5, 81.13, 21.81, 0.91207, 6, 11.06, 27.38, 0.08598, 65, -43.68, 20.56, 0.00195, 4, 5, 79.25, 10.99, 0.74935, 6, 12.96, 16.57, 0.21871, 65, -42.49, 31.48, 0.00394, 10, 69.55, 22.2, 0.028, 3, 5, 84.67, 9.03, 0.49078, 6, 18.73, 16.57, 0.50317, 65, -48.02, 33.09, 0.00605, 3, 5, 93.2, 5.95, 0.29867, 6, 27.8, 16.58, 0.69568, 65, -56.74, 35.62, 0.00565, 4, 5, 77.78, -2.55, 0.46804, 6, 16.19, 3.34, 0.41891, 65, -41.88, 45.08, 0.00105, 10, 68.08, 8.66, 0.112, 4, 5, 53.74, -6.18, 0.63434, 6, -5.18, -8.25, 0.16545, 65, -18.12, 50.23, 0.00021, 10, 44.04, 5.03, 0.2, 4, 5, 37.85, -12.37, 0.72175, 6, -18.03, -19.47, 0.02701, 65, -2.65, 57.4, 4e-05, 10, 28.15, -1.16, 0.2512, 5, 4, 49.04, -12.59, 0.02726, 5, 20.92, -15.03, 0.70528, 6, -33.04, -27.73, 0.00345, 65, 14.08, 61.12, 1e-05, 10, 11.22, -3.82, 0.264, 3, 4, 34.45, -11.15, 0.09541, 5, 6.53, -12.24, 0.64059, 10, -3.16, -1.03, 0.264, 3, 4, 22.39, -9.43, 0.19763, 5, -5.32, -9.42, 0.53837, 10, -15.02, 1.79, 0.264, 3, 4, 11.81, -8.32, 0.25556, 5, -15.75, -7.34, 0.48044, 10, -25.45, 3.87, 0.264, 4, 5, 61.4, -21.49, 0.83194, 6, 7.22, -20.04, 0.10026, 63, -17.26, -1.64, 0.00113, 11, 21.23, -10.67, 0.06667, 7, 4, 78.68, -27.49, 0.00365, 5, 49.06, -32.6, 0.85097, 6, -0.6, -34.68, 0.02462, 63, -1.08, -5.38, 0.00038, 67, -19.6, 0.4, 0.01557, 68, -40.09, 6.43, 0.00481, 11, 8.9, -21.79, 0.1, 5, 4, 65.55, -32.3, 0.01492, 5, 35.55, -36.18, 0.8093, 67, -6.08, -3.14, 0.05194, 68, -27.26, 0.86, 0.02384, 11, -4.62, -25.37, 0.1, 5, 4, 51.27, -33.91, 0.03743, 5, 21.18, -36.46, 0.66812, 67, 6.56, -9.97, 0.11284, 68, -15.81, -7.82, 0.06662, 11, -18.99, -25.65, 0.115, 5, 4, 44.88, -31.51, 0.01492, 5, 15.03, -33.48, 0.8093, 67, 10.44, -15.6, 0.05194, 68, -12.84, -13.97, 0.02384, 11, -25.13, -22.67, 0.1, 5, 4, 33.83, -28.98, 0.00365, 5, 4.27, -29.94, 0.87597, 67, 18.06, -23.99, 0.01557, 68, -6.58, -23.42, 0.00481, 11, -35.9, -19.13, 0.1, 2, 5, -3.21, -27.93, 0.9, 11, -43.37, -17.12, 0.1, 4, 5, 71.87, 13.17, 0.83189, 6, 5.29, 16.11, 0.08357, 65, -34.99, 29.77, 0.00187, 10, 62.17, 24.38, 0.08267, 4, 5, 68.51, 20.9, 0.82916, 6, -0.5, 22.24, 0.01438, 65, -31.15, 22.26, 0.00046, 10, 58.81, 32.11, 0.156, 2, 5, 50.44, 17.21, 0.832, 10, 40.74, 28.43, 0.168, 2, 5, 34.92, 18.43, 0.816, 10, 25.22, 29.64, 0.184, 2, 5, 22.53, 17.41, 0.816, 10, 12.84, 28.62, 0.184, 2, 5, 13.84, 15.7, 0.816, 10, 4.15, 26.91, 0.184, 2, 5, 62.2, 36.73, 0.86133, 11, 22.04, 47.54, 0.13867, 2, 5, 42.76, 36.05, 0.9, 11, 2.6, 46.86, 0.1, 2, 5, 27.75, 35.11, 0.9, 11, -12.41, 45.92, 0.1, 2, 5, 14.85, 31.59, 0.9, 11, -25.32, 42.4, 0.1, 2, 5, 3.96, 26.9, 0.9, 11, -36.2, 37.71, 0.1, 3, 4, 54.35, 4.2, 0.04089, 5, 27.76, 1.2, 0.69511, 10, 18.06, 12.41, 0.264, 3, 4, 37.98, 6.4, 0.09541, 5, 11.67, 4.9, 0.64059, 10, 1.97, 16.11, 0.264, 3, 4, 25.37, 9.17, 0.19763, 5, -0.63, 8.83, 0.53837, 10, -10.33, 20.04, 0.264, 3, 4, 14.41, 10.9, 0.25556, 5, -11.39, 11.56, 0.48044, 10, -21.09, 22.77, 0.264, 4, 5, 42.61, 3.43, 0.73081, 6, -18.92, -3, 0.00517, 65, -6.4, 41.34, 2e-05, 10, 32.91, 14.64, 0.264, 4, 5, 37.18, -26.02, 0.73081, 6, -14.01, -32.54, 0.00517, 65, -2.84, 71.07, 2e-05, 10, 27.48, -14.81, 0.264, 2, 5, 56.96, 8.89, 0.836, 10, 47.26, 20.1, 0.164, 5, 4, 42.11, -36.88, 0.08483, 5, 11.79, -38.57, 0.3742, 67, 15.77, -12.77, 0.20836, 68, -7.13, -11.99, 0.19261, 11, -28.38, -27.76, 0.14, 5, 4, 35.47, -39.24, 0.11746, 5, 4.95, -40.31, 0.14364, 67, 22.58, -14.63, 0.2443, 68, -0.69, -14.86, 0.33461, 11, -35.21, -29.5, 0.16, 6, 4, 30.87, -41.19, 0.11893, 5, 0.2, -41.83, 0.03922, 67, 27.46, -15.65, 0.20408, 68, 3.98, -16.61, 0.42798, 11, -39.97, -31.01, 0.16713, 10, -9.5, -30.62, 0.04267, 6, 4, 36.03, -50.14, 0.11506, 5, 4.51, -51.22, 0.00586, 67, 28.34, -5.35, 0.17253, 68, 6.41, -6.57, 0.47186, 11, -35.66, -40.4, 0.17069, 10, -5.19, -40, 0.064, 5, 4, 32.88, 44.41, 0.02599, 5, 10.1, 43.22, 0.0093, 66, 2.6, 3.8, 0.66313, 65, 28.56, 3.68, 0.08558, 11, -30.07, 54.03, 0.216], "hull": 55, "edges": [0, 108, 0, 2, 6, 8, 18, 20, 20, 22, 28, 30, 30, 32, 32, 34, 52, 54, 58, 60, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 80, 82, 82, 84, 102, 104, 48, 50, 50, 52, 60, 62, 62, 64, 54, 56, 56, 58, 38, 40, 16, 18, 14, 16, 8, 10, 104, 106, 106, 108, 2, 4, 4, 6, 100, 102, 96, 98, 98, 100, 92, 94, 94, 96, 90, 92, 88, 90, 84, 86, 86, 88, 76, 78, 78, 80, 0, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 8, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 104, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 96, 150, 150, 152, 152, 154, 154, 156, 156, 158, 160, 162, 162, 164, 164, 166, 114, 168, 114, 170, 140, 172, 130, 174, 174, 176, 176, 178, 178, 180, 82, 182, 44, 46, 46, 48, 26, 28, 22, 24, 24, 26, 34, 36, 36, 38, 40, 42, 42, 44, 10, 12, 12, 14], "width": 130, "height": 119}}, "a11": {"a11": {"type": "mesh", "uvs": [0.97542, 0.04018, 0.99225, 0.04086, 0.99196, 0.09566, 0.96791, 0.20622, 0.93673, 0.31458, 0.90531, 0.43277, 0.85436, 0.50962, 0.78955, 0.57839, 0.71546, 0.63736, 0.64144, 0.68141, 0.54903, 0.71509, 0.46235, 0.719, 0.39165, 0.70635, 0.32002, 0.67924, 0.25597, 0.71119, 0.191, 0.78191, 0.12696, 0.8573, 0.08027, 0.93104, 0.04497, 0.99999, 0.01613, 0.95622, 0.00943, 0.84399, 0.02318, 0.73259, 0.05152, 0.62039, 0.10513, 0.50416, 0.1767, 0.40431, 0.25569, 0.31419, 0.35165, 0.25847, 0.44679, 0.26403, 0.53556, 0.31424, 0.62396, 0.32585, 0.7169, 0.32586, 0.79844, 0.31008, 0.87641, 0.24748, 0.93061, 0.15875, 0.9672, 0.08177], "triangles": [33, 34, 3, 3, 34, 2, 2, 34, 1, 1, 34, 0, 13, 26, 12, 18, 19, 17, 19, 20, 17, 17, 20, 16, 20, 21, 16, 15, 16, 22, 16, 21, 22, 14, 15, 23, 15, 22, 23, 23, 24, 14, 14, 24, 13, 24, 25, 13, 13, 25, 26, 10, 11, 28, 28, 11, 27, 10, 29, 9, 10, 28, 29, 11, 12, 27, 12, 26, 27, 9, 29, 8, 8, 30, 7, 8, 29, 30, 7, 31, 6, 7, 30, 31, 6, 31, 5, 31, 32, 5, 5, 32, 4, 32, 33, 4, 4, 33, 3], "vertices": [2, 71, 27.32, 30.22, 0.088, 72, -15.33, -27.55, 0.912, 2, 71, 29.37, 30.17, 0.088, 72, -13.27, -27.6, 0.912, 2, 71, 29.34, 26.17, 0.22459, 72, -13.31, -31.6, 0.77541, 2, 71, 26.4, 18.1, 0.45956, 72, -16.24, -39.67, 0.54044, 3, 71, 22.6, 10.19, 0.70601, 70, 107.37, 39.42, 7e-05, 72, -20.05, -47.58, 0.29393, 3, 71, 18.77, 1.56, 0.88868, 70, 103.54, 30.79, 0.0014, 72, -23.88, -56.21, 0.10993, 3, 71, 12.55, -4.05, 0.96816, 70, 97.32, 25.18, 0.00754, 72, -30.1, -61.82, 0.0243, 2, 71, 4.64, -9.07, 0.97428, 70, 89.42, 20.16, 0.02572, 2, 71, -4.39, -13.37, 0.93604, 70, 80.38, 15.85, 0.06396, 2, 71, -13.42, -16.59, 0.87269, 70, 71.35, 12.64, 0.12731, 2, 71, -24.7, -19.05, 0.78493, 70, 60.07, 10.18, 0.21507, 2, 71, -35.27, -19.33, 0.67674, 70, 49.5, 9.89, 0.32326, 2, 71, -43.9, -18.41, 0.55189, 70, 40.87, 10.82, 0.44811, 2, 71, -52.64, -16.43, 0.41623, 70, 32.13, 12.8, 0.58377, 2, 71, -60.45, -18.76, 0.28102, 70, 24.32, 10.46, 0.71898, 2, 71, -68.38, -23.92, 0.16362, 70, 16.39, 5.3, 0.83638, 2, 71, -76.19, -29.43, 0.07844, 70, 8.58, -0.2, 0.92156, 2, 71, -81.89, -34.81, 0.02914, 70, 2.88, -5.59, 0.97086, 2, 71, -86.19, -39.84, 0.0084, 70, -1.42, -10.62, 0.9916, 2, 71, -89.71, -36.65, 0.00629, 70, -4.94, -7.42, 0.99371, 2, 71, -90.53, -28.46, 0.01923, 70, -5.76, 0.77, 0.98077, 2, 71, -88.85, -20.32, 0.05098, 70, -4.08, 8.9, 0.94902, 2, 71, -85.39, -12.13, 0.1076, 70, -0.62, 17.09, 0.8924, 2, 71, -78.85, -3.65, 0.19058, 70, 5.92, 25.58, 0.80942, 2, 71, -70.12, 3.64, 0.29442, 70, 14.65, 32.87, 0.70558, 2, 71, -60.49, 10.22, 0.41023, 70, 24.28, 39.44, 0.58977, 2, 71, -48.78, 14.29, 0.53054, 70, 35.99, 43.51, 0.46946, 2, 71, -37.17, 13.88, 0.65128, 70, 47.6, 43.11, 0.34872, 2, 71, -26.34, 10.21, 0.76635, 70, 58.43, 39.44, 0.23365, 2, 71, -15.56, 9.37, 0.86544, 70, 69.21, 38.59, 0.13456, 3, 71, -4.22, 9.37, 0.91271, 70, 80.55, 38.59, 0.06299, 72, -46.87, -48.41, 0.0243, 3, 71, 5.73, 10.52, 0.868, 70, 90.5, 39.74, 0.02207, 72, -36.92, -47.26, 0.10993, 3, 71, 15.24, 15.09, 0.70095, 70, 100.01, 44.31, 0.00512, 72, -27.41, -42.69, 0.29393, 3, 71, 21.85, 21.57, 0.45903, 70, 106.63, 50.79, 0.00053, 72, -20.79, -36.21, 0.54044, 2, 71, 26.32, 27.19, 0.22459, 72, -16.33, -30.59, 0.77541], "hull": 35, "edges": [0, 68, 0, 2, 2, 4, 4, 6, 10, 12, 42, 44, 48, 50, 54, 56, 64, 66, 66, 68, 44, 46, 46, 48, 56, 58, 58, 60, 28, 30, 30, 32, 16, 18, 18, 20, 12, 14, 14, 16, 6, 8, 8, 10, 20, 22, 22, 24, 32, 34, 34, 36, 38, 40, 40, 42, 36, 38, 24, 26, 26, 28, 50, 52, 52, 54, 60, 62, 62, 64], "width": 122, "height": 73}}, "xlt_add/lizi_15": {"xlt_add/lizi_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28.39, -19.18, -28.61, -19.18, -28.61, 18.82, 28.39, 18.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 57, "height": 38}}, "a13": {"a13": {"x": 11.5, "y": 0.11, "rotation": 12.65, "width": 25, "height": 29}}, "a14": {"a14": {"rotation": -94.92, "width": 301, "height": 301}}, "a15": {"a15": {"type": "mesh", "uvs": [0.16837, 0.01811, 0.33027, 0.05599, 0.38231, 0.18933, 0.62178, 0.42573, 0.68742, 0.41112, 0.73302, 0.39427, 0.79839, 0.37012, 0.94183, 0.40869, 0.99853, 0.47378, 0.96654, 0.93985, 0.85156, 0.98005, 0.76087, 0.97113, 0.69271, 0.96459, 0.66656, 0.88095, 0.64263, 0.80039, 0.60683, 0.79728, 0.304, 0.76641, 0.20457, 0.78882, 0.01396, 0.57623, 0, 0.34419, 0.03835, 0.14464, 0.08365, 0.02942, 0.64511, 0.62331, 0.13021, 0.35819, 0.82677, 0.68236], "triangles": [9, 10, 24, 24, 10, 11, 24, 11, 13, 11, 12, 13, 9, 24, 8, 13, 14, 24, 24, 22, 5, 24, 7, 8, 5, 6, 24, 24, 6, 7, 18, 19, 23, 19, 20, 23, 0, 23, 21, 21, 23, 20, 24, 14, 22, 22, 4, 5, 14, 15, 22, 15, 16, 22, 16, 17, 23, 17, 18, 23, 16, 23, 2, 16, 3, 22, 16, 2, 3, 1, 23, 0, 23, 1, 2, 22, 3, 4], "vertices": [2, 37, -1.75, 11.75, 0.35012, 36, 35.13, 11.53, 0.64988, 2, 37, 9.04, 13.61, 0.87941, 36, 45.69, 14.4, 0.12059, 2, 37, 13.93, 9.7, 0.97703, 36, 50.93, 10.97, 0.02297, 2, 37, 32.01, 5.75, 0.9594, 38, -6.13, 6.29, 0.0406, 2, 37, 36.03, 7.62, 0.63929, 38, -1.96, 7.81, 0.36071, 2, 37, 38.75, 9.17, 0.34425, 38, 0.88, 9.12, 0.65575, 2, 37, 42.64, 11.38, 0.10682, 38, 4.94, 11, 0.89318, 1, 38, 14.65, 11.64, 1, 1, 38, 18.91, 9.99, 1, 1, 38, 20.8, -8.21, 1, 2, 37, 53.21, -10.22, 0.0001, 38, 13.63, -11.43, 0.9999, 2, 37, 47.31, -11.72, 0.03095, 38, 7.62, -12.42, 0.96905, 2, 37, 42.88, -12.86, 0.10143, 38, 3.11, -13.17, 0.89857, 2, 37, 40.22, -10.27, 0.21898, 38, 0.69, -10.37, 0.78102, 2, 37, 37.75, -7.76, 0.57573, 38, -1.56, -7.66, 0.42427, 2, 37, 35.42, -8.37, 0.85449, 38, -3.93, -8.06, 0.14551, 2, 37, 15.72, -13.34, 0.99116, 36, 54.87, -11.8, 0.00884, 2, 37, 9.63, -16.18, 0.93213, 36, 49.08, -15.2, 0.06787, 2, 37, -5.05, -12.13, 0.33343, 36, 34.09, -12.55, 0.66657, 2, 37, -8.67, -3.78, 0.0154, 36, 29.7, -4.58, 0.9846, 2, 37, -8.57, 4.41, 0.00719, 36, 29.03, 3.59, 0.99281, 2, 37, -7.03, 9.61, 0.09754, 36, 30.07, 8.91, 0.90246, 2, 37, 35.82, -1.13, 0.98972, 38, -2.92, -0.88, 0.01028, 2, 37, -0.19, -1.67, 0.42648, 36, 37.94, -1.68, 0.57352, 1, 38, 9.46, -0.46, 1], "hull": 22, "edges": [0, 42, 0, 2, 2, 4, 4, 6, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 6, 8, 8, 10, 10, 12, 28, 30, 30, 32, 24, 26, 26, 28, 8, 44, 42, 46, 16, 48], "width": 67, "height": 39}}, "a16": {"a16": {"type": "mesh", "uvs": [0.2468, 0.0059, 0.35085, 0.01221, 0.51989, 0.24429, 0.81909, 0.40794, 0.88441, 0.43355, 0.95293, 0.46041, 0.99045, 0.53782, 0.98608, 0.76332, 0.91041, 0.93242, 0.82231, 0.97231, 0.76742, 0.95684, 0.70924, 0.90197, 0.61755, 0.90432, 0.4614, 0.86716, 0.22189, 0.72972, 0.09098, 0.71735, 0.01913, 0.57315, 0.01936, 0.17278, 0.12699, 0.05456, 0.22201, 0.34816, 0.83895, 0.71215], "triangles": [8, 20, 7, 19, 18, 0, 17, 18, 19, 19, 0, 1, 19, 1, 2, 16, 17, 19, 20, 3, 4, 5, 6, 20, 5, 20, 4, 14, 15, 16, 19, 14, 16, 7, 20, 6, 2, 14, 19, 13, 2, 3, 13, 3, 20, 13, 14, 2, 20, 12, 13, 11, 12, 20, 10, 11, 20, 20, 9, 10, 8, 9, 20], "vertices": [1, 36, -6.63, 16.04, 1, 1, 36, -1.63, 17.84, 1, 1, 36, 10.47, 11.37, 1, 2, 36, 27.46, 10.39, 0.99193, 37, -9.49, 11.33, 0.00807, 2, 36, 30.98, 10.6, 0.95603, 37, -5.96, 11.21, 0.04397, 2, 36, 34.68, 10.83, 0.8873, 37, -2.25, 11.09, 0.1127, 2, 36, 37.83, 8.3, 0.7667, 37, 0.64, 8.27, 0.2333, 2, 36, 41.67, -1.34, 0.01267, 37, 3.56, -1.69, 0.98733, 2, 36, 41.15, -10.01, 0.48733, 37, 2.22, -10.27, 0.51267, 2, 36, 37.73, -13.45, 0.63289, 37, -1.51, -13.37, 0.36711, 2, 36, 34.87, -13.89, 0.70904, 37, -4.39, -13.54, 0.29096, 2, 36, 31.16, -12.72, 0.85388, 37, -7.98, -12.02, 0.14612, 2, 36, 26.89, -14.64, 0.96047, 37, -12.41, -13.54, 0.03953, 2, 36, 18.89, -16.18, 0.99881, 37, -20.51, -14.31, 0.00119, 1, 36, 5.18, -15.12, 1, 1, 36, -1.19, -17.2, 1, 1, 36, -7.15, -12.52, 1, 1, 36, -14.32, 4.45, 1, 1, 36, -11.39, 11.6, 1, 1, 36, -1.66, 1.05, 1, 2, 36, 33.84, -2.1, 0.95436, 37, -4.31, -1.7, 0.04564], "hull": 19, "edges": [0, 36, 0, 2, 2, 4, 4, 6, 10, 12, 12, 14, 14, 16, 16, 18, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 24, 26, 18, 20, 20, 22, 22, 24, 34, 38, 14, 40, 6, 8, 8, 10, 10, 40], "width": 51, "height": 46}}, "a17": {"a17": {"type": "mesh", "uvs": [0.55021, 0, 0.71991, 0.01842, 0.89037, 0.02583, 0.99326, 0.07515, 0.99092, 0.10096, 0.90479, 0.22826, 0.81125, 0.3858, 0.71149, 0.5881, 0.65185, 0.73182, 0.61396, 0.85329, 0.59006, 0.92968, 0.5303, 0.97149, 0.4372, 0.99947, 0.31808, 0.99175, 0.20738, 0.95644, 0.11211, 0.89403, 0.03335, 0.78912, 0, 0.68842, 0, 0.60141, 0.00541, 0.4775, 0.20444, 0.29713, 0.35616, 0.13859, 0.48849, 0, 0.34561, 0.74355, 0.69288, 0.16355, 0.17499, 0.56618, 0.34953, 0.42285, 0.5259, 0.27951], "triangles": [6, 24, 5, 27, 0, 24, 24, 2, 5, 4, 2, 3, 27, 22, 0, 5, 2, 4, 24, 1, 2, 24, 0, 1, 8, 23, 7, 11, 12, 23, 23, 12, 13, 10, 11, 23, 23, 13, 14, 14, 15, 23, 10, 23, 9, 15, 16, 23, 9, 23, 8, 16, 17, 25, 16, 25, 23, 25, 17, 18, 23, 25, 26, 7, 23, 26, 18, 19, 25, 26, 27, 7, 7, 27, 6, 25, 20, 26, 25, 19, 20, 27, 26, 21, 27, 24, 6, 26, 20, 21, 21, 22, 27], "vertices": [2, 22, -18.9, -16.08, 0.05867, 12, -7.38, -27.13, 0.94133, 2, 22, -25.1, 1.66, 0.044, 12, -4.64, -8.53, 0.956, 2, 22, -32.52, 18.9, 0.099, 12, -3.21, 10.18, 0.901, 2, 22, -32.16, 31.67, 0.10267, 12, 3.03, 21.33, 0.89733, 2, 22, -29.27, 32.8, 0.22844, 12, 6.12, 20.98, 0.77156, 3, 22, -11.38, 30.98, 0.50261, 23, -70.42, 36.02, 0.00672, 12, 21.12, 11.08, 0.49067, 3, 22, 10.12, 30.02, 0.71417, 23, -49.03, 33.6, 0.0735, 12, 39.72, 0.25, 0.21233, 3, 22, 36.75, 30.8, 0.709, 23, -22.41, 32.56, 0.24168, 12, 63.67, -11.41, 0.04932, 2, 22, 55.13, 32.47, 0.46477, 23, -3.96, 32.97, 0.53523, 2, 22, 70.05, 35.11, 0.18357, 23, 11.11, 34.59, 0.81643, 2, 22, 79.44, 36.77, 0.12836, 23, 20.59, 35.6, 0.87164, 2, 22, 86.84, 33.06, 0.06146, 23, 27.72, 31.4, 0.93854, 2, 22, 94.34, 25.33, 0.02957, 23, 34.68, 23.17, 0.97043, 2, 22, 99.26, 13.15, 0.00959, 23, 38.75, 10.68, 0.99041, 2, 22, 100.79, 0.35, 0.00722, 23, 39.4, -2.2, 0.99278, 2, 22, 98.65, -12.36, 0.03586, 23, 36.4, -14.72, 0.96414, 2, 22, 91.14, -25.66, 0.105, 23, 28, -27.49, 0.895, 2, 22, 81.88, -34.26, 0.24174, 23, 18.18, -35.43, 0.75826, 2, 22, 72.5, -38.84, 0.47702, 23, 8.5, -39.36, 0.52298, 3, 22, 58.88, -44.82, 0.58447, 23, -5.5, -44.4, 0.36308, 12, 48.19, -88.67, 0.05244, 3, 22, 29.82, -34.63, 0.58328, 23, -33.79, -32.25, 0.20912, 12, 27.18, -66.17, 0.2076, 3, 22, 5.41, -27.98, 0.41505, 23, -57.69, -23.94, 0.13586, 12, 8.64, -48.94, 0.44909, 3, 22, -15.92, -22.19, 0.22335, 23, -78.57, -16.71, 0.0051, 12, -7.57, -33.92, 0.77156, 2, 22, 71.16, 2.81, 0.05143, 23, 10.01, 2.28, 0.94857, 2, 22, -8.14, 6.63, 0.198, 12, 12.69, -12, 0.802, 2, 22, 60.26, -23.39, 0.63626, 23, -2.65, -23.11, 0.36374, 2, 22, 36.38, -13.67, 0.82516, 23, -25.81, -11.79, 0.17484, 2, 22, 12.42, -3.78, 0.92012, 23, -49.04, -0.28, 0.07988], "hull": 23, "edges": [0, 44, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 30, 46, 4, 48, 40, 42, 42, 44, 14, 16, 16, 18, 36, 50, 50, 52, 52, 54, 0, 2, 2, 4, 28, 30, 46, 20], "width": 110, "height": 120}}, "a18": {"a18": {"type": "mesh", "uvs": [0.7267, 0.01059, 0.81862, 0.02534, 0.97938, 0.11092, 0.97943, 0.16795, 0.91039, 0.23734, 0.74878, 0.42069, 0.77791, 0.43858, 0.70595, 0.51118, 0.71373, 0.56747, 0.66961, 0.58356, 0.71952, 0.63873, 0.72233, 0.68977, 0.56277, 0.79125, 0.56306, 0.86882, 0.39622, 0.94471, 0.36856, 0.98912, 0.14995, 0.98899, 0.02042, 0.9339, 0.01996, 0.8555, 0.23478, 0.65801, 0.37134, 0.53513, 0.36305, 0.496, 0.39818, 0.4571, 0.42189, 0.39169, 0.46348, 0.38325, 0.48135, 0.30113, 0.542, 0.11882, 0.62961, 0.00637, 0.73754, 0.14936, 0.54256, 0.52671], "triangles": [28, 0, 1, 28, 1, 2, 27, 0, 28, 28, 2, 3, 4, 28, 3, 26, 27, 28, 25, 26, 28, 4, 25, 28, 5, 25, 4, 24, 25, 5, 5, 29, 24, 7, 5, 6, 7, 29, 5, 22, 23, 24, 29, 22, 24, 21, 22, 29, 29, 7, 8, 9, 29, 8, 9, 20, 29, 10, 19, 9, 20, 21, 29, 12, 10, 11, 9, 19, 20, 10, 12, 19, 18, 19, 12, 14, 18, 12, 18, 16, 17, 12, 13, 14, 14, 16, 18, 15, 16, 14], "vertices": [1, 23, 2.25, -8.57, 1, 1, 23, 1.82, -3.98, 1, 1, 23, 6.06, 6.07, 1, 1, 23, 10.81, 8, 1, 1, 23, 17.84, 7.28, 1, 1, 23, 36.05, 6.3, 1, 1, 23, 37.02, 8.2, 1, 1, 23, 44.37, 7.45, 1, 2, 27, -1.46, 9.69, 0.01041, 23, 48.92, 9.71, 0.98959, 2, 27, 0.75, 8.38, 0.09139, 23, 51.06, 8.29, 0.90861, 2, 27, 4.24, 12.64, 0.3844, 23, 54.76, 12.38, 0.6156, 2, 27, 8.35, 14.7, 0.53359, 23, 58.97, 14.23, 0.46641, 2, 27, 19.86, 11.6, 0.9387, 23, 70.31, 10.57, 0.0613, 2, 27, 26.19, 14.56, 0.99398, 23, 76.77, 13.2, 0.00602, 1, 27, 35.75, 10.17, 1, 1, 27, 39.94, 10.65, 1, 1, 27, 44.35, 1.13, 1, 1, 27, 42.47, -6.59, 1, 1, 27, 36.08, -9.59, 1, 1, 27, 15.62, -7.73, 1, 2, 27, 2.83, -6.44, 0.99323, 23, 52.41, -6.62, 0.00677, 2, 27, -0.2, -8.29, 0.85886, 23, 49.29, -8.31, 0.14114, 2, 27, -4.08, -8.23, 0.50027, 23, 45.42, -8.06, 0.49973, 2, 27, -9.9, -9.68, 0.13897, 23, 39.53, -9.22, 0.86103, 2, 27, -11.43, -8.19, 0.08039, 23, 38.08, -7.66, 0.91961, 2, 27, -18.5, -10.53, 0.00099, 23, 30.91, -9.64, 0.99901, 1, 23, 14.61, -13.12, 1, 1, 23, 3.65, -13.03, 1, 1, 23, 13.63, -3.39, 1, 1, 23, 48.61, 0.71, 1], "hull": 28, "edges": [0, 54, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 40, 42, 42, 44, 10, 12, 12, 14, 14, 16, 52, 54, 44, 46, 54, 56, 36, 38, 38, 40, 16, 18, 18, 20, 20, 22, 46, 48, 48, 50, 50, 52, 44, 58, 58, 16], "width": 48, "height": 90}}, "a19": {"a19": {"type": "mesh", "uvs": [0.22057, 0.00601, 0.34438, 0.00681, 0.43957, 0.08449, 0.5315, 0.1595, 0.65359, 0.30036, 0.72299, 0.41197, 0.80974, 0.57834, 0.8767, 0.70676, 0.9399, 0.76964, 1, 0.82943, 0.87544, 0.903, 0.73933, 0.96133, 0.59093, 0.9935, 0.44379, 0.99587, 0.27383, 0.98363, 0.14429, 0.97998, 0.05101, 0.96469, 0.08592, 0.88978, 0.09732, 0.80085, 0.09721, 0.68907, 0.09479, 0.54566, 0.0533, 0.35783, 0.01237, 0.18341, 0.0112, 0.14012, 0.11041, 0.06685, 0.25104, 0.16217, 0.56055, 0.91207, 0.52616, 0.81553, 0.48031, 0.67961, 0.41841, 0.5221, 0.34275, 0.34426], "triangles": [21, 22, 25, 30, 25, 3, 22, 23, 25, 23, 24, 25, 24, 0, 25, 25, 2, 3, 25, 1, 2, 25, 0, 1, 21, 25, 30, 30, 3, 4, 12, 13, 26, 13, 14, 26, 12, 26, 11, 11, 26, 10, 26, 27, 10, 10, 8, 9, 10, 27, 8, 27, 7, 8, 27, 26, 14, 16, 17, 15, 17, 14, 15, 27, 14, 17, 27, 17, 18, 18, 28, 27, 27, 28, 7, 18, 19, 28, 28, 6, 7, 19, 29, 28, 19, 20, 29, 28, 29, 6, 29, 5, 6, 20, 30, 29, 5, 29, 30, 20, 21, 30, 5, 30, 4], "vertices": [2, 4, 37.53, -4.61, 0.91667, 12, -38.75, -0.66, 0.08333, 1, 4, 35.69, -14.59, 1, 2, 4, 23.04, -20.34, 0.88889, 12, -26.62, 16.96, 0.11111, 4, 14, -32.47, 23.28, 0.11036, 15, -87.1, 21.53, 0.00075, 4, 10.83, -25.9, 0.66667, 12, -15.31, 24.18, 0.22222, 4, 14, -9.59, 26.65, 0.30661, 15, -64.27, 25.26, 0.02672, 4, -11.41, -32.24, 0.33333, 12, 5.81, 33.59, 0.33333, 4, 14, 7.87, 27.18, 0.5239, 15, -46.82, 26.07, 0.14277, 4, -28.66, -35.05, 0.11111, 12, 22.49, 38.81, 0.22222, 3, 14, 33.5, 26.67, 0.52281, 15, -21.19, 25.96, 0.36608, 12, 47.3, 45.21, 0.11111, 2, 14, 53.27, 26.27, 0.34994, 15, -1.41, 25.87, 0.65006, 2, 14, 63.7, 28.46, 0.135, 15, 8.98, 28.22, 0.865, 2, 14, 73.61, 30.54, 0.02573, 15, 18.86, 30.46, 0.97427, 2, 14, 80.98, 17.56, 0.01314, 15, 26.43, 17.59, 0.98686, 2, 14, 85.91, 4.34, 0.0645, 15, 31.57, 4.45, 0.9355, 2, 14, 86.85, -8.7, 0.22942, 15, 32.71, -8.57, 0.77058, 2, 14, 83.6, -20.32, 0.41203, 15, 29.65, -20.24, 0.58797, 2, 14, 77.74, -33.09, 0.63648, 15, 23.98, -33.1, 0.36352, 2, 14, 74.07, -43.08, 0.79904, 15, 20.48, -43.14, 0.20096, 2, 14, 69.64, -49.71, 0.88881, 15, 16.15, -49.85, 0.11119, 2, 14, 59.9, -43.69, 0.93402, 15, 6.32, -43.98, 0.06598, 2, 14, 47.61, -38.89, 0.96698, 15, -6.05, -39.37, 0.03302, 3, 14, 31.81, -33.99, 0.87738, 15, -21.92, -34.72, 0.01151, 12, 62.02, -13.66, 0.11111, 4, 14, 11.48, -27.88, 0.66512, 15, -42.35, -28.93, 0.00155, 4, -39.45, 19.06, 0.11111, 12, 40.79, -13.25, 0.22222, 3, 14, -16.08, -22.88, 0.33333, 4, -11.47, 17.72, 0.33333, 12, 12.91, -15.86, 0.33333, 3, 14, -41.72, -18.43, 0.11111, 4, 14.54, 16.66, 0.66667, 12, -12.99, -18.47, 0.22222, 2, 4, 20.87, 15.67, 0.88889, 12, -19.4, -18.39, 0.11111, 1, 4, 30.18, 5.82, 1, 3, 14, -38.92, 1.2, 0.11111, 4, 14.33, -3.16, 0.66667, 12, -15.57, 1.18, 0.22222, 2, 14, 74.6, -7.5, 0.36427, 15, 20.45, -7.56, 0.63573, 2, 14, 60.12, -5.95, 0.60975, 15, 5.94, -6.24, 0.39025, 3, 14, 39.79, -3.57, 0.72684, 15, -14.42, -4.18, 0.16204, 12, 61.51, 17.78, 0.11111, 4, 14, 16.02, -1.5, 0.63252, 15, -38.22, -2.49, 0.03415, 4, -40.5, -7.68, 0.11111, 12, 38.07, 13.38, 0.22222, 3, 14, -10.95, 0.38, 0.33333, 4, -13.51, -6.02, 0.33333, 12, 11.58, 7.93, 0.33333], "hull": 25, "edges": [0, 48, 0, 2, 6, 8, 8, 10, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 44, 46, 46, 48, 34, 36, 36, 38, 38, 40, 10, 12, 12, 14, 40, 42, 42, 44, 14, 16, 16, 18, 0, 50, 2, 4, 4, 6, 24, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 50, 18, 20], "width": 82, "height": 148}}, "huoyan_add/huoyan_65": {"huoyan_add/huoyan_00058": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00060": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00062": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00064": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00066": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00068": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00070": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00072": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}}, "huoyankuosna-add/huokuo_1": {"huoyankuosna-add/huokuo_0001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [54.11, -29.04, -49.89, -29.04, -49.89, 60.96, 54.11, 60.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 104, "height": 90}, "huoyankuosna-add/huokuo_0003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [54.11, -29.04, -49.89, -29.04, -49.89, 60.96, 54.11, 60.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 104, "height": 90}, "huoyankuosna-add/huokuo_0005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [54.11, -29.04, -49.89, -29.04, -49.89, 60.96, 54.11, 60.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 104, "height": 90}, "huoyankuosna-add/huokuo_0007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [54.11, -29.04, -49.89, -29.04, -49.89, 60.96, 54.11, 60.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 104, "height": 90}, "huoyankuosna-add/huokuo_0009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [54.11, -29.04, -49.89, -29.04, -49.89, 60.96, 54.11, 60.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 104, "height": 90}, "huoyankuosna-add/huokuo_0011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [54.11, -29.04, -49.89, -29.04, -49.89, 60.96, 54.11, 60.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 104, "height": 90}, "huoyankuosna-add/huokuo_0013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [54.11, -29.04, -49.89, -29.04, -49.89, 60.96, 54.11, 60.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 104, "height": 90}, "huoyankuosna-add/huokuo_0015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [54.11, -29.04, -49.89, -29.04, -49.89, 60.96, 54.11, 60.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 104, "height": 90}, "huoyankuosna-add/huokuo_0017": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [54.11, -29.04, -49.89, -29.04, -49.89, 60.96, 54.11, 60.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 104, "height": 90}, "huoyankuosna-add/huokuo_0019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [54.11, -29.04, -49.89, -29.04, -49.89, 60.96, 54.11, 60.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 104, "height": 90}, "huoyankuosna-add/huokuo_0021": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [54.11, -29.04, -49.89, -29.04, -49.89, 60.96, 54.11, 60.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 104, "height": 90}}, "huoyankuosna-add/huokuo_2": {"huoyankuosna-add/huokuo_0001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [54.11, -29.04, -49.89, -29.04, -49.89, 60.96, 54.11, 60.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 104, "height": 90}, "huoyankuosna-add/huokuo_0003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [54.11, -29.04, -49.89, -29.04, -49.89, 60.96, 54.11, 60.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 104, "height": 90}, "huoyankuosna-add/huokuo_0005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [54.11, -29.04, -49.89, -29.04, -49.89, 60.96, 54.11, 60.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 104, "height": 90}, "huoyankuosna-add/huokuo_0007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [54.11, -29.04, -49.89, -29.04, -49.89, 60.96, 54.11, 60.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 104, "height": 90}, "huoyankuosna-add/huokuo_0009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [54.11, -29.04, -49.89, -29.04, -49.89, 60.96, 54.11, 60.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 104, "height": 90}, "huoyankuosna-add/huokuo_0011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [54.11, -29.04, -49.89, -29.04, -49.89, 60.96, 54.11, 60.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 104, "height": 90}, "huoyankuosna-add/huokuo_0013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [54.11, -29.04, -49.89, -29.04, -49.89, 60.96, 54.11, 60.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 104, "height": 90}, "huoyankuosna-add/huokuo_0015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [54.11, -29.04, -49.89, -29.04, -49.89, 60.96, 54.11, 60.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 104, "height": 90}, "huoyankuosna-add/huokuo_0017": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [54.11, -29.04, -49.89, -29.04, -49.89, 60.96, 54.11, 60.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 104, "height": 90}, "huoyankuosna-add/huokuo_0019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [54.11, -29.04, -49.89, -29.04, -49.89, 60.96, 54.11, 60.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 104, "height": 90}, "huoyankuosna-add/huokuo_0021": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [54.11, -29.04, -49.89, -29.04, -49.89, 60.96, 54.11, 60.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 104, "height": 90}}, "jushouyun": {"jushouyun-add/jushouyun_0015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [97.11, -75.99, -116.89, -75.99, -116.89, 53.01, 97.11, 53.01], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 214, "height": 129}}, "huoyan_add/huoyan_63": {"huoyan_add/huoyan_00058": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00060": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00062": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00064": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00066": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00068": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00070": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00072": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}}, "jushouyun2": {"jushouyun-add/jushouyun_0015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [97.11, -75.99, -116.89, -75.99, -116.89, 53.01, 97.11, 53.01], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 214, "height": 129}}, "ks/ks_01": {"ks/kuo_0002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [44, -44, -44, -44, -44, 44, 44, 44], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 88, "height": 88}}, "huoyan_add/huoyan_00058": {"huoyan_add/huoyan_00058": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00060": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00062": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00064": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00066": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00068": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00070": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00072": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}}, "huoyan_add/huoyan_68": {"huoyan_add/huoyan_00058": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00060": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00062": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00064": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00066": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00068": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00070": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00072": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}}, "huoyan_add/huoyan_58": {"huoyan_add/huoyan_00058": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00060": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00062": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00064": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00066": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00068": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00070": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00072": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}}, "huoyan_add/huoyan_59": {"huoyan_add/huoyan_00058": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00060": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00062": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00064": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00066": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00068": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00070": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00072": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}}, "xlt_add/lizi_4": {"xlt_add/lizi_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28.39, -19.18, -28.61, -19.18, -28.61, 18.82, 28.39, 18.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 57, "height": 38}}, "xialuo/xialuo_3": {"xialuo/xialuo_00000": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00006": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00010": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00012": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00014": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00016": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00018": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00020": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00022": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}, "xialuo/xialuo_00024": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -11.33, -39, -11.33, -39, 96.67, 40, 96.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 79, "height": 108}}, "huoyan_add/huoyan_64": {"huoyan_add/huoyan_00058": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00060": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00062": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00064": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00066": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00068": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00070": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00072": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}}, "nq_add_02/nq_b_01": {"nq_add_02/nq_b_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15.5, -37, -15.5, -37, -15.5, 37, 15.5, 37], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 31, "height": 74}, "nq_add_02/nq_b_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15.5, -37, -15.5, -37, -15.5, 37, 15.5, 37], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 31, "height": 74}, "nq_add_02/nq_b_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15.5, -37, -15.5, -37, -15.5, 37, 15.5, 37], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 31, "height": 74}, "nq_add_02/nq_b_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15.5, -37, -15.5, -37, -15.5, 37, 15.5, 37], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 31, "height": 74}, "nq_add_02/nq_b_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15.5, -37, -15.5, -37, -15.5, 37, 15.5, 37], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 31, "height": 74}, "nq_add_02/nq_b_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15.5, -37, -15.5, -37, -15.5, 37, 15.5, 37], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 31, "height": 74}, "nq_add_02/nq_b_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15.5, -37, -15.5, -37, -15.5, 37, 15.5, 37], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 31, "height": 74}, "nq_add_02/nq_b_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15.5, -37, -15.5, -37, -15.5, 37, 15.5, 37], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 31, "height": 74}, "nq_add_02/nq_b_09": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15.5, -37, -15.5, -37, -15.5, 37, 15.5, 37], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 31, "height": 74}, "nq_add_02/nq_b_10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15.5, -37, -15.5, -37, -15.5, 37, 15.5, 37], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 31, "height": 74}, "nq_add_02/nq_b_11": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15.5, -37, -15.5, -37, -15.5, 37, 15.5, 37], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 31, "height": 74}, "nq_add_02/nq_b_12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15.5, -37, -15.5, -37, -15.5, 37, 15.5, 37], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 31, "height": 74}}, "a21": {"a21": {"type": "mesh", "uvs": [0.66303, 0.03887, 0.73569, 0.17126, 0.79603, 0.28367, 0.84939, 0.37249, 0.9096, 0.46354, 0.95703, 0.53989, 0.99555, 0.60209, 0.99999, 0.68788, 0.95748, 0.77646, 0.89814, 0.85177, 0.83713, 0.89472, 0.7533, 0.92317, 0.65232, 0.9408, 0.55882, 0.96009, 0.46004, 0.99145, 0.35395, 0.96754, 0.2688, 0.9412, 0.19663, 0.92192, 0.11573, 0.89158, 0.04638, 0.85752, 0, 0.81687, 0.02652, 0.65443, 0.08839, 0.57055, 0.15501, 0.49741, 0.22721, 0.39809, 0.29605, 0.28629, 0.37078, 0.14678, 0.44158, 0.02269, 0.52475, 0.01799, 0.61151, 0.01309, 0.5491, 0.35392], "triangles": [19, 21, 22, 20, 21, 19, 18, 22, 23, 19, 22, 18, 17, 23, 24, 18, 23, 17, 15, 16, 24, 17, 24, 16, 1, 2, 30, 11, 12, 2, 2, 12, 30, 15, 24, 25, 30, 28, 29, 27, 28, 30, 30, 0, 1, 0, 30, 29, 13, 30, 12, 30, 15, 25, 26, 27, 30, 30, 25, 26, 14, 15, 30, 13, 14, 30, 4, 10, 3, 3, 11, 2, 9, 10, 4, 10, 11, 3, 8, 5, 6, 8, 6, 7, 9, 4, 5, 9, 5, 8], "vertices": [2, 44, -16.53, -0.27, 0.11111, 4, 5.95, -16.17, 0.88889, 2, 44, -4.18, 0.34, 0.33333, 4, -2.69, -25.01, 0.66667, 3, 45, -17.55, 2.51, 0.11111, 44, 6.15, 0.74, 0.55556, 4, -9.99, -32.33, 0.33333, 3, 45, -8.72, 2.42, 0.33333, 44, 14.94, 1.53, 0.55556, 4, -15.89, -38.9, 0.11111, 2, 45, 0.98, 2.74, 0.66667, 44, 24.57, 2.82, 0.33333, 2, 45, 8.76, 2.78, 0.88889, 44, 32.3, 3.64, 0.11111, 1, 45, 15.08, 2.8, 1, 2, 45, 18, -0.74, 0.88889, 44, 41.84, 1.06, 0.11111, 2, 45, 15.39, -7.86, 0.66667, 44, 39.97, -6.28, 0.33333, 3, 45, 10.42, -15.62, 0.33333, 44, 35.79, -14.5, 0.55556, 4, -42.08, -41.33, 0.11111, 3, 45, 4.34, -22.04, 0.11111, 44, 30.39, -21.49, 0.55556, 4, -42.88, -32.52, 0.33333, 2, 44, 21.98, -29.82, 0.33333, 4, -42.38, -20.7, 0.66667, 2, 44, 11.32, -39.15, 0.11111, 4, -40.91, -6.61, 0.88889, 1, 4, -39.71, 6.46, 1, 2, 4, -39.01, 20.37, 0.88889, 46, 9.26, 46.76, 0.11111, 2, 4, -35.25, 34.8, 0.66667, 46, 20.45, 36.92, 0.33333, 3, 4, -31.86, 46.31, 0.33333, 46, 29.21, 28.71, 0.55556, 47, -0.46, 28.71, 0.11111, 3, 4, -29.14, 56.1, 0.11111, 46, 36.73, 21.89, 0.55556, 47, 7.06, 21.89, 0.33333, 2, 46, 44.89, 13.87, 0.33333, 47, 15.22, 13.88, 0.66667, 2, 46, 51.63, 6.65, 0.11111, 47, 21.97, 6.66, 0.88889, 1, 47, 25.91, 1.07, 1, 2, 46, 47.47, -3.66, 0.11111, 47, 17.81, -3.65, 0.88889, 2, 46, 37.86, -2.09, 0.33333, 47, 8.21, -2.09, 0.66667, 3, 4, -5.98, 58.04, 0.11111, 46, 28.06, 0.33, 0.55556, 47, -1.6, 0.33, 0.33333, 3, 4, -2.5, 47.18, 0.33333, 46, 16.8, 2.1, 0.55556, 47, -12.86, 2.09, 0.11111, 2, 4, 1.71, 36.68, 0.66667, 46, 5.53, 3.06, 0.33333, 2, 4, 7.23, 25.12, 0.88889, 46, -7.28, 3.32, 0.11111, 1, 4, 12.03, 14.24, 1, 1, 4, 10.31, 2.72, 1, 1, 4, 8.51, -9.29, 1, 1, 4, -7.81, 2.37, 1], "hull": 30, "edges": [0, 58, 0, 2, 12, 14, 24, 26, 26, 28, 36, 38, 38, 40, 40, 42, 34, 36, 32, 34, 30, 32, 42, 44, 48, 50, 44, 46, 46, 48, 2, 4, 4, 6, 6, 8, 22, 24, 18, 20, 20, 22, 14, 16, 16, 18, 54, 56, 56, 58, 50, 52, 52, 54, 8, 10, 10, 12, 28, 30, 54, 60], "width": 140, "height": 53}}, "a22": {"a22": {"x": 1.67, "y": -1.12, "rotation": -90, "width": 26, "height": 28}}, "quan_chongji/quanshuaxiao_00016": {"quan_chongji/quanshuaxiao_00016": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [191, -86, -191, -86, -191, 86, 191, 86], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 382, "height": 172}, "quan_chongji/quanshuaxiao_00018": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [191, -86, -191, -86, -191, 86, 191, 86], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 382, "height": 172}, "quan_chongji/quanshuaxiao_00020": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [191, -86, -191, -86, -191, 86, 191, 86], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 382, "height": 172}, "quan_chongji/quanshuaxiao_00022": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [191, -86, -191, -86, -191, 86, 191, 86], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 382, "height": 172}, "quan_chongji/quanshuaxiao_00024": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [191, -86, -191, -86, -191, 86, 191, 86], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 382, "height": 172}, "quan_chongji/quanshuaxiao_00026": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [191, -86, -191, -86, -191, 86, 191, 86], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 382, "height": 172}}, "a24": {"a24": {"x": 32.27, "y": -1.23, "rotation": -90.64, "width": 71, "height": 75}}, "a25": {"a25": {"x": 34.73, "y": 2.13, "rotation": -90, "width": 71, "height": 75}}, "a26": {"a26": {"type": "mesh", "uvs": [1, 0.14299, 0.86631, 0, 0.79475, 0, 0.68316, 0.14653, 0.64363, 0.31523, 0.58392, 0.39415, 0.50013, 0.35921, 0.4585, 0.43298, 0.37576, 0.431, 0.35002, 0.37276, 0.3205, 0.40382, 0.27509, 0.22524, 0.25163, 0, 0.2041, 0, 0.15793, 0.14953, 0.07846, 0, 0.0285, 0.04665, 0, 0.24271, 0, 0.53547, 0.05566, 0.76259, 0.08896, 0.79753, 0.15254, 0.73153, 0.21763, 0.76647, 0.26305, 0.76841, 0.28651, 0.85771, 0.32208, 0.83441, 0.40294, 0.93584, 0.49528, 1, 0.56642, 1, 0.6395, 0.9531, 0.70686, 0.90845, 0.78633, 0.84245, 0.8764, 0.75122, 0.97933, 0.59398, 1, 0.33387, 0.84797, 0.38434, 0.5885, 0.67273, 0.30013, 0.59314], "triangles": [20, 19, 21, 22, 21, 37, 19, 18, 21, 21, 11, 37, 21, 14, 11, 14, 13, 11, 13, 12, 11, 21, 18, 14, 15, 14, 17, 17, 16, 15, 14, 18, 17, 29, 28, 36, 26, 7, 27, 28, 27, 36, 5, 36, 6, 6, 36, 7, 7, 36, 27, 29, 36, 30, 7, 26, 8, 24, 23, 25, 26, 25, 8, 23, 37, 25, 25, 37, 8, 8, 10, 9, 10, 8, 37, 23, 22, 37, 36, 5, 4, 37, 11, 10, 31, 30, 4, 30, 36, 4, 31, 4, 35, 31, 35, 32, 35, 4, 3, 32, 35, 33, 33, 35, 34, 3, 2, 35, 34, 35, 0, 35, 2, 1, 35, 1, 0], "vertices": [1, 55, -34.09, 10.36, 1, 1, 55, -13.75, -13.79, 1, 1, 55, 0.12, -20.94, 1, 2, 55, 27.45, -21.01, 0.99509, 56, -20.29, -39.01, 0.00491, 2, 55, 41.68, -12.21, 0.87446, 56, -13.41, -23.76, 0.12554, 2, 55, 56.32, -12.2, 0.35791, 56, -1.27, -15.57, 0.64209, 2, 55, 71.2, -23.21, 0.01571, 56, 17.22, -16.38, 0.98429, 1, 56, 25.5, -9.09, 1, 2, 56, 43.43, -7.15, 0.99394, 57, -30.86, -20.46, 0.00606, 2, 56, 49.58, -11.4, 0.95983, 57, -23.55, -22.05, 0.04017, 2, 56, 55.66, -8.03, 0.81674, 57, -19.21, -16.61, 0.18326, 2, 56, 67.27, -21.94, 0.13319, 57, -3.17, -25.05, 0.86681, 2, 56, 74.6, -40.36, 0.00311, 57, 10.62, -39.28, 0.99689, 1, 57, 19.67, -34.23, 1, 1, 57, 22.27, -18.23, 1, 2, 56, 112.09, -35.94, 0, 57, 43.59, -20.89, 1, 2, 56, 122.44, -30.72, 0, 57, 51.17, -12.13, 1, 1, 57, 48.48, 5.45, 1, 1, 57, 36.36, 27.19, 1, 2, 56, 109.43, 29.02, 0, 57, 16.36, 38.14, 1, 2, 56, 101.87, 31.12, 0, 57, 8.58, 37.2, 1, 2, 56, 88.76, 23.92, 0.02343, 57, -0.8, 25.55, 0.97657, 2, 56, 74.32, 25.21, 0.3519, 57, -14.64, 21.23, 0.6481, 2, 56, 64.47, 24.22, 0.80672, 57, -23.36, 16.56, 0.19328, 2, 56, 58.5, 31.15, 0.96922, 57, -31.53, 20.7, 0.03078, 2, 56, 51.03, 28.28, 0.99636, 57, -37.34, 15.19, 0.00364, 1, 56, 32.52, 34.78, 1, 2, 55, 97.08, 24.73, 0.00045, 56, 11.89, 37.83, 0.99955, 2, 55, 83.29, 31.83, 0.06927, 56, -3.51, 36.02, 0.93073, 2, 55, 67.31, 35.58, 0.45428, 56, -18.87, 30.19, 0.54572, 2, 55, 52.51, 38.93, 0.88415, 56, -33.01, 24.7, 0.11585, 2, 55, 34.54, 41.88, 0.99965, 56, -49.55, 17.1, 0.00035, 1, 55, 13.54, 43.98, 1, 1, 55, -12.53, 42.37, 1, 1, 55, -26.66, 24.78, 1, 1, 55, 4.77, 13.42, 1, 1, 56, -5.03, 7.83, 1, 1, 56, 58.19, 8.47, 1], "hull": 35, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 0, 68, 0, 70, 10, 72, 20, 74], "width": 218, "height": 85}}, "a27": {"a27": {"type": "mesh", "uvs": [1, 0.84743, 0.92807, 0.73885, 0.83022, 0.70185, 0.74757, 0.61132, 0.66708, 0.56184, 0.58043, 0.57604, 0.55531, 0.56255, 0.54414, 0.51218, 0.46307, 0.44508, 0.39978, 0.39112, 0.38862, 0.31646, 0.34022, 0.30567, 0.34301, 0.2508, 0.32254, 0.23192, 0.3365, 0.19954, 0.32872, 0.10288, 0.29987, 0, 0.14074, 0, 0, 0.11817, 0, 0.17124, 0.06202, 0.31455, 0.10665, 0.32893, 0.1026, 0.36358, 0.14655, 0.38645, 0.16617, 0.42371, 0.14816, 0.47271, 0.20226, 0.50997, 0.25093, 0.71292, 0.33879, 0.76208, 0.37361, 0.81919, 0.47996, 0.84869, 0.55622, 0.9321, 0.6709, 0.95534, 0.78003, 1, 0.87437, 0.99646, 0.96038, 0.95176, 1, 0.89366, 0.83772, 0.8694, 0.48643, 0.67767, 0.25787, 0.35732], "triangles": [13, 21, 19, 13, 19, 17, 13, 17, 15, 19, 21, 20, 13, 15, 14, 15, 17, 16, 19, 18, 17, 28, 27, 38, 38, 26, 8, 8, 26, 9, 9, 26, 39, 9, 11, 10, 9, 39, 11, 27, 26, 38, 6, 8, 7, 6, 38, 8, 25, 24, 26, 26, 24, 39, 24, 23, 39, 39, 23, 21, 23, 22, 21, 11, 39, 13, 39, 21, 13, 11, 13, 12, 34, 33, 37, 33, 32, 37, 34, 37, 35, 32, 31, 3, 5, 3, 38, 32, 2, 37, 32, 3, 2, 3, 5, 4, 35, 37, 36, 31, 30, 3, 38, 3, 30, 37, 0, 36, 37, 1, 0, 37, 2, 1, 30, 29, 38, 29, 28, 38, 38, 6, 5], "vertices": [1, 52, -11.14, -12.52, 1, 1, 52, 9.17, -23.47, 1, 1, 52, 27.19, -21.05, 1, 1, 52, 47.55, -28.28, 1, 2, 52, 64.03, -29.27, 0.97224, 53, -4.02, -34.91, 0.02776, 2, 52, 75.9, -19.78, 0.70574, 53, 2.25, -21.05, 0.29426, 2, 52, 80.87, -19.78, 0.39883, 53, 6.68, -18.8, 0.60117, 2, 52, 86.94, -26.73, 0.11417, 53, 15.24, -22.23, 0.88583, 2, 52, 105.03, -30.42, 0.00055, 53, 33.03, -17.3, 0.99945, 1, 53, 47.16, -13.62, 1, 2, 53, 59.32, -19.48, 0.99711, 54, -18.28, -22.19, 0.00289, 2, 53, 65.6, -13.61, 0.93018, 54, -12.86, -15.52, 0.06982, 2, 53, 73.48, -19.5, 0.62687, 54, -4.25, -20.27, 0.37313, 2, 53, 78.26, -18.45, 0.41764, 54, 0.34, -18.58, 0.58236, 2, 53, 81.72, -23.69, 0.18752, 54, 4.49, -23.29, 0.81248, 2, 53, 96.82, -32.24, 0.00854, 54, 20.62, -29.69, 0.99146, 1, 54, 39.35, -33.3, 1, 1, 54, 51.45, -8.57, 1, 1, 54, 43.16, 22.59, 1, 1, 54, 34.62, 26.77, 1, 2, 53, 91.17, 27.2, 0.13975, 54, 6.87, 28.41, 0.86025, 2, 53, 84.72, 22.23, 0.3584, 54, 1.16, 22.6, 0.6416, 2, 53, 79.97, 26.28, 0.55039, 54, -4.1, 25.96, 0.44961, 2, 53, 72.32, 22.26, 0.78258, 54, -11.12, 20.93, 0.21742, 2, 53, 64.89, 23.17, 0.96048, 54, -18.6, 20.81, 0.03952, 2, 53, 59.36, 30.65, 0.999, 54, -25.11, 27.47, 0.001, 1, 53, 48.6, 26.61, 1, 2, 52, 113.86, 29.31, 0.05892, 53, 13.76, 39.92, 0.94108, 2, 52, 96.3, 29.63, 0.32995, 53, -2.03, 32.22, 0.67005, 2, 52, 86.07, 35.64, 0.59543, 53, -13.87, 32.94, 0.40457, 2, 52, 67.42, 31.33, 0.92003, 53, -28.53, 20.62, 0.07997, 2, 52, 48.64, 37.99, 0.99986, 53, -48.28, 18.02, 0.00014, 1, 52, 29.27, 32, 1, 1, 52, 8.89, 29.83, 1, 1, 52, -5.08, 21.35, 1, 1, 52, -14.21, 7.14, 1, 1, 52, -15.15, -5.28, 1, 1, 52, 11.5, 4.54, 1, 2, 52, 81.29, 4.02, 0.8075, 53, -3.76, 2.6, 0.1925, 1, 53, 65.89, 3.37, 1], "hull": 37, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 0, 72, 72, 74, 12, 76, 22, 78], "width": 173, "height": 179}}, "a28": {"a28": {"type": "mesh", "uvs": [0.8279, 0.09707, 0.79378, 0, 0.7486, 0, 0.70063, 0.31606, 0.68226, 0.39306, 0.64058, 0.36556, 0.62389, 0.43156, 0.53983, 0.43889, 0.49462, 0.35456, 0.41424, 0.41139, 0.38316, 0.34356, 0.35349, 0.32156, 0.30188, 0.12496, 0.20086, 0, 0.15283, 0, 0.05703, 0.08049, 0, 0.17765, 0, 0.37775, 0.02242, 0.60091, 0.11552, 0.73704, 0.22996, 0.84704, 0.34793, 0.95154, 0.43624, 1, 0.49911, 1, 0.58469, 0.94065, 0.6737, 0.82699, 0.71326, 0.86182, 0.73572, 0.75754, 0.79224, 0.77221, 0.84663, 0.73004, 0.93384, 0.78871, 1, 0.53204, 0.99954, 0.21778, 0.9402, 0, 0.67764, 0.59808, 0.39854, 0.66225], "triangles": [30, 29, 31, 3, 29, 28, 28, 4, 3, 3, 0, 29, 31, 0, 32, 32, 0, 33, 0, 31, 29, 3, 2, 0, 2, 1, 0, 7, 24, 23, 21, 35, 22, 23, 22, 35, 23, 35, 7, 35, 9, 7, 9, 8, 7, 25, 24, 34, 34, 24, 7, 26, 25, 27, 25, 34, 27, 6, 34, 7, 34, 4, 28, 28, 27, 34, 34, 6, 4, 4, 6, 5, 21, 20, 35, 35, 11, 10, 35, 20, 11, 11, 19, 12, 11, 20, 19, 12, 19, 13, 19, 14, 13, 19, 17, 15, 19, 15, 14, 35, 10, 9, 19, 18, 17, 17, 16, 15], "vertices": [1, 60, 37.21, 25.86, 1, 1, 60, 33.47, 36.25, 1, 1, 60, 24.38, 40.03, 1, 2, 59, 68.67, 20.02, 0.21868, 60, 4.53, 19.53, 0.78132, 2, 59, 63.95, 14.05, 0.643, 60, -1.65, 15.1, 0.357, 2, 59, 55.19, 17.39, 0.93649, 60, -9.16, 20.72, 0.06351, 2, 59, 50.94, 12.29, 0.98666, 60, -14.64, 17, 0.01334, 2, 58, 82.15, 29.61, 0.00063, 59, 32.66, 13.78, 0.99937, 2, 58, 70.14, 31.4, 0.03126, 59, 23.68, 21.94, 0.96874, 2, 58, 56.75, 19.14, 0.47866, 59, 5.73, 19.2, 0.52134, 2, 58, 48.11, 21.11, 0.8161, 59, -0.35, 25.63, 0.1839, 2, 58, 41.52, 19.79, 0.93588, 59, -6.57, 28.21, 0.06412, 1, 58, 23.96, 29.34, 1, 1, 58, -0.42, 28.6, 1, 1, 58, -9.74, 23.81, 1, 1, 58, -25.22, 8.25, 1, 1, 58, -32.54, -4.7, 1, 1, 58, -24.85, -19.65, 1, 1, 58, -11.93, -34.08, 1, 1, 58, 11.35, -34.97, 1, 2, 58, 37.76, -31.78, 0.99952, 59, -38.36, -12.57, 0.00048, 2, 58, 64.64, -27.82, 0.70224, 59, -13.81, -24.23, 0.29776, 2, 58, 83.62, -22.64, 0.18262, 59, 4.84, -30.47, 0.81738, 2, 58, 95.81, -16.37, 0.02886, 59, 18.46, -32.04, 0.97114, 1, 59, 37.57, -29.21, 1, 2, 59, 57.93, -21.94, 0.95894, 60, -17.38, -17.84, 0.04106, 2, 59, 66.17, -25.84, 0.86377, 60, -10.54, -23.85, 0.13623, 2, 59, 72.03, -17.69, 0.56988, 60, -2.65, -17.65, 0.43012, 2, 59, 84.13, -20.32, 0.09747, 60, 8.25, -23.52, 0.90253, 2, 59, 96.32, -18.16, 0.00377, 60, 20.56, -24.8, 0.99623, 1, 60, 36.22, -36.65, 1, 1, 60, 57.82, -22.29, 1, 1, 60, 67.86, 2.13, 1, 1, 60, 62.94, 23.99, 1, 1, 59, 60.98, -2.94, 1, 2, 58, 63.34, -1.16, 0.44754, 59, -0.08, -1.35, 0.55246], "hull": 34, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 0, 66, 8, 68, 18, 70], "width": 218, "height": 84}}, "a29": {"a29": {"type": "mesh", "uvs": [0.88999, 0.33124, 0.94399, 0.30418, 1, 0.15438, 1, 0.11379, 0.84599, 0, 0.69399, 0, 0.66039, 0.13187, 0.66405, 0.21303, 0.68107, 0.23091, 0.65887, 0.25451, 0.66405, 0.30314, 0.60285, 0.3242, 0.45488, 0.50729, 0.44674, 0.57094, 0.41123, 0.57952, 0.33206, 0.56093, 0.16245, 0.69462, 0.06774, 0.7461, 0, 0.85241, 0, 0.88816, 0.03779, 0.95466, 0.143, 1, 0.24106, 1, 0.34538, 0.95192, 0.46464, 0.9185, 0.52332, 0.85082, 0.62173, 0.82078, 0.66834, 0.75285, 0.7471, 0.71441, 0.80173, 0.50768, 0.84242, 0.47121, 0.82985, 0.42044, 0.855, 0.38111, 0.89643, 0.35823, 0.73292, 0.36181, 0.53132, 0.66165, 0.1291, 0.88514], "triangles": [1, 0, 2, 8, 2, 0, 4, 6, 5, 2, 8, 4, 6, 4, 8, 8, 7, 6, 4, 3, 2, 27, 35, 28, 28, 35, 29, 29, 12, 34, 34, 12, 11, 11, 10, 34, 13, 12, 35, 12, 29, 35, 29, 31, 30, 29, 34, 31, 31, 34, 32, 32, 0, 33, 32, 34, 0, 0, 34, 8, 34, 10, 8, 10, 9, 8, 23, 22, 36, 20, 36, 21, 22, 21, 36, 20, 19, 36, 25, 24, 23, 23, 36, 16, 14, 25, 23, 14, 23, 16, 35, 25, 14, 19, 18, 36, 16, 15, 14, 18, 17, 36, 36, 17, 16, 25, 35, 26, 35, 14, 13, 26, 35, 27], "vertices": [2, 50, 82.06, -21.42, 0.33402, 51, 0.91, -21.91, 0.66598, 2, 50, 91.26, -26.54, 0.08141, 51, 9.08, -28.54, 0.91859, 1, 51, 37.54, -26.7, 1, 1, 51, 44.2, -23.79, 1, 1, 51, 52.21, 8.77, 1, 1, 51, 41.7, 32.87, 1, 2, 50, 89.89, 31.4, 0.01782, 51, 17.74, 28.77, 0.98218, 2, 50, 78.13, 22.85, 0.18907, 51, 4.68, 22.38, 0.81093, 2, 50, 77.08, 18.63, 0.34383, 51, 2.92, 18.4, 0.65617, 2, 50, 71.44, 19.5, 0.61317, 51, -2.49, 20.23, 0.38683, 2, 50, 64.68, 13.95, 0.9197, 51, -10.11, 15.93, 0.0803, 1, 50, 55.69, 20.69, 1, 2, 49, 73.92, 27.73, 0.10225, 50, 14.23, 23.94, 0.89775, 2, 49, 66.82, 18.71, 0.49902, 50, 3.96, 18.83, 0.50098, 2, 49, 60.77, 20.58, 0.82294, 50, -0.72, 23.1, 0.17706, 2, 49, 50.78, 30.53, 0.98759, 50, -5.5, 36.36, 0.01241, 1, 49, 13.28, 25.26, 1, 1, 49, -5.51, 25.87, 1, 1, 49, -25.39, 15.67, 1, 1, 49, -28.71, 10.2, 1, 1, 49, -29.29, -3.37, 1, 1, 49, -17.92, -19.74, 1, 1, 49, -3.41, -28.54, 1, 1, 49, 16.48, -30.53, 1, 1, 49, 37.23, -36.1, 1, 2, 49, 52.19, -31, 0.96868, 50, -30.5, -19.87, 0.03132, 2, 49, 69.53, -35.22, 0.75185, 50, -16.62, -31.1, 0.24815, 2, 49, 82.73, -29, 0.40262, 50, -2.03, -31.11, 0.59738, 2, 49, 97.95, -30.18, 0.12191, 50, 11.23, -38.67, 0.87809, 1, 50, 47.3, -26.13, 1, 2, 50, 56.63, -28.39, 0.99795, 51, -25.35, -24.38, 0.00205, 2, 50, 63.01, -21.56, 0.94952, 51, -17.89, -18.76, 0.05048, 2, 50, 71.28, -21.31, 0.71659, 51, -9.69, -19.93, 0.28341, 2, 50, 78.65, -25.02, 0.47975, 51, -3.07, -24.86, 0.52025, 1, 50, 62.5, -1.79, 1, 2, 49, 70.92, -2.75, 0.67103, 50, -1.51, -2.33, 0.32897, 1, 49, -9.33, -0.91, 1], "hull": 34, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 0, 66, 20, 68, 26, 70, 38, 72], "width": 173, "height": 179}}, "xlt_add/lizi_1": {"xlt_add/lizi_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28.39, -19.18, -28.61, -19.18, -28.61, 18.82, 28.39, 18.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 57, "height": 38}}, "huoyan_add/huoyan_60": {"huoyan_add/huoyan_00058": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00060": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00062": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00064": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00066": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00068": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00070": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00072": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}}, "huoyan_add/huoyan_61": {"huoyan_add/huoyan_00058": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00060": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00062": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00064": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00066": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00068": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00070": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00072": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}}, "huoyan_add/huoyan_62": {"huoyan_add/huoyan_00058": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00060": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00062": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00064": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00066": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00068": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00070": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00072": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}}, "xlt_add/lizi_5": {"xlt_add/lizi_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28.39, -19.18, -28.61, -19.18, -28.61, 18.82, 28.39, 18.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 57, "height": 38}}, "a1": {"a1": {"type": "mesh", "uvs": [1, 1, 0.75, 1, 0.5, 1, 0.25, 1, 0, 1, 0, 0, 0.25, 0, 0.5, 0, 0.75, 0, 1, 0], "triangles": [0, 1, 9, 1, 8, 9, 1, 2, 8, 2, 7, 8, 2, 3, 7, 3, 6, 7, 3, 4, 6, 4, 5, 6], "vertices": [2, 7, 10.52, -35.7, 0.952, 9, -37.2, -22.24, 0.048, 2, 7, 7.99, -21.93, 0.744, 8, 0.43, -9.92, 0.256, 2, 7, 5.46, -8.16, 0.744, 8, -2.1, 3.85, 0.256, 2, 7, 2.94, 5.61, 0.744, 8, -4.63, 17.62, 0.256, 2, 7, 0.41, 19.38, 0.81, 8, -7.16, 31.38, 0.19, 2, 7, 23.03, 23.53, 0.81, 8, 15.47, 35.54, 0.19, 2, 7, 25.56, 9.76, 0.744, 8, 17.99, 21.77, 0.256, 2, 7, 28.09, -4.01, 0.744, 8, 20.52, 8, 0.256, 2, 7, 30.61, -17.78, 0.744, 8, 23.05, -5.77, 0.256, 2, 7, 33.14, -31.55, 0.952, 9, -14.58, -18.09, 0.048], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 0], "width": 56, "height": 23}}, "huoyankuosna-add/huokuo_0001": {"huoyankuosna-add/huokuo_0001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [54.11, -29.04, -49.89, -29.04, -49.89, 60.96, 54.11, 60.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 104, "height": 90}, "huoyankuosna-add/huokuo_0003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [54.11, -29.04, -49.89, -29.04, -49.89, 60.96, 54.11, 60.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 104, "height": 90}, "huoyankuosna-add/huokuo_0005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [54.11, -29.04, -49.89, -29.04, -49.89, 60.96, 54.11, 60.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 104, "height": 90}, "huoyankuosna-add/huokuo_0007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [54.11, -29.04, -49.89, -29.04, -49.89, 60.96, 54.11, 60.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 104, "height": 90}, "huoyankuosna-add/huokuo_0009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [54.11, -29.04, -49.89, -29.04, -49.89, 60.96, 54.11, 60.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 104, "height": 90}, "huoyankuosna-add/huokuo_0011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [54.11, -29.04, -49.89, -29.04, -49.89, 60.96, 54.11, 60.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 104, "height": 90}, "huoyankuosna-add/huokuo_0013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [54.11, -29.04, -49.89, -29.04, -49.89, 60.96, 54.11, 60.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 104, "height": 90}, "huoyankuosna-add/huokuo_0015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [54.11, -29.04, -49.89, -29.04, -49.89, 60.96, 54.11, 60.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 104, "height": 90}, "huoyankuosna-add/huokuo_0017": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [54.11, -29.04, -49.89, -29.04, -49.89, 60.96, 54.11, 60.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 104, "height": 90}, "huoyankuosna-add/huokuo_0019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [54.11, -29.04, -49.89, -29.04, -49.89, 60.96, 54.11, 60.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 104, "height": 90}, "huoyankuosna-add/huokuo_0021": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [54.11, -29.04, -49.89, -29.04, -49.89, 60.96, 54.11, 60.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 104, "height": 90}}, "huoyan_add/huoyan_66": {"huoyan_add/huoyan_00058": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00060": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00062": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00064": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00066": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00068": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00070": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}, "huoyan_add/huoyan_00072": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [59, -24.23, -58, -24.23, -58, 174.77, 59, 174.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 199}}, "a4": {"a4": {"type": "mesh", "uvs": [0.47747, 0, 0.61989, 0, 0.71927, 0.07758, 0.8077, 0.19271, 0.88652, 0.21329, 0.98906, 0.43473, 1, 0.57889, 0.92889, 0.73014, 0.85165, 0.87344, 0.7917, 0.97826, 0.75563, 1, 0.74339, 0.86094, 0.72401, 0.7257, 0.64037, 0.81522, 0.53395, 0.84904, 0.54691, 0.74894, 0.43756, 0.79932, 0.32821, 0.83699, 0.18922, 0.92011, 0.21937, 0.76886, 0.12695, 0.74941, 0.00966, 0.71999, 0.0059, 0.68505, 0.08905, 0.59396, 0.19124, 0.49408, 0.29732, 0.35065, 0.39481, 0.20758, 0.29302, 0.06625, 0.3263, 0.01997, 0.73481, 0.5864, 0.78714, 0.38263, 0.58928, 0.50832, 0.70516, 0.31187, 0.31617, 0.66004, 0.37901, 0.46199, 0.46446, 0.26093, 0.60574, 0.18734, 0.74004, 0.23829], "triangles": [6, 30, 5, 6, 7, 30, 30, 4, 5, 30, 3, 4, 32, 37, 30, 30, 37, 3, 32, 36, 37, 36, 2, 37, 37, 2, 3, 36, 1, 2, 36, 0, 1, 31, 35, 36, 36, 35, 0, 29, 30, 7, 29, 32, 30, 31, 36, 32, 31, 32, 29, 10, 11, 9, 9, 11, 8, 7, 8, 12, 8, 11, 12, 14, 15, 13, 17, 33, 16, 12, 13, 31, 15, 16, 34, 16, 33, 34, 13, 15, 31, 15, 34, 31, 12, 31, 29, 33, 24, 34, 34, 24, 25, 34, 35, 31, 34, 26, 35, 34, 25, 26, 27, 28, 26, 35, 26, 0, 26, 28, 0, 12, 29, 7, 18, 19, 17, 33, 19, 24, 21, 23, 20, 19, 20, 24, 20, 23, 24, 21, 22, 23, 19, 33, 17], "vertices": [5, 62, -25.2, -27.56, 0.00326, 61, 0.13, -20.98, 0.43283, 10, 85.26, 59.08, 0.09156, 11, 54.8, 58.68, 0.03852, 5, 94.96, 47.87, 0.43385, 5, 62, -37.14, -24.84, 8e-05, 61, -10.86, -15.57, 0.25955, 10, 82.09, 47.25, 0.12593, 11, 51.62, 46.85, 0.01541, 5, 91.79, 36.04, 0.59904, 4, 61, -16.71, -8.11, 0.12494, 10, 75.9, 40.06, 0.14459, 11, 45.43, 39.66, 0.00385, 5, 85.6, 28.85, 0.72661, 3, 61, -20.85, 0.72, 0.04943, 10, 68.04, 34.3, 0.15141, 5, 77.73, 23.09, 0.79917, 3, 61, -26.45, 4.69, 0.03266, 10, 65.22, 28.04, 0.152, 5, 74.92, 16.83, 0.81534, 3, 61, -29.18, 19.11, 0.07493, 10, 51.6, 22.56, 0.152, 5, 61.3, 11.35, 0.77307, 3, 61, -26.66, 26.38, 0.18298, 10, 43.98, 23.64, 0.14637, 5, 53.68, 12.43, 0.67065, 3, 61, -17.63, 30.88, 0.35565, 10, 37.82, 31.62, 0.12948, 5, 47.52, 20.41, 0.51486, 3, 61, -8.32, 34.76, 0.5639, 10, 32.21, 40.01, 0.0957, 5, 41.91, 28.8, 0.3404, 3, 61, -1.24, 37.47, 0.75052, 10, 28.18, 46.43, 0.0563, 5, 37.88, 35.22, 0.19318, 4, 61, 2.05, 37.14, 0.85574, 10, 27.87, 49.72, 0.0263, 11, -2.59, 49.32, 0.00289, 5, 37.57, 38.51, 0.11508, 5, 62, -37.39, 22.01, 8e-05, 61, -0.26, 30.06, 0.8531, 10, 35.27, 48.83, 0.02024, 11, 4.8, 48.43, 0.01083, 5, 44.96, 37.62, 0.11575, 5, 62, -37.35, 14.65, 0.0012, 61, -1.92, 22.89, 0.75949, 10, 42.62, 48.58, 0.03662, 11, 12.15, 48.18, 0.0269, 5, 52.32, 37.37, 0.17579, 5, 62, -29.28, 17.68, 0.00927, 61, 6.62, 23.98, 0.78817, 10, 39.9, 56.75, 0.01839, 11, 9.44, 56.36, 0.05802, 5, 49.6, 45.54, 0.12616, 5, 62, -19.96, 17.4, 0.03291, 61, 15.63, 21.55, 0.74771, 10, 40.55, 66.06, 0.01839, 11, 10.08, 65.66, 0.0768, 5, 50.24, 54.85, 0.1242, 5, 62, -22.23, 12.47, 0.08263, 61, 12.29, 17.28, 0.63781, 10, 45.38, 63.61, 0.03662, 11, 14.91, 63.21, 0.07421, 5, 55.08, 52.4, 0.16872, 5, 62, -12.46, 12.99, 0.21821, 61, 21.9, 15.53, 0.58554, 10, 45.24, 73.38, 0.01461, 11, 14.77, 72.98, 0.09317, 5, 54.94, 62.17, 0.08847, 5, 62, -2.85, 12.86, 0.36062, 61, 31.22, 13.17, 0.50039, 10, 45.75, 82.98, 0.00378, 11, 15.29, 82.58, 0.09822, 5, 55.45, 71.77, 0.037, 5, 62, 9.78, 14.5, 0.47691, 61, 43.9, 11.85, 0.40751, 10, 44.6, 95.67, 0.00244, 11, 14.13, 95.27, 0.09317, 5, 54.3, 84.46, 0.01997, 5, 62, 5.48, 7.26, 0.54248, 61, 38.03, 5.8, 0.33162, 10, 51.67, 91.08, 0.0105, 11, 21.2, 90.69, 0.0771, 5, 61.37, 79.87, 0.0383, 5, 62, 13, 4.49, 0.70629, 61, 44.71, 1.37, 0.18783, 10, 54.72, 98.49, 0.00244, 11, 24.26, 98.1, 0.09317, 5, 64.42, 87.28, 0.01027, 4, 62, 22.49, 0.74, 0.78701, 61, 53.07, -4.48, 0.11025, 11, 28.38, 107.43, 0.10111, 5, 68.54, 96.62, 0.00163, 3, 62, 22.39, -1.14, 0.77204, 61, 52.55, -6.29, 0.12396, 11, 30.25, 107.26, 0.104, 3, 62, 14.35, -4.26, 0.66873, 61, 44, -7.47, 0.22727, 11, 33.06, 99.11, 0.104, 4, 62, 4.61, -7.48, 0.50331, 61, 33.78, -8.34, 0.39075, 11, 35.89, 89.25, 0.104, 5, 76.06, 78.43, 0.00195, 4, 62, -5.97, -12.87, 0.32104, 61, 22.24, -11.14, 0.56154, 11, 40.87, 78.46, 0.104, 5, 81.03, 67.65, 0.01342, 5, 62, -15.83, -18.4, 0.16694, 61, 11.37, -14.24, 0.67715, 10, 76.48, 68.8, 0.00504, 11, 46.02, 68.4, 0.10015, 5, 86.18, 57.59, 0.05072, 5, 62, -8.95, -27.65, 0.06746, 61, 15.92, -24.83, 0.69057, 10, 85.99, 75.31, 0.02074, 11, 55.52, 74.91, 0.08859, 5, 95.69, 64.1, 0.13263, 5, 62, -12.29, -29.4, 0.01919, 61, 12.27, -25.76, 0.59642, 10, 87.61, 71.91, 0.05274, 11, 57.15, 71.51, 0.06548, 5, 97.31, 60.7, 0.26617, 5, 62, -39.89, 7.66, 8e-05, 61, -6.02, 16.68, 0.59977, 10, 49.51, 45.77, 0.08128, 11, 19.04, 45.37, 0.01083, 5, 59.21, 34.55, 0.30805, 4, 61, -14.82, 8.97, 0.51271, 10, 58.77, 38.62, 0.10567, 11, 28.31, 38.22, 0.00433, 5, 68.47, 27.41, 0.37729, 5, 62, -28.6, 0.85, 0.03377, 61, 3.39, 7.44, 0.53598, 10, 56.75, 56.78, 0.08128, 11, 26.29, 56.38, 0.04502, 5, 66.45, 45.57, 0.30395, 5, 62, -40.63, -7.1, 0.01508, 61, -10.15, 2.5, 0.47772, 10, 64.22, 44.45, 0.10567, 11, 33.76, 44.06, 0.02744, 5, 73.92, 33.24, 0.37408, 5, 62, -3.92, 3.48, 0.37513, 61, 28.02, 4.29, 0.41123, 10, 55.08, 81.55, 0.03948, 11, 24.61, 81.15, 0.04309, 5, 64.78, 70.34, 0.13106, 5, 62, -11.52, -5.56, 0.21106, 61, 18.53, -2.74, 0.42991, 10, 63.81, 73.61, 0.07556, 11, 33.35, 73.21, 0.01637, 5, 73.51, 62.4, 0.26711, 5, 62, -21.04, -14.32, 0.09191, 61, 7.24, -9.06, 0.36489, 10, 72.2, 63.75, 0.11111, 11, 41.73, 63.35, 0.00385, 5, 81.9, 52.54, 0.42824, 4, 62, -33.76, -15.43, 0.029, 61, -5.39, -7.2, 0.26677, 10, 72.81, 51, 0.13541, 5, 82.51, 39.79, 0.56882, 4, 62, -44.42, -10.24, 0.00855, 61, -14.56, 0.32, 0.20991, 10, 67.21, 40.55, 0.14533, 5, 76.91, 29.33, 0.6362], "hull": 29, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 16, 18, 18, 20, 20, 22, 22, 24, 28, 30, 36, 38, 42, 44, 50, 52, 52, 54, 54, 56, 38, 40, 40, 42, 44, 46, 46, 48, 48, 50, 34, 36, 30, 32, 32, 34, 24, 26, 26, 28, 2, 0, 0, 56, 12, 14, 14, 16, 24, 58, 58, 60, 30, 62, 62, 64, 38, 66, 66, 68, 68, 70, 70, 72, 72, 74], "width": 86, "height": 53}}, "xlt_add/lizi_6": {"xlt_add/lizi_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28.39, -19.18, -28.61, -19.18, -28.61, 18.82, 28.39, 18.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 57, "height": 38}}, "a20": {"a20": {"type": "mesh", "uvs": [0.21279, 0, 0.38641, 0, 0.45955, 0.07749, 0.54471, 0.37264, 0.5965, 0.40312, 0.58465, 0.45586, 0.63753, 0.48695, 0.62625, 0.53428, 0.7221, 0.62169, 0.84301, 0.75235, 0.99464, 0.85734, 1, 0.93078, 0.84554, 0.99987, 0.63632, 1, 0.4063, 0.86759, 0.413, 0.7876, 0.25576, 0.68371, 0.31238, 0.58753, 0.29226, 0.57175, 0.28495, 0.51208, 0.22506, 0.45767, 0.21773, 0.38146, 0.0011, 0.16644, 0, 0.10472, 0.46529, 0.53415, 0.23497, 0.13028], "triangles": [21, 22, 25, 22, 23, 25, 23, 0, 25, 25, 0, 1, 8, 24, 7, 24, 8, 17, 17, 18, 24, 18, 19, 24, 7, 24, 6, 24, 5, 6, 24, 19, 5, 19, 20, 5, 20, 3, 5, 20, 21, 3, 5, 3, 4, 21, 25, 3, 25, 2, 3, 25, 1, 2, 13, 14, 9, 13, 9, 12, 9, 15, 8, 15, 9, 14, 12, 10, 11, 12, 9, 10, 16, 17, 15, 8, 15, 17], "vertices": [2, 15, -10.18, 1.69, 0.06588, 14, 44.13, 2.23, 0.93412, 2, 15, -7.83, 9.68, 0.67938, 14, 46.6, 10.19, 0.32062, 2, 15, -0.22, 11.11, 0.97574, 14, 54.23, 11.49, 0.02426, 1, 15, 26.13, 7.63, 1, 1, 15, 29.44, 9.25, 1, 2, 20, -6.26, 8.31, 0.00715, 15, 33.78, 7.38, 0.99285, 2, 20, -2.69, 9.48, 0.07346, 15, 37.15, 9.04, 0.92654, 2, 20, 0.93, 7.25, 0.37684, 15, 41.04, 7.34, 0.62316, 2, 20, 9.91, 8.24, 0.97958, 15, 49.8, 9.56, 0.02042, 1, 20, 22.9, 8.73, 1, 1, 20, 34.41, 11.51, 1, 1, 20, 40.47, 9.05, 1, 1, 20, 43.02, -0.24, 1, 1, 20, 38.89, -9.39, 1, 1, 20, 23.6, -14.6, 1, 1, 20, 17.25, -11.37, 1, 2, 20, 5.71, -14.43, 0.96542, 15, 48.79, -13.47, 0.03458, 2, 20, -0.97, -8.43, 0.48774, 15, 41.34, -8.46, 0.51226, 2, 20, -2.64, -8.73, 0.29929, 15, 39.72, -8.99, 0.70071, 2, 20, -7.63, -6.86, 0.01792, 15, 34.53, -7.83, 0.98208, 1, 15, 29.07, -9.22, 1, 2, 15, 22.47, -7.65, 0.9974, 14, 76.62, -7.62, 0.0026, 2, 15, 1.18, -12.24, 0.10158, 14, 55.26, -11.87, 0.89842, 2, 15, -4.11, -10.74, 0.00146, 14, 50, -10.29, 0.99854, 1, 15, 38.85, -0.08, 1, 2, 15, 1.25, -0.56, 0.35624, 14, 55.52, -0.2, 0.64376], "hull": 24, "edges": [0, 46, 0, 2, 2, 4, 20, 22, 22, 24, 24, 26, 26, 28, 40, 42, 42, 44, 44, 46, 38, 40, 36, 38, 32, 34, 34, 36, 12, 14, 10, 12, 4, 6, 14, 16, 16, 18, 18, 20, 28, 30, 30, 32, 12, 48, 6, 8, 8, 10, 2, 50], "width": 48, "height": 89}}, "a7": {"a7": {"x": 24.99, "y": 0.19, "rotation": 29.42, "width": 429, "height": 244}}, "a3": {"a3": {"type": "mesh", "uvs": [0.46069, 0.00358, 0.48269, 0, 0.50559, 0.02468, 0.51132, 0.05338, 0.50495, 0.08052, 0.48306, 0.09889, 0.44161, 0.10312, 0.42407, 0.12113, 0.45507, 0.15279, 0.50087, 0.18069, 0.54045, 0.21288, 0.61064, 0.22571, 0.68823, 0.26585, 0.74344, 0.30785, 0.79693, 0.34855, 0.78886, 0.39689, 0.82008, 0.42129, 0.86925, 0.44326, 0.96733, 0.47634, 0.99918, 0.52208, 0.99198, 0.61944, 0.99182, 0.68637, 0.90445, 0.75509, 0.85775, 0.81095, 0.82588, 0.87177, 0.78337, 0.9134, 0.71434, 0.95896, 0.63944, 1, 0.54831, 1, 0.43875, 0.95123, 0.34652, 0.90233, 0.24346, 0.86319, 0.15966, 0.8261, 0.12891, 0.79446, 0.13352, 0.7208, 0.06709, 0.66725, 0.04823, 0.60253, 0.06365, 0.54135, 0.09911, 0.5046, 0.06126, 0.47666, 0.02118, 0.43989, 0, 0.39325, 0.0159, 0.34927, 0.0491, 0.31716, 0.0818, 0.31702, 0.06849, 0.36543, 0.07913, 0.39874, 0.11625, 0.41022, 0.12596, 0.36608, 0.16858, 0.33419, 0.20246, 0.3014, 0.22375, 0.25584, 0.20981, 0.22238, 0.17171, 0.19142, 0.21588, 0.1916, 0.27573, 0.21705, 0.32456, 0.26354, 0.3368, 0.2328, 0.37235, 0.23003, 0.35989, 0.1972, 0.31501, 0.15433, 0.30086, 0.12203, 0.31106, 0.08091, 0.36436, 0.04975, 0.40773, 0.04902, 0.44686, 0.04555, 0.46786, 0.03173, 0.69866, 0.70373, 0.54799, 0.63376, 0.43704, 0.61071, 0.44115, 0.65352, 0.39458, 0.71855, 0.35622, 0.7309, 0.35075, 0.8098, 0.40964, 0.89953, 0.87063, 0.66589, 0.86004, 0.73872, 0.66945, 0.81862, 0.65768, 0.85892, 0.67416, 0.89356, 0.62827, 0.90841, 0.6118, 0.93387, 0.60945, 0.95932, 0.57063, 0.70043, 0.5318, 0.76123, 0.5071, 0.80931, 0.5071, 0.86447, 0.52004, 0.91467, 0.77886, 0.72107, 0.76121, 0.78683, 0.75298, 0.83279, 0.73768, 0.87662, 0.71298, 0.91127, 0.67533, 0.93248, 0.33309, 0.30593, 0.34535, 0.37793, 0.40797, 0.43929, 0.46787, 0.5072, 0.48829, 0.56366, 0.54879, 0.25385, 0.50931, 0.26752, 0.514, 0.31252, 0.54773, 0.35567, 0.60461, 0.36921, 0.59563, 0.32442, 0.64197, 0.30464, 0.64136, 0.27594, 0.60052, 0.2383, 0.67767, 0.40948, 0.75663, 0.45693, 0.79883, 0.51748, 0.79883, 0.5903, 0.77432, 0.64184, 0.31044, 0.72179, 0.23284, 0.70297, 0.18792, 0.76515, 0.20834, 0.80443, 0.3363, 0.85024, 0.27777, 0.84861, 0.17996, 0.44804, 0.22489, 0.52659, 0.21672, 0.59859, 0.21944, 0.65668, 0.50392, 0.40094, 0.58424, 0.46231, 0.64686, 0.53349, 0.66047, 0.60058, 0.8975, 0.49763, 0.91111, 0.55654, 0.90022, 0.61217], "triangles": [41, 45, 46, 41, 42, 45, 42, 43, 45, 45, 43, 44, 104, 106, 105, 106, 99, 107, 106, 104, 99, 105, 12, 13, 105, 106, 12, 106, 107, 12, 107, 11, 12, 99, 10, 107, 107, 10, 11, 4, 65, 3, 3, 65, 66, 66, 2, 3, 66, 1, 2, 66, 0, 1, 6, 62, 63, 6, 63, 64, 6, 65, 5, 6, 64, 65, 5, 65, 4, 10, 58, 9, 9, 59, 8, 9, 58, 59, 8, 60, 7, 8, 59, 60, 62, 6, 7, 7, 60, 61, 62, 7, 61, 94, 95, 50, 50, 95, 49, 50, 56, 94, 50, 51, 56, 100, 94, 58, 94, 100, 101, 57, 58, 56, 101, 100, 99, 58, 94, 56, 100, 58, 10, 100, 10, 99, 51, 55, 56, 51, 52, 55, 52, 54, 55, 52, 53, 54, 14, 108, 13, 48, 49, 119, 38, 39, 119, 39, 47, 119, 47, 40, 46, 47, 39, 40, 47, 48, 119, 49, 95, 119, 108, 103, 13, 15, 108, 14, 102, 104, 103, 102, 123, 101, 123, 95, 101, 34, 35, 122, 122, 35, 121, 122, 121, 69, 121, 120, 69, 69, 120, 97, 129, 128, 20, 20, 128, 19, 129, 111, 128, 111, 110, 128, 111, 125, 110, 128, 127, 19, 97, 124, 125, 98, 97, 125, 28, 82, 27, 26, 27, 82, 26, 82, 93, 93, 82, 81, 29, 87, 28, 28, 87, 82, 82, 87, 81, 93, 92, 26, 26, 92, 25, 30, 74, 29, 29, 74, 87, 81, 80, 93, 81, 87, 80, 80, 79, 93, 93, 79, 92, 74, 86, 87, 87, 86, 80, 92, 91, 25, 25, 91, 24, 92, 79, 91, 80, 78, 79, 80, 86, 78, 31, 118, 30, 118, 117, 30, 30, 117, 74, 86, 74, 73, 79, 78, 91, 91, 90, 24, 91, 78, 90, 24, 90, 23, 74, 117, 73, 86, 73, 85, 71, 85, 73, 73, 72, 71, 86, 85, 78, 32, 116, 31, 31, 116, 118, 78, 77, 90, 78, 85, 77, 117, 118, 73, 118, 116, 73, 90, 89, 23, 90, 77, 89, 32, 33, 116, 85, 84, 77, 77, 84, 67, 77, 67, 89, 67, 84, 83, 23, 76, 22, 23, 89, 76, 116, 113, 73, 73, 113, 72, 85, 71, 84, 33, 115, 116, 113, 115, 114, 113, 116, 115, 33, 34, 115, 89, 88, 76, 89, 67, 88, 115, 34, 114, 84, 71, 83, 22, 76, 21, 76, 75, 21, 76, 88, 75, 72, 113, 71, 70, 71, 113, 88, 67, 75, 34, 122, 114, 71, 70, 83, 122, 70, 113, 67, 112, 75, 112, 67, 126, 122, 113, 114, 70, 68, 83, 126, 67, 68, 67, 83, 68, 21, 75, 20, 20, 75, 129, 75, 112, 129, 122, 69, 70, 70, 69, 68, 112, 111, 129, 112, 126, 111, 69, 98, 68, 68, 98, 126, 69, 97, 98, 98, 125, 126, 126, 125, 111, 120, 96, 97, 96, 119, 95, 124, 96, 123, 124, 97, 96, 124, 123, 103, 103, 123, 102, 96, 95, 123, 110, 127, 128, 125, 109, 110, 109, 124, 108, 109, 125, 124, 127, 18, 19, 110, 17, 127, 17, 109, 16, 17, 110, 109, 127, 17, 18, 124, 103, 108, 109, 15, 16, 109, 108, 15, 35, 36, 121, 36, 37, 121, 121, 37, 120, 120, 37, 38, 38, 119, 120, 96, 120, 119, 103, 104, 105, 102, 101, 104, 101, 95, 94, 101, 99, 104, 40, 41, 46, 13, 103, 105], "vertices": [3, 30, 16, 11.39, 0.05679, 31, 11.55, 2.59, 0.84321, 9, 121.95, 53.93, 0.1, 3, 30, 18.69, 11, 0.0169, 31, 12.49, 0.04, 0.8831, 9, 123.12, 51.49, 0.1, 4, 29, 33.67, -18.81, 0.00126, 30, 19.25, 5.44, 0.09599, 31, 7.86, -3.1, 0.80275, 9, 118.8, 47.92, 0.1, 4, 29, 28.08, -17.58, 0.00986, 30, 17.62, -0.05, 0.26846, 31, 2.26, -4.28, 0.62168, 9, 113.34, 46.23, 0.1, 4, 29, 23.25, -15.09, 0.04733, 30, 14.79, -4.68, 0.4836, 31, -3.16, -4, 0.36907, 9, 107.91, 46, 0.1, 4, 29, 20.68, -11.43, 0.16669, 30, 10.96, -6.98, 0.57928, 31, -7.01, -1.72, 0.15404, 9, 103.87, 47.91, 0.1, 5, 29, 21.52, -6.49, 0.37109, 30, 6.1, -5.8, 0.49368, 31, -8.28, 3.12, 0.03511, 33, 47.75, 4.54, 0.00012, 9, 102.15, 52.61, 0.1, 6, 28, 52.3, 2.35, 0.01043, 29, 18.85, -3.35, 0.60071, 30, 2.76, -8.24, 0.28007, 31, -12.02, 4.88, 0.00195, 33, 45.72, 8.14, 0.00684, 9, 98.27, 54.02, 0.1, 5, 28, 45.9, -1.1, 0.06136, 29, 11.71, -4.76, 0.68165, 30, 3.66, -15.46, 0.10281, 33, 38.44, 8.1, 0.05419, 9, 92.77, 49.26, 0.1, 6, 28, 40.17, -6.34, 0.13162, 29, 4.7, -8.08, 0.54555, 30, 6.47, -22.69, 0.01593, 33, 30.93, 6.16, 0.20609, 32, 46.47, 15.2, 0.00082, 9, 88.32, 42.9, 0.1, 6, 28, 33.62, -10.8, 0.25268, 29, -2.88, -10.42, 0.23929, 30, 8.27, -30.41, 0.00013, 33, 23.05, 5.3, 0.42478, 32, 39.22, 12, 0.00812, 9, 82.9, 37.11, 0.075, 5, 28, 30.77, -19.05, 0.11075, 29, -8.03, -17.46, 0.09585, 33, 16.66, -0.65, 0.58249, 32, 34.92, 4.4, 0.11091, 9, 81.91, 28.44, 0.1, 5, 28, 22.47, -27.98, 0.04049, 29, -18.59, -23.55, 0.01895, 33, 5.14, -4.64, 0.52765, 32, 25.14, -2.87, 0.31291, 9, 75.76, 17.92, 0.1, 5, 7, 116.49, -3.5, 0.1, 29, -28.6, -27, 4e-05, 33, -5.35, -6.14, 0.29879, 32, 15.59, -7.46, 0.50117, 9, 68.77, 9.96, 0.1, 4, 7, 109.72, -11.22, 0.3, 33, -15.51, -7.59, 0.10165, 32, 6.34, -11.91, 0.49835, 9, 61.99, 2.24, 0.1, 4, 7, 100.13, -12, 0.6, 33, -23.31, -1.95, 0.00446, 32, -2.79, -8.88, 0.29554, 9, 52.4, 1.46, 0.1, 3, 7, 96.05, -16.53, 0.8, 32, -8.32, -11.45, 0.1, 9, 48.32, -3.07, 0.1, 3, 7, 92.83, -23.07, 0.877, 9, 45.1, -9.61, 0.075, 8, 85.26, -11.06, 0.048, 2, 7, 88.49, -35.73, 0.9, 9, 40.77, -22.27, 0.1, 2, 7, 80.27, -41.09, 0.9, 9, 32.54, -27.63, 0.1, 2, 7, 61.16, -43.73, 0.9, 9, 13.43, -30.27, 0.1, 2, 7, 48.12, -46.11, 0.9, 9, 0.39, -32.65, 0.1, 2, 7, 32.86, -38.34, 0.9, 9, -14.87, -24.88, 0.1, 2, 7, 20.98, -34.87, 0.9, 9, -26.75, -21.41, 0.1, 2, 7, 8.45, -33.31, 0.9, 9, -39.28, -19.85, 0.1, 2, 7, -0.57, -29.83, 0.9, 9, -48.3, -16.37, 0.1, 2, 7, -10.93, -23.38, 0.9, 9, -58.66, -9.92, 0.1, 2, 7, -20.53, -16.08, 0.9, 9, -68.26, -2.62, 0.1, 2, 7, -22.49, -5.41, 0.9, 9, -70.22, 8.05, 0.1, 2, 7, -15.35, 9.15, 0.9, 9, -63.07, 22.61, 0.1, 2, 7, -7.81, 21.7, 0.9, 9, -55.53, 35.16, 0.1, 2, 7, -2.4, 35.16, 0.9, 9, -50.13, 48.62, 0.1, 2, 7, 3.02, 46.29, 0.9, 9, -44.7, 59.75, 0.1, 2, 7, 8.52, 51.03, 0.9, 9, -39.2, 64.49, 0.1, 2, 7, 22.97, 53.12, 0.9, 9, -24.76, 66.58, 0.1, 2, 7, 31.97, 62.81, 0.9, 9, -15.76, 76.27, 0.1, 2, 7, 44.17, 67.33, 0.9, 9, -3.56, 80.79, 0.1, 2, 7, 56.41, 67.71, 0.9, 9, 8.69, 81.17, 0.1, 2, 7, 64.33, 64.88, 0.93333, 9, 16.61, 78.34, 0.06667, 4, 7, 68.96, 70.31, 0.83333, 34, 3.47, 7.94, 0.05148, 35, -16.4, -5.68, 0.04852, 9, 21.23, 83.77, 0.06667, 4, 7, 75.26, 76.31, 0.63333, 34, 12.17, 7.71, 0.10296, 35, -10.23, 0.45, 0.19704, 9, 27.53, 89.77, 0.06667, 4, 7, 83.88, 80.46, 0.3, 34, 21.19, 4.52, 0.15444, 35, -1.69, 4.77, 0.44556, 9, 36.16, 93.92, 0.1, 4, 7, 92.79, 80.17, 0.1, 34, 27.27, -2, 0.10296, 35, 7.22, 4.66, 0.69704, 9, 45.07, 93.63, 0.1, 3, 34, 30.24, -8.87, 0.0539, 35, 14.24, 2.07, 0.8461, 9, 52.03, 90.89, 0.1, 5, 7, 100.49, 73.61, 0.0624, 28, 15.1, 44.52, 0.00022, 34, 28.04, -12.09, 0.03998, 35, 15.05, -1.74, 0.7974, 9, 52.76, 87.07, 0.1, 5, 7, 90.78, 73.44, 0.2248, 28, 5.58, 46.47, 0.00044, 34, 21.07, -5.32, 0.07753, 35, 5.34, -2.11, 0.59723, 9, 43.05, 86.9, 0.1, 5, 7, 84.52, 71, 0.4558, 28, -1.06, 45.46, 0.01448, 34, 14.93, -2.6, 0.1284, 35, -0.86, -4.67, 0.30132, 9, 36.79, 84.46, 0.1, 6, 7, 83.08, 66.25, 0.62625, 28, -3.5, 41.13, 0.06364, 34, 10.55, -4.94, 0.13099, 35, -2.21, -9.45, 0.07613, 9, 35.35, 79.71, 0.075, 8, 75.52, 78.25, 0.028, 5, 7, 91.88, 66.69, 0.4222, 28, 5.19, 39.64, 0.22508, 34, 17.07, -10.87, 0.25104, 35, 6.59, -8.84, 0.00168, 9, 44.16, 80.15, 0.1, 4, 7, 99.01, 62.84, 0.1912, 28, 11.31, 34.33, 0.41563, 34, 19.36, -18.63, 0.29317, 9, 51.28, 76.3, 0.1, 4, 7, 106.12, 60.05, 0.0456, 28, 17.64, 30.06, 0.60909, 34, 22.4, -25.65, 0.27865, 9, 58.4, 73.51, 0.06667, 3, 28, 26.56, 27.18, 0.72205, 34, 28.37, -32.87, 0.21128, 9, 67.73, 72.65, 0.06667, 3, 28, 33.24, 28.59, 0.77378, 34, 34.76, -35.29, 0.15955, 9, 73.95, 75.47, 0.06667, 3, 28, 39.54, 32.88, 0.76607, 34, 42.38, -35.05, 0.13393, 9, 79.16, 81.04, 0.1, 4, 28, 39.31, 27.63, 0.77106, 29, 13.86, 24.65, 0.00189, 34, 39.35, -39.35, 0.12704, 9, 80.07, 75.86, 0.1, 4, 28, 34, 20.71, 0.77205, 29, 6.75, 19.59, 0.01243, 34, 31.15, -42.33, 0.11552, 9, 76.4, 67.95, 0.1, 6, 7, 116.12, 47.11, 0.07667, 28, 24.58, 15.25, 0.70198, 29, -3.86, 17.15, 0.04175, 34, 20.28, -41.86, 0.0846, 9, 68.4, 60.57, 0.075, 8, 108.56, 59.12, 0.02, 4, 28, 30.61, 13.57, 0.66922, 29, 1.41, 13.76, 0.17583, 34, 24.45, -46.52, 0.05495, 9, 74.65, 60.24, 0.1, 4, 28, 30.99, 9.32, 0.49803, 29, 0.53, 9.59, 0.37708, 34, 22.49, -50.31, 0.02489, 9, 75.95, 56.18, 0.1, 5, 28, 37.54, 10.55, 0.27115, 29, 7.15, 8.84, 0.6196, 30, -10.22, -19.04, 0.00073, 34, 28.67, -52.8, 0.00852, 9, 82.07, 58.81, 0.1, 5, 28, 46.23, 15.56, 0.10145, 29, 16.93, 11.07, 0.75962, 30, -11.76, -9.12, 0.03668, 34, 38.69, -53.25, 0.00225, 9, 89.46, 65.59, 0.1, 6, 28, 52.69, 17, 0.01534, 29, 23.52, 10.55, 0.71614, 30, -10.76, -2.58, 0.16696, 31, -13.48, 19.47, 0.00141, 34, 44.9, -55.51, 0.00015, 9, 95.44, 68.41, 0.1, 5, 28, 60.78, 15.48, 4e-05, 29, 30.81, 6.72, 0.50138, 30, -6.42, 4.41, 0.38686, 31, -5.26, 18.98, 0.01173, 9, 103.67, 68.68, 0.1, 4, 29, 34.53, -1.31, 0.24163, 30, 1.85, 7.55, 0.58777, 31, 1.44, 13.2, 0.0706, 9, 110.88, 63.56, 0.1, 4, 29, 32.96, -6.23, 0.07331, 30, 6.65, 5.64, 0.6027, 31, 2.04, 8.08, 0.22399, 9, 111.96, 58.51, 0.1, 4, 29, 32.07, -10.85, 0.00426, 30, 11.19, 4.42, 0.43619, 31, 3.13, 3.5, 0.45955, 9, 113.48, 54.05, 0.1, 3, 30, 14.57, 5.94, 0.202, 31, 6.08, 1.25, 0.698, 9, 116.62, 52.09, 0.1, 2, 7, 38.44, -12.42, 0.726, 8, 30.87, -0.41, 0.274, 2, 7, 48.82, 7.72, 0.696, 8, 41.26, 19.73, 0.304, 2, 7, 50.93, 21.53, 0.696, 8, 43.37, 33.54, 0.304, 2, 7, 42.68, 19.52, 0.70133, 8, 35.12, 31.53, 0.29867, 2, 7, 29.02, 22.65, 0.73333, 8, 21.45, 34.65, 0.26667, 2, 7, 25.79, 26.69, 0.78133, 8, 18.22, 38.7, 0.21867, 2, 7, 10.3, 24.51, 0.8512, 8, 2.74, 36.52, 0.1488, 2, 7, -5.91, 14.41, 0.864, 8, -13.47, 26.42, 0.136, 2, 7, 49.5, -31.19, 0.79467, 8, 41.94, -19.18, 0.20533, 2, 7, 35.09, -32.55, 0.844, 8, 27.53, -20.55, 0.156, 2, 7, 15.44, -13.1, 0.696, 8, 7.87, -1.1, 0.304, 2, 7, 7.33, -13.17, 0.696, 8, -0.23, -1.16, 0.304, 2, 7, 0.94, -16.34, 0.696, 8, -6.62, -4.33, 0.304, 2, 7, -2.94, -11.5, 0.70667, 8, -10.5, 0.51, 0.29333, 2, 7, -8.25, -10.48, 0.74133, 8, -15.81, 1.53, 0.25867, 2, 7, -13.26, -11.11, 0.764, 8, -20.82, 0.89, 0.236, 2, 7, 36.33, 2.69, 0.696, 8, 28.76, 14.69, 0.304, 2, 7, 23.65, 5.06, 0.696, 8, 16.09, 17.06, 0.304, 2, 7, 13.76, 6.23, 0.696, 8, 6.19, 18.24, 0.304, 2, 7, 3.02, 4.26, 0.696, 8, -4.55, 16.26, 0.304, 2, 7, -6.48, 0.95, 0.696, 8, -14.05, 12.95, 0.304, 2, 7, 36.78, -22.42, 0.752, 8, 29.22, -10.42, 0.248, 2, 7, 23.6, -22.71, 0.76267, 8, 16.03, -10.7, 0.23733, 2, 7, 14.47, -23.39, 0.78133, 8, 6.91, -11.38, 0.21867, 2, 7, 5.6, -23.17, 0.79733, 8, -1.96, -11.16, 0.20267, 2, 7, -1.67, -21.51, 0.79733, 8, -9.24, -9.51, 0.20267, 2, 7, -6.61, -17.87, 0.792, 8, -14.18, -5.86, 0.208, 6, 7, 108.05, 44.6, 0.27111, 28, 16.15, 14.56, 0.50846, 29, -12.11, 18.96, 0.01247, 34, 12.8, -37.91, 0.06795, 9, 60.32, 58.06, 0.03333, 8, 100.49, 56.6, 0.10667, 5, 7, 94.29, 40.59, 0.50311, 28, 1.85, 13.64, 0.25116, 29, -26.05, 22.29, 0.00194, 34, 0.26, -30.98, 0.03579, 8, 86.73, 52.59, 0.208, 4, 7, 83.69, 31.06, 0.63289, 28, -10.57, 6.66, 0.07307, 34, -13.97, -30.17, 0.01138, 8, 76.12, 43.07, 0.28267, 2, 7, 71.75, 21.62, 0.696, 8, 64.19, 33.63, 0.304, 2, 7, 61.19, 17.22, 0.696, 8, 53.63, 29.22, 0.304, 6, 7, 122.83, 21.21, 0.064, 28, 25.48, -11.49, 0.41244, 29, -10.86, -8.67, 0.0993, 33, 15.54, 8.51, 0.38082, 32, 31.09, 12.8, 0.0101, 9, 75.1, 34.67, 0.03333, 6, 7, 119.32, 25.34, 0.2223, 28, 22.96, -6.69, 0.45016, 29, -11.86, -3.35, 0.02241, 33, 15.56, 13.94, 0.25902, 32, 29.48, 17.98, 0.01411, 8, 111.75, 37.35, 0.032, 6, 7, 110.65, 23.19, 0.45815, 28, 14.03, -6.91, 0.30518, 29, -20.46, -0.93, 0.00072, 33, 7.58, 17.93, 0.13076, 32, 20.66, 19.38, 0.01452, 8, 103.09, 35.19, 0.09067, 5, 7, 102.98, 17.7, 0.63732, 28, 5.34, -10.59, 0.14757, 33, -1.83, 18.76, 0.04287, 32, 11.45, 17.33, 0.02024, 8, 95.41, 29.7, 0.152, 5, 7, 101.56, 10.55, 0.67209, 28, 2.4, -17.25, 0.04591, 33, -7.55, 14.25, 0.05784, 32, 7.35, 11.32, 0.05616, 8, 94, 22.56, 0.168, 5, 7, 110.09, 13.21, 0.46827, 28, 11.31, -16.52, 0.05597, 33, 0.66, 10.72, 0.24978, 32, 16.24, 10.42, 0.13797, 8, 102.53, 25.21, 0.088, 6, 7, 114.94, 8.49, 0.2251, 28, 15.01, -22.18, 0.06117, 29, -24.01, -15.82, 0.00022, 33, 1.27, 3.98, 0.51485, 32, 18.85, 4.18, 0.17199, 8, 107.38, 20.5, 0.02667, 6, 7, 120.51, 9.59, 0.064, 28, 20.69, -22.33, 0.04806, 29, -18.63, -17.63, 0.00051, 33, 6.22, 1.19, 0.73929, 32, 24.41, 3.01, 0.11481, 9, 72.79, 23.05, 0.03333, 5, 28, 28.32, -17.75, 0.03457, 29, -9.99, -15.5, 0.00077, 33, 15.11, 1.64, 0.84181, 32, 32.75, 6.12, 0.07285, 9, 79.24, 29.17, 0.05, 5, 7, 95.29, 0.56, 0.73117, 28, -5.9, -25.64, 0.00934, 33, -18.81, 10.74, 0.00722, 32, -2.33, 4.58, 0.01227, 8, 87.72, 12.57, 0.24, 2, 7, 87.74, -10.38, 0.72, 8, 80.18, 1.63, 0.28, 2, 7, 76.86, -17.48, 0.696, 8, 69.3, -5.47, 0.304, 2, 7, 62.68, -20.08, 0.696, 8, 55.12, -8.08, 0.304, 2, 7, 52.11, -19.06, 0.696, 8, 44.55, -7.05, 0.304, 2, 7, 26.58, 32.38, 0.84267, 8, 19.01, 44.38, 0.15733, 2, 7, 28.57, 42.13, 0.896, 8, 21.01, 54.14, 0.104, 2, 7, 15.5, 45.17, 0.95467, 8, 7.93, 57.17, 0.04533, 1, 7, 8.29, 41.37, 1, 2, 7, 2.12, 24.76, 0.91733, 8, -5.45, 36.76, 0.08267, 2, 7, 1.18, 31.67, 0.97067, 8, -6.39, 43.67, 0.02933, 6, 7, 77.08, 57.44, 0.799, 28, -11.27, 33.84, 0.01404, 34, 0.07, -6.89, 0.05328, 35, -8.02, -18.38, 0.00168, 9, 29.36, 70.9, 0.03333, 8, 69.52, 69.44, 0.09867, 2, 7, 62.75, 49.37, 0.85067, 8, 55.19, 61.38, 0.14933, 2, 7, 48.55, 47.75, 0.84533, 8, 40.99, 59.76, 0.15467, 2, 7, 37.3, 45.36, 0.86, 8, 29.74, 57.36, 0.14, 2, 7, 93.22, 21.2, 0.696, 8, 85.65, 33.21, 0.304, 2, 7, 82.99, 9.61, 0.696, 8, 75.43, 21.62, 0.304, 2, 7, 70.48, -0.26, 0.696, 8, 62.91, 11.74, 0.304, 2, 7, 57.7, -4.26, 0.696, 8, 50.14, 7.75, 0.304, 3, 7, 82.85, -28.32, 0.83867, 9, 35.12, -14.86, 0.03333, 8, 75.28, -16.31, 0.128, 2, 7, 71.67, -32.02, 0.8, 8, 64.1, -20.01, 0.2, 2, 7, 60.6, -32.73, 0.796, 8, 53.03, -20.73, 0.204], "hull": 67, "edges": [0, 132, 0, 2, 2, 4, 28, 30, 36, 38, 42, 44, 48, 50, 54, 56, 62, 64, 64, 66, 66, 68, 68, 70, 80, 82, 86, 88, 88, 90, 102, 104, 104, 106, 106, 108, 124, 126, 126, 128, 122, 124, 120, 122, 20, 22, 22, 24, 8, 10, 10, 12, 24, 26, 26, 28, 100, 102, 98, 100, 94, 96, 96, 98, 82, 84, 84, 86, 76, 78, 78, 80, 74, 76, 70, 72, 72, 74, 60, 62, 56, 58, 58, 60, 44, 46, 46, 48, 50, 52, 52, 54, 38, 40, 40, 42, 34, 36, 30, 32, 32, 34, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 134, 150, 150, 152, 134, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 166, 168, 168, 170, 170, 172, 172, 174, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 112, 188, 188, 190, 190, 192, 192, 194, 194, 196, 20, 198, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 208, 210, 210, 212, 212, 214, 206, 216, 216, 218, 218, 220, 220, 222, 222, 224, 146, 226, 226, 228, 228, 230, 230, 232, 146, 234, 234, 236, 236, 232, 94, 238, 238, 240, 240, 242, 242, 244, 246, 248, 248, 250, 250, 252, 34, 254, 254, 256, 256, 258, 90, 92, 92, 94, 108, 110, 110, 112, 116, 118, 118, 120, 112, 114, 114, 116, 128, 130, 130, 132, 4, 6, 6, 8, 16, 18, 18, 20, 12, 14, 14, 16], "width": 119, "height": 198}}, "a12": {"a12": {"type": "mesh", "uvs": [0.50921, 0, 0.56205, 0.00225, 0.60926, 0.02193, 0.65202, 0.05365, 0.69737, 0.10962, 0.73762, 0.18095, 0.76566, 0.24919, 0.78916, 0.33958, 0.79385, 0.43495, 0.77618, 0.51337, 0.74361, 0.59878, 0.71848, 0.66814, 0.70612, 0.72763, 0.70911, 0.80552, 0.72529, 0.86084, 0.7464, 0.89207, 0.77026, 0.90969, 0.79495, 0.90451, 0.80864, 0.83955, 0.82668, 0.76987, 0.85371, 0.70681, 0.89617, 0.66871, 0.94733, 0.67967, 0.98476, 0.72461, 0.99851, 0.77563, 0.99709, 0.81467, 0.96454, 0.78754, 0.93431, 0.76806, 0.90186, 0.76245, 0.87898, 0.81347, 0.86471, 0.88321, 0.84945, 0.94033, 0.82661, 0.97834, 0.79781, 0.9993, 0.75693, 0.99584, 0.71408, 0.96098, 0.6795, 0.911, 0.65694, 0.83275, 0.6626, 0.72979, 0.68217, 0.63428, 0.70439, 0.55869, 0.72307, 0.49592, 0.73245, 0.41436, 0.72654, 0.33047, 0.70919, 0.2403, 0.67018, 0.1599, 0.63288, 0.10809, 0.58403, 0.07303, 0.53525, 0.05816, 0.48709, 0.05879, 0.43643, 0.07876, 0.39669, 0.12235, 0.36118, 0.16575, 0.3199, 0.19005, 0.27258, 0.19107, 0.2273, 0.17958, 0.17592, 0.16141, 0.12333, 0.16072, 0.08898, 0.18744, 0.07335, 0.24169, 0.07346, 0.30881, 0.08095, 0.34904, 0.08762, 0.39235, 0.07402, 0.38802, 0.05261, 0.35269, 0.02972, 0.30878, 0.00861, 0.25353, 0, 0.19567, 0.00637, 0.12102, 0.02263, 0.06441, 0.04985, 0.02676, 0.08389, 0.01109, 0.13295, 0.02706, 0.17815, 0.05464, 0.22323, 0.08477, 0.26936, 0.1122, 0.30765, 0.11206, 0.33834, 0.09181, 0.37482, 0.05975, 0.41254, 0.02888, 0.4581, 0.00493], "triangles": [65, 66, 59, 65, 59, 60, 64, 65, 60, 64, 60, 61, 63, 64, 61, 63, 61, 62, 57, 71, 72, 57, 72, 73, 56, 57, 73, 56, 73, 74, 56, 74, 55, 58, 71, 57, 70, 71, 58, 69, 70, 58, 68, 69, 58, 67, 68, 58, 59, 67, 58, 66, 67, 59, 50, 79, 80, 50, 80, 49, 51, 78, 79, 51, 79, 50, 51, 52, 77, 51, 77, 78, 55, 74, 75, 52, 53, 76, 52, 76, 77, 54, 75, 76, 54, 76, 53, 55, 75, 54, 44, 45, 5, 48, 0, 1, 49, 80, 0, 49, 0, 48, 47, 1, 2, 48, 1, 47, 46, 2, 3, 47, 2, 46, 46, 3, 4, 45, 46, 4, 45, 4, 5, 44, 5, 6, 43, 44, 6, 43, 6, 7, 42, 43, 7, 42, 7, 8, 9, 41, 42, 8, 9, 42, 10, 41, 9, 40, 41, 10, 39, 40, 10, 11, 39, 10, 12, 39, 11, 38, 39, 12, 38, 12, 13, 37, 38, 13, 14, 36, 37, 14, 37, 13, 17, 18, 30, 31, 17, 30, 35, 36, 14, 35, 14, 15, 32, 17, 31, 34, 15, 16, 35, 15, 34, 33, 17, 32, 16, 17, 33, 34, 16, 33, 28, 21, 22, 20, 21, 28, 27, 28, 22, 29, 19, 20, 28, 29, 20, 18, 19, 29, 30, 18, 29, 27, 22, 23, 26, 27, 23, 24, 26, 23, 25, 26, 24], "vertices": [3, 78, -90.25, 94.35, 0.00302, 77, -34.35, 13.35, 0.67652, 76, 66.02, 31.43, 0.32046, 3, 78, -71.12, 93.85, 0.02005, 77, -15.22, 12.86, 0.80857, 76, 85.15, 30.94, 0.17139, 3, 78, -54.03, 89.5, 0.07204, 77, 1.87, 8.51, 0.85616, 76, 102.24, 26.59, 0.07181, 3, 78, -38.55, 82.49, 0.17845, 77, 17.35, 1.5, 0.80015, 76, 117.72, 19.58, 0.0214, 3, 78, -22.14, 70.12, 0.34093, 77, 33.76, -10.87, 0.65535, 76, 134.13, 7.21, 0.00372, 4, 79, 19.19, 122.74, 0.00018, 78, -7.57, 54.36, 0.53341, 77, 48.33, -26.64, 0.46628, 76, 148.7, -8.56, 0.00012, 3, 79, 29.34, 107.66, 0.00534, 78, 2.59, 39.28, 0.71167, 77, 58.48, -41.72, 0.28299, 3, 79, 37.85, 87.68, 0.03361, 78, 11.09, 19.3, 0.82374, 77, 66.99, -61.69, 0.14265, 3, 79, 39.55, 66.6, 0.11465, 78, 12.79, -1.78, 0.82887, 77, 68.69, -82.77, 0.05647, 4, 80, -1.76, 99.42, 0.00014, 79, 33.15, 49.27, 0.27039, 78, 6.39, -19.11, 0.71344, 77, 62.29, -100.1, 0.01604, 4, 80, -13.55, 80.55, 0.00824, 79, 21.36, 30.4, 0.47566, 78, -5.4, -37.98, 0.51356, 77, 50.5, -118.98, 0.00254, 4, 80, -22.65, 65.22, 0.04366, 79, 12.26, 15.07, 0.65801, 78, -14.49, -53.31, 0.29829, 77, 41.4, -134.31, 4e-05, 4, 81, -58.74, 7.57, 0, 80, -27.12, 52.07, 0.13416, 79, 7.79, 1.92, 0.7318, 78, -18.97, -66.46, 0.13404, 4, 81, -57.66, -9.64, 0.00113, 80, -26.04, 34.86, 0.28965, 79, 8.87, -15.29, 0.66629, 78, -17.88, -83.67, 0.04294, 4, 81, -51.8, -21.87, 0.01084, 80, -20.18, 22.63, 0.48145, 79, 14.73, -27.52, 0.4991, 78, -12.03, -95.9, 0.0086, 4, 81, -44.16, -28.77, 0.05112, 80, -12.54, 15.73, 0.64053, 79, 22.37, -34.42, 0.30775, 78, -4.39, -102.8, 0.0006, 3, 81, -35.52, -32.67, 0.15172, 80, -3.9, 11.83, 0.69421, 79, 31.01, -38.32, 0.15407, 4, 82, -69.38, -26.61, 0.00134, 81, -26.59, -31.52, 0.32591, 80, 5.03, 12.98, 0.61227, 79, 39.95, -37.17, 0.06049, 4, 82, -64.42, -12.26, 0.01308, 81, -21.63, -17.17, 0.53575, 80, 9.99, 27.33, 0.43389, 79, 44.9, -22.81, 0.01728, 4, 82, -57.89, 3.14, 0.05639, 81, -15.1, -1.77, 0.70113, 80, 16.52, 42.73, 0.23955, 79, 51.43, -7.41, 0.00293, 4, 82, -48.11, 17.08, 0.15953, 81, -5.31, 12.17, 0.74216, 80, 26.31, 56.67, 0.09819, 79, 61.22, 6.52, 0.00012, 3, 82, -32.74, 25.5, 0.33232, 81, 10.06, 20.59, 0.64084, 80, 41.68, 65.09, 0.02684, 3, 82, -14.22, 23.08, 0.54831, 81, 28.58, 18.17, 0.44777, 80, 60.2, 62.67, 0.00393, 2, 82, -0.67, 13.14, 0.74823, 81, 42.13, 8.24, 0.25177, 2, 82, 4.31, 1.87, 0.87014, 81, 47.1, -3.04, 0.12986, 2, 82, 3.8, -6.76, 0.87377, 81, 46.59, -11.67, 0.12623, 3, 82, -7.99, -0.76, 0.7542, 81, 34.81, -5.67, 0.24161, 80, 66.43, 38.83, 0.00419, 3, 82, -18.93, 3.54, 0.54761, 81, 23.86, -1.37, 0.42401, 80, 55.48, 43.13, 0.02839, 3, 82, -30.68, 4.78, 0.32185, 81, 12.11, -0.12, 0.5803, 80, 43.73, 44.37, 0.09785, 3, 82, -38.96, -6.49, 0.14473, 81, 3.83, -11.4, 0.62346, 80, 35.45, 33.1, 0.23182, 3, 82, -44.13, -21.9, 0.04504, 81, -1.33, -26.81, 0.53584, 80, 30.29, 17.69, 0.41912, 4, 82, -49.65, -34.53, 0.00787, 81, -6.86, -39.44, 0.37112, 80, 24.76, 5.06, 0.61946, 79, 59.68, -45.09, 0.00155, 4, 82, -57.92, -42.93, 0.00017, 81, -15.13, -47.84, 0.2082, 80, 16.49, -3.34, 0.77791, 79, 51.41, -53.49, 0.01372, 3, 81, -25.55, -52.47, 0.09331, 80, 6.07, -7.97, 0.85176, 79, 40.98, -58.12, 0.05493, 3, 81, -40.35, -51.71, 0.03211, 80, -8.73, -7.21, 0.81876, 79, 26.18, -57.35, 0.14913, 4, 81, -55.86, -44, 0.00756, 80, -24.24, 0.5, 0.68646, 79, 10.67, -49.65, 0.30592, 78, -16.09, -118.03, 7e-05, 4, 81, -68.38, -32.95, 0.00078, 80, -36.76, 11.54, 0.49191, 79, -1.85, -38.6, 0.49944, 78, -28.61, -106.98, 0.00787, 3, 80, -44.93, 28.84, 0.29102, 79, -10.01, -21.31, 0.66591, 78, -36.77, -89.69, 0.04307, 3, 80, -42.88, 51.59, 0.13541, 79, -7.97, 1.44, 0.72892, 78, -34.72, -66.94, 0.13567, 4, 80, -35.79, 72.7, 0.04474, 79, -0.88, 22.55, 0.65575, 78, -27.64, -45.83, 0.29916, 77, 28.26, -126.82, 0.00035, 4, 80, -27.75, 89.4, 0.00884, 79, 7.16, 39.26, 0.47854, 78, -19.6, -29.12, 0.50709, 77, 36.3, -110.12, 0.00554, 4, 80, -20.99, 103.28, 0.00013, 79, 13.93, 53.13, 0.27819, 78, -12.83, -15.25, 0.69264, 77, 43.07, -96.24, 0.02904, 3, 79, 17.32, 71.15, 0.12385, 78, -9.44, 2.77, 0.78064, 77, 46.46, -78.22, 0.09552, 3, 79, 15.18, 89.69, 0.03902, 78, -11.58, 21.31, 0.73564, 77, 44.32, -59.68, 0.22533, 4, 79, 8.9, 109.62, 0.0074, 78, -17.86, 41.24, 0.57747, 77, 38.04, -39.75, 0.41434, 76, 138.41, -21.67, 0.00078, 4, 79, -5.22, 127.39, 0.00019, 78, -31.98, 59.01, 0.37402, 77, 23.92, -21.98, 0.61711, 76, 124.29, -3.9, 0.00868, 3, 78, -45.48, 70.46, 0.19363, 77, 10.42, -10.53, 0.76778, 76, 110.79, 7.55, 0.03859, 3, 78, -63.16, 78.21, 0.0768, 77, -7.27, -2.79, 0.8102, 76, 93.11, 15.29, 0.113, 3, 78, -80.82, 81.49, 0.02096, 77, -24.92, 0.5, 0.73397, 76, 75.45, 18.58, 0.24508, 3, 78, -98.26, 81.36, 0.00322, 77, -42.36, 0.36, 0.57065, 76, 58.01, 18.44, 0.42613, 3, 77, -60.7, -4.05, 0.37891, 76, 39.67, 14.03, 0.62021, 75, 126.9, 6.06, 0.00088, 3, 77, -75.08, -13.69, 0.21057, 76, 25.29, 4.39, 0.78006, 75, 112.52, -3.58, 0.00937, 3, 77, -87.94, -23.28, 0.09512, 76, 12.43, -5.2, 0.86375, 75, 99.66, -13.17, 0.04112, 3, 77, -102.88, -28.65, 0.03298, 76, -2.51, -10.57, 0.84716, 75, 84.72, -18.54, 0.11986, 4, 77, -120.01, -28.87, 0.00796, 76, -19.64, -10.79, 0.73215, 75, 67.59, -18.76, 0.25943, 74, 71.27, 39.34, 0.00045, 4, 77, -136.4, -26.33, 0.00104, 76, -36.03, -8.25, 0.54776, 75, 51.2, -16.22, 0.4439, 74, 54.87, 41.88, 0.0073, 3, 76, -54.63, -4.24, 0.34562, 75, 32.6, -12.21, 0.61667, 74, 36.28, 45.89, 0.03771, 3, 76, -73.67, -4.09, 0.17685, 75, 13.56, -12.06, 0.70233, 74, 17.24, 46.04, 0.12082, 3, 76, -86.11, -9.99, 0.06932, 75, 1.12, -17.96, 0.65571, 74, 4.8, 40.14, 0.27497, 3, 76, -91.76, -21.98, 0.01889, 75, -4.54, -29.95, 0.49541, 74, -0.86, 28.15, 0.4857, 3, 76, -91.72, -36.81, 0.00285, 75, -4.49, -44.78, 0.29727, 74, -0.82, 13.32, 0.69989, 3, 76, -89.01, -45.7, 0.00011, 75, -1.78, -53.67, 0.13866, 74, 1.9, 4.43, 0.86123, 2, 75, 0.63, -63.25, 0.06332, 74, 4.31, -5.15, 0.93668, 2, 75, -4.29, -62.29, 0.07562, 74, -0.61, -4.19, 0.92438, 2, 75, -12.04, -54.48, 0.16425, 74, -8.36, 3.62, 0.83575, 2, 75, -20.33, -44.78, 0.30978, 74, -16.65, 13.32, 0.69022, 2, 75, -27.97, -32.57, 0.48448, 74, -24.29, 25.54, 0.51552, 2, 75, -31.09, -19.78, 0.65694, 74, -27.41, 38.32, 0.34306, 3, 76, -116.01, 4.69, 0.00028, 75, -28.78, -3.28, 0.79984, 74, -25.1, 54.82, 0.19988, 3, 76, -110.12, 17.2, 0.0036, 75, -22.89, 9.23, 0.89796, 74, -19.22, 67.33, 0.09844, 3, 76, -100.27, 25.52, 0.01843, 75, -13.04, 17.55, 0.94219, 74, -9.36, 75.65, 0.03938, 3, 76, -87.95, 28.98, 0.06133, 75, -0.72, 21.01, 0.92692, 74, 2.96, 79.11, 0.01175, 3, 76, -70.19, 25.45, 0.1525, 75, 17.04, 17.48, 0.84508, 74, 20.72, 75.58, 0.00242, 3, 76, -53.82, 19.36, 0.30198, 75, 33.4, 11.39, 0.69777, 74, 37.08, 69.49, 0.00024, 3, 77, -137.88, -5.38, 0.00111, 76, -37.51, 12.7, 0.49433, 75, 49.72, 4.73, 0.50456, 3, 77, -121.18, -11.44, 0.00825, 76, -20.81, 6.64, 0.68462, 75, 66.42, -1.33, 0.30713, 3, 77, -107.32, -11.41, 0.03195, 76, -6.95, 6.67, 0.81828, 75, 80.28, -1.3, 0.14978, 3, 77, -96.21, -6.94, 0.08689, 76, 4.16, 11.14, 0.85849, 75, 91.39, 3.17, 0.05463, 3, 77, -83, 0.15, 0.18476, 76, 17.37, 18.23, 0.80204, 75, 104.6, 10.26, 0.0132, 3, 77, -69.35, 6.97, 0.32765, 76, 31.02, 25.05, 0.67082, 75, 118.25, 17.08, 0.00153, 4, 78, -108.75, 93.26, 0, 77, -52.85, 12.27, 0.50174, 76, 47.52, 30.35, 0.49825, 75, 134.75, 22.37, 0], "hull": 81, "edges": [14, 16, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 104, 106, 124, 126, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 150, 152, 156, 158, 158, 160, 122, 124, 114, 116, 116, 118, 112, 114, 144, 146, 146, 148, 148, 150, 110, 112, 106, 108, 108, 110, 152, 154, 154, 156, 96, 98, 98, 100, 2, 0, 0, 160, 2, 4, 4, 6, 92, 94, 94, 96, 6, 8, 8, 10, 88, 90, 90, 92, 10, 12, 12, 14, 84, 86, 86, 88, 78, 80, 16, 18, 22, 24, 24, 26, 26, 28, 32, 34, 34, 36, 36, 38, 38, 40, 56, 58, 58, 60, 60, 62, 52, 54, 54, 56, 126, 128, 128, 130, 28, 30, 30, 32, 18, 20, 20, 22, 100, 102, 102, 104, 118, 120, 120, 122, 80, 82, 82, 84], "width": 362, "height": 221}}, "hou/huoyan_0001": {"hou/huoyan_0001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [31.06, -12.6, -32.94, -12.6, -32.94, 69.4, 31.06, 69.4], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 82}, "hou/huoyan_0003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [31.06, -12.6, -32.94, -12.6, -32.94, 69.4, 31.06, 69.4], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 82}, "hou/huoyan_0005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [31.06, -12.6, -32.94, -12.6, -32.94, 69.4, 31.06, 69.4], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 82}, "hou/huoyan_0007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [31.06, -12.6, -32.94, -12.6, -32.94, 69.4, 31.06, 69.4], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 82}, "hou/huoyan_0009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [31.06, -12.6, -32.94, -12.6, -32.94, 69.4, 31.06, 69.4], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 82}, "hou/huoyan_0011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [31.06, -12.6, -32.94, -12.6, -32.94, 69.4, 31.06, 69.4], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 82}, "hou/huoyan_0013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [31.06, -12.6, -32.94, -12.6, -32.94, 69.4, 31.06, 69.4], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 82}, "hou/huoyan_0015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [31.06, -12.6, -32.94, -12.6, -32.94, 69.4, 31.06, 69.4], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 82}, "hou/huoyan_0017": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [31.06, -12.6, -32.94, -12.6, -32.94, 69.4, 31.06, 69.4], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 82}, "hou/huoyan_0019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [31.06, -12.6, -32.94, -12.6, -32.94, 69.4, 31.06, 69.4], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 82}, "hou/huoyan_0021": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [31.06, -12.6, -32.94, -12.6, -32.94, 69.4, 31.06, 69.4], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 82}}, "a39": {"a12": {"type": "mesh", "uvs": [0.50921, 0, 0.56205, 0.00225, 0.60926, 0.02193, 0.65202, 0.05365, 0.69737, 0.10962, 0.73762, 0.18095, 0.76566, 0.24919, 0.78916, 0.33958, 0.79385, 0.43495, 0.77618, 0.51337, 0.74361, 0.59878, 0.71848, 0.66814, 0.70612, 0.72763, 0.70911, 0.80552, 0.72529, 0.86084, 0.7464, 0.89207, 0.77026, 0.90969, 0.79495, 0.90451, 0.80864, 0.83955, 0.82668, 0.76987, 0.85371, 0.70681, 0.89617, 0.66871, 0.94733, 0.67967, 0.98476, 0.72461, 0.99851, 0.77563, 0.99709, 0.81467, 0.96454, 0.78754, 0.93431, 0.76806, 0.90186, 0.76245, 0.87898, 0.81347, 0.86471, 0.88321, 0.84945, 0.94033, 0.82661, 0.97834, 0.79781, 0.9993, 0.75693, 0.99584, 0.71408, 0.96098, 0.6795, 0.911, 0.65694, 0.83275, 0.6626, 0.72979, 0.68217, 0.63428, 0.70439, 0.55869, 0.72307, 0.49592, 0.73245, 0.41436, 0.72654, 0.33047, 0.70919, 0.2403, 0.67018, 0.1599, 0.63288, 0.10809, 0.58403, 0.07303, 0.53525, 0.05816, 0.48709, 0.05879, 0.43643, 0.07876, 0.39669, 0.12235, 0.36118, 0.16575, 0.3199, 0.19005, 0.27258, 0.19107, 0.2273, 0.17958, 0.17592, 0.16141, 0.12333, 0.16072, 0.08898, 0.18744, 0.07335, 0.24169, 0.07346, 0.30881, 0.08095, 0.34904, 0.08762, 0.39235, 0.07402, 0.38802, 0.05261, 0.35269, 0.02972, 0.30878, 0.00861, 0.25353, 0, 0.19567, 0.00637, 0.12102, 0.02263, 0.06441, 0.04985, 0.02676, 0.08389, 0.01109, 0.13295, 0.02706, 0.17815, 0.05464, 0.22323, 0.08477, 0.26936, 0.1122, 0.30765, 0.11206, 0.33834, 0.09181, 0.37482, 0.05975, 0.41254, 0.02888, 0.4581, 0.00493], "triangles": [65, 66, 59, 65, 59, 60, 64, 65, 60, 64, 60, 61, 63, 64, 61, 63, 61, 62, 57, 71, 72, 57, 72, 73, 56, 57, 73, 56, 73, 74, 56, 74, 55, 58, 71, 57, 70, 71, 58, 69, 70, 58, 68, 69, 58, 67, 68, 58, 59, 67, 58, 66, 67, 59, 50, 79, 80, 50, 80, 49, 51, 78, 79, 51, 79, 50, 51, 52, 77, 51, 77, 78, 55, 74, 75, 52, 53, 76, 52, 76, 77, 54, 75, 76, 54, 76, 53, 55, 75, 54, 44, 45, 5, 48, 0, 1, 49, 80, 0, 49, 0, 48, 47, 1, 2, 48, 1, 47, 46, 2, 3, 47, 2, 46, 46, 3, 4, 45, 46, 4, 45, 4, 5, 44, 5, 6, 43, 44, 6, 43, 6, 7, 42, 43, 7, 42, 7, 8, 9, 41, 42, 8, 9, 42, 10, 41, 9, 40, 41, 10, 39, 40, 10, 11, 39, 10, 12, 39, 11, 38, 39, 12, 38, 12, 13, 37, 38, 13, 14, 36, 37, 14, 37, 13, 17, 18, 30, 31, 17, 30, 35, 36, 14, 35, 14, 15, 32, 17, 31, 34, 15, 16, 35, 15, 34, 33, 17, 32, 16, 17, 33, 34, 16, 33, 28, 21, 22, 20, 21, 28, 27, 28, 22, 29, 19, 20, 28, 29, 20, 18, 19, 29, 30, 18, 29, 27, 22, 23, 26, 27, 23, 24, 26, 23, 25, 26, 24], "vertices": [3, 105, -90.25, 94.35, 0.00302, 104, -34.35, 13.35, 0.67652, 103, 66.02, 31.43, 0.32046, 3, 105, -71.12, 93.85, 0.02005, 104, -15.22, 12.86, 0.80857, 103, 85.15, 30.94, 0.17139, 3, 105, -54.03, 89.5, 0.07204, 104, 1.87, 8.51, 0.85616, 103, 102.24, 26.59, 0.07181, 3, 105, -38.55, 82.49, 0.17845, 104, 17.35, 1.5, 0.80015, 103, 117.72, 19.58, 0.0214, 3, 105, -22.14, 70.12, 0.34093, 104, 33.76, -10.87, 0.65535, 103, 134.13, 7.21, 0.00372, 4, 106, 19.19, 122.74, 0.00018, 105, -7.57, 54.36, 0.53341, 104, 48.33, -26.64, 0.46628, 103, 148.7, -8.56, 0.00012, 3, 106, 29.34, 107.66, 0.00534, 105, 2.59, 39.28, 0.71167, 104, 58.48, -41.72, 0.28299, 3, 106, 37.85, 87.68, 0.03361, 105, 11.09, 19.3, 0.82374, 104, 66.99, -61.69, 0.14265, 3, 106, 39.55, 66.6, 0.11465, 105, 12.79, -1.78, 0.82887, 104, 68.69, -82.77, 0.05647, 4, 107, -1.76, 99.42, 0.00014, 106, 33.15, 49.27, 0.27039, 105, 6.39, -19.11, 0.71344, 104, 62.29, -100.1, 0.01604, 4, 107, -13.55, 80.55, 0.00824, 106, 21.36, 30.4, 0.47566, 105, -5.4, -37.98, 0.51356, 104, 50.5, -118.98, 0.00254, 4, 107, -22.65, 65.22, 0.04366, 106, 12.26, 15.07, 0.65801, 105, -14.49, -53.31, 0.29829, 104, 41.4, -134.31, 4e-05, 4, 108, -58.74, 7.57, 0, 107, -27.12, 52.07, 0.13416, 106, 7.79, 1.92, 0.7318, 105, -18.97, -66.46, 0.13404, 4, 108, -57.66, -9.64, 0.00113, 107, -26.04, 34.86, 0.28965, 106, 8.87, -15.29, 0.66629, 105, -17.88, -83.67, 0.04294, 4, 108, -51.8, -21.87, 0.01084, 107, -20.18, 22.63, 0.48145, 106, 14.73, -27.52, 0.4991, 105, -12.03, -95.9, 0.0086, 4, 108, -44.16, -28.77, 0.05112, 107, -12.54, 15.73, 0.64053, 106, 22.37, -34.42, 0.30775, 105, -4.39, -102.8, 0.0006, 3, 108, -35.52, -32.67, 0.15172, 107, -3.9, 11.83, 0.69421, 106, 31.01, -38.32, 0.15407, 4, 109, -69.38, -26.61, 0.00134, 108, -26.59, -31.52, 0.32591, 107, 5.03, 12.98, 0.61227, 106, 39.95, -37.17, 0.06049, 4, 109, -64.42, -12.26, 0.01308, 108, -21.63, -17.17, 0.53575, 107, 9.99, 27.33, 0.43389, 106, 44.9, -22.81, 0.01728, 4, 109, -57.89, 3.14, 0.05639, 108, -15.1, -1.77, 0.70113, 107, 16.52, 42.73, 0.23955, 106, 51.43, -7.41, 0.00293, 4, 109, -48.11, 17.08, 0.15953, 108, -5.31, 12.17, 0.74216, 107, 26.31, 56.67, 0.09819, 106, 61.22, 6.52, 0.00012, 3, 109, -32.74, 25.5, 0.33232, 108, 10.06, 20.59, 0.64084, 107, 41.68, 65.09, 0.02684, 3, 109, -14.22, 23.08, 0.54831, 108, 28.58, 18.17, 0.44777, 107, 60.2, 62.67, 0.00393, 2, 109, -0.67, 13.14, 0.74823, 108, 42.13, 8.24, 0.25177, 2, 109, 4.31, 1.87, 0.87014, 108, 47.1, -3.04, 0.12986, 2, 109, 3.8, -6.76, 0.87377, 108, 46.59, -11.67, 0.12623, 3, 109, -7.99, -0.76, 0.7542, 108, 34.81, -5.67, 0.24161, 107, 66.43, 38.83, 0.00419, 3, 109, -18.93, 3.54, 0.54761, 108, 23.86, -1.37, 0.42401, 107, 55.48, 43.13, 0.02839, 3, 109, -30.68, 4.78, 0.32185, 108, 12.11, -0.12, 0.5803, 107, 43.73, 44.37, 0.09785, 3, 109, -38.96, -6.49, 0.14473, 108, 3.83, -11.4, 0.62346, 107, 35.45, 33.1, 0.23182, 3, 109, -44.13, -21.9, 0.04504, 108, -1.33, -26.81, 0.53584, 107, 30.29, 17.69, 0.41912, 4, 109, -49.65, -34.53, 0.00787, 108, -6.86, -39.44, 0.37112, 107, 24.76, 5.06, 0.61946, 106, 59.68, -45.09, 0.00155, 4, 109, -57.92, -42.93, 0.00017, 108, -15.13, -47.84, 0.2082, 107, 16.49, -3.34, 0.77791, 106, 51.41, -53.49, 0.01372, 3, 108, -25.55, -52.47, 0.09331, 107, 6.07, -7.97, 0.85176, 106, 40.98, -58.12, 0.05493, 3, 108, -40.35, -51.71, 0.03211, 107, -8.73, -7.21, 0.81876, 106, 26.18, -57.35, 0.14913, 4, 108, -55.86, -44, 0.00756, 107, -24.24, 0.5, 0.68646, 106, 10.67, -49.65, 0.30592, 105, -16.09, -118.03, 7e-05, 4, 108, -68.38, -32.95, 0.00078, 107, -36.76, 11.54, 0.49191, 106, -1.85, -38.6, 0.49944, 105, -28.61, -106.98, 0.00787, 3, 107, -44.93, 28.84, 0.29102, 106, -10.01, -21.31, 0.66591, 105, -36.77, -89.69, 0.04307, 3, 107, -42.88, 51.59, 0.13541, 106, -7.97, 1.44, 0.72892, 105, -34.72, -66.94, 0.13567, 4, 107, -35.79, 72.7, 0.04474, 106, -0.88, 22.55, 0.65575, 105, -27.64, -45.83, 0.29916, 104, 28.26, -126.82, 0.00035, 4, 107, -27.75, 89.4, 0.00884, 106, 7.16, 39.26, 0.47854, 105, -19.6, -29.12, 0.50709, 104, 36.3, -110.12, 0.00554, 4, 107, -20.99, 103.28, 0.00013, 106, 13.93, 53.13, 0.27819, 105, -12.83, -15.25, 0.69264, 104, 43.07, -96.24, 0.02904, 3, 106, 17.32, 71.15, 0.12385, 105, -9.44, 2.77, 0.78064, 104, 46.46, -78.22, 0.09552, 3, 106, 15.18, 89.69, 0.03902, 105, -11.58, 21.31, 0.73564, 104, 44.32, -59.68, 0.22533, 4, 106, 8.9, 109.62, 0.0074, 105, -17.86, 41.24, 0.57747, 104, 38.04, -39.75, 0.41434, 103, 138.41, -21.67, 0.00078, 4, 106, -5.22, 127.39, 0.00019, 105, -31.98, 59.01, 0.37402, 104, 23.92, -21.98, 0.61711, 103, 124.29, -3.9, 0.00868, 3, 105, -45.48, 70.46, 0.19363, 104, 10.42, -10.53, 0.76778, 103, 110.79, 7.55, 0.03859, 3, 105, -63.16, 78.21, 0.0768, 104, -7.27, -2.79, 0.8102, 103, 93.11, 15.29, 0.113, 3, 105, -80.82, 81.49, 0.02096, 104, -24.92, 0.5, 0.73397, 103, 75.45, 18.58, 0.24508, 3, 105, -98.26, 81.36, 0.00322, 104, -42.36, 0.36, 0.57065, 103, 58.01, 18.44, 0.42613, 3, 104, -60.7, -4.05, 0.37891, 103, 39.67, 14.03, 0.62021, 102, 126.9, 6.06, 0.00088, 3, 104, -75.08, -13.69, 0.21057, 103, 25.29, 4.39, 0.78006, 102, 112.52, -3.58, 0.00937, 3, 104, -87.94, -23.28, 0.09512, 103, 12.43, -5.2, 0.86375, 102, 99.66, -13.17, 0.04112, 3, 104, -102.88, -28.65, 0.03298, 103, -2.51, -10.57, 0.84716, 102, 84.72, -18.54, 0.11986, 4, 104, -120.01, -28.87, 0.00796, 103, -19.64, -10.79, 0.73215, 102, 67.59, -18.76, 0.25943, 101, 71.27, 39.34, 0.00045, 4, 104, -136.4, -26.33, 0.00104, 103, -36.03, -8.25, 0.54776, 102, 51.2, -16.22, 0.4439, 101, 54.87, 41.88, 0.0073, 3, 103, -54.63, -4.24, 0.34562, 102, 32.6, -12.21, 0.61667, 101, 36.28, 45.89, 0.03771, 3, 103, -73.67, -4.09, 0.17685, 102, 13.56, -12.06, 0.70233, 101, 17.24, 46.04, 0.12082, 3, 103, -86.11, -9.99, 0.06932, 102, 1.12, -17.96, 0.65571, 101, 4.8, 40.14, 0.27497, 3, 103, -91.76, -21.98, 0.01889, 102, -4.54, -29.95, 0.49541, 101, -0.86, 28.15, 0.4857, 3, 103, -91.72, -36.81, 0.00285, 102, -4.49, -44.78, 0.29727, 101, -0.82, 13.32, 0.69989, 3, 103, -89.01, -45.7, 0.00011, 102, -1.78, -53.67, 0.13866, 101, 1.9, 4.43, 0.86123, 2, 102, 0.63, -63.25, 0.06332, 101, 4.31, -5.15, 0.93668, 2, 102, -4.29, -62.29, 0.07562, 101, -0.61, -4.19, 0.92438, 2, 102, -12.04, -54.48, 0.16425, 101, -8.36, 3.62, 0.83575, 2, 102, -20.33, -44.78, 0.30978, 101, -16.65, 13.32, 0.69022, 2, 102, -27.97, -32.57, 0.48448, 101, -24.29, 25.54, 0.51552, 2, 102, -31.09, -19.78, 0.65694, 101, -27.41, 38.32, 0.34306, 3, 103, -116.01, 4.69, 0.00028, 102, -28.78, -3.28, 0.79984, 101, -25.1, 54.82, 0.19988, 3, 103, -110.12, 17.2, 0.0036, 102, -22.89, 9.23, 0.89796, 101, -19.22, 67.33, 0.09844, 3, 103, -100.27, 25.52, 0.01843, 102, -13.04, 17.55, 0.94219, 101, -9.36, 75.65, 0.03938, 3, 103, -87.95, 28.98, 0.06133, 102, -0.72, 21.01, 0.92692, 101, 2.96, 79.11, 0.01175, 3, 103, -70.19, 25.45, 0.1525, 102, 17.04, 17.48, 0.84508, 101, 20.72, 75.58, 0.00242, 3, 103, -53.82, 19.36, 0.30198, 102, 33.4, 11.39, 0.69777, 101, 37.08, 69.49, 0.00024, 3, 104, -137.88, -5.38, 0.00111, 103, -37.51, 12.7, 0.49433, 102, 49.72, 4.73, 0.50456, 3, 104, -121.18, -11.44, 0.00825, 103, -20.81, 6.64, 0.68462, 102, 66.42, -1.33, 0.30713, 3, 104, -107.32, -11.41, 0.03195, 103, -6.95, 6.67, 0.81828, 102, 80.28, -1.3, 0.14978, 3, 104, -96.21, -6.94, 0.08689, 103, 4.16, 11.14, 0.85849, 102, 91.39, 3.17, 0.05463, 3, 104, -83, 0.15, 0.18476, 103, 17.37, 18.23, 0.80204, 102, 104.6, 10.26, 0.0132, 3, 104, -69.35, 6.97, 0.32765, 103, 31.02, 25.05, 0.67082, 102, 118.25, 17.08, 0.00153, 4, 105, -108.75, 93.26, 0, 104, -52.85, 12.27, 0.50174, 103, 47.52, 30.35, 0.49825, 102, 134.75, 22.37, 0], "hull": 81, "edges": [14, 16, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 104, 106, 124, 126, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 150, 152, 156, 158, 158, 160, 122, 124, 114, 116, 116, 118, 112, 114, 144, 146, 146, 148, 148, 150, 110, 112, 106, 108, 108, 110, 152, 154, 154, 156, 96, 98, 98, 100, 2, 0, 0, 160, 2, 4, 4, 6, 92, 94, 94, 96, 6, 8, 8, 10, 88, 90, 90, 92, 10, 12, 12, 14, 84, 86, 86, 88, 78, 80, 16, 18, 22, 24, 24, 26, 26, 28, 32, 34, 34, 36, 36, 38, 38, 40, 56, 58, 58, 60, 60, 62, 52, 54, 54, 56, 126, 128, 128, 130, 28, 30, 30, 32, 18, 20, 20, 22, 100, 102, 102, 104, 118, 120, 120, 122, 80, 82, 82, 84], "width": 362, "height": 221}}, "a5": {"a0": {"type": "mesh", "uvs": [0.50162, 0.00811, 0.61341, 0.04397, 0.7046, 0.10273, 0.79789, 0.18318, 0.86489, 0.26916, 0.93193, 0.37447, 0.97306, 0.47851, 0.99717, 0.57747, 0.98486, 0.67752, 0.94614, 0.76155, 0.88863, 0.83759, 0.82509, 0.89577, 0.74287, 0.95231, 0.67444, 0.99568, 0.64997, 0.97857, 0.69577, 0.90428, 0.7322, 0.83001, 0.76172, 0.73089, 0.76384, 0.63129, 0.74793, 0.53384, 0.70437, 0.43332, 0.63483, 0.34635, 0.54349, 0.2734, 0.42413, 0.2276, 0.32591, 0.21794, 0.34742, 0.25428, 0.2926, 0.28359, 0.22207, 0.27829, 0.15108, 0.25496, 0.09846, 0.21228, 0.04625, 0.16291, 0.01163, 0.11695, 0, 0.06642, 0.00775, 0.04086, 0.04554, 0.08518, 0.09723, 0.13146, 0.16934, 0.16107, 0.22352, 0.15556, 0.22918, 0.09902, 0.2977, 0.03538, 0.39398, 0.00181], "triangles": [12, 13, 15, 13, 14, 15, 12, 15, 11, 37, 28, 36, 28, 29, 36, 30, 35, 29, 29, 35, 36, 31, 34, 30, 30, 34, 35, 31, 32, 34, 32, 33, 34, 21, 3, 4, 3, 21, 2, 25, 26, 24, 27, 28, 37, 26, 27, 24, 27, 37, 24, 21, 22, 2, 22, 1, 2, 22, 23, 1, 24, 40, 23, 23, 0, 1, 23, 40, 0, 37, 38, 24, 38, 39, 24, 24, 39, 40, 15, 16, 11, 11, 16, 10, 16, 17, 10, 10, 17, 9, 8, 9, 18, 9, 17, 18, 8, 18, 7, 18, 19, 7, 19, 6, 7, 19, 5, 6, 19, 20, 5, 20, 4, 5, 20, 21, 4], "vertices": [2, 99, -48.61, 82.22, 0.04426, 100, 3.44, 19.29, 0.95574, 2, 99, -33.52, 77.09, 0.11344, 100, 18.54, 14.17, 0.88656, 2, 99, -21.21, 68.69, 0.23108, 100, 30.85, 5.76, 0.76892, 2, 99, -8.61, 57.19, 0.39143, 100, 43.44, -5.74, 0.60857, 2, 99, 0.43, 44.89, 0.57291, 100, 52.49, -18.04, 0.42709, 2, 99, 9.48, 29.83, 0.74188, 100, 61.54, -33.1, 0.25812, 2, 99, 15.03, 14.95, 0.87125, 100, 67.09, -47.97, 0.12875, 2, 99, 18.29, 0.8, 0.94958, 100, 70.34, -62.12, 0.05042, 2, 99, 16.63, -13.51, 0.98593, 100, 68.68, -76.43, 0.01407, 3, 99, 11.4, -25.52, 0.96056, 100, 63.45, -88.45, 0.0024, 98, 54.05, 32.25, 0.03704, 2, 99, 3.64, -36.4, 0.85183, 98, 46.28, 21.38, 0.14817, 2, 99, -4.94, -44.71, 0.62963, 98, 37.71, 13.06, 0.37037, 2, 99, -16.04, -52.8, 0.37037, 98, 26.61, 4.97, 0.62963, 2, 99, -25.28, -59, 0.18519, 98, 17.37, -1.23, 0.81481, 2, 99, -28.58, -56.56, 0.18519, 98, 14.07, 1.22, 0.81481, 2, 99, -22.4, -45.93, 0.37031, 98, 20.25, 11.84, 0.62969, 2, 99, -17.48, -35.31, 0.62848, 98, 25.17, 22.46, 0.37152, 3, 99, -13.5, -21.14, 0.83172, 100, 38.56, -84.06, 0.02013, 98, 29.15, 36.64, 0.14815, 3, 99, -13.21, -6.89, 0.89012, 100, 38.84, -69.82, 0.07284, 98, 29.44, 50.88, 0.03704, 2, 99, -15.36, 7.04, 0.81521, 100, 36.7, -55.89, 0.18479, 2, 99, -21.24, 21.41, 0.64056, 100, 30.82, -41.51, 0.35944, 2, 99, -30.63, 33.85, 0.43174, 100, 21.43, -29.07, 0.56826, 2, 99, -42.96, 44.28, 0.23919, 100, 9.1, -18.64, 0.76081, 2, 99, -59.07, 50.83, 0.10301, 100, -7.02, -12.09, 0.89699, 2, 99, -72.33, 52.21, 0.0315, 100, -20.28, -10.71, 0.9685, 3, 99, -69.43, 47.02, 0.00567, 100, -17.37, -15.91, 0.95729, 101, 47.49, -25.8, 0.03704, 3, 99, -76.83, 42.83, 0.0002, 100, -24.77, -20.1, 0.85166, 101, 40.09, -29.99, 0.14815, 2, 100, -34.3, -19.34, 0.62963, 101, 30.57, -29.23, 0.37037, 2, 100, -43.88, -16.01, 0.37037, 101, 20.99, -25.89, 0.62963, 2, 100, -50.98, -9.9, 0.14815, 101, 13.88, -19.79, 0.85185, 2, 100, -58.03, -2.84, 0.03704, 101, 6.84, -12.73, 0.96296, 1, 101, 2.16, -6.16, 1, 1, 101, 0.59, 1.07, 1, 1, 101, 1.64, 4.72, 1, 2, 100, -58.13, 8.27, 0.03704, 101, 6.74, -1.62, 0.96296, 2, 100, -51.15, 1.65, 0.14815, 101, 13.72, -8.23, 0.85185, 2, 100, -41.41, -2.58, 0.37037, 101, 23.45, -12.47, 0.62963, 2, 100, -34.1, -1.79, 0.62963, 101, 30.77, -11.68, 0.37037, 3, 99, -85.39, 69.22, 9e-05, 100, -33.33, 6.29, 0.85176, 101, 31.53, -3.6, 0.14815, 3, 99, -76.14, 78.32, 0.00215, 100, -24.08, 15.39, 0.96081, 101, 40.78, 5.5, 0.03704, 2, 99, -63.14, 83.12, 0.01241, 100, -11.09, 20.19, 0.98759], "hull": 41, "edges": [0, 80, 12, 14, 14, 16, 20, 22, 26, 28, 42, 44, 48, 50, 62, 64, 64, 66, 66, 68, 74, 76, 76, 78, 78, 80, 0, 2, 2, 4, 8, 10, 10, 12, 38, 40, 40, 42, 34, 36, 36, 38, 32, 34, 28, 30, 30, 32, 22, 24, 24, 26, 50, 52, 60, 62, 56, 58, 58, 60, 44, 46, 46, 48, 68, 70, 70, 72, 72, 74, 52, 54, 54, 56, 4, 6, 6, 8, 16, 18, 18, 20], "width": 135, "height": 143}}, "a30": {"a21": {"type": "mesh", "uvs": [0.84225, 0.6765, 0.84939, 0.37249, 0.9096, 0.46354, 0.95703, 0.53989, 0.99555, 0.60209, 0.99999, 0.68788, 0.95748, 0.77646, 0.89814, 0.85177, 0.8566, 0.88101], "triangles": [0, 6, 7, 0, 1, 2, 8, 0, 7, 6, 0, 3, 6, 3, 4, 6, 4, 5, 3, 0, 2], "vertices": [2, 44, 23.93, -11.87, 0.984, 12, 25.39, 39.35, 0.016, 2, 45, -8.72, 2.42, 0.33333, 44, 14.94, 1.53, 0.66667, 2, 45, 0.98, 2.74, 0.66667, 44, 24.57, 2.82, 0.33333, 2, 45, 8.76, 2.78, 0.88889, 44, 32.3, 3.64, 0.11111, 1, 45, 15.08, 2.8, 1, 2, 45, 18, -0.74, 0.88889, 44, 41.84, 1.06, 0.11111, 3, 45, 15.39, -7.86, 0.432, 44, 39.97, -6.28, 0.216, 12, 31.14, 55.33, 0.352, 1, 44, 35.79, -14.5, 1, 1, 44, 32.11, -19.26, 1], "hull": 9, "edges": [8, 10, 2, 4, 10, 12, 12, 14, 4, 6, 6, 8, 2, 0, 14, 16, 0, 16], "width": 140, "height": 53}}}}], "events": {"atk": {}, "audio": {}}, "animations": {"nezha_appear": {"slots": {"xlt_add/lizi_1": {"color": [{"color": "ffffffff"}], "attachment": [{"time": 0.0333, "name": "xlt_add/lizi_01"}]}, "a2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff"}]}, "huoyan_add/huoyan_65": {"attachment": [{"time": 0.8667, "name": "huoyan_add/huoyan_00058"}, {"time": 0.9333, "name": "huoyan_add/huoyan_00060"}]}, "huoyan_add/huoyan_63": {"attachment": [{"time": 0.4333, "name": "huoyan_add/huoyan_00066"}, {"time": 0.5, "name": "huoyan_add/huoyan_00068"}, {"time": 0.5667, "name": "huoyan_add/huoyan_00070"}, {"time": 0.6333, "name": "huoyan_add/huoyan_00072"}, {"time": 0.6667, "name": null}, {"time": 0.9333, "name": "huoyan_add/huoyan_00066"}]}, "a11": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff"}]}, "a21": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff"}]}, "a19": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff"}]}, "a23": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff"}]}, "huoyan_add/huoyan_59": {"attachment": [{"time": 0.4333, "name": "huoyan_add/huoyan_00058"}, {"time": 0.5, "name": "huoyan_add/huoyan_00060"}, {"time": 0.5667, "name": "huoyan_add/huoyan_00062"}, {"time": 0.6333, "name": "huoyan_add/huoyan_00064"}, {"time": 0.7, "name": "huoyan_add/huoyan_00066"}, {"time": 0.7667, "name": "huoyan_add/huoyan_00068"}, {"time": 0.8333, "name": "huoyan_add/huoyan_00070"}, {"time": 0.9, "name": "huoyan_add/huoyan_00072"}, {"time": 0.9333, "name": "huoyan_add/huoyan_00058"}]}, "huoyankuosna-add/huokuo_1": {"color": [{"time": 0.4333, "color": "ffffffff"}], "attachment": [{"time": 0.6, "name": "huoyankuosna-add/huokuo_0003"}, {"time": 0.6333, "name": "huoyankuosna-add/huokuo_0005"}, {"time": 0.6667, "name": "huoyankuosna-add/huokuo_0009"}, {"time": 0.7, "name": "huoyankuosna-add/huokuo_0011"}, {"time": 0.7333, "name": "huoyankuosna-add/huokuo_0013"}, {"time": 0.7667, "name": "huoyankuosna-add/huokuo_0015"}, {"time": 0.8, "name": "huoyankuosna-add/huokuo_0017"}, {"time": 0.8333, "name": "huoyankuosna-add/huokuo_0019"}, {"time": 0.8667, "name": "huoyankuosna-add/huokuo_0021"}, {"time": 0.9, "name": null}]}, "heilizi/lizi2": {"color": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.2667, "color": "ffffffff"}, {"time": 0.5, "color": "ffffff35"}], "attachment": [{"time": 0.2667, "name": "heilizi/lizi"}]}, "a26": {"color": [{"time": 0.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "color": "ffffffff"}]}, "huoxing-add/huoxing_0001": {"color": [{"time": 0.4333, "color": "ffffffff"}], "attachment": [{"time": 0.8, "name": "huoxing-add/huoxing_0001"}, {"time": 0.8333, "name": "huoxing-add/huoxing_0003"}, {"time": 0.8667, "name": "huoxing-add/huoxing_0005"}, {"time": 0.9, "name": "huoxing-add/huoxing_0007"}]}, "huoyan_add/huoyan_66": {"attachment": [{"time": 0.6333, "name": "huoyan_add/huoyan_00058"}, {"time": 0.7, "name": "huoyan_add/huoyan_00060"}, {"time": 0.7667, "name": "huoyan_add/huoyan_00062"}, {"time": 0.8333, "name": "huoyan_add/huoyan_00064"}, {"time": 0.9, "name": "huoyan_add/huoyan_00066"}]}, "a17": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff"}]}, "huoyan_add/huoyan_62": {"attachment": [{"time": 0.6333, "name": "huoyan_add/huoyan_00058"}, {"time": 0.7, "name": "huoyan_add/huoyan_00060"}, {"time": 0.7667, "name": "huoyan_add/huoyan_00062"}, {"time": 0.8333, "name": "huoyan_add/huoyan_00064"}, {"time": 0.9, "name": "huoyan_add/huoyan_00066"}]}, "a4": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff"}]}, "heilizi/lizi3": {"color": [{"color": "ffffffff"}], "attachment": [{"time": 0.2667, "name": "heilizi/lizi"}]}, "xlt_add/lizi_14": {"color": [{"color": "ffffffff"}], "attachment": [{"time": 0.1667, "name": "xlt_add/lizi_01"}]}, "a30": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff"}]}, "xlt_add/lizi_15": {"color": [{"color": "ffffffff"}], "attachment": [{"time": 0.1667, "name": "xlt_add/lizi_01"}, {"time": 0.3333, "name": null}]}, "huoyan_add/huoyan_61": {"attachment": [{"time": 0.8667, "name": "huoyan_add/huoyan_00058"}, {"time": 0.9333, "name": "huoyan_add/huoyan_00060"}]}, "jushouyun": {"color": [{"time": 0.4333, "color": "ffffff00"}, {"time": 0.5333, "color": "ffffffff"}, {"time": 0.9333, "color": "ffffff00"}], "attachment": [{"time": 0.3333, "name": "jushouyun-add/jushouyun_0015"}]}, "xialuo/xialuo_3": {"attachment": [{"time": 0.4667, "name": "xialuo/xialuo_00000"}, {"time": 0.5, "name": "xialuo/xialuo_00002"}, {"time": 0.5333, "name": "xialuo/xialuo_00004"}, {"time": 0.5667, "name": "xialuo/xialuo_00006"}, {"time": 0.6, "name": "xialuo/xialuo_00008"}, {"time": 0.6333, "name": "xialuo/xialuo_00010"}, {"time": 0.6667, "name": "xialuo/xialuo_00012"}, {"time": 0.7, "name": "xialuo/xialuo_00014"}, {"time": 0.7333, "name": "xialuo/xialuo_00016"}, {"time": 0.7667, "name": "xialuo/xialuo_00018"}, {"time": 0.8, "name": "xialuo/xialuo_00020"}, {"time": 0.8333, "name": "xialuo/xialuo_00022"}, {"time": 0.8667, "name": "xialuo/xialuo_00024"}, {"time": 0.9, "name": null}]}, "xlt_add/lizi_01": {"color": [{"color": "ffffffff"}], "attachment": [{"time": 0.0333, "name": "xlt_add/lizi_01"}]}, "a8": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff"}]}, "xialuo/xialuo_0": {"attachment": [{"time": 0.9, "name": "xialuo/xialuo_00000"}, {"time": 0.9333, "name": "xialuo/xialuo_00002"}]}, "nq_add_02/nq_b_1": {"color": [{"time": 0.4333, "color": "ffffffff"}], "attachment": [{"time": 0.7333, "name": "nq_add_02/nq_b_01"}, {"time": 0.7667, "name": "nq_add_02/nq_b_02"}, {"time": 0.8, "name": "nq_add_02/nq_b_04"}, {"time": 0.8333, "name": "nq_add_02/nq_b_05"}, {"time": 0.8667, "name": "nq_add_02/nq_b_06"}, {"time": 0.9, "name": "nq_add_02/nq_b_07"}, {"time": 0.9333, "name": null}]}, "heilizi/lizi": {"color": [{"color": "ffffffff"}], "attachment": [{"time": 0.2333, "name": "heilizi/lizi"}]}, "a18": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff"}]}, "a25": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff"}]}, "huoyan_add/huoyan_60": {"attachment": [{"time": 0.4333, "name": "huoyan_add/huoyan_00066"}, {"time": 0.5, "name": "huoyan_add/huoyan_00068"}, {"time": 0.5667, "name": "huoyan_add/huoyan_00070"}, {"time": 0.6333, "name": "huoyan_add/huoyan_00072"}, {"time": 0.6667, "name": null}, {"time": 0.9333, "name": "huoyan_add/huoyan_00066"}]}, "a9": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff"}]}, "a15": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff"}]}, "a28": {"color": [{"time": 0.7, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "color": "ffffffff"}]}, "huoyan_add/huoyan_68": {"attachment": [{"time": 0.4333, "name": "huoyan_add/huoyan_00058"}, {"time": 0.5, "name": "huoyan_add/huoyan_00060"}, {"time": 0.5667, "name": "huoyan_add/huoyan_00062"}, {"time": 0.6333, "name": "huoyan_add/huoyan_00064"}, {"time": 0.7, "name": "huoyan_add/huoyan_00066"}, {"time": 0.7667, "name": "huoyan_add/huoyan_00068"}, {"time": 0.8333, "name": "huoyan_add/huoyan_00070"}, {"time": 0.9, "name": "huoyan_add/huoyan_00072"}, {"time": 0.9333, "name": "huoyan_add/huoyan_00058"}]}, "xlt_add/lizi_13": {"color": [{"color": "ffffffff"}], "attachment": [{"time": 0.1667, "name": "xlt_add/lizi_01"}, {"time": 0.3333, "name": null}]}, "a12": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff"}]}, "golw_add/glow_01": {"color": [{"color": "ffffff00"}, {"time": 0.3, "color": "ffffffff"}, {"time": 0.5333, "color": "ffffff00"}], "attachment": [{"name": "golw_add/glow"}]}, "xlt_add/lizi_10": {"color": [{"color": "ffffffff"}], "attachment": [{"time": 0.1667, "name": "xlt_add/lizi_01"}, {"time": 0.3333, "name": null}]}, "a0": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff"}]}, "xialuo/xialuo_2": {"attachment": [{"time": 0.9, "name": "xialuo/xialuo_00000"}, {"time": 0.9333, "name": "xialuo/xialuo_00002"}]}, "a5": {"twoColor": [{"light": "ff000000", "dark": "ff0200", "curve": "stepped"}, {"time": 0.4, "light": "ff000000", "dark": "ff0200"}, {"time": 0.4333, "light": "ff0000ff", "dark": "ff0200"}]}, "a3": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff"}]}, "xlt_add/lizi_3": {"color": [{"color": "ffffffff"}], "attachment": [{"time": 0.0667, "name": "xlt_add/lizi_01"}]}, "xlt_add/lizi_6": {"color": [{"color": "ffffffff"}], "attachment": [{"time": 0.1, "name": "xlt_add/lizi_01"}]}, "a13": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff"}]}, "huoyan_add/huoyan_64": {"attachment": [{"time": 0.4333, "name": "huoyan_add/huoyan_00058"}, {"time": 0.5, "name": "huoyan_add/huoyan_00060"}, {"time": 0.5667, "name": "huoyan_add/huoyan_00062"}, {"time": 0.6333, "name": "huoyan_add/huoyan_00064"}, {"time": 0.7, "name": "huoyan_add/huoyan_00066"}, {"time": 0.7667, "name": "huoyan_add/huoyan_00068"}, {"time": 0.8333, "name": "huoyan_add/huoyan_00070"}, {"time": 0.9, "name": "huoyan_add/huoyan_00072"}, {"time": 0.9333, "name": "huoyan_add/huoyan_00058"}]}, "a16": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff"}]}, "huoxing-add/huoxing_5": {"color": [{"time": 0.4333, "color": "ffffffff"}], "attachment": [{"time": 0.7333, "name": "huoxing-add/huoxing_0001"}, {"time": 0.7667, "name": "huoxing-add/huoxing_0003"}, {"time": 0.8, "name": "huoxing-add/huoxing_0007"}, {"time": 0.8333, "name": "huoxing-add/huoxing_0009"}, {"time": 0.8667, "name": "huoxing-add/huoxing_0011"}, {"time": 0.9, "name": "huoxing-add/huoxing_0013"}]}, "ks/ks_01": {"color": [{"time": 0.5667, "color": "ffffff00"}, {"time": 0.6, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7667, "color": "ffffff00"}], "attachment": [{"time": 0.4333, "name": "ks/kuo_0002"}]}, "a7": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff"}]}, "huoxing-add/huoxing_4": {"color": [{"time": 0.4333, "color": "ffffffff"}], "attachment": [{"time": 0.7667, "name": "huoxing-add/huoxing_0001"}, {"time": 0.8, "name": "huoxing-add/huoxing_0003"}, {"time": 0.8333, "name": "huoxing-add/huoxing_0005"}, {"time": 0.8667, "name": "huoxing-add/huoxing_0007"}, {"time": 0.9, "name": "huoxing-add/huoxing_0009"}, {"time": 0.9333, "name": null}]}, "xlt_add/lizi_11": {"color": [{"color": "ffffffff"}], "attachment": [{"time": 0.1667, "name": "xlt_add/lizi_01"}, {"time": 0.3333, "name": null}]}, "hou/huoyan_0001": {"color": [{"time": 0.4333, "color": "ffffffff"}], "attachment": [{"time": 0.6, "name": "hou/huoyan_0001"}, {"time": 0.6333, "name": "hou/huoyan_0005"}, {"time": 0.6667, "name": "hou/huoyan_0009"}, {"time": 0.7, "name": "hou/huoyan_0015"}, {"time": 0.7333, "name": "hou/huoyan_0019"}, {"time": 0.7667, "name": null}]}, "huoyankuosna-add/huokuo_0001": {"color": [{"time": 0.4333, "color": "ffffffff"}], "attachment": [{"time": 0.6333, "name": "huoyankuosna-add/huokuo_0001"}, {"time": 0.6667, "name": "huoyankuosna-add/huokuo_0003"}, {"time": 0.7, "name": "huoyankuosna-add/huokuo_0007"}, {"time": 0.7333, "name": "huoyankuosna-add/huokuo_0009"}, {"time": 0.7667, "name": "huoyankuosna-add/huokuo_0011"}, {"time": 0.8, "name": "huoyankuosna-add/huokuo_0013"}, {"time": 0.8333, "name": "huoyankuosna-add/huokuo_0015"}, {"time": 0.8667, "name": "huoyankuosna-add/huokuo_0017"}, {"time": 0.9, "name": "huoyankuosna-add/huokuo_0019"}, {"time": 0.9333, "name": null}]}, "a38": {"twoColor": [{"light": "ff000000", "dark": "ff0200", "curve": "stepped"}, {"time": 0.4, "light": "ff000000", "dark": "ff0200"}, {"time": 0.4333, "light": "ff0000ff", "dark": "ff0200"}]}, "a33": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff"}]}, "xialuo/xialuo_00000": {"attachment": [{"time": 0.4667, "name": "xialuo/xialuo_00000"}, {"time": 0.5, "name": "xialuo/xialuo_00002"}, {"time": 0.5333, "name": "xialuo/xialuo_00004"}, {"time": 0.5667, "name": "xialuo/xialuo_00006"}, {"time": 0.6, "name": "xialuo/xialuo_00008"}, {"time": 0.6333, "name": "xialuo/xialuo_00010"}, {"time": 0.6667, "name": "xialuo/xialuo_00012"}, {"time": 0.7, "name": "xialuo/xialuo_00014"}, {"time": 0.7333, "name": "xialuo/xialuo_00016"}, {"time": 0.7667, "name": "xialuo/xialuo_00018"}, {"time": 0.8, "name": "xialuo/xialuo_00020"}, {"time": 0.8333, "name": "xialuo/xialuo_00022"}, {"time": 0.8667, "name": "xialuo/xialuo_00024"}, {"time": 0.9, "name": null}]}, "a27": {"color": [{"time": 0.6, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "color": "ffffffff"}]}, "huoxing-add/huoxing_1": {"color": [{"time": 0.4333, "color": "ffffffff"}], "attachment": [{"time": 0.4333, "name": null}, {"time": 0.8333, "name": "huoxing-add/huoxing_0001"}, {"time": 0.8667, "name": "huoxing-add/huoxing_0003"}, {"time": 0.9, "name": "huoxing-add/huoxing_0005"}, {"time": 0.9333, "name": "huoxing-add/huoxing_0007"}]}, "a39": {"twoColor": [{"light": "ff000000", "dark": "ff0200", "curve": "stepped"}, {"time": 0.4, "light": "ff000000", "dark": "ff0200"}, {"time": 0.4333, "light": "ff0000ff", "dark": "ff0200"}]}, "a20": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff"}]}, "xialuo/xialuo_4": {"attachment": [{"time": 0.9, "name": "xialuo/xialuo_00000"}, {"time": 0.9333, "name": "xialuo/xialuo_00002"}]}, "a10": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff"}]}, "xlt_add/lizi_5": {"color": [{"color": "ffffffff"}], "attachment": [{"time": 0.1, "name": "xlt_add/lizi_01"}]}, "huoyan_add/huoyan_58": {"attachment": [{"time": 0.6333, "name": "huoyan_add/huoyan_00058"}, {"time": 0.7, "name": "huoyan_add/huoyan_00060"}, {"time": 0.7667, "name": "huoyan_add/huoyan_00062"}, {"time": 0.8333, "name": "huoyan_add/huoyan_00064"}, {"time": 0.9, "name": "huoyan_add/huoyan_00066"}]}, "huoyankuosna-add/huokuo_2": {"color": [{"time": 0.4333, "color": "ffffffff"}], "attachment": [{"time": 0.5333, "name": "huoyankuosna-add/huokuo_0001"}, {"time": 0.5667, "name": "huoyankuosna-add/huokuo_0003"}, {"time": 0.6, "name": "huoyankuosna-add/huokuo_0005"}, {"time": 0.6333, "name": "huoyankuosna-add/huokuo_0007"}, {"time": 0.6667, "name": "huoyankuosna-add/huokuo_0009"}, {"time": 0.7, "name": "huoyankuosna-add/huokuo_0011"}, {"time": 0.7333, "name": "huoyankuosna-add/huokuo_0013"}, {"time": 0.7667, "name": "huoyankuosna-add/huokuo_0015"}, {"time": 0.8, "name": "huoyankuosna-add/huokuo_0017"}, {"time": 0.8333, "name": "huoyankuosna-add/huokuo_0019"}, {"time": 0.8667, "name": "huoyankuosna-add/huokuo_0021"}, {"time": 0.9, "name": null}]}, "xialuo/xialuo_1": {"attachment": [{"time": 0.4667, "name": "xialuo/xialuo_00000"}, {"time": 0.5, "name": "xialuo/xialuo_00002"}, {"time": 0.5333, "name": "xialuo/xialuo_00004"}, {"time": 0.5667, "name": "xialuo/xialuo_00006"}, {"time": 0.6, "name": "xialuo/xialuo_00008"}, {"time": 0.6333, "name": "xialuo/xialuo_00010"}, {"time": 0.6667, "name": "xialuo/xialuo_00012"}, {"time": 0.7, "name": "xialuo/xialuo_00014"}, {"time": 0.7333, "name": "xialuo/xialuo_00016"}, {"time": 0.7667, "name": "xialuo/xialuo_00018"}, {"time": 0.8, "name": "xialuo/xialuo_00020"}, {"time": 0.8333, "name": "xialuo/xialuo_00022"}, {"time": 0.8667, "name": "xialuo/xialuo_00024"}, {"time": 0.9, "name": null}]}, "xlt_add/lizi_8": {"color": [{"color": "ffffffff"}], "attachment": [{"time": 0.1333, "name": "xlt_add/lizi_01"}]}, "jushouyun2": {"color": [{"time": 0.4333, "color": "ffffff00"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.7, "color": "ffffff00"}], "attachment": [{"time": 0.3333, "name": "jushouyun-add/jushouyun_0015"}]}, "nq_add_02/nq_b_01": {"color": [{"time": 0.4333, "color": "ffffffff"}], "attachment": [{"time": 0.7333, "name": "nq_add_02/nq_b_01"}, {"time": 0.7667, "name": "nq_add_02/nq_b_02"}, {"time": 0.8, "name": "nq_add_02/nq_b_04"}, {"time": 0.8333, "name": "nq_add_02/nq_b_05"}, {"time": 0.8667, "name": "nq_add_02/nq_b_06"}, {"time": 0.9, "name": "nq_add_02/nq_b_07"}, {"time": 0.9333, "name": null}]}, "a29": {"color": [{"time": 0.6333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.7, "color": "ffffffff"}]}, "huoyan_add/huoyan_67": {"attachment": [{"time": 0.4333, "name": "huoyan_add/huoyan_00066"}, {"time": 0.5, "name": "huoyan_add/huoyan_00068"}, {"time": 0.5667, "name": "huoyan_add/huoyan_00070"}, {"time": 0.6333, "name": "huoyan_add/huoyan_00072"}, {"time": 0.6667, "name": null}, {"time": 0.9333, "name": "huoyan_add/huoyan_00066"}]}, "a14": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff"}]}, "xlt_add/lizi_12": {"color": [{"color": "ffffffff"}], "attachment": [{"time": 0.1667, "name": "xlt_add/lizi_01"}, {"time": 0.3333, "name": null}]}, "a24": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff"}]}, "lightttt": {"color": [{"time": 0.5333, "color": "ff7100ff"}, {"time": 0.6333, "color": "ff710000"}], "attachment": [{"time": 0.3333, "name": "lightttt"}]}, "huoyan_add/huoyan_00058": {"attachment": [{"time": 0.8667, "name": "huoyan_add/huoyan_00058"}, {"time": 0.9333, "name": "huoyan_add/huoyan_00060"}]}, "xlt_add/lizi_7": {"color": [{"color": "ffffffff"}], "attachment": [{"time": 0.1333, "name": "xlt_add/lizi_01"}]}, "huoxing-add/huoxing_2": {"color": [{"time": 0.4333, "color": "ffffffff"}], "attachment": [{"time": 0.7667, "name": "huoxing-add/huoxing_0001"}, {"time": 0.8, "name": "huoxing-add/huoxing_0003"}, {"time": 0.8333, "name": "huoxing-add/huoxing_0005"}, {"time": 0.8667, "name": "huoxing-add/huoxing_0007"}, {"time": 0.9, "name": "huoxing-add/huoxing_0009"}, {"time": 0.9333, "name": "huoxing-add/huoxing_0011"}]}, "xlt_add/lizi_4": {"color": [{"color": "ffffffff"}], "attachment": [{"time": 0.1, "name": "xlt_add/lizi_01"}]}, "huoxing-add/huoxing_3": {"color": [{"time": 0.4333, "color": "ffffffff"}], "attachment": [{"time": 0.7333, "name": "huoxing-add/huoxing_0001"}, {"time": 0.7667, "name": "huoxing-add/huoxing_0003"}, {"time": 0.8, "name": "huoxing-add/huoxing_0005"}, {"time": 0.8333, "name": "huoxing-add/huoxing_0007"}, {"time": 0.8667, "name": "huoxing-add/huoxing_0009"}, {"time": 0.9, "name": "huoxing-add/huoxing_0011"}, {"time": 0.9333, "name": null}]}, "a1": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff"}]}, "xlt_add/lizi_9": {"color": [{"color": "ffffffff"}], "attachment": [{"time": 0.1333, "name": "xlt_add/lizi_01"}]}, "xuanzhuan/linxiangru_skill_xl_00000": {"attachment": [{"name": "xuanzhuan/linxiangru_skill_xl_00000"}, {"time": 0.0667, "name": "xuanzhuan/linxiangru_skill_xl_00002"}, {"time": 0.1, "name": "xuanzhuan/linxiangru_skill_xl_00004"}, {"time": 0.1667, "name": "xuanzhuan/linxiangru_skill_xl_00006"}, {"time": 0.2, "name": "xuanzhuan/linxiangru_skill_xl_00008"}, {"time": 0.2667, "name": "xuanzhuan/linxiangru_skill_xl_00010"}, {"time": 0.3, "name": "xuanzhuan/linxiangru_skill_xl_00012"}, {"time": 0.3667, "name": "xuanzhuan/linxiangru_skill_xl_00014"}, {"time": 0.4333, "name": "xuanzhuan/linxiangru_skill_xl_00016"}, {"time": 0.4667, "name": "xuanzhuan/linxiangru_skill_xl_00032"}, {"time": 0.5333, "name": null}]}, "a22": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff"}]}, "xlt_add/lizi_2": {"color": [{"color": "ffffffff"}], "attachment": [{"time": 0.0667, "name": "xlt_add/lizi_01"}]}}, "bones": {"nzbone2": {"translate": [{"time": 0.5667, "y": 23.32}]}, "nzbone4": {"translate": [{"time": 0.4333, "x": 0.04, "y": 0.8}]}, "nzbone5": {"translate": [{"time": 0.4333, "x": 2.48, "y": -0.28}]}, "nzbone6": {"rotate": [{"time": 0.4333, "angle": 4.04}]}, "nzbone8": {"translate": [{"time": 0.4333, "x": 3.12, "y": 0.05}]}, "nzbone18": {"translate": [{"x": 1.26, "y": -0.16}]}, "nzbone67": {"rotate": [{"time": 0.4333, "angle": 5.43}], "translate": [{"time": 0.4333, "x": 0.24, "y": 8.44}]}, "nzbone68": {"rotate": [{"time": 0.4333, "angle": -12.81}]}, "nzbone70": {"rotate": [{"time": 0.4333, "angle": -14.36}], "translate": [{"time": 0.4333, "x": 0.25, "y": 8.59}]}, "nzbone71": {"rotate": [{"time": 0.4333, "angle": 20.05}]}, "nzbone78": {"translate": [{"y": 29.7}]}, "nzbone80": {"translate": [{"y": 35.97}]}, "nzbone13": {"rotate": [{"time": 0.4333, "angle": 6.83, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5, "angle": 17.25, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -11.06, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.7667, "angle": 6.83, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.8333, "angle": 17.25, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 6.83}], "translate": [{"time": 0.4333, "x": 5.28, "y": 17.59, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5, "x": 8.35, "y": 27.83, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.7667, "x": 5.28, "y": 17.59, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.8333, "x": 8.35, "y": 27.83, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": 5.28, "y": 17.59}]}, "nzbone10": {"rotate": [{"time": 0.4333, "angle": 5.25, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 17.25, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.7667, "angle": 5.25, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 5.25}], "translate": [{"time": 0.4333, "x": 0.97, "y": 0.87, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 3.17, "y": 2.84, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.7667, "x": 0.97, "y": 0.87, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": 0.97, "y": 0.87}]}, "nzbone11": {"rotate": [{"time": 0.4333, "angle": 13.07, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 17.25, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.7667, "angle": 13.07, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 13.07}], "translate": [{"time": 0.4333, "x": 1.34, "y": 11.66, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": 1.77, "y": 15.38, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.7667, "x": 1.34, "y": 11.66, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": 1.34, "y": 11.66}]}, "nzbone12": {"rotate": [{"time": 0.4333, "angle": 42.33, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.7667, "angle": 42.33, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 41.61}], "translate": [{"time": 0.4333, "x": -0.69, "y": 15.11, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.7667, "x": -0.69, "y": 15.11, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": -0.68, "y": 14.85}]}, "nzbone14": {"translate": [{"time": 0.4333, "x": 1.55, "y": 1.26, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 6.39, "y": 5.2, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.7667, "x": 1.55, "y": 1.26, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": 1.55, "y": 1.26}]}, "nzbone15": {"translate": [{"time": 0.4333, "x": 4.29, "y": 5.09, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": 6.16, "y": 7.31, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.7667, "x": 4.29, "y": 5.09, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": 4.29, "y": 5.09}]}, "nzbone16": {"translate": [{"time": 0.4333, "x": 5.97, "y": 4.73, "curve": 0.312, "c2": 0.27, "c3": 0.67, "c4": 0.68}, {"time": 0.5, "x": 2.23, "y": 1.77, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": 7.32, "y": 5.8, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.7667, "x": 5.97, "y": 4.73, "curve": 0.312, "c2": 0.27, "c3": 0.67, "c4": 0.68}, {"time": 0.8333, "x": 2.23, "y": 1.77, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": 5.97, "y": 4.73}]}, "nzbone17": {"translate": [{"time": 0.4333, "x": 10.15, "y": 7.25, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.5, "x": 7.69, "y": 5.5, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.6, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 0.7667, "x": 10.15, "y": 7.25, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.8333, "x": 7.69, "y": 5.5, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.9333, "x": 9.72, "y": 6.94}]}, "nzbone57": {"translate": [{"time": 0.4333, "x": -8.11, "y": 19.37}], "scale": [{"time": 0.4333, "x": 0.848, "y": 0.848}]}, "nzbone58": {"translate": [{"time": 0.4333, "x": -4.06, "y": 29.34, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": -3.99, "y": 28.78}], "scale": [{"time": 0.4333, "x": 0.8, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": 0.803, "y": 0.803}]}, "nzbone59": {"translate": [{"time": 0.4333, "x": 5.48, "y": 18.14}], "scale": [{"time": 0.4333, "x": 0.874, "y": 0.874}]}, "nzbone56": {"translate": [{"time": 0.4333, "x": -2.86, "y": 0.7}], "scale": [{"time": 0.4333, "x": 0.963, "y": 0.963}]}, "nzbone55": {"translate": [{"time": 0.4333, "y": -3.51, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": -0.27, "y": -2.1}], "scale": [{"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": 0.991, "y": 0.991}]}, "nzbone54": {"translate": [{"time": 0.4333, "x": 1.22, "y": 10.52}], "scale": [{"time": 0.4333, "x": 0.913, "y": 0.913}]}, "nzbone60": {"translate": [{"time": 0.4333, "x": 9.17, "y": 17.28}], "scale": [{"time": 0.4333, "x": 0.826, "y": 0.826}]}, "nzbone61": {"translate": [{"time": 0.4333, "x": -12.89, "y": 13.89}], "scale": [{"time": 0.4333, "x": 0.817, "y": 0.817}]}, "nzbone62": {"translate": [{"time": 0.4333, "x": -7.38, "y": 4.22}], "scale": [{"time": 0.4333, "x": 0.9, "y": 0.9}]}, "nzbone63": {"translate": [{"time": 0.4333, "x": 0.23, "y": -1.47}], "scale": [{"time": 0.4333, "x": 0.983, "y": 0.983}]}, "nzbone64": {"translate": [{"time": 0.4333, "x": 1.46, "y": -1.5}], "scale": [{"time": 0.4333, "x": 0.974, "y": 0.974}]}, "nzbone53": {"translate": [{"time": 0.4333, "x": 0.8, "y": 13.2}], "scale": [{"time": 0.4333, "x": 0.887, "y": 0.887}]}, "nzbone52": {"translate": [{"time": 0.4333, "x": -1.07, "y": 6.55}], "scale": [{"time": 0.4333, "x": 0.939, "y": 0.939}]}, "nzbone48": {"rotate": [{"time": 0.4333, "angle": -13.76, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.5333, "angle": 7.58, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -20.58, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.7667, "angle": -13.76, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.8667, "angle": 7.58, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -13.76}]}, "nzbone47": {"rotate": [{"time": 0.4333, "angle": -0.85, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.5, "angle": 3.88, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -11.64, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 0.7667, "angle": -0.85, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.8333, "angle": 3.88, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -0.85}]}, "nzbone45": {"rotate": [{"time": 0.4333, "angle": 0.37, "curve": 0.337, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 0.4667, "angle": -2.29, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.5, "angle": -6.47, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 7.25, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.7667, "angle": 0.37, "curve": 0.337, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 0.8, "angle": -2.29, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.8333, "angle": -6.47, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 0.37}]}, "nzbone46": {"rotate": [{"time": 0.4333, "angle": 17.19, "curve": 0.311, "c2": 0.25, "c3": 0.648, "c4": 0.59}, {"time": 0.4667, "angle": 11.54, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.5667, "angle": -14.79, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 19.95, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 0.7667, "angle": 17.19, "curve": 0.311, "c2": 0.25, "c3": 0.648, "c4": 0.59}, {"time": 0.8, "angle": 11.54, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.9, "angle": -14.79, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 17.19}]}, "nzbone40": {"rotate": [{"time": 0.4333, "angle": 8.17, "curve": 0.262, "c2": 0.08, "c3": 0.652, "c4": 0.62}, {"time": 0.5333, "angle": -0.66, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.6, "angle": -4.59, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 8.17, "curve": 0.262, "c2": 0.08, "c3": 0.652, "c4": 0.62}, {"time": 0.8667, "angle": -0.66, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.9333, "angle": 8.17}]}, "nzbone41": {"rotate": [{"time": 0.4333, "angle": 7.28, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4667, "angle": 16.07, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.5333, "angle": 7.28, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.6333, "angle": -20.22, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.7667, "angle": 7.28, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.8, "angle": 16.07, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.8667, "angle": 7.28}]}, "nzbone49": {"rotate": [{"time": 0.4333, "angle": 4.17, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.5, "angle": -2.31, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.5333, "angle": -6.67, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 7.64, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.7667, "angle": 4.17, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.8333, "angle": -2.31, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.8667, "angle": -6.67, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 4.17}]}, "nzbone50": {"rotate": [{"time": 0.4333, "angle": 17.87, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.5, "angle": 9.45, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.6, "angle": -16.89, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 0.7667, "angle": 17.87, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.8333, "angle": 9.45, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.9333, "angle": 17.28}]}, "nzbone38": {"rotate": [{"time": 0.4333, "angle": -8.47, "curve": 0.312, "c2": 0.27, "c3": 0.67, "c4": 0.68}, {"time": 0.5, "angle": -0.21, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.5667, "angle": 4.71, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -11.46, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.7667, "angle": -8.47, "curve": 0.312, "c2": 0.27, "c3": 0.67, "c4": 0.68}, {"time": 0.8333, "angle": -0.21, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.9, "angle": 4.71, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -8.47}]}, "nzbone39": {"rotate": [{"time": 0.4333, "angle": -17.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.5, "angle": -8.56, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.6, "angle": 19.17, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 0.7667, "angle": -17.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.8333, "angle": -8.56, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.9333, "angle": -15.87}]}, "nzbone42": {"rotate": [{"time": 0.4333, "angle": -2.21, "curve": 0.337, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 0.4667, "angle": -5.32, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.5, "angle": -10.2, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.5667, "angle": -4.31, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.6667, "angle": 5.8, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.7667, "angle": -2.21, "curve": 0.337, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 0.8, "angle": -5.32, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.8333, "angle": -10.2, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.9, "angle": -4.31, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.9333, "angle": -2.21}]}, "nzbone43": {"rotate": [{"time": 0.4333, "angle": 9.5, "curve": 0.311, "c2": 0.25, "c3": 0.648, "c4": 0.59}, {"time": 0.4667, "angle": 4.56, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.5667, "angle": -17.92, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 0.7333, "angle": 11.92, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 0.7667, "angle": 9.5, "curve": 0.311, "c2": 0.25, "c3": 0.648, "c4": 0.59}, {"time": 0.8, "angle": 4.56, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.9, "angle": -17.92, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 0.9333, "angle": 9.5}]}, "nzbone26": {"rotate": [{"time": 0.4333, "angle": 1.18}], "scale": [{"time": 0.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6333, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "nzbone27": {"rotate": [{"time": 0.4333, "angle": -1.06}], "scale": [{"time": 0.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "nzbone28": {"rotate": [{"time": 0.4333, "angle": -2.18}], "scale": [{"time": 0.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7667, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.8667}]}, "nzbone31": {"rotate": [{"time": 0.4333, "angle": 4.33}], "scale": [{"time": 0.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7333, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}]}, "nzbone30": {"rotate": [{"time": 0.4333, "angle": 2.58}], "scale": [{"time": 0.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "nzbone29": {"rotate": [{"time": 0.4333, "angle": -0.95}], "scale": [{"time": 0.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "nzbone34": {"rotate": [{"time": 0.4333, "angle": 3.71}], "scale": [{"time": 0.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.9}]}, "nzbone33": {"rotate": [{"time": 0.4333, "angle": 2.37}], "scale": [{"time": 0.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7333, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.8}]}, "nzbone32": {"rotate": [{"time": 0.4333, "angle": -0.33}], "scale": [{"time": 0.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "nzbone35": {"rotate": [{"time": 0.4333, "angle": -0.29}], "scale": [{"time": 0.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "nzbone36": {"rotate": [{"time": 0.4333, "angle": -3.23}], "scale": [{"time": 0.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7667, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}]}, "nzbone37": {"rotate": [{"time": 0.4333, "angle": -4.69}], "scale": [{"time": 0.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "nzbone23": {"rotate": [{"time": 0.4333, "angle": -3.54, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -6.2, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -3.54}], "translate": [{"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -9.71, "y": 0.82}]}, "nzbone24": {"rotate": [{"time": 0.4333, "angle": -2.19, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 0.05, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -2.19}]}, "nzbone20": {"rotate": [{"time": 0.4333, "angle": 1, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -9.82, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 1}]}, "nzbone21": {"rotate": [{"time": 0.4333, "angle": 8.08, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 12.45, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 8.08}]}, "nzbone22": {"rotate": [{"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -30.26, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "nzbone74": {"rotate": [{"time": 0.4333, "angle": -6.69}]}, "nzbone75": {"rotate": [{"time": 0.4333, "angle": 11.95, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 11.95, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 11.95}]}, "nzbone76": {"rotate": [{"time": 0.4333, "angle": 15.45}]}, "nzbone77": {"rotate": [{"time": 0.4333, "angle": -29.71}]}, "a11": {"rotate": [{"time": 0.4333, "angle": 5.69}], "translate": [{"time": 0.4333, "x": -76.07, "y": 124.26}]}, "nzbone83": {"translate": [{"time": 0.4333, "x": -1.07, "y": 6.55}], "scale": [{"time": 0.4333, "x": 0.939, "y": 0.939}]}, "nzbone84": {"translate": [{"time": 0.4333, "x": 0.8, "y": 13.2}], "scale": [{"time": 0.4333, "x": 0.887, "y": 0.887}]}, "nzbone85": {"translate": [{"time": 0.4333, "x": 1.22, "y": 10.52}], "scale": [{"time": 0.4333, "x": 0.913, "y": 0.913}]}, "nzbone86": {"translate": [{"time": 0.4333, "y": -3.51, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": -0.27, "y": -2.1}], "scale": [{"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": 0.991, "y": 0.991}]}, "nzbone87": {"translate": [{"time": 0.4333, "x": -2.86, "y": 0.7}], "scale": [{"time": 0.4333, "x": 0.963, "y": 0.963}]}, "nzbone88": {"translate": [{"time": 0.4333, "x": -8.11, "y": 19.37}], "scale": [{"time": 0.4333, "x": 0.848, "y": 0.848}]}, "nzbone89": {"translate": [{"time": 0.4333, "x": -4.06, "y": 29.34, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": -3.99, "y": 28.78}], "scale": [{"time": 0.4333, "x": 0.8, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": 0.803, "y": 0.803}]}, "nzbone90": {"translate": [{"time": 0.4333, "x": 5.48, "y": 18.14}], "scale": [{"time": 0.4333, "x": 0.874, "y": 0.874}]}, "nzbone91": {"translate": [{"time": 0.4333, "x": 9.17, "y": 17.28}], "scale": [{"time": 0.4333, "x": 0.826, "y": 0.826}]}, "nzbone92": {"translate": [{"time": 0.4333, "x": -12.89, "y": 13.89}], "scale": [{"time": 0.4333, "x": 0.817, "y": 0.817}]}, "nzbone93": {"translate": [{"time": 0.4333, "x": -7.38, "y": 4.22}], "scale": [{"time": 0.4333, "x": 0.9, "y": 0.9}]}, "nzbone94": {"translate": [{"time": 0.4333, "x": 0.23, "y": -1.47}], "scale": [{"time": 0.4333, "x": 0.983, "y": 0.983}]}, "nzbone95": {"translate": [{"time": 0.4333, "x": 1.46, "y": -1.5}], "scale": [{"time": 0.4333, "x": 0.974, "y": 0.974}]}, "xlt5": {"rotate": [{}, {"time": 0.1, "angle": 14.07}], "translate": [{}, {"time": 0.1, "x": -25.74, "y": -57.87}, {"time": 0.3, "x": -155.84, "y": -102.21}], "scale": [{}, {"time": 0.1, "x": 0.35, "y": 0.35}, {"time": 0.1333, "x": 0.7, "y": 0.7, "curve": "stepped"}, {"time": 0.2667, "x": 0.7, "y": 0.7}, {"time": 0.3, "x": 0, "y": 0}]}, "xlt": {"rotate": [{}, {"time": 0.0333, "angle": 33.55}], "translate": [{}, {"time": 0.2667, "x": -156.17, "y": -102.35}], "scale": [{}, {"time": 0.0333, "x": 0.3, "y": 0.3}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2}, {"time": 0.2667, "x": 0, "y": 0}]}, "xlt12": {"rotate": [{}, {"time": 0.1667, "angle": -60.47}], "translate": [{}, {"time": 0.1667, "x": -190.36, "y": -32.11}, {"time": 0.3, "x": -164.36, "y": -90.18}], "scale": [{}, {"time": 0.1667, "x": 0.1, "y": 0.2}, {"time": 0.2, "x": 0.5, "y": 0.5}, {"time": 0.2667, "x": 0.35, "y": 0.35}]}, "xlt14": {"translate": [{"x": -249.64, "y": -101.19, "curve": "stepped"}, {"time": 0.1667, "x": -249.64, "y": -101.19}, {"time": 0.3, "x": -165.32, "y": -101.19}], "scale": [{}, {"time": 0.1667, "x": 0.3, "y": 0.3}, {"time": 0.2333, "x": 0.7, "y": 0.7}, {"time": 0.3, "x": 0.5, "y": 0.5}]}, "xlt13": {"rotate": [{}, {"time": 0.1667, "angle": -7.01}], "translate": [{}, {"time": 0.1667, "x": -54.61, "y": -114.14}, {"time": 0.3, "x": -124.25, "y": -105.69}], "scale": [{}, {"time": 0.1667, "x": 0.2, "y": 0.3}, {"time": 0.2, "x": 0.8, "y": 0.8}, {"time": 0.2667, "x": 0.65, "y": 0.65}]}, "xlt2": {"rotate": [{}, {"time": 0.0333, "angle": -24.12}], "translate": [{}, {"time": 0.0333, "x": -270.14, "y": -50.86}, {"time": 0.2333, "x": -156.72, "y": -101.9}], "scale": [{}, {"time": 0.0333, "x": 0.3, "y": 0.3}, {"time": 0.0667, "x": 0.6, "y": 0.6, "curve": "stepped"}, {"time": 0.2, "x": 0.6, "y": 0.6}, {"time": 0.2333, "x": 0, "y": 0}]}, "xlt6": {"rotate": [{}, {"time": 0.1, "angle": 8.06}], "translate": [{}, {"time": 0.1, "x": -220.03, "y": -113.44}, {"time": 0.3, "x": -155.68, "y": -102.07}], "scale": [{}, {"time": 0.1, "x": 0.35, "y": 0.35}, {"time": 0.1333, "x": 0.5, "y": 0.5, "curve": "stepped"}, {"time": 0.2333, "x": 0.5, "y": 0.5}, {"time": 0.3, "x": 0, "y": 0}]}, "xlt9": {"rotate": [{"angle": -24.22}], "translate": [{"x": -245.44, "y": -64.96}, {"time": 0.1333, "x": -235.7, "y": -69.05}, {"time": 0.3, "x": -156.34, "y": -101.64}], "scale": [{"time": 0.1333, "x": 0.2, "y": 0.2}, {"time": 0.1667, "x": 0.5, "y": 0.5, "curve": "stepped"}, {"time": 0.2667, "x": 0.5, "y": 0.5}, {"time": 0.3, "x": 0, "y": 0}]}, "xlt4": {"rotate": [{"angle": -56.07}], "translate": [{"x": -99.61, "y": -181.83, "curve": "stepped"}, {"time": 0.0667, "x": -99.61, "y": -181.83}, {"time": 0.2667, "x": -155.91, "y": -102.9}], "scale": [{}, {"time": 0.0667, "x": 0.2, "y": 0.2}, {"time": 0.1333, "x": 0.8, "y": 0.8, "curve": "stepped"}, {"time": 0.2333, "x": 0.8, "y": 0.8}, {"time": 0.2667, "x": 0, "y": 0}]}, "xlt3": {"rotate": [{}, {"time": 0.0667, "angle": 119.58}], "translate": [{}, {"time": 0.0667, "x": -203.39, "y": 12.6}, {"time": 0.2667, "x": -155.71, "y": -101.76}], "scale": [{}, {"time": 0.0667, "x": 0.45, "y": 0.45}, {"time": 0.1, "x": 0.7, "y": 0.7, "curve": "stepped"}, {"time": 0.2333, "x": 0.7, "y": 0.7}, {"time": 0.2667, "x": 0, "y": 0}]}, "xlt7": {"rotate": [{}, {"time": 0.1, "angle": 98.14}], "translate": [{}, {"time": 0.1, "x": -171.94, "y": -15.27}, {"time": 0.3, "x": -156.22, "y": -101.79}], "scale": [{}, {"time": 0.1, "x": 0.2, "y": 0.2}, {"time": 0.1333, "x": 0.4, "y": 0.4, "curve": "stepped"}, {"time": 0.2667, "x": 0.4, "y": 0.4}, {"time": 0.3, "x": 0, "y": 0}]}, "xlt10": {"rotate": [{"angle": -51.74}], "translate": [{"x": -90.75, "y": -175.9, "curve": "stepped"}, {"time": 0.1333, "x": -90.75, "y": -175.9}, {"time": 0.3, "x": -150.85, "y": -108.77}], "scale": [{"time": 0.1333, "x": 0.2, "y": 0.3}, {"time": 0.1667, "x": 0.8, "y": 0.8, "curve": "stepped"}, {"time": 0.2667, "x": 0.8, "y": 0.8}, {"time": 0.3, "x": 0, "y": 0}]}, "xlt16": {"rotate": [{"angle": -51.74}, {"time": 0.1667, "angle": -73.55}], "translate": [{"x": -90.75, "y": -175.9}, {"time": 0.1667, "x": -138.99, "y": -175.9}, {"time": 0.3, "x": -145.07, "y": -138.72}], "scale": [{"time": 0.1667, "x": 0.2, "y": 0.3}, {"time": 0.2333, "x": 0.5, "y": 0.5}, {"time": 0.2667, "x": 0.8, "y": 0.8}, {"time": 0.3, "x": 0.5, "y": 0.5}]}, "xlt15": {"rotate": [{"angle": -51.74}, {"time": 0.1667, "angle": -135.1}], "translate": [{"x": -90.75, "y": -175.9}, {"time": 0.1667, "x": -213.77, "y": -175.32}, {"time": 0.3333, "x": -150.85, "y": -108.77}], "scale": [{"time": 0.1667, "x": 0.2, "y": 0.3}, {"time": 0.2333, "x": 0.8, "y": 0.8, "curve": "stepped"}, {"time": 0.2667, "x": 0.8, "y": 0.8}, {"time": 0.3, "x": 0, "y": 0}]}, "xlt8": {"rotate": [{"angle": -16.41}], "translate": [{"x": -57.53, "y": -136.27, "curve": "stepped"}, {"time": 0.1333, "x": -57.53, "y": -136.27}, {"time": 0.3, "x": -148.22, "y": -105.52}], "scale": [{"time": 0.1333, "x": 0.3, "y": 0.3}, {"time": 0.1667, "x": 0.7, "y": 0.7, "curve": "stepped"}, {"time": 0.2667, "x": 0.7, "y": 0.7}, {"time": 0.3, "x": 0, "y": 0}]}, "nq2": {"scale": [{"time": 0.4333, "x": -1}]}, "huoyankuosna": {"translate": [{"time": 0.4333, "x": -59.42, "y": -66.75}]}, "huoyankuosna3": {"translate": [{"time": 0.4333, "x": -24.42, "y": 16.28}]}, "huoyankuosna2": {"translate": [{"time": 0.4333, "x": 38.26, "y": 8.14}]}, "huoxing-add/huoxing_3": {"translate": [{"time": 0.7333, "x": -83.84, "y": 111.52}]}, "huoxing-add/huoxing_2": {"translate": [{"time": 0.7333, "x": -38.26, "y": 32.56}]}, "heilizi/lizi": {"translate": [{"time": 0.2333, "x": 415.81, "y": 50.41}], "scale": [{"time": 0.2333, "x": 0.5, "y": 0.5}]}, "huoxing-add/huoxing_4": {"translate": [{"time": 0.7667, "x": -29.3, "y": -34.19}]}, "huoxing-add/huoxing_1": {"translate": [{"time": 0.8333, "x": 65.93, "y": 112.33}]}, "huoxing-add/huoxing_5": {"translate": [{"time": 0.7333, "x": 36.63, "y": 14.65}]}, "ks": {"scale": [{"time": 0.4333, "x": 2, "y": 2}, {"time": 0.6, "x": 1.25, "y": 1.25}, {"time": 0.6333, "x": 2, "y": 2}]}, "huoxing-add/huoxing_6": {"translate": [{"time": 0.8, "x": 15.47}]}, "heilizi/lizi2": {"translate": [{"time": 0.2667, "x": 434.52, "y": 68.19}], "scale": [{"time": 0.2667, "x": 0.584, "y": 0.584}]}, "golw_add/glow_01": {"translate": [{"x": 17.82, "y": 52.1}]}, "lizi2tiao": {"translate": [{"x": 109.86, "y": 38.2}], "scale": [{"x": 2.498, "y": 2.498}]}, "heilizi/lizi3": {"translate": [{"time": 0.2667, "x": 346.58, "y": 40.12}], "scale": [{"time": 0.2667, "x": 0.326, "y": 0.326}]}, "xlt11": {"rotate": [{}, {"time": 0.1667, "angle": 13.84}, {"time": 0.3, "angle": 17.33}], "translate": [{}, {"time": 0.1667, "x": -41.98, "y": -65.8}, {"time": 0.3, "x": -137.86, "y": -96.73}], "scale": [{}, {"time": 0.1667, "x": 0.3, "y": 0.3}, {"time": 0.2, "x": 0.5, "y": 0.5}]}, "jushouyun": {"translate": [{"time": 0.4333, "y": 27.9}, {"time": 0.5333, "x": -6.51, "y": 237.17}], "scale": [{"time": 0.3333, "x": 2, "y": 2}]}, "nzbone": {"translate": [{"time": 0.4667, "y": -82.2}, {"time": 0.6}]}, "jushouyun2": {"translate": [{"time": 0.4333, "y": 27.9}, {"time": 0.5667, "x": -6.51, "y": 237.17}, {"time": 0.7, "x": -6.51, "y": 344.29}], "scale": [{"time": 0.3333, "x": 2, "y": 2, "curve": "stepped"}, {"time": 0.4333, "x": 2, "y": 2}, {"time": 0.8333, "x": 3.078, "y": 3.078}]}, "nzbone25": {"rotate": [{}, {"time": 0.9333, "angle": -4.14}]}, "root": {"translate": [{"time": 0.9333, "y": 1.74}]}, "lighttt": {"scale": [{"time": 0.3333}, {"time": 0.5333, "x": 11.256, "y": 11.256}]}}, "deform": {"default": {"ks/ks_01": {"ks/kuo_0002": [{"time": 0.6667, "vertices": [-0.29224, 0, -0.29224, 0, -0.29224, 0, -0.29224]}]}, "a36": {"a6": [{"vertices": [-51.12001, 7.41124, -50.11333, 9.24277, -47.95671, 10.21869, -45.74039, 9.312, -42.18549, 7.28273, -37.8436, 6.72709, -37.00831, 9.94782, -37.22334, 13.62815, -34.92979, 14.65709, -27.40778, 13.10272, -21.01677, 14.57769, -14.85593, 15.05109, -11.30721, 15.99736, -9.30468, 15.67241, -6.58126, 12.68947, -7.63943, 10.18942, -6.00109, 6.14682, -4.1232, 1.47404, -2.52893, 0.45983, 0, 0, 0, 0, -0.84612, 0.07318, 1.03917, -1.41762, 0.84559, -3.04546, -0.41669, -4.35209, -2.12697, -4.531, -5.94872, -4.53137, -8.39604, -2.34838, -8.92449, -4.34307, -11.50598, -6.48013, -16.40412, -4.78436, -19.34193, -0.2953, -22.08281, 0.22766, -23.88922, 0.92345, -20.60853, 0.6255, -20.31696, 1.88308, -30.25004, 4.12808, -32.70719, 2.59747, -36.7842, 4.6211, -40.86856, 3.60755, -44.36232, 5.10687, -46.77997, 6.95484, -49.4958, 6.99363, -51.12834, 4.81388, -51.67223, 5.53821, -29.48202, 8.09438, -20.7077, 7.44671, -14.24933, 4.76388, -7.75502, 0.86387, -13.19829, 11.09657, -3.8177, -2.66624, -33.20877, 5.61972, -13.23015, -2.45774]}]}, "a30": {"a21": [{"time": 0.4333, "vertices": [5.64005, 4.89908, -0.30093, 7.46462, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.45341, 2.64563, 0.30749, 3.00287, -2.15234, 2.11633, 3.32195, 6.69148, 5.63273, 7.46909]}]}}}, "drawOrder": [{"offsets": [{"slot": "golw_add/glow_01", "offset": -16}, {"slot": "daoqi/nanjian_zmr_dg_00051", "offset": -16}, {"slot": "chongji/gsxm_hudun_00", "offset": -16}, {"slot": "chongji/tw_0001", "offset": -16}, {"slot": "quan_chongji/quanshuaxiao_00016", "offset": -16}, {"slot": "xuanzhuan/linxiangru_skill_xl_00000", "offset": -16}]}]}, "nezha_idle": {"slots": {"huoyan_add/huoyan_67": {"attachment": [{"name": "huoyan_add/huoyan_00066"}, {"time": 0.0667, "name": "huoyan_add/huoyan_00068"}, {"time": 0.1333, "name": "huoyan_add/huoyan_00070"}, {"time": 0.2, "name": "huoyan_add/huoyan_00072"}, {"time": 0.2333, "name": null}, {"time": 0.6667, "name": "huoyan_add/huoyan_00058"}, {"time": 0.7333, "name": "huoyan_add/huoyan_00060"}, {"time": 0.8, "name": "huoyan_add/huoyan_00062"}, {"time": 0.8667, "name": "huoyan_add/huoyan_00064"}, {"time": 0.9, "name": "huoyan_add/huoyan_00066"}, {"time": 0.9667, "name": "huoyan_add/huoyan_00068"}, {"time": 1.0333, "name": "huoyan_add/huoyan_00070"}, {"time": 1.1, "name": "huoyan_add/huoyan_00072"}, {"time": 1.1333, "name": null}, {"time": 1.5667, "name": "huoyan_add/huoyan_00058"}, {"time": 1.6333, "name": "huoyan_add/huoyan_00060"}, {"time": 1.7, "name": "huoyan_add/huoyan_00062"}, {"time": 1.7667, "name": "huoyan_add/huoyan_00064"}, {"time": 1.8, "name": "huoyan_add/huoyan_00066"}, {"time": 1.8667, "name": "huoyan_add/huoyan_00068"}, {"time": 1.9333, "name": "huoyan_add/huoyan_00070"}, {"time": 2, "name": "huoyan_add/huoyan_00072"}, {"time": 2.0333, "name": null}, {"time": 2.4667, "name": "huoyan_add/huoyan_00058"}, {"time": 2.5333, "name": "huoyan_add/huoyan_00060"}, {"time": 2.6, "name": "huoyan_add/huoyan_00062"}, {"time": 2.6667, "name": "huoyan_add/huoyan_00066"}]}, "huoyan_add/huoyan_66": {"attachment": [{"time": 0.2, "name": "huoyan_add/huoyan_00058"}, {"time": 0.2667, "name": "huoyan_add/huoyan_00060"}, {"time": 0.3333, "name": "huoyan_add/huoyan_00062"}, {"time": 0.4, "name": "huoyan_add/huoyan_00064"}, {"time": 0.4333, "name": "huoyan_add/huoyan_00066"}, {"time": 0.5, "name": "huoyan_add/huoyan_00068"}, {"time": 0.5667, "name": "huoyan_add/huoyan_00070"}, {"time": 0.6333, "name": "huoyan_add/huoyan_00072"}, {"time": 0.7, "name": null}, {"time": 1.1, "name": "huoyan_add/huoyan_00058"}, {"time": 1.1667, "name": "huoyan_add/huoyan_00060"}, {"time": 1.2333, "name": "huoyan_add/huoyan_00062"}, {"time": 1.3, "name": "huoyan_add/huoyan_00064"}, {"time": 1.3333, "name": "huoyan_add/huoyan_00066"}, {"time": 1.4, "name": "huoyan_add/huoyan_00068"}, {"time": 1.4667, "name": "huoyan_add/huoyan_00070"}, {"time": 1.5333, "name": "huoyan_add/huoyan_00072"}, {"time": 1.6, "name": null}, {"time": 2, "name": "huoyan_add/huoyan_00058"}, {"time": 2.0667, "name": "huoyan_add/huoyan_00060"}, {"time": 2.1333, "name": "huoyan_add/huoyan_00062"}, {"time": 2.2, "name": "huoyan_add/huoyan_00064"}, {"time": 2.2333, "name": "huoyan_add/huoyan_00066"}, {"time": 2.3, "name": "huoyan_add/huoyan_00068"}, {"time": 2.3667, "name": "huoyan_add/huoyan_00070"}, {"time": 2.4333, "name": "huoyan_add/huoyan_00072"}, {"time": 2.5, "name": null}]}, "huoxing-add/huoxing_1": {"attachment": [{"name": "huoxing-add/huoxing_0007"}]}, "huoyan_add/huoyan_59": {"attachment": [{"name": "huoyan_add/huoyan_00058"}, {"time": 0.0667, "name": "huoyan_add/huoyan_00060"}, {"time": 0.1333, "name": "huoyan_add/huoyan_00062"}, {"time": 0.2, "name": "huoyan_add/huoyan_00064"}, {"time": 0.2667, "name": "huoyan_add/huoyan_00066"}, {"time": 0.3333, "name": "huoyan_add/huoyan_00068"}, {"time": 0.4, "name": "huoyan_add/huoyan_00070"}, {"time": 0.4333, "name": "huoyan_add/huoyan_00072"}, {"time": 0.5, "name": null}, {"time": 0.9, "name": "huoyan_add/huoyan_00058"}, {"time": 0.9667, "name": "huoyan_add/huoyan_00060"}, {"time": 1.0333, "name": "huoyan_add/huoyan_00062"}, {"time": 1.1, "name": "huoyan_add/huoyan_00064"}, {"time": 1.1667, "name": "huoyan_add/huoyan_00066"}, {"time": 1.2333, "name": "huoyan_add/huoyan_00068"}, {"time": 1.3, "name": "huoyan_add/huoyan_00070"}, {"time": 1.3333, "name": "huoyan_add/huoyan_00072"}, {"time": 1.4, "name": null}, {"time": 1.8, "name": "huoyan_add/huoyan_00058"}, {"time": 1.8667, "name": "huoyan_add/huoyan_00060"}, {"time": 1.9333, "name": "huoyan_add/huoyan_00062"}, {"time": 2, "name": "huoyan_add/huoyan_00064"}, {"time": 2.0667, "name": "huoyan_add/huoyan_00066"}, {"time": 2.1333, "name": "huoyan_add/huoyan_00068"}, {"time": 2.2, "name": "huoyan_add/huoyan_00070"}, {"time": 2.2333, "name": "huoyan_add/huoyan_00072"}, {"time": 2.3, "name": null}, {"time": 2.6667, "name": "huoyan_add/huoyan_00058"}]}, "huoyan_add/huoyan_68": {"attachment": [{"name": "huoyan_add/huoyan_00058"}, {"time": 0.0667, "name": "huoyan_add/huoyan_00060"}, {"time": 0.1333, "name": "huoyan_add/huoyan_00062"}, {"time": 0.2, "name": "huoyan_add/huoyan_00064"}, {"time": 0.2667, "name": "huoyan_add/huoyan_00066"}, {"time": 0.3333, "name": "huoyan_add/huoyan_00068"}, {"time": 0.4, "name": "huoyan_add/huoyan_00070"}, {"time": 0.4333, "name": "huoyan_add/huoyan_00072"}, {"time": 0.5, "name": null}, {"time": 0.9, "name": "huoyan_add/huoyan_00058"}, {"time": 0.9667, "name": "huoyan_add/huoyan_00060"}, {"time": 1.0333, "name": "huoyan_add/huoyan_00062"}, {"time": 1.1, "name": "huoyan_add/huoyan_00064"}, {"time": 1.1667, "name": "huoyan_add/huoyan_00066"}, {"time": 1.2333, "name": "huoyan_add/huoyan_00068"}, {"time": 1.3, "name": "huoyan_add/huoyan_00070"}, {"time": 1.3333, "name": "huoyan_add/huoyan_00072"}, {"time": 1.4, "name": null}, {"time": 1.8, "name": "huoyan_add/huoyan_00058"}, {"time": 1.8667, "name": "huoyan_add/huoyan_00060"}, {"time": 1.9333, "name": "huoyan_add/huoyan_00062"}, {"time": 2, "name": "huoyan_add/huoyan_00064"}, {"time": 2.0667, "name": "huoyan_add/huoyan_00066"}, {"time": 2.1333, "name": "huoyan_add/huoyan_00068"}, {"time": 2.2, "name": "huoyan_add/huoyan_00070"}, {"time": 2.2333, "name": "huoyan_add/huoyan_00072"}, {"time": 2.3, "name": null}, {"time": 2.6667, "name": "huoyan_add/huoyan_00058"}]}, "xialuo/xialuo_3": {"attachment": [{"time": 0.0333, "name": "xialuo/xialuo_00000"}, {"time": 0.0667, "name": "xialuo/xialuo_00002"}, {"time": 0.1, "name": "xialuo/xialuo_00004"}, {"time": 0.1333, "name": "xialuo/xialuo_00006"}, {"time": 0.1667, "name": "xialuo/xialuo_00008"}, {"time": 0.2, "name": "xialuo/xialuo_00010"}, {"time": 0.2333, "name": "xialuo/xialuo_00012"}, {"time": 0.2667, "name": "xialuo/xialuo_00014"}, {"time": 0.3, "name": "xialuo/xialuo_00016"}, {"time": 0.3333, "name": "xialuo/xialuo_00018"}, {"time": 0.3667, "name": "xialuo/xialuo_00020"}, {"time": 0.4, "name": "xialuo/xialuo_00022"}, {"time": 0.4333, "name": null}, {"time": 0.9333, "name": "xialuo/xialuo_00000"}, {"time": 0.9667, "name": "xialuo/xialuo_00002"}, {"time": 1, "name": "xialuo/xialuo_00004"}, {"time": 1.0333, "name": "xialuo/xialuo_00006"}, {"time": 1.0667, "name": "xialuo/xialuo_00008"}, {"time": 1.1, "name": "xialuo/xialuo_00010"}, {"time": 1.1333, "name": "xialuo/xialuo_00012"}, {"time": 1.1667, "name": "xialuo/xialuo_00014"}, {"time": 1.2, "name": "xialuo/xialuo_00016"}, {"time": 1.2333, "name": "xialuo/xialuo_00018"}, {"time": 1.2667, "name": "xialuo/xialuo_00020"}, {"time": 1.3, "name": "xialuo/xialuo_00022"}, {"time": 1.3333, "name": null}, {"time": 1.8333, "name": "xialuo/xialuo_00000"}, {"time": 1.8667, "name": "xialuo/xialuo_00002"}, {"time": 1.9, "name": "xialuo/xialuo_00004"}, {"time": 1.9333, "name": "xialuo/xialuo_00006"}, {"time": 1.9667, "name": "xialuo/xialuo_00008"}, {"time": 2, "name": "xialuo/xialuo_00010"}, {"time": 2.0333, "name": "xialuo/xialuo_00012"}, {"time": 2.0667, "name": "xialuo/xialuo_00014"}, {"time": 2.1, "name": "xialuo/xialuo_00016"}, {"time": 2.1333, "name": "xialuo/xialuo_00018"}, {"time": 2.1667, "name": "xialuo/xialuo_00020"}, {"time": 2.2, "name": "xialuo/xialuo_00024"}, {"time": 2.2333, "name": null}]}, "xialuo/xialuo_00000": {"attachment": [{"time": 0.0333, "name": "xialuo/xialuo_00000"}, {"time": 0.0667, "name": "xialuo/xialuo_00002"}, {"time": 0.1, "name": "xialuo/xialuo_00004"}, {"time": 0.1333, "name": "xialuo/xialuo_00006"}, {"time": 0.1667, "name": "xialuo/xialuo_00008"}, {"time": 0.2, "name": "xialuo/xialuo_00010"}, {"time": 0.2333, "name": "xialuo/xialuo_00012"}, {"time": 0.2667, "name": "xialuo/xialuo_00014"}, {"time": 0.3, "name": "xialuo/xialuo_00016"}, {"time": 0.3333, "name": "xialuo/xialuo_00018"}, {"time": 0.3667, "name": "xialuo/xialuo_00020"}, {"time": 0.4, "name": "xialuo/xialuo_00022"}, {"time": 0.4333, "name": null}, {"time": 0.9333, "name": "xialuo/xialuo_00000"}, {"time": 0.9667, "name": "xialuo/xialuo_00002"}, {"time": 1, "name": "xialuo/xialuo_00004"}, {"time": 1.0333, "name": "xialuo/xialuo_00006"}, {"time": 1.0667, "name": "xialuo/xialuo_00008"}, {"time": 1.1, "name": "xialuo/xialuo_00010"}, {"time": 1.1333, "name": "xialuo/xialuo_00012"}, {"time": 1.1667, "name": "xialuo/xialuo_00014"}, {"time": 1.2, "name": "xialuo/xialuo_00016"}, {"time": 1.2333, "name": "xialuo/xialuo_00018"}, {"time": 1.2667, "name": "xialuo/xialuo_00020"}, {"time": 1.3, "name": "xialuo/xialuo_00022"}, {"time": 1.3333, "name": null}, {"time": 1.8333, "name": "xialuo/xialuo_00000"}, {"time": 1.8667, "name": "xialuo/xialuo_00002"}, {"time": 1.9, "name": "xialuo/xialuo_00004"}, {"time": 1.9333, "name": "xialuo/xialuo_00006"}, {"time": 1.9667, "name": "xialuo/xialuo_00008"}, {"time": 2, "name": "xialuo/xialuo_00010"}, {"time": 2.0333, "name": "xialuo/xialuo_00012"}, {"time": 2.0667, "name": "xialuo/xialuo_00014"}, {"time": 2.1, "name": "xialuo/xialuo_00016"}, {"time": 2.1333, "name": "xialuo/xialuo_00018"}, {"time": 2.1667, "name": "xialuo/xialuo_00020"}, {"time": 2.2, "name": "xialuo/xialuo_00024"}, {"time": 2.2333, "name": null}]}, "a27": {"color": [{"color": "ffffffff", "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 0.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffffff"}]}, "huoyan_add/huoyan_62": {"attachment": [{"time": 0.2, "name": "huoyan_add/huoyan_00058"}, {"time": 0.2667, "name": "huoyan_add/huoyan_00060"}, {"time": 0.3333, "name": "huoyan_add/huoyan_00062"}, {"time": 0.4, "name": "huoyan_add/huoyan_00064"}, {"time": 0.4333, "name": "huoyan_add/huoyan_00066"}, {"time": 0.5, "name": "huoyan_add/huoyan_00068"}, {"time": 0.5667, "name": "huoyan_add/huoyan_00070"}, {"time": 0.6333, "name": "huoyan_add/huoyan_00072"}, {"time": 0.7, "name": null}, {"time": 1.1, "name": "huoyan_add/huoyan_00058"}, {"time": 1.1667, "name": "huoyan_add/huoyan_00060"}, {"time": 1.2333, "name": "huoyan_add/huoyan_00062"}, {"time": 1.3, "name": "huoyan_add/huoyan_00064"}, {"time": 1.3333, "name": "huoyan_add/huoyan_00066"}, {"time": 1.4, "name": "huoyan_add/huoyan_00068"}, {"time": 1.4667, "name": "huoyan_add/huoyan_00070"}, {"time": 1.5333, "name": "huoyan_add/huoyan_00072"}, {"time": 1.6, "name": null}, {"time": 2, "name": "huoyan_add/huoyan_00058"}, {"time": 2.0667, "name": "huoyan_add/huoyan_00060"}, {"time": 2.1333, "name": "huoyan_add/huoyan_00062"}, {"time": 2.2, "name": "huoyan_add/huoyan_00064"}, {"time": 2.2333, "name": "huoyan_add/huoyan_00066"}, {"time": 2.3, "name": "huoyan_add/huoyan_00068"}, {"time": 2.3667, "name": "huoyan_add/huoyan_00070"}, {"time": 2.4333, "name": "huoyan_add/huoyan_00072"}, {"time": 2.5, "name": null}]}, "huoyan_add/huoyan_61": {"attachment": [{"name": "huoyan_add/huoyan_00060"}, {"time": 0.3667, "name": null}, {"time": 0.4333, "name": "huoyan_add/huoyan_00058"}, {"time": 0.4667, "name": "huoyan_add/huoyan_00060"}, {"time": 0.5333, "name": "huoyan_add/huoyan_00062"}, {"time": 0.6, "name": "huoyan_add/huoyan_00064"}, {"time": 0.6667, "name": "huoyan_add/huoyan_00066"}, {"time": 0.7333, "name": "huoyan_add/huoyan_00068"}, {"time": 0.8, "name": "huoyan_add/huoyan_00070"}, {"time": 0.8667, "name": "huoyan_add/huoyan_00072"}, {"time": 1.2667, "name": null}, {"time": 1.3333, "name": "huoyan_add/huoyan_00058"}, {"time": 1.3667, "name": "huoyan_add/huoyan_00060"}, {"time": 1.4333, "name": "huoyan_add/huoyan_00062"}, {"time": 1.5, "name": "huoyan_add/huoyan_00064"}, {"time": 1.5667, "name": "huoyan_add/huoyan_00066"}, {"time": 1.6333, "name": "huoyan_add/huoyan_00068"}, {"time": 1.7, "name": "huoyan_add/huoyan_00070"}, {"time": 1.7667, "name": "huoyan_add/huoyan_00072"}, {"time": 2.1667, "name": null}, {"time": 2.2, "name": "huoyan_add/huoyan_00058"}, {"time": 2.2667, "name": "huoyan_add/huoyan_00060"}, {"time": 2.3333, "name": "huoyan_add/huoyan_00062"}, {"time": 2.4, "name": "huoyan_add/huoyan_00064"}, {"time": 2.4667, "name": "huoyan_add/huoyan_00066"}, {"time": 2.5333, "name": "huoyan_add/huoyan_00068"}, {"time": 2.6, "name": "huoyan_add/huoyan_00070"}, {"time": 2.6667, "name": "huoyan_add/huoyan_00060"}]}, "huoyan_add/huoyan_64": {"attachment": [{"name": "huoyan_add/huoyan_00058"}, {"time": 0.0667, "name": "huoyan_add/huoyan_00060"}, {"time": 0.1333, "name": "huoyan_add/huoyan_00062"}, {"time": 0.2, "name": "huoyan_add/huoyan_00064"}, {"time": 0.2667, "name": "huoyan_add/huoyan_00066"}, {"time": 0.3333, "name": "huoyan_add/huoyan_00068"}, {"time": 0.4, "name": "huoyan_add/huoyan_00070"}, {"time": 0.4333, "name": "huoyan_add/huoyan_00072"}, {"time": 0.5, "name": null}, {"time": 0.9, "name": "huoyan_add/huoyan_00058"}, {"time": 0.9667, "name": "huoyan_add/huoyan_00060"}, {"time": 1.0333, "name": "huoyan_add/huoyan_00062"}, {"time": 1.1, "name": "huoyan_add/huoyan_00064"}, {"time": 1.1667, "name": "huoyan_add/huoyan_00066"}, {"time": 1.2333, "name": "huoyan_add/huoyan_00068"}, {"time": 1.3, "name": "huoyan_add/huoyan_00070"}, {"time": 1.3333, "name": "huoyan_add/huoyan_00072"}, {"time": 1.4, "name": null}, {"time": 1.8, "name": "huoyan_add/huoyan_00058"}, {"time": 1.8667, "name": "huoyan_add/huoyan_00060"}, {"time": 1.9333, "name": "huoyan_add/huoyan_00062"}, {"time": 2, "name": "huoyan_add/huoyan_00064"}, {"time": 2.0667, "name": "huoyan_add/huoyan_00066"}, {"time": 2.1333, "name": "huoyan_add/huoyan_00068"}, {"time": 2.2, "name": "huoyan_add/huoyan_00070"}, {"time": 2.2333, "name": "huoyan_add/huoyan_00072"}, {"time": 2.3, "name": null}, {"time": 2.6667, "name": "huoyan_add/huoyan_00058"}]}, "huoyan_add/huoyan_63": {"attachment": [{"name": "huoyan_add/huoyan_00066"}, {"time": 0.0667, "name": "huoyan_add/huoyan_00068"}, {"time": 0.1333, "name": "huoyan_add/huoyan_00070"}, {"time": 0.2, "name": "huoyan_add/huoyan_00072"}, {"time": 0.2333, "name": null}, {"time": 0.6667, "name": "huoyan_add/huoyan_00058"}, {"time": 0.7333, "name": "huoyan_add/huoyan_00060"}, {"time": 0.8, "name": "huoyan_add/huoyan_00062"}, {"time": 0.8667, "name": "huoyan_add/huoyan_00064"}, {"time": 0.9, "name": "huoyan_add/huoyan_00066"}, {"time": 0.9667, "name": "huoyan_add/huoyan_00068"}, {"time": 1.0333, "name": "huoyan_add/huoyan_00070"}, {"time": 1.1, "name": "huoyan_add/huoyan_00072"}, {"time": 1.1333, "name": null}, {"time": 1.5667, "name": "huoyan_add/huoyan_00058"}, {"time": 1.6333, "name": "huoyan_add/huoyan_00060"}, {"time": 1.7, "name": "huoyan_add/huoyan_00062"}, {"time": 1.7667, "name": "huoyan_add/huoyan_00064"}, {"time": 1.8, "name": "huoyan_add/huoyan_00066"}, {"time": 1.8667, "name": "huoyan_add/huoyan_00068"}, {"time": 1.9333, "name": "huoyan_add/huoyan_00070"}, {"time": 2, "name": "huoyan_add/huoyan_00072"}, {"time": 2.0333, "name": null}, {"time": 2.4667, "name": "huoyan_add/huoyan_00058"}, {"time": 2.5333, "name": "huoyan_add/huoyan_00060"}, {"time": 2.6, "name": "huoyan_add/huoyan_00062"}, {"time": 2.6667, "name": "huoyan_add/huoyan_00066"}]}, "xialuo/xialuo_4": {"attachment": [{"name": "xialuo/xialuo_00002"}, {"time": 0.4333, "name": "xialuo/xialuo_00000"}, {"time": 0.4667, "name": "xialuo/xialuo_00002"}, {"time": 0.5, "name": "xialuo/xialuo_00004"}, {"time": 0.5333, "name": "xialuo/xialuo_00006"}, {"time": 0.5667, "name": "xialuo/xialuo_00008"}, {"time": 0.6, "name": "xialuo/xialuo_00010"}, {"time": 0.6333, "name": "xialuo/xialuo_00012"}, {"time": 0.6667, "name": "xialuo/xialuo_00014"}, {"time": 0.7, "name": "xialuo/xialuo_00016"}, {"time": 0.7333, "name": "xialuo/xialuo_00018"}, {"time": 0.7667, "name": "xialuo/xialuo_00020"}, {"time": 0.8, "name": "xialuo/xialuo_00022"}, {"time": 0.8333, "name": "xialuo/xialuo_00024"}, {"time": 0.8667, "name": null}, {"time": 1.3333, "name": "xialuo/xialuo_00000"}, {"time": 1.3667, "name": "xialuo/xialuo_00002"}, {"time": 1.4, "name": "xialuo/xialuo_00004"}, {"time": 1.4333, "name": "xialuo/xialuo_00006"}, {"time": 1.4667, "name": "xialuo/xialuo_00008"}, {"time": 1.5, "name": "xialuo/xialuo_00010"}, {"time": 1.5333, "name": "xialuo/xialuo_00012"}, {"time": 1.5667, "name": "xialuo/xialuo_00014"}, {"time": 1.6, "name": "xialuo/xialuo_00016"}, {"time": 1.6333, "name": "xialuo/xialuo_00018"}, {"time": 1.6667, "name": "xialuo/xialuo_00020"}, {"time": 1.7, "name": "xialuo/xialuo_00022"}, {"time": 1.7333, "name": "xialuo/xialuo_00024"}, {"time": 1.7667, "name": null}, {"time": 2.2333, "name": "xialuo/xialuo_00000"}, {"time": 2.2667, "name": "xialuo/xialuo_00002"}, {"time": 2.3, "name": "xialuo/xialuo_00004"}, {"time": 2.3333, "name": "xialuo/xialuo_00006"}, {"time": 2.3667, "name": "xialuo/xialuo_00008"}, {"time": 2.4, "name": "xialuo/xialuo_00010"}, {"time": 2.4333, "name": "xialuo/xialuo_00012"}, {"time": 2.4667, "name": "xialuo/xialuo_00014"}, {"time": 2.5, "name": "xialuo/xialuo_00016"}, {"time": 2.5333, "name": "xialuo/xialuo_00018"}, {"time": 2.5667, "name": "xialuo/xialuo_00020"}, {"time": 2.6, "name": "xialuo/xialuo_00022"}, {"time": 2.6333, "name": "xialuo/xialuo_00024"}, {"time": 2.6667, "name": "xialuo/xialuo_00002"}]}, "huoyan_add/huoyan_00058": {"attachment": [{"name": "huoyan_add/huoyan_00060"}, {"time": 0.3667, "name": null}, {"time": 0.4333, "name": "huoyan_add/huoyan_00058"}, {"time": 0.4667, "name": "huoyan_add/huoyan_00060"}, {"time": 0.5333, "name": "huoyan_add/huoyan_00062"}, {"time": 0.6, "name": "huoyan_add/huoyan_00064"}, {"time": 0.6667, "name": "huoyan_add/huoyan_00066"}, {"time": 0.7333, "name": "huoyan_add/huoyan_00068"}, {"time": 0.8, "name": "huoyan_add/huoyan_00070"}, {"time": 0.8667, "name": "huoyan_add/huoyan_00072"}, {"time": 1.2667, "name": null}, {"time": 1.3333, "name": "huoyan_add/huoyan_00058"}, {"time": 1.3667, "name": "huoyan_add/huoyan_00060"}, {"time": 1.4333, "name": "huoyan_add/huoyan_00062"}, {"time": 1.5, "name": "huoyan_add/huoyan_00064"}, {"time": 1.5667, "name": "huoyan_add/huoyan_00066"}, {"time": 1.6333, "name": "huoyan_add/huoyan_00068"}, {"time": 1.7, "name": "huoyan_add/huoyan_00070"}, {"time": 1.7667, "name": "huoyan_add/huoyan_00072"}, {"time": 2.1667, "name": null}, {"time": 2.2, "name": "huoyan_add/huoyan_00058"}, {"time": 2.2667, "name": "huoyan_add/huoyan_00060"}, {"time": 2.3333, "name": "huoyan_add/huoyan_00062"}, {"time": 2.4, "name": "huoyan_add/huoyan_00064"}, {"time": 2.4667, "name": "huoyan_add/huoyan_00066"}, {"time": 2.5333, "name": "huoyan_add/huoyan_00068"}, {"time": 2.6, "name": "huoyan_add/huoyan_00070"}, {"time": 2.6667, "name": "huoyan_add/huoyan_00060"}]}, "huoyan_add/huoyan_58": {"attachment": [{"time": 0.2, "name": "huoyan_add/huoyan_00058"}, {"time": 0.2667, "name": "huoyan_add/huoyan_00060"}, {"time": 0.3333, "name": "huoyan_add/huoyan_00062"}, {"time": 0.4, "name": "huoyan_add/huoyan_00064"}, {"time": 0.4333, "name": "huoyan_add/huoyan_00066"}, {"time": 0.5, "name": "huoyan_add/huoyan_00068"}, {"time": 0.5667, "name": "huoyan_add/huoyan_00070"}, {"time": 0.6333, "name": "huoyan_add/huoyan_00072"}, {"time": 0.7, "name": null}, {"time": 1.1, "name": "huoyan_add/huoyan_00058"}, {"time": 1.1667, "name": "huoyan_add/huoyan_00060"}, {"time": 1.2333, "name": "huoyan_add/huoyan_00062"}, {"time": 1.3, "name": "huoyan_add/huoyan_00064"}, {"time": 1.3333, "name": "huoyan_add/huoyan_00066"}, {"time": 1.4, "name": "huoyan_add/huoyan_00068"}, {"time": 1.4667, "name": "huoyan_add/huoyan_00070"}, {"time": 1.5333, "name": "huoyan_add/huoyan_00072"}, {"time": 1.6, "name": null}, {"time": 2, "name": "huoyan_add/huoyan_00058"}, {"time": 2.0667, "name": "huoyan_add/huoyan_00060"}, {"time": 2.1333, "name": "huoyan_add/huoyan_00062"}, {"time": 2.2, "name": "huoyan_add/huoyan_00064"}, {"time": 2.2333, "name": "huoyan_add/huoyan_00066"}, {"time": 2.3, "name": "huoyan_add/huoyan_00068"}, {"time": 2.3667, "name": "huoyan_add/huoyan_00070"}, {"time": 2.4333, "name": "huoyan_add/huoyan_00072"}, {"time": 2.5, "name": null}]}, "xialuo/xialuo_1": {"attachment": [{"time": 0.0333, "name": "xialuo/xialuo_00000"}, {"time": 0.0667, "name": "xialuo/xialuo_00002"}, {"time": 0.1, "name": "xialuo/xialuo_00004"}, {"time": 0.1333, "name": "xialuo/xialuo_00006"}, {"time": 0.1667, "name": "xialuo/xialuo_00008"}, {"time": 0.2, "name": "xialuo/xialuo_00010"}, {"time": 0.2333, "name": "xialuo/xialuo_00012"}, {"time": 0.2667, "name": "xialuo/xialuo_00014"}, {"time": 0.3, "name": "xialuo/xialuo_00016"}, {"time": 0.3333, "name": "xialuo/xialuo_00018"}, {"time": 0.3667, "name": "xialuo/xialuo_00020"}, {"time": 0.4, "name": "xialuo/xialuo_00022"}, {"time": 0.4333, "name": null}, {"time": 0.9333, "name": "xialuo/xialuo_00000"}, {"time": 0.9667, "name": "xialuo/xialuo_00002"}, {"time": 1, "name": "xialuo/xialuo_00004"}, {"time": 1.0333, "name": "xialuo/xialuo_00006"}, {"time": 1.0667, "name": "xialuo/xialuo_00008"}, {"time": 1.1, "name": "xialuo/xialuo_00010"}, {"time": 1.1333, "name": "xialuo/xialuo_00012"}, {"time": 1.1667, "name": "xialuo/xialuo_00014"}, {"time": 1.2, "name": "xialuo/xialuo_00016"}, {"time": 1.2333, "name": "xialuo/xialuo_00018"}, {"time": 1.2667, "name": "xialuo/xialuo_00020"}, {"time": 1.3, "name": "xialuo/xialuo_00022"}, {"time": 1.3333, "name": null}, {"time": 1.8333, "name": "xialuo/xialuo_00000"}, {"time": 1.8667, "name": "xialuo/xialuo_00002"}, {"time": 1.9, "name": "xialuo/xialuo_00004"}, {"time": 1.9333, "name": "xialuo/xialuo_00006"}, {"time": 1.9667, "name": "xialuo/xialuo_00008"}, {"time": 2, "name": "xialuo/xialuo_00010"}, {"time": 2.0333, "name": "xialuo/xialuo_00012"}, {"time": 2.0667, "name": "xialuo/xialuo_00014"}, {"time": 2.1, "name": "xialuo/xialuo_00016"}, {"time": 2.1333, "name": "xialuo/xialuo_00018"}, {"time": 2.1667, "name": "xialuo/xialuo_00020"}, {"time": 2.2, "name": "xialuo/xialuo_00024"}, {"time": 2.2333, "name": null}]}, "nq_add_02/nq_b_1": {"attachment": [{"name": "nq_add_02/nq_b_08"}]}, "xialuo/xialuo_0": {"attachment": [{"name": "xialuo/xialuo_00002"}, {"time": 0.4333, "name": "xialuo/xialuo_00000"}, {"time": 0.4667, "name": "xialuo/xialuo_00002"}, {"time": 0.5, "name": "xialuo/xialuo_00004"}, {"time": 0.5333, "name": "xialuo/xialuo_00006"}, {"time": 0.5667, "name": "xialuo/xialuo_00008"}, {"time": 0.6, "name": "xialuo/xialuo_00010"}, {"time": 0.6333, "name": "xialuo/xialuo_00012"}, {"time": 0.6667, "name": "xialuo/xialuo_00014"}, {"time": 0.7, "name": "xialuo/xialuo_00016"}, {"time": 0.7333, "name": "xialuo/xialuo_00018"}, {"time": 0.7667, "name": "xialuo/xialuo_00020"}, {"time": 0.8, "name": "xialuo/xialuo_00022"}, {"time": 0.8333, "name": "xialuo/xialuo_00024"}, {"time": 0.8667, "name": null}, {"time": 1.3333, "name": "xialuo/xialuo_00000"}, {"time": 1.3667, "name": "xialuo/xialuo_00002"}, {"time": 1.4, "name": "xialuo/xialuo_00004"}, {"time": 1.4333, "name": "xialuo/xialuo_00006"}, {"time": 1.4667, "name": "xialuo/xialuo_00008"}, {"time": 1.5, "name": "xialuo/xialuo_00010"}, {"time": 1.5333, "name": "xialuo/xialuo_00012"}, {"time": 1.5667, "name": "xialuo/xialuo_00014"}, {"time": 1.6, "name": "xialuo/xialuo_00016"}, {"time": 1.6333, "name": "xialuo/xialuo_00018"}, {"time": 1.6667, "name": "xialuo/xialuo_00020"}, {"time": 1.7, "name": "xialuo/xialuo_00022"}, {"time": 1.7333, "name": "xialuo/xialuo_00024"}, {"time": 1.7667, "name": null}, {"time": 2.2333, "name": "xialuo/xialuo_00000"}, {"time": 2.2667, "name": "xialuo/xialuo_00002"}, {"time": 2.3, "name": "xialuo/xialuo_00004"}, {"time": 2.3333, "name": "xialuo/xialuo_00006"}, {"time": 2.3667, "name": "xialuo/xialuo_00008"}, {"time": 2.4, "name": "xialuo/xialuo_00010"}, {"time": 2.4333, "name": "xialuo/xialuo_00012"}, {"time": 2.4667, "name": "xialuo/xialuo_00014"}, {"time": 2.5, "name": "xialuo/xialuo_00016"}, {"time": 2.5333, "name": "xialuo/xialuo_00018"}, {"time": 2.5667, "name": "xialuo/xialuo_00020"}, {"time": 2.6, "name": "xialuo/xialuo_00022"}, {"time": 2.6333, "name": "xialuo/xialuo_00024"}, {"time": 2.6667, "name": "xialuo/xialuo_00002"}]}, "huoxing-add/huoxing_2": {"attachment": [{"name": "huoxing-add/huoxing_0011"}]}, "xialuo/xialuo_2": {"attachment": [{"name": "xialuo/xialuo_00002"}, {"time": 0.4333, "name": "xialuo/xialuo_00000"}, {"time": 0.4667, "name": "xialuo/xialuo_00002"}, {"time": 0.5, "name": "xialuo/xialuo_00004"}, {"time": 0.5333, "name": "xialuo/xialuo_00006"}, {"time": 0.5667, "name": "xialuo/xialuo_00008"}, {"time": 0.6, "name": "xialuo/xialuo_00010"}, {"time": 0.6333, "name": "xialuo/xialuo_00012"}, {"time": 0.6667, "name": "xialuo/xialuo_00014"}, {"time": 0.7, "name": "xialuo/xialuo_00016"}, {"time": 0.7333, "name": "xialuo/xialuo_00018"}, {"time": 0.7667, "name": "xialuo/xialuo_00020"}, {"time": 0.8, "name": "xialuo/xialuo_00022"}, {"time": 0.8333, "name": "xialuo/xialuo_00024"}, {"time": 0.8667, "name": null}, {"time": 1.3333, "name": "xialuo/xialuo_00000"}, {"time": 1.3667, "name": "xialuo/xialuo_00002"}, {"time": 1.4, "name": "xialuo/xialuo_00004"}, {"time": 1.4333, "name": "xialuo/xialuo_00006"}, {"time": 1.4667, "name": "xialuo/xialuo_00008"}, {"time": 1.5, "name": "xialuo/xialuo_00010"}, {"time": 1.5333, "name": "xialuo/xialuo_00012"}, {"time": 1.5667, "name": "xialuo/xialuo_00014"}, {"time": 1.6, "name": "xialuo/xialuo_00016"}, {"time": 1.6333, "name": "xialuo/xialuo_00018"}, {"time": 1.6667, "name": "xialuo/xialuo_00020"}, {"time": 1.7, "name": "xialuo/xialuo_00022"}, {"time": 1.7333, "name": "xialuo/xialuo_00024"}, {"time": 1.7667, "name": null}, {"time": 2.2333, "name": "xialuo/xialuo_00000"}, {"time": 2.2667, "name": "xialuo/xialuo_00002"}, {"time": 2.3, "name": "xialuo/xialuo_00004"}, {"time": 2.3333, "name": "xialuo/xialuo_00006"}, {"time": 2.3667, "name": "xialuo/xialuo_00008"}, {"time": 2.4, "name": "xialuo/xialuo_00010"}, {"time": 2.4333, "name": "xialuo/xialuo_00012"}, {"time": 2.4667, "name": "xialuo/xialuo_00014"}, {"time": 2.5, "name": "xialuo/xialuo_00016"}, {"time": 2.5333, "name": "xialuo/xialuo_00018"}, {"time": 2.5667, "name": "xialuo/xialuo_00020"}, {"time": 2.6, "name": "xialuo/xialuo_00022"}, {"time": 2.6333, "name": "xialuo/xialuo_00024"}, {"time": 2.6667, "name": "xialuo/xialuo_00002"}]}, "huoyan_add/huoyan_60": {"attachment": [{"name": "huoyan_add/huoyan_00066"}, {"time": 0.0667, "name": "huoyan_add/huoyan_00068"}, {"time": 0.1333, "name": "huoyan_add/huoyan_00070"}, {"time": 0.2, "name": "huoyan_add/huoyan_00072"}, {"time": 0.2333, "name": null}, {"time": 0.6667, "name": "huoyan_add/huoyan_00058"}, {"time": 0.7333, "name": "huoyan_add/huoyan_00060"}, {"time": 0.8, "name": "huoyan_add/huoyan_00062"}, {"time": 0.8667, "name": "huoyan_add/huoyan_00064"}, {"time": 0.9, "name": "huoyan_add/huoyan_00066"}, {"time": 0.9667, "name": "huoyan_add/huoyan_00068"}, {"time": 1.0333, "name": "huoyan_add/huoyan_00070"}, {"time": 1.1, "name": "huoyan_add/huoyan_00072"}, {"time": 1.1333, "name": null}, {"time": 1.5667, "name": "huoyan_add/huoyan_00058"}, {"time": 1.6333, "name": "huoyan_add/huoyan_00060"}, {"time": 1.7, "name": "huoyan_add/huoyan_00062"}, {"time": 1.7667, "name": "huoyan_add/huoyan_00064"}, {"time": 1.8, "name": "huoyan_add/huoyan_00066"}, {"time": 1.8667, "name": "huoyan_add/huoyan_00068"}, {"time": 1.9333, "name": "huoyan_add/huoyan_00070"}, {"time": 2, "name": "huoyan_add/huoyan_00072"}, {"time": 2.0333, "name": null}, {"time": 2.4667, "name": "huoyan_add/huoyan_00058"}, {"time": 2.5333, "name": "huoyan_add/huoyan_00060"}, {"time": 2.6, "name": "huoyan_add/huoyan_00062"}, {"time": 2.6667, "name": "huoyan_add/huoyan_00066"}]}, "a26": {"color": [{"color": "ffffffff", "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 0.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffffff"}]}, "a28": {"color": [{"color": "ffffffff", "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 0.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffffff"}]}, "nq_add_02/nq_b_01": {"attachment": [{"name": "nq_add_02/nq_b_08"}]}, "huoyan_add/huoyan_65": {"attachment": [{"name": "huoyan_add/huoyan_00060"}, {"time": 0.3667, "name": null}, {"time": 0.4333, "name": "huoyan_add/huoyan_00058"}, {"time": 0.4667, "name": "huoyan_add/huoyan_00060"}, {"time": 0.5333, "name": "huoyan_add/huoyan_00062"}, {"time": 0.6, "name": "huoyan_add/huoyan_00064"}, {"time": 0.6667, "name": "huoyan_add/huoyan_00066"}, {"time": 0.7333, "name": "huoyan_add/huoyan_00068"}, {"time": 0.8, "name": "huoyan_add/huoyan_00070"}, {"time": 0.8667, "name": "huoyan_add/huoyan_00072"}, {"time": 1.2667, "name": null}, {"time": 1.3333, "name": "huoyan_add/huoyan_00058"}, {"time": 1.3667, "name": "huoyan_add/huoyan_00060"}, {"time": 1.4333, "name": "huoyan_add/huoyan_00062"}, {"time": 1.5, "name": "huoyan_add/huoyan_00064"}, {"time": 1.5667, "name": "huoyan_add/huoyan_00066"}, {"time": 1.6333, "name": "huoyan_add/huoyan_00068"}, {"time": 1.7, "name": "huoyan_add/huoyan_00070"}, {"time": 1.7667, "name": "huoyan_add/huoyan_00072"}, {"time": 2.1667, "name": null}, {"time": 2.2, "name": "huoyan_add/huoyan_00058"}, {"time": 2.2667, "name": "huoyan_add/huoyan_00060"}, {"time": 2.3333, "name": "huoyan_add/huoyan_00062"}, {"time": 2.4, "name": "huoyan_add/huoyan_00064"}, {"time": 2.4667, "name": "huoyan_add/huoyan_00066"}, {"time": 2.5333, "name": "huoyan_add/huoyan_00068"}, {"time": 2.6, "name": "huoyan_add/huoyan_00070"}, {"time": 2.6667, "name": "huoyan_add/huoyan_00060"}]}, "a29": {"color": [{"color": "ffffffff", "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 0.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffffff"}]}}, "bones": {"nzbone2": {"translate": [{"y": 23.32, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": 41.35, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 23.32, "curve": 0.25, "c3": 0.75}, {"time": 2, "y": 41.35, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "y": 23.32}]}, "nzbone4": {"translate": [{"x": 0.04, "y": 0.8, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.23, "y": 4.36, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "x": 0.04, "y": 0.8, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 0.23, "y": 4.36, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 0.04, "y": 0.8}]}, "nzbone5": {"translate": [{"x": 2.48, "y": -0.28, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 4.96, "y": -0.55, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 2.48, "y": -0.28, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 4.96, "y": -0.55, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 2.48, "y": -0.28}]}, "nzbone6": {"rotate": [{"angle": 4.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": 2.35, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 5.73, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 4.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "angle": 2.35, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 5.73, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 4.04}]}, "nzbone8": {"translate": [{"x": 3.12, "y": 0.05, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 6.24, "y": 0.1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 3.12, "y": 0.05, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 6.24, "y": 0.1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 3.12, "y": 0.05}]}, "nzbone18": {"translate": [{"x": 1.26, "y": -0.16, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 6.81, "y": -0.86, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "x": 1.26, "y": -0.16, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 6.81, "y": -0.86, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 1.26, "y": -0.16}]}, "nzbone67": {"rotate": [{"angle": 5.43, "curve": 0.302, "c2": 0.23, "c3": 0.667, "c4": 0.67}, {"time": 0.3, "angle": 15.6, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.5333, "angle": 14.59, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 17.91, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": 17.47, "curve": 0.302, "c2": 0.23, "c3": 0.667, "c4": 0.67}, {"time": 1.6333, "angle": 15.6, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.8667, "angle": 14.59, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "angle": 17.91, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "angle": 5.43}], "translate": [{"x": 0.24, "y": 8.44}]}, "nzbone68": {"rotate": [{"angle": -12.81, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.1, "angle": -52.32, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3, "angle": -53.12, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.7667, "angle": -55.63, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 1.3333, "angle": -52.59, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 1.4333, "angle": -52.32, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.6333, "angle": -53.12, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 2.1, "angle": -55.63, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 2.6667, "angle": -12.81}]}, "nzbone70": {"rotate": [{"angle": -14.36, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 2.42, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": 0.74, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.5667, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": 2.42, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "angle": -14.36}], "translate": [{"x": 0.25, "y": 8.59}]}, "nzbone71": {"rotate": [{"angle": 20.05, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 2.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": 1.84, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": 2.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "angle": 20.05}]}, "nzbone78": {"translate": [{"y": 29.7, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "y": 25.48, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "y": 39.32, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "y": 29.7, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.5667, "y": 25.48, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "y": 39.32, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "y": 29.7}]}, "nzbone80": {"translate": [{"y": 35.97, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4667, "y": 25.48, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "y": 39.32, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "y": 35.97, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.8, "y": 25.48, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "y": 39.32, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "y": 35.97}]}, "nzbone13": {"rotate": [{"angle": 6.83, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "angle": 17.25, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -11.06, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "angle": 6.83, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.6, "angle": 17.25, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -11.06, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": 6.83}], "translate": [{"x": 5.28, "y": 17.59, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "x": 8.35, "y": 27.83, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "x": 5.28, "y": 17.59, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.6, "x": 8.35, "y": 27.83, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "x": 5.28, "y": 17.59}]}, "nzbone10": {"rotate": [{"angle": 5.25, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 17.25, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": 5.25, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.5667, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": 17.25, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "angle": 5.25}], "translate": [{"x": 0.97, "y": 0.87, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 3.17, "y": 2.84, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "x": 0.97, "y": 0.87, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.5667, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "x": 3.17, "y": 2.84, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "x": 0.97, "y": 0.87}]}, "nzbone11": {"rotate": [{"angle": 13.07, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 17.25, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": 13.07, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": 17.25, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "angle": 13.07}], "translate": [{"x": 1.34, "y": 11.66, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": 1.77, "y": 15.38, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "x": 1.34, "y": 11.66, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "x": 1.77, "y": 15.38, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "x": 1.34, "y": 11.66}]}, "nzbone12": {"rotate": [{"angle": 41.61, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0333, "angle": 42.33, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 1.3333, "angle": 41.61, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 1.3667, "angle": 42.33, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 2.6667, "angle": 41.61}], "translate": [{"x": -0.68, "y": 14.85, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0333, "x": -0.69, "y": 15.11, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 1.3333, "x": -0.68, "y": 14.85, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 1.3667, "x": -0.69, "y": 15.11, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 2.6667, "x": -0.68, "y": 14.85}]}, "nzbone14": {"translate": [{"x": 1.55, "y": 1.26, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": 6.39, "y": 5.2, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3, "x": 1.95, "y": 1.58, "curve": 0.337, "c2": 0.35, "c3": 0.671, "c4": 0.68}, {"time": 1.3333, "x": 1.55, "y": 1.26, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.5333, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "x": 6.39, "y": 5.2, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6333, "x": 1.95, "y": 1.58, "curve": 0.337, "c2": 0.35, "c3": 0.671, "c4": 0.68}, {"time": 2.6667, "x": 1.55, "y": 1.26}]}, "nzbone15": {"translate": [{"x": 4.29, "y": 5.09, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "x": 6.16, "y": 7.31, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3, "x": 4.67, "y": 5.54, "curve": 0.329, "c2": 0.32, "c3": 0.663, "c4": 0.65}, {"time": 1.3333, "x": 4.29, "y": 5.09, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "x": 6.16, "y": 7.31, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6333, "x": 4.67, "y": 5.54, "curve": 0.329, "c2": 0.32, "c3": 0.663, "c4": 0.65}, {"time": 2.6667, "x": 4.29, "y": 5.09}]}, "nzbone16": {"translate": [{"x": 5.97, "y": 4.73, "curve": 0.312, "c2": 0.27, "c3": 0.67, "c4": 0.68}, {"time": 0.2667, "x": 2.23, "y": 1.77, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 7.32, "y": 5.8, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "x": 5.97, "y": 4.73, "curve": 0.312, "c2": 0.27, "c3": 0.67, "c4": 0.68}, {"time": 1.6, "x": 2.23, "y": 1.77, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": 7.32, "y": 5.8, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": 5.97, "y": 4.73}]}, "nzbone17": {"translate": [{"x": 9.72, "y": 6.94, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0667, "x": 10.15, "y": 7.25, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2667, "x": 7.69, "y": 5.5, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.7333, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 1.3333, "x": 9.72, "y": 6.94, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 1.4, "x": 10.15, "y": 7.25, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.6, "x": 7.69, "y": 5.5, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 2.0667, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2.6667, "x": 9.72, "y": 6.94}]}, "nzbone57": {"translate": [{"x": -8.11, "y": 19.37, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4667, "y": -3.51, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": -10.7, "y": 26.69, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "x": -8.11, "y": 19.37, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.8, "y": -3.51, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "x": -10.7, "y": 26.69, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "x": -8.11, "y": 19.37}], "scale": [{"x": 0.848, "y": 0.848, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": 0.8, "y": 0.8, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "x": 0.848, "y": 0.848, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "x": 0.8, "y": 0.8, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "x": 0.848, "y": 0.848}]}, "nzbone58": {"translate": [{"x": -3.99, "y": 28.78, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0333, "x": -4.06, "y": 29.34, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "y": -3.51, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 1.3333, "x": -3.99, "y": 28.78, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 1.3667, "x": -4.06, "y": 29.34, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "y": -3.51, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 2.6667, "x": -3.99, "y": 28.78}], "scale": [{"x": 0.803, "y": 0.803, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0333, "x": 0.8, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 1.3333, "x": 0.803, "y": 0.803, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 1.3667, "x": 0.8, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 2.6667, "x": 0.803, "y": 0.803}]}, "nzbone59": {"translate": [{"x": 5.48, "y": 18.14, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "x": 8.66, "y": 30.75, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "y": -3.51, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "x": 5.48, "y": 18.14, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.6, "x": 8.66, "y": 30.75, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "y": -3.51, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "x": 5.48, "y": 18.14}], "scale": [{"x": 0.874, "y": 0.874, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "x": 0.8, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "x": 0.874, "y": 0.874, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.6, "x": 0.8, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "x": 0.874, "y": 0.874}]}, "nzbone56": {"translate": [{"x": -2.86, "y": 0.7, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5, "x": -15.52, "y": 19.34, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "y": -3.51, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "x": -2.86, "y": 0.7, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.8333, "x": -15.52, "y": 19.34, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "y": -3.51, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": -2.86, "y": 0.7}], "scale": [{"x": 0.963, "y": 0.963, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5, "x": 0.8, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "x": 0.963, "y": 0.963, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.8333, "x": 0.8, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": 0.963, "y": 0.963}]}, "nzbone55": {"translate": [{"x": -0.27, "y": -2.1, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0667, "y": -3.51, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": -6.33, "y": 29.63, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 1.3333, "x": -0.27, "y": -2.1, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 1.4, "y": -3.51, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "x": -6.33, "y": 29.63, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2.6667, "x": -0.27, "y": -2.1}], "scale": [{"x": 0.991, "y": 0.991, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": 0.8, "y": 0.8, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 1.3333, "x": 0.991, "y": 0.991, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "x": 0.8, "y": 0.8, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2.6667, "x": 0.991, "y": 0.991}]}, "nzbone54": {"translate": [{"x": 1.22, "y": 10.52, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.3, "y": -3.51, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "x": 2.81, "y": 28.82, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 1.3333, "x": 1.22, "y": 10.52, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 1.6333, "y": -3.51, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "x": 2.81, "y": 28.82, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 2.6667, "x": 1.22, "y": 10.52}], "scale": [{"x": 0.913, "y": 0.913, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "x": 0.8, "y": 0.8, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 1.3333, "x": 0.913, "y": 0.913, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 1.6333, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "x": 0.8, "y": 0.8, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 2.6667, "x": 0.913, "y": 0.913}]}, "nzbone60": {"translate": [{"x": 9.17, "y": 17.28, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "y": -3.51, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": 10.54, "y": 20.39, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "x": 9.17, "y": 17.28, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.8667, "y": -3.51, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "x": 10.54, "y": 20.39, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "x": 9.17, "y": 17.28}], "scale": [{"x": 0.826, "y": 0.826, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": 0.8, "y": 0.8, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "x": 0.826, "y": 0.826, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.8667, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "x": 0.8, "y": 0.8, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "x": 0.826, "y": 0.826}]}, "nzbone61": {"translate": [{"x": -12.89, "y": 13.89, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.1, "x": -14.06, "y": 15.47, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "y": -3.51, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 1.3333, "x": -12.89, "y": 13.89, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 1.4333, "x": -14.06, "y": 15.47, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "y": -3.51, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 2.6667, "x": -12.89, "y": 13.89}], "scale": [{"x": 0.817, "y": 0.817, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.1, "x": 0.8, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 1.3333, "x": 0.817, "y": 0.817, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 1.4333, "x": 0.8, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 2.6667, "x": 0.817, "y": 0.817}]}, "nzbone62": {"translate": [{"x": -7.38, "y": 4.22, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "x": -14.76, "y": 11.95, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": -3.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": -7.38, "y": 4.22, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "x": -14.76, "y": 11.95, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "y": -3.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": -7.38, "y": 4.22}], "scale": [{"x": 0.9, "y": 0.9, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "x": 0.8, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 0.9, "y": 0.9, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "x": 0.8, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 0.9, "y": 0.9}]}, "nzbone63": {"translate": [{"x": 0.23, "y": -1.47, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 0.5667, "x": 2.81, "y": 21.09, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "y": -3.51, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 1.3333, "x": 0.23, "y": -1.47, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 1.9, "x": 2.81, "y": 21.09, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "y": -3.51, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 2.6667, "x": 0.23, "y": -1.47}], "scale": [{"x": 0.983, "y": 0.983, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 0.5667, "x": 0.8, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 1.3333, "x": 0.983, "y": 0.983, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 1.9, "x": 0.8, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 2.6667, "x": 0.983, "y": 0.983}]}, "nzbone64": {"translate": [{"x": 1.46, "y": -1.5, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "y": -3.51, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": 11.25, "y": 11.95, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.3333, "x": 1.46, "y": -1.5, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.4667, "y": -3.51, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "x": 11.25, "y": 11.95, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.6667, "x": 1.46, "y": -1.5}], "scale": [{"x": 0.974, "y": 0.974, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": 0.8, "y": 0.8, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.3333, "x": 0.974, "y": 0.974, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.4667, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "x": 0.8, "y": 0.8, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.6667, "x": 0.974, "y": 0.974}]}, "nzbone53": {"translate": [{"x": 0.8, "y": 13.2, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 0.3667, "y": -3.51, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "x": 1.41, "y": 26.01, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 1.3333, "x": 0.8, "y": 13.2, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 1.7, "y": -3.51, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "x": 1.41, "y": 26.01, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 2.6667, "x": 0.8, "y": 13.2}], "scale": [{"x": 0.887, "y": 0.887, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "x": 0.8, "y": 0.8, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 1.3333, "x": 0.887, "y": 0.887, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "x": 0.8, "y": 0.8, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 2.6667, "x": 0.887, "y": 0.887}]}, "nzbone52": {"translate": [{"x": -1.07, "y": 6.55, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "y": -3.51, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": -3.51, "y": 29.53, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "x": -1.07, "y": 6.55, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.5667, "y": -3.51, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "x": -3.51, "y": 29.53, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "x": -1.07, "y": 6.55}], "scale": [{"x": 0.939, "y": 0.939, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 0.8, "y": 0.8, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "x": 0.939, "y": 0.939, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.5667, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "x": 0.8, "y": 0.8, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "x": 0.939, "y": 0.939}]}, "nzbone48": {"rotate": [{"angle": -13.76, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4667, "angle": 7.58, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -20.58, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": -13.76, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.8, "angle": 7.58, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": -20.58, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "angle": -13.76}]}, "nzbone47": {"rotate": [{"angle": -0.85, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": 3.88, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -11.64, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": -0.85, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.5667, "angle": 3.88, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": -11.64, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "angle": -0.85}]}, "nzbone45": {"rotate": [{"angle": 0.37, "curve": 0.337, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 0.1, "angle": -2.29, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.3333, "angle": -6.47, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 7.25, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 0.37, "curve": 0.337, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 1.4333, "angle": -2.29, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.6667, "angle": -6.47, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 7.25, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 0.37}]}, "nzbone46": {"rotate": [{"angle": 17.19, "curve": 0.311, "c2": 0.25, "c3": 0.648, "c4": 0.59}, {"time": 0.1, "angle": 11.54, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.5667, "angle": -14.79, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 19.95, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 1.3333, "angle": 17.19, "curve": 0.311, "c2": 0.25, "c3": 0.648, "c4": 0.59}, {"time": 1.4333, "angle": 11.54, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.9, "angle": -14.79, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": 19.95, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 2.6667, "angle": 17.19}]}, "nzbone40": {"rotate": [{"angle": 8.17, "curve": 0.262, "c2": 0.08, "c3": 0.652, "c4": 0.62}, {"time": 0.4, "angle": -0.66, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.6333, "angle": -4.59, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": 8.32, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 1.3333, "angle": 8.17, "curve": 0.262, "c2": 0.08, "c3": 0.652, "c4": 0.62}, {"time": 1.7333, "angle": -0.66, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.9667, "angle": -4.59, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": 8.32, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 2.6667, "angle": 8.17}]}, "nzbone41": {"rotate": [{"angle": 7.28, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": 16.07, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.4, "angle": 7.28, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.8667, "angle": -20.22, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "angle": 7.28, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.5333, "angle": 16.07, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.7333, "angle": 7.28, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 2.2, "angle": -20.22, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.6667, "angle": 7.28}]}, "nzbone49": {"rotate": [{"angle": 4.17, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 0.2333, "angle": -2.31, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.4667, "angle": -6.67, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 7.64, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": 4.17, "curve": 0.32, "c2": 0.29, "c3": 0.672, "c4": 0.68}, {"time": 1.5667, "angle": -2.31, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.8, "angle": -6.67, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": 7.64, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "angle": 4.17}]}, "nzbone50": {"rotate": [{"angle": 17.28, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0333, "angle": 17.87, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2333, "angle": 9.45, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.7, "angle": -16.89, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 1.3333, "angle": 17.28, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 1.3667, "angle": 17.87, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.5667, "angle": 9.45, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 2.0333, "angle": -16.89, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 2.6667, "angle": 17.28}]}, "nzbone38": {"rotate": [{"angle": -8.47, "curve": 0.312, "c2": 0.27, "c3": 0.67, "c4": 0.68}, {"time": 0.2667, "angle": -0.21, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.5, "angle": 4.71, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -11.46, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "angle": -8.47, "curve": 0.312, "c2": 0.27, "c3": 0.67, "c4": 0.68}, {"time": 1.6, "angle": -0.21, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.8333, "angle": 4.71, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -11.46, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -8.47}]}, "nzbone39": {"rotate": [{"angle": -15.87, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0667, "angle": -17.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.2667, "angle": -8.56, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.7333, "angle": 19.17, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 1.3333, "angle": -15.87, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 1.4, "angle": -17.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.6, "angle": -8.56, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 2.0667, "angle": 19.17, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2.6667, "angle": -15.87}]}, "nzbone42": {"rotate": [{"angle": -2.21, "curve": 0.337, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 0.1, "angle": -5.32, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.3333, "angle": -10.2, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6, "angle": -4.31, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1, "angle": 5.8, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": -2.21, "curve": 0.337, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 1.4333, "angle": -5.32, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.6667, "angle": -10.2, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.9333, "angle": -4.31, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.3333, "angle": 5.8, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -2.21}]}, "nzbone43": {"rotate": [{"angle": 9.5, "curve": 0.311, "c2": 0.25, "c3": 0.648, "c4": 0.59}, {"time": 0.1, "angle": 4.56, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.5667, "angle": -18.44, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 0.6, "angle": -17.92, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 1.2333, "angle": 11.92, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 1.3333, "angle": 9.5, "curve": 0.311, "c2": 0.25, "c3": 0.648, "c4": 0.59}, {"time": 1.4333, "angle": 4.56, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.9, "angle": -18.44, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 1.9333, "angle": -17.92, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 2.5667, "angle": 11.92, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 2.6667, "angle": 9.5}]}, "nzbone26": {"rotate": [{"angle": 1.18, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": 2.69, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -2.26, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": 1.18, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.5667, "angle": 2.69, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": -2.26, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "angle": 1.18}]}, "nzbone27": {"rotate": [{"angle": -1.06, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4667, "angle": 2.69, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -2.26, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": -1.06, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.8, "angle": 2.69, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": -2.26, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "angle": -1.06}]}, "nzbone28": {"rotate": [{"angle": -2.18, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0333, "angle": -2.26, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 2.69, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 1.3333, "angle": -2.18, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 1.3667, "angle": -2.26, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": 2.69, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 2.6667, "angle": -2.18}]}, "nzbone31": {"rotate": [{"angle": 4.33, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0333, "angle": 4.46, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -3.31, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 1.3333, "angle": 4.33, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 1.3667, "angle": 4.46, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": -3.31, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 2.6667, "angle": 4.33}]}, "nzbone30": {"rotate": [{"angle": 2.58, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4667, "angle": -3.31, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 4.46, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": 2.58, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.8, "angle": -3.31, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": 4.46, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "angle": 2.58}]}, "nzbone29": {"rotate": [{"angle": -0.95, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": -3.31, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 4.46, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": -0.95, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.5667, "angle": -3.31, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": 4.46, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "angle": -0.95}]}, "nzbone34": {"rotate": [{"angle": 3.71, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0333, "angle": 3.81, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -2.14, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 1.3333, "angle": 3.71, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 1.3667, "angle": 3.81, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": -2.14, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 2.6667, "angle": 3.71}]}, "nzbone33": {"rotate": [{"angle": 2.37, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4667, "angle": -2.14, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 3.81, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": 2.37, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.8, "angle": -2.14, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": 3.81, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "angle": 2.37}]}, "nzbone32": {"rotate": [{"angle": -0.33, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": -2.14, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 3.81, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": -0.33, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.5667, "angle": -2.14, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": 3.81, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "angle": -0.33}]}, "nzbone35": {"rotate": [{"angle": -0.29, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": 1.68, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -4.8, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "angle": -0.29, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.5667, "angle": 1.68, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": -4.8, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "angle": -0.29}]}, "nzbone36": {"rotate": [{"angle": -3.23, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4667, "angle": 1.68, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -4.8, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": -3.23, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.8, "angle": 1.68, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": -4.8, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "angle": -3.23}]}, "nzbone37": {"rotate": [{"angle": -4.69, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0333, "angle": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 1.68, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 1.3333, "angle": -4.69, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 1.3667, "angle": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": 1.68, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 2.6667, "angle": -4.69}]}, "nzbone23": {"rotate": [{"angle": -3.54, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "angle": -4.26, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6, "angle": -0.05, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.8, "angle": 1.29, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.3333, "angle": -3.54, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.4667, "angle": -4.26, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.9333, "angle": -0.05, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.1333, "angle": 1.29, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.6667, "angle": -3.54}], "translate": [{"x": -9.71, "y": 0.82}]}, "nzbone24": {"rotate": [{"angle": -2.19, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": -6.14, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6, "angle": -3.23, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1, "angle": 1.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": -2.19, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "angle": -6.14, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.9333, "angle": -3.23, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.3333, "angle": 1.76, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -2.19}]}, "nzbone25": {"rotate": [{"angle": -4.14, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.6, "angle": -5.45, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1.2667, "angle": -4.08, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.3333, "angle": -4.14, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1.9333, "angle": -5.45, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 2.6, "angle": -4.08, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2.6667, "angle": -4.14}]}, "nzbone20": {"rotate": [{"angle": 1, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 0.2667, "angle": -2.22, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4667, "angle": -3.74, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 2.54, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": 1, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 1.6, "angle": -2.22, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.8, "angle": -3.74, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": 2.54, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "angle": 1}]}, "nzbone21": {"rotate": [{"angle": 8.08, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.2667, "angle": 5.17, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.6667, "angle": 0.16, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 8.08, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.6, "angle": 5.17, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2, "angle": 0.16, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 8.08}]}, "nzbone22": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -1.58, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -1.58, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "nzbone66": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 9.32, "y": -0.27, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 9.32, "y": -0.27, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "nzbone73": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 21.43, "y": -0.61, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 21.43, "y": -0.61, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "nzbone83": {"translate": [{"x": -1.07, "y": 6.55, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "y": -3.51, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": -3.51, "y": 29.53, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "x": -1.07, "y": 6.55, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.5667, "y": -3.51, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "x": -3.51, "y": 29.53, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "x": -1.07, "y": 6.55}], "scale": [{"x": 0.939, "y": 0.939, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 0.8, "y": 0.8, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 1.3333, "x": 0.939, "y": 0.939, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.5667, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "x": 0.8, "y": 0.8, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "x": 0.939, "y": 0.939}]}, "nzbone84": {"translate": [{"x": 0.8, "y": 13.2, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 0.3667, "y": -3.51, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "x": 1.41, "y": 26.01, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 1.3333, "x": 0.8, "y": 13.2, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 1.7, "y": -3.51, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "x": 1.41, "y": 26.01, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 2.6667, "x": 0.8, "y": 13.2}], "scale": [{"x": 0.887, "y": 0.887, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "x": 0.8, "y": 0.8, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 1.3333, "x": 0.887, "y": 0.887, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "x": 0.8, "y": 0.8, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 2.6667, "x": 0.887, "y": 0.887}]}, "nzbone85": {"translate": [{"x": 1.22, "y": 10.52, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.3, "y": -3.51, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "x": 2.81, "y": 28.82, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 1.3333, "x": 1.22, "y": 10.52, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 1.6333, "y": -3.51, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "x": 2.81, "y": 28.82, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 2.6667, "x": 1.22, "y": 10.52}], "scale": [{"x": 0.913, "y": 0.913, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "x": 0.8, "y": 0.8, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 1.3333, "x": 0.913, "y": 0.913, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 1.6333, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "x": 0.8, "y": 0.8, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 2.6667, "x": 0.913, "y": 0.913}]}, "nzbone86": {"translate": [{"x": -0.27, "y": -2.1, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0667, "y": -3.51, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": -6.33, "y": 29.63, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 1.3333, "x": -0.27, "y": -2.1, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 1.4, "y": -3.51, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "x": -6.33, "y": 29.63, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2.6667, "x": -0.27, "y": -2.1}], "scale": [{"x": 0.991, "y": 0.991, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": 0.8, "y": 0.8, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 1.3333, "x": 0.991, "y": 0.991, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "x": 0.8, "y": 0.8, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2.6667, "x": 0.991, "y": 0.991}]}, "nzbone87": {"translate": [{"x": -2.86, "y": 0.7, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5, "x": -15.52, "y": 19.34, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "y": -3.51, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "x": -2.86, "y": 0.7, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.8333, "x": -15.52, "y": 19.34, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "y": -3.51, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": -2.86, "y": 0.7}], "scale": [{"x": 0.963, "y": 0.963, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5, "x": 0.8, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.3333, "x": 0.963, "y": 0.963, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.8333, "x": 0.8, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": 0.963, "y": 0.963}]}, "nzbone88": {"translate": [{"x": -8.11, "y": 19.37, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4667, "y": -3.51, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": -10.7, "y": 26.69, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "x": -8.11, "y": 19.37, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.8, "y": -3.51, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "x": -10.7, "y": 26.69, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "x": -8.11, "y": 19.37}], "scale": [{"x": 0.848, "y": 0.848, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": 0.8, "y": 0.8, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "x": 0.848, "y": 0.848, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "x": 0.8, "y": 0.8, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "x": 0.848, "y": 0.848}]}, "nzbone89": {"translate": [{"x": -3.99, "y": 28.78, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0333, "x": -4.06, "y": 29.34, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "y": -3.51, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 1.3333, "x": -3.99, "y": 28.78, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 1.3667, "x": -4.06, "y": 29.34, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "y": -3.51, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 2.6667, "x": -3.99, "y": 28.78}], "scale": [{"x": 0.803, "y": 0.803, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0333, "x": 0.8, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 1.3333, "x": 0.803, "y": 0.803, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 1.3667, "x": 0.8, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "curve": 0.247, "c3": 0.729, "c4": 0.91}, {"time": 2.6667, "x": 0.803, "y": 0.803}]}, "nzbone90": {"translate": [{"x": 5.48, "y": 18.14, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "x": 8.66, "y": 30.75, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "y": -3.51, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "x": 5.48, "y": 18.14, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.6, "x": 8.66, "y": 30.75, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "y": -3.51, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "x": 5.48, "y": 18.14}], "scale": [{"x": 0.874, "y": 0.874, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "x": 0.8, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "x": 0.874, "y": 0.874, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.6, "x": 0.8, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "x": 0.874, "y": 0.874}]}, "nzbone91": {"translate": [{"x": 9.17, "y": 17.28, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "y": -3.51, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": 10.54, "y": 20.39, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "x": 9.17, "y": 17.28, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.8667, "y": -3.51, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "x": 10.54, "y": 20.39, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "x": 9.17, "y": 17.28}], "scale": [{"x": 0.826, "y": 0.826, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": 0.8, "y": 0.8, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "x": 0.826, "y": 0.826, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.8667, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "x": 0.8, "y": 0.8, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "x": 0.826, "y": 0.826}]}, "nzbone92": {"translate": [{"x": -12.89, "y": 13.89, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.1, "x": -14.06, "y": 15.47, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "y": -3.51, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 1.3333, "x": -12.89, "y": 13.89, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 1.4333, "x": -14.06, "y": 15.47, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "y": -3.51, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 2.6667, "x": -12.89, "y": 13.89}], "scale": [{"x": 0.817, "y": 0.817, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.1, "x": 0.8, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 1.3333, "x": 0.817, "y": 0.817, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 1.4333, "x": 0.8, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 2.6667, "x": 0.817, "y": 0.817}]}, "nzbone93": {"translate": [{"x": -7.38, "y": 4.22, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "x": -14.76, "y": 11.95, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": -3.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": -7.38, "y": 4.22, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "x": -14.76, "y": 11.95, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "y": -3.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": -7.38, "y": 4.22}], "scale": [{"x": 0.9, "y": 0.9, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "x": 0.8, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 0.9, "y": 0.9, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "x": 0.8, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 0.9, "y": 0.9}]}, "nzbone94": {"translate": [{"x": 0.23, "y": -1.47, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 0.5667, "x": 2.81, "y": 21.09, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "y": -3.51, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 1.3333, "x": 0.23, "y": -1.47, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 1.9, "x": 2.81, "y": 21.09, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "y": -3.51, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 2.6667, "x": 0.23, "y": -1.47}], "scale": [{"x": 0.983, "y": 0.983, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 0.5667, "x": 0.8, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 1.3333, "x": 0.983, "y": 0.983, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 1.9, "x": 0.8, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 2.6667, "x": 0.983, "y": 0.983}]}, "nzbone95": {"translate": [{"x": 1.46, "y": -1.5, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "y": -3.51, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": 11.25, "y": 11.95, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.3333, "x": 1.46, "y": -1.5, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.4667, "y": -3.51, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "x": 11.25, "y": 11.95, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.6667, "x": 1.46, "y": -1.5}], "scale": [{"x": 0.974, "y": 0.974, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": 0.8, "y": 0.8, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.3333, "x": 0.974, "y": 0.974, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.4667, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "x": 0.8, "y": 0.8, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.6667, "x": 0.974, "y": 0.974}]}, "xlt16": {"rotate": [{"angle": -73.55}], "translate": [{"x": -145.07, "y": -138.72}], "scale": [{"x": 0.5, "y": 0.5}]}, "root": {"translate": [{"y": 1.74}]}, "heilizi/lizi2": {"translate": [{"x": 434.52, "y": 68.19}], "scale": [{"x": 0.584, "y": 0.584}]}, "heilizi/lizi3": {"translate": [{"x": 346.58, "y": 40.12}], "scale": [{"x": 0.326, "y": 0.326}]}, "huoyankuosna3": {"translate": [{"x": -24.42, "y": 16.28}]}, "huoyankuosna2": {"translate": [{"x": 38.26, "y": 8.14}]}, "huoxing-add/huoxing_1": {"translate": [{"x": 65.93, "y": 112.33}]}, "huoxing-add/huoxing_2": {"translate": [{"x": -38.26, "y": 32.56}]}, "nzbone74": {"rotate": [{"angle": -6.69}]}, "nzbone75": {"rotate": [{"angle": 11.95}]}, "nzbone76": {"rotate": [{"angle": 15.45}]}, "nzbone77": {"rotate": [{"angle": -29.71}]}, "a11": {"rotate": [{"angle": 5.69}], "translate": [{"x": -76.07, "y": 124.26}]}, "golw_add/glow_01": {"translate": [{"x": 17.82, "y": 52.1}]}, "heilizi/lizi": {"translate": [{"x": 415.81, "y": 50.41}], "scale": [{"x": 0.5, "y": 0.5}]}, "huoyankuosna": {"translate": [{"x": -59.42, "y": -66.75}]}, "huoxing-add/huoxing_3": {"translate": [{"x": -83.84, "y": 111.52}]}, "huoxing-add/huoxing_4": {"translate": [{"x": -29.3, "y": -34.19}]}, "huoxing-add/huoxing_5": {"translate": [{"x": 36.63, "y": 14.65}]}, "huoxing-add/huoxing_6": {"translate": [{"x": 15.47}]}, "lizi2tiao": {"translate": [{"x": 72.5, "y": 0.83}], "scale": [{"x": 2.498, "y": 2.498}]}, "xlt": {"rotate": [{"angle": 33.55}], "translate": [{"x": -156.17, "y": -102.35}], "scale": [{"x": 0, "y": 0}]}, "xlt2": {"rotate": [{"angle": -24.12}], "translate": [{"x": -156.72, "y": -101.9}], "scale": [{"x": 0, "y": 0}]}, "xlt3": {"rotate": [{"angle": 119.58}], "translate": [{"x": -155.71, "y": -101.76}], "scale": [{"x": 0, "y": 0}]}, "xlt4": {"rotate": [{"angle": -56.07}], "translate": [{"x": -155.91, "y": -102.9}], "scale": [{"x": 0, "y": 0}]}, "xlt5": {"rotate": [{"angle": 14.07}], "translate": [{"x": -155.84, "y": -102.21}], "scale": [{"x": 0, "y": 0}]}, "xlt6": {"rotate": [{"angle": 8.06}], "translate": [{"x": -155.68, "y": -102.07}], "scale": [{"x": 0, "y": 0}]}, "xlt7": {"rotate": [{"angle": 98.14}], "translate": [{"x": -156.22, "y": -101.79}], "scale": [{"x": 0, "y": 0}]}, "xlt8": {"rotate": [{"angle": -16.41}], "translate": [{"x": -148.22, "y": -105.52}], "scale": [{"x": 0, "y": 0}]}, "xlt9": {"rotate": [{"angle": -24.22}], "translate": [{"x": -156.34, "y": -101.64}], "scale": [{"x": 0, "y": 0}]}, "xlt10": {"rotate": [{"angle": -51.74}], "translate": [{"x": -150.85, "y": -108.77}], "scale": [{"x": 0, "y": 0}]}, "xlt11": {"rotate": [{"angle": 17.33}], "translate": [{"x": -137.86, "y": -96.73}], "scale": [{"x": 0.5, "y": 0.5}]}, "xlt12": {"rotate": [{"angle": -60.47}], "translate": [{"x": -164.36, "y": -90.18}], "scale": [{"x": 0.35, "y": 0.35}]}, "xlt13": {"rotate": [{"angle": -7.01}], "translate": [{"x": -124.25, "y": -105.69}], "scale": [{"x": 0.65, "y": 0.65}]}, "xlt14": {"translate": [{"x": -165.32, "y": -101.19}], "scale": [{"x": 0.5, "y": 0.5}]}, "xlt15": {"rotate": [{"angle": -135.1}], "translate": [{"x": -150.85, "y": -108.77}], "scale": [{"x": 0, "y": 0}]}, "jushouyun": {"translate": [{"x": -6.51, "y": 237.17}], "scale": [{"x": 2, "y": 2}]}, "ks": {"scale": [{"x": 2, "y": 2}]}, "nq2": {"scale": [{"x": -1}]}, "jushouyun2": {"translate": [{"x": -6.51, "y": 344.29}], "scale": [{"x": 3.078, "y": 3.078}]}}, "deform": {"default": {"a30": {"a21": [{"vertices": [5.64005, 4.89908, -0.30093, 7.46462, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.45341, 2.64563, 0.30749, 3.00287, -2.15234, 2.11633, 3.32195, 6.69148, 5.63273, 7.46909], "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "vertices": [5.64005, 4.89908, -0.30093, 7.46462, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.45341, 2.64563, 0.30749, 3.00287, -2.15234, 2.11633, 3.32195, 6.69148, 5.63273, 7.46909], "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "vertices": [5.64005, 4.89908, -0.30093, 7.46462, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.45341, 2.64563, 0.30749, 3.00287, -2.15234, 2.11633, 3.32195, 6.69148, 5.63273, 7.46909]}]}}}}}}