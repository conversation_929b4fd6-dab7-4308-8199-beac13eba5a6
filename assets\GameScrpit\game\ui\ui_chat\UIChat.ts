import { _decorator, EditBox, Label, Sprite } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { AdapterView } from "../../../../platform/src/core/ui/adapter_view/AdapterView";
import { ChatAdapter } from "./ChatAdapter";
import { ChatModule } from "../../../module/chat/ChatModule";
import { ChatPackMessage } from "../../net/protocol/Chat";
import TipMgr from "../../../lib/tips/TipMgr";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { MarketType } from "../../../module/chat/ChatConstant";
import { ClubModule } from "../../../module/club/ClubModule";
import { GameDirector } from "../../GameDirector";
import { SystemOpenEnum } from "../../GameDefine";
import { LangMgr } from "../../mgr/LangMgr";
import { JsonMgr } from "../../mgr/JsonMgr";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
import { ChatAudioName } from "../../../module/chat/ChatConfig";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { ListLayoutManager } from "db://assets/platform/src/core/ui/adapter_view/layout_manager/ListLayoutManager";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Wed Nov 06 2024 12:02:30 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/ui_chat/UIChat.ts
 *
 */

@ccclass("UIChat")
export class UIChat extends UINode {
  protected _openAct: boolean = true; //打开动作
  private _adapter: ChatAdapter;
  private _chatType: number;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_CHAT}?prefab/ui/UIChat`;
  }
  //=================================================
  public init(args: any): void {
    super.init(args);
    this._chatType = args.chatType;
    if (!this._chatType) {
      this._chatType = MarketType.WORLD;
    }
  }
  protected onEvtShow(): void {
    super.onEvtShow();
    MsgMgr.on(MsgEnum.ON_CHAT_WORLD_MESSAGE, this.onWorldMessage, this);
    MsgMgr.on(MsgEnum.ON_CHAT_UNION_MESSAGE, this.onClubMessage, this);
    this._adapter = new ChatAdapter(this.getNode("chat_mine_viewholder"), this.getNode("chat_other_viewholder"));
    this.getNode("list_chat").addComponent(ListLayoutManager);
    this.getNode("list_chat").getComponent(AdapterView).setAdapter(this._adapter);
    let data = ChatModule.data.getMarketMessage(MarketType.WORLD);
    this._adapter.setData(data, MarketType.WORLD);
    this.setTabUI(this._chatType);
    if (!ClubModule.data.clubMessage) {
      this.getNode("btn_club_unselect").getComponent(Sprite).grayscale = true;
      this.getNode("btn_club_unselect").getComponentInChildren(Label).enableOutline = false;
      // this.getNode("btn_club_unselect").getComponentInChildren(Label).outlineColor = math.color("#e9e9e9");
    }
  }

  protected onEvtHide(): void {
    MsgMgr.off(MsgEnum.ON_CHAT_WORLD_MESSAGE, this.onWorldMessage, this);
    MsgMgr.off(MsgEnum.ON_CHAT_UNION_MESSAGE, this.onClubMessage, this);
  }
  private onWorldMessage() {
    //
    if (this.getNode("btn_world_select").active) {
      let data = ChatModule.data.getMarketMessage(MarketType.WORLD);
      this._adapter.setData(data, MarketType.WORLD);
    }
  }
  private onClubMessage() {
    if (this.getNode("btn_club_select").active) {
      let data = ChatModule.data.getMarketMessage(MarketType.CLUB);
      this._adapter.setData(data, MarketType.CLUB);
    }
  }
  private on_click_btn_send() {
    AudioMgr.instance.playEffect(ChatAudioName.Effect.点击发送按钮);
    if (!GameDirector.instance.isSystemOpen(SystemOpenEnum.CHAT_聊天)) {
      // 普通文本
      let lv = JsonMgr.instance.jsonList.c_systemOpen[SystemOpenEnum.CHAT_聊天].openList[1];
      let str = LangMgr.txMsgCode(109, [lv], ".聊天功能未开启.");
      TipMgr.showTip(str);
      return;
    }

    let content = this.getNode("edit_input").getComponent(EditBox).string;
    if (!content) {
      TipMgr.showTip("请输入内容");
      return;
    }
    let marketType = this.getNode("btn_world_select").active ? 2 : 1;
    ChatModule.api.addMessage(
      marketType,
      1,
      content,
      [],
      (data: ChatPackMessage) => {
        log.log(data);
        this.getNode("edit_input").getComponent(EditBox).string = "";
        // this._adapter.insertNewData(data);
        this._adapter.notifyDataSetChanged();
      },
      (errorCode: number, msg: string[], data: any): boolean => {
        TipsMgr.showErrX(errorCode, msg);
        this.getNode("edit_input").getComponent(EditBox).string = "";
        return true;
      }
    );
  }
  private setTabUI(chatType: MarketType) {
    if (MarketType.CLUB == chatType) {
      this.getNode("btn_club_unselect").active = false;
      this.getNode("btn_club_select").active = true;
      this.getNode("btn_world_select").active = false;
      this.getNode("btn_world_unselect").active = true;
      this.getNode("lbl_title").getComponent(Label).string = "战盟 /";
    } else {
      this.getNode("btn_club_unselect").active = true;
      this.getNode("btn_club_select").active = false;
      this.getNode("btn_world_select").active = true;
      this.getNode("btn_world_unselect").active = false;
      this.getNode("lbl_title").getComponent(Label).string = "世界 /";
    }
    let data = ChatModule.data.getMarketMessage(chatType);
    this._adapter.setData(data, chatType);
  }

  private on_click_btn_club_unselect() {
    AudioMgr.instance.playEffect(ChatAudioName.Effect.点击世界战盟切换);
    if (!ClubModule.data.clubMessage) {
      TipMgr.showTip("未加入战盟");
      return;
    }
    this.setTabUI(MarketType.CLUB);
  }
  private on_click_btn_world_unselect() {
    AudioMgr.instance.playEffect(ChatAudioName.Effect.点击世界战盟切换);
    this.setTabUI(MarketType.WORLD);
  }
}
