import MsgEnum from "../../game/event/MsgEnum";
import { IConfigPet, IConfigPetSkill } from "../../game/JsonDefine";
import { JsonMgr } from "../../game/mgr/JsonMgr";
import { PetMessage } from "../../game/net/protocol/Pet";
import MsgMgr from "../../lib/event/MsgMgr";
import { FriendType, FriendSort } from "../friend/FriendConstant";
import { FriendModule } from "../friend/FriendModule";
import { PetSort } from "./PetConstant";
import { PetModule } from "./PetModule";

export class PetData {
  private _data: { [key: number]: PetMessage } = {};
  private _ownedPetIds: number[] = [];
  private _unOwnedPetIds: number[] = [];
  private _heroPetMap: Map<number, number> = new Map(); // 新增：存储 petId -> id 的映射

  public init() {
    this._data = {};
    this._heroPetMap.clear(); // 初始化时清空旧数据

    // 遍历 JsonMgr 中的 c_hero 对象
    const cHero = JsonMgr.instance.jsonList.c_hero;
    for (const heroIdStr in cHero) {
      const heroConfig = cHero[heroIdStr];
      // 确保对象属性存在 petId 和 id
      if (heroConfig && typeof heroConfig.petId !== "undefined" && typeof heroConfig.id !== "undefined") {
        this._heroPetMap.set(heroConfig.petId, heroConfig.id); // petId 作为 Key，id 作为 Value
      }
    }
  }

  public getPetNum() {
    let list = Object.keys(this._data);

    return list.length;
  }

  public getPet(petId: number) {
    return this._data[petId];
  }

  // 新增：通过 petId 查找对应的 heroId
  public getHeroIdByPetId(petId: number): number | undefined {
    return this._heroPetMap.get(petId);
  }

  public setPet(petId: number, petMessage: PetMessage) {
    this._data[petId] = petMessage;
    //
    let pets = Object.values(this._data);
    this.setPetMessageList(pets);
    MsgMgr.emit(MsgEnum.ON_PET_UPDATE);
  }

  public setPetMessageList(pet: PetMessage[]) {
    for (let i = 0; i < pet.length; i++) {
      this._data[pet[i].petId] = pet[i];
    }
    // 将 JsonMgr 中的宠物配置对象转换为数组
    const allPetConfigs = Object.values(JsonMgr.instance.jsonList.c_pet);
    // 清空已拥有和未拥有的宠物 ID 列表
    this._ownedPetIds = [];
    this._unOwnedPetIds = [];

    // 获取所有宠物 ID
    const allPetIds = allPetConfigs.map((config) => config.id);

    // 遍历传入的宠物消息列表，将存在的宠物 ID 加入已拥有列表
    for (let i = 0; i < pet.length; i++) {
      this._ownedPetIds.push(pet[i].petId);
    }

    // 遍历所有宠物 ID，将不在已拥有列表中的 ID 加入未拥有列表
    for (let i = 0; i < allPetIds.length; i++) {
      const petId = allPetIds[i];
      if (!this._ownedPetIds.includes(petId)) {
        this._unOwnedPetIds.push(petId);
      }
    }

    // 发送宠物列表更新消息
  }

  private sortQuality(a: number, b: number): number {
    // 获取宠物基础配置
    const petAConfig = PetModule.config.getHeroPet(a);
    const petBConfig = PetModule.config.getHeroPet(b);

    // 获取宠物状态信息（可能为undefined，未拥有时不存在）
    const petAInfo = this.getPet(a);
    const petBInfo = this.getPet(b);

    // 确定皮肤ID：优先使用已选择的皮肤，否则使用默认皮肤（firstSkin）
    const skinIdA = petAInfo?.chosenSkinId ?? petAConfig.firstSkin;
    const skinIdB = petBInfo?.chosenSkinId ?? petBConfig.firstSkin;

    // 获取皮肤配置
    const skinA = JsonMgr.instance.jsonList.c_petSkin[skinIdA];
    const skinB = JsonMgr.instance.jsonList.c_petSkin[skinIdB];

    // 使用皮肤的color排序（若皮肤配置不存在，降级使用宠物基础配置的color）
    const colorA = skinA?.color ?? petAConfig.color;
    const colorB = skinB?.color ?? petBConfig.color;

    if (colorA > colorB) {
      return -1;
    } else if (colorA < colorB) {
      return 1;
    } else {
      return a - b;
    }
  }
  private sortLevel(a: number, b: number): number {
    let petA = this.getPet(a);
    let petB = this.getPet(b);
    if (petA.level > petB.level) {
      return -1;
    } else if (petA.level < petB.level) {
      return 1;
    } else {
      return a - b;
    }
  }
  public getPetIds(isOwned: boolean, sort: PetSort = PetSort.QUALITY): number[] {
    if (isOwned) {
      switch (sort) {
        case PetSort.LEVEL:
          return this._ownedPetIds.sort((a, b) => this.sortLevel(a, b));
        default:
          return this._ownedPetIds.sort((a, b) => this.sortQuality(a, b));
      }
    }
    return this._unOwnedPetIds.sort((a, b) => this.sortQuality(a, b));
  }
  /**
   *
   * @param a 洗练次数
   * @returns
   */
  public getHeroPetSkillCost(a: number) {
    // 输入验证：确保a是非负整数
    if (!Number.isInteger(a) || a < 0) {
      return 0;
    }
    // 计算公式
    const result = Math.floor(
      (Math.pow(a, 4) * 0.1 + 100) *
        Math.pow(1.03, Math.max(0, a - 250)) *
        Math.pow(1.1, Math.max(0, a - 300) * Math.pow(1.1, Math.max(0, a - 350)))
    );
    return result;
  }

  public getHeroPetQuality(petId: number) {
    let pet = this.getPet(petId);
    let heroPet: IConfigPet = JsonMgr.instance.jsonList.c_pet[petId];
    if (!pet || pet.level < 1) {
      return heroPet.qualityFirst;
    }
    let quality = 0;
    if (pet.level > heroPet.levelMax) {
      quality =
        heroPet.qualityFirst +
        heroPet.qualityAdd * (heroPet.levelMax - 1) +
        (pet.level - heroPet.levelMax) * heroPet.wakeQualityAdd;
    } else {
      quality = heroPet.qualityFirst + heroPet.qualityAdd * (pet.level - 1);
    }
    for (let i = 0; i < pet.skinList.length; i++) {
      quality += JsonMgr.instance.jsonList.c_petSkin[pet.skinList[i]].add1;
    }
    return quality;
  }

  public getHeroPetSkillList(): Array<IConfigPetSkill> {
    let skillMap = JsonMgr.instance.jsonList.c_petSkill;
    return Object.values(skillMap);
  }
}
