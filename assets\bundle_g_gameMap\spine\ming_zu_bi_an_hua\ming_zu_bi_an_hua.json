{"skeleton": {"hash": "3oq+W7mEpZ2OwO06arcpM0uBUsQ=", "spine": "3.8.75", "x": -121.84, "y": -339.88, "width": 1362, "height": 743, "images": "./images/", "audio": "D:/spine导出/场景部件/冥族-彼岸花"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "x": 67.46, "y": 3.59}, {"name": "bone2", "parent": "bone", "length": 34.56, "rotation": 73.57, "x": -68.58, "y": 1.4}, {"name": "bone3", "parent": "bone2", "length": 44.68, "rotation": -17.71, "x": 34.56}, {"name": "bone4", "parent": "bone2", "length": 31.59, "rotation": -93.22, "x": 23.33, "y": -17.49}, {"name": "bone5", "parent": "bone2", "length": 44.44, "rotation": 72.74, "x": 22.34, "y": 15.89}, {"name": "bone6", "parent": "bone", "length": 34.17, "rotation": 84.29, "x": -32.03, "y": 54.95}, {"name": "bone7", "parent": "bone6", "length": 53.68, "rotation": -17.61, "x": 33.32, "y": -0.08}, {"name": "bone8", "parent": "bone6", "length": 35.95, "rotation": -108.73, "x": 31.93, "y": -16.03}, {"name": "bone9", "parent": "bone6", "length": 57.55, "rotation": 57.91, "x": 39.58, "y": 22.75}, {"name": "bone10", "parent": "bone", "length": 39.51, "rotation": 108.82, "x": -111.44, "y": 97.75}, {"name": "bone11", "parent": "bone10", "length": 49.49, "rotation": 1.79, "x": 39.51}, {"name": "bone12", "parent": "bone10", "length": 32.59, "rotation": 59.13, "x": 33.69, "y": 13.21}, {"name": "bone13", "parent": "bone10", "length": 41.57, "rotation": -55.93, "x": 33.26, "y": -10.44}, {"name": "bone14", "parent": "bone", "length": 34.42, "rotation": 110.22, "x": -61.29, "y": 165.75}, {"name": "bone15", "parent": "bone14", "length": 43.03, "rotation": 3.05, "x": 34.42}, {"name": "bone16", "parent": "bone14", "length": 27.36, "rotation": -63.34, "x": 27.68, "y": -13.37}, {"name": "bone17", "parent": "bone14", "length": 27.96, "rotation": 50.24, "x": 36.31, "y": 17.42}], "slots": [{"name": "hua4", "bone": "bone", "attachment": "hua4"}, {"name": "hua3", "bone": "bone", "attachment": "hua3"}, {"name": "hua2", "bone": "bone", "attachment": "hua2"}, {"name": "hua1", "bone": "bone", "attachment": "hua1"}], "skins": [{"name": "default", "attachments": {"hua4": {"hua4": {"type": "mesh", "uvs": [0.5272, 0.99906, 0.62869, 0.98688, 0.66252, 0.91986, 0.60332, 0.79803, 0.63715, 0.69142, 0.79784, 0.61222, 0.93879, 0.50561, 1, 0.35636, 0.82603, 0.14619, 0.6005, 0.01216, 0.2622, 0, 0.03103, 0.12791, 0, 0.35331, 0.01411, 0.55739, 0.18326, 0.61831, 0.34113, 0.664, 0.42007, 0.75234, 0.46236, 0.87113, 0.53284, 0.84067, 0.49901, 0.67314, 0.41161, 0.41119, 0.28475, 0.16142, 0.55257, 0.45688, 0.66816, 0.32285, 0.82603, 0.33808, 0.54975, 0.17055, 0.3073, 0.46906, 0.12124, 0.30153], "triangles": [0, 18, 1, 0, 17, 18, 1, 18, 2, 18, 3, 2, 17, 16, 18, 3, 18, 19, 18, 16, 19, 3, 19, 4, 16, 15, 19, 5, 4, 22, 19, 26, 20, 4, 19, 22, 19, 20, 22, 14, 26, 15, 19, 15, 26, 26, 14, 27, 22, 23, 5, 5, 24, 6, 5, 23, 24, 14, 13, 27, 13, 12, 27, 6, 24, 7, 20, 26, 21, 23, 22, 25, 26, 27, 21, 22, 20, 25, 20, 21, 25, 12, 11, 27, 23, 8, 24, 7, 24, 8, 23, 25, 8, 27, 11, 21, 25, 9, 8, 9, 25, 10, 11, 10, 21, 25, 21, 10], "vertices": [1, 14, -4.82, 7.59, 1, 1, 14, -7.12, -1.73, 1, 1, 14, -2.75, -6.73, 1, 2, 14, 9.12, -5.17, 0.972, 16, -15.66, -12.91, 0.028, 2, 14, 16.72, -11.36, 0.46653, 16, -6.71, -8.89, 0.53347, 1, 16, 8.64, -15.21, 1, 1, 16, 24.46, -18.55, 1, 1, 16, 37.88, -13.87, 1, 2, 16, 40.05, 10.56, 0.87227, 15, 18.28, -45.46, 0.12773, 2, 16, 34.07, 34.01, 0.3791, 15, 37.37, -30.59, 0.6209, 3, 16, 13.11, 57.95, 0.00153, 15, 50.91, -1.79, 0.99648, 17, 18.68, -48.26, 0.00199, 2, 15, 49.27, 22.57, 0.7123, 17, 35.44, -30.5, 0.2877, 2, 15, 32.41, 32.99, 0.24321, 17, 31.63, -11.04, 0.75679, 1, 17, 24.44, 5.24, 1, 1, 17, 7.68, 4.92, 1, 3, 14, 28.58, 13.92, 0.4531, 15, -5.09, 14.22, 0.01277, 17, -7.63, 3.71, 0.53413, 2, 14, 18.8, 9.62, 0.93473, 17, -17.19, 8.47, 0.06527, 2, 14, 7.73, 9.46, 0.99939, 17, -24.4, 16.88, 0.00061, 1, 14, 7.93, 2.33, 1, 2, 14, 22.7, 0.27, 0.99927, 17, -21.88, -0.51, 0.00073, 2, 16, -3.41, 23.24, 0.00413, 15, 12.5, -0.56, 0.99587, 2, 15, 37.17, 1.81, 0.9902, 17, 11.98, -35.73, 0.0098, 3, 14, 38.62, -10.95, 0.04225, 16, 2.75, 10.86, 0.49683, 15, 3.61, -11.16, 0.46092, 2, 16, 18.69, 10.89, 0.76956, 15, 10.03, -25.75, 0.23044, 1, 16, 27.86, -0.85, 1, 2, 16, 20.75, 28.07, 0.33958, 15, 26.6, -20.76, 0.66042, 3, 14, 45.59, 11.05, 0.00021, 15, 11.74, 10.44, 0.55922, 17, 1.04, -11.21, 0.44057, 2, 15, 32.04, 20.74, 0.49987, 17, 22.39, -19.1, 0.50013], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 2, 36, 36, 38, 38, 40, 40, 42, 38, 44, 44, 46, 46, 48, 38, 52, 52, 54], "width": 94, "height": 87}}, "hua1": {"hua1": {"type": "mesh", "uvs": [0.3685, 0.96032, 0.41278, 1, 0.46172, 0.98035, 0.47337, 0.87015, 0.52231, 0.79501, 0.62019, 0.87766, 0.76001, 0.96032, 0.92081, 0.89519, 1, 0.67228, 0.97907, 0.39927, 0.90916, 0.15381, 0.64815, 0, 0.37316, 0, 0.14012, 0.08869, 0, 0.22895, 0, 0.49194, 0.08885, 0.63721, 0.21236, 0.69733, 0.35452, 0.75493, 0.35219, 0.86263, 0.41511, 0.85762, 0.4524, 0.74241, 0.53629, 0.43434, 0.61553, 0.16634, 0.64582, 0.56959, 0.84624, 0.60966, 0.79963, 0.79501, 0.81594, 0.38174, 0.40113, 0.43434, 0.16342, 0.28906, 0.17274, 0.50196, 0.36384, 0.18637], "triangles": [2, 1, 20, 1, 0, 20, 2, 20, 3, 6, 26, 7, 6, 5, 26, 0, 19, 20, 7, 26, 8, 26, 5, 24, 4, 3, 21, 19, 18, 20, 3, 20, 21, 20, 18, 21, 5, 4, 24, 26, 25, 8, 26, 24, 25, 4, 21, 24, 21, 18, 28, 18, 17, 28, 21, 22, 24, 21, 28, 22, 16, 30, 17, 17, 30, 28, 25, 9, 8, 16, 15, 30, 24, 27, 25, 25, 27, 9, 24, 22, 27, 15, 29, 30, 30, 29, 28, 15, 14, 29, 29, 31, 28, 28, 31, 22, 22, 23, 27, 22, 31, 23, 27, 10, 9, 27, 23, 10, 14, 13, 29, 29, 13, 31, 31, 12, 23, 31, 13, 12, 23, 11, 10, 23, 12, 11], "vertices": [1, 2, -8.55, 2.01, 1, 1, 2, -11.18, -4.08, 1, 1, 2, -7.57, -8.88, 1, 2, 2, 4.12, -6.83, 0.97791, 4, -9.57, -19.78, 0.02209, 2, 2, 13.42, -9.95, 0.59504, 4, -6.97, -10.32, 0.40496, 2, 2, 8.12, -23.25, 0.01659, 4, 6.61, -14.86, 0.98341, 1, 4, 24.72, -17.78, 1, 1, 4, 39.79, -5, 1, 2, 3, 24.59, -54.01, 0.18606, 4, 40.35, 20.53, 0.81394, 2, 3, 47.42, -35.62, 0.6799, 4, 28.26, 47.23, 0.3201, 2, 3, 64.64, -14.23, 0.96215, 4, 11.85, 69.26, 0.03785, 2, 3, 61.41, 19.85, 0.95201, 5, 7.81, -77.95, 0.04799, 2, 3, 43.66, 46.03, 0.68548, 5, 34.13, -60.41, 0.31452, 2, 3, 20.77, 62.88, 0.32833, 5, 51.16, -37.65, 0.67167, 2, 3, -0.7, 67.79, 0.109, 5, 56.24, -16.22, 0.891, 1, 5, 40.63, 7.19, 1, 1, 5, 23.51, 14.46, 1, 1, 5, 8.12, 11.93, 1, 2, 2, 12.07, 9.77, 0.62112, 5, -8.9, 7.99, 0.37888, 2, 2, 0.95, 6.76, 0.989, 5, -15.07, 17.73, 0.011, 2, 2, 3.51, -0.02, 0.99996, 4, -16.33, -20.77, 4e-05, 2, 2, 16.54, -0.65, 0.98842, 4, -16.43, -7.72, 0.01158, 2, 3, 15.73, 4.41, 0.97036, 5, -7.26, -32.15, 0.02964, 2, 3, 44.58, 12.97, 0.95647, 5, 1.06, -61.06, 0.04353, 3, 2, 40.57, -16.76, 0.02093, 3, 10.82, -14.13, 0.58355, 4, -1.71, 17.18, 0.39551, 2, 3, 20.21, -35.62, 0.30104, 4, 21.44, 20.89, 0.69896, 2, 3, 0.79, -42.31, 0.00128, 4, 23.06, 0.41, 0.99872, 2, 3, 38.44, -19.04, 0.80878, 4, 9.96, 42.69, 0.19122, 3, 2, 46.49, 14.33, 0.0191, 3, 7.01, 17.28, 0.62758, 5, 5.67, -23.53, 0.35332, 2, 3, 4.53, 48.63, 0.20491, 5, 37.04, -21.3, 0.79509, 2, 3, -13.72, 34.95, 0.01593, 5, 23.51, -2.94, 0.98407, 2, 3, 26.56, 35.72, 0.62792, 5, 23.96, -43.22, 0.37208], "hull": 20, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 0, 38, 2, 40, 40, 42, 42, 44, 44, 46, 8, 48, 48, 50, 36, 56, 56, 58], "width": 115, "height": 107}}, "hua2": {"hua2": {"type": "mesh", "uvs": [0.46637, 0.97603, 0.472, 0.86456, 0.47575, 0.77336, 0.3519, 0.69989, 0.16612, 0.57069, 0, 0.41109, 0, 0.26923, 0.08918, 0.13749, 0.23931, 0.01083, 0.44385, 0, 0.62212, 0.03363, 0.776, 0.15269, 0.92612, 0.38069, 1, 0.64416, 1, 0.88229, 0.87358, 1, 0.76474, 0.99376, 0.68029, 0.86963, 0.56958, 0.78096, 0.53392, 0.86963, 0.52266, 1, 0.48326, 1, 0.50888, 0.75812, 0.54102, 0.50467, 0.63066, 0.28776, 0.67463, 0.64852, 0.84715, 0.69876, 0.43784, 0.47042, 0.23657, 0.26264], "triangles": [14, 15, 26, 19, 20, 0, 20, 21, 0, 15, 16, 26, 16, 17, 26, 0, 1, 19, 26, 13, 14, 18, 25, 17, 17, 25, 26, 1, 2, 19, 2, 22, 19, 19, 22, 18, 25, 18, 23, 22, 2, 27, 2, 3, 27, 18, 22, 23, 22, 27, 23, 3, 4, 27, 13, 26, 12, 23, 24, 25, 26, 25, 12, 12, 24, 11, 12, 25, 24, 4, 28, 27, 4, 5, 28, 23, 27, 24, 27, 28, 9, 24, 9, 10, 24, 27, 9, 9, 28, 8, 28, 6, 7, 28, 5, 6, 24, 10, 11, 28, 7, 8], "vertices": [1, 6, -3.81, 2.35, 1, 1, 6, 9.59, 2.78, 1, 2, 6, 20.54, 3.26, 0.97732, 9, -26.63, 5.78, 0.02268, 2, 6, 27.32, 24.1, 0.1994, 9, -5.37, 11.11, 0.8006, 1, 9, 27.91, 17.3, 1, 1, 9, 60.92, 18.66, 1, 2, 7, 11.85, 94.05, 9e-05, 9, 71.35, 5.21, 0.99991, 2, 7, 32.09, 87.04, 0.04311, 9, 69.62, -16.13, 0.95689, 2, 7, 55.68, 70.72, 0.25493, 9, 59.72, -43.05, 0.74507, 2, 7, 69.99, 40.8, 0.65194, 9, 34.34, -64.39, 0.34806, 2, 7, 77.71, 12.69, 0.94134, 9, 9.05, -78.9, 0.05866, 2, 7, 74.46, -15.86, 0.93534, 8, -1.15, 70.96, 0.06466, 2, 7, 58.96, -49.03, 0.50348, 8, 32.31, 56.11, 0.49652, 2, 7, 34.66, -72.53, 0.11903, 8, 56.29, 32.28, 0.88097, 2, 7, 8.42, -83.84, 0.00618, 8, 68.11, 6.27, 0.99382, 1, 8, 55.31, -15.07, 1, 1, 8, 38.95, -21.68, 1, 2, 6, 12.35, -30.86, 0.00137, 8, 20.33, -13.78, 0.99863, 3, 6, 21.15, -11.95, 0.45572, 7, -8.01, -15, 0.00485, 8, -0.4, -11.52, 0.53943, 2, 6, 9.99, -7.27, 0.97715, 8, -1.25, -23.59, 0.02285, 1, 6, -5.76, -7.01, 1, 1, 6, -6.4, -0.65, 1, 3, 6, 22.9, -1.9, 0.95601, 7, -9.39, -4.88, 0.00247, 8, -10.48, -13.09, 0.04153, 2, 7, 20.6, 2.38, 0.97975, 9, -15.22, -26.18, 0.02025, 2, 7, 50.25, -0.66, 0.99848, 8, -15.88, 46.46, 0.00152, 2, 7, 13.32, -24.33, 0.15973, 8, 8.52, 9.99, 0.84027, 2, 7, 18.84, -52.39, 0.08189, 8, 36.46, 16.07, 0.91811, 3, 6, 56.11, 12.99, 0.00523, 7, 17.76, 19.35, 0.54324, 9, 0.51, -19.18, 0.45153, 2, 7, 27.75, 59.17, 0.12431, 9, 41.55, -18.9, 0.87569], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 0, 42, 44, 46, 46, 48, 48, 22, 36, 50, 50, 52, 52, 28, 4, 54, 54, 56], "width": 162, "height": 120}}, "hua3": {"hua3": {"type": "mesh", "uvs": [0.57876, 0.978, 0.65496, 1, 0.70225, 0.94227, 0.66809, 0.84018, 0.65233, 0.74063, 0.7259, 0.62578, 0.87041, 0.47263, 1, 0.34246, 0.96237, 0.22505, 0.76531, 0.07446, 0.47892, 0.01321, 0.28449, 0.03107, 0.13209, 0.17656, 0, 0.39381, 0, 0.5981, 0.12397, 0.70333, 0.34701, 0.76524, 0.47446, 0.7869, 0.54137, 0.86738, 0.33742, 0.20465, 0.45953, 0.49741, 0.53227, 0.65641, 0.57124, 0.75989, 0.6206, 0.87093, 0.6284, 0.47974, 0.72453, 0.30813, 0.83884, 0.26774, 0.56864, 0.22484, 0.32183, 0.49236, 0.08021, 0.46965, 0.16335, 0.27027], "triangles": [2, 1, 23, 1, 0, 23, 0, 18, 23, 23, 3, 2, 3, 23, 22, 23, 18, 22, 18, 17, 22, 22, 4, 3, 22, 17, 21, 15, 28, 16, 17, 16, 21, 16, 28, 21, 22, 21, 4, 4, 21, 5, 14, 29, 15, 15, 29, 28, 28, 20, 21, 21, 24, 5, 21, 20, 24, 5, 24, 6, 14, 13, 29, 24, 20, 27, 29, 30, 28, 27, 20, 19, 20, 28, 19, 28, 30, 19, 24, 25, 6, 24, 27, 25, 25, 26, 6, 6, 26, 7, 29, 13, 30, 13, 12, 30, 26, 8, 7, 26, 25, 9, 30, 12, 19, 25, 27, 9, 26, 9, 8, 19, 10, 27, 27, 10, 9, 12, 11, 19, 19, 11, 10], "vertices": [1, 10, -2.47, 4.89, 1, 1, 10, -7.17, -1.72, 1, 1, 10, -2.99, -8.24, 1, 2, 10, 8.29, -8.41, 0.98796, 13, -15.68, -19.55, 0.01204, 2, 10, 18.7, -10.26, 0.709, 13, -8.31, -11.96, 0.291, 2, 10, 27.69, -21.25, 0.01294, 13, 5.83, -10.67, 0.98706, 1, 13, 27.55, -12.72, 1, 1, 13, 46.43, -15.02, 1, 2, 11, 18.41, -58.26, 0.00179, 13, 53.94, -4.52, 0.99821, 2, 11, 40.29, -45.01, 0.26941, 13, 54.43, 21.05, 0.73059, 2, 11, 56.59, -19.94, 0.85022, 13, 41.93, 48.22, 0.14978, 3, 11, 61.82, -0.71, 0.99652, 13, 28.47, 62.91, 0.00324, 12, 24.4, -64.21, 0.00024, 2, 11, 52.99, 19.21, 0.87474, 12, 36.42, -46.02, 0.12526, 2, 11, 36.38, 39.86, 0.3642, 12, 44.84, -20.9, 0.6358, 2, 11, 16.31, 47.41, 0.0189, 12, 40.36, 0.07, 0.9811, 1, 12, 25.69, 8.24, 1, 2, 10, 26.3, 20.05, 0.05784, 12, 2.08, 9.85, 0.94216, 2, 10, 19.95, 8.48, 0.7789, 12, -11.1, 9.37, 0.2211, 2, 10, 9.75, 4.75, 0.99838, 12, -19.54, 16.21, 0.00162, 2, 11, 42.86, 0.65, 0.99878, 12, 15.32, -47.51, 0.00122, 2, 11, 9.7, -0.19, 0.99781, 13, 0.19, 19.13, 0.00219, 2, 10, 31.02, -1.52, 0.93215, 13, -8.65, 3.14, 0.06785, 2, 10, 19.45, -1.78, 0.9748, 13, -14.91, -6.58, 0.0252, 2, 10, 6.79, -2.78, 0.99898, 13, -21.17, -17.63, 0.00102, 2, 11, 5.37, -16.96, 0.21846, 13, 12.06, 6.51, 0.78154, 2, 11, 18.79, -32.48, 0.14054, 13, 32.35, 9.56, 0.85946, 2, 11, 18.65, -44.89, 0.01844, 13, 42.77, 2.82, 0.98156, 2, 11, 32.57, -20.68, 0.63293, 13, 29.73, 27.52, 0.36707, 2, 11, 15.14, 12.77, 0.63384, 12, 10.57, -17.63, 0.36616, 2, 11, 26.05, 35, 0.25239, 12, 35.17, -14.82, 0.74761, 2, 11, 42.66, 19.69, 0.78473, 12, 31.25, -37.07, 0.21527], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36, 22, 38, 38, 40, 40, 42, 42, 44, 44, 46, 42, 48, 48, 50, 50, 52, 42, 56, 56, 58], "width": 102, "height": 105}}}}], "animations": {"animation": {"bones": {"bone14": {"rotate": [{"angle": -4.5, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.7333, "angle": -6.68, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 4, "angle": -4.5}]}, "bone17": {"rotate": [{"angle": -6.62, "curve": 0.344, "c2": 0.66, "c3": 0.677}, {"time": 0.0667, "angle": -6.68, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2.7333, "angle": -1.9, "curve": 0.347, "c2": 0.38, "c3": 0.744, "c4": 0.94}, {"time": 4, "angle": -6.62}]}, "bone16": {"rotate": [{"angle": -1.62, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.4, "angle": -6.68, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2.7333, "angle": -1.9, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 3.4, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 4, "angle": -1.62}]}, "bone15": {"rotate": [{"angle": -1.62, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 1.4, "angle": -6.68, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2.7333, "angle": -1.9, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 3.4, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 4, "angle": -1.62}]}, "bone13": {"rotate": [{"angle": -0.37, "curve": 0.295, "c2": 0.19, "c3": 0.755}, {"time": 1.7667, "angle": -6.68, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 3.1, "angle": -1.9, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 3.7667, "curve": 0.301, "c3": 0.637, "c4": 0.36}, {"time": 4, "angle": -0.37}]}, "bone12": {"rotate": [{"angle": -5.69, "curve": 0.377, "c2": 0.61, "c3": 0.719}, {"time": 0.4333, "angle": -6.68, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 3.1, "angle": -1.9, "curve": 0.33, "c2": 0.32, "c3": 0.694, "c4": 0.75}, {"time": 4, "angle": -5.69}]}, "bone11": {"rotate": [{"angle": -0.37, "curve": 0.295, "c2": 0.19, "c3": 0.755}, {"time": 1.7667, "angle": -6.68, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 3.1, "angle": -1.9, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 3.7667, "curve": 0.301, "c3": 0.637, "c4": 0.36}, {"time": 4, "angle": -0.37}]}, "bone10": {"rotate": [{"angle": -2.9, "curve": 0.37, "c2": 0.47, "c3": 0.753}, {"time": 1.1, "angle": -6.68, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 4, "angle": -2.9}]}, "bone9": {"rotate": [{"angle": -0.75, "curve": 0.373, "c2": 0.62, "c3": 0.713}, {"time": 0.3667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.0333, "angle": -1.9, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.3667, "angle": -6.68, "curve": 0.243, "c3": 0.684, "c4": 0.73}, {"time": 4, "angle": -0.75}]}, "bone8": {"rotate": [{"angle": -6.13, "curve": 0.291, "c2": 0.19, "c3": 0.665, "c4": 0.66}, {"time": 1.0333, "angle": -1.9, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 3.7, "angle": -6.68, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 4, "angle": -6.13}]}, "bone7": {"rotate": [{"angle": -6.13, "curve": 0.291, "c2": 0.19, "c3": 0.665, "c4": 0.66}, {"time": 1.0333, "angle": -1.9, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 3.7, "angle": -6.68, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 4, "angle": -6.13}]}, "bone6": {"rotate": [{"angle": -3.49, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.0333, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "angle": -6.68, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 4, "angle": -3.49}]}, "bone5": {"rotate": [{"angle": -1.9, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.3333, "angle": -6.68, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -1.9}]}, "bone4": {"rotate": [{"angle": -1.9, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -6.68, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -1.9}]}, "bone3": {"rotate": [{"angle": -1.9, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -6.68, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -1.9}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -6.68, "curve": 0.25, "c3": 0.75}, {"time": 4}]}}}}}