import MsgEnum from "../../game/event/MsgEnum";
import { GameDirector } from "../../game/GameDirector";
import { Api<PERSON><PERSON><PERSON>, ApiHandlerFail, ApiHandlerSuccess } from "../../game/mgr/ApiHandler";
import { JsonMgr } from "../../game/mgr/JsonMgr";
import { ItemSubCmd, PlayerSubCmd, StatisticsSubCmd } from "../../game/net/cmd/CmdData";
import { CommIntegerMapMessage } from "../../game/net/protocol/Comm";
import {
  <PERSON><PERSON><PERSON>al<PERSON>,
  ByteValueList,
  DoubleValue,
  DoubleValueList,
  IntValue,
  IntValueList,
  LongValue,
  LongValueList,
  StringValue,
  StringValueList,
} from "../../game/net/protocol/ExternalMessage";
import { ItemUseMessage } from "../../game/net/protocol/Item";
import {
  BubbleMessage,
  HeadFrameMessage,
  HeadShowMessage,
  OfflineEnergyMessage,
  PlayerBattleAttrResponse,
  PlayerCreateMessage,
  PlayerDailyTreasureResponse,
  PlayerDataMessage,
  PlayerDetailMessage,
  PlayerEnergyUpdateMessage,
  PlayerLevelUpResponse,
  PlayerRenameResponse,
  PlayerUpdateEnergyRequestMessage,
  SkinMessage,
  TitleMessage,
} from "../../game/net/protocol/Player";
import { ItemEnum } from "../../lib/common/ItemEnum";
import MsgMgr from "../../lib/event/MsgMgr";
import { UIMgr } from "../../lib/ui/UIMgr";
import { TimeUtils } from "../../lib/utils/TimeUtils";
import { PlayerRouteName } from "./PlayerConstant";
import { PlayerModule } from "./PlayerModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export default class PlayerApi {
  /** 获取角色信息 */
  public getPlayerInfo(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(PlayerDataMessage, PlayerSubCmd.playerInfo, null, (data: PlayerDataMessage) => {
      PlayerModule.data.initPlayerInfo(data);
      success && success(data);
    });
  }

  /**获取随机生成的昵称列表 */
  public getRandomNickName(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(StringValueList, PlayerSubCmd.randomNickName, null, (data: StringValueList) => {
      success && success(data.values);
    });
  }

  /**创建游戏角色 */
  public postCreatrPlayer(data: PlayerCreateMessage, success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    ApiHandler.instance.request(
      PlayerDataMessage,
      PlayerSubCmd.create,
      PlayerCreateMessage.encode(data),
      (data: PlayerDataMessage) => {
        PlayerModule.data.initPlayerInfo(data);
        success && success(data);
      },
      (errorCode: any, msg: string[], data: any) => {
        log.log(`${errorCode}`);
        log.log(data);
        error && error(errorCode, msg, data);
        return true;
      }
    );
  }

  /**下线命令 */
  public offline(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(DoubleValue, PlayerSubCmd.offline, null, (data: DoubleValue) => {
      success && success(data);
    });
  }

  /**
   * 主角升级
   * LongValueList 解锁的资源集合  值配置表 unlockList:number[]
   * @param success
   */
  public levelUp(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(PlayerLevelUpResponse, PlayerSubCmd.levelUp, null, (data: PlayerLevelUpResponse) => {
      PlayerModule.data.setLevel(data.level);
      success && success(data.rewardMessage);

      // 检查模块开启
      GameDirector.instance.checkAndLoadModule(true);

      MsgMgr.emit(MsgEnum.ON_PLAYER_LEVEL_UP);
    });
  }

  /**领取每日宝箱 */
  public dailyTreasure(success?: ApiHandlerSuccess) {
    ApiHandler.instance.requestSync(
      PlayerDailyTreasureResponse,
      PlayerSubCmd.dailyTreasure,
      null,
      (data: PlayerDailyTreasureResponse) => {
        PlayerModule.data.dailyTreasureRes(data.lastDailyTreasureTs);
        success && success(data.resAddList);
      }
    );
  }

  /**
   * 改名 返回0表示成功，返回1表示包含敏感词，返回2表示命名重复
   * @param name
   * @param success
   */
  public rename(name: string, success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    let data: StringValue = {
      value: name,
    };
    ApiHandler.instance.request(
      PlayerRenameResponse,
      PlayerSubCmd.rename,
      StringValue.encode(data),
      (data: PlayerRenameResponse) => {
        PlayerModule.data.renameRes(data);
        success && success(data);
      },
      (errorCode: any, msg: string[], data: any) => {
        log.log(`${errorCode}`);
        log.log(data);
        error && error(errorCode, msg, data);
        return true;
      }
    );
  }

  /**
   * 获取玩家的战斗属性和总战力
   * @param success
   */
  public getPower(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(
      PlayerBattleAttrResponse,
      PlayerSubCmd.getPower,
      null,
      (data: PlayerBattleAttrResponse) => {
        PlayerModule.data.playerBattleAttrResponse = data;
        success && success(data);
      }
    );
  }

  /**
   * 展示计算各模块的战力及赚速信息
   * @param success
   */
  public powerInfo(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(StringValue, PlayerSubCmd.powerInfo, null, (data: StringValue) => {
      success && success(data);
    });
  }

  /**
   * 获取系统开启情况
   * @param success
   */
  public systemOpen(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(IntValueList, PlayerSubCmd.systemOpen, null, (data: IntValueList) => {
      success && success(data.values);
    });
  }

  /**
   * 获取指定玩家详情
   * @param success
   */
  public getOtherPlayerDetail(palyerId: number, success?: ApiHandlerSuccess) {
    let data: LongValue = {
      value: palyerId,
    };
    ApiHandler.instance.request(
      PlayerDetailMessage,
      PlayerSubCmd.getDetail,
      LongValue.encode(data),
      (data: PlayerDetailMessage) => {
        success && success(data);
      }
    );
  }

  /**
   * 更新引导id
   * @param success
   */
  public updateGuideId(guideId: number, success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    let data: LongValue = {
      value: guideId,
    };
    ApiHandler.instance.request(
      IntValue,
      PlayerSubCmd.updateGuideId,
      LongValue.encode(data),
      (data: IntValue) => {
        if (PlayerModule.data.getPlayerInfo().guideId < data.value) {
          PlayerModule.data.getPlayerInfo().guideId = data.value;
        }

        MsgMgr.emit(MsgEnum.ON_PLAYER_GUIDE_ID_UPDATE);

        success && success(data);
      },
      error
    );
  }

  /**
   * 一键升级，每次升一级
   * @param success
   */
  public testLv(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(LongValueList, PlayerSubCmd.testLv, null, (data: LongValueList) => {
      success && success(data);
    });
  }

  /**
   * 重置角色
   * @param success
   */
  public resetRole(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(BoolValue, PlayerSubCmd.resetRole, null, (data: BoolValue) => {
      success && success(data);
    });
  }

  /**
   * 后端测试-删除角色
   * @param userId
   * @param success
   */
  public delRole(userId: number, success?: ApiHandlerSuccess) {
    let data: LongValue = {
      value: userId,
    };
    ApiHandler.instance.request(BoolValue, PlayerSubCmd.delRole, LongValue.encode(data), (data: BoolValue) => {
      success && success(data);
    });
  }

  //=============道具

  /**使用道具 */
  public useItem(info: ItemUseMessage, success?) {
    log.log("传入参数====", info);
    ApiHandler.instance.request(
      DoubleValueList,
      ItemSubCmd.useItem,
      ItemUseMessage.encode(info),
      (data: DoubleValueList) => {
        success && success(data);
      }
    );
  }

  /** ============================== 20240922 审核后的代码===================================== */

  /** 更新气运接口 */
  public updateEnergy(data: PlayerUpdateEnergyRequestMessage, success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(
      PlayerEnergyUpdateMessage,
      PlayerSubCmd.updateEnergy,
      PlayerUpdateEnergyRequestMessage.encode(data),
      (data: PlayerEnergyUpdateMessage) => {
        PlayerModule.data.setItemNum(ItemEnum.气运_1, data.totalEnergy);
        // 更新本地与服务器的时间差
        TimeUtils.serverTimeBase = new Date().valueOf() - data.nowStamp;
        success && success(data);
      }
    );
  }

  /**府邸主角装饰api */
  /**上阵任意一个皮肤|装饰 */
  public workSkinOrDecoration(val: number, success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(
      LongValue,
      PlayerSubCmd.workSkinOrDecoration,
      LongValue.encode({
        value: val,
      }),
      (data: LongValue) => {
        if (JsonMgr.instance.jsonList.c_leaderSkin[data.value]) {
          PlayerModule.data.setUseSkin(data.value);
        } else if (JsonMgr.instance.jsonList.c_headShow[data.value]) {
          PlayerModule.data.setUseHeadShow(data.value);
        } else if (JsonMgr.instance.jsonList.c_head[data.value]) {
          PlayerModule.data.setUseHeadFrame(data.value);
        } else if (JsonMgr.instance.jsonList.c_title[data.value]) {
          PlayerModule.data.setUseTitle(data.value);
        } else if (JsonMgr.instance.jsonList.c_bubble[data.value]) {
          PlayerModule.data.setUseTitle(data.value);
        }
        success && success(data);
      }
    );
  }

  /**激活皮肤 */
  public activeSkin(id: number, success) {
    ApiHandler.instance.request(
      SkinMessage,
      PlayerSubCmd.activeSkin,
      LongValue.encode({
        value: id,
      }),
      (data: SkinMessage) => {
        PlayerModule.data.skinMap[data.skinId] = data;
        success && success(data);
      }
    );
  }

  public getAllSkin() {
    ApiHandler.instance.request(ByteValueList, PlayerSubCmd.getAllSkin, null, (data: ByteValueList) => {
      for (let i = 0; i < data.values.length; i++) {
        let info = SkinMessage.decode(data.values[i]);
        PlayerModule.data.skinMap[info.skinId] = info;
      }
    });
  }

  public getAllTitle() {
    ApiHandler.instance.request(ByteValueList, PlayerSubCmd.getAllTitle, null, (data: ByteValueList) => {
      for (let i = 0; i < data.values.length; i++) {
        let info = TitleMessage.decode(data.values[i]);
        PlayerModule.data.titleMap[info.titleId] = info;
      }
    });
  }

  public getAllHeadFrame() {
    ApiHandler.instance.request(ByteValueList, PlayerSubCmd.getAllHeadFrame, null, (data: ByteValueList) => {
      for (let i = 0; i < data.values.length; i++) {
        let info = HeadFrameMessage.decode(data.values[i]);
        PlayerModule.data.headFrameMap[info.headFrameId] = info;
      }
    });
  }

  public getGetAllHeadShow() {
    ApiHandler.instance.request(ByteValueList, PlayerSubCmd.getGetAllHeadShow, null, (data: ByteValueList) => {
      for (let i = 0; i < data.values.length; i++) {
        let info = HeadShowMessage.decode(data.values[i]);
        PlayerModule.data.headShowMap[info.headShowId] = info;
      }
    });
  }

  public getAllBubble() {
    ApiHandler.instance.request(ByteValueList, PlayerSubCmd.getAllBubble, null, (data: ByteValueList) => {
      for (let i = 0; i < data.values.length; i++) {
        let info = BubbleMessage.decode(data.values[i]);
        PlayerModule.data.bubbleMap[info.bubbleId] = info;
      }
    });
  }

  //离线========

  /**获取离线的收益的大小 */
  public getOfflineEnergy() {
    if (PlayerModule.data.rightMap[201] <= 0) {
      return;
    }
    ApiHandler.instance.request(
      OfflineEnergyMessage,
      PlayerSubCmd.getOfflineEnergy,
      null,
      (data: OfflineEnergyMessage) => {
        log.log("查看离线的收益====", data);
        if (data.energy > 0) {
          UIMgr.instance.showDialog(PlayerRouteName.UIOffAward, { offData: data });
        }
      }
    );
  }

  public updateEquity(success?) {
    ApiHandler.instance.request(
      CommIntegerMapMessage,
      PlayerSubCmd.updateEquity,
      null,
      (data: CommIntegerMapMessage) => {
        log.log("更新的权益情况===", data);
        PlayerModule.data.rightMap = data.intMap;
        success && success(data);
      }
    );
  }

  public testStatisticsEvent(taskId: number, success?: ApiHandlerSuccess) {
    let data: IntValue = {
      value: taskId,
    };
    ApiHandler.instance.request(
      StringValue,
      StatisticsSubCmd.testStatisticsEvent,
      IntValue.encode(data),
      (data: StringValue) => {
        log.log("测试统计事件====", data.value);
        success && success(data);
      }
    );
  }

  public archive(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(StringValue, PlayerSubCmd.archive, null, (data: StringValue) => {
      log.log("前端获取保存在后端的数据====", data.value);
      success && success(data);
    });
  }

  public updateArchive(stringValue: string, success?: ApiHandlerSuccess) {
    let data: StringValue = {
      value: stringValue,
    };

    ApiHandler.instance.request(StringValue, PlayerSubCmd.archive, StringValue.encode(data), (data: StringValue) => {
      log.log("前端保存数据在后端====", data.value);
      success && success(data);
    });
  }
}

// 路由: 3 - 1  --- 【获取角色信息】 --- 【PlayerAction:76】【playerInfo】
// 方法返回值: PlayerDataMessage

// 路由: 3 - 2  --- 【获取随机生成的昵称列表】 --- 【PlayerAction:108】【randomNicknameList】
// 方法返回值: StringValueList

// 路由: 3 - 3  --- 【创建游戏角色】 --- 【PlayerAction:120】【createPlayer】
// 方法参数: PlayerCreateMessage
// 方法返回值: PlayerDataMessage 玩家信息

// 路由: 3 - 4  --- 【下线命令】 --- 【PlayerAction:169】【offline】
// 方法返回值: void

// 路由: 3 - 5  --- 【更新气运接口】 --- 【PlayerAction:192】【updateEnergy】
// 方法参数: PlayerUpdateEnergyRequestMessage requestMessage 点击水晶的信息
// 方法返回值: Double 返回最新气运

// 路由: 3 - 6  --- 【主角升级】 --- 【PlayerAction:236】【levelUp】
// 方法返回值: LongValueList 解锁的资源集合  值配置表 unlockList:number[]

// 路由: 3 - 7  --- 【领取每日宝箱】 --- 【PlayerAction:294】【dailyTreasure】
// 方法返回值: BoolValue

// 路由: 3 - 8  --- 【改名 返回0表示成功，返回1表示包含敏感词，返回2表示命名重复】 --- 【PlayerAction:133】【rename】
// 方法参数: StringValue
// 方法返回值: IntValue

// 路由: 3 - 9  --- 【获取玩家的战斗属性和总战力】 --- 【PlayerAction:317】【getPower】
// 方法返回值: PlayerBattleAttrResponse

// 路由: 3 - 102  --- 【后端调试：展示计算各模块的战力及赚速信息（新版）】 --- 【PlayerAction:336】【playerNewInfo】
// 方法返回值: StringValue

// 路由: 3 - 103  --- 【一键升级，每次升一级】 --- 【PlayerAction:412】【testLv】
// 方法返回值: LongValueList

// 路由: 3 - 121  --- 【后端测试：重置角色】 --- 【PlayerAction:441】【resetRole】
// 方法返回值: PlayerDataMessage

// 路由: 3 - 122  --- 【后端测试-删除角色】 --- 【PlayerAction:462】【delRole】
// 方法参数: LongValue userId
// 方法返回值: BoolValue

// 路由: 3 - 123  --- 【】 --- 【PlayerAction:468】【delAllRole】
// 方法返回值: BoolValue
