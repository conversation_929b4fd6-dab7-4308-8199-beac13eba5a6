import { _decorator, instantiate, Label, Node, sp, Sprite, tween, UITransform, Widget } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { ClubModule } from "../../../module/club/ClubModule";
import Formate from "../../../lib/utils/Formate";
import { FontColor } from "../../common/FmConstant";
import { ClubBossBuddyMessage, ClubHurtRewardResponse } from "../../net/protocol/Club";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { ClubRouteItem } from "../../../module/club/ClubRoute";
import { BossConfig, ClubAudioName } from "../../../module/club/ClubConfig";
import ResMgr from "../../../lib/common/ResMgr";
import { JsonMgr } from "../../mgr/JsonMgr";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { UIClubTips } from "../../common/UIClubTips";
import { FightData } from "../../fight/FightDefine";
import { BadgeMgr, BadgeType } from "../../mgr/BadgeMgr";
import { CLUB_POSITION } from "../../../module/club/ClubConstant";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { LangMgr } from "../../mgr/LangMgr";
import { ScrollableView } from "../../../../platform/src/core/ui/components/ScrollableView";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { RouteManager } from "db://assets/platform/src/core/managers/RouteManager";
import { UIClubConfirmDialog } from "./UIClubConfirmDialog";
import { RouteShowArgs } from "db://assets/platform/src/core/managers/RouteTableManager";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Sun Aug 25 2024 11:13:48 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/club/UIClubBossMain.ts
 *
 */

@ccclass("UIClubBossMain")
export class UIClubBossMain extends UINode {
  protected _openAct: boolean = true; //打开动作
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_CLUB}?prefab/ui/UIClubBossMain`;
  }
  //=================================================
  public init(args: any): void {
    super.init(args);
  }
  private _data: ClubBossBuddyMessage;

  private refreshBossItem(itemNode: Node, boss: BossConfig, index: number) {
    let clubMessage = ClubModule.data.clubMessage;
    let tiaojian1: boolean = clubMessage.killBossCnt >= index;
    let tiaojian2: boolean = clubMessage.level >= boss.unlock;
    let roleId = JsonMgr.instance.jsonList.c_unionBoss[boss.id].monsterId;
    let monster = JsonMgr.instance.jsonList.c_monsterShow[roleId];
    let spineId = monster.spineId;
    let path = JsonMgr.instance.jsonList.c_spineShow[spineId].prefabPath.split("?");
    let node_info = itemNode.getChildByName("node_info");
    let bg_defeat = itemNode.getChildByName("bg_defeat");
    itemNode.getChildByName("node_boss_name").getChildByName("lbl_boss_name").getComponent(Label).string = monster.name;

    let btn_haozhao = node_info.getChildByName("btn_haozhao");
    let btn_go_fight = node_info.getChildByName("btn_go_fight");
    let node_jiesuo = node_info.getChildByName("node_jiesuo");
    let node_killer = node_info.getChildByName("node_killer");
    node_jiesuo.getChildByName("tiaojian2").getChildByName("Label").getComponent(Label).string = LangMgr.txMsgCode(
      169,
      [boss.unlock]
    ); // `战盟等级达到${boss.unlock}级解锁`;

    itemNode.getChildByName("bg_boss_img").removeAllChildren();
    itemNode.getChildByPath("node_info/node_award/bg_award0").active = false;
    itemNode.getChildByPath("node_info/node_award/bg_award1").active = false;
    itemNode.getChildByPath("node_info/node_award/bg_award2").active = false;
    itemNode.getChildByPath(`node_info/node_award/bg_award${Math.floor(index / 2)}`).active = true;

    ResMgr.setNodePrefab(path[0], path[1], itemNode.getChildByName("bg_boss_img"), (node: Node) => {
      itemNode.getChildByName("bg_boss_img").addChild(node);
      node.setScale(monster.renderScale5[0], monster.renderScale5[1]);
      node.setPosition(0, 0);
      node.getChildByName("render").getComponent(sp.Skeleton).setAnimation(0, "boss_idle", true);
      return true;
    });
    // ResMgr.loadPrefab(
    //   `${path[0]}?${path[1]}`,
    //   (prefab) => {
    //     let node = instantiate(prefab);
    //     itemNode.getChildByName("bg_boss_img").addChild(node);
    //     node.setScale(monster.renderScale5[0], monster.renderScale5[1]);
    //     node.setPosition(0, 0);
    //     node.getChildByName("render").getComponent(sp.Skeleton).setAnimation(0, "boss_idle", true);
    //     node.walk((child) => {
    //       child.layer = itemNode.layer;
    //     });
    //   },
    //   this
    // );
    bg_defeat.active = false;
    btn_haozhao.active = false;
    btn_go_fight.active = false;
    node_jiesuo.active = false;
    node_killer.active = false;

    //已经召唤的BOSS
    if (this._data.bossTrain.bossIndex == index) {
      if (this._data.bossTrain.killUserMessage) {
        node_killer.active = true;
        bg_defeat.active = true;
        node_killer.getChildByName("lbl_killer").getComponent(Label).string =
          this._data.bossTrain.killUserMessage.nickname;

        btn_go_fight.active = true;
      } else {
        // 击败后依然可以挑战boss灵魂
        btn_go_fight.active = true;
      }
      if (this._data.bossTrain.isTakeKill) {
        itemNode.getChildByPath("node_info/node_award/bg_yilingqu").active = true;
      } else {
        itemNode.getChildByPath("node_info/node_award/bg_yilingqu").active = false;
      }
      //设置血量和玩家信息
      let blood = boss.monsterPower[0][1];
      let progress = itemNode.getChildByName("bg_progress_under").getChildByName("bg_progress");
      let lbl_progress = itemNode.getChildByName("bg_progress_under").getChildByName("lbl_progress");
      progress.getComponent(Sprite).fillRange = this._data.bossTrain.remainHp / blood;
      lbl_progress.getComponent(Label).string = `${Formate.format(this._data.bossTrain.remainHp)}/${Formate.format(
        blood
      )}`;
    } else {
      itemNode.getChildByPath("node_info/node_award/bg_yilingqu").active = false;
      let blood = boss.monsterPower[0][1];
      let progress = itemNode.getChildByName("bg_progress_under").getChildByName("bg_progress");
      let lbl_progress = itemNode.getChildByName("bg_progress_under").getChildByName("lbl_progress");
      progress.getComponent(Sprite).fillRange = 1;
      lbl_progress.getComponent(Label).string = `${Formate.format(blood)}/${Formate.format(blood)}`;
      if (tiaojian1 && tiaojian2) {
        btn_haozhao.active = true;
      } else if (tiaojian1) {
        node_jiesuo.active = true;
        node_jiesuo.getChildByName("tiaojian1").getChildByName("icon_yes").active = true;
        node_jiesuo.getChildByName("tiaojian1").getChildByName("icon_no").active = false;
        node_jiesuo.getChildByName("tiaojian2").getChildByName("icon_yes").active = false;
        node_jiesuo.getChildByName("tiaojian2").getChildByName("icon_no").active = true;
        node_jiesuo.getChildByName("tiaojian1").getChildByName("Label").getComponent(Label).color = FontColor.GREEN_2;
      } else if (tiaojian2) {
        node_jiesuo.active = true;
        node_jiesuo.getChildByName("tiaojian2").getChildByName("icon_yes").active = true;
        node_jiesuo.getChildByName("tiaojian2").getChildByName("icon_no").active = false;
        node_jiesuo.getChildByName("tiaojian1").getChildByName("icon_yes").active = false;
        node_jiesuo.getChildByName("tiaojian1").getChildByName("icon_no").active = true;
        node_jiesuo.getChildByName("tiaojian2").getChildByName("Label").getComponent(Label).color = FontColor.GREEN_2;
      } else {
        node_jiesuo.active = true;
        node_jiesuo.getChildByName("tiaojian2").getChildByName("icon_yes").active = false;
        node_jiesuo.getChildByName("tiaojian2").getChildByName("icon_no").active = true;
        node_jiesuo.getChildByName("tiaojian1").getChildByName("icon_yes").active = false;
        node_jiesuo.getChildByName("tiaojian1").getChildByName("icon_no").active = true;
      }
    }
  }
  private refreshBossList() {
    ClubModule.api.getBossTrain((data: ClubBossBuddyMessage) => {
      this._data = data;
      let datas = ClubModule.config.getClubBossList();
      log.log(datas);
      log.log(data);
      for (let i = 0; i < datas.length; i++) {
        let boss = datas[i];
        if (this.getNode(`boss_${boss.id}`).children.length > 0) {
          this.refreshBossItem(this.getNode(`boss_${boss.id}`).children[0], boss, i);
        } else {
          let item = instantiate(this.getNode("boss_item"));
          item.setPosition(0, 0);
          this.getNode(`boss_${boss.id}`).addChild(item);
          this.initBossItem(item, boss, i);
          this.refreshBossItem(item, boss, i);
        }
      }
      this.updateProcess(data.bossTrain.damageHp, data.bossTrain.hurtRewardIndexList);
      this.scrolltocurIndex();
    });
  }

  private scrolltocurIndex() {
    if (this._data.bossTrain.bossIndex < 0) {
      return;
    }

    tween(this.node)
      .delay(0.3)
      .call(() => {
        let offsetY =
          this.getNode(`node_boss_list`).getWorldPosition().y -
          this.getNode(`boss_${this._data.bossTrain.bossIndex + 1}`).getWorldPosition().y;
        this.getNode("node_boss_list").getComponent(ScrollableView).scrollTo(0, offsetY, true);
      })
      .start();
  }
  private initBossItem(itemNode: Node, boss: BossConfig, index: number) {
    // let data = datas[i];
    let node_info = itemNode.getChildByName("node_info");
    let btn_haozhao = node_info.getChildByName("btn_haozhao");
    let btn_go_fight = node_info.getChildByName("btn_go_fight");

    BadgeMgr.instance.setBadgeId(
      node_info.getChildByName("node_award"),
      BadgeType.UIClubMain.btn_boss[`boss_award_${index}`].id
    );

    node_info.getChildByName("node_award").on(
      Node.EventType.TOUCH_END,
      () => {
        AudioMgr.instance.playEffect(ClubAudioName.Effect.战盟首领点击宝箱领取奖励);
        UIMgr.instance.showDialog(ClubRouteItem.UIClubBossAward, { bossInfo: boss }, () => {
          this.refreshBossList();
        });
      },
      this
    );
    btn_haozhao.on(
      Node.EventType.TOUCH_END,
      (e) => {
        AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
        if (ClubModule.service.isInterceptOperation()) {
          return;
        }
        if (ClubModule.data.position != CLUB_POSITION.盟主) {
          TipsMgr.showTipX(163, [], "只有盟主才能召唤BOSS");
          return;
        }
        log.log(e);
        if (this._data.bossTrain.bossIndex >= 0) {
          TipsMgr.showTipX(164, [], "每天最多只能召唤一只BOSS");
          return;
        }
        ClubModule.api.callBoss(boss.id - 1, () => {
          TipsMgr.showTipX(165, [], "召唤成功");
          btn_go_fight.active = true;
          btn_haozhao.active = false;
        });
      },
      this
    );
    btn_go_fight.on(
      Node.EventType.TOUCH_END,
      () => {
        AudioMgr.instance.playEffect(ClubAudioName.Effect.战盟首领点击进入战斗按钮);
        if (ClubModule.service.isInterceptOperation()) {
          return;
        }
        this.goBoss(boss);
      },
      this
    );
  }

  protected onEvtShow(): void {
    super.onEvtShow();
    this.refreshBossList();
    // MsgMgr.on(ClubEvent.CLUB_BOSS_STATE_CHANGE, this.bossStateChange, this);
    // log.s
    this.getNode("bossAward").on(Node.EventType.SIZE_CHANGED, () => {
      let contentWidth = this.getNode("bossAward").getComponent(UITransform).width;
      this.getNode("task_head").getComponent(UITransform).width = contentWidth + 50;
      this.getNode("task_process_bkg").getComponent(UITransform).width = contentWidth;
      this.getNode("task_process")
        .getComponentsInChildren(Widget)
        .forEach((wiget) => {
          wiget.updateAlignment();
        });
      // this.getNode("task_head").setPosition(0, 0);
      log.log(this.getNode("bossAward").getComponent(UITransform).width);
    });
  }
  private goBoss(bossData: BossConfig) {
    let bossCallback = (res) => {
      log.log("战盟boss战斗的数据====", res);
      let data = JSON.parse(res.replay);
      let args: FightData = {
        fightData: data,
        win: res.isWin,
        clubBossInfo: res,
        resAddList: res.resAddList,
        // buddyList: this._buddyList,
      };
      UIMgr.instance.showDialog("UIClubFight", { data: args, bossId: bossData.id }, () => {
        this.refreshBossList();
        if (ClubModule.data.clubFormMessage.popUpMessage) {
          UIMgr.instance.back();
          ClubModule.service.showExitedTips();
        }
      });
    };
    if (ClubModule.data.bossChance.remainCnt > 0) {
      ClubModule.api.killBoss(false, bossCallback);
    } else if (ClubModule.data.bossChance.remainItemCnt > 0) {
      let dialogArgs: RouteShowArgs = {
        payload: {
          title: "提示",
          content: `是否要花‘${bossData.costList[1]}仙玉’购买一次额外战斗次数?\n<color=e10000>(今日剩余可额外战斗次数：${ClubModule.data.bossChance.remainItemCnt})</>`,
          callback: (isOK) => {
            if (isOK) {
              ClubModule.api.killBoss(true, bossCallback);
            }
          },
        },
      };
      RouteManager.uiRouteCtrl.showRoute(UIClubConfirmDialog, dialogArgs);
    } else {
      TipsMgr.showTipX(166, [], "今日已没有挑战次数");
    }
  }
  private checkIsTake(index: number, hurtRewardIndexList: number[]): boolean {
    let activeTask = hurtRewardIndexList;
    for (let i = 0; i < activeTask.length; i++) {
      if (activeTask[i] == index) {
        return true;
      }
    }
    return false;
  }
  private show_tips(node: Node, index: number, id: number) {
    let wolrd = node.getWorldPosition();
    let args = {
      worldx: wolrd.x,
      worldy: wolrd.y - 50,
      itemList: ClubModule.config.getClubBossAwardList(id),
      // keepMask: true,
    };
    log.log(args);
    this.getNode("UIClubTips").active = true;
    this.getNode("UIClubTips").getComponent(UIClubTips).setTips(args);
  }
  updateProcess(totalHurt: number, hurtRewardIndexList: number[]) {
    this.getNode("remain_challenge").getComponent(Label).string = `${ClubModule.data.bossChance.remainCnt}/3`;
    let harmAwardList = ClubModule.config.getClubBossHarmList();

    log.log(totalHurt);
    // totalHurt = 10000 * 1000 - 1; //10000 * 10000 * 9000;
    this.getNode("lbl_total_hurt").getComponent(Label).string = `${LangMgr.txMsgCode(168)}${Formate.format(totalHurt)}`;
    this.getNode("bossAward").destroyAllChildren();
    let index = -1;
    for (let i = 0; i < harmAwardList.length; i++) {
      let item = instantiate(this.getNode("p_normal"));
      item.name = `p_${i}`;
      if (totalHurt >= harmAwardList[i]) {
        item.getChildByName("select").active = true;
        item.getChildByName("light").active = true;
        index = i;
      }
      if (this.checkIsTake(i, hurtRewardIndexList)) {
        item.getChildByName("close").active = false;
        item.getChildByName("light").active = false;
        item.getChildByName("open").active = true;
      } else {
        item.getChildByName("close").active = true;
        item.getChildByName("open").active = false;
      }
      item.getChildByName("btn_click").on(
        Node.EventType.TOUCH_END,
        () => {
          if (ClubModule.service.isInterceptOperation()) {
            return;
          }
          if (item.getChildByName("select").active && !this.checkIsTake(i, hurtRewardIndexList)) {
            ClubModule.api.takeBossHurtReward(i, (data: ClubHurtRewardResponse) => {
              MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.resAddList });
              // hurtRewardIndexList.push(i); //将领取奖励的索引加入到列表
              this.updateProcess(totalHurt, data.hurtRewardTakeList);
            });
          } else if (!item.getChildByName("select").active) {
            this.show_tips(item, i, i + 1);
          } else {
            TipsMgr.showTipX(167, [], "已领取");
          }
        },
        this,
        true
      );
      item.getChildByName("label").getComponent(Label).string = `${Formate.format(harmAwardList[i])}`;
      this.getNode("bossAward").addChild(item);
    }

    log.log(`index is ${index}`);
    let littlePro = 0;
    if (index == -1) {
      let p1 = totalHurt;
      let pt = harmAwardList[0];
      littlePro = p1 / pt;
    } else if (index + 1 < harmAwardList.length) {
      let p1 = totalHurt - harmAwardList[index];
      let pt = harmAwardList[index + 1] - harmAwardList[index];
      log.log(p1, pt);
      littlePro = p1 / pt;
    } else {
      littlePro = 1;
    }
    this.getNode("task_process").getComponent(UITransform).width = 110 * (index + 1) + 110 * littlePro;
  }
  protected onEvtHide(): void {
    // MsgMgr.off(ClubEvent.CLUB_BOSS_STATE_CHANGE, this.bossStateChange, this);
  }
}
