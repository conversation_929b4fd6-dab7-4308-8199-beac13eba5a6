import { _decorator, Label, Node } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import ToolExt from "../../common/ToolExt";
import { JsonMgr } from "../../mgr/JsonMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import MsgMgr from "../../../lib/event/MsgMgr";
import { CityModule } from "../../../module/city/CityModule";
import MsgEnum from "../../event/MsgEnum";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import { ClubModule } from "../../../module/club/ClubModule";
import { NodeTool } from "../../../lib/utils/NodeTool";
import { BoutStartUp } from "../../BoutStartUp";
import { activityId, FrbpModule } from "../../../module/frbp/FrbpModule";
import GuideMgr from "../../../ext_guide/GuideMgr";
const { ccclass, property } = _decorator;

@ccclass("UIFrbpTs")
export class UIFrbpTs extends UINode {
  protected _openAct: boolean = true;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_HD_FRBP}?prefab/ui/UIFrbpTs`;
  }

  private _vo: any;

  public init(args: any): void {
    super.init(args);
    this._vo = args.vo;
  }

  protected async onEvtShow() {
    this._vo = await FrbpModule.data.getVO(
      activityId,
      () => {
        TipsMgr.setEnableTouch(true);
      },
      () => {
        UIMgr.instance.back();
        TipsMgr.setEnableTouch(true);
      }
    );
    TipsMgr.setEnableTouch(true);

    let jumplist = this._vo.jumplist;

    for (let i = 0; i < jumplist.length; i++) {
      let node = ToolExt.clone(this.getNode("btn_find"), this);
      this.getNode("layout_path").addChild(node);
      node.active = true;

      let jumpId = jumplist[i];
      this.setFind(node, jumpId);
    }
  }

  private setFind(node: Node, jumpId) {
    node["jumpId"] = jumpId;
    let db = JsonMgr.instance.getConfigJump(jumpId);
    if (!db) {
      node.active = false;
      return;
    }

    node["lbl_title"].getComponent(Label).string = db.des;
  }

  private on_click_btn_find(event) {
    let jumpId = event.node["jumpId"];
    let cfgJump = JsonMgr.instance.getConfigJump(jumpId);

    UIMgr.instance.back();
    // 弱提示类型
    if (cfgJump.pageName == "tips") {
      TipsMgr.showTip(cfgJump.args);
    } else if (cfgJump.type > 0) {
      // 主地图回到正常模式
      MsgMgr.emit(MsgEnum.ON_CITY_UN_FOCOUS);

      let args: any = {};
      if (cfgJump.id == 71) {
        args.buildId = CityModule.service.findMinCostLevelUpCityId();
      } else if (cfgJump.id == 72) {
        args.buildId = CityModule.service.findMinCostCallCityId();
      }
      GuideMgr.startGuide({ stepId: cfgJump.type, args: args });
    } else if (cfgJump.pageName == PlayerRouteName.UIItemUse) {
      UIMgr.instance.showDialog(PlayerRouteName.UIItemUse, { itemId: Number(cfgJump.args) });
    } else if (cfgJump.pageName == PlayerRouteName.UIItemJoinPop) {
      UIMgr.instance.showDialog(PlayerRouteName.UIItemJoinPop, { itemId: Number(cfgJump.args) });
    } else if (cfgJump.pageName) {
      // 主地图回到正常模式
      MsgMgr.emit(MsgEnum.ON_CITY_UN_FOCOUS);

      // 战盟商店
      if (cfgJump.id == 49) {
        if (!ClubModule.data.clubMessage) {
          TipsMgr.showTip("请先创建或加入战盟");
          return;
        }
      }

      // 模拟点击事件
      function clickNode(nodePageName: string, nodeClickName: string) {
        if (nodeClickName) {
          let nodeParent = NodeTool.findByName(BoutStartUp.instance.gameRoot, nodePageName);
          let nodeClick = NodeTool.findByName(nodeParent, nodeClickName);
          NodeTool.fakeClick(nodeClick);
        }
      }

      UIMgr.instance.showDialog(cfgJump.pageName, {}, null, () => {
        clickNode(cfgJump.pageName, cfgJump.nodeClick);
      });
    }
  }
}
