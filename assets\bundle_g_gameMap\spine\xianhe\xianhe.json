{"skeleton": {"hash": "ErqtAuCVeBgWXcY/iWqZZzZ9qis=", "spine": "3.8.75", "x": -213.85, "y": -86.77, "width": 264.7, "height": 153.14, "images": "./image/", "audio": "C:/特效素材/特效24.06.07/国风修仙技能特效spine骨骼动画源文件仙侠UI场景光效CG美/SP91 国风修仙类特效SPINE/场景特效/E100141"}, "bones": [{"name": "root", "scaleX": -1}, {"name": "bone", "parent": "root", "length": 62.51, "rotation": 142.07, "x": 1.79, "y": -70.53}, {"name": "2", "parent": "bone", "rotation": -142.07, "x": 38.59, "y": -58.17}, {"name": "3", "parent": "2", "length": 29.02, "rotation": 164, "x": -0.19, "y": -0.19}, {"name": "4", "parent": "3", "length": 22.62, "rotation": -29.67, "x": 29.02}, {"name": "5", "parent": "4", "length": 12.69, "rotation": 34.69, "x": 22.62}, {"name": "7", "parent": "2", "length": 29.92, "rotation": -30.23, "x": 12.28, "y": -5.21}, {"name": "8", "parent": "3", "length": 24.37, "rotation": -37.03, "x": 16.68, "y": -1.64}, {"name": "9", "parent": "8", "length": 33.21, "rotation": -8.11, "x": 24.37}, {"name": "1", "parent": "3", "length": 18.42, "rotation": -125.57, "x": 4.96, "y": -9.77}, {"name": "6", "parent": "1", "length": 26.72, "rotation": 5.52, "x": 18.42}, {"name": "bone2", "parent": "root", "length": 62.51, "rotation": 142.07, "x": 133.81, "y": -62.5}, {"name": "10", "parent": "bone2", "rotation": -142.07, "x": 38.59, "y": -58.17}, {"name": "11", "parent": "10", "length": 29.02, "rotation": 164, "x": -0.19, "y": -0.19}, {"name": "12", "parent": "11", "length": 22.62, "rotation": -29.67, "x": 29.02}, {"name": "13", "parent": "12", "length": 12.69, "rotation": 34.69, "x": 22.62}, {"name": "14", "parent": "11", "length": 24.37, "rotation": -37.03, "x": 16.68, "y": -1.64}, {"name": "15", "parent": "14", "length": 33.21, "rotation": -8.11, "x": 24.37}, {"name": "16", "parent": "11", "length": 18.42, "rotation": -125.57, "x": 4.96, "y": -9.77}, {"name": "17", "parent": "16", "length": 26.72, "rotation": 5.52, "x": 18.42}, {"name": "18", "parent": "10", "length": 29.92, "rotation": -30.23, "x": 12.28, "y": -5.21}, {"name": "bone3", "parent": "root", "length": 62.51, "rotation": 142.07, "x": 158.49, "y": -125.64}, {"name": "19", "parent": "bone3", "rotation": -142.07, "x": 38.59, "y": -58.17}, {"name": "20", "parent": "19", "length": 29.02, "rotation": 164, "x": -0.19, "y": -0.19}, {"name": "21", "parent": "20", "length": 22.62, "rotation": -29.67, "x": 29.02}, {"name": "22", "parent": "21", "length": 12.69, "rotation": 34.69, "x": 22.62}, {"name": "23", "parent": "20", "length": 24.37, "rotation": -37.03, "x": 16.68, "y": -1.64}, {"name": "24", "parent": "23", "length": 33.21, "rotation": -8.11, "x": 24.37}, {"name": "25", "parent": "20", "length": 18.42, "rotation": -125.57, "x": 4.96, "y": -9.77}, {"name": "26", "parent": "25", "length": 26.72, "rotation": 5.52, "x": 18.42}, {"name": "27", "parent": "19", "length": 29.92, "rotation": -30.23, "x": 12.28, "y": -5.21}], "slots": [{"name": "1", "bone": "1", "attachment": "1"}, {"name": "5", "bone": "16", "attachment": "1"}, {"name": "8", "bone": "25", "attachment": "1"}, {"name": "2", "bone": "7", "attachment": "2"}, {"name": "6", "bone": "18", "attachment": "2"}, {"name": "9", "bone": "27", "attachment": "2"}, {"name": "3", "bone": "8", "attachment": "3"}, {"name": "4", "bone": "14", "attachment": "3"}, {"name": "7", "bone": "23", "attachment": "3"}], "skins": [{"name": "default", "attachments": {"1": {"1": {"type": "mesh", "uvs": [0.0243, 0.66498, 0.01707, 0.7769, 0.17477, 0.89873, 0.43375, 1, 0.49885, 0.8874, 0.59868, 0.76273, 0.70162, 0.65153, 0.81591, 0.52545, 0.89259, 0.40078, 0.95191, 0.27895, 1, 0.12878, 1, 0, 0.93744, 0, 0.85353, 0.12595, 0.73345, 0.2152, 0.60468, 0.2917, 0.45566, 0.36112, 0.26757, 0.42203, 0.12723, 0.50278], "triangles": [5, 16, 6, 16, 15, 6, 6, 15, 7, 7, 15, 8, 8, 15, 14, 8, 14, 9, 14, 13, 9, 9, 13, 10, 13, 12, 10, 12, 11, 10, 3, 2, 4, 2, 17, 4, 2, 1, 0, 2, 0, 18, 2, 18, 17, 4, 17, 5, 5, 17, 16], "vertices": [2, 9, -2.95, 8.44, 0.96717, 10, -20.46, 10.46, 0.03283, 2, 9, -6.56, 4.45, 0.99791, 10, -24.43, 6.83, 0.00209, 2, 9, -4.39, -4.74, 0.99254, 10, -23.16, -2.53, 0.00746, 2, 9, 2.13, -16.12, 0.95235, 10, -17.77, -14.47, 0.04765, 2, 9, 7.88, -13.79, 0.84333, 10, -11.81, -12.71, 0.15667, 2, 9, 15.28, -12.01, 0.64881, 10, -4.28, -11.66, 0.35119, 2, 9, 22.39, -10.84, 0.40958, 10, 2.91, -11.17, 0.59042, 2, 9, 30.36, -9.44, 0.19758, 10, 10.97, -10.55, 0.80242, 2, 9, 36.9, -6.99, 0.06649, 10, 17.72, -8.74, 0.93351, 2, 9, 42.72, -4.15, 0.01257, 10, 23.79, -6.47, 0.98743, 2, 9, 48.97, 0.09, 0.00049, 10, 30.42, -2.85, 0.99951, 1, 10, 34.71, 1.6, 1, 2, 9, 50.51, 6.76, 2e-05, 10, 32.59, 3.65, 0.99998, 2, 9, 43.66, 4.48, 0.00986, 10, 25.56, 2.03, 0.99014, 2, 9, 36.58, 4.63, 0.06471, 10, 18.52, 2.87, 0.93529, 2, 9, 29.55, 5.52, 0.20153, 10, 11.62, 4.42, 0.79847, 2, 9, 22, 7.26, 0.42795, 10, 4.26, 6.89, 0.57205, 2, 9, 13.25, 10.47, 0.67548, 10, -4.13, 10.92, 0.32452, 2, 9, 5.68, 11.53, 0.8701, 10, -11.57, 12.71, 0.1299], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36], "width": 33, "height": 34}}, "2": {"2": {"type": "mesh", "uvs": [0, 0, 0.11632, 0, 0.18073, 0.07093, 0.20771, 0.16094, 0.25123, 0.25096, 0.30737, 0.29876, 0.36699, 0.29557, 0.43444, 0.26928, 0.517, 0.26017, 0.58177, 0.32664, 0.63085, 0.40928, 0.69563, 0.4524, 0.7604, 0.43264, 0.82518, 0.42006, 0.77316, 0.52427, 0.80948, 0.60152, 0.86051, 0.67518, 0.92684, 0.72733, 1, 0.7453, 1, 0.87106, 1, 1, 0.93666, 1, 0.87286, 0.91777, 0.80416, 0.84232, 0.72956, 0.7453, 0.68442, 0.68062, 0.60884, 0.63391, 0.52345, 0.62493, 0.43744, 0.60337, 0.35304, 0.56744, 0.302, 0.48659, 0.23722, 0.36622, 0.18676, 0.28213, 0.14554, 0.17613, 0.0788, 0.12403, 0, 0.07912], "triangles": [21, 19, 20, 21, 22, 19, 23, 16, 22, 22, 17, 19, 22, 16, 17, 17, 18, 19, 23, 15, 16, 23, 24, 15, 24, 25, 14, 24, 14, 15, 14, 11, 12, 11, 14, 25, 25, 26, 11, 26, 10, 11, 14, 12, 13, 26, 27, 10, 27, 9, 10, 9, 27, 8, 29, 6, 28, 6, 7, 28, 27, 28, 8, 28, 7, 8, 29, 30, 6, 6, 30, 5, 30, 31, 5, 31, 4, 5, 31, 32, 4, 32, 3, 4, 32, 33, 3, 33, 2, 3, 2, 33, 1, 33, 34, 1, 1, 34, 0, 34, 35, 0], "vertices": [1, 5, 14.62, -1.52, 1, 2, 5, 2.29, -3.91, 0.96822, 4, 26.73, -1.91, 0.03178, 1, 4, 18.87, -3.96, 1, 1, 4, 13.04, -2.34, 1, 1, 4, 5.96, -1.99, 1, 2, 4, -0.3, -4.35, 0.17025, 3, 26.61, -3.63, 0.82975, 1, 3, 20.47, -5.59, 1, 1, 3, 13.9, -9.09, 1, 2, 3, 5.47, -12.06, 0.99902, 6, -21.57, 8.41, 0.00098, 2, 3, -2.33, -10.22, 0.8975, 6, -13.55, 8.54, 0.1025, 2, 3, -8.77, -6.99, 0.4491, 6, -6.51, 7, 0.5509, 2, 3, -16.2, -6.48, 0.02531, 6, 0.81, 8.32, 0.97469, 1, 6, 6.27, 12.85, 1, 1, 6, 11.94, 17.02, 1, 1, 6, 10.18, 8.88, 1, 1, 6, 15.86, 6.91, 1, 1, 6, 22.82, 5.93, 1, 1, 6, 30.55, 6.88, 1, 1, 6, 37.91, 9.94, 1, 1, 6, 41.65, 3.53, 1, 1, 6, 45.48, -3.04, 1, 1, 6, 39.57, -6.48, 1, 1, 6, 31.17, -5.76, 1, 1, 6, 22.52, -5.65, 1, 1, 6, 12.68, -4.76, 1, 1, 6, 6.54, -3.92, 1, 2, 3, -10.14, 6.4, 0.22839, 6, -1.9, -5.65, 0.77161, 2, 3, -1.13, 8.43, 0.89825, 6, -10.13, -9.83, 0.10175, 1, 3, 8.15, 9.77, 1, 1, 3, 17.5, 10.24, 1, 1, 3, 24.11, 7.18, 1, 2, 4, 2.15, 3.85, 0.94877, 3, 32.8, 2.28, 0.05123, 1, 4, 9.51, 4.28, 1, 2, 5, -2.79, 5.69, 0.02036, 4, 17.09, 3.09, 0.97964, 2, 5, 4.87, 4.05, 0.90684, 4, 24.33, 6.1, 0.09316, 1, 5, 13.73, 3.07, 1], "hull": 36, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 0, 70], "width": 76, "height": 41}}, "3": {"3": {"type": "mesh", "uvs": [1, 0.99562, 0.56344, 0.99562, 0.46698, 0.84769, 0.28929, 0.73845, 0.22075, 0.61555, 0.17759, 0.41755, 0.13698, 0.24396, 0.07098, 0.12561, 0, 0, 0.21567, 0.05506, 0.36798, 0.15975, 0.47205, 0.29858, 0.60659, 0.42147, 0.76144, 0.52616, 0.91121, 0.66954, 1, 0.83568], "triangles": [3, 4, 12, 4, 11, 12, 4, 5, 11, 11, 6, 10, 11, 5, 6, 6, 9, 10, 6, 7, 9, 7, 8, 9, 1, 15, 0, 1, 14, 15, 1, 2, 14, 2, 13, 14, 2, 12, 13, 2, 3, 12], "vertices": [2, 7, -13.08, -9.94, 0.98749, 8, -35.67, -15.13, 0.01251, 2, 7, 0.58, 8.19, 0.9444, 8, -24.71, 4.75, 0.0556, 2, 7, 10.45, 7.04, 0.82268, 8, -14.77, 5, 0.17732, 2, 7, 21.07, 10.61, 0.6153, 8, -4.76, 10.04, 0.3847, 2, 7, 28.91, 9.17, 0.37273, 8, 3.2, 9.72, 0.62727, 2, 7, 39.43, 4.05, 0.16904, 8, 14.34, 6.14, 0.83096, 2, 7, 48.74, -0.31, 0.05188, 8, 24.18, 3.13, 0.94812, 2, 7, 56.29, -1.7, 0.00788, 8, 31.85, 2.82, 0.99212, 2, 7, 64.33, -3.13, 0.00128, 8, 40.01, 2.54, 0.99872, 2, 7, 55.03, -10.17, 0.02039, 8, 31.8, -5.74, 0.97961, 2, 7, 45.42, -12.85, 0.09076, 8, 22.66, -9.75, 0.90924, 2, 7, 35.73, -12.33, 0.24558, 8, 12.99, -10.6, 0.75442, 2, 7, 25.83, -13.63, 0.47219, 8, 3.37, -13.29, 0.52781, 2, 7, 16.14, -16.41, 0.70735, 8, -5.83, -17.41, 0.29265, 2, 7, 4.81, -17.63, 0.88092, 8, -16.87, -20.21, 0.11908, 2, 7, -5.67, -15.52, 0.9685, 8, -27.54, -19.61, 0.0315], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30], "width": 36, "height": 41}}, "4": {"3": {"type": "mesh", "uvs": [1, 0.99562, 0.56344, 0.99562, 0.46698, 0.84769, 0.28929, 0.73845, 0.22075, 0.61555, 0.17759, 0.41755, 0.13698, 0.24396, 0.07098, 0.12561, 0, 0, 0.21567, 0.05506, 0.36798, 0.15975, 0.47205, 0.29858, 0.60659, 0.42147, 0.76144, 0.52616, 0.91121, 0.66954, 1, 0.83568], "triangles": [3, 4, 12, 4, 11, 12, 4, 5, 11, 11, 6, 10, 11, 5, 6, 6, 9, 10, 6, 7, 9, 7, 8, 9, 1, 15, 0, 1, 14, 15, 1, 2, 14, 2, 13, 14, 2, 12, 13, 2, 3, 12], "vertices": [2, 16, -13.08, -9.94, 0.98749, 17, -35.67, -15.13, 0.01251, 2, 16, 0.58, 8.19, 0.9444, 17, -24.71, 4.75, 0.0556, 2, 16, 10.45, 7.04, 0.82268, 17, -14.77, 5, 0.17732, 2, 16, 21.07, 10.61, 0.6153, 17, -4.76, 10.04, 0.3847, 2, 16, 28.91, 9.17, 0.37273, 17, 3.2, 9.72, 0.62727, 2, 16, 39.43, 4.05, 0.16904, 17, 14.34, 6.14, 0.83096, 2, 16, 48.74, -0.31, 0.05188, 17, 24.18, 3.13, 0.94812, 2, 16, 56.29, -1.7, 0.00788, 17, 31.85, 2.82, 0.99212, 2, 16, 64.33, -3.13, 0.00128, 17, 40.01, 2.54, 0.99872, 2, 16, 55.03, -10.17, 0.02039, 17, 31.8, -5.74, 0.97961, 2, 16, 45.42, -12.85, 0.09076, 17, 22.66, -9.75, 0.90924, 2, 16, 35.73, -12.33, 0.24558, 17, 12.99, -10.6, 0.75442, 2, 16, 25.83, -13.63, 0.47219, 17, 3.37, -13.29, 0.52781, 2, 16, 16.14, -16.41, 0.70735, 17, -5.83, -17.41, 0.29265, 2, 16, 4.81, -17.63, 0.88092, 17, -16.87, -20.21, 0.11908, 2, 16, -5.67, -15.52, 0.9685, 17, -27.54, -19.61, 0.0315], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30], "width": 36, "height": 41}}, "5": {"1": {"type": "mesh", "uvs": [0.0243, 0.66498, 0.01707, 0.7769, 0.17477, 0.89873, 0.43375, 1, 0.49885, 0.8874, 0.59868, 0.76273, 0.70162, 0.65153, 0.81591, 0.52545, 0.89259, 0.40078, 0.95191, 0.27895, 1, 0.12878, 1, 0, 0.93744, 0, 0.85353, 0.12595, 0.73345, 0.2152, 0.60468, 0.2917, 0.45566, 0.36112, 0.26757, 0.42203, 0.12723, 0.50278], "triangles": [5, 16, 6, 16, 15, 6, 6, 15, 7, 7, 15, 8, 8, 15, 14, 8, 14, 9, 14, 13, 9, 9, 13, 10, 13, 12, 10, 12, 11, 10, 3, 2, 4, 2, 17, 4, 2, 1, 0, 2, 0, 18, 2, 18, 17, 4, 17, 5, 5, 17, 16], "vertices": [2, 18, -2.95, 8.44, 0.96717, 19, -20.46, 10.46, 0.03283, 2, 18, -6.56, 4.45, 0.99791, 19, -24.43, 6.83, 0.00209, 2, 18, -4.39, -4.74, 0.99254, 19, -23.16, -2.53, 0.00746, 2, 18, 2.13, -16.12, 0.95235, 19, -17.77, -14.47, 0.04765, 2, 18, 7.88, -13.79, 0.84333, 19, -11.81, -12.71, 0.15667, 2, 18, 15.28, -12.01, 0.64881, 19, -4.28, -11.66, 0.35119, 2, 18, 22.39, -10.84, 0.40958, 19, 2.91, -11.17, 0.59042, 2, 18, 30.36, -9.44, 0.19758, 19, 10.97, -10.55, 0.80242, 2, 18, 36.9, -6.99, 0.06649, 19, 17.72, -8.74, 0.93351, 2, 18, 42.72, -4.15, 0.01257, 19, 23.79, -6.47, 0.98743, 2, 18, 48.97, 0.09, 0.00049, 19, 30.42, -2.85, 0.99951, 1, 19, 34.71, 1.6, 1, 2, 18, 50.51, 6.76, 2e-05, 19, 32.59, 3.65, 0.99998, 2, 18, 43.66, 4.48, 0.00986, 19, 25.56, 2.03, 0.99014, 2, 18, 36.58, 4.63, 0.06471, 19, 18.52, 2.87, 0.93529, 2, 18, 29.55, 5.52, 0.20153, 19, 11.62, 4.42, 0.79847, 2, 18, 22, 7.26, 0.42795, 19, 4.26, 6.89, 0.57205, 2, 18, 13.25, 10.47, 0.67548, 19, -4.13, 10.92, 0.32452, 2, 18, 5.68, 11.53, 0.8701, 19, -11.57, 12.71, 0.1299], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36], "width": 33, "height": 34}}, "6": {"2": {"type": "mesh", "uvs": [0, 0, 0.11632, 0, 0.18073, 0.07093, 0.20771, 0.16094, 0.25123, 0.25096, 0.30737, 0.29876, 0.36699, 0.29557, 0.43444, 0.26928, 0.517, 0.26017, 0.58177, 0.32664, 0.63085, 0.40928, 0.69563, 0.4524, 0.7604, 0.43264, 0.82518, 0.42006, 0.77316, 0.52427, 0.80948, 0.60152, 0.86051, 0.67518, 0.92684, 0.72733, 1, 0.7453, 1, 0.87106, 1, 1, 0.93666, 1, 0.87286, 0.91777, 0.80416, 0.84232, 0.72956, 0.7453, 0.68442, 0.68062, 0.60884, 0.63391, 0.52345, 0.62493, 0.43744, 0.60337, 0.35304, 0.56744, 0.302, 0.48659, 0.23722, 0.36622, 0.18676, 0.28213, 0.14554, 0.17613, 0.0788, 0.12403, 0, 0.07912], "triangles": [21, 19, 20, 21, 22, 19, 23, 16, 22, 22, 17, 19, 22, 16, 17, 17, 18, 19, 23, 15, 16, 23, 24, 15, 24, 25, 14, 24, 14, 15, 14, 11, 12, 11, 14, 25, 25, 26, 11, 26, 10, 11, 14, 12, 13, 26, 27, 10, 27, 9, 10, 9, 27, 8, 29, 6, 28, 6, 7, 28, 27, 28, 8, 28, 7, 8, 29, 30, 6, 6, 30, 5, 30, 31, 5, 31, 4, 5, 31, 32, 4, 32, 3, 4, 32, 33, 3, 33, 2, 3, 2, 33, 1, 33, 34, 1, 1, 34, 0, 34, 35, 0], "vertices": [1, 15, 14.62, -1.52, 1, 2, 15, 2.29, -3.91, 0.96822, 14, 26.73, -1.91, 0.03178, 1, 14, 18.87, -3.96, 1, 1, 14, 13.04, -2.34, 1, 1, 14, 5.96, -1.99, 1, 2, 14, -0.3, -4.35, 0.17025, 13, 26.61, -3.63, 0.82975, 1, 13, 20.47, -5.59, 1, 1, 13, 13.9, -9.09, 1, 2, 13, 5.47, -12.06, 0.99902, 20, -21.57, 8.41, 0.00098, 2, 13, -2.33, -10.22, 0.8975, 20, -13.55, 8.54, 0.1025, 2, 13, -8.77, -6.99, 0.4491, 20, -6.51, 7, 0.5509, 2, 13, -16.2, -6.48, 0.02531, 20, 0.81, 8.32, 0.97469, 1, 20, 6.27, 12.85, 1, 1, 20, 11.94, 17.02, 1, 1, 20, 10.18, 8.88, 1, 1, 20, 15.86, 6.91, 1, 1, 20, 22.82, 5.93, 1, 1, 20, 30.55, 6.88, 1, 1, 20, 37.91, 9.94, 1, 1, 20, 41.65, 3.53, 1, 1, 20, 45.48, -3.04, 1, 1, 20, 39.57, -6.48, 1, 1, 20, 31.17, -5.76, 1, 1, 20, 22.52, -5.65, 1, 1, 20, 12.68, -4.76, 1, 1, 20, 6.54, -3.92, 1, 2, 13, -10.14, 6.4, 0.22839, 20, -1.9, -5.65, 0.77161, 2, 13, -1.13, 8.43, 0.89825, 20, -10.13, -9.83, 0.10175, 1, 13, 8.15, 9.77, 1, 1, 13, 17.5, 10.24, 1, 1, 13, 24.11, 7.18, 1, 2, 14, 2.15, 3.85, 0.94877, 13, 32.8, 2.28, 0.05123, 1, 14, 9.51, 4.28, 1, 2, 15, -2.79, 5.69, 0.02036, 14, 17.09, 3.09, 0.97964, 2, 15, 4.87, 4.05, 0.90684, 14, 24.33, 6.1, 0.09316, 1, 15, 13.73, 3.07, 1], "hull": 36, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 0, 70], "width": 76, "height": 41}}, "7": {"3": {"type": "mesh", "uvs": [1, 0.99562, 0.56344, 0.99562, 0.46698, 0.84769, 0.28929, 0.73845, 0.22075, 0.61555, 0.17759, 0.41755, 0.13698, 0.24396, 0.07098, 0.12561, 0, 0, 0.21567, 0.05506, 0.36798, 0.15975, 0.47205, 0.29858, 0.60659, 0.42147, 0.76144, 0.52616, 0.91121, 0.66954, 1, 0.83568], "triangles": [3, 4, 12, 4, 11, 12, 4, 5, 11, 11, 6, 10, 11, 5, 6, 6, 9, 10, 6, 7, 9, 7, 8, 9, 1, 15, 0, 1, 14, 15, 1, 2, 14, 2, 13, 14, 2, 12, 13, 2, 3, 12], "vertices": [2, 26, -13.08, -9.94, 0.98749, 27, -35.67, -15.13, 0.01251, 2, 26, 0.58, 8.19, 0.9444, 27, -24.71, 4.75, 0.0556, 2, 26, 10.45, 7.04, 0.82268, 27, -14.77, 5, 0.17732, 2, 26, 21.07, 10.61, 0.6153, 27, -4.76, 10.04, 0.3847, 2, 26, 28.91, 9.17, 0.37273, 27, 3.2, 9.72, 0.62727, 2, 26, 39.43, 4.05, 0.16904, 27, 14.34, 6.14, 0.83096, 2, 26, 48.74, -0.31, 0.05188, 27, 24.18, 3.13, 0.94812, 2, 26, 56.29, -1.7, 0.00788, 27, 31.85, 2.82, 0.99212, 2, 26, 64.33, -3.13, 0.00128, 27, 40.01, 2.54, 0.99872, 2, 26, 55.03, -10.17, 0.02039, 27, 31.8, -5.74, 0.97961, 2, 26, 45.42, -12.85, 0.09076, 27, 22.66, -9.75, 0.90924, 2, 26, 35.73, -12.33, 0.24558, 27, 12.99, -10.6, 0.75442, 2, 26, 25.83, -13.63, 0.47219, 27, 3.37, -13.29, 0.52781, 2, 26, 16.14, -16.41, 0.70735, 27, -5.83, -17.41, 0.29265, 2, 26, 4.81, -17.63, 0.88092, 27, -16.87, -20.21, 0.11908, 2, 26, -5.67, -15.52, 0.9685, 27, -27.54, -19.61, 0.0315], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30], "width": 36, "height": 41}}, "8": {"1": {"type": "mesh", "uvs": [0.0243, 0.66498, 0.01707, 0.7769, 0.17477, 0.89873, 0.43375, 1, 0.49885, 0.8874, 0.59868, 0.76273, 0.70162, 0.65153, 0.81591, 0.52545, 0.89259, 0.40078, 0.95191, 0.27895, 1, 0.12878, 1, 0, 0.93744, 0, 0.85353, 0.12595, 0.73345, 0.2152, 0.60468, 0.2917, 0.45566, 0.36112, 0.26757, 0.42203, 0.12723, 0.50278], "triangles": [5, 16, 6, 16, 15, 6, 6, 15, 7, 7, 15, 8, 8, 15, 14, 8, 14, 9, 14, 13, 9, 9, 13, 10, 13, 12, 10, 12, 11, 10, 3, 2, 4, 2, 17, 4, 2, 1, 0, 2, 0, 18, 2, 18, 17, 4, 17, 5, 5, 17, 16], "vertices": [2, 28, -2.95, 8.44, 0.96717, 29, -20.46, 10.46, 0.03283, 2, 28, -6.56, 4.45, 0.99791, 29, -24.43, 6.83, 0.00209, 2, 28, -4.39, -4.74, 0.99254, 29, -23.16, -2.53, 0.00746, 2, 28, 2.13, -16.12, 0.95235, 29, -17.77, -14.47, 0.04765, 2, 28, 7.88, -13.79, 0.84333, 29, -11.81, -12.71, 0.15667, 2, 28, 15.28, -12.01, 0.64881, 29, -4.28, -11.66, 0.35119, 2, 28, 22.39, -10.84, 0.40958, 29, 2.91, -11.17, 0.59042, 2, 28, 30.36, -9.44, 0.19758, 29, 10.97, -10.55, 0.80242, 2, 28, 36.9, -6.99, 0.06649, 29, 17.72, -8.74, 0.93351, 2, 28, 42.72, -4.15, 0.01257, 29, 23.79, -6.47, 0.98743, 2, 28, 48.97, 0.09, 0.00049, 29, 30.42, -2.85, 0.99951, 1, 29, 34.71, 1.6, 1, 2, 28, 50.51, 6.76, 2e-05, 29, 32.59, 3.65, 0.99998, 2, 28, 43.66, 4.48, 0.00986, 29, 25.56, 2.03, 0.99014, 2, 28, 36.58, 4.63, 0.06471, 29, 18.52, 2.87, 0.93529, 2, 28, 29.55, 5.52, 0.20153, 29, 11.62, 4.42, 0.79847, 2, 28, 22, 7.26, 0.42795, 29, 4.26, 6.89, 0.57205, 2, 28, 13.25, 10.47, 0.67548, 29, -4.13, 10.92, 0.32452, 2, 28, 5.68, 11.53, 0.8701, 29, -11.57, 12.71, 0.1299], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36], "width": 33, "height": 34}}, "9": {"2": {"type": "mesh", "uvs": [0, 0, 0.11632, 0, 0.18073, 0.07093, 0.20771, 0.16094, 0.25123, 0.25096, 0.30737, 0.29876, 0.36699, 0.29557, 0.43444, 0.26928, 0.517, 0.26017, 0.58177, 0.32664, 0.63085, 0.40928, 0.69563, 0.4524, 0.7604, 0.43264, 0.82518, 0.42006, 0.77316, 0.52427, 0.80948, 0.60152, 0.86051, 0.67518, 0.92684, 0.72733, 1, 0.7453, 1, 0.87106, 1, 1, 0.93666, 1, 0.87286, 0.91777, 0.80416, 0.84232, 0.72956, 0.7453, 0.68442, 0.68062, 0.60884, 0.63391, 0.52345, 0.62493, 0.43744, 0.60337, 0.35304, 0.56744, 0.302, 0.48659, 0.23722, 0.36622, 0.18676, 0.28213, 0.14554, 0.17613, 0.0788, 0.12403, 0, 0.07912], "triangles": [21, 19, 20, 21, 22, 19, 23, 16, 22, 22, 17, 19, 22, 16, 17, 17, 18, 19, 23, 15, 16, 23, 24, 15, 24, 25, 14, 24, 14, 15, 14, 11, 12, 11, 14, 25, 25, 26, 11, 26, 10, 11, 14, 12, 13, 26, 27, 10, 27, 9, 10, 9, 27, 8, 29, 6, 28, 6, 7, 28, 27, 28, 8, 28, 7, 8, 29, 30, 6, 6, 30, 5, 30, 31, 5, 31, 4, 5, 31, 32, 4, 32, 3, 4, 32, 33, 3, 33, 2, 3, 2, 33, 1, 33, 34, 1, 1, 34, 0, 34, 35, 0], "vertices": [1, 25, 14.62, -1.52, 1, 2, 25, 2.29, -3.91, 0.96822, 24, 26.73, -1.91, 0.03178, 1, 24, 18.87, -3.96, 1, 1, 24, 13.04, -2.34, 1, 1, 24, 5.96, -1.99, 1, 2, 24, -0.3, -4.35, 0.17025, 23, 26.61, -3.63, 0.82975, 1, 23, 20.47, -5.59, 1, 1, 23, 13.9, -9.09, 1, 2, 23, 5.47, -12.06, 0.99902, 30, -21.57, 8.41, 0.00098, 2, 23, -2.33, -10.22, 0.8975, 30, -13.55, 8.54, 0.1025, 2, 23, -8.77, -6.99, 0.4491, 30, -6.51, 7, 0.5509, 2, 23, -16.2, -6.48, 0.02531, 30, 0.81, 8.32, 0.97469, 1, 30, 6.27, 12.85, 1, 1, 30, 11.94, 17.02, 1, 1, 30, 10.18, 8.88, 1, 1, 30, 15.86, 6.91, 1, 1, 30, 22.82, 5.93, 1, 1, 30, 30.55, 6.88, 1, 1, 30, 37.91, 9.94, 1, 1, 30, 41.65, 3.53, 1, 1, 30, 45.48, -3.04, 1, 1, 30, 39.57, -6.48, 1, 1, 30, 31.17, -5.76, 1, 1, 30, 22.52, -5.65, 1, 1, 30, 12.68, -4.76, 1, 1, 30, 6.54, -3.92, 1, 2, 23, -10.14, 6.4, 0.22839, 30, -1.9, -5.65, 0.77161, 2, 23, -1.13, 8.43, 0.89825, 30, -10.13, -9.83, 0.10175, 1, 23, 8.15, 9.77, 1, 1, 23, 17.5, 10.24, 1, 1, 23, 24.11, 7.18, 1, 2, 24, 2.15, 3.85, 0.94877, 23, 32.8, 2.28, 0.05123, 1, 24, 9.51, 4.28, 1, 2, 25, -2.79, 5.69, 0.02036, 24, 17.09, 3.09, 0.97964, 2, 25, 4.87, 4.05, 0.90684, 24, 24.33, 6.1, 0.09316, 1, 25, 13.73, 3.07, 1], "hull": 36, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 0, 70], "width": 76, "height": 41}}}}], "animations": {"action": {"bones": {"8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 111.28, "curve": 0.25, "c3": 0.75}, {"time": 1.5}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.7333, "y": -0.906, "curve": 0.25, "c3": 0.75}, {"time": 1.5}]}, "7": {"rotate": [{"angle": 2.98}]}, "1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 28.18, "curve": 0.25, "c3": 0.75}, {"time": 1.5}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": -1.94, "y": 7.97, "curve": 0.25, "c3": 0.75}, {"time": 1.5}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": -0.695, "curve": 0.25, "c3": 0.75}, {"time": 1.5}], "shear": [{"curve": 0.25, "c3": 0.75}, {"time": 0.7333, "y": -42.82, "curve": 0.25, "c3": 0.75}, {"time": 1.5}]}, "9": {"rotate": [{"angle": -21.55, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 52.32, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -21.55}]}, "2": {"translate": [{"y": -2.17}]}, "bone": {"rotate": [{"angle": -2.08}], "translate": [{"x": -91.16, "y": 25.96}]}, "15": {"rotate": [{"angle": 50.52, "curve": 0.279, "c2": 0.12, "c3": 0.754}, {"time": 0.7333, "angle": -21.55}, {"time": 1.4333, "angle": 52.32, "curve": 0.312, "c3": 0.646, "c4": 0.35}, {"time": 1.5, "angle": 50.52}]}, "14": {"rotate": [{"angle": 108.57, "curve": 0.279, "c2": 0.12, "c3": 0.754}, {"time": 0.7333}, {"time": 1.4333, "angle": 111.28, "curve": 0.312, "c3": 0.646, "c4": 0.35}, {"time": 1.5, "angle": 108.57}], "scale": [{"y": -0.859, "curve": 0.279, "c2": 0.12, "c3": 0.754}, {"time": 0.7333}, {"time": 1.4333, "y": -0.906, "curve": 0.312, "c3": 0.646, "c4": 0.35}, {"time": 1.5, "y": -0.859}]}, "16": {"rotate": [{"angle": 27.5, "curve": 0.279, "c2": 0.12, "c3": 0.754}, {"time": 0.7333}, {"time": 1.4333, "angle": 28.18, "curve": 0.312, "c3": 0.646, "c4": 0.35}, {"time": 1.5, "angle": 27.5}], "translate": [{"x": -1.9, "y": 7.78, "curve": 0.279, "c2": 0.12, "c3": 0.754}, {"time": 0.7333}, {"time": 1.4333, "x": -1.94, "y": 7.97, "curve": 0.312, "c3": 0.646, "c4": 0.35}, {"time": 1.5, "x": -1.9, "y": 7.78}], "scale": [{"x": -0.653, "curve": 0.279, "c2": 0.12, "c3": 0.754}, {"time": 0.7333}, {"time": 1.4333, "x": -0.695, "curve": 0.312, "c3": 0.646, "c4": 0.35}, {"time": 1.5, "x": -0.653}], "shear": [{"y": -41.78, "curve": 0.279, "c2": 0.12, "c3": 0.754}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "y": -42.82, "curve": 0.312, "c3": 0.646, "c4": 0.35}, {"time": 1.5, "y": -41.78}]}, "18": {"rotate": [{"angle": 2.98}]}, "10": {"translate": [{"y": -2.17}]}, "bone2": {"rotate": [{"angle": -2.08}], "translate": [{"x": -91.16, "y": 25.96}]}, "24": {"rotate": [{"angle": 29.16, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 0.5, "angle": -21.55}, {"time": 1.2333, "angle": 52.32, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 1.5, "angle": 29.16}]}, "23": {"rotate": [{"angle": 76.4, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 111.28, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 1.5, "angle": 76.4}], "scale": [{"y": -0.308, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "y": -0.906, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 1.5, "y": -0.308}]}, "25": {"rotate": [{"angle": 19.35, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 28.18, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 1.5, "angle": 19.35}], "translate": [{"x": -1.33, "y": 5.47, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": -1.94, "y": 7.97, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 1.5, "x": -1.33, "y": 5.47}], "scale": [{"x": -0.163, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": -0.695, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 1.5, "x": -0.163}], "shear": [{"y": -29.4, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "y": -42.82, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 1.5, "y": -29.4}]}, "27": {"rotate": [{"angle": 2.98}]}, "19": {"translate": [{"y": -2.17}]}, "bone3": {"rotate": [{"angle": -2.08}], "translate": [{"x": -91.16, "y": 25.96}]}}}, "action2": {"slots": {"9": {"attachment": [{"name": null}]}, "8": {"attachment": [{"name": null}]}, "7": {"attachment": [{"name": null}]}}, "bones": {"8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 111.28, "curve": 0.25, "c3": 0.75}, {"time": 1.5}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.7333, "y": -0.906, "curve": 0.25, "c3": 0.75}, {"time": 1.5}]}, "7": {"rotate": [{"angle": 2.98}]}, "1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 28.18, "curve": 0.25, "c3": 0.75}, {"time": 1.5}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": -1.94, "y": 7.97, "curve": 0.25, "c3": 0.75}, {"time": 1.5}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": -0.695, "curve": 0.25, "c3": 0.75}, {"time": 1.5}], "shear": [{"curve": 0.25, "c3": 0.75}, {"time": 0.7333, "y": -42.82, "curve": 0.25, "c3": 0.75}, {"time": 1.5}]}, "9": {"rotate": [{"angle": -21.55, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 52.32, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -21.55}]}, "2": {"translate": [{"y": -2.17}]}, "bone": {"rotate": [{"angle": -2.08}], "translate": [{"x": -35.9, "y": 22.9}]}, "15": {"rotate": [{"angle": 50.52, "curve": 0.279, "c2": 0.12, "c3": 0.754}, {"time": 0.7333, "angle": -21.55}, {"time": 1.4333, "angle": 52.32, "curve": 0.312, "c3": 0.646, "c4": 0.35}, {"time": 1.5, "angle": 50.52}]}, "14": {"rotate": [{"angle": 108.57, "curve": 0.279, "c2": 0.12, "c3": 0.754}, {"time": 0.7333}, {"time": 1.4333, "angle": 111.28, "curve": 0.312, "c3": 0.646, "c4": 0.35}, {"time": 1.5, "angle": 108.57}], "scale": [{"y": -0.859, "curve": 0.279, "c2": 0.12, "c3": 0.754}, {"time": 0.7333}, {"time": 1.4333, "y": -0.906, "curve": 0.312, "c3": 0.646, "c4": 0.35}, {"time": 1.5, "y": -0.859}]}, "16": {"rotate": [{"angle": 27.5, "curve": 0.279, "c2": 0.12, "c3": 0.754}, {"time": 0.7333}, {"time": 1.4333, "angle": 28.18, "curve": 0.312, "c3": 0.646, "c4": 0.35}, {"time": 1.5, "angle": 27.5}], "translate": [{"x": -1.9, "y": 7.78, "curve": 0.279, "c2": 0.12, "c3": 0.754}, {"time": 0.7333}, {"time": 1.4333, "x": -1.94, "y": 7.97, "curve": 0.312, "c3": 0.646, "c4": 0.35}, {"time": 1.5, "x": -1.9, "y": 7.78}], "scale": [{"x": -0.653, "curve": 0.279, "c2": 0.12, "c3": 0.754}, {"time": 0.7333}, {"time": 1.4333, "x": -0.695, "curve": 0.312, "c3": 0.646, "c4": 0.35}, {"time": 1.5, "x": -0.653}], "shear": [{"y": -41.78, "curve": 0.279, "c2": 0.12, "c3": 0.754}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "y": -42.82, "curve": 0.312, "c3": 0.646, "c4": 0.35}, {"time": 1.5, "y": -41.78}]}, "18": {"rotate": [{"angle": 2.98}]}, "10": {"translate": [{"y": -2.17}]}, "bone2": {"rotate": [{"angle": -2.08}], "translate": [{"x": -98.28, "y": -39.02}]}, "24": {"rotate": [{"angle": 29.16, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 0.5, "angle": -21.55}, {"time": 1.2333, "angle": 52.32, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 1.5, "angle": 29.16}]}, "23": {"rotate": [{"angle": 76.4, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 111.28, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 1.5, "angle": 76.4}], "scale": [{"y": -0.308, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "y": -0.906, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 1.5, "y": -0.308}]}, "25": {"rotate": [{"angle": 19.35, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 28.18, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 1.5, "angle": 19.35}], "translate": [{"x": -1.33, "y": 5.47, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": -1.94, "y": 7.97, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 1.5, "x": -1.33, "y": 5.47}], "scale": [{"x": -0.163, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": -0.695, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 1.5, "x": -0.163}], "shear": [{"y": -29.4, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "y": -42.82, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 1.5, "y": -29.4}]}, "27": {"rotate": [{"angle": 2.98}]}, "19": {"translate": [{"y": -2.17}]}, "bone3": {"rotate": [{"angle": -2.08}], "translate": [{"x": -5.62, "y": 0.3}]}}}}}