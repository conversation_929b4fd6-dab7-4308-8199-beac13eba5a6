import { UIPlayerChangeName } from "../../game/ui/player/UIPlayerChangeName";
import { UIPlayerLevelUp } from "../../game/ui/player/UIPlayerLevelUp";
import { UIPlayerLevelUpRes } from "../../game/ui/player/UIPlayerLevelUpRes";
import { UIPlayerMsg } from "../../game/ui/player/UIPlayerMsg";
import { UIPlayerOtherMsg } from "../../game/ui/player/UIPlayerOtherMsg";
import { UIPlayerSkinTip } from "../../game/ui/player/UIPlayerSkinTip";
import { UIKnappsack } from "../../game/ui/ui_knappsack/UIKnappsack";
import { UIKnappsack_huodongMain_son } from "../../game/ui/ui_knappsack/UIKnappsack_huodongMain_son";
import { UIKnappsack_itemMain_son } from "../../game/ui/ui_knappsack/UIKnappsack_itemMain_son";
import { UIKnappsack_joinMain_son } from "../../game/ui/ui_knappsack/UIKnappsack_joinMain_son";
import { UIShopCenter } from "../../game/ui/ui_shop/UIShopCenter";
import { UIActivity7Day } from "../../game/ui/ui_territory/UIActivity7Day";
import { UITerritory } from "../../game/ui/ui_territory/UITerritory";
import { UIItemUse } from "../../game/ui/UIItemUse";
import { UIBuyConfirm } from "../../game/ui/UIBuyConfirm";
import { UICostConfirm } from "../../game/ui/UICostConfirm";
import { UIHelpPop } from "../../game/ui/UIHelpPop";
import { UIItemFetch } from "../../game/ui/UIItemFetch";
import { UIItemJoinPop } from "../../game/ui/ui_knappsack/UIItemJoinPop";

import { UIMask } from "../../game/ui/UIMask";
import { UIOffAward } from "../../game/ui/UIOffAward";
import { UIOpen } from "../../game/ui/UIOpen";
import { UIWatchSkill } from "../../game/ui/UIWatchSkill";
import { Recording, RecordingMap } from "../../lib/ui/recordingMap";
import { UIItemUseZx } from "../../game/ui/UIItemUseZx";
import { UINotice } from "../../game/ui/UINotice";
import { AudioName } from "db://assets/platform/src/AudioHelper";
import { UIPlayerJinJieYuLan } from "../../game/ui/player/UIPlayerJinJieYuLan";
import { UIQiYunNo } from "../../game/ui/UIQiYunNo";
import { UIThief } from "../../game/ui/UIThief";
import { UISkinOpen } from "../../game/ui/player/UISkinOpen";
import { UIStrategy } from "../../game/ui/ui_strategy/UIStrategy";
import { PlayerRouteName, PublicRouteName, ShopRouteName } from "./PlayerConstant";

export class PlayerRoute {
  rotueTables: Recording[] = [
    {
      node: UIMask,
      uiName: PlayerRouteName.UIMask,
      keep: true,
      relevanceUIList: [],
    },
    {
      node: UIKnappsack,
      uiName: PlayerRouteName.UIKnappsack,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIKnappsack_itemMain_son,
      uiName: PlayerRouteName.UIKnappsack_itemMain_son,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UIKnappsack_huodongMain_son,
      uiName: PlayerRouteName.UIKnappsack_huodongMain_son,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UIKnappsack_joinMain_son,
      uiName: PlayerRouteName.UIKnappsack_joinMain_son,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UITerritory,
      uiName: PlayerRouteName.UITerritory,
      keep: true,
      relevanceUIList: [],
      music: AudioName.Sound.府邸和三界通用,
    },

    {
      node: UIStrategy,
      uiName: PlayerRouteName.UIStrategy,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UIPlayerMsg,
      uiName: PlayerRouteName.UIPlayerMsg,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIPlayerChangeName,
      uiName: PlayerRouteName.UIPlayerChangeName,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIPlayerLevelUp,
      uiName: PlayerRouteName.UIPlayerLevelUp,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UISkinOpen,
      uiName: PlayerRouteName.UISkinOpen,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UIPlayerJinJieYuLan,
      uiName: PlayerRouteName.UIPlayerJinJieYuLan,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UIPlayerLevelUpRes,
      uiName: PlayerRouteName.UIPlayerLevelUpRes,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UIItemJoinPop,
      uiName: PlayerRouteName.UIItemJoinPop,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIPlayerOtherMsg,
      uiName: PlayerRouteName.UIPlayerOtherMsg,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIHelpPop,
      uiName: PlayerRouteName.UIHelpPop,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UICostConfirm,
      uiName: PublicRouteName.UICostConfirm,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIBuyConfirm,
      uiName: PublicRouteName.UIBuyConfirm,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIShopCenter,
      uiName: ShopRouteName.UIShopCenter,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UIWatchSkill,
      uiName: PlayerRouteName.UIWatchSkill,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIItemFetch,
      uiName: PlayerRouteName.UIItemFetch,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIOpen,
      uiName: PlayerRouteName.UIOpen,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIActivity7Day,
      uiName: PlayerRouteName.UIActivity7Day,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UIPlayerSkinTip,
      uiName: PlayerRouteName.UIPlayerSkinTip,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UIOffAward,
      uiName: PlayerRouteName.UIOffAward,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UIItemUse,
      uiName: PlayerRouteName.UIItemUse,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UIItemUseZx,
      uiName: PlayerRouteName.UIItemUseZx,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UINotice,
      uiName: PlayerRouteName.UINotice,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UIQiYunNo,
      uiName: PlayerRouteName.UIQiYunNo,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UIThief,
      uiName: PlayerRouteName.UIThief,
      keep: false,
      relevanceUIList: [],
    },
  ];

  public async init() {
    this.rotueTables.forEach((item) => {
      RecordingMap.instance.addRecording(item.uiName, item);
    });
  }
}
