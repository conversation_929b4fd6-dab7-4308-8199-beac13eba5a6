import { _decorator, instantiate, Label, Node, UI } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import StorageMgr, { StorageKeyEnum } from "../../../../platform/src/StorageHelper";
import GameHttpApi from "../../httpNet/GameHttpApi";
import { ItemCtrl } from "../../common/ItemCtrl";
import TipMgr from "../../../lib/tips/TipMgr";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { GoodsRouteName } from "../../../module/goods/GoodsRoute";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { ArrayUtils } from "../../../lib/utils/ArrayUtils";
import { HdTiaoJianLiBaoModule } from "../../../module/hd_tiaojianlibao/HdTiaoJianLiBaoModule";
import { WindowsPackBuyResponse } from "../../net/protocol/Activity";
import { TimeUtils } from "../../../lib/utils/TimeUtils";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { ActivityModule } from "../../../module/activity/ActivityModule";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
import FmUtils from "../../../lib/utils/FmUtils";
const log = Logger.getLoger(LOG_LEVEL.DEBUG);
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Thu Nov 07 2024 17:23:03 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/ui_hd_tiaojianlibao/UITiaoJianLiBao.ts
 *
 */
const ACTIVITY_ID = 10601;
@ccclass("UITiaoJianLiBao")
export class UITiaoJianLiBao extends UINode {
  protected _openAct: boolean = true; //打开动作
  private _tiaojianlibao_activity_info: any = null;
  private _uiList = [
    "",
    "node_zhanjiang",
    "node_youli",
    "node_linglongbaota",
    "node_jianzhu",
    "node_shenqi",
    "node_yueli",
  ];
  private _libaoName = ["", "战将突破礼包", "游历礼包", "玲珑宝鼎礼包", "建筑升级礼包", "至宝进阶礼包", "战将小升级包"];
  private _dialogList: number[] = []; //存储弹窗类型ID列表
  private _timeList: number[] = []; //弹窗类型对应的倒计时
  private _index = 0; //弹窗类型索引
  private _dialogMap: { [key: number]: any[] } = {}; //存储弹窗类型ID对应的数据结构
  private _typeMap = new Map<number, number[]>(); //存储窗口类型对应的礼包充值id
  private _redeemMap: { [key: number]: boolean } = {}; //充值信息
  private _itemId: number;

  private _dialogType = -1;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_HD_TIAOJIANLIBAO}?prefab/ui/UITiaoJianLiBao`;
  }
  //=================================================
  public init(args: any): void {
    super.init(args);
    this._itemId = args.itemId;
    if (!this._itemId) {
      this._itemId = 0;
    }
  }
  protected onEvtShow(): void {
    super.onEvtShow();
    this._tiaojianlibao_activity_info = ActivityModule.data.allActivityConfig[ACTIVITY_ID];
    log.debug("onEvtShow", this._tiaojianlibao_activity_info);

    // 数据分类
    this.refreshActivityInfo();

    // 查找当前itemId对应的弹窗类型
    this._dialogType = this.findDialogType();

    //
    this.requestData();
    MsgMgr.on(MsgEnum.ON_ACTIVITY_TIAOJIANLIBAO_BUY, this.onBuySuccess, this);
    // this.refreshUI();
  }
  protected onEvtHide(): void {
    MsgMgr.off(MsgEnum.ON_ACTIVITY_TIAOJIANLIBAO_BUY, this.onBuySuccess, this);
  }
  private onBuySuccess(rs: WindowsPackBuyResponse) {
    if (!this.node || !this.node.active) {
      log.warn("UI is not active");
      return;
    }
    TipMgr.showTip("购买成功");
    MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: rs.rewardList });
    this.requestData();
  }
  private requestData() {
    let data = HdTiaoJianLiBaoModule.data.windowPackMessage;
    log.log("请求数据", data);
    this._dialogList = [];
    this._timeList = [];
    Object.keys(data?.durationMap).forEach((key) => {
      log.log("活动---", this._typeMap[key], data.redeemList);
      if (ArrayUtils.isArraySubset(this._typeMap[key], data?.redeemList)) {
        log.log(`活动${key}-已购买`);
        // 如果在当前窗口购买了所有商品则将dialogType设置为-1，以便重新选择一个新的窗口类型进行展示
        // 假如这是最后一个可展示的类型设置为-1后将关闭窗口
        MsgMgr.emit(MsgEnum.ON_ACTIVITY_TIAOJIAN_UPDATE, data);
        if (this._dialogType == Number(key)) {
          this._dialogType = -1;
        }
      } else if (TimeUtils.serverTime < data.durationMap[key]) {
        this._dialogList.push(Number(key));
        this._timeList.push(data.durationMap[key]);
      }
    });
    data?.redeemList.forEach((item) => {
      this._redeemMap[item] = true;
    });
    if (this.findIndex()) {
      this._itemId = 0;
    }
    this.refreshUI();
    // WindowsPackBuyResponse
  }

  private findDialogType() {
    let dialogType = -1;
    //查找传入的itemId对应的弹窗礼包类型
    for (let i = 0; i < this._tiaojianlibao_activity_info.packList.length; i++) {
      for (let j = 0; j < this._tiaojianlibao_activity_info.packList[i].itemIdList.length; j++) {
        if (this._itemId == this._tiaojianlibao_activity_info.packList[i].itemIdList[j]) {
          dialogType = this._tiaojianlibao_activity_info.packList[i].type;
          return dialogType;
        }
      }
    }
    return dialogType;
  }
  private refreshActivityInfo() {
    for (let i = 0; i < this._tiaojianlibao_activity_info.packList.length; i++) {
      //将礼包充值ID按弹窗类型进行分组
      if (this._typeMap[this._tiaojianlibao_activity_info.packList[i].type]) {
        this._typeMap[this._tiaojianlibao_activity_info.packList[i].type].push(
          this._tiaojianlibao_activity_info.packList[i].id
        );
      } else {
        this._typeMap[this._tiaojianlibao_activity_info.packList[i].type] = [
          this._tiaojianlibao_activity_info.packList[i].id,
        ];
      }
      //将礼包按弹窗类型分组
      if (!this._dialogMap[this._tiaojianlibao_activity_info.packList[i].type]) {
        this._dialogMap[this._tiaojianlibao_activity_info.packList[i].type] = [];
      }
      this._dialogMap[this._tiaojianlibao_activity_info.packList[i].type].push(
        this._tiaojianlibao_activity_info.packList[i]
      );
    }
  }
  private refreshUI() {
    try {
      let dialogType = this._dialogList[this._index];
      if (!dialogType) {
        UIMgr.instance.back();
        return;
      }
      for (let i = 1; i < this._uiList.length; i++) {
        this.getNode(this._uiList[i]).active = false;
      }
      this.getNode(this._uiList[dialogType]).active = true;

      this.setPrices();

      let itemId = this._dialogMap[dialogType][this.getItemLevelIndex()].id;
      if (this._redeemMap[itemId]) {
        this.getNode("bg_yigoumai").active = true;
        this.getNode("btn_buy").active = false;
      } else {
        this.getNode("bg_yigoumai").active = false;
        this.getNode("btn_buy").active = true;
      }
      let itemIndex = this.getItemLevelIndex();
      let rewardList = this._dialogMap[dialogType][itemIndex].rewardList;
      if (this._timeList[this._index]) {
        FmUtils.setCd(this.getNode("LblCd"), this._timeList[this._index], false, () => {
          this.requestData();
        });
      } else {
        this.getNode("LblCd").active = false;
      }
      this.getNode(this._uiList[dialogType]).getChildByName("node_awards").removeAllChildren();
      for (let i = 0; i < rewardList.length; i++) {
        let item = instantiate(this.getNode("Item"));
        item.active = true;
        this.getNode(this._uiList[dialogType]).getChildByName("node_awards").addChild(item);
        item.getComponent(ItemCtrl).setItemId(rewardList[i++], rewardList[i]);
      }
      if (this._dialogList.length > 1) {
        this.getNode("node_switch").active = true;
        this.getNode("node_indicator").active = true;
        this.refreshIndicator();
      } else {
        this.getNode("node_switch").active = false;
        this.getNode("node_indicator").active = false;
      }
    } catch (error) {
      log.error(error);
    }
  }
  private refreshIndicator() {
    while (this.getNode("node_indicator").getChildByName("node_points").children.length < this._dialogList.length) {
      let point = instantiate(this.getNode("node_point"));
      point.active = true;
      this.getNode("node_indicator").getChildByName("node_points").addChild(point);
    }
    this.getNode("node_indicator")
      .getChildByName("node_points")
      .children.forEach((node: Node, index: number) => {
        if (index == this._index) {
          node.getChildByName("bg_point_light").active = true;
        } else {
          node.getChildByName("bg_point_light").active = false;
        }
      });
  }

  /**
   * 查找对应弹窗类型的Index，如果dialogType不为-1且找不到对应的弹窗类型则插入一条新的记录并向服务器提交请求
   * @param dialogType
   * @returns
   */
  private findIndex(): boolean {
    log.log("findIndex", this._dialogType);
    if (this._dialogType == -1) {
      //当没有传入道具ID打开这个页面的时候，值为-1
      try {
        let storageValue = ActivityModule.data.allActivityConfig[ACTIVITY_ID] as any;
        let typeMap = new Map<number, number[]>(); //存储窗口类型对应的礼包充值id
        for (let i = 0; i < storageValue.packList.length; i++) {
          if (typeMap[storageValue.packList[i].type]) {
            typeMap[storageValue.packList[i].type].push(storageValue.packList[i].id);
          } else {
            typeMap[storageValue.packList[i].type] = [storageValue.packList[i].id];
          }
        }
        let data = HdTiaoJianLiBaoModule.data.windowPackMessage;
        let minCountDown = 0;
        Object.keys(data.durationMap).forEach((k, index) => {
          //获取大于当前时间的最小倒计时
          let v = data.durationMap[k];
          let isAllCharge = ArrayUtils.isArraySubset(typeMap[k], data.redeemList);
          if (!isAllCharge && v > TimeUtils.serverTime && (minCountDown == 0 || v < minCountDown)) {
            minCountDown = v;
            this._dialogType = Number(k);
          }
        });
      } catch (error) {
        log.error("查找类型错误", error);
        this._index = 0;
      }
    }
    for (let i = 0; i < this._dialogList.length; i++) {
      // 如果当前弹窗类型在列表中，则直接返回
      if (this._dialogList[i] == this._dialogType) {
        this._index = i;
        return true;
      }
    }
    if (this._dialogType == -1) {
      return false;
    }

    // 如果当前弹窗类型不在列表中，则向服务器提交请求
    HdTiaoJianLiBaoModule.api.recordWindowUp(
      ACTIVITY_ID,
      this._dialogType,
      () => {
        //成功回调
        this._dialogList.push(this._dialogType);
        this.requestData();
      },
      () => {
        //失败回调
        UIMgr.instance.back();
        return true;
      }
    );
    return false;
    // this.findIndex(dialogType);
  }

  /**
   * 获得充值等级6,30,98
   * @returns
   */
  private getItemLevelIndex(): number {
    if (!this.getNode("btn_0_unselect").active) {
      return 0;
    }
    if (!this.getNode("btn_1_unselect").active) {
      return 1;
    }
    if (!this.getNode("btn_2_unselect").active) {
      return 2;
    }
    return 0;
  }
  private setPrices() {
    try {
      this.getNode("btn_buy")?.children?.forEach((child) => {
        child.active = false;
      });
      let dialogType = this._dialogList[this._index];
      let priceMap: { [key: number]: number } = {};
      let i = 0;
      for (; i < this._dialogMap[dialogType].length && i < 3; i++) {
        if (priceMap[`${this._dialogMap[dialogType][i].price}`]) {
          break;
        }

        this.getNode(`node_tab_${i}`).active = true;
        log.log(`btn_${i}_unselect`);
        log.log(this.getNode(`btn_${i}_unselect`));
        log.log(this.getNode(`btn_${i}_unselect`).getChildByPath("node_price/lbl_price"));
        this.getNode(`btn_${i}_unselect`).getChildByPath("node_price/lbl_price").getComponent(Label).string = `${
          this._dialogMap[dialogType][i].price % 10000
        }`;
        this.getNode(`btn_${i}_select`).getChildByPath("node_price/lbl_price").getComponent(Label).string = `${
          this._dialogMap[dialogType][i].price % 10000
        }`;
        priceMap[`${this._dialogMap[dialogType][i].price}`] = 1;
      }

      if (i == 1) {
        this.getNode("node_tab").active = false;
      } else {
        this.getNode("node_tab").active = true;
        for (; i < this.getNode("node_tab").children.length; i++) {
          this.getNode("node_tab").children[i].active = false;
        }
      }
      let index = this.getItemLevelIndex();
      if (index >= this._dialogMap[dialogType].length) {
        this.refreshBtnSelectState(0);
        index = this.getItemLevelIndex();
      }
      let price = this._dialogMap[dialogType][index].price % 10000;
      this.getNode("btn_buy").getChildByName("label_" + price).active = true;
    } catch (error) {
      log.error(error);
    }
  }
  private refreshBtnSelectState(index: number) {
    this.getNode("btn_0_unselect").active = true;
    this.getNode("btn_1_unselect").active = true;
    this.getNode("btn_2_unselect").active = true;
    this.getNode(`btn_${index}_unselect`).active = false;
  }
  private on_click_btn_0_unselect() {
    AudioMgr.instance.playEffect(1841);
    this.getNode("btn_0_unselect").active = false;
    this.getNode("btn_1_unselect").active = true;
    this.getNode("btn_2_unselect").active = true;
    this.refreshUI();
  }
  private on_click_btn_1_unselect() {
    AudioMgr.instance.playEffect(1841);
    this.getNode("btn_0_unselect").active = true;
    this.getNode("btn_1_unselect").active = false;
    this.getNode("btn_2_unselect").active = true;
    this.refreshUI();
  }
  private on_click_btn_2_unselect() {
    AudioMgr.instance.playEffect(1841);
    this.getNode("btn_0_unselect").active = true;
    this.getNode("btn_1_unselect").active = true;
    this.getNode("btn_2_unselect").active = false;
    this.refreshUI();
  }

  private on_click_btn_close() {
    UIMgr.instance.back();
  }
  private on_click_btn_next() {
    this._index++;
    if (this._index >= this._dialogList.length) {
      this._index = 0;
    }
    this.refreshUI();
  }
  private on_click_btn_last() {
    this._index--;
    if (this._index < 0) {
      this._index = this._dialogList.length - 1;
    }
    this.refreshUI();
  }
  private on_click_btn_buy() {
    AudioMgr.instance.playEffect(1842);
    //
    let dialogType = this._dialogList[this._index];
    let goodsId = ACTIVITY_ID * 10000 + this._dialogMap[dialogType][this.getItemLevelIndex()].id;
    let price = this._dialogMap[dialogType][this.getItemLevelIndex()].price % 10000;
    let goodsName = this._libaoName[dialogType];
    GameHttpApi.pay({
      goodsId: goodsId,
      goodsType: 7,
      count: 1,
      playerId: PlayerModule.data.playerId,
      orderAmount: price,
      goodsName: goodsName,
      platformType: "TEST",
    }).then((resp: any) => {
      log.debug("pay resp");
      if (resp.code != 200) {
        log.error(resp);
        return;
      }
      // window.open(resp.data.url);
      UIMgr.instance.showDialog(GoodsRouteName.UIPay, { url: resp.data.url });
    });
  }
}
