import { _decorator, Node } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { FarmModule } from "../../../module/farm/FarmModule";
import { Label } from "cc";
import Formate from "../../../lib/utils/Formate";
import { ProgressBar } from "cc";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { FarmRouteName } from "../../../module/farm/FarmRoute";
import GameHttpApi from "../../httpNet/GameHttpApi";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { GoodsModule } from "../../../module/goods/GoodsModule";
import { GoodsRouteName } from "../../../module/goods/GoodsRoute";
import { FarmFundMessage, FundRewardResponse } from "../../net/protocol/Farm";
import MsgMgr from "../../../lib/event/MsgMgr";
import { FarmEvent } from "../../../module/farm/FarmEvent";
import { UIClubTips } from "../../common/UIClubTips";
import { LangMgr } from "../../mgr/LangMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { FarmAudioName } from "../../../module/farm/FarmConfig";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import MsgEnum from "../../event/MsgEnum";

const log = Logger.getLoger(LOG_LEVEL.DEBUG);
const { ccclass, property } = _decorator;

@ccclass("UIFarmCjjj")
export class UIFarmCjjj extends UINode {
  protected _openAct: boolean = true;

  selectTabId: number;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_FARM}?prefab/ui/UIFarmCjjj`;
  }

  private get fundList() {
    return FarmModule.data.farmTrainMessage.fundList || [];
  }

  protected onRegEvent() {
    MsgMgr.on(MsgEnum.FARM_FUND_BUY_SUCCESS, this.subFarmFundBuy, this);
  }
  protected onDelEvent() {
    MsgMgr.off(MsgEnum.FARM_FUND_BUY_SUCCESS, this.subFarmFundBuy, this);
  }

  protected onEvtShow(): void {
    this.getNode("UIClubTips").active = false;

    let fundList = this.fundList;
    for (let idx = 0; idx < fundList.length; idx++) {
      let item = fundList[idx];
      if (!item.take) {
        this.onSwitchTab(idx + 1);
        return;
      }
    }
  }

  protected onEvtClose(): void {}

  private subFarmFundBuy() {
    TipsMgr.setEnableTouch(true);
    this.onSwitchTab(this.selectTabId);
  }

  // 切换tab
  private onSwitchTab(tabId: number) {
    this.selectTabId = tabId;

    this.getNode("btn_lv1").getChildByName("bg_unactive").active = tabId != 1;
    this.getNode("btn_lv1").getChildByName("bg_active").active = tabId == 1;
    this.getNode("btn_lv2").getChildByName("bg_unactive").active = tabId != 2;
    this.getNode("btn_lv2").getChildByName("bg_active").active = tabId == 2;
    this.getNode("btn_lv3").getChildByName("bg_unactive").active = tabId != 3;
    this.getNode("btn_lv3").getChildByName("bg_active").active = tabId == 3;

    this.getNode("bg_lv1").active = tabId == 1;
    this.getNode("bg_lv2").active = tabId == 2;
    this.getNode("bg_lv3").active = tabId == 3;

    this.setProgress(tabId);
  }

  private setProgress(tabId: number) {
    let fund: FarmFundMessage = this.fundList[tabId - 1];
    if (!fund) {
      log.error("setProgress fund is null");
      return;
    }

    let cfgFund = FarmModule.data.getConfigBlessLandFund(tabId);

    this.getNode("lbl_cur_award").getComponent(Label).string = String(cfgFund.rewardList[0][1]);
    // 进度条上面的提示信息
    this.getNode("lbl_hint").getComponent(Label).string = LangMgr.txMsgCode(230, [
      Formate.format(100 / cfgFund.fetchTime),
    ]);

    // 进度
    let progress = fund.count / cfgFund.fetchTime;
    progress = Math.min(1, progress);

    // 进度条
    this.getNode("progress_bar").getComponent(ProgressBar).progress = progress;

    // 当前进度
    this.getNode("lbl_progress").getComponent(Label).string = `${Formate.format(progress * 100)}%`;

    this.getNode("btn_buy").getChildByName("Label").getComponent(Label).string = LangMgr.txMsgCode(461, [
      cfgFund.price % 10000,
    ]);

    // 按钮状态
    this.getNode("btn_buy").active = false;
    this.getNode("btn_do").active = false;
    this.getNode("btn_collect").active = false;
    this.getNode("bg_finish").active = false;
    // 未购买
    if (fund.recharge == false) {
      this.getNode("btn_buy").active = true;
    } else {
      // 未完成
      if (progress < 1) {
        this.getNode("btn_do").active = true;
      } else {
        // 未领取
        if (fund.take == false) {
          this.getNode("btn_collect").active = true;
        } else {
          // 已领取
          this.getNode("bg_finish").active = fund.take;
        }
      }
    }
  }

  private on_click_btn_lv1() {
    AudioMgr.instance.playEffect(FarmAudioName.Effect.采集基金进行葫芦品质切换);
    this.onSwitchTab(1);
  }

  private on_click_btn_lv2() {
    AudioMgr.instance.playEffect(FarmAudioName.Effect.采集基金进行葫芦品质切换);
    this.onSwitchTab(2);
  }

  private on_click_btn_lv3() {
    AudioMgr.instance.playEffect(FarmAudioName.Effect.采集基金进行葫芦品质切换);
    this.onSwitchTab(3);
  }

  private on_click_btn_do() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    // 去采集
    UIMgr.instance.showDialog(FarmRouteName.UIFarmFind);
  }

  private on_click_btn_buy() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let goodsId = -1;
    let orderAmount = "";
    let cfgFund = FarmModule.data.getConfigBlessLandFund(this.selectTabId);

    if (this.selectTabId == 2) {
      goodsId = 20;
    } else if (this.selectTabId == 3) {
      goodsId = 30;
    } else {
      return;
    }

    orderAmount = GoodsModule.service.getPayInfo(cfgFund.price).substring(1);

    TipsMgr.setEnableTouch(false, 3);
    GameHttpApi.pay({
      goodsId,
      goodsType: 1,
      playerId: PlayerModule.data.playerId,
      orderAmount,
      goodsName: cfgFund.name,
      platformType: "TEST",
    }).then((resp: any) => {
      if (resp.code != 200) {
        TipsMgr.showErrX(resp.code, [resp.msg]);
        return;
      }
      UIMgr.instance.showDialog(GoodsRouteName.UIPay, { url: resp.data.url }, (resp) => {
        if (resp.ok) {
          TipsMgr.setEnableTouch(false, 7);
        }
      });
    });
  }

  private on_click_btn_collect() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    FarmModule.api.takeFundReward(this.selectTabId - 1, (data: FundRewardResponse) => {
      this.onSwitchTab(this.selectTabId);
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardList });
    });
  }

  private on_click_btn_reward() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let cfgFund = FarmModule.data.getConfigBlessLandFund(this.selectTabId);

    let rewardListConvert = [];
    for (let idx in cfgFund.rewardList) {
      rewardListConvert.push(cfgFund.rewardList[idx][0]);
      rewardListConvert.push(cfgFund.rewardList[idx][1]);
    }
    this.showTips(this.getNode("btn_reward"), rewardListConvert);
  }

  private showTips(node: Node, itemList: number[]) {
    let wolrd = node.getWorldPosition();
    let args = {
      worldx: wolrd.x,
      worldy: wolrd.y - 50,
      itemList: itemList,
      // keepMask: true,
    };
    log.log(args);
    this.getNode("UIClubTips").active = true;
    this.getNode("UIClubTips").getComponent(UIClubTips).setTips(args);
  }
}
