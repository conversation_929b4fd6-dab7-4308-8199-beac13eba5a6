[{"__type__": "cc.Prefab", "_name": "city_103", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "city_103", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 12}, {"__id__": 34}, {"__id__": 83}, {"__id__": 93}, {"__id__": 113}, {"__id__": 140}, {"__id__": 150}], "_active": true, "_components": [{"__id__": 156}, {"__id__": 158}, {"__id__": 160}], "_prefab": {"__id__": 163}, "_lpos": {"__type__": "cc.Vec3", "x": -1010.99, "y": -369.763, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btn_manage", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}], "_active": true, "_components": [{"__id__": 9}], "_prefab": {"__id__": 11}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "ani", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}, {"__id__": 6}], "_prefab": {"__id__": 8}, "_lpos": {"__type__": "cc.Vec3", "x": -0.473, "y": -62.611, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1.1, "y": 1.1, "z": 1}, "_mobility": 0, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 5}, "_contentSize": {"__type__": "cc.Size", "width": 265.94000244140625, "height": 265.94000244140625}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5009400616594154, "y": 0.16797021041912383}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2bYIp/BpREzJTxyOMr2F8H"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 7}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "f5a25c7a-81ef-47c5-b93a-4d0827e24e81", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "a06-1", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a6bfvNd1dLs6njm4D6rNSj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "92BFp2ZRNMb4oIzsYDT4YK", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 10}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "38/fDfleZCYrpjuBPiZoDC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8dowRZGzdBu6sVjImz0lwT", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_effect_ruins", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 13}, {"__id__": 19}, {"__id__": 25}], "_active": false, "_components": [{"__id__": 31}], "_prefab": {"__id__": 33}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 50, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "flame", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 12}, "_children": [], "_active": true, "_components": [{"__id__": 14}, {"__id__": 16}], "_prefab": {"__id__": 18}, "_lpos": {"__type__": "cc.Vec3", "x": 7.415, "y": -24.195, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0.3826834323650898, "w": 0.9238795325112867}, "_lscale": {"__type__": "cc.Vec3", "x": 1.5, "y": 1.5, "z": 1}, "_mobility": 0, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 45}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 15}, "_contentSize": {"__type__": "cc.Size", "width": 183.0399932861328, "height": 164.27000427246094}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.320367141283604, "y": 0.17501673617975183}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "06iHfVOIJDsYIlL6D/nD7P"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 17}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "4b153260-6a6f-4e95-a017-000fd418531c", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "action1", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "21dAaLTHRHmYimzRaTQWmw"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6fe2kVJARK3ZGoqGWEsuMh", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "flame", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 12}, "_children": [], "_active": true, "_components": [{"__id__": 20}, {"__id__": 22}], "_prefab": {"__id__": 24}, "_lpos": {"__type__": "cc.Vec3", "x": -75.259, "y": -40.993, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": -0.3826834323650898, "w": 0.9238795325112867}, "_lscale": {"__type__": "cc.Vec3", "x": -1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -45}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": {"__id__": 21}, "_contentSize": {"__type__": "cc.Size", "width": 183.0399932861328, "height": 164.27000427246094}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.320367141283604, "y": 0.17501673617975183}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6cF6JlWM9LxbRp6TcuzG6q"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": {"__id__": 23}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "4b153260-6a6f-4e95-a017-000fd418531c", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "action1", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c90RkEJZBG9LibqGZBfsvR"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5cXagvlwFN25w7dAPJBQ/9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "flame", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 12}, "_children": [], "_active": true, "_components": [{"__id__": 26}, {"__id__": 28}], "_prefab": {"__id__": 30}, "_lpos": {"__type__": "cc.Vec3", "x": 87.806, "y": -42.273, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0.3826834323650898, "w": 0.9238795325112867}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 45}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": {"__id__": 27}, "_contentSize": {"__type__": "cc.Size", "width": 183.0399932861328, "height": 164.27000427246094}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.320367141283604, "y": 0.17501673617975183}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5cbZQrKk5FcJlUpmCusqye"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": {"__id__": 29}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "4b153260-6a6f-4e95-a017-000fd418531c", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "action1", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "50GFuxvL9K6YRdR2ANoPoP"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "39h8ENN5BL2rANeTydpVRy", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 32}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "189pKNH/tF6qfh+/LXIzmQ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d54vskGXZPUbCE8h83dhfe", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_build", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 35}, {"__id__": 41}, {"__id__": 52}, {"__id__": 72}], "_active": true, "_components": [{"__id__": 78}, {"__id__": 80}], "_prefab": {"__id__": 82}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 50, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.9999999999999991, "y": 0.9999999999999991, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "build", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 34}, "_children": [], "_active": true, "_components": [{"__id__": 36}, {"__id__": 38}], "_prefab": {"__id__": 40}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 15.999999999999886, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.999999999999999, "y": 0.999999999999999, "z": 0.999999999999999}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 35}, "_enabled": true, "__prefab": {"__id__": 37}, "_contentSize": {"__type__": "cc.Size", "width": 107, "height": 101}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2b2h05FxNIDbGeQboc+NcB"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 35}, "_enabled": true, "__prefab": {"__id__": 39}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "996d4d40-9078-4947-95a1-bc7edb418170@f2df0", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": {"__uuid__": "996d4d40-9078-4947-95a1-bc7edb418170", "__expectedType__": "cc.SpriteAtlas"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ecNYFbh0pNeordK+nTfe7F"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1fIMjquKpNWKPSSvsphQe6", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 34}, "_prefab": {"__id__": 42}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 41}, "asset": {"__uuid__": "21255ee7-bab1-4ea5-9b60-556815704f09", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 43}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "87gssQF0FAXYjLS9IjCtSV", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 44}, {"__id__": 46}, {"__id__": 47}, {"__id__": 48}, {"__id__": 49}, {"__id__": 50}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 45}, "propertyPath": ["_name"], "value": "Badge"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 45}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 25, "y": 45, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 45}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 45}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 45}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 51}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.TargetInfo", "localID": ["d62NA3LF1OTZbxn67P5fPN"]}, {"__type__": "cc.Node", "_name": "Layout", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 34}, "_children": [{"__id__": 53}, {"__id__": 59}], "_active": true, "_components": [{"__id__": 65}, {"__id__": 67}, {"__id__": 69}], "_prefab": {"__id__": 71}, "_lpos": {"__type__": "cc.Vec3", "x": -2.125, "y": -100.492, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "item_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 52}, "_children": [], "_active": true, "_components": [{"__id__": 54}, {"__id__": 56}], "_prefab": {"__id__": 58}, "_lpos": {"__type__": "cc.Vec3", "x": -20.000000000000004, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.35, "y": 0.35, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 53}, "_enabled": true, "__prefab": {"__id__": 55}, "_contentSize": {"__type__": "cc.Size", "width": 114, "height": 114}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c1oOW+BldF+ZpujV51Kx03"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 53}, "_enabled": true, "__prefab": {"__id__": 57}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "c5ddc7c6-0144-4433-8eb2-53f32cd574f2@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "73x5/6Ph5BIrdYLYQUkBg1"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ab2o2g8ZxPcK8Lzf+eR2Om", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lab_qiyun", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 52}, "_children": [], "_active": true, "_components": [{"__id__": 60}, {"__id__": 62}], "_prefab": {"__id__": 64}, "_lpos": {"__type__": "cc.Vec3", "x": 14.949999999999996, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 59}, "_enabled": true, "__prefab": {"__id__": 61}, "_contentSize": {"__type__": "cc.Size", "width": 30, "height": 31.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bdgx72hvpEMbRPV8Xmx/CM"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 59}, "_enabled": true, "__prefab": {"__id__": 63}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "62", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25, "_fontSize": 25, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 25, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 116, "g": 255, "b": 119, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "40aTvaJGJD5r9kcR1PQdph"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7cK2KX4LtCZ5LcGwZQ9Tyd", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 52}, "_enabled": true, "__prefab": {"__id__": 66}, "_contentSize": {"__type__": "cc.Size", "width": 79.9, "height": 37}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e6Il33SlhAB5mnStD/5uiQ"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 52}, "_enabled": true, "__prefab": {"__id__": 68}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 10, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": true, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7aqnZlbdlDcbszV5JnPGLu"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 52}, "_enabled": true, "__prefab": {"__id__": 70}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "21ae14fd-2905-4383-ab78-ed1b5dced747@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "57X6SskydBa4jkD9rZw/xW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b4rgo0su9MTIYqkYuQ6Wjn", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "disable", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 34}, "_children": [], "_active": true, "_components": [{"__id__": 73}, {"__id__": 75}], "_prefab": {"__id__": 77}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 11.104, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 72}, "_enabled": true, "__prefab": {"__id__": 74}, "_contentSize": {"__type__": "cc.Size", "width": 87, "height": 81}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "63T5CtFdpD6b7JBXDQUaQu"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 72}, "_enabled": true, "__prefab": {"__id__": 76}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "7849d6ab-2361-4255-aed4-1eecfc7e9c8a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9aJ+n/e9ZFAYXh0jXQik8m"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9cqVBflxBBF6CfoiKpzJkp", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 34}, "_enabled": true, "__prefab": {"__id__": 79}, "_contentSize": {"__type__": "cc.Size", "width": 116, "height": 129}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dfQHVW8XtJYa5dfkq+o94X"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 34}, "_enabled": true, "__prefab": {"__id__": 81}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "996d4d40-9078-4947-95a1-bc7edb418170@f95d4", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": {"__uuid__": "996d4d40-9078-4947-95a1-bc7edb418170", "__expectedType__": "cc.SpriteAtlas"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "21ggvWzrBCz7xiPR5CzZ7r"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "061xmZjSpN+63pCaL6G6l5", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_fight", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 84}], "_active": false, "_components": [{"__id__": 90}], "_prefab": {"__id__": 92}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 50, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "ani_fight", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 83}, "_children": [], "_active": true, "_components": [{"__id__": 85}, {"__id__": 87}], "_prefab": {"__id__": 89}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -2.2737367544323206e-13, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1.1999999999999968, "y": 1.1999999999999968, "z": 0.9999999999999978}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 84}, "_enabled": true, "__prefab": {"__id__": 86}, "_contentSize": {"__type__": "cc.Size", "width": 63.86000061035156, "height": 59.4900016784668}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.4502035660071649, "y": 0.4632711159582652}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "68doUMwbNFpoVcggAHahDK"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 84}, "_enabled": true, "__prefab": {"__id__": 88}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "cc664fe0-6acd-4934-aa84-05466bfecb50", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "action", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "04avQBa+RNyrJeClcwUoJy"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5cfWZkcW1Oja3OJTsZfDvZ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 83}, "_enabled": true, "__prefab": {"__id__": 91}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e8i7eR6QRP8oSq+sEBQRSY"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "80G9CGf21A/qvdSP5TDWw3", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_num", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 94}, {"__id__": 100}], "_active": true, "_components": [{"__id__": 106}, {"__id__": 108}, {"__id__": 110}], "_prefab": {"__id__": 112}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -50, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.9999999999999992, "y": 0.9999999999999992, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_icon", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 93}, "_children": [], "_active": true, "_components": [{"__id__": 95}, {"__id__": 97}], "_prefab": {"__id__": 99}, "_lpos": {"__type__": "cc.Vec3", "x": -25, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 94}, "_enabled": true, "__prefab": {"__id__": 96}, "_contentSize": {"__type__": "cc.Size", "width": 22, "height": 24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6fpwlNnalD9bKArRz0msNx"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 94}, "_enabled": true, "__prefab": {"__id__": 98}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6bf8bc55-7328-4aa4-ab72-306f7a613031@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "789NU2e49IXqAOMdjB8Qcd"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "727fRuJ71ARIQByh5LEEZZ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_num", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 93}, "_children": [], "_active": true, "_components": [{"__id__": 101}, {"__id__": 103}], "_prefab": {"__id__": 105}, "_lpos": {"__type__": "cc.Vec3", "x": 13.5, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.9999999999999996, "y": 0.9999999999999996, "z": 0.9999999999999997}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 100}, "_enabled": true, "__prefab": {"__id__": 102}, "_contentSize": {"__type__": "cc.Size", "width": 45, "height": 25}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5ajzuO8btFbpactzNkaWh+"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 100}, "_enabled": true, "__prefab": {"__id__": 104}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "555", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25, "_fontSize": 25, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 25, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 2, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "10pfj/zWdPHoP6Q7yN4kK5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d1a5arvrRI1KOmQas2YVp1", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 93}, "_enabled": true, "__prefab": {"__id__": 107}, "_contentSize": {"__type__": "cc.Size", "width": 112, "height": 36}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "10gaTPWulFYKk4GQetnHJQ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 93}, "_enabled": true, "__prefab": {"__id__": 109}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "21ae14fd-2905-4383-ab78-ed1b5dced747@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4e3O1H941CDoKJ3t4J0N92"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 93}, "_enabled": true, "__prefab": {"__id__": 111}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 20, "_paddingRight": 20, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 5, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e4LQPv/PdGGYFrJrlH325j"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b2/yk1PQZBWoVhb6MQf2UA", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_name", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 114}, {"__id__": 126}], "_active": true, "_components": [{"__id__": 132}, {"__id__": 134}, {"__id__": 136}], "_prefab": {"__id__": 139}, "_lpos": {"__type__": "cc.Vec3", "x": 133.851, "y": 152.515, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.9999999999999996, "y": 0.9999999999999996, "z": 1}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 113}, "_prefab": {"__id__": 115}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 114}, "asset": {"__uuid__": "21255ee7-bab1-4ea5-9b60-556815704f09", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 116}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "9bCQHIUp1M6YHri0hAaaqR", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 117}, {"__id__": 119}, {"__id__": 120}, {"__id__": 121}, {"__id__": 122}, {"__id__": 124}, {"__id__": 125}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 118}, "propertyPath": ["_name"], "value": "Badge"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 118}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -40, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 118}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 118}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 123}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 15, "y": 35, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["d62NA3LF1OTZbxn67P5fPN"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 118}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 123}, "propertyPath": ["_layer"], "value": 8}, {"__type__": "cc.Node", "_name": "lbl_name", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 113}, "_children": [], "_active": true, "_components": [{"__id__": 127}, {"__id__": 129}], "_prefab": {"__id__": 131}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -40, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.9999999999999996, "y": 0.9999999999999996, "z": 0.9999999999999991}, "_mobility": 0, "_layer": 8, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 126}, "_enabled": true, "__prefab": {"__id__": 128}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 81.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3fq7JPoYVLXpFX0pwenEFc"}, {"__type__": "cc.RichText", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 126}, "_enabled": true, "__prefab": {"__id__": 130}, "_lineHeight": 25, "_string": "<color=FFF9EB>君天城</color><color=fff17f>1</color><color=FFF9EB>级</color>", "_horizontalAlign": 1, "_verticalAlign": 0, "_fontSize": 25, "_fontColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_maxWidth": 50, "_fontFamily": "<PERSON><PERSON>", "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_userDefinedFont": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_cacheMode": 0, "_imageAtlas": null, "_handleTouchEvent": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2dyefC9i5FwIHAyrq1vN3r"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "86mdjvjINPu7lo5SIpiLy2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 113}, "_enabled": true, "__prefab": {"__id__": 133}, "_contentSize": {"__type__": "cc.Size", "width": 45, "height": 236.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b85RCnMutE3YBQk++ofGxG"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 113}, "_enabled": true, "__prefab": {"__id__": 135}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "996d4d40-9078-4947-95a1-bc7edb418170@9a132", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": {"__uuid__": "996d4d40-9078-4947-95a1-bc7edb418170", "__expectedType__": "cc.SpriteAtlas"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fa/E2MNoFPyrAIu5c22tE2"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 113}, "_enabled": true, "__prefab": {"__id__": 137}, "clickEvents": [{"__id__": 138}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.05, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d5YTelrxZELZ/zunE2iTHD"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "79577UDyIhHCLRElgifC9HR", "handler": "onClick", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "05Guthm7tLkaTGzZ+VhFjM", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "node_effect_call_parent", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 141}], "_active": true, "_components": [{"__id__": 147}], "_prefab": {"__id__": 149}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 1}, "_mobility": 0, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "ani_gate", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 140}, "_children": [], "_active": false, "_components": [{"__id__": 142}, {"__id__": 144}], "_prefab": {"__id__": 146}, "_lpos": {"__type__": "cc.Vec3", "x": -231.812, "y": 280, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 141}, "_enabled": true, "__prefab": {"__id__": 143}, "_contentSize": {"__type__": "cc.Size", "width": 810.1699829101562, "height": 480.************}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5791994394984114, "y": 0.25988066807604554}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e3yQL/4W9AaLH09kjNoXKI"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 141}, "_enabled": true, "__prefab": {"__id__": 145}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "f67e5c3e-3bfd-4e56-8227-1f82e81486bd", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "animation2", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9cNHYNG4FCwrFCpXlOLcac"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "51Z0UwOJVEip2mgQAVvlq1", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 140}, "_enabled": true, "__prefab": {"__id__": 148}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f4t+2B0TRBebm8S9RqUly3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "af94oul5lH+beBUEgp1Fv0", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ani_build", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": false, "_components": [{"__id__": 151}, {"__id__": 153}], "_prefab": {"__id__": 155}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 40.465, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 2, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 150}, "_enabled": true, "__prefab": {"__id__": 152}, "_contentSize": {"__type__": "cc.Size", "width": 224.8300018310547, "height": 672.8099975585938}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1.4115109350129516, "y": 0.4200740267064756}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b46GVgh79H+Y4uss4WKOz6"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 150}, "_enabled": true, "__prefab": {"__id__": 154}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "97a427a8-5738-4e9c-9977-3758b6246877", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "<PERSON><PERSON><PERSON>", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": true, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0eQrsy7wBNAqSv3YWpFSW8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d9M8dCuslMkohKolI2K6B0", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 157}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e7dvZ5qpxM76fvgEHJdEpc"}, {"__type__": "79577UDyIhHCLRElgifC9HR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 159}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1aCTcO16lFJaTFX2XBXpkN"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 161}, "clickEvents": [{"__id__": 162}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.05, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a0f8E0EqxDC7fiHffveSLg"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "79577UDyIhHCLRElgifC9HR", "handler": "onClick", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2dauI56adBYroEZGpldbAo", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": [{"__id__": 114}, {"__id__": 41}]}]