import { Label } from "cc";
import { tween } from "cc";
import { randomRangeInt } from "cc";
import { v3 } from "cc";
import { UIOpacity } from "cc";
import { Vec3 } from "cc";
import { _decorator, Node } from "cc";
import ToolExt from "../game/common/ToolExt";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { LayerEnum } from "../game/GameDefine";

const { ccclass, property } = _decorator;

export interface ArgsUIOpen {
  nodeIconIn: Node;
  worldPos: Vec3;
  name: string;
}

@ccclass("TipOpen")
export class TipOpen extends BaseCtrl {
  @property(Node)
  private nodeIcon: Node;

  @property(Label)
  private lblName: Label;

  private _args: ArgsUIOpen;
  private _nodeIconIn: Node;
  private _isHide = false;

  public init(args: ArgsUIOpen) {
    this._args = args;

    this._nodeIconIn = args.nodeIconIn;
    this._nodeIconIn.off("click");
    this._nodeIconIn.setPosition(0, 0);
    this._nodeIconIn.active = true;
    this._nodeIconIn.walk((child) => (child.layer = LayerEnum.TOP));
    this.lblName.string = this._args.name;

    this.nodeIcon.addChild(this._nodeIconIn);
  }

  start() {
    super.start();
  }

  private playOpacityNodeAni(node: Node) {
    let uiOpacity = node.addComponent(UIOpacity);
    tween(uiOpacity).to(0.3, { opacity: 0 }).start();
  }

  private onGo() {
    if (this._isHide) {
      return;
    }
    this._isHide = true;

    const nodeIcon = this.nodeIcon;

    // this.playOpacityNodeAni(nodeIcon);
    // this.playOpacityNodeAni(this.node.getChildByName("node_other"));
    nodeIcon.active = false;
    this.node.getChildByName("node_other").active = false;

    // 缩小
    tween(nodeIcon)
      .to(0.3, { scale: v3(0.2, 0.2, 0.2) })
      .delay(0.15)
      .start();

    let pointCtrl: Vec3 = v3();
    pointCtrl.x = randomRangeInt(nodeIcon.worldPosition.x - 400, nodeIcon.worldPosition.x + 400);
    if (this._args.worldPos.y > nodeIcon.worldPosition.y) {
      pointCtrl.y = nodeIcon.worldPosition.y - 500;
    } else {
      pointCtrl.y = nodeIcon.worldPosition.y + 500;
    }

    let pointStart = nodeIcon.worldPosition;

    tween(this.node.getChildByName("Particle2D"))
      .delay(0.5)
      .to(
        1,
        {},
        {
          onUpdate: (target: Node, ratio) => {
            target.setWorldPosition(ToolExt.twoBezier(ratio, pointStart, pointCtrl, this._args.worldPos));
          },
        }
      )
      .delay(0.3)
      .call(() => {
        //  this.parentCallBack && this.parentCallBack();

        this.closeBack();
      })
      .start();
  }
}
