<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>S0821.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{70,53}</string>
                <key>spriteSourceSize</key>
                <string>{70,53}</string>
                <key>textureRect</key>
                <string>{{113,1},{70,53}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>S0822.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{11,11}</string>
                <key>spriteSourceSize</key>
                <string>{11,11}</string>
                <key>textureRect</key>
                <string>{{168,53},{11,11}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S0840.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{48,50}</string>
                <key>spriteSourceSize</key>
                <string>{48,50}</string>
                <key>textureRect</key>
                <string>{{113,73},{48,50}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>S0841.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{48,50}</string>
                <key>spriteSourceSize</key>
                <string>{48,50}</string>
                <key>textureRect</key>
                <string>{{165,73},{48,50}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>S0842.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{48,50}</string>
                <key>spriteSourceSize</key>
                <string>{48,50}</string>
                <key>textureRect</key>
                <string>{{217,70},{48,50}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S0843.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{48,50}</string>
                <key>spriteSourceSize</key>
                <string>{48,50}</string>
                <key>textureRect</key>
                <string>{{267,70},{48,50}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S0844.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{48,50}</string>
                <key>spriteSourceSize</key>
                <string>{48,50}</string>
                <key>textureRect</key>
                <string>{{317,70},{48,50}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S0845.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{47,50}</string>
                <key>spriteSourceSize</key>
                <string>{47,50}</string>
                <key>textureRect</key>
                <string>{{168,1},{47,50}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S0852.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{112,110}</string>
                <key>spriteSourceSize</key>
                <string>{112,110}</string>
                <key>textureRect</key>
                <string>{{1,1},{112,110}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>S0853.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{41,35}</string>
                <key>spriteSourceSize</key>
                <string>{41,35}</string>
                <key>textureRect</key>
                <string>{{367,70},{41,35}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>S0879.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{185,67}</string>
                <key>spriteSourceSize</key>
                <string>{185,67}</string>
                <key>textureRect</key>
                <string>{{217,1},{185,67}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>UIHorse.png</string>
            <key>size</key>
            <string>{403,122}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:89e9d9795a61d079dcc12abb842e9ae6:f4d91e8a9a48205d2594cf76ecc8957c:bf0aa33d31eccf3e184554a8d83ea7c5$</string>
            <key>textureFileName</key>
            <string>UIHorse.png</string>
        </dict>
    </dict>
</plist>
