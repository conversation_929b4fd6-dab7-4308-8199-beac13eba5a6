import { GameData } from "../../game/GameData";
import data from "../../lib/data/data";
import CityApi from "./CityApi";
import { CityData } from "./CityData";
import { CityRoute } from "./CityRoute";
import { CityService } from "./CityService";
import { CitySubscriber } from "./CitySubscriber";

export class CityModule extends data {
  private constructor() {
    super();
  }

  public static get instance(): CityModule {
    if (!GameData.instance.CityModule) {
      GameData.instance.CityModule = new CityModule();
    }
    return GameData.instance.CityModule;
  }

  private _api = new CityApi();
  private _data = new CityData();
  private _service = new CityService();
  private _subscriber = new CitySubscriber();
  private _route = new CityRoute();

  public static get data(): CityData {
    return this.instance._data;
  }
  public static get api(): CityApi {
    return this.instance._api;
  }

  public static get service(): CityService {
    return this.instance._service;
  }

  protected saveKey(): string {
    return this.constructor.name;
  }
  public init(data?: any, completedCallback?: Function) {
    if (this._subscriber) {
      this._subscriber.unRegister();
    }

    // 初始化模块
    this._api = new CityApi();
    this._data = new CityData();
    this._service = new CityService();
    this._subscriber = new CitySubscriber();
    this._route = new CityRoute();

    // 初始化路由
    this._route.init();

    // 初始化数据
    CityModule.api.getAllCity((data) => {
      this._subscriber.register();
      CityModule.api.aggregateInfo((data) => {
        CityModule.api.getEnergyFactory((data) => {
          completedCallback && completedCallback();
        });
      });
    });
    this._service.init();
  }
}
