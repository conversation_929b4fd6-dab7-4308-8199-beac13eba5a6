import { GameData } from "../../game/GameData";
import data from "../../lib/data/data";
import { Soul<PERSON><PERSON> } from "./SoulApi";
import { SoulData } from "./SoulData";
import { SoulRoute } from "./SoulRoute";
import { SoulService } from "./SoulService";

export class SoulModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): SoulModule {
    if (!GameData.instance.SoulModule) {
      GameData.instance.SoulModule = new SoulModule();
      GameData.instance.SoulModule.onViewLoad();
    }
    return GameData.instance.SoulModule;
  }
  private _data = new SoulData();
  private _api = new SoulApi();
  private _service = new SoulService();
  private _route = new SoulRoute();

  public static get data() {
    return this.instance._data;
  }
  public static get api() {
    return this.instance._api;
  }

  public static get service() {
    return this.instance._service;
  }

  protected saveKey(): string {
    return this.constructor.name;
  }
  public init(data?: any, completedCallback?: Function) {
    this._data = new SoulData();
    this._api = new SoulApi();
    this._service = new SoulService();
    this._route = new SoulRoute();

    // 初始化模块
    this._data.init();
    SoulModule.api.getAll((data) => {
      this._route.init();
      this._service.init();
      completedCallback && completedCallback();
    });
  }
}
