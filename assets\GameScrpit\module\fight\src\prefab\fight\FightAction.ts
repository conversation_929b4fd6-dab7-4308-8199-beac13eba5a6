import { BaseSkillType } from "../../FightConstant";
import { BattleActionDTO } from "../../utils/BattleReportUtil";
import { CharacterCtrl } from "./CharacterCtrl";

export class FightAction {
  /**
   * 行动方式
   */
  actionType: BaseSkillType;
  /**
   * 行动目标
   */
  target: CharacterCtrl;
  /**
   * 行动前回调
   */
  beforeActionCallback: (action: FightAction) => void;
  /**
   * 行动后回调
   */
  afterActionCallback: (action: FightAction) => void;

  /**
   *
   */
  startAction(action: BattleActionDTO) {}
}
