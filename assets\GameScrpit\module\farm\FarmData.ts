import { IConfigBlessLand, IConfigBlessLandFund } from "../../game/JsonDefine";
import { JsonMgr } from "../../game/mgr/JsonMgr";
import { FarmEnemyResponse, FarmNeighborResponse, FarmTrainMessage } from "../../game/net/protocol/Farm";
import { FarmInfo } from "../../game/ui/ui_farm/adapter/FarmFarmItemViewHolder";
export class FarmData {
  public stopHint = false;
  public stopHintHire = false;

  private _farmTrainMessage: FarmTrainMessage = null;
  public farmNeighborList: FarmNeighborResponse = null;
  public farmEnemyList: FarmEnemyResponse = null;

  private _rewardList: number[] = [];

  public data1: FarmInfo[] = null;

  public get farmTrainMessage() {
    if (!this._farmTrainMessage) {
      this._farmTrainMessage = {
        /** 槽位的情况 */
        slotList: [],
        /** 蜜蜂派遣情况 key:此次采集的葫芦的唯一标识 */
        dispatchMap: {},
        /** 蜜蜂统计信息 */
        beeMetric: {
          /** 还可以雇佣的蜜蜂数量 */
          canHireBeeCnt: 0,
          /** 空闲蜜蜂数量 */
          relaxBeeCnt: 0,
          /** 总蜜蜂数量 */
          totalBeeCnt: 0,
        },
        /** 待领取的奖励 */
        rewardGourdList: [],
        /** 仇恨值集合 */
        hatredMap: {},
        /** 当日剩余的采集次数 */
        remainFetch: 0,
        /** 总采集次数 */
        totalColCnt: 0,
        /** 剩下的广告刷新的次数 */
        remainAdsRefreshCnt: 0,
        /** 下次探寻的冷却时间 */
        nextColdRefreshStamp: 0,
        /** 基金 */
        fundList: [],
      };
    }
    return this._farmTrainMessage;
  }

  public set farmTrainMessage(data: FarmTrainMessage) {
    this._farmTrainMessage = data;
  }

  public get rewardList() {
    return this._rewardList;
  }

  public set rewardList(data: number[]) {
    this._rewardList = data;
  }

  /** 蜜蜂派遣情况 */
  public get dispatchList() {
    let dispatchList = Object.keys(this.farmTrainMessage.dispatchMap).map(Number);
    return dispatchList;
  }

  public getConfigBlessLandFund(id: number): IConfigBlessLandFund {
    return JsonMgr.instance.jsonList.c_blessLandFund[id];
  }

  public getConfigBlessLand(id: number): IConfigBlessLand {
    return JsonMgr.instance.jsonList.c_blessLand[id];
  }
}
