"use strict";
var __importDefault =
  (this && this.__importDefault) ||
  function (mod) {
    return mod && mod.__esModule ? mod : { default: mod };
  };
Object.defineProperty(exports, "__esModule", { value: true });
exports.BuilderHookerCodeInjection = void 0;
const path_1 = __importDefault(require("path"));
const code_injection_1 = require("../core/code-injection");
const builder_1 = require("./builder");
/**
 * 注入代码到 main.js
 */
class BuilderHookerCodeInjection {
  onAfterBuild(options, result) {
    const pluginConfig = (0, builder_1.getBuilderPluginConfig)(options);
    if (!pluginConfig.enable) {
      return;
    }
    // 获取构建资源目录 e.g. /Users/<USER>/game/build/android/data
    const assetsRootDir = path_1.default.join(result.dest, "data");
    code_injection_1.codeInjection.injectCodeToMainJs(assetsRootDir);
  }
}
exports.BuilderHookerCodeInjection = BuilderHookerCodeInjection;
