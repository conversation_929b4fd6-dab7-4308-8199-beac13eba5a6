import { instantiate, Node } from "cc";
import { ListAdapter } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { SoulTuJianViewholder } from "./SoulTuJianViewholder";
import { SoulTuJianAttrViewHolder } from "./SoulTuJianAttrViewHolder";

export class SoulTuJianAttrAdapter extends ListAdapter {
  private item: Node;
  private _datas: any;
  constructor(item: Node) {
    super();
    this.item = item;
  }
  public setData(data: any) {
    this._datas = data;
    this.notifyDataSetChanged();
  }
  onCreateView(viewType: number): Node {
    let item = instantiate(this.item);
    item.active = true;
    return item;
  }
  onBindData(node: Node, position: number): void {
    node.getComponent(SoulTuJianAttrViewHolder).updateData(this._datas, position);
  }
  getCount(): number {
    return this._datas.refreshAttr.length;
  }
}
