{"skeleton": {"hash": "7jaAvD6RpSVSc7TjDUa14vwkP/Y=", "spine": "3.8.75", "x": 0.88, "y": -0.97, "width": 205.17, "height": 212, "images": "./images/", "audio": "D:/spine/龙头"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 18.73, "rotation": -89.07, "x": 312.75, "y": 245.24}, {"name": "bone2", "parent": "bone", "length": 18.73, "x": 18.73}, {"name": "bone3", "parent": "bone2", "length": 18.73, "x": 18.73}, {"name": "bone4", "parent": "bone3", "length": 18.73, "x": 18.73}, {"name": "bone5", "parent": "bone4", "length": 18.73, "x": 18.73}, {"name": "bone6", "parent": "bone5", "length": 18.73, "x": 18.73}, {"name": "bone7", "parent": "bone6", "length": 18.73, "x": 18.73}, {"name": "bone8", "parent": "bone7", "length": 18.73, "x": 18.73}, {"name": "bone9", "parent": "bone8", "length": 18.73, "x": 18.73}, {"name": "bone10", "parent": "bone9", "length": 18.73, "x": 18.73}, {"name": "bone11", "parent": "bone10", "length": 18.73, "x": 18.73}, {"name": "bone12", "parent": "bone11", "length": 18.73, "x": 18.73}, {"name": "1", "parent": "root", "x": 112.89, "y": 25.6}, {"name": "2", "parent": "1", "x": 3.71, "y": 31.21}, {"name": "3", "parent": "2", "x": 68.39, "y": 25.19}, {"name": "huo", "parent": "root", "rotation": 138.73, "x": 100.98, "y": 152.9}, {"name": "huo2", "parent": "root", "rotation": -137.69, "x": 135.52, "y": 151.12, "scaleX": -1}, {"name": "ef", "parent": "root", "x": 118.04, "y": 118.04, "scaleX": 0.1448, "scaleY": 0.1448, "color": "ff0000ff"}, {"name": "effect008_qinguli_skill_projectile", "parent": "ef", "color": "0081ffff"}, {"name": "ef/luoxuanqiu_0000", "parent": "effect008_qinguli_skill_projectile", "color": "ff0000ff"}, {"name": "ef/glow_117", "parent": "effect008_qinguli_skill_projectile", "color": "ff0000ff"}, {"name": "ef/baidian2", "parent": "effect008_qinguli_skill_projectile", "color": "fe0000ff"}, {"name": "ef/huoquan2_1", "parent": "effect008_qinguli_skill_projectile", "color": "ff0000ff"}, {"name": "ef/huoquan2_2", "parent": "effect008_qinguli_skill_projectile", "color": "ff0000ff"}, {"name": "yanwu", "parent": "root", "x": 112, "y": 75.46, "scaleX": 1.4582, "scaleY": 1.4582}, {"name": "qipao", "parent": "root", "x": 94.29, "y": 59.93, "scaleX": 0.7428, "scaleY": 0.7428}, {"name": "qipao2", "parent": "root", "x": 110.45, "y": 43.49, "scaleX": 0.5415, "scaleY": 0.5415}, {"name": "qipao3", "parent": "root", "x": 134.84, "y": 62.94, "scaleX": 0.721, "scaleY": 0.721}], "slots": [{"name": "1", "bone": "1", "attachment": "1"}, {"name": "2", "bone": "root", "attachment": "2"}, {"name": "root", "bone": "root", "attachment": "root"}, {"name": "huo/huomiao_0001", "bone": "huo", "attachment": "huo/huomiao_0001"}, {"name": "huo/huomiao_1", "bone": "huo2", "attachment": "huo/huomiao_0001"}, {"name": "ef/glow_117", "bone": "ef/glow_117", "attachment": "ef/glow_00114"}, {"name": "ef/baidian2", "bone": "ef/baidian2", "attachment": "ef/baidian", "blend": "additive"}, {"name": "ef/huoquan2_2", "bone": "ef/huoquan2_2", "attachment": "ef/huoquan3_0017"}, {"name": "ef/huoquan2_1", "bone": "ef/huoquan2_1", "attachment": "ef/huoquan3_0017"}, {"name": "ef/luoxuanqiu_0000", "bone": "ef/luoxuanqiu_0000", "attachment": "ef/luoxuanqiu_0005", "blend": "additive"}, {"name": "qipao/huo01", "bone": "qipao", "dark": "ffde2d", "attachment": "qipao/huo010"}, {"name": "qipao/huo1", "bone": "qipao2", "dark": "ffde2d", "attachment": "qipao/huo010"}, {"name": "qipao/huo2", "bone": "qipao3", "dark": "ffde2d", "attachment": "qipao/huo010"}, {"name": "A_shuiliu_00000", "bone": "yanwu", "color": "000000ff", "attachment": "A_shuiliu_00019"}, {"name": "A_shuiliu_0", "bone": "yanwu", "color": "000000ff", "attachment": "A_shuiliu_00019"}], "path": [{"name": "22", "bones": ["bone", "bone2", "bone3", "bone4", "bone5", "bone6", "bone7", "bone8", "bone9", "bone10", "bone11", "bone12"], "target": "root"}], "skins": [{"name": "default", "attachments": {"huo/huomiao_0001": {"huo/huomiao_0001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [22, -27, -22, -27, -22, 28, 22, 28], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 44, "height": 55}, "huo/huomiao_0002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [22, -27, -22, -27, -22, 28, 22, 28], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 44, "height": 55}, "huo/huomiao_0003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [22, -27, -22, -27, -22, 28, 22, 28], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 44, "height": 55}, "huo/huomiao_0005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [22, -27, -22, -27, -22, 28, 22, 28], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 44, "height": 55}, "huo/huomiao_0007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [22, -27, -22, -27, -22, 28, 22, 28], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 44, "height": 55}}, "huo/huomiao_1": {"huo/huomiao_0001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [22, -27, -22, -27, -22, 28, 22, 28], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 44, "height": 55}, "huo/huomiao_0002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [22, -27, -22, -27, -22, 28, 22, 28], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 44, "height": 55}, "huo/huomiao_0003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [22, -27, -22, -27, -22, 28, 22, 28], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 44, "height": 55}, "huo/huomiao_0005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [22, -27, -22, -27, -22, 28, 22, 28], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 44, "height": 55}, "huo/huomiao_0007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [22, -27, -22, -27, -22, 28, 22, 28], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 44, "height": 55}}, "A_shuiliu_00000": {"A_shuiliu_0": {"type": "<PERSON><PERSON><PERSON>", "path": "A_shuiliu_00000", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00000": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.5, 0, 0, 0.5, 0, 1, 0, 1, 0.5, 0.5, 0.5], "triangles": [0, 1, 7, 1, 8, 7, 7, 8, 6, 8, 5, 6, 1, 2, 8, 2, 3, 8, 8, 3, 5, 3, 4, 5], "vertices": [22.91, -32, 0, -32, -22.91, -32, -49.38, 0, -59.98, 32, 0, 32, 59.98, 32, 49.38, 0, 0, 0], "hull": 8, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 0], "width": 64, "height": 64}, "A_shuiliu_00001": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00002": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00003": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00004": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00005": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00006": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00007": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00008": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00009": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00010": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00011": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00012": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00013": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00014": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00015": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00016": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00017": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00018": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00019": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}}, "ef/glow_117": {"ef/glow_00114": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64.15, -63.52, -63.85, -63.52, -63.85, 64.48, 64.15, 64.48], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}}, "A_shuiliu_0": {"A_shuiliu_0": {"type": "<PERSON><PERSON><PERSON>", "path": "A_shuiliu_00000", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00000": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.5, 0, 0, 0.5, 0, 1, 0, 1, 0.5, 0.5, 0.5], "triangles": [0, 1, 7, 1, 8, 7, 7, 8, 6, 8, 5, 6, 1, 2, 8, 2, 3, 8, 8, 3, 5, 3, 4, 5], "vertices": [22.91, -32, 0, -32, -22.91, -32, -49.38, 0, -59.98, 32, 0, 32, 59.98, 32, 49.38, 0, 0, 0], "hull": 8, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 0], "width": 64, "height": 64}, "A_shuiliu_00001": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00002": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00003": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00004": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00005": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00006": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00007": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00008": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00009": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00010": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00011": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00012": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00013": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00014": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00015": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00016": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00017": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00018": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}, "A_shuiliu_00019": {"type": "<PERSON><PERSON><PERSON>", "parent": "A_shuiliu_00000", "width": 64, "height": 64}}, "qipao/huo1": {"qipao/huo01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15, -11, -15, -11, -15, 11, 15, 11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 22}, "qipao/huo02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15, -11, -15, -11, -15, 11, 15, 11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 22}, "qipao/huo03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15, -11, -15, -11, -15, 11, 15, 11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 22}, "qipao/huo04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15, -11, -15, -11, -15, 11, 15, 11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 22}, "qipao/huo05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15, -11, -15, -11, -15, 11, 15, 11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 22}, "qipao/huo06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15, -11, -15, -11, -15, 11, 15, 11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 22}, "qipao/huo07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15, -11, -15, -11, -15, 11, 15, 11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 22}, "qipao/huo08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15, -11, -15, -11, -15, 11, 15, 11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 22}, "qipao/huo09": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15, -11, -15, -11, -15, 11, 15, 11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 22}, "qipao/huo010": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15, -11, -15, -11, -15, 11, 15, 11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 22}}, "qipao/huo2": {"qipao/huo01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15, -11, -15, -11, -15, 11, 15, 11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 22}, "qipao/huo02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15, -11, -15, -11, -15, 11, 15, 11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 22}, "qipao/huo03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15, -11, -15, -11, -15, 11, 15, 11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 22}, "qipao/huo04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15, -11, -15, -11, -15, 11, 15, 11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 22}, "qipao/huo05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15, -11, -15, -11, -15, 11, 15, 11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 22}, "qipao/huo06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15, -11, -15, -11, -15, 11, 15, 11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 22}, "qipao/huo07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15, -11, -15, -11, -15, 11, 15, 11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 22}, "qipao/huo08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15, -11, -15, -11, -15, 11, 15, 11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 22}, "qipao/huo09": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15, -11, -15, -11, -15, 11, 15, 11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 22}, "qipao/huo010": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15, -11, -15, -11, -15, 11, 15, 11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 22}}, "ef/huoquan2_1": {"ef/huoquan3_0001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [127.46, -88.78, -128.54, -88.78, -128.54, 40.22, 127.46, 40.22], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 32}, "ef/huoquan3_0003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [127.46, -88.78, -128.54, -88.78, -128.54, 40.22, 127.46, 40.22], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 32}, "ef/huoquan3_0005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [127.46, -88.78, -128.54, -88.78, -128.54, 40.22, 127.46, 40.22], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 32}, "ef/huoquan3_0007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [127.46, -88.78, -128.54, -88.78, -128.54, 40.22, 127.46, 40.22], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 32}, "ef/huoquan3_0009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [127.46, -88.78, -128.54, -88.78, -128.54, 40.22, 127.46, 40.22], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 32}, "ef/huoquan3_0011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [127.46, -88.78, -128.54, -88.78, -128.54, 40.22, 127.46, 40.22], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 32}, "ef/huoquan3_0013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [127.46, -88.78, -128.54, -88.78, -128.54, 40.22, 127.46, 40.22], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 32}, "ef/huoquan3_0015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [127.46, -88.78, -128.54, -88.78, -128.54, 40.22, 127.46, 40.22], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 32}, "ef/huoquan3_0017": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [127.46, -88.78, -128.54, -88.78, -128.54, 40.22, 127.46, 40.22], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 32}}, "ef/huoquan2_2": {"ef/huoquan3_0001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [118.1, -86.44, -137.9, -86.44, -137.9, 42.56, 118.1, 42.56], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 32}, "ef/huoquan3_0003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [118.1, -86.44, -137.9, -86.44, -137.9, 42.56, 118.1, 42.56], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 32}, "ef/huoquan3_0005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [118.1, -86.44, -137.9, -86.44, -137.9, 42.56, 118.1, 42.56], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 32}, "ef/huoquan3_0007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [118.1, -86.44, -137.9, -86.44, -137.9, 42.56, 118.1, 42.56], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 32}, "ef/huoquan3_0009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [118.1, -86.44, -137.9, -86.44, -137.9, 42.56, 118.1, 42.56], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 32}, "ef/huoquan3_0011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [118.1, -86.44, -137.9, -86.44, -137.9, 42.56, 118.1, 42.56], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 32}, "ef/huoquan3_0013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [118.1, -86.44, -137.9, -86.44, -137.9, 42.56, 118.1, 42.56], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 32}, "ef/huoquan3_0015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [118.1, -86.44, -137.9, -86.44, -137.9, 42.56, 118.1, 42.56], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 32}, "ef/huoquan3_0017": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [118.1, -86.44, -137.9, -86.44, -137.9, 42.56, 118.1, 42.56], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 32}}, "root": {"root": {"type": "path", "lengths": [60.54, 105.29, 187.7, 224.13, 296.25], "vertexCount": 15, "vertices": [127.25, 138.25, 111.81, 139.35, 101.4, 140.09, 62.96, 119.47, 66.3, 104.59, 69.15, 91.95, 87.48, 93.84, 106.44, 101.11, 127.09, 109.03, 132.06, 117.04, 121.92, 173.79, 120.34, 182.64, 115.6, 187.13, 111.18, 208.55, 108.97, 219.24]}}, "ef/baidian2": {"ef/baidian": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -29.92, -32, -29.92, -32, 34.08, 32, 34.08], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 16, "height": 16}}, "1": {"1": {"type": "mesh", "uvs": [0.88425, 0.0552, 0.91074, 0.0998, 0.96033, 0.23366, 0.97945, 0.3101, 0.99525, 0.37324, 0.99546, 0.45156, 0.95181, 0.5732, 0.94241, 0.61874, 0.95513, 0.70711, 0.91341, 0.74244, 0.87373, 0.84686, 0.78515, 0.91201, 0.71388, 0.95989, 0.62612, 0.95992, 0.54579, 0.91631, 0.49263, 0.93908, 0.43593, 0.99972, 0.3554, 0.95213, 0.28811, 0.83673, 0.25944, 0.90327, 0.18771, 0.90384, 0.13658, 0.88903, 0.06176, 0.82219, 0.02353, 0.75744, 0.00946, 0.67709, 0.00941, 0.65279, 0.029, 0.56973, 0.03839, 0.4925, 0.00906, 0.34482, 0.00929, 0.28032, 0.05498, 0.35859, 0.08282, 0.21924, 0.14402, 0.32235, 0.20601, 0.08342, 0.27164, 0.06811, 0.3161, 0.14278, 0.33922, 0.15777, 0.42362, 0.20109, 0.48113, 0.2011, 0.52568, 0.17122, 0.61085, 0.23371, 0.65394, 0.23206, 0.7421, 0.1106, 0.81796, 0.20819, 0.81645, 0.15784, 0.81402, 0.07745, 0.81208, 0.01312, 0.83849, 0.01694, 0.83762, 0.23543, 0.82003, 0.30775, 0.80755, 0.39435, 0.83478, 0.43812, 0.91647, 0.45906, 0.84682, 0.08492, 0.88483, 0.12965, 0.83888, 0.16867, 0.87008, 0.16391, 0.91077, 0.16539, 0.87292, 0.24479, 0.90469, 0.24479, 0.861, 0.31807, 0.91149, 0.31997, 0.85703, 0.38563, 0.91887, 0.38183, 0.40167, 0.41712, 0.48679, 0.35854, 0.59047, 0.36037, 0.70506, 0.39882, 0.77054, 0.50133, 0.76618, 0.60385, 0.64285, 0.69721, 0.5108, 0.7027, 0.41149, 0.64412, 0.36456, 0.53795, 0.42458, 0.52513, 0.50207, 0.48303, 0.50098, 0.5709, 0.59483, 0.49767, 0.60357, 0.59103, 0.68869, 0.54344], "triangles": [54, 56, 53, 53, 0, 54, 7, 51, 52, 68, 50, 51, 43, 44, 55, 68, 67, 50, 68, 51, 69, 79, 67, 68, 66, 65, 39, 64, 65, 75, 73, 64, 74, 70, 79, 69, 7, 52, 6, 63, 4, 5, 61, 2, 3, 2, 57, 1, 65, 38, 39, 65, 37, 38, 64, 36, 37, 64, 35, 36, 33, 35, 32, 30, 31, 32, 73, 26, 32, 24, 25, 26, 73, 18, 26, 18, 73, 72, 71, 78, 70, 18, 21, 26, 18, 20, 21, 19, 20, 18, 11, 69, 10, 70, 69, 11, 14, 71, 70, 70, 13, 14, 15, 72, 71, 15, 71, 14, 17, 18, 72, 15, 17, 72, 11, 12, 70, 12, 13, 70, 16, 17, 15, 26, 21, 22, 23, 26, 22, 23, 24, 26, 32, 26, 27, 27, 28, 30, 32, 27, 30, 28, 29, 30, 33, 34, 35, 32, 35, 73, 40, 66, 39, 43, 41, 42, 66, 40, 41, 44, 45, 53, 45, 46, 47, 45, 47, 53, 53, 47, 0, 54, 0, 1, 63, 3, 4, 6, 52, 5, 9, 7, 8, 51, 7, 69, 9, 69, 7, 10, 69, 9, 69, 79, 68, 72, 76, 71, 72, 73, 74, 64, 73, 35, 65, 64, 37, 67, 66, 41, 75, 65, 66, 49, 67, 41, 77, 66, 67, 41, 43, 49, 50, 67, 49, 52, 63, 5, 54, 1, 57, 2, 59, 57, 53, 55, 44, 56, 54, 57, 49, 48, 60, 48, 43, 55, 49, 43, 48, 56, 55, 53, 59, 58, 56, 57, 59, 56, 48, 55, 56, 58, 48, 56, 60, 48, 58, 61, 59, 2, 58, 59, 61, 60, 58, 61, 63, 61, 3, 62, 49, 60, 62, 60, 61, 62, 61, 63, 50, 49, 62, 51, 50, 62, 52, 62, 63, 51, 62, 52, 70, 78, 79, 75, 66, 77, 74, 64, 75, 79, 77, 67, 76, 74, 75, 76, 75, 77, 78, 77, 79, 76, 77, 78, 72, 74, 76, 71, 76, 78], "vertices": [1, 13, 70.03, 90.56, 1, 1, 13, 75.54, 85.03, 1, 1, 13, 85.86, 68.43, 1, 1, 13, 89.83, 58.95, 1, 1, 13, 93.12, 51.12, 1, 1, 13, 93.16, 41.41, 1, 1, 13, 84.08, 26.33, 1, 1, 13, 82.13, 20.68, 1, 1, 13, 84.78, 9.72, 1, 1, 13, 76.1, 5.34, 1, 1, 13, 67.84, -7.61, 1, 1, 13, 49.42, -15.69, 1, 1, 13, 34.59, -21.62, 1, 1, 13, 16.34, -21.63, 1, 1, 13, -0.37, -16.22, 1, 1, 13, -11.43, -19.04, 1, 1, 13, -23.22, -26.56, 1, 1, 13, -39.97, -20.66, 1, 1, 13, -53.97, -6.35, 1, 1, 13, -59.93, -14.6, 1, 1, 13, -74.85, -14.67, 1, 1, 13, -85.48, -12.83, 1, 1, 13, -101.05, -4.55, 1, 1, 13, -109, 3.48, 1, 1, 13, -111.92, 13.45, 1, 1, 13, -111.94, 16.46, 1, 1, 13, -107.86, 26.76, 1, 1, 13, -105.91, 36.33, 1, 1, 13, -112.01, 54.65, 1, 1, 13, -111.96, 62.64, 1, 1, 13, -102.46, 52.94, 1, 1, 13, -96.67, 70.22, 1, 1, 13, -83.94, 57.43, 1, 1, 13, -71.04, 87.06, 1, 1, 13, -57.39, 88.96, 1, 1, 13, -48.14, 79.7, 1, 1, 13, -43.34, 77.84, 1, 1, 13, -25.78, 72.47, 1, 1, 13, -13.82, 72.47, 1, 1, 13, -4.55, 76.17, 1, 1, 13, 13.16, 68.42, 1, 1, 13, 22.13, 68.63, 1, 1, 13, 40.46, 83.69, 1, 3, 14, 52.53, 40.38, 0.08206, 15, -15.86, 15.19, 0.46779, 13, 56.24, 71.59, 0.45015, 1, 13, 55.93, 77.83, 1, 1, 13, 55.42, 87.8, 1, 1, 13, 55.02, 95.78, 1, 1, 13, 60.51, 95.3, 1, 3, 14, 56.62, 37, 0.05401, 15, -11.78, 11.81, 0.47814, 13, 60.33, 68.21, 0.46785, 3, 14, 52.96, 28.03, 0.09122, 15, -15.44, 2.84, 0.45177, 13, 56.67, 59.24, 0.45701, 3, 14, 50.36, 17.29, 0.11673, 15, -18.03, -7.9, 0.39713, 13, 54.08, 48.51, 0.48613, 3, 14, 56.03, 11.87, 0.07797, 15, -12.37, -13.33, 0.41412, 13, 59.74, 43.08, 0.50791, 3, 14, 73.02, 9.27, 0.01253, 15, 4.62, -15.92, 0.48676, 13, 76.73, 40.48, 0.50071, 3, 14, 58.53, 55.66, 0.00139, 15, -9.86, 30.47, 0.25335, 13, 62.25, 86.87, 0.74525, 3, 14, 66.44, 50.12, 0.00106, 15, -1.96, 24.92, 0.19894, 13, 70.15, 81.33, 0.8, 2, 14, 56.88, 45.28, 0.04729, 15, -11.51, 20.09, 0.95271, 2, 14, 63.37, 45.87, 0.01934, 15, -5.02, 20.68, 0.98066, 3, 14, 71.83, 45.69, 0.0002, 15, 3.44, 20.49, 0.0998, 13, 75.55, 76.9, 0.9, 3, 14, 63.96, 35.84, 0.03014, 15, -4.43, 10.65, 0.86986, 13, 67.67, 67.05, 0.1, 3, 14, 70.57, 35.84, 0.00521, 15, 2.17, 10.65, 0.89479, 13, 74.28, 67.05, 0.1, 3, 14, 61.48, 26.75, 0.05069, 15, -6.91, 1.56, 0.8455, 13, 65.2, 57.96, 0.10381, 2, 15, 3.59, 1.32, 0.9, 13, 75.7, 57.73, 0.1, 3, 14, 60.66, 18.37, 0.07522, 15, -7.74, -6.82, 0.82846, 13, 64.37, 49.59, 0.09633, 3, 14, 73.52, 18.85, 0.00343, 15, 5.12, -6.35, 0.90021, 13, 77.23, 50.06, 0.09636, 1, 13, -30.35, 45.68, 1, 1, 13, -12.64, 52.95, 1, 1, 13, 8.93, 52.72, 1, 1, 13, 32.76, 47.95, 1, 1, 13, 46.38, 35.24, 1, 1, 13, 45.47, 22.53, 1, 1, 13, 19.82, 10.95, 1, 1, 13, -7.65, 10.27, 1, 1, 13, -28.3, 17.53, 1, 1, 13, -38.06, 30.7, 1, 1, 14, -29.29, 1.08, 1, 1, 14, -13.18, 6.3, 1, 1, 14, -13.4, -4.6, 1, 1, 14, 6.12, 4.48, 1, 1, 14, 7.93, -7.1, 1, 1, 14, 25.64, -1.19, 1], "hull": 48, "edges": [0, 94, 0, 2, 2, 4, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 92, 94, 86, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 10, 90, 92, 90, 106, 106, 108, 86, 88, 88, 90, 88, 110, 110, 112, 4, 114, 114, 108, 112, 114, 96, 116, 116, 118, 118, 4, 98, 120, 120, 122, 4, 6, 6, 8, 122, 6, 100, 124, 124, 126, 126, 8, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 128, 128, 148, 148, 144, 130, 150, 150, 152, 152, 142, 132, 154, 154, 156, 156, 140, 134, 158, 158, 138], "width": 208, "height": 124}}, "2": {"2": {"type": "mesh", "uvs": [0.6232, 0.01569, 0.62974, 0.02047, 0.62985, 0.1186, 0.66728, 0.11798, 0.66609, 0.14007, 0.68452, 0.19228, 0.71235, 0.19251, 0.71123, 0.20019, 0.7113, 0.24532, 0.71673, 0.2567, 0.75344, 0.2937, 0.79015, 0.3307, 0.79253, 0.33237, 0.81104, 0.38823, 0.82956, 0.44409, 0.84253, 0.44938, 0.90235, 0.47294, 0.92903, 0.49086, 0.97513, 0.52569, 0.99283, 0.53893, 0.99269, 0.54704, 0.99286, 0.54818, 0.98525, 0.55561, 0.98183, 0.55732, 0.97104, 0.56308, 0.95673, 0.56304, 0.87407, 0.56326, 0.84018, 0.61075, 0.80629, 0.65824, 0.852, 0.68146, 0.85254, 0.68832, 0.84212, 0.69881, 0.83784, 0.70391, 0.77131, 0.70389, 0.75406, 0.73162, 0.73988, 0.78119, 0.72569, 0.83077, 0.71812, 0.84579, 0.70613, 0.86074, 0.65213, 0.92613, 0.64967, 0.92743, 0.59617, 0.95756, 0.55482, 0.97824, 0.54805, 0.97904, 0.54803, 0.97047, 0.48542, 0.97047, 0.48147, 0.97048, 0.4266, 0.95676, 0.41576, 0.95607, 0.31864, 0.98529, 0.28638, 0.98526, 0.26242, 0.98545, 0.26024, 0.99275, 0.21081, 0.99269, 0.20751, 0.9929, 0.1624, 0.97769, 0.13458, 0.96399, 0.10864, 0.94668, 0.0855, 0.93005, 0.03638, 0.88107, 0.03487, 0.87728, 0.0147, 0.82977, 0.01462, 0.76365, 0.00687, 0.76339, 0.00688, 0.7569, 0.02153, 0.71234, 0.04362, 0.66737, 0.10447, 0.60653, 0.11146, 0.59936, 0.13511, 0.5638, 0.15273, 0.55309, 0.1926, 0.53324, 0.26796, 0.5101, 0.29595, 0.50957, 0.32411, 0.52343, 0.337, 0.5298, 0.35744, 0.53172, 0.37433, 0.54313, 0.40278, 0.56283, 0.42307, 0.57024, 0.42144, 0.58698, 0.413, 0.60031, 0.41344, 0.61172, 0.42233, 0.63231, 0.42411, 0.63987, 0.42055, 0.64906, 0.40796, 0.66565, 0.37537, 0.69587, 0.36293, 0.70994, 0.34752, 0.71972, 0.36411, 0.7132, 0.377, 0.6978, 0.41033, 0.66757, 0.42293, 0.65098, 0.4256, 0.64239, 0.425, 0.63276, 0.41715, 0.61202, 0.41641, 0.60031, 0.42367, 0.59009, 0.42678, 0.56891, 0.40367, 0.55988, 0.37581, 0.54121, 0.35833, 0.52936, 0.33804, 0.52758, 0.31581, 0.51662, 0.30494, 0.51101, 0.31983, 0.49325, 0.33903, 0.48793, 0.26164, 0.47203, 0.18425, 0.45612, 0.18509, 0.4335, 0.18469, 0.4259, 0.18519, 0.42953, 0.1992, 0.4225, 0.24481, 0.39734, 0.31994, 0.37224, 0.26668, 0.31853, 0.26611, 0.28697, 0.26474, 0.274, 0.28147, 0.27392, 0.31755, 0.3013, 0.31822, 0.24157, 0.31888, 0.18183, 0.31844, 0.16586, 0.31821, 0.13667, 0.31767, 0.13571, 0.35545, 0.14765, 0.34631, 0.09843, 0.34005, 0.07968, 0.3405, 0.06691, 0.34086, 0.06617, 0.3422, 0.06659, 0.36313, 0.06658, 0.40728, 0.08868, 0.4073, 0.04277, 0.41508, 0.02102, 0.41846, 0.02199, 0.43879, 0.02195, 0.48247, 0.07995, 0.56706, 0.08276, 0.59754, 0.03052, 0.60745, 0.01456, 0.60984, 0.01474, 0.62285, 0.01467, 0.52013, 0.18467, 0.57217, 0.59887, 0.5233, 0.40106, 0.53596, 0.83758, 0.09488, 0.80654, 0.7381, 0.44816, 0.66427, 0.8197, 0.23178, 0.93747, 0.17747, 0.60837, 0.77619, 0.55244, 0.38194, 0.2615, 0.33572, 0.87751, 0.19078, 0.74621, 0.66242, 0.31744, 0.61632, 0.48108, 0.46638, 0.69565, 0.63369, 0.72861, 0.40863, 0.80679, 0.29968, 0.59375, 0.70415, 0.63631, 0.39251, 0.19618, 0.43264, 0.20478, 0.45844, 0.22803, 0.47309, 0.26275, 0.43869, 0.27454, 0.39983, 0.27294, 0.38518, 0.22644, 0.39523, 0.24014, 0.41434, 0.26466, 0.51808, 0.26148, 0.53879, 0.2274, 0.57605, 0.21497, 0.61682, 0.23695, 0.61587, 0.2653, 0.57542, 0.28696, 0.53528, 0.27422, 0.56777, 0.27708, 0.60345, 0.26402], "triangles": [100, 101, 146, 168, 146, 115, 107, 146, 101, 168, 167, 146, 146, 167, 179, 146, 107, 115, 158, 146, 157, 157, 146, 178, 157, 178, 177, 11, 157, 10, 146, 179, 178, 157, 177, 8, 157, 9, 10, 157, 8, 9, 8, 5, 7, 5, 8, 176, 176, 8, 177, 179, 180, 178, 178, 181, 177, 178, 180, 181, 179, 174, 180, 180, 175, 181, 177, 181, 176, 181, 175, 176, 5, 176, 4, 7, 5, 6, 103, 107, 102, 101, 102, 107, 107, 103, 106, 106, 103, 104, 104, 105, 106, 108, 115, 107, 108, 109, 113, 113, 109, 110, 110, 112, 113, 115, 169, 168, 113, 114, 108, 108, 114, 115, 110, 111, 112, 179, 167, 173, 116, 120, 115, 169, 120, 154, 169, 115, 120, 120, 117, 119, 120, 116, 117, 120, 121, 154, 117, 118, 119, 180, 174, 175, 169, 172, 168, 168, 172, 167, 179, 173, 174, 172, 169, 171, 169, 154, 171, 172, 166, 167, 172, 165, 166, 172, 171, 165, 167, 166, 173, 173, 166, 174, 166, 144, 174, 166, 165, 144, 174, 144, 175, 176, 175, 4, 175, 2, 4, 175, 144, 2, 165, 138, 144, 144, 139, 2, 144, 138, 139, 4, 2, 3, 139, 140, 2, 140, 1, 2, 140, 0, 1, 140, 142, 0, 142, 143, 0, 140, 141, 142, 158, 99, 146, 158, 157, 149, 13, 11, 12, 13, 149, 11, 11, 149, 157, 159, 91, 92, 33, 34, 163, 159, 145, 160, 160, 145, 163, 31, 32, 28, 32, 33, 28, 33, 163, 28, 31, 29, 30, 31, 28, 29, 92, 93, 159, 93, 94, 159, 145, 159, 95, 27, 28, 153, 159, 94, 95, 145, 158, 163, 28, 163, 153, 153, 158, 149, 153, 163, 158, 145, 95, 98, 98, 95, 96, 96, 97, 98, 27, 153, 26, 15, 153, 14, 153, 15, 26, 98, 99, 145, 145, 99, 158, 26, 17, 25, 26, 16, 17, 26, 15, 16, 23, 24, 18, 24, 25, 18, 25, 17, 18, 99, 100, 146, 23, 18, 22, 22, 20, 21, 20, 22, 19, 153, 149, 14, 19, 22, 18, 149, 13, 14, 41, 147, 40, 39, 40, 150, 39, 150, 38, 150, 40, 147, 38, 150, 37, 37, 150, 36, 161, 159, 147, 147, 160, 150, 147, 159, 160, 36, 150, 35, 150, 160, 35, 90, 91, 161, 159, 161, 91, 35, 160, 34, 34, 160, 163, 155, 161, 48, 155, 89, 161, 89, 90, 161, 43, 44, 42, 42, 44, 41, 45, 46, 147, 41, 44, 147, 147, 44, 45, 147, 46, 47, 147, 47, 161, 47, 48, 161, 155, 156, 89, 53, 54, 151, 52, 53, 51, 53, 151, 51, 151, 54, 55, 51, 151, 50, 49, 155, 48, 49, 50, 155, 50, 151, 155, 55, 56, 151, 56, 57, 151, 151, 57, 148, 148, 156, 151, 151, 156, 155, 57, 58, 148, 58, 59, 148, 59, 60, 148, 60, 61, 148, 61, 62, 148, 67, 156, 66, 62, 65, 148, 65, 66, 148, 66, 156, 148, 63, 64, 62, 62, 64, 65, 89, 156, 162, 152, 156, 67, 67, 68, 152, 162, 152, 71, 71, 72, 162, 162, 156, 152, 89, 162, 87, 88, 89, 87, 162, 82, 87, 86, 87, 82, 85, 86, 83, 68, 69, 152, 69, 70, 152, 152, 70, 71, 154, 170, 171, 154, 121, 170, 121, 122, 170, 165, 171, 164, 164, 171, 170, 170, 122, 164, 164, 133, 165, 165, 133, 138, 133, 134, 138, 138, 134, 137, 134, 136, 137, 122, 126, 164, 164, 126, 133, 122, 123, 126, 123, 124, 126, 133, 127, 132, 133, 126, 127, 126, 124, 125, 127, 128, 132, 132, 128, 131, 128, 129, 131, 129, 130, 131, 134, 135, 136, 72, 73, 162, 77, 75, 76, 162, 77, 82, 75, 77, 162, 81, 77, 78, 82, 77, 81, 85, 83, 84, 83, 86, 82, 81, 78, 80, 162, 74, 75, 162, 73, 74, 80, 78, 79], "vertices": [6, 12, 11.41, -27.54, 0, 7, 70.65, 84.11, 0, 8, 92.96, 16.4, 0, 9, 74.32, -14.57, 0, 11, 34.12, -22.46, 0.76536, 10, 53.94, -20.77, 0.23464, 5, 12, 10.5, -28.16, 0, 7, 71.15, 83.13, 0, 9, 73.63, -15.42, 3e-05, 11, 33.33, -23.22, 0.76527, 10, 53.18, -21.56, 0.23469, 6, 4, -103.11, 48.88, 0, 7, 65.18, 71.3, 0, 8, 80.01, 11.29, 0.00577, 9, 60.4, -14.85, 0.03531, 11, 20.28, -20.91, 0.60547, 10, 40.06, -19.74, 0.35345, 6, 4, -104.42, 53.76, 0, 7, 69.73, 69.1, 0, 8, 81.64, 6.5, 0.01037, 9, 60.26, -19.9, 0.05554, 11, 19.48, -25.9, 0.50863, 10, 39.44, -24.75, 0.42545, 7, 2, -71.69, -31.69, 0, 4, -101.49, 54.33, 0, 7, 68.24, 66.51, 0, 8, 78.75, 5.74, 0.01286, 9, 57.28, -19.61, 0.06377, 11, 16.57, -25.22, 0.47836, 10, 36.51, -24.18, 0.44501, 6, 2, -70.76, -24.27, 0, 4, -95.27, 58.47, 0, 8, 72.81, 1.21, 0.03932, 9, 50.13, -21.78, 0.1409, 11, 9.19, -26.43, 0.25354, 10, 29.19, -25.67, 0.56625, 7, 2, -74.1, -22.56, 0, 4, -96.16, 62.12, 0, 7, 70.62, 57.37, 1e-05, 8, 73.93, -2.38, 0.04967, 9, 49.93, -25.53, 0.16708, 11, 8.5, -30.13, 0.1988, 10, 28.64, -29.39, 0.58444, 7, 2, -73.5, -21.71, 0, 4, -95.11, 62.22, 0, 7, 70.02, 56.51, 2e-05, 8, 72.9, -2.55, 0.04996, 9, 48.9, -25.33, 0.16759, 11, 7.51, -29.8, 0.19823, 10, 27.63, -29.09, 0.5842, 7, 2, -70.78, -16.25, 0, 4, -89.21, 63.72, 0, 7, 67.28, 51.07, 0.00092, 8, 67.1, -4.43, 0.08656, 9, 42.82, -25.07, 0.23772, 11, 1.51, -28.74, 0.10582, 10, 21.59, -28.26, 0.56899, 7, 2, -70.75, -14.55, 0, 4, -87.9, 64.81, 0, 7, 67.24, 49.37, 0.00174, 8, 65.86, -5.6, 0.10861, 9, 41.25, -25.73, 0.27283, 11, -0.13, -29.19, 0.07325, 10, 19.97, -28.77, 0.54357, 7, 2, -72.94, -7.87, 0, 4, -84.27, 70.84, 0, 7, 69.42, 42.68, 0.00714, 8, 62.63, -11.85, 0.19801, 9, 36.04, -30.46, 0.36943, 11, -5.92, -33.19, 0.01375, 10, 14.34, -32.99, 0.41167, 7, 2, -75.14, -1.18, 0, 4, -80.64, 76.86, 0, 7, 71.59, 35.98, 0.01577, 8, 59.4, -18.1, 0.28038, 9, 30.83, -35.19, 0.40415, 11, -11.7, -37.2, 0.00104, 10, 8.71, -37.21, 0.29867, 7, 2, -75.33, -0.84, 0, 4, -80.5, 77.23, 0, 7, 71.77, 35.64, 0.01607, 8, 59.28, -18.47, 0.28283, 9, 30.59, -35.5, 0.40472, 11, -11.98, -37.47, 0.00087, 10, 8.44, -37.49, 0.29552, 5, 4, -73.8, 81.5, 0, 7, 70.6, 27.78, 0.03371, 8, 52.87, -23.16, 0.38578, 9, 22.94, -37.66, 0.39423, 10, 0.62, -38.92, 0.18629, 5, 4, -67.09, 85.76, 0, 7, 69.43, 19.92, 0.07, 8, 46.46, -27.86, 0.52944, 9, 15.3, -39.82, 0.32444, 10, -7.19, -40.36, 0.07612, 5, 4, -66.83, 87.64, 0, 7, 70.67, 18.5, 0.07965, 8, 46.32, -29.74, 0.56238, 9, 14.51, -41.54, 0.30261, 10, -8.14, -41.99, 0.05536, 5, 4, -65.72, 96.25, 0, 7, 76.44, 12.01, 0.098, 8, 45.77, -38.41, 0.63175, 9, 10.97, -49.46, 0.25572, 10, -12.41, -49.55, 0.01453, 5, 4, -64.26, 100.33, 0, 7, 78.57, 8.23, 0.09996, 8, 44.57, -42.58, 0.64554, 9, 8.39, -52.95, 0.24758, 10, -15.3, -52.78, 0.00692, 5, 4, -61.22, 107.51, 0, 7, 82, 1.23, 0.09957, 8, 42.01, -49.94, 0.65736, 9, 3.41, -58.96, 0.24254, 10, -20.82, -58.29, 0.00053, 5, 4, -60.07, 110.27, 0, 7, 83.33, -1.45, 0.09932, 8, 41.04, -52.77, 0.65834, 9, 1.52, -61.27, 0.24233, 10, -22.92, -60.41, 1e-05, 5, 4, -59, 110.52, 0, 7, 82.82, -2.41, 0.09932, 8, 39.99, -53.08, 0.65836, 9, 0.43, -61.2, 0.24232, 10, -24, -60.24, 0, 4, 4, -58.86, 110.58, 0, 7, 82.77, -2.56, 0.09932, 8, 39.85, -53.15, 0.65836, 9, 0.27, -61.22, 0.24232, 5, 4, -57.64, 109.83, 0, 7, 81.4, -2.99, 0.09939, 8, 38.58, -52.48, 0.6583, 9, -0.68, -60.14, 0.24228, 10, -25.01, -59.09, 3e-05, 5, 4, -57.3, 109.43, 0, 7, 80.88, -2.99, 0.09943, 8, 38.22, -52.11, 0.65824, 9, -0.89, -59.67, 0.24226, 10, -25.18, -58.6, 6e-05, 5, 4, -56.19, 108.21, 0, 7, 79.23, -3.03, 0.09966, 8, 37.03, -50.97, 0.6579, 9, -1.6, -58.18, 0.24219, 10, -25.74, -57.05, 0.00024, 5, 4, -55.72, 106.34, 0, 7, 77.51, -2.15, 0.1002, 8, 36.45, -49.13, 0.65705, 9, -1.51, -56.25, 0.24205, 10, -25.47, -55.14, 0.0007, 5, 4, -52.96, 95.53, 0, 7, 67.54, 2.85, 0.12338, 8, 32.99, -38.51, 0.64356, 9, -1.04, -45.1, 0.22523, 10, -23.96, -44.08, 0.00783, 5, 4, -45.63, 92.66, 0, 7, 60.56, -0.81, 0.19405, 8, 25.49, -36.13, 0.64391, 9, -7.24, -40.25, 0.15857, 10, -29.67, -38.66, 0.00346, 6, 4, -38.29, 89.79, 0, 6, 72.12, 8.61, 3e-05, 7, 53.59, -4.46, 0.32471, 8, 17.98, -33.74, 0.60545, 9, -13.44, -35.39, 0.06967, 10, -35.39, -33.24, 0.00015, 4, 4, -36.76, 96.54, 0, 7, 57.68, -10.05, 0.36399, 8, 16.89, -40.58, 0.59245, 9, -16.85, -41.42, 0.04356, 4, 4, -35.88, 96.83, 0, 7, 57.33, -10.9, 0.36401, 8, 16.03, -40.93, 0.59244, 9, -17.78, -41.45, 0.04355, 4, 4, -34.17, 95.82, 0, 7, 55.44, -11.53, 0.3645, 8, 14.25, -40.02, 0.59181, 9, -19.13, -39.98, 0.04369, 4, 4, -33.36, 95.43, 0, 7, 54.61, -11.89, 0.36469, 8, 13.42, -39.68, 0.59152, 9, -19.79, -39.37, 0.04379, 5, 4, -31.16, 86.72, 0, 6, 65.98, 3.85, 0.00338, 7, 46.59, -7.84, 0.43677, 8, 10.67, -31.14, 0.52668, 9, -19.39, -30.4, 0.03317, 5, 4, -26.96, 85.37, 0, 6, 62.78, 0.82, 0.0148, 7, 42.83, -10.13, 0.57704, 8, 6.39, -30.07, 0.39579, 9, -23.03, -27.9, 0.01237, 5, 4, -20.01, 85.15, 0, 6, 59.23, -5.16, 0.05393, 7, 38.1, -15.24, 0.75114, 8, -0.57, -30.3, 0.19405, 9, -29.63, -25.69, 0.00088, 4, 4, -13.05, 84.93, 0, 6, 55.67, -11.15, 0.11658, 7, 33.37, -20.34, 0.81288, 8, -7.52, -30.53, 0.07055, 3, 6, 54.17, -12.85, 0.13858, 7, 31.55, -21.69, 0.81225, 8, -9.77, -30.18, 0.04917, 3, 6, 52.09, -14.39, 0.16921, 7, 29.19, -22.76, 0.80123, 8, -12.18, -29.26, 0.02956, 4, 5, 65.8, 3.47, 0.00141, 6, 42.8, -21.08, 0.35866, 7, 18.7, -27.36, 0.63992, 8, -22.82, -25.03, 0, 3, 5, 65.5, 3.24, 0.00156, 6, 42.43, -21.16, 0.3635, 7, 18.33, -27.36, 0.63494, 3, 5, 59.08, -2, 0.01148, 6, 34.41, -23.26, 0.52971, 7, 10.05, -27.74, 0.45882, 3, 5, 54.05, -5.7, 0.02576, 6, 28.3, -24.54, 0.63536, 7, 3.81, -27.71, 0.33888, 3, 5, 53.17, -5.96, 0.02602, 6, 27.39, -24.41, 0.63689, 7, 2.94, -27.4, 0.33708, 3, 5, 52.97, -4.82, 0.0282, 6, 27.68, -23.29, 0.64503, 7, 3.46, -26.36, 0.32677, 3, 5, 44.64, -6.25, 0.09284, 6, 19.51, -21.14, 0.77653, 7, -4.08, -22.55, 0.13063, 3, 5, 44.11, -6.35, 0.0983, 6, 18.99, -21.01, 0.78003, 7, -4.56, -22.31, 0.12166, 3, 5, 36.5, -5.78, 0.26628, 6, 12.3, -17.34, 0.71605, 7, -10.34, -17.32, 0.01767, 3, 5, 35.04, -5.94, 0.33344, 6, 10.91, -16.87, 0.65897, 7, -11.6, -16.57, 0.00759, 3, 2, 21.34, 49.34, 0, 5, 22.79, -12.05, 0.94103, 6, -2.78, -17.36, 0.05897, 3, 2, 25.24, 47.38, 0, 5, 18.5, -12.79, 0.99385, 6, -6.99, -16.25, 0.00615, 3, 2, 28.14, 45.96, 0, 4, 22.49, 29.4, 0, 5, 15.32, -13.36, 1, 3, 2, 28.84, 46.71, 0, 4, 23.52, 29.35, 0, 5, 15.19, -14.38, 1, 3, 2, 34.81, 43.71, 0, 4, 25.15, 22.88, 0.00373, 5, 8.62, -15.51, 0.99627, 3, 2, 35.22, 43.54, 0, 4, 25.28, 22.46, 0.00483, 5, 8.18, -15.61, 0.99517, 3, 2, 39.74, 38.98, 0, 4, 24.78, 16.05, 0.06928, 5, 1.83, -14.63, 0.93072, 3, 2, 42.27, 35.64, 0, 4, 23.9, 11.95, 0.18413, 5, -2.19, -13.44, 0.81587, 3, 2, 44.36, 31.98, 0, 4, 22.49, 7.99, 0.39087, 5, -6.03, -11.73, 0.60913, 3, 2, 46.15, 28.58, 0, 4, 21.08, 4.41, 0.64928, 5, -9.49, -10.05, 0.35072, 3, 2, 49.12, 19.7, 0, 3, 33.32, 11.19, 0.00699, 4, 16.29, -3.64, 0.99301, 3, 2, 49.07, 19.15, 0, 3, 33.16, 10.66, 0.00978, 4, 15.85, -3.96, 0.99022, 3, 2, 48.63, 12.19, 0, 3, 31.29, 3.95, 0.14655, 4, 10.29, -8.17, 0.85345, 2, 3, 25.74, -3.04, 0.79637, 4, 1.64, -10.36, 0.20363, 3, 2, 45.57, 3.71, 4e-05, 3, 26.54, -3.72, 0.84051, 4, 1.86, -11.39, 0.15945, 3, 2, 45.17, 2.92, 0.0001, 3, 25.99, -4.41, 0.8502, 4, 1.01, -11.6, 0.1497, 2, 2, 40.71, -1.57, 0.01827, 3, 20.69, -7.88, 0.98173, 2, 2, 35.33, -5.66, 0.11952, 3, 14.58, -10.77, 0.88048, 3, 1, 43.01, 11.31, 0.00976, 2, 24.3, -9.33, 0.74824, 3, 3.04, -12.08, 0.242, 3, 1, 42.14, 10.28, 0.01597, 2, 23.03, -9.77, 0.83066, 3, 1.7, -12.25, 0.15337, 3, 1, 39.29, 5.26, 0.06232, 2, 18.02, -12.63, 0.93528, 3, -3.79, -14.01, 0.0024, 2, 1, 37.02, 3.65, 0.09687, 2, 15.25, -12.86, 0.90313, 2, 1, 31.85, 0.6, 0.25987, 2, 9.24, -12.85, 0.74013, 2, 1, 21.92, -3.24, 0.9357, 2, -1.26, -11.09, 0.0643, 1, 1, 18.15, -3.58, 1, 1, 1, 14.23, -1.99, 1, 1, 1, 12.43, -1.25, 1, 1, 1, 9.66, -1.19, 1, 5, 1, 7.28, 0.19, 0.97094, 2, -12.1, -0.67, 0.02661, 6, 19.67, 38.46, 0.00119, 7, 8.55, 35.7, 0.0012, 8, 14.88, 26.53, 6e-05, 6, 1, 3.26, 2.57, 0.82577, 2, -14.35, 3.43, 0.15442, 3, -32.14, 8.4, 0.00082, 6, 22.71, 34.91, 0.00963, 7, 10.78, 31.6, 0.00866, 8, 13.53, 22.06, 0.0007, 6, 1, 0.46, 3.37, 0.78619, 2, -16.35, 5.55, 0.18769, 3, -33.66, 10.89, 0.00131, 6, 25.1, 33.25, 0.01264, 7, 12.77, 29.47, 0.01122, 8, 13.41, 19.14, 0.00095, 6, 1, 0.51, 5.64, 0.75065, 2, -15.14, 7.47, 0.21605, 3, -32.08, 12.52, 0.00267, 6, 24.31, 31.12, 0.01583, 7, 11.56, 27.55, 0.01348, 8, 11.2, 18.66, 0.00131, 6, 1, 1.52, 7.52, 0.65795, 2, -13.32, 8.57, 0.28803, 3, -30.07, 13.22, 0.0069, 6, 22.75, 29.67, 0.02497, 7, 9.73, 26.46, 0.01978, 8, 9.13, 19.19, 0.00238, 6, 1, 1.36, 9.05, 0.56461, 2, -12.68, 9.97, 0.35585, 3, -29.15, 14.46, 0.01211, 6, 22.42, 28.17, 0.03615, 7, 9.09, 25.06, 0.02752, 8, 7.69, 18.66, 0.00374, 6, 1, -0.04, 11.74, 0.45063, 2, -12.51, 13, 0.43064, 3, -28.36, 17.38, 0.02019, 6, 22.88, 25.17, 0.05324, 7, 8.9, 22.04, 0.03935, 8, 5.41, 16.67, 0.00595, 6, 1, -0.35, 12.74, 0.43283, 2, -12.27, 14.02, 0.441, 3, -27.91, 18.33, 0.02179, 6, 22.85, 24.12, 0.05645, 7, 8.66, 21.02, 0.04153, 8, 4.51, 16.13, 0.0064, 6, 1, 0.04, 14.01, 0.44, 2, -11.28, 14.91, 0.42994, 3, -26.76, 19, 0.02302, 6, 22.07, 23.05, 0.05799, 7, 7.67, 20.13, 0.04233, 8, 3.18, 16.2, 0.00673, 6, 1, 1.58, 16.36, 0.33465, 2, -8.76, 16.15, 0.48703, 3, -24.04, 19.69, 0.03403, 6, 19.85, 21.31, 0.07862, 7, 5.14, 18.89, 0.05581, 8, 0.53, 17.13, 0.00985, 8, 1, 5.68, 20.74, 0.15474, 2, -3, 17.83, 0.48687, 3, -18.06, 20.14, 0.07216, 4, -19.14, 34.62, 0.00033, 6, 14.56, 18.49, 0.15411, 7, -0.62, 17.24, 0.10657, 8, -4.7, 20.07, 0.02509, 9, -15.92, 22.95, 0.00011, 8, 1, 7.22, 22.76, 0.09523, 2, -0.65, 18.78, 0.41972, 3, -15.56, 20.58, 0.08842, 4, -16.89, 33.46, 0.00104, 6, 12.45, 17.07, 0.20562, 7, -2.98, 16.3, 0.14937, 8, -7.03, 21.08, 0.04014, 9, -17.74, 24.72, 0.00046, 9, 1, 9.2, 24.22, 0.04426, 2, 1.81, 19.03, 0.28719, 3, -13.11, 20.32, 0.09737, 4, -15.1, 31.76, 0.00225, 5, 20.54, 23.94, 0.0002, 6, 10.11, 16.33, 0.27731, 7, -5.43, 16.06, 0.22197, 8, -8.92, 22.66, 0.06804, 9, -18.97, 26.85, 0.00141, 7, 2, -0.59, 19.24, 0.15361, 3, -15.41, 21.03, 0.05757, 4, -16.5, 33.72, 0.00068, 6, 12.5, 16.61, 0.2261, 7, -3.04, 15.83, 0.38239, 8, -7.4, 20.8, 0.17262, 9, -18.19, 24.58, 0.00703, 7, 2, -3.08, 18.16, 0.09342, 3, -18.07, 20.48, 0.03444, 4, -18.94, 34.9, 9e-05, 6, 14.71, 18.18, 0.14031, 7, -0.55, 16.91, 0.43538, 8, -4.88, 19.78, 0.27996, 9, -16.19, 22.74, 0.0164, 6, 2, -8.93, 16.53, 0.0364, 3, -24.13, 20.1, 0.01162, 6, 20.1, 20.98, 0.03143, 7, 5.31, 18.52, 0.33312, 8, 0.38, 16.75, 0.52303, 9, -12.32, 18.07, 0.06441, 6, 2, -11.45, 15.29, 0.02136, 3, -26.85, 19.4, 0.00606, 6, 22.31, 22.71, 0.01029, 7, 7.84, 19.75, 0.2207, 8, 3.03, 15.82, 0.61562, 9, -10.15, 16.27, 0.12597, 6, 2, -12.29, 14.41, 0.0163, 3, -27.86, 18.72, 0.00427, 6, 22.96, 23.74, 0.00524, 7, 8.68, 20.62, 0.16954, 8, 4.25, 15.83, 0.63333, 9, -9.01, 15.86, 0.17133, 6, 2, -12.8, 13.21, 0.01241, 3, -28.6, 17.65, 0.00291, 6, 23.21, 25.02, 0.00216, 7, 9.2, 21.82, 0.1268, 8, 5.46, 16.31, 0.62975, 9, -7.71, 15.88, 0.22597, 6, 2, -13.11, 10.23, 0.00787, 3, -29.52, 14.8, 0.00137, 6, 22.89, 28, 8e-05, 7, 9.51, 24.8, 0.07374, 8, 7.8, 18.17, 0.59158, 9, -4.86, 16.81, 0.32536, 5, 2, -13.73, 8.78, 0.00688, 3, -30.43, 13.5, 0.00106, 7, 10.14, 26.25, 0.06213, 8, 9.27, 18.75, 0.57245, 9, -3.28, 16.84, 0.35748, 5, 2, -15.22, 7.98, 0.00575, 3, -32.05, 13.04, 0.00076, 7, 11.63, 27.04, 0.04932, 8, 10.89, 18.25, 0.53387, 9, -1.95, 15.8, 0.4103, 5, 2, -16.88, 5.61, 0.00377, 3, -34.16, 11.06, 0.00027, 7, 13.3, 29.41, 0.02733, 8, 13.74, 18.72, 0.445, 9, 0.89, 15.25, 0.52364, 5, 2, -14.63, 3.12, 0.00583, 3, -32.48, 8.16, 1e-05, 7, 11.06, 31.9, 0.03617, 8, 13.94, 22.07, 0.75864, 9, 2.25, 18.32, 0.19936, 5, 12, -44.51, 27.43, 0.07465, 3, -31.11, 3.85, 0, 7, 8.85, 35.85, 0.00014, 11, -29.88, 22.85, 0.48036, 10, -11.7, 22.13, 0.44485, 7, 12, -42.22, 29.13, 0.07258, 2, -11.01, -3.3, 0, 3, -30.26, 1.13, 0, 7, 7.46, 38.34, 4e-05, 8, 15.99, 29.16, 0.00039, 11, -27.89, 24.9, 0.45183, 10, -9.79, 24.24, 0.47516, 7, 12, -41.09, 31.64, 0.07864, 2, -8.66, -4.74, 0, 3, -28.27, -0.77, 0, 7, 5.12, 39.79, 4e-05, 8, 15.37, 31.84, 0.00036, 11, -27.17, 27.55, 0.49265, 10, -9.17, 26.92, 0.42831, 5, 12, -38.7, 33.98, 0.08209, 7, 3.11, 42.46, 4e-05, 8, 15.86, 35.15, 0.00035, 11, -25.19, 30.25, 0.51444, 10, -7.29, 29.69, 0.40308, 5, 12, -37.5, 35.12, 0.08236, 7, 2.14, 43.8, 4e-05, 8, 16.13, 36.78, 0.00036, 11, -24.19, 31.56, 0.51848, 10, -6.34, 31.04, 0.39877, 5, 12, -35.9, 32.43, 0.08324, 7, 5.02, 45.03, 3e-05, 8, 19.03, 35.6, 0.00029, 11, -22.18, 29.16, 0.50082, 10, -4.24, 28.71, 0.41563, 5, 12, -36.08, 29.74, 0.08664, 7, 7.66, 44.5, 1e-05, 8, 20.51, 33.35, 0.00013, 11, -21.93, 26.48, 0.46586, 10, -3.89, 26.05, 0.44735, 5, 12, -30.6, 38.89, 0.10226, 7, -0.7, 51.13, 1e-05, 8, 19.35, 43.95, 0.0001, 11, -17.98, 36.39, 0.59352, 10, -0.31, 36.1, 0.30411, 5, 12, -25.12, 48.04, 0.09185, 7, -9.06, 57.76, 1e-05, 8, 18.18, 54.56, 0.00011, 11, -14.04, 46.3, 0.63498, 10, 3.26, 46.15, 0.27304, 6, 1, 33.81, -12.76, 0.15457, 12, -22.28, 46.93, 0.07799, 7, -7.58, 60.44, 1e-05, 8, 21.12, 55.39, 9e-05, 11, -11.05, 45.65, 0.53632, 10, 6.27, 45.61, 0.23102, 5, 12, -21.29, 46.64, 0.09227, 7, -7.16, 61.38, 1e-05, 8, 22.08, 55.75, 0.00011, 11, -10.03, 45.53, 0.63436, 10, 7.29, 45.52, 0.27325, 5, 12, -21.77, 46.74, 0.09226, 7, -7.32, 60.91, 1e-05, 8, 21.64, 55.54, 0.00011, 11, -10.53, 45.55, 0.63436, 10, 6.8, 45.52, 0.27326, 5, 12, -21.5, 44.64, 0.09378, 7, -5.21, 60.9, 1e-05, 8, 23.12, 54.03, 0.00011, 11, -9.92, 43.52, 0.63125, 10, 7.48, 43.52, 0.27485, 5, 12, -20.33, 37.7, 0.11078, 7, 1.82, 61.16, 1e-05, 8, 28.24, 49.21, 9e-05, 11, -7.66, 36.86, 0.60246, 10, 9.99, 36.95, 0.28666, 6, 12, -20.49, 27.01, 0.20667, 2, -15.87, -24.59, 0, 7, 12.4, 59.61, 0, 8, 34.58, 40.6, 3e-05, 11, -6.1, 26.28, 0.55837, 10, 11.94, 26.44, 0.23493, 5, 12, -11.27, 31.4, 0.32074, 7, 9.25, 69.32, 0, 8, 39.28, 49.67, 1e-05, 11, 2.3, 32.09, 0.59175, 10, 20.11, 32.56, 0.08749, 6, 1, 24.3, -33.27, 0.016, 12, -7.22, 30.06, 0.32071, 7, 11.11, 73.16, 0, 8, 43.31, 51.05, 1e-05, 11, 6.51, 31.42, 0.58503, 10, 24.34, 32.04, 0.07825, 5, 12, -5.51, 29.66, 0.32629, 7, 11.73, 74.81, 0, 8, 44.92, 51.76, 1e-05, 11, 8.26, 31.29, 0.5951, 10, 26.1, 31.98, 0.07859, 5, 12, -6.24, 27.53, 0.32664, 7, 13.75, 73.8, 0, 8, 45.62, 49.61, 1e-05, 11, 7.88, 29.07, 0.59423, 10, 25.8, 29.75, 0.07911, 5, 12, -11.34, 24.15, 0.35691, 7, 16.43, 68.3, 0, 8, 43.6, 43.84, 1e-05, 11, 3.39, 24.92, 0.56032, 10, 21.47, 25.43, 0.08276, 7, 1, 17.72, -39.88, 0.0904, 12, -3.76, 21.4, 0.56933, 2, -23.56, -40.47, 0, 7, 20.15, 75.46, 0, 8, 51.3, 46.23, 0, 11, 11.31, 23.42, 0.32891, 10, 29.44, 24.23, 0.01135, 7, 1, 18.21, -47.93, 0.03466, 12, 3.82, 18.65, 0.84156, 2, -27.25, -47.64, 0, 7, 23.87, 82.61, 0, 8, 59, 48.62, 0, 11, 19.23, 21.92, 0.12375, 10, 37.42, 23.02, 2e-05, 5, 1, 18.42, -50.08, 0.02707, 12, 5.87, 17.99, 0.8883, 2, -28.16, -49.59, 0, 8, 61.04, 49.34, 0, 11, 21.37, 21.6, 0.08463, 5, 1, 18.73, -54.01, 0.02251, 12, 9.6, 16.72, 0.91853, 2, -29.9, -53.13, 0, 8, 64.78, 50.58, 0, 11, 25.25, 20.94, 0.05896, 4, 1, 18.81, -54.13, 0.02254, 12, 9.75, 16.74, 0.91851, 2, -29.89, -53.28, 0, 11, 25.39, 20.99, 0.05895, 5, 1, 13.61, -52.89, 0.01576, 12, 6.54, 12.46, 0.94416, 2, -33.73, -49.55, 0, 8, 64.91, 45.34, 0, 11, 22.91, 16.25, 0.04007, 4, 1, 15.31, -59.43, 0.00104, 12, 13.22, 11.43, 0.99896, 2, -35.6, -56.05, 0, 11, 29.67, 16.3, 0, 4, 1, 16.33, -61.89, 8e-05, 12, 15.89, 11.39, 0.99992, 2, -35.98, -58.69, 0, 11, 32.31, 16.69, 0, 4, 12, 17.5, 10.77, 1, 2, -36.8, -60.2, 0, 4, -100.32, 9.29, 0, 11, 34, 16.33, 0, 5, 1, 16.35, -63.72, 0, 12, 17.57, 10.69, 1, 2, -36.89, -60.27, 0, 4, -100.43, 9.32, 0, 11, 34.09, 16.26, 0, 5, 1, 16.17, -63.68, 0, 12, 17.46, 10.54, 1, 2, -37.03, -60.14, 0, 4, -100.42, 9.51, 0, 11, 34, 16.09, 0, 3, 12, 16.53, 7.87, 1, 2, -39.56, -58.87, 0, 11, 33.5, 13.31, 0, 3, 1, 7.19, -61.33, 0.00018, 12, 11.74, 3.23, 0.99982, 2, -43.55, -53.54, 0, 4, 12, 17.59, 1.18, 1, 2, -46.33, -59.08, 0, 4, -105.69, 17.24, 0, 10, 54.36, 8.6, 0, 2, 12, 20.01, -0.78, 1, 10, 57.12, 7.15, 0, 2, 12, 19.74, -1.17, 1, 10, 56.93, 6.72, 0, 1, 12, 18.84, -3.76, 1, 5, 12, 9.5, -6.74, 0.69338, 7, 49.78, 84.93, 0, 9, 66.5, 4.8, 1e-05, 11, 28.9, -2.24, 0.28749, 10, 47.98, -0.75, 0.01912, 6, 12, 5.36, -17.39, 0.02565, 7, 59.8, 79.44, 0, 8, 82.01, 20.84, 0.00028, 9, 65.61, -6.6, 0.00715, 11, 26.53, -13.41, 0.75518, 10, 46.02, -12.01, 0.21174, 5, 12, 10.66, -23.61, 3e-05, 7, 66.65, 83.88, 0, 9, 72.47, -11.02, 0.00096, 11, 32.75, -18.7, 0.76375, 10, 52.44, -17.06, 0.23527, 5, 7, 68.82, 85.2, 0, 8, 92.45, 18.47, 0, 9, 74.56, -12.45, 4e-05, 11, 34.64, -20.39, 0.76535, 10, 54.39, -18.69, 0.2346, 6, 12, 12.12, -25.88, 0, 7, 69.1, 85.03, 0, 8, 92.53, 18.16, 0, 9, 74.53, -12.77, 5e-05, 11, 34.56, -20.71, 0.76535, 10, 54.32, -19, 0.23461, 5, 12, 11.55, -27.54, 0, 7, 70.67, 84.25, 0, 8, 93.07, 16.49, 0, 11, 34.26, -22.44, 0.76537, 10, 54.09, -20.74, 0.23463, 5, 4, -90.84, 36.7, 0, 8, 66.98, 22.65, 0.00083, 9, 52.15, 0.35, 0.00729, 11, 14.1, -4.76, 0.85703, 10, 33.28, -3.83, 0.13486, 3, 4, -38.34, 57.18, 0, 7, 28.99, 16.94, 0.00966, 8, 15.91, -1.2, 0.99034, 5, 2, -38.67, -8.82, 0, 4, -62.62, 44.25, 0, 8, 39.3, 13.28, 0.00029, 9, 22.95, 1.23, 0.00167, 10, 4.29, -0.21, 0.99805, 3, 5, 48.31, 12.58, 0.00432, 6, 30.67, -5.53, 0.36231, 7, 10.1, -9.61, 0.63338, 3, 4, 4.6, 1.56, 0.9566, 5, -11.08, 6.59, 0.04192, 6, -25.86, 13.66, 0.00148, 5, 4, -63.54, 73.93, 0, 7, 58.16, 25, 0.04474, 8, 42.15, -16.27, 0.44357, 9, 15.3, -27.46, 0.40063, 10, -6.03, -28.05, 0.11106, 3, 6, 48.03, -7.6, 0.13844, 7, 26.65, -15.27, 0.82149, 8, -8.65, -22.18, 0.04007, 2, 2, 28.94, 38.32, 0, 5, 10.14, -7.68, 1, 2, 1, 33.16, 10.86, 0.03199, 2, 15.6, -4.69, 0.96801, 5, 4, -51.15, 82.35, 0, 7, 56.4, 10.12, 0.14362, 8, 30.33, -25.49, 0.6338, 9, 1.01, -31.97, 0.20819, 10, -20.68, -31.2, 0.01438, 5, 12, -9.15, 14.17, 0.4637, 7, 26.61, 69.18, 0, 8, 51.38, 37.22, 0, 11, 7.15, 15.42, 0.5164, 10, 25.59, 16.08, 0.0199, 2, 5, 22.59, 2.68, 0.38369, 6, 3.15, -3.87, 0.61631, 7, 2, 22.33, 12.75, 0.00938, 3, 5.67, 9.93, 0.52414, 4, -6.46, 12.12, 0.18876, 5, 0.3, 16.82, 0.14956, 6, -11.27, 18.25, 0.12031, 7, -25.93, 22.41, 0.00755, 8, -18.82, 41.7, 0.00031, 6, 4, -78.16, 59.7, 0, 7, 57, 45.36, 0.0023, 8, 55.81, -1.14, 0.12172, 9, 33.38, -18.04, 0.33785, 11, -6.92, -20.53, 0.01638, 10, 12.87, -20.38, 0.52175, 5, 4, -55.21, 59.07, 0, 7, 41.49, 28.45, 0.01246, 8, 32.88, -1.99, 0.32167, 9, 11.59, -10.84, 0.65287, 10, -8.15, -11.15, 0.01301, 8, 1, -6.58, 19.84, 0.07588, 2, -14, 23.3, 0.01121, 3, -27.68, 27.77, 0.00303, 4, -22.18, 46.53, 0, 6, 26.45, 15.39, 0.00154, 7, 10.35, 11.72, 0.23784, 8, -0.91, 8.38, 0.63135, 9, -16.44, 10.68, 0.03915, 5, 2, -32.2, 37.4, 0, 4, -23.38, 69.52, 0, 6, 47.17, 5.35, 0.01529, 7, 28.51, -2.44, 0.7202, 8, 1.79, -14.48, 0.26451, 7, 1, 0.14, 35.36, 0.00169, 2, -0.31, 33.23, 0.00842, 3, -12.24, 34.65, 0.00405, 6, 15.1, 2.86, 0.57332, 7, -3.37, 1.85, 0.39724, 8, -17.58, 11.2, 0.01523, 9, -31.08, 19.14, 4e-05, 5, 2, -0.03, 0.93, 0.99137, 3, -18.65, 2.99, 0.00281, 6, 8.18, 34.42, 0.00358, 7, -3.53, 34.15, 0.00194, 8, 5.28, 34.02, 0.0003, 7, 2, -46.29, 30.51, 0, 4, -37.79, 75.69, 0, 6, 59.53, 14.98, 5e-05, 7, 42.62, 4.4, 0.3054, 8, 16.57, -19.71, 0.64608, 9, -9.87, -21.75, 0.04843, 10, -30.55, -20, 4e-05, 9, 1, 8.15, -46.71, 0.00729, 12, -1.3, 9.91, 0.60928, 2, -35.27, -41.45, 0, 4, -85.12, 20.37, 0, 7, 31.86, 76.4, 0, 8, 60.21, 38.57, 0.00014, 9, 51.37, 17.63, 0.0012, 11, 15.58, 12.47, 0.35241, 10, 34.12, 13.45, 0.02968, 9, 1, 2.67, -45.93, 0.0035, 12, -4.18, 5.18, 0.38047, 2, -39.59, -37.99, 0, 4, -85.32, 25.91, 0, 7, 36.18, 72.92, 0, 8, 60.77, 33.05, 0.00034, 9, 49.96, 12.27, 0.00298, 11, 13.49, 7.34, 0.55016, 10, 32.22, 8.24, 0.06255, 9, 1, -1.03, -43.05, 0.00248, 12, -8.3, 2.93, 0.27013, 2, -41.3, -33.62, 0, 4, -83.12, 30.05, 0, 7, 37.87, 68.55, 0, 8, 58.85, 28.78, 0.00041, 9, 46.67, 8.93, 0.00348, 11, 9.79, 4.46, 0.52396, 10, 28.63, 5.23, 0.19954, 9, 1, -3.34, -38.51, 0.00192, 12, -13.37, 2.61, 0.20883, 2, -40.97, -28.55, 0, 4, -79.06, 33.12, 0, 7, 37.52, 63.48, 0, 8, 54.99, 25.46, 0.00039, 9, 41.9, 7.16, 0.00318, 11, 4.83, 3.34, 0.42307, 10, 23.72, 3.92, 0.36261, 9, 1, 1.18, -36.6, 0.00134, 12, -13.34, 7.52, 0.25774, 2, -36.11, -29.2, 0, 4, -76.39, 29, 0, 7, 32.66, 64.15, 0, 8, 52.06, 29.39, 0.00028, 9, 40.52, 11.87, 0.00222, 11, 4.08, 8.19, 0.45593, 10, 22.78, 8.74, 0.2825, 9, 1, 6.43, -36.44, 0.00051, 12, -11.4, 12.4, 0.37048, 2, -31.51, -31.74, 0, 4, -75.31, 23.86, 0, 7, 28.07, 66.71, 0, 8, 50.65, 34.45, 0.00011, 9, 40.97, 17.1, 0.00084, 11, 5.21, 13.31, 0.49592, 10, 23.72, 13.9, 0.13214, 9, 1, 8.85, -42.56, 0.00712, 12, -4.82, 12.19, 0.54406, 2, -32.56, -38.24, 0, 4, -80.91, 20.41, 0, 7, 29.14, 73.2, 0, 8, 56.02, 38.26, 7e-05, 9, 47.33, 18.8, 0.00063, 11, 11.74, 14.16, 0.42339, 10, 30.21, 14.99, 0.02473, 9, 1, 7.37, -40.81, 0.00446, 12, -7.02, 11.52, 0.4671, 2, -32.94, -35.98, 0, 4, -79.45, 22.18, 0, 7, 29.52, 70.94, 0, 8, 54.68, 36.4, 0.00011, 9, 45.42, 17.53, 0.00095, 11, 9.68, 13.15, 0.46172, 10, 28.19, 13.9, 0.06565, 9, 1, 4.56, -37.69, 0.00192, 12, -11, 10.18, 0.35104, 2, -33.77, -31.87, 0, 4, -76.87, 25.49, 0, 7, 30.33, 66.82, 0, 8, 52.32, 32.92, 0.00018, 9, 42, 15.1, 0.00143, 11, 5.97, 11.19, 0.47039, 10, 24.56, 11.81, 0.17504, 9, 1, -9.38, -39.12, 0.00124, 12, -15.22, -3.18, 0.13477, 2, -46.48, -25.98, 0, 4, -80.72, 38.96, 0, 7, 43.02, 60.89, 0.00046, 8, 57.02, 19.73, 0.0246, 9, 41.8, 1.09, 0.07041, 11, 3.93, -2.67, 0.4097, 10, 23.04, -2.12, 0.35882, 9, 1, -11.84, -43.9, 0.00067, 12, -11.8, -7.34, 0.07337, 2, -51.04, -28.84, 0, 4, -85.86, 40.55, 0, 7, 47.59, 63.74, 0.00025, 8, 62.26, 18.48, 0.0184, 9, 46.27, -1.91, 0.05772, 11, 7.97, -6.23, 0.54094, 10, 27.21, -5.53, 0.30866, 9, 1, -16.74, -45.93, 0.00049, 12, -11.88, -12.64, 0.05351, 2, -56.29, -28.09, 0, 4, -88.72, 45.02, 0, 7, 52.83, 62.96, 0.00018, 8, 65.4, 14.21, 0.02273, 9, 47.72, -7.01, 0.07604, 11, 8.74, -11.48, 0.47712, 10, 28.18, -10.74, 0.36992, 9, 1, -22.44, -43.36, 0.00032, 12, -16.5, -16.85, 0.0344, 2, -59.88, -22.97, 0, 4, -87.19, 51.08, 0, 7, 56.41, 57.83, 0.00066, 8, 64.26, 8.06, 0.05096, 9, 44.52, -12.38, 0.14926, 11, 4.86, -16.38, 0.33067, 10, 24.48, -15.78, 0.43374, 9, 1, -22.59, -39.54, 0.00036, 12, -20.07, -15.46, 0.03903, 2, -58.05, -19.61, 0, 4, -83.44, 51.89, 0, 7, 54.57, 54.48, 0.00113, 8, 60.58, 7.01, 0.06864, 9, 40.7, -12.08, 0.19509, 11, 1.11, -15.58, 0.24638, 10, 20.71, -15.13, 0.44937, 9, 1, -17.35, -36.23, 0.00052, 12, -21.02, -9.34, 0.05701, 2, -51.86, -19.44, 0, 4, -79.27, 47.31, 0, 7, 48.38, 54.33, 0.00077, 8, 56.12, 11.31, 0.04598, 9, 38.02, -6.49, 0.13094, 11, -0.81, -9.69, 0.24708, 10, 18.57, -9.31, 0.51769, 9, 1, -11.82, -37.57, 0.00098, 12, -17.61, -4.8, 0.1067, 2, -47.79, -23.4, 0, 4, -79.62, 41.64, 0, 7, 44.32, 58.31, 0.00053, 8, 56.1, 16.99, 0.03004, 9, 39.98, -1.16, 0.08581, 11, 1.83, -4.66, 0.34625, 10, 21.02, -4.18, 0.42968, 9, 1, -16.22, -37.49, 0.00057, 12, -19.43, -8.81, 0.06197, 2, -51.54, -21.09, 0, 4, -80.31, 45.98, 0, 7, 48.06, 55.98, 0.00068, 8, 57.08, 12.7, 0.04092, 9, 39.4, -5.52, 0.11737, 11, 0.68, -8.91, 0.29756, 10, 20.03, -8.47, 0.48093, 9, 1, -20.9, -39.59, 0.00041, 12, -19.35, -13.94, 0.04431, 2, -56.63, -20.51, 0, 4, -83.2, 50.22, 0, 7, 53.15, 55.39, 0.00094, 8, 60.23, 8.65, 0.05868, 9, 40.94, -10.41, 0.16774, 11, 1.58, -13.96, 0.2727, 10, 21.11, -13.49, 0.45522], "hull": 144, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 208, 210, 210, 212, 212, 214, 214, 216, 216, 218, 218, 220, 220, 222, 222, 224, 224, 226, 226, 228, 228, 230, 230, 232, 232, 234, 234, 236, 236, 238, 238, 240, 240, 242, 242, 244, 244, 246, 246, 248, 248, 250, 250, 252, 252, 254, 254, 256, 256, 258, 258, 260, 260, 262, 262, 264, 264, 266, 266, 268, 268, 270, 270, 272, 272, 274, 274, 276, 276, 278, 278, 280, 280, 282, 282, 284, 284, 286, 286, 0, 328, 330, 330, 332, 332, 334, 334, 336, 336, 338, 338, 308, 308, 340, 340, 328, 328, 342, 342, 344, 344, 334, 346, 348, 348, 350, 350, 352, 352, 354, 354, 356, 356, 358, 358, 346, 358, 360, 360, 362, 362, 352], "width": 135, "height": 135}}, "ef/luoxuanqiu_0000": {"ef/luoxuanqiu_0000": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [137, -136, -136, -136, -136, 137, 137, 137], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 68, "height": 68}, "ef/luoxuanqiu_0001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [137, -136, -136, -136, -136, 137, 137, 137], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 68, "height": 68}, "ef/luoxuanqiu_0002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [137, -136, -136, -136, -136, 137, 137, 137], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 68, "height": 68}, "ef/luoxuanqiu_0003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [137, -136, -136, -136, -136, 137, 137, 137], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 68, "height": 68}, "ef/luoxuanqiu_0004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [137, -136, -136, -136, -136, 137, 137, 137], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 68, "height": 68}, "ef/luoxuanqiu_0005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [137, -136, -136, -136, -136, 137, 137, 137], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 68, "height": 68}}, "qipao/huo01": {"qipao/huo01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15, -11, -15, -11, -15, 11, 15, 11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 22}, "qipao/huo02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15, -11, -15, -11, -15, 11, 15, 11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 22}, "qipao/huo03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15, -11, -15, -11, -15, 11, 15, 11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 22}, "qipao/huo04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15, -11, -15, -11, -15, 11, 15, 11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 22}, "qipao/huo05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15, -11, -15, -11, -15, 11, 15, 11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 22}, "qipao/huo06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15, -11, -15, -11, -15, 11, 15, 11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 22}, "qipao/huo07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15, -11, -15, -11, -15, 11, 15, 11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 22}, "qipao/huo08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15, -11, -15, -11, -15, 11, 15, 11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 22}, "qipao/huo09": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15, -11, -15, -11, -15, 11, 15, 11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 22}, "qipao/huo010": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15, -11, -15, -11, -15, 11, 15, 11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 22}}}}], "animations": {"long_yuan": {"slots": {"ef/huoquan2_2": {"color": [{"time": 0.4667, "color": "ffffff00"}, {"time": 0.6, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9333, "color": "ffffffff"}, {"time": 1.0667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.1333, "color": "ffffff00"}, {"time": 1.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6, "color": "ffffffff"}, {"time": 1.7333, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 0.4667, "name": "ef/huoquan3_0001"}, {"time": 0.5333, "name": "ef/huoquan3_0003"}, {"time": 0.6, "name": "ef/huoquan3_0005"}, {"time": 0.6667, "name": "ef/huoquan3_0007"}, {"time": 0.7333, "name": "ef/huoquan3_0009"}, {"time": 0.8, "name": "ef/huoquan3_0011"}, {"time": 0.8667, "name": "ef/huoquan3_0013"}, {"time": 0.9333, "name": "ef/huoquan3_0015"}, {"time": 1, "name": "ef/huoquan3_0017"}, {"time": 1.0667, "name": null}, {"time": 1.1333, "name": "ef/huoquan3_0001"}, {"time": 1.2, "name": "ef/huoquan3_0003"}, {"time": 1.2667, "name": "ef/huoquan3_0005"}, {"time": 1.3333, "name": "ef/huoquan3_0007"}, {"time": 1.4, "name": "ef/huoquan3_0009"}, {"time": 1.4667, "name": "ef/huoquan3_0011"}, {"time": 1.5333, "name": "ef/huoquan3_0013"}, {"time": 1.6, "name": "ef/huoquan3_0015"}, {"time": 1.6667, "name": "ef/huoquan3_0017"}, {"time": 1.7333, "name": null}]}, "ef/baidian2": {"color": [{"color": "ffdf72ff"}]}, "A_shuiliu_00000": {"attachment": [{"time": 0.0667, "name": "A_shuiliu_00018"}, {"time": 0.1, "name": "A_shuiliu_00017"}, {"time": 0.1667, "name": "A_shuiliu_00016"}, {"time": 0.2333, "name": "A_shuiliu_00014"}, {"time": 0.3, "name": "A_shuiliu_00013"}, {"time": 0.3667, "name": "A_shuiliu_00012"}, {"time": 0.4, "name": "A_shuiliu_00011"}, {"time": 0.4667, "name": "A_shuiliu_00010"}, {"time": 0.5333, "name": "A_shuiliu_00009"}, {"time": 0.5667, "name": "A_shuiliu_00008"}, {"time": 0.6333, "name": "A_shuiliu_00007"}, {"time": 0.6667, "name": "A_shuiliu_00006"}, {"time": 0.7, "name": "A_shuiliu_00005"}, {"time": 0.7667, "name": "A_shuiliu_00004"}, {"time": 0.8333, "name": "A_shuiliu_00003"}, {"time": 0.8667, "name": "A_shuiliu_00002"}, {"time": 0.9333, "name": "A_shuiliu_00001"}, {"time": 1, "name": "A_shuiliu_00019"}, {"time": 1.0667, "name": "A_shuiliu_00018"}, {"time": 1.1, "name": "A_shuiliu_00017"}, {"time": 1.1667, "name": "A_shuiliu_00016"}, {"time": 1.2333, "name": "A_shuiliu_00014"}, {"time": 1.3, "name": "A_shuiliu_00013"}, {"time": 1.3667, "name": "A_shuiliu_00012"}, {"time": 1.4, "name": "A_shuiliu_00011"}, {"time": 1.4667, "name": "A_shuiliu_00010"}, {"time": 1.5333, "name": "A_shuiliu_00009"}, {"time": 1.5667, "name": "A_shuiliu_00008"}, {"time": 1.6333, "name": "A_shuiliu_00007"}, {"time": 1.6667, "name": "A_shuiliu_00006"}, {"time": 1.7, "name": "A_shuiliu_00005"}, {"time": 1.7667, "name": "A_shuiliu_00004"}, {"time": 1.8333, "name": "A_shuiliu_00003"}, {"time": 1.8667, "name": "A_shuiliu_00002"}, {"time": 1.9333, "name": "A_shuiliu_00001"}, {"time": 2, "name": "A_shuiliu_00000"}]}, "qipao/huo01": {"attachment": [{"name": null}, {"time": 0.3333, "name": "qipao/huo01"}, {"time": 0.3667, "name": "qipao/huo02"}, {"time": 0.4333, "name": "qipao/huo03"}, {"time": 0.5, "name": "qipao/huo04"}, {"time": 0.5333, "name": "qipao/huo05"}, {"time": 0.6, "name": "qipao/huo06"}, {"time": 0.6667, "name": "qipao/huo07"}, {"time": 0.7, "name": "qipao/huo08"}, {"time": 0.7667, "name": "qipao/huo09"}, {"time": 0.8333, "name": "qipao/huo010"}, {"time": 0.8667, "name": null}, {"time": 1.3, "name": "qipao/huo01"}, {"time": 1.3333, "name": "qipao/huo02"}, {"time": 1.4, "name": "qipao/huo03"}, {"time": 1.4667, "name": "qipao/huo04"}, {"time": 1.5, "name": "qipao/huo05"}, {"time": 1.5667, "name": "qipao/huo06"}, {"time": 1.6333, "name": "qipao/huo07"}, {"time": 1.6667, "name": "qipao/huo08"}, {"time": 1.7333, "name": "qipao/huo09"}, {"time": 1.8, "name": "qipao/huo010"}, {"time": 1.8333, "name": null}]}, "ef/luoxuanqiu_0000": {"attachment": [{"name": "ef/luoxuanqiu_0000"}, {"time": 0.0667, "name": "ef/luoxuanqiu_0001"}, {"time": 0.1333, "name": "ef/luoxuanqiu_0002"}, {"time": 0.2, "name": "ef/luoxuanqiu_0003"}, {"time": 0.2667, "name": "ef/luoxuanqiu_0004"}, {"time": 0.3333, "name": "ef/luoxuanqiu_0005"}, {"time": 0.4, "name": "ef/luoxuanqiu_0000"}, {"time": 0.4667, "name": "ef/luoxuanqiu_0001"}, {"time": 0.5333, "name": "ef/luoxuanqiu_0002"}, {"time": 0.6, "name": "ef/luoxuanqiu_0003"}, {"time": 0.6667, "name": "ef/luoxuanqiu_0004"}, {"time": 0.7333, "name": "ef/luoxuanqiu_0005"}, {"time": 0.8, "name": "ef/luoxuanqiu_0000"}, {"time": 0.8667, "name": "ef/luoxuanqiu_0001"}, {"time": 0.9333, "name": "ef/luoxuanqiu_0002"}, {"time": 1, "name": "ef/luoxuanqiu_0003"}, {"time": 1.0667, "name": "ef/luoxuanqiu_0004"}, {"time": 1.1333, "name": "ef/luoxuanqiu_0005"}, {"time": 1.2, "name": "ef/luoxuanqiu_0000"}, {"time": 1.2667, "name": "ef/luoxuanqiu_0001"}, {"time": 1.3333, "name": "ef/luoxuanqiu_0002"}, {"time": 1.4, "name": "ef/luoxuanqiu_0003"}, {"time": 1.4667, "name": "ef/luoxuanqiu_0004"}, {"time": 1.5333, "name": "ef/luoxuanqiu_0005"}, {"time": 1.6, "name": "ef/luoxuanqiu_0000"}, {"time": 1.6667, "name": "ef/luoxuanqiu_0001"}, {"time": 1.7333, "name": "ef/luoxuanqiu_0002"}, {"time": 1.8, "name": "ef/luoxuanqiu_0003"}, {"time": 1.8667, "name": "ef/luoxuanqiu_0004"}, {"time": 1.9333, "name": "ef/luoxuanqiu_0005"}, {"time": 2, "name": "ef/luoxuanqiu_0000"}]}, "qipao/huo1": {"attachment": [{"name": null}, {"time": 0.6333, "name": "qipao/huo01"}, {"time": 0.6667, "name": "qipao/huo02"}, {"time": 0.7333, "name": "qipao/huo03"}, {"time": 0.8, "name": "qipao/huo04"}, {"time": 0.8333, "name": "qipao/huo05"}, {"time": 0.9, "name": "qipao/huo06"}, {"time": 0.9667, "name": "qipao/huo07"}, {"time": 1, "name": "qipao/huo08"}, {"time": 1.0667, "name": "qipao/huo09"}, {"time": 1.1333, "name": "qipao/huo010"}, {"time": 1.1667, "name": null}]}, "ef/glow_117": {"color": [{"color": "d32800ff"}]}, "A_shuiliu_0": {"attachment": [{"time": 0.0667, "name": "A_shuiliu_00018"}, {"time": 0.1, "name": "A_shuiliu_00017"}, {"time": 0.1667, "name": "A_shuiliu_00016"}, {"time": 0.2333, "name": "A_shuiliu_00014"}, {"time": 0.3, "name": "A_shuiliu_00013"}, {"time": 0.3667, "name": "A_shuiliu_00012"}, {"time": 0.4, "name": "A_shuiliu_00011"}, {"time": 0.4667, "name": "A_shuiliu_00010"}, {"time": 0.5333, "name": "A_shuiliu_00009"}, {"time": 0.5667, "name": "A_shuiliu_00008"}, {"time": 0.6333, "name": "A_shuiliu_00007"}, {"time": 0.6667, "name": "A_shuiliu_00006"}, {"time": 0.7, "name": "A_shuiliu_00005"}, {"time": 0.7667, "name": "A_shuiliu_00004"}, {"time": 0.8333, "name": "A_shuiliu_00003"}, {"time": 0.8667, "name": "A_shuiliu_00002"}, {"time": 0.9333, "name": "A_shuiliu_00001"}, {"time": 1, "name": "A_shuiliu_00019"}, {"time": 1.0667, "name": "A_shuiliu_00018"}, {"time": 1.1, "name": "A_shuiliu_00017"}, {"time": 1.1667, "name": "A_shuiliu_00016"}, {"time": 1.2333, "name": "A_shuiliu_00014"}, {"time": 1.3, "name": "A_shuiliu_00013"}, {"time": 1.3667, "name": "A_shuiliu_00012"}, {"time": 1.4, "name": "A_shuiliu_00011"}, {"time": 1.4667, "name": "A_shuiliu_00010"}, {"time": 1.5333, "name": "A_shuiliu_00009"}, {"time": 1.5667, "name": "A_shuiliu_00008"}, {"time": 1.6333, "name": "A_shuiliu_00007"}, {"time": 1.6667, "name": "A_shuiliu_00006"}, {"time": 1.7, "name": "A_shuiliu_00005"}, {"time": 1.7667, "name": "A_shuiliu_00004"}, {"time": 1.8333, "name": "A_shuiliu_00003"}, {"time": 1.8667, "name": "A_shuiliu_00002"}, {"time": 1.9333, "name": "A_shuiliu_00001"}, {"time": 2, "name": "A_shuiliu_00000"}]}, "ef/huoquan2_1": {"color": [{"time": 0.0667, "color": "ffffff00"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.7333, "color": "ffffff00"}, {"time": 0.8667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.2, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 0.0667, "name": "ef/huoquan3_0001"}, {"time": 0.1333, "name": "ef/huoquan3_0003"}, {"time": 0.2, "name": "ef/huoquan3_0005"}, {"time": 0.2667, "name": "ef/huoquan3_0007"}, {"time": 0.3333, "name": "ef/huoquan3_0009"}, {"time": 0.4, "name": "ef/huoquan3_0011"}, {"time": 0.4667, "name": "ef/huoquan3_0013"}, {"time": 0.5333, "name": "ef/huoquan3_0015"}, {"time": 0.6, "name": "ef/huoquan3_0017"}, {"time": 0.6667, "name": null}, {"time": 0.7333, "name": "ef/huoquan3_0001"}, {"time": 0.8, "name": "ef/huoquan3_0003"}, {"time": 0.8667, "name": "ef/huoquan3_0005"}, {"time": 0.9333, "name": "ef/huoquan3_0007"}, {"time": 1, "name": "ef/huoquan3_0009"}, {"time": 1.0667, "name": "ef/huoquan3_0011"}, {"time": 1.1333, "name": "ef/huoquan3_0013"}, {"time": 1.2, "name": "ef/huoquan3_0015"}, {"time": 1.2667, "name": "ef/huoquan3_0017"}, {"time": 1.3333, "name": null}]}, "qipao/huo2": {"attachment": [{"name": null}, {"time": 0.0667, "name": "qipao/huo01"}, {"time": 0.1, "name": "qipao/huo02"}, {"time": 0.1667, "name": "qipao/huo03"}, {"time": 0.2333, "name": "qipao/huo04"}, {"time": 0.2667, "name": "qipao/huo05"}, {"time": 0.3333, "name": "qipao/huo06"}, {"time": 0.4, "name": "qipao/huo07"}, {"time": 0.4333, "name": "qipao/huo08"}, {"time": 0.5, "name": "qipao/huo09"}, {"time": 0.5667, "name": "qipao/huo010"}, {"time": 0.6, "name": null}, {"time": 1.1667, "name": "qipao/huo01"}, {"time": 1.2, "name": "qipao/huo02"}, {"time": 1.2667, "name": "qipao/huo03"}, {"time": 1.3333, "name": "qipao/huo04"}, {"time": 1.3667, "name": "qipao/huo05"}, {"time": 1.4333, "name": "qipao/huo06"}, {"time": 1.5, "name": "qipao/huo07"}, {"time": 1.5333, "name": "qipao/huo08"}, {"time": 1.6, "name": "qipao/huo09"}, {"time": 1.6667, "name": "qipao/huo010"}, {"time": 1.7, "name": null}]}}, "bones": {"ef/baidian2": {"scale": [{"x": 8.063, "y": 8.063}, {"time": 0.1333, "x": 2.916, "y": 2.916}, {"time": 0.2667, "x": 8.063, "y": 8.063}, {"time": 0.4, "x": 2.916, "y": 2.916}, {"time": 0.5333, "x": 8.063, "y": 8.063}, {"time": 0.6667, "x": 2.916, "y": 2.916}, {"time": 0.8, "x": 8.063, "y": 8.063}, {"time": 0.9333, "x": 2.916, "y": 2.916}, {"time": 1.0667, "x": 8.063, "y": 8.063}, {"time": 1.2, "x": 2.916, "y": 2.916}, {"time": 1.3333, "x": 8.063, "y": 8.063}, {"time": 1.4667, "x": 2.916, "y": 2.916}, {"time": 1.6, "x": 8.063, "y": 8.063}, {"time": 1.7333, "x": 2.916, "y": 2.916}, {"time": 1.8667, "x": 8.063, "y": 8.063}, {"time": 2, "x": 2.916, "y": 2.916}]}, "ef/glow_117": {"translate": [{"x": 3.24, "y": -6.39}], "scale": [{"x": 5.512, "y": 5.512}]}, "ef/huoquan2_1": {"rotate": [{"time": 0.6667}, {"time": 0.7333, "angle": 26.6}], "translate": [{}, {"time": 0.0667, "x": 8.68, "y": 17.37}], "scale": [{}, {"time": 0.0667, "x": 2.24, "y": 2.24}]}, "ef/huoquan2_2": {"rotate": [{}, {"time": 0.4667, "angle": -107.69, "curve": "stepped"}, {"time": 1.0667, "angle": -107.69}, {"time": 1.1333, "angle": -16.98}], "translate": [{}, {"time": 0.4667, "x": 13.03, "y": -21.71, "curve": "stepped"}, {"time": 1.0667, "x": 13.03, "y": -21.71}, {"time": 1.1333, "x": 15.2, "y": 10.86}], "scale": [{}, {"time": 0.4667, "x": 2, "y": 2}]}, "effect008_qinguli_skill_projectile": {"rotate": [{"angle": -53.44}], "scale": [{"x": 1.5, "y": 1.5}]}}}, "long_yuan_jiesuo": {"slots": {"huo/huomiao_0001": {"color": [{"color": "ffffff00"}]}, "ef/huoquan2_2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "color": "ffffff00"}, {"time": 1.6, "color": "ffffffff", "curve": "stepped"}, {"time": 1.9333, "color": "ffffffff"}, {"time": 2.0667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1333, "color": "ffffff00"}, {"time": 2.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6, "color": "ffffffff"}, {"time": 2.7333, "color": "ffffff00"}], "attachment": [{"time": 1, "name": null}, {"time": 1.4667, "name": "ef/huoquan3_0001"}, {"time": 1.5333, "name": "ef/huoquan3_0003"}, {"time": 1.6, "name": "ef/huoquan3_0005"}, {"time": 1.6667, "name": "ef/huoquan3_0007"}, {"time": 1.7333, "name": "ef/huoquan3_0009"}, {"time": 1.8, "name": "ef/huoquan3_0011"}, {"time": 1.8667, "name": "ef/huoquan3_0013"}, {"time": 1.9333, "name": "ef/huoquan3_0015"}, {"time": 2, "name": "ef/huoquan3_0017"}, {"time": 2.0667, "name": null}, {"time": 2.1333, "name": "ef/huoquan3_0001"}, {"time": 2.2, "name": "ef/huoquan3_0003"}, {"time": 2.2667, "name": "ef/huoquan3_0005"}, {"time": 2.3333, "name": "ef/huoquan3_0007"}, {"time": 2.4, "name": "ef/huoquan3_0009"}, {"time": 2.4667, "name": "ef/huoquan3_0011"}, {"time": 2.5333, "name": "ef/huoquan3_0013"}, {"time": 2.6, "name": "ef/huoquan3_0015"}, {"time": 2.6667, "name": "ef/huoquan3_0017"}, {"time": 2.7333, "name": null}]}, "ef/baidian2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "color": "ffdf72ff"}]}, "A_shuiliu_00000": {"attachment": [{"name": null}, {"time": 0.9333, "name": "A_shuiliu_00019"}, {"time": 1, "name": "A_shuiliu_00018"}, {"time": 1.0333, "name": "A_shuiliu_00017"}, {"time": 1.1, "name": "A_shuiliu_00016"}, {"time": 1.1667, "name": "A_shuiliu_00014"}, {"time": 1.2333, "name": "A_shuiliu_00013"}, {"time": 1.3, "name": "A_shuiliu_00012"}, {"time": 1.3333, "name": "A_shuiliu_00011"}, {"time": 1.4, "name": "A_shuiliu_00010"}, {"time": 1.4667, "name": "A_shuiliu_00009"}, {"time": 1.5, "name": "A_shuiliu_00008"}, {"time": 1.5667, "name": "A_shuiliu_00007"}, {"time": 1.6, "name": "A_shuiliu_00006"}, {"time": 1.6333, "name": "A_shuiliu_00005"}, {"time": 1.7, "name": "A_shuiliu_00004"}, {"time": 1.7667, "name": "A_shuiliu_00003"}, {"time": 1.8, "name": "A_shuiliu_00002"}, {"time": 1.8667, "name": "A_shuiliu_00001"}, {"time": 1.9333, "name": "A_shuiliu_00000"}, {"time": 2, "name": "A_shuiliu_00019"}, {"time": 2.0667, "name": "A_shuiliu_00018"}, {"time": 2.1, "name": "A_shuiliu_00017"}, {"time": 2.1667, "name": "A_shuiliu_00016"}, {"time": 2.2333, "name": "A_shuiliu_00014"}, {"time": 2.3, "name": "A_shuiliu_00013"}, {"time": 2.3667, "name": "A_shuiliu_00012"}, {"time": 2.4, "name": "A_shuiliu_00011"}, {"time": 2.4667, "name": "A_shuiliu_00010"}, {"time": 2.5333, "name": "A_shuiliu_00009"}, {"time": 2.5667, "name": "A_shuiliu_00008"}, {"time": 2.6333, "name": "A_shuiliu_00007"}, {"time": 2.6667, "name": "A_shuiliu_00006"}, {"time": 2.7, "name": "A_shuiliu_00005"}, {"time": 2.7667, "name": "A_shuiliu_00004"}, {"time": 2.8333, "name": "A_shuiliu_00003"}, {"time": 2.8667, "name": "A_shuiliu_00002"}, {"time": 2.9333, "name": "A_shuiliu_00001"}, {"time": 3, "name": "A_shuiliu_00000"}]}, "qipao/huo01": {"attachment": [{"name": null}, {"time": 1.2333, "name": "qipao/huo01"}, {"time": 1.2667, "name": "qipao/huo02"}, {"time": 1.3333, "name": "qipao/huo03"}, {"time": 1.4, "name": "qipao/huo04"}, {"time": 1.4333, "name": "qipao/huo05"}, {"time": 1.5, "name": "qipao/huo06"}, {"time": 1.5667, "name": "qipao/huo07"}, {"time": 1.6, "name": "qipao/huo08"}, {"time": 1.6667, "name": "qipao/huo09"}, {"time": 1.7333, "name": "qipao/huo010"}, {"time": 1.7667, "name": null}]}, "ef/luoxuanqiu_0000": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.2, "color": "ffffffff"}], "attachment": [{"time": 1, "name": "ef/luoxuanqiu_0000"}, {"time": 1.0667, "name": "ef/luoxuanqiu_0001"}, {"time": 1.1333, "name": "ef/luoxuanqiu_0002"}, {"time": 1.2, "name": "ef/luoxuanqiu_0003"}, {"time": 1.2667, "name": "ef/luoxuanqiu_0004"}, {"time": 1.3333, "name": "ef/luoxuanqiu_0005"}, {"time": 1.4, "name": "ef/luoxuanqiu_0000"}, {"time": 1.4667, "name": "ef/luoxuanqiu_0001"}, {"time": 1.5333, "name": "ef/luoxuanqiu_0002"}, {"time": 1.6, "name": "ef/luoxuanqiu_0003"}, {"time": 1.6667, "name": "ef/luoxuanqiu_0004"}, {"time": 1.7333, "name": "ef/luoxuanqiu_0005"}, {"time": 1.8, "name": "ef/luoxuanqiu_0000"}, {"time": 1.8667, "name": "ef/luoxuanqiu_0001"}, {"time": 1.9333, "name": "ef/luoxuanqiu_0002"}, {"time": 2, "name": "ef/luoxuanqiu_0003"}, {"time": 2.0667, "name": "ef/luoxuanqiu_0004"}, {"time": 2.1333, "name": "ef/luoxuanqiu_0005"}, {"time": 2.2, "name": "ef/luoxuanqiu_0000"}, {"time": 2.2667, "name": "ef/luoxuanqiu_0001"}, {"time": 2.3333, "name": "ef/luoxuanqiu_0002"}, {"time": 2.4, "name": "ef/luoxuanqiu_0003"}, {"time": 2.4667, "name": "ef/luoxuanqiu_0004"}, {"time": 2.5333, "name": "ef/luoxuanqiu_0005"}, {"time": 2.6, "name": "ef/luoxuanqiu_0000"}, {"time": 2.6667, "name": "ef/luoxuanqiu_0001"}, {"time": 2.7333, "name": "ef/luoxuanqiu_0002"}, {"time": 2.8, "name": "ef/luoxuanqiu_0003"}, {"time": 2.8667, "name": "ef/luoxuanqiu_0004"}, {"time": 2.9333, "name": "ef/luoxuanqiu_0005"}, {"time": 3, "name": "ef/luoxuanqiu_0000"}]}, "qipao/huo1": {"attachment": [{"name": null}, {"time": 1.5333, "name": "qipao/huo01"}, {"time": 1.5667, "name": "qipao/huo02"}, {"time": 1.6333, "name": "qipao/huo03"}, {"time": 1.7, "name": "qipao/huo04"}, {"time": 1.7333, "name": "qipao/huo05"}, {"time": 1.8, "name": "qipao/huo06"}, {"time": 1.8667, "name": "qipao/huo07"}, {"time": 1.9, "name": "qipao/huo08"}, {"time": 1.9667, "name": "qipao/huo09"}, {"time": 2.0333, "name": "qipao/huo010"}, {"time": 2.0667, "name": null}]}, "ef/glow_117": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1, "color": "d32800ff"}]}, "A_shuiliu_0": {"attachment": [{"name": null}, {"time": 0.9333, "name": "A_shuiliu_00019"}, {"time": 1, "name": "A_shuiliu_00018"}, {"time": 1.0333, "name": "A_shuiliu_00017"}, {"time": 1.1, "name": "A_shuiliu_00016"}, {"time": 1.1667, "name": "A_shuiliu_00014"}, {"time": 1.2333, "name": "A_shuiliu_00013"}, {"time": 1.3, "name": "A_shuiliu_00012"}, {"time": 1.3333, "name": "A_shuiliu_00011"}, {"time": 1.4, "name": "A_shuiliu_00010"}, {"time": 1.4667, "name": "A_shuiliu_00009"}, {"time": 1.5, "name": "A_shuiliu_00008"}, {"time": 1.5667, "name": "A_shuiliu_00007"}, {"time": 1.6, "name": "A_shuiliu_00006"}, {"time": 1.6333, "name": "A_shuiliu_00005"}, {"time": 1.7, "name": "A_shuiliu_00004"}, {"time": 1.7667, "name": "A_shuiliu_00003"}, {"time": 1.8, "name": "A_shuiliu_00002"}, {"time": 1.8667, "name": "A_shuiliu_00001"}, {"time": 1.9333, "name": "A_shuiliu_00000"}, {"time": 2, "name": "A_shuiliu_00019"}, {"time": 2.0667, "name": "A_shuiliu_00018"}, {"time": 2.1, "name": "A_shuiliu_00017"}, {"time": 2.1667, "name": "A_shuiliu_00016"}, {"time": 2.2333, "name": "A_shuiliu_00014"}, {"time": 2.3, "name": "A_shuiliu_00013"}, {"time": 2.3667, "name": "A_shuiliu_00012"}, {"time": 2.4, "name": "A_shuiliu_00011"}, {"time": 2.4667, "name": "A_shuiliu_00010"}, {"time": 2.5333, "name": "A_shuiliu_00009"}, {"time": 2.5667, "name": "A_shuiliu_00008"}, {"time": 2.6333, "name": "A_shuiliu_00007"}, {"time": 2.6667, "name": "A_shuiliu_00006"}, {"time": 2.7, "name": "A_shuiliu_00005"}, {"time": 2.7667, "name": "A_shuiliu_00004"}, {"time": 2.8333, "name": "A_shuiliu_00003"}, {"time": 2.8667, "name": "A_shuiliu_00002"}, {"time": 2.9333, "name": "A_shuiliu_00001"}, {"time": 3, "name": "A_shuiliu_00000"}]}, "ef/huoquan2_1": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.0667, "color": "ffffff00"}, {"time": 1.2, "color": "ffffffff", "curve": "stepped"}, {"time": 1.5333, "color": "ffffffff"}, {"time": 1.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.7333, "color": "ffffff00"}, {"time": 1.8667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.2, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff00"}], "attachment": [{"time": 1, "name": null}, {"time": 1.0667, "name": "ef/huoquan3_0001"}, {"time": 1.1333, "name": "ef/huoquan3_0003"}, {"time": 1.2, "name": "ef/huoquan3_0005"}, {"time": 1.2667, "name": "ef/huoquan3_0007"}, {"time": 1.3333, "name": "ef/huoquan3_0009"}, {"time": 1.4, "name": "ef/huoquan3_0011"}, {"time": 1.4667, "name": "ef/huoquan3_0013"}, {"time": 1.5333, "name": "ef/huoquan3_0015"}, {"time": 1.6, "name": "ef/huoquan3_0017"}, {"time": 1.6667, "name": null}, {"time": 1.7333, "name": "ef/huoquan3_0001"}, {"time": 1.8, "name": "ef/huoquan3_0003"}, {"time": 1.8667, "name": "ef/huoquan3_0005"}, {"time": 1.9333, "name": "ef/huoquan3_0007"}, {"time": 2, "name": "ef/huoquan3_0009"}, {"time": 2.0667, "name": "ef/huoquan3_0011"}, {"time": 2.1333, "name": "ef/huoquan3_0013"}, {"time": 2.2, "name": "ef/huoquan3_0015"}, {"time": 2.2667, "name": "ef/huoquan3_0017"}, {"time": 2.3333, "name": null}]}, "qipao/huo2": {"attachment": [{"name": null}, {"time": 0.9667, "name": "qipao/huo01"}, {"time": 1, "name": "qipao/huo02"}, {"time": 1.0667, "name": "qipao/huo03"}, {"time": 1.1333, "name": "qipao/huo04"}, {"time": 1.1667, "name": "qipao/huo05"}, {"time": 1.2333, "name": "qipao/huo06"}, {"time": 1.3, "name": "qipao/huo07"}, {"time": 1.3333, "name": "qipao/huo08"}, {"time": 1.4, "name": "qipao/huo09"}, {"time": 1.4667, "name": "qipao/huo010"}, {"time": 1.5, "name": null}]}, "huo/huomiao_1": {"color": [{"color": "ffffff00"}]}}, "bones": {"bone": {"rotate": [{"time": 3, "angle": -95}], "translate": [{"time": 3, "x": -200.94, "y": -105.89}], "scale": [{"y": 0, "curve": "stepped"}, {"time": 0.3333, "y": 0, "curve": 0.232, "c2": 0.2, "c3": 0.75}, {"time": 0.8333, "y": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}], "shear": [{"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "y": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 1.0333}]}, "1": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "y": 42, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}], "scale": [{"x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 1.24, "y": 1.14, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 0.96, "y": 0.98, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "2": {"shear": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "y": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 1.5}]}, "bone2": {"rotate": [{"time": 3, "angle": 30.66}], "translate": [{"time": 3, "x": -1.38, "y": 6.94}]}, "bone3": {"rotate": [{"time": 3, "angle": 11.94}], "translate": [{"time": 3, "x": 0.1, "y": 1.86}]}, "bone4": {"rotate": [{"time": 3, "angle": 37.31}], "translate": [{"time": 3, "x": -0.58, "y": 4.21}]}, "bone5": {"rotate": [{"time": 3, "angle": 94.37}], "translate": [{"time": 3, "x": -8.39, "y": 13.11}]}, "bone6": {"rotate": [{"time": 3, "angle": 24.51}], "translate": [{"time": 3, "x": -0.61, "y": 4.89}]}, "bone7": {"rotate": [{"time": 3, "angle": 12.08}], "translate": [{"time": 3, "x": 0.05, "y": 1.76}]}, "bone8": {"rotate": [{"time": 3, "angle": 45.32}], "translate": [{"time": 3, "x": -1.78, "y": 6.47}]}, "bone9": {"rotate": [{"time": 3, "angle": 20.43}], "translate": [{"time": 3, "x": -0.5, "y": 4.12}]}, "bone10": {"rotate": [{"time": 3, "angle": 5.4}], "translate": [{"time": 3, "x": -0.07, "y": 1.03}]}, "bone11": {"rotate": [{"time": 3, "angle": 2.14}], "translate": [{"time": 3, "x": 0.28, "y": 0.4}]}, "bone12": {"rotate": [{"time": 3, "angle": 9.21}], "translate": [{"time": 3, "x": -0.28, "y": 2.9}]}, "ef/baidian2": {"scale": [{"time": 1, "x": 8.063, "y": 8.063}, {"time": 1.1333, "x": 2.916, "y": 2.916}, {"time": 1.2667, "x": 8.063, "y": 8.063}, {"time": 1.4, "x": 2.916, "y": 2.916}, {"time": 1.5333, "x": 8.063, "y": 8.063}, {"time": 1.6667, "x": 2.916, "y": 2.916}, {"time": 1.8, "x": 8.063, "y": 8.063}, {"time": 1.9333, "x": 2.916, "y": 2.916}, {"time": 2.0667, "x": 8.063, "y": 8.063}, {"time": 2.2, "x": 2.916, "y": 2.916}, {"time": 2.3333, "x": 8.063, "y": 8.063}, {"time": 2.4667, "x": 2.916, "y": 2.916}, {"time": 2.6, "x": 8.063, "y": 8.063}, {"time": 2.7333, "x": 2.916, "y": 2.916}, {"time": 2.8667, "x": 8.063, "y": 8.063}, {"time": 3, "x": 2.916, "y": 2.916}]}, "ef/glow_117": {"translate": [{"time": 1, "x": 3.24, "y": -6.39}], "scale": [{"time": 1, "x": 5.512, "y": 5.512}]}, "ef/huoquan2_1": {"rotate": [{"time": 1.6667}, {"time": 1.7333, "angle": 26.6}], "translate": [{"time": 1}, {"time": 1.0667, "x": 8.68, "y": 17.37}], "scale": [{"time": 3, "x": 2.24, "y": 2.24}]}, "ef/huoquan2_2": {"rotate": [{"time": 1}, {"time": 1.4667, "angle": -107.69, "curve": "stepped"}, {"time": 2.0667, "angle": -107.69}, {"time": 2.1333, "angle": -16.98}], "translate": [{"time": 1}, {"time": 1.4667, "x": 13.03, "y": -21.71, "curve": "stepped"}, {"time": 2.0667, "x": 13.03, "y": -21.71}, {"time": 2.1333, "x": 15.2, "y": 10.86}], "scale": [{"time": 1}, {"time": 1.4667, "x": 2, "y": 2}]}, "effect008_qinguli_skill_projectile": {"rotate": [{"time": 1, "angle": -53.44}], "scale": [{"time": 1, "x": 1.5, "y": 1.5}]}}, "path": {"22": {"spacing": [{"spacing": -19.4, "curve": "stepped"}, {"time": 0.3667, "spacing": -19.4, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "spacing": 0.15, "curve": 0.25, "c3": 0.75}, {"time": 1.4}]}}, "deform": {"default": {"1": {"1": [{"time": 0.3667, "curve": 0.318, "c2": 0.28, "c3": 0.682, "c4": 0.72}, {"time": 0.7333, "offset": 92, "vertices": [-0.282, 0.472, -0.282, -0.828, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.91, 0, -0.91, 0, -0.91, 0, 0, 0, 0, 0, 0, 0.498, 0.472, 0.498, 0.472, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.01801, -0.11101, -0.01801, -0.11101, -0.01801, -0.11101, 0, 0, 0, 0, 0, 0, -0.01801, -0.11101, -0.01801, -0.11101, -0.01801, -0.11101, 0, 0, 0, 0, 0.77101, 0, 0.77101, 0, 0.77101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.45657, 0, 1.45657, 0, 1.45657, 0, 1.45657], "curve": 0.381, "c2": 0.59, "c3": 0.727}, {"time": 1.0667, "offset": 92, "vertices": [0.64999, 0, 0.30798, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.60503, 0, 0.60503, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.045, 0.34201, -0.045, 0.34201, -0.045, 0.34201, 0, 0, 0, 0, 0, 0, 0.38998, 0.34201, 0.38998, 0.34201, 0.38998, 0.34201, 0, 0, 0, 0, 0.38998, 0, 0.38998, 0, 0.38998, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.87, 0, 0.87, 0, 0.87, 0, 0.87], "curve": 0.25, "c3": 0.75}, {"time": 1.3, "offset": 210, "vertices": [-2.92851, 0, -1.19224, -1.29501, -1.21669, -1.29501, 0.88641, -1.29501, 1.08204, -1.29501, 2.98949], "curve": 0.25, "c3": 0.75}, {"time": 1.5, "offset": 210, "vertices": [-0.19671, 0, -0.08008, 0, -0.08172, 0, 0.05955, 0, 0.07269, 0, 0.20082]}]}, "root": {"root": [{"vertices": [14.39898, -28.05189, 14.39898, -28.05189, 14.39898, -28.05189, 14.39898, -10.81697, 14.39898, -10.81697, 14.39898, -10.81697, 14.39898, -10.81697, 14.39898, -10.81697, 14.39898, -10.81697, 14.39898, -10.81697, 14.39898, -10.81697, 14.39898, -10.81697, 14.39898, -10.81697, 14.39898, -10.81697, 14.39898, -10.81697], "curve": "stepped"}, {"time": 0.3667, "vertices": [14.39898, -28.05189, 14.39898, -28.05189, 14.39898, -28.05189, 14.39898, -10.81697, 14.39898, -10.81697, 14.39898, -10.81697, 14.39898, -10.81697, 14.39898, -10.81697, 14.39898, -10.81697, 14.39898, -10.81697, 14.39898, -10.81697, 14.39898, -10.81697, 14.39898, -10.81697, 14.39898, -10.81697, 14.39898, -10.81697], "curve": 0.25, "c3": 0.75}, {"time": 0.6, "offset": 1, "vertices": [-22.95769, 0, -22.95769, 0, -22.95769, 0, -13.76573, 0, -13.76573, 0, -13.76573, 0, -13.76573, 0, -13.76573, 0, -13.76573, 0, -13.76573, 0, -13.76573, 0, -13.76573, 0, -13.76573, 0, -13.76573, 0, -13.76573], "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1, "offset": 1, "vertices": [-1.34769, 0, -1.34769, 0, -1.34769, 0, -1.34769, 0, -1.34769, 0, -1.34769, 0, -1.34769, 0, -1.34769, 0, -1.34769, 0, -1.34769, 0, -1.34769, 0, -1.34769, 0, -1.34769, 0, -1.34769, 0, -1.34769], "curve": 0.25, "c3": 0.75}, {"time": 1.1667}]}, "2": {"2": [{"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "vertices": [-2.95557, 1.03552, -1.41245, -2.79539, -2.98093, -0.96114, -3.12888, 0.13989, -3.0834, 0.54895, -3.10172, 0.43338, -2.95557, 1.03552, -1.41245, -2.79539, -3.12888, 0.13989, -3.0834, 0.54895, -3.10172, 0.43338, 3.03702, 0.7659, -1.41245, -2.79539, -2.98093, -0.96114, -3.12888, 0.13989, -3.0834, 0.54895, -3.10172, 0.43338, 3.03702, 0.7659, -1.41245, -2.79539, -2.98093, -0.96114, -3.12888, 0.13989, -3.0834, 0.54895, -3.10172, 0.43338, 1.40192, 2.80057, 3.03702, 0.7659, -1.41245, -2.79539, -2.98093, -0.96114, -3.12888, 0.13989, -3.0834, 0.54895, -3.10172, 0.43338, 1.40192, 2.80057, 3.03702, 0.7659, -2.98093, -0.96114, -3.12888, 0.13989, -3.0834, 0.54895, -3.10172, 0.43338, 1.40192, 2.80057, 3.03702, 0.7659, -1.41245, -2.79539, -2.98093, -0.96114, -3.12888, 0.13989, -3.0834, 0.54895, -3.10172, 0.43338, 1.40192, 2.80057, 3.03702, 0.7659, -1.41245, -2.79539, -2.98093, -0.96114, -3.12888, 0.13989, -3.0834, 0.54895, -3.10172, 0.43338, 1.40192, 2.80057, 3.03702, 0.7659, -1.41245, -2.79539, -2.98093, -0.96114, -3.12888, 0.13989, -3.0834, 0.54895, -3.10172, 0.43338, 1.40192, 2.80057, 3.03702, 0.7659, -1.41245, -2.79539, -2.98093, -0.96114, -3.12888, 0.13989, -3.0834, 0.54895, -3.10172, 0.43338, 1.40192, 2.80057, 3.03702, 0.7659, -1.41245, -2.79539, -2.98093, -0.96114, -3.12888, 0.13989, -3.0834, 0.54895, -3.10172, 0.43338, 1.40192, 2.80057, 3.03702, 0.7659, -1.41245, -2.79539, -2.98093, -0.96114, -3.12888, 0.13989, -3.0834, 0.54895, -3.10172, 0.43338, 1.40192, 2.80057, 3.03702, 0.7659, -1.41245, -2.79539, -2.98093, -0.96114, -3.12888, 0.13989, -3.0834, 0.54895, -3.10172, 0.43338, 2.75659, 0.57518, -1.32487, -2.48485, -2.71205, -0.75753, -2.81198, 0.15157, -2.78731, 0.40099, 1.50359, 0.31374, -0.72266, -1.35536, -1.47929, -0.41321, -1.5338, 0.08267, -1.52034, 0.21872, 1.50359, 0.31374, -0.72266, -1.35536, -1.47929, -0.41321, -1.5338, 0.08267, -1.52034, 0.21872, 0.75169, 0.15688, -0.36125, -0.67766, -0.7395, -0.20656, -0.76695, 0.04134, -0.76015, 0.10928, 0.50119, 0.10458, -0.24088, -0.45178, -0.49307, -0.13773, -0.51126, 0.02756, -0.50677, 0.07291, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.66063, 0.1379, -0.3174, -0.59562, -0.64978, -0.18153, -0.67419, 0.03632, -0.66795, 0.09598, 0.66063, 0.1379, -0.3174, -0.59562, -0.64978, -0.18153, -0.67419, 0.03632, -0.66795, 0.09598, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.5123, -0.17078, 0.25412, 0.4765, 0.52019, 0.14529, 0.52786, -0.114, 0.53452, -0.07693, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.15319, 2.15462, -2.04915, 0.68295, -1.0161, -1.90596, -2.08009, -0.58104, -2.11116, 0.45633, -2.13777, 0.30746, -2.04915, 0.68295, -1.0161, -1.90596, -2.08009, -0.58104, -2.11116, 0.45633, -2.13777, 0.30746, -2.04915, 0.68295, -1.0161, -1.90596, -2.08009, -0.58104, -2.11116, 0.45633, -2.13777, 0.30746, -2.04915, 0.68295, -1.0161, -1.90596, -2.08009, -0.58104, -2.11116, 0.45633, -2.13777, 0.30746, -2.04915, 0.68295, -1.0161, -1.90596, -2.08009, -0.58104, -2.11116, 0.45633, -2.13777, 0.30746, -2.25731, 0.7908, 1.07077, 2.13889, -1.0787, -2.13491, -2.27657, -0.73404, -2.35492, 0.41914, -2.36899, 0.33107, -2.95557, 1.03552, -1.41245, -2.79539, -2.98093, -0.96114, -3.0834, 0.54895, -3.10172, 0.43338, -0.22211, 3.12411, -2.95557, 1.03552, -1.41245, -2.79539, -2.98093, -0.96114, -3.0834, 0.54895, -3.10172, 0.43338, -2.95557, 1.03552, -1.41245, -2.79539, -2.98093, -0.96114, -3.0834, 0.54895, -3.10172, 0.43338, -2.95557, 1.03552, -1.41245, -2.79539, -2.98093, -0.96114, -3.0834, 0.54895, -3.10172, 0.43338, -2.95557, 1.03552, -1.41245, -2.79539, -2.98093, -0.96114, -3.0834, 0.54895, -3.10172, 0.43338, -0.22211, 3.12411, -2.95557, 1.03552, 1.40192, 2.80057, -1.41245, -2.79539, -2.98093, -0.96114, -3.0834, 0.54895, -3.10172, 0.43338, -0.22211, 3.12411, -2.95557, 1.03552, 1.40192, 2.80057, -1.41245, -2.79539, -2.98093, -0.96114, -3.0834, 0.54895, -3.10172, 0.43338, -0.22211, 3.12411, -2.95557, 1.03552, 1.40192, 2.80057, -2.98093, -0.96114, -3.0834, 0.54895, -0.22211, 3.12411, -2.95557, 1.03552, 1.40192, 2.80057, -2.98093, -0.96114, -3.0834, 0.54895, -0.22211, 3.12411, -2.95557, 1.03552, 1.40192, 2.80057, -3.0834, 0.54895, -0.22211, 3.12411, -2.95557, 1.03552, 1.40192, 2.80057, -2.98093, -0.96114, -3.0834, 0.54895, -0.22211, 3.12411, -2.95557, 1.03552, 1.40192, 2.80057, -3.0834, 0.54895, -0.22211, 3.12411, -2.95557, 1.03552, 1.40192, 2.80057, -3.0834, 0.54895, -2.95557, 1.03552, 1.40192, 2.80057, 3.03702, 0.7659, -3.0834, 0.54895, -0.22211, 3.12411, -2.95557, 1.03552, 1.40192, 2.80057, 3.03702, 0.7659, -3.0834, 0.54895, -0.22211, 3.12411, -2.95557, 1.03552, 1.40192, 2.80057, 3.03702, 0.7659, -3.0834, 0.54895, -2.95557, 1.03552, 1.40192, 2.80057, -3.0834, 0.54895, -0.22211, 3.12411, -2.95557, 1.03552, 1.40192, 2.80057, -2.95557, 1.03552, 1.40192, 2.80057, 3.03702, 0.7659, -3.10172, 0.43338, -2.95557, 1.03552, -3.10172, 0.43338, -2.95557, 1.03552, -3.10172, 0.43338, -2.95557, 1.03552, -2.95557, 1.03552, -1.41245, -2.79539, -3.12888, 0.13989, -3.0834, 0.54895, -3.10172, 0.43338, -2.95557, 1.03552, -1.41245, -2.79539, -2.98093, -0.96114, -3.12888, 0.13989, -3.0834, 0.54895, -3.10172, 0.43338, -2.95557, 1.03552, -1.41245, -2.79539, -3.12888, 0.13989, -3.0834, 0.54895, -3.10172, 0.43338, -1.41245, -2.79539, -2.98093, -0.96114, -3.12888, 0.13989, -3.0834, 0.54895, -3.10172, 0.43338, -2.95557, 1.03552, -1.41245, -2.79539, -2.98093, -0.96114, -3.12888, 0.13989, -3.0834, 0.54895, -3.10172, 0.43338, -2.95557, 1.03552, -1.41245, -2.79539, -2.98093, -0.96114, -3.0834, 0.54895, -3.10172, 0.43338, 3.03702, 0.7659, -2.98093, -0.96114, -3.12888, 0.13989, -3.0834, 0.54895, -3.10172, 0.43338, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.08438, -0.57246, 0.29601, -2.14229, -1.3161, -1.7166, -1.96672, -0.89893, -1.93207, -0.97252, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.03702, 0.7659, -1.41245, -2.79539, -2.98093, -0.96114, -3.12888, 0.13989, -3.0834, 0.54895, -3.10172, 0.43338, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.22211, 3.12411, -2.95557, 1.03552, 1.40192, 2.80057, 3.03702, 0.7659, -1.41245, -2.79539, -2.98093, -0.96114, -3.12888, 0.13989, -3.0834, 0.54895, -3.10172, 0.43338, 1.37623, 4.34743, -3.44402, 2.9882, 3.40016, 3.03766, 4.52171, -0.59327, -3.4119, -3.02522, -4.55037, 0.29908, -4.15958, 1.86867, -3.87854, 2.3981, -3.96527, 2.25137, 1.37623, 4.34743, -3.44402, 2.9882, 3.40016, 3.03766, 4.52171, -0.59327, -3.4119, -3.02522, -4.55037, 0.29908, -4.15958, 1.86867, -3.87854, 2.3981, -3.96527, 2.25137, -0.22211, 3.12411, -2.95557, 1.03552, 1.40192, 2.80057, 3.03702, 0.7659, -1.41245, -2.79539, -2.98093, -0.96114, -3.12888, 0.13989, -3.0834, 0.54895, -3.10172, 0.43338, -0.22211, 3.12411, -2.95557, 1.03552, 1.40192, 2.80057, 3.03702, 0.7659, -1.41245, -2.79539, -2.98093, -0.96114, -3.12888, 0.13989, -3.0834, 0.54895, -3.10172, 0.43338, -1.35293, 1.687, -2.08438, -0.57246, -0.30446, 2.14139, 1.42451, 1.6279, 0.29601, -2.14229, -1.3161, -1.7166, -1.83215, -1.14931, -1.96672, -0.89893, -1.93207, -0.97252, -0.22211, 3.12411, -2.95557, 1.03552, 1.40192, 2.80057, 3.03702, 0.7659, -1.41245, -2.79539, -2.98093, -0.96114, -3.12888, 0.13989, -3.0834, 0.54895, -3.10172, 0.43338, -1.11636, 1.8273, -2.11932, -0.29964, -0.02928, 2.14118, 1.60393, 1.41933, 0.02083, -2.14122, -1.50854, -1.52022, -1.94412, -0.89806, -2.04472, -0.63535, -2.01984, -0.7117, -1.11636, 1.8273, -2.11932, -0.29964, -0.02928, 2.14118, 1.60393, 1.41933, 0.02083, -2.14122, -1.50854, -1.52022, -1.94412, -0.89806, -2.04472, -0.63535, -2.01984, -0.7117, -0.22211, 3.12411, -2.95557, 1.03552, 1.40192, 2.80057, 3.03702, 0.7659, -1.41245, -2.79539, -2.98093, -0.96114, -3.12888, 0.13989, -3.0834, 0.54895, -3.10172, 0.43338, -1.2086, 5.43788, -5.47046, 1.04797, 1.7328, 5.29387, 5.1425, 2.14253, -1.75285, -5.28754, -4.99263, -2.47131, -5.54099, -0.57306, -5.56818, 0.15851, -5.57022, -0.0497, -1.2086, 5.43788, -5.47046, 1.04797, 1.7328, 5.29387, 5.1425, 2.14253, -1.75285, -5.28754, -4.99263, -2.47131, -5.54099, -0.57306, -5.56818, 0.15851, -5.57022, -0.0497, -0.22211, 3.12411, -2.95557, 1.03552, 1.40192, 2.80057, 3.03702, 0.7659, -1.41245, -2.79539, -2.98093, -0.96114, -3.12888, 0.13989, -3.0834, 0.54895, -3.10172, 0.43338, 1.09222, 2.55991, -1.91591, 2.0183, 2.24442, 1.6449, 2.71185, -0.62688, -2.2509, -1.63663, -2.7467, 0.44984, -2.41681, 1.38033, -2.21468, 1.68535, -2.27612, 1.60126, 1.09222, 2.55991, -1.91591, 2.0183, 2.24442, 1.6449, 2.71185, -0.62688, -2.2509, -1.63663, -2.7467, 0.44984, -2.41681, 1.38033, -2.21468, 1.68535, -2.27612, 1.60126, -0.1756, 2.46983, -2.3362, 0.81873, 1.10799, 2.21393, 2.40102, 0.6055, -1.11656, -2.20988, -2.35664, -0.75985, -2.47359, 0.1106, -2.43745, 0.43398, -2.45207, 0.34256, 1.21202, 2.60951, -1.91348, 2.14818, 2.37224, 1.62634, 2.78181, -0.73608, -2.3791, -1.6177, -2.82341, 0.55428, -2.45215, 1.50502, -2.23332, 1.81364, -2.29965, 1.72873, 1.21202, 2.60951, -1.91348, 2.14818, 2.37224, 1.62634, 2.78181, -0.73608, -2.3791, -1.6177, -2.82341, 0.55428, -2.45215, 1.50502, -2.23332, 1.81364, -2.29965, 1.72873], "curve": 0.25, "c3": 0.75}, {"time": 1.4}]}}}}}}