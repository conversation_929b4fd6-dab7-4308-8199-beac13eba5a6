import { _decorator, Color, isValid, sp, Tween, tween, v3 } from "cc";
import { FSMState } from "../fight/FSM/FSMState";
import { FSMBoard, STATE } from "../fight/section/StateSection";
import { AnimationSection } from "../fight/section/AnimationSection";
import { CallSkillDetail } from "../fight/FightDefine";
import FightManager, { scaleList } from "../fight/manager/FightManager";
import SkillManager from "../fight/manager/SkillManager";
import TickerMgr from "../../lib/ticker/TickerMgr";
import { PlayerModule } from "../../module/player/PlayerModule";
import RenderSection from "../../lib/object/RenderSection";
interface TimeHandle {
  time: number;
  handle: Function;
  isHandleOver: boolean;
  skillId: number;
}
const { ccclass, property } = _decorator;

const tweenTag = 990;

@ccclass
export default class Hurt extends FSMState {
  private timeHandle: Array<TimeHandle> = [];
  public async onEnter(board: FSMBoard) {
    let time = board.sub.getSection(AnimationSection).playAction(7, false);
    if (time < 0.1) {
      time = 0.1;
    }

    tween(board.sub.getSection(RenderSection).getRender())
      .to(0.05 / scaleList[FightManager.instance.speed], { position: v3(-50, 0, 0) })
      .to(0.05 / scaleList[FightManager.instance.speed], { position: v3(0, 0, 0) })
      .start();
    board.sub.getSection(RenderSection).getRender().getComponent(sp.Skeleton).color = new Color(255, 0, 0, 255);
    if (FightManager.instance.fightOver == true) {
      return;
    }
    tween(board.sub)
      .delay(time / scaleList[FightManager.instance.speed])
      .call(() => {
        if (FightManager.instance.fightOver) {
          return;
        }
        board.sub.getSection(RenderSection).getRender().getComponent(sp.Skeleton).color = new Color(255, 255, 255, 255);
      })
      .start();

    await new Promise((res) => TickerMgr.setTimeout(time / scaleList[FightManager.instance.speed], res));
    if (FightManager.instance.fightOver) {
      return;
    }
  }

  private playSound(board: FSMBoard) {}

  public update(board: FSMBoard, dt) {
    this.updateHandle(dt);
  }

  public onExit(board: any): void {
    super.onEnter(board);
  }

  public updateHandle(dt) {}

  private getSkillObj(roleId: number) {}
}
