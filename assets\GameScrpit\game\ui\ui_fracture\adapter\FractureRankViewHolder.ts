import { _decorator, Component, instantiate, Label, math, Node, RichText, Sprite } from "cc";
import { ListAdapter, ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { SimplePointMessage } from "../../../net/protocol/Activity";
import FmUtils from "db://assets/GameScrpit/lib/utils/FmUtils";
import { PlayerModule } from "db://assets/GameScrpit/module/player/PlayerModule";
const { ccclass, property } = _decorator;

@ccclass("FractureRankViewHolder")
export class FractureRankViewHolder extends ViewHolder {
  @property(Node)
  lblRank: Node;
  @property(Node)
  btnHeader: Node;
  @property(Node)
  lblName: Node;
  @property(Node)
  lblLevel: Node;
  @property(Node)
  lblScore: Node;
  _data: SimplePointMessage;
  start() {}
  update(deltaTime: number) {}
  updateData(data: SimplePointMessage, index: number) {
    this._data = data;
    if (index % 2 == 0) {
      this.node.getComponent(Sprite).color = math.color("#d9f1ff");
    } else {
      this.node.getComponent(Sprite).color = math.color("#bfe0f6");
    }
    this.lblRank.getComponent(Label).string = `${index + 4}`;
    this.lblName.getComponent(Label).string = `${data.detailMessage.simpleMessage.nickname}`;
    // this.lblLevel.getComponent(Label).string = `${data.detailMessage.simpleMessage.level}`;
    this.lblScore.getComponent(Label).string = `${data.point}`;
    FmUtils.setHeaderNode(this.btnHeader, data.detailMessage.simpleMessage);
    let configLeader = PlayerModule.data.getConfigLeaderData(data.detailMessage.simpleMessage.level);
    this.lblLevel.getComponent(RichText).string = `${configLeader.jingjie2}`;
  }
}

export class FractureRankAdapter extends ListAdapter {
  private _data: any[] = [];
  private _item: Node = null;
  constructor(item: Node) {
    super();
    this._item = item;
  }
  setData(data: any[]) {
    this._data = data;
    this.notifyDataSetChanged();
  }
  onCreateView(viewType: number): Node {
    let item = instantiate(this._item);
    item.active = true;
    return item;
  }
  onBindData(node: Node, position: number): void {
    node.getComponent(FractureRankViewHolder).updateData(this._data[position], position);
  }
  getCount(): number {
    return this._data.length;
  }
}
