import { BundleEnum } from "../../platform/src/ResHelper";
import { createRouteItem } from "../../platform/src/RouteHelper";

// 登录模块路由配置

export const RouteSceneLogin = {
  PageStart: createRouteItem(BundleEnum.BUNDLE_INIT_LOGIN, "prefabs/PageStart"),
  PageLogin: createRouteItem(BundleEnum.BUNDLE_INIT_LOGIN, "prefabs/PageLogin"),
  PageRegister: createRouteItem(BundleEnum.BUNDLE_INIT_LOGIN, "prefabs/PageRegister"),
  PageServer: createRouteItem(BundleEnum.BUNDLE_INIT_LOGIN, "prefabs/PageServer"),
  PageNotice: createRouteItem(BundleEnum.BUNDLE_INIT_LOGIN, "prefabs/PageNotice"),
  UIGameHealthAgeTips: createRouteItem(BundleEnum.BUNDLE_INIT_LOGIN, "prefabs/UIGameHealthAgeTips"),
  UIGameHealthAuth: createRouteItem(BundleEnum.BUNDLE_INIT_LOGIN, "prefabs/UIGameHealthAuth"),
  UIGameHealthAuthFailTips: createRouteItem(BundleEnum.BUNDLE_INIT_LOGIN, "prefabs/UIGameHealthAuthFailTips"),
  UIGameHealthAuthOKTips: createRouteItem(BundleEnum.BUNDLE_INIT_LOGIN, "prefabs/UIGameHealthAuthOKTips"),
  UIGameHealthAuthResult: createRouteItem(BundleEnum.BUNDLE_INIT_LOGIN, "prefabs/UIGameHealthAuthResult"),
};
