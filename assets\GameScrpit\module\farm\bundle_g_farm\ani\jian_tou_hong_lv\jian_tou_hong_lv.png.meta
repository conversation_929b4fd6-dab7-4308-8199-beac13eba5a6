{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "5283f494-f0de-4f47-a38f-605480afb149", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "5283f494-f0de-4f47-a38f-605480afb149@6c48a", "displayName": "jian_tou_hong_lv", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "5283f494-f0de-4f47-a38f-605480afb149", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "5283f494-f0de-4f47-a38f-605480afb149@f9941", "displayName": "jian_tou_hong_lv", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 2, "trimY": 2, "width": 20, "height": 35, "rawWidth": 24, "rawHeight": 39, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-10, -17.5, 0, 10, -17.5, 0, -10, 17.5, 0, 10, 17.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [2, 37, 22, 37, 2, 2, 22, 2], "nuv": [0.08333333333333333, 0.05128205128205128, 0.9166666666666666, 0.05128205128205128, 0.08333333333333333, 0.9487179487179487, 0.9166666666666666, 0.9487179487179487], "minPos": [-10, -17.5, 0], "maxPos": [10, 17.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "5283f494-f0de-4f47-a38f-605480afb149@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "5283f494-f0de-4f47-a38f-605480afb149@6c48a"}}