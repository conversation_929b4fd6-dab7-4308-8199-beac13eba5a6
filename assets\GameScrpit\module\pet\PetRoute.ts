import { UIHeroPetAwake } from "../../game/ui/pet/UIHeroPetAwake";
import { UIHeroPetAwakeSuccess } from "../../game/ui/pet/UIHeroPetAwakeSuccess";
import { UIPetList } from "../../game/ui/pet/UIPetList";
import { UIPetXilianTips } from "../../game/ui/pet/UIPetXilianTips";
import { Recording, RecordingMap } from "../../lib/ui/recordingMap";

export enum PetRouteItem {
  UIHeroPetAwake = "UIHeroPetAwake",
  UIHeroPetAwakeSuccess = "UIHeroPetAwakeSuccess",
  UIPetXilianTips = "UIPetXilianTips",
  UIPetList = "UIPetList",
}
export class PetRoute {
  rotueTables: Recording[] = [
    {
      node: UIHeroPetAwake,
      uiName: PetRouteItem.UIHeroPetAwake,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIHeroPetAwakeSuccess,
      uiName: PetRouteItem.UIHeroPetAwakeSuccess,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIPetXilianTips,
      uiName: PetRouteItem.UIPetXilianTips,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIPetList,
      uiName: PetRouteItem.UIPetList,
      keep: false,
      relevanceUIList: [],
    },
  ];
  public async init() {
    this.rotueTables.forEach((item) => {
      RecordingMap.instance.addRecording(item.uiName, item);
    });
  }
}
