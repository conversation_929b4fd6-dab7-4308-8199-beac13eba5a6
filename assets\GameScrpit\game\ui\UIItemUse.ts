import { _decorator, Label, Slider, Sprite } from "cc";
import MsgMgr from "../../lib/event/MsgMgr";
import TipMgr from "../../lib/tips/TipMgr";
import { UIMgr } from "../../lib/ui/UIMgr";
import { UINode } from "../../lib/ui/UINode";
import { PlayerModule } from "../../module/player/PlayerModule";
import { BundleEnum } from "../bundleEnum/BundleEnum";
import MsgEnum from "../event/MsgEnum";
import { JsonMgr } from "../mgr/JsonMgr";
import { ItemUseMessage } from "../net/protocol/Item";
import { TravelModule } from "../../module/travel/TravelModule";
import { PlayerRouteName } from "../../module/player/PlayerConstant";
import { FriendModule } from "../../module/friend/FriendModule";
import { FriendVitalityMessage } from "../net/protocol/Friend";
import { IConfigItem } from "../JsonDefine";
import { HeroModule } from "../../module/hero/HeroModule";
import { HeroSort, HeroType } from "../../module/hero/HeroConfig";
import { LangMgr } from "../mgr/LangMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { KnappsackAudioName } from "../../module/player/PlayerConfig";
import { FmConfig } from "../GameDefine";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "../../lib/utils/FmUtils";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

// 使用方式
export const enum UseTypeEnum {
  UITravel,
  UIFriend,
  Custom, // 外部自己调用返回使用

  NONE,
}

@ccclass("UIItemUse")
export class UIItemUse extends UINode {
  protected _openAct: boolean = true; //打开动作
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_COMMON_MAIN}?prefab/ui/UIItemUse`;
  }

  private _itemId: number = null;

  private _type: UseTypeEnum = null;

  private _sliderNum: number = 1;

  private _max: number = 0;

  public init(args: any): void {
    super.init(args);
    this._itemId = args.itemId;
    this._type = args.type ?? UseTypeEnum.NONE;
  }

  protected onRegEvent(): void {
    MsgMgr.on(MsgEnum.ON_EXIST_ITEM_CHANGE, this.setItemNum, this);
    this["Slider"].on("slide", this.onSliderValueChanged, this);
  }

  protected onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_EXIST_ITEM_CHANGE, this.setItemNum, this);
    this["Slider"].off("slide", this.onSliderValueChanged, this);
  }

  private onSliderValueChanged(event) {
    // 这里处理滑动条值改变的逻辑
    let progress = event.progress;
    this._sliderNum = Math.round(this._max * progress);
    this.setItemNum();
  }

  protected onEvtShow(): void {
    let num = 10000;
    if (FmConfig.isDebug == true) {
      num = 10000000;
    }
    this._max = PlayerModule.data.getItemNum(this._itemId) > num ? num : PlayerModule.data.getItemNum(this._itemId);

    let info = JsonMgr.instance.getConfigItem(this._itemId);
    FmUtils.setItemNode(this.getNode("Item"), this._itemId);
    this.getNode("itemName").getComponent(Label).string = info.name;
    this.getNode("itemdes").getComponent(Label).string = info.des;

    let itemNum = PlayerModule.data.getItemNum(this._itemId);

    let str = LangMgr.txMsgCode(217, [itemNum]);
    this.getNode("itemNum").getComponent(Label).string = str;
    // 普通文本

    this.setItemNum();
  }

  private setItemNum() {
    if (PlayerModule.data.getItemNum(this._itemId) <= 0) {
      this._sliderNum = 0;
    }

    this.getNode("slidernNum").getComponent(Label).string = LangMgr.txMsgCode(216, [this._sliderNum]);

    let proogress = this._sliderNum / this._max;
    this["Slider"].getComponent(Slider).progress = proogress;
    this["fillSpr"].getComponent(Sprite).fillRange = proogress;
  }

  private on_click_btn_minus() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    if (PlayerModule.data.getItemNum(this._itemId) <= 0 || this._sliderNum <= 0) {
      return;
    }
    this._sliderNum--;
    this.setItemNum();
  }

  private on_click_btn_add() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    if (PlayerModule.data.getItemNum(this._itemId) <= 0 || this._sliderNum >= this._max) {
      return;
    }
    this._sliderNum++;
    this.setItemNum();
  }

  private on_click_btn_use() {
    AudioMgr.instance.playEffect(KnappsackAudioName.Effect.点击使用按钮);
    if (this._sliderNum <= 0) {
      TipMgr.showTip("使用数量为0");
      return;
    }

    let info: ItemUseMessage = {
      itemId: this._itemId,
      num: this._sliderNum,
      heroId: 0,
      cityId: 0,
      choiceItemMap: {},
    };

    if (this._type == UseTypeEnum.UITravel) {
      TravelModule.api.addTravelVitality(info, (res) => {
        //log.log("addTravelVitality恢复了游历精力===", res);
        UIMgr.instance.back();
      });
    } else if (this._type == UseTypeEnum.UIFriend) {
      FriendModule.api.addvitality(this._sliderNum, (data: FriendVitalityMessage) => {
        UIMgr.instance.back();
      });
    } else if (this._type == UseTypeEnum.Custom) {
      UIMgr.instance.back({ resp: { ok: true, num: this._sliderNum } });
    } else {
      /**背包的使用道具 */
      let callback = (res) => {
        if (res.values.length > 0) {
          MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: res.values });
        }
        UIMgr.instance.back({ resp: { ok: true, num: this._sliderNum } });
      };
      let bool = this.useBagItem(this._itemId);

      if (bool == false) {
        return;
      }

      let info: ItemUseMessage = {
        itemId: this._itemId,
        num: this._sliderNum,
        heroId: 0,
        cityId: 0,
        choiceItemMap: {},
      };

      PlayerModule.api.useItem(info, callback);
    }
  }

  private useBagItem(itemId: number) {
    let db = JsonMgr.instance.getConfigItem(itemId);
    if (db.type1 == 1) {
      return this.addAttribute(db);
    }

    return true;
  }

  private addAttribute(db: IConfigItem) {
    let bool = true;
    switch (db.type1Son1List[0]) {
      case 501:
        if (HeroModule.data.getOwnedHeros(HeroSort.DEFAULT, HeroType.PERSON).length <= 0) {
          TipMgr.showTip("没有该种族战将");
          bool = false;
        }
        break;

      case 502:
        if (HeroModule.data.getOwnedHeros(HeroSort.DEFAULT, HeroType.GOD).length <= 0) {
          TipMgr.showTip("没有该种族战将");
          bool = false;
        }
        break;

      case 503:
        if (HeroModule.data.getOwnedHeros(HeroSort.DEFAULT, HeroType.YAO).length <= 0) {
          TipMgr.showTip("没有该种族战将");
          bool = false;
        }
        break;

      case 504:
        if (HeroModule.data.getOwnedHeros(HeroSort.DEFAULT, HeroType.MING).length <= 0) {
          TipMgr.showTip("没有该种族战将");
          bool = false;
        }
        break;

      case 505:
        if (HeroModule.data.getOwnedHeros(HeroSort.DEFAULT, HeroType.WU).length <= 0) {
          TipMgr.showTip("没有该种族战将");
          bool = false;
        }
        break;
    }

    return bool;
  }

  private on_click_btn_close_lan() {
    // 关闭窗口 effect_guanbiyinxiao
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  private on_click_btn_dj_huoqu() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, { itemId: this._itemId });
  }
}
