import { _decorator, EventTouch, isValid, Node } from "cc";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
import { routeConfig, RouteShowArgs } from "../../platform/src/core/managers/RouteTableManager";
import { BundleEnum } from "../game/bundleEnum/BundleEnum";
const { ccclass, property } = _decorator;
@ccclass("TopFingerControl")
@routeConfig({
  bundle: BundleEnum.BUNDLE_EXT_GUIDE,
  url: "prefab/top/TopFingerControl",
  nextHop: [],
  exit: "",
  transparent: true,
})
export class TopFingerControl extends BaseCtrl {
  private clickNode: Node;
  init(args: RouteShowArgs): void {
    this.clickNode = args.payload.clickNode;
    //
  }
  protected start(): void {
    super.start();
    this.node.on(Node.EventType.TOUCH_START, this.onClickBlank, this);
    this.node.on(Node.EventType.TOUCH_END, this.onClickBlank, this);
    this.node.on(Node.EventType.TOUCH_CANCEL, this.onClickBlank, this);
    this.node.on(Node.EventType.TOUCH_MOVE, this.onClickBlank, this);

    if (isValid(this.clickNode)) {
      let worldPosition = this.clickNode.getWorldPosition();
      this.getNode("finger").setWorldPosition(worldPosition);
    }
  }
  private onClickBlank(event: EventTouch) {
    event.preventSwallow = true;
    if (event.type != Node.EventType.TOUCH_END) {
      return;
    }
    this.closeBack();
  }
  protected onDestroy(): void {
    super.onDestroy();
    this.node.off(Node.EventType.TOUCH_END, this.onClickBlank, this);
  }
}
