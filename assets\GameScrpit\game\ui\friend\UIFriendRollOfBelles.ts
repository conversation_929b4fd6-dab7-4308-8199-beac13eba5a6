import { _decorator, Color, instantiate, Label, Layout, Node, Prefab, Sprite, UITransform } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import ResMgr from "../../../lib/common/ResMgr";
import { JsonMgr } from "../../mgr/JsonMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { UIFriendCard } from "./UIFriendCard";
import { FriendModule } from "../../../module/friend/FriendModule";
import { FriendBellesColorHorizontal, FriendBellesColorHorizontalURL } from "../../../module/friend/FriendConstant";
import { DialogZero } from "../../GameDefine";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Tue Jun 25 2024 15:04:10 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/friend/UIFriendRollOfBelles.ts
 *
 */

@ccclass("UIFriendRollOfBelles")
export class UIFriendRollOfBelles extends UINode {
  protected _openAct: boolean = true;
  private _content: Node;

  private _indexMap: number[] = [];
  private _bellesMax: number = 0;
  private _index: number = 0;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_FRIEND}?prefab/ui/UIFriendRollOfBelles`;
  }
  public tick(dt: any): void {
    if (this._content) {
      if (this._index < this._bellesMax) {
        let friendIds = FriendModule.data.getFriendBellesByLv(this._index + 1);
        let length = friendIds?.length ?? 0;
        if (!this._indexMap[this._index]) {
          let name = FriendModule.config.getFriendBellesByFameLevel(this._index + 1)?.name;
          this._indexMap.push(0);
          let title = instantiate(this.getNode("UIFriendRollOfBellesItem"));
          title.getChildByPath("background/indicator_name").getComponent(Label).string = `${name} ( ${length} )`;
          ResMgr.loadSpriteFrame(
            FriendBellesColorHorizontalURL,
            FriendBellesColorHorizontal[`${this._index + 1}`],
            title.getChildByPath("background/bg_meiming").getComponent(Sprite),
            this
          );
          while (this._indexMap[this._index] < length) {
            let lastNode = title;
            let card = instantiate(this.getNode("UIFriendCard"));
            lastNode.getChildByName("node_content").addChild(card);
            card.getComponent(UIFriendCard).init(friendIds[this._indexMap[this._index]]);
            lastNode.getChildByName("node_content").on(Node.EventType.SIZE_CHANGED, () => {
              lastNode
                .getComponent(UITransform)
                .setContentSize(
                  lastNode.getComponent(UITransform).contentSize.width,
                  lastNode.getChildByName("node_content").getComponent(UITransform).contentSize.height
                );
              this._content.getComponent(Layout).updateLayout();
            });
            this._indexMap[this._index]++;
          }
          this._content.addChild(title);
        }
        if (!(this._indexMap[this._index] < length)) {
          this._index++;
        }
      }
    }
  }
  //=================================================
  public init(args: any): void {
    super.init(args);
    this._bellesMax = Object.values(JsonMgr.instance.jsonList.c_friednFameLv).length;
  }
  protected onEvtShow(): void {
    super.onEvtShow();
    this._content = this.getNode("content");
  }
  private on_click_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
}
