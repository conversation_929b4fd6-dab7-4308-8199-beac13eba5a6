import { _decorator, EventTouch, Label, Node } from "cc";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { PlayerModule } from "../../../module/player/PlayerModule";
import TipMgr from "../../../lib/tips/TipMgr";
import ToolExt from "../../common/ToolExt";
import { director } from "cc";
import SocketClient from "../../../lib/socket/SocketClient";
import TickerMgr from "../../../lib/ticker/TickerMgr";
import { AudioMgr } from "../../../../platform/src/AudioHelper";
import { GameDirector } from "../../GameDirector";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import { ClubModule } from "../../../module/club/ClubModule";
import { FmConfig } from "../../GameDefine";
import { PlayerRouteName, PublicRouteName } from "../../../module/player/PlayerConstant";
import { HdTiaoJianLiBaoModule } from "../../../module/hd_tiaojianlibao/HdTiaoJianLiBaoModule";
import StorageMgr, { StorageKeyEnum } from "db://assets/platform/src/StorageHelper";
import { EventActionModule } from "../../../module/event_action/src/EventActionModule";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { GuideRouteEnum } from "../../../ext_guide/GuideDefine";
import { MainTaskModule } from "../../../module/mainTask/MainTaskModule";
import { ConfirmMsg } from "../UICostConfirm";
import { RouteManager } from "db://assets/platform/src/core/managers/RouteManager";
import { UIFractureMain } from "../ui_fracture/UIFractureMain";
import { RouteShowArgs } from "db://assets/platform/src/core/managers/RouteTableManager";
import FmUtils from "../../../lib/utils/FmUtils";

const { ccclass, property } = _decorator;
const log = Logger.getLoger(LOG_LEVEL.STOP);

@ccclass("UIPlayerMsg")
export class UIPlayerMsg extends UINode {
  protected _openAct: boolean = true;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_PLAYER}?prefab/ui/UIPlayerMsg`;
  }

  /** 初始化显示事件 */
  protected onEvtShow() {
    this.initTopNode();

    // 测试功能不展示
    this.getNode("node_debug").active = FmConfig.isDebug && !FmConfig.forReview;
    this.getNode("UIWorldTalk").active = FmConfig.isDebug && !FmConfig.forReview;

    GameDirector.instance.getVersion();
  }

  private initTopNode() {
    // 设置头像
    let data = ToolExt.newPlayerBaseMessage();
    data.avatarList = PlayerModule.data.getMyAvatarList();
    data.vipLevel = PlayerModule.data.getPlayerInfo().vipLevel;
    FmUtils.setHeaderNode(this.getNode("BtnHeader"), data);

    let playerDataMsg = PlayerModule.data.getPlayerInfo();
    let configLeader = PlayerModule.data.getConfigLeaderData(playerDataMsg.level);

    this.getNode("player_name_lab").getComponent(Label).string = `${playerDataMsg.nickname}`;
    this.getNode("player_level_lab").getComponent(Label).string = `掌门等级 : ${configLeader.name}`;

    let clubName = ClubModule.data.clubMessage?.name || "无";
    this.getNode("xm_lab").getComponent(Label).string = `所属战盟：${clubName}`;

    let clubJob = ClubModule.data.position ? ClubModule.data.positionName : "无";
    this.getNode("xm_zw_lab").getComponent(Label).string = `战盟职位 : ${clubJob}`;

    this.getNode("server_lab").getComponent(Label).string = `服务器：${PlayerModule.data.getPlayerInfo().serverId}`;

    this.getNode("id_lab").getComponent(Label).string = `${playerDataMsg.id}`;

    this.getNode("btn_effect").getChildByPath("check_box/check").active = AudioMgr.instance.isEnableEffect();
    this.getNode("btn_music").getChildByPath("check_box/check").active = AudioMgr.instance.isEnableMusic();
    this.getNode("lbl_version").getComponent(Label).string = `版本号:${FmConfig.version}-${FmConfig.packageType}`;
  }

  private on_click_btn_close() {
    UIMgr.instance.back();
  }

  private on_click_btn_bianji() {
    UIMgr.instance.showDialog("UIPlayerChangeName", {}, () => {
      this.initTopNode();
    });
  }

  private on_click_btn_reset() {
    const msg: ConfirmMsg = {
      msg: "重置主角，会清除所有数据，是否确认？",
      okText: "确认",
    };

    UIMgr.instance.showDialog(PublicRouteName.UICostConfirm, msg, (resp) => {
      if (resp?.ok) {
        HdTiaoJianLiBaoModule.api.testReset(() => {
          PlayerModule.api.resetRole();
          UIMgr.clear();
          TickerMgr.clearAllTicker();
          director.loadScene("SceneLogin");
        });
      }
    });
  }

  private on_click_btn_levelUp() {
    PlayerModule.api.testLv(() => {
      TipMgr.showTip("升级成功");
    });
  }

  private on_click_btn_close_bg() {
    UIMgr.instance.back();
  }

  private on_click_btn_change_server() {
    SocketClient.ins.close();
    UIMgr.clear();
    TickerMgr.clearAllTicker();
    director.loadScene("SceneLogin");
  }
  private on_click_btn_effect(e: EventTouch) {
    let target: Node = e.target;
    target.getChildByPath("check_box/check").active = !target.getChildByPath("check_box/check").active;
    if (target.getChildByPath("check_box/check").active) {
      AudioMgr.instance.enableEffect();
    } else {
      AudioMgr.instance.disableEffect();
    }
  }
  private on_click_btn_music(e: EventTouch) {
    let target: Node = e.target;
    target.getChildByPath("check_box/check").active = !target.getChildByPath("check_box/check").active;
    if (target.getChildByPath("check_box/check").active) {
      AudioMgr.instance.enableMusic();
    } else {
      AudioMgr.instance.disableMusic();
    }
  }

  private on_click_btn_notice() {
    UIMgr.instance.showDialog(PlayerRouteName.UINotice);
  }

  private on_click_btn_xiaoyouxi() {
    director.loadScene("SceneQiyunXiaoyouxi");
  }
  private on_click_btn_reset_windows() {
    // 提示不能开启
    const msg: ConfirmMsg = {
      msg: "重置主角，会清除所有数据，是否确认？",
      okText: "确认",
    };

    UIMgr.instance.showDialog(PublicRouteName.UICostConfirm, msg, (resp) => {
      if (resp?.ok) {
        HdTiaoJianLiBaoModule.api.testReset(() => {
          for (let i = 0; i < 10; i++) {
            let load_storageValue = StorageMgr.loadStr(`${StorageKeyEnum.TiaoJianLiBao_DAYMARK}_${i}`);
            if (load_storageValue !== "") {
              StorageMgr.saveItem(`${StorageKeyEnum.TiaoJianLiBao_DAYMARK}_${i}`, "");
            }
          }
          TipMgr.showTip("重置成功");
        });
      }
    });
  }

  private on_click_btn_san_jie_shi_jian() {
    TipsMgr.topRouteCtrl.show(GuideRouteEnum.TopConfirm, {}, (resp) => {
      if (resp.code == 200) {
        EventActionModule.api.test_申请_测试事件(parseInt(resp.param1), () => {
          TipsMgr.showTip("操作成功:");
        });
      }
    });
  }

  private on_click_btn_task_set() {
    TipsMgr.topRouteCtrl.show(GuideRouteEnum.TopConfirm, {}, (resp) => {
      if (resp.code == 200) {
        MainTaskModule.api.testGoToMainTask(Number(resp.param1), () => {
          TipsMgr.showTip("操作成功:");
        });
      }
    });
  }

  private on_click_btn_statistic_emit() {
    TipsMgr.topRouteCtrl.show(GuideRouteEnum.TopConfirm, {}, (resp) => {
      if (resp.code == 200) {
        PlayerModule.api.testStatisticsEvent(Number(resp.param1), (data) => {
          TipsMgr.showTip("发送成功:" + data.value);
        });
      }
    });
  }

  private on_click_btn_route_test() {
    let args: RouteShowArgs = {
      payload: {
        data: "123",
      },
      onCloseBack: (arg) => {
        log.log("onCloseBack-返回参数：", arg);
      },
      onUIReady: () => {
        log.log("onUIReady");
      },
    };
    RouteManager.uiRouteCtrl.showRoute(UIFractureMain, args);
  }
}
