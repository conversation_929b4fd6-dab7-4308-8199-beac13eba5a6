import { _decorator, instantiate, isValid, Label, Node, Sprite, tween, v3 } from "cc";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UINode } from "../../../lib/ui/UINode";
import { FarmModule } from "../../../module/farm/FarmModule";
import { ItemCtrl } from "../../common/ItemCtrl";
import Formate from "../../../lib/utils/Formate";
import { NodeTool } from "../../../lib/utils/NodeTool";
import { JsonMgr } from "../../mgr/JsonMgr";
import { Sleep } from "../../GameDefine";
import { BgList } from "../../../module/farm/FarmSlotUITool";
const { ccclass, property } = _decorator;

@ccclass("UIFarmPreview")
export class UIFarmPreview extends UINode {
  protected _openAct = true;
  layout_1: Node;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_FARM}?prefab/ui/UIFarmPreview`;
  }

  async onEvtShow() {
    this.layout_1 = this.getNode("layout_1");

    this.layout_1.children.forEach((child) => {
      child.active = false;
    });

    let keyList = Object.keys(JsonMgr.instance.jsonList.c_blessLand);
    for (let idx in keyList) {
      if (isValid(this.node) == false) {
        return;
      }

      let nodeItem1 = this.layout_1.children[idx];
      if (!nodeItem1) {
        nodeItem1 = instantiate(this.layout_1.children[0]);
        nodeItem1.setParent(this.layout_1);
      }

      // 动画表现
      nodeItem1.active = true;
      nodeItem1.setScale(0, 0, 1);
      tween(nodeItem1)
        .to(0.2, { scale: v3(1, 1, 1) })
        .start();

      // 初始化数据
      await this.updateOne(nodeItem1, Number(keyList[idx]));
    }
  }

  async updateOne(scrollViewItem: Node, gourdId: number) {
    const cfgHulu = FarmModule.data.getConfigBlessLand(gourdId);
    if (!cfgHulu) {
      return;
    }
    // 设置葫芦
    const nodeHulu = NodeTool.findByName(scrollViewItem, "hulu");
    nodeHulu.children?.forEach((node) => {
      if (node.name.startsWith("img_hulu_")) {
        if (node.name == "img_hulu_" + (gourdId - 1)) {
          node.active = true;
        } else {
          node.active = false;
        }
      }
    });

    // 葫芦背景
    let sf = await this.assetMgr.loadSpriteFrameSync(
      BundleEnum.BUNDLE_COMMON_ITEM,
      `autoItem/${BgList[cfgHulu.color - 1]}`
    );
    if (isValid(this.node) == false) {
      return;
    }
    nodeHulu.getChildByName("bg").getComponent(Sprite).spriteFrame = sf;

    // 等级
    nodeHulu.getChildByName("lbl_level").getComponent(Label).string = `${gourdId}级`;

    // 设置奖励列表
    const nodeContentReward = NodeTool.findByName(scrollViewItem, "content_reward");
    const nodeItemRewardFix = NodeTool.findByName(scrollViewItem, "item_reward_0");
    const nodeItemRewardRate = NodeTool.findByName(scrollViewItem, "item_reward_1");
    nodeContentReward.children.forEach((child) => {
      child.active = false;
    });

    // 必得
    for (let idx in cfgHulu.reward1List) {
      let itemInfo = cfgHulu.reward1List[idx];
      nodeItemRewardFix.active = true;
      nodeItemRewardFix.setParent(nodeContentReward);
      nodeItemRewardFix.getChildByName("Item").getComponent(ItemCtrl).setItemId(itemInfo[0], itemInfo[1]);
    }

    // 几率获得
    let powerAll = 0; // 总权重
    for (let idx in cfgHulu.reward2List) {
      let itemInfo = cfgHulu.reward2List[idx];
      powerAll += itemInfo[2];
    }
    for (let idx in cfgHulu.reward2List) {
      if (Number(idx) > 0 && Number(idx) % 3 == 0) {
        await Sleep(0.01);
        if (isValid(this.node) == false) {
          return;
        }
      }

      let itemInfo = cfgHulu.reward2List[idx];
      let nodeNew = nodeContentReward.children[Number(idx) + 1];
      if (!nodeNew) {
        nodeNew = instantiate(nodeItemRewardRate);
      }

      // 初始化数据

      nodeNew.setParent(nodeContentReward);
      nodeNew.getChildByName("Item").getComponent(ItemCtrl).setItemId(itemInfo[0], itemInfo[1]);
      nodeNew.getChildByName("lbl_rate").getComponent(Label).string = `${Formate.formatDecimal(
        (itemInfo[2] * 100) / powerAll,
        2
      )}%`;

      // 动画
      nodeNew.active = true;
      nodeNew.setScale(0, 0, 1);
      tween(nodeNew)
        .to(0.2, { scale: v3(1, 1, 1) })
        .start();
    }
    await Sleep(0.01);
  }
}
