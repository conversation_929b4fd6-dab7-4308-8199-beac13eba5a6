import { _decorator, instantiate, Label, Node, Prefab } from "cc";
import { UINode } from "../../../../../lib/ui/UINode";
import { BundleEnum } from "../../../../../game/bundleEnum/BundleEnum";
import { UIMgr } from "../../../../../lib/ui/UIMgr";
import MsgMgr from "../../../../../lib/event/MsgMgr";
import MsgEnum from "../../../../../game/event/MsgEnum";
import TipMgr from "../../../../../lib/tips/TipMgr";
import { JsonMgr } from "../../../../../game/mgr/JsonMgr";
import { PupilModule } from "../../PupilModule";
import ToolExt from "../../../../../game/common/ToolExt";
import { PupilRouteName } from "../../PupilRoute";
import { IConfigPupil } from "../../PupilData";
import { PupilAni } from "./PupilAni";
import { formatNumber } from "../../../../../lib/utils/NumbersUtils";
import { addAttrMap, AttrArrayToMap } from "../../../../../lib/utils/AttrTool";
import ResMgr from "../../../../../lib/common/ResMgr";
import { TipAttrAdd } from "../../../../../lib/common/TipAttrAdd";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

enum SlotState {
  /**未解锁 */
  LOCKED = "locked",
  /**添加 */
  ADD = "add",
  /**任命 */
  WORK = "work",
}
@ccclass("UIPupilWorkSlot")
export class UIPupilWorkSlot extends UINode {
  protected _openAct: boolean = true;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_PUPIL}?prefab/ui/UIPupilWorkSlot`;
  }
  protected dependOn(): BundleEnum[] {
    return [BundleEnum.BUNDLE_COMMON_UI, BundleEnum.BUNDLE_COMMON_ITEM];
  }

  private _slotStateMap: Map<number, SlotState> = new Map<number, SlotState>();

  /**配置 */
  private baseConfig: IConfigPupil;

  protected onRegEvent() {
    MsgMgr.on(MsgEnum.ON_PUPIL_UPDATE, this.refresh, this);
  }
  protected onDelEvent() {
    MsgMgr.off(MsgEnum.ON_PUPIL_UPDATE, this.refresh, this);
  }

  protected onEvtShow(): void {
    this.baseConfig = JsonMgr.instance.jsonList.c_pupil[1];
    this.refresh();
  }

  private refresh() {
    this.getNode("work_slot_bg").children.forEach((val, index) => {
      this.setSlotState(val, index);

      val.getChildByName("bg_chegnhao").active = true;
      val.getChildByName("Label3").getComponent(Label).string = `额外+${this.baseConfig.placeAdd[index] / 100}%`;

      let pupilMessage = PupilModule.data.allPupilMap[PupilModule.data.pupilTrainMsg.workSlotList[index]];
      if (pupilMessage?.ownInfo) {
        val.getChildByPath("btn_pupil/PupilAni1").active = true;
        val
          .getChildByPath("btn_pupil/PupilAni1")
          .getComponent(PupilAni)
          .setAniByNameId(pupilMessage.ownInfo.nameId, pupilMessage.ownInfo.adultAttrList.length);
      } else {
        val.getChildByPath("btn_pupil/PupilAni1").active = false;
      }
      if (pupilMessage?.partnerInfo) {
        val.getChildByPath("btn_pupil/PupilAni2").active = true;
        val
          .getChildByPath("btn_pupil/PupilAni2")
          .getComponent(PupilAni)
          .setAniByNameId(pupilMessage.partnerInfo.nameId, pupilMessage.ownInfo.adultAttrList.length);
      } else {
        val.getChildByPath("btn_pupil/PupilAni2").active = false;
      }
    });

    this.setBottomAttrAdd();
  }

  /**设置任命槽位状态 */
  private setSlotState(valNode: Node, slotIndex: number) {
    let slotNum = PupilModule.data.pupilTrainMsg.workSlotList[slotIndex] || -2;

    if (slotNum == -2) {
      this._slotStateMap.set(slotIndex, SlotState.LOCKED);
    } else if (slotNum == -1) {
      this._slotStateMap.set(slotIndex, SlotState.ADD);
    } else {
      this._slotStateMap.set(slotIndex, SlotState.WORK);
    }

    let state = this._slotStateMap.get(slotIndex);
    valNode.getChildByName("btn_suo").active = state == SlotState.LOCKED;
    valNode.getChildByName("btn_jiahao").active = state == SlotState.ADD;
    valNode.getChildByName("btn_shuaxin").active = state == SlotState.WORK;
    valNode.getChildByName("btn_pupil").active = state == SlotState.WORK;

    valNode.getChildByName("btn_pupil").on(Node.EventType.TOUCH_END, () => {
      AudioMgr.instance.playEffect(1556);
      UIMgr.instance.showDialog(PupilRouteName.UIPupilWorkSlotPop, { slotIndex });
    });

    valNode.getChildByName("btn_shuaxin").on(Node.EventType.TOUCH_END, () => {
      AudioMgr.instance.playEffect(1556);
      UIMgr.instance.showDialog(PupilRouteName.UIPupilWorkSlotPop, { slotIndex });
    });

    valNode.getChildByName("btn_jiahao").on(Node.EventType.TOUCH_END, () => {
      AudioMgr.instance.playEffect(1556);
      UIMgr.instance.showDialog(PupilRouteName.UIPupilWorkSlotPop, { slotIndex });
    });

    valNode.getChildByName("btn_suo").on(Node.EventType.TOUCH_END, () => {
      AudioMgr.instance.playEffect(1556);
      TipMgr.showTip(`弟子数量达到${this.baseConfig.unlockWorkPlaceList[slotIndex]}解锁`);
    });

    valNode.on(Node.EventType.TOUCH_END, () => {
      AudioMgr.instance.playEffect(1556);
      if (state == SlotState.LOCKED) {
        TipMgr.showTip(`弟子数量达到${this.baseConfig.unlockWorkPlaceList[slotIndex]}解锁`);
      } else {
        UIMgr.instance.showDialog(PupilRouteName.UIPupilWorkSlotPop, { slotIndex }, () => {
          this.refresh();
        });
      }
    });
  }

  private addByJob(attrMap: Map<number, number>, attrAdd: number[], jobAddRate: number) {
    if (!attrAdd || attrAdd.length == 0) {
      return;
    }

    let attrAddClone = JSON.parse(JSON.stringify(attrAdd));
    for (let i = 0; i < attrAddClone.length; i += 2) {
      attrAddClone[i + 1] = attrAddClone[i + 1] * (1 + jobAddRate);
    }
    let attrMap1 = AttrArrayToMap(attrAddClone);
    addAttrMap(attrMap, attrMap1);
  }

  /**任命弟子的总加成 */
  private setBottomAttrAdd() {
    let content = this.getNode("content");
    content.removeAllChildren();

    let workSlotList = PupilModule.data.pupilTrainMsg.workSlotList;
    let attrMap = new Map<number, number>();
    for (let i = 0; i < workSlotList.length; i++) {
      // 弟子属性
      let pupilMessage = PupilModule.data.allPupilMap[workSlotList[i]];

      // 额外加成
      let extAdd = this.baseConfig.placeAdd[i] / 10000;

      // 弟子成年属性
      this.addByJob(attrMap, pupilMessage?.ownInfo?.adultAttrList, extAdd);

      // 弟子配偶成年属性
      this.addByJob(attrMap, pupilMessage?.partnerInfo?.adultAttrList, extAdd);

      // 弟子初始属性
      this.addByJob(attrMap, pupilMessage?.ownInfo?.initAttrList, extAdd);

      // 弟子配偶初始属性
      this.addByJob(attrMap, pupilMessage?.partnerInfo?.initAttrList, extAdd);
    }

    // 属性展示
    Object.keys(attrMap).forEach((key) => {
      let attr_lab = ToolExt.clone(this.getNode("attr_lab"), this);
      attr_lab.active = true;
      attr_lab.getComponent(Label).string = `${JsonMgr.instance.jsonList.c_attribute[key]["name"]}:${formatNumber(
        attrMap[key] * 100,
        1
      )}%`;
      content.addChild(attr_lab);
    });
  }

  private on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  private on_click_btn_gantanhao_huang() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let obj: { [key: number]: number } = Object.create(null);
    let workSlotList = PupilModule.data.pupilTrainMsg.workSlotList;
    log.log("workSlotList====", workSlotList);
    for (let i = 0; i < workSlotList.length; i++) {
      let id = workSlotList[i];
      if (id < 0) {
        continue;
      }
      // 额外加成
      let extAdd = this.baseConfig.placeAdd[i] / 10000;
      let pupilMessage = PupilModule.data.allPupilMap[workSlotList[i]];

      log.log("pupilMessage====", pupilMessage);

      let ownInfo = pupilMessage?.ownInfo;

      for (let j = 0; j < ownInfo.adultAttrList.length; j += 2) {
        if (!obj[ownInfo.adultAttrList[j]]) {
          obj[ownInfo.adultAttrList[j]] = 0;
        }

        obj[ownInfo.adultAttrList[j]] += ownInfo.adultAttrList[j + 1] * (1 + extAdd);
      }

      // for (let j = 0; j < ownInfo.basicAttrList.length; j += 2) {
      //   if (!obj[ownInfo.basicAttrList[j]]) {
      //     obj[ownInfo.basicAttrList[j]] = 0;
      //   }

      //   obj[ownInfo.basicAttrList[j]] += ownInfo.basicAttrList[j + 1] * (1 + extAdd);
      // }

      for (let j = 0; j < ownInfo.initAttrList.length; j += 2) {
        if (!obj[ownInfo.initAttrList[j]]) {
          obj[ownInfo.initAttrList[j]] = 0;
        }

        obj[ownInfo.initAttrList[j]] += ownInfo.initAttrList[j + 1] * (1 + extAdd);
      }

      let partnerInfo = pupilMessage?.partnerInfo;

      for (let j = 0; j < partnerInfo.adultAttrList.length; j += 2) {
        if (!obj[partnerInfo.adultAttrList[j]]) {
          obj[partnerInfo.adultAttrList[j]] = 0;
        }

        obj[partnerInfo.adultAttrList[j]] += partnerInfo.adultAttrList[j + 1] * (1 + extAdd);
      }

      for (let j = 0; j < partnerInfo.initAttrList.length; j += 2) {
        if (!obj[partnerInfo.initAttrList[j]]) {
          obj[partnerInfo.initAttrList[j]] = 0;
        }

        obj[partnerInfo.initAttrList[j]] += partnerInfo.initAttrList[j + 1] * (1 + extAdd);
      }

      // for (let j = 0; j < partnerInfo.basicAttrList.length; j += 2) {
      //   if (!obj[partnerInfo.basicAttrList[j]]) {
      //     obj[partnerInfo.basicAttrList[j]] = 0;
      //   }

      //   obj[partnerInfo.basicAttrList[j]] += partnerInfo.basicAttrList[j + 1] * (1 + extAdd);
      // }
    }

    ResMgr.loadPrefab(
      `${BundleEnum.BUNDLE_G_COMMON_MAIN}?prefab/top/TipAttrAdd2`,
      (prefab: Prefab) => {
        let node = instantiate(prefab);
        node.getComponent(TipAttrAdd).setAttr(obj, false);
        TipsMgr.showTipNode(node, -1);
      },
      this
    );
  }
}
