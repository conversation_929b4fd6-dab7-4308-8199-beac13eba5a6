syntax = "proto3";
package sim;

// 
message LuckDrawResponse {
  // 神迹触发几次
  int32 trigger = 1;
  // 偶数索引为道具的ID，奇数为数量
  repeated double resAddList = 2;
  // 最新的模块信息
  LuckTrainMessage luckTrain = 3;
  repeated LuckRecordMessage recordList = 4;
}

// 
message LuckRecordMessage {
  string name = 1;
}

// 
message LuckTrainMessage {
  // 剩余的观看视频的次数
  int32 video = 1;
  // 已经累计的幸运值
  int32 count = 2;
  // 引导ID
  int64 guideId = 3;
}

// 
message LuckTrainResponse {
  // 幸运抽奖模块的总体信息
  LuckTrainMessage train = 1;
  // 抽到大奖的记录用来滚动展示
  repeated LuckRecordMessage recordList = 2;
}

