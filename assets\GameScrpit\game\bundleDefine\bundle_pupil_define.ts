/**弟子界面头部按钮索引 */
export enum TopBtnLay_Enum {
  btn_trainMain = "btn_trainMain",
  btn_unmarry_main = "btn_unmarry_main",
  btn_married_main = "btn_married_main",
}

/**弟子状态 */
export enum PupilState_Enum {
  pupil_train = "pupil_train",
  pupil_adult_unmarried = "pupil_adult_unmarried",
  pupil_married_unwork = "pupil_married_unwork",
  pupil_work = "pupil_work",
}

export interface ConfigPupilSkinRecord {
  id: number;
  iconId01: string;
  iconId02: string;
  iconId03: string;
  sex: number;
  showChangeList: number[];
}
