import { MailMessage } from "../../game/net/protocol/Mail";

export class MailData {
  private _sysMailList: MailMessage[] = [];
  private _dailyMailList: MailMessage[] = [];



  public get sysMailList(): MailMessage[] {
    return this._sysMailList;
  }

  public get dailyMailList(): MailMessage[] {
    return this._dailyMailList;
  }

  public set dailyMailList(list: MailMessage[]) {
    this._dailyMailList = list;
  }

  public set sysMailList(list: MailMessage[]) {
    this._sysMailList = list;
  }
}
