{"skeleton": {"hash": "Xmsp0aDuXlUCKCk7gvx8GIDVsYY=", "spine": "3.8.75", "x": -72.81, "y": -12.83, "width": 278.01, "height": 243.27, "images": "./images/", "audio": "D:/spine导出/盘古动画"}, "bones": [{"name": "root"}, {"name": "all", "parent": "root", "length": 104.55, "rotation": -1.37, "x": -8.88, "y": -23.33}, {"name": "bone", "parent": "all", "length": 54.53, "rotation": 1.07, "x": 11.24, "y": 104.81, "scaleY": 1.02}, {"name": "bone2", "parent": "bone", "length": 31.76, "rotation": 87.7, "x": -9.45, "y": 1.69}, {"name": "bone3", "parent": "bone2", "length": 27.53, "rotation": -2.98, "x": 31.76}, {"name": "bone4", "parent": "bone3", "length": 9.21, "rotation": -4.72, "x": 27.53}, {"name": "bone5", "parent": "bone4", "x": 1.61, "y": 3.88, "color": "abe323ff"}, {"name": "bone6", "parent": "bone4", "x": 11.49, "y": 4.26}, {"name": "bone7", "parent": "bone4", "length": 9.85, "rotation": -169.7, "x": -1.86, "y": -10.81, "color": "32ff00ff"}, {"name": "bone8", "parent": "bone7", "length": 10.88, "rotation": -23.39, "x": 9.85, "color": "32ff00ff"}, {"name": "bone9", "parent": "bone4", "length": 8.42, "rotation": -163.25, "x": -7.51, "y": 8.19, "color": "32ff00ff"}, {"name": "bone10", "parent": "bone9", "length": 8.86, "rotation": 1.44, "x": 8.42, "color": "32ff00ff"}, {"name": "bone11", "parent": "bone4", "length": 17.06, "rotation": 165.2, "x": 10.32, "y": 15.65, "color": "ffc900ff"}, {"name": "bone12", "parent": "bone11", "length": 13.83, "rotation": 10.63, "x": 17.06, "color": "ffc900ff"}, {"name": "bone13", "parent": "bone12", "length": 12.82, "rotation": 2.31, "x": 13.83, "color": "ffc900ff"}, {"name": "bone14", "parent": "bone4", "length": 16.58, "rotation": -140.46, "x": 19.75, "y": -12.05, "color": "ffc900ff"}, {"name": "bone15", "parent": "bone14", "length": 13.41, "rotation": 7.91, "x": 18.5, "y": -0.33, "color": "ffc900ff"}, {"name": "bone16", "parent": "bone15", "length": 12.65, "rotation": -26.32, "x": 13.41, "color": "ffc900ff"}, {"name": "bone17", "parent": "bone2", "x": 13.87, "y": 4.82, "color": "abe323ff"}, {"name": "bone18", "parent": "bone2", "x": 33.97, "y": 4.13, "color": "abe323ff"}, {"name": "bone19", "parent": "bone3", "x": 17.38, "y": -29.23}, {"name": "bone20", "parent": "bone3", "x": 18.7, "y": 17.99}, {"name": "bone21", "parent": "bone20", "length": 22.15, "rotation": 51.24, "x": -2.26, "y": -1.29}, {"name": "bone22", "parent": "bone21", "length": 19.71, "rotation": -16.91, "x": 22.23, "y": -0.09}, {"name": "bone23", "parent": "bone22", "length": 13.82, "rotation": -10.38, "x": 21.72, "y": 0.19}, {"name": "bone24", "parent": "bone19", "length": 26.63, "rotation": -153.65, "x": -1.1, "y": -2.04}, {"name": "bone25", "parent": "bone24", "length": 24.11, "rotation": -2.51, "x": 26.63}, {"name": "bone26", "parent": "bone", "length": 11.29, "rotation": -109.1, "x": -21.46, "y": 1.23, "color": "ec00ffff"}, {"name": "bone27", "parent": "bone26", "length": 11.57, "rotation": -14.5, "x": 11.29, "color": "ec00ffff"}, {"name": "bone28", "parent": "bone", "length": 12.95, "rotation": -71.47, "x": 11.81, "y": 5.48, "color": "ec00ffff"}, {"name": "bone29", "parent": "bone28", "length": 13.22, "rotation": 6.6, "x": 12.95, "color": "ec00ffff"}, {"name": "bone30", "parent": "bone", "length": 11.85, "rotation": -89.7, "x": -4.21, "y": -0.31, "color": "ec00ffff"}, {"name": "bone31", "parent": "bone30", "length": 14.71, "rotation": 1.75, "x": 11.85, "color": "ec00ffff"}, {"name": "bone32", "parent": "bone", "length": 43.13, "rotation": -104.32, "x": -14.31, "y": -5.53}, {"name": "bone33", "parent": "bone32", "length": 24.81, "rotation": 18.44, "x": 43.13}, {"name": "bone34", "parent": "bone33", "length": 18.06, "rotation": -58.22, "x": 24.81}, {"name": "bone35", "parent": "bone", "length": 44.47, "rotation": -69.51, "x": 3.83, "y": -4.95}, {"name": "bone36", "parent": "bone35", "length": 29.77, "rotation": -19.23, "x": 44.47}, {"name": "bone37", "parent": "bone36", "length": 16.98, "rotation": 56.78, "x": 29.77}, {"name": "bone38", "parent": "bone", "length": 50.7, "rotation": -50.54, "x": 5.31, "y": -3.65}, {"name": "bone39", "parent": "bone38", "length": 36.6, "rotation": -65.95, "x": 50.7, "color": "abe323ff"}, {"name": "bone40", "parent": "bone", "length": 45.99, "rotation": -123.23, "x": -17.12, "y": -5.38}, {"name": "bone41", "parent": "bone40", "length": 34.18, "rotation": 67.6, "x": 45.99, "color": "abe323ff"}, {"name": "all3", "parent": "all", "length": 23.75, "rotation": -1.62, "x": 33.89, "y": 19.97, "color": "ff0000ff"}, {"name": "all2", "parent": "all3", "length": 18.42, "rotation": 94.64, "x": -0.17, "y": -0.33, "color": "ff0000ff"}, {"name": "target1", "parent": "all2", "rotation": -91.65, "x": 38.45, "y": 0.25, "color": "ff3f00ff"}, {"name": "target2", "parent": "all2", "rotation": -91.65, "x": 7.6, "y": 0.82, "color": "ff3f00ff"}, {"name": "target3", "parent": "target2", "x": 14.28, "y": -9.05, "transform": "noScale", "color": "ff3f00ff"}, {"name": "all4", "parent": "all", "length": 26.28, "rotation": -176.26, "x": -10.3, "y": 27.63, "color": "ff0000ff"}, {"name": "all5", "parent": "all4", "length": 17.54, "rotation": -92.37, "x": 0.03, "y": 0.73, "color": "ff0000ff"}, {"name": "targe1", "parent": "all5", "rotation": -90, "x": 29.46, "y": 1.75, "color": "ff3f00ff"}, {"name": "targe2", "parent": "all5", "rotation": -90, "x": 3.68, "y": 0.05, "color": "ff3f00ff"}, {"name": "targe3", "parent": "targe2", "x": -14.76, "y": -10.58, "transform": "noScale", "color": "ff3f00ff"}, {"name": "all6", "parent": "all", "x": 92.85, "y": 31.19, "color": "1aff00ff"}, {"name": "bone42", "parent": "bone23", "rotation": 108.03, "x": -0.42, "y": 20.2, "transform": "noRotationOrReflection", "color": "1aff00ff"}, {"name": "all7", "parent": "all", "x": -52.03, "y": 88.16, "color": "1aff00ff"}, {"name": "bone43", "parent": "bone23", "length": 14.17, "rotation": -17.35, "x": -272.17, "y": -227.33}, {"name": "bone44", "parent": "bone43", "length": 14.17, "x": 14.17}, {"name": "bone45", "parent": "bone44", "length": 14.17, "x": 14.17}, {"name": "bone46", "parent": "bone45", "length": 14.17, "x": 14.17}, {"name": "bone47", "parent": "bone46", "length": 14.17, "x": 14.17}, {"name": "bone48", "parent": "bone47", "length": 14.17, "x": 14.17}, {"name": "bone49", "parent": "bone48", "length": 14.17, "x": 14.17}, {"name": "bone50", "parent": "bone49", "length": 14.17, "x": 14.17}, {"name": "bone51", "parent": "bone50", "length": 14.17, "x": 14.17}, {"name": "bone52", "parent": "bone51", "length": 14.17, "x": 14.17}, {"name": "bone53", "parent": "bone52", "length": 14.17, "x": 14.17}, {"name": "bone54", "parent": "bone53", "length": 14.17, "x": 14.17}, {"name": "bone55", "parent": "bone54", "length": 14.17, "x": 14.17}, {"name": "bone56", "parent": "bone55", "length": 14.17, "x": 14.17}, {"name": "bone57", "parent": "bone56", "length": 14.17, "x": 14.17}, {"name": "bone58", "parent": "bone57", "length": 14.17, "x": 14.17}, {"name": "bone59", "parent": "bone58", "length": 14.17, "x": 14.17}, {"name": "bone60", "parent": "bone59", "length": 14.17, "x": 14.17}, {"name": "bone61", "parent": "bone60", "length": 14.17, "x": 14.17}, {"name": "bone62", "parent": "bone61", "length": 14.17, "x": 14.17}, {"name": "bone63", "parent": "bone23", "rotation": 108.03, "x": 9.15, "y": -23.06, "transform": "noRotationOrReflection", "color": "002cffff"}, {"name": "all8", "parent": "all", "x": 68.07, "y": 115.68, "color": "002cffff"}, {"name": "all9", "parent": "all", "x": 222.07, "y": 23.21, "color": "002cffff"}, {"name": "all10", "parent": "all", "length": 12.27, "rotation": 92.51, "x": 270.48, "y": 20.36}, {"name": "all11", "parent": "all10", "length": 12.27, "x": 12.27}, {"name": "all12", "parent": "all11", "length": 12.27, "x": 12.27}, {"name": "all13", "parent": "all12", "length": 12.27, "x": 12.27}, {"name": "all14", "parent": "all13", "length": 12.27, "x": 12.27}, {"name": "all15", "parent": "all14", "length": 12.27, "x": 12.27}, {"name": "all16", "parent": "all15", "length": 12.27, "x": 12.27}, {"name": "all17", "parent": "all16", "length": 12.27, "x": 12.27}, {"name": "all18", "parent": "all17", "length": 12.27, "x": 12.27}, {"name": "all19", "parent": "all18", "length": 12.27, "x": 12.27}, {"name": "all20", "parent": "all19", "length": 12.27, "x": 12.27}, {"name": "all21", "parent": "all20", "length": 12.27, "x": 12.27}, {"name": "all22", "parent": "all21", "length": 12.27, "x": 12.27}, {"name": "all23", "parent": "all22", "length": 12.27, "x": 12.27}, {"name": "all24", "parent": "all23", "length": 12.27, "x": 12.27}, {"name": "all25", "parent": "all24", "length": 12.27, "x": 12.27}, {"name": "all26", "parent": "all25", "length": 12.27, "x": 12.27}, {"name": "all27", "parent": "all26", "length": 12.27, "x": 12.27}, {"name": "all28", "parent": "all27", "length": 12.27, "x": 12.27}, {"name": "all29", "parent": "all28", "length": 12.27, "x": 12.27}], "slots": [{"name": "lian2", "bone": "root", "attachment": "lian2"}, {"name": "lian1", "bone": "root", "attachment": "lian1"}, {"name": "tui2", "bone": "root", "attachment": "tui2"}, {"name": "tui1", "bone": "root", "attachment": "tui1"}, {"name": "toufa", "bone": "root", "attachment": "toufa"}, {"name": "shou1", "bone": "root", "attachment": "shou1"}, {"name": "body", "bone": "root", "attachment": "body"}, {"name": "qunzi", "bone": "root", "attachment": "qunzi"}, {"name": "shou2", "bone": "root", "attachment": "shou2"}, {"name": "tou", "bone": "root", "attachment": "tou"}, {"name": "lian11", "bone": "bone23", "attachment": "lian11"}, {"name": "lianl2", "bone": "bone23", "attachment": "lianl2"}], "ik": [{"name": "targe1", "order": 7, "bones": ["bone32"], "target": "targe1", "compress": true, "stretch": true}, {"name": "targe2", "order": 8, "bones": ["bone33"], "target": "targe2", "compress": true, "stretch": true}, {"name": "targe3", "order": 9, "bones": ["bone34"], "target": "targe3"}, {"name": "targe4", "order": 5, "bones": ["bone40", "bone41"], "target": "targe2"}, {"name": "target1", "order": 2, "bones": ["bone35"], "target": "target1", "compress": true, "stretch": true}, {"name": "target2", "order": 3, "bones": ["bone36"], "target": "target2", "compress": true, "stretch": true}, {"name": "target3", "order": 4, "bones": ["bone37"], "target": "target3"}, {"name": "target4", "bones": ["bone38", "bone39"], "target": "target2", "bendPositive": false}], "transform": [{"name": "all7", "order": 17, "bones": ["all6"], "target": "all7", "x": 99.3, "y": -16.58, "rotateMix": 0, "translateMix": 0.032, "scaleMix": 0, "shearMix": 0}, {"name": "body", "order": 10, "bones": ["bone18"], "target": "bone17", "x": 20.1, "y": -0.69, "rotateMix": -0.828, "translateMix": -0.828, "scaleMix": -0.828, "shearMix": -0.828}, {"name": "jio1", "order": 1, "bones": ["target1"], "target": "bone39", "rotation": 116.61, "x": 9.82, "y": -14.05, "scaleX": -0.016, "scaleY": -0.0041, "shearY": 0.9, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}, {"name": "jio2", "order": 6, "bones": ["targe1"], "target": "bone41", "rotation": 56.91, "x": 12.18, "y": 12.57, "scaleX": -0.0138, "scaleY": -0.0063, "shearY": -1.1, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}, {"name": "lian", "order": 12, "bones": ["bone6"], "target": "bone5", "x": 9.87, "y": 0.39, "rotateMix": -0.801, "translateMix": -0.801, "scaleMix": -0.801, "shearMix": -0.801}, {"name": "shou1", "order": 13, "bones": ["bone21"], "target": "bone18", "rotation": 48.26, "x": 15.06, "y": 11.69, "shearY": -360, "rotateMix": 0, "translateMix": 0.3, "scaleMix": 0, "shearMix": 0}, {"name": "shou2", "order": 16, "bones": ["bone24"], "target": "bone18", "rotation": -156.63, "x": 12.42, "y": -36.21, "rotateMix": 0, "translateMix": 0.3, "scaleMix": 0, "shearMix": 0}, {"name": "tou", "order": 11, "bones": ["bone4"], "target": "bone18", "rotation": -7.7, "x": 25.27, "y": -5.56, "rotateMix": 0, "translateMix": 0.201, "scaleMix": 0, "shearMix": 0}], "path": [{"name": "lian11", "order": 14, "bones": ["bone43", "bone44", "bone45", "bone46", "bone47", "bone48", "bone49", "bone50", "bone51", "bone52", "bone53", "bone54", "bone55", "bone56", "bone57", "bone58", "bone59", "bone60", "bone61", "bone62"], "target": "lian11", "spacingMode": "percent", "spacing": 0.0496}, {"name": "lianl2", "order": 15, "bones": ["all10", "all11", "all12", "all13", "all14", "all15", "all16", "all17", "all18", "all19", "all20", "all21", "all22", "all23", "all24", "all25", "all26", "all27", "all28", "all29"], "target": "lianl2", "spacingMode": "percent", "spacing": 0.05}], "skins": [{"name": "default", "attachments": {"lian1": {"lian1": {"type": "mesh", "uvs": [7e-05, 0.06354, 0.01354, 0, 0.08384, 0.01324, 0.11376, 0.10863, 0.14197, 0.19904, 0.16462, 0.27165, 0.19287, 0.33404, 0.22744, 0.4104, 0.26274, 0.45614, 0.30373, 0.50925, 0.3686, 0.57457, 0.41741, 0.62371, 0.49193, 0.68323, 0.55204, 0.73124, 0.62553, 0.7836, 0.67619, 0.81969, 0.7521, 0.85249, 0.83392, 0.88785, 0.89886, 0.90141, 0.981, 0.91857, 1, 0.96773, 0.97702, 1, 0.89983, 0.99918, 0.83127, 0.99846, 0.75312, 0.9743, 0.69215, 0.95544, 0.6071, 0.91039, 0.53845, 0.87402, 0.46453, 0.81061, 0.40595, 0.76034, 0.33731, 0.69685, 0.27963, 0.6435, 0.21097, 0.56313, 0.15743, 0.50046, 0.11441, 0.41352, 0.07148, 0.32676, 0.04177, 0.2397, 0.02048, 0.1773, 0.0374, 0.04844, 0.07164, 0.15189, 0.12144, 0.30706, 0.19614, 0.45983, 0.29055, 0.58493, 0.41194, 0.69439, 0.54466, 0.80317, 0.68206, 0.88418, 0.83156, 0.94196, 0.97639, 0.96337, 0.09446, 0.223, 0.1572, 0.3802, 0.24116, 0.51949, 0.35185, 0.64021, 0.47588, 0.7468, 0.61089, 0.84222, 0.75656, 0.91297, 0.90422, 0.9527], "triangles": [22, 47, 21, 21, 47, 20, 47, 19, 20, 47, 55, 19, 22, 55, 47, 22, 23, 55, 46, 18, 55, 55, 18, 19, 23, 46, 55, 23, 24, 46, 46, 17, 18, 46, 54, 17, 24, 54, 46, 24, 25, 54, 45, 16, 54, 54, 16, 17, 26, 45, 25, 25, 45, 54, 45, 15, 16, 26, 53, 45, 26, 27, 53, 53, 15, 45, 53, 14, 15, 53, 44, 14, 27, 44, 53, 27, 28, 44, 44, 13, 14, 44, 52, 13, 28, 52, 44, 28, 29, 52, 52, 12, 13, 52, 43, 12, 29, 43, 52, 29, 30, 43, 43, 11, 12, 43, 51, 11, 30, 51, 43, 30, 31, 51, 51, 10, 11, 51, 42, 10, 31, 42, 51, 31, 32, 42, 42, 9, 10, 32, 50, 42, 42, 50, 9, 32, 33, 50, 50, 8, 9, 33, 41, 50, 50, 41, 8, 33, 34, 41, 41, 7, 8, 41, 49, 7, 34, 49, 41, 34, 40, 49, 49, 6, 7, 49, 40, 6, 34, 35, 40, 40, 5, 6, 35, 48, 40, 40, 48, 5, 35, 36, 48, 48, 4, 5, 48, 36, 39, 36, 37, 39, 48, 39, 4, 39, 3, 4, 39, 37, 0, 39, 38, 3, 39, 0, 38, 38, 2, 3, 0, 1, 38, 38, 1, 2], "vertices": [2, 79, 9.28, -8.84, 0.94355, 80, -7.82, -8.92, 0.05645, 1, 79, -2.97, -3.06, 1, 2, 79, 3.06, 12.93, 0.78225, 80, -12.07, 13.32, 0.21775, 4, 79, 23.84, 16.05, 0.01897, 80, 8.9, 14.57, 0.64287, 81, -6.27, 14.45, 0.32647, 82, -21.53, 15.44, 0.01169, 4, 80, 28.77, 15.72, 0.00405, 81, 13.63, 14.03, 0.38243, 82, -1.71, 13.6, 0.60437, 83, -17.13, 14.21, 0.00915, 3, 82, 14.2, 12.12, 0.40011, 83, -1.35, 11.69, 0.59226, 84, -16.93, 12.26, 0.00763, 2, 83, 13.24, 11.38, 0.48059, 84, -2.4, 11.03, 0.51941, 3, 84, 15.39, 9.53, 0.28739, 85, -0.38, 9.06, 0.70537, 86, -16.12, 9.59, 0.00724, 2, 85, 12.27, 10.34, 0.57172, 86, -3.42, 10.06, 0.42828, 3, 86, 11.32, 10.6, 0.60232, 87, -4.29, 10.37, 0.39689, 88, -19.77, 11.35, 0.00078, 3, 87, 16.24, 12.31, 0.2302, 88, 0.85, 11.65, 0.73756, 89, -14.3, 12.37, 0.03224, 3, 88, 16.36, 11.88, 0.26566, 89, 1.16, 11.03, 0.72136, 90, -14.57, 10.95, 0.01297, 3, 89, 22.87, 11.58, 0.01614, 90, 7.15, 10.71, 0.91599, 91, -9.28, 10.75, 0.06788, 3, 90, 24.67, 10.51, 0.04183, 91, 8.24, 10.76, 0.8086, 92, -7.81, 10.8, 0.14957, 3, 92, 12.88, 11.43, 0.47722, 93, -2.81, 11.2, 0.52251, 94, -18.25, 11.98, 0.00027, 3, 93, 11.46, 10.91, 0.5821, 94, -4.04, 10.66, 0.41674, 95, -19.39, 11.65, 0.00116, 3, 94, 15.3, 12.64, 0.3413, 95, 0.05, 12.01, 0.63012, 96, -15.17, 12.66, 0.02858, 3, 95, 21, 12.4, 0.04774, 96, 5.74, 11.24, 0.81649, 97, -9.61, 11.4, 0.13577, 3, 96, 21.48, 13, 0.05062, 97, 6.22, 11.84, 0.79037, 98, -9.21, 11.95, 0.15901, 1, 98, 10.79, 11, 1, 1, 98, 16.64, 1.5, 1, 1, 98, 12.05, -5.84, 1, 3, 96, 27.48, -6.33, 0.00757, 97, 10.6, -7.92, 0.92694, 98, -6.34, -8.09, 0.06549, 3, 95, 28.8, -9.14, 0.00148, 96, 11.66, -10.88, 0.84939, 97, -5.53, -11.14, 0.14913, 3, 94, 26.68, -9.86, 0.06825, 95, 9.52, -11.36, 0.78629, 96, -7.74, -11.44, 0.14546, 3, 93, 28.97, -11.4, 0.03427, 94, 11.83, -12.84, 0.75373, 95, -5.53, -13.09, 0.212, 3, 92, 23.6, -12.94, 0.1202, 93, 6.65, -13.68, 0.80903, 94, -10.6, -13.51, 0.07077, 3, 91, 22.34, -15.42, 0.16003, 92, 5.69, -15.69, 0.76789, 93, -11.37, -15.51, 0.07208, 4, 89, 33.05, -13.61, 0.01965, 90, 16.4, -14.84, 0.31411, 91, 0.28, -14.69, 0.64687, 92, -16.34, -14.46, 0.01938, 4, 88, 33.16, -11.04, 0.0018, 89, 15.56, -13.46, 0.54441, 90, -1.07, -14.05, 0.44192, 91, -17.2, -14.11, 0.01187, 3, 87, 29.38, -10.59, 0.00918, 88, 12.12, -12.22, 0.73788, 89, -5.49, -12.52, 0.25295, 3, 86, 29.01, -11.6, 0.01395, 87, 11.83, -12.99, 0.7184, 88, -5.56, -13.22, 0.26765, 3, 85, 22.85, -12.75, 0.1852, 86, 5.68, -13.65, 0.75865, 87, -11.58, -13.44, 0.05615, 3, 84, 22.06, -14.67, 0.19529, 85, 4.8, -15.49, 0.75015, 86, -12.51, -15.25, 0.05457, 3, 83, 18.52, -13.06, 0.33204, 84, 1.34, -13.69, 0.6428, 85, -15.83, -13.24, 0.02516, 3, 81, 32.21, -11.27, 0.001, 82, 15.02, -12.96, 0.53268, 83, -2.19, -13.39, 0.46632, 3, 80, 29.98, -9.73, 0.00679, 81, 12.83, -11.43, 0.71116, 82, -4.33, -11.74, 0.28205, 2, 80, 16.14, -10.94, 0.46311, 81, -1.06, -11.55, 0.53689, 2, 79, 7.99, 0.56, 0.98738, 80, -8.27, 0.56, 0.01262, 3, 80, 14.6, 2.33, 0.30832, 81, -1.55, 1.8, 0.68839, 82, -17.73, 2.48, 0.00329, 2, 82, 16.43, -0.37, 0.02061, 83, 0.05, -0.92, 0.97939, 2, 84, 19.97, -2.31, 0.06218, 85, 3.47, -3.03, 0.93782, 2, 86, 21.36, -1.84, 0.0127, 87, 4.86, -2.73, 0.9873, 1, 89, 8.7, -1.58, 1, 2, 91, 15.24, -2.5, 0.32457, 92, -1.11, -2.62, 0.67543, 2, 93, 19.42, 0.1, 0.00409, 94, 3.13, -0.68, 0.99591, 1, 96, 8.39, 0.35, 1, 1, 98, 10.91, 1.66, 1, 2, 81, 14.15, 1.61, 0.46816, 82, -2.07, 1.17, 0.53184, 2, 83, 17.45, -0.74, 0.01857, 84, 1.05, -1.32, 0.98143, 2, 85, 19.82, -1.57, 0.03606, 86, 3.36, -2.3, 0.96394, 2, 87, 23.3, 0.02, 0.00293, 88, 6.91, -1.16, 0.99707, 3, 89, 27.49, -1.33, 0.00886, 90, 11.29, -2.36, 0.94699, 91, -4.98, -2.27, 0.04415, 1, 93, 0.3, -1.04, 1, 2, 95, 5.63, 0.75, 0.99369, 96, -10.57, 0.96, 0.00631, 2, 97, 9.66, 1.72, 0.96292, 98, -6.55, 1.59, 0.03708], "hull": 38, "edges": [0, 2, 2, 4, 4, 6, 38, 40, 40, 42, 0, 74, 2, 76, 76, 78, 78, 96, 96, 80, 70, 72, 72, 74, 6, 8, 8, 10, 66, 68, 68, 70, 80, 98, 98, 82, 10, 12, 12, 14, 14, 16, 16, 18, 82, 100, 100, 84, 62, 64, 64, 66, 18, 20, 20, 22, 84, 102, 102, 86, 58, 60, 60, 62, 22, 24, 24, 26, 86, 104, 104, 88, 54, 56, 56, 58, 26, 28, 28, 30, 88, 106, 106, 90, 50, 52, 52, 54, 30, 32, 32, 34, 90, 108, 108, 92, 46, 48, 48, 50, 34, 36, 36, 38, 92, 110, 110, 94, 42, 44, 44, 46], "width": 240, "height": 207}}, "lian2": {"lian2": {"type": "mesh", "uvs": [0, 0.47055, 0, 0.44318, 0.03998, 0.41262, 0.11612, 0.43681, 0.12386, 0.46737, 0.13677, 0.52658, 0.15742, 0.58261, 0.20258, 0.65391, 0.27098, 0.7144, 0.35115, 0.77097, 0.47298, 0.82786, 0.59917, 0.87723, 0.7479, 0.91301, 0.91372, 0.93831, 0.98381, 0.95349, 1, 1, 0.96158, 1, 0.88124, 1, 0.72397, 0.97795, 0.57183, 0.9459, 0.4402, 0.90711, 0.28977, 0.85651, 0.20429, 0.79748, 0.12395, 0.72832, 0.05557, 0.66339, 0.01454, 0.58665, 0.00087, 0.51834, 0.04878, 0.44188, 0.05918, 0.46908, 0.06751, 0.52452, 0.09248, 0.58561, 0.13618, 0.6621, 0.20902, 0.72318, 0.28602, 0.78427, 0.39632, 0.83766, 0.50751, 0.88462, 0.64077, 0.92268, 0.79135, 0.95299, 0.89752, 0.96787, 0.96096, 0.98776], "triangles": [25, 26, 29, 30, 29, 5, 29, 4, 5, 24, 25, 30, 31, 30, 6, 25, 29, 30, 30, 5, 6, 23, 31, 32, 23, 24, 31, 24, 30, 31, 31, 6, 7, 22, 32, 33, 22, 23, 32, 31, 7, 32, 32, 7, 8, 22, 33, 21, 21, 33, 34, 32, 8, 33, 33, 8, 9, 21, 34, 20, 20, 34, 35, 33, 9, 34, 34, 9, 10, 20, 35, 19, 19, 35, 36, 34, 10, 35, 35, 10, 11, 19, 36, 18, 18, 36, 37, 35, 11, 36, 36, 11, 12, 18, 37, 17, 17, 37, 38, 36, 12, 37, 37, 12, 13, 15, 39, 14, 17, 39, 16, 15, 16, 39, 17, 38, 39, 39, 38, 14, 38, 13, 14, 38, 37, 13, 26, 28, 29, 29, 28, 4, 26, 0, 28, 0, 27, 28, 0, 1, 27, 4, 28, 3, 28, 27, 3, 1, 2, 27, 27, 2, 3], "vertices": [2, 56, 8.27, -8.99, 0.86691, 57, -3.05, -8.91, 0.13309, 2, 56, 0.22, -8.76, 0.99442, 57, -11.09, -8.5, 0.00558, 2, 56, -8.6, -2.59, 0.99999, 57, -19.77, -2.13, 1e-05, 2, 56, -1.17, 8.47, 0.94357, 57, -12.1, 8.76, 0.05643, 3, 56, 7.85, 9.36, 0.61241, 58, -13.61, 9.98, 0.05149, 57, -3.07, 9.44, 0.3361, 4, 60, -17.22, 10.83, 0.00861, 59, -6.69, 9.98, 0.27214, 58, 3.91, 9.88, 0.7121, 57, 14.42, 10.46, 0.00714, 4, 62, -21.47, 11.38, 0.00344, 61, -11.04, 10.1, 0.13377, 60, -0.51, 9.67, 0.65905, 59, 10.05, 10, 0.20374, 4, 64, -20.53, 10.96, 0.00444, 63, -10.12, 9.66, 0.19156, 62, 0.4, 9.26, 0.66205, 61, 10.93, 9.7, 0.14195, 4, 66, -21.07, 11.56, 0.00305, 65, -10.51, 10.34, 0.16295, 64, -0.19, 9.88, 0.71621, 63, 10.24, 10.3, 0.11779, 4, 68, -21.99, 10.8, 0.0003, 67, -11.45, 9.68, 0.1292, 66, -0.9, 9.25, 0.69454, 65, 9.78, 9.63, 0.17596, 4, 70, -18.9, 10.81, 0.01138, 69, -8.31, 9.94, 0.25662, 68, 2.32, 9.74, 0.68623, 67, 12.89, 10.26, 0.04577, 3, 72, -16.97, 9.62, 0.02852, 71, -6.3, 8.9, 0.14808, 70, 4.39, 8.85, 0.8234, 4, 75, -24.71, 11.8, 0.00011, 74, -14.2, 10.41, 0.06078, 73, -3.59, 9.84, 0.37279, 72, 7.04, 9.95, 0.56632, 2, 75, 0.46, 11.17, 0.78943, 74, 10.96, 11.56, 0.21057, 1, 75, 11.46, 9.61, 1, 1, 75, 17.43, -3.17, 1, 2, 75, 12.07, -4.71, 0.94582, 74, 23.61, -3.46, 0.05418, 2, 74, 12.64, -7.48, 0.81909, 73, 24.29, -6.31, 0.18091, 3, 72, 12.53, -9.04, 0.7697, 71, 24.24, -7.92, 0.22723, 70, 35.87, -6.05, 0.00306, 4, 71, 0.41, -10.94, 0.05018, 70, 12.27, -10.54, 0.65233, 69, 24.12, -9.38, 0.28425, 68, 35.9, -7.4, 0.01325, 4, 69, 2.01, -11.92, 0.03587, 68, 14, -11.39, 0.69403, 67, 25.9, -10.04, 0.25153, 66, 37.68, -7.84, 0.01857, 4, 67, -0.08, -14.94, 0.01625, 66, 12.08, -14.52, 0.66374, 65, 24.52, -13.05, 0.27945, 64, 36.64, -10.44, 0.04055, 4, 65, 3.19, -12.53, 0.12826, 64, 15.34, -11.73, 0.69495, 63, 27.47, -9.93, 0.17475, 62, 39.41, -7.19, 0.00204, 4, 63, 4.05, -10.94, 0.13921, 62, 16.14, -10.12, 0.70517, 61, 28.08, -8.39, 0.15212, 60, 39.83, -5.86, 0.0035, 4, 61, 6.82, -11.85, 0.30342, 60, 18.87, -10.89, 0.61698, 59, 30.8, -9.13, 0.07959, 58, 42.56, -6.66, 1e-05, 3, 59, 7.52, -11.02, 0.46553, 58, 19.46, -10.11, 0.49381, 57, 31.16, -8.52, 0.04065, 3, 56, 22.32, -9.26, 0.36227, 58, -0.73, -9.83, 0.00021, 57, 10.99, -9.51, 0.63753, 1, 56, 0.04, -1.53, 1, 1, 56, 8.08, -0.22, 1, 3, 56, 24.42, 0.55, 0.00028, 58, 2.17, -0.23, 0.67065, 57, 13.3, 0.25, 0.32906, 3, 62, -24.31, 2.17, 1e-05, 61, -13.18, 0.7, 0.00286, 59, 9.24, 0.39, 0.99713, 3, 61, 10.16, -0.4, 0.98323, 60, 21.39, 0.77, 0.01619, 59, 32.51, 2.68, 0.00058, 3, 65, -13.85, 1.42, 0.00634, 64, -2.79, 0.71, 0.05246, 63, 8.39, 0.95, 0.9412, 3, 65, 7.22, -0.48, 0.93082, 64, 18.37, 0.62, 0.06741, 63, 29.49, 2.64, 0.00177, 3, 69, -15.42, 0.73, 0.00058, 68, -4.19, 0.08, 0.00877, 67, 7.01, 0.18, 0.99065, 4, 69, 5.78, -0.51, 0.9764, 68, 17.05, 0.24, 0.02037, 67, 28.19, 1.77, 0.0032, 66, 39.18, 4.09, 3e-05, 3, 73, -16.74, 0.9, 2e-05, 72, -5.56, 0.21, 0.01437, 71, 5.63, 0.22, 0.9856, 3, 75, -15.46, 2, 0.00212, 74, -4.31, 1.3, 0.1202, 73, 6.85, 1.37, 0.87768, 1, 75, 0.56, 1.98, 1, 2, 75, 11, -1.2, 0.98084, 74, 22.31, -0.04, 0.01916], "hull": 27, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 0, 52, 4, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 30, 78, 78, 76], "width": 148, "height": 300}}, "lian11": {"lian11": {"type": "path", "lengths": [110.06, 231.08, 435.76], "vertexCount": 9, "vertices": [3, 53, -120.45, 144.7, 0.296, 55, -14.45, 107.88, 0.49, 54, -29.23, -13.32, 0.214, 1, 54, -2.93, 1.42, 1, 1, 54, -12.61, 4.33, 1, 3, 53, -140.86, 103.22, 0.07543, 55, -34.85, 66.4, 0.04438, 54, -60.62, 19.63, 0.88019, 2, 55, 6.05, 20.84, 0.17181, 54, -116.46, -3.77, 0.82819, 2, 53, -59.46, 7.03, 0.69501, 55, 46.55, -29.78, 0.30499, 2, 53, -17.97, -4.76, 0.80859, 55, 88.03, -41.58, 0.19141, 1, 53, -3.66, -1.62, 1, 3, 53, 17.64, -20.5, 0.296, 55, 123.65, -57.32, 0.49, 54, -228.25, -88.56, 0.214]}}, "toufa": {"toufa": {"type": "mesh", "uvs": [0.51631, 0.0026, 0.66866, 0.02671, 0.77848, 0.13866, 0.82631, 0.3126, 0.86528, 0.41077, 0.97688, 0.4986, 1, 0.62949, 0.97334, 0.73799, 0.86351, 0.84304, 0.72534, 0.93432, 0.5482, 0.97049, 0.43837, 1, 0.25591, 0.95499, 0.03449, 0.86716, 0, 0.7001, 0.03271, 0.56405, 0.13723, 0.42455, 0.20277, 0.24716, 0.2754, 0.08527, 0.38877, 0.01983, 0.49683, 0.1266, 0.47203, 0.30571, 0.45431, 0.53132, 0.43837, 0.78793, 0.61374, 0.13005, 0.65448, 0.30227, 0.68106, 0.47794, 0.7218, 0.64671, 0.72534, 0.81549, 0.8458, 0.53305, 0.87414, 0.69149, 0.38169, 0.11455, 0.33917, 0.29021, 0.26123, 0.47966, 0.20809, 0.63293, 0.21517, 0.79654], "triangles": [9, 10, 28, 10, 23, 28, 9, 28, 8, 8, 30, 7, 8, 28, 30, 23, 27, 28, 28, 27, 30, 7, 30, 6, 30, 29, 6, 23, 22, 27, 27, 29, 30, 22, 26, 27, 27, 26, 29, 29, 5, 6, 29, 4, 5, 29, 26, 4, 22, 21, 26, 21, 25, 26, 21, 24, 25, 26, 3, 4, 26, 25, 3, 25, 2, 3, 21, 20, 24, 25, 24, 2, 24, 1, 2, 1, 24, 0, 24, 20, 0, 20, 19, 0, 10, 11, 23, 11, 12, 23, 13, 35, 12, 12, 35, 23, 13, 14, 35, 14, 34, 35, 35, 34, 23, 23, 34, 22, 22, 34, 33, 14, 15, 34, 15, 16, 34, 34, 16, 33, 33, 32, 22, 22, 32, 21, 33, 16, 32, 32, 16, 17, 32, 31, 21, 21, 31, 20, 17, 18, 32, 32, 18, 31, 31, 19, 20, 31, 18, 19], "vertices": [2, 12, -25.46, 7.23, 0.3457, 15, -19.33, -2.46, 0.6543, 2, 12, -28.41, 17.62, 0.10822, 15, -12.61, 5.99, 0.89178, 2, 12, -24.38, 28.01, 0.00214, 15, -1.82, 8.77, 0.99786, 1, 15, 10.74, 5.57, 1, 2, 15, 18.24, 4.5, 0.51619, 16, 0.4, 4.82, 0.48381, 3, 15, 27.57, 8.23, 0.00088, 16, 10.16, 7.23, 0.96501, 17, -6.12, 5.04, 0.03411, 2, 16, 18.65, 2.83, 0.02792, 17, 3.44, 4.86, 0.97208, 4, 13, 2.82, 59.16, 0, 14, -8.62, 59.56, 0, 16, 23.75, -3.38, 0, 17, 10.76, 1.56, 0.99999, 4, 13, 12.07, 53.61, 0.03982, 14, 0.4, 53.63, 0.04217, 16, 25.14, -14.07, 0.06286, 17, 16.75, -7.42, 0.85515, 5, 12, 29.08, 48.94, 0, 13, 20.85, 45.88, 0.11421, 14, 8.86, 45.56, 0.14113, 16, 24.54, -25.75, 0.12728, 17, 21.39, -18.15, 0.61737, 5, 12, 36.7, 38.82, 0.0005, 13, 26.47, 34.53, 0.18296, 14, 14.02, 33.99, 0.30697, 16, 19.12, -37.21, 0.12793, 17, 21.61, -30.82, 0.38163, 4, 13, 30.45, 27.61, 0.19371, 14, 17.71, 26.92, 0.43272, 16, 16.18, -44.62, 0.10279, 17, 22.26, -38.77, 0.27078, 4, 13, 30.5, 14.44, 0.13678, 14, 17.24, 13.75, 0.70111, 16, 5.88, -52.84, 0.04885, 17, 16.67, -50.71, 0.11326, 1, 14, 14.32, -2.73, 1, 2, 13, 17.21, -7.49, 0.33533, 14, 3.07, -7.62, 0.66467, 2, 13, 7.15, -7.72, 0.99001, 14, -6.99, -7.45, 0.00999, 2, 12, 13.31, -3.91, 0.9226, 13, -4.4, -3.15, 0.0774, 2, 12, -0.2, -5.17, 0.99701, 15, -14.69, -30.22, 0.00299, 2, 12, -12.92, -5.52, 0.79491, 15, -22.38, -20.09, 0.20509, 2, 12, -20.55, -0.33, 0.58372, 15, -22.61, -10.86, 0.41628, 3, 12, -16.8, 9.78, 0.39931, 15, -12.21, -8.02, 0.59995, 16, -31.48, -3.38, 0.00074, 5, 12, -4.38, 13.68, 0.50098, 13, -18.55, 17.4, 0.02411, 15, -1.8, -15.83, 0.42793, 16, -22.25, -12.55, 0.04356, 17, -26.4, -27.06, 0.00341, 6, 12, 10.85, 19.45, 0.30629, 13, -2.51, 20.26, 0.25223, 14, -15.51, 20.9, 0.0363, 15, 11.76, -24.84, 0.14804, 16, -10.05, -23.35, 0.18415, 17, -10.68, -31.33, 0.07299, 6, 12, 28.06, 26.28, 0.02815, 13, 15.66, 23.8, 0.2856, 14, 2.79, 23.71, 0.31066, 15, 27.34, -34.84, 0.0093, 16, 4, -35.4, 0.1399, 17, 7.26, -35.9, 0.22639, 2, 12, -20.05, 17.3, 0.13639, 15, -8, -1, 0.86361, 6, 12, -10.03, 25.14, 0.06055, 13, -21.98, 29.71, 0.00688, 14, -34.58, 31.13, 1e-05, 15, 4.22, -4.56, 0.90249, 16, -14.73, -2.22, 0.0292, 17, -24.24, -14.47, 0.00086, 6, 12, 0.64, 32.19, 0.06395, 13, -10.19, 34.67, 0.04637, 14, -22.61, 35.61, 0.00902, 15, 16.16, -9.12, 0.36667, 16, -3.53, -8.38, 0.46407, 17, -11.47, -15.02, 0.04992, 6, 12, 10.43, 39.93, 0.02484, 13, 0.86, 40.47, 0.0733, 14, -11.33, 40.96, 0.04061, 15, 28.16, -12.56, 0.02379, 16, 7.88, -13.44, 0.47008, 17, 1, -14.5, 0.36738, 6, 12, 21.33, 45.31, 0.00451, 13, 12.56, 43.74, 0.09657, 14, 0.5, 43.76, 0.10071, 15, 38.88, -18.28, 0.00029, 16, 17.72, -20.58, 0.16194, 17, 12.98, -16.54, 0.63597, 5, 12, -0.66, 44.32, 0.00109, 13, -9.23, 46.83, 0.00256, 14, -21.16, 47.72, 0.00099, 16, 6.6, -1.58, 0.97447, 17, -5.41, -4.44, 0.0209, 5, 12, 8.83, 50.96, 0.00098, 13, 1.32, 51.6, 0.01073, 14, -10.43, 52.06, 0.00805, 16, 16.89, -6.89, 0.1095, 17, 6.17, -4.64, 0.87074, 3, 12, -14.16, 2.12, 0.66239, 15, -16.9, -14.62, 0.33744, 16, -37.04, -9.28, 0.00017, 5, 12, -1.45, 4.79, 0.87281, 13, -17.3, 8.12, 0.007, 15, -7.32, -23.4, 0.10866, 16, -28.75, -19.29, 0.01077, 17, -29.24, -35.99, 0.00075, 6, 12, 13.22, 5.63, 0.69359, 13, -2.73, 6.25, 0.25478, 14, -16.3, 6.91, 0.00041, 15, 1.92, -34.82, 0.02021, 16, -21.18, -31.88, 0.0226, 17, -16.87, -43.91, 0.00841, 6, 12, 24.79, 6.95, 0.01782, 13, 8.88, 5.4, 0.88284, 14, -4.73, 5.6, 0.06292, 15, 9.73, -43.46, 0.0036, 16, -14.63, -41.51, 0.01774, 17, -6.73, -49.64, 0.01508, 6, 12, 35.25, 12.4, 0.00069, 13, 20.17, 8.83, 0.19857, 14, 6.69, 8.56, 0.72701, 15, 20.25, -48.78, 0.00043, 16, -4.94, -48.23, 0.02679, 17, 4.93, -51.36, 0.0465], "hull": 20, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 0, 38, 0, 40, 40, 42, 42, 44, 44, 46, 48, 50, 50, 52, 52, 54, 54, 56, 58, 60, 62, 64, 64, 66, 66, 68, 68, 70], "width": 70, "height": 72}}, "shou1": {"shou1": {"type": "mesh", "uvs": [0.85429, 0.91524, 0.85684, 0.78665, 0.76025, 0.70916, 0.6323, 0.68057, 0.59375, 0.67196, 0.57091, 0.64867, 0.48584, 0.56196, 0.59889, 0.47529, 0.60095, 0.42796, 0.79143, 0.48535, 1, 0.39425, 1, 0.20779, 0.86385, 0, 0.59832, 0, 0.38985, 0.04267, 0.16601, 0.18786, 0.12652, 0.33305, 0, 0.57645, 0, 0.63908, 0.10238, 0.67324, 0.28013, 0.61488, 0.38415, 0.76579, 0.40082, 0.78996, 0.41692, 0.80851, 0.4886, 0.89103, 0.63782, 0.98924, 0.77168, 0.98213, 0.39308, 0.58475, 0.51775, 0.73545, 0.70286, 0.85675, 0.53953, 0.74972, 0.49874, 0.71247], "triangles": [20, 19, 17, 19, 18, 17, 27, 20, 16, 16, 20, 17, 7, 6, 8, 9, 8, 10, 16, 8, 27, 16, 15, 8, 15, 14, 8, 14, 13, 8, 10, 8, 11, 11, 8, 13, 11, 13, 12, 31, 20, 27, 31, 21, 20, 31, 27, 5, 31, 5, 4, 5, 27, 6, 8, 6, 27, 25, 29, 26, 25, 24, 29, 26, 29, 0, 0, 29, 1, 24, 30, 29, 24, 23, 30, 29, 30, 2, 29, 2, 1, 2, 30, 3, 23, 28, 30, 23, 22, 28, 22, 21, 28, 21, 31, 28, 30, 4, 3, 30, 28, 4, 28, 31, 4], "vertices": [1, 22, -11.28, -2.57, 1, 1, 22, -1.43, -12.9, 1, 2, 22, 9.55, -14.19, 0.99889, 23, -8.03, -17.18, 0.00111, 2, 22, 18.36, -10.02, 0.81261, 23, -0.81, -10.63, 0.18739, 2, 22, 21.01, -8.77, 0.55576, 23, 1.36, -8.66, 0.44424, 2, 22, 24, -9.47, 0.23816, 23, 4.42, -8.46, 0.76184, 2, 23, 15.8, -7.72, 0.81658, 24, -4.7, -8.69, 0.18342, 2, 23, 20.32, -19.49, 0.20686, 24, 1.48, -19.67, 0.79314, 2, 23, 24.85, -22.14, 0.09017, 24, 6.36, -21.63, 0.90983, 2, 23, 12.67, -31.1, 0.01264, 24, -4.38, -32.28, 0.98736, 2, 23, 14.31, -49.13, 0.00112, 24, -0.13, -49.87, 0.99888, 1, 24, 19.28, -57.07, 1, 1, 24, 44.31, -55.9, 1, 1, 24, 50.96, -37.98, 1, 1, 24, 51.74, -22.26, 1, 1, 24, 42.24, -1.54, 1, 1, 24, 28.12, 6.73, 1, 1, 24, 5.95, 24.67, 1, 1, 24, -0.56, 27.08, 1, 2, 23, 18.25, 22.43, 0.01263, 24, -6.68, 21.49, 0.98737, 2, 23, 17.77, 8.09, 0.48781, 24, -5.06, 7.24, 0.51219, 2, 22, 24.53, 9.23, 0.42384, 23, -0.51, 9.58, 0.57616, 2, 22, 21.79, 10.31, 0.64861, 23, -3.44, 9.82, 0.35139, 2, 22, 19.52, 10.97, 0.78704, 23, -5.81, 9.79, 0.21296, 2, 22, 9.43, 13.92, 0.99791, 23, -16.32, 9.67, 0.00209, 1, 22, -5.87, 14.2, 1, 1, 22, -12.21, 6.9, 1, 1, 23, 16.79, -0.65, 1, 1, 22, 20, 0.1, 1, 1, 22, 1.06, 0.41, 1, 2, 22, 17.77, 0.13, 0.99964, 23, -4.33, -1.09, 0.00036, 2, 22, 22.76, -0.77, 0.27237, 23, 0.71, -0.5, 0.72763], "hull": 27, "edges": [0, 2, 2, 4, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 48, 50, 50, 52, 0, 52, 4, 6, 6, 8, 56, 60, 60, 58, 6, 60, 44, 46, 46, 48, 60, 46, 40, 42, 42, 44, 54, 62, 62, 56, 42, 62, 8, 10, 10, 12, 62, 10], "width": 72, "height": 111}}, "shou2": {"shou2": {"type": "mesh", "uvs": [0.0279, 0.04924, 0.18485, 0, 0.41853, 0.02982, 0.58595, 0.12692, 0.63478, 0.25933, 0.70104, 0.37055, 0.72197, 0.41822, 0.76034, 0.45882, 0.85102, 0.56828, 0.87543, 0.69539, 0.96612, 0.8172, 1, 0.91607, 0.81614, 1, 0.57548, 0.99551, 0.43946, 0.9143, 0.38365, 0.82427, 0.47782, 0.70422, 0.3697, 0.60712, 0.30343, 0.51355, 0.27553, 0.46588, 0.17787, 0.43764, 0.04185, 0.32995, 0.03487, 0.22755, 0, 0.15164, 0.30778, 0.13778, 0.42729, 0.28346, 0.50778, 0.40691, 0.53705, 0.44889, 0.56388, 0.4921, 0.64193, 0.60321, 0.69559, 0.70691, 0.69559, 0.85629], "triangles": [28, 27, 6, 28, 6, 7, 18, 27, 28, 29, 28, 7, 29, 7, 8, 17, 18, 28, 17, 28, 29, 29, 8, 9, 16, 17, 29, 30, 29, 9, 16, 29, 30, 10, 31, 30, 10, 30, 9, 15, 31, 14, 16, 31, 15, 30, 31, 16, 31, 10, 11, 13, 14, 31, 12, 31, 11, 13, 31, 12, 24, 1, 2, 24, 2, 3, 0, 1, 24, 23, 0, 24, 22, 23, 24, 25, 24, 3, 25, 3, 4, 22, 24, 25, 21, 22, 25, 26, 25, 4, 26, 4, 5, 26, 5, 6, 20, 21, 25, 19, 20, 25, 27, 26, 6, 26, 19, 25, 19, 26, 27, 18, 19, 27], "vertices": [1, 25, -11.45, -6.6, 1, 1, 25, -12.9, 0.83, 1, 1, 25, -7.24, 8.93, 1, 1, 25, 2.54, 12.56, 1, 1, 25, 13.28, 10.63, 1, 2, 25, 22.67, 9.98, 0.85518, 26, -4.39, 9.8, 0.14482, 2, 25, 26.58, 9.41, 0.54269, 26, -0.46, 9.4, 0.45731, 2, 25, 30.22, 9.72, 0.22101, 26, 3.16, 9.87, 0.77899, 2, 25, 39.82, 10.05, 0.00027, 26, 12.74, 10.62, 0.99973, 1, 26, 22.84, 8.34, 1, 1, 26, 33.37, 8.78, 1, 1, 26, 41.41, 7.59, 1, 1, 26, 45.51, -1.69, 1, 1, 26, 42.07, -10.95, 1, 1, 26, 34.07, -14.19, 1, 1, 26, 26.43, -14.07, 1, 1, 26, 18.41, -7.36, 1, 2, 25, 35.77, -9.52, 0.01893, 26, 9.55, -9.11, 0.98107, 2, 25, 27.72, -9.37, 0.40873, 26, 1.5, -9.31, 0.59127, 2, 25, 23.7, -9.07, 0.79955, 26, -2.52, -9.19, 0.20045, 2, 25, 20.14, -12, 0.97229, 26, -5.95, -12.27, 0.02771, 1, 25, 10.01, -14.12, 1, 1, 25, 2.15, -11.45, 1, 1, 25, -4.1, -10.61, 1, 1, 25, -0.68, 1.59, 1, 1, 25, 12.09, 1.99, 1, 2, 25, 22.61, 1.53, 0.99434, 26, -4.08, 1.35, 0.00566, 2, 25, 26.22, 1.44, 0.68159, 26, -0.47, 1.42, 0.31841, 2, 25, 29.88, 1.23, 0.01371, 26, 3.2, 1.37, 0.98629, 1, 26, 12.75, 1.59, 1, 1, 26, 21.41, 1.05, 1, 1, 26, 32.9, -2.74, 1], "hull": 24, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 0, 46, 2, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62], "width": 41, "height": 81}}, "tou": {"tou": {"type": "mesh", "uvs": [0.01235, 0.30939, 0.07891, 0.16806, 0.23668, 0.04692, 0.44868, 0, 0.70751, 0.01327, 0.89486, 0.09908, 1, 0.21685, 1, 0.34978, 0.98086, 0.43452, 0.95896, 0.53149, 0.88254, 0.59206, 0.86282, 0.7233, 0.80612, 0.86968, 0.64096, 0.94876, 0.43882, 1, 0.24407, 0.94203, 0.07398, 0.78724, 0.02715, 0.69458, 0, 0.64085, 0.01482, 0.49447, 0.38212, 0.14451, 0.75189, 0.17984, 0.34514, 0.29257, 0.74942, 0.30771, 0.56509, 0.30081, 0.58376, 0.16377, 0.20956, 0.16301, 0.17751, 0.28416, 0.32542, 0.444, 0.36979, 0.6173, 0.54728, 0.45073, 0.74449, 0.47765, 0.143, 0.46082, 0.59165, 0.62066, 0.17751, 0.62403, 0.37719, 0.7519, 0.4117, 0.88314, 0.68286, 0.77041, 0.20217, 0.77378], "triangles": [35, 33, 37, 12, 37, 11, 36, 35, 37, 13, 36, 37, 13, 37, 12, 14, 36, 13, 37, 33, 11, 38, 34, 35, 38, 35, 36, 15, 38, 36, 16, 38, 15, 15, 36, 14, 16, 17, 38, 17, 18, 34, 17, 34, 38, 35, 29, 33, 34, 28, 29, 22, 26, 20, 22, 20, 24, 28, 27, 22, 28, 22, 30, 29, 28, 30, 18, 19, 34, 33, 31, 10, 9, 31, 8, 8, 23, 7, 31, 23, 8, 33, 30, 31, 20, 2, 3, 26, 2, 20, 25, 3, 4, 21, 25, 4, 20, 3, 25, 26, 1, 2, 5, 21, 4, 21, 5, 6, 27, 1, 26, 27, 26, 22, 24, 20, 25, 24, 25, 21, 23, 24, 21, 23, 21, 6, 0, 1, 27, 23, 6, 7, 30, 22, 24, 32, 0, 27, 32, 27, 28, 23, 30, 24, 31, 30, 23, 19, 0, 32, 29, 30, 33, 34, 32, 28, 10, 31, 9, 19, 32, 34, 11, 33, 10, 34, 29, 35], "vertices": [2, 5, 10.23, 19.97, 0.808, 7, -1.26, 15.7, 0.192, 2, 5, 19.5, 18.74, 0.808, 7, 8.01, 14.48, 0.192, 2, 5, 28.22, 13.43, 0.808, 7, 16.74, 9.17, 0.192, 2, 5, 32.76, 4.99, 0.808, 7, 21.28, 0.73, 0.192, 2, 5, 33.93, -6.11, 0.808, 7, 22.44, -10.37, 0.192, 2, 5, 30.05, -15, 0.808, 7, 18.57, -19.26, 0.192, 2, 5, 23.56, -20.78, 0.808, 7, 12.08, -25.04, 0.192, 2, 5, 15.32, -22.27, 0.808, 7, 3.84, -26.54, 0.192, 2, 5, 9.92, -22.42, 0.808, 7, -1.56, -26.68, 0.192, 2, 5, 3.74, -22.58, 0.808, 7, -7.74, -26.85, 0.192, 3, 5, -0.6, -20.03, 0.62258, 8, 0.41, 9.3, 0.18543, 7, -12.08, -24.3, 0.192, 3, 8, 8.68, 8.45, 0.62626, 9, -4.43, 7.29, 0.18174, 7, -20.37, -24.94, 0.192, 3, 8, 17.9, 6.01, 0.03601, 9, 5, 8.71, 0.77199, 7, -29.88, -24.19, 0.192, 3, 11, 14.66, 15.81, 0.00166, 9, 12.39, 4.17, 0.80634, 7, -36.05, -18.1, 0.192, 3, 11, 16.67, 6.76, 0.30471, 9, 18.81, -2.53, 0.50329, 7, -40.78, -10.12, 0.192, 3, 11, 11.9, -1.03, 0.77369, 9, 18.78, -11.66, 0.03431, 7, -38.69, -1.23, 0.192, 3, 10, 9.84, -6.91, 0.34166, 11, 1.24, -6.94, 0.46634, 7, -30.4, 7.71, 0.192, 3, 10, 3.81, -8.25, 0.76537, 11, -4.82, -8.14, 0.04263, 7, -25.02, 10.73, 0.192, 4, 5, -10.41, 16.75, 0.00932, 10, 0.31, -9.04, 0.79717, 11, -8.33, -8.83, 0.0015, 7, -21.9, 12.49, 0.192, 2, 5, -1.22, 17.78, 0.808, 7, -12.71, 13.51, 0.192, 2, 5, 23.29, 6.18, 0.72, 6, 21.68, 2.3, 0.28, 2, 5, 23.95, -9.86, 0.816, 6, 22.33, -13.74, 0.184, 2, 5, 13.83, 6.08, 0.72, 6, 12.22, 2.2, 0.28, 2, 5, 16, -11.2, 0.816, 6, 14.39, -15.08, 0.184, 2, 5, 15.01, -3.32, 0.72, 6, 13.4, -7.2, 0.28, 2, 5, 23.65, -2.57, 0.72, 6, 22.04, -6.45, 0.28, 2, 5, 20.82, 13.27, 0.8, 6, 19.21, 9.39, 0.2, 2, 5, 13.06, 13.26, 0.8, 6, 11.45, 9.38, 0.2, 2, 5, 4.29, 5.2, 0.72, 6, 2.68, 1.32, 0.28, 6, 5, -6.11, 1.37, 0.59508, 10, 0.62, 6.93, 0.09736, 11, -7.62, 7.13, 0.00157, 8, 2, -12.75, 0.01973, 9, -2.15, -14.82, 0.00625, 6, -7.72, -2.51, 0.28, 2, 5, 5.58, -4.26, 0.72, 6, 3.97, -8.14, 0.28, 2, 5, 5.43, -12.9, 0.816, 6, 3.82, -16.78, 0.184, 1, 5, 1.85, 12.73, 1, 6, 5, -4.61, -8.05, 0.60375, 10, 1.91, 16.39, 0.00665, 11, -6.1, 16.55, 0.00087, 8, 2.21, -3.21, 0.20101, 9, -5.74, -5.98, 0.00373, 6, -6.22, -11.93, 0.184, 3, 5, -8, 9.43, 0.6528, 10, 0.12, -1.33, 0.1472, 6, -9.62, 5.55, 0.2, 6, 5, -14.39, -0.46, 0.03513, 10, 9.09, 6.3, 0.19969, 11, 0.82, 6.28, 0.2788, 8, 10.48, -12.43, 0.05274, 9, 5.51, -11.16, 0.15364, 6, -16.01, -4.34, 0.28, 5, 5, -22.26, -3.4, 0.00061, 10, 17.47, 6.84, 0.00146, 11, 9.21, 6.61, 0.5161, 8, 18.75, -10.95, 0.00131, 9, 12.51, -6.52, 0.48051, 3, 8, 11.65, 0.71, 0.03672, 9, 1.36, 1.36, 0.77929, 6, -14.8, -17.48, 0.184, 3, 10, 9.61, -1.34, 0.03652, 11, 1.15, -1.37, 0.76348, 6, -18.71, 2.82, 0.2], "hull": 20, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 36, 38, 0, 38, 14, 16, 16, 18, 6, 40, 40, 44, 44, 48, 48, 46, 40, 50, 50, 42, 52, 54, 44, 56, 56, 58, 56, 60, 60, 62, 56, 64, 58, 66, 58, 68, 58, 70, 70, 72, 66, 22, 32, 34, 34, 36, 68, 34, 70, 74], "width": 43, "height": 63}}, "body": {"body": {"type": "mesh", "uvs": [0.19296, 0.10324, 0.2555, 0.03828, 0.39959, 0, 0.58446, 0.029, 0.65787, 0.09628, 0.83187, 0.145, 1, 0.23548, 1, 0.38628, 0.90799, 0.54172, 0.81012, 0.63684, 0.76934, 0.78996, 0.65515, 0.95236, 0.49746, 1, 0.32075, 1, 0.125, 0.91988, 0.05703, 0.81316, 0.09509, 0.66236, 0.03256, 0.5046, 0, 0.32132, 0.02712, 0.19372, 0.08693, 0.11252, 0.3478, 0.1143, 0.32563, 0.26222, 0.32362, 0.3981, 0.34579, 0.5787, 0.58766, 0.61482, 0.74085, 0.59246, 0.12407, 0.55634, 0.31354, 0.72662, 0.59371, 0.73694, 0.29741, 0.8711, 0.56146, 0.88658, 0.61387, 0.29146, 0.79124, 0.31726, 0.90613, 0.36886, 0.12206, 0.25534], "triangles": [12, 31, 11, 14, 30, 13, 12, 13, 30, 14, 15, 30, 17, 35, 23, 17, 18, 35, 18, 19, 35, 35, 0, 22, 19, 20, 35, 35, 20, 0, 9, 26, 8, 8, 33, 34, 8, 26, 33, 26, 32, 33, 8, 34, 7, 34, 6, 7, 34, 33, 6, 33, 5, 6, 33, 32, 5, 32, 4, 5, 22, 0, 21, 0, 1, 21, 21, 1, 2, 32, 25, 23, 32, 22, 21, 22, 32, 23, 32, 21, 4, 21, 2, 3, 23, 35, 22, 4, 21, 3, 27, 17, 23, 31, 12, 30, 30, 28, 31, 31, 28, 29, 31, 29, 10, 25, 28, 24, 28, 25, 29, 30, 15, 28, 15, 16, 28, 10, 29, 9, 9, 29, 26, 29, 25, 26, 28, 16, 24, 24, 16, 27, 16, 17, 27, 26, 25, 32, 27, 23, 24, 25, 24, 23, 11, 31, 10], "vertices": [4, 4, 27.64, 13.07, 0.01544, 6, -2.58, 9.15, 0.39136, 21, 8.94, -4.92, 0.3372, 19, 26.07, 7.48, 0.256, 3, 6, 2.93, 6.09, 0.67525, 21, 14.18, -8.43, 0.06875, 19, 31.12, 3.7, 0.256, 4, 4, 36.63, 0.66, 0.25495, 6, 7.41, -2.47, 0.48127, 20, 19.25, 29.89, 0.00778, 19, 34.4, -5.38, 0.256, 4, 4, 35.62, -11.33, 0.60645, 6, 7.38, -14.5, 0.02261, 20, 18.24, 17.9, 0.11494, 19, 32.77, -17.3, 0.256, 4, 4, 31.06, -16.49, 0.48306, 6, 3.26, -20.03, 0.00011, 20, 13.67, 12.74, 0.26084, 19, 27.94, -22.22, 0.256, 3, 4, 28.5, -27.93, 0.12335, 20, 11.12, 1.3, 0.62065, 19, 24.8, -33.51, 0.256, 3, 3, 52.48, -40.43, 5e-05, 20, 5.41, -10.07, 0.74395, 19, 18.5, -44.57, 0.256, 3, 3, 41.18, -40.95, 0.03155, 20, -5.84, -11.17, 0.71245, 19, 7.21, -45.08, 0.256, 4, 3, 29.27, -35.59, 0.18939, 4, -0.64, -35.68, 0.0133, 20, -18.02, -6.45, 0.54131, 19, -4.71, -39.73, 0.256, 4, 3, 21.86, -29.66, 0.404, 4, -8.35, -30.14, 0.02116, 20, -25.73, -0.91, 0.31884, 19, -12.12, -33.79, 0.256, 5, 3, 10.26, -27.57, 0.35995, 4, -20.03, -28.65, 0.0005, 20, -37.41, 0.57, 0.07405, 19, -23.71, -31.71, 0.1495, 2, 18.52, 10.84, 0.416, 4, 3, -2.23, -20.83, 0.1695, 20, -50.24, 6.66, 0.00906, 19, -36.21, -24.96, 0.06144, 2, 11.27, -1.38, 0.76, 4, 3, -6.26, -10.91, 0.15254, 20, -54.78, 16.36, 0.00222, 19, -40.24, -15.04, 0.05325, 2, 1.2, -5.01, 0.792, 4, 3, -6.78, 0.39, 0.15474, 20, -55.88, 27.62, 1e-05, 19, -40.75, -3.74, 0.05325, 2, -10.11, -5.07, 0.792, 4, 3, -1.34, 13.18, 0.13094, 21, -52.44, -6.55, 0, 19, -35.32, 9.05, 0.04506, 2, -22.67, 0.87, 0.824, 4, 3, 6.46, 17.89, 0.35582, 21, -44.89, -1.44, 0.0013, 19, -27.52, 13.75, 0.12288, 2, -27.06, 8.86, 0.52, 4, 3, 17.87, 15.97, 0.68227, 4, -14.7, 15.22, 0.01742, 21, -33.4, -2.76, 0.04432, 19, -16.11, 11.83, 0.256, 4, 3, 29.5, 20.5, 0.36233, 4, -3.32, 20.36, 0.14423, 21, -22.02, 2.37, 0.23744, 19, -4.47, 16.37, 0.256, 4, 3, 43.14, 23.21, 0.06206, 4, 10.16, 23.77, 0.08828, 21, -8.54, 5.78, 0.59367, 19, 9.17, 19.08, 0.256, 4, 3, 52.78, 21.91, 0.00163, 6, -11.15, 18.38, 0.00494, 21, 1.16, 4.99, 0.73744, 19, 18.81, 17.78, 0.256, 3, 6, -4.48, 15.71, 0.10688, 21, 7.59, 1.77, 0.63712, 19, 25.06, 14.23, 0.256, 4, 4, 27.78, 3.13, 0.21792, 6, -1.62, -0.74, 0.48514, 21, 9.08, -14.86, 0.00895, 18, 45.79, -3.14, 0.288, 4, 4, 16.6, 3.46, 0.62395, 6, -12.79, -1.33, 0.02396, 21, -2.1, -14.53, 0.06409, 18, 34.64, -2.23, 0.288, 4, 3, 38.33, 2.26, 0.0065, 4, 6.45, 2.6, 0.6774, 21, -12.25, -15.39, 0.0281, 18, 24.46, -2.56, 0.288, 3, 3, 24.86, 0.22, 0.71174, 21, -25.6, -18.12, 0.00026, 18, 10.99, -4.6, 0.288, 4, 3, 22.86, -15.36, 0.56429, 4, -8.09, -15.8, 0.05822, 20, -25.47, 13.42, 0.15349, 18, 8.99, -20.18, 0.224, 4, 3, 24.98, -25.08, 0.44529, 4, -5.46, -25.4, 0.04782, 20, -22.85, 3.83, 0.37089, 18, 11.11, -29.9, 0.136, 4, 3, 25.89, 14.48, 0.59785, 4, -6.61, 14.15, 0.10761, 21, -25.31, -3.83, 0.14254, 18, 12.02, 9.66, 0.152, 3, 3, 13.69, 1.78, 0.71137, 21, -36.84, -17.15, 0.00063, 18, -0.19, -3.04, 0.288, 4, 3, 13.73, -16.17, 0.67933, 4, -17.17, -17.08, 0.00399, 20, -34.55, 12.15, 0.09268, 18, -0.15, -20.98, 0.224, 2, 3, 2.81, 2.32, 0.712, 18, -11.06, -2.5, 0.288, 3, 3, 2.42, -14.61, 0.74337, 20, -45.92, 13.11, 0.03263, 18, -11.45, -19.43, 0.224, 4, 3, 47.16, -15.94, 0.01717, 4, 16.21, -15.11, 0.44046, 20, -1.17, 14.11, 0.31837, 18, 33.29, -20.76, 0.224, 4, 3, 45.74, -27.37, 0.01966, 4, 15.39, -26.6, 0.06814, 20, -1.99, 2.63, 0.7762, 18, 31.87, -32.18, 0.136, 4, 3, 42.21, -34.89, 0.04302, 4, 12.25, -34.3, 0.00046, 20, -5.13, -5.07, 0.92452, 18, 28.34, -39.71, 0.032, 5, 3, 48.44, 15.63, 0.01164, 4, 15.85, 16.48, 0.09819, 6, -14.61, 11.58, 0.00552, 21, -2.85, -1.51, 0.73265, 18, 34.57, 10.81, 0.152], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 4, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 48, 54, 48, 56, 56, 60, 58, 62, 64, 66, 66, 68], "width": 64, "height": 75}}, "tui1": {"tui1": {"type": "mesh", "uvs": [0.36713, 0.58377, 0.34883, 0.53049, 0.37933, 0.4814, 0.31528, 0.3552, 0.35188, 0.19113, 0.50133, 0.0495, 0.69348, 0, 0.88563, 0.01304, 1, 0.08035, 1, 0.22899, 0.93138, 0.37062, 0.83073, 0.4758, 0.76058, 0.5389, 0.76363, 0.58097, 0.76363, 0.67071, 0.64773, 0.80253, 0.64163, 0.89368, 0.45863, 0.94697, 0.30308, 0.98904, 0.10178, 0.9666, 0, 0.88807, 0.20853, 0.81655, 0.33663, 0.77589, 0.36103, 0.65249, 0.63858, 0.05511, 0.55623, 0.21216, 0.51658, 0.35941, 0.50133, 0.49122, 0.47998, 0.5375, 0.47388, 0.5992, 0.45558, 0.78711, 0.30308, 0.87545], "triangles": [19, 31, 18, 18, 31, 17, 19, 20, 31, 17, 31, 16, 31, 30, 16, 20, 21, 31, 31, 22, 30, 31, 21, 22, 16, 30, 15, 15, 30, 14, 22, 23, 30, 29, 30, 23, 29, 14, 30, 14, 29, 13, 13, 29, 28, 13, 28, 12, 23, 0, 29, 29, 0, 28, 0, 1, 28, 1, 2, 28, 28, 27, 12, 12, 27, 11, 28, 2, 27, 11, 27, 26, 27, 2, 26, 2, 3, 26, 11, 26, 10, 26, 25, 10, 10, 25, 9, 26, 3, 25, 3, 4, 25, 25, 24, 9, 9, 24, 8, 24, 7, 8, 24, 6, 7, 24, 25, 5, 25, 4, 5, 24, 5, 6], "vertices": [2, 33, 49, -4.45, 0.02154, 34, 4.14, -6.09, 0.97846, 2, 33, 44.79, -6.3, 0.37782, 34, -0.44, -6.5, 0.62218, 2, 33, 40.43, -6.18, 0.8883, 34, -4.53, -4.99, 0.1117, 1, 33, 30.65, -11.36, 1, 1, 33, 16.74, -13.47, 1, 1, 33, 3.55, -10.72, 1, 1, 33, -2.47, -4.34, 1, 1, 33, -3.33, 3.38, 1, 1, 33, 1.08, 9.25, 1, 1, 33, 13.35, 12.44, 1, 1, 33, 25.73, 12.82, 1, 2, 33, 35.43, 11.18, 0.92389, 34, -3.73, 13.05, 0.07611, 2, 33, 41.34, 9.82, 0.53232, 34, 1.44, 9.88, 0.46768, 2, 33, 44.78, 10.84, 0.20394, 34, 5.03, 9.75, 0.79606, 2, 33, 52.19, 12.76, 0.00325, 34, 12.66, 9.21, 0.99675, 2, 34, 23.55, 3.79, 0.93657, 35, -3.88, 0.93, 0.06343, 2, 34, 31.29, 3, 0.00863, 35, 0.87, 7.09, 0.99137, 1, 35, 9.46, 6.48, 1, 1, 35, 16.61, 5.74, 1, 1, 35, 22.01, -0.53, 1, 1, 35, 21.39, -8.34, 1, 2, 34, 23.5, -13.82, 0.05694, 35, 11.05, -8.39, 0.94306, 2, 34, 20.4, -8.46, 0.48144, 35, 4.87, -8.2, 0.51856, 2, 34, 9.97, -6.75, 0.99599, 35, -2.09, -16.16, 0.00401, 1, 33, 2.63, -5.28, 1, 1, 33, 16.42, -5.1, 1, 1, 33, 28.98, -3.48, 1, 1, 33, 40.01, -1.24, 1, 2, 33, 44.05, -1.07, 0.14419, 34, 0.53, -1.31, 0.85581, 1, 34, 5.76, -1.93, 1, 2, 34, 21.69, -3.78, 0.65776, 35, 1.57, -4.64, 0.34224, 2, 34, 28.78, -10.4, 0.0095, 35, 10.93, -2.11, 0.9905], "hull": 24, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 0, 46, 12, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62], "width": 40, "height": 87}}, "tui2": {"tui2": {"type": "mesh", "uvs": [0.20643, 0, 0.39516, 0.00658, 0.58389, 0.09561, 0.61594, 0.21846, 0.63731, 0.41609, 0.63731, 0.46773, 0.64087, 0.5158, 0.69072, 0.66892, 0.71921, 0.80602, 0.86521, 0.87012, 0.99697, 0.936, 0.96492, 1, 0.80824, 1, 0.59814, 1, 0.39516, 0.95024, 0.39516, 0.85053, 0.23848, 0.70987, 0.22424, 0.57812, 0.21711, 0.52292, 0.13877, 0.48019, 0.00702, 0.29146, 0.00491, 0.1664, 0.00346, 0.07958, 0.3157, 0.12858, 0.36107, 0.25106, 0.46087, 0.44311, 0.49716, 0.49906, 0.51229, 0.55804, 0.5516, 0.69262, 0.5879, 0.84233, 0.76331, 0.93306], "triangles": [30, 8, 9, 29, 8, 30, 30, 9, 10, 14, 15, 29, 13, 14, 29, 30, 13, 29, 11, 12, 30, 13, 30, 12, 10, 11, 30, 26, 5, 6, 18, 25, 26, 27, 26, 6, 18, 26, 27, 17, 18, 27, 27, 6, 7, 28, 27, 7, 28, 16, 17, 28, 17, 27, 28, 7, 8, 29, 28, 8, 15, 16, 28, 15, 28, 29, 23, 0, 1, 23, 1, 2, 22, 0, 23, 21, 22, 23, 23, 2, 3, 24, 23, 3, 21, 23, 24, 20, 21, 24, 24, 3, 4, 25, 24, 4, 25, 4, 5, 19, 20, 24, 19, 24, 25, 26, 25, 5, 18, 19, 25], "vertices": [1, 36, 4.09, 2.75, 1, 1, 36, 7.27, 9.82, 1, 1, 36, 16.66, 14.61, 1, 1, 36, 26.38, 12.42, 1, 2, 36, 41.59, 7.74, 0.94065, 37, -5.34, 6.3, 0.05935, 2, 36, 45.49, 6.31, 0.5896, 37, -1.19, 6.28, 0.4104, 2, 36, 49.16, 5.11, 0.13841, 37, 2.68, 6.4, 0.86159, 2, 37, 15, 8.36, 0.98553, 38, -0.9, 16.95, 0.01447, 2, 37, 26.03, 9.46, 0.39336, 38, 5.96, 8.24, 0.60664, 2, 37, 31.22, 15.41, 0.01363, 38, 13.77, 7.07, 0.98637, 1, 38, 21.16, 5.46, 1, 1, 38, 22.79, 0.41, 1, 1, 38, 17.36, -3.01, 1, 1, 38, 10.06, -7.6, 1, 1, 38, 0.89, -8.65, 1, 2, 37, 29.52, -3.85, 0.46855, 38, -3.38, -1.86, 0.53145, 1, 37, 18.17, -10.2, 1, 2, 36, 47.95, -12.65, 0.12677, 37, 7.57, -10.72, 0.87323, 2, 36, 43.68, -11.39, 0.45152, 37, 3.13, -10.98, 0.54848, 2, 36, 39.35, -13.21, 0.77396, 37, -0.32, -14.17, 0.22604, 1, 36, 23.24, -13.03, 1, 1, 36, 13.78, -9.63, 1, 1, 36, 7.21, -7.27, 1, 1, 36, 15.34, 3.37, 1, 1, 36, 25.22, 1.71, 1, 1, 36, 41.12, 0.21, 1, 1, 37, 1.29, 0.51, 1, 1, 37, 6.04, 1.1, 1, 2, 37, 16.87, 2.65, 0.99353, 38, -4.71, 12.3, 0.00647, 2, 37, 28.91, 4.05, 0.29259, 38, 2.96, 2.9, 0.70741, 2, 37, 36.25, 11.2, 0.00034, 38, 12.93, 0.56, 0.99966], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 44, 40, 42, 42, 44, 0, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 22], "width": 41, "height": 82}}, "lianl2": {"lianl2": {"type": "path", "lengths": [170.4, 325.65, 674.59], "vertexCount": 9, "vertices": [1, 78, -256.08, 234.6, 1, 1, 76, 6.98, 0.18, 1, 2, 77, -79.1, 40.97, 0.256, 76, -51.02, 7, 0.744, 2, 77, -34.32, -15.29, 0.92, 76, -118.03, -16.51, 0.08, 1, 77, 3.05, -38, 1, 2, 78, -113.57, 31.75, 0.08, 77, 40.42, -60.72, 0.92, 2, 78, -80.28, 0.99, 0.784, 77, 73.71, -91.48, 0.216, 1, 78, -8.75, -1.84, 1, 1, 78, 62.78, -4.67, 1]}}, "qunzi": {"qunzi": {"type": "mesh", "uvs": [0.14676, 0.24718, 0.1296, 0.14113, 0.16882, 0.03155, 0.29876, 0.11639, 0.48753, 0.1376, 0.68121, 0.08457, 0.82831, 0, 0.90186, 0.08104, 0.88224, 0.2295, 0.92882, 0.45927, 1, 0.77388, 0.83076, 0.90467, 0.64444, 1, 0.46302, 1, 0.18598, 0.94355, 0.02663, 0.78448, 0, 0.63955, 0.10263, 0.41332, 0.50224, 0.33909, 0.73269, 0.26132, 0.26198, 0.2896, 0.48508, 0.71378, 0.77927, 0.60774, 0.19089, 0.57946], "triangles": [20, 0, 3, 19, 5, 6, 8, 19, 6, 8, 6, 7, 3, 0, 1, 3, 1, 2, 20, 3, 4, 18, 4, 5, 9, 22, 19, 22, 9, 10, 11, 22, 10, 12, 22, 11, 9, 19, 8, 13, 14, 21, 12, 21, 22, 13, 21, 12, 18, 20, 4, 18, 19, 22, 18, 23, 20, 21, 18, 22, 21, 23, 18, 18, 5, 19, 16, 17, 23, 15, 16, 23, 14, 15, 23, 14, 23, 21, 20, 17, 0, 23, 17, 20], "vertices": [2, 27, 0.76, -5.15, 0.104, 2, -26.58, 2.22, 0.896, 3, 27, -3.19, -7.66, 0.06822, 2, -27.66, 6.77, 0.58778, 3, 4.35, 18.41, 0.344, 4, 27, -8.44, -6.93, 0.0682, 31, -11.91, -20.99, 2e-05, 2, -25.26, 11.5, 0.58778, 3, 9.17, 16.19, 0.344, 4, 27, -7.67, 1.88, 0.05492, 31, -8.26, -12.93, 0.01331, 2, -17.18, 7.89, 0.58778, 3, 5.89, 7.98, 0.344, 5, 27, -10.7, 13.22, 0.0071, 31, -7.35, -1.23, 0.05688, 29, -6.97, -15.89, 0.00424, 2, -5.47, 7.04, 0.58778, 3, 5.51, -3.76, 0.344, 4, 31, -9.63, 10.78, 0.01444, 29, -5.38, -3.78, 0.05379, 2, 6.52, 9.38, 0.58778, 3, 8.33, -15.65, 0.344, 3, 29, -5.98, 6.02, 0.06822, 2, 15.62, 13.07, 0.58778, 3, 12.38, -24.59, 0.344, 3, 29, -1.25, 9.27, 0.06822, 2, 20.2, 9.61, 0.58778, 3, 9.1, -29.31, 0.344, 4, 29, 4.43, 6.11, 0.06822, 30, -7.76, 7.05, 1e-05, 2, 19.02, 3.22, 0.58778, 3, 2.67, -28.38, 0.344, 2, 29, 14.72, 5.77, 0.19974, 30, 2.42, 5.53, 0.80026, 1, 30, 16.55, 3.85, 1, 3, 31, 25.63, 20.05, 0.00401, 32, 14.39, 19.62, 0.24263, 30, 17.25, -8.03, 0.75335, 3, 31, 29.73, 8.5, 0.00038, 32, 18.13, 7.95, 0.79933, 30, 16.13, -20.24, 0.20029, 2, 28, 7.03, 26.71, 0.05178, 32, 17.79, -3.29, 0.94822, 2, 28, 14.59, 11.1, 0.72226, 32, 14.84, -20.39, 0.27774, 2, 28, 14.43, -0.91, 0.99891, 32, 7.7, -30.05, 0.00109, 2, 27, 19.7, -8.12, 0.00074, 28, 10.17, -5.76, 0.99926, 2, 27, 8.41, -5.35, 0.76452, 28, -1.45, -5.9, 0.23548, 2, 27, -2.83, 16.96, 0.00558, 31, 1.31, -0.32, 0.99442, 2, 31, -2.03, 13.97, 0.12084, 29, 2.83, -3.12, 0.87916, 2, 27, 0.11, 2.2, 0.91313, 31, -0.82, -15.21, 0.08687, 4, 27, 12.72, 21.3, 0.00614, 28, -3.95, 20.98, 0.0163, 31, 17.42, -1.38, 0.00417, 32, 5.53, -1.55, 0.9734, 4, 31, 12.86, 16.86, 0.08769, 32, 1.53, 16.82, 0.09713, 29, 17.89, -5.04, 0.0733, 30, 4.33, -5.57, 0.74189, 4, 27, 13.33, 2.18, 0.05146, 28, 1.43, 2.62, 0.88732, 31, 11.65, -19.62, 0.02033, 32, -0.8, -19.6, 0.04089], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 36, 38, 36, 40, 36, 42, 38, 44, 40, 46], "width": 62, "height": 43}}}}], "animations": {"pan_gu": {"bones": {"all27": {"rotate": [{"angle": 4.93}], "translate": [{"x": 3.97, "y": 0.7}]}, "bone27": {"rotate": [{"angle": 1.13, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": 5.33, "curve": 0.242, "c3": 0.661, "c4": 0.65}, {"time": 2.6667, "angle": 1.13, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 3.0333, "curve": 0.25, "c3": 0.75}, {"time": 4.3667, "angle": 5.33, "curve": 0.242, "c3": 0.661, "c4": 0.65}, {"time": 5.3333, "angle": 3.79, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "angle": 7.57, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6, "angle": 1.13, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 6.3667, "curve": 0.25, "c3": 0.75}, {"time": 7.7, "angle": 5.33, "curve": 0.242, "c3": 0.661, "c4": 0.65}, {"time": 8.6667, "angle": 1.13}]}, "bone": {"rotate": [{"time": 5.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.6667, "angle": -2.26, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 2.72, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 2.72, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.6667, "x": 3.39, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.3333, "x": 2.72, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8.6667}]}, "bone2": {"rotate": [{"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": -4.4, "curve": 0.25, "c3": 0.75}, {"time": 6}]}, "bone3": {"rotate": [{"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 4.26, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 5.4667, "curve": 0.25, "c3": 0.75}, {"time": 5.8, "angle": 11.59, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6}], "translate": [{"x": 0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.74, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 0.74, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": 0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "x": 0.74, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8.6667, "x": 0.14}]}, "bone4": {"translate": [{"x": 0.37, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.01, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 2.6667, "x": 0.37, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 1.01, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": 0.37, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 8, "x": 1.01, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 8.6667, "x": 0.37}]}, "bone5": {"translate": [{"x": 1.91, "y": 1.46, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 10.34, "y": 7.94, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 1.91, "y": 1.46, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": 10.34, "y": 7.94, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": 1.91, "y": 1.46, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "x": 10.34, "y": 7.94, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8.6667, "x": 1.91, "y": 1.46}]}, "all28": {"rotate": [{"angle": 4.74}], "translate": [{"x": 3.99, "y": 0.68}]}, "bone9": {"rotate": [{"angle": 3.88, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 21.02, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 3.88, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 21.02, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 8.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "angle": 17.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6, "angle": 3.88, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": 21.02, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8.6667, "angle": 3.88}]}, "bone11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 13.89, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 13.89, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": 17.28, "curve": 0.25, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": 13.89, "curve": 0.25, "c3": 0.75}, {"time": 8.6667}]}, "bone7": {"rotate": [{"angle": 9.75, "curve": 0.344, "c2": 0.37, "c3": 0.685, "c4": 0.72}, {"time": 0.3, "angle": 3.88, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": 21.02, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 2.6667, "angle": 9.75, "curve": 0.344, "c2": 0.37, "c3": 0.685, "c4": 0.72}, {"time": 2.9667, "angle": 3.88, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.3, "curve": 0.25, "c3": 0.75}, {"time": 4.6333, "angle": 21.02, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 5.3333, "angle": 8.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "angle": 17.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6, "angle": 9.75, "curve": 0.344, "c2": 0.37, "c3": 0.685, "c4": 0.72}, {"time": 6.3, "angle": 3.88, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 6.6333, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "angle": 21.02, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 8.6667, "angle": 9.75}]}, "bone10": {"rotate": [{"angle": 10.51, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 21.02, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 10.51, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 21.02, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 17.28, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 10.51, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 21.02, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8.6667, "angle": 10.51}]}, "bone30": {"rotate": [{"angle": 0.73, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 11.61, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": 0.73, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": 11.61, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.3333, "angle": 1.7, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.4, "curve": 0.25, "c3": 0.75}, {"time": 5.7333, "angle": 13.07, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 6, "angle": 0.73, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 6.1667, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "angle": 11.61, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 8.6667, "angle": 0.73}]}, "bone12": {"rotate": [{"angle": 2.56, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 13.89, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 2.56, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 13.89, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 8.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "angle": 17.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6, "angle": 2.56, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": 13.89, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8.6667, "angle": 2.56}]}, "bone13": {"rotate": [{"angle": 6.94, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 13.89, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 6.94, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 13.89, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 17.28, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 6.94, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 13.89, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8.6667, "angle": 6.94}]}, "bone8": {"rotate": [{"angle": 16.58, "curve": 0.318, "c2": 0.29, "c3": 0.659, "c4": 0.64}, {"time": 0.3, "angle": 10.51, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": 21.02, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 2.6667, "angle": 16.58, "curve": 0.318, "c2": 0.29, "c3": 0.659, "c4": 0.64}, {"time": 2.9667, "angle": 10.51, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.6333, "curve": 0.25, "c3": 0.75}, {"time": 4.9667, "angle": 21.02, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 5.3333, "angle": 17.28, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 16.58, "curve": 0.318, "c2": 0.29, "c3": 0.659, "c4": 0.64}, {"time": 6.3, "angle": 10.51, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.9667, "curve": 0.25, "c3": 0.75}, {"time": 8.3, "angle": 21.02, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 8.6667, "angle": 16.58}]}, "bone16": {"rotate": [{"angle": 9.23, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.1667, "angle": 6.94, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 13.89, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": 9.23, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 2.8333, "angle": 6.94, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "angle": 13.89, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "angle": 17.28, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 9.23, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 6.1667, "angle": 6.94, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.8333, "curve": 0.25, "c3": 0.75}, {"time": 8.1667, "angle": 13.89, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 8.6667, "angle": 9.23}]}, "bone17": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 10.34, "y": 7.94, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 10.34, "y": 7.94, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": "stepped"}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": 10.34, "y": 7.94, "curve": 0.25, "c3": 0.75}, {"time": 8.6667}]}, "bone19": {"translate": [{"x": 0.45, "y": -0.31, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 1.34, "y": -0.92, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": 0.45, "y": -0.31, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 1.34, "y": -0.92, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": 0.45, "y": -0.31, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 6.5, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": 1.34, "y": -0.92, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8.6667, "x": 0.45, "y": -0.31}]}, "bone20": {"translate": [{"x": 0.38, "y": 0.36, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 1.14, "y": 1.07, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": 0.38, "y": 0.36, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "x": 1.14, "y": 1.07, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": 0.38, "y": 0.36, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 6.5, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": 1.14, "y": 1.07, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8.6667, "x": 0.38, "y": 0.36}]}, "bone21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -5.17, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -5.17, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": 89.04, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "angle": -10.57, "curve": 0.25, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -5.17, "curve": 0.25, "c3": 0.75}, {"time": 8.6667}]}, "bone22": {"rotate": [{"angle": -0.95, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -5.17, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -0.95, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": -5.17, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": -91.08, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "angle": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": -0.95, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": -5.17, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8.6667, "angle": -0.95}]}, "bone23": {"rotate": [{"angle": -1.74, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -5.17, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -1.74, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": -5.17, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": -1.74, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 6.5, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "angle": -5.17, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8.6667, "angle": -1.74}]}, "bone24": {"rotate": [{"angle": 1.97, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 10.69, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 1.97, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 10.69, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": -0.99, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 5.6333, "angle": -23.28, "curve": 0.25, "c3": 0.75}, {"time": 5.9667, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 6, "angle": 1.97, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": 10.69, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8.6667, "angle": 1.97}]}, "bone29": {"rotate": [{"angle": 8.41, "curve": 0.326, "c2": 0.31, "c3": 0.683, "c4": 0.72}, {"time": 0.5333, "angle": 2.46, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": 11.61, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "angle": 8.41, "curve": 0.326, "c2": 0.31, "c3": 0.683, "c4": 0.72}, {"time": 3.2, "angle": 2.46, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 3.5667, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "angle": 11.61, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 5.3333, "angle": 12.53, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 5.4667, "angle": 6.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.6333, "curve": 0.25, "c3": 0.75}, {"time": 5.9667, "angle": 13.07, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 6, "angle": 8.41, "curve": 0.326, "c2": 0.31, "c3": 0.683, "c4": 0.72}, {"time": 6.5333, "angle": 2.46, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 6.9, "curve": 0.25, "c3": 0.75}, {"time": 8.2333, "angle": 11.61, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 8.6667, "angle": 8.41}]}, "bone25": {"rotate": [{"angle": -8.62, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5333, "angle": -13.64, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": -8.62, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.2, "angle": -13.64, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.3333, "angle": -3.83, "curve": 0.351, "c2": 0.42, "c3": 0.686, "c4": 0.76}, {"time": 5.6667, "angle": -2.5, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 6, "angle": -8.62, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.5333, "angle": -13.64, "curve": 0.25, "c3": 0.75}, {"time": 7.8667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8.6667, "angle": -8.62}]}, "bone14": {"rotate": [{"angle": 0.87, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 13.89, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": 0.87, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": 13.89, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": 17.28, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 0.87, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 6.1667, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "angle": 13.89, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 8.6667, "angle": 0.87}]}, "bone15": {"rotate": [{"angle": 4.64, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 0.1667, "angle": 2.56, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 13.89, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": 4.64, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 2.8333, "angle": 2.56, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "angle": 13.89, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "angle": 8.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "angle": 17.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6, "angle": 4.64, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 6.1667, "angle": 2.56, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 6.5, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "angle": 13.89, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 8.6667, "angle": 4.64}]}, "bone26": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 5.33, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 5.33, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": 7.57, "curve": 0.25, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": 5.33, "curve": 0.25, "c3": 0.75}, {"time": 8.6667}]}, "bone28": {"rotate": [{"angle": 4.27, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 11.61, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": 4.27, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "angle": 11.61, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.3333, "angle": 4.81, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 5.4667, "curve": 0.25, "c3": 0.75}, {"time": 5.8, "angle": 13.07, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6, "angle": 4.27, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.5333, "curve": 0.25, "c3": 0.75}, {"time": 7.8667, "angle": 11.61, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8.6667, "angle": 4.27}]}, "bone31": {"rotate": [{"angle": 4.24, "curve": 0.342, "c2": 0.36, "c3": 0.677, "c4": 0.7}, {"time": 0.1667, "angle": 2.46, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 11.61, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": 4.24, "curve": 0.342, "c2": 0.36, "c3": 0.677, "c4": 0.7}, {"time": 2.8333, "angle": 2.46, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "angle": 11.61, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 5.3333, "angle": 9.91, "curve": 0.321, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 5.4, "angle": 6.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.5667, "curve": 0.25, "c3": 0.75}, {"time": 5.9, "angle": 13.07, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 6, "angle": 4.24, "curve": 0.342, "c2": 0.36, "c3": 0.677, "c4": 0.7}, {"time": 6.1667, "angle": 2.46, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 6.5333, "curve": 0.25, "c3": 0.75}, {"time": 7.8667, "angle": 11.61, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8.6667, "angle": 4.24}]}, "all6": {"rotate": [{"time": 5.3333, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 5.7, "angle": 72.24, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 6}], "translate": [{"x": -6.58, "y": 1.04, "curve": 0.351, "c2": 0.65, "c3": 0.686}, {"time": 0.3333, "x": -5.43, "y": 1.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "x": -17.79, "y": 1.12, "curve": 0.364, "c2": 0.48, "c3": 0.704, "c4": 0.85}, {"time": 2.6667, "x": -6.58, "y": 1.04, "curve": 0.351, "c2": 0.65, "c3": 0.686}, {"time": 3, "x": -5.43, "y": 1.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.3333, "x": -17.79, "y": 1.12, "curve": 0.364, "c2": 0.48, "c3": 0.704, "c4": 0.85}, {"time": 5.3333, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 5.7, "x": 10.52, "y": -9.11, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 6, "x": -6.58, "y": 1.04, "curve": 0.351, "c2": 0.65, "c3": 0.686}, {"time": 6.3333, "x": -5.43, "y": 1.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 7.6667, "x": -17.79, "y": 1.12, "curve": 0.364, "c2": 0.48, "c3": 0.704, "c4": 0.85}, {"time": 8.6667, "x": -6.58, "y": 1.04}]}, "bone42": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 8.45, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 8.45, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": "stepped"}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": 8.45, "curve": 0.25, "c3": 0.75}, {"time": 8.6667}]}, "all7": {"translate": [{"x": 5.01, "y": 2.33, "curve": 0.342, "c2": 0.36, "c3": 0.676, "c4": 0.7}, {"time": 1.3333, "x": 8.44, "y": 4.34, "curve": 0.345, "c2": 0.38, "c3": 0.679, "c4": 0.72}, {"time": 2.6667, "x": 5.01, "y": 2.33, "curve": 0.342, "c2": 0.36, "c3": 0.676, "c4": 0.7}, {"time": 4, "x": 8.44, "y": 4.34, "curve": 0.345, "c2": 0.38, "c3": 0.679, "c4": 0.72}, {"time": 5.3333, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 5.7, "x": 129.41, "y": 50.37, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 6, "x": 5.01, "y": 2.33, "curve": 0.342, "c2": 0.36, "c3": 0.676, "c4": 0.7}, {"time": 7.3333, "x": 8.44, "y": 4.34, "curve": 0.345, "c2": 0.38, "c3": 0.679, "c4": 0.72}, {"time": 8.6667, "x": 5.01, "y": 2.33}]}, "bone43": {"rotate": [{"angle": -179.38, "curve": "stepped"}, {"time": 2.6667, "angle": -179.38, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -179.44, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": -179.38}], "translate": [{"x": 268.81, "y": 248.95, "curve": "stepped"}, {"time": 2.6667, "x": 268.81, "y": 248.95, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 268.83, "y": 248.95, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": 268.81, "y": 248.95}]}, "bone44": {"rotate": [{"angle": 2.65, "curve": "stepped"}, {"time": 2.6667, "angle": 2.65, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 2.68, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 2.65}], "translate": [{"x": -2.93, "y": 0.18, "curve": "stepped"}, {"time": 2.6667, "x": -2.93, "y": 0.18, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": -2.94, "y": 0.18, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": -2.93, "y": 0.18}]}, "bone45": {"rotate": [{"angle": 3.85, "curve": "stepped"}, {"time": 2.6667, "angle": 3.85, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 3.88, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 3.85, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "angle": 3.85}], "translate": [{"x": -2.91, "y": 0.38, "curve": "stepped"}, {"time": 2.6667, "x": -2.91, "y": 0.38, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": -2.91, "y": 0.38, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": -2.91, "y": 0.38}]}, "bone46": {"rotate": [{"angle": 3.94, "curve": "stepped"}, {"time": 2.6667, "angle": 3.94, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 3.95, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 3.94, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "angle": 3.93}], "translate": [{"x": -2.8, "y": 0.39, "curve": "stepped"}, {"time": 2.6667, "x": -2.8, "y": 0.39, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": -2.8, "y": 0.39, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": -2.8, "y": 0.39}]}, "bone47": {"rotate": [{"angle": 4.02, "curve": "stepped"}, {"time": 2.6667, "angle": 4.02, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 4.03, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 4.02, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "angle": 4.01}], "translate": [{"x": -2.81, "y": 0.4, "curve": "stepped"}, {"time": 2.6667, "x": -2.81, "y": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": -2.81, "y": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": -2.81, "y": 0.4}]}, "bone48": {"rotate": [{"angle": 4.14, "curve": "stepped"}, {"time": 2.6667, "angle": 4.14, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 4.14, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 4.14, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "angle": 4.12}], "translate": [{"x": -2.81, "y": 0.41}]}, "bone49": {"rotate": [{"angle": 4.27, "curve": "stepped"}, {"time": 2.6667, "angle": 4.27, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 4.26, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 4.27, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "angle": 4.25}], "translate": [{"x": -2.79, "y": 0.42, "curve": "stepped"}, {"time": 2.6667, "x": -2.79, "y": 0.42, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": -2.79, "y": 0.42, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": -2.79, "y": 0.42}]}, "bone50": {"rotate": [{"angle": 4.4, "curve": "stepped"}, {"time": 2.6667, "angle": 4.4, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 4.37, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 4.4, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "angle": 4.36}], "translate": [{"x": -2.75, "y": 0.43, "curve": "stepped"}, {"time": 2.6667, "x": -2.75, "y": 0.43, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": -2.74, "y": 0.43, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": -2.75, "y": 0.43, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "x": -2.75, "y": 0.43}]}, "bone51": {"rotate": [{"angle": 4.47, "curve": "stepped"}, {"time": 2.6667, "angle": 4.47, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 4.43, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 4.47, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "angle": 4.42}], "translate": [{"x": -2.76, "y": 0.44, "curve": "stepped"}, {"time": 2.6667, "x": -2.76, "y": 0.44, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": -2.76, "y": 0.44, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": -2.76, "y": 0.44, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "x": -2.76, "y": 0.44}]}, "bone52": {"rotate": [{"angle": 4.46, "curve": "stepped"}, {"time": 2.6667, "angle": 4.46, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 4.42, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 4.46, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "angle": 4.41}], "translate": [{"x": -2.84, "y": 0.44, "curve": "stepped"}, {"time": 2.6667, "x": -2.84, "y": 0.44, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": -2.84, "y": 0.43, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": -2.84, "y": 0.44, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "x": -2.84, "y": 0.44}]}, "bone53": {"rotate": [{"angle": 13.7, "curve": "stepped"}, {"time": 2.6667, "angle": 13.7, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 13.8, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 13.7, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "angle": 13.45}], "translate": [{"x": -2.67, "y": 1.36, "curve": "stepped"}, {"time": 2.6667, "x": -2.67, "y": 1.36, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": -2.66, "y": 1.36, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": -2.67, "y": 1.36, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "x": -2.67, "y": 1.36}]}, "bone54": {"rotate": [{"angle": 2.64, "curve": "stepped"}, {"time": 2.6667, "angle": 2.64, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 2.64, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 2.64, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "angle": 2.58}], "translate": [{"x": -2.75, "y": 0.26, "curve": "stepped"}, {"time": 2.6667, "x": -2.75, "y": 0.26, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": -2.75, "y": 0.26, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": -2.75, "y": 0.26, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "x": -2.76, "y": 0.26}]}, "bone55": {"rotate": [{"angle": 2.66, "curve": "stepped"}, {"time": 2.6667, "angle": 2.66, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 2.66, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 2.66, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "angle": 2.59}], "translate": [{"x": -2.64, "y": 0.26, "curve": "stepped"}, {"time": 2.6667, "x": -2.64, "y": 0.26, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": -2.64, "y": 0.26, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": -2.64, "y": 0.26, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "x": -2.65, "y": 0.26}]}, "bone56": {"rotate": [{"angle": 2.68, "curve": "stepped"}, {"time": 2.6667, "angle": 2.68, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 2.68, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 2.68, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "angle": 2.6}], "translate": [{"x": -2.64, "y": 0.26, "curve": "stepped"}, {"time": 2.6667, "x": -2.64, "y": 0.26, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": -2.64, "y": 0.26, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": -2.64, "y": 0.26, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "x": -2.64, "y": 0.26}]}, "bone57": {"rotate": [{"angle": 2.71, "curve": "stepped"}, {"time": 2.6667, "angle": 2.71, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 2.71, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 2.71, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "angle": 2.63}], "translate": [{"x": -2.67, "y": 0.26, "curve": "stepped"}, {"time": 2.6667, "x": -2.67, "y": 0.26, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": -2.67, "y": 0.26, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": -2.67, "y": 0.26, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "x": -2.67, "y": 0.26}]}, "bone58": {"rotate": [{"angle": 2.78, "curve": "stepped"}, {"time": 2.6667, "angle": 2.78, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 2.78, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 2.78, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "angle": 2.69}], "translate": [{"x": -2.68, "y": 0.27, "curve": "stepped"}, {"time": 2.6667, "x": -2.68, "y": 0.27, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": -2.68, "y": 0.27, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": -2.68, "y": 0.27, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "x": -2.69, "y": 0.27}]}, "bone59": {"rotate": [{"angle": 2.92, "curve": "stepped"}, {"time": 2.6667, "angle": 2.92, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 2.92, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 2.92, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "angle": 2.83}], "translate": [{"x": -2.69, "y": 0.28, "curve": "stepped"}, {"time": 2.6667, "x": -2.69, "y": 0.28, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": -2.68, "y": 0.28, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": -2.69, "y": 0.28, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "x": -2.69, "y": 0.28}]}, "bone60": {"rotate": [{"angle": 3.18, "curve": "stepped"}, {"time": 2.6667, "angle": 3.18, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 3.18, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 3.18, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "angle": 3.07}], "translate": [{"x": -2.66, "y": 0.3, "curve": "stepped"}, {"time": 2.6667, "x": -2.66, "y": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": -2.66, "y": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": -2.66, "y": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "x": -2.67, "y": 0.3}]}, "bone61": {"rotate": [{"angle": 3.67, "curve": "stepped"}, {"time": 2.6667, "angle": 3.67, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 3.67, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 3.67, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "angle": 3.54}], "translate": [{"x": -2.61, "y": 0.35, "curve": "stepped"}, {"time": 2.6667, "x": -2.61, "y": 0.35, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": -2.61, "y": 0.35, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": -2.61, "y": 0.35, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "x": -2.61, "y": 0.35}]}, "bone62": {"rotate": [{"angle": 4.69, "curve": "stepped"}, {"time": 2.6667, "angle": 4.69, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 4.68, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 4.69, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "angle": 4.51}], "translate": [{"x": -2.54, "y": 0.43, "curve": "stepped"}, {"time": 2.6667, "x": -2.54, "y": 0.43, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": -2.54, "y": 0.43, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": -2.54, "y": 0.43, "curve": 0.25, "c3": 0.75}, {"time": 8.6667, "x": -2.54, "y": 0.43}]}, "all8": {"translate": [{"x": -0.53, "y": -0.5, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -8.51, "y": -7.99, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "x": -0.53, "y": -0.5, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "x": -8.51, "y": -7.99, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 5.3333, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 5.7, "x": -33.62, "y": -23.27, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 6, "x": -0.53, "y": -0.5, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 6.1667, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "x": -8.51, "y": -7.99, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 8.6667, "x": -0.53, "y": -0.5}]}, "all9": {"translate": [{"x": -1.63, "y": 0.55, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -13.86, "y": 4.71, "curve": 0.356, "c2": 0.41, "c3": 0.711, "c4": 0.82}, {"time": 2.6667, "x": -1.63, "y": 0.55, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": -13.86, "y": 4.71, "curve": 0.356, "c2": 0.41, "c3": 0.711, "c4": 0.82}, {"time": 5.3333, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 5.7, "x": -30.91, "y": 0.67, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 6, "x": -1.63, "y": 0.55, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "x": -13.86, "y": 4.71, "curve": 0.356, "c2": 0.41, "c3": 0.711, "c4": 0.82}, {"time": 8.6667, "x": -1.63, "y": 0.55}]}, "all10": {"rotate": [{"angle": -169.71, "curve": "stepped"}, {"time": 2.6667, "angle": -169.71, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": -169.71, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": -169.71}], "translate": [{"x": -294.69, "y": 194.31, "curve": "stepped"}, {"time": 2.6667, "x": -294.69, "y": 194.31, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": -294.69, "y": 194.32, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": -294.69, "y": 194.31}]}, "all11": {"rotate": [{"angle": 5.13, "curve": "stepped"}, {"time": 2.6667, "angle": 5.13, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 5.14, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 5.13}], "translate": [{"x": 4, "y": 0.75, "curve": "stepped"}, {"time": 2.6667, "x": 4, "y": 0.75, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 4, "y": 0.75, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": 4, "y": 0.75}]}, "all12": {"rotate": [{"angle": 4.53, "curve": "stepped"}, {"time": 2.6667, "angle": 4.53, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 4.53, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 4.53}], "translate": [{"x": 4.02, "y": 0.66, "curve": "stepped"}, {"time": 2.6667, "x": 4.02, "y": 0.66, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 4.02, "y": 0.66, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": 4.02, "y": 0.66}]}, "all13": {"rotate": [{"angle": 4.08, "curve": "stepped"}, {"time": 2.6667, "angle": 4.08, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 4.08, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 4.08}], "translate": [{"x": 4.03, "y": 0.59}]}, "all14": {"rotate": [{"angle": 3.77, "curve": "stepped"}, {"time": 2.6667, "angle": 3.77, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 3.77, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 3.77}], "translate": [{"x": 4.04, "y": 0.54, "curve": "stepped"}, {"time": 2.6667, "x": 4.04, "y": 0.54, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 4.04, "y": 0.54, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": 4.04, "y": 0.54}]}, "all15": {"rotate": [{"angle": 3.59, "curve": "stepped"}, {"time": 2.6667, "angle": 3.59, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 3.59, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 3.59}], "translate": [{"x": 4.05, "y": 0.51, "curve": "stepped"}, {"time": 2.6667, "x": 4.05, "y": 0.51, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 4.05, "y": 0.51, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": 4.05, "y": 0.51}]}, "all16": {"rotate": [{"angle": 3.53, "curve": "stepped"}, {"time": 2.6667, "angle": 3.53, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 3.53, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 3.53}], "translate": [{"x": 4.05, "y": 0.5, "curve": "stepped"}, {"time": 2.6667, "x": 4.05, "y": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 4.05, "y": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": 4.05, "y": 0.5}]}, "all17": {"rotate": [{"angle": 3.63, "curve": "stepped"}, {"time": 2.6667, "angle": 3.63, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 3.63, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 3.63}], "translate": [{"x": 4.04, "y": 0.51, "curve": "stepped"}, {"time": 2.6667, "x": 4.04, "y": 0.51, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 4.04, "y": 0.51, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": 4.04, "y": 0.51}]}, "all18": {"rotate": [{"angle": 3.93, "curve": "stepped"}, {"time": 2.6667, "angle": 3.93, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 3.93, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 3.93}], "translate": [{"x": 4.04, "y": 0.55, "curve": "stepped"}, {"time": 2.6667, "x": 4.04, "y": 0.55, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 4.04, "y": 0.55, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": 4.04, "y": 0.55}]}, "all19": {"rotate": [{"angle": 4.56, "curve": "stepped"}, {"time": 2.6667, "angle": 4.56, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 4.56, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 4.56}], "translate": [{"x": 4.05, "y": 0.63, "curve": "stepped"}, {"time": 2.6667, "x": 4.05, "y": 0.63, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 4.05, "y": 0.63, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": 4.05, "y": 0.63}]}, "all20": {"rotate": [{"angle": 5.79, "curve": "stepped"}, {"time": 2.6667, "angle": 5.79, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 5.8, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 5.79}], "translate": [{"x": 4.05, "y": 0.79, "curve": "stepped"}, {"time": 2.6667, "x": 4.05, "y": 0.79, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 4.05, "y": 0.79, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": 4.05, "y": 0.79}]}, "all21": {"rotate": [{"angle": 2.1, "curve": "stepped"}, {"time": 2.6667, "angle": 2.1, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 2.1, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 2.1}], "translate": [{"x": 3.84, "y": 0.62, "curve": "stepped"}, {"time": 2.6667, "x": 3.84, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 3.84, "y": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": 3.84, "y": 0.62}]}, "all22": {"rotate": [{"angle": -0.69}], "translate": [{"x": 4.03, "y": -0.15}]}, "all23": {"rotate": [{"angle": 1.3}], "translate": [{"x": 4.02, "y": 0.14}]}, "all24": {"rotate": [{"angle": 2.93}], "translate": [{"x": 3.98, "y": 0.38}]}, "all25": {"rotate": [{"angle": 4.11}], "translate": [{"x": 3.98, "y": 0.56}]}, "all26": {"rotate": [{"angle": 4.76}], "translate": [{"x": 3.97, "y": 0.67}]}, "all29": {"rotate": [{"angle": 4.34}], "translate": [{"x": 4.04, "y": 0.63}]}}}}}