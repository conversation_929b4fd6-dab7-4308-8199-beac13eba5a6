import { _decorator, Label, Node, Sprite, Color, sp, tween, v3, isValid } from "cc";
import MsgMgr from "../../../lib/event/MsgMgr";
import TipMgr from "../../../lib/tips/TipMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { UINode } from "../../../lib/ui/UINode";
import Formate from "../../../lib/utils/Formate";
import { PostAudioName, PostMsgEnum } from "../../../module/post/postConfig";
import { PostModule } from "../../../module/post/postModule";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import ToolExt from "../../common/ToolExt";
import MsgEnum from "../../event/MsgEnum";
import { JsonMgr } from "../../mgr/JsonMgr";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { PlayerRouteName, ShopRouteName } from "../../../module/player/PlayerConstant";
import { PostHarvestResponse } from "../../net/protocol/Post";
import { ItemEnum } from "../../../lib/common/ItemEnum";
import { TimeUtils } from "../../../lib/utils/TimeUtils";
import { AudioMgr, AudioName } from "../../../../platform/src/AudioHelper";
import { BadgeMgr, BadgeType } from "../../mgr/BadgeMgr";
import { LangMgr } from "../../mgr/LangMgr";
import TickerMgr from "../../../lib/ticker/TickerMgr";
import { FmConfig } from "../../GameDefine";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "../../../lib/utils/FmUtils";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

const greenColor = "#00af04";
const redColor = "#e10000";

const item_6: number = 6;

enum POST_STATE {
  NONE_STATE = "NONE_STATE",
  CARRY_STATE = "CARRY_STATE",
  AWARD_STATE = "AWARD_STATE",
}

class PostItem {
  id: number;
  num: number;
  /**权重 */
  weight: number;
  /**是否解锁 */
  lock: boolean;
  /**是否是每日奖励 */
  dayAward: boolean;
  /**解锁等级 */
  openLv: number;
}

@ccclass("UIPost")
export class UIPost extends UINode {
  protected _openAct: boolean = true; // 打开动作

  private _levelNode: Node;
  private _timeNode: Node;
  private _postCountNode: Node;
  private _speedNumNode: Node;
  private _contentAwardNode: Node;
  private _contentNoneNode: Node;
  private _btnPostAwardGetNode: Node;
  private _btnGoPostNode: Node;

  private _state: POST_STATE = POST_STATE.NONE_STATE;

  private _oddsItemList: PostItem[] = [];

  private _oddsIndex: number = 0;

  private tween_leveHit = null;

  private _tickId: number;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_POST}?prefab/ui/UIPost`;
  }

  protected onRegEvent(): void {
    MsgMgr.on(PostMsgEnum.POST_REFRESH, this.initMain, this);
    MsgMgr.on(MsgEnum.ON_PLAYER_NUM_UPDATE, this.upShowItemNum, this);
    MsgMgr.on(MsgEnum.ON_EXIST_ITEM_CHANGE, this.upShowItemNum, this);
  }

  protected onDelEvent(): void {
    MsgMgr.off(PostMsgEnum.POST_REFRESH, this.initMain, this);
    MsgMgr.off(MsgEnum.ON_PLAYER_NUM_UPDATE, this.upShowItemNum, this);
    MsgMgr.off(MsgEnum.ON_EXIST_ITEM_CHANGE, this.upShowItemNum, this);
  }

  protected onEvtShow(): void {
    this.getNode("levelup").active = false;
    this.getNode("leveHit").active = false;
    this.getNode("btn_text1").active = FmConfig.isDebug;

    this.setMessage();

    this._levelNode = this.getNode("lbl_lv");
    this._timeNode = this.getNode("lbl_time");
    this._postCountNode = this.getNode("lbl_post_count");
    this._speedNumNode = this.getNode("lbl_speed_num");
    this._contentAwardNode = this.getNode("content_award");
    this._contentNoneNode = this.getNode("content_none");
    this._btnPostAwardGetNode = this.getNode("btn_post_award_get");
    this._btnGoPostNode = this.getNode("btn_go_post");

    this.getOddsItemList();
    this.set_item_6_icon();
    PostModule.api.getPostInfo(() => {});
    this.upShowItemNum(6);

    BadgeMgr.instance.setBadgeId(this._btnPostAwardGetNode, BadgeType.btn_tiao_zhan.btn_post.btn_post_award_get.id);
    BadgeMgr.instance.setBadgeId(this._btnGoPostNode, BadgeType.btn_tiao_zhan.btn_post.btn_go_post.id);
    BadgeMgr.instance.setBadgeId(this.getNode("btn_lv_add"), BadgeType.btn_tiao_zhan.btn_post.btn_lv_add.id);

    const c_post = JsonMgr.instance.jsonList.c_post;
    const list = Object.keys(c_post);
    const maxLevelIs = PostModule.data.message.level >= list.length;

    if (!maxLevelIs && PostModule.data.message.historyCount >= c_post[PostModule.data.message.level + 1].need) {
      TickerMgr.setTimeout(1, () => {
        if (isValid(this.node) == false) {
          return;
        }
        this.getNode("xinshouxindao").active = true;
      });

      TickerMgr.setTimeout(5, () => {
        if (isValid(this.node) == false) {
          return;
        }
        this.getNode("xinshouxindao").active = false;
      });
    }
  }

  private setMessage() {
    this.getNode("lab2").getComponent(Label).string = LangMgr.txMsgCode(119, []);
    this.getNode("lbl_award_title").getComponent(Label).string = LangMgr.txMsgCode(120, []);
  }

  private set_item_6_icon() {
    ToolExt.setItemIcon(this.getNode("item_6"), item_6, this);
  }

  private upShowItemNum(id: number) {
    switch (id) {
      case 6:
        this.set_lbl_item6();
        break;
    }
  }

  private set_lbl_item6() {
    let xianYu = PlayerModule.data.getItemNum(ItemEnum.仙玉_6);
    let fateLab: Node = this.getNode("lbl_item6");
    fateLab.getComponent(Label).string = Formate.format(xianYu);
  }

  private initMain() {
    this.set_lbl_lv();
    this.set_lbl_time();
    this.set_lbl_post_count();
    this.set_lbl_speed_num();

    this.changeStateEnum();
  }

  private changeStateEnum() {
    if (PostModule.data.lastDeadline <= 0) {
      this._state = POST_STATE.NONE_STATE;
    } else if (PostModule.data.rewardList.length <= 0 && PostModule.data.lastDeadline > 0) {
      this._state = POST_STATE.CARRY_STATE;
    } else if (PostModule.data.rewardList.length > 0) {
      this._state = POST_STATE.AWARD_STATE;
    }
    this.changeStateMain();
  }

  private changeStateMain() {
    switch (this._state) {
      case POST_STATE.NONE_STATE:
        this.setNoneeState();
        break;

      case POST_STATE.CARRY_STATE:
        this.setCarryState();
        break;

      case POST_STATE.AWARD_STATE:
        this.setAwardState();
        break;
    }
  }

  private setNoneeState() {
    this.getNode("nono_state").active = true;
    this.getNode("btn_go_post").active = true;

    this.getNode("lay_time").active = false;
    this.getNode("spr_time_bg").active = false;
    this.getNode("btn_speed_post").active = false;
    this.getNode("award_state").active = false;
  }

  private setCarryState() {
    this.getNode("nono_state").active = true;
    this.getNode("lay_time").active = true;
    this.getNode("spr_time_bg").active = true;
    this.getNode("btn_speed_post").active = true;

    this.getNode("btn_go_post").active = false;
    this.getNode("award_state").active = false;
  }

  private setAwardState() {
    this.getNode("lay_time").active = false;
    this.getNode("spr_time_bg").active = false;
    this.getNode("nono_state").active = false;

    this.getNode("award_state").active = true;

    this._contentAwardNode.destroyAllChildren();

    let itmeMap = ToolExt.traAwardItemMapList(PostModule.data.rewardList);
    let newAwardList = ToolExt.minuteItemLayer(itmeMap, 5);

    for (let i = 0; i < newAwardList.length; i++) {
      let list = newAwardList[i];
      let horLayer = ToolExt.clone(this.getNode("horLayer"), this);
      this._contentAwardNode.addChild(horLayer);
      horLayer.active = true;

      for (let j = 0; j < list.length; j++) {
        let info = list[j];
        let btn_item = ToolExt.clone(this.getNode("btn_item"), this);
        horLayer.addChild(btn_item);
        btn_item.active = true;
        FmUtils.setItemNode(btn_item, info.id, info.num);
      }
    }
  }

  private set_lbl_lv() {
    this._levelNode.getComponent(Label).string = `${PostModule.data.level}级`;
  }

  private set_lbl_post_count() {
    if (PostModule.data.dailyMaxCount == 0) {
      this._postCountNode.active = false;
      return;
    }
    this._postCountNode.active = true;

    let param = LangMgr.txMsgCode(215, [
      String(PostModule.data.dailyMaxCount - PostModule.data.dailyCount),
      String(PostModule.data.dailyMaxCount),
    ]);
    let str = LangMgr.txMsgCode(121, [param]);
    this._postCountNode.getComponent(Label).string = str;
  }

  private set_lbl_time() {
    FmUtils.setCd(this._timeNode, PostModule.data.lastDeadline);
  }

  private set_lbl_speed_num() {
    let needNum = this.getSpeedCost();
    let myNum = PlayerModule.data.getItemNum(ItemEnum.仙玉_6);
    this._speedNumNode.getComponent(Label).string = Formate.format(myNum) + "/" + Formate.format(needNum);
    let color = greenColor;
    if (needNum > myNum) {
      color = redColor;
    }
    ToolExt.setLabColor(this._speedNumNode.getComponent(Label), color);
  }

  /**计算出几率获取的道具列表 */
  private getOddsItemList() {
    this._oddsItemList = [];
    this.getDayAwardData();
    this.getOpenLvAwardData();
    this.getUnLockLvAwardData();
  }

  /**每日首次奖励数据 */
  private getDayAwardData() {
    let c_post = JsonMgr.instance.jsonList.c_post;
    let level = PostModule.data.level;
    let curInfo = c_post[level];
    let reward1List = curInfo.reward1List;
    for (let i = 0; i < reward1List.length; i++) {
      let info = reward1List[i];
      let data: PostItem = {
        id: info[0],
        num: info[1],
        weight: -1,
        lock: true,
        dayAward: true,
        openLv: -1,
      };
      this._oddsItemList.push(data);
    }
  }

  /**获取等级奖励数据 */
  private getOpenLvAwardData() {
    let c_post = JsonMgr.instance.jsonList.c_post;
    let level = PostModule.data.level;
    let curInfo = c_post[level];
    let reward2List = curInfo.reward2List;

    let maxWeight: number = 0;
    for (let i = 0; i < reward2List.length; i++) {
      let info = reward2List[i];
      maxWeight += info[2];
    }

    for (let i = 0; i < reward2List.length; i++) {
      let info = reward2List[i];
      let data: PostItem = {
        id: info[0],
        num: info[1],
        weight: info[2] / maxWeight,
        lock: true,
        dayAward: false,
        openLv: -1,
      };
      this._oddsItemList.push(data);
    }
  }

  /**获取未解锁的等级奖励 */
  private getUnLockLvAwardData() {
    let c_post = JsonMgr.instance.jsonList.c_post;
    let level = PostModule.data.level;
    let list = Object.keys(c_post);

    for (let i = level; i < list.length; i++) {
      let db = c_post[list[i]];
      let startIndex: number = 0;
      let reward2List = db.reward2List;

      if (level != 1) startIndex = c_post[list[i - 1]].reward2List.length;

      for (let j = startIndex; j < reward2List.length; j++) {
        let info = reward2List[j];
        let data: PostItem = {
          id: info[0],
          num: info[1],
          weight: 0,
          lock: false,
          dayAward: false,
          openLv: db.id,
        };
        this._oddsItemList.push(data);
      }
    }
  }

  /**获取我的加速消耗 */
  private getSpeedCost(): number {
    if (PostModule.data.lastDeadline < TimeUtils.serverTime) {
      return 0;
    }
    let c_post = JsonMgr.instance.jsonList.c_post;
    let postLevel = PostModule.data.level;
    let cur_postInfo = c_post[postLevel];
    let speedCostList = cur_postInfo.speedCostList;
    let time = (PostModule.data.lastDeadline - TimeUtils.serverTime) / 1000;
    let num1 = Math.ceil(time / speedCostList[0]);
    let num2 = num1 * speedCostList[1];
    return num2;
  }

  public tick(dt: number): void {
    let maxIndex = this._oddsIndex + 2;
    if (this._oddsIndex >= this._oddsItemList.length) return;

    let nodes = [];
    for (let i = this._oddsIndex; i < this._oddsItemList.length && i < maxIndex; i++) {
      let info = this._oddsItemList[i];
      let node = ToolExt.clone(this.getNode("oods_item"));
      nodes.push(node);
      this.set_oddsItemInfo(node, info);
      this._oddsIndex += 1;
    }

    nodes.forEach((node) => this._contentNoneNode.addChild(node));
  }

  private set_oddsItemInfo(node: Node, info: PostItem) {
    node.active = true;
    if (info.weight == -1) {
      node["lbl_weight"].active = false;
      node["dayAward"].active = true;
      node["lbl_day_hit"].getComponent(Label).string = PostModule.data.dailyCount == 0 ? "首次必得" : "明日再来";
      node["dayAward"].getComponent(Sprite).grayscale = PostModule.data.dailyCount == 0 ? false : true;
    } else {
      node["lbl_weight"].active = true;
      node["dayAward"].active = false;
      node["lbl_weight"].getComponent(Label).string = (info.weight * 100).toFixed(2) + "%";
    }

    if (info.lock == false) {
      node["dayAward"].active = false;
      node["lbl_weight"].getComponent(Label).string = `${info.openLv} 级解锁`;
      ToolExt.setLabColor(node["lbl_weight"].getComponent(Label), redColor);
      this.setUnItemState(node["Item"], false);
    } else {
      this.setUnItemState(node["Item"], true);
      ToolExt.setLabColor(node["lbl_weight"].getComponent(Label), greenColor);
    }

    FmUtils.setItemNode(node["Item"], info.id, info.num);
  }

  private setUnItemState(item: Node, colorBool: boolean) {
    let color = new Color(100, 100, 100, 255);
    if (colorBool == true) {
      color = new Color(255, 255, 255, 255);
    }
    item.getComponentsInChildren(Sprite).forEach((sprite) => {
      sprite.color = color;
    });
    item.getComponentsInChildren(Label).forEach((label) => {
      label.color = color;
    });
  }

  private on_click_btn_close() {
    // 关闭窗口 effect_guanbiyinxiao
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  private on_click_btn_addItem6() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    UIMgr.instance.showDialog(ShopRouteName.UIShopCenter, { tabIdx: 6 });
    // TipMgr.showTip("购买仙玉----暂时为重置次数");
    // PostModule.api.testResetDailyCount(() => {});
  }

  private on_click_btn_lv_add() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    UIMgr.instance.showDialog("UIPostLevelUp", {
      keepMask: false,
      data: PostModule.data.message,
      callback: () => {
        log.log("驿站升级特效");
        this.getNode("levelup").active = true;
        const skeleton = this.getNode("levelup").getComponent(sp.Skeleton);
        skeleton.setCompleteListener(() => {
          this.getNode("levelup").active = false;
        });
        AudioMgr.instance.playEffect(AudioName.Effect.武将升级);
        skeleton.setAnimation(0, "action", false);
        this.showLevelHit();
        this.getOddsItemList();

        for (let i = 0; i < this._oddsItemList.length; i++) {
          let info = this._oddsItemList[i];
          let node = this._contentNoneNode.children[i];

          if (!node) {
            node = ToolExt.clone(this.getNode("oods_item"));
            node.active = true;
          }
          this.set_oddsItemInfo(node, info);
        }
        let max = this._oddsItemList.length;
        if (max > this._contentNoneNode.children.length) {
          for (let i = max; i > this._contentNoneNode.children.length; i++) {
            this._contentNoneNode.children[i].active = false;
          }
        }
      },
    });
  }

  private showLevelHit() {
    if (this.tween_leveHit) {
      this.tween_leveHit.stop();
      this.tween_leveHit = null;
    }
    this.getNode("leveHit").active = false;
    this.getNode("leveHit").setPosition(v3(0, -100, 0));
    this.getNode("leveHit").active = true;
    this.tween_leveHit = tween(this.getNode("leveHit"))
      .by(1, { position: v3(0, 100, 0) })
      .call(() => {
        this.getNode("leveHit").active = false;
      })
      .start();
  }

  private on_click_btn_go_post() {
    AudioMgr.instance.playEffect(PostAudioName.Effect.点击出发按钮);
    if (PostModule.data.dailyMaxCount != 0 && PostModule.data.dailyCount >= PostModule.data.dailyMaxCount) {
      TipMgr.showTip("今日已到达上限");
      return;
    }
    UIMgr.instance.showDialog("UIPostBet");
  }

  private on_click_btn_speed_post() {
    AudioMgr.instance.playEffect(PostAudioName.Effect.点击加速按钮);
    UIMgr.instance.showDialog("UIPostSpeed", { num: this.getSpeedCost() });
  }

  private on_click_btn_wenhao() {
    AudioMgr.instance.playEffect(522);
    UIMgr.instance.showDialog(PlayerRouteName.UIHelpPop, { titleId: -1, desId: 13 });
  }

  private on_click_btn_post_award_get() {
    AudioMgr.instance.playEffect(PostAudioName.Effect.点击领取按钮);
    PostModule.api.getPostAward((res: PostHarvestResponse) => {
      if (res.resAddList.length > 0) {
        MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: res.resAddList });
      }
    });
  }

  protected onEvtClose(): void {
    if (this.tween_leveHit) {
      this.tween_leveHit.stop();
      this.tween_leveHit = null;
    }
  }
}
