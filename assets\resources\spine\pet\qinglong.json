{"skeleton": {"hash": "te3/B+UJZWIhOPb5JkjvlqhoakE=", "spine": "3.8.75", "x": -223.64, "y": -67, "width": 439.05, "height": 449.89, "images": "./images/", "audio": "D:/spine导出/灵兽动画/青龙"}, "bones": [{"name": "root", "scaleX": -1}, {"name": "bone", "parent": "root", "length": 669.9, "rotation": -90, "x": -12.34, "y": -119.47, "scaleX": 0.5959, "scaleY": 0.5959}, {"name": "bone2", "parent": "bone", "length": 68.45, "rotation": 90.87, "x": -405.69, "y": 322.79, "scaleX": 1.5506, "scaleY": 1.5506}, {"name": "lj<PERSON>", "parent": "bone", "x": -55.5, "y": -18.47}, {"name": "ljbone2", "parent": "lj<PERSON>", "length": 50, "rotation": 179.04, "x": -93.35, "y": 1.22}, {"name": "ljbone3", "parent": "ljbone2", "length": 50, "x": 50}, {"name": "ljbone4", "parent": "ljbone3", "length": 50, "x": 50}, {"name": "ljbone5", "parent": "ljbone4", "length": 50, "x": 50}, {"name": "ljbone6", "parent": "ljbone5", "length": 50, "x": 50}, {"name": "ljbone7", "parent": "ljbone6", "length": 50, "x": 50}, {"name": "ljbone8", "parent": "ljbone7", "length": 50, "x": 50}, {"name": "ljbone9", "parent": "ljbone8", "length": 50, "x": 50}, {"name": "ljbone10", "parent": "ljbone9", "length": 50, "x": 50}, {"name": "ljbone11", "parent": "ljbone10", "length": 50, "x": 50}, {"name": "ljbone12", "parent": "ljbone11", "length": 50, "x": 50}, {"name": "ljbone13", "parent": "ljbone12", "length": 50, "x": 50}, {"name": "ljbone14", "parent": "ljbone13", "length": 50, "x": 50}, {"name": "ljbone15", "parent": "ljbone14", "length": 50, "x": 50}, {"name": "ljbone16", "parent": "ljbone15", "length": 50, "x": 50}, {"name": "ljbone17", "parent": "ljbone16", "length": 50, "x": 50}, {"name": "ljbone18", "parent": "ljbone17", "length": 50, "x": 50}, {"name": "ljbone19", "parent": "ljbone18", "length": 50, "x": 50}, {"name": "ljbone20", "parent": "ljbone19", "length": 50, "x": 50}, {"name": "ljbone21", "parent": "ljbone20", "length": 50, "x": 50}, {"name": "ljbone22", "parent": "ljbone21", "length": 50, "x": 50}, {"name": "ljbone23", "parent": "ljbone22", "length": 50, "x": 50}, {"name": "ljbone24", "parent": "ljbone23", "length": 50, "x": 50}, {"name": "ljbone25", "parent": "ljbone24", "length": 50, "x": 50}, {"name": "ljbone26", "parent": "ljbone25", "length": 50, "x": 50}, {"name": "ljbone27", "parent": "ljbone26", "length": 50, "x": 50}, {"name": "ljbone28", "parent": "ljbone27", "length": 50, "x": 50}, {"name": "ljbone29", "parent": "ljbone28", "length": 50, "x": 50}, {"name": "ljbone30", "parent": "ljbone29", "length": 50, "x": 50}, {"name": "ljbone31", "parent": "ljbone30", "length": 50, "x": 50}, {"name": "ljbone32", "parent": "ljbone31", "length": 50, "x": 50}, {"name": "ljbone33", "parent": "ljbone32", "length": 50, "x": 50}, {"name": "ljbone34", "parent": "ljbone33", "length": 50, "x": 50}, {"name": "ljbone35", "parent": "ljbone34", "length": 50, "x": 50}, {"name": "ljbone36", "parent": "ljbone35", "length": 50, "x": 50}, {"name": "ljbone37", "parent": "ljbone36", "length": 50, "x": 50}, {"name": "ljbone38", "parent": "ljbone37", "length": 50, "x": 50}, {"name": "ljbone39", "parent": "ljbone38", "length": 50, "x": 50}, {"name": "ljbone40", "parent": "ljbone39", "length": 50, "x": 50}, {"name": "ljbone41", "parent": "ljbone40", "length": 50, "x": 50}, {"name": "ljbone42", "parent": "lj<PERSON>", "x": -227.2, "y": 263.17, "color": "21ff00ff"}, {"name": "ljbone43", "parent": "lj<PERSON>", "x": -327.51, "y": -197.08, "color": "21ff00ff"}, {"name": "ljbone44", "parent": "lj<PERSON>", "x": -455.31, "y": 314.13, "color": "21ff00ff"}, {"name": "ljbone45", "parent": "lj<PERSON>", "x": -390.6, "y": -88.69, "color": "21ff00ff"}, {"name": "ljbone<PERSON>", "parent": "lj<PERSON>", "x": -534.58, "y": 160.45, "color": "21ff00ff"}, {"name": "ljbone47", "parent": "lj<PERSON>", "x": -618.27, "y": -151.78, "color": "21ff00ff"}, {"name": "ljbone48", "parent": "lj<PERSON>", "x": -111.4, "y": -191.65, "color": "28ff00ff"}, {"name": "ljbone49", "parent": "ljbone29", "length": 60.1, "rotation": 144.98, "x": -12.91, "y": 5.78}, {"name": "ljbone50", "parent": "ljbone49", "length": 78.93, "rotation": -131.97, "x": 59.38, "y": -0.52}, {"name": "ljbone51", "parent": "ljbone50", "length": 26.64, "rotation": 25.44, "x": 78.93}, {"name": "ljbone52", "parent": "ljbone51", "length": 25.72, "rotation": 29.33, "x": 26.64}, {"name": "ljbone53", "parent": "ljbone28", "length": 40.52, "rotation": 25.46, "x": 47.85, "y": 17.12}, {"name": "ljbone54", "parent": "ljbone53", "length": 28.64, "rotation": 18.31, "x": 40.52}, {"name": "ljbone55", "parent": "ljbone54", "length": 27.32, "rotation": 15.49, "x": 28.64}, {"name": "ljbone56", "parent": "ljbone7", "length": 65.14, "rotation": -60, "x": 16.23, "y": -26.14}, {"name": "ljbone62", "parent": "ljbone56", "length": 32.37, "rotation": -165.79, "x": 63.79, "y": -0.53}, {"name": "ljbone57", "parent": "ljbone62", "rotation": -166.26, "x": 33.43, "y": -1.96, "transform": "noRotationOrReflection"}, {"name": "ljbone58", "parent": "ljbone8", "length": 40.01, "rotation": 135.59, "x": 3.39, "y": 31.62}, {"name": "ljbone59", "parent": "ljbone58", "length": 32.75, "rotation": -49.48, "x": 40.01}, {"name": "ljbone60", "parent": "ljbone59", "length": 25.28, "rotation": 0.87, "x": 32.75}, {"name": "ljbone61", "parent": "ljbone60", "length": 22.63, "rotation": -80.8, "x": 25.28}, {"name": "ljbone63", "parent": "ljbone3", "length": 24.06, "rotation": 8.3, "x": 28.63, "y": 9.21}, {"name": "bone3", "parent": "bone", "length": 329, "rotation": 89.6, "x": -613.41, "y": 270.61}, {"name": "light", "parent": "bone3", "rotation": 89.6, "x": -237.43, "y": 61.48}, {"name": "ljbone64", "parent": "ljbone63", "length": 72.53, "rotation": 118.96, "x": 30.73, "y": -6.8}, {"name": "ljbone65", "parent": "ljbone64", "length": 77.54, "rotation": 45.56, "x": 72.53}, {"name": "ljbone66", "parent": "ljbone65", "length": 78.83, "rotation": -8.09, "x": 77.54}, {"name": "ljbone67", "parent": "ljbone66", "length": 77.48, "rotation": -22.54, "x": 78.84, "y": -1.19}, {"name": "ljbone68", "parent": "ljbone3", "length": 59.4, "rotation": -48.46, "x": 76.6, "y": -12.24}, {"name": "ljbone69", "parent": "ljbone68", "length": 65.51, "rotation": 5.9, "x": 59.4}, {"name": "ljbone70", "parent": "ljbone69", "length": 49.42, "rotation": -115.64, "x": 65.76, "y": -0.32}, {"name": "ljbone71", "parent": "ljbone70", "length": 78.57, "rotation": -45.37, "x": 49.42}, {"name": "ljbone72", "parent": "ljbone71", "length": 36.02, "rotation": 129.11, "x": 78.57}, {"name": "ljbone73", "parent": "ljbone3", "length": 49.02, "rotation": -168.57, "x": 3.38, "y": -42.03}, {"name": "ljbone74", "parent": "ljbone73", "length": 45.51, "rotation": -10.86, "x": 49.02}, {"name": "ljbone75", "parent": "ljbone74", "length": 45.73, "rotation": 30.03, "x": 46.16, "y": 0.52}, {"name": "ljbone76", "parent": "ljbone3", "length": 52.58, "rotation": -133.45, "x": -4.03, "y": -62.49}, {"name": "ljbone77", "parent": "ljbone76", "length": 50.49, "rotation": -17.31, "x": 52.58}, {"name": "ljbone78", "parent": "ljbone3", "length": 64.98, "rotation": 160.63, "x": -47.48, "y": -9.69}, {"name": "ljbone79", "parent": "ljbone78", "length": 70.71, "rotation": -7.96, "x": 64.98}, {"name": "ljbone80", "parent": "ljbone3", "length": 49.92, "rotation": 131.27, "x": -44.88, "y": 19.03}, {"name": "ljbone81", "parent": "ljbone80", "length": 59.52, "rotation": -4.25, "x": 49.23, "y": 0.47}, {"name": "ljbone82", "parent": "ljbone5", "length": 48.45, "rotation": -153.77, "x": 51.55, "y": -26.23}, {"name": "ljbone83", "parent": "ljbone82", "length": 51.11, "rotation": 5.75, "x": 48.08, "y": -0.28}, {"name": "bone11", "parent": "bone", "length": 188.33, "rotation": -89.15, "x": -85.12, "y": 424.78, "color": "0011ffff"}, {"name": "bone4", "parent": "bone11", "rotation": 87.54, "x": 201.24, "y": -51.54, "color": "0069ffff"}, {"name": "bone5", "parent": "bone4", "x": -53.73, "y": 5.06, "color": "0069ffff"}, {"name": "bone6", "parent": "bone4", "x": -63.55, "y": -80.57, "color": "0069ffff"}, {"name": "bone7", "parent": "bone4", "x": -101.18, "y": 92.33, "color": "0069ffff"}, {"name": "bone8", "parent": "bone4", "x": -136.09, "y": 21.43, "color": "0069ffff"}, {"name": "bone9", "parent": "bone4", "x": -139.36, "y": -80.02, "color": "0069ffff"}, {"name": "bone10", "parent": "bone4", "x": -143.72, "y": -174.38, "color": "0069ffff"}, {"name": "bone12", "parent": "bone11", "x": 123.31, "y": -264.71, "color": "0011ffff"}, {"name": "bone13", "parent": "bone12", "x": 67.76, "y": -38.78, "color": "0011ffff"}, {"name": "bone14", "parent": "bone12", "x": 92.62, "y": 27.52, "color": "0011ffff"}, {"name": "bone15", "parent": "bone12", "x": 113.22, "y": 105.55, "color": "0011ffff"}, {"name": "bone16", "parent": "bone12", "x": 161.86, "y": 51.5, "color": "0011ffff"}, {"name": "bone17", "parent": "bone12", "x": 217.9, "y": 46.78, "color": "0011ffff"}, {"name": "bone18", "parent": "bone11", "x": 363.2, "y": -60.02, "color": "0011ffff"}, {"name": "bone19", "parent": "bone18", "x": -31.57, "y": -25.8, "color": "0011ffff"}, {"name": "bone20", "parent": "bone18", "x": 26.37, "y": -29.93, "color": "0011ffff"}, {"name": "bone21", "parent": "bone18", "x": 80.45, "y": -35.66, "color": "0011ffff"}, {"name": "bone22", "parent": "bone11", "x": 354.79, "y": -149.96, "color": "0011ffff"}, {"name": "bone23", "parent": "bone22", "x": 33.57, "y": 19.02, "color": "0011ffff"}, {"name": "bone24", "parent": "bone22", "x": 83.3, "y": 4.51, "color": "0011ffff"}, {"name": "bone25", "parent": "bone22", "x": 135.62, "y": -28.4, "color": "0011ffff"}, {"name": "bone26", "parent": "bone22", "x": 187.72, "y": -37.21, "color": "0011ffff"}, {"name": "bone27", "parent": "bone11", "x": 687.02, "y": -183.88, "color": "0011ffff"}, {"name": "bone28", "parent": "bone27", "x": -98.46, "y": -41.87, "color": "0011ffff"}, {"name": "bone29", "parent": "bone27", "x": -37.94, "y": -91.31, "color": "0011ffff"}, {"name": "bone30", "parent": "bone27", "x": 34.84, "y": -119.27, "color": "0011ffff"}, {"name": "bone31", "parent": "bone11", "x": 112.02, "y": -381.18, "color": "0011ffff"}, {"name": "bone32", "parent": "bone31", "x": 73.21, "y": -79.55, "color": "0011ffff"}, {"name": "bone33", "parent": "bone31", "x": 145.88, "y": -29.12, "color": "0011ffff"}, {"name": "bone34", "parent": "bone31", "x": 214.19, "y": 2.56, "color": "0011ffff"}, {"name": "bone35", "parent": "bone11", "x": 85.37, "y": -609.62, "color": "0011ffff"}, {"name": "bone36", "parent": "bone35", "x": 245.64, "y": 92.07, "color": "0011ffff"}, {"name": "bone37", "parent": "bone35", "x": 143.79, "y": 48.25, "color": "0011ffff"}, {"name": "bone38", "parent": "bone35", "x": 256.51, "y": 9.58, "color": "0011ffff"}, {"name": "bone39", "parent": "bone35", "x": 102.59, "y": -31.2, "color": "0011ffff"}, {"name": "bone40", "parent": "bone35", "x": 268.5, "y": -47.99, "color": "0011ffff"}, {"name": "bone41", "parent": "bone11", "x": 498.67, "y": -269.77, "color": "0011ffff"}, {"name": "bone42", "parent": "bone41", "x": 47.04, "y": -21.81, "color": "0011ffff"}, {"name": "bone43", "parent": "bone41", "x": 13.53, "y": -49.85, "color": "0011ffff"}, {"name": "bone44", "parent": "bone41", "x": -26.94, "y": -46.97, "color": "0011ffff"}, {"name": "ljbone84", "parent": "ljbone39", "x": -63.89, "y": 20.05}, {"name": "ljbone85", "parent": "ljbone40", "x": -5.15, "y": -44.05}, {"name": "ljbone86", "parent": "ljbone41", "x": -16.42, "y": 34.13}, {"name": "ljbone87", "parent": "ljbone2", "x": -14.73, "y": -28}, {"name": "ljbone88", "parent": "ljbone87", "x": -2.83, "y": -35.76}], "slots": [{"name": "back", "bone": "root"}, {"name": "g2", "bone": "bone3", "attachment": "g2"}, {"name": "g1", "bone": "light", "attachment": "g1", "blend": "additive"}, {"name": "sp5", "bone": "bone3", "attachment": "sp5"}, {"name": "sp4", "bone": "bone3", "attachment": "sp4"}, {"name": "yw2", "bone": "bone", "attachment": "yw2"}, {"name": "s5", "bone": "ljbone53", "attachment": "s5"}, {"name": "y8", "bone": "bone"}, {"name": "wb", "bone": "bone", "attachment": "wb"}, {"name": "st3", "bone": "bone", "attachment": "st3"}, {"name": "yw1", "bone": "bone", "attachment": "yw1"}, {"name": "y7", "bone": "bone", "attachment": "y7"}, {"name": "y6", "bone": "bone", "attachment": "y6"}, {"name": "s4", "bone": "ljbone49", "attachment": "s4"}, {"name": "s3", "bone": "ljbone50", "attachment": "s3"}, {"name": "s2", "bone": "ljbone58", "attachment": "s2"}, {"name": "m10", "bone": "bone", "attachment": "m10"}, {"name": "m9", "bone": "bone", "attachment": "m9"}, {"name": "y5", "bone": "bone", "attachment": "y5"}, {"name": "st2", "bone": "bone", "attachment": "st2"}, {"name": "m8", "bone": "ljbone6", "attachment": "m8"}, {"name": "m7", "bone": "ljbone5", "attachment": "m7"}, {"name": "m6", "bone": "ljbone4", "attachment": "m6"}, {"name": "m5", "bone": "ljbone5", "attachment": "m5"}, {"name": "st1", "bone": "bone", "attachment": "st1"}, {"name": "sb2", "bone": "ljbone56", "attachment": "sb2"}, {"name": "sb1", "bone": "ljbone62", "attachment": "sb1"}, {"name": "s1", "bone": "ljbone57", "attachment": "s1"}, {"name": "m4", "bone": "ljbone3", "attachment": "m4"}, {"name": "m3", "bone": "ljbone3", "attachment": "m3"}, {"name": "m2", "bone": "ljbone3", "attachment": "m2"}, {"name": "m1", "bone": "ljbone3", "attachment": "m1"}, {"name": "hx2", "bone": "ljbone3", "attachment": "hx2"}, {"name": "zz1", "bone": "ljbone63", "attachment": "zz1"}, {"name": "l", "bone": "ljbone3", "attachment": "l"}, {"name": "j2", "bone": "ljbone3", "attachment": "j2"}, {"name": "j1", "bone": "ljbone3", "attachment": "j1"}, {"name": "sp3", "bone": "ljbone3", "attachment": "sp3"}, {"name": "mm2", "bone": "ljbone3", "attachment": "mm2"}, {"name": "by", "bone": "ljbone3", "attachment": "by"}, {"name": "yj", "bone": "ljbone3", "attachment": "yj"}, {"name": "mm1", "bone": "ljbone3", "attachment": "mm1"}, {"name": "hx1", "bone": "ljbone3", "attachment": "hx1"}, {"name": "sp2", "bone": "ljbone8", "attachment": "sp2"}, {"name": "sp1", "bone": "bone3", "attachment": "sp1"}, {"name": "y4", "bone": "bone", "attachment": "y4"}, {"name": "y3", "bone": "bone", "attachment": "y3"}, {"name": "y2", "bone": "bone", "attachment": "y2"}, {"name": "y1", "bone": "bone", "attachment": "y1"}, {"name": "lj<PERSON>", "bone": "lj<PERSON>", "attachment": "lj<PERSON>"}], "path": [{"name": "lj<PERSON>", "bones": ["ljbone2", "ljbone41", "ljbone40", "ljbone39", "ljbone38", "ljbone37", "ljbone36", "ljbone35", "ljbone34", "ljbone33", "ljbone32", "ljbone31", "ljbone30", "ljbone29", "ljbone28", "ljbone27", "ljbone26", "ljbone25", "ljbone24", "ljbone23", "ljbone22", "ljbone21", "ljbone20", "ljbone19", "ljbone18", "ljbone17", "ljbone16", "ljbone15", "ljbone14", "ljbone13", "ljbone12", "ljbone11", "ljbone10", "ljbone9", "ljbone8", "ljbone7", "ljbone6", "ljbone5", "ljbone4", "ljbone3"], "target": "lj<PERSON>", "rotateMode": "chainScale", "position": 0.0564, "spacing": -1}], "skins": [{"name": "default", "attachments": {"m9": {"m9": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-449.07, 44.21, -449.07, -2.79, -496.07, -2.79, -496.07, 44.21], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 47, "height": 47}}, "sb1": {"sb1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-15.49, 18.29, 45.04, 15.19, 42.39, -20.21, -18.14, -17.11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 60, "height": 35}}, "sb2": {"sb2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [79.33, -15.24, -19.74, -35, -27.79, 10.95, 71.29, 30.71], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 46}}, "j1": {"j1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.98, 6.96, 72.43, -47.73, -67.4, -163.52, -115.85, -108.84], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 72, "height": 178}}, "j2": {"j2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-33.44, 77.91, 52.02, -18.54, -81.53, -129.13, -166.99, -32.68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 127, "height": 170}}, "zz1": {"zz1": {"type": "mesh", "uvs": [0.35443, 0.24657, 0.36186, 0.13477, 0.50114, 0.03857, 0.69057, 0, 0.919, 0.02297, 1, 0.14517, 0.98586, 0.21147, 0.84471, 0.29987, 0.73329, 0.41427, 0.72771, 0.55467, 0.80943, 0.70287, 0.854, 0.81857, 0.76486, 0.94337, 0.60329, 0.97197, 0.40829, 0.97717, 0.28015, 0.98107, 0.14272, 0.99667, 0.04243, 0.93297, 0, 0.82117, 0.04058, 0.75877, 0.15015, 0.78867, 0.25972, 0.72757, 0.31729, 0.68467, 0.26343, 0.54687, 0.28943, 0.38177, 0.5923, 0.11179, 0.50816, 0.23178, 0.44894, 0.41722, 0.42401, 0.59611, 0.41777, 0.67901, 0.51439, 0.65065, 0.24043, 0.86612, 0.34064, 0.86081, 0.45148, 0.84593, 0.58661, 0.82892, 0.70352, 0.76303, 0.10985, 0.85655], "triangles": [16, 31, 15, 16, 36, 31, 15, 32, 14, 15, 31, 32, 14, 33, 13, 14, 32, 33, 33, 34, 13, 21, 32, 31, 31, 36, 20, 32, 21, 33, 29, 21, 22, 21, 29, 33, 33, 29, 34, 29, 30, 34, 34, 30, 35, 29, 22, 28, 29, 28, 30, 30, 28, 9, 28, 27, 9, 28, 23, 27, 3, 4, 25, 25, 2, 3, 27, 26, 8, 27, 0, 26, 26, 25, 7, 26, 1, 25, 17, 36, 16, 13, 34, 12, 34, 35, 12, 12, 35, 11, 17, 18, 36, 31, 20, 21, 18, 19, 36, 36, 19, 20, 35, 10, 11, 35, 30, 10, 30, 9, 10, 28, 22, 23, 23, 24, 27, 24, 0, 27, 8, 26, 7, 7, 25, 5, 0, 1, 26, 9, 27, 8, 25, 4, 5, 1, 2, 25, 6, 7, 5], "vertices": [17.28, -8.62, 11.72, -11.66, 4.07, -9.91, -1.89, -4.8, -5.71, 3.4, -1.55, 9.66, 1.95, 11.14, 9.27, 9.09, 17.2, 8.79, 24.1, 12.73, 29.5, 19.78, 34.12, 24.65, 42.08, 25.38, 46.95, 20.9, 51.41, 14.63, 54.36, 10.53, 58.08, 6.46, 57.17, 1.29, 52.69, -3.4, 48.8, -3.9, 47.87, 0.59, 42.56, 2.4, 39.24, 3.04, 33.75, -2.79, 25.21, -6.79, 5.63, -4.75, 13.25, -3.99, 23.48, -0.49, 32.66, 3.95, 36.8, 6.18, 33.34, 8.53, 49.67, 5.84, 47.25, 8.98, 44.13, 12.2, 40.4, 16.15, 34.69, 18.06, 52.02, 1.26], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48, 6, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 20, 62, 64, 64, 66], "width": 42, "height": 60}}, "hx2": {"hx2": {"type": "mesh", "uvs": [0.33885, 0.17751, 0.40247, 0.11663, 0.48551, 0.07979, 0.56639, 0.06858, 0.59401, 0.07609, 0.64296, 0.0894, 0.67532, 0.12945, 0.62355, 0.19673, 0.58926, 0.22604, 0.54483, 0.26401, 0.49579, 0.29656, 0.44345, 0.33129, 0.39232, 0.36465, 0.32806, 0.40658, 0.26012, 0.44663, 0.21379, 0.48026, 0.16522, 0.51552, 0.10806, 0.5812, 0.04659, 0.65008, 0.00669, 0.72537, 0, 0.82629, 0.02071, 0.9192, 0.0714, 0.96886, 0.11993, 0.99289, 0.15636, 0.99439, 0.24219, 0.99793, 0.29247, 1, 0.36257, 0.99129, 0.45747, 0.96085, 0.55345, 0.92241, 0.63649, 0.89197, 0.70767, 0.86954, 0.77737, 0.8802, 0.85433, 0.89197, 0.90751, 0.9227, 0.98545, 0.986, 1, 0.9613, 1, 0.90418, 0.93557, 0.85787, 0.87841, 0.82391, 0.81606, 0.79921, 0.75682, 0.80075, 0.68304, 0.80847, 0.61757, 0.82545, 0.55417, 0.84398, 0.50325, 0.86713, 0.42739, 0.89337, 0.35361, 0.91653, 0.29333, 0.92734, 0.23473, 0.93975, 0.16234, 0.93067, 0.1032, 0.88977, 0.0614, 0.83979, 0.05018, 0.76861, 0.06955, 0.70651, 0.11544, 0.65502, 0.16336, 0.60655, 0.22147, 0.55051, 0.27449, 0.51113, 0.35096, 0.45055, 0.41418, 0.40966, 0.45764, 0.38289, 0.49779, 0.35816, 0.54775, 0.32333, 0.60281, 0.29001, 0.64767, 0.2476, 0.69967, 0.19762, 0.7221, 0.13552, 0.7221, 0.06131, 0.69151, 0.01739, 0.63339, 0, 0.56741, 0, 0.49782, 0.00767, 0.4472, 0.02476, 0.39601, 0.04783, 0.35633, 0.09225, 0.33447, 0.13241, 0.32814, 0.17171], "triangles": [36, 35, 37, 7, 6, 66, 66, 6, 67, 77, 76, 0, 1, 76, 75, 1, 0, 76, 6, 68, 67, 6, 69, 68, 6, 5, 69, 75, 74, 1, 1, 73, 2, 1, 74, 73, 4, 70, 5, 5, 70, 69, 2, 72, 3, 2, 73, 72, 3, 71, 4, 4, 71, 70, 3, 72, 71, 57, 15, 58, 15, 14, 58, 58, 13, 59, 58, 14, 13, 59, 12, 60, 59, 13, 12, 61, 60, 11, 60, 12, 11, 61, 11, 62, 11, 10, 62, 62, 10, 63, 10, 9, 63, 63, 9, 64, 9, 8, 64, 65, 8, 7, 65, 64, 8, 65, 7, 66, 22, 51, 23, 22, 21, 51, 21, 52, 51, 21, 20, 52, 20, 53, 52, 20, 19, 53, 53, 19, 54, 19, 18, 54, 54, 18, 55, 18, 17, 55, 55, 17, 56, 56, 16, 57, 56, 17, 16, 16, 15, 57, 27, 48, 47, 27, 26, 48, 26, 25, 48, 25, 24, 49, 24, 50, 49, 25, 49, 48, 24, 23, 50, 23, 51, 50, 27, 46, 28, 27, 47, 46, 28, 45, 29, 28, 46, 45, 37, 35, 34, 34, 38, 37, 33, 39, 34, 34, 39, 38, 30, 44, 43, 30, 29, 44, 29, 45, 44, 32, 40, 33, 33, 40, 39, 30, 42, 31, 30, 43, 42, 31, 41, 32, 32, 41, 40, 31, 42, 41], "vertices": [1, 76, 48.35, 20.79, 1, 1, 76, 42.35, 10.89, 1, 2, 75, 56.53, 24.09, 0.00289, 76, 32.6, 1.91, 0.99711, 2, 75, 68.24, 19.93, 0.01857, 76, 21.98, -4.56, 0.98143, 2, 75, 71.75, 17.45, 0.06802, 76, 17.85, -5.72, 0.93198, 2, 75, 77.97, 13.06, 0.173, 76, 10.52, -7.77, 0.827, 2, 75, 80.72, 7.23, 0.3396, 76, 4.26, -6.23, 0.6604, 2, 75, 70.62, 4.28, 0.54189, 76, 8.34, 3.47, 0.45811, 2, 75, 64.59, 3.75, 0.73428, 76, 11.73, 8.48, 0.26572, 2, 75, 56.78, 3.06, 0.87615, 76, 16.12, 14.98, 0.12385, 3, 74, 85.79, -32.33, 0.00094, 75, 48.56, 3.18, 0.95552, 76, 21.4, 21.28, 0.04354, 3, 74, 79.72, -26, 0.0087, 75, 39.79, 3.3, 0.98081, 76, 27.02, 28.01, 0.01049, 3, 74, 73.84, -19.8, 0.03817, 75, 31.25, 3.47, 0.96052, 76, 32.55, 34.53, 0.00131, 3, 74, 66.46, -12.01, 0.1127, 75, 20.51, 3.69, 0.88728, 76, 39.49, 42.72, 2e-05, 2, 74, 59.06, -3.62, 0.2483, 75, 9.35, 4.32, 0.7517, 2, 74, 53.41, 1.88, 0.43713, 75, 1.46, 4.16, 0.56287, 2, 74, 47.48, 7.65, 0.64055, 75, -6.81, 3.99, 0.35945, 3, 73, 61.5, -40.6, 0.00017, 74, 38.16, 13.6, 0.81222, 75, -17.59, 1.54, 0.18761, 3, 73, 71.59, -34.51, 0.00481, 74, 28.3, 20.05, 0.9189, 75, -29.11, -0.94, 0.07629, 3, 73, 78.45, -27.42, 0.02646, 74, 18.94, 23.18, 0.95155, 75, -37.91, -5.41, 0.02198, 3, 73, 80.54, -17.19, 0.08917, 74, 8.81, 20.63, 0.90698, 75, -43.21, -14.41, 0.00384, 3, 73, 78.37, -7.34, 0.21282, 74, 0.87, 14.41, 0.78704, 75, -44.36, -24.43, 0.00013, 2, 73, 71.18, -1.46, 0.39666, 74, -1.32, 5.39, 0.60334, 2, 73, 64.05, 1.76, 0.60456, 74, -1.14, -2.44, 0.39544, 3, 72, 117.36, 8.49, 0.00402, 73, 58.52, 2.49, 0.78412, 74, 0.6, -7.74, 0.21187, 3, 72, 104.23, 8.85, 0.0242, 73, 45.5, 4.2, 0.8872, 74, 4.69, -20.22, 0.0886, 3, 72, 96.53, 9.07, 0.08495, 73, 37.87, 5.2, 0.88884, 74, 7.09, -27.53, 0.02621, 3, 72, 85.81, 8.17, 0.20693, 73, 27.11, 5.41, 0.78833, 74, 11.56, -37.32, 0.00473, 3, 72, 71.29, 5.03, 0.39064, 73, 12.34, 3.79, 0.60921, 74, 19.41, -49.93, 0.00016, 2, 72, 56.6, 1.07, 0.5999, 73, -2.67, 1.36, 0.4001, 3, 72, 43.9, -2.06, 0.78155, 73, -15.63, -0.46, 0.21433, 5, 104.17, -46.46, 0.00412, 3, 72, 33.01, -4.37, 0.8859, 73, -26.7, -1.64, 0.08941, 5, 95.22, -39.84, 0.02469, 3, 72, 22.34, -3.27, 0.88738, 73, -37.2, 0.55, 0.0262, 5, 88.97, -31.13, 0.08642, 3, 72, 10.57, -2.06, 0.78961, 73, -48.79, 2.96, 0.00462, 5, 82.07, -21.52, 0.20576, 3, 72, 2.43, 1.1, 0.62952, 73, -56.55, 6.95, 0.0001, 5, 79.04, -13.33, 0.37037, 3, 72, -9.49, 7.62, 0.48148, 73, -67.75, 14.66, 0, 5, 76.01, -0.08, 0.51852, 2, 72, -11.72, 5.08, 0.41975, 5, 72.63, -0.1, 0.58025, 2, 72, -11.72, -0.81, 0.48148, 5, 68.23, -4, 0.51852, 2, 72, -1.86, -5.58, 0.62963, 5, 71.19, -14.54, 0.37037, 3, 72, 6.88, -9.07, 0.79377, 73, -53.17, -3.63, 0.00047, 5, 74.38, -23.41, 0.20576, 3, 72, 16.42, -11.62, 0.9075, 73, -43.94, -7.14, 0.00608, 5, 78.8, -32.23, 0.08642, 3, 72, 25.49, -11.46, 0.94551, 73, -34.91, -7.91, 0.02979, 5, 84.93, -38.91, 0.02469, 3, 72, 36.78, -10.66, 0.90122, 73, -23.6, -8.28, 0.09467, 5, 93.01, -46.83, 0.00412, 2, 72, 46.79, -8.91, 0.78069, 73, -13.46, -7.57, 0.21931, 2, 72, 56.49, -7.01, 0.59857, 73, -3.61, -6.67, 0.40143, 3, 72, 64.28, -4.62, 0.39387, 73, 4.38, -5.1, 0.60564, 74, 30.87, -53.26, 0.00049, 3, 72, 75.89, -1.92, 0.21339, 73, 16.2, -3.6, 0.78002, 74, 24.4, -43.25, 0.00659, 3, 72, 87.18, 0.47, 0.09112, 73, 27.68, -2.39, 0.87678, 74, 18.34, -33.43, 0.03211, 3, 72, 96.4, 1.58, 0.02822, 73, 36.97, -2.23, 0.87053, 74, 14.18, -25.13, 0.10125, 3, 72, 105.37, 2.86, 0.0056, 73, 46.02, -1.88, 0.76227, 74, 9.94, -17.12, 0.23212, 3, 72, 116.44, 1.92, 0.00039, 73, 56.94, -3.94, 0.57933, 74, 7.08, -6.38, 0.42027, 2, 73, 65.51, -9.06, 0.37281, 74, 7.99, 3.56, 0.62719, 3, 73, 71.34, -14.84, 0.19505, 74, 10.67, 11.32, 0.80431, 75, -35.27, -19.62, 0.00065, 3, 73, 72.29, -22.31, 0.079, 74, 17, 15.41, 0.91365, 75, -33.74, -12.25, 0.00734, 3, 73, 68.69, -28.37, 0.02222, 74, 24.02, 14.78, 0.9434, 75, -28.36, -7.7, 0.03438, 3, 73, 61.16, -32.92, 0.00362, 74, 31.38, 9.97, 0.89058, 75, -19.76, -5.84, 0.1058, 2, 74, 38.56, 4.75, 0.76106, 75, -11.01, -4.39, 0.23894, 2, 74, 46.99, -1.67, 0.572, 75, -0.52, -2.9, 0.428, 2, 74, 53.55, -7.93, 0.366, 75, 8.55, -2.63, 0.634, 2, 74, 63.38, -16.84, 0.1905, 75, 21.79, -1.89, 0.8095, 2, 74, 70.61, -24.52, 0.07673, 75, 32.33, -2.14, 0.92327, 3, 74, 75.45, -29.84, 0.02146, 75, 39.53, -2.44, 0.97543, 76, 22.74, 31.83, 0.00311, 3, 74, 79.92, -34.76, 0.00347, 75, 46.17, -2.71, 0.97688, 76, 18.34, 26.85, 0.01965, 2, 75, 54.61, -2.67, 0.92876, 76, 13.04, 20.27, 0.07124, 2, 75, 63.7, -3.1, 0.82062, 76, 6.98, 13.5, 0.17938, 2, 75, 71.76, -2.03, 0.65088, 76, 2.72, 6.56, 0.34912, 2, 75, 81.15, -0.7, 0.44742, 76, -2.17, -1.55, 0.55258, 2, 75, 86.95, 3.66, 0.25637, 76, -2.45, -8.81, 0.74364, 2, 75, 90.17, 10.59, 0.1177, 76, 0.9, -15.68, 0.8823, 2, 75, 87.83, 16.66, 0.04053, 76, 7.09, -17.69, 0.95947, 2, 75, 80.51, 22.03, 0.00952, 76, 15.87, -15.4, 0.99048, 2, 75, 71.35, 26.28, 0.00113, 76, 24.94, -10.97, 0.99887, 2, 75, 61.36, 30.04, 3e-05, 76, 34.17, -5.59, 0.99997, 1, 76, 40.35, -0.62, 1, 1, 76, 46.35, 4.95, 1, 1, 76, 49.8, 11.73, 1, 1, 76, 50.99, 16.91, 1, 1, 76, 50.09, 20.97, 1], "hull": 78, "edges": [0, 2, 2, 4, 4, 6, 10, 12, 12, 14, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 50, 52, 46, 48, 48, 50, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 22, 24, 24, 26, 18, 20, 20, 22, 14, 16, 16, 18, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 6, 8, 8, 10, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 0, 154], "width": 153, "height": 103}}, "wb": {"wb": {"type": "mesh", "uvs": [1, 0.28052, 0.92095, 0.26108, 0.82662, 0.19445, 0.7356, 0.08895, 0.66775, 0, 0.58997, 0, 0.5006, 0, 0.40131, 0, 0.30367, 0, 0.15804, 0.07229, 0.06205, 0.19168, 0, 0.31106, 0, 0.47764, 0, 0.59702, 0.03392, 0.74139, 0.14314, 0.86632, 0.20603, 0.94961, 0.29043, 1, 0.4344, 1, 0.54363, 1, 0.64127, 0.98015, 0.65616, 0.8802, 0.58997, 0.80524, 0.62472, 0.70807, 0.6181, 0.62478, 0.585, 0.54427, 0.57176, 0.4471, 0.64623, 0.45543, 0.71408, 0.49429, 0.80345, 0.47208, 0.86964, 0.42489, 0.9408, 0.37214, 1, 0.33604, 0.92835, 0.32353, 0.8178, 0.3135, 0.68933, 0.26338, 0.5743, 0.24583, 0.45777, 0.27842, 0.37262, 0.35861, 0.29793, 0.46137, 0.29494, 0.5942, 0.31286, 0.73705, 0.34424, 0.81976, 0.41594, 0.84231, 0.52052, 0.84983, 0.58475, 0.89745, 0.61015, 0.16814, 0.48914, 0.14057, 0.36963, 0.14559, 0.27701, 0.20072, 0.18738, 0.32102, 0.14405, 0.46889, 0.13658, 0.64934, 0.17841, 0.79219, 0.23966, 0.89996, 0.50259, 0.46638, 0.46225, 0.58919, 0.49064, 0.68693, 0.55786, 0.69194, 0.40399, 0.49646, 0.37411, 0.57415, 0.401, 0.68192], "triangles": [25, 56, 55, 25, 58, 56, 61, 56, 57, 56, 58, 57, 56, 59, 55, 61, 60, 56, 42, 61, 43, 42, 41, 61, 18, 44, 19, 17, 42, 18, 45, 44, 22, 45, 22, 21, 19, 45, 20, 20, 45, 21, 19, 44, 45, 58, 25, 24, 43, 61, 57, 51, 10, 50, 51, 50, 39, 11, 10, 51, 12, 11, 51, 60, 39, 59, 60, 59, 56, 40, 51, 39, 40, 39, 60, 13, 12, 51, 52, 13, 51, 52, 51, 40, 58, 24, 23, 41, 40, 60, 41, 60, 61, 14, 13, 52, 53, 52, 40, 53, 40, 41, 15, 14, 52, 22, 58, 23, 44, 57, 58, 44, 58, 22, 43, 57, 44, 53, 15, 52, 54, 53, 41, 54, 41, 42, 16, 15, 53, 54, 16, 53, 17, 54, 42, 16, 54, 17, 18, 43, 44, 42, 43, 18, 50, 49, 39, 48, 8, 7, 48, 7, 47, 49, 9, 8, 49, 8, 48, 37, 48, 47, 50, 9, 49, 10, 9, 50, 38, 48, 37, 49, 48, 38, 36, 55, 37, 39, 49, 38, 59, 38, 37, 55, 59, 37, 39, 38, 59, 47, 7, 6, 46, 5, 4, 46, 4, 3, 47, 6, 5, 46, 47, 5, 36, 47, 46, 36, 46, 35, 37, 47, 36, 27, 26, 36, 35, 27, 36, 26, 55, 36, 55, 26, 25, 35, 46, 3, 35, 3, 2, 34, 35, 2, 34, 2, 1, 30, 34, 33, 34, 28, 35, 29, 34, 30, 28, 27, 35, 34, 29, 28, 33, 1, 0, 34, 1, 33, 33, 0, 32, 31, 33, 32, 30, 33, 31], "vertices": [1, 40, 35.37, 5.1, 1, 2, 40, 14.91, 8.74, 0.98423, 41, 63.39, 10.78, 0.01577, 3, 40, -9.27, 19.69, 0.03661, 41, 37.95, 18.16, 0.96298, 42, 88.74, 16.93, 0.00041, 2, 41, 12.68, 31.5, 0.70164, 42, 63.79, 31.08, 0.29836, 2, 41, -6.33, 43, 0.29087, 42, 45.07, 43.18, 0.70913, 3, 41, -26.36, 40.79, 0.08737, 42, 24.86, 41.61, 0.89832, 43, 96.2, 18.79, 0.01431, 4, 41, -49.38, 38.24, 0.00253, 42, 1.65, 39.8, 0.60765, 43, 76.12, 30.93, 0.18982, 129, 14.51, 18.18, 0.2, 3, 42, -24.15, 37.79, 0.14999, 43, 53.82, 44.41, 0.41001, 129, -11.07, 15.36, 0.44, 4, 42, -49.51, 35.81, 0.03281, 43, 31.88, 57.68, 0.70884, 4, 119.71, 9.35, 0.01035, 129, -36.21, 12.57, 0.248, 3, 43, -6.92, 68.16, 0.41941, 4, 105.08, 45.63, 0.11659, 131, 9.49, 34.03, 0.464, 3, 43, -38.55, 65.84, 0.28115, 4, 83.7, 68.77, 0.26285, 131, -22.13, 31.71, 0.456, 2, 43, -62.55, 58.91, 0.35016, 4, 62.98, 83.28, 0.64984, 3, 43, -76.59, 37.48, 0.12729, 4, 35.74, 81.51, 0.52871, 131, -60.17, 3.35, 0.344, 2, 43, -86.65, 22.12, 0.09843, 4, 16.22, 80.23, 0.90157, 2, 43, -91.19, -1.06, 0.03075, 4, -6.74, 70.06, 0.96925, 2, 43, -77.19, -31.97, 0.00012, 4, -25.06, 40.95, 0.99988, 2, 4, -37.47, 24.06, 0.68, 132, -22.74, 52.06, 0.32, 4, 42, -40.62, -115.98, 0.00145, 43, -55.37, -69.17, 5e-05, 4, -44.08, 2.06, 0.4785, 132, -29.35, 30.05, 0.52, 5, 42, -3.22, -113.06, 0.01888, 43, -23.02, -88.72, 0.01216, 4, -41.3, -34.57, 0.3856, 133, -23.75, 29.19, 0.16, 132, -26.58, -6.57, 0.42336, 5, 42, 25.16, -110.85, 0.0208, 43, 1.51, -103.56, 0.01474, 4, -39.2, -62.35, 0.19838, 133, -21.64, 1.41, 0.32, 132, -24.47, -34.35, 0.44608, 4, 42, 50.28, -105.87, 0.05123, 43, 25.12, -114.27, 0.0357, 4, -34.07, -86.97, 0.40907, 133, -16.51, -23.22, 0.504, 4, 42, 52.91, -90.42, 0.05212, 43, 36.89, -103.43, 0.03709, 4, -17.43, -89.7, 0.39879, 133, 0.12, -25.94, 0.512, 4, 42, 34.79, -80.4, 0.08576, 43, 28.34, -84.8, 0.07188, 4, -6.45, -72.06, 0.47436, 133, 11.1, -8.3, 0.368, 4, 42, 42.62, -64.98, 0.10087, 43, 44.33, -77.02, 0.08797, 4, 10.11, -79.86, 0.30716, 133, 27.66, -16.11, 0.504, 4, 42, 39.88, -52.49, 0.16036, 43, 49.87, -65.41, 0.13668, 4, 23.6, -77.29, 0.36696, 130, 45.03, -8.44, 0.336, 5, 41, -18.24, -41.57, 5e-05, 42, 30.28, -40.96, 0.25906, 43, 49.22, -50.55, 0.19225, 4, 36.13, -68.01, 0.31664, 130, 35.44, 3.09, 0.232, 5, 41, -23.33, -27.27, 0.01681, 42, 25.65, -26.51, 0.4446, 43, 54.43, -36.25, 0.13007, 4, 51.77, -63.61, 0.09652, 130, 30.8, 17.55, 0.312, 4, 41, -4.01, -26.4, 0.43715, 42, 45.1, -26.26, 0.54, 43, 70.46, -47.44, 0.01311, 4, 51.84, -82.64, 0.00974, 3, 41, 14.14, -30.34, 0.88942, 42, 63.2, -30.78, 0.11056, 4, 46.79, -100.31, 2e-05, 2, 40, -16.78, -22.28, 0.00927, 41, 36.77, -24.44, 0.99073, 2, 40, 0.69, -15.69, 0.44466, 41, 53.01, -15.42, 0.55534, 1, 40, 19.48, -8.3, 1, 1, 40, 35.07, -3.34, 1, 1, 40, 16.5, -0.81, 1, 1, 41, 37.73, -0.08, 1, 2, 41, 3.78, 3.83, 0.90992, 42, 53.92, 3.71, 0.09008, 2, 42, 23.82, 4.04, 0.736, 129, 37.73, -16.85, 0.264, 1, 43, 43.04, 0.93, 1, 3, 43, 17.15, 2.18, 0.61408, 131, 33.57, -31.95, 0.14592, 129, -12.26, -39.63, 0.24, 4, 43, -8.29, -0.9, 0.05558, 4, 44.15, 5.9, 0.62743, 130, -40.16, 9.84, 0.072, 131, 8.13, -35.02, 0.24499, 1, 4, 22.37, 5.24, 1, 5, 42, -38.03, -75.68, 0.00016, 43, -28.17, -38.39, 0.00019, 4, -0.65, -0.84, 0.38403, 132, 14.08, 27.15, 0.376, 131, -11.75, -72.52, 0.23962, 4, 42, -28.86, -87.58, 0.00546, 43, -28.09, -53.29, 0.00417, 4, -13.57, -9.71, 0.51037, 132, 1.16, 18.29, 0.48, 4, 42, -9.96, -89.54, 0.02579, 43, -13.88, -65.93, 0.02366, 4, -15.87, -28.19, 0.55055, 132, -1.14, -0.19, 0.4, 5, 42, 17.3, -88.57, 0.04089, 43, 8.98, -81.1, 0.03474, 4, -15.08, -54.87, 0.33064, 133, 2.47, 8.89, 0.448, 132, -0.36, -26.87, 0.14573, 4, 42, 34.58, -94.48, 0.03736, 43, 19.4, -95.95, 0.02776, 4, -21.63, -71.72, 0.28687, 133, -4.08, -7.96, 0.648, 2, 41, -18.26, 15.96, 0.06691, 42, 32.18, 16.54, 0.93309, 4, 41, -49.9, 16.68, 3e-05, 42, 0.41, 18.27, 0.50508, 43, 61.7, 14.4, 0.19088, 129, 13.98, -3.38, 0.304, 3, 42, -30.58, 15.09, 0.0507, 43, 34.43, 29.99, 0.55731, 129, -16.71, -7.54, 0.392, 5, 42, -53.96, 4.86, 0.00063, 43, 8.98, 35.48, 0.32655, 4, 86.37, 14, 0.02643, 131, 25.4, 1.35, 0.3264, 129, -39.61, -18.51, 0.32, 2, 43, -21.29, 32.17, 0.54243, 4, 64.97, 35.51, 0.45757, 3, 43, -43.49, 19.04, 0.11389, 4, 39.95, 44.96, 0.43811, 131, -27.07, -15.09, 0.448, 2, 43, -60.37, -3.16, 0.03284, 4, 10.3, 44.93, 0.96716, 2, 43, -63.02, -27.22, 0.00029, 4, -12.26, 32.77, 0.99971, 3, 42, -55.04, -101.85, 4e-05, 4, -28.7, 16.04, 0.72796, 132, -13.97, 44.03, 0.272, 4, 42, 7.92, -30.83, 0.24869, 43, 37.27, -29.34, 0.29973, 4, 47.28, -46.22, 0.16357, 130, 13.07, 13.23, 0.288, 5, 42, -1.05, -50.25, 0.05928, 43, 17.86, -39.66, 0.12085, 4, 26.42, -37.27, 0.2296, 132, 41.14, -9.27, 0.11827, 130, 4.1, -6.2, 0.472, 6, 42, 7.53, -64.49, 0.05763, 43, 16, -56.09, 0.0729, 4, 10.98, -45.53, 0.28145, 133, 28.53, 18.22, 0.09216, 132, 25.71, -17.54, 0.13586, 130, 12.68, -20.43, 0.36, 5, 42, 25.06, -63.89, 0.10189, 43, 30.67, -65.86, 0.09592, 4, 11.46, -62.68, 0.33217, 133, 29.01, 1.07, 0.19802, 130, 30.21, -19.83, 0.272, 4, 42, -17.33, -37.38, 0.03674, 43, 12.58, -19.82, 0.38526, 4, 40.46, -21.46, 0.338, 130, -12.17, 6.67, 0.24, 4, 42, -24.13, -49.76, 0.01774, 43, -0.68, -25.75, 0.1148, 4, 27.17, -14.69, 0.58746, 130, -18.98, -5.7, 0.28, 5, 42, -15.82, -65.54, 0.00734, 43, -3.72, -43.27, 0.01557, 4, 10.07, -22.68, 0.13683, 132, 24.8, 5.32, 0.35226, 130, -10.66, -21.49, 0.488], "hull": 33, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 0, 64, 0, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 40, 70, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 52, 110, 110, 112, 112, 114, 114, 116, 118, 120, 120, 122], "width": 255, "height": 152}}, "m10": {"m10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-425.07, 14.21, -425.07, -22.79, -481.07, -22.79, -481.07, 14.21], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 37, "height": 56}}, "m7": {"m7": {"type": "mesh", "uvs": [0, 0.07056, 0.09103, 0.20455, 0.15992, 0.3897, 0.23812, 0.59433, 0.33307, 0.80141, 0.47084, 0.94271, 0.64957, 1, 0.79479, 1, 0.8916, 0.8818, 0.99214, 0.7332, 1, 0.6187, 0.97166, 0.46522, 0.94932, 0.27763, 0.92884, 0.16557, 0.79479, 0.10954, 0.65516, 0.03645, 0.44477, 0, 0.23439, 0, 0.05938, 0.02427, 0.11907, 0.09737, 0.35565, 0.20663, 0.53378, 0.38509, 0.68408, 0.54169, 0.85664, 0.66916, 0.25493, 0.29472, 0.37274, 0.45729, 0.48198, 0.6563, 0.63621, 0.80485, 0.77973, 0.86091, 0.40701, 0.06489, 0.61265, 0.19382, 0.75402, 0.33677, 0.86541, 0.43487], "triangles": [6, 28, 7, 7, 28, 8, 5, 27, 6, 6, 27, 28, 5, 26, 27, 5, 4, 26, 28, 23, 8, 8, 23, 9, 28, 27, 23, 27, 22, 23, 27, 26, 22, 4, 3, 26, 9, 23, 10, 23, 11, 10, 23, 32, 11, 23, 22, 32, 3, 25, 26, 26, 21, 22, 22, 31, 32, 22, 21, 31, 32, 12, 11, 32, 31, 12, 31, 13, 12, 31, 14, 13, 26, 25, 21, 25, 2, 24, 25, 3, 2, 24, 20, 25, 25, 20, 21, 2, 1, 24, 21, 30, 31, 21, 20, 30, 31, 30, 14, 1, 19, 24, 24, 19, 20, 19, 17, 20, 20, 29, 30, 20, 17, 29, 1, 0, 19, 29, 16, 30, 30, 15, 14, 30, 16, 15, 0, 18, 19, 19, 18, 17, 29, 17, 16], "vertices": [1, 86, -23.27, -13.38, 1, 2, 86, -6.21, -16.58, 0.99446, 87, -55.65, -10.78, 0.00554, 2, 86, 11.78, -25.29, 0.86058, 87, -38.62, -21.25, 0.13942, 2, 86, 31.86, -34.76, 0.38679, 87, -19.59, -32.68, 0.61321, 2, 86, 53.74, -43.15, 0.03901, 87, 1.34, -43.22, 0.96099, 1, 87, 23.3, -45.66, 1, 1, 87, 45.13, -38.65, 1, 1, 87, 60.41, -29.19, 1, 1, 87, 64.36, -13.34, 1, 2, 86, 114.31, 11.62, 0.00028, 87, 67.09, 5.21, 0.99972, 2, 86, 108.15, 20.81, 0.00894, 87, 61.87, 14.97, 0.99106, 2, 86, 96.06, 30.19, 0.07256, 87, 50.79, 25.52, 0.92744, 2, 86, 82.49, 42.59, 0.24696, 87, 38.53, 39.21, 0.75304, 2, 86, 73.69, 49.46, 0.33064, 87, 30.46, 46.93, 0.66936, 2, 86, 57.13, 43.57, 0.53824, 87, 13.39, 42.72, 0.46176, 2, 86, 38.99, 38.54, 0.86165, 87, -5.16, 39.53, 0.13835, 1, 86, 16.12, 25.42, 1, 1, 86, -4.53, 9.58, 1, 1, 86, -20.24, -5.43, 1, 1, 86, -9.95, -6.42, 1, 1, 86, 19.9, 3.2, 1, 1, 86, 48.21, 3.22, 1, 2, 86, 72.46, 2.79, 0.00172, 87, 24.56, 0.61, 0.99828, 2, 86, 97.13, 6.22, 0.00078, 87, 49.45, 1.56, 0.99922, 2, 86, 15.35, -11, 0.93971, 87, -33.64, -7.39, 0.06029, 2, 86, 36.77, -14.33, 0.50405, 87, -12.66, -12.85, 0.49595, 2, 86, 59.56, -21.04, 0.0051, 87, 9.35, -21.8, 0.9949, 1, 87, 33.42, -23.76, 1, 1, 87, 51.48, -18.94, 1, 1, 86, 16.35, 17.71, 1, 2, 86, 44.36, 23.52, 0.86363, 87, -1.32, 24.06, 0.13637, 2, 86, 66.9, 23.44, 0.31187, 87, 21.1, 21.72, 0.68813, 2, 86, 83.79, 24.47, 0.11336, 87, 38, 21.05, 0.88664], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36, 0, 38, 38, 40, 40, 42, 42, 44, 44, 46, 2, 48, 48, 50, 50, 52, 52, 54, 54, 56, 34, 58, 58, 60, 60, 62, 62, 64], "width": 123, "height": 94}}, "s1": {"s1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [37.51, 30.35, -25.64, 43, -38.72, -23.92, 24.43, -36.57], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 67}}, "hx1": {"hx1": {"type": "mesh", "uvs": [0, 0.82004, 0.00042, 0.9057, 0.06123, 0.95309, 0.11547, 1, 0.18286, 1, 0.27244, 0.96585, 0.33079, 0.8729, 0.38174, 0.77812, 0.42776, 0.65601, 0.49761, 0.52114, 0.56747, 0.38445, 0.65047, 0.26962, 0.72937, 0.17849, 0.81566, 0.10741, 0.88387, 0.07279, 0.95208, 0.06185, 0.96441, 0.10559, 0.93236, 0.17667, 0.88716, 0.24411, 0.87812, 0.33341, 0.90195, 0.38445, 0.92907, 0.36804, 0.90524, 0.3079, 0.92414, 0.25869, 0.96441, 0.21859, 0.9866, 0.19308, 0.99975, 0.12746, 1, 0.03633, 0.96112, 0, 0.89044, 0, 0.83292, 0.01993, 0.78032, 0.06185, 0.71211, 0.1147, 0.66499, 0.15314, 0.64061, 0.17303, 0.59705, 0.24228, 0.5461, 0.32248, 0.49268, 0.40267, 0.44173, 0.51203, 0.40722, 0.58311, 0.36139, 0.6779, 0.32154, 0.77761, 0.26126, 0.86372, 0.19484, 0.91811, 0.13557, 0.91811, 0.07528, 0.84559], "triangles": [20, 22, 21, 20, 19, 22, 19, 18, 22, 22, 18, 23, 23, 17, 24, 23, 18, 17, 25, 24, 16, 24, 17, 16, 25, 16, 26, 26, 16, 27, 13, 30, 14, 13, 31, 30, 16, 15, 27, 14, 29, 15, 14, 30, 29, 15, 28, 27, 15, 29, 28, 12, 31, 13, 10, 37, 36, 10, 9, 37, 10, 35, 11, 10, 36, 35, 12, 33, 32, 12, 11, 33, 35, 34, 11, 11, 34, 33, 12, 32, 31, 42, 41, 6, 6, 41, 7, 41, 40, 7, 7, 40, 8, 40, 39, 8, 8, 38, 9, 8, 39, 38, 38, 37, 9, 4, 43, 5, 3, 44, 4, 4, 44, 43, 3, 2, 44, 43, 42, 5, 5, 42, 6, 2, 45, 44, 2, 1, 45, 1, 0, 45], "vertices": [1, 68, -7.26, 6.27, 1, 1, 68, -6.94, -4.35, 1, 1, 68, 9.89, -9.91, 1, 2, 68, 24.91, -15.44, 0.99778, 69, -44.37, 23.19, 0.00222, 2, 68, 43.44, -15.09, 0.88444, 69, -31.14, 10.2, 0.11556, 2, 68, 67.99, -10.39, 0.65999, 69, -10.6, -4.03, 0.34001, 2, 68, 83.82, 1.44, 0.32889, 69, 8.93, -7.05, 0.67111, 3, 68, 97.6, 13.45, 0.10889, 69, 27.16, -8.48, 0.87666, 70, -48.69, -15.49, 0.01445, 2, 69, 46.8, -6.55, 0.85999, 70, -29.51, -10.81, 0.14001, 2, 69, 72.22, -8.07, 0.62332, 70, -4.13, -8.74, 0.37668, 2, 69, 97.8, -9.44, 0.30443, 70, 21.39, -6.49, 0.69557, 3, 69, 124.07, -15.27, 0.09666, 70, 48.21, -8.57, 0.79223, 71, -25.46, -18.55, 0.11111, 2, 70, 72.38, -12.34, 0.66667, 71, -1.69, -12.77, 0.33333, 2, 70, 96.72, -19.32, 0.33333, 71, 23.46, -9.89, 0.66667, 2, 70, 114.36, -26.99, 0.11111, 71, 42.7, -10.21, 0.88889, 1, 71, 61.24, -13.38, 1, 1, 71, 63.23, -19.46, 1, 1, 71, 52.56, -25.91, 1, 1, 71, 38.49, -31.05, 1, 1, 71, 33.43, -41.21, 1, 1, 71, 38.28, -48.92, 1, 1, 71, 46.01, -48.73, 1, 1, 71, 41.43, -39.92, 1, 1, 71, 47.94, -35.24, 1, 1, 71, 59.88, -33.06, 1, 1, 71, 66.56, -31.45, 1, 1, 71, 72.02, -24.41, 1, 1, 71, 74.79, -13.46, 1, 1, 71, 65.48, -6.53, 1, 1, 71, 46.61, -1.88, 1, 2, 70, 106.97, -13.4, 0.11111, 71, 30.66, -0.49, 0.88889, 2, 70, 92.24, -9, 0.33333, 71, 15.37, -2.08, 0.66667, 2, 70, 73.25, -3.15, 0.66667, 71, -4.41, -3.96, 0.33333, 2, 70, 59.99, 0.7, 0.88889, 71, -18.13, -5.48, 0.11111, 1, 70, 53.13, 2.69, 1, 2, 69, 115.96, -2.56, 0.00134, 70, 38.4, 2.88, 0.99866, 2, 69, 98.99, 0.16, 0.11378, 70, 21.22, 3.18, 0.88622, 2, 69, 81.55, 3.36, 0.33734, 70, 3.49, 3.89, 0.66266, 2, 69, 62.05, 3.5, 0.66934, 70, -15.83, 1.29, 0.33066, 2, 69, 49.1, 3.86, 0.89023, 70, -28.7, -0.18, 0.10977, 2, 68, 91.77, 25.77, 0.10787, 69, 31.87, 4.3, 0.89213, 2, 68, 81.05, 13.2, 0.32684, 69, 15.39, 3.16, 0.67316, 2, 68, 64.68, 2.21, 0.65693, 69, -3.92, 7.16, 0.34307, 2, 68, 46.54, -4.88, 0.8824, 69, -21.68, 15.14, 0.1176, 2, 68, 30.25, -5.19, 0.99676, 69, -33.31, 26.56, 0.00324, 1, 68, 13.5, 3.49, 1], "hull": 46, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 68, 70, 64, 66, 66, 68, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 0, 90], "width": 275, "height": 124}}, "y3": {"y3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 105, -23.16, 19.52, 1, 1, 105, 18.84, 18.9, 1, 1, 105, 18.32, -16.09, 1, 1, 105, -23.67, -15.47, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 35}}, "l": {"l": {"type": "mesh", "uvs": [0.1117, 0.63892, 0.11464, 0.5631, 0.07201, 0.48172, 0.03526, 0.3689, 0, 0.26163, 0, 0.15067, 0.01321, 0.08779, 0.09847, 0.15437, 0.19109, 0.2117, 0.28664, 0.2228, 0.3822, 0.15991, 0.49098, 0.10998, 0.613, 0.09148, 0.70268, 0.06559, 0.80558, 0.00641, 0.87909, 0, 0.94671, 0.06744, 1, 0.13957, 0.95112, 0.29493, 0.87909, 0.45953, 0.81734, 0.50576, 0.76736, 0.5409, 0.65416, 0.56495, 0.58948, 0.62598, 0.53803, 0.63892, 0.50421, 0.60379, 0.44247, 0.63338, 0.3822, 0.65557, 0.34545, 0.75174, 0.33809, 0.87936, 0.38808, 0.99217, 0.31898, 0.98847, 0.28095, 0.99088, 0.18741, 0.99679, 0.13669, 1, 0.0676, 0.94779, 0.03967, 0.85901, 0.03967, 0.77209, 0.07495, 0.71845, 0.29306, 0.56935, 0.26461, 0.6421, 0.26644, 0.71832, 0.28388, 0.7726, 0.27287, 0.83728, 0.10121, 0.87654, 0.15996, 0.88463, 0.2334, 0.88116, 0.30224, 0.87539, 0.31326, 0.76914, 0.31601, 0.71948, 0.32795, 0.64326, 0.35824, 0.57859, 0.13487, 0.82203, 0.14875, 0.73787, 0.17147, 0.64418, 0.18031, 0.56161, 0.20113, 0.46078, 0.30779, 0.52826, 0.43447, 0.49962, 0.50766, 0.40868, 0.59893, 0.32796, 0.74893, 0.25862, 0.87273, 0.1779, 0.5908, 0.51099, 0.68929, 0.43482, 0.79592, 0.37571, 0.88809, 0.31432, 0.35314, 0.40754, 0.45525, 0.28817, 0.56007, 0.22565, 0.68568, 0.17449, 0.82032, 0.10969, 0.21958, 0.33478, 0.29639, 0.35411, 0.3479, 0.24952, 0.15723, 0.3507, 0.1238, 0.23929, 0.03795, 0.15858, 0.06055, 0.25521, 0.09759, 0.35638, 0.12561, 0.48143], "triangles": [19, 65, 66, 65, 61, 66, 64, 61, 65, 20, 65, 19, 71, 14, 15, 61, 70, 71, 61, 62, 66, 64, 60, 61, 60, 70, 61, 35, 44, 34, 34, 45, 33, 34, 44, 45, 33, 46, 32, 33, 45, 46, 31, 29, 30, 32, 46, 47, 32, 47, 31, 47, 46, 43, 31, 47, 29, 35, 36, 44, 44, 52, 45, 45, 52, 46, 52, 53, 46, 43, 46, 42, 42, 46, 53, 47, 48, 29, 29, 48, 28, 53, 54, 41, 54, 40, 41, 52, 44, 37, 47, 43, 48, 48, 43, 42, 44, 36, 37, 41, 42, 53, 37, 38, 52, 52, 38, 53, 48, 42, 49, 48, 49, 28, 49, 42, 41, 28, 49, 27, 27, 49, 50, 38, 0, 53, 53, 0, 54, 50, 49, 40, 49, 41, 40, 50, 51, 27, 27, 51, 26, 0, 1, 54, 54, 55, 40, 54, 1, 55, 40, 39, 50, 50, 39, 51, 40, 55, 39, 24, 25, 23, 51, 58, 26, 26, 58, 25, 23, 63, 22, 23, 25, 63, 25, 59, 63, 25, 58, 59, 39, 57, 51, 51, 57, 58, 57, 39, 56, 22, 63, 64, 1, 80, 55, 1, 2, 80, 39, 55, 56, 55, 80, 56, 56, 73, 57, 57, 67, 58, 57, 73, 67, 63, 60, 64, 63, 59, 60, 58, 67, 59, 2, 79, 80, 2, 3, 79, 80, 75, 56, 80, 79, 75, 56, 72, 73, 56, 75, 72, 67, 68, 59, 60, 68, 69, 60, 59, 68, 73, 74, 67, 67, 74, 68, 3, 78, 79, 3, 4, 78, 79, 76, 75, 79, 78, 76, 72, 9, 73, 73, 9, 74, 72, 75, 8, 75, 76, 8, 72, 8, 9, 60, 69, 70, 74, 10, 68, 68, 11, 69, 68, 10, 11, 4, 77, 78, 4, 5, 77, 78, 7, 76, 78, 77, 7, 74, 9, 10, 76, 7, 8, 69, 12, 70, 69, 11, 12, 7, 77, 6, 77, 5, 6, 22, 64, 21, 62, 71, 16, 66, 62, 18, 61, 71, 62, 70, 13, 71, 70, 12, 13, 21, 64, 65, 20, 21, 65, 19, 66, 18, 18, 62, 17, 62, 16, 17, 71, 15, 16, 13, 14, 71], "vertices": [64.62, -17.27, 59.29, -21.27, 56.9, -29.32, 51.95, -38.64, 47.28, -47.54, 39.79, -53.74, 34.58, -56.17, 32.87, -45.45, 30.01, -34.64, 23.8, -26.17, 12.61, -21.84, 1.32, -15.7, -8.8, -6.72, -17.07, -0.8, -28.56, 4.34, -34.34, 10.02, -34.7, 19.34, -33.71, 27.75, -19.67, 32.42, -3.31, 35.7, 4.3, 33.22, 10.31, 31.08, 20.16, 23.13, 28.99, 21.23, 33.61, 17.73, 33.7, 12.99, 40.18, 9.58, 46.07, 5.87, 55.23, 8.23, 64.38, 14.76, 68.36, 25.17, 73.14, 19.29, 76.07, 16.3, 83.27, 8.95, 87.18, 4.96, 88.68, -3.63, 84.72, -10.88, 78.85, -15.74, 72.66, -15.84, 46.73, -6.27, 53.71, -4.54, 58.72, -0.13, 61.12, 4.34, 66.29, 7.05, 81.43, -4.85, 77.7, 0.42, 72.12, 6.26, 66.72, 11.59, 58.75, 6.56, 55.2, 4.01, 49.18, 0.72, 42.61, -0.4, 75.3, -5.13, 68.61, -8.7, 60.63, -12.07, 54.41, -15.96, 46.09, -19.89, 42.89, -7.36, 31.74, 1.44, 20.27, 2.37, 8.18, 5.35, -7.41, 13.79, -21.86, 19.44, 21.13, 14.91, 8.82, 18.74, -2.92, 24.19, -13.77, 28.33, 31.44, -10.38, 15.95, -8.67, 4.1, -3.56, -8.49, 3.89, -22.66, 11.32, 36.24, -25.42, 31.96, -18.03, 21.15, -19.65, 41.85, -29.65, 36.76, -38.62, 37.56, -50.18, 42.44, -42.92, 46.57, -34.23, 52.98, -24.94], "hull": 39, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 68, 70, 70, 72, 72, 74, 74, 76, 0, 76, 78, 80, 80, 82, 82, 84, 84, 86, 72, 88, 88, 90, 90, 92, 86, 92, 94, 96, 96, 98, 98, 100, 100, 102, 66, 68, 90, 66, 62, 64, 64, 66, 92, 64, 90, 104, 104, 106, 106, 108, 108, 110, 110, 112, 78, 114, 102, 116, 116, 118, 118, 120, 120, 122, 122, 124, 50, 126, 126, 128, 128, 130, 130, 132, 114, 134, 134, 136, 136, 138, 138, 140, 140, 142, 112, 144, 146, 148, 150, 152, 154, 156, 156, 158, 158, 160, 160, 2], "width": 117, "height": 93}}, "yw1": {"yw1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [3, 120, -176.92, 9.29, 0.00053, 121, -75.07, 53.1, 0.91861, 123, -33.87, 132.55, 0.08087, 3, 120, 54.06, 5.88, 0.80739, 122, 43.19, 88.36, 0.19261, 124, 31.2, 145.94, 0, 2, 122, 40.83, -71.62, 0.00054, 124, 28.84, -14.04, 0.99946, 2, 121, -77.43, -106.88, 0.00092, 123, -36.23, -27.43, 0.99908], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 231, "height": 160}}, "mm2": {"mm2": {"type": "mesh", "uvs": [0.03154, 0.57612, 0.0042, 0.31167, 0.26387, 0.16407, 0.59529, 0, 0.85153, 0.20507, 1, 0.37932, 1, 0.61712, 0.69779, 0.75447, 0.52012, 0.88772, 0.29804, 1, 0.15112, 0.85697, 0.07254, 0.71142, 0.45349, 0.22867, 0.36749, 0.50045, 0.29868, 0.74127, 0.29295, 0.87887], "triangles": [10, 15, 9, 9, 15, 8, 7, 8, 14, 10, 11, 14, 6, 7, 13, 6, 13, 5, 14, 11, 0, 12, 4, 5, 0, 1, 13, 12, 1, 2, 4, 12, 3, 12, 2, 3, 8, 15, 14, 15, 10, 14, 7, 14, 13, 13, 14, 0, 5, 13, 12, 13, 1, 12], "vertices": [38.53, -33.99, 30.66, -41.37, 21.83, -40.47, 11.32, -38.7, 13.62, -28.7, 16.7, -21.45, 24.18, -15.27, 33.37, -17.2, 40.43, -16.97, 47.54, -18.1, 45.42, -24.5, 42.12, -29.72, 20.79, -35.34, 30.72, -29.83, 39.4, -24.82, 43.82, -21.34], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 0, 22, 6, 24, 24, 26, 26, 28, 28, 30], "width": 24, "height": 40}}, "by": {"by": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [23.76, 13.36, 37.89, -2.59, 18.25, -18.86, 4.12, -2.91], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 21, "height": 25}}, "y1": {"y1": {"type": "mesh", "uvs": [1, 1, 0.75, 1, 0.5, 1, 0.25, 1, 0, 1, 0, 0.5, 0, 0, 0.25, 0, 0.5, 0, 0.75, 0, 1, 0, 1, 0.5, 0.75, 0.5, 0.5, 0.5, 0.25, 0.5], "triangles": [12, 13, 9, 2, 13, 12, 1, 2, 12, 12, 9, 10, 11, 12, 10, 1, 12, 11, 0, 1, 11, 5, 6, 7, 14, 5, 7, 14, 7, 8, 13, 14, 8, 3, 14, 13, 2, 3, 13, 13, 8, 9, 4, 5, 14, 3, 4, 14], "vertices": [3, 126, -99.11, 19.01, 0.00053, 127, -65.6, 47.05, 0.00212, 128, -25.13, 44.17, 0.99735, 3, 126, -69.11, 18.57, 0.05273, 127, -35.6, 46.61, 0.13303, 128, 4.87, 43.73, 0.81424, 3, 126, -39.12, 18.13, 0.35256, 127, -5.61, 46.17, 0.34041, 128, 34.87, 43.29, 0.30703, 3, 126, -9.12, 17.69, 0.90645, 127, 24.39, 45.73, 0.07007, 128, 64.86, 42.85, 0.02349, 1, 126, 20.88, 17.25, 1, 3, 126, 20.26, -24.25, 0.65882, 127, 53.77, 3.79, 0.34118, 128, 94.25, 0.91, 0, 3, 126, 19.65, -65.75, 0.29905, 127, 53.16, -37.71, 0.70095, 128, 93.63, -40.59, 0, 3, 126, -10.35, -65.3, 0.1712, 127, 23.17, -37.27, 0.82303, 128, 63.64, -40.15, 0.00576, 3, 126, -40.34, -64.86, 0.01215, 127, -6.83, -36.82, 0.78043, 128, 33.64, -39.7, 0.20742, 2, 127, -36.83, -36.38, 0.27173, 128, 3.64, -39.26, 0.72827, 2, 127, -66.83, -35.94, 0.02255, 128, -26.35, -38.82, 0.97745, 1, 128, -25.74, 2.68, 1, 3, 126, -69.73, -22.92, 0.00152, 127, -36.22, 5.11, 0.07431, 128, 4.26, 2.24, 0.92417, 3, 126, -39.73, -23.37, 0.00828, 127, -6.22, 4.67, 0.86525, 128, 34.25, 1.79, 0.12647, 2, 126, -9.73, -23.81, 0.42769, 127, 23.78, 4.23, 0.57231], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 0], "width": 120, "height": 83}}, "y2": {"y2": {"type": "mesh", "uvs": [1, 1, 0.83333, 1, 0.66667, 1, 0.5, 1, 0.33333, 1, 0.16667, 1, 0, 1, 0, 0.5, 0, 0, 0.16667, 0, 0.33333, 0, 0.5, 0, 0.66667, 0, 0.83333, 0, 1, 0, 1, 0.5, 0.83333, 0.5, 0.66667, 0.5, 0.5, 0.5, 0.33333, 0.5, 0.16667, 0.5], "triangles": [0, 1, 15, 1, 16, 15, 15, 16, 14, 16, 13, 14, 1, 2, 16, 2, 17, 16, 16, 17, 13, 17, 12, 13, 2, 3, 17, 3, 18, 17, 17, 18, 12, 18, 11, 12, 3, 4, 18, 4, 19, 18, 18, 19, 11, 19, 10, 11, 4, 5, 19, 5, 20, 19, 19, 20, 10, 20, 9, 10, 5, 6, 20, 6, 7, 20, 20, 7, 9, 7, 8, 9], "vertices": [1, 103, -15.04, 17.2, 1, 2, 104, -56.81, 21.09, 0.03046, 103, 1.13, 16.96, 0.96954, 2, 104, -40.64, 20.86, 0.24515, 103, 17.29, 16.72, 0.75485, 2, 104, -24.48, 20.62, 0.58928, 103, 33.46, 16.48, 0.41072, 2, 104, -8.31, 20.38, 0.88285, 103, 49.62, 16.24, 0.11715, 2, 104, 7.85, 20.14, 0.99444, 103, 65.79, 16, 0.00556, 1, 104, 24.02, 19.9, 1, 1, 104, 23.72, -0.1, 1, 1, 104, 23.43, -20.09, 1, 2, 104, 7.26, -19.86, 0.99586, 103, 65.2, -23.99, 0.00414, 2, 104, -8.9, -19.62, 0.88884, 103, 49.03, -23.75, 0.11116, 2, 104, -25.07, -19.38, 0.60049, 103, 32.87, -23.52, 0.39951, 2, 104, -41.23, -19.14, 0.2661, 103, 16.7, -23.28, 0.7339, 2, 104, -57.4, -18.9, 0.04951, 103, 0.54, -23.04, 0.95049, 1, 103, -15.63, -22.8, 1, 1, 103, -15.33, -2.8, 1, 2, 104, -57.1, 1.1, 0.01213, 103, 0.83, -3.04, 0.98787, 2, 104, -40.94, 0.86, 0.24759, 103, 17, -3.28, 0.75241, 2, 104, -24.77, 0.62, 0.59754, 103, 33.16, -3.52, 0.40246, 2, 104, -8.61, 0.38, 0.90723, 103, 49.33, -3.76, 0.09277, 1, 104, 7.56, 0.14, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 0], "width": 97, "height": 40}}, "yw2": {"yw2": {"type": "mesh", "uvs": [1, 1, 0.85714, 1, 0.71429, 1, 0.57143, 1, 0.42857, 1, 0.28571, 1, 0.14286, 1, 0, 1, 0, 0.5, 0, 0, 0.14286, 0, 0.28571, 0, 0.42857, 0, 0.57143, 0, 0.71429, 0, 0.85714, 0, 1, 0, 1, 0.5, 0.85714, 0.5, 0.71429, 0.5, 0.57143, 0.5, 0.42857, 0.5, 0.28571, 0.5, 0.14286, 0.5, 0.85291, 0.75112, 0.70173, 0.7408, 0.57051, 0.75456, 0.43645, 0.73392, 0.26958, 0.75456, 0.13266, 0.74768, 0.13979, 0.24907, 0.28669, 0.24563, 0.42932, 0.24219, 0.56053, 0.26626, 0.72883, 0.26626, 0.85861, 0.28002], "triangles": [26, 27, 20, 26, 20, 25, 3, 26, 25, 3, 4, 26, 3, 25, 2, 27, 21, 20, 28, 23, 22, 6, 29, 28, 22, 27, 28, 27, 5, 28, 21, 27, 22, 6, 28, 5, 4, 5, 27, 4, 27, 26, 35, 15, 16, 34, 15, 35, 18, 34, 35, 19, 34, 18, 35, 16, 17, 18, 35, 17, 24, 19, 18, 25, 19, 24, 2, 25, 24, 17, 24, 18, 0, 1, 24, 2, 24, 1, 0, 24, 17, 34, 14, 15, 13, 33, 12, 14, 33, 13, 33, 19, 20, 34, 33, 14, 21, 33, 20, 34, 19, 33, 25, 20, 19, 33, 32, 12, 31, 11, 12, 31, 12, 32, 31, 10, 11, 21, 31, 32, 21, 32, 33, 22, 31, 21, 30, 9, 10, 8, 9, 30, 10, 31, 30, 23, 30, 31, 8, 30, 23, 22, 23, 31, 29, 8, 23, 29, 23, 28, 7, 8, 29, 7, 29, 6], "vertices": [2, 92, 73.3, 75.58, 0.9816, 90, 25.85, 162.85, 0.0184, 2, 92, 73.3, 19.44, 0.85768, 90, 25.85, 106.71, 0.14232, 3, 93, 108.2, 34.2, 0.00019, 92, 73.3, -36.7, 0.40939, 90, 25.85, 50.56, 0.59042, 3, 92, 73.3, -92.84, 0.00532, 91, 35.67, 80.05, 0.0629, 90, 25.85, -5.58, 0.93178, 2, 91, 35.67, 23.91, 0.72729, 90, 25.85, -61.72, 0.27271, 2, 95, 115.84, 61.57, 0.15212, 91, 35.67, -32.24, 0.84788, 2, 95, 115.84, 5.43, 0.49093, 91, 35.67, -88.38, 0.50907, 2, 95, 115.84, -50.71, 0.63349, 91, 35.67, -144.52, 0.36651, 2, 95, 34.34, -50.71, 0.86487, 91, -45.83, -144.52, 0.13513, 1, 95, -47.16, -50.71, 1, 2, 95, -47.16, 5.43, 0.87081, 94, -51.52, -88.93, 0.12919, 3, 95, -47.16, 61.57, 0.30934, 94, -51.52, -32.78, 0.68502, 93, -54.8, -134.23, 0.00563, 3, 95, -47.16, 117.72, 0.00968, 94, -51.52, 23.36, 0.74882, 93, -54.8, -78.08, 0.2415, 3, 94, -51.52, 79.5, 0.24701, 93, -54.8, -21.94, 0.74823, 92, -89.7, -92.84, 0.00476, 3, 94, -51.52, 135.65, 0.00836, 93, -54.8, 34.2, 0.72509, 92, -89.7, -36.7, 0.26655, 2, 93, -54.8, 90.34, 0.28291, 92, -89.7, 19.44, 0.71709, 2, 93, -54.8, 146.49, 0.10909, 92, -89.7, 75.58, 0.89091, 2, 93, 26.7, 146.49, 0.00777, 92, -8.2, 75.58, 0.99223, 2, 93, 26.7, 90.34, 0.01789, 92, -8.2, 19.44, 0.98211, 3, 93, 26.7, 34.2, 0.40322, 92, -8.2, -36.7, 0.46537, 90, -55.65, 50.56, 0.13141, 5, 94, 29.98, 79.5, 0.10777, 93, 26.7, -21.94, 0.55588, 92, -8.2, -92.84, 0.01199, 91, -45.83, 80.05, 0.04927, 90, -55.65, -5.58, 0.2751, 5, 95, 34.34, 117.72, 4e-05, 94, 29.98, 23.36, 0.49574, 93, 26.7, -78.08, 0.12925, 91, -45.83, 23.91, 0.27757, 90, -55.65, -61.72, 0.0974, 3, 95, 34.34, 61.57, 0.24055, 94, 29.98, -32.78, 0.42069, 91, -45.83, -32.24, 0.33876, 3, 95, 34.34, 5.43, 0.75785, 94, 29.98, -88.93, 0.04872, 91, -45.83, -88.38, 0.19343, 2, 92, 32.73, 17.78, 0.91968, 90, -14.72, 105.04, 0.08032, 3, 93, 65.95, 29.27, 0.1036, 92, 31.05, -41.64, 0.41172, 90, -16.4, 45.63, 0.48469, 5, 94, 71.47, 79.14, 0.02238, 93, 68.2, -22.3, 0.09609, 92, 33.29, -93.2, 0.00671, 91, -4.34, 79.69, 0.05616, 90, -14.16, -5.94, 0.81867, 4, 94, 68.11, 26.46, 0.09467, 93, 64.83, -74.99, 0.04382, 91, -7.71, 27, 0.62962, 90, -17.52, -58.63, 0.23189, 3, 95, 75.83, 55.23, 0.21822, 94, 71.47, -39.12, 0.06756, 91, -4.34, -38.58, 0.71422, 3, 95, 74.71, 1.42, 0.58076, 94, 70.35, -92.93, 0.01309, 91, -5.46, -92.39, 0.40615, 2, 95, -6.56, 4.23, 0.96083, 94, -10.93, -90.13, 0.03917, 3, 95, -7.12, 61.96, 0.28326, 94, -11.49, -32.4, 0.68982, 91, -87.3, -31.85, 0.02692, 4, 94, -12.05, 23.65, 0.77798, 93, -15.32, -77.79, 0.2024, 91, -87.86, 24.2, 0.00832, 90, -97.68, -61.43, 0.0113, 4, 94, -8.12, 75.22, 0.22088, 93, -11.4, -26.22, 0.74976, 91, -83.93, 75.77, 0.00885, 90, -93.75, -9.86, 0.02052, 3, 93, -11.4, 39.92, 0.62122, 92, -46.3, -30.99, 0.37703, 90, -93.75, 56.28, 0.00175, 2, 93, -9.15, 90.92, 0.17631, 92, -44.06, 20.02, 0.82369], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 0], "width": 393, "height": 163}}, "y4": {"y4": {"type": "mesh", "uvs": [1, 1, 0.75, 1, 0.5, 1, 0.25, 1, 0, 1, 0, 0.5, 0, 0, 0.25, 0, 0.5, 0, 0.75, 0, 1, 0, 1, 0.5, 0.75, 0.5, 0.5, 0.5, 0.25, 0.5], "triangles": [5, 6, 7, 14, 5, 7, 4, 5, 14, 3, 4, 14, 14, 7, 8, 3, 14, 13, 13, 14, 8, 2, 3, 13, 13, 8, 9, 12, 13, 9, 2, 13, 12, 1, 2, 12, 12, 9, 10, 11, 12, 10, 1, 12, 11, 0, 1, 11], "vertices": [1, 112, -21.92, 22.42, 1, 3, 112, 27.07, 21.7, 0.78294, 113, -33.44, 71.14, 0.20868, 114, -106.22, 99.1, 0.00838, 3, 112, 76.06, 20.97, 0.24003, 113, 15.55, 70.41, 0.57488, 114, -57.23, 98.37, 0.18509, 3, 112, 125.06, 20.25, 0.0299, 113, 64.55, 69.69, 0.38138, 114, -8.24, 97.65, 0.58872, 3, 112, 174.05, 19.53, 3e-05, 113, 113.54, 68.97, 0.20819, 114, 40.76, 96.93, 0.79179, 2, 113, 112.53, 0.47, 0.04254, 114, 39.75, 28.44, 0.95746, 1, 114, 38.74, -40.06, 1, 2, 113, 62.52, -67.3, 0.13803, 114, -10.26, -39.33, 0.86197, 3, 112, 74.04, -116.01, 0.00387, 113, 13.53, -66.57, 0.68064, 114, -59.25, -38.61, 0.31549, 3, 112, 25.05, -115.29, 0.10439, 113, -35.46, -65.85, 0.86622, 114, -108.25, -37.89, 0.0294, 2, 112, -23.95, -114.57, 0.20248, 113, -84.46, -65.13, 0.79752, 2, 112, -22.94, -46.07, 0.55225, 113, -83.45, 3.37, 0.44775, 2, 112, 26.06, -46.8, 0.30444, 113, -34.45, 2.64, 0.69556, 3, 112, 75.05, -47.52, 0.00362, 113, 14.54, 1.92, 0.86276, 114, -58.24, 29.88, 0.13362, 3, 112, 124.05, -48.24, 0.00064, 113, 63.54, 1.2, 0.23821, 114, -9.25, 29.16, 0.76115], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 0], "width": 196, "height": 137}}, "y5": {"y5": {"type": "mesh", "uvs": [1, 1, 0.8, 1, 0.6, 1, 0.4, 1, 0.2, 1, 0, 1, 0, 0.5, 0, 0, 0.2, 0, 0.4, 0, 0.6, 0, 0.8, 0, 1, 0, 1, 0.5, 0.8, 0.5, 0.6, 0.5, 0.4, 0.5, 0.2, 0.5], "triangles": [15, 10, 11, 14, 15, 11, 14, 11, 12, 13, 14, 12, 1, 14, 13, 16, 9, 10, 15, 16, 10, 3, 16, 15, 2, 3, 15, 2, 15, 14, 1, 2, 14, 0, 1, 13, 6, 7, 8, 17, 6, 8, 5, 6, 17, 4, 5, 17, 17, 8, 9, 16, 17, 9, 4, 17, 16, 3, 4, 16], "vertices": [2, 117, -98.47, 59.97, 0.63797, 116, -25.8, 110.39, 0.36203, 3, 118, -120.19, 27.59, 0.00407, 117, -51.88, 59.28, 0.75522, 116, 20.79, 109.7, 0.24071, 3, 118, -73.59, 26.9, 0.1433, 117, -5.28, 58.59, 0.81498, 116, 67.39, 109.01, 0.04172, 2, 118, -27, 26.22, 0.68738, 117, 41.31, 57.9, 0.31262, 1, 118, 19.6, 25.53, 1, 1, 118, 66.19, 24.84, 1, 2, 118, 65.06, -51.65, 0.906, 117, 133.37, -19.96, 0.094, 3, 118, 63.94, -128.14, 0.75784, 117, 132.24, -96.45, 0.2412, 116, 204.91, -46.03, 0.00096, 3, 118, 17.34, -127.45, 0.64158, 117, 85.65, -95.77, 0.32804, 116, 158.32, -45.35, 0.03038, 3, 118, -29.25, -126.77, 0.35244, 117, 39.05, -95.08, 0.48321, 116, 111.72, -44.66, 0.16435, 3, 118, -75.85, -126.08, 0.09344, 117, -7.54, -94.39, 0.41129, 116, 65.13, -43.97, 0.49527, 3, 118, -122.44, -125.39, 0.0053, 117, -54.14, -93.71, 0.0896, 116, 18.53, -43.28, 0.90509, 1, 116, -28.06, -42.6, 1, 2, 117, -99.6, -16.53, 0.23173, 116, -26.93, 33.89, 0.76827, 2, 117, -53.01, -17.21, 0.43036, 116, 19.66, 33.21, 0.56964, 3, 118, -74.72, -49.59, 0.01817, 117, -6.41, -17.9, 0.83744, 116, 66.26, 32.52, 0.14439, 3, 118, -28.13, -50.28, 0.42571, 117, 40.18, -18.59, 0.55132, 116, 112.85, 31.83, 0.02297, 3, 118, 18.47, -50.96, 0.81625, 117, 86.78, -19.28, 0.18105, 116, 159.45, 31.15, 0.0027], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 0], "width": 233, "height": 153}}, "y6": {"y6": {"type": "mesh", "uvs": [1, 1, 0.75, 1, 0.5, 1, 0.25, 1, 0, 1, 0, 0.66667, 0, 0.33333, 0, 0, 0.25, 0, 0.5, 0, 0.75, 0, 1, 0, 1, 0.33333, 1, 0.66667, 0.75, 0.66667, 0.5, 0.66667, 0.25, 0.66667, 0.75, 0.33333, 0.5, 0.33333, 0.25, 0.33333], "triangles": [18, 9, 10, 17, 18, 10, 17, 10, 11, 12, 17, 11, 14, 15, 17, 14, 17, 12, 13, 14, 12, 1, 2, 14, 1, 14, 13, 0, 1, 13, 15, 16, 18, 2, 3, 15, 15, 18, 17, 2, 15, 14, 6, 7, 8, 19, 6, 8, 5, 6, 19, 16, 5, 19, 4, 5, 16, 3, 4, 16, 19, 8, 9, 18, 19, 9, 16, 19, 18, 3, 16, 15], "vertices": [2, 99, -83, 19.93, 0.62811, 98, -62.4, 97.96, 0.37189, 2, 99, -16.76, 18.96, 0.9125, 98, 3.84, 96.98, 0.0875, 4, 101, -55.2, 76.74, 0.17382, 100, 0.84, 72.03, 0.35543, 99, 49.49, 17.98, 0.47076, 97, 94.95, 162.3, 0, 4, 101, 11.04, 75.77, 0.78078, 100, 67.08, 71.05, 0.14707, 99, 115.73, 17, 0.07215, 97, 161.19, 161.33, 0, 3, 101, 77.29, 74.79, 0.99768, 100, 133.33, 70.07, 0.00048, 99, 181.97, 16.03, 0.00184, 2, 101, 76.33, 9.8, 0.9993, 97, 226.47, 95.36, 0.0007, 3, 101, 75.37, -55.2, 0.96518, 100, 131.41, -59.91, 0.00882, 97, 225.52, 30.36, 0.026, 3, 101, 74.41, -120.19, 0.91194, 100, 130.45, -124.9, 0.03657, 97, 224.56, -34.63, 0.05149, 4, 101, 8.17, -119.21, 0.72579, 100, 64.21, -123.93, 0.14154, 98, 133.45, -99.95, 0.01738, 97, 158.31, -33.65, 0.11529, 4, 101, -58.07, -118.24, 0.30168, 100, -2.03, -122.95, 0.25242, 98, 67.21, -98.97, 0.09163, 97, 92.07, -32.68, 0.35428, 4, 101, -124.32, -117.26, 0.03771, 100, -68.28, -121.97, 0.06811, 98, 0.96, -98, 0.03412, 97, 25.83, -31.7, 0.86006, 1, 97, -40.41, -30.72, 1, 3, 99, -84.92, -110.05, 0.01671, 98, -64.32, -32.03, 0.4241, 97, -39.46, 34.27, 0.55919, 3, 99, -83.96, -45.06, 0.31334, 98, -63.36, 32.97, 0.6483, 97, -38.5, 99.26, 0.03836, 3, 100, -66.36, 8.01, 0.07986, 99, -17.71, -46.04, 0.31403, 98, 2.88, 31.99, 0.60611, 3, 101, -56.16, 11.75, 0.02034, 100, -0.12, 7.04, 0.93162, 99, 48.53, -47.01, 0.04804, 3, 101, 10.09, 10.77, 0.98286, 100, 66.13, 6.06, 0.00994, 99, 114.77, -47.99, 0.0072, 4, 101, -123.36, -52.27, 0.01247, 100, -67.32, -56.98, 0.06769, 98, 1.92, -33, 0.54391, 97, 26.79, 33.29, 0.37593, 4, 101, -57.12, -53.24, 0.23679, 100, -1.08, -57.96, 0.43165, 98, 68.16, -33.98, 0.16891, 97, 93.03, 32.32, 0.16266, 4, 101, 9.13, -54.22, 0.81134, 100, 65.17, -58.93, 0.11983, 98, 134.41, -34.96, 0.01095, 97, 159.27, 31.34, 0.05788], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 0], "width": 265, "height": 195}}, "y7": {"y7": {"type": "mesh", "uvs": [1, 1, 0.8, 1, 0.6, 1, 0.4, 1, 0.2, 1, 0, 1, 0, 0.5, 0, 0, 0.2, 0, 0.4, 0, 0.6, 0, 0.8, 0, 1, 0, 1, 0.5, 0.8, 0.5, 0.6, 0.5, 0.4, 0.5, 0.2, 0.5], "triangles": [0, 1, 13, 1, 14, 13, 13, 14, 12, 14, 11, 12, 1, 2, 14, 2, 15, 14, 14, 15, 11, 15, 10, 11, 2, 3, 15, 3, 16, 15, 15, 16, 10, 16, 9, 10, 3, 4, 16, 4, 17, 16, 16, 17, 9, 17, 8, 9, 4, 5, 17, 5, 6, 17, 17, 6, 8, 6, 7, 8], "vertices": [1, 107, -26.44, 16.64, 1, 2, 108, -38.57, 30.6, 0.17598, 107, 11.15, 16.09, 0.82402, 4, 110, -105.4, 71.76, 0.00052, 109, -53.3, 62.96, 0.01881, 108, -0.97, 30.04, 0.86574, 107, 48.75, 15.53, 0.11494, 3, 110, -67.8, 71.21, 0.05313, 109, -15.7, 62.4, 0.31712, 108, 36.62, 29.49, 0.62975, 3, 110, -30.21, 70.65, 0.31888, 109, 21.9, 61.85, 0.47228, 108, 74.22, 28.93, 0.20884, 3, 110, 7.39, 70.1, 0.55626, 109, 59.49, 61.29, 0.38194, 108, 111.81, 28.38, 0.0618, 3, 110, 6.69, 22.6, 0.82513, 109, 58.79, 13.8, 0.16784, 108, 111.11, -19.12, 0.00703, 2, 110, 5.99, -24.89, 0.9795, 109, 58.09, -33.7, 0.0205, 2, 110, -31.61, -24.34, 0.42958, 109, 20.49, -33.14, 0.57042, 4, 110, -69.21, -23.78, 0.01569, 109, -17.1, -32.59, 0.84113, 108, 35.22, -65.5, 0.13358, 107, 84.94, -80.01, 0.0096, 3, 109, -54.7, -32.03, 0.42989, 108, -2.38, -64.95, 0.42809, 107, 47.35, -79.46, 0.14202, 3, 109, -92.29, -31.48, 0.11883, 108, -39.97, -64.39, 0.38868, 107, 9.75, -78.9, 0.49249, 3, 109, -129.89, -30.92, 0.02925, 108, -77.57, -63.84, 0.26699, 107, -27.84, -78.35, 0.70377, 3, 109, -129.19, 16.57, 0.00363, 108, -76.87, -16.34, 0.11056, 107, -27.14, -30.85, 0.88581, 3, 109, -91.59, 16.02, 0.02624, 108, -39.27, -16.9, 0.35516, 107, 10.45, -31.41, 0.6186, 3, 109, -54, 15.46, 0.16197, 108, -1.67, -17.45, 0.76234, 107, 48.05, -31.96, 0.07569, 3, 110, -68.5, 23.71, 0.00428, 109, -16.4, 14.91, 0.66015, 108, 35.92, -18.01, 0.33557, 3, 110, -30.91, 23.16, 0.34917, 109, 21.2, 14.35, 0.61596, 108, 73.52, -18.56, 0.03487], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 0], "width": 188, "height": 95}}, "s2": {"s2": {"type": "mesh", "uvs": [1, 0.53259, 0.91991, 0.46617, 0.79413, 0.51537, 0.67962, 0.59654, 0.62143, 0.61868, 0.52382, 0.56456, 0.43747, 0.46371, 0.42245, 0.39484, 0.50505, 0.27677, 0.49191, 0.11196, 0.42433, 0.01111, 0.27227, 0, 0.0395, 0.00865, 0, 0.1464, 0, 0.28907, 0, 0.51783, 0.12398, 0.62606, 0.27603, 0.66049, 0.33234, 0.8376, 0.47877, 0.96058, 0.69652, 1, 0.87298, 1, 0.97247, 0.7884, 0.79755, 0.78476, 0.62944, 0.78285, 0.49083, 0.72729, 0.41249, 0.60276, 0.33706, 0.48206, 0.20481, 0.32113, 0.25689, 0.13146, 0.38937, 0.2445], "triangles": [29, 12, 11, 13, 12, 29, 10, 29, 11, 30, 10, 9, 30, 29, 10, 30, 9, 8, 29, 28, 13, 28, 29, 30, 7, 30, 8, 28, 14, 13, 27, 28, 30, 15, 14, 28, 16, 15, 28, 16, 28, 27, 7, 27, 30, 27, 7, 6, 26, 27, 6, 26, 6, 5, 17, 16, 27, 17, 27, 26, 25, 26, 5, 25, 5, 4, 18, 17, 26, 18, 26, 25, 19, 18, 25, 24, 4, 3, 25, 4, 24, 2, 22, 23, 0, 2, 1, 3, 2, 23, 24, 3, 23, 0, 22, 2, 19, 25, 24, 20, 24, 23, 19, 24, 20, 21, 23, 22, 20, 23, 21], "vertices": [1, 61, -15.4, -18.69, 1, 1, 61, -6.39, -24.65, 1, 1, 61, 8.03, -20.67, 1, 2, 61, 21.23, -13.88, 0.97918, 62, -1.64, -23.29, 0.02082, 2, 61, 27.91, -12.1, 0.83403, 62, 1.33, -17.06, 0.16597, 2, 61, 38.93, -17.03, 0.17196, 62, 12.25, -11.89, 0.82804, 4, 61, 48.59, -26.01, 0.00035, 62, 25.35, -10.37, 0.86629, 63, -7.55, -10.26, 0.09955, 64, 4.88, -34.05, 0.03381, 3, 62, 30.96, -13.08, 0.38484, 63, -1.98, -13.05, 0.42997, 64, 8.53, -29, 0.18519, 3, 62, 32.37, -26.95, 0.05022, 63, -0.79, -26.94, 0.38974, 64, 22.42, -30.04, 0.56004, 3, 62, 44.07, -35.37, 0.00326, 63, 10.78, -35.54, 0.2065, 64, 32.76, -19.99, 0.79024, 2, 63, 22.45, -35.8, 0.08334, 64, 34.89, -8.51, 0.91666, 1, 64, 24.92, 5.71, 1, 2, 63, 52.29, -3.64, 0.00263, 64, 7.92, 26.09, 0.99737, 2, 63, 46.52, 7.78, 0.13137, 64, -4.28, 22.21, 0.86863, 2, 63, 37.38, 16.18, 0.56142, 64, -14.04, 14.53, 0.43858, 3, 62, 55.02, 29.99, 0.00449, 63, 22.72, 29.65, 0.98563, 64, -29.67, 2.22, 0.00988, 2, 62, 38.58, 25.7, 0.12598, 63, 6.23, 25.61, 0.87402, 2, 62, 24.81, 14.76, 0.78777, 63, -7.71, 14.87, 0.21223, 2, 61, 61.25, 6.26, 0.03906, 62, 9.04, 20.21, 0.96094, 2, 61, 44.79, 17.3, 0.6885, 62, -10.05, 14.87, 0.3115, 1, 61, 20.04, 21.25, 1, 1, 61, -0.07, 21.67, 1, 1, 61, -11.8, 3.5, 1, 1, 61, 8.13, 2.77, 1, 1, 61, 27.29, 2.2, 1, 1, 62, 4.18, 0.34, 1, 3, 61, 51.69, -13.98, 0.00011, 62, 18.21, -0.2, 0.99989, 64, -6.39, -39.3, 0, 3, 62, 31.77, -0.77, 0.80301, 63, -0.98, -0.75, 0.19486, 64, -3.46, -26.04, 0.00212, 1, 63, 19.53, 0.88, 1, 2, 63, 27.66, -14.66, 0.00031, 64, 14.85, 0.01, 0.99969, 3, 62, 43.24, -18.97, 0.02863, 63, 10.2, -19.13, 0.40337, 64, 16.47, -17.94, 0.568], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60], "width": 114, "height": 87}}, "s3": {"s3": {"type": "mesh", "uvs": [1, 0.05593, 0.90861, 0, 0.77426, 0, 0.62102, 0.04019, 0.49087, 0.13235, 0.32923, 0.12786, 0.25996, 0.26048, 0.26416, 0.41108, 0.14871, 0.49649, 0.02275, 0.55044, 0, 0.70778, 0, 0.88536, 0.06474, 0.95954, 0.23057, 1, 0.38171, 1, 0.409, 0.8921, 0.36282, 0.7415, 0.44259, 0.73701, 0.57904, 0.75274, 0.58953, 0.66508, 0.56224, 0.52347, 0.45309, 0.49649, 0.57694, 0.4403, 0.75956, 0.39309, 0.89811, 0.36163, 0.97578, 0.24025, 0.82803, 0.13528, 0.59834, 0.27357, 0.41029, 0.37729, 0.33343, 0.46257, 0.22564, 0.60778, 0.16146, 0.74376, 0.19091, 0.87975, 0.38023, 0.60778, 0.49318, 0.64465], "triangles": [14, 13, 15, 12, 32, 13, 13, 32, 15, 12, 11, 32, 32, 16, 15, 11, 31, 32, 11, 10, 31, 32, 31, 16, 10, 9, 31, 31, 9, 30, 31, 30, 16, 30, 9, 8, 16, 33, 17, 16, 30, 33, 30, 8, 7, 17, 34, 18, 18, 34, 19, 17, 33, 34, 34, 20, 19, 33, 21, 34, 34, 21, 20, 30, 29, 33, 30, 7, 29, 33, 29, 21, 29, 28, 21, 21, 28, 22, 29, 7, 28, 22, 27, 23, 22, 28, 27, 7, 6, 28, 24, 23, 26, 6, 5, 28, 28, 4, 27, 28, 5, 4, 23, 27, 26, 24, 26, 25, 26, 3, 2, 26, 27, 3, 27, 4, 3, 25, 26, 0, 26, 1, 0, 26, 2, 1], "vertices": [1, 52, -21.22, -0.73, 1, 1, 52, -14.88, -11.78, 1, 1, 52, -0.86, -20.01, 1, 1, 52, 17.43, -25.48, 1, 1, 52, 36.28, -24.47, 1, 1, 52, 52.89, -34.81, 1, 1, 52, 67.7, -26.13, 1, 2, 52, 75.88, -11.2, 0.85545, 53, -7.56, -8.8, 0.14455, 3, 52, 92.82, -9.95, 0.00679, 53, 8.27, -14.95, 0.98896, 54, -23.34, -4.03, 0.00425, 2, 53, 21.87, -24.14, 0.83339, 54, -15.98, -18.71, 0.16661, 2, 53, 38.13, -16.43, 0.25962, 54, 1.97, -19.96, 0.74038, 1, 54, 21.96, -18.27, 1, 1, 54, 29.66, -9.76, 1, 2, 53, 49.79, 25.19, 0.00539, 54, 32.53, 10.62, 0.99461, 2, 53, 39.53, 40.32, 0.0415, 54, 30.99, 28.84, 0.9585, 3, 52, 88.29, 44.55, 0.0052, 53, 27.58, 36.21, 0.10197, 54, 18.56, 31.11, 0.89283, 3, 52, 84.49, 27.04, 0.18335, 53, 16.64, 22.04, 0.46986, 54, 2.08, 24.11, 0.3468, 3, 52, 75.91, 31.49, 0.5054, 53, 10.8, 29.74, 0.42404, 54, 0.76, 33.69, 0.07056, 3, 52, 62.57, 41.38, 0.67116, 53, 3, 44.4, 0.32716, 54, 1.14, 50.29, 0.00168, 3, 52, 56.46, 33.49, 0.69702, 53, -5.91, 39.89, 0.30285, 54, -8.83, 50.72, 0.00013, 2, 52, 51.21, 18.01, 0.78132, 53, -17.3, 28.17, 0.21868, 3, 52, 61.05, 8.7, 0.91607, 53, -12.41, 15.53, 0.08384, 54, -26.43, 32.67, 9e-05, 1, 52, 44.92, 10.81, 1, 1, 52, 23.16, 17.4, 1, 1, 52, 6.9, 22.82, 1, 1, 52, -8.15, 15.75, 1, 1, 52, 1.27, -3.53, 1, 1, 52, 33.14, -4.13, 1, 1, 52, 58.7, -5.54, 1, 1, 52, 71.6, -1.94, 1, 1, 53, 13.45, -0.18, 1, 1, 54, 4.38, -0.15, 1, 2, 53, 41.24, 13.59, 0.00975, 54, 19.39, 4.69, 0.99025, 3, 52, 75.02, 15.08, 0.49622, 53, 2.95, 15.3, 0.46981, 54, -13.16, 24.94, 0.03397, 3, 52, 65.35, 25.59, 0.66364, 53, -1.27, 28.95, 0.32337, 54, -10.15, 38.91, 0.01299], "hull": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 0, 50, 0, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 60, 66, 66, 68], "width": 121, "height": 113}}, "s4": {"s4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [74.92, -12.74, -7.14, -38.79, -22.2, 10, 59.86, 36.06], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 85, "height": 51}}, "s5": {"s5": {"type": "mesh", "uvs": [0.61335, 0.4274, 0.72885, 0.42251, 0.8691, 0.41762, 0.99698, 0.35407, 1, 0.25873, 0.9516, 0.12184, 0.81135, 0.06807, 0.71441, 0, 0.53704, 0, 0.42566, 0.0974, 0.3576, 0.24895, 0.34523, 0.34184, 0.25241, 0.41273, 0.13485, 0.42007, 0.05648, 0.52273, 0, 0.67918, 0.00491, 0.82829, 0.12248, 0.9554, 0.27304, 1, 0.29573, 0.89184, 0.36379, 0.76718, 0.45454, 0.72318, 0.55766, 0.73784, 0.52879, 0.87473, 0.62366, 0.90162, 0.72266, 0.75984, 0.71854, 0.63273, 0.66491, 0.52762, 0.65855, 0.25927, 0.42701, 0.49726, 0.23449, 0.6655, 0.17792, 0.80502, 0.58159, 0.60805, 0.62181, 0.76398], "triangles": [18, 17, 19, 17, 31, 19, 17, 16, 31, 19, 31, 20, 16, 15, 31, 31, 30, 20, 31, 15, 30, 20, 30, 29, 15, 14, 30, 14, 13, 30, 30, 13, 12, 23, 33, 24, 24, 33, 25, 23, 22, 33, 21, 20, 29, 22, 32, 33, 33, 26, 25, 33, 32, 26, 22, 21, 32, 21, 29, 32, 30, 12, 29, 32, 27, 26, 32, 0, 27, 32, 29, 0, 12, 11, 29, 28, 29, 11, 10, 28, 11, 28, 0, 29, 0, 28, 1, 1, 28, 2, 3, 2, 4, 5, 4, 2, 5, 2, 28, 28, 6, 5, 10, 9, 28, 9, 8, 28, 28, 7, 6, 28, 8, 7], "vertices": [2, 55, 37.17, 5.61, 0.62757, 56, -1.42, 6.38, 0.37243, 1, 55, 26.64, 9.11, 1, 1, 55, 13.88, 13.44, 1, 1, 55, 0.58, 12.91, 1, 1, 55, -2.39, 5.77, 1, 1, 55, -1.9, -6.24, 1, 1, 55, 9.19, -15.02, 1, 2, 55, 15.99, -23.44, 0.99317, 56, -30.66, -14.54, 0.00683, 2, 55, 31.94, -29.39, 0.87497, 56, -17.38, -25.2, 0.12503, 2, 55, 44.72, -25.73, 0.63687, 56, -4.1, -25.74, 0.36313, 2, 55, 55.13, -16.5, 0.22927, 56, 8.68, -20.26, 0.77073, 3, 55, 58.87, -9.87, 0.04723, 56, 14.31, -15.13, 0.94814, 57, -17.84, -10.76, 0.00463, 2, 56, 24.86, -16.23, 0.75749, 57, -7.98, -14.63, 0.24251, 2, 56, 34.03, -22.83, 0.36834, 57, -0.9, -23.44, 0.63166, 2, 56, 45.1, -21.06, 0.14425, 57, 10.24, -24.69, 0.85575, 2, 56, 57.26, -14.57, 0.0111, 57, 23.7, -21.68, 0.9889, 1, 57, 33.22, -14.24, 1, 1, 57, 34.98, 0.93, 1, 1, 57, 29.47, 14.77, 1, 1, 57, 21.08, 11.42, 1, 2, 56, 34.49, 12.85, 0.09387, 57, 9.07, 10.82, 0.90613, 2, 56, 25.46, 15.53, 0.6445, 57, 1.09, 15.81, 0.3555, 2, 56, 18.49, 22.65, 0.95391, 57, -3.73, 24.54, 0.04609, 2, 56, 27.59, 29.56, 0.99881, 57, 6.88, 28.77, 0.00119, 1, 56, 21.85, 36.96, 1, 1, 56, 7.25, 33.95, 1, 2, 55, 33.51, 24.72, 0.00522, 56, 1.11, 25.67, 0.99478, 2, 55, 35.36, 14.95, 0.0683, 56, -0.2, 15.81, 0.9317, 2, 55, 28.35, -5.63, 0.99672, 56, -13.33, -1.52, 0.00328, 1, 56, 16.07, -0.4, 1, 2, 56, 39.01, -1.34, 0.00625, 57, 9.64, -4.06, 0.99375, 1, 57, 21.99, -1.87, 1, 3, 55, 45.13, 18.26, 0.00821, 56, 10.11, 15.89, 0.97148, 57, -13.61, 20.26, 0.02031, 2, 56, 15.01, 28.15, 0.99131, 57, -5.61, 30.77, 0.00869], "hull": 28, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 0, 54, 10, 56, 56, 58, 58, 60, 60, 62, 58, 64, 64, 66], "width": 96, "height": 81}}, "m1": {"m1": {"type": "mesh", "uvs": [0.19631, 1, 0.14344, 0.9623, 0.07985, 0.91697, 0.00832, 0.79699, 0, 0.6661, 0.09316, 0.55582, 0.21461, 0.4625, 0.35103, 0.37403, 0.49243, 0.3098, 0.58061, 0.28798, 0.57562, 0.20921, 0.63883, 0.12558, 0.75862, 0.05045, 0.81185, 0, 0.85843, 0, 0.89836, 0.05529, 0.97322, 0.11589, 1, 0.17288, 1, 0.23398, 0.97899, 0.29778, 0.97899, 0.39825, 0.9734, 0.515, 0.93054, 0.63447, 0.84108, 0.76345, 0.74417, 0.84898, 0.58763, 0.91143, 0.45904, 0.94537, 0.26335, 1, 0.1961, 0.91825, 0.24434, 0.77975, 0.35501, 0.66916, 0.48554, 0.58234, 0.63168, 0.48105, 0.69411, 0.37252, 0.68418, 0.24229, 0.73384, 0.141, 0.79771, 0.06346, 0.29887, 0.92563, 0.40322, 0.84201, 0.55453, 0.72291, 0.68497, 0.62282, 0.78758, 0.505, 0.8415, 0.3859, 0.84324, 0.26301, 0.85889, 0.17305, 0.85541, 0.06156, 0.62337, 0.32451, 0.54676, 0.36482, 0.40629, 0.45887, 0.29988, 0.53329, 0.21475, 0.60874, 0.11686, 0.72243, 0.10834, 0.80409, 0.12679, 0.92191], "triangles": [45, 14, 15, 16, 45, 15, 44, 16, 17, 44, 17, 18, 36, 12, 13, 35, 11, 12, 14, 36, 13, 10, 11, 34, 47, 7, 8, 42, 19, 20, 47, 8, 9, 9, 10, 34, 21, 42, 20, 19, 43, 18, 14, 45, 36, 35, 12, 36, 16, 44, 45, 36, 45, 44, 35, 36, 44, 34, 11, 35, 44, 34, 35, 43, 34, 44, 32, 47, 33, 33, 34, 43, 46, 34, 33, 42, 33, 43, 42, 43, 19, 41, 33, 42, 46, 9, 34, 47, 9, 46, 33, 47, 46, 43, 44, 18, 22, 41, 21, 23, 40, 22, 24, 39, 23, 6, 7, 48, 40, 32, 41, 31, 32, 40, 39, 31, 40, 40, 41, 22, 30, 49, 31, 49, 6, 48, 47, 48, 7, 48, 47, 32, 49, 48, 31, 31, 48, 32, 32, 33, 41, 41, 42, 21, 39, 40, 23, 2, 53, 1, 50, 5, 6, 50, 4, 5, 26, 38, 25, 27, 37, 26, 25, 39, 24, 3, 4, 51, 2, 3, 52, 53, 2, 52, 1, 28, 0, 37, 28, 29, 28, 53, 52, 1, 53, 28, 38, 37, 29, 27, 0, 28, 37, 27, 28, 37, 38, 26, 38, 39, 25, 30, 31, 39, 29, 50, 30, 49, 50, 6, 50, 49, 30, 51, 50, 29, 51, 4, 50, 52, 3, 51, 52, 51, 29, 28, 52, 29, 38, 30, 39, 29, 30, 38], "vertices": [1, 77, -13.28, -3.49, 1, 1, 77, -11.13, 4.36, 1, 1, 77, -8.53, 13.8, 1, 2, 77, 3.75, 29.44, 0.99982, 78, -50, 20.38, 0.00018, 3, 77, 21.05, 39.76, 0.96564, 78, -34.96, 33.78, 0.03434, 79, -53.58, 69.39, 2e-05, 3, 77, 41.24, 38.79, 0.78047, 78, -14.95, 36.63, 0.2103, 79, -34.83, 61.84, 0.00922, 3, 77, 60.7, 33.86, 0.36372, 78, 5.1, 35.45, 0.56093, 79, -18.06, 50.79, 0.07536, 3, 77, 80.35, 27.12, 0.05138, 78, 25.66, 32.54, 0.63177, 79, -1.72, 37.98, 0.31684, 3, 77, 96.99, 18.15, 0.0002, 78, 43.7, 26.86, 0.2484, 79, 11.05, 24.03, 0.75139, 2, 78, 52.76, 21.54, 0.03447, 79, 16.23, 14.89, 0.96553, 1, 79, 28.11, 17.34, 1, 1, 79, 42.17, 12.43, 1, 1, 79, 56.13, 1.12, 1, 1, 79, 64.93, -3.5, 1, 1, 79, 65.92, -8.6, 1, 1, 79, 58.35, -14.31, 1, 1, 79, 50.72, -23.98, 1, 2, 78, 97.21, -2.64, 0.0006, 79, 42.62, -28.29, 0.9994, 2, 78, 89.91, -8.56, 0.01272, 79, 33.33, -29.76, 0.98728, 2, 78, 80.74, -12.99, 0.07649, 79, 23.18, -29, 0.92351, 3, 77, 112.23, -35.27, 3e-05, 78, 68.72, -22.73, 0.37037, 79, 7.89, -31.43, 0.62961, 3, 77, 96.06, -43.23, 0.01219, 78, 54.35, -33.59, 0.77978, 79, -9.98, -33.64, 0.20803, 3, 77, 77.45, -47.78, 0.08499, 78, 36.92, -41.57, 0.89471, 79, -29.07, -31.82, 0.0203, 2, 77, 54.93, -48.53, 0.30547, 78, 14.95, -46.55, 0.69453, 2, 77, 37.89, -45.4, 0.54456, 78, -2.37, -46.68, 0.45544, 2, 77, 20.65, -34.83, 0.83002, 78, -21.3, -39.55, 0.16998, 2, 77, 8.84, -24.88, 0.96472, 78, -34.77, -32, 0.03528, 1, 77, -9.53, -9.96, 1, 1, 77, -2.2, 2.48, 1, 1, 77, 19.3, 7.9, 1, 3, 77, 40.51, 5.26, 0.98204, 78, -9.34, 3.57, 0.01761, 79, -46.53, 30.41, 0.00035, 2, 78, 10.59, 0.99, 0.99889, 79, -30.56, 18.2, 0.00111, 2, 77, 81.54, -7.76, 0.00027, 78, 33.4, -1.5, 0.99973, 1, 79, 5.78, 0.41, 1, 1, 79, 25.37, 4.65, 1, 1, 79, 41.83, 1.65, 1, 1, 79, 54.98, -3.48, 1, 2, 77, 2.56, -7.98, 0.99987, 78, -44.13, -16.59, 0.00013, 2, 77, 19.75, -11.97, 0.97953, 78, -26.49, -17.27, 0.02047, 2, 77, 44.39, -17.91, 0.60359, 78, -1.17, -18.47, 0.39641, 2, 77, 65.28, -23.22, 0.11967, 78, 20.35, -19.75, 0.88033, 3, 77, 87.02, -24.56, 0.0109, 78, 41.95, -16.96, 0.90755, 79, -12.4, -13.03, 0.08155, 2, 78, 60.14, -9.95, 0.28704, 79, 6.86, -16.06, 0.71296, 2, 78, 74.96, 1.83, 0.0144, 79, 25.59, -13.29, 0.9856, 1, 79, 39.61, -12.83, 1, 1, 79, 56.49, -9.76, 1, 2, 78, 51.52, 14.39, 0.0175, 79, 11.58, 9.32, 0.9825, 2, 78, 41.09, 16.94, 0.25912, 79, 3.83, 16.75, 0.74088, 3, 77, 71.93, 15.61, 0.05636, 78, 19.56, 19.65, 0.76024, 79, -13.45, 29.87, 0.18341, 3, 77, 55.87, 20.47, 0.37747, 78, 2.87, 21.4, 0.57571, 79, -27.03, 39.73, 0.04682, 3, 77, 40.86, 23.2, 0.79392, 78, -12.38, 21.25, 0.19837, 79, -40.31, 47.24, 0.00771, 2, 77, 19.95, 24.38, 0.98729, 78, -33.14, 18.47, 0.01271, 1, 77, 8.39, 19.26, 1, 1, 77, -6.58, 8.91, 1], "hull": 28, "edges": [4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 0, 54, 0, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 28, 72, 72, 70, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 0, 2, 2, 4, 106, 2], "width": 110, "height": 151}}, "m2": {"m2": {"type": "mesh", "uvs": [0.35848, 1, 0.38217, 0.84262, 0.53786, 0.71451, 0.78833, 0.6058, 1, 0.47768, 1, 0.36509, 0.90002, 0.23504, 0.63263, 0.08945, 0.51417, 0, 0.33817, 0, 0.12156, 0.12633, 0.04371, 0.29133, 0, 0.47768, 0.0234, 0.64851, 0.11479, 0.85233, 0.26371, 1, 0.51892, 0.37736, 0.43845, 0.51581, 0.3258, 0.70503, 0.31513, 0.15427, 0.20928, 0.35259, 0.19516, 0.50234, 0.18811, 0.68042, 0.24456, 0.85041], "triangles": [4, 3, 16, 16, 17, 20, 16, 5, 4, 12, 11, 20, 20, 19, 16, 16, 19, 7, 16, 6, 5, 16, 7, 6, 7, 19, 8, 20, 11, 19, 19, 11, 10, 19, 9, 8, 19, 10, 9, 1, 0, 23, 14, 23, 15, 0, 15, 23, 14, 22, 23, 14, 13, 22, 23, 18, 1, 23, 22, 18, 1, 18, 2, 3, 2, 17, 2, 18, 17, 18, 22, 17, 22, 21, 17, 22, 13, 21, 13, 12, 21, 3, 17, 16, 17, 21, 20, 21, 12, 20], "vertices": [1, 80, -27.93, 5.35, 1, 1, 80, -6.42, 0.85, 1, 2, 80, 10.15, -13.57, 0.9826, 81, -36.47, -25.58, 0.01739, 2, 80, 23.39, -35.14, 0.73728, 81, -17.42, -42.23, 0.26272, 2, 80, 39.56, -53.97, 0.44352, 81, 3.63, -55.4, 0.55648, 2, 80, 55.07, -55.85, 0.31132, 81, 18.99, -52.58, 0.68868, 2, 80, 73.67, -50.15, 0.13943, 81, 35.05, -41.6, 0.86057, 2, 80, 95.57, -31.5, 0.00382, 81, 50.41, -17.28, 0.99618, 1, 81, 60.62, -5.89, 1, 1, 81, 57.65, 7.72, 1, 1, 81, 36.76, 21.32, 1, 2, 80, 71.84, 18.31, 0.02487, 81, 12.94, 23.21, 0.97513, 2, 80, 46.48, 24.87, 0.7582, 81, -13.23, 21.93, 0.2418, 2, 80, 22.79, 25.89, 0.99985, 81, -36.14, 15.85, 0.00015, 1, 80, -5.91, 22.09, 1, 1, 80, -27.27, 12.82, 1, 2, 80, 56.71, -17.72, 0.23194, 81, 9.21, -15.69, 0.76806, 2, 80, 38.2, -9.06, 0.90776, 81, -11.04, -12.93, 0.09224, 1, 80, 12.92, 2.99, 1, 1, 81, 36.21, 5.65, 1, 2, 80, 62.26, 6.28, 0.02255, 81, 7.37, 8.88, 0.97745, 2, 80, 41.73, 9.9, 0.95163, 81, -13.3, 6.22, 0.04837, 1, 80, 17.26, 13.43, 1, 1, 80, -6.54, 11.83, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30, 12, 32, 32, 34, 34, 36, 36, 2, 16, 38, 38, 40, 40, 42, 42, 44, 44, 46], "width": 78, "height": 136}}, "m3": {"m3": {"type": "mesh", "uvs": [0, 1, 0, 0.79041, 0.04272, 0.57254, 0.15989, 0.35466, 0.29989, 0.24441, 0.39119, 0.10266, 0.52511, 0, 0.68184, 0, 0.84011, 0, 0.95728, 0, 0.97097, 0.16829, 0.97706, 0.35991, 0.99989, 0.59879, 1, 0.82716, 0.86293, 0.87178, 0.79141, 0.99778, 0.62554, 1, 0.45663, 0.92691, 0.28772, 0.90328, 0.11272, 0.95053, 0.1235, 0.78853, 0.28013, 0.63288, 0.4657, 0.51247, 0.69724, 0.43024, 0.88792, 0.37444, 0.81131, 0.63288, 0.65978, 0.74741, 0.51848, 0.75622, 0.35674, 0.75329, 0.25629, 0.79146, 0.07583, 0.61819, 0.21373, 0.48016, 0.38398, 0.36269, 0.56104, 0.27459, 0.70405, 0.22466, 0.86068, 0.18942], "triangles": [16, 26, 15, 16, 27, 26, 15, 26, 14, 26, 25, 14, 13, 14, 12, 14, 25, 12, 26, 23, 25, 26, 22, 23, 12, 24, 11, 12, 25, 24, 25, 23, 24, 22, 33, 23, 24, 34, 35, 24, 23, 34, 23, 33, 34, 11, 24, 10, 32, 5, 33, 24, 35, 10, 5, 6, 33, 33, 7, 34, 33, 6, 7, 34, 8, 35, 34, 7, 8, 35, 9, 10, 35, 8, 9, 17, 27, 16, 19, 0, 20, 19, 29, 18, 19, 20, 29, 20, 0, 1, 18, 28, 17, 17, 28, 27, 18, 29, 28, 29, 21, 28, 29, 20, 21, 1, 30, 20, 1, 2, 30, 20, 31, 21, 20, 30, 31, 28, 22, 27, 27, 22, 26, 28, 21, 22, 21, 32, 22, 21, 31, 32, 31, 30, 3, 30, 2, 3, 22, 32, 33, 31, 4, 32, 31, 3, 4, 32, 4, 5], "vertices": [1, 82, -13.66, -4.42, 1, 1, 82, -4.85, 10.24, 1, 1, 82, 9.54, 22.57, 1, 2, 82, 33.04, 29.83, 0.99873, 83, -35.76, 25.12, 0.00127, 2, 82, 54.8, 28.01, 0.81397, 83, -13.96, 26.33, 0.18603, 2, 82, 71.93, 31.71, 0.36106, 83, 2.5, 32.36, 0.63894, 2, 82, 92.63, 29.77, 0.05216, 83, 23.27, 33.31, 0.94784, 1, 83, 43.74, 25.4, 1, 1, 83, 64.41, 17.41, 1, 1, 83, 79.71, 11.49, 1, 1, 83, 76.12, -1.84, 1, 1, 83, 70.8, -16.53, 1, 2, 82, 125.56, -44.43, 1e-05, 83, 66.15, -35.62, 0.99999, 2, 82, 115.97, -60.41, 0.0027, 83, 58.87, -52.77, 0.9973, 2, 82, 97.32, -54.2, 0.04111, 83, 39.54, -49.2, 0.95889, 2, 82, 83.28, -58.15, 0.10385, 83, 26.17, -55.05, 0.89615, 2, 82, 62.89, -47.01, 0.29851, 83, 4.44, -46.84, 0.70149, 2, 82, 45.29, -30.4, 0.75027, 83, -15.29, -32.83, 0.24973, 2, 82, 25.61, -17.24, 0.99538, 83, -36.6, -22.53, 0.00462, 1, 82, 2.22, -8.63, 1, 1, 82, 10.34, 1.96, 1, 1, 82, 36.05, 2.19, 1, 2, 82, 63.82, -2.03, 0.77688, 83, -0.86, -2.17, 0.22312, 2, 82, 95.61, -12.04, 0.00289, 83, 32, -7.68, 0.99711, 1, 83, 58.69, -13.12, 1, 2, 82, 101.05, -33.98, 0.02026, 83, 40.43, -28.66, 0.97974, 2, 82, 77.69, -31.67, 0.17406, 83, 16.98, -29.61, 0.82594, 2, 82, 60.03, -22.67, 0.55873, 83, -1.76, -23.13, 0.44127, 2, 82, 40.37, -11.45, 0.97686, 83, -22.79, -14.75, 0.02314, 1, 82, 26.47, -7.28, 1, 1, 82, 11.67, 17.12, 1, 2, 82, 34.35, 17.39, 0.99952, 83, -32.74, 12.98, 0.00048, 2, 82, 60.12, 14.01, 0.76442, 83, -6.75, 13.2, 0.23558, 2, 82, 85.49, 8.12, 0.01183, 83, 19.19, 10.88, 0.98817, 1, 83, 39.46, 7.41, 1, 1, 83, 61.04, 2.15, 1], "hull": 20, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 0, 38, 0, 40, 40, 42, 42, 44, 44, 46, 46, 48, 24, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 38, 2, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70], "width": 138, "height": 80}}, "m4": {"m4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-60.13, 101.89, 11.87, 20.63, -32.12, -15.8, -104.13, 65.46], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 107, "height": 56}}, "m5": {"m5": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-6.03, -45.73, 20.24, 7.5, 66.82, -14.07, 40.54, -67.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 59, "height": 50}}, "m6": {"m6": {"type": "mesh", "uvs": [0, 0.47403, 0.06358, 0.24712, 0.1736, 0.07197, 0.31111, 0, 0.44574, 0.03216, 0.5572, 0.18741, 0.66432, 0.15158, 0.75262, 0.0242, 0.85105, 0.21926, 0.96106, 0.47801, 1, 0.68899, 0.92777, 0.86016, 0.82355, 0.97162, 0.68169, 1, 0.51522, 1, 0.35599, 0.85618, 0.20255, 0.7686, 0.09543, 0.70889, 0.01292, 0.57355, 0.0853, 0.42377, 0.19057, 0.38814, 0.33147, 0.39704, 0.4675, 0.48612, 0.62621, 0.57073, 0.77844, 0.58409, 0.91772, 0.61527, 0.11283, 0.55292, 0.22944, 0.57964, 0.36871, 0.63309, 0.49179, 0.73107, 0.61973, 0.7756, 0.75901, 0.78896, 0.86104, 0.79787, 0.19219, 0.20554, 0.32499, 0.20109, 0.46264, 0.25453, 0.56143, 0.35696, 0.65698, 0.35251, 0.78816, 0.31243], "triangles": [14, 30, 13, 13, 31, 12, 13, 30, 31, 14, 29, 30, 12, 32, 11, 12, 31, 32, 32, 25, 11, 11, 25, 10, 31, 24, 32, 32, 24, 25, 30, 23, 31, 31, 23, 24, 30, 29, 23, 29, 22, 23, 25, 9, 10, 24, 38, 25, 38, 8, 25, 25, 8, 9, 23, 37, 24, 24, 37, 38, 22, 36, 23, 23, 36, 37, 21, 35, 22, 22, 35, 36, 35, 5, 36, 36, 5, 37, 37, 6, 38, 37, 5, 6, 6, 7, 38, 38, 7, 8, 34, 4, 35, 35, 4, 5, 34, 3, 4, 15, 29, 14, 16, 27, 15, 15, 28, 29, 15, 27, 28, 17, 26, 16, 16, 26, 27, 28, 22, 29, 17, 18, 26, 27, 21, 28, 28, 21, 22, 26, 20, 27, 27, 20, 21, 18, 19, 26, 18, 0, 19, 26, 19, 20, 0, 1, 19, 20, 19, 33, 21, 20, 34, 20, 33, 34, 21, 34, 35, 33, 19, 1, 1, 2, 33, 34, 33, 3, 33, 2, 3], "vertices": [1, 84, -12.67, -0.42, 1, 2, 84, -3.1, 11.65, 0.9999, 85, -53.01, 7.28, 0.0001, 2, 84, 13.13, 21.01, 0.95466, 85, -37.52, 17.82, 0.04534, 2, 84, 33.19, 24.92, 0.60344, 85, -17.8, 23.2, 0.39656, 2, 84, 52.68, 23.31, 0.05042, 85, 1.75, 23.04, 0.94958, 1, 85, 18.24, 16.08, 1, 1, 85, 33.65, 19.21, 1, 1, 85, 46.12, 26.97, 1, 1, 85, 60.83, 17.76, 1, 1, 85, 77.36, 5.31, 1, 1, 85, 83.5, -5.43, 1, 1, 85, 73.47, -15.32, 1, 2, 84, 106.07, -26.24, 0.00035, 85, 58.66, -22.42, 0.99965, 2, 84, 85.44, -27.84, 0.05091, 85, 38.21, -25.55, 0.94909, 2, 84, 61.29, -27.96, 0.46818, 85, 14.13, -27.45, 0.53182, 2, 84, 38.4, -20.44, 0.99458, 85, -9.25, -21.65, 0.00542, 1, 84, 16.27, -15.9, 1, 1, 84, 0.82, -12.81, 1, 1, 84, -10.95, -5.69, 1, 1, 84, -0.22, 2.3, 1, 2, 84, 15.11, 4.26, 0.99512, 85, -34.31, 1.26, 0.00488, 2, 84, 35.54, 3.89, 0.92061, 85, -13.9, 2.4, 0.07939, 2, 84, 55.15, -0.74, 0.07552, 85, 5.99, -0.77, 0.92448, 2, 84, 78.05, -5.12, 0.00996, 85, 29.15, -3.44, 0.99004, 1, 85, 51.2, -2.4, 1, 1, 85, 71.42, -2.46, 1, 1, 84, 3.58, -4.53, 1, 1, 84, 20.46, -5.86, 1, 1, 84, 40.59, -8.6, 1, 2, 84, 58.3, -13.71, 0.53947, 85, 10.09, -13.47, 0.46053, 2, 84, 76.79, -15.99, 0.09397, 85, 28.71, -14.36, 0.90603, 2, 84, 96.98, -16.6, 0.0038, 85, 48.89, -13.48, 0.9962, 1, 85, 63.67, -12.79, 1, 2, 84, 15.62, 13.94, 0.96075, 85, -34.51, 10.95, 0.03925, 2, 84, 34.9, 14.27, 0.64308, 85, -15.31, 12.71, 0.35692, 1, 85, 4.73, 11.45, 1, 1, 85, 19.26, 7.15, 1, 1, 85, 33.08, 8.48, 1, 1, 85, 51.95, 12.1, 1], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36, 0, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 36, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 2, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76], "width": 143, "height": 52}}, "ljbone": {"ljbone": {"type": "path", "lengths": [75.26, 132.55, 225.07, 304.87, 379.11, 493.9, 579.2, 694.06, 814.81, 913.62, 1013.77, 1105.94, 1153.75, 1204.79, 1272.2, 1607.4], "vertexCount": 48, "vertices": [1, 50, 161.81, 215.23, 1, 1, 50, 134.54, 192.61, 1, 1, 50, 101, 164.78, 1, 2, 44, 175.24, -324.24, 0.09551, 50, 59.43, 130.58, 0.90449, 2, 44, 148.92, -337.17, 0.09551, 50, 33.12, 117.66, 0.90449, 2, 44, 122.61, -350.09, 0.09551, 50, 6.81, 104.73, 0.90449, 2, 44, 82.95, -321.9, 0.24735, 50, -32.86, 132.92, 0.75265, 2, 44, 81.88, -286.51, 0.24735, 50, -33.92, 168.31, 0.75265, 2, 44, 80.9, -253.8, 0.24735, 50, -34.9, 201.03, 0.75265, 3, 44, 102.18, -174.26, 0.45404, 45, 202.48, 286, 0.04751, 50, -13.62, 280.57, 0.49845, 3, 44, 86.61, -132.19, 0.46008, 45, 186.91, 328.06, 0.04751, 50, -29.2, 322.63, 0.49241, 3, 44, 71.03, -90.13, 0.46661, 45, 171.33, 370.12, 0.04751, 50, -44.78, 364.7, 0.48588, 4, 44, 28.74, -49.78, 0.54507, 46, 256.84, -100.74, 0.00147, 45, 129.04, 410.47, 0.21101, 50, -87.06, 405.04, 0.24245, 4, 44, -11.59, -51.91, 0.54507, 46, 216.51, -102.86, 0.00147, 45, 88.71, 408.35, 0.21101, 50, -127.4, 402.92, 0.24245, 4, 44, -51.92, -54.03, 0.54507, 46, 176.18, -104.99, 0.00147, 45, 48.38, 406.22, 0.21101, 50, -167.73, 400.79, 0.24245, 4, 44, -80.87, -93.37, 0.51261, 46, 147.24, -144.32, 0.00098, 45, 19.44, 366.89, 0.39825, 50, -196.67, 361.46, 0.08816, 4, 44, -83.14, -141.11, 0.51261, 46, 144.97, -192.07, 0.00098, 45, 17.16, 319.15, 0.39825, 50, -198.94, 313.72, 0.08816, 4, 44, -85.41, -188.85, 0.49677, 46, 142.69, -239.81, 0.00098, 45, 14.89, 271.4, 0.41409, 50, -201.21, 265.97, 0.08816, 3, 44, -25.7, -279.31, 0.34237, 46, 202.41, -330.27, 0.12588, 45, 74.6, 180.94, 0.53176, 3, 44, -30.26, -325.43, 0.34141, 46, 197.85, -376.39, 0.12539, 45, 70.04, 134.82, 0.5332, 3, 44, -34.81, -371.55, 0.34141, 46, 193.29, -422.51, 0.12539, 45, 65.49, 88.7, 0.5332, 3, 44, -75.68, -429.54, 0.17889, 46, 152.42, -480.5, 0.26629, 45, 24.62, 30.71, 0.55482, 3, 44, -122.09, -414.77, 0.17889, 46, 106.01, -465.73, 0.26629, 45, -21.79, 45.48, 0.55482, 3, 44, -168.5, -400.01, 0.17889, 46, 59.61, -450.97, 0.26629, 45, -68.2, 60.25, 0.55482, 4, 44, -219.49, -327.22, 0.14573, 46, 8.61, -378.18, 0.46678, 45, -119.19, 133.04, 0.30178, 47, -56.1, 24.65, 0.08571, 4, 44, -240.9, -268.67, 0.12532, 46, -12.79, -319.63, 0.48718, 45, -140.59, 191.59, 0.30178, 47, -77.5, 83.2, 0.08571, 4, 44, -262.3, -210.12, 0.12532, 46, -34.19, -261.07, 0.48718, 45, -161.99, 250.14, 0.30178, 47, -98.9, 141.75, 0.08571, 4, 44, -289.23, -111.81, 0.04082, 46, -61.13, -162.77, 0.53469, 45, -188.93, 348.44, 0.18367, 47, -125.84, 240.05, 0.24082, 4, 44, -250.4, -73.67, 0.04082, 46, -22.3, -124.62, 0.53469, 45, -150.1, 386.59, 0.18367, 47, -87.01, 278.2, 0.24082, 4, 44, -211.57, -35.52, 0.04082, 46, 16.54, -86.47, 0.53469, 45, -111.27, 424.74, 0.18367, 47, -48.18, 316.35, 0.24082, 4, 44, -136.12, -98.61, 0.06122, 46, 91.98, -149.57, 0.46122, 47, 27.27, 253.25, 0.46041, 48, 171.25, 4.12, 0.01714, 4, 44, -115.95, -138.78, 0.06122, 46, 112.16, -189.74, 0.44767, 47, 47.45, 213.08, 0.47396, 48, 191.43, -36.05, 0.01714, 4, 44, -95.77, -178.95, 0.06122, 46, 132.33, -229.91, 0.4462, 47, 67.62, 172.91, 0.47543, 48, 211.6, -76.22, 0.01714, 4, 46, 144.96, -323.96, 0.24898, 47, 80.25, 78.86, 0.55592, 48, 224.23, -170.27, 0.17551, 49, 460.8, -38.2, 0.01959, 4, 46, 107.57, -345.6, 0.24898, 47, 42.86, 57.22, 0.55592, 48, 186.84, -191.92, 0.17551, 49, 423.41, -59.84, 0.01959, 4, 46, 70.18, -367.25, 0.24898, 47, 5.47, 35.57, 0.55592, 48, 149.45, -213.56, 0.17551, 49, 386.02, -81.49, 0.01959, 4, 46, -20.16, -256.01, 0.09796, 47, -84.87, 146.81, 0.56163, 48, 59.11, -102.32, 0.14367, 49, 295.68, 29.75, 0.19673, 4, 46, -45.11, -234.89, 0.09796, 47, -109.82, 167.93, 0.56163, 48, 34.16, -81.2, 0.14367, 49, 270.73, 50.87, 0.19673, 4, 46, -63.38, -219.42, 0.09796, 47, -128.09, 183.4, 0.55592, 48, 15.89, -65.74, 0.14939, 49, 252.45, 66.34, 0.19673, 4, 44, -323.44, -134.74, 0.06122, 47, -160.04, 217.12, 0.29469, 48, -16.06, -32.01, 0.36, 49, 220.51, 100.06, 0.28408, 4, 44, -358.22, -138.34, 0.06122, 47, -194.82, 213.52, 0.29469, 48, -50.84, -35.61, 0.36, 49, 185.72, 96.46, 0.28408, 4, 44, -384.61, -139.8, 0.06122, 47, -221.22, 212.07, 0.29469, 48, -77.24, -37.07, 0.35347, 49, 159.33, 95, 0.29061, 4, 44, -473.38, -100.4, 0.05612, 47, -309.99, 251.47, 0.16653, 48, -166.01, 2.33, 0.12653, 49, 70.56, 134.4, 0.65082, 4, 44, -469.73, -128.32, 0.05612, 47, -306.34, 223.55, 0.16653, 48, -162.36, -25.59, 0.12653, 49, 74.21, 106.48, 0.65082, 4, 44, -466.56, -152.57, 0.05612, 47, -303.17, 199.29, 0.16653, 48, -159.19, -49.84, 0.12653, 49, 77.38, 82.23, 0.65082, 3, 44, -425.87, -179.08, 0.10714, 48, -118.5, -76.35, 0.28714, 49, 118.07, 55.72, 0.60571, 3, 44, -373.06, -198.71, 0.10714, 48, -65.68, -95.98, 0.28714, 49, 170.89, 36.1, 0.60571, 3, 44, -330.86, -222.1, 0.16964, 48, -23.49, -119.37, 0.28714, 49, 213.08, 12.71, 0.54321]}}, "m8": {"m8": {"type": "mesh", "uvs": [0.36785, 0.00689, 0.5834, 0.10942, 0.69516, 0.23545, 0.71112, 0.36575, 0.76434, 0.50032, 0.93199, 0.621, 1, 0.72804, 1, 0.84347, 0.87998, 0.95615, 0.69167, 1, 0.50337, 1, 0.27056, 1, 0.10279, 0.91355, 0.10279, 0.7624, 0.06171, 0.60299, 0, 0.47932, 0, 0.34191, 0.08568, 0.20449, 0.22947, 0.09593, 0.31605, 0.12631, 0.29881, 0.237, 0.28158, 0.38228, 0.32179, 0.55524, 0.44245, 0.72819, 0.47693, 0.89423, 0.51905, 0.24207, 0.52346, 0.38187, 0.58077, 0.5305, 0.6954, 0.70922, 0.7439, 0.87025, 0.17076, 0.22438, 0.14872, 0.36948, 0.16194, 0.52696, 0.1884, 0.60836, 0.23248, 0.757, 0.30302, 0.91979], "triangles": [19, 18, 0, 1, 19, 0, 30, 17, 18, 19, 30, 18, 20, 30, 19, 25, 19, 1, 25, 1, 2, 20, 19, 25, 16, 17, 30, 25, 2, 3, 31, 16, 30, 31, 30, 20, 26, 25, 3, 21, 31, 20, 21, 20, 25, 21, 25, 26, 15, 16, 31, 26, 3, 4, 31, 32, 15, 21, 32, 31, 27, 26, 4, 22, 21, 26, 22, 26, 27, 32, 21, 22, 14, 15, 32, 33, 32, 22, 14, 32, 33, 5, 28, 27, 5, 27, 4, 28, 5, 6, 23, 22, 27, 23, 27, 28, 33, 22, 23, 34, 33, 23, 13, 14, 33, 13, 33, 34, 29, 28, 6, 7, 29, 6, 23, 28, 29, 24, 23, 29, 34, 23, 24, 12, 13, 34, 35, 12, 34, 24, 35, 34, 8, 29, 7, 11, 12, 35, 9, 10, 24, 29, 9, 24, 35, 24, 10, 11, 35, 10, 8, 9, 29], "vertices": [95.59, -7.44, 83.85, -23.61, 67, -34.75, 48.01, -40.6, 28.97, -48.73, 13.79, -62.86, -0.94, -70.79, -17.99, -75.16, -36.54, -72.58, -45.99, -63.51, -48.97, -52.78, -52.65, -39.5, -42.53, -26.67, -20.2, -20.94, 2.7, -12.56, 19.99, -4.36, 40.29, 0.84, 61.94, 1.16, 80.25, -2.92, 77.13, -9.01, 60.51, -12.22, 38.77, -16.74, 13.86, -25.58, -9.78, -39.01, -33.76, -47.26, 63.24, -24.96, 42.66, -30.51, 21.61, -39.41, -2.98, -52.71, -26, -61.57, 60.35, -4.44, 38.57, -8.68, 15.51, -15.4, 3.91, -19.99, -17.35, -28.13, -40.29, -38.32], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36, 0, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 2, 50, 50, 52, 52, 54, 54, 56, 56, 58, 36, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70], "width": 59, "height": 147}}, "yj": {"yj": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [22.3, 13.47, 37.1, -3.24, 16.68, -20.16, 1.88, -3.45], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 22, "height": 26}}, "sp2": {"sp2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2.92, -55.19, -61.65, 10.74, 9.76, 79.78, 74.32, 13.85], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 92, "height": 99}}, "st1": {"st1": {"type": "mesh", "uvs": [0.27223, 0.39793, 0.27341, 0.36828, 0.31606, 0.33092, 0.3676, 0.30939, 0.43276, 0.29802, 0.48434, 0.32145, 0.5355, 0.3634, 0.5676, 0.41223, 0.57763, 0.46588, 0.57663, 0.52365, 0.55757, 0.57317, 0.52948, 0.61581, 0.48734, 0.65295, 0.42414, 0.69422, 0.36094, 0.72104, 0.30777, 0.74855, 0.27066, 0.78019, 0.25661, 0.8077, 0.30477, 0.81939, 0.3459, 0.80014, 0.40207, 0.79463, 0.47731, 0.79257, 0.54453, 0.78913, 0.60271, 0.78019, 0.66792, 0.75199, 0.73714, 0.72998, 0.80937, 0.7121, 0.86053, 0.68734, 0.9125, 0.67057, 0.94946, 0.66433, 1, 0.65579, 1, 0.70095, 0.9604, 0.74035, 0.92208, 0.77483, 0.87418, 0.81342, 0.82269, 0.85282, 0.7724, 0.88813, 0.71253, 0.9185, 0.64188, 0.95052, 0.56524, 0.97761, 0.48262, 0.99649, 0.40119, 1, 0.3042, 1, 0.2084, 0.99321, 0.13895, 0.97597, 0.06119, 0.9382, 0.01487, 0.89145, 0, 0.84028, 0.00329, 0.77677, 0.0406, 0.71855, 0.09078, 0.66916, 0.14996, 0.63299, 0.20529, 0.60565, 0.25675, 0.58007, 0.3005, 0.55537, 0.34553, 0.51567, 0.3481, 0.47421, 0.32495, 0.43452, 0.29921, 0.41511, 0.36634, 0.35024, 0.4029, 0.37948, 0.43844, 0.4115, 0.46179, 0.45397, 0.45266, 0.50479, 0.43438, 0.54169, 0.40594, 0.5772, 0.36939, 0.6134, 0.32166, 0.64333, 0.27394, 0.67745, 0.22926, 0.71434, 0.19168, 0.74219, 0.15005, 0.77978, 0.13279, 0.82782, 0.14396, 0.87516, 0.17848, 0.91276, 0.23535, 0.92668, 0.30237, 0.93643, 0.38665, 0.92946, 0.475, 0.91832, 0.57654, 0.89814, 0.66184, 0.86959, 0.74612, 0.82713, 0.79486, 0.7777, 0.85477, 0.73593, 0.86594, 0.70599, 0.95124, 0.69555, 0.89844, 0.74498, 0.85071, 0.77978, 0.7776, 0.74706, 0.73394, 0.77143, 0.68418, 0.79649, 0.62427, 0.82156, 0.55928, 0.83896, 0.48109, 0.84801, 0.40696, 0.8501, 0.32369, 0.85149, 0.22621, 0.83269, 0.21809, 0.7958, 0.25464, 0.74846, 0.29221, 0.722, 0.33283, 0.69555, 0.37243, 0.66909, 0.42219, 0.63011, 0.48109, 0.58555, 0.49632, 0.54865, 0.51257, 0.50967, 0.51968, 0.46441, 0.51764, 0.42891, 0.49937, 0.38923, 0.45773, 0.35302, 0.41102, 0.32935, 0.33689, 0.38366, 0.36533, 0.41429, 0.38767, 0.46093, 0.39477, 0.49992, 0.38462, 0.54656, 0.34096, 0.5758, 0.3044, 0.59947, 0.25261, 0.6308, 0.21301, 0.66283, 0.1338, 0.72618, 0.15411, 0.70042, 0.09115, 0.7582, 0.05155, 0.82782, 0.0546, 0.88004, 0.0942, 0.92181, 0.15411, 0.94826, 0.21098, 0.96358, 0.28003, 0.97332, 0.36533, 0.97263, 0.44758, 0.96288, 0.52983, 0.94617, 0.61027, 0.92389, 0.69759, 0.89117, 0.77375, 0.85358, 0.81234, 0.82434], "triangles": [85, 29, 30, 28, 29, 85, 85, 30, 31, 84, 27, 28, 84, 28, 85, 26, 27, 84, 83, 26, 84, 32, 85, 31, 86, 84, 85, 86, 83, 84, 85, 32, 86, 88, 26, 83, 33, 86, 32, 87, 83, 86, 88, 25, 26, 89, 24, 25, 89, 25, 88, 82, 88, 83, 89, 88, 82, 87, 82, 83, 87, 86, 33, 90, 24, 89, 34, 87, 33, 135, 82, 87, 135, 87, 34, 81, 89, 82, 81, 82, 135, 90, 89, 81, 35, 135, 34, 134, 81, 135, 134, 135, 35, 36, 134, 35, 23, 24, 90, 91, 23, 90, 91, 92, 22, 91, 22, 23, 80, 91, 90, 80, 90, 81, 133, 80, 81, 81, 134, 133, 36, 133, 134, 80, 92, 91, 37, 133, 36, 132, 80, 133, 132, 133, 37, 38, 132, 37, 93, 21, 22, 93, 22, 92, 80, 79, 92, 78, 94, 93, 93, 92, 79, 78, 93, 79, 132, 79, 80, 131, 78, 79, 131, 79, 132, 130, 78, 131, 39, 131, 132, 39, 132, 38, 40, 130, 131, 40, 131, 39, 94, 20, 21, 94, 21, 93, 19, 20, 94, 95, 18, 19, 95, 19, 94, 95, 96, 18, 77, 95, 94, 77, 94, 78, 77, 76, 95, 130, 77, 78, 129, 76, 77, 129, 77, 130, 128, 76, 129, 42, 128, 129, 41, 129, 130, 41, 130, 40, 42, 129, 41, 95, 75, 96, 74, 96, 75, 76, 75, 95, 126, 125, 74, 127, 74, 75, 126, 74, 127, 128, 75, 76, 127, 75, 128, 44, 125, 126, 44, 126, 127, 45, 125, 44, 43, 44, 127, 43, 127, 128, 43, 128, 42, 123, 122, 72, 72, 97, 96, 47, 48, 123, 73, 72, 96, 124, 123, 72, 124, 72, 73, 47, 123, 124, 46, 47, 124, 74, 73, 96, 125, 124, 73, 125, 73, 74, 45, 124, 125, 46, 124, 45, 50, 51, 121, 120, 49, 50, 121, 120, 50, 70, 121, 69, 120, 121, 70, 70, 69, 98, 122, 49, 120, 48, 49, 122, 71, 120, 70, 122, 120, 71, 16, 98, 15, 97, 70, 98, 97, 98, 16, 71, 70, 97, 17, 97, 16, 72, 122, 71, 72, 71, 97, 123, 48, 122, 96, 97, 17, 96, 17, 18, 118, 52, 53, 118, 117, 67, 119, 52, 118, 51, 52, 119, 68, 118, 67, 119, 118, 68, 100, 67, 101, 68, 67, 100, 121, 51, 119, 69, 119, 68, 121, 119, 69, 14, 100, 101, 14, 101, 13, 99, 68, 100, 99, 100, 14, 69, 68, 99, 98, 69, 99, 15, 99, 14, 98, 99, 15, 54, 55, 115, 116, 54, 115, 65, 115, 64, 116, 115, 65, 65, 64, 103, 117, 54, 116, 53, 54, 117, 66, 116, 65, 117, 116, 66, 102, 65, 103, 12, 102, 103, 66, 65, 102, 118, 53, 117, 67, 117, 66, 11, 12, 103, 101, 66, 102, 67, 66, 101, 13, 102, 12, 101, 102, 13, 8, 106, 107, 63, 114, 113, 56, 113, 114, 62, 63, 113, 63, 62, 106, 105, 63, 106, 55, 56, 114, 8, 105, 106, 9, 105, 8, 64, 114, 63, 115, 55, 114, 115, 114, 64, 104, 63, 105, 104, 105, 9, 64, 63, 104, 10, 104, 9, 103, 64, 104, 103, 104, 10, 11, 103, 10, 109, 110, 5, 109, 5, 6, 60, 110, 109, 108, 109, 6, 61, 60, 109, 61, 109, 108, 108, 6, 7, 112, 111, 60, 112, 60, 61, 107, 108, 7, 61, 108, 107, 57, 58, 112, 62, 61, 107, 113, 112, 61, 113, 61, 62, 57, 112, 113, 62, 107, 106, 8, 107, 7, 56, 57, 113, 110, 3, 4, 110, 4, 5, 59, 2, 3, 59, 3, 110, 60, 59, 110, 111, 2, 59, 111, 59, 60, 1, 2, 111, 0, 1, 111, 58, 0, 111, 58, 111, 112], "vertices": [3, 6, 38.2, 26.47, 0.82, 7, 52.84, 43.97, 0.16768, 8, 60.79, 66.67, 0.01232, 3, 6, 41.61, 13.26, 0.92872, 7, 65.32, 37.78, 0.06946, 8, 74.54, 69.81, 0.00182, 1, 6, 32.36, -6.73, 1, 2, 6, 18.17, -20.43, 0.96672, 7, 77.27, -0.54, 0.03328, 2, 6, -1.82, -30.74, 0.69617, 7, 73.01, -21.24, 0.30383, 2, 6, -21.79, -24.56, 0.31812, 7, 55.81, -31.23, 0.68188, 3, 6, -44, -10.14, 0.02178, 7, 30.75, -37.43, 0.97586, 8, 98.83, -9.13, 0.00235, 2, 7, 5.43, -36.88, 0.83653, 8, 79.06, -24.64, 0.16347, 2, 7, -18.86, -29.12, 0.32683, 8, 55.21, -34.01, 0.67317, 3, 7, -43.37, -17.43, 0.01951, 8, 28.54, -40.52, 0.95333, 9, 93.31, -24.64, 0.02716, 2, 8, 4.15, -40.58, 0.75159, 9, 71.68, -34.85, 0.24841, 3, 8, -17.84, -37.09, 0.36642, 9, 50.62, -40.86, 0.63331, 10, 102.23, -37.66, 0.00027, 3, 8, -38.48, -28.69, 0.08551, 9, 28.57, -41.89, 0.87167, 10, 80.68, -40.04, 0.04283, 3, 8, -62.79, -14.39, 0.0002, 9, 0.65, -39.12, 0.63612, 10, 53.12, -39, 0.36368, 4, 9, -22.13, -32.04, 0.1739, 10, 30.33, -33.34, 0.80558, 11, 74.93, -37.6, 0.02048, 13, 6.93, -97.91, 4e-05, 6, 9, -43.05, -27.56, 0.00828, 10, 9.53, -30.16, 0.72229, 11, 54.75, -31.27, 0.25784, 12, 68.63, -60.17, 0.00279, 13, 20.04, -81.32, 0.00607, 14, 40.15, -83.06, 0.00272, 6, 10, -9.1, -31.92, 0.24049, 11, 35.97, -30.14, 0.61752, 12, 54.81, -46.39, 0.0574, 13, 27.63, -64.26, 0.05497, 14, 53.13, -69.58, 0.02954, 15, 88.19, -78.47, 9e-05, 6, 10, -21.28, -37.54, 0.03541, 11, 22.98, -33.82, 0.47176, 12, 41.79, -40.06, 0.17289, 13, 28.56, -50.94, 0.19112, 14, 58.64, -57.38, 0.12521, 15, 95.98, -67.57, 0.00362, 7, 10, -14.53, -52.11, 0.00121, 11, 27.41, -49.26, 0.16436, 12, 33.31, -54.15, 0.09912, 13, 12.1, -49.8, 0.26209, 14, 43.81, -50.8, 0.42992, 15, 82.78, -58.26, 0.04207, 16, 110.46, -81.47, 0.00122, 6, 11, 42.25, -54.69, 0.07592, 12, 40.71, -68.29, 0.03741, 13, 1.7, -61.73, 0.15957, 14, 30.02, -58.53, 0.59061, 15, 67.79, -63.19, 0.12639, 16, 94.41, -81.37, 0.0101, 6, 11, 54.66, -67.63, 0.03781, 12, 40.4, -86.11, 0.01278, 13, -15.1, -68.85, 0.08011, 14, 11.99, -59.59, 0.57425, 15, 49.95, -60.75, 0.25602, 16, 78.06, -73.4, 0.03903, 7, 11, 69.26, -86.36, 0.01102, 12, 37.33, -109.59, 0.00102, 13, -38.27, -76.05, 0.01804, 14, -11.97, -58.59, 0.35015, 15, 26.74, -55.15, 0.45465, 16, 57.57, -60.73, 0.16319, 17, 86.48, -74.37, 0.00193, 6, 11, 82.92, -102.66, 0.00214, 13, -58.77, -83.18, 0.0016, 14, -33.42, -58.42, 0.13418, 15, 5.81, -50.86, 0.41116, 16, 38.85, -50.01, 0.41428, 17, 71.98, -58.83, 0.03664, 5, 11, 96.99, -115.2, 0.00014, 14, -52.23, -61, 0.03428, 15, -13.08, -49.76, 0.18055, 16, 21.04, -42.98, 0.59783, 17, 57.26, -47.08, 0.18719, 5, 14, -74, -72.19, 0.0022, 15, -36.56, -56.54, 0.02373, 16, -3.77, -41.94, 0.35084, 17, 34.19, -39.09, 0.62166, 18, 66.25, -49.24, 0.00157, 4, 15, -60.47, -60.24, 0.00011, 16, -28, -37.86, 0.05888, 17, 12.57, -28.35, 0.83454, 18, 50.35, -30.96, 0.10647, 2, 17, -8.15, -15.69, 0.22608, 18, 36.07, -11.27, 0.77392, 1, 18, 20.47, 1.2, 1, 1, 18, 8.42, 14.9, 1, 1, 18, 2.43, 25.33, 1, 1, 18, -5.76, 39.59, 1, 2, 17, -49.79, 27.98, 0.00036, 18, 14.7, 45.08, 0.99964, 2, 17, -27.72, 29.48, 0.07782, 18, 35.94, 37.89, 0.92218, 2, 17, -7.68, 29.89, 0.4336, 18, 54.85, 30.48, 0.5664, 2, 17, 15.74, 29.12, 0.94988, 18, 76.43, 20.68, 0.05012, 2, 16, -17.8, 23.69, 0.15707, 17, 40.15, 27.7, 0.84293, 2, 16, 4.96, 27.99, 0.75081, 17, 62.85, 25.4, 0.24919, 3, 15, -28.95, 20.63, 0.03311, 16, 28.95, 28.73, 0.96453, 17, 85.66, 19.34, 0.00235, 2, 15, -3.32, 28.62, 0.56957, 16, 56.23, 28.16, 0.43043, 3, 14, -32.8, 28.15, 0.04443, 15, 23.51, 33.92, 0.93756, 16, 83.77, 24.67, 0.01802, 2, 14, -5.86, 34.63, 0.47843, 15, 51.11, 35.09, 0.52157, 3, 13, -40.56, 21.89, 0.00527, 14, 20.11, 34.13, 0.92422, 15, 76.39, 29.59, 0.07051, 2, 13, -10.36, 29.99, 0.39826, 14, 50.89, 31.62, 0.60174, 2, 13, 20.32, 34.99, 0.96552, 14, 81.03, 26.05, 0.03448, 2, 12, -36.07, 8.52, 0.07099, 13, 44.11, 33.18, 0.92901, 2, 12, -12.91, 29.92, 0.70584, 13, 73.06, 23, 0.29416, 3, 11, -53.12, 5.93, 0.00073, 12, 13.04, 40.93, 0.99011, 13, 93.34, 6.22, 0.00916, 2, 11, -36.5, 23.28, 0.10292, 12, 39.47, 41.86, 0.89708, 2, 11, -11.88, 39.26, 0.56576, 12, 71.1, 36.28, 0.43424, 3, 10, -39.44, 39.54, 0.0464, 11, 17, 45.12, 0.89349, 12, 98.23, 20.52, 0.06011, 2, 10, -12.06, 44.03, 0.32996, 11, 44.9, 45.35, 0.67004, 3, 9, -34.92, 44.5, 0.02045, 10, 12.93, 42.26, 0.73564, 11, 69.46, 39.77, 0.24391, 3, 9, -13.6, 39.46, 0.1821, 10, 34.15, 38.54, 0.786, 11, 89.96, 32.84, 0.0319, 3, 8, -24.34, 49.87, 0.00303, 9, 6.28, 34.81, 0.59597, 10, 53.94, 35.13, 0.401, 4, 7, -18.28, 67.06, 0.00074, 8, -9.29, 39.51, 0.07078, 9, 24.23, 31.75, 0.85001, 10, 71.74, 33.18, 0.07847, 4, 6, -1.02, 72.63, 0.00405, 7, -7.65, 46.46, 0.06261, 8, 12.77, 30.53, 0.50009, 9, 47.79, 32.84, 0.43325, 4, 6, 3.45, 54.08, 0.06138, 7, 9.68, 37.55, 0.36042, 8, 32.07, 34.64, 0.50132, 9, 63.1, 44.58, 0.07687, 4, 6, 16.16, 38.39, 0.34694, 7, 29.85, 36.26, 0.5109, 8, 48.39, 46.35, 0.13735, 9, 72.39, 61.93, 0.00481, 4, 6, 27.11, 31.89, 0.64433, 7, 41.73, 39.72, 0.31433, 8, 55.15, 56.45, 0.04115, 9, 73.91, 73.85, 0.00019, 1, 6, 13.34, -2.26, 1, 3, 6, -2.43, 7.72, 0.05678, 7, 42.43, 3.31, 0.94188, 8, 80.28, 29.2, 0.00134, 2, 7, 23.8, -0.43, 0.99999, 8, 68.53, 14.64, 1e-05, 4, 6, -31.36, 35.91, 0.00023, 7, 2.42, 1.34, 0.69408, 8, 50.95, 2.54, 0.30554, 9, 94.1, 23.51, 0.00015, 1, 8, 26.79, -0.68, 1, 2, 7, -31.17, 26.44, 0.00024, 8, 8.27, 0.51, 0.99976, 2, 8, -10.46, 4.94, 0.00028, 9, 38.52, 0.1, 0.99972, 1, 9, 17.98, -1.97, 1, 2, 9, -2.67, 0.48, 0.24176, 10, 47.35, 0.32, 0.75824, 1, 10, 25.56, 0.16, 1, 5, 10, 3.53, -1.57, 0.83288, 11, 53.26, -2.09, 0.16684, 12, 90.05, -38.28, 0, 13, 48.77, -89.87, 0.00019, 14, 63.75, -100.7, 8e-05, 4, 11, 35.82, 0.16, 0.99991, 12, 78.15, -24.63, 4e-05, 13, 56.98, -74.44, 3e-05, 14, 76.74, -88.96, 1e-05, 1, 11, 13.96, 0.87, 1, 4, 11, -7.38, -7.43, 0.00161, 12, 38.47, -0.21, 0.99797, 13, 64.59, -31.71, 0.00034, 14, 98.7, -51.4, 8e-05, 3, 11, -23.24, -22.83, 0.00051, 12, 14.15, -0.28, 0.99939, 14, 96.96, -29.5, 9e-05, 1, 13, 39.72, 1.97, 1, 1, 13, 20.27, 3.37, 1, 2, 13, -1.82, 2.07, 0.38509, 14, 49.04, 2.56, 0.61491, 1, 14, 22.03, 1.56, 1, 3, 11, 21.28, -119.07, 0, 15, 43.47, 0.02, 0.99994, 16, 91.82, -13.79, 6e-05, 3, 11, 47.58, -139.73, 0, 14, -39.42, -7.84, 0.00013, 15, 9.93, -0.09, 0.99987, 1, 16, 29.29, 1.25, 1, 2, 16, -4.39, 0.33, 0.11316, 17, 45.96, 1.55, 0.88684, 3, 16, -30.52, -9.58, 0.00028, 17, 18.45, -0.56, 0.99936, 18, 67.03, -7.68, 0.00036, 1, 18, 42.98, 5.37, 1, 1, 18, 28.46, 5.11, 1, 2, 17, -42.13, 14.46, 0.00155, 18, 16.42, 29.66, 0.99845, 2, 17, -13.82, 15.57, 0.19831, 18, 43.34, 19.69, 0.80169, 2, 17, 8.19, 13.76, 0.90406, 18, 63.2, 9.48, 0.09594, 3, 16, -34.15, -24.2, 0.01174, 17, 10.77, -13.53, 0.84549, 18, 54.63, -16.63, 0.14277, 4, 15, -54.22, -42.27, 0.00119, 16, -16.06, -22.83, 0.10238, 17, 28.21, -17.32, 0.88766, 18, 69.41, -26.89, 0.00877, 4, 14, -77.46, -51.45, 0.00222, 15, -35.85, -35.54, 0.02295, 16, 3.82, -22.28, 0.50254, 17, 47.09, -22.41, 0.4723, 5, 11, 85.31, -131.68, 4e-05, 14, -57.49, -41.56, 0.01952, 15, -14.38, -29.68, 0.14651, 16, 26.4, -23.55, 0.74004, 17, 67.98, -29.99, 0.09389, 6, 11, 66.78, -119.64, 0.00093, 13, -69.6, -62.41, 0.00019, 14, -36.2, -35.3, 0.08163, 15, 7.66, -27.64, 0.51765, 16, 48.27, -28.61, 0.38697, 17, 87.1, -41, 0.01263, 7, 11, 48.99, -102.01, 0.00698, 12, 9.36, -106.77, 0.00043, 13, -46.39, -51.89, 0.01035, 14, -11.05, -33.18, 0.32355, 15, 32.66, -30.42, 0.56727, 16, 71.4, -39.18, 0.09129, 17, 105.8, -57.64, 0.00014, 6, 11, 34.58, -83.57, 0.02647, 12, 12.35, -83.64, 0.0089, 13, -23.57, -44.77, 0.06359, 14, 12.55, -34.15, 0.65918, 15, 55.53, -35.91, 0.22187, 16, 91.6, -51.64, 0.01999, 6, 11, 18.75, -62.61, 0.08809, 12, 16.2, -57.71, 0.05977, 13, 2.18, -37.21, 0.27231, 14, 39.03, -35.66, 0.53981, 15, 81.1, -42.49, 0.03906, 16, 114.03, -66, 0.00096, 6, 10, -36.24, -38.68, 0.00391, 11, 7.94, -32.66, 0.31086, 12, 30.93, -28.83, 0.32918, 13, 34.89, -37.36, 0.26016, 14, 69.24, -46.76, 0.09502, 15, 108.44, -59.2, 0.00087, 6, 10, -25.84, -25.07, 0.03041, 11, 20.4, -20.8, 0.68863, 12, 49.86, -28.97, 0.1504, 13, 42.05, -52.98, 0.08841, 14, 70.41, -63.81, 0.04203, 15, 106.21, -76.15, 0.00011, 5, 10, -2.15, -18.18, 0.42415, 11, 45.02, -17.63, 0.54743, 12, 71.57, -43.7, 0.00936, 13, 36.6, -76.93, 0.01281, 14, 57, -84.47, 0.00624, 5, 9, -36.85, -15.94, 0.00807, 10, 14.87, -18.18, 0.87014, 11, 61.93, -20.25, 0.11967, 13, 28.21, -91.75, 0.00155, 14, 44.07, -95.58, 0.00057, 3, 9, -18.93, -17.74, 0.12088, 10, 32.56, -18.87, 0.87142, 11, 79.4, -23.65, 0.0077, 2, 9, -1.22, -19.3, 0.54138, 10, 50.03, -19.34, 0.45862, 3, 8, -33.45, -6.23, 0.01571, 9, 23.08, -19.54, 0.95077, 10, 73.87, -18.08, 0.03352, 2, 8, -7.99, -18.84, 0.43406, 9, 51.27, -20.31, 0.56595, 2, 8, 10.28, -19.11, 0.86967, 9, 67.6, -12.94, 0.13033, 3, 7, -28.44, -2.04, 0.0139, 8, 29.59, -19.44, 0.98347, 9, 84.88, -5.2, 0.00263, 2, 7, -10.13, -12.99, 0.33115, 8, 51.01, -16.25, 0.66885, 2, 7, 5.3, -19.43, 0.82143, 8, 67.18, -11.45, 0.17857, 3, 6, -35.43, 4.22, 0.003, 7, 24.79, -22.09, 0.99223, 8, 83.91, -1.22, 0.00477, 2, 6, -17.09, -8.43, 0.18786, 7, 46.05, -17.45, 0.81214, 2, 6, 1.32, -15.12, 0.76052, 7, 62.68, -8.9, 0.23948, 3, 6, 18.75, 14.92, 0.75635, 7, 49.88, 22.83, 0.2319, 8, 72.8, 48.73, 0.01175, 4, 6, 5.47, 26.17, 0.28565, 7, 32.84, 20.83, 0.643, 8, 61.1, 36.49, 0.07057, 9, 88.04, 58.33, 0.00077, 4, 6, -7.86, 45, 0.04972, 7, 9.81, 23.71, 0.47977, 8, 41.51, 24.21, 0.44299, 9, 76.1, 39.11, 0.02753, 4, 6, -15.2, 61.67, 0.00694, 7, -7.81, 29.4, 0.10032, 8, 24.16, 17.45, 0.77478, 9, 63.71, 25.79, 0.11796, 3, 7, -26.29, 41.49, 0.00604, 8, 1.84, 15.03, 0.32944, 9, 44.97, 14.31, 0.66452, 4, 7, -32.66, 59.64, 8e-05, 8, -15.29, 24.83, 0.04348, 9, 25.42, 16.01, 0.93692, 10, 73.91, 17.54, 0.01952, 3, 8, -29.26, 33.12, 0.0031, 9, 9.34, 17.67, 0.77198, 10, 58.03, 18.21, 0.22492, 3, 9, -12.66, 20.68, 0.16761, 10, 36.27, 19.86, 0.8271, 11, 89.15, 14.05, 0.00528, 3, 9, -32.35, 20.58, 0.01451, 10, 16.97, 18.54, 0.90728, 11, 69.76, 15.72, 0.07821, 3, 10, -21.42, 16.13, 0.06347, 11, 31.24, 19.23, 0.93531, 12, 89.32, -7.83, 0.00122, 2, 10, -8.43, 19.79, 0.30696, 11, 44.72, 20.85, 0.69304, 3, 10, -41.39, 15.5, 0.00452, 11, 11.29, 21.67, 0.90072, 12, 75.61, 7.69, 0.09475, 2, 11, -22.31, 13.38, 0.13892, 12, 42.9, 24.98, 0.86108, 3, 11, -41.5, -1.23, 0.00029, 12, 16.58, 27.79, 0.99357, 13, 82.4, -2.13, 0.00614, 2, 12, -6.5, 18.51, 0.68471, 13, 64.83, 13, 0.31529, 2, 12, -23.02, 1.83, 0.07093, 13, 42.86, 19.68, 0.92907, 2, 13, 23.24, 21.69, 0.98795, 14, 79.08, 12.59, 0.01205, 2, 13, 0.51, 20.23, 0.64146, 14, 57.54, 18.82, 0.35854, 3, 13, -25.96, 12.8, 0.03432, 14, 30.45, 20.71, 0.96208, 15, 83.84, 14.44, 0.0036, 2, 14, 3.98, 18.38, 0.72272, 15, 57.51, 17.26, 0.27728, 2, 14, -22.76, 12.88, 0.04211, 15, 30.3, 17.02, 0.95789, 2, 15, 2.93, 14.16, 0.7036, 16, 57.48, 12.49, 0.2964, 2, 15, -27.87, 7.31, 0.00407, 16, 25.59, 15.77, 0.99593, 2, 16, -4.64, 15.23, 0.42691, 17, 50.08, 15.88, 0.57309, 2, 16, -22.68, 11.11, 0.0266, 17, 31.89, 17.03, 0.97341], "hull": 59, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 60, 62, 56, 58, 58, 60, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 0, 116, 4, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 60, 170, 170, 172, 172, 174], "width": 314, "height": 458}}, "st2": {"st2": {"type": "mesh", "uvs": [0.0137, 0.43548, 0.10016, 0.4406, 0.18238, 0.38937, 0.25892, 0.35351, 0.35247, 0.33644, 0.43752, 0.33473, 0.53958, 0.35693, 0.61896, 0.39108, 0.68133, 0.43206, 0.70259, 0.485, 0.66715, 0.54818, 0.61896, 0.61819, 0.56509, 0.67796, 0.49139, 0.74968, 0.41768, 0.80261, 0.32979, 0.87092, 0.2575, 0.92898, 0.19372, 0.9785, 0.24758, 1, 0.29523, 1, 0.34822, 1, 0.45311, 0.98703, 0.55517, 0.9563, 0.64731, 0.91361, 0.71251, 0.86238, 0.78055, 0.7992, 0.85284, 0.72919, 0.90954, 0.64893, 0.94923, 0.56867, 0.9875, 0.50037, 1, 0.40303, 0.97333, 0.29375, 0.93364, 0.21008, 0.8656, 0.12299, 0.7763, 0.06834, 0.6955, 0.03248, 0.59344, 0.01029, 0.53107, 0, 0.4361, 0, 0.33121, 0, 0.28727, 0.05127, 0.20506, 0.08713, 0.12993, 0.10762, 0.07748, 0.12982, 0.01086, 0.15202, 0, 0.18958, 0, 0.26472, 0, 0.33985, 0.07967, 0.26648, 0.1635, 0.25, 0.27983, 0.23145, 0.36879, 0.20259, 0.45775, 0.18611, 0.55526, 0.18817, 0.64593, 0.21084, 0.71778, 0.23763, 0.77424, 0.30564, 0.80845, 0.38602, 0.81187, 0.47257, 0.79306, 0.54883, 0.76568, 0.65806, 0.71436, 0.75492, 0.64422, 0.81056, 0.57579, 0.87239, 0.47486, 0.91567, 0.40642, 0.94246, 0.87517, 0.6189, 0.90083, 0.52204, 0.90768, 0.42517, 0.90083, 0.33655, 0.86491, 0.25206, 0.81872, 0.19023, 0.7366, 0.12634, 0.65277, 0.11191, 0.57066, 0.10367, 0.47999, 0.09337, 0.36023, 0.09337, 0.2918, 0.11398, 0.09165, 0.37983, 0.23193, 0.32213, 0.35681, 0.27679, 0.47143, 0.27267, 0.59119, 0.29534, 0.68357, 0.33243, 0.752, 0.41487, 0.75029, 0.53028, 0.69725, 0.6189, 0.60658, 0.71988, 0.53131, 0.80026, 0.45604, 0.86415, 0.34142, 0.91567, 0.40471, 0.89506], "triangles": [48, 43, 49, 78, 48, 49, 1, 78, 2, 48, 46, 45, 45, 44, 43, 43, 48, 45, 47, 46, 48, 47, 48, 78, 0, 47, 78, 0, 78, 1, 76, 39, 38, 40, 39, 76, 77, 40, 76, 41, 40, 77, 51, 76, 52, 77, 76, 51, 50, 41, 77, 50, 77, 51, 49, 42, 41, 49, 41, 50, 43, 42, 49, 80, 50, 51, 79, 49, 50, 51, 81, 80, 80, 3, 50, 4, 80, 5, 3, 79, 50, 80, 4, 3, 2, 78, 49, 79, 2, 49, 2, 79, 3, 75, 38, 37, 76, 38, 75, 74, 37, 36, 75, 37, 74, 73, 36, 35, 74, 36, 73, 72, 35, 34, 73, 35, 72, 52, 76, 75, 53, 75, 74, 52, 75, 53, 71, 34, 33, 72, 34, 71, 70, 71, 33, 54, 74, 73, 54, 73, 72, 53, 74, 54, 55, 54, 72, 55, 72, 71, 81, 52, 53, 82, 53, 54, 81, 53, 82, 56, 55, 71, 83, 54, 55, 83, 55, 56, 82, 54, 83, 81, 51, 52, 5, 80, 81, 6, 81, 82, 5, 81, 6, 7, 82, 83, 6, 82, 7, 8, 7, 83, 32, 70, 33, 56, 71, 70, 69, 70, 32, 69, 32, 31, 57, 56, 70, 57, 70, 69, 69, 31, 30, 84, 83, 56, 84, 56, 57, 68, 69, 30, 57, 69, 68, 8, 83, 84, 58, 57, 68, 84, 57, 58, 9, 8, 84, 29, 68, 30, 67, 58, 68, 67, 68, 29, 85, 9, 84, 85, 84, 58, 10, 9, 85, 59, 85, 58, 59, 58, 67, 28, 67, 29, 66, 59, 67, 66, 67, 28, 86, 10, 85, 11, 10, 86, 27, 66, 28, 60, 85, 59, 60, 59, 66, 86, 85, 60, 26, 60, 66, 26, 66, 27, 61, 86, 60, 26, 25, 60, 87, 12, 11, 25, 61, 60, 88, 13, 12, 88, 12, 87, 87, 86, 61, 86, 87, 11, 62, 87, 61, 88, 87, 62, 24, 62, 61, 24, 61, 25, 89, 14, 13, 89, 13, 88, 63, 88, 62, 91, 15, 14, 91, 14, 89, 23, 62, 24, 63, 62, 23, 90, 15, 91, 64, 89, 88, 64, 88, 63, 91, 89, 64, 16, 15, 90, 65, 91, 64, 90, 91, 65, 22, 64, 63, 22, 63, 23, 21, 65, 64, 21, 64, 22, 18, 17, 16, 19, 16, 90, 18, 16, 19, 20, 90, 65, 19, 90, 20, 20, 65, 21], "vertices": [1, 22, 3.87, -29.65, 1, 1, 22, 21.12, -34.96, 1, 2, 21, -5.91, -31.75, 0.776, 22, 39.97, -30.73, 0.224, 2, 20, -30.24, -37.09, 0.00394, 21, 10.76, -27.6, 0.99606, 2, 20, -11.25, -31.26, 0.14315, 21, 30.56, -27.07, 0.85685, 3, 18, 5.92, -77.46, 0.00041, 20, 6.38, -28.29, 0.6109, 21, 48.27, -28.93, 0.38868, 3, 18, 3.95, -56.19, 0.04608, 20, 28.19, -28.83, 0.92739, 21, 69, -35.27, 0.02653, 2, 18, 5.27, -38.87, 0.26056, 20, 45.6, -32.11, 0.73944, 2, 18, 8.7, -24.62, 0.65645, 20, 59.66, -37.07, 0.34355, 3, 17, -23.23, -29.37, 0.00523, 18, 16.46, -18, 0.91265, 20, 65.49, -45.35, 0.08212, 3, 17, -10.11, -28.36, 0.12099, 18, 29.14, -22.16, 0.87443, 20, 59.81, -57.14, 0.00458, 2, 17, 5.59, -28.68, 0.53407, 18, 43.69, -28.55, 0.46593, 2, 17, 20.64, -31, 0.87608, 18, 56.83, -36.54, 0.12392, 2, 17, 39.87, -35.26, 0.99333, 18, 73.1, -47.92, 0.00667, 1, 17, 56.56, -41.51, 1, 1, 17, 77.16, -48.41, 1, 1, 17, 94.37, -53.89, 1, 1, 17, 109.31, -58.9, 1, 1, 17, 105.25, -47.93, 1, 1, 17, 99.08, -40.25, 1, 1, 17, 92.22, -31.7, 1, 1, 17, 76.89, -16.17, 1, 1, 17, 59.53, -2.98, 1, 1, 17, 41.84, 7.33, 1, 1, 17, 26.48, 12.39, 1, 2, 17, 9.14, 16.64, 0.89764, 18, 65.24, 11.76, 0.10236, 2, 17, -9.68, 20.85, 0.23209, 18, 49.34, 22.94, 0.76791, 2, 17, -27.86, 21.45, 0.01312, 18, 32.57, 30.55, 0.98688, 1, 18, 16.77, 34.79, 1, 1, 18, 3.06, 39.29, 1, 2, 18, -14.11, 37.35, 0.98381, 20, 125.13, -22.14, 0.01619, 2, 18, -31.1, 27.08, 0.88178, 20, 116.67, -4.51, 0.11822, 2, 18, -43.02, 15.4, 0.70545, 20, 106.19, 8.38, 0.29455, 2, 18, -53.94, -2.07, 0.4178, 20, 89.72, 20.96, 0.5822, 2, 18, -58.17, -22.28, 0.14261, 20, 69.7, 27.38, 0.85739, 2, 18, -59.7, -39.96, 0.01709, 20, 51.95, 30.9, 0.98291, 2, 20, 30.14, 31.44, 0.99878, 21, 87.59, 22.2, 0.00122, 2, 20, 16.91, 31.22, 0.9552, 21, 74.85, 25.52, 0.0448, 2, 20, -2.84, 28.23, 0.57813, 21, 55.11, 27.92, 0.42187, 2, 20, -24.64, 24.93, 0.10766, 21, 33.31, 30.57, 0.89234, 2, 20, -32.41, 14.88, 0.01771, 21, 23.09, 22.98, 0.98229, 1, 21, 5.24, 18.97, 1, 2, 21, -10.81, 17.39, 0.784, 22, 41.61, 18.62, 0.216, 1, 22, 30.04, 17.66, 1, 1, 22, 15.61, 17.42, 1, 1, 22, 11.78, 11.76, 1, 1, 22, 8.52, -0.68, 1, 1, 22, 5.25, -13.11, 1, 1, 22, 24.54, -5.08, 1, 2, 21, -6.87, -7.62, 0.792, 22, 42.21, -6.68, 0.208, 2, 20, -29.15, -15.8, 0.00183, 21, 17.7, -7.41, 0.99817, 2, 20, -11.43, -8.13, 0.03447, 21, 36.8, -4.76, 0.96553, 2, 20, 6.63, -2.54, 0.97204, 21, 55.64, -4.21, 0.02796, 1, 20, 26.95, 0.18, 1, 2, 18, -26.74, -41.7, 0.00212, 20, 46.4, -0.8, 0.99788, 2, 18, -26.24, -26.22, 0.19481, 20, 62.05, -3.07, 0.80519, 2, 18, -17.91, -11.91, 0.58817, 20, 75.6, -12.78, 0.41183, 2, 18, -6.23, -1.47, 0.92164, 20, 84.86, -25.29, 0.07836, 1, 18, 8.22, 3.14, 1, 1, 18, 22.18, 2.87, 1, 1, 18, 42.2, 2.4, 1, 1, 17, 11.72, 1.26, 1, 2, 17, 28.32, -4.13, 0.99755, 18, 74.82, -14.8, 0.00245, 1, 17, 45.53, -8.59, 1, 1, 17, 64.44, -20.26, 1, 1, 17, 76.91, -28.44, 1, 2, 17, -27.47, 12.71, 0.0082, 18, 29.42, 22.36, 0.9918, 1, 18, 11.59, 23.06, 1, 2, 18, -5.18, 20.02, 0.98908, 20, 106.53, -28.79, 0.01092, 2, 18, -19.78, 14.63, 0.89396, 20, 102.74, -14.02, 0.10604, 2, 18, -32.06, 3.66, 0.66697, 20, 93.02, -0.88, 0.33303, 2, 18, -39.93, -8.32, 0.41356, 20, 81.77, 8.12, 0.58644, 2, 18, -46.13, -27.53, 0.10261, 20, 63, 16.33, 0.89739, 2, 18, -43.86, -44.84, 0.00263, 20, 45.19, 16.13, 0.99737, 1, 20, 27.9, 14.94, 1, 2, 20, 8.78, 13.83, 0.90314, 21, 62.24, 10.96, 0.09686, 2, 20, -16.12, 10.06, 0.0854, 21, 37.35, 13.99, 0.9146, 2, 20, -29.79, 4.43, 0.00176, 21, 22.7, 12.22, 0.99824, 1, 22, 22.04, -24.46, 1, 2, 20, -36.69, -32.63, 0.00044, 21, 5.82, -21.59, 0.99956, 2, 20, -11.94, -21.04, 0.13144, 21, 32.73, -17.05, 0.86856, 3, 18, -6.48, -73.55, 0.00106, 20, 11.78, -16.74, 0.81597, 21, 56.64, -19.25, 0.18297, 3, 18, -9.37, -48.74, 0.06458, 20, 37.28, -16.8, 0.93476, 21, 81.04, -26.12, 0.00066, 2, 18, -8.28, -28.7, 0.35614, 20, 57.47, -20.16, 0.64386, 2, 18, 1.82, -11.37, 0.85066, 20, 73.89, -31.94, 0.14934, 3, 17, -23.28, -16.86, 0.00209, 18, 21.44, -6.47, 0.98927, 20, 76.61, -51.5, 0.00864, 2, 17, -4.45, -15.98, 0.20112, 18, 39.41, -12.97, 0.79888, 2, 17, 20.93, -19.85, 0.8947, 18, 61.59, -26.39, 0.1053, 2, 17, 41.54, -23.44, 0.99542, 18, 79.42, -37.69, 0.00458, 1, 17, 59.91, -28.77, 1, 1, 17, 81.71, -41.77, 1, 1, 17, 70.73, -33.76, 1], "hull": 48, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 0, 94, 36, 38, 38, 40], "width": 206, "height": 171}}, "st3": {"st3": {"type": "mesh", "uvs": [0.55685, 0.1435, 0.57783, 0.07913, 0.60509, 0.0064, 0.53378, 0, 0.46177, 0, 0.39536, 0.01142, 0.33104, 0.02981, 0.2828, 0.05154, 0.21848, 0.08582, 0.17304, 0.11842, 0.13039, 0.14852, 0.0773, 0.20817, 0.03704, 0.25873, 0.00885, 0.32614, 0, 0.4116, 0, 0.5079, 0.04106, 0.57651, 0.09744, 0.64271, 0.14978, 0.68003, 0.22327, 0.71614, 0.30078, 0.7366, 0.38735, 0.73178, 0.45681, 0.71734, 0.52022, 0.69327, 0.57861, 0.66077, 0.63196, 0.62225, 0.70243, 0.60178, 0.75588, 0.59987, 0.81793, 0.62011, 0.85084, 0.66958, 0.86307, 0.72692, 0.85836, 0.80225, 0.82357, 0.86634, 0.78596, 0.90906, 0.73613, 0.93605, 0.67595, 0.94841, 0.60261, 0.95853, 0.54714, 0.95291, 0.48132, 0.93717, 0.42772, 0.93155, 0.46627, 0.97427, 0.52927, 1, 0.58851, 1, 0.65997, 1, 0.72485, 1, 0.80477, 0.98102, 0.92042, 0.93717, 0.96179, 0.90344, 0.98718, 0.8371, 0.99658, 0.77751, 1, 0.71568, 1, 0.64709, 1, 0.57626, 1, 0.52004, 0.95051, 0.48069, 0.89033, 0.45371, 0.8499, 0.44134, 0.79443, 0.44583, 0.73801, 0.44583, 0.68034, 0.45379, 0.61578, 0.4627, 0.5603, 0.47957, 0.48696, 0.51667, 0.4296, 0.53128, 0.38259, 0.5133, 0.34874, 0.48406, 0.28292, 0.45595, 0.24719, 0.4121, 0.26506, 0.36151, 0.30643, 0.32328, 0.42114, 0.26369, 0.47568, 0.24458, 0.51517, 0.22996, 0.48067, 0.07272, 0.41182, 0.07542, 0.34071, 0.10646, 0.27525, 0.1564, 0.19624, 0.20363, 0.14432, 0.24817, 0.06757, 0.37774, 0.10482, 0.29271, 0.05854, 0.46411, 0.09466, 0.52755, 0.16125, 0.57748, 0.21204, 0.62202, 0.26284, 0.65441, 0.31588, 0.66656, 0.38586, 0.65036, 0.42988, 0.63147, 0.48631, 0.60313, 0.54613, 0.58153, 0.5879, 0.55994, 0.62853, 0.54509, 0.69512, 0.53295, 0.73914, 0.5289, 0.80009, 0.53699, 0.84749, 0.55994, 0.89038, 0.59098, 0.9107, 0.65846, 0.91634, 0.73944, 0.91183, 0.79613, 0.89603, 0.84066, 0.86104, 0.88925, 0.80686, 0.92299, 0.76848, 0.95133, 0.72898, 0.97158, 0.6748, 0.97428, 0.64659, 0.97428, 0.5981, 0.97927, 0.53711, 0.97599, 0.47502, 0.95562, 0.87651, 0.90255, 0.91841, 0.86402, 0.95064, 0.79369, 0.96111, 0.73396, 0.96594, 0.66845, 0.94983, 0.6039, 0.91841, 0.54899, 0.8741, 0.51334, 0.82979, 0.49118, 0.76775, 0.48348, 0.72827, 0.47866, 0.68315, 0.46903, 0.67842, 0.58367, 0.60994, 0.59908, 0.52051, 0.63762, 0.45444, 0.66459, 0.38757, 0.6935, 0.33118, 0.71276, 0.44478, 0.55188, 0.39966, 0.57404, 0.32393, 0.57885, 0.25303, 0.55477, 0.20388, 0.49407, 0.19905, 0.41604, 0.23127, 0.36305, 0.48506, 0.15675, 0.37629, 0.18951, 0.31668, 0.22997, 0.249, 0.27621], "triangles": [40, 39, 110, 110, 39, 38, 109, 38, 37, 110, 38, 109, 108, 37, 36, 109, 37, 108, 41, 110, 109, 40, 110, 41, 42, 109, 108, 41, 109, 42, 42, 108, 43, 107, 36, 35, 106, 107, 35, 106, 35, 105, 108, 36, 107, 43, 107, 106, 108, 107, 43, 43, 106, 44, 103, 33, 32, 104, 34, 33, 104, 33, 103, 105, 35, 34, 105, 34, 104, 45, 104, 103, 111, 45, 103, 44, 106, 105, 44, 105, 104, 44, 104, 45, 102, 32, 31, 101, 102, 31, 111, 102, 101, 112, 111, 101, 47, 112, 48, 103, 32, 102, 46, 112, 47, 111, 112, 46, 111, 103, 102, 45, 111, 46, 114, 115, 50, 99, 30, 98, 115, 114, 99, 49, 114, 50, 113, 99, 114, 113, 114, 49, 100, 30, 99, 100, 99, 113, 31, 30, 100, 48, 113, 49, 101, 31, 100, 112, 100, 113, 112, 113, 48, 101, 100, 112, 117, 54, 53, 117, 53, 52, 97, 118, 117, 116, 117, 52, 97, 117, 116, 116, 52, 51, 98, 97, 116, 115, 116, 51, 98, 116, 115, 29, 28, 97, 29, 97, 98, 115, 51, 50, 30, 29, 98, 115, 99, 98, 119, 57, 56, 120, 57, 119, 118, 56, 55, 118, 55, 54, 119, 56, 118, 95, 120, 119, 117, 118, 54, 96, 119, 118, 95, 119, 96, 96, 118, 97, 28, 95, 96, 28, 96, 97, 27, 95, 28, 121, 122, 59, 58, 121, 59, 120, 58, 57, 121, 58, 120, 94, 121, 120, 93, 122, 121, 93, 121, 94, 94, 120, 95, 122, 60, 59, 92, 122, 93, 27, 94, 95, 26, 93, 94, 26, 94, 27, 123, 93, 26, 122, 92, 60, 91, 61, 60, 92, 91, 60, 91, 90, 61, 123, 92, 93, 124, 91, 92, 124, 92, 123, 25, 124, 123, 25, 123, 26, 124, 90, 91, 24, 124, 25, 129, 63, 62, 90, 62, 61, 89, 129, 62, 89, 62, 90, 125, 89, 90, 124, 125, 90, 24, 125, 124, 126, 89, 125, 23, 126, 125, 23, 125, 24, 130, 64, 63, 130, 63, 129, 131, 65, 64, 131, 64, 130, 88, 130, 129, 88, 129, 89, 87, 131, 130, 87, 130, 88, 126, 88, 89, 127, 87, 88, 127, 88, 126, 86, 87, 127, 22, 126, 23, 127, 126, 22, 21, 128, 127, 21, 127, 22, 131, 66, 65, 85, 132, 131, 86, 85, 131, 86, 131, 87, 128, 86, 127, 20, 85, 86, 20, 86, 128, 19, 85, 20, 20, 128, 21, 132, 133, 66, 83, 133, 132, 132, 66, 131, 84, 83, 132, 84, 132, 85, 18, 17, 83, 18, 83, 84, 19, 84, 85, 18, 84, 19, 133, 67, 66, 82, 81, 134, 133, 82, 134, 16, 15, 81, 16, 81, 82, 83, 82, 133, 17, 82, 83, 16, 82, 17, 80, 13, 12, 79, 13, 80, 14, 13, 79, 79, 80, 134, 81, 14, 79, 81, 79, 134, 133, 134, 67, 15, 14, 81, 78, 10, 77, 11, 10, 78, 80, 11, 78, 12, 11, 80, 139, 78, 77, 135, 139, 68, 135, 78, 139, 80, 78, 135, 67, 135, 68, 134, 80, 135, 134, 135, 67, 76, 8, 7, 77, 9, 8, 77, 8, 76, 10, 9, 77, 138, 76, 137, 139, 77, 76, 139, 76, 138, 69, 139, 138, 70, 69, 138, 68, 139, 69, 74, 75, 6, 7, 6, 75, 76, 7, 75, 137, 75, 74, 76, 75, 137, 70, 137, 71, 70, 138, 137, 73, 4, 3, 74, 5, 4, 74, 4, 73, 74, 6, 5, 0, 73, 1, 136, 73, 0, 136, 137, 74, 136, 74, 73, 72, 136, 0, 71, 137, 136, 71, 136, 72, 1, 3, 2, 73, 3, 1], "vertices": [5, 22, 13.61, -31.91, 0.48797, 23, 60.79, -32.98, 0.48789, 24, 105.35, -40.76, 0.02414, 32, -55.74, -149.21, 0, 41, -8.93, 311.15, 0, 4, 22, 29.1, -10.73, 0.96995, 23, 78.26, -13.23, 0.03005, 32, -73.94, -168.21, 0, 41, -2.11, 336.39, 0, 2, 22, 48.18, 12.8, 0.99992, 23, 99.56, 8.53, 8e-05, 2, 22, 17.16, 23.22, 0.88526, 23, 69.34, 21.62, 0.11474, 3, 22, -14.78, 31.37, 0.17054, 23, 37.99, 32.54, 0.81047, 24, 91.95, 27.18, 0.01899, 4, 22, -45.34, 34.72, 5e-05, 23, 7.6, 38.54, 0.65773, 24, 62.82, 37.17, 0.34209, 25, 117.48, 28.18, 0.00013, 4, 23, -22.79, 41.74, 0.14567, 24, 33.32, 44.39, 0.78974, 25, 89.29, 39.38, 0.0646, 41, -116.9, 342.45, 0, 4, 23, -46.61, 41.32, 0.01563, 24, 9.78, 47.14, 0.64958, 25, 66.38, 45.33, 0.33381, 26, 124.02, 32.56, 0.00098, 3, 24, -22.54, 49.02, 0.18027, 25, 34.65, 51.64, 0.73458, 26, 93.89, 44.49, 0.08515, 4, 24, -46.86, 47.54, 0.02384, 25, 10.36, 53.51, 0.6587, 26, 70.27, 50.72, 0.31631, 27, 136.59, 18.32, 0.00116, 3, 25, -12.32, 55.42, 0.38528, 26, 48.24, 56.7, 0.58585, 27, 118.68, 32.56, 0.02886, 3, 25, -45.51, 50.96, 0.08492, 26, 14.66, 58.32, 0.66719, 27, 88.21, 47.43, 0.2479, 3, 25, -71.86, 45.95, 0.00755, 26, -12.29, 58.16, 0.4277, 27, 63.15, 58.02, 0.56475, 3, 26, -40.05, 49.65, 0.15926, 27, 33.81, 61.31, 0.83408, 28, 120, 15.96, 0.00666, 4, 26, -66.63, 30.1, 0.0113, 27, 0.9, 54.06, 0.83112, 28, 92.66, 35.02, 0.15758, 41, -251.64, 182.32, 0, 3, 27, -34.58, 41.64, 0.41887, 28, 59.61, 52.54, 0.58113, 41, -247.5, 146.15, 0, 4, 27, -53.3, 15.35, 0.10024, 28, 26.81, 48.75, 0.86839, 29, 97.96, 28.73, 0.03137, 41, -225.81, 122.45, 0, 4, 28, -8.61, 38.46, 0.59008, 29, 62.68, 37.32, 0.40992, 32, 211.74, -52.1, 0, 41, -197.23, 100.43, 0, 4, 28, -33.21, 24.51, 0.10966, 29, 34.65, 37.43, 0.89034, 32, 194.82, -30.16, 0, 41, -171.73, 89.06, 0, 3, 29, -1.52, 32.29, 0.63646, 30, 62.93, 29.81, 0.36354, 32, 168.73, -5.05, 0, 4, 29, -36.22, 21.13, 0.022, 30, 26.09, 34.34, 0.93888, 31, 87.08, 21.87, 0.0391, 32, 138.68, 15.25, 1e-05, 5, 29, -69.56, -0.34, 1e-05, 30, -14.07, 28.98, 0.33545, 31, 48.7, 31.99, 0.65324, 32, 101.12, 28.23, 0.0113, 41, -61.06, 81.61, 0, 5, 29, -94.24, -21.01, 0, 30, -45.92, 20.7, 0.01423, 31, 16.68, 36.3, 0.73796, 32, 69.47, 34.94, 0.24781, 41, -29.97, 90.55, 0, 5, 29, -114.65, -43.43, 0, 31, -13.83, 36.31, 0.19654, 32, 39, 37.25, 0.76516, 33, 86.39, 39.67, 0.03831, 41, -2.05, 102.79, 0, 4, 31, -43.11, 32.6, 0.00382, 32, 9.47, 35.75, 0.63238, 33, 57.07, 36.28, 0.3638, 41, 23.21, 117.95, 0, 3, 32, -18.79, 31.29, 0.10449, 33, 29.21, 30.02, 0.83776, 34, 67.31, 38.07, 0.05776, 3, 33, -3.92, 32.66, 0.29234, 34, 34.69, 29.35, 0.6718, 35, 67.13, 41.38, 0.03586, 3, 33, -27.42, 39.55, 0.01013, 34, 9.79, 27.88, 0.61992, 35, 45.74, 29.16, 0.36995, 4, 34, -19.4, 34.66, 0.04482, 35, 16.74, 22.42, 0.79492, 36, 50.13, 27.73, 0.15979, 37, 67.66, 52.96, 0.00047, 3, 35, -5.85, 31.7, 0.10589, 36, 25.93, 22.38, 0.79263, 37, 53.56, 33.35, 0.10149, 3, 35, -21.48, 48.07, 0.00053, 36, 3.14, 26.74, 0.28688, 37, 33.74, 21.84, 0.71258, 3, 37, 4.91, 15.7, 0.59719, 38, 46.94, 16.13, 0.40281, 41, 157, 78.93, 0, 5, 29, -268.64, -56.85, 0, 38, 17.99, 10.59, 0.98403, 39, 63.06, 16.14, 0.01597, 40, 101.15, 39.07, 0, 41, 143.87, 53.1, 0, 5, 29, -262.11, -34.29, 0, 38, -5.93, 11.57, 0.11967, 39, 40.23, 8.76, 0.88029, 40, 83.25, 23.51, 4e-05, 41, 128.53, 35.15, 0, 5, 29, -247.65, -14.06, 0, 38, -29.4, 20.67, 0.00086, 39, 14.86, 9.12, 0.99132, 40, 59.92, 14.09, 0.00782, 41, 106.94, 22.5, 0, 5, 29, -226.24, 3.8, 0, 38, -52.15, 36.96, 1e-05, 39, -12.43, 16.47, 0.01803, 40, 32.01, 10.35, 0.98195, 41, 80, 14.81, 0, 3, 29, -199.17, 23.96, 0, 40, -1.94, 7.67, 0.30195, 41, 46.95, 7.31, 0.69805, 4, 29, -176.11, 34.88, 0, 40, -27.44, 10.65, 0.00048, 41, 21.39, 6.62, 0.91152, 42, 71.71, 5.93, 0.088, 3, 29, -146.96, 44.89, 0, 41, -9.34, 9.21, 0.784, 42, 40.92, 9.5, 0.216, 1, 42, 16.07, 9.7, 1, 1, 42, 35.13, -5.02, 1, 4, 29, -178.24, 54.32, 0, 40, -36.3, -6.86, 0.00039, 41, 15.25, -11.97, 0.90361, 42, 64.93, -12.45, 0.096, 2, 40, -8.99, -7.78, 0.05525, 41, 42.29, -8.98, 0.94475, 1, 40, 23.96, -8.89, 1, 2, 39, -0.43, -10.59, 0.67254, 40, 53.87, -9.89, 0.32746, 2, 38, -19.85, -13.45, 0.0779, 39, 36.24, -19.47, 0.9221, 2, 37, -37.26, -25.8, 0.10751, 38, 29.43, -39.49, 0.89249, 2, 37, -19.1, -40.05, 0.36219, 38, 52.06, -44.06, 0.63781, 3, 29, -327.72, -103.97, 0, 37, 9.13, -43.85, 0.76659, 38, 78.61, -34.87, 0.23341, 4, 29, -319.78, -125.53, 0, 36, -41.84, -19.45, 0.07299, 37, 32.69, -41.46, 0.8944, 38, 98.16, -22.26, 0.0326, 3, 29, -309.03, -146.44, 0, 36, -20.62, -30.93, 0.40016, 37, 56.3, -36.24, 0.59984, 5, 29, -295.61, -168.76, 0, 35, -62.34, -8.09, 0.00843, 36, 3.68, -42.11, 0.83171, 37, 81.96, -28.8, 0.15986, 41, 215, 144.37, 0, 5, 29, -281.76, -191.81, 0, 35, -49.18, -31.58, 0.12164, 36, 28.78, -53.66, 0.86813, 37, 108.47, -21.12, 0.01022, 41, 211.96, 170.97, 0, 4, 29, -270.76, -210.11, 0, 35, -38.73, -50.23, 0.25017, 36, 48.7, -62.83, 0.74983, 41, 209.55, 192.09, 0, 4, 29, -243.46, -211.54, 0, 35, -11.28, -52.56, 0.48143, 36, 72.66, -49.07, 0.51857, 41, 185.27, 204.37, 0, 5, 29, -214.35, -206.49, 0, 34, -51.16, -29.22, 0.01354, 35, 18.22, -48.47, 0.81892, 36, 94.42, -28.93, 0.16754, 41, 156.63, 211.47, 0, 5, 29, -195.91, -201.22, 0, 34, -32.15, -33.32, 0.09359, 35, 36.96, -43.81, 0.86762, 36, 106.99, -14.46, 0.03879, 41, 137.65, 214.07, 0, 4, 29, -174.82, -187, 0, 34, -6.34, -30.85, 0.47018, 35, 58.69, -30.29, 0.52982, 41, 112.51, 209.58, 0, 3, 29, -152.48, -174.03, 0, 34, 19.97, -30.06, 0.9576, 35, 81.64, -18.06, 0.0424, 3, 29, -131.2, -158.18, 0, 33, -12.1, -23.59, 0.26231, 34, 46.78, -26.25, 0.73769, 4, 29, -107.37, -140.44, 0, 32, -34.87, -27.37, 0.03849, 33, 17.07, -29.54, 0.881, 34, 76.78, -21.98, 0.08051, 3, 29, -88.7, -122.19, 0, 32, -8.83, -30.85, 0.37943, 33, 43.25, -31.35, 0.62057, 4, 29, -66.92, -93.26, 0.0001, 31, -19.81, -31.86, 0.14101, 32, 27.74, -30.28, 0.84327, 33, 79.64, -28.43, 0.01561, 8, 26, 37.75, -141.08, 0.00157, 27, 25.46, -143.91, 0.0017, 28, -45.21, -113.42, 0.00092, 29, -47.06, -75.31, 0.01615, 30, -26.66, -48.23, 0.02573, 31, 6.95, -34.45, 0.65818, 32, 54.27, -34.87, 0.29548, 33, 106.36, -31.32, 0.00028, 8, 26, 27.68, -121.07, 0.01059, 27, 24.58, -121.62, 0.01254, 28, -28.44, -98.07, 0.01273, 29, -24.93, -70.36, 0.09584, 30, -4.11, -53.07, 0.172, 31, 25.4, -47.37, 0.65784, 32, 71.69, -49.14, 0.03802, 33, 124.67, -44.44, 0.00043, 8, 26, 24.96, -102.38, 0.02772, 27, 29.94, -103.47, 0.0337, 28, -10.78, -89.97, 0.04044, 29, -5.8, -72.09, 0.20268, 30, 12.69, -62.69, 0.2484, 31, 36.79, -62.54, 0.4439, 32, 81.9, -65.12, 0.0028, 33, 135.89, -59.73, 0.00036, 8, 25, -23.75, -79.24, 0.00133, 26, 11.69, -73.59, 0.11268, 27, 29.78, -71.88, 0.12989, 28, 13.69, -69.01, 0.14642, 29, 25.76, -66.11, 0.30764, 30, 44.35, -70.58, 0.14891, 31, 62.28, -81.68, 0.15296, 33, 161.16, -79.15, 0.00017, 9, 24, -68.59, -66.44, 0.00044, 25, -27.35, -56.37, 0.03006, 26, 12.42, -50.46, 0.33918, 27, 40.22, -51.04, 0.254, 28, 36.79, -62.83, 0.1548, 29, 48.49, -72.16, 0.14219, 30, 62.56, -85.62, 0.03902, 31, 72.79, -102.37, 0.04027, 33, 171.44, -99.95, 6e-05, 10, 24, -52.34, -53.18, 0.02042, 25, -9.38, -45.47, 0.23647, 26, 32.2, -43, 0.54921, 27, 61.72, -52.11, 0.10826, 28, 50.13, -79.11, 0.04074, 29, 51.32, -92.73, 0.03131, 30, 56.03, -105.4, 0.00602, 31, 59.11, -118.15, 0.00755, 32, 99.88, -122.25, 0, 33, 157.58, -115.58, 1e-05, 11, 23, -71.54, -59.03, 0.00079, 24, -28.69, -48.96, 0.14298, 25, 14.64, -44.54, 0.59285, 26, 56.08, -46.43, 0.24269, 27, 82.42, -64.76, 0.01111, 28, 53.93, -102.46, 0.00454, 29, 42.41, -114.69, 0.00396, 30, 38.1, -121.48, 0.00032, 31, 36.6, -126.25, 0.00074, 32, 76.77, -128.64, 0, 33, 134.97, -123.44, 0, 5, 23, -13.88, -55.2, 0.26985, 24, 28.68, -52.84, 0.59781, 25, 70.89, -56.26, 0.13234, 32, 19.35, -130.08, 0, 41, -65.72, 259.15, 0, 6, 22, -32.1, -59.7, 0.01516, 23, 12.34, -56.66, 0.60362, 24, 54.32, -57.78, 0.36125, 25, 95.58, -64.67, 0.01998, 32, -6.62, -127.54, 0, 41, -41.65, 269.08, 0, 6, 22, -13.18, -58.83, 0.06389, 23, 31.43, -57.45, 0.71567, 24, 73.03, -61.09, 0.21806, 25, 113.63, -70.52, 0.00238, 32, -25.54, -125.97, 0, 41, -24.24, 276.57, 0, 1, 23, 36.8, 3.78, 1, 2, 23, 6.47, 13.26, 0.76947, 24, 58.21, 12.27, 0.23053, 3, 23, -28.51, 12.98, 0.01263, 24, 23.69, 16.65, 0.96143, 25, 75.83, 13.24, 0.02594, 2, 24, -11.87, 13.52, 0.1348, 25, 40.17, 15.02, 0.8652, 2, 25, 0.05, 21.18, 0.56289, 26, 54.03, 20.81, 0.43711, 3, 25, -29.33, 21.08, 0.07711, 26, 25.02, 26.02, 0.85366, 27, 84.19, 13.8, 0.06924, 3, 26, -35.57, 17, 0.04279, 27, 24.18, 29.71, 0.94073, 28, 89.06, 2.08, 0.01649, 3, 25, -54.05, 17.72, 0.00705, 26, -0.01, 27.19, 0.56414, 27, 61.46, 24.84, 0.4288, 3, 27, -9.09, 22.41, 0.58838, 28, 61.45, 21.38, 0.41162, 41, -222.66, 165.55, 0, 4, 27, -26.69, -1.11, 0.04224, 28, 31.54, 18.61, 0.95336, 29, 86.31, 0.48, 0.00441, 41, -203.44, 143.55, 0, 4, 28, -0.6, 1.31, 0.4901, 29, 50.17, 1.42, 0.5099, 32, 175.17, -64.15, 0, 41, -170.9, 128.15, 0, 2, 29, 21.34, 4.24, 1, 32, 159.96, -39.94, 0, 3, 29, -5.11, 3.1, 0.26114, 30, 46.68, 4.95, 0.73886, 32, 143, -19.98, 0, 1, 30, 21.53, 7.35, 1, 3, 31, 39.89, 2.45, 0.99948, 32, 90.04, -0.55, 0.00052, 41, -65.23, 112.12, 0, 3, 31, 18.46, 1.68, 0.99788, 32, 68.57, 0.28, 0.00212, 41, -45.95, 121.44, 0, 1, 32, 40.46, -0.09, 1, 2, 32, 11.89, 2.48, 1, 41, 4.98, 146.07, 0, 3, 32, -9, 1.99, 0.00433, 33, 40.9, 1.41, 0.99567, 41, 23.12, 156.29, 0, 3, 32, -28.44, 3.67, 0.00065, 33, 21.43, 1.84, 0.99479, 34, 69.84, 8.97, 0.00456, 3, 33, -9.02, 6.92, 0.02283, 34, 38.93, 3.45, 0.97525, 35, 82.9, 20.13, 0.00192, 2, 33, -28.67, 11.7, 0.0006, 34, 18.45, 1.3, 0.9994, 3, 29, -194.9, -158.63, 0, 35, 39.44, -1.28, 1, 41, 119.01, 175.63, 0, 3, 29, -218.17, -162.06, 0, 35, 15.9, -3.94, 1, 41, 141.64, 169.4, 0, 4, 29, -241.22, -161.82, 0, 35, -7.32, -2.94, 0.21681, 36, 45.77, -6.56, 0.78319, 41, 162.55, 159.91, 0, 3, 29, -262.47, -144.53, 0, 36, 17.75, -3.84, 1, 41, 174.72, 135.59, 0, 2, 29, -280.55, -119.47, 0, 37, 36.19, -2.59, 1, 3, 29, -289.86, -99.99, 0, 37, 14.37, -6.78, 0.98368, 38, 65.79, 0.37, 0.01632, 3, 29, -292.31, -81.86, 0, 37, -4.41, -4.77, 0.33701, 38, 48.37, -6.2, 0.66299, 1, 38, 23.66, -7.61, 1, 4, 29, -273.11, -34.57, 0, 38, -3.08, 1.12, 0.32476, 39, 46.69, -0.02, 0.67524, 40, 92.7, 17.93, 0, 1, 39, 25.91, -2.33, 1, 4, 38, -41.55, 13.65, 1e-05, 39, 6, -1.66, 0.84555, 40, 56.15, 0.78, 0.15445, 41, 105.2, 8.79, 0, 4, 29, -230.85, 12.49, 0, 38, -59.69, 30.54, 0, 40, 31.14, 0.6, 1, 41, 80.59, 5.04, 0, 3, 29, -219.67, 18.97, 0, 38, -68.75, 39.7, 0, 40, 18.13, 1.04, 1, 2, 40, -4.29, -0.1, 0.20867, 41, 45.78, -0.71, 0.79133, 4, 29, -176.65, 44.7, 0, 40, -32.37, 2.09, 0.00019, 41, 17.8, -2.55, 0.92781, 42, 67.8, -3.12, 0.072, 3, 29, -148.07, 52.35, 0, 41, -11.42, 1.96, 0.608, 42, 38.59, 2.33, 0.392, 2, 37, -30.19, -3.03, 0.00984, 38, 24.94, -16.13, 0.99016, 3, 29, -305.75, -79.4, 0, 37, -10.15, -16.99, 0.33884, 38, 49.07, -19.61, 0.66116, 3, 29, -304.75, -109.7, 0, 37, 20.48, -23.32, 0.93622, 38, 78.92, -11.59, 0.06378, 3, 29, -297.21, -131.55, 0, 36, -19.22, -12.09, 0.21147, 37, 44.24, -21.38, 0.78853, 3, 29, -286.31, -153.98, 0, 36, 3.02, -24.74, 0.76415, 37, 69.41, -16.37, 0.23585, 5, 29, -267.29, -171.28, 0, 35, -33.91, -11.54, 0.06246, 36, 29.15, -28.7, 0.92126, 37, 91.4, -2.39, 0.01628, 41, 190.24, 158.06, 0, 4, 29, -244.11, -181.93, 0, 35, -10.92, -22.95, 0.41393, 36, 54.97, -24.84, 0.58607, 41, 173.54, 177.1, 0, 5, 29, -219.58, -183.34, 0, 34, -44.3, -6.46, 0.00105, 35, 13.73, -25.17, 0.85184, 36, 76.58, -12.58, 0.14712, 41, 151.78, 188.25, 0, 5, 29, -197.7, -180.37, 0, 34, -23.37, -14.21, 0.11793, 35, 35.88, -22.91, 0.87495, 36, 93.41, 1.87, 0.00712, 41, 130.6, 194.33, 0, 4, 29, -171.62, -168.61, 0, 34, 5.65, -16.26, 0.78272, 35, 62.55, -12.02, 0.21728, 41, 101.95, 194.1, 0, 3, 29, -155.05, -161.1, 0, 34, 24.12, -17.53, 0.99966, 35, 79.5, -5.07, 0.00034, 3, 29, -135.3, -153.87, 0, 33, -11.5, -17.72, 0.24426, 34, 45.28, -20.54, 0.75574, 3, 33, 4.35, 22.76, 0.53601, 34, 46.1, 22.85, 0.45899, 35, 80.26, 40.57, 0.00501, 4, 32, -12.75, 19.44, 0.14795, 33, 36.01, 18.58, 0.84545, 34, 77.85, 29.63, 0.00659, 41, 34.87, 142.7, 0, 4, 31, -20.42, 16.3, 0.05794, 32, 30.87, 17.79, 0.90246, 33, 79.57, 19.74, 0.0396, 41, -4.31, 123.71, 0, 3, 31, 11.58, 16.97, 0.75651, 32, 62.88, 16.05, 0.24349, 41, -33.31, 110.24, 0, 5, 29, -62.16, -12.86, 1e-05, 30, -12.82, 14.56, 0.2523, 31, 44.15, 18.23, 0.73958, 32, 95.52, 14.85, 0.00812, 41, -62.6, 96.01, 0, 2, 30, 12.77, 24.12, 0.83979, 31, 71.03, 17.44, 0.16021, 7, 26, 36.89, -151.42, 0.00059, 27, 20.3, -153.02, 0.00059, 28, -55.69, -115.69, 0.00012, 29, -57.1, -72.1, 0.00645, 30, -34.45, -41.1, 0.00667, 31, 2.71, -24.96, 0.55768, 32, 50.77, -25.09, 0.4279, 8, 26, 16.27, -142.56, 0.00239, 27, 4.93, -136.7, 0.00267, 28, -53.13, -93.78, 0.00164, 29, -43.57, -54.51, 0.02498, 30, -14.22, -30.91, 0.06428, 31, 24.99, -23.15, 0.84219, 32, 73.17, -24.97, 0.0616, 33, 124.54, -20.22, 0.00025, 7, 26, -9.33, -119.18, 0.00903, 27, -8.95, -105.14, 0.01112, 28, -37.72, -62.9, 0.01238, 29, -14.52, -35.54, 0.1429, 30, 20.89, -25.99, 0.5375, 31, 58.64, -31.79, 0.2869, 33, 158.09, -29.22, 0.00017, 7, 26, -25.35, -89.79, 0.02353, 27, -11.41, -71.91, 0.03514, 28, -13.48, -39.19, 0.08319, 29, 18.27, -27.07, 0.60917, 30, 54.78, -32.16, 0.17756, 31, 86.81, -50.18, 0.07132, 33, 186.05, -47.92, 8e-05, 7, 26, -24.23, -57.88, 0.06445, 27, 3.1, -43.21, 0.16082, 28, 18.42, -30.76, 0.44916, 29, 49.61, -35.53, 0.2611, 30, 79.83, -52.99, 0.03477, 31, 101.23, -78.77, 0.02966, 33, 200.15, -76.67, 4e-05, 8, 25, -46.26, -44.96, 0.00616, 26, -4.09, -35.83, 0.25691, 27, 31.08, -31.09, 0.44089, 28, 46.29, -43.04, 0.18168, 29, 66.79, -59.81, 0.08098, 30, 84.85, -82.17, 0.0159, 31, 94.28, -107.54, 0.01744, 33, 192.87, -105.36, 3e-05, 10, 24, -66.4, -46.67, 0.00468, 25, -22.38, -37.09, 0.09712, 26, 20.95, -32.41, 0.6044, 27, 55.75, -37.96, 0.17878, 28, 57.21, -65.45, 0.05749, 29, 64.39, -84.47, 0.03996, 30, 71.71, -103.43, 0.00793, 31, 74.05, -122.22, 0.00963, 32, 114.49, -127.43, 0, 33, 172.47, -119.82, 2e-05, 6, 22, -19.5, -28.63, 0.05919, 23, 27.81, -26.81, 0.81314, 24, 73.7, -30.26, 0.1273, 25, 118.68, -40.09, 0.00037, 32, -23.26, -156.73, 0, 41, -41.13, 302.55, 0, 5, 23, -23.79, -21.99, 0.06966, 24, 23.51, -18.62, 0.83693, 25, 70.64, -21.66, 0.09341, 32, 27.76, -163.68, 0, 41, -89.38, 284.75, 0, 7, 23, -54.99, -27.36, 0.00365, 24, -7.98, -19.78, 0.29925, 25, 39.28, -18.49, 0.68258, 26, 85.29, -25.28, 0.01452, 32, 58.97, -159.6, 0, 33, 119.29, -155.48, 0, 41, -114.86, 266.54, 0, 10, 24, -43.79, -21.22, 0.01926, 25, 3.63, -14.99, 0.59547, 26, 50.76, -15.39, 0.37575, 27, 90.59, -34.29, 0.00492, 28, 83.02, -88.27, 0.00219, 29, 74.37, -116.8, 0.00195, 30, 66.53, -136.85, 0.0001, 31, 56.22, -151.07, 0.00035, 32, 94.44, -154.86, 0, 33, 154.31, -148.47, 0], "hull": 73, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 0, 144, 2, 146, 214, 216, 216, 218, 218, 220, 222, 224, 224, 226, 226, 228, 228, 230, 230, 232, 232, 234, 234, 236, 236, 238, 238, 240, 240, 242, 242, 244, 246, 248, 248, 250, 250, 252, 252, 254, 254, 256, 258, 260, 260, 262, 262, 264, 264, 266, 266, 268, 268, 270, 0, 272, 272, 274, 274, 276, 276, 278], "width": 452, "height": 378}}, "sp1": {"sp1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [9.9, -189.28, -224.09, -190.9, -226.16, 109.1, 7.83, 110.71], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 234, "height": 300}}, "mm1": {"mm1": {"type": "mesh", "uvs": [0.01307, 0.20911, 0.14666, 0.18511, 0.317, 0.15061, 0.52948, 0.10711, 0.73928, 0.05161, 1, 0, 0.95355, 0.19561, 0.84238, 0.39361, 0.71776, 0.65311, 0.5761, 0.86161, 0.47928, 1, 0.36811, 1, 0.27038, 1, 0.14666, 0.97261, 0.08211, 0.80161, 0, 0.58261, 0, 0.35161, 0.28261, 0.37134, 0.25911, 0.65211, 0.25576, 0.84678, 0.50413, 0.36198, 0.44036, 0.61842, 0.37211, 0.87298, 0.11927, 0.3676, 0.13045, 0.63152, 0.17632, 0.82806, 0.65516, 0.37509, 0.5858, 0.61842, 0.47504, 0.87673], "triangles": [6, 4, 5, 20, 2, 3, 23, 0, 1, 16, 0, 23, 17, 1, 2, 17, 2, 20, 23, 1, 17, 26, 3, 4, 20, 3, 26, 7, 4, 6, 26, 4, 7, 15, 16, 23, 27, 20, 26, 21, 17, 20, 21, 20, 27, 24, 23, 17, 15, 23, 24, 18, 24, 17, 18, 17, 21, 8, 26, 7, 27, 26, 8, 14, 15, 24, 25, 24, 18, 14, 24, 25, 19, 25, 18, 27, 28, 21, 9, 27, 8, 22, 18, 21, 28, 22, 21, 19, 18, 22, 27, 9, 28, 13, 14, 25, 12, 19, 22, 13, 25, 19, 12, 13, 19, 11, 12, 22, 10, 28, 9, 11, 22, 28, 10, 11, 28], "vertices": [36.7, -56.13, 28.57, -48.72, 18.05, -39.4, 4.91, -27.79, -8.53, -16.72, -24.58, -2.41, -14.69, 0.87, -1.2, 0.27, 15.34, 0.77, 30.87, -1.36, 41.33, -2.95, 47.35, -9.73, 52.63, -15.7, 58.29, -24.11, 55.33, -33.4, 51.5, -45.25, 42.78, -52.47, 28.24, -34.6, 40.11, -27.26, 47.64, -21.38, 15.9, -21.37, 29.03, -17.25, 42.33, -13.46, 36.94, -44.69, 46.29, -35.76, 51.23, -26.82, 8.23, -11.74, 21.17, -8.37, 36.91, -7.06], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32, 4, 34, 34, 36, 36, 38, 6, 40, 40, 42, 42, 44, 2, 46, 46, 48, 48, 50, 8, 52, 52, 54, 54, 56], "width": 87, "height": 52}}, "sp3": {"sp3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-8.21, 44.82, 59.76, -31.89, -80.86, -148.33, -148.83, -71.63], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 101, "height": 179}}, "sp4": {"sp4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-341.16, -181.7, -454.16, -182.48, -456.39, 141.51, -343.39, 142.29], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 113, "height": 324}}, "sp5": {"sp5": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-65.48, -133.8, -366.48, -135.88, -368.98, 227.11, -67.99, 229.19], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 301, "height": 363}}, "g1": {"g1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [151.46, 140.59, 151.46, -145.41, -162.54, -145.41, -162.54, 140.59], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 286, "height": 314}}, "g2": {"g2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-77.48, -133.89, -364.48, -135.86, -366.9, 216.13, -79.91, 218.11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 287, "height": 352}}}}], "events": {"atk": {}}, "animations": {"boss_attack1": {"bones": {"ljbone42": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 3.96, "y": 34.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "x": 37.17, "y": -86.11, "curve": "stepped"}, {"time": 0.3667, "x": 37.17, "y": -86.11, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4667, "x": 5.35, "y": -25.71, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "ljbone43": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 33.11, "y": 86.25, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "x": -42.85, "y": 135.77, "curve": "stepped"}, {"time": 0.3667, "x": -42.85, "y": 135.77, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4667, "x": 43.53, "y": 132.52, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "ljbone44": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 178.43, "y": -0.36, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "x": 107.24, "y": -36.66, "curve": "stepped"}, {"time": 0.3667, "x": 107.24, "y": -36.66, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4667, "x": 55.28, "y": -156.8, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "ljbone45": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 72.56, "y": 24.91, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 31.17, "y": 120.35, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "ljbone46": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 149.34, "y": 38.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "x": 6.34, "y": 170.04, "curve": "stepped"}, {"time": 0.3667, "x": 6.34, "y": 170.04, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4667, "x": 8.94, "y": -150.79, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "ljbone47": {"rotate": [{"time": 0.3667, "curve": 0.333, "c2": 0.33, "c3": 0.674, "c4": 0.69}, {"time": 0.4667, "angle": -70.66, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.7333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 129.86, "y": -16.47, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -126.72, "y": 11.96, "curve": "stepped"}, {"time": 0.3667, "x": -126.72, "y": 11.96, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 180.35, "y": -143.49, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "ljbone48": {"translate": [{"curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 0.1667, "x": -7.39, "y": -9.7, "curve": 0.333, "c2": 0.33, "c3": 0.695, "c4": 0.76}, {"time": 0.3667, "x": -8.33, "y": 202.68, "curve": 0.376, "c2": 0.61, "c3": 0.718}, {"time": 0.4667, "x": 1.3, "y": 205.23, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "ljbone": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 0.966, "y": 0.966, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 1.007, "y": 1.007, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 1.155, "y": 1.155, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "bone39": {"translate": [{"x": -32.91, "y": -29.74, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -40.35, "y": -36.47, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.7333, "x": -32.91, "y": -29.74}]}, "bone38": {"translate": [{"x": -20.18, "y": -18.24, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -40.35, "y": -36.47, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.7333, "x": -20.18, "y": -18.24}]}, "bone37": {"translate": [{"x": -7.44, "y": -6.73, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -40.35, "y": -36.47, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.7333, "x": -7.44, "y": -6.73}]}, "bone21": {"translate": [{"x": 2.69, "y": -1.16, "curve": 0.313, "c2": 0.26, "c3": 0.757}, {"time": 0.3, "x": 26.08, "y": -11.23, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.289, "c3": 0.628, "c4": 0.38}, {"time": 0.7333, "x": 2.69, "y": -1.16}]}, "bone43": {"translate": [{"x": 3.23, "y": -1.86, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 15.28, "y": -8.78, "curve": 0.242, "c3": 0.661, "c4": 0.65}, {"time": 0.7333, "x": 3.23, "y": -1.86}]}, "bone20": {"translate": [{"x": 6.39, "y": -0.09, "curve": 0.38, "c2": 0.53, "c3": 0.744}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 15.93, "y": -0.23, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 0.7333, "x": 6.39, "y": -0.09}]}, "bone34": {"translate": [{"x": 2.48, "y": -0.65, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 29.91, "y": -7.83, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.7333, "x": 2.48, "y": -0.65}]}, "bone32": {"translate": [{"x": 17.92, "y": -4.69, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 29.91, "y": -7.83, "curve": 0.256, "c3": 0.62, "c4": 0.47}, {"time": 0.7333, "x": 17.92, "y": -4.69}]}, "bone35": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 15.93, "y": -0.23, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "bone30": {"translate": [{"x": 5.35, "y": -0.08, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 15.93, "y": -0.23, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.7333, "x": 5.35, "y": -0.08}]}, "bone28": {"translate": [{"x": 3.86, "y": -0.06, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2667, "x": 15.93, "y": -0.23, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.7333, "x": 3.86, "y": -0.06}]}, "bone24": {"translate": [{"x": 3.69, "y": -1.94, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 19.99, "y": -10.54, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.7333, "x": 3.69, "y": -1.94}]}, "bone23": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 19.99, "y": -10.54, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "bone26": {"translate": [{"x": 15.76, "y": -8.31, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 19.99, "y": -10.54, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 0.7333, "x": 15.76, "y": -8.31}]}, "bone25": {"translate": [{"x": 11.98, "y": -6.31, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 19.99, "y": -10.54, "curve": 0.256, "c3": 0.62, "c4": 0.47}, {"time": 0.7333, "x": 11.98, "y": -6.31}]}, "bone19": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 15.93, "y": -0.23, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "bone16": {"translate": [{"x": 4.38, "y": -1.91, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 20.68, "y": -9.03, "curve": 0.242, "c3": 0.661, "c4": 0.65}, {"time": 0.7333, "x": 4.38, "y": -1.91}]}, "bone17": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 20.68, "y": -9.03, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "bone33": {"translate": [{"x": 10.04, "y": -2.63, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 29.91, "y": -7.83, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.7333, "x": 10.04, "y": -2.63}]}, "bone15": {"translate": [{"x": 1.72, "y": -0.75, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 0.3, "x": 20.68, "y": -9.03, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 0.7333, "x": 1.72, "y": -0.75}]}, "bone36": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -40.35, "y": -36.47, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "bone13": {"translate": [{"x": 5.65, "y": -2.47, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 20.68, "y": -9.03, "curve": 0.243, "c3": 0.65, "c4": 0.61}, {"time": 0.7333, "x": 5.65, "y": -2.47}]}, "bone14": {"translate": [{"x": 12.99, "y": -5.68, "curve": 0.335, "c2": 0.34, "c3": 0.684, "c4": 0.72}, {"time": 0.1333, "x": 4.38, "y": -1.91, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 20.68, "y": -9.03, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.7333, "x": 12.99, "y": -5.68}]}, "bone40": {"translate": [{"x": -40.35, "y": -36.47, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": -40.35, "y": -36.47}]}, "bone29": {"translate": [{"x": 1.32, "y": -0.02, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 0.3, "x": 15.93, "y": -0.23, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 0.7333, "x": 1.32, "y": -0.02}]}, "bone42": {"translate": [{"x": 6.12, "y": -3.52, "curve": 0.38, "c2": 0.53, "c3": 0.744}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 15.28, "y": -8.78, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 0.7333, "x": 6.12, "y": -3.52}]}, "bone44": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 15.28, "y": -8.78, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "bone3": {"translate": [{"curve": 0.257, "c3": 0.619, "c4": 0.46}, {"time": 0.3, "y": 32.14, "curve": 0.336, "c2": 0.34, "c3": 0.684, "c4": 0.72}, {"time": 0.5333, "x": -1.66, "y": -42.19, "curve": 0.381, "c2": 0.59, "c3": 0.727}, {"time": 0.7333}]}, "bone11": {"translate": [{"curve": 0.257, "c3": 0.619, "c4": 0.46}, {"time": 0.3, "y": 32.14, "curve": 0.364, "c2": 0.45, "c3": 0.755}, {"time": 0.7333}]}, "bone5": {"translate": [{"x": -2.81, "y": -3.42, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -15.25, "y": -18.52, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.7333, "x": -2.81, "y": -3.42}]}, "bone6": {"translate": [{"x": -7.12, "y": -8.65, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -15.25, "y": -18.52, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 0.7333, "x": -7.12, "y": -8.65}]}, "bone10": {"translate": [{"x": -7.62, "y": -9.26, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "x": -15.25, "y": -18.52, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.7333, "x": -7.62, "y": -9.26}]}, "bone9": {"translate": [{"x": -12.44, "y": -15.1, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1, "x": -15.25, "y": -18.52, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.7333, "x": -12.44, "y": -15.1}]}, "bone8": {"translate": [{"x": -15.12, "y": -18.36, "curve": 0.261, "c2": 0.05, "c3": 0.751}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": -15.25, "y": -18.52, "curve": 0.325, "c3": 0.659, "c4": 0.34}, {"time": 0.7333, "x": -15.12, "y": -18.36}]}, "bone7": {"translate": [{"x": -12.02, "y": -14.6, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -15.25, "y": -18.52, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 0.7333, "x": -12.02, "y": -14.6}]}}, "events": [{"time": 0.4435, "name": "atk"}]}, "boss_idle": {"bones": {"ljbone47": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -17.89, "y": -12.63, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "ljbone46": {"translate": [{"x": -3.3, "y": -2.33, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -17.89, "y": -12.63, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": -3.3, "y": -2.33}]}, "ljbone44": {"translate": [{"x": -14.59, "y": -10.3, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": -17.89, "y": -12.63, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": -14.59, "y": -10.3}]}, "ljbone43": {"translate": [{"x": -17.89, "y": -12.63, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": -17.89, "y": -12.63}]}, "ljbone45": {"translate": [{"x": -8.94, "y": -6.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -17.89, "y": -12.63, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": -8.94, "y": -6.31}]}, "ljbone42": {"translate": [{"x": -15.07, "y": -10.64, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.3, "x": -17.89, "y": -12.63, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "curve": 0.242, "c3": 0.673, "c4": 0.69}, {"time": 2.6667, "x": -15.07, "y": -10.64}]}, "ljbone48": {"translate": [{"x": -8.94, "y": -6.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "x": -17.89, "y": -12.63, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": -8.94, "y": -6.31}]}, "bone3": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -16.84, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "ljbone": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -29.76, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "ljbone64": {"rotate": [{"angle": -6.69, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 2.89, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -6.69}]}, "ljbone65": {"rotate": [{"angle": -4.93, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -6.69, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6667, "angle": 2.89, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -4.93}]}, "ljbone66": {"rotate": [{"angle": -1.9, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -6.69, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 2.89, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.9}]}, "ljbone67": {"rotate": [{"angle": 2.89, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -6.69, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 2.89}]}, "ljbone68": {"rotate": [{"angle": 10.72, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.88, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 10.72}]}, "ljbone69": {"rotate": [{"angle": 9.09, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": 10.72, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 1.88, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 9.09}]}, "ljbone70": {"rotate": [{"angle": 6.3, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": 10.72, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 1.88, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 6.3}]}, "ljbone71": {"rotate": [{"angle": 1.88, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 10.72, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 1.88}]}, "ljbone72": {"rotate": [{"angle": 1.88, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 10.72, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 1.88}]}, "bone40": {"translate": [{"x": -40.35, "y": -36.47, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": -40.35, "y": -36.47}]}, "bone39": {"translate": [{"x": -32.91, "y": -29.74, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": -40.35, "y": -36.47, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": -32.91, "y": -29.74}]}, "bone38": {"translate": [{"x": -20.18, "y": -18.24, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -40.35, "y": -36.47, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": -20.18, "y": -18.24}]}, "bone37": {"translate": [{"x": -7.44, "y": -6.73, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -40.35, "y": -36.47, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": -7.44, "y": -6.73}]}, "bone36": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -40.35, "y": -36.47, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone35": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 15.93, "y": -0.23, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone34": {"translate": [{"x": 2.48, "y": -0.65, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "x": 29.91, "y": -7.83, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 2.6667, "x": 2.48, "y": -0.65}]}, "bone33": {"translate": [{"x": 10.04, "y": -2.63, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 29.91, "y": -7.83, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": 10.04, "y": -2.63}]}, "bone32": {"translate": [{"x": 17.92, "y": -4.69, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "x": 29.91, "y": -7.83, "curve": 0.256, "c3": 0.62, "c4": 0.47}, {"time": 2.6667, "x": 17.92, "y": -4.69}]}, "bone30": {"translate": [{"x": 5.35, "y": -0.08, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 15.93, "y": -0.23, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": 5.35, "y": -0.08}]}, "bone29": {"translate": [{"x": 1.32, "y": -0.02, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 1.1333, "x": 15.93, "y": -0.23, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 2.6667, "x": 1.32, "y": -0.02}]}, "bone28": {"translate": [{"x": 3.86, "y": -0.06, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.9333, "x": 15.93, "y": -0.23, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.6667, "x": 3.86, "y": -0.06}]}, "bone26": {"translate": [{"x": 15.76, "y": -8.31, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "x": 19.99, "y": -10.54, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 2.6667, "x": 15.76, "y": -8.31}]}, "bone25": {"translate": [{"x": 11.98, "y": -6.31, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "x": 19.99, "y": -10.54, "curve": 0.256, "c3": 0.62, "c4": 0.47}, {"time": 2.6667, "x": 11.98, "y": -6.31}]}, "bone24": {"translate": [{"x": 3.69, "y": -1.94, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 19.99, "y": -10.54, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 3.69, "y": -1.94}]}, "bone23": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 19.99, "y": -10.54, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone21": {"translate": [{"x": 2.69, "y": -1.16, "curve": 0.313, "c2": 0.26, "c3": 0.757}, {"time": 1.1, "x": 26.08, "y": -11.23, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "curve": 0.289, "c3": 0.628, "c4": 0.38}, {"time": 2.6667, "x": 2.69, "y": -1.16}]}, "bone20": {"translate": [{"x": 6.39, "y": -0.09, "curve": 0.38, "c2": 0.53, "c3": 0.744}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "x": 15.93, "y": -0.23, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 2.6667, "x": 6.39, "y": -0.09}]}, "bone19": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 15.93, "y": -0.23, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone17": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 20.68, "y": -9.03, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone16": {"translate": [{"x": 4.38, "y": -1.91, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "x": 20.68, "y": -9.03, "curve": 0.242, "c3": 0.661, "c4": 0.65}, {"time": 2.6667, "x": 4.38, "y": -1.91}]}, "bone15": {"translate": [{"x": 1.72, "y": -0.75, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 1.1333, "x": 20.68, "y": -9.03, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 2.6667, "x": 1.72, "y": -0.75}]}, "bone14": {"translate": [{"x": 12.99, "y": -5.68, "curve": 0.335, "c2": 0.34, "c3": 0.684, "c4": 0.72}, {"time": 0.4333, "x": 4.38, "y": -1.91, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "x": 20.68, "y": -9.03, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "x": 12.99, "y": -5.68}]}, "bone13": {"translate": [{"x": 5.65, "y": -2.47, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": 20.68, "y": -9.03, "curve": 0.243, "c3": 0.65, "c4": 0.61}, {"time": 2.6667, "x": 5.65, "y": -2.47}]}, "bone10": {"translate": [{"x": -7.62, "y": -9.26, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "x": -15.25, "y": -18.52, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": -7.62, "y": -9.26}]}, "bone9": {"translate": [{"x": -12.44, "y": -15.1, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "x": -15.25, "y": -18.52, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": -12.44, "y": -15.1}]}, "bone8": {"translate": [{"x": -15.12, "y": -18.36, "curve": 0.261, "c2": 0.05, "c3": 0.751}, {"time": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "x": -15.25, "y": -18.52, "curve": 0.325, "c3": 0.659, "c4": 0.34}, {"time": 2.6667, "x": -15.12, "y": -18.36}]}, "bone7": {"translate": [{"x": -12.02, "y": -14.6, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "x": -15.25, "y": -18.52, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 2.6667, "x": -12.02, "y": -14.6}]}, "bone6": {"translate": [{"x": -7.12, "y": -8.65, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "x": -15.25, "y": -18.52, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 2.6667, "x": -7.12, "y": -8.65}]}, "bone5": {"translate": [{"x": -2.81, "y": -3.42, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -15.25, "y": -18.52, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": -2.81, "y": -3.42}]}, "bone42": {"translate": [{"x": 6.12, "y": -3.52, "curve": 0.38, "c2": 0.53, "c3": 0.744}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "x": 15.28, "y": -8.78, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 2.6667, "x": 6.12, "y": -3.52}]}, "bone43": {"translate": [{"x": 3.23, "y": -1.86, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "x": 15.28, "y": -8.78, "curve": 0.242, "c3": 0.661, "c4": 0.65}, {"time": 2.6667, "x": 3.23, "y": -1.86}]}, "bone44": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 15.28, "y": -8.78, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "ljbone76": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -6.26, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "ljbone77": {"rotate": [{"angle": -1.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -6.26, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -1.15}]}, "ljbone75": {"rotate": [{"angle": -3.33, "curve": 0.336, "c2": 0.34, "c3": 0.674, "c4": 0.69}, {"time": 0.2333, "angle": -1.91, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "angle": -6.26, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 2.6667, "angle": -3.33}]}, "ljbone74": {"rotate": [{"angle": -2.49, "curve": 0.344, "c2": 0.37, "c3": 0.682, "c4": 0.72}, {"time": 0.2333, "angle": -1.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": -6.26, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 2.6667, "angle": -2.49}]}, "ljbone73": {"rotate": [{"angle": -0.65, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": -6.26, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 2.6667, "angle": -0.65}]}, "ljbone78": {"rotate": [{"angle": -1.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -6.26, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -1.15}]}, "ljbone79": {"rotate": [{"angle": -1.91, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": -6.26, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "angle": -1.91}]}, "ljbone80": {"rotate": [{"angle": -1.71, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": -6.26, "curve": 0.243, "c3": 0.65, "c4": 0.61}, {"time": 2.6667, "angle": -1.71}]}, "ljbone81": {"rotate": [{"angle": -3.73, "curve": 0.338, "c2": 0.35, "c3": 0.688, "c4": 0.73}, {"time": 0.4333, "angle": -1.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "angle": -6.26, "curve": 0.256, "c3": 0.62, "c4": 0.47}, {"time": 2.6667, "angle": -3.73}]}, "ljbone82": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -6.26, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "ljbone83": {"rotate": [{"angle": -3.93, "curve": 0.336, "c2": 0.34, "c3": 0.688, "c4": 0.73}, {"time": 0.4667, "angle": -1.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -6.26, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "angle": -3.93}]}, "ljbone58": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -9.38, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "ljbone59": {"rotate": [{"angle": -1.73, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -9.38, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -1.73}]}, "ljbone60": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -9.38, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "ljbone61": {"rotate": [{"angle": -4.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -9.38, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -4.69}]}, "ljbone56": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 8.69, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "ljbone62": {"rotate": [{"angle": 1.6, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 8.69, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 1.6}]}, "ljbone57": {"rotate": [{"angle": 4.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 8.69, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 4.34}]}, "ljbone49": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -11.28, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "ljbone50": {"rotate": [{"angle": -2.08, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -11.28, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -2.08}]}, "ljbone51": {"rotate": [{"angle": -5.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -11.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -5.64}]}, "ljbone52": {"rotate": [{"angle": -8.2, "curve": 0.35, "c2": 0.39, "c3": 0.757}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": -11.28, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "angle": -8.2}]}, "ljbone53": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -11.28, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "ljbone54": {"rotate": [{"angle": -2.08, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -11.28, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -2.08}]}, "ljbone55": {"rotate": [{"angle": -5.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -11.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -5.64}]}, "ljbone86": {"translate": [{"x": -4.87, "y": -9.95, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -9.74, "y": -19.89, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": -4.87, "y": -9.95}]}, "ljbone85": {"translate": [{"x": -7.94, "y": -16.22, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": -9.74, "y": -19.89, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": -7.94, "y": -16.22}]}, "ljbone87": {"translate": [{"x": -9.65, "y": -19.72, "curve": 0.261, "c2": 0.05, "c3": 0.751}, {"time": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "x": -9.74, "y": -19.89, "curve": 0.325, "c3": 0.659, "c4": 0.34}, {"time": 2.6667, "x": -9.65, "y": -19.72}]}, "ljbone88": {"translate": [{"x": 9.7, "y": -16.29, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 0.4333, "x": 13.35, "y": -22.41, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "curve": 0.243, "c3": 0.65, "c4": 0.61}, {"time": 2.6667, "x": 9.7, "y": -16.29}]}, "ljbone84": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -9.74, "y": -19.89, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}}}, "die": {"slots": {"y2": {"color": [{"time": 0.1, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff00"}]}, "y8": {"color": [{"time": 0.1, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff00"}]}, "g2": {"color": [{"time": 0.1, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "color": "ffffff00"}]}, "y6": {"color": [{"time": 0.1, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff00"}]}, "y3": {"color": [{"time": 0.1, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff00"}]}, "y5": {"color": [{"time": 0.1, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff00"}]}, "yj": {"attachment": [{"time": 0.0333, "name": null}]}, "sp4": {"color": [{"time": 0.1, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "color": "ffffff00"}]}, "y7": {"color": [{"time": 0.1, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff00"}]}, "y1": {"color": [{"time": 0.1, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff00"}]}, "yw2": {"color": [{"time": 0.1, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff00"}]}, "yw1": {"color": [{"time": 0.1, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff00"}]}, "g1": {"color": [{"time": 0.1333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff00"}]}, "y4": {"color": [{"time": 0.1, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff00"}]}, "sp5": {"color": [{"time": 0.1, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "color": "ffffff00"}]}, "sp1": {"color": [{"time": 0.1, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "color": "ffffff00"}]}}, "bones": {"ljbone47": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -22.27, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 353.18, "y": -108.18}]}, "ljbone44": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -22.27, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 259.32}]}, "ljbone46": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -22.27, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 427.95, "y": 235.45}]}, "ljbone45": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -22.27, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 119.32}]}, "ljbone43": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -22.27, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 117.73}]}, "ljbone42": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -22.27, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 97.05}]}, "bone3": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -7.18, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 93.37}]}, "bone11": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -7.18, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 37.35}]}}}, "hurt": {"bones": {"ljbone48": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 2.83, "y": 32.12, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}]}, "ljbone47": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": 15.12, "y": -98.26, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 0.3, "x": 88.53, "y": -59.13, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5667}]}, "ljbone46": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 15.12, "y": -42.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 7.5, "y": 66.72, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}]}, "ljbone44": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 3.78, "y": -50.07, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 4.94, "y": -31.98, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}]}, "ljbone43": {"translate": [{"x": -2.32, "y": 11.33, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 5.67, "y": -56.69, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -9.59, "y": 46.76, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.5667, "x": -2.32, "y": 11.33}]}, "ljbone42": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": -7.56, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -9.45, "y": 45.85, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}]}, "bone37": {"translate": [{"x": -7.44, "y": -6.73, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -40.35, "y": -36.47, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5667, "x": -7.44, "y": -6.73}]}, "bone44": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 15.28, "y": -8.78, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}]}, "bone32": {"translate": [{"x": 17.92, "y": -4.69, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 29.91, "y": -7.83, "curve": 0.256, "c3": 0.62, "c4": 0.47}, {"time": 0.5667, "x": 17.92, "y": -4.69}]}, "bone34": {"translate": [{"x": 2.48, "y": -0.65, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 29.91, "y": -7.83, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 0.5667, "x": 2.48, "y": -0.65}]}, "bone36": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -40.35, "y": -36.47, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}]}, "bone28": {"translate": [{"x": 3.86, "y": -0.06, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2, "x": 15.93, "y": -0.23, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.5667, "x": 3.86, "y": -0.06}]}, "bone15": {"translate": [{"x": 1.72, "y": -0.75, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 0.2333, "x": 20.68, "y": -9.03, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 0.5667, "x": 1.72, "y": -0.75}]}, "bone30": {"translate": [{"x": 5.35, "y": -0.08, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 15.93, "y": -0.23, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.5667, "x": 5.35, "y": -0.08}]}, "bone16": {"translate": [{"x": 4.38, "y": -1.91, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 20.68, "y": -9.03, "curve": 0.242, "c3": 0.661, "c4": 0.65}, {"time": 0.5667, "x": 4.38, "y": -1.91}]}, "bone25": {"translate": [{"x": 11.98, "y": -6.31, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 19.99, "y": -10.54, "curve": 0.256, "c3": 0.62, "c4": 0.47}, {"time": 0.5667, "x": 11.98, "y": -6.31}]}, "bone14": {"translate": [{"x": 12.99, "y": -5.68, "curve": 0.335, "c2": 0.34, "c3": 0.684, "c4": 0.72}, {"time": 0.1, "x": 4.38, "y": -1.91, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 20.68, "y": -9.03, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.5667, "x": 12.99, "y": -5.68}]}, "bone43": {"translate": [{"x": 3.23, "y": -1.86, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 15.28, "y": -8.78, "curve": 0.242, "c3": 0.661, "c4": 0.65}, {"time": 0.5667, "x": 3.23, "y": -1.86}]}, "bone39": {"translate": [{"x": -32.91, "y": -29.74, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -40.35, "y": -36.47, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5667, "x": -32.91, "y": -29.74}]}, "bone21": {"translate": [{"x": 2.69, "y": -1.16, "curve": 0.313, "c2": 0.26, "c3": 0.757}, {"time": 0.2333, "x": 26.08, "y": -11.23, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.289, "c3": 0.628, "c4": 0.38}, {"time": 0.5667, "x": 2.69, "y": -1.16}]}, "bone42": {"translate": [{"x": 6.12, "y": -3.52, "curve": 0.38, "c2": 0.53, "c3": 0.744}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 15.28, "y": -8.78, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 0.5667, "x": 6.12, "y": -3.52}]}, "bone13": {"translate": [{"x": 5.65, "y": -2.47, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 20.68, "y": -9.03, "curve": 0.243, "c3": 0.65, "c4": 0.61}, {"time": 0.5667, "x": 5.65, "y": -2.47}]}, "bone35": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 15.93, "y": -0.23, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}]}, "bone33": {"translate": [{"x": 10.04, "y": -2.63, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 29.91, "y": -7.83, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.5667, "x": 10.04, "y": -2.63}]}, "bone17": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 20.68, "y": -9.03, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}]}, "bone29": {"translate": [{"x": 1.32, "y": -0.02, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 0.2333, "x": 15.93, "y": -0.23, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 0.5667, "x": 1.32, "y": -0.02}]}, "bone24": {"translate": [{"x": 3.69, "y": -1.94, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 19.99, "y": -10.54, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5667, "x": 3.69, "y": -1.94}]}, "bone38": {"translate": [{"x": -20.18, "y": -18.24, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -40.35, "y": -36.47, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5667, "x": -20.18, "y": -18.24}]}, "bone20": {"translate": [{"x": 6.39, "y": -0.09, "curve": 0.38, "c2": 0.53, "c3": 0.744}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 15.93, "y": -0.23, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 0.5667, "x": 6.39, "y": -0.09}]}, "bone40": {"translate": [{"x": -40.35, "y": -36.47, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": -40.35, "y": -36.47}]}, "bone19": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 15.93, "y": -0.23, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}]}, "bone26": {"translate": [{"x": 15.76, "y": -8.31, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 19.99, "y": -10.54, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 0.5667, "x": 15.76, "y": -8.31}]}, "bone23": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 19.99, "y": -10.54, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}]}, "bone3": {"translate": [{"curve": 0.252, "c3": 0.622, "c4": 0.49}, {"time": 0.2667, "y": 25.45, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5667}]}, "bone6": {"translate": [{"x": -7.12, "y": -8.65, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -15.25, "y": -18.52, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 0.5667, "x": -7.12, "y": -8.65}]}, "bone5": {"translate": [{"x": -2.81, "y": -3.42, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -15.25, "y": -18.52, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5667, "x": -2.81, "y": -3.42}]}, "bone7": {"translate": [{"x": -12.02, "y": -14.6, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -15.25, "y": -18.52, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 0.5667, "x": -12.02, "y": -14.6}]}, "bone10": {"translate": [{"x": -7.62, "y": -9.26, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "x": -15.25, "y": -18.52, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5667, "x": -7.62, "y": -9.26}]}, "bone9": {"translate": [{"x": -12.44, "y": -15.1, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "x": -15.25, "y": -18.52, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5667, "x": -12.44, "y": -15.1}]}, "bone8": {"translate": [{"x": -15.12, "y": -18.36, "curve": 0.261, "c2": 0.05, "c3": 0.751}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -15.25, "y": -18.52, "curve": 0.325, "c3": 0.659, "c4": 0.34}, {"time": 0.5667, "x": -15.12, "y": -18.36}]}}}}}