import { GameData } from "../../game/GameData";
import data from "../../lib/data/data";
import { ActivityModule } from "../activity/ActivityModule";
import { FrbpApi } from "./FrbpApi";
import { FrbpConfig } from "./FrbpConfig";
import { FrbpData } from "./FrbpData";
import { FrbpRoute } from "./FrbpRoute";
import { FrbpService } from "./FrbpService";
import { FrbpSubscriber } from "./FrbpSubscriber";
import { FrbpViewModel } from "./FrbpViewModel";

export const activityId = 10801;
export class FrbpModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): FrbpModule {
    if (!GameData.instance.FrbpModule) {
      GameData.instance.FrbpModule = new FrbpModule();
    }
    return GameData.instance.FrbpModule;
  }
  private _data = new FrbpData();
  private _api = new FrbpApi();
  private _service = new FrbpService();
  private _subscriber = new FrbpSubscriber();
  private _route = new FrbpRoute();
  private _viewModel = new FrbpViewModel();
  private _config = new FrbpConfig();

  public static get data() {
    return this.instance._data;
  }

  public static get route() {
    return this.instance._route;
  }

  public static get api() {
    return this.instance._api;
  }

  public static get config() {
    return this.instance._config;
  }

  public static get service() {
    return this.instance._service;
  }

  public static get viewModel() {
    return this.instance._viewModel;
  }

  public init(data?: any, completedCallback?) {
    if (this._subscriber) {
      this._subscriber.unRegister();
    }
    this._data = new FrbpData();
    this._api = new FrbpApi();
    this._service = new FrbpService();
    this._subscriber = new FrbpSubscriber();
    this._route = new FrbpRoute();
    this._viewModel = new FrbpViewModel();
    this._config = new FrbpConfig();

    // 模块初始化
    this._subscriber.register();
    this._route.init();

    this._api.getFrbpInfo(activityId);
    this._api.getRankList(activityId);

    completedCallback && completedCallback();
  }

  protected saveKey(): string {
    return this.constructor.name;
  }
}
