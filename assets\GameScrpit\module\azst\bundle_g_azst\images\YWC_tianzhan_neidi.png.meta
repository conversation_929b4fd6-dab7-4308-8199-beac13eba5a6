{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "c09523b0-f42f-41be-8c67-8222125e6f05", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "c09523b0-f42f-41be-8c67-8222125e6f05@6c48a", "displayName": "Y<PERSON>_tian<PERSON><PERSON>_neidi", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "c09523b0-f42f-41be-8c67-8222125e6f05", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "c09523b0-f42f-41be-8c67-8222125e6f05@f9941", "displayName": "Y<PERSON>_tian<PERSON><PERSON>_neidi", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 31, "height": 30, "rawWidth": 31, "rawHeight": 30, "borderTop": 12, "borderBottom": 12, "borderLeft": 12, "borderRight": 12, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-15.5, -15, 0, 15.5, -15, 0, -15.5, 15, 0, 15.5, 15, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 30, 31, 30, 0, 0, 31, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-15.5, -15, 0], "maxPos": [15.5, 15, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "c09523b0-f42f-41be-8c67-8222125e6f05@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "c09523b0-f42f-41be-8c67-8222125e6f05@6c48a"}}