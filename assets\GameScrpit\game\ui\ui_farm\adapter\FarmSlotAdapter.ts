import { _decorator, instantiate, Label, Node, Sprite } from "cc";
import { ListAdapter } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { FarmSlotItemViewHolder } from "./FarmSlotItemViewHolder";

export class FarmSlotAdapter extends ListAdapter {
  // 常规节点
  item: Node;

  constructor(item: Node) {
    super();
    this.item = item;
  }

  // 所有缓存节点
  datas: any[] = [];
  setDatas(data: any[]) {
    if (!data) {
      return;
    }
    this.datas = data;
    this.notifyDataSetChanged();
  }

  getCount(): number {
    return this.datas.length;
  }

  onCreateView(viewType: number): Node {
    let item = instantiate(this.item);
    item.getComponent(FarmSlotItemViewHolder).init();
    item.active = true;
    return item;
  }

  onBindData(node: Node, position: number): void {
    let itemData = this.datas[position];
    node.getComponent(FarmSlotItemViewHolder).updateData(itemData);
  }
}
