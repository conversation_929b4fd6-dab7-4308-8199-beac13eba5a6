import { ExternalMessage } from "../net/protocol/ExternalMessage";
import SocketClient from "../../lib/socket/SocketClient";
import { BinaryWriter } from "@bufbuild/protobuf/wire";

export default class SendMgr {
  private static msgId: number = 0;

  public static send(mergeCmd: number, data: BinaryWriter, cmdCode: number = 1, protocolSwitch: number = 0) {
    SendMgr.msgId++;

    let msg: ExternalMessage = {
      cmdCode: cmdCode,
      protocolSwitch: protocolSwitch,
      cmdMerge: mergeCmd,
      responseStatus: 0,
      validMsg: "",
      data: new Uint8Array(0),
      msgId: SendMgr.msgId,
    };

    if (data) {
      msg.data = data.finish();
    }

    SocketClient.ins.send(mergeCmd, ExternalMessage.encode(msg).finish());
    return SendMgr.msgId;
  }
}
