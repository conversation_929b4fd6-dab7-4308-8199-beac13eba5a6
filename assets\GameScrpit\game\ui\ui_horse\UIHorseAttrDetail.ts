import { _decorator, Label, Node } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { JsonMgr } from "../../mgr/JsonMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { HorseModule } from "../../../module/horse/HorseModule";
import { AttrEnum } from "../../GameDefine";
import Formate from "../../../lib/utils/Formate";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
const { ccclass, property } = _decorator;

@ccclass("UIHorseAttrDetail")
export class UIHorseAttrDetail extends UINode {
  protected _openAct: boolean = true;
  // 坐骑ID
  horseId: number;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_HORSE}?prefab/ui/UIHorseAttrDetail`;
  }

  public init(args: any): void {
    super.init(args);
    this.horseId = args.horseId;
  }

  protected onEvtShow(): void {
    let horse = HorseModule.data.horseMessage;
    let resistAttr = HorseModule.data.getResistAttr(this.horseId);
    let resistAttrNext = HorseModule.data.getResistAttr(this.horseId, true);
    let configHorse = HorseModule.data.getConfigHorse(this.horseId);
    this.getNode("horse_level_lab").getComponent(Label).string = `${horse.stage} 阶 ${horse.grade} 级`;

    //使用加成
    this.getNode("no_attr_add_node").active = configHorse.mainId == 0;
    this.getNode("node_attr_up_main").active = configHorse.mainId != 0;
    if (configHorse.mainId != 0) {
      let node1 = this.getNode("node_attr_up_main");
      let config = JsonMgr.instance.getConfigAttribute(configHorse.mainId);
      this.setAttrLabNode(node1, config.name, resistAttr[configHorse.mainId], resistAttrNext[configHorse.mainId]);
    }

    let list1 = [AttrEnum.抗击晕_31, AttrEnum.抗闪避_32, AttrEnum.抗连击_33, AttrEnum.抗反击_34, AttrEnum.抗暴击_35];
    list1 = list1.filter((e) => e != configHorse.mainId);
    this.getNode("attr_list_lay").children.forEach((child) => (child.active = false));
    for (let i = 0; i < list1.length; i++) {
      let attrId = list1[i];
      if (!resistAttrNext[attrId] && !resistAttr[attrId]) {
        continue;
      }
      let node = this.getNode("attr_list_lay").children[i];
      let config = JsonMgr.instance.getConfigAttribute(attrId);
      this.setAttrLabNode(node, config.name, resistAttr[attrId], resistAttrNext[attrId]);
    }
  }

  private setAttrLabNode(node: Node, name: string, num1: number, num2: number) {
    node.getChildByName("lbl_title").getComponent(Label).string = name;
    node.getChildByName("lbl_attr_old").getComponent(Label).string = `${Formate.format(num1 * 100)}%`;
    node.getChildByName("lbl_attr_new").getComponent(Label).string = `${Formate.format(num2 * 100)}%`;
    node.active = true;
  }

  private on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
}
