syntax = "proto3";
package sim;

// 
message GoodsMessage {
  // 资源列表
  map<int64,double> resMap = 1;
  // 道具列表
  map<int64,int32> itemMap = 2;
  // 英雄列表
  map<int64,int32> heroMap = 3;
  // 珍兽列表
  map<int64,int32> petMap = 4;
  // 挚友列表
  map<int64,int32> friendMap = 5;
  // 皮肤列表
  map<int64,int32> skinMap = 6;
  // 头像列表
  map<int64,int32> headShowMap = 7;
  // 头像框列表
  map<int64,int32> headFrameMap = 8;
  // 气泡列表
  map<int64,int32> bubbleMap = 9;
  // 头像列表
  map<int64,int32> titleMap = 10;
}

// 物品类别数量信息
message ItemMessage {
  // 物品ID
  int64 id = 1;
  // 类别Id
  int64 itemId = 2;
  // 数量
  int32 num = 3;
}

// 
message ItemUseMessage {
  // 使用的道具(合成时表示要合成的道具)
  int64 itemId = 1;
  int32 num = 2;
  // 指定英雄
  int64 heroId = 3;
  // 指定据点
  int64 cityId = 4;
  // 自选道具宝箱：用户从宝箱中选择道具的索引(从0开始)，以及选择的数量
  map<int32,int32> choiceItemMap = 5;
}

