/** c_leader(主角) */
export interface IConfigLeaderRecord {
  id: number;
  /** 主角身份 */
  name: string;
  /**境界 */
  jingjie: string;
  jingjie2: string;
  jingjieIcon: string;
  jingjieBg: string;
  jinjieBg: string;
  attrAdd: number[][];
  jieji: number;
  /** 所需运势 */
  speed: number;
  /** 所需功德 */
  virtue: number;
  /** 每日奖励仙玉 */
  dayReward: number;
  /** 解锁战将 */
  unlockList: number[][];
  /** 谈心上限 */
  chatMax: number;
  /** 谈心恢复时间 */
  chatTime: number;
  /** 徒弟体力槽大小 */
  trainMax: number;
  /** 徒弟基础培养获得学识 */
  trainReward: number;
  /** 徒弟培养气运消耗 */
  trainCost: number;
  /** 徒弟成年运势加成 */
  trainAdd: number;
  /**  改名卡消耗道具数量 */
  nameCostList: number[];
  /** 默认进入府邸的等级 */
  fudiLv: number;
  /** 初始属性 */
  fistAttrList: number[][];

  maxLv: number;

  guideColdTime: number;
}

export class PlayerMsgEnum {
  public static SKIN_UPDATA = "SKIN_UPDATA";
}

export interface ConfigSystemOpenRecord {
  id: number;
  name: string;
  key: string;
  openList: number[];
}

export const PlayerAudioName = {
  Effect: {
    点击页签: 1041,
    升级成功: 1042,
    点击皮肤图标: 1043,
    点击战斗预览按钮: 1044,
    点击换装内页签: 1046,
    点击换装使用按钮: 1047,
    点击激活按钮: 1048,
    点击头像图标: 1049,
    点击称号图标: 1050,
    点击装饰使用按钮: 1051,
  },
  Sound: {
    战斗预览背景音乐: 1045,
  },
};

export const PlayerIdentityAudioName = {
  Effect: {
    点击聚宝盆: 1161,
  },
  Sound: {},
};

export const CollectAudioName = {
  Effect: {
    点击图鉴兑换: 1221,
    点击羁绊: 1222,
    点击角色: 1223,
    点击兑换按钮: 1224,
    点击角色技能: 1225,
  },
  Sound: {},
};

export const ShopAudioName = {
  Effect: {
    点击商店图标: 1241,
    点击下方页签: 1242,
    点击购买: 1243,
  },
  Sound: {},
};

export const KnappsackAudioName = {
  Effect: {
    点击背包图标: 1441,
    点击道具图标: 1442,
    点击使用按钮: 1443,
    点击合成按钮: 1444,
    点击下方页签: 1445,
  },
  Sound: {},
};
