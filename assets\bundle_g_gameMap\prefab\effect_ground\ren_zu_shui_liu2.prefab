[{"__type__": "cc.Prefab", "_name": "ren_zu_shui_liu2", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "ren_zu_shui_liu2", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}, {"__id__": 4}], "_prefab": {"__id__": 6}, "_lpos": {"__type__": "cc.Vec3", "x": -1585.94, "y": -94.518, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 3}, "_contentSize": {"__type__": "cc.Size", "width": 181.2100067138672, "height": 424.3500061035156}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.43844155402352974, "y": 0.41032165102576384}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "17bcGs1RxGCr0931uVZ5R6"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 5}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "8d0baa07-217f-4c7c-9a2a-95b169cdcea1", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "ren_zu_shui_liu_2", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "87gmU7bcxKYpHoTRM+CTq0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bcreW/Gg9F9K9sboMtrULg", "targetOverrides": null}]