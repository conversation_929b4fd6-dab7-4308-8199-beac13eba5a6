import { instantiate, Node } from "cc";
import { ListAdapter } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { ChengjiuviewHolder } from "./ChengjiuViewHolder";

export class ChengjiuAdapter extends ListAdapter {
  private _item: Node;
  constructor(item: Node) {
    super();
    this._item = item;
  }
  onCreateView(viewType: number): Node {
    let item = instantiate(this._item);
    item.active = true;
    return item;
  }
  onBindData(node: Node, position: number): void {
    node.getComponent(ChengjiuviewHolder).updateData(position);
  }
  getCount(): number {
    return 14;
  }
}
