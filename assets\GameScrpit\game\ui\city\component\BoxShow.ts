import { _decorator, Node, sp, tween, v3, Vec3 } from "cc";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import ToolExt from "../../../common/ToolExt";
const { ccclass, property } = _decorator;

@ccclass("BoxShow")
export class BoxShow extends BaseCtrl {
  startAni(startPos: Vec3, endPos: Vec3, type: number) {
    const node_box = this.getNode("node_box");

    tween(node_box)
      .set({ worldPosition: startPos })
      .to(0.7, null, {
        onUpdate: (target: Node, ratio: number) => {
          let pos = ToolExt.twoBezier(ratio, startPos, v3((endPos.x + startPos.x) / 2, startPos.y - 500, 0), endPos);
          target.setWorldPosition(pos);
        },
        easing: "sineInOut",
      })
      .delay(0.3)
      .destroySelf()
      .start();

    tween(this.getNode("bg_box"))
      .delay(0.7)
      .to(0.3, { scale: v3(0, 0, 1) }, { easing: "backIn" })
      .start();
  }
}
