import { _decorator, Component, isValid, Label, Node, ScrollView, Sprite, UITransform, v3 } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { Sleep } from "../../GameDefine";
import { dtTime } from "../../BoutStartUp";
import ToolExt from "../../common/ToolExt";
import { activityId, FrbpModule } from "../../../module/frbp/FrbpModule";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { RedeemRequest, RedeemResponse } from "../../net/protocol/Activity";
import MsgMgr from "../../../lib/event/MsgMgr";
import { PlayerModule } from "../../../module/player/PlayerModule";
import MsgEnum from "../../event/MsgEnum";
import GameHttpApi from "../../httpNet/GameHttpApi";
import { GoodsRouteName } from "../../../module/goods/GoodsRoute";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import FmUtils from "../../../lib/utils/FmUtils";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;
const db_info = "db_info";
const lb_index = "lb_index";
@ccclass("UIFrbpLb")
export class UIFrbpLb extends UINode {
  protected _openAct: boolean = true;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_HD_FRBP}?prefab/ui/UIFrbpLb`;
  }

  private _vo: any;

  protected onRegEvent(): void {
    MsgMgr.on(MsgEnum.ON_FRBP_LB_UPDATE, this.loadLbList, this);
  }

  protected onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_FRBP_LB_UPDATE, this.loadLbList, this);
  }

  public init(args: any): void {
    super.init(args);
    this._vo = args.vo;
  }

  protected async onEvtShow() {
    this._vo = await FrbpModule.data.getVO(
      activityId,
      () => {
        TipsMgr.setEnableTouch(true);
      },
      () => {
        UIMgr.instance.back();
        TipsMgr.setEnableTouch(true);
      }
    );
    TipsMgr.setEnableTouch(true);

    this.loadLbList();
  }

  private async loadLbList() {
    let list = this._vo.redeemList[0];

    let arr1 = [];
    let arr2 = [];
    for (let i = 0; i < list.length; i++) {
      let info = list[i];
      /**次数判断，是否显示购买按钮售空状态 */
      let redem_num = FrbpModule.data.ProsperityMessage.redeemMap[info.id] || 0;
      let show_bool = redem_num >= info.max ? true : false; //true 是代表已经售空
      if (show_bool == true) {
        arr1.push({ index: i, db: info });
      } else {
        arr2.push({ index: i, db: info });
      }
    }
    let arr3 = arr2.concat(arr1);

    if (arr3.length < this.getNode("award_content3").children.length) {
      let num = this.getNode("award_content3").children.length - arr3.length;
      for (let i = 0; i < num; i++) {
        let item: Node = this.getNode("award_content3").children[0];
        item.removeFromParent();
        item.destroy();
      }
    }

    for (let i = 0; i < arr3.length; i++) {
      if (i > 3 && this.getNode("award_content3").children.length < arr3.length) {
        await Sleep(dtTime);
      }

      if (isValid(this.node) == false) {
        return;
      }

      let node = this.getNode("award_content3").children[i];

      if (!node) {
        node = ToolExt.clone(this.getNode("lb_node"), this);
        node.setPosition(v3(0, 0, 0));
        node.active = true;
        this.getNode("award_content3").addChild(node);
      }

      this.setLbNode(node, arr3[i]);
    }
  }

  private setLbNode(node: Node, info: any) {
    log.log("单个礼包数据=====", info);
    node["lbl_day_item_name"].getComponent(Label).string = info.db.name || "没配置名字的礼包";
    this.setBuyType(node, info);
    this.update_type3_node_state(node, info);
    this.loadLbItem(node, info);
  }

  private setBuyType(node: Node, info: any) {
    node["btn_type3_goumai"][db_info] = info;

    /**代表是免费礼包 ， 不要钱免费送 */
    if (info.db.adNum == 0 && info.db.price == 0) {
      node["lbl_money"].getComponent(Label).string = "免费";
    } else if (info.db.price > 0) {
      /**收费礼包，要人民币 */
      node["lbl_money"].getComponent(Label).string = (info.db.price % 10000) + "元";
    }
  }

  /**节点刷新是否领取过的状态 */
  private update_type3_node_state(node: Node, info: any) {
    /**次数判断，是否显示购买按钮售空状态 */
    let redem_num = FrbpModule.data.ProsperityMessage.redeemMap[info.db.id] || 0;
    let show_bool = redem_num >= info.db.max ? true : false; //true 是代表已经售空

    node["btn_yigoumai"].active = show_bool;
    node["btn_type3_goumai"].active = !show_bool;
    node["lbl_buy_max"].active = !show_bool;
    if (show_bool == false) {
      node["lbl_buy_max"].getComponent(Label).string =
        ToolExt.getMaxtypeLab(info.db.maxtype) + `(${info.db.max - redem_num}/${info.db.max})`;
    }
  }

  loadLbItem(node: Node, info: any) {
    let item_content = node["item_content"];
    let list = ToolExt.traAwardItemMapList(info.db.rewardList);

    if (list.length < item_content.children.length) {
      let num = item_content.children.length - list.length;
      for (let i = 0; i < num; i++) {
        let item: Node = item_content.children[0];
        item.removeFromParent();
        item.destroy();
      }
    }

    for (let i = 0; i < list.length; i++) {
      let item = item_content.children[i];

      if (!item) {
        item = ToolExt.clone(this.getNode("Item"));
        item.setPosition(v3(0, 0, 0));
        item.parent = item_content;
        item.active = true;
      }

      let data_item = list[i];
      FmUtils.setItemNode(item, data_item.id, data_item.num);
    }

    if (item_content.children.length > 3) {
      node.getChildByName("ScrollView").getComponent(ScrollView).enabled = true;
    } else {
      node.getChildByName("ScrollView").getComponent(ScrollView).enabled = false;
    }
  }

  private on_click_btn_type3_goumai(event) {
    AudioMgr.instance.playEffect(2007);
    let info: any = event.node[db_info].db;
    if (info.price == 0) {
      let param: RedeemRequest = {
        activityId: this._vo.id,
        redeemId: info.id,
        count: 1,
      };

      FrbpModule.api.buyFixedPack(param, (res: RedeemResponse) => {
        let rewardList = res.rewardList;
        MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: rewardList });
      });
      return;
    }

    let goodsId = Number(info.id);
    let goodsType = 9;
    let playerId = PlayerModule.data.playerId;
    let orderAmount = info.price % 10000;
    let goodsName = info.name || "";
    let platformType = "TEST";
    this.onPay(goodsId, goodsType, playerId, orderAmount, goodsName, platformType);
  }

  private onPay(
    goodsId: number,
    goodsType: number,
    playerId: number,
    orderAmount: number,
    goodsName: string,
    platformType: string
  ) {
    GameHttpApi.pay({
      goodsId,
      goodsType,
      playerId,
      orderAmount,
      goodsName,
      platformType,
    }).then((resp: any) => {
      // window.open(resp.data.url);
      if (resp.code != 200) {
        let err = JSON.parse(resp.msg);
        log.log(err);
        return;
      }
      UIMgr.instance.showDialog(GoodsRouteName.UIPay, { url: resp.data.url });
    });
  }

  on_click_btn_close_huang_2() {
    UIMgr.instance.back();
  }

  private _tickIndex: number = 0;
  public tick(dt: any): void {
    let pengz = this.getNode("pengz");
    let content_list = this.getNode("award_content3");

    const Box1 = pengz.getComponent(UITransform).getBoundingBoxToWorld();

    const index = this._tickIndex;
    for (let i = index; i < index + 5 && i < content_list.children.length; i++) {
      const Box2 = content_list.children[i].getComponent(UITransform).getBoundingBoxToWorld();

      if (Box1.intersects(Box2)) {
        content_list.children[i].walk((val) => {
          if (val.getComponent(Label)) {
            val.getComponent(Label).enabled = true;
          }
          if (val.getComponent(Sprite)) {
            val.getComponent(Sprite).enabled = true;
          }
        });
      } else {
        content_list.children[i].walk((val) => {
          if (val.getComponent(Label)) {
            val.getComponent(Label).enabled = false;
          }
          if (val.getComponent(Sprite)) {
            val.getComponent(Sprite).enabled = false;
          }
        });
      }
      this._tickIndex = i;
    }

    if (this._tickIndex >= content_list.children.length - 1) {
      this._tickIndex = 0;
    }
  }
}
