{"skeleton": {"hash": "MMsyFQLQklH0Dy798HOvnp3hS/8", "spine": "3.8.99", "x": -148.7, "y": -4.06, "width": 279.26, "height": 209.11, "images": "./images/", "audio": "D:/spine导出/灵兽动画/黑狗"}, "bones": [{"name": "root", "scaleX": -1}, {"name": "bone", "parent": "root", "length": 120.78, "rotation": -0.46, "x": 9.27, "y": -19.22, "scaleX": 0.5497, "scaleY": 0.5497}, {"name": "bone2", "parent": "bone", "length": 90, "rotation": 0.7, "x": 23.44, "y": 189.72}, {"name": "bone3", "parent": "bone2", "length": 66.71, "rotation": 177.95, "x": -10.79, "y": -44.98}, {"name": "bone4", "parent": "bone3", "length": 48.05, "rotation": -36.77, "x": 66.71}, {"name": "bone5", "parent": "bone4", "length": 24.54, "rotation": -24.85, "x": 48.05}, {"name": "bone6", "parent": "bone2", "length": 42.76, "rotation": 4.29, "x": 0.6, "y": -45.03}, {"name": "bone7", "parent": "bone5", "rotation": -25.57, "x": 65.22, "y": 9.32}, {"name": "bone8", "parent": "bone5", "length": 22.71, "rotation": 32.76, "x": 186.73, "y": -18.71}, {"name": "bone9", "parent": "bone8", "length": 36.88, "rotation": 55.63, "x": 23, "y": 0.17}, {"name": "bone10", "parent": "bone9", "length": 40.29, "rotation": 22.04, "x": 37.04, "y": -0.44}, {"name": "bone11", "parent": "bone5", "length": 23.04, "rotation": -85.6, "x": 148.64, "y": -121.74}, {"name": "bone12", "parent": "bone11", "length": 44.1, "rotation": -54.27, "x": 23.04}, {"name": "bone13", "parent": "bone12", "length": 39.75, "rotation": -35.24, "x": 44.49, "y": -0.91}, {"name": "bone14", "parent": "bone5", "length": 27.33, "rotation": 12.85, "x": 160.36, "y": -67.71}, {"name": "bone15", "parent": "bone14", "length": 24.82, "rotation": 29.4, "x": 27.33}, {"name": "bone16", "parent": "bone15", "length": 29.22, "rotation": -14.55, "x": 24.82}, {"name": "bone17", "parent": "bone5", "length": 15.4, "rotation": -31.95, "x": 158.16, "y": -89.57}, {"name": "bone18", "parent": "bone17", "length": 19.24, "rotation": 47.95, "x": 15.4}, {"name": "bone19", "parent": "bone5", "length": 23.32, "rotation": 40.05, "x": 181.71, "y": -30.85}, {"name": "bone20", "parent": "bone3", "length": 81.4, "rotation": 94.45, "x": 47.41, "y": -27.01}, {"name": "bone21", "parent": "bone20", "length": 50.62, "rotation": -35.86, "x": 81.4}, {"name": "bone22", "parent": "bone21", "length": 43.14, "rotation": -48.79, "x": 50.62}, {"name": "bone23", "parent": "bone3", "length": 88.63, "rotation": 74.4, "x": 106.85, "y": -21.82}, {"name": "bone24", "parent": "bone23", "length": 47.03, "rotation": -32.23, "x": 88.63}, {"name": "bone25", "parent": "bone24", "length": 30.71, "rotation": -51.81, "x": 47.03}, {"name": "bone26", "parent": "bone6", "length": 24.43, "rotation": -74.75, "x": 50.45, "y": 34.39}, {"name": "bone27", "parent": "bone26", "length": 23.81, "rotation": 8.74, "x": 24.43}, {"name": "bone28", "parent": "bone27", "length": 23.57, "rotation": 6.22, "x": 23.81}, {"name": "bone29", "parent": "bone6", "length": 55.99, "rotation": -68.87, "x": 25.42, "y": 19.21}, {"name": "bone30", "parent": "bone29", "length": 66.16, "rotation": 45.44, "x": 55.74, "y": 0.12}, {"name": "bone31", "parent": "bone30", "length": 32.31, "rotation": -103.27, "x": 65.38, "y": -2.06}, {"name": "bone32", "parent": "bone31", "length": 41.75, "rotation": -41.36, "x": 32.31}, {"name": "bone33", "parent": "bone6", "length": 42.48, "rotation": -104.95, "x": -3.97, "y": 17.66}, {"name": "bone34", "parent": "bone33", "length": 44.09, "rotation": 67.42, "x": 42.48}, {"name": "bone35", "parent": "bone34", "length": 36.3, "rotation": -82.89, "x": 44.09}, {"name": "bone36", "parent": "bone35", "length": 38.78, "rotation": -56.28, "x": 36.3}, {"name": "bone37", "parent": "bone6", "length": 58.23, "rotation": -4.18, "x": 44.46, "y": 59.47}, {"name": "bone38", "parent": "bone37", "length": 57.37, "rotation": 23.22, "x": 58.23}, {"name": "bone39", "parent": "bone38", "length": 54.58, "rotation": 47.18, "x": 57.05, "y": 0.14}, {"name": "bone40", "parent": "bone39", "length": 55.4, "rotation": 83.18, "x": 54.58}, {"name": "bone41", "parent": "bone40", "length": 61.77, "rotation": 55.13, "x": 55.4}, {"name": "bone42", "parent": "bone13", "length": 15.25, "rotation": 0.63, "x": 48.67, "y": 18.83}, {"name": "bone43", "parent": "bone39", "length": 28.73, "rotation": -82.14, "x": 8.34, "y": 26.23}, {"name": "bone44", "parent": "bone43", "length": 30.65, "rotation": -26.37, "x": 28.73}, {"name": "bone45", "parent": "bone44", "length": 27.34, "rotation": -48.57, "x": 30.52, "y": 0.17}, {"name": "bone46", "parent": "bone45", "length": 30.29, "rotation": -17, "x": 27.34}, {"name": "bone47", "parent": "bone46", "length": 25.38, "rotation": 17.28, "x": 30.29}, {"name": "bone48", "parent": "bone43", "length": 35.33, "rotation": -28.59, "x": -0.07, "y": -10.7}, {"name": "bone49", "parent": "bone48", "length": 39.34, "rotation": -13.59, "x": 35.33}, {"name": "bone50", "parent": "bone49", "length": 39.37, "rotation": -15.98, "x": 49.13, "y": -1.8}, {"name": "bone51", "parent": "bone50", "length": 26.95, "rotation": 14.66, "x": 43.72, "y": -0.22}, {"name": "bone52", "parent": "bone47", "length": 20.27, "rotation": 10.01, "x": 47.9, "y": 3.63}, {"name": "bone53", "parent": "bone", "rotation": 0.7, "x": -174.17, "y": 34.41}, {"name": "target1", "parent": "bone53", "rotation": -0.24, "x": 18.58, "y": 18.48, "color": "ff3f00ff"}, {"name": "target2", "parent": "bone53", "rotation": -0.24, "x": -13.01, "y": 23.83, "color": "ff3f00ff"}, {"name": "bone54", "parent": "bone", "x": -79.09, "y": 20.68}, {"name": "target3", "parent": "bone54", "rotation": 0.46, "x": 22.34, "y": 28.14, "color": "ff3f00ff"}, {"name": "target4", "parent": "bone54", "rotation": 0.46, "x": -21.58, "y": 20.69, "color": "ff3f00ff"}, {"name": "bone55", "parent": "bone", "x": 17.11, "y": 43}, {"name": "target5", "parent": "bone55", "rotation": 0.46, "x": 31.53, "y": 53.17, "color": "ff3f00ff"}, {"name": "target6", "parent": "bone55", "rotation": 0.46, "x": 15.55, "y": 20.56, "color": "ff3f00ff"}, {"name": "bone56", "parent": "bone", "x": 104.06, "y": 34.4}, {"name": "target7", "parent": "bone56", "rotation": 0.46, "x": 29.42, "y": 58.82, "color": "ff3f00ff"}, {"name": "target8", "parent": "bone56", "rotation": 0.46, "x": 10.64, "y": 29.08, "color": "ff3f00ff"}], "slots": [{"name": "hongsheng", "bone": "bone", "attachment": "hongsheng"}, {"name": "tq31", "bone": "bone", "attachment": "tq31"}, {"name": "weiba2", "bone": "bone", "attachment": "weiba2"}, {"name": "weiba1", "bone": "bone", "attachment": "weiba1"}, {"name": "jio3", "bone": "bone", "attachment": "jio3"}, {"name": "jio4", "bone": "bone", "attachment": "jio4"}, {"name": "bd", "bone": "bone", "attachment": "bd"}, {"name": "bomao1", "bone": "bone", "attachment": "bomao1"}, {"name": "jio1", "bone": "bone56", "attachment": "jio1"}, {"name": "jio2", "bone": "bone", "attachment": "jio2"}, {"name": "bomao", "bone": "bone", "attachment": "bomao"}, {"name": "tou", "bone": "bone", "attachment": "tou"}, {"name": "<PERSON><PERSON>", "bone": "bone7", "attachment": "<PERSON><PERSON>"}, {"name": "biyan", "bone": "bone7"}, {"name": "yanj", "bone": "bone7", "attachment": "yanj"}, {"name": "meimao", "bone": "bone7", "attachment": "meimao"}, {"name": "toufa1", "bone": "bone", "attachment": "toufa1"}, {"name": "erduo3", "bone": "bone8", "attachment": "erduo3"}, {"name": "erduo2", "bone": "bone11", "attachment": "erduo2"}, {"name": "erduo1", "bone": "bone", "attachment": "erduo1"}, {"name": "tq3", "bone": "bone42", "attachment": "tq3"}, {"name": "f1", "bone": "bone", "attachment": "f1"}, {"name": "tq1", "bone": "bone", "attachment": "tq1"}, {"name": "sd", "bone": "bone"}], "ik": [{"name": "target1", "bones": ["bone23", "bone24"], "target": "target1", "bendPositive": false, "stretch": true}, {"name": "target2", "order": 7, "bones": ["bone24", "bone25"], "target": "target2", "bendPositive": false}, {"name": "target3", "order": 1, "bones": ["bone20", "bone21"], "target": "target3", "bendPositive": false, "stretch": true}, {"name": "target4", "order": 6, "bones": ["bone21", "bone22"], "target": "target4", "bendPositive": false}, {"name": "target5", "order": 2, "bones": ["bone33", "bone34"], "target": "target5", "stretch": true}, {"name": "target6", "order": 5, "bones": ["bone34", "bone35"], "target": "target6", "bendPositive": false}, {"name": "target7", "order": 3, "bones": ["bone29", "bone30"], "target": "target7", "stretch": true}, {"name": "target8", "order": 4, "bones": ["bone30", "bone31"], "target": "target8", "bendPositive": false}], "skins": [{"name": "default", "attachments": {"bd": {"bd": {"type": "mesh", "uvs": [0.06375, 0.16154, 0.17087, 0.07983, 0.31013, 0.02327, 0.43546, 0, 0.5715, 0, 0.71397, 0, 0.81737, 0.07021, 0.89497, 0.1994, 0.94005, 0.36345, 1, 0.45368, 1, 0.54391, 0.95683, 0.63003, 0.9044, 0.63003, 0.82576, 0.76538, 0.73454, 0.87201, 0.615, 0.96019, 0.45877, 1, 0.31198, 0.97864, 0.18196, 0.92533, 0.06032, 0.78998, 0, 0.61568, 0, 0.43522, 0.02782, 0.28758, 0.15419, 0.34323, 0.30942, 0.3217, 0.44703, 0.29802, 0.58244, 0.2851, 0.72885, 0.31309, 0.86316, 0.42289, 0.73436, 0.58437, 0.60225, 0.67695, 0.44042, 0.70494, 0.29731, 0.67264, 0.11676, 0.58652], "triangles": [24, 1, 2, 23, 0, 1, 23, 1, 24, 22, 0, 23, 23, 21, 22, 33, 21, 23, 20, 21, 33, 32, 23, 24, 33, 23, 32, 19, 20, 33, 18, 33, 32, 19, 33, 18, 18, 32, 17, 25, 3, 4, 25, 4, 26, 2, 3, 25, 24, 2, 25, 31, 24, 25, 32, 24, 31, 31, 25, 30, 31, 30, 15, 17, 32, 31, 16, 31, 15, 17, 31, 16, 26, 4, 5, 27, 5, 6, 27, 6, 7, 26, 5, 27, 28, 27, 7, 28, 7, 8, 8, 9, 10, 29, 27, 28, 12, 28, 8, 11, 12, 8, 29, 28, 12, 10, 11, 8, 29, 30, 26, 29, 26, 27, 25, 26, 30, 13, 29, 12, 14, 29, 13, 30, 29, 14, 15, 30, 14], "vertices": [2, 4, 99.03, 6.29, 0.04018, 5, 43.62, 27.14, 0.95982, 3, 3, 117.13, -68.19, 0.00708, 4, 81.21, -24.44, 0.00355, 5, 40.36, -8.24, 0.98937, 4, 6, -76.64, 87.12, 0.01944, 3, 74.41, -78.48, 0.21446, 4, 53.14, -58.26, 0.20013, 5, 29.11, -50.73, 0.56597, 4, 6, -37.75, 87.73, 0.15288, 3, 35.82, -83.38, 0.46255, 4, 25.16, -85.29, 0.15659, 5, 15.08, -87.01, 0.22798, 4, 6, 4.16, 84.41, 0.51542, 3, -6.2, -84.71, 0.37798, 4, -7.7, -111.5, 0.04137, 5, -3.72, -124.61, 0.06524, 4, 6, 48.04, 80.93, 0.86047, 3, -50.2, -86.11, 0.12522, 4, -42.11, -138.96, 0.00286, 5, -23.41, -163.98, 0.01145, 3, 6, 79.02, 67.35, 0.96586, 3, -82.49, -76.03, 0.03252, 5, -47.62, -187.6, 0.00162, 2, 6, 101.31, 45.11, 0.99697, 3, -107.1, -56.38, 0.00303, 1, 6, 113.15, 18.17, 1, 1, 6, 130.49, 2.5, 1, 1, 6, 129.37, -11.71, 1, 1, 6, 114.99, -24.23, 1, 1, 6, 98.84, -22.95, 1, 2, 6, 72.93, -42.35, 0.99916, 3, -88.55, 33.67, 0.00084, 2, 6, 43.5, -56.92, 0.93743, 3, -60.91, 51.4, 0.06257, 2, 6, 5.58, -67.89, 0.61677, 3, -24.43, 66.5, 0.38323, 3, 6, -43.04, -70.35, 0.14165, 3, 23.62, 74.31, 0.81722, 4, -79, 33.73, 0.04114, 3, 6, -87.99, -63.4, 0.00582, 3, 69.06, 72.37, 0.64693, 4, -41.44, 59.38, 0.34725, 3, 3, 109.49, 65.22, 0.26134, 4, -4.78, 77.85, 0.73688, 5, -80.66, 48.44, 0.00178, 3, 3, 147.73, 45.04, 0.04524, 4, 37.94, 84.57, 0.87089, 5, -44.72, 72.49, 0.08387, 3, 3, 167.23, 18.1, 0.00274, 4, 69.68, 74.67, 0.7438, 5, -11.75, 76.84, 0.25347, 2, 4, 87.46, 52.38, 0.5098, 5, 13.75, 64.09, 0.4902, 2, 4, 95.29, 28.78, 0.22622, 5, 30.77, 45.97, 0.77378, 2, 4, 59.28, 11.3, 0.25909, 5, 5.44, 14.98, 0.74091, 4, 6, -80.59, 40.13, 0.00204, 3, 73.14, -31.35, 0.16327, 4, 23.91, -21.27, 0.6606, 5, -12.97, -29.44, 0.17409, 4, 6, -37.9, 40.51, 0.08822, 3, 30.75, -36.43, 0.69288, 4, -7, -50.71, 0.12979, 5, -28.63, -69.15, 0.08912, 4, 6, 3.97, 39.24, 0.58107, 3, -11, -39.8, 0.37582, 4, -38.43, -78.4, 0.01685, 5, -45.52, -107.49, 0.02627, 4, 6, 48.72, 31.26, 0.96035, 3, -56.36, -36.81, 0.03724, 4, -76.56, -103.16, 9e-05, 5, -69.71, -145.98, 0.00232, 1, 6, 88.72, 10.69, 1, 1, 6, 47.03, -11.61, 1, 2, 6, 5.19, -22.96, 0.76164, 3, -19.08, 21.89, 0.23836, 3, 6, -45.01, -23.42, 0.03611, 3, 30.76, 27.89, 0.95122, 4, -45.5, 0.82, 0.01267, 2, 3, 75.12, 24.19, 0.47146, 4, -7.74, 24.41, 0.52854, 3, 3, 131.31, 12.36, 0.01623, 4, 44.35, 48.57, 0.83784, 5, -23.77, 42.51, 0.14593], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66], "width": 309, "height": 158}}, "biyan": {"biyan": {"type": "mesh", "uvs": [1, 1, 0.8, 1, 0.6, 1, 0.4, 1, 0.2, 1, 0, 1, 0, 0, 0.2, 0, 0.4, 0, 0.6, 0, 0.8, 0, 1, 0], "triangles": [5, 6, 7, 2, 9, 10, 4, 7, 8, 3, 4, 8, 3, 8, 9, 2, 3, 9, 4, 5, 7, 1, 2, 10, 1, 10, 11, 0, 1, 11], "vertices": [2, 5, 25.61, -83.83, 0.544, 7, 4.47, -101.12, 0.456, 2, 5, 40.55, -53.96, 0.544, 7, 5.06, -67.73, 0.456, 2, 5, 55.49, -24.08, 0.44444, 7, 5.64, -34.33, 0.55556, 2, 5, 70.43, 5.79, 0.22222, 7, 6.22, -0.94, 0.77778, 2, 5, 85.36, 35.67, 0.44444, 7, 6.8, 32.46, 0.55556, 2, 5, 100.3, 65.54, 0.472, 7, 7.38, 65.85, 0.528, 2, 5, 137.87, 46.76, 0.456, 7, 49.38, 65.12, 0.544, 2, 5, 122.93, 16.88, 0.44444, 7, 48.79, 31.73, 0.55556, 2, 5, 107.99, -12.99, 0.22222, 7, 48.21, -1.67, 0.77778, 2, 5, 93.05, -42.87, 0.44444, 7, 47.63, -35.06, 0.55556, 2, 5, 78.12, -72.74, 0.544, 7, 47.05, -68.46, 0.456, 2, 5, 63.18, -102.61, 0.544, 7, 46.47, -101.85, 0.456], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 0], "width": 167, "height": 42}}, "bomao": {"bomao": {"type": "mesh", "uvs": [0, 0.78282, 0.04336, 0.54175, 0.17018, 0.39889, 0.37187, 0.32449, 0.5494, 0.23818, 0.70158, 0.09234, 0.8151, 0, 0.92984, 8e-05, 1, 0.20841, 1, 0.53579, 0.92863, 0.66377, 0.80544, 0.80067, 0.6738, 0.91377, 0.51317, 1, 0.34167, 0.97925, 0.18225, 0.90782, 0.0651, 0.88698, 0.10436, 0.71112, 0.24546, 0.66283, 0.41595, 0.63385, 0.58253, 0.58073, 0.74911, 0.48415, 0.90588, 0.35858], "triangles": [20, 4, 21, 19, 3, 4, 18, 3, 19, 19, 4, 20, 18, 2, 3, 0, 1, 17, 17, 1, 2, 4, 5, 21, 21, 5, 6, 22, 7, 8, 22, 6, 7, 14, 19, 13, 13, 20, 12, 13, 19, 20, 15, 18, 14, 14, 18, 19, 12, 21, 11, 12, 20, 21, 16, 17, 15, 15, 17, 18, 16, 0, 17, 11, 21, 10, 21, 22, 10, 10, 22, 9, 22, 8, 9, 21, 6, 22, 17, 2, 18], "vertices": [2, 5, 17.78, 76.61, 0.856, 7, -71.83, 40.23, 0.144, 1, 5, 31.88, 59.53, 1, 1, 5, 30.87, 30.68, 1, 2, 5, 17.79, -9.46, 0.544, 7, -34.68, -37.41, 0.456, 2, 5, 7.84, -45.57, 0.544, 7, -28.07, -74.28, 0.456, 2, 5, 4.71, -79.22, 0.656, 7, -16.37, -105.98, 0.344, 2, 5, 1.14, -103.71, 0.656, 7, -9.02, -129.62, 0.344, 2, 5, -9.49, -124.95, 0.76, 7, -9.44, -153.36, 0.24, 2, 5, -31.63, -130.11, 0.76, 7, -27.19, -167.58, 0.24, 2, 4, -52.49, -83.27, 0.76, 7, -54.69, -167.1, 0.24, 2, 4, -47.65, -65.65, 0.656, 7, -65.18, -152.14, 0.344, 2, 4, -34.88, -40.76, 0.656, 7, -76.24, -126.44, 0.344, 2, 4, -19.51, -16.34, 0.544, 7, -85.26, -99.03, 0.456, 2, 4, 1.97, 10.06, 0.544, 7, -91.92, -65.66, 0.456, 2, 4, 30.81, 30.83, 0.544, 7, -89.56, -30.2, 0.456, 2, 4, 60.34, 46.72, 0.656, 7, -82.99, 2.69, 0.344, 2, 4, 80.39, 60.48, 0.656, 7, -80.82, 26.91, 0.344, 2, 4, 83.25, 43.86, 0.656, 7, -66.19, 18.53, 0.344, 2, 4, 62.95, 22.48, 0.544, 7, -62.64, -10.75, 0.456, 2, 4, 36.88, -1.43, 0.544, 7, -60.82, -46.08, 0.456, 2, 4, 12.71, -26.43, 0.544, 7, -56.96, -80.63, 0.456, 2, 4, -9.19, -54.27, 0.656, 7, -49.45, -115.25, 0.344, 2, 4, -27.98, -82.76, 0.656, 7, -39.47, -147.88, 0.344], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32, 0, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44], "width": 207, "height": 84}}, "bomao1": {"bomao1": {"type": "mesh", "uvs": [0.2233, 0, 0.36072, 0.08719, 0.52717, 0.15919, 0.68007, 0.26119, 0.83104, 0.39119, 0.90265, 0.58119, 1, 0.62919, 1, 0.79918, 0.94136, 0.90118, 0.80201, 0.99718, 0.56588, 1, 0.36459, 0.96318, 0.15749, 0.91518, 0, 0.86718, 0, 0.64719, 0, 0.39719, 0.02975, 0.19319, 0.13814, 0.04119, 0.23255, 0.21837, 0.32761, 0.53854, 0.45436, 0.735, 0.64449, 0.89872, 0.3769, 0.2111, 0.5459, 0.39301, 0.70082, 0.59311, 0.80293, 0.77139, 0.15509, 0.37482, 0.13748, 0.60039, 0.2748, 0.79685], "triangles": [28, 19, 20, 22, 1, 2, 18, 0, 1, 18, 1, 22, 17, 0, 18, 16, 17, 18, 26, 16, 18, 23, 2, 3, 22, 2, 23, 15, 16, 26, 18, 19, 26, 19, 22, 23, 22, 19, 18, 27, 15, 26, 27, 26, 19, 14, 15, 27, 4, 23, 3, 20, 19, 23, 28, 27, 19, 13, 14, 27, 24, 4, 5, 24, 23, 4, 20, 23, 24, 25, 24, 5, 25, 5, 6, 7, 25, 6, 12, 13, 27, 21, 20, 24, 21, 24, 25, 8, 25, 7, 28, 12, 27, 11, 28, 20, 10, 11, 20, 12, 28, 11, 9, 21, 25, 9, 25, 8, 21, 10, 20, 10, 21, 9], "vertices": [1, 5, 31.09, 25.5, 1, 1, 5, 18.35, 17.58, 1, 2, 4, 55.95, 3.65, 0.11111, 5, 5.63, 6.63, 0.88889, 2, 4, 39.11, 1.96, 0.33333, 5, -8.94, -1.98, 0.66667, 2, 4, 20.83, 2.35, 0.66667, 5, -25.68, -9.31, 0.33333, 2, 4, 4.96, 11.56, 0.88889, 5, -43.95, -7.62, 0.11111, 1, 4, -4.81, 9.29, 1, 1, 4, -14.35, 21.25, 1, 1, 4, -15.81, 31.83, 1, 1, 4, -11.07, 46.67, 1, 1, 4, 5.94, 60.56, 1, 1, 4, 22.64, 69.64, 1, 2, 4, 40.39, 78.28, 0.88889, 5, -39.85, 67.81, 0.11111, 2, 4, 54.54, 84.03, 0.66667, 5, -29.43, 78.98, 0.33333, 2, 4, 66.88, 68.56, 0.33333, 5, -11.72, 70.12, 0.66667, 2, 4, 80.91, 50.97, 0.11111, 5, 8.4, 60.06, 0.88889, 1, 5, 23.58, 49.37, 1, 1, 5, 31.31, 34.24, 1, 2, 4, 74.04, 24.9, 0.11111, 5, 13.12, 33.52, 0.88889, 2, 4, 49.16, 41.91, 0.33333, 5, -16.6, 38.5, 0.66667, 2, 4, 28.92, 48.38, 0.66667, 5, -37.69, 35.86, 0.33333, 2, 4, 5.91, 48.87, 0.83333, 5, -58.78, 26.64, 0.16667, 2, 4, 63.96, 16.02, 0.16667, 5, 7.7, 21.22, 0.83333, 2, 4, 41.46, 19.01, 0.33333, 5, -13.97, 14.48, 0.66667, 2, 4, 18.97, 24.11, 0.66667, 5, -36.52, 9.65, 0.33333, 2, 4, 1.54, 30.73, 0.83333, 5, -55.12, 8.33, 0.16667, 2, 4, 70.89, 40.4, 0.16667, 5, 3.75, 46.26, 0.83333, 2, 4, 59.51, 57.29, 0.27778, 5, -13.68, 56.8, 0.72222, 2, 4, 38.5, 63.15, 0.41667, 5, -35.2, 53.29, 0.58333], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 0, 36, 36, 38, 38, 40, 40, 42, 44, 46, 46, 48, 48, 50, 52, 54, 54, 56], "width": 93, "height": 90}}, "erduo1": {"erduo1": {"type": "mesh", "uvs": [0, 0.09452, 0.1233, 0.02521, 0.32684, 0, 0.49499, 0, 0.68525, 0.09699, 0.8733, 0.27768, 0.96622, 0.51036, 1, 0.7257, 0.99056, 0.84947, 0.90206, 0.93115, 0.73614, 0.9361, 0.50826, 0.91877, 0.32463, 0.79749, 0.20738, 0.56729, 0.11224, 0.34452, 0.01711, 0.18363, 0.15607, 0.14626, 0.39417, 0.21521, 0.60145, 0.40324, 0.79753, 0.62888, 0.85635, 0.78245, 0.65187, 0.76364, 0.4726, 0.59441, 0.29613, 0.38444], "triangles": [23, 14, 16, 14, 15, 16, 15, 0, 16, 0, 1, 16, 19, 18, 5, 13, 23, 22, 22, 23, 18, 13, 14, 23, 23, 17, 18, 18, 17, 4, 18, 4, 5, 4, 17, 3, 17, 23, 16, 16, 2, 17, 17, 2, 3, 16, 1, 2, 11, 21, 10, 10, 20, 9, 10, 21, 20, 9, 20, 8, 21, 11, 22, 8, 20, 7, 11, 12, 22, 12, 13, 22, 21, 19, 20, 20, 19, 7, 19, 21, 18, 19, 6, 7, 21, 22, 18, 19, 5, 6], "vertices": [2, 12, -26.84, 7.61, 0.03763, 11, 13.54, 26.23, 0.96237, 2, 12, -16.81, 19.55, 0.32782, 11, 29.09, 25.06, 0.67218, 2, 12, 3.3, 30.99, 0.93437, 11, 50.12, 15.41, 0.06563, 1, 12, 20.75, 38.51, 1, 2, 13, -22.56, 31.72, 0.00384, 12, 44.37, 38.02, 0.99616, 2, 13, 4.1, 40.33, 0.28429, 12, 71.11, 29.67, 0.71571, 2, 13, 29.63, 37.03, 0.75335, 12, 90.05, 12.24, 0.24665, 2, 13, 50.17, 28.93, 0.96009, 12, 102.16, -6.22, 0.03991, 2, 13, 60.28, 21.5, 0.99224, 12, 106.13, -18.13, 0.00776, 2, 13, 62.1, 8.66, 0.99985, 12, 100.21, -29.66, 0.00015, 1, 13, 52.74, -7.59, 1, 3, 13, 37.81, -28.65, 0.9742, 12, 58.85, -46.12, 0.02578, 11, 19.96, -74.7, 2e-05, 3, 13, 16.54, -39.95, 0.75825, 12, 34.94, -43.08, 0.23057, 11, 8.47, -53.52, 0.01118, 3, 13, -10.21, -39.13, 0.23275, 12, 13.58, -26.97, 0.62689, 11, 9.07, -26.77, 0.14036, 3, 13, -35.01, -36.56, 0.00183, 12, -5.2, -10.56, 0.05072, 11, 11.43, -1.95, 0.94745, 2, 12, -21.5, 0.11, 0.00761, 11, 10.57, 17.52, 0.99239, 2, 12, -8.58, 9.79, 0.33353, 11, 25.98, 12.68, 0.66647, 1, 12, 18.89, 14.04, 1, 2, 13, -1.11, 7.51, 0.07013, 12, 47.91, 5.86, 0.92987, 2, 13, 29.89, 14.52, 0.91579, 12, 77.28, -6.3, 0.08421, 2, 13, 46.59, 12.09, 0.98836, 12, 89.52, -17.91, 0.01164, 1, 13, 32.92, -6.63, 1, 3, 13, 7.76, -14.99, 0.77174, 12, 42.18, -17.63, 0.22445, 11, 33.36, -44.54, 0.0038, 3, 13, -20.73, -20.93, 0.02544, 12, 15.48, -6.04, 0.92164, 11, 27.17, -16.09, 0.05292], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 42, 44, 44, 46], "width": 113, "height": 101}}, "erduo2": {"erduo2": {"x": 9.02, "y": -6.57, "rotation": -30.96, "width": 72, "height": 62}}, "erduo3": {"erduo3": {"type": "mesh", "uvs": [0.12054, 0.25874, 0.24294, 0.14692, 0.34913, 0.03045, 0.53453, 0, 0.71993, 0, 0.81353, 0.01415, 0.91073, 0.11897, 0.97553, 0.20982, 1, 0.34492, 0.92693, 0.43344, 0.82793, 0.53594, 0.77034, 0.66172, 0.66953, 0.80848, 0.52913, 0.92961, 0.36534, 1, 0.16374, 0.98085, 0.02874, 0.90631, 0, 0.74558, 0, 0.5406, 0.07914, 0.37987, 0.13715, 0.75606, 0.28777, 0.54419, 0.43185, 0.39587, 0.61194, 0.2857, 0.76584, 0.23485, 0.92628, 0.30265], "triangles": [21, 13, 14, 16, 20, 15, 14, 15, 20, 14, 20, 21, 16, 17, 20, 13, 21, 22, 21, 20, 18, 21, 18, 19, 18, 20, 17, 19, 0, 21, 0, 1, 21, 21, 1, 22, 1, 2, 22, 12, 13, 22, 12, 22, 11, 11, 22, 23, 11, 23, 10, 22, 3, 23, 22, 2, 3, 23, 3, 4, 10, 23, 24, 9, 24, 25, 9, 10, 24, 9, 25, 8, 25, 7, 8, 24, 6, 25, 25, 6, 7, 23, 4, 24, 24, 5, 6, 24, 4, 5], "vertices": [1, 10, 11.75, -31.17, 1, 2, 9, 43.42, -27.86, 0.07326, 10, -4.38, -27.81, 0.92674, 2, 9, 28.65, -31.91, 0.36038, 10, -19.59, -26.02, 0.63962, 3, 8, 49.29, -6.83, 0.034, 9, 9.07, -25.65, 0.76794, 10, -35.39, -12.87, 0.19807, 3, 8, 31.75, -17.23, 0.72457, 9, -9.42, -17.04, 0.26282, 10, -49.3, 2.05, 0.01261, 3, 8, 22.28, -21.45, 0.98106, 9, -18.25, -11.61, 0.01863, 10, -55.44, 10.4, 0.00031, 1, 8, 8.54, -19.24, 1, 1, 8, -1.53, -16.24, 1, 1, 8, -9.71, -7.73, 1, 2, 8, -6.63, 2.84, 0.98848, 9, -14.52, 25.97, 0.01152, 3, 8, -1.71, 15.89, 0.71668, 9, -0.97, 29.27, 0.28082, 10, -24.09, 41.81, 0.00249, 3, 8, -1.72, 28.32, 0.40922, 9, 9.28, 36.29, 0.55198, 10, -11.95, 44.46, 0.0388, 3, 8, 1.45, 44.7, 0.18101, 9, 24.6, 42.92, 0.62274, 10, 4.74, 44.86, 0.19625, 3, 8, 9.48, 61.44, 0.06013, 9, 42.94, 45.74, 0.43375, 10, 22.8, 40.59, 0.50612, 3, 8, 21.93, 75.77, 0.01224, 9, 61.8, 43.56, 0.1822, 10, 39.46, 31.49, 0.80556, 3, 8, 41.83, 85.69, 8e-05, 9, 81.22, 32.73, 0.01884, 10, 53.4, 14.17, 0.98108, 2, 9, 92.01, 20.72, 0, 10, 58.89, -1.01, 1, 1, 10, 51.06, -12.64, 1, 1, 10, 38.32, -24.53, 1, 1, 10, 22.39, -27.48, 1, 1, 10, 41.42, -1, 1, 1, 10, 16.95, -1.17, 1, 2, 8, 41.83, 27.88, 6e-05, 9, 33.5, 0.09, 0.99994, 2, 9, 11.59, -0.04, 0.99994, 10, -23.44, 9.92, 6e-05, 1, 8, 17.22, -2.64, 1, 1, 8, -0.9, -6.69, 1], "hull": 20, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 0, 38, 32, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50], "width": 110, "height": 85}}, "f1": {"f1": {"type": "mesh", "uvs": [0, 0.09283, 0.18635, 0.04348, 0.46997, 0, 0.49104, 0.20034, 0.56884, 0.41889, 0.68877, 0.62863, 0.843, 0.77965, 1, 0.90915, 0.99426, 0.9459, 0.69656, 0.98265, 0.38116, 1, 0.22185, 0.86715, 0.10438, 0.6414, 0.04162, 0.49265, 0, 0.26515, 0.23697, 0.24787, 0.32479, 0.45479, 0.43896, 0.6458, 0.56483, 0.82407], "triangles": [10, 18, 9, 18, 10, 17, 9, 6, 8, 9, 18, 6, 8, 6, 7, 18, 5, 6, 18, 17, 5, 10, 11, 17, 11, 12, 17, 12, 16, 17, 17, 4, 5, 17, 16, 4, 12, 13, 16, 16, 3, 4, 13, 15, 16, 13, 14, 15, 16, 15, 3, 15, 0, 1, 15, 14, 0, 3, 15, 2, 15, 1, 2], "vertices": [1, 26, -10.56, -18.19, 1, 1, 26, -8.79, -1.6, 1, 2, 26, -3.72, 22.8, 0.99374, 27, -24.36, 26.81, 0.00626, 2, 26, 11.99, 19.1, 0.81646, 27, -9.4, 20.77, 0.18354, 3, 26, 30.73, 19.55, 0.10933, 27, 9.19, 18.37, 0.84751, 28, -12.54, 19.85, 0.04316, 2, 27, 28.92, 19.53, 0.26683, 28, 7.2, 18.86, 0.73317, 2, 27, 45.94, 25.55, 0.00347, 28, 24.77, 23, 0.99653, 1, 28, 41.07, 28.33, 1, 1, 28, 43.2, 26.24, 1, 1, 28, 30.86, 3.28, 1, 2, 27, 42.25, -18.17, 0.18493, 28, 16.36, -20.06, 0.81507, 3, 26, 54.26, -20.99, 0.00075, 27, 26.3, -25.27, 0.65912, 28, -0.27, -25.4, 0.34013, 3, 26, 33.81, -24.49, 0.20448, 27, 5.55, -25.63, 0.78511, 28, -20.93, -23.5, 0.01041, 2, 26, 20.76, -25.61, 0.59979, 27, -7.51, -24.75, 0.40021, 2, 26, 2.41, -22.86, 0.96751, 27, -25.24, -19.25, 0.03249, 1, 26, 8.09, -2.99, 1, 2, 26, 26.25, -1.4, 0.02841, 27, 1.58, -1.66, 0.97159, 2, 27, 19.75, -0.22, 0.99925, 28, -4.06, 0.22, 0.00075, 1, 28, 13.9, 1.09, 1], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28, 2, 30, 30, 32, 32, 34, 34, 36], "width": 87, "height": 80}}, "hongsheng": {"hongsheng": {"type": "mesh", "uvs": [0.18848, 0.10763, 0.20848, 0.01446, 0.33448, 0.02293, 0.51848, 0.14152, 0.67648, 0.2961, 0.82848, 0.4634, 0.98848, 0.65822, 1, 0.82551, 1, 1, 0.87048, 0.9801, 0.68848, 0.83187, 0.55048, 0.66669, 0.41048, 0.51846, 0.24248, 0.37234, 0.10448, 0.19446, 0.32218, 0.20427, 0.48388, 0.32626, 0.62133, 0.46108, 0.77494, 0.61732, 0.87802, 0.78425, 0.93866, 0.90837], "triangles": [18, 17, 5, 18, 5, 6, 11, 17, 18, 19, 18, 6, 19, 6, 7, 10, 11, 18, 10, 18, 19, 20, 19, 7, 9, 10, 19, 9, 19, 20, 20, 7, 8, 9, 20, 8, 2, 0, 1, 15, 2, 3, 15, 0, 2, 16, 15, 3, 16, 3, 4, 15, 14, 0, 13, 15, 16, 13, 14, 15, 17, 16, 4, 17, 4, 5, 12, 13, 16, 12, 16, 17, 11, 12, 17], "vertices": [1, 48, -7.51, -1.05, 1, 1, 48, -10.48, 4.73, 1, 1, 48, -3.16, 10.12, 1, 1, 48, 12.18, 12.45, 1, 2, 48, 27.65, 11.71, 0.95874, 49, -10.22, 9.57, 0.04126, 2, 48, 43.34, 10.02, 0.06733, 49, 5.44, 11.63, 0.93267, 1, 49, 22.94, 13.03, 1, 1, 49, 32.58, 6.94, 1, 1, 49, 42.13, -0.1, 1, 1, 49, 35.5, -6.8, 1, 2, 48, 51.72, -15.65, 0.00322, 49, 19.61, -11.36, 0.99678, 2, 48, 36.89, -13.43, 0.4206, 49, 4.67, -12.69, 0.5794, 2, 48, 22.69, -12.19, 0.99465, 49, -9.42, -14.81, 0.00535, 1, 48, 7.04, -12.35, 1, 1, 48, -8.35, -9.46, 1, 1, 48, 4.09, 0.1, 1, 1, 48, 18.34, 1.22, 1, 1, 48, 31.82, 0.56, 1, 1, 49, 11.57, 2.31, 1, 1, 49, 25.11, 1.54, 1, 1, 49, 34.49, 0.04, 1], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28, 0, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40], "width": 72, "height": 68}}, "jio1": {"jio1": {"type": "mesh", "uvs": [0.20392, 0.05492, 0.39044, 0, 0.60697, 0.02326, 0.69028, 0.09227, 0.79135, 0.17599, 0.92856, 0.31381, 1, 0.41625, 1, 0.56711, 0.98644, 0.71425, 0.93928, 0.84835, 0.85567, 0.97127, 0.7399, 1, 0.52765, 1, 0.34542, 0.96941, 0.31686, 0.92655, 0.29826, 0.89864, 0.37115, 0.79806, 0.43356, 0.7588, 0.49549, 0.71984, 0.4419, 0.70121, 0.29611, 0.67327, 0.1246, 0.58015, 0.02169, 0.44232, 0, 0.29519, 0.02598, 0.13315, 0.11174, 0.0959, 0.367, 0.17146, 0.562, 0.27988, 0.7531, 0.44251, 0.8467, 0.60175, 0.8428, 0.6729, 0.7687, 0.80843, 0.6595, 0.87958, 0.4645, 0.91007, 0.14634, 0.2484, 0.21313, 0.36444, 0.34422, 0.46328, 0.52231, 0.59221, 0.65588, 0.63949, 0.57207, 0.80125, 0.6044, 0.72407, 0.70212, 0.71168], "triangles": [31, 32, 39, 39, 33, 17, 33, 39, 32, 16, 17, 33, 14, 15, 16, 33, 14, 16, 13, 14, 33, 11, 32, 31, 12, 33, 32, 13, 33, 12, 12, 32, 11, 39, 18, 40, 31, 41, 30, 9, 30, 8, 31, 30, 9, 41, 39, 40, 31, 39, 41, 39, 17, 18, 10, 31, 9, 10, 11, 31, 28, 27, 4, 28, 4, 5, 28, 5, 6, 29, 28, 6, 37, 27, 28, 7, 29, 6, 38, 37, 28, 29, 38, 28, 30, 38, 29, 20, 36, 37, 19, 20, 37, 41, 38, 30, 8, 29, 7, 30, 29, 8, 18, 19, 37, 40, 18, 37, 38, 40, 37, 41, 40, 38, 26, 0, 1, 26, 1, 2, 34, 25, 0, 34, 0, 26, 24, 25, 34, 27, 26, 2, 27, 2, 3, 27, 3, 4, 23, 24, 34, 35, 34, 26, 35, 22, 23, 35, 23, 34, 36, 35, 26, 36, 26, 27, 21, 22, 35, 21, 35, 36, 37, 36, 27, 20, 21, 36], "vertices": [2, 29, -26.3, 4.21, 0.99954, 30, -54.65, 61.33, 0.00046, 2, 29, -22.99, 31.39, 0.96634, 30, -32.97, 78.04, 0.03366, 2, 29, -6.6, 56.91, 0.84686, 30, -3.29, 84.26, 0.15314, 2, 29, 8.36, 62.56, 0.73941, 30, 11.25, 77.57, 0.26059, 2, 29, 26.52, 69.42, 0.53139, 30, 28.88, 69.45, 0.46861, 2, 29, 54.66, 77.06, 0.21704, 30, 54.06, 54.76, 0.78296, 2, 29, 73.73, 78.92, 0.09712, 30, 68.77, 42.47, 0.90288, 2, 29, 95.49, 68.46, 0.01689, 30, 76.58, 19.63, 0.98311, 2, 30, 82.43, -3.25, 0.46843, 31, -2.75, 16.87, 0.53157, 3, 30, 83.17, -25.67, 0.00023, 31, 18.9, 22.74, 0.98816, 32, -25.09, 8.21, 0.01161, 2, 31, 41.74, 23.37, 0.57395, 32, -8.37, 23.77, 0.42605, 2, 31, 54.2, 12.2, 0.14926, 32, 8.37, 23.62, 0.85074, 1, 32, 36.66, 15.25, 1, 2, 30, 11.35, -70.74, 0.008, 32, 59.56, 3.37, 0.992, 1, 32, 61.42, -4.33, 1, 1, 32, 62.64, -9.35, 1, 2, 31, 54.13, -48.39, 0.16913, 32, 48.36, -21.9, 0.83087, 2, 31, 44.19, -44.39, 0.24208, 32, 38.25, -25.47, 0.75792, 1, 31, 34.33, -40.42, 1, 4, 29, 81.23, -10.75, 0.04408, 30, 10.14, -25.79, 0.79989, 31, 35.78, -48.32, 0.00211, 32, 34.53, -33.97, 0.15392, 3, 29, 68.43, -27.08, 0.45665, 30, -10.48, -28.13, 0.53134, 32, 52.69, -44.01, 0.012, 2, 29, 44.67, -42.12, 0.91663, 30, -37.86, -21.75, 0.08337, 2, 29, 18.6, -45.46, 0.99882, 30, -58.54, -5.52, 0.00118, 1, 29, -3.92, -37.98, 1, 1, 29, -25.73, -23.5, 1, 1, 29, -25.94, -10.18, 1, 2, 29, 0.32, 16.57, 0.97522, 30, -27.16, 51.03, 0.02478, 2, 29, 27.7, 33.49, 0.72033, 30, 4.1, 43.39, 0.27967, 2, 29, 62.66, 46.16, 0.15944, 30, 37.66, 27.38, 0.84056, 2, 29, 91.25, 46.86, 0.00607, 30, 58.22, 7.49, 0.99393, 3, 30, 61.39, -3.46, 0.44232, 31, 2.28, -3.56, 0.55763, 32, -20.19, -22.51, 5e-05, 3, 30, 58.67, -27.31, 0.00299, 31, 26.12, -0.73, 0.98905, 32, -4.16, -4.64, 0.00796, 1, 32, 13.62, 1.97, 1, 2, 30, 23.93, -56.39, 0.00182, 32, 41, -1.04, 0.99818, 1, 29, -1.86, -16.41, 1, 1, 29, 18.89, -16.08, 1, 2, 29, 41.04, -6.5, 0.99785, 30, -15.04, 5.83, 0.00215, 3, 30, 15.06, -5.67, 0.97251, 31, 15.06, -48.14, 0.00064, 32, 18.87, -47.53, 0.02686, 3, 30, 35.08, -6.82, 0.90243, 31, 11.58, -28.4, 0.04105, 32, 3.21, -35.01, 0.05652, 3, 30, 33.21, -35.83, 6e-05, 31, 39.69, -24.48, 0.49626, 32, 21.72, -13.49, 0.50368, 3, 30, 33.11, -22.41, 0.4013, 31, 26.59, -27.35, 0.32634, 32, 13.78, -24.31, 0.27237, 3, 30, 45.16, -15.77, 0.51715, 31, 17.6, -16.94, 0.44271, 32, 0.15, -22.43, 0.04014], "hull": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 0, 50, 0, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 26, 28, 28, 30, 66, 28, 68, 70, 70, 72, 72, 74, 74, 76, 80, 82], "width": 139, "height": 160}}, "jio2": {"jio2": {"type": "mesh", "uvs": [0.90608, 0, 0.9917, 0.09855, 1, 0.24719, 1, 0.39875, 0.91679, 0.52845, 0.83474, 0.65087, 0.74378, 0.73394, 0.64925, 0.8374, 0.58147, 0.92484, 0.52083, 1, 0.36209, 1, 0.1766, 0.98605, 0.02143, 0.94233, 0, 0.88258, 0.0464, 0.80097, 0.11596, 0.76454, 0.19087, 0.66544, 0.26399, 0.56197, 0.30502, 0.45267, 0.29967, 0.31569, 0.34426, 0.14955, 0.49764, 0.02568, 0.68849, 0, 0.74365, 0.14835, 0.62045, 0.31307, 0.54653, 0.45766, 0.48829, 0.6059, 0.40093, 0.76696, 0.25086, 0.88226, 0.11198, 0.88592, 0.42109, 0.90972], "triangles": [10, 30, 9, 9, 30, 8, 11, 28, 10, 10, 28, 30, 12, 29, 11, 11, 29, 28, 12, 13, 29, 28, 27, 30, 13, 14, 29, 29, 15, 28, 29, 14, 15, 15, 16, 28, 28, 16, 27, 7, 8, 27, 8, 30, 27, 6, 7, 26, 16, 17, 27, 7, 27, 26, 27, 17, 26, 6, 26, 5, 26, 25, 5, 5, 25, 4, 17, 18, 26, 26, 18, 25, 25, 24, 4, 4, 24, 3, 24, 25, 19, 25, 18, 19, 3, 24, 2, 24, 23, 2, 19, 20, 24, 20, 21, 24, 23, 21, 22, 23, 24, 21, 23, 1, 2, 23, 0, 1, 23, 22, 0], "vertices": [1, 20, -16.39, 40.03, 1, 1, 20, 0.52, 50.73, 1, 1, 20, 24.93, 50.72, 1, 1, 20, 49.76, 49.58, 1, 2, 20, 70.49, 37.46, 0.94551, 21, -30.79, 23.97, 0.05449, 2, 20, 90.04, 25.55, 0.52535, 21, -7.97, 25.77, 0.47465, 2, 20, 103.09, 12.75, 0.05816, 21, 10.1, 23.04, 0.94184, 1, 21, 31.24, 21.74, 1, 2, 21, 48.21, 21.99, 0.84923, 22, -18.13, 12.68, 0.15077, 2, 21, 62.98, 21.95, 0.47577, 22, -8.37, 23.76, 0.52423, 2, 21, 74.63, 4.15, 0.03141, 22, 12.69, 20.8, 0.96859, 1, 22, 36.99, 15.08, 1, 1, 22, 56.59, 5.09, 1, 2, 21, 85.1, -46.99, 2e-05, 22, 58.07, -5.01, 0.99998, 3, 20, 109.77, -81.11, 0.00177, 21, 70.5, -49.12, 0.01269, 22, 50.05, -17.4, 0.98554, 3, 20, 104.23, -71.52, 0.01288, 21, 60.39, -44.59, 0.06821, 22, 39.99, -22.02, 0.91892, 3, 20, 88.46, -60.74, 0.09872, 21, 41.3, -45.1, 0.30527, 22, 27.79, -36.72, 0.59601, 3, 20, 71.96, -50.17, 0.35304, 21, 21.73, -46.2, 0.40703, 22, 15.73, -52.17, 0.23993, 3, 20, 54.3, -43.86, 0.72125, 21, 3.73, -51.42, 0.21593, 22, 7.79, -69.15, 0.06282, 3, 20, 31.83, -43.54, 0.94643, 21, -14.68, -64.33, 0.04615, 22, 5.38, -91.5, 0.00742, 2, 20, 4.89, -36.32, 0.99869, 21, -40.74, -74.26, 0.00131, 1, 20, -14.46, -14.85, 1, 1, 20, -17.49, 10.89, 1, 1, 20, 7.15, 17.15, 1, 3, 20, 33.38, -0.58, 0.99991, 21, -38.58, -28.6, 9e-05, 22, -37.25, -85.95, 0, 3, 20, 56.61, -11.57, 0.93432, 21, -13.32, -23.9, 0.05883, 22, -24.14, -63.85, 0.00685, 3, 20, 80.54, -20.48, 0.32389, 21, 11.29, -17.11, 0.62632, 22, -13.04, -40.85, 0.04979, 3, 20, 106.38, -33.39, 0.01817, 21, 39.8, -12.43, 0.63608, 22, 2.23, -16.32, 0.34575, 3, 20, 124.35, -54.35, 8e-05, 21, 66.64, -18.89, 0.0008, 22, 24.77, -0.39, 0.99912, 3, 20, 124.09, -72.97, 0.00012, 21, 77.34, -34.13, 0.00104, 22, 43.28, -2.38, 0.99883, 2, 21, 57.91, 2.66, 0.1083, 22, 2.81, 7.24, 0.8917], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44, 0, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58], "width": 134, "height": 164}}, "jio3": {"jio3": {"type": "mesh", "uvs": [0.22079, 0.54015, 0.10198, 0.51888, 0, 0.40602, 0, 0.27681, 0.06715, 0.14432, 0.22898, 0.04454, 0.4236, 0, 0.66942, 0, 0.86608, 0.05436, 1, 0.20484, 1, 0.3684, 0.98285, 0.5287, 0.95212, 0.58104, 0.86403, 0.65301, 0.89271, 0.75442, 0.85789, 0.80676, 0.75546, 0.81166, 0.65713, 0.92616, 0.57724, 1, 0.36419, 1, 0.17367, 0.99159, 0.01389, 0.94906, 0.02422, 0.87754, 0.03232, 0.82148, 0.12246, 0.75278, 0.20031, 0.7168, 0.21669, 0.61375, 0.29021, 0.14754, 0.30884, 0.32309, 0.41318, 0.45401, 0.52498, 0.58791, 0.49889, 0.72775, 0.42436, 0.84677, 0.2194, 0.86463, 0.56224, 0.11481, 0.70013, 0.23681, 0.71503, 0.40938, 0.70758, 0.57303], "triangles": [32, 25, 31, 33, 25, 32, 24, 25, 33, 23, 24, 33, 22, 23, 33, 32, 31, 17, 20, 22, 33, 21, 22, 20, 19, 33, 32, 18, 19, 32, 20, 33, 19, 17, 18, 32, 13, 16, 37, 14, 16, 13, 14, 15, 16, 16, 31, 37, 17, 31, 16, 35, 9, 10, 36, 35, 10, 36, 29, 35, 11, 36, 10, 29, 0, 28, 36, 30, 29, 37, 36, 11, 12, 37, 11, 37, 30, 36, 13, 37, 12, 30, 0, 29, 30, 26, 0, 31, 26, 30, 25, 26, 31, 31, 30, 37, 34, 6, 7, 27, 5, 6, 27, 6, 34, 4, 5, 27, 35, 7, 8, 35, 8, 9, 34, 7, 35, 28, 27, 34, 28, 34, 35, 3, 28, 2, 4, 28, 3, 27, 28, 4, 29, 28, 35, 1, 2, 28, 0, 1, 28], "vertices": [4, 33, 58.82, -7.51, 0.07892, 34, -0.66, -17.97, 0.82569, 35, 12.29, -46.63, 0.01602, 36, 25.46, -45.85, 0.07937, 4, 33, 58.34, -20.04, 0.37983, 34, -12.42, -22.33, 0.61032, 35, 15.17, -58.84, 0.00027, 36, 37.21, -50.24, 0.00958, 2, 33, 45.92, -33, 0.70724, 34, -29.15, -15.84, 0.29276, 2, 33, 29.52, -36.02, 0.90456, 34, -38.23, -1.86, 0.09544, 2, 33, 11.46, -32.31, 0.99348, 34, -41.74, 16.24, 0.00652, 1, 33, -4.21, -18.24, 1, 2, 33, -13.49, 0.44, 0.98654, 34, -21.09, 51.85, 0.01346, 2, 33, -18.07, 25.34, 0.79085, 34, 0.15, 65.64, 0.20915, 2, 33, -14.83, 46.53, 0.60511, 34, 20.96, 70.79, 0.39489, 3, 33, 1.76, 63.6, 0.42481, 34, 43.1, 62.02, 0.57502, 35, -61.66, 6.69, 0.00017, 3, 33, 22.52, 67.42, 0.25478, 34, 54.59, 44.32, 0.71573, 35, -42.68, 15.9, 0.02949, 3, 33, 43.17, 69.42, 0.09685, 34, 64.37, 26.02, 0.69411, 35, -23.31, 23.34, 0.20903, 3, 33, 50.39, 67.53, 0.06436, 34, 65.39, 18.63, 0.61432, 35, -15.85, 23.44, 0.32132, 3, 33, 61.16, 60.29, 0.01378, 34, 62.84, 5.9, 0.24367, 35, -3.54, 19.33, 0.74255, 3, 33, 73.49, 65.56, 3e-05, 34, 72.44, -3.46, 0.00499, 35, 6.94, 27.7, 0.99498, 1, 35, 14.58, 27.42, 1, 2, 35, 19.76, 18.21, 0.99979, 36, -24.33, -3.65, 0.00021, 2, 35, 37.47, 15.54, 0.65317, 36, -12.28, 9.6, 0.34683, 2, 35, 49.63, 12.3, 0.15602, 36, -2.83, 17.92, 0.84398, 1, 36, 18.91, 14.93, 1, 1, 36, 38.2, 11.18, 1, 1, 36, 53.76, 3.5, 1, 2, 34, 6.07, -65.5, 0.00504, 36, 51.45, -5.49, 0.99496, 3, 34, 2.83, -58.98, 0.01942, 35, 53.42, -48.25, 4e-05, 36, 49.63, -12.54, 0.98055, 3, 34, 5.79, -46.49, 0.10223, 35, 41.39, -43.77, 0.01095, 36, 39.23, -20.06, 0.88682, 3, 34, 9.98, -38.23, 0.29005, 35, 33.71, -38.58, 0.04779, 36, 30.65, -23.56, 0.66216, 4, 33, 68.24, -6.2, 0.00076, 34, 4.16, -26.16, 0.68292, 35, 21.02, -42.86, 0.05208, 36, 27.17, -36.5, 0.26424, 1, 33, 7.72, -9.63, 1, 1, 33, 29.64, -3.65, 1, 2, 33, 44.31, 9.97, 0.05833, 34, 9.91, 2.14, 0.94167, 3, 34, 28.98, -6.07, 0.85656, 35, 4.16, -15.75, 0.12215, 36, -4.74, -35.48, 0.02129, 3, 34, 36.55, -22.67, 0.17786, 35, 21.56, -10.29, 0.66179, 36, 0.38, -17.97, 0.16035, 3, 34, 38.47, -39.72, 0.01953, 35, 38.72, -10.5, 0.07935, 36, 10.08, -3.81, 0.90112, 3, 34, 22.02, -53.15, 0.01965, 35, 50.01, -28.48, 0.00274, 36, 31.3, -4.4, 0.97761, 2, 33, -1.5, 17.16, 0.85892, 34, -1.04, 47.2, 0.14108, 2, 33, 11.41, 33.97, 0.55018, 34, 19.44, 41.74, 0.44982, 3, 33, 33.02, 39.51, 0.21574, 34, 32.85, 23.9, 0.78097, 35, -25.11, -8.2, 0.00329, 3, 33, 53.93, 42.57, 0.01593, 34, 43.7, 5.78, 0.83575, 35, -5.78, 0.33, 0.14833], "hull": 27, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 46, 48, 48, 50, 50, 52, 0, 52, 12, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 42, 44, 44, 46, 66, 44, 68, 70, 70, 72, 72, 74], "width": 103, "height": 129}}, "jio4": {"jio4": {"type": "mesh", "uvs": [0.92147, 0.08058, 1, 0.1785, 1, 0.32946, 0.87439, 0.47226, 0.70766, 0.63138, 0.57428, 0.76398, 0.43109, 0.9333, 0.34674, 1, 0.20551, 0.98022, 0.07801, 0.90066, 0, 0.79662, 0.01916, 0.69258, 0.13686, 0.6579, 0.19374, 0.54978, 0.23689, 0.38658, 0.29378, 0.20298, 0.43109, 0.0561, 0.6037, 0, 0.7822, 0.02754, 0.76928, 0.26149, 0.60618, 0.42395, 0.40172, 0.60313, 0.35348, 0.77276, 0.2547, 0.82771, 0.10309, 0.77754], "triangles": [7, 8, 23, 8, 9, 23, 9, 24, 23, 9, 10, 24, 22, 23, 12, 10, 11, 24, 23, 24, 12, 24, 11, 12, 6, 7, 23, 23, 22, 6, 6, 22, 5, 12, 13, 22, 22, 21, 5, 22, 13, 21, 5, 21, 4, 21, 20, 4, 4, 20, 3, 13, 14, 21, 21, 14, 20, 20, 19, 3, 3, 19, 2, 14, 15, 20, 15, 16, 20, 20, 16, 19, 19, 16, 17, 19, 1, 2, 19, 0, 1, 19, 18, 0, 19, 17, 18], "vertices": [1, 23, -14.22, 31.31, 1, 1, 23, -3.87, 47.4, 1, 1, 23, 17.73, 54.17, 1, 1, 23, 44.04, 41.88, 1, 1, 23, 74.59, 24.21, 1, 2, 23, 99.8, 10.3, 0.12882, 24, 3.95, 14.67, 0.87118, 2, 24, 37.42, 19.55, 0.94344, 25, -21.31, 4.53, 0.05656, 2, 24, 53.92, 18.66, 0.46351, 25, -10.4, 16.95, 0.53649, 1, 25, 11.78, 18.41, 1, 1, 25, 33.64, 10.66, 1, 3, 23, 131.27, -73.72, 8e-05, 24, 75.38, -39.62, 0.00021, 25, 48.67, -2.22, 0.99971, 3, 23, 115.49, -75.53, 0.00693, 24, 62.99, -49.58, 0.0188, 25, 48.83, -18.11, 0.97427, 3, 23, 105.03, -59.57, 0.07981, 24, 45.64, -41.65, 0.17562, 25, 31.87, -26.85, 0.74457, 3, 23, 86.9, -55.95, 0.33517, 24, 28.37, -48.26, 0.33302, 25, 26.39, -44.51, 0.33181, 3, 23, 61.53, -56.86, 0.72384, 24, 7.39, -62.55, 0.1854, 25, 24.65, -69.84, 0.09077, 3, 23, 32.59, -56.63, 0.93496, 24, -17.21, -77.79, 0.04904, 25, 21.41, -98.59, 0.01601, 3, 23, 5.16, -42.78, 0.99504, 24, -47.8, -80.7, 0.00446, 25, 4.79, -124.43, 0.0005, 1, 23, -10.92, -19.6, 1, 1, 23, -15.31, 8.2, 1, 1, 23, 18.78, 16.78, 1, 3, 23, 49.64, -0.21, 0.99988, 24, -32.88, -20.97, 0.0001, 25, -32.93, -75.77, 2e-05, 3, 23, 84.83, -22.6, 0.43045, 24, 8.83, -21.15, 0.49566, 25, -7, -43.1, 0.07389, 3, 23, 111.36, -22.17, 0.01514, 24, 31.05, -6.64, 0.88508, 25, -4.67, -16.67, 0.09978, 3, 23, 123.83, -34.41, 0.00439, 24, 48.13, -10.34, 0.13445, 25, 8.8, -5.53, 0.86116, 3, 23, 123.73, -59.23, 0.00639, 24, 61.27, -31.39, 0.02037, 25, 33.47, -8.22, 0.97324], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36, 0, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48], "width": 156, "height": 150}}, "meimao": {"meimao": {"x": 46.09, "y": -8.83, "rotation": -91, "width": 62, "height": 18}}, "sd": {"sd": {"x": -50.76, "y": 174.57, "rotation": 0.46, "width": 506, "height": 242}}, "tou": {"tou": {"type": "mesh", "uvs": [0.43071, 0, 0.5348, 0, 0.64067, 0.03125, 0.7492, 0.08718, 0.83816, 0.18414, 0.87108, 0.28109, 0.90934, 0.31092, 0.91111, 0.3942, 0.89866, 0.5098, 0.88354, 0.63037, 0.86752, 0.73478, 0.84617, 0.81682, 0.80703, 0.8827, 0.73586, 0.93242, 0.63177, 0.98711, 0.50633, 1, 0.41025, 1, 0.34976, 1, 0.2519, 1, 0.15404, 0.94733, 0.07063, 0.86644, 0.01771, 0.74732, 0, 0.61862, 0, 0.46801, 0, 0.33794, 0.04613, 0.16953, 0.14804, 0.06411, 0.25584, 0.0203, 0.33619, 0, 0.34681, 0.10833, 0.31365, 0.29569, 0.29779, 0.52335, 0.30067, 0.72884, 0.32662, 0.89807, 0.49244, 0.11841, 0.45928, 0.3098, 0.43332, 0.53544, 0.43188, 0.71877, 0.45207, 0.888, 0.2156, 0.13855, 0.17811, 0.32994, 0.16657, 0.52536, 0.17955, 0.71474, 0.20839, 0.888, 0.14062, 0.14863, 0.07862, 0.3239, 0.06852, 0.50723, 0.09159, 0.72481, 0.11899, 0.85979, 0.61933, 0.19295, 0.58473, 0.34002, 0.56021, 0.54752, 0.56886, 0.72884, 0.59626, 0.86382, 0.72747, 0.22317, 0.74622, 0.40247, 0.72892, 0.59386, 0.69864, 0.74899, 0.68277, 0.85173, 0.82841, 0.34203, 0.83129, 0.51529, 0.80534, 0.67444, 0.76785, 0.81748], "triangles": [44, 25, 26, 44, 26, 39, 39, 26, 27, 39, 27, 29, 45, 44, 40, 48, 42, 43, 38, 37, 52, 48, 47, 42, 46, 45, 40, 47, 46, 41, 30, 39, 29, 35, 29, 34, 30, 29, 35, 40, 39, 30, 31, 40, 30, 41, 46, 40, 31, 41, 40, 36, 30, 35, 51, 36, 35, 31, 30, 36, 42, 41, 31, 36, 32, 31, 32, 42, 31, 47, 41, 42, 36, 37, 32, 43, 42, 32, 33, 32, 37, 37, 36, 51, 33, 37, 38, 50, 51, 35, 37, 51, 52, 40, 44, 39, 43, 32, 33, 29, 28, 0, 27, 28, 29, 34, 0, 1, 29, 0, 34, 49, 1, 2, 49, 2, 3, 34, 1, 49, 54, 49, 3, 54, 3, 4, 35, 34, 49, 45, 25, 44, 24, 25, 45, 50, 35, 49, 59, 54, 4, 59, 4, 5, 55, 54, 59, 23, 24, 45, 46, 23, 45, 59, 6, 7, 6, 59, 5, 7, 60, 59, 8, 60, 7, 55, 59, 60, 55, 50, 54, 56, 55, 60, 22, 23, 46, 9, 60, 8, 61, 56, 60, 61, 60, 9, 50, 49, 54, 55, 51, 50, 22, 46, 47, 56, 52, 51, 55, 56, 51, 10, 61, 9, 21, 22, 47, 57, 52, 56, 57, 56, 61, 11, 61, 10, 62, 57, 61, 11, 62, 61, 57, 53, 52, 58, 57, 62, 58, 53, 57, 20, 21, 47, 20, 47, 48, 12, 62, 11, 38, 52, 53, 13, 58, 62, 13, 62, 12, 19, 48, 43, 20, 48, 19, 14, 53, 58, 14, 58, 13, 18, 43, 33, 19, 43, 18, 16, 17, 33, 18, 33, 17, 38, 16, 33, 15, 38, 53, 15, 53, 14, 16, 38, 15], "vertices": [1, 5, 175.08, -84.53, 1, 2, 5, 161.16, -112.37, 0.312, 11, -8.38, 13.2, 0.688, 2, 5, 141.03, -137.69, 0.272, 11, 15.32, -8.82, 0.728, 2, 5, 115.81, -161.36, 0.72, 11, 36.99, -35.78, 0.28, 1, 5, 85.35, -175.87, 1, 2, 5, 62.39, -175.4, 0.84, 7, 77.17, -167.85, 0.16, 2, 5, 51.57, -182.77, 0.84, 7, 70.59, -179.17, 0.16, 2, 5, 35.39, -175.28, 0.84, 7, 52.76, -179.4, 0.16, 2, 5, 14.93, -160.88, 0.84, 7, 28.09, -175.24, 0.16, 2, 5, -6.13, -145.3, 0.84, 7, 2.37, -170.27, 0.16, 2, 5, -23.97, -131.03, 0.84, 7, -19.89, -165.09, 0.16, 2, 5, -36.82, -117.47, 0.872, 7, -37.33, -158.41, 0.128, 2, 5, -44.19, -100.69, 0.88, 7, -51.22, -146.46, 0.12, 2, 5, -44.19, -76.9, 0.888, 7, -61.49, -125, 0.112, 2, 5, -40.74, -43.83, 0.904, 7, -72.65, -93.68, 0.096, 2, 5, -26.44, -9.05, 0.96, 7, -74.75, -56.13, 0.04, 2, 5, -13.59, 16.65, 0.912, 7, -74.25, -27.4, 0.088, 2, 5, -5.5, 32.82, 0.952, 7, -73.94, -9.32, 0.048, 2, 5, 7.58, 59, 0.904, 7, -73.43, 19.94, 0.096, 2, 5, 30.75, 80.13, 0.832, 7, -61.65, 49, 0.168, 2, 5, 57.39, 94.69, 0.824, 7, -43.91, 73.63, 0.176, 2, 5, 87.26, 97.44, 0.776, 7, -18.15, 89.01, 0.224, 2, 5, 114.27, 89.86, 0.736, 7, 9.48, 93.82, 0.264, 2, 5, 143.09, 75.45, 0.768, 7, 41.71, 93.26, 0.232, 2, 5, 167.99, 63, 0.72, 7, 69.54, 92.78, 0.28, 2, 5, 194.06, 34.55, 0.856, 7, 105.33, 78.36, 0.144, 1, 8, 20.28, 5.87, 1, 1, 8, -2.65, -18.64, 1, 1, 5, 187.72, -59.25, 1, 1, 7, 116.86, -11.76, 1, 1, 7, 76.95, -1.15, 1, 1, 7, 28.32, 4.44, 1, 1, 7, -15.66, 4.35, 1, 2, 5, 17.1, 29.26, 0.792, 7, -52.01, -2.78, 0.208, 2, 5, 144.16, -89.71, 0.704, 7, 113.95, -55.26, 0.296, 1, 7, 73.17, -44.63, 1, 1, 7, 25.03, -36.03, 1, 1, 7, -14.19, -34.92, 1, 2, 5, 2.26, -5.26, 0.664, 7, -50.51, -40.32, 0.336, 3, 5, 177.33, -13.74, 0.40186, 7, 111.08, 27.58, 0.15014, 8, -5.22, 9.27, 0.448, 1, 7, 70.32, 39.5, 1, 1, 7, 28.57, 43.68, 1, 1, 7, -12.02, 40.5, 1, 2, 5, 34.84, 59.91, 0.544, 7, -49.24, 32.53, 0.456, 3, 5, 185.42, 7.28, 0.39603, 7, 109.32, 50.03, 0.272, 8, 12.96, 22.56, 0.33197, 2, 5, 160.17, 40.63, 0.456, 7, 72.14, 69.22, 0.544, 2, 5, 126.42, 60.88, 0.456, 7, 32.96, 72.92, 0.544, 2, 5, 81.69, 75.53, 0.456, 7, -13.71, 66.84, 0.544, 2, 5, 52.19, 81.12, 0.448, 7, -42.74, 59.15, 0.552, 2, 5, 112.93, -116.51, 0.84, 7, 97.34, -92.92, 0.16, 2, 5, 89.41, -93.18, 0.752, 7, 66.05, -82.02, 0.248, 1, 7, 21.78, -73.92, 1, 2, 5, 17.1, -51.72, 0.728, 7, -17.06, -75.83, 0.272, 2, 5, -12.4, -46.13, 0.824, 7, -46.08, -83.52, 0.176, 2, 5, 92.68, -142.54, 0.912, 7, 90.31, -125.13, 0.088, 2, 5, 55.86, -130.39, 0.856, 7, 51.85, -130.07, 0.144, 2, 5, 21.54, -107.44, 0.8, 7, 10.99, -124.18, 0.2, 2, 5, -4.11, -84.5, 0.88, 7, -22.05, -114.55, 0.12, 2, 5, -21.65, -70.43, 0.904, 7, -43.95, -109.43, 0.096, 2, 5, 56.44, -158.15, 0.872, 7, 64.35, -154.86, 0.128, 2, 5, 22.89, -142.34, 0.92, 7, 27.27, -155.08, 0.08, 2, 5, -4.11, -120.17, 0.928, 7, -6.65, -146.73, 0.072, 2, 5, -26.47, -96.45, 0.912, 7, -37.06, -134.99, 0.088], "hull": 29, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 0, 56, 0, 58, 58, 60, 60, 62, 62, 64, 64, 66, 2, 68, 68, 70, 70, 72, 72, 74, 74, 76, 54, 78, 78, 80, 80, 82, 82, 84, 84, 86, 88, 90, 90, 92, 92, 94, 94, 96, 98, 100, 100, 102, 102, 104, 104, 106, 108, 110, 110, 112, 112, 114, 114, 116, 118, 120, 120, 122, 122, 124], "width": 297, "height": 212}}, "toufa1": {"toufa1": {"type": "mesh", "uvs": [0.75636, 1, 0.87265, 0.89243, 1, 0.7508, 1, 0.55375, 1, 0.34849, 0.88684, 0.19454, 0.72793, 0.14497, 0.55782, 0.09191, 0.27986, 0, 0.0884, 0, 0.026, 0.20686, 0, 0.38338, 0, 0.63175, 0.06713, 0.8247, 0.25716, 0.88833, 0.44436, 0.92528, 0.59611, 0.9807, 0.18725, 0.18704, 0.34462, 0.39614, 0.5252, 0.50069, 0.67483, 0.72473, 0.52004, 0.26172, 0.66968, 0.3476, 0.82705, 0.5119, 0.88638, 0.7434, 0.10469, 0.34387, 0.17951, 0.56417, 0.38073, 0.71726, 0.53552, 0.81808], "triangles": [26, 25, 18, 26, 12, 11, 26, 11, 25, 26, 18, 27, 13, 12, 26, 14, 26, 27, 13, 26, 14, 15, 27, 28, 14, 27, 15, 17, 9, 8, 10, 9, 17, 18, 17, 8, 25, 10, 17, 11, 10, 25, 25, 17, 18, 20, 22, 23, 19, 22, 20, 28, 27, 19, 20, 28, 19, 1, 20, 24, 16, 28, 20, 0, 16, 20, 15, 28, 16, 1, 0, 20, 21, 8, 7, 21, 7, 22, 21, 18, 8, 19, 21, 22, 18, 21, 19, 27, 18, 19, 22, 7, 6, 5, 22, 6, 23, 5, 4, 23, 22, 5, 23, 4, 3, 24, 23, 3, 20, 23, 24, 24, 3, 2, 1, 24, 2], "vertices": [3, 17, -27.29, 11.15, 0.13737, 14, -18.52, 6.32, 0.85507, 19, -53.64, -0.41, 0.00755, 2, 17, -17.95, -0.82, 0.59235, 14, -20.32, -8.76, 0.40765, 2, 17, -5.92, -13.76, 0.99159, 14, -20.9, -26.41, 0.00841, 2, 17, 8.99, -12.35, 0.96591, 18, -13.46, -3.51, 0.03409, 2, 17, 24.52, -10.89, 0.25544, 18, -1.97, -14.07, 0.74456, 1, 18, 15.06, -12.81, 1, 4, 18, 29.66, -2.49, 0.84865, 15, -10.45, -31.45, 0.13337, 14, 33.67, -32.53, 0.01724, 16, -26.23, -39.3, 0.00074, 4, 18, 45.29, 8.57, 0.29203, 15, 8.46, -28.45, 0.61449, 14, 48.67, -20.63, 0.01203, 16, -8.68, -31.65, 0.08145, 3, 18, 71.12, 26.36, 0.00858, 15, 39.49, -23.92, 0.13166, 16, 20.22, -19.46, 0.85976, 1, 16, 37.31, -7.16, 1, 2, 16, 33.7, 9.61, 0.88795, 19, 44.03, -23.85, 0.11205, 2, 16, 28.19, 22.17, 0.50919, 19, 41.33, -10.4, 0.49081, 2, 16, 17.16, 37.49, 0.02436, 19, 33.83, 6.93, 0.97564, 1, 19, 21.24, 17.46, 1, 2, 14, 22.91, 43.35, 0.01413, 19, 0.13, 13.6, 0.98587, 3, 15, -2.79, 35.11, 0.0015, 14, 7.66, 29.22, 0.47583, 19, -19.89, 8, 0.52266, 3, 17, -27.49, 28.83, 0.0002, 14, -6.19, 19, 0.8722, 19, -36.88, 5.24, 0.1276, 3, 18, 67.54, 43.48, 8e-05, 15, 43.86, -6.98, 0.0031, 16, 20.18, -1.97, 0.99681, 3, 15, 21.97, 1.58, 0.97803, 14, 45.7, 12.17, 0.0001, 19, 6.15, -24.55, 0.02188, 3, 15, 0.58, 1.81, 0.45287, 14, 26.95, 1.86, 0.53916, 19, -15.24, -25.14, 0.00797, 3, 17, -7.31, 22.04, 0.0004, 18, 1.16, 31.62, 8e-05, 14, 3.34, -0.04, 0.99952, 4, 18, 38.59, 20.36, 0.1759, 15, 7.67, -14.92, 0.75123, 14, 41.34, -9.23, 0.02659, 16, -12.85, -18.75, 0.04628, 4, 17, 21.18, 25.3, 0.0116, 18, 22.65, 12.65, 0.51951, 15, -10.03, -14.78, 0.15873, 14, 25.84, -17.8, 0.31015, 3, 17, 10.37, 6.89, 0.5962, 18, 1.75, 8.35, 0.28922, 14, 5.21, -23.24, 0.11458, 2, 17, -6.53, -1.26, 0.88618, 14, -12.53, -17.12, 0.11382, 2, 16, 20.59, 13.01, 0.70105, 19, 31.95, -17.72, 0.29895, 3, 15, 34.29, 20.05, 0.01909, 16, 4.14, 21.79, 0.13334, 19, 17.75, -5.62, 0.84757, 3, 15, 9.45, 22.9, 0.10606, 14, 24.32, 24.59, 0.17289, 19, -7.19, -3.73, 0.72105, 3, 15, -9.19, 23.9, 0.01039, 14, 7.59, 16.3, 0.74607, 19, -25.86, -3.45, 0.24355], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32, 18, 34, 34, 36, 36, 38, 38, 40, 40, 0, 42, 44, 44, 46, 46, 48, 50, 52, 52, 54, 54, 56], "width": 110, "height": 76}}, "tq1": {"tq1": {"type": "mesh", "uvs": [0.07965, 0.07698, 0.13454, 0.01419, 0.32361, 0.01786, 0.47151, 0.04169, 0.61636, 0.07056, 0.75893, 0.13336, 0.83498, 0.23687, 0.85951, 0.33028, 0.83031, 0.41667, 0.76488, 0.46373, 0.7532, 0.55996, 0.88288, 0.58244, 0.95298, 0.69552, 0.88872, 0.77629, 0.83031, 0.79877, 0.90694, 0.84048, 0.98735, 0.89801, 1, 0.94054, 0.84216, 0.99929, 0.61135, 0.99649, 0.61135, 0.94278, 0.63648, 0.86724, 0.643, 0.82416, 0.52573, 0.79898, 0.42801, 0.70274, 0.49688, 0.59028, 0.61508, 0.56286, 0.60229, 0.45086, 0.63811, 0.41098, 0.62086, 0.32165, 0.59835, 0.25654, 0.5273, 0.18689, 0.3574, 0.17854, 0.1875, 0.16646, 0.07475, 0.14975, 0.22557, 0.08114, 0.41594, 0.10236, 0.56219, 0.12131, 0.69079, 0.163, 0.71601, 0.23652, 0.74248, 0.31914, 0.73114, 0.40858, 0.69457, 0.46012, 0.68827, 0.56093, 0.68572, 0.68845, 0.73368, 0.81246, 0.76481, 0.87171, 0.80717, 0.94448], "triangles": [47, 16, 17, 47, 46, 15, 46, 14, 15, 20, 21, 46, 46, 21, 45, 46, 47, 20, 47, 19, 20, 19, 47, 18, 47, 15, 16, 18, 47, 17, 43, 26, 42, 43, 42, 10, 21, 22, 45, 46, 45, 14, 45, 22, 44, 14, 45, 44, 44, 22, 23, 23, 24, 44, 14, 44, 13, 13, 44, 12, 24, 25, 44, 44, 11, 12, 11, 44, 10, 44, 43, 10, 25, 26, 44, 44, 26, 43, 42, 26, 27, 10, 42, 9, 42, 41, 9, 9, 41, 8, 27, 28, 42, 42, 28, 41, 8, 41, 7, 28, 29, 41, 41, 40, 7, 41, 29, 40, 40, 6, 7, 40, 29, 39, 29, 30, 39, 40, 39, 6, 6, 39, 38, 39, 30, 38, 6, 38, 5, 30, 31, 38, 31, 37, 38, 31, 36, 37, 5, 38, 4, 38, 37, 4, 37, 3, 4, 32, 36, 31, 33, 35, 32, 32, 35, 36, 35, 33, 0, 33, 34, 0, 36, 3, 37, 35, 2, 36, 36, 2, 3, 0, 1, 35, 35, 1, 2], "vertices": [1, 43, -11.88, -2.47, 1, 1, 43, -8.53, 8.97, 1, 1, 43, 10.31, 12.14, 1, 2, 43, 25.74, 11.16, 0.86128, 44, -7.63, 8.67, 0.13872, 2, 43, 41.04, 9.3, 0.02931, 44, 6.91, 13.8, 0.97069, 2, 44, 24.75, 14.28, 0.97909, 45, -14.4, 5.01, 0.02091, 2, 44, 41.47, 5.24, 0.02903, 45, 3.45, 11.56, 0.97097, 2, 45, 19.27, 13.03, 0.96312, 46, -11.53, 10.1, 0.03688, 2, 45, 33.57, 9.16, 0.20069, 46, 3.27, 10.58, 0.79931, 1, 46, 12.49, 5.98, 1, 1, 47, 0.83, 8.7, 1, 1, 47, 5.5, 21.51, 1, 1, 47, 24.94, 27.27, 1, 1, 47, 38.03, 19.86, 1, 1, 47, 41.39, 13.71, 1, 1, 52, 4, 16.88, 1, 1, 52, 15.34, 22.43, 1, 1, 52, 22.58, 21.95, 1, 1, 52, 28.32, 4.09, 1, 1, 52, 22.24, -18.42, 1, 1, 52, 13.48, -16.24, 1, 1, 52, 1.78, -10.72, 1, 1, 47, 44.34, -5.45, 1, 1, 47, 39.31, -16.98, 1, 1, 47, 22.5, -25.71, 1, 1, 47, 4.13, -17.47, 1, 2, 46, 32.18, -4.91, 0.008, 47, 0.35, -5.25, 0.992, 2, 45, 37.83, -14.19, 0.00258, 46, 14.17, -10.5, 0.99742, 2, 45, 31.37, -10.15, 0.16597, 46, 6.82, -8.53, 0.83403, 3, 44, 33.1, -19.27, 0.04527, 45, 16.28, -10.93, 0.95028, 46, -7.38, -13.68, 0.00445, 2, 44, 24.6, -12.01, 0.52451, 45, 5.22, -12.5, 0.47549, 3, 43, 36.09, -11.64, 0.03611, 44, 11.76, -7.16, 0.95819, 45, -6.91, -18.91, 0.0057, 2, 43, 18.99, -13.65, 0.90322, 44, -2.66, -16.56, 0.09678, 1, 43, 1.76, -15.05, 1, 1, 43, -9.95, -14.55, 1, 1, 43, 2.7, -0.24, 1, 1, 43, 22.26, 0.06, 1, 1, 44, 7.8, 3.71, 1, 2, 44, 22.36, 6.13, 0.99798, 45, -9.87, -2.18, 0.00202, 1, 45, 2.62, -0.42, 1, 1, 45, 16.65, 1.36, 1, 1, 46, 4.26, 0.52, 1, 1, 46, 13.54, -1.07, 1, 1, 47, 0.54, 2.15, 1, 1, 47, 21.89, 0.42, 1, 1, 47, 43.01, 3.82, 1, 1, 52, 5.63, 1.68, 1, 1, 52, 18.53, 2.89, 1], "hull": 35, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 0, 68, 0, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 90, 92, 92, 94], "width": 101, "height": 168}}, "tq3": {"tq3": {"x": 11.48, "y": 0.95, "rotation": 57.92, "width": 33, "height": 37}}, "tq31": {"tq31": {"type": "mesh", "uvs": [0.09687, 0, 0.20442, 0, 0.31774, 0.09478, 0.51172, 0.11471, 0.71339, 0.25279, 0.71339, 0.48198, 0.60775, 0.57024, 0.77293, 0.60867, 1, 0.69693, 1, 0.78804, 0.89585, 0.90334, 0.75756, 0.96455, 0.50788, 1, 0.45218, 0.92327, 0.38304, 0.75387, 0.34463, 0.62575, 0.13144, 0.58874, 0, 0.43927, 0, 0.28268, 0.06421, 0.1987, 0.04885, 0.05919, 0.19199, 0.14244, 0.3408, 0.39307, 0.48709, 0.61029, 0.56205, 0.7214, 0.70515, 0.84136], "triangles": [11, 12, 25, 12, 13, 25, 11, 25, 10, 13, 24, 25, 13, 14, 24, 10, 25, 9, 9, 25, 8, 25, 24, 7, 25, 7, 8, 14, 23, 24, 24, 6, 7, 24, 23, 6, 23, 15, 22, 6, 23, 22, 22, 15, 16, 16, 17, 22, 6, 22, 5, 22, 4, 5, 17, 18, 22, 18, 19, 22, 19, 21, 22, 21, 2, 22, 22, 3, 4, 22, 2, 3, 19, 20, 21, 21, 1, 2, 14, 15, 23, 1, 21, 0, 0, 21, 20], "vertices": [2, 49, 32.4, -1.5, 0.99851, 50, -16.17, -4.31, 0.00149, 1, 49, 36.43, 3.96, 1, 1, 50, -3.76, 5.91, 1, 1, 50, 2.1, 16.77, 1, 1, 50, 17.53, 24.58, 1, 2, 50, 35.79, 17.77, 0.99617, 51, -3.12, 19.41, 0.00383, 1, 50, 40.49, 8.92, 1, 2, 50, 47.19, 17.52, 0.05481, 51, 7.85, 16.29, 0.94519, 1, 51, 22.22, 23.68, 1, 1, 51, 28.55, 19.22, 1, 1, 51, 32.8, 8.22, 1, 1, 51, 32.04, -1.9, 1, 1, 51, 25.46, -16.5, 1, 1, 51, 18.11, -15.62, 1, 2, 50, 50.17, -9.8, 0.00538, 51, 3.82, -10.9, 0.99462, 1, 50, 39.12, -8.27, 1, 1, 50, 31.48, -19.75, 1, 1, 50, 16.69, -23.07, 1, 2, 49, 48.11, -20.67, 0.02194, 50, 4.22, -18.42, 0.97806, 1, 50, -1.06, -12.14, 1, 2, 49, 34.65, -6.92, 0.91252, 50, -12.51, -8.9, 0.08748, 1, 50, -2.73, -2.93, 1, 1, 50, 20.51, -1.58, 1, 1, 50, 41.03, 0.6, 1, 1, 51, 8.05, -0.09, 1, 1, 51, 21.58, 1.42, 1], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 0, 42, 46, 48, 48, 50], "width": 63, "height": 85}}, "weiba1": {"weiba1": {"type": "mesh", "uvs": [0.02698, 0.86156, 0.11244, 0.77783, 0.1937, 0.72617, 0.31419, 0.72082, 0.46831, 0.67807, 0.55517, 0.61216, 0.5888, 0.51061, 0.56358, 0.4447, 0.43608, 0.40373, 0.42067, 0.25943, 0.49913, 0.12582, 0.61402, 0.02249, 0.81436, 0.06703, 0.95307, 0.17926, 1, 0.37522, 1, 0.59078, 0.93065, 0.79565, 0.82557, 0.92925, 0.73591, 1, 0.57339, 1, 0.38985, 0.98804, 0.21052, 0.97557, 0.09003, 0.94707, 0.11965, 0.88064, 0.22817, 0.87664, 0.3304, 0.86264, 0.45308, 0.84464, 0.58519, 0.80064, 0.70787, 0.73065, 0.76134, 0.55866, 0.8101, 0.36668, 0.68585, 0.24269, 0.57418, 0.22869], "triangles": [7, 8, 32, 8, 9, 32, 9, 10, 32, 7, 31, 30, 7, 32, 31, 30, 12, 13, 30, 31, 12, 32, 11, 31, 31, 11, 12, 32, 10, 11, 28, 29, 16, 16, 29, 15, 28, 6, 29, 29, 30, 15, 30, 14, 15, 29, 6, 30, 6, 7, 30, 30, 13, 14, 19, 27, 18, 27, 28, 18, 18, 28, 17, 20, 26, 19, 19, 26, 27, 17, 28, 16, 26, 4, 27, 26, 3, 4, 4, 5, 27, 27, 5, 28, 5, 6, 28, 20, 21, 25, 21, 24, 25, 20, 25, 26, 22, 23, 21, 21, 23, 24, 22, 0, 23, 0, 1, 23, 23, 1, 24, 1, 2, 24, 24, 3, 25, 24, 2, 3, 25, 3, 26], "vertices": [1, 37, -22.57, -1.28, 1, 1, 37, -6.08, 11.26, 1, 1, 37, 9.57, 18.97, 1, 2, 37, 32.71, 19.63, 0.97851, 38, -15.72, 28.11, 0.02149, 5, 37, 62.33, 25.91, 0.21599, 38, 13.99, 22.19, 0.77589, 39, -13.1, 46.57, 0.00644, 40, 38.2, 72.73, 0.00145, 41, 49.84, 55.69, 0.00024, 5, 37, 79.07, 35.76, 0.00519, 38, 33.25, 24.65, 0.7808, 39, 1.8, 34.11, 0.16236, 40, 27.6, 56.45, 0.04033, 41, 30.42, 55.08, 0.01132, 4, 38, 45.3, 36.12, 0.27826, 39, 18.4, 33.07, 0.38632, 40, 28.54, 39.84, 0.24113, 41, 17.33, 44.82, 0.09429, 4, 38, 44.84, 47.18, 0.08062, 39, 26.2, 40.92, 0.21272, 40, 37.26, 33.03, 0.3927, 41, 16.73, 33.76, 0.31396, 4, 38, 24.88, 62.64, 0.00473, 39, 23.97, 66.07, 0.01894, 40, 61.97, 38.24, 0.08988, 41, 35.13, 16.47, 0.88645, 4, 38, 30.88, 83.79, 0, 39, 43.57, 76.05, 0, 40, 74.21, 19.96, 2e-05, 41, 27.13, -4.02, 0.99997, 2, 40, 69.54, -4.78, 0.15216, 41, 4.17, -14.34, 0.84784, 2, 40, 56.59, -28.49, 0.94983, 41, -22.69, -17.26, 0.05017, 2, 39, 95.92, 14.27, 0.00202, 40, 19.08, -39.36, 0.99798, 2, 39, 88.7, -16.46, 0.22878, 40, -12.29, -35.84, 0.77122, 2, 39, 63.74, -34.72, 0.80843, 40, -33.39, -13.22, 0.19157, 2, 39, 33.01, -45.46, 0.99999, 40, -47.7, 16.01, 1e-05, 2, 38, 88.25, -29.58, 0.14246, 39, -0.58, -43.09, 0.85754, 2, 38, 61.69, -40, 0.65587, 39, -26.28, -30.7, 0.34413, 2, 38, 41.64, -42.91, 0.91867, 39, -42.04, -17.97, 0.08133, 2, 37, 82.21, -22.82, 0.03701, 38, 13.04, -30.43, 0.96299, 2, 37, 46.99, -20.81, 0.93906, 38, -18.54, -14.69, 0.06094, 1, 37, 12.57, -18.71, 1, 1, 37, -10.54, -14.27, 1, 1, 37, -4.79, -4.27, 1, 1, 37, 16.05, -3.79, 1, 1, 37, 35.69, -1.8, 1, 2, 37, 59.26, 0.77, 0.25838, 38, 1.25, 0.31, 0.74162, 1, 38, 27.16, -3.75, 1, 1, 38, 52.97, -3.48, 1, 1, 39, 22.48, -0.6, 1, 2, 39, 52.93, 0.12, 0.80688, 40, -0.07, 1.65, 0.19312, 1, 40, 29.58, -4.68, 1, 4, 38, 59.75, 76.26, 0.00038, 39, 57.67, 49.76, 0.00168, 40, 49.77, 2.84, 0.83829, 41, -0.89, 6.24, 0.15965], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44, 0, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 18], "width": 192, "height": 151}}, "weiba2": {"weiba2": {"type": "mesh", "uvs": [0.63811, 0.85483, 0.5604, 0.94555, 0.43375, 1, 0.26968, 1, 0.11425, 0.93372, 0.03077, 0.81144, 0, 0.65563, 0.00487, 0.39925, 0.04373, 0.26119, 0.1603, 0.11327, 0.27112, 0.01466, 0.39201, 0, 0.53161, 0.02452, 0.71439, 0.07186, 0.85687, 0.13891, 0.9792, 0.26119, 1, 0.40122, 0.92307, 0.3598, 0.79642, 0.37558, 0.70575, 0.45841, 0.65106, 0.57872, 0.65538, 0.68719, 0.67121, 0.79172, 0.11205, 0.6171, 0.25401, 0.4698, 0.38381, 0.35863, 0.58053, 0.2697, 0.72655, 0.30583], "triangles": [25, 10, 11, 25, 11, 12, 25, 12, 26, 9, 10, 25, 24, 9, 25, 8, 9, 24, 7, 8, 24, 25, 26, 20, 23, 7, 24, 6, 7, 23, 5, 6, 23, 24, 25, 21, 21, 25, 20, 0, 21, 22, 3, 4, 23, 5, 23, 4, 0, 1, 21, 21, 2, 24, 24, 3, 23, 24, 2, 3, 21, 1, 2, 26, 12, 13, 27, 13, 14, 26, 13, 27, 17, 14, 15, 18, 27, 14, 17, 18, 14, 17, 15, 16, 19, 26, 27, 19, 27, 18, 20, 26, 19], "vertices": [2, 40, 19.64, 45.75, 0.29674, 41, 17.1, 55.49, 0.70326, 2, 40, 25.67, 59.61, 0.21311, 41, 31.91, 58.47, 0.78689, 2, 40, 39.92, 73.13, 0.11471, 41, 51.15, 54.51, 0.88529, 2, 40, 61.73, 83.8, 0.03345, 41, 72.38, 42.72, 0.96655, 2, 40, 85.54, 87.49, 0.00346, 41, 89.01, 25.29, 0.99654, 1, 41, 93.4, 7.74, 1, 1, 41, 89.21, -9.18, 1, 1, 41, 75.13, -33.03, 1, 1, 41, 62.86, -43.27, 1, 1, 41, 40.02, -48.86, 1, 2, 40, 108.31, -11.87, 0.00035, 41, 20.51, -50.2, 0.99965, 2, 40, 92.94, -21.16, 0.04737, 41, 4.1, -42.9, 0.95263, 2, 40, 73.22, -27.86, 0.37611, 41, -12.67, -30.55, 0.62389, 2, 40, 46.67, -35.16, 0.96221, 41, -33.84, -12.94, 0.03779, 1, 40, 24.55, -37.93, 1, 1, 40, 2.48, -34.02, 1, 1, 40, -6.93, -21.79, 1, 1, 40, 5.26, -20.8, 1, 1, 40, 21.35, -11.04, 1, 2, 40, 29.47, 2.9, 0.98368, 41, -12.45, 22.93, 0.01632, 2, 40, 31.03, 18.13, 0.63443, 41, 0.94, 30.36, 0.36557, 2, 40, 25.31, 28.37, 0.41327, 41, 6.07, 40.91, 0.58673, 2, 40, 18.24, 37.48, 0.33461, 41, 9.5, 51.91, 0.66539, 1, 41, 72.69, -4.76, 1, 1, 41, 46.6, -8.46, 1, 1, 41, 23.97, -9.63, 1, 2, 40, 55.08, -7.26, 0.84734, 41, -6.14, -3.89, 0.15266, 1, 40, 33.95, -13.26, 1], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44, 10, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 36], "width": 148, "height": 108}}, "yanj": {"yanj": {"type": "mesh", "uvs": [1, 0.54457, 1, 1, 0.8, 1, 0.6, 1, 0.4, 1, 0.2, 1, 0, 1, 0, 0.52325, 0, 0, 0.2, 0, 0.4, 0, 0.6, 0, 0.8, 0, 1, 0, 0.1971, 0.52325, 0.39441, 0.53391, 0.59869, 0.56056, 0.796, 0.55523], "triangles": [14, 8, 9, 1, 2, 17, 17, 12, 0, 5, 14, 15, 14, 9, 15, 3, 17, 2, 17, 11, 12, 4, 16, 3, 4, 15, 16, 16, 15, 11, 11, 15, 10, 3, 16, 17, 5, 15, 4, 16, 11, 17, 15, 9, 10, 1, 17, 0, 12, 13, 0, 6, 14, 5, 6, 7, 14, 7, 8, 14], "vertices": [2, 5, 43.36, -89.35, 0.396, 7, 22.86, -98.44, 0.604, 2, 5, 14.43, -74.88, 0.396, 7, -9.47, -97.88, 0.604, 2, 5, 29.01, -45.73, 0.4, 7, -8.9, -65.28, 0.6, 1, 7, -8.34, -32.69, 1, 1, 7, -7.77, -0.09, 1, 2, 5, 72.75, 41.75, 0.4, 7, -7.2, 32.5, 0.6, 2, 5, 87.33, 70.91, 0.748, 7, -6.63, 65.1, 0.252, 2, 5, 117.61, 55.77, 0.748, 7, 27.21, 64.51, 0.252, 2, 5, 150.83, 39.15, 0.648, 7, 64.36, 63.86, 0.352, 2, 5, 136.26, 10, 0.4, 7, 63.79, 31.27, 0.6, 1, 7, 63.22, -1.33, 1, 1, 7, 62.65, -33.92, 1, 2, 5, 92.52, -77.48, 0.4, 7, 62.09, -66.52, 0.6, 2, 5, 77.94, -106.64, 0.396, 7, 61.52, -99.11, 0.604, 2, 5, 103.24, 27.03, 0.4, 7, 26.65, 32.39, 0.6, 1, 7, 25.33, 0.24, 1, 1, 7, 22.86, -33.02, 1, 2, 5, 57.55, -59.27, 0.4, 7, 22.68, -65.18, 0.6], "hull": 14, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 12, 14, 14, 16, 2, 0, 0, 26], "width": 163, "height": 71}}, "zuiba": {"zuiba": {"type": "mesh", "uvs": [0.42802, 0, 0.67704, 0, 0.84776, 0, 1, 0.09561, 1, 0.20756, 1, 0.62756, 1, 0.78592, 0.85073, 1, 0.68448, 1, 0.57181, 1, 0.41129, 1, 0.27792, 1, 0.192, 1, 0.13761, 1, 0.05763, 0.94314, 0, 0.82903, 0, 0.59999, 0, 0.25211, 0, 0.17227, 0.07096, 0.07592, 0.16227, 0, 0.35139, 0.5032, 0.38026, 0.26359, 0.54819, 0.26659, 0.6689, 0.49122, 0.63216, 0.75479, 0.46685, 0.82967, 0.24118, 0.81469, 0.13097, 0.5661, 0.16246, 0.36243, 0.23856, 0.28456, 0.40912, 0.08988, 0.63216, 0.10186, 0.82896, 0.21867, 0.86832, 0.5062, 0.83684, 0.77276, 0.73712, 0.92252, 0.13884, 0.88657, 0.06275, 0.69489, 0.05225, 0.58407, 0.0575, 0.35644, 0.14934, 0.16475], "triangles": [36, 35, 7, 37, 15, 38, 35, 34, 5, 16, 39, 38, 39, 16, 40, 34, 33, 4, 40, 17, 41, 32, 1, 33, 41, 20, 31, 31, 0, 32, 15, 16, 38, 40, 16, 17, 8, 36, 7, 7, 35, 6, 8, 9, 36, 13, 37, 12, 13, 14, 37, 14, 15, 37, 35, 5, 6, 34, 4, 5, 17, 18, 41, 4, 33, 3, 33, 1, 2, 33, 2, 3, 18, 19, 41, 41, 19, 20, 32, 0, 1, 31, 20, 0, 11, 12, 37, 9, 25, 36, 10, 26, 9, 9, 26, 25, 26, 10, 27, 36, 25, 35, 27, 37, 38, 27, 21, 26, 26, 21, 24, 25, 26, 24, 21, 23, 24, 38, 28, 27, 27, 28, 21, 25, 24, 35, 35, 24, 34, 22, 23, 21, 38, 39, 28, 39, 40, 28, 28, 29, 21, 28, 40, 29, 24, 33, 34, 29, 30, 21, 21, 30, 22, 24, 23, 33, 33, 23, 32, 30, 29, 41, 29, 40, 41, 22, 30, 31, 22, 31, 23, 23, 31, 32, 31, 30, 41, 11, 37, 27, 10, 11, 27], "vertices": [1, 5, 99.24, -19.31, 1, 1, 5, 86.66, -44.48, 1, 1, 5, 78.03, -61.73, 1, 1, 5, 61.87, -72.89, 1, 1, 5, 51.96, -67.93, 1, 1, 5, 14.77, -49.34, 1, 1, 5, 0.74, -42.33, 1, 1, 5, -10.67, -17.76, 1, 1, 5, -2.27, -0.96, 1, 2, 7, -56.22, -25.67, 0.584, 5, 3.43, 10.43, 0.416, 2, 7, -55.91, -7.53, 0.424, 5, 11.54, 26.65, 0.576, 2, 7, -55.64, 7.54, 0.296, 5, 18.28, 40.13, 0.704, 1, 5, 22.62, 48.82, 1, 1, 5, 25.37, 54.31, 1, 1, 5, 34.45, 59.88, 1, 1, 5, 47.46, 60.65, 1, 1, 5, 67.74, 50.51, 1, 1, 5, 98.55, 35.11, 1, 1, 5, 105.62, 31.58, 1, 1, 5, 110.56, 20.14, 1, 1, 5, 112.67, 7.55, 1, 1, 7, -6.61, -1.62, 1, 1, 7, 17.05, -5.29, 1, 2, 7, 16.42, -24.26, 0.888, 5, 69.56, -19.65, 0.112, 2, 7, -6.05, -37.51, 0.736, 5, 43.57, -21.91, 0.264, 2, 7, -32.07, -32.91, 0.76, 5, 22.09, -6.53, 0.24, 2, 7, -39.15, -14.1, 0.832, 5, 23.81, 13.5, 0.168, 2, 7, -37.23, 11.37, 0.856, 5, 36.54, 35.64, 0.144, 2, 7, -12.4, 23.39, 0.864, 5, 64.13, 35.77, 0.136, 2, 7, 7.69, 19.48, 0.856, 5, 80.57, 23.58, 0.144, 2, 7, 15.25, 10.75, 0.856, 5, 83.62, 12.44, 0.144, 2, 7, 34.19, -8.85, 0.392, 5, 92.24, -13.42, 0.608, 2, 7, 32.56, -34.03, 0.392, 5, 79.91, -35.43, 0.608, 2, 7, 20.61, -56.07, 0.392, 5, 59.62, -50.15, 0.608, 2, 7, -7.93, -60.02, 0.392, 5, 32.17, -41.4, 0.608, 2, 7, -34.25, -56, 0.392, 5, 10.16, -26.42, 0.608, 2, 7, -48.88, -44.48, 0.592, 5, 1.93, -9.71, 0.408, 2, 7, -44.14, 23.06, 0.392, 5, 35.35, 49.17, 0.608, 2, 7, -25.02, 31.32, 0.392, 5, 56.17, 48.37, 0.608, 2, 7, -14.03, 32.32, 0.392, 5, 66.51, 44.53, 0.608, 2, 7, 8.49, 31.33, 0.392, 5, 86.4, 33.92, 0.608, 2, 7, 27.29, 20.63, 0.392, 5, 98.74, 16.15, 0.608], "hull": 21, "edges": [0, 2, 8, 10, 32, 34, 0, 40, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 44, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 16, 18, 18, 20, 72, 18, 20, 22, 22, 24, 22, 74, 74, 76, 76, 78, 78, 80, 80, 82, 62, 82, 2, 4, 6, 8, 4, 6, 10, 12, 14, 16, 12, 14, 30, 32, 30, 28, 24, 26, 28, 26, 34, 36, 36, 38, 40, 38], "width": 113, "height": 99}}}}], "events": {"atk": {}}, "animations": {"boss_attack1": {"bones": {"bone3": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 12.2, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "angle": -23.68, "curve": 0.337, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 0.4333, "angle": 8.46, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.6667}]}, "bone4": {"rotate": [{"angle": 2.23, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 12.2, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.4, "angle": -9.56, "curve": 0.337, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 0.5, "angle": 17.03, "curve": 0.362, "c2": 0.45, "c3": 0.706, "c4": 0.82}, {"time": 0.6667, "angle": 2.23}]}, "bone5": {"rotate": [{"angle": 7.15, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 12.2, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.4667, "angle": -9.56, "curve": 0.337, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 0.5667, "angle": 17.03, "curve": 0.348, "c2": 0.38, "c3": 0.685, "c4": 0.73}, {"time": 0.6667, "angle": 7.15}]}, "bone6": {"rotate": [{"angle": 5.95, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 18.51, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -21.87, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 16.17, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 5.95}]}, "bone7": {"translate": [{"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -4.25, "y": -12.72, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 6.4, "y": 23.57, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone20": {"rotate": [{"angle": -0.2}]}, "bone21": {"rotate": [{"angle": -1.94}], "scale": [{"time": 0.3333, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 0.4667, "y": 1.524, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.6667}]}, "bone22": {"rotate": [{"angle": 5.12}]}, "bone23": {"rotate": [{"angle": -1.46}]}, "bone24": {"rotate": [{"angle": 0.92}], "scale": [{"time": 0.3333, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 0.4667, "y": 1.524, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.6667}]}, "bone25": {"rotate": [{"angle": 4.73}]}, "bone26": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 21.81, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone27": {"rotate": [{"angle": 5.28, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 21.81, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": 5.28}]}, "bone28": {"rotate": [{"angle": 16.52, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 21.81, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.6667, "angle": 16.52}]}, "bone29": {"rotate": [{"angle": -2.08}]}, "bone30": {"rotate": [{"angle": 0.5}]}, "bone31": {"rotate": [{"angle": 1.32}]}, "bone33": {"rotate": [{"angle": -0.17}]}, "bone34": {"rotate": [{"angle": -0.23}]}, "bone35": {"rotate": [{"angle": -0.27}]}, "bone37": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 6.09, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -8.8, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone38": {"rotate": [{"angle": -2.13, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 6.09, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -8.8, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": -2.13}]}, "bone39": {"rotate": [{"angle": -5.56, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 6.09, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -8.8, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -5.56}]}, "bone40": {"rotate": [{"angle": -8.43, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 6.09, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -8.8, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.6667, "angle": -8.43}]}, "bone41": {"rotate": [{"angle": -3.32, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -8.8, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 6.09, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -3.32}]}, "bone43": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -9.94, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 7.54, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone44": {"rotate": [{"angle": -0.86, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -9.94, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 7.54, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -2.34, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -0.86}]}, "bone45": {"rotate": [{"angle": -2.03, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -9.94, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 7.54, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -2.34, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": -2.03}]}, "bone46": {"rotate": [{"angle": -1.05, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": -2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -9.94, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 7.54, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -1.05}]}, "bone47": {"rotate": [{"angle": -2.03, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -9.94, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 7.54, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -2.34, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": -2.03}]}, "bone48": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -9.94, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 7.54, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone49": {"rotate": [{"angle": -0.86, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -9.94, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 7.54, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -2.34, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -0.86}]}, "bone50": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -9.94, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 7.54, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone51": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -9.94, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 7.54, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone52": {"rotate": [{"angle": -1.05, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": -2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -9.94, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 7.54, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -1.05}]}, "bone53": {"translate": [{"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -49.69, "y": 170.52, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -26.91, "y": -0.34, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "target2": {"translate": [{"time": 0.3333, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.5, "y": -15.5, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667}]}, "bone54": {"translate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -49.69, "y": 170.52, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -26.91, "y": -0.34, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "target4": {"translate": [{"time": 0.3333, "curve": 0.34, "c2": 0.35, "c3": 0.679, "c4": 0.71}, {"time": 0.4684, "y": -2.01, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.6667}]}}, "events": [{"time": 0.4333, "name": "atk"}]}, "boss_idle": {"slots": {"biyan": {"attachment": [{"time": 1.3, "name": "biyan"}, {"time": 1.3667, "name": null}]}, "yanj": {"attachment": [{"time": 1.3, "name": null}, {"time": 1.3667, "name": "yanj"}]}}, "bones": {"bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 4.55, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone4": {"rotate": [{"angle": 1.29, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.55, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 1.29}]}, "bone5": {"rotate": [{"angle": 3.07, "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": 4.55, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 2, "angle": 3.07}]}, "bone6": {"rotate": [{"angle": -2.74, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -9.66, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -2.74}]}, "bone7": {"translate": [{"x": 8.73, "y": 12.08, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": 12.19, "y": 16.87, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 8.73, "y": 12.08}]}, "bone8": {"rotate": [{"angle": -3.66, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.6, "angle": -9.96, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.2667, "angle": -2.83, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.6, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "angle": -3.66}]}, "bone9": {"rotate": [{"angle": -0.23, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 0.9333, "angle": -9.96, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.2667, "angle": -7.13, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.9333, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 2, "angle": -0.23}]}, "bone10": {"rotate": [{"angle": -2.02, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -9.96, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 2, "angle": -2.02}]}, "bone11": {"rotate": [{"angle": 3.42, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.6, "angle": 9.31, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.2667, "angle": 2.64, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.6, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "angle": 3.42}]}, "bone12": {"rotate": [{"angle": 0.21, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 0.9333, "angle": 9.31, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.2667, "angle": 6.67, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.9333, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 2, "angle": 0.21}]}, "bone13": {"rotate": [{"angle": 1.89, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 9.31, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 2, "angle": 1.89}]}, "bone14": {"rotate": [{"angle": 1.82, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": -6.2, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 1.82}]}, "bone15": {"rotate": [{"angle": -3.02, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6667, "angle": -6.2, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -3.02}]}, "bone16": {"rotate": [{"angle": -6.2, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 5, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -6.2}]}, "bone17": {"rotate": [{"angle": -3.48, "curve": 0.32, "c2": 0.29, "c3": 0.674, "c4": 0.69}, {"time": 0.3667, "angle": 1.82, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.7, "angle": 5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.7, "angle": -6.2, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2, "angle": -3.48}]}, "bone18": {"rotate": [{"angle": -5.71, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 0.0333, "angle": -6.2, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -3.02, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.0333, "angle": 5, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 2, "angle": -5.71}]}, "bone19": {"rotate": [{"angle": 3.93, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.1667, "angle": -6.2, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.8333, "angle": 1.82, "curve": 0.351, "c2": 0.4, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 3.93}]}, "bone20": {"rotate": [{"angle": -0.2}]}, "bone21": {"rotate": [{"angle": -1.94}]}, "bone22": {"rotate": [{"angle": 5.12}]}, "bone23": {"rotate": [{"angle": -1.46}]}, "bone24": {"rotate": [{"angle": 0.92}]}, "bone25": {"rotate": [{"angle": 4.73}]}, "bone26": {"rotate": [{"angle": 12.71, "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": 15.24, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 2, "angle": 12.71}]}, "bone27": {"rotate": [{"angle": 14.6, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "angle": 15.24, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.7667, "angle": 4.33, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.1, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2, "angle": 14.6}]}, "bone28": {"rotate": [{"angle": 8.97, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.4333, "angle": 15.24, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.7667, "angle": 10.92, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.4333, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2, "angle": 8.97}]}, "bone29": {"rotate": [{"angle": -2.08}]}, "bone30": {"rotate": [{"angle": 0.5}]}, "bone31": {"rotate": [{"angle": 1.32}]}, "bone33": {"rotate": [{"angle": -0.17}]}, "bone34": {"rotate": [{"angle": -0.23}]}, "bone35": {"rotate": [{"angle": -0.27}]}, "bone37": {"rotate": [{"angle": 2.96, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 10.44, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 2.96}]}, "bone38": {"rotate": [{"angle": 7.48, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 10.44, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 7.48}]}, "bone39": {"rotate": [{"angle": 10.44, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 10.44}]}, "bone40": {"rotate": [{"angle": 7.48, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 10.44, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 7.48}]}, "bone41": {"rotate": [{"angle": 2.96, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 10.44, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 2.96}]}, "bone42": {"rotate": [{"angle": -19.07, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": -47.45, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.1667, "angle": -6.8, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.5, "angle": 9.31, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": -19.07}]}, "bone43": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 7.66, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone44": {"rotate": [{"angle": 2.17, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.66, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 2.17}]}, "bone45": {"rotate": [{"angle": 5.48, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 7.66, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 5.48}]}, "bone46": {"rotate": [{"angle": 7.66, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 7.66}]}, "bone47": {"rotate": [{"angle": 5.48, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 7.66, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 5.48}]}, "bone48": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 7.66, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone49": {"rotate": [{"angle": 2.17, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.66, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 2.17}]}, "bone50": {"rotate": [{"angle": 5.48, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 7.66, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 5.48}]}, "bone51": {"rotate": [{"angle": 1.85, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.7, "angle": 7.66, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2, "angle": 1.85}]}, "bone52": {"rotate": [{"angle": 1.85, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.7, "angle": 7.66, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2, "angle": 1.85}]}}, "deform": {"default": {"yanj": {"yanj": [{"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 4, "vertices": [28.44403, -9.05876, 29.56798, 4.10422, 21.85205, -12.92255, 25.28917, -2.22626, 26.85455, 8.32359, 25.24359, -0.76443, 24.93063, -11.27084, 27.35345, 0.59232, 25.83391, -12.42113, 28.66464, -0.05545, 0, 0, 0, 0, -24.18248, 9.34251, -25.84665, -2.0087, -29.78647, 13.69304, -32.7794, -0.50275, -32.82486, 0.95912, -31.16708, 0.23589, -30.00407, 13.41279, -32.85478, -0.84947, -25.01944, 5.95054, -25.13779, -5.42965], "curve": 0.25, "c3": 0.75}, {"time": 1.5}]}}}}, "die": {"slots": {"biyan": {"attachment": [{"time": 0.2333, "name": "biyan"}]}, "yanj": {"attachment": [{"time": 0.2333, "name": null}]}}, "bones": {"bone": {"rotate": [{"curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 0.2333, "angle": -12.95, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.4667}], "translate": [{"curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 0.2333, "y": 53.8, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.4667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "y": 10.22, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6}]}, "bone2": {"rotate": [{"time": 0.1, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 0.2667, "angle": -13.74, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.5}], "translate": [{"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": -46.95}]}, "bone3": {"rotate": [{"curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1, "angle": 6.58, "curve": 0.304, "c2": 0.24, "c3": 0.655, "c4": 0.63}, {"time": 0.2667, "angle": -13.98, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.5}], "translate": [{"curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 0.2667, "x": 1.75, "y": 13.57, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.5}]}, "bone4": {"rotate": [{"angle": -1.13, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 6.58, "curve": 0.304, "c2": 0.24, "c3": 0.655, "c4": 0.63}, {"time": 0.3333, "angle": -7.84, "curve": 0.356, "c2": 0.41, "c3": 0.707, "c4": 0.81}, {"time": 0.5, "angle": -1.13}]}, "bone5": {"rotate": [{"angle": -3.46, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 6.58, "curve": 0.304, "c2": 0.24, "c3": 0.655, "c4": 0.63}, {"time": 0.4, "angle": -7.84, "curve": 0.342, "c2": 0.36, "c3": 0.682, "c4": 0.72}, {"time": 0.5, "angle": -3.46, "curve": "stepped"}, {"time": 1.0667, "angle": -3.46}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -29.21, "y": 38.17}]}, "bone20": {"rotate": [{"angle": -0.2}]}, "bone21": {"rotate": [{"angle": -1.94}]}, "bone22": {"rotate": [{"angle": 5.12}]}, "bone23": {"rotate": [{"angle": -1.46}]}, "bone24": {"rotate": [{"angle": 0.92}]}, "bone25": {"rotate": [{"angle": 4.73}]}, "bone26": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 7.07, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -21.46, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone27": {"rotate": [{"angle": -6.09, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 7.07, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -21.46, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5, "angle": -6.09}]}, "bone28": {"rotate": [{"angle": -15.37, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 7.07, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -21.46, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.5, "angle": -15.37}]}, "bone29": {"rotate": [{"angle": -2.08}]}, "bone30": {"rotate": [{"angle": 0.5}]}, "bone31": {"rotate": [{"angle": 1.32}]}, "bone33": {"rotate": [{"angle": -0.17}]}, "bone34": {"rotate": [{"angle": -0.23}]}, "bone35": {"rotate": [{"angle": -0.27}]}, "bone37": {"rotate": [{"curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1, "angle": 12.05, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.3, "angle": -8.95, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5, "angle": -33.86}]}, "bone38": {"rotate": [{"curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1, "angle": 12.05, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.3, "angle": -8.95, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5, "angle": -33.86}]}, "bone39": {"rotate": [{"curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1, "angle": 12.05, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.3, "angle": -8.95, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5, "angle": -33.86}]}, "bone40": {"rotate": [{"curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1, "angle": 12.05, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.3, "angle": -8.95, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5, "angle": -33.86}]}, "bone41": {"rotate": [{"curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1, "angle": 12.05, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.3, "angle": -8.95, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5, "angle": -33.86}]}, "bone43": {"rotate": [{"time": 0.1333, "curve": 0.316, "c2": 0.28, "c3": 0.66, "c4": 0.65}, {"time": 0.2667, "angle": 15.85, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.5, "angle": 47.34}]}, "bone44": {"rotate": [{"time": 0.1333, "curve": 0.316, "c2": 0.28, "c3": 0.66, "c4": 0.65}, {"time": 0.2667, "angle": 15.85, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.5, "angle": 47.34}]}, "bone45": {"rotate": [{"time": 0.1333, "curve": 0.316, "c2": 0.28, "c3": 0.66, "c4": 0.65}, {"time": 0.2667, "angle": 15.85, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.5, "angle": 47.34}]}, "bone46": {"rotate": [{"time": 0.1333, "curve": 0.316, "c2": 0.28, "c3": 0.66, "c4": 0.65}, {"time": 0.2667, "angle": 15.85, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.5, "angle": 47.34}]}, "bone47": {"rotate": [{"time": 0.1333, "curve": 0.316, "c2": 0.28, "c3": 0.66, "c4": 0.65}, {"time": 0.2667, "angle": 15.85, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.5, "angle": 47.34}]}, "bone48": {"rotate": [{"time": 0.1333, "curve": 0.316, "c2": 0.28, "c3": 0.66, "c4": 0.65}, {"time": 0.2667, "angle": 15.85, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.5, "angle": 47.34}]}, "bone49": {"rotate": [{"time": 0.1333, "curve": 0.316, "c2": 0.28, "c3": 0.66, "c4": 0.65}, {"time": 0.2667, "angle": 15.85, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.5, "angle": 47.34}]}, "bone50": {"rotate": [{"time": 0.1333, "curve": 0.316, "c2": 0.28, "c3": 0.66, "c4": 0.65}, {"time": 0.2667, "angle": 15.85, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.5, "angle": 47.34}]}, "bone51": {"rotate": [{"time": 0.1333, "curve": 0.316, "c2": 0.28, "c3": 0.66, "c4": 0.65}, {"time": 0.2667, "angle": 15.85, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.5, "angle": 47.34}]}, "bone52": {"rotate": [{"time": 0.1333, "curve": 0.316, "c2": 0.28, "c3": 0.66, "c4": 0.65}, {"time": 0.2667, "angle": 15.85, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.5, "angle": 47.34}]}, "bone53": {"translate": [{"x": -0.63, "y": 4.12, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.1333, "curve": 0.304, "c2": 0.24, "c3": 0.655, "c4": 0.63}, {"time": 0.3, "x": -14.33, "y": 93.24, "curve": 0.366, "c2": 0.46, "c3": 0.725, "c4": 0.89}, {"time": 0.5, "x": -0.63, "y": 4.12}]}, "bone54": {"translate": [{"x": -0.63, "y": 4.12, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.1333, "curve": 0.304, "c2": 0.24, "c3": 0.655, "c4": 0.63}, {"time": 0.3, "x": -14.33, "y": 93.24, "curve": 0.366, "c2": 0.46, "c3": 0.725, "c4": 0.89}, {"time": 0.5, "x": -0.63, "y": 4.12}]}}}, "hurt": {"slots": {"biyan": {"attachment": [{"time": 0.1, "name": "biyan"}]}, "yanj": {"attachment": [{"time": 0.1, "name": null}]}}, "bones": {"bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 7.83, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -9.35, "curve": 0.25, "c3": 0.75}, {"time": 0.4}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 21, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 0.2333, "x": 29.97, "curve": 0.371, "c2": 0.48, "c3": 0.752}, {"time": 0.4}]}, "bone4": {"rotate": [{"angle": -1.41, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 7.83, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -6.28, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.4, "angle": -1.41}], "translate": [{"curve": 0.246, "c3": 0.636, "c4": 0.55}, {"time": 0.2333, "x": -11.85, "curve": 0.381, "c2": 0.54, "c3": 0.743}, {"time": 0.4}]}, "bone5": {"rotate": [{"angle": -3.73, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 7.83, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.28, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.4, "angle": -3.73}]}, "bone6": {"rotate": [{"curve": 0.27, "c3": 0.618, "c4": 0.41}, {"time": 0.1333, "angle": 9.57, "curve": 0.321, "c2": 0.29, "c3": 0.679, "c4": 0.71}, {"time": 0.3, "angle": -15.18, "curve": 0.382, "c2": 0.59, "c3": 0.73}, {"time": 0.4}]}, "bone7": {"translate": [{"x": 1.02, "y": -5.46, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 8.11, "y": 31.14, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 2.78, "y": -14.85, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.4, "x": 1.02, "y": -5.46}]}, "bone8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 10.12, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -6.91, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "bone9": {"rotate": [{"angle": -1.96, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 10.12, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -6.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.4, "angle": -1.96}]}, "bone10": {"rotate": [{"angle": -4.95, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 10.12, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -6.91, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.4, "angle": -4.95}]}, "bone11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -12.02, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 5.47, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "bone12": {"rotate": [{"angle": 1.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -12.02, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 5.47, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.4, "angle": 1.55}]}, "bone13": {"rotate": [{"angle": 3.92, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -12.02, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 5.47, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.4, "angle": 3.92}]}, "bone14": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 8.15, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -8.98, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "bone15": {"rotate": [{"angle": -2.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 8.15, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -8.98, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.4, "angle": -2.55}]}, "bone16": {"rotate": [{"angle": -6.43, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 8.15, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -8.98, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.4, "angle": -6.43}]}, "bone17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 8.15, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -8.98, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "bone18": {"rotate": [{"angle": -2.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 8.15, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -8.98, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.4, "angle": -2.55}]}, "bone19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 8.15, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -8.98, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "bone20": {"rotate": [{"angle": -0.2}]}, "bone21": {"rotate": [{"angle": -1.94}]}, "bone22": {"rotate": [{"angle": 5.12}]}, "bone23": {"rotate": [{"angle": -1.46}], "translate": [{"curve": 0.246, "c3": 0.636, "c4": 0.55}, {"time": 0.2333, "x": -27.34, "curve": 0.381, "c2": 0.54, "c3": 0.743}, {"time": 0.4}]}, "bone24": {"rotate": [{"angle": 0.92}]}, "bone25": {"rotate": [{"angle": 4.73}]}, "bone29": {"rotate": [{"angle": -2.08}]}, "bone30": {"rotate": [{"angle": 0.5}]}, "bone31": {"rotate": [{"angle": 1.32}]}, "bone33": {"rotate": [{"angle": -0.17}]}, "bone34": {"rotate": [{"angle": -0.23}]}, "bone35": {"rotate": [{"angle": -0.27}]}, "bone37": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 5.81, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -15.61, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "bone38": {"rotate": [{"angle": -1.21, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 5.81, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -15.61, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.4, "angle": -1.21}]}, "bone39": {"rotate": [{"angle": -3.5, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 5.81, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -15.61, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.4, "angle": -3.5}]}, "bone40": {"rotate": [{"angle": -6.33, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 5.81, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -15.61, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.4, "angle": -6.33}]}, "bone41": {"rotate": [{"angle": -12.11, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 5.81, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -15.61, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 0.4, "angle": -12.11}]}, "bone43": {"rotate": [{"angle": 7.19, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -10.6, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 25.34, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.4, "angle": 7.19}]}, "bone44": {"rotate": [{"angle": 18.13, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.0667, "angle": 7.19, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -10.6, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 25.34, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.4, "angle": 18.13}]}, "bone45": {"rotate": [{"angle": 25.34, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.0667, "angle": 18.15, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -10.6, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 25.34}]}, "bone46": {"rotate": [{"angle": 7.19, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -10.6, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 25.34, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.4, "angle": 7.19}]}, "bone47": {"rotate": [{"angle": 7.19, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -10.6, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 25.34, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.4, "angle": 7.19}]}, "bone48": {"rotate": [{"angle": 7.19, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -10.6, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 25.34, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.4, "angle": 7.19}]}, "bone49": {"rotate": [{"angle": 18.13, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.0667, "angle": 7.19, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -10.6, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 25.34, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.4, "angle": 18.13}]}, "bone50": {"rotate": [{"angle": 25.34, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.0667, "angle": 18.15, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -10.6, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 25.34}]}, "bone51": {"rotate": [{"angle": 15.14, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "angle": 25.34, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -10.6, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.4, "angle": 15.14}]}, "bone52": {"rotate": [{"angle": 15.14, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "angle": 25.34, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -10.6, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.4, "angle": 15.14}]}, "bone53": {"translate": [{"time": 0.1, "curve": 0.31, "c2": 0.26, "c3": 0.677, "c4": 0.7}, {"time": 0.2333, "x": 32.79, "y": 86.61, "curve": 0.382, "c2": 0.59, "c3": 0.73}, {"time": 0.4}]}, "bone54": {"translate": [{"time": 0.1, "curve": 0.31, "c2": 0.26, "c3": 0.677, "c4": 0.7}, {"time": 0.3, "x": 40.9, "y": 61.53, "curve": 0.382, "c2": 0.59, "c3": 0.73}, {"time": 0.4}]}}}}}