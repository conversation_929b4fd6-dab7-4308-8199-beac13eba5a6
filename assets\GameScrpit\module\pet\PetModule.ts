import { GameData } from "../../game/GameData";
import data from "../../lib/data/data";
import { Pet<PERSON><PERSON> } from "./PetApi";
import { PetConfig } from "./PetConfig";
import { PetData } from "./PetData";
import { PetRoute } from "./PetRoute";
import { PetService } from "./PetService";
import { PetViewModel } from "./PetViewModel";

export class PetModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): PetModule {
    if (!GameData.instance.PetModule) {
      GameData.instance.PetModule = new PetModule();
    }
    return GameData.instance.PetModule;
  }
  private _data = new PetData();
  private _api = new PetApi();
  private _route = new PetRoute();
  private _config = new PetConfig();
  private _service = new PetService();
  private _viewModel = new PetViewModel();

  public static get data() {
    return this.instance._data;
  }
  public static get api() {
    return this.instance._api;
  }
  public static get config() {
    return this.instance._config;
  }
  public static get service() {
    return this.instance._service;
  }
  public static get viewModel() {
    return this.instance._viewModel;
  }
  protected saveKey(): string {
    return this.constructor.name;
  }
  public init(data?: any, completedCallback?: Function) {
    this._data = new PetData();
    this._api = new PetApi();
    this._route = new PetRoute();
    this._config = new PetConfig();
    this._service = new PetService();
    this._viewModel = new PetViewModel();

    // 初始化模块
    this._data.init();
    PetModule.api.getAllPet((data) => {
      this._route.init();
      completedCallback && completedCallback();
    });
  }
}
