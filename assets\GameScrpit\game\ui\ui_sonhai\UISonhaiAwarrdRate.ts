import { _decorator, Component, Label, Node } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { SonhaiModule, sonhai_activityId } from "../../../module/sonhai/SonhaiModule";
import ToolExt from "../../common/ToolExt";
import { UIMgr } from "../../../lib/ui/UIMgr";
import TickerMgr from "../../../lib/ticker/TickerMgr";
import { dtTime } from "../../BoutStartUp";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "../../../lib/utils/FmUtils";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UISonhaiAwarrdRate")
export class UISonhaiAwarrdRate extends UINode {
  protected _openAct: boolean = true; //打开动作
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_HD_SONHAI}?prefab/ui/UISonhaiAwarrdRate`;
  }

  private _tickMgrIdList: number[] = [];

  protected onEvtShow(): void {
    this.setRareLbl();
    this.loadRareList();
    this.loadCommonList();
  }

  private async loadCommonList() {
    let db = await SonhaiModule.data.getSonhaiDb(sonhai_activityId);
    let subList = db.smallPrizeVO.subList;

    let maxRate = 0;
    for (let i = 0; i < subList.length; i++) {
      maxRate += subList[i].rate;
    }

    for (let i = 0; i < subList.length; i++) {
      let tickid = TickerMgr.setTimeout(dtTime * i, () => {
        let info = subList[i];
        let node = ToolExt.clone(this.getNode("commonItem"), this);
        this.getNode("content_common").addChild(node);
        node.active = true;

        let id = info.rewardList[0];
        let num = info.rewardList[1];

        FmUtils.setItemNode(node.getChildByName("Item_small"), id, num);

        let rate = (info.rate / maxRate) * 100;

        node.getChildByName("lbl_common_rare").getComponent(Label).string = rate.toFixed(1) + "%";
      });

      this._tickMgrIdList.push(tickid);
    }
  }

  private async setRareLbl() {
    let db = await SonhaiModule.data.getSonhaiDb(sonhai_activityId);
    let round = db.bigPrizeVO.round;
    let curCostCount = SonhaiModule.data.curCostCount;

    this.getNode("lbl_rare").getComponent(Label).string = curCostCount + "/" + round + "次必出大奖";
  }

  private async loadRareList() {
    let db = await SonhaiModule.data.getSonhaiDb(sonhai_activityId);
    let subList = db.bigPrizeVO.subList;
    let subArr = this.checkRepetition(subList);

    for (let i = 0; i < subArr.length; i++) {
      let tickid = TickerMgr.setTimeout((dtTime / 2) * i, () => {
        let node = ToolExt.clone(this.getNode("Item"), this);
        this.getNode("content_rare").addChild(node);
        node.active = true;

        let info = subArr[i];
        let id = info.rewardList[0];
        let num = info.rewardList[1];
        FmUtils.setItemNode(node, id, num);
      });

      this._tickMgrIdList.push(tickid);
    }
  }

  private checkRepetition(subList) {
    let arr = [];
    let subArr = [];
    log.log("subList", subList);
    for (let i = 0; i < subList.length; i++) {
      let info = subList[i];
      if (arr.indexOf(info.rewardList[0]) == -1) {
        arr.push(info.rewardList[0]);
        subArr.push(info);
      }
    }

    log.log("subArr", subArr);
    return subArr;
  }

  private on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  protected onEvtClose(): void {
    for (let i = 0; i < this._tickMgrIdList.length; i++) {
      TickerMgr.clearTimeout(this._tickMgrIdList[i]);
    }
  }
}
