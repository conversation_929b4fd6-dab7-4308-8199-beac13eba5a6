import { _decorator, EventTouch, Label, RichText } from "cc";
import { BaseCtrl } from "../../../platform/src/core/BaseCtrl";
import { PlayerDetailMessage, PlayerSimpleMessage } from "../net/protocol/Player";
import ToolExt from "./ToolExt";
import { PlayerModule } from "../../module/player/PlayerModule";
import { UIMgr } from "../../lib/ui/UIMgr";
import { PlayerRouteName } from "../../module/player/PlayerConstant";
import { AudioMgr } from "../../../platform/src/AudioHelper";
const { ccclass, property } = _decorator;

@ccclass("BtnPlayerCtrl")
export class BtnPlayerCtrl extends BaseCtrl {
  private playerBaseMessage: PlayerSimpleMessage | PlayerDetailMessage = null;

  start() {
    super.start();
  }
  public setPlayer(args: PlayerSimpleMessage | PlayerDetailMessage) {
    //
    this.playerBaseMessage = args;
    if (!args) {
      this.getNode("lbl_name").getComponent(Label).string = "";
      this.getNode("lbl_level").getComponent(RichText).string = "";
      return;
    }

    let nickName = "";
    let roleSkinId;
    let level;
    if ("simpleMessage" in args) {
      roleSkinId = args.simpleMessage.avatarList[0];
      nickName = args.simpleMessage.nickname;
      level = args.simpleMessage.level;
    } else {
      roleSkinId = args.avatarList[0];
      nickName = args.nickname;
      level = args.level;
    }
    let horseId;
    if ("horseMessage" in args) {
      horseId = args.horseMessage.horseId;
    } else {
      horseId = args.horseId;
    }

    ToolExt.loadUIRole(this.getNode("spine_point"), roleSkinId, horseId, "renderScale21", this as any);
    this.getNode("lbl_name").getComponent(Label).string = nickName;

    let configLeader = PlayerModule.data.getConfigLeaderData(level);
    this.getNode("lbl_level").getComponent(RichText).string = `${configLeader.jingjie2}`;
  }
  update(deltaTime: number) {}

  private onClickPlayer(event: EventTouch) {
    AudioMgr.instance.playEffect(518);
    if (!this.playerBaseMessage) {
      return;
    }
    if ("simpleMessage" in this.playerBaseMessage) {
      UIMgr.instance.showDialog(PlayerRouteName.UIPlayerOtherMsg, this.playerBaseMessage.simpleMessage);
    } else {
      UIMgr.instance.showDialog(PlayerRouteName.UIPlayerOtherMsg, this.playerBaseMessage);
    }
  }
}
