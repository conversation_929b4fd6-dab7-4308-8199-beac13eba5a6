import { _decorator, Node } from "cc";
import { BaseCtrl } from "../BaseCtrl";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
// export class
// 路由配置
const log = Logger.getLoger(LOG_LEVEL.STOP);
export interface RouteConfig {
  bundle: string;
  url: string;
  // 添加到节点
  node?: Node;
  transparent?: boolean;
  parentNode?: Node;
  // 控制脚本
  baseCtrl?: BaseCtrl;
  // 单例节点
  isSingleton?: boolean;
  /**这个页面会调用的路由集合 */
  nextHop: RouteNode[]; //
  exit: string | null; // 关闭页面的节点名称
}
export interface RouteNode {
  pageTag?: string; // 页面标识
  via: string; // 触发路由的节点名称
  des: new () => any; // 目标路由类型
  desPageTag?: string; // 目标路由页面标识
  viaType?: string; // 触发路由的操作类型，如click, touch 默认不写为click
}
export interface RouteTable {
  [key: string]: RouteConfig;
}
// 定义一个全局注册表
const AllRouteConfig: {
  classes: Map<Function, any>;
  properties: Map<Function, Map<string, any>>;
} = {
  classes: new Map(),
  properties: new Map(),
};

// 路由参数【路由跳转的时候携带的参数】
export class RouteShowArgs {
  public title?: string; // 路由标题
  public payload?: any; // 业务数据
  public pageTag?: string; // 页面tag
  public isSilent?: boolean = false; // 是否静默显示（无动画）
  public isModal?: boolean = false; // 是否显示遮罩
  public clearTop?: boolean = false; // 是否清除顶部节点
  public onCloseBack?: Function;
  public onUIReady?: Function;
}

const RouteFunctionMap: Map<string, Function> = new Map();

// 边
export type PageEdge = {
  via: string;
  pageTag: string;
  des: PageNode;
  src: PageNode;
};
// 顶点
type PageNode = {
  id: string;
  pageTag: string;
  neighbor: PageEdge[];
};

// 图
type RouteGraph = {
  nodes: PageNode[];
};
const routeGraph: RouteGraph = {
  nodes: [],
};
export class RouteTableManager {
  private static _instance: RouteTableManager;
  private constructor() {
    this.buildRouteGraph();
    log.log("routeGraph", routeGraph);
  }
  public static get instance(): RouteTableManager {
    if (!this._instance) {
      this._instance = new RouteTableManager();
    }
    return this._instance;
  }

  // 获取装饰器信息
  getRouteConfig<T>(type: new () => T): RouteConfig {
    return AllRouteConfig.classes.get(type);
  }
  // 构建路由图
  buildRouteGraph() {
    // 生成所有的顶点
    let routeNodeMap = new Map<string, PageNode>();
    for (const [type, config] of AllRouteConfig.classes) {
      let routeConfig = config as RouteConfig;

      const node: PageNode = {
        id: routeConfig.url,
        pageTag: null,
        neighbor: [],
      };
      routeConfig.nextHop.forEach((item) => {
        if (item.desPageTag) {
          if (!routeNodeMap.get(AllRouteConfig.classes.get(item.des).url + item.desPageTag)) {
            const node_p: PageNode = {
              id: AllRouteConfig.classes.get(item.des).url,
              pageTag: item.desPageTag,
              neighbor: [],
            };
            routeGraph.nodes.push(node_p);
            routeNodeMap.set(node_p.id + node_p.pageTag, node_p);
          }
        }
      });
      routeGraph.nodes.push(node);
    }
    // 生成所有的边
    routeGraph.nodes.forEach((node) => {
      let routeConfig = AllRouteConfig.classes.get(RouteFunctionMap.get(node.id)) as RouteConfig;

      routeConfig.nextHop.forEach((item) => {
        let desNode = routeGraph.nodes.find(
          (pageNode) => pageNode.id == AllRouteConfig.classes.get(item.des).url && pageNode.pageTag == item.desPageTag
        );
        if (node.pageTag == item.pageTag) {
          const edge: PageEdge = {
            via: item.via,
            pageTag: item.desPageTag,
            des: desNode,
            src: node,
          };
          desNode.neighbor.push(edge);
          node.neighbor.push(edge);
        }
      });
    });
  }

  findShortestPath(src: string, des: string, srcTag?: string, desTag?: string) {
    let srcNode = routeGraph.nodes.find((node) => node.id == src && node.pageTag == srcTag);
    let desNode = routeGraph.nodes.find((node) => node.id == des && node.pageTag == desTag);
    log.log("srcNode", srcNode, "desNode", desNode);

    if (!srcNode || !desNode) {
      return [];
    }
    const queue: { node: PageNode; path: PageNode[] }[] = [];
    let shortestPath: PageNode[] = [];
    const visited = new Set<PageNode>();
    queue.push({ node: srcNode, path: [srcNode] });
    visited.add(srcNode);
    while (queue.length > 0) {
      const current = queue.shift()!;

      if (current.node === desNode) {
        if (shortestPath.length === 0 || current.path.length < shortestPath.length) {
          shortestPath = current.path;
        }
        continue;
      }

      current.node.neighbor.forEach((edge) => {
        if (!visited.has(edge.des)) {
          visited.add(edge.des);
          queue.push({
            node: edge.des,
            path: [...current.path, edge.des],
          });
        }
      });
    }
    return shortestPath;
  }
  findPath(src: string, des: string, srcTag?: string, desTag?: string) {
    let srcNode = routeGraph.nodes.find((node) => node.id == src && node.pageTag == srcTag);
    let desNode = routeGraph.nodes.find((node) => node.id == des && node.pageTag == desTag);
    log.log("srcNode", srcNode, "desNode", desNode);
    let path = [];
    const visited = new Set<PageNode>();
    const stack: { node: PageNode; path: PageEdge[] }[] = [];
    stack.push({ node: srcNode, path: [] });
    visited.add(srcNode);

    while (stack.length > 0) {
      const current = stack.pop();
      if (current.node === desNode) {
        return current.path;
      }

      current.node.neighbor.forEach((edge) => {
        if (!visited.has(edge.des)) {
          visited.add(edge.des);
          stack.push({
            node: edge.des,
            path: [...current.path, edge],
          });
        }
      });
    }
    return path;
  }
}

export function routeConfig(options: RouteConfig) {
  return function (target: Function) {
    AllRouteConfig.classes.set(target, options);
    RouteFunctionMap.set(options.url, target);
  };
}
