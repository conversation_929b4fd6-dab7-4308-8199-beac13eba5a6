import { _decorator, Component, instantiate, Label, Node, sp, tween, v3 } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import ToolExt from "../../common/ToolExt";
import { JsonMgr } from "../../mgr/JsonMgr";
import Formate from "../../../lib/utils/Formate";
import { tweenTagEnum } from "../../GameDefine";
import ResMgr from "../../../lib/common/ResMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { ItemCtrl } from "../../common/ItemCtrl";
import { AudioMgr, AudioName } from "../../../../platform/src/AudioHelper";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
const { ccclass, property } = _decorator;

@ccclass("UIClubFightFinish")
export class UIClubFightFinish extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_CLUB}?prefab/ui/UIClubFightFinish`;
  }

  // private _bossId: number;
  private _monsterId: number;

  private _resAddList: number[] = [];

  private _hurt: number;
  public init(args: any): void {
    super.init(args);
    this._resAddList = args.resAddList;
    this._monsterId = JsonMgr.instance.jsonList.c_unionBoss[args.bossId].monsterId;
    // let spineId = monster.spineId;
    this._hurt = args.hurt;

    // this._bossId = args.bossId;
  }

  protected onEvtShow(): void {
    TipsMgr.setEnableTouch(true);

    this.node.on(
      Node.EventType.TOUCH_END,
      () => {
        UIMgr.instance.back();
      },
      this
    );
    AudioMgr.instance.playEffect(AudioName.Effect.战斗胜利);
    this.getNode("bg").scale = v3(0, 0, 0);
    this.getNode("tytanchuang")
      .getComponent(sp.Skeleton)
      .setCompleteListener(() => {
        this.getNode("tytanchuang").getComponent(sp.Skeleton).setAnimation(0, "zi_tiaozhanchenggong_1", true);
      });
    this.getNode("tytanchuang").getComponent(sp.Skeleton).setAnimation(0, "zi_tiaozhanchenggong", false);

    // let bossInfo = JsonMgr.instance.jsonList.c_monsterShow[this._bossId];
    // let bossInfo = JsonMgr.instance.jsonList.c_roleShow[this._bossId];

    let monster = JsonMgr.instance.jsonList.c_monsterShow[this._monsterId];

    this.getNode("boss_hurt").getComponent(Label).string = `您对${monster.name}造成了${this._hurt}的伤害`;

    tween(this.getNode("bg"))
      .tag(tweenTagEnum.TopItemAwardPop_Tag)
      .to(0.1, { scale: v3(1, 1, 1) })
      .call(() => {
        let list = ToolExt.traAwardItemMapList(this._resAddList);
        for (let i = 0; i < list.length; i++) {
          let node = instantiate(this.getNode("Item"));
          node.active = true;
          let info = list[i];
          let c_item_info = JsonMgr.instance.getConfigItem(info.id);

          this.getNode("itemContent").addChild(node);
          node.getComponent(ItemCtrl).setItemId(c_item_info.id, info.num);
        }
      })
      .start();
  }
}
