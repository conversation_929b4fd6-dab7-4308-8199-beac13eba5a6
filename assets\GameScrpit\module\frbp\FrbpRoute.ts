import { UIFrbpAward } from "../../game/ui/ui_frbp/UIFrbpAward";
import { UIFrbpCj } from "../../game/ui/ui_frbp/UIFrbpCj";
import { UIFrbpLb } from "../../game/ui/ui_frbp/UIFrbpLb";
import { UIFrbpMain } from "../../game/ui/ui_frbp/UIFrbpMain";
import { UIFrbpRank } from "../../game/ui/ui_frbp/UIFrbpRank";
import { UIFrbpSd } from "../../game/ui/ui_frbp/UIFrbpSd";
import { UIFrbpTs } from "../../game/ui/ui_frbp/UIFrbpTs";
import { Recording, RecordingMap } from "../../lib/ui/recordingMap";

export enum FrbpRouteItem {
  UIFrbpMain = "UIFrbpMain",
  UIFrbpAward = "UIFrbpAward",
  UIFrbpRank = "UIFrbpRank",
  UIFrbpLb = "UIFrbpLb",
  UIFrbpCj = "UIFrbpCj",
  UIFrbpSd = "UIFrbpSd",
  UIFrbpTs = "UIFrbpTs",
}

export class FrbpRoute {
  rotueTables: Recording[] = [
    {
      node: UIFrbpMain,
      uiName: FrbpRouteItem.UIFrbpMain,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UIFrbpAward,
      uiName: FrbpRouteItem.UIFrbpAward,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UIFrbpRank,
      uiName: FrbpRouteItem.UIFrbpRank,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UIFrbpLb,
      uiName: FrbpRouteItem.UIFrbpLb,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UIFrbpCj,
      uiName: FrbpRouteItem.UIFrbpCj,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UIFrbpSd,
      uiName: FrbpRouteItem.UIFrbpSd,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UIFrbpTs,
      uiName: FrbpRouteItem.UIFrbpTs,
      keep: false,
      relevanceUIList: [],
    },
  ];

  public async init() {
    this.rotueTables.forEach((item) => {
      RecordingMap.instance.addRecording(item.uiName, item);
    });
  }
}
