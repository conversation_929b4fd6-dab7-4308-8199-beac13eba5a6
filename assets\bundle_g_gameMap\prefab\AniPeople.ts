import { tween } from "cc";
import { randomRangeInt } from "cc";
import { v3 } from "cc";
import { sp } from "cc";
import { _decorator, Component, Node } from "cc";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
const { ccclass, property } = _decorator;

@ccclass("AniPeople")
export class AniPeople extends BaseCtrl {
  @property(Node)
  public nodeAniPeople: Node = null;

  @property(Node)
  public nodeAniYao: Node = null;

  @property(Node)
  public nodeAniMing: Node = null;

  protected onLoad(): void {
    this.nodeAniPeople.active = false;
    this.nodeAniYao.active = false;
  }

  playAni(raceType: number, nodeAniGate: Node) {
    this.nodeAniMing.active = false;
    this.nodeAniYao.active = false;
    this.nodeAniPeople.active = false;
    if (raceType == 3) {
      this.playYaoAni(nodeAniGate);
    } else if (raceType == 4) {
      this.playMingAni(nodeAniGate);
    } else {
      this.playPeopleAni(nodeAniGate);
    }
  }

  // 人族动画
  public playPeopleAni(nodeAniGate: Node) {
    this.nodeAniPeople.active = true;
    this.nodeAniPeople.setPosition(nodeAniGate.getPosition().x, nodeAniGate.getPosition().y - 280);

    // 播放动画
    let aniPeople = this.nodeAniPeople.getComponent(sp.Skeleton);

    let rIdx = randomRangeInt(1, 6);
    aniPeople.setAnimation(0, `ren${rIdx}_1`, false);

    aniPeople.setCompleteListener(() => {
      aniPeople.setCompleteListener(null);
      aniPeople.setAnimation(0, `ren${rIdx}_2`, true);

      tween(this.nodeAniPeople)
        .to(1, { position: v3(0, 0, 0) })
        .call(() => {
          this.node.destroy();
        })
        .start();
    });
  }

  // 妖族动画
  public playYaoAni(nodeAniGate: Node) {
    this.nodeAniYao.active = true;
    this.nodeAniYao.setPosition(nodeAniGate.getPosition().x, nodeAniGate.getPosition().y);

    // 播放动画
    let aniYao = this.nodeAniYao.getComponent(sp.Skeleton);

    let rIdx = randomRangeInt(1, 4);
    aniYao.setAnimation(0, `a${rIdx}_1`, false);

    aniYao.setCompleteListener(() => {
      aniYao.setCompleteListener(null);
      aniYao.setAnimation(0, `a${rIdx}_2`, true);

      tween(this.nodeAniYao)
        .to(1, { position: v3(0, 0, 0) })
        .call(() => {
          this.node.destroy();
        })
        .start();
    });
  }

  public playMingAni(nodeAniGate: Node) {
    this.nodeAniMing.active = true;
    this.nodeAniMing.setPosition(nodeAniGate.getPosition().x, nodeAniGate.getPosition().y);

    // 播放动画
    let aniMing = this.nodeAniMing.getComponent(sp.Skeleton);

    let rIdx = randomRangeInt(1, 4);
    aniMing.setAnimation(0, `ren${rIdx}`, false);

    aniMing.setCompleteListener(() => {
      aniMing.setCompleteListener(null);
      aniMing.setAnimation(0, `ren${rIdx}_1`, true);

      tween(this.nodeAniMing)
        .to(1, { position: v3(0, 0, 0) })
        .call(() => {
          this.node.destroy();
        })
        .start();
    });
  }
}
