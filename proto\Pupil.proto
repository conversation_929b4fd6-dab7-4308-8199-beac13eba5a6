syntax = "proto3";
package sim;

// 
message BasePupilInfoMessage {
  // 玩家名称
  string userName = 1;
  // 服务器名称
  string serverName = 2;
  // 种族
  int32 raceType = 3;
  // 弟子天赋ID
  int64 talentId = 4;
  // 弟子昵称Id
  int64 nameId = 5;
  // 挚友ID
  int64 friendId = 6;
  // 徒弟初始化的属性集合
  repeated double initAttrList = 7;
  // 生命 攻击 防御
  repeated double basicAttrList = 8;
  // 出师领悟的属性集合
  repeated double adultAttrList = 9;
}

// 
message PupilAddResponse {
  // 槽位的索引
  int32 index = 1;
  // 新招募到的徒弟信息
  PupilMessage pupilMessage = 2;
}

// 弟子成年信息
message PupilAdultMessage {
  // 徒弟信息
  PupilMessage pupilMessage = 1;
  // 奖励的书籍 和 数量 偶数索引处值为道具ID 奇数索引值为数量
  repeated double rewardList = 2;
}

// 
message PupilMarketRequest {
  int64 pupilId = 1;
  // CLUB(1,"妖盟"), LOCAL(2,"本地"),目前制作了本地 , CROSS(3,"跨服"),;
  int32 channelId = 2;
}

// 
message PupilMarketResponse {
  // CLUB(1,"妖盟"), LOCAL(2,"本地"),目前制作了本地 , CROSS(3,"跨服"),; 目前只做本地服，此参数可以不填
  int32 channelId = 1;
  // 对应存放联姻申请的截止时间
  map<int64,int64> marryApplyMap = 2;
}

// 
message PupilMarryPageRequest {
  int32 channelId = 1;
  // 是否扣减剩余刷新次数
  bool isCost = 2;
}

// 
message PupilMarryPageResponse {
  // 获取的结拜页的信息列表
  repeated PupilMarryReqMessage reqMessageList = 1;
  // 每日本服结拜刷新的剩余次数
  int32 localMarryRefreshCnt = 2;
}

// 
message PupilMarryReqMessage {
  int64 id = 1;
  // 徒弟信息
  PupilMessage pupil = 2;
  // 申请联姻的结束时间
  int64 endTime = 3;
}

// 
message PupilMarryRequest {
  // 己方的弟子
  int64 pupilId = 1;
  // 分页得到的待联姻记录的ID
  int64 marryId = 2;
  int32 channelId = 3;
}

// 
message PupilMarryResponse {
  // 徒弟信息
  PupilMessage pupilMessage = 1;
  repeated double rewardList = 2;
}

// 徒弟信息
message PupilMessage {
  // 唯一标识
  int64 id = 1;
  // 获取的时间
  int64 addStamp = 2;
  // 结伴的时间
  int64 marryStamp = 3;
  // 是否有展示过联姻弹窗
  bool isMarryShow = 4;
  // 自身的信息
  BasePupilInfoMessage ownInfo = 5;
  // 联姻的对象信息
  BasePupilInfoMessage partnerInfo = 6;
}

// 
message PupilRankResponse {
  int32 rank = 1;
  repeated PupilMessage pupilList = 2;
}

// 槽位信息
message PupilSlotMessage {
  // 目前在培养的徒弟，-1表示当前没有槽位没有正在培养的徒弟
  int64 pupilId = 1;
  // 已经训练的次数
  int32 train = 2;
  // 体力值
  int32 vitality = 3;
  // 体力槽位大小
  int32 vitalitySize = 4;
  // 最近一次更新体力值的时间
  int64 lastUpdateTime = 5;
}

// 
message PupilTrainMessage {
  // 发起的妖盟联姻申请 key:徒弟ID val:剩余时长
  map<int64,int64> clubMarryApplyMap = 1;
  // 发起的本地联姻申请
  map<int64,int64> localMarryApplyMap = 2;
  // 发起的跨服联姻申请
  map<int64,int64> crossMarryApplyMap = 3;
  // index就是槽位索引，从0开始
  repeated PupilSlotMessage trainSlotList = 4;
  // index就是槽位索引，从0开始，如果当前位置没有委任，则为-1
  repeated int64 workSlotList = 5;
  // 每日本服结拜刷新的剩余次数
  int32 localMarryRefreshCnt = 6;
}

// 培养后的返回信息
message PupilTrainResponse {
  // 新出师弟子的列表 如果列表不为空，本次培养出师弟子，否则就是培养一次弟子
  repeated PupilAdultMessage newAdultList = 1;
  // index就是槽位索引，从0开始，如果当前位置没有委任，则为-1
  repeated int64 workSlotList = 2;
  // 掉落的经验
  double heroExp = 3;
  // 掉落的道具列表，为空就没有道具掉落，偶数位为道具ID，奇数位为数量
  repeated double dropItemList = 4;
  // 有更新的培养槽的信息 key：槽位索引 value:变化的槽位信息
  map<int32,PupilSlotMessage> changeSlotMap = 5;
}

// 
message PupilVitalityRequest {
  // key : 槽位索引 val : 活力丹数量
  map<int32,int32> slotIndexAndCountMap = 1;
}

// 
message PupilVitalityResponse {
  // 体力值有变化的槽位信息
  map<int32,PupilSlotMessage> slotUpdateMap = 1;
}

// 
message PupilWorkRequest {
  int64 pupilId = 1;
  int32 slotIndex = 2;
}

