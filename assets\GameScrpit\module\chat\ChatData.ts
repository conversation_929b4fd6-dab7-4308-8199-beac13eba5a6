import MsgEnum from "../../game/event/MsgEnum";
import { JsonMgr } from "../../game/mgr/JsonMgr";
import { ChatPackMessage } from "../../game/net/protocol/Chat";
import MsgMgr from "../../lib/event/MsgMgr";
import { times } from "../../lib/utils/NumbersUtils";
import { ChatModule } from "./ChatModule";

export class ChatData {


  private marketTypeMessage: { [key: number]: ChatPackMessage[] } = Object.create(null);

  public getMarketMessage(marketType: number): ChatPackMessage[] {
    return this.marketTypeMessage[marketType];
  }
  public setMarketMessage(marketType: number, messages: ChatPackMessage[]) {
    this.marketTypeMessage[marketType] = messages.reverse();
  }
  public getNewestMessage(marketType: number): ChatPackMessage {
    if (!this.marketTypeMessage[marketType] || this.marketTypeMessage[marketType].length == 0) {
      return null;
    }
    return this.marketTypeMessage[marketType][0];
  }
  public addNewMessage(marketType: number, message: ChatPackMessage) {
    if (!this.marketTypeMessage[marketType]) {
      this.marketTypeMessage[marketType] = [];
    }
    this.marketTypeMessage[marketType].unshift(message);
    switch (message.marketType) {
      case 1:
        MsgMgr.emit(MsgEnum.ON_CHAT_UNION_MESSAGE);
        break;
      case 2:
        MsgMgr.emit(MsgEnum.ON_CHAT_WORLD_MESSAGE);
        break;
    }
  }
}
