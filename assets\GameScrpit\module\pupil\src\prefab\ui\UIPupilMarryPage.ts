import { _decorator, Label } from "cc";
import { UINode } from "../../../../../lib/ui/UINode";
import { BundleEnum } from "../../../../../game/bundleEnum/BundleEnum";
import { PupilMarryPageResponse, PupilMessage } from "../../../../../game/net/protocol/Pupil";
import { UIMgr } from "../../../../../lib/ui/UIMgr";

import TipMgr from "../../../../../lib/tips/TipMgr";
import { PupilMarryAdapter } from "../../adapter/PupilMarryAdapter";
import { ListView } from "../../../../../game/common/ListView";
import { PupilModule } from "../../PupilModule";
import { MarryChannelEnum, PupilAudioName } from "../../PupilConstant";
import { ClubModule } from "../../../../club/ClubModule";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { AdapterView } from "../../../../../../platform/src/core/ui/adapter_view/AdapterView";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UIPupilMarryPage")
export class UIPupilMarryPage extends UINode {
  protected _openAct: boolean = true;
  private _marryAdapter: PupilMarryAdapter;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_PUPIL}?prefab/ui/UIPupilMarryPage`;
  }

  private _pupilId: number = null;

  // 当前选项卡
  private _tabIdx: number = -1;

  /**要结伴的弟子信息 */
  pupilMsg: PupilMessage = null;

  public init(param: any) {
    super.init(param);
    this._pupilId = param.pupilId;
  }

  private channelMap = {
    2: MarryChannelEnum.CLUB,
    3: MarryChannelEnum.LOCAL,
    4: MarryChannelEnum.CROSS,
  };

  protected onEvtShow(): void {
    this._marryAdapter = new PupilMarryAdapter(this.getNode("pupil_local_item"));
    this.getNode("list_wait").getComponent(AdapterView).setAdapter(this._marryAdapter);

    this.onSwitchTab(3);
    PupilModule.api.marryPage(MarryChannelEnum.LOCAL, false, (data: PupilMarryPageResponse) => {
      this.refreshData(data);
    });
  }

  private refresh() {
    if (this._tabIdx == 1) {
      this.on_click_btn_appoint();
    } else if (this._tabIdx == 2) {
      this.on_click_btn_club();
    } else if (this._tabIdx == 3) {
      this.on_click_btn_local();
    } else if (this._tabIdx == 4) {
      this.on_click_btn_cross();
    }
  }

  protected refreshData(data: PupilMarryPageResponse) {
    // 征婚数据列表
    if (data) {
      const dataList = [];
      data.reqMessageList.forEach((element) => {
        dataList.push({ marryReq: element, pupilId: this._pupilId, channelId: this.channelMap[this._tabIdx] });
      });

      this._marryAdapter.setDatas(dataList);
      log.log("========", dataList);
    }

    // 全服剩余刷新数次
    this.getNode("lbl_refresh_times").getComponent(Label).string = `${PupilModule.data.localMarryRefreshCnt}/10`;
  }

  // tab 切换界面状态修改
  private onSwitchTab(tab: number) {
    // 返回结果是否有切换
    let rs = this._tabIdx == tab;
    this._tabIdx = tab;

    this.getNode("btn_appoint").getChildByName("bg_unactive").active = tab != 1;
    this.getNode("btn_appoint").getChildByName("bg_active").active = tab == 1;

    this.getNode("btn_club").getChildByName("bg_unactive").active = tab != 2;
    this.getNode("btn_club").getChildByName("bg_active").active = tab == 2;

    this.getNode("btn_local").getChildByName("bg_unactive").active = tab != 3;
    this.getNode("btn_local").getChildByName("bg_active").active = tab == 3;

    this.getNode("btn_cross").getChildByName("bg_unactive").active = tab != 4;
    this.getNode("btn_cross").getChildByName("bg_active").active = tab == 4;

    // this.getNode("btn_sort").active = tab != 1;
    this.getNode("btn_push_market").active = tab != 1;
    this.getNode("btn_refresh").active = tab != 1;

    this.getNode("list_wait").active = tab != 1;

    if (tab == 2) {
      this.getNode("btn_push_market").getComponentInChildren(Label).string = "战盟发布";
    } else if (tab == 3) {
      this.getNode("btn_push_market").getComponentInChildren(Label).string = "全服发布";
    }

    return rs;
  }

  private on_click_btn_appoint() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    TipMgr.showTip("开发中");
  }

  private on_click_btn_club() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    if (!ClubModule.data.clubMessage) {
      TipMgr.showTip("未加入战盟");
      return;
    }

    this.onSwitchTab(2);
    PupilModule.api.marryPage(MarryChannelEnum.CLUB, false, (data: PupilMarryPageResponse) => {
      this.refreshData(data);
    });
  }

  private on_click_btn_local() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this.onSwitchTab(3);

    PupilModule.api.marryPage(MarryChannelEnum.LOCAL, false, (data: PupilMarryPageResponse) => {
      this.refreshData(data);
    });
  }

  private on_click_btn_cross() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    TipMgr.showTip("开发中");
  }

  private on_click_btn_refresh() {
    AudioMgr.instance.playEffect(PupilAudioName.Effect.点击刷新按钮);
    let channelId: number = -1;
    if (this._tabIdx == 2) {
      channelId = MarryChannelEnum.CLUB;
    } else if (this._tabIdx == 3) {
      channelId = MarryChannelEnum.LOCAL;
    } else if (this._tabIdx == 3) {
      channelId = MarryChannelEnum.CROSS;
    } else {
      return;
    }
    if (PupilModule.data.localMarryRefreshCnt <= 0) {
      TipMgr.showTip("刷新次数不足");
      return;
    }
    PupilModule.api.marryPage(channelId, true, (data: PupilMarryPageResponse) => {
      this.refreshData(data);
    });
  }

  private on_click_btn_push_market() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let channelId: number = -1;
    if (this._tabIdx == 2) {
      channelId = MarryChannelEnum.CLUB;
    } else if (this._tabIdx == 3) {
      channelId = MarryChannelEnum.LOCAL;
    } else if (this._tabIdx == 4) {
      channelId = MarryChannelEnum.CROSS;
    } else {
      return;
    }

    PupilModule.api.pushToMarket(this._pupilId, channelId, () => {
      UIMgr.instance.back();
      TipMgr.showTip("发布成功");
    });
  }
}
