import { _decorator, color, isValid, Label } from "cc";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import Formate from "../../../lib/utils/Formate";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { CityModule } from "../../../module/city/CityModule";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import { BadgeMgr, BadgeType } from "../../mgr/BadgeMgr";
import { v3 } from "cc";
import { JsonMgr } from "../../mgr/JsonMgr";
import { BtnCity100 } from "./BtnCity100";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { RewardRouteEnum } from "../../../ext_reward/RewardDefine";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { LangMgr } from "../../mgr/LangMgr";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UIEnergyUpgrade")
export class UIEnergyUpgrade extends UINode {
  protected _openAct: boolean = true;
  protected dependOn(): BundleEnum[] {
    return [BundleEnum.BUNDLE_COMMON_UI, BundleEnum.BUNDLE_CITY_100, BundleEnum.BUNDLE_COMMON_ITEM];
  }
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_MAJORCITY}?prefab/ui/UIEnergyUpgrade`;
  }

  private _stateIndex: number = -1;
  private _lv = null;

  protected onRegEvent(): void {
    MsgMgr.on(MsgEnum.ON_EXIST_ITEM_CHANGE, this.upItem, this);
  }
  protected onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_EXIST_ITEM_CHANGE, this.upItem, this);
  }

  private upItem() {
    this.showMyLevel(this._lv);
  }

  protected onEvtShow(): void {
    // 发送场景焦点触发消息
    MsgMgr.emit(MsgEnum.ON_ZHAOGE_CITY_FOCOUS, "city_100");
    BadgeMgr.instance.setBadgeId(this.getNode("btn_upgrade"), BadgeType.UIMajorCity.btn_level_up.btn_upgrade.id);
    let lv = CityModule.data.energyFactoryMsg.level;
    this._stateIndex = this.getNvWaImageKey(lv);
    this._lv = lv;
    this.showMyLevel(lv);

    // 翻译
    LangMgr.txNode(this.getNode("dialog_title"), 173);
    // LangMgr.txNode(this.getNode("lbl_xiaoguo"), 174);
    LangMgr.txNode(this.getNode("layout_effect1").getChildByPath("title_node/title_lab"), 175);
    LangMgr.txNode(this.getNode("layout_effect2").getChildByPath("title_node/title_lab"), 176);
    LangMgr.txNode(this.getNode("layout_effect3").getChildByPath("title_node/title_lab"), 177);
  }

  /**等级的展示 */
  private showMyLevel(lv: number) {
    this.isMaxLevel_showBtn();
    this["lbl_energy_level"].getComponent(Label).string = `LV.${lv} `;
    this.loadNvwa(lv);
    this.showDetails(lv);
    this.showArrowBtn(lv);

    this.unlockLabTips(lv);
  }

  /**解锁提示文本展示 */
  private unlockLabTips(lv: number) {
    let mylv = CityModule.data.energyFactoryMsg.level;

    let myIndex = this.getNvWaImageKey(mylv);
    let showIndex = this.getNvWaImageKey(lv);

    //const lblUn = this.getNode("lbl_un").getComponent(Label);

    if (showIndex < myIndex) {
      this.getNode("btn_upgrade").active = false;
      this.getNode("layout_item").active = false;

      // lblUn.string = LangMgr.txMsgCode(170); // ("已解锁");
      // lblUn.color = color().fromHEX("#FFFFFF");
      // lblUn.outlineColor = color().fromHEX("#09B316");
      // lblUn.outlineWidth = 2;
    } else if (showIndex == myIndex) {
      // lblUn.string = LangMgr.txMsgCode(156, []);
      // lblUn.color = color().fromHEX("#FFFFFF");
      // lblUn.outlineColor = color().fromHEX("#a15d21");
      // lblUn.outlineWidth = 2;

      let lv = CityModule.data.energyFactoryMsg.level;
      let db = JsonMgr.instance.jsonList.c_buildCrystal;
      let list = Object.keys(db);
      let info = db[list[0]];
      let lvMax = info.lvMax;

      if (lv < lvMax) {
        this.getNode("btn_upgrade").active = true;
        this.getNode("layout_item").active = true;
      }
    } else if (showIndex > myIndex) {
      this.getNode("btn_upgrade").active = false;
      this.getNode("layout_item").active = false;

      // lblUn.string = LangMgr.txMsgCode(171, [lv]); //"石像达到" + lv + "级解锁";
      // lblUn.color = color().fromHEX("#ff0000");
      // lblUn.outlineColor = color().fromHEX("#631a22");
      // lblUn.outlineWidth = 0;
    }
  }

  /**判断目前展示的形态是否是第一个或者最后一个形态隐藏箭头按钮 */
  private showArrowBtn(lv: number) {
    let db = JsonMgr.instance.jsonList.c_buildCrystal;
    let list = Object.keys(db);
    let info = db[list[0]];
    let index = -1;
    for (let i = 0; i < info.unlockLvList.length; i++) {
      if (lv >= info.unlockLvList[i]) {
        index = i;
      } else {
        break;
      }
    }
    if (index == -1) {
      this.getNode("btn_left").active = false;
      this.getNode("btn_right").active = true;
    } else if (index >= info.unlockLvList.length - 1) {
      this.getNode("btn_left").active = true;
      this.getNode("btn_right").active = false;
    } else {
      this.getNode("btn_left").active = true;
      this.getNode("btn_right").active = true;
    }
  }

  /**判断说是否满级了，不展示升级按钮 */
  private isMaxLevel_showBtn() {
    let lv = CityModule.data.energyFactoryMsg.level;
    let db = JsonMgr.instance.jsonList.c_buildCrystal;
    let list = Object.keys(db);
    let info = db[list[0]];
    let lvMax = info.lvMax;

    if (lv >= lvMax) {
      this.getNode("zi_yimanji").active = true;
      this.getNode("layout_item").active = false;
      this.getNode("btn_upgrade").active = false;
      this.getNode("bg_9g_shuxingdi").children.forEach((val) => {
        val.getChildByName("bg_to").active = false;
        val.getChildByName("node_next").active = false;
      });
    } else {
      this.getNode("zi_yimanji").active = false;
      this.getNode("layout_item").active = true;
      this.getNode("btn_upgrade").active = true;
      this.getNode("bg_9g_shuxingdi").children.forEach((val) => {
        val.getChildByName("bg_to").active = true;
        val.getChildByName("node_next").active = true;
      });
    }
  }

  private async loadNvwa(lv: number) {
    this.getNode("bg_nv_shixiang").destroyAllChildren();
    const nodeNvwa = await CityModule.service.loadNvWaImage(lv);
    if (isValid(this.node) == false) {
      return;
    }
    this.getNode("bg_nv_shixiang").addChild(nodeNvwa);
    nodeNvwa.walk((child) => (child.layer = this.node.layer));
    nodeNvwa.getComponent(BtnCity100).showAnimation(lv);
    nodeNvwa.getComponent(BtnCity100).skt.node.setPosition(v3(0, 0, 0));
    nodeNvwa.setPosition(v3(0, 80, 0));
  }

  /**属性信息的展示 */
  private showDetails(lv: number) {
    const config_buildCrystal = CityModule.data.getConfigBuildCrystal(lv);
    const config_buildCrystal_next = CityModule.data.getConfigBuildCrystal(lv + 1);

    //点击收益;
    this.getNode("bg_9g_shuxingdi").children[0].getChildByPath("node_now/lbl_now").getComponent(Label).string =
      Formate.format(config_buildCrystal.reward);
    this.getNode("bg_9g_shuxingdi").children[0].getChildByPath("node_next/lbl_next").getComponent(Label).string =
      Formate.format(config_buildCrystal_next.reward);

    //据点加成;
    this.getNode("bg_9g_shuxingdi").children[1].getChildByPath("node_now/lbl_now").getComponent(Label).string = `${
      config_buildCrystal.rateAdd / 100
    }%`;
    this.getNode("bg_9g_shuxingdi").children[1].getChildByPath("node_next/lbl_next").getComponent(Label).string = `${
      config_buildCrystal_next.rateAdd / 100
    }%`;

    // 自动点击次数
    this.getNode("bg_9g_shuxingdi").children[2].getChildByPath("node_now/lbl_now").getComponent(Label).string =
      LangMgr.txMsgCode(172, [config_buildCrystal.time + ""]); //`${config_buildCrystal.time}次/秒`;
    this.getNode("bg_9g_shuxingdi").children[2].getChildByPath("node_next/lbl_next").getComponent(Label).string =
      LangMgr.txMsgCode(172, [config_buildCrystal_next.time + ""]); //`${config_buildCrystal_next.time}次/秒`;

    //升级消耗;
    let item_num = PlayerModule.data.getItemNum(config_buildCrystal_next.cfg);
    this["lbl_item_cost"].getComponent(Label).string = `${Formate.format(item_num)}/${config_buildCrystal_next.cost}`;
    if (item_num >= config_buildCrystal_next.cost) {
      this["lbl_item_cost"].getComponent(Label).color = color().fromHEX("#01b505");
    } else {
      this["lbl_item_cost"].getComponent(Label).color = color().fromHEX("#e10000");
    }
  }

  private on_click_btn_left() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    if (this._stateIndex <= -1) {
      return;
    }

    this._stateIndex--;

    let db = JsonMgr.instance.jsonList.c_buildCrystal;
    let list = Object.keys(db);
    let info = db[list[0]];

    let lv = info.unlockLvList[this._stateIndex];
    if (this._stateIndex == -1) {
      lv = 1;
    }
    lv = this.isMyLvSection(lv);
    this._lv = lv;
    this.showMyLevel(lv);
  }

  private on_click_btn_right() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let db = JsonMgr.instance.jsonList.c_buildCrystal;
    let list = Object.keys(db);
    let info = db[list[0]];
    if (this._stateIndex >= info.unlockLvList.length - 1) {
      return;
    }
    this._stateIndex++;

    let lv = info.unlockLvList[this._stateIndex];
    lv = this.isMyLvSection(lv);
    this._lv = lv;
    this.showMyLevel(lv);
  }

  /**判断当前自己的等级是否在当前阶段中，如果在就是展示自己的当前等级 */
  private isMyLvSection(lv: number) {
    let db = JsonMgr.instance.jsonList.c_buildCrystal;
    let list = Object.keys(db);
    let info = db[list[0]];

    let myLv = CityModule.data.energyFactoryMsg.level;

    let curLv = info.unlockLvList[this._stateIndex];
    if (this._stateIndex == -1) {
      curLv = 1;
    }
    let nextLv = info.unlockLvList[this._stateIndex + 1];
    if (!nextLv) {
      nextLv = info.lvMax;
      if (myLv >= nextLv) {
        return myLv;
      }
    }

    if (myLv >= curLv && myLv < nextLv) {
      return myLv;
    }

    return lv;
  }

  public getNvWaImageKey(lv: number) {
    let db = JsonMgr.instance.jsonList.c_buildCrystal;
    let list = Object.keys(db);
    let info = db[list[0]];
    let index = -1;
    for (let i = 0; i < info.unlockLvList.length; i++) {
      if (lv >= info.unlockLvList[i]) {
        index = i;
      } else {
        break;
      }
    }
    return index;
  }

  private on_click_btn_upgrade() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    // 水晶等级
    let level = CityModule.data.energyFactoryMsg.level;
    const config_buildCrystal_next = CityModule.data.getConfigBuildCrystal(level + 1);
    let item_num = PlayerModule.data.getItemNum(config_buildCrystal_next.cfg);
    if (item_num < config_buildCrystal_next.cost) {
      UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
        itemId: config_buildCrystal_next.cfg,
        needNum: config_buildCrystal_next.cost,
      });
      //弹窗礼包事件
      MsgMgr.emit(MsgEnum.ON_ACTIVITY_TANCHUANG, config_buildCrystal_next.cfg);
      return;
    }
    CityModule.api.upgradeEnergyFactory((data) => {
      if (isValid(this.node) === false) {
        return;
      }
      // log.log("女娲等级提升后数据=====", data);
      let key1 = CityModule.service.getNvWaImageKey(level);
      let key2 = CityModule.service.getNvWaImageKey(data.level);
      this._stateIndex = this.getNvWaImageKey(data.level);
      this.showMyLevel(data.level);
      if (key1 == key2) {
        TipsMgr.topRouteCtrl.show(RewardRouteEnum.TopEnergyUpgradeRes);
      } else {
        UIMgr.instance.back();
        MsgMgr.emit(MsgEnum.ON_ENERGYFACTORY_CHANGE_ANI, {
          level: data.level,
          callback: () => {
            TipsMgr.topRouteCtrl.show(RewardRouteEnum.TopEnergyUpgradeRes);
          },
        });
      }
    });
  }

  private on_click_btn_close() {
    // 关闭窗口 effect_guanbiyinxiao
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  // private _lv: number = null;
  // private _myIndex;
  // private _stateIndex: number;
  // protected onRegEvent() {
  //   MsgMgr.on(MsgEnum.ON_ENERGYFACTORY_UPDATE, this.refresh, this);
  // }
  // protected onDelEvent() {
  //   MsgMgr.off(MsgEnum.ON_ENERGYFACTORY_UPDATE, this.refresh, this);
  // }
  // protected onEvtShow(): void {
  //   // 发送场景焦点触发消息
  //   MsgMgr.emit(MsgEnum.ON_ZHAOGE_CITY_FOCOUS, "city_100");
  //   this._lv = CityModule.data.energyFactoryMsg.level;
  //   this._myIndex = this.getNvWaImageKey(this._lv);
  //   this._stateIndex = this.getNvWaImageKey(this._lv);
  //   this.refresh();
  //   // 显示红点
  //   BadgeMgr.instance.setBadgeId(this.getNode("btn_upgrade"), BadgeType.UIMajorCity.btn_level_up.btn_upgrade.id);
  // }

  // private refresh() {
  //   // 水晶等级
  //   let level = this._lv;
  //   // 水晶等级
  //   this["lbl_energy_level"].getComponent(Label).string = `LV.${level} `;
  //   // 本级与下级的配置信息
  //   const config_buildCrystal = CityModule.data.getConfigBuildCrystal(level);
  //   const config_buildCrystal_next = CityModule.data.getConfigBuildCrystal(level + 1);
  //   // 点击收益
  //   this.getNode("bg_9g_shuxingdi").children[0].getChildByPath("node_now/lbl_now").getComponent(Label).string =
  //     Formate.format(config_buildCrystal.reward);
  //   this.getNode("bg_9g_shuxingdi").children[0].getChildByPath("node_next/lbl_next").getComponent(Label).string =
  //     Formate.format(config_buildCrystal_next.reward);
  //   // 据点加成
  //   this.getNode("bg_9g_shuxingdi").children[1].getChildByPath("node_now/lbl_now").getComponent(Label).string = `${
  //     config_buildCrystal.rateAdd / 100
  //   }%`;
  //   this.getNode("bg_9g_shuxingdi").children[1].getChildByPath("node_next/lbl_next").getComponent(Label).string = `${
  //     config_buildCrystal_next.rateAdd / 100
  //   }%`;
  //   // 自动点击次数
  //   this.getNode("bg_9g_shuxingdi")
  //     .children[2].getChildByPath("node_now/lbl_now")
  //     .getComponent(Label).string = `${config_buildCrystal.time}次/秒`;
  //   this.getNode("bg_9g_shuxingdi")
  //     .children[2].getChildByPath("node_next/lbl_next")
  //     .getComponent(Label).string = `${config_buildCrystal_next.time}次/秒`;
  //   // 升级消耗
  //   let item_num = PlayerModule.data.getItemNum(config_buildCrystal_next.cfg);
  //   this["lbl_item_cost"].getComponent(Label).string = `${Formate.format(item_num)}/${config_buildCrystal_next.cost}`;
  //   if (item_num >= config_buildCrystal_next.cost) {
  //     this["lbl_item_cost"].getComponent(Label).color = color().fromHEX("#01b505");
  //   } else {
  //     this["lbl_item_cost"].getComponent(Label).color = color().fromHEX("#e10000");
  //   }
  //   if (this._stateIndex < this._myIndex) {
  //     this.getNode("lbl_un").getComponent(Label).string = "已解锁";
  //     this.getNode("btn_upgrade").active = false;
  //     this.getNode("layout_item").active = false;
  //     this.getNode("lbl_un").getComponent(Label).color = color().fromHEX("#FFFFFF");
  //     this.getNode("lbl_un").getComponent(Label).outlineColor = color().fromHEX("#09B316");
  //   } else if (this._stateIndex == this._myIndex) {
  //     this.getNode("lbl_un").getComponent(Label).string = "当前";
  //     this.getNode("btn_upgrade").active = true;
  //     this.getNode("layout_item").active = true;
  //     this.getNode("lbl_un").getComponent(Label).color = color().fromHEX("#FFFFFF");
  //     this.getNode("lbl_un").getComponent(Label).outlineColor = color().fromHEX("#a15d21");
  //   } else if (this._stateIndex > this._myIndex) {
  //     this.getNode("lbl_un").getComponent(Label).string = "石像达到" + this._lv + "级解锁";
  //     this.getNode("btn_upgrade").active = false;
  //     this.getNode("layout_item").active = false;
  //     this.getNode("lbl_un").getComponent(Label).color = color().fromHEX("#ff0000");
  //     this.getNode("lbl_un").getComponent(Label).outlineColor = color().fromHEX("#631a22");
  //   }
  //   // 女娲形象
  //   this.loadNvwa();
  // }
  // private on_click_btn_upgrade() {
  //   // 水晶等级
  //   let level = CityModule.data.energyFactoryMsg.level;
  //   const config_buildCrystal_next = CityModule.data.getConfigBuildCrystal(level + 1);
  //   let item_num = PlayerModule.data.getItemNum(config_buildCrystal_next.cfg);
  //   if (item_num < config_buildCrystal_next.cost) {
  //     UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
  //       itemId: config_buildCrystal_next.cfg,
  //       needNum: config_buildCrystal_next.cost,
  //     });
  //     //弹窗礼包事件
  //     MsgMgr.emit(MsgEnum.ON_ACTIVITY_TANCHUANG, config_buildCrystal_next.cfg);
  //     return;
  //   }
  //   CityModule.api.upgradeEnergyFactory((data) => {
  //     log.log("女娲等级提升后数据=====", data);
  //     let key1 = CityModule.service.getNvWaImageKey(level);
  //     let key2 = CityModule.service.getNvWaImageKey(data.level);
  //     this._myIndex = this.getNvWaImageKey(data.level);
  //     this._stateIndex = this.getNvWaImageKey(data.level);
  //     this._lv = data.level;
  //     if (key1 == key2) {
  //       TipsMgr.topRouteCtrl.show(RewardRouteEnum.TopEnergyUpgradeRes);
  //     } else {
  //       UIMgr.instance.back();
  //       MsgMgr.emit(MsgEnum.ON_ENERGYFACTORY_CHANGE_ANI, {
  //         level: data.level,
  //         callback: () => {
  //           TipsMgr.topRouteCtrl.show(RewardRouteEnum.TopEnergyUpgradeRes);
  //         },
  //       });
  //     }
  //   });
  // }
  // private on_click_btn_left() {
  //   if (this._stateIndex <= 0) {
  //     return;
  //   }
  //   let db = JsonMgr.instance.jsonList.c_buildCrystal;
  //   let list = Object.keys(db);
  //   let info = db[list[0]];
  //   this._stateIndex--;
  //   this._lv = info.unlockLvList[this._stateIndex];
  //   this.refresh();
  // }
  // private on_click_btn_right() {
  //   let db = JsonMgr.instance.jsonList.c_buildCrystal;
  //   let list = Object.keys(db);
  //   let info = db[list[0]];
  //   if (this._stateIndex >= info.unlockLvList.length - 1) {
  //     return;
  //   }
  //   this._stateIndex++;
  //   this._lv = info.unlockLvList[this._stateIndex];
  //   this.refresh();
  // }
  // private on_click_btn_close() {
  //   UIMgr.instance.back();
  // }
}
