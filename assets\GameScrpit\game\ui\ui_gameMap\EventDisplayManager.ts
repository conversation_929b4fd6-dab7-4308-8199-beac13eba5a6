// import { _decorator, Node, Vec3 } from "cc";
// import { BaseEvent } from "./base_event/BaseEvent";
// import { ThiefEvent } from "./base_event/ThiefEvent";
// import { JsonMgr } from "../../mgr/JsonMgr";
// import { EventActionModule } from "../../../module/event_action/EventActionModule";
// import MsgMgr from "../../../lib/event/MsgMgr";
// import MsgEnum from "../../event/MsgEnum";
// import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
// const log = Logger.getLoger(LOG_LEVEL.WARN);
// const { ccclass } = _decorator;

// // 定义事件类映射表
// const eventClassMap: { [key: number]: new (config: any) => BaseEvent } = {
//   101: ThiefEvent,
//   201: ThiefEvent,
//   // 可以继续添加其他事件类型
// };

// export class MapBaseEvent {
//   id: number;
//   event: BaseEvent;
// }

// @ccclass("EventDisplayManager")
// export class EventDisplayManager {
//   private static _instance: EventDisplayManager;
//   private mapNode: Node | null = null;
//   private activeEvents: Map<number, MapBaseEvent> = new Map<number, MapBaseEvent>();

//   public static get instance(): EventDisplayManager {
//     if (!this._instance) {
//       this._instance = new EventDisplayManager();
//     }
//     return this._instance;
//   }

//   /**
//    * 初始化管理类
//    * @param mapNode 地图节点，用于放置事件实体
//    */
//   public init(mapNode: Node) {
//     MsgMgr.off(MsgEnum.ON_SANJIE_EVENT_DATA_UPDATE, this.showEvents, this);
//     MsgMgr.on(MsgEnum.ON_SANJIE_EVENT_DATA_UPDATE, this.showEvents, this);

//     this.mapNode = mapNode;
//     let eventTotalProgressMap = EventActionModule.data.eventTrainMessage.eventTotalProgressMap;
//     this.showEvents(eventTotalProgressMap);
//   }

//   /**
//    * 根据事件信息显示三界事件中的各种事件
//    * @param eventTotalProgressMap 事件消息数据
//    */
//   public showEvents(eventTotalProgressMap: { [key: number]: number }) {
//     if (!this.mapNode) {
//       log.error("EventDisplayManager 未正确初始化");
//       return;
//     }

//     let db = JsonMgr.instance.jsonList.c_event2;

//     this.activeEvents.forEach((val, key) => {
//       if (!eventTotalProgressMap[key]) {
//         this.activeEvents.delete(key);
//         val.event.stopEvent();
//       }
//     });

//     for (let i in eventTotalProgressMap) {
//       let obj = eventTotalProgressMap[i];
//       if (!db[i]) {
//         log.error(`未找到事件 ID ${i} 对应的事件配置`);
//         continue;
//       }

//       let data = db[i];
//       const eventId = Number(data.id);
//       if (this.activeEvents.has(eventId)) {
//         log.warn(`事件 ID ${i} 已经存在，跳过显示`);
//         let mapBaseEvent = this.activeEvents.get(eventId);
//         mapBaseEvent.event.upData();
//         continue;
//       }

//       const EventClass = eventClassMap[eventId];
//       if (!EventClass) {
//         log.error(`未找到事件 ID ${eventId} 对应的事件类`);
//         continue;
//       }

//       let insBaseEvent = new EventClass(obj);
//       const mapBaseEvent: MapBaseEvent = {
//         id: eventId,
//         event: insBaseEvent,
//       };
//       log.log(`显示事件 ID ${eventId}`);

//       insBaseEvent.init(mapBaseEvent);
//       this.mapNode.addChild(insBaseEvent);
//       insBaseEvent.startEvent();
//       this.activeEvents.set(eventId, mapBaseEvent);
//     }
//   }

//   /**
//    * 清理当前显示的所有事件实体
//    */
//   public clearEvents() {
//     this.activeEvents.forEach((mapBaseEvent) => {
//       mapBaseEvent.event.stopEvent();
//       mapBaseEvent.event.destroy();
//     });
//     this.activeEvents.clear();
//   }
// }
