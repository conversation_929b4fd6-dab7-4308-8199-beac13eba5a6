import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../game/mgr/ApiHandler";
import { PostActionSubCmd } from "../../game/net/cmd/CmdData";
import { IntValue } from "../../game/net/protocol/ExternalMessage";
import { PostHarvestResponse, PostTrainMessage } from "../../game/net/protocol/Post";
import { PostModule } from "./postModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class PostApi {
  /**获取驿站基础信息 */
  public getPostInfo(success) {
    ApiHandler.instance.request(
      PostTrainMessage,
      PostActionSubCmd.postInfo,
      null, //入参
      (res: PostTrainMessage) => {
        PostModule.data.message = res;
        success && success(res);
      }
    );
  }

  /**驿站开始挂机 */
  public deliver(count, success) {
    ApiHandler.instance.request(
      PostTrainMessage,
      PostActionSubCmd.deliver,
      IntValue.encode({ value: count }), //入参
      (res: PostTrainMessage) => {
        PostModule.data.message = res;
        success && success(res);
      }
    );
  }

  /**驿站加速 */
  public speed(success) {
    ApiHandler.instance.request(
      PostTrainMessage,
      PostActionSubCmd.speed,
      null, //入参
      (res: PostTrainMessage) => {
        log.log("驿站加速=====code", res);
        PostModule.data.message = res;
        success && success(res);
      }
    );
  }

  /**领取驿站奖励 */
  public getPostAward(success) {
    ApiHandler.instance.request(
      PostHarvestResponse,
      PostActionSubCmd.take,
      null,
      (res: PostHarvestResponse) => {
        PostModule.data.message = res.postTrain;
        success && success(res);
      } //请求成功回调
    );
  }

  /**领取驿站奖励 */
  public levelUp(success) {
    ApiHandler.instance.request(
      PostTrainMessage,
      PostActionSubCmd.levelUp,
      null, //入参
      (res: PostTrainMessage) => {
        PostModule.data.message = res;
        success && success(res);
      }
    );
  }

  /**测试接口 ---- 重置每日挂机的次数 ---- 解析 PostTrainMessage */
  public testResetDailyCount(success?) {
    ApiHandler.instance.request(
      PostTrainMessage,
      PostActionSubCmd.testResetDailyCount,
      null,
      (res: PostTrainMessage) => {
        success && success(res);
      } //请求成功回调
    );
  }
}
