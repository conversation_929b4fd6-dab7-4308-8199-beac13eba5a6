import { FundVO } from "../../game/GameDefine";
import GameHttpApi from "../../game/httpNet/GameHttpApi";
import { AchieveMessage } from "../../game/net/protocol/Activity";
import TipMgr from "../../lib/tips/TipMgr";
import { UIMgr } from "../../lib/ui/UIMgr";
import { FundModule } from "./FundModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class FundData {
  /**基金 */
  private _fundMap: Map<number, AchieveMessage> = new Map<number, AchieveMessage>();

  private _fundDbMap: Map<number, FundVO> = new Map<number, FundVO>();

  public getFundData(achieveId: number) {
    if (this._fundMap.has(achieveId)) {
      return this._fundMap.get(achieveId);
    }

    return null;
  }

  public async getFundVO(activityId: number): Promise<FundVO> {
    return new Promise((resolve, reject) => {
      if (this._fundDbMap.has(activityId)) {
        let fundVO = this._fundDbMap.get(activityId);
        resolve(fundVO);
      } else {
        GameHttpApi.getActivityConfig(activityId).then((resp: any) => {
          if (resp.code != 200) {
            log.error(resp);
          }
          this._fundDbMap.set(activityId, JSON.parse(resp.msg));
          resolve(this._fundDbMap.get(activityId));
        });
      }
    });
  }

  public upVO(id) {
    if (this._fundDbMap.has(id)) {
      this._fundDbMap.delete(id);
    }
    this.getFundVO(id);

    let rotueTables = FundModule.route.rotueTables;
    for (let i = 0; i < rotueTables.length; i++) {
      UIMgr.instance.closeByName(rotueTables[i].uiName);
    }
    TipMgr.showTip("活动内容已变更");
  }

  public setFundData(rs: { [key: number]: AchieveMessage }, activityId: number, callback?: Function) {
    let list = Object.keys(rs);

    let cb = () => {
      for (let i = 0; i < list.length; i++) {
        let key = list[i];
        let info = rs[key];
        this._fundMap.set(info.id, info);
      }
    };

    if (!this._fundDbMap.has(activityId)) {
      GameHttpApi.getActivityConfig(activityId).then((resp: any) => {
        if (resp.code != 200) {
          log.error(resp);
        }
        this._fundDbMap.set(activityId, JSON.parse(resp.msg));
        cb();
        callback && callback();
      });
    } else {
      cb();
      callback && callback();
    }
  }
}
