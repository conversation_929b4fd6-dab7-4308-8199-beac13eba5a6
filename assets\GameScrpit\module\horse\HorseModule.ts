import { GameData } from "../../game/GameData";
import data from "../../lib/data/data";
import { HorseA<PERSON> } from "./HorseApi";
import { HorseData } from "./HorseData";
import { HorseRoute } from "./HorseRoute";
import { HorseService } from "./HorseService";
import { HorseSubscriber } from "./HorseSubscriber";

export class HorseModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): HorseModule {
    if (!GameData.instance.HorseModule) {
      GameData.instance.HorseModule = new HorseModule();
    }
    return GameData.instance.HorseModule;
  }
  private _data = new HorseData();
  private _api = new HorseApi();
  private _service = new HorseService();
  private _subscriber = new HorseSubscriber();
  private _route = new HorseRoute();
  public static get data() {
    return this.instance._data;
  }
  public static get api() {
    return this.instance._api;
  }

  public static get service() {
    return this.instance._service;
  }

  protected saveKey(): string {
    return this.constructor.name;
  }
  public init(data?: any, completedCallback?: Function) {
    if (this._subscriber) {
      this._subscriber.unRegister();
    }
    this._data = new HorseData();
    this._api = new HorseApi();
    this._service = new HorseService();
    this._subscriber = new HorseSubscriber();
    this._route = new HorseRoute();

    // 初始化模块
    HorseModule.api.getHorse((data) => {
      this._route.init();
      HorseModule.service.init();
      completedCallback && completedCallback();
    });
  }
}
