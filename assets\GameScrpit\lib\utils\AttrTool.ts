import { Attr<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ddAttr, IAttr } from "../../game/GameDefine";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
/**
 * 全部为0的战斗用属性
 * @returns 初始化的属性列表
 */
export function InitAttrMap(): Map<number, number> {
  let attrMap: any = {};
  for (let idx in AttrEnum) {
    if (Number(idx)) {
      attrMap[idx] = 0;
    }
  }
  return attrMap;
}

/**
 * 把属性加到属性列表
 * @param attrMap 原属性值
 * @param addAttrList 要加的值
 * @returns 返回加上属性后的属性列表
 */
export function AddAttrMap(attrMap: Map<number, number>, addAttrList: number[][]): any {
  for (let idx in addAttrList) {
    let attrItem = addAttrList[idx];
    if (!attrMap[attrItem[0]] && attrMap[attrItem[0]] !== 0) {
      log.error("属性不存在", attrItem);
      attrMap[attrItem[0]] = 0;
    }

    attrMap[attrItem[0]] += attrItem[1];
  }
  return attrMap;
}

/**
 * 二维属性转换为map
 * @param list 二维属性 number[][]
 * @returns 属性map
 */

export function AttrArray2ToMap(list: number[][]) {
  let rs = new Map();
  for (let i in list) {
    if (BaseAttrList.includes(list[i][0])) {
      rs[list[i][0]] = list[i][1];
    } else {
      rs[list[i][0]] = list[i][1] / 10000;
    }
  }
  return rs;
}

/**
 * 一维属性转map
 * @param list 一维属性 number[]
 * @returns
 */
export function AttrArrayToMap(list: number[]): Map<number, number> {
  let rs = new Map<number, number>();

  if (!list || list.length < 2) {
    return rs;
  }
  for (let i = 0; i < list.length; i += 2) {
    rs[list[i]] = list[i + 1];
  }
  return rs;
}

/**
 * 把两个map值相加
 * @param attrReturnMap 最终返回这个map
 * @param attrAddMap 要加的值
 * @returns 返回属性
 */
export function addAttrMap(
  attrReturnMap: Map<number, number> | IAttr | HeroAddAttr,
  attrAddMap: Map<number, number> | IAttr | HeroAddAttr
) {
  for (let key in attrAddMap) {
    if (attrReturnMap[key]) {
      attrReturnMap[key] = (attrReturnMap[key] || 0) + (attrAddMap[key] || 0);
    } else {
      attrReturnMap[key] = attrAddMap[key] || 0;
    }
  }
  return attrReturnMap;
}
