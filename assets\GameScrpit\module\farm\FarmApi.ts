import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ApiHandlerSuccess } from "../../game/mgr/ApiHandler";
import { FarmActionCmd } from "../../game/net/cmd/CmdData";
import { BoolValue, DoubleValueList, IntValue, LongValue } from "../../game/net/protocol/ExternalMessage";
import {
  FarmBeeMetricMessage,
  FarmCollectRequest,
  FarmCollectResponse,
  FarmEnemyResponse,
  FarmLogMessage,
  FarmNeighborResponse,
  FarmRecallAllResponse,
  FarmRecallResponse,
  FarmRewardMessage,
  FarmSimpleMessage,
  FarmTrainMessage,
  FundRewardResponse,
} from "../../game/net/protocol/Farm";
import MsgMgr from "../../lib/event/MsgMgr";
import { PlayerModule } from "../player/PlayerModule";
import { FarmEvent } from "./FarmEvent";
import { FarmModule } from "./FarmModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class FarmApi {
  /** 获取福地和采摘完后奖励 */
  public getFarmInfo(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(FarmTrainMessage, FarmActionCmd.ownFarm, null, (data: FarmTrainMessage) => {
      FarmModule.data.farmTrainMessage = data;
      success && success(data);
    });
  }

  /** 获取福地奖励 */
  public takeReward(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(FarmRewardMessage, FarmActionCmd.takeReward, null, (data: FarmRewardMessage) => {
      if (FarmModule.data.farmTrainMessage) {
        FarmModule.data.farmTrainMessage.rewardGourdList = [];
      }
      success && success(data);
    });
  }

  /**
   * 采集福地
   * @param success
   */
  public collectFarm(data: FarmCollectRequest, success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(
      FarmCollectResponse,
      FarmActionCmd.collectFarm,
      FarmCollectRequest.encode(data),
      (data: FarmCollectResponse) => {
        log.log("collectFarm", data);

        // 更新蜜蜂统计信息
        FarmModule.data.farmTrainMessage.remainFetch = data.remainFetch;
        FarmModule.data.farmTrainMessage.beeMetric = data.farmBeeMetric;

        // 更新蜜蜂派遣情况
        Object.keys(data.updateDispatchMap)
          .map(Number)
          .forEach((key) => {
            FarmModule.data.farmTrainMessage.dispatchMap[key] = data.updateDispatchMap[key];
          });

        // 槽位信息更新
        if (data.farmSlot.userId == PlayerModule.data.playerId) {
          FarmModule.data.farmTrainMessage.slotList[data.farmSlot.rank] = data.farmSlot;
        } else {
          MsgMgr.emit(FarmEvent.OTHER_FARM_REFRESH);
        }

        MsgMgr.emit(FarmEvent.FARM_REFRESH);

        success && success(data);
      }
    );
  }

  /**
   * 雇佣新的蜜蜂
   * @param value
   * @param success
   */
  public hireBee(value: number, success?: ApiHandlerSuccess) {
    let data: IntValue = {
      value: value,
    };
    ApiHandler.instance.request(
      FarmBeeMetricMessage,
      FarmActionCmd.hireBee,
      IntValue.encode(data),
      (data: FarmBeeMetricMessage) => {
        FarmModule.data.farmTrainMessage.beeMetric = data;
        MsgMgr.emit(FarmEvent.FARM_REFRESH);
        success && success(data);
      }
    );
  }

  /**
   * 刷新自己的福地
   * @param value BoolValue true-高级刷新  false-普通刷新
   * @param success
   */
  public refresh(value: boolean, success?: ApiHandlerSuccess) {
    let data: BoolValue = {
      value: value,
    };
    ApiHandler.instance.request(
      FarmTrainMessage,
      FarmActionCmd.refresh,
      BoolValue.encode(data),
      (data: FarmTrainMessage) => {
        FarmModule.data.farmTrainMessage = data;
        MsgMgr.emit(FarmEvent.FARM_REFRESH);
        success && success(data);
      }
    );
  }

  /**
   * 获取周围福地
   * @param value true-重新刷新出一波新的周围福地 false-获取最新的周围福地信息
   * @param success
   */
  public findNeighbor(value: boolean, success?: ApiHandlerSuccess) {
    let data: BoolValue = {
      value: value,
    };
    ApiHandler.instance.request(
      FarmNeighborResponse,
      FarmActionCmd.findNeighbor,
      BoolValue.encode(data),
      (data: FarmNeighborResponse) => {
        FarmModule.data.farmNeighborList = data;
        success && success(data);
      }
    );
  }

  /**
   * 获取敌对福地
   * @param success
   */
  public findEnemy(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(FarmEnemyResponse, FarmActionCmd.findEnemy, null, (data: FarmEnemyResponse) => {
      FarmModule.data.farmEnemyList = data;
      success && success(data);
    });
  }
  /**
   * 获取指定用户的福地
   * @param value
   * @param success
   */
  public getOtherFarm(value: number, success?: ApiHandlerSuccess) {
    let data: LongValue = {
      value: value,
    };
    ApiHandler.instance.request(
      FarmSimpleMessage,
      FarmActionCmd.getOtherFarm,
      LongValue.encode(data),
      (data: FarmSimpleMessage) => {
        // 更新缓存信息
        if (FarmModule.data.data1) {
          for (let i = 0; i < FarmModule.data.data1.length; i++) {
            if (FarmModule.data.data1[i].simpleMessage.userId == data.simpleMessage.userId) {
              FarmModule.data.data1[i] = data;
              break;
            }
          }
        }

        if (FarmModule.data.farmEnemyList) {
          for (let i = 0; i < FarmModule.data.farmEnemyList.farmList.length; i++) {
            if (FarmModule.data.farmEnemyList.farmList[i].simpleMessage.userId == data.simpleMessage.userId) {
              FarmModule.data.farmEnemyList.farmList[i] = data;
              break;
            }
          }
        }
        success && success(data);
      }
    );
  }

  public getLog(success?: ApiHandlerSuccess) {
    let data: LongValue = {
      value: 0,
    };
    ApiHandler.instance.list(FarmLogMessage, FarmActionCmd.getLog, LongValue.encode(data), (data: FarmLogMessage[]) => {
      success && success(data);
    });
  }

  public recall(value: number, success?: ApiHandlerSuccess) {
    let data: LongValue = {
      value: value,
    };
    ApiHandler.instance.request(
      FarmRecallResponse,
      FarmActionCmd.recall,
      LongValue.encode(data),
      (resp: FarmRecallResponse) => {
        FarmModule.data.farmTrainMessage.slotList[resp.farmSlot.rank] = resp.farmSlot;
        FarmModule.data.farmTrainMessage.beeMetric = resp.farmBeeMetric;
        delete FarmModule.data.farmTrainMessage.dispatchMap[resp.delDispatchKey];
        MsgMgr.emit(FarmEvent.FARM_REFRESH);
        success && success(data);
      }
    );
  }

  public addAttention(userId: number, success?: ApiHandlerSuccess) {
    let data: LongValue = {
      value: userId,
    };
    ApiHandler.instance.request(BoolValue, FarmActionCmd.addAttention, LongValue.encode(data), (resp: BoolValue) => {
      success && success(data);
    });
  }

  public recallAll(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(FarmRecallAllResponse, FarmActionCmd.recallAll, null, (resp: FarmRecallAllResponse) => {
      if (resp.updateSlotList) {
        for (let i = 0; i < resp.updateSlotList.length; i++) {
          let slot = resp.updateSlotList[i];
          FarmModule.data.farmTrainMessage.slotList[slot.rank] = slot;
        }
      }

      // 移除所有派遣信息
      FarmModule.data.farmTrainMessage.dispatchMap = {};
      // 更新统计信息
      FarmModule.data.farmTrainMessage.beeMetric = resp.farmBeeMetric;

      MsgMgr.emit(FarmEvent.FARM_REFRESH);
      success && success(resp);
    });
  }

  public takeFundReward(idx: number, success?: ApiHandlerSuccess) {
    let data: IntValue = {
      value: idx,
    };
    ApiHandler.instance.request(
      FundRewardResponse,
      FarmActionCmd.takeFundReward,
      IntValue.encode(data),
      (resp: FundRewardResponse) => {
        if (resp.fundList) {
          FarmModule.data.farmTrainMessage.fundList = resp.fundList;
        }
        success && success(resp);
      }
    );
  }
}

// ==================== FarmAction  ====================
// 路由: 22 - 1  --- 【获取福地和采摘完后奖励】 --- 【FarmAction:60】【getFarmInfo】
//     方法返回值: FarmTrainResponse

// 路由: 22 - 2  --- 【采集福地】 --- 【FarmAction:71】【collectFarm】
//     方法参数: FarmCollectRequest
//     方法返回值: FarmCollectResponse

// 路由: 22 - 3  --- 【雇佣新的蜜蜂】 --- 【FarmAction:110】【hireBee】
//     方法参数: IntValue
//     方法返回值: FarmBeeMetricMessage

// 路由: 22 - 4  --- 【刷新自己的福地】 --- 【FarmAction:135】【refresh】
//     方法参数: BoolValue advanceRefresh true-高级刷新  false-普通刷新
//     方法返回值: FarmTrainMessage

// 路由: 22 - 5  --- 【召回蜜蜂，成功返回后 前端需要删除派遣的信息  0-表示成功;1-表示失败，蜜蜂采集信息不存在或;2-表示失败，葫芦采摘结束，已召回】 --- 【FarmAction:90】【recall】
//     方法参数: FarmRecallRequest
//     方法返回值: FarmRecallResponse

// 路由: 22 - 6  --- 【用于定时任务获取采集的槽位的最新信息，如果是自己采集胜利，并获得道具奖励】 --- 【FarmAction:161】【scheduleSlotUpdate】
//     方法参数: FarmSlotUpdateRequest
//     方法返回值: FarmSlotUpdateResponse

// 路由: 22 - 7  --- 【获取周围福地】 --- 【FarmAction:181】【findNeighbor】
//     方法参数: BoolValue isNew true-重新刷新出一波新的周围福地 false-获取最新的周围福地信息
//     方法返回值: FarmFindResponse

// 路由: 22 - 8  --- 【获取敌对福地】 --- 【FarmAction:193】【findEnemy】
//     方法返回值: FarmFindResponse

// 路由: 22 - 9  --- 【获取指定用户的福地】 --- 【FarmAction:205】【getOtherFarm】
//     方法参数: LongValue
//     方法返回值: FarmFindMessage

// 路由: 22 - 11  --- 【获取日志】 --- 【FarmAction:217】【getLog】
//     方法返回值: ByteValueList<FarmLogMessage>

// 路由: 22 - 102  --- 【测试：获取某人的福地信息(未触发刷新)】 --- 【FarmAction:239】【testGetFarm】
//     方法参数: LongValue
//     方法返回值: FarmTrainMessage

// 路由: 22 - 103  --- 【测试：获取某人的所有通知信息】 --- 【FarmAction:251】【testGetNotice】
//     方法参数: LongValue
//     方法返回值: StringValue

// 路由: 22 - 104  --- 【后侧测试：开奖励】 --- 【FarmAction:269】【testOpenReward】
//     方法返回值: StringValue

// 路由: 22 - 110  --- 【测试：重置自身福地】 --- 【FarmAction:228】【testResetFarmTrain】
//     方法返回值: FarmTrainMessage
