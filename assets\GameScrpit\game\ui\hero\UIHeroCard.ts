import { _decorator, math, Node, Sprite, Component } from "cc";
import { Label } from "cc";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { Star } from "../../common/Star";
import { HeroModule } from "../../../module/hero/HeroModule";
import { HeroTypeIcon, HeroColorCard, HeroColorBord } from "../../../module/hero/HeroConstant";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import ResMgr from "../../../lib/common/ResMgr";
import { IConfigHero } from "../../JsonDefine";
import { PetModule } from "../../../module/pet/PetModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UIHeroCard")
export class UIHeroCard extends Component {
  /**
   * 配置数据
   */
  config: IConfigHero = undefined;

  /**
   * 等级，0表示未获得
   */
  level: number = 0;

  /**
   * 所片列表的位置
   */
  position: number = 0;

  // hero_bg
  @property(Node)
  private heroBg: Node;
  @property(Node)
  private hero: Node;
  @property(Node)
  private lbLevel: Node;
  @property(Node)
  private lbName: Node;
  @property(Node)
  private type: Node;
  @property(Node)
  private colorBord: Node;
  @property(Node)
  private star: Node;
  @property(Node)
  private imprintBg: Node;

  @property(Node)
  private nodeBgHasPet: Node;

  start() {
    MsgMgr.on(MsgEnum.ON_HERO_UPDATE, this.handleHeroChange, this);
  }

  protected onDestroy(): void {
    MsgMgr.off(MsgEnum.ON_HERO_UPDATE, this.handleHeroChange, this);
  }
  /**打开界面时的方法 ---- 继承---界面写 */
  private handleHeroChange() {
    if (!this.config) {
      return;
    }
    let hero = HeroModule.data.getHeroMessage(this.config.id);
    if (!hero) {
      // log.error("hero message is null");
      return;
    }
    if (this.config.id == hero.heroId && this.level == 0) {
      //获得新英雄
      let index = this.node.parent.getChildByName("nodeFenGeXian").getSiblingIndex();
      this.node.setSiblingIndex(index);
      this.refreshUI(this.config, hero.level);
    } else if (this.config.id == hero.heroId) {
      this.refreshUI(this.config, hero.level);
    }
  }
  private async refreshUI(heroConfig: IConfigHero, lv: number) {
    // log.log("hero card data update");
    this.node.name = `hero_card_${heroConfig.id}`;
    this.config = heroConfig;
    this.level = lv;
    let imprintLevel = HeroModule.data.getHeroImprintsLv(heroConfig.id);
    if (imprintLevel > 0 && heroConfig.talentList.length > 0) {
      this.star.active = true;
      this.imprintBg.active = true;
      this.star.getComponent(Star).setStar(imprintLevel);
    } else {
      this.star.active = false;
      this.imprintBg.active = false;
    }
    if (this.level > 0) {
      this.lbLevel.parent.active = true;
      this.lbLevel.getComponent(Label).string = this.level + "";
    } else {
      this.lbLevel.parent.active = false;
      this.node.getComponentsInChildren(Sprite).forEach((item) => {
        item.color = math.color("#6E6E6E");
      });
    }
    ResMgr.setSpriteFrame(
      BundleEnum.BUNDLE_COMMON_UI,
      `atlas_imgs/${HeroTypeIcon[`type_${this.config.type}`]}`,
      this.type.getComponent(Sprite)
    );
    ResMgr.setSpriteFrame(
      BundleEnum.BUNDLE_COMMON_HERO_ICON,
      `images/${HeroColorCard[`color_${this.config.color}`]}`,
      this.heroBg.getComponent(Sprite)
    );

    ResMgr.setSpriteFrame(
      BundleEnum.BUNDLE_COMMON_HERO_HALF,
      `images/herohalf_${this.config.id}`,
      this.hero.getComponent(Sprite)
    );
    ResMgr.setSpriteFrame(
      BundleEnum.BUNDLE_COMMON_HERO_ICON,
      `images/${HeroColorBord[`color_${this.config.color}`]}`,
      this.colorBord.getComponent(Sprite)
    );

    this.lbName.getComponent(Label).string = this.config.name;

    // type 种族
    // this.bgJob.getComponent(Sprite).spriteFrame = this.Jobs[this.config.type - 1];

    // 灵兽显示
    // let petId = JsonMgr.instance.jsonList.c_pet[]
    if (PetModule.data.getPet(heroConfig.petId)) {
      this.nodeBgHasPet.active = true;
    } else {
      this.nodeBgHasPet.active = false;
    }
  }
  async initInfo(heroConfig: IConfigHero, lv: number, position: number) {
    this.start();
    this.position = position;
    this.refreshUI(heroConfig, lv);
  }
  async updatePosition(position: number) {
    this.position = position;
  }
}
