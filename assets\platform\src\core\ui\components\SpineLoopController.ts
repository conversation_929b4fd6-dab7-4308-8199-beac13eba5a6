import { _decorator, ccenum, CCString, Component, instantiate, sp, Node } from "cc";
const { ccclass, property } = _decorator;

/**
 * @en
 * layout direction.
 *
 * @zh
 * 展示方式。
 */
enum DisplayMode {
  /**
   * @en
   * Loop display mode
   *
   * @zh
   * 循环展示
   */
  LOOP = 0,
  /**
   * @en
   * Stack display mode
   *
   * @zh
   * 叠加展示
   */
  STACK = 1,

  RESET = 2,
}
ccenum(DisplayMode);

@ccclass("SpineLoopController")
export class SpineLoopController extends Component {
  @property({ type: CCString })
  private anim1: string;
  @property({ type: CCString })
  private anim2: string;
  @property({
    type: DisplayMode,
    tooltip: "动画展示方式 \n1:LOOP为循环展示（anim1播放完成后，循环播放anim2）\n2:STACK为叠加展示",
  })
  private displayMode: DisplayMode = DisplayMode.LOOP;

  private _cloneNode: Node = null;

  protected onLoad(): void {}
  protected start(): void {
    if (this.node.name != "spine_loop_child") {
      this._cloneNode = instantiate(this.node);
      this._cloneNode.name = "spine_loop_child";
      let child = instantiate(this._cloneNode);
      child.setPosition(0, 0);
      child.setParent(this.node);
      this.node.setScale(1, 1, 1);
      this.node.getComponent(sp.Skeleton)?.destroy();
    }
  }
  protected onEnable(): void {
    if (this.node.name == "spine_loop_child") {
      if (this.anim1) {
        this.node.getComponent(sp.Skeleton).setAnimation(0, this.anim1, false);
      }
      this.node.getComponent(sp.Skeleton).setCompleteListener((trackEntry) => {
        if (trackEntry.animation.name == this.anim1) {
          if (this.anim2 && this.anim2.length > 0) {
            this.node.getComponent(sp.Skeleton).setAnimation(0, this.anim2, true);
          } else {
            this.node.destroy();
            // if (this.displayMode == DisplayMode.STACK) {
            // }
            // this.node.active = false;
          }
        }
      });
    }
  }
  public play() {
    if (this.node.name != "spine_loop_child" && this._cloneNode) {
      let child = instantiate(this._cloneNode);
      child.setPosition(0, 0);
      this.node.addChild(child);
      this.node.getComponent(sp.Skeleton)?.destroy();
      this.node.setScale(1, 1, 1);
    }
  }
}
