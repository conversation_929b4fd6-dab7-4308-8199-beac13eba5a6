import { _decorator, Asset, Camera, Component, game, Node, UITransform, view } from "cc";
import TickerMgr from "./ticker/TickerMgr";
import * as asy from "./ext/async.js";
import { TipsMgr } from "../../platform/src/TipsHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";

const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

//npm run buildpb

@ccclass("StartUp")
export class StartUp extends Component {
  private static _instance: StartUp = null;
  public static get instance(): StartUp {
    return this._instance;
  }

  // 提示层
  protected _tipsMgr: TipsMgr;

  @property(Node)
  public uiRoot: Node = null;
  @property(Node)
  public gameRoot: Node = null;

  @property(Camera)
  public uiCamera: Camera = null;
  @property(Camera)
  public gameCamera: Camera = null;

  @property(Asset)
  public versionManifest: Asset = null;

  @property(Node)
  public nodeBorder: Node = null;

  onLoad() {
    StartUp._instance = this;
  }

  public static get tipsMgr(): TipsMgr {
    return this.instance._tipsMgr;
  }

  start() {
    this.initProcess();
  }

  protected initProcess() {
    if (this.uiRoot == null) {
      log.error("游戏ui根节点没有指定");
      return;
    }

    if (this.gameRoot == null) {
      log.error("游戏对象根节点没有指定");
      return;
    }

    game.frameRate = 60;

    let tasks = [];
    tasks.push(this.startGame.bind(this));
    asy.default.eachSeries(
      tasks,
      (task, completedCallback) => {
        task(completedCallback);
      },
      () => {
        log.info("==== 启动任务全部完成");
      }
    );
  }

  //启动游戏
  protected async startGame(completedCallback) {
    // this.gameRoot.getComponent(UITransform).setContentSize(StartUp.instance.getVisibleSize());
    completedCallback && completedCallback();
  }

  update(dt) {
    try {
      TickerMgr.instance.doTick(dt);
    } catch (erro) {
      log.error("==== 启动失败", erro);
    }
  }

  public getVisibleSize() {
    return this.nodeBorder.getComponent(UITransform).contentSize;
  }

  public getWorldCenter() {
    return this.nodeBorder.getWorldPosition();
  }

  public adaptScreen() {
    this.nodeBorder.getComponent(UITransform).setContentSize(view.getDesignResolutionSize());
  }
}
