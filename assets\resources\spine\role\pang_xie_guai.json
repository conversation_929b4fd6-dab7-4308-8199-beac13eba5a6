{"skeleton": {"hash": "Vh+3dMofH3gZ8b6jOD1ONWOEmcQ=", "spine": "3.8.75", "x": -112.86, "y": -13.71, "width": 282.02, "height": 181, "images": "./images/", "audio": "D:/spine导出/S_时空裂隙_怪/螃蟹怪"}, "bones": [{"name": "root"}, {"name": "pang<PERSON><PERSON>l", "parent": "root", "length": 216.24, "rotation": 0.52, "x": 11.19, "y": 0.88}, {"name": "pangxie1", "parent": "pang<PERSON><PERSON>l", "length": 78.8, "rotation": 0.03, "x": 15.59, "y": 51.29}, {"name": "bone2", "parent": "pangxie1", "length": 29.94, "rotation": 106.29, "x": 2.33, "y": 6.76}, {"name": "xiejio1", "parent": "pangxie1", "length": 26.82, "rotation": 157.89, "x": -50.01, "y": -25.31, "color": "002fffff"}, {"name": "bone4", "parent": "xiejio1", "length": 35.6, "rotation": 102.09, "x": 26.82, "color": "002fffff"}, {"name": "xiejio2", "parent": "pangxie1", "length": 26.82, "rotation": 148.08, "x": -67.78, "y": -15.59, "color": "ff0500ff"}, {"name": "bone5", "parent": "xiejio2", "length": 35.6, "rotation": 92.28, "x": 23.71, "y": -0.81, "color": "ff0500ff"}, {"name": "xiejio3", "parent": "pangxie1", "length": 26.82, "rotation": 153.86, "x": -67.69, "y": -5.73, "color": "ffd700ff"}, {"name": "bone6", "parent": "xiejio3", "length": 35.6, "rotation": 76.27, "x": 26.82, "color": "ffd700ff"}, {"name": "xiejio4", "parent": "pangxie1", "length": 30.59, "rotation": 26.48, "x": -0.95, "y": -19.74, "color": "f7ff00ff"}, {"name": "bone7", "parent": "xiejio4", "length": 34.53, "rotation": -104, "x": 30.59, "color": "f7ff00ff"}, {"name": "xiejio5", "parent": "pangxie1", "length": 30.59, "rotation": 23.63, "x": 14.88, "y": -21.01, "color": "0014ffff"}, {"name": "bone8", "parent": "xiejio5", "length": 34.53, "rotation": -83.58, "x": 30.59, "color": "0014ffff"}, {"name": "xiejio6", "parent": "pangxie1", "length": 30.59, "rotation": 26.48, "x": 16.23, "y": -26.02, "color": "ff0500ff"}, {"name": "bone9", "parent": "xiejio6", "length": 34.53, "rotation": -104, "x": 30.59, "color": "ff0500ff"}, {"name": "bone3", "parent": "bone2", "length": 16.28, "rotation": -13.47, "x": 31.63, "y": 0.6}, {"name": "bone10", "parent": "bone2", "x": 65.3, "y": 73.44}, {"name": "bone11", "parent": "bone2", "rotation": -13.47, "x": 28.82, "y": -43.56}, {"name": "bone12", "parent": "bone10", "length": 28.98, "rotation": 97.9, "x": 7.91, "y": -8.59, "color": "21ff00ff"}, {"name": "bone13", "parent": "bone10", "length": 32.3, "rotation": 116.54, "x": -5.59, "y": 10.05, "color": "21ff00ff"}, {"name": "bone14", "parent": "bone13", "length": 23.62, "rotation": 52.89, "x": 34.08, "y": 1.88, "color": "21ff00ff"}, {"name": "bone15", "parent": "bone14", "length": 32.88, "rotation": 2.3, "x": 24.93, "y": 0.12, "color": "21ff00ff"}, {"name": "bone16", "parent": "bone15", "length": 32.35, "rotation": 57.93, "x": 32.88, "color": "21ff00ff"}, {"name": "bone17", "parent": "bone14", "length": 30.4, "rotation": 53.68, "x": 18.94, "y": 9.34, "color": "21ff00ff"}, {"name": "bone18", "parent": "bone17", "length": 28.73, "rotation": -33.85, "x": 30.4, "color": "21ff00ff"}, {"name": "bone19", "parent": "bone11", "length": 22.89, "rotation": -94.2, "x": -7.76, "y": 12.61, "color": "39ff00ff"}, {"name": "bone20", "parent": "bone11", "length": 23.39, "rotation": -137.99, "x": -8.86, "y": 13.92, "color": "39ff00ff"}, {"name": "bone21", "parent": "bone20", "length": 35.85, "rotation": 24.03, "x": 23.39, "y": 0.02, "color": "39ff00ff"}, {"name": "bone22", "parent": "bone21", "length": 28.91, "rotation": 3.36, "x": 35.85, "color": "39ff00ff"}, {"name": "target1", "parent": "pang<PERSON><PERSON>l", "rotation": -0.52, "x": 66.79, "y": 5.26, "color": "ff3f00ff"}, {"name": "target2", "parent": "pang<PERSON><PERSON>l", "rotation": -0.52, "x": 75.54, "y": 13.17, "color": "ff3f00ff"}, {"name": "target3", "parent": "pang<PERSON><PERSON>l", "rotation": -0.52, "x": 49.36, "y": 11.5, "color": "ff3f00ff"}, {"name": "target4", "parent": "pang<PERSON><PERSON>l", "rotation": -0.52, "x": -65.97, "y": -0.1, "color": "ff3f00ff"}, {"name": "target5", "parent": "pang<PERSON><PERSON>l", "rotation": -0.52, "x": -89.75, "y": 18.12, "color": "ff3f00ff"}, {"name": "target6", "parent": "pang<PERSON><PERSON>l", "rotation": -0.52, "x": -98.95, "y": 30.04, "color": "ff3f00ff"}, {"name": "bone37", "parent": "bone2", "x": 0.93, "y": -4.01, "color": "abe323ff"}, {"name": "bone38", "parent": "bone2", "x": 15.29, "y": -8.35}, {"name": "dg01", "parent": "root", "x": -55.32, "y": 891.05, "scaleX": 2.0367, "scaleY": 2.0367}, {"name": "dg1", "parent": "root", "x": 82.21, "y": 829.67, "scaleX": 1.8056, "scaleY": 1.8056}, {"name": "bone23", "parent": "bone11", "length": 32.3, "rotation": -170.77, "x": -7.02, "y": 8.98, "color": "c400ffff"}, {"name": "bone24", "parent": "bone23", "length": 23.62, "rotation": 52.89, "x": 34.08, "y": 1.88, "color": "c400ffff"}, {"name": "bone25", "parent": "bone24", "length": 32.88, "rotation": 2.3, "x": 24.93, "y": 0.12, "color": "c400ffff"}, {"name": "bone26", "parent": "bone25", "length": 32.35, "rotation": 57.93, "x": 32.88, "color": "c400ffff"}, {"name": "bone27", "parent": "bone24", "length": 30.4, "rotation": 53.68, "x": 18.94, "y": 9.34, "color": "c400ffff"}, {"name": "bone28", "parent": "bone27", "length": 28.73, "rotation": -33.85, "x": 30.4, "color": "c400ffff"}, {"name": "qianall", "parent": "root", "length": 338.27, "rotation": 12.7, "x": 303.94, "y": -320.44, "scaleX": 1.1995, "scaleY": 1.1995}, {"name": "qian", "parent": "qianall", "rotation": 0.76, "x": -132.11, "y": 399.34, "scaleX": 1.1745, "scaleY": 1.1745}, {"name": "qian2", "parent": "qianall", "rotation": 0.76, "x": -40.71, "y": 436.74, "scaleX": 0.8894, "scaleY": 0.8894}, {"name": "qian3", "parent": "qianall", "rotation": 0.76, "x": -106.32, "y": 320.79, "scaleX": 1.0608, "scaleY": 1.0608}, {"name": "qian4", "parent": "qianall", "rotation": 0.76, "x": -44.48, "y": 382.92, "scaleX": 0.8894, "scaleY": 0.8894}, {"name": "txtx3", "parent": "root", "x": 161.67, "y": 94.74, "scaleX": 2.606, "scaleY": 1.9387}, {"name": "txhit", "parent": "root", "x": 294.27, "y": 82.54, "scaleX": 2.867, "scaleY": 2.867}, {"name": "txtx4", "parent": "root", "x": 141.85, "y": 113.52, "scaleX": 2.606, "scaleY": 1.9387}, {"name": "txhit2", "parent": "root", "x": 346.77, "y": 109.55, "scaleX": 2.867, "scaleY": 2.867}, {"name": "txhit3", "parent": "root", "x": 395.73, "y": 62.29, "scaleX": 2.3459, "scaleY": 2.3459}, {"name": "txhit4", "parent": "root", "x": 383.91, "y": 170.32, "scaleX": 2.3459, "scaleY": 2.3459}], "slots": [{"name": "pangxie1_25", "bone": "bone23"}, {"name": "pangxie1_26", "bone": "bone24"}, {"name": "pangxie1_28", "bone": "bone27"}, {"name": "pangxie1_27", "bone": "bone25"}, {"name": "pangxie1_15", "bone": "xiejio3", "attachment": "pangxie1_013"}, {"name": "pangxie1_16", "bone": "bone6", "attachment": "pangxie1_014"}, {"name": "pangxie1_05", "bone": "bone20", "attachment": "pangxie1_05"}, {"name": "pangxie1_06", "bone": "bone19", "attachment": "pangxie1_06"}, {"name": "pangxie1_17", "bone": "xiejio4", "attachment": "pangxie1_013"}, {"name": "pangxie1_20", "bone": "bone7", "attachment": "pangxie1_014"}, {"name": "pangxie1_21", "bone": "xiejio5", "attachment": "pangxie1_013"}, {"name": "pangxie1_22", "bone": "bone8", "attachment": "pangxie1_014"}, {"name": "pangxie1_23", "bone": "xiejio6", "attachment": "pangxie1_013"}, {"name": "pangxie1_24", "bone": "bone9", "attachment": "pangxie1_014"}, {"name": "pangxie1_09", "bone": "bone2", "attachment": "pangxie1_09"}, {"name": "pangxie1_13", "bone": "xiejio2", "attachment": "pangxie1_013"}, {"name": "pangxie1_14", "bone": "bone5", "attachment": "pangxie1_014"}, {"name": "pangxie1_013", "bone": "xiejio1", "attachment": "pangxie1_013"}, {"name": "pangxie1_014", "bone": "bone4", "attachment": "pangxie1_014"}, {"name": "pangxie1_015", "bone": "bone3", "attachment": "pangxie1_015"}, {"name": "pangxie1_016", "bone": "bone12", "attachment": "pangxie1_016"}, {"name": "pangxie1_017", "bone": "bone13", "attachment": "pangxie1_017"}, {"name": "pangxie1_18", "bone": "bone14", "attachment": "pangxie1_018"}, {"name": "pangxie1_19", "bone": "bone17", "attachment": "pangxie1_018"}, {"name": "pangxie1_018", "bone": "bone15", "attachment": "pangxie1_018"}, {"name": "pangxie1_019", "bone": "bone12", "attachment": "pangxie1_019"}, {"name": "tx1/dg_01", "bone": "dg01", "blend": "additive"}, {"name": "tx1/bc_01", "bone": "dg1", "blend": "additive"}, {"name": "eye", "bone": "bone3"}, {"name": "tx2/qiliu2_add_01", "bone": "txtx3", "blend": "additive"}, {"name": "tx2/qiliu2_add_1", "bone": "txtx4", "blend": "additive"}, {"name": "hit/hit0001", "bone": "txhit"}, {"name": "hit/hit2", "bone": "txhit2"}, {"name": "hit/hit4", "bone": "txhit3"}, {"name": "hit/hit6", "bone": "txhit4"}, {"name": "hit/hit1", "bone": "txhit", "blend": "additive"}, {"name": "hit/hit3", "bone": "txhit2", "blend": "additive"}, {"name": "hit/hit5", "bone": "txhit3", "blend": "additive"}, {"name": "hit/hit7", "bone": "txhit4", "blend": "additive"}, {"name": "pangxie1_033", "bone": "qian"}, {"name": "pangxie1_33", "bone": "qian2"}, {"name": "pangxie1_35", "bone": "qian4"}, {"name": "pangxie1_34", "bone": "qian3"}], "ik": [{"name": "target1", "bones": ["xiejio6", "bone9"], "target": "target1", "bendPositive": false}, {"name": "target2", "order": 1, "bones": ["xiejio5", "bone8"], "target": "target2", "bendPositive": false}, {"name": "target3", "order": 2, "bones": ["xiejio4", "bone7"], "target": "target3", "bendPositive": false}, {"name": "target4", "order": 3, "bones": ["xiejio1", "bone4"], "target": "target4"}, {"name": "target5", "order": 4, "bones": ["xiejio2", "bone5"], "target": "target5"}, {"name": "target6", "order": 5, "bones": ["xiejio3", "bone6"], "target": "target6"}], "transform": [{"name": "body", "order": 6, "bones": ["bone38"], "target": "bone37", "x": 14.36, "y": -4.35, "rotateMix": -1, "translateMix": -1, "scaleMix": -1, "shearMix": -1}, {"name": "s1", "order": 7, "bones": ["bone10"], "target": "bone37", "x": 64.38, "y": 77.45, "rotateMix": 0.3, "translateMix": 0.3, "scaleMix": 0.3, "shearMix": 0.3}, {"name": "s2", "order": 8, "bones": ["bone11"], "target": "bone37", "rotation": -13.47, "x": 27.89, "y": -39.55, "rotateMix": 0.3, "translateMix": 0.3, "scaleMix": 0.3, "shearMix": 0.3}], "skins": [{"name": "default", "attachments": {"hit/hit7": {"hit/hit0002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -38, -38, -38, -38, 39, 39, 39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 77}, "hit/hit0004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -38, -38, -38, -38, 39, 39, 39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 77}, "hit/hit0005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -38, -38, -38, -38, 39, 39, 39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 77}, "hit/hit0008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -38, -38, -38, -38, 39, 39, 39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 77}}, "pangxie1_09": {"pangxie1_09": {"type": "mesh", "uvs": [0.47961, 0, 0.73875, 0.08731, 0.87886, 0.21511, 0.99245, 0.3709, 0.99273, 0.5561, 0.96633, 0.67712, 0.89886, 0.79099, 0.81603, 0.87887, 0.70037, 0.95837, 0.5215, 0.99691, 0.31039, 0.96056, 0.18906, 0.87816, 0.09047, 0.74351, 0.02815, 0.62795, 0.00205, 0.39597, 0.02475, 0.24335, 0.09608, 0.09497, 0.18725, 0.0299, 0.32636, 1e-05, 0.61025, 0.14275, 0.70649, 0.40667, 0.70071, 0.71281, 0.60255, 0.88172, 0.34077, 0.21137, 0.40814, 0.41195, 0.39852, 0.66003, 0.2965, 0.77879, 0.19064, 0.36708, 0.18294, 0.57821, 0.81428, 0.23248, 0.88742, 0.42778, 0.88164, 0.66267, 0.81428, 0.81046], "triangles": [9, 22, 8, 22, 9, 25, 11, 26, 10, 25, 9, 26, 9, 10, 26, 8, 32, 7, 8, 21, 32, 8, 22, 21, 22, 25, 21, 7, 32, 6, 11, 12, 26, 6, 32, 31, 32, 21, 31, 6, 31, 5, 12, 28, 26, 26, 28, 25, 12, 13, 28, 21, 25, 20, 21, 20, 31, 20, 25, 24, 5, 31, 4, 31, 30, 4, 31, 20, 30, 25, 28, 24, 13, 14, 28, 28, 27, 24, 28, 14, 27, 30, 3, 4, 20, 29, 30, 29, 2, 30, 30, 2, 3, 27, 23, 24, 24, 19, 20, 19, 23, 0, 19, 24, 23, 29, 19, 1, 29, 20, 19, 14, 15, 27, 15, 16, 27, 16, 17, 27, 27, 17, 23, 29, 1, 2, 17, 18, 23, 23, 18, 0, 19, 0, 1], "vertices": [3, 3, 55.97, 15.08, 0.49, 36, 55.04, 19.09, 0.21, 37, 40.67, 23.44, 0.3, 3, 3, 37.88, -15.45, 0.56, 36, 36.95, -11.44, 0.14, 37, 22.58, -7.1, 0.3, 2, 3, 20.61, -29.7, 0.7, 37, 5.32, -21.34, 0.3, 2, 3, 1.78, -39.78, 0.7, 37, -13.52, -31.43, 0.3, 2, 3, -15.43, -34.61, 0.7, 37, -30.72, -26.26, 0.3, 2, 3, -25.65, -27.85, 0.7, 37, -40.94, -19.5, 0.3, 2, 3, -33.62, -16.06, 0.7, 37, -48.91, -7.71, 0.3, 3, 3, -38.59, -3.05, 0.56, 36, -39.52, 0.96, 0.14, 37, -53.88, 5.3, 0.3, 3, 3, -41.51, 13.91, 0.56, 36, -42.44, 17.91, 0.14, 37, -56.81, 22.26, 0.3, 3, 3, -38.2, 37.76, 0.49, 36, -39.13, 41.77, 0.21, 37, -53.49, 46.11, 0.3, 3, 3, -26.69, 63.61, 0.56, 36, -27.62, 67.62, 0.14, 37, -41.99, 71.97, 0.3, 3, 3, -14.37, 76.74, 0.63, 36, -15.3, 80.75, 0.07, 37, -29.66, 85.1, 0.3, 2, 3, 1.93, 85.51, 0.7, 37, -13.36, 93.86, 0.3, 2, 3, 15.06, 90.2, 0.7, 37, -0.23, 98.55, 0.3, 2, 3, 37.6, 87, 0.7, 37, 22.31, 95.36, 0.3, 2, 3, 50.9, 79.82, 0.7, 37, 35.6, 88.18, 0.3, 2, 3, 61.92, 66.57, 0.7, 37, 46.63, 74.93, 0.3, 3, 3, 64.45, 53.14, 0.63, 36, 63.53, 57.15, 0.07, 37, 49.16, 61.49, 0.3, 3, 3, 61.87, 34.59, 0.56, 36, 60.94, 38.6, 0.14, 37, 46.58, 42.95, 0.3, 2, 3, 37.68, 2.46, 0.7, 36, 36.75, 6.47, 0.3, 2, 3, 9.47, -2.37, 0.7, 36, 8.54, 1.64, 0.3, 2, 3, -18.73, 6.96, 0.7, 36, -19.66, 10.97, 0.3, 2, 3, -30.63, 24.21, 0.964, 36, -31.56, 28.21, 0.036, 2, 3, 41.69, 38.7, 0.8, 36, 40.76, 42.7, 0.2, 2, 3, 20.47, 35.76, 0.8, 36, 19.55, 39.76, 0.2, 2, 3, -2.19, 43.95, 0.8, 36, -3.12, 47.96, 0.2, 2, 3, -9.28, 60.27, 0.8, 36, -10.21, 64.28, 0.2, 2, 3, 33.02, 62.18, 0.9, 36, 32.09, 66.19, 0.1, 2, 3, 13.71, 69.09, 0.9, 36, 12.79, 73.1, 0.1, 2, 3, 21.49, -20.99, 0.8, 36, 20.56, -16.98, 0.2, 2, 3, 0.54, -24.81, 0.8, 36, -0.39, -20.8, 0.2, 2, 3, -21.04, -17.48, 0.8, 36, -21.97, -13.47, 0.2, 3, 3, -32.17, -4.75, 0.77235, 36, -33.1, -0.74, 0.096, 37, -47.46, 3.61, 0.13165], "hull": 19, "edges": [0, 36, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 38, 38, 40, 40, 42, 42, 44, 44, 18, 46, 48, 48, 50, 50, 52, 54, 56, 58, 60, 60, 62, 62, 64], "width": 133, "height": 97}}, "pangxie1_33": {"pangxie1_033": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -37, -64, -37, -64, 38, 64, 38], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 128, "height": 75}}, "eye": {"eye": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [11.25, -34.87, 16.25, 29.94, 36.19, 28.4, 31.19, -36.4], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 65, "height": 20}}, "pangxie1_35": {"pangxie1_033": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -37, -64, -37, -64, 38, 64, 38], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 128, "height": 75}}, "hit/hit4": {"hit/hit0002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -38, -38, -38, -38, 39, 39, 39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 77}, "hit/hit0004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -38, -38, -38, -38, 39, 39, 39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 77}, "hit/hit0005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -38, -38, -38, -38, 39, 39, 39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 77}, "hit/hit0008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -38, -38, -38, -38, 39, 39, 39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 77}}, "tx2/qiliu2_add_1": {"tx2/qiliu2_add_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [74, -57, -73, -57, -73, 57, 74, 57], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 147, "height": 114}, "tx2/qiliu2_add_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [74, -57, -73, -57, -73, 57, 74, 57], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 147, "height": 114}, "tx2/qiliu2_add_13": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [74, -57, -73, -57, -73, 57, 74, 57], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 147, "height": 114}, "tx2/qiliu2_add_19": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [74, -57, -73, -57, -73, 57, 74, 57], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 147, "height": 114}}, "hit/hit5": {"hit/hit0002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -38, -38, -38, -38, 39, 39, 39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 77}, "hit/hit0004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -38, -38, -38, -38, 39, 39, 39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 77}, "hit/hit0005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -38, -38, -38, -38, 39, 39, 39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 77}, "hit/hit0008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -38, -38, -38, -38, 39, 39, 39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 77}}, "pangxie1_033": {"pangxie1_033": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -37, -64, -37, -64, 38, 64, 38], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 128, "height": 75}}, "pangxie1_21": {"pangxie1_013": {"type": "mesh", "uvs": [0.96861, 0.24258, 0.97277, 0.63339, 0.63018, 0.99399, 0.40737, 0.83544, 0.03081, 0.43482, 0.03021, 0.03198, 0.82152, 0.0329], "triangles": [4, 5, 6, 3, 4, 6, 3, 6, 0, 3, 0, 1, 2, 3, 1], "vertices": [-1.11, 10.4, -6.55, 0.02, -1.71, -14.6, 6.81, -13.6, 23, -8.37, 28.51, 2.38, 5.94, 13.87], "hull": 7, "edges": [0, 12, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12], "width": 32, "height": 30}}, "pangxie1_017": {"pangxie1_017": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15.29, 34.77, 52.36, -0.26, 22.14, -32.24, -14.93, 2.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 51, "height": 44}}, "hit/hit3": {"hit/hit0002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -38, -38, -38, -38, 39, 39, 39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 77}, "hit/hit0004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -38, -38, -38, -38, 39, 39, 39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 77}, "hit/hit0005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -38, -38, -38, -38, 39, 39, 39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 77}, "hit/hit0008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -38, -38, -38, -38, 39, 39, 39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 77}}, "pangxie1_14": {"pangxie1_014": {"type": "mesh", "uvs": [0.31466, 0, 0.76407, 0.04363, 0.97192, 0.19138, 0.95507, 0.33553, 0.71912, 0.40761, 0.42701, 0.67068, 0.28095, 1, 0.04501, 0.73195, 7e-05, 0.38959, 0.15174, 0.11931, 0.42701, 0.23822, 0.22477, 0.5049], "triangles": [10, 0, 1, 9, 0, 10, 2, 10, 1, 2, 4, 10, 8, 9, 10, 3, 4, 2, 11, 8, 10, 11, 10, 4, 5, 11, 4, 7, 8, 11, 6, 7, 11, 5, 6, 11], "vertices": [-9.16, -5.34, -9.39, 10.11, -2.83, 18.37, 4.8, 19.06, 9.89, 11.78, 25.28, 4.27, 43.31, 2.24, 30.61, -8.01, 12.97, -12.5, -2.01, -9.76, 2.67, 0.5, 17.74, -3.96], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18, 2, 20, 20, 22, 22, 12], "width": 34, "height": 53}}, "pangxie1_015": {"pangxie1_015": {"type": "mesh", "uvs": [0.55371, 0, 0.63511, 0.10592, 0.63369, 0.19962, 0.68828, 0.21877, 0.78319, 0.26823, 0.9498, 0.35507, 0.98243, 0.4502, 0.98975, 0.6836, 0.99515, 0.85614, 0.92156, 0.98846, 0.81291, 0.98889, 0.69318, 0.98975, 0.57526, 0.98845, 0.4014, 0.99041, 0.21667, 0.95603, 0.11359, 0.91533, 0.00878, 0.78055, 0.00795, 0.66492, 0.04099, 0.53622, 0.08968, 0.34656, 0.15697, 0.23463, 0.2651, 0.21256, 0.3272, 0.19988, 0.46314, 0.17366, 0.46333, 0.1197, 0.53953, 0, 0.55876, 0.19568, 0.64988, 0.29974, 0.68492, 0.41658, 0.69894, 0.5681, 0.70034, 0.75431, 0.69333, 0.8967, 0.81949, 0.38007, 0.86996, 0.53524, 0.86435, 0.71232, 0.84613, 0.89853, 0.41298, 0.28696, 0.44662, 0.36181, 0.46484, 0.52794, 0.47325, 0.69407, 0.46905, 0.87662, 0.26999, 0.29609, 0.24336, 0.43301, 0.21252, 0.62652, 0.20551, 0.82916], "triangles": [2, 26, 1, 23, 24, 26, 26, 0, 1, 36, 22, 23, 41, 21, 22, 41, 22, 36, 20, 21, 41, 27, 2, 3, 26, 2, 27, 25, 0, 26, 24, 25, 26, 36, 23, 26, 36, 26, 27, 37, 36, 27, 41, 36, 37, 32, 4, 5, 4, 28, 27, 4, 27, 3, 28, 4, 32, 37, 27, 28, 42, 20, 41, 42, 41, 37, 19, 20, 42, 38, 37, 28, 42, 37, 38, 33, 32, 5, 33, 5, 6, 18, 19, 42, 29, 28, 32, 29, 32, 33, 38, 28, 29, 43, 18, 42, 43, 42, 38, 17, 18, 43, 33, 6, 7, 39, 38, 29, 43, 38, 39, 34, 29, 33, 34, 33, 7, 30, 29, 34, 39, 29, 30, 16, 17, 43, 44, 16, 43, 44, 43, 39, 34, 7, 8, 40, 44, 39, 40, 39, 30, 31, 40, 30, 35, 30, 34, 35, 34, 8, 31, 30, 35, 15, 16, 44, 13, 14, 44, 15, 44, 14, 12, 40, 31, 9, 35, 8, 10, 31, 35, 10, 35, 9, 11, 12, 31, 11, 31, 10, 40, 13, 44, 13, 40, 12], "vertices": [3, 16, 78.36, 2.09, 0.7296, 36, 107.39, -11.62, 0.1824, 37, 93.02, -7.27, 0.088, 3, 16, 68.73, -6.47, 0.66, 36, 96.03, -17.7, 0.22, 37, 81.66, -13.36, 0.12, 3, 16, 60.69, -5.84, 0.66, 36, 88.36, -15.22, 0.22, 37, 74, -10.87, 0.12, 3, 16, 58.69, -11.85, 0.568, 36, 85.02, -20.59, 0.232, 37, 70.65, -16.25, 0.2, 2, 16, 53.82, -22.21, 0.912, 37, 63.5, -25.19, 0.088, 2, 16, 45.27, -40.4, 0.928, 37, 50.95, -40.89, 0.072, 2, 16, 36.89, -43.57, 0.92, 37, 42.06, -42.01, 0.08, 2, 16, 16.8, -43.21, 0.8, 37, 22.61, -36.98, 0.2, 2, 16, 1.95, -42.94, 0.8, 37, 8.23, -33.26, 0.2, 2, 16, -8.92, -34.04, 0.8, 37, -0.27, -22.08, 0.2, 3, 16, -8.24, -21.89, 0.568, 36, 17.58, -14.77, 0.232, 37, 3.22, -10.42, 0.2, 3, 16, -7.53, -8.5, 0.64, 36, 21.39, -1.91, 0.16, 37, 7.03, 2.43, 0.2, 3, 16, -6.64, 4.67, 0.6, 36, 25.33, 10.7, 0.2, 37, 10.96, 15.04, 0.2, 3, 16, -5.67, 24.12, 0.64, 36, 30.81, 29.38, 0.16, 37, 16.44, 33.73, 0.2, 3, 16, -1.5, 44.6, 0.72, 36, 39.63, 48.33, 0.08, 37, 25.26, 52.68, 0.2, 2, 16, 2.67, 55.92, 0.8, 37, 31.96, 62.71, 0.2, 2, 16, 14.93, 66.96, 0.8, 37, 46.45, 70.59, 0.2, 2, 16, 24.86, 66.47, 0.8, 37, 56, 67.8, 0.2, 2, 16, 35.7, 62.13, 0.8, 37, 65.52, 61.05, 0.2, 2, 16, 51.66, 55.72, 0.8, 37, 79.55, 51.11, 0.2, 2, 16, 60.82, 47.63, 0.8, 37, 86.58, 41.1, 0.2, 3, 16, 62.01, 35.43, 0.72, 36, 99.26, 24.62, 0.08, 37, 84.89, 28.96, 0.2, 3, 16, 62.69, 28.43, 0.6144, 36, 98.28, 17.64, 0.1536, 37, 83.92, 21.99, 0.232, 3, 16, 64.05, 13.1, 0.648, 36, 96.03, 2.42, 0.216, 37, 81.67, 6.77, 0.136, 3, 16, 68.68, 12.8, 0.648, 36, 100.47, 1.05, 0.216, 37, 86.1, 5.4, 0.136, 3, 16, 78.45, 3.68, 0.7296, 36, 107.85, -10.1, 0.1824, 37, 93.48, -5.75, 0.088, 2, 16, 61.53, 2.52, 0.8, 36, 91.12, -7.28, 0.2, 2, 16, 51.99, -7.15, 0.8, 36, 79.6, -14.46, 0.2, 2, 16, 41.73, -10.47, 0.8, 36, 68.84, -15.31, 0.2, 2, 16, 28.63, -11.28, 0.8, 36, 55.91, -13.03, 0.2, 2, 16, 12.64, -10.49, 0.8, 36, 40.54, -8.55, 0.2, 2, 16, 0.46, -8.99, 0.8, 36, 29.05, -4.25, 0.2, 2, 16, 43.98, -25.7, 0.8, 36, 67.48, -30.64, 0.2, 2, 16, 30.33, -30.56, 0.8, 36, 53.07, -32.18, 0.2, 2, 16, 15.16, -29.04, 0.8, 36, 38.68, -27.17, 0.2, 2, 16, -0.7, -26.06, 0.912, 36, 23.94, -20.58, 0.088, 2, 16, 54.65, 19.28, 0.8, 36, 88.33, 10.62, 0.2, 2, 16, 48, 15.89, 0.8, 36, 81.08, 8.88, 0.2, 2, 16, 33.62, 14.69, 0.8, 36, 66.82, 11.06, 0.2, 2, 16, 19.3, 14.59, 0.8, 36, 52.87, 14.3, 0.2, 2, 16, 3.66, 15.99, 0.8, 36, 37.98, 19.3, 0.2, 2, 16, 54.8, 35.31, 0.9, 36, 92.22, 26.17, 0.1, 2, 16, 43.23, 38.98, 0.9, 36, 81.81, 32.44, 0.1, 2, 16, 26.81, 43.4, 0.9, 36, 66.89, 40.57, 0.1, 2, 16, 9.46, 45.21, 0.9, 36, 50.43, 46.36, 0.1], "hull": 26, "edges": [0, 50, 0, 2, 2, 4, 4, 6, 10, 12, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 38, 40, 44, 46, 46, 48, 48, 50, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 64, 66, 6, 8, 8, 10, 66, 68, 68, 70, 44, 72, 72, 74, 74, 76, 76, 78, 78, 80, 82, 84, 40, 42, 42, 44, 84, 86, 86, 88, 80, 88, 82, 74, 74, 56, 56, 64, 72, 54, 12, 14, 14, 16, 34, 36, 36, 38], "width": 112, "height": 86}}, "pangxie1_019": {"pangxie1_019": {"type": "mesh", "uvs": [0, 0.49496, 0.43174, 0, 0.71653, 0, 1, 0.2925, 1, 0.77907, 0.78773, 1, 0.53739, 1, 0, 0.6909], "triangles": [6, 7, 0, 5, 6, 2, 6, 1, 2, 2, 3, 5, 6, 0, 1, 4, 5, 3], "vertices": [59.01, -10.92, 10.07, -23.25, -13.47, -12.41, -29.06, 15.39, -16.03, 43.67, 7.43, 48.43, 28.12, 38.9, 64.26, 0.47], "hull": 8, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 0, 14], "width": 91, "height": 64}}, "pangxie1_014": {"pangxie1_014": {"type": "mesh", "uvs": [0.31466, 0, 0.76407, 0.04363, 0.97192, 0.19138, 0.95507, 0.33553, 0.71912, 0.40761, 0.42701, 0.67068, 0.28095, 1, 0.04501, 0.73195, 7e-05, 0.38959, 0.15174, 0.11931, 0.42701, 0.23822, 0.22477, 0.5049], "triangles": [10, 0, 1, 9, 0, 10, 2, 10, 1, 2, 4, 10, 8, 9, 10, 3, 4, 2, 11, 8, 10, 11, 10, 4, 5, 11, 4, 7, 8, 11, 6, 7, 11, 5, 6, 11], "vertices": [-9.16, -5.34, -9.39, 10.11, -2.83, 18.37, 4.8, 19.06, 9.89, 11.78, 25.28, 4.27, 43.31, 2.24, 30.61, -8.01, 12.97, -12.5, -2.01, -9.76, 2.67, 0.5, 17.74, -3.96], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18, 2, 20, 20, 22, 22, 12], "width": 34, "height": 53}}, "pangxie1_06": {"pangxie1_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [48.66, -20.81, -7.33, -21.63, -8, 24.37, 48, 25.18], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 56, "height": 46}}, "pangxie1_018": {"pangxie1_018": {"type": "mesh", "uvs": [0.09224, 0.45092, 0.1221, 0.36824, 0.17638, 0.29704, 0.30124, 0.29015, 0.40981, 0.3246, 0.5021, 0.3958, 0.56181, 0.49226, 0.58624, 0.59791, 0.59981, 0.65533, 0.75181, 0.75179, 0.90381, 0.8115, 0.96895, 0.84365, 0.88752, 0.94012, 0.6731, 1, 0.47224, 0.98835, 0.25238, 0.90107, 0.1411, 0.80002, 0.07053, 0.61858, 0.79524, 0.8781, 0.58895, 0.85973, 0.39081, 0.74719, 0.36095, 0.6714, 0.26867, 0.49455, 0.22253, 0.35905], "triangles": [20, 21, 8, 19, 20, 8, 19, 8, 9, 18, 9, 10, 19, 9, 18, 18, 10, 11, 15, 16, 20, 12, 18, 11, 14, 20, 19, 15, 20, 14, 13, 19, 18, 14, 19, 13, 13, 18, 12, 23, 2, 3, 1, 2, 23, 4, 22, 23, 4, 23, 3, 22, 4, 5, 0, 1, 23, 22, 0, 23, 6, 21, 22, 6, 22, 5, 21, 6, 7, 21, 7, 8, 22, 17, 0, 21, 17, 22, 16, 21, 20, 21, 16, 17], "vertices": [1, 22, 3.75, -17.24, 1, 1, 22, -3.35, -13.84, 1, 1, 22, -9.13, -8.75, 1, 1, 22, -8.32, 0.85, 1, 1, 22, -3.97, 8.65, 1, 2, 22, 3.49, 14.71, 0.98815, 23, -3.13, 32.71, 0.01185, 2, 22, 12.86, 17.95, 0.90188, 23, 4.58, 26.49, 0.09812, 2, 22, 22.64, 18.38, 0.61746, 23, 10.14, 18.43, 0.38254, 2, 22, 27.97, 18.63, 0.31936, 23, 13.18, 14.05, 0.68064, 2, 22, 38.39, 28.9, 0.00374, 23, 27.42, 10.67, 0.99626, 1, 23, 40.32, 10.35, 1, 1, 23, 46.08, 9.67, 1, 1, 23, 43.83, -0.88, 1, 1, 23, 30.86, -12.46, 1, 1, 23, 16.26, -17.66, 1, 2, 22, 46.09, -11.15, 0.26654, 23, -2.43, -17.12, 0.73346, 2, 22, 35.72, -18.26, 0.78313, 23, -13.96, -12.11, 0.21687, 2, 22, 18.58, -21.17, 0.99995, 23, -25.53, 0.87, 5e-05, 1, 23, 35.07, 1.46, 1, 1, 23, 19.83, -3.34, 1, 1, 23, 1.76, -0.03, 1, 2, 22, 26.67, 0.23, 0.99462, 23, -3.1, 5.38, 0.00538, 1, 22, 9.7, -4.4, 1, 1, 22, -3.02, -6.07, 1], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 22, 36, 36, 38, 38, 40, 42, 44, 44, 46], "width": 77, "height": 91}}, "pangxie1_13": {"pangxie1_013": {"type": "mesh", "uvs": [0.35361, 0, 0.80723, 0, 1, 0.31149, 1, 0.56616, 0.74754, 0.92906, 0.3357, 0.82719, 0.02533, 0.43882, 0.01339, 0.05683, 0.32973, 0.30512, 0.72366, 0.54069], "triangles": [8, 7, 0, 6, 7, 8, 1, 8, 0, 9, 1, 2, 9, 8, 1, 9, 2, 3, 5, 8, 9, 6, 8, 5, 4, 9, 3, 5, 9, 4], "vertices": [16.67, -9.29, 3.17, -14.62, -6, -8.2, -8.81, -1.09, -5.29, 12, 8.09, 14, 21.61, 6.81, 26.17, -3.71, 14.02, -0.5, -0.3, 1.44], "hull": 8, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 0, 14, 14, 16, 16, 18], "width": 32, "height": 30}}, "hit/hit2": {"hit/hit0002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -38, -38, -38, -38, 39, 39, 39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 77}, "hit/hit0004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -38, -38, -38, -38, 39, 39, 39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 77}, "hit/hit0005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -38, -38, -38, -38, 39, 39, 39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 77}, "hit/hit0008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -38, -38, -38, -38, 39, 39, 39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 77}}, "pangxie1_15": {"pangxie1_013": {"type": "mesh", "uvs": [0.35361, 0, 0.80723, 0, 1, 0.31149, 1, 0.56616, 0.74754, 0.92906, 0.3357, 0.82719, 0.02533, 0.43882, 0.01339, 0.05683, 0.32973, 0.30512, 0.72366, 0.54069], "triangles": [8, 7, 0, 6, 7, 8, 1, 8, 0, 9, 1, 2, 9, 8, 1, 9, 2, 3, 5, 8, 9, 6, 8, 5, 4, 9, 3, 5, 9, 4], "vertices": [16.67, -9.29, 3.17, -14.62, -6, -8.2, -8.81, -1.09, -5.29, 12, 8.09, 14, 21.61, 6.81, 26.17, -3.71, 14.02, -0.5, -0.3, 1.44], "hull": 8, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 0, 14, 14, 16, 16, 18], "width": 32, "height": 30}}, "pangxie1_016": {"pangxie1_016": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [35.8, 19.95, 64.86, 6.55, 55.65, -13.43, 26.59, -0.03], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 22}}, "pangxie1_17": {"pangxie1_013": {"type": "mesh", "uvs": [0.96861, 0.24258, 0.97277, 0.63339, 0.63018, 0.99399, 0.40737, 0.83544, 0.03081, 0.43482, 0.03021, 0.03198, 0.82152, 0.0329], "triangles": [4, 5, 6, 3, 4, 6, 3, 6, 0, 3, 0, 1, 2, 3, 1], "vertices": [-1.11, 10.4, -6.55, 0.02, -1.71, -14.6, 6.81, -13.6, 23, -8.37, 28.51, 2.38, 5.94, 13.87], "hull": 7, "edges": [0, 12, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12], "width": 32, "height": 30}}, "pangxie1_18": {"pangxie1_018": {"type": "mesh", "uvs": [0.01624, 0.08345, 0.06781, 0.01684, 0.28495, 0, 0.4831, 0.06507, 0.55095, 0.17991, 0.49395, 0.24651, 0.45324, 0.33149, 0.37995, 0.3935, 0.18453, 0.43255, 0.0841, 0.42336, 0, 0.26948], "triangles": [5, 3, 4, 5, 6, 2, 5, 2, 3, 6, 10, 2, 2, 0, 1, 2, 10, 0, 7, 10, 6, 8, 9, 10, 7, 8, 10], "vertices": [-4.51, -19.12, -10.1, -14.51, -9.8, 2.27, -2.25, 16.79, 8.71, 20.84, 14.26, 15.82, 21.6, 11.86, 26.59, 5.63, 28.48, -9.71, 26.81, -17.31, 12.18, -22.22], "hull": 11, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20], "width": 77, "height": 91}}, "pangxie1_19": {"pangxie1_018": {"type": "mesh", "uvs": [0.20353, 0.28785, 0.16281, 0.35216, 0.17095, 0.46929, 0.2741, 0.57035, 0.44781, 0.63695, 0.60524, 0.65073, 0.71924, 0.6714, 0.8061, 0.71734, 0.91738, 0.82298, 0.99881, 0.74719, 1, 0.57724, 0.93367, 0.42336, 0.78981, 0.30393, 0.57267, 0.22814, 0.45053, 0.22355, 0.30667, 0.24651, 0.38538, 0.36594, 0.63238, 0.43484, 0.7681, 0.51752, 0.87938, 0.63925, 0.9201, 0.7426], "triangles": [18, 12, 11, 18, 11, 10, 19, 18, 10, 6, 5, 18, 19, 6, 18, 7, 6, 19, 9, 20, 19, 7, 19, 20, 10, 9, 19, 8, 7, 20, 8, 20, 9, 16, 15, 14, 0, 15, 16, 1, 0, 16, 17, 13, 12, 16, 14, 13, 17, 16, 13, 2, 1, 16, 18, 17, 12, 3, 2, 16, 4, 16, 17, 3, 16, 4, 5, 4, 17, 5, 17, 18], "vertices": [1, 24, -15.03, -6.85, 1, 1, 24, -14.81, -13.48, 1, 2, 24, -8.93, -22.39, 0.99971, 25, -20.19, -40.51, 0.00029, 2, 24, 2.55, -26.38, 0.98242, 25, -8.44, -37.42, 0.01758, 2, 24, 17.16, -24.93, 0.85882, 25, 2.89, -28.08, 0.14118, 2, 24, 28.28, -19.94, 0.52131, 25, 9.35, -17.74, 0.47869, 2, 24, 36.82, -17.18, 0.13345, 25, 14.9, -10.69, 0.86655, 2, 24, 44.71, -17.44, 0.00567, 25, 21.6, -6.52, 0.99433, 1, 25, 34, -3.06, 1, 1, 25, 30.57, 5.61, 1, 2, 24, 51.25, 1.07, 9e-05, 25, 16.72, 12.5, 0.99991, 2, 24, 39.81, 10.63, 0.30055, 25, 1.9, 14.07, 0.69945, 2, 24, 24.78, 14.49, 0.98584, 25, -12.74, 8.91, 0.01416, 1, 24, 6.86, 12.09, 1, 1, 24, -1.49, 7.74, 1, 1, 24, -10.03, 0.39, 1, 2, 24, 0.65, -5.99, 0.99975, 25, -21.37, -21.54, 0.00025, 2, 24, 20.26, -1.89, 0.98541, 25, -7.37, -7.22, 0.01459, 2, 24, 33.07, -3.17, 0.04829, 25, 3.98, -1.15, 0.95171, 1, 25, 17.7, 1.67, 1, 1, 25, 27.52, 0.35, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30, 2, 32, 32, 34, 34, 36, 36, 38, 38, 40], "width": 77, "height": 91}}, "tx1/dg_01": {"tx1/dg_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [113, -75, -113, -75, -113, 75, 113, 75], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 226, "height": 150}, "tx1/dg_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [113, -75, -113, -75, -113, 75, 113, 75], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 226, "height": 150}, "tx1/dg_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [113, -75, -113, -75, -113, 75, 113, 75], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 226, "height": 150}, "tx1/dg_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [113, -75, -113, -75, -113, 75, 113, 75], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 226, "height": 150}, "tx1/dg_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [113, -75, -113, -75, -113, 75, 113, 75], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 226, "height": 150}, "tx1/dg_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [113, -75, -113, -75, -113, 75, 113, 75], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 226, "height": 150}, "tx1/dg_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [113, -75, -113, -75, -113, 75, 113, 75], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 226, "height": 150}, "tx1/dg_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [113, -75, -113, -75, -113, 75, 113, 75], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 226, "height": 150}}, "tx1/bc_01": {"tx1/bc_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [68, -62, -67, -62, -67, 62, 68, 62], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 135, "height": 124}, "tx1/bc_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [68, -62, -67, -62, -67, 62, 68, 62], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 135, "height": 124}, "tx1/bc_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [68, -62, -67, -62, -67, 62, 68, 62], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 135, "height": 124}, "tx1/bc_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [68, -62, -67, -62, -67, 62, 68, 62], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 135, "height": 124}, "tx1/bc_09": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [68, -62, -67, -62, -67, 62, 68, 62], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 135, "height": 124}}, "pangxie1_34": {"pangxie1_033": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -37, -64, -37, -64, 38, 64, 38], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 128, "height": 75}}, "tx2/qiliu2_add_01": {"tx2/qiliu2_add_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [74, -57, -73, -57, -73, 57, 74, 57], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 147, "height": 114}, "tx2/qiliu2_add_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [74, -57, -73, -57, -73, 57, 74, 57], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 147, "height": 114}, "tx2/qiliu2_add_13": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [74, -57, -73, -57, -73, 57, 74, 57], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 147, "height": 114}, "tx2/qiliu2_add_19": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [74, -57, -73, -57, -73, 57, 74, 57], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 147, "height": 114}}, "pangxie1_05": {"pangxie1_05": {"type": "mesh", "uvs": [0.95036, 0.00685, 0.96979, 0.00645, 0.99254, 0.08823, 0.99264, 0.24208, 0.95949, 0.38829, 0.89283, 0.47953, 0.78805, 0.60889, 0.8868, 0.60959, 0.84358, 0.69129, 0.75238, 0.73285, 0.76105, 0.77349, 0.74675, 0.85008, 0.72493, 0.87956, 0.66459, 0.92454, 0.59354, 0.9775, 0.54147, 0.99334, 0.48345, 0.99323, 0.46704, 0.9819, 0.43533, 0.93641, 0.43632, 0.84671, 0.33963, 0.80156, 0.23393, 0.7779, 0.19269, 0.75915, 0.15837, 0.74355, 0.11938, 0.72583, 0.06101, 0.6858, 0.00698, 0.58933, 0.0074, 0.50655, 0.02303, 0.46266, 0.06406, 0.43827, 0.20086, 0.4369, 0.26708, 0.47713, 0.30092, 0.49768, 0.33348, 0.51746, 0.37805, 0.54454, 0.41986, 0.57919, 0.4679, 0.57821, 0.54264, 0.60447, 0.51274, 0.55973, 0.50543, 0.51108, 0.53423, 0.42707, 0.56206, 0.4208, 0.60881, 0.51305, 0.68936, 0.26303, 0.78185, 0.15058, 0.88933, 0.0527, 0.59843, 0.59462, 0.66899, 0.62739, 0.73678, 0.67528, 0.94153, 0.08421, 0.82394, 0.29468, 0.71049, 0.54799, 0.50021, 0.95758, 0.52926, 0.89457, 0.52788, 0.85802, 0.57491, 0.90087, 0.71188, 0.80887, 0.60812, 0.76476, 0.51681, 0.72947, 0.41305, 0.70175, 0.34941, 0.68158, 0.22075, 0.61605, 0.09624, 0.52153], "triangles": [16, 52, 15, 15, 52, 55, 15, 55, 14, 55, 52, 53, 16, 17, 52, 17, 18, 52, 14, 55, 13, 52, 18, 53, 53, 19, 54, 53, 18, 19, 12, 13, 56, 13, 55, 56, 55, 57, 56, 57, 55, 54, 55, 53, 54, 12, 56, 11, 19, 58, 54, 54, 58, 57, 11, 56, 10, 19, 59, 58, 10, 56, 9, 56, 57, 9, 57, 58, 46, 58, 37, 46, 57, 48, 9, 57, 47, 48, 57, 46, 47, 9, 48, 8, 58, 59, 37, 48, 6, 8, 8, 6, 7, 48, 47, 6, 47, 51, 6, 47, 46, 51, 6, 51, 5, 37, 38, 46, 46, 38, 42, 40, 42, 39, 46, 42, 51, 42, 40, 41, 42, 38, 39, 42, 43, 51, 51, 50, 5, 51, 43, 50, 5, 50, 4, 4, 50, 3, 3, 50, 49, 49, 2, 3, 2, 49, 1, 49, 0, 1, 43, 44, 50, 49, 44, 45, 49, 50, 44, 49, 45, 0, 20, 59, 19, 20, 60, 59, 20, 21, 60, 60, 21, 61, 61, 21, 22, 22, 23, 61, 37, 59, 36, 59, 35, 36, 59, 60, 35, 60, 61, 34, 60, 34, 35, 34, 61, 33, 23, 24, 61, 24, 25, 61, 25, 62, 61, 25, 26, 62, 61, 62, 31, 61, 32, 33, 61, 31, 32, 31, 62, 30, 26, 27, 62, 27, 28, 62, 28, 29, 62, 62, 29, 30], "vertices": [1, 29, 30.83, 119.19, 1, 1, 29, 33.28, 120.02, 1, 1, 29, 39.71, 109.51, 1, 1, 29, 46.38, 88.06, 1, 1, 29, 48.49, 66.36, 1, 1, 29, 43.98, 51.02, 1, 1, 29, 36.26, 28.85, 1, 1, 29, 48.84, 32.64, 1, 1, 29, 46.88, 19.55, 1, 1, 29, 37.1, 10.16, 1, 1, 29, 39.96, 4.83, 1, 1, 29, 41.46, -6.41, 1, 1, 29, 39.96, -11.38, 1, 1, 29, 34.24, -20.03, 1, 1, 29, 27.51, -30.22, 1, 1, 29, 21.58, -34.48, 1, 2, 28, 52.18, -35.86, 0.00119, 29, 14.21, -36.75, 0.99881, 2, 28, 49.56, -35.08, 0.0031, 29, 11.63, -35.82, 0.9969, 2, 28, 43.28, -30.34, 0.02191, 29, 5.63, -30.73, 0.97809, 2, 28, 38.79, -18.04, 0.2612, 29, 1.88, -18.18, 0.7388, 2, 28, 24.43, -16.39, 0.93218, 29, -12.36, -15.7, 0.06782, 2, 27, 39.94, -12.45, 0.04886, 28, 10.06, -18.1, 0.95114, 2, 27, 34.12, -14.35, 0.19279, 28, 3.97, -17.47, 0.80721, 2, 27, 29.27, -15.93, 0.41369, 28, -1.1, -16.94, 0.58631, 2, 27, 23.77, -17.72, 0.67965, 28, -6.86, -16.34, 0.32035, 2, 27, 14.14, -19.01, 0.94241, 28, -16.19, -13.61, 0.05759, 1, 27, -0.87, -14.04, 1, 1, 27, -9.32, -5.4, 1, 1, 27, -12.34, 0.63, 1, 1, 27, -10.96, 6.99, 1, 2, 27, 1.85, 19.92, 0.93294, 28, -11.59, 26.96, 0.06706, 2, 27, 12.25, 21.92, 0.73268, 28, -1.27, 24.56, 0.26732, 2, 27, 17.56, 22.94, 0.55082, 28, 4, 23.33, 0.44918, 2, 27, 22.67, 23.93, 0.36544, 28, 9.07, 22.15, 0.63456, 3, 27, 29.67, 25.27, 0.16726, 28, 16.01, 20.53, 0.82516, 29, -18.6, 21.66, 0.00758, 3, 27, 37.19, 25.57, 0.04136, 28, 23, 17.75, 0.86901, 29, -11.79, 18.48, 0.08963, 3, 27, 41.64, 30.16, 0.00657, 28, 28.93, 20.13, 0.71906, 29, -5.73, 20.5, 0.27437, 2, 28, 39.58, 20.04, 0.19176, 29, 4.9, 19.79, 0.80824, 2, 28, 33.56, 24.76, 0.05119, 29, -0.83, 24.85, 0.94881, 2, 28, 30.15, 31.06, 0.01901, 29, -3.87, 31.35, 0.98099, 2, 28, 29.42, 43.89, 0.00012, 29, -3.84, 44.19, 0.99988, 1, 29, -0.58, 46.17, 1, 2, 28, 43.12, 35.63, 0.01011, 29, 9.35, 35.14, 0.98989, 1, 29, 8.76, 73.18, 1, 1, 29, 15.64, 92.51, 1, 1, 29, 25.06, 110.39, 1, 2, 28, 46.02, 24, 0.05097, 29, 11.56, 23.36, 0.94903, 2, 28, 56.49, 22.82, 0.00559, 29, 21.94, 21.57, 0.99441, 1, 29, 32.63, 17.57, 1, 1, 29, 33.06, 108.06, 1, 1, 29, 27.23, 74.08, 1, 2, 28, 57.58, 35.62, 0.00046, 29, 23.78, 34.28, 0.99954, 2, 28, 52.44, -30.2, 0.00345, 29, 14.79, -31.12, 0.99655, 2, 28, 52.82, -20.23, 0.01329, 29, 15.75, -21.19, 0.98671, 2, 28, 50.77, -15.3, 0.02516, 29, 14, -16.15, 0.97484, 2, 28, 58.83, -18.95, 0.00095, 29, 21.83, -20.27, 0.99905, 1, 29, 35.24, -2.04, 1, 1, 29, 20.15, 0.02, 1, 2, 28, 42.79, 1.75, 0.00774, 29, 7.03, 1.34, 0.99226, 3, 27, 49.11, 12.19, 0, 28, 28.45, 0.68, 0.99711, 29, -7.35, 1.12, 0.00288, 2, 27, 41.01, 8.34, 0.00042, 28, 19.49, 0.46, 0.99958, 2, 27, 22.11, 3.15, 0.59127, 28, 0.11, 3.41, 0.40873, 2, 27, 0.63, 1.35, 0.99976, 28, -20.25, 10.49, 0.00024], "hull": 46, "edges": [0, 90, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 58, 60, 78, 76, 76, 74, 74, 72, 72, 70, 70, 68, 74, 92, 92, 94, 94, 96, 96, 18, 98, 100, 100, 102, 104, 106, 38, 108, 108, 110, 24, 26, 26, 28, 110, 26, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 42, 44, 60, 62, 62, 64, 62, 122, 64, 66, 66, 68, 66, 122, 44, 46, 46, 48, 122, 46, 122, 42], "width": 133, "height": 146}}, "pangxie1_013": {"pangxie1_013": {"type": "mesh", "uvs": [0.35361, 0, 0.80723, 0, 1, 0.31149, 1, 0.56616, 0.74754, 0.92906, 0.3357, 0.82719, 0.02533, 0.43882, 0.01339, 0.05683, 0.32973, 0.30512, 0.72366, 0.54069], "triangles": [8, 7, 0, 6, 7, 8, 1, 8, 0, 9, 1, 2, 9, 8, 1, 9, 2, 3, 5, 8, 9, 6, 8, 5, 4, 9, 3, 5, 9, 4], "vertices": [16.67, -9.29, 3.17, -14.62, -6, -8.2, -8.81, -1.09, -5.29, 12, 8.09, 14, 21.61, 6.81, 26.17, -3.71, 14.02, -0.5, -0.3, 1.44], "hull": 8, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 0, 14, 14, 16, 16, 18], "width": 32, "height": 30}}, "hit/hit0001": {"hit/hit0002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -38, -38, -38, -38, 39, 39, 39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 77}, "hit/hit0004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -38, -38, -38, -38, 39, 39, 39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 77}, "hit/hit0005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -38, -38, -38, -38, 39, 39, 39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 77}, "hit/hit0008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -38, -38, -38, -38, 39, 39, 39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 77}}, "pangxie1_16": {"pangxie1_014": {"type": "mesh", "uvs": [0.31466, 0, 0.76407, 0.04363, 0.97192, 0.19138, 0.95507, 0.33553, 0.71912, 0.40761, 0.42701, 0.67068, 0.28095, 1, 0.04501, 0.73195, 7e-05, 0.38959, 0.15174, 0.11931, 0.42701, 0.23822, 0.22477, 0.5049], "triangles": [10, 0, 1, 9, 0, 10, 2, 10, 1, 2, 4, 10, 8, 9, 10, 3, 4, 2, 11, 8, 10, 11, 10, 4, 5, 11, 4, 7, 8, 11, 6, 7, 11, 5, 6, 11], "vertices": [-9.16, -5.34, -9.39, 10.11, -2.83, 18.37, 4.8, 19.06, 9.89, 11.78, 25.28, 4.27, 43.31, 2.24, 30.61, -8.01, 12.97, -12.5, -2.01, -9.76, 2.67, 0.5, 17.74, -3.96], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18, 2, 20, 20, 22, 22, 12], "width": 34, "height": 53}}, "pangxie1_20": {"pangxie1_014": {"type": "mesh", "uvs": [0.2929, 0, 0.7723, 0.06011, 0.97051, 0.18802, 0.97283, 0.3052, 0.70482, 0.43905, 0.52673, 0.57102, 0.28552, 0.99705, 0.12785, 0.89388, 0.02841, 0.64167, 0.02928, 0.30368, 0.26168, 0], "triangles": [4, 0, 1, 4, 1, 2, 4, 2, 3, 0, 9, 10, 0, 4, 9, 5, 9, 4, 8, 9, 5, 7, 8, 5, 6, 7, 5], "vertices": [-8.66, 7.95, -9.23, -8.65, -4.15, -16.75, 1.89, -18.22, 10.85, -10.95, 19.03, -6.62, 42.88, -3.72, 38.76, 2.73, 26.5, 9.04, 9.04, 13.05, -8.42, 8.98], "hull": 11, "edges": [0, 20, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20], "width": 34, "height": 53}}, "hit/hit1": {"hit/hit0002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -38, -38, -38, -38, 39, 39, 39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 77}, "hit/hit0004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -38, -38, -38, -38, 39, 39, 39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 77}, "hit/hit0005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -38, -38, -38, -38, 39, 39, 39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 77}, "hit/hit0008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -38, -38, -38, -38, 39, 39, 39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 77}}, "pangxie1_22": {"pangxie1_014": {"type": "mesh", "uvs": [0.2929, 0, 0.7723, 0.06011, 0.97051, 0.18802, 0.97283, 0.3052, 0.70482, 0.43905, 0.52673, 0.57102, 0.28552, 0.99705, 0.12785, 0.89388, 0.02841, 0.64167, 0.02928, 0.30368, 0.26168, 0], "triangles": [4, 0, 1, 4, 1, 2, 4, 2, 3, 0, 9, 10, 0, 4, 9, 5, 9, 4, 8, 9, 5, 7, 8, 5, 6, 7, 5], "vertices": [-8.66, 7.95, -9.23, -8.65, -4.15, -16.75, 1.89, -18.22, 10.85, -10.95, 19.03, -6.62, 42.88, -3.72, 38.76, 2.73, 26.5, 9.04, 9.04, 13.05, -8.42, 8.98], "hull": 11, "edges": [0, 20, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20], "width": 34, "height": 53}}, "pangxie1_23": {"pangxie1_013": {"type": "mesh", "uvs": [0.96861, 0.24258, 0.97277, 0.63339, 0.63018, 0.99399, 0.40737, 0.83544, 0.03081, 0.43482, 0.03021, 0.03198, 0.82152, 0.0329], "triangles": [4, 5, 6, 3, 4, 6, 3, 6, 0, 3, 0, 1, 2, 3, 1], "vertices": [-2.7, 13.42, -10.23, 8.34, -11.14, -2.06, 0.89, -10.4, 23, -8.37, 32.49, 4.4, 6.75, 14.01], "hull": 7, "edges": [0, 12, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12], "width": 32, "height": 30}}, "pangxie1_24": {"pangxie1_014": {"type": "mesh", "uvs": [0.2929, 0, 0.7723, 0.06011, 0.97051, 0.18802, 0.97283, 0.3052, 0.70482, 0.43905, 0.52673, 0.57102, 0.28552, 0.99705, 0.12785, 0.89388, 0.02841, 0.64167, 0.02928, 0.30368, 0.26168, 0], "triangles": [4, 0, 1, 4, 1, 2, 4, 2, 3, 0, 9, 10, 0, 4, 9, 5, 9, 4, 8, 9, 5, 7, 8, 5, 6, 7, 5], "vertices": [-8.66, 7.95, -9.23, -8.65, -4.15, -16.75, 1.89, -18.22, 10.85, -10.95, 19.03, -6.62, 42.88, -3.72, 38.76, 2.73, 26.5, 9.04, 9.04, 13.05, -8.42, 8.98], "hull": 11, "edges": [0, 20, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20], "width": 34, "height": 53}}, "pangxie1_25": {"pangxie1_017": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15.29, 34.77, 52.36, -0.26, 22.14, -32.24, -14.93, 2.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 51, "height": 44}}, "pangxie1_26": {"pangxie1_018": {"type": "mesh", "uvs": [0.01624, 0.08345, 0.06781, 0.01684, 0.28495, 0, 0.4831, 0.06507, 0.55095, 0.17991, 0.49395, 0.24651, 0.45324, 0.33149, 0.37995, 0.3935, 0.18453, 0.43255, 0.0841, 0.42336, 0, 0.26948], "triangles": [5, 3, 4, 5, 6, 2, 5, 2, 3, 6, 10, 2, 2, 0, 1, 2, 10, 0, 7, 10, 6, 8, 9, 10, 7, 8, 10], "vertices": [-4.51, -19.12, -10.1, -14.51, -9.8, 2.27, -2.25, 16.79, 8.71, 20.84, 14.26, 15.82, 21.6, 11.86, 26.59, 5.63, 28.48, -9.71, 26.81, -17.31, 12.18, -22.22], "hull": 11, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20], "width": 77, "height": 91}}, "pangxie1_27": {"pangxie1_018": {"type": "mesh", "uvs": [0.09224, 0.45092, 0.1221, 0.36824, 0.17638, 0.29704, 0.30124, 0.29015, 0.40981, 0.3246, 0.5021, 0.3958, 0.56181, 0.49226, 0.58624, 0.59791, 0.59981, 0.65533, 0.75181, 0.75179, 0.90381, 0.8115, 0.96895, 0.84365, 0.88752, 0.94012, 0.6731, 1, 0.47224, 0.98835, 0.25238, 0.90107, 0.1411, 0.80002, 0.07053, 0.61858, 0.79524, 0.8781, 0.58895, 0.85973, 0.39081, 0.74719, 0.36095, 0.6714, 0.26867, 0.49455, 0.22253, 0.35905], "triangles": [20, 21, 8, 19, 20, 8, 19, 8, 9, 18, 9, 10, 19, 9, 18, 18, 10, 11, 15, 16, 20, 12, 18, 11, 14, 20, 19, 15, 20, 14, 13, 19, 18, 14, 19, 13, 13, 18, 12, 23, 2, 3, 1, 2, 23, 4, 22, 23, 4, 23, 3, 22, 4, 5, 0, 1, 23, 22, 0, 23, 6, 21, 22, 6, 22, 5, 21, 6, 7, 21, 7, 8, 22, 17, 0, 21, 17, 22, 16, 21, 20, 21, 16, 17], "vertices": [1, 42, 3.75, -17.24, 1, 1, 42, -3.35, -13.84, 1, 1, 42, -9.13, -8.75, 1, 1, 42, -8.32, 0.85, 1, 1, 42, -3.97, 8.65, 1, 2, 42, 3.49, 14.71, 0.98815, 43, -3.13, 32.71, 0.01185, 2, 42, 12.86, 17.95, 0.90188, 43, 4.58, 26.49, 0.09812, 2, 42, 22.64, 18.38, 0.61746, 43, 10.14, 18.43, 0.38254, 2, 42, 27.97, 18.63, 0.31936, 43, 13.18, 14.05, 0.68064, 2, 42, 38.39, 28.9, 0.00374, 43, 27.42, 10.67, 0.99626, 1, 43, 40.32, 10.35, 1, 1, 43, 46.08, 9.67, 1, 1, 43, 43.83, -0.88, 1, 1, 43, 30.86, -12.46, 1, 1, 43, 16.26, -17.66, 1, 2, 42, 46.09, -11.15, 0.26654, 43, -2.43, -17.12, 0.73346, 2, 42, 35.72, -18.26, 0.78313, 43, -13.96, -12.11, 0.21687, 2, 42, 18.58, -21.17, 0.99995, 43, -25.53, 0.87, 5e-05, 1, 43, 35.07, 1.46, 1, 1, 43, 19.83, -3.34, 1, 1, 43, 1.76, -0.03, 1, 2, 42, 26.67, 0.23, 0.99462, 43, -3.1, 5.38, 0.00538, 1, 42, 9.7, -4.4, 1, 1, 42, -3.02, -6.07, 1], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 22, 36, 36, 38, 38, 40, 42, 44, 44, 46], "width": 77, "height": 91}}, "pangxie1_28": {"pangxie1_018": {"type": "mesh", "uvs": [0.20353, 0.28785, 0.16281, 0.35216, 0.17095, 0.46929, 0.2741, 0.57035, 0.44781, 0.63695, 0.60524, 0.65073, 0.71924, 0.6714, 0.8061, 0.71734, 0.91738, 0.82298, 0.99881, 0.74719, 1, 0.57724, 0.93367, 0.42336, 0.78981, 0.30393, 0.57267, 0.22814, 0.45053, 0.22355, 0.30667, 0.24651, 0.38538, 0.36594, 0.63238, 0.43484, 0.7681, 0.51752, 0.87938, 0.63925, 0.9201, 0.7426], "triangles": [18, 12, 11, 18, 11, 10, 19, 18, 10, 6, 5, 18, 19, 6, 18, 7, 6, 19, 9, 20, 19, 7, 19, 20, 10, 9, 19, 8, 7, 20, 8, 20, 9, 16, 15, 14, 0, 15, 16, 1, 0, 16, 17, 13, 12, 16, 14, 13, 17, 16, 13, 2, 1, 16, 18, 17, 12, 3, 2, 16, 4, 16, 17, 3, 16, 4, 5, 4, 17, 5, 17, 18], "vertices": [1, 44, -15.03, -6.85, 1, 1, 44, -14.81, -13.48, 1, 2, 44, -8.93, -22.39, 0.99971, 45, -20.19, -40.51, 0.00029, 2, 44, 2.55, -26.38, 0.98242, 45, -8.44, -37.42, 0.01758, 2, 44, 17.16, -24.93, 0.85882, 45, 2.89, -28.08, 0.14118, 2, 44, 28.28, -19.94, 0.52131, 45, 9.35, -17.74, 0.47869, 2, 44, 36.82, -17.18, 0.13345, 45, 14.9, -10.69, 0.86655, 2, 44, 44.71, -17.44, 0.00567, 45, 21.6, -6.52, 0.99433, 1, 45, 34, -3.06, 1, 1, 45, 30.57, 5.61, 1, 2, 44, 51.25, 1.07, 9e-05, 45, 16.72, 12.5, 0.99991, 2, 44, 39.81, 10.63, 0.30055, 45, 1.9, 14.07, 0.69945, 2, 44, 24.78, 14.49, 0.98584, 45, -12.74, 8.91, 0.01416, 1, 44, 6.86, 12.09, 1, 1, 44, -1.49, 7.74, 1, 1, 44, -10.03, 0.39, 1, 2, 44, 0.65, -5.99, 0.99975, 45, -21.37, -21.54, 0.00025, 2, 44, 20.26, -1.89, 0.98541, 45, -7.37, -7.22, 0.01459, 2, 44, 33.07, -3.17, 0.04829, 45, 3.98, -1.15, 0.95171, 1, 45, 17.7, 1.67, 1, 1, 45, 27.52, 0.35, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30, 2, 32, 32, 34, 34, 36, 36, 38, 38, 40], "width": 77, "height": 91}}, "hit/hit6": {"hit/hit0002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -38, -38, -38, -38, 39, 39, 39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 77}, "hit/hit0004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -38, -38, -38, -38, 39, 39, 39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 77}, "hit/hit0005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -38, -38, -38, -38, 39, 39, 39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 77}, "hit/hit0008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -38, -38, -38, -38, 39, 39, 39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 77}}}}], "events": {"atk": {}}, "animations": {"boss_attack1": {"slots": {"tx1/bc_01": {"attachment": [{"time": 0.4333, "name": "tx1/bc_01"}, {"time": 0.4667, "name": "tx1/bc_03"}, {"time": 0.5, "name": "tx1/bc_05"}, {"time": 0.5333, "name": "tx1/bc_07"}, {"time": 0.5667, "name": "tx1/bc_09"}, {"time": 0.6, "name": null}]}, "tx1/dg_01": {"attachment": [{"time": 0.3667, "name": "tx1/dg_01"}, {"time": 0.4, "name": "tx1/dg_02"}, {"time": 0.4333, "name": "tx1/dg_03"}, {"time": 0.4667, "name": "tx1/dg_04"}, {"time": 0.5, "name": "tx1/dg_05"}, {"time": 0.5333, "name": "tx1/dg_06"}, {"time": 0.5667, "name": "tx1/dg_07"}, {"time": 0.6, "name": "tx1/dg_08"}, {"time": 0.6333, "name": null}]}}, "bones": {"pangxie1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -6.36, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 24.31, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -6.36, "curve": 0.25, "c3": 0.75}, {"time": 0.6}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -9.45, "y": -6.7, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -33.98, "y": 26.41, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 14.07, "y": -6.7, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "xiejio1": {"rotate": [{"angle": 2.51, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 7.59, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 2.89, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 7.59, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 2.51}]}, "bone4": {"rotate": [{"angle": -2.72, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -4.87, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -18.49, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -4.87, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -2.72}]}, "xiejio2": {"rotate": [{"angle": -0.07, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 12.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -9.01, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 12.06, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -0.07}]}, "bone5": {"rotate": [{"angle": -0.46, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -9.71, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -4.71, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -9.71, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -0.46}]}, "xiejio3": {"rotate": [{"angle": -0.13, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 14.09, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -13.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 14.09, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -0.13}]}, "bone6": {"rotate": [{"angle": 0.14, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -13.79, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 2.84, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -13.79, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 0.14}]}, "xiejio4": {"rotate": [{"angle": 0.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 20.01, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 21.23, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 20.01, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 0.08}]}, "bone7": {"rotate": [{"angle": -0.28, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -10.82, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 2.41, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -10.82, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -0.28}]}, "xiejio5": {"rotate": [{"angle": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 22.43, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 7.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 22.43, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 1.05}]}, "bone8": {"rotate": [{"angle": -1.13, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -11.64, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 8.37, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -11.64, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -1.13}]}, "xiejio6": {"rotate": [{"angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 23.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 16.77, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 23.1, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 0.03}]}, "bone9": {"rotate": [{"angle": 0.17, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -10.18, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -10.18, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 0.17}]}, "bone3": {"rotate": [{"angle": -2.31, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -5.5, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 10.96, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -6.28, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6, "angle": -2.31}]}, "bone12": {"rotate": [{"angle": -2.75, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 18.72, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -4.46, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -21.13, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6, "angle": -2.75}]}, "bone13": {"rotate": [{"angle": -2.75, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 18.72, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -4.46, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -21.13, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6, "angle": -2.75}]}, "bone14": {"rotate": [{"angle": 6.89, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "angle": 18.72, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 17.15, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -24.19, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.5333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6, "angle": 6.89}]}, "bone19": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 150.26, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -40.53, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 134.31, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "bone21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 49.92, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -18.48, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -16.45, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone22": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -20.41, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 66.68, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -55.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "target1": {"translate": [{"x": 2.74, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.2, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "x": -43.45, "y": 51.65, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3667, "x": -16.23, "y": 59.37, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 28.36, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.6, "x": 2.74}]}, "target2": {"translate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -45.56, "y": 59.37, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 28.36, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "target3": {"translate": [{"x": 8.05, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "x": -39.59, "y": 37.53, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "x": -16.23, "y": 59.37, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 28.36, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6, "x": 8.05}]}, "bone37": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -7.35, "y": 2.59, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 4.77, "y": -6.86, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -3.5, "y": 5.59, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "dg01": {"translate": [{"x": 119.31, "y": -744.96}]}, "dg1": {"translate": [{"x": 119.31, "y": -744.96}]}}, "events": [{"time": 0.3667, "name": "atk"}]}, "boss_attack3": {"slots": {"pangxie1_35": {"color": [{"time": 0.5, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "color": "ffffff00"}], "attachment": [{"time": 0.4333, "name": "pangxie1_033"}, {"time": 0.5333, "name": null}, {"time": 0.6667, "name": "pangxie1_033"}, {"time": 0.7667, "name": null}]}, "hit/hit0001": {"attachment": [{"time": 0.3333, "name": "hit/hit0002"}, {"time": 0.3667, "name": "hit/hit0004"}, {"time": 0.4, "name": "hit/hit0005"}, {"time": 0.4333, "name": "hit/hit0008"}, {"time": 0.4667, "name": null}]}, "hit/hit7": {"attachment": [{"time": 0.5, "name": "hit/hit0002"}, {"time": 0.5333, "name": "hit/hit0004"}, {"time": 0.5667, "name": "hit/hit0005"}, {"time": 0.6, "name": "hit/hit0008"}, {"time": 0.6333, "name": null}]}, "pangxie1_27": {"attachment": [{"time": 0.2667, "name": "pangxie1_018"}]}, "pangxie1_34": {"color": [{"time": 0.4333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.7, "color": "ffffff00"}], "attachment": [{"time": 0.3667, "name": "pangxie1_033"}, {"time": 0.4667, "name": null}, {"time": 0.6, "name": "pangxie1_033"}, {"time": 0.7, "name": null}]}, "tx2/qiliu2_add_1": {"attachment": [{"time": 0.3667, "name": "tx2/qiliu2_add_01"}, {"time": 0.4, "name": "tx2/qiliu2_add_07"}, {"time": 0.4333, "name": "tx2/qiliu2_add_13"}, {"time": 0.4667, "name": "tx2/qiliu2_add_19"}, {"time": 0.5, "name": null}, {"time": 0.5333, "name": "tx2/qiliu2_add_01"}, {"time": 0.5667, "name": "tx2/qiliu2_add_07"}, {"time": 0.6, "name": "tx2/qiliu2_add_13"}, {"time": 0.6333, "name": "tx2/qiliu2_add_19"}, {"time": 0.6667, "name": null}, {"time": 0.7, "name": "tx2/qiliu2_add_01"}, {"time": 0.7333, "name": "tx2/qiliu2_add_07"}, {"time": 0.7667, "name": "tx2/qiliu2_add_13"}, {"time": 0.8, "name": "tx2/qiliu2_add_19"}, {"time": 0.8333, "name": null}]}, "pangxie1_033": {"color": [{"time": 0.3667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.4, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.6, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "color": "ffffff00"}], "attachment": [{"time": 0.3, "name": "pangxie1_033"}, {"time": 0.4, "name": null}, {"time": 0.5333, "name": "pangxie1_033"}, {"time": 0.6333, "name": null}]}, "tx2/qiliu2_add_01": {"attachment": [{"time": 0.3, "name": "tx2/qiliu2_add_01"}, {"time": 0.3333, "name": "tx2/qiliu2_add_07"}, {"time": 0.3667, "name": "tx2/qiliu2_add_13"}, {"time": 0.4, "name": "tx2/qiliu2_add_19"}, {"time": 0.4333, "name": null}, {"time": 0.4667, "name": "tx2/qiliu2_add_01"}, {"time": 0.5, "name": "tx2/qiliu2_add_07"}, {"time": 0.5333, "name": "tx2/qiliu2_add_13"}, {"time": 0.5667, "name": "tx2/qiliu2_add_19"}, {"time": 0.6, "name": null}, {"time": 0.6333, "name": "tx2/qiliu2_add_01"}, {"time": 0.6667, "name": "tx2/qiliu2_add_07"}, {"time": 0.7, "name": "tx2/qiliu2_add_13"}, {"time": 0.7333, "name": "tx2/qiliu2_add_19"}, {"time": 0.7667, "name": null}]}, "pangxie1_33": {"color": [{"time": 0.4, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{"time": 0.3333, "name": "pangxie1_033"}, {"time": 0.4333, "name": null}, {"time": 0.5667, "name": "pangxie1_033"}, {"time": 0.6667, "name": null}]}, "hit/hit6": {"attachment": [{"time": 0.5, "name": "hit/hit0002"}, {"time": 0.5333, "name": "hit/hit0004"}, {"time": 0.5667, "name": "hit/hit0005"}, {"time": 0.6, "name": "hit/hit0008"}, {"time": 0.6333, "name": null}]}, "hit/hit3": {"attachment": [{"time": 0.4, "name": "hit/hit0002"}, {"time": 0.4333, "name": "hit/hit0004"}, {"time": 0.4667, "name": "hit/hit0005"}, {"time": 0.5, "name": "hit/hit0008"}, {"time": 0.5333, "name": null}]}, "pangxie1_25": {"attachment": [{"time": 0.3, "name": "pangxie1_017"}]}, "pangxie1_28": {"attachment": [{"time": 0.2667, "name": "pangxie1_018"}]}, "pangxie1_26": {"attachment": [{"time": 0.3, "name": "pangxie1_018"}]}, "hit/hit5": {"attachment": [{"time": 0.6333, "name": "hit/hit0002"}, {"time": 0.6667, "name": "hit/hit0004"}, {"time": 0.7, "name": "hit/hit0005"}, {"time": 0.7333, "name": "hit/hit0008"}, {"time": 0.7667, "name": null}]}, "pangxie1_05": {"attachment": [{"time": 0.2667, "name": null}]}, "hit/hit4": {"attachment": [{"time": 0.6333, "name": "hit/hit0002"}, {"time": 0.6667, "name": "hit/hit0004"}, {"time": 0.7, "name": "hit/hit0005"}, {"time": 0.7333, "name": "hit/hit0008"}, {"time": 0.7667, "name": null}]}, "hit/hit1": {"attachment": [{"time": 0.3333, "name": "hit/hit0002"}, {"time": 0.3667, "name": "hit/hit0004"}, {"time": 0.4, "name": "hit/hit0005"}, {"time": 0.4333, "name": "hit/hit0008"}, {"time": 0.4667, "name": null}]}, "hit/hit2": {"attachment": [{"time": 0.4, "name": "hit/hit0002"}, {"time": 0.4333, "name": "hit/hit0004"}, {"time": 0.4667, "name": "hit/hit0005"}, {"time": 0.5, "name": "hit/hit0008"}, {"time": 0.5333, "name": null}]}}, "bones": {"pangxie1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -8.2, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 0.3, "angle": -6.77, "curve": 0.309, "c2": 0.24, "c3": 0.757}, {"time": 0.9333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -23.04, "y": -12.05, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 10.45, "y": 22.39, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 177.63, "y": 1.25, "curve": "stepped"}, {"time": 0.8667, "x": 177.63, "y": 1.25, "curve": 0.295, "c2": 0.19, "c3": 0.755}, {"time": 0.9333}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -13.44, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 15.9, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 4.62, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 0.4, "angle": -3.04, "curve": 0.313, "c2": 0.24, "c3": 0.648, "c4": 0.58}, {"time": 0.4667, "angle": 4.13, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 0.5333, "angle": -3.04, "curve": 0.313, "c2": 0.24, "c3": 0.648, "c4": 0.58}, {"time": 0.6, "angle": 4.13, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 0.6667, "angle": -3.04, "curve": 0.313, "c2": 0.24, "c3": 0.648, "c4": 0.58}, {"time": 0.7333, "angle": 4.13, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 0.8, "angle": -3.04, "curve": 0.313, "c2": 0.24, "c3": 0.648, "c4": 0.58}, {"time": 0.8667, "angle": 4.13, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 0.9333}]}, "xiejio1": {"rotate": [{"angle": 2.51, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 13.12, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 2.51}]}, "bone4": {"rotate": [{"angle": -2.72, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 39.64, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -2.72}]}, "xiejio2": {"rotate": [{"angle": -0.07, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.65, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -0.07}]}, "bone5": {"rotate": [{"angle": -0.46, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 53.39, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -0.46}]}, "xiejio3": {"rotate": [{"angle": -0.13, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -19.31, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -0.13}]}, "bone6": {"rotate": [{"angle": 0.14, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 54.73, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 0.14}]}, "xiejio4": {"rotate": [{"angle": 0.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 10.46, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 0.08}]}, "bone7": {"rotate": [{"angle": -0.28, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 33.32, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -0.28}]}, "xiejio5": {"rotate": [{"angle": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -3.79, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 1.05}]}, "bone8": {"rotate": [{"angle": -1.13, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 56.19, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -1.13}]}, "xiejio6": {"rotate": [{"angle": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 11.53, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 0.03}]}, "bone9": {"rotate": [{"angle": 0.17, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 36.39, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 0.17}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -13.44, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 15.9, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 4.62, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 0.4333, "angle": -3.04, "curve": 0.313, "c2": 0.24, "c3": 0.648, "c4": 0.58}, {"time": 0.5, "angle": 4.13, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 0.5667, "angle": -3.04, "curve": 0.313, "c2": 0.24, "c3": 0.648, "c4": 0.58}, {"time": 0.6333, "angle": 4.13, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 0.7, "angle": -3.04, "curve": 0.313, "c2": 0.24, "c3": 0.648, "c4": 0.58}, {"time": 0.7667, "angle": 4.13, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 0.8333, "angle": -3.04, "curve": 0.313, "c2": 0.24, "c3": 0.648, "c4": 0.58}, {"time": 0.9, "angle": 4.13, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 0.9333}]}, "bone10": {"translate": [{"time": 0.2667, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.3, "x": -48.77, "y": -14.4, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.3667, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.4, "x": -48.77, "y": -14.4, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.4333, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.5, "x": -48.77, "y": -14.4, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.5333, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.6, "x": -48.77, "y": -14.4, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.6333, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.6667, "x": -48.77, "y": -14.4, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.7333, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.7667, "x": -48.77, "y": -14.4, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.8}]}, "bone12": {"rotate": [{"time": 0.1667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.2667, "angle": -13.49, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3, "angle": 34.87, "curve": 0.32, "c3": 0.653, "c4": 0.34}, {"time": 0.3667, "angle": 6.25, "curve": 0.322, "c2": 0.23, "c3": 0.655, "c4": 0.56}, {"time": 0.4, "angle": 34.87, "curve": 0.32, "c3": 0.653, "c4": 0.34}, {"time": 0.4333, "angle": 6.25, "curve": 0.322, "c2": 0.23, "c3": 0.655, "c4": 0.56}, {"time": 0.5, "angle": 34.87, "curve": 0.32, "c3": 0.653, "c4": 0.34}, {"time": 0.5333, "angle": 6.25, "curve": 0.322, "c2": 0.23, "c3": 0.655, "c4": 0.56}, {"time": 0.6, "angle": 43.19, "curve": 0.285, "c2": 0.15, "c3": 0.754}, {"time": 0.6333, "angle": 6.25, "curve": 0.322, "c2": 0.23, "c3": 0.655, "c4": 0.56}, {"time": 0.6667, "angle": 43.19, "curve": 0.285, "c2": 0.15, "c3": 0.754}, {"time": 0.7333, "angle": 6.25, "curve": 0.322, "c2": 0.23, "c3": 0.655, "c4": 0.56}, {"time": 0.7667, "angle": 43.19, "curve": 0.285, "c2": 0.15, "c3": 0.754}, {"time": 0.8, "angle": 6.25, "curve": 0.322, "c2": 0.23, "c3": 0.655, "c4": 0.56}, {"time": 0.9333}]}, "bone13": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -31.52, "curve": "stepped"}, {"time": 0.2667, "angle": -31.52, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.3, "angle": 139.82, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.3667, "angle": -1.5, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.4, "angle": 93.51, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.4333, "angle": -1.5, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.5, "angle": 93.51, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.5333, "angle": -1.5, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.6, "angle": 93.51, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.6333, "angle": -1.5, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.6667, "angle": 93.51, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.7333, "angle": -1.5, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.7667, "angle": 93.51, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.8, "angle": -1.5, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.9333}]}, "bone14": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 56.19, "curve": "stepped"}, {"time": 0.2667, "angle": 56.19, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.3, "angle": -74.32, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.3667, "angle": 46.84, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.4, "angle": -31.88, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.4333, "angle": 46.84, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.5, "angle": -31.88, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.5333, "angle": 46.84, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.6, "angle": -31.88, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.6333, "angle": 46.84, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.6667, "angle": -31.88, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.7333, "angle": 46.84, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.7667, "angle": -31.88, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.8, "angle": 46.84, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.9333}]}, "bone19": {"rotate": [{"time": 0.1667, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.3, "angle": 26.15, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.9333}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -136.44, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "bone21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 115.79, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "target1": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -8.05, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 16.7, "y": 24.83, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 157.22, "curve": "stepped"}, {"time": 0.8667, "x": 157.22, "curve": 0.283, "c2": 0.14, "c3": 0.754}, {"time": 0.9333}]}, "target2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -8.05, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 16.7, "y": 24.83, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 157.22, "curve": "stepped"}, {"time": 0.8667, "x": 157.22, "curve": 0.283, "c2": 0.14, "c3": 0.754}, {"time": 0.9333}]}, "target3": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -8.05, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 16.7, "y": 24.83, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 157.22, "curve": "stepped"}, {"time": 0.8667, "x": 157.22, "curve": 0.283, "c2": 0.14, "c3": 0.754}, {"time": 0.9333}]}, "target4": {"translate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 22.89, "y": 18.78, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 165.04, "curve": "stepped"}, {"time": 0.8667, "x": 165.04, "curve": 0.307, "c2": 0.24, "c3": 0.756}, {"time": 0.9333}]}, "target5": {"translate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 22.89, "y": 18.78, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 165.04, "curve": "stepped"}, {"time": 0.8667, "x": 165.04, "curve": 0.307, "c2": 0.24, "c3": 0.756}, {"time": 0.9333}]}, "target6": {"translate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 22.89, "y": 18.78, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 165.04, "curve": "stepped"}, {"time": 0.8667, "x": 165.04, "curve": 0.307, "c2": 0.24, "c3": 0.756}, {"time": 0.9333}]}, "bone23": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -68.97, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.3, "angle": -89.31, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.3667, "angle": 82.64, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.4, "angle": -51.26, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.4333, "angle": 82.64, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.5, "angle": -51.26, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.5333, "angle": 82.64, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.6, "angle": -51.26, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.6333, "angle": 82.64, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.6667, "angle": -51.26, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.7333, "angle": 82.64, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.7667, "angle": -51.26, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.8, "angle": 82.64, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.9333}], "translate": [{"time": 0.3, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.3667, "x": -10.26, "y": 1.97, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.4, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.4333, "x": -10.26, "y": 1.97, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.5, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.5333, "x": -10.26, "y": 1.97, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.6, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.6333, "x": -10.26, "y": 1.97, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.6667, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.7333, "x": -10.26, "y": 1.97, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.7667, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.8, "x": -10.26, "y": 1.97, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.9333}]}, "bone24": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 57.46, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.3, "angle": 70.33, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.3667, "angle": -87.8, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.4, "angle": 53.27, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.4333, "angle": -87.8, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.5, "angle": 53.27, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.5333, "angle": -87.8, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.6, "angle": 53.27, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.6333, "angle": -87.8, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.6667, "angle": 53.27, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.7333, "angle": -87.8, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.7667, "angle": 53.27, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.8, "angle": -87.8, "curve": 1, "c2": 0.07, "c3": 0.138, "c4": 0.95}, {"time": 0.9333}]}, "bone25": {"rotate": [{"time": 0.1667, "curve": 0.298, "c3": 0.635, "c4": 0.37}, {"time": 0.2667, "angle": -12.9, "curve": 0.299, "c2": 0.21, "c3": 0.756}, {"time": 0.9333}]}, "qian": {"translate": [{"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 159.84, "y": -103.23, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 159.84, "y": -103.23}]}, "qian2": {"translate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 159.84, "y": -103.23, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 159.84, "y": -103.23}]}, "qian3": {"translate": [{"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 159.84, "y": -103.23, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": 159.84, "y": -103.23}]}, "qian4": {"translate": [{"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 159.84, "y": -103.23, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 159.84, "y": -103.23}]}, "txtx3": {"translate": [{"time": 0.3, "curve": "stepped"}, {"time": 0.4667, "x": 1.96, "y": -51.11, "curve": "stepped"}, {"time": 0.6, "x": 46.15, "y": 17.9}]}, "txtx4": {"translate": [{"time": 0.3667, "curve": "stepped"}, {"time": 0.5333, "x": 1.96, "y": -51.11, "curve": "stepped"}, {"time": 0.6667, "x": 46.15, "y": 17.9}]}}, "deform": {"default": {"pangxie1_35": {"pangxie1_033": [{"time": 0.4333, "vertices": [0.60619, 1.05387, 0.62334, -1.04399, -0.60575, -1.0541, -0.62305, 1.04385], "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "vertices": [2.56481, 54.89558, 2.58196, 52.79771, 1.35287, 52.78761, 1.33557, 54.88556], "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "vertices": [0.60619, 1.05387, 0.62334, -1.04399, -0.60575, -1.0541, -0.62305, 1.04385], "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "vertices": [2.56481, 54.89558, 2.58196, 52.79771, 1.35287, 52.78761, 1.33557, 54.88556]}]}, "pangxie1_34": {"pangxie1_033": [{"time": 0.3667, "vertices": [0.60619, 1.05387, 0.62334, -1.04399, -0.60575, -1.0541, -0.62305, 1.04385], "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "vertices": [2.56481, 54.89558, 2.58196, 52.79771, 1.35287, 52.78761, 1.33557, 54.88556], "curve": 0.25, "c3": 0.75}, {"time": 0.6, "vertices": [0.60619, 1.05387, 0.62334, -1.04399, -0.60575, -1.0541, -0.62305, 1.04385], "curve": 0.25, "c3": 0.75}, {"time": 0.7, "vertices": [2.56481, 54.89558, 2.58196, 52.79771, 1.35287, 52.78761, 1.33557, 54.88556]}]}, "pangxie1_033": {"pangxie1_033": [{"time": 0.3, "vertices": [0.60619, 1.05387, 0.62334, -1.04399, -0.60575, -1.0541, -0.62305, 1.04385], "curve": 0.25, "c3": 0.75}, {"time": 0.4, "vertices": [2.56481, 54.89558, 2.58196, 52.79771, 1.35287, 52.78761, 1.33557, 54.88556], "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "vertices": [0.60619, 1.05387, 0.62334, -1.04399, -0.60575, -1.0541, -0.62305, 1.04385], "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "vertices": [2.56481, 54.89558, 2.58196, 52.79771, 1.35287, 52.78761, 1.33557, 54.88556]}]}, "pangxie1_33": {"pangxie1_033": [{"time": 0.3333, "vertices": [0.60619, 1.05387, 0.62334, -1.04399, -0.60575, -1.0541, -0.62305, 1.04385], "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "vertices": [2.56481, 54.89558, 2.58196, 52.79771, 1.35287, 52.78761, 1.33557, 54.88556], "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "vertices": [0.60619, 1.05387, 0.62334, -1.04399, -0.60575, -1.0541, -0.62305, 1.04385], "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "vertices": [2.56481, 54.89558, 2.58196, 52.79771, 1.35287, 52.78761, 1.33557, 54.88556]}]}}}, "events": [{"time": 0.3667, "name": "atk"}]}, "boss_idle": {"slots": {"eye": {"attachment": [{"time": 1, "name": "eye"}, {"time": 1.1667, "name": null}]}}, "bones": {"pangxie1": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "y": 8.98, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "xiejio1": {"rotate": [{"angle": 2.51}]}, "bone4": {"rotate": [{"angle": -2.72}]}, "xiejio2": {"rotate": [{"angle": -0.07}]}, "bone5": {"rotate": [{"angle": -0.46}]}, "xiejio3": {"rotate": [{"angle": -0.13}]}, "bone6": {"rotate": [{"angle": 0.14}]}, "xiejio4": {"rotate": [{"angle": 0.08}]}, "bone7": {"rotate": [{"angle": -0.28}]}, "xiejio5": {"rotate": [{"angle": 1.05}]}, "bone8": {"rotate": [{"angle": -1.13}]}, "xiejio6": {"rotate": [{"angle": 0.03}]}, "bone9": {"rotate": [{"angle": 0.17}]}, "bone10": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 1.82, "y": 3.81, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone11": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "y": -2.19, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone12": {"rotate": [{"angle": -1.51, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -5.31, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -1.51}]}, "bone13": {"rotate": [{"angle": -2.96, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -10.43, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -2.96}]}, "bone14": {"rotate": [{"angle": -7.47, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -10.43, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -7.47}]}, "bone16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -2.46, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone18": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 2.23, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone19": {"rotate": [{"angle": 2.61, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 12.87, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 2, "angle": 2.61}]}, "bone20": {"rotate": [{"angle": 3.65, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 12.87, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 3.65}]}, "bone21": {"rotate": [{"angle": 9.22, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 12.87, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 9.22}]}, "bone22": {"rotate": [{"angle": 12.87, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 12.87}]}, "bone37": {"translate": [{"x": -6.3, "y": 2.1, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -12.61, "y": 4.19, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": -6.3, "y": 2.1}]}}, "deform": {"default": {"eye": {"eye": [{"time": 1, "vertices": [-1.49226, -0.07745, -1.49226, -0.07745, -1.49226, -0.07745, -1.49226, -0.07745], "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "vertices": [-2.15106, -0.03875, -2.15106, -0.03875, -2.15106, -0.03875, -2.15106, -0.03875]}]}}}}, "die": {"slots": {"eye": {"attachment": [{"time": 0.0667, "name": "eye"}]}}, "bones": {"pangxie1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -6.99, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 2.22, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -17.52}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 0.32, "y": -9.77, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 1.39, "y": 1.85, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 1.39, "y": -16.89}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -29.93, "curve": 0.248, "c3": 0.734, "c4": 0.93}, {"time": 0.2667, "angle": 9.89, "curve": 0.345, "c2": 0.66, "c3": 0.678}, {"time": 0.5, "angle": 5.05}]}, "xiejio1": {"rotate": [{"angle": 2.51}]}, "bone4": {"rotate": [{"angle": -2.72}]}, "xiejio2": {"rotate": [{"angle": -0.07}]}, "bone5": {"rotate": [{"angle": -0.46}]}, "xiejio3": {"rotate": [{"angle": -0.13}]}, "bone6": {"rotate": [{"angle": 0.14}]}, "xiejio4": {"rotate": [{"angle": 0.08}]}, "bone7": {"rotate": [{"angle": -0.28}]}, "xiejio5": {"rotate": [{"angle": 1.05}]}, "bone8": {"rotate": [{"angle": -1.13}]}, "xiejio6": {"rotate": [{"angle": 0.03}]}, "bone9": {"rotate": [{"angle": 0.17}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -29.93, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 39.69, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 5.05}]}, "bone12": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 22.08}]}, "bone13": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "angle": 0.89}]}, "bone14": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "angle": 0.89, "curve": "stepped"}, {"time": 0.3, "angle": 0.89, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4, "angle": 35.69}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -9.04, "curve": 0.285, "c3": 0.626, "c4": 0.38}, {"time": 0.2667, "angle": -65.8, "curve": 0.319, "c2": 0.28, "c3": 0.757}, {"time": 0.4, "angle": -148.21}]}, "bone21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 54.55, "curve": 0.285, "c3": 0.626, "c4": 0.38}, {"time": 0.2667, "angle": 20.02, "curve": 0.319, "c2": 0.28, "c3": 0.757}, {"time": 0.4, "angle": 94.01}]}, "pangxieall": {"rotate": [{"angle": -0.02, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 0.07}], "translate": [{"curve": 0.491, "c2": 0.32, "c3": 0.821, "c4": 0.11}, {"time": 0.0333, "x": -45.3, "curve": 0.491, "c2": 0.32, "c3": 0.821, "c4": 0.11}, {"time": 0.3, "curve": "stepped"}, {"time": 0.4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "y": 10.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5333}]}, "bone18": {"rotate": [{"time": 0.6}]}}, "deform": {"default": {"eye": {"eye": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "vertices": [3.47279, 0.84724, -3.8725, 0.9975, -1.8769, 3.33289, 3.51903, 3.10751], "curve": 0.25, "c3": 0.75}, {"time": 0.2, "vertices": [4.12106, 0.67297, -4.48307, 0.76557, -2.50705, 3.52607, 4.14962, 3.32063], "curve": 0.25, "c3": 0.75}, {"time": 0.3, "vertices": [4.12106, 0.67297, -4.48307, 0.76557, -0.37341, 2.80848, 2.86255, 1.36872], "curve": 0.293, "c3": 0.631, "c4": 0.37}, {"time": 0.3667, "vertices": [-2.158, 0.29798, -2.12206, 4.18139, 1.93811, 0.4353, 1.69674, -3.32829], "curve": 0.25, "c3": 0.75}, {"time": 0.4, "vertices": [-2.158, 0.29798, -2.12206, 4.18139, 2.60817, -5.41463, -2.87016, -8.01336], "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "vertices": [-2.158, 0.29798, -2.12206, 4.18139, 1.06662, -1.88369, 1.59796, -3.06754]}]}}}}, "hurt": {"slots": {"eye": {"attachment": [{"time": 0.0667, "name": "eye"}]}}, "bones": {"bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -7.36, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 11.19, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "xiejio1": {"rotate": [{"angle": 2.51}]}, "bone4": {"rotate": [{"angle": -2.72}]}, "xiejio2": {"rotate": [{"angle": -0.07}]}, "bone5": {"rotate": [{"angle": -0.46}]}, "xiejio3": {"rotate": [{"angle": -0.13}]}, "bone6": {"rotate": [{"angle": 0.14}]}, "xiejio4": {"rotate": [{"angle": 0.08}]}, "bone7": {"rotate": [{"angle": -0.28}]}, "xiejio5": {"rotate": [{"angle": 1.05}]}, "bone8": {"rotate": [{"angle": -1.13}]}, "xiejio6": {"rotate": [{"angle": 0.03}]}, "bone9": {"rotate": [{"angle": 0.17}]}, "bone3": {"rotate": [{"angle": 5.59, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -7.36, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 11.19, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": 5.59}]}, "bone13": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -5.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 9.25, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone14": {"rotate": [{"angle": 4.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -5.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 9.25, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": 4.63}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -5.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 9.25, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone21": {"rotate": [{"angle": 4.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -5.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 9.25, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": 4.63}]}, "pangxieall": {"translate": [{"curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1, "x": 3.53, "curve": 1, "c2": 0.03, "c3": 0.75}, {"time": 0.2333, "x": -35.1, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3333}]}}}}}