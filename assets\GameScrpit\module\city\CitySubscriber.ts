import MsgEnum from "../../game/event/MsgEnum";
import { ApiHandler } from "../../game/mgr/ApiHandler";
import { CitySubCmd } from "../../game/net/cmd/CmdData";
import { CityMessage } from "../../game/net/protocol/City";
import { CommLongListMessage } from "../../game/net/protocol/Comm";
import { ByteValueList, IntValue } from "../../game/net/protocol/ExternalMessage";
import MsgMgr from "../../lib/event/MsgMgr";
import { CityModule } from "./CityModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class CitySubscriber {
  private cityHireWorkerCallback(data: ByteValueList) {
    for (let i = 0; i < data.values.length; i++) {
      let uint8 = data.values[i];
      let info = CityMessage.decode(uint8);
      log.log("使用道具后收到主动推送的城镇数据=====", info);
      CityModule.data.setCityData(info);
    }
  }

  private newTrimAdd(data: CommLongListMessage) {
    for (let i = 0; i < data.longList.length; i++) {
      CityModule.data.addDecorationIdList(data.longList[i]);
    }
  }

  private newOpenCity(data: IntValue) {
    log.log("当五族荣耀解锁新建筑推送ID", data);
    CityModule.data.cityAggregateMessage.unlockRaceShowId = data.value;
    MsgMgr.emit(MsgEnum.ON_CITY_TRIM_UPDATE, CityModule.data.cityAggregateMessage);
  }

  private activeManual(data: CommLongListMessage) {
    log.log("当五族荣耀解锁新建筑推送ID", data);
    CityModule.data.cityAggregateMessage.activeLockIdList = data.longList;
    MsgMgr.emit(MsgEnum.ON_CITY_TRIM_UPDATE, CityModule.data.cityAggregateMessage);
  }

  public register() {
    ApiHandler.instance.subscribe(ByteValueList, CitySubCmd.CityHireWorkerListMessage, this.cityHireWorkerCallback);
    ApiHandler.instance.subscribe(CommLongListMessage, CitySubCmd.getNewTrim, this.newTrimAdd);
    ApiHandler.instance.subscribe(IntValue, CitySubCmd.getNewTrimBuildId, this.newOpenCity);
    ApiHandler.instance.subscribe(CommLongListMessage, CitySubCmd.subTrimUpdate, this.activeManual);
  }
  public unRegister() {
    //取消订阅服务器消息
    ApiHandler.instance.unSubscribe(CitySubCmd.CityHireWorkerListMessage, this.cityHireWorkerCallback);
    ApiHandler.instance.unSubscribe(CitySubCmd.getNewTrim, this.newTrimAdd);
    ApiHandler.instance.unSubscribe(CitySubCmd.getNewTrimBuildId, this.newOpenCity);
    ApiHandler.instance.unSubscribe(CitySubCmd.subTrimUpdate, this.activeManual);
  }
}
