syntax = "proto3";
package sim;

// 
message HeroMessage {
  // 英雄编号
  int64 heroId = 1;
  // 英雄等级
  int32 level = 2;
  // 英雄突破几次
  int32 breakTopLevel = 3;
  // 技能列表
  map<int64,int32> skillMap = 4;
  // 道具增加的战斗属性
  map<int64,double> itemAddBattleAttrMap = 5;
}

// 
message HeroSkillLvRequest {
  // 英雄ID
  int64 heroId = 1;
  // 技能ID
  int64 skillId = 2;
}

// 
message HeroSkillUpdateMessage {
  // 变更的技能
  int64 skillId = 1;
  // 涉及的英雄
  repeated int64 heroIdList = 2;
  // 技能等级
  int32 level = 3;
}

