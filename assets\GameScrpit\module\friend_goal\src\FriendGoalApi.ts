import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ApiH<PERSON>lerFail, ApiHandlerSuccess } from "../../../game/mgr/ApiHandler";
import { FriendSubCmd } from "../../../game/net/cmd/CmdData";
import { FriendLabelMessage } from "../../../game/net/protocol/Friend";
import { FriendGoalModule } from "./FriendGoalModule";

export class FriendGoalApi {
  // 接口方法，数据模块在这里改好，然后通知上层，下面这个是个例子
  // public improveSkill(friendId: number, rank: number, consumeitem: boolean, success?: ApiHandlerSuccess) {
  //   let data: FriendCitySkillRequest = {
  //     friendId: friendId,
  //     rank: rank,
  //     consumeItem: consumeitem,
  //   };
  //   console.log("apiImproveSkill", friendId, rank, consumeitem);
  //   ApiHandler.instance.request(
  //     FriendCitySkillMessage,
  //     FriendSubCmd.improveSkill,
  //     FriendCitySkillRequest.encode(data),
  //     (data: FriendCitySkillMessage) => {
  //       console.log(data);
  //       FriendModule.data.updateFriendCitySkill(friendId, rank, data);
  //       success && success(data);
  //     },
  //     (errorCode: any, msg: string, data: any) => {
  //       console.log(`${errorCode}`);
  //       console.log(data);
  //     }
  //   );
  // }
  public getLabel(success?: (data: FriendLabelMessage) => void) {
    ApiHandler.instance.request(FriendLabelMessage, FriendSubCmd.getLabel, null, (data: FriendLabelMessage) => {
      FriendGoalModule.data.setLabel(data);
      success && success(data);
    });
  }
}
