import { _decorator, Label, Node, sp, Sprite } from "cc";

import { ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { HeroModule } from "../../../../module/hero/HeroModule";
import { times } from "../../../../lib/utils/NumbersUtils";
import { PetModule } from "../../../../module/pet/PetModule";
import ResMgr from "../../../../lib/common/ResMgr";
import { BundleEnum } from "../../../bundleEnum/BundleEnum";
import { HeroPetExpandViewHolder } from "./HeroPetExpandViewHolder";
import { UIMgr } from "../../../../lib/ui/UIMgr";
import { PetRouteItem } from "../../../../module/pet/PetRoute";
import FlashLight from "../../../common/effect_ctrl/FlashLight";
import { AudioMgr } from "../../../../../platform/src/AudioHelper";
import { PetAudioName } from "db://assets/GameScrpit/module/pet/PetConfig";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;
@ccclass("HeroPetViewHolder")
export class HeroPetViewHolder extends ViewHolder {
  @property(Node)
  private select: Node;
  @property(Node)
  private skillIcon: Node;
  @property(Node)
  private nodeAdd: Node;

  @property(Label)
  private lblAdd: Label;
  @property(Node)
  private addInfo: Node;

  private _lastSkillAdd: number = 0;
  private _data: any;
  public setSelect(select: boolean, expand: Node): boolean {
    // log.log(select);
    if (this.skillIcon.active == false) {
      let petMessage = PetModule.data.getPet(HeroModule.viewModel.currentHero.petId);
      UIMgr.instance.showDialog(PetRouteItem.UIHeroPetAwake, petMessage);
      return false;
    }

    if (!this.addInfo.active) {
      return false;
    }
    if (select) {
      this.select.active = true;
      expand.getComponent(HeroPetExpandViewHolder).updateData(this.position, true);
      // this.skillIcon.getComponent(FlashLight).startFlash(false);
      // AudioMgr.instance.playEffect(AudioName.Effect.武将升级);
    } else {
      this.select.active = false;
    }
    return true;
  }
  public init() {
    this.node.getComponentInChildren(sp.Skeleton).setCompleteListener(() => {
      if (times(this._data.skillAdd, 100) < 25) {
        this.node.getChildByName("xilian_tx").active = false;
      }
    });
    this.node.on(Node.EventType.TOUCH_END, this.onClick, this);
  }
  private onClick() {
    if (this.skillIcon.active == false) {
      AudioMgr.instance.playEffect(PetAudioName.Effect.点击觉醒图标);
    } else {
      AudioMgr.instance.playEffect(PetAudioName.Effect.点击灵兽技能图标);
    }
  }
  updateData(data, position, onlyUpdateData) {
    this.position = position;
    this._data = data;
    let petSkillList = PetModule.data.getHeroPetSkillList();
    let petSkill = petSkillList[position % petSkillList.length];
    ResMgr.setSpriteFrame(
      BundleEnum.BUNDLE_G_PET,
      `atlas_pet_skill/petskill_${petSkill.iconId}`,
      this.skillIcon.getComponent(Sprite)
    );
    if (!onlyUpdateData) {
      this.select.active = false;
      this.node.getChildByName("xilian_tx").active = false;
    }
    if (data) {
      if (times(data.skillAdd, 100) >= 25) {
        this.skillIcon.getComponent(FlashLight).startFlash(true);
        // this.node.getChildByName("xilian_tx").active = true;
        // this.node.getChildByName("xilian_tx").getComponent(sp.Skeleton).setAnimation(0, "action", true);
      } else if (data.skillAdd > this._lastSkillAdd && this.select.active) {
        this.node.getChildByName("xilian_tx").active = true;
        this.node.getChildByName("xilian_tx").getComponent(sp.Skeleton).setAnimation(0, "action", false);
        this.skillIcon.getComponent(FlashLight).stopFlash();
      } else {
        this.skillIcon.getComponent(FlashLight).stopFlash();
      }
      this._lastSkillAdd = data.skillAdd;
      this.skillIcon.active = true;
      this.nodeAdd.active = false;
      if (data.skillAdd != 0) {
        this.addInfo.active = true;
        const formattedString = `+${times(data.skillAdd, 100)}%`;
        this.lblAdd.string = formattedString;
      } else {
        //隐藏增加百分比信息
        this.addInfo.active = false;
      }
    } else {
      this.skillIcon.active = false;
      this.addInfo.active = false;
      this.nodeAdd.active = true;
    }
  }
}
