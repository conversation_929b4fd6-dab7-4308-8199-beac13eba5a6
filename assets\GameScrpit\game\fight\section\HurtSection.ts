import { _decorator, Component, Node } from "cc";
import { Section } from "../../../lib/object/Section";
import GameObject from "../../../lib/object/GameObject";
import { HurtDetail } from "../FightDefine";
import FightManager from "../manager/FightManager";
import { HurtLabelManager } from "../manager/HurtLabelManager";
import { STATE } from "./StateSection";
import MsgEnum from "../../event/MsgEnum";
import MsgMgr from "../../../lib/event/MsgMgr";
const { ccclass, property } = _decorator;

@ccclass("HurtSection")
export class HurtSection extends Section {
  public static sectionName(): string {
    return "HurtSection";
  }

  public onInit(go: GameObject, args) {
    super.onInit(go, args);
    this.ready();
    this.onMsg("OnRoleHurt", this.onRoleHurt.bind(this));
  }

  public onStart() {
    super.onStart();
  }

  private onRoleHurt(detail: HurtDetail) {
    let realHurtNum = detail.hurt;
    detail.hurtRole.getRoundDetail().a -= realHurtNum;

    //战盟boss
    if (detail.hurtRole.getDir() == 2) {
      MsgMgr.emit(MsgEnum.ON_ClUB_FIGHT_DAMAGE, realHurtNum);
    }

    detail.hurtRole.emitMsg("OnHurt");
    if (detail.hurtRole.getRoundDetail().a <= 0) {
      detail.hurtRole.emitMsg("OnSwitchState", STATE.DIE);
    }
    let path = "resources?prefab/effectLab/hurt_lab_common";
    if (detail.isCrit) {
      path = "resources?prefab/effectLab/hurt_lab_crit";
    }

    FightManager.instance.getSection(HurtLabelManager).callHurt(detail, path);
  }

  public onRemove(): void {
    this.offMsg("OnRoleHurt", this.onRoleHurt.bind(this));
  }
}
