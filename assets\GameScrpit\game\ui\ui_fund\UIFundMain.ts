import { _decorator, find, Graphics, Mask } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { BadgeMgr, BadgeType } from "../../mgr/BadgeMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
const { ccclass, property } = _decorator;

@ccclass("UIFundMain")
export class UIFundMain extends UINode {
  protected _openAct: boolean = true; //打开动作
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_HD_FUND}?prefab/ui/UIFundMain`;
  }

  protected onEvtShow(): void {
    this.getNode("view_list").getComponent(Mask).enabled = true;
    this.getNode("view_list").getComponent(Graphics).enabled = true;

    this.getNode("content_banner").children.forEach((val) => {
      let btnGo = find("btn_go", val);
      BadgeMgr.instance.setBadgeId(btnGo, BadgeType.UITerritory.btn_chongzhihaoli[val.name].id);
    });
  }

  private on_click_btn_close() {
    // 关闭窗口 effect_guanbiyinxiao
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
}
