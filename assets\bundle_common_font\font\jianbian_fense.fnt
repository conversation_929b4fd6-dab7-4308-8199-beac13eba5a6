info face="jianbian_fense" size=32 bold=0 italic=0 charset="" unicode=0 stretchH=100 smooth=1 aa=1 padding=0,0,0,0 spacing=1,1
common lineHeight=80 base=26 scaleW=128 scaleH=128 pages=1 packed=0 alphaChnl=1 redChnl=0 greenChnl=0 blueChnl=0
page id=0 file="jianbian_fense.png"
chars count=15
char id=49 x=90 y=0 width=13 height=31 xoffset=0 yoffset=3 xadvance=14 page=0 chnl=0 letter="1"
char id=50 x=0 y=68 width=24 height=31 xoffset=0 yoffset=3 xadvance=25 page=0 chnl=0 letter="2"
char id=51 x=88 y=32 width=23 height=31 xoffset=0 yoffset=3 xadvance=24 page=0 chnl=0 letter="3"
char id=52 x=50 y=67 width=24 height=31 xoffset=0 yoffset=3 xadvance=25 page=0 chnl=0 letter="4"
char id=53 x=66 y=0 width=23 height=31 xoffset=0 yoffset=3 xadvance=24 page=0 chnl=0 letter="5"
char id=54 x=25 y=68 width=24 height=31 xoffset=0 yoffset=3 xadvance=25 page=0 chnl=0 letter="6"
char id=55 x=75 y=64 width=23 height=31 xoffset=0 yoffset=3 xadvance=24 page=0 chnl=0 letter="7"
char id=56 x=41 y=0 width=24 height=31 xoffset=0 yoffset=3 xadvance=25 page=0 chnl=0 letter="8"
char id=57 x=64 y=32 width=23 height=31 xoffset=0 yoffset=3 xadvance=24 page=0 chnl=0 letter="9"
char id=48 x=39 y=35 width=24 height=31 xoffset=0 yoffset=3 xadvance=25 page=0 chnl=0 letter="0"
char id=46 x=0 y=100 width=10 height=10 xoffset=0 yoffset=24 xadvance=11 page=0 chnl=0 letter="."
char id=19975 x=0 y=0 width=40 height=34 xoffset=0 yoffset=0 xadvance=41 page=0 chnl=0 letter="万"
char id=20159 x=0 y=35 width=38 height=32 xoffset=0 yoffset=1 xadvance=39 page=0 chnl=0 letter="亿"
char id=32 x=0 y=0 width=0 height=0 xoffset=0 yoffset=0 xadvance=20 page=0 chnl=0 letter=" "
char id=9 x=0 y=0 width=0 height=0 xoffset=0 yoffset=0 xadvance=160 page=0 chnl=0 letter="	"

kernings count=0
