import { _decorator, color, Label, RichText, Sprite } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { JsonMgr } from "../../mgr/JsonMgr";
import ToolExt from "../../common/ToolExt";
import { CityModule } from "../../../module/city/CityModule";
import ResMgr from "../../../lib/common/ResMgr";
import { MainTaskModule } from "../../../module/mainTask/MainTaskModule";
import { TaskType } from "../../../module/mainTask/MainTaskData";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import GuideMgr from "../../../ext_guide/GuideMgr";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UIWorldPreviewTrim")
export class UIWorldPreviewTrim extends UINode {
  protected _openAct: boolean = true;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_MAJORCITY}?prefab/ui/UIWorldPreviewTrim`;
  }

  private _id: number = null;
  public init(args: any): void {
    super.init(args);
    this._id = args.buildShowId;
  }

  protected onEvtShow(): void {
    const cfgBuildShow = JsonMgr.instance.jsonList.c_buildShow[this._id];
    let trimId = cfgBuildShow.trimId;
    let newTrimId = this.sortTrim(trimId);
    this.loadTrimCard(newTrimId);
  }

  private loadTrimCard(trimId: number[]) {
    let c_buildTrimReward = JsonMgr.instance.jsonList.c_buildTrimReward;
    for (let i = 0; i < trimId.length; i++) {
      let info = c_buildTrimReward[trimId[i]];
      let node = ToolExt.clone(this.getNode("trim_card"), this);
      node.active = true;
      this.getNode("content").addChild(node);

      ResMgr.loadImage(
        `${BundleEnum.BUNDLE_G_MAJORCITY}?wu_zu_yu_lan/trim/${info.spineShow}`,
        node["spr_icno"].getComponent(Sprite),
        this
      );

      node["lbl_name"].getComponent(Label).string = info.name;

      node["lbl_xu_hao"].getComponent(Label).string = String(i + 1);
      node["lbl_fan_rong_du"].getComponent(Label).string = "繁荣度+" + info.prosperityShow;

      let obj = this.getYaoQiu(info.buildLv);
      node["rich_yao_qiu"].getComponent(RichText).string = obj.str;

      if (CityModule.data.cityAggregateMessage.decorationIdList.includes(info.id) == true) {
        node["bg_yilingqu_bai"].active = true;
        node["btn_lanse"].active = false;
        node["lbl_fan_rong_du"].getComponent(Label).color = color().fromHEX("#3973ae");
      } else {
        node["bg_yilingqu_bai"].active = false;
        node["btn_lanse"].active = true;
        node["lbl_fan_rong_du"].getComponent(Label).color = color().fromHEX("#00AF04");
      }
    }
  }

  private getYaoQiu(need: number) {
    let level = CityModule.data.cityTotalLevel;
    if (level >= need) {
      return {
        str: `<color=#3973ae>建筑总等级达到</color><color=#00af04>${need}/${need}</color><color=#3973ae>级时解锁</color>`,
        bool: true,
      };
    } else {
      return {
        str: `<color=#3973ae>建筑总等级达到</color><color=#ff6f6f>${level}/${need}</color><color=#3973ae>级时解锁</color>`,
        bool: false,
      };
    }
  }

  private sortTrim(trimId: number[]) {
    let c_buildTrimReward = JsonMgr.instance.jsonList.c_buildTrimReward;
    /**排序trimId */
    let arr = trimId.sort((a, b) => {
      let a1 = c_buildTrimReward[a];
      let b1 = c_buildTrimReward[b];
      if (a1.buildLv > b1.buildLv) {
        return 1;
      }
      return -1;
    });

    log.log(arr);

    return arr;
  }

  private on_click_btn_lanse() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let info = MainTaskModule.data.getConfigTask(TaskType.建筑升级_38);
    let args: any = {};
    args.buildId = CityModule.service.findMinCostLevelUpCityId();
    GuideMgr.startGuide({ stepId: info.guideType, args });
  }
}
