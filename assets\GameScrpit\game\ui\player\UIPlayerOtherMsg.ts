import { _decorator, instantiate, Label, Node, RichText, v3 } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { PlayerDetailMessage } from "../../net/protocol/Player";
import { JsonMgr } from "../../mgr/JsonMgr";
import { IConfigLeaderRecord } from "../../../module/player/PlayerConfig";
import ToolExt from "../../common/ToolExt";
import Formate from "../../../lib/utils/Formate";
import { baseAttrList } from "../../../module/player/PlayerConstant";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { UIClubAvatar } from "../club/UIClubAvatar";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "../../../lib/utils/FmUtils";
import { MessageComponent } from "../../../../platform/src/core/ui/components/MessageComponent";
import { LangMgr } from "../../mgr/LangMgr";
import { ItemCtrl } from "../../common/ItemCtrl";
import { HeroModule } from "../../../module/hero/HeroModule";
import { FriendModule } from "../../../module/friend/FriendModule";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UIPlayerOtherMsg")
export class UIPlayerOtherMsg extends UINode {
  protected _openAct: boolean = true; //打开动作
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_PLAYER}?prefab/ui/UIPlayerOtherMsg`;
  }

  protected dependOn(): BundleEnum[] {
    return [BundleEnum.BUNDLE_COMMON_UI, BundleEnum.BUNDLE_COMMON_ITEM];
  }

  private _playerId: number;

  private _playerInfo: PlayerDetailMessage = null;

  private _configLeader: IConfigLeaderRecord = null;

  /**人物跟节点 */
  private _role: Node;
  public init(param: any) {
    super.init(param);
    this._playerId = param["userId"];
  }

  protected onEvtShow() {
    PlayerModule.api.getOtherPlayerDetail(this._playerId, (data: PlayerDetailMessage) => {
      if (data.simpleMessage.userId == 0) {
        log.error("我是机器人，我没有数据");
        UIMgr.instance.back();
        return;
      }
      this._playerInfo = data;
      this._configLeader = PlayerModule.data.getConfigLeaderData(this._playerInfo.simpleMessage.level);
      this.setPower();
      this.setSoul();
      this.setHorse();
      this.loadRole();
      this.setHeader();
      this.setPlayerInfo();
      this.setClub();
      this.initAttrLay();

      let totalHero = HeroModule.data.allHeroList.length;
      let totalFriend = FriendModule.data.totalFriendNum;
      FmUtils.setItemNode(this.getNode("bg_hero"), 10311, 0, true);
      FmUtils.setItemNode(this.getNode("bg_friend"), 30101, 0, true);
      this.getNode("bg_hero")
        .getChildByName("lbl_num")
        .getComponent(Label).string = `${this._playerInfo.heroCount}/${totalHero}`;
      this.getNode("bg_friend")
        .getChildByName("lbl_num")
        .getComponent(Label).string = `${this._playerInfo.friendCount}/${totalFriend}`;

      if (this._playerInfo.powerRank < 0) {
        this.getNode("lbl_rank_1").getComponent(MessageComponent).args = [LangMgr.txMsgCode(648)];
      } else {
        this.getNode("lbl_rank_1").getComponent(MessageComponent).args = [this._playerInfo.powerRank + ""];
      }
      if (this._playerInfo.energySpeedRank < 0) {
        this.getNode("lbl_rank_2").getComponent(MessageComponent).args = [LangMgr.txMsgCode(648)];
      } else {
        this.getNode("lbl_rank_2").getComponent(MessageComponent).args = [this._playerInfo.energySpeedRank + ""];
      }
      if (this._playerInfo.competeRank < 0) {
        this.getNode("lbl_rank_3").getComponent(MessageComponent).args = [LangMgr.txMsgCode(648)];
      } else {
        this.getNode("lbl_rank_3").getComponent(MessageComponent).args = [this._playerInfo.competeRank + ""];
      }

      // log.log("查看其他用户的信息=====", data);
      // this._configLeader = PlayerModule.data.getConfigLeaderData(this._playerInfo.simpleMessage.level);

      // this.showPopup();
      // this.initAttrLay();

      // let roleSkinId = this._playerInfo.simpleMessage.avatarList[0];
      // let horseId = this._playerInfo.horseMessage.horseId;c
      // ToolExt.loadUIRole(this.getNode("role_point"), roleSkinId, horseId, "renderScale14", this);
    });
  }

  private setPower() {
    this.getNode("power_lab").getComponent(Label).string = `${Formate.format(this._playerInfo.simpleMessage.power)}`;
  }

  private loadRole() {
    let roleSkinId = this._playerInfo.simpleMessage.avatarList[0];
    let horseId = this._playerInfo.horseMessage.horseId;
    ToolExt.loadUIRole(this.getNode("role_point"), roleSkinId, horseId, "renderScale14", this);
  }

  private setSoul() {
    let soulId = this._playerInfo.soulMessage.id;
    if (soulId == -1) {
      this.getNode("bg_soul").active = false;
    } else {
      let itemConfig = JsonMgr.instance.getConfigItem(this._playerInfo.soulMessage.soulTemplateId);
      FmUtils.setItemNode(this.getNode("bg_soul"), itemConfig.id, -1, true);

      let lblComp = this.getNode("bg_soul").getChildByName("lbl_num").getComponent(Label);
      lblComp.string = this._playerInfo.soulMessage.stage + "级";
      lblComp.node.active = true;
    }
  }

  private setHorse() {
    // 坐骑
    let horseId = this._playerInfo.horseMessage.horseId;
    if (horseId == -1) {
      this.getNode("bg_horse").active = false;
    } else {
      let itemConfig = JsonMgr.instance.getConfigItem(horseId);
      FmUtils.setItemNode(this.getNode("bg_horse"), itemConfig.id, -1, true);

      let lblComp = this.getNode("bg_horse").getChildByName("lbl_num").getComponent(Label);
      lblComp.string = this._playerInfo.horseMessage.grade + "级";
      lblComp.node.active = true;
    }
  }

  private setHero() {
    // let heroId = this._playerInfo.horseMessage
    // if (heroId == -1) {
    //   this.getNode("bg_hero").active = false;
    // } else {
    //   let itemConfig = JsonMgr.instance.getConfigItem(heroId);
    //   FmUtils.setItemNode(this.getNode("bg_hero"), itemConfig.id, -1, true);
    //   let lblComp = this.getNode("bg_hero").getChildByName("lbl_num").getComponent(Label);
    //   lblComp.string = this._playerInfo.heroMessage.grade + "级";
    //   lblComp.node.active = true;
    // }
  }

  private setHeader() {
    FmUtils.setHeaderNode(this.getNode("BtnHeader"), this._playerInfo.simpleMessage);
  }

  private setPlayerInfo() {
    this.getNode("player_name_lab").getComponent(Label).string = this._playerInfo.simpleMessage.nickname;
    this.getNode("player_level_lab").getComponent(RichText).string = `${this._configLeader.jingjie2}`;
    if (this._playerInfo.simpleMessage.avatarList[3] == -1) {
      this.getNode("title_point").active = false;
    } else {
      this.getNode("title_point").active = true;
      this.getNode("title_point").destroyAllChildren();
      PlayerModule.service.createTitle(
        this.getNode("title_point"),
        this._playerInfo.simpleMessage.avatarList[3],
        (titleNode: Node) => {
          titleNode.setScale(v3(0.5, 0.5, 1));
        }
      );
    }
  }

  private setClub() {
    let list = ["UIClubAvatar", "xianmeng_name_lab", "xiangmeng_num_lab", "xiangmeng_level_lab"];
    if (this._playerInfo.clubSimpleMessage.id == -1) {
      list.forEach((element) => {
        this.getNode(element).active = false;
      });
      this.getNode("xianmeng_name_lab_no").active = true;
      return;
    }

    list.forEach((element) => {
      this.getNode(element).active = true;
    });
    this.getNode("xianmeng_name_lab_no").active = false;

    let clubSimpleMessage = this._playerInfo.clubSimpleMessage;

    this.getNode("UIClubAvatar").getComponent(UIClubAvatar).setAvatar(clubSimpleMessage.avatar);

    this.getNode("xianmeng_name_lab").getComponent(Label).string = clubSimpleMessage.name;
    this.getNode("xiangmeng_level_lab").getComponent(Label).string = `${clubSimpleMessage.level} 级`;

    let str = `人数:${clubSimpleMessage.cnt}/${clubSimpleMessage.maxCnt}`;
    this.getNode("xiangmeng_num_lab").getComponent(Label).string = str;
  }

  private initAttrLay() {
    let battleAttrMap = this._playerInfo.simpleMessage.battleAttrMap;
    let base_attr_node = this.getNode("base_attr_node");
    let other_attr_lab = this.getNode("other_attr_lab");
    this.getNode("base_attr_lay").removeAllChildren();
    this.getNode("other_attr_lay").removeAllChildren();
    for (let i = 0; i < baseAttrList.length; i++) {
      let attrId = baseAttrList[i];
      let config = JsonMgr.instance.jsonList.c_attribute[baseAttrList[i]];
      let node = instantiate(base_attr_node);
      node.active = true;
      node.getChildByName("attr_name_lab").getComponent(Label).string = `${config.name}`;
      node.getChildByName("attr_num_lab").getComponent(Label).string = `${Formate.formatAttr(
        attrId,
        battleAttrMap[attrId]
      )}`;
      this.getNode("base_attr_lay").addChild(node);
    }
    let attr_list = Object.keys(battleAttrMap).map(Number);
    for (let i = 0; i < attr_list.length; i++) {
      let attrId = attr_list[i];
      if (baseAttrList.indexOf(attrId) == -1) {
        let node = instantiate(other_attr_lab);
        node.active = true;
        node.getComponent(Label).string = Formate.formatAttribute(attrId, battleAttrMap[attrId]);
        this.getNode("other_attr_lay").addChild(node);
      }
    }
  }

  private on_click_btn_close() {
    UIMgr.instance.back();
  }

  private on_click_btn_close_bg() {
    UIMgr.instance.back();
  }
}
