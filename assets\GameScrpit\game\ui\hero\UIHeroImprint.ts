import { __private, _decorator, Label } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { HeroMessage } from "../../net/protocol/Hero";
import { UIHeroSkillItem } from "./UIHeroSkillItem";
import TipMgr from "../../../lib/tips/TipMgr";
import Formate from "../../../lib/utils/Formate";
import { divide } from "../../../lib/utils/NumbersUtils";
import { HeroModule } from "../../../module/hero/HeroModule";
import { HeroRouteItem } from "../../../module/hero/HeroRoute";
import { ItemCost } from "../../common/ItemCost";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import { IConfigHero } from "../../JsonDefine";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { HeroAudioName } from "../../../module/hero/HeroConfig";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "../../../lib/utils/FmUtils";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Fri Jun 07 2024 11:59:41 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/hero/UIHeroImprint.ts
 *
 */

@ccclass("UIHeroImprint")
export class UIHeroImprint extends UINode {
  protected _openAct: boolean = true;
  hero: IConfigHero;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_HERO}?prefab/ui/UIHeroImprint`;
  }

  //=================================================
  public init(args: any): void {
    super.init(args);
    this.hero = args.heroInfo;
  }
  protected onEvtShow(): void {
    super.onEvtShow();
    this.refreshUI();
  }
  private refreshUI() {
    let level = HeroModule.data.getHeroImprintsLv(this.hero.id) ?? 0;
    let skillInfoId = this.hero.talentList[level];
    let skillInfo = HeroModule.config.getHeroSkillData(skillInfoId);
    let curSkillLvinfo = HeroModule.config.getHeroImprintLvInfo(level);
    let nextSkillLvinfo = HeroModule.config.getHeroImprintLvInfo(level + 1);
    FmUtils.setDialogTitle(this.getNode("DialogSub"), `${level}级印记`);
    this.getNode("title").getComponent(Label).string = `${level}级印记`;
    this.getNode("UIHeroSkillItem").getComponent(UIHeroSkillItem).open(skillInfo, 1, this.hero.id);
    this.getNode("original_title").getComponent(Label).string = `${level}级印记`;
    this.getNode("original_attack").getComponent(Label).string = `+${Formate.format(
      curSkillLvinfo?.powerAdd1[1][1] ?? 0
    )}`;
    this.getNode("original_blood").getComponent(Label).string = `+${Formate.format(
      curSkillLvinfo?.powerAdd1[0][1] ?? 0
    )}`;
    this.getNode("original_defense").getComponent(Label).string = `+${Formate.format(
      curSkillLvinfo?.powerAdd1[2][1] ?? 0
    )}`;
    this.getNode("original_attack_per").getComponent(Label).string = `+${divide(curSkillLvinfo?.powerAdd2, 100)}%`;
    this.getNode("original_blood_per").getComponent(Label).string = `+${divide(curSkillLvinfo?.powerAdd2, 100)}%`;
    this.getNode("original_defense_per").getComponent(Label).string = `+${divide(curSkillLvinfo?.powerAdd2, 100)}%`;

    // 保持与上方格式一致，即使没有使用 Formate.format()，也统一使用模板字符串和空值合并运算符
    this.getNode("improved_title").getComponent(Label).string = `${level + 1}级印记`;

    this.getNode("improved_attack").getComponent(Label).string = `+${Formate.format(
      nextSkillLvinfo?.powerAdd1[1][1] ?? 0
    )}`;
    this.getNode("improved_blood").getComponent(Label).string = `+${Formate.format(
      nextSkillLvinfo?.powerAdd1[0][1] ?? 0
    )}`;
    this.getNode("improved_defense").getComponent(Label).string = `+${Formate.format(
      nextSkillLvinfo?.powerAdd1[2][1] ?? 0
    )}`;
    this.getNode("improved_attack_per").getComponent(Label).string = `+${divide(nextSkillLvinfo?.powerAdd2, 100)}%`;
    this.getNode("improved_blood_per").getComponent(Label).string = `+${divide(nextSkillLvinfo?.powerAdd2, 100)}%`;
    this.getNode("improved_defense_per").getComponent(Label).string = `+${divide(nextSkillLvinfo?.powerAdd2, 100)}%`;
    this.getNode("ItemCost").getComponent(ItemCost).setItemId(nextSkillLvinfo.costList[0], nextSkillLvinfo.costList[1]);
  }
  private on_click_btn_upgrade() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    if (!HeroModule.service.canUpgradeImprint(this.hero.id)) {
      TipMgr.showTip("进阶4阶需要有15个战将达到3阶印记");
      return;
    }
    let level = HeroModule.data.getHeroImprintsLv(this.hero.id) ?? 0;
    let nextSkillLvinfo = HeroModule.config.getHeroImprintLvInfo(level + 1);
    if (this.getNode("ItemCost").getComponent(ItemCost).isEnough()) {
      HeroModule.api.skillLevelUp(
        this.hero.id,
        3100,
        (data: HeroMessage) => {
          AudioMgr.instance.playEffect(HeroAudioName.Effect.晋升成功);
          UIMgr.instance.back();
          UIMgr.instance.showDialog(HeroRouteItem.UIHeroImprintPromotion, { heroId: data.heroId });
        },
        (error: number, msg: string[], data) => {
          log.log(error, msg, data);
          TipsMgr.showErrX(error, msg);
          // TipMgr.showTip(msg);
          return true;
        }
      );
    } else {
      UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, { itemId: nextSkillLvinfo.costList[0] });
    }
  }
}
