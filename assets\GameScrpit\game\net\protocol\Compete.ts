// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v4.25.1
// source: Compete.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { PlayerSimpleMessage } from "./Player";

export const protobufPackage = "sim";

/**  */
export interface CompeteAllRankResponse {
  /** 排行榜 */
  rankList: CompeteBattlerMessage[];
}

/**  */
export interface CompeteBattleResponse {
  /** 是否胜利 */
  win: boolean;
  /** 是否触发了圣殿技能 */
  trigger: boolean;
  /** 玩家自身积分的变动情况 */
  ownPointChange: number;
  /** 玩家积分 */
  point: number;
  /** 玩家位次 */
  rank: number;
  /** 被挑战方积分的变动情况 */
  challengerPointChange: number;
  /** 被挑战人员的总积分 */
  challengerPoint: number;
  /** 获取的道具奖励 */
  resAddList: number[];
  /** 当数据不为空的话，就是新的对手,前端需要覆盖 */
  battlerList: CompeteBattlerMessage[];
}

/**  */
export interface CompeteBattlerMessage {
  /** 用户信息 */
  simpleMessage:
    | PlayerSimpleMessage
    | undefined;
  /** 积分 */
  point: number;
  /** 是否是机器人 */
  isRobot: boolean;
}

/**  */
export interface CompeteExtraMessage {
  /** 挑战券槽位大小 */
  ticketSize: number;
  /** 今日剩余的免费刷新次数 */
  freeFreshCount: number;
  /** 上一次触发修正剩余的免费刷新次数的时间  这个时间计算要满足以下规范：1.如果剩余的免费刷新次数的时间已经达到最大值，lastUpdateTime会更新为当前最新的时间; 2.如果使用免费刷新，刷新后的剩余免费剩余次数 ==  配置文件中配置的最大值 - 1，则lastUpdateTime更新为当前最新的时间；否则不变; 3.在1、2的基础上，如果剩余免费刷新次数 < 配置文件中配置的最大值 && 如果系统当前时间 - lastUpdateTime > 恢复一次免费刷新次数的时间的话，则lastUpdateTime =  lastUpdateTime + 新增加的次数 * 谈心精力值恢复一次的时间； */
  lastUpdateTime: number;
  /** 距离日榜结束还有的时间 */
  dailyRewardDeadline: number;
  /** 距离日周结束还有的时间 */
  weekRewardDeadline: number;
  /** 对手的名单 */
  battlerList: CompeteBattlerMessage[];
}

/**  */
export interface CompeteLogMessage {
  /** 本次记录的标识 */
  id: number;
  /** 用户信息 */
  simpleMessage:
    | PlayerSimpleMessage
    | undefined;
  /** 对战的时间 */
  timeStamp: number;
  /** 胜负情况 */
  win: boolean;
  /** 输掉|获得的分数 */
  losePoint: number;
  /** 是否切磋过 */
  revenge: boolean;
}

/**  */
export interface CompeteOwnRankMessage {
  point: number;
  rank: number;
  rankList: CompeteBattlerMessage[];
}

/**  */
export interface CompeteReplayMessage {
  /** 是否胜利 */
  win: boolean;
  /** 录像回放 */
  replay: string;
}

/**  */
export interface CompeteSyncRefreshCountResponse {
  /** 今日剩余的免费刷新次数 */
  freeFreshCount: number;
  /** 上一次触发修正剩余的免费刷新次数的时间 */
  lastUpdateTime: number;
}

function createBaseCompeteAllRankResponse(): CompeteAllRankResponse {
  return { rankList: [] };
}

export const CompeteAllRankResponse: MessageFns<CompeteAllRankResponse> = {
  encode(message: CompeteAllRankResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.rankList) {
      CompeteBattlerMessage.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CompeteAllRankResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCompeteAllRankResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.rankList.push(CompeteBattlerMessage.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CompeteAllRankResponse>, I>>(base?: I): CompeteAllRankResponse {
    return CompeteAllRankResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CompeteAllRankResponse>, I>>(object: I): CompeteAllRankResponse {
    const message = createBaseCompeteAllRankResponse();
    message.rankList = object.rankList?.map((e) => CompeteBattlerMessage.fromPartial(e)) || [];
    return message;
  },
};

function createBaseCompeteBattleResponse(): CompeteBattleResponse {
  return {
    win: false,
    trigger: false,
    ownPointChange: 0,
    point: 0,
    rank: 0,
    challengerPointChange: 0,
    challengerPoint: 0,
    resAddList: [],
    battlerList: [],
  };
}

export const CompeteBattleResponse: MessageFns<CompeteBattleResponse> = {
  encode(message: CompeteBattleResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.win !== false) {
      writer.uint32(8).bool(message.win);
    }
    if (message.trigger !== false) {
      writer.uint32(16).bool(message.trigger);
    }
    if (message.ownPointChange !== 0) {
      writer.uint32(24).int32(message.ownPointChange);
    }
    if (message.point !== 0) {
      writer.uint32(32).int64(message.point);
    }
    if (message.rank !== 0) {
      writer.uint32(40).int32(message.rank);
    }
    if (message.challengerPointChange !== 0) {
      writer.uint32(48).int32(message.challengerPointChange);
    }
    if (message.challengerPoint !== 0) {
      writer.uint32(56).int64(message.challengerPoint);
    }
    writer.uint32(66).fork();
    for (const v of message.resAddList) {
      writer.double(v);
    }
    writer.join();
    for (const v of message.battlerList) {
      CompeteBattlerMessage.encode(v!, writer.uint32(74).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CompeteBattleResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCompeteBattleResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.win = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.trigger = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.ownPointChange = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.point = longToNumber(reader.int64());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.rank = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.challengerPointChange = reader.int32();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.challengerPoint = longToNumber(reader.int64());
          continue;
        }
        case 8: {
          if (tag === 65) {
            message.resAddList.push(reader.double());

            continue;
          }

          if (tag === 66) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.resAddList.push(reader.double());
            }

            continue;
          }

          break;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.battlerList.push(CompeteBattlerMessage.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CompeteBattleResponse>, I>>(base?: I): CompeteBattleResponse {
    return CompeteBattleResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CompeteBattleResponse>, I>>(object: I): CompeteBattleResponse {
    const message = createBaseCompeteBattleResponse();
    message.win = object.win ?? false;
    message.trigger = object.trigger ?? false;
    message.ownPointChange = object.ownPointChange ?? 0;
    message.point = object.point ?? 0;
    message.rank = object.rank ?? 0;
    message.challengerPointChange = object.challengerPointChange ?? 0;
    message.challengerPoint = object.challengerPoint ?? 0;
    message.resAddList = object.resAddList?.map((e) => e) || [];
    message.battlerList = object.battlerList?.map((e) => CompeteBattlerMessage.fromPartial(e)) || [];
    return message;
  },
};

function createBaseCompeteBattlerMessage(): CompeteBattlerMessage {
  return { simpleMessage: undefined, point: 0, isRobot: false };
}

export const CompeteBattlerMessage: MessageFns<CompeteBattlerMessage> = {
  encode(message: CompeteBattlerMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.simpleMessage !== undefined) {
      PlayerSimpleMessage.encode(message.simpleMessage, writer.uint32(10).fork()).join();
    }
    if (message.point !== 0) {
      writer.uint32(17).double(message.point);
    }
    if (message.isRobot !== false) {
      writer.uint32(24).bool(message.isRobot);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CompeteBattlerMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCompeteBattlerMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.simpleMessage = PlayerSimpleMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.point = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.isRobot = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CompeteBattlerMessage>, I>>(base?: I): CompeteBattlerMessage {
    return CompeteBattlerMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CompeteBattlerMessage>, I>>(object: I): CompeteBattlerMessage {
    const message = createBaseCompeteBattlerMessage();
    message.simpleMessage = (object.simpleMessage !== undefined && object.simpleMessage !== null)
      ? PlayerSimpleMessage.fromPartial(object.simpleMessage)
      : undefined;
    message.point = object.point ?? 0;
    message.isRobot = object.isRobot ?? false;
    return message;
  },
};

function createBaseCompeteExtraMessage(): CompeteExtraMessage {
  return {
    ticketSize: 0,
    freeFreshCount: 0,
    lastUpdateTime: 0,
    dailyRewardDeadline: 0,
    weekRewardDeadline: 0,
    battlerList: [],
  };
}

export const CompeteExtraMessage: MessageFns<CompeteExtraMessage> = {
  encode(message: CompeteExtraMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.ticketSize !== 0) {
      writer.uint32(8).int32(message.ticketSize);
    }
    if (message.freeFreshCount !== 0) {
      writer.uint32(16).int32(message.freeFreshCount);
    }
    if (message.lastUpdateTime !== 0) {
      writer.uint32(24).int64(message.lastUpdateTime);
    }
    if (message.dailyRewardDeadline !== 0) {
      writer.uint32(32).int64(message.dailyRewardDeadline);
    }
    if (message.weekRewardDeadline !== 0) {
      writer.uint32(40).int64(message.weekRewardDeadline);
    }
    for (const v of message.battlerList) {
      CompeteBattlerMessage.encode(v!, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CompeteExtraMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCompeteExtraMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.ticketSize = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.freeFreshCount = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.lastUpdateTime = longToNumber(reader.int64());
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.dailyRewardDeadline = longToNumber(reader.int64());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.weekRewardDeadline = longToNumber(reader.int64());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.battlerList.push(CompeteBattlerMessage.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CompeteExtraMessage>, I>>(base?: I): CompeteExtraMessage {
    return CompeteExtraMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CompeteExtraMessage>, I>>(object: I): CompeteExtraMessage {
    const message = createBaseCompeteExtraMessage();
    message.ticketSize = object.ticketSize ?? 0;
    message.freeFreshCount = object.freeFreshCount ?? 0;
    message.lastUpdateTime = object.lastUpdateTime ?? 0;
    message.dailyRewardDeadline = object.dailyRewardDeadline ?? 0;
    message.weekRewardDeadline = object.weekRewardDeadline ?? 0;
    message.battlerList = object.battlerList?.map((e) => CompeteBattlerMessage.fromPartial(e)) || [];
    return message;
  },
};

function createBaseCompeteLogMessage(): CompeteLogMessage {
  return { id: 0, simpleMessage: undefined, timeStamp: 0, win: false, losePoint: 0, revenge: false };
}

export const CompeteLogMessage: MessageFns<CompeteLogMessage> = {
  encode(message: CompeteLogMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).int64(message.id);
    }
    if (message.simpleMessage !== undefined) {
      PlayerSimpleMessage.encode(message.simpleMessage, writer.uint32(18).fork()).join();
    }
    if (message.timeStamp !== 0) {
      writer.uint32(24).int64(message.timeStamp);
    }
    if (message.win !== false) {
      writer.uint32(32).bool(message.win);
    }
    if (message.losePoint !== 0) {
      writer.uint32(40).int32(message.losePoint);
    }
    if (message.revenge !== false) {
      writer.uint32(48).bool(message.revenge);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CompeteLogMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCompeteLogMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.id = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.simpleMessage = PlayerSimpleMessage.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.timeStamp = longToNumber(reader.int64());
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.win = reader.bool();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.losePoint = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.revenge = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CompeteLogMessage>, I>>(base?: I): CompeteLogMessage {
    return CompeteLogMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CompeteLogMessage>, I>>(object: I): CompeteLogMessage {
    const message = createBaseCompeteLogMessage();
    message.id = object.id ?? 0;
    message.simpleMessage = (object.simpleMessage !== undefined && object.simpleMessage !== null)
      ? PlayerSimpleMessage.fromPartial(object.simpleMessage)
      : undefined;
    message.timeStamp = object.timeStamp ?? 0;
    message.win = object.win ?? false;
    message.losePoint = object.losePoint ?? 0;
    message.revenge = object.revenge ?? false;
    return message;
  },
};

function createBaseCompeteOwnRankMessage(): CompeteOwnRankMessage {
  return { point: 0, rank: 0, rankList: [] };
}

export const CompeteOwnRankMessage: MessageFns<CompeteOwnRankMessage> = {
  encode(message: CompeteOwnRankMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.point !== 0) {
      writer.uint32(8).int64(message.point);
    }
    if (message.rank !== 0) {
      writer.uint32(16).int32(message.rank);
    }
    for (const v of message.rankList) {
      CompeteBattlerMessage.encode(v!, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CompeteOwnRankMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCompeteOwnRankMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.point = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.rank = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.rankList.push(CompeteBattlerMessage.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CompeteOwnRankMessage>, I>>(base?: I): CompeteOwnRankMessage {
    return CompeteOwnRankMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CompeteOwnRankMessage>, I>>(object: I): CompeteOwnRankMessage {
    const message = createBaseCompeteOwnRankMessage();
    message.point = object.point ?? 0;
    message.rank = object.rank ?? 0;
    message.rankList = object.rankList?.map((e) => CompeteBattlerMessage.fromPartial(e)) || [];
    return message;
  },
};

function createBaseCompeteReplayMessage(): CompeteReplayMessage {
  return { win: false, replay: "" };
}

export const CompeteReplayMessage: MessageFns<CompeteReplayMessage> = {
  encode(message: CompeteReplayMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.win !== false) {
      writer.uint32(8).bool(message.win);
    }
    if (message.replay !== "") {
      writer.uint32(18).string(message.replay);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CompeteReplayMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCompeteReplayMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.win = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.replay = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CompeteReplayMessage>, I>>(base?: I): CompeteReplayMessage {
    return CompeteReplayMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CompeteReplayMessage>, I>>(object: I): CompeteReplayMessage {
    const message = createBaseCompeteReplayMessage();
    message.win = object.win ?? false;
    message.replay = object.replay ?? "";
    return message;
  },
};

function createBaseCompeteSyncRefreshCountResponse(): CompeteSyncRefreshCountResponse {
  return { freeFreshCount: 0, lastUpdateTime: 0 };
}

export const CompeteSyncRefreshCountResponse: MessageFns<CompeteSyncRefreshCountResponse> = {
  encode(message: CompeteSyncRefreshCountResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.freeFreshCount !== 0) {
      writer.uint32(8).int32(message.freeFreshCount);
    }
    if (message.lastUpdateTime !== 0) {
      writer.uint32(16).int64(message.lastUpdateTime);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CompeteSyncRefreshCountResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCompeteSyncRefreshCountResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.freeFreshCount = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.lastUpdateTime = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CompeteSyncRefreshCountResponse>, I>>(base?: I): CompeteSyncRefreshCountResponse {
    return CompeteSyncRefreshCountResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CompeteSyncRefreshCountResponse>, I>>(
    object: I,
  ): CompeteSyncRefreshCountResponse {
    const message = createBaseCompeteSyncRefreshCountResponse();
    message.freeFreshCount = object.freeFreshCount ?? 0;
    message.lastUpdateTime = object.lastUpdateTime ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
