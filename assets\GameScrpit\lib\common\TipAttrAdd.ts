import { _decorator, Component, Label, Node } from "cc";
import { JsonMgr } from "../../game/mgr/JsonMgr";
import Formate from "../utils/Formate";
import { AttrEnum } from "../../game/GameDefine";
const { ccclass, property } = _decorator;

@ccclass("TipAttrAdd")
export class TipAttrAdd extends Component {
  @property(Node)
  private layoutBase: Node;

  @property(Node)
  private layoutAdvance: Node;

  @property(Node)
  private layoutAnt: Node;

  private formatAttr(nodeParent: Node, attrList: number[], attrMap: { [key: number]: number }, need10000: boolean) {
    for (let i = 0; i < attrList.length; i++) {
      let node = nodeParent.children[i];
      node.active = true;

      let configAttr = JsonMgr.instance.getConfigAttribute(attrList[i]);
      let num = attrMap[attrList[i]];

      if (need10000 == true && configAttr.type1 == 2) {
        num = num / 10000;
      }

      node.getChildByName("lbl_key").getComponent(Label).string = configAttr.name;
      node.getChildByName("lbl_value").getComponent(Label).string = "+" + Formate.formatAttr(attrList[i], num);
    }
  }

  start() {}

  private onClose() {
    this.node.destroy();
  }

  public setAttr(attrMap: { [key: number]: number }, need10000: boolean = false) {
    // 基础属性
    let baseAttr = [
      AttrEnum.攻击_2,
      AttrEnum.生命_1,
      AttrEnum.防御_3,
      AttrEnum.敏捷_4,
      AttrEnum.攻击百分比_16,
      AttrEnum.生命百分比_15,
      AttrEnum.防御百分比_17,
      AttrEnum.敏捷百分比_18,
    ];
    this.formatAttr(this.layoutBase, baseAttr, attrMap, need10000);

    let advanceAttrList = [
      AttrEnum.击晕_21,
      AttrEnum.闪避_22,
      AttrEnum.连击_23,
      AttrEnum.反击_24,
      AttrEnum.暴击_25,
      AttrEnum.吸血_26,
    ];
    this.formatAttr(this.layoutAdvance, advanceAttrList, attrMap, need10000);

    let antAttrList = [
      AttrEnum.抗击晕_31,
      AttrEnum.抗闪避_32,
      AttrEnum.抗连击_33,
      AttrEnum.抗反击_34,
      AttrEnum.抗暴击_35,
      AttrEnum.抗吸血_36,
    ];
    this.formatAttr(this.layoutAnt, antAttrList, attrMap, need10000);
  }
}
