import { _decorator, find, instantiate, isValid, Label, ProgressBar, sp, Sprite, Tween, tween, UITransform } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { JsonMgr } from "../../mgr/JsonMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
import Formate from "../../../lib/utils/Formate";
import ToolExt from "../../common/ToolExt";
import { HuntModule } from "../../../module/hunt/HuntModule";
import { PlayerBaseMessage } from "../../net/protocol/Player";
import { HuntRouteName } from "../../../module/hunt/HuntRoute";
import TipMgr from "../../../lib/tips/TipMgr";
import { TimeUtils } from "../../../lib/utils/TimeUtils";
import { tweenTagEnum } from "../../GameDefine";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { HuntAudioName } from "../../../module/hunt/HuntConfig";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "../../../lib/utils/FmUtils";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UIHuntPreparePrimitive")
export class UIHuntPreparePrimitive extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_HUNT}?prefab/ui/UIHuntPreparePrimitive`;
  }
  protected _openAct: boolean = true; //打开动作
  private _buddyList: any[] = [];

  private openState: boolean = false;

  protected onEvtShow(): void {
    let bossdata = this.getBasicsBossData();
    this.getNode("lbl_count").getComponent(Label).string =
      "今日剩余可挑战次数：" + HuntModule.data.bossChance + "/" + bossdata.max;
    this.setBossHuntState();
    let time = TimeUtils.serverTime;
    const date = new Date(time);
    const hour = date.getHours();

    if (hour >= bossdata.over2 && !HuntModule.data.killMessage) {
      this.getNode("commonFightMain").active = false;
      TipMgr.showTip("首领已经逃走");
    } else if (HuntModule.data.killMessage) {
      this.getNode("commonFightMain").active = false;
      this.getNode("dieMain").active = true;
      let obj = {
        userId: HuntModule.data.killMessage.simpleMessage.userId,
        /** 角色名称 */
        nickname: HuntModule.data.killMessage.simpleMessage.nickname,
        /** 头像 */
        avatarList: HuntModule.data.killMessage.simpleMessage.avatarList,
        /** 性别 */
        sex: HuntModule.data.killMessage.simpleMessage.sex,
        /** 当前等级 */
        level: HuntModule.data.killMessage.simpleMessage.level,
        /** 会员等级 */
        vipLevel: HuntModule.data.killMessage.simpleMessage.vipLevel,
        /** 是否是真实玩家,false表示是真实玩家，true表示非真实玩家 */
        unreal: false,
      };
      FmUtils.setHeaderNode(this.getNode("BtnHeader"), obj);
      this.getNode("lab_player_name").getComponent(Label).string = HuntModule.data.killMessage.simpleMessage.nickname;
      this.getNode("dieTime").getComponent(Label).string = TimeUtils.getTimeAgo(HuntModule.data.killMessage.timeStamp);
    } else {
      this.getNode("dieMain").active = false;
      this.setBossHp();
      this.setBossShow();
      this.setBossName();
      this.setBossPower();
      this.setBuddyList();
      this.getPrimitiveIsOpen();
    }
  }

  /**获取洪荒开启状态 */
  private getPrimitiveIsOpen() {
    let obj = {
      time: 0,
      state: false,
    };

    // 如果为０表示未开启
    if (HuntModule.data.bossEndStamp == 0) {
      return obj;
    }

    if (HuntModule.data.bossStartStamp > TimeUtils.serverTime) {
      obj.state = false;
    } else {
      obj.state = true;
    }
    obj.time = HuntModule.data.bossEndStamp - TimeUtils.serverTime;

    if (obj.state == false) {
      this.getNode("buddyLayer").active = false;
    } else {
      this.getNode("buddyLayer").active = true;
    }
  }

  private getBasicsBossData() {
    let c_huntBoss = JsonMgr.instance.jsonList.c_huntBoss;
    let list = Object.keys(c_huntBoss);
    return c_huntBoss[list[0]];
  }

  private setBossHp() {
    this.getNode("bossPro").getComponent(ProgressBar).progress =
      HuntModule.data.message.bossRemainHp / HuntModule.data.message.bossHp;
  }

  private setBossShow() {
    let bossdata = this.getBasicsBossData();
    let db1 = JsonMgr.instance.jsonList.c_monsterShow[bossdata.monsterId];
    let db2 = JsonMgr.instance.jsonList.c_spineShow[db1.spineId];

    ToolExt.loadUIRole(this.getNode("skt_boss"), bossdata.monsterId, -1, "renderScale3", this);

    // ResMgr.loadPrefab(
    //   db2.prefabPath,
    //   (prefab) => {
    //     let node = instantiate(prefab);
    //     this.getNode("skt_boss").addChild(node);
    //     node.name = "image";
    //     node.setScale(v3(db1.renderScale3[0], db1.renderScale3[1], db1.renderScale3[2]));
    //     //node.getChildByName("render").getComponent(sp.Skeleton).setAnimation(0, "boss_idle", true);
    //   },
    //   this
    // );
  }

  private setBossName() {
    let bossdata = this.getBasicsBossData();
    let db1 = JsonMgr.instance.jsonList.c_monsterShow[bossdata.monsterId];
    this.getNode("lab_boss_name").getComponent(Label).string = db1.name;
  }

  private setBossPower() {
    this.getNode("lab_boss_power").getComponent(Label).string = Formate.format(HuntModule.data.bossPower);
  }

  private setBuddyList() {
    let buddyList = HuntModule.data.buddyList;
    this.getNode("buddyLayer").children.forEach((val) => {
      val.active = false;
      let obj = {
        index: 2,
        time: 0,
        node: val,
      };
      this._buddyList.push(obj);
    });

    for (let i = 0; i < buddyList.length && i < this.getNode("buddyLayer").children.length; i++) {
      let node = this.getNode("buddyLayer").children[i];
      node.active = true;
      let info: PlayerBaseMessage = buddyList[i];

      let btn_header = find("BtnHeader", node);
      FmUtils.setHeaderNode(btn_header, info);

      let buddyName = node.getChildByName("buddyName");
      buddyName.getComponent(Label).string = info.nickname;
    }
  }

  private on_click_btn_bossHunt() {
    AudioMgr.instance.playEffect(HuntAudioName.Effect.点击洪荒出现战斗按钮);

    if (this.openState == false) {
      TipsMgr.showTipX(182, [], "狩猎BOSS战场还未开启");
      UIMgr.instance.back();
      return;
    }

    if (HuntModule.data.bossChance <= 0) {
      TipsMgr.showTipX(183, [], "狩猎BOSS次数已经用完");
      return;
    }
    TipsMgr.setEnableTouch(false, 3);
    HuntModule.api.bossHunt((res) => {
      //log.error("洪荒boss战斗的数据====", res);
      let data = JSON.parse(res.replay);
      let args = {
        win: res.win,
        damageHp: res.damageHp,
        fightData: data,
        resAddList: res.resAddList,
        buddyList: HuntModule.data.buddyList,
      };
      UIMgr.instance.replaceDialog(HuntRouteName.UIHuntFight, { data: args }, null, () => {
        TipsMgr.setEnableTouch(true);
      });
      this.setBossHuntState();
    });
  }

  private setBossHuntState() {
    if (HuntModule.data.bossChance <= 0) {
      this.getNode("btn_bossHunt").getComponent(Sprite).grayscale = true;
    } else {
      this.getNode("btn_bossHunt").getComponent(Sprite).grayscale = false;
    }
  }

  private on_click_btn_close() {
    // 关闭窗口 effect_guanbiyinxiao
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  public tick(dt: any): void {
    this.PrimitiveIsOpen();
    for (let i = 0; i < this._buddyList.length; i++) {
      let obj = this._buddyList[i];
      if (obj.node.activeInHierarchy == false) continue;
      obj.time -= dt;
      if (obj.time <= 0) {
        this.move(obj);
      }
    }
  }

  private move(obj) {
    let callback = () => {
      let endPos = ToolExt.getRandomPoint(
        this.getNode("buddyLayer").getComponent(UITransform).getBoundingBox(),
        obj.node.getComponent(UITransform).contentSize
      );
      let pointA = obj.node.getPosition();
      let distance = this.calculateDistance(pointA, endPos);
      let time = distance / 50;
      obj.time = time;
      //obj.time += 0.5;

      tween(obj.node)
        .tag(tweenTagEnum.UIHuntPreparePrimitive_Tag)
        .to(time, { position: endPos })
        .call(() => {
          obj.index -= 1;
          if (obj.index <= 0) {
            this.loadBullet(obj.node);
            obj.index = 2;
          }
        })
        .delay(0.5)
        .start();
    };
    callback();
  }

  private loadBullet(node) {
    if (
      !this.getNode("skt_boss").getChildByName("role_point") ||
      this.getNode("buddyLayer").activeInHierarchy == false
    ) {
      return;
    }

    let bullet = instantiate(this.getNode("jiguang"));
    this.getNode("bulletLayer").addChild(bullet);
    let bulletPos = ToolExt.transferOfAxes(node, this.getNode("bulletLayer"));
    bullet.setPosition(bulletPos);
    bullet.active = true;
    log.log("发射子弹skt_boss");

    let pos = ToolExt.transferOfAxes(
      this.getNode("skt_boss").getChildByName("role_point"),
      this.getNode("bulletLayer")
    );

    let height =
      this.getNode("skt_boss").getChildByPath("role_point/renderPoint/render").getComponent(UITransform).height / 2;

    height *= this.getNode("skt_boss").getChildByPath("role_point/renderPoint").getScale().y;

    pos.y += height;

    bullet.angle = this.calculateAngle(bulletPos, pos);

    let distance = this.calculateDistance(pos, bulletPos);
    let time = distance / 150;
    if (time < 0) {
      time = 0.1;
    }
    tween(bullet)
      .tag(tweenTagEnum.UIHuntPreparePrimitive_Tag)
      .to(time, { position: pos })
      .call(() => {
        bullet.getComponent(sp.Skeleton).setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
          //清空监听
          if ("animation2" == trackEntry.animation.name) {
            if (isValid(bullet) == false) {
              return;
            }
            bullet.getComponent(sp.Skeleton).setCompleteListener(null);
            bullet.destroy();
          }
        });
        bullet.getComponent(sp.Skeleton).setAnimation(0, "animation2", false);
      })
      .start();
  }

  calculateAngle(p1, p2) {
    var deltaX = p2.x - p1.x;
    var deltaY = p2.y - p1.y;
    var angle = (Math.atan2(deltaY, deltaX) * 180) / Math.PI; // 转换为角度
    return angle;
  }

  private calculateDistance(pointA, pointB) {
    // 使用欧几里得距离公式
    var dx = pointB.x - pointA.x;
    var dy = pointB.y - pointA.y;
    return Math.sqrt(dx * dx + dy * dy);
  }

  protected onEvtClose(): void {
    Tween.stopAllByTag(tweenTagEnum.UIHuntPreparePrimitive_Tag);
  }

  /**获取洪荒开启状态 */
  private PrimitiveIsOpen() {
    // 如果为０表示未开启
    if (HuntModule.data.bossEndStamp == 0) {
      this.openState = false;
    }

    if (HuntModule.data.bossStartStamp > TimeUtils.serverTime) {
      this.openState = false;
    } else {
      this.openState = true;
    }
  }
}
