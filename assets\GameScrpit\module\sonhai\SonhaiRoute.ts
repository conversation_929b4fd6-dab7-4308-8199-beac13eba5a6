import { UISonhai } from "../../game/ui/ui_sonhai/UISonhai";
import { UISonhaiAwarrdRate } from "../../game/ui/ui_sonhai/UISonhaiAwarrdRate";
import { UISonhaiBigAward } from "../../game/ui/ui_sonhai/UISonhaiBigAward";
import { UISonhaiLeiJi } from "../../game/ui/ui_sonhai/UISonhaiLeiJi";
import { UISonhaiSd } from "../../game/ui/ui_sonhai/UISonhaiSd";
import { UISonhaiTask } from "../../game/ui/ui_sonhai/UISonhaiTask";
import { UISonhaiXslb } from "../../game/ui/ui_sonhai/UISonhaiXslb";
import { Recording, RecordingMap } from "../../lib/ui/recordingMap";

export enum SonhaiRouteName {
  UISonhai = "UISonhai",
  UISonhaiBigAward = "UISonhaiBigAward",
  UISonhaiAwarrdRate = "UISonhaiAwarrdRate",
  UISonhaiTask = "UISonhaiTask",
  UISonhaiLeiJi = "UISonhaiLeiJi",
  UISonhaiXslb = "UISonhaiXslb",
  UISonhaiSd = "UISonhaiSd",
}
export class SonhaiRoute {
  rotueTables: Recording[] = [
    {
      node: UISonhai,
      uiName: SonhaiRouteName.UISonhai,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UISonhaiBigAward,
      uiName: SonhaiRouteName.UISonhaiBigAward,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UISonhaiAwarrdRate,
      uiName: SonhaiRouteName.UISonhaiAwarrdRate,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UISonhaiTask,
      uiName: SonhaiRouteName.UISonhaiTask,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UISonhaiLeiJi,
      uiName: SonhaiRouteName.UISonhaiLeiJi,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UISonhaiXslb,
      uiName: SonhaiRouteName.UISonhaiXslb,
      keep: false,
      relevanceUIList: [],
    },

    {
      node: UISonhaiSd,
      uiName: SonhaiRouteName.UISonhaiSd,
      keep: false,
      relevanceUIList: [],
    },
  ];

  public async init() {
    this.rotueTables.forEach((item) => {
      RecordingMap.instance.addRecording(item.uiName, item);
    });
  }
}
