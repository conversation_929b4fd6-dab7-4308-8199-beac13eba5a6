import { _decorator, Component, Label, Node, ScrollView } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { sonhai_activityId, SonhaiModule } from "../../../module/sonhai/SonhaiModule";
import { Sleep } from "../../GameDefine";
import ToolExt from "../../common/ToolExt";
import { RedeemRequest, RedeemResponse } from "../../net/protocol/Activity";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { PlayerModule } from "../../../module/player/PlayerModule";
import GameHttpApi from "../../httpNet/GameHttpApi";
import { GoodsRouteName } from "../../../module/goods/GoodsRoute";
import { ConfirmMsg } from "../UICostConfirm";
import { JsonMgr } from "../../mgr/JsonMgr";
import { PublicRouteName } from "../../../module/player/PlayerConstant";
import { SonhaiAudioName, SonhaiMsgEnum } from "../../../module/sonhai/SonhaiConfig";
import { dtTime } from "../../BoutStartUp";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "../../../lib/utils/FmUtils";
import { RedeemPackVO } from "../../../module/activity/ActivityConfig";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

const LbId: number = 11101201;
const db_info: string = "db_info";

@ccclass("UISonhaiXslb")
export class UISonhaiXslb extends UINode {
  protected _openAct: boolean = true; //打开动作
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_HD_SONHAI}?prefab/ui/UISonhaiXslb`;
  }

  private _list: Array<RedeemPackVO> = null;

  private _goodsType: number = null;

  protected onRegEvent(): void {
    MsgMgr.on(SonhaiMsgEnum.SONHAI_GIFTPACKAGE_REFRESH, this.loadLb, this);
  }

  protected onDelEvent(): void {
    MsgMgr.off(SonhaiMsgEnum.SONHAI_GIFTPACKAGE_REFRESH, this.loadLb, this);
  }

  protected async onEvtShow() {
    let db = await SonhaiModule.data.getSonhaiDb(sonhai_activityId);
    this._goodsType = db.buyType;
    for (let i = 0; i < db.redeemList.length; i++) {
      let list = db.redeemList[i];
      let id = list[0].id;
      if (LbId == id) {
        this._list = list;
      }
    }
    log.log("限时礼包=====", this._list);

    this.loadLb();
  }

  private async loadLb() {
    let arr1 = [];
    let arr2 = [];
    for (let i = 0; i < this._list.length; i++) {
      let info = this._list[i];
      let redem_num = SonhaiModule.data.redeemMap[info.id] || 0;
      let show_bool = redem_num >= info.max ? true : false; //true 是代表已经售空

      if (show_bool == true) {
        arr2.push(info);
      } else {
        arr1.push(info);
      }
    }
    this._list = arr1.concat(arr2);

    if (this._list.length < this.getNode("content_libao").children.length) {
      let num = this.getNode("content_libao").children.length - this._list.length;
      for (let i = 0; i < num; i++) {
        let pop: Node = this.getNode("content_libao").children[0];
        pop.removeFromParent();
        pop.destroy();
      }
    }

    for (let i = 0; i < this._list.length; i++) {
      let info = this._list[i];
      let node = this.getNode("content_libao").children[i];
      if (!node) {
        node = ToolExt.clone(this.getNode("libao_item"), this);
        this.getNode("content_libao").addChild(node);
        node.active = true;
      }
      this.setDetail(node, info);
    }
  }

  private async setDetail(node: Node, info: RedeemPackVO) {
    node["lbl_lb_name"].getComponent(Label).string = info.name || "服务端没有名字";

    let redem_num = SonhaiModule.data.redeemMap[info.id] || 0;
    let show_bool = redem_num >= info.max ? true : false; //true 是代表已经售空
    if (show_bool == false) {
      node["lbl_buy_max"].getComponent(Label).string =
        ToolExt.getMaxtypeLab(info.maxtype) + `(${info.max - redem_num}/${info.max})`;
    } else {
      node["lbl_buy_max"].active = false;
      node["btn_item6_buy"].active = false;
      node["btn_goumai"].active = false;
      node["btn_ad"].active = false;
    }
    node["yishouqin"].active = show_bool;
    node["btn_goumai"].active = !show_bool;

    node["btn_item6_buy"][db_info] = info;
    node["btn_goumai"][db_info] = info;
    node["btn_ad"][db_info] = info;

    let is = false;
    if (is == false && show_bool == false) {
      is = this.isPrice(node, info);
    }
    if (is == false && show_bool == false) {
      is == this.isItemBuy(node, info);
    }
    if (is == false && show_bool == false) {
      is == this.isAd(node, info);
    }

    let rewardList = ToolExt.traAwardItemMapList(info.rewardList);
    if (rewardList.length >= 4) {
      node["ScrollView"].getComponent(ScrollView).enabled = true;
    } else {
      node["ScrollView"].getComponent(ScrollView).enabled = false;
    }
    if (rewardList.length < node["item_content"].children.length) {
      let num = node["item_content"].children.length - rewardList.length;
      for (let i = 0; i < num; i++) {
        let pop = node["item_content"].children[0];
        pop.removeFromParent();
        pop.destroy();
      }
    }

    for (let i = 0; i < rewardList.length; i++) {
      await Sleep(dtTime);

      let item = node["item_content"].children[i];
      if (!item) {
        item = ToolExt.clone(this.getNode("Item"), this);
        node["item_content"].addChild(item);
        item.active = true;
      }

      let info = rewardList[i];
      FmUtils.setItemNode(item, info.id, info.num);
    }
  }

  private isPrice(node: Node, info: RedeemPackVO) {
    if (info.price > 0) {
      let lbl_price: Node = node["lbl_price"];
      lbl_price.getComponent(Label).string = (info.price % 10000) + "元";

      node["btn_item6_buy"].active = false;
      node["btn_ad"].active = false;

      node["btn_goumai"].active = true;
      return true;
    } else {
      return false;
    }
  }

  private isItemBuy(node: Node, info: RedeemPackVO) {
    if (info.cost && info.cost.length >= 2) {
      ToolExt.setItemIcon(node["item_icon"], info.cost[0]);
      node["lbl_itembuy_num"].getComponent(Label).string = info.cost[1];

      node["btn_goumai"].active = false;
      node["btn_ad"].active = false;

      node["btn_item6_buy"].active = true;
      return true;
    } else {
      return false;
    }
  }

  private isAd(node: Node, info: RedeemPackVO) {
    node["lbl_ad_hit_count"].getComponent(Label).string = "观看" + info.adNum + "次视频";

    if (info.adNum > 0) {
      node["btn_item6_buy"].active = false;
      node["btn_goumai"].active = false;
      node["btn_ad"].active = true;
    }

    if (info.adNum == 1) {
      node["lbl_ad_hit_count"].active = false;
      node["lay_ad_count"].active = false;

      node["lab_ad_free"].active = true;
      return true;
    } else if (info.adNum > 1) {
      let childrenNum = node["lay_ad_count"].children.length;
      for (let i = 0; i < info.adNum && childrenNum < info.adNum; i++) {
        let ad_bar_clone = ToolExt.clone(this.getNode("ad_bar"));
        node["lay_ad_count"].addChild(ad_bar_clone);
        ad_bar_clone.active = true;
      }
      node["lay_ad_count"].children.forEach((val) => {
        val.getChildByName("has").active = false;
      });
      let ad_num = SonhaiModule.data.adMap[info.id] || 0;
      for (let i = 0; i < ad_num; i++) {
        node["lay_ad_count"].children[i].getChildByName("has").active = true;
      }
      return true;
    } else {
      return false;
    }
  }

  private on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  on_click_btn_ad(event) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let node = event.node;
    let info: RedeemPackVO = node[db_info];

    let param: RedeemRequest = {
      activityId: sonhai_activityId,
      redeemId: info.id,
      count: 1,
    };
    log.log("山海探险广告兑换礼包===", param);

    SonhaiModule.api.watchAdFixedPack(param, (res: RedeemResponse) => {
      let rewardList = res.rewardList;
      if (rewardList.length > 0) {
        MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: rewardList });
        this.loadLb();
      }
    });
  }

  private on_click_btn_goumai(event) {
    AudioMgr.instance.playEffect(SonhaiAudioName.Effect.点击充值按钮);
    let node: Node = event.node;
    let info: RedeemPackVO = node[db_info];
    if (info.price == 0) {
      let param: RedeemRequest = {
        activityId: sonhai_activityId,
        redeemId: info.id,
        count: 1,
      };

      SonhaiModule.api.buyFixedPack(param, (res: RedeemResponse) => {
        MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: res.rewardList });
        this.loadLb();
      });
      return;
    }

    let goodsId = info.id;
    let goodsType = this._goodsType;
    let playerId = PlayerModule.data.playerId;
    let orderAmount = info.price % 10000;
    let goodsName = info.name || "";
    let platformType = "TEST";
    this.onPay(goodsId, goodsType, playerId, orderAmount, goodsName, platformType);
  }

  private onPay(
    goodsId: number,
    goodsType: number,
    playerId: number,
    orderAmount: number,
    goodsName: string,
    platformType: string
  ) {
    let data = {
      goodsId: goodsId,
      goodsType: goodsType,
      playerId: playerId,
      orderAmount: orderAmount,
      goodsName: goodsName,
      platformType: platformType,
    };
    log.log("每日礼包购买参数=====", data);
    GameHttpApi.pay(data).then((resp: any) => {
      // window.open(resp.data.url);
      if (resp.code != 200) {
        let err = JSON.parse(resp.msg);
        log.log(err);
        return;
      }
      UIMgr.instance.showDialog(GoodsRouteName.UIPay, { url: resp.data.url });
    });
  }

  private on_click_btn_item6_buy(event) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let node = event.node.parent;
    let info: RedeemPackVO = node[db_info];
    let msg: ConfirmMsg = {
      msg: "是否花费" + info.cost[1] + JsonMgr.instance.getConfigItem(info.cost[0]).name + "购买该商品？",
      itemList: [],
      stopHintOption: false,
    };
    UIMgr.instance.showDialog(PublicRouteName.UICostConfirm, msg, (resp) => {
      if (resp?.ok) {
        let param: RedeemRequest = {
          activityId: sonhai_activityId,
          redeemId: info.id,
          count: 1,
        };

        SonhaiModule.api.buyFixedPack(param, (res: RedeemResponse) => {
          MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: res.rewardList });
        });
      }
    });
  }

  protected onEvtClose(): void {}
}
