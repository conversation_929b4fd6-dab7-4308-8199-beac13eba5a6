import { _decorator, Vec3 } from "cc";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
import { Sleep } from "../game/GameDefine";
import { TipsMgr } from "../../platform/src/TipsHelper";
import { RewardRouteEnum } from "./RewardDefine";
import { ItemEnum } from "../lib/common/ItemEnum";
const { ccclass, property } = _decorator;

@ccclass("TopItemFlyAni")
export class TopItemFlyAni extends BaseCtrl {
  private _play气运_1: boolean = false;
  private _play仙玉_6: boolean = false;
  private _playItemIdList: number[] = [];

  private _wPosition: Vec3;

  init(args: { itemList: number[]; transformList?: number[]; wPosition: Vec3 }): void {
    super.init(args);

    this._wPosition = args.wPosition;

    let itemMap = {};
    for (let i = 0; i + 1 < args.itemList.length; i += 2) {
      const item = args.itemList[i];
      itemMap[item] = args.itemList[i + 1];
    }

    if (args.transformList) {
      for (let i = 0; i + 1 < args.transformList.length; i += 2) {
        const item = args.transformList[i];
        if (itemMap[item]) {
          itemMap[item] = itemMap[item] + args.transformList[i + 1];
        } else {
          itemMap[item] = args.transformList[i + 1];
        }

        if (!itemMap[item]) {
          delete itemMap[item];
        }
      }
    }

    for (let key in itemMap) {
      if (key == "1") {
        this._play气运_1 = true;
      } else if (key == "6") {
        this._play仙玉_6 = true;
      } else {
        this._playItemIdList.push(Number(key));
        this._playItemIdList.push(Number(itemMap[key]));
      }
    }
  }

  async start() {
    super.start();

    this.node.setWorldPosition(this._wPosition);

    if (this._playItemIdList.length > 0) {
      TipsMgr.topRouteCtrl.show(RewardRouteEnum.TopGetItem, {
        itemList: this._playItemIdList,
        startPos: this.getNode("ani_item").worldPosition,
      });
      await Sleep(0.3);
    }

    if (this._play气运_1) {
      TipsMgr.topRouteCtrl.show(RewardRouteEnum.TopGetRes, {
        itemId: ItemEnum.气运_1,
        startPos: this.getNode("ani_气运_1").worldPosition,
      });
      await Sleep(0.1);
    }

    if (this._play仙玉_6) {
      TipsMgr.topRouteCtrl.show(RewardRouteEnum.TopGetRes, {
        itemId: ItemEnum.仙玉_6,
        startPos: this.getNode("ani_仙玉_6").worldPosition,
      });
      await Sleep(0.1);
    }

    this.closeBack();
  }
}
