import { _decorator, Label, Node, sp } from "cc";
import { ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { HeroModule } from "../../../../module/hero/HeroModule";
import { JsonMgr } from "../../../mgr/JsonMgr";
import { UIMgr } from "../../../../lib/ui/UIMgr";
import { PlayerRouteName } from "../../../../module/player/PlayerConstant";
import { ItemCost } from "../../../common/ItemCost";
import { IConfigHeroSkill } from "../../../JsonDefine";
import { HeroAudioName } from "db://assets/GameScrpit/module/hero/HeroConfig";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
const { ccclass, property } = _decorator;
@ccclass("HeroExpandViewHolder")
export class HeroExpandViewHolder extends ViewHolder {
  @property(Label)
  private lblExtendsTitle: Label;
  @property(Label)
  private lblAddTips: Label;
  @property(Label)
  private lblAddTipsNext: Label;
  @property(Node)
  private itemCost: Node;
  @property(Node)
  private bgLevelFull: Node;
  @property(Node)
  private upEffect: Node;
  private skillInfo: IConfigHeroSkill;
  private heroId: number;
  private onClickLevelUp() {
    let attr = this.skillInfo.costList[0];
    if (!this.itemCost.getComponent(ItemCost).isEnough()) {
      UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, { itemId: attr[0] });
      return;
    }
    this.upEffect.active = true;
    this.upEffect.getComponent(sp.Skeleton).setAnimation(0, "animation", false);
    HeroModule.api.skillLevelUp(this.heroId, this.skillInfo.id, this.skillUpResponse.bind(this));
  }
  private skillUpResponse(data: any) {
    AudioMgr.instance.playEffect(HeroAudioName.Effect.提升成功);
    this.updateData(this.heroId, this.skillInfo.id);
  }
  public updateData(heroId: number, skillId: number) {
    this.heroId = heroId;
    this.skillInfo = JsonMgr.instance.jsonList.c_heroSkill[skillId];
    let heroMessage = HeroModule.data.getHeroMessage(heroId);
    let level = heroMessage?.skillMap[skillId];
    this.lblExtendsTitle.getComponent(Label).string = `${this.skillInfo.name}${level}级`;
    let quaAdd = HeroModule.service.getSkillQuality(this.skillInfo.id, level);
    this.lblAddTips.getComponent(Label).string = `资质+${quaAdd}`;
    if (level < this.skillInfo.maxLv) {
      let nextQuaAdd = HeroModule.service.getSkillQuality(this.skillInfo.id, level + 1);
      this.lblAddTipsNext.string = `(下级+${nextQuaAdd})`;
      this.itemCost.parent.active = true;
      this.bgLevelFull.active = false;
    } else {
      //显示满级
      this.itemCost.parent.active = false;
      this.bgLevelFull.active = true;
    }
    let attr = this.skillInfo.costList[0];
    this.itemCost.getComponent(ItemCost).setItemId(attr[0], attr[1]);
  }
}
