import { _decorator, Component, find, instantiate, Label, Layout, Node, tween, v3 } from "cc";
import { SceneLogin } from "../../script_game/scene_login/SceneLogin";
import { RouteSceneLogin } from "../../script_game/scene_login/RouteSceneLogin";
const { ccclass, property } = _decorator;

@ccclass("PageNotice")
export class PageNotice extends Component {
  playShowAni: boolean = true;

  @property(Node)
  private details: Node;

  @property(Node)
  private content: Node;

  onLoad() {}

  protected start(): void {
    let data = SceneLogin.routeMgr.findByKey(RouteSceneLogin.PageNotice.key).args;
    this.loadNoticeList(data);
  }

  private loadNoticeList(data: any) {
    for (let i = 0; i < data.length; i++) {
      let node = instantiate(this.details);

      this.content.addChild(node);
      node.getChildByName("lbl_title").getComponent(Label).string = data[i].title;
      let des = find("lay/des", node);
      des.getComponent(Label).string = data[i].content;
      node.active = true;

      /**强更刷新 */
      des.getComponent(Label).updateRenderData(true);
      this.content.getComponent(Layout).updateLayout(true);

      tween(node)
        .delay(0.1 * i)
        .by(0.1, { position: v3(-700, 0, 0) })
        .start();
    }
  }

  closeMain() {
    SceneLogin.routeMgr.back();
  }

  update(deltaTime: number) {}
}
