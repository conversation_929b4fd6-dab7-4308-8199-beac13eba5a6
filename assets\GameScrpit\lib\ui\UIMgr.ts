import { _decorator, Component, instantiate, Node, Prefab, UITransform, Widget } from "cc";
import { StartUp } from "../StartUp";
import { UINode } from "./UINode";
import MsgMgr from "../event/MsgMgr";
import TipMgr from "../tips/TipMgr";
import { RecordingMap } from "./recordingMap";
import MsgEnum from "../../game/event/MsgEnum";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";

import { AudioMgr } from "../../../platform/src/AudioHelper";
import { CityRouteName } from "../../module/city/CityConstant";
import { BoutStartUp } from "../../game/BoutStartUp";
import ResMgr from "../common/ResMgr";
import { NodeTool } from "../utils/NodeTool";
import { ArrayUtils } from "../utils/ArrayUtils";

const { ccclass, property } = _decorator;

const UIMask = "UIMask";

// 页面类型
export enum UIType {
  PAGE = 0,
  DIALOG = 1,
}

// 路由状态明细
export class RouteDetail {
  name: string;
  uiType: UIType;
  args: any;
  isHide: boolean;
  uiNode: UINode;
  callBack?: Function;
  uiReadyCallBack?: Function;
}

const log = Logger.getLoger(LOG_LEVEL.STOP);

@ccclass("UIMgr")
export class UIMgr extends Component {
  private static _instance: UIMgr = null;

  public static get instance(): UIMgr {
    if (UIMgr._instance == null) {
      UIMgr._instance = BoutStartUp.instance.gameRoot.addComponent(UIMgr);
      UIMgr._instance.init();
      MsgMgr.on(
        MsgEnum.UIReady,
        (name: string) => {
          let routeInfo = UIMgr._instance.getRouteByName(name);
          // 页面准备完成
          if (routeInfo) {
            routeInfo.uiReadyCallBack && routeInfo.uiReadyCallBack();
            routeInfo.uiReadyCallBack = null;
          }

          UIMgr._instance.showRelevanceList(name);

          // 是关联页面打开，不在重复关闭或打开其他页面
          const routeLast = UIMgr._instance._history[UIMgr._instance._history.length - 1];
          if (routeLast?.name !== name) {
            return;
          }

          // page页面准备完成后，隐藏后面的页面
          if (routeInfo.uiType == UIType.PAGE) {
            if (UIMgr._instance._history.length > 1) {
              const pageNameClose = UIMgr._instance._history[UIMgr._instance._history.length - 2].name;
              let routeInfoRecord = RecordingMap.instance.getMapKeyInfo(name);
              // 在关联页面列表里不隐藏

              let findNode = false;
              for (let i = 0; i < routeInfoRecord.relevanceUIList.length; i++) {
                let uiNode = new routeInfoRecord.relevanceUIList[i]();
                if (pageNameClose == uiNode.name) {
                  findNode = true;

                  break;
                }
              }

              if (!findNode) {
                UIMgr._instance.pageHideClose(pageNameClose, null);
              }
            }
          }
        },
        UIMgr._instance
      );
    }
    return UIMgr._instance;
  }

  public defaultPageName: string = CityRouteName.UIGameMap;

  /////////////////////////////////////////层

  private _gameRoot: Node;
  private _uiRoot: Node;

  public get gameRoot(): Node {
    return this._gameRoot;
  }

  public get uiRoot(): Node {
    return this._uiRoot;
  }

  ////////////////////////////////////////////////////////////////////

  private _ready: Map<string, UINode> = new Map<string, UINode>();
  private _all: Map<string, UINode> = new Map<string, UINode>();
  private _focus: UINode = null;

  private _history: RouteDetail[] = [];

  /** 路由最后跳转时间戳 */
  private _lastRouteChangeTs = 0;

  protected init() {
    this._gameRoot = BoutStartUp.instance.gameRoot;
    this._uiRoot = BoutStartUp.instance.uiRoot;

    this._gameRoot.setWorldPosition(StartUp.instance.getWorldCenter());
    this._uiRoot.setWorldPosition(StartUp.instance.getWorldCenter());

    let contentSize = StartUp.instance.getVisibleSize();

    this._gameRoot.getComponent(UITransform).setContentSize(contentSize);
    this._uiRoot.getComponent(UITransform).setContentSize(contentSize);
  }

  public static clear() {
    if (UIMgr._instance?._all) {
      UIMgr.instance._all.forEach((val, key) => {
        UIMgr.instance.closeByName(key, null);
      });
    }
    UIMgr._instance = null;
  }

  // 释放所有界面
  public release() {
    while (this._history.length > 1) {
      this.back();
    }
    UIMgr._instance = null;
  }

  /**
   * 添加路由信息
   * @param data 路由信息
   */
  public pushHistory(data: RouteDetail): RouteDetail {
    log.info("pushHistory", data.name);

    // 没有历史 直接插入
    if (this._history.length == 0) {
      this._history.push(data);
      return data;
    }

    // 不在历史记录里，直接插入
    let idx = this._history.findIndex((e) => e.name == data.name);
    if (idx == -1) {
      this._history.push(data);
      return data;
    }

    // 如果在当前界面就是要的界面，直接返回
    if (idx < this._history.length - 1) {
      for (let i = this._history.length - 1; i > idx; i--) {
        this.closeLastRoute({});
      }

      // 存在keep的页面，把自己移到最前
      if (idx < this._history.length - 1) {
        // 栈移到最前
        ArrayUtils.moveToLast(this._history, idx);

        // 节点称到最前
        const nodeToShow = this._all.get(data.name).node;
        nodeToShow.setSiblingIndex(nodeToShow.parent.children.length - 1);
      }
    }

    setTimeout(() => {
      data.uiReadyCallBack && data.uiReadyCallBack();
      data.uiReadyCallBack = null;
      data = this._history[this._history.length - 1];
    }, 1);

    this.isMaxHistory();
    return data;
  }

  public getMaxHistory(index: number = 1) {
    let list = this._history.concat([]);
    return list.splice(-index);
  }

  private isMaxHistory() {
    let list = this.getMaxHistory();
    if (list.length <= 0) {
      let data = new RouteDetail();
      data.args = null;
      data.uiType = UIType.PAGE;
      data.name = this.defaultPageName;
      this._history.push(data);
    }
  }

  public show<T extends UINode>(type: { new (): T }, args = null) {
    if (!type) {
      log.error(" show ", "show type 不存在", type);
      return;
    }
    log.info("  show ", type.name);
    if (!type) {
      TipMgr.showTip("界面未进行注册----" + type);
      return;
    }
    let uiNode = new type();
    let name = uiNode.name;

    if (this._all.has(name)) {
      MsgMgr.emit("ON_UI_READY" + name, name);

      if (!this._all.get(name).isShow || name == UIMask) {
        this._all.get(name).onReshow(args);
      } else {
        MsgMgr.emit(MsgEnum.UIReady, name);
      }
    } else {
      if (this._ready.has(name)) {
        return;
      }
      //正在等待ui加载完成
      if (uiNode == null) {
        return;
      }
      if (!args?.parent) {
        args = Object.assign(args || {}, { parent: this._uiRoot });
      }
      uiNode.init(args);
      this._ready.set(name, uiNode);
    }
    return uiNode;
  }

  public close<T extends UINode>(type: { new (): T }) {
    let uiNode = new type();
    let name = uiNode.name;
    this.closeByName(name, null);
  }

  public hide<T extends UINode>(type: { new (): T }) {
    let uiNode = new type();
    let name = uiNode.name;
    this.hideByName(name);
  }

  public closeByName(name, resp?: any) {
    // 移除路由
    let pageInfoRemove = this.getRouteByName(name);
    this._history = this._history.filter((e) => e.name != name);

    if (this._all.has(name)) {
      this._all.get(name).onClose();
      this._all.delete(name);
      pageInfoRemove?.callBack && pageInfoRemove.callBack(resp || {});
    }
    if (this._ready.has(name)) {
      this._ready.get(name).onClose();
      this._ready.delete(name);
    }
  }

  public hideByName(name) {
    let idx = this._history.findIndex((e) => e.name == name);
    // 移到上一个不是隐藏的页面前
    // ArrayUtils.swapArrayElements(this._history, idx, 0);
    this._history[idx].isHide = true;

    if (this._all.has(name)) {
      this._all.get(name).onHide();
    }
  }

  public override update(dt) {
    this.updateUINode(dt);
  }

  public showSon<T extends UINode>(type: { new (): T }, args = null) {
    this.show(type, args);
  }

  private inspectReady() {
    log.info("  inspectReady ");
    //检查节点准备好了吗
    let deleteKeyArray = new Array<string>();
    let messageList = [];
    this._ready.forEach((node, name) => {
      if (node.ready) {
        log.info("  inspectReady add all " + name);
        this._all.set(name, node);
        deleteKeyArray.push(name);
        messageList.push(name);
      }
    });
    for (let i = 0; i < deleteKeyArray.length; i++) {
      let name = deleteKeyArray[i];
      this._ready.delete(name);
    }
    for (let idx in messageList) {
      let name = messageList[idx];
      MsgMgr.emit("ON_UI_READY" + name, name);
    }
  }

  private updateUINode(dt) {
    this._all.forEach((node, name) => {
      if (node.isShow) {
        node.tick(dt);
      } else {
        node.sleepTick(dt);
      }
    });
  }

  public getByName(name: string) {
    if (this._all.has(name)) {
      return this._all.get(name);
    } else return null;
  }

  public getByAllReady(name: string) {
    if (this._all.has(name)) {
      return this._all.get(name);
    }
    if (this._ready.has(name)) {
      return this._ready.get(name);
    }
    return null;
  }

  public get focus(): UINode {
    return this._focus;
  }

  public showPage(key: string, args = null, callBack?: Function, uiReadyCallBack?: Function) {
    log.info("showPage", key);
    let routeInfo = RecordingMap.instance.getMapKeyInfo(key);
    if (!routeInfo) {
      log.info(" showPage 界面没有注册======", key);
      return;
    }

    let data = new RouteDetail();
    data.name = key;
    data.args = args || {};
    data.uiType = UIType.PAGE;
    data.callBack = callBack;
    data.uiReadyCallBack = uiReadyCallBack;
    data = this.pushHistory(data);

    data.args = Object.assign(data.args, { parent: this._gameRoot });

    MsgMgr.once("ON_UI_READY" + routeInfo.uiName, this.addNodeToWorld, this);
    this._lastRouteChangeTs = new Date().valueOf();
    data.uiNode = this.show(routeInfo.node, data.args);

    setTimeout(() => {
      // 背景音乐播放
      if (routeInfo.music && routeInfo.music > 0) {
        AudioMgr.instance.playMusic(routeInfo.music);
      }
    }, 500);

    // if (
    //   [
    //     "UILevelBoss",
    //     "UIFightPage",
    //     FightRouteItem.UILevelGame,
    //     ClubRouteItem.UIClubFight,
    //     HuntRouteName.UIHuntFight,
    //     CityRouteName.UICityFight,
    //     HuntRouteName.UIPrepareFight,
    //     HuntRouteName.UIHuntPrepareSpirit,
    //   ].includes(key)
    // ) {
    // AudioMgr.instance.playMusic(AudioName.Sound.战斗);
    // } else if ("UIHuntMain" == key) {
    //   AudioMgr.instance.playMusic(AudioName.Sound.天荒古境);
    // } else {
    //   AudioMgr.instance.playMusic(AudioName.Sound.府邸和三界通用);
    // }
  }

  public showDialog(key: string, args = null, callBack?: Function, uiReadyCallBack?: Function) {
    log.info(" showDialog ", key);
    let routeInfo = RecordingMap.instance.getMapKeyInfo(key);
    if (!routeInfo) {
      log.info(" showDialog 界面没有注册======" + key);
      return;
    }

    let data = new RouteDetail();
    data.name = key;
    data.args = args || {};

    data.uiType = UIType.DIALOG;
    data.callBack = callBack;
    data.uiReadyCallBack = uiReadyCallBack;
    data = this.pushHistory(data);

    data.args = Object.assign({ parent: this._uiRoot }, data.args);

    MsgMgr.once("ON_UI_READY" + routeInfo.uiName, this.addNodeToWorld, this);
    this._lastRouteChangeTs = new Date().valueOf();
    data.uiNode = this.show(routeInfo.node, data.args);

    // 背景音乐播放

    if (routeInfo.music && routeInfo.music > 0) {
      AudioMgr.instance.playMusic(routeInfo.music);
    }
    // if (
    //   [
    //     "UILevelBoss",
    //     "UIFightPage",
    //     FightRouteItem.UILevelGame,
    //     ClubRouteItem.UIClubFight,
    //     HuntRouteName.UIHuntFight,
    //     CityRouteName.UICityFight,
    //     HuntRouteName.UIPrepareFight,
    //     HuntRouteName.UIHuntPrepareSpirit,
    //   ].includes(key)
    // ) {
    //   AudioMgr.instance.playMusic(AudioName.Sound.战斗);
    // } else if ("UIHuntMain" == key) {
    //   AudioMgr.instance.playMusic(AudioName.Sound.天荒古境);
    // } else {
    //   AudioMgr.instance.playMusic(AudioName.Sound.府邸和三界通用);
    // }
  }

  /**
   * 把节点加入到主场景，并更新UIMASK位置
   * @param name 节点名
   */
  private addNodeToWorld(name: string) {
    log.info(" addNodeToWorld ", name);

    MsgMgr.emit(MsgEnum.ON_UINAVIGATE_MAIN_CLOSE_GO_ZHAOGE, name);

    // 更新UIMask
    this.updateUIMask();
  }

  /** 更新UIMask位置 */
  private updateUIMask() {
    log.info(" updateUIMask ");
    let pageInfoShow = this._history[this._history.length - 1];

    if (pageInfoShow && pageInfoShow.uiType == UIType.DIALOG) {
      if (pageInfoShow.args["hideMask"]) {
        this.closeByName(UIMask);
        return;
      }
      if (pageInfoShow.args["keepMask"]) {
        return;
      }
      const uiNodeMask = this.getByName(UIMask);
      if (!uiNodeMask) {
        let routeInfo = RecordingMap.instance.getMapKeyInfo(UIMask);
        this.show(routeInfo.node);
        MsgMgr.once("ON_UI_READY" + UIMask, this.addNodeToWorld, this);
        return;
      }
      const uiNodeShow = this.getByName(pageInfoShow.name);

      // 节点未准备
      if (!uiNodeShow) {
        log.info(" updateUIMask 节点未准备 " + pageInfoShow.name);
        return;
      }

      const parent = uiNodeShow.node.getParent();
      parent.removeChild(uiNodeMask.node);
      parent.insertChild(uiNodeMask.node, uiNodeShow.node.getSiblingIndex());
      // 加入界面，手动适配一下界面，在移动位置
      if (pageInfoShow.uiNode?.node && pageInfoShow.uiNode.node.components) {
        let widget = pageInfoShow.uiNode.node.getComponent(Widget);
        widget && widget.updateAlignment();
      }
    } else {
      this.closeByName(UIMask);
    }
  }

  private showRelevanceList(uiName: string) {
    log.info(" showRelevanceList ", uiName);

    // 刷新状态
    this.inspectReady();

    // uimask的情况更新
    if (uiName == UIMask) {
      log.info(" showRelevanceList updateUIMask ", uiName);
      this.updateUIMask();
      return;
    }
    log.info(" showRelevanceList uiName == UIMask.name ", uiName == UIMask);

    // 非路由配置的，不做处理
    let routeInfo = RecordingMap.instance.getMapKeyInfo(uiName);
    if (!routeInfo) {
      return;
    }

    // 没有配置关联UI, 保持不变
    if (routeInfo.relevanceUIList.length == 1 && !routeInfo.relevanceUIList) {
      return;
    }

    const pageInfoCurr = this._history[this._history.length - 1];
    if (!pageInfoCurr || pageInfoCurr.name != uiName) {
      return;
    }

    // 路由控制
    if (pageInfoCurr.uiType == UIType.PAGE) {
      // 打开面前前，关闭所有弹窗，因为会
      // for (let idxPage = this._history.length - 2; idxPage > 0; idxPage--) {
      //   let routePage = this._history[idxPage];
      //   if (routePage.uiType == UIType.DIALOG) {
      //     // 取出并关闭当前界面
      //     this.closeLastRoute({}); // this.back({ dialogWaitCd: false });
      //     continue;
      //   }
      //   break;
      // }
      // // 隐藏或关闭其他页面
      // for (let idxPage = this._history.length - 2; idxPage > 0; idxPage--) {
      //   let pageInfo = this._history[idxPage];
      //   if (UIType.PAGE != pageInfo.uiType) {
      //     continue;
      //   }
      //   this.pageHideClose(pageInfo.name);
      // }
    }

    // 其他关联界面
    for (let idx in routeInfo.relevanceUIList) {
      const classNode = routeInfo.relevanceUIList[idx];
      this.show(classNode);
    }
  }

  private pageHideClose(key: string, resp?: any) {
    log.info(" 正在处理页面隐藏/关闭：", key);

    let routeInfo = RecordingMap.instance.getMapKeyInfo(key);

    // 修复1：增加路由信息空判断
    if (!routeInfo) {
      this.closeByName(key, resp);
      return;
    }

    // 修复2：调整判断顺序防止空访问
    if (routeInfo.node == null) {
      this.closeByName(key, resp);
      return; // 添加返回避免继续执行
    }

    // 修复3：转换弱类型判断为严格模式
    if (routeInfo.keep === true && !routeInfo.isNoHide) {
      this.hideByName(key);
      return;
    }

    if (!routeInfo.isNoHide) {
      // 已通过上方空判断，可直接访问属性
      this.closeByName(key, resp);
    }
  }

  /**
   * 关闭最后打开页面，主页不关
   */

  private closeLastRoute(resp?: any) {
    if (this._history.length > 0) {
      let pageInfoRemove = this._history[this._history.length - 1]; //this._history.pop();

      if (!pageInfoRemove) {
        return;
      }

      this.pageHideClose(pageInfoRemove.name, resp);

      // pageInfoRemove.callBack && pageInfoRemove.callBack(resp || {});
    }
  }

  /**
   * 回退
   */
  public back(params: any = { dialogWaitCd: false, resp: null }) {
    log.info(" back ", this._history.length);

    // 弹窗冷却时间， 用于防止弹窗过快关闭
    if (params.dialogWaitCd && this._lastRouteChangeTs + 500 > Date.now()) {
      let pageInfoCurr = this._history[this._history.length - 1];
      if (pageInfoCurr.uiType == UIType.DIALOG) {
        log.warn(" back ", "弹窗冷却时间， 用于防止弹窗过快关闭");
        return;
      }
    }

    // 最新窗口未准备
    let pageInfoCurr = this._history[this._history.length - 1];
    if (!this.getByName(pageInfoCurr.name)) {
      log.warn(" back ", "窗口未准备好，不能关闭", pageInfoCurr.name);
      return;
    }

    // 取出并关闭当前界面
    this.closeLastRoute(params.resp);

    // 显示上一页面
    pageInfoCurr = this._history[this._history.length - 1];

    if (pageInfoCurr) {
      if (pageInfoCurr.uiType == UIType.DIALOG) {
        this.showDialog(pageInfoCurr.name, pageInfoCurr.args);
      } else if (pageInfoCurr.uiType == UIType.PAGE) {
        this.showPage(pageInfoCurr.name, pageInfoCurr.args);
      }
    } else {
      this.showPage(this.defaultPageName);
    }

    // 设置UIMASK
    this.updateUIMask();

    // 关闭不刷新最后切换时间
    this._lastRouteChangeTs = 0;
  }

  /**
   * 替换当前界面，换为一个对话框
   * @param key 要加载的界面
   * @param args 参数
   */
  public replaceDialog(key: string, args = Object.create(null), callBack?: Function, uiReadyCallBack?: Function) {
    let routeInfo = RecordingMap.instance.getMapKeyInfo(key);
    if (!routeInfo) {
      log.info(" showPage 界面没有注册======" + key);
      return;
    }

    // 取出并关闭当前界面
    // this.closeLastRoute();

    // 最后的页面
    let routeLast = this._history.pop();

    let uiReadyCallBackNew: Function = null;

    uiReadyCallBackNew = () => {
      log.info(" uiReadyCallBack ");

      uiReadyCallBack && uiReadyCallBack();
      // 关闭上一页面
      this.closeByName(routeLast.name);
    };

    this.showDialog(key, args, callBack, uiReadyCallBackNew);
  }

  public getLastPageInfo(): RouteDetail {
    if (!this._history || this._history.length == 0) {
      this.showPage(this.defaultPageName);
    }

    return this._history[this._history.length - 1];
  }

  public getRouteByName(name: string): RouteDetail {
    for (let idx in this._history) {
      let route = this._history[idx];
      if (route.name == name) {
        return route;
      }
    }

    return null;
  }

  public addToUIRoot(bundle: string, asserUrl: String, name: string, callBack?: Function) {
    let nodeExist = NodeTool.findByName(UIMgr.instance.uiRoot, name);

    if (nodeExist) {
      callBack && callBack();
      return;
    }

    ResMgr.loadPrefab(`${bundle}?${asserUrl}`, (pb: Prefab) => {
      let nodeExist = NodeTool.findByName(UIMgr.instance.uiRoot, name);
      if (!nodeExist) {
        let node = instantiate(pb);
        node.name = name;
        UIMgr.instance.uiRoot.addChild(node);
      }

      callBack && callBack();
    });
  }
}
