{"skeleton": {"hash": "VEC9czYRYB9rMBc2Q+Eq4O3nQ9M=", "spine": "3.8.75", "x": -372.9, "y": -375, "width": 843.72, "height": 771.11, "images": "./images/", "audio": "D:/1.熊/2D项目东西/18号楼_3.8.75/优秀-金银角"}, "bones": [{"name": "root", "scaleX": 0.5, "scaleY": 0.5}, {"name": "bone", "parent": "root", "length": 948.25, "y": -750}, {"name": "wu7", "parent": "bone", "length": 788.89, "rotation": 0.66, "x": -48.92, "y": 57.79}, {"name": "yy", "parent": "bone", "length": 297.71, "rotation": -0.44, "x": 8.63, "y": 142.6}, {"name": "jj_17", "parent": "bone", "length": 99.62, "rotation": -1.15, "x": 219.85, "y": 648.27}, {"name": "jj_18", "parent": "jj_17", "length": 141.34, "rotation": 97.62, "x": -2.61, "y": 30.79}, {"name": "jj_19", "parent": "jj_18", "length": 196.05, "rotation": -22.7, "x": 140.24, "y": -0.88}, {"name": "jj_3", "parent": "jj_19", "length": 218.67, "rotation": 3.98, "x": 243.9, "y": -45.96}, {"name": "jj_4", "parent": "jj_19", "rotation": 3.98, "x": 186.7, "y": -224.18, "color": "00e309ff"}, {"name": "jj_13", "parent": "jj_4", "length": 275.42, "rotation": -149.85, "x": -8.74, "y": 2.23, "color": "00e309ff"}, {"name": "jj_12", "parent": "jj_13", "length": 138.67, "rotation": -88.81, "x": 274.8, "y": -1.92, "color": "00e309ff"}, {"name": "jj_1", "parent": "jj_12", "length": 124.34, "rotation": -32.79, "x": 139.28, "y": -0.31, "color": "00e309ff"}, {"name": "jj_10", "parent": "jj_4", "length": 118.1, "rotation": -153.35, "x": 12.18, "y": -62.34}, {"name": "jj_11", "parent": "jj_10", "length": 111.06, "rotation": -6.14, "x": 118.72, "y": -0.16}, {"name": "jj_9", "parent": "jj_4", "length": 95.89, "rotation": -85.15, "x": 39.39, "y": 84.07}, {"name": "jj_14", "parent": "jj_9", "length": 82.76, "rotation": -18.38, "x": 95.89}, {"name": "jj_15", "parent": "jj_14", "length": 88.41, "rotation": -11.43, "x": 82.76}, {"name": "jj_6", "parent": "jj_9", "length": 68.67, "rotation": -21.33, "x": 41.22, "y": 61.77}, {"name": "jj_7", "parent": "jj_14", "length": 71.62, "rotation": -13.32, "x": -2.78, "y": 37.84}, {"name": "jj_20", "parent": "jj_19", "x": 231.98, "y": -112.62}, {"name": "jj_21", "parent": "jj_19", "length": 64.73, "rotation": 174.71, "x": 173.69, "y": 33.78}, {"name": "jj_22", "parent": "jj_21", "length": 47.64, "rotation": 5.28, "x": 64.73}, {"name": "jj_23", "parent": "jj_22", "length": 61.44, "rotation": 13.53, "x": 47.64}, {"name": "jj_24", "parent": "jj_19", "length": 50.64, "rotation": 112.13, "x": 250.49, "y": 115.38}, {"name": "jj_25", "parent": "jj_24", "length": 44.04, "rotation": 18.97, "x": 50.64}, {"name": "jj_27", "parent": "jj_19", "length": 51.87, "rotation": 116.47, "x": 44.43, "y": 113.09}, {"name": "jj_16", "parent": "jj_27", "length": 53.08, "rotation": 69.75, "x": -52.03, "y": -4.35}, {"name": "jj_26", "parent": "jj_16", "length": 49.3, "rotation": 6.43, "x": 52.58, "y": -0.71}, {"name": "jj_28", "parent": "jj_27", "length": 43.72, "rotation": 83.01, "x": 53.39, "y": 8.06}, {"name": "jj_29", "parent": "jj_28", "length": 41.9, "rotation": -9.37, "x": 43.72}, {"name": "jj_31", "parent": "jj_19", "length": 67.01, "rotation": -82.08, "x": 48.87, "y": -106.08}, {"name": "jj_32", "parent": "jj_31", "length": 47.85, "rotation": -89.25, "x": -70.8, "y": 1.4}, {"name": "jj_33", "parent": "jj_32", "length": 51.8, "rotation": -16.13, "x": 47.85}, {"name": "jj_34", "parent": "jj_31", "length": 40.76, "rotation": -77.61, "x": 34.21, "y": -9.19}, {"name": "jj_35", "parent": "jj_34", "length": 38, "rotation": -6.8, "x": 40.76}, {"name": "jj_8", "parent": "jj_19", "length": 82.42, "rotation": 113.79, "x": 226.34, "y": 120.25}, {"name": "jj_30", "parent": "jj_8", "length": 101.86, "rotation": 1.83, "x": 82.42}, {"name": "jj_36", "parent": "jj_19", "x": 143.58, "y": 170.5, "color": "00e309ff"}, {"name": "jj_38", "parent": "jj_36", "length": 219.79, "rotation": 131.95, "x": -3.1, "y": 0.97, "color": "00e309ff"}, {"name": "jj_37", "parent": "jj_38", "length": 212.55, "rotation": 109.27, "x": 219.78, "color": "00e309ff"}, {"name": "jj_40", "parent": "jj_17", "length": 76.12, "rotation": -23.3, "x": -192.13, "y": 103.56, "color": "00e309ff"}, {"name": "jj_41", "parent": "jj_36", "length": 106.91, "rotation": 113.82, "x": 12.94, "y": 28.67}, {"name": "jj_42", "parent": "jj_41", "length": 84.91, "rotation": 11.85, "x": 106.91}, {"name": "jj_5", "parent": "jj_3", "length": 41.84, "rotation": -124, "x": 38.73, "y": -72.11, "color": "ff0505ff"}, {"name": "jj_43", "parent": "jj_3", "length": 41.79, "rotation": 102.24, "x": 38.94, "y": 124.01, "color": "ff0505ff"}, {"name": "jj_44", "parent": "jj_43", "length": 42.78, "rotation": -47.44, "x": 41.79, "color": "ff0505ff"}, {"name": "jj_45", "parent": "jj_3", "length": 42.52, "rotation": -147.4, "x": 79.22, "y": -119.25, "color": "ff0505ff"}, {"name": "jj_46", "parent": "jj_45", "length": 52.69, "rotation": 45.9, "x": 42.52, "color": "ff0505ff"}, {"name": "jj_47", "parent": "jj_46", "length": 57.36, "rotation": 61, "x": 52.69, "color": "ff0505ff"}, {"name": "jj_48", "parent": "jj_17", "length": 72.46, "rotation": -100.95, "x": -28.02, "y": 15.09}, {"name": "jj_49", "parent": "jj_48", "length": 82.02, "rotation": 7.04, "x": 72.46}, {"name": "jj_50", "parent": "jj_49", "length": 93.19, "rotation": -4.77, "x": 82.02}, {"name": "jj_51", "parent": "jj_17", "length": 246.26, "rotation": -57.34, "x": 48.54, "y": -27.88, "color": "00e309ff"}, {"name": "jj_52", "parent": "jj_51", "length": 240.52, "rotation": -23.95, "x": 246.26, "color": "00e309ff"}, {"name": "jj_53", "parent": "bone", "length": 99.11, "rotation": -62.07, "x": 428.19, "y": 171.05, "color": "00e309ff"}, {"name": "jj_54", "parent": "jj_17", "length": 206.91, "rotation": -123.68, "x": -48.64, "y": -22.44, "color": "00e309ff"}, {"name": "jj_55", "parent": "jj_54", "length": 184.26, "rotation": 26.93, "x": 206.91, "color": "00e309ff"}, {"name": "jj_56", "parent": "bone", "length": 107.43, "rotation": -144.59, "x": 27.29, "y": 274.44, "color": "00e309ff"}, {"name": "jj_57", "parent": "jj_17", "length": 99.32, "rotation": -71.71, "x": 128.94, "y": 17.3}, {"name": "jj_58", "parent": "jj_57", "length": 99.66, "rotation": -1.12, "x": 99.32}, {"name": "jj_59", "parent": "jj_57", "length": 95.72, "rotation": 3.48, "x": 198.97, "y": -1.96}, {"name": "jj_39", "parent": "jj_17", "length": 86.86, "rotation": -85.28, "x": 61.51, "y": -15.43}, {"name": "jj_60", "parent": "jj_39", "length": 118.47, "rotation": 3.18, "x": 86.86}, {"name": "jj_61", "parent": "jj_39", "length": 106.27, "rotation": 6.92, "x": 205.14, "y": 6.57}, {"name": "jj_63", "parent": "jj_17", "length": 116.68, "rotation": -104.82, "x": -110.98, "y": 34.03}, {"name": "jj_64", "parent": "jj_63", "length": 90.99, "rotation": -6.64, "x": 116.68}, {"name": "jj_65", "parent": "jj_63", "length": 89.46, "rotation": 1.41, "x": 207.07, "y": -10.52}, {"name": "jj_66", "parent": "jj_17", "length": 82.56, "rotation": -100.16, "x": -6.89, "y": -46.85}, {"name": "jj_67", "parent": "jj_66", "length": 77.09, "rotation": 3.26, "x": 82.56}, {"name": "jj_68", "parent": "jj_67", "length": 81.09, "rotation": 4.78, "x": 77.09}, {"name": "yj_18", "parent": "bone", "length": 79.14, "rotation": 14.97, "x": -329.06, "y": 487.19, "color": "102de8ff"}, {"name": "yj_19", "parent": "yj_18", "length": 73.87, "rotation": 72.27, "x": -6.93, "y": 15.38, "color": "102de8ff"}, {"name": "yj_20", "parent": "yj_19", "length": 124.41, "rotation": -36.73, "x": 73.87, "color": "102de8ff"}, {"name": "yj_21", "parent": "yj_20", "length": 59.44, "rotation": -49.69, "x": 128.52, "y": -9.82, "color": "102de8ff"}, {"name": "yj_biyan", "parent": "yj_21", "length": 183.23, "rotation": 59.77, "x": 59.45, "y": 0.85, "color": "102de8ff"}, {"name": "yj_22", "parent": "yj_20", "x": 191.33, "y": -132.73, "color": "102de8ff"}, {"name": "yj_24", "parent": "yj_22", "length": 185.74, "rotation": -123.93, "x": -2.63, "y": 3.19, "color": "102de8ff"}, {"name": "yj_17", "parent": "yj_24", "length": 104.8, "rotation": 118.1, "x": 184.95, "y": 0.24, "color": "102de8ff"}, {"name": "yj_9", "parent": "yj_17", "length": 78.7, "rotation": 47.13, "x": 103.64, "y": -1.18, "color": "102de8ff"}, {"name": "yj_10", "parent": "yj_9", "length": 40.57, "rotation": -1.81, "x": 56.27, "y": -28.29, "color": "102de8ff"}, {"name": "yj_11", "parent": "yj_10", "length": 65.46, "rotation": 18.43, "x": 40.57, "color": "102de8ff"}, {"name": "yj_16", "parent": "yj_22", "length": 71.71, "rotation": -105.67, "x": 30.79, "y": -11.9}, {"name": "yj_23", "parent": "yj_16", "length": 101.18, "rotation": 10.16, "x": 71.71}, {"name": "yj_12", "parent": "yj_biyan", "length": 99, "rotation": 88.8, "x": 20.39, "y": 82.52}, {"name": "yj_13", "parent": "yj_12", "length": 85.92, "rotation": 20.14, "x": 99}, {"name": "yj_14", "parent": "yj_13", "length": 99.4, "rotation": 10.47, "x": 85.92}, {"name": "yj_15", "parent": "yj_14", "length": 65.79, "rotation": 29.05, "x": 99.4}, {"name": "yj_25", "parent": "yj_15", "length": 64.61, "rotation": -41.11, "x": 65.79}, {"name": "yj_biyan2", "parent": "yj_biyan", "length": 72.47, "rotation": 76.57, "x": 161.55, "y": 70.18}, {"name": "yj_biyan3", "parent": "yj_biyan2", "length": 91.59, "rotation": 31.42, "x": 72.47}, {"name": "yj_biyan4", "parent": "yj_biyan3", "length": 77.46, "rotation": 8.2, "x": 91.59}, {"name": "yj_biyan5", "parent": "yj_biyan4", "length": 80.38, "rotation": 11.22, "x": 77.46}, {"name": "yj_26", "parent": "yj_20", "x": 66.46, "y": 124.65, "color": "102de8ff"}, {"name": "yj_28", "parent": "yj_26", "length": 182.55, "rotation": 175.74, "x": -1.19, "y": -1.9, "color": "102de8ff"}, {"name": "yj_8", "parent": "yj_28", "length": 181.56, "rotation": 174.16, "x": 182.53, "y": 0.02, "color": "102de8ff"}, {"name": "yj_6", "parent": "yj_8", "length": 80.13, "rotation": 48.58, "x": 181.57, "color": "102de8ff"}, {"name": "yj_1", "parent": "yj_6", "length": 55.78, "rotation": -113.48, "x": 54.75, "y": -34.14}, {"name": "yj_2", "parent": "yj_20", "length": 256.51, "rotation": 85.23, "x": 154.14, "y": 197.66, "transform": "noRotationOrReflection"}, {"name": "yj_29", "parent": "yj_26", "length": 100.63, "rotation": 164.48, "x": 44.44, "y": 35.36}, {"name": "yj_30", "parent": "yj_29", "length": 86.61, "rotation": 13.22, "x": 100.63}, {"name": "yj_31", "parent": "yj_20", "length": 54.12, "rotation": -139.81, "x": 93.41, "y": -71.13, "color": "a019c8ff"}, {"name": "yj_32", "parent": "yj_31", "length": 40.98, "rotation": 14.23, "x": 54.12, "color": "a019c8ff"}, {"name": "yj_33", "parent": "yj_32", "length": 45.85, "rotation": 15.33, "x": 40.98, "color": "a019c8ff"}, {"name": "yj_27", "parent": "yj_20", "rotation": 3.11, "x": 92.46, "y": 21.22, "color": "a019c8ff"}, {"name": "yj_34", "parent": "yj_20", "rotation": 3.76, "x": 74.6, "y": 178.58, "color": "a019c8ff"}, {"name": "yj_36", "parent": "yj_18", "length": 143.55, "rotation": -55.52, "x": 36.08, "y": -32.43, "color": "102de8ff"}, {"name": "yj_37", "parent": "yj_36", "length": 156.42, "rotation": -36, "x": 143.55, "color": "102de8ff"}, {"name": "yj_35", "parent": "bone", "length": 99.27, "rotation": -18.88, "x": -140.39, "y": 219.75, "color": "102de8ff"}, {"name": "yj_39", "parent": "yj_18", "length": 180.22, "rotation": -138.78, "x": -34.08, "y": -27.76, "color": "102de8ff"}, {"name": "yj_40", "parent": "yj_39", "length": 201.95, "rotation": 27.49, "x": 180.22, "color": "102de8ff"}, {"name": "yj_38", "parent": "bone", "length": 86.51, "rotation": -122.16, "x": -477.31, "y": 101.1, "color": "102de8ff"}, {"name": "yj_42", "parent": "yj_18", "length": 51.17, "rotation": -85.95, "x": 14.03, "y": -4.23}, {"name": "yj_43", "parent": "yj_42", "length": 45.5, "rotation": 13.5, "x": 51.17}, {"name": "yj_44", "parent": "yj_43", "length": 56.49, "rotation": 3.67, "x": 45.5}, {"name": "yj_45", "parent": "yj_18", "length": 67.05, "rotation": -54.29, "x": 51.02, "y": -2}, {"name": "yj_46", "parent": "yj_45", "length": 51.08, "rotation": -2.54, "x": 67.05}, {"name": "yj_47", "parent": "yj_46", "length": 40.67, "rotation": -8.06, "x": 51.08}, {"name": "yj_48", "parent": "yj_47", "length": 40.51, "rotation": -27.39, "x": 40.67}, {"name": "yj_49", "parent": "yj_48", "length": 31.83, "rotation": 4.25, "x": 70.32, "y": 41.89}, {"name": "yj_50", "parent": "yj_49", "length": 29.04, "rotation": 7.3, "x": 31.83}, {"name": "yj_51", "parent": "yj_48", "length": 31.33, "rotation": -19.35, "x": 59.56, "y": 31.76}, {"name": "yj_52", "parent": "yj_51", "length": 27.3, "rotation": 12.94, "x": 31.33}, {"name": "yj_53", "parent": "yj_48", "length": 38.1, "rotation": -20.67, "x": 47.81, "y": 12.69}, {"name": "yj_54", "parent": "yj_53", "length": 34.6, "rotation": -6.98, "x": 38.1}, {"name": "yj_55", "parent": "yj_48", "length": 33.34, "rotation": -36.7, "x": 28.1, "y": -26.65}, {"name": "yj_56", "parent": "yj_55", "length": 38.11, "rotation": 13, "x": 33.34}, {"name": "yj_57", "parent": "yj_18", "length": 55.47, "rotation": -92.77, "x": 0.12, "y": -22.51}, {"name": "yj_58", "parent": "yj_57", "length": 51.2, "rotation": 5.09, "x": 56.54, "y": 0.09}, {"name": "yj_59", "parent": "yj_58", "length": 40.85, "rotation": 2.33, "x": 51.2}, {"name": "yj_60", "parent": "yj_59", "length": 45.27, "rotation": 7.74, "x": 40.85}, {"name": "yj_61", "parent": "yj_18", "length": 70.79, "rotation": -134.97, "x": -69.21, "y": 29.76}, {"name": "yj_62", "parent": "yj_61", "length": 65.17, "rotation": 10.16, "x": 70.79}, {"name": "yj_63", "parent": "yj_62", "length": 78.33, "rotation": 5.36, "x": 65.17}, {"name": "yj_41", "parent": "yj_18", "length": 80.56, "rotation": -100.02, "x": -33.49, "y": 1.88}, {"name": "yj_64", "parent": "yj_41", "length": 76.48, "rotation": -4, "x": 80.56}, {"name": "yj_65", "parent": "yj_64", "length": 79.64, "rotation": -0.04, "x": 76.48}, {"name": "yj_66", "parent": "yj_65", "length": 38.43, "rotation": 10.55, "x": 91.55, "y": 15.77}, {"name": "yj_67", "parent": "yj_66", "length": 40.68, "rotation": 32.02, "x": 38.43}, {"name": "yj_68", "parent": "yj_65", "length": 30.61, "rotation": -4.72, "x": 87.06, "y": -10.12}, {"name": "yj_69", "parent": "yj_68", "length": 26.4, "rotation": 22.95, "x": 30.61}, {"name": "yj_70", "parent": "yj_65", "length": 28.79, "rotation": -9.04, "x": 78.41, "y": -42.06}, {"name": "yj_71", "parent": "yj_70", "length": 28.52, "rotation": 10.18, "x": 28.79}, {"name": "yj_72", "parent": "yj_63", "length": 35.09, "rotation": -9.48, "x": 87.04, "y": 2.71}, {"name": "yj_73", "parent": "yj_72", "length": 31.77, "rotation": -11.26, "x": 35.09}, {"name": "yj_74", "parent": "yj_63", "length": 36.67, "rotation": 0.83, "x": 93.06, "y": 22.14}, {"name": "yj_75", "parent": "yj_74", "length": 31.95, "rotation": -8.83, "x": 36.67}, {"name": "yj_76", "parent": "yj_63", "length": 29.54, "rotation": 12.51, "x": 106.88, "y": 54.1}, {"name": "yj_77", "parent": "yj_76", "length": 24.57, "rotation": -11.2, "x": 29.54}], "slots": [{"name": "dabj", "bone": "wu7", "attachment": "dabj"}, {"name": "lu", "bone": "wu7", "attachment": "lu"}, {"name": "yy", "bone": "yy", "color": "605f5fb6", "attachment": "yy"}, {"name": "wu7", "bone": "wu7", "attachment": "wu7"}, {"name": "wu6", "bone": "wu7", "attachment": "wu6"}, {"name": "wu5", "bone": "wu7", "attachment": "wu5"}, {"name": "baiy8", "bone": "bone", "attachment": "baiy8"}, {"name": "baiy7", "bone": "bone", "attachment": "baiy7"}, {"name": "wu4", "bone": "wu7", "attachment": "wu4"}, {"name": "wu3", "bone": "wu7", "attachment": "wu3"}, {"name": "wu2", "bone": "wu7", "attachment": "wu2"}, {"name": "wu1", "bone": "wu7", "attachment": "wu1"}, {"name": "baiy6", "bone": "bone", "attachment": "baiy6"}, {"name": "baiy5", "bone": "bone", "attachment": "baiy5"}, {"name": "baiy4", "bone": "bone", "attachment": "baiy4"}, {"name": "baiy3", "bone": "bone", "attachment": "baiy3"}, {"name": "baiy2", "bone": "bone", "attachment": "baiy2"}, {"name": "baiy1", "bone": "bone", "attachment": "baiy1"}, {"name": "jj_28", "bone": "jj_51", "attachment": "jj_28"}, {"name": "jj_27", "bone": "jj_54", "attachment": "jj_27"}, {"name": "jj_26", "bone": "jj_66", "attachment": "jj_26"}, {"name": "jj_25", "bone": "jj_57", "attachment": "jj_25"}, {"name": "jj_24", "bone": "jj_63", "attachment": "jj_24"}, {"name": "jj_23", "bone": "jj_48", "attachment": "jj_23"}, {"name": "jj_22", "bone": "jj_38", "attachment": "jj_22"}, {"name": "jj_21", "bone": "jj_37", "attachment": "jj_21"}, {"name": "jj_20", "bone": "jj_40", "attachment": "jj_20"}, {"name": "jj_19", "bone": "jj_30", "attachment": "jj_19"}, {"name": "jj_18", "bone": "jj_41", "attachment": "jj_18"}, {"name": "jj_17", "bone": "jj_17", "attachment": "jj_17"}, {"name": "jj_16", "bone": "jj_31", "attachment": "jj_16"}, {"name": "jj_15", "bone": "jj_16", "attachment": "jj_15"}, {"name": "jj_14", "bone": "jj_19", "attachment": "jj_14"}, {"name": "jj_13", "bone": "jj_13", "attachment": "jj_13"}, {"name": "jj_12", "bone": "jj_12", "attachment": "jj_12"}, {"name": "jj_11", "bone": "jj_15", "attachment": "jj_11"}, {"name": "jj_10", "bone": "jj_10", "attachment": "jj_10"}, {"name": "jj_9", "bone": "jj_9", "attachment": "jj_9"}, {"name": "jj_8", "bone": "jj_8", "attachment": "jj_8"}, {"name": "jj_6", "bone": "jj_6", "attachment": "jj_6"}, {"name": "jj_5", "bone": "jj_45", "attachment": "jj_5"}, {"name": "jj_4", "bone": "jj_43", "attachment": "jj_4"}, {"name": "jj_3", "bone": "jj_3", "attachment": "jj_3"}, {"name": "jj_biyan", "bone": "jj_3"}, {"name": "jj_2", "bone": "jj_12", "attachment": "jj_2"}, {"name": "jj_1", "bone": "jj_1", "attachment": "jj_1"}, {"name": "yj_27", "bone": "yj_45", "attachment": "yj_27"}, {"name": "yj_28", "bone": "yj_35", "attachment": "yj_28"}, {"name": "yj_26", "bone": "yj_36", "attachment": "yj_26"}, {"name": "yj_25", "bone": "yj_57", "attachment": "yj_25"}, {"name": "yj_24", "bone": "yj_38", "attachment": "yj_24"}, {"name": "yj_23", "bone": "yj_39", "attachment": "yj_23"}, {"name": "yj_22", "bone": "yj_45", "attachment": "yj_22"}, {"name": "yj_21", "bone": "yj_61", "attachment": "yj_21"}, {"name": "yj_20", "bone": "yj_42", "attachment": "yj_20"}, {"name": "yj_19", "bone": "yj_24", "attachment": "yj_19"}, {"name": "yj_18", "bone": "yj_18", "attachment": "yj_18"}, {"name": "yj_17", "bone": "yj_17", "attachment": "yj_17"}, {"name": "yj_16", "bone": "yj_16", "attachment": "yj_16"}, {"name": "yj_15", "bone": "yj_28", "attachment": "yj_15"}, {"name": "yj_14", "bone": "yj_29", "attachment": "yj_14"}, {"name": "yj_13", "bone": "yj_21", "attachment": "yj_13"}, {"name": "yj_12", "bone": "yj_31", "attachment": "yj_12"}, {"name": "yj_11", "bone": "yj_12", "attachment": "yj_11"}, {"name": "yj_10", "bone": "yj_17", "attachment": "yj_10"}, {"name": "yj_9", "bone": "yj_9", "attachment": "yj_9"}, {"name": "yj_8", "bone": "yj_8", "attachment": "yj_8"}, {"name": "yj_7", "bone": "yj_8", "attachment": "yj_7"}, {"name": "yj_6", "bone": "yj_6", "attachment": "yj_6"}, {"name": "yj_5", "bone": "yj_biyan", "attachment": "yj_5"}, {"name": "yj_biyan", "bone": "yj_biyan"}, {"name": "yj_3", "bone": "yj_6", "attachment": "yj_3"}, {"name": "yj_2", "bone": "yj_2", "attachment": "yj_2"}, {"name": "yj_1", "bone": "yj_1", "attachment": "yj_1"}], "ik": [{"name": "jj_jiao1", "order": 1, "bones": ["jj_51", "jj_52"], "target": "jj_53", "bendPositive": false, "stretch": true}, {"name": "jj_jiao2", "order": 2, "bones": ["jj_54", "jj_55"], "target": "jj_56", "stretch": true}, {"name": "jj_shou", "bones": ["jj_38", "jj_37"], "target": "jj_40", "stretch": true}, {"name": "yj_jiao1", "order": 3, "bones": ["yj_36", "yj_37"], "target": "yj_35", "bendPositive": false, "stretch": true}, {"name": "yj_jiao2", "order": 4, "bones": ["yj_39", "yj_40"], "target": "yj_38", "stretch": true}], "skins": [{"name": "default", "attachments": {"jj_12": {"jj_12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-32.28, 127.61, 171.84, 56.95, 106.09, -132.99, -98.03, -62.34], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 216, "height": 201}}, "jj_13": {"jj_13": {"type": "mesh", "uvs": [0.22156, 0.22126, 0.62259, 0.22126, 0.91066, 0.27654, 0.9502, 0.38861, 1, 0.5548, 1, 0.8018, 0.88788, 0.92569, 0.59114, 1, 0.35487, 1, 0.10727, 0.96469, 0, 0.8577, 1e-05, 0.72569, 1e-05, 0.6508, 1e-05, 0.59115, 0.01135, 0.53363, 0, 0.38188, 0.11706, 0.46033, 0.33735, 0.33183, 0.19016, 0.5108, 0.49635, 0.4188, 0.11672, 0.63469, 0.60437, 0.59769], "triangles": [17, 1, 2, 16, 15, 17, 13, 14, 16, 17, 0, 1, 15, 0, 17, 14, 15, 16, 19, 17, 2, 19, 2, 3, 18, 16, 17, 19, 18, 17, 18, 13, 16, 21, 19, 3, 21, 3, 4, 18, 19, 21, 18, 12, 13, 20, 18, 21, 20, 12, 18, 11, 12, 20, 21, 4, 5, 6, 21, 5, 20, 10, 11, 8, 9, 10, 8, 20, 21, 7, 8, 21, 8, 10, 20, 6, 7, 21], "vertices": [1, 8, -49.35, 4.44, 1, 1, 8, -33.87, -66.88, 1, 2, 9, 90.66, 91.19, 0.52, 8, -41.34, -122.15, 0.48, 2, 9, 129.56, 86.18, 0.752, 8, -77.49, -137.36, 0.248, 1, 9, 186.74, 77.23, 1, 1, 9, 267.6, 51.11, 1, 1, 9, 301.88, 18.59, 1, 1, 9, 309.6, -40.66, 1, 1, 9, 296.38, -81.58, 1, 1, 9, 270.97, -120.73, 1, 1, 9, 229.94, -127.99, 1, 1, 9, 186.73, -114.03, 1, 2, 9, 162.22, -106.11, 0.752, 8, -202.3, 12.52, 0.248, 2, 9, 142.69, -99.8, 0.52, 8, -182.25, 16.87, 0.48, 1, 8, -162.47, 19.05, 1, 1, 8, -111.89, 32.13, 1, 2, 9, 106.42, -65.69, 0.52, 8, -133.75, 5.59, 0.48, 2, 9, 76.68, -13.95, 0.52, 8, -82.05, -24.22, 0.48, 2, 9, 127.03, -58.37, 0.752, 8, -147.9, -11.09, 0.248, 2, 9, 114.04, 4.39, 0.752, 8, -105.15, -58.84, 0.248, 1, 9, 163.48, -84.19, 1, 1, 9, 178.65, 4.18, 1], "hull": 16, "edges": [28, 30, 30, 0, 0, 2, 2, 4, 4, 6, 26, 28, 26, 32, 32, 34, 34, 4, 24, 26, 24, 36, 36, 38, 38, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 22, 40, 40, 42, 42, 10], "width": 182, "height": 344}}, "jj_14": {"jj_14": {"type": "mesh", "uvs": [0.55726, 0.06574, 0.71739, 0.19015, 0.84312, 0.25714, 0.93708, 0.31265, 0.93552, 0.47835, 0.89225, 0.75896, 0.80543, 0.71138, 0.81425, 0.84989, 0.74115, 1, 0.53196, 1, 0.39459, 0.85172, 0.35427, 0.68586, 0.25723, 0.78793, 0.12491, 0.85172, 0, 0.81162, 0, 0.63118, 0.03903, 0.51632, 0.0961, 0.26863, 0.13123, 0.22407, 0.2057, 0.21151, 0.18651, 0.17708, 0.19736, 0.12212, 0.22021, 0.10796, 0.21427, 0.16286, 0.23707, 0.18324, 0.23334, 0.12809, 0.25292, 0.08769, 0.33135, 0.02559, 0.31417, 0.06947, 0.3073, 0.11501, 0.33077, 0.1473, 0.35425, 0.10176, 0.34222, 0.03387, 0.37543, 0.03801, 0.3958, 0, 0.67688, 0.75512, 0.48281, 0.6968, 0.24337, 0.63118, 0.1123, 0.65852, 0.41624, 0.50661, 0.31155, 0.50739, 0.14336, 0.54634, 0.54048, 0.51671, 0.69682, 0.4945, 0.83362, 0.48642, 0.17286, 0.34519, 0.33167, 0.43897, 0.45077, 0.43514, 0.58709, 0.40069, 0.70355, 0.39112, 0.8312, 0.42557, 0.93576, 0.37964, 0.2446, 0.27039, 0.36575, 0.31501, 0.5652, 0.36241, 0.71077, 0.31456, 0.31864, 0.23119], "triangles": [1, 54, 0, 0, 53, 31, 31, 32, 33, 33, 34, 0, 33, 0, 31, 55, 1, 2, 30, 31, 53, 55, 54, 1, 54, 53, 0, 28, 26, 27, 29, 26, 28, 22, 23, 21, 20, 21, 23, 29, 24, 25, 29, 25, 26, 19, 20, 23, 19, 23, 24, 56, 29, 30, 24, 29, 56, 52, 24, 56, 19, 24, 52, 56, 30, 53, 52, 56, 53, 45, 18, 19, 45, 19, 52, 17, 18, 45, 51, 2, 3, 55, 48, 54, 49, 48, 55, 50, 55, 2, 50, 2, 51, 49, 55, 50, 47, 53, 54, 47, 54, 48, 46, 52, 53, 46, 53, 47, 45, 52, 46, 4, 50, 51, 4, 51, 3, 44, 50, 4, 43, 48, 49, 43, 49, 50, 43, 50, 44, 39, 46, 47, 40, 45, 46, 40, 46, 39, 16, 17, 45, 42, 47, 48, 42, 48, 43, 39, 47, 42, 41, 16, 45, 41, 45, 40, 37, 41, 40, 38, 16, 41, 38, 41, 37, 15, 16, 38, 11, 40, 39, 37, 40, 11, 36, 39, 42, 11, 39, 36, 6, 43, 44, 35, 42, 43, 35, 43, 6, 36, 42, 35, 5, 44, 4, 6, 44, 5, 12, 37, 11, 38, 37, 12, 14, 15, 38, 35, 6, 7, 13, 38, 12, 14, 38, 13, 10, 11, 36, 9, 36, 35, 10, 36, 9, 8, 35, 7, 9, 35, 8], "vertices": [2, 6, 350.34, -8.54, 0.16, 7, 108.78, 29.94, 0.84, 2, 6, 329.97, -104.19, 0.16, 7, 81.82, -64.07, 0.84, 2, 6, 324.95, -176.1, 0.568, 7, 71.81, -135.45, 0.432, 2, 6, 319.25, -230.41, 0.808, 7, 62.35, -189.23, 0.192, 1, 6, 259.83, -246.83, 1, 1, 6, 153.09, -253.64, 1, 1, 6, 157.04, -203.85, 1, 1, 6, 108.89, -222.8, 1, 1, 6, 44.28, -200.64, 1, 1, 6, 12.83, -92.58, 1, 1, 6, 45.15, -6.21, 1, 1, 6, 98.33, 31.86, 1, 1, 6, 47.29, 71.38, 1, 1, 6, 4.61, 133.1, 1, 1, 6, 0.15, 201.8, 1, 1, 6, 64.6, 220.55, 1, 1, 6, 111.5, 212.33, 1, 1, 6, 208.55, 208.59, 1, 1, 6, 229.75, 195.08, 1, 1, 6, 245.43, 157.91, 1, 1, 6, 254.84, 171.41, 1, 1, 6, 276.1, 171.51, 1, 1, 6, 284.59, 161.18, 1, 1, 6, 264.09, 158.54, 1, 1, 6, 260.24, 144.65, 1, 1, 6, 279.38, 152.31, 1, 1, 6, 296.75, 146.39, 1, 2, 6, 330.72, 112.33, 0.912, 7, 97.61, 151.88, 0.088, 2, 6, 312.47, 116.64, 0.864, 7, 79.69, 157.45, 0.136, 2, 6, 295.17, 115.46, 0.776, 7, 62.35, 157.47, 0.224, 2, 6, 287.16, 99.98, 0.568, 7, 53.29, 142.58, 0.432, 2, 6, 306.96, 92.59, 0.232, 7, 72.53, 133.83, 0.768, 2, 6, 329.4, 105.85, 0.16, 7, 95.84, 145.51, 0.84, 2, 6, 332.91, 88.27, 0.16, 7, 98.12, 127.73, 0.84, 2, 6, 349.55, 81.7, 0.16, 7, 114.26, 120.01, 0.84, 1, 6, 122.09, -141.99, 1, 1, 6, 113.75, -35.67, 1, 1, 6, 101.19, 94.83, 1, 1, 6, 71.72, 159.7, 1, 1, 6, 171.67, 18.48, 1, 1, 6, 155.65, 72.48, 1, 1, 6, 116.46, 155.31, 1, 1, 6, 186.74, -46.75, 1, 1, 6, 218.17, -125.2, 1, 1, 6, 241.62, -195.03, 1, 1, 6, 192.74, 160.98, 1, 2, 6, 183.12, 69.2, 0.808, 7, -52.64, 119.11, 0.192, 2, 6, 202.39, 8.07, 0.808, 7, -37.66, 56.79, 0.192, 2, 6, 235.19, -58.76, 0.808, 7, -9.58, -12.17, 0.192, 2, 6, 256.11, -117.93, 0.808, 7, 7.18, -72.64, 0.192, 2, 6, 263, -187.45, 0.96, 7, 9.22, -142.48, 0.04, 2, 6, 295.12, -236.69, 0.96, 7, 37.85, -193.82, 0.04, 2, 6, 230.24, 131.7, 0.776, 7, -1.28, 178.18, 0.224, 2, 6, 232.52, 64.48, 0.568, 7, -3.69, 110.97, 0.432, 2, 6, 245.57, -43.48, 0.568, 7, 1.84, 2.36, 0.432, 2, 6, 284.54, -113.7, 0.568, 7, 35.84, -70.4, 0.432, 2, 6, 255.38, 97.52, 0.568, 7, 21.41, 142.34, 0.432], "hull": 35, "edges": [28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 16, 18, 16, 14, 14, 12, 12, 70, 70, 72, 72, 22, 22, 74, 74, 76, 28, 30, 76, 30, 76, 26, 74, 24, 70, 16, 72, 18, 22, 78, 78, 80, 80, 82, 82, 32, 32, 30, 82, 76, 80, 74, 78, 84, 84, 86, 86, 88, 88, 8, 8, 10, 10, 12, 88, 12, 86, 70, 84, 72, 32, 34, 34, 90, 90, 92, 92, 80, 90, 82, 92, 94, 94, 78, 94, 96, 96, 98, 98, 100, 100, 102, 102, 8, 100, 88, 98, 86, 96, 84, 34, 36, 104, 106, 106, 108, 108, 110, 110, 4, 4, 6, 6, 102, 4, 2, 2, 0, 0, 68, 68, 66, 66, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 42, 40, 36, 38, 38, 104, 40, 38, 66, 64, 64, 62, 44, 42, 44, 46, 46, 48, 48, 50, 50, 52, 46, 40, 6, 8, 60, 112, 112, 106], "width": 538, "height": 372}}, "jj_15": {"jj_15": {"type": "mesh", "uvs": [0.92268, 0.25841, 0.89223, 0.42141, 0.86343, 0.57562, 0.83286, 0.73123, 0.84159, 0.88685, 0.67129, 0.8974, 0.42239, 1, 0.14247, 1, 1e-05, 0.93312, 0.0419, 0.82156, 0.05123, 0.68376, 0.049, 0.54093, 0.04686, 0.40418, 0.04763, 0.23244, 0.17233, 0.19867, 0.43463, 0.13374, 0.71628, 0.04543, 0.93128, 0, 0.18385, 0.38324, 0.18385, 0.68673, 0.43331, 0.61518, 0.68003, 0.5466, 0.67784, 0.71277, 0.43331, 0.74706, 0.16386, 0.83805, 0.39593, 0.34412, 0.70553, 0.26101, 0.69364, 0.39417, 0.41326, 0.46976, 0.18385, 0.53115], "triangles": [27, 26, 1, 28, 25, 27, 11, 12, 29, 28, 29, 18, 28, 18, 25, 12, 18, 29, 1, 26, 0, 12, 13, 18, 27, 25, 26, 13, 14, 18, 18, 14, 25, 25, 15, 26, 25, 14, 15, 26, 16, 0, 26, 15, 16, 0, 16, 17, 19, 10, 11, 29, 19, 11, 19, 29, 20, 24, 10, 19, 19, 20, 23, 29, 28, 20, 24, 19, 23, 7, 24, 6, 6, 24, 23, 8, 9, 7, 7, 9, 24, 9, 10, 24, 2, 3, 21, 1, 2, 27, 2, 21, 27, 21, 28, 27, 22, 20, 21, 20, 28, 21, 6, 23, 5, 5, 3, 4, 5, 22, 3, 5, 23, 22, 23, 20, 22, 3, 22, 21], "vertices": [4, 27, -46.29, 38.13, 0.01563, 26, 2.31, 32, 0.29101, 28, -15.56, 133.74, 0.01586, 25, -81.25, 8.9, 0.6775, 4, 27, -21.71, 34.18, 0.10063, 26, 27.18, 30.82, 0.51953, 28, 8.38, 126.89, 0.04172, 25, -71.54, 31.82, 0.33812, 5, 27, 1.55, 30.44, 0.32279, 26, 50.71, 29.71, 0.51953, 29, -32.12, 116.74, 0.01534, 28, 31.02, 120.41, 0.04172, 25, -62.35, 53.51, 0.10063, 5, 27, 25.03, 26.39, 0.61087, 26, 74.5, 28.32, 0.31185, 29, -8.48, 113.74, 0.04579, 28, 53.86, 113.6, 0.01586, 25, -52.81, 75.35, 0.01563, 3, 27, 48.08, 29.41, 0.77638, 26, 97.06, 33.9, 0.14444, 29, 14.4, 117.77, 0.07918, 4, 27, 51.56, -1.09, 0.68182, 26, 103.94, 3.98, 0.10247, 29, 19.23, 87.46, 0.19984, 28, 76.93, 83.16, 0.01586, 4, 27, 69.61, -44.85, 0.45412, 26, 126.78, -37.48, 0.05426, 29, 39.21, 44.54, 0.43838, 28, 89.65, 37.56, 0.05324, 4, 27, 72.75, -95.14, 0.21161, 26, 135.53, -87.1, 0.01664, 29, 44.57, -5.56, 0.67005, 28, 86.79, -12.74, 0.10169, 3, 27, 64.4, -121.35, 0.0861, 29, 37.4, -32.12, 0.76946, 28, 75.39, -37.78, 0.14444, 5, 27, 47.34, -114.86, 0.05004, 26, 112.5, -109.55, 0.01664, 29, 20.07, -26.39, 0.60663, 28, 59.22, -29.31, 0.31107, 25, 89.69, 63.28, 0.01563, 5, 27, 26.75, -114.47, 0.01716, 26, 91.99, -111.46, 0.04328, 29, -0.53, -26.91, 0.32097, 28, 38.82, -26.46, 0.51797, 25, 84.38, 43.37, 0.10063, 4, 26, 71.1, -115.56, 0.04328, 29, -21.65, -29.58, 0.10063, 28, 17.55, -25.66, 0.51797, 25, 80.99, 22.36, 0.33812, 4, 26, 51.1, -119.48, 0.01664, 29, -41.87, -32.13, 0.01563, 28, -2.82, -24.89, 0.29023, 25, 77.75, 2.24, 0.6775, 1, 25, 73.06, -22.92, 1, 1, 25, 50.08, -23.88, 1, 1, 25, 1.89, -25, 1, 1, 25, -50.33, -28.94, 1, 1, 25, -89.62, -28.71, 1, 4, 26, 43.74, -95.74, 0.06086, 29, -47.59, -7.94, 0.008, 28, -4.53, -0.09, 0.24714, 25, 52.93, 3.55, 0.684, 5, 27, 25.7, -90.62, 0.06307, 26, 88.27, -87.88, 0.13075, 29, -2.63, -3.13, 0.26093, 28, 40.61, -2.66, 0.45875, 25, 60.97, 48.05, 0.0865, 5, 27, 12.26, -46.46, 0.16547, 26, 69.97, -45.51, 0.3004, 29, -18.01, 40.39, 0.15853, 28, 32.52, 42.78, 0.2956, 25, 14.89, 45.55, 0.08, 5, 27, -0.71, -2.78, 0.26507, 26, 52.19, -3.55, 0.46225, 29, -32.91, 83.45, 0.05893, 28, 24.84, 87.7, 0.12725, 25, -30.63, 43.39, 0.0865, 5, 27, 24.03, -1.63, 0.53244, 26, 76.64, 0.36, 0.25886, 29, -8.25, 85.7, 0.14156, 28, 49.53, 85.9, 0.05914, 25, -25.84, 67.68, 0.008, 5, 27, 31.87, -45.24, 0.34322, 26, 89.32, -42.1, 0.16046, 29, 1.52, 42.48, 0.33078, 28, 52.14, 41.66, 0.15754, 25, 18.38, 64.88, 0.008, 5, 27, 48.43, -92.8, 0.15094, 26, 111.1, -87.5, 0.06086, 29, 20.17, -4.3, 0.52306, 28, 62.92, -7.53, 0.25714, 25, 68.52, 69.6, 0.008, 5, 27, -27.63, -55.7, 0.00422, 26, 31.37, -59.16, 0.15534, 29, -57.46, 29.39, 0.00378, 28, -8.19, 38.35, 0.15266, 25, 14.32, 4.61, 0.684, 4, 27, -43.47, -0.85, 0.008, 26, 9.49, -6.43, 0.24886, 28, -17.39, 94.69, 0.05914, 25, -42.72, 2.33, 0.684, 5, 27, -23.53, -1.75, 0.07517, 26, 29.4, -5.09, 0.46225, 29, -55.75, 83.47, 0.01133, 28, 2.3, 91.43, 0.12725, 25, -37.08, 21.47, 0.324, 5, 27, -9.14, -51.42, 0.0409, 26, 49.26, -52.83, 0.3004, 29, -39.17, 34.49, 0.0391, 28, 10.68, 40.4, 0.2956, 25, 14.58, 23.58, 0.324, 5, 27, 2.56, -92.06, 0.01267, 26, 65.44, -91.91, 0.13075, 29, -25.68, -5.6, 0.07383, 28, 17.47, -1.34, 0.45875, 25, 56.85, 25.24, 0.324], "hull": 18, "edges": [14, 16, 24, 36, 20, 38, 38, 40, 40, 42, 42, 4, 4, 6, 6, 8, 8, 10, 10, 44, 44, 42, 6, 44, 44, 46, 12, 14, 46, 12, 12, 10, 40, 46, 14, 48, 48, 38, 46, 48, 16, 18, 18, 20, 48, 18, 36, 50, 50, 52, 52, 0, 0, 34, 34, 32, 32, 52, 32, 30, 30, 28, 28, 26, 26, 24, 28, 36, 30, 50, 0, 2, 2, 4, 42, 54, 54, 52, 2, 54, 40, 56, 56, 50, 54, 56, 36, 58, 58, 38, 56, 58, 20, 22, 22, 24, 58, 22], "width": 180, "height": 149}}, "jj_16": {"jj_16": {"type": "mesh", "uvs": [0.28094, 0.05248, 0.4435, 0.10252, 0.58839, 0.12754, 0.78644, 0.16368, 1, 0.19704, 0.95783, 0.3555, 0.95783, 0.55844, 1, 0.77806, 0.91719, 0.9254, 0.78114, 0.95042, 0.55497, 0.86702, 0.57264, 0.6335, 0.57248, 0.4389, 0.54318, 0.63252, 0.51974, 0.93063, 0.29706, 0.92449, 0.14079, 0.83844, 0, 0.62023, 0.08023, 0.35592, 0.13297, 0.08854, 0.15195, 0, 0.25604, 0.18074, 0.2365, 0.50037, 0.42509, 0.31866, 0.40357, 0.57236, 0.76877, 0.36384, 0.7723, 0.62794], "triangles": [26, 11, 12, 26, 25, 6, 22, 21, 23, 1, 21, 0, 12, 25, 26, 24, 23, 12, 25, 5, 6, 12, 23, 2, 12, 2, 25, 2, 23, 1, 25, 3, 5, 25, 2, 3, 5, 3, 4, 23, 21, 1, 18, 19, 21, 21, 19, 0, 0, 19, 20, 22, 18, 21, 24, 22, 23, 13, 24, 12, 9, 10, 26, 9, 26, 8, 7, 8, 26, 10, 11, 26, 7, 26, 6, 13, 14, 24, 17, 18, 22, 15, 24, 14, 16, 22, 15, 16, 17, 22, 15, 22, 24], "vertices": [1, 30, -70.74, 21.3, 1, 1, 30, -31.7, 19.42, 1, 1, 30, 2.68, 20.65, 1, 1, 30, 49.71, 22.03, 1, 1, 30, 100.31, 24.36, 1, 2, 34, -43.21, 55.41, 0.208, 30, 93.89, -0.6, 0.792, 3, 34, -12.8, 56.85, 0.32102, 33, 34.78, 57.97, 0.38298, 30, 98.29, -30.72, 0.296, 3, 34, 19.63, 68.35, 0.6976, 33, 68.34, 65.55, 0.1744, 30, 112.9, -61.88, 0.128, 3, 34, 42.63, 49.88, 0.6976, 33, 89, 44.48, 0.1744, 30, 96.75, -86.57, 0.128, 3, 34, 47.9, 17.98, 0.6976, 33, 90.45, 12.18, 0.1744, 30, 65.52, -94.92, 0.128, 3, 34, 37.93, -35.93, 0.6976, 33, 74.17, -40.17, 0.1744, 30, 10.9, -90.25, 0.128, 3, 34, 2.75, -33.42, 0.32102, 33, 39.53, -33.51, 0.38298, 30, 9.96, -54.99, 0.296, 3, 32, -39.73, 67.78, 0.16474, 34, -26.41, -34.84, 0.208, 30, 5.71, -26.11, 0.62726, 3, 32, -10.36, 73.11, 0.35482, 31, 58.21, 73.11, 0.34918, 30, 3.07, -55.85, 0.296, 3, 32, 32.81, 86.01, 0.62086, 31, 103.27, 73.51, 0.25114, 30, 4.05, -100.9, 0.128, 3, 32, 53.08, 37.52, 0.62086, 31, 109.27, 21.29, 0.25114, 30, -48.08, -107.57, 0.128, 3, 32, 56.08, -1.44, 0.62086, 31, 101.32, -16.97, 0.25114, 30, -86.44, -100.13, 0.128, 3, 32, 39.46, -45.02, 0.62086, 31, 73.24, -54.21, 0.25114, 30, -124.05, -72.54, 0.128, 3, 32, -4.45, -43.61, 0.35482, 31, 31.45, -40.65, 0.34918, 30, -111.04, -30.58, 0.296, 2, 32, -46.18, -48.32, 0.208, 30, -104.52, 10.91, 0.792, 1, 30, -102, 24.7, 1, 2, 32, -45.19, -16.17, 0.208, 30, -73.78, 1.42, 0.792, 3, 32, 0.57, -1.13, 0.35482, 31, 48.08, -1.24, 0.34918, 30, -71.42, -46.69, 0.296, 2, 32, -42.27, 28.68, 0.208, 30, -31.31, -13.29, 0.792, 3, 32, -5.38, 39.31, 0.35482, 31, 53.6, 39.26, 0.34918, 30, -30.84, -51.68, 0.296, 2, 34, -39.85, 10.9, 0.208, 30, 49.92, -8.28, 0.792, 3, 34, -0.32, 13.61, 0.32102, 33, 42.05, 13.55, 0.38298, 30, 56.47, -47.36, 0.296], "hull": 21, "edges": [38, 42, 42, 44, 44, 32, 32, 34, 34, 36, 36, 38, 36, 44, 26, 24, 26, 28, 28, 30, 30, 32, 42, 46, 46, 24, 44, 48, 48, 26, 46, 48, 48, 30, 24, 4, 4, 2, 2, 0, 0, 40, 40, 38, 0, 42, 2, 46, 4, 6, 6, 8, 8, 10, 10, 50, 50, 24, 6, 50, 50, 52, 52, 22, 22, 24, 52, 12, 12, 10, 52, 18, 18, 20, 20, 22, 12, 14, 14, 16, 16, 18], "width": 236, "height": 150}}, "dabj": {"dabj": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [804.41, -67.07, -697.49, -49.74, -680.16, 1452.16, 821.74, 1434.83], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 405, "height": 405}}, "jj_18": {"jj_18": {"type": "mesh", "uvs": [1, 0.10609, 1, 0.35211, 0.80671, 0.4927, 0.63222, 0.57577, 0.45519, 0.73872, 0.2554, 0.87931, 0.08343, 1, 0, 0.63009, 0, 0.15082, 0.14919, 0.17638, 0.26805, 0.11567, 0.49059, 0.05177, 0.7187, 0, 1, 0, 0.75361, 0.15401, 0.31104, 0.38406, 0.54623, 0.31058, 0.12895, 0.45436], "triangles": [2, 14, 1, 14, 0, 1, 0, 14, 13, 16, 14, 2, 14, 12, 13, 14, 11, 12, 3, 4, 15, 3, 16, 2, 3, 15, 16, 16, 10, 11, 14, 16, 11, 16, 15, 10, 5, 15, 4, 9, 10, 15, 6, 7, 5, 7, 17, 5, 5, 17, 15, 7, 8, 17, 17, 9, 15, 17, 8, 9], "vertices": [1, 37, 36.62, -39.2, 1, 1, 37, -11.81, -53.29, 1, 3, 42, -100, 100.36, 0.01563, 41, -11.56, 77.69, 0.30937, 37, -53.47, -13.28, 0.675, 3, 42, -51.71, 101.37, 0.10063, 41, 35.49, 88.6, 0.55875, 37, -82.45, 25.36, 0.34062, 3, 42, 2.64, 117.61, 0.34062, 41, 85.35, 115.65, 0.55875, 37, -127.33, 60.05, 0.10063, 3, 42, 61.03, 127.57, 0.65417, 41, 140.45, 137.38, 0.33021, 37, -169.46, 101.68, 0.01563, 2, 42, 111.26, 136.08, 0.85556, 41, 187.87, 156.02, 0.14444, 2, 42, 106.4, 57.38, 0.87083, 41, 199.27, 78, 0.12917, 2, 42, 73.7, -35.27, 0.85556, 41, 186.29, -19.39, 0.14444, 3, 42, 39.01, -17.47, 0.65417, 41, 148.68, -9.09, 0.33021, 37, -38.79, 168.36, 0.01563, 3, 42, 5.84, -18.96, 0.34062, 41, 116.52, -17.36, 0.55875, 37, -18.24, 142.27, 0.10063, 3, 42, -52.88, -12.13, 0.10063, 41, 57.65, -22.73, 0.55875, 37, 10.45, 90.59, 0.34062, 3, 42, -112.12, -2.48, 0.01563, 41, -2.31, -25.44, 0.30937, 37, 37.14, 36.83, 0.675, 1, 37, 57.5, -33.13, 1, 3, 42, -110.14, 30.31, 0.008, 41, -7.1, 7.05, 0.312, 37, 9.36, 19.33, 0.68, 3, 42, 13.65, 36.63, 0.328, 41, 112.75, 38.65, 0.579, 37, -67.95, 116.21, 0.093, 3, 42, -48.81, 42.7, 0.093, 41, 50.38, 31.77, 0.579, 37, -36.47, 61.93, 0.328, 3, 42, 62.92, 34.52, 0.67, 41, 161.4, 46.7, 0.322, 37, -94.97, 157.46, 0.008], "hull": 14, "edges": [24, 26, 24, 28, 28, 4, 4, 2, 2, 0, 0, 26, 28, 0, 24, 22, 22, 20, 20, 30, 30, 8, 8, 6, 6, 4, 28, 32, 32, 30, 6, 32, 32, 22, 20, 18, 18, 16, 18, 34, 34, 10, 10, 12, 14, 16, 12, 14, 14, 34, 34, 30, 8, 10], "width": 259, "height": 205}}, "jj_19": {"jj_19": {"type": "mesh", "uvs": [0.58671, 0.13714, 1, 0.16511, 1, 0.49609, 0.9172, 1, 0.41135, 0.86437, 0.08086, 0.57068, 0, 0.24436, 0, 0, 0.09097, 0, 0.51252, 0.49609], "triangles": [4, 9, 3, 3, 9, 2, 4, 5, 9, 5, 6, 9, 9, 0, 2, 0, 1, 2, 6, 8, 9, 9, 8, 0, 6, 7, 8], "vertices": [2, 36, 77.1, -15.85, 0.8, 35, 159.98, -13.38, 0.2, 2, 36, 39.08, -7.64, 0.664, 35, 121.72, -6.38, 0.336, 2, 36, 42.75, 14.57, 0.664, 35, 124.68, 15.93, 0.336, 2, 36, 56.02, 47.11, 0.664, 35, 136.9, 48.87, 0.336, 2, 36, 101.43, 30.24, 0.8, 35, 182.83, 33.47, 0.2, 2, 36, 128.82, 5.47, 0.864, 35, 210.99, 9.59, 0.136, 2, 36, 132.7, -17.66, 0.864, 35, 215.61, -13.41, 0.136, 2, 36, 129.98, -34.06, 0.864, 35, 213.42, -29.88, 0.136, 2, 36, 121.55, -32.66, 0.864, 35, 204.95, -28.75, 0.136, 2, 36, 87.96, 7.09, 0.8, 35, 170.1, 9.9, 0.2], "hull": 9, "edges": [12, 14, 12, 18, 18, 4, 4, 6, 6, 8, 8, 10, 10, 12, 14, 16, 16, 0, 0, 18, 18, 8, 4, 2, 0, 2], "width": 94, "height": 68}}, "yj_6": {"yj_6": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-4.45, -47.63, -6.13, 47.35, 99.86, 49.23, 101.54, -45.76], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 95, "height": 106}}, "yj_7": {"yj_7": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [113.06, -107.54, 23.21, -31.05, 115.26, 77.08, 205.11, 0.58], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 118, "height": 142}}, "yj_8": {"yj_8": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [62.91, -127.88, -79.48, -6.66, 6.09, 93.85, 148.48, -27.37], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 187, "height": 132}}, "yj_9": {"yj_9": {"type": "mesh", "uvs": [0.5869, 0.19291, 0.77215, 0.35556, 0.73529, 0.46493, 0.70415, 0.55731, 0.70051, 0.59315, 0.75097, 0.49409, 0.88359, 0.38641, 1, 0.46699, 1, 0.50313, 0.86752, 0.56187, 0.7952, 0.69967, 0.66242, 0.78734, 0.64082, 0.86426, 0.55922, 0.95231, 0.43442, 1, 0.30482, 1, 0.21002, 0.95635, 0.15242, 0.89057, 0.22442, 0.79139, 0.16682, 0.63958, 0, 0.48778, 0.08642, 0.33395, 0.25322, 0.51409, 0.45842, 0.4139, 0.51002, 0.54546, 0.55708, 0.61589, 0.54994, 0.56653, 0.54932, 0.47763, 0.54872, 0.39141, 0.40233, 0.24821, 0.40233, 0.01984, 0.49583, 0, 0.60026, 0, 0.64476, 0.66297, 0.63494, 0.59608, 0.64587, 0.37605, 0.50433, 0.2093, 0.64144, 0.45684], "triangles": [30, 31, 36, 0, 31, 32, 36, 0, 35, 29, 36, 28, 29, 30, 36, 36, 31, 0, 27, 28, 37, 37, 35, 2, 2, 35, 1, 37, 28, 35, 28, 36, 35, 35, 0, 1, 26, 27, 37, 2, 3, 37, 11, 25, 33, 11, 33, 10, 25, 34, 33, 25, 26, 34, 13, 14, 18, 18, 14, 15, 15, 16, 18, 16, 17, 18, 13, 18, 12, 12, 18, 11, 25, 11, 18, 25, 19, 24, 24, 19, 22, 19, 25, 18, 19, 20, 22, 22, 23, 24, 20, 21, 22, 33, 4, 10, 10, 4, 9, 33, 34, 4, 4, 34, 3, 9, 4, 5, 37, 3, 34, 34, 26, 37, 7, 8, 9, 9, 5, 6, 9, 6, 7], "vertices": [3, 78, 124.16, -24, 0.00397, 79, 67.72, 6.43, 0.31575, 80, 27.79, -2.49, 0.68028, 3, 78, 96.35, -49.07, 0.06601, 79, 40.72, -19.51, 0.58999, 80, -6.03, -18.55, 0.344, 3, 78, 78.37, -43.34, 0.29158, 79, 22.57, -14.35, 0.56443, 80, -21.62, -7.92, 0.144, 2, 78, 63.18, -38.5, 0.6625, 79, 7.23, -9.99, 0.3375, 2, 78, 57.25, -37.8, 0.92, 79, 1.28, -9.48, 0.08, 2, 78, 73.46, -45.38, 0.98333, 79, 17.73, -16.54, 0.01667, 1, 78, 90.74, -64.5, 1, 1, 78, 76.86, -80.37, 1, 1, 78, 70.86, -80.18, 1, 1, 78, 61.7, -61.33, 1, 2, 78, 39.16, -50.49, 0.9875, 79, -16.4, -22.73, 0.0125, 1, 78, 25.2, -31.45, 1, 1, 78, 12.53, -28.03, 1, 1, 78, -1.71, -16.15, 1, 1, 78, -9.08, 1.57, 1, 1, 78, -8.5, 19.7, 1, 1, 78, -0.84, 32.74, 1, 1, 78, 10.33, 40.45, 1, 1, 78, 26.46, 29.86, 1, 1, 78, 51.91, 37.12, 1, 1, 78, 77.83, 59.67, 1, 1, 78, 102.97, 46.77, 1, 1, 78, 72.35, 24.38, 1, 1, 78, 88.06, -4.86, 1, 2, 78, 66.01, -11.39, 0.97917, 79, 9.2, 17.19, 0.02083, 2, 78, 54.11, -17.61, 0.89688, 79, -2.49, 10.6, 0.10313, 2, 78, 62.33, -16.87, 0.65938, 79, 5.7, 11.6, 0.34062, 3, 78, 77.09, -17.25, 0.29158, 79, 20.46, 11.69, 0.56443, 80, -15.38, 17.45, 0.144, 3, 78, 91.39, -17.62, 0.06601, 79, 34.77, 11.77, 0.58999, 80, -1.78, 13, 0.344, 3, 78, 115.8, 2.12, 0.00397, 79, 58.54, 32.27, 0.31575, 80, 27.25, 24.93, 0.68028, 2, 79, 96.45, 32.27, 0.096, 80, 63.22, 12.94, 0.904, 2, 79, 99.75, 19.18, 0.096, 80, 62.2, -0.52, 0.904, 2, 79, 99.75, 4.56, 0.096, 80, 57.58, -14.39, 0.904, 2, 78, 45.91, -29.63, 0.88437, 79, -10.31, -1.67, 0.11563, 2, 78, 57.06, -28.61, 0.67, 79, 0.8, -0.3, 0.33, 3, 78, 93.51, -31.29, 0.06101, 79, 37.32, -1.83, 0.59499, 80, -3.66, -0.71, 0.344, 3, 78, 121.81, -12.36, 0.00203, 79, 65, 17.99, 0.31782, 80, 28.87, 9.34, 0.68014, 3, 78, 80.13, -30.25, 0.28077, 79, 23.91, -1.21, 0.57523, 80, -16.19, 4.12, 0.144], "hull": 33, "edges": [50, 66, 66, 8, 8, 6, 6, 68, 68, 52, 52, 50, 68, 66, 70, 56, 70, 2, 70, 72, 72, 62, 62, 60, 60, 58, 58, 56, 58, 72, 72, 0, 62, 64, 0, 64, 0, 2, 52, 54, 54, 56, 68, 74, 74, 70, 54, 74, 6, 4, 4, 2, 74, 4, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 8, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50], "width": 140, "height": 166}}, "yj_10": {"yj_10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [57.9, -68.63, 14.52, -25.73, 77.1, 37.55, 120.48, -5.34], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 61, "height": 89}}, "yj_11": {"yj_11": {"type": "mesh", "uvs": [0.93294, 0.07373, 0.96726, 0.23411, 1, 0.28725, 1, 0.49934, 0.974, 0.65661, 0.92137, 0.79721, 0.89708, 1, 0.8286, 0.94928, 0.76484, 0.90206, 0.63607, 0.94055, 0.52811, 0.78328, 0.45641, 0.7043, 0.36645, 0.73154, 0.29191, 0.71565, 0.21866, 0.70657, 0.16479, 0.73895, 0.11082, 0.79115, 0.01103, 0.72862, 0, 0.61466, 0.01989, 0.50241, 0.05789, 0.52403, 0.03989, 0.5926, 0.06146, 0.63576, 0.09426, 0.63126, 0.13055, 0.56344, 0.09161, 0.46267, 0.05026, 0.29634, 0.1031, 0.21318, 0.11229, 0.35111, 0.17776, 0.36733, 0.26988, 0.2149, 0.33579, 0.21543, 0.3061, 0.10217, 0.35455, 0, 0.41069, 0, 0.36686, 0.08451, 0.37993, 0.13476, 0.47857, 0.10501, 0.57445, 0, 0.67806, 0, 0.77547, 0.03714, 0.84252, 0.10651, 0.91985, 0.01433, 0.83734, 0.78536, 0.93352, 0.36827, 0.88316, 0.52958, 0.67384, 0.72847, 0.57399, 0.61885, 0.73187, 0.46158, 0.80743, 0.20183, 0.62286, 0.13844, 0.5958, 0.38501, 0.48597, 0.57948, 0.40243, 0.5409, 0.30091, 0.52501, 0.20709, 0.54998, 0.50468, 0.37905, 0.42625, 0.38828, 0.30525, 0.36125, 0.2334, 0.36468, 0.71306, 0.16758, 0.55034, 0.12164, 0.43896, 0.22648, 0.52179, 0.22897, 0.61112, 0.24543, 0.69399, 0.28151, 0.75534, 0.3809, 0.90567, 0.15442, 0.86862, 0.2826, 0.81447, 0.44223, 0.81063, 0.52144, 0.76665, 0.74682], "triangles": [55, 29, 59, 24, 25, 29, 24, 29, 55, 57, 31, 62, 29, 25, 28, 30, 59, 29, 58, 31, 57, 31, 36, 62, 36, 32, 35, 36, 31, 32, 32, 33, 35, 35, 33, 34, 58, 30, 31, 58, 59, 30, 25, 26, 28, 26, 27, 28, 62, 36, 37, 56, 62, 63, 63, 61, 64, 62, 37, 63, 63, 37, 61, 61, 38, 50, 61, 37, 38, 56, 63, 51, 57, 62, 56, 48, 65, 66, 51, 63, 64, 64, 61, 50, 51, 64, 65, 49, 65, 60, 64, 50, 65, 65, 50, 60, 60, 40, 49, 50, 39, 60, 60, 39, 40, 50, 38, 39, 48, 66, 69, 69, 66, 68, 49, 40, 41, 49, 41, 68, 68, 41, 67, 67, 0, 1, 67, 41, 0, 41, 42, 0, 66, 49, 68, 68, 67, 1, 66, 65, 49, 17, 22, 16, 22, 18, 21, 22, 17, 18, 18, 19, 21, 21, 19, 20, 22, 23, 16, 16, 23, 15, 23, 24, 15, 15, 24, 55, 53, 54, 58, 54, 13, 55, 13, 14, 55, 55, 59, 54, 54, 59, 58, 15, 55, 14, 12, 13, 54, 57, 53, 58, 11, 12, 53, 53, 12, 54, 11, 52, 10, 11, 53, 52, 52, 56, 47, 53, 57, 52, 52, 57, 56, 47, 51, 48, 9, 46, 8, 9, 10, 46, 10, 47, 46, 46, 47, 48, 10, 52, 47, 71, 46, 48, 70, 48, 69, 47, 56, 51, 51, 65, 48, 8, 71, 43, 4, 45, 3, 45, 44, 3, 45, 69, 44, 44, 2, 3, 44, 1, 2, 6, 7, 5, 5, 7, 43, 5, 43, 45, 7, 8, 43, 4, 5, 45, 43, 70, 45, 44, 68, 1, 71, 70, 43, 8, 46, 71, 70, 69, 45, 70, 71, 48, 69, 68, 44], "vertices": [4, 74, 238.09, 104.12, 0.29146, 83, 26.13, -217.21, 0.0052, 88, 50.79, -66.57, 0.67667, 89, -53.2, -45.51, 0.02667, 4, 74, 207.09, 67.02, 0.54717, 83, -11.61, -186.98, 0.01525, 88, 7.5, -45.03, 0.42958, 89, -78.91, -4.56, 0.008, 1, 74, 202.04, 45.46, 1, 1, 74, 149.95, 16.08, 1, 1, 74, 104.95, 5.58, 1, 1, 74, 57.54, 8.93, 1, 1, 74, 1.79, -8.62, 1, 3, 74, -2.5, 28.11, 0.86021, 83, -54.88, 21.75, 0.12099, 88, -79.02, 149.79, 0.0188, 4, 74, -6.5, 62.32, 0.61937, 83, -20.77, 26.46, 0.329, 84, -103.33, 66.07, 0.01563, 88, -46.68, 161.63, 0.036, 5, 74, -47.45, 112.84, 0.30562, 83, 28.9, 68.45, 0.53603, 84, -42.25, 88.4, 0.10063, 88, -7.05, 213.19, 0.0336, 89, 43.28, 223.38, 0.02412, 7, 74, -35.22, 181.46, 0.10063, 83, 97.75, 57.66, 0.51901, 84, 18.69, 54.56, 0.30664, 85, -56.2, 65.88, 0.01563, 88, 62.53, 217.24, 0.0156, 89, 104.77, 190.56, 0.02664, 90, 40.23, 186.73, 0.01586, 6, 74, -33.36, 223.5, 0.01563, 83, 139.82, 56.68, 0.30586, 84, 57.84, 39.16, 0.51953, 85, -20.49, 43.61, 0.10063, 89, 144.18, 175.81, 0.01664, 90, 77.13, 166.51, 0.04172, 6, 83, 174.47, 86.1, 0.10063, 84, 100.5, 54.85, 0.51953, 85, 24.31, 51.29, 0.30612, 86, -40.73, 81.3, 0.01563, 90, 121.43, 176.7, 0.04172, 91, 77.51, 164.76, 0.01638, 6, 83, 208.7, 101.15, 0.01563, 84, 137.82, 57.2, 0.30664, 85, 61.43, 46.81, 0.51849, 86, -10.46, 59.36, 0.10063, 90, 158.74, 174.31, 0.01586, 91, 113.65, 155.16, 0.04276, 5, 84, 174.16, 61.31, 0.10063, 85, 97.91, 44.25, 0.51474, 86, 20.19, 39.4, 0.29439, 87, -60.27, -0.29, 0.01563, 91, 149.42, 147.55, 0.07462, 5, 84, 198.87, 75.16, 0.01563, 85, 124.73, 53.38, 0.29224, 86, 48.07, 34.36, 0.50669, 87, -35.94, 14.24, 0.10417, 91, 177.25, 152.86, 0.08128, 4, 85, 151.61, 68.1, 0.09896, 86, 78.71, 34.17, 0.52696, 87, -12.73, 34.24, 0.34375, 91, 205.92, 163.7, 0.03033, 3, 85, 201.31, 50.46, 0.01563, 86, 113.59, -5.38, 0.32813, 87, 39.56, 27.38, 0.65625, 2, 86, 102.79, -36.14, 0.10938, 87, 51.64, -2.91, 0.89063, 2, 86, 78.75, -59, 0.02083, 87, 48.56, -35.93, 0.97917, 2, 86, 65.17, -44.48, 0.02083, 87, 28.78, -33.92, 0.97917, 2, 86, 82.4, -31.93, 0.10938, 87, 33.51, -13.14, 0.89063, 4, 85, 176.19, 24.28, 0.01042, 86, 78.92, -16.07, 0.31817, 87, 20.46, -3.48, 0.65625, 91, 224.16, 116.88, 0.01517, 4, 85, 159.86, 23.01, 0.07667, 86, 64.02, -9.25, 0.50981, 87, 4.75, -8.13, 0.33854, 91, 207.81, 117.9, 0.07498, 5, 84, 224.64, 29.59, 0.00667, 85, 141.79, 3.88, 0.21298, 86, 38.94, -17.19, 0.42714, 87, -8.93, -30.61, 0.06944, 91, 187.25, 101.48, 0.28377, 4, 85, 161.18, -24.53, 0.06724, 86, 42.09, -51.45, 0.40375, 87, 15.97, -54.35, 0.01042, 91, 202.5, 70.64, 0.51859, 3, 85, 181.77, -71.44, 0.01389, 86, 37.31, -102.45, 0.34881, 91, 216.37, 21.32, 0.6373, 3, 85, 155.46, -94.89, 0.00827, 86, 2.92, -110.17, 0.32533, 91, 187.05, 1.76, 0.66641, 3, 85, 150.88, -55.99, 0.05603, 86, 17.81, -73.95, 0.30829, 91, 187.93, 40.91, 0.63568, 6, 84, 211.58, -29.06, 0.0041, 85, 118.28, -51.42, 0.18097, 86, -8.47, -54.12, 0.27626, 87, -20.38, -89.6, 0.00833, 90, 221.03, 79.44, 0.01224, 91, 156.28, 49.98, 0.5181, 5, 84, 174.28, -79.67, 0.01365, 85, 72.4, -94.4, 0.1946, 86, -69.45, -69.41, 0.14196, 90, 177.64, 33.94, 0.06579, 91, 104.87, 13.8, 0.58399, 6, 84, 141.97, -85.49, 0.04958, 85, 39.58, -94.25, 0.11503, 86, -98.07, -53.34, 0.03047, 89, 230.37, 52.58, 0.00667, 90, 144.86, 32.25, 0.21213, 91, 72.38, 18.51, 0.58612, 5, 84, 162.32, -114.21, 0.00853, 85, 54.36, -126.2, 0.03038, 86, -100.66, -88.44, 0.00567, 90, 161.42, 1.19, 0.07654, 91, 82.58, -15.18, 0.87889, 3, 85, 30.23, -155.01, 0.00689, 90, 138.95, -28.94, 0.01389, 91, 54.68, -40.35, 0.97922, 2, 90, 111.04, -30.51, 0.02083, 91, 26.99, -36.46, 0.97917, 3, 85, 24.11, -131.17, 0.01033, 90, 131.49, -5.49, 0.10208, 91, 51.93, -15.9, 0.88758, 6, 84, 124.49, -111.85, 0.01365, 85, 17.6, -117, 0.03038, 86, -128.33, -62.55, 0.00567, 89, 213.33, 25.93, 0.0225, 90, 124.19, 8.3, 0.30704, 91, 47.45, -0.96, 0.62076, 5, 84, 77.72, -129.04, 0.03258, 85, -31.53, -125.39, 0.01141, 89, 166.85, 7.97, 0.144, 90, 75.62, -2.85, 0.56292, 91, -2.36, -2.43, 0.24909, 5, 84, 36.15, -166.83, 0.01024, 88, 195.82, 39.6, 0.01563, 89, 125.91, -30.51, 0.32937, 90, 29.62, -35.1, 0.56476, 91, -53.76, -25.11, 0.08, 5, 83, 145.96, -170.47, 0.00488, 88, 157.99, 4.51, 0.10375, 89, 75.34, -40.74, 0.55325, 90, -21.9, -38, 0.32563, 91, -104.86, -17.94, 0.0125, 5, 74, 208.56, 177.5, 0.01, 83, 98.88, -186.15, 0.0208, 88, 115.3, -20.8, 0.33795, 89, 25.71, -40.08, 0.53062, 90, -70.92, -30.27, 0.10063, 5, 74, 207.92, 138.8, 0.08267, 83, 60.18, -186.32, 0.02912, 88, 77.52, -29.16, 0.62738, 89, -10.89, -27.52, 0.24833, 90, -105.36, -12.62, 0.0125, 4, 74, 249.48, 118.03, 0.12861, 83, 40.28, -228.3, 0.00693, 88, 66.96, -74.41, 0.76557, 89, -43.49, -60.63, 0.09889, 3, 74, 39.9, 47.03, 0.81172, 83, -35.08, -20.25, 0.12097, 88, -50.77, 112.94, 0.06731, 4, 74, 165.88, 63.08, 0.63622, 83, -16.41, -145.87, 0.05847, 88, -5.9, -5.86, 0.29731, 89, -69.93, 35.85, 0.008, 4, 74, 113.94, 62.58, 0.64185, 83, -17.99, -93.95, 0.14139, 88, -18.45, 44.54, 0.20169, 89, -54.37, 85.41, 0.01507, 6, 74, 13.89, 125.83, 0.248, 83, 43.16, 7.4, 0.49569, 84, -49.87, 26.17, 0.07411, 88, 19.83, 156.55, 0.09563, 89, 36.7, 161.03, 0.07551, 90, -31.36, 167.22, 0.01106, 7, 74, 16.39, 184.33, 0.0625, 83, 101.7, 6.12, 0.47296, 84, 4.65, 4.82, 0.25686, 85, -79.04, 19.51, 0.008, 88, 77.31, 167.71, 0.0384, 89, 91.56, 140.59, 0.10214, 90, 20.02, 139.15, 0.05914, 6, 74, 93.64, 137.63, 0.12056, 83, 56.62, -72.09, 0.3661, 84, -64.6, -53.09, 0.03308, 88, 49.83, 81.72, 0.21436, 89, 23.28, 81.54, 0.23344, 90, -55.98, 90.45, 0.03247, 5, 74, 175.93, 140.82, 0.08, 83, 61.53, -154.29, 0.116, 88, 72.05, 2.43, 0.50992, 89, 0.91, 2.29, 0.28608, 90, -89.43, 15.2, 0.008, 6, 83, 149.74, -122.87, 0.04467, 84, 5.34, -132.83, 0.01229, 88, 151.6, 51.83, 0.08554, 89, 94.55, 2.98, 0.54579, 90, 3.35, 2.52, 0.30371, 91, -72.21, 16.9, 0.008, 8, 74, 79.17, 207.26, 0.028, 83, 125.93, -56.17, 0.32, 84, 5.95, -62.01, 0.15876, 85, -89.91, -46.43, 0.00397, 88, 114.19, 111.97, 0.08315, 89, 93.98, 73.8, 0.24819, 90, 12.89, 72.7, 0.15391, 91, -49.19, 83.88, 0.00403, 8, 74, 4.53, 227.97, 0.008, 83, 145.08, 18.89, 0.2559, 84, 49.77, 1.87, 0.46225, 85, -35.21, 8.41, 0.0744, 88, 117, 189.37, 0.01152, 89, 136.73, 138.39, 0.04858, 90, 64.41, 130.54, 0.12725, 91, 12.61, 130.58, 0.0121, 7, 83, 186.42, 30.71, 0.07421, 84, 92.65, -1.27, 0.46225, 85, 6.39, -2.47, 0.25571, 86, -82.51, 43.01, 0.008, 89, 179.66, 135.97, 0.01229, 90, 106.56, 122.01, 0.12725, 91, 52.29, 114.02, 0.06029, 6, 83, 232.22, 52.6, 0.008, 84, 143.19, 3.52, 0.25686, 85, 56.95, -6.95, 0.45792, 86, -40.49, 14.54, 0.07083, 90, 157.29, 120.39, 0.0578, 91, 101.73, 102.55, 0.14858, 6, 84, 187.85, 18.93, 0.07479, 85, 103.67, 0.09, 0.45014, 86, 3.77, -2, 0.24744, 87, -45.42, -42.28, 0.00833, 90, 203.54, 130.05, 0.01171, 91, 148.98, 103.02, 0.20759, 7, 83, 165.84, -34.51, 0.16158, 84, 50.88, -55.41, 0.3004, 85, -44.53, -48.11, 0.03987, 88, 148.6, 141.59, 0.01728, 89, 138.79, 81.14, 0.14314, 90, 58.29, 73.58, 0.2976, 91, -4.49, 75.9, 0.04013, 8, 83, 198.13, -12.37, 0.03987, 84, 88.81, -45.75, 0.3004, 85, -5.47, -45.51, 0.1562, 86, -113.78, 11.15, 0.00435, 88, 175.47, 170.07, 0.00384, 89, 176.56, 91.43, 0.03629, 90, 97.14, 78.38, 0.29493, 91, 34.55, 73.05, 0.16412, 7, 83, 253.87, 11.75, 0.0039, 84, 149.45, -42.29, 0.15807, 85, 54.79, -53.13, 0.29383, 86, -64.8, -24.78, 0.04703, 89, 237.13, 95.9, 0.0041, 90, 157.73, 74.16, 0.14581, 91, 93.17, 57.12, 0.34725, 5, 84, 184.46, -34.83, 0.04038, 85, 90.57, -52.17, 0.31117, 86, -33.05, -41.31, 0.15937, 90, 193.4, 77.14, 0.03862, 91, 128.73, 53.1, 0.45047, 5, 74, 161.26, 186.51, 0.008, 83, 106.9, -138.67, 0.08221, 88, 113.08, 27.31, 0.28402, 89, 48.89, 2.13, 0.53927, 90, -41.96, 8.2, 0.0865, 6, 83, 183.24, -108.56, 0.01171, 84, 41.72, -130.92, 0.03258, 88, 181.3, 72.91, 0.008, 89, 130.89, 5.49, 0.31429, 90, 39.68, -0.17, 0.56142, 91, -37.1, 7.18, 0.072, 7, 83, 215.92, -54.87, 0.01171, 84, 90.88, -91.77, 0.12836, 85, -11.8, -91.14, 0.05535, 86, -141.47, -25.67, 0.00453, 89, 179.39, 45.45, 0.07829, 90, 93.39, 32.46, 0.46153, 91, 21.94, 28.74, 0.26023, 7, 83, 180.06, -75.27, 0.06406, 84, 50.2, -98.57, 0.12973, 85, -53.05, -90.44, 0.01124, 88, 171.14, 104.77, 0.01536, 89, 138.83, 37.97, 0.24258, 90, 52.17, 30.84, 0.46694, 91, -18.81, 35.18, 0.07009, 6, 83, 139.41, -93.92, 0.15712, 84, 5.61, -102.1, 0.06086, 88, 135.37, 77.92, 0.07776, 89, 94.3, 33.71, 0.44312, 90, 7.49, 32.98, 0.25314, 91, -62.22, 45.97, 0.008, 6, 74, 128.61, 179, 0.028, 83, 98.71, -106.18, 0.22507, 84, -36.83, -99.59, 0.01161, 88, 98.19, 57.32, 0.24067, 89, 51.83, 35.5, 0.42759, 90, -34.29, 40.81, 0.06706, 6, 74, 119.2, 138.62, 0.09467, 83, 58.14, -97.62, 0.25364, 84, -71.96, -77.59, 0.00341, 88, 56.73, 57.09, 0.37563, 89, 16.33, 56.92, 0.2614, 90, -66.37, 67.08, 0.01125, 4, 74, 211.6, 104.77, 0.2805, 83, 26.24, -190.71, 0.02912, 88, 45.27, -40.65, 0.62838, 89, -44.39, -20.51, 0.062, 4, 74, 171.05, 103.09, 0.30333, 83, 23.71, -150.2, 0.10539, 88, 34.22, -1.6, 0.51128, 89, -33.47, 18.58, 0.08, 4, 74, 118.6, 104.47, 0.33289, 83, 23.99, -97.73, 0.21804, 88, 23.38, 49.74, 0.37291, 89, -15.96, 68.04, 0.07616, 6, 74, 98.2, 95.17, 0.40689, 83, 14.27, -77.53, 0.2838, 84, -106.24, -43.62, 0.00341, 88, 9.59, 67.43, 0.23941, 89, -18.51, 90.32, 0.06323, 90, -96.09, 105.1, 0.00325, 5, 74, 32.08, 83.03, 0.53533, 83, 0.75, -11.68, 0.33001, 84, -96.26, 22.86, 0.008, 88, -17.57, 128.91, 0.1024, 89, -9.63, 156.95, 0.02426], "hull": 43, "edges": [12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 88, 88, 90, 90, 86, 86, 10, 8, 90, 88, 6, 92, 94, 94, 20, 20, 18, 18, 16, 18, 92, 92, 96, 98, 82, 82, 84, 82, 80, 80, 78, 78, 100, 102, 94, 102, 96, 94, 104, 104, 106, 106, 24, 24, 22, 22, 20, 22, 104, 106, 108, 108, 110, 110, 28, 28, 26, 108, 26, 26, 24, 110, 48, 48, 46, 46, 32, 32, 30, 30, 28, 30, 48, 46, 44, 44, 34, 34, 32, 34, 36, 36, 38, 38, 40, 44, 42, 42, 40, 42, 36, 104, 112, 112, 114, 114, 106, 112, 102, 108, 116, 58, 48, 116, 114, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 50, 56, 116, 118, 118, 58, 110, 118, 118, 60, 60, 58, 76, 78, 74, 76, 80, 120, 120, 100, 120, 98, 100, 122, 122, 74, 76, 122, 74, 124, 124, 114, 124, 62, 62, 60, 62, 116, 74, 72, 72, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 70, 64, 124, 126, 100, 128, 128, 102, 126, 128, 122, 126, 126, 112, 128, 130, 96, 132, 132, 98, 130, 132, 120, 130, 130, 96, 84, 0, 0, 2, 2, 134, 134, 82, 0, 134, 88, 136, 136, 98, 134, 136, 90, 138, 138, 132, 136, 138, 90, 140, 140, 96, 138, 140, 86, 142, 142, 92, 140, 142, 16, 142, 12, 14, 14, 16, 86, 14], "width": 498, "height": 282}}, "yj_12": {"yj_12": {"type": "mesh", "uvs": [0.69396, 0.11649, 0.71072, 0.23514, 0.74702, 0.33019, 0.78472, 0.51367, 0.791, 0.60396, 0.79729, 0.70443, 0.81113, 0.77464, 0.90067, 0.86202, 0.94622, 0.90914, 1, 0.96011, 0.90538, 0.99999, 0.63991, 1, 0.61006, 0.92463, 0.59906, 0.81979, 0.58522, 0.75395, 0.55223, 0.67385, 0.5538, 0.5661, 0.4784, 0.43941, 0.3166, 0.37096, 0.12339, 0.26612, 0, 0.15545, 0.14224, 0.10594, 0.36486, 0.06141, 0.54769, 0.02469, 0.71834, 0, 0.38415, 0.28796, 0.23806, 0.18748, 0.55066, 0.36077, 0.65905, 0.52241, 0.69204, 0.62289, 0.69675, 0.72482, 0.72159, 0.79503, 0.764, 0.86347, 0.82527, 0.94409, 0.41514, 0.15039, 0.50351, 0.2535, 0.6193, 0.23796, 0.56598, 0.11932, 0.62952, 0.34849], "triangles": [19, 20, 21, 25, 26, 34, 18, 26, 25, 18, 25, 17, 17, 25, 27, 19, 26, 18, 19, 21, 26, 27, 38, 28, 26, 22, 34, 25, 34, 35, 28, 38, 2, 0, 23, 24, 37, 23, 0, 34, 22, 23, 37, 34, 23, 36, 37, 0, 36, 0, 1, 35, 34, 37, 35, 37, 36, 38, 36, 1, 38, 1, 2, 27, 35, 36, 38, 27, 36, 28, 2, 3, 26, 21, 22, 25, 35, 27, 4, 29, 3, 15, 16, 29, 31, 30, 5, 13, 14, 30, 29, 4, 5, 30, 29, 5, 15, 29, 30, 14, 15, 30, 17, 27, 28, 16, 17, 28, 29, 28, 3, 16, 28, 29, 6, 31, 5, 13, 30, 31, 32, 31, 6, 32, 6, 7, 32, 12, 13, 32, 13, 31, 33, 32, 7, 11, 12, 32, 33, 7, 8, 10, 33, 8, 10, 8, 9, 11, 32, 33, 11, 33, 10], "vertices": [1, 72, 186.44, -9.18, 1, 1, 72, 164.06, -33.14, 1, 3, 100, -52.33, 25.47, 0.25205, 72, 149.82, -56.81, 0.71043, 103, 53.04, -81.03, 0.03752, 4, 101, -45.89, 47.16, 0.01563, 100, -1.95, 34.43, 0.52457, 72, 117.11, -96.18, 0.42136, 103, 18.25, -118.56, 0.03844, 4, 101, -21.57, 42.33, 0.10063, 100, 22.81, 35.72, 0.70527, 72, 99.04, -113.14, 0.18466, 103, -0.72, -134.52, 0.00945, 4, 102, -24.56, 44.86, 0.01563, 101, 5.44, 36.77, 0.325, 100, 50.36, 36.98, 0.60724, 72, 78.81, -131.88, 0.05213, 4, 102, -6.17, 38.2, 0.10063, 101, 24.93, 35.22, 0.55875, 100, 69.64, 40.26, 0.33262, 72, 66.2, -146.83, 0.008, 3, 102, 25.97, 45.79, 0.34062, 101, 53.93, 51.02, 0.55875, 100, 93.85, 62.71, 0.10063, 3, 102, 42.95, 49.28, 0.65417, 101, 69.38, 58.88, 0.33021, 100, 106.9, 74.13, 0.01563, 2, 102, 61.9, 54.04, 0.83472, 101, 86.4, 68.47, 0.16528, 2, 102, 59.23, 27.77, 0.80104, 101, 90.76, 42.44, 0.19896, 3, 102, 25.25, -30.47, 0.64688, 101, 73.39, -22.72, 0.3375, 100, 130.85, -3.98, 0.01563, 3, 102, 3.59, -26.62, 0.34062, 101, 51.48, -24.72, 0.55875, 100, 110.11, -11.3, 0.10063, 3, 102, -22.63, -14.55, 0.10063, 101, 23.01, -20.02, 0.55875, 100, 81.35, -13.75, 0.34062, 5, 102, -39.98, -8.5, 0.01563, 101, 4.67, -18.77, 0.325, 100, 63.26, -17.04, 0.63899, 72, 34.08, -98.94, 0.01251, 103, -64.81, -116.81, 0.00788, 4, 101, -18.69, -21.21, 0.10063, 100, 41.22, -25.15, 0.78427, 72, 45.69, -78.52, 0.05853, 103, -52.11, -97.05, 0.05658, 4, 101, -47.12, -13.22, 0.01563, 100, 11.7, -24.39, 0.6453, 72, 68.73, -60.05, 0.14489, 103, -28.11, -79.86, 0.19419, 4, 100, -23.24, -43.12, 0.40769, 72, 83.34, -23.19, 0.15618, 103, -11.52, -43.85, 0.42814, 104, -4.49, -201.91, 0.008, 4, 100, -42.5, -83.99, 0.18852, 72, 71.67, 20.45, 0.12378, 103, -20.8, 0.36, 0.63885, 104, -13.27, -157.59, 0.04885, 4, 100, -71.82, -132.71, 0.05778, 72, 62.63, 76.59, 0.11833, 103, -26.78, 56.91, 0.69538, 104, -18.62, -100.98, 0.1285, 4, 100, -102.52, -163.68, 0.01033, 72, 66.1, 120.06, 0.14444, 103, -20.96, 100.13, 0.64801, 104, -12.31, -57.83, 0.19721, 4, 100, -115.65, -127.39, 0.00496, 72, 99.55, 100.81, 0.34333, 103, 11.39, 79.09, 0.49121, 104, 19.81, -79.23, 0.1605, 4, 100, -127.16, -70.7, 0.00496, 72, 144.92, 64.93, 0.6675, 103, 54.76, 40.8, 0.24029, 104, 62.73, -118.01, 0.08725, 1, 72, 182.22, 35.49, 1, 1, 72, 215.01, 6.34, 1, 4, 100, -65.03, -66.55, 0.1436, 72, 100.13, 21.67, 0.31505, 103, 7.69, 0.04, 0.5114, 104, 15.21, -158.24, 0.02995, 4, 100, -93.01, -103.32, 0.03813, 72, 97.78, 67.82, 0.31752, 103, 7.84, 46.24, 0.54161, 104, 15.88, -112.04, 0.10274, 4, 100, -44.56, -24.51, 0.29227, 72, 111.64, -23.65, 0.38806, 103, 16.71, -45.84, 0.31626, 104, 23.72, -204.22, 0.00341, 4, 101, -51.8, 15.7, 0.00667, 100, 0.06, 2.48, 0.53658, 72, 94.97, -73.07, 0.32934, 103, -2.61, -94.28, 0.12741, 4, 101, -23.04, 16.7, 0.093, 100, 27.69, 10.53, 0.74753, 72, 79.05, -97.04, 0.12829, 103, -19.81, -117.36, 0.03118, 5, 102, -32.6, 19.99, 0.008, 101, 4.26, 10.66, 0.32, 100, 55.63, 11.38, 0.63961, 72, 58.26, -115.73, 0.02903, 103, -41.58, -134.89, 0.00336, 4, 102, -12.81, 15.75, 0.093, 101, 24.47, 11.8, 0.579, 100, 74.94, 17.46, 0.32538, 72, 47.42, -132.83, 0.00262, 3, 102, 8.82, 15.6, 0.328, 101, 45.37, 17.38, 0.579, 100, 93.83, 28, 0.093, 3, 102, 35.74, 17.91, 0.6575, 101, 70.72, 26.72, 0.3345, 100, 116.1, 43.29, 0.008, 4, 100, -102.62, -58.22, 0.02697, 72, 134.23, 39.57, 0.65152, 103, 42.7, 16.06, 0.27364, 104, 50.4, -142.61, 0.04787, 4, 100, -74.1, -36.12, 0.10754, 72, 126.7, 4.28, 0.58105, 103, 33.27, -18.77, 0.2981, 104, 40.58, -177.33, 0.01331, 1, 72, 148.69, -15.71, 1, 1, 72, 165.17, 15.42, 1, 3, 100, -47.69, -4.44, 0.26792, 72, 126.97, -36.97, 0.61732, 103, 31.3, -59.97, 0.11477], "hull": 25, "edges": [50, 36, 36, 38, 38, 40, 40, 42, 42, 52, 52, 50, 52, 38, 50, 54, 54, 56, 56, 32, 32, 34, 34, 36, 34, 54, 4, 6, 6, 56, 56, 58, 58, 60, 60, 28, 28, 30, 30, 32, 30, 58, 58, 8, 8, 10, 10, 60, 8, 6, 60, 62, 62, 64, 64, 24, 24, 26, 26, 28, 26, 62, 62, 12, 12, 14, 14, 64, 12, 10, 64, 66, 66, 22, 22, 24, 20, 22, 66, 20, 14, 16, 16, 18, 18, 20, 66, 16, 42, 44, 44, 46, 46, 48, 48, 0, 0, 2, 2, 4, 44, 68, 68, 70, 70, 54, 70, 72, 72, 74, 74, 46, 0, 74, 74, 68, 68, 52, 50, 70, 72, 2, 54, 76, 76, 4, 72, 76, 76, 56], "width": 254, "height": 274}}, "yj_13": {"yj_13": {"type": "mesh", "uvs": [0.92338, 0.10933, 1, 0.28766, 0.97602, 0.59659, 0.92393, 0.79379, 0.79316, 0.86281, 0.69026, 0.90002, 0.63561, 0.92521, 0.58792, 0.94647, 0.5815, 0.89685, 0.54481, 0.80612, 0.46463, 0.75287, 0.32198, 0.59825, 0.20803, 0.48263, 0.09274, 0.41765, 0, 0.39076, 0, 0.30875, 0.12118, 0.16199, 0.36631, 0, 0.56617, 0, 0.77773, 0, 0.48643, 0.68297, 0.22804, 0.4033, 0.36547, 0.52431, 0.10143, 0.35446, 0.63378, 0.87275, 0.576, 0.76643, 0.65549, 0.75377, 0.61182, 0.56627, 0.48273, 0.37982, 0.33572, 0.24156, 0.21771, 0.18253, 0.73951, 0.74388, 0.88314, 0.64497, 0.89451, 0.35481, 0.73951, 0.51747, 0.63711, 0.22511, 0.49491, 0.07564], "triangles": [24, 25, 26, 6, 24, 5, 8, 24, 6, 7, 8, 6, 8, 25, 24, 24, 26, 5, 9, 25, 8, 22, 11, 21, 11, 12, 21, 10, 11, 20, 9, 10, 20, 25, 9, 20, 20, 11, 22, 31, 26, 34, 3, 32, 2, 4, 31, 32, 3, 4, 32, 5, 26, 31, 5, 31, 4, 36, 17, 18, 29, 17, 36, 28, 29, 36, 22, 29, 28, 20, 28, 27, 35, 18, 19, 36, 18, 35, 33, 19, 0, 33, 0, 1, 35, 19, 33, 28, 36, 35, 34, 35, 33, 27, 28, 35, 34, 27, 35, 2, 33, 1, 32, 34, 33, 2, 32, 33, 34, 26, 27, 31, 34, 32, 26, 25, 27, 30, 16, 17, 29, 30, 17, 23, 15, 16, 23, 16, 30, 14, 15, 23, 21, 30, 29, 23, 30, 21, 13, 14, 23, 22, 28, 20, 25, 20, 27, 12, 13, 21, 21, 29, 22, 13, 23, 21], "vertices": [1, 72, 381.31, -28.56, 1, 1, 72, 360.87, -95.74, 1, 2, 72, 274.94, -150.8, 0.98438, 100, -87.27, 178, 0.01563, 2, 72, 208.23, -171.55, 0.93125, 100, -22.91, 150.81, 0.06875, 3, 72, 148.6, -134.78, 0.73625, 103, 47.59, -158.81, 0.01632, 100, -1.09, 84.24, 0.24743, 3, 72, 106, -102.28, 0.3915, 103, 6.82, -124.05, 0.0697, 100, 10.48, 31.92, 0.5388, 3, 72, 82, -86.15, 0.1425, 103, -16.27, -106.65, 0.06213, 100, 18.41, 4.11, 0.79537, 3, 72, 61.24, -71.93, 0.03333, 103, -36.22, -91.32, 0.08808, 100, 25.09, -20.15, 0.87859, 4, 72, 71.73, -59.07, 0.0425, 103, -25.05, -79.04, 0.20735, 104, -18.42, -236.95, 0.00762, 100, 8.77, -23.2, 0.74252, 4, 72, 82.87, -25.79, 0.075, 103, -12.13, -46.42, 0.40152, 104, -5.13, -204.47, 0.0571, 100, -21.21, -41.44, 0.46638, 4, 72, 70.5, 16.69, 0.11588, 103, -22.18, -3.33, 0.49773, 104, -14.69, -161.27, 0.19229, 100, -39.17, -81.88, 0.19411, 4, 72, 63.64, 104.76, 0.16869, 103, -24.25, 84.98, 0.39524, 104, -15.77, -72.94, 0.38169, 100, -90.77, -153.58, 0.05439, 4, 72, 56.17, 173.46, 0.31559, 103, -27.99, 153.99, 0.19846, 104, -18.72, -3.9, 0.47883, 100, -129.39, -210.88, 0.00713, 3, 72, 35.44, 232.13, 0.54075, 103, -45.5, 213.69, 0.05915, 104, -35.57, 56, 0.4001, 3, 72, 12.34, 274.02, 0.70433, 103, -66.29, 256.78, 0.01067, 104, -55.87, 99.32, 0.28501, 3, 72, 33.1, 291.13, 0.79475, 103, -44.64, 272.73, 0.00512, 104, -34.03, 115.03, 0.20013, 2, 72, 109.32, 274.33, 0.90737, 104, 40.92, 93.27, 0.09263, 2, 72, 229.36, 212.21, 0.95071, 104, 156.64, 23.42, 0.04929, 2, 72, 293.81, 134.01, 0.98536, 104, 215.82, -58.83, 0.01464, 3, 72, 362.02, 51.23, 0.992, 103, 270.79, 15.35, 0.0041, 104, 278.47, -145.9, 0.0039, 4, 72, 95.22, 22.75, 0.30229, 103, 2.83, 1.38, 0.39677, 104, 10.37, -156.85, 0.13898, 100, -61.96, -70.55, 0.16197, 4, 72, 82.7, 182.18, 0.47228, 103, -1.02, 161.26, 0.14359, 104, 8.32, 3.07, 0.38048, 100, -155.29, -200.42, 0.00365, 4, 72, 96.38, 103.17, 0.35921, 103, 8.36, 81.62, 0.30881, 104, 16.8, -76.67, 0.29912, 100, -114.75, -131.23, 0.03285, 3, 72, 54.24, 241.91, 0.65709, 103, -26.2, 222.43, 0.0359, 104, -16.17, 64.53, 0.307, 4, 72, 94.69, -74.49, 0.154, 103, -2.97, -95.69, 0.14972, 104, 3.48, -253.84, 0.0039, 100, 1.19, 3.39, 0.69238, 4, 72, 102.97, -29.71, 0.25, 103, 7.73, -51.42, 0.31417, 104, 14.67, -209.7, 0.0346, 100, -34.04, -25.47, 0.40123, 4, 72, 131.81, -58.17, 0.54, 103, 34.98, -81.4, 0.15374, 104, 41.58, -239.98, 0.01171, 100, -37.7, 14.88, 0.29454, 4, 72, 165.19, -1.97, 0.648, 103, 71.36, -27.1, 0.20214, 104, 78.57, -186.09, 0.06011, 100, -99.46, -6.51, 0.08974, 4, 72, 170.76, 87.43, 0.68486, 103, 81.77, 61.87, 0.15054, 104, 89.99, -97.25, 0.14565, 100, -161.41, -71.21, 0.01894, 3, 72, 158.36, 173.79, 0.74366, 103, 74.07, 148.77, 0.06189, 104, 83.27, -10.26, 0.19445, 3, 72, 135.25, 232.27, 0.84052, 103, 54.17, 208.43, 0.01229, 104, 64.04, 49.61, 0.14719, 3, 72, 161.4, -88.98, 0.8435, 103, 62.86, -113.77, 0.04141, 100, -40.42, 57.51, 0.11509, 3, 72, 232.75, -124.54, 0.9615, 103, 132.18, -153.15, 0.00435, 100, -71.98, 130.72, 0.03415, 2, 72, 309.86, -68.47, 0.992, 103, 212.22, -101.34, 0.008, 4, 72, 218.71, -41.75, 0.894, 103, 122.65, -69.72, 0.05734, 104, 129.37, -229.29, 0.01171, 100, -114.68, 58.42, 0.03694, 3, 72, 259.7, 59.29, 0.92, 103, 169.06, 28.95, 0.04038, 104, 176.89, -131.15, 0.03962, 3, 72, 251.68, 146.11, 0.93286, 103, 165.76, 116.08, 0.01229, 104, 174.58, -43.99, 0.05485], "hull": 20, "edges": [40, 20, 20, 22, 22, 24, 24, 42, 42, 44, 44, 40, 44, 22, 42, 46, 46, 30, 28, 30, 28, 26, 26, 24, 26, 46, 20, 18, 16, 48, 48, 12, 12, 14, 18, 50, 50, 40, 50, 48, 16, 14, 16, 18, 48, 10, 10, 12, 10, 52, 52, 54, 52, 50, 54, 40, 54, 56, 56, 58, 58, 60, 60, 32, 32, 30, 46, 60, 58, 42, 56, 44, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 38, 36, 38, 34, 36, 34, 32, 60, 34, 52, 62, 62, 64, 64, 4, 64, 6, 62, 8, 64, 66, 66, 68, 68, 54, 68, 62, 66, 2, 68, 70, 70, 72, 72, 34, 72, 58, 36, 72, 56, 70, 70, 38, 66, 38], "width": 507, "height": 328}}, "yj_14": {"yj_14": {"type": "mesh", "uvs": [0.59049, 0.23147, 0.74434, 0.30947, 0.95902, 0.46169, 1, 0.53595, 1, 0.66406, 0.92423, 0.70305, 0.79459, 0.81633, 0.70826, 1, 0.52915, 0.90359, 0.23407, 0.76805, 0, 0.72721, 0.15805, 0.49142, 0.41654, 0.3113, 0.63481, 0.36702, 0.40932, 0.5434, 0.63095, 0.6548, 0.84563, 0.52853], "triangles": [6, 15, 16, 15, 14, 13, 14, 11, 12, 14, 12, 13, 13, 1, 16, 16, 1, 2, 15, 13, 16, 16, 2, 3, 3, 5, 16, 4, 5, 3, 6, 16, 5, 13, 0, 1, 12, 0, 13, 6, 7, 15, 9, 11, 14, 8, 14, 15, 10, 11, 9, 9, 14, 8, 7, 8, 15], "vertices": [1, 92, 47.91, 87.83, 1, 1, 92, 70.45, 26.26, 1, 1, 92, 92.55, -67.35, 1, 1, 92, 87.04, -93.24, 1, 1, 92, 59.46, -115.97, 1, 3, 99, -90.62, 158.16, 0.09542, 98, -23.76, 133.24, 0.22708, 92, 31.69, -99.38, 0.6775, 3, 99, -32.33, 140.36, 0.29431, 98, 37.05, 129.24, 0.36236, 92, -25.84, -79.26, 0.34333, 3, 99, 29.01, 148.63, 0.47546, 98, 94.87, 151.32, 0.38009, 92, -87.46, -85.07, 0.14444, 3, 99, 56.93, 77.01, 0.50472, 98, 138.43, 87.99, 0.37694, 92, -112.49, -12.4, 0.11833, 3, 99, 107.77, -36.63, 0.50472, 98, 213.93, -11.01, 0.37694, 92, -158.75, 103.19, 0.11833, 3, 99, 161.98, -114.39, 0.47546, 98, 284.48, -74.31, 0.38009, 92, -209.79, 183.06, 0.14444, 3, 99, 70.59, -110.85, 0.29431, 98, 194.7, -91.77, 0.36236, 92, -118.62, 175.86, 0.34333, 3, 99, -36.13, -66.85, 0.09542, 98, 80.76, -73.34, 0.22708, 92, -13.75, 127.63, 0.6775, 3, 99, -83, 8.94, 0.0845, 98, 17.79, -10.29, 0.2335, 92, 30.05, 50.03, 0.682, 3, 99, 14.09, -25.87, 0.29417, 98, 120.27, -21.96, 0.37783, 92, -65.57, 88.69, 0.328, 3, 99, -22.1, 61.28, 0.29417, 98, 65.1, 54.6, 0.37783, 92, -32.9, 0.16, 0.328, 3, 99, -105.87, 102.15, 0.0845, 98, -25.8, 75.23, 0.2335, 92, 49.17, -44.03, 0.682], "hull": 13, "edges": [2, 26, 26, 28, 28, 18, 18, 16, 16, 14, 14, 12, 12, 30, 30, 28, 28, 22, 22, 20, 20, 18, 16, 30, 26, 32, 32, 10, 10, 12, 10, 8, 8, 6, 6, 4, 4, 32, 2, 4, 2, 0, 0, 24, 24, 22, 24, 26, 32, 30], "width": 402, "height": 279}}, "yj_15": {"yj_15": {"type": "mesh", "uvs": [0.92426, 0.06758, 1, 0.25373, 1, 0.40726, 0.91873, 0.58573, 0.8099, 0.76037, 0.71579, 0.8764, 0.62199, 0.97075, 0.36208, 0.95586, 0.06841, 0.81691, 0, 0.59558, 0.05472, 0.50758, 0.1459, 0.37656, 0.24366, 0.19041, 0.40413, 0, 0.70846, 0, 0.17996, 0.68638, 0.50009, 0.8606, 0.28239, 0.55887, 0.58857, 0.74886, 0.40044, 0.3305, 0.72322, 0.48594, 0.5609, 0.10021, 0.8597, 0.27293], "triangles": [7, 16, 6, 8, 15, 7, 8, 9, 15, 6, 16, 5, 7, 15, 16, 9, 10, 15, 16, 18, 5, 15, 17, 16, 15, 10, 17, 5, 18, 4, 16, 17, 18, 10, 11, 17, 4, 20, 3, 18, 19, 20, 11, 12, 19, 3, 20, 2, 19, 21, 20, 19, 12, 13, 20, 22, 2, 20, 21, 22, 19, 13, 21, 18, 20, 4, 17, 19, 18, 17, 11, 19, 22, 1, 2, 21, 14, 22, 22, 0, 1, 22, 14, 0, 21, 13, 14], "vertices": [2, 92, -25.4, -1.08, 0.752, 93, 24.2, 0.98, 0.248, 2, 92, -41.58, -32.08, 0.752, 93, 38.04, 33.09, 0.248, 2, 92, -62.08, -48.97, 0.584, 93, 57.23, 51.46, 0.416, 2, 92, -95.21, -57.31, 0.328, 93, 89.65, 62.24, 0.672, 3, 92, -130.97, -61.4, 0.12768, 93, 125.01, 68.97, 0.71232, 94, 64.19, -62.79, 0.16, 2, 93, 151.2, 70.61, 0.52, 94, 38.29, -67.07, 0.48, 2, 93, 174.66, 69.71, 0.288, 94, 14.87, -68.54, 0.712, 2, 93, 205.17, 34.15, 0.288, 94, -19.07, -36.24, 0.712, 2, 93, 224.39, -20.65, 0.288, 94, -43.73, 16.34, 0.712, 2, 93, 205.26, -56.03, 0.288, 94, -28.28, 53.47, 0.712, 2, 93, 187.45, -59.45, 0.52, 94, -10.89, 58.68, 0.48, 3, 92, -155.76, 73.06, 0.12768, 93, 159.71, -63.28, 0.71232, 94, 16.32, 65.28, 0.16, 2, 92, -119.71, 79.95, 0.328, 93, 124.27, -72.83, 0.672, 2, 92, -75.92, 78.61, 0.584, 93, 80.5, -74.74, 0.416, 2, 92, -41.08, 36.34, 0.752, 93, 42.62, -35.17, 0.248, 2, 93, 194.19, -21.78, 0.52, 94, -13.8, 20.51, 0.48, 2, 93, 176.09, 40.68, 0.52, 94, 10.52, -39.8, 0.48, 3, 92, -164.47, 34.04, 0.12768, 93, 165.5, -23.72, 0.71232, 94, 14.55, 25.34, 0.16, 3, 92, -154.78, -29.4, 0.12768, 93, 151.12, 38.82, 0.71232, 94, 35.17, -35.43, 0.16, 2, 92, -120.46, 42.76, 0.328, 93, 122.26, -35.69, 0.672, 2, 92, -104.27, -19.18, 0.328, 93, 101.51, 24.88, 0.672, 2, 92, -71.35, 45.81, 0.584, 93, 73.51, -42.37, 0.416, 2, 92, -60.21, -14.7, 0.584, 93, 57.9, 17.15, 0.416], "hull": 15, "edges": [20, 30, 30, 32, 32, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 32, 14, 20, 22, 22, 34, 34, 36, 36, 8, 8, 10, 8, 6, 6, 4, 4, 2, 2, 0, 0, 28, 26, 28, 26, 24, 24, 22, 24, 38, 38, 40, 40, 6, 40, 36, 36, 32, 16, 30, 30, 34, 34, 38, 38, 42, 42, 28, 26, 42, 42, 44, 44, 4, 0, 44, 44, 40], "width": 180, "height": 173}}, "wu1": {"wu1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-508.39, 304.1, -588.39, 305.02, -587.7, 365.02, -507.7, 364.1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 80, "height": 60}}, "wu2": {"wu2": {"type": "mesh", "uvs": [1, 0.21966, 1, 0.54662, 1, 1, 0.87862, 0.96676, 0.76836, 0.89381, 0.56803, 0.81577, 0.62578, 0.78842, 0.47047, 0.72509, 0.28328, 0.70065, 0.12907, 0.63377, 0, 0.56432, 0, 0.53217, 0.04614, 0.53217, 0.10971, 0.42799, 0.20624, 0.32124, 0.34987, 0.29166, 0.45534, 0.31312, 0.59352, 0.18752, 0.73317, 0.18032, 0.84911, 0.03783, 0.94133, 0, 1, 0, 0.56878, 0.3944, 0.61528, 0.48989, 0.68037, 0.39643, 0.75979, 0.38041, 0.84097, 0.27452, 0.90355, 0.29687, 0.80168, 0.20767, 0.67287, 0.53633, 0.67287, 0.59035, 0.71996, 0.65337, 0.70701, 0.76527, 0.77294, 0.68681, 0.86123, 0.70996, 0.92009, 0.5762, 0.20559, 0.53088, 0.33155, 0.49616, 0.46458, 0.53603, 0.52697, 0.49873, 0.54238, 0.60052, 0.33118, 0.60222, 0.33118, 0.39185, 0.65419, 0.27479, 0.93216, 0.1204, 0.94614, 0.75151, 0.86383, 0.43256], "triangles": [44, 19, 20, 28, 18, 19, 44, 28, 19, 44, 20, 21, 0, 44, 21, 26, 28, 44, 27, 26, 44, 43, 17, 18, 0, 27, 44, 22, 16, 17, 28, 43, 18, 25, 28, 26, 25, 43, 28, 42, 14, 15, 43, 22, 17, 24, 22, 43, 25, 24, 43, 46, 26, 27, 25, 26, 46, 23, 22, 24, 37, 42, 38, 16, 42, 15, 39, 16, 22, 39, 22, 23, 16, 38, 42, 36, 13, 14, 36, 14, 42, 36, 42, 37, 12, 13, 36, 16, 39, 38, 29, 23, 24, 27, 0, 1, 46, 27, 1, 10, 11, 12, 35, 46, 1, 30, 23, 29, 40, 39, 23, 40, 23, 30, 38, 39, 40, 41, 36, 37, 41, 37, 38, 9, 12, 36, 10, 12, 9, 25, 29, 24, 46, 29, 25, 31, 46, 35, 46, 31, 29, 30, 29, 31, 33, 31, 35, 8, 36, 41, 9, 36, 8, 34, 33, 35, 7, 38, 40, 41, 38, 7, 8, 41, 7, 45, 35, 1, 34, 35, 45, 31, 6, 30, 32, 31, 33, 6, 40, 30, 32, 6, 31, 7, 40, 6, 34, 4, 32, 34, 32, 33, 6, 32, 4, 5, 6, 4, 45, 4, 34, 3, 4, 45, 45, 1, 2, 3, 45, 2], "vertices": [810.78, 484.89, 809.47, 371.77, 807.66, 214.91, 761.92, 226.94, 720.53, 252.66, 645.13, 280.53, 667.06, 289.75, 608.61, 312.33, 537.96, 321.6, 479.93, 345.42, 431.43, 370.01, 431.56, 381.13, 449, 380.93, 473.44, 416.69, 510.35, 453.21, 564.76, 462.81, 604.54, 454.93, 657.27, 497.78, 710.08, 499.66, 754.47, 548.46, 789.48, 561.14, 811.66, 560.89, 647.09, 426.32, 664.29, 393.07, 689.26, 425.13, 719.35, 430.32, 750.45, 466.6, 774.01, 458.6, 735.87, 489.9, 685.87, 376.75, 685.65, 358.07, 703.2, 336.06, 697.86, 297.4, 723.09, 324.26, 756.37, 315.86, 779.15, 361.88, 509.27, 380.68, 557.02, 392.14, 607.14, 377.77, 630.87, 390.4, 636.29, 355.12, 556.46, 355.45, 557.3, 428.23, 679.85, 467.33, 785.53, 519.53, 788.3, 301.12, 758.46, 411.82], "hull": 22, "edges": [32, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 0, 42, 54, 0, 52, 56, 56, 36, 36, 34, 34, 32, 36, 38, 40, 42, 38, 40, 46, 58, 58, 60, 60, 62, 62, 64, 64, 12, 64, 66, 66, 68, 68, 70, 0, 2, 2, 4, 70, 2, 12, 14, 14, 16, 16, 18, 18, 72, 72, 74, 74, 76, 76, 78, 78, 46, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 12, 10, 10, 8, 8, 6, 6, 4], "width": 226, "height": 207}}, "wu3": {"wu3": {"type": "mesh", "uvs": [0.23738, 0.1061, 0.31553, 0.30454, 0.40077, 0.31006, 0.47323, 0.37988, 0.54711, 0.30271, 0.68777, 0.28433, 0.80853, 0.35048, 0.85968, 0.45522, 0.95061, 0.46073, 1, 0.55628, 0.91599, 0.58034, 0.85894, 0.61149, 0.74699, 0.63065, 0.65951, 0.63229, 0.6798, 0.6618, 0.6088, 0.68312, 0.55175, 0.71755, 0.4478, 0.72903, 0.32102, 0.75526, 0.25763, 0.77493, 0.2196, 0.81428, 0.1448, 0.83396, 0.19805, 0.87331, 0.09916, 0.95037, 0, 1, 0, 0.77562, 0, 0.52072, 0, 0.16965, 0, 0, 0.10667, 0, 0.50143, 0.46557, 0.59524, 0.51148, 0.62821, 0.38523, 0.75752, 0.50984, 0.08735, 0.49021, 0.20533, 0.55303, 0.22476, 0.64817, 0.3011, 0.68407, 0.10123, 0.6356, 0.08041, 0.76844, 0.04293, 0.86896, 0.07486, 0.18882, 0.23031, 0.33781, 0.2553, 0.47424, 0.38299, 0.49219, 0.43434, 0.57656, 0.55232, 0.59271], "triangles": [41, 27, 28, 29, 41, 28, 41, 29, 0, 42, 41, 0, 42, 0, 1, 32, 4, 5, 30, 3, 4, 30, 4, 32, 43, 42, 1, 34, 41, 42, 34, 42, 43, 44, 1, 2, 44, 2, 3, 44, 3, 30, 43, 1, 44, 33, 5, 6, 33, 6, 7, 32, 5, 33, 31, 30, 32, 31, 32, 33, 34, 26, 27, 34, 27, 41, 35, 34, 43, 45, 44, 30, 10, 7, 8, 10, 8, 9, 46, 30, 31, 45, 30, 46, 11, 33, 7, 11, 7, 10, 33, 13, 31, 12, 33, 11, 12, 13, 33, 46, 31, 13, 38, 34, 35, 26, 34, 38, 37, 36, 35, 38, 35, 36, 15, 46, 13, 15, 13, 14, 37, 43, 44, 37, 44, 45, 37, 35, 43, 46, 17, 45, 16, 46, 15, 16, 17, 46, 37, 45, 17, 18, 37, 17, 38, 25, 26, 19, 36, 37, 19, 37, 18, 39, 25, 38, 36, 39, 38, 20, 36, 19, 36, 21, 39, 20, 21, 36, 40, 25, 39, 40, 39, 21, 23, 40, 21, 23, 21, 22, 24, 25, 40, 24, 40, 23], "vertices": [-604.15, 509.19, -576.2, 452.71, -545.02, 450.79, -518.73, 430.72, -491.44, 452.25, -439.91, 456.85, -395.92, 437.63, -377.55, 407.77, -344.29, 405.83, -326.52, 378.58, -357.35, 372.13, -378.33, 363.55, -419.36, 358.6, -451.38, 358.51, -444.05, 350.07, -470.11, 344.34, -491.1, 334.84, -529.18, 332.03, -575.66, 325.14, -598.93, 319.84, -612.97, 308.87, -640.41, 303.61, -621.05, 292.25, -657.49, 270.86, -693.95, 257.24, -693.22, 320.73, -692.38, 392.87, -691.24, 492.21, -690.68, 540.22, -651.64, 539.77, -508.69, 406.35, -474.51, 392.97, -462.03, 428.55, -415.11, 392.75, -660.32, 401.13, -617.34, 382.85, -610.54, 355.85, -582.72, 345.37, -655.71, 359.93, -663.76, 322.43, -677.81, 294.14, -663.9, 486.47, -607.5, 443.65, -598.8, 404.94, -552.13, 399.32, -533.61, 375.23, -490.48, 370.16], "hull": 30, "edges": [56, 58, 58, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 6, 60, 60, 62, 62, 26, 52, 68, 68, 70, 70, 72, 72, 74, 74, 36, 48, 50, 50, 52, 50, 78, 78, 42, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 30, 92, 52, 54, 54, 56, 82, 54], "width": 219, "height": 169}}, "wu4": {"wu4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [763.52, 202.42, 654.52, 203.68, 655.07, 250.67, 764.06, 249.42], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 109, "height": 47}}, "wu5": {"wu5": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-328.32, 396.03, -692.3, 400.23, -682.49, 1250.17, -318.52, 1245.97], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 218, "height": 510}}, "wu6": {"wu6": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [129.65, 390.74, -280.33, 395.48, -272.6, 1065.43, 137.38, 1060.7], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 246, "height": 402}}, "wu7": {"wu7": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [809.6, 382.9, 434.63, 387.23, 444.42, 1236.17, 819.4, 1231.84], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 225, "height": 509}}, "lu": {"lu": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [804.41, -67.07, -697.49, -49.74, -689.53, 640.21, 812.37, 622.88], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 901, "height": 414}}, "jj_20": {"jj_20": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [120.44, -27.25, 24.86, -70.7, -12.8, 12.15, 82.79, 55.6], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 105, "height": 91}}, "jj_23": {"jj_23": {"type": "mesh", "uvs": [1, 0.02579, 1, 0.12143, 0.97895, 0.24281, 0.95888, 0.35848, 0.96352, 0.4844, 0.96726, 0.58577, 1, 0.72847, 0.70949, 0.79288, 0.68434, 0.89516, 0.3574, 0.94441, 0.17088, 0.76257, 0, 0.62492, 0.08076, 0.54789, 0.11871, 0.42509, 0.15412, 0.31049, 0.20863, 0.19312, 0.26572, 0.07018, 0.31624, 0, 0.63479, 0, 0.59459, 0.12143, 0.49437, 0.36099, 0.46084, 0.62239, 0.40599, 0.79317, 0.5524, 0.22229, 0.47642, 0.50091], "triangles": [23, 16, 19, 19, 17, 18, 2, 19, 1, 18, 0, 1, 19, 18, 1, 16, 17, 19, 13, 14, 20, 14, 15, 20, 20, 23, 3, 20, 15, 23, 15, 16, 23, 3, 23, 2, 4, 20, 3, 23, 19, 2, 21, 24, 5, 21, 12, 24, 24, 4, 5, 12, 13, 24, 4, 24, 20, 24, 13, 20, 7, 5, 6, 9, 22, 8, 9, 10, 22, 8, 22, 7, 22, 21, 7, 22, 10, 21, 11, 12, 10, 7, 21, 5, 10, 12, 21], "vertices": [1, 4, 50.35, 50.7, 1, 3, 50, -83.07, 87.27, 0.01563, 49, -20.67, 76.43, 0.30937, 4, 50.95, 20.87, 0.675, 3, 50, -45, 86.66, 0.10063, 49, 17.18, 80.5, 0.55875, 4, 47.75, -17.07, 0.34062, 4, 51, -97.58, 78.24, 0.01563, 50, -8.72, 86.09, 0.325, 49, 53.26, 84.37, 0.55875, 4, 44.7, -53.23, 0.10063, 4, 51, -59.02, 85.81, 0.09542, 50, 30.34, 90.42, 0.56396, 49, 91.5, 93.46, 0.325, 4, 46.36, -92.49, 0.01563, 3, 51, -27.97, 91.9, 0.32014, 50, 61.78, 93.91, 0.57924, 49, 122.27, 100.77, 0.10063, 3, 51, 14.84, 105.56, 0.59491, 50, 105.59, 103.96, 0.38426, 49, 164.52, 116.12, 0.02083, 2, 51, 43.97, 55.18, 0.82639, 50, 130.42, 51.33, 0.17361, 2, 51, 76.22, 55.97, 0.93889, 50, 162.63, 49.43, 0.06111, 2, 51, 101.85, -1.97, 0.90417, 50, 183.35, -10.44, 0.09583, 2, 51, 51.93, -46.21, 0.81389, 50, 129.93, -50.37, 0.18611, 3, 51, 15.1, -85.19, 0.59491, 50, 89.98, -86.15, 0.38426, 49, 172.32, -74.48, 0.02083, 3, 51, -11.17, -74.33, 0.32014, 50, 64.7, -73.15, 0.57924, 49, 145.64, -64.67, 0.10063, 4, 51, -50.14, -73.84, 0.09542, 50, 25.91, -69.42, 0.56396, 49, 106.68, -65.72, 0.325, 4, -112.81, -77.16, 0.01563, 4, 51, -86.51, -73.39, 0.01563, 50, -10.29, -65.94, 0.325, 49, 70.32, -66.7, 0.55875, 4, -106.87, -41.29, 0.10063, 3, 50, -47.67, -58.96, 0.10063, 49, 32.37, -64.36, 0.55875, 4, -97.35, -4.47, 0.34062, 3, 50, -86.83, -51.65, 0.01563, 49, -7.38, -61.9, 0.30937, 4, -87.39, 34.1, 0.675, 1, 4, -78.33, 56.18, 1, 1, 4, -18.46, 57.38, 1, 3, 50, -76.35, 11.35, 0.008, 49, -4.7, 1.91, 0.312, 4, -25.25, 19.35, 0.68, 4, 51, -81.9, -7.67, 0.008, 50, -0.24, -0.83, 0.32, 49, 72.33, -0.85, 0.579, 4, -42.6, -55.76, 0.093, 3, 51, -0.47, 0.04, 0.33967, 50, 81.56, 0.08, 0.56733, 49, 153.4, 10.07, 0.093, 3, 51, 53.79, -1.03, 0.75667, 50, 135.54, -5.5, 0.23533, 49, 207.65, 11.15, 0.008, 3, 50, -44.31, 6.22, 0.093, 49, 27.73, 0.75, 0.579, 4, -32.56, -12.27, 0.328, 4, 51, -38.31, -3.54, 0.093, 50, 43.54, -0.34, 0.579, 49, 115.72, 4.99, 0.32, 4, -45.1, -99.47, 0.008], "hull": 19, "edges": [32, 38, 38, 2, 32, 34, 34, 36, 2, 0, 36, 0, 36, 38, 42, 24, 24, 22, 22, 20, 20, 18, 42, 44, 44, 18, 20, 44, 44, 14, 14, 16, 16, 18, 42, 10, 10, 12, 12, 14, 40, 6, 40, 28, 32, 30, 30, 28, 38, 46, 46, 40, 30, 46, 2, 4, 4, 6, 46, 4, 24, 26, 26, 28, 40, 48, 48, 42, 26, 48, 10, 8, 8, 6, 48, 8], "width": 188, "height": 312}}, "jj_22": {"jj_22": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [21.72, 196.38, 311.8, 56.62, 209.37, -155.99, -80.72, -16.23], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 322, "height": 236}}, "yy": {"yy": {"type": "mesh", "uvs": [0.81212, 0.19959, 1, 0.58426, 1, 0.90151, 0.79696, 0.84996, 0.57725, 0.83865, 0.31211, 0.90016, 0.09423, 1, 0, 1, 0, 0.68995, 0.21468, 0.2577, 0.38146, 0, 0.46708, 0, 0.59461, 0, 0.45402, 0.42857, 0.40099, 0.14152, 0.23153, 0.4337, 0.03441, 0.76176, 0.09204, 0.85403, 0.31799, 0.72075, 0.56584, 0.70025, 0.80447, 0.71563, 0.94972, 0.72075, 0.77911, 0.4337, 0.57622, 0.17228, 0.48054, 0.09539], "triangles": [24, 11, 12, 14, 10, 11, 14, 11, 24, 23, 24, 12, 13, 14, 24, 15, 9, 10, 15, 10, 14, 22, 12, 0, 23, 12, 22, 16, 8, 9, 23, 13, 24, 19, 23, 22, 19, 13, 23, 21, 20, 22, 0, 21, 22, 19, 22, 20, 18, 15, 14, 18, 14, 13, 18, 13, 19, 1, 21, 0, 15, 16, 9, 4, 19, 20, 3, 4, 20, 17, 16, 15, 17, 15, 18, 5, 17, 18, 5, 18, 19, 5, 19, 4, 21, 1, 2, 3, 20, 21, 2, 3, 21, 7, 8, 16, 6, 17, 5, 16, 17, 6, 7, 16, 6], "vertices": [2, 3, 395.07, 83.14, 0.168, 1, 404.33, 222.71, 0.832, 1, 1, 623.21, 121.92, 1, 1, 1, 623.21, 38.8, 1, 2, 3, 378.72, -87.38, 0.168, 1, 386.66, 52.31, 0.832, 2, 3, 122.74, -86.39, 0.168, 1, 130.71, 55.27, 0.832, 2, 3, -186.01, -104.88, 0.168, 1, -178.18, 39.16, 0.832, 1, 1, -432.01, 13, 1, 1, 1, -541.79, 13, 1, 1, 1, -541.79, 94.23, 1, 2, 3, -300.81, 62.57, 0.168, 1, -291.69, 207.48, 0.832, 2, 3, -107.04, 131.58, 0.168, 1, -97.39, 275, 0.832, 1, 1, 2.35, 275, 1, 2, 3, 141.28, 133.49, 0.168, 1, 150.93, 275, 0.832, 2, 3, -21.64, 19.94, 0.448, 1, -12.86, 162.71, 0.552, 2, 3, -84, 94.68, 0.168, 1, -74.64, 237.92, 0.832, 2, 3, -280.82, 16.61, 0.168, 1, -272.06, 161.37, 0.832, 1, 1, -501.71, 75.42, 1, 1, 1, -434.56, 51.24, 1, 2, 3, -179.52, -57.82, 0.168, 1, -171.33, 86.16, 0.832, 2, 3, 109.17, -50.23, 0.168, 1, 117.41, 91.53, 0.832, 2, 3, 387.2, -52.12, 0.168, 1, 395.41, 87.51, 0.832, 1, 1, 564.63, 86.16, 1, 2, 3, 357.08, 21.51, 0.168, 1, 365.87, 161.37, 0.832, 2, 3, 120.2, 88.19, 0.168, 1, 129.5, 229.86, 0.832, 1, 1, 18.03, 250.01, 1], "hull": 13, "edges": [14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 0, 2, 4, 4, 6, 0, 2, 6, 8, 8, 10, 12, 14, 10, 12, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 28], "width": 699, "height": 157}}, "jj_24": {"jj_24": {"type": "mesh", "uvs": [1, 0.0413, 1, 0.15159, 0.92714, 0.31703, 0.824, 0.5057, 0.72774, 0.62911, 0.65369, 0.75973, 0.62196, 0.87438, 0.58757, 1, 0.16178, 0.88163, 0, 0.62476, 0.06393, 0.57106, 0.12476, 0.52608, 0.21468, 0.45061, 0.27391, 0.33735, 0.46961, 0.20093, 0.59391, 0.05726, 0.66796, 0, 0.85309, 0, 0.7473, 0.11096, 0.64681, 0.26044, 0.5278, 0.41137, 0.41303, 0.52608, 0.32311, 0.63492, 0.24112, 0.73215], "triangles": [1, 18, 0, 18, 17, 0, 15, 16, 18, 18, 16, 17, 1, 19, 18, 14, 15, 18, 21, 20, 3, 3, 20, 2, 2, 20, 19, 13, 14, 20, 20, 14, 19, 2, 19, 1, 19, 14, 18, 12, 13, 20, 5, 22, 4, 10, 11, 22, 22, 21, 4, 22, 11, 21, 4, 21, 3, 11, 12, 21, 21, 12, 20, 23, 22, 5, 22, 23, 10, 7, 8, 6, 8, 23, 6, 8, 9, 23, 6, 23, 5, 10, 23, 9], "vertices": [1, 4, -51.75, 54.88, 1, 3, 65, -120.43, 49.22, 0.01563, 64, 2.75, 62.81, 0.30937, 4, -50.96, 15.3, 0.675, 3, 65, -60.08, 58.81, 0.10063, 64, 63.8, 65.36, 0.55875, 4, -64.13, -44.37, 0.34062, 4, 66, -70.69, 76.74, 0.01563, 65, 10.26, 66.08, 0.325, 64, 134.51, 64.46, 0.55875, 4, -83.09, -112.5, 0.10063, 4, 66, -23.04, 69.52, 0.10063, 65, 58.45, 65.61, 0.55875, 64, 182.32, 58.42, 0.325, 4, -101.16, -157.17, 0.01563, 3, 66, 26.01, 67.19, 0.34062, 65, 107.34, 70.17, 0.55875, 64, 231.42, 57.29, 0.10063, 3, 66, 67.42, 71.48, 0.65417, 65, 147.74, 80.22, 0.33021, 64, 272.71, 62.6, 0.01563, 2, 66, 112.78, 76.26, 0.85556, 65, 191.98, 91.3, 0.14444, 2, 66, 92.73, -15.61, 0.87083, 65, 185, -2.47, 0.12917, 2, 66, 11.49, -69.63, 0.85556, 65, 112.12, -67.34, 0.14444, 3, 66, -10.34, -62.29, 0.65417, 65, 89.48, -63.12, 0.33021, 64, 198.27, -73.04, 0.01563, 3, 66, -28.98, -54.75, 0.34062, 65, 69.96, -58.27, 0.55875, 64, 179.44, -65.97, 0.10063, 4, 66, -59.66, -44.42, 0.10063, 65, 38.14, -52.33, 0.55875, 64, 148.52, -56.39, 0.325, 4, -203.49, -95.12, 0.01563, 4, 66, -101.94, -43.34, 0.01563, 65, -3.88, -57.19, 0.325, 64, 106.22, -56.36, 0.55875, 4, -192.64, -54.24, 0.10063, 3, 65, -63.91, -40.42, 0.10063, 64, 48.53, -32.77, 0.55875, 4, -155.07, -4.5, 0.34062, 3, 65, -120.94, -37.65, 0.01563, 64, -7.8, -23.42, 0.30937, 4, -131.62, 47.55, 0.675, 1, 4, -117.45, 68.4, 1, 1, 4, -80.99, 69.13, 1, 3, 65, -114.76, -2.34, 0.008, 64, 2.42, 10.94, 0.312, 4, -101.03, 28.89, 0.68, 3, 65, -57.61, 0.01, 0.093, 64, 59.46, 6.67, 0.579, 4, -119.75, -25.16, 0.328, 4, 66, -88.8, 11.75, 0.008, 65, 1.42, -0.8, 0.32, 64, 118, -0.96, 0.579, 4, -142.1, -79.81, 0.093, 4, 66, -43.26, 0.21, 0.093, 65, 48.13, -5.84, 0.579, 64, 163.82, -11.37, 0.32, 4, -163.88, -121.43, 0.008, 3, 66, -0.98, -7.11, 0.328, 65, 91.01, -7.18, 0.579, 64, 206.26, -17.65, 0.093, 3, 66, 36.86, -13.97, 0.67, 65, 129.45, -8.67, 0.322, 64, 244.26, -23.57, 0.008], "hull": 18, "edges": [30, 36, 36, 2, 2, 0, 0, 34, 32, 34, 32, 30, 34, 36, 36, 38, 38, 40, 40, 6, 6, 4, 4, 2, 4, 38, 38, 28, 28, 26, 26, 40, 28, 30, 40, 42, 42, 44, 44, 10, 10, 8, 8, 6, 8, 42, 42, 24, 24, 22, 22, 44, 24, 26, 44, 46, 46, 16, 16, 14, 14, 12, 12, 10, 12, 46, 46, 20, 20, 18, 18, 16, 20, 22], "width": 197, "height": 359}}, "jj_25": {"jj_25": {"type": "mesh", "uvs": [0.60738, 0.12226, 0.74927, 0.36768, 0.86609, 0.5843, 1, 0.77503, 0.88215, 0.85216, 0.68671, 0.92508, 0.39757, 1, 0.1111, 1, 0.04952, 0.72173, 0, 0.43639, 0, 0.22183, 0, 0.03251, 0.14689, 0.02269, 0.2861, 0.02129, 0.3932, 0, 0.51367, 0, 0.34768, 0.16854, 0.16831, 0.20781, 0.49226, 0.1419, 0.44674, 0.41676, 0.22185, 0.4406, 0.62076, 0.3831, 0.72955, 0.62076, 0.55285, 0.65862, 0.29315, 0.71191], "triangles": [2, 21, 1, 22, 2, 3, 2, 22, 21, 23, 19, 22, 23, 22, 4, 5, 23, 4, 4, 22, 3, 8, 9, 20, 7, 8, 24, 24, 23, 5, 8, 20, 24, 23, 24, 20, 5, 6, 24, 6, 7, 24, 21, 18, 0, 18, 15, 0, 16, 18, 21, 13, 14, 18, 17, 20, 10, 17, 16, 19, 10, 11, 17, 11, 12, 17, 16, 12, 13, 16, 17, 12, 16, 13, 18, 18, 14, 15, 19, 20, 17, 20, 9, 10, 23, 20, 19, 1, 21, 0, 19, 21, 22, 19, 16, 21], "vertices": [5, 4, 146.44, 28.32, 0.6775, 61, -36.61, 88.24, 0.01612, 60, -202.23, 34.36, 0.01563, 59, -104.67, 18.02, 0.085, 58, -4.97, 20.07, 0.20575, 6, 4, 179.71, -74.11, 0.33812, 62, -12.36, 113.82, 0.0156, 61, 68.21, 112.96, 0.02612, 60, -94.76, 27.28, 0.09542, 59, 3.02, 19.58, 0.22711, 58, 102.73, 19.52, 0.29763, 7, 4, 207.22, -164.56, 0.10063, 63, -28.99, 130.81, 0.02115, 62, 81.01, 128.65, 0.0256, 61, 160.62, 132.94, 0.01612, 60, -0.56, 19.29, 0.27316, 59, 97.56, 19.19, 0.33676, 58, 197.24, 17.27, 0.22659, 6, 4, 238.28, -244.06, 0.02083, 63, 55.14, 145.2, 0.05472, 62, 164.03, 148.48, 0.026, 60, 84.79, 18.66, 0.42074, 59, 182.69, 25.4, 0.35409, 58, 282.47, 21.81, 0.12361, 7, 4, 213.01, -276.97, 0.01, 63, 82.27, 113.8, 0.12787, 62, 193.15, 118.93, 0.07473, 61, 273.12, 129.45, 0.01612, 60, 105.98, -17.02, 0.38206, 59, 206.67, -8.46, 0.29701, 58, 305.78, -12.51, 0.09221, 7, 4, 170.63, -308.45, 0.01, 63, 104.56, 65.95, 0.2565, 62, 218.51, 72.63, 0.18528, 61, 301, 84.62, 0.04862, 60, 119.51, -68.04, 0.25996, 59, 224.25, -58.23, 0.19076, 58, 322.38, -62.63, 0.04888, 7, 4, 107.66, -341.18, 0.01, 63, 123.92, -2.33, 0.37884, 62, 242.28, 5.75, 0.29396, 61, 328.44, 19.17, 0.09195, 60, 126.56, -138.66, 0.13109, 59, 236.94, -128.06, 0.07778, 58, 333.7, -132.69, 0.01638, 6, 4, 44.65, -342.44, 0.02083, 63, 112.44, -64.3, 0.41907, 62, 234.86, -56.83, 0.35193, 61, 324.51, -43.73, 0.12361, 60, 104.36, -197.65, 0.05639, 59, 219.55, -188.64, 0.02817, 7, 4, 28.77, -225.86, 0.10063, 63, -4.94, -56.34, 0.27212, 62, 117.21, -56.54, 0.33546, 61, 207.02, -49.96, 0.22633, 60, -9.79, -169.17, 0.02219, 59, 103.48, -169.41, 0.0269, 58, 199.45, -171.41, 0.01638, 6, 4, 15.48, -106.26, 0.33812, 63, -124.77, -45.23, 0.09542, 62, -3.08, -53.27, 0.22581, 61, 86.73, -53.35, 0.29737, 59, -14.72, -146.82, 0.0169, 58, 81.72, -146.5, 0.02638, 5, 4, 13.68, -16.17, 0.6775, 63, -213.38, -28.82, 0.01563, 62, -92.57, -42.67, 0.085, 61, -3.21, -47.73, 0.20549, 58, -4.39, -119.95, 0.01638, 1, 4, 12.09, 63.33, 1, 1, 4, 44.32, 68.1, 1, 1, 4, 74.93, 69.3, 1, 1, 4, 98.3, 78.71, 1, 1, 4, 124.8, 79.24, 1, 7, 4, 89.71, 7.74, 0.684, 63, -221.46, 50.47, 0.0039, 62, -105.8, 35.92, 0.03552, 61, -20.78, 30, 0.11765, 60, -204.15, -25.96, 0.0041, 59, -101.75, -42.26, 0.03648, 58, -3.24, -40.26, 0.11835, 6, 4, 50.58, -9.54, 0.684, 63, -212.42, 8.66, 0.008, 62, -94.07, -5.21, 0.06602, 61, -6.78, -10.41, 0.18131, 59, -96.78, -84.74, 0.01248, 58, 0.89, -82.83, 0.04819, 6, 4, 121.28, 19.57, 0.684, 62, -113.18, 68.82, 0.01152, 61, -29.96, 62.45, 0.04781, 60, -203.43, 7.75, 0.008, 59, -103.73, -8.6, 0.06698, 58, -4.56, -6.56, 0.18169, 7, 4, 113.58, -96.05, 0.324, 63, -114.98, 52.91, 0.03869, 62, 0.29, 45.31, 0.12104, 61, 84.63, 45.25, 0.17558, 60, -98.9, -42.28, 0.03931, 59, 4.47, -50.08, 0.12496, 58, 102.81, -50.16, 0.17642, 7, 4, 64.31, -107.06, 0.324, 63, -114.14, 2.44, 0.07119, 62, 4.41, -5.01, 0.19054, 61, 91.54, -4.75, 0.27061, 60, -106.95, -92.11, 0.01331, 59, 0.45, -100.4, 0.04896, 58, 97.8, -100.39, 0.08139, 7, 4, 151.57, -81.16, 0.324, 63, -121.91, 93.13, 0.01269, 62, -9.25, 84.99, 0.04704, 61, 72.91, 84.34, 0.08061, 60, -98.65, -1.47, 0.07181, 59, 1.45, -9.38, 0.19246, 58, 100.58, -9.41, 0.27139, 7, 4, 177.5, -180.47, 0.0865, 63, -19.4, 98.49, 0.06027, 62, 92.69, 97.02, 0.08312, 61, 174.03, 102, 0.04781, 60, 3.2, -14.22, 0.2319, 59, 103.99, -13.91, 0.29871, 58, 203.02, -15.95, 0.19169, 7, 4, 138.95, -197.15, 0.08, 63, -10.84, 57.37, 0.14694, 62, 103.91, 56.54, 0.1865, 61, 187.47, 62.21, 0.12261, 60, 4.39, -56.2, 0.14956, 59, 108.55, -55.67, 0.191, 58, 206.76, -57.79, 0.12339, 7, 4, 82.28, -220.67, 0.0865, 63, 0.76, -2.88, 0.23043, 62, 119.42, -2.83, 0.29445, 61, 206.25, 3.8, 0.19131, 60, 5.22, -117.55, 0.06173, 59, 114.3, -116.76, 0.08738, 58, 211.31, -118.98, 0.04819], "hull": 16, "edges": [26, 32, 32, 34, 34, 20, 20, 22, 22, 24, 24, 34, 24, 26, 26, 28, 28, 30, 30, 0, 0, 36, 36, 32, 32, 38, 38, 40, 18, 20, 40, 18, 34, 40, 38, 42, 42, 2, 2, 0, 28, 36, 36, 42, 42, 44, 44, 4, 4, 2, 38, 46, 46, 48, 48, 16, 16, 18, 40, 48, 48, 12, 12, 14, 14, 16, 46, 10, 10, 12, 44, 46, 44, 8, 8, 6, 6, 4, 8, 10], "width": 220, "height": 420}}, "jj_biyan": {"jj_biyan": {"x": 116.92, "y": 29.97, "rotation": -77.76, "width": 141, "height": 59}}, "jj_27": {"jj_27": {"type": "mesh", "uvs": [0.76287, 0.07857, 1, 0.21468, 1, 0.32213, 0.9342, 0.37049, 0.91391, 0.48869, 0.65885, 0.62369, 0.55478, 0.71843, 0.53811, 0.7555, 0.54439, 0.78427, 0.56326, 0.83717, 0.45784, 0.86714, 0.37906, 0.96058, 0.23259, 1, 0.0617, 1, 0, 0.9835, 0.1083, 0.87154, 0.22593, 0.84069, 0.33578, 0.76664, 0.36978, 0.733, 0.38145, 0.68665, 0.41988, 0.53772, 0.37737, 0.32751, 0.45627, 0.18244, 0.43598, 0.07677, 0.55096, 0, 0.69975, 0.15916, 0.63662, 0.28273, 0.59154, 0.4672, 0.49428, 0.62369, 0.45811, 0.71711, 0.44311, 0.75948, 0.42899, 0.80367, 0.28066, 0.88354, 0.16157, 0.90593], "triangles": [25, 0, 1, 25, 24, 0, 23, 24, 25, 25, 1, 2, 30, 29, 7, 7, 29, 6, 18, 19, 29, 29, 28, 6, 6, 28, 5, 29, 19, 28, 19, 20, 28, 5, 28, 27, 28, 20, 27, 5, 27, 4, 20, 21, 27, 4, 27, 3, 27, 26, 3, 27, 21, 26, 2, 3, 25, 21, 22, 26, 3, 26, 25, 26, 22, 25, 22, 23, 25, 30, 18, 29, 33, 16, 32, 33, 15, 16, 33, 13, 15, 13, 33, 12, 12, 32, 11, 13, 14, 15, 11, 32, 10, 32, 31, 10, 10, 31, 8, 8, 31, 30, 17, 18, 31, 8, 30, 7, 31, 18, 30, 10, 8, 9, 12, 33, 32, 32, 17, 31, 32, 16, 17], "vertices": [2, 56, -19.52, 35.18, 0.28, 55, 173.58, 22.53, 0.72, 2, 56, 11.74, 94.1, 0.28, 55, 174.77, 89.22, 0.72, 2, 56, 42.29, 98.34, 0.552, 55, 200.09, 106.83, 0.448, 2, 56, 58.1, 85.39, 0.728, 55, 220.04, 102.44, 0.272, 2, 56, 92.34, 85.47, 0.872, 55, 250.53, 118.02, 0.128, 2, 57, -55.4, -10.39, 0.064, 56, 138.7, 33.19, 0.936, 2, 57, -20.31, -1.98, 0.272, 56, 168.9, 13.42, 0.728, 2, 57, -11.04, 4.49, 0.496, 56, 179.96, 11.12, 0.504, 2, 57, -7.43, 12.05, 0.672, 56, 187.94, 13.67, 0.328, 1, 57, -2.14, 26.92, 1, 1, 57, 22.44, 20.01, 1, 1, 57, 52.61, 31.46, 1, 1, 57, 86.39, 21.33, 1, 1, 57, 118.15, -1.25, 1, 1, 57, 126.87, -13.26, 1, 1, 57, 88.12, -25.14, 1, 2, 57, 61.14, -16.82, 0.912, 56, 213.96, -56.02, 0.088, 2, 57, 28.41, -19.63, 0.672, 56, 189.47, -34.13, 0.328, 2, 57, 16.5, -23.01, 0.496, 56, 178.83, -27.78, 0.504, 2, 57, 6.62, -32.31, 0.272, 56, 165.3, -26.98, 0.728, 2, 57, -25.28, -62.07, 0.064, 56, 121.75, -24.17, 0.936, 2, 56, 63.33, -42.06, 0.872, 55, 282.42, -8.82, 0.128, 2, 56, 19.61, -29.96, 0.728, 55, 237.97, -17.83, 0.272, 2, 56, -9.79, -38.71, 0.552, 55, 215.71, -38.95, 0.448, 2, 56, -35.22, -15.77, 0.28, 55, 182.66, -30.01, 0.72, 2, 56, 5.37, 24.1, 0.552, 55, 200.78, 23.92, 0.448, 2, 56, 42.47, 14.72, 0.728, 55, 238.11, 32.36, 0.272, 2, 56, 96.33, 11.81, 0.872, 55, 287.45, 54.16, 0.128, 2, 57, -24.82, -32.13, 0.064, 56, 143.86, -3.98, 0.936, 2, 57, -2.56, -15.06, 0.272, 56, 171.55, -8.46, 0.728, 2, 57, 7.27, -7.13, 0.496, 56, 184.06, -10.18, 0.504, 2, 57, 17.24, 1.35, 0.672, 56, 197.07, -11.62, 0.328, 2, 57, 58.09, 0.44, 0.912, 56, 224.42, -41.97, 0.088, 1, 57, 83.94, -10.06, 1], "hull": 25, "edges": [46, 50, 50, 4, 4, 2, 2, 0, 0, 48, 48, 46, 0, 50, 50, 52, 52, 44, 44, 46, 52, 6, 6, 4, 52, 54, 54, 42, 42, 44, 54, 8, 8, 6, 54, 56, 56, 40, 40, 42, 56, 10, 10, 8, 56, 58, 58, 60, 60, 14, 14, 12, 12, 58, 58, 38, 38, 36, 36, 60, 38, 40, 12, 10, 60, 62, 62, 16, 16, 14, 16, 18, 18, 20, 20, 62, 62, 34, 34, 32, 22, 20, 32, 64, 64, 22, 62, 64, 64, 66, 66, 26, 26, 28, 28, 30, 30, 32, 30, 66, 24, 26, 66, 24, 24, 22, 36, 34], "width": 228, "height": 287}}, "jj_28": {"jj_28": {"type": "mesh", "uvs": [0.66436, 0.06822, 0.72803, 0.18087, 0.82449, 0.27318, 0.75697, 0.49602, 0.73575, 0.60276, 0.76083, 0.67598, 0.77047, 0.711, 0.78228, 0.73523, 0.90674, 0.81827, 1, 0.88168, 0.92564, 1, 0.64353, 0.90132, 0.69046, 0.85362, 0.59048, 0.79471, 0.60578, 0.7689, 0.58719, 0.74071, 0.58526, 0.711, 0.5351, 0.65051, 0.3904, 0.55119, 0.11066, 0.45251, 0.06628, 0.38036, 0, 0.20616, 0, 0.12446, 0.29008, 0, 0.63928, 0, 0.36339, 0.10642, 0.44249, 0.26682, 0.55054, 0.41431, 0.62964, 0.56181, 0.67401, 0.66537, 0.6933, 0.74177, 0.68618, 0.71357, 0.7078, 0.76385, 0.79044, 0.82669, 0.86899, 0.90581], "triangles": [25, 23, 24, 22, 23, 25, 21, 22, 25, 25, 24, 0, 1, 26, 25, 1, 25, 0, 21, 25, 26, 20, 21, 26, 2, 27, 26, 2, 26, 1, 19, 20, 26, 27, 19, 26, 3, 27, 2, 18, 19, 27, 28, 18, 27, 3, 28, 27, 4, 28, 3, 17, 18, 28, 29, 17, 28, 4, 29, 28, 29, 4, 5, 16, 17, 29, 31, 29, 5, 31, 5, 6, 16, 29, 31, 15, 16, 31, 30, 31, 6, 15, 31, 30, 33, 7, 8, 12, 32, 33, 12, 13, 32, 34, 33, 8, 34, 8, 9, 12, 33, 34, 11, 12, 34, 10, 34, 9, 11, 34, 10, 32, 7, 33, 32, 13, 14, 32, 30, 7, 14, 15, 30, 14, 30, 32, 30, 6, 7], "vertices": [2, 53, -1.21, 28.62, 0.552, 52, 256.77, 26.65, 0.448, 2, 53, 40.65, 35.78, 0.72, 52, 297.93, 16.2, 0.28, 2, 53, 76.11, 50.34, 0.872, 52, 336.25, 15.11, 0.128, 1, 53, 153.87, 26.53, 1, 2, 54, -40.02, 33.32, 0.088, 53, 191.41, 17.31, 0.912, 2, 54, -14.4, 25.36, 0.272, 53, 218.19, 18.76, 0.728, 2, 54, -2.37, 21.14, 0.504, 53, 230.94, 19, 0.496, 2, 54, 6.43, 19.12, 0.712, 53, 239.9, 20.16, 0.288, 1, 54, 44.39, 26.89, 1, 1, 54, 73.21, 32.52, 1, 1, 54, 103.94, -0.44, 1, 1, 54, 46.39, -33.16, 1, 1, 54, 35.58, -16.9, 1, 1, 54, 7.56, -24.46, 1, 2, 54, 0.77, -17.43, 0.712, 53, 247.31, -16.07, 0.288, 2, 54, -9.92, -15.93, 0.504, 53, 236.77, -18.39, 0.496, 2, 54, -19.55, -11.26, 0.272, 53, 226.12, -17.36, 0.728, 2, 54, -43.44, -9.84, 0.088, 53, 203.22, -24.34, 0.912, 1, 53, 164.01, -48.03, 1, 2, 53, 121.51, -98.27, 0.872, 52, 317.41, -139.13, 0.128, 2, 53, 94.6, -103.56, 0.72, 52, 290.67, -133.04, 0.28, 2, 53, 30.71, -108.32, 0.552, 52, 230.35, -111.46, 0.448, 2, 53, 1.55, -104.45, 0.24, 52, 205.28, -96.08, 0.76, 2, 53, -35.31, -41.61, 0.24, 52, 197.1, -23.7, 0.76, 2, 53, -26.21, 26.93, 0.24, 52, 233.24, 35.25, 0.76, 2, 53, 4.58, -32.27, 0.552, 52, 237.35, -31.35, 0.448, 2, 53, 63.88, -24.34, 0.72, 52, 294.76, -48.17, 0.28, 2, 53, 119.33, -10.12, 0.872, 52, 351.21, -57.69, 0.128, 1, 53, 174.03, -1.58, 1, 2, 54, -25.83, 11.96, 0.088, 53, 212.15, 2.22, 0.912, 2, 54, 0.26, 2.45, 0.504, 53, 239.91, 2.39, 0.496, 2, 54, -9.37, 5.96, 0.272, 53, 229.67, 2.33, 0.728, 2, 54, 8.63, 1.27, 0.712, 53, 248.17, 4.19, 0.288, 1, 54, 36.28, 5.13, 1, 1, 54, 68.73, 5.53, 1], "hull": 25, "edges": [0, 50, 50, 42, 42, 44, 44, 46, 46, 48, 48, 0, 46, 50, 50, 52, 52, 40, 40, 42, 52, 2, 2, 0, 52, 54, 54, 38, 38, 40, 54, 4, 4, 2, 54, 56, 56, 36, 36, 38, 56, 6, 6, 4, 56, 58, 60, 30, 30, 32, 58, 62, 62, 60, 32, 62, 62, 10, 10, 12, 12, 60, 10, 8, 8, 58, 58, 34, 36, 34, 34, 32, 6, 8, 60, 64, 64, 28, 28, 30, 64, 14, 14, 12, 28, 26, 26, 24, 24, 66, 66, 16, 16, 14, 64, 66, 66, 68, 68, 22, 24, 22, 68, 18, 18, 16, 22, 20, 20, 18], "width": 198, "height": 360}}, "yj_20": {"yj_20": {"type": "mesh", "uvs": [0.60319, 0.09702, 0.72846, 0.33162, 0.87453, 0.52507, 1, 0.6373, 0.85316, 1, 0.08477, 0.84825, 0.11343, 0.66705, 0.0935, 0.4763, 0.04443, 0.1983, 0, 0.02799, 0.30157, 0, 0.55814, 0, 0.35187, 0.17989, 0.45781, 0.46745, 0.61834, 0.71626], "triangles": [7, 12, 13, 1, 13, 12, 7, 8, 12, 1, 12, 0, 8, 10, 12, 8, 9, 10, 12, 11, 0, 12, 10, 11, 14, 13, 1, 6, 7, 13, 14, 1, 2, 5, 6, 14, 6, 13, 14, 3, 14, 2, 4, 5, 14, 3, 4, 14], "vertices": [4, 113, -79.96, 55.97, 0.01563, 112, -37.88, 50.74, 0.085, 111, 2.49, 40.5, 0.22437, 70, 54.61, -3.85, 0.675, 4, 113, -38.76, 49.11, 0.09542, 112, 3.68, 46.53, 0.24521, 111, 43.88, 46.1, 0.31875, 70, 63.12, -44.74, 0.34062, 4, 113, -1, 48.63, 0.29681, 112, 41.39, 48.47, 0.35736, 111, 80.1, 56.79, 0.24521, 70, 76.34, -80.12, 0.10063, 4, 113, 24.51, 53.28, 0.46852, 112, 66.56, 54.74, 0.38704, 111, 103.11, 68.76, 0.12361, 70, 89.91, -102.22, 0.02083, 4, 113, 58.05, 1.45, 0.49299, 112, 103.34, 5.16, 0.37785, 111, 150.44, 29.14, 0.11917, 70, 53.74, -152.24, 0.01, 4, 113, -29.49, -77.32, 0.46852, 112, 21.02, -79.05, 0.38704, 111, 90.06, -71.95, 0.12361, 70, -51.38, -99.15, 0.02083, 4, 113, -50.2, -56.83, 0.29681, 112, -0.96, -59.93, 0.35736, 111, 64.23, -58.5, 0.24521, 70, -39.78, -72.43, 0.10063, 4, 113, -76.44, -41.34, 0.09542, 112, -28.14, -46.14, 0.24521, 111, 34.58, -51.44, 0.31875, 70, -34.83, -42.36, 0.34062, 4, 113, -116.46, -21.18, 0.01563, 112, -69.36, -28.58, 0.085, 111, -9.61, -43.99, 0.22437, 70, -30.52, 2.24, 0.675, 1, 70, -29.96, 30.13, 1, 1, 70, 14.89, 22.74, 1, 1, 70, 52.06, 12.79, 1, 4, 113, -91.59, 17.77, 0.008, 112, -47.04, 11.87, 0.085, 111, 2.66, 0.56, 0.227, 70, 14.78, -6.84, 0.68, 4, 113, -45.31, 3.59, 0.091, 112, 0.06, 0.69, 0.237, 111, 51.06, 0.68, 0.344, 70, 18.32, -55.12, 0.328, 4, 113, 0.84, -0.33, 0.29183, 112, 46.36, -0.28, 0.37817, 111, 96.31, 10.55, 0.237, 70, 31.36, -99.56, 0.093], "hull": 12, "edges": [0, 24, 24, 16, 16, 18, 18, 20, 20, 22, 22, 0, 20, 24, 24, 26, 26, 28, 28, 8, 0, 2, 2, 26, 26, 14, 14, 16, 2, 4, 4, 6, 4, 28, 28, 12, 12, 14, 12, 10, 6, 8, 10, 8], "width": 150, "height": 159}}, "yj_21": {"yj_21": {"type": "mesh", "uvs": [0.71826, 0.01959, 0.8004, 0.02962, 0.80698, 0.08866, 0.80428, 0.2222, 0.80205, 0.33262, 0.80326, 0.43637, 0.80474, 0.56406, 0.81692, 0.69632, 0.85942, 0.83293, 0.83664, 0.84512, 0.83745, 0.87794, 0.86165, 0.9135, 0.9399, 0.93812, 1, 0.99118, 0.91812, 1, 0.87698, 0.96109, 0.81163, 0.92718, 0.78904, 0.87904, 0.77372, 0.83582, 0.70992, 0.79951, 0.70432, 0.81675, 0.70512, 0.85121, 0.71319, 0.88513, 0.72852, 0.91795, 0.78096, 0.96171, 0.68415, 0.96827, 0.66479, 0.92506, 0.65591, 0.88732, 0.65107, 0.8534, 0.65349, 0.81402, 0.63072, 0.78703, 0.58764, 0.79214, 0.56908, 0.8255, 0.56989, 0.85778, 0.58118, 0.9081, 0.61345, 0.97374, 0.50697, 0.93381, 0.50213, 0.90099, 0.50535, 0.8534, 0.51019, 0.8162, 0.51665, 0.78338, 0.4381, 0.75668, 0.41102, 0.76157, 0.40293, 0.79193, 0.40542, 0.82314, 0.39361, 0.85813, 0.38117, 0.88976, 0.29784, 0.88006, 0.31525, 0.84464, 0.33391, 0.81723, 0.34697, 0.78224, 0.3563, 0.74935, 0.32196, 0.71403, 0.29065, 0.71457, 0.27132, 0.74871, 0.26172, 0.78489, 0.24467, 0.82673, 0.24467, 0.88064, 0.14115, 0.85531, 0.1817, 0.81117, 0.20622, 0.77187, 0.2265, 0.73207, 0.2379, 0.69546, 0.22959, 0.66834, 0.20096, 0.67364, 0.17501, 0.70867, 0.15298, 0.74149, 0.11753, 0.77754, 0.09421, 0.8313, 0, 0.76679, 0.04757, 0.74086, 0.09981, 0.70607, 0.12283, 0.6873, 0.15906, 0.64978, 0.13745, 0.6152, 0.18469, 0.49978, 0.22452, 0.40244, 0.28155, 0.29556, 0.33548, 0.19449, 0.37326, 0.08309, 0.49319, 0.03853, 0.5244, 0, 0.60655, 0, 0.5819, 0.07195, 0.71826, 0.08866, 0.72155, 0.32816, 0.53098, 0.307, 0.40448, 0.25241, 0.43733, 0.16218, 0.55512, 0.19558, 0.7201, 0.22232, 0.6964, 0.55127, 0.46454, 0.49597, 0.30995, 0.43252, 0.35712, 0.34264, 0.49951, 0.39649, 0.70935, 0.43639, 0.27053, 0.55424, 0.45092, 0.63027, 0.70323, 0.67669, 0.36074, 0.72827, 0.40717, 0.74532, 0.5218, 0.76987, 0.58949, 0.78053, 0.65726, 0.79121, 0.29211, 0.69926, 0.24599, 0.67645, 0.21304, 0.6588, 0.76218, 0.81119], "triangles": [90, 89, 84, 3, 84, 2, 89, 83, 84, 88, 80, 83, 84, 1, 2, 84, 0, 1, 84, 83, 0, 80, 81, 83, 83, 82, 0, 83, 81, 82, 94, 87, 86, 86, 87, 89, 77, 78, 87, 87, 88, 89, 87, 78, 88, 89, 88, 83, 78, 79, 88, 88, 79, 80, 97, 93, 92, 75, 76, 93, 92, 93, 95, 93, 94, 95, 93, 76, 94, 76, 77, 94, 95, 94, 86, 94, 77, 87, 98, 102, 41, 41, 101, 98, 101, 100, 98, 100, 52, 98, 53, 105, 52, 52, 105, 98, 62, 106, 105, 105, 106, 98, 62, 63, 106, 64, 107, 63, 97, 106, 63, 63, 107, 97, 107, 73, 74, 51, 100, 101, 50, 51, 42, 51, 101, 42, 51, 52, 100, 54, 61, 53, 61, 62, 53, 53, 62, 105, 65, 72, 64, 72, 73, 64, 64, 73, 107, 42, 101, 41, 98, 106, 97, 97, 107, 74, 98, 97, 92, 74, 75, 97, 97, 75, 93, 96, 85, 4, 85, 95, 86, 4, 85, 3, 86, 90, 85, 85, 90, 3, 86, 89, 90, 3, 90, 84, 99, 91, 6, 98, 92, 91, 6, 91, 5, 91, 96, 5, 91, 92, 96, 92, 95, 96, 96, 95, 85, 96, 4, 5, 99, 6, 7, 98, 91, 99, 19, 99, 7, 17, 9, 10, 17, 18, 9, 21, 29, 20, 21, 28, 29, 8, 9, 108, 18, 19, 108, 9, 18, 108, 31, 32, 40, 20, 29, 104, 31, 40, 103, 31, 103, 30, 103, 40, 102, 40, 41, 102, 108, 7, 8, 19, 20, 104, 29, 30, 104, 108, 19, 7, 104, 99, 19, 104, 30, 99, 30, 103, 99, 103, 102, 99, 99, 102, 98, 66, 71, 65, 65, 71, 72, 68, 69, 67, 67, 69, 70, 67, 70, 66, 66, 70, 71, 60, 61, 54, 56, 59, 55, 55, 59, 60, 55, 60, 54, 57, 58, 56, 58, 59, 56, 45, 49, 44, 49, 43, 44, 49, 50, 43, 43, 50, 42, 48, 49, 45, 47, 48, 46, 46, 48, 45, 38, 39, 32, 32, 39, 40, 37, 33, 34, 37, 38, 33, 38, 32, 33, 36, 34, 35, 36, 37, 34, 27, 28, 21, 26, 22, 23, 26, 27, 22, 27, 21, 22, 25, 23, 24, 25, 26, 23, 16, 17, 10, 14, 12, 13, 16, 11, 15, 16, 10, 11, 14, 15, 12, 15, 11, 12], "vertices": [2, 133, -27.05, 9.42, 0.05, 70, -19.5, 26.89, 0.95, 2, 133, -21.86, 28.6, 0.08333, 70, -1.52, 18.43, 0.91667, 2, 133, -1.08, 28.37, 0.3, 70, -5.37, -1.99, 0.7, 4, 134, -36.57, 21.18, 0.0625, 133, 45.56, 23.68, 0.6123, 130, 18.25, 90.48, 0.0252, 70, -18.1, -47.11, 0.3, 4, 134, 2.17, 20.01, 0.3, 133, 84.13, 19.81, 0.6123, 130, 52.08, 109.4, 0.0252, 70, -28.62, -84.41, 0.0625, 4, 135, -37.91, 19.67, 0.0625, 134, 38.59, 19.69, 0.6115, 133, 120.43, 16.95, 0.3, 131, 35.04, 123.61, 0.026, 4, 135, 6.91, 19.31, 0.3, 134, 83.41, 19.31, 0.6115, 133, 165.12, 13.44, 0.0625, 131, 77.08, 139.16, 0.026, 3, 135, 53.38, 21.47, 0.6764, 134, 129.87, 21.44, 0.3, 132, 69.07, 151.86, 0.0236, 2, 136, 12.52, 12.99, 0.06667, 135, 101.48, 30.83, 0.93333, 3, 136, 15.64, 6.82, 0.27324, 143, -64.06, 160.23, 0, 135, 105.67, 25.34, 0.72676, 4, 137, -7.22, 10.08, 0.25376, 136, 26.97, 4.72, 0.43373, 143, -54.76, 167.03, 0, 135, 117.2, 25.35, 0.3125, 4, 137, 5.8, 5.67, 0.61164, 136, 40.34, 7.89, 0.32585, 143, -47.89, 178.93, 0, 135, 129.77, 30.91, 0.0625, 5, 137, 24.89, 13.24, 0.87828, 136, 52.51, 24.42, 0.12172, 138, 46.56, 63.56, 0, 143, -51.57, 199.13, 0, 135, 138.7, 49.39, 0, 5, 137, 48.24, 10.81, 0.98822, 136, 73.61, 34.74, 0.01178, 138, 64.19, 79.08, 0, 143, -44.6, 221.55, 0, 135, 157.55, 63.4, 0, 5, 137, 37.08, -5.47, 0.97179, 136, 72.77, 15.03, 0.02821, 138, 68.58, 59.84, 0, 143, -30.84, 207.42, 0, 135, 160.34, 43.86, 0, 5, 137, 20.43, -3.17, 0.87861, 136, 57.44, 8.14, 0.12139, 138, 55.6, 49.16, 0, 143, -36.35, 191.54, 0, 135, 146.53, 34.29, 0, 5, 137, 1.09, -6.27, 0.587, 136, 42.68, -4.73, 0.3505, 138, 44.76, 32.85, 0, 143, -37.1, 171.97, 0, 135, 134.38, 18.93, 0.0625, 4, 137, -14.86, 1.46, 0.2539, 136, 25.06, -6.65, 0.43359, 143, -47.81, 157.84, 0, 135, 117.4, 13.82, 0.31251, 2, 136, 9.47, -7.21, 0.28259, 135, 102.17, 10.41, 0.71741, 4, 136, -6.04, -19.56, 0.0431, 138, 1.66, 5.72, 0.0431, 135, 89.19, -4.56, 0.88124, 132, 110.5, 136.26, 0.03255, 3, 138, 7.79, 4.79, 0.28185, 143, -54.04, 128.76, 0, 135, 95.22, -5.99, 0.71815, 4, 139, -7.66, 9.52, 0.23073, 138, 19.84, 5.78, 0.45677, 143, -44.27, 135.89, 0, 135, 107.31, -5.99, 0.3125, 4, 139, 4.22, 7.43, 0.5661, 138, 31.59, 8.49, 0.3714, 143, -35.65, 144.32, 0, 135, 119.25, -4.26, 0.0625, 3, 139, 16.3, 7.1, 0.85573, 138, 42.84, 12.9, 0.14427, 143, -28.34, 153.95, 0, 3, 139, 34.9, 13.86, 0.97358, 138, 57.34, 26.37, 0.02642, 143, -22.99, 173, 0, 3, 139, 29.52, -8.66, 0.97191, 138, 61.17, 3.53, 0.02809, 143, -7.82, 155.51, 0, 3, 139, 13.68, -8.04, 0.85573, 138, 46.34, -2.07, 0.14427, 143, -17.56, 142.99, 0, 4, 139, 0.47, -5.7, 0.56359, 138, 33.26, -5.06, 0.37391, 143, -27.16, 133.63, 0, 135, 119.8, -17.91, 0.0625, 4, 139, -11.15, -2.88, 0.23073, 138, 21.46, -7, 0.45677, 143, -36.22, 125.82, 0, 135, 107.88, -18.87, 0.3125, 3, 138, 7.63, -7.35, 0.28992, 143, -47.85, 118.32, 0, 135, 94.06, -18.07, 0.71008, 4, 138, -1.46, -13.39, 0.05692, 140, 3.08, 19.44, 0.05633, 143, -52.46, 108.43, 0, 135, 84.51, -23.34, 0.88675, 6, 138, 1.01, -23.5, 0.00061, 140, 6.3, 9.54, 0.25464, 146, 19.74, 50.25, 0.00028, 143, -45.08, 101.09, 0, 135, 86.14, -33.62, 0.60283, 132, 115.28, 107.44, 0.14163, 7, 138, 12.99, -27.12, 0.00118, 141, -8.9, 8.53, 0.19945, 140, 18.52, 6.83, 0.42611, 146, 31.6, 46.24, 0.00027, 143, -32.97, 104.23, 0, 135, 97.78, -38.22, 0.285, 132, 127.72, 106.09, 0.088, 6, 138, 24.28, -26.18, 0.00065, 141, 2.43, 8.32, 0.55585, 140, 29.71, 8.62, 0.38071, 146, 42.92, 46.82, 0.00029, 143, -23.82, 110.92, 0, 135, 109.11, -38.21, 0.0625, 4, 138, 41.73, -22.32, 0.00032, 141, 20.17, 10.38, 0.84369, 140, 46.82, 13.78, 0.15599, 143, -10.94, 123.31, 0, 3, 141, 43.47, 17.23, 0.97477, 140, 68.54, 24.64, 0.02523, 143, 3.45, 142.87, 0, 3, 141, 28.56, -7.6, 0.96268, 140, 58.25, -2.43, 0.03732, 143, 6.62, 114.08, 0, 4, 141, 17.01, -8.34, 0.84369, 140, 47.01, -5.2, 0.15617, 146, 58.63, 31.23, 0.00015, 143, -2.13, 106.49, 0, 6, 138, 23.77, -41.61, 0.00065, 141, 0.34, -6.97, 0.53772, 140, 30.36, -6.8, 0.39884, 146, 41.91, 31.42, 0.00029, 143, -16.22, 97.49, 0, 135, 107.33, -53.55, 0.0625, 7, 138, 10.67, -41.33, 0.00061, 141, -12.66, -5.36, 0.20551, 140, 17.27, -7.51, 0.43939, 146, 28.82, 32.12, 0.00055, 143, -27.55, 90.9, 0, 135, 94.29, -52.19, 0.26594, 132, 128.07, 91.7, 0.088, 6, 138, -0.93, -40.56, 0.00059, 140, 5.65, -7.62, 0.25349, 146, 17.26, 33.26, 0.00027, 143, -37.85, 85.51, 0, 135, 82.8, -50.47, 0.48982, 132, 116.53, 90.31, 0.25583, 3, 146, 8.53, 14.25, 0.10143, 135, 73.13, -69.01, 0.32615, 132, 112.13, 69.86, 0.57242, 3, 146, 10.47, 7.87, 0.29774, 135, 74.74, -75.48, 0.2, 132, 115.4, 64.05, 0.50226, 3, 147, -9.42, 4.57, 0.06463, 146, 21.19, 6.31, 0.64633, 132, 126.2, 64.86, 0.28904, 4, 147, 1.11, 7.64, 0.31, 146, 32.11, 7.28, 0.62, 135, 96.33, -77.16, 0.008, 132, 136.66, 68.17, 0.062, 2, 147, 13.71, 7.71, 0.6875, 146, 44.49, 4.9, 0.3125, 2, 147, 25.19, 7.35, 0.91667, 146, 55.68, 2.32, 0.08333, 2, 147, 26.4, -12.73, 0.91667, 146, 52.96, -17.62, 0.08333, 2, 147, 13.35, -11.53, 0.6875, 146, 40.4, -13.9, 0.3125, 3, 147, 2.97, -9.4, 0.3125, 146, 30.63, -9.8, 0.625, 132, 138.91, 51.17, 0.0625, 3, 147, -9.7, -9.17, 0.0625, 146, 18.24, -7.12, 0.625, 132, 126.24, 51.11, 0.3125, 3, 146, 6.63, -5.29, 0.31097, 135, 70.25, -88.44, 0.064, 132, 114.51, 50.37, 0.62503, 4, 146, -5.48, -13.89, 0.0572, 144, 11.73, 17.04, 0.05611, 143, -31.02, 33.62, 0, 132, 104.55, 39.36, 0.88669, 4, 146, -5.03, -21.33, 0.00034, 144, 13.68, 9.85, 0.28873, 143, -26.56, 27.64, 0, 132, 106.59, 32.19, 0.71093, 5, 146, 7.11, -25.51, 0.00062, 145, -11.39, 6.54, 0.23292, 144, 26.41, 8.21, 0.45395, 143, -14.12, 30.79, 0, 132, 119.35, 30.73, 0.3125, 5, 146, 19.88, -27.36, 0.00031, 145, 1.22, 9.28, 0.56504, 144, 39.29, 8.98, 0.37214, 143, -2.43, 36.25, 0, 132, 132.22, 31.7, 0.0625, 4, 146, 34.69, -30.91, 0.00016, 145, 16.33, 11.14, 0.85529, 144, 54.52, 8.5, 0.14455, 143, 11.91, 41.4, 0, 3, 145, 33.82, 18.38, 0.97258, 144, 72.9, 12.97, 0.02742, 143, 27.37, 52.32, 0, 3, 145, 35.02, -7.79, 0.97203, 144, 70.08, -13.07, 0.02797, 143, 34.31, 27.06, 0, 3, 145, 17.02, -4.79, 0.85442, 144, 52.75, -7.35, 0.14558, 143, 16.09, 26.01, 0, 5, 146, 15.76, -40.72, 0.00031, 145, 2.04, -4.67, 0.56421, 144, 37.96, -4.93, 0.37298, 143, 1.46, 22.82, 0, 132, 131.09, 17.76, 0.0625, 5, 146, 1.64, -36.38, 0.00031, 145, -12.71, -5.56, 0.23117, 144, 23.25, -3.54, 0.45602, 143, -12.74, 18.71, 0, 132, 116.36, 18.94, 0.3125, 4, 146, -11.3, -34.11, 0.00033, 144, 10.12, -3.93, 0.28108, 143, -24.8, 13.51, 0, 132, 103.24, 18.36, 0.7186, 3, 144, 1.34, -8.1, 0.04473, 142, 5.51, 12.42, 0.04473, 132, 94.52, 14.06, 0.91054, 2, 142, 9.98, 6.95, 0.28752, 132, 98.03, 7.93, 0.71248, 3, 143, -12.38, 3.96, 0.17037, 142, 23.72, 6.3, 0.51713, 132, 111.47, 5.02, 0.3125, 3, 143, 0.05, 6.32, 0.48636, 142, 36.38, 6.19, 0.45114, 132, 123.94, 2.83, 0.0625, 3, 143, 15.25, 6.73, 0.7926, 142, 51.37, 3.62, 0.2074, 132, 138.3, -2.17, 0, 3, 143, 33.87, 13.07, 0.9594, 142, 70.86, 6.21, 0.0406, 132, 157.96, -2.83, 0, 3, 143, 28.3, -18.3, 0.94054, 142, 59.28, -23.47, 0.05946, 132, 141.64, -30.2, 0, 3, 143, 14.34, -14.3, 0.79163, 142, 46.36, -16.82, 0.20837, 132, 130, -21.52, 0, 3, 143, -2.81, -11.19, 0.45807, 142, 30.15, -10.42, 0.47943, 132, 115.07, -12.53, 0.0625, 3, 143, -11.35, -10.51, 0.17102, 142, 21.91, -8.09, 0.51648, 132, 107.32, -8.88, 0.3125, 2, 142, 6.37, -5.56, 0.29782, 132, 92.41, -3.82, 0.70218, 3, 142, -2.63, -15.19, 0.06186, 132, 81.95, -11.84, 0.87559, 131, 147.87, -4.14, 0.06254, 2, 132, 39.91, -11.08, 0.69187, 131, 105.94, -7.31, 0.30813, 4, 134, 24.4, -117.83, 0.024, 132, 4.46, -10.45, 0.3, 131, 70.58, -9.99, 0.6135, 130, 142.03, 2.62, 0.0625, 4, 134, -12.88, -103.64, 0.024, 132, -35.26, -6.69, 0.0625, 131, 30.69, -9.96, 0.6135, 130, 102.75, -4.39, 0.3, 4, 133, 26.24, -86.64, 0.0248, 131, -7.04, -9.92, 0.3, 130, 65.61, -11.01, 0.6127, 70, -123.37, -8.88, 0.0625, 4, 133, -11.94, -74.3, 0.031, 131, -46.87, -14.74, 0.0625, 130, 27.25, -22.77, 0.6565, 70, -104.58, 26.57, 0.25, 3, 133, -25.05, -44.52, 0.0248, 130, -0.56, -5.87, 0.2752, 70, -72.97, 34.3, 0.7, 2, 130, -15.99, -6.2, 0.08333, 70, -62.3, 45.45, 0.91667, 3, 133, -36.2, -16.47, 0.0248, 130, -25.77, 10.73, 0.0252, 70, -43.41, 40.4, 0.95, 3, 133, -11.55, -24.5, 0.13952, 130, -0.96, 18.28, 0.15048, 70, -55.6, 17.52, 0.71, 3, 133, -2.9, 7.33, 0.24968, 130, -12.11, 49.32, 0.04032, 70, -25.76, 3.47, 0.71, 5, 134, 0.29, 0.88, 0.2484, 133, 80.92, 0.86, 0.54936, 131, 5.91, 92.43, 0.0416, 130, 60.31, 92.03, 0.12064, 70, -46.73, -77.95, 0.04, 5, 134, -7.89, -44.35, 0.1376, 133, 69.6, -43.69, 0.33888, 131, 14.32, 47.25, 0.1424, 130, 76.55, 49.03, 0.34112, 70, -88.62, -59.05, 0.04, 4, 134, -27.54, -74.14, 0.0384, 133, 47.92, -72.03, 0.12432, 131, 6.51, 12.42, 0.2516, 130, 75.01, 13.38, 0.58568, 4, 133, 17.04, -61.51, 0.1554, 131, -25.93, 9.03, 0.05, 130, 43.67, 4.32, 0.6821, 70, -97.02, -4.19, 0.1125, 5, 134, -46.89, -37.96, 0.0192, 133, 31.14, -34.59, 0.34384, 131, -24.42, 39.38, 0.0208, 130, 39.81, 34.45, 0.37616, 70, -72.97, -22.76, 0.24, 4, 134, -36.86, 1.15, 0.04, 133, 43.88, 3.72, 0.54936, 130, 28.3, 73.15, 0.12064, 70, -37.46, -41.97, 0.29, 5, 135, 2.02, -6.4, 0.25224, 134, 78.49, -6.4, 0.5468, 133, 158.42, -11.86, 0.04, 132, 26.95, 111.35, 0.03776, 131, 81.61, 113.38, 0.1232, 6, 135, -18.27, -61.27, 0.14336, 134, 58.17, -61.26, 0.3344, 133, 134.32, -65.17, 0.01984, 132, 21.95, 53.07, 0.13664, 131, 82.08, 54.88, 0.3456, 130, 141.9, 68.5, 0.02016, 5, 135, -41.12, -97.7, 0.04224, 134, 35.3, -97.67, 0.1168, 132, 9.59, 11.88, 0.24776, 131, 73.61, 12.72, 0.5532, 130, 141, 25.5, 0.04, 5, 134, 3.94, -85.93, 0.1168, 133, 78.49, -85.99, 0.03968, 132, -23.76, 14.85, 0.04, 131, 40.13, 12.57, 0.5532, 130, 108.07, 19.45, 0.25032, 6, 135, -53.05, -52.39, 0.02112, 134, 23.4, -52.36, 0.3344, 133, 100.25, -53.86, 0.13952, 132, -13.94, 52.39, 0.01888, 131, 46.41, 50.86, 0.3456, 130, 107.5, 58.25, 0.14048, 5, 135, -38.25, -2.68, 0.04, 134, 38.23, -2.65, 0.5468, 133, 118.51, -5.32, 0.24968, 131, 42.63, 102.59, 0.1232, 130, 94.66, 108.5, 0.04032, 3, 134, 77.86, -107.76, 0.04283, 132, 53.31, 13.48, 0.67657, 131, 116.99, 18.39, 0.2806, 4, 135, 28.81, -65.26, 0.19147, 134, 105.25, -65.28, 0.17589, 132, 68.41, 61.72, 0.45062, 131, 127.52, 67.84, 0.18202, 4, 135, 46.06, -5.47, 0.61336, 134, 122.54, -5.5, 0.2484, 132, 69.16, 123.94, 0.09664, 131, 122.46, 129.85, 0.0416, 3, 146, -0.8, -4.49, 0.19174, 135, 62.87, -87.26, 0.144, 132, 107.08, 49.54, 0.66426, 3, 146, 4.8, 6.76, 0.1792, 135, 69.02, -76.31, 0.2, 132, 110.11, 61.74, 0.6208, 3, 140, 0.78, -7.08, 0.1504, 135, 78.07, -49.17, 0.6016, 132, 111.63, 90.31, 0.248, 3, 140, 2.21, 9.4, 0.1728, 135, 82.07, -33.12, 0.6912, 132, 111.22, 106.84, 0.136, 5, 136, -11.39, -31.26, 0.0155, 138, -0.41, -6.99, 0.05262, 140, 3.64, 25.9, 0.03673, 143, -54.9, 114.44, 0, 135, 86.07, -17.05, 0.89515, 5, 146, -10.41, -21.17, 0.03914, 144, 8.38, 8.92, 0.05252, 143, -31.15, 24.83, 0, 142, 9.38, 30.43, 0.01412, 132, 101.31, 31.18, 0.89421, 5, 146, -18.04, -32.41, 0.01044, 144, 3.18, -3.64, 0.04681, 143, -31.36, 11.24, 0, 142, 6.52, 17.15, 0.03656, 132, 96.3, 18.55, 0.90618, 3, 144, -0.99, -12.72, 0.0369, 142, 4.05, 7.46, 0.04846, 132, 92.26, 9.41, 0.91465, 4, 136, 0.45, -8.18, 0.05118, 138, 4.92, 18.4, 0.02834, 135, 93.48, 7.81, 0.89909, 132, 111.36, 149.33, 0.0214], "hull": 83, "edges": [160, 166, 166, 168, 168, 4, 4, 2, 2, 0, 0, 164, 162, 164, 162, 160, 164, 166, 0, 168, 170, 8, 172, 174, 174, 156, 156, 158, 158, 160, 158, 176, 166, 178, 178, 172, 176, 178, 168, 180, 180, 170, 178, 180, 4, 6, 6, 8, 180, 6, 172, 170, 182, 12, 176, 174, 152, 186, 186, 184, 184, 182, 156, 154, 154, 152, 174, 188, 188, 186, 154, 188, 172, 190, 190, 184, 188, 190, 170, 192, 192, 182, 190, 192, 8, 10, 10, 12, 192, 10, 148, 150, 150, 152, 126, 194, 194, 186, 150, 194, 184, 196, 196, 82, 194, 196, 182, 198, 198, 38, 196, 198, 12, 14, 14, 16, 198, 14, 142, 132, 126, 128, 148, 146, 128, 146, 142, 140, 140, 138, 138, 136, 136, 134, 134, 132, 140, 134, 142, 144, 144, 146, 132, 130, 130, 128, 144, 130, 126, 124, 124, 106, 106, 104, 124, 122, 122, 120, 120, 110, 110, 108, 108, 106, 122, 108, 120, 118, 118, 116, 116, 114, 114, 112, 112, 110, 118, 112, 104, 102, 102, 84, 84, 82, 102, 100, 100, 98, 98, 88, 88, 86, 86, 84, 100, 86, 98, 96, 96, 94, 94, 92, 92, 90, 90, 88, 90, 96, 60, 62, 62, 80, 80, 82, 80, 78, 78, 76, 76, 66, 66, 64, 64, 62, 78, 64, 76, 74, 74, 72, 72, 70, 70, 68, 68, 66, 68, 74, 60, 58, 58, 40, 40, 38, 58, 56, 56, 54, 54, 44, 44, 42, 42, 40, 56, 42, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 52, 46, 38, 36, 36, 18, 18, 16, 36, 34, 34, 32, 32, 22, 22, 20, 20, 18, 34, 20, 32, 30, 30, 28, 30, 24, 24, 26, 26, 28, 24, 22, 200, 104, 102, 200, 82, 202, 202, 200, 84, 202, 204, 82, 204, 80, 60, 206, 206, 204, 62, 206, 38, 208, 208, 60, 58, 208, 104, 210, 210, 106, 126, 212, 212, 210, 212, 124, 126, 214, 214, 148, 128, 214, 16, 216, 216, 38, 36, 216], "width": 238, "height": 351}}, "yj_22": {"yj_22": {"type": "mesh", "uvs": [0.10962, 0.03074, 0.1496, 0.09666, 0.27473, 0.2296, 0.41507, 0.36382, 0.51712, 0.45163, 0.64114, 0.54996, 0.76448, 0.65303, 0.84254, 0.74242, 0.92183, 0.85119, 1, 0.97187, 1, 1, 0.95598, 1, 0.87914, 0.9093, 0.79619, 0.82884, 0.5824, 0.71474, 0.43879, 0.62438, 0.31933, 0.5379, 0.1605, 0.39837, 0.05174, 0.20168, 0.02176, 0.08481, 0, 0, 0.03651, 0, 0.09099, 0, 0.11592, 0.1658, 0.0682, 0.06616, 0.24427, 0.32927, 0.38461, 0.44222, 0.47361, 0.53269, 0.61177, 0.62438, 0.72544, 0.70369, 0.81205, 0.79308, 0.90597, 0.88695], "triangles": [18, 24, 23, 18, 19, 24, 23, 24, 1, 1, 24, 0, 19, 21, 24, 19, 20, 21, 24, 22, 0, 24, 21, 22, 18, 23, 17, 23, 1, 2, 17, 25, 16, 16, 25, 26, 26, 25, 3, 17, 23, 25, 25, 2, 3, 25, 23, 2, 27, 16, 26, 26, 3, 4, 14, 28, 29, 14, 15, 28, 15, 16, 27, 15, 27, 28, 28, 27, 5, 5, 27, 4, 27, 26, 4, 29, 28, 5, 12, 13, 30, 30, 7, 8, 14, 29, 13, 13, 29, 30, 30, 29, 7, 7, 29, 6, 6, 29, 5, 31, 12, 30, 31, 8, 9, 12, 31, 11, 31, 30, 8, 10, 11, 31, 10, 31, 9], "vertices": [2, 114, -9.8, 12.63, 0.10063, 70, 55.55, 13.33, 0.89938, 3, 115, -65.36, 6.75, 0.01563, 114, 2.05, 9.64, 0.325, 70, 60.04, 1.96, 0.65938, 3, 115, -35.18, 7.19, 0.10063, 114, 32.22, 8.75, 0.55875, 70, 76.92, -23.06, 0.34062, 4, 116, -54.67, 1.69, 0.01563, 115, -2.81, 9.34, 0.325, 114, 64.65, 9.46, 0.55875, 70, 96.43, -48.97, 0.10063, 4, 116, -32.7, 7.48, 0.10063, 115, 19.75, 12, 0.55875, 114, 87.31, 11.11, 0.325, 70, 111, -66.41, 0.01563, 4, 117, -49.34, -8.31, 0.01563, 116, -6.95, 15.32, 0.31938, 115, 46.34, 16.15, 0.56437, 114, 114.06, 14.07, 0.10063, 4, 117, -29.43, 10.22, 0.10313, 116, 19.25, 22.61, 0.54688, 115, 73.3, 19.69, 0.33438, 114, 141.15, 16.42, 0.01563, 3, 117, -13.31, 21.16, 0.34062, 116, 38.59, 24.91, 0.54062, 115, 92.78, 19.25, 0.11875, 3, 117, 5.67, 31.68, 0.55017, 116, 60.28, 25.52, 0.42951, 115, 114.34, 16.81, 0.02032, 2, 117, 26.33, 41.61, 0.56755, 116, 83.2, 24.83, 0.43245, 2, 117, 30.42, 40.69, 0.58283, 116, 86.4, 22.13, 0.41717, 3, 117, 28.66, 32.88, 0.56755, 116, 81.25, 16, 0.37007, 115, 133.76, 4.45, 0.06237, 3, 117, 12.41, 22.2, 0.55017, 116, 61.9, 14, 0.34821, 115, 114.33, 5.18, 0.10162, 4, 117, -2.6, 10.1, 0.34062, 116, 43.01, 10.17, 0.38125, 115, 95.09, 4.03, 0.2625, 114, 162.22, -0.19, 0.01563, 4, 117, -27.73, -24.13, 0.09063, 116, 4.95, -8.66, 0.26, 115, 54.76, -9.27, 0.54875, 114, 121.34, -11.69, 0.10063, 5, 117, -46.6, -46.67, 0.01563, 116, -22.18, -20, 0.085, 115, 26.31, -16.69, 0.55875, 114, 92.59, -17.84, 0.325, 70, 90.57, -87.6, 0.01563, 4, 116, -46.04, -28.34, 0.01563, 115, 1.52, -21.6, 0.325, 114, 67.61, -21.65, 0.55875, 70, 72.9, -69.53, 0.10063, 3, 115, -33.88, -25.41, 0.10063, 114, 32.07, -23.88, 0.55875, 70, 50.34, -41.98, 0.34062, 3, 115, -68.18, -16.79, 0.01563, 114, -1.81, -13.75, 0.325, 70, 38.79, -8.55, 0.65938, 2, 114, -17.07, -3.74, 0.10063, 70, 38.02, 9.68, 0.89938, 1, 70, 37.46, 22.91, 1, 1, 70, 43.88, 21.19, 1, 1, 70, 53.46, 18.63, 1, 3, 115, -63.05, -5.02, 0.008, 114, 3.84, -2.21, 0.32, 70, 51.46, -6.4, 0.672, 2, 114, -12.29, 3.77, 0.093, 70, 46.9, 10.18, 0.907, 3, 115, -29.4, -7.57, 0.093, 114, 37.34, -6.25, 0.579, 70, 67.73, -35.97, 0.328, 4, 116, -49.3, -10.07, 0.008, 115, 0.85, -3.06, 0.32, 114, 67.77, -3.09, 0.579, 70, 88.06, -58.83, 0.093, 4, 116, -28.55, -6.35, 0.091, 115, 21.91, -2.28, 0.581, 114, 88.84, -3.25, 0.32, 70, 100.22, -76.03, 0.008, 4, 117, -39.69, -15.96, 0.0225, 116, -1.91, 4.09, 0.2985, 115, 49.76, 4.32, 0.586, 114, 116.95, 2.11, 0.093, 4, 117, -23.62, 1.63, 0.10313, 116, 20.45, 12.32, 0.535, 115, 73.05, 9.33, 0.35188, 114, 140.44, 6.08, 0.01, 3, 117, -7.17, 14.09, 0.33, 116, 40.79, 15.81, 0.5225, 115, 93.68, 9.93, 0.1475, 3, 117, 10.23, 27.7, 0.566, 116, 62.5, 19.88, 0.39126, 115, 115.74, 10.92, 0.04274], "hull": 23, "edges": [2, 46, 46, 36, 40, 42, 42, 44, 44, 0, 0, 2, 42, 48, 48, 46, 0, 48, 40, 38, 38, 36, 48, 38, 46, 50, 50, 52, 52, 6, 6, 4, 4, 2, 4, 50, 50, 34, 34, 32, 32, 52, 34, 36, 52, 54, 54, 56, 56, 28, 28, 30, 30, 32, 30, 54, 54, 8, 8, 10, 10, 56, 8, 6, 56, 58, 58, 60, 60, 26, 58, 12, 12, 10, 12, 14, 14, 60, 60, 62, 62, 20, 20, 18, 18, 16, 16, 14, 16, 62, 62, 24, 20, 22, 24, 22, 24, 26, 28, 26], "width": 182, "height": 149}}, "yj_23": {"yj_23": {"type": "mesh", "uvs": [0.65363, 0.02719, 1, 0.1076, 0.95555, 0.22543, 0.8578, 0.32165, 0.74054, 0.42146, 0.74055, 0.57811, 0.39459, 0.68979, 0.26269, 0.85476, 0.1425, 0.86862, 0.07215, 0.83813, 0.11319, 0.63712, 0, 0.3771, 0.11032, 0.31195, 0.14549, 0.19966, 0.1641, 0.11037, 0.25497, 0, 0.43378, 0.15057, 0.38293, 0.25372, 0.32723, 0.35908, 0.23929, 0.50603, 0.20992, 0.64959], "triangles": [16, 0, 2, 2, 0, 1, 14, 15, 16, 16, 15, 0, 4, 18, 17, 18, 12, 17, 19, 18, 4, 18, 11, 12, 13, 14, 16, 7, 8, 20, 6, 7, 20, 20, 8, 9, 9, 10, 20, 5, 6, 19, 6, 20, 19, 20, 10, 19, 10, 11, 19, 19, 4, 5, 19, 11, 18, 3, 4, 17, 12, 13, 17, 3, 17, 16, 17, 13, 16, 2, 3, 16], "vertices": [2, 109, -32.86, 36.53, 0.192, 108, 134.2, 17.23, 0.808, 2, 109, -15.71, 84.08, 0.192, 108, 127.46, 67.33, 0.808, 2, 109, 17.37, 81.88, 0.504, 108, 157.82, 80.65, 0.496, 2, 109, 45.27, 72.08, 0.8, 108, 187.1, 84.84, 0.2, 2, 109, 74.44, 59.86, 0.872, 108, 218.61, 87.46, 0.128, 2, 109, 117.57, 64.63, 0.944, 108, 254.67, 111.61, 0.056, 1, 109, 153.3, 22.99, 1, 1, 109, 200.62, 10.84, 1, 1, 109, 206.17, -4.39, 1, 1, 109, 198.78, -14.48, 1, 1, 109, 142.85, -15.26, 1, 2, 109, 72.89, -37.92, 0.944, 108, 262.38, 0.02, 0.056, 2, 109, 53.37, -25.54, 0.872, 108, 239.35, 1.98, 0.128, 2, 109, 21.94, -24.38, 0.8, 108, 210.94, -11.49, 0.2, 2, 109, -2.91, -24.67, 0.504, 108, 189.03, -23.23, 0.496, 2, 109, -34.6, -16.2, 0.192, 108, 157, -30.35, 0.808, 2, 109, 4.28, 11.66, 0.504, 108, 178.63, 12.32, 0.496, 2, 109, 33.41, 8.18, 0.8, 108, 206.07, 22.68, 0.2, 2, 109, 63.22, 4.14, 0.872, 108, 234.38, 32.86, 0.128, 2, 109, 104.94, -2.83, 0.944, 108, 274.61, 45.93, 0.056, 1, 109, 144.89, -2.28, 1], "hull": 16, "edges": [0, 32, 32, 28, 28, 30, 0, 2, 2, 4, 4, 32, 0, 30, 32, 34, 28, 26, 26, 34, 34, 6, 6, 4, 34, 36, 36, 38, 38, 22, 22, 24, 24, 26, 24, 36, 36, 8, 8, 10, 10, 38, 8, 6, 38, 40, 40, 16, 16, 18, 18, 20, 20, 22, 20, 40, 40, 12, 12, 10, 12, 14, 14, 16], "width": 131, "height": 277}}, "yj_24": {"yj_24": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [63.06, 52.08, 95, 1.28, 17.97, -47.16, -13.97, 3.63], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 60, "height": 91}}, "yj_25": {"yj_25": {"type": "mesh", "uvs": [0.57187, 0.03465, 0.69192, 0.16926, 0.82469, 0.30892, 0.92356, 0.43007, 1, 0.56467, 0.8713, 0.75481, 0.60968, 0.94057, 0.33565, 0.99999, 0.11812, 0.99999, 0, 0.97335, 0, 0.78021, 0.0206, 0.59869, 0.03036, 0.42621, 0.04376, 0.17209, 0.06449, 0.07333, 0.12952, 0.07333, 0.22954, 0.05875, 0.29414, 0.04343, 0.4391, 0, 0.52809, 0, 0.32819, 0.13775, 0.39853, 0.37522, 0.45377, 0.57638, 0.52441, 0.75209, 0.12545, 0.18375, 0.12156, 0.42143, 0.12846, 0.6313, 0.12665, 0.81539, 0.31682, 0.82668, 0.29, 0.63209, 0.24628, 0.16456, 0.26922, 0.39716, 0.48289, 0.08681, 0.59023, 0.29041, 0.70746, 0.45026, 0.79362, 0.59328], "triangles": [31, 20, 21, 29, 31, 21, 25, 24, 31, 22, 21, 33, 21, 32, 33, 33, 0, 1, 25, 13, 24, 24, 30, 31, 32, 0, 33, 30, 16, 17, 20, 18, 32, 32, 19, 0, 32, 18, 19, 30, 15, 16, 30, 24, 15, 13, 14, 24, 24, 14, 15, 31, 30, 20, 30, 17, 20, 20, 17, 18, 21, 20, 32, 23, 34, 35, 23, 22, 34, 22, 33, 34, 34, 2, 3, 2, 33, 1, 34, 33, 2, 35, 34, 3, 35, 3, 4, 5, 35, 4, 7, 8, 28, 28, 27, 29, 7, 28, 23, 28, 29, 22, 26, 31, 29, 8, 27, 28, 8, 9, 27, 9, 10, 27, 27, 26, 29, 27, 10, 26, 10, 11, 26, 26, 11, 25, 11, 12, 25, 26, 25, 31, 12, 13, 25, 29, 21, 22, 6, 23, 35, 28, 22, 23, 6, 35, 5, 7, 23, 6], "vertices": [4, 127, -58.23, 74.68, 0.018, 126, -8.09, 69.31, 0.06849, 105, 6.97, 36.04, 0.25685, 70, 69.74, -17.78, 0.65667, 6, 128, -66.1, 99.65, 0.018, 127, -18.89, 96.88, 0.06849, 126, 29.13, 94.91, 0.10711, 105, 52.09, 33.89, 0.45266, 106, -94.13, -25.56, 0.01563, 70, 93.5, -56.19, 0.33812, 7, 129, -47.32, 130.67, 0.02149, 128, -23.64, 123.11, 0.06849, 127, 22.58, 122.04, 0.10711, 126, 68.21, 123.65, 0.06849, 105, 100.6, 33.12, 0.55987, 106, -54.2, 2, 0.07392, 70, 120.33, -96.61, 0.10063, 7, 129, -10.16, 141.93, 0.09514, 128, 11.67, 139.27, 0.11468, 127, 57.2, 139.63, 0.06849, 126, 101.13, 144.24, 0.018, 105, 139.27, 29.58, 0.48891, 106, -20.67, 21.58, 0.19917, 70, 139.3, -130.5, 0.01563, 5, 129, 26.93, 146.38, 0.18718, 128, 47.82, 148.68, 0.11579, 127, 92.95, 150.5, 0.02557, 105, 175.31, 19.75, 0.38318, 106, 14.37, 34.53, 0.28828, 7, 129, 49.68, 95, 0.26364, 128, 77.29, 100.83, 0.18655, 127, 124.33, 103.88, 0.04784, 126, 171.16, 114.59, 0.00472, 108, 7.26, 221.31, 0.01563, 105, 177.07, -36.42, 0.25096, 106, 48.43, -10.17, 0.23067, 7, 129, 54.75, 11.66, 0.33957, 128, 93.53, 18.93, 0.24912, 127, 143.89, 22.71, 0.08034, 126, 197.84, 35.48, 0.01, 108, 82.71, 185.56, 0.10063, 105, 150.42, -115.54, 0.10075, 106, 72.72, -90.05, 0.1196, 7, 129, 32.34, -61.34, 0.29638, 128, 81.15, -56.43, 0.23112, 127, 134.58, -53.09, 0.065, 126, 195.3, -40.85, 0.01, 108, 135.85, 130.72, 0.33812, 105, 102.19, -174.76, 0.02013, 106, 67.87, -166.27, 0.03925, 5, 129, 4.91, -114.29, 0.18858, 128, 61.1, -112.59, 0.11437, 127, 116.82, -110.01, 0.0325, 108, 169, 81.15, 0.65667, 106, 53.48, -224.14, 0.00787, 3, 129, -15.42, -140.22, 0.09028, 128, 44.44, -141.02, 0.05417, 108, 181.91, 50.84, 0.85556, 5, 129, -54.88, -119.81, 0.04333, 128, 2.6, -126.1, 0.0425, 127, 58.92, -125.89, 0.0325, 108, 145, 26.12, 0.86604, 70, -125.94, -142.95, 0.01563, 5, 128, -34.83, -106.77, 0.0325, 127, 20.74, -108.1, 0.0425, 126, 86.79, -105.74, 0.0325, 108, 107.17, 7.58, 0.79188, 70, -109.7, -104.08, 0.10063, 4, 127, -16.35, -93.75, 0.0325, 126, 48.58, -94.75, 0.0425, 108, 72.72, -12.27, 0.58687, 70, -96.87, -66.44, 0.33812, 3, 126, -7.77, -78.8, 0.0325, 108, 22.11, -41.74, 0.31083, 70, -78.22, -10.93, 0.65667, 2, 108, 0.08, -49.66, 0.14444, 70, -66.87, 9.55, 0.85556, 3, 126, -25.01, -51.04, 0.0325, 108, -9.84, -34.86, 0.08583, 70, -49.65, 4.94, 0.88167, 4, 127, -80.82, -16.52, 0.01, 126, -22.49, -23.54, 0.065, 108, -27.87, -13.95, 0.0325, 70, -22.31, 1.1, 0.8925, 4, 127, -78.92, 1.42, 0.01, 126, -22.2, -5.5, 0.08034, 105, -49.54, -14.96, 0.01716, 70, -4.3, -0.07, 0.8925, 4, 127, -76.66, 42.32, 0.00472, 126, -23.57, 35.44, 0.04784, 105, -25.85, 18.45, 0.06577, 70, 36.65, -0.68, 0.88167, 3, 126, -18.42, 59.27, 0.02557, 105, -7.32, 34.3, 0.11888, 70, 60.21, -6.98, 0.85556, 6, 128, -106.39, 8.22, 0.008, 127, -55.44, 3.89, 0.05933, 126, 0.98, -0.96, 0.16866, 108, -27.81, 18.62, 0.024, 105, -28.34, -25.38, 0.06402, 70, -0.89, -23.44, 0.676, 7, 129, -87.43, 19.98, 0.008, 128, -48.48, 8.03, 0.05933, 127, 2.44, 6.05, 0.16666, 126, 58.43, 6.33, 0.24626, 108, 6.85, 65.02, 0.056, 105, 21.8, -54.35, 0.14176, 70, 3.61, -81.18, 0.322, 8, 129, -39.37, 12.16, 0.0649, 128, 0.2, 6.75, 0.16866, 127, 51.13, 6.75, 0.24626, 126, 106.87, 11.35, 0.16666, 108, 36.88, 103.35, 0.08, 105, 63.39, -79.68, 0.18043, 106, -18.95, -111.42, 0.0131, 70, 6.28, -129.8, 0.08, 8, 129, 5.46, 10.78, 0.20076, 128, 44.81, 11.42, 0.26727, 127, 95.51, 13.23, 0.16866, 126, 150.5, 21.74, 0.05933, 108, 59.7, 141.96, 0.0865, 105, 104.42, -97.82, 0.14625, 106, 24.98, -102.35, 0.06324, 70, 14.55, -173.88, 0.008, 4, 127, -61.85, -52.3, 0.024, 126, -0.42, -57.5, 0.072, 108, 11.89, -21.65, 0.23, 70, -57.29, -19.31, 0.674, 6, 128, -63.95, -67.02, 0.024, 127, -9.97, -69.57, 0.072, 126, 52.78, -70.09, 0.12578, 108, 57.9, 7.88, 0.45, 105, -28.95, -111.77, 0.00422, 70, -72.44, -71.84, 0.324, 7, 129, -69.12, -72.81, 0.026, 128, -17.84, -81.45, 0.072, 127, 36.68, -82.11, 0.12578, 126, 100.36, -78.44, 0.072, 108, 96.96, 36.31, 0.6135, 105, 3.87, -147.21, 0.00422, 70, -83.09, -118.96, 0.0865, 7, 129, -31.74, -92.7, 0.0915, 128, 21.87, -96.13, 0.13428, 127, 76.96, -95.16, 0.072, 126, 141.64, -87.87, 0.024, 108, 132.42, 59.46, 0.666, 105, 31.02, -179.71, 0.00422, 70, -94.5, -159.73, 0.008, 8, 129, -5.47, -47.62, 0.18563, 128, 41.83, -47.92, 0.22833, 127, 94.95, -46.18, 0.15733, 126, 155.21, -37.49, 0.048, 108, 105.59, 104.21, 0.324, 105, 72.32, -147.82, 0.03334, 106, 27.9, -161.7, 0.01537, 70, -44.84, -175.73, 0.008, 7, 129, -48.62, -33.58, 0.052, 128, -2.81, -39.82, 0.15733, 127, 50.01, -39.9, 0.21133, 126, 109.89, -35.22, 0.15533, 108, 72.48, 73.19, 0.298, 105, 37.62, -118.58, 0.04602, 70, -40.38, -130.57, 0.08, 6, 128, -108.12, -15, 0.008, 127, -56.22, -19.38, 0.048, 126, 2.26, -24.2, 0.15733, 108, -10.2, 3.4, 0.098, 105, -41.39, -44.66, 0.01267, 70, -24.17, -23.59, 0.676, 7, 129, -99.23, -13.8, 0.008, 128, -55.62, -27.04, 0.048, 127, -3.27, -29.28, 0.15533, 126, 55.88, -29.36, 0.21133, 108, 30.75, 38.39, 0.222, 105, -1.83, -81.22, 0.03334, 70, -31.91, -76.9, 0.322, 5, 128, -103.2, 52.07, 0.00378, 127, -54.03, 47.84, 0.03533, 126, -1.52, 42.94, 0.12227, 105, -3.75, 11.08, 0.16463, 70, 43.08, -23.07, 0.674, 8, 129, -80.62, 75.59, 0.00397, 128, -49.21, 64.05, 0.03533, 127, -0.57, 62, 0.12132, 126, 50.47, 61.79, 0.1751, 108, -38.59, 97.81, 0.008, 105, 49.04, -5.39, 0.32825, 106, -73.79, -59.3, 0.00403, 70, 59.4, -75.91, 0.324, 8, 129, -33.19, 87.22, 0.0389, 128, -3.78, 81.96, 0.12227, 127, 44.09, 81.73, 0.1751, 126, 93.21, 85.41, 0.12132, 108, -25.89, 144.96, 0.008, 105, 97.35, -12.46, 0.40231, 106, -30.37, -36.98, 0.0456, 70, 80.92, -119.74, 0.0865, 8, 129, 6.89, 93.08, 0.15218, 128, 35.14, 93.16, 0.18761, 127, 82.53, 94.52, 0.12227, 126, 130.36, 101.55, 0.03533, 108, -11.7, 182.9, 0.008, 105, 136.7, -22.1, 0.34663, 106, 7.26, -21.97, 0.13999, 70, 95.24, -157.63, 0.008], "hull": 20, "edges": [34, 40, 40, 42, 42, 44, 44, 46, 46, 12, 30, 28, 28, 26, 26, 48, 30, 48, 48, 50, 50, 24, 24, 26, 50, 52, 52, 54, 54, 16, 54, 56, 56, 46, 44, 58, 58, 52, 24, 22, 22, 20, 20, 54, 22, 52, 18, 20, 18, 16, 14, 16, 56, 14, 14, 12, 58, 56, 34, 32, 32, 30, 40, 60, 60, 48, 32, 60, 42, 62, 62, 50, 60, 62, 62, 58, 40, 64, 64, 0, 0, 38, 36, 38, 36, 34, 36, 64, 64, 66, 66, 42, 66, 2, 2, 0, 66, 68, 68, 44, 68, 4, 4, 2, 68, 70, 70, 46, 70, 10, 10, 8, 8, 6, 6, 4, 6, 70, 10, 12], "width": 274, "height": 230}}, "yj_26": {"yj_26": {"type": "mesh", "uvs": [0.67577, 0.07444, 0.76552, 0.18214, 0.83415, 0.29776, 1, 0.39754, 0.89838, 0.70309, 1, 0.93918, 0.91359, 0.98138, 0.79003, 0.97339, 0.66837, 0.7738, 0.33526, 0.65728, 0.35902, 0.54166, 0.19008, 0.48464, 0, 0.38803, 0, 0.19664, 0.16374, 0, 0.5154, 0, 0.37221, 0.17739, 0.41181, 0.32151, 0.53323, 0.48148, 0.69689, 0.57967, 0.78136, 0.73894], "triangles": [13, 14, 16, 16, 14, 15, 16, 12, 13, 16, 15, 0, 18, 17, 2, 18, 2, 3, 18, 10, 17, 19, 18, 3, 19, 9, 10, 19, 10, 18, 4, 19, 3, 20, 19, 4, 8, 9, 19, 20, 8, 19, 20, 4, 5, 6, 7, 20, 8, 20, 7, 5, 6, 20, 10, 11, 17, 17, 1, 2, 16, 0, 1, 16, 17, 12, 17, 16, 1, 11, 12, 17], "vertices": [2, 106, -5.64, 16.74, 0.488, 105, 148.83, 16.85, 0.512, 2, 106, 16.06, 21.79, 0.768, 105, 169.36, 8.19, 0.232, 2, 106, 38.63, 24.23, 0.88, 105, 189.05, -3.11, 0.12, 1, 106, 60.87, 37.84, 1, 1, 106, 113.22, 13.72, 1, 1, 106, 158.32, 14.54, 1, 1, 106, 163.69, 3.39, 1, 1, 106, 159.06, -9.6, 1, 1, 106, 120.01, -14.15, 1, 2, 106, 90.44, -45.1, 0.99842, 105, 190.21, -89.65, 0.00158, 2, 106, 70.25, -37.56, 0.88, 105, 178.31, -71.68, 0.12, 2, 106, 55.63, -53.34, 0.768, 105, 157.2, -75.86, 0.232, 2, 106, 33.34, -69.71, 0.488, 105, 129.55, -75.99, 0.512, 2, 106, -1.09, -61.47, 0.256, 105, 106.53, -49.09, 0.744, 2, 106, -32.25, -35.34, 0.256, 105, 96.69, -9.63, 0.744, 2, 106, -23.17, 2.63, 0.256, 105, 126.36, 15.75, 0.744, 2, 106, 5.05, -20.46, 0.488, 105, 135.61, -19.52, 0.512, 2, 106, 32.01, -22.39, 0.768, 105, 156.29, -36.93, 0.232, 2, 106, 63.92, -16.16, 0.88, 105, 185.76, -50.65, 0.12, 1, 106, 85.81, -2.72, 1, 1, 106, 116.65, -0.45, 1], "hull": 16, "edges": [0, 32, 32, 24, 24, 22, 22, 34, 34, 2, 2, 0, 32, 34, 34, 36, 36, 20, 20, 22, 20, 18, 18, 38, 38, 6, 6, 4, 4, 2, 4, 36, 36, 38, 38, 40, 40, 12, 12, 14, 14, 16, 16, 18, 16, 40, 40, 8, 8, 10, 10, 12, 8, 6, 0, 30, 28, 30, 24, 26, 28, 26, 28, 32], "width": 111, "height": 185}}, "yj_27": {"yj_27": {"type": "mesh", "uvs": [0.08954, 0.03091, 0.14247, 0.06206, 0.22003, 0.12658, 0.36777, 0.21558, 0.43794, 0.27454, 0.56967, 0.35241, 0.66088, 0.42824, 0.77045, 0.5317, 0.83835, 0.63445, 0.89731, 0.76884, 0.94257, 0.87097, 1, 1, 0.90721, 1, 0.86596, 0.88251, 0.8316, 0.74533, 0.82964, 0.84829, 0.85878, 0.99071, 0.7356, 0.96318, 0.75613, 0.8471, 0.75297, 0.71719, 0.72743, 0.85276, 0.66794, 0.96417, 0.58862, 0.96417, 0.63949, 0.83172, 0.63507, 0.67501, 0.56706, 0.80445, 0.53688, 0.94469, 0.46187, 0.91509, 0.48774, 0.79043, 0.52507, 0.65354, 0.46446, 0.78108, 0.44462, 0.94236, 0.35754, 0.92833, 0.37651, 0.75615, 0.42563, 0.63207, 0.42915, 0.59072, 0.42451, 0.55617, 0.40973, 0.51056, 0.35792, 0.44919, 0.30744, 0.39802, 0.23604, 0.3335, 0.12031, 0.23783, 0.03537, 0.12547, 0, 0.0654, 0, 0, 0.02552, 0, 0.06491, 0, 0.04398, 0.05205, 0.09323, 0.09543, 0.18433, 0.18666, 0.29513, 0.27009, 0.38746, 0.32793, 0.46749, 0.39691, 0.52177, 0.46162, 0.55993, 0.53059, 0.60691, 0.59947], "triangles": [2, 48, 1, 42, 48, 41, 47, 45, 46, 47, 46, 0, 47, 43, 44, 47, 44, 45, 48, 0, 1, 47, 0, 48, 42, 43, 47, 42, 47, 48, 50, 3, 51, 49, 48, 2, 50, 49, 2, 41, 48, 49, 3, 50, 2, 40, 49, 50, 41, 49, 40, 40, 50, 39, 51, 3, 4, 52, 4, 5, 51, 4, 52, 39, 50, 51, 38, 39, 51, 38, 51, 52, 37, 38, 52, 53, 52, 5, 35, 36, 54, 53, 5, 6, 37, 52, 53, 54, 53, 6, 54, 6, 7, 36, 37, 53, 36, 53, 54, 10, 13, 9, 15, 18, 14, 20, 23, 19, 33, 34, 30, 14, 8, 9, 18, 19, 14, 34, 35, 29, 30, 34, 29, 29, 35, 55, 24, 55, 8, 29, 55, 24, 19, 24, 8, 25, 29, 24, 23, 24, 19, 35, 54, 55, 14, 19, 8, 9, 13, 14, 55, 7, 8, 55, 54, 7, 30, 32, 33, 28, 29, 25, 27, 28, 25, 30, 31, 32, 26, 27, 25, 21, 23, 20, 22, 23, 21, 17, 18, 15, 16, 17, 15, 12, 13, 10, 12, 10, 11], "vertices": [1, 70, 54.93, 10.27, 1, 3, 115, -61.06, 9.19, 0.01563, 114, 6.45, 11.89, 0.30937, 70, 64.44, -0.3, 0.675, 3, 115, -37.34, 8.87, 0.10063, 114, 30.13, 10.52, 0.55875, 70, 77.14, -20.33, 0.34062, 4, 116, -50.43, 7.55, 0.01563, 115, 2.2, 14.55, 0.325, 114, 69.89, 14.44, 0.55875, 70, 103.53, -50.32, 0.10063, 4, 116, -29.04, 10.18, 0.10063, 115, 23.76, 14.15, 0.55875, 114, 91.41, 13.08, 0.325, 70, 114.99, -68.59, 0.01563, 4, 117, -41.15, 1.63, 0.01563, 116, 4.88, 20.38, 0.325, 115, 58.77, 19.49, 0.55875, 114, 126.62, 16.86, 0.10063, 4, 117, -18.22, 17.5, 0.18062, 116, 32.54, 23.93, 0.50905, 115, 86.65, 19.12, 0.29609, 114, 154.47, 15.26, 0.01424, 4, 118, -58.29, -1.67, 0.01044, 117, 12.32, 35.9, 0.549, 116, 68.13, 26.21, 0.37333, 115, 122.21, 16.39, 0.06723, 8, 119, -60, 13.22, 0.014, 118, -29.36, 5.49, 0.06718, 120, -22.31, 6.4, 0.0112, 122, -18.18, 27.88, 0.00746, 124, -32.88, 65.09, 0.00746, 117, 40.64, 45.19, 0.58758, 116, 97.54, 21.43, 0.29112, 115, 150.66, 7.53, 0.014, 7, 119, -24.04, 11.59, 0.10104, 118, 6.51, 8.44, 0.22146, 121, -16.13, 27.78, 0.0125, 120, 9.38, 23.47, 0.0575, 122, 13.11, 45.67, 0.01, 117, 76.19, 50.79, 0.50687, 116, 131.69, 10.04, 0.09063, 4, 119, 3.33, 10.44, 0.30093, 118, 33.81, 10.78, 0.3604, 117, 103.24, 55.14, 0.32265, 116, 157.7, 1.46, 0.01603, 3, 119, 37.93, 9.04, 0.44907, 118, 68.31, 13.79, 0.3912, 117, 137.42, 60.69, 0.15972, 3, 119, 29.36, -10, 0.45669, 118, 62.23, -6.19, 0.3936, 117, 132.84, 40.33, 0.14972, 3, 119, -1.12, -6.46, 0.31552, 118, 31.54, -6.55, 0.36904, 117, 102.26, 37.69, 0.31544, 9, 119, -35.45, 0.51, 0.08083, 118, -3.39, -4, 0.18017, 121, -23.56, 13.73, 0.07833, 120, 5.29, 8.1, 0.16867, 123, -32.19, 26.5, 0.01, 122, 9.37, 30.21, 0.04267, 124, -7.05, 74.94, 0.00667, 117, 67.24, 37.65, 0.42017, 116, 117.69, 2.49, 0.0125, 3, 121, 1.87, 10.49, 0.32075, 120, 30.8, 10.64, 0.36894, 117, 92.15, 31.59, 0.31031, 3, 121, 37.84, 13.14, 0.45669, 120, 65.26, 21.28, 0.3936, 117, 128.19, 30.2, 0.14972, 3, 121, 28, -13.66, 0.45669, 120, 61.68, -7.05, 0.3936, 117, 115.42, 4.67, 0.14972, 3, 121, -0.23, -5.92, 0.32223, 120, 32.43, -5.82, 0.37064, 117, 88.23, 15.52, 0.30712, 10, 119, -49.1, -12.75, 0.01, 118, -15.25, -18.89, 0.046, 121, -32.46, -3.1, 0.07833, 120, 0.39, -10.28, 0.16533, 123, -34.39, 7.6, 0.07667, 122, 4.89, 11.72, 0.15822, 125, -25.98, 63.4, 0.00833, 124, -6.24, 55.93, 0.04222, 117, 56.52, 21.92, 0.40822, 116, 100.94, -6.54, 0.00667, 3, 123, -0.3, 10.76, 0.32208, 122, 39.12, 10.72, 0.36631, 117, 88.19, 8.91, 0.3116, 3, 123, 29.96, 5, 0.45669, 122, 68.45, 1.32, 0.3936, 117, 112.32, -10.24, 0.14972, 3, 123, 34.57, -12.25, 0.4554, 122, 70.93, -16.36, 0.38967, 117, 108.4, -27.65, 0.15493, 4, 123, -0.25, -9.71, 0.31535, 122, 36.68, -9.6, 0.35334, 117, 78.74, -9.25, 0.32219, 116, 106.32, -44.43, 0.00912, 9, 118, -33.02, -41.21, 0.00667, 121, -45.79, -28.32, 0.00833, 120, -6.96, -37.85, 0.03556, 123, -37.69, -20.74, 0.06389, 122, -1.83, -16.01, 0.13352, 125, -31.22, 35.35, 0.07222, 124, -5.04, 27.42, 0.17074, 117, 40.45, -1.65, 0.45352, 116, 75.82, -20.07, 0.05556, 4, 125, 3.34, 26.49, 0.30832, 124, 30.63, 26.56, 0.36776, 117, 68.53, -23.65, 0.31501, 116, 90.64, -52.53, 0.00892, 3, 125, 38.91, 26.5, 0.4554, 124, 65.29, 34.57, 0.38967, 117, 101.11, -37.94, 0.15493, 3, 125, 34.9, 8.52, 0.44907, 124, 65.43, 16.16, 0.4037, 117, 90.22, -52.79, 0.14722, 3, 125, 3.32, 8.3, 0.30856, 124, 34.71, 8.84, 0.39759, 117, 61.21, -40.3, 0.29384, 7, 120, -9.4, -63.06, 0.00667, 123, -36.46, -46.04, 0.00833, 122, -3.68, -41.27, 0.03556, 125, -31.74, 10.04, 0.1575, 124, 0.15, 2.64, 0.33906, 117, 29.8, -24.62, 0.43372, 116, 55.8, -35.57, 0.01917, 3, 125, 2.04, 2.72, 0.30532, 124, 34.71, 3.11, 0.39867, 117, 57.79, -44.9, 0.29601, 3, 125, 42.31, 6.01, 0.44907, 124, 73.21, 15.38, 0.4037, 117, 95.99, -58.07, 0.14722, 3, 125, 42.62, -13.89, 0.44907, 124, 77.99, -3.94, 0.3912, 117, 88.28, -76.42, 0.15972, 4, 125, -0.28, -17.89, 0.2934, 124, 37.1, -17.49, 0.37639, 117, 47.39, -62.84, 0.31458, 116, 53.83, -77.6, 0.01563, 5, 122, -5.86, -64.17, 0.00833, 125, -32.71, -12.95, 0.11354, 124, 4.38, -19.98, 0.27729, 117, 19.67, -45.28, 0.51021, 116, 37.31, -49.25, 0.09063, 6, 122, -16.17, -64.81, 0.00833, 125, -42.97, -14.14, 0.01563, 124, -5.35, -23.44, 0.09583, 117, 9.8, -42.25, 0.53958, 116, 29.94, -42.02, 0.325, 115, 74.82, -45.8, 0.01563, 4, 124, -12.78, -27.9, 0.01563, 117, 1.18, -41.38, 0.325, 116, 22.68, -37.28, 0.55875, 115, 68.3, -40.09, 0.10063, 4, 117, -10.63, -42.13, 0.10063, 116, 11.85, -32.51, 0.55875, 115, 58.25, -33.85, 0.325, 114, 123.74, -36.41, 0.01563, 4, 117, -28.1, -50.15, 0.01563, 116, -7.35, -31.6, 0.325, 115, 39.37, -30.25, 0.55875, 114, 105.03, -31.97, 0.10063, 4, 116, -24.41, -32.08, 0.10063, 115, 22.41, -28.34, 0.55875, 114, 88.17, -29.31, 0.325, 70, 78.68, -90.7, 0.01563, 4, 116, -47.05, -34.04, 0.01563, 115, -0.28, -27.1, 0.325, 114, 65.57, -27.06, 0.55875, 70, 67.31, -71.03, 0.10063, 3, 115, -35.57, -26.74, 0.10063, 114, 30.33, -25.13, 0.55875, 70, 48.31, -41.29, 0.34062, 3, 115, -68.47, -18.66, 0.01563, 114, -2.19, -15.6, 0.30937, 70, 37.08, -9.33, 0.675, 1, 70, 33.25, 7.18, 1, 1, 70, 37.46, 22.91, 1, 1, 70, 43.01, 21.43, 1, 1, 70, 51.57, 19.14, 1, 1, 70, 43.67, 7.83, 1, 3, 115, -63.77, -4.4, 0.008, 114, 3.15, -1.56, 0.312, 70, 51.59, -5.46, 0.68, 3, 115, -33.34, -7.63, 0.093, 114, 33.4, -6.15, 0.579, 70, 65.52, -32.7, 0.328, 4, 116, -50.57, -13.7, 0.008, 115, -0.91, -6.47, 0.32, 114, 65.85, -6.42, 0.579, 70, 84.23, -59.21, 0.093, 4, 116, -26.17, -7.07, 0.093, 115, 24.17, -3.33, 0.579, 114, 91.05, -4.4, 0.32, 70, 100.58, -78.5, 0.008, 4, 117, -35.39, -23.24, 0.008, 116, -1.44, -4.35, 0.32, 115, 49.04, -4.1, 0.579, 114, 115.86, -6.28, 0.093, 4, 117, -16.99, -14.86, 0.093, 116, 18.75, -5.38, 0.579, 115, 68.89, -7.95, 0.32, 114, 135.52, -11, 0.008, 5, 122, -35.09, -37.75, 0.00667, 124, -31, -2.66, 0.00667, 117, 1.65, -10.25, 0.31467, 116, 37.42, -9.86, 0.579, 115, 86.75, -15.01, 0.093, 9, 118, -52.86, -41.79, 0.0125, 120, -24.91, -46.33, 0.00667, 123, -54.22, -31.72, 0.00833, 122, -19.57, -24.9, 0.03556, 125, -48.48, 25.54, 0.00833, 124, -19.64, 13.97, 0.05472, 117, 20.71, -3.7, 0.54722, 116, 57.35, -12.81, 0.31867, 115, 106.06, -20.73, 0.008], "hull": 47, "edges": [88, 90, 90, 94, 94, 96, 96, 84, 86, 88, 84, 86, 86, 94, 94, 0, 0, 2, 2, 96, 90, 92, 0, 92, 96, 98, 98, 100, 100, 80, 80, 82, 82, 84, 82, 98, 98, 4, 4, 6, 6, 100, 4, 2, 100, 102, 102, 104, 104, 76, 76, 78, 78, 80, 78, 102, 102, 8, 8, 10, 10, 104, 8, 6, 104, 106, 106, 108, 108, 14, 14, 12, 12, 10, 12, 106, 106, 74, 74, 72, 72, 108, 74, 76, 108, 110, 110, 48, 18, 16, 16, 14, 16, 110, 110, 70, 70, 68, 72, 70, 48, 58, 58, 68, 28, 18, 28, 26, 26, 20, 20, 18, 22, 24, 26, 24, 20, 22, 48, 38, 38, 28, 38, 36, 36, 30, 28, 30, 30, 32, 32, 34, 34, 36, 48, 46, 46, 40, 40, 38, 46, 44, 44, 42, 42, 40, 58, 56, 56, 50, 50, 48, 56, 54, 54, 52, 52, 50, 68, 66, 66, 60, 60, 58, 66, 64, 64, 62, 62, 60], "width": 225, "height": 249}}, "yj_28": {"yj_28": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [121.62, -1.48, -5.17, -44.83, -21.03, 1.53, 105.76, 44.89], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 134, "height": 49}}, "yj_17": {"yj_17": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [58.85, -111.75, -32.88, -21.05, 43.76, 56.46, 135.49, -34.25], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 129, "height": 109}}, "yj_16": {"yj_16": {"type": "mesh", "uvs": [0.44495, 0.27428, 0.52935, 0.36969, 0.73906, 0.48458, 0.84456, 0.59865, 1, 0.71548, 0.94814, 0.8401, 0.82346, 0.92772, 0.47243, 1, 0.34966, 1, 0.28828, 0.89657, 0.23778, 0.80002, 0.1553, 0.69877, 0.0498, 0.61699, 0, 0.52157, 0.18216, 0.2957, 0.3241, 0.17693, 0.2819, 0.40864, 0.35096, 0.52547, 0.44879, 0.62672, 0.58944, 0.75053], "triangles": [16, 15, 0, 1, 16, 0, 13, 14, 16, 12, 13, 16, 11, 12, 16, 16, 14, 15, 9, 10, 18, 17, 11, 16, 18, 1, 2, 1, 17, 16, 18, 17, 1, 10, 11, 17, 10, 17, 18, 19, 18, 2, 19, 2, 3, 5, 3, 4, 6, 19, 3, 9, 18, 19, 5, 6, 3, 7, 8, 9, 19, 7, 9, 7, 19, 6], "vertices": [3, 82, -65.39, 64.3, 0.01563, 81, -3.99, 51.76, 0.30937, 75, 81.7, -22.04, 0.675, 3, 82, -31.45, 62.48, 0.10063, 81, 29.73, 55.95, 0.55875, 75, 76.63, -55.64, 0.34062, 3, 82, 29.96, 80.84, 0.34062, 81, 86.94, 84.85, 0.55875, 75, 89.01, -118.53, 0.10063, 3, 82, 71.41, 79.53, 0.65417, 81, 127.97, 90.88, 0.33021, 75, 83.73, -159.66, 0.01563, 2, 82, 122.86, 87.2, 0.87222, 81, 177.27, 107.51, 0.12778, 2, 82, 136.35, 53.99, 0.95556, 81, 196.4, 77.19, 0.04444, 2, 82, 129.06, 13.85, 0.9125, 81, 196.3, 36.4, 0.0875, 2, 82, 75.83, -66.46, 0.95556, 81, 158.07, -52.04, 0.04444, 2, 82, 52.48, -89.81, 0.87222, 81, 139.2, -79.15, 0.12778, 3, 82, 21.42, -82.11, 0.65417, 81, 107.27, -77.04, 0.33021, 75, -72.36, -94.39, 0.01563, 3, 82, -6.28, -73.62, 0.34062, 81, 78.52, -73.57, 0.55875, 75, -61.25, -67.63, 0.10063, 3, 82, -40.94, -70.34, 0.10063, 81, 43.82, -76.45, 0.55875, 75, -54.66, -33.44, 0.34062, 3, 82, -76.33, -75.08, 0.01563, 81, 9.82, -87.37, 0.30937, 75, -55.98, 2.24, 0.675, 1, 75, -44.99, 28.66, 1, 1, 75, 32.37, 28.91, 1, 1, 75, 80.94, 19.46, 1, 3, 82, -71.22, 8.11, 0.008, 81, 0.17, -4.58, 0.312, 75, 26.33, -10.83, 0.68, 3, 82, -36.2, -0.65, 0.093, 81, 36.19, -7.02, 0.579, 75, 14.25, -44.86, 0.328, 3, 82, 1.39, -1.01, 0.328, 81, 73.25, -0.75, 0.579, 75, 10.28, -82.23, 0.093, 3, 82, 51.34, 2.54, 0.67, 81, 121.79, 11.56, 0.322, 75, 9.02, -132.29, 0.008], "hull": 16, "edges": [28, 26, 26, 24, 24, 32, 32, 0, 0, 30, 30, 28, 28, 32, 32, 34, 34, 36, 36, 20, 20, 22, 22, 24, 22, 34, 34, 2, 2, 4, 4, 36, 2, 0, 36, 38, 38, 12, 12, 14, 14, 16, 16, 18, 18, 20, 18, 38, 38, 6, 6, 8, 8, 10, 10, 12, 6, 4], "width": 269, "height": 265}}, "jj_17": {"jj_17": {"type": "mesh", "uvs": [0.94351, 0.20226, 0.88418, 0.40081, 0.82725, 0.60392, 0.77032, 0.72116, 0.71225, 0.79811, 0.70228, 0.90509, 0.55024, 0.96653, 0.30597, 0.97712, 0.15309, 0.89238, 0.14478, 0.79175, 0.09921, 0.69464, 2e-05, 0.50333, 0, 0.25142, 0.0748, 0.07798, 0.10843, 0, 0.49111, 0, 0.7581, 0, 0.95389, 0, 0.5627, 0.84895, 0.30597, 0.87014, 0.31598, 0.56483, 0.57873, 0.63183, 0.56995, 0.75073, 0.30175, 0.74349, 0.36058, 0.3147, 0.64389, 0.40838, 0.75958, 0.17389, 0.40912, 0.17694], "triangles": [20, 11, 24, 21, 20, 24, 11, 12, 24, 26, 16, 17, 27, 14, 15, 26, 27, 15, 26, 15, 16, 13, 14, 27, 0, 26, 17, 24, 13, 27, 12, 13, 24, 1, 26, 0, 25, 27, 26, 25, 26, 1, 24, 27, 25, 2, 25, 1, 25, 21, 24, 21, 25, 2, 18, 22, 4, 19, 23, 18, 9, 23, 19, 10, 11, 20, 23, 10, 20, 23, 20, 21, 22, 23, 21, 9, 10, 23, 4, 22, 3, 18, 23, 22, 22, 21, 3, 3, 21, 2, 8, 9, 19, 5, 18, 4, 6, 19, 18, 6, 18, 5, 7, 19, 6, 8, 19, 7], "vertices": [1, 6, 248.15, -254.7, 1, 2, 5, 191.22, -290.45, 0.216, 6, 158.78, -247.46, 0.784, 3, 4, 231.01, 172.53, 0.112, 5, 109.51, -250.35, 0.44755, 6, 67.93, -242, 0.44045, 3, 4, 201.38, 122.46, 0.22368, 5, 63.81, -214.34, 0.61336, 6, 11.86, -226.42, 0.16296, 3, 4, 170.79, 89.36, 0.44045, 5, 35.06, -179.64, 0.44755, 6, -28.04, -205.5, 0.112, 2, 4, 166.33, 44.12, 0.784, 5, -9.19, -169.22, 0.216, 2, 4, 85.07, 16.56, 0.784, 5, -25.73, -85.01, 0.216, 2, 4, -46.24, 9.46, 0.784, 5, -15.35, 46.07, 0.216, 2, 4, -129.18, 43.57, 0.784, 5, 29.45, 123.76, 0.216, 3, 4, -134.5, 85.94, 0.44045, 5, 72.15, 123.41, 0.44755, 6, -110.78, 88.39, 0.112, 3, 4, -159.83, 126.42, 0.22368, 5, 115.64, 143.15, 0.61336, 6, -78.27, 123.38, 0.16296, 3, 4, -214.8, 206.07, 0.112, 5, 201.87, 187.08, 0.44755, 6, -15.67, 197.18, 0.44045, 2, 5, 307.5, 175.1, 0.216, 6, 86.4, 226.89, 0.784, 1, 6, 167.92, 208.71, 1, 1, 6, 204.57, 200.53, 1, 1, 6, 262.1, 2.84, 1, 1, 6, 302.23, -135.07, 1, 1, 6, 331.67, -236.21, 1, 3, 4, 90.78, 66.3, 0.44045, 5, 22.81, -97.27, 0.44755, 6, -71.13, -134.24, 0.112, 3, 4, -47.14, 54.6, 0.44045, 5, 29.51, 40.98, 0.44755, 6, -118.31, -4.12, 0.112, 3, 4, -44.33, 183.52, 0.112, 5, 156.91, 21.1, 0.44755, 6, 6.91, 26.71, 0.44045, 3, 4, 97.57, 158.08, 0.112, 5, 112.88, -116.17, 0.44755, 6, 19.26, -116.92, 0.44045, 3, 4, 93.85, 107.82, 0.22368, 5, 63.56, -105.82, 0.61336, 6, -30.24, -126.4, 0.16296, 3, 4, -50.48, 107.99, 0.22368, 5, 82.87, 37.21, 0.61336, 6, -67.62, 13, 0.16296, 2, 5, 259.09, -14.65, 0.216, 6, 114.96, 33.16, 0.784, 2, 5, 202.63, -161.63, 0.216, 6, 119.59, -124.23, 0.784, 1, 6, 232, -156.34, 1, 1, 6, 178.08, 24.34, 1], "hull": 18, "edges": [18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 36, 36, 38, 38, 18, 38, 14, 36, 12, 40, 42, 42, 4, 4, 6, 6, 8, 36, 44, 44, 42, 6, 44, 44, 46, 46, 20, 20, 22, 22, 40, 40, 46, 46, 38, 20, 18, 40, 48, 48, 24, 24, 22, 48, 50, 50, 2, 2, 4, 42, 50, 28, 30, 34, 0, 0, 2, 0, 52, 52, 50, 30, 32, 32, 34, 52, 32, 30, 54, 54, 48, 52, 54, 24, 26, 26, 28, 54, 26], "width": 538, "height": 422}}, "yj_5": {"yj_5": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [9.75, -220.24, -114.51, 0.14, 189.5, 171.55, 313.76, -48.83], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 253, "height": 349}}, "jj_21": {"jj_21": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [252.87, 13.97, 59.84, -179.09, -93.61, -25.65, 99.42, 167.4], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 273, "height": 217}}, "yj_1": {"yj_1": {"type": "mesh", "uvs": [0.78395, 0.40478, 0.88729, 0.88156, 0.55229, 1, 0.38562, 0.81029, 0.18896, 0.55715, 0.08069, 0.39956, 0, 0.28702, 0.04157, 0.14212, 0.08832, 0.03802, 0.25336, 0, 0.40562, 0, 0.68395, 0, 1, 0, 0.17323, 0.20965, 0.33896, 0.28681, 0.52896, 0.36791], "triangles": [13, 8, 9, 7, 8, 13, 14, 9, 10, 13, 9, 14, 13, 6, 7, 5, 6, 13, 4, 13, 14, 5, 13, 4, 15, 10, 11, 14, 10, 15, 0, 11, 12, 15, 11, 0, 3, 14, 15, 4, 14, 3, 2, 15, 0, 2, 0, 1, 3, 15, 2], "vertices": [2, 96, 52.68, 13.77, 0.92, 95, 46.39, -87.94, 0.08, 2, 96, 72.52, -8.1, 0.92, 95, 18.43, -97.43, 0.08, 2, 96, 48.9, -26.54, 0.92, 95, 10.92, -68.41, 0.08, 2, 96, 31.06, -22.37, 0.728, 95, 21.86, -53.72, 0.272, 2, 96, 9.3, -15.87, 0.512, 95, 36.49, -36.35, 0.488, 1, 95, 45.62, -26.76, 1, 1, 95, 52.13, -19.63, 1, 1, 95, 60.75, -23.09, 1, 1, 95, 66.96, -27.05, 1, 1, 95, 69.46, -41.37, 1, 2, 96, 12.83, 21.86, 0.512, 95, 69.69, -54.61, 0.488, 2, 96, 34.86, 31.9, 0.728, 95, 70.12, -78.82, 0.272, 2, 96, 59.88, 43.3, 0.92, 95, 70.6, -106.32, 0.08, 1, 95, 56.96, -34.62, 1, 2, 96, 14.56, 4.06, 0.512, 95, 52.67, -49.11, 0.488, 2, 96, 31.59, 6.56, 0.728, 95, 48.18, -65.72, 0.272], "hull": 13, "edges": [12, 14, 14, 16, 16, 18, 18, 26, 26, 10, 10, 12, 14, 26, 26, 28, 28, 30, 30, 0, 0, 24, 22, 24, 18, 20, 20, 22, 20, 28, 28, 8, 8, 6, 6, 30, 10, 8, 6, 4, 4, 2, 2, 0, 22, 30], "width": 87, "height": 59}}, "jj_26": {"jj_26": {"type": "mesh", "uvs": [0.57688, 0.03328, 0.73947, 0.03513, 0.86258, 0.03699, 0.88349, 0.16716, 0.94156, 0.4275, 0.99999, 0.73561, 1, 0.94958, 0.75809, 1, 0.38508, 1, 0.1222, 0.83573, 0, 0.75929, 0.0279, 0.58242, 0.09175, 0.36912, 0.16692, 0.15932, 0.23543, 0, 0.3957, 0, 0.52832, 0.27784, 0.46949, 0.50514, 0.4388, 0.73244, 0.30506, 0.226, 0.23802, 0.44394, 0.17156, 0.66421, 0.7549, 0.78533, 0.73483, 0.49816, 0.72786, 0.2527], "triangles": [21, 20, 17, 21, 11, 20, 11, 12, 20, 10, 11, 21, 9, 10, 21, 22, 5, 6, 22, 23, 5, 7, 22, 6, 18, 17, 23, 17, 16, 23, 17, 20, 16, 13, 14, 19, 16, 24, 23, 20, 19, 16, 24, 3, 4, 16, 19, 0, 16, 0, 24, 0, 19, 15, 3, 1, 2, 3, 24, 1, 24, 0, 1, 19, 14, 15, 23, 4, 5, 23, 24, 4, 12, 13, 19, 20, 12, 19, 22, 18, 23, 18, 21, 17, 8, 18, 22, 9, 21, 18, 8, 22, 7, 8, 9, 18], "vertices": [1, 4, -5.13, 44.79, 1, 1, 4, 41.21, 45.06, 1, 1, 4, 76.3, 45.1, 1, 7, 68, -138.66, 88.62, 0.0183, 67, -60.91, 80.59, 0.07337, 4, 83.19, -1.11, 0.67254, 63, -214.1, 42.3, 0.01563, 62, -97.93, 28.25, 0.0667, 61, -12.49, 22.78, 0.14851, 64, -15.71, 196.7, 0.00496, 8, 69, -116.03, 128.09, 0.02288, 68, -49.21, 117.98, 0.0691, 67, 26.72, 114.99, 0.1109, 4, 101.59, -93.44, 0.33812, 63, -119.95, 41.69, 0.07254, 62, -3.95, 33.78, 0.17361, 61, 81.04, 33.51, 0.20789, 64, 68.84, 238.11, 0.00496, 8, 69, -7.47, 150.98, 0.09848, 68, 57.07, 149.83, 0.1111, 67, 131.01, 152.83, 0.07337, 4, 120.43, -202.78, 0.10063, 63, -9.07, 38.09, 0.19583, 62, 106.94, 37.42, 0.24646, 61, 191.56, 43.29, 0.16934, 65, 19.82, 288.52, 0.0048, 8, 69, 68.58, 155.33, 0.1831, 68, 132.49, 160.5, 0.11683, 67, 205.71, 167.78, 0.02903, 4, 121.96, -278.94, 0.02083, 63, 65.83, 24.23, 0.28203, 62, 182.58, 28.47, 0.26326, 61, 267.58, 38.54, 0.09458, 66, 43.65, 314.79, 0.01033, 9, 69, 90.43, 87.52, 0.24573, 68, 159.92, 94.75, 0.16947, 67, 236.83, 103.69, 0.04992, 4, 53.38, -298.26, 0.01, 63, 70.93, -46.83, 0.22533, 62, 192.3, -42.11, 0.18396, 61, 281.2, -31.39, 0.05841, 66, 78.35, 252.57, 0.03887, 65, 133.21, 261.06, 0.0183, 10, 69, 96.5, -18.62, 0.28262, 68, 174.8, -10.51, 0.21312, 67, 257.67, -0.55, 0.0663, 4, -52.9, -300.39, 0.01, 63, 51.57, -151.36, 0.11531, 62, 179.8, -147.68, 0.08298, 61, 274.57, -137.49, 0.01508, 66, 105.07, 149.68, 0.11853, 65, 174.07, 162.92, 0.07993, 64, 308.42, 141.71, 0.01612, 9, 69, 42.39, -96.75, 0.24285, 68, 127.39, -92.88, 0.17407, 67, 215.03, -85.49, 0.04888, 4, -128.98, -243.42, 0.00504, 63, -19.57, -214.38, 0.03775, 62, 112.92, -215.2, 0.01983, 66, 67.31, 62.47, 0.22933, 65, 148.89, 71.27, 0.17784, 64, 272.81, 53.59, 0.06441, 7, 69, 17.21, -133.07, 0.17891, 68, 105.32, -131.18, 0.12206, 67, 195.17, -124.98, 0.0273, 63, -52.67, -243.67, 0.00983, 66, 49.72, 21.92, 0.28672, 65, 137.16, 28.66, 0.25804, 64, 256.23, 12.62, 0.11714, 8, 69, -46.11, -128.73, 0.09597, 68, 41.86, -132.12, 0.11682, 67, 131.87, -129.53, 0.07093, 4, -157.65, -153.79, 0.03484, 62, 20.2, -231.29, 0.0052, 66, -13.22, 13.78, 0.19833, 65, 75.97, 11.8, 0.24034, 64, 193.51, 2.94, 0.23756, 8, 69, -122.96, -114.89, 0.02184, 68, -35.87, -124.74, 0.07215, 67, 53.84, -126.58, 0.10685, 4, -140.98, -77.51, 0.18718, 61, 45.2, -206.91, 0.00464, 66, -91.29, 12.31, 0.07358, 65, -1.12, -0.59, 0.17056, 64, 115.5, -0.46, 0.36321, 6, 68, -112.83, -113.98, 0.01982, 67, -23.6, -120.22, 0.07093, 4, -121.05, -2.4, 0.53102, 66, -168.97, 14.27, 0.01563, 65, -78.31, -9.53, 0.06518, 64, 37.79, -0.41, 0.29742, 1, 4, -102.66, 54.69, 1, 1, 4, -57, 55.6, 1, 8, 69, -162.5, 7.47, 0.008, 68, -85.47, -6.09, 0.048, 67, -2.43, -10.94, 0.13792, 4, -17.23, -42.53, 0.6213, 62, -70.7, -76.9, 0.01248, 61, 20.52, -80.71, 0.04827, 65, -78.95, 101.78, 0.01152, 64, 50.02, 110.22, 0.11251, 10, 69, -80.76, -4.65, 0.05283, 68, -3, -11.36, 0.146, 67, 80.21, -11.51, 0.20592, 4, -32.37, -123.77, 0.27419, 63, -117.27, -95.63, 0.01227, 62, 7.68, -103.07, 0.05096, 61, 100.24, -102.49, 0.07147, 66, -71.49, 128.68, 0.0129, 65, 2.19, 117.41, 0.04904, 64, 132.43, 116.37, 0.12442, 10, 69, 0.53, -8.77, 0.17245, 68, 78.35, -8.69, 0.219, 67, 161.27, -4.22, 0.14792, 4, -39.5, -204.85, 0.0681, 63, -39.3, -118.97, 0.06129, 62, 87.01, -121.27, 0.08138, 61, 180.46, -116.27, 0.04827, 66, 9.03, 140.56, 0.06276, 65, 80.26, 140.44, 0.07712, 64, 212.63, 130.22, 0.06171, 8, 69, -177.3, -57.1, 0.00403, 68, -94.83, -71.68, 0.03648, 67, -8.05, -76.95, 0.1095, 4, -81.21, -25.35, 0.5611, 61, -1.87, -143.06, 0.01114, 66, -155.89, 58.35, 0.00397, 65, -71.53, 35.94, 0.04202, 64, 49.78, 43.97, 0.23176, 9, 69, -98.75, -71.75, 0.0391, 68, -15.34, -79.73, 0.1149, 67, 71.78, -80.47, 0.16756, 4, -98.76, -103.3, 0.21514, 62, -21.71, -166.02, 0.01248, 61, 74.38, -166.97, 0.01914, 66, -75.99, 59.35, 0.0454, 65, 7.44, 48.13, 0.11212, 64, 129.63, 46.95, 0.27416, 10, 69, -19.38, -86.19, 0.13876, 68, 64.96, -87.51, 0.18094, 67, 152.39, -83.67, 0.11454, 4, -116.14, -182.08, 0.0441, 63, -77.05, -189.43, 0.0144, 62, 53.93, -194.05, 0.02048, 61, 151.46, -190.77, 0.01114, 66, 4.67, 60.73, 0.13901, 65, 87.11, 60.79, 0.18041, 64, 210.23, 50.31, 0.15623, 10, 69, 14.19, 82.25, 0.14096, 68, 84.38, 83.15, 0.17706, 67, 162.07, 87.81, 0.11718, 4, 50.95, -221.87, 0.0865, 63, -4.38, -33.81, 0.13607, 62, 116.3, -34.03, 0.18525, 61, 204.87, -27.53, 0.11042, 66, 4.61, 232.48, 0.01513, 65, 63.01, 230.84, 0.01952, 64, 205.95, 222.01, 0.0119, 9, 69, -87.55, 70.71, 0.03973, 68, -16.05, 63.17, 0.1116, 67, 62.94, 62.15, 0.1704, 4, 43.18, -119.77, 0.3121, 63, -105.95, -20.82, 0.04477, 62, 14.11, -27.68, 0.11638, 61, 102.48, -26.86, 0.1617, 65, -29.17, 186.26, 0.01152, 64, 109.24, 188.38, 0.03181, 8, 69, -174.68, 63.74, 0.00422, 68, -102.29, 48.97, 0.03552, 67, -22.36, 43.07, 0.11182, 4, 39.45, -32.45, 0.6641, 63, -192.23, -6.86, 0.00378, 62, -72.9, -19.38, 0.04298, 61, 15.14, -23.39, 0.10578, 64, 25.77, 162.43, 0.03181], "hull": 16, "edges": [32, 34, 34, 36, 36, 16, 32, 38, 38, 26, 26, 24, 24, 40, 40, 34, 38, 40, 40, 42, 42, 22, 22, 24, 42, 36, 42, 18, 18, 20, 20, 22, 18, 16, 36, 44, 44, 10, 10, 12, 14, 16, 12, 14, 14, 44, 34, 46, 46, 8, 8, 10, 46, 44, 46, 48, 48, 6, 6, 8, 48, 32, 48, 2, 2, 4, 4, 6, 2, 0, 0, 32, 26, 28, 28, 30, 30, 38, 30, 0], "width": 285, "height": 356}}, "yj_19": {"yj_19": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [251.81, 91.37, 200.17, -82.11, -23.14, -15.63, 28.49, 157.84], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 181, "height": 233}}, "yj_2": {"yj_2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-52.76, -254.15, -85, 132.5, 381.38, 171.39, 413.62, -215.27], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 388, "height": 468}}, "yj_3": {"yj_3": {"type": "mesh", "uvs": [0.93538, 0.21316, 0.86345, 0.45041, 0.83367, 0.61718, 0.78678, 0.87974, 0.66002, 0.93277, 0.49928, 1, 0.37965, 0.74758, 0.27695, 0.53091, 0.14006, 0.32438, 1e-05, 0.11308, 0.45329, 0, 1, 0, 0.55679, 0.44658, 0.59622, 0.63231, 0.49568, 0.18292], "triangles": [14, 10, 11, 8, 9, 10, 0, 14, 11, 14, 8, 10, 12, 14, 0, 7, 8, 14, 1, 12, 0, 12, 7, 14, 2, 12, 1, 13, 12, 2, 7, 12, 13, 6, 7, 13, 3, 13, 2, 4, 13, 3, 6, 13, 4, 5, 6, 4], "vertices": [1, 97, -3.18, -21.13, 1, 2, 95, 128.58, -133.51, 0.472, 97, -35.39, 97.86, 0.528, 1, 95, 106.12, -0.69, 1, 1, 95, 93.48, 1.34, 1, 1, 95, 90.82, 7.38, 1, 1, 95, 87.46, 15.04, 1, 1, 95, 99.47, 20.99, 1, 2, 95, 91.45, -88.37, 0.472, 97, -11.35, 112.29, 0.528, 1, 97, -11.67, 16.47, 1, 1, 97, -2.12, 24.02, 1, 1, 97, 5.09, 2.78, 1, 1, 97, 7.27, -23.37, 1, 2, 95, 113.03, -107.53, 0.472, 97, -21.96, 103.4, 0.528, 1, 95, 105.19, 10.7, 1, 1, 97, -3.49, 0.03, 1], "hull": 12, "edges": [2, 24, 24, 14, 20, 22, 20, 18, 10, 8, 8, 6, 10, 12, 12, 14, 24, 26, 26, 8, 12, 26, 6, 4, 4, 2, 26, 4, 18, 16, 16, 14, 20, 28, 28, 24, 16, 28, 22, 0, 0, 2, 28, 0], "width": 48, "height": 48}}, "yj_18": {"yj_18": {"type": "mesh", "uvs": [0.46827, 0.05374, 0.79441, 0.17508, 0.93755, 0.3062, 1, 0.42754, 0.95231, 0.51757, 0.80326, 0.59194, 0.68033, 0.65089, 0.62596, 0.73388, 0.64648, 0.80463, 0.57528, 0.81019, 0.56913, 0.89182, 0.42037, 1, 0.23469, 0.97481, 0.17752, 0.94311, 0.12184, 0.91223, 0.11876, 0.80339, 0.05864, 0.71348, 0, 0.57879, 0, 0.40014, 0.03293, 0.15159, 0.11557, 0.03808, 0.28971, 0, 0.48295, 0.80202, 0.23776, 0.89318, 0.53568, 0.67538, 0.27203, 0.73933, 0.26587, 0.60327, 0.13763, 0.62368, 0.5244, 0.56246, 0.17097, 0.72708, 0.17758, 0.84777, 0.57748, 0.43146, 0.34874, 0.39623, 0.16132, 0.42754, 0.6734, 0.27685, 0.34136, 0.15942, 0.15542, 0.13007], "triangles": [28, 31, 6, 17, 18, 33, 6, 31, 5, 26, 33, 32, 31, 34, 5, 5, 34, 4, 28, 32, 31, 4, 34, 2, 34, 1, 2, 4, 2, 3, 34, 31, 35, 18, 19, 33, 19, 36, 33, 33, 35, 32, 33, 36, 35, 31, 32, 35, 35, 0, 34, 34, 0, 1, 36, 21, 35, 35, 21, 0, 19, 20, 36, 36, 20, 21, 26, 32, 28, 27, 33, 26, 27, 17, 33, 7, 24, 6, 22, 25, 24, 30, 29, 25, 15, 16, 29, 29, 26, 25, 24, 25, 26, 16, 27, 29, 29, 27, 26, 16, 17, 27, 24, 26, 28, 9, 24, 7, 24, 28, 6, 23, 25, 22, 22, 24, 9, 9, 7, 8, 23, 30, 25, 15, 29, 30, 12, 13, 23, 13, 30, 23, 13, 14, 30, 14, 15, 30, 11, 22, 10, 11, 23, 22, 10, 22, 9, 12, 23, 11], "vertices": [1, 72, 119.52, 56.93, 1, 1, 72, 161.79, -38.82, 1, 1, 72, 166.34, -92.39, 1, 1, 72, 156.8, -125.26, 1, 2, 71, 102.96, -180.45, 0.048, 72, 131.24, -127.23, 0.952, 2, 71, 83.44, -135.29, 0.264, 72, 88.57, -102.7, 0.736, 3, 70, 107.09, 50.19, 0.112, 71, 67.89, -98.01, 0.47597, 72, 53.82, -82.13, 0.41203, 3, 70, 85.87, 35.85, 0.248, 71, 47.76, -82.15, 0.55949, 72, 28.21, -81.46, 0.19251, 2, 70, 87.73, 18.29, 0.616, 71, 31.6, -89.28, 0.384, 2, 70, 66.15, 22.72, 0.616, 71, 29.25, -67.37, 0.384, 1, 70, 59.4, 4.84, 1, 1, 70, 8.48, -7.64, 1, 1, 70, -45.43, 12.86, 1, 1, 70, -60.59, 24.55, 1, 1, 70, -75.35, 35.95, 1, 2, 70, -69.72, 60.7, 0.616, 71, 24.04, 73.61, 0.384, 3, 70, -82.25, 85.73, 0.248, 71, 44.07, 93.17, 0.55949, 72, -79.61, 56.85, 0.19251, 3, 70, -91.65, 120.73, 0.112, 71, 74.55, 112.78, 0.47597, 72, -66.91, 90.79, 0.41203, 2, 71, 116.12, 114.79, 0.264, 72, -34.79, 117.26, 0.736, 2, 71, 174.46, 107.41, 0.048, 72, 16.38, 146.24, 0.952, 1, 72, 53.03, 143.35, 1, 1, 72, 94.09, 107.47, 1, 2, 70, 39.08, 31.93, 0.616, 71, 29.78, -38.78, 0.384, 2, 70, -39.6, 30.98, 0.616, 71, 4.92, 35.87, 0.384, 3, 70, 62.44, 56.22, 0.248, 71, 60.04, -53.63, 0.55949, 72, 20.99, -51.26, 0.19251, 3, 70, -20.11, 62.88, 0.248, 71, 41.23, 27.02, 0.55949, 72, -42.32, 2.14, 0.19251, 3, 70, -13.76, 93.99, 0.112, 71, 72.8, 30.45, 0.47597, 72, -19.07, 23.76, 0.41203, 3, 70, -53.27, 99.64, 0.112, 71, 66.15, 69.8, 0.47597, 72, -47.94, 51.32, 0.41203, 3, 70, 65.87, 82.54, 0.112, 71, 86.15, -48.89, 0.47597, 72, 39.08, -31.84, 0.41203, 3, 70, -49.54, 73.7, 0.248, 71, 42.58, 58.35, 0.55949, 72, -59.98, 28.05, 0.19251, 2, 70, -54.83, 46.01, 0.616, 71, 14.59, 54.96, 0.384, 2, 71, 117.43, -63.8, 0.264, 72, 73.06, -25.08, 0.736, 2, 71, 122.22, 7.19, 0.264, 72, 34.45, 34.68, 0.736, 2, 71, 112.15, 64.69, 0.264, 72, -8.01, 74.73, 0.736, 2, 71, 154.83, -91.67, 0.048, 72, 119.71, -25.05, 0.952, 2, 71, 177.22, 12.13, 0.048, 72, 75.58, 71.53, 0.952, 2, 71, 181.29, 69.85, 0.048, 72, 44.32, 120.22, 0.952], "hull": 22, "edges": [24, 22, 22, 20, 20, 18, 18, 44, 44, 46, 30, 28, 46, 24, 44, 22, 18, 16, 16, 14, 14, 48, 48, 50, 32, 30, 46, 50, 50, 52, 52, 54, 54, 34, 34, 32, 52, 56, 56, 12, 12, 14, 56, 48, 48, 44, 50, 58, 58, 32, 54, 58, 46, 60, 60, 30, 58, 60, 28, 26, 26, 24, 60, 26, 56, 62, 62, 10, 10, 12, 62, 64, 64, 52, 64, 66, 34, 36, 66, 36, 66, 54, 62, 68, 68, 8, 8, 10, 68, 70, 70, 72, 72, 38, 38, 36, 66, 72, 70, 64, 68, 2, 2, 4, 4, 6, 6, 8, 2, 0, 0, 42, 42, 40, 40, 38, 72, 42, 70, 0], "width": 309, "height": 233}}, "baiy1": {"baiy1": {"type": "mesh", "uvs": [1, 0.20222, 1, 0.36453, 1, 0.58615, 0.91192, 0.64858, 0.79547, 0.62048, 0.84273, 0.39887, 0.7124, 0.42272, 0.56558, 0.51636, 0.48795, 0.60376, 0.61501, 0.73044, 0.47014, 0.92399, 0.25687, 0.95346, 0.149, 1, 0, 1, 0, 0.93986, 0, 0.90132, 0.12081, 0.92626, 0.21764, 0.82878, 0.36718, 0.81518, 0.40787, 0.74406, 0.29909, 0.6009, 0.3985, 0.37902, 0.63646, 0.13244, 0.86973, 0, 1, 0, 0.88829, 0.24592, 0.94905, 0.38326, 0.40694, 0.56006, 0.69215, 0.27914, 0.48795, 0.4196, 0.48824, 0.75557, 0.39292, 0.86278, 0.23603, 0.90132, 0.13307, 0.9648], "triangles": [23, 24, 0, 25, 23, 0, 28, 22, 23, 25, 28, 23, 26, 25, 0, 1, 26, 0, 5, 28, 25, 5, 25, 26, 29, 21, 22, 29, 22, 28, 6, 28, 5, 7, 29, 28, 6, 7, 28, 27, 21, 29, 26, 1, 2, 20, 21, 27, 8, 29, 7, 27, 29, 8, 3, 5, 26, 3, 26, 2, 4, 5, 3, 30, 19, 27, 20, 27, 19, 30, 8, 9, 30, 27, 8, 31, 18, 19, 31, 19, 30, 32, 17, 18, 32, 18, 31, 10, 31, 30, 10, 30, 9, 33, 16, 17, 14, 15, 16, 11, 32, 31, 11, 31, 10, 32, 33, 17, 14, 16, 33, 13, 14, 33, 12, 33, 32, 13, 33, 12, 12, 32, 11], "vertices": [756.21, 866.34, 756.21, 832.91, 756.21, 787.25, 722.65, 774.39, 678.28, 780.18, 696.29, 825.83, 646.63, 820.92, 590.69, 801.63, 561.12, 783.63, 609.53, 757.53, 554.33, 717.66, 473.07, 711.59, 431.98, 702, 375.21, 702, 375.21, 714.39, 375.21, 722.33, 421.24, 717.19, 458.13, 737.27, 515.1, 740.07, 530.61, 754.72, 489.16, 784.21, 527.04, 829.92, 617.7, 880.72, 706.58, 908, 756.21, 908, 713.65, 857.34, 736.8, 829.05, 530.25, 792.63, 638.92, 850.5, 561.12, 821.56, 561.23, 752.35, 524.91, 730.27, 465.14, 722.33, 425.91, 709.25], "hull": 25, "edges": [46, 48, 46, 50, 50, 10, 10, 8, 8, 6, 6, 4, 10, 52, 4, 2, 52, 2, 2, 0, 0, 48, 50, 0, 0, 52, 52, 6, 46, 44, 44, 42, 42, 40, 40, 54, 54, 16, 16, 14, 14, 12, 12, 10, 50, 56, 56, 58, 58, 54, 42, 58, 58, 14, 44, 56, 56, 12, 54, 60, 60, 38, 38, 40, 60, 18, 18, 16, 60, 62, 62, 64, 64, 34, 34, 36, 36, 38, 36, 62, 62, 20, 20, 18, 20, 22, 22, 64, 34, 32, 32, 30, 64, 66, 26, 28, 28, 30, 66, 28, 32, 66, 24, 26, 66, 24, 24, 22], "width": 381, "height": 206}}, "baiy2": {"baiy2": {"type": "mesh", "uvs": [1, 0.41923, 1, 0.831, 0.84942, 0.87919, 0.69857, 0.61198, 0.52257, 0.66454, 0.35914, 0.90109, 0.19257, 1, 0.02442, 0.86167, 0, 0.73901, 0, 0.63388, 0.17842, 0.75653, 0.31357, 0.6295, 0.48014, 0.1564, 0.719, 0, 0.89028, 0.11698, 1, 0, 0.906, 0.45866, 0.67814, 0.25277, 0.48328, 0.4937, 0.33242, 0.8091, 0.17214, 0.87919], "triangles": [17, 12, 13, 14, 15, 0, 16, 14, 0, 17, 13, 14, 16, 17, 14, 18, 12, 17, 3, 17, 16, 4, 18, 17, 18, 11, 12, 3, 4, 17, 8, 20, 7, 19, 11, 18, 5, 19, 18, 10, 11, 19, 16, 0, 1, 20, 8, 10, 2, 3, 16, 2, 16, 1, 9, 10, 8, 20, 10, 19, 4, 5, 18, 6, 20, 19, 7, 20, 6, 6, 19, 5], "vertices": [756.21, 681.63, 756.21, 635.1, 708.78, 629.65, 661.26, 659.85, 605.82, 653.91, 554.34, 627.18, 501.87, 616, 448.9, 631.63, 441.21, 645.49, 441.21, 657.37, 497.41, 643.51, 539.98, 657.87, 592.45, 711.33, 667.69, 729, 721.65, 715.78, 756.21, 729, 726.6, 677.17, 654.82, 700.44, 593.44, 673.21, 545.92, 637.57, 495.43, 629.65], "hull": 16, "edges": [30, 28, 28, 32, 32, 4, 4, 2, 2, 0, 0, 30, 32, 0, 28, 26, 26, 34, 34, 6, 6, 4, 32, 34, 34, 36, 36, 24, 24, 26, 36, 8, 8, 6, 36, 38, 38, 22, 22, 24, 38, 10, 10, 8, 38, 40, 40, 16, 16, 18, 18, 20, 20, 22, 20, 40, 40, 12, 12, 14, 14, 16, 12, 10], "width": 315, "height": 113}}, "baiy3": {"baiy3": {"type": "mesh", "uvs": [0.92261, 0.13348, 0.62527, 0.25855, 0.54303, 0.48067, 0.36853, 0.68569, 0.18499, 0.78294, 0.20921, 0.89838, 0.15216, 0.97602, 0.0728, 0.99999, 0, 1, 1e-05, 0.97263, 0.05558, 0.95522, 0.10385, 0.90756, 0.10902, 0.76945, 0.23897, 0.6099, 0.31808, 0.49038, 0.20189, 0.33481, 0.29051, 0.12128, 0.35943, 0, 0.70645, 1e-05, 1, 0, 0.54241, 0.07963, 0.42221, 0.30791, 0.4126, 0.48428, 0.29783, 0.63469, 0.14209, 0.79997, 0.14503, 0.90325, 0.11342, 0.96069], "triangles": [20, 17, 18, 16, 17, 20, 0, 18, 19, 20, 18, 0, 1, 20, 0, 21, 16, 20, 21, 20, 1, 15, 16, 21, 2, 21, 1, 21, 14, 15, 22, 21, 2, 22, 14, 21, 23, 13, 14, 23, 14, 22, 3, 23, 22, 3, 22, 2, 13, 24, 12, 23, 24, 13, 4, 23, 3, 23, 4, 24, 25, 24, 4, 25, 4, 5, 11, 12, 24, 11, 24, 25, 26, 11, 25, 10, 11, 26, 6, 25, 5, 26, 25, 6, 26, 8, 10, 7, 26, 6, 8, 9, 10, 7, 8, 26], "vertices": [827.04, 1436.7, 627.76, 1341.79, 392.1, 1273.79, 274.73, 1234.88, 149.99, 1210.2, 155.19, 1163.26, 103.79, 1129.16, 39.29, 1114.87, -18.56, 1110.07, -13.3, 1122.88, 29.48, 1131.49, 72.25, 1153.76, 96.46, 1214.4, 205.08, 1277.55, 260.69, 1311.88, 242.5, 1388.01, 317.15, 1453.42, 443.45, 1542.23, 731.14, 1523.39, 941.65, 1503.7, 582.4, 1433.06, 432.6, 1338.55, 315.56, 1296.9, 237.57, 1262.3, 115.62, 1201.43, 104.54, 1157.63, 74.24, 1132.62], "hull": 20, "edges": [34, 36, 36, 38, 38, 0, 0, 40, 40, 32, 34, 32, 36, 40, 40, 42, 42, 44, 44, 28, 28, 30, 30, 32, 30, 42, 42, 2, 2, 4, 4, 44, 2, 0, 44, 46, 46, 48, 48, 24, 24, 26, 26, 28, 26, 46, 46, 6, 6, 4, 6, 8, 8, 48, 48, 50, 50, 22, 16, 18, 24, 22, 50, 10, 10, 8, 14, 16, 50, 52, 52, 16, 18, 20, 20, 22, 20, 52, 14, 12, 12, 10, 52, 12], "width": 350, "height": 226}}, "baiy4": {"baiy4": {"type": "mesh", "uvs": [0.50081, 0.12349, 0.68495, 0.37799, 0.7813, 0.68281, 0.89693, 0.77751, 1, 0.91956, 1, 1, 0.92262, 1, 0.75347, 0.90772, 0.61215, 0.77455, 0.40017, 0.48157, 0, 0.2537, 0, 0, 0.18606, 0, 0.4286, 0, 0.25886, 0.20339, 0.70208, 0.75383, 0.56076, 0.40463, 0.83697, 0.84853], "triangles": [14, 12, 13, 14, 13, 0, 10, 11, 12, 10, 12, 14, 16, 0, 1, 14, 0, 16, 9, 14, 16, 10, 14, 9, 15, 1, 2, 16, 1, 15, 8, 16, 15, 9, 16, 8, 17, 2, 3, 15, 2, 17, 7, 15, 17, 8, 15, 7, 17, 4, 5, 6, 17, 5, 7, 17, 6, 4, 17, 3], "vertices": [-426.52, 1471.62, -363.91, 1409.01, -331.15, 1334.03, -291.84, 1310.73, -256.79, 1275.79, -256.79, 1256, -283.1, 1256, -340.61, 1278.7, -388.66, 1311.46, -460.73, 1383.53, -596.79, 1439.59, -596.79, 1502, -533.53, 1502, -451.07, 1502, -508.78, 1451.97, -358.09, 1316.56, -406.13, 1402.46, -312.22, 1293.26], "hull": 14, "edges": [22, 24, 24, 28, 20, 22, 28, 20, 28, 0, 0, 2, 2, 4, 4, 30, 30, 16, 16, 18, 18, 20, 28, 32, 32, 30, 2, 32, 32, 18, 30, 34, 34, 10, 10, 8, 8, 6, 6, 4, 6, 34, 34, 14, 10, 12, 14, 12, 14, 16, 24, 26, 0, 26], "width": 340, "height": 246}}, "baiy5": {"baiy5": {"type": "mesh", "uvs": [0.08257, 0.05126, 0.20034, 0.23754, 0.40154, 0.33999, 0.58146, 0.60389, 0.68939, 0.69259, 0.83955, 0.81271, 0.87816, 0.89007, 1, 0.95318, 1, 1, 0.84813, 1, 0.78163, 0.91653, 0.62932, 0.82899, 0.46696, 0.71566, 0.35083, 0.53559, 0.1938, 0.52317, 0.03187, 0.43313, 0, 0.37414, 0, 0, 0.05967, 0, 0.04168, 0.2158, 0.17744, 0.37104, 0.38191, 0.44555, 0.52748, 0.64736, 0.65292, 0.75367, 0.80737, 0.85953, 0.85456, 0.9491], "triangles": [0, 19, 17, 0, 17, 18, 19, 0, 1, 20, 19, 1, 16, 17, 19, 15, 16, 19, 20, 15, 19, 21, 1, 2, 20, 1, 21, 13, 14, 20, 15, 20, 14, 21, 13, 20, 22, 21, 2, 3, 22, 2, 12, 13, 21, 22, 12, 21, 23, 3, 4, 22, 3, 23, 11, 22, 23, 12, 22, 11, 24, 4, 5, 23, 4, 24, 10, 23, 24, 11, 23, 10, 6, 24, 5, 25, 24, 6, 10, 24, 25, 9, 10, 25, 6, 7, 8, 25, 6, 8, 9, 25, 8], "vertices": [-688.9, 1483.39, -607.76, 1415.77, -469.13, 1378.58, -345.16, 1282.79, -270.8, 1250.59, -167.34, 1206.99, -140.74, 1178.91, -56.79, 1156, -56.79, 1139, -161.43, 1139, -207.25, 1169.3, -312.19, 1201.08, -424.05, 1242.22, -504.07, 1307.58, -612.26, 1312.09, -723.84, 1344.77, -745.79, 1366.19, -745.79, 1502, -704.68, 1502, -717.07, 1423.66, -623.53, 1367.31, -482.66, 1340.26, -382.36, 1267.01, -295.93, 1228.42, -189.51, 1189.99, -157, 1157.48], "hull": 19, "edges": [34, 38, 38, 30, 32, 34, 30, 32, 38, 0, 34, 36, 0, 36, 38, 40, 40, 28, 28, 30, 0, 2, 2, 40, 40, 42, 42, 44, 44, 24, 24, 26, 26, 28, 26, 42, 42, 4, 4, 6, 6, 44, 4, 2, 44, 46, 46, 48, 48, 20, 20, 22, 22, 24, 6, 8, 8, 10, 10, 48, 48, 50, 50, 16, 16, 14, 14, 12, 12, 10, 12, 50, 16, 18, 50, 18, 18, 20], "width": 413, "height": 217}}, "baiy6": {"baiy6": {"type": "mesh", "uvs": [0.14461, 0.06655, 0.23888, 0.28564, 0.17218, 0.40415, 0.27523, 0.52059, 0.47849, 0.61045, 0.61681, 0.6417, 0.74838, 0.83313, 0.86759, 0.7932, 1, 0.94751, 1, 1, 0.96373, 1, 0.84819, 0.92798, 0.72137, 0.96596, 0.56536, 0.83118, 0.42199, 0.76476, 0.16728, 0.76086, 0.0181, 0.57726, 0.01235, 0.46274, 0, 0.44943, 0, 0.18891, 0, 0, 0.0683, 0.25689, 0.10146, 0.48272, 0.21742, 0.64781, 0.4591, 0.68467, 0.5991, 0.75304, 0.73657, 0.9054, 0.8718, 0.85375], "triangles": [21, 19, 20, 0, 21, 20, 2, 21, 0, 1, 2, 0, 18, 19, 21, 17, 18, 21, 22, 21, 2, 17, 21, 22, 16, 17, 22, 23, 2, 3, 22, 2, 23, 24, 3, 4, 23, 3, 24, 25, 4, 5, 24, 4, 25, 15, 22, 23, 16, 22, 15, 14, 23, 24, 15, 23, 14, 13, 24, 25, 14, 24, 13, 25, 5, 6, 8, 27, 7, 26, 25, 6, 27, 26, 6, 27, 6, 7, 11, 26, 27, 12, 25, 26, 13, 25, 12, 12, 26, 11, 9, 10, 27, 11, 27, 10, 9, 27, 8], "vertices": [-681.15, 1029.16, -639.01, 986.87, -668.83, 964, -622.76, 941.53, -531.91, 924.18, -470.08, 918.15, -411.27, 881.21, -357.98, 888.91, -298.79, 859.13, -298.79, 849, -315, 849, -366.65, 862.9, -423.34, 855.57, -493.07, 881.58, -557.16, 894.4, -671.02, 895.15, -737.7, 930.59, -740.27, 952.69, -745.79, 955.26, -745.79, 1005.54, -745.79, 1042, -715.26, 992.42, -700.44, 948.84, -648.61, 916.97, -540.58, 909.86, -477.99, 896.66, -416.54, 867.26, -356.1, 877.23], "hull": 21, "edges": [38, 40, 38, 42, 42, 0, 40, 0, 36, 38, 36, 34, 34, 42, 42, 44, 44, 32, 32, 34, 44, 4, 4, 2, 2, 0, 44, 46, 46, 48, 48, 8, 8, 6, 6, 4, 6, 46, 46, 30, 32, 30, 30, 28, 28, 48, 48, 50, 50, 52, 52, 12, 12, 10, 10, 8, 10, 50, 50, 26, 26, 24, 24, 52, 26, 28, 52, 54, 54, 14, 14, 12, 54, 22, 22, 24, 54, 18, 18, 16, 16, 14, 18, 20, 22, 20], "width": 447, "height": 193}}, "baiy7": {"baiy7": {"type": "mesh", "uvs": [0.0889, 0.09378, 0.11381, 0.2502, 0.17018, 0.18717, 0.27768, 0.19651, 0.37339, 0.34826, 0.41272, 0.56071, 0.4822, 0.49534, 0.55869, 0.63629, 0.68025, 0.45836, 0.82679, 0.51471, 0.965, 0.66594, 1, 0.86166, 1, 1, 0.91838, 1, 0.80681, 1, 0.68148, 1, 0.52153, 0.80351, 0.3865, 0.90623, 0.23442, 0.83152, 0.10463, 0.51868, 0, 0.47666, 0, 0.38126, 0, 0, 0.03908, 0, 0.10575, 0.39609, 0.22564, 0.57401, 0.25562, 0.73711, 0.37884, 0.7727, 0.49541, 0.71635, 0.6253, 0.76676, 0.69523, 0.88241, 0.86176, 0.84386, 0.71189, 0.64222, 0.03777, 0.1825, 0.20689, 0.3646, 0.29735, 0.51401, 0.33799, 0.68444, 0.4586, 0.64242], "triangles": [33, 22, 23, 33, 23, 0, 33, 0, 1, 34, 2, 3, 34, 3, 4, 1, 2, 34, 21, 22, 33, 21, 33, 1, 24, 21, 1, 24, 1, 34, 20, 21, 24, 35, 34, 4, 19, 20, 24, 35, 4, 5, 25, 34, 35, 25, 19, 24, 34, 25, 24, 32, 8, 9, 7, 8, 32, 37, 5, 6, 37, 6, 7, 36, 35, 5, 25, 35, 36, 36, 5, 37, 28, 37, 7, 26, 25, 36, 29, 7, 32, 28, 7, 29, 27, 36, 37, 27, 37, 28, 26, 36, 27, 16, 28, 29, 18, 25, 26, 19, 25, 18, 31, 9, 10, 32, 9, 31, 31, 10, 11, 30, 29, 32, 30, 32, 31, 16, 17, 27, 16, 27, 28, 18, 26, 27, 17, 18, 27, 15, 29, 30, 16, 29, 15, 14, 30, 31, 15, 30, 14, 13, 31, 11, 14, 31, 13, 13, 11, 12], "vertices": [-704.63, 677.62, -693.1, 636.95, -667, 653.34, -617.22, 650.91, -572.91, 611.45, -554.7, 556.22, -522.53, 573.21, -487.12, 536.57, -430.84, 582.83, -362.99, 568.18, -299, 528.86, -282.79, 477.97, -282.79, 442, -320.58, 442, -372.24, 442, -430.27, 442, -504.32, 493.09, -566.84, 466.38, -637.26, 485.8, -697.35, 567.14, -745.79, 578.07, -745.79, 602.87, -745.79, 702, -727.7, 702, -696.83, 599.02, -641.32, 552.76, -627.44, 510.35, -570.39, 501.1, -516.42, 515.75, -456.28, 502.64, -423.9, 472.57, -346.8, 482.6, -416.19, 535.02, -728.31, 654.55, -650, 607.2, -608.12, 568.36, -589.3, 524.05, -533.46, 534.97], "hull": 24, "edges": [42, 44, 42, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 28, 24, 26, 26, 28, 26, 62, 62, 64, 64, 14, 14, 16, 16, 18, 18, 20, 24, 22, 20, 22, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 44, 46, 0, 46, 68, 70, 70, 72, 72, 74, 40, 42, 40, 38, 38, 36, 36, 34, 34, 32, 28, 30, 32, 30], "width": 277, "height": 156}}, "baiy8": {"baiy8": {"type": "mesh", "uvs": [0.68724, 0.09869, 0.7369, 0.24116, 0.81089, 0.09595, 0.92817, 0.09047, 1, 0.21279, 1, 0.31575, 0.93356, 0.52134, 0.84177, 0.55679, 0.77358, 0.73402, 0.64639, 0.72693, 0.56523, 0.92189, 0.52851, 1, 0.48209, 1, 0.37116, 1, 0.16004, 1, 0, 1, 0, 0.88999, 0, 0.69598, 0.03127, 0.47405, 0.10222, 0.3261, 0.20864, 0.30144, 0.29986, 0.44665, 0.32925, 0.41925, 0.35966, 0.20555, 0.40831, 0.06855, 0.49142, 0, 0.61122, 0, 0.91219, 0.35119, 0.77975, 0.5816, 0.69058, 0.59578, 0.56732, 0.69857, 0.15611, 0.88999, 0.38296, 0.91835, 0.50538, 0.91764, 0.12765, 0.5338, 0.32083, 0.68853, 0.49255, 0.22111, 0.75658, 0.37906, 0.86986, 0.23723], "triangles": [36, 25, 26, 24, 25, 36, 38, 2, 3, 27, 3, 4, 27, 38, 3, 4, 5, 27, 37, 1, 2, 37, 2, 38, 6, 27, 5, 34, 19, 20, 18, 19, 34, 38, 28, 37, 27, 28, 38, 7, 27, 6, 27, 7, 28, 29, 0, 1, 29, 1, 37, 29, 37, 28, 35, 21, 22, 23, 24, 36, 22, 23, 36, 36, 29, 30, 0, 36, 26, 29, 36, 0, 22, 36, 30, 35, 22, 30, 9, 30, 29, 8, 29, 28, 9, 29, 8, 8, 28, 7, 17, 31, 16, 34, 17, 18, 34, 35, 31, 21, 34, 20, 35, 34, 21, 31, 17, 34, 33, 35, 30, 32, 35, 33, 31, 35, 32, 10, 33, 30, 10, 30, 9, 15, 16, 31, 13, 14, 31, 15, 31, 14, 32, 13, 31, 12, 32, 33, 13, 32, 12, 11, 33, 10, 12, 33, 11], "vertices": [511.22, 655.72, 544.25, 620.67, 593.45, 656.4, 671.44, 657.74, 719.21, 627.65, 719.21, 602.33, 675.02, 551.75, 613.98, 543.03, 568.64, 499.43, 484.06, 501.17, 430.08, 453.21, 405.67, 434, 374.8, 434, 301.03, 434, 160.64, 434, 54.21, 434, 54.21, 461.06, 54.21, 508.79, 75, 563.38, 122.18, 599.78, 192.95, 605.85, 253.61, 570.12, 273.16, 576.86, 293.38, 629.44, 325.73, 663.14, 381, 680, 460.67, 680, 660.81, 593.61, 572.74, 536.93, 513.44, 533.44, 431.48, 508.15, 158.02, 461.06, 308.88, 454.09, 390.28, 454.26, 139.1, 548.69, 267.56, 510.62, 381.76, 625.61, 557.33, 586.75, 632.67, 621.64], "hull": 27, "edges": [8, 54, 54, 56, 56, 58, 58, 60, 30, 32, 32, 62, 62, 64, 24, 66, 66, 60, 64, 66, 66, 20, 22, 24, 20, 22, 24, 26, 64, 26, 26, 28, 28, 30, 62, 28, 20, 18, 18, 16, 16, 14, 14, 12, 8, 10, 12, 10, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 0, 0, 2, 2, 4, 4, 6, 6, 8], "width": 399, "height": 147}}, "yj_biyan": {"yj_biyan": {"x": 69.96, "y": -71.49, "rotation": -60.58, "width": 108, "height": 33}}, "jj_1": {"jj_1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-18.76, 9.59, 147.37, 50.09, 226.48, -274.41, 60.34, -314.91], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 171, "height": 334}}, "jj_2": {"jj_2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [20.35, 83.99, 162.1, 34.92, 132.98, -49.18, -8.76, -0.11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 150, "height": 89}}, "jj_3": {"jj_3": {"type": "mesh", "uvs": [0.75179, 0.16784, 0.73945, 0.30142, 0.81716, 0.31674, 0.91429, 0.46683, 0.94537, 0.58834, 0.95217, 0.69964, 1, 0.71629, 0.94611, 0.79433, 0.85769, 0.82761, 0.79494, 0.78927, 0.79437, 0.82646, 0.82275, 0.88614, 0.8686, 0.93204, 0.85114, 0.9527, 0.82821, 0.97221, 0.76489, 0.94466, 0.69939, 0.90679, 0.68144, 0.85664, 0.51187, 0.93524, 0.34897, 0.86366, 0.22818, 0.87128, 0.15159, 0.79077, 0.10526, 0.81263, 0.01827, 0.8176, 0, 0.69236, 0.07122, 0.70031, 0.13268, 0.67745, 0.18464, 0.62195, 0.15911, 0.53051, 0.21955, 0.54664, 0.22592, 0.50778, 0.26673, 0.4622, 0.2578, 0.3925, 0.37383, 0.30672, 0.49147, 0.26892, 0.575, 0.11372, 0.73274, 0, 0.78325, 0, 0.84347, 0, 0.42107, 0.55767, 0.40505, 0.66575, 0.72551, 0.8005, 0.74197, 0.84138, 0.77909, 0.90909, 0.5886, 0.30875, 0.64688, 0.16988], "triangles": [15, 16, 43, 43, 10, 11, 13, 14, 43, 14, 15, 43, 43, 11, 13, 13, 11, 12, 16, 42, 43, 43, 42, 10, 17, 18, 40, 20, 21, 19, 19, 21, 40, 18, 19, 40, 40, 21, 27, 23, 25, 22, 23, 24, 25, 22, 25, 21, 40, 39, 41, 39, 44, 41, 41, 44, 1, 25, 26, 21, 21, 26, 27, 3, 1, 2, 1, 3, 41, 27, 29, 40, 40, 29, 39, 31, 39, 29, 31, 29, 30, 27, 28, 29, 31, 33, 39, 39, 34, 44, 39, 33, 34, 31, 32, 33, 44, 45, 1, 44, 34, 45, 1, 45, 0, 34, 35, 45, 37, 0, 45, 0, 37, 38, 45, 35, 36, 37, 45, 36, 16, 17, 42, 17, 41, 42, 17, 40, 41, 42, 41, 10, 5, 7, 8, 10, 41, 9, 9, 41, 3, 7, 5, 6, 5, 8, 9, 9, 4, 5, 9, 3, 4], "vertices": [1, 7, 259.44, -29.02, 1, 1, 7, 215.19, -34.22, 1, 1, 7, 215.97, -61.8, 1, 1, 7, 174.46, -105.5, 1, 1, 7, 137.34, -124.65, 1, 1, 7, 101.73, -134.81, 1, 1, 7, 99.87, -152.29, 1, 1, 7, 70.56, -139.41, 1, 1, 7, 53.22, -111.59, 1, 1, 7, 61.01, -87.49, 1, 2, 7, 48.9, -89.91, 0.696, 43, 9.07, 18.39, 0.304, 2, 7, 31.64, -103.8, 0.32, 43, 30.23, 11.85, 0.68, 2, 7, 20.14, -122.67, 0.136, 43, 52.3, 12.86, 0.864, 2, 7, 12.15, -118.16, 0.136, 43, 53.04, 3.72, 0.864, 2, 7, 4.12, -111.72, 0.136, 43, 52.18, -6.54, 0.864, 2, 7, 8.37, -88.18, 0.32, 43, 30.3, -16.18, 0.68, 2, 7, 15.81, -63.18, 0.696, 43, 5.41, -23.99, 0.304, 1, 7, 30.76, -53.52, 1, 1, 7, -7.3, -1.22, 1, 1, 7, 3.87, 59.38, 1, 1, 7, -7.54, 100.04, 1, 1, 7, 12.92, 131.83, 1, 1, 7, 2.39, 146.09, 1, 1, 7, -5.66, 175.41, 1, 1, 7, 33.63, 190.46, 1, 1, 7, 36.32, 165.61, 1, 1, 7, 48.28, 146.25, 1, 1, 7, 70.13, 132.44, 1, 1, 7, 97.91, 147.59, 1, 1, 7, 97.15, 125.84, 1, 1, 7, 110.23, 126.4, 1, 1, 7, 128.04, 115.69, 1, 1, 7, 149.99, 123.64, 1, 1, 7, 186.41, 90.11, 1, 1, 7, 207.38, 52.65, 1, 1, 7, 263.92, 35.08, 1, 1, 7, 312.49, -10.71, 1, 1, 7, 316.23, -27.94, 1, 1, 7, 320.68, -48.48, 1, 1, 7, 108.49, 56.33, 1, 1, 7, 72.24, 54.18, 1, 1, 7, 52.23, -64.6, 1, 2, 7, 40.19, -73.09, 0.696, 43, 0, 1.76, 0.304, 2, 7, 20.96, -90.52, 0.32, 43, 25.19, -4.43, 0.68, 1, 7, 201.65, 16.71, 1, 1, 7, 251.02, 6.61, 1], "hull": 39, "edges": [78, 80, 80, 38, 38, 36, 36, 34, 34, 82, 82, 18, 18, 20, 20, 84, 84, 32, 32, 34, 82, 84, 84, 86, 86, 26, 26, 28, 28, 30, 30, 32, 30, 86, 86, 22, 22, 24, 24, 26, 22, 20, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 88, 88, 68, 68, 70, 70, 90, 90, 0, 0, 2, 90, 74, 74, 76, 76, 0, 72, 74, 72, 70, 68, 66, 66, 64, 64, 62, 62, 60, 60, 58, 58, 54, 54, 56, 56, 58, 54, 52, 52, 42, 42, 40, 40, 38, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 50, 44], "width": 349, "height": 332}}, "jj_4": {"jj_4": {"type": "mesh", "uvs": [0.54707, 0.23109, 0.53677, 0.41223, 0.62432, 0.50812, 0.70672, 0.53654, 0.88697, 0.50102, 1, 0.82423, 0.93075, 1, 0.7196, 1, 0.4827, 1, 0.2561, 0.91657, 0, 0.82067, 0, 0.49391, 0.24322, 0.18492, 0.58827, 0, 0.76852, 0.74609, 0.53677, 0.78871, 0.38742, 0.70347, 0.30245, 0.50812], "triangles": [14, 3, 4, 14, 4, 5, 6, 14, 5, 7, 14, 6, 15, 3, 14, 7, 15, 14, 16, 17, 1, 16, 9, 17, 16, 1, 2, 9, 16, 8, 15, 16, 2, 15, 2, 3, 8, 16, 15, 8, 15, 7, 0, 12, 13, 0, 17, 12, 1, 17, 0, 11, 12, 17, 10, 11, 17, 9, 10, 17], "vertices": [3, 45, 18.93, -40.98, 0.65417, 44, 24.42, -41.66, 0.33021, 7, 74.47, 156.7, 0.01563, 3, 45, 8.16, -29.41, 0.34062, 44, 25.65, -25.9, 0.55875, 7, 58.81, 154.57, 0.10063, 3, 45, -5.09, -31.5, 0.10063, 44, 15.15, -17.55, 0.55875, 7, 52.88, 142.53, 0.34062, 3, 45, -13.6, -37.11, 0.01563, 44, 5.26, -15.08, 0.30937, 7, 52.57, 132.35, 0.675, 1, 7, 60.17, 111.86, 1, 1, 7, 35.57, 92.64, 1, 1, 7, 18.86, 97.52, 1, 3, 45, -44.35, -10.98, 0.01563, 44, 3.71, 25.24, 0.30937, 7, 13.49, 122.29, 0.675, 3, 45, -25.12, 9.96, 0.10063, 44, 32.14, 25.24, 0.55875, 7, 7.46, 150.07, 0.34062, 3, 45, -1.38, 25.08, 0.34062, 44, 59.33, 17.98, 0.55875, 7, 8.79, 178.18, 0.10063, 3, 45, 25.55, 42.07, 0.65417, 44, 90.07, 9.64, 0.33021, 7, 10.42, 209.98, 0.01563, 2, 45, 46.49, 22.84, 0.85556, 44, 90.07, -18.79, 0.14444, 2, 45, 46.55, -16.84, 0.87083, 44, 60.88, -45.67, 0.12917, 2, 45, 30.39, -58.22, 0.85556, 44, 19.47, -61.76, 0.14444, 3, 45, -32.05, -30.24, 0.008, 44, -2.16, 3.15, 0.312, 7, 36.32, 121.23, 0.68, 3, 45, -15.97, -7.25, 0.093, 44, 25.65, 6.86, 0.579, 7, 26.8, 147.62, 0.328, 3, 45, 1.62, 0.93, 0.328, 44, 43.58, -0.56, 0.579, 7, 30.25, 166.71, 0.093, 3, 45, 21.03, -3.05, 0.67, 44, 53.77, -17.56, 0.322, 7, 44.69, 180.28, 0.008], "hull": 14, "edges": [6, 28, 28, 14, 28, 30, 30, 32, 32, 18, 14, 16, 18, 16, 16, 30, 30, 4, 4, 2, 2, 32, 6, 8, 8, 10, 12, 14, 10, 12, 28, 10, 6, 4, 32, 34, 34, 24, 24, 22, 20, 22, 20, 18, 20, 34, 34, 0, 0, 26, 26, 24, 0, 2], "width": 120, "height": 87}}, "jj_5": {"jj_5": {"type": "mesh", "uvs": [0.39338, 0.16211, 0.4257, 0.32264, 0.51997, 0.41765, 0.58191, 0.46679, 0.67348, 0.52904, 0.73273, 0.36851, 0.70849, 0.07693, 0.88086, 0.11952, 1, 0.38489, 0.99999, 0.86852, 0.83287, 0.99999, 0.41762, 0.77147, 0.25064, 0.6994, 0.05673, 0.53559, 0, 0.30954, 0, 0.09987, 0.17792, 0, 0.3853, 0, 0.21832, 0.23746, 0.26949, 0.42748, 0.33903, 0.59758, 0.45337, 0.70046, 0.67117, 0.75871, 0.83238, 0.52904, 0.50756, 0.60194], "triangles": [24, 20, 2, 0, 18, 16, 0, 16, 17, 15, 16, 18, 14, 15, 18, 18, 0, 1, 19, 18, 1, 19, 13, 14, 19, 14, 18, 20, 19, 1, 20, 1, 2, 12, 13, 19, 12, 19, 20, 12, 20, 11, 10, 11, 22, 22, 24, 4, 24, 2, 3, 24, 3, 4, 21, 20, 24, 21, 24, 22, 11, 20, 21, 11, 21, 22, 22, 4, 23, 10, 22, 9, 5, 6, 7, 23, 5, 7, 8, 23, 7, 4, 5, 23, 9, 23, 8, 22, 23, 9], "vertices": [2, 47, -16.2, 54.19, 0.01563, 46, -7.66, 26.08, 0.98438, 2, 47, -2.71, 36.62, 0.11563, 46, 14.34, 23.54, 0.88438, 3, 48, 9.7, 46.77, 0.0125, 47, 16.48, 31.15, 0.36285, 46, 31.62, 33.51, 0.62465, 3, 48, 13.75, 35.42, 0.08, 47, 28.37, 29.19, 0.59, 46, 41.3, 40.69, 0.33, 3, 48, 20.58, 19.74, 0.2595, 47, 45.39, 27.57, 0.60183, 46, 54.31, 51.78, 0.13867, 3, 48, 41.29, 31.02, 0.63354, 47, 45.57, 51.15, 0.34396, 46, 37.51, 68.32, 0.0225, 2, 48, 61.8, 64.51, 0.85139, 47, 26.22, 85.32, 0.14861, 2, 48, 80.7, 42.96, 0.87083, 47, 54.23, 91.41, 0.12917, 2, 48, 74.63, 2.9, 0.85556, 47, 86.33, 66.68, 0.14444, 3, 48, 35.4, -48.68, 0.65417, 47, 112.43, 7.37, 0.33021, 46, 115.48, 85.86, 0.01563, 3, 48, 3.06, -46.22, 0.33812, 47, 94.59, -19.73, 0.56125, 46, 122.52, 54.19, 0.10063, 3, 48, -32.28, 19.13, 0.10063, 47, 20.3, -18.96, 0.57625, 46, 70.27, 1.38, 0.32313, 3, 48, -48.1, 43.3, 0.01563, 47, -8.5, -21.08, 0.35972, 46, 51.75, -20.78, 0.62465, 2, 47, -46.27, -13.72, 0.11563, 46, 20.17, -42.78, 0.88438, 2, 47, -66.93, 10.28, 0.01563, 46, -11.44, -40.91, 0.98438, 1, 46, -37.78, -31.14, 1, 1, 46, -40.24, 0.71, 1, 1, 46, -28.49, 32.4, 1, 2, 47, -38.25, 33.45, 0.01333, 46, -8.12, -4.19, 0.98667, 2, 47, -20.36, 13.51, 0.11944, 46, 18.65, -5.22, 0.88056, 3, 48, -28.37, 45.43, 0.01333, 47, -0.81, -2.79, 0.42204, 46, 43.97, -2.52, 0.56463, 3, 48, -21.88, 23.18, 0.0865, 47, 21.8, -7.91, 0.62128, 46, 63.37, 10.16, 0.29222, 3, 48, 1.65, -4.53, 0.312, 47, 57.44, -0.75, 0.59683, 46, 83.04, 40.73, 0.09117, 3, 48, 41.19, 4.06, 0.6655, 47, 69.1, 38, 0.3265, 46, 63.32, 76.07, 0.008, 3, 48, -6.86, 28.34, 0.072, 47, 24.57, 7.74, 0.62578, 46, 54.07, 23.03, 0.30222], "hull": 18, "edges": [0, 36, 36, 28, 28, 30, 30, 32, 32, 34, 34, 0, 32, 36, 36, 38, 38, 40, 40, 24, 24, 26, 26, 28, 26, 38, 38, 2, 2, 4, 4, 40, 2, 0, 40, 42, 42, 44, 44, 8, 8, 6, 6, 4, 42, 22, 22, 20, 24, 22, 44, 20, 44, 46, 46, 14, 14, 12, 12, 10, 10, 8, 10, 46, 46, 18, 18, 20, 18, 16, 16, 14, 42, 48, 48, 6, 40, 48, 48, 8], "width": 163, "height": 134}}, "jj_6": {"jj_6": {"type": "mesh", "uvs": [0.69752, 0.06868, 0.70771, 0.19063, 0.84716, 0.31502, 0.91612, 0.38983, 0.9621, 0.44205, 1, 0.52122, 1, 0.57201, 0.9259, 0.48238, 0.80455, 0.45467, 0.63178, 0.45966, 0.50239, 0.48584, 0.41028, 0.49486, 0.38949, 0.51577, 0.34869, 0.5725, 0.36056, 0.63663, 0.39644, 0.7059, 0.41761, 0.75241, 0.44634, 0.81305, 0.47053, 0.89639, 0.25036, 1, 0.0836, 0.91059, 0.1193, 0.85318, 0.15253, 0.77782, 0.18073, 0.70325, 0.21532, 0.6178, 0.21783, 0.53361, 0.23707, 0.44329, 0.24227, 0.39451, 0.14484, 0.34154, 0.02045, 0.23966, 0, 0.15296, 0.08709, 0.12973, 0.18705, 0.09844, 0.27313, 0.04661, 0.5013, 0, 0.28384, 0.36803, 0.34186, 0.40566, 0.39556, 0.45305, 0.2873, 0.43493, 0.34013, 0.45862, 0.28486, 0.64206, 0.30244, 0.52984, 0.27495, 0.72867, 0.26402, 0.80928, 0.25764, 0.88842, 0.4745, 0.33746, 0.58916, 0.26639, 0.62713, 0.37861, 0.49619, 0.41601, 0.75807, 0.36863, 0.65967, 0.23303, 0.19998, 0.28856, 0.10875, 0.18246, 0.20426, 0.15386, 0.25146, 0.25372, 0.29367, 0.11364, 0.32866, 0.22267, 0.3481, 0.32009, 0.41351, 0.30665, 0.4606, 0.20775, 0.49172, 0.09472], "triangles": [29, 30, 31, 8, 3, 7, 7, 3, 4, 7, 4, 5, 6, 7, 5, 8, 49, 2, 8, 2, 3, 9, 49, 8, 41, 39, 13, 41, 25, 26, 53, 32, 33, 52, 32, 53, 51, 52, 53, 27, 51, 35, 38, 35, 36, 27, 35, 38, 41, 38, 39, 60, 55, 34, 56, 55, 59, 60, 34, 0, 50, 60, 0, 46, 59, 60, 45, 59, 46, 48, 45, 46, 59, 55, 60, 58, 56, 59, 57, 56, 58, 58, 59, 45, 36, 57, 58, 36, 58, 45, 37, 36, 45, 37, 45, 48, 39, 36, 37, 11, 37, 48, 10, 11, 48, 12, 39, 37, 12, 37, 11, 13, 39, 12, 35, 56, 57, 36, 35, 57, 39, 38, 36, 50, 46, 60, 47, 46, 50, 48, 46, 47, 50, 0, 1, 50, 1, 49, 34, 55, 33, 53, 33, 55, 54, 53, 55, 54, 55, 56, 51, 53, 54, 35, 54, 56, 51, 54, 35, 28, 51, 27, 26, 27, 38, 41, 26, 38, 47, 50, 49, 9, 47, 49, 48, 47, 9, 10, 48, 9, 49, 1, 2, 29, 52, 28, 29, 31, 52, 52, 31, 32, 28, 52, 51, 42, 24, 40, 40, 25, 41, 24, 25, 40, 40, 13, 14, 42, 40, 14, 40, 41, 13, 23, 24, 42, 43, 23, 42, 22, 23, 43, 42, 14, 15, 43, 42, 15, 44, 22, 43, 43, 15, 16, 44, 43, 16, 18, 44, 17, 18, 19, 44, 21, 22, 44, 19, 21, 44, 20, 21, 19, 44, 16, 17], "vertices": [1, 6, 349.69, -122.69, 1, 4, 6, 313.68, -138.63, 0.6775, 19, 81.7, -26.01, 0.08008, 17, -35.14, 19, 0.2268, 18, -105.53, 0.01, 0.01563, 4, 6, 295.53, -218.71, 0.34324, 19, 63.55, -106.09, 0.15854, 17, 46.98, 18.61, 0.39759, 18, -24.69, 14.41, 0.10063, 4, 6, 282.47, -259.5, 0.10703, 19, 50.49, -146.87, 0.16814, 17, 89.62, 14.69, 0.40233, 18, 17.97, 18.22, 0.3225, 4, 6, 273.04, -286.9, 0.01563, 19, 41.06, -174.28, 0.0783, 17, 118.42, 11.41, 0.26128, 18, 46.89, 20.18, 0.64479, 3, 19, 22.19, -200.1, 0.02889, 17, 147.71, -1.42, 0.13083, 18, 78.01, 12.83, 0.84028, 3, 19, 6.59, -204.64, 0.05076, 17, 155.52, -15.67, 0.11313, 18, 88.26, 0.21, 0.83611, 4, 6, 255.44, -272.61, 0.0164, 19, 23.46, -159.99, 0.13441, 17, 108.28, -8.86, 0.2194, 18, 40.56, -1.59, 0.62979, 4, 6, 246.49, -210.13, 0.13454, 19, 14.51, -97.5, 0.2873, 17, 49.21, -31.12, 0.31716, 18, -13.54, -34.12, 0.261, 4, 6, 220.1, -125.14, 0.30899, 19, -11.88, -12.51, 0.44078, 17, -28.05, -75.29, 0.17773, 18, -81.59, -91.46, 0.0725, 5, 20, -28.62, 95.05, 0.01563, 6, 193.43, -63.5, 0.53876, 19, -38.54, 49.12, 0.37463, 17, -82.46, -114.66, 0.05849, 18, -128.02, -139.99, 0.0125, 4, 20, -8.54, 51.97, 0.09063, 6, 177.41, -18.76, 0.69798, 19, -54.57, 93.87, 0.20227, 17, -122.67, -139.99, 0.00912, 4, 21, -58.75, 50.09, 0.01563, 20, 1.61, 44.47, 0.3075, 6, 167.99, -10.35, 0.62313, 19, -63.99, 102.27, 0.05375, 4, 21, -35.45, 34.99, 0.09896, 20, 26.21, 31.58, 0.52569, 6, 144.69, 4.76, 0.36772, 19, -87.29, 117.38, 0.00762, 4, 22, -52.39, 60.53, 0.01563, 21, -17.46, 46.6, 0.325, 20, 43.05, 44.79, 0.54375, 6, 126.69, -6.85, 0.11563, 4, 22, -31.12, 80.03, 0.10063, 21, -1.34, 70.53, 0.55875, 20, 56.9, 70.11, 0.325, 6, 110.57, -30.78, 0.01563, 3, 22, -16.76, 91.63, 0.34062, 21, 9.9, 85.16, 0.55875, 20, 66.75, 85.71, 0.10063, 3, 22, 1.93, 107.32, 0.65417, 21, 24.4, 104.79, 0.33021, 20, 79.37, 106.59, 0.01563, 2, 22, 27.98, 121.02, 0.85556, 21, 46.52, 124.21, 0.14444, 2, 22, 66.44, 9.32, 0.87083, 21, 110.05, 24.61, 0.12917, 2, 22, 41.9, -77.81, 0.85556, 21, 106.59, -65.84, 0.14444, 3, 22, 22.69, -60.31, 0.65417, 21, 83.81, -53.33, 0.33021, 20, 153.09, -45.38, 0.01563, 3, 22, -2.21, -44.35, 0.34062, 21, 55.87, -43.64, 0.55875, 20, 124.38, -38.31, 0.10063, 4, 22, -26.73, -30.97, 0.10063, 21, 28.9, -36.37, 0.55875, 20, 96.85, -33.55, 0.325, 6, 80.35, 76.12, 0.01563, 4, 22, -54.88, -14.47, 0.01563, 21, -2.33, -26.91, 0.325, 20, 64.88, -27.01, 0.54375, 6, 111.58, 66.66, 0.11563, 4, 21, -28.56, -33.19, 0.09896, 20, 39.35, -35.68, 0.52569, 6, 137.81, 72.94, 0.36747, 24, -5.78, 129.28, 0.00787, 5, 21, -59.08, -31.76, 0.01563, 20, 8.82, -37.06, 0.3075, 6, 168.33, 71.5, 0.62419, 24, -26.93, 107.22, 0.03925, 23, -9.69, 92.64, 0.01344, 4, 20, -6.68, -40.3, 0.09777, 6, 184.06, 73.29, 0.71226, 24, -35.92, 94.19, 0.12415, 23, -13.96, 77.39, 0.06582, 4, 20, -4.05, -93.19, 0.01563, 6, 186.32, 126.21, 0.55068, 24, 2.46, 57.7, 0.25881, 23, 34.21, 55.37, 0.17489, 3, 6, 199.72, 196.83, 0.32932, 24, 46.86, 1.17, 0.38099, 23, 94.58, 16.34, 0.28969, 3, 6, 223.42, 214.69, 0.23092, 24, 44.74, -28.43, 0.42019, 23, 102.2, -12.33, 0.34889, 3, 6, 243.09, 173.71, 0.39355, 24, 0.93, -16.3, 0.27281, 23, 56.82, -15.11, 0.33364, 3, 6, 267.08, 127.07, 0.68059, 24, -49.99, -3.72, 0.09542, 23, 4.58, -19.77, 0.22399, 3, 6, 295.4, 89.14, 0.89938, 24, -97.18, -0.12, 0.01563, 23, -41.22, -31.71, 0.085, 1, 6, 342.55, -19.52, 1, 5, 20, -22.42, -23.49, 0.05471, 6, 198.18, 55.11, 0.82943, 19, -33.8, 167.73, 0.00448, 24, -58.91, 95.51, 0.04477, 23, -36.12, 71.16, 0.0666, 5, 20, -22.17, 8.73, 0.04578, 6, 194.97, 23.05, 0.88795, 19, -37.01, 135.67, 0.04989, 24, -80.95, 119, 0.00463, 23, -64.61, 86.22, 0.01175, 5, 20, -18.21, 40.01, 0.02277, 6, 188.13, -7.74, 0.79083, 19, -43.84, 104.89, 0.1784, 17, -135.75, -131.9, 0.00584, 23, -90.55, 104.15, 0.00216, 5, 21, -68.88, -7.67, 0.00667, 20, -3.15, -13.98, 0.28327, 6, 178.12, 47.41, 0.68257, 24, -51.52, 115.68, 0.01278, 23, -35.69, 92.64, 0.01471, 5, 21, -69.21, 20.57, 0.00667, 20, -6.08, 14.11, 0.28327, 6, 178.45, 19.17, 0.69354, 19, -53.53, 131.79, 0.01237, 23, -61.98, 102.98, 0.00415, 4, 22, -48.81, 21.67, 0.008, 21, -4.89, 9.65, 0.31867, 20, 58.97, 9.16, 0.55389, 6, 114.13, 30.1, 0.11944, 4, 21, -41.9, 8.3, 0.07639, 20, 22.24, 4.41, 0.48824, 6, 151.14, 31.44, 0.43335, 23, -40.32, 123.66, 0.00202, 4, 22, -20.89, 17.88, 0.093, 21, 23.15, 12.5, 0.579, 20, 86.63, 14.57, 0.31467, 6, 86.09, 27.26, 0.01333, 3, 22, 5.14, 13.47, 0.328, 21, 49.49, 14.3, 0.579, 20, 112.69, 18.8, 0.093, 3, 22, 30.6, 11.39, 0.67, 21, 74.72, 18.23, 0.322, 20, 137.45, 25.03, 0.008, 5, 20, -67.53, 64.27, 0.00571, 6, 235.01, -36.44, 0.8346, 19, 3.03, 76.18, 0.14394, 17, -117.87, -79.93, 0.01402, 23, -134.8, 71.54, 0.00173, 3, 6, 273.35, -86.78, 0.73989, 19, 41.37, 25.84, 0.1909, 17, -77.02, -31.6, 0.06922, 4, 6, 244.33, -115.59, 0.47026, 19, 12.35, -2.97, 0.36518, 17, -42.62, -53.69, 0.14656, 18, -99.8, -72.85, 0.018, 3, 6, 214, -54.19, 0.65532, 19, -17.98, 58.43, 0.30485, 17, -95.99, -96.6, 0.03983, 4, 6, 266.24, -179.45, 0.35862, 19, 34.26, -66.82, 0.27214, 17, 14.98, -18.49, 0.30073, 18, -49.48, -27.85, 0.0685, 4, 6, 293.74, -118.67, 0.69629, 19, 61.77, -6.04, 0.13384, 17, -50.31, -4.79, 0.16187, 18, -116.17, -26.12, 0.008, 4, 20, -30.24, -73, 0.00571, 6, 210.53, 103.68, 0.66903, 24, -30.43, 54.27, 0.1461, 23, 4.22, 41.42, 0.17916, 3, 6, 230, 158.28, 0.48107, 24, -2.09, 3.7, 0.23141, 23, 47.46, 2.82, 0.28752, 3, 6, 252.54, 113.61, 0.73812, 24, -50.57, 16.09, 0.0716, 23, -2.41, -1.22, 0.19027, 4, 20, -50.33, -52.42, 0.00571, 6, 228.64, 81.34, 0.82619, 24, -59.18, 55.31, 0.04558, 23, -23.3, 33.06, 0.12251, 3, 6, 277.76, 72.99, 0.92732, 24, -97.76, 23.79, 0.008, 23, -49.54, -9.29, 0.06468, 4, 6, 249.29, 45.94, 0.94895, 19, 17.31, 158.56, 0.008, 24, -99.42, 63.02, 0.00403, 23, -63.87, 27.27, 0.03902, 5, 20, -48.82, 1.67, 0.01143, 6, 222.16, 27.62, 0.95208, 19, -9.82, 140.24, 0.01018, 24, -95.39, 95.52, 0.00648, 23, -70.62, 59.31, 0.01984, 4, 20, -65.18, 31.43, 0.00571, 6, 235.7, -3.53, 0.94424, 19, 3.72, 109.1, 0.04589, 23, -104.57, 58.5, 0.00415, 4, 6, 272.86, -17.97, 0.9369, 19, 40.89, 94.66, 0.05037, 17, -144.1, -46.97, 0.00934, 23, -131.95, 29.51, 0.00339, 1, 6, 312.07, -23.25, 1], "hull": 35, "edges": [54, 70, 70, 72, 72, 74, 74, 22, 54, 52, 52, 76, 76, 78, 78, 24, 24, 22, 52, 50, 50, 48, 48, 80, 80, 28, 28, 26, 26, 24, 50, 82, 82, 80, 82, 26, 82, 78, 72, 78, 70, 76, 76, 82, 80, 84, 84, 86, 86, 88, 88, 38, 48, 46, 46, 44, 44, 86, 86, 32, 32, 30, 30, 28, 30, 84, 84, 46, 44, 42, 42, 40, 40, 38, 42, 88, 88, 34, 34, 36, 36, 38, 34, 32, 72, 90, 90, 92, 92, 94, 94, 18, 18, 20, 20, 22, 74, 96, 96, 94, 90, 96, 96, 20, 94, 98, 98, 4, 4, 6, 6, 16, 16, 98, 18, 16, 4, 2, 2, 100, 100, 92, 100, 98, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 14, 8, 70, 102, 102, 104, 104, 58, 58, 56, 56, 54, 102, 56, 104, 62, 62, 60, 60, 58, 62, 64, 64, 106, 106, 108, 108, 70, 108, 102, 64, 66, 66, 110, 110, 106, 106, 104, 110, 112, 112, 108, 112, 114, 114, 72, 114, 70, 72, 116, 116, 118, 118, 120, 120, 68, 68, 66, 68, 0, 0, 2, 110, 120, 120, 100, 112, 118, 118, 92, 114, 116, 116, 90], "width": 515, "height": 320}}, "jj_8": {"jj_8": {"type": "mesh", "uvs": [0.91047, 0.13629, 1, 0.53787, 0.90824, 0.78455, 0.72967, 1, 0.56896, 1, 0.30111, 1, 0.2587, 0.56082, 0, 0.41166, 0, 0.11908, 0.21852, 0.1535, 0.41718, 0, 0.54887, 0, 0.7252, 0, 0.85913, 0, 0.75645, 0.2625, 0.47521, 0.50919, 0.61886, 0.38319], "triangles": [14, 12, 13, 14, 13, 0, 2, 14, 0, 2, 0, 1, 3, 14, 2, 16, 12, 14, 16, 11, 12, 15, 10, 11, 15, 11, 16, 4, 15, 16, 3, 16, 14, 4, 16, 3, 5, 15, 4, 15, 6, 10, 7, 8, 9, 6, 9, 10, 7, 9, 6, 5, 6, 15], "vertices": [2, 35, -51.22, 3.77, 0.12917, 6, 243.54, 71.86, 0.87083, 2, 35, -71.2, 52.61, 0.14444, 6, 206.91, 33.88, 0.85556, 3, 36, -120.73, 80.85, 0.01563, 35, -40.84, 76.95, 0.33021, 6, 172.4, 51.84, 0.65417, 3, 36, -65.11, 96.54, 0.09542, 35, 14.25, 94.41, 0.56396, 6, 134.2, 95.21, 0.34062, 3, 36, -18.65, 88.86, 0.30347, 35, 60.93, 88.21, 0.5959, 6, 121.04, 140.42, 0.10063, 3, 36, 58.78, 76.04, 0.51157, 35, 138.73, 77.89, 0.46759, 6, 99.11, 215.78, 0.02083, 3, 36, 62.86, 24.62, 0.63778, 35, 144.46, 26.62, 0.35422, 6, 143.71, 241.7, 0.008, 2, 36, 134.87, -4.53, 0.84444, 35, 217.36, -0.21, 0.15556, 2, 36, 129.42, -37.44, 0.85, 35, 212.97, -33.28, 0.15, 3, 36, 66.9, -23.11, 0.65, 35, 150.02, -20.96, 0.33437, 6, 185.01, 265.98, 0.01563, 3, 36, 6.61, -30.87, 0.34062, 35, 90.01, -30.65, 0.55875, 6, 218.07, 214.98, 0.10063, 3, 36, -31.45, -24.58, 0.10063, 35, 51.76, -25.57, 0.55875, 6, 228.86, 177.93, 0.34062, 3, 36, -82.43, -16.14, 0.01563, 35, 0.55, -18.77, 0.33021, 6, 243.29, 128.32, 0.65417, 2, 35, -38.35, -13.61, 0.14444, 6, 254.26, 90.64, 0.85556, 3, 36, -86.57, 14.88, 0.008, 35, -4.59, 12.1, 0.322, 6, 217.12, 111.17, 0.67, 3, 36, -0.69, 29.17, 0.3205, 35, 80.8, 29.13, 0.5865, 6, 167.09, 182.43, 0.093, 3, 36, -44.55, 21.87, 0.093, 35, 37.19, 20.43, 0.579, 6, 192.64, 146.03, 0.328], "hull": 14, "edges": [24, 28, 30, 12, 12, 14, 14, 16, 16, 18, 18, 12, 18, 20, 20, 30, 20, 22, 22, 24, 28, 32, 32, 30, 22, 32, 28, 0, 24, 26, 0, 26, 30, 8, 8, 10, 10, 12, 6, 8, 32, 6, 6, 4, 4, 2, 2, 0, 28, 4], "width": 293, "height": 114}}, "jj_9": {"jj_9": {"type": "mesh", "uvs": [0.63525, 0.17051, 0.82258, 0.48399, 0.99999, 0.8893, 0.96985, 0.97822, 0.71337, 0.98398, 0.47878, 0.8309, 0.33421, 0.92691, 0.1244, 0.88763, 0, 0.67706, 0, 0.22318, 0.08682, 0, 0.4598, 0, 0.35799, 0.23579, 0.58734, 0.37271, 0.77217, 0.62986, 0.23018, 0.49922], "triangles": [5, 12, 13, 5, 13, 4, 13, 11, 0, 12, 10, 11, 12, 11, 13, 15, 10, 12, 9, 10, 15, 8, 9, 15, 15, 12, 5, 7, 8, 15, 6, 15, 5, 7, 15, 6, 2, 3, 1, 3, 14, 1, 4, 14, 3, 4, 13, 14, 1, 14, 0, 14, 13, 0], "vertices": [4, 16, -87.85, 27.72, 0.09542, 15, 2.15, 44.59, 0.24271, 14, 111.98, 41.64, 0.31375, 6, 278.5, -241.84, 0.34812, 4, 16, 2.69, 32.69, 0.26347, 15, 91.87, 31.51, 0.39069, 14, 193.01, 0.94, 0.24521, 6, 250.73, -328.15, 0.10063, 4, 16, 99.05, 23.41, 0.38519, 15, 184.49, 3.31, 0.44954, 14, 272.01, -55.02, 0.14444, 6, 207.56, -414.81, 0.02083, 4, 16, 98.13, 4.63, 0.33847, 15, 179.86, -14.92, 0.45944, 14, 261.87, -70.86, 0.16708, 6, 190.35, -407.23, 0.035, 4, 16, 16.78, -58.3, 0.23292, 15, 87.65, -60.47, 0.39333, 14, 160, -85.02, 0.23542, 6, 160.72, -308.74, 0.13833, 4, 16, -73.14, -95.43, 0.0655, 15, -7.85, -79.03, 0.19733, 14, 63.52, -72.53, 0.27072, 6, 158.24, -211.48, 0.46644, 4, 16, -109.91, -142.87, 0.01667, 15, -53.29, -118.25, 0.08222, 14, 8.03, -95.41, 0.24481, 6, 127.11, -160.16, 0.6563, 3, 15, -131.83, -149.09, 0.02667, 14, -76.22, -99.93, 0.13444, 6, 109.71, -77.6, 0.83889, 1, 6, 128.53, -20.18, 1, 1, 6, 199.13, 0.37, 1, 1, 6, 243.57, -22.96, 1, 4, 16, -160.59, 7.18, 0.01563, 15, -73.22, 38.88, 0.085, 14, 38.66, 59.98, 0.22187, 6, 285.36, -166.56, 0.6775, 4, 16, -170.01, -47.93, 0.008, 15, -93.38, -13.27, 0.0785, 14, 3.09, 16.84, 0.2055, 6, 237.28, -138.04, 0.708, 4, 16, -83.35, -9.98, 0.0785, 15, -0.91, 6.74, 0.2335, 14, 97.14, 6.68, 0.32933, 6, 241.68, -232.54, 0.35867, 4, 16, 0.87, 1.65, 0.24017, 15, 83.94, 1.44, 0.41283, 14, 176.01, -25.1, 0.2405, 6, 222.39, -315.35, 0.1065, 4, 16, -185.03, -112.9, 0.01, 15, -120.98, -73.98, 0.052, 14, -42.25, -32.07, 0.18, 6, 181.98, -100.75, 0.758], "hull": 12, "edges": [24, 26, 26, 28, 28, 6, 6, 4, 24, 22, 22, 0, 0, 26, 0, 2, 2, 28, 2, 4, 24, 30, 30, 14, 14, 12, 12, 10, 10, 30, 26, 10, 6, 8, 10, 8, 8, 28, 20, 22, 24, 20, 20, 18, 16, 18, 16, 14, 30, 18], "width": 401, "height": 162}}, "jj_10": {"jj_10": {"type": "mesh", "uvs": [0.34689, 0.03339, 0.64471, 0.05033, 0.70005, 0.18023, 0.76594, 0.35907, 0.86346, 0.55109, 0.91617, 0.70922, 1, 0.94265, 1, 1, 0.62626, 1, 0.59463, 0.93701, 0, 1, 0, 0.76946, 0, 0.57932, 0, 0.37789, 0, 0.18211, 0, 0.06539, 0.45495, 0.14446, 0.50766, 0.35154, 0.56301, 0.5492, 0.58146, 0.7224], "triangles": [16, 0, 1, 14, 15, 0, 14, 0, 16, 16, 1, 2, 17, 16, 2, 17, 14, 16, 19, 18, 4, 17, 13, 14, 18, 3, 4, 17, 2, 3, 18, 17, 3, 12, 13, 17, 12, 17, 18, 19, 12, 18, 19, 4, 5, 19, 11, 12, 9, 19, 5, 11, 19, 9, 9, 5, 6, 10, 11, 9, 8, 9, 6, 8, 6, 7], "vertices": [2, 12, -50.34, -16.58, 0.12917, 8, 49.73, -24.94, 0.87083, 2, 12, -28.49, 46.99, 0.14444, 8, 58.73, -91.56, 0.85556, 3, 13, -109.12, 37.57, 0.01563, 12, 14.24, 48.87, 0.33021, 8, 21.38, -112.4, 0.65417, 3, 13, -51.24, 44.13, 0.10063, 12, 72.49, 49.2, 0.55875, 8, -30.53, -138.84, 0.34062, 3, 13, 11.77, 57.15, 0.34062, 12, 136.53, 55.4, 0.55875, 8, -84.99, -173.11, 0.10063, 3, 13, 62.77, 61.72, 0.65938, 12, 187.73, 54.49, 0.325, 8, -131.15, -195.26, 0.01563, 2, 13, 138.25, 69.81, 0.89938, 12, 263.64, 54.46, 0.10063, 2, 13, 156.13, 67.21, 0.97917, 12, 281.14, 49.96, 0.02083, 2, 13, 144.03, -16.01, 0.98667, 12, 260.21, -31.49, 0.01333, 2, 13, 123.37, -20.2, 0.90283, 12, 239.22, -33.44, 0.09717, 2, 13, 123.76, -155.45, 0.85889, 12, 225.14, -167.96, 0.14111, 3, 13, 51.9, -145.01, 0.65417, 12, 154.8, -149.89, 0.33021, 8, -193.41, 2.17, 0.01563, 3, 13, -7.37, -136.39, 0.34062, 12, 96.79, -134.98, 0.55875, 8, -134.88, 14.87, 0.10063, 3, 13, -70.16, -127.26, 0.10063, 12, 35.34, -119.19, 0.55875, 8, -72.87, 28.32, 0.34062, 3, 13, -131.19, -118.39, 0.01563, 12, -24.39, -103.84, 0.33021, 8, -12.6, 41.4, 0.65417, 2, 12, -60, -94.68, 0.14444, 8, 23.33, 49.2, 0.85556, 3, 13, -128.21, -15.39, 0.008, 12, -10.4, -1.74, 0.322, 8, 20.7, -56.12, 0.67, 3, 13, -61.95, -13.03, 0.093, 12, 55.73, -6.49, 0.579, 8, -40.54, -81.54, 0.328, 3, 13, 1.46, -9.67, 0.328, 12, 119.13, -9.93, 0.579, 8, -98.74, -106.92, 0.093, 3, 13, 56.05, -13.41, 0.672, 12, 173.01, -19.49, 0.32, 8, -151.18, -122.54, 0.008], "hull": 16, "edges": [28, 32, 32, 4, 4, 2, 2, 0, 28, 30, 0, 30, 0, 32, 32, 34, 34, 36, 36, 8, 8, 6, 6, 34, 26, 28, 34, 26, 24, 26, 24, 36, 6, 4, 36, 38, 38, 18, 18, 20, 20, 22, 22, 24, 22, 38, 38, 10, 14, 12, 10, 12, 12, 18, 14, 16, 18, 16, 8, 10], "width": 225, "height": 315}}, "jj_11": {"jj_11": {"type": "mesh", "uvs": [0.75094, 0.27504, 1, 0.60695, 0.80637, 0.91547, 0.6744, 1, 0.47644, 1, 0.17554, 0.88743, 0, 0.70511, 0, 0.53215, 0.29432, 0.21427, 0.56354, 0, 0.54242, 0.59292], "triangles": [4, 5, 10, 10, 8, 9, 5, 8, 10, 10, 9, 0, 8, 5, 7, 5, 6, 7, 10, 0, 2, 2, 0, 1, 3, 4, 10, 2, 3, 10], "vertices": [3, 16, 44.25, 18.52, 0.29595, 15, 129.8, 9.38, 0.12805, 14, 222.03, -32.02, 0.576, 3, 16, 90.07, 18.71, 0.5175, 15, 174.75, 0.49, 0.0825, 14, 261.88, -54.63, 0.4, 3, 16, 82.88, -18.89, 0.5365, 15, 160.25, -34.95, 0.0635, 14, 236.95, -83.69, 0.4, 3, 16, 71.67, -36.21, 0.57333, 15, 145.83, -49.7, 0.02667, 14, 218.61, -93.14, 0.4, 3, 16, 48.49, -53.81, 0.5275, 15, 119.62, -62.35, 0.0725, 14, 189.75, -96.89, 0.4, 3, 16, 7.61, -73.11, 0.28711, 15, 75.73, -73.17, 0.13689, 14, 144.68, -93.31, 0.576, 3, 16, -22.09, -76.66, 0.18954, 15, 45.91, -70.76, 0.48934, 14, 117.15, -81.63, 0.32111, 3, 16, -30.77, -65.23, 0.1224, 15, 39.67, -57.83, 0.57013, 14, 115.3, -67.39, 0.30747, 3, 16, -12.26, -18.05, 0.18856, 15, 67.16, -15.26, 0.49048, 14, 154.81, -35.66, 0.32095, 3, 16, 8.5, 20.04, 0.23322, 15, 95.07, 17.96, 0.43935, 14, 191.77, -12.93, 0.32743, 3, 16, 35.79, -21.03, 0.29235, 15, 113.67, -27.71, 0.13165, 14, 195.02, -62.13, 0.576], "hull": 10, "edges": [16, 20, 20, 4, 4, 2, 2, 0, 0, 18, 18, 16, 0, 20, 20, 10, 10, 12, 12, 14, 14, 16, 10, 8, 6, 8, 6, 4], "width": 147, "height": 83}}}}], "animations": {"animation1": {"slots": {"jj_biyan": {"color": [{"time": 2.8667, "color": "ffffff00"}, {"time": 3.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.2667, "color": "ffffffff"}, {"time": 3.4667, "color": "ffffff00"}], "attachment": [{"time": 2.8667, "name": "jj_biyan"}, {"time": 3.4667, "name": null}]}, "baiy4": {"color": [{"color": "ffffffba"}, {"time": 0.9, "color": "ffffff00", "curve": "stepped"}, {"time": 1, "color": "ffffff00"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6667, "color": "ffffffff"}, {"time": 4, "color": "ffffffba"}]}, "baiy7": {"color": [{"color": "ffffff00"}, {"time": 0.5, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3667, "color": "ffffffff"}, {"time": 1.9, "color": "ffffff00"}, {"time": 2, "color": "ffffff00"}, {"time": 2.5, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3667, "color": "ffffffff"}, {"time": 3.9, "color": "ffffff00"}]}, "baiy5": {"color": [{"time": 0.3333, "color": "ffffffff"}, {"time": 1.5667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6667, "color": "ffffff00"}, {"time": 2.6667, "color": "ffffffff"}]}, "yj_biyan": {"color": [{"time": 1.1333, "color": "ffffff00"}, {"time": 1.3, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4333, "color": "ffffffff"}, {"time": 1.5333, "color": "ffffff00"}], "attachment": [{"time": 1.1333, "name": "yj_biyan"}, {"time": 1.5333, "name": null}]}, "baiy8": {"color": [{"color": "ffffff00"}, {"time": 0.5, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1667, "color": "ffffffff"}, {"time": 1.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 2, "color": "ffffff00"}, {"time": 2.5, "color": "ffffffff", "curve": "stepped"}, {"time": 3.1667, "color": "ffffffff"}, {"time": 3.6667, "color": "ffffff00"}]}, "baiy1": {"color": [{"color": "ffffff00"}, {"time": 0.3333, "color": "ffffff2f"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff"}, {"time": 3.9, "color": "ffffff00"}]}, "baiy2": {"color": [{"color": "ffffff75"}, {"time": 0.5667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 3.3333, "color": "ffffffff"}, {"time": 4, "color": "ffffff75"}]}, "baiy3": {"color": [{"color": "ffffffa2", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffa2"}, {"time": 2.5667, "color": "ffffff00"}, {"time": 2.6667, "color": "ffffff00"}, {"time": 3.6667, "color": "ffffffa2"}]}, "baiy6": {"color": [{"color": "ffffffaa"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}, {"time": 3.2333, "color": "ffffff00"}, {"time": 3.3333, "color": "ffffff00"}, {"time": 4, "color": "ffffffaa"}]}}, "bones": {"yy": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": -68.18, "y": -25.86, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -68.18, "y": -25.86, "curve": 0.25, "c3": 0.75}, {"time": 4}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 1.694, "y": 1.694, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 1.694, "y": 1.694, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "jj_17": {"translate": [{"y": 0.29, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "y": 6.54, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "y": -2.18, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "y": 0.29, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "y": 6.54, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "y": -2.18, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "y": 0.29}]}, "jj_18": {"rotate": [{"angle": -0.51, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -0.97, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 0.67, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -0.51, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": -0.97, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 0.67, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -0.51}], "translate": [{"x": -0.01, "y": 0.41, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "x": -0.09, "y": 4.25, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "x": -0.01, "y": 0.41, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "x": -0.09, "y": 4.25, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "x": -0.01, "y": 0.41}]}, "jj_19": {"rotate": [{"angle": 1.46, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 2.69, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.62, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 1.46, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": 2.69, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -1.62, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 1.46}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 9.26, "y": -1.05, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 9.26, "y": -1.05, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "jj_3": {"rotate": [{"angle": 0.8, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 2.31, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -0.72, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 0.8, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "angle": 2.31, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -0.72, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 0.8}], "translate": [{"x": 3.38, "y": 0.98, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 11.9, "y": 3.46, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 3.38, "y": 0.98, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 11.9, "y": 3.46, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": 3.38, "y": 0.98}]}, "jj_13": {"rotate": [{"angle": 0.83, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 1.67, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 0.83, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 1.67, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 0.83}], "translate": [{"x": 1.96, "y": -1.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 6.91, "y": -4.45, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 1.96, "y": -1.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 6.91, "y": -4.45, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": 1.96, "y": -1.26}]}, "jj_12": {"rotate": [{"angle": -1.15, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -1.6, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -1.15, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -1.6, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -1.15}], "translate": [{"x": -1.44, "y": 4.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -2.88, "y": 9.25, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": -1.44, "y": 4.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": -2.88, "y": 9.25, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": -1.44, "y": 4.63}]}, "jj_1": {"rotate": [{"angle": -2.66, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -2.94, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": -2.66, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -2.94, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": -2.66}]}, "jj_10": {"rotate": [{"angle": 8.34, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 3.06, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 10.43, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 8.34, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": 3.06, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 10.43, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 8.34}], "translate": [{"x": 1.78, "y": 8.5}]}, "jj_11": {"rotate": [{"angle": 1.98, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": -4.75, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 2.69, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": 1.98, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": -4.75, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 2.69, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": 1.98}], "translate": [{"x": -6.5, "y": 5.12, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": -0.04, "y": -5.44, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -9.06, "y": 9.31, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -6.5, "y": 5.12, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "x": -0.04, "y": -5.44, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": -9.06, "y": 9.31, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": -6.5, "y": 5.12}]}, "jj_9": {"rotate": [{"angle": 2.8, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": -4.97, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 3.63, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": 2.8, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": -4.97, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 3.63, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": 2.8}]}, "jj_14": {"rotate": [{"angle": 8.07, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -2.64, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 8.07, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -2.64, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 8.07}]}, "jj_15": {"rotate": [{"angle": 19.61, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 22.54, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -7.84, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 19.61, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": 22.54, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -7.84, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": 19.61}]}, "jj_20": {"translate": [{"x": 19.56, "y": -10.74, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "x": 24.53, "y": -13.47, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 2, "x": 19.56, "y": -10.74, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 2.7333, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "x": 24.53, "y": -13.47, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 4, "x": 19.56, "y": -10.74}]}, "jj_21": {"rotate": [{"angle": -4.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -9.38, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": -4.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -9.38, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -4.69}], "translate": [{"x": 0.6, "y": 1.09, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 6.21, "y": 11.31, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "x": 0.6, "y": 1.09, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": 6.21, "y": 11.31, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "x": 0.6, "y": 1.09}]}, "jj_22": {"rotate": [{"angle": -10.28, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 8.8, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -17.85, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -10.28, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": 8.8, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -17.85, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -10.28}], "translate": [{"x": 0.7, "y": 1.42, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": 2.94, "y": 6.76, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -4.93, "y": -12.06, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.7, "y": 1.42, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "x": 2.94, "y": 6.76, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -4.93, "y": -12.06, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": 0.7, "y": 1.42}]}, "jj_23": {"rotate": [{"angle": -20.18, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": 3.76, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -22.74, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": -20.18, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": 3.76, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -22.74, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": -20.18}], "translate": [{"x": 1.31, "y": -4.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "x": 4.07, "y": 9.33, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -1.44, "y": -19.32, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 1.31, "y": -4.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "x": 4.07, "y": 9.33, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": -1.44, "y": -19.32, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 1.31, "y": -4.99}]}, "jj_24": {"rotate": [{"angle": -5.69, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": 2.21, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -6.53, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": -5.69, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": 2.21, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -6.53, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": -5.69}]}, "jj_25": {"rotate": [{"angle": -27.48, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 3.81, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -27.48, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 3.81, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -27.48}]}, "jj_16": {"rotate": [{"angle": -1.09, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 7.29, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -4.41, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -1.09, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": 7.29, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -4.41, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -1.09}]}, "jj_26": {"rotate": [{"angle": -9.62, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": 7.38, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -11.44, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": -9.62, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": 7.38, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -11.44, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": -9.62}]}, "jj_28": {"rotate": [{"angle": 1.44, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 7.29, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -4.41, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 1.44, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "angle": 7.29, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -4.41, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 1.44}]}, "jj_29": {"rotate": [{"angle": -6.1, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 7.38, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -11.44, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -6.1, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": 7.38, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -11.44, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -6.1}]}, "jj_32": {"rotate": [{"angle": 9.46, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": -6.39, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 11.16, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": 9.46, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": -6.39, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 11.16, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": 9.46}]}, "jj_33": {"rotate": [{"angle": 21.74, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -6.48, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 21.74, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -6.48, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 21.74}]}, "jj_34": {"rotate": [{"angle": 1.68, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 9.02, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 1.68, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 9.02, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 1.68}]}, "jj_35": {"rotate": [{"angle": 13.14, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -12.24, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 23.2, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 13.14, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": -12.24, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 23.2, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 13.14}]}, "jj_8": {"rotate": [{"angle": -2.78, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 3.05, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -5.1, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -2.78, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": 3.05, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -5.1, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -2.78}]}, "jj_30": {"rotate": [{"angle": -12.23, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": 4.25, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -13.99, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": -12.23, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": 4.25, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -13.99, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": -12.23}]}, "jj_38": {"rotate": [{}]}, "jj_37": {"rotate": [{}]}, "jj_41": {"rotate": [{"angle": -0.72, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 3.31, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -4.75, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": -0.72, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "angle": 3.31, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -4.75, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -0.72}]}, "jj_42": {"rotate": [{"angle": -6.66, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": -8.35, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 2, "angle": -6.66, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 2.7333, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "angle": -8.35, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 4, "angle": -6.66}], "translate": [{"x": 1.06, "y": -5.33, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 2.11, "y": -10.67, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 1.06, "y": -5.33, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 2.11, "y": -10.67, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 1.06, "y": -5.33}]}, "jj_5": {"rotate": [{"angle": 10.85, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.5, "angle": 21.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 10.85, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.5, "angle": 21.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 10.85}]}, "jj_43": {"rotate": [{"angle": 7.46, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 10.86, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.12, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 7.46, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": 10.86, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -1.12, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 7.46}]}, "jj_44": {"rotate": [{"angle": 6.26, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.5667, "angle": 23.71, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": -5.96, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 2, "angle": 6.26, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 2.5667, "angle": 23.71, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": -5.96, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 4, "angle": 6.26}]}, "jj_45": {"rotate": [{"angle": -2.44, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 1.88, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6667, "angle": -4.16, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -2.44, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": 1.88, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.6667, "angle": -4.16, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -2.44}]}, "jj_46": {"rotate": [{"angle": -9.9, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": 5.9, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.8333, "angle": -11.58, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": -9.9, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": 5.9, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.8333, "angle": -11.58, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": -9.9}]}, "jj_47": {"rotate": [{"angle": -23.57, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 10.54, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -23.57, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 10.54, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -23.57}]}, "jj_48": {"rotate": [{"angle": -0.64, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": -6.59, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": -0.64, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": -6.59, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": -0.64}]}, "jj_49": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -8.84, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -8.84, "curve": 0.25, "c3": 0.75}, {"time": 4}], "translate": [{"x": 0.01, "y": -2.25, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": 0.03, "y": -7.94, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 0.01, "y": -2.25, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "x": 0.03, "y": -7.94, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": 0.01, "y": -2.25}]}, "jj_50": {"rotate": [{"angle": 8.16, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 10.52, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -13.97, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 8.16, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": 10.52, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -13.97, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": 8.16}]}, "jj_51": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 7.78, "y": -1.78, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 7.78, "y": -1.78, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "jj_54": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 0.06, "y": -2.96, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 0.06, "y": -2.96, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "jj_57": {"rotate": [{"angle": 1.6, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": 2.72, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 2, "angle": 1.6, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 2.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": 2.72, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 4, "angle": 1.6}]}, "jj_58": {"translate": [{"x": -3.46, "y": 11.07, "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 0.7667, "x": 7.8, "y": -5.08, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": -5.71, "y": 14.29, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 2, "x": -3.46, "y": 11.07, "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 2.7667, "x": 7.8, "y": -5.08, "curve": 0.25, "c3": 0.75}, {"time": 3.7667, "x": -5.71, "y": 14.29, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 4, "x": -3.46, "y": 11.07}]}, "jj_59": {"rotate": [{"angle": 3.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -8.74, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 3.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -8.74, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 3.89}], "translate": [{"x": -10.68, "y": 41.9, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 17.19, "y": -9.31, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -10.68, "y": 41.9, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 17.19, "y": -9.31, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -10.68, "y": 41.9}]}, "jj_39": {"rotate": [{"angle": 0.77, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 2.72, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 0.77, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 2.72, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 0.77}]}, "jj_60": {"translate": [{"x": -0.07, "y": 4.72, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "x": 8.77, "y": -3.11, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -8.9, "y": 12.55, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": -0.07, "y": 4.72, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "x": 8.77, "y": -3.11, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": -8.9, "y": 12.55, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": -0.07, "y": 4.72}]}, "jj_61": {"rotate": [{"angle": 5.32, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -6.91, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 5.32, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -6.91, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 5.32}], "translate": [{"x": -9.11, "y": 25.96, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": 18.89, "y": -5.02, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -20.21, "y": 38.23, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -9.11, "y": 25.96, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "x": 18.89, "y": -5.02, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": -20.21, "y": 38.23, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": -9.11, "y": 25.96}]}, "jj_63": {"rotate": [{"angle": -0.32, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -3.27, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": -0.32, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -3.27, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": -0.32}]}, "jj_64": {"translate": [{"x": -1.25, "y": -1.5, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "x": 2.63, "y": 4.74, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "x": -7.91, "y": -12.24, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "x": -1.25, "y": -1.5, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.4, "x": 2.63, "y": 4.74, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "x": -7.91, "y": -12.24, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "x": -1.25, "y": -1.5}]}, "jj_65": {"rotate": [{"angle": -9.73, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 6.11, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -9.73, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "angle": 6.11, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -9.73}], "translate": [{"x": -11, "y": -16.55, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": 3.16, "y": 7.55, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -16.61, "y": -26.1, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -11, "y": -16.55, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "x": 3.16, "y": 7.55, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": -16.61, "y": -26.1, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": -11, "y": -16.55}]}, "jj_66": {"rotate": [{"angle": -0.47, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": -1.66, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -0.47, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": -1.66, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -0.47}], "translate": [{"curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3333, "x": -1.12, "y": 2.47, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1, "x": -3.94, "y": 8.71, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2.3333, "x": -1.12, "y": 2.47, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 3, "x": -3.94, "y": 8.71, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "jj_67": {"rotate": [{"angle": -2.94, "curve": 0.351, "c2": 0.39, "c3": 0.701, "c4": 0.78}, {"time": 0.3333, "angle": -0.57, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -5.95, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": -2.94, "curve": 0.351, "c2": 0.39, "c3": 0.701, "c4": 0.78}, {"time": 2.3333, "angle": -0.57, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -5.95, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -2.94}], "translate": [{"x": 0.77, "y": 5.71, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "x": 1.4, "y": 7.55, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.3333, "x": 0.77, "y": 5.71, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 1.1667, "x": -5.08, "y": -11.47, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "x": 0.77, "y": 5.71, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "x": 1.4, "y": 7.55, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2.3333, "x": 0.77, "y": 5.71, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 3.1667, "x": -5.08, "y": -11.47, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "x": 0.77, "y": 5.71}]}, "jj_68": {"rotate": [{"angle": -7.04, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.3333, "angle": -0.79, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6667, "angle": 3.32, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -11.17, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -7.04, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 2.3333, "angle": -0.79, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.6667, "angle": 3.32, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -11.17, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -7.04}], "translate": [{"x": 0.79, "y": 0.07, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": 2.02, "y": 6.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": -2.32, "y": -14.99, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.79, "y": 0.07, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "x": 2.02, "y": 6.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "x": -2.32, "y": -14.99, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": 0.79, "y": 0.07}]}, "yj_18": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "y": -9.75, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "y": -9.75, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "yj_19": {"rotate": [{"angle": 0.22, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -2.22, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 1.19, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 0.22, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": -2.22, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 1.19, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 0.22}], "translate": [{"x": -0.05, "y": -0.2, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": -0.54, "y": -2.03, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "x": -0.05, "y": -0.2, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": -0.54, "y": -2.03, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "x": -0.05, "y": -0.2}]}, "yj_20": {"rotate": [{"angle": -0.61, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 2.99, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -2.04, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -0.61, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": 2.99, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -2.04, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -0.61}], "translate": [{"x": -2.97, "y": -0.14, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -10.47, "y": -0.5, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -2.97, "y": -0.14, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -10.47, "y": -0.5, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": -2.97, "y": -0.14}]}, "yj_21": {"translate": [{"x": -0.46, "y": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "x": 2.73, "y": 2.25, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.5, "x": -3.64, "y": -3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": -0.46, "y": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "x": 2.73, "y": 2.25, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.5, "x": -3.64, "y": -3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": -0.46, "y": -0.38}]}, "yj_biyan": {"rotate": [{"angle": -6.42, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -6.42, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -6.42}], "translate": [{"x": -0.04, "y": -3.01, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -0.06, "y": -4.21, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -0.04, "y": -3.01, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": -0.06, "y": -4.21, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": -0.04, "y": -3.01}]}, "yj_24": {"rotate": [{"angle": -2.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -5.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": -2.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "angle": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -5.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -2.34}]}, "yj_17": {"rotate": [{"angle": -1.58, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 0.7333, "angle": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": -2.13, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 2, "angle": -1.58, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 2.7333, "angle": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "angle": -2.13, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 4, "angle": -1.58}]}, "yj_9": {"rotate": [{"angle": -8.04, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 1.79, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -8.04, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 1.79, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -8.04}]}, "yj_10": {"rotate": [{"angle": -7.58, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": -8.56, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 1.66, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": -7.58, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": -8.56, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 1.66, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": -7.58}]}, "yj_11": {"rotate": [{"angle": -3.46, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -6.27, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 3.63, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -3.46, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": -6.27, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 3.63, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -3.46}]}, "yj_16": {"rotate": [{"angle": -1.31, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 1.53, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -2.44, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -1.31, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": 1.53, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -2.44, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -1.31}]}, "yj_23": {"rotate": [{"angle": -5.87, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": 2.05, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -6.71, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": -5.87, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": 2.05, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -6.71, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": -5.87}], "translate": [{"x": -0.5, "y": -2.3, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.6, "x": -6.04, "y": 7.4, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "x": 2.73, "y": -7.95, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "x": -0.5, "y": -2.3, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.6, "x": -6.04, "y": 7.4, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "x": 2.73, "y": -7.95, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4, "x": -0.5, "y": -2.3}]}, "yj_12": {"rotate": [{"angle": -1.73, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": -2.01, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.1667, "angle": 0.89, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": -1.73, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": -2.01, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 3.1667, "angle": 0.89, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": -1.73}], "translate": [{"x": 8.14, "y": -3.69, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 8.14, "y": -3.69, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 8.14, "y": -3.69}]}, "yj_13": {"rotate": [{"angle": -1.42, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -2.84, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.3333, "angle": 2.19, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -1.42, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": -2.84, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 3.3333, "angle": 2.19, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -1.42}], "translate": [{"x": -2.89, "y": -9, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "x": -3.2, "y": -9.96, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "x": -2.89, "y": -9, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "x": -3.2, "y": -9.96, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 3.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "x": -2.89, "y": -9}]}, "yj_14": {"rotate": [{"angle": -0.56, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": -5.27, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.5, "angle": 4.15, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": -0.56, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "angle": -5.27, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 3.5, "angle": 4.15, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -0.56}], "translate": [{"x": -2.26, "y": -4.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": -3.16, "y": -6.36, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -2.26, "y": -4.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "x": -3.16, "y": -6.36, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 3.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": -2.26, "y": -4.55}]}, "yj_15": {"rotate": [{"angle": 1.6, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -10.93, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.6667, "angle": 6.57, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 1.6, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": -10.93, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 3.6667, "angle": 6.57, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 1.6}], "translate": [{"x": -2.1, "y": -2.12, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "x": -4.21, "y": -4.24, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": -2.1, "y": -2.12, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "x": -4.21, "y": -4.24, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 3.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": -2.1, "y": -2.12}]}, "yj_25": {"rotate": [{"angle": 13.84, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": -18.94, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.8333, "angle": 17.34, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": 13.84, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": -18.94, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 3.8333, "angle": 17.34, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": 13.84}], "translate": [{"x": -1.99, "y": -2.69, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": -7.02, "y": -9.46, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -1.99, "y": -2.69, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "x": -7.02, "y": -9.46, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 3.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": -1.99, "y": -2.69}]}, "yj_biyan2": {"rotate": [{"angle": 0.07, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -2.01, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.6667, "angle": 0.89, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 0.07, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": -2.01, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 3.6667, "angle": 0.89, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 0.07}]}, "yj_biyan3": {"rotate": [{"angle": 1.7, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": -2.84, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.8333, "angle": 2.19, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": 1.7, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": -2.84, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 3.8333, "angle": 2.19, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": 1.7}], "translate": [{"x": -0.84, "y": -2.13, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": -2.97, "y": -7.5, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -0.84, "y": -2.13, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "x": -2.97, "y": -7.5, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 3.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": -0.84, "y": -2.13}]}, "yj_biyan4": {"rotate": [{"angle": 4.15, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -5.27, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 4.15, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -5.27, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 4.15}], "translate": [{"x": -0.2, "y": -0.33, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "x": -2.03, "y": -3.45, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.8333, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "x": -0.2, "y": -0.33, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "x": -2.03, "y": -3.45, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 3.8333, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "x": -0.2, "y": -0.33}]}, "yj_biyan5": {"rotate": [{"angle": 5.49, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 6.57, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -4.58, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 5.49, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": 6.57, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -4.58, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": 5.49}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": -6.44, "y": -4.47, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -6.44, "y": -4.47, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "yj_28": {"rotate": [{"angle": 1.12, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 2.24, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 1.12, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "angle": 2.24, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 1.12}]}, "yj_8": {"rotate": [{"angle": -3.95, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -5.51, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -3.95, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -5.51, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -3.95}], "translate": [{"x": 1.61, "y": 1.43, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 5.68, "y": 5.03, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 1.61, "y": 1.43, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 5.68, "y": 5.03, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": 1.61, "y": 1.43}]}, "yj_6": {"rotate": [{"angle": 6.29, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 6.96, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": 6.29, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 6.96, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": 6.29}]}, "yj_1": {"rotate": [{"angle": -16.99, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.2333, "angle": -26.39, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 30.09, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 2, "angle": -16.99, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 2.2333, "angle": -26.39, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "angle": 30.09, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 4, "angle": -16.99}]}, "yj_29": {"rotate": [{"angle": -1.17, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -2.63, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 2.53, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -1.17, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": -2.63, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 2.53, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -1.17}]}, "yj_30": {"rotate": [{"angle": 2.85, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -5.54, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 6.17, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 2.85, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": -5.54, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 6.17, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 2.85}], "translate": [{"x": -0.91, "y": -8.12, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": -0.37, "y": -15.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -2.25, "y": 10, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -0.91, "y": -8.12, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "x": -0.37, "y": -15.3, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -2.25, "y": 10, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": -0.91, "y": -8.12}], "scale": [{"y": 0.735, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "y": 0.735, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4, "y": 0.735}]}, "yj_31": {"rotate": [{"angle": 6.77, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -11.35, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 6.77, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -11.35, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 6.77}], "translate": [{"x": -2.22, "y": 0.47, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": -7.84, "y": 1.64, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -2.22, "y": 0.47, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "x": -7.84, "y": 1.64, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": -2.22, "y": 0.47}]}, "yj_32": {"rotate": [{"angle": 6.49, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 7.69, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -4.76, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 6.49, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": 7.69, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -4.76, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": 6.49}]}, "yj_33": {"rotate": [{"angle": 3.19, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "angle": 12.85, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -13.4, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "angle": 3.19, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.4, "angle": 12.85, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "angle": -13.4, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "angle": 3.19}]}, "yj_27": {"translate": [{"x": -10.27, "y": -3.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -20.54, "y": -7.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": -10.27, "y": -3.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": -20.54, "y": -7.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": -10.27, "y": -3.64}]}, "yj_34": {"translate": [{"x": -13.74, "y": -5.29, "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": -16.48, "y": -6.35, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 2, "x": -13.74, "y": -5.29, "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 2.7667, "curve": 0.25, "c3": 0.75}, {"time": 3.7667, "x": -16.48, "y": -6.35, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 4, "x": -13.74, "y": -5.29}]}, "yj_36": {"rotate": [{"angle": 0.02}]}, "yj_37": {"rotate": [{"angle": -0.03}]}, "yj_39": {"rotate": [{}]}, "yj_40": {"rotate": [{}]}, "yj_42": {"rotate": [{"angle": 2.99, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 3.59, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -2.65, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 2.99, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": 3.59, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -2.65, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": 2.99}], "translate": [{"x": 5.81, "y": 2.69, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 5.81, "y": 2.69, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 5.81, "y": 2.69}]}, "yj_43": {"rotate": [{"angle": 4.67, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 7.52, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.51, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 4.67, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": 7.52, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -2.51, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 4.67}], "translate": [{"x": -2.51, "y": 7.47, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "x": -2.78, "y": 8.26, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "x": -2.51, "y": 7.47, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "x": -2.78, "y": 8.26, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "x": -2.51, "y": 7.47}]}, "yj_44": {"rotate": [{"angle": 1.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 11.56, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -8.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 1.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "angle": 11.56, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -8.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 1.42}], "translate": [{"x": -1.33, "y": 3.71, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": -1.86, "y": 5.18, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -1.33, "y": 3.71, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "x": -1.86, "y": 5.18, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": -1.33, "y": 3.71}]}, "yj_45": {"rotate": [{"angle": 2.52, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 3.51, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 2.52, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": 3.51, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 2.52}]}, "yj_46": {"rotate": [{"angle": 0.61, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 1.22, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 0.61, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "angle": 1.22, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 0.61}], "translate": [{"x": -1.2, "y": 1.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": -2.1, "y": 2.69, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.08, "y": -1.32, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -1.2, "y": 1.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "x": -2.1, "y": 2.69, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 1.08, "y": -1.32, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": -1.2, "y": 1.55}]}, "yj_47": {"rotate": [{"angle": -1.47, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 2.81, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -3.16, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -1.47, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": 2.81, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -3.16, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -1.47}], "translate": [{"x": -0.11, "y": 1.57, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "x": -1.93, "y": 5.04, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 1.71, "y": -1.91, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": -0.11, "y": 1.57, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "x": -1.93, "y": 5.04, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 1.71, "y": -1.91, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": -0.11, "y": 1.57}]}, "yj_48": {"rotate": [{"angle": -0.45, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": 7.64, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -1.32, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": -0.45, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": 7.64, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -1.32, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": -0.45}], "translate": [{"x": -0.13, "y": 0.46, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": -2.38, "y": 6.05, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.76, "y": -1.75, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -0.13, "y": 0.46, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "x": -2.38, "y": 6.05, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 0.76, "y": -1.75, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": -0.13, "y": 0.46}]}, "yj_49": {"rotate": [{"angle": -5.76, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 7.9, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -5.76, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 7.9, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -5.76}]}, "yj_50": {"rotate": [{"angle": -10.07, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": -13.52, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 22.19, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": -10.07, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": -13.52, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 22.19, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": -10.07}]}, "yj_51": {"rotate": [{"angle": -4.44, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": -5.76, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 7.9, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": -4.44, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": -5.76, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 7.9, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": -4.44}]}, "yj_52": {"rotate": [{"angle": -0.38, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "angle": -13.52, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 22.19, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "angle": -0.38, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.4, "angle": -13.52, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "angle": 22.19, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "angle": -0.38}]}, "yj_53": {"rotate": [{"angle": -1.88, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -5.76, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.9, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -1.88, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": -5.76, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 7.9, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -1.88}]}, "yj_54": {"rotate": [{"angle": 4.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": -13.52, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 22.19, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 4.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "angle": -13.52, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 22.19, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 4.34}]}, "yj_55": {"rotate": [{"angle": -4.81, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.8667, "angle": 7.9, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -5.76, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 2, "angle": -4.81, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 2.8667, "angle": 7.9, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "angle": -5.76, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 4, "angle": -4.81}]}, "yj_56": {"rotate": [{"angle": -7.58, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.2333, "angle": -13.52, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 22.19, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 2, "angle": -7.58, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 2.2333, "angle": -13.52, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "angle": 22.19, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 4, "angle": -7.58}]}, "yj_57": {"rotate": [{"angle": 0.83, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 1.67, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 0.83, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "angle": 1.67, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 0.83}], "translate": [{"x": 2.53, "y": 0.79, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": 4.81, "y": 2.11, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -3.22, "y": -2.53, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 2.53, "y": 0.79, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "x": 4.81, "y": 2.11, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -3.22, "y": -2.53, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": 2.53, "y": 0.79}]}, "yj_58": {"rotate": [{"angle": 1.53, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 5.38, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 1.53, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": 5.38, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 1.53}], "translate": [{"x": 0.35, "y": 1.24, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "x": -2.6, "y": 6.56, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 3.31, "y": -4.07, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 0.35, "y": 1.24, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "x": -2.6, "y": 6.56, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 3.31, "y": -4.07, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 0.35, "y": 1.24}]}, "yj_59": {"rotate": [{"angle": 0.7, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": 7.24, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": 0.7, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": 7.24, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": 0.7}], "translate": [{"x": 3.57, "y": 0.47, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": -0.02, "y": 9.91, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 4.99, "y": -3.27, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 3.57, "y": 0.47, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "x": -0.02, "y": 9.91, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 4.99, "y": -3.27, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": 3.57, "y": 0.47}]}, "yj_60": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.95, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 8.95, "curve": 0.25, "c3": 0.75}, {"time": 4}], "translate": [{"x": 3.53, "y": -3.04, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "x": -6.34, "y": 8.21, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 4.58, "y": -4.24, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "x": 3.53, "y": -3.04, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "x": -6.34, "y": 8.21, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 4.58, "y": -4.24, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "x": 3.53, "y": -3.04}]}, "yj_61": {"rotate": [{"angle": -4.47, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -6.24, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -4.47, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": -6.24, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -4.47}]}, "yj_62": {"rotate": [{"angle": -1.49, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": -2.97, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": -1.49, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "angle": -2.97, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -1.49}], "translate": [{"x": 0.23, "y": -5.22, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": 0.32, "y": -7.29, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.23, "y": -5.22, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "x": 0.32, "y": -7.29, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": 0.23, "y": -5.22}]}, "yj_63": {"rotate": [{"angle": -1.07, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -6.73, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 1.17, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -1.07, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": -6.73, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 1.17, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -1.07}], "translate": [{"x": 0.71, "y": -1.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "x": -2.1, "y": -6.64, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 3.52, "y": 3.13, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 0.71, "y": -1.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "x": -2.1, "y": -6.64, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 3.52, "y": 3.13, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 0.71, "y": -1.75}]}, "yj_41": {"rotate": [{"angle": -0.6, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": -6.24, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": -0.6, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": -6.24, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": -0.6}]}, "yj_64": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -2.97, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -2.97, "curve": 0.25, "c3": 0.75}, {"time": 4}], "translate": [{"x": -0.38, "y": -0.59, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "x": -3.91, "y": -6.16, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "x": -0.38, "y": -0.59, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "x": -3.91, "y": -6.16, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "x": -0.38, "y": -0.59}]}, "yj_65": {"rotate": [{"angle": 0.41, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 1.17, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -6.73, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 0.41, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": 1.17, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -6.73, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": 0.41}], "translate": [{"x": 4.4, "y": 1.68, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -4.32, "y": -5.46, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 4.4, "y": 1.68, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -4.32, "y": -5.46, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 4.4, "y": 1.68}]}, "yj_66": {"rotate": [{"angle": 1.9, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 6.25, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -9.07, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 1.9, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": 6.25, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -9.07, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 1.9}]}, "yj_67": {"rotate": [{"angle": -5.22, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 12.37, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -22.81, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": -5.22, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "angle": 12.37, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -22.81, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -5.22}]}, "yj_68": {"rotate": [{"angle": -1.41, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 6.25, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -9.07, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": -1.41, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "angle": 6.25, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -9.07, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -1.41}]}, "yj_69": {"rotate": [{"angle": -12.83, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 12.37, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -22.81, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -12.83, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": 12.37, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -22.81, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -12.83}]}, "yj_70": {"rotate": [{"angle": -4.73, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 6.25, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -9.07, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -4.73, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": 6.25, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -9.07, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -4.73}]}, "yj_71": {"rotate": [{"angle": -19.42, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": 12.37, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -22.81, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": -19.42, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": 12.37, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -22.81, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": -19.42}]}, "yj_72": {"rotate": [{"angle": 5.91, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": -12.99, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 7.93, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": 5.91, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": -12.99, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 7.93, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": 5.91}]}, "yj_73": {"rotate": [{"angle": 16.03, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -21.1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 16.03, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -21.1, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 16.03}]}, "yj_74": {"rotate": [{"angle": 7.93, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -12.99, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 7.93, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -12.99, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 7.93}]}, "yj_75": {"rotate": [{"angle": 12.45, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 16.03, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -21.1, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 12.45, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": 16.03, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -21.1, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": 12.45}]}, "yj_76": {"rotate": [{"angle": -0.69, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.5667, "angle": -12.99, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": 7.93, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 2, "angle": -0.69, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 2.5667, "angle": -12.99, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": 7.93, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 4, "angle": -0.69}]}, "yj_77": {"rotate": [{"angle": 9.85, "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 0.7667, "angle": -21.1, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": 16.03, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 2, "angle": 9.85, "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 2.7667, "angle": -21.1, "curve": 0.25, "c3": 0.75}, {"time": 3.7667, "angle": 16.03, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 4, "angle": 9.85}]}, "yj_2": {"rotate": [{"angle": -0.51, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -1.71, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -0.51, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -1.71, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -0.51}], "translate": [{"x": 0.23, "y": -2.39, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -4.6, "y": -12.95, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.23, "y": -2.39, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -4.6, "y": -12.95, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": 0.23, "y": -2.39}]}}, "deform": {"default": {"wu2": {"wu2": [{"vertices": [0.048, 4.15994, 0.04303, 3.72907, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.5052, 2.12548, -2.49664, 3.44866, -2.7198, 7.01144, -1.93964, 6.50926, -0.52745, 5.87029, -0.12194, 5.86925, -1.36365, 4.54404, -0.60729, 4.38177, 0.04566, 3.95671, -2.72998, 5.57146, -1.62774, 6.67496, -1.17819, 5.03376, -0.00953, 4.05011, -0.25534, 4.12625, 0.42661, 2.89655, 0.23137, 4.46674, -0.49158, 4.36941, -0.39935, 2.5695, -1.20613, 2.45802, 0.01695, 1.46902, -0.29971, 2.83552, -0.23356, 2.3383, 0.13562, 4.05968, -3.28584, 3.73398, -1.65745, 4.45986, -1.82643, 4.84779, -1.13955, 4.07798, -1.23378, 3.06604, -3.23283, 2.17366, -4.23721, 5.39457, -1.08083, 6.70287, 0.38044, 7.03613, 0.68002, 4.59788, 0.42043, 4.61759], "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "vertices": [0.16914, 14.6584, 0.15162, 13.14016, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -12.3513, 7.48958, -8.79743, 12.15206, -9.58379, 24.70625, -6.83473, 22.93673, -1.85856, 20.68519, -0.42968, 20.6815, -4.8051, 16.01187, -2.13991, 15.44009, 0.16088, 13.94229, -9.61964, 19.6322, -5.73567, 23.52059, -4.1516, 17.73749, -0.03359, 14.27141, -0.89976, 14.53968, 1.50326, 10.20661, 0.81528, 15.73947, -1.7322, 15.39651, -1.40718, 9.05418, -4.25006, 8.66133, 0.05971, 5.17641, -1.05607, 9.99155, -0.82298, 8.23948, 0.47789, 14.30514, -11.57835, 13.15744, -5.84038, 15.71524, -6.43581, 17.0822, -4.01546, 14.36962, -4.34746, 10.80383, -11.39154, 7.65935, -14.93069, 19.00888, -3.80854, 23.61895, 1.34057, 24.79325, 2.39618, 16.20158, 1.48146, 16.27104], "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "vertices": [0.16914, 14.6584, 0.15162, 13.14016, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -12.3513, 7.48958, -8.79743, 12.15206, -9.58379, 24.70625, -6.83473, 22.93673, -1.85856, 20.68519, -0.42968, 20.6815, -4.8051, 16.01187, -2.13991, 15.44009, 0.16088, 13.94229, -9.61964, 19.6322, -5.73567, 23.52059, -4.1516, 17.73749, -0.03359, 14.27141, -0.89976, 14.53968, 1.50326, 10.20661, 0.81528, 15.73947, -1.7322, 15.39651, -1.40718, 9.05418, -4.25006, 8.66133, 0.05971, 5.17641, -1.05607, 9.99155, -0.82298, 8.23948, 0.47789, 14.30514, -11.57835, 13.15744, -5.84038, 15.71524, -6.43581, 17.0822, -4.01546, 14.36962, -4.34746, 10.80383, -11.39154, 7.65935, -14.93069, 19.00888, -3.80854, 23.61895, 1.34057, 24.79325, 2.39618, 16.20158, 1.48146, 16.27104], "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "vertices": [0.048, 4.15994, 0.04303, 3.72907, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.5052, 2.12548, -2.49664, 3.44866, -2.7198, 7.01144, -1.93964, 6.50926, -0.52745, 5.87029, -0.12194, 5.86925, -1.36365, 4.54404, -0.60729, 4.38177, 0.04566, 3.95671, -2.72998, 5.57146, -1.62774, 6.67496, -1.17819, 5.03376, -0.00953, 4.05011, -0.25534, 4.12625, 0.42661, 2.89655, 0.23137, 4.46674, -0.49158, 4.36941, -0.39935, 2.5695, -1.20613, 2.45802, 0.01695, 1.46902, -0.29971, 2.83552, -0.23356, 2.3383, 0.13562, 4.05968, -3.28584, 3.73398, -1.65745, 4.45986, -1.82643, 4.84779, -1.13955, 4.07798, -1.23378, 3.06604, -3.23283, 2.17366, -4.23721, 5.39457, -1.08083, 6.70287, 0.38044, 7.03613, 0.68002, 4.59788, 0.42043, 4.61759]}]}, "baiy4": {"baiy4": [{"vertices": [-1.25167, -4.04704, 19.29988, -10.09164, 65.23866, -7.67382, 114.80414, 4.41528, 160.74297, -40.31454, 180.08566, -49.98593, 160.74297, -40.31454, 108.7596, -17.3451, 51.94061, -25.80753, 7.21068, -19.76292, -7.29622, -17.3451, -25.42997, 4.41548, -9.71409, 4.41548, -9.71409, 4.41548, -10.923, -10.09154, 64.02977, -16.13614, 12.04632, -17.3451, 116.01312, -10.09154], "curve": 0.377, "c2": 0.61, "c3": 0.719}, {"time": 0.9, "vertices": [10.01001, -10.01013, 34.31998, -17.16016, 88.65994, -14.30017, 147.28984, -0.00024, 201.62985, -52.91016, 224.5099, -64.35022, 201.62985, -52.91016, 140.13988, -25.74011, 72.92996, -35.75012, 20.01993, -28.6001, 2.86004, -25.74011, -18.58997, 0, 0, 0, 0, 0, -1.42999, -17.16003, 87.22997, -24.31006, 25.7399, -25.74011, 148.71992, -17.16003], "curve": 0.25, "c3": 0.75}, {"time": 1, "vertices": [-62.83203, 28.55994, -62.83203, 28.55994, -62.83203, 28.55994, -62.83203, 28.55994, -62.83203, 28.55994, -62.83203, 28.55994, -62.83203, 28.55994, -62.83203, 28.55994, -62.83203, 28.55994, -62.83203, 28.55994, -62.83203, 28.55994, -62.83203, 28.55994, -62.83203, 28.55994, -62.83203, 28.55994, -62.83203, 28.55994, -62.83203, 28.55994, -62.83203, 28.55994, -62.83203, 28.55994], "curve": 0.261, "c2": 0.08, "c3": 0.68, "c4": 0.71}, {"time": 4, "vertices": [-1.25167, -4.04704, 19.29988, -10.09164, 65.23866, -7.67382, 114.80414, 4.41528, 160.74297, -40.31454, 180.08566, -49.98593, 160.74297, -40.31454, 108.7596, -17.3451, 51.94061, -25.80753, 7.21068, -19.76292, -7.29622, -17.3451, -25.42997, 4.41548, -9.71409, 4.41548, -9.71409, 4.41548, -10.923, -10.09154, 64.02977, -16.13614, 12.04632, -17.3451, 116.01312, -10.09154]}]}, "baiy7": {"baiy7": [{"vertices": [-16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704], "curve": 0.25, "c3": 0.75}, {"time": 1.9, "vertices": [46.19997, 38.84998, 46.19997, 38.84998, 46.19997, 38.84998, 46.19997, 38.84998, 46.19997, 38.84998, 46.19997, 52.5, 46.19997, 38.84998, 52.50002, 48.29993, 46.19997, 38.84998, 46.19997, 38.84998, 46.19997, 38.84998, 46.19997, 38.84998, 25.19995, 14.69998, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.20001, 36.75, 46.19997, 38.84998, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48.3, 39.90002, 44.10002, 46.19989, 15.75, 29.39996, 28.35004, 32.55005, 37.79997, 28.34991, 41.99998, 27.29999, 48.29991, 24.14996], "curve": 0.25, "c3": 0.75}, {"time": 2, "vertices": [-16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704], "curve": 0.25, "c3": 0.75}, {"time": 3.9, "vertices": [46.19997, 38.84998, 46.19997, 38.84998, 46.19997, 38.84998, 46.19997, 38.84998, 46.19997, 38.84998, 46.19997, 52.5, 46.19997, 38.84998, 52.50002, 48.29993, 46.19997, 38.84998, 46.19997, 38.84998, 46.19997, 38.84998, 46.19997, 38.84998, 25.19995, 14.69998, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.20001, 36.75, 46.19997, 38.84998, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48.3, 39.90002, 44.10002, 46.19989, 15.75, 29.39996, 28.35004, 32.55005, 37.79997, 28.34991, 41.99998, 27.29999, 48.29991, 24.14996], "curve": 0.25, "c3": 0.75}, {"time": 4, "vertices": [-16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704]}]}, "wu3": {"wu3": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "vertices": [-7.10076, -5.13318, -4.22289, -5.4574, 3.13598, -11.80554, 3.59238, -10.30164, 11.96704, -9.4259, 13.37873, -20.35132, -0.17139, -15.19312, -2.223, -6.56274, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.86335, -0.66248, 0.39123, -0.2998, 0.93003, -0.71387, 0.88069, -0.67542, 0.65993, -0.5061, -0.33271, -0.51685, -0.22075, -0.34302, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -32.5699, 4.95935, -32.79245, 11.76819, -28.70555, 0.3092, -34.85657, -2.29663, -14.79303, -6.0636, 4.13268, -8.75391, 1.97865, -1.60461, 8.08647, -6.26917, -3.3614, -4.95081, -7.90342, 1.38196, -4.06274, -3.5979, -1.62264, -3.04053, -1.50698, -2.34863, -2.66518, -0.83557, -0.69055, 0.09424, 0, 0, -8.61418, -0.26331, -7.00995, -3.073, -6.26721, -4.68408, -2.41994, -6.95239, -1.01698, -4.57556, 2.11131, -1.9325], "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "vertices": [-7.10076, -5.13318, -4.22289, -5.4574, 3.13598, -11.80554, 3.59238, -10.30164, 11.96704, -9.4259, 13.37873, -20.35132, -0.17139, -15.19312, -2.223, -6.56274, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.86335, -0.66248, 0.39123, -0.2998, 0.93003, -0.71387, 0.88069, -0.67542, 0.65993, -0.5061, -0.33271, -0.51685, -0.22075, -0.34302, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -32.5699, 4.95935, -32.79245, 11.76819, -28.70555, 0.3092, -34.85657, -2.29663, -14.79303, -6.0636, 4.13268, -8.75391, 1.97865, -1.60461, 8.08647, -6.26917, -3.3614, -4.95081, -7.90342, 1.38196, -4.06274, -3.5979, -1.62264, -3.04053, -1.50698, -2.34863, -2.66518, -0.83557, -0.69055, 0.09424, 0, 0, -8.61418, -0.26331, -7.00995, -3.073, -6.26721, -4.68408, -2.41994, -6.95239, -1.01698, -4.57556, 2.11131, -1.9325], "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "baiy5": {"baiy5": [{"vertices": [-22.29343, 23.53328, -4.56009, 13.26667, 20.99529, 28.38483, 56.05455, 5.34048, 100.49918, -4.85974, 130.7213, 2.91015, 177.57281, -37.89616, 220.37329, -26.86664, 235.62436, -23.7113, 169.68431, -44.20692, 127.56589, -18.65154, 99.97331, -20.63667, 47.11427, -12.01403, 17.83987, 22.59999, 9.63913, 3.8005, -19.66389, 16.69661, -28.82672, 28.19998, -28.82672, 28.19998, -28.82672, 28.19998, -14.40497, 15.64486, 1.7507, 10.11126, 23.09884, 27.333, 48.69198, -7.28102, 102.07689, -9.06691, 131.24718, -9.71129, 175.46918, -38.42208]}, {"time": 1.5667, "vertices": [10.91992, -7.80005, 40.55994, -24.95996, 83.27393, 0.30896, 141.87297, -38.20801, 216.15901, -55.25696, 266.6731, -42.27014, 344.98206, -110.47498, 416.52002, -92.03992, 442.01105, -86.76599, 331.797, -121.02295, 261.39905, -78.30896, 215.28003, -81.62695, 126.92993, -67.21484, 77.99988, -9.35999, 64.29291, -40.78198, 15.315, -19.22705, 0, 0, 0, 0, 0, 0, 24.10492, -20.98499, 51.10797, -30.23401, 86.78986, -1.4491, 129.56696, -59.30396, 218.79604, -62.28894, 267.55206, -63.36597, 341.46597, -111.354], "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 1.6667, "vertices": [-71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995]}, {"time": 4, "vertices": [-22.29343, 23.53328, -4.56009, 13.26667, 20.99529, 28.38483, 56.05455, 5.34048, 100.49918, -4.85974, 130.7213, 2.91015, 177.57281, -37.89616, 220.37329, -26.86664, 235.62436, -23.7113, 169.68431, -44.20692, 127.56589, -18.65154, 99.97331, -20.63667, 47.11427, -12.01403, 17.83987, 22.59999, 9.63913, 3.8005, -19.66389, 16.69661, -28.82672, 28.19998, -28.82672, 28.19998, -28.82672, 28.19998, -14.40497, 15.64486, 1.7507, 10.11126, 23.09884, 27.333, 48.69198, -7.28102, 102.07689, -9.06691, 131.24718, -9.71129, 175.46918, -38.42208]}]}, "baiy8": {"baiy8": [{"vertices": [32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001], "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "vertices": [-23.57898, 28.54291, -19.85593, 28.54297, -9.92792, 21.09698, -8.68701, 17.3739, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -13.651, 9.92801, -29.784, 18.61499, -44.67603, 50.88098, -45.91699, 52.12201, -18.61499, 63.29102, -12.41003, 11.16907, -9.92798, 18.61511, -21.09702, 11.16895, -35.98901, 21.09698, -27.30203, 26.06104, -26.06094, 33.50696, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -39.71204, 22.33801, -37.22997, 11.16895, -32.26602, 19.85596, -34.74799, 8.68701, -37.22998, 16.13293], "curve": 0.25, "c3": 0.75}, {"time": 2, "vertices": [32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001], "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "vertices": [-23.57898, 28.54291, -19.85593, 28.54297, -9.92792, 21.09698, -8.68701, 17.3739, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -13.651, 9.92801, -29.784, 18.61499, -44.67603, 50.88098, -45.91699, 52.12201, -18.61499, 63.29102, -12.41003, 11.16907, -9.92798, 18.61511, -21.09702, 11.16895, -35.98901, 21.09698, -27.30203, 26.06104, -26.06094, 33.50696, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -39.71204, 22.33801, -37.22997, 11.16895, -32.26602, 19.85596, -34.74799, 8.68701, -37.22998, 16.13293], "curve": 0.25, "c3": 0.75}, {"time": 4, "vertices": [32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001]}]}, "baiy1": {"baiy1": [{"vertices": [57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396], "curve": 0.25, "c3": 0.75}, {"time": 3.9, "offset": 6, "vertices": [-36.56787, -20.89606, -44.40393, -13.06012, -35.26202, -9.14203, -44.40399, -1.30603, -57.46399, 1.30603, -65.29993, -7.836, -73.1358, -31.34412, -90.11389, -35.26215, -112.31586, -26.12024, -152.80182, -16.97839, -154.10779, -3.91833, -154.10779, -3.91833, -154.10779, -3.91833, -152.80182, -16.97839, -112.31586, -26.12024, -90.11389, -35.26215, -75.74792, -20.89606, -87.50189, -3.91797, -78.35999, 15.672, -53.54602, 10.448, -22.20197, 16.97797, 0, 0, -23.50793, -3.91803, -22.20197, -5.22406, -65.29993, -7.836, -45.70996, 1.30603, -67.91199, 11.75409, -75.74792, -20.89606, -90.11389, -35.26215, -112.31586, -26.12024, -152.80182, -16.97839]}, {"time": 4, "vertices": [57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396]}]}, "baiy2": {"baiy2": [{"vertices": [2.72364, 2.05935, 3.98702, -17.52284, -28.17914, -28.84343, -52.40607, -17.9456, -43.63056, -7.21148, -59.85454, -38.88683, -103.11839, -33.47879, -178.05759, 9.78506, -178.05759, 9.78506, -178.05759, 9.78506, -97.71042, -18.79993, -66.0351, -20.34517, -59.08186, 14.42042, -27.40658, 24.46392, -16.59063, -2.57612, 2.72364, 2.05935, -31.75558, -22.94003, -29.8606, -15.27795, -49.03848, 2.83191, -62.17222, -28.07087, -97.71042, -22.66284], "curve": 0.368, "c2": 0.63, "c3": 0.705}, {"time": 0.5667, "offset": 2, "vertices": [1.37201, -21.26599, -33.56, -33.56, -59.87012, -21.7251, -50.34003, -10.06799, -67.95905, -44.46698, -114.94299, -38.59393, -196.32593, 8.39001, -196.32593, 8.39001, -196.32593, 8.39001, -109.07001, -22.65289, -74.67105, -24.33099, -67.11993, 13.42395, -32.72101, 24.33105, -20.97504, -5.03406, 0, 0, -37.44397, -27.14899, -35.38605, -18.82806, -56.21295, 0.83899, -70.47601, -32.72101, -109.07001, -26.84796], "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "vertices": [34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909], "curve": 0.244, "c3": 0.696, "c4": 0.77}, {"time": 4, "vertices": [2.72364, 2.05935, 3.98702, -17.52284, -28.17914, -28.84343, -52.40607, -17.9456, -43.63056, -7.21148, -59.85454, -38.88683, -103.11839, -33.47879, -178.05759, 9.78506, -178.05759, 9.78506, -178.05759, 9.78506, -97.71042, -18.79993, -66.0351, -20.34517, -59.08186, 14.42042, -27.40658, 24.46392, -16.59063, -2.57612, 2.72364, 2.05935, -31.75558, -22.94003, -29.8606, -15.27795, -49.03848, 2.83191, -62.17222, -28.07087, -97.71042, -22.66284]}]}, "baiy3": {"baiy3": [{"vertices": [84.98116, 66.34428, 76.55911, 56.36307, 88.9404, 51.54639, 70.05842, 35.07068, 56.58234, 32.05861, 46.01048, 18.42504, 31.45402, 1.00064, 0.50815, 3.83624, 0.50815, 3.83624, 0.50815, 3.83624, 30.35423, 7.59937, 37.2121, 18.79164, 36.38679, 38.79052, 65.7308, 44.68759, 75.95754, 52.50804, 66.84912, 62.67267, 76.88948, 67.42319, 82.21063, 71.86156, 91.38119, 71.32211, 91.38119, 71.32211, 76.8895, 63.64705, 70.78227, 61.17099, 77.88095, 52.50804, 71.02013, 39.39827, 45.52286, 36.8671, 45.27727, 18.79164, 30.35423, 4.66656], "curve": 0.353, "c2": 0.41, "c3": 0.757}, {"time": 2.5667, "vertices": [-21.73511, -16.90515, -50.33713, -50.80225, -8.28912, -67.16016, -72.41406, -123.11316, -118.18007, -133.3424, -154.08305, -179.64325, -203.51811, -238.81812, -308.6131, -229.18817, -308.6131, -229.18817, -308.6131, -229.18817, -207.25313, -216.4082, -183.96312, -178.39825, -186.76598, -110.48022, -87.11102, -90.45325, -52.3801, -63.89429, -83.31314, -29.37427, -49.21515, -13.24109, -31.14404, 1.83203, 0, 0, 0, 0, -49.21509, -26.06519, -69.95578, -34.47412, -45.84802, -63.89429, -69.14801, -108.41626, -155.73906, -117.01233, -156.5731, -178.39825, -207.25313, -226.36829]}, {"time": 2.6667, "vertices": [129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813], "curve": 0.264, "c3": 0.618, "c4": 0.43}, {"time": 4, "vertices": [84.98116, 66.34428, 76.55911, 56.36307, 88.9404, 51.54639, 70.05842, 35.07068, 56.58234, 32.05861, 46.01048, 18.42504, 31.45402, 1.00064, 0.50815, 3.83624, 0.50815, 3.83624, 0.50815, 3.83624, 30.35423, 7.59937, 37.2121, 18.79164, 36.38679, 38.79052, 65.7308, 44.68759, 75.95754, 52.50804, 66.84912, 62.67267, 76.88948, 67.42319, 82.21063, 71.86156, 91.38119, 71.32211, 91.38119, 71.32211, 76.8895, 63.64705, 70.78227, 61.17099, 77.88095, 52.50804, 71.02013, 39.39827, 45.52286, 36.8671, 45.27727, 18.79164, 30.35423, 4.66656]}]}, "baiy6": {"baiy6": [{"vertices": [-53.8987, 32.7593, -52.51366, 32.06022, -50.64476, 29.38796, -49.14782, 30.18793, -46.9737, 33.43927, -42.40037, 34.37332, -39.7365, 34.04145, -35.67091, 33.99329, -24.40675, 32.37135, -24.40675, 32.37135, -24.40675, 32.37135, -35.43102, 32.31411, -39.87358, 30.7807, -43.64564, 32.84113, -46.27852, 30.21205, -49.31435, 27.39881, -52.54352, 27.42445, -55.19223, 31.53111, -56.69496, 33.23497, -56.69496, 33.23497, -56.69496, 33.23497, -54.11291, 32.88298, -52.43402, 28.41335, -49.78904, 29.52464, -46.26395, 31.80486, -42.72098, 33.30811, -40.16631, 32.63352, -35.73944, 33.44498], "curve": 0.312, "c2": 0.25, "c3": 0.757}, {"time": 3.2333, "vertices": [27.98798, -4.76099, 41.85107, -11.75812, 60.55707, -38.50507, 75.54004, -30.49811, 97.30099, 2.04492, 143.07593, 11.39392, 169.73886, 8.0722, 210.43176, 7.59009, 323.17578, -8.64398, 323.17578, -8.64398, 323.17578, -8.64398, 212.83282, -9.21692, 168.36682, -24.56494, 130.61188, -3.94196, 104.25916, -30.25665, 73.87317, -58.41467, 41.55212, -58.15802, 15.04095, -17.05402, 0, 0, 0, 0, 0, 0, 25.84399, -3.52301, 42.64813, -48.26001, 69.12201, -37.13702, 104.405, -14.31409, 139.86694, 0.73212, 165.43686, -6.01996, 209.74579, 2.10205], "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "vertices": [-62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401], "curve": 0.289, "c3": 0.628, "c4": 0.38}, {"time": 4, "vertices": [-53.8987, 32.7593, -52.51366, 32.06022, -50.64476, 29.38796, -49.14782, 30.18793, -46.9737, 33.43927, -42.40037, 34.37332, -39.7365, 34.04145, -35.67091, 33.99329, -24.40675, 32.37135, -24.40675, 32.37135, -24.40675, 32.37135, -35.43102, 32.31411, -39.87358, 30.7807, -43.64564, 32.84113, -46.27852, 30.21205, -49.31435, 27.39881, -52.54352, 27.42445, -55.19223, 31.53111, -56.69496, 33.23497, -56.69496, 33.23497, -56.69496, 33.23497, -54.11291, 32.88298, -52.43402, 28.41335, -49.78904, 29.52464, -46.26395, 31.80486, -42.72098, 33.30811, -40.16631, 32.63352, -35.73944, 33.44498]}]}}}}, "animation2": {"slots": {"wu6": {"attachment": [{"name": null}]}, "wu2": {"attachment": [{"name": null}]}, "jj_biyan": {"color": [{"time": 2.8667, "color": "ffffff00"}, {"time": 3.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.2667, "color": "ffffffff"}, {"time": 3.4667, "color": "ffffff00"}], "attachment": [{"time": 2.8667, "name": "jj_biyan"}, {"time": 3.4667, "name": null}]}, "lu": {"attachment": [{"name": null}]}, "baiy4": {"color": [{"color": "ffffffba"}, {"time": 0.9, "color": "ffffff00", "curve": "stepped"}, {"time": 1, "color": "ffffff00"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6667, "color": "ffffffff"}, {"time": 4, "color": "ffffffba"}], "attachment": [{"name": null}]}, "baiy7": {"color": [{"color": "ffffff00"}, {"time": 0.5, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3667, "color": "ffffffff"}, {"time": 1.9, "color": "ffffff00"}, {"time": 2, "color": "ffffff00"}, {"time": 2.5, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3667, "color": "ffffffff"}, {"time": 3.9, "color": "ffffff00"}], "attachment": [{"name": null}]}, "wu3": {"attachment": [{"name": null}]}, "baiy5": {"color": [{"time": 0.3333, "color": "ffffffff"}, {"time": 1.5667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6667, "color": "ffffff00"}, {"time": 2.6667, "color": "ffffffff"}], "attachment": [{"name": null}]}, "wu1": {"attachment": [{"name": null}]}, "yj_biyan": {"color": [{"time": 1.1333, "color": "ffffff00"}, {"time": 1.3, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4333, "color": "ffffffff"}, {"time": 1.5333, "color": "ffffff00"}], "attachment": [{"time": 1.1333, "name": "yj_biyan"}, {"time": 1.5333, "name": null}]}, "wu4": {"attachment": [{"name": null}]}, "baiy8": {"color": [{"color": "ffffff00"}, {"time": 0.5, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1667, "color": "ffffffff"}, {"time": 1.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 2, "color": "ffffff00"}, {"time": 2.5, "color": "ffffffff", "curve": "stepped"}, {"time": 3.1667, "color": "ffffffff"}, {"time": 3.6667, "color": "ffffff00"}], "attachment": [{"name": null}]}, "baiy1": {"color": [{"color": "ffffff00"}, {"time": 0.3333, "color": "ffffff2f"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff"}, {"time": 3.9, "color": "ffffff00"}], "attachment": [{"name": null}]}, "wu5": {"attachment": [{"name": null}]}, "wu7": {"attachment": [{"name": null}]}, "baiy2": {"color": [{"color": "ffffff75"}, {"time": 0.5667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 3.3333, "color": "ffffffff"}, {"time": 4, "color": "ffffff75"}], "attachment": [{"name": null}]}, "baiy3": {"color": [{"color": "ffffffa2", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffa2"}, {"time": 2.5667, "color": "ffffff00"}, {"time": 2.6667, "color": "ffffff00"}, {"time": 3.6667, "color": "ffffffa2"}], "attachment": [{"name": null}]}, "baiy6": {"color": [{"color": "ffffffaa"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}, {"time": 3.2333, "color": "ffffff00"}, {"time": 3.3333, "color": "ffffff00"}, {"time": 4, "color": "ffffffaa"}], "attachment": [{"name": null}]}, "dabj": {"attachment": [{"name": null}]}}, "bones": {"yy": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": -68.18, "y": -25.86, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -68.18, "y": -25.86, "curve": 0.25, "c3": 0.75}, {"time": 4}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 1.694, "y": 1.694, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 1.694, "y": 1.694, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "jj_17": {"translate": [{"y": 0.29, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "y": 6.54, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "y": -2.18, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "y": 0.29, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "y": 6.54, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "y": -2.18, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "y": 0.29}]}, "jj_18": {"rotate": [{"angle": -0.51, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -0.97, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 0.67, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -0.51, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": -0.97, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 0.67, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -0.51}], "translate": [{"x": -0.01, "y": 0.41, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "x": -0.09, "y": 4.25, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "x": -0.01, "y": 0.41, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "x": -0.09, "y": 4.25, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "x": -0.01, "y": 0.41}]}, "jj_19": {"rotate": [{"angle": 1.46, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 2.69, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.62, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 1.46, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": 2.69, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -1.62, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 1.46}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 9.26, "y": -1.05, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 9.26, "y": -1.05, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "jj_3": {"rotate": [{"angle": 0.8, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 2.31, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -0.72, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 0.8, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "angle": 2.31, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -0.72, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 0.8}], "translate": [{"x": 3.38, "y": 0.98, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 11.9, "y": 3.46, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 3.38, "y": 0.98, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 11.9, "y": 3.46, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": 3.38, "y": 0.98}]}, "jj_13": {"rotate": [{"angle": 0.83, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 1.67, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 0.83, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 1.67, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 0.83}], "translate": [{"x": 1.96, "y": -1.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 6.91, "y": -4.45, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 1.96, "y": -1.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 6.91, "y": -4.45, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": 1.96, "y": -1.26}]}, "jj_12": {"rotate": [{"angle": -1.15, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -1.6, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -1.15, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -1.6, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -1.15}], "translate": [{"x": -1.44, "y": 4.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -2.88, "y": 9.25, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": -1.44, "y": 4.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": -2.88, "y": 9.25, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": -1.44, "y": 4.63}]}, "jj_1": {"rotate": [{"angle": -2.66, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -2.94, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": -2.66, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -2.94, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": -2.66}]}, "jj_10": {"rotate": [{"angle": 8.34, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 3.06, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 10.43, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 8.34, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": 3.06, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 10.43, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 8.34}], "translate": [{"x": 1.78, "y": 8.5}]}, "jj_11": {"rotate": [{"angle": 1.98, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": -4.75, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 2.69, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": 1.98, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": -4.75, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 2.69, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": 1.98}], "translate": [{"x": -6.5, "y": 5.12, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": -0.04, "y": -5.44, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -9.06, "y": 9.31, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -6.5, "y": 5.12, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "x": -0.04, "y": -5.44, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": -9.06, "y": 9.31, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": -6.5, "y": 5.12}]}, "jj_9": {"rotate": [{"angle": 2.8, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": -4.97, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 3.63, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": 2.8, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": -4.97, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 3.63, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": 2.8}]}, "jj_14": {"rotate": [{"angle": 8.07, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -2.64, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 8.07, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -2.64, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 8.07}]}, "jj_15": {"rotate": [{"angle": 19.61, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 22.54, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -7.84, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 19.61, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": 22.54, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -7.84, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": 19.61}]}, "jj_20": {"translate": [{"x": 19.56, "y": -10.74, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "x": 24.53, "y": -13.47, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 2, "x": 19.56, "y": -10.74, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 2.7333, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "x": 24.53, "y": -13.47, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 4, "x": 19.56, "y": -10.74}]}, "jj_21": {"rotate": [{"angle": -4.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -9.38, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": -4.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -9.38, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -4.69}], "translate": [{"x": 0.6, "y": 1.09, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 6.21, "y": 11.31, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "x": 0.6, "y": 1.09, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": 6.21, "y": 11.31, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "x": 0.6, "y": 1.09}]}, "jj_22": {"rotate": [{"angle": -10.28, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 8.8, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -17.85, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -10.28, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": 8.8, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -17.85, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -10.28}], "translate": [{"x": 0.7, "y": 1.42, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": 2.94, "y": 6.76, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -4.93, "y": -12.06, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.7, "y": 1.42, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "x": 2.94, "y": 6.76, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -4.93, "y": -12.06, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": 0.7, "y": 1.42}]}, "jj_23": {"rotate": [{"angle": -20.18, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": 3.76, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -22.74, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": -20.18, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": 3.76, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -22.74, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": -20.18}], "translate": [{"x": 1.31, "y": -4.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "x": 4.07, "y": 9.33, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -1.44, "y": -19.32, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 1.31, "y": -4.99, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "x": 4.07, "y": 9.33, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": -1.44, "y": -19.32, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 1.31, "y": -4.99}]}, "jj_24": {"rotate": [{"angle": -5.69, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": 2.21, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -6.53, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": -5.69, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": 2.21, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -6.53, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": -5.69}]}, "jj_25": {"rotate": [{"angle": -27.48, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 3.81, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -27.48, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 3.81, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -27.48}]}, "jj_16": {"rotate": [{"angle": -1.09, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 7.29, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -4.41, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -1.09, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": 7.29, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -4.41, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -1.09}]}, "jj_26": {"rotate": [{"angle": -9.62, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": 7.38, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -11.44, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": -9.62, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": 7.38, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -11.44, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": -9.62}]}, "jj_28": {"rotate": [{"angle": 1.44, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 7.29, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -4.41, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 1.44, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "angle": 7.29, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -4.41, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 1.44}]}, "jj_29": {"rotate": [{"angle": -6.1, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 7.38, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -11.44, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -6.1, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": 7.38, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -11.44, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -6.1}]}, "jj_32": {"rotate": [{"angle": 9.46, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": -6.39, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 11.16, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": 9.46, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": -6.39, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 11.16, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": 9.46}]}, "jj_33": {"rotate": [{"angle": 21.74, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -6.48, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 21.74, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -6.48, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 21.74}]}, "jj_34": {"rotate": [{"angle": 1.68, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 9.02, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 1.68, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 9.02, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 1.68}]}, "jj_35": {"rotate": [{"angle": 13.14, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -12.24, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 23.2, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 13.14, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": -12.24, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 23.2, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 13.14}]}, "jj_8": {"rotate": [{"angle": -2.78, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 3.05, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -5.1, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -2.78, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": 3.05, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -5.1, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -2.78}]}, "jj_30": {"rotate": [{"angle": -12.23, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": 4.25, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -13.99, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": -12.23, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": 4.25, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -13.99, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": -12.23}]}, "jj_38": {"rotate": [{}]}, "jj_37": {"rotate": [{}]}, "jj_41": {"rotate": [{"angle": -0.72, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 3.31, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -4.75, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": -0.72, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "angle": 3.31, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -4.75, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -0.72}]}, "jj_42": {"rotate": [{"angle": -6.66, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": -8.35, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 2, "angle": -6.66, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 2.7333, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "angle": -8.35, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 4, "angle": -6.66}], "translate": [{"x": 1.06, "y": -5.33, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 2.11, "y": -10.67, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 1.06, "y": -5.33, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 2.11, "y": -10.67, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 1.06, "y": -5.33}]}, "jj_5": {"rotate": [{"angle": 10.85, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.5, "angle": 21.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 10.85, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.5, "angle": 21.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 10.85}]}, "jj_43": {"rotate": [{"angle": 7.46, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 10.86, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.12, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 7.46, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": 10.86, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -1.12, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 7.46}]}, "jj_44": {"rotate": [{"angle": 6.26, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.5667, "angle": 23.71, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": -5.96, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 2, "angle": 6.26, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 2.5667, "angle": 23.71, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": -5.96, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 4, "angle": 6.26}]}, "jj_45": {"rotate": [{"angle": -2.44, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 1.88, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6667, "angle": -4.16, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -2.44, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": 1.88, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.6667, "angle": -4.16, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -2.44}]}, "jj_46": {"rotate": [{"angle": -9.9, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": 5.9, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.8333, "angle": -11.58, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": -9.9, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": 5.9, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.8333, "angle": -11.58, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": -9.9}]}, "jj_47": {"rotate": [{"angle": -23.57, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 10.54, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -23.57, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 10.54, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -23.57}]}, "jj_48": {"rotate": [{"angle": -0.64, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": -6.59, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": -0.64, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": -6.59, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": -0.64}]}, "jj_49": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -8.84, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -8.84, "curve": 0.25, "c3": 0.75}, {"time": 4}], "translate": [{"x": 0.01, "y": -2.25, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": 0.03, "y": -7.94, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 0.01, "y": -2.25, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "x": 0.03, "y": -7.94, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": 0.01, "y": -2.25}]}, "jj_50": {"rotate": [{"angle": 8.16, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 10.52, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -13.97, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 8.16, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": 10.52, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -13.97, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": 8.16}]}, "jj_51": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 7.78, "y": -1.78, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 7.78, "y": -1.78, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "jj_54": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 0.06, "y": -2.96, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 0.06, "y": -2.96, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "jj_57": {"rotate": [{"angle": 1.6, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": 2.72, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 2, "angle": 1.6, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 2.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": 2.72, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 4, "angle": 1.6}]}, "jj_58": {"translate": [{"x": -3.46, "y": 11.07, "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 0.7667, "x": 7.8, "y": -5.08, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": -5.71, "y": 14.29, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 2, "x": -3.46, "y": 11.07, "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 2.7667, "x": 7.8, "y": -5.08, "curve": 0.25, "c3": 0.75}, {"time": 3.7667, "x": -5.71, "y": 14.29, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 4, "x": -3.46, "y": 11.07}]}, "jj_59": {"rotate": [{"angle": 3.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -8.74, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 3.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -8.74, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 3.89}], "translate": [{"x": -10.68, "y": 41.9, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 17.19, "y": -9.31, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -10.68, "y": 41.9, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 17.19, "y": -9.31, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -10.68, "y": 41.9}]}, "jj_39": {"rotate": [{"angle": 0.77, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 2.72, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 0.77, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 2.72, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 0.77}]}, "jj_60": {"translate": [{"x": -0.07, "y": 4.72, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "x": 8.77, "y": -3.11, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -8.9, "y": 12.55, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": -0.07, "y": 4.72, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "x": 8.77, "y": -3.11, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": -8.9, "y": 12.55, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": -0.07, "y": 4.72}]}, "jj_61": {"rotate": [{"angle": 5.32, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -6.91, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 5.32, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -6.91, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 5.32}], "translate": [{"x": -9.11, "y": 25.96, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": 18.89, "y": -5.02, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -20.21, "y": 38.23, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -9.11, "y": 25.96, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "x": 18.89, "y": -5.02, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": -20.21, "y": 38.23, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": -9.11, "y": 25.96}]}, "jj_63": {"rotate": [{"angle": -0.32, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -3.27, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": -0.32, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -3.27, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": -0.32}]}, "jj_64": {"translate": [{"x": -1.25, "y": -1.5, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "x": 2.63, "y": 4.74, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "x": -7.91, "y": -12.24, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "x": -1.25, "y": -1.5, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.4, "x": 2.63, "y": 4.74, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "x": -7.91, "y": -12.24, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "x": -1.25, "y": -1.5}]}, "jj_65": {"rotate": [{"angle": -9.73, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 6.11, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -9.73, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "angle": 6.11, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -9.73}], "translate": [{"x": -11, "y": -16.55, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": 3.16, "y": 7.55, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -16.61, "y": -26.1, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -11, "y": -16.55, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "x": 3.16, "y": 7.55, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": -16.61, "y": -26.1, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": -11, "y": -16.55}]}, "jj_66": {"rotate": [{"angle": -0.47, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": -1.66, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -0.47, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": -1.66, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -0.47}], "translate": [{"curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3333, "x": -1.12, "y": 2.47, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1, "x": -3.94, "y": 8.71, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2.3333, "x": -1.12, "y": 2.47, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 3, "x": -3.94, "y": 8.71, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "jj_67": {"rotate": [{"angle": -2.94, "curve": 0.351, "c2": 0.39, "c3": 0.701, "c4": 0.78}, {"time": 0.3333, "angle": -0.57, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -5.95, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": -2.94, "curve": 0.351, "c2": 0.39, "c3": 0.701, "c4": 0.78}, {"time": 2.3333, "angle": -0.57, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -5.95, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -2.94}], "translate": [{"x": 0.77, "y": 5.71, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "x": 1.4, "y": 7.55, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.3333, "x": 0.77, "y": 5.71, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 1.1667, "x": -5.08, "y": -11.47, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "x": 0.77, "y": 5.71, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "x": 1.4, "y": 7.55, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2.3333, "x": 0.77, "y": 5.71, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 3.1667, "x": -5.08, "y": -11.47, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "x": 0.77, "y": 5.71}]}, "jj_68": {"rotate": [{"angle": -7.04, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.3333, "angle": -0.79, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6667, "angle": 3.32, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -11.17, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -7.04, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 2.3333, "angle": -0.79, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.6667, "angle": 3.32, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -11.17, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -7.04}], "translate": [{"x": 0.79, "y": 0.07, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": 2.02, "y": 6.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": -2.32, "y": -14.99, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.79, "y": 0.07, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "x": 2.02, "y": 6.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "x": -2.32, "y": -14.99, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": 0.79, "y": 0.07}]}, "yj_18": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "y": -9.75, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "y": -9.75, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "yj_19": {"rotate": [{"angle": 0.22, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -2.22, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 1.19, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 0.22, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": -2.22, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 1.19, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 0.22}], "translate": [{"x": -0.05, "y": -0.2, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": -0.54, "y": -2.03, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "x": -0.05, "y": -0.2, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": -0.54, "y": -2.03, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "x": -0.05, "y": -0.2}]}, "yj_20": {"rotate": [{"angle": -0.61, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 2.99, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -2.04, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -0.61, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": 2.99, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -2.04, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -0.61}], "translate": [{"x": -2.97, "y": -0.14, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -10.47, "y": -0.5, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -2.97, "y": -0.14, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -10.47, "y": -0.5, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": -2.97, "y": -0.14}]}, "yj_21": {"translate": [{"x": -0.46, "y": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "x": 2.73, "y": 2.25, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.5, "x": -3.64, "y": -3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": -0.46, "y": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "x": 2.73, "y": 2.25, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.5, "x": -3.64, "y": -3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": -0.46, "y": -0.38}]}, "yj_biyan": {"rotate": [{"angle": -6.42, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -6.42, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -6.42}], "translate": [{"x": -0.04, "y": -3.01, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -0.06, "y": -4.21, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -0.04, "y": -3.01, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": -0.06, "y": -4.21, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": -0.04, "y": -3.01}]}, "yj_24": {"rotate": [{"angle": -2.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -5.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": -2.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "angle": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -5.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -2.34}]}, "yj_17": {"rotate": [{"angle": -1.58, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 0.7333, "angle": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": -2.13, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 2, "angle": -1.58, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 2.7333, "angle": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "angle": -2.13, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 4, "angle": -1.58}]}, "yj_9": {"rotate": [{"angle": -8.04, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 1.79, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -8.04, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 1.79, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -8.04}]}, "yj_10": {"rotate": [{"angle": -7.58, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": -8.56, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 1.66, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": -7.58, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": -8.56, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 1.66, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": -7.58}]}, "yj_11": {"rotate": [{"angle": -3.46, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -6.27, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 3.63, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -3.46, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": -6.27, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 3.63, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -3.46}]}, "yj_16": {"rotate": [{"angle": -1.31, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 1.53, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -2.44, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -1.31, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": 1.53, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -2.44, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -1.31}]}, "yj_23": {"rotate": [{"angle": -5.87, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": 2.05, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -6.71, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": -5.87, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": 2.05, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -6.71, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": -5.87}], "translate": [{"x": -0.5, "y": -2.3, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.6, "x": -6.04, "y": 7.4, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "x": 2.73, "y": -7.95, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "x": -0.5, "y": -2.3, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.6, "x": -6.04, "y": 7.4, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "x": 2.73, "y": -7.95, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4, "x": -0.5, "y": -2.3}]}, "yj_12": {"rotate": [{"angle": -1.73, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": -2.01, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.1667, "angle": 0.89, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": -1.73, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": -2.01, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 3.1667, "angle": 0.89, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": -1.73}], "translate": [{"x": 8.14, "y": -3.69, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 8.14, "y": -3.69, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 8.14, "y": -3.69}]}, "yj_13": {"rotate": [{"angle": -1.42, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -2.84, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.3333, "angle": 2.19, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -1.42, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": -2.84, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 3.3333, "angle": 2.19, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -1.42}], "translate": [{"x": -2.89, "y": -9, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "x": -3.2, "y": -9.96, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "x": -2.89, "y": -9, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "x": -3.2, "y": -9.96, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 3.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "x": -2.89, "y": -9}]}, "yj_14": {"rotate": [{"angle": -0.56, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": -5.27, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.5, "angle": 4.15, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": -0.56, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "angle": -5.27, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 3.5, "angle": 4.15, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -0.56}], "translate": [{"x": -2.26, "y": -4.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": -3.16, "y": -6.36, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -2.26, "y": -4.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "x": -3.16, "y": -6.36, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 3.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": -2.26, "y": -4.55}]}, "yj_15": {"rotate": [{"angle": 1.6, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -10.93, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.6667, "angle": 6.57, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 1.6, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": -10.93, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 3.6667, "angle": 6.57, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 1.6}], "translate": [{"x": -2.1, "y": -2.12, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "x": -4.21, "y": -4.24, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": -2.1, "y": -2.12, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "x": -4.21, "y": -4.24, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 3.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": -2.1, "y": -2.12}]}, "yj_25": {"rotate": [{"angle": 13.84, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": -18.94, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.8333, "angle": 17.34, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": 13.84, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": -18.94, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 3.8333, "angle": 17.34, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": 13.84}], "translate": [{"x": -1.99, "y": -2.69, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": -7.02, "y": -9.46, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -1.99, "y": -2.69, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "x": -7.02, "y": -9.46, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 3.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": -1.99, "y": -2.69}]}, "yj_biyan2": {"rotate": [{"angle": 0.07, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -2.01, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.6667, "angle": 0.89, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 0.07, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": -2.01, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 3.6667, "angle": 0.89, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 0.07}]}, "yj_biyan3": {"rotate": [{"angle": 1.7, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": -2.84, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.8333, "angle": 2.19, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": 1.7, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": -2.84, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 3.8333, "angle": 2.19, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": 1.7}], "translate": [{"x": -0.84, "y": -2.13, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": -2.97, "y": -7.5, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -0.84, "y": -2.13, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "x": -2.97, "y": -7.5, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 3.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": -0.84, "y": -2.13}]}, "yj_biyan4": {"rotate": [{"angle": 4.15, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -5.27, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 4.15, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -5.27, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 4.15}], "translate": [{"x": -0.2, "y": -0.33, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "x": -2.03, "y": -3.45, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.8333, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "x": -0.2, "y": -0.33, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "x": -2.03, "y": -3.45, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 3.8333, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "x": -0.2, "y": -0.33}]}, "yj_biyan5": {"rotate": [{"angle": 5.49, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 6.57, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -4.58, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 5.49, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": 6.57, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -4.58, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": 5.49}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": -6.44, "y": -4.47, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -6.44, "y": -4.47, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "yj_28": {"rotate": [{"angle": 1.12, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 2.24, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 1.12, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "angle": 2.24, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 1.12}]}, "yj_8": {"rotate": [{"angle": -3.95, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -5.51, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -3.95, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -5.51, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -3.95}], "translate": [{"x": 1.61, "y": 1.43, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 5.68, "y": 5.03, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 1.61, "y": 1.43, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 5.68, "y": 5.03, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": 1.61, "y": 1.43}]}, "yj_6": {"rotate": [{"angle": 6.29, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 6.96, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": 6.29, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 6.96, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": 6.29}]}, "yj_1": {"rotate": [{"angle": -16.99, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.2333, "angle": -26.39, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 30.09, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 2, "angle": -16.99, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 2.2333, "angle": -26.39, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "angle": 30.09, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 4, "angle": -16.99}]}, "yj_29": {"rotate": [{"angle": -1.17, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -2.63, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 2.53, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -1.17, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": -2.63, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 2.53, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -1.17}]}, "yj_30": {"rotate": [{"angle": 2.85, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -5.54, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 6.17, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 2.85, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": -5.54, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 6.17, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 2.85}], "translate": [{"x": -0.91, "y": -8.12, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": -0.37, "y": -15.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -2.25, "y": 10, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -0.91, "y": -8.12, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "x": -0.37, "y": -15.3, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -2.25, "y": 10, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": -0.91, "y": -8.12}], "scale": [{"y": 0.735, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "y": 0.735, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4, "y": 0.735}]}, "yj_31": {"rotate": [{"angle": 6.77, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -11.35, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 6.77, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -11.35, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 6.77}], "translate": [{"x": -2.22, "y": 0.47, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": -7.84, "y": 1.64, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -2.22, "y": 0.47, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "x": -7.84, "y": 1.64, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": -2.22, "y": 0.47}]}, "yj_32": {"rotate": [{"angle": 6.49, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 7.69, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -4.76, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 6.49, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": 7.69, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -4.76, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": 6.49}]}, "yj_33": {"rotate": [{"angle": 3.19, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "angle": 12.85, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -13.4, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "angle": 3.19, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.4, "angle": 12.85, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "angle": -13.4, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "angle": 3.19}]}, "yj_27": {"translate": [{"x": -10.27, "y": -3.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -20.54, "y": -7.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": -10.27, "y": -3.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": -20.54, "y": -7.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": -10.27, "y": -3.64}]}, "yj_34": {"translate": [{"x": -13.74, "y": -5.29, "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": -16.48, "y": -6.35, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 2, "x": -13.74, "y": -5.29, "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 2.7667, "curve": 0.25, "c3": 0.75}, {"time": 3.7667, "x": -16.48, "y": -6.35, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 4, "x": -13.74, "y": -5.29}]}, "yj_36": {"rotate": [{"angle": 0.02}]}, "yj_37": {"rotate": [{"angle": -0.03}]}, "yj_39": {"rotate": [{}]}, "yj_40": {"rotate": [{}]}, "yj_42": {"rotate": [{"angle": 2.99, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 3.59, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -2.65, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 2.99, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": 3.59, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -2.65, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": 2.99}], "translate": [{"x": 5.81, "y": 2.69, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 5.81, "y": 2.69, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 5.81, "y": 2.69}]}, "yj_43": {"rotate": [{"angle": 4.67, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 7.52, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.51, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 4.67, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": 7.52, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -2.51, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 4.67}], "translate": [{"x": -2.51, "y": 7.47, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "x": -2.78, "y": 8.26, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "x": -2.51, "y": 7.47, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "x": -2.78, "y": 8.26, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "x": -2.51, "y": 7.47}]}, "yj_44": {"rotate": [{"angle": 1.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 11.56, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -8.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 1.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "angle": 11.56, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -8.71, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 1.42}], "translate": [{"x": -1.33, "y": 3.71, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": -1.86, "y": 5.18, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -1.33, "y": 3.71, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "x": -1.86, "y": 5.18, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": -1.33, "y": 3.71}]}, "yj_45": {"rotate": [{"angle": 2.52, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 3.51, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 2.52, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": 3.51, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 2.52}]}, "yj_46": {"rotate": [{"angle": 0.61, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 1.22, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 0.61, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "angle": 1.22, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 0.61}], "translate": [{"x": -1.2, "y": 1.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": -2.1, "y": 2.69, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.08, "y": -1.32, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -1.2, "y": 1.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "x": -2.1, "y": 2.69, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 1.08, "y": -1.32, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": -1.2, "y": 1.55}]}, "yj_47": {"rotate": [{"angle": -1.47, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 2.81, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -3.16, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -1.47, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": 2.81, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -3.16, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -1.47}], "translate": [{"x": -0.11, "y": 1.57, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "x": -1.93, "y": 5.04, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 1.71, "y": -1.91, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": -0.11, "y": 1.57, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "x": -1.93, "y": 5.04, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 1.71, "y": -1.91, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": -0.11, "y": 1.57}]}, "yj_48": {"rotate": [{"angle": -0.45, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": 7.64, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -1.32, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": -0.45, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": 7.64, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -1.32, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": -0.45}], "translate": [{"x": -0.13, "y": 0.46, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": -2.38, "y": 6.05, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.76, "y": -1.75, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -0.13, "y": 0.46, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "x": -2.38, "y": 6.05, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 0.76, "y": -1.75, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": -0.13, "y": 0.46}]}, "yj_49": {"rotate": [{"angle": -5.76, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 7.9, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -5.76, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 7.9, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -5.76}]}, "yj_50": {"rotate": [{"angle": -10.07, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": -13.52, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 22.19, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": -10.07, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": -13.52, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 22.19, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": -10.07}]}, "yj_51": {"rotate": [{"angle": -4.44, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": -5.76, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 7.9, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": -4.44, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": -5.76, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 7.9, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": -4.44}]}, "yj_52": {"rotate": [{"angle": -0.38, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "angle": -13.52, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 22.19, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "angle": -0.38, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.4, "angle": -13.52, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "angle": 22.19, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "angle": -0.38}]}, "yj_53": {"rotate": [{"angle": -1.88, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -5.76, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.9, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -1.88, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": -5.76, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 7.9, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -1.88}]}, "yj_54": {"rotate": [{"angle": 4.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": -13.52, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 22.19, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 4.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "angle": -13.52, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 22.19, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 4.34}]}, "yj_55": {"rotate": [{"angle": -4.81, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.8667, "angle": 7.9, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -5.76, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 2, "angle": -4.81, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 2.8667, "angle": 7.9, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "angle": -5.76, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 4, "angle": -4.81}]}, "yj_56": {"rotate": [{"angle": -7.58, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.2333, "angle": -13.52, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 22.19, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 2, "angle": -7.58, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 2.2333, "angle": -13.52, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "angle": 22.19, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 4, "angle": -7.58}]}, "yj_57": {"rotate": [{"angle": 0.83, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 1.67, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 0.83, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "angle": 1.67, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 0.83}], "translate": [{"x": 2.53, "y": 0.79, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": 4.81, "y": 2.11, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -3.22, "y": -2.53, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 2.53, "y": 0.79, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "x": 4.81, "y": 2.11, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -3.22, "y": -2.53, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": 2.53, "y": 0.79}]}, "yj_58": {"rotate": [{"angle": 1.53, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 5.38, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 1.53, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": 5.38, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 1.53}], "translate": [{"x": 0.35, "y": 1.24, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "x": -2.6, "y": 6.56, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 3.31, "y": -4.07, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 0.35, "y": 1.24, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "x": -2.6, "y": 6.56, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 3.31, "y": -4.07, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 0.35, "y": 1.24}]}, "yj_59": {"rotate": [{"angle": 0.7, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": 7.24, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": 0.7, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": 7.24, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": 0.7}], "translate": [{"x": 3.57, "y": 0.47, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": -0.02, "y": 9.91, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 4.99, "y": -3.27, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 3.57, "y": 0.47, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "x": -0.02, "y": 9.91, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 4.99, "y": -3.27, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": 3.57, "y": 0.47}]}, "yj_60": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.95, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 8.95, "curve": 0.25, "c3": 0.75}, {"time": 4}], "translate": [{"x": 3.53, "y": -3.04, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "x": -6.34, "y": 8.21, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 4.58, "y": -4.24, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "x": 3.53, "y": -3.04, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "x": -6.34, "y": 8.21, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 4.58, "y": -4.24, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "x": 3.53, "y": -3.04}]}, "yj_61": {"rotate": [{"angle": -4.47, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -6.24, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -4.47, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": -6.24, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -4.47}]}, "yj_62": {"rotate": [{"angle": -1.49, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": -2.97, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": -1.49, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "angle": -2.97, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -1.49}], "translate": [{"x": 0.23, "y": -5.22, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": 0.32, "y": -7.29, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.23, "y": -5.22, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "x": 0.32, "y": -7.29, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": 0.23, "y": -5.22}]}, "yj_63": {"rotate": [{"angle": -1.07, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -6.73, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 1.17, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -1.07, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": -6.73, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 1.17, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -1.07}], "translate": [{"x": 0.71, "y": -1.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "x": -2.1, "y": -6.64, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 3.52, "y": 3.13, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 0.71, "y": -1.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "x": -2.1, "y": -6.64, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 3.52, "y": 3.13, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 0.71, "y": -1.75}]}, "yj_41": {"rotate": [{"angle": -0.6, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": -6.24, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": -0.6, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": -6.24, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": -0.6}]}, "yj_64": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -2.97, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -2.97, "curve": 0.25, "c3": 0.75}, {"time": 4}], "translate": [{"x": -0.38, "y": -0.59, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "x": -3.91, "y": -6.16, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "x": -0.38, "y": -0.59, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "x": -3.91, "y": -6.16, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "x": -0.38, "y": -0.59}]}, "yj_65": {"rotate": [{"angle": 0.41, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 1.17, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -6.73, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 0.41, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": 1.17, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -6.73, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": 0.41}], "translate": [{"x": 4.4, "y": 1.68, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -4.32, "y": -5.46, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 4.4, "y": 1.68, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -4.32, "y": -5.46, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 4.4, "y": 1.68}]}, "yj_66": {"rotate": [{"angle": 1.9, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 6.25, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -9.07, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 1.9, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": 6.25, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -9.07, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 1.9}]}, "yj_67": {"rotate": [{"angle": -5.22, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 12.37, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -22.81, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": -5.22, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "angle": 12.37, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -22.81, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -5.22}]}, "yj_68": {"rotate": [{"angle": -1.41, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 6.25, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -9.07, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": -1.41, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "angle": 6.25, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -9.07, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -1.41}]}, "yj_69": {"rotate": [{"angle": -12.83, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 12.37, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -22.81, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -12.83, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": 12.37, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -22.81, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -12.83}]}, "yj_70": {"rotate": [{"angle": -4.73, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 6.25, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -9.07, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -4.73, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": 6.25, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -9.07, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -4.73}]}, "yj_71": {"rotate": [{"angle": -19.42, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": 12.37, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -22.81, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": -19.42, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": 12.37, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -22.81, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": -19.42}]}, "yj_72": {"rotate": [{"angle": 5.91, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": -12.99, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 7.93, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": 5.91, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": -12.99, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 7.93, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": 5.91}]}, "yj_73": {"rotate": [{"angle": 16.03, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -21.1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 16.03, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -21.1, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 16.03}]}, "yj_74": {"rotate": [{"angle": 7.93, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -12.99, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 7.93, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -12.99, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 7.93}]}, "yj_75": {"rotate": [{"angle": 12.45, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 16.03, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -21.1, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 12.45, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": 16.03, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -21.1, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": 12.45}]}, "yj_76": {"rotate": [{"angle": -0.69, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.5667, "angle": -12.99, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": 7.93, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 2, "angle": -0.69, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 2.5667, "angle": -12.99, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": 7.93, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 4, "angle": -0.69}]}, "yj_77": {"rotate": [{"angle": 9.85, "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 0.7667, "angle": -21.1, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": 16.03, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 2, "angle": 9.85, "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 2.7667, "angle": -21.1, "curve": 0.25, "c3": 0.75}, {"time": 3.7667, "angle": 16.03, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 4, "angle": 9.85}]}, "yj_2": {"rotate": [{"angle": -0.51, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -1.71, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -0.51, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -1.71, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -0.51}], "translate": [{"x": 0.23, "y": -2.39, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -4.6, "y": -12.95, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.23, "y": -2.39, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -4.6, "y": -12.95, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": 0.23, "y": -2.39}]}}, "deform": {"default": {"wu2": {"wu2": [{"vertices": [0.048, 4.15994, 0.04303, 3.72907, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.5052, 2.12548, -2.49664, 3.44866, -2.7198, 7.01144, -1.93964, 6.50926, -0.52745, 5.87029, -0.12194, 5.86925, -1.36365, 4.54404, -0.60729, 4.38177, 0.04566, 3.95671, -2.72998, 5.57146, -1.62774, 6.67496, -1.17819, 5.03376, -0.00953, 4.05011, -0.25534, 4.12625, 0.42661, 2.89655, 0.23137, 4.46674, -0.49158, 4.36941, -0.39935, 2.5695, -1.20613, 2.45802, 0.01695, 1.46902, -0.29971, 2.83552, -0.23356, 2.3383, 0.13562, 4.05968, -3.28584, 3.73398, -1.65745, 4.45986, -1.82643, 4.84779, -1.13955, 4.07798, -1.23378, 3.06604, -3.23283, 2.17366, -4.23721, 5.39457, -1.08083, 6.70287, 0.38044, 7.03613, 0.68002, 4.59788, 0.42043, 4.61759], "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "vertices": [0.16914, 14.6584, 0.15162, 13.14016, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -12.3513, 7.48958, -8.79743, 12.15206, -9.58379, 24.70625, -6.83473, 22.93673, -1.85856, 20.68519, -0.42968, 20.6815, -4.8051, 16.01187, -2.13991, 15.44009, 0.16088, 13.94229, -9.61964, 19.6322, -5.73567, 23.52059, -4.1516, 17.73749, -0.03359, 14.27141, -0.89976, 14.53968, 1.50326, 10.20661, 0.81528, 15.73947, -1.7322, 15.39651, -1.40718, 9.05418, -4.25006, 8.66133, 0.05971, 5.17641, -1.05607, 9.99155, -0.82298, 8.23948, 0.47789, 14.30514, -11.57835, 13.15744, -5.84038, 15.71524, -6.43581, 17.0822, -4.01546, 14.36962, -4.34746, 10.80383, -11.39154, 7.65935, -14.93069, 19.00888, -3.80854, 23.61895, 1.34057, 24.79325, 2.39618, 16.20158, 1.48146, 16.27104], "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "vertices": [0.16914, 14.6584, 0.15162, 13.14016, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -12.3513, 7.48958, -8.79743, 12.15206, -9.58379, 24.70625, -6.83473, 22.93673, -1.85856, 20.68519, -0.42968, 20.6815, -4.8051, 16.01187, -2.13991, 15.44009, 0.16088, 13.94229, -9.61964, 19.6322, -5.73567, 23.52059, -4.1516, 17.73749, -0.03359, 14.27141, -0.89976, 14.53968, 1.50326, 10.20661, 0.81528, 15.73947, -1.7322, 15.39651, -1.40718, 9.05418, -4.25006, 8.66133, 0.05971, 5.17641, -1.05607, 9.99155, -0.82298, 8.23948, 0.47789, 14.30514, -11.57835, 13.15744, -5.84038, 15.71524, -6.43581, 17.0822, -4.01546, 14.36962, -4.34746, 10.80383, -11.39154, 7.65935, -14.93069, 19.00888, -3.80854, 23.61895, 1.34057, 24.79325, 2.39618, 16.20158, 1.48146, 16.27104], "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "vertices": [0.048, 4.15994, 0.04303, 3.72907, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.5052, 2.12548, -2.49664, 3.44866, -2.7198, 7.01144, -1.93964, 6.50926, -0.52745, 5.87029, -0.12194, 5.86925, -1.36365, 4.54404, -0.60729, 4.38177, 0.04566, 3.95671, -2.72998, 5.57146, -1.62774, 6.67496, -1.17819, 5.03376, -0.00953, 4.05011, -0.25534, 4.12625, 0.42661, 2.89655, 0.23137, 4.46674, -0.49158, 4.36941, -0.39935, 2.5695, -1.20613, 2.45802, 0.01695, 1.46902, -0.29971, 2.83552, -0.23356, 2.3383, 0.13562, 4.05968, -3.28584, 3.73398, -1.65745, 4.45986, -1.82643, 4.84779, -1.13955, 4.07798, -1.23378, 3.06604, -3.23283, 2.17366, -4.23721, 5.39457, -1.08083, 6.70287, 0.38044, 7.03613, 0.68002, 4.59788, 0.42043, 4.61759]}]}, "baiy4": {"baiy4": [{"vertices": [-1.25167, -4.04704, 19.29988, -10.09164, 65.23866, -7.67382, 114.80414, 4.41528, 160.74297, -40.31454, 180.08566, -49.98593, 160.74297, -40.31454, 108.7596, -17.3451, 51.94061, -25.80753, 7.21068, -19.76292, -7.29622, -17.3451, -25.42997, 4.41548, -9.71409, 4.41548, -9.71409, 4.41548, -10.923, -10.09154, 64.02977, -16.13614, 12.04632, -17.3451, 116.01312, -10.09154], "curve": 0.377, "c2": 0.61, "c3": 0.719}, {"time": 0.9, "vertices": [10.01001, -10.01013, 34.31998, -17.16016, 88.65994, -14.30017, 147.28984, -0.00024, 201.62985, -52.91016, 224.5099, -64.35022, 201.62985, -52.91016, 140.13988, -25.74011, 72.92996, -35.75012, 20.01993, -28.6001, 2.86004, -25.74011, -18.58997, 0, 0, 0, 0, 0, -1.42999, -17.16003, 87.22997, -24.31006, 25.7399, -25.74011, 148.71992, -17.16003], "curve": 0.25, "c3": 0.75}, {"time": 1, "vertices": [-62.83203, 28.55994, -62.83203, 28.55994, -62.83203, 28.55994, -62.83203, 28.55994, -62.83203, 28.55994, -62.83203, 28.55994, -62.83203, 28.55994, -62.83203, 28.55994, -62.83203, 28.55994, -62.83203, 28.55994, -62.83203, 28.55994, -62.83203, 28.55994, -62.83203, 28.55994, -62.83203, 28.55994, -62.83203, 28.55994, -62.83203, 28.55994, -62.83203, 28.55994, -62.83203, 28.55994], "curve": 0.261, "c2": 0.08, "c3": 0.68, "c4": 0.71}, {"time": 4, "vertices": [-1.25167, -4.04704, 19.29988, -10.09164, 65.23866, -7.67382, 114.80414, 4.41528, 160.74297, -40.31454, 180.08566, -49.98593, 160.74297, -40.31454, 108.7596, -17.3451, 51.94061, -25.80753, 7.21068, -19.76292, -7.29622, -17.3451, -25.42997, 4.41548, -9.71409, 4.41548, -9.71409, 4.41548, -10.923, -10.09154, 64.02977, -16.13614, 12.04632, -17.3451, 116.01312, -10.09154]}]}, "baiy7": {"baiy7": [{"vertices": [-16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704], "curve": 0.25, "c3": 0.75}, {"time": 1.9, "vertices": [46.19997, 38.84998, 46.19997, 38.84998, 46.19997, 38.84998, 46.19997, 38.84998, 46.19997, 38.84998, 46.19997, 52.5, 46.19997, 38.84998, 52.50002, 48.29993, 46.19997, 38.84998, 46.19997, 38.84998, 46.19997, 38.84998, 46.19997, 38.84998, 25.19995, 14.69998, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.20001, 36.75, 46.19997, 38.84998, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48.3, 39.90002, 44.10002, 46.19989, 15.75, 29.39996, 28.35004, 32.55005, 37.79997, 28.34991, 41.99998, 27.29999, 48.29991, 24.14996], "curve": 0.25, "c3": 0.75}, {"time": 2, "vertices": [-16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704], "curve": 0.25, "c3": 0.75}, {"time": 3.9, "vertices": [46.19997, 38.84998, 46.19997, 38.84998, 46.19997, 38.84998, 46.19997, 38.84998, 46.19997, 38.84998, 46.19997, 52.5, 46.19997, 38.84998, 52.50002, 48.29993, 46.19997, 38.84998, 46.19997, 38.84998, 46.19997, 38.84998, 46.19997, 38.84998, 25.19995, 14.69998, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.20001, 36.75, 46.19997, 38.84998, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48.3, 39.90002, 44.10002, 46.19989, 15.75, 29.39996, 28.35004, 32.55005, 37.79997, 28.34991, 41.99998, 27.29999, 48.29991, 24.14996], "curve": 0.25, "c3": 0.75}, {"time": 4, "vertices": [-16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704, -16.36987, -34.37704]}]}, "wu3": {"wu3": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "vertices": [-7.10076, -5.13318, -4.22289, -5.4574, 3.13598, -11.80554, 3.59238, -10.30164, 11.96704, -9.4259, 13.37873, -20.35132, -0.17139, -15.19312, -2.223, -6.56274, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.86335, -0.66248, 0.39123, -0.2998, 0.93003, -0.71387, 0.88069, -0.67542, 0.65993, -0.5061, -0.33271, -0.51685, -0.22075, -0.34302, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -32.5699, 4.95935, -32.79245, 11.76819, -28.70555, 0.3092, -34.85657, -2.29663, -14.79303, -6.0636, 4.13268, -8.75391, 1.97865, -1.60461, 8.08647, -6.26917, -3.3614, -4.95081, -7.90342, 1.38196, -4.06274, -3.5979, -1.62264, -3.04053, -1.50698, -2.34863, -2.66518, -0.83557, -0.69055, 0.09424, 0, 0, -8.61418, -0.26331, -7.00995, -3.073, -6.26721, -4.68408, -2.41994, -6.95239, -1.01698, -4.57556, 2.11131, -1.9325], "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "vertices": [-7.10076, -5.13318, -4.22289, -5.4574, 3.13598, -11.80554, 3.59238, -10.30164, 11.96704, -9.4259, 13.37873, -20.35132, -0.17139, -15.19312, -2.223, -6.56274, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.86335, -0.66248, 0.39123, -0.2998, 0.93003, -0.71387, 0.88069, -0.67542, 0.65993, -0.5061, -0.33271, -0.51685, -0.22075, -0.34302, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -32.5699, 4.95935, -32.79245, 11.76819, -28.70555, 0.3092, -34.85657, -2.29663, -14.79303, -6.0636, 4.13268, -8.75391, 1.97865, -1.60461, 8.08647, -6.26917, -3.3614, -4.95081, -7.90342, 1.38196, -4.06274, -3.5979, -1.62264, -3.04053, -1.50698, -2.34863, -2.66518, -0.83557, -0.69055, 0.09424, 0, 0, -8.61418, -0.26331, -7.00995, -3.073, -6.26721, -4.68408, -2.41994, -6.95239, -1.01698, -4.57556, 2.11131, -1.9325], "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "baiy5": {"baiy5": [{"vertices": [-22.29343, 23.53328, -4.56009, 13.26667, 20.99529, 28.38483, 56.05455, 5.34048, 100.49918, -4.85974, 130.7213, 2.91015, 177.57281, -37.89616, 220.37329, -26.86664, 235.62436, -23.7113, 169.68431, -44.20692, 127.56589, -18.65154, 99.97331, -20.63667, 47.11427, -12.01403, 17.83987, 22.59999, 9.63913, 3.8005, -19.66389, 16.69661, -28.82672, 28.19998, -28.82672, 28.19998, -28.82672, 28.19998, -14.40497, 15.64486, 1.7507, 10.11126, 23.09884, 27.333, 48.69198, -7.28102, 102.07689, -9.06691, 131.24718, -9.71129, 175.46918, -38.42208]}, {"time": 1.5667, "vertices": [10.91992, -7.80005, 40.55994, -24.95996, 83.27393, 0.30896, 141.87297, -38.20801, 216.15901, -55.25696, 266.6731, -42.27014, 344.98206, -110.47498, 416.52002, -92.03992, 442.01105, -86.76599, 331.797, -121.02295, 261.39905, -78.30896, 215.28003, -81.62695, 126.92993, -67.21484, 77.99988, -9.35999, 64.29291, -40.78198, 15.315, -19.22705, 0, 0, 0, 0, 0, 0, 24.10492, -20.98499, 51.10797, -30.23401, 86.78986, -1.4491, 129.56696, -59.30396, 218.79604, -62.28894, 267.55206, -63.36597, 341.46597, -111.354], "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 1.6667, "vertices": [-71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995, -71.76013, 70.19995]}, {"time": 4, "vertices": [-22.29343, 23.53328, -4.56009, 13.26667, 20.99529, 28.38483, 56.05455, 5.34048, 100.49918, -4.85974, 130.7213, 2.91015, 177.57281, -37.89616, 220.37329, -26.86664, 235.62436, -23.7113, 169.68431, -44.20692, 127.56589, -18.65154, 99.97331, -20.63667, 47.11427, -12.01403, 17.83987, 22.59999, 9.63913, 3.8005, -19.66389, 16.69661, -28.82672, 28.19998, -28.82672, 28.19998, -28.82672, 28.19998, -14.40497, 15.64486, 1.7507, 10.11126, 23.09884, 27.333, 48.69198, -7.28102, 102.07689, -9.06691, 131.24718, -9.71129, 175.46918, -38.42208]}]}, "baiy8": {"baiy8": [{"vertices": [32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001], "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "vertices": [-23.57898, 28.54291, -19.85593, 28.54297, -9.92792, 21.09698, -8.68701, 17.3739, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -13.651, 9.92801, -29.784, 18.61499, -44.67603, 50.88098, -45.91699, 52.12201, -18.61499, 63.29102, -12.41003, 11.16907, -9.92798, 18.61511, -21.09702, 11.16895, -35.98901, 21.09698, -27.30203, 26.06104, -26.06094, 33.50696, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -39.71204, 22.33801, -37.22997, 11.16895, -32.26602, 19.85596, -34.74799, 8.68701, -37.22998, 16.13293], "curve": 0.25, "c3": 0.75}, {"time": 2, "vertices": [32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001], "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "vertices": [-23.57898, 28.54291, -19.85593, 28.54297, -9.92792, 21.09698, -8.68701, 17.3739, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -13.651, 9.92801, -29.784, 18.61499, -44.67603, 50.88098, -45.91699, 52.12201, -18.61499, 63.29102, -12.41003, 11.16907, -9.92798, 18.61511, -21.09702, 11.16895, -35.98901, 21.09698, -27.30203, 26.06104, -26.06094, 33.50696, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -39.71204, 22.33801, -37.22997, 11.16895, -32.26602, 19.85596, -34.74799, 8.68701, -37.22998, 16.13293], "curve": 0.25, "c3": 0.75}, {"time": 4, "vertices": [32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001, 32.5799, -39.82001]}]}, "baiy1": {"baiy1": [{"vertices": [57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396], "curve": 0.25, "c3": 0.75}, {"time": 3.9, "offset": 6, "vertices": [-36.56787, -20.89606, -44.40393, -13.06012, -35.26202, -9.14203, -44.40399, -1.30603, -57.46399, 1.30603, -65.29993, -7.836, -73.1358, -31.34412, -90.11389, -35.26215, -112.31586, -26.12024, -152.80182, -16.97839, -154.10779, -3.91833, -154.10779, -3.91833, -154.10779, -3.91833, -152.80182, -16.97839, -112.31586, -26.12024, -90.11389, -35.26215, -75.74792, -20.89606, -87.50189, -3.91797, -78.35999, 15.672, -53.54602, 10.448, -22.20197, 16.97797, 0, 0, -23.50793, -3.91803, -22.20197, -5.22406, -65.29993, -7.836, -45.70996, 1.30603, -67.91199, 11.75409, -75.74792, -20.89606, -90.11389, -35.26215, -112.31586, -26.12024, -152.80182, -16.97839]}, {"time": 4, "vertices": [57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396, 0, 57.56396]}]}, "baiy2": {"baiy2": [{"vertices": [2.72364, 2.05935, 3.98702, -17.52284, -28.17914, -28.84343, -52.40607, -17.9456, -43.63056, -7.21148, -59.85454, -38.88683, -103.11839, -33.47879, -178.05759, 9.78506, -178.05759, 9.78506, -178.05759, 9.78506, -97.71042, -18.79993, -66.0351, -20.34517, -59.08186, 14.42042, -27.40658, 24.46392, -16.59063, -2.57612, 2.72364, 2.05935, -31.75558, -22.94003, -29.8606, -15.27795, -49.03848, 2.83191, -62.17222, -28.07087, -97.71042, -22.66284], "curve": 0.368, "c2": 0.63, "c3": 0.705}, {"time": 0.5667, "offset": 2, "vertices": [1.37201, -21.26599, -33.56, -33.56, -59.87012, -21.7251, -50.34003, -10.06799, -67.95905, -44.46698, -114.94299, -38.59393, -196.32593, 8.39001, -196.32593, 8.39001, -196.32593, 8.39001, -109.07001, -22.65289, -74.67105, -24.33099, -67.11993, 13.42395, -32.72101, 24.33105, -20.97504, -5.03406, 0, 0, -37.44397, -27.14899, -35.38605, -18.82806, -56.21295, 0.83899, -70.47601, -32.72101, -109.07001, -26.84796], "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "vertices": [34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909, 34.39899, 26.00909], "curve": 0.244, "c3": 0.696, "c4": 0.77}, {"time": 4, "vertices": [2.72364, 2.05935, 3.98702, -17.52284, -28.17914, -28.84343, -52.40607, -17.9456, -43.63056, -7.21148, -59.85454, -38.88683, -103.11839, -33.47879, -178.05759, 9.78506, -178.05759, 9.78506, -178.05759, 9.78506, -97.71042, -18.79993, -66.0351, -20.34517, -59.08186, 14.42042, -27.40658, 24.46392, -16.59063, -2.57612, 2.72364, 2.05935, -31.75558, -22.94003, -29.8606, -15.27795, -49.03848, 2.83191, -62.17222, -28.07087, -97.71042, -22.66284]}]}, "baiy3": {"baiy3": [{"vertices": [84.98116, 66.34428, 76.55911, 56.36307, 88.9404, 51.54639, 70.05842, 35.07068, 56.58234, 32.05861, 46.01048, 18.42504, 31.45402, 1.00064, 0.50815, 3.83624, 0.50815, 3.83624, 0.50815, 3.83624, 30.35423, 7.59937, 37.2121, 18.79164, 36.38679, 38.79052, 65.7308, 44.68759, 75.95754, 52.50804, 66.84912, 62.67267, 76.88948, 67.42319, 82.21063, 71.86156, 91.38119, 71.32211, 91.38119, 71.32211, 76.8895, 63.64705, 70.78227, 61.17099, 77.88095, 52.50804, 71.02013, 39.39827, 45.52286, 36.8671, 45.27727, 18.79164, 30.35423, 4.66656], "curve": 0.353, "c2": 0.41, "c3": 0.757}, {"time": 2.5667, "vertices": [-21.73511, -16.90515, -50.33713, -50.80225, -8.28912, -67.16016, -72.41406, -123.11316, -118.18007, -133.3424, -154.08305, -179.64325, -203.51811, -238.81812, -308.6131, -229.18817, -308.6131, -229.18817, -308.6131, -229.18817, -207.25313, -216.4082, -183.96312, -178.39825, -186.76598, -110.48022, -87.11102, -90.45325, -52.3801, -63.89429, -83.31314, -29.37427, -49.21515, -13.24109, -31.14404, 1.83203, 0, 0, 0, 0, -49.21509, -26.06519, -69.95578, -34.47412, -45.84802, -63.89429, -69.14801, -108.41626, -155.73906, -117.01233, -156.5731, -178.39825, -207.25313, -226.36829]}, {"time": 2.6667, "vertices": [129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813, 129.5188, 101.08813], "curve": 0.264, "c3": 0.618, "c4": 0.43}, {"time": 4, "vertices": [84.98116, 66.34428, 76.55911, 56.36307, 88.9404, 51.54639, 70.05842, 35.07068, 56.58234, 32.05861, 46.01048, 18.42504, 31.45402, 1.00064, 0.50815, 3.83624, 0.50815, 3.83624, 0.50815, 3.83624, 30.35423, 7.59937, 37.2121, 18.79164, 36.38679, 38.79052, 65.7308, 44.68759, 75.95754, 52.50804, 66.84912, 62.67267, 76.88948, 67.42319, 82.21063, 71.86156, 91.38119, 71.32211, 91.38119, 71.32211, 76.8895, 63.64705, 70.78227, 61.17099, 77.88095, 52.50804, 71.02013, 39.39827, 45.52286, 36.8671, 45.27727, 18.79164, 30.35423, 4.66656]}]}, "baiy6": {"baiy6": [{"vertices": [-53.8987, 32.7593, -52.51366, 32.06022, -50.64476, 29.38796, -49.14782, 30.18793, -46.9737, 33.43927, -42.40037, 34.37332, -39.7365, 34.04145, -35.67091, 33.99329, -24.40675, 32.37135, -24.40675, 32.37135, -24.40675, 32.37135, -35.43102, 32.31411, -39.87358, 30.7807, -43.64564, 32.84113, -46.27852, 30.21205, -49.31435, 27.39881, -52.54352, 27.42445, -55.19223, 31.53111, -56.69496, 33.23497, -56.69496, 33.23497, -56.69496, 33.23497, -54.11291, 32.88298, -52.43402, 28.41335, -49.78904, 29.52464, -46.26395, 31.80486, -42.72098, 33.30811, -40.16631, 32.63352, -35.73944, 33.44498], "curve": 0.312, "c2": 0.25, "c3": 0.757}, {"time": 3.2333, "vertices": [27.98798, -4.76099, 41.85107, -11.75812, 60.55707, -38.50507, 75.54004, -30.49811, 97.30099, 2.04492, 143.07593, 11.39392, 169.73886, 8.0722, 210.43176, 7.59009, 323.17578, -8.64398, 323.17578, -8.64398, 323.17578, -8.64398, 212.83282, -9.21692, 168.36682, -24.56494, 130.61188, -3.94196, 104.25916, -30.25665, 73.87317, -58.41467, 41.55212, -58.15802, 15.04095, -17.05402, 0, 0, 0, 0, 0, 0, 25.84399, -3.52301, 42.64813, -48.26001, 69.12201, -37.13702, 104.405, -14.31409, 139.86694, 0.73212, 165.43686, -6.01996, 209.74579, 2.10205], "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "vertices": [-62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401, -62.98804, 36.92401], "curve": 0.289, "c3": 0.628, "c4": 0.38}, {"time": 4, "vertices": [-53.8987, 32.7593, -52.51366, 32.06022, -50.64476, 29.38796, -49.14782, 30.18793, -46.9737, 33.43927, -42.40037, 34.37332, -39.7365, 34.04145, -35.67091, 33.99329, -24.40675, 32.37135, -24.40675, 32.37135, -24.40675, 32.37135, -35.43102, 32.31411, -39.87358, 30.7807, -43.64564, 32.84113, -46.27852, 30.21205, -49.31435, 27.39881, -52.54352, 27.42445, -55.19223, 31.53111, -56.69496, 33.23497, -56.69496, 33.23497, -56.69496, 33.23497, -54.11291, 32.88298, -52.43402, 28.41335, -49.78904, 29.52464, -46.26395, 31.80486, -42.72098, 33.30811, -40.16631, 32.63352, -35.73944, 33.44498]}]}}}}}}