import { _decorator, instantiate, Prefab, sp, tween, UIOpacity, v3, Vec3 } from "cc";
import { GORole } from "./GORole";
import { PlayerBackInfo } from "../FightDefine";
import { JsonMgr } from "../../mgr/JsonMgr";
import RenderSection from "../../../lib/object/RenderSection";
import ResMgr from "../../../lib/common/ResMgr";
import FightManager, { scaleList } from "../manager/FightManager";
import { AnimationSection } from "../section/AnimationSection";

const { ccclass, property } = _decorator;

@ccclass("GOMonster")
export class GOMonster extends GORole {
  public onInitDetail(detail: PlayerBackInfo, renderName: string = ""): void {
    super.onInitDetail(detail);
    this._renderName = renderName;
    const db = JsonMgr.instance.jsonList.c_monsterShow[detail.dbId];
    this._dbScale = this.getSceneScale(detail.sceneId, db);
    this._spineId = Number(db.spineId);
  }

  protected getSceneScale(sceneId: number, db: any): Vec3 {
    switch (sceneId) {
      case 1:
        return v3(db.renderScale2[0], db.renderScale2[1], db.renderScale2[2]);
      case 2:
        return v3(1, 1, 1);
      case 3:
        return v3(db.renderScale8[0], db.renderScale8[1], db.renderScale8[2]);
      case 4:
        return v3(db.renderScale4[0], db.renderScale4[1], db.renderScale4[2]);
      case 5:
        return v3(db.renderScale6[0], db.renderScale6[1], db.renderScale6[2]);
      case 6:
        return v3(db.renderScale9[0], db.renderScale9[1], db.renderScale9[2]);
      case 101:
        return v3(db.renderScale2[0], db.renderScale2[1], db.renderScale2[2]);
      default:
        return v3(1, 1, 1); // 默认缩放比例
    }
  }

  public async createAllSection() {
    await this.createRenderSection();

    const render = this._renderSection.getRender();
    const opacityComp = render.addComponent(UIOpacity);
    opacityComp.opacity = 0;

    await super.createAllSection();
    await this.createHPSection();
  }

  protected async createRenderSection(): Promise<void> {
    return new Promise<void>(async (resolve) => {
      const param = {
        renderName: this._renderName,
        spineId: this._spineId,
        dbScale: this._dbScale,
        render: this._render,
        isBoss: true,
        callBack: resolve,
      };
      this._renderSection = await this.createSection(RenderSection, param);
    });
  }

  public fightStartAni() {
    const render = this._renderSection.getRender();
    const opacityComp = render.getComponent(UIOpacity);
    const path = "resources?prefab/effect/boss_appears";

    ResMgr.loadPrefab(path, (prefab: Prefab) => {
      if (this.isValid == false) {
        return;
      }

      const node = instantiate(prefab);
      this.addChild(node);

      const skt = node.getComponent(sp.Skeleton);
      //const speed = FightManager.instance.speed;
      //const time = 0.5 / scaleList[speed];

      this.getSection(AnimationSection).playAction(1, true);
      skt.setAnimation(0, "animation", false);
      const time = 0.5;
      this._startTween = tween(opacityComp)
        .to(time, { opacity: 255 })
        .call(() => {
          this.getSection(AnimationSection).playAction(1, true);
          this.fightShowTime = true;
          node.destroy();
        })
        .start();
    });
  }
}
