import { instantiate, Node } from "cc";
import { ListAdapter } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { CityLevelViewHolder } from "./CityLevelViewHolder";
import { CityWorkerViewHolder } from "./CityWorkerViewHolder";
import { JsonMgr } from "../../../mgr/JsonMgr";
import { CityModule } from "../../../../module/city/CityModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class CityRewardAdapter extends ListAdapter {
  private datas: any[] = [];
  private _item1: Node;
  private _item2: Node;
  private _viewType: number;

  constructor(item1: Node, item2: Node) {
    super();
    this._item1 = item1;
    this._item2 = item2;
  }
  public setDatas(viewType: number) {
    this.clearData();
    if (viewType == 1) {
      this.initLevelData();
    } else {
      this.datas = Object.keys(JsonMgr.instance.jsonList.c_buildWorkerReward).map(Number);
    }
    this._viewType = viewType;
    log.log("setDatas", this._viewType);
    this.notifyDataSetChanged();
  }

  private initLevelData() {
    this.datas = [];
    // 待领取
    let get_task_list = [];
    // 已领取
    let finished_task_list = [];
    // 不可领取
    let unfinished_task_list = [];

    let config = JsonMgr.instance.jsonList.c_buildLvReward;
    let list = Object.keys(config)
      .map(Number)
      .sort((a, b) => {
        return a - b;
      });

    for (let i = 0; i < list.length; i++) {
      let id = list[i];
      let configBuildLvReward = config[id];
      if (configBuildLvReward.buildId <= CityModule.data.cityTotalLevel) {
        if (CityModule.data.cityAggregateMessage.nextCityLevelRewardIndex > i) {
          finished_task_list.push(configBuildLvReward.id);
        } else {
          get_task_list.push(configBuildLvReward.id);
        }
      } else {
        unfinished_task_list.push(configBuildLvReward.id);
      }
    }
    finished_task_list.sort((a, b) => {
      return a - b;
    });
    get_task_list.sort((a, b) => {
      return a - b;
    });

    this.datas = [...get_task_list, ...unfinished_task_list, ...finished_task_list];
  }

  getViewType(position: number): number {
    // log.log("getViewType", this._viewType);
    return this._viewType;
  }
  onCreateView(viewType: number): Node {
    let item;
    if (viewType == 1) {
      item = instantiate(this._item1);
      item.getComponent(CityLevelViewHolder);
    } else {
      item = instantiate(this._item2);
      item.getComponent(CityWorkerViewHolder);
    }
    // log.log(`onCreateView  ${viewType}`);
    // log.log(item);
    return item;
  }
  onBindData(node: Node, position: number): void {
    // log.log(`updateView ${position}`);
    // log.log(node);
    if (this._viewType == 1) {
      node.getComponent(CityLevelViewHolder).updateData(this, this.datas[position]);
    } else {
      node.getComponent(CityWorkerViewHolder).updateData(this.datas[position]);
    }
  }
  getCount(): number {
    return this.datas.length;
  }
}
