import { _decorator } from "cc";
import { StartUp } from "../../../lib/StartUp";
import { ObjectManager } from "../manager/ObjectManager";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import GamePlay from "./GamePlay";
import { PlayerBackInfo } from "../FightDefine";
import FightManager from "../manager/FightManager";
import TickerMgr from "../../../lib/ticker/TickerMgr";
import { FightModule } from "../../../module/fight/src/FightModule";

const { ccclass, property } = _decorator;

@ccclass("GPHuntBoss")
export class GPHuntBoss extends GamePlay {
  public play() {
    MsgMgr.on(MsgEnum.ON_FIGHT_SKIP, this.fightSkip, this);
    MsgMgr.on(MsgEnum.ON_FIGHT_SPEED, this.upFightSpeed, this);
    MsgMgr.emit(MsgEnum.ON_FIGHT_SPEED);
    this._fightData = FightModule.instance.fightData;
    this._posMap = FightModule.instance.posMap;
    this._roundIndex = 0;
    this.initPlay();
  }

  public exit() {
    MsgMgr.off(MsgEnum.ON_FIGHT_SPEED, this.upFightSpeed, this);
    MsgMgr.off(MsgEnum.ON_FIGHT_SKIP, this.fightSkip, this);
  }

  private async initPlay() {
    let queueList = this.newQueueList();
    await this.loadRolObject(queueList);
    await new Promise((res) => TickerMgr.setTimeout(0.2, res));
    await this.startGame();

    await this.roundFight(this._fightData.e);
    await new Promise((res) => TickerMgr.setTimeout(0.5, res));
    if (FightManager.instance.fightOver == false) {
      MsgMgr.emit(MsgEnum.ON_FIGHT_END);
    }
  }

  protected async loadRolObject(queueList: Array<PlayerBackInfo>) {
    for (let i = 0; i < queueList.length; i++) {
      let role = await FightManager.instance.getSection(ObjectManager).callObject(queueList[i]);
      this._allRole.set(queueList[i].dir, role);
    }
  }
}
