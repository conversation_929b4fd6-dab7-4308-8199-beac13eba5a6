{"skeleton": {"hash": "aCTdbK65RyWEMpouY4YnsNQTbik=", "spine": "3.8.75", "x": -454.98, "y": -752, "width": 830.98, "height": 1502, "images": "./images/", "audio": "D:/UI/G_关卡场景/冥族场景"}, "bones": [{"name": "root"}, {"name": "all", "parent": "root"}, {"name": "somethingrock", "parent": "all", "length": 100, "y": -799.69, "color": "ff0000ff"}, {"name": "cloud", "parent": "root", "length": 301.08, "y": -932.19, "color": "a9fcffff"}, {"name": "cloud01", "parent": "cloud", "rotation": 177.66, "x": 29.28, "y": 1298.57, "color": "a9fcffff"}, {"name": "cloud02", "parent": "cloud", "rotation": 2.66, "x": 66.9, "y": 1409.98, "color": "a9fcffff"}, {"name": "cloud01_1", "parent": "cloud", "rotation": 177.66, "x": 29.28, "y": 1298.57, "color": "a9fcffff"}, {"name": "cloud2", "parent": "cloud", "rotation": 2.66, "x": 66.9, "y": 1409.98, "color": "a9fcffff"}, {"name": "cloud03", "parent": "cloud", "x": -22.55, "y": 1068.68, "color": "a9fcffff"}, {"name": "cloud3", "parent": "cloud", "x": -22.55, "y": 1068.68, "color": "a9fcffff"}, {"name": "cloud4", "parent": "cloud", "x": 64.21, "y": 1143.75, "color": "a9fcffff"}, {"name": "cloud5", "parent": "cloud", "x": 64.21, "y": 1143.75, "color": "a9fcffff"}, {"name": "cloud09", "parent": "cloud", "x": 36.97, "y": 1100.33}, {"name": "cloud9", "parent": "cloud", "x": 36.97, "y": 1100.33}, {"name": "cloud6", "parent": "cloud", "x": -45.58, "y": 920.74, "color": "a9fcffff"}, {"name": "cloud7", "parent": "cloud", "x": -91.09, "y": 855.09, "color": "a9fcffff"}, {"name": "cloud8", "parent": "cloud", "x": 116.3, "y": 885.68, "color": "a9fcffff"}, {"name": "cloud10", "parent": "cloud", "x": -91.09, "y": 855.09, "color": "a9fcffff"}, {"name": "cloud11", "parent": "cloud", "x": -45.58, "y": 920.74, "color": "a9fcffff"}, {"name": "cloud12", "parent": "cloud", "x": 116.3, "y": 885.68, "color": "a9fcffff"}, {"name": "all2", "parent": "all", "length": 72.11, "rotation": 2.58, "x": 142.91, "y": 53.53}, {"name": "all3", "parent": "all2", "length": 35.17, "rotation": 92.44, "x": -1.39, "y": 3.7}, {"name": "all4", "parent": "all3", "length": 34.27, "rotation": -4.38, "x": 35.17}, {"name": "all5", "parent": "all3", "x": 80.13, "y": -68.88}, {"name": "all6", "parent": "all3", "x": 90.92, "y": 53.84}, {"name": "all7", "parent": "all5", "length": 43.1, "rotation": -136.4, "x": -18.09, "y": -12.32}, {"name": "all8", "parent": "all7", "length": 43.56, "rotation": -40.49, "x": 43.1}, {"name": "all9", "parent": "all8", "length": 34.37, "rotation": -21.08, "x": 43.56}, {"name": "all10", "parent": "all6", "length": 51.12, "rotation": 135.17, "x": -5.8, "y": 8.75}, {"name": "all11", "parent": "all10", "length": 46.66, "rotation": 36.49, "x": 51.12}, {"name": "all12", "parent": "all11", "length": 33.22, "rotation": 7.96, "x": 46.66}, {"name": "all15", "parent": "all", "length": 65.44, "rotation": -1.68, "x": 137.68, "y": -21.97}, {"name": "all13", "parent": "all", "length": 52.12, "rotation": -0.85, "x": -308.96, "y": 149.54}, {"name": "all14", "parent": "all13", "length": 9.92, "rotation": 90.85, "x": -93.19, "y": -19}, {"name": "all16", "parent": "all14", "length": 9.92, "x": 9.92}, {"name": "all17", "parent": "all16", "length": 9.92, "x": 9.92}, {"name": "all18", "parent": "all17", "length": 9.92, "x": 9.92}, {"name": "all19", "parent": "all18", "length": 9.92, "x": 9.92}, {"name": "all20", "parent": "all19", "length": 9.92, "x": 9.92}, {"name": "all21", "parent": "all20", "length": 9.92, "x": 9.92}, {"name": "all22", "parent": "all21", "length": 9.92, "x": 9.92}, {"name": "all23", "parent": "all22", "length": 9.92, "x": 9.92}, {"name": "all24", "parent": "all23", "length": 9.92, "x": 9.92}, {"name": "all25", "parent": "all24", "length": 9.92, "x": 9.92}, {"name": "all26", "parent": "all25", "length": 9.92, "x": 9.92}, {"name": "all27", "parent": "all26", "length": 9.92, "x": 9.92}, {"name": "all28", "parent": "all27", "length": 9.92, "x": 9.92}, {"name": "all29", "parent": "all28", "length": 9.92, "x": 9.92}, {"name": "all30", "parent": "all29", "length": 9.92, "x": 9.92}, {"name": "all31", "parent": "all30", "length": 9.92, "x": 9.92}, {"name": "all32", "parent": "all31", "length": 9.92, "x": 9.92}, {"name": "all33", "parent": "all32", "length": 9.92, "x": 9.92}, {"name": "all34", "parent": "all33", "length": 9.92, "x": 9.92}, {"name": "all35", "parent": "all13", "x": -9.48, "y": 121.47, "color": "ff0000ff"}, {"name": "all36", "parent": "all13", "x": -47.38, "y": 92.68, "color": "ff0000ff"}, {"name": "all37", "parent": "all13", "x": 4.43, "y": 86.24, "color": "ff0000ff"}, {"name": "all38", "parent": "all13", "x": 3.43, "y": 28.54, "color": "ff0000ff"}, {"name": "all39", "parent": "all13", "x": 51.22, "y": 56.85, "color": "ff0000ff"}, {"name": "all40", "parent": "all23", "length": 19.46, "rotation": 64.61, "x": -0.32, "y": 14.55, "transform": "noRotationOrReflection"}, {"name": "all41", "parent": "all40", "length": 18.82, "rotation": -26.33, "x": 19.46}, {"name": "all42", "parent": "all41", "length": 21.27, "rotation": 56.62, "x": 18.82}, {"name": "all43", "parent": "all42", "length": 21.97, "rotation": 25.44, "x": 21.27}, {"name": "all44", "parent": "all23", "length": 6.29, "rotation": -105.04, "x": 0.4, "y": -0.87, "color": "ff0000ff"}, {"name": "all45", "parent": "all44", "length": 11.37, "rotation": 91.34, "x": 6.29, "color": "ff0000ff"}, {"name": "all46", "parent": "all45", "length": 6.86, "rotation": -10.53, "x": 11.37, "color": "ff0000ff"}, {"name": "all47", "parent": "all22", "length": 7.25, "rotation": -119.72, "x": 1.62, "y": 2.46, "color": "ff0000ff"}, {"name": "all48", "parent": "all47", "length": 12.49, "rotation": 90.4, "x": 7.18, "y": -0.04, "color": "ff0000ff"}, {"name": "all49", "parent": "all48", "length": 7.46, "rotation": -35.26, "x": 12.49, "color": "ff0000ff"}, {"name": "all50", "parent": "all25", "length": 6.68, "rotation": -122.15, "x": -1.05, "y": -2.09, "color": "ff0000ff"}, {"name": "all51", "parent": "all50", "length": 11.26, "rotation": 105.81, "x": 6.68, "color": "ff0000ff"}, {"name": "all52", "parent": "all51", "length": 7.78, "rotation": -17.71, "x": 11.26, "color": "ff0000ff"}, {"name": "all53", "parent": "all26", "length": 7.31, "rotation": -176.97, "x": 0.45, "y": -3.67, "color": "ff0000ff"}, {"name": "all54", "parent": "all53", "length": 13.17, "rotation": 96.4, "x": 7.26, "y": 0.06, "color": "ff0000ff"}, {"name": "all55", "parent": "all54", "length": 8.01, "rotation": -25.53, "x": 13.11, "y": -0.04, "color": "ff0000ff"}, {"name": "all56", "parent": "all23", "length": 19.46, "rotation": 64.61, "x": -0.35, "y": 8.82, "transform": "noRotationOrReflection"}, {"name": "all57", "parent": "all56", "length": 18.82, "rotation": -26.33, "x": 19.46}, {"name": "all58", "parent": "all57", "length": 21.27, "rotation": 56.62, "x": 18.82}, {"name": "all59", "parent": "all58", "length": 21.97, "rotation": 25.44, "x": 21.27}, {"name": "bone", "parent": "all", "length": 26.36, "rotation": -179.4, "x": -435.89, "y": 406.14, "color": "ffeb00ff"}, {"name": "bone2", "parent": "bone", "length": 11.81, "rotation": -83.89, "x": 4.36, "y": -5.01, "color": "ffeb00ff"}, {"name": "bone3", "parent": "bone2", "length": 19.07, "rotation": 20.41, "x": 11.81, "color": "ffeb00ff"}, {"name": "bone4", "parent": "bone", "length": 10.8, "rotation": -155.67, "x": -5.43, "y": -4.36, "color": "ffeb00ff"}, {"name": "bone5", "parent": "bone4", "length": 14.5, "rotation": -22.75, "x": 10.8, "color": "ffeb00ff"}, {"name": "bone6", "parent": "bone", "x": -3.31, "y": 0.31, "color": "ffeb00ff"}], "slots": [{"name": "mingzu_bg01", "bone": "somethingrock", "attachment": "mingzu_bg01"}, {"name": "mingzu_bg02", "bone": "cloud6", "attachment": "mingzu_bg02"}, {"name": "mingzu_bg2", "bone": "cloud11", "attachment": "mingzu_bg02"}, {"name": "mingzu_bg03", "bone": "somethingrock", "attachment": "mingzu_bg03"}, {"name": "mingzu_bg04", "bone": "cloud7", "attachment": "mingzu_bg04"}, {"name": "mingzu_bg4", "bone": "cloud10", "attachment": "mingzu_bg04"}, {"name": "mingzu_bg05", "bone": "cloud8", "attachment": "mingzu_bg05"}, {"name": "mingzu_bg5", "bone": "cloud12", "attachment": "mingzu_bg05"}, {"name": "mingzu_bg06", "bone": "cloud09", "attachment": "mingzu_bg06"}, {"name": "mingzu_bg6", "bone": "cloud9", "attachment": "mingzu_bg06"}, {"name": "mingzu_bg07", "bone": "somethingrock", "attachment": "mingzu_bg07"}, {"name": "mingzu_bg08", "bone": "cloud01", "attachment": "mingzu_bg08"}, {"name": "mingzu_bg8", "bone": "cloud01_1", "attachment": "mingzu_bg08"}, {"name": "mingzu_bg09", "bone": "cloud03", "attachment": "mingzu_bg09"}, {"name": "mingzu_bg9", "bone": "cloud3", "attachment": "mingzu_bg09"}, {"name": "mingzu_bg010", "bone": "cloud02", "attachment": "mingzu_bg010"}, {"name": "mingzu_bg10", "bone": "cloud2", "attachment": "mingzu_bg010"}, {"name": "mingzu_bg011", "bone": "cloud4", "attachment": "mingzu_bg011"}, {"name": "mingzu_bg11", "bone": "cloud5", "attachment": "mingzu_bg011"}, {"name": "mingzu_bg012", "bone": "all", "attachment": "mingzu_bg012"}, {"name": "mingzu_bg013", "bone": "somethingrock", "attachment": "mingzu_bg013"}, {"name": "mingzu_bg014", "bone": "somethingrock", "attachment": "mingzu_bg014"}, {"name": "body", "bone": "all", "attachment": "body"}, {"name": "eye", "bone": "all4"}, {"name": "chiblong", "bone": "all40", "attachment": "chiblong"}, {"name": "longbody", "bone": "all", "attachment": "longbody"}, {"name": "chiblong2", "bone": "all56", "attachment": "chiblong"}, {"name": "hui2", "bone": "cloud", "attachment": "hui2"}, {"name": "loong_zhua", "bone": "all13", "attachment": "loong_zhua"}, {"name": "loong_zhua2", "bone": "all13", "attachment": "loong_zhua"}, {"name": "hui1", "bone": "cloud", "color": "ffffffcc", "attachment": "hui1"}, {"name": "loong", "bone": "all13", "attachment": "loong"}, {"name": "loong_zhua4", "bone": "all13", "attachment": "loong_zhua"}, {"name": "loong_zhua3", "bone": "all13", "attachment": "loong_zhua"}], "path": [{"name": "loong", "bones": ["all14", "all34", "all33", "all32", "all31", "all30", "all29", "all28", "all27", "all26", "all25", "all24", "all23", "all22", "all21", "all20", "all19", "all18", "all17", "all16"], "target": "loong", "spacingMode": "percent", "rotateMode": "chainScale", "position": 0.1271, "spacing": 0.032}], "skins": [{"name": "default", "attachments": {"loong_zhua4": {"loong_zhua": {"type": "mesh", "uvs": [0.44651, 0, 0.62967, 0, 0.82756, 0.01606, 0.98335, 0.05446, 1, 0.22246, 0.9223, 0.32326, 0.75388, 0.31046, 0.6023, 0.26086, 0.58335, 0.38886, 0.58335, 0.54406, 0.53493, 0.64005, 0.61914, 0.74725, 0.58756, 0.87045, 0.48862, 0.99045, 0.22967, 1, 0.00231, 0.95205, 0, 0.77605, 0, 0.58566, 0.12862, 0.48166, 0.32441, 0.47046, 0.35178, 0.34566, 0.33704, 0.16006, 0.3623, 0.04326, 0.80038, 0.14755, 0.58603, 0.09581, 0.47156, 0.155, 0.46774, 0.30881, 0.45603, 0.43889, 0.41653, 0.55069, 0.31235, 0.72668, 0.14919, 0.81236], "triangles": [24, 0, 1, 23, 1, 2, 23, 2, 3, 24, 1, 23, 25, 0, 24, 22, 0, 25, 21, 22, 25, 23, 3, 4, 7, 24, 23, 25, 24, 7, 26, 21, 25, 26, 25, 7, 6, 7, 23, 5, 6, 23, 4, 5, 23, 20, 21, 26, 8, 26, 7, 27, 20, 26, 27, 26, 8, 19, 20, 27, 27, 8, 9, 28, 19, 27, 28, 27, 9, 10, 28, 9, 29, 18, 19, 29, 19, 28, 29, 28, 10, 17, 18, 29, 30, 16, 17, 29, 30, 17, 11, 29, 10, 12, 29, 11, 15, 16, 30, 13, 29, 12, 14, 30, 29, 13, 14, 29, 15, 30, 14], "vertices": [-11.04, 78.37, -7.88, 76.92, -4.63, 74.99, -2.34, 72.88, -3.8, 68.93, -6.2, 67.26, -8.97, 68.88, -11.07, 71.21, -12.73, 68.45, -14.35, 64.92, -16.19, 63.13, -15.85, 60.02, -17.68, 57.47, -20.64, 55.53, -25.21, 57.36, -28.64, 60.26, -26.84, 64.27, -24.86, 68.6, -21.55, 69.94, -18.05, 68.65, -16.28, 71.27, -14.6, 75.6, -12.95, 78.06, -6.47, 72.22, -9.63, 75.09, -12.23, 74.65, -13.9, 71.19, -15.45, 68.32, -17.3, 66.1, -20.93, 62.92, -24.64, 62.27], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44, 8, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60], "width": 19, "height": 25}}, "loong_zhua": {"loong_zhua": {"type": "mesh", "uvs": [0.45476, 0, 0.6916, 0, 0.94739, 0.02922, 1, 0.17322, 0.96634, 0.30762, 0.79581, 0.32442, 0.6095, 0.26682, 0.57792, 0.39402, 0.55581, 0.54762, 0.54002, 0.65562, 0.60318, 0.76842, 0.57792, 0.89802, 0.41687, 1, 0.1895, 0.97962, 0, 0.93882, 0, 0.74682, 0.00318, 0.53562, 0.17371, 0.47322, 0.31897, 0.45162, 0.34108, 0.32202, 0.34739, 0.13962, 0.35687, 0.02922, 0.84175, 0.15438, 0.67699, 0.13652, 0.51495, 0.1296, 0.47234, 0.25869, 0.46314, 0.36881, 0.43239, 0.51093, 0.37235, 0.64574, 0.25809, 0.78364], "triangles": [24, 0, 1, 21, 0, 24, 23, 24, 1, 22, 23, 1, 20, 21, 24, 2, 22, 1, 22, 2, 3, 25, 20, 24, 6, 24, 23, 25, 24, 6, 4, 22, 3, 19, 20, 25, 5, 23, 22, 5, 22, 4, 6, 23, 5, 26, 19, 25, 26, 25, 6, 7, 26, 6, 18, 19, 26, 27, 18, 26, 27, 26, 7, 8, 27, 7, 28, 18, 27, 28, 27, 8, 9, 28, 8, 17, 28, 16, 28, 17, 18, 29, 16, 28, 15, 16, 29, 9, 10, 28, 14, 15, 29, 10, 29, 28, 13, 14, 29, 11, 29, 10, 12, 29, 11, 13, 29, 12], "vertices": [-8.67, 70.45, -4.25, 71.32, 0.66, 71.54, 2.33, 68.2, 2.35, 64.78, -0.74, 63.75, -4.49, 64.48, -4.47, 61.24, -4.14, 57.39, -3.91, 54.68, -2.19, 52.15, -2.03, 48.88, -4.54, 45.78, -8.88, 45.45, -12.61, 45.75, -13.54, 50.46, -14.5, 55.66, -11.62, 57.81, -9.02, 58.88, -9.23, 62.14, -10, 66.63, -10.35, 69.38, -0.71, 68.09, -3.87, 67.92, -6.92, 67.49, -7.09, 64.17, -6.73, 61.44, -6.62, 57.84, -7.08, 54.31, -8.55, 50.51], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 0, 42, 6, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58], "width": 19, "height": 25}}, "body": {"body": {"type": "mesh", "uvs": [0.39939, 0.10093, 0.40539, 0.05018, 0.41995, 0, 0.44051, 0, 0.46193, 0.05203, 0.47735, 0.10278, 0.55187, 0.09632, 0.63753, 0.10647, 0.71377, 0.15169, 0.75126, 0.12639, 0.77621, 0.07475, 0.80181, 0.09527, 0.82808, 0.15822, 0.81889, 0.22612, 0.85436, 0.2565, 0.87405, 0.31812, 0.89065, 0.37974, 0.90542, 0.40824, 0.93433, 0.4354, 0.97431, 0.501, 0.99164, 0.5928, 0.99367, 0.6772, 1, 0.74997, 0.99569, 0.80454, 0.95449, 0.84602, 0.88559, 0.86203, 0.8248, 0.84383, 0.79305, 0.80163, 0.76198, 0.74779, 0.75725, 0.67939, 0.78562, 0.64082, 0.77009, 0.60735, 0.75995, 0.56733, 0.76198, 0.52949, 0.72821, 0.50839, 0.71094, 0.54714, 0.67997, 0.58731, 0.66563, 0.63797, 0.66219, 0.68924, 0.65562, 0.74288, 0.64741, 0.79475, 0.64741, 0.83837, 0.67477, 0.87079, 0.68736, 0.89849, 0.72894, 0.9315, 0.76724, 0.95979, 0.76998, 0.97924, 0.72128, 0.99457, 0.65015, 1, 0.58285, 0.99634, 0.52594, 0.98926, 0.49951, 0.95454, 0.50773, 0.91269, 0.5167, 0.88774, 0.50549, 0.85393, 0.48009, 0.84669, 0.46589, 0.8491, 0.45169, 0.87647, 0.48023, 0.90991, 0.49008, 0.9672, 0.45002, 0.98418, 0.40406, 0.99196, 0.34365, 0.9962, 0.28522, 0.99055, 0.26355, 0.97852, 0.2734, 0.9474, 0.30229, 0.92689, 0.33052, 0.90425, 0.32198, 0.86252, 0.31936, 0.80098, 0.33971, 0.75077, 0.36138, 0.70408, 0.34562, 0.65952, 0.34103, 0.60081, 0.3017, 0.55076, 0.28008, 0.50495, 0.27215, 0.4801, 0.25269, 0.5065, 0.23828, 0.52048, 0.23972, 0.5725, 0.22675, 0.6152, 0.21017, 0.64315, 0.23179, 0.6711, 0.2426, 0.71536, 0.23323, 0.75495, 0.20354, 0.80406, 0.159, 0.85888, 0.11022, 0.86345, 0.04872, 0.84518, 0.00737, 0.81319, 0, 0.74009, 0, 0.67041, 0.01267, 0.59159, 0.02751, 0.51963, 0.07947, 0.46252, 0.09856, 0.4054, 0.12825, 0.35514, 0.13143, 0.29689, 0.16574, 0.24051, 0.16799, 0.14724, 0.19836, 0.07577, 0.22872, 0.13028, 0.27257, 0.16056, 0.31671, 0.13125, 0.36655, 0.11238, 0.68545, 0.39408, 0.70187, 0.46017, 0.27757, 0.36615, 0.26893, 0.42573, 0.48065, 0.52626, 0.54027, 0.51044, 0.59558, 0.45738, 0.61718, 0.37639, 0.61113, 0.26655, 0.58866, 0.24327, 0.55669, 0.23955, 0.49793, 0.19859, 0.48151, 0.13994, 0.39423, 0.14274, 0.38991, 0.21069, 0.35966, 0.23024, 0.33806, 0.2712, 0.34497, 0.33357, 0.3346, 0.3978, 0.34929, 0.46855, 0.39509, 0.51323, 0.45731, 0.52813, 0.46958, 0.50185, 0.45668, 0.44697, 0.45192, 0.39063, 0.44717, 0.31819, 0.44717, 0.24648, 0.44173, 0.19672, 0.43494, 0.14038, 0.42815, 0.08697, 0.42815, 0.04014, 0.47329, 0.58601, 0.54767, 0.59817, 0.62338, 0.58171, 0.67984, 0.53879, 0.43277, 0.59817, 0.36902, 0.59101, 0.33116, 0.54809, 0.47196, 0.66019, 0.47196, 0.72887, 0.47727, 0.79755, 0.57557, 0.6516, 0.58022, 0.72243, 0.5749, 0.78754, 0.58021, 0.83905, 0.59283, 0.88698, 0.63468, 0.94064, 0.69644, 0.9571, 0.56095, 0.93921, 0.40835, 0.65875, 0.40835, 0.72028, 0.40503, 0.77823, 0.38776, 0.8276, 0.40303, 0.89986, 0.3758, 0.94422, 0.31935, 0.96282, 0.4389, 0.94636, 0.5607, 0.16165, 0.64445, 0.17947, 0.73544, 0.25633, 0.78817, 0.34209, 0.83056, 0.43565, 0.87812, 0.51807, 0.89467, 0.59938, 0.90501, 0.69406, 0.88019, 0.78316, 0.3273, 0.18351, 0.25182, 0.23586, 0.20426, 0.31382, 0.17944, 0.39179, 0.14946, 0.46642, 0.12464, 0.54661, 0.09776, 0.64352, 0.09983, 0.74599, 0.15566, 0.76269, 0.49926, 0.24606, 0.53849, 0.31783, 0.59452, 0.30173, 0.54658, 0.38021, 0.53724, 0.44393, 0.404, 0.24807, 0.38407, 0.32118, 0.38283, 0.39295, 0.40337, 0.45734], "triangles": [49, 151, 48, 48, 152, 47, 48, 151, 152, 50, 153, 49, 49, 153, 151, 63, 160, 62, 62, 159, 61, 62, 160, 159, 47, 45, 46, 47, 44, 45, 47, 152, 44, 61, 161, 60, 61, 159, 161, 64, 65, 63, 63, 65, 160, 50, 51, 153, 60, 161, 59, 65, 66, 160, 160, 67, 159, 160, 66, 67, 151, 43, 152, 152, 43, 44, 59, 161, 58, 51, 52, 153, 159, 158, 161, 161, 57, 58, 161, 158, 57, 159, 67, 158, 153, 150, 151, 151, 42, 43, 42, 150, 41, 42, 151, 150, 52, 53, 153, 153, 53, 150, 67, 68, 158, 68, 157, 158, 158, 157, 57, 53, 149, 150, 53, 54, 149, 150, 149, 41, 57, 157, 56, 68, 69, 157, 149, 54, 148, 157, 156, 56, 56, 145, 55, 56, 156, 145, 148, 54, 145, 54, 55, 145, 41, 149, 40, 149, 148, 40, 69, 70, 157, 26, 170, 25, 25, 170, 24, 24, 170, 23, 26, 27, 170, 23, 170, 22, 27, 28, 170, 170, 169, 22, 170, 28, 169, 169, 21, 22, 28, 30, 169, 28, 29, 30, 30, 168, 169, 169, 168, 21, 168, 20, 21, 30, 31, 168, 31, 32, 167, 31, 167, 168, 167, 32, 33, 168, 19, 20, 168, 167, 19, 167, 18, 19, 35, 139, 34, 139, 111, 106, 139, 106, 34, 111, 105, 106, 167, 33, 166, 33, 34, 166, 167, 17, 18, 167, 166, 17, 34, 106, 166, 106, 165, 166, 106, 105, 165, 166, 16, 17, 16, 165, 15, 16, 166, 165, 165, 105, 164, 165, 14, 15, 165, 13, 14, 88, 178, 87, 87, 179, 86, 87, 178, 179, 86, 179, 85, 88, 89, 178, 89, 90, 178, 85, 179, 84, 84, 179, 83, 82, 83, 179, 178, 91, 177, 82, 179, 81, 179, 178, 177, 178, 90, 91, 81, 179, 177, 91, 92, 177, 177, 176, 81, 177, 92, 176, 81, 176, 80, 80, 176, 79, 92, 93, 176, 176, 78, 79, 93, 94, 176, 176, 175, 78, 176, 94, 175, 78, 175, 77, 77, 175, 108, 77, 108, 76, 108, 175, 174, 75, 76, 124, 76, 108, 124, 94, 95, 175, 175, 95, 174, 108, 107, 123, 108, 174, 107, 95, 96, 174, 174, 173, 107, 174, 96, 173, 173, 172, 107, 96, 97, 173, 97, 98, 173, 173, 98, 172, 123, 107, 122, 107, 121, 122, 107, 172, 121, 122, 121, 186, 186, 121, 185, 185, 121, 120, 172, 171, 121, 121, 171, 120, 120, 119, 185, 185, 119, 132, 132, 118, 133, 118, 132, 119, 98, 99, 172, 99, 101, 172, 172, 102, 171, 172, 101, 102, 120, 171, 119, 119, 171, 118, 102, 103, 171, 171, 104, 118, 171, 103, 104, 99, 100, 101, 118, 0, 133, 118, 104, 0, 0, 134, 133, 133, 134, 5, 134, 4, 5, 0, 1, 134, 134, 135, 4, 134, 1, 135, 135, 3, 4, 1, 2, 135, 135, 2, 3, 182, 113, 112, 164, 105, 113, 105, 112, 113, 165, 164, 13, 115, 114, 182, 182, 114, 113, 113, 163, 164, 113, 114, 163, 163, 8, 164, 13, 8, 9, 13, 164, 8, 163, 114, 162, 114, 115, 162, 13, 9, 12, 162, 7, 163, 163, 7, 8, 162, 6, 7, 9, 11, 12, 9, 10, 11, 127, 184, 110, 105, 111, 112, 127, 125, 188, 127, 188, 128, 125, 124, 188, 127, 128, 184, 108, 123, 124, 124, 187, 188, 124, 123, 187, 184, 183, 111, 111, 183, 112, 188, 187, 129, 188, 129, 128, 129, 187, 186, 128, 129, 184, 184, 129, 183, 123, 122, 187, 186, 130, 129, 187, 122, 186, 129, 181, 183, 129, 130, 181, 183, 182, 112, 183, 181, 182, 186, 185, 130, 130, 180, 181, 185, 131, 130, 130, 131, 180, 181, 115, 182, 181, 180, 115, 185, 132, 131, 131, 116, 180, 131, 132, 116, 180, 116, 115, 115, 116, 162, 132, 117, 116, 116, 117, 162, 132, 133, 117, 117, 6, 162, 133, 5, 117, 117, 5, 6, 110, 184, 111, 157, 70, 156, 156, 144, 145, 145, 144, 148, 40, 148, 39, 148, 147, 39, 148, 144, 147, 156, 70, 155, 156, 155, 144, 155, 70, 71, 39, 147, 38, 155, 143, 144, 144, 143, 147, 143, 146, 147, 147, 146, 38, 155, 154, 143, 155, 71, 154, 71, 72, 154, 38, 146, 37, 154, 140, 143, 143, 137, 146, 143, 136, 137, 143, 140, 136, 72, 141, 154, 72, 73, 141, 154, 141, 140, 146, 138, 37, 146, 137, 138, 37, 138, 36, 74, 142, 73, 73, 142, 141, 136, 109, 137, 109, 110, 137, 137, 110, 138, 141, 125, 140, 140, 126, 136, 140, 125, 126, 141, 142, 125, 138, 139, 36, 36, 139, 35, 136, 126, 109, 110, 111, 138, 138, 111, 139, 74, 75, 142, 142, 124, 125, 142, 75, 124, 126, 125, 127, 126, 127, 109, 109, 127, 110], "vertices": [3, 22, 97.21, 20.34, 0.35817, 23, 53.52, 81.74, 0.14599, 24, 42.73, -40.98, 0.49583, 3, 22, 109.67, 18.61, 0.37319, 23, 65.81, 79.07, 0.16592, 24, 55.02, -43.66, 0.46088, 3, 22, 121.97, 14.62, 0.37397, 23, 77.77, 74.14, 0.16592, 24, 66.98, -48.59, 0.46011, 3, 22, 121.91, 9.17, 0.37404, 23, 77.3, 68.71, 0.1661, 24, 66.51, -54.01, 0.45986, 3, 22, 109.05, 3.64, 0.37686, 23, 64.05, 64.18, 0.17431, 24, 53.26, -58.55, 0.44882, 3, 22, 96.52, -0.31, 0.39338, 23, 51.25, 61.2, 0.22931, 24, 40.47, -61.52, 0.37731, 3, 22, 97.89, -20.07, 0.3682, 23, 51.11, 41.39, 0.42881, 24, 40.32, -81.33, 0.20299, 3, 22, 95.14, -42.74, 0.2608, 23, 46.63, 19, 0.64314, 24, 35.84, -103.73, 0.09606, 3, 22, 83.79, -62.82, 0.08749, 23, 33.78, -0.15, 0.88687, 24, 22.99, -122.88, 0.02564, 4, 22, 89.9, -72.82, 0.01627, 23, 39.11, -10.59, 0.9778, 24, 28.32, -133.32, 0.00508, 25, -42.62, 38.19, 0.00086, 1, 23, 51.19, -18.29, 1, 2, 23, 45.57, -24.61, 0.99974, 25, -37.63, 52.79, 0.00026, 2, 23, 29.53, -30.19, 0.96408, 25, -22.17, 45.78, 0.03592, 2, 23, 13.1, -26.3, 0.7364, 25, -12.95, 31.63, 0.2636, 2, 23, 4.84, -35.01, 0.42391, 25, -0.96, 32.24, 0.57609, 2, 23, -10.72, -38.88, 0.12661, 25, 12.98, 24.31, 0.87339, 2, 23, -26.21, -41.94, 0.00712, 25, 26.3, 15.85, 0.99288, 2, 25, 33.87, 13.17, 0.99075, 26, -15.57, 4.02, 0.00925, 2, 25, 44.04, 13.22, 0.61154, 26, -7.87, 10.66, 0.38846, 2, 25, 62.65, 8.12, 0.00394, 26, 9.6, 18.87, 0.99606, 2, 26, 32.6, 20.22, 0.92911, 27, -17.49, 14.93, 0.07089, 2, 26, 53.24, 17.82, 0.23846, 27, 2.62, 20.11, 0.76154, 2, 26, 71.19, 16.95, 0.00237, 27, 19.69, 25.75, 0.99763, 1, 27, 33.03, 27.65, 1, 1, 27, 45.42, 19.29, 1, 1, 27, 53.35, 2.38, 1, 2, 26, 87.49, -32.28, 0.00148, 27, 52.6, -14.32, 0.99852, 3, 25, 75.5, -79.13, 6e-05, 26, 76.02, -39.14, 0.02354, 27, 44.36, -24.85, 0.97639, 4, 21, -33.28, -68.92, 0, 25, 60.56, -74.63, 0.00531, 26, 61.74, -45.42, 0.09453, 27, 33.3, -35.84, 0.90016, 4, 21, -16.41, -69.15, 0.00106, 25, 48.5, -62.83, 0.0289, 26, 44.91, -44.28, 0.2283, 27, 17.18, -40.83, 0.74175, 5, 21, -7.62, -77.47, 0.00544, 22, -36.75, -80.51, 0.00018, 25, 47.87, -50.75, 0.08847, 26, 36.58, -35.49, 0.41063, 27, 6.25, -35.63, 0.49529, 5, 21, 0.94, -74.09, 0.01574, 22, -28.47, -76.48, 0.00186, 25, 39.34, -47.29, 0.20096, 26, 27.85, -38.41, 0.51619, 27, -0.85, -41.49, 0.26525, 5, 21, 10.99, -72.27, 0.03441, 22, -18.59, -73.91, 0.00721, 25, 30.82, -41.68, 0.34209, 26, 17.72, -39.67, 0.48546, 27, -9.84, -46.31, 0.13083, 6, 21, 20.21, -73.62, 0.07522, 22, -9.29, -74.55, 0.02223, 23, -59.92, -4.74, 0.00086, 25, 25.07, -34.34, 0.52, 26, 8.58, -37.82, 0.33453, 27, -19.04, -47.87, 0.04716, 7, 21, 26.17, -65.16, 0.23182, 22, -4, -65.66, 0.07622, 23, -53.96, 3.72, 0.01535, 25, 14.92, -36.36, 0.56354, 26, 2.18, -45.95, 0.10652, 27, -22.09, -57.76, 0.00652, 31, 63, 112.81, 2e-05, 7, 21, 17.07, -59.77, 0.44562, 22, -13.48, -60.98, 0.09486, 23, -63.06, 9.11, 0.01454, 25, 17.79, -46.53, 0.40485, 26, 10.97, -51.83, 0.03739, 27, -11.77, -60.08, 0.00055, 31, 58.71, 103.15, 0.00219, 6, 21, 7.95, -50.73, 0.6524, 22, -23.27, -52.66, 0.06312, 23, -72.18, 18.15, 0.00522, 25, 18.16, -59.37, 0.25102, 26, 19.59, -61.35, 0.01196, 31, 50.79, 93.03, 0.01629, 6, 21, -4.14, -45.85, 0.77998, 22, -35.69, -48.72, 0.01904, 23, -84.27, 23.03, 0.0002, 25, 23.55, -71.24, 0.13111, 26, 31.39, -66.87, 0.0025, 31, 47.36, 80.46, 0.06717, 5, 21, -16.62, -43.84, 0.77302, 22, -48.29, -47.67, 0.00188, 25, 31.2, -81.31, 0.06692, 26, 43.74, -69.56, 0.00024, 31, 46.82, 67.82, 0.15793, 3, 21, -29.61, -40.95, 0.668, 25, 38.62, -92.36, 0.03157, 31, 45.47, 54.58, 0.30044, 3, 21, -42.13, -37.67, 0.48497, 25, 45.42, -103.37, 0.01274, 31, 43.67, 41.77, 0.50229, 3, 21, -52.82, -36.73, 0.27373, 25, 52.51, -111.42, 0.00415, 31, 43.99, 31.04, 0.72212, 3, 21, -61.4, -43.25, 0.11916, 25, 63.22, -112.61, 0.00112, 31, 51.47, 23.28, 0.87972, 3, 21, -68.48, -45.97, 0.05005, 25, 70.23, -115.52, 0.00029, 31, 55, 16.57, 0.94966, 2, 21, -77.54, -56.24, 0.00308, 31, 66.26, 8.78, 0.99692, 1, 31, 76.61, 2.12, 1, 1, 31, 77.47, -2.64, 1, 1, 31, 64.68, -6.79, 1, 1, 31, 45.88, -8.68, 1, 1, 31, 28.03, -8.31, 1, 1, 31, 12.9, -7.01, 1, 2, 21, -77.86, 4.82, 0.00757, 31, 5.65, 1.32, 0.99243, 2, 21, -67.79, 1.75, 0.07663, 31, 7.52, 11.68, 0.92337, 3, 21, -61.89, -1.16, 0.17905, 25, 34.55, -143.43, 0.00011, 31, 9.72, 17.88, 0.82084, 3, 21, -53.34, 1.07, 0.36148, 25, 26.82, -139.15, 0.00033, 31, 6.51, 26.11, 0.63819, 3, 21, -50.98, 7.62, 0.41514, 25, 20.59, -142.27, 9e-05, 31, -0.28, 27.69, 0.58477, 2, 21, -51.24, 11.42, 0.38468, 31, -4.02, 26.99, 0.61532, 2, 21, -57.62, 15.76, 0.22136, 31, -7.58, 20.15, 0.77864, 2, 21, -66.47, 8.95, 0.06042, 31, 0.22, 12.15, 0.93958, 1, 31, 3.24, -1.87, 1, 2, 21, -83.97, 18.52, 0.00418, 31, -7.24, -6.35, 0.99582, 2, 21, -84.81, 30.82, 0.016, 31, -19.36, -8.62, 0.984, 2, 21, -84.45, 46.86, 0.01556, 31, -35.33, -10.14, 0.98444, 2, 21, -81.71, 62.17, 0.00282, 31, -50.85, -9.2, 0.99718, 2, 21, -78.26, 67.63, 0.00102, 31, -56.68, -6.41, 0.99898, 2, 21, -70.86, 64.36, 0.00724, 31, -54.3, 1.32, 0.99276, 2, 21, -66.5, 56.29, 0.03637, 31, -46.79, 6.59, 0.96363, 2, 21, -61.61, 48.35, 0.12883, 31, -39.48, 12.37, 0.87117, 2, 21, -51.19, 49.7, 0.27165, 31, -42.04, 22.57, 0.72835, 3, 21, -36.05, 49.07, 0.44025, 28, 76.4, 95.01, 0.00015, 31, -43.18, 37.68, 0.5596, 3, 21, -24.21, 42.61, 0.58944, 28, 63.45, 91.25, 0.00197, 31, -38.15, 50.18, 0.40859, 5, 21, -13.28, 35.89, 0.77005, 22, -51.05, 32.08, 0.00331, 28, 50.95, 88.31, 0.0102, 29, 52.39, 71.09, 5e-05, 31, -32.75, 61.83, 0.2164, 6, 21, -1.99, 39.09, 0.85166, 22, -40.04, 36.14, 0.02658, 24, -92.91, -14.76, 0.00148, 28, 45.21, 78.08, 0.03077, 29, 41.69, 66.29, 0.00085, 31, -37.25, 72.67, 0.08866, 6, 21, 12.5, 39.04, 0.75418, 22, -25.58, 37.19, 0.12058, 24, -78.42, -14.81, 0.01277, 28, 34.89, 67.9, 0.08872, 29, 27.34, 64.24, 0.00616, 31, -38.89, 87.07, 0.01759, 6, 21, 25.68, 48.34, 0.4696, 22, -13.15, 47.47, 0.25454, 24, -65.24, -5.51, 0.04134, 28, 32.1, 52.01, 0.20921, 29, 15.65, 53.12, 0.02481, 31, -49.67, 99.07, 0.0005, 5, 21, 37.41, 53.06, 0.26218, 22, -1.82, 53.08, 0.26145, 24, -53.51, -0.78, 0.06471, 28, 27.11, 40.4, 0.34633, 29, 4.73, 46.75, 0.06533, 6, 21, 43.68, 54.62, 0.13726, 22, 4.32, 55.11, 0.19871, 24, -47.24, 0.77, 0.0672, 28, 23.76, 34.87, 0.47244, 29, -1.25, 44.3, 0.12423, 30, -41.31, 50.51, 0.00016, 6, 21, 37.67, 60.32, 0.06429, 22, -2.12, 60.34, 0.10113, 24, -53.25, 6.48, 0.02904, 28, 32.05, 35.06, 0.48679, 29, 5.53, 39.53, 0.31604, 30, -35.26, 44.85, 0.00271, 6, 21, 34.57, 64.43, 0.03626, 22, -5.51, 64.2, 0.05889, 24, -56.34, 10.59, 0.01309, 28, 37.14, 34.33, 0.43034, 29, 9.18, 35.91, 0.45172, 30, -32.14, 40.76, 0.00971, 6, 21, 21.79, 65.17, 0.01056, 22, -18.31, 63.96, 0.01745, 24, -69.13, 11.33, 0.00099, 28, 46.73, 42.82, 0.21367, 29, 21.94, 37.03, 0.68975, 30, -19.36, 40.1, 0.06758, 5, 21, 11.63, 69.52, 0.00391, 22, -28.78, 67.51, 0.00645, 28, 57, 46.9, 0.10582, 29, 32.62, 34.21, 0.68807, 30, -9.17, 35.82, 0.19575, 5, 21, 5.17, 74.49, 0.00115, 22, -35.61, 71.98, 0.00188, 28, 65.09, 47.93, 0.04061, 29, 39.74, 30.22, 0.56086, 30, -2.67, 30.89, 0.3955, 5, 21, -2.19, 69.39, 0.00017, 22, -42.55, 66.33, 0.00027, 28, 66.71, 56.73, 0.01102, 29, 46.27, 36.34, 0.35467, 30, 4.65, 36.04, 0.63388, 3, 28, 73.24, 65.9, 0.00138, 29, 56.98, 39.83, 0.20467, 30, 15.73, 38.01, 0.79395, 2, 29, 66.84, 37.91, 0.1163, 30, 25.24, 34.75, 0.8837, 2, 29, 79.36, 30.75, 0.02753, 30, 36.64, 25.92, 0.97247, 1, 30, 49.13, 13.07, 1, 1, 30, 49.2, 0.09, 1, 1, 30, 43.39, -15.79, 1, 1, 30, 34.66, -26.07, 1, 2, 29, 66.76, -24.01, 0.0512, 30, 16.58, -26.56, 0.9488, 2, 29, 49.65, -25, 0.42631, 30, -0.5, -25.17, 0.57369, 2, 29, 30.1, -22.76, 0.9437, 30, -19.56, -20.25, 0.0563, 1, 29, 12.2, -19.86, 1, 2, 28, 53.13, -7.13, 0.49553, 29, -2.62, -6.92, 0.50447, 1, 28, 39.1, -12.24, 1, 1, 28, 24.56, -14.11, 1, 2, 24, 0.93, 33.97, 0.05977, 28, 13.01, -22.63, 0.94023, 2, 24, 13.94, 23.7, 0.47199, 28, -3.46, -24.53, 0.52801, 2, 24, 36.75, 21.1, 0.87687, 28, -21.47, -38.76, 0.12313, 2, 24, 53.56, 11.54, 0.92615, 28, -40.13, -43.83, 0.07385, 4, 22, 90.5, 65.65, 0.01364, 23, 50.29, 127.43, 0.00333, 24, 39.5, 4.7, 0.90737, 28, -34.98, -29.07, 0.07566, 4, 22, 82.92, 54.11, 0.09575, 23, 41.85, 116.5, 0.02188, 24, 31.06, -6.22, 0.85451, 28, -36.7, -15.37, 0.02786, 4, 22, 90, 42.34, 0.22139, 23, 48.01, 104.22, 0.05839, 24, 37.22, -18.5, 0.7198, 28, -49.72, -11, 0.00042, 3, 22, 94.49, 29.08, 0.31107, 23, 51.47, 90.66, 0.10408, 24, 40.68, -32.07, 0.58485, 5, 21, 55.17, -56.34, 0.09554, 22, 24.25, -54.65, 0.17804, 23, -24.96, 12.54, 0.26642, 25, -12.17, -22.75, 0.45422, 26, -27.26, -53.19, 0.00578, 6, 21, 38.59, -59.25, 0.20764, 22, 7.94, -58.81, 0.12592, 23, -41.54, 9.63, 0.06493, 25, 1.84, -32.07, 0.56314, 26, -10.55, -51.18, 0.03809, 27, -32.09, -67.22, 0.00027, 5, 21, 71.48, 50.73, 0.02066, 22, 32.33, 53.36, 0.15807, 24, -19.44, -3.11, 0.31576, 28, 1.31, 18.03, 0.49496, 29, -29.32, 44.11, 0.01054, 5, 21, 57.08, 54.3, 0.06683, 22, 17.7, 55.81, 0.18325, 24, -33.84, 0.45, 0.12133, 28, 14.03, 25.65, 0.57013, 29, -14.56, 42.68, 0.05846, 5, 21, 27.53, 0.57, 0.72758, 22, -7.66, -0.01, 0.272, 24, -63.39, -53.27, 9e-05, 28, -2.88, 84.58, 0.00033, 29, 6.89, 100.11, 1e-05, 5, 21, 30.03, -15.51, 0.66279, 22, -3.94, -15.85, 0.26137, 23, -50.1, 53.38, 0.0144, 25, -22.12, -69.66, 0.06131, 26, -4.37, -95.32, 0.00014, 5, 21, 41.75, -31.25, 0.303, 22, 8.94, -30.66, 0.41816, 23, -38.39, 37.63, 0.09417, 25, -19.75, -50.17, 0.18276, 26, -15.22, -78.97, 0.00191, 6, 21, 61.09, -38.7, 0.07884, 22, 28.8, -36.6, 0.43681, 23, -19.04, 30.18, 0.32071, 24, -29.83, -92.54, 0.00052, 25, -28.62, -31.44, 0.16278, 26, -34.13, -70.48, 0.00034, 5, 21, 88.15, -39.47, 0.00236, 22, 55.84, -35.31, 0.36049, 23, 8.02, 29.41, 0.58568, 24, -2.77, -93.31, 0.04269, 25, -47.69, -12.22, 0.00878, 5, 21, 94.37, -34.04, 0.00025, 22, 61.63, -29.42, 0.40423, 23, 14.24, 34.84, 0.519, 24, 3.45, -87.88, 0.0749, 25, -55.94, -11.86, 0.00162, 4, 22, 62.64, -20.95, 0.47995, 23, 15.9, 43.2, 0.40843, 24, 5.11, -79.52, 0.11119, 25, -62.9, -16.78, 0.00043, 3, 22, 72.89, -5.5, 0.47543, 23, 27.3, 57.83, 0.26618, 24, 16.51, -64.89, 0.25838, 3, 22, 87.37, -1.31, 0.41237, 23, 42.05, 60.91, 0.24859, 24, 31.26, -61.82, 0.33904, 3, 22, 86.94, 21.83, 0.34512, 23, 43.39, 84.01, 0.12049, 24, 32.6, -38.72, 0.5344, 3, 22, 70.24, 23.16, 0.37057, 23, 26.84, 86.61, 0.08167, 24, 16.05, -36.12, 0.54776, 3, 22, 65.52, 31.23, 0.30748, 23, 22.75, 95.02, 0.04786, 24, 11.96, -27.71, 0.64466, 3, 22, 55.51, 37.07, 0.26863, 23, 13.22, 101.6, 0.01999, 24, 2.43, -21.13, 0.71138, 6, 21, 77.9, 32.24, 0.00681, 22, 40.14, 35.41, 0.38836, 23, -2.23, 101.12, 0.004, 24, -13.02, -21.61, 0.52159, 28, -16.28, 26.62, 0.07834, 29, -38.35, 61.48, 0.0009, 5, 21, 62.4, 36.36, 0.05671, 22, 24.37, 38.33, 0.41833, 24, -28.52, -17.49, 0.27624, 28, -2.38, 34.62, 0.23725, 29, -22.42, 59.65, 0.01147, 5, 21, 44.72, 34.01, 0.23626, 22, 6.93, 34.64, 0.43144, 24, -46.2, -19.84, 0.09952, 28, 8.49, 48.75, 0.21397, 29, -5.27, 64.54, 0.01881, 5, 21, 32.71, 22.88, 0.51534, 22, -4.2, 22.62, 0.35348, 24, -58.21, -30.97, 0.03193, 28, 9.17, 65.11, 0.09332, 29, 5, 77.3, 0.00593, 5, 21, 27.62, 6.77, 0.75391, 22, -8.05, 6.18, 0.23155, 24, -63.3, -47.07, 0.00297, 28, 1.43, 80.13, 0.01116, 29, 7.71, 93.97, 0.00041, 5, 21, 33.77, 2.97, 0.2195, 22, -1.62, 2.85, 0.77794, 24, -57.15, -50.88, 0.00076, 28, -5.62, 78.49, 0.00174, 29, 1.07, 96.84, 6e-05, 5, 21, 47.52, 5.19, 0.04712, 22, 11.92, 6.12, 0.89235, 24, -43.4, -48.65, 0.03023, 28, -13.8, 67.22, 0.02954, 29, -12.21, 92.65, 0.00076, 4, 22, 25.79, 7.23, 0.85426, 24, -29.48, -48.61, 0.11495, 28, -23.64, 57.38, 0.03055, 29, -25.98, 90.59, 0.00024, 4, 22, 43.62, 8.29, 0.7702, 23, -0.83, 73.81, 0.03666, 24, -11.62, -48.92, 0.19055, 28, -36.53, 45, 0.00259, 3, 22, 61.26, 8.09, 0.54567, 23, 16.74, 72.27, 0.12157, 24, 5.95, -50.46, 0.33276, 3, 22, 73.52, 9.39, 0.44479, 23, 29.06, 72.63, 0.15456, 24, 18.27, -50.1, 0.40064, 3, 22, 87.4, 11.03, 0.38963, 23, 43.02, 73.21, 0.16853, 24, 32.23, -49.52, 0.44184, 3, 22, 100.56, 12.69, 0.37386, 23, 56.27, 73.85, 0.16881, 24, 45.48, -48.88, 0.45732, 3, 22, 112.08, 12.56, 0.37417, 23, 67.75, 72.84, 0.1673, 24, 56.96, -49.89, 0.45853, 6, 21, 13.06, 3.8, 0.99446, 22, -22.33, 2.1, 0.00227, 24, -77.86, -50.04, 0.00016, 28, 9.66, 92.49, 0.00289, 29, 21.68, 99.02, 5e-05, 31, -3.96, 91.74, 0.00018, 6, 21, 8.36, -15.57, 0.93523, 22, -25.55, -17.57, 0.00942, 23, -71.77, 53.31, 0.00071, 25, -6.38, -84.55, 0.04641, 26, 17.27, -96.43, 9e-05, 31, 15.83, 89.33, 0.00813, 6, 21, 10.63, -35.91, 0.74931, 22, -21.72, -37.68, 0.0567, 23, -69.5, 32.97, 0.00492, 25, 6, -68.25, 0.1686, 26, 16.1, -76, 0.00414, 31, 35.77, 93.96, 0.01634, 7, 21, 19.84, -51.74, 0.49361, 22, -11.33, -52.76, 0.10368, 23, -60.29, 17.14, 0.01692, 25, 10.24, -50.44, 0.35765, 26, 7.77, -59.7, 0.02524, 27, -11.93, -68.57, 8e-05, 31, 50.41, 104.96, 0.00281, 6, 21, 11.02, 14.76, 0.94449, 22, -25.2, 12.87, 0.02435, 24, -79.9, -39.08, 0.0016, 28, 18.83, 86.16, 0.02036, 29, 25.29, 88.47, 0.00058, 31, -14.61, 88.43, 0.00862, 6, 21, 14.26, 31.44, 0.7846, 22, -23.25, 29.75, 0.11108, 24, -76.66, -22.41, 0.0106, 28, 28.29, 72.06, 0.07424, 29, 24.5, 71.5, 0.00444, 31, -31.55, 89.69, 0.01504, 6, 21, 25.65, 40.51, 0.53292, 22, -12.58, 39.66, 0.24141, 24, -65.27, -13.34, 0.03487, 28, 26.6, 57.59, 0.17182, 29, 14.54, 60.88, 0.01751, 31, -41.88, 99.95, 0.00147, 3, 21, -5.08, 5.75, 0.95352, 28, 23.9, 103.91, 0.00197, 31, -3.78, 73.49, 0.04451, 4, 21, -21.92, 7.23, 0.76331, 28, 36.88, 114.72, 0.0005, 25, -0.18, -121.94, 0.00063, 31, -3.28, 56.6, 0.23555, 3, 21, -38.87, 7.31, 0.53794, 25, 12.04, -133.69, 0.00027, 31, -1.38, 39.75, 0.46179, 5, 21, -5.39, -21.78, 0.87752, 22, -38.77, -24.82, 0.00123, 25, 7.85, -89.53, 0.0498, 26, 31.33, -90.97, 8e-05, 31, 23.6, 76.41, 0.07137, 3, 21, -22.85, -21.48, 0.74031, 25, 20.3, -101.79, 0.02287, 31, 25.35, 59.03, 0.23682, 3, 21, -38.68, -18.68, 0.53111, 25, 29.83, -114.74, 0.00795, 31, 24.41, 42.97, 0.46094, 3, 21, -51.43, -18.97, 0.32983, 25, 39.26, -123.32, 0.00284, 31, 26.19, 30.35, 0.66733, 3, 21, -63.47, -21.27, 0.1522, 25, 49.56, -129.95, 0.00081, 31, 29.88, 18.66, 0.84699, 3, 21, -77.59, -31.16, 0.0197, 25, 66.61, -132.53, 8e-05, 31, 41.35, 5.79, 0.98023, 3, 21, -83.05, -47.11, 0.00198, 25, 81.57, -124.75, 0, 31, 57.83, 2.23, 0.99802, 3, 21, -75.53, -11.73, 0.02606, 25, 51.72, -145.18, 1e-05, 31, 21.81, 5.57, 0.97393, 6, 21, -3.26, 22.51, 0.89169, 22, -40.04, 19.51, 0.0083, 24, -94.18, -31.33, 0.00029, 28, 34.42, 90.73, 0.01584, 29, 40.54, 82.87, 0.0002, 31, -20.64, 73.34, 0.08368, 4, 21, -18.34, 23.84, 0.75444, 22, -55.17, 19.68, 0, 28, 46.05, 100.42, 0.00446, 31, -20.19, 58.21, 0.2411, 3, 21, -32.46, 25.96, 0.56709, 28, 57.56, 108.87, 0.00057, 31, -20.65, 43.94, 0.43234, 2, 21, -44.16, 31.59, 0.38889, 31, -24.87, 31.67, 0.61111, 2, 21, -62.22, 29.11, 0.15226, 31, -20.3, 14.02, 0.84774, 2, 21, -72.46, 37.25, 0.05585, 31, -27.19, 2.9, 0.94415, 2, 21, -75.7, 52.56, 0.02014, 31, -42.01, -2.12, 0.97986, 2, 21, -74.45, 20.64, 0.03171, 31, -10.46, 2.86, 0.96829, 3, 22, 81.79, -22.23, 0.37892, 23, 34.89, 40.47, 0.45264, 24, 24.1, -82.26, 0.16844, 3, 22, 77.16, -44.37, 0.22795, 23, 28.58, 18.74, 0.70648, 24, 17.79, -103.98, 0.06557, 3, 22, 57.98, -68.27, 0.00217, 23, 7.64, -3.62, 0.99517, 24, -3.15, -126.35, 0.00266, 2, 23, -14.6, -15.69, 0.08117, 25, -0.2, 4.84, 0.91883, 5, 21, 41.62, -93.75, 0.00586, 22, 13.59, -92.98, 0.0017, 25, 23.44, -5, 0.94897, 26, -11.7, -16.57, 0.04318, 27, -45.61, -35.33, 0.00029, 5, 21, 20.31, -104.53, 0.00272, 22, -6.83, -105.36, 0.00013, 25, 46.3, -11.88, 0.16056, 26, 10.15, -6.96, 0.83243, 27, -28.67, -18.5, 0.00416, 4, 21, 0.01, -107.14, 0.00066, 25, 62.81, -23.99, 0.01526, 26, 30.57, -5.45, 0.93077, 27, -10.16, -9.75, 0.05332, 4, 21, -23.44, -107.83, 1e-05, 25, 80.27, -39.66, 0.00054, 26, 54.02, -6.03, 0.01254, 27, 11.93, -1.86, 0.98691, 2, 26, 74.79, -15.64, 0.00174, 27, 34.76, -3.36, 0.99826, 3, 22, 77.11, 39.68, 0.21615, 23, 34.95, 102.55, 0.04804, 24, 24.16, -20.17, 0.73581, 4, 22, 64.46, 59.82, 0.02564, 23, 23.88, 123.61, 0.00517, 24, 13.09, 0.88, 0.88314, 28, -18.94, -7.74, 0.08605, 1, 28, 3.86, -5.14, 1, 5, 21, 67.47, 77.19, 0.00152, 22, 26.31, 79.43, 0.00398, 24, -23.44, 23.35, 0.0019, 28, 22.8, 2.09, 0.9866, 29, -21.52, 18.52, 0.006, 5, 21, 49.88, 86.71, 0.00375, 22, 8.05, 87.58, 0.00699, 24, -41.04, 32.87, 0, 28, 41.99, 7.74, 0.7584, 29, -2.73, 11.65, 0.23086, 5, 21, 30.81, 94.99, 0.0009, 22, -11.61, 94.38, 0.00151, 28, 61.36, 15.31, 0.05221, 29, 17.34, 6.22, 0.94458, 30, -28.18, 10.22, 0.0008, 5, 21, 7.68, 104.18, 0, 22, -35.36, 101.77, 0, 28, 84.23, 25.1, 0.0001, 29, 41.55, 0.48, 0.96391, 30, -5, 1.19, 0.03598, 1, 30, 20.17, -0.31, 1, 2, 29, 69.93, 17.5, 0.02778, 30, 25.47, 14.1, 0.97222, 5, 22, 61.21, -5.72, 0.52342, 23, 15.63, 58.51, 0.2546, 24, 4.85, -64.22, 0.22167, 28, -58.99, 44.25, 0.0001, 25, -73.26, -28.04, 0.00021, 7, 21, 77.27, -19.19, 0.03386, 22, 43.44, -15.91, 0.54029, 23, -2.86, 49.7, 0.23524, 24, -13.65, -73.03, 0.11988, 28, -52.09, 63.53, 0.0006, 25, -53.79, -34.42, 0.06997, 26, -51.34, -89.09, 0.00015, 7, 21, 79.91, -34.32, 0.02583, 22, 47.23, -30.81, 0.42037, 23, -0.22, 34.56, 0.44508, 24, -11.01, -88.17, 0.05299, 28, -64.64, 72.41, 0.00015, 25, -45.27, -21.63, 0.05548, 26, -53.16, -73.83, 0.00011, 8, 21, 61.79, -19.98, 0.12807, 22, 28.07, -17.89, 0.55544, 23, -18.34, 48.9, 0.14797, 24, -29.13, -73.82, 0.06453, 28, -41.67, 75, 0.0021, 29, -29.99, 115.48, 1e-05, 25, -42.04, -44.51, 0.1011, 26, -35.85, -89.14, 0.00077, 8, 21, 46.4, -16.14, 0.24621, 22, 12.43, -15.24, 0.62678, 23, -33.73, 52.74, 0.05224, 24, -44.52, -69.98, 0.02156, 28, -28.04, 83.14, 0.00137, 29, -14.19, 113.92, 2e-05, 25, -33.54, -57.91, 0.05151, 26, -20.68, -93.8, 0.00029, 3, 22, 61, 19.53, 0.43242, 23, 17.35, 83.7, 0.08314, 24, 6.56, -39.03, 0.48444, 6, 21, 80.03, 21.65, 0.00396, 22, 43.07, 25.01, 0.51947, 23, -0.1, 90.53, 0.02161, 24, -10.89, -32.19, 0.40801, 28, -25.26, 32.63, 0.04642, 29, -41.99, 71.65, 0.00053, 6, 21, 62.47, 23.52, 0.03265, 22, 25.42, 25.54, 0.63059, 23, -17.66, 92.41, 0.00056, 24, -28.45, -30.32, 0.18733, 28, -11.48, 43.67, 0.14223, 29, -24.35, 72.34, 0.00664, 6, 21, 46.21, 19.49, 0.12799, 22, 9.52, 20.27, 0.69195, 23, -33.92, 88.37, 0, 24, -44.7, -34.36, 0.05727, 28, -2.8, 58, 0.1133, 29, -8.85, 78.69, 0.00949], "hull": 105, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 0, 208, 210, 212, 212, 68, 214, 216, 216, 152, 218, 220, 220, 222, 222, 224, 224, 226, 226, 228, 228, 230, 230, 232, 232, 234, 0, 236, 10, 234, 236, 238, 238, 240, 240, 242, 242, 244, 244, 246, 246, 248, 248, 250, 250, 252, 252, 218, 254, 256, 256, 258, 258, 260, 260, 262, 262, 264, 264, 266, 266, 268, 268, 270, 218, 272, 272, 274, 274, 276, 276, 278, 272, 280, 280, 282, 282, 284, 272, 286, 286, 288, 288, 290, 292, 294, 294, 296, 296, 298, 298, 300, 300, 302, 302, 304, 280, 308, 308, 310, 310, 312, 312, 314, 314, 316, 316, 318, 318, 320, 324, 326, 326, 328, 328, 330, 330, 332, 332, 334, 334, 336, 336, 338, 338, 340, 342, 344, 344, 346, 346, 348, 348, 350, 350, 352, 352, 354, 354, 356, 356, 358, 360, 362, 230, 364, 364, 224, 362, 366, 366, 368, 370, 372, 372, 374, 374, 376], "width": 265, "height": 246}}, "loong": {"loong": {"type": "path", "lengths": [46.48, 73.16, 92.39, 121.77, 161.16, 191.43, 231.03, 285.53, 496.95], "vertexCount": 27, "vertices": [1, 57, -71.59, 114.36, 1, 2, 54, 38.35, 57.93, 0.104, 53, 0.46, 29.15, 0.896, 2, 54, 47.53, 41.27, 0.104, 53, 9.64, 12.48, 0.896, 1, 53, 6.34, -5.38, 1, 1, 53, -0.97, -15.08, 1, 2, 54, 27.66, 1.42, 0.496, 53, -10.24, -27.36, 0.504, 2, 54, 17.22, 9.81, 0.808, 53, -20.67, -18.97, 0.192, 1, 54, 12.51, 6.68, 1, 1, 54, 7.81, 3.55, 1, 1, 54, 10.64, -5.46, 1, 2, 55, -35.43, -3.55, 0.32, 54, 16.38, -10, 0.68, 2, 55, -29.7, -8.09, 0.888, 54, 22.11, -14.53, 0.112, 1, 55, -15.88, -4.11, 1, 1, 55, -7.66, -11.92, 1, 2, 56, 5.84, 33.89, 0.208, 55, 4.84, -23.81, 0.792, 2, 56, -5.49, 16.12, 0.808, 55, -6.48, -41.58, 0.192, 1, 56, 7.06, 10.22, 1, 1, 56, 16.17, 5.93, 1, 2, 57, -26.06, 7.15, 0.384, 56, 21.73, 35.47, 0.616, 2, 57, -18.85, -0.9, 0.688, 56, 28.94, 27.41, 0.312, 2, 57, -11.63, -8.96, 0.944, 56, 36.16, 19.36, 0.056, 1, 57, -21.94, -31.59, 1, 1, 57, -19.92, -39.83, 1, 1, 57, -17.27, -50.59, 1, 1, 57, -1.86, -72.22, 1, 1, 57, 17.49, -76.88, 1, 1, 57, 31.21, -80.19, 1]}}, "longbody": {"longbody": {"type": "mesh", "uvs": [0.22299, 0.10612, 0.22759, 0.05644, 0.25211, 0.01347, 0.30575, 0, 0.35786, 0.01078, 0.43449, 0.03495, 0.50499, 0.03092, 0.47127, 0.09269, 0.4207, 0.15581, 0.3548, 0.18938, 0.27357, 0.20147, 0.21533, 0.16789, 0.16301, 0.15212, 0.1184, 0.14832, 0.08495, 0.18577, 0.10601, 0.22486, 0.16673, 0.25471, 0.25285, 0.2634, 0.325, 0.27274, 0.40896, 0.29488, 0.48562, 0.33172, 0.54122, 0.38132, 0.58569, 0.44509, 0.60288, 0.51772, 0.6049, 0.57352, 0.614, 0.60806, 0.62714, 0.57352, 0.6514, 0.52126, 0.69082, 0.48672, 0.77675, 0.469, 0.83942, 0.49292, 0.90987, 0.5214, 0.96375, 0.56431, 0.99084, 0.63805, 0.98741, 0.70574, 0.97593, 0.77865, 0.93863, 0.83481, 0.90515, 0.89264, 0.86976, 0.9513, 0.80443, 0.97398, 0.7413, 0.95135, 0.68032, 0.91867, 0.65162, 0.88221, 0.65664, 0.85456, 0.69825, 0.84953, 0.72623, 0.81998, 0.73699, 0.79547, 0.72838, 0.76278, 0.73054, 0.73387, 0.75134, 0.7037, 0.77573, 0.67856, 0.78793, 0.64839, 0.76927, 0.61759, 0.74775, 0.6201, 0.73197, 0.65153, 0.72121, 0.69867, 0.70041, 0.7433, 0.65808, 0.77284, 0.60421, 0.79287, 0.53821, 0.77841, 0.47579, 0.73944, 0.43993, 0.68476, 0.44208, 0.6087, 0.45499, 0.5515, 0.4263, 0.49996, 0.38612, 0.45282, 0.33734, 0.40693, 0.26775, 0.38745, 0.18659, 0.3755, 0.10983, 0.34847, 0.051, 0.30196, 0.01585, 0.25419, 0.00796, 0.19699, 0.02446, 0.14608, 0.06607, 0.10522, 0.11772, 0.09265, 0.1622, 0.10019, 0.20739, 0.11465, 0.44003, 0.06652, 0.38299, 0.09334, 0.33012, 0.1104, 0.27586, 0.14088, 0.21603, 0.13722, 0.16038, 0.12869, 0.11777, 0.12067, 0.06983, 0.13824, 0.04929, 0.17038, 0.05027, 0.20767, 0.062, 0.24153, 0.10016, 0.27967, 0.14516, 0.30238, 0.20189, 0.3221, 0.25863, 0.32767, 0.3276, 0.33667, 0.38434, 0.35895, 0.43765, 0.39238, 0.47972, 0.43952, 0.50556, 0.49281, 0.51632, 0.53738, 0.51828, 0.58195, 0.5217, 0.63124, 0.53638, 0.68352, 0.59899, 0.70967, 0.65516, 0.68138, 0.67228, 0.62567, 0.69526, 0.58453, 0.74467, 0.55495, 0.79994, 0.55024, 0.84934, 0.58967, 0.86934, 0.63744, 0.86934, 0.69316, 0.85369, 0.75616, 0.83216, 0.80759, 0.81358, 0.84873, 0.76662, 0.88387, 0.72015, 0.89716], "triangles": [78, 5, 6, 7, 78, 6, 79, 4, 5, 79, 5, 78, 80, 3, 4, 80, 4, 79, 2, 3, 80, 1, 2, 80, 81, 0, 80, 7, 8, 79, 7, 79, 78, 8, 9, 80, 8, 80, 79, 81, 80, 9, 10, 11, 81, 10, 81, 9, 0, 1, 80, 82, 77, 0, 83, 77, 82, 82, 0, 81, 12, 83, 82, 13, 83, 12, 11, 12, 82, 11, 82, 81, 84, 75, 76, 74, 75, 84, 83, 84, 76, 83, 76, 77, 85, 74, 84, 73, 74, 85, 13, 84, 83, 85, 84, 13, 86, 73, 85, 14, 85, 13, 86, 85, 14, 72, 73, 86, 87, 86, 14, 72, 86, 87, 87, 14, 15, 88, 87, 15, 71, 72, 87, 71, 87, 88, 70, 71, 88, 89, 88, 15, 89, 15, 16, 70, 88, 89, 69, 89, 90, 70, 89, 69, 69, 90, 68, 90, 89, 16, 90, 16, 91, 68, 90, 91, 68, 91, 92, 67, 68, 92, 67, 93, 66, 91, 16, 17, 92, 17, 18, 91, 17, 92, 93, 18, 19, 92, 18, 93, 94, 93, 19, 94, 19, 20, 67, 92, 93, 66, 93, 94, 95, 94, 20, 95, 20, 21, 96, 95, 21, 96, 21, 22, 95, 66, 94, 65, 95, 96, 65, 66, 95, 97, 96, 22, 64, 65, 96, 64, 96, 97, 97, 22, 23, 98, 97, 23, 63, 64, 97, 63, 97, 98, 98, 23, 24, 99, 98, 24, 63, 98, 99, 25, 99, 24, 25, 100, 99, 62, 99, 100, 101, 100, 25, 61, 100, 101, 62, 63, 99, 61, 62, 100, 60, 61, 101, 59, 101, 102, 58, 59, 102, 60, 101, 59, 57, 58, 102, 104, 26, 105, 25, 26, 104, 103, 25, 104, 104, 105, 53, 103, 102, 25, 54, 104, 53, 103, 104, 54, 55, 103, 54, 56, 103, 55, 57, 103, 56, 102, 103, 57, 102, 101, 25, 27, 28, 106, 105, 26, 27, 106, 105, 27, 53, 105, 106, 52, 53, 106, 107, 29, 30, 106, 28, 29, 106, 29, 107, 108, 30, 31, 108, 31, 32, 107, 30, 108, 52, 106, 107, 52, 107, 108, 109, 108, 32, 109, 32, 33, 109, 51, 108, 110, 109, 33, 51, 109, 110, 34, 110, 33, 35, 110, 34, 51, 52, 108, 50, 51, 110, 111, 50, 110, 111, 110, 35, 36, 111, 35, 49, 50, 111, 48, 111, 47, 111, 48, 49, 112, 46, 47, 111, 112, 47, 112, 111, 36, 113, 46, 112, 45, 46, 113, 114, 45, 113, 44, 45, 114, 37, 112, 36, 113, 112, 37, 115, 44, 114, 44, 42, 43, 115, 42, 44, 115, 41, 42, 38, 113, 37, 114, 113, 38, 40, 115, 114, 39, 40, 114, 41, 115, 40, 38, 39, 114], "vertices": [5, 49, -10.48, 12.42, 2e-05, 50, -7.2, -3.83, 0.17079, 51, 0.81, -3.53, 0.55087, 52, 7.15, -5.88, 0.21732, 33, 13.66, -9.27, 0.061, 5, 49, -15.58, 11.26, 0, 50, -6.97, -9.06, 0.06074, 51, -0.38, -8.63, 0.48351, 52, 3.29, -9.41, 0.30536, 33, 8.85, -11.32, 0.15039, 5, 49, -20.56, 12.06, 0, 50, -8.65, -13.81, 0.01458, 51, -3.28, -12.76, 0.3507, 52, -1.44, -11.18, 0.33652, 33, 3.8, -11.43, 0.29821, 4, 50, -13.37, -15.83, 0.00165, 51, -8.37, -13.43, 0.21385, 52, -6.01, -8.85, 0.29477, 33, 0.25, -7.72, 0.48973, 4, 50, -18.27, -15.3, 0.0002, 51, -12.94, -11.6, 0.11052, 52, -8.75, -4.76, 0.20246, 33, -0.98, -2.95, 0.68682, 3, 51, -19.54, -8.04, 0.04691, 52, -12.19, 1.9, 0.1088, 33, -2.02, 4.47, 0.8443, 3, 51, -26.01, -7.5, 0.0118, 52, -17.22, 6.01, 0.061, 33, -5.41, 10.01, 0.92721, 4, 48, 2.77, 39.76, 0, 51, -21.98, -1.54, 0.01109, 52, -10.53, 8.64, 0.07913, 33, 1.77, 10.29, 0.90978, 4, 48, 6.41, 32.53, 0, 51, -16.4, 4.32, 0.04552, 52, -2.61, 10.33, 0.15935, 33, 9.8, 9.26, 0.79513, 6, 48, 6.65, 25.52, 1e-05, 49, -5.76, 26.6, 0.00122, 50, -20.32, 3.34, 0.01072, 51, -9.88, 6.91, 0.13179, 52, 4.23, 8.78, 0.25176, 33, 15.75, 5.53, 0.60449, 7, 47, 6.39, 15.2, 1e-05, 48, 4.23, 18.34, 0.00124, 49, -2.32, 19.85, 0.01542, 50, -13.06, 5.53, 0.0435, 51, -2.3, 7.06, 0.25624, 52, 10.56, 4.62, 0.29991, 33, 20.35, -0.49, 0.38368, 8, 46, -3.91, 16.26, 9e-05, 47, 0.07, 16.25, 0.00095, 48, -1.41, 15.28, 0.01463, 49, -4.08, 13.68, 0.05596, 50, -7.31, 2.7, 0.10966, 51, 2.47, 2.78, 0.35726, 52, 12.09, -1.61, 0.26691, 33, 19.73, -6.87, 0.19455, 8, 46, -8.88, 17.37, 0.00249, 47, -5.01, 15.82, 0.01148, 48, -5.15, 11.82, 0.05355, 49, -4.22, 8.59, 0.13133, 50, -2.32, 1.65, 0.17763, 51, 6.99, 0.42, 0.37204, 52, 14.48, -6.1, 0.17838, 33, 20.5, -11.9, 0.07312, 8, 46, -13, 17.32, 0.02064, 47, -8.92, 14.52, 0.04136, 48, -7.44, 8.4, 0.12655, 49, -3.37, 4.56, 0.2079, 50, 1.8, 1.76, 0.21342, 51, 10.99, -0.58, 0.28696, 52, 17.21, -9.19, 0.08479, 33, 22.06, -15.72, 0.01838, 8, 46, -15.63, 13.07, 0.07998, 47, -10.14, 9.68, 0.09669, 48, -5.43, 3.83, 0.20327, 49, 1.3, 2.8, 0.24556, 50, 4.36, 6.05, 0.18307, 51, 14.61, 2.86, 0.16251, 52, 22.15, -8.41, 0.0268, 33, 26.98, -16.61, 0.00212, 8, 45, -23.46, 6.03, 0.00591, 46, -13.25, 9.21, 0.20381, 47, -6.71, 6.71, 0.15265, 48, -0.9, 3.59, 0.24355, 49, 4.64, 5.87, 0.21153, 50, 1.93, 9.88, 0.11691, 51, 13.3, 7.2, 0.06149, 52, 23.52, -4.08, 0.00414, 9, 44, -25.95, -6.24, 0.00144, 45, -17.24, 4.5, 0.03423, 46, -7.35, 6.7, 0.37091, 47, -0.33, 6.11, 0.1799, 48, 4.51, 7.03, 0.2145, 49, 5.96, 12.14, 0.13617, 50, -4, 12.3, 0.04894, 51, 8.24, 11.13, 0.01391, 52, 21.57, 2.02, 0, 9, 44, -19.52, -1.52, 0.0152, 45, -9.36, 5.73, 0.10437, 46, 0.62, 6.67, 0.51121, 47, 7.28, 8.48, 0.15458, 48, 9.06, 13.58, 0.14151, 49, 4.46, 19.98, 0.05959, 50, -11.98, 12.22, 0.01314, 51, 0.54, 13.21, 0.00039, 52, 16.39, 8.08, 0, 10, 38, 16.25, 48.62, 2e-05, 40, -45.83, 16.1, 0, 43, -22.27, -6.23, 0.00298, 44, -13.98, 2.28, 0.06136, 45, -2.7, 6.55, 0.21226, 46, 7.33, 6.42, 0.54174, 47, 13.75, 10.27, 0.09924, 48, 13.06, 18.96, 0.06476, 49, 3.41, 26.6, 0.01703, 50, -18.68, 12.37, 0.0006, 11, 38, 18.35, 40.83, 0.00054, 39, 9.81, 47.3, 0, 40, -38.92, 20.26, 0.0002, 42, -25.56, -6.04, 0.00103, 43, -17.36, 0.17, 0.02015, 44, -6.74, 5.82, 0.15668, 45, 5.37, 6.37, 0.31141, 46, 15.26, 4.96, 0.44552, 47, 21.75, 11.28, 0.04325, 48, 18.76, 24.67, 0.01959, 49, 3.32, 34.67, 0.00163, 10, 38, 18.78, 32.8, 0.00393, 39, 13.35, 40.08, 0.00015, 40, -31.31, 22.89, 0.00168, 42, -21.24, 0.75, 0.00843, 43, -11.25, 5.4, 0.0675, 44, 1.07, 7.78, 0.28054, 45, 13.19, 4.51, 0.34369, 46, 22.69, 1.89, 0.27932, 47, 29.77, 10.6, 0.01229, 48, 25.51, 29.05, 0.00247, 9, 38, 17.05, 25.71, 0.01773, 39, 14.53, 32.88, 0.00121, 40, -24.02, 23.18, 0.00681, 42, -15.72, 5.52, 0.03112, 43, -4.57, 8.35, 0.15069, 44, 8.36, 7.44, 0.37547, 45, 19.51, 0.86, 0.28592, 46, 28.35, -2.73, 0.12993, 47, 36.55, 7.91, 0.00114, 8, 38, 13.52, 18.7, 0.0575, 39, 14.02, 25.05, 0.00396, 40, -16.31, 21.71, 0.01761, 42, -8.77, 9.18, 0.0753, 43, 3.14, 9.8, 0.24298, 44, 15.91, 5.31, 0.38297, 45, 25.24, -4.51, 0.17849, 46, 33.15, -8.94, 0.0412, 9, 37, -3.1, 13.25, 0.00072, 38, 7.87, 13.34, 0.14255, 39, 10.92, 17.9, 0.00856, 40, -9.6, 17.76, 0.03268, 42, -1.06, 10.26, 0.13026, 43, 10.83, 8.59, 0.29853, 44, 22.25, 0.79, 0.29968, 45, 28.79, -11.44, 0.07934, 46, 35.56, -16.34, 0.00768, 9, 37, -7.54, 9.43, 0.00771, 38, 2.99, 10.09, 0.27884, 39, 7.7, 13, 0.01279, 40, -5.13, 13.96, 0.04534, 42, 4.8, 10.07, 0.17097, 43, 16.38, 6.69, 0.28315, 44, 26.37, -3.38, 0.17746, 45, 30.53, -17.04, 0.02343, 46, 36.39, -22.15, 0.00031, 8, 37, -9.84, 6.5, 0.02473, 38, 0.35, 7.47, 0.28383, 39, 6.3, 9.56, 0.33381, 40, -1.89, 12.15, 0.05628, 42, 8.47, 10.68, 0.11254, 43, 20.07, 6.19, 0.13657, 44, 29.45, -5.47, 0.05004, 45, 32.3, -20.31, 0.0022, 9, 36, -8.94, -14.83, 0.00062, 37, -6.26, 7.83, 0.02228, 38, 4.07, 8.35, 0.30643, 39, 9.37, 11.83, 0.60877, 40, -3.76, 15.48, 0.00767, 42, 4.93, 12.12, 0.02644, 43, 17.1, 8.6, 0.0233, 44, 27.87, -1.98, 0.00447, 45, 32.5, -16.49, 2e-05, 8, 36, -12.44, -10.05, 0.01377, 37, -0.59, 9.54, 0.032, 38, 9.91, 9.35, 0.85362, 39, 14.36, 15.03, 0.00414, 40, -6.33, 20.82, 0.01445, 42, -0.4, 14.7, 0.04848, 43, 12.76, 12.63, 0.03107, 44, 25.78, 3.56, 0.00246, 8, 36, -13.62, -5.06, 0.08736, 37, 4.51, 8.99, 0.38501, 38, 14.9, 8.18, 0.45713, 39, 19.41, 15.9, 0.00284, 40, -6.58, 25.94, 0.01122, 42, -3.79, 18.55, 0.0382, 43, 10.65, 17.31, 0.01773, 44, 25.97, 8.68, 0.00051, 9, 35, -19.28, -5.96, 0.00064, 36, -11.05, 2.65, 0.21995, 37, 10.92, 4, 0.46503, 38, 20.65, 2.44, 0.29269, 39, 26.95, 12.87, 0.00072, 40, -2.64, 33.05, 0.00351, 42, -5.14, 26.56, 0.01283, 43, 11.7, 25.36, 0.00459, 44, 30.51, 15.42, 4e-05, 9, 35, -16.99, -0.11, 0.00683, 36, -5.89, 6.24, 0.41143, 37, 12.58, -2.07, 0.43417, 38, 21.55, -3.79, 0.14298, 39, 30.21, 7.5, 3e-05, 40, 3.09, 35.63, 0.00061, 42, -2.26, 32.15, 0.00301, 43, 16.09, 29.86, 0.00092, 44, 36.44, 17.5, 0, 8, 34, -24.05, 3.68, 0.00061, 35, -14.25, 6.48, 0.03143, 36, 0.06, 10.18, 0.60646, 37, 14.32, -8.99, 0.31136, 38, 22.43, -10.87, 0.04951, 40, 9.66, 38.43, 4e-05, 42, 1.14, 38.43, 0.00047, 43, 21.18, 34.87, 0.00011, 7, 34, -20.57, 9.4, 0.00662, 35, -9.94, 11.61, 0.0915, 36, 6.5, 12.03, 0.72335, 37, 13.92, -15.68, 0.16764, 38, 21.21, -17.46, 0.01083, 42, 5.95, 43.08, 5e-05, 43, 27.15, 37.91, 1e-05, 5, 34, -13.44, 13.32, 0.03222, 35, -2.3, 14.39, 0.18967, 36, 14.39, 10.09, 0.71326, 37, 9.46, -22.47, 0.06401, 38, 15.94, -23.66, 0.00084, 4, 34, -6.4, 14.37, 0.10084, 35, 4.82, 14.35, 0.29753, 36, 20.28, 6.09, 0.58602, 37, 3.72, -26.69, 0.01561, 4, 34, 1.31, 14.79, 0.23142, 35, 12.51, 13.58, 0.36354, 36, 26.24, 1.17, 0.40338, 37, -2.9, -30.67, 0.00166, 4, 34, 7.75, 12.55, 0.4177, 35, 18.53, 10.38, 0.35099, 36, 29.45, -4.85, 0.23128, 37, -9.64, -31.7, 3e-05, 3, 34, 14.3, 10.68, 0.62267, 35, 24.72, 7.53, 0.26815, 36, 33, -10.66, 0.10918, 3, 34, 20.97, 8.66, 0.79776, 35, 30.99, 4.51, 0.16092, 36, 36.54, -16.66, 0.04132, 3, 34, 24.45, 3.21, 0.91371, 35, 33.6, -1.4, 0.07434, 36, 35.41, -23.03, 0.01195, 3, 34, 23.23, -2.94, 0.97191, 35, 31.45, -7.3, 0.02565, 36, 30.34, -26.72, 0.00243, 3, 34, 20.93, -9.1, 0.99346, 35, 28.23, -13.03, 0.00627, 36, 24.47, -29.7, 0.00027, 3, 34, 17.67, -12.42, 0.99896, 35, 24.51, -15.82, 0.00103, 36, 19.82, -29.93, 1e-05, 2, 34, 14.74, -12.52, 0.9999, 35, 21.59, -15.47, 0.0001, 2, 34, 13.49, -8.87, 0.99951, 35, 20.92, -11.66, 0.00049, 3, 34, 9.95, -6.93, 0.99492, 35, 17.72, -9.21, 0.00504, 37, -20.69, -15.5, 3e-05, 5, 34, 7.24, -6.45, 0.97463, 35, 15.11, -8.32, 0.02492, 36, 16.2, -18.47, 5e-05, 37, -18.07, -14.66, 0.0004, 39, 4.21, -13.05, 0, 5, 34, 4.02, -7.88, 0.91751, 35, 11.71, -9.24, 0.07782, 36, 12.86, -17.34, 0.00166, 37, -15.89, -11.89, 0.00295, 39, 5.54, -9.79, 5e-05, 6, 34, 1, -8.27, 0.80266, 35, 8.67, -9.16, 0.17142, 36, 10.38, -15.57, 0.00945, 37, -13.4, -10.14, 0.01522, 38, -5.23, -8.61, 0.00069, 39, 7.45, -7.42, 0.00057, 6, 34, -2.47, -6.99, 0.6288, 35, 5.43, -7.36, 0.28349, 36, 8.69, -12.28, 0.02897, 37, -9.73, -9.64, 0.04715, 38, -1.53, -8.56, 0.00826, 39, 10.84, -5.93, 0.00333, 6, 34, -5.49, -5.29, 0.42523, 35, 2.71, -5.22, 0.36124, 36, 7.62, -8.99, 0.06004, 37, -6.27, -9.73, 0.10305, 38, 1.9, -9.08, 0.03258, 39, 14.2, -5.06, 0.01786, 6, 34, -8.82, -4.79, 0.23933, 35, -0.5, -4.22, 0.36104, 36, 5.51, -6.37, 0.0901, 37, -3.1, -8.62, 0.16418, 38, 5.18, -8.36, 0.08078, 39, 16.94, -3.12, 0.06457, 7, 34, -11.67, -7.09, 0.10752, 35, -3.67, -6.06, 0.28215, 36, 1.86, -6.13, 0.10231, 37, -1.66, -5.25, 0.19939, 38, 7.03, -5.2, 0.13789, 39, 17.4, 0.51, 0.17026, 40, 8.45, 22.06, 0.00048, 7, 34, -11.03, -8.99, 0.03644, 35, -3.33, -8.03, 0.17187, 36, 1.05, -7.95, 0.08813, 37, -3.11, -3.88, 0.18468, 38, 5.76, -3.66, 0.17433, 39, 15.63, 1.44, 0.33776, 40, 7.32, 20.41, 0.00678, 8, 34, -7.51, -9.78, 0.01181, 35, 0.02, -9.35, 0.10977, 36, 3.09, -10.92, 0.07976, 37, -6.59, -4.82, 0.18116, 38, 2.19, -4.16, 0.22811, 39, 12.54, -0.43, 0.33873, 40, 8.79, 17.12, 0.05057, 41, 5.36, 21.21, 9e-05, 8, 34, -2.46, -9.81, 0.00089, 35, 5.01, -10.15, 0.02078, 36, 6.79, -14.36, 0.02082, 37, -11.06, -7.15, 0.05209, 38, -2.54, -5.93, 0.34305, 39, 8.88, -3.9, 0.47247, 40, 11.79, 13.06, 0.08736, 41, 10.19, 19.75, 0.00255, 8, 34, 2.5, -10.8, 7e-05, 35, 9.76, -11.89, 0.00573, 36, 9.77, -18.45, 0.00787, 37, -15.91, -8.6, 0.02212, 38, -7.53, -6.77, 0.17134, 39, 4.62, -6.63, 0.55334, 40, 13.97, 8.5, 0.21769, 41, 14.67, 17.4, 0.02184, 8, 34, 6.29, -14.03, 1e-05, 35, 13.01, -15.66, 0.00119, 36, 10.36, -23.4, 0.00165, 37, -20.77, -7.51, 0.00561, 38, -12.22, -5.1, 0.02015, 39, -0.35, -6.92, 0.48887, 40, 13.66, 3.53, 0.39106, 41, 17.39, 13.23, 0.09147, 8, 35, 15.3, -20.53, 0.0001, 36, 9.55, -28.72, 0.00012, 37, -25.52, -4.97, 0.00052, 38, -16.62, -2, 0.00366, 39, -5.62, -5.79, 0.29594, 40, 11.88, -1.55, 0.46924, 41, 19.01, 8.09, 0.22913, 42, 27.78, 8.53, 0.00128, 5, 39, -9.71, -1.05, 0.13479, 40, 6.68, -5.03, 0.43141, 41, 16.92, 2.19, 0.41697, 42, 25.87, 2.57, 0.01673, 43, 34.33, -6.66, 9e-05, 6, 39, -12.04, 5.6, 0.04186, 40, -0.21, -6.54, 0.30103, 41, 12.29, -3.13, 0.58516, 42, 21.42, -2.9, 0.06301, 43, 28.47, -10.58, 0.00886, 44, 29.51, -24.23, 8e-05, 8, 39, -11.43, 12.2, 0.0075, 40, -6.68, -5.13, 0.15515, 41, 6.26, -5.87, 0.63816, 42, 15.48, -5.82, 0.15209, 43, 21.93, -11.64, 0.04085, 44, 23.19, -22.26, 0.00539, 45, 19.01, -32.34, 0.00079, 47, 40.94, -25, 6e-05, 7, 40, -12.46, 0.39, 0.05469, 41, -1.67, -4.9, 0.54867, 42, 7.52, -5.11, 0.24958, 43, 14.53, -8.63, 0.11104, 44, 17.9, -16.27, 0.02487, 45, 17.08, -24.59, 0.00994, 47, 37.89, -17.61, 0.00122, 8, 40, -16.11, 5.3, 0.01141, 41, -7.53, -3.15, 0.36319, 42, 1.61, -3.54, 0.30593, 43, 9.34, -5.39, 0.20052, 44, 14.69, -11.06, 0.0676, 45, 16.62, -18.48, 0.03892, 46, 22.43, -21.36, 0.00099, 47, 36.54, -11.64, 0.01144, 9, 40, -21.89, 6.98, 0.00054, 41, -13.17, -5.26, 0.18227, 42, -3.96, -5.83, 0.27752, 43, 3.34, -5.95, 0.26654, 44, 9.07, -8.88, 0.12233, 45, 12.64, -13.97, 0.09606, 46, 19.21, -16.27, 0.00396, 47, 31.93, -7.76, 0.0489, 48, 38.49, 15.89, 0.00189, 9, 41, -18.45, -8.46, 0.06313, 42, -9.14, -9.2, 0.19028, 43, -2.6, -7.66, 0.2636, 44, 3, -7.77, 0.1627, 45, 7.76, -10.18, 0.1626, 46, 14.99, -11.76, 0.0099, 47, 26.55, -4.74, 0.12789, 48, 32.38, 14.98, 0.01987, 49, 19.77, 37.62, 3e-05, 9, 41, -23.68, -12.47, 0.01368, 42, -14.24, -13.37, 0.09282, 43, -8.69, -10.15, 0.19697, 44, -3.57, -7.28, 0.16102, 45, 2.15, -6.73, 0.20408, 46, 10, -7.46, 0.01584, 47, 20.5, -2.15, 0.23499, 48, 26.01, 13.3, 0.07745, 49, 16.51, 31.9, 0.00316, 9, 41, -26.33, -18.64, 0.00065, 42, -16.69, -19.63, 0.03134, 43, -12.87, -15.41, 0.10767, 44, -9.66, -10.14, 0.12007, 45, -4.57, -6.46, 0.19079, 46, 3.41, -6.13, 0.01881, 47, 13.81, -2.87, 0.31528, 48, 21.18, 8.63, 0.19058, 49, 16.48, 25.18, 0.02481, 8, 42, -18.42, -27, 0.00565, 43, -16.68, -21.96, 0.04128, 44, -15.98, -14.29, 0.06534, 45, -12.1, -7.24, 0.13364, 46, -4.15, -5.7, 0.01584, 47, 6.48, -4.75, 0.31965, 48, 16.54, 2.64, 0.32593, 49, 17.51, 17.68, 0.09265, 9, 42, -21.7, -33.86, 0.00048, 43, -21.83, -27.56, 0.0097, 44, -23.09, -17.01, 0.02467, 45, -19.66, -6.39, 0.0671, 46, -11.48, -3.66, 0.0099, 47, -1.13, -5.01, 0.24066, 48, 10.7, -2.24, 0.41301, 49, 16.92, 10.09, 0.2303, 50, -0.03, 22.71, 0.00418, 9, 42, -26.93, -38.95, 1e-05, 43, -28.32, -30.89, 0.001, 44, -30.38, -17.11, 0.00557, 45, -26.18, -3.12, 0.02246, 46, -17.39, 0.6, 0.00396, 47, -8.05, -2.74, 0.13331, 48, 3.84, -4.7, 0.39261, 49, 13.87, 3.47, 0.412, 50, 5.95, 18.54, 0.02908, 8, 43, -34.15, -32.14, 5e-05, 44, -36.16, -15.63, 0.00045, 45, -30.63, 0.85, 0.00392, 46, -21.16, 5.24, 0.00099, 47, -13.04, 0.53, 0.04877, 48, -2.11, -5.17, 0.27948, 49, 10.05, -1.12, 0.56277, 50, 9.78, 13.96, 0.10357, 5, 47, -16.14, 5.73, 0.01046, 48, -7.74, -2.97, 0.14413, 49, 4.54, -3.61, 0.59329, 50, 11.25, 8.1, 0.24893, 51, 21.79, 2.97, 0.0032, 5, 48, -11.73, 0.89, 0.04951, 49, -1.01, -3.76, 0.48831, 50, 10.4, 2.6, 0.43792, 51, 19.5, -2.09, 0.02411, 52, 23.38, -15.25, 0.00015, 5, 48, -13.7, 6.3, 0.0093, 49, -6.25, -1.39, 0.30653, 50, 7.14, -2.13, 0.59415, 51, 15.08, -5.77, 0.08798, 52, 17.66, -15.78, 0.00204, 5, 49, -8.93, 2.75, 0.14342, 50, 2.59, -4.03, 0.62971, 51, 10.18, -6.37, 0.21287, 52, 13.28, -13.51, 0.01352, 33, 16.92, -18.5, 0.00049, 5, 49, -9.4, 6.89, 0.04506, 50, -1.57, -3.75, 0.52733, 51, 6.25, -4.98, 0.37511, 52, 10.82, -10.15, 0.04838, 33, 15.71, -14.51, 0.00413, 5, 49, -9.2, 11.31, 0.0082, 50, -5.89, -2.76, 0.34297, 51, 2.37, -2.86, 0.51018, 52, 8.81, -6.2, 0.11976, 33, 15.12, -10.12, 0.01888, 4, 50, -26.5, -10.43, 5e-05, 51, -19.55, -4.69, 0.01519, 52, -10.3, 4.67, 0.08378, 33, 0.68, 6.46, 0.90098, 5, 48, -1.01, 32.57, 0, 50, -21.64, -6.99, 0.00596, 51, -13.94, -2.68, 0.05614, 52, -4.54, 3.16, 0.16423, 33, 5.62, 3.13, 0.77367, 5, 48, -1.73, 27.44, 0, 50, -17.04, -4.61, 0.03589, 51, -8.87, -1.63, 0.14929, 52, 0.24, 1.16, 0.24412, 33, 9.47, -0.34, 0.5707, 6, 48, -1.28, 21.53, 0, 49, -8.45, 18.15, 0.00283, 50, -12.48, -0.81, 0.11732, 51, -3.46, 0.8, 0.26256, 52, 6.08, 0.1, 0.2732, 33, 14.62, -3.26, 0.34408, 6, 48, -4.22, 16.86, 0, 49, -7.17, 12.78, 0.02367, 50, -6.97, -0.51, 0.25467, 51, 1.93, -0.4, 0.33894, 52, 9.84, -3.93, 0.22123, 33, 16.85, -8.31, 0.16148, 7, 47, -6.22, 17.97, 0, 48, -7.43, 12.77, 0.0004, 49, -6.5, 7.63, 0.08965, 50, -1.78, -0.76, 0.40297, 51, 6.86, -2.04, 0.3218, 52, 12.98, -8.07, 0.13255, 33, 18.44, -13.26, 0.05264, 7, 47, -10.15, 17.15, 0, 48, -10.03, 9.72, 0.00917, 49, -6.13, 3.63, 0.22131, 50, 2.22, -1.11, 0.47951, 51, 10.61, -3.46, 0.22827, 52, 15.28, -11.36, 0.05149, 33, 19.52, -17.12, 0.01023, 8, 46, -17.56, 17.88, 3e-05, 47, -13.43, 13.68, 0.00039, 48, -10.49, 4.96, 0.04626, 49, -3.05, -0.02, 0.38984, 50, 6.36, 1.27, 0.43614, 51, 15.25, -2.29, 0.115, 52, 19.77, -13.01, 0.01224, 33, 23.21, -20.17, 0.00011, 7, 46, -19.06, 14.32, 0.00032, 47, -13.8, 9.83, 0.01366, 48, -8.41, 1.7, 0.12999, 49, 0.74, -0.82, 0.51861, 50, 7.82, 4.85, 0.29879, 51, 17.62, 0.77, 0.03862, 52, 23.45, -11.83, 0, 7, 46, -18.55, 10.44, 0.00286, 47, -12.13, 6.28, 0.06295, 48, -4.91, -0.07, 0.24835, 49, 4.45, 0.44, 0.52897, 50, 7.24, 8.72, 0.15029, 51, 18.11, 4.66, 0.00658, 52, 26.05, -8.9, 0, 6, 46, -17.08, 7.02, 0.02179, 47, -9.7, 3.47, 0.16992, 48, -1.27, -0.8, 0.34157, 49, 7.52, 2.53, 0.41632, 50, 5.73, 12.12, 0.0504, 52, 27.68, -5.56, 0, 9, 43, -28.91, -25.83, 0, 44, -28.66, -12.32, 1e-05, 45, -22.44, 0.34, 0.00458, 46, -13.15, 3.43, 0.07813, 47, -4.87, 1.23, 0.30774, 48, 3.92, 0.4, 0.35224, 49, 10.29, 7.08, 0.24779, 50, 1.75, 15.66, 0.00951, 52, 27.84, -0.24, 0, 7, 43, -25.21, -22.81, 6e-05, 44, -24, -11.26, 0.00122, 45, -17.82, -0.86, 0.0294, 46, -8.78, 1.51, 0.18602, 47, -0.12, 0.73, 0.40711, 48, 7.98, 2.91, 0.2685, 49, 11.33, 11.75, 0.1077, 7, 43, -21.43, -18.66, 0.00023, 44, -18.76, -9.22, 0.01703, 45, -12.24, -1.46, 0.09027, 46, -3.36, 0.03, 0.31057, 47, 5.49, 0.95, 0.40079, 48, 12.27, 6.53, 0.15014, 49, 11.74, 17.35, 0.03097, 7, 43, -19.04, -13.98, 0.00493, 44, -14.54, -6.1, 0.06877, 45, -7.05, -0.64, 0.18468, 46, 1.89, 0.02, 0.38727, 47, 10.5, 2.53, 0.29426, 48, 15.25, 10.86, 0.05534, 49, 10.74, 22.5, 0.00476, 7, 38, 10.68, 44.88, 0, 43, -15.91, -8.38, 0.02959, 44, -9.24, -2.48, 0.17413, 45, -0.68, 0.14, 0.26582, 46, 8.3, -0.23, 0.36189, 47, 16.69, 4.23, 0.15663, 48, 19.09, 16, 0.01194, 8, 38, 11.44, 39.21, 4e-05, 39, 4.09, 43.11, 0, 40, -35.46, 14.06, 1e-05, 43, -11.87, -4.32, 0.10188, 44, -3.82, -0.65, 0.30209, 45, 4.97, -0.72, 0.28626, 46, 13.75, -1.98, 0.2539, 47, 22.41, 4.21, 0.05582, 9, 38, 11.04, 33.19, 0.00042, 39, 6.08, 37.41, 4e-05, 40, -29.56, 15.34, 0.00032, 42, -15.17, -4.06, 0.00926, 43, -6.86, -0.98, 0.23452, 44, 2.16, 0.11, 0.38976, 45, 10.64, -2.8, 0.22603, 46, 19.01, -4.93, 0.12831, 47, 28.31, 2.99, 0.01134, 9, 38, 8.87, 27.29, 0.00185, 39, 6.39, 31.14, 0.00034, 40, -23.29, 14.88, 0.00266, 42, -9.98, -0.52, 0.04695, 43, -0.86, 0.89, 0.39595, 44, 8.36, -0.89, 0.37703, 45, 15.68, -6.54, 0.13162, 46, 23.4, -9.42, 0.04326, 47, 33.86, 0.03, 0.00033, 9, 38, 5.37, 22.32, 0.00513, 39, 5.11, 25.19, 0.00162, 40, -17.55, 12.89, 0.01929, 42, -4.25, 1.5, 0.1333, 43, 5.22, 1.14, 0.50575, 44, 13.91, -3.37, 0.2766, 45, 19.47, -11.3, 0.05047, 46, 26.37, -14.73, 0.00774, 47, 38.3, -4.12, 0.0001, 8, 38, 1.92, 19.02, 0.01015, 39, 3.22, 20.8, 0.01243, 40, -13.42, 10.48, 0.06498, 42, 0.49, 2.19, 0.25185, 43, 9.95, 0.41, 0.49985, 44, 17.82, -6.13, 0.14901, 45, 21.67, -15.55, 0.01171, 47, 41.1, -8, 2e-05, 7, 38, -1.96, 16.4, 0.01709, 39, 0.68, 16.87, 0.05069, 40, -9.83, 7.47, 0.14895, 42, 5.17, 2.07, 0.34671, 43, 14.39, -1.08, 0.37982, 44, 21.13, -9.44, 0.05659, 45, 23.09, -20.01, 0.00016, 6, 38, -6.2, 13.4, 0.03681, 39, -2.05, 12.46, 0.13243, 40, -5.79, 4.22, 0.23845, 42, 10.36, 2.05, 0.35837, 43, 19.34, -2.61, 0.22057, 44, 24.88, -13.02, 0.01337, 6, 38, -10.15, 9.36, 0.09284, 39, -4.11, 7.19, 0.24184, 40, -0.81, 1.54, 0.28784, 42, 15.92, 3.04, 0.28165, 43, 24.95, -3.29, 0.09428, 44, 29.6, -16.13, 0.00155, 7, 37, -19.02, 0.88, 0.00962, 38, -9.45, 3.02, 0.20658, 39, -0.98, 1.63, 0.3238, 40, 5.09, 3.96, 0.25939, 42, 19.03, 8.61, 0.16997, 43, 29.56, 1.12, 0.03051, 44, 35.69, -14.23, 0.00013, 8, 36, 2.05, -18.58, 0.00584, 37, -13.46, -1.28, 0.04499, 38, -4.2, 0.19, 0.35494, 39, 4.96, 1.08, 0.32601, 40, 6.36, 9.78, 0.17785, 42, 16.4, 13.96, 0.08079, 43, 28.61, 7.01, 0.00956, 44, 37.46, -8.54, 1e-05, 7, 36, -2.1, -14.17, 0.01417, 37, -7.92, 1.16, 0.04849, 38, 1.6, 1.94, 0.18547, 39, 9.61, 4.96, 0.70012, 40, 3.09, 14.88, 0.03509, 42, 10.66, 15.91, 0.0142, 43, 23.69, 10.55, 0.00245, 7, 36, -4.67, -10.1, 0.14931, 37, -3.23, 2.23, 0.27205, 38, 6.39, 2.42, 0.34416, 39, 13.83, 7.27, 0.16615, 40, 1.31, 19.35, 0.03954, 42, 6.48, 18.3, 0.02275, 43, 20.4, 14.06, 0.00603, 8, 35, -10.15, -8.57, 0.00796, 36, -4.92, -4.6, 0.27074, 37, 2.04, 0.64, 0.28683, 38, 11.42, 0.19, 0.36435, 39, 19.33, 7.19, 0.04849, 40, 2.06, 24.8, 0.00766, 42, 3.68, 23.03, 0.01078, 43, 19.1, 19.41, 0.00319, 8, 35, -10.84, -3.51, 0.04341, 36, -2.68, -0.02, 0.45035, 37, 5.62, -3.01, 0.27747, 38, 14.52, -3.87, 0.21046, 39, 23.78, 4.67, 0.01012, 40, 5.11, 28.9, 0.00172, 42, 3.51, 28.14, 0.00494, 43, 20.44, 24.34, 0.00153, 8, 34, -15.95, -0.42, 0.00963, 35, -6.88, 1.19, 0.12846, 36, 3.23, 1.68, 0.57271, 37, 5.25, -9.15, 0.20023, 38, 13.4, -9.91, 0.08643, 40, 11.23, 29.48, 0.00048, 42, 7.93, 32.41, 0.00156, 43, 25.92, 27.12, 0.0005, 8, 34, -11.37, 2.34, 0.05039, 35, -1.94, 3.22, 0.25382, 36, 8.47, 0.61, 0.56906, 37, 2.49, -13.73, 0.1035, 38, 10.11, -14.12, 0.02284, 40, 16.18, 27.48, 6e-05, 42, 13.06, 33.92, 0.00026, 43, 31.26, 27.07, 8e-05, 7, 34, -5.63, 3.45, 0.15609, 35, 3.91, 3.44, 0.3639, 36, 13.45, -2.46, 0.44166, 37, -2.06, -17.4, 0.03563, 38, 5.14, -17.21, 0.00272, 42, 18.89, 33.55, 0, 43, 36.73, 25, 0, 4, 34, 1.14, 3.3, 0.33439, 35, 10.58, 2.25, 0.39292, 36, 18.32, -7.16, 0.26619, 37, -8.12, -20.43, 0.0065, 3, 34, 6.82, 2.39, 0.55779, 35, 16.05, 0.48, 0.32098, 36, 21.88, -11.68, 0.12123, 3, 34, 11.38, 1.53, 0.76161, 35, 20.43, -1.06, 0.19786, 36, 24.66, -15.41, 0.04052, 3, 34, 15.83, -2.01, 0.90019, 35, 24.28, -5.24, 0.09028, 36, 25.53, -21.02, 0.00953, 3, 34, 18.01, -5.94, 0.95405, 35, 25.84, -9.46, 0.04334, 36, 24.47, -25.39, 0.00261], "hull": 78, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 0, 154, 12, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 208, 210, 210, 212, 212, 214, 214, 216, 216, 218, 218, 220, 220, 222, 222, 224, 224, 226, 226, 228, 228, 230], "width": 92, "height": 105}}, "loong_zhua2": {"loong_zhua": {"type": "mesh", "uvs": [0.65225, 0.29898, 0.80751, 0.32998, 0.98119, 0.31698, 1, 0.18298, 0.97725, 0.03398, 0.73778, 0.00898, 0.54304, 0, 0.43383, 0.00098, 0.3391, 0.12298, 0.34451, 0.30719, 0.32662, 0.42959, 0.26556, 0.49039, 0.11714, 0.48159, 0, 0.55919, 0, 0.78434, 0, 0.98594, 0.27328, 1, 0.50381, 1, 0.60802, 0.86594, 0.6238, 0.68834, 0.57012, 0.58994, 0.59854, 0.38354, 0.76837, 0.14705, 0.54845, 0.14762, 0.46466, 0.3217, 0.46432, 0.51852, 0.38638, 0.72125, 0.19859, 0.84829], "triangles": [22, 5, 4, 23, 6, 5, 23, 5, 22, 22, 4, 3, 0, 23, 22, 8, 24, 9, 7, 6, 23, 23, 8, 7, 3, 1, 22, 1, 0, 22, 23, 24, 8, 24, 23, 0, 3, 2, 1, 21, 24, 0, 10, 9, 24, 25, 10, 24, 25, 24, 21, 11, 10, 25, 20, 25, 21, 26, 11, 25, 26, 25, 20, 26, 20, 19, 13, 26, 14, 11, 13, 12, 26, 13, 11, 27, 14, 26, 18, 26, 19, 15, 14, 27, 17, 16, 27, 26, 17, 27, 15, 27, 16, 18, 17, 26], "vertices": [7.22, 37.1, 10.27, 37.19, 13.34, 38.44, 12.73, 41.75, 11.26, 45.2, 6.72, 44.51, 3.11, 43.68, 1.13, 43.06, 0.27, 39.63, 1.67, 35.24, 2.22, 32.21, 1.53, 30.43, -1.23, 29.84, -2.82, 27.34, -1.22, 21.95, 0.21, 17.11, 5.29, 18.25, 9.49, 19.49, 10.44, 23.27, 9.47, 27.61, 7.79, 29.68, 6.84, 34.78, 8.26, 41.37, 4.26, 40.17, 3.97, 35.54, 5.36, 30.82, 5.37, 25.54, 2.85, 21.48], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 0, 42, 6, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54], "width": 19, "height": 25}}, "mingzu_bg01": {"mingzu_bg01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [376, 343.69, -376, 343.69, -376, 1391.69, 376, 1391.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 752, "height": 1048}}, "mingzu_bg02": {"mingzu_bg02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-2.42, -15.56, -330.42, -15.56, -330.42, 76.44, -2.42, 76.44], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 328, "height": 92}}, "mingzu_bg03": {"mingzu_bg03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-143, 762.69, -295, 762.69, -295, 809.69, -143, 809.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 152, "height": 47}}, "mingzu_bg04": {"mingzu_bg04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [12.09, -39.91, -284.91, -39.91, -284.91, 52.09, 12.09, 52.09], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 297, "height": 92}}, "mingzu_bg05": {"mingzu_bg05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [259.7, -23.49, -5.3, -23.49, -5.3, 130.51, 259.7, 130.51], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 265, "height": 154}}, "mingzu_bg06": {"mingzu_bg06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [339.03, -12.15, -13.97, -12.15, -13.97, 240.85, 339.03, 240.85], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 353, "height": 253}}, "mingzu_bg07": {"mingzu_bg07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [370, 705.69, 234, 705.69, 234, 767.69, 370, 767.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 136, "height": 62}}, "mingzu_bg08": {"mingzu_bg08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-20.19, 35.59, 403.46, 52.88, 414.14, -208.9, -9.5, -226.19], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 424, "height": 262}}, "mingzu_bg09": {"mingzu_bg09": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-1.86, -8.05, -356.86, -8.05, -356.86, 389.95, -1.86, 389.95], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 355, "height": 398}}, "mingzu_bg2": {"mingzu_bg02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-2.42, -15.56, -330.42, -15.56, -330.42, 76.44, -2.42, 76.44], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 328, "height": 92}}, "mingzu_bg4": {"mingzu_bg04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [12.09, -39.91, -284.91, -39.91, -284.91, 52.09, 12.09, 52.09], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 297, "height": 92}}, "mingzu_bg5": {"mingzu_bg05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [259.7, -23.49, -5.3, -23.49, -5.3, 130.51, 259.7, 130.51], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 265, "height": 154}}, "mingzu_bg6": {"mingzu_bg06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [339.03, -12.15, -13.97, -12.15, -13.97, 240.85, 339.03, 240.85], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 353, "height": 253}}, "mingzu_bg8": {"mingzu_bg08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-20.19, 35.59, 403.46, 52.88, 414.14, -208.9, -9.5, -226.19], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 424, "height": 262}}, "mingzu_bg9": {"mingzu_bg09": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1.54, -6.49, -353.46, -6.49, -353.46, 391.51, 1.54, 391.51], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 355, "height": 398}}, "mingzu_bg014": {"mingzu_bg014": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-193, 542.69, -376, 542.69, -376, 782.69, -193, 782.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 183, "height": 240}}, "eye": {"eye": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.62, -15.92, 18.13, 29.07, 51.13, 28.7, 50.62, -16.3], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 45, "height": 33}}, "mingzu_bg10": {"mingzu_bg010": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [308.08, -29.14, -13.57, -14.18, -7.57, 114.68, 314.08, 99.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 322, "height": 129}}, "mingzu_bg11": {"mingzu_bg011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [311.79, -10.57, -20.21, -10.57, -20.21, 342.43, 311.79, 342.43], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 332, "height": 353}}, "hui1": {"hui1": {"type": "mesh", "uvs": [0, 0, 0.31192, 0.00941, 0.73687, 0.128, 0.96906, 0.33298, 1, 0.53457, 1, 0.71583, 0.94278, 0.809, 0.67335, 0.80053, 0.35135, 0.79376, 0, 0.79884], "triangles": [7, 8, 3, 8, 1, 2, 8, 2, 3, 3, 4, 7, 9, 0, 1, 9, 1, 8, 5, 7, 4, 6, 7, 5], "vertices": [-376, 1194.78, -290.85, 1190.86, -174.83, 1149, -111.45, 1076.64, -103, 1005.48, -103, 941.5, -118.62, 908.61, -192.18, 911.6, -280.08, 913.99, -376, 912.19], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 273, "height": 353}}, "hui2": {"hui2": {"type": "mesh", "uvs": [1, 0, 0.52439, 0, 0.05647, 0.17335, 0, 0.45424, 0, 0.82452, 1, 0.8008], "triangles": [1, 0, 5, 3, 5, 4, 1, 3, 2, 5, 3, 1], "vertices": [376, 1118.19, 166.73, 1118.19, -39.15, 1053.18, -64, 947.84, -64, 808.99, 376, 817.88], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 440, "height": 375}}, "chiblong2": {"chiblong": {"type": "mesh", "uvs": [0.17177, 0.97706, 0.26736, 0.99991, 0.36811, 0.98849, 0.42752, 0.87101, 0.49986, 0.76333, 0.67294, 0.70133, 0.79177, 0.74212, 0.79177, 0.83675, 0.95969, 0.82859, 0.99585, 0.68175, 0.85894, 0.49575, 0.72202, 0.28365, 0.52569, 0.12865, 0.27769, 0, 0, 0, 0.00605, 0.11392, 0.01419, 0.26733, 0.07107, 0.40675, 0.12269, 0.53328, 0.15159, 0.66888, 0.17694, 0.7878, 0.16402, 0.89875, 0.29836, 0.12538, 0.42236, 0.29507, 0.51019, 0.51533, 0.35519, 0.70949, 0.29577, 0.84164, 0.26994, 0.92649, 0.67552, 0.5088, 0.85119, 0.64422, 0.88219, 0.76333], "triangles": [17, 16, 23, 16, 22, 23, 23, 22, 12, 16, 15, 22, 22, 14, 13, 12, 22, 13, 22, 15, 14, 28, 11, 10, 28, 23, 11, 23, 12, 11, 7, 30, 8, 7, 6, 30, 8, 30, 9, 9, 30, 29, 30, 6, 29, 6, 5, 29, 5, 25, 24, 24, 25, 18, 5, 28, 29, 5, 24, 28, 29, 10, 9, 29, 28, 10, 18, 17, 24, 17, 23, 24, 24, 23, 28, 1, 27, 2, 1, 0, 27, 2, 27, 3, 0, 21, 27, 27, 26, 3, 27, 21, 26, 21, 20, 26, 3, 26, 4, 26, 25, 4, 26, 20, 25, 20, 19, 25, 4, 25, 5, 25, 19, 18], "vertices": [1, 74, -4.54, 4.21, 1, 1, 74, -5.08, -0.66, 1, 1, 74, -3.04, -5.13, 1, 1, 74, 6.32, -5.68, 1, 2, 74, 15.11, -7, 0.70373, 75, -0.79, -8.21, 0.29627, 2, 74, 21.74, -13.88, 0.05362, 75, 8.2, -11.43, 0.94638, 3, 74, 20.15, -20.18, 0.00185, 75, 9.57, -17.77, 0.99766, 76, -19.93, -2.06, 0.00048, 1, 75, 4.12, -22.47, 1, 1, 75, 9.85, -28.17, 1, 2, 75, 19.44, -22.2, 0.98246, 76, -18.2, -12.74, 0.01754, 2, 75, 25.87, -8, 0.46943, 76, -2.8, -10.29, 0.53057, 1, 76, 14.5, -8.39, 1, 1, 77, 5.35, -5.37, 1, 1, 77, 20.56, -2.89, 1, 2, 75, 27.52, 47.83, 0.00042, 77, 29.36, 7.12, 0.99958, 3, 75, 21.15, 41.96, 0.01902, 76, 36.32, 21.13, 0.02936, 77, 22.67, 12.62, 0.95163, 4, 74, 45.83, 24.94, 0, 75, 12.57, 34.05, 0.12546, 76, 25, 23.95, 0.1851, 77, 13.65, 20.03, 0.68944, 4, 74, 36.25, 19.67, 0.01478, 75, 6.32, 25.07, 0.34902, 76, 14.06, 24.23, 0.30721, 77, 3.9, 24.98, 0.32899, 4, 74, 27.55, 14.88, 0.14462, 75, 0.65, 16.92, 0.56907, 76, 4.14, 24.48, 0.19096, 77, -4.96, 29.47, 0.09535, 4, 74, 17.91, 10.97, 0.69098, 75, -6.26, 9.15, 0.27036, 76, -6.15, 25.97, 0.02853, 77, -13.61, 35.23, 0.01013, 4, 74, 9.46, 7.55, 0.99918, 75, -12.31, 2.33, 0.00068, 76, -15.18, 27.27, 0.00012, 77, -21.2, 40.29, 2e-05, 1, 74, 1.14, 6.05, 1, 3, 75, 29.64, 30.76, 0.00505, 76, 31.64, 7.88, 0.01248, 77, 12.75, 2.66, 0.98247, 3, 75, 23.75, 17.83, 0.05167, 76, 17.61, 5.69, 0.67367, 77, -0.87, 6.71, 0.27466, 3, 75, 13.82, 3.71, 0.80972, 76, 0.35, 6.22, 0.18552, 77, -16.22, 14.6, 0.00477, 3, 74, 17.35, 0.74, 0.99944, 76, -11.8, 17.41, 0.00042, 77, -22.39, 29.93, 0.00013, 1, 74, 6.91, 1.01, 1, 1, 74, 0.36, 0.6, 1, 2, 75, 19.37, -1.98, 0.6207, 76, -1.34, -1.55, 0.3793, 3, 74, 28.07, -21.09, 0.00015, 75, 17.07, -15.08, 0.95039, 76, -13.55, -6.84, 0.04946, 2, 75, 11.18, -22.11, 0.99977, 76, -22.67, -5.79, 0.00023], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 40, 42, 0, 42, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 28, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 56, 58, 58, 60], "width": 48, "height": 76}}, "chiblong": {"chiblong": {"type": "mesh", "uvs": [0.17177, 0.97706, 0.26736, 0.99991, 0.36811, 0.98849, 0.42752, 0.87101, 0.49986, 0.76333, 0.67294, 0.70133, 0.79177, 0.74212, 0.79177, 0.83675, 0.95969, 0.82859, 0.99585, 0.68175, 0.85894, 0.49575, 0.72202, 0.28365, 0.52569, 0.12865, 0.27769, 0, 0, 0, 0.00605, 0.11392, 0.01419, 0.26733, 0.07107, 0.40675, 0.12269, 0.53328, 0.15159, 0.66888, 0.17694, 0.7878, 0.16402, 0.89875, 0.29836, 0.12538, 0.42236, 0.29507, 0.51019, 0.51533, 0.35519, 0.70949, 0.29577, 0.84164, 0.26994, 0.92649, 0.67552, 0.5088, 0.85119, 0.64422, 0.88219, 0.76333], "triangles": [17, 16, 23, 16, 22, 23, 23, 22, 12, 16, 15, 22, 22, 14, 13, 12, 22, 13, 22, 15, 14, 28, 11, 10, 28, 23, 11, 23, 12, 11, 7, 30, 8, 7, 6, 30, 8, 30, 9, 9, 30, 29, 30, 6, 29, 6, 5, 29, 5, 25, 24, 24, 25, 18, 5, 28, 29, 5, 24, 28, 29, 10, 9, 29, 28, 10, 18, 17, 24, 17, 23, 24, 24, 23, 28, 1, 27, 2, 1, 0, 27, 2, 27, 3, 0, 21, 27, 27, 26, 3, 27, 21, 26, 21, 20, 26, 3, 26, 4, 26, 25, 4, 26, 20, 25, 20, 19, 25, 4, 25, 5, 25, 19, 18], "vertices": [1, 58, -4.54, 4.21, 1, 1, 58, -5.08, -0.66, 1, 1, 58, -3.04, -5.13, 1, 1, 58, 6.32, -5.68, 1, 2, 58, 15.11, -7, 0.70373, 59, -0.79, -8.21, 0.29627, 2, 58, 21.74, -13.88, 0.05362, 59, 8.2, -11.43, 0.94638, 3, 58, 20.15, -20.18, 0.00185, 59, 9.57, -17.77, 0.99766, 60, -19.93, -2.06, 0.00048, 1, 59, 4.12, -22.47, 1, 1, 59, 9.85, -28.17, 1, 2, 59, 19.44, -22.2, 0.98246, 60, -18.2, -12.74, 0.01754, 2, 59, 25.87, -8, 0.46943, 60, -2.8, -10.29, 0.53057, 1, 60, 14.5, -8.39, 1, 1, 61, 5.35, -5.37, 1, 1, 61, 20.56, -2.89, 1, 2, 59, 27.52, 47.83, 0.00042, 61, 29.36, 7.12, 0.99958, 3, 59, 21.15, 41.96, 0.01902, 60, 36.32, 21.13, 0.02936, 61, 22.67, 12.62, 0.95163, 4, 58, 45.83, 24.94, 0, 59, 12.57, 34.05, 0.12546, 60, 25, 23.95, 0.1851, 61, 13.65, 20.03, 0.68944, 4, 58, 36.25, 19.67, 0.01478, 59, 6.32, 25.07, 0.34902, 60, 14.06, 24.23, 0.30721, 61, 3.9, 24.98, 0.32899, 4, 58, 27.55, 14.88, 0.14462, 59, 0.65, 16.92, 0.56907, 60, 4.14, 24.48, 0.19096, 61, -4.96, 29.47, 0.09535, 4, 58, 17.91, 10.97, 0.69098, 59, -6.26, 9.15, 0.27036, 60, -6.15, 25.97, 0.02853, 61, -13.61, 35.23, 0.01013, 4, 58, 9.46, 7.55, 0.99918, 59, -12.31, 2.33, 0.00068, 60, -15.18, 27.27, 0.00012, 61, -21.2, 40.29, 2e-05, 1, 58, 1.14, 6.05, 1, 3, 59, 29.64, 30.76, 0.00505, 60, 31.64, 7.88, 0.01248, 61, 12.75, 2.66, 0.98247, 3, 59, 23.75, 17.83, 0.05167, 60, 17.61, 5.69, 0.67367, 61, -0.87, 6.71, 0.27466, 3, 59, 13.82, 3.71, 0.80972, 60, 0.35, 6.22, 0.18552, 61, -16.22, 14.6, 0.00477, 3, 58, 17.35, 0.74, 0.99944, 60, -11.8, 17.41, 0.00042, 61, -22.39, 29.93, 0.00013, 1, 58, 6.91, 1.01, 1, 1, 58, 0.36, 0.6, 1, 2, 59, 19.37, -1.98, 0.6207, 60, -1.34, -1.55, 0.3793, 3, 58, 28.07, -21.09, 0.00015, 59, 17.07, -15.08, 0.95039, 60, -13.55, -6.84, 0.04946, 2, 59, 11.18, -22.11, 0.99977, 60, -22.67, -5.79, 0.00023], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 40, 42, 0, 42, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 28, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 56, 58, 58, 60], "width": 48, "height": 76}}, "mingzu_bg010": {"mingzu_bg010": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [308.08, -29.14, -13.57, -14.18, -7.57, 114.68, 314.08, 99.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 322, "height": 129}}, "mingzu_bg011": {"mingzu_bg011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [311.79, -10.57, -20.21, -10.57, -20.21, 342.43, 311.79, 342.43], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 332, "height": 353}}, "mingzu_bg012": {"mingzu_bg012": {"type": "mesh", "uvs": [0.17105, 0.755, 0.15679, 0.65704, 0.06622, 0.55296, 0.08509, 0.38561, 0.00207, 0.19377, 0.04924, 0, 0.18886, 0.14683, 0.3266, 0.38561, 0.341, 0.51621, 0.35113, 0.60806, 0.36622, 0.74683, 0.46245, 0.63867, 0.63226, 0.60194, 0.84169, 0.59785, 1, 0.64855, 0.99898, 0.72532, 0.8393, 0.73195, 0.68586, 0.76762, 0.58477, 0.80252, 0.62607, 0.84592, 0.69158, 0.93963, 0.64298, 0.98535, 0.56901, 0.99335, 0.49399, 0.91106, 0.38094, 0.92249, 0.25732, 0.91335, 0.17068, 0.87335, 0.07135, 0.85735, 0.04388, 0.79792, 0.07981, 0.74649, 0.11393, 0.17133, 0.21718, 0.38227, 0.24156, 0.52186, 0.2559, 0.65524, 0.2774, 0.77622, 0.43944, 0.77312, 0.51401, 0.71108, 0.64737, 0.68937, 0.83834, 0.67075, 0.16125, 0.82441, 0.28026, 0.83217, 0.3706, 0.82906, 0.48819, 0.84768, 0.57423, 0.89266], "triangles": [30, 5, 6, 4, 5, 30, 7, 31, 6, 30, 6, 31, 3, 4, 30, 3, 30, 31, 32, 31, 7, 32, 7, 8, 3, 32, 2, 32, 3, 31, 9, 33, 32, 9, 32, 8, 1, 32, 33, 1, 2, 32, 33, 9, 10, 0, 1, 33, 34, 33, 10, 0, 33, 34, 34, 10, 41, 13, 37, 12, 38, 13, 14, 38, 37, 13, 15, 38, 14, 16, 38, 15, 17, 37, 38, 16, 17, 38, 36, 11, 12, 37, 36, 12, 35, 10, 11, 36, 35, 11, 18, 36, 37, 18, 37, 17, 42, 35, 36, 39, 0, 34, 28, 29, 39, 27, 28, 39, 27, 39, 26, 26, 39, 40, 25, 26, 40, 43, 42, 18, 43, 18, 19, 41, 35, 42, 23, 42, 43, 24, 41, 42, 24, 42, 23, 40, 41, 24, 43, 19, 20, 21, 43, 20, 22, 23, 43, 22, 43, 21, 25, 40, 24, 41, 10, 35, 40, 34, 41, 39, 34, 40, 39, 29, 0, 42, 36, 18], "vertices": [1, 83, 13.39, -5.28, 1, 2, 79, 5.42, 5.88, 0.89007, 80, -3.94, 7.74, 0.10993, 2, 79, 11.04, 10.05, 0.3612, 80, 2.79, 9.69, 0.6388, 2, 79, 19.07, 8.1, 0.01295, 80, 9.63, 5.06, 0.98705, 1, 80, 20, 4.69, 1, 1, 80, 27.31, -1.86, 1, 1, 80, 17.54, -5.17, 1, 2, 79, 17.58, -4.61, 0.01108, 80, 3.79, -6.33, 0.98892, 2, 79, 11.13, -4.62, 0.71647, 80, -2.25, -4.1, 0.28353, 2, 81, -2.19, 9.45, 0.00233, 79, 6.6, -4.63, 0.99767, 3, 83, 3.04, -5.57, 0.1701, 81, -4.33, 2.95, 0.31497, 79, -0.25, -4.63, 0.51493, 2, 81, 2.53, 5.6, 0.99872, 79, 4.42, -10.31, 0.00128, 2, 81, 11.45, 3.44, 0.59153, 82, -0.74, 3.42, 0.40847, 1, 82, 10.36, 3.2, 1, 1, 82, 18.65, 0.4, 1, 1, 82, 18.45, -3.36, 1, 1, 82, 9.98, -3.36, 1, 3, 83, -13.89, -4.37, 0.00174, 81, 10.6, -5.12, 0.38732, 82, 1.79, -4.8, 0.61094, 1, 83, -8.51, -2.72, 1, 1, 83, -10.68, -0.57, 1, 1, 83, -14.1, 4.06, 1, 1, 83, -11.5, 6.27, 1, 1, 83, -7.58, 6.62, 1, 1, 83, -3.65, 2.55, 1, 1, 83, 2.35, 3.04, 1, 1, 83, 8.9, 2.53, 1, 1, 83, 13.47, 0.52, 1, 1, 83, 18.73, -0.32, 1, 1, 83, 20.15, -3.25, 1, 1, 83, 18.22, -5.75, 1, 1, 80, 18.28, -1.09, 1, 1, 80, 6.58, -1.25, 1, 2, 79, 11.47, 0.64, 0.64524, 80, -0.09, 0.72, 0.35476, 2, 79, 4.89, 0.65, 0.99797, 80, -6.26, 3.02, 0.00203, 3, 83, 7.76, -4.18, 0.06077, 81, -9.21, 3.62, 0.00511, 79, -1.13, 0.21, 0.93413, 3, 83, -0.83, -4.24, 0.15106, 81, -1.36, 0.14, 0.80346, 79, -1.98, -8.33, 0.04548, 1, 81, 3.51, 1.23, 1, 3, 83, -11.89, -8.23, 5e-05, 81, 10.37, -0.78, 0.66453, 82, -0.1, -0.89, 0.33542, 1, 82, 10.05, -0.36, 1, 1, 83, 13.94, -1.88, 1, 1, 83, 7.64, -1.44, 1, 1, 83, 2.85, -1.54, 1, 1, 83, -3.37, -0.56, 1, 1, 83, -7.91, 1.69, 1], "hull": 30, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 0, 58, 10, 60, 60, 62, 62, 64, 14, 16, 16, 18, 64, 66, 66, 68, 70, 72, 72, 74, 74, 76, 76, 30, 56, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 42], "width": 53, "height": 49}}, "mingzu_bg013": {"mingzu_bg013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [376, 948.69, 199, 948.69, 199, 1227.69, 376, 1227.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 177, "height": 279}}, "loong_zhua3": {"loong_zhua": {"type": "mesh", "uvs": [0.42617, 0, 0.643, 0, 0.87458, 0.01184, 1, 0.06144, 1, 0.20384, 0.92932, 0.31904, 0.763, 0.32064, 0.66827, 0.29344, 0.6009, 0.25504, 0.59037, 0.37824, 0.55669, 0.57824, 0.58616, 0.73504, 0.57915, 0.91858, 0.42126, 0.99778, 0.1856, 0.97414, 0, 0.93814, 0, 0.75574, 0, 0.56214, 0.13191, 0.48294, 0.28138, 0.45974, 0.32875, 0.30427, 0.35717, 0.10907, 0.83209, 0.15116, 0.64399, 0.11487, 0.48816, 0.11343, 0.46119, 0.24174, 0.45099, 0.36667, 0.42253, 0.52701, 0.28964, 0.73012, 0.13197, 0.85031], "triangles": [24, 0, 1, 21, 0, 24, 23, 1, 2, 24, 1, 23, 22, 23, 2, 22, 2, 3, 22, 3, 4, 25, 21, 24, 8, 24, 23, 7, 8, 23, 25, 24, 8, 22, 7, 23, 20, 21, 25, 5, 22, 4, 6, 7, 22, 5, 6, 22, 26, 20, 25, 26, 25, 8, 9, 26, 8, 19, 20, 26, 27, 19, 26, 27, 26, 9, 10, 27, 9, 28, 19, 27, 28, 27, 10, 18, 19, 28, 17, 18, 28, 28, 10, 11, 16, 17, 28, 29, 16, 28, 12, 28, 11, 15, 16, 29, 14, 29, 28, 13, 14, 28, 15, 29, 14, 12, 13, 28], "vertices": [-4.7, 53.13, -0.58, 53.13, 3.82, 52.84, 6.2, 51.6, 6.2, 48.04, 4.86, 45.16, 1.7, 45.12, -0.1, 45.8, -1.38, 46.76, -1.58, 43.68, -2.22, 38.68, -1.66, 34.76, -1.8, 30.17, -4.8, 28.19, -9.27, 28.78, -12.8, 29.68, -12.8, 34.24, -12.8, 39.08, -10.29, 41.06, -7.45, 41.64, -6.55, 45.53, -6.01, 50.41, 3.01, 49.35, -0.56, 50.26, -3.52, 50.3, -4.04, 47.09, -4.23, 43.97, -4.77, 39.96, -7.3, 34.88, -10.29, 31.88], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 0, 42, 44, 8, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58], "width": 19, "height": 25}}}}], "animations": {"mz_defeat": {"slots": {"mingzu_bg05": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 8, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 10, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 12, "color": "ffffff00"}]}, "mingzu_bg02": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 8, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 10, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 12, "color": "ffffff00"}]}, "mingzu_bg010": {"color": [{"color": "ffffffac", "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.7333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "color": "ffffff00", "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 4, "color": "ffffffac", "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 4.7333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 6.7333, "color": "ffffff00", "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 8, "color": "ffffffac", "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 8.7333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 10.7333, "color": "ffffff00", "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 12, "color": "ffffffac"}]}, "mingzu_bg10": {"color": [{"color": "ffffff52", "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.7333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "color": "ffffffff", "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 4, "color": "ffffff52", "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 4.7333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6.7333, "color": "ffffffff", "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 8, "color": "ffffff52", "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 8.7333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 10.7333, "color": "ffffffff", "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 12, "color": "ffffff52"}]}, "mingzu_bg2": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 6, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 8, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 10, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 12, "color": "ffffffff"}]}, "mingzu_bg08": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 8, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 10, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 12, "color": "ffffff00"}]}, "mingzu_bg011": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 8, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 10, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 12, "color": "ffffff00"}]}, "hui2": {"color": [{"color": "ffffffd8", "curve": 0.382, "c2": 0.55, "c3": 0.741}, {"time": 0.7667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "color": "ffffff8f", "curve": 0.245, "c3": 0.64, "c4": 0.57}, {"time": 4, "color": "ffffffd8", "curve": 0.382, "c2": 0.55, "c3": 0.741}, {"time": 4.7667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "color": "ffffff8f", "curve": 0.245, "c3": 0.64, "c4": 0.57}, {"time": 8, "color": "ffffffd8", "curve": 0.382, "c2": 0.55, "c3": 0.741}, {"time": 8.7667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 10.7667, "color": "ffffff8f", "curve": 0.245, "c3": 0.64, "c4": 0.57}, {"time": 12, "color": "ffffffd8"}]}, "mingzu_bg6": {"color": [{"color": "ffffff52", "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 1.2667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "color": "ffffff00", "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 4, "color": "ffffff52", "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 5.2667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "color": "ffffff00", "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 8, "color": "ffffff52", "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 9.2667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 11.2667, "color": "ffffff00", "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 12, "color": "ffffff52"}]}, "mingzu_bg04": {"color": [{"color": "ffffff4d", "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.7, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.7, "color": "ffffffff", "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 4, "color": "ffffff4d", "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 4.7, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6.7, "color": "ffffffff", "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 8, "color": "ffffff4d", "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 8.7, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 10.7, "color": "ffffffff", "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 12, "color": "ffffff4d"}]}, "mingzu_bg11": {"color": [{"color": "fffffffd", "curve": 0.257, "c2": 0.03, "c3": 0.751}, {"time": 1.9667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "color": "ffffffff", "curve": 0.328, "c3": 0.661, "c4": 0.34}, {"time": 4, "color": "fffffffd", "curve": 0.257, "c2": 0.03, "c3": 0.751}, {"time": 5.9667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "color": "ffffffff", "curve": 0.328, "c3": 0.661, "c4": 0.34}, {"time": 8, "color": "fffffffd", "curve": 0.257, "c2": 0.03, "c3": 0.751}, {"time": 9.9667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 11.9667, "color": "ffffffff", "curve": 0.328, "c3": 0.661, "c4": 0.34}, {"time": 12, "color": "fffffffd"}]}, "eye": {"attachment": [{"time": 1, "name": "eye"}, {"time": 1.1667, "name": null}, {"time": 3, "name": "eye"}, {"time": 3.1667, "name": null}, {"time": 5, "name": "eye"}, {"time": 5.1667, "name": null}, {"time": 7, "name": "eye"}, {"time": 7.1667, "name": null}, {"time": 9, "name": "eye"}, {"time": 9.1667, "name": null}, {"time": 11, "name": "eye"}, {"time": 11.1667, "name": null}]}, "mingzu_bg012": {"color": [{"color": "ffffff9a", "curve": 0.25, "c3": 0.75}, {"time": 2.2, "color": "ffffff00"}]}, "hui1": {"color": [{"color": "ffffffcc", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffff8f", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffffcc", "curve": 0.25, "c3": 0.75}, {"time": 6, "color": "ffffff8f", "curve": 0.25, "c3": 0.75}, {"time": 8, "color": "ffffffcc", "curve": 0.25, "c3": 0.75}, {"time": 10, "color": "ffffff8f", "curve": 0.25, "c3": 0.75}, {"time": 12, "color": "ffffffcc"}]}, "mingzu_bg8": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 6, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 8, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 10, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 12, "color": "ffffffff"}]}, "mingzu_bg4": {"color": [{"color": "ffffffb1", "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.7, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.7, "color": "ffffff00", "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 4, "color": "ffffffb1", "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 4.7, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 6.7, "color": "ffffff00", "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 8, "color": "ffffffb1", "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 8.7, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 10.7, "color": "ffffff00", "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 12, "color": "ffffffb1"}]}, "mingzu_bg9": {"color": [{"color": "ffffffcb", "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.5333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "color": "ffffff00", "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 4, "color": "ffffffcb", "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 4.5333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 6.5333, "color": "ffffff00", "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 8, "color": "ffffffcb", "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 8.5333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 10.5333, "color": "ffffff00", "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 12, "color": "ffffffcb"}]}, "mingzu_bg09": {"color": [{"color": "ffffff33", "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.5333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "color": "ffffffff", "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 4, "color": "ffffff33", "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 4.5333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6.5333, "color": "ffffffff", "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 8, "color": "ffffff33", "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 8.5333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 10.5333, "color": "ffffffff", "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 12, "color": "ffffff33"}]}, "mingzu_bg5": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 6, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 8, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 10, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 12, "color": "ffffffff"}]}, "mingzu_bg06": {"color": [{"color": "ffffffac", "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 1.2667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "color": "ffffffff", "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 4, "color": "ffffffac", "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 5.2667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "color": "ffffffff", "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 8, "color": "ffffffac", "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 9.2667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 11.2667, "color": "ffffffff", "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 12, "color": "ffffffac"}]}}, "bones": {"cloud01": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 3.9667, "x": -9.14, "y": 3.05, "curve": "stepped"}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "x": -9.14, "y": 3.05, "curve": "stepped"}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 11.9667, "x": -9.14, "y": 3.05, "curve": "stepped"}, {"time": 12}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 3.9667, "x": 1.074, "y": 1.074, "curve": "stepped"}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "x": 1.074, "y": 1.074, "curve": "stepped"}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 11.9667, "x": 1.074, "y": 1.074, "curve": "stepped"}, {"time": 12}]}, "cloud01_1": {"translate": [{"x": -4.62, "y": 1.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.9667, "x": -9.14, "y": 3.05, "curve": "stepped"}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": -4.62, "y": 1.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.9667, "x": -9.14, "y": 3.05, "curve": "stepped"}, {"time": 6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": -4.62, "y": 1.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 9.9667, "x": -9.14, "y": 3.05, "curve": "stepped"}, {"time": 10, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 12, "x": -4.62, "y": 1.54}], "scale": [{"x": 1.038, "y": 1.038, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.9667, "x": 1.074, "y": 1.074, "curve": "stepped"}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 1.038, "y": 1.038, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.9667, "x": 1.074, "y": 1.074, "curve": "stepped"}, {"time": 6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": 1.038, "y": 1.038, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 9.9667, "x": 1.074, "y": 1.074, "curve": "stepped"}, {"time": 10, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 12, "x": 1.038, "y": 1.038}]}, "cloud02": {"translate": [{"x": 3.06, "y": 2.26, "curve": 0.349, "c2": 0.39, "c3": 0.757}, {"time": 2.7, "x": 11.5, "y": 8.47, "curve": "stepped"}, {"time": 2.7333, "curve": 0.266, "c3": 0.618, "c4": 0.42}, {"time": 4, "x": 3.06, "y": 2.26, "curve": 0.349, "c2": 0.39, "c3": 0.757}, {"time": 6.7, "x": 11.5, "y": 8.47, "curve": "stepped"}, {"time": 6.7333, "curve": 0.266, "c3": 0.618, "c4": 0.42}, {"time": 8, "x": 3.06, "y": 2.26, "curve": 0.349, "c2": 0.39, "c3": 0.757}, {"time": 10.7, "x": 11.5, "y": 8.47, "curve": "stepped"}, {"time": 10.7333, "curve": 0.266, "c3": 0.618, "c4": 0.42}, {"time": 12, "x": 3.06, "y": 2.26}], "scale": [{"x": 1.026, "y": 1.026, "curve": 0.347, "c2": 0.38, "c3": 0.754, "c4": 0.99}, {"time": 2.7, "x": 1.097, "y": 1.097, "curve": "stepped"}, {"time": 2.7333, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 4, "x": 1.026, "y": 1.026, "curve": 0.347, "c2": 0.38, "c3": 0.754, "c4": 0.99}, {"time": 6.7, "x": 1.097, "y": 1.097, "curve": "stepped"}, {"time": 6.7333, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 8, "x": 1.026, "y": 1.026, "curve": 0.347, "c2": 0.38, "c3": 0.754, "c4": 0.99}, {"time": 10.7, "x": 1.097, "y": 1.097, "curve": "stepped"}, {"time": 10.7333, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 12, "x": 1.026, "y": 1.026}]}, "cloud2": {"translate": [{"x": 10.29, "y": 7.58, "curve": 0.372, "c2": 0.62, "c3": 0.712}, {"time": 0.7, "x": 11.5, "y": 8.47, "curve": "stepped"}, {"time": 0.7333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.7333, "x": 5.81, "y": 4.28, "curve": 0.35, "c2": 0.39, "c3": 0.699, "c4": 0.77}, {"time": 4, "x": 10.29, "y": 7.58, "curve": 0.372, "c2": 0.62, "c3": 0.712}, {"time": 4.7, "x": 11.5, "y": 8.47, "curve": "stepped"}, {"time": 4.7333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.7333, "x": 5.81, "y": 4.28, "curve": 0.35, "c2": 0.39, "c3": 0.699, "c4": 0.77}, {"time": 8, "x": 10.29, "y": 7.58, "curve": 0.372, "c2": 0.62, "c3": 0.712}, {"time": 8.7, "x": 11.5, "y": 8.47, "curve": "stepped"}, {"time": 8.7333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.7333, "x": 5.81, "y": 4.28, "curve": 0.35, "c2": 0.39, "c3": 0.699, "c4": 0.77}, {"time": 12, "x": 10.29, "y": 7.58}], "scale": [{"x": 1.086, "y": 1.086, "curve": 0.371, "c2": 0.59, "c3": 0.71, "c4": 0.97}, {"time": 0.7, "x": 1.097, "y": 1.097, "curve": "stepped"}, {"time": 0.7333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.7333, "x": 1.049, "y": 1.049, "curve": 0.349, "c2": 0.38, "c3": 0.698, "c4": 0.77}, {"time": 4, "x": 1.086, "y": 1.086, "curve": 0.371, "c2": 0.59, "c3": 0.71, "c4": 0.97}, {"time": 4.7, "x": 1.097, "y": 1.097, "curve": "stepped"}, {"time": 4.7333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.7333, "x": 1.049, "y": 1.049, "curve": 0.349, "c2": 0.38, "c3": 0.698, "c4": 0.77}, {"time": 8, "x": 1.086, "y": 1.086, "curve": 0.371, "c2": 0.59, "c3": 0.71, "c4": 0.97}, {"time": 8.7, "x": 1.097, "y": 1.097, "curve": "stepped"}, {"time": 8.7333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.7333, "x": 1.049, "y": 1.049, "curve": 0.349, "c2": 0.38, "c3": 0.698, "c4": 0.77}, {"time": 12, "x": 1.086, "y": 1.086}]}, "cloud03": {"translate": [{"x": -5.44, "y": 5.44, "curve": 0.363, "c2": 0.6, "c3": 0.7, "c4": 0.96}, {"time": 0.5, "x": -5.84, "y": 5.84, "curve": "stepped"}, {"time": 0.5333, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 4, "x": -5.44, "y": 5.44, "curve": 0.363, "c2": 0.6, "c3": 0.7, "c4": 0.96}, {"time": 4.5, "x": -5.84, "y": 5.84, "curve": "stepped"}, {"time": 4.5333, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 8, "x": -5.44, "y": 5.44, "curve": 0.363, "c2": 0.6, "c3": 0.7, "c4": 0.96}, {"time": 8.5, "x": -5.84, "y": 5.84, "curve": "stepped"}, {"time": 8.5333, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 12, "x": -5.44, "y": 5.44}], "scale": [{"x": 1.045, "y": 1.045, "curve": 0.363, "c2": 0.6, "c3": 0.7, "c4": 0.96}, {"time": 0.5, "x": 1.049, "y": 1.049, "curve": "stepped"}, {"time": 0.5333, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 4, "x": 1.045, "y": 1.045, "curve": 0.363, "c2": 0.6, "c3": 0.7, "c4": 0.96}, {"time": 4.5, "x": 1.049, "y": 1.049, "curve": "stepped"}, {"time": 4.5333, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 8, "x": 1.045, "y": 1.045, "curve": 0.363, "c2": 0.6, "c3": 0.7, "c4": 0.96}, {"time": 8.5, "x": 1.049, "y": 1.049, "curve": "stepped"}, {"time": 8.5333, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 12, "x": 1.045, "y": 1.045}]}, "cloud3": {"translate": [{"x": -1.9, "y": 1.9, "curve": 0.328, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.5333, "x": -2.93, "y": 2.93, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 2.5, "x": -5.84, "y": 5.84, "curve": "stepped"}, {"time": 2.5333, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 4, "x": -1.9, "y": 1.9, "curve": 0.328, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 4.5333, "x": -2.93, "y": 2.93, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 6.5, "x": -5.84, "y": 5.84, "curve": "stepped"}, {"time": 6.5333, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 8, "x": -1.9, "y": 1.9, "curve": 0.328, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 8.5333, "x": -2.93, "y": 2.93, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 10.5, "x": -5.84, "y": 5.84, "curve": "stepped"}, {"time": 10.5333, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 12, "x": -1.9, "y": 1.9}], "scale": [{"x": 1.016, "y": 1.016, "curve": 0.328, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.5333, "x": 1.024, "y": 1.024, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 2.5, "x": 1.049, "y": 1.049, "curve": "stepped"}, {"time": 2.5333, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 4, "x": 1.016, "y": 1.016, "curve": 0.328, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 4.5333, "x": 1.024, "y": 1.024, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 6.5, "x": 1.049, "y": 1.049, "curve": "stepped"}, {"time": 6.5333, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 8, "x": 1.016, "y": 1.016, "curve": 0.328, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 8.5333, "x": 1.024, "y": 1.024, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 10.5, "x": 1.049, "y": 1.049, "curve": "stepped"}, {"time": 10.5333, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 12, "x": 1.016, "y": 1.016}]}, "cloud4": {"translate": [{"curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 3.9667, "x": 10.15, "y": 8.31, "curve": "stepped"}, {"time": 4, "curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 7.9667, "x": 10.15, "y": 8.31, "curve": "stepped"}, {"time": 8, "curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 11.9667, "x": 10.15, "y": 8.31, "curve": "stepped"}, {"time": 12}], "scale": [{"curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 3.9667, "x": 1.028, "y": 1.028, "curve": "stepped"}, {"time": 4, "curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 7.9667, "x": 1.028, "y": 1.028, "curve": "stepped"}, {"time": 8, "curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 11.9667, "x": 1.028, "y": 1.028, "curve": "stepped"}, {"time": 12}]}, "cloud5": {"translate": [{"x": 5.2, "y": 4.26, "curve": 0.374, "c2": 0.5, "c3": 0.746, "c4": 0.98}, {"time": 1.9333, "x": 10.15, "y": 8.31, "curve": "stepped"}, {"time": 1.9667, "curve": 0.249, "c3": 0.626, "c4": 0.5}, {"time": 4, "x": 5.2, "y": 4.26, "curve": 0.374, "c2": 0.5, "c3": 0.746, "c4": 0.98}, {"time": 5.9333, "x": 10.15, "y": 8.31, "curve": "stepped"}, {"time": 5.9667, "curve": 0.249, "c3": 0.626, "c4": 0.5}, {"time": 8, "x": 5.2, "y": 4.26, "curve": 0.374, "c2": 0.5, "c3": 0.746, "c4": 0.98}, {"time": 9.9333, "x": 10.15, "y": 8.31, "curve": "stepped"}, {"time": 9.9667, "curve": 0.249, "c3": 0.626, "c4": 0.5}, {"time": 12, "x": 5.2, "y": 4.26}], "scale": [{"x": 1.014, "y": 1.014, "curve": 0.374, "c2": 0.5, "c3": 0.746, "c4": 0.98}, {"time": 1.9333, "x": 1.028, "y": 1.028, "curve": "stepped"}, {"time": 1.9667, "curve": 0.249, "c3": 0.626, "c4": 0.5}, {"time": 4, "x": 1.014, "y": 1.014, "curve": 0.374, "c2": 0.5, "c3": 0.746, "c4": 0.98}, {"time": 5.9333, "x": 1.028, "y": 1.028, "curve": "stepped"}, {"time": 5.9667, "curve": 0.249, "c3": 0.626, "c4": 0.5}, {"time": 8, "x": 1.014, "y": 1.014, "curve": 0.374, "c2": 0.5, "c3": 0.746, "c4": 0.98}, {"time": 9.9333, "x": 1.028, "y": 1.028, "curve": "stepped"}, {"time": 9.9667, "curve": 0.249, "c3": 0.626, "c4": 0.5}, {"time": 12, "x": 1.014, "y": 1.014}]}, "cloud09": {"translate": [{"x": 4.77, "y": 4.24, "curve": 0.38, "c2": 0.56, "c3": 0.73, "c4": 0.98}, {"time": 1.2333, "x": 6.45, "y": 5.74, "curve": "stepped"}, {"time": 1.2667, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 4, "x": 4.77, "y": 4.24, "curve": 0.38, "c2": 0.56, "c3": 0.73, "c4": 0.98}, {"time": 5.2333, "x": 6.45, "y": 5.74, "curve": "stepped"}, {"time": 5.2667, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 8, "x": 4.77, "y": 4.24, "curve": 0.38, "c2": 0.56, "c3": 0.73, "c4": 0.98}, {"time": 9.2333, "x": 6.45, "y": 5.74, "curve": "stepped"}, {"time": 9.2667, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 12, "x": 4.77, "y": 4.24}], "scale": [{"x": 1.037, "y": 1.037, "curve": 0.38, "c2": 0.56, "c3": 0.73, "c4": 0.98}, {"time": 1.2333, "x": 1.05, "y": 1.05, "curve": "stepped"}, {"time": 1.2667, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 4, "x": 1.037, "y": 1.037, "curve": 0.38, "c2": 0.56, "c3": 0.73, "c4": 0.98}, {"time": 5.2333, "x": 1.05, "y": 1.05, "curve": "stepped"}, {"time": 5.2667, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 8, "x": 1.037, "y": 1.037, "curve": 0.38, "c2": 0.56, "c3": 0.73, "c4": 0.98}, {"time": 9.2333, "x": 1.05, "y": 1.05, "curve": "stepped"}, {"time": 9.2667, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 12, "x": 1.037, "y": 1.037}]}, "cloud9": {"translate": [{"x": 0.72, "y": 0.64, "curve": 0.302, "c2": 0.23, "c3": 0.651, "c4": 0.62}, {"time": 1.2667, "x": 3.24, "y": 2.88, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 3.2333, "x": 6.45, "y": 5.74, "curve": "stepped"}, {"time": 3.2667, "curve": 0.287, "c3": 0.627, "c4": 0.38}, {"time": 4, "x": 0.72, "y": 0.64, "curve": 0.302, "c2": 0.23, "c3": 0.651, "c4": 0.62}, {"time": 5.2667, "x": 3.24, "y": 2.88, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 7.2333, "x": 6.45, "y": 5.74, "curve": "stepped"}, {"time": 7.2667, "curve": 0.287, "c3": 0.627, "c4": 0.38}, {"time": 8, "x": 0.72, "y": 0.64, "curve": 0.302, "c2": 0.23, "c3": 0.651, "c4": 0.62}, {"time": 9.2667, "x": 3.24, "y": 2.88, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 11.2333, "x": 6.45, "y": 5.74, "curve": "stepped"}, {"time": 11.2667, "curve": 0.287, "c3": 0.627, "c4": 0.38}, {"time": 12, "x": 0.72, "y": 0.64}], "scale": [{"x": 1.006, "y": 1.006, "curve": 0.302, "c2": 0.23, "c3": 0.651, "c4": 0.62}, {"time": 1.2667, "x": 1.025, "y": 1.025, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 3.2333, "x": 1.05, "y": 1.05, "curve": "stepped"}, {"time": 3.2667, "curve": 0.287, "c3": 0.627, "c4": 0.38}, {"time": 4, "x": 1.006, "y": 1.006, "curve": 0.302, "c2": 0.23, "c3": 0.651, "c4": 0.62}, {"time": 5.2667, "x": 1.025, "y": 1.025, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 7.2333, "x": 1.05, "y": 1.05, "curve": "stepped"}, {"time": 7.2667, "curve": 0.287, "c3": 0.627, "c4": 0.38}, {"time": 8, "x": 1.006, "y": 1.006, "curve": 0.302, "c2": 0.23, "c3": 0.651, "c4": 0.62}, {"time": 9.2667, "x": 1.025, "y": 1.025, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 11.2333, "x": 1.05, "y": 1.05, "curve": "stepped"}, {"time": 11.2667, "curve": 0.287, "c3": 0.627, "c4": 0.38}, {"time": 12, "x": 1.006, "y": 1.006}]}, "cloud6": {"translate": [{"curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 3.9667, "x": -13.43, "curve": "stepped"}, {"time": 4, "curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 7.9667, "x": -13.43, "curve": "stepped"}, {"time": 8, "curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 11.9667, "x": -13.43, "curve": "stepped"}, {"time": 12}], "scale": [{"curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 3.9667, "x": 1.045, "y": 1.13, "curve": "stepped"}, {"time": 4, "curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 7.9667, "x": 1.045, "y": 1.13, "curve": "stepped"}, {"time": 8, "curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 11.9667, "x": 1.045, "y": 1.13, "curve": "stepped"}, {"time": 12}]}, "cloud7": {"translate": [{"x": -12.05, "curve": 0.37, "c2": 0.59, "c3": 0.708, "c4": 0.97}, {"time": 0.6667, "x": -13.43, "curve": "stepped"}, {"time": 0.7, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 4, "x": -12.05, "curve": 0.37, "c2": 0.59, "c3": 0.708, "c4": 0.97}, {"time": 4.6667, "x": -13.43, "curve": "stepped"}, {"time": 4.7, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 8, "x": -12.05, "curve": 0.37, "c2": 0.59, "c3": 0.708, "c4": 0.97}, {"time": 8.6667, "x": -13.43, "curve": "stepped"}, {"time": 8.7, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 12, "x": -12.05}], "scale": [{"x": 1.04, "y": 1.117, "curve": 0.37, "c2": 0.59, "c3": 0.708, "c4": 0.97}, {"time": 0.6667, "x": 1.045, "y": 1.13, "curve": "stepped"}, {"time": 0.7, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 4, "x": 1.04, "y": 1.117, "curve": 0.37, "c2": 0.59, "c3": 0.708, "c4": 0.97}, {"time": 4.6667, "x": 1.045, "y": 1.13, "curve": "stepped"}, {"time": 4.7, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 8, "x": 1.04, "y": 1.117, "curve": 0.37, "c2": 0.59, "c3": 0.708, "c4": 0.97}, {"time": 8.6667, "x": 1.045, "y": 1.13, "curve": "stepped"}, {"time": 8.7, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 12, "x": 1.04, "y": 1.117}]}, "cloud10": {"translate": [{"x": -3.66, "curve": 0.324, "c2": 0.3, "c3": 0.662, "c4": 0.65}, {"time": 0.7, "x": -6.73, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 2.6667, "x": -13.43, "curve": "stepped"}, {"time": 2.7, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": -3.66, "curve": 0.324, "c2": 0.3, "c3": 0.662, "c4": 0.65}, {"time": 4.7, "x": -6.73, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 6.6667, "x": -13.43, "curve": "stepped"}, {"time": 6.7, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 8, "x": -3.66, "curve": 0.324, "c2": 0.3, "c3": 0.662, "c4": 0.65}, {"time": 8.7, "x": -6.73, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 10.6667, "x": -13.43, "curve": "stepped"}, {"time": 10.7, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 12, "x": -3.66}], "scale": [{"x": 1.012, "y": 1.036, "curve": 0.324, "c2": 0.3, "c3": 0.662, "c4": 0.65}, {"time": 0.7, "x": 1.022, "y": 1.065, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 2.6667, "x": 1.045, "y": 1.13, "curve": "stepped"}, {"time": 2.7, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": 1.012, "y": 1.036, "curve": 0.324, "c2": 0.3, "c3": 0.662, "c4": 0.65}, {"time": 4.7, "x": 1.022, "y": 1.065, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 6.6667, "x": 1.045, "y": 1.13, "curve": "stepped"}, {"time": 6.7, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 8, "x": 1.012, "y": 1.036, "curve": 0.324, "c2": 0.3, "c3": 0.662, "c4": 0.65}, {"time": 8.7, "x": 1.022, "y": 1.065, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 10.6667, "x": 1.045, "y": 1.13, "curve": "stepped"}, {"time": 10.7, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 12, "x": 1.012, "y": 1.036}]}, "cloud11": {"translate": [{"x": -6.73, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 1.9667, "x": -13.43, "curve": "stepped"}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": -6.73, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 5.9667, "x": -13.43, "curve": "stepped"}, {"time": 6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": -6.73, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 9.9667, "x": -13.43, "curve": "stepped"}, {"time": 10, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 12, "x": -6.73}], "scale": [{"x": 1.022, "y": 1.065, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 1.9667, "x": 1.045, "y": 1.13, "curve": "stepped"}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 1.022, "y": 1.065, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 5.9667, "x": 1.045, "y": 1.13, "curve": "stepped"}, {"time": 6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": 1.022, "y": 1.065, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 9.9667, "x": 1.045, "y": 1.13, "curve": "stepped"}, {"time": 10, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 12, "x": 1.022, "y": 1.065}]}, "cloud8": {"translate": [{"curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 3.9667, "x": 3.73, "curve": "stepped"}, {"time": 4, "curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 7.9667, "x": 3.73, "curve": "stepped"}, {"time": 8, "curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 11.9667, "x": 3.73, "curve": "stepped"}, {"time": 12}], "scale": [{"curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 3.9667, "x": 1.061, "y": 1.061, "curve": "stepped"}, {"time": 4, "curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 7.9667, "x": 1.061, "y": 1.061, "curve": "stepped"}, {"time": 8, "curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 11.9667, "x": 1.061, "y": 1.061, "curve": "stepped"}, {"time": 12}]}, "cloud12": {"translate": [{"x": 1.87, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 1.9667, "x": 3.73, "curve": "stepped"}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 1.87, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 5.9667, "x": 3.73, "curve": "stepped"}, {"time": 6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": 1.87, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 9.9667, "x": 3.73, "curve": "stepped"}, {"time": 10, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 12, "x": 1.87}], "scale": [{"x": 1.03, "y": 1.03, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 1.9667, "x": 1.061, "y": 1.061, "curve": "stepped"}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 1.03, "y": 1.03, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 5.9667, "x": 1.061, "y": 1.061, "curve": "stepped"}, {"time": 6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": 1.03, "y": 1.03, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 9.9667, "x": 1.061, "y": 1.061, "curve": "stepped"}, {"time": 10, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 12, "x": 1.03, "y": 1.03}]}, "bone4": {"rotate": [{"angle": 43.84, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -33.94, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 43.84, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -33.94, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 43.84, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -33.94, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 43.84, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": -33.94, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 43.84, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -33.94, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 43.84, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "angle": -33.94, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 43.84, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": -33.94, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 43.84, "curve": 0.25, "c3": 0.75}, {"time": 3.7667, "angle": -33.94, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 43.84}]}, "bone5": {"rotate": [{"angle": -1.37, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1, "angle": 30.18, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -47.59, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 30.18, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -47.59, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 30.18, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": -47.59, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 30.18, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -47.59, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2, "angle": -1.37, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 2.1, "angle": 30.18, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "angle": -47.59, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 30.18, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "angle": -47.59, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "angle": 30.18, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": -47.59, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": 30.18, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "angle": -47.59, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 4, "angle": -1.37}]}, "bone3": {"rotate": [{"angle": 16.41, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1, "angle": -17.78, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 66.51, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -17.78, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 66.51, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -17.78, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": 66.51, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": -17.78, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 66.51, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2, "angle": 16.41, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 2.1, "angle": -17.78, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "angle": 66.51, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": -17.78, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "angle": 66.51, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "angle": -17.78, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": 66.51, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": -17.78, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "angle": 66.51, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 4, "angle": 16.41}]}, "bone2": {"rotate": [{"angle": -26.93, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 57.37, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -26.93, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 57.37, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -26.93, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 57.37, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -26.93, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": 57.37, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -26.93, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 57.37, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -26.93, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "angle": 57.37, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -26.93, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": 57.37, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -26.93, "curve": 0.25, "c3": 0.75}, {"time": 3.7667, "angle": 57.37, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -26.93}]}, "bone": {"translate": [{"x": -1.43, "curve": 0.188, "c2": 0.65, "c3": 0.75}, {"time": 3.0333, "x": 459.45, "y": -266.72}], "scale": [{"x": 1.3, "y": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 2.0667}]}, "all58": {"rotate": [{"angle": 10.3, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.3333, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 16.62, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 10.3, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 5.3333, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": 16.62, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "angle": 10.3, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 9.3333, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 11.3333, "angle": 16.62, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 12, "angle": 10.3}]}, "all41": {"rotate": [{"angle": 16.17, "curve": 0.329, "c2": 0.32, "c3": 0.676, "c4": 0.69}, {"time": 0.6, "angle": 3.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.2667, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": 26.76, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 4, "angle": 16.17, "curve": 0.329, "c2": 0.32, "c3": 0.676, "c4": 0.69}, {"time": 4.6, "angle": 3.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 5.2667, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "angle": 26.76, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 8, "angle": 16.17, "curve": 0.329, "c2": 0.32, "c3": 0.676, "c4": 0.69}, {"time": 8.6, "angle": 3.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 9.2667, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 11.2667, "angle": 26.76, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 12, "angle": 16.17}]}, "all57": {"rotate": [{"angle": 4.58, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6667, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 30.41, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 4.58, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.6667, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 30.41, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "angle": 4.58, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 8.6667, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 30.41, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 12, "angle": 4.58}]}, "all56": {"rotate": [{"angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 16.62, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 16.62, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": 16.62, "curve": 0.25, "c3": 0.75}, {"time": 12, "angle": -5.65}]}, "all37": {"translate": [{"x": 4.37, "y": 2.17, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 6.1, "y": 3.03, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": 4.37, "y": 2.17, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": 6.1, "y": 3.03, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "x": 4.37, "y": 2.17, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 9.3333, "curve": 0.25, "c3": 0.75}, {"time": 11.3333, "x": 6.1, "y": 3.03, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 12, "x": 4.37, "y": 2.17}]}, "all43": {"rotate": [{"angle": 11.23, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.6, "angle": 16.62, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": -5.65, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4, "angle": 11.23, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 4.6, "angle": 16.62, "curve": 0.25, "c3": 0.75}, {"time": 6.6, "angle": -5.65, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 8, "angle": 11.23, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.6, "angle": 16.62, "curve": 0.25, "c3": 0.75}, {"time": 10.6, "angle": -5.65, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 12, "angle": 11.23}]}, "all38": {"translate": [{"x": -1.34, "y": 1.7, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": -4.74, "y": 6, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": -1.34, "y": 1.7, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": -4.74, "y": 6, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "x": -1.34, "y": 1.7, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 8.6667, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "x": -4.74, "y": 6, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 12, "x": -1.34, "y": 1.7}]}, "all42": {"rotate": [{"angle": 16.49, "curve": 0.276, "c2": 0.08, "c3": 0.625, "c4": 0.48}, {"time": 0.6, "angle": 10.3, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.9333, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "angle": 16.62, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 4, "angle": 16.49, "curve": 0.276, "c2": 0.08, "c3": 0.625, "c4": 0.48}, {"time": 4.6, "angle": 10.3, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 5.9333, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 7.9333, "angle": 16.62, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 8, "angle": 16.49, "curve": 0.276, "c2": 0.08, "c3": 0.625, "c4": 0.48}, {"time": 8.6, "angle": 10.3, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 9.9333, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 11.9333, "angle": 16.62, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 12, "angle": 16.49}]}, "all35": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -28.69, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": -28.69, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": -28.69, "curve": 0.25, "c3": 0.75}, {"time": 12}]}, "all36": {"translate": [{"x": 4.61, "y": -3.05, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 4.61, "y": -3.05, "curve": 0.25, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 8, "x": 4.61, "y": -3.05, "curve": 0.25, "c3": 0.75}, {"time": 10, "curve": 0.25, "c3": 0.75}, {"time": 12, "x": 4.61, "y": -3.05}]}, "all40": {"rotate": [{"angle": -0.26, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.6, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 16.62, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4, "angle": -0.26, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 4.6, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 6.6, "angle": 16.62, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 8, "angle": -0.26, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.6, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 10.6, "angle": 16.62, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 12, "angle": -0.26}]}, "all39": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 39.14, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 19.51, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": 19.51, "curve": 0.25, "c3": 0.75}, {"time": 12}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2, "x": -5.84, "y": 26.5, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": -5.84, "y": 26.5, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 10, "x": -5.84, "y": 26.5, "curve": 0.25, "c3": 0.75}, {"time": 12}], "scale": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 0.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6, "x": 0.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10, "x": 0.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 12}]}, "all59": {"rotate": [{"angle": 16.62, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 16.62, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 16.62, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 12, "angle": 16.62}]}, "all3": {"translate": [{"y": 0.29, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "y": 3.05, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "y": 0.29, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "y": 3.05, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "y": 0.29, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "y": 3.05, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 6, "y": 0.29, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 6.1667, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "y": 3.05, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 8, "y": 0.29, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 9.1667, "y": 3.05, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 10, "y": 0.29, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 10.1667, "curve": 0.25, "c3": 0.75}, {"time": 11.1667, "y": 3.05, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 12, "y": 0.29}]}, "all11": {"rotate": [{"angle": -2.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -2.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -2.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "angle": -2.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.3333, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "angle": -2.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 8.3333, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 10, "angle": -2.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 10.3333, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 11.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 12, "angle": -2.54}]}, "all10": {"rotate": [{"angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 9, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 11, "curve": 0.25, "c3": 0.75}, {"time": 12, "angle": -3.55}]}, "all5": {"translate": [{"y": -4.79, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "y": -5.3, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "y": -4.79, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "y": -5.3, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "y": -4.79, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 4.8333, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "y": -5.3, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 6, "y": -4.79, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 6.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "y": -5.3, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 8, "y": -4.79, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 8.8333, "curve": 0.25, "c3": 0.75}, {"time": 9.8333, "y": -5.3, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 10, "y": -4.79, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 10.8333, "curve": 0.25, "c3": 0.75}, {"time": 11.8333, "y": -5.3, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 12, "y": -4.79}]}, "all8": {"rotate": [{"angle": 3.99, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 3.99, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 3.99, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "angle": 3.99, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.3333, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "angle": 3.99, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 8.3333, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 10, "angle": 3.99, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 10.3333, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 11.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 12, "angle": 3.99}]}, "all7": {"rotate": [{"angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 9, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 11, "curve": 0.25, "c3": 0.75}, {"time": 12, "angle": 5.57}]}, "all9": {"rotate": [{"angle": 1.58, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 1.58, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 1.58, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.6667, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "angle": 1.58, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 6.6667, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "angle": 1.58, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 8.6667, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 10, "angle": 1.58, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 10.6667, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 12, "angle": 1.58}]}, "all4": {"translate": [{"x": 3.5, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 4.89, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 3.5, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 4.89, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": 3.5, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": 4.89, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "x": 3.5, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "x": 4.89, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "x": 3.5, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 8.6667, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "x": 4.89, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 10, "x": 3.5, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 10.6667, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "x": 4.89, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 12, "x": 3.5}]}, "all6": {"translate": [{"y": 3.08, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "y": 3.41, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "y": 3.08, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "y": 3.41, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "y": 3.08, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 4.8333, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "y": 3.41, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 6, "y": 3.08, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 6.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "y": 3.41, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 8, "y": 3.08, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 8.8333, "curve": 0.25, "c3": 0.75}, {"time": 9.8333, "y": 3.41, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 10, "y": 3.08, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 10.8333, "curve": 0.25, "c3": 0.75}, {"time": 11.8333, "y": 3.41, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 12, "y": 3.08}]}, "all2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "y": 3.04, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "y": 3.04, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5, "y": 3.04, "curve": 0.25, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7, "y": 3.04, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9, "y": 3.04, "curve": 0.25, "c3": 0.75}, {"time": 10, "curve": 0.25, "c3": 0.75}, {"time": 11, "y": 3.04, "curve": 0.25, "c3": 0.75}, {"time": 12}]}, "all12": {"rotate": [{"angle": -1.01, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -1.01, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -1.01, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.6667, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "angle": -1.01, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 6.6667, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "angle": -1.01, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 8.6667, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 10, "angle": -1.01, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 10.6667, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 12, "angle": -1.01}]}}, "path": {"loong": {"spacing": [{"spacing": 0.0332, "curve": 0.364, "c2": 0.45, "c3": 0.755}, {"time": 1.9667, "spacing": 0.032, "curve": 0.25, "c3": 0.75}, {"time": 4.6333, "spacing": 0.034, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "spacing": 0.032, "curve": 0.25, "c3": 0.75}, {"time": 10.6333, "spacing": 0.034, "curve": 0.257, "c3": 0.619, "c4": 0.46}, {"time": 12, "spacing": 0.0332}]}}}, "mz_idle": {"slots": {"mingzu_bg05": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 8, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 10, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 12, "color": "ffffff00"}]}, "mingzu_bg02": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 8, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 10, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 12, "color": "ffffff00"}]}, "mingzu_bg010": {"color": [{"color": "ffffffac", "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.7333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "color": "ffffff00", "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 4, "color": "ffffffac", "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 4.7333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 6.7333, "color": "ffffff00", "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 8, "color": "ffffffac", "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 8.7333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 10.7333, "color": "ffffff00", "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 12, "color": "ffffffac"}]}, "mingzu_bg10": {"color": [{"color": "ffffff52", "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.7333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "color": "ffffffff", "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 4, "color": "ffffff52", "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 4.7333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6.7333, "color": "ffffffff", "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 8, "color": "ffffff52", "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 8.7333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 10.7333, "color": "ffffffff", "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 12, "color": "ffffff52"}]}, "mingzu_bg2": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 6, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 8, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 10, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 12, "color": "ffffffff"}]}, "mingzu_bg08": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 8, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 10, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 12, "color": "ffffff00"}]}, "mingzu_bg011": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 8, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 10, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 12, "color": "ffffff00"}]}, "hui2": {"color": [{"color": "ffffffd8", "curve": 0.382, "c2": 0.55, "c3": 0.741}, {"time": 0.7667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "color": "ffffff8f", "curve": 0.245, "c3": 0.64, "c4": 0.57}, {"time": 4, "color": "ffffffd8", "curve": 0.382, "c2": 0.55, "c3": 0.741}, {"time": 4.7667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "color": "ffffff8f", "curve": 0.245, "c3": 0.64, "c4": 0.57}, {"time": 8, "color": "ffffffd8", "curve": 0.382, "c2": 0.55, "c3": 0.741}, {"time": 8.7667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 10.7667, "color": "ffffff8f", "curve": 0.245, "c3": 0.64, "c4": 0.57}, {"time": 12, "color": "ffffffd8"}]}, "mingzu_bg6": {"color": [{"color": "ffffff52", "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 1.2667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "color": "ffffff00", "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 4, "color": "ffffff52", "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 5.2667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "color": "ffffff00", "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 8, "color": "ffffff52", "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 9.2667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 11.2667, "color": "ffffff00", "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 12, "color": "ffffff52"}]}, "mingzu_bg04": {"color": [{"color": "ffffff4d", "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.7, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.7, "color": "ffffffff", "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 4, "color": "ffffff4d", "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 4.7, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6.7, "color": "ffffffff", "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 8, "color": "ffffff4d", "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 8.7, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 10.7, "color": "ffffffff", "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 12, "color": "ffffff4d"}]}, "mingzu_bg11": {"color": [{"color": "fffffffd", "curve": 0.257, "c2": 0.03, "c3": 0.751}, {"time": 1.9667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "color": "ffffffff", "curve": 0.328, "c3": 0.661, "c4": 0.34}, {"time": 4, "color": "fffffffd", "curve": 0.257, "c2": 0.03, "c3": 0.751}, {"time": 5.9667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "color": "ffffffff", "curve": 0.328, "c3": 0.661, "c4": 0.34}, {"time": 8, "color": "fffffffd", "curve": 0.257, "c2": 0.03, "c3": 0.751}, {"time": 9.9667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 11.9667, "color": "ffffffff", "curve": 0.328, "c3": 0.661, "c4": 0.34}, {"time": 12, "color": "fffffffd"}]}, "eye": {"attachment": [{"time": 1, "name": "eye"}, {"time": 1.1667, "name": null}, {"time": 3, "name": "eye"}, {"time": 3.1667, "name": null}, {"time": 5, "name": "eye"}, {"time": 5.1667, "name": null}, {"time": 7, "name": "eye"}, {"time": 7.1667, "name": null}, {"time": 9, "name": "eye"}, {"time": 9.1667, "name": null}, {"time": 11, "name": "eye"}, {"time": 11.1667, "name": null}]}, "mingzu_bg012": {"color": [{"color": "ffffff9a", "curve": 0.25, "c3": 0.75}, {"time": 2.2, "color": "ffffff00"}]}, "hui1": {"color": [{"color": "ffffffcc", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffff8f", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffffcc", "curve": 0.25, "c3": 0.75}, {"time": 6, "color": "ffffff8f", "curve": 0.25, "c3": 0.75}, {"time": 8, "color": "ffffffcc", "curve": 0.25, "c3": 0.75}, {"time": 10, "color": "ffffff8f", "curve": 0.25, "c3": 0.75}, {"time": 12, "color": "ffffffcc"}]}, "mingzu_bg8": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 6, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 8, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 10, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 12, "color": "ffffffff"}]}, "mingzu_bg4": {"color": [{"color": "ffffffb1", "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.7, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.7, "color": "ffffff00", "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 4, "color": "ffffffb1", "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 4.7, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 6.7, "color": "ffffff00", "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 8, "color": "ffffffb1", "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 8.7, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 10.7, "color": "ffffff00", "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 12, "color": "ffffffb1"}]}, "mingzu_bg9": {"color": [{"color": "ffffffcb", "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.5333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "color": "ffffff00", "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 4, "color": "ffffffcb", "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 4.5333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 6.5333, "color": "ffffff00", "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 8, "color": "ffffffcb", "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 8.5333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 10.5333, "color": "ffffff00", "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 12, "color": "ffffffcb"}]}, "mingzu_bg09": {"color": [{"color": "ffffff33", "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.5333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "color": "ffffffff", "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 4, "color": "ffffff33", "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 4.5333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6.5333, "color": "ffffffff", "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 8, "color": "ffffff33", "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 8.5333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 10.5333, "color": "ffffffff", "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 12, "color": "ffffff33"}]}, "mingzu_bg5": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 6, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 8, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 10, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 12, "color": "ffffffff"}]}, "mingzu_bg06": {"color": [{"color": "ffffffac", "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 1.2667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "color": "ffffffff", "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 4, "color": "ffffffac", "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 5.2667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "color": "ffffffff", "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 8, "color": "ffffffac", "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 9.2667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 11.2667, "color": "ffffffff", "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 12, "color": "ffffffac"}]}}, "bones": {"cloud01": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 3.9667, "x": -9.14, "y": 3.05, "curve": "stepped"}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "x": -9.14, "y": 3.05, "curve": "stepped"}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 11.9667, "x": -9.14, "y": 3.05, "curve": "stepped"}, {"time": 12}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 3.9667, "x": 1.074, "y": 1.074, "curve": "stepped"}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "x": 1.074, "y": 1.074, "curve": "stepped"}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 11.9667, "x": 1.074, "y": 1.074, "curve": "stepped"}, {"time": 12}]}, "cloud01_1": {"translate": [{"x": -4.62, "y": 1.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.9667, "x": -9.14, "y": 3.05, "curve": "stepped"}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": -4.62, "y": 1.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.9667, "x": -9.14, "y": 3.05, "curve": "stepped"}, {"time": 6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": -4.62, "y": 1.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 9.9667, "x": -9.14, "y": 3.05, "curve": "stepped"}, {"time": 10, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 12, "x": -4.62, "y": 1.54}], "scale": [{"x": 1.038, "y": 1.038, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.9667, "x": 1.074, "y": 1.074, "curve": "stepped"}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 1.038, "y": 1.038, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.9667, "x": 1.074, "y": 1.074, "curve": "stepped"}, {"time": 6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": 1.038, "y": 1.038, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 9.9667, "x": 1.074, "y": 1.074, "curve": "stepped"}, {"time": 10, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 12, "x": 1.038, "y": 1.038}]}, "cloud02": {"translate": [{"x": 3.06, "y": 2.26, "curve": 0.349, "c2": 0.39, "c3": 0.757}, {"time": 2.7, "x": 11.5, "y": 8.47, "curve": "stepped"}, {"time": 2.7333, "curve": 0.266, "c3": 0.618, "c4": 0.42}, {"time": 4, "x": 3.06, "y": 2.26, "curve": 0.349, "c2": 0.39, "c3": 0.757}, {"time": 6.7, "x": 11.5, "y": 8.47, "curve": "stepped"}, {"time": 6.7333, "curve": 0.266, "c3": 0.618, "c4": 0.42}, {"time": 8, "x": 3.06, "y": 2.26, "curve": 0.349, "c2": 0.39, "c3": 0.757}, {"time": 10.7, "x": 11.5, "y": 8.47, "curve": "stepped"}, {"time": 10.7333, "curve": 0.266, "c3": 0.618, "c4": 0.42}, {"time": 12, "x": 3.06, "y": 2.26}], "scale": [{"x": 1.026, "y": 1.026, "curve": 0.347, "c2": 0.38, "c3": 0.754, "c4": 0.99}, {"time": 2.7, "x": 1.097, "y": 1.097, "curve": "stepped"}, {"time": 2.7333, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 4, "x": 1.026, "y": 1.026, "curve": 0.347, "c2": 0.38, "c3": 0.754, "c4": 0.99}, {"time": 6.7, "x": 1.097, "y": 1.097, "curve": "stepped"}, {"time": 6.7333, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 8, "x": 1.026, "y": 1.026, "curve": 0.347, "c2": 0.38, "c3": 0.754, "c4": 0.99}, {"time": 10.7, "x": 1.097, "y": 1.097, "curve": "stepped"}, {"time": 10.7333, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 12, "x": 1.026, "y": 1.026}]}, "cloud2": {"translate": [{"x": 10.29, "y": 7.58, "curve": 0.372, "c2": 0.62, "c3": 0.712}, {"time": 0.7, "x": 11.5, "y": 8.47, "curve": "stepped"}, {"time": 0.7333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.7333, "x": 5.81, "y": 4.28, "curve": 0.35, "c2": 0.39, "c3": 0.699, "c4": 0.77}, {"time": 4, "x": 10.29, "y": 7.58, "curve": 0.372, "c2": 0.62, "c3": 0.712}, {"time": 4.7, "x": 11.5, "y": 8.47, "curve": "stepped"}, {"time": 4.7333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.7333, "x": 5.81, "y": 4.28, "curve": 0.35, "c2": 0.39, "c3": 0.699, "c4": 0.77}, {"time": 8, "x": 10.29, "y": 7.58, "curve": 0.372, "c2": 0.62, "c3": 0.712}, {"time": 8.7, "x": 11.5, "y": 8.47, "curve": "stepped"}, {"time": 8.7333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.7333, "x": 5.81, "y": 4.28, "curve": 0.35, "c2": 0.39, "c3": 0.699, "c4": 0.77}, {"time": 12, "x": 10.29, "y": 7.58}], "scale": [{"x": 1.086, "y": 1.086, "curve": 0.371, "c2": 0.59, "c3": 0.71, "c4": 0.97}, {"time": 0.7, "x": 1.097, "y": 1.097, "curve": "stepped"}, {"time": 0.7333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.7333, "x": 1.049, "y": 1.049, "curve": 0.349, "c2": 0.38, "c3": 0.698, "c4": 0.77}, {"time": 4, "x": 1.086, "y": 1.086, "curve": 0.371, "c2": 0.59, "c3": 0.71, "c4": 0.97}, {"time": 4.7, "x": 1.097, "y": 1.097, "curve": "stepped"}, {"time": 4.7333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.7333, "x": 1.049, "y": 1.049, "curve": 0.349, "c2": 0.38, "c3": 0.698, "c4": 0.77}, {"time": 8, "x": 1.086, "y": 1.086, "curve": 0.371, "c2": 0.59, "c3": 0.71, "c4": 0.97}, {"time": 8.7, "x": 1.097, "y": 1.097, "curve": "stepped"}, {"time": 8.7333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.7333, "x": 1.049, "y": 1.049, "curve": 0.349, "c2": 0.38, "c3": 0.698, "c4": 0.77}, {"time": 12, "x": 1.086, "y": 1.086}]}, "cloud03": {"translate": [{"x": -5.44, "y": 5.44, "curve": 0.363, "c2": 0.6, "c3": 0.7, "c4": 0.96}, {"time": 0.5, "x": -5.84, "y": 5.84, "curve": "stepped"}, {"time": 0.5333, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 4, "x": -5.44, "y": 5.44, "curve": 0.363, "c2": 0.6, "c3": 0.7, "c4": 0.96}, {"time": 4.5, "x": -5.84, "y": 5.84, "curve": "stepped"}, {"time": 4.5333, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 8, "x": -5.44, "y": 5.44, "curve": 0.363, "c2": 0.6, "c3": 0.7, "c4": 0.96}, {"time": 8.5, "x": -5.84, "y": 5.84, "curve": "stepped"}, {"time": 8.5333, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 12, "x": -5.44, "y": 5.44}], "scale": [{"x": 1.045, "y": 1.045, "curve": 0.363, "c2": 0.6, "c3": 0.7, "c4": 0.96}, {"time": 0.5, "x": 1.049, "y": 1.049, "curve": "stepped"}, {"time": 0.5333, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 4, "x": 1.045, "y": 1.045, "curve": 0.363, "c2": 0.6, "c3": 0.7, "c4": 0.96}, {"time": 4.5, "x": 1.049, "y": 1.049, "curve": "stepped"}, {"time": 4.5333, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 8, "x": 1.045, "y": 1.045, "curve": 0.363, "c2": 0.6, "c3": 0.7, "c4": 0.96}, {"time": 8.5, "x": 1.049, "y": 1.049, "curve": "stepped"}, {"time": 8.5333, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 12, "x": 1.045, "y": 1.045}]}, "cloud3": {"translate": [{"x": -1.9, "y": 1.9, "curve": 0.328, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.5333, "x": -2.93, "y": 2.93, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 2.5, "x": -5.84, "y": 5.84, "curve": "stepped"}, {"time": 2.5333, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 4, "x": -1.9, "y": 1.9, "curve": 0.328, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 4.5333, "x": -2.93, "y": 2.93, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 6.5, "x": -5.84, "y": 5.84, "curve": "stepped"}, {"time": 6.5333, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 8, "x": -1.9, "y": 1.9, "curve": 0.328, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 8.5333, "x": -2.93, "y": 2.93, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 10.5, "x": -5.84, "y": 5.84, "curve": "stepped"}, {"time": 10.5333, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 12, "x": -1.9, "y": 1.9}], "scale": [{"x": 1.016, "y": 1.016, "curve": 0.328, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.5333, "x": 1.024, "y": 1.024, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 2.5, "x": 1.049, "y": 1.049, "curve": "stepped"}, {"time": 2.5333, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 4, "x": 1.016, "y": 1.016, "curve": 0.328, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 4.5333, "x": 1.024, "y": 1.024, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 6.5, "x": 1.049, "y": 1.049, "curve": "stepped"}, {"time": 6.5333, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 8, "x": 1.016, "y": 1.016, "curve": 0.328, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 8.5333, "x": 1.024, "y": 1.024, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 10.5, "x": 1.049, "y": 1.049, "curve": "stepped"}, {"time": 10.5333, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 12, "x": 1.016, "y": 1.016}]}, "cloud4": {"translate": [{"curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 3.9667, "x": 10.15, "y": 8.31, "curve": "stepped"}, {"time": 4, "curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 7.9667, "x": 10.15, "y": 8.31, "curve": "stepped"}, {"time": 8, "curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 11.9667, "x": 10.15, "y": 8.31, "curve": "stepped"}, {"time": 12}], "scale": [{"curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 3.9667, "x": 1.028, "y": 1.028, "curve": "stepped"}, {"time": 4, "curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 7.9667, "x": 1.028, "y": 1.028, "curve": "stepped"}, {"time": 8, "curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 11.9667, "x": 1.028, "y": 1.028, "curve": "stepped"}, {"time": 12}]}, "cloud5": {"translate": [{"x": 5.2, "y": 4.26, "curve": 0.374, "c2": 0.5, "c3": 0.746, "c4": 0.98}, {"time": 1.9333, "x": 10.15, "y": 8.31, "curve": "stepped"}, {"time": 1.9667, "curve": 0.249, "c3": 0.626, "c4": 0.5}, {"time": 4, "x": 5.2, "y": 4.26, "curve": 0.374, "c2": 0.5, "c3": 0.746, "c4": 0.98}, {"time": 5.9333, "x": 10.15, "y": 8.31, "curve": "stepped"}, {"time": 5.9667, "curve": 0.249, "c3": 0.626, "c4": 0.5}, {"time": 8, "x": 5.2, "y": 4.26, "curve": 0.374, "c2": 0.5, "c3": 0.746, "c4": 0.98}, {"time": 9.9333, "x": 10.15, "y": 8.31, "curve": "stepped"}, {"time": 9.9667, "curve": 0.249, "c3": 0.626, "c4": 0.5}, {"time": 12, "x": 5.2, "y": 4.26}], "scale": [{"x": 1.014, "y": 1.014, "curve": 0.374, "c2": 0.5, "c3": 0.746, "c4": 0.98}, {"time": 1.9333, "x": 1.028, "y": 1.028, "curve": "stepped"}, {"time": 1.9667, "curve": 0.249, "c3": 0.626, "c4": 0.5}, {"time": 4, "x": 1.014, "y": 1.014, "curve": 0.374, "c2": 0.5, "c3": 0.746, "c4": 0.98}, {"time": 5.9333, "x": 1.028, "y": 1.028, "curve": "stepped"}, {"time": 5.9667, "curve": 0.249, "c3": 0.626, "c4": 0.5}, {"time": 8, "x": 1.014, "y": 1.014, "curve": 0.374, "c2": 0.5, "c3": 0.746, "c4": 0.98}, {"time": 9.9333, "x": 1.028, "y": 1.028, "curve": "stepped"}, {"time": 9.9667, "curve": 0.249, "c3": 0.626, "c4": 0.5}, {"time": 12, "x": 1.014, "y": 1.014}]}, "cloud09": {"translate": [{"x": 4.77, "y": 4.24, "curve": 0.38, "c2": 0.56, "c3": 0.73, "c4": 0.98}, {"time": 1.2333, "x": 6.45, "y": 5.74, "curve": "stepped"}, {"time": 1.2667, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 4, "x": 4.77, "y": 4.24, "curve": 0.38, "c2": 0.56, "c3": 0.73, "c4": 0.98}, {"time": 5.2333, "x": 6.45, "y": 5.74, "curve": "stepped"}, {"time": 5.2667, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 8, "x": 4.77, "y": 4.24, "curve": 0.38, "c2": 0.56, "c3": 0.73, "c4": 0.98}, {"time": 9.2333, "x": 6.45, "y": 5.74, "curve": "stepped"}, {"time": 9.2667, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 12, "x": 4.77, "y": 4.24}], "scale": [{"x": 1.037, "y": 1.037, "curve": 0.38, "c2": 0.56, "c3": 0.73, "c4": 0.98}, {"time": 1.2333, "x": 1.05, "y": 1.05, "curve": "stepped"}, {"time": 1.2667, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 4, "x": 1.037, "y": 1.037, "curve": 0.38, "c2": 0.56, "c3": 0.73, "c4": 0.98}, {"time": 5.2333, "x": 1.05, "y": 1.05, "curve": "stepped"}, {"time": 5.2667, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 8, "x": 1.037, "y": 1.037, "curve": 0.38, "c2": 0.56, "c3": 0.73, "c4": 0.98}, {"time": 9.2333, "x": 1.05, "y": 1.05, "curve": "stepped"}, {"time": 9.2667, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 12, "x": 1.037, "y": 1.037}]}, "cloud9": {"translate": [{"x": 0.72, "y": 0.64, "curve": 0.302, "c2": 0.23, "c3": 0.651, "c4": 0.62}, {"time": 1.2667, "x": 3.24, "y": 2.88, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 3.2333, "x": 6.45, "y": 5.74, "curve": "stepped"}, {"time": 3.2667, "curve": 0.287, "c3": 0.627, "c4": 0.38}, {"time": 4, "x": 0.72, "y": 0.64, "curve": 0.302, "c2": 0.23, "c3": 0.651, "c4": 0.62}, {"time": 5.2667, "x": 3.24, "y": 2.88, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 7.2333, "x": 6.45, "y": 5.74, "curve": "stepped"}, {"time": 7.2667, "curve": 0.287, "c3": 0.627, "c4": 0.38}, {"time": 8, "x": 0.72, "y": 0.64, "curve": 0.302, "c2": 0.23, "c3": 0.651, "c4": 0.62}, {"time": 9.2667, "x": 3.24, "y": 2.88, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 11.2333, "x": 6.45, "y": 5.74, "curve": "stepped"}, {"time": 11.2667, "curve": 0.287, "c3": 0.627, "c4": 0.38}, {"time": 12, "x": 0.72, "y": 0.64}], "scale": [{"x": 1.006, "y": 1.006, "curve": 0.302, "c2": 0.23, "c3": 0.651, "c4": 0.62}, {"time": 1.2667, "x": 1.025, "y": 1.025, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 3.2333, "x": 1.05, "y": 1.05, "curve": "stepped"}, {"time": 3.2667, "curve": 0.287, "c3": 0.627, "c4": 0.38}, {"time": 4, "x": 1.006, "y": 1.006, "curve": 0.302, "c2": 0.23, "c3": 0.651, "c4": 0.62}, {"time": 5.2667, "x": 1.025, "y": 1.025, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 7.2333, "x": 1.05, "y": 1.05, "curve": "stepped"}, {"time": 7.2667, "curve": 0.287, "c3": 0.627, "c4": 0.38}, {"time": 8, "x": 1.006, "y": 1.006, "curve": 0.302, "c2": 0.23, "c3": 0.651, "c4": 0.62}, {"time": 9.2667, "x": 1.025, "y": 1.025, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 11.2333, "x": 1.05, "y": 1.05, "curve": "stepped"}, {"time": 11.2667, "curve": 0.287, "c3": 0.627, "c4": 0.38}, {"time": 12, "x": 1.006, "y": 1.006}]}, "cloud6": {"translate": [{"curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 3.9667, "x": -13.43, "curve": "stepped"}, {"time": 4, "curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 7.9667, "x": -13.43, "curve": "stepped"}, {"time": 8, "curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 11.9667, "x": -13.43, "curve": "stepped"}, {"time": 12}], "scale": [{"curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 3.9667, "x": 1.045, "y": 1.13, "curve": "stepped"}, {"time": 4, "curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 7.9667, "x": 1.045, "y": 1.13, "curve": "stepped"}, {"time": 8, "curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 11.9667, "x": 1.045, "y": 1.13, "curve": "stepped"}, {"time": 12}]}, "cloud7": {"translate": [{"x": -12.05, "curve": 0.37, "c2": 0.59, "c3": 0.708, "c4": 0.97}, {"time": 0.6667, "x": -13.43, "curve": "stepped"}, {"time": 0.7, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 4, "x": -12.05, "curve": 0.37, "c2": 0.59, "c3": 0.708, "c4": 0.97}, {"time": 4.6667, "x": -13.43, "curve": "stepped"}, {"time": 4.7, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 8, "x": -12.05, "curve": 0.37, "c2": 0.59, "c3": 0.708, "c4": 0.97}, {"time": 8.6667, "x": -13.43, "curve": "stepped"}, {"time": 8.7, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 12, "x": -12.05}], "scale": [{"x": 1.04, "y": 1.117, "curve": 0.37, "c2": 0.59, "c3": 0.708, "c4": 0.97}, {"time": 0.6667, "x": 1.045, "y": 1.13, "curve": "stepped"}, {"time": 0.7, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 4, "x": 1.04, "y": 1.117, "curve": 0.37, "c2": 0.59, "c3": 0.708, "c4": 0.97}, {"time": 4.6667, "x": 1.045, "y": 1.13, "curve": "stepped"}, {"time": 4.7, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 8, "x": 1.04, "y": 1.117, "curve": 0.37, "c2": 0.59, "c3": 0.708, "c4": 0.97}, {"time": 8.6667, "x": 1.045, "y": 1.13, "curve": "stepped"}, {"time": 8.7, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 12, "x": 1.04, "y": 1.117}]}, "cloud10": {"translate": [{"x": -3.66, "curve": 0.324, "c2": 0.3, "c3": 0.662, "c4": 0.65}, {"time": 0.7, "x": -6.73, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 2.6667, "x": -13.43, "curve": "stepped"}, {"time": 2.7, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": -3.66, "curve": 0.324, "c2": 0.3, "c3": 0.662, "c4": 0.65}, {"time": 4.7, "x": -6.73, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 6.6667, "x": -13.43, "curve": "stepped"}, {"time": 6.7, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 8, "x": -3.66, "curve": 0.324, "c2": 0.3, "c3": 0.662, "c4": 0.65}, {"time": 8.7, "x": -6.73, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 10.6667, "x": -13.43, "curve": "stepped"}, {"time": 10.7, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 12, "x": -3.66}], "scale": [{"x": 1.012, "y": 1.036, "curve": 0.324, "c2": 0.3, "c3": 0.662, "c4": 0.65}, {"time": 0.7, "x": 1.022, "y": 1.065, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 2.6667, "x": 1.045, "y": 1.13, "curve": "stepped"}, {"time": 2.7, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": 1.012, "y": 1.036, "curve": 0.324, "c2": 0.3, "c3": 0.662, "c4": 0.65}, {"time": 4.7, "x": 1.022, "y": 1.065, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 6.6667, "x": 1.045, "y": 1.13, "curve": "stepped"}, {"time": 6.7, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 8, "x": 1.012, "y": 1.036, "curve": 0.324, "c2": 0.3, "c3": 0.662, "c4": 0.65}, {"time": 8.7, "x": 1.022, "y": 1.065, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 10.6667, "x": 1.045, "y": 1.13, "curve": "stepped"}, {"time": 10.7, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 12, "x": 1.012, "y": 1.036}]}, "cloud11": {"translate": [{"x": -6.73, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 1.9667, "x": -13.43, "curve": "stepped"}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": -6.73, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 5.9667, "x": -13.43, "curve": "stepped"}, {"time": 6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": -6.73, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 9.9667, "x": -13.43, "curve": "stepped"}, {"time": 10, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 12, "x": -6.73}], "scale": [{"x": 1.022, "y": 1.065, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 1.9667, "x": 1.045, "y": 1.13, "curve": "stepped"}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 1.022, "y": 1.065, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 5.9667, "x": 1.045, "y": 1.13, "curve": "stepped"}, {"time": 6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": 1.022, "y": 1.065, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 9.9667, "x": 1.045, "y": 1.13, "curve": "stepped"}, {"time": 10, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 12, "x": 1.022, "y": 1.065}]}, "cloud8": {"translate": [{"curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 3.9667, "x": 3.73, "curve": "stepped"}, {"time": 4, "curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 7.9667, "x": 3.73, "curve": "stepped"}, {"time": 8, "curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 11.9667, "x": 3.73, "curve": "stepped"}, {"time": 12}], "scale": [{"curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 3.9667, "x": 1.061, "y": 1.061, "curve": "stepped"}, {"time": 4, "curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 7.9667, "x": 1.061, "y": 1.061, "curve": "stepped"}, {"time": 8, "curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 11.9667, "x": 1.061, "y": 1.061, "curve": "stepped"}, {"time": 12}]}, "cloud12": {"translate": [{"x": 1.87, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 1.9667, "x": 3.73, "curve": "stepped"}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 1.87, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 5.9667, "x": 3.73, "curve": "stepped"}, {"time": 6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": 1.87, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 9.9667, "x": 3.73, "curve": "stepped"}, {"time": 10, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 12, "x": 1.87}], "scale": [{"x": 1.03, "y": 1.03, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 1.9667, "x": 1.061, "y": 1.061, "curve": "stepped"}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 1.03, "y": 1.03, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 5.9667, "x": 1.061, "y": 1.061, "curve": "stepped"}, {"time": 6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": 1.03, "y": 1.03, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 9.9667, "x": 1.061, "y": 1.061, "curve": "stepped"}, {"time": 10, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 12, "x": 1.03, "y": 1.03}]}, "bone4": {"rotate": [{"angle": 43.84, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -33.94, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 43.84, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -33.94, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 43.84, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -33.94, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 43.84, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": -33.94, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 43.84, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -33.94, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 43.84, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "angle": -33.94, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 43.84, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": -33.94, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 43.84, "curve": 0.25, "c3": 0.75}, {"time": 3.7667, "angle": -33.94, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 43.84}]}, "bone5": {"rotate": [{"angle": -1.37, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1, "angle": 30.18, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -47.59, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 30.18, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -47.59, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 30.18, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": -47.59, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 30.18, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -47.59, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2, "angle": -1.37, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 2.1, "angle": 30.18, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "angle": -47.59, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 30.18, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "angle": -47.59, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "angle": 30.18, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": -47.59, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": 30.18, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "angle": -47.59, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 4, "angle": -1.37}]}, "bone3": {"rotate": [{"angle": 16.41, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1, "angle": -17.78, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 66.51, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -17.78, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 66.51, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -17.78, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": 66.51, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": -17.78, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 66.51, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2, "angle": 16.41, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 2.1, "angle": -17.78, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "angle": 66.51, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": -17.78, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "angle": 66.51, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "angle": -17.78, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": 66.51, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": -17.78, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "angle": 66.51, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 4, "angle": 16.41}]}, "bone2": {"rotate": [{"angle": -26.93, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 57.37, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -26.93, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 57.37, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -26.93, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 57.37, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -26.93, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": 57.37, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -26.93, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 57.37, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -26.93, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "angle": 57.37, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -26.93, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": 57.37, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -26.93, "curve": 0.25, "c3": 0.75}, {"time": 3.7667, "angle": 57.37, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -26.93}]}, "bone": {"translate": [{"x": -1.43, "curve": 0.188, "c2": 0.65, "c3": 0.75}, {"time": 3.0333, "x": 459.45, "y": -266.72}], "scale": [{"x": 1.3, "y": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 2.0667}]}, "all58": {"rotate": [{"angle": 10.3, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.3333, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 16.62, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 10.3, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 5.3333, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": 16.62, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "angle": 10.3, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 9.3333, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 11.3333, "angle": 16.62, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 12, "angle": 10.3}]}, "all41": {"rotate": [{"angle": 16.17, "curve": 0.329, "c2": 0.32, "c3": 0.676, "c4": 0.69}, {"time": 0.6, "angle": 3.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.2667, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": 26.76, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 4, "angle": 16.17, "curve": 0.329, "c2": 0.32, "c3": 0.676, "c4": 0.69}, {"time": 4.6, "angle": 3.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 5.2667, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "angle": 26.76, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 8, "angle": 16.17, "curve": 0.329, "c2": 0.32, "c3": 0.676, "c4": 0.69}, {"time": 8.6, "angle": 3.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 9.2667, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 11.2667, "angle": 26.76, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 12, "angle": 16.17}]}, "all57": {"rotate": [{"angle": 4.58, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6667, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 30.41, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 4.58, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.6667, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 30.41, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "angle": 4.58, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 8.6667, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 30.41, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 12, "angle": 4.58}]}, "all56": {"rotate": [{"angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 16.62, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 16.62, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": 16.62, "curve": 0.25, "c3": 0.75}, {"time": 12, "angle": -5.65}]}, "all37": {"translate": [{"x": 4.37, "y": 2.17, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 6.1, "y": 3.03, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": 4.37, "y": 2.17, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": 6.1, "y": 3.03, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "x": 4.37, "y": 2.17, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 9.3333, "curve": 0.25, "c3": 0.75}, {"time": 11.3333, "x": 6.1, "y": 3.03, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 12, "x": 4.37, "y": 2.17}]}, "all43": {"rotate": [{"angle": 11.23, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.6, "angle": 16.62, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": -5.65, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4, "angle": 11.23, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 4.6, "angle": 16.62, "curve": 0.25, "c3": 0.75}, {"time": 6.6, "angle": -5.65, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 8, "angle": 11.23, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.6, "angle": 16.62, "curve": 0.25, "c3": 0.75}, {"time": 10.6, "angle": -5.65, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 12, "angle": 11.23}]}, "all38": {"translate": [{"x": -1.34, "y": 1.7, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": -4.74, "y": 6, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": -1.34, "y": 1.7, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": -4.74, "y": 6, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "x": -1.34, "y": 1.7, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 8.6667, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "x": -4.74, "y": 6, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 12, "x": -1.34, "y": 1.7}]}, "all42": {"rotate": [{"angle": 16.49, "curve": 0.276, "c2": 0.08, "c3": 0.625, "c4": 0.48}, {"time": 0.6, "angle": 10.3, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.9333, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "angle": 16.62, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 4, "angle": 16.49, "curve": 0.276, "c2": 0.08, "c3": 0.625, "c4": 0.48}, {"time": 4.6, "angle": 10.3, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 5.9333, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 7.9333, "angle": 16.62, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 8, "angle": 16.49, "curve": 0.276, "c2": 0.08, "c3": 0.625, "c4": 0.48}, {"time": 8.6, "angle": 10.3, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 9.9333, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 11.9333, "angle": 16.62, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 12, "angle": 16.49}]}, "all35": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -28.69, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": -28.69, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": -28.69, "curve": 0.25, "c3": 0.75}, {"time": 12}]}, "all36": {"translate": [{"x": 4.61, "y": -3.05, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 4.61, "y": -3.05, "curve": 0.25, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 8, "x": 4.61, "y": -3.05, "curve": 0.25, "c3": 0.75}, {"time": 10, "curve": 0.25, "c3": 0.75}, {"time": 12, "x": 4.61, "y": -3.05}]}, "all40": {"rotate": [{"angle": -0.26, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.6, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 16.62, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4, "angle": -0.26, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 4.6, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 6.6, "angle": 16.62, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 8, "angle": -0.26, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.6, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 10.6, "angle": 16.62, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 12, "angle": -0.26}]}, "all39": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 39.14, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 19.51, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": 19.51, "curve": 0.25, "c3": 0.75}, {"time": 12}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2, "x": -5.84, "y": 26.5, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": -5.84, "y": 26.5, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 10, "x": -5.84, "y": 26.5, "curve": 0.25, "c3": 0.75}, {"time": 12}], "scale": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 0.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6, "x": 0.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10, "x": 0.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 12}]}, "all59": {"rotate": [{"angle": 16.62, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 16.62, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 16.62, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 12, "angle": 16.62}]}, "all3": {"translate": [{"y": 0.29, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "y": 3.05, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "y": 0.29, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "y": 3.05, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "y": 0.29, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "y": 3.05, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 6, "y": 0.29, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 6.1667, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "y": 3.05, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 8, "y": 0.29, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 9.1667, "y": 3.05, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 10, "y": 0.29, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 10.1667, "curve": 0.25, "c3": 0.75}, {"time": 11.1667, "y": 3.05, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 12, "y": 0.29}]}, "all11": {"rotate": [{"angle": -2.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -2.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -2.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "angle": -2.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.3333, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "angle": -2.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 8.3333, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 10, "angle": -2.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 10.3333, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 11.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 12, "angle": -2.54}]}, "all10": {"rotate": [{"angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 9, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 11, "curve": 0.25, "c3": 0.75}, {"time": 12, "angle": -3.55}]}, "all5": {"translate": [{"y": -4.79, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "y": -5.3, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "y": -4.79, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "y": -5.3, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "y": -4.79, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 4.8333, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "y": -5.3, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 6, "y": -4.79, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 6.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "y": -5.3, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 8, "y": -4.79, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 8.8333, "curve": 0.25, "c3": 0.75}, {"time": 9.8333, "y": -5.3, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 10, "y": -4.79, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 10.8333, "curve": 0.25, "c3": 0.75}, {"time": 11.8333, "y": -5.3, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 12, "y": -4.79}]}, "all8": {"rotate": [{"angle": 3.99, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 3.99, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 3.99, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "angle": 3.99, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.3333, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "angle": 3.99, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 8.3333, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 10, "angle": 3.99, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 10.3333, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 11.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 12, "angle": 3.99}]}, "all7": {"rotate": [{"angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 9, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 11, "curve": 0.25, "c3": 0.75}, {"time": 12, "angle": 5.57}]}, "all9": {"rotate": [{"angle": 1.58, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 1.58, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 1.58, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.6667, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "angle": 1.58, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 6.6667, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "angle": 1.58, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 8.6667, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 10, "angle": 1.58, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 10.6667, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 12, "angle": 1.58}]}, "all4": {"translate": [{"x": 3.5, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 4.89, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 3.5, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 4.89, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": 3.5, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": 4.89, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "x": 3.5, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "x": 4.89, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "x": 3.5, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 8.6667, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "x": 4.89, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 10, "x": 3.5, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 10.6667, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "x": 4.89, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 12, "x": 3.5}]}, "all6": {"translate": [{"y": 3.08, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "y": 3.41, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "y": 3.08, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "y": 3.41, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "y": 3.08, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 4.8333, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "y": 3.41, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 6, "y": 3.08, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 6.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "y": 3.41, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 8, "y": 3.08, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 8.8333, "curve": 0.25, "c3": 0.75}, {"time": 9.8333, "y": 3.41, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 10, "y": 3.08, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 10.8333, "curve": 0.25, "c3": 0.75}, {"time": 11.8333, "y": 3.41, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 12, "y": 3.08}]}, "all2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "y": 3.04, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "y": 3.04, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5, "y": 3.04, "curve": 0.25, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7, "y": 3.04, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9, "y": 3.04, "curve": 0.25, "c3": 0.75}, {"time": 10, "curve": 0.25, "c3": 0.75}, {"time": 11, "y": 3.04, "curve": 0.25, "c3": 0.75}, {"time": 12}]}, "all12": {"rotate": [{"angle": -1.01, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -1.01, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -1.01, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.6667, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "angle": -1.01, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 6.6667, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "angle": -1.01, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 8.6667, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 10, "angle": -1.01, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 10.6667, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 12, "angle": -1.01}]}}, "path": {"loong": {"spacing": [{"spacing": 0.0332, "curve": 0.364, "c2": 0.45, "c3": 0.755}, {"time": 1.9667, "spacing": 0.032, "curve": 0.25, "c3": 0.75}, {"time": 4.6333, "spacing": 0.034, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "spacing": 0.032, "curve": 0.25, "c3": 0.75}, {"time": 10.6333, "spacing": 0.034, "curve": 0.257, "c3": 0.619, "c4": 0.46}, {"time": 12, "spacing": 0.0332}]}}}, "mz_win": {"slots": {"mingzu_bg05": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 8, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 10, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 12, "color": "ffffff00"}]}, "mingzu_bg02": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 8, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 10, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 12, "color": "ffffff00"}]}, "mingzu_bg010": {"color": [{"color": "ffffffac", "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.7333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "color": "ffffff00", "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 4, "color": "ffffffac", "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 4.7333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 6.7333, "color": "ffffff00", "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 8, "color": "ffffffac", "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 8.7333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 10.7333, "color": "ffffff00", "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 12, "color": "ffffffac"}]}, "mingzu_bg10": {"color": [{"color": "ffffff52", "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.7333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "color": "ffffffff", "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 4, "color": "ffffff52", "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 4.7333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6.7333, "color": "ffffffff", "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 8, "color": "ffffff52", "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 8.7333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 10.7333, "color": "ffffffff", "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 12, "color": "ffffff52"}]}, "mingzu_bg2": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 6, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 8, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 10, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 12, "color": "ffffffff"}]}, "mingzu_bg08": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 8, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 10, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 12, "color": "ffffff00"}]}, "mingzu_bg011": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 8, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 10, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 12, "color": "ffffff00"}]}, "hui2": {"color": [{"color": "ffffffd8", "curve": 0.382, "c2": 0.55, "c3": 0.741}, {"time": 0.7667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "color": "ffffff8f", "curve": 0.245, "c3": 0.64, "c4": 0.57}, {"time": 4, "color": "ffffffd8", "curve": 0.382, "c2": 0.55, "c3": 0.741}, {"time": 4.7667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 6.7667, "color": "ffffff8f", "curve": 0.245, "c3": 0.64, "c4": 0.57}, {"time": 8, "color": "ffffffd8", "curve": 0.382, "c2": 0.55, "c3": 0.741}, {"time": 8.7667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 10.7667, "color": "ffffff8f", "curve": 0.245, "c3": 0.64, "c4": 0.57}, {"time": 12, "color": "ffffffd8"}]}, "mingzu_bg6": {"color": [{"color": "ffffff52", "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 1.2667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "color": "ffffff00", "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 4, "color": "ffffff52", "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 5.2667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "color": "ffffff00", "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 8, "color": "ffffff52", "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 9.2667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 11.2667, "color": "ffffff00", "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 12, "color": "ffffff52"}]}, "mingzu_bg04": {"color": [{"color": "ffffff4d", "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.7, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.7, "color": "ffffffff", "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 4, "color": "ffffff4d", "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 4.7, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6.7, "color": "ffffffff", "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 8, "color": "ffffff4d", "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 8.7, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 10.7, "color": "ffffffff", "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 12, "color": "ffffff4d"}]}, "mingzu_bg11": {"color": [{"color": "fffffffd", "curve": 0.257, "c2": 0.03, "c3": 0.751}, {"time": 1.9667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "color": "ffffffff", "curve": 0.328, "c3": 0.661, "c4": 0.34}, {"time": 4, "color": "fffffffd", "curve": 0.257, "c2": 0.03, "c3": 0.751}, {"time": 5.9667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "color": "ffffffff", "curve": 0.328, "c3": 0.661, "c4": 0.34}, {"time": 8, "color": "fffffffd", "curve": 0.257, "c2": 0.03, "c3": 0.751}, {"time": 9.9667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 11.9667, "color": "ffffffff", "curve": 0.328, "c3": 0.661, "c4": 0.34}, {"time": 12, "color": "fffffffd"}]}, "eye": {"attachment": [{"time": 1, "name": "eye"}, {"time": 1.1667, "name": null}, {"time": 3, "name": "eye"}, {"time": 3.1667, "name": null}, {"time": 5, "name": "eye"}, {"time": 5.1667, "name": null}, {"time": 7, "name": "eye"}, {"time": 7.1667, "name": null}, {"time": 9, "name": "eye"}, {"time": 9.1667, "name": null}, {"time": 11, "name": "eye"}, {"time": 11.1667, "name": null}]}, "mingzu_bg012": {"color": [{"color": "ffffff9a", "curve": 0.25, "c3": 0.75}, {"time": 2.2, "color": "ffffff00"}]}, "hui1": {"color": [{"color": "ffffffcc", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffff8f", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffffcc", "curve": 0.25, "c3": 0.75}, {"time": 6, "color": "ffffff8f", "curve": 0.25, "c3": 0.75}, {"time": 8, "color": "ffffffcc", "curve": 0.25, "c3": 0.75}, {"time": 10, "color": "ffffff8f", "curve": 0.25, "c3": 0.75}, {"time": 12, "color": "ffffffcc"}]}, "mingzu_bg8": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 6, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 8, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 10, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 12, "color": "ffffffff"}]}, "mingzu_bg4": {"color": [{"color": "ffffffb1", "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.7, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.7, "color": "ffffff00", "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 4, "color": "ffffffb1", "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 4.7, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 6.7, "color": "ffffff00", "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 8, "color": "ffffffb1", "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 8.7, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 10.7, "color": "ffffff00", "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 12, "color": "ffffffb1"}]}, "mingzu_bg9": {"color": [{"color": "ffffffcb", "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.5333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "color": "ffffff00", "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 4, "color": "ffffffcb", "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 4.5333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 6.5333, "color": "ffffff00", "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 8, "color": "ffffffcb", "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 8.5333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 10.5333, "color": "ffffff00", "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 12, "color": "ffffffcb"}]}, "mingzu_bg09": {"color": [{"color": "ffffff33", "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.5333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "color": "ffffffff", "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 4, "color": "ffffff33", "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 4.5333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 6.5333, "color": "ffffffff", "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 8, "color": "ffffff33", "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 8.5333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 10.5333, "color": "ffffffff", "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 12, "color": "ffffff33"}]}, "mingzu_bg5": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 6, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 8, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 10, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 12, "color": "ffffffff"}]}, "mingzu_bg06": {"color": [{"color": "ffffffac", "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 1.2667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "color": "ffffffff", "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 4, "color": "ffffffac", "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 5.2667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "color": "ffffffff", "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 8, "color": "ffffffac", "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 9.2667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 11.2667, "color": "ffffffff", "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 12, "color": "ffffffac"}]}}, "bones": {"cloud01": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 3.9667, "x": -9.14, "y": 3.05, "curve": "stepped"}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "x": -9.14, "y": 3.05, "curve": "stepped"}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 11.9667, "x": -9.14, "y": 3.05, "curve": "stepped"}, {"time": 12}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 3.9667, "x": 1.074, "y": 1.074, "curve": "stepped"}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "x": 1.074, "y": 1.074, "curve": "stepped"}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 11.9667, "x": 1.074, "y": 1.074, "curve": "stepped"}, {"time": 12}]}, "cloud01_1": {"translate": [{"x": -4.62, "y": 1.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.9667, "x": -9.14, "y": 3.05, "curve": "stepped"}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": -4.62, "y": 1.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.9667, "x": -9.14, "y": 3.05, "curve": "stepped"}, {"time": 6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": -4.62, "y": 1.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 9.9667, "x": -9.14, "y": 3.05, "curve": "stepped"}, {"time": 10, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 12, "x": -4.62, "y": 1.54}], "scale": [{"x": 1.038, "y": 1.038, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.9667, "x": 1.074, "y": 1.074, "curve": "stepped"}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 1.038, "y": 1.038, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.9667, "x": 1.074, "y": 1.074, "curve": "stepped"}, {"time": 6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": 1.038, "y": 1.038, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 9.9667, "x": 1.074, "y": 1.074, "curve": "stepped"}, {"time": 10, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 12, "x": 1.038, "y": 1.038}]}, "cloud02": {"translate": [{"x": 3.06, "y": 2.26, "curve": 0.349, "c2": 0.39, "c3": 0.757}, {"time": 2.7, "x": 11.5, "y": 8.47, "curve": "stepped"}, {"time": 2.7333, "curve": 0.266, "c3": 0.618, "c4": 0.42}, {"time": 4, "x": 3.06, "y": 2.26, "curve": 0.349, "c2": 0.39, "c3": 0.757}, {"time": 6.7, "x": 11.5, "y": 8.47, "curve": "stepped"}, {"time": 6.7333, "curve": 0.266, "c3": 0.618, "c4": 0.42}, {"time": 8, "x": 3.06, "y": 2.26, "curve": 0.349, "c2": 0.39, "c3": 0.757}, {"time": 10.7, "x": 11.5, "y": 8.47, "curve": "stepped"}, {"time": 10.7333, "curve": 0.266, "c3": 0.618, "c4": 0.42}, {"time": 12, "x": 3.06, "y": 2.26}], "scale": [{"x": 1.026, "y": 1.026, "curve": 0.347, "c2": 0.38, "c3": 0.754, "c4": 0.99}, {"time": 2.7, "x": 1.097, "y": 1.097, "curve": "stepped"}, {"time": 2.7333, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 4, "x": 1.026, "y": 1.026, "curve": 0.347, "c2": 0.38, "c3": 0.754, "c4": 0.99}, {"time": 6.7, "x": 1.097, "y": 1.097, "curve": "stepped"}, {"time": 6.7333, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 8, "x": 1.026, "y": 1.026, "curve": 0.347, "c2": 0.38, "c3": 0.754, "c4": 0.99}, {"time": 10.7, "x": 1.097, "y": 1.097, "curve": "stepped"}, {"time": 10.7333, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 12, "x": 1.026, "y": 1.026}]}, "cloud2": {"translate": [{"x": 10.29, "y": 7.58, "curve": 0.372, "c2": 0.62, "c3": 0.712}, {"time": 0.7, "x": 11.5, "y": 8.47, "curve": "stepped"}, {"time": 0.7333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.7333, "x": 5.81, "y": 4.28, "curve": 0.35, "c2": 0.39, "c3": 0.699, "c4": 0.77}, {"time": 4, "x": 10.29, "y": 7.58, "curve": 0.372, "c2": 0.62, "c3": 0.712}, {"time": 4.7, "x": 11.5, "y": 8.47, "curve": "stepped"}, {"time": 4.7333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.7333, "x": 5.81, "y": 4.28, "curve": 0.35, "c2": 0.39, "c3": 0.699, "c4": 0.77}, {"time": 8, "x": 10.29, "y": 7.58, "curve": 0.372, "c2": 0.62, "c3": 0.712}, {"time": 8.7, "x": 11.5, "y": 8.47, "curve": "stepped"}, {"time": 8.7333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.7333, "x": 5.81, "y": 4.28, "curve": 0.35, "c2": 0.39, "c3": 0.699, "c4": 0.77}, {"time": 12, "x": 10.29, "y": 7.58}], "scale": [{"x": 1.086, "y": 1.086, "curve": 0.371, "c2": 0.59, "c3": 0.71, "c4": 0.97}, {"time": 0.7, "x": 1.097, "y": 1.097, "curve": "stepped"}, {"time": 0.7333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.7333, "x": 1.049, "y": 1.049, "curve": 0.349, "c2": 0.38, "c3": 0.698, "c4": 0.77}, {"time": 4, "x": 1.086, "y": 1.086, "curve": 0.371, "c2": 0.59, "c3": 0.71, "c4": 0.97}, {"time": 4.7, "x": 1.097, "y": 1.097, "curve": "stepped"}, {"time": 4.7333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6.7333, "x": 1.049, "y": 1.049, "curve": 0.349, "c2": 0.38, "c3": 0.698, "c4": 0.77}, {"time": 8, "x": 1.086, "y": 1.086, "curve": 0.371, "c2": 0.59, "c3": 0.71, "c4": 0.97}, {"time": 8.7, "x": 1.097, "y": 1.097, "curve": "stepped"}, {"time": 8.7333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10.7333, "x": 1.049, "y": 1.049, "curve": 0.349, "c2": 0.38, "c3": 0.698, "c4": 0.77}, {"time": 12, "x": 1.086, "y": 1.086}]}, "cloud03": {"translate": [{"x": -5.44, "y": 5.44, "curve": 0.363, "c2": 0.6, "c3": 0.7, "c4": 0.96}, {"time": 0.5, "x": -5.84, "y": 5.84, "curve": "stepped"}, {"time": 0.5333, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 4, "x": -5.44, "y": 5.44, "curve": 0.363, "c2": 0.6, "c3": 0.7, "c4": 0.96}, {"time": 4.5, "x": -5.84, "y": 5.84, "curve": "stepped"}, {"time": 4.5333, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 8, "x": -5.44, "y": 5.44, "curve": 0.363, "c2": 0.6, "c3": 0.7, "c4": 0.96}, {"time": 8.5, "x": -5.84, "y": 5.84, "curve": "stepped"}, {"time": 8.5333, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 12, "x": -5.44, "y": 5.44}], "scale": [{"x": 1.045, "y": 1.045, "curve": 0.363, "c2": 0.6, "c3": 0.7, "c4": 0.96}, {"time": 0.5, "x": 1.049, "y": 1.049, "curve": "stepped"}, {"time": 0.5333, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 4, "x": 1.045, "y": 1.045, "curve": 0.363, "c2": 0.6, "c3": 0.7, "c4": 0.96}, {"time": 4.5, "x": 1.049, "y": 1.049, "curve": "stepped"}, {"time": 4.5333, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 8, "x": 1.045, "y": 1.045, "curve": 0.363, "c2": 0.6, "c3": 0.7, "c4": 0.96}, {"time": 8.5, "x": 1.049, "y": 1.049, "curve": "stepped"}, {"time": 8.5333, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 12, "x": 1.045, "y": 1.045}]}, "cloud3": {"translate": [{"x": -1.9, "y": 1.9, "curve": 0.328, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.5333, "x": -2.93, "y": 2.93, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 2.5, "x": -5.84, "y": 5.84, "curve": "stepped"}, {"time": 2.5333, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 4, "x": -1.9, "y": 1.9, "curve": 0.328, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 4.5333, "x": -2.93, "y": 2.93, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 6.5, "x": -5.84, "y": 5.84, "curve": "stepped"}, {"time": 6.5333, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 8, "x": -1.9, "y": 1.9, "curve": 0.328, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 8.5333, "x": -2.93, "y": 2.93, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 10.5, "x": -5.84, "y": 5.84, "curve": "stepped"}, {"time": 10.5333, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 12, "x": -1.9, "y": 1.9}], "scale": [{"x": 1.016, "y": 1.016, "curve": 0.328, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.5333, "x": 1.024, "y": 1.024, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 2.5, "x": 1.049, "y": 1.049, "curve": "stepped"}, {"time": 2.5333, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 4, "x": 1.016, "y": 1.016, "curve": 0.328, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 4.5333, "x": 1.024, "y": 1.024, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 6.5, "x": 1.049, "y": 1.049, "curve": "stepped"}, {"time": 6.5333, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 8, "x": 1.016, "y": 1.016, "curve": 0.328, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 8.5333, "x": 1.024, "y": 1.024, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 10.5, "x": 1.049, "y": 1.049, "curve": "stepped"}, {"time": 10.5333, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 12, "x": 1.016, "y": 1.016}]}, "cloud4": {"translate": [{"curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 3.9667, "x": 10.15, "y": 8.31, "curve": "stepped"}, {"time": 4, "curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 7.9667, "x": 10.15, "y": 8.31, "curve": "stepped"}, {"time": 8, "curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 11.9667, "x": 10.15, "y": 8.31, "curve": "stepped"}, {"time": 12}], "scale": [{"curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 3.9667, "x": 1.028, "y": 1.028, "curve": "stepped"}, {"time": 4, "curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 7.9667, "x": 1.028, "y": 1.028, "curve": "stepped"}, {"time": 8, "curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 11.9667, "x": 1.028, "y": 1.028, "curve": "stepped"}, {"time": 12}]}, "cloud5": {"translate": [{"x": 5.2, "y": 4.26, "curve": 0.374, "c2": 0.5, "c3": 0.746, "c4": 0.98}, {"time": 1.9333, "x": 10.15, "y": 8.31, "curve": "stepped"}, {"time": 1.9667, "curve": 0.249, "c3": 0.626, "c4": 0.5}, {"time": 4, "x": 5.2, "y": 4.26, "curve": 0.374, "c2": 0.5, "c3": 0.746, "c4": 0.98}, {"time": 5.9333, "x": 10.15, "y": 8.31, "curve": "stepped"}, {"time": 5.9667, "curve": 0.249, "c3": 0.626, "c4": 0.5}, {"time": 8, "x": 5.2, "y": 4.26, "curve": 0.374, "c2": 0.5, "c3": 0.746, "c4": 0.98}, {"time": 9.9333, "x": 10.15, "y": 8.31, "curve": "stepped"}, {"time": 9.9667, "curve": 0.249, "c3": 0.626, "c4": 0.5}, {"time": 12, "x": 5.2, "y": 4.26}], "scale": [{"x": 1.014, "y": 1.014, "curve": 0.374, "c2": 0.5, "c3": 0.746, "c4": 0.98}, {"time": 1.9333, "x": 1.028, "y": 1.028, "curve": "stepped"}, {"time": 1.9667, "curve": 0.249, "c3": 0.626, "c4": 0.5}, {"time": 4, "x": 1.014, "y": 1.014, "curve": 0.374, "c2": 0.5, "c3": 0.746, "c4": 0.98}, {"time": 5.9333, "x": 1.028, "y": 1.028, "curve": "stepped"}, {"time": 5.9667, "curve": 0.249, "c3": 0.626, "c4": 0.5}, {"time": 8, "x": 1.014, "y": 1.014, "curve": 0.374, "c2": 0.5, "c3": 0.746, "c4": 0.98}, {"time": 9.9333, "x": 1.028, "y": 1.028, "curve": "stepped"}, {"time": 9.9667, "curve": 0.249, "c3": 0.626, "c4": 0.5}, {"time": 12, "x": 1.014, "y": 1.014}]}, "cloud09": {"translate": [{"x": 4.77, "y": 4.24, "curve": 0.38, "c2": 0.56, "c3": 0.73, "c4": 0.98}, {"time": 1.2333, "x": 6.45, "y": 5.74, "curve": "stepped"}, {"time": 1.2667, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 4, "x": 4.77, "y": 4.24, "curve": 0.38, "c2": 0.56, "c3": 0.73, "c4": 0.98}, {"time": 5.2333, "x": 6.45, "y": 5.74, "curve": "stepped"}, {"time": 5.2667, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 8, "x": 4.77, "y": 4.24, "curve": 0.38, "c2": 0.56, "c3": 0.73, "c4": 0.98}, {"time": 9.2333, "x": 6.45, "y": 5.74, "curve": "stepped"}, {"time": 9.2667, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 12, "x": 4.77, "y": 4.24}], "scale": [{"x": 1.037, "y": 1.037, "curve": 0.38, "c2": 0.56, "c3": 0.73, "c4": 0.98}, {"time": 1.2333, "x": 1.05, "y": 1.05, "curve": "stepped"}, {"time": 1.2667, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 4, "x": 1.037, "y": 1.037, "curve": 0.38, "c2": 0.56, "c3": 0.73, "c4": 0.98}, {"time": 5.2333, "x": 1.05, "y": 1.05, "curve": "stepped"}, {"time": 5.2667, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 8, "x": 1.037, "y": 1.037, "curve": 0.38, "c2": 0.56, "c3": 0.73, "c4": 0.98}, {"time": 9.2333, "x": 1.05, "y": 1.05, "curve": "stepped"}, {"time": 9.2667, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 12, "x": 1.037, "y": 1.037}]}, "cloud9": {"translate": [{"x": 0.72, "y": 0.64, "curve": 0.302, "c2": 0.23, "c3": 0.651, "c4": 0.62}, {"time": 1.2667, "x": 3.24, "y": 2.88, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 3.2333, "x": 6.45, "y": 5.74, "curve": "stepped"}, {"time": 3.2667, "curve": 0.287, "c3": 0.627, "c4": 0.38}, {"time": 4, "x": 0.72, "y": 0.64, "curve": 0.302, "c2": 0.23, "c3": 0.651, "c4": 0.62}, {"time": 5.2667, "x": 3.24, "y": 2.88, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 7.2333, "x": 6.45, "y": 5.74, "curve": "stepped"}, {"time": 7.2667, "curve": 0.287, "c3": 0.627, "c4": 0.38}, {"time": 8, "x": 0.72, "y": 0.64, "curve": 0.302, "c2": 0.23, "c3": 0.651, "c4": 0.62}, {"time": 9.2667, "x": 3.24, "y": 2.88, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 11.2333, "x": 6.45, "y": 5.74, "curve": "stepped"}, {"time": 11.2667, "curve": 0.287, "c3": 0.627, "c4": 0.38}, {"time": 12, "x": 0.72, "y": 0.64}], "scale": [{"x": 1.006, "y": 1.006, "curve": 0.302, "c2": 0.23, "c3": 0.651, "c4": 0.62}, {"time": 1.2667, "x": 1.025, "y": 1.025, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 3.2333, "x": 1.05, "y": 1.05, "curve": "stepped"}, {"time": 3.2667, "curve": 0.287, "c3": 0.627, "c4": 0.38}, {"time": 4, "x": 1.006, "y": 1.006, "curve": 0.302, "c2": 0.23, "c3": 0.651, "c4": 0.62}, {"time": 5.2667, "x": 1.025, "y": 1.025, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 7.2333, "x": 1.05, "y": 1.05, "curve": "stepped"}, {"time": 7.2667, "curve": 0.287, "c3": 0.627, "c4": 0.38}, {"time": 8, "x": 1.006, "y": 1.006, "curve": 0.302, "c2": 0.23, "c3": 0.651, "c4": 0.62}, {"time": 9.2667, "x": 1.025, "y": 1.025, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 11.2333, "x": 1.05, "y": 1.05, "curve": "stepped"}, {"time": 11.2667, "curve": 0.287, "c3": 0.627, "c4": 0.38}, {"time": 12, "x": 1.006, "y": 1.006}]}, "cloud6": {"translate": [{"curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 3.9667, "x": -13.43, "curve": "stepped"}, {"time": 4, "curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 7.9667, "x": -13.43, "curve": "stepped"}, {"time": 8, "curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 11.9667, "x": -13.43, "curve": "stepped"}, {"time": 12}], "scale": [{"curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 3.9667, "x": 1.045, "y": 1.13, "curve": "stepped"}, {"time": 4, "curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 7.9667, "x": 1.045, "y": 1.13, "curve": "stepped"}, {"time": 8, "curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 11.9667, "x": 1.045, "y": 1.13, "curve": "stepped"}, {"time": 12}]}, "cloud7": {"translate": [{"x": -12.05, "curve": 0.37, "c2": 0.59, "c3": 0.708, "c4": 0.97}, {"time": 0.6667, "x": -13.43, "curve": "stepped"}, {"time": 0.7, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 4, "x": -12.05, "curve": 0.37, "c2": 0.59, "c3": 0.708, "c4": 0.97}, {"time": 4.6667, "x": -13.43, "curve": "stepped"}, {"time": 4.7, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 8, "x": -12.05, "curve": 0.37, "c2": 0.59, "c3": 0.708, "c4": 0.97}, {"time": 8.6667, "x": -13.43, "curve": "stepped"}, {"time": 8.7, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 12, "x": -12.05}], "scale": [{"x": 1.04, "y": 1.117, "curve": 0.37, "c2": 0.59, "c3": 0.708, "c4": 0.97}, {"time": 0.6667, "x": 1.045, "y": 1.13, "curve": "stepped"}, {"time": 0.7, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 4, "x": 1.04, "y": 1.117, "curve": 0.37, "c2": 0.59, "c3": 0.708, "c4": 0.97}, {"time": 4.6667, "x": 1.045, "y": 1.13, "curve": "stepped"}, {"time": 4.7, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 8, "x": 1.04, "y": 1.117, "curve": 0.37, "c2": 0.59, "c3": 0.708, "c4": 0.97}, {"time": 8.6667, "x": 1.045, "y": 1.13, "curve": "stepped"}, {"time": 8.7, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 12, "x": 1.04, "y": 1.117}]}, "cloud10": {"translate": [{"x": -3.66, "curve": 0.324, "c2": 0.3, "c3": 0.662, "c4": 0.65}, {"time": 0.7, "x": -6.73, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 2.6667, "x": -13.43, "curve": "stepped"}, {"time": 2.7, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": -3.66, "curve": 0.324, "c2": 0.3, "c3": 0.662, "c4": 0.65}, {"time": 4.7, "x": -6.73, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 6.6667, "x": -13.43, "curve": "stepped"}, {"time": 6.7, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 8, "x": -3.66, "curve": 0.324, "c2": 0.3, "c3": 0.662, "c4": 0.65}, {"time": 8.7, "x": -6.73, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 10.6667, "x": -13.43, "curve": "stepped"}, {"time": 10.7, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 12, "x": -3.66}], "scale": [{"x": 1.012, "y": 1.036, "curve": 0.324, "c2": 0.3, "c3": 0.662, "c4": 0.65}, {"time": 0.7, "x": 1.022, "y": 1.065, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 2.6667, "x": 1.045, "y": 1.13, "curve": "stepped"}, {"time": 2.7, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": 1.012, "y": 1.036, "curve": 0.324, "c2": 0.3, "c3": 0.662, "c4": 0.65}, {"time": 4.7, "x": 1.022, "y": 1.065, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 6.6667, "x": 1.045, "y": 1.13, "curve": "stepped"}, {"time": 6.7, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 8, "x": 1.012, "y": 1.036, "curve": 0.324, "c2": 0.3, "c3": 0.662, "c4": 0.65}, {"time": 8.7, "x": 1.022, "y": 1.065, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 10.6667, "x": 1.045, "y": 1.13, "curve": "stepped"}, {"time": 10.7, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 12, "x": 1.012, "y": 1.036}]}, "cloud11": {"translate": [{"x": -6.73, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 1.9667, "x": -13.43, "curve": "stepped"}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": -6.73, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 5.9667, "x": -13.43, "curve": "stepped"}, {"time": 6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": -6.73, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 9.9667, "x": -13.43, "curve": "stepped"}, {"time": 10, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 12, "x": -6.73}], "scale": [{"x": 1.022, "y": 1.065, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 1.9667, "x": 1.045, "y": 1.13, "curve": "stepped"}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 1.022, "y": 1.065, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 5.9667, "x": 1.045, "y": 1.13, "curve": "stepped"}, {"time": 6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": 1.022, "y": 1.065, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 9.9667, "x": 1.045, "y": 1.13, "curve": "stepped"}, {"time": 10, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 12, "x": 1.022, "y": 1.065}]}, "cloud8": {"translate": [{"curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 3.9667, "x": 3.73, "curve": "stepped"}, {"time": 4, "curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 7.9667, "x": 3.73, "curve": "stepped"}, {"time": 8, "curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 11.9667, "x": 3.73, "curve": "stepped"}, {"time": 12}], "scale": [{"curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 3.9667, "x": 1.061, "y": 1.061, "curve": "stepped"}, {"time": 4, "curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 7.9667, "x": 1.061, "y": 1.061, "curve": "stepped"}, {"time": 8, "curve": 0.249, "c3": 0.746, "c4": 0.98}, {"time": 11.9667, "x": 1.061, "y": 1.061, "curve": "stepped"}, {"time": 12}]}, "cloud12": {"translate": [{"x": 1.87, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 1.9667, "x": 3.73, "curve": "stepped"}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 1.87, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 5.9667, "x": 3.73, "curve": "stepped"}, {"time": 6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": 1.87, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 9.9667, "x": 3.73, "curve": "stepped"}, {"time": 10, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 12, "x": 1.87}], "scale": [{"x": 1.03, "y": 1.03, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 1.9667, "x": 1.061, "y": 1.061, "curve": "stepped"}, {"time": 2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 1.03, "y": 1.03, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 5.9667, "x": 1.061, "y": 1.061, "curve": "stepped"}, {"time": 6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": 1.03, "y": 1.03, "curve": 0.373, "c2": 0.49, "c3": 0.747, "c4": 0.98}, {"time": 9.9667, "x": 1.061, "y": 1.061, "curve": "stepped"}, {"time": 10, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 12, "x": 1.03, "y": 1.03}]}, "bone4": {"rotate": [{"angle": 43.84, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -33.94, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 43.84, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -33.94, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 43.84, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -33.94, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 43.84, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": -33.94, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 43.84, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -33.94, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 43.84, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "angle": -33.94, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 43.84, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": -33.94, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 43.84, "curve": 0.25, "c3": 0.75}, {"time": 3.7667, "angle": -33.94, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 43.84}]}, "bone5": {"rotate": [{"angle": -1.37, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1, "angle": 30.18, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -47.59, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 30.18, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -47.59, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 30.18, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": -47.59, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 30.18, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -47.59, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2, "angle": -1.37, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 2.1, "angle": 30.18, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "angle": -47.59, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 30.18, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "angle": -47.59, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "angle": 30.18, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": -47.59, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": 30.18, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "angle": -47.59, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 4, "angle": -1.37}]}, "bone3": {"rotate": [{"angle": 16.41, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1, "angle": -17.78, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 66.51, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -17.78, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 66.51, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -17.78, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": 66.51, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": -17.78, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 66.51, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2, "angle": 16.41, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 2.1, "angle": -17.78, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "angle": 66.51, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": -17.78, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "angle": 66.51, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "angle": -17.78, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": 66.51, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": -17.78, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "angle": 66.51, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 4, "angle": 16.41}]}, "bone2": {"rotate": [{"angle": -26.93, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 57.37, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -26.93, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 57.37, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -26.93, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 57.37, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -26.93, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": 57.37, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -26.93, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 57.37, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -26.93, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "angle": 57.37, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -26.93, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": 57.37, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -26.93, "curve": 0.25, "c3": 0.75}, {"time": 3.7667, "angle": 57.37, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -26.93}]}, "bone": {"translate": [{"x": -1.43, "curve": 0.188, "c2": 0.65, "c3": 0.75}, {"time": 3.0333, "x": 459.45, "y": -266.72}], "scale": [{"x": 1.3, "y": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 2.0667}]}, "all58": {"rotate": [{"angle": 10.3, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.3333, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 16.62, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 10.3, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 5.3333, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": 16.62, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "angle": 10.3, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 9.3333, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 11.3333, "angle": 16.62, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 12, "angle": 10.3}]}, "all41": {"rotate": [{"angle": 16.17, "curve": 0.329, "c2": 0.32, "c3": 0.676, "c4": 0.69}, {"time": 0.6, "angle": 3.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.2667, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "angle": 26.76, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 4, "angle": 16.17, "curve": 0.329, "c2": 0.32, "c3": 0.676, "c4": 0.69}, {"time": 4.6, "angle": 3.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 5.2667, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "angle": 26.76, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 8, "angle": 16.17, "curve": 0.329, "c2": 0.32, "c3": 0.676, "c4": 0.69}, {"time": 8.6, "angle": 3.55, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 9.2667, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 11.2667, "angle": 26.76, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 12, "angle": 16.17}]}, "all57": {"rotate": [{"angle": 4.58, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6667, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 30.41, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 4.58, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.6667, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "angle": 30.41, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "angle": 4.58, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 8.6667, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": 30.41, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 12, "angle": 4.58}]}, "all56": {"rotate": [{"angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 16.62, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 16.62, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": 16.62, "curve": 0.25, "c3": 0.75}, {"time": 12, "angle": -5.65}]}, "all37": {"translate": [{"x": 4.37, "y": 2.17, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 6.1, "y": 3.03, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": 4.37, "y": 2.17, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 5.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": 6.1, "y": 3.03, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "x": 4.37, "y": 2.17, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 9.3333, "curve": 0.25, "c3": 0.75}, {"time": 11.3333, "x": 6.1, "y": 3.03, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 12, "x": 4.37, "y": 2.17}]}, "all43": {"rotate": [{"angle": 11.23, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.6, "angle": 16.62, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": -5.65, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4, "angle": 11.23, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 4.6, "angle": 16.62, "curve": 0.25, "c3": 0.75}, {"time": 6.6, "angle": -5.65, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 8, "angle": 11.23, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.6, "angle": 16.62, "curve": 0.25, "c3": 0.75}, {"time": 10.6, "angle": -5.65, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 12, "angle": 11.23}]}, "all38": {"translate": [{"x": -1.34, "y": 1.7, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": -4.74, "y": 6, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": -1.34, "y": 1.7, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": -4.74, "y": 6, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "x": -1.34, "y": 1.7, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 8.6667, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "x": -4.74, "y": 6, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 12, "x": -1.34, "y": 1.7}]}, "all42": {"rotate": [{"angle": 16.49, "curve": 0.276, "c2": 0.08, "c3": 0.625, "c4": 0.48}, {"time": 0.6, "angle": 10.3, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.9333, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "angle": 16.62, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 4, "angle": 16.49, "curve": 0.276, "c2": 0.08, "c3": 0.625, "c4": 0.48}, {"time": 4.6, "angle": 10.3, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 5.9333, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 7.9333, "angle": 16.62, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 8, "angle": 16.49, "curve": 0.276, "c2": 0.08, "c3": 0.625, "c4": 0.48}, {"time": 8.6, "angle": 10.3, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 9.9333, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 11.9333, "angle": 16.62, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 12, "angle": 16.49}]}, "all35": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -28.69, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": -28.69, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": -28.69, "curve": 0.25, "c3": 0.75}, {"time": 12}]}, "all36": {"translate": [{"x": 4.61, "y": -3.05, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 4.61, "y": -3.05, "curve": 0.25, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 8, "x": 4.61, "y": -3.05, "curve": 0.25, "c3": 0.75}, {"time": 10, "curve": 0.25, "c3": 0.75}, {"time": 12, "x": 4.61, "y": -3.05}]}, "all40": {"rotate": [{"angle": -0.26, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.6, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 16.62, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4, "angle": -0.26, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 4.6, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 6.6, "angle": 16.62, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 8, "angle": -0.26, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.6, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 10.6, "angle": 16.62, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 12, "angle": -0.26}]}, "all39": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 39.14, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 19.51, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": 19.51, "curve": 0.25, "c3": 0.75}, {"time": 12}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2, "x": -5.84, "y": 26.5, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": -5.84, "y": 26.5, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 10, "x": -5.84, "y": 26.5, "curve": 0.25, "c3": 0.75}, {"time": 12}], "scale": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 0.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6, "x": 0.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 10, "x": 0.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 12}]}, "all59": {"rotate": [{"angle": 16.62, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 16.62, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 16.62, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 12, "angle": 16.62}]}, "all3": {"translate": [{"y": 0.29, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "y": 3.05, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "y": 0.29, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "y": 3.05, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "y": 0.29, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "y": 3.05, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 6, "y": 0.29, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 6.1667, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "y": 3.05, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 8, "y": 0.29, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 8.1667, "curve": 0.25, "c3": 0.75}, {"time": 9.1667, "y": 3.05, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 10, "y": 0.29, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 10.1667, "curve": 0.25, "c3": 0.75}, {"time": 11.1667, "y": 3.05, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 12, "y": 0.29}]}, "all11": {"rotate": [{"angle": -2.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -2.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -2.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "angle": -2.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.3333, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "angle": -2.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 8.3333, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 10, "angle": -2.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 10.3333, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 11.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 12, "angle": -2.54}]}, "all10": {"rotate": [{"angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 9, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 11, "curve": 0.25, "c3": 0.75}, {"time": 12, "angle": -3.55}]}, "all5": {"translate": [{"y": -4.79, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "y": -5.3, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "y": -4.79, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "y": -5.3, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "y": -4.79, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 4.8333, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "y": -5.3, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 6, "y": -4.79, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 6.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "y": -5.3, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 8, "y": -4.79, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 8.8333, "curve": 0.25, "c3": 0.75}, {"time": 9.8333, "y": -5.3, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 10, "y": -4.79, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 10.8333, "curve": 0.25, "c3": 0.75}, {"time": 11.8333, "y": -5.3, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 12, "y": -4.79}]}, "all8": {"rotate": [{"angle": 3.99, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 3.99, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 3.99, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "angle": 3.99, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.3333, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "angle": 3.99, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 8.3333, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 9.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 10, "angle": 3.99, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 10.3333, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 11.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 12, "angle": 3.99}]}, "all7": {"rotate": [{"angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 9, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 11, "curve": 0.25, "c3": 0.75}, {"time": 12, "angle": 5.57}]}, "all9": {"rotate": [{"angle": 1.58, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 1.58, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 1.58, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.6667, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "angle": 1.58, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 6.6667, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "angle": 1.58, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 8.6667, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 10, "angle": 1.58, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 10.6667, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 12, "angle": 1.58}]}, "all4": {"translate": [{"x": 3.5, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 4.89, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 3.5, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 4.89, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": 3.5, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": 4.89, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "x": 3.5, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 6.6667, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "x": 4.89, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "x": 3.5, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 8.6667, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "x": 4.89, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 10, "x": 3.5, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 10.6667, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "x": 4.89, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 12, "x": 3.5}]}, "all6": {"translate": [{"y": 3.08, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "y": 3.41, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "y": 3.08, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "y": 3.41, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "y": 3.08, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 4.8333, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "y": 3.41, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 6, "y": 3.08, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 6.8333, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "y": 3.41, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 8, "y": 3.08, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 8.8333, "curve": 0.25, "c3": 0.75}, {"time": 9.8333, "y": 3.41, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 10, "y": 3.08, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 10.8333, "curve": 0.25, "c3": 0.75}, {"time": 11.8333, "y": 3.41, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 12, "y": 3.08}]}, "all2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "y": 3.04, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "y": 3.04, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5, "y": 3.04, "curve": 0.25, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7, "y": 3.04, "curve": 0.25, "c3": 0.75}, {"time": 8, "curve": 0.25, "c3": 0.75}, {"time": 9, "y": 3.04, "curve": 0.25, "c3": 0.75}, {"time": 10, "curve": 0.25, "c3": 0.75}, {"time": 11, "y": 3.04, "curve": 0.25, "c3": 0.75}, {"time": 12}]}, "all12": {"rotate": [{"angle": -1.01, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -1.01, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -1.01, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.6667, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "angle": -1.01, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 6.6667, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "angle": -1.01, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 8.6667, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 9.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 10, "angle": -1.01, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 10.6667, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 12, "angle": -1.01}]}}, "path": {"loong": {"spacing": [{"spacing": 0.0332, "curve": 0.364, "c2": 0.45, "c3": 0.755}, {"time": 1.9667, "spacing": 0.032, "curve": 0.25, "c3": 0.75}, {"time": 4.6333, "spacing": 0.034, "curve": 0.25, "c3": 0.75}, {"time": 7.9667, "spacing": 0.032, "curve": 0.25, "c3": 0.75}, {"time": 10.6333, "spacing": 0.034, "curve": 0.257, "c3": 0.619, "c4": 0.46}, {"time": 12, "spacing": 0.0332}]}}}}}