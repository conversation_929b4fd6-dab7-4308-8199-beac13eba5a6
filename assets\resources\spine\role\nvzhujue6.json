{"skeleton": {"hash": "NsF+VbSrl2PvA7PxozF22c4yKMw=", "spine": "3.8.75", "x": -131.98, "y": 11.34, "width": 246.32, "height": 411.32, "images": "./images/", "audio": "D:/spine导出/主角换皮动画/主角女/女主角6"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 39.06, "rotation": 1.79, "x": 5.53, "y": 174.53}, {"name": "bone2", "parent": "bone", "length": 36.94, "rotation": 90.58, "x": -3.04, "y": 0.4}, {"name": "bone3", "parent": "bone2", "length": 32.12, "rotation": 15.9, "x": 36.94}, {"name": "bone4", "parent": "bone3", "length": 14.96, "rotation": -15.93, "x": 32.3, "y": -0.06}, {"name": "bone5", "parent": "bone4", "x": 28.59, "y": -13.21}, {"name": "bone6", "parent": "bone4", "x": 30.73, "y": 22.51}, {"name": "bone8", "parent": "bone4", "x": 9.95, "y": -10.21}, {"name": "bone9", "parent": "bone4", "x": 11.3, "y": -27.66}, {"name": "bone10", "parent": "bone4", "x": 16.17, "y": 15.7}, {"name": "bone7", "parent": "bone", "x": -12.39, "y": 131.54, "transform": "noRotationOrReflection", "color": "b81515ff"}, {"name": "bone11", "parent": "bone3", "length": 19.9, "rotation": -146.92, "x": 36.71, "y": 6.43}, {"name": "bone12", "parent": "bone11", "length": 12.46, "rotation": -47.25, "x": 37.86, "y": -10.37}, {"name": "bone13", "parent": "bone3", "x": 28.43, "y": 26.42}, {"name": "bone14", "parent": "bone13", "length": 34.89, "rotation": 150.24, "x": -3.61, "y": 0.88}, {"name": "bone15", "parent": "bone14", "length": 29.68, "rotation": -102.64, "x": 34.89}, {"name": "bone16", "parent": "bone15", "length": 16.69, "rotation": -4.47, "x": 29.68}, {"name": "bone17", "parent": "bone3", "x": 12.42, "y": -19.26}, {"name": "bone18", "parent": "bone17", "length": 37, "rotation": -170.88, "x": -4.73, "y": -0.15}, {"name": "bone19", "parent": "bone18", "length": 31.59, "rotation": 28.58, "x": 36.61, "y": 0.29}, {"name": "bone20", "parent": "bone19", "length": 17.42, "rotation": 5.47, "x": 31.59}, {"name": "bone21", "parent": "bone", "x": -27.03, "y": -0.12}, {"name": "bone23", "parent": "bone", "length": 51.15, "rotation": -95.14, "x": -18.7, "y": -21.25}, {"name": "bone24", "parent": "bone23", "length": 49.04, "rotation": 0.66, "x": 60.3}, {"name": "bone25", "parent": "bone24", "length": 22.35, "rotation": -0.85, "x": 49.04}, {"name": "bone26", "parent": "bone", "length": 45.7, "rotation": -51.69, "x": 7.51, "y": -14.71}, {"name": "bone27", "parent": "bone26", "length": 43.94, "rotation": -79.79, "x": 45.7}, {"name": "bone28", "parent": "bone27", "length": 22.98, "rotation": 53.59, "x": 43.94}, {"name": "bone22", "parent": "bone", "x": 22.78, "y": -7}, {"name": "bone29", "parent": "bone", "x": -23.93, "y": -32.52}, {"name": "bone30", "parent": "bone", "x": -11.36, "y": -46.62}, {"name": "bone31", "parent": "bone30", "x": 4.99, "y": 162.81}, {"name": "bone32", "parent": "bone30", "x": 72.2, "y": 142.62}, {"name": "bone33", "parent": "bone30", "x": -72.64, "y": 138.66}, {"name": "bone34", "parent": "bone30", "x": 51.53, "y": 78.26}, {"name": "bone35", "parent": "bone30", "x": -39.92, "y": 82.24}, {"name": "bone36", "parent": "bone30", "x": -49.92, "y": 33.38}, {"name": "bone37", "parent": "bone30", "x": 67.75, "y": 18.4}, {"name": "bone38", "parent": "bone30", "x": 106.94, "y": 60.7}, {"name": "bone39", "parent": "bone30", "x": -92.46, "y": 65.23}, {"name": "bone40", "parent": "bone30", "x": -108.99, "y": 38}, {"name": "bone41", "parent": "bone4", "x": 59.16, "y": -15.72}, {"name": "bone42", "parent": "bone41", "length": 8.46, "rotation": -57.35, "x": 1.01, "y": -3.32}, {"name": "bone43", "parent": "bone42", "length": 10.02, "rotation": -59.98, "x": 8.46}, {"name": "bone44", "parent": "bone43", "length": 15.86, "rotation": -64.73, "x": 10.02}, {"name": "bone45", "parent": "bone44", "length": 10.29, "rotation": 45.63, "x": 15.79, "y": 0.08}, {"name": "bone46", "parent": "bone41", "length": 8.94, "rotation": 57.83, "x": 0.63, "y": 3.49}, {"name": "bone47", "parent": "bone46", "length": 10.95, "rotation": 28.64, "x": 8.94}, {"name": "bone48", "parent": "bone47", "length": 14.19, "rotation": 75.82, "x": 10.95}, {"name": "bone49", "parent": "bone48", "length": 12.98, "rotation": -31.4, "x": 14.19}, {"name": "bone50", "parent": "bone4", "x": 96.53, "y": -0.8}, {"name": "bone51", "parent": "bone50", "length": 10.61, "rotation": 19.83, "x": 12.51, "y": 7.5}, {"name": "bone52", "parent": "bone51", "length": 15.92, "rotation": -32.71, "x": 10.61}, {"name": "bone53", "parent": "bone", "rotation": -178.11, "x": -82.63, "y": 57.03, "scaleX": -1, "transform": "noRotationOrReflection"}, {"name": "bone54", "parent": "bone53", "x": -0.13, "y": -10.93}], "slots": [{"name": "lunzhuan", "bone": "bone7", "attachment": "lunzhuan"}, {"name": "weimaof", "bone": "bone4", "attachment": "weimaof"}, {"name": "p<PERSON><PERSON><PERSON>", "bone": "root", "attachment": "p<PERSON><PERSON><PERSON>"}, {"name": "ss1", "bone": "bone18", "attachment": "ss1"}, {"name": "bopdy", "bone": "bone2", "attachment": "bopdy"}, {"name": "xiongshi2", "bone": "bone2", "attachment": "xiongshi2"}, {"name": "jings1", "bone": "bone4", "attachment": "jings1"}, {"name": "kuz", "bone": "root", "attachment": "kuz"}, {"name": "qundai", "bone": "root", "attachment": "qundai"}, {"name": "ss2", "bone": "bone14", "attachment": "ss2"}, {"name": "m3", "bone": "bone4", "attachment": "m3"}, {"name": "m2", "bone": "bone4", "attachment": "m2"}, {"name": "m1", "bone": "bone4", "attachment": "m1"}, {"name": "gt", "bone": "root", "attachment": "gt"}, {"name": "tou", "bone": "bone4", "attachment": "tou"}, {"name": "biyan", "bone": "bone5"}, {"name": "tf4", "bone": "bone4", "attachment": "tf4"}, {"name": "erduo", "bone": "bone4", "attachment": "erduo"}, {"name": "jings2", "bone": "bone11", "attachment": "jings2"}, {"name": "xiongs1", "bone": "bone12", "attachment": "xiongs1"}, {"name": "tf3", "bone": "bone4", "attachment": "tf3"}, {"name": "tg", "bone": "bone4", "attachment": "tg"}, {"name": "mianz", "bone": "root", "color": "ffffff85", "attachment": "mianz"}, {"name": "tf2", "bone": "root", "attachment": "tf2"}, {"name": "tf1", "bone": "bone42", "attachment": "tf1"}, {"name": "lhh", "bone": "bone53", "attachment": "lhh"}], "skins": [{"name": "default", "attachments": {"mianz": {"mianz": {"type": "mesh", "uvs": [0.6774, 0.241, 0.7641, 0.26418, 0.844, 0.30822, 0.9103, 0.29663, 0.9868, 0.25259, 0.9936, 0.343, 0.9817, 0.46122, 0.9936, 0.60031, 1, 0.78577, 0.9137, 0.84372, 0.8287, 0.92486, 0.7148, 0.99904, 0.5771, 0.92718, 0.4258, 0.88545, 0.2728, 0.81127, 0.1402, 0.70695, 0.0552, 0.58872, 0.0552, 0.41486, 0.0433, 0.24563, 0.0348, 0.16682, 0.0348, 0.04627, 0.0722, 0.04859, 0.1317, 0.14827, 0.2439, 0.24332, 0.3799, 0.28041, 0.5159, 0.28504, 0.1521, 0.34531, 0.2762, 0.41718, 0.4037, 0.45195, 0.5669, 0.445, 0.6927, 0.41486, 0.8168, 0.44036, 0.9256, 0.45195, 0.8882, 0.62581, 0.7862, 0.67681, 0.6638, 0.6884, 0.5006, 0.66059, 0.3136, 0.60031, 0.1674, 0.51454], "triangles": [17, 26, 38, 16, 17, 38, 37, 27, 28, 38, 27, 37, 37, 28, 36, 15, 16, 38, 15, 38, 37, 14, 15, 37, 14, 37, 13, 35, 29, 30, 35, 30, 34, 36, 29, 35, 13, 37, 36, 12, 36, 35, 13, 36, 12, 10, 11, 35, 10, 35, 34, 12, 35, 11, 7, 33, 32, 34, 31, 33, 33, 7, 8, 9, 33, 8, 34, 33, 9, 10, 34, 9, 6, 32, 5, 34, 30, 31, 31, 2, 32, 30, 1, 31, 36, 28, 29, 30, 29, 0, 38, 26, 27, 28, 24, 25, 26, 23, 27, 30, 0, 1, 31, 1, 2, 19, 20, 21, 19, 21, 22, 18, 19, 22, 26, 22, 23, 18, 22, 26, 17, 18, 26, 27, 23, 24, 3, 4, 5, 2, 3, 32, 29, 25, 0, 27, 24, 28, 28, 25, 29, 32, 3, 5, 33, 31, 32, 7, 32, 6], "vertices": [1, 6, -3.61, -35.61, 1, 1, 6, -4.84, -40.77, 1, 1, 6, -6.97, -45.48, 1, 1, 6, -6.62, -49.47, 1, 1, 6, -4.87, -54.14, 1, 1, 6, -8.86, -54.38, 1, 3, 6, -14.03, -53.46, 0.736, 8, 5.4, -3.3, 0.26129, 7, 6.76, -20.74, 0.00271, 1, 8, -0.74, -3.76, 1, 2, 8, -8.91, -3.81, 0.92393, 7, -7.56, -21.25, 0.07607, 2, 8, -11.25, 1.46, 0.73523, 7, -9.89, -15.98, 0.26477, 2, 8, -14.61, 6.71, 0.42607, 7, -13.25, -10.74, 0.57393, 3, 8, -17.59, 13.67, 0.19444, 7, -16.24, -3.77, 0.80118, 9, -22.46, -29.69, 0.00438, 3, 8, -14.09, 21.79, 0.03592, 7, -12.74, 4.35, 0.87506, 9, -18.96, -21.56, 0.08903, 2, 7, -10.53, 13.35, 0.64666, 9, -16.76, -12.56, 0.35334, 2, 7, -6.9, 22.39, 0.29252, 9, -13.12, -3.53, 0.70748, 3, 6, -22.78, -2.57, 0.00026, 7, -1.99, 30.15, 0.04064, 9, -8.21, 4.24, 0.9591, 2, 6, -17.37, 2.31, 0.05735, 9, -2.8, 9.12, 0.94265, 2, 6, -9.73, 2, 0.38372, 9, 4.84, 8.81, 0.61628, 2, 6, -2.26, 2.41, 0.93006, 9, 12.31, 9.22, 0.06994, 1, 6, 1.23, 2.78, 1, 1, 6, 6.53, 2.56, 1, 1, 6, 6.33, 0.32, 1, 1, 6, 1.81, -3.06, 1, 1, 6, -2.65, -9.62, 1, 1, 6, -4.61, -17.71, 1, 1, 6, -5.15, -25.85, 1, 3, 6, -6.91, -3.93, 0.89583, 7, 13.88, 28.79, 0.00118, 9, 7.66, 2.87, 0.10298, 3, 6, -10.37, -11.24, 0.58112, 7, 10.42, 21.48, 0.05696, 9, 4.2, -4.44, 0.36192, 4, 6, -12.21, -18.82, 0.63285, 8, 7.22, 31.34, 0.0001, 7, 8.58, 13.89, 0.16324, 9, 2.36, -12.02, 0.20381, 4, 6, -12.3, -28.62, 0.7044, 8, 7.13, 21.54, 0.01233, 7, 8.48, 4.1, 0.2297, 9, 2.26, -21.81, 0.05357, 4, 6, -11.29, -36.22, 0.592, 8, 8.15, 13.94, 0.10604, 7, 9.5, -3.5, 0.28704, 9, 3.28, -29.41, 0.01492, 4, 6, -12.71, -43.61, 0.784, 8, 6.72, 6.55, 0.14162, 7, 8.08, -10.89, 0.07413, 9, 1.85, -36.8, 0.00025, 3, 6, -13.49, -50.11, 0.76, 8, 5.95, 0.05, 0.22748, 7, 7.3, -17.39, 0.01252, 2, 8, -1.61, 2.6, 0.88083, 7, -0.25, -14.84, 0.11917, 2, 8, -3.6, 8.81, 0.46873, 7, -2.24, -8.63, 0.53127, 3, 8, -3.81, 16.17, 0.05949, 7, -2.45, -1.27, 0.93657, 9, -8.68, -27.19, 0.00394, 2, 7, -0.83, 8.46, 0.74262, 9, -7.05, -17.45, 0.25738, 2, 7, 2.28, 19.56, 0.25096, 9, -3.95, -6.35, 0.74904, 2, 6, -14.38, -4.55, 0.04473, 9, 0.18, 2.26, 0.95527], "hull": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 0, 50, 36, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64], "width": 60, "height": 44}}, "tf3": {"tf3": {"x": 25.46, "y": 21.63, "rotation": -92.34, "width": 12, "height": 33}}, "qundai": {"qundai": {"type": "mesh", "uvs": [0.1726, 0, 0.33473, 0, 0.51923, 0, 0.74473, 0, 0.91805, 0, 1, 0.03799, 1, 0.1701, 0.93669, 0.41154, 0.82673, 0.60971, 0.6385, 0.86026, 0.42978, 1, 0.20987, 1, 0.08687, 0.88987, 0.01232, 0.76915, 0, 0.50948, 0, 0.30904, 0.06078, 0.13365, 0.42232, 0.18149, 0.3161, 0.44798, 0.22851, 0.74182, 0.67391, 0.20882, 0.59378, 0.49582, 0.44282, 0.78282, 0.18751, 0.20426, 0.13346, 0.45937, 0.10364, 0.72132, 0.22105, 0.89215], "triangles": [24, 23, 18, 18, 17, 21, 14, 15, 24, 25, 14, 24, 19, 24, 18, 25, 24, 19, 13, 14, 25, 22, 18, 21, 19, 18, 22, 9, 21, 8, 22, 21, 9, 12, 13, 25, 26, 12, 25, 19, 26, 25, 26, 19, 22, 11, 12, 26, 10, 26, 22, 10, 22, 9, 11, 26, 10, 17, 1, 2, 23, 0, 1, 23, 1, 17, 16, 0, 23, 15, 16, 23, 18, 23, 17, 24, 15, 23, 4, 5, 6, 20, 2, 3, 17, 2, 20, 4, 20, 3, 21, 17, 20, 4, 6, 20, 7, 20, 6, 7, 21, 20, 8, 21, 7], "vertices": [1, 21, -0.83, 1.95, 1, 3, 28, -39.95, 8.49, 0.12465, 21, 9.86, 1.61, 0.82374, 29, 6.76, 34.01, 0.0516, 3, 28, -27.77, 8.11, 0.4115, 21, 22.04, 1.23, 0.48494, 29, 18.93, 33.63, 0.10355, 3, 28, -12.9, 7.64, 0.80333, 21, 36.91, 0.77, 0.14824, 29, 33.81, 33.16, 0.04843, 3, 28, -1.47, 7.29, 0.98424, 21, 48.34, 0.41, 0.01496, 29, 45.24, 32.8, 0.00079, 2, 28, 3.88, 5.07, 0.9992, 21, 53.69, -1.81, 0.0008, 2, 28, 3.65, -2.06, 0.99423, 29, 50.36, 23.45, 0.00577, 3, 28, -0.93, -14.96, 0.8164, 21, 48.88, -21.84, 0.01972, 29, 45.78, 10.55, 0.16388, 3, 28, -8.52, -25.43, 0.55562, 21, 41.29, -32.31, 0.02854, 29, 38.19, 0.08, 0.41584, 3, 28, -21.36, -38.57, 0.21659, 21, 28.45, -45.44, 0.00399, 29, 25.35, -13.05, 0.77943, 2, 28, -35.36, -45.68, 0.05775, 29, 11.35, -20.16, 0.94225, 2, 28, -49.87, -45.23, 0.00441, 29, -3.16, -19.71, 0.99559, 2, 21, -7.99, -45.91, 0.00024, 29, -11.09, -13.51, 0.99976, 2, 21, -12.7, -39.24, 0.01716, 29, -15.8, -6.84, 0.98284, 2, 21, -13.08, -25.2, 0.23322, 29, -16.18, 7.2, 0.76678, 2, 21, -12.74, -14.38, 0.54459, 29, -15.84, 18.02, 0.45541, 2, 21, -8.43, -5.04, 0.82361, 29, -11.53, 27.36, 0.17639, 3, 28, -34.47, -1.49, 0.23955, 21, 15.34, -8.36, 0.55909, 29, 12.24, 24.03, 0.20136, 3, 28, -41.93, -15.65, 0.0738, 21, 7.88, -22.53, 0.24689, 29, 4.78, 9.87, 0.67931, 2, 28, -48.2, -31.33, 0.00256, 29, -1.5, -5.81, 0.99744, 3, 28, -17.92, -3.48, 0.646, 21, 31.89, -10.36, 0.19722, 29, 28.79, 22.04, 0.15679, 3, 28, -23.69, -18.81, 0.3753, 21, 26.12, -25.68, 0.10542, 29, 23.02, 6.71, 0.51928, 3, 28, -34.14, -33.98, 0.09813, 21, 15.67, -40.86, 0.00371, 29, 12.57, -8.47, 0.89816, 3, 28, -50, -2.23, 0.01727, 21, -0.19, -9.11, 0.76932, 29, -3.29, 23.29, 0.21341, 3, 28, -54, -15.89, 0.00297, 21, -4.19, -22.77, 0.28721, 29, -7.29, 9.63, 0.70982, 2, 21, -6.6, -36.84, 0.01913, 29, -9.7, -4.45, 0.98087, 2, 28, -48.95, -39.43, 0.00412, 29, -2.24, -13.91, 0.99588], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32, 4, 34, 34, 36, 36, 38, 6, 40, 40, 42, 42, 44, 2, 46, 46, 48, 48, 50], "width": 66, "height": 54}}, "piaodai": {"piaodai": {"type": "mesh", "uvs": [0, 0.82836, 0.01913, 0.90027, 0.0702, 0.9619, 0.08438, 0.87561, 0.09715, 0.76261, 0.12268, 0.84069, 0.16097, 0.94136, 0.22764, 0.98245, 0.31416, 0.9619, 0.36664, 0.87972, 0.37799, 0.75029, 0.37232, 0.58181, 0.32409, 0.42567, 0.27445, 0.33321, 0.28438, 0.2572, 0.35671, 0.22021, 0.45317, 0.20583, 0.53543, 0.20172, 0.6404, 0.212, 0.71699, 0.2572, 0.77231, 0.32499, 0.7638, 0.37636, 0.70422, 0.45443, 0.67018, 0.56127, 0.64181, 0.71125, 0.65316, 0.84479, 0.6943, 0.95779, 0.75812, 1, 0.84039, 1, 0.93117, 0.95985, 0.98649, 0.87972, 1, 0.77699, 1, 0.6455, 0.94819, 0.60852, 0.87302, 0.61674, 0.80351, 0.67016, 0.84748, 0.69276, 0.90138, 0.72563, 0.89571, 0.79959, 0.85741, 0.87767, 0.80493, 0.8941, 0.74252, 0.87767, 0.71132, 0.78316, 0.71132, 0.67016, 0.73259, 0.58181, 0.79358, 0.50785, 0.8716, 0.45649, 0.89713, 0.33321, 0.87869, 0.18118, 0.78082, 0.05996, 0.64607, 0, 0.5326, 0, 0.35955, 0.00038, 0.24324, 0.0353, 0.15388, 0.1031, 0.13261, 0.2387, 0.15956, 0.36609, 0.22622, 0.47498, 0.2787, 0.56743, 0.29998, 0.68454, 0.30565, 0.79343, 0.26735, 0.85712, 0.2248, 0.81603, 0.18934, 0.7133, 0.09005, 0.62701, 0.01913, 0.68454, 0.00211, 0.75029, 0.03147, 0.81887, 0.04747, 0.74778, 0.09548, 0.69523, 0.1435, 0.74005, 0.16804, 0.83123, 0.19364, 0.90078, 0.25766, 0.90542, 0.31954, 0.87605, 0.33768, 0.78951, 0.34408, 0.68905, 0.32168, 0.59014, 0.30247, 0.49123, 0.25873, 0.41705, 0.21178, 0.34751, 0.19471, 0.24551, 0.24272, 0.14505, 0.35262, 0.09714, 0.46785, 0.09251, 0.58948, 0.09096, 0.6791, 0.12187, 0.76126, 0.16051, 0.8114, 0.23933, 0.84128, 0.34442, 0.80714, 0.4186, 0.75592, 0.46342, 0.71645, 0.52214, 0.69084, 0.59787, 0.6727, 0.69523, 0.67697, 0.80187, 0.70471, 0.88687, 0.74952, 0.93633, 0.8146, 0.95332, 0.88892, 0.92314, 0.93976, 0.84814, 0.95765, 0.74587, 0.93223, 0.65723], "triangles": [2, 1, 3, 1, 67, 3, 1, 0, 67, 3, 67, 4, 0, 66, 67, 67, 66, 68, 6, 5, 71, 4, 67, 68, 5, 70, 71, 5, 4, 70, 71, 63, 62, 71, 70, 63, 68, 69, 4, 4, 69, 70, 66, 65, 68, 68, 65, 69, 70, 69, 63, 65, 64, 69, 64, 63, 69, 6, 72, 7, 7, 73, 8, 7, 72, 73, 8, 74, 9, 8, 73, 74, 6, 71, 72, 73, 61, 74, 61, 73, 62, 73, 72, 62, 72, 71, 62, 74, 75, 9, 9, 75, 10, 61, 60, 74, 74, 60, 75, 60, 59, 75, 75, 76, 10, 75, 59, 76, 76, 11, 10, 59, 77, 76, 76, 77, 11, 59, 58, 77, 11, 77, 78, 77, 58, 78, 78, 12, 11, 58, 57, 78, 57, 79, 78, 78, 79, 12, 56, 80, 57, 57, 80, 79, 79, 13, 12, 79, 80, 13, 56, 81, 80, 56, 55, 81, 80, 81, 13, 13, 81, 14, 81, 82, 14, 14, 82, 15, 82, 81, 54, 81, 55, 54, 82, 83, 15, 82, 53, 83, 82, 54, 53, 15, 83, 16, 17, 85, 18, 18, 85, 86, 16, 84, 17, 16, 83, 84, 17, 84, 85, 85, 50, 86, 83, 52, 84, 83, 53, 52, 84, 51, 85, 84, 52, 51, 85, 51, 50, 45, 90, 46, 90, 89, 46, 46, 89, 47, 21, 20, 90, 90, 20, 89, 47, 89, 88, 89, 20, 88, 88, 48, 47, 20, 19, 88, 18, 86, 19, 19, 87, 88, 19, 86, 87, 88, 87, 48, 87, 49, 48, 87, 86, 49, 86, 50, 49, 25, 24, 95, 24, 94, 95, 95, 94, 42, 94, 43, 42, 94, 24, 93, 94, 93, 43, 93, 24, 23, 43, 93, 44, 44, 93, 92, 93, 23, 92, 45, 92, 91, 45, 44, 92, 23, 22, 92, 92, 22, 91, 45, 91, 90, 91, 21, 90, 91, 22, 21, 28, 99, 29, 27, 98, 28, 28, 98, 99, 26, 97, 27, 27, 97, 98, 29, 99, 100, 26, 96, 97, 26, 25, 96, 97, 40, 98, 98, 39, 99, 98, 40, 39, 96, 41, 97, 97, 41, 40, 99, 39, 100, 25, 95, 96, 96, 42, 41, 96, 95, 42, 29, 100, 30, 30, 100, 31, 39, 38, 100, 100, 101, 31, 100, 38, 101, 38, 37, 101, 101, 32, 31, 37, 102, 101, 101, 102, 32, 36, 34, 37, 37, 34, 102, 36, 35, 34, 102, 33, 32, 102, 34, 33], "vertices": [2, 39, -26.48, -19.86, 0.17164, 40, -9.95, 7.37, 0.82836, 2, 38, -221.68, -27.33, 0, 40, -5.75, -4.63, 1, 2, 38, -209.8, -37.87, 0, 40, 6.13, -15.18, 1, 3, 38, -205.97, -23.75, 0, 39, -6.57, -28.28, 0.0517, 40, 9.97, -1.05, 0.9483, 4, 38, -202.33, -5.21, 0, 36, -45.47, 22.11, 0.03968, 39, -2.93, -9.74, 0.75107, 40, 13.6, 17.49, 0.20925, 4, 38, -196.64, -18.27, 0, 36, -39.77, 9.04, 0.33126, 39, 2.76, -22.81, 0.65611, 40, 19.29, 4.42, 0.01263, 3, 38, -188.01, -35.16, 0, 36, -31.14, -7.84, 0.63284, 39, 11.39, -39.7, 0.36716, 3, 38, -172.3, -42.44, 0, 36, -15.43, -15.12, 0.83935, 39, 27.11, -46.97, 0.16065, 4, 38, -151.52, -39.7, 0, 35, -4.66, -61.24, 0.00081, 36, 5.35, -12.38, 0.99902, 39, 47.88, -44.23, 0.00017, 2, 35, 8.3, -48.08, 0.11408, 36, 18.31, 0.78, 0.88592, 2, 35, 11.68, -26.82, 0.50526, 36, 21.69, 22.05, 0.49474, 3, 33, 43.91, -55.41, 0.00038, 35, 11.19, 1.01, 0.98599, 36, 21.2, 49.87, 0.01363, 4, 38, -146.38, 48.67, 0, 31, -44.43, -53.44, 0.0002, 33, 33.2, -29.3, 0.32878, 35, 0.48, 27.12, 0.67102, 4, 38, -157.77, 64.28, 0, 31, -55.82, -37.82, 0.0289, 33, 21.81, -13.68, 0.70798, 35, -10.9, 42.74, 0.26311, 3, 31, -53.05, -25.36, 0.1799, 33, 24.58, -1.22, 0.77298, 35, -8.14, 55.2, 0.04712, 3, 31, -35.58, -19.8, 0.53808, 33, 42.05, 4.34, 0.46106, 35, 9.33, 60.76, 0.00086, 3, 32, -79.67, 2.04, 0.0002, 31, -12.47, -18.15, 0.88106, 33, 65.16, 5.99, 0.11874, 4, 38, -94.74, 84.02, 0, 32, -60, 2.1, 0.09237, 31, 7.21, -18.09, 0.90299, 33, 84.84, 6.05, 0.00464, 3, 38, -69.72, 81.54, 0, 32, -34.98, -0.38, 0.52376, 31, 32.23, -20.57, 0.47624, 4, 38, -51.66, 73.52, 0, 34, 3.75, 55.96, 0.01076, 32, -16.91, -8.4, 0.88137, 31, 50.29, -28.59, 0.10787, 3, 34, 16.62, 44.36, 0.19251, 32, -4.05, -20, 0.80662, 31, 63.16, -40.19, 0.00087, 2, 34, 14.32, 35.96, 0.41264, 32, -6.34, -28.4, 0.58736, 2, 34, -0.31, 23.53, 0.78401, 32, -20.98, -40.84, 0.21599, 4, 38, -64.41, 23.72, 0, 37, -25.21, 66.02, 0.0007, 34, -8.99, 6.16, 0.98713, 32, -29.66, -58.2, 0.01217, 3, 38, -71.96, -0.8, 0, 37, -32.76, 41.5, 0.23706, 34, -16.54, -18.36, 0.76293, 3, 38, -69.94, -22.91, 0, 37, -30.74, 19.39, 0.58419, 34, -14.52, -40.47, 0.41581, 2, 37, -21.5, 0.45, 0.85582, 34, -5.28, -59.41, 0.14418, 2, 37, -6.47, -6.99, 0.98199, 34, 9.75, -66.85, 0.01801, 2, 38, -26.01, -49.91, 0.11946, 37, 13.19, -7.61, 0.88054, 2, 38, -4.12, -43.96, 0.45066, 37, 35.08, -1.66, 0.54934, 2, 38, 9.51, -31.16, 0.68826, 37, 48.71, 11.14, 0.31174, 2, 38, 13.27, -14.32, 0.88658, 37, 52.46, 27.98, 0.11342, 1, 38, 13.95, 7.37, 1, 1, 38, 1.76, 13.85, 1, 1, 38, -16.24, 13.06, 1, 1, 38, -33.12, 4.77, 1, 2, 38, -22.73, 0.71, 0.99885, 37, 16.46, 43.01, 0.00115, 2, 38, -10.03, -5.11, 0.95685, 37, 29.17, 37.19, 0.04315, 2, 38, -11.76, -17.27, 0.72337, 37, 27.43, 25.03, 0.27663, 2, 38, -21.31, -29.86, 0.32301, 37, 17.88, 12.44, 0.67699, 3, 38, -33.93, -32.18, 0.06214, 37, 5.26, 10.12, 0.93238, 34, 21.48, -49.74, 0.00548, 2, 37, -9.56, 13.3, 0.83845, 34, 6.66, -46.56, 0.16155, 3, 38, -55.73, -13.18, 1e-05, 37, -16.53, 29.12, 0.47268, 34, -0.31, -30.74, 0.52731, 3, 38, -55.14, 5.45, 0, 37, -15.95, 47.76, 0.11566, 34, 0.27, -12.1, 0.88434, 2, 34, 5.81, 2.31, 0.95771, 32, -14.86, -62.05, 0.04229, 2, 34, 20.76, 14.05, 0.62948, 32, 0.09, -50.31, 0.37052, 2, 34, 39.66, 21.94, 0.36561, 32, 18.99, -42.42, 0.63439, 2, 34, 46.39, 42.08, 0.18711, 32, 25.73, -22.28, 0.81289, 2, 34, 42.77, 67.29, 0.02604, 32, 22.11, 2.93, 0.97396, 3, 38, -35.4, 105.57, 0, 32, -0.65, 23.65, 0.89276, 31, 66.55, 3.46, 0.10724, 3, 38, -67.28, 116.46, 0, 32, -32.53, 34.54, 0.42443, 31, 34.67, 14.35, 0.57557, 4, 38, -94.38, 117.31, 0, 32, -59.63, 35.39, 0.06447, 31, 7.57, 15.2, 0.93497, 33, 85.2, 39.34, 0.00057, 2, 31, -33.77, 16.43, 0.64875, 33, 43.86, 40.57, 0.35125, 2, 31, -61.74, 11.54, 0.28526, 33, 15.9, 35.68, 0.71474, 2, 31, -83.43, 1.02, 0.08296, 33, -5.8, 25.17, 0.91704, 2, 38, -191.16, 80.93, 0, 33, -11.58, 2.96, 1, 3, 38, -185.38, 59.72, 0, 33, -5.8, -18.25, 0.86045, 35, -38.52, 38.17, 0.13955, 3, 38, -170.02, 41.26, 0, 33, 9.56, -36.7, 0.45929, 35, -23.16, 19.72, 0.54071, 4, 38, -157.96, 25.63, 0, 33, 21.62, -52.34, 0.09512, 35, -11.09, 4.08, 0.90294, 36, -1.09, 52.94, 0.00194, 2, 35, -6.62, -15.39, 0.77323, 36, 3.39, 33.47, 0.22677, 2, 35, -5.82, -33.4, 0.27905, 36, 4.18, 15.47, 0.72095, 4, 38, -162.16, -22.07, 0, 35, -15.3, -43.61, 0.00801, 36, -5.29, 5.25, 0.90278, 39, 37.24, -26.6, 0.08921, 3, 38, -172.12, -14.97, 0, 36, -15.25, 12.35, 0.5665, 39, 27.29, -19.51, 0.4335, 3, 38, -180.06, 2.24, 0, 36, -23.19, 29.55, 0.17237, 39, 19.34, -2.3, 0.82763, 2, 39, -3.93, 12.67, 0.98589, 40, 12.6, 39.9, 0.01411, 2, 39, -21.17, 3.71, 0.70549, 40, -4.64, 30.95, 0.29451, 2, 39, -25.57, -7, 0.47411, 40, -9.04, 20.23, 0.52589, 2, 39, -18.91, -18.53, 0.20536, 40, -2.38, 8.7, 0.79464, 3, 36, -57.26, 24.93, 0.00166, 39, -14.72, -6.93, 0.58199, 40, 1.81, 20.3, 0.41636, 2, 39, -2.98, 1.38, 0.97613, 40, 13.55, 28.61, 0.02387, 4, 38, -191.15, -1.83, 0, 36, -34.28, 25.48, 0.11431, 39, 8.25, -6.37, 0.8761, 40, 24.79, 20.86, 0.00958, 4, 38, -185.75, -17.05, 0, 36, -28.88, 10.26, 0.41732, 39, 13.65, -21.59, 0.58252, 40, 30.18, 5.64, 0.00016, 3, 38, -180, -28.72, 0, 36, -23.13, -1.4, 0.6572, 39, 19.41, -33.25, 0.3428, 3, 38, -164.73, -29.96, 0, 36, -7.86, -2.64, 0.90164, 39, 34.67, -34.49, 0.09836, 2, 35, -2.93, -47.12, 0.05944, 36, 7.08, 1.74, 0.94056, 2, 35, 1.85, -32.99, 0.33424, 36, 11.86, 15.88, 0.66576, 2, 35, 3.9, -16.47, 0.73423, 36, 13.9, 32.4, 0.26577, 3, 38, -147.81, 21.56, 0, 33, 31.77, -56.41, 0.00252, 35, -0.95, 0.01, 0.99748, 3, 38, -151.89, 38.01, 0, 33, 27.69, -39.95, 0.21773, 35, -5.02, 16.47, 0.78227, 4, 38, -161.95, 50.58, 0, 31, -60, -51.53, 0.00119, 33, 17.63, -27.39, 0.51471, 35, -15.09, 29.03, 0.48409, 4, 38, -172.81, 62.39, 0, 31, -70.86, -39.71, 0.00427, 33, 6.77, -15.57, 0.80512, 35, -25.95, 40.85, 0.19061, 3, 31, -74.41, -22.77, 0.01548, 33, 3.22, 1.38, 0.9836, 35, -29.5, 57.8, 0.00092, 3, 31, -62.43, -6.56, 0.20134, 33, 15.21, 17.59, 0.79817, 35, -17.51, 74, 0.00049, 2, 31, -35.93, 0.52, 0.59256, 33, 41.7, 24.67, 0.40744, 2, 31, -8.38, 0.43, 0.93962, 33, 69.26, 24.57, 0.06038, 3, 38, -81.26, 101.88, 0, 32, -46.51, 19.96, 0.2432, 31, 20.69, -0.22, 0.7568, 3, 38, -60.01, 96.12, 0, 32, -25.26, 14.2, 0.62599, 31, 41.94, -5.99, 0.37401, 3, 38, -40.59, 89.13, 0, 32, -5.84, 7.21, 0.92858, 31, 61.36, -12.98, 0.07142, 2, 34, 26.4, 58.2, 0.0458, 32, 5.73, -6.16, 0.9542, 2, 34, 32.99, 40.65, 0.22362, 32, 12.33, -23.71, 0.77638, 2, 34, 24.46, 28.67, 0.42406, 32, 3.79, -35.69, 0.57594, 2, 34, 11.99, 21.66, 0.66896, 32, -8.67, -42.7, 0.33104, 2, 34, 2.26, 12.27, 0.88736, 32, -18.41, -52.09, 0.11264, 4, 38, -59.66, 17.53, 0, 37, -20.47, 59.83, 0.01158, 34, -4.25, -0.03, 0.98819, 32, -24.91, -64.39, 0.00023, 3, 38, -64.5, 1.61, 0, 37, -25.3, 43.91, 0.19388, 34, -9.08, -15.95, 0.80611, 3, 38, -64.03, -16.01, 1e-05, 37, -24.83, 26.29, 0.49481, 34, -8.61, -33.57, 0.50519, 2, 37, -18.64, 12.07, 0.76151, 34, -2.42, -47.8, 0.23849, 2, 37, -8.19, 3.57, 0.93544, 34, 8.03, -56.29, 0.06456, 2, 38, -31.93, -42.02, 0.06223, 37, 7.27, 0.29, 0.93777, 2, 38, -14.02, -37.59, 0.36433, 37, 25.18, 4.71, 0.63567, 2, 38, -1.49, -25.6, 0.67627, 37, 37.71, 16.7, 0.32373, 2, 38, 3.31, -8.87, 0.92877, 37, 42.51, 33.43, 0.07123, 1, 38, -2.3, 5.94, 1], "hull": 67, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 0, 132, 2, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 200, 202, 202, 204, 204, 68], "width": 239, "height": 165}}, "weimaof": {"weimaof": {"x": 10.74, "y": -0.28, "rotation": -92.34, "width": 55, "height": 31}}, "tf1": {"tf1": {"type": "mesh", "uvs": [0.0402, 0.48186, 0.0509, 0.30586, 0.10009, 0.14086, 0.27762, 0, 0.51504, 0, 0.7717, 0.04369, 0.8872, 0.25452, 0.9257, 0.47086, 0.84442, 0.63769, 0.82303, 0.77519, 0.89148, 0.81736, 0.99414, 0.77336, 0.99754, 0.84885, 1, 0.90352, 0.8487, 0.99335, 0.65406, 1, 0.47654, 0.89986, 0.40809, 0.75319, 0.45087, 0.59186, 0.40809, 0.47636, 0.25409, 0.46536, 0.10009, 0.52402, 0.22201, 0.2362, 0.47226, 0.1977, 0.65192, 0.3187, 0.66048, 0.5057, 0.62626, 0.68537, 0.66048, 0.8357, 0.80164, 0.89803, 0.90003, 0.88153], "triangles": [15, 28, 14, 15, 27, 28, 15, 16, 27, 14, 29, 13, 14, 28, 29, 29, 12, 13, 28, 10, 29, 28, 9, 10, 28, 27, 9, 29, 10, 12, 10, 11, 12, 17, 26, 27, 27, 26, 9, 9, 26, 8, 26, 25, 8, 16, 17, 27, 17, 18, 26, 26, 18, 25, 8, 25, 7, 18, 19, 25, 19, 24, 25, 25, 24, 7, 24, 6, 7, 24, 19, 23, 6, 24, 5, 24, 23, 5, 23, 4, 5, 23, 3, 4, 21, 0, 20, 0, 1, 20, 1, 22, 20, 23, 19, 22, 19, 20, 22, 1, 2, 22, 23, 22, 3, 22, 2, 3], "vertices": [1, 42, -7.4, -2.25, 1, 1, 42, -2.84, 3.59, 1, 2, 42, 2.58, 8.25, 0.9975, 43, -10.09, -0.96, 0.0025, 2, 42, 11.21, 9.43, 0.64372, 43, -6.79, 7.1, 0.35628, 2, 42, 18.21, 4.53, 0.07977, 43, 0.95, 10.71, 0.92023, 2, 43, 10.1, 12.95, 0.93634, 44, -11.68, 5.6, 0.06366, 3, 43, 17.61, 6.68, 0.41678, 44, -2.8, 9.72, 0.56272, 45, -6.11, 20.03, 0.02049, 3, 43, 22.71, -0.97, 0.01546, 44, 6.29, 11.06, 0.77839, 45, 1.21, 14.47, 0.20615, 2, 44, 13.28, 8.1, 0.2786, 45, 3.98, 7.4, 0.7214, 1, 45, 7.45, 2.71, 1, 1, 45, 10.45, 3.16, 1, 1, 45, 11.82, 7.06, 1, 1, 45, 14.11, 4.86, 1, 1, 45, 15.77, 3.28, 1, 2, 44, 28.22, 8.18, 0.01352, 45, 14.49, -3.22, 0.98648, 2, 44, 28.47, 1.17, 0.22152, 45, 9.65, -8.3, 0.77848, 2, 44, 24.23, -5.2, 0.74438, 45, 2.13, -9.73, 0.25562, 2, 44, 18.06, -7.63, 0.99822, 45, -3.92, -7.02, 0.00178, 3, 42, 2.06, -14.51, 0.02572, 43, 9.36, -12.8, 0.02709, 44, 11.29, -6.06, 0.94719, 3, 42, 3.58, -9.65, 0.27696, 43, 5.92, -9.05, 0.21655, 44, 6.43, -7.58, 0.50649, 3, 42, -0.69, -6.09, 0.86596, 43, 0.7, -10.97, 0.07019, 44, 5.94, -13.12, 0.06385, 3, 42, -6.65, -4.93, 0.99897, 43, -3.29, -15.55, 0.00025, 44, 8.38, -18.67, 0.00078, 1, 42, 3.88, 2.45, 1, 1, 43, 3.07, 2.53, 1, 3, 43, 11.08, 0.66, 0.37415, 44, -0.15, 1.23, 0.62383, 45, -10.32, 12.2, 0.00203, 2, 44, 7.71, 1.5, 0.95974, 45, -4.63, 6.77, 0.04026, 1, 45, -0.26, 0.49, 1, 2, 44, 21.57, 1.44, 0.26275, 45, 5.01, -3.18, 0.73725, 2, 44, 24.21, 6.5, 0.01615, 45, 10.49, -1.53, 0.98385, 1, 45, 12.55, 1.43, 1], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 0, 42, 2, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 22, 24, 24, 26, 58, 24], "width": 36, "height": 42}}, "lunzhuan": {"lunzhuan": {"x": -0.02, "y": 0.05, "width": 242, "height": 234}}, "kuz": {"kuz": {"type": "mesh", "uvs": [0.1718, 0.04881, 0.1524, 0.00299, 0.21319, 0, 0.28045, 0.00668, 0.33866, 0.03403, 0.42015, 0.03772, 0.5301, 0.03551, 0.63099, 0.02146, 0.70213, 0, 0.73447, 0.01481, 0.73317, 0.04659, 0.7267, 0.06507, 0.76551, 0.09833, 0.84829, 0.15968, 0.92753, 0.23069, 1, 0.30817, 1, 0.38162, 0.93985, 0.45306, 0.88351, 0.52853, 0.80603, 0.58186, 0.69157, 0.61808, 0.74792, 0.65631, 0.81659, 0.68046, 0.77503, 0.71951, 0.70506, 0.74486, 0.61632, 0.73121, 0.5583, 0.69903, 0.53897, 0.65273, 0.51297, 0.62701, 0.47397, 0.61444, 0.4693, 0.59622, 0.46974, 0.58157, 0.48474, 0.56157, 0.49387, 0.55051, 0.48909, 0.52004, 0.47996, 0.48687, 0.4793, 0.4552, 0.49822, 0.43731, 0.54648, 0.41793, 0.60387, 0.39892, 0.61887, 0.37321, 0.62343, 0.35719, 0.56652, 0.34044, 0.52089, 0.32217, 0.50351, 0.31151, 0.48613, 0.343, 0.4785, 0.37594, 0.47378, 0.41615, 0.47239, 0.46819, 0.47322, 0.54655, 0.46289, 0.59068, 0.44123, 0.6336, 0.42512, 0.64458, 0.45114, 0.67396, 0.44991, 0.71078, 0.39848, 0.74442, 0.35759, 0.76566, 0.36122, 0.80048, 0.33685, 0.80414, 0.34968, 0.85032, 0.33685, 0.89283, 0.33942, 0.95439, 0.33172, 0.99543, 0.2817, 1, 0.19192, 0.98664, 0.14062, 0.96758, 0.13933, 0.92361, 0.15601, 0.87597, 0.13549, 0.81734, 0.12907, 0.79315, 0.05212, 0.74844, 0, 0.69274, 0, 0.64657, 0.03159, 0.58647, 0.06109, 0.5271, 0.06622, 0.44501, 0.0637, 0.33294, 0.07414, 0.21766, 0.1037, 0.11828, 0.13153, 0.08151, 0.25594, 0.08258, 0.40491, 0.09142, 0.6158, 0.0859, 0.58291, 0.15665, 0.73769, 0.25284, 0.87313, 0.34682, 0.80928, 0.41978, 0.68739, 0.48722, 0.58291, 0.56462, 0.61193, 0.63427, 0.67772, 0.7006, 0.28883, 0.15886, 0.25787, 0.32913, 0.24626, 0.4828, 0.23465, 0.68402, 0.22304, 0.79789, 0.23513, 0.90576], "triangles": [62, 63, 61, 61, 63, 64, 64, 96, 61, 96, 64, 66, 64, 65, 66, 96, 60, 61, 66, 67, 96, 60, 96, 59, 95, 59, 96, 95, 96, 67, 59, 95, 58, 67, 68, 95, 25, 90, 24, 24, 90, 23, 25, 26, 90, 90, 21, 23, 23, 21, 22, 26, 89, 90, 90, 20, 21, 90, 89, 20, 26, 27, 89, 27, 28, 89, 20, 89, 88, 31, 88, 28, 88, 31, 32, 89, 28, 88, 88, 32, 33, 28, 29, 30, 31, 28, 30, 20, 88, 87, 19, 20, 87, 19, 87, 18, 88, 33, 87, 87, 35, 38, 38, 35, 37, 87, 33, 34, 87, 86, 18, 18, 86, 17, 87, 34, 35, 38, 39, 87, 87, 39, 86, 35, 36, 37, 86, 85, 17, 17, 85, 16, 39, 40, 86, 40, 41, 86, 86, 41, 85, 85, 15, 16, 85, 41, 84, 41, 42, 84, 85, 14, 15, 85, 84, 14, 42, 43, 84, 43, 44, 84, 44, 83, 84, 83, 91, 81, 13, 84, 83, 12, 83, 82, 84, 13, 14, 13, 83, 12, 12, 82, 11, 82, 83, 81, 81, 5, 6, 82, 81, 6, 82, 7, 11, 82, 6, 7, 11, 7, 10, 7, 8, 10, 10, 8, 9, 68, 69, 95, 57, 58, 56, 58, 95, 56, 56, 95, 94, 95, 69, 94, 69, 70, 94, 56, 94, 55, 70, 71, 94, 54, 55, 53, 53, 55, 94, 53, 94, 52, 71, 72, 94, 72, 73, 94, 73, 74, 94, 94, 93, 52, 94, 74, 93, 51, 52, 50, 50, 52, 93, 49, 50, 93, 48, 49, 93, 74, 75, 93, 47, 48, 93, 93, 75, 92, 47, 93, 92, 75, 76, 92, 47, 92, 46, 46, 92, 45, 45, 92, 44, 76, 77, 92, 92, 77, 91, 92, 91, 44, 91, 77, 78, 44, 91, 83, 78, 80, 91, 78, 79, 80, 79, 0, 80, 91, 80, 81, 80, 4, 81, 81, 4, 5, 80, 0, 3, 80, 3, 4, 3, 0, 2, 0, 1, 2], "vertices": [2, 22, -20.28, -11.67, 0.93197, 25, -33.85, -19.14, 0.06803, 2, 22, -27.54, -13.88, 0.92035, 25, -40.65, -15.76, 0.07965, 2, 22, -28.34, -8.32, 0.90719, 25, -37.41, -11.17, 0.09281, 2, 22, -27.63, -2.08, 0.85997, 25, -32.6, -7.13, 0.14003, 2, 22, -23.55, 3.52, 0.72514, 25, -25.79, -5.87, 0.27486, 2, 22, -23.39, 11.04, 0.44274, 25, -20.5, -0.52, 0.55726, 2, 22, -24.34, 21.12, 0.11697, 25, -14.26, 7.45, 0.88303, 2, 22, -27.14, 30.25, 0.00729, 25, -10.01, 16.01, 0.99271, 1, 25, -8.44, 23.24, 1, 1, 25, -4.7, 23.98, 1, 1, 25, -0.86, 20.59, 1, 1, 25, 1.03, 18.22, 1, 1, 25, 7.43, 17.5, 1, 1, 25, 19.89, 16.96, 1, 1, 25, 33.33, 15.18, 1, 2, 25, 47.16, 12.24, 0.84131, 26, -11.79, 3.61, 0.15869, 2, 25, 56.21, 4.62, 0.21107, 26, -2.69, 11.16, 0.78893, 2, 26, 9.7, 14.25, 0.9992, 27, -8.86, 36.01, 0.0008, 2, 26, 22.36, 18.02, 0.94759, 27, 1.69, 28.06, 0.05241, 2, 26, 33.52, 18.02, 0.75616, 27, 8.31, 19.08, 0.24384, 2, 26, 44.73, 13.64, 0.13846, 27, 11.44, 7.46, 0.86154, 2, 26, 46.15, 21.56, 0.0045, 27, 18.67, 11.01, 0.9955, 2, 26, 45.11, 28.9, 0, 27, 23.96, 16.21, 1, 2, 26, 52.39, 29.97, 0, 27, 29.14, 10.99, 1, 2, 26, 59.64, 27.63, 0, 27, 31.56, 3.76, 1, 1, 27, 27.46, -3.64, 1, 1, 27, 21.15, -7.57, 1, 1, 27, 13.49, -7.51, 1, 2, 26, 56.33, 1.91, 0.00199, 27, 8.89, -8.84, 0.99801, 2, 26, 57.06, -2.14, 0.02629, 27, 6.07, -11.83, 0.97371, 2, 26, 55.08, -4.35, 0.06609, 27, 3.12, -11.55, 0.93391, 2, 26, 53.24, -5.82, 0.13658, 27, 0.84, -10.94, 0.86342, 2, 26, 49.88, -6.82, 0.32232, 27, -1.96, -8.83, 0.67768, 2, 26, 47.97, -7.31, 0.48557, 27, -3.48, -7.59, 0.51443, 2, 26, 44.48, -10.78, 0.8369, 27, -8.35, -6.83, 0.1631, 3, 25, 38.35, -42.89, 0.00038, 26, 40.91, -14.83, 0.9695, 27, -13.74, -6.37, 0.03013, 4, 22, 43.39, 20.4, 3e-05, 25, 34.42, -39.65, 0.00366, 26, 37.02, -18.14, 0.99489, 27, -18.7, -5.2, 0.00142, 3, 22, 40.41, 21.97, 0.00031, 25, 33.33, -36.46, 0.00933, 26, 33.69, -18.64, 0.99036, 3, 22, 37.03, 26.22, 0.0021, 25, 33.81, -31.06, 0.03369, 26, 28.46, -17.21, 0.96421, 3, 22, 33.67, 31.31, 0.01229, 25, 34.87, -25.05, 0.13678, 26, 22.73, -15.11, 0.85093, 3, 22, 29.46, 32.45, 0.038, 25, 32.59, -21.33, 0.32242, 26, 18.66, -16.69, 0.63958, 3, 22, 26.86, 32.72, 0.07395, 25, 30.89, -19.34, 0.4784, 26, 16.41, -18.01, 0.44765, 3, 22, 24.47, 27.33, 0.20804, 25, 25.45, -21.61, 0.60335, 26, 17.68, -23.76, 0.18862, 3, 22, 21.78, 22.97, 0.38803, 25, 20.5, -22.93, 0.52549, 26, 18.1, -28.87, 0.08648, 3, 22, 20.16, 21.27, 0.52812, 25, 18.15, -23.04, 0.42613, 26, 17.8, -31.2, 0.04575, 4, 22, 25.32, 19.97, 0.79333, 23, -25.6, 20.27, 0.00073, 25, 21, -27.53, 0.19277, 26, 22.72, -29.19, 0.01317, 4, 22, 30.65, 19.58, 0.8973, 23, -20.27, 19.82, 0.01358, 25, 24.61, -31.49, 0.08551, 26, 27.25, -26.34, 0.00361, 4, 22, 37.14, 19.53, 0.891, 23, -13.78, 19.69, 0.0795, 25, 29.28, -35.99, 0.02901, 26, 32.51, -22.54, 0.00049, 3, 22, 45.51, 19.89, 0.6847, 23, -5.41, 19.95, 0.31096, 25, 35.61, -41.48, 0.00434, 2, 22, 58.1, 20.7, 0.20776, 23, 7.19, 20.62, 0.79224, 2, 22, 65.25, 20.17, 0.06331, 23, 14.33, 20, 0.93669, 2, 22, 72.26, 18.58, 0.00947, 23, 21.33, 18.34, 0.99053, 2, 22, 74.12, 17.21, 0.00284, 23, 23.17, 16.94, 0.99716, 1, 23, 27.78, 19.55, 1, 1, 23, 33.71, 19.72, 1, 2, 23, 39.34, 15.25, 0.98522, 24, -9.93, 15.1, 0.01478, 2, 23, 42.93, 11.65, 0.88836, 24, -6.29, 11.56, 0.11164, 2, 23, 48.51, 12.25, 0.63193, 24, -0.71, 12.24, 0.36807, 2, 23, 49.21, 10.03, 0.52252, 24, 0.01, 10.03, 0.47748, 2, 23, 56.58, 11.56, 0.055, 24, 7.36, 11.67, 0.945, 1, 24, 14.27, 10.92, 1, 1, 24, 24.14, 11.76, 1, 1, 24, 30.78, 11.46, 1, 1, 24, 31.8, 6.92, 1, 1, 24, 30.16, -1.46, 1, 1, 24, 27.39, -6.36, 1, 1, 24, 20.33, -6.92, 1, 1, 24, 12.58, -5.86, 1, 2, 23, 52.2, -8.37, 0.29338, 24, 3.28, -8.32, 0.70661, 2, 23, 48.34, -9.14, 0.70915, 24, -0.57, -9.15, 0.29085, 2, 23, 41.48, -16.55, 0.99569, 24, -7.32, -16.66, 0.00431, 1, 23, 32.75, -21.76, 1, 1, 23, 25.32, -22.11, 1, 2, 22, 66.89, -19.48, 0.01779, 23, 15.52, -19.66, 0.98221, 2, 22, 57.19, -17.33, 0.21123, 23, 5.84, -17.4, 0.78877, 2, 22, 43.97, -17.63, 0.80866, 23, -7.38, -17.55, 0.19134, 1, 22, 25.97, -18.92, 1, 1, 22, 7.39, -19.05, 1, 2, 22, -8.75, -17.27, 0.99525, 25, -29.33, -31.14, 0.00475, 2, 22, -14.81, -15.06, 0.9726, 25, -32.21, -25.37, 0.0274, 2, 22, -15.3, -3.62, 0.88561, 25, -24.71, -16.73, 0.11439, 2, 22, -14.68, 10.15, 0.50548, 25, -14.79, -7.16, 0.49452, 2, 22, -16.7, 29.46, 0.00843, 25, -2.98, 8.26, 0.99157, 3, 22, -5.16, 27.11, 0.01553, 25, 3.79, -1.4, 0.98447, 26, -6.05, -41.5, 0, 3, 22, 9.47, 42.23, 0.00114, 25, 24.81, -0.48, 0.99697, 26, -3.23, -20.65, 0.00189, 3, 22, 23.85, 55.55, 0, 25, 44.41, -0.69, 0.67998, 26, 0.45, -1.4, 0.32002, 1, 26, 13.25, 1.58, 1, 3, 25, 50.69, -28.33, 3e-05, 26, 28.76, -0.11, 0.99952, 27, -9.1, 12.14, 0.00045, 2, 26, 44.49, 0.45, 0.34537, 27, 0.69, -0.18, 0.65463, 1, 27, 12.21, -0.28, 1, 1, 27, 24.04, 3.03, 1, 2, 22, -3.22, 0.12, 0.95456, 25, -13.37, -22.32, 0.04544, 1, 22, 24.31, -1.12, 1, 2, 22, 49.08, -0.74, 0.92805, 23, -2.08, -0.72, 0.07195, 1, 23, 30.33, -0.27, 1, 2, 23, 48.69, -0.47, 0.56979, 24, -0.34, -0.48, 0.43021, 1, 24, 16.92, 1.7, 1], "hull": 80, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 0, 158, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192], "width": 92, "height": 161}}, "ss1": {"ss1": {"type": "mesh", "uvs": [0.03204, 0, 0.12852, 0, 0.22686, 0.07879, 0.29922, 0.23863, 0.36973, 0.38289, 0.40312, 0.47646, 0.50146, 0.53884, 0.62021, 0.63435, 0.7371, 0.72403, 0.85214, 0.7903, 0.96346, 0.87218, 1, 0.90337, 0.98944, 0.9677, 0.88739, 1, 0.73153, 1, 0.63505, 0.93651, 0.57753, 0.86633, 0.43096, 0.7903, 0.29551, 0.71233, 0.19161, 0.59732, 0.11182, 0.41018, 0.01349, 0.27177, 0, 0.13337, 0, 0.05539, 0.09883, 0.14117, 0.20273, 0.33416, 0.31962, 0.57978, 0.2866, 0.51038, 0.37157, 0.62072, 0.54784, 0.72598, 0.66473, 0.8059, 0.80945, 0.88583], "triangles": [30, 7, 8, 16, 29, 30, 31, 8, 9, 31, 9, 10, 30, 8, 31, 15, 16, 30, 15, 30, 31, 12, 10, 11, 14, 15, 31, 13, 31, 10, 13, 10, 12, 14, 31, 13, 28, 26, 5, 28, 5, 6, 18, 26, 28, 29, 6, 7, 28, 6, 29, 17, 28, 29, 18, 28, 17, 29, 7, 30, 17, 29, 16, 24, 22, 23, 24, 0, 1, 24, 1, 2, 24, 23, 0, 21, 22, 24, 3, 25, 24, 3, 24, 2, 21, 24, 25, 25, 3, 4, 20, 21, 25, 27, 25, 4, 27, 4, 5, 20, 25, 27, 26, 27, 5, 19, 20, 27, 19, 27, 26, 18, 19, 26], "vertices": [1, 18, -12.88, -0.18, 1, 1, 18, -9.19, 6.93, 1, 1, 18, 0.09, 11.31, 1, 2, 18, 14.06, 10.84, 0.99486, 19, -14.76, 20.05, 0.00514, 2, 18, 26.87, 10.79, 0.73034, 19, -3.53, 13.88, 0.26966, 2, 18, 34.71, 9.85, 0.13732, 19, 2.9, 9.31, 0.86268, 1, 19, 12.43, 9.79, 1, 2, 19, 24.82, 9.05, 0.93658, 20, -5.88, 9.66, 0.06342, 2, 19, 36.82, 8.61, 0.0878, 20, 6.03, 8.07, 0.9122, 1, 20, 16.92, 8.04, 1, 1, 20, 28.12, 6.78, 1, 1, 20, 31.97, 6.06, 1, 1, 20, 33.63, 1.18, 1, 1, 20, 27.41, -5.11, 1, 1, 20, 16.05, -11.3, 1, 2, 19, 39.2, -10.04, 0.03113, 20, 6.61, -10.72, 0.96887, 2, 19, 32.14, -8.12, 0.46007, 20, -0.23, -8.13, 0.53993, 2, 18, 57.79, 0.5, 0.02249, 19, 18.69, -9.95, 0.97751, 2, 18, 47.15, -6.65, 0.48578, 19, 5.93, -11.14, 0.51422, 1, 18, 35.11, -10.13, 1, 1, 18, 18.94, -9.21, 1, 1, 18, 5.48, -11.43, 1, 1, 18, -4.74, -7.39, 1, 1, 18, -10.21, -4.56, 1, 1, 18, -0.42, -0.39, 1, 2, 18, 17.08, 0.25, 0.99987, 19, -17.17, 9.31, 0.00013, 2, 18, 38.77, -0.06, 0.44848, 19, 1.73, -1.34, 0.55152, 2, 18, 32.64, 0.03, 0.99845, 19, -3.61, 1.67, 0.00155, 2, 18, 43.63, 2.29, 0.06598, 19, 7.11, -1.6, 0.93402, 1, 19, 23.89, -0.31, 1, 1, 20, 3.84, -0.48, 1, 1, 20, 17.41, -0.28, 1], "hull": 24, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 0, 46, 0, 48, 48, 50, 50, 54, 54, 52, 52, 56, 56, 58, 58, 60, 60, 62], "width": 83, "height": 79}}, "ss2": {"ss2": {"type": "mesh", "uvs": [0.91728, 0, 0.97925, 0.07664, 1, 0.23891, 0.97221, 0.40117, 0.9384, 0.52759, 0.9046, 0.63702, 0.86516, 0.8257, 0.80319, 1, 0.6708, 0.97664, 0.50078, 0.86532, 0.28278, 0.70725, 0.13278, 0.5974, 0.02878, 0.39646, 0, 0.27321, 0.04678, 0.23838, 0.16078, 0.25446, 0.28078, 0.24374, 0.35478, 0.37234, 0.41278, 0.49023, 0.50278, 0.54114, 0.62278, 0.57597, 0.63878, 0.45272, 0.66878, 0.27321, 0.75278, 0.04816, 0.81478, 0, 0.87278, 0.15264, 0.83278, 0.34019, 0.78678, 0.54381, 0.74278, 0.82781, 0.57478, 0.75011, 0.41278, 0.6483, 0.34678, 0.584, 0.18478, 0.43932], "triangles": [12, 13, 14, 32, 15, 16, 32, 16, 17, 31, 32, 17, 31, 17, 18, 12, 15, 32, 15, 12, 14, 11, 32, 31, 11, 12, 32, 31, 18, 30, 10, 11, 31, 10, 31, 30, 30, 18, 19, 29, 19, 20, 30, 19, 29, 29, 20, 28, 9, 30, 29, 10, 30, 9, 8, 29, 28, 9, 29, 8, 8, 28, 7, 25, 24, 0, 25, 0, 1, 23, 24, 25, 25, 1, 2, 22, 23, 25, 26, 22, 25, 26, 25, 2, 3, 26, 2, 21, 22, 26, 4, 26, 3, 27, 21, 26, 27, 26, 4, 20, 21, 27, 5, 27, 4, 5, 28, 27, 28, 20, 27, 6, 28, 5, 7, 28, 6], "vertices": [1, 14, -11.52, 2.87, 1, 1, 14, -8.42, 7.99, 1, 1, 14, -0.29, 11.15, 1, 1, 14, 8.53, 10.93, 1, 1, 14, 15.58, 9.91, 1, 1, 14, 21.74, 8.72, 1, 2, 14, 32.1, 7.97, 0.99297, 15, -7.16, -4.47, 0.00703, 2, 14, 42.03, 5.5, 0.38866, 15, -6.93, 5.76, 0.61134, 2, 14, 42.69, -3.96, 0.00258, 15, 2.16, 8.48, 0.99742, 1, 15, 15.59, 8.03, 1, 2, 15, 33.14, 6.71, 0.16151, 16, 2.92, 6.96, 0.83849, 1, 16, 15.06, 6.95, 1, 1, 16, 26.64, 1.14, 1, 1, 16, 31.57, -3.62, 1, 1, 16, 29.53, -6.83, 1, 1, 16, 22.02, -9.96, 1, 1, 16, 14.81, -14.54, 1, 2, 15, 35.73, -11.58, 0.01777, 16, 6.94, -11.07, 0.98223, 2, 15, 29.42, -7.56, 0.4404, 16, 0.33, -7.56, 0.5596, 3, 14, 22.45, -20.25, 0.01713, 15, 22.48, -7.71, 0.95894, 16, -6.57, -8.25, 0.02394, 2, 14, 22.56, -11.53, 0.44407, 15, 13.95, -9.51, 0.55593, 2, 14, 15.93, -11.72, 0.88896, 15, 15.59, -15.93, 0.11104, 2, 14, 6.18, -11.53, 0.99694, 15, 17.53, -25.49, 0.00306, 1, 14, -6.69, -8.07, 1, 1, 14, -10.07, -4.26, 1, 1, 14, -2.97, 1.39, 1, 1, 14, 7.34, 0.59, 1, 2, 14, 18.57, -0.46, 0.9972, 15, 4.02, -15.83, 0.0028, 2, 14, 33.94, -0.52, 0.65767, 15, 0.72, -0.81, 0.34233, 1, 15, 13.29, 0.31, 1, 1, 15, 25.99, 0.09, 1, 2, 15, 31.66, -1.11, 0.02386, 16, 2.06, -0.95, 0.97614, 1, 16, 15.83, -2.17, 1], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48, 0, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64], "width": 71, "height": 53}}, "xiongshi2": {"xiongshi2": {"x": 20.7, "y": 0.89, "rotation": -92.37, "width": 47, "height": 30}}, "jings1": {"jings1": {"x": 3.58, "y": 0.51, "rotation": -92.34, "width": 22, "height": 11}}, "jings2": {"jings2": {"x": 18.18, "y": -6.08, "rotation": 38.66, "width": 49, "height": 36}}, "bopdy": {"bopdy": {"type": "mesh", "uvs": [0.43147, 0, 0.52895, 0.00813, 0.58538, 0.05125, 0.60077, 0.16698, 0.65208, 0.19534, 0.7564, 0.23051, 0.88979, 0.25775, 0.9257, 0.31788, 0.92399, 0.37575, 0.98385, 0.46084, 0.9924, 0.54707, 0.93938, 0.61628, 0.92228, 0.71726, 0.93083, 0.79669, 0.96845, 0.85001, 0.93938, 0.93738, 0.8248, 0.99978, 0.63327, 1, 0.42463, 0.97255, 0.29637, 0.91015, 0.27072, 0.84547, 0.3015, 0.72974, 0.25533, 0.64805, 0.10825, 0.53346, 0.01078, 0.43928, 0, 0.34057, 0.01762, 0.26228, 0.14759, 0.22257, 0.25191, 0.19421, 0.28953, 0.15677, 0.29295, 0.05465, 0.34596, 0.00927, 0.44515, 0.07734, 0.47251, 0.18853, 0.5717, 0.32582, 0.64695, 0.4268, 0.68457, 0.55388, 0.69654, 0.70932, 0.69654, 0.8659, 0.65208, 0.24867, 0.75811, 0.3054, 0.79573, 0.40978, 0.84361, 0.51984, 0.8248, 0.61288, 0.82309, 0.7184, 0.82822, 0.85455, 0.81625, 0.91809, 0.30321, 0.26569, 0.31518, 0.33944, 0.38872, 0.44155, 0.4537, 0.55161, 0.49133, 0.65259, 0.50159, 0.72634, 0.50672, 0.84774, 0.51356, 0.89653, 0.13562, 0.36667, 0.22283, 0.45517, 0.34083, 0.58338, 0.35793, 0.69684, 0.36478, 0.77513], "triangles": [55, 26, 27, 25, 26, 55, 27, 28, 47, 55, 27, 47, 24, 25, 55, 48, 56, 55, 48, 55, 47, 24, 55, 56, 23, 24, 56, 23, 56, 57, 40, 5, 6, 39, 5, 40, 40, 6, 7, 8, 40, 7, 41, 40, 8, 35, 40, 41, 9, 42, 41, 9, 41, 8, 32, 0, 1, 32, 1, 2, 31, 0, 32, 30, 31, 32, 29, 30, 32, 32, 2, 3, 33, 32, 3, 29, 32, 33, 47, 29, 33, 28, 29, 47, 3, 39, 33, 39, 4, 5, 33, 39, 34, 4, 39, 3, 34, 39, 40, 47, 33, 34, 48, 47, 34, 35, 34, 40, 49, 48, 34, 49, 34, 35, 56, 48, 49, 50, 49, 35, 42, 35, 41, 57, 56, 49, 42, 9, 10, 42, 36, 35, 50, 35, 36, 57, 49, 50, 43, 36, 42, 11, 43, 42, 10, 11, 42, 22, 23, 57, 51, 50, 36, 57, 50, 51, 58, 57, 51, 22, 57, 58, 37, 36, 43, 51, 36, 37, 12, 43, 11, 44, 37, 43, 12, 44, 43, 52, 51, 37, 58, 51, 52, 21, 22, 58, 59, 58, 52, 21, 58, 59, 20, 21, 59, 53, 52, 37, 59, 52, 53, 13, 45, 44, 13, 44, 12, 45, 13, 14, 38, 53, 37, 38, 37, 44, 38, 44, 45, 54, 53, 38, 53, 19, 20, 53, 20, 59, 19, 53, 54, 46, 38, 45, 15, 45, 14, 46, 45, 15, 18, 19, 54, 16, 46, 15, 17, 54, 38, 17, 38, 46, 17, 46, 16, 18, 54, 17], "vertices": [1, 4, 21.12, 1.02, 1, 1, 4, 20, -5.66, 1, 3, 3, 44.49, -13.28, 0.00021, 4, 15.36, -9.37, 0.99954, 17, 32.07, 5.98, 0.00024, 3, 3, 32.73, -10.52, 0.3506, 4, 3.29, -9.94, 0.56967, 17, 20.31, 8.74, 0.07973, 3, 3, 28.82, -12.96, 0.5482, 4, 0.2, -13.36, 0.19643, 17, 16.39, 6.3, 0.25537, 3, 3, 23.09, -18.65, 0.28301, 4, -3.75, -20.4, 0.01858, 17, 10.67, 0.61, 0.69841, 2, 3, 17.52, -26.5, 0.00432, 17, 5.09, -7.24, 0.99568, 2, 2, 54.69, -22.91, 0.0188, 17, -1.62, -7.63, 0.9812, 3, 2, 48.68, -22.54, 0.12823, 3, 5.12, -24.89, 0.0112, 17, -7.3, -5.63, 0.86057, 3, 2, 39.67, -26.3, 0.41635, 3, -4.58, -26.04, 0.0193, 17, -17, -6.78, 0.56435, 3, 2, 30.68, -26.52, 0.62431, 3, -13.28, -23.79, 0.00459, 17, -25.7, -4.53, 0.3711, 3, 2, 23.64, -22.57, 0.79961, 3, -18.97, -18.06, 7e-05, 17, -31.39, 1.2, 0.20033, 2, 2, 13.2, -20.96, 0.94907, 17, -40.99, 5.61, 0.05093, 2, 2, 4.92, -21.2, 0.98966, 17, -49.02, 7.64, 0.01034, 2, 2, -0.73, -23.57, 0.99781, 17, -55.1, 6.91, 0.00219, 2, 2, -9.72, -21.19, 1, 17, -63.1, 11.66, 0, 1, 2, -15.88, -13.02, 1, 1, 2, -15.36, 0.18, 1, 1, 2, -11.91, 14.45, 1, 2, 2, -5.06, 23.02, 0.99764, 13, -62.52, 7.23, 0.00236, 3, 2, 1.73, 24.51, 0.99148, 3, -27.14, 33.22, 0.00037, 13, -55.58, 6.8, 0.00815, 3, 2, 13.67, 21.9, 0.90652, 3, -16.38, 27.43, 0.03158, 13, -44.81, 1.01, 0.0619, 3, 2, 22.29, 24.73, 0.66437, 3, -7.31, 27.79, 0.13335, 13, -35.75, 1.37, 0.20228, 3, 2, 34.62, 34.38, 0.21617, 3, 7.19, 33.7, 0.18314, 13, -21.25, 7.28, 0.60069, 3, 2, 44.68, 40.69, 0.06086, 3, 18.59, 37.01, 0.06099, 13, -9.84, 10.59, 0.87815, 3, 2, 54.97, 41.01, 0.00647, 3, 28.58, 34.5, 0.00075, 13, 0.14, 8.08, 0.99277, 2, 4, -4.97, 30.67, 0.0038, 13, 7.49, 4.38, 0.9962, 3, 3, 37.04, 20.99, 0.10015, 4, -1.21, 21.54, 0.10281, 13, 8.6, -5.43, 0.79704, 3, 3, 37.58, 13.23, 0.22118, 4, 1.44, 14.23, 0.40599, 13, 9.15, -13.19, 0.37283, 3, 3, 40.47, 9.54, 0.0972, 4, 5.23, 11.47, 0.77039, 13, 12.03, -16.88, 0.13241, 2, 4, 15.83, 10.8, 0.99494, 13, 22.05, -20.43, 0.00506, 2, 4, 20.39, 6.96, 0.99997, 13, 25.38, -25.38, 3e-05, 2, 4, 13.04, 0.41, 0.99989, 13, 16.51, -29.66, 0.00011, 3, 3, 33.37, -1.41, 0.06521, 4, 1.41, -1.01, 0.93207, 17, 20.95, 17.85, 0.00272, 2, 3, 17.67, -3.44, 0.92097, 17, 5.25, 15.82, 0.07903, 3, 2, 44.17, -3.22, 0.02058, 3, 6.07, -5.08, 0.83817, 17, -6.35, 14.18, 0.14125, 3, 2, 30.85, -5.27, 0.91749, 3, -7.29, -3.4, 0.02442, 17, -19.72, 15.86, 0.05809, 2, 2, 14.67, -5.43, 0.99064, 17, -35.33, 20.14, 0.00936, 2, 2, -1.6, -4.75, 0.99996, 17, -50.79, 25.24, 4e-05, 3, 3, 23.55, -11.22, 0.59154, 4, -5.34, -13.13, 0.0595, 17, 11.13, 8.04, 0.34897, 3, 3, 15.66, -16.32, 0.19384, 4, -11.54, -20.2, 0.00113, 17, 3.23, 2.94, 0.80503, 3, 2, 45.51, -13.55, 0.14605, 3, 4.53, -15.38, 0.22201, 17, -7.89, 3.88, 0.63194, 3, 2, 33.94, -16.38, 0.59779, 3, -7.37, -14.93, 0.06528, 17, -19.79, 4.33, 0.33693, 3, 2, 24.32, -14.68, 0.86074, 3, -16.15, -10.67, 0.00268, 17, -28.58, 8.59, 0.13658, 2, 2, 13.36, -14.11, 0.96353, 17, -38.96, 12.14, 0.03647, 2, 2, -0.8, -13.88, 0.99829, 17, -52.52, 16.25, 0.00171, 2, 2, -7.37, -12.78, 0.99998, 17, -58.53, 19.1, 2e-05, 4, 2, 61.89, 19.79, 0.00016, 3, 29.42, 12.19, 0.46371, 4, -6.13, 10.99, 0.15652, 13, 0.98, -14.22, 0.37961, 4, 2, 54.19, 19.28, 0.01312, 3, 21.87, 13.81, 0.56524, 4, -13.83, 10.48, 0.01554, 13, -6.56, -12.61, 0.4061, 3, 2, 43.37, 14.65, 0.12037, 3, 10.2, 12.32, 0.65837, 13, -18.24, -14.1, 0.22126, 3, 2, 31.75, 10.64, 0.63572, 3, -2.08, 11.65, 0.2684, 13, -30.51, -14.77, 0.09588, 3, 2, 21.15, 8.48, 0.93506, 3, -12.86, 12.48, 0.02909, 13, -41.3, -13.94, 0.03586, 3, 2, 13.46, 8.09, 0.9804, 3, -20.37, 14.21, 0.00441, 13, -48.8, -12.21, 0.01519, 2, 2, 0.83, 8.25, 0.99842, 13, -60.9, -8.59, 0.00158, 2, 2, -4.26, 7.99, 0.99979, 13, -65.87, -7.45, 0.00021, 4, 2, 51.87, 31.77, 0.02331, 3, 23.07, 26.47, 0.0831, 4, -16.15, 22.98, 0.00019, 13, -5.37, 0.05, 0.8934, 3, 2, 42.43, 26.14, 0.14272, 3, 12.44, 23.64, 0.28885, 13, -15.99, -2.78, 0.56843, 3, 2, 28.77, 18.56, 0.58228, 3, -2.77, 20.08, 0.20912, 13, -31.21, -6.34, 0.2086, 3, 2, 16.93, 17.86, 0.87515, 3, -14.35, 22.66, 0.04672, 13, -42.78, -3.76, 0.07813, 3, 2, 8.78, 17.73, 0.96729, 3, -22.23, 24.76, 0.00749, 13, -50.66, -1.66, 0.02522], "hull": 32, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 0, 62, 0, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 34, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 110, 112, 112, 114, 114, 116, 116, 118], "width": 69, "height": 104}}, "tou": {"tou": {"x": 45.57, "y": -0.7, "rotation": -92.34, "width": 71, "height": 85}}, "m1": {"m1": {"x": 14.99, "y": -26.48, "rotation": -92.34, "width": 9, "height": 12}}, "m2": {"m2": {"x": 24.82, "y": -30.88, "rotation": -92.34, "width": 11, "height": 30}}, "m3": {"m3": {"x": 19.42, "y": 20.88, "rotation": -92.34, "width": 14, "height": 17}}, "tf2": {"tf2": {"type": "mesh", "uvs": [1, 0.3965, 0.98597, 0.269, 0.90956, 0.114, 0.77326, 0.0165, 0.54608, 0, 0.32924, 0.039, 0.22184, 0.2415, 0.20945, 0.4365, 0.14543, 0.609, 0.13717, 0.744, 0.02978, 0.699, 0, 0.7965, 0.02565, 0.9565, 0.2425, 1, 0.44282, 1, 0.58739, 0.8815, 0.65347, 0.729, 0.65967, 0.5465, 0.69891, 0.484, 0.81663, 0.519, 0.96326, 0.5515, 1, 0.5565, 0.84554, 0.29651, 0.67, 0.22651, 0.49239, 0.25401, 0.4263, 0.44151, 0.42424, 0.66901, 0.31271, 0.80401, 0.16195, 0.85151], "triangles": [13, 27, 14, 15, 14, 26, 12, 28, 13, 13, 28, 27, 12, 11, 28, 14, 27, 26, 15, 26, 16, 28, 11, 9, 11, 10, 9, 28, 9, 27, 26, 27, 8, 27, 9, 8, 16, 26, 17, 8, 7, 26, 26, 25, 17, 26, 7, 25, 17, 25, 18, 18, 24, 23, 24, 18, 25, 7, 6, 25, 25, 6, 24, 6, 5, 24, 24, 5, 4, 23, 3, 22, 24, 4, 23, 23, 4, 3, 20, 0, 21, 20, 19, 0, 19, 22, 0, 19, 18, 22, 18, 23, 22, 22, 1, 0, 22, 2, 1, 22, 3, 2], "vertices": [1, 46, -3.72, -0.69, 1, 1, 46, -0.75, -4.57, 1, 2, 46, 5.23, -7.93, 0.88488, 47, -7.06, -5.18, 0.11512, 2, 46, 12.51, -8.02, 0.25838, 47, -0.71, -8.75, 0.74162, 2, 47, 9.75, -9.16, 0.94656, 48, -9.18, -1.09, 0.05344, 3, 47, 19.69, -7.47, 0.30099, 48, -5.1, -10.31, 0.69867, 49, -11.09, -18.85, 0.00034, 3, 47, 24.47, 0.32, 0.01623, 48, 3.63, -13.03, 0.92126, 49, -2.22, -16.63, 0.06251, 2, 48, 10.92, -11.62, 0.63074, 49, 3.27, -11.62, 0.36926, 2, 48, 18.02, -12.72, 0.15897, 49, 9.9, -8.86, 0.84103, 2, 48, 23.07, -11.73, 0.01444, 49, 13.69, -5.38, 0.98556, 1, 49, 16.12, -10.01, 1, 1, 49, 19.66, -8.25, 1, 1, 49, 22.96, -3.01, 1, 1, 49, 16.82, 5.03, 1, 2, 48, 28.72, 4.41, 0.00317, 49, 10.11, 11.34, 0.99683, 4, 46, 3.57, 24.74, 6e-05, 47, 7.15, 24.29, 0.00013, 48, 22.62, 9.63, 0.13749, 49, 2.18, 12.61, 0.86232, 4, 46, 3.82, 18.21, 0.01004, 47, 4.24, 18.43, 0.01533, 48, 16.22, 11.02, 0.44635, 49, -4, 10.47, 0.52828, 4, 46, 7.02, 12.05, 0.11949, 47, 4.09, 11.49, 0.14709, 48, 9.46, 9.46, 0.65101, 49, -8.96, 5.61, 0.08241, 4, 46, 6.64, 9.09, 0.37985, 47, 2.34, 9.08, 0.2473, 48, 6.69, 10.57, 0.36468, 49, -11.9, 5.12, 0.00816, 3, 46, 1.28, 7.55, 0.90517, 47, -3.1, 10.3, 0.04082, 48, 6.54, 16.14, 0.05401, 2, 46, -5.19, 5.26, 0.9994, 48, 5.94, 22.97, 0.0006, 1, 46, -6.75, 4.59, 1, 1, 46, 4.33, -0.45, 1, 1, 47, 3.87, -0.68, 1, 2, 48, 0.79, -0.91, 0.99998, 49, -10.97, -7.76, 2e-05, 2, 48, 8.46, -1.95, 0.97088, 49, -3.87, -4.65, 0.02912, 2, 46, 14.1, 21.47, 2e-05, 49, 2.12, 1.59, 0.99998, 1, 49, 9.37, 1.81, 1, 2, 48, 26.71, -9.54, 0.00015, 49, 15.66, -1.62, 0.99985], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 0, 42, 0, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 22], "width": 46, "height": 38}}, "lhh": {"lhh": {"type": "mesh", "uvs": [0, 0.9985, 1, 1, 1, 0, 0, 0, 0.4622, 0.3801, 0.61902, 0.3576, 0.74329, 0.3966, 0.71814, 0.5496, 0.63085, 0.6576, 0.46663, 0.70109, 0.29354, 0.65459, 0.17666, 0.5376, 0.18702, 0.3936, 0.29206, 0.3621, 0.47403, 0.5406, 0.60866, 0.5016, 0.31721, 0.5106], "triangles": [14, 4, 15, 16, 4, 14, 11, 12, 16, 11, 3, 12, 12, 13, 16, 16, 13, 4, 15, 5, 6, 15, 4, 5, 6, 5, 2, 12, 3, 13, 5, 4, 3, 4, 13, 3, 5, 3, 2, 1, 0, 9, 0, 10, 9, 9, 8, 1, 8, 7, 1, 0, 11, 10, 9, 14, 8, 9, 10, 14, 7, 6, 1, 6, 2, 1, 0, 3, 11, 14, 15, 8, 8, 15, 7, 10, 16, 14, 10, 11, 16, 7, 15, 6], "vertices": [1, 53, 35.53, 21.39, 1, 1, 53, -37.44, 23.51, 1, 1, 53, -39.43, -48.46, 1, 1, 53, 33.54, -50.48, 1, 2, 53, 0.57, -22.19, 0.72, 54, 0.69, -11.26, 0.28, 1, 53, -10.92, -23.49, 1, 1, 53, -19.91, -20.44, 1, 1, 53, -17.77, -9.48, 1, 1, 53, -11.19, -1.88, 1, 2, 53, 0.88, 0.92, 0.76, 54, 1.01, 11.85, 0.24, 1, 53, 13.42, -2.77, 1, 1, 53, 21.72, -11.43, 1, 1, 53, 20.68, -21.77, 1, 1, 53, 12.95, -23.83, 1, 1, 54, 0.15, 0.32, 1, 2, 53, -9.88, -13.15, 0.744, 54, -9.75, -2.22, 0.256, 2, 53, 11.41, -13.09, 0.76, 54, 11.53, -2.16, 0.24], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 8], "width": 73, "height": 72}}, "gt": {"gt": {"type": "mesh", "uvs": [0.1681, 0.90857, 0.27859, 0.98849, 0.51085, 1, 0.77016, 1, 0.95957, 0.92611, 1, 0.78773, 0.93026, 0.64739, 0.74987, 0.59086, 0.67771, 0.45247, 0.66193, 0.31993, 0.73408, 0.164, 0.63487, 0.06849, 0.49668, 0, 0.21475, 0, 0.03507, 0.08621, 0, 0.28438, 0, 0.51736, 0.09393, 0.64054, 0.19617, 0.75034, 0.16209, 0.83336, 0.62989, 0.80658, 0.49977, 0.64858, 0.39444, 0.50129, 0.33248, 0.29509, 0.33558, 0.1237], "triangles": [24, 13, 12, 24, 14, 13, 24, 15, 14, 23, 15, 24, 9, 11, 10, 23, 24, 9, 11, 24, 12, 9, 24, 11, 22, 23, 9, 16, 15, 23, 22, 9, 8, 16, 23, 22, 17, 16, 22, 21, 22, 8, 21, 8, 7, 18, 17, 22, 18, 22, 21, 20, 21, 7, 1, 0, 19, 6, 5, 20, 20, 1, 18, 20, 18, 21, 1, 19, 18, 2, 1, 20, 20, 7, 6, 5, 3, 20, 4, 3, 5, 2, 20, 3], "vertices": [2, 50, -8.17, 21.29, 0.68679, 51, -14.78, 19.99, 0.31321, 2, 50, -13.11, 15.86, 0.79392, 51, -21.27, 16.55, 0.20608, 2, 50, -14.27, 4.05, 0.97035, 51, -26.37, 5.83, 0.02965, 1, 50, -14.81, -9.17, 1, 1, 50, -10.85, -19, 1, 2, 50, -2.78, -21.39, 0.99986, 51, -24.18, -21.99, 0.00014, 3, 50, 5.64, -18.17, 0.97624, 51, -15.17, -21.82, 0.02376, 52, -9.9, -32.3, 0, 3, 50, 9.35, -9.12, 0.72854, 51, -8.61, -14.56, 0.2591, 52, -8.31, -22.64, 0.01236, 3, 50, 17.66, -5.77, 0.18779, 51, 0.34, -14.23, 0.61604, 52, -0.95, -17.53, 0.19616, 3, 50, 25.5, -5.29, 0.01752, 51, 7.88, -16.44, 0.33296, 52, 6.59, -15.31, 0.64952, 2, 51, 15.02, -23.32, 0.0753, 52, 16.3, -17.24, 0.9247, 2, 51, 22.14, -20.76, 0.02371, 52, 20.92, -11.24, 0.97629, 2, 51, 28.54, -15.76, 0.00018, 52, 23.6, -3.57, 0.99982, 1, 52, 20.97, 10.57, 1, 2, 51, 32.72, 7.96, 0.00751, 52, 14.3, 18.64, 0.99249, 2, 51, 22.56, 14.03, 0.19899, 52, 2.47, 18.26, 0.80101, 3, 50, 15.24, 28.92, 0.00518, 51, 9.83, 19.22, 0.72748, 52, -11.04, 15.75, 0.26734, 3, 50, 7.78, 24.43, 0.08317, 51, 1.29, 17.52, 0.8514, 52, -17.31, 9.71, 0.06543, 3, 50, 1.1, 19.48, 0.40783, 51, -6.67, 15.14, 0.59085, 52, -22.72, 3.4, 0.00132, 2, 50, -3.72, 21.42, 0.61354, 51, -10.55, 18.6, 0.38646, 1, 50, -3.12, -2.48, 1, 3, 50, 6.47, 3.77, 0.46968, 51, -6.95, -1.47, 0.53015, 52, -13.99, -10.72, 0.00017, 1, 51, 3.12, 0.23, 1, 1, 52, 4.95, 1.48, 1, 1, 52, 14.92, 3.17, 1], "hull": 20, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 0, 38], "width": 51, "height": 59}}, "tf4": {"tf4": {"x": 60.34, "y": 1.19, "rotation": -92.34, "width": 80, "height": 62}}, "erduo": {"erduo": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.89, 18.73, 18.67, 37.71, 39.65, 36.85, 38.87, 17.87], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 19, "height": 21}}, "tg": {"tg": {"x": 83.2, "y": 1.76, "rotation": -92.34, "width": 69, "height": 36}}, "xiongs1": {"xiongs1": {"x": 15.34, "y": -0.79, "rotation": 85.91, "width": 13, "height": 32}}, "biyan": {"biyan": {"x": 4.93, "y": 7, "rotation": -92.34, "width": 57, "height": 27}}}}], "events": {"atk": {}}, "animations": {"boss_attack1": {"slots": {"lhh": {"color": [{"time": 0.7, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "color": "ffffff00"}]}}, "bones": {"bone": {"rotate": [{"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -6.39, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "y": -15.22, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "y": 20.13, "curve": "stepped"}, {"time": 0.6333, "y": 20.13, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": 10.8, "y": 65.48, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "bone53": {"rotate": [{"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 79.77}], "translate": [{"curve": 0.352, "c2": 0.44, "c3": 0.685, "c4": 0}, {"time": 0.0333, "x": -2.72, "y": 0.81, "curve": 0.73, "c2": -0.19, "c3": 0.733}, {"time": 0.6333, "x": -14.99, "y": 195.96, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": 504.33, "y": 5.08}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 4.465, "y": 4.465, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": 1.727, "y": 3.737}]}, "bone54": {"scale": [{"curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 0.1, "x": 1.784, "y": 1.784, "curve": 0.317, "c2": 0.27, "c3": 0.652, "c4": 0.61}, {"time": 0.1667, "x": 0.682, "y": 0.682, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2333, "x": 1.784, "y": 1.784, "curve": 0.317, "c2": 0.27, "c3": 0.652, "c4": 0.61}, {"time": 0.3, "x": 0.682, "y": 0.682, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.3667, "x": 1.784, "y": 1.784, "curve": 0.317, "c2": 0.27, "c3": 0.652, "c4": 0.61}, {"time": 0.4333, "x": 0.682, "y": 0.682, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5, "x": 1.784, "y": 1.784, "curve": 0.317, "c2": 0.27, "c3": 0.652, "c4": 0.61}, {"time": 0.5667, "x": 0.682, "y": 0.682, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.6667}]}, "bone14": {"rotate": [{"curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.5333, "angle": -71.91, "curve": 0.339, "c2": 0.35, "c3": 0.674, "c4": 0.69}, {"time": 0.6333, "angle": -47.43, "curve": 0.337, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.6667, "angle": -119.85, "curve": "stepped"}, {"time": 0.7333, "angle": -119.85, "curve": 0.334, "c2": 0.34, "c3": 0.667, "c4": 0.67}, {"time": 0.7667, "angle": -113.45, "curve": 0.337, "c2": 0.35, "c3": 0.671, "c4": 0.68}, {"time": 0.9333}], "scale": [{"time": 0.6333, "curve": "stepped"}, {"time": 0.6667, "x": -1, "curve": "stepped"}, {"time": 0.7333, "x": -1, "curve": "stepped"}, {"time": 0.7667}]}, "bone15": {"rotate": [{"angle": -0.07, "curve": 0.355, "c2": 0.43, "c3": 0.691, "c4": 0.78}, {"time": 0.5333, "angle": 40.77, "curve": "stepped"}, {"time": 0.6, "angle": 40.77, "curve": 0.341, "c2": 0.39, "c3": 0.675, "c4": 0.73}, {"time": 0.6667, "angle": 89.16, "curve": "stepped"}, {"time": 0.7, "angle": 89.16, "curve": 0.352, "c2": 0.65, "c3": 0.686}, {"time": 0.9333, "angle": -0.07}]}, "bone16": {"rotate": [{"angle": -0.32}]}, "bone18": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 6.11, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -1.14, "curve": "stepped"}, {"time": 0.6667, "angle": -1.14, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "bone19": {"rotate": [{"angle": -0.21, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 6.11, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -1.14, "curve": "stepped"}, {"time": 0.7333, "angle": -1.14, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.9333, "angle": -0.21}]}, "bone20": {"rotate": [{"angle": -0.57, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 6.11, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -1.14, "curve": "stepped"}, {"time": 0.8, "angle": -1.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.9333, "angle": -0.57}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 1.42, "curve": 0.25, "c3": 0.75}, {"time": 0.5535, "angle": 0.74, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -1.65, "curve": "stepped"}, {"time": 0.7333, "angle": -1.65, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 1.42, "curve": 0.25, "c3": 0.75}, {"time": 0.5535, "angle": 0.74, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -1.65, "curve": "stepped"}, {"time": 0.7333, "angle": -1.65, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "bone4": {"rotate": [{"angle": -0.47, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 1.42, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 0.74, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -1.65, "curve": "stepped"}, {"time": 0.8, "angle": -1.65, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.9333, "angle": -0.47}]}, "bone7": {"translate": [{"y": 5.16, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "y": -5.3, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "y": 7.21, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.9333, "y": 5.16}]}, "bone38": {"translate": [{"y": 12.97, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "curve": "stepped"}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "y": 20.52, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.9333, "y": 12.97}]}, "bone37": {"translate": [{"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "y": 20.52, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "bone34": {"translate": [{"y": 12.97, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "curve": "stepped"}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "y": 20.52, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.9333, "y": 12.97}]}, "bone32": {"translate": [{"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "y": 20.52, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "bone31": {"translate": [{"y": 12.97, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "curve": "stepped"}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "y": 20.52, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.9333, "y": 12.97}]}, "bone33": {"translate": [{"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "y": 20.52, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "bone35": {"translate": [{"y": 12.97, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "curve": "stepped"}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "y": 20.52, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.9333, "y": 12.97}]}, "bone36": {"translate": [{"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "y": 20.52, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "bone40": {"translate": [{"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "y": 20.52, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "bone26": {"rotate": [{"time": 0.6667, "curve": 0.352, "c2": 0.41, "c3": 0.689, "c4": 0.76}, {"time": 0.8, "angle": -0.26, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.9333}]}, "bone27": {"rotate": [{"angle": 0.88, "curve": 0.344, "c2": 0.66, "c3": 0.678}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.7, "curve": 0.352, "c2": 0.41, "c3": 0.689, "c4": 0.76}, {"time": 0.8333, "angle": -22.34, "curve": 0.358, "c2": 0.51, "c3": 0.693, "c4": 0.86}, {"time": 0.9333, "angle": 0.88}]}, "bone28": {"rotate": [{"angle": 9.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 18.06, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.9333, "angle": 9.03}]}, "bone23": {"rotate": [{"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -3.88, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "bone24": {"rotate": [{"angle": -0.72, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -3.88, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.9333, "angle": -0.72}]}, "bone25": {"rotate": [{"angle": -1.94, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -3.88, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.9333, "angle": -1.94}]}}, "events": [{"time": 0.7333, "name": "atk"}]}, "boss_idle": {"slots": {"biyan": {"attachment": [{"time": 1, "name": "biyan"}, {"time": 1.1, "name": null}]}}, "bones": {"bone": {"translate": [{"y": -12.9, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": -0.83, "curve": 0.25, "c3": 0.75}, {"time": 2, "y": -12.9}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 1.39, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone3": {"rotate": [{"angle": 0.39, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.39, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 0.39}]}, "bone4": {"rotate": [{"angle": 1, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 1.39, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 1}]}, "bone8": {"translate": [{"y": 1.32, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "y": 3.19, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2, "y": 1.32}]}, "bone9": {"translate": [{"y": 2.89, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "y": 3.19, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "y": 2.89}]}, "bone10": {"translate": [{"y": 0.31, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "y": 3.19, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "y": 0.31}]}, "bone7": {"translate": [{"y": -1.14, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "y": -1.8, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "y": -1.14}]}, "bone11": {"rotate": [{"angle": 0.27, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 1.6, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 2, "angle": 0.27}]}, "bone12": {"rotate": [{"angle": 6.22, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": 10.58, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 2, "angle": 6.22}]}, "bone13": {"translate": [{"x": 1.38, "y": 0.13, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.92, "y": 0.18, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 1.38, "y": 0.13}]}, "bone14": {"rotate": [{"angle": -2.27, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -4.99, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "angle": -2.27}]}, "bone15": {"rotate": [{"angle": -4.51, "curve": 0.295, "c2": 0.21, "c3": 0.662, "c4": 0.65}, {"time": 0.4667, "angle": -1.62, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -4.99, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": -4.51}]}, "bone16": {"rotate": [{"angle": -4.34, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "angle": -4.99, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 0.4667, "angle": -3.98, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 1.2, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2, "angle": -4.34}]}, "bone17": {"translate": [{"x": 1.18, "y": -1.02, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.65, "y": -1.42, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 1.18, "y": -1.02}]}, "bone18": {"rotate": [{"angle": 3.32, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 11.69, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 3.32}]}, "bone19": {"rotate": [{"angle": 8.37, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 11.69, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 8.37}]}, "bone20": {"rotate": [{"angle": 11.69, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 11.69}]}, "bone23": {"rotate": [{"angle": -0.26, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -3.32, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -0.26}]}, "bone24": {"rotate": [{"angle": -0.29, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -3.06, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": -0.29}], "scale": [{"x": 0.984, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "x": 0.933, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2, "x": 0.984}]}, "bone25": {"rotate": [{"angle": -2.66, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": -3.06, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "angle": -2.66}], "scale": [{"x": 0.957, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "x": 0.932, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "x": 0.957}]}, "bone26": {"rotate": [{"angle": 0.42, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 4.35, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 0.42}]}, "bone27": {"rotate": [{"angle": 1.23, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.35, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 1.23}]}, "bone28": {"rotate": [{"angle": 3.11, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 4.35, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 3.11}]}, "bone29": {"translate": [{"x": -1.5, "y": 0.5, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -5.29, "y": 1.77, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -1.5, "y": 0.5}]}, "bone30": {"translate": [{"y": 0.58, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "y": 3.47, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 2, "y": 0.58}]}, "bone31": {"translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1, "y": -8.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2}]}, "bone32": {"translate": [{"x": 2.98, "y": -2.42, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "y": -8.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "x": 4.16, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 2.98, "y": -2.42}]}, "bone33": {"translate": [{"x": -3.29, "y": -2.42, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "y": -8.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "x": -4.59, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -3.29, "y": -2.42}]}, "bone34": {"translate": [{"x": 1.18, "y": -6.11, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "y": -8.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "x": 4.16, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 1.18, "y": -6.11}]}, "bone35": {"translate": [{"x": -1.3, "y": -6.11, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "y": -8.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "x": -4.59, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -1.3, "y": -6.11}]}, "bone36": {"translate": [{"x": -3.29, "y": -2.42, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "y": -8.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "x": -4.59, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -3.29, "y": -2.42}]}, "bone37": {"translate": [{"x": 2.98, "y": -2.42, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "y": -8.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "x": 4.16, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 2.98, "y": -2.42}]}, "bone38": {"translate": [{"x": 4.16, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 12.49, "y": -8.54, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 4.16}]}, "bone39": {"translate": [{"x": -4.59, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": -8.54, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -4.59}]}, "bone40": {"translate": [{"x": -3.29, "y": -2.42, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": -4.59, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": -8.54, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -3.29, "y": -2.42}]}, "bone42": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -4.36, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone43": {"rotate": [{"angle": -1.24, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -4.36, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -1.24}]}, "bone44": {"rotate": [{"angle": -3.12, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -4.36, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -3.12}]}, "bone45": {"rotate": [{"angle": -4.36, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -4.36}]}, "bone46": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone47": {"rotate": [{"angle": 1.58, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 5.57, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 1.58}]}, "bone48": {"rotate": [{"angle": 3.99, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 5.57, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 3.99}]}, "bone49": {"rotate": [{"angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 5.57}]}, "bone51": {"rotate": [{"angle": 3.47, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 12.22, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 3.47}]}, "bone52": {"rotate": [{"angle": 8.76, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 12.22, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 8.76}]}, "bone53": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 1.147, "y": 1.147, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone54": {"translate": [{"x": -1.08, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "x": -3.3, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 2, "x": -1.08}]}}}, "die": {"slots": {"lhh": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffff00"}]}, "biyan": {"attachment": [{"time": 0.1667, "name": "biyan"}]}, "piaodai": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffff00"}]}, "lunzhuan": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "color": "ffffff00"}]}}, "bones": {"bone": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 87.02}], "translate": [{"curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.2, "y": 55.06, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4667, "y": -140.5, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "y": -118.93, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "y": -140.5}]}, "bone15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 121.02}]}, "bone2": {"rotate": [{"curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 0.1, "angle": -8.37, "curve": 0.327, "c2": 0.31, "c3": 0.668, "c4": 0.67}, {"time": 0.2333, "angle": 6.3, "curve": 0.381, "c2": 0.54, "c3": 0.743}, {"time": 0.4667}]}, "bone3": {"rotate": [{"curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 0.1, "angle": -8.37, "curve": 0.327, "c2": 0.31, "c3": 0.668, "c4": 0.67}, {"time": 0.2333, "angle": 6.3, "curve": 0.381, "c2": 0.54, "c3": 0.743}, {"time": 0.4667}]}, "bone18": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -6.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -38.11}]}, "bone26": {"rotate": [{"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -32.2}]}, "bone27": {"rotate": [{"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 48.18}]}}}, "hurt": {"bones": {"bone": {"translate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -22.4, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone2": {"rotate": [{"angle": -3.1, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": -10.93, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 8.06, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "angle": 1.49, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3333, "angle": -3.1}]}, "bone3": {"rotate": [{"angle": 1.5, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -10.93, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 8.06, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2667, "angle": 6.57, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 0.3333, "angle": 1.5}]}, "bone4": {"rotate": [{"angle": 6.57, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -10.93, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2667, "angle": 2.67, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3, "angle": 8.06, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "angle": 6.57}]}, "bone26": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -4.78, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 10.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone27": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -4.78, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 10.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone28": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -4.78, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 10.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone23": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -4.69, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 6.36, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone24": {"rotate": [{"angle": 1.17, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -4.69, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 6.36, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.3333, "angle": 1.17}]}, "bone25": {"rotate": [{"angle": 3.18, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -4.69, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 6.36, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": 3.18}]}, "bone7": {"translate": [{"x": -4.81, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -4.81}]}, "bone18": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -1.49, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 16.01, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone19": {"rotate": [{"angle": 2.95, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -1.49, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 16.01, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.3333, "angle": 2.95}]}, "bone20": {"rotate": [{"angle": 8.01, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -1.49, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 16.01, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": 8.01}]}, "bone36": {"translate": [{"x": -7.04, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1, "curve": "stepped"}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -8.63, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "x": -7.04}]}, "bone39": {"translate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -8.63, "y": -10.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone40": {"translate": [{"x": -7.04, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1, "curve": "stepped"}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -8.63, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "x": -7.04}]}, "bone35": {"translate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -8.63, "y": -10.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone33": {"translate": [{"x": -7.04, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1, "curve": "stepped"}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -8.63, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "x": -7.04}]}, "bone31": {"translate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -8.63, "y": -10.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone32": {"translate": [{"x": -7.04, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1, "curve": "stepped"}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -8.63, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "x": -7.04}]}, "bone34": {"translate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -8.63, "y": -10.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone37": {"translate": [{"x": -7.04, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1, "curve": "stepped"}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -8.63, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "x": -7.04}]}, "bone38": {"translate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -8.63, "y": -10.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone30": {"translate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -9.86, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone53": {"translate": [{"x": -12.22, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -19.34, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "x": -12.22}]}}}, "run1": {"slots": {"biyan": {"attachment": [{"time": 0.3333, "name": "biyan"}, {"time": 0.3667, "name": null}]}}, "bones": {"bone": {"rotate": [{"angle": -12.74}], "translate": [{"y": -3.81, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": 6.21, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": -3.81}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 1.39, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone3": {"rotate": [{"angle": 0.39, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 1.39, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "angle": 0.39}]}, "bone4": {"rotate": [{"angle": 1, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 1.39, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "angle": 1}]}, "bone8": {"translate": [{"y": 1.32, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 3.19, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.6667, "y": 1.32}]}, "bone9": {"translate": [{"y": 2.89, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "y": 3.19, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.6667, "y": 2.89}]}, "bone10": {"translate": [{"y": 0.31, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "y": 3.19, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.6667, "y": 0.31}]}, "bone7": {"translate": [{"y": -1.14, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "y": -1.8, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "y": -1.14}]}, "bone11": {"rotate": [{"angle": 0.27, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 1.6, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 0.6667, "angle": 0.27}]}, "bone12": {"rotate": [{"angle": 6.22, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 10.58, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.6667, "angle": 6.22}]}, "bone13": {"translate": [{"x": 1.38, "y": 0.13, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 1.92, "y": 0.18, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "x": 1.38, "y": 0.13}]}, "bone14": {"rotate": [{"angle": -2.27, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -4.99, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 0.6667, "angle": -2.27}]}, "bone15": {"rotate": [{"angle": -4.51, "curve": 0.295, "c2": 0.21, "c3": 0.662, "c4": 0.65}, {"time": 0.1667, "angle": -1.62, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -4.99, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.6667, "angle": -4.51}]}, "bone16": {"rotate": [{"angle": -4.34, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "angle": -4.99, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 0.1667, "angle": -3.98, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 0.4, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -4.34}]}, "bone17": {"translate": [{"x": 1.18, "y": -1.02, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 1.65, "y": -1.42, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "x": 1.18, "y": -1.02}]}, "bone18": {"rotate": [{"angle": 3.32, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 11.69, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "angle": 3.32}]}, "bone19": {"rotate": [{"angle": 8.37, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 11.69, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "angle": 8.37}]}, "bone20": {"rotate": [{"angle": 11.69, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 11.69}]}, "bone23": {"rotate": [{"angle": -0.26, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -3.32, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -0.26}]}, "bone24": {"rotate": [{"angle": -0.29, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -3.06, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.6667, "angle": -0.29}], "scale": [{"x": 0.984, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 0.933, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "x": 0.984}]}, "bone25": {"rotate": [{"angle": -2.66, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -3.06, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": -2.66}], "scale": [{"x": 0.957, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 0.932, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "x": 0.957}]}, "bone26": {"rotate": [{"angle": 0.42, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 4.35, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.6667, "angle": 0.42}]}, "bone27": {"rotate": [{"angle": 1.23, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 4.35, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "angle": 1.23}]}, "bone28": {"rotate": [{"angle": 3.11, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 4.35, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "angle": 3.11}]}, "bone29": {"translate": [{"x": -1.5, "y": 0.5, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -5.29, "y": 1.77, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "x": -1.5, "y": 0.5}]}, "bone30": {"translate": [{"y": 0.58, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "y": 3.47, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 0.6667, "y": 0.58}]}, "bone31": {"translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "y": -8.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}]}, "bone32": {"translate": [{"x": 2.98, "y": -2.42, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "y": -8.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5667, "x": 4.16, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "x": 2.98, "y": -2.42}]}, "bone33": {"translate": [{"x": -3.29, "y": -2.42, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "y": -8.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5667, "x": -4.59, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "x": -3.29, "y": -2.42}]}, "bone34": {"translate": [{"x": 1.18, "y": -6.11, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "y": -8.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4333, "x": 4.16, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "x": 1.18, "y": -6.11}]}, "bone35": {"translate": [{"x": -1.3, "y": -6.11, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "y": -8.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4333, "x": -4.59, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "x": -1.3, "y": -6.11}]}, "bone36": {"translate": [{"x": -3.29, "y": -2.42, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "y": -8.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5667, "x": -4.59, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "x": -3.29, "y": -2.42}]}, "bone37": {"translate": [{"x": 2.98, "y": -2.42, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "y": -8.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5667, "x": 4.16, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "x": 2.98, "y": -2.42}]}, "bone38": {"translate": [{"x": 4.16, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 12.49, "y": -8.54, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 4.16}]}, "bone39": {"translate": [{"x": -4.59, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": -8.54, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -4.59}]}, "bone40": {"translate": [{"x": -3.29, "y": -2.42, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "x": -4.59, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "y": -8.54, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "x": -3.29, "y": -2.42}]}, "bone42": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -4.36, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone43": {"rotate": [{"angle": -1.24, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -4.36, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "angle": -1.24}]}, "bone44": {"rotate": [{"angle": -3.12, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -4.36, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "angle": -3.12}]}, "bone45": {"rotate": [{"angle": -4.36, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -4.36}]}, "bone46": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone47": {"rotate": [{"angle": 1.58, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 5.57, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "angle": 1.58}]}, "bone48": {"rotate": [{"angle": 3.99, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 5.57, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "angle": 3.99}]}, "bone49": {"rotate": [{"angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 5.57}]}, "bone51": {"rotate": [{"angle": 3.47, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 12.22, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "angle": 3.47}]}, "bone52": {"rotate": [{"angle": 8.76, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 12.22, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "angle": 8.76}]}, "bone53": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 1.147, "y": 1.147, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone54": {"translate": [{"x": -1.08, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -3.3, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 0.6667, "x": -1.08}]}}}, "run2": {"bones": {"bone": {"rotate": [{"angle": -27.38}], "translate": [{"y": -3.81, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 6.21, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": -3.81}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 1.39, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone3": {"rotate": [{"angle": 0.39, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 1.39, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": 0.39}]}, "bone4": {"rotate": [{"angle": 1, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 1.39, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": 1}]}, "bone8": {"translate": [{"y": 1.32, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "y": 3.19, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.3667, "y": 1.32}]}, "bone9": {"translate": [{"y": 2.89, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": 3.19, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.3667, "y": 2.89}]}, "bone10": {"translate": [{"y": 0.31, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "y": 3.19, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.3667, "y": 0.31}]}, "bone7": {"translate": [{"y": -1.14, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "y": -1.8, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3667, "y": -1.14}]}, "bone11": {"rotate": [{"angle": 0.27, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 1.6, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 0.3667, "angle": 0.27}]}, "bone12": {"rotate": [{"angle": 6.22, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 10.58, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.3667, "angle": 6.22}]}, "bone13": {"translate": [{"x": 1.38, "y": 0.13, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 1.92, "y": 0.18, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "x": 1.38, "y": 0.13}]}, "bone14": {"rotate": [{"angle": -2.27, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -4.99, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 0.3667, "angle": -2.27}]}, "bone15": {"rotate": [{"angle": -4.51, "curve": 0.295, "c2": 0.21, "c3": 0.662, "c4": 0.65}, {"time": 0.1, "angle": -1.62, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -4.99, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.3667, "angle": -4.51}]}, "bone16": {"rotate": [{"angle": -4.34, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": -4.99, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 0.1, "angle": -3.98, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 0.2, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3667, "angle": -4.34}]}, "bone17": {"translate": [{"x": 1.18, "y": -1.02, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 1.65, "y": -1.42, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "x": 1.18, "y": -1.02}]}, "bone18": {"rotate": [{"angle": 3.32, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 11.69, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": 3.32}]}, "bone19": {"rotate": [{"angle": 8.37, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 11.69, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": 8.37}]}, "bone20": {"rotate": [{"angle": 11.69, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 11.69}]}, "bone23": {"rotate": [{"angle": -0.26, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -3.32, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -0.26}]}, "bone24": {"rotate": [{"angle": -0.29, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -3.06, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.3667, "angle": -0.29}], "scale": [{"x": 0.984, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 0.933, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.3667, "x": 0.984}]}, "bone25": {"rotate": [{"angle": -2.66, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -3.06, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3667, "angle": -2.66}], "scale": [{"x": 0.957, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 0.932, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3667, "x": 0.957}]}, "bone26": {"rotate": [{"angle": 0.42, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 4.35, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.3667, "angle": 0.42}]}, "bone27": {"rotate": [{"angle": 1.23, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 4.35, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": 1.23}]}, "bone28": {"rotate": [{"angle": 3.11, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 4.35, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": 3.11}]}, "bone29": {"translate": [{"x": -1.5, "y": 0.5, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -5.29, "y": 1.77, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "x": -1.5, "y": 0.5}]}, "bone30": {"translate": [{"y": 0.58, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "y": 3.47, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 0.3667, "y": 0.58}]}, "bone31": {"translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "y": -8.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667}]}, "bone32": {"translate": [{"x": 2.98, "y": -2.42, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "y": -8.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "x": 4.16, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "x": 2.98, "y": -2.42}]}, "bone33": {"translate": [{"x": -3.29, "y": -2.42, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "y": -8.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "x": -4.59, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "x": -3.29, "y": -2.42}]}, "bone34": {"translate": [{"x": 1.18, "y": -6.11, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "y": -8.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "x": 4.16, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "x": 1.18, "y": -6.11}]}, "bone35": {"translate": [{"x": -1.3, "y": -6.11, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "y": -8.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "x": -4.59, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "x": -1.3, "y": -6.11}]}, "bone36": {"translate": [{"x": -3.29, "y": -2.42, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "y": -8.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "x": -4.59, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "x": -3.29, "y": -2.42}]}, "bone37": {"translate": [{"x": 2.98, "y": -2.42, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "y": -8.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "x": 4.16, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "x": 2.98, "y": -2.42}]}, "bone38": {"translate": [{"x": 4.16, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 12.49, "y": -8.54, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 4.16}]}, "bone39": {"translate": [{"x": -4.59, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": -8.54, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -4.59}]}, "bone40": {"translate": [{"x": -3.29, "y": -2.42, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "x": -4.59, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "y": -8.54, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "x": -3.29, "y": -2.42}]}, "bone42": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -4.36, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone43": {"rotate": [{"angle": -1.24, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -4.36, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": -1.24}]}, "bone44": {"rotate": [{"angle": -3.12, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -4.36, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -3.12}]}, "bone45": {"rotate": [{"angle": -4.36, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -4.36}]}, "bone46": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone47": {"rotate": [{"angle": 1.58, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 5.57, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": 1.58}]}, "bone48": {"rotate": [{"angle": 3.99, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 5.57, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": 3.99}]}, "bone49": {"rotate": [{"angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 5.57}]}, "bone51": {"rotate": [{"angle": 3.47, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 12.22, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": 3.47}]}, "bone52": {"rotate": [{"angle": 8.76, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 12.22, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": 8.76}]}, "bone53": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 1.147, "y": 1.147, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone54": {"translate": [{"x": -1.08, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -3.3, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 0.3667, "x": -1.08}]}}}, "show_time": {"bones": {"bone": {"rotate": [{"angle": -27.38}], "translate": [{"y": -3.81, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 6.21, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": -3.81}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 1.39, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone3": {"rotate": [{"angle": 0.39, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 1.39, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": 0.39}]}, "bone4": {"rotate": [{"angle": 1, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 1.39, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": 1}]}, "bone8": {"translate": [{"y": 1.32, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "y": 3.19, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.3667, "y": 1.32}]}, "bone9": {"translate": [{"y": 2.89, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": 3.19, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.3667, "y": 2.89}]}, "bone10": {"translate": [{"y": 0.31, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "y": 3.19, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.3667, "y": 0.31}]}, "bone7": {"translate": [{"y": -1.14, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "y": -1.8, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3667, "y": -1.14}]}, "bone11": {"rotate": [{"angle": 0.27, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 1.6, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 0.3667, "angle": 0.27}]}, "bone12": {"rotate": [{"angle": 6.22, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 10.58, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.3667, "angle": 6.22}]}, "bone13": {"translate": [{"x": 1.38, "y": 0.13, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 1.92, "y": 0.18, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "x": 1.38, "y": 0.13}]}, "bone14": {"rotate": [{"angle": -2.27, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -4.99, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 0.3667, "angle": -2.27}]}, "bone15": {"rotate": [{"angle": -4.51, "curve": 0.295, "c2": 0.21, "c3": 0.662, "c4": 0.65}, {"time": 0.1, "angle": -1.62, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -4.99, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.3667, "angle": -4.51}]}, "bone16": {"rotate": [{"angle": -4.34, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": -4.99, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 0.1, "angle": -3.98, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 0.2, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3667, "angle": -4.34}]}, "bone17": {"translate": [{"x": 1.18, "y": -1.02, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 1.65, "y": -1.42, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "x": 1.18, "y": -1.02}]}, "bone18": {"rotate": [{"angle": 3.32, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 11.69, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": 3.32}]}, "bone19": {"rotate": [{"angle": 8.37, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 11.69, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": 8.37}]}, "bone20": {"rotate": [{"angle": 11.69, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 11.69}]}, "bone23": {"rotate": [{"angle": -0.26, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -3.32, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -0.26}]}, "bone24": {"rotate": [{"angle": -0.29, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -3.06, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.3667, "angle": -0.29}], "scale": [{"x": 0.984, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 0.933, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.3667, "x": 0.984}]}, "bone25": {"rotate": [{"angle": -2.66, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -3.06, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3667, "angle": -2.66}], "scale": [{"x": 0.957, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 0.932, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3667, "x": 0.957}]}, "bone26": {"rotate": [{"angle": 0.42, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 4.35, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.3667, "angle": 0.42}]}, "bone27": {"rotate": [{"angle": 1.23, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 4.35, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": 1.23}]}, "bone28": {"rotate": [{"angle": 3.11, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 4.35, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": 3.11}]}, "bone29": {"translate": [{"x": -1.5, "y": 0.5, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -5.29, "y": 1.77, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "x": -1.5, "y": 0.5}]}, "bone30": {"translate": [{"y": 0.58, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "y": 3.47, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 0.3667, "y": 0.58}]}, "bone31": {"translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "y": -8.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667}]}, "bone32": {"translate": [{"x": 2.98, "y": -2.42, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "y": -8.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "x": 4.16, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "x": 2.98, "y": -2.42}]}, "bone33": {"translate": [{"x": -3.29, "y": -2.42, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "y": -8.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "x": -4.59, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "x": -3.29, "y": -2.42}]}, "bone34": {"translate": [{"x": 1.18, "y": -6.11, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "y": -8.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "x": 4.16, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "x": 1.18, "y": -6.11}]}, "bone35": {"translate": [{"x": -1.3, "y": -6.11, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "y": -8.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "x": -4.59, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "x": -1.3, "y": -6.11}]}, "bone36": {"translate": [{"x": -3.29, "y": -2.42, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "y": -8.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "x": -4.59, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "x": -3.29, "y": -2.42}]}, "bone37": {"translate": [{"x": 2.98, "y": -2.42, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "y": -8.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "x": 4.16, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "x": 2.98, "y": -2.42}]}, "bone38": {"translate": [{"x": 4.16, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 12.49, "y": -8.54, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 4.16}]}, "bone39": {"translate": [{"x": -4.59, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": -8.54, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -4.59}]}, "bone40": {"translate": [{"x": -3.29, "y": -2.42, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "x": -4.59, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "y": -8.54, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "x": -3.29, "y": -2.42}]}, "bone42": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -4.36, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone43": {"rotate": [{"angle": -1.24, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -4.36, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": -1.24}]}, "bone44": {"rotate": [{"angle": -3.12, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -4.36, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -3.12}]}, "bone45": {"rotate": [{"angle": -4.36, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -4.36}]}, "bone46": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone47": {"rotate": [{"angle": 1.58, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 5.57, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": 1.58}]}, "bone48": {"rotate": [{"angle": 3.99, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 5.57, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": 3.99}]}, "bone49": {"rotate": [{"angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 5.57}]}, "bone51": {"rotate": [{"angle": 3.47, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 12.22, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": 3.47}]}, "bone52": {"rotate": [{"angle": 8.76, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 12.22, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": 8.76}]}, "bone53": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 1.147, "y": 1.147, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone54": {"translate": [{"x": -1.08, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -3.3, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 0.3667, "x": -1.08}]}}}}}