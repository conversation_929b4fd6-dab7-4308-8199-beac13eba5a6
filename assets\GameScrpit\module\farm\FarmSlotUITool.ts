import { FarmCollectorMessage, FarmSlotMessage } from "../../game/net/protocol/Farm";
import { <PERSON>de, Tween, Vec3, sp, Label } from "cc";
import { PlayerModule } from "../player/PlayerModule";
import { FmColor } from "../../game/common/FmConstant";
import FmUtils from "../../lib/utils/FmUtils";
import { Sprite } from "cc";
import { BundleEnum } from "../../game/bundleEnum/BundleEnum";
import ResMgr from "../../lib/common/ResMgr";
import { FarmModule } from "./FarmModule";

export const BgList = ["S0325", "S0328", "S0326", "S0324", "S0327"];

export class FarmSlotUITool {
  public static updateHulu(hulu: Node, identifyId: number, zs: boolean = false, hideAniBg = false) {
    if (identifyId > 0) {
      hulu.active = true;
    } else {
      hulu.active = false;
      return;
    }

    hulu.getChildByName("ani_bg").active = identifyId >= 6 && hideAniBg == false;

    hulu.getChildByName("lbl_zs").active = zs;

    hulu.children?.forEach((node) => {
      if (node.name.startsWith("img_hulu_")) {
        if (node.name == "img_hulu_" + (identifyId - 1)) {
          node.active = true;
        } else {
          node.active = false;
        }
      }
    });

    let cfgBlessLand = FarmModule.data.getConfigBlessLand(identifyId);

    ResMgr.loadImage(
      `${BundleEnum.BUNDLE_COMMON_ITEM}?autoItem/${BgList[cfgBlessLand.color - 1]}`,
      hulu.getChildByName("bg").getComponent(Sprite),
      null,
      () => {
        hulu.getChildByName("bg").active = true;
      }
    );

    hulu.getChildByName("lbl_level").active = true;
    hulu.getChildByName("lbl_level").getComponent(Label).string = `${identifyId}级`;
  }

  public static updateSlot(pInfo: FarmSlotMessage, pNode: Node, refreshCount: number) {
    pNode.active = true;
    // 数据修复
    if (!pInfo.ownCollectorMessage.playerBaseMessage) {
      pInfo.ownCollectorMessage.playerBaseMessage = {
        userId: -1,
        nickname: "",
        avatarList: [],
        sex: 0,
        level: 0,
        /** 会员等级 */
        vipLevel: 0,
        /** 是否对外隐藏贵族 */
        hiddenVip: true,
      };
    }
    if (!pInfo.otherCollectorMessage.playerBaseMessage) {
      pInfo.otherCollectorMessage.playerBaseMessage = {
        userId: -1,
        nickname: "",
        avatarList: [],
        sex: 0,
        level: 0,
        /** 会员等级 */
        vipLevel: 0,
        /** 是否对外隐藏贵族 */
        hiddenVip: true,
      };
    }

    // 是否自己拉
    let ownPull: boolean = false;
    // 是否别人拉
    let otherPull: boolean = false;
    // 拉取倒计时
    let endTime: number = -1;
    if (pInfo.gourdId == -1) {
      // 不可用状态
    } else if (pInfo.gourdId == -1) {
      // 专属
      ownPull = pInfo.ownCollectorMessage.playerBaseMessage.userId != -1;
    } else {
      // 常规
      ownPull = pInfo.ownCollectorMessage.playerBaseMessage.userId != -1;
      otherPull = pInfo.otherCollectorMessage.playerBaseMessage.userId != -1;
    }

    FarmSlotUITool.updateHulu(pNode.getChildByName("hulu"), pInfo.gourdId, pInfo.deadlineStamp > 0);

    const nodeHouzi1 = pNode.getChildByName("houzi_1");
    const nodeHouzi2 = pNode.getChildByName("houzi_2");

    // 设置猴子状态
    function setHouzi(nodeHouzi: Node, msg: FarmCollectorMessage) {
      if (msg.endTime > 0) {
        endTime = msg.endTime;
        nodeHouzi.getChildByPath("img_qipao/img_emoji_smile").active = true;
        nodeHouzi.getChildByPath("img_qipao/img_emoji_cry").active = false;
      }

      // 显示我
      nodeHouzi.getChildByName("lbl_self").active = msg?.playerBaseMessage.userId == PlayerModule.data.playerId;

      // 后面加的需要播放入场动画
      if (refreshCount > 0 && nodeHouzi.active == false) {
        FarmSlotUITool.playAdd(nodeHouzi);
      }

      // 一进来就有的，就直接开采
      if (refreshCount == 0) {
        FarmSlotUITool.playWait(nodeHouzi);
      }

      const nodeImgQipao = nodeHouzi.getChildByName("img_qipao");
      FarmSlotUITool.playChatAni(nodeImgQipao, msg);

      // if (msg.userId == PlayerModule.data.playerId) {
      //   nodeImgQipao.active = true;

      // } else {
      //   nodeImgQipao.active = false;
      // }
    }

    if (ownPull) {
      setHouzi(nodeHouzi1, pInfo.ownCollectorMessage);
    } else {
      nodeHouzi1.active = false;
    }

    if (otherPull) {
      setHouzi(nodeHouzi2, pInfo.otherCollectorMessage);
    } else {
      nodeHouzi2.active = false;
    }

    if (endTime == -1) {
      pNode.getChildByName("lbl_cd").active = false;
    } else {
      const nodeCd = pNode.getChildByName("lbl_cd");
      nodeCd.active = true;
      FmUtils.setCd(nodeCd, endTime);
      // 玩家输赢
      let win = false;
      if (
        pInfo.ownCollectorMessage?.playerBaseMessage.userId == PlayerModule.data.playerId &&
        pInfo.ownCollectorMessage.endTime > 0
      ) {
        win = true;
      }
      if (
        !win &&
        pInfo.otherCollectorMessage?.playerBaseMessage.userId == PlayerModule.data.playerId &&
        pInfo.otherCollectorMessage.endTime > 0
      ) {
        win = true;
      }
      nodeCd.getComponent(Label).color = win ? FmColor.COLOR_GREEN_DARK : FmColor.COLOR_RED_DARK;
    }
  }

  /** 播放持续动画 */
  private static playWait(houzi: Node) {
    houzi.active = true;
    houzi.getChildByName("ani_houzi").getComponent(sp.Skeleton).setAnimation(0, "animation2", true);
    houzi.getChildByName("ani_baibaodai").getComponent(sp.Skeleton).setAnimation(0, "animtion2", true);
    houzi.getChildByName("ani_xi").getComponent(sp.Skeleton).setAnimation(0, "animation2", true);
  }

  /**
   *
   * @param chatNode 对话框
   * @param showName 显示的表情
   * @returns
   */
  private static playChatAni(chatNode: Node, msg: FarmCollectorMessage) {
    if (!chatNode.activeInHierarchy) {
      return;
    }

    // 停止之前的动作
    if (chatNode["__ani"]) {
      chatNode["__ani"].stop();
    }

    chatNode.children.forEach((v) => (v.active = false));
    chatNode["__ani"] = new Tween(chatNode)
      .set({ scale: new Vec3(0, 0, 1) })
      .delay(3 + Math.random() * 2)
      .to(0.25, { scale: new Vec3(1, 1, 1) })
      .call(() => {
        chatNode.getChildByName(msg.endTime > 0 ? "img_emoji_smile" : "img_emoji_cry").active = true;
      })
      .delay(2 + Math.random() * 2)
      .call(() => {
        this.playChatAni(chatNode, msg);
      })
      .start();
  }

  /** 播放增加动画 */
  private static playAdd(houzi: Node) {
    houzi.active = true;
    const aniHouzi = houzi.getChildByName("ani_houzi").getComponent(sp.Skeleton);
    aniHouzi.setAnimation(0, "animation1", false);
    aniHouzi.addAnimation(0, "animation2", true);

    const houziAniList = aniHouzi.skeletonData.getRuntimeData(true).animations;

    // 猴子掉下来的时间
    const ani1 = houziAniList.find((e) => {
      return e.name == "animation1";
    });

    const aniBaibaodai = houzi.getChildByName("ani_baibaodai").getComponent(sp.Skeleton);
    new Tween(houzi.getChildByName("ani_baibaodai"))
      .hide()
      .delay(ani1.duration)
      .show()
      .call(() => {
        aniBaibaodai.setAnimation(0, "animtion1", false);
        aniBaibaodai.addAnimation(0, "animtion2", true);
      })
      .start();

    const aniXi = houzi.getChildByName("ani_xi").getComponent(sp.Skeleton);
    new Tween(houzi.getChildByName("ani_xi"))
      .hide()
      .delay(ani1.duration)
      .show()
      .call(() => {
        aniXi.setAnimation(0, "animation1", false);
        aniXi.addAnimation(0, "animation2", true);
      })
      .start();
  }
}
