import { _decorator, instantiate, Label } from "cc";
import { ClubModule } from "../../../module/club/ClubModule";
import { PlayerModule } from "../../../module/player/PlayerModule";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { ItemEnum } from "../../../lib/common/ItemEnum";
import { GoodsModule } from "../../../module/goods/GoodsModule";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { FractureShopViewholder } from "./adapter/FractureShopViewHolder";
import { FractureModule } from "../../../module/fracture/FractureModule";
import { routeConfig } from "db://assets/platform/src/core/managers/RouteTableManager";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UIFractureDrawPreview } from "./UIFractureDrawPreview";
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Wed Aug 21 2024 20:21:34 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/club/UIClubShop.ts
 *
 */

@ccclass("UIFractureShop")
@routeConfig({
  bundle: BundleEnum.BUNDLE_G_FRACTURE,
  url: "prefab/ui/UIFractureShop",
  nextHop: [],
  exit: "",
})
export class UIFractureShop extends BaseCtrl {
  public playShowAni: boolean = true;
  //=================================================
  public init(args: any): void {
    super.init(args);
  }
  private _index: number = 0;
  private _canUpdate: boolean = false;

  protected update(dt: number): void {
    let shopList = FractureModule.config.getFractureShopConfig();
    if (this._index < shopList.length && this._canUpdate) {
      let item = instantiate(this.getNode("shop_viewholder"));
      item.active = true;
      this.getNode("shop_list_content").addChild(item);
      item.getComponent(FractureShopViewholder).updateData(shopList[this._index]);
      this._index++;
    }
  }
  protected start(): void {
    super.start();
    let clubCoin = PlayerModule.data.getItemNum(ItemEnum.时空积分_20023);
    this.getNode("club_coin").getComponent(Label).string = `${clubCoin}`;
    MsgMgr.on(MsgEnum.ON_EXIST_ITEM_CHANGE, this.onItemChange, this);
    GoodsModule.api.buyInfo((data) => {
      this._canUpdate = true;
    });
  }
  protected onDestroy(): void {
    super.onDestroy();
    MsgMgr.off(MsgEnum.ON_EXIST_ITEM_CHANGE, this.onItemChange, this);
  }

  private onItemChange() {
    let clubCoin = PlayerModule.data.getItemNum(ItemEnum.时空积分_20023);
    this.getNode("club_coin").getComponent(Label).string = `${clubCoin}`;
  }
}
