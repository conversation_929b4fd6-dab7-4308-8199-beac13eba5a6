{"skeleton": {"hash": "CbAaherDg9vle1YVp0LNezl+VxU=", "spine": "3.8.75", "x": -61.96, "y": -27.4, "width": 127.56, "height": 138.9, "images": "./images/", "audio": "D:/spine导出/弟子spine/熊猫弟子"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 204.24, "rotation": 0.32, "x": 0.42, "y": 0.37, "scaleX": 0.3861, "scaleY": 0.3861}, {"name": "bone2", "parent": "bone", "length": 189.87, "rotation": -1.28, "x": -18.52, "y": 90.72}, {"name": "bone3", "parent": "bone2", "length": 44.6, "rotation": 90.14, "x": -4.49, "y": 1.84}, {"name": "bone4", "parent": "bone3", "length": 38, "rotation": -16.63, "x": 44.6}, {"name": "bone5", "parent": "bone2", "length": 46.66, "rotation": -84.34, "x": -56.08, "y": 0.33}, {"name": "bone6", "parent": "bone5", "length": 39.75, "rotation": 16.93, "x": 46.66}, {"name": "bone7", "parent": "bone6", "length": 26.76, "rotation": -180, "x": 42.24, "y": -2.36, "transform": "noRotationOrReflection"}, {"name": "bone8", "parent": "bone2", "length": 56.71, "rotation": -87.75, "x": 52.04, "y": 12.34}, {"name": "bone9", "parent": "bone8", "length": 41.53, "rotation": 31.18, "x": 56.71}, {"name": "bone10", "parent": "bone9", "length": 25.94, "rotation": -155.32, "x": 41.53, "transform": "noRotationOrReflection"}, {"name": "bone11", "parent": "bone4", "length": 21.26, "rotation": 17.45, "x": 36.38, "y": 0.03}, {"name": "bone12", "parent": "bone4", "x": 59.68, "y": -97.84}, {"name": "bone13", "parent": "bone4", "x": 13.36, "y": 67.25}, {"name": "bone14", "parent": "bone13", "length": 39.72, "rotation": 166.59, "x": -5.83, "y": 11.17}, {"name": "bone15", "parent": "bone14", "length": 35.98, "rotation": 134.38, "x": 47.46, "y": -2.6}, {"name": "bone16", "parent": "bone12", "length": 38.81, "rotation": -145.98, "x": -3.56, "y": -3.44}, {"name": "bone17", "parent": "bone16", "length": 54.76, "rotation": -89.16, "x": 38.81}, {"name": "bone18", "parent": "bone17", "length": 52.32, "rotation": -14.5, "x": 54.76}, {"name": "bone19", "parent": "bone11", "x": 15.81, "y": 19.54, "color": "abe323ff"}, {"name": "bone20", "parent": "bone11", "x": 58.22, "y": 9.83}, {"name": "bone21", "parent": "bone12", "length": 36.83, "rotation": -143.37, "x": -8.44, "y": 6.22, "color": "ff0000ff"}, {"name": "bone22", "parent": "bone21", "length": 30.91, "rotation": -4.29, "x": 36.83, "color": "ff0000ff"}, {"name": "bone23", "parent": "bone22", "length": 27.38, "rotation": 2.8, "x": 30.91, "color": "ff0000ff"}, {"name": "bone24", "parent": "bone13", "length": 39.91, "rotation": 171.61, "x": 8.33, "y": 14.57, "color": "ff0000ff"}, {"name": "bone25", "parent": "bone24", "length": 41.61, "rotation": 8.39, "x": 39.91, "color": "ff0000ff"}, {"name": "bone26", "parent": "bone25", "length": 35.5, "rotation": 9.49, "x": 41.61, "color": "ff0000ff"}, {"name": "target1", "parent": "root", "x": -21.55, "y": 2.92, "color": "ff3f00ff"}, {"name": "target2", "parent": "root", "x": 22.42, "y": 4.24, "color": "ff3f00ff"}], "slots": [{"name": "sd", "bone": "root", "color": "ffffff87", "attachment": "sd"}, {"name": "j2", "bone": "bone", "attachment": "j2"}, {"name": "j1", "bone": "bone", "attachment": "j1"}, {"name": "s1", "bone": "bone14", "attachment": "s1"}, {"name": "b1", "bone": "bone", "attachment": "b1"}, {"name": "tou2", "bone": "bone11", "attachment": "tou2"}, {"name": "tou1", "bone": "bone11"}, {"name": "y2", "bone": "bone12", "attachment": "y2"}, {"name": "y1", "bone": "bone", "attachment": "y1"}, {"name": "s3", "bone": "bone15", "attachment": "s3"}, {"name": "s2", "bone": "bone", "attachment": "s2"}], "ik": [{"name": "target1", "bones": ["bone5", "bone6"], "target": "target1"}, {"name": "target2", "order": 1, "bones": ["bone8", "bone9"], "target": "target2"}], "transform": [{"name": "face", "order": 2, "bones": ["bone20"], "target": "bone19", "x": 42.41, "y": -9.71, "rotateMix": -0.97, "translateMix": -0.97, "scaleMix": -0.97, "shearMix": -0.97}, {"name": "s1", "order": 3, "bones": ["bone12"], "target": "bone19", "rotation": -17.45, "x": -22.93, "y": -119.9, "rotateMix": -0.192, "translateMix": -0.192, "scaleMix": -0.192, "shearMix": -0.192}, {"name": "s2", "order": 4, "bones": ["bone13"], "target": "bone19", "rotation": -17.45, "x": -17.61, "y": 51.48, "rotateMix": -0.199, "translateMix": -0.199, "scaleMix": -0.199, "shearMix": -0.199}, {"name": "y1", "order": 5, "bones": ["bone21"], "target": "bone19", "rotation": -160.82, "x": -29.12, "y": -111.43, "rotateMix": -0.196, "translateMix": -0.196, "scaleMix": -0.196, "shearMix": -0.196}, {"name": "y2", "order": 6, "bones": ["bone24"], "target": "bone19", "rotation": 154.16, "x": -5.3, "y": 62.88, "rotateMix": 0.151, "translateMix": 0.151, "scaleMix": 0.151, "shearMix": 0.151}], "skins": [{"name": "default", "attachments": {"j1": {"j1": {"type": "mesh", "uvs": [0.45205, 0, 0.64187, 0.00635, 0.83009, 0.07154, 0.962, 0.21073, 1, 0.42392, 0.96844, 0.61244, 0.87835, 0.74811, 0.82848, 0.80625, 0.82225, 0.85657, 0.81561, 0.9102, 0.74, 0.97363, 0.59522, 1, 0.44561, 0.9842, 0.31209, 0.9472, 0.3009, 0.9096, 0.28796, 0.86616, 0.15766, 0.80097, 0.037, 0.66178, 0, 0.49616, 0.00805, 0.30939, 0.13674, 0.12792, 0.31692, 0.01868, 0.4156, 0.16358, 0.39953, 0.35491, 0.40154, 0.53964, 0.43166, 0.70678, 0.52804, 0.86512, 0.53206, 0.9179], "triangles": [8, 26, 7, 12, 27, 11, 11, 27, 10, 27, 12, 14, 12, 13, 14, 14, 26, 27, 8, 10, 27, 8, 27, 26, 14, 15, 26, 9, 10, 8, 25, 24, 5, 15, 25, 26, 15, 16, 25, 7, 26, 25, 7, 25, 6, 6, 25, 5, 25, 16, 24, 16, 17, 24, 17, 18, 24, 5, 24, 4, 4, 23, 3, 3, 23, 2, 2, 23, 22, 18, 23, 24, 24, 23, 4, 18, 19, 23, 19, 20, 23, 23, 20, 22, 22, 20, 21, 22, 1, 2, 22, 0, 1, 22, 21, 0], "vertices": [2, 8, -17.47, 11.01, 0.98572, 9, -57.76, 47.83, 0.01428, 1, 8, -16.08, 37.19, 1, 1, 8, -7.29, 62.97, 1, 2, 8, 10.65, 80.77, 0.984, 9, 2.42, 92.95, 0.016, 2, 8, 37.63, 85.41, 0.49985, 9, 27.9, 82.95, 0.50015, 2, 8, 61.28, 80.52, 0.45668, 9, 45.6, 66.53, 0.54332, 1, 9, 53.35, 46.86, 1, 2, 8, 85.26, 60.67, 0.13989, 9, 55.83, 37.12, 0.86011, 1, 10, -35.29, 14.54, 1, 1, 10, -31.64, 20.3, 1, 1, 10, -18.82, 23.2, 1, 1, 10, 0.72, 17.88, 1, 1, 10, 18.65, 7.45, 1, 1, 10, 33.45, -4.48, 1, 1, 10, 32.88, -9.43, 1, 3, 8, 91.13, -14.08, 0.04964, 9, 22.15, -29.86, 0.58664, 10, 32.21, -15.15, 0.36372, 1, 9, 5.57, -40.62, 1, 2, 8, 64.6, -48.12, 0.7366, 9, -18.16, -45.25, 0.2634, 2, 8, 43.63, -52.76, 0.96461, 9, -38.51, -38.36, 0.03539, 2, 8, 20.13, -51.12, 0.99977, 9, -57.77, -24.79, 0.00023, 1, 8, -2.33, -32.85, 1, 2, 8, -15.54, -7.68, 0.99999, 9, -65.79, 30.84, 1e-05, 2, 8, 3.02, 5.52, 0.99442, 9, -43.07, 32.53, 0.00558, 2, 8, 27.07, 2.76, 0.996, 9, -23.93, 17.71, 0.004, 2, 8, 50.35, 2.52, 0.94565, 9, -4.14, 5.45, 0.05435, 1, 9, 15.86, -2.35, 1, 2, 9, 39.83, -1.84, 0.41256, 10, 2.05, -1.43, 0.58744, 1, 10, 4.33, 4.84, 1], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 14, 16, 16, 18, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 0, 42, 0, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 16, 54, 28, 50, 10, 48, 8, 44, 4], "width": 138, "height": 126}}, "j2": {"j2": {"type": "mesh", "uvs": [0.41329, 0, 0.60684, 0.00806, 0.76616, 0.09233, 0.92811, 0.24638, 1, 0.53209, 0.97946, 0.72169, 0.88598, 0.83756, 0.89256, 0.89681, 0.85833, 0.97186, 0.69901, 0.99688, 0.52126, 0.99293, 0.36985, 0.97054, 0.30796, 0.93499, 0.30344, 0.89102, 0.29875, 0.84546, 0.16708, 0.75988, 0.05121, 0.61241, 0, 0.43335, 0.03146, 0.2319, 0.18815, 0.05678, 0.37249, 0.18842, 0.3745, 0.41923, 0.41062, 0.59585, 0.49692, 0.74236, 0.55312, 0.87683, 0.56316, 0.93503], "triangles": [8, 9, 7, 13, 11, 12, 25, 13, 24, 25, 11, 13, 7, 9, 25, 10, 11, 25, 10, 25, 9, 7, 25, 6, 25, 24, 6, 13, 14, 24, 4, 22, 3, 4, 23, 22, 5, 23, 4, 15, 16, 22, 15, 22, 23, 6, 23, 5, 14, 15, 23, 24, 23, 6, 14, 23, 24, 20, 19, 0, 20, 0, 1, 20, 1, 2, 21, 20, 2, 21, 2, 3, 18, 19, 20, 22, 21, 3, 18, 21, 17, 21, 18, 20, 16, 21, 22, 16, 17, 21], "vertices": [1, 5, -22.82, 2.01, 1, 2, 5, -19.95, 25.08, 0.98278, 6, -56.42, 43.39, 0.01722, 2, 5, -8.31, 43.3, 0.90663, 6, -39.98, 57.44, 0.09337, 2, 5, 11.71, 61.16, 0.72453, 6, -15.63, 68.69, 0.27547, 2, 5, 46.58, 66.95, 0.38248, 6, 19.42, 64.07, 0.61752, 1, 6, 39.66, 53.39, 1, 3, 5, 82, 50.31, 0.03164, 6, 48.45, 37.84, 0.36658, 7, -39.65, -9.04, 0.60178, 2, 5, 89.15, 50.51, 0.00777, 7, -40.44, -1.93, 0.99223, 1, 7, -36.34, 7.08, 1, 1, 7, -17.22, 10.08, 1, 1, 7, 4.11, 9.6, 1, 1, 7, 22.28, 6.92, 1, 1, 7, 29.71, 2.65, 1, 3, 5, 82.66, -19.89, 0.00505, 6, 28.65, -29.51, 0.06675, 7, 30.25, -2.63, 0.92821, 3, 5, 77.17, -20, 0.0772, 6, 23.36, -28.02, 0.6748, 7, 30.81, -8.09, 0.248, 2, 5, 65.64, -34.91, 0.36679, 6, 7.99, -38.92, 0.63321, 3, 5, 46.86, -47.31, 0.67996, 6, -13.59, -45.32, 0.30404, 7, 60.52, -36.06, 0.016, 2, 5, 24.94, -51.68, 0.88985, 6, -35.83, -43.11, 0.11015, 3, 5, 1.16, -45.94, 0.9754, 6, -56.91, -30.69, 0.0182, 7, 62.89, -81.72, 0.0064, 1, 5, -18.24, -25.48, 1, 1, 5, -0.69, -4.72, 1, 3, 5, 26.94, -6.75, 0.99371, 6, -20.84, -0.71, 0.00351, 7, 21.72, -59.24, 0.00278, 3, 5, 48.42, -4.17, 0.27963, 6, 0.46, -4.5, 0.70273, 7, 17.39, -38.05, 0.01764, 2, 6, 20.62, -1.35, 0.98414, 7, 7.03, -20.47, 0.01586, 2, 6, 38.11, -1.03, 0.91816, 7, 0.29, -4.33, 0.08184, 1, 7, -0.92, 2.66, 1], "hull": 20, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 0, 38, 0, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 14, 50, 26, 46, 30, 40, 4, 42, 6], "width": 120, "height": 120}}, "b1": {"b1": {"type": "mesh", "uvs": [0.46901, 0, 0.58993, 0, 0.72655, 0.03143, 0.85532, 0.09913, 0.96367, 0.26614, 1, 0.44217, 1, 0.57984, 0.93854, 0.74007, 0.83019, 0.88, 0.66059, 0.96801, 0.5067, 1, 0.37008, 1, 0.21462, 0.9635, 0.07486, 0.88677, 0, 0.77618, 0, 0.5821, 0.0089, 0.39704, 0.06072, 0.22777, 0.17222, 0.07882, 0.31512, 0.01563, 0.38035, 0.17145, 0.33436, 0.3722, 0.31903, 0.61458, 0.34118, 0.85695, 0.54175, 0.19172, 0.50039, 0.37427, 0.48414, 0.61414, 0.49005, 0.83914, 0.68649, 0.20233, 0.67025, 0.38701, 0.65843, 0.61838, 0.654, 0.80942, 0.84453, 0.25752, 0.83862, 0.42522, 0.83124, 0.62899, 0.81795, 0.76909, 0.23748, 0.16837, 0.18579, 0.37852, 0.17545, 0.62051, 0.19169, 0.80943, 0.11637, 0.21507, 0.08535, 0.3955, 0.07501, 0.60565, 0.08683, 0.7882], "triangles": [11, 23, 27, 12, 39, 23, 9, 31, 8, 10, 27, 9, 8, 35, 7, 13, 14, 43, 12, 23, 11, 12, 13, 39, 11, 27, 10, 35, 34, 7, 31, 35, 8, 27, 31, 9, 13, 43, 39, 40, 18, 36, 41, 17, 40, 41, 40, 37, 21, 36, 20, 36, 19, 20, 37, 40, 36, 37, 36, 21, 20, 0, 24, 40, 17, 18, 20, 19, 0, 36, 18, 19, 28, 1, 2, 28, 2, 3, 24, 0, 1, 32, 28, 3, 29, 24, 28, 29, 28, 32, 33, 29, 32, 30, 25, 29, 30, 29, 33, 33, 32, 4, 34, 30, 33, 34, 33, 6, 26, 21, 25, 24, 1, 28, 25, 20, 24, 25, 24, 29, 21, 20, 25, 32, 3, 4, 33, 4, 5, 6, 33, 5, 7, 34, 6, 42, 15, 41, 42, 41, 38, 35, 30, 34, 43, 42, 38, 14, 42, 43, 31, 30, 35, 22, 37, 21, 22, 21, 26, 23, 22, 26, 39, 22, 23, 26, 25, 30, 38, 41, 37, 38, 37, 22, 31, 26, 30, 39, 38, 22, 43, 38, 39, 27, 26, 31, 23, 26, 27, 41, 15, 16, 14, 15, 42, 16, 17, 41], "vertices": [4, 4, 79.15, 10.57, 0.03731, 11, 43.96, -2.77, 0.45269, 19, 28.15, -22.32, 0.21, 20, -14.25, -12.61, 0.3, 5, 3, 123.92, -44.32, 6e-05, 4, 88.68, -19.77, 0.00442, 11, 43.96, -34.57, 0.55552, 19, 28.15, -54.12, 0.14, 20, -14.25, -44.41, 0.3, 3, 11, 38.21, -70.51, 0.63, 19, 22.4, -90.05, 0.07, 20, -20.01, -80.34, 0.3, 3, 11, 25.82, -104.37, 0.665, 19, 10.01, -123.91, 0.035, 20, -32.4, -114.2, 0.3, 4, 3, 76.62, -143.3, 0.14883, 4, 71.69, -128.14, 0.53017, 19, -20.55, -152.41, 0.021, 20, -62.96, -142.7, 0.3, 4, 3, 44.55, -153.31, 0.19877, 4, 43.82, -146.92, 0.48023, 19, -52.77, -161.97, 0.021, 20, -95.17, -152.25, 0.3, 5, 3, 19.36, -153.67, 0.19426, 4, 19.79, -154.47, 0.36524, 2, 149.14, 21.58, 0.1195, 19, -77.96, -161.97, 0.021, 20, -120.37, -152.25, 0.3, 5, 3, -10.19, -137.93, 0.16104, 4, -13.03, -147.84, 0.20833, 2, 133.47, -8.01, 0.30962, 19, -107.28, -145.8, 0.021, 20, -149.69, -136.09, 0.3, 4, 3, -36.2, -109.8, 0.11704, 2, 105.4, -34.09, 0.54796, 19, -132.89, -117.31, 0.035, 20, -175.29, -107.6, 0.3, 3, 2, 61.08, -50.95, 0.63, 19, -149, -72.7, 0.07, 20, -191.4, -62.99, 0.3, 3, 2, 20.71, -57.48, 0.56, 19, -154.85, -32.23, 0.14, 20, -197.25, -22.52, 0.3, 3, 2, -15.22, -58.08, 0.49, 19, -154.85, 3.7, 0.21, 20, -197.25, 13.41, 0.3, 3, 2, -56.21, -52.09, 0.56, 19, -148.17, 44.59, 0.14, 20, -190.58, 54.3, 0.3, 4, 3, -40.28, 88.81, 0.19152, 2, -93.2, -38.67, 0.43848, 19, -134.13, 81.35, 0.07, 20, -176.53, 91.06, 0.3, 5, 3, -20.33, 108.79, 0.30963, 11, -98.08, 120.58, 0.05213, 2, -113.23, -18.76, 0.30324, 19, -113.89, 101.03, 0.035, 20, -156.3, 110.75, 0.3, 6, 3, 15.19, 109.29, 0.40998, 4, -59.46, 96.31, 1e-05, 11, -62.56, 120.58, 0.13797, 2, -113.82, 16.75, 0.11704, 19, -78.37, 101.03, 0.035, 20, -120.78, 110.75, 0.3, 5, 3, 49.08, 107.44, 0.38109, 4, -26.45, 104.23, 0.00184, 11, -28.69, 118.24, 0.28207, 19, -44.51, 98.69, 0.035, 20, -86.91, 108.4, 0.3, 5, 3, 80.25, 94.25, 0.25073, 4, 7.19, 100.51, 0.00468, 11, 2.28, 104.61, 0.4096, 19, -13.53, 85.06, 0.035, 20, -55.94, 94.77, 0.3, 5, 3, 107.92, 65.32, 0.09604, 4, 41.98, 80.71, 0.00179, 11, 29.54, 75.28, 0.53217, 19, 13.73, 55.74, 0.07, 20, -28.68, 65.45, 0.3, 4, 3, 120.02, 27.91, 0.00977, 11, 41.1, 37.7, 0.55023, 19, 25.29, 18.16, 0.14, 20, -17.12, 27.87, 0.3, 3, 3, 91.76, 10.34, 0.00327, 11, 12.59, 20.54, 0.69673, 19, -3.22, 1, 0.3, 4, 3, 54.85, 21.91, 0.20424, 4, 3.55, 23.93, 0.26288, 11, -24.15, 32.64, 0.23288, 19, -39.96, 13.1, 0.3, 5, 3, 10.44, 25.31, 0.5458, 4, -39.97, 14.48, 0.00064, 11, -68.5, 36.67, 0.03036, 2, -29.83, 12.22, 0.1232, 19, -84.32, 17.13, 0.3, 4, 3, -33.82, 18.85, 0.37777, 11, -112.86, 30.85, 0.00303, 2, -23.26, -32.03, 0.3192, 19, -128.67, 11.3, 0.3, 4, 3, 88.65, -32.15, 0.01111, 4, 51.41, -18.2, 0.54294, 11, 8.88, -21.9, 0.24595, 19, -6.93, -41.45, 0.2, 3, 3, 55.09, -21.75, 0.12399, 4, 16.28, -17.84, 0.67601, 19, -40.34, -30.57, 0.2, 4, 3, 11.14, -18.11, 0.58765, 4, -26.88, -26.92, 0.07155, 2, 13.59, 13.02, 0.1408, 19, -84.24, -26.3, 0.2, 4, 3, -30.01, -20.25, 0.41236, 4, -65.69, -40.75, 0.02284, 2, 15.83, -28.12, 0.3648, 19, -125.41, -27.85, 0.2, 4, 3, 87.26, -70.24, 0.07466, 4, 60.97, -55.1, 0.69551, 11, 6.94, -59.97, 0.12984, 19, -8.88, -79.51, 0.1, 3, 3, 53.4, -66.45, 0.22189, 4, 27.45, -61.16, 0.67811, 19, -42.67, -75.24, 0.1, 4, 3, 11.02, -63.95, 0.4179, 4, -13.87, -70.89, 0.3237, 2, 59.43, 13.02, 0.1584, 19, -85.01, -72.13, 0.1, 4, 3, -23.95, -63.29, 0.35779, 4, -47.57, -80.26, 0.13181, 2, 58.85, -21.96, 0.4104, 19, -119.97, -70.97, 0.1, 3, 3, 77.75, -111.95, 0.17395, 4, 63.8, -97.78, 0.77605, 19, -18.98, -121.08, 0.05, 3, 3, 47.04, -110.83, 0.2668, 4, 34.06, -105.5, 0.6832, 19, -49.66, -119.52, 0.05, 4, 3, 9.73, -109.42, 0.33705, 4, -2.1, -114.83, 0.44575, 2, 104.91, 11.84, 0.1672, 19, -86.95, -117.58, 0.05, 4, 3, -15.96, -106.29, 0.27335, 4, -27.61, -119.18, 0.24345, 2, 101.84, -13.86, 0.4332, 19, -112.59, -114.09, 0.05, 4, 3, 91.78, 47.92, 0.10512, 4, 31.5, 59.42, 0.01761, 11, 13.15, 58.12, 0.67727, 19, -2.66, 38.58, 0.2, 4, 3, 53.14, 60.97, 0.38047, 4, -9.27, 60.86, 0.0408, 11, -25.31, 71.72, 0.37874, 19, -41.12, 52.17, 0.2, 5, 3, 8.82, 63.05, 0.54469, 4, -52.33, 50.18, 0.00245, 11, -69.59, 74.43, 0.11206, 2, -67.57, 10.5, 0.1408, 19, -85.4, 54.89, 0.2, 4, 3, -25.69, 58.29, 0.40564, 11, -104.16, 70.16, 0.02956, 2, -62.71, -24, 0.3648, 19, -119.97, 50.62, 0.2, 4, 3, 82.78, 79.65, 0.28186, 4, 13.79, 87.25, 0.00913, 11, 4.61, 89.97, 0.60901, 19, -11.21, 70.43, 0.1, 4, 3, 49.65, 87.34, 0.48937, 4, -20.15, 85.13, 0.00837, 11, -28.41, 98.13, 0.40226, 19, -44.22, 78.59, 0.1, 5, 3, 11.16, 89.51, 0.57362, 4, -57.65, 76.19, 0.00059, 11, -66.87, 100.85, 0.16739, 2, -94.02, 12.77, 0.1584, 19, -82.68, 81.31, 0.1, 4, 3, -22.2, 85.92, 0.4319, 11, -100.28, 97.74, 0.0577, 2, -90.36, -20.58, 0.4104, 19, -116.09, 78.2, 0.1], "hull": 20, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 0, 38, 0, 40, 40, 42, 42, 44, 44, 46, 2, 48, 48, 50, 50, 52, 52, 54, 4, 56, 56, 58, 58, 60, 60, 62, 6, 64, 64, 66, 66, 68, 68, 70, 38, 72, 72, 74, 74, 76, 76, 78, 36, 80, 80, 82, 82, 84, 84, 86], "width": 263, "height": 183}}, "sd": {"sd": {"x": 3.1, "y": -2.4, "width": 125, "height": 50}}, "tou1": {"tou1": {"x": 35.96, "y": 12.08, "rotation": -90, "width": 65, "height": 80}}, "tou2": {"tou2": {"type": "mesh", "uvs": [0.49809, 0, 0.56415, 0.00629, 0.61339, 0.03052, 0.68905, 0, 0.80075, 0.03425, 0.87161, 0.16473, 0.87641, 0.27284, 0.95688, 0.40518, 1, 0.61581, 0.98691, 0.79662, 0.88122, 0.9066, 0.72869, 0.98861, 0.55574, 1, 0.38039, 1, 0.21825, 0.98861, 0.05491, 0.89914, 0, 0.74443, 0.00206, 0.57108, 0.07773, 0.38468, 0.15219, 0.28029, 0.15339, 0.16473, 0.22305, 0.05475, 0.30112, 0.01188, 0.38399, 0.02679, 0.43323, 0.00815, 0.42603, 0.12809, 0.37664, 0.28293, 0.3559, 0.43318, 0.33911, 0.57576, 0.34009, 0.7536, 0.3559, 0.89771, 0.48312, 0.59507, 0.68299, 0.59315, 0.88163, 0.56232, 0.47939, 0.43516, 0.65941, 0.42552, 0.81459, 0.37543, 0.47567, 0.28872, 0.63582, 0.25982, 0.74382, 0.20009, 0.52409, 0.1288, 0.61347, 0.12302, 0.70782, 0.07293, 0.34234, 0.12302, 0.27655, 0.09027, 0.2902, 0.28102, 0.22565, 0.27331, 0.25793, 0.42938, 0.16233, 0.41204, 0.2182, 0.59122, 0.10274, 0.57195, 0.21199, 0.75885, 0.08164, 0.72417, 0.4789, 0.76656, 0.70857, 0.77233, 0.88734, 0.71839, 0.49088, 0.89647, 0.72428, 0.89647, 0.21032, 0.88298], "triangles": [13, 56, 12, 12, 57, 11, 57, 12, 54, 11, 57, 10, 15, 58, 14, 14, 58, 30, 29, 58, 51, 10, 55, 9, 55, 10, 54, 15, 52, 58, 15, 16, 52, 30, 53, 56, 12, 56, 54, 10, 57, 54, 56, 53, 54, 58, 52, 51, 9, 55, 8, 54, 53, 32, 53, 31, 32, 54, 32, 55, 53, 29, 31, 51, 52, 49, 51, 49, 29, 49, 52, 50, 16, 17, 52, 52, 17, 50, 32, 33, 55, 55, 33, 8, 33, 7, 8, 27, 34, 31, 31, 35, 32, 31, 34, 35, 32, 36, 33, 32, 35, 36, 50, 48, 49, 49, 47, 28, 49, 48, 47, 17, 18, 50, 50, 18, 48, 33, 36, 7, 26, 37, 34, 34, 38, 35, 34, 37, 38, 47, 45, 27, 48, 46, 47, 47, 46, 45, 35, 39, 36, 35, 38, 39, 18, 19, 48, 48, 19, 46, 36, 6, 7, 36, 39, 6, 38, 40, 41, 38, 37, 40, 37, 25, 40, 45, 43, 26, 46, 44, 45, 45, 44, 43, 19, 20, 46, 20, 21, 46, 46, 21, 44, 39, 5, 6, 39, 41, 42, 39, 38, 41, 39, 4, 5, 39, 42, 4, 41, 1, 2, 41, 40, 1, 40, 0, 1, 43, 23, 25, 25, 23, 24, 44, 22, 43, 43, 22, 23, 42, 2, 3, 42, 41, 2, 44, 21, 22, 42, 3, 4, 14, 30, 13, 13, 30, 56, 58, 29, 30, 30, 29, 53, 49, 28, 29, 29, 28, 31, 28, 27, 31, 28, 47, 27, 27, 26, 34, 27, 45, 26, 26, 25, 37, 26, 43, 25, 25, 0, 40, 25, 24, 0], "vertices": [3, 11, 115.96, -11.05, 0.49, 19, 100.15, -30.6, 0.21, 20, 57.74, -20.88, 0.3, 3, 11, 115.18, -23.87, 0.539, 19, 99.37, -43.41, 0.161, 20, 56.96, -33.7, 0.3, 3, 11, 112.15, -33.42, 0.588, 19, 96.34, -52.96, 0.112, 20, 53.93, -43.25, 0.3, 3, 11, 115.96, -48.1, 0.637, 19, 100.15, -67.64, 0.063, 20, 57.74, -57.93, 0.3, 3, 11, 111.68, -69.77, 0.658, 19, 95.87, -89.31, 0.042, 20, 53.46, -79.6, 0.3, 3, 11, 95.37, -83.52, 0.658, 19, 79.56, -103.06, 0.042, 20, 37.15, -93.35, 0.3, 3, 11, 81.86, -84.45, 0.658, 19, 66.05, -103.99, 0.042, 20, 23.64, -94.28, 0.3, 3, 11, 65.32, -100.06, 0.658, 19, 49.5, -119.6, 0.042, 20, 7.1, -109.89, 0.3, 3, 11, 38.99, -108.42, 0.658, 19, 23.17, -127.97, 0.042, 20, -19.23, -118.26, 0.3, 3, 11, 16.39, -105.88, 0.658, 19, 0.57, -125.43, 0.042, 20, -41.83, -115.72, 0.3, 3, 11, 2.64, -85.38, 0.637, 19, -13.17, -104.92, 0.063, 20, -55.58, -95.21, 0.3, 3, 11, -7.61, -55.79, 0.588, 19, -23.43, -75.33, 0.112, 20, -65.83, -65.62, 0.3, 3, 11, -9.04, -22.24, 0.539, 19, -24.85, -41.78, 0.161, 20, -67.25, -32.07, 0.3, 3, 11, -9.04, 11.78, 0.49, 19, -24.85, -7.76, 0.21, 20, -67.26, 1.95, 0.3, 3, 11, -7.61, 43.24, 0.539, 19, -23.43, 23.69, 0.161, 20, -65.83, 33.4, 0.3, 3, 11, 3.57, 74.92, 0.588, 19, -12.24, 55.38, 0.112, 20, -54.65, 65.09, 0.3, 3, 11, 22.91, 85.58, 0.658, 19, 7.1, 66.03, 0.042, 20, -35.31, 75.75, 0.3, 3, 11, 44.58, 85.18, 0.658, 19, 28.77, 65.63, 0.042, 20, -13.64, 75.34, 0.3, 3, 11, 67.88, 70.5, 0.658, 19, 52.07, 50.95, 0.042, 20, 9.66, 60.67, 0.3, 3, 11, 80.93, 56.05, 0.658, 19, 65.11, 36.51, 0.042, 20, 22.71, 46.22, 0.3, 3, 11, 95.37, 55.82, 0.658, 19, 79.56, 36.28, 0.042, 20, 37.15, 45.99, 0.3, 3, 11, 109.12, 42.3, 0.658, 19, 93.31, 22.76, 0.042, 20, 50.9, 32.47, 0.3, 3, 11, 114.48, 27.16, 0.588, 19, 98.67, 7.62, 0.112, 20, 56.26, 17.33, 0.3, 3, 11, 112.61, 11.08, 0.5544, 19, 96.8, -8.46, 0.1656, 20, 54.4, 1.25, 0.28, 3, 11, 114.94, 1.53, 0.539, 19, 99.13, -18.01, 0.161, 20, 56.73, -8.3, 0.3, 2, 11, 99.95, 2.93, 0.7, 19, 84.14, -16.62, 0.3, 2, 11, 80.6, 12.51, 0.7, 19, 64.78, -7.04, 0.3, 2, 11, 61.82, 16.53, 0.7, 19, 46, -3.01, 0.3, 2, 11, 43.99, 19.79, 0.7, 19, 28.18, 0.25, 0.3, 2, 11, 21.76, 19.6, 0.7, 19, 5.95, 0.06, 0.3, 2, 11, 3.75, 16.53, 0.7, 19, -12.06, -3.01, 0.3, 2, 11, 41.58, -8.15, 0.77, 19, 25.77, -27.69, 0.23, 2, 11, 41.82, -46.92, 0.84, 19, 26.01, -66.47, 0.16, 2, 11, 45.67, -85.46, 0.91, 19, 29.86, -105, 0.09, 2, 11, 61.57, -7.43, 0.77, 19, 45.76, -26.97, 0.23, 2, 11, 62.77, -42.35, 0.84, 19, 46.96, -61.89, 0.16, 2, 11, 69.03, -72.45, 0.91, 19, 53.22, -92, 0.09, 2, 11, 79.87, -6.7, 0.77, 19, 64.06, -26.25, 0.23, 2, 11, 83.49, -37.77, 0.84, 19, 67.67, -57.31, 0.16, 2, 11, 90.95, -58.73, 0.91, 19, 75.14, -78.27, 0.09, 2, 11, 99.86, -16.1, 0.77, 19, 84.05, -35.64, 0.23, 2, 11, 100.58, -33.44, 0.84, 19, 84.77, -52.98, 0.16, 2, 11, 106.85, -51.74, 0.91, 19, 91.04, -71.28, 0.09, 2, 11, 100.58, 19.16, 0.77, 19, 84.77, -0.38, 0.23, 2, 11, 104.68, 31.93, 0.84, 19, 88.87, 12.38, 0.16, 2, 11, 80.84, 29.28, 0.77, 19, 65.02, 9.73, 0.23, 2, 11, 81.8, 41.8, 0.84, 19, 65.99, 22.26, 0.16, 2, 11, 62.29, 35.54, 0.77, 19, 46.48, 16, 0.23, 2, 11, 64.46, 54.08, 0.84, 19, 48.65, 34.54, 0.16, 2, 11, 42.06, 43.25, 0.77, 19, 26.25, 23.7, 0.23, 2, 11, 44.47, 65.64, 0.84, 19, 28.66, 46.1, 0.16, 2, 11, 21.11, 44.45, 0.77, 19, 5.3, 24.91, 0.23, 2, 11, 25.44, 69.74, 0.84, 19, 9.63, 50.2, 0.16, 2, 11, 20.14, -7.33, 0.77, 19, 4.33, -26.87, 0.23, 2, 11, 19.42, -51.89, 0.84, 19, 3.61, -71.43, 0.16, 2, 11, 26.16, -86.57, 0.91, 19, 10.35, -106.11, 0.09, 2, 11, 3.9, -9.65, 0.77, 19, -11.91, -29.2, 0.23, 2, 11, 3.9, -54.93, 0.84, 19, -11.91, -74.48, 0.16, 2, 11, 5.59, 44.78, 0.77, 19, -10.22, 25.23, 0.23], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48, 0, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 56, 62, 62, 64, 64, 66, 54, 68, 68, 70, 70, 72, 52, 74, 74, 76, 76, 78, 50, 80, 80, 82, 82, 84, 50, 86, 86, 88, 52, 90, 90, 92, 54, 94, 94, 96, 56, 98, 98, 100, 58, 102, 102, 104, 58, 106, 106, 108, 108, 110, 106, 112, 108, 114, 102, 116], "width": 194, "height": 125}}, "y2": {"y2": {"type": "mesh", "uvs": [0.30913, 0.00849, 0.47587, 0, 0.70459, 0.02617, 0.86705, 0.14186, 0.944, 0.37486, 0.97393, 0.56287, 1, 0.80872, 0.86705, 0.8971, 0.59985, 0.97423, 0.33692, 1, 0.23859, 0.82479, 0.15095, 0.61429, 0.04834, 0.41664, 0, 0.26881, 0.08255, 0.11776, 0.42497, 0.20887, 0.54661, 0.45176, 0.64545, 0.72324], "triangles": [9, 10, 8, 8, 17, 7, 8, 10, 17, 7, 17, 6, 10, 11, 17, 17, 5, 6, 17, 16, 5, 11, 16, 17, 11, 12, 16, 16, 4, 5, 12, 15, 16, 3, 16, 15, 16, 3, 4, 3, 15, 2, 2, 15, 1, 15, 13, 14, 15, 12, 13, 14, 0, 15, 15, 0, 1], "vertices": [1, 21, -37.94, -2.44, 1, 2, 21, -33.14, 15.13, 0.99706, 22, -70.9, 9.85, 0.00294, 2, 21, -21.36, 37.43, 0.9564, 22, -60.83, 32.97, 0.0436, 2, 21, 0.3, 48.64, 0.85406, 22, -40.07, 45.77, 0.14594, 3, 21, 34.96, 45.47, 0.45791, 22, -5.26, 45.2, 0.52457, 23, -33.92, 46.91, 0.01753, 3, 21, 61.78, 39.59, 0.09192, 22, 21.92, 41.35, 0.67246, 23, -6.96, 41.74, 0.23562, 2, 22, 57.1, 34.94, 0.19231, 23, 27.87, 33.62, 0.80769, 2, 22, 65.77, 17.64, 0.05177, 23, 35.68, 15.92, 0.94823, 1, 23, 37.49, -15.23, 1, 3, 21, 98.84, -46.81, 0.00019, 22, 65.34, -42.04, 0.02676, 23, 32.34, -43.67, 0.97304, 3, 21, 71.32, -48.59, 0.05851, 22, 38.03, -45.87, 0.21537, 23, 4.88, -46.16, 0.72612, 3, 21, 39.36, -47.58, 0.47514, 22, 6.08, -47.26, 0.33909, 23, -27.1, -45.99, 0.18577, 3, 21, 8.61, -48.73, 0.9197, 22, -24.49, -50.71, 0.06774, 23, -57.8, -47.94, 0.01256, 3, 21, -13.36, -46.67, 0.99379, 22, -46.56, -50.29, 0.00617, 23, -79.83, -46.45, 3e-05, 1, 21, -31.09, -30.97, 1, 2, 21, -6.35, -0.06, 0.99993, 22, -43.06, -3.29, 7e-05, 2, 21, 31.27, 0.89, 0.9921, 22, -5.61, 0.47, 0.0079, 2, 22, 35.2, 0.77, 0.02478, 23, 4.32, 0.56, 0.97522], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28, 0, 30, 30, 32, 32, 34], "width": 109, "height": 145}}, "s1": {"s1": {"x": 24.89, "y": 4.21, "rotation": 120.85, "width": 92, "height": 101}}, "s2": {"s2": {"type": "mesh", "uvs": [0.51501, 0.20821, 0.52425, 0.17644, 0.56278, 0.10422, 0.65189, 0.02209, 0.75544, 0, 0.86982, 0.02917, 0.95531, 0.15378, 1, 0.34352, 1, 0.51344, 0.96254, 0.68053, 0.85658, 0.83204, 0.66263, 0.94483, 0.46953, 1, 0.24933, 0.98268, 0.08163, 0.86515, 0, 0.69583, 0.00371, 0.50061, 0.10535, 0.30739, 0.27135, 0.21377, 0.40008, 0.19584, 0.78624, 0.12926, 0.79544, 0.27886, 0.76938, 0.41224, 0.67589, 0.55643, 0.52109, 0.59789, 0.33717, 0.62673, 0.15478, 0.63754, 0.05278, 0.67689], "triangles": [25, 18, 19, 25, 19, 24, 17, 18, 25, 26, 16, 17, 25, 26, 17, 27, 16, 26, 15, 16, 27, 14, 27, 26, 15, 27, 14, 13, 26, 25, 14, 26, 13, 12, 25, 24, 13, 25, 12, 1, 23, 0, 23, 1, 22, 19, 0, 23, 24, 19, 23, 23, 22, 9, 10, 23, 9, 11, 24, 23, 11, 23, 10, 12, 24, 11, 20, 4, 5, 20, 5, 6, 21, 20, 6, 21, 6, 7, 20, 3, 4, 20, 2, 3, 22, 21, 7, 2, 20, 21, 21, 1, 2, 22, 7, 8, 21, 22, 1, 9, 22, 8], "vertices": [3, 16, -6.45, -34.01, 0.51024, 17, 33.35, -45.75, 0.29734, 18, -9.28, -49.66, 0.19243, 3, 16, -9.87, -31.58, 0.63716, 17, 30.86, -49.14, 0.23774, 18, -10.84, -53.56, 0.1251, 3, 16, -16.91, -23.57, 0.82151, 17, 22.76, -56.06, 0.12752, 18, -16.95, -62.28, 0.05096, 3, 16, -23.01, -8.09, 0.96484, 17, 7.19, -61.93, 0.02658, 18, -30.56, -71.87, 0.00858, 3, 16, -21.31, 7.29, 0.99927, 17, -8.16, -60.01, 0.00057, 18, -45.9, -73.86, 0.00015, 1, 16, -13.02, 22.36, 1, 1, 16, 5.49, 29.97, 1, 2, 16, 30.1, 29.5, 0.99905, 17, -29.62, -8.28, 0.00095, 2, 16, 50.45, 23.44, 0.73961, 17, -23.26, 11.98, 0.26039, 2, 16, 68.9, 12.2, 0.2195, 17, -11.76, 30.27, 0.7805, 3, 16, 82.61, -8.13, 0.0101, 17, 8.77, 43.68, 0.9747, 18, -55.47, 30.77, 0.0152, 2, 17, 40.19, 48.6, 0.75998, 18, -26.28, 43.4, 0.24002, 2, 17, 69.34, 46.69, 0.29707, 18, 2.42, 48.85, 0.70293, 2, 17, 99.58, 34.94, 0.01672, 18, 34.64, 45.04, 0.98329, 1, 18, 58.51, 29.12, 1, 1, 18, 69.42, 7.37, 1, 3, 16, 7.14, -116.47, 7e-05, 17, 116, -33.36, 0.00057, 18, 67.64, -16.97, 0.99936, 3, 16, -11.75, -95.27, 0.01325, 17, 94.52, -51.94, 0.03793, 18, 51.49, -40.33, 0.94883, 3, 16, -16, -68.54, 0.07894, 17, 67.73, -55.81, 0.16517, 18, 26.53, -50.78, 0.75588, 3, 16, -12.75, -49.76, 0.20259, 17, 49.01, -52.28, 0.28954, 18, 7.51, -52.06, 0.50788, 1, 16, -4.54, 7.02, 1, 1, 16, 13.77, 2.98, 1, 2, 16, 28.66, -5.45, 0.7309, 17, 5.3, -10.23, 0.2691, 1, 17, 23.8, 2.86, 1, 1, 17, 47.07, 0.99, 1, 1, 18, 19.49, 1.26, 1, 1, 18, 46.33, 1.25, 1, 1, 18, 61.55, 5.4, 1], "hull": 20, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 0, 38, 8, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 2, 46, 46, 0, 38, 46, 46, 18, 46, 20, 46, 22], "width": 147, "height": 125}}, "s3": {"s3": {"x": 7.44, "y": -2.93, "rotation": -13.52, "width": 95, "height": 97}}, "y1": {"y1": {"type": "mesh", "uvs": [1, 0.0261, 1, 0.12154, 0.84411, 0.19563, 0.66888, 0.3187, 0.52011, 0.46312, 0.39447, 0.62512, 0.32174, 0.77959, 0.30521, 1, 0.13329, 0.99935, 0, 0.95666, 0.0308, 0.75949, 0.0837, 0.59498, 0.12999, 0.41415, 0.25892, 0.23582, 0.50358, 0.09266, 0.73831, 0.01856, 0.88378, 0, 0.84193, 0.07305, 0.66118, 0.14781, 0.45232, 0.27445, 0.32378, 0.43617, 0.24747, 0.60553, 0.17516, 0.77336], "triangles": [22, 10, 21, 6, 22, 5, 9, 10, 22, 8, 9, 22, 6, 8, 22, 7, 8, 6, 20, 12, 19, 4, 20, 3, 11, 12, 20, 21, 11, 20, 21, 20, 4, 5, 21, 4, 10, 11, 21, 22, 21, 5, 17, 15, 16, 17, 16, 0, 14, 15, 17, 17, 0, 1, 18, 14, 17, 2, 17, 1, 18, 17, 2, 13, 14, 18, 19, 13, 18, 19, 18, 2, 3, 19, 2, 12, 13, 19, 20, 19, 3], "vertices": [1, 24, -9.36, 3.08, 1, 1, 24, 1.72, 8.44, 1, 1, 24, 13.65, 5.73, 1, 2, 24, 31.69, 4.93, 0.99977, 25, -7.42, 6.07, 0.00023, 1, 25, 12.54, 4.7, 1, 2, 25, 34.32, 5.1, 0.99967, 26, -6.35, 6.23, 0.00033, 1, 26, 13.88, 5.46, 1, 1, 26, 42.15, 8.59, 1, 1, 26, 43.23, 0.24, 1, 1, 26, 38.68, -6.99, 1, 1, 26, 13.28, -9.02, 1, 2, 25, 35.18, -10.6, 0.96999, 26, -8.09, -9.39, 0.03001, 2, 24, 54.27, -13.47, 0.02991, 25, 12.25, -15.43, 0.97009, 2, 24, 30.82, -17.81, 0.91856, 25, -11.59, -16.3, 0.08144, 1, 24, 8.97, -15.07, 1, 1, 24, -4.64, -8.89, 1, 1, 24, -9.91, -3.51, 1, 1, 24, -0.53, -1.25, 1, 1, 24, 12.01, -5.02, 1, 2, 24, 31.17, -7.11, 0.97013, 25, -9.68, -5.76, 0.02987, 2, 24, 52.69, -3.69, 0.00237, 25, 12.11, -5.52, 0.99763, 1, 25, 34.07, -2.53, 1, 1, 26, 14.08, -1.77, 1], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32, 0, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 16], "width": 49, "height": 129}}}}], "animations": {"idle": {"bones": {"bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -2.23, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone4": {"rotate": [{"angle": -0.63, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.23, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -0.63}], "translate": [{"x": 0.49, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.74, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.49}]}, "bone5": {"rotate": [{"angle": 7.33}]}, "bone6": {"rotate": [{"angle": -16.93}]}, "bone8": {"rotate": [{"angle": 0.78}]}, "bone9": {"rotate": [{"angle": -1.74}]}, "bone11": {"rotate": [{"angle": -1.6, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -2.23, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -1.6}], "translate": [{"x": 1.36, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.89, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 1.36}]}, "bone12": {"translate": [{"x": 1.11, "y": -1.61, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 2.21, "y": -3.23, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 1.11, "y": -1.61}]}, "bone13": {"translate": [{"x": 0.03, "y": 2.76, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 0.07, "y": 5.52, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 0.03, "y": 2.76}]}, "bone14": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.39, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone15": {"rotate": [{"angle": 2.38, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 8.39, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 2.38}]}, "bone16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -6.01, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone17": {"rotate": [{"angle": -1.7, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -6.01, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -1.7}]}, "bone18": {"rotate": [{"angle": -4.3, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -6.01, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -4.3}]}, "bone19": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": -2.74, "y": 15.57, "curve": 0.25, "c3": 0.75}, {"time": 2}]}}}, "train": {"slots": {"tou1": {"attachment": [{"time": 0.2333, "name": "tou1"}]}}, "bones": {"bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.98, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 38.77, "curve": "stepped"}, {"time": 0.4, "angle": 38.77, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 42.57, "y": 17.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 14.54, "y": 33.25, "curve": "stepped"}, {"time": 0.4, "x": 14.54, "y": 33.25, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -10.76, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 31.97, "curve": "stepped"}, {"time": 0.4, "angle": 31.97, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}]}, "bone4": {"rotate": [{"angle": 4.73, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 4.14, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 12.84, "curve": "stepped"}, {"time": 0.4667, "angle": 12.84, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.5667, "angle": 4.73}]}, "bone5": {"rotate": [{"angle": 7.33}]}, "bone6": {"rotate": [{"angle": -16.93}]}, "bone8": {"rotate": [{"angle": 0.78}]}, "bone9": {"rotate": [{"angle": -1.74}]}, "bone11": {"rotate": [{"angle": -7.01, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -10.76, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -8.06, "curve": "stepped"}, {"time": 0.5333, "angle": -8.06, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.5667, "angle": -7.01}], "scale": [{"time": 0.1333, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.2667, "x": 1.137, "y": 1.137, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.4}]}, "bone14": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -44.45, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "bone15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -44.45, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "bone16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 21.39, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 35.52, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}]}, "bone17": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 21.39, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 52.12, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}]}, "bone22": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 23.75, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "bone23": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 23.75, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "target1": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -8.8, "y": 32.6, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "target2": {"translate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 16.63, "y": 33.46, "curve": "stepped"}, {"time": 0.4, "x": 16.63, "y": 33.46, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}]}}}}}