[{"__type__": "cc.Prefab", "_name": "group8", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "group8", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 10}, {"__id__": 38}, {"__id__": 62}, {"__id__": 90}, {"__id__": 118}, {"__id__": 124}, {"__id__": 30}, {"__id__": 54}, {"__id__": 82}, {"__id__": 110}], "_active": true, "_components": [{"__id__": 132}, {"__id__": 134}], "_prefab": {"__id__": 136}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_sidatianwang", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}, {"__id__": 5}, {"__id__": 7}], "_prefab": {"__id__": 9}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 4}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d1enfL77hL2pxAM0DIw4kk"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 6}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "280304ef-a81a-420c-8d8a-2a9e5da22aa2@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bbYvLu5ZFOMKNpZOcc/oK0"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 8}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": false, "_isAbsRight": false, "_isAbsTop": false, "_isAbsBottom": false, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 750, "_originalHeight": 1500, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "73Fjnm+R5OAqi32GF4KIme"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "64tq4DQPlDwLPtvTfCGxIf", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 11}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 10}, "asset": {"__uuid__": "ed3cb4df-5620-43c8-85f0-9236e82f1995", "__expectedType__": "cc.Prefab"}, "fileId": "e6g9t2mLpOxopRV91lkbKj", "instance": {"__id__": 12}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "b1gXo+tu9DooWYchlG5l3b", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 13}, {"__id__": 15}, {"__id__": 16}, {"__id__": 17}, {"__id__": 18}, {"__id__": 20}, {"__id__": 22}, {"__id__": 24}, {"__id__": 26}, {"__id__": 28}, {"__id__": 29}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 14}, "propertyPath": ["_name"], "value": "portrait1"}, {"__type__": "cc.TargetInfo", "localID": ["e6g9t2mLpOxopRV91lkbKj"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 14}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -184.161, "y": 304.87699999999995, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 14}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 14}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 19}, "propertyPath": ["_bottom"], "value": 827.377}, {"__type__": "cc.TargetInfo", "localID": ["36iOv3iBlGQpU1RHEt4wvl"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 21}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 648, "height": 904}}, {"__type__": "cc.TargetInfo", "localID": ["bd3n2uusdGc6R5NHdZXfH4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 23}, "propertyPath": ["_spriteFrame"], "value": {"__uuid__": "9a40a612-0e37-4653-bd78-34ca45d294e3@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["d9bjqxaFBEuYlXA47aQuJb"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 25}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 132.699, "y": -91.868, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["67ZyPQBRNO+4KddWx438I8"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 27}, "propertyPath": ["heroId"], "value": 10407}, {"__type__": "cc.TargetInfo", "localID": ["cbh+X3CWxJdokdLhq3jxTZ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 23}, "propertyPath": ["_enabled"], "value": true}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 27}, "propertyPath": ["qualityN"], "value": {"__id__": 30}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 31}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 30}, "asset": {"__uuid__": "4b0be7cd-6f24-4008-8cac-b7c0b5abc8c8", "__expectedType__": "cc.Prefab"}, "fileId": "f4h5rmkgpOFoPtxO24bQFJ", "instance": {"__id__": 32}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "baSHcSIpBOnqNVhGcOPyid", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 33}, {"__id__": 35}, {"__id__": 36}, {"__id__": 37}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 34}, "propertyPath": ["_name"], "value": "quality"}, {"__type__": "cc.TargetInfo", "localID": ["f4h5rmkgpOFoPtxO24bQFJ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 34}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -78.801, "y": 327.037, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 34}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 34}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 39}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 38}, "asset": {"__uuid__": "ed3cb4df-5620-43c8-85f0-9236e82f1995", "__expectedType__": "cc.Prefab"}, "fileId": "e6g9t2mLpOxopRV91lkbKj", "instance": {"__id__": 40}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "4elKeKVNlKQ4rpBOVnaQCv", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 41}, {"__id__": 43}, {"__id__": 44}, {"__id__": 45}, {"__id__": 46}, {"__id__": 48}, {"__id__": 50}, {"__id__": 52}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 42}, "propertyPath": ["_name"], "value": "portrait2"}, {"__type__": "cc.TargetInfo", "localID": ["e6g9t2mLpOxopRV91lkbKj"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 42}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 240.721, "y": 198.94399999999996, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 42}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 42}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 47}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -158.847, "y": -121.834, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["67ZyPQBRNO+4KddWx438I8"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 49}, "propertyPath": ["_bottom"], "value": 721.444}, {"__type__": "cc.TargetInfo", "localID": ["36iOv3iBlGQpU1RHEt4wvl"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 51}, "propertyPath": ["_enabled"], "value": true}, {"__type__": "cc.TargetInfo", "localID": ["d9bjqxaFBEuYlXA47aQuJb"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 53}, "propertyPath": ["qualityN"], "value": {"__id__": 54}}, {"__type__": "cc.TargetInfo", "localID": ["cbh+X3CWxJdokdLhq3jxTZ"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 55}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 54}, "asset": {"__uuid__": "4b0be7cd-6f24-4008-8cac-b7c0b5abc8c8", "__expectedType__": "cc.Prefab"}, "fileId": "f4h5rmkgpOFoPtxO24bQFJ", "instance": {"__id__": 56}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "a84jQybfNNmZSqFr3vDEZN", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 57}, {"__id__": 59}, {"__id__": 60}, {"__id__": 61}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 58}, "propertyPath": ["_name"], "value": "quality"}, {"__type__": "cc.TargetInfo", "localID": ["f4h5rmkgpOFoPtxO24bQFJ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 58}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 89.44, "y": 82.658, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 58}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 58}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 63}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 62}, "asset": {"__uuid__": "ed3cb4df-5620-43c8-85f0-9236e82f1995", "__expectedType__": "cc.Prefab"}, "fileId": "e6g9t2mLpOxopRV91lkbKj", "instance": {"__id__": 64}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "1dpmgYEu5Lo6Jr+ssDzaTN", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 65}, {"__id__": 67}, {"__id__": 68}, {"__id__": 69}, {"__id__": 70}, {"__id__": 72}, {"__id__": 74}, {"__id__": 76}, {"__id__": 78}, {"__id__": 80}, {"__id__": 81}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 66}, "propertyPath": ["_name"], "value": "portrait3"}, {"__type__": "cc.TargetInfo", "localID": ["e6g9t2mLpOxopRV91lkbKj"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 66}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -191.943, "y": -341.476, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 66}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 66}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 71}, "propertyPath": ["heroId"], "value": 10409}, {"__type__": "cc.TargetInfo", "localID": ["cbh+X3CWxJdokdLhq3jxTZ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 73}, "propertyPath": ["_bottom"], "value": 181.024}, {"__type__": "cc.TargetInfo", "localID": ["36iOv3iBlGQpU1RHEt4wvl"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 75}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 126.461, "y": 41.64, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["67ZyPQBRNO+4KddWx438I8"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 77}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 624, "height": 860}}, {"__type__": "cc.TargetInfo", "localID": ["bd3n2uusdGc6R5NHdZXfH4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 79}, "propertyPath": ["_spriteFrame"], "value": {"__uuid__": "eeaa22e6-78aa-484f-8ad4-36f63f5aeccd@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["d9bjqxaFBEuYlXA47aQuJb"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 79}, "propertyPath": ["_enabled"], "value": true}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 71}, "propertyPath": ["qualityN"], "value": {"__id__": 82}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 83}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 82}, "asset": {"__uuid__": "4b0be7cd-6f24-4008-8cac-b7c0b5abc8c8", "__expectedType__": "cc.Prefab"}, "fileId": "f4h5rmkgpOFoPtxO24bQFJ", "instance": {"__id__": 84}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "44fb7gU/xDDLPq3syphRE0", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 85}, {"__id__": 87}, {"__id__": 88}, {"__id__": 89}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 86}, "propertyPath": ["_name"], "value": "quality"}, {"__type__": "cc.TargetInfo", "localID": ["f4h5rmkgpOFoPtxO24bQFJ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 86}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -46.872, "y": -228.036, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 86}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 86}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 91}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 90}, "asset": {"__uuid__": "ed3cb4df-5620-43c8-85f0-9236e82f1995", "__expectedType__": "cc.Prefab"}, "fileId": "e6g9t2mLpOxopRV91lkbKj", "instance": {"__id__": 92}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "94A1ogJ7ZPTrSi6f1ex28/", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 93}, {"__id__": 95}, {"__id__": 96}, {"__id__": 97}, {"__id__": 98}, {"__id__": 100}, {"__id__": 102}, {"__id__": 104}, {"__id__": 106}, {"__id__": 108}, {"__id__": 109}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 94}, "propertyPath": ["_name"], "value": "portrait4"}, {"__type__": "cc.TargetInfo", "localID": ["e6g9t2mLpOxopRV91lkbKj"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 94}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 216.619, "y": -463.46899999999994, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 94}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 94}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 99}, "propertyPath": ["heroId"], "value": 10408}, {"__type__": "cc.TargetInfo", "localID": ["cbh+X3CWxJdokdLhq3jxTZ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 101}, "propertyPath": ["_bottom"], "value": 59.03100000000006}, {"__type__": "cc.TargetInfo", "localID": ["36iOv3iBlGQpU1RHEt4wvl"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 103}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -151.137, "y": 74.026, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["67ZyPQBRNO+4KddWx438I8"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 105}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 620, "height": 726}}, {"__type__": "cc.TargetInfo", "localID": ["bd3n2uusdGc6R5NHdZXfH4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 107}, "propertyPath": ["_spriteFrame"], "value": {"__uuid__": "818eaec2-8d53-47f8-ad81-0b4f9db24ac5@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["d9bjqxaFBEuYlXA47aQuJb"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 107}, "propertyPath": ["_enabled"], "value": true}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 99}, "propertyPath": ["qualityN"], "value": {"__id__": 110}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 111}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 110}, "asset": {"__uuid__": "4b0be7cd-6f24-4008-8cac-b7c0b5abc8c8", "__expectedType__": "cc.Prefab"}, "fileId": "f4h5rmkgpOFoPtxO24bQFJ", "instance": {"__id__": 112}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "020YZJ+v5GwIIObBpvrZlY", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 113}, {"__id__": 115}, {"__id__": 116}, {"__id__": 117}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 114}, "propertyPath": ["_name"], "value": "quality"}, {"__type__": "cc.TargetInfo", "localID": ["f4h5rmkgpOFoPtxO24bQFJ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 114}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 61.195, "y": -477.328, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 114}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 114}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_name": "sida<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 119}, {"__id__": 121}], "_prefab": {"__id__": 123}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 627.015, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 118}, "_enabled": true, "__prefab": {"__id__": 120}, "_contentSize": {"__type__": "cc.Size", "width": 512, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4fGtrz/JZAYbQDcXoSEsoW"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 118}, "_enabled": true, "__prefab": {"__id__": 122}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "55662c0f-1be4-487e-8317-ae369aab9087@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "929tIrxJlHH7gE6wqq1U84"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f64jEkG4VK4bVTAwy33MEg", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "img_yun", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 125}, {"__id__": 127}, {"__id__": 129}], "_prefab": {"__id__": 131}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -645.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 124}, "_enabled": true, "__prefab": {"__id__": 126}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 209}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a4c/MObv5BCK916azavLRA"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 124}, "_enabled": true, "__prefab": {"__id__": 128}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "4255772a-b415-4594-8677-ebd42fbd5cb5@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "27v+oCy6VDPLKSnci4vGHD"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 124}, "_enabled": true, "__prefab": {"__id__": 130}, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6el+y8dNVKBIyTaZfWcUS5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d29We7allNoYvOdbufe/vd", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 133}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f9gjDjQTlMX7oONrqBNH4p"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 135}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1fiusF8M9Cm7wD3bHzxaiB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2097LMUN9CY4U99usSG7RL", "instance": null, "targetOverrides": [{"__id__": 137}, {"__id__": 140}, {"__id__": 143}, {"__id__": 146}], "nestedPrefabInstanceRoots": [{"__id__": 110}, {"__id__": 82}, {"__id__": 54}, {"__id__": 30}, {"__id__": 90}, {"__id__": 62}, {"__id__": 38}, {"__id__": 10}]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 10}, "sourceInfo": {"__id__": 138}, "propertyPath": ["<PERSON><PERSON><PERSON>"], "target": {"__id__": 30}, "targetInfo": {"__id__": 139}}, {"__type__": "cc.TargetInfo", "localID": ["cbh+X3CWxJdokdLhq3jxTZ"]}, {"__type__": "cc.TargetInfo", "localID": ["fb7CQIQz1Lw4yio8fXusdx"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 38}, "sourceInfo": {"__id__": 141}, "propertyPath": ["<PERSON><PERSON><PERSON>"], "target": {"__id__": 54}, "targetInfo": {"__id__": 142}}, {"__type__": "cc.TargetInfo", "localID": ["cbh+X3CWxJdokdLhq3jxTZ"]}, {"__type__": "cc.TargetInfo", "localID": ["fb7CQIQz1Lw4yio8fXusdx"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 62}, "sourceInfo": {"__id__": 144}, "propertyPath": ["<PERSON><PERSON><PERSON>"], "target": {"__id__": 82}, "targetInfo": {"__id__": 145}}, {"__type__": "cc.TargetInfo", "localID": ["cbh+X3CWxJdokdLhq3jxTZ"]}, {"__type__": "cc.TargetInfo", "localID": ["fb7CQIQz1Lw4yio8fXusdx"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 90}, "sourceInfo": {"__id__": 147}, "propertyPath": ["<PERSON><PERSON><PERSON>"], "target": {"__id__": 110}, "targetInfo": {"__id__": 148}}, {"__type__": "cc.TargetInfo", "localID": ["cbh+X3CWxJdokdLhq3jxTZ"]}, {"__type__": "cc.TargetInfo", "localID": ["fb7CQIQz1Lw4yio8fXusdx"]}]