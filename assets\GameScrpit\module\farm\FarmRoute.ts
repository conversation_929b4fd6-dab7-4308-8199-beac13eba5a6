import { UIFarmBeeManage } from "../../game/ui/ui_farm/UIFarmBeeManage";
import { UIFarmCjjj } from "../../game/ui/ui_farm/UIFarmCjjj";
import { UIFarmCollect } from "../../game/ui/ui_farm/UIFarmCollect";
import { UIFarmFind } from "../../game/ui/ui_farm/UIFarmFind";
import { UIFarmLog } from "../../game/ui/ui_farm/UIFarmLog";
import { UIFarmMain } from "../../game/ui/ui_farm/UIFarmMain";
import { UIFarmMainOther } from "../../game/ui/ui_farm/UIFarmMainOther";
import { UIFarmPreview } from "../../game/ui/ui_farm/UIFarmPreview";
import { UIFarmShop } from "../../game/ui/ui_farm/UIFarmShop";
import { Recording, RecordingMap } from "../../lib/ui/recordingMap";

export enum FarmRouteName {
  UIFarmMain = "UIFarmMain",
  UIFarmCollect = "UIFarmCollect",
  UIFarmShop = "UIFarmShop",
  UIFarmLog = "UIFarmLog",
  UIFarmBeeManage = "UIFarmBeeManage",
  UIFarmFind = "UIFarmFind",
  UIFarmMainOther = "UIFarmMainOther",
  UIFarmCjjj = "UIFarmCjjj",
  UIFarmPreview = "UIFarmPreview",
}

export class FarmRoute {
  rotueTables: Recording[] = [
    {
      node: UIFarmMain,
      uiName: FarmRouteName.UIFarmMain,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIFarmCollect,
      uiName: FarmRouteName.UIFarmCollect,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIFarmShop,
      uiName: FarmRouteName.UIFarmShop,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIFarmLog,
      uiName: FarmRouteName.UIFarmLog,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIFarmBeeManage,
      uiName: FarmRouteName.UIFarmBeeManage,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIFarmFind,
      uiName: FarmRouteName.UIFarmFind,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIFarmMainOther,
      uiName: FarmRouteName.UIFarmMainOther,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIFarmCjjj,
      uiName: FarmRouteName.UIFarmCjjj,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIFarmPreview,
      uiName: FarmRouteName.UIFarmPreview,
      keep: false,
      relevanceUIList: [],
    },
  ];
  public async init() {
    this.rotueTables.forEach((item) => {
      RecordingMap.instance.addRecording(item.uiName, item);
    });
  }
}
