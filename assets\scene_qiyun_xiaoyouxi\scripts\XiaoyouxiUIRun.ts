import { _decorator, instantiate, isValid, math, Node, randomRangeInt, tween, v3, Vec3 } from "cc";
import { IRunModule, RunState, XiaoyouxiRoleBehavior, XiaoyouxiRoleType } from "../interface/IRunModule";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
import { BundleEnum } from "../../platform/src/ResHelper";
import { PlayerModule } from "../../GameScrpit/module/player/PlayerModule";
import { PoolMgr } from "../../platform/src/PoolHelper";
import { CharacterPlayer } from "./CharacterPlayer";
import { CharacterBase, RoleSideEnum, RoleStatusEnum } from "./CharacterBase";
import { DialogBox, DialogSideEnum } from "./DialogBox";
import { CharacterBossShiJi } from "./CharacterBossShiJi";
import { CharacterMonster } from "./CharacterMonster";
import { FightModule } from "../../GameScrpit/module/fight/src/FightModule";
import { TipsMgr } from "../../platform/src/TipsHelper";
import { Sleep } from "../../GameScrpit/game/GameDefine";
import { QiyunXiaoyouxiData } from "../QiyunXiaoyouxiData";
import { AudioMgr } from "../../platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("XiaoyouxiUIRun")
export class XiaoyouxiUIRun extends BaseCtrl implements IRunModule {
  // 玩家节点
  private _characterPlayer: CharacterPlayer;

  // NPC妲己
  private _characterNpcDaJi: CharacterBase;

  // 怪物根节点
  public _nodeRootMonster: Node;
  // 怪物对话框
  private _monsterDialogBox: DialogBox;

  // boss节点
  private _nodeBoss: Node;
  private _characterBoss: CharacterBossShiJi;

  // 怪物预制体
  private _nodeMonster: Node;

  // 怪物池
  private _monsterPool: PoolMgr<Node>;

  // 天空节点
  private _nodeSky: Node;

  // 背景节点
  private _nodeGround: Node;

  // 前景节点
  private _nodeFront: Node;

  // 摄像机节点
  private _nodeCamera: Node;

  // 摄像机当前位置
  private _cameraPosX: number;

  // 怪物列表，新加的怪物节点全放这
  private _monsterList: Node[] = [];

  // 玩家是否在战斗中
  private _playerBattle: boolean = false;

  // 妲己是否跟随主角
  private _dajiFollow: boolean = false;

  // 场景回调
  private _stateCallback: (state: RunState, val?: any) => void;

  setNodeCamera(node: Node) {
    this._nodeCamera = node;
  }

  getMonsterPosition(): math.Vec3 {
    return this._nodeRootMonster.worldPosition;
  }

  getBossPosition(): math.Vec3 {
    return this._nodeBoss.worldPosition;
  }

  getLeaderPosition(): math.Vec3 {
    return this._characterPlayer.node.worldPosition;
  }

  getLeaderNode(): Node {
    return this._characterPlayer.node;
  }

  getMonster01Node(): Node {
    return this._nodeRootMonster.getChildByName("CharacterMonster01");
  }

  protected async onLoad(): Promise<void> {
    this._nodeRootMonster = this.getNode("node_root_monster");

    this._nodeSky = this.getNode("node_sky");
    this._nodeGround = this.getNode("node_ground");
    this._nodeFront = this.getNode("node_front");
    this._nodeBoss = this.getNode("CharacterBoss");

    // 初始化数据
    this._characterPlayer = this.getNode("CharacterPlayer").getComponent(CharacterPlayer);

    // 设置主角等级
    this._characterPlayer.setLevelSpine(PlayerModule.data.getPlayerInfo().sex, 1, () => {});

    // 加载NPC妲己
    this._characterNpcDaJi = this.getNode("CharacterNpcDaJi").getComponent(CharacterBase);

    // 加载NPC BOSS 石饥娘娘
    this._characterBoss = this.getNode("CharacterBoss").getComponent(CharacterBossShiJi);

    // 加载怪物预制体
    this._characterNpcDaJi.init({
      aniName: {
        spineAniDefault: "idle",
        spineAniIdle: "boss_idle",
        spineAniWalk: "run1",
        spineAniRun: "run1",
        spineAniAttack: "boss_attack1",
      },
      attr: {
        walkSpeed: 300,
        runSpeed: 600,
        acceleration: 900,
        hpMax: 1,
        attack: 0,
        def: 0,
      },
    });

    this._nodeMonster = this.getNode("CharacterMonster01");

    // 初始化怪物池
    let list: Node[] = [];
    for (let i = 0; i < this._nodeRootMonster.children.length; i++) {
      const characterMonster = this._nodeRootMonster.children[i].getComponent(CharacterMonster);
      if (characterMonster) {
        characterMonster.initMonster(`xiaoguai${(i % 3) + 1}`);
        list.push(this._nodeRootMonster.children[i]);
      }
    }

    this._monsterPool = new PoolMgr<Node>(
      async () => {
        let node = instantiate(this._nodeMonster);
        return node;
      },
      100,
      list
    );
  }

  protected start(): void {
    super.start();

    setTimeout(() => {
      // 初始化怪物转向
      this._nodeRootMonster.children.forEach((node) => {
        const characterMonster = node.getComponent(CharacterMonster);
        if (characterMonster) {
          characterMonster.idle(RoleSideEnum.left);
          node.active = false;
        }
      });
    }, 1);

    this._characterPlayer.hideUI();

    // 石矶初始化好，下一帧在隐藏
    setTimeout(() => {
      this._characterBoss.node.active = false;
    }, 100);
  }

  onDestroy(): boolean {
    super.onDestroy();
    this._monsterPool.release((item: Node) => {
      item.destroy();
    });
    return true;
  }

  roleDo(type: XiaoyouxiRoleType, behavior: XiaoyouxiRoleBehavior, val?: any): void {
    log.log("XiaoyouxiRoleType " + type + "  behavior " + behavior, val);
    if (type == XiaoyouxiRoleType.ROLE_TYPE_LEAD) {
      if (behavior == XiaoyouxiRoleBehavior.ROLE_IN) {
        log.log("主角进场");
        this.playerIn();
      } else if (behavior == XiaoyouxiRoleBehavior.ROLE_OUT) {
        log.log("主角出场");
        this._characterPlayer.idle();
      } else if (behavior == XiaoyouxiRoleBehavior.ROLE_UI) {
        log.log("主角血条UI");
        this._characterPlayer.showUI();
      } else if (behavior == XiaoyouxiRoleBehavior.ROLE_UPGRADE) {
        log.log("主角升级");
        this._characterPlayer.setLevelSpine(PlayerModule.data.getPlayerInfo().sex, val, () => {
          this._stateCallback(RunState.ACT_END);
        });
      } else if (behavior == XiaoyouxiRoleBehavior.ROLE_RUN) {
        log.log("主角跑向boss");
        this.startHit(val);
      }
    } else if (type == XiaoyouxiRoleType.ROLE_TYPE_DAJI) {
      if (behavior == XiaoyouxiRoleBehavior.ROLE_IN) {
        log.log("妲己进场");
        this.dajiIn();
      } else if (behavior == XiaoyouxiRoleBehavior.ROLE_OUT) {
        log.log("妲己出场");
        this.dajiOut();
      } else if (behavior == XiaoyouxiRoleBehavior.ROLE_TALK) {
        log.log("妲己说话");
        this._characterNpcDaJi.talk(
          val,
          () => {
            this._stateCallback(RunState.TALK_END);
          },
          this._nodeCamera.worldPosition
        );
      }
    } else if (type == XiaoyouxiRoleType.ROLE_TYPE_MONSTER) {
      if (behavior == XiaoyouxiRoleBehavior.ROLE_TALK) {
        log.log("怪物说话");
        this.onMonsterTalk(val, () => {
          this._stateCallback(RunState.TALK_END);
        });
      } else if (behavior == XiaoyouxiRoleBehavior.ROLE_IN) {
        log.log("怪物进场");
        this.onMonsterBorn();
      } else if (behavior == XiaoyouxiRoleBehavior.ROLE_RUN) {
        log.log("怪物跑向主角");
        this.monsterRun();
      }
    } else if (type == XiaoyouxiRoleType.ROLE_TYPE_BOSS) {
      if (behavior == XiaoyouxiRoleBehavior.ROLE_IN) {
        log.log("boss进场");
        // let func = () => {
        //   this.bossIn(func);
        // };
        this.bossIn(() => {
          // func();
          this._stateCallback(RunState.ACT_END);
        });
      } else if (behavior == XiaoyouxiRoleBehavior.ROLE_TALK) {
        log.log("boss说话");
        this._characterBoss.talk(
          val,
          () => {
            this._stateCallback(RunState.TALK_END);
          },
          this._nodeCamera.worldPosition
        );
      } else if (behavior == XiaoyouxiRoleBehavior.ROLE_OUT) {
        log.log("BOSS离场");
        this._characterBoss.disappear(() => {
          // this._stateCallback(RunState.ACT_END);
        });
      }
    }
  }

  // 初始化开始
  onStart(callback: (state: RunState, val?: any) => void, any): void {
    this._stateCallback = callback;
  }

  /**
   * 主角进场
   */
  async playerIn(): Promise<void> {
    while (!this._characterPlayer.isReady) {
      await Sleep(0.1);
    }

    this._characterPlayer.goTo(RoleStatusEnum.run, v3(-200, this._characterPlayer.node.position.y, 0), () => {
      this._stateCallback(RunState.ACT_END);
    });
  }

  /**
   * 妲己进场
   */
  async dajiIn(): Promise<void> {
    this._dajiFollow = false;

    while (!this._characterNpcDaJi?.isReady) {
      await Sleep(0.1);
    }

    // 设置激活
    this._characterNpcDaJi.node.active = true;

    // 向右
    this._characterNpcDaJi.idle(RoleSideEnum.right);

    // 跟随状态
    this._dajiFollow = false;

    // 设置出场位置
    let y = this._characterNpcDaJi.node.position.y;

    tween(this._characterNpcDaJi.node)
      .by(
        0.5,
        {},
        {
          onUpdate: (target?: Node, ratio?: number) => {
            target.setPosition(this._characterPlayer.node.position.x - 400 + 300 * ratio, y);
          },
        }
      )
      .call(() => {
        // 设置跟随状态
        this._dajiFollow = true;
        this._stateCallback(RunState.ACT_END);
      })
      .start();
  }

  /**
   * 妲己出场
   */
  async dajiOut(): Promise<void> {
    this._dajiFollow = false;
    // 设置出场位置
    let y = this._characterNpcDaJi.node.position.y;
    let spaceX = this._characterPlayer.node.position.x - this._characterNpcDaJi.node.position.x;
    let dtX = 400 - spaceX;

    tween(this._characterNpcDaJi.node)
      .by(
        0.5,
        {},
        {
          onUpdate: (target?: Node, ratio?: number) => {
            target.setPosition(this._characterPlayer.node.position.x - spaceX - dtX * ratio, y);
          },
        }
      )
      .call(() => {
        // 设置跟随状态
        this._characterNpcDaJi.node.active = false;
        this._stateCallback(RunState.ACT_END);
      })
      .start();
  }

  /**
   * boss进场，不在镜头内会跟随主角
   */
  bossIn(cbFunc: Function): void {
    this._characterBoss.node.active = true;
    if (this._nodeCamera.position.x > this._characterBoss.node.position.x + 750) {
      this._characterBoss.node.setPosition(this._nodeCamera.position.x, this._characterBoss.node.position.y);
    }

    this._characterBoss.idle(RoleSideEnum.left);
    this._characterBoss.appear(() => {
      AudioMgr.instance.playEffect(1665);
      cbFunc();
    });
  }

  /**
   * 怪物跑向主角，中途释放，只是表示一下
   */
  monsterRun() {
    let seconds = 3;
    this._nodeRootMonster.children.forEach((node, index) => {
      let characterMonster = node.getComponent(CharacterMonster);
      if (characterMonster) {
        characterMonster.showSoundSpine();
        AudioMgr.instance.playEffect(1666);
        characterMonster.goTo(
          RoleStatusEnum.run,
          v3(node.position.x - seconds * characterMonster.attr.runSpeed, node.position.y, 0),
          () => {
            this._monsterPool.recycle(node);
            node.active = false;
            this._characterBoss.node.active = false;
          }
        );
      }
    });

    setTimeout(() => {
      this._stateCallback(RunState.ACT_END);
    }, seconds * 1000);
  }

  protected update(dt: number): void {
    if (this._playerBattle) {
      if (this._monsterList.length > 0) {
        this.checkMonsterDie();
        if (this._monsterList.length == 0) {
          // this._playerBattle = false;
          this._stateCallback(RunState.ACT_END);
          this._characterPlayer.walk(RoleSideEnum.right);
        }
      }
      this._stateCallback(RunState.ROLE_MOVE, this._characterPlayer.node.getWorldPosition());
    }

    if (this._dajiFollow) {
      this._characterNpcDaJi.node.setPosition(
        this._characterPlayer.node.position.x - 100,
        this._characterNpcDaJi.node.position.y
      );
    }

    this._cameraPosX = this._nodeCamera.worldPosition.x;
    this.updateFrame(dt, this._cameraPosX);
  }

  /**
   *
   * @param dt 帧间隔
   * @param cameraOffsetX 摄像机x轴坐标
   */
  updateFrame(dt: number, cameraOffsetX: number): void {
    // 天空移动
    // 相对距离，越大移动越慢
    let skyPosX = (cameraOffsetX / 4) * 3;
    let space = cameraOffsetX - skyPosX;
    let count = Math.floor(space / 2048);
    skyPosX = skyPosX + count * 2048;
    this._nodeSky.setPosition(skyPosX, this._nodeSky.position.y);

    // 背景移动
    this.bgMove(cameraOffsetX, this._nodeGround);
    this.bgMove(cameraOffsetX, this._nodeFront);
  }

  /**
   * 地板位移
   *
   * @param cameraOffsetX 摄像机x坐标
   * @param nodeMove 要移动的节点
   */
  private bgMove(cameraOffsetX: number, nodeMove: Node): void {
    let x = Math.floor((cameraOffsetX + 1024) / 2048) * 2048;
    if (x != nodeMove.position.x) {
      nodeMove.setPosition(x, nodeMove.position.y);
    }
  }

  async onMonsterBorn() {
    for (let i = 0; i < this._nodeRootMonster.children.length; i++) {
      let node = this._nodeRootMonster.children[i];
      await Sleep(0.25);
      node.active = true;
      const characterMonster = node.getComponent(CharacterMonster);
      if (characterMonster) {
        characterMonster.born();
      }
    }

    tween(this._nodeRootMonster)
      .delay(1)
      .call(() => {
        this._stateCallback(RunState.ACT_END);
      })
      .start();
  }

  /**
   * 初始的几个小怪说话，
   *
   * @param word 说话内容
   * @param endFunc 说完回调
   */
  async onMonsterTalk(word: string, endFunc: Function) {
    if (!this._monsterDialogBox) {
      let pbDialogBox = await this.assetMgr.loadPrefabSync(BundleEnum.BUNDLE_PUB, "prefab/DialogBox");
      let nodeDialogBox = instantiate(pbDialogBox);
      TipsMgr.showTipNode(nodeDialogBox);
      this._monsterDialogBox = nodeDialogBox.getComponent(DialogBox);
    }

    this._monsterDialogBox.node.active = true;

    let worldPos = this._nodeRootMonster.getChildByName("node_talk_pos").getWorldPosition();
    // 摄像机位置
    worldPos.subtract(this._nodeCamera.worldPosition);

    this._monsterDialogBox.node.setPosition(worldPos);

    this._monsterDialogBox.typeWord(word, DialogSideEnum.left);
    this._monsterDialogBox.registerEndCallBack(() => {
      this._monsterDialogBox.node.active = false;
      endFunc && endFunc();
    });
  }

  /**
   *
   * @param hpMax 最大血量
   */
  async startHit(hpMax: number) {
    this._playerBattle = true;

    // 创建怪物
    const configMonsterMatrix = FightModule.data.getConfigMonsterMatrix(40);
    for (let idx in configMonsterMatrix.positionList) {
      await this.createMonster(
        v3(configMonsterMatrix.positionList[idx][0], configMonsterMatrix.positionList[idx][1]),
        configMonsterMatrix.skinId[idx]
      );
    }

    // 怪物初始化好在向左跑
    setTimeout(() => {
      // 怪物开始跑
      this._monsterList.forEach((node) => {
        node.getComponent(CharacterMonster).run(RoleSideEnum.left);
      });
    }, 100);

    // 主角开始冲击
    this._characterPlayer.run(RoleSideEnum.right);

    // 设置血量最大值
    this._characterPlayer.setHpMax(hpMax);

    // 初始血量同步
    this._characterPlayer.setHp(hpMax);
  }

  /**
   * 创建怪物
   * @param pos 怪物位置
   * @param skinId 怪物形象
   */
  private async createMonster(pos: Vec3, skinId: number) {
    if (!isValid(this.node)) {
      return;
    }
    pos = pos.add(this._characterPlayer.node.position);

    // 随机怪物形象
    if (!skinId || skinId < 1 || skinId > 7) {
      skinId = randomRangeInt(1, 8);
    }

    // 从对象池取出怪物
    let nodeMonster = await this._monsterPool.getOne();
    nodeMonster.active = true;
    const characterMonster = nodeMonster.getComponent(CharacterMonster);
    characterMonster.initMonster(`xiaoguai${skinId}`);

    // 加入到父节点
    this.node.addChild(nodeMonster);

    // 设置初始位置
    nodeMonster.setPosition(pos);
    characterMonster.idle(RoleSideEnum.left);

    // 统一列表管理
    this._monsterList.push(nodeMonster);
  }

  /**
   * 检查怪物是否要死
   */
  private checkMonsterDie(): void {
    // 先过滤一下怪物列表
    this._monsterList = this._monsterList.filter((node) => {
      return node.active;
    });

    this._monsterList.forEach((node, index) => {
      let characterMonster = node.getComponent(CharacterMonster);
      if (characterMonster.status === RoleStatusEnum.die) {
        return;
      }

      if (node.position.x < this._characterPlayer.node.position.x + 220) {
        this._characterPlayer.attack();

        // 消耗气运
        QiyunXiaoyouxiData.instance.consumeQiyun();

        node.setPosition(this._characterPlayer.node.position.x + 220, node.position.y);
        characterMonster.die(() => {
          node.active = false;
          this._monsterPool.recycle(node);
        });
      }
    });
  }
}
