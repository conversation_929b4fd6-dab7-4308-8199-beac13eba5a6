import MsgEnum from "../../game/event/MsgEnum";
import { IConfigTask, IConfigTaskMain } from "../../game/JsonDefine";
import { JsonMgr } from "../../game/mgr/JsonMgr";
import { MainTaskMessage } from "../../game/net/protocol/MainTask";
import MsgMgr from "../../lib/event/MsgMgr";

export enum TaskType {
  点击女娲_1 = 1,
  关卡通关_2 = 2,
  战将总等级_3 = 3,
  战将升级_4 = 4,
  建造x_5 = 5,
  升级x_6 = 6,
  建筑总等级_7 = 7,
  建筑招募_8 = 8,
  累计建筑总招募_9 = 9,
  身份提升_10 = 10,
  升级女娲_11 = 11,
  仙友累计赠送_12 = 12,
  仙友累计互动_13 = 13,
  弟子累计获得_14 = 14,
  弟子累计培养_15 = 15,
  演武场挑战_16 = 16,
  神器升级_17 = 17,
  灵兽获取_18 = 18,
  灵兽技能生效_19 = 19,
  仙友据点技能提升_20 = 20,
  游历_21 = 21,
  玲珑宝鼎_22 = 22,
  古境狩猎_23 = 23,
  古境挑战BOSS_24 = 24,
  弟子累计出师_25 = 25,
  弟子累计结伴_26 = 26,
  灵兽升级_27 = 27,
  灵兽技能洗炼_28 = 28,
  仙友战将技能升级_29 = 29,
  仙友互动_30 = 30,
  仙友赠送_31 = 31,
  仙友技能提升_32 = 32,
  弟子培养_33 = 33,
  战将升级_34 = 34,
  弟子出师_35 = 35,
  弟子结伴_36 = 36,
  建筑招募_37 = 37,
  建筑升级_38 = 38,
  挑战战盟BOSS_39 = 39,
  参与战盟捐献_40 = 40,
  洞天采集_41 = 41,
  洞天抢夺_42 = 42,
  洞天探寻_43 = 43,
  驿站出发_44 = 44,
  建造地图大装饰_63 = 63,
  点击女娲_64 = 64,
  击退贼人_65 = 65,
  三界小家_66 = 66,
}

export class MainTaskData {
  // 消息定义
  private _mainTaskMsg: MainTaskMessage;

  public get mainTaskMsg() {
    return this._mainTaskMsg;
  }

  public set mainTaskMsg(value: MainTaskMessage) {
    this._mainTaskMsg = value;

    MsgMgr.emit(MsgEnum.ON_MAINTASK_UPDATE);
  }

  public getConfigTaskMain(taskTimelineId: number): IConfigTaskMain {
    return JsonMgr.instance.jsonList.c_taskMain[taskTimelineId];
  }
  public getConfigTask(taskTimelineId: number): IConfigTask {
    return JsonMgr.instance.jsonList.c_task[taskTimelineId];
  }
}
