{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "7773659b-672e-4a31-a4bd-d4d1f2db995b", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "7773659b-672e-4a31-a4bd-d4d1f2db995b@6c48a", "displayName": "bg_9g_zhanjiang_tujian_xia", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "7773659b-672e-4a31-a4bd-d4d1f2db995b", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "7773659b-672e-4a31-a4bd-d4d1f2db995b@f9941", "displayName": "bg_9g_zhanjiang_tujian_xia", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 323, "height": 19, "rawWidth": 323, "rawHeight": 19, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-161.5, -9.5, 0, 161.5, -9.5, 0, -161.5, 9.5, 0, 161.5, 9.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 19, 323, 19, 0, 0, 323, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-161.5, -9.5, 0], "maxPos": [161.5, 9.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "7773659b-672e-4a31-a4bd-d4d1f2db995b@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "7773659b-672e-4a31-a4bd-d4d1f2db995b@6c48a"}}