import { Label } from "cc";
import { _decorator, Component, Node } from "cc";
import { tween } from "cc";
import { v3 } from "cc";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { MessageComponent } from "../../../../platform/src/core/ui/components/MessageComponent";
const { ccclass, property } = _decorator;

@ccclass("TopFractureHelpTips")
export class TopFractureHelpTips extends BaseCtrl {
  @property(Label)
  private lblTask: Label;

  private _playerName: string = "";

  init(args: { playerName: string }): void {
    super.init(args);
    this._playerName = args.playerName;
  }

  start() {
    super.start();
    // todo:lblTask
    this.lblTask.getComponent(MessageComponent).args = [this._playerName];

    // 播放出现动画
    tween(this.node.getChildByName("bg"))
      .set({ scale: v3(0.5, 0.5, 1) })
      .to(0.3, { scale: v3(1, 1, 1) }, { easing: "backOut" })
      .start();

    tween(this.node.getChildByName("layout"))
      .set({ scale: v3(0.25, 0.25, 1) })
      .to(0.5, { scale: v3(1, 1, 1) }, { easing: "backOut" })
      .delay(3)
      .call(() => {
        this.closeBack();
      })
      .start();
  }
}
