import MsgEnum from "../../game/event/MsgEnum";
import { ApiHandler } from "../../game/mgr/ApiHandler";
import { FarmActionCmd } from "../../game/net/cmd/CmdData";
import { LongValue } from "../../game/net/protocol/ExternalMessage";
import { FarmFundBuyResponse } from "../../game/net/protocol/Farm";
import MsgMgr from "../../lib/event/MsgMgr";
import { FarmEvent } from "./FarmEvent";
import { FarmModule } from "./FarmModule";

export class FarmSubScriber {
  private pushUpdateFarmUserIdCallback(data: LongValue) {
    MsgMgr.emit(FarmEvent.OTHER_FARM_REFRESH, data.value);
  }

  private subFarmFundBuy(farmFundBuy: FarmFundBuyResponse) {
    FarmModule.data.farmTrainMessage.fundList[farmFundBuy.fundIndex] = farmFundBuy.farmFundMessage;
    MsgMgr.emit(MsgEnum.FARM_FUND_BUY_SUCCESS, farmFundBuy);
  }

  public register() {
    //订阅服务器消息
    ApiHandler.instance.subscribe(LongValue, FarmActionCmd.pushUpdateFarmUserId, this.pushUpdateFarmUserIdCallback);

    // 基金支付成功后，推送购买的基金索引和更新后的信息
    ApiHandler.instance.subscribe(FarmFundBuyResponse, FarmActionCmd.SubFarmFundBuy, this.subFarmFundBuy);
  }

  public unRegister() {
    //取消订阅服务器消息
    ApiHandler.instance.unSubscribe(FarmActionCmd.pushUpdateFarmUserId, this.pushUpdateFarmUserIdCallback);

    ApiHandler.instance.unSubscribe(FarmActionCmd.SubFarmFundBuy, this.subFarmFundBuy);
  }
}
