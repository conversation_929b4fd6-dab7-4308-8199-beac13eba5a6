import { GameData } from "../../game/GameData";
import data from "../../lib/data/data";
import { MainRankApi } from "./MainRankApi";
import { MainRankConfig } from "./MainRankConfig";
import { MainRankData } from "./MainRankData";
import { MainRankRoute } from "./MainRankRoute";
import { MainRankService } from "./MainRankService";
import { MainRankSubscriber } from "./MainRankSubscriber";
import { MainRankViewModel } from "./MainRankViewModel";

export class MainRankModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): MainRankModule {
    if (!GameData.instance.MainRankModule) {
      GameData.instance.MainRankModule = new MainRankModule();
    }
    return GameData.instance.MainRankModule;
  }
  private _data = new MainRankData();
  private _api = new MainRankApi();
  private _service = new MainRankService();
  private _subscriber = new MainRankSubscriber();
  private _route = new MainRankRoute();
  private _viewModel = new MainRankViewModel();
  private _config = new MainRankConfig();

  public static get data() {
    return this.instance._data;
  }

  public static get api() {
    return this.instance._api;
  }

  public static get config() {
    return this.instance._config;
  }

  public static get service() {
    return this.instance._service;
  }

  public static get viewModel() {
    return this.instance._viewModel;
  }

  public init(data?: any, completedCallback?: Function) {
    if (this._subscriber) {
      this._subscriber.unRegister();
    }
    this._data = new MainRankData();
    this._api = new MainRankApi();
    this._service = new MainRankService();
    this._subscriber = new MainRankSubscriber();
    this._route = new MainRankRoute();
    this._viewModel = new MainRankViewModel();
    this._config = new MainRankConfig();

    // 模块初始化
    this._subscriber.register();
    this._route.init();
    this._api.rankRewardInfo(() => {
      completedCallback && completedCallback();
      this._service.init();
    });
  }

  protected saveKey(): string {
    return this.constructor.name;
  }
}
