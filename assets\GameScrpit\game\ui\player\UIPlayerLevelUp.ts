import {
  _decorator,
  Color,
  Input,
  instantiate,
  isValid,
  Label,
  Node,
  Prefab,
  RichText,
  ScrollView,
  sp,
  Sprite,
  UITransform,
  v3,
  Vec2,
} from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { JsonMgr } from "../../mgr/JsonMgr";
import Formate from "../../../lib/utils/Formate";
import TipMgr from "../../../lib/tips/TipMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
import ToolExt from "../../common/ToolExt";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { PlayerRouteName, PublicRouteName } from "../../../module/player/PlayerConstant";
import ResMgr from "../../../lib/common/ResMgr";
import { SkinMessage } from "../../net/protocol/Player";
import { BadgeMgr, BadgeType } from "../../mgr/BadgeMgr";
import { ConfirmMsg } from "../UICostConfirm";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { TipAttrAdd } from "../../../lib/common/TipAttrAdd";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { PlayerAudioName } from "../../../module/player/PlayerConfig";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "../../../lib/utils/FmUtils";

const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

/**获取系统开启表里的等级开启功能 */
function getSystemOpendDbLv(lv: number) {
  let c_systemOpen = JsonMgr.instance.jsonList.c_systemOpen;
  let list = Object.keys(c_systemOpen);

  let arr = [];
  for (let i = 0; i < list.length; i++) {
    let info = c_systemOpen[list[i]];
    if (info.openList[0] == 1 && info.openList[1] == lv && info.show == 1) {
      arr.push(info);
    }
  }
  return arr;
}

enum MAIN_TAB {
  BTN_GO_LV_MAIN = "btn_go_lv_main",
  BTN_GO_SKIN_MAIN = "btn_go_skin_main",
  BTN_GO_ADORN_MAIN = "btn_go_adorn_main",
}

enum ADRON_TAB_ENUM {
  HEAD = "head",
  HEAD_FRAME = "head_frame",
  TITLE_NAME = "title_name",
  BUBBLE = "bubble",
}

enum SKIN_TAB_ENUM {
  IDENTITY = "identity",
  ACTIVITY = "activity",
  SHENGDIAN = "shengDian",
}

enum SKIN_SHOW_TYPE_ENUM {
  PORTRAIT = "portrait",
  MODEL = "model",
}

const wei_xuan_zhong = "bg_yeqian_weixuanzhong";
const xuan_zhong = "bg_yeqian_xuanzhong";

const blueColor = "#3067B4";
const brownColor = "#B76E38";

enum MAIN_TAB {
  PlayerLvMain = "PlayerLvMain",
}

@ccclass("UIPlayerLevelUp")
export class UIPlayerLevelUp extends UINode {
  // protected prefab(): string {
  //   return `${BundleEnum.BUNDLE_G_PLAYER}?prefab/ui/UIPlayerLevelUp`;
  // }

  // private _cur_main_key: MAIN_TAB;

  // public init(args: any): void {
  //   super.init(args);
  //   if (args.tab) {
  //     this._cur_main_key = args.tab;
  //   } else {
  //     this._cur_main_key = MAIN_TAB.PLAYER_LV_MAIN;
  //   }
  // }

  // protected onEvtShow(): void {
  //  // this.loadShowMain();
  // }

  // private async loadShowMain() {
  //   this.getNode("list_main").children.forEach((node) => {
  //     node.active = false;
  //   });

  //   let key = this._cur_main_key;
  //   let node = this.getNode("list_main").getChildByName(key);
  //   if (node) {
  //     node.active = true;
  //     return;
  //   }

  //   let main = await this.assetMgr.loadPrefabSync(BundleEnum.BUNDLE_G_PLAYER, `prefab/player_main/${key}`);
  //   node = instantiate(main);
  //   this.getNode("list_main").addChild(node);
  //   node.walk((child) => (child.layer = this.node.layer));
  // }

  protected _openAct: boolean = true; //打开动作
  /**底部大标签 */
  private _curMain: MAIN_TAB = MAIN_TAB.BTN_GO_LV_MAIN;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_PLAYER}?prefab/ui/UIPlayerLevelUp`;
  }
  protected dependOn(): BundleEnum[] {
    return [BundleEnum.BUNDLE_COMMON_HERO_HALF];
  }
  private mainMap: Map<MAIN_TAB, string> = new Map([
    [MAIN_TAB.BTN_GO_LV_MAIN, "node_lv_main"],
    [MAIN_TAB.BTN_GO_SKIN_MAIN, "node_skin_main"],
    [MAIN_TAB.BTN_GO_ADORN_MAIN, "node_adorn_main"],
  ]);
  private _nodeList: string[] = [
    "lay_level1",
    "lay_level2",
    "lab_left_lay",
    "icon_jiantou",
    "lab_right_lay",
    "lab_look_lay",
    "bottomLayer1",
    "bottomLayer2",
    "bottomLayer3",
  ];
  /**展示的当前等级 --- 可以左右变动查看 */
  private _curShowLv: number = null;
  /**历史奖励领取过 */
  private _isAwardLast: boolean = false;
  protected onRegEvent(): void {
    MsgMgr.on(MsgEnum.ON_EXIST_ITEM_CHANGE, this.upItem, this);
    //MsgMgr.on(PlayerMsgEnum.SKIN_UPDATA, this.setSkinYuLan, this);
  }
  protected onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_EXIST_ITEM_CHANGE, this.upItem, this);
    //MsgMgr.off(PlayerMsgEnum.SKIN_UPDATA, this.setSkinYuLan, this);
  }
  upItem() {
    for (let i = 0; i < this._skin_node_list.length; i++) {
      let node = this._skin_node_list[i];
      let data = node["skinInfo"];
      this.setActiveState(node, data.id);
    }
  }
  protected onEvtShow(): void {
    this.changeMain();
    this.getNode("zj_sweep_light").active = false;
    // 红点
    BadgeMgr.instance.setBadgeId(this.getNode("btn_go_lv_main"), BadgeType.UITerritory.btn_shengji.btn_go_lv_main.id);
    BadgeMgr.instance.setBadgeId(
      this.getNode("btn_go_skin_main"),
      BadgeType.UITerritory.btn_shengji.btn_go_skin_main.id
    );
    BadgeMgr.instance.setBadgeId(
      this.getNode("btn_post_lv"),
      BadgeType.UITerritory.btn_shengji.btn_go_lv_main.btn_post_lv.id
    );
    BadgeMgr.instance.setBadgeId(
      this.getNode("btn_skin_identity"),
      BadgeType.UITerritory.btn_shengji.btn_go_skin_main.btn_skin_identity.id
    );
    BadgeMgr.instance.setBadgeId(
      this.getNode("btn_skin_activity"),
      BadgeType.UITerritory.btn_shengji.btn_go_skin_main.btn_skin_activity.id
    );
  }
  private changeMain() {
    this.changeBtnState();
    this.changeTitle();
    switch (this._curMain) {
      case MAIN_TAB.BTN_GO_LV_MAIN:
        this.lvMain();
        break;
      case MAIN_TAB.BTN_GO_SKIN_MAIN:
        this.skinMain();
        break;
      case MAIN_TAB.BTN_GO_ADORN_MAIN:
        this.adomMain();
        break;
    }
  }
  private changeBtnState() {
    switch (this._curMain) {
      case MAIN_TAB.BTN_GO_LV_MAIN:
        this.lvChange();
        break;
      case MAIN_TAB.BTN_GO_SKIN_MAIN:
        this.skinChange();
        break;
      case MAIN_TAB.BTN_GO_ADORN_MAIN:
        this.adomChange();
        break;
    }
  }
  private changeTitle() {
    return;
    switch (this._curMain) {
      case MAIN_TAB.BTN_GO_LV_MAIN:
        this.getNode("lab_title").getComponent(Label).string = "升 级";
        break;
      case MAIN_TAB.BTN_GO_SKIN_MAIN:
        this.getNode("lab_title").getComponent(Label).string = "换 装";
        break;
      case MAIN_TAB.BTN_GO_ADORN_MAIN:
        this.getNode("lab_title").getComponent(Label).string = "装 饰";
        break;
    }
  }
  private lvMain() {
    // this._curShowLv = PlayerModule.data.getPlayerInfo().level;
    // this.showLvMainIsState();
    // this._curShowLv = PlayerModule.data.getPlayerInfo().level + 1;
    // this.showLvMainIsState();
  }
  private skinMain() {
    this.showSkinMainIsState();
  }
  private adomMain() {
    this.showAdronMainIsState();
  }
  private lvChange() {
    this.changeBtnSpr(this.getNode("btn_go_lv_main"), xuan_zhong);
    this.changeBtnLabColor(this.getNode("btn_go_lv_main").getChildByName("Label").getComponent(Label), brownColor);
    this.changeBtnSpr(this.getNode("btn_go_skin_main"), wei_xuan_zhong);
    this.changeBtnLabColor(this.getNode("btn_go_skin_main").getChildByName("Label").getComponent(Label), blueColor);
    this.changeBtnSpr(this.getNode("btn_go_adorn_main"), wei_xuan_zhong);
    this.changeBtnLabColor(this.getNode("btn_go_adorn_main").getChildByName("Label").getComponent(Label), blueColor);
    this.mainMap.forEach((val, key) => {
      if (this.getNode(val)) {
        this.getNode(val).active = false;
      }
    });
    this.getNode("node_lv_main").active = true;
  }
  private skinChange() {
    this.changeBtnSpr(this.getNode("btn_go_lv_main"), wei_xuan_zhong);
    this.changeBtnLabColor(this.getNode("btn_go_lv_main").getChildByName("Label").getComponent(Label), blueColor);
    this.changeBtnSpr(this.getNode("btn_go_skin_main"), xuan_zhong);
    this.changeBtnLabColor(this.getNode("btn_go_skin_main").getChildByName("Label").getComponent(Label), brownColor);
    this.changeBtnSpr(this.getNode("btn_go_adorn_main"), wei_xuan_zhong);
    this.changeBtnLabColor(this.getNode("btn_go_adorn_main").getChildByName("Label").getComponent(Label), blueColor);
    this.mainMap.forEach((val, key) => {
      if (this.getNode(val)) {
        this.getNode(val).active = false;
      }
    });
    this.getNode("node_skin_main").active = true;
  }
  private adomChange() {
    this.changeBtnSpr(this.getNode("btn_go_lv_main"), wei_xuan_zhong);
    this.changeBtnLabColor(this.getNode("btn_go_lv_main").getChildByName("Label").getComponent(Label), blueColor);
    this.changeBtnSpr(this.getNode("btn_go_skin_main"), wei_xuan_zhong);
    this.changeBtnLabColor(this.getNode("btn_go_skin_main").getChildByName("Label").getComponent(Label), blueColor);
    this.changeBtnSpr(this.getNode("btn_go_adorn_main"), xuan_zhong);
    this.changeBtnLabColor(this.getNode("btn_go_adorn_main").getChildByName("Label").getComponent(Label), brownColor);
    this.mainMap.forEach((val, key) => {
      if (this.getNode(val)) {
        this.getNode(val).active = false;
      }
    });
    this.getNode("node_adorn_main").active = true;
  }
  private changeBtnSpr(node: Node, sprKey: string) {
    //assets/bundle_common_ui/atlas_imgs/bg_yeqian_weixuanzhong.png/spriteFrame
    db: ResMgr.setSpriteFrame(BundleEnum.BUNDLE_COMMON_UI, `atlas_imgs/${sprKey}`, node.getComponent(Sprite));
  }
  private changeBtnLabColor(lab: Label, colorKye: string) {
    lab.outlineColor = new Color(colorKye);
  }
  private on_click_btn_go_lv_main() {
    AudioMgr.instance.playEffect(PlayerAudioName.Effect.点击换装内页签);
    if (this._curMain == MAIN_TAB.BTN_GO_LV_MAIN) {
      return;
    }
    this._curMain = MAIN_TAB.BTN_GO_LV_MAIN;
    this.changeMain();
  }
  private on_click_btn_go_skin_main() {
    AudioMgr.instance.playEffect(PlayerAudioName.Effect.点击换装内页签);
    if (this._curMain == MAIN_TAB.BTN_GO_SKIN_MAIN) {
      return;
    }
    this._curMain = MAIN_TAB.BTN_GO_SKIN_MAIN;
    this.changeMain();
  }
  private on_click_btn_go_adorn_main() {
    AudioMgr.instance.playEffect(PlayerAudioName.Effect.点击换装内页签);
    if (this._curMain == MAIN_TAB.BTN_GO_ADORN_MAIN) {
      return;
    }
    this._curMain = MAIN_TAB.BTN_GO_ADORN_MAIN;
    this.changeMain();
  }
  // //升级====================================================================================================================================================================================================================================
  private showLvMainIsState() {
    // this._nodeList.forEach((val) => {
    //   this.getNode(val).active = false;
    // });
    // this.setSkinYuLan();
    // let playerLv = PlayerModule.data.getPlayerInfo().level;
    // let maxLv = PlayerModule.data.getConfigLeaderData(this._curShowLv).maxLv;
    // if (this._curShowLv >= maxLv && playerLv >= maxLv) {
    //   this.setMaxLvMain();
    // } else if (this._curShowLv == playerLv + 1) {
    //   //正常展示下一级
    //   this.setCurLvMain();
    // } else if (this._curShowLv <= playerLv) {
    //   //查看以往的等级
    //   this.setLastMain();
    // } else if (this._curShowLv > playerLv) {
    //   //查看未来等级
    //   this.setNextMain();
    // }
    // if (this._curShowLv >= maxLv && playerLv >= maxLv) {
    //   // 达到最大等级
    //   this.setMaxLvMain();
    // } else if (this._curShowLv == playerLv) {
    //   //正常展示下一级
    //   this.setCurLvMain();
    // } else if (this._curShowLv < playerLv) {
    //   //查看以往的等级
    //   this.setLastMain();
    // } else if (this._curShowLv > playerLv) {
    //   //查看未来等级
    //   this.setNextMain();
    // }
  }
  // private setMaxLvMain() {
  //   this.getNode("lay_level2").active = true;
  //   this.getNode("lab_look_lay").active = true;
  //   this.getNode("bottomLayer2").active = true;
  //   let c_leader_info = PlayerModule.data.getConfigLeaderData(this._curShowLv);
  //   if (!c_leader_info) {
  //     log.error("没有这个级配置====", this._curShowLv);
  //     return;
  //   }
  //   this.getNode("spr_look_level_bg").getChildByName("lab_lv").getComponent(Label).string = c_leader_info.name;
  //   this.getNode("spr_look_level_bg").getChildByName("lab_lv_jingjie").getComponent(RichText).string =
  //     c_leader_info.jingjie;
  //   let key1 = `每日奖励仙玉：${Formate.format(c_leader_info.dayReward)}`;
  //   key1 += `\n互动次数上限：${Formate.format(c_leader_info.chatMax)}`;
  //   key1 += `\n互动恢复时间：${Formate.format(c_leader_info.chatTime)}`;
  //   key1 += `\n弟子体力上限：${Formate.format(c_leader_info.trainMax)}`;
  //   key1 += `\n培养弟子获得阅历：${Formate.format(c_leader_info.trainReward)}`;
  //   this.getNode("lab_look_up").getComponent(Label).string = key1;
  //   /**创建升级奖励 */
  //   this.getNode("lay_upLv_award").destroyAllChildren();
  //   for (let i = 0; i < c_leader_info.unlockList.length; i++) {
  //     let info = c_leader_info.unlockList[i];
  //     let node = ToolExt.clone(this.getNode("Item"), this);
  //     this.getNode("lay_upLv_award").addChild(node);
  //     node.active = true;
  //     FmUtils.setItemNode(node, info[0], info[1]);
  //     let c_item_info = JsonMgr.instance.getConfigItem(info[0]);
  //     node["lbl_name"].getComponent(Label).string = c_item_info.name;
  //     this.setUnItemState(node, true);
  //   }
  //   /**我的功德 */
  //   let merit = PlayerModule.data.getItemNum(ItemEnum.功德_5);
  //   /**配置所需的功德 */
  //   let db_virtue = c_leader_info.virtue;
  //   this.getNode("lbl_merit").getComponent(Label).string = `${Formate.format(merit)}/${Formate.format(db_virtue)}`;
  //   this.getNode("progressBar_merit").getComponent(ProgressBar).progress = merit / db_virtue;
  //   /**我的繁荣度 */
  //   let bloom = PlayerModule.data.playerBattleAttrResponse.speed;
  //   /**配置所需的繁荣度 */
  //   let db_speed = c_leader_info.speed;
  //   this.getNode("lbl_prosperity").getComponent(Label).string = `${Formate.format(bloom)}/${Formate.format(db_speed)}`;
  //   this.getNode("progressBar_prosperity").getComponent(ProgressBar).progress = bloom / db_speed;
  //   let arr = getSystemOpendDbLv(this._curShowLv);
  //   if (arr.length == 0) {
  //     this.getNode("lab_un").active = false;
  //     return;
  //   }
  //   this.getNode("lab_un").active = true;
  //   let key = "";
  //   for (let i = 0; i < arr.length; i++) {
  //     let info = arr[i];
  //     if (i == 0) {
  //       key += info.name;
  //     } else {
  //       key += `,${info.name}`;
  //     }
  //   }
  //   this.getNode("lab_un").getComponent(Label).string = key;
  // }
  // private setSkinYuLan() {
  //   let db = JsonMgr.instance.jsonList.c_leaderSkin;
  //   let sex = PlayerModule.data.getPlayerInfo().sex;
  //   let dbList = Object.keys(db);
  //   let bool = false;
  //   for (let i = 0; i < dbList.length; i++) {
  //     let info = db[dbList[i]];
  //     if (info.type == 1 && info.sex == sex) {
  //       if (!PlayerModule.data.skinMap[info.id]) {
  //         bool = true;
  //         break;
  //       }
  //     }
  //   }
  //   this.getNode("btn_jin_jie_yu_lan").active = bool;
  // }
  // private setCurLvMain() {
  //   this.getNode("lay_level1").active = true;
  //   this.getNode("lab_left_lay").active = true;
  //   this.getNode("icon_jiantou").active = true;
  //   this.getNode("lab_right_lay").active = true;
  //   this.getNode("bottomLayer1").active = true;
  //   let playerCurLv = this._curShowLv - 1;
  //   let playerUpLv = this._curShowLv;
  //   /**设置当前等级 */
  //   let c_leader_info = PlayerModule.data.getConfigLeaderData(playerCurLv);
  //   if (!c_leader_info) {
  //     log.error("没有这个级配置====", playerCurLv);
  //     return;
  //   }
  //   this.getNode("spr_cur_level_bg").getChildByName("lab_lv").getComponent(Label).string = c_leader_info.name;
  //   this.getNode("spr_cur_level_bg").getChildByName("lab_lv_jingjie").getComponent(RichText).string =
  //     c_leader_info.jingjie;
  //   let key1 = `每日奖励仙玉：${Formate.format(c_leader_info.dayReward)}`;
  //   key1 += `\n互动次数上限：${Formate.format(c_leader_info.chatMax)}`;
  //   key1 += `\n互动恢复时间：${Formate.format(c_leader_info.chatTime)}`;
  //   key1 += `\n弟子体力上限：${Formate.format(c_leader_info.trainMax)}`;
  //   key1 += `\n培养弟子获得阅历：${Formate.format(c_leader_info.trainReward)}`;
  //   this.getNode("lab_cur_up").getComponent(Label).string = key1;
  //   /**设置下一等级 */
  //   c_leader_info = PlayerModule.data.getConfigLeaderData(playerUpLv);
  //   if (!c_leader_info) {
  //     log.error("没有这个下一等级配置====", playerUpLv);
  //     return;
  //   }
  //   let key2 = `每日奖励仙玉：${Formate.format(c_leader_info.dayReward)}`;
  //   key2 += `\n互动次数上限：${Formate.format(c_leader_info.chatMax)}`;
  //   key2 += `\n互动恢复时间：${Formate.format(c_leader_info.chatTime)}`;
  //   key2 += `\n弟子体力上限：${Formate.format(c_leader_info.trainMax)}`;
  //   key2 += `\n培养弟子获得阅历：${Formate.format(c_leader_info.trainReward)}`;
  //   this.getNode("lab_next_up").getComponent(Label).string = key2;
  //   this.getNode("spr_next_level_bg").getChildByName("lab_lv").getComponent(Label).string = c_leader_info.name;
  //   this.getNode("spr_next_level_bg").getChildByName("lab_lv_jingjie").getComponent(RichText).string =
  //     c_leader_info.jingjie;
  //   /**创建升级奖励 */
  //   this.getNode("lay_upLv_award").destroyAllChildren();
  //   c_leader_info = PlayerModule.data.getConfigLeaderData(playerUpLv);
  //   for (let i = 0; i < c_leader_info.unlockList.length; i++) {
  //     let info = c_leader_info.unlockList[i];
  //     let node = ToolExt.clone(this.getNode("Item"), this);
  //     this.getNode("lay_upLv_award").addChild(node);
  //     node.active = true;
  //     FmUtils.setItemNode(node, info[0], info[1]);
  //     let c_item_info = JsonMgr.instance.getConfigItem(info[0]);
  //     node["lbl_name"].getComponent(Label).string = c_item_info.name;
  //     this.setUnItemState(node, false);
  //   }
  //   /**我的功德 */
  //   let merit = PlayerModule.data.getItemNum(ItemEnum.功德_5);
  //   /**配置所需的功德 */
  //   let db_virtue = c_leader_info.virtue;
  //   this.getNode("lbl_merit").getComponent(Label).string = `${Formate.format(merit)}/${Formate.format(db_virtue)}`;
  //   this.getNode("progressBar_merit").getComponent(ProgressBar).progress = merit / db_virtue;
  //   /**我的繁荣度 */
  //   let bloom = PlayerModule.data.playerBattleAttrResponse.speed;
  //   /**配置所需的繁荣度 */
  //   let db_speed = c_leader_info.speed;
  //   this.getNode("lbl_prosperity").getComponent(Label).string = `${Formate.format(bloom)}/${Formate.format(db_speed)}`;
  //   this.getNode("progressBar_prosperity").getComponent(ProgressBar).progress = bloom / db_speed;
  //   let arr = getSystemOpendDbLv(playerUpLv);
  //   if (arr.length == 0) {
  //     this.getNode("lab_un").active = false;
  //     return;
  //   }
  //   this.getNode("lab_un").active = true;
  //   let key = "";
  //   for (let i = 0; i < arr.length; i++) {
  //     let info = arr[i];
  //     if (i == 0) {
  //       key += info.name;
  //     } else {
  //       key += `,${info.name}`;
  //     }
  //   }
  //   this.getNode("lab_un").getComponent(Label).string = key;
  // }
  // private setLastMain() {
  //   this.getNode("lay_level2").active = true;
  //   this.getNode("lab_look_lay").active = true;
  //   this.getNode("bottomLayer2").active = true;
  //   let c_leader_info = PlayerModule.data.getConfigLeaderData(this._curShowLv);
  //   if (!c_leader_info) {
  //     log.error("没有这个级配置====", this._curShowLv);
  //     return;
  //   }
  //   this.getNode("spr_look_level_bg").getChildByName("lab_lv").getComponent(Label).string = c_leader_info.name;
  //   this.getNode("spr_look_level_bg").getChildByName("lab_lv_jingjie").getComponent(RichText).string =
  //     c_leader_info.jingjie;
  //   let key1 = `每日奖励仙玉：${Formate.format(c_leader_info.dayReward)}`;
  //   key1 += `\n互动次数上限：${Formate.format(c_leader_info.chatMax)}`;
  //   key1 += `\n互动恢复时间：${Formate.format(c_leader_info.chatTime)}`;
  //   key1 += `\n弟子体力上限：${Formate.format(c_leader_info.trainMax)}`;
  //   key1 += `\n培养弟子获得阅历：${Formate.format(c_leader_info.trainReward)}`;
  //   this.getNode("lab_look_up").getComponent(Label).string = key1;
  //   /**创建升级奖励 */
  //   this.getNode("lay_upLv_award").destroyAllChildren();
  //   for (let i = 0; i < c_leader_info.unlockList.length; i++) {
  //     let info = c_leader_info.unlockList[i];
  //     let node = ToolExt.clone(this.getNode("Item"), this);
  //     this.getNode("lay_upLv_award").addChild(node);
  //     node.active = true;
  //     FmUtils.setItemNode(node, info[0], info[1]);
  //     let c_item_info = JsonMgr.instance.getConfigItem(info[0]);
  //     node["lbl_name"].getComponent(Label).string = c_item_info.name;
  //     this.setUnItemState(node, true);
  //   }
  //   /**我的功德 */
  //   let merit = PlayerModule.data.getItemNum(ItemEnum.功德_5);
  //   /**配置所需的功德 */
  //   let db_virtue = c_leader_info.virtue;
  //   this.getNode("lbl_merit").getComponent(Label).string = `${Formate.format(merit)}/${Formate.format(db_virtue)}`;
  //   this.getNode("progressBar_merit").getComponent(ProgressBar).progress = merit / db_virtue;
  //   /**我的繁荣度 */
  //   let bloom = PlayerModule.data.playerBattleAttrResponse.speed;
  //   /**配置所需的繁荣度 */
  //   let db_speed = c_leader_info.speed;
  //   this.getNode("lbl_prosperity").getComponent(Label).string = `${Formate.format(bloom)}/${Formate.format(db_speed)}`;
  //   this.getNode("progressBar_prosperity").getComponent(ProgressBar).progress = bloom / db_speed;
  //   let arr = getSystemOpendDbLv(this._curShowLv);
  //   if (arr.length == 0) {
  //     this.getNode("lab_un").active = false;
  //     return;
  //   }
  //   this.getNode("lab_un").active = true;
  //   let key = "";
  //   for (let i = 0; i < arr.length; i++) {
  //     let info = arr[i];
  //     if (i == 0) {
  //       key += info.name;
  //     } else {
  //       key += `,${info.name}`;
  //     }
  //   }
  //   this.getNode("lab_un").getComponent(Label).string = key;
  // }
  // private setNextMain() {
  //   this.getNode("lay_level2").active = true;
  //   this.getNode("lab_look_lay").active = true;
  //   this.getNode("bottomLayer3").active = true;
  //   let c_leader_info = PlayerModule.data.getConfigLeaderData(this._curShowLv);
  //   if (!c_leader_info) {
  //     log.error("没有这个级配置====", this._curShowLv);
  //     return;
  //   }
  //   this.getNode("spr_look_level_bg").getChildByName("lab_lv").getComponent(Label).string = c_leader_info.name;
  //   this.getNode("spr_look_level_bg").getChildByName("lab_lv_jingjie").getComponent(RichText).string =
  //     c_leader_info.jingjie;
  //   let key1 = `每日奖励仙玉：${Formate.format(c_leader_info.dayReward)}`;
  //   key1 += `\n互动次数上限：${Formate.format(c_leader_info.chatMax)}`;
  //   key1 += `\n互动恢复时间：${Formate.format(c_leader_info.chatTime)}`;
  //   key1 += `\n弟子体力上限：${Formate.format(c_leader_info.trainMax)}`;
  //   key1 += `\n培养弟子获得阅历：${Formate.format(c_leader_info.trainReward)}`;
  //   this.getNode("lab_look_up").getComponent(Label).string = key1;
  //   /**创建升级奖励 */
  //   this.getNode("lay_upLv_award").destroyAllChildren();
  //   for (let i = 0; i < c_leader_info.unlockList.length; i++) {
  //     let info = c_leader_info.unlockList[i];
  //     let node = ToolExt.clone(this.getNode("Item"), this);
  //     this.getNode("lay_upLv_award").addChild(node);
  //     node.active = true;
  //     FmUtils.setItemNode(node, info[0], info[1]);
  //     let c_item_info = JsonMgr.instance.getConfigItem(info[0]);
  //     node["lbl_name"].getComponent(Label).string = c_item_info.name;
  //     this.setUnItemState(node, false);
  //   }
  //   /**我的功德 */
  //   let merit = PlayerModule.data.getItemNum(ItemEnum.功德_5);
  //   /**配置所需的功德 */
  //   let db_virtue = c_leader_info.virtue;
  //   this.getNode("lbl_merit").getComponent(Label).string = `${Formate.format(merit)}/${Formate.format(db_virtue)}`;
  //   this.getNode("progressBar_merit").getComponent(ProgressBar).progress = merit / db_virtue;
  //   /**我的繁荣度 */
  //   let bloom = PlayerModule.data.playerBattleAttrResponse.speed;
  //   /**配置所需的繁荣度 */
  //   let db_speed = c_leader_info.speed;
  //   this.getNode("lbl_prosperity").getComponent(Label).string = `${Formate.format(bloom)}/${Formate.format(db_speed)}`;
  //   this.getNode("progressBar_prosperity").getComponent(ProgressBar).progress = bloom / db_speed;
  //   let arr = getSystemOpendDbLv(this._curShowLv);
  //   if (arr.length == 0) {
  //     this.getNode("lab_un").active = false;
  //     return;
  //   }
  //   this.getNode("lab_un").active = true;
  //   let key = "";
  //   for (let i = 0; i < arr.length; i++) {
  //     let info = arr[i];
  //     if (i == 0) {
  //       key += info.name;
  //     } else {
  //       key += `,${info.name}`;
  //     }
  //   }
  //   this.getNode("lab_un").getComponent(Label).string = key;
  // }
  // private setUnItemState(item: Node, colorBool: boolean) {
  //   let color = new Color(255, 255, 255, 255);
  //   if (colorBool == true) {
  //     color = new Color(100, 100, 100, 255);
  //     item.getChildByName("bg_yiyongyou").active = true;
  //   }
  //   item.getComponentsInChildren(Sprite).forEach((sprite) => {
  //     sprite.color = color;
  //   });
  //   //item.getChildByName("bg_num").getComponent(Sprite).color = color;
  //   item["lbl_num"].getComponent(Label).color = color;
  // }
  // /**设置功德显示 */
  // private setMeritShow(lv: number) {
  //   let c_leader_info = PlayerModule.data.getConfigLeaderData(lv + 1);
  //   /**我的功德 */
  //   let merit = PlayerModule.data.getItemNum(ItemEnum.功德_5);
  //   /**配置所需的功德 */
  //   let db_virtue = c_leader_info.virtue;
  //   this.getNode("lbl_merit").getComponent(Label).string = `${Formate.format(merit)}/${Formate.format(db_virtue)}`;
  //   this.getNode("progressBar_merit").getComponent(ProgressBar).progress = merit / db_virtue;
  // }
  // private on_click_btn_post_lv() {
  //   AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
  //   // 等待返回
  //   TipsMgr.setEnableTouch(false, 3, false);
  //   let c_leader_info = PlayerModule.data.getConfigLeaderData(this._curShowLv);
  //   /**我的繁荣度 */
  //   let bloom = PlayerModule.data.playerBattleAttrResponse.speed;
  //   /**我的功德 */
  //   let merit = PlayerModule.data.getItemNum(ItemEnum.功德_5);
  //   if (bloom < c_leader_info.speed) {
  //     UIMgr.instance.showDialog(
  //       PlayerRouteName.UIItemFetch,
  //       {
  //         itemId: ItemEnum.繁荣度_2,
  //         needNum: c_leader_info.speed,
  //       },
  //       null,
  //       () => {
  //         TipsMgr.setEnableTouch(true);
  //       }
  //     );
  //     return;
  //   }
  //   if (merit < c_leader_info.virtue) {
  //     UIMgr.instance.showDialog(
  //       PlayerRouteName.UIItemFetch,
  //       {
  //         itemId: ItemEnum.功德_5,
  //         needNum: c_leader_info.virtue,
  //       },
  //       null,
  //       () => {
  //         TipsMgr.setEnableTouch(true);
  //       }
  //     );
  //     return;
  //   }
  //   PlayerModule.api.levelUp((data: RewardMessage) => {
  //     AudioMgr.instance.playEffect(PlayerAudioName.Effect.升级成功);
  //     this.lvMain();
  //     // 2级时候移除英雄奖励，在新手引导展示
  //     if (PlayerModule.data.getPlayerInfo().level == 2) {
  //       for (let i = 0; i < data.rewardList.length; i += 2) {
  //         if (data[i] === 10102) {
  //           data[i] = 0;
  //           data[i + 1] = 0;
  //         }
  //       }
  //       data.rewardList = data.rewardList.filter((e) => e > 0);
  //     }
  //     TipsMgr.topRouteCtrl.show(RewardRouteEnum.TopPlayerLevelUpRes, {}, () => {
  //       let level = PlayerModule.data.getPlayerInfo().level;
  //       let skin = this.getHasSkin(level);
  //       if (skin) {
  //         data.rewardList.push(skin.id, 1);
  //       }
  //       MsgMgr.emit(MsgEnum.ON_GET_AWARD, data);
  //     });
  //   });
  // }
  // private getHasSkin(level: number): IConfigLeaderSkin {
  //   let db = JsonMgr.instance.jsonList.c_leaderSkin;
  //   let sex = PlayerModule.data.getPlayerInfo().sex;
  //   let dbList = Object.keys(db);
  //   for (let i = 0; i < dbList.length; i++) {
  //     let info = db[dbList[i]];
  //     if (
  //       info.type == 1 &&
  //       info.sex == sex &&
  //       info.unlock.length >= 2 &&
  //       info.unlock[0] == 1 &&
  //       info.unlock[1] == level
  //     ) {
  //       return info;
  //     }
  //   }
  //   return null;
  // }
  // private on_click_btn_left() {
  //   AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
  //   if (this._curShowLv <= 1) {
  //     return;
  //   }
  //   this._curShowLv--;
  //   if (this._curShowLv <= 0) {
  //     this._curShowLv = 1;
  //   }
  //   if (PlayerModule.data.getPlayerInfo().level == 1 && this._curShowLv == 1) {
  //     this._curShowLv = 2;
  //     return;
  //   }
  //   this.showLvMainIsState();
  // }
  // private on_click_btn_right() {
  //   AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
  //   let c_leader = JsonMgr.instance.jsonList.c_leader;
  //   let list = Object.keys(c_leader);
  //   if (this._curShowLv >= list.length) {
  //     return;
  //   }
  //   this._curShowLv++;
  //   if (this._curShowLv > list.length) {
  //     this._curShowLv = list.length;
  //   }
  //   this.showLvMainIsState();
  // }
  // private on_click_btn_jin_jie_yu_lan() {
  //   UIMgr.instance.showDialog(PlayerRouteName.UIPlayerJinJieYuLan);
  // }
  //升级====================================================================================================================================================================================================================================
  //换装====================================================================================================================================================================================================================================
  private _skin_title_tab: SKIN_TAB_ENUM = SKIN_TAB_ENUM.ACTIVITY;
  private _skin_show_type: SKIN_SHOW_TYPE_ENUM = SKIN_SHOW_TYPE_ENUM.PORTRAIT;
  private _skin_listMap: Map<number, any[]> = new Map<number, any[]>();
  private _has_skin_list_map: Map<number, any[]> = new Map<number, any[]>();
  private _skin_node_list: Node[] = [];
  /**选中 */
  private _cur_Pitch_skinId: number = 0;
  /**使用 */
  private _cur_Use_skinId: number = 0;
  private showSkinMainIsState() {
    this.getSkinInfoList();
    this.changeScrollList();
    this.setShowType();
  }
  private setShowType() {
    if (this._skin_show_type == SKIN_SHOW_TYPE_ENUM.PORTRAIT) {
      this.getNode("skin_portrait_mask").active = true;
      this.getNode("skin_model_mask").active = false;
    } else if (this._skin_show_type == SKIN_SHOW_TYPE_ENUM.MODEL) {
      this.getNode("skin_portrait_mask").active = false;
      this.getNode("skin_model_mask").active = true;
    }
  }
  private getSkinInfoList() {
    let db = JsonMgr.instance.jsonList.c_leaderSkin;
    let dbList = Object.keys(db);
    let skinList1 = [];
    let skinList2 = [];
    let skinList3 = [];
    for (let i = 0; i < dbList.length; i++) {
      let data = db[dbList[i]];
      if (data.type == 1 && data.sex == PlayerModule.data.getPlayerInfo().sex) {
        skinList1.push(data);
      } else if (data.type == 2) {
        skinList2.push(data);
      } else if (data.type == 3) {
        skinList3.push(data);
      }
    }
    this._skin_listMap.set(1, skinList1);
    this._skin_listMap.set(2, skinList2);
    this._skin_listMap.set(3, skinList3);
  }
  private initIdentityList() {
    this.getNode("content_identity_skin").destroyAllChildren();
    this._skin_node_list = [];
    this._has_skin_list_map.set(1, []);
    let list = this._skin_listMap.get(1);
    let boolType = false;
    if (this._cur_Pitch_skinId != 0 && JsonMgr.instance.jsonList.c_leaderSkin[this._cur_Pitch_skinId].type == 1) {
      boolType = true;
    }
    for (let i = 0; i < list.length; i++) {
      let data = list[i];
      let spineShowdb = JsonMgr.instance.jsonList.c_spineShow[data.spineId];
      let node = ToolExt.clone(this.getNode("btn_skin_card"), this);
      node.on(Input.EventType.TOUCH_END, ToolExt.tryFunc(this.OnClick_btn_skin_card.bind(this)), this);
      node.name += "_" + data.id;
      this.getNode("content_identity_skin").addChild(node);
      node.active = true;
      node.getChildByName("lab_skinName").getComponent(Label).string = data.name;
      ResMgr.loadImage(spineShowdb.halfRes, node["herohalf"].getComponent(Sprite), this);
      node["skinInfo"] = data;
      if (boolType == false) {
        this._cur_Pitch_skinId = data.id;
        boolType = true;
      }
      let bool = false;
      if (PlayerModule.data.skinMap[data.id]) {
        bool = true;
        let list = this._has_skin_list_map.get(1);
        list.push(data);
        this._has_skin_list_map.set(1, list);
      }
      this.setUnSkinState(node, bool);
      this._skin_node_list.push(node);
      this.setActiveState(node, data.id);
      node["btn_skin_active"]["skinId"] = data.id;
      node["btn_icon_suo"]["skinId"] = data.id;
      node["btn_skin_active"]["card_node"] = node;
    }
  }
  private sortActivity() {
    let list = this._skin_listMap.get(2);
    let arr1 = [];
    let arr2 = [];
    let arr3 = [];
    for (let i = 0; i < list.length; i++) {
      let data = list[i];
      let info = PlayerModule.data.skinMap[data.id];
      if (info && info.level >= 1) {
        arr1.push(data);
      } else if (info && info.level == 0) {
        arr2.push(data);
      } else {
        arr3.push(data);
      }
    }
    let newList = arr1.concat(arr2, arr3);
    this._skin_listMap.set(2, newList);
  }
  private initActivityList() {
    this.getNode("content_activity_skin").destroyAllChildren();
    this._skin_node_list = [];
    this._has_skin_list_map.set(2, []);
    let list = this._skin_listMap.get(2);
    let boolType = false;
    if (this._cur_Pitch_skinId != 0 && JsonMgr.instance.jsonList.c_leaderSkin[this._cur_Pitch_skinId].type == 2) {
      boolType = true;
    }
    for (let i = 0; i < list.length; i++) {
      let data = list[i];
      let spineShowdb = JsonMgr.instance.jsonList.c_spineShow[data.spineId];
      let node = ToolExt.clone(this.getNode("btn_skin_card"), this);
      node.on(Input.EventType.TOUCH_END, ToolExt.tryFunc(this.OnClick_btn_skin_card.bind(this)), this);
      node.name += "_" + data.id;
      this.getNode("content_activity_skin").addChild(node);
      node.active = true;
      node.getChildByName("lab_skinName").getComponent(Label).string = data.name;
      ResMgr.loadImage(spineShowdb.halfRes, node["herohalf"].getComponent(Sprite), this);
      node["skinInfo"] = data;
      if (boolType == false) {
        this._cur_Pitch_skinId = data.id;
        boolType = true;
      }
      let bool = false;
      let info = PlayerModule.data.skinMap[data.id];
      if (info && info.level >= 1) {
        bool = true;
        let list = this._has_skin_list_map.get(2);
        list.push(data);
        this._has_skin_list_map.set(2, list);
      }
      this.setUnSkinState(node, bool);
      this._skin_node_list.push(node);
      this.setActiveState(node, data.id);
      node["btn_skin_active"]["skinId"] = data.id;
      node["btn_icon_suo"]["skinId"] = data.id;
      node["btn_skin_active"]["card_node"] = node;
    }
  }
  private initShengdianList() {
    this.getNode("content_shengdian_skin").destroyAllChildren();
    this._skin_node_list = [];
    this._has_skin_list_map.set(3, []);
    let list = this._skin_listMap.get(3);
    let boolType = false;
    if (this._cur_Pitch_skinId != 0 && JsonMgr.instance.jsonList.c_leaderSkin[this._cur_Pitch_skinId].type == 3) {
      boolType = true;
    }
    for (let i = 0; i < list.length; i++) {
      let data = list[i];
      let spineShowdb = JsonMgr.instance.jsonList.c_spineShow[data.spineId];
      let node = ToolExt.clone(this.getNode("btn_skin_card"), this);
      node.on(Input.EventType.TOUCH_END, ToolExt.tryFunc(this.OnClick_btn_skin_card.bind(this)), this);
      node.name += "_" + data.id;
      this.getNode("content_shengdian_skin").addChild(node);
      node.active = true;
      node.getChildByName("lab_skinName").active = true;
      node.getChildByName("lab_skinName").getComponent(Label).string = data.name;
      ResMgr.loadImage(spineShowdb.halfRes, node["herohalf"].getComponent(Sprite), this);
      node["skinInfo"] = data;
      if (boolType == false) {
        this._cur_Pitch_skinId = data.id;
        boolType = true;
      }
      let bool = false;
      let info = PlayerModule.data.skinMap[data.id];
      if (info && info.level >= 1) {
        bool = true;
        let list = this._has_skin_list_map.get(3);
        list.push(data);
        this._has_skin_list_map.set(3, list);
      }
      this.setUnSkinState(node, bool);
      this._skin_node_list.push(node);
      this.setActiveState(node, data.id);
      node["btn_skin_active"]["skinId"] = data.id;
      node["btn_icon_suo"]["skinId"] = data.id;
      node["btn_skin_active"]["card_node"] = node;
    }
  }
  private setUnSkinState(skinNode: Node, colorBool: boolean) {
    let color = new Color(100, 100, 100, 255);
    if (colorBool == true) {
      color = new Color(255, 255, 255, 255);
    }
    // head.getComponent(Sprite).color = head.getComponent(Sprite).color = color;
    skinNode["kuang"].getComponent(Sprite).color = color;
    skinNode["herohalf"].getComponent(Sprite).color = color;
    skinNode["btn_icon_suo"].active = !colorBool;
  }
  private changePitchSkin() {
    this.foreachSkinList(this._cur_Pitch_skinId, "spr_cur_pitch");
    this.setTopSkinName();
    let curId = this._cur_Pitch_skinId;
    let data = PlayerModule.data.skinMap[curId];
    if (!data) {
      this.getNode("btn_use_skin").active = false;
      this.getNode("btn_jihuo_skin").active = true;
      this.getNode("btn_jihuo_skin").getChildByName("icon").getComponent(Sprite).grayscale = true;
      this.getNode("btn_jihuo_skin").getChildByName("Label").getComponent(Label).color = new Color(100, 100, 100, 255);
      this.getNode("btn_jihuo_skin").getChildByName("Label").getComponent(Label).enableOutline = false;
    } else if (data && data.level >= 1) {
      this.getNode("btn_use_skin").active = true;
      this.getNode("btn_jihuo_skin").active = false;
    } else if (data && data.level == 0) {
      this.getNode("btn_use_skin").active = false;
      this.getNode("btn_jihuo_skin").active = true;
      this.getNode("btn_jihuo_skin").getChildByName("icon").getComponent(Sprite).grayscale = false;
      this.getNode("btn_jihuo_skin").getChildByName("Label").getComponent(Label).color = new Color(255, 255, 255, 255);
      this.getNode("btn_jihuo_skin").getChildByName("Label").getComponent(Label).enableOutline = true;
    }
    //
    if (this._skin_show_type == SKIN_SHOW_TYPE_ENUM.PORTRAIT) {
      this.setSkinProtraitPrefab();
    } else if (this._skin_show_type == SKIN_SHOW_TYPE_ENUM.MODEL) {
      this.setSkinModelPrefab();
    }

    if (this._cur_Use_skinId == this._cur_Pitch_skinId) {
      this.getNode("btn_use_skin").getComponent(Sprite).grayscale = true;
      this.getNode("btn_use_skin").getChildByName("Label").getComponent(Label).string = "使用中";
      this.getNode("btn_use_skin").getChildByName("Label").getComponent(Label).outlineColor = new Color(
        82,
        88,
        93,
        255
      );
      this.getNode("btn_use_skin").getChildByName("Label").getComponent(Label).color = new Color(255, 255, 255, 255);
    } else {
      this.getNode("btn_use_skin").getComponent(Sprite).grayscale = false;
      this.getNode("btn_use_skin").getChildByName("Label").getComponent(Label).string = "使 用";
      this.getNode("btn_use_skin").getChildByName("Label").getComponent(Label).outlineColor = new Color(
        33,
        94,
        143,
        255
      );
      this.getNode("btn_use_skin").getChildByName("Label").getComponent(Label).color = new Color(243, 244, 234, 255);
    }
  }
  private setActiveState(node: Node, id: number) {
    let db = JsonMgr.instance.jsonList.c_leaderSkin[id];
    if (db.type == 1 || db.type == 3) {
      node["active_state"].active = false;
      return;
    }
    let data = PlayerModule.data.skinMap[id];
    let db1 = JsonMgr.instance.jsonList.c_leaderSkin[id];
    if (data && data.level >= 1) {
      node["active_state"].active = false;
      node["btn_icon_suo"].active = false;
    } else if (data && data.level == 0) {
      node["active_state"].active = true;
      node["btn_icon_suo"].active = false;
    } else if (!data) {
      node["active_state"].active = false;
      node["btn_icon_suo"].active = true;
    }
    // else {
    //   node["active_state"].active = false;
    // }
    if (db1.activateCostList.length > 0) {
      ToolExt.setItemIcon(node["skin_item_icon"], db1.activateCostList[0]);
      node["lbl_skin_item_lab"].getComponent(Label).string =
        Formate.format(PlayerModule.data.getItemNum(db1.activateCostList[0])) + "/" + db1.activateCostList[1];
      if (PlayerModule.data.getItemNum(db1.activateCostList[0]) >= db1.activateCostList[1]) {
        ToolExt.setLabColor(node["lbl_skin_item_lab"].getComponent(Label), "#00af04");
      } else {
        ToolExt.setLabColor(node["lbl_skin_item_lab"].getComponent(Label), "#FF0000");
      }
    }
  }
  private changeUseSkin() {
    this.foreachSkinList(this._cur_Use_skinId, "spr_cur_use");
  }
  private foreachSkinList(id: number, nodeKey: string) {
    for (let i = 0; i < this._skin_node_list.length; i++) {
      let skin_node = this._skin_node_list[i];
      let skinInfo = skin_node["skinInfo"];
      if (skinInfo.id == id) {
        skin_node.getChildByName(nodeKey).active = true;
      } else {
        skin_node.getChildByName(nodeKey).active = false;
      }
    }
  }
  private setTopSkinName() {
    let info = JsonMgr.instance.jsonList.c_leaderSkin[this._cur_Pitch_skinId];
    if (!info) {
      return;
    }
    this.getNode("lab_top_skin_name").getComponent(Label).string = info.name;
    this.setAttrTip();
    this.setUnlockTip();
  }
  private setAttrTip() {
    let info = JsonMgr.instance.jsonList.c_leaderSkin[this._cur_Pitch_skinId];
    if (!info) {
      return;
    }
    let db = JsonMgr.instance.jsonList.c_attribute;
    let attrAdd = info.attrAdd;
    if (attrAdd.length <= 0) {
      this.getNode("rich_addAttr").getComponent(RichText).string = `<color=#dfeaff>无</color>`;
      return;
    }
    let str = ``;
    for (let i = 0; i < attrAdd.length; i++) {
      let attrinfo = db[attrAdd[i][0]];
      str += `<color=#dfeaff>${attrinfo.name}</color>`;
      if (attrinfo.type1 == 1) {
        str += `<color=#eda03e>+${attrAdd[i][1]}</color>`;
      } else {
        str += `<color=#eda03e>+${attrAdd[i][1]}%</color>`;
      }
    }
    this.getNode("rich_addAttr").getComponent(RichText).string = str;
  }
  private setUnlockTip() {
    let info = JsonMgr.instance.jsonList.c_leaderSkin[this._cur_Pitch_skinId];
    if (!info) {
      return;
    }
    if (info.type == 3) {
      this.getNode("rich_unlock").active = false;
      return;
    }
    this.getNode("rich_unlock").active = true;
    if (info.unlock.length < 2) {
      this.getNode("rich_unlock").getComponent(
        RichText
      ).string = `<outline color=#770404 width=2><color=#ffa0a0>默认解锁</color></outline>`;
      return;
    }
    let type = info.unlock[0];
    let param = info.unlock[1];
    switch (type) {
      case 0:
        this.getNode("rich_unlock").getComponent(
          RichText
        ).string = `<outline color=#770404 width=2><color=#ffa0a0>默认解锁</color></outline>`;
        break;
      case 1:
        this.getNode("rich_unlock").getComponent(
          RichText
        ).string = `<outline color=#770404 width=2><color=#ffa0a0>身份等级达到${param}级，永久有效</color></outline>`;
        break;
      case 2:
        this.getNode("rich_unlock").getComponent(
          RichText
        ).string = `<outline color=#770404 width=2><color=#ffa0a0>${info.source}</color></outline>`;
        break;
      case 3:
        this.getNode("rich_unlock").getComponent(
          RichText
        ).string = `<outline color=#770404 width=2><color=#ffa0a0>${info.source}</color></outline>`;
        break;
      default:
        break;
    }
  }
  private setSkinProtraitPrefab() {
    this.getNode("zj_sweep_light").active = false;
    this.getNode("zj_sweep_light").getComponent(sp.Skeleton).setCompleteListener(null);
    let info = JsonMgr.instance.jsonList.c_leaderSkin[this._cur_Pitch_skinId];
    if (!info) {
      return;
    }
    let roledb = JsonMgr.instance.jsonList.c_spineShow[info.spineId];
    if (!roledb) {
      return;
    }
    let name = "skin_point";
    let node: Node = this.getNode("skin_potrait_point").getChildByName(name);
    if (roledb.imageRes == "") {
      node.removeFromParent();
      node.destroy();
      let newNode = new Node(name);
      this.getNode("skin_potrait_point").addChild(newNode);
      return;
    }
    ResMgr.loadPrefab(
      `${roledb.imageRes}`,
      (prefab: Prefab) => {
        if (isValid(this.node) == false) {
          prefab.decRef();
          return;
        }
        let newNode = instantiate(prefab);
        prefab.addRef();
        newNode["resPrefab"] = prefab;
        newNode.layer = this.getNode("skin_potrait_point").layer;
        newNode.walk((val) => {
          val.layer = newNode.layer;
        });
        newNode.name = name;
        this.getNode("skin_potrait_point").addChild(newNode);
        if (newNode.children[0].activeInHierarchy) {
          newNode.children[0].getComponent(sp.Skeleton).setAnimation(0, "animation1", true);
        }
        this.getNode("zj_sweep_light").active = true;
        this.getNode("zj_sweep_light")
          .getComponent(sp.Skeleton)
          .setCompleteListener(() => {
            this.getNode("zj_sweep_light").active = false;
            this.getNode("zj_sweep_light").getComponent(sp.Skeleton).setCompleteListener(null);
          });
        this.getNode("zj_sweep_light").getComponent(sp.Skeleton).setAnimation(0, "animation", false);
        node.removeFromParent();
        let resPrefab: Prefab = node["resPrefab"];
        if (resPrefab) {
          resPrefab.decRef();
        }
        node.destroy();
      },
      this
    );
  }
  private setSkinModelPrefab() {
    let info = JsonMgr.instance.jsonList.c_leaderSkin[this._cur_Pitch_skinId];
    if (!info) {
      return;
    }
    let roledb = JsonMgr.instance.jsonList.c_spineShow[info.spineId];
    if (!roledb) {
      return;
    }
    let name = "skin_model";
    let node = this.getNode("skin_model_point").getChildByName(name);
    if (roledb.prefabPath == "" || roledb.prefabPath == "null") {
      node.removeFromParent();
      node.destroy();
      let newNode = new Node(name);
      this.getNode("skin_model_point").addChild(newNode);
      return;
    }
    ToolExt.loadUIRole(this.getNode("skin_model_point"), this._cur_Pitch_skinId, -1, "renderScale2", this);
    // ResMgr.loadPrefab(
    //   `${roledb.prefabPath}`,
    //   (prefab: Prefab) => {
    //     if (isValid(this.node) == false) {
    //       prefab.decRef();
    //       return;
    //     }
    //     if (info.id != this._cur_Pitch_skinId) {
    //       prefab.decRef();
    //       return;
    //     }
    //     let newNode = instantiate(prefab);
    //     prefab.addRef();
    //     newNode["resPrefab"] = prefab;
    //     newNode.name = name;
    //     newNode.setScale(v3(info.renderScale2[0], info.renderScale2[1], info.renderScale2[2]));
    //     this.getNode("skin_model_point").addChild(newNode);
    //     if (newNode.getChildByName("render").activeInHierarchy) {
    //       newNode.getChildByName("render").getComponent(sp.Skeleton).setAnimation(0, "boss_idle", true);
    //     }
    //     node.removeFromParent();
    //     let resPrefab: Prefab = node["resPrefab"];
    //     if (resPrefab) {
    //       resPrefab.decRef();
    //     }
    //     node.destroy();
    //   },
    //   this
    // );
  }
  private changeScrollList() {
    this._cur_Pitch_skinId = PlayerModule.data.skin.skinId;
    this._cur_Use_skinId = PlayerModule.data.skin.skinId;
    switch (this._skin_title_tab) {
      case SKIN_TAB_ENUM.IDENTITY:
        this._skin_show_type = SKIN_SHOW_TYPE_ENUM.MODEL;
        this.setShowType();
        this.getNode("btn_change_skin_type").active = false;
        this.getNode("scrollview_identity_skin").getComponent(ScrollView).scrollToTop(0.1);
        this.initIdentityList();
        this.getNode("scrollview_identity_skin").active = true;
        this.getNode("scrollview_activity_skin").active = false;
        this.getNode("scrollview_shengdian_skin").active = false;
        break;
      case SKIN_TAB_ENUM.ACTIVITY:
        this.getNode("btn_change_skin_type").active = true;
        this.getNode("scrollview_activity_skin").getComponent(ScrollView).scrollToTop(0.1);
        this.sortActivity();
        this.initActivityList();
        this.getNode("scrollview_identity_skin").active = false;
        this.getNode("scrollview_shengdian_skin").active = false;
        this.getNode("scrollview_activity_skin").active = true;
        break;
      case SKIN_TAB_ENUM.SHENGDIAN:
        this._skin_show_type = SKIN_SHOW_TYPE_ENUM.MODEL;
        this.setShowType();
        this.getNode("btn_change_skin_type").active = false;
        this.getNode("scrollview_shengdian_skin").getComponent(ScrollView).scrollToTop(0.1);
        this.initShengdianList();
        this.getNode("scrollview_identity_skin").active = false;
        this.getNode("scrollview_activity_skin").active = false;
        this.getNode("scrollview_shengdian_skin").active = true;
        break;
    }
    this.changePitchSkin();
    this.changeUseSkin();
    this.setLabSkinNum();
    this.changeSkinTabBtn();
  }
  private changeSkinTabBtn() {
    if (this._skin_title_tab == SKIN_TAB_ENUM.IDENTITY) {
      this.getNode("btn_skin_identity").getChildByName("pitch").active = true;
      this.getNode("btn_skin_identity").getChildByName("no_pitch").active = false;
      this.getNode("btn_skin_activity").getChildByName("pitch").active = false;
      this.getNode("btn_skin_activity").getChildByName("no_pitch").active = true;
      this.getNode("btn_skin_shengdian").getChildByName("pitch").active = false;
      this.getNode("btn_skin_shengdian").getChildByName("no_pitch").active = true;
      this.getNode("btn_watchSkill").active = false;
    } else if (this._skin_title_tab == SKIN_TAB_ENUM.ACTIVITY) {
      this.getNode("btn_skin_identity").getChildByName("pitch").active = false;
      this.getNode("btn_skin_identity").getChildByName("no_pitch").active = true;
      this.getNode("btn_skin_activity").getChildByName("pitch").active = true;
      this.getNode("btn_skin_activity").getChildByName("no_pitch").active = false;
      this.getNode("btn_skin_shengdian").getChildByName("pitch").active = false;
      this.getNode("btn_skin_shengdian").getChildByName("no_pitch").active = true;
      this.getNode("btn_watchSkill").active = true;
    } else if (this._skin_title_tab == SKIN_TAB_ENUM.SHENGDIAN) {
      this.getNode("btn_skin_identity").getChildByName("pitch").active = false;
      this.getNode("btn_skin_identity").getChildByName("no_pitch").active = true;
      this.getNode("btn_skin_activity").getChildByName("pitch").active = false;
      this.getNode("btn_skin_activity").getChildByName("no_pitch").active = true;
      this.getNode("btn_skin_shengdian").getChildByName("pitch").active = true;
      this.getNode("btn_skin_shengdian").getChildByName("no_pitch").active = false;
      this.getNode("btn_watchSkill").active = true;
    }
  }
  private setLabSkinNum() {
    let haslist = [];
    let maxList = [];
    switch (this._skin_title_tab) {
      case SKIN_TAB_ENUM.IDENTITY:
        haslist = this._has_skin_list_map.get(1) || [];
        maxList = this._skin_listMap.get(1) || [];
        break;
      case SKIN_TAB_ENUM.ACTIVITY:
        haslist = this._has_skin_list_map.get(2) || [];
        maxList = this._skin_listMap.get(2) || [];
        break;
      case SKIN_TAB_ENUM.SHENGDIAN:
        haslist = this._has_skin_list_map.get(3) || [];
        maxList = this._skin_listMap.get(3) || [];
        break;
    }
    this.getNode("lab_skin_hasNum").getComponent(Label).string = "已拥有:" + haslist.length + "/" + maxList.length;
  }
  private on_click_btn_skin_identity() {
    AudioMgr.instance.playEffect(PlayerAudioName.Effect.点击页签);
    if (this._skin_title_tab == SKIN_TAB_ENUM.IDENTITY) {
      return;
    }
    this._skin_title_tab = SKIN_TAB_ENUM.IDENTITY;
    this.changeScrollList();
  }
  private on_click_btn_skin_activity() {
    AudioMgr.instance.playEffect(PlayerAudioName.Effect.点击页签);
    if (this._skin_title_tab == SKIN_TAB_ENUM.ACTIVITY) {
      return;
    }
    this._skin_title_tab = SKIN_TAB_ENUM.ACTIVITY;
    this.changeScrollList();
  }
  private on_click_btn_skin_shengdian() {
    AudioMgr.instance.playEffect(PlayerAudioName.Effect.点击页签);
    if (this._skin_title_tab == SKIN_TAB_ENUM.SHENGDIAN) {
      return;
    }
    this._skin_title_tab = SKIN_TAB_ENUM.SHENGDIAN;
    this.changeScrollList();
  }
  private on_click_btn_change_skin_type() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    if (this._skin_show_type == SKIN_SHOW_TYPE_ENUM.PORTRAIT) {
      this._skin_show_type = SKIN_SHOW_TYPE_ENUM.MODEL;
      this.getNode("skin_portrait_mask").active = false;
      this.getNode("skin_model_mask").active = true;
      this.setSkinModelPrefab();
    } else if (this._skin_show_type == SKIN_SHOW_TYPE_ENUM.MODEL) {
      this._skin_show_type = SKIN_SHOW_TYPE_ENUM.PORTRAIT;
      this.getNode("skin_portrait_mask").active = true;
      this.getNode("skin_model_mask").active = false;
      this.setSkinProtraitPrefab();
    }
  }
  private on_click_btn_use_skin() {
    AudioMgr.instance.playEffect(PlayerAudioName.Effect.点击换装使用按钮);
    let info = PlayerModule.data.skinMap[this._cur_Pitch_skinId];
    if (!info || info.level == -1) {
      TipMgr.showTip("未拥有皮肤");
      return;
    }
    PlayerModule.api.workSkinOrDecoration(info.skinId, () => {
      this._cur_Pitch_skinId = PlayerModule.data.skin.skinId;
      this._cur_Use_skinId = PlayerModule.data.skin.skinId;
      this.changePitchSkin();
      this.changeUseSkin();
      TipMgr.showTip("使用成功");
    });
  }
  private on_click_btn_jihuo_skin() {
    AudioMgr.instance.playEffect(PlayerAudioName.Effect.点击激活按钮);
    let curId = this._cur_Pitch_skinId;
    let data = PlayerModule.data.skinMap[curId];
    if (!data) {
      TipMgr.showTip("请先获取该战将");
      return;
    }
    if (data && data.level >= 1) {
      return;
    }
    let db = JsonMgr.instance.jsonList.c_leaderSkin[curId];
    let id = db.activateCostList[0];
    let num = db.activateCostList[1];
    let my = PlayerModule.data.getItemNum(id);
    let itemdb = JsonMgr.instance.getConfigItem(id);
    let msg: ConfirmMsg = {
      msg: `是否花费'${num}${itemdb.name}'解锁该皮肤`,
      itemList: db.activateCostList,
    };
    UIMgr.instance.showDialog(PublicRouteName.UICostConfirm, msg, (resp) => {
      if (resp?.ok) {
        if (my < num) {
          UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
            itemId: id,
            needNum: num,
          });
          TipMgr.showTip(JsonMgr.instance.getConfigItem(id).name + "不足");
          return;
        }
        PlayerModule.api.activeSkin(curId, (data: SkinMessage) => {
          for (let i = 0; i < this._skin_node_list.length; i++) {
            let skin_node = this._skin_node_list[i];
            let skinInfo = skin_node["skinInfo"];
            if (skinInfo.id == data.skinId) {
              let callback = () => {
                TipMgr.showTip("解锁成功");
                this.changePitchSkin();
                this.changeUseSkin();
                this.setUnSkinState(skin_node, true);
                this.setActiveState(skin_node, data.skinId);
              };
              if (db.type == 2) {
                let node: Node = skin_node;
                this.moveScroll(skin_node);
                UIMgr.instance.showDialog(PlayerRouteName.UISkinOpen, {
                  leaderSkinId: curId,
                  end_node: node,
                  callback: callback,
                  keepMask: true,
                });
              } else {
                callback();
              }
            }
          }
        });
      }
    });
  }
  private on_click_btn_watchSkill() {
    AudioMgr.instance.playEffect(PlayerAudioName.Effect.点击战斗预览按钮);
    let info = JsonMgr.instance.jsonList.c_leaderSkin[this._cur_Pitch_skinId];
    if (!info) {
      return;
    }
    UIMgr.instance.showDialog(PlayerRouteName.UIWatchSkill, { roleId: this._cur_Pitch_skinId });
  }
  // private on_click_btn_xiangxixinxi() {
  //   let info = JsonMgr.instance.jsonList.c_leaderSkin[this._cur_Pitch_skinId];
  //   UIMgr.instance.showDialog(PlayerRouteName.UIPlayerSkinTip, { info: info, keepMask: true });
  // }
  private OnClick_btn_skin_card(event) {
    AudioMgr.instance.playEffect(PlayerAudioName.Effect.点击皮肤图标);
    if (this._cur_Pitch_skinId == event.target["skinInfo"].id) {
      return;
    }
    this._cur_Pitch_skinId = event.target["skinInfo"].id;
    this.changePitchSkin();
  }
  private moveScroll(node: Node) {
    let content: Node = node.parent;
    let scroll = content.parent.parent;
    let cadWorPos = node.getWorldPosition();
    let scrollWorPos = scroll.getWorldPosition();
    let dis = scrollWorPos.y - cadWorPos.y - scroll.getComponent(UITransform).height / 2;
    let newY = content.getPosition().y + dis;
    scroll.getComponent(ScrollView).scrollToOffset(new Vec2(0, newY), 0.15, true);
  }
  private on_click_btn_icon_suo(event) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let curId = event.node["skinId"];
    let data = PlayerModule.data.skinMap[curId];
    let db = JsonMgr.instance.jsonList.c_leaderSkin[curId];
    if (!data) {
      switch (db.unlock[0]) {
        case 1:
          TipMgr.showTip(`等级达到${db.unlock[1]}级解锁`);
          break;
        case 2:
          TipMgr.showTip(db.source);
          break;
        case 3:
          TipMgr.showTip(db.source);
          break;
        default:
          break;
      }
      return;
    }
    let id = db.activateCostList[0];
    let num = db.activateCostList[1];
    let my = PlayerModule.data.getItemNum(id);
    if (my < num) {
      UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
        itemId: id,
        needNum: num,
      });
      return;
    }
  }
  private on_click_btn_skin_active(event) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let curId = event.node["skinId"];
    let data = PlayerModule.data.skinMap[curId];
    if (!data) {
      return;
    }
    if (data && data.level >= 1) {
      return;
    }
    let db = JsonMgr.instance.jsonList.c_leaderSkin[curId];
    let id = db.activateCostList[0];
    let num = db.activateCostList[1];
    let my = PlayerModule.data.getItemNum(id);
    let itemdb = JsonMgr.instance.getConfigItem(id);
    let msg: ConfirmMsg = {
      msg: `是否花费'${num}${itemdb.name}'解锁该皮肤`,
      itemList: db.activateCostList,
    };
    UIMgr.instance.showDialog(PublicRouteName.UICostConfirm, msg, (resp) => {
      if (resp?.ok) {
        if (my < num) {
          UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
            itemId: id,
            needNum: num,
          });
          TipMgr.showTip(JsonMgr.instance.getConfigItem(id).name + "不足");
          return;
        }
        PlayerModule.api.activeSkin(curId, (data: SkinMessage) => {
          for (let i = 0; i < this._skin_node_list.length; i++) {
            let skin_node = this._skin_node_list[i];
            let skinInfo = skin_node["skinInfo"];
            if (skinInfo.id == data.skinId) {
              //TipMgr.showTip("解锁成功");
              let callback = () => {
                this.setUnSkinState(skin_node, true);
                this.setActiveState(skin_node, data.skinId);
                this.changePitchSkin();
                this.changeUseSkin();
              };
              if (db.type == 2) {
                let node: Node = event.node;
                this.moveScroll(event.node["card_node"]);
                UIMgr.instance.showDialog(PlayerRouteName.UISkinOpen, {
                  leaderSkinId: curId,
                  end_node: node,
                  callback: callback,
                  keepMask: true,
                });
              } else {
                callback();
              }
            }
          }
        });
      }
    });
  }
  //换装====================================================================================================================================================================================================================================
  //装饰====================================================================================================================================================================================================================================
  private _adron_title_tab: ADRON_TAB_ENUM = ADRON_TAB_ENUM.HEAD;
  private showAdronMainIsState() {
    this.change_adron_tab();
    this.change_adorn_main_layer();
    switch (this._adron_title_tab) {
      case ADRON_TAB_ENUM.HEAD:
        this.initHeadMain();
        break;
      case ADRON_TAB_ENUM.HEAD_FRAME:
        break;
      case ADRON_TAB_ENUM.TITLE_NAME:
        this.initTitleMain();
        break;
      case ADRON_TAB_ENUM.BUBBLE:
        break;
    }
  }
  private on_click_btn_adorn_head() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    if (this._adron_title_tab == ADRON_TAB_ENUM.HEAD) {
      return;
    }
    this._adron_title_tab = ADRON_TAB_ENUM.HEAD;
    this.showAdronMainIsState();
  }
  private on_click_btn_adorn_title_name() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    if (this._adron_title_tab == ADRON_TAB_ENUM.TITLE_NAME) {
      return;
    }
    this._adron_title_tab = ADRON_TAB_ENUM.TITLE_NAME;
    this.showAdronMainIsState();
  }
  private change_adron_tab() {
    this.getNode("node_adorn_top_tab").children.forEach((val) => {
      val.getComponent(Sprite).grayscale = true;
      let lab = val.getChildByName("Label");
      lab.getComponent(Label).outlineColor = new Color("#81838D");
    });
    let curNode = null;
    switch (this._adron_title_tab) {
      case ADRON_TAB_ENUM.HEAD:
        curNode = this.getNode("btn_adorn_head");
        break;
      case ADRON_TAB_ENUM.HEAD_FRAME:
        curNode = this.getNode("btn_adorn_head_frame");
        break;
      case ADRON_TAB_ENUM.TITLE_NAME:
        curNode = this.getNode("btn_adorn_title_name");
        break;
      case ADRON_TAB_ENUM.BUBBLE:
        curNode = this.getNode("btn_adorn_bubble");
        break;
    }
    curNode.getComponent(Sprite).grayscale = false;
    let lab = curNode.getChildByName("Label");
    lab.getComponent(Label).outlineColor = new Color("#E7A339");
  }
  private change_adorn_main_layer() {
    this.getNode("adorn_main_layer").children.forEach((val) => {
      val.active = false;
    });
    let curNode = null;
    switch (this._adron_title_tab) {
      case ADRON_TAB_ENUM.HEAD:
        curNode = this.getNode("head_tab_main");
        break;
      case ADRON_TAB_ENUM.HEAD_FRAME:
        break;
      case ADRON_TAB_ENUM.TITLE_NAME:
        curNode = this.getNode("title_tab_main");
        break;
      case ADRON_TAB_ENUM.BUBBLE:
        break;
    }
    curNode.active = true;
  }
  //================================头像=================================
  private _head_node_list: Node[] = [];
  private _identity_head_info_List = [];
  private _hero_head_info_List = [];
  /**选中 */
  private _cur_Pitch_HeadId: number = 0;
  /**使用 */
  private _cur_Use_HeadId: number = 0;
  private initHeadMain() {
    this._head_node_list = [];
    this._identity_head_info_List = this.getHeadIdentityList();
    this._hero_head_info_List = this.getHeadHeroList();
    this._cur_Pitch_HeadId = PlayerModule.data.head.headShowId;
    this._cur_Use_HeadId = PlayerModule.data.head.headShowId;
    this.setMyHead(this._cur_Pitch_HeadId);
    this.loadIdentityHead();
    this.loadHeroHead();
    this.changePitchHead();
    this.changeUseHead();
    this.setHeroHeadNum();
  }
  private getHeadIdentityList() {
    let c_headShow = JsonMgr.instance.jsonList.c_headShow;
    let list = Object.keys(c_headShow);
    let identity_head_info_List = [];
    for (let i = 0; i < list.length; i++) {
      let info = c_headShow[list[i]];
      if (info.type == 1 && PlayerModule.data.sex == info.sex) {
        identity_head_info_List.push(info);
      }
    }
    return identity_head_info_List;
  }
  private getHeadHeroList() {
    let c_headShow = JsonMgr.instance.jsonList.c_headShow;
    let list = Object.keys(c_headShow);
    let hero_head_info_List = [];
    for (let i = 0; i < list.length; i++) {
      let info = c_headShow[list[i]];
      if (info.type == 2) {
        hero_head_info_List.push(info);
      }
    }
    return hero_head_info_List;
  }
  private loadIdentityHead() {
    this.getNode("content_adron_idenity").destroyAllChildren();
    for (let i = 0; i < this._identity_head_info_List.length; i++) {
      let node = ToolExt.clone(this.getNode("btn_load_head"), this);
      this.getNode("content_adron_idenity").addChild(node);
      node.active = true;
      let info = this._identity_head_info_List[i];
      let data = ToolExt.newPlayerBaseMessage();
      data.avatarList[1] = info.id;
      FmUtils.setHeaderNode(node, data);
      node["headInfo"] = info;
      this._head_node_list.push(node);
      let bool = true;
      if (!PlayerModule.data.headShowMap[info.id]) {
        bool = false;
      }
      this.setUnHeadState(node, bool);
    }
  }
  private loadHeroHead() {
    this.getNode("content_adron_warrior").destroyAllChildren();
    let list1 = [];
    let list2 = [];
    for (let i = 0; i < this._hero_head_info_List.length; i++) {
      let info = this._hero_head_info_List[i];
      if (!PlayerModule.data.headShowMap[info.id]) {
        list2.push(info);
      } else {
        list1.push(info);
      }
    }
    this._hero_head_info_List = [].concat(list1, list2);
    for (let i = 0; i < this._hero_head_info_List.length; i++) {
      let node = ToolExt.clone(this.getNode("btn_load_head"), this);
      this.getNode("content_adron_warrior").addChild(node);
      node.active = true;
      let info = this._hero_head_info_List[i];
      let data = ToolExt.newPlayerBaseMessage();
      data.avatarList[1] = info.id;
      FmUtils.setHeaderNode(node, data);
      node["headInfo"] = info;
      this._head_node_list.push(node);
      let bool = true;
      if (!PlayerModule.data.headShowMap[info.id]) {
        bool = false;
      }
      this.setUnHeadState(node, bool);
    }
  }
  private setUnHeadState(head: Node, colorBool: boolean) {
    let color = new Color(100, 100, 100, 255);
    if (colorBool == true) {
      color = new Color(255, 255, 255, 255);
    }
    head["bgHead"].getComponent(Sprite).color = color;
    head["icon_suo"].active = !colorBool;
  }
  private setHeroHeadNum() {
    //let list = this.getHasHeroHeadList();
    let headShowMap = PlayerModule.data.headShowMap;
    let list1 = Object.keys(headShowMap);
    let c_headShow = JsonMgr.instance.jsonList.c_headShow;
    let list2 = Object.keys(c_headShow);
    this.getNode("lab_hero_num").getComponent(Label).string = "已拥有:" + list1.length + "/" + list2.length;
  }
  private getHasHeroHeadList() {
    let decorationMap = PlayerModule.data.headShowMap;
    let db = JsonMgr.instance.jsonList.c_headShow;
    let list = [];
    Object.keys(decorationMap).forEach(function (key) {
      let info = db[decorationMap[key].headShowId];
      if (!info) {
        return;
      }
      list.push(info);
    });
    return list;
  }
  private async setMyHead(id: number) {
    let data = ToolExt.newPlayerBaseMessage();
    data.avatarList[1] = id;
    FmUtils.setHeaderNode(this.getNode("node_show_pitch_head"), data);
    this.setIntroduce(id);
  }
  private setIntroduce(id: number) {
    this.getNode("head_spr_message_bg").active = true;
    let info = PlayerModule.service.getAvatarConfig(id);
    let unlock = info.unlock;
    if (unlock.length < 2) {
      //log.error("配置解锁数据错误");
      unlock = [0, 0];
      //this.getNode("head_spr_message_bg").active = false;
      //return;
    }
    switch (unlock[0]) {
      case 0:
        this.getNode("lab_head_introduce1").getComponent(Label).string = "初始解锁";
        this.getNode("lab_head_introduce2").getComponent(Label).string = ``;
        break;
      case 1:
        this.getNode("lab_head_introduce1").getComponent(Label).string = "等级解锁";
        this.getNode("lab_head_introduce2").getComponent(Label).string = `等级达到${unlock[1]}解锁`;
        break;
      case 2:
        this.getNode("lab_head_introduce1").getComponent(Label).string = "获得对应战将解锁";
        this.getNode("lab_head_introduce2").getComponent(Label).string = `获得战将${info.name}解锁`;
        break;
      default:
        log.error("新增了头像解锁类型，获取不到");
        break;
    }
  }
  private changePitchHead() {
    this.foreachHeadList(this._cur_Pitch_HeadId, "spr_cur_pitch");
    this.setMyHead(this._cur_Pitch_HeadId);
  }
  private changeUseHead() {
    this.foreachHeadList(this._cur_Use_HeadId, "spr_cur_use");
  }
  private foreachHeadList(id: number, nodeKey: string) {
    for (let i = 0; i < this._head_node_list.length; i++) {
      let head_node = this._head_node_list[i];
      let headInfo = head_node["headInfo"];
      if (headInfo.id == id) {
        head_node.getChildByName(nodeKey).active = true;
      } else {
        head_node.getChildByName(nodeKey).active = false;
      }
    }
  }
  private on_click_btn_load_head(event) {
    AudioMgr.instance.playEffect(PlayerAudioName.Effect.点击头像图标);
    this._cur_Pitch_HeadId = event.node["headInfo"].id;
    this.changePitchHead();
  }
  private on_click_btn_use_head() {
    AudioMgr.instance.playEffect(PlayerAudioName.Effect.点击装饰使用按钮);
    let info = PlayerModule.data.headShowMap[this._cur_Pitch_HeadId];
    if (!info) {
      TipMgr.showTip("未拥有头像");
      return;
    }
    PlayerModule.api.workSkinOrDecoration(info.headShowId, () => {
      this._cur_Pitch_HeadId = PlayerModule.data.head.headShowId;
      this._cur_Use_HeadId = PlayerModule.data.head.headShowId;
      this.setMyHead(this._cur_Pitch_HeadId);
      this.changePitchHead();
      this.changeUseHead();
      TipMgr.showTip("使用成功");
    });
  }
  //================================头像=================================
  //================================称号=================================
  private _title_node_map: Map<number, Node> = new Map<number, Node>();
  /**选中 */
  private _cur_Pitch_titleId: number = 0;
  /**使用 */
  private _cur_Use_titleId: number = 0;
  private initTitleMain() {
    this.checkUserTitle();
    this.load_title();
    this.setTileMaxNum();
  }
  private load_title() {
    this.getNode("content_title").destroyAllChildren();
    let db = JsonMgr.instance.jsonList.c_title;
    let list = Object.keys(db);
    for (let i = 0; i < list.length; i++) {
      let info = db[list[i]];
      let node = ToolExt.clone(this.getNode("btn_title_ins"), this);
      this.getNode("content_title").addChild(node);
      node.active = true;
      node["titleId"] = info.id;
      FmUtils.setItemNode(node["title_Item"], info.id);
      this.setTitleDetail(info, node);
      this.setUserPitch(node);
      this.setUnlock(node, info.id);
    }
  }
  private setTitleDetail(info: any, node) {
    PlayerModule.service.createTitle(node["point"], info.id, (titleNode: Node) => {
      titleNode.setPosition(v3(0, -10, 0));
      titleNode.setScale(v3(0.85, 0.85, 1));
      node["titleId"] = info.id;
      this._title_node_map.set(info.id, node);
    });
  }
  /**设置选中使用状态 */
  private setUserPitch(node) {
    if (node["titleId"] == this._cur_Pitch_titleId) {
      node["spr_cur_pitch"].active = true;
    } else {
      node["spr_cur_pitch"].active = false;
    }
    if (node["titleId"] == this._cur_Use_titleId) {
      node["spr_cur_use"].active = true;
    } else {
      node["spr_cur_use"].active = false;
    }
  }
  /**是否有解锁 */
  private setUnlock(node, id) {
    let data = PlayerModule.data.titleMap[id];
    if (!data) {
      node["icon_suo"].active = true;
      node["icon_suo_2"].active = true;
    } else {
      node["icon_suo"].active = false;
      node["icon_suo_2"].active = false;
    }
  }
  /**设置是否是有当前使用的称号 */
  private checkUserTitle() {
    let useId = PlayerModule.data.title?.titleId;
    log.log("UIPlayerLevelUpuseId", useId);
    log.log("UIPlayerLevelUp_title_node_map", this._title_node_map);
    if (this._title_node_map.has(useId)) {
      this._cur_Pitch_titleId = useId;
      this._cur_Use_titleId = useId;
    } else {
      let db = JsonMgr.instance.jsonList.c_title;
      let list = Object.keys(db);
      let id: number = Number(list[0]);
      this._cur_Pitch_titleId = id;
      this._cur_Use_titleId = useId;
    }
    this.showPichTitle();
    this.setTitleState();
  }
  /**展示选中的称号 */
  private showPichTitle() {
    this.getNode("show_title_point").destroyAllChildren();
    PlayerModule.service.createTitle(this.getNode("show_title_point"), this._cur_Pitch_titleId, (prefab, db) => {
      this.getNode("title_open_unlock_lbl").getComponent(Label).string = "解锁条件：";
      let key = db.source == "" ? "暂无获取方式" : db.source;
      this.getNode("title_open_unlock_lbl").getComponent(Label).string += key;
      this.getNode("title_open_unlock_lbl_op").getComponent(Label).string =
        this.getNode("title_open_unlock_lbl").getComponent(Label).string;
      this.isShowTitle_time();
      let itemdb = JsonMgr.instance.getConfigItem(db.id);
      this.getNode("title_open_attr_lbl").getComponent(Label).string = "";
      // this.getNode("title_open_attr_lbl").getComponent(Label).string = "解锁效果：" + itemdb.des;
    });
  }
  /**选中称号当前的底部按钮状态设置 */
  private setTitleState() {
    this.getNode("title_state_layer").children.forEach((val) => {
      val.active = false;
    });
    this.getNode("btn_title_use").active = false;
    if (this._cur_Pitch_titleId == this._cur_Use_titleId) {
      this.getNode("title_use_hit").active = true;
      return;
    }
    let data = PlayerModule.data.titleMap[this._cur_Pitch_titleId];
    if (!data) {
      this.getNode("title_no_open_hit").active = true;
      return;
    }
    this.getNode("btn_title_use").active = true;
  }
  /**是否展示称号剩余使用天数，和时间倒计时 */
  private isShowTitle_time() {
    let data = PlayerModule.data.titleMap[this._cur_Pitch_titleId];
    if (!data) {
      this.getNode("title_time_lay").active = false;
      return;
    }
    if (data.expireTime == -1) {
      this.getNode("title_time_lay").active = false;
      return;
    }
    this.getNode("title_time_lay").active = true;
    FmUtils.setCd(this.getNode("title_time_lab2"), data.expireTime);
  }
  private setTileMaxNum() {
    this.getNode("lbl_title_max_num").getComponent(Label).string =
      "已拥有：" +
      Object.keys(PlayerModule.data.titleMap).length +
      "/" +
      Object.keys(JsonMgr.instance.jsonList.c_title).length;
  }
  private on_click_btn_title_ins(event) {
    AudioMgr.instance.playEffect(PlayerAudioName.Effect.点击称号图标);
    let node = event.node;
    let titleId = node["titleId"];
    this._cur_Pitch_titleId = titleId;
    this._title_node_map.forEach((val, key) => {
      this.setUserPitch(val);
    });
    this.showPichTitle();
    this.setTitleState();
  }
  private on_click_btn_title_use(event) {
    AudioMgr.instance.playEffect(PlayerAudioName.Effect.点击装饰使用按钮);
    PlayerModule.api.workSkinOrDecoration(this._cur_Pitch_titleId, () => {
      this._cur_Pitch_titleId = PlayerModule.data.title?.titleId;
      this._cur_Use_titleId = PlayerModule.data.title?.titleId;
      this.showPichTitle();
      this.setTitleState();
      this._title_node_map.forEach((val, key) => {
        this.setUserPitch(val);
      });
      TipMgr.showTip("使用成功");
    });
  }
  on_click_btn_title_ljjc() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let obj: { [key: number]: number } = Object.create(null);
    let titleMap = PlayerModule.data.titleMap;
    let db = JsonMgr.instance.jsonList.c_title;
    for (let i in titleMap) {
      let info = db[titleMap[i].titleId];
      let attrAdd = info.attrAdd;
      for (let j = 0; j < attrAdd.length; j++) {
        if (!obj[attrAdd[j][0]]) {
          obj[attrAdd[j][0]] = 0;
        }
        obj[attrAdd[j][0]] += attrAdd[j][1];
      }
    }
    ResMgr.loadPrefab(
      `${BundleEnum.BUNDLE_G_COMMON_MAIN}?prefab/top/TipAttrAdd`,
      (prefab: Prefab) => {
        let node = instantiate(prefab);
        node.getComponent(TipAttrAdd).setAttr(obj, true);
        TipsMgr.showTipNode(node, -1);
      },
      this
    );
  }
  //================================称号=================================
  //装饰====================================================================================================================================================================================================================================
  private on_click_btn_close() {
    // 关闭窗口 effect_guanbiyinxiao
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
}
