import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
/**
 * 对象池管理器
 * 资源管理、创建、销毁
 */
export class PoolMgr<T> {
  // 可用池子列表
  private poolIdleList: T[] = [];

  // 闲置池子列表
  private poolUseList: T[] = [];

  // 最大数量
  private maxNum: number;

  // 创建对象方法
  private createOneFunc: () => Promise<T>;

  public constructor(createOneFunc: () => Promise<T>, maxNum: number, listInit?: T[]) {
    this.createOneFunc = createOneFunc;
    this.maxNum = maxNum;
    this.poolUseList = listInit || [];
  }

  /**
   * 获取一个对象
   * @returns 对象
   */
  public async getOne(): Promise<T> {
    let rs: T;
    if (this.poolIdleList.length > 0) {
      rs = this.poolIdleList.shift();
    } else if (this.poolUseList.length < this.maxNum) {
      rs = await this.createOneFunc();
    } else {
      throw new Error("池子已满");
    }
    this.poolUseList.push(rs);

    return rs;
  }

  /**
   * 回收一个对象
   * @param obj
   */
  public recycle(obj: T) {
    let idx = this.poolUseList.indexOf(obj);
    if (idx < 0) {
      log.log("回收对象不在使用池中");
    } else {
      this.poolUseList.splice(idx, 1);
    }
    this.poolIdleList.push(obj);
  }

  /**
   * 释放池子
   */
  public release(releaseFunc: (obj: T) => void) {
    this.poolIdleList.forEach((obj) => {
      releaseFunc && releaseFunc(obj);
    });
    this.poolUseList.forEach((obj) => {
      releaseFunc && releaseFunc(obj);
    });
    this.poolIdleList = [];
    this.poolUseList = [];
  }
}
