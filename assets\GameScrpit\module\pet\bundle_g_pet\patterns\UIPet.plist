<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>S0408.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{549,241}</string>
                <key>spriteSourceSize</key>
                <string>{549,241}</string>
                <key>textureRect</key>
                <string>{{1,1},{549,241}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S1126.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{51,61}</string>
                <key>spriteSourceSize</key>
                <string>{51,61}</string>
                <key>textureRect</key>
                <string>{{552,1},{51,61}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>S1127.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{50,60}</string>
                <key>spriteSourceSize</key>
                <string>{50,60}</string>
                <key>textureRect</key>
                <string>{{552,64},{50,60}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>UIPet.png</string>
            <key>size</key>
            <string>{604,243}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:b29d06200485c67f80793d1da8a8a77e:215e89e3bceb5432a6f32b2f99497bc3:c482b32da04a11d0c90f11a8e5aadee0$</string>
            <key>textureFileName</key>
            <string>UIPet.png</string>
        </dict>
    </dict>
</plist>
