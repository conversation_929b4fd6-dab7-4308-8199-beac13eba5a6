import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>lerF<PERSON>, ApiHandlerSuccess } from "../../game/mgr/ApiHandler";
import { ActivityCmd } from "../../game/net/cmd/CmdData";
import { SevenDaySignMessage, SevenDaySignResponse } from "../../game/net/protocol/Activity";
import { LongValue } from "../../game/net/protocol/ExternalMessage";
import { ActivityModule } from "../activity/ActivityModule";
import { HdSevenModule } from "./HdSevenModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class HdSevenApi {
  // 接口方法，数据模块在这里改好，然后通知上层，下面这个是个例子
  // public improveSkill(friendId: number, rank: number, consumeitem: boolean, success?: ApiHandlerSuccess) {
  //   let data: FriendCitySkillRequest = {
  //     friendId: friendId,
  //     rank: rank,
  //     consumeItem: consumeitem,
  //   };
  //   log.log("apiImproveSkill", friendId, rank, consumeitem);
  //   ApiHandler.instance.request(
  //     FriendCitySkillMessage,
  //     FriendSubCmd.improveSkill,
  //     FriendCitySkillRequest.encode(data),
  //     (data: FriendCitySkillMessage) => {
  //       log.log(data);
  //       FriendModule.data.updateFriendCitySkill(friendId, rank, data);
  //       success && success(data);
  //     },
  //     (errorCode: any, msg: string, data: any) => {
  //       log.log(`${errorCode}`);
  //       log.log(data);
  //     }
  //   );
  // }

  public sevenDaySign(id: number, success?: ApiHandlerSuccess) {
    let data: LongValue = {
      value: id,
    };
    ApiHandler.instance.request(
      SevenDaySignMessage,
      ActivityCmd.sevenDaySign,
      LongValue.encode(data),
      (data: SevenDaySignMessage) => {
        HdSevenModule.data.sevenDaySignMessage = data;
        success && success(data);
      }
    );
  }

  public takeSevenDayReward(id: number, success?: ApiHandlerSuccess) {
    let data: LongValue = {
      value: id,
    };

    ApiHandler.instance.request(
      SevenDaySignResponse,
      ActivityCmd.takeSevenDayReward,
      LongValue.encode(data),
      (data: SevenDaySignResponse) => {
        HdSevenModule.data.sevenDaySignMessage = data.sevenDaySign;
        success && success(data);
      }
    );
  }
}
