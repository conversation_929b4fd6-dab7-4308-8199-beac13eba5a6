import { _decorator, Component, Node, sp, Tween, tween, v3 } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { tweenTagEnum } from "../../GameDefine";
import ToolExt from "../../common/ToolExt";
import FmUtils from "../../../lib/utils/FmUtils";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { AudioMgr, AudioName } from "../../../../platform/src/AudioHelper";
import { HuntAudioName } from "../../../module/hunt/HuntConfig";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
const { ccclass, property } = _decorator;

@ccclass("UIPrepareWin")
export class UIPrepareWin extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_HUNT}?prefab/ui/UIPrepareWin`;
  }

  private _award: any = null;

  public init(args: any): void {
    super.init(args);
    this._award = args.resAddList;
  }

  protected onEvtShow(): void {
    TipsMgr.setEnableTouch(true);
    this.playeAction();
    AudioMgr.instance.playEffect(HuntAudioName.Effect.成功击杀);
  }

  private playeAction() {
    this.getNode("bg_spr").scale = v3(0, 0, 0);
    this.getNode("tytanchuang")
      .getComponent(sp.Skeleton)
      .setCompleteListener(() => {
        this.getNode("tytanchuang").getComponent(sp.Skeleton).setAnimation(0, "zi_chenggongjisha_1", true);
      });
    this.getNode("tytanchuang").getComponent(sp.Skeleton).setAnimation(0, "zi_chenggongjisha", false);
    tween(this.getNode("bg_spr"))
      .tag(tweenTagEnum.UIHuntBossFinish_Tag)
      .to(0.1, { scale: v3(1, 1, 1) })
      .call(() => {
        let list = ToolExt.traAwardItemMapList(this._award);
        this.loadItem(list);
      })
      .start();
  }

  private loadItem(layerList: Array<{ id: number; num: number }>) {
    for (let i = 0; i < layerList.length; i++) {
      let node = ToolExt.clone(this.getNode("item_top"), this);
      node.active = true;
      node["Item"].active = false;
      this.getNode("itemContent").addChild(node);

      this.itemAct(node["Item"], layerList[i].id, layerList[i].num, i);
    }
  }

  private itemAct(node: Node, id: number, num: number, index) {
    FmUtils.setItemNode(node, id, num);
    node.scale = v3(0, 0, 0);
    node.active = true;
    tween(node)
      .tag(tweenTagEnum.UIHuntBossFinish_Tag)
      .delay(0.08 * index)
      .to(0.16, { scale: v3(1, 1, 1) })
      .start();
  }

  private on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  protected onEvtClose(): void {
    Tween.stopAllByTag(tweenTagEnum.UIHuntBossFinish_Tag);
  }
}
