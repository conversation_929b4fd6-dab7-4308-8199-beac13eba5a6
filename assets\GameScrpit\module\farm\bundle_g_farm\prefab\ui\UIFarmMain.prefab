[{"__type__": "cc.Prefab", "_name": "UIFarmMain", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "UIFarmMain", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [{"__id__": 952}], "_prefab": {"__id__": 954}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 3}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 2}, "asset": {"__uuid__": "83425a7d-905e-4982-b252-c50b3e262134", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 4}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "80tY2wGJhPML6vl/fUNi0j", "prefabRootNode": {"__id__": 1}, "mountedChildren": [{"__id__": 5}, {"__id__": 887}], "mountedComponents": [], "propertyOverrides": [{"__id__": 924}, {"__id__": 926}, {"__id__": 927}, {"__id__": 928}, {"__id__": 929}, {"__id__": 931}, {"__id__": 933}, {"__id__": 935}, {"__id__": 937}, {"__id__": 939}, {"__id__": 940}, {"__id__": 942}, {"__id__": 944}, {"__id__": 946}, {"__id__": 947}, {"__id__": 948}, {"__id__": 950}], "removedComponents": []}, {"__type__": "cc.MountedChildrenInfo", "targetInfo": {"__id__": 6}, "nodes": [{"__id__": 7}, {"__id__": 9}, {"__id__": 857}]}, {"__type__": "cc.TargetInfo", "localID": ["9ffoAr1CFKQoFFCc+XwXS3"]}, {"__type__": "cc.Node", "_name": "BG_dongtianhulu_2", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 2}}, "_parent": {"__id__": 8}, "_children": [{"__id__": 878}], "_active": true, "_components": [{"__id__": 884}], "_prefab": {"__id__": 886}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "dialog_content", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 7}, {"__id__": 9}, {"__id__": 857}], "_active": true, "_components": [{"__id__": 869}, {"__id__": 871}, {"__id__": 873}, {"__id__": 875}], "_prefab": {"__id__": 877}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -77.87349999999992, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "dialog_content", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 2}}, "_parent": {"__id__": 8}, "_children": [{"__id__": 10}, {"__id__": 46}, {"__id__": 696}], "_active": true, "_components": [{"__id__": 852}, {"__id__": 854}], "_prefab": {"__id__": 856}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -1, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "node_refresh", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 9}, "_children": [{"__id__": 11}, {"__id__": 17}], "_active": false, "_components": [{"__id__": 43}], "_prefab": {"__id__": 45}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_lanse<PERSON>_jianbian", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 10}, "_children": [], "_active": true, "_components": [{"__id__": 12}, {"__id__": 14}], "_prefab": {"__id__": 16}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 426.7739999999999, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": true, "__prefab": {"__id__": 13}, "_contentSize": {"__type__": "cc.Size", "width": 450, "height": 38}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dfj7KcG8tHhJ+YBgIWSo96"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": true, "__prefab": {"__id__": 15}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "7fc2d4e8-5549-462e-bfa5-4a7d7e5dae24@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cekMwv+OpL/61MWWiDzke5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "35uF9CyZJKapwifoLqOcEE", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Layout", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 10}, "_children": [{"__id__": 18}, {"__id__": 24}], "_active": true, "_components": [{"__id__": 38}, {"__id__": 40}], "_prefab": {"__id__": 42}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lbl_next_refresh", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 17}, "_children": [], "_active": true, "_components": [{"__id__": 19}, {"__id__": 21}], "_prefab": {"__id__": 23}, "_lpos": {"__type__": "cc.Vec3", "x": -31.139976501464844, "y": 426.7739999999999, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 20}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "66k7CuyoBL5JBS6EmzSNhh"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 22}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "下次刷新时间", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e4Ezkr4U1L4Jx/BZCWXx88"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c92qQbHkFDBLCTOrN+UGub", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 17}, "_prefab": {"__id__": 25}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 24}, "asset": {"__uuid__": "ddfa3a35-1513-4b06-970d-3018496ac69e", "__expectedType__": "cc.Prefab"}, "fileId": "ddw48bgTVJQaI4eRLCZMBo", "instance": {"__id__": 26}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "5bsj670iRISKBZXjYEn5Eb", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 27}, {"__id__": 29}, {"__id__": 30}, {"__id__": 31}, {"__id__": 32}, {"__id__": 34}, {"__id__": 36}, {"__id__": 37}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 28}, "propertyPath": ["_name"], "value": "lbl_refresh_cd"}, {"__type__": "cc.TargetInfo", "localID": ["ddw48bgTVJQaI4eRLCZMBo"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 28}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 90, "y": 426.7739999999999, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 28}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 28}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 33}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 62.27995300292969, "height": 50.4}}, {"__type__": "cc.TargetInfo", "localID": ["94RD/5HzNB4bdksbVCxbeX"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 35}, "propertyPath": ["_actualFontSize"], "value": 31.575}, {"__type__": "cc.TargetInfo", "localID": ["73icu5TNJLZaYTvRIxQDjK"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 35}, "propertyPath": ["_enableOutline"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 35}, "propertyPath": ["_fontSize"], "value": 30}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 17}, "_enabled": true, "__prefab": {"__id__": 39}, "_contentSize": {"__type__": "cc.Size", "width": 242.2799530029297, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2ellhWM2JJRokIDtJjl7Tc"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 17}, "_enabled": true, "__prefab": {"__id__": 41}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "98MXeVC15Fi6NMmZwgJpCx"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f2IxE6jHBNKpnfSqT0377k", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 10}, "_enabled": true, "__prefab": {"__id__": 44}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b5GxRPaoxLJ5WlabwtpaFA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "390ikacXdBbYdweHqJVvZH", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "scene", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 9}, "_children": [{"__id__": 47}, {"__id__": 181}, {"__id__": 309}, {"__id__": 437}, {"__id__": 565}], "_active": true, "_components": [{"__id__": 693}], "_prefab": {"__id__": 695}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "p1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 46}, "_children": [{"__id__": 48}, {"__id__": 94}, {"__id__": 140}, {"__id__": 163}], "_active": false, "_components": [{"__id__": 178}], "_prefab": {"__id__": 180}, "_lpos": {"__type__": "cc.Vec3", "x": -240, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "houzi_2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 47}, "_children": [{"__id__": 49}, {"__id__": 55}, {"__id__": 61}, {"__id__": 67}, {"__id__": 73}], "_active": true, "_components": [{"__id__": 91}], "_prefab": {"__id__": 93}, "_lpos": {"__type__": "cc.Vec3", "x": 3.124, "y": 210.45, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": -1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "ani_houzi", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 48}, "_children": [], "_active": true, "_components": [{"__id__": 50}, {"__id__": 52}], "_prefab": {"__id__": 54}, "_lpos": {"__type__": "cc.Vec3", "x": -21.671, "y": -51.174, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 49}, "_enabled": true, "__prefab": {"__id__": 51}, "_contentSize": {"__type__": "cc.Size", "width": 119, "height": 130.49000549316406}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.6867226993336397, "y": 0.1132653775062142}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dfJTJYSQVF45WtQr+aMRRH"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 49}, "_enabled": true, "__prefab": {"__id__": 53}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "2577d58c-43f0-4b90-a7e2-0ee43f24bb6f", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "animation2", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "25bXK30EJB+Km1W2dtWRfe"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "34gDfjy/dIirVup+7NpuaE", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ani_baibaodai", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 48}, "_children": [], "_active": true, "_components": [{"__id__": 56}, {"__id__": 58}], "_prefab": {"__id__": 60}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 55}, "_enabled": true, "__prefab": {"__id__": 57}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fakHWMzyFCZ5ZUPRsfvhN2"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 55}, "_enabled": true, "__prefab": {"__id__": 59}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "93bfeae4-cccb-441b-933c-09ce71f1073c", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "animtion2", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8fDc4+NGhEbZ6ZLPPfztnd"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b7zTAziWdAmLMEwf4ouCOZ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ani_xi", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 48}, "_children": [], "_active": true, "_components": [{"__id__": 62}, {"__id__": 64}], "_prefab": {"__id__": 66}, "_lpos": {"__type__": "cc.Vec3", "x": 3.1239999999999952, "y": -7.52800000000002, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 6.123233995736766e-17, "y": 6.123233995736766e-17, "z": -1, "w": 3.749399456654644e-33}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 180, "y": 180, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 61}, "_enabled": true, "__prefab": {"__id__": 63}, "_contentSize": {"__type__": "cc.Size", "width": 230.4600067138672, "height": 555.8699951171875}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.3650133946404309}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7bpbhoGSVPAarlFmB5FNG7"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 61}, "_enabled": true, "__prefab": {"__id__": 65}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "6ffa53fc-3c39-4fd5-b754-6812a429d274", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "animation2", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a6NZ0bMs1AA4FTS8FUD/Lt"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "52/7Id5QBH6Y7x764mPNtQ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_self", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 48}, "_children": [], "_active": true, "_components": [{"__id__": 68}, {"__id__": 70}], "_prefab": {"__id__": 72}, "_lpos": {"__type__": "cc.Vec3", "x": -49.93, "y": 68.641, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": -1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 67}, "_enabled": true, "__prefab": {"__id__": 69}, "_contentSize": {"__type__": "cc.Size", "width": 28.99204796334708, "height": 32.98}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "450BiwENRAJbXOXkcMXZ4r"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 67}, "_enabled": true, "__prefab": {"__id__": 71}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 124, "g": 242, "b": 72, "a": 255}, "_string": "我", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26.208333333333332, "_fontSize": 25, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 23, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 48, "g": 103, "b": 180, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "52/gytROtNA4LivYvc2/+p"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "83AWUjEwtJWKCD4091KdYd", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "img_qipao", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 48}, "_children": [{"__id__": 74}, {"__id__": 80}], "_active": true, "_components": [{"__id__": 86}, {"__id__": 88}], "_prefab": {"__id__": 90}, "_lpos": {"__type__": "cc.Vec3", "x": 16.655, "y": 68.39, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "img_emoji_cry", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 73}, "_children": [], "_active": true, "_components": [{"__id__": 75}, {"__id__": 77}], "_prefab": {"__id__": 79}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 74}, "_enabled": true, "__prefab": {"__id__": 76}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "85U4TMuuhAjLqo2cOfsMDR"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 74}, "_enabled": true, "__prefab": {"__id__": 78}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "60adef43-67ee-4fe4-aab9-8e953aa24102@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "20a7oxKPJNZYgpnaKr7WkG"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "74tRgXwzVKgKYWRjTtPxwf", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "img_emoji_smile", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 73}, "_children": [], "_active": true, "_components": [{"__id__": 81}, {"__id__": 83}], "_prefab": {"__id__": 85}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 80}, "_enabled": true, "__prefab": {"__id__": 82}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "18KvEfZ3lNo6RI6kRKD278"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 80}, "_enabled": true, "__prefab": {"__id__": 84}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b2b368d6-0116-49df-b247-2071ce06562a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "64lmlQzQJHqLXjU2DDVUQ2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2dn09iurVFA5GKQESLtZD6", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 73}, "_enabled": true, "__prefab": {"__id__": 87}, "_contentSize": {"__type__": "cc.Size", "width": 72, "height": 54}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "75frlUZflKO6ucjdEdFseH"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 73}, "_enabled": true, "__prefab": {"__id__": 89}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "201519a4-0749-4c1d-b9b6-215b22f244ab@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "celfocGetEV6t9m87uM42c"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "35qg0UU39Ch5wPw1HttbX2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 48}, "_enabled": true, "__prefab": {"__id__": 92}, "_contentSize": {"__type__": "cc.Size", "width": 91, "height": 110}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "62X49ozB9AhZ0JBT7fyI26"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e6suSx+wpKe5ssS4D3kvA3", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "houzi_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 47}, "_children": [{"__id__": 95}, {"__id__": 101}, {"__id__": 107}, {"__id__": 113}, {"__id__": 119}], "_active": true, "_components": [{"__id__": 137}], "_prefab": {"__id__": 139}, "_lpos": {"__type__": "cc.Vec3", "x": -2.261, "y": -243.154, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "ani_houzi", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 94}, "_children": [], "_active": true, "_components": [{"__id__": 96}, {"__id__": 98}], "_prefab": {"__id__": 100}, "_lpos": {"__type__": "cc.Vec3", "x": -21.242, "y": -35.46, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 95}, "_enabled": true, "__prefab": {"__id__": 97}, "_contentSize": {"__type__": "cc.Size", "width": 119, "height": 130.49000549316406}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.6867226993336397, "y": 0.1132653775062142}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9d6GeOgjFHyIs7q0InSSLf"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 95}, "_enabled": true, "__prefab": {"__id__": 99}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "2577d58c-43f0-4b90-a7e2-0ee43f24bb6f", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "animation2", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "63XnNW3zVIsKdlFqkxoUL8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "94KI1048ZGlJGQ5ZjpBQTc", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ani_baibaodai", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 94}, "_children": [], "_active": true, "_components": [{"__id__": 102}, {"__id__": 104}], "_prefab": {"__id__": 106}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 101}, "_enabled": true, "__prefab": {"__id__": 103}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0dCVlaFQxOK4/7Qrc599aw"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 101}, "_enabled": true, "__prefab": {"__id__": 105}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "93bfeae4-cccb-441b-933c-09ce71f1073c", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "animtion1", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "36lbZ+JthJ+r3ozXeyU0Vk"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1baZ2ILJNLPoG63fDAcbvl", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ani_xi", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 94}, "_children": [], "_active": true, "_components": [{"__id__": 108}, {"__id__": 110}], "_prefab": {"__id__": 112}, "_lpos": {"__type__": "cc.Vec3", "x": 2.2609999999999957, "y": 5.548000000000059, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 107}, "_enabled": true, "__prefab": {"__id__": 109}, "_contentSize": {"__type__": "cc.Size", "width": 230.4600067138672, "height": 555.8699951171875}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.3650133946404309}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2f+ipltORIfb2FtEWpnyaM"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 107}, "_enabled": true, "__prefab": {"__id__": 111}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "6ffa53fc-3c39-4fd5-b754-6812a429d274", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "animation2", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2ea8+VroNCi5S3DlC/HBtG"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d1heBz17lL5otPSoMsRKwS", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_self", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 94}, "_children": [], "_active": true, "_components": [{"__id__": 114}, {"__id__": 116}], "_prefab": {"__id__": 118}, "_lpos": {"__type__": "cc.Vec3", "x": -34.321, "y": 82.572, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 113}, "_enabled": true, "__prefab": {"__id__": 115}, "_contentSize": {"__type__": "cc.Size", "width": 28.99204796334708, "height": 32.98}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "27q/61AutAcaK2ktPThcpU"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 113}, "_enabled": true, "__prefab": {"__id__": 117}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 124, "g": 242, "b": 72, "a": 255}, "_string": "我", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26.208333333333332, "_fontSize": 25, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 23, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 48, "g": 103, "b": 180, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "50pwuOHPBGL4n7Zq/mwtkU"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "96/xyjV3ZLjLk2cbasUzEI", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "img_qipao", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 94}, "_children": [{"__id__": 120}, {"__id__": 126}], "_active": true, "_components": [{"__id__": 132}, {"__id__": 134}], "_prefab": {"__id__": 136}, "_lpos": {"__type__": "cc.Vec3", "x": -23.918, "y": 63.74, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "img_emoji_cry", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 119}, "_children": [], "_active": true, "_components": [{"__id__": 121}, {"__id__": 123}], "_prefab": {"__id__": 125}, "_lpos": {"__type__": "cc.Vec3", "x": 36.605, "y": 27.921, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 120}, "_enabled": true, "__prefab": {"__id__": 122}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0etxt6ZEhI9IkuAnL+iQ07"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 120}, "_enabled": true, "__prefab": {"__id__": 124}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "60adef43-67ee-4fe4-aab9-8e953aa24102@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f2318pYYFAK4TyofB5nMMO"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "69yQ6ItclEYYlmGvy0VOEp", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "img_emoji_smile", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 119}, "_children": [], "_active": true, "_components": [{"__id__": 127}, {"__id__": 129}], "_prefab": {"__id__": 131}, "_lpos": {"__type__": "cc.Vec3", "x": 36.605, "y": 27.921, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 126}, "_enabled": true, "__prefab": {"__id__": 128}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "14P/wnzudK9bxITMrvmrz5"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 126}, "_enabled": true, "__prefab": {"__id__": 130}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b2b368d6-0116-49df-b247-2071ce06562a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "45EB0pCbJADKD+LdPhqn1M"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2a2qrajf1OvpO3NWabiU3+", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 119}, "_enabled": true, "__prefab": {"__id__": 133}, "_contentSize": {"__type__": "cc.Size", "width": 72, "height": 54}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cc7N7KGU1Ps7AGwW+N5mBw"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 119}, "_enabled": true, "__prefab": {"__id__": 135}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "201519a4-0749-4c1d-b9b6-215b22f244ab@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "21XPzvpqBIbqRih0asM5kE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "65ULIaODlO76I/WvxPAdBn", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 94}, "_enabled": true, "__prefab": {"__id__": 138}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "efYPxoKDVEJKvEoGElFCZl"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f3SkAsLr1Ej5LdmCQm0rMJ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 47}, "_prefab": {"__id__": 141}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 140}, "asset": {"__uuid__": "ebe26d9f-9d41-4412-a791-b043cb06c858", "__expectedType__": "cc.Prefab"}, "fileId": "96elUVidxPRqijnRGfo8vE", "instance": {"__id__": 142}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "eaYVc/2l1FwoQXHifId1Zo", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 143}, {"__id__": 145}, {"__id__": 146}, {"__id__": 147}, {"__id__": 148}, {"__id__": 150}, {"__id__": 152}, {"__id__": 153}, {"__id__": 155}, {"__id__": 156}, {"__id__": 158}, {"__id__": 159}, {"__id__": 161}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 144}, "propertyPath": ["_name"], "value": "hulu"}, {"__type__": "cc.TargetInfo", "localID": ["96elUVidxPRqijnRGfo8vE"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 144}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 144}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 144}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 149}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["ddAQ5xzpdMtYI/eBlxjBY6"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 151}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["52PTmDLKRBQ5mCweNJHDzg"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 151}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -2.359, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 154}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["f4T4X9+ypGzIW72M5/9sfX"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 144}, "propertyPath": ["_active"], "value": true}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 157}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["01Zf2mXShLmbeTh6rpky1u"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 157}, "propertyPath": ["_name"], "value": "ani_bg"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 160}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["25z1KwwItB/6CBXUmwcqnq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 162}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 36.49598080099861, "height": 34.24}}, {"__type__": "cc.TargetInfo", "localID": ["85QU562IhLooxJJeUfFnfs"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 47}, "_prefab": {"__id__": 164}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 163}, "asset": {"__uuid__": "ddfa3a35-1513-4b06-970d-3018496ac69e", "__expectedType__": "cc.Prefab"}, "fileId": "ddw48bgTVJQaI4eRLCZMBo", "instance": {"__id__": 165}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "94o4tWZBpBTIm2O8XNU0p2", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 166}, {"__id__": 168}, {"__id__": 170}, {"__id__": 172}, {"__id__": 174}, {"__id__": 176}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 167}, "propertyPath": ["_name"], "value": "lbl_cd"}, {"__type__": "cc.TargetInfo", "localID": ["ddw48bgTVJQaI4eRLCZMBo"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 169}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -3.989, "y": -51, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["ddw48bgTVJQaI4eRLCZMBo"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 171}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["ddw48bgTVJQaI4eRLCZMBo"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 173}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["ddw48bgTVJQaI4eRLCZMBo"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 175}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 45.506763937363374, "height": 54.400000000000006}}, {"__type__": "cc.TargetInfo", "localID": ["94RD/5HzNB4bdksbVCxbeX"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 177}, "propertyPath": ["_active"], "value": true}, {"__type__": "cc.TargetInfo", "localID": ["ddw48bgTVJQaI4eRLCZMBo"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 47}, "_enabled": true, "__prefab": {"__id__": 179}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d9m9EQsSNFcqDkGo9TLe4W"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "556IlwvN9Jb6pQG8JFItUr", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "p2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 46}, "_children": [{"__id__": 182}, {"__id__": 228}, {"__id__": 274}, {"__id__": 295}], "_active": false, "_components": [{"__id__": 306}], "_prefab": {"__id__": 308}, "_lpos": {"__type__": "cc.Vec3", "x": -120, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "houzi_2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 181}, "_children": [{"__id__": 183}, {"__id__": 189}, {"__id__": 195}, {"__id__": 201}, {"__id__": 207}], "_active": true, "_components": [{"__id__": 225}], "_prefab": {"__id__": 227}, "_lpos": {"__type__": "cc.Vec3", "x": 3.124, "y": 210.45, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": -1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "ani_houzi", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 182}, "_children": [], "_active": true, "_components": [{"__id__": 184}, {"__id__": 186}], "_prefab": {"__id__": 188}, "_lpos": {"__type__": "cc.Vec3", "x": -21.671, "y": -51.174, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 183}, "_enabled": true, "__prefab": {"__id__": 185}, "_contentSize": {"__type__": "cc.Size", "width": 119, "height": 130.49000549316406}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.6867226993336397, "y": 0.1132653775062142}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "493XqUGMZE5rWXBLMkhYLR"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 183}, "_enabled": true, "__prefab": {"__id__": 187}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "2577d58c-43f0-4b90-a7e2-0ee43f24bb6f", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "animation2", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8bxwCdntZPFa1/FDqsMbTa"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9ejHH7PIpHx5v7xMcyuaHl", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ani_baibaodai", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 182}, "_children": [], "_active": true, "_components": [{"__id__": 190}, {"__id__": 192}], "_prefab": {"__id__": 194}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 189}, "_enabled": true, "__prefab": {"__id__": 191}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f0o3fExpNHcoey0NU3kHtj"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 189}, "_enabled": true, "__prefab": {"__id__": 193}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "93bfeae4-cccb-441b-933c-09ce71f1073c", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "animtion2", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5dw0B5DjtKCoj7NTezM80H"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ecEU1EB+1Ph6lJ2B3gVD4n", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ani_xi", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 182}, "_children": [], "_active": true, "_components": [{"__id__": 196}, {"__id__": 198}], "_prefab": {"__id__": 200}, "_lpos": {"__type__": "cc.Vec3", "x": 3.1239999999999952, "y": -7.52800000000002, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 6.123233995736766e-17, "y": 6.123233995736766e-17, "z": -1, "w": 3.749399456654644e-33}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 180, "y": 180, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 195}, "_enabled": true, "__prefab": {"__id__": 197}, "_contentSize": {"__type__": "cc.Size", "width": 230.4600067138672, "height": 555.8699951171875}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.3650133946404309}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "64HgLc+P5IlYzSYXh33t+X"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 195}, "_enabled": true, "__prefab": {"__id__": 199}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "6ffa53fc-3c39-4fd5-b754-6812a429d274", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "animation2", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "70wxHOPbNMcJQ16eKQw7gN"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "914wAh3dhKRaVaTaFSudHo", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_self", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 182}, "_children": [], "_active": true, "_components": [{"__id__": 202}, {"__id__": 204}], "_prefab": {"__id__": 206}, "_lpos": {"__type__": "cc.Vec3", "x": -49.93, "y": 68.641, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": -1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 201}, "_enabled": true, "__prefab": {"__id__": 203}, "_contentSize": {"__type__": "cc.Size", "width": 28.99204796334708, "height": 32.98}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f6ZOcdJa5LyaXbDJINT8Kv"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 201}, "_enabled": true, "__prefab": {"__id__": 205}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 124, "g": 242, "b": 72, "a": 255}, "_string": "我", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26.208333333333332, "_fontSize": 25, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 23, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 48, "g": 103, "b": 180, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "35NICFL7xHn5+9JFjxl3ru"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "edj+emkMxCWYI/JCT3QcYZ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "img_qipao", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 182}, "_children": [{"__id__": 208}, {"__id__": 214}], "_active": true, "_components": [{"__id__": 220}, {"__id__": 222}], "_prefab": {"__id__": 224}, "_lpos": {"__type__": "cc.Vec3", "x": 16.655, "y": 68.39, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "img_emoji_cry", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 207}, "_children": [], "_active": true, "_components": [{"__id__": 209}, {"__id__": 211}], "_prefab": {"__id__": 213}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 208}, "_enabled": true, "__prefab": {"__id__": 210}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "06EOlBaSRLA6gd0V7xBa5e"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 208}, "_enabled": true, "__prefab": {"__id__": 212}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "60adef43-67ee-4fe4-aab9-8e953aa24102@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a2KWu3RARJVaJrzuJZTCCX"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d0IoBgf9lA0oVx/s6sl3VV", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "img_emoji_smile", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 207}, "_children": [], "_active": true, "_components": [{"__id__": 215}, {"__id__": 217}], "_prefab": {"__id__": 219}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 214}, "_enabled": true, "__prefab": {"__id__": 216}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ea10sefMJKdoyedhl+aMDo"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 214}, "_enabled": true, "__prefab": {"__id__": 218}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b2b368d6-0116-49df-b247-2071ce06562a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2fne2/R9BPqIJAATf6Ka3R"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dcT2jhlNNJ+4qpf6/MJc93", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 207}, "_enabled": true, "__prefab": {"__id__": 221}, "_contentSize": {"__type__": "cc.Size", "width": 72, "height": 54}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f8t5b7EARIdZjiJwIf7SqA"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 207}, "_enabled": true, "__prefab": {"__id__": 223}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "201519a4-0749-4c1d-b9b6-215b22f244ab@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "84ZwNhZK5AUK/jcD01GELL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "49O5jNP6BB6YZ05tyaWLL7", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 182}, "_enabled": true, "__prefab": {"__id__": 226}, "_contentSize": {"__type__": "cc.Size", "width": 91, "height": 110}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "78hj86WLRLgafrAg4J6eWY"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2f+4m7nxdEmIJu+zO4SIt4", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "houzi_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 181}, "_children": [{"__id__": 229}, {"__id__": 235}, {"__id__": 241}, {"__id__": 247}, {"__id__": 253}], "_active": true, "_components": [{"__id__": 271}], "_prefab": {"__id__": 273}, "_lpos": {"__type__": "cc.Vec3", "x": -2.261, "y": -243.154, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "ani_houzi", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 228}, "_children": [], "_active": true, "_components": [{"__id__": 230}, {"__id__": 232}], "_prefab": {"__id__": 234}, "_lpos": {"__type__": "cc.Vec3", "x": -21.242, "y": -35.46, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 229}, "_enabled": true, "__prefab": {"__id__": 231}, "_contentSize": {"__type__": "cc.Size", "width": 119, "height": 130.49000549316406}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.6867226993336397, "y": 0.1132653775062142}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8bW55vZ5dIL6bO8+WXJURN"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 229}, "_enabled": true, "__prefab": {"__id__": 233}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "2577d58c-43f0-4b90-a7e2-0ee43f24bb6f", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "animation2", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4exKFhUkRGbaOPMu2/dViE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c4+R+UhPRNIbSBxRG/JJPh", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ani_baibaodai", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 228}, "_children": [], "_active": true, "_components": [{"__id__": 236}, {"__id__": 238}], "_prefab": {"__id__": 240}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 235}, "_enabled": true, "__prefab": {"__id__": 237}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ca4gdeAMtGoIbsFy4NKUXt"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 235}, "_enabled": true, "__prefab": {"__id__": 239}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "93bfeae4-cccb-441b-933c-09ce71f1073c", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "animtion1", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0bATTv8uJLBZs7zfvVsssm"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ceb3FiOgVF37/6LstdriQ2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ani_xi", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 228}, "_children": [], "_active": true, "_components": [{"__id__": 242}, {"__id__": 244}], "_prefab": {"__id__": 246}, "_lpos": {"__type__": "cc.Vec3", "x": 2.2609999999999957, "y": 5.548000000000059, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 241}, "_enabled": true, "__prefab": {"__id__": 243}, "_contentSize": {"__type__": "cc.Size", "width": 230.4600067138672, "height": 555.8699951171875}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.3650133946404309}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d2EzFd671G5J6p/yG/s3ES"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 241}, "_enabled": true, "__prefab": {"__id__": 245}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "6ffa53fc-3c39-4fd5-b754-6812a429d274", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "animation2", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2bDQhq0FtGnY0qV0CUZh1Y"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dfXqf5knVF+YJtO+Wfzw5t", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_self", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 228}, "_children": [], "_active": true, "_components": [{"__id__": 248}, {"__id__": 250}], "_prefab": {"__id__": 252}, "_lpos": {"__type__": "cc.Vec3", "x": -34.321, "y": 82.572, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 247}, "_enabled": true, "__prefab": {"__id__": 249}, "_contentSize": {"__type__": "cc.Size", "width": 28.99204796334708, "height": 32.98}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "83voB4x+xMIL/+NAF+QUNV"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 247}, "_enabled": true, "__prefab": {"__id__": 251}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 124, "g": 242, "b": 72, "a": 255}, "_string": "我", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26.208333333333332, "_fontSize": 25, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 23, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 48, "g": 103, "b": 180, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e2bgEfQhFIS4W7pLjsL+Tj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "25OcKFJ3lJuLWUJ10Lfp/X", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "img_qipao", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 228}, "_children": [{"__id__": 254}, {"__id__": 260}], "_active": true, "_components": [{"__id__": 266}, {"__id__": 268}], "_prefab": {"__id__": 270}, "_lpos": {"__type__": "cc.Vec3", "x": -23.918, "y": 63.74, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "img_emoji_cry", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 253}, "_children": [], "_active": true, "_components": [{"__id__": 255}, {"__id__": 257}], "_prefab": {"__id__": 259}, "_lpos": {"__type__": "cc.Vec3", "x": 36.605, "y": 27.921, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 254}, "_enabled": true, "__prefab": {"__id__": 256}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6fiBfSVdFAWK4nNph9384a"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 254}, "_enabled": true, "__prefab": {"__id__": 258}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "60adef43-67ee-4fe4-aab9-8e953aa24102@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0aZSzip/tLSJqvDZTk06wZ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6d/HzpHhRCUryi0+ltkewf", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "img_emoji_smile", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 253}, "_children": [], "_active": true, "_components": [{"__id__": 261}, {"__id__": 263}], "_prefab": {"__id__": 265}, "_lpos": {"__type__": "cc.Vec3", "x": 36.605, "y": 27.921, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 260}, "_enabled": true, "__prefab": {"__id__": 262}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "91pjeDfcJIm4b8Iz2D7+xc"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 260}, "_enabled": true, "__prefab": {"__id__": 264}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b2b368d6-0116-49df-b247-2071ce06562a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "11ZVv9zkhFEru0wP6jgocY"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "30lqv9kzFKTITxb7+Gdtke", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 253}, "_enabled": true, "__prefab": {"__id__": 267}, "_contentSize": {"__type__": "cc.Size", "width": 72, "height": 54}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eeiqofl1xFy6PlQ/fx/Ocp"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 253}, "_enabled": true, "__prefab": {"__id__": 269}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "201519a4-0749-4c1d-b9b6-215b22f244ab@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4dFhG2vlRAh6JkI6SrSm5z"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fbtiQzFolIsKY6eyebWwCc", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 228}, "_enabled": true, "__prefab": {"__id__": 272}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6cV+OThitJC5KtmOZHqNeW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "88OcVg0aVD/JEBnK+XoLTk", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 181}, "_prefab": {"__id__": 275}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 274}, "asset": {"__uuid__": "ebe26d9f-9d41-4412-a791-b043cb06c858", "__expectedType__": "cc.Prefab"}, "fileId": "96elUVidxPRqijnRGfo8vE", "instance": {"__id__": 276}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "2emPDsHVVDlojATAUeOSke", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 277}, {"__id__": 279}, {"__id__": 280}, {"__id__": 281}, {"__id__": 282}, {"__id__": 284}, {"__id__": 286}, {"__id__": 287}, {"__id__": 289}, {"__id__": 290}, {"__id__": 292}, {"__id__": 293}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 278}, "propertyPath": ["_name"], "value": "hulu"}, {"__type__": "cc.TargetInfo", "localID": ["96elUVidxPRqijnRGfo8vE"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 278}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 278}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 278}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 283}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["ddAQ5xzpdMtYI/eBlxjBY6"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 285}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["52PTmDLKRBQ5mCweNJHDzg"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 285}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -2.359, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 288}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["f4T4X9+ypGzIW72M5/9sfX"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 278}, "propertyPath": ["_active"], "value": true}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 291}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["01Zf2mXShLmbeTh6rpky1u"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 291}, "propertyPath": ["_name"], "value": "ani_bg"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 294}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 36.49598080099861, "height": 34.24}}, {"__type__": "cc.TargetInfo", "localID": ["85QU562IhLooxJJeUfFnfs"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 181}, "_prefab": {"__id__": 296}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 295}, "asset": {"__uuid__": "ddfa3a35-1513-4b06-970d-3018496ac69e", "__expectedType__": "cc.Prefab"}, "fileId": "ddw48bgTVJQaI4eRLCZMBo", "instance": {"__id__": 297}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "84TZbZyB9J6LSbOBzzm1uj", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 298}, {"__id__": 300}, {"__id__": 301}, {"__id__": 302}, {"__id__": 303}, {"__id__": 305}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 299}, "propertyPath": ["_name"], "value": "lbl_cd"}, {"__type__": "cc.TargetInfo", "localID": ["ddw48bgTVJQaI4eRLCZMBo"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 299}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -3.989, "y": -51, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 299}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 299}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 304}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 45.506763937363374, "height": 54.400000000000006}}, {"__type__": "cc.TargetInfo", "localID": ["94RD/5HzNB4bdksbVCxbeX"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 299}, "propertyPath": ["_active"], "value": true}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 181}, "_enabled": true, "__prefab": {"__id__": 307}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f8lG4eC39BUburm75IMAqP"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a3OodjDK5PyamvoOMhBr94", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "p3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 46}, "_children": [{"__id__": 310}, {"__id__": 356}, {"__id__": 402}, {"__id__": 423}], "_active": false, "_components": [{"__id__": 434}], "_prefab": {"__id__": 436}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "houzi_2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 309}, "_children": [{"__id__": 311}, {"__id__": 317}, {"__id__": 323}, {"__id__": 329}, {"__id__": 335}], "_active": true, "_components": [{"__id__": 353}], "_prefab": {"__id__": 355}, "_lpos": {"__type__": "cc.Vec3", "x": 3.124, "y": 210.45, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": -1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "ani_houzi", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 310}, "_children": [], "_active": true, "_components": [{"__id__": 312}, {"__id__": 314}], "_prefab": {"__id__": 316}, "_lpos": {"__type__": "cc.Vec3", "x": -21.671, "y": -51.174, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 311}, "_enabled": true, "__prefab": {"__id__": 313}, "_contentSize": {"__type__": "cc.Size", "width": 119, "height": 130.49000549316406}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.6867226993336397, "y": 0.1132653775062142}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "34Q4oGHdlOoLgpI0ZdCoIX"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 311}, "_enabled": true, "__prefab": {"__id__": 315}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "2577d58c-43f0-4b90-a7e2-0ee43f24bb6f", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "animation2", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "20Hbq6Q7xHT70Trrbchbos"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d8rjrwcfNJgpamisvDCtDR", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ani_baibaodai", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 310}, "_children": [], "_active": true, "_components": [{"__id__": 318}, {"__id__": 320}], "_prefab": {"__id__": 322}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 317}, "_enabled": true, "__prefab": {"__id__": 319}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "13K0N3RctK9pDaaSDJX6yX"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 317}, "_enabled": true, "__prefab": {"__id__": 321}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "93bfeae4-cccb-441b-933c-09ce71f1073c", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "animtion2", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7dIVzbhE9Hubie5A7KXDNf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "425nFBzGRNj6AEQ4lSF+af", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ani_xi", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 310}, "_children": [], "_active": true, "_components": [{"__id__": 324}, {"__id__": 326}], "_prefab": {"__id__": 328}, "_lpos": {"__type__": "cc.Vec3", "x": 3.1239999999999952, "y": -7.52800000000002, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 6.123233995736766e-17, "y": 6.123233995736766e-17, "z": -1, "w": 3.749399456654644e-33}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 180, "y": 180, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 323}, "_enabled": true, "__prefab": {"__id__": 325}, "_contentSize": {"__type__": "cc.Size", "width": 230.4600067138672, "height": 555.8699951171875}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.3650133946404309}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c96a4n17lDM5sc+sfKUtMb"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 323}, "_enabled": true, "__prefab": {"__id__": 327}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "6ffa53fc-3c39-4fd5-b754-6812a429d274", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "animation2", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "17EwFs/HxDNK+QjA20sPKt"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bctt8ASqdNs7nhj2LbMWlL", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_self", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 310}, "_children": [], "_active": true, "_components": [{"__id__": 330}, {"__id__": 332}], "_prefab": {"__id__": 334}, "_lpos": {"__type__": "cc.Vec3", "x": -49.93, "y": 68.641, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": -1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 329}, "_enabled": true, "__prefab": {"__id__": 331}, "_contentSize": {"__type__": "cc.Size", "width": 28.99204796334708, "height": 32.98}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8830HbKKxGA6TjCfnJrH8t"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 329}, "_enabled": true, "__prefab": {"__id__": 333}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 124, "g": 242, "b": 72, "a": 255}, "_string": "我", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26.208333333333332, "_fontSize": 25, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 23, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 48, "g": 103, "b": 180, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c4Sao4OGFIZ7+5LAipC5My"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "91cC0WD5NImITkf/ZJSE8e", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "img_qipao", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 310}, "_children": [{"__id__": 336}, {"__id__": 342}], "_active": true, "_components": [{"__id__": 348}, {"__id__": 350}], "_prefab": {"__id__": 352}, "_lpos": {"__type__": "cc.Vec3", "x": 16.655, "y": 68.39, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "img_emoji_cry", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 335}, "_children": [], "_active": true, "_components": [{"__id__": 337}, {"__id__": 339}], "_prefab": {"__id__": 341}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 336}, "_enabled": true, "__prefab": {"__id__": 338}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "46EoWKr4BMlbDIn3n2iz12"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 336}, "_enabled": true, "__prefab": {"__id__": 340}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "60adef43-67ee-4fe4-aab9-8e953aa24102@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ed/9bi+SNOV5KtDDwafZin"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2ccblbACZAaoIu/gXL+GCH", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "img_emoji_smile", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 335}, "_children": [], "_active": true, "_components": [{"__id__": 343}, {"__id__": 345}], "_prefab": {"__id__": 347}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 342}, "_enabled": true, "__prefab": {"__id__": 344}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "18Xuig6fZDKKAz69jsye4X"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 342}, "_enabled": true, "__prefab": {"__id__": 346}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b2b368d6-0116-49df-b247-2071ce06562a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3fMveQs/lL3qi5CaqDgtNE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a5plXwEoFNLrKeCKa/KKsF", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 335}, "_enabled": true, "__prefab": {"__id__": 349}, "_contentSize": {"__type__": "cc.Size", "width": 72, "height": 54}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "30jAVtoXZMx6yYb3mLqdKr"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 335}, "_enabled": true, "__prefab": {"__id__": 351}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "201519a4-0749-4c1d-b9b6-215b22f244ab@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b3QM+Mq7tHWZYP4RfeJEit"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "91bj6d265FwKNp4nBkGszP", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 310}, "_enabled": true, "__prefab": {"__id__": 354}, "_contentSize": {"__type__": "cc.Size", "width": 91, "height": 110}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dcfUq2+BVI5qmNskE2jrhG"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ecTGhzdmtCn6ym55Pt0AVD", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "houzi_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 309}, "_children": [{"__id__": 357}, {"__id__": 363}, {"__id__": 369}, {"__id__": 375}, {"__id__": 381}], "_active": true, "_components": [{"__id__": 399}], "_prefab": {"__id__": 401}, "_lpos": {"__type__": "cc.Vec3", "x": -2.261, "y": -243.154, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "ani_houzi", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 356}, "_children": [], "_active": true, "_components": [{"__id__": 358}, {"__id__": 360}], "_prefab": {"__id__": 362}, "_lpos": {"__type__": "cc.Vec3", "x": -21.242, "y": -35.46, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 357}, "_enabled": true, "__prefab": {"__id__": 359}, "_contentSize": {"__type__": "cc.Size", "width": 119, "height": 130.49000549316406}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.6867226993336397, "y": 0.1132653775062142}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1fu5Vt8aRE7oY9TAdv8PQ1"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 357}, "_enabled": true, "__prefab": {"__id__": 361}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "2577d58c-43f0-4b90-a7e2-0ee43f24bb6f", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "animation2", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "41cuXKkudHeqobQcpRwC9p"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c4spmuLMBJ67683obCNJVm", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ani_baibaodai", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 356}, "_children": [], "_active": true, "_components": [{"__id__": 364}, {"__id__": 366}], "_prefab": {"__id__": 368}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 363}, "_enabled": true, "__prefab": {"__id__": 365}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c6zeX8TwNHzZv8VckIQB5j"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 363}, "_enabled": true, "__prefab": {"__id__": 367}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "93bfeae4-cccb-441b-933c-09ce71f1073c", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "animtion1", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5b3ba0H0BNBq2gR9dwo0wd"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fa7MIcpypEQoZ2v1tCp2vR", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ani_xi", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 356}, "_children": [], "_active": true, "_components": [{"__id__": 370}, {"__id__": 372}], "_prefab": {"__id__": 374}, "_lpos": {"__type__": "cc.Vec3", "x": 2.2609999999999957, "y": 5.548000000000059, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 369}, "_enabled": true, "__prefab": {"__id__": 371}, "_contentSize": {"__type__": "cc.Size", "width": 230.4600067138672, "height": 555.8699951171875}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.3650133946404309}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e0Nj+AQJ5KI5imjfCEzqbi"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 369}, "_enabled": true, "__prefab": {"__id__": 373}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "6ffa53fc-3c39-4fd5-b754-6812a429d274", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "animation2", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cdu6l7QwRBdoQgN7ges0vN"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "17BTAPY0hKX78aYImjkpL0", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_self", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 356}, "_children": [], "_active": true, "_components": [{"__id__": 376}, {"__id__": 378}], "_prefab": {"__id__": 380}, "_lpos": {"__type__": "cc.Vec3", "x": -34.321, "y": 82.572, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 375}, "_enabled": true, "__prefab": {"__id__": 377}, "_contentSize": {"__type__": "cc.Size", "width": 28.99204796334708, "height": 32.98}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5eGSY00mxMdZbQxl6Z2z/L"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 375}, "_enabled": true, "__prefab": {"__id__": 379}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 124, "g": 242, "b": 72, "a": 255}, "_string": "我", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26.208333333333332, "_fontSize": 25, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 23, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 48, "g": 103, "b": 180, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fb6fOlPmFIAobDNqRTckHH"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "85DwCuLkRCC4G/BZZOK6W5", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "img_qipao", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 356}, "_children": [{"__id__": 382}, {"__id__": 388}], "_active": true, "_components": [{"__id__": 394}, {"__id__": 396}], "_prefab": {"__id__": 398}, "_lpos": {"__type__": "cc.Vec3", "x": -23.918, "y": 63.74, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "img_emoji_cry", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 381}, "_children": [], "_active": true, "_components": [{"__id__": 383}, {"__id__": 385}], "_prefab": {"__id__": 387}, "_lpos": {"__type__": "cc.Vec3", "x": 36.605, "y": 27.921, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 382}, "_enabled": true, "__prefab": {"__id__": 384}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f0rlu7vYtEfa3fOHR7KOKk"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 382}, "_enabled": true, "__prefab": {"__id__": 386}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "60adef43-67ee-4fe4-aab9-8e953aa24102@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "87NBJt3BVPnr4dvU9WU8A0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2amkldsglBrKfBbP1aJKOU", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "img_emoji_smile", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 381}, "_children": [], "_active": true, "_components": [{"__id__": 389}, {"__id__": 391}], "_prefab": {"__id__": 393}, "_lpos": {"__type__": "cc.Vec3", "x": 36.605, "y": 27.921, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 388}, "_enabled": true, "__prefab": {"__id__": 390}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dehBP86yBGfqz4kW0B2t+r"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 388}, "_enabled": true, "__prefab": {"__id__": 392}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b2b368d6-0116-49df-b247-2071ce06562a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "73D+JW26xFPKMJCBoeVO85"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d31w4Wh3VPwJ2Ts6AteS3u", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 381}, "_enabled": true, "__prefab": {"__id__": 395}, "_contentSize": {"__type__": "cc.Size", "width": 72, "height": 54}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "45Zvt8Sq5KMqc141lKpXSa"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 381}, "_enabled": true, "__prefab": {"__id__": 397}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "201519a4-0749-4c1d-b9b6-215b22f244ab@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "06mTUcBt1CBbyemHm5czeX"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "06G3C8lNBMg4nZeqQkGUFv", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 356}, "_enabled": true, "__prefab": {"__id__": 400}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "41vpUbWHFKCanp1l7PDu+L"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f4xcLiqqJCTZMcmE9zCkED", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 309}, "_prefab": {"__id__": 403}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 402}, "asset": {"__uuid__": "ebe26d9f-9d41-4412-a791-b043cb06c858", "__expectedType__": "cc.Prefab"}, "fileId": "96elUVidxPRqijnRGfo8vE", "instance": {"__id__": 404}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "75CltU485C3IS7bE2C6yIO", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 405}, {"__id__": 407}, {"__id__": 408}, {"__id__": 409}, {"__id__": 410}, {"__id__": 412}, {"__id__": 414}, {"__id__": 415}, {"__id__": 417}, {"__id__": 418}, {"__id__": 420}, {"__id__": 421}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 406}, "propertyPath": ["_name"], "value": "hulu"}, {"__type__": "cc.TargetInfo", "localID": ["96elUVidxPRqijnRGfo8vE"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 406}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 406}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 406}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 411}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["ddAQ5xzpdMtYI/eBlxjBY6"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 413}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["52PTmDLKRBQ5mCweNJHDzg"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 413}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -2.359, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 416}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["f4T4X9+ypGzIW72M5/9sfX"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 406}, "propertyPath": ["_active"], "value": true}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 419}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["01Zf2mXShLmbeTh6rpky1u"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 419}, "propertyPath": ["_name"], "value": "ani_bg"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 422}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 36.49598080099861, "height": 34.24}}, {"__type__": "cc.TargetInfo", "localID": ["85QU562IhLooxJJeUfFnfs"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 309}, "_prefab": {"__id__": 424}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 423}, "asset": {"__uuid__": "ddfa3a35-1513-4b06-970d-3018496ac69e", "__expectedType__": "cc.Prefab"}, "fileId": "ddw48bgTVJQaI4eRLCZMBo", "instance": {"__id__": 425}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "34i5NLDR9CTrJrvyiamaai", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 426}, {"__id__": 428}, {"__id__": 429}, {"__id__": 430}, {"__id__": 431}, {"__id__": 433}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 427}, "propertyPath": ["_name"], "value": "lbl_cd"}, {"__type__": "cc.TargetInfo", "localID": ["ddw48bgTVJQaI4eRLCZMBo"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 427}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -3.989, "y": -51, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 427}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 427}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 432}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 45.506763937363374, "height": 54.400000000000006}}, {"__type__": "cc.TargetInfo", "localID": ["94RD/5HzNB4bdksbVCxbeX"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 427}, "propertyPath": ["_active"], "value": true}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 309}, "_enabled": true, "__prefab": {"__id__": 435}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "57ihEhcJVC44X95DP7eGcO"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bfR5Iy9z9ImoZcjEJwMY71", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "p4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 46}, "_children": [{"__id__": 438}, {"__id__": 484}, {"__id__": 530}, {"__id__": 551}], "_active": false, "_components": [{"__id__": 562}], "_prefab": {"__id__": 564}, "_lpos": {"__type__": "cc.Vec3", "x": 120, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "houzi_2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 437}, "_children": [{"__id__": 439}, {"__id__": 445}, {"__id__": 451}, {"__id__": 457}, {"__id__": 463}], "_active": true, "_components": [{"__id__": 481}], "_prefab": {"__id__": 483}, "_lpos": {"__type__": "cc.Vec3", "x": 3.124, "y": 210.45, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": -1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "ani_houzi", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 438}, "_children": [], "_active": true, "_components": [{"__id__": 440}, {"__id__": 442}], "_prefab": {"__id__": 444}, "_lpos": {"__type__": "cc.Vec3", "x": -21.671, "y": -51.174, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 439}, "_enabled": true, "__prefab": {"__id__": 441}, "_contentSize": {"__type__": "cc.Size", "width": 119, "height": 130.49000549316406}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.6867226993336397, "y": 0.1132653775062142}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e9PscB9hhAOo6xY1oNwxqA"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 439}, "_enabled": true, "__prefab": {"__id__": 443}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "2577d58c-43f0-4b90-a7e2-0ee43f24bb6f", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "animation2", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d6dqEA0/NBwK4fAE20/1G8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3e4ceV0bZNGaHMamw22Wc0", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ani_baibaodai", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 438}, "_children": [], "_active": true, "_components": [{"__id__": 446}, {"__id__": 448}], "_prefab": {"__id__": 450}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 445}, "_enabled": true, "__prefab": {"__id__": 447}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a3ylfM62dMv5o7PrNtwTku"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 445}, "_enabled": true, "__prefab": {"__id__": 449}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "93bfeae4-cccb-441b-933c-09ce71f1073c", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "animtion2", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "68kUk40BxM/qzG+kSykqfH"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e8Lb1Koy9Ij6Hkfmk8JTmL", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ani_xi", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 438}, "_children": [], "_active": true, "_components": [{"__id__": 452}, {"__id__": 454}], "_prefab": {"__id__": 456}, "_lpos": {"__type__": "cc.Vec3", "x": 3.1239999999999952, "y": -7.52800000000002, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 6.123233995736766e-17, "y": 6.123233995736766e-17, "z": -1, "w": 3.749399456654644e-33}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 180, "y": 180, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 451}, "_enabled": true, "__prefab": {"__id__": 453}, "_contentSize": {"__type__": "cc.Size", "width": 230.4600067138672, "height": 555.8699951171875}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.3650133946404309}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0fY44OVNBCvLC530kIBdym"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 451}, "_enabled": true, "__prefab": {"__id__": 455}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "6ffa53fc-3c39-4fd5-b754-6812a429d274", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "animation2", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c8nQrXSJFNW4mboOfkyjoA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "04Z13pEtBMlKHyB9RxjuIz", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_self", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 438}, "_children": [], "_active": true, "_components": [{"__id__": 458}, {"__id__": 460}], "_prefab": {"__id__": 462}, "_lpos": {"__type__": "cc.Vec3", "x": -49.93, "y": 68.641, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": -1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 457}, "_enabled": true, "__prefab": {"__id__": 459}, "_contentSize": {"__type__": "cc.Size", "width": 28.99204796334708, "height": 32.98}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "caxy3YFphAvo7/9Zv+lqQV"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 457}, "_enabled": true, "__prefab": {"__id__": 461}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 124, "g": 242, "b": 72, "a": 255}, "_string": "我", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26.208333333333332, "_fontSize": 25, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 23, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 48, "g": 103, "b": 180, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "83hrnYl4RP9I1WftIHjmSs"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7btMpHFJFFQLbnNExnd039", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "img_qipao", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 438}, "_children": [{"__id__": 464}, {"__id__": 470}], "_active": true, "_components": [{"__id__": 476}, {"__id__": 478}], "_prefab": {"__id__": 480}, "_lpos": {"__type__": "cc.Vec3", "x": 16.655, "y": 68.39, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "img_emoji_cry", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 463}, "_children": [], "_active": true, "_components": [{"__id__": 465}, {"__id__": 467}], "_prefab": {"__id__": 469}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 464}, "_enabled": true, "__prefab": {"__id__": 466}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c1MzP2lA1GNoKxzNpPDHDQ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 464}, "_enabled": true, "__prefab": {"__id__": 468}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "60adef43-67ee-4fe4-aab9-8e953aa24102@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "90W4D54OFAQY7eZuCfTx8u"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "27vyUBoDZIuY+uk9QJvNDI", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "img_emoji_smile", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 463}, "_children": [], "_active": true, "_components": [{"__id__": 471}, {"__id__": 473}], "_prefab": {"__id__": 475}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 470}, "_enabled": true, "__prefab": {"__id__": 472}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0czo/lUy5Bc6BOCSGkT7I1"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 470}, "_enabled": true, "__prefab": {"__id__": 474}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b2b368d6-0116-49df-b247-2071ce06562a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "561pETmXpD+4jB49O3z2sO"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "55rvQ8oSRFTbk3rWmL3stj", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 463}, "_enabled": true, "__prefab": {"__id__": 477}, "_contentSize": {"__type__": "cc.Size", "width": 72, "height": 54}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5ck5kgXRxBBLwcDonFMwaM"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 463}, "_enabled": true, "__prefab": {"__id__": 479}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "201519a4-0749-4c1d-b9b6-215b22f244ab@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "372J0RArNAyaBYXWxH+vX3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "23ebIuPfRL247YGHj9wVoO", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 438}, "_enabled": true, "__prefab": {"__id__": 482}, "_contentSize": {"__type__": "cc.Size", "width": 91, "height": 110}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4eg8Ticl1IPK3FoK+KZbLK"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cdGbdYahVG6YL/SE+aV8S0", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "houzi_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 437}, "_children": [{"__id__": 485}, {"__id__": 491}, {"__id__": 497}, {"__id__": 503}, {"__id__": 509}], "_active": true, "_components": [{"__id__": 527}], "_prefab": {"__id__": 529}, "_lpos": {"__type__": "cc.Vec3", "x": -2.261, "y": -243.154, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "ani_houzi", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 484}, "_children": [], "_active": true, "_components": [{"__id__": 486}, {"__id__": 488}], "_prefab": {"__id__": 490}, "_lpos": {"__type__": "cc.Vec3", "x": -21.242, "y": -35.46, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 485}, "_enabled": true, "__prefab": {"__id__": 487}, "_contentSize": {"__type__": "cc.Size", "width": 119, "height": 130.49000549316406}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.6867226993336397, "y": 0.1132653775062142}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "daVICn43pMToL02Vw6y/PK"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 485}, "_enabled": true, "__prefab": {"__id__": 489}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "2577d58c-43f0-4b90-a7e2-0ee43f24bb6f", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "animation2", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9aDJ183LBAR4om1U/abWpy"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f3fTLQeTNA1bVOSa7aK6+P", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ani_baibaodai", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 484}, "_children": [], "_active": true, "_components": [{"__id__": 492}, {"__id__": 494}], "_prefab": {"__id__": 496}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 491}, "_enabled": true, "__prefab": {"__id__": 493}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3bzyaUQiVPwJZP96alN7xQ"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 491}, "_enabled": true, "__prefab": {"__id__": 495}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "93bfeae4-cccb-441b-933c-09ce71f1073c", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "animtion1", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "31AfVpcfJGZaSw1Vjh0Hny"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "acEUmwFe5OAof8sPp2M0O7", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ani_xi", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 484}, "_children": [], "_active": true, "_components": [{"__id__": 498}, {"__id__": 500}], "_prefab": {"__id__": 502}, "_lpos": {"__type__": "cc.Vec3", "x": 2.2609999999999957, "y": 5.548000000000059, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 497}, "_enabled": true, "__prefab": {"__id__": 499}, "_contentSize": {"__type__": "cc.Size", "width": 230.4600067138672, "height": 555.8699951171875}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.3650133946404309}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8eHNUHLChBf6z735IEdjBI"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 497}, "_enabled": true, "__prefab": {"__id__": 501}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "6ffa53fc-3c39-4fd5-b754-6812a429d274", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "animation2", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bbNTU8NixEBKVX9DjgMNBm"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3ez0GwCK9MpoA1W8grB75Z", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_self", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 484}, "_children": [], "_active": true, "_components": [{"__id__": 504}, {"__id__": 506}], "_prefab": {"__id__": 508}, "_lpos": {"__type__": "cc.Vec3", "x": -34.321, "y": 82.572, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 503}, "_enabled": true, "__prefab": {"__id__": 505}, "_contentSize": {"__type__": "cc.Size", "width": 28.99204796334708, "height": 32.98}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c0Y1GucRFI2II+wvIqOK+T"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 503}, "_enabled": true, "__prefab": {"__id__": 507}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 124, "g": 242, "b": 72, "a": 255}, "_string": "我", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26.208333333333332, "_fontSize": 25, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 23, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 48, "g": 103, "b": 180, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a5XDrag01Ax55I2h4cCi77"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cfObbkNJhMFa/Yc82qhohU", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "img_qipao", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 484}, "_children": [{"__id__": 510}, {"__id__": 516}], "_active": true, "_components": [{"__id__": 522}, {"__id__": 524}], "_prefab": {"__id__": 526}, "_lpos": {"__type__": "cc.Vec3", "x": -23.918, "y": 63.74, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "img_emoji_cry", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 509}, "_children": [], "_active": true, "_components": [{"__id__": 511}, {"__id__": 513}], "_prefab": {"__id__": 515}, "_lpos": {"__type__": "cc.Vec3", "x": 36.605, "y": 27.921, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 510}, "_enabled": true, "__prefab": {"__id__": 512}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "51tBGp1XVB9p5esLQrW1L8"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 510}, "_enabled": true, "__prefab": {"__id__": 514}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "60adef43-67ee-4fe4-aab9-8e953aa24102@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e3m1qmu+1FPLaBI3BxA7ZQ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "51MufIe6ZNtp6WTZaWobma", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "img_emoji_smile", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 509}, "_children": [], "_active": true, "_components": [{"__id__": 517}, {"__id__": 519}], "_prefab": {"__id__": 521}, "_lpos": {"__type__": "cc.Vec3", "x": 36.605, "y": 27.921, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 516}, "_enabled": true, "__prefab": {"__id__": 518}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a5DurutyNF8ohqX5Vb0YG9"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 516}, "_enabled": true, "__prefab": {"__id__": 520}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b2b368d6-0116-49df-b247-2071ce06562a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bb+sI0O7dNJ7yXLzIzSI+R"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "40o9EVIohNf56YJdJsxJ0g", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 509}, "_enabled": true, "__prefab": {"__id__": 523}, "_contentSize": {"__type__": "cc.Size", "width": 72, "height": 54}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d4DGbMeihBRqm4A1DeFCyg"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 509}, "_enabled": true, "__prefab": {"__id__": 525}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "201519a4-0749-4c1d-b9b6-215b22f244ab@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6d3c1Ky5pI54UeGqLn+IJy"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c3OgAHGcRCRbx+ZoFy//1K", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 484}, "_enabled": true, "__prefab": {"__id__": 528}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d8V2y2cLlNzIeGhM+HyA3s"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2bxqUWvD5E0odM9rAyejq3", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 437}, "_prefab": {"__id__": 531}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 530}, "asset": {"__uuid__": "ebe26d9f-9d41-4412-a791-b043cb06c858", "__expectedType__": "cc.Prefab"}, "fileId": "96elUVidxPRqijnRGfo8vE", "instance": {"__id__": 532}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "e6I4a1ff5DxIj/yWqXA63m", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 533}, {"__id__": 535}, {"__id__": 536}, {"__id__": 537}, {"__id__": 538}, {"__id__": 540}, {"__id__": 542}, {"__id__": 543}, {"__id__": 545}, {"__id__": 546}, {"__id__": 548}, {"__id__": 549}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 534}, "propertyPath": ["_name"], "value": "hulu"}, {"__type__": "cc.TargetInfo", "localID": ["96elUVidxPRqijnRGfo8vE"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 534}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 534}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 534}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 539}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["ddAQ5xzpdMtYI/eBlxjBY6"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 541}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["52PTmDLKRBQ5mCweNJHDzg"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 541}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -2.359, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 544}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["f4T4X9+ypGzIW72M5/9sfX"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 534}, "propertyPath": ["_active"], "value": true}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 547}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["01Zf2mXShLmbeTh6rpky1u"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 547}, "propertyPath": ["_name"], "value": "ani_bg"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 550}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 36.49598080099861, "height": 34.24}}, {"__type__": "cc.TargetInfo", "localID": ["85QU562IhLooxJJeUfFnfs"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 437}, "_prefab": {"__id__": 552}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 551}, "asset": {"__uuid__": "ddfa3a35-1513-4b06-970d-3018496ac69e", "__expectedType__": "cc.Prefab"}, "fileId": "ddw48bgTVJQaI4eRLCZMBo", "instance": {"__id__": 553}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "cb/GXQTDBAHbH0b+7jJhrp", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 554}, {"__id__": 556}, {"__id__": 557}, {"__id__": 558}, {"__id__": 559}, {"__id__": 561}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 555}, "propertyPath": ["_name"], "value": "lbl_cd"}, {"__type__": "cc.TargetInfo", "localID": ["ddw48bgTVJQaI4eRLCZMBo"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 555}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -3.989, "y": -51, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 555}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 555}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 560}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 45.506763937363374, "height": 54.400000000000006}}, {"__type__": "cc.TargetInfo", "localID": ["94RD/5HzNB4bdksbVCxbeX"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 555}, "propertyPath": ["_active"], "value": true}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 437}, "_enabled": true, "__prefab": {"__id__": 563}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bcRT4US01IxZ6x1JeUUs/0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fanG1o+Y5CCr7cIBfs5uVZ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "p5", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 46}, "_children": [{"__id__": 566}, {"__id__": 612}, {"__id__": 658}, {"__id__": 679}], "_active": false, "_components": [{"__id__": 690}], "_prefab": {"__id__": 692}, "_lpos": {"__type__": "cc.Vec3", "x": 240, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "houzi_2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 565}, "_children": [{"__id__": 567}, {"__id__": 573}, {"__id__": 579}, {"__id__": 585}, {"__id__": 591}], "_active": true, "_components": [{"__id__": 609}], "_prefab": {"__id__": 611}, "_lpos": {"__type__": "cc.Vec3", "x": 3.124, "y": 210.45, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": -1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "ani_houzi", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 566}, "_children": [], "_active": true, "_components": [{"__id__": 568}, {"__id__": 570}], "_prefab": {"__id__": 572}, "_lpos": {"__type__": "cc.Vec3", "x": -21.671, "y": -51.174, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 567}, "_enabled": true, "__prefab": {"__id__": 569}, "_contentSize": {"__type__": "cc.Size", "width": 119, "height": 130.49000549316406}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.6867226993336397, "y": 0.1132653775062142}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8c8X3QI2tNF4aFnaVvSDW7"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 567}, "_enabled": true, "__prefab": {"__id__": 571}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "2577d58c-43f0-4b90-a7e2-0ee43f24bb6f", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "animation2", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "363xwTq1BPfa5BOhFZvZJU"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "351qyvn59MD5Ht2Zd2+MLw", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ani_baibaodai", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 566}, "_children": [], "_active": true, "_components": [{"__id__": 574}, {"__id__": 576}], "_prefab": {"__id__": 578}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 573}, "_enabled": true, "__prefab": {"__id__": 575}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a4sn1PmWhCI4+pq6Tk4UrV"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 573}, "_enabled": true, "__prefab": {"__id__": 577}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "93bfeae4-cccb-441b-933c-09ce71f1073c", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "animtion2", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "53nomrx9ZEtZ6Yac2Cb10W"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7bCd+T/4xBMo3GO42riwR5", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ani_xi", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 566}, "_children": [], "_active": true, "_components": [{"__id__": 580}, {"__id__": 582}], "_prefab": {"__id__": 584}, "_lpos": {"__type__": "cc.Vec3", "x": 3.1239999999999952, "y": -7.52800000000002, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 6.123233995736766e-17, "y": 6.123233995736766e-17, "z": -1, "w": 3.749399456654644e-33}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 180, "y": 180, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 579}, "_enabled": true, "__prefab": {"__id__": 581}, "_contentSize": {"__type__": "cc.Size", "width": 230.4600067138672, "height": 555.8699951171875}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.3650133946404309}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "96ELHJPGROvKzAqQxo6Xmv"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 579}, "_enabled": true, "__prefab": {"__id__": 583}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "6ffa53fc-3c39-4fd5-b754-6812a429d274", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "animation2", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a6hIvzzmtCSZNe4bd93WyA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a9OXE9SDlI5JMohLb0ZiLA", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_self", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 566}, "_children": [], "_active": true, "_components": [{"__id__": 586}, {"__id__": 588}], "_prefab": {"__id__": 590}, "_lpos": {"__type__": "cc.Vec3", "x": -49.93, "y": 68.641, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": -1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 585}, "_enabled": true, "__prefab": {"__id__": 587}, "_contentSize": {"__type__": "cc.Size", "width": 28.99204796334708, "height": 32.98}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8f37FfcWpPCq+bAwuivYDZ"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 585}, "_enabled": true, "__prefab": {"__id__": 589}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 124, "g": 242, "b": 72, "a": 255}, "_string": "我", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26.208333333333332, "_fontSize": 25, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 23, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 48, "g": 103, "b": 180, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "60cdk1p+ZNjZ5LwOknf8Ma"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "82AMPFRhVKW5eZg00RuTfN", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "img_qipao", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 566}, "_children": [{"__id__": 592}, {"__id__": 598}], "_active": true, "_components": [{"__id__": 604}, {"__id__": 606}], "_prefab": {"__id__": 608}, "_lpos": {"__type__": "cc.Vec3", "x": 16.655, "y": 68.39, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "img_emoji_cry", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 591}, "_children": [], "_active": true, "_components": [{"__id__": 593}, {"__id__": 595}], "_prefab": {"__id__": 597}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 592}, "_enabled": true, "__prefab": {"__id__": 594}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5cGgPnrS9L9bNzMhspb/Uu"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 592}, "_enabled": true, "__prefab": {"__id__": 596}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "60adef43-67ee-4fe4-aab9-8e953aa24102@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "10ClIZ6CFOGaP3zugPqXuu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "06aNhDZVJDm6H2b4tsa4O9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "img_emoji_smile", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 591}, "_children": [], "_active": true, "_components": [{"__id__": 599}, {"__id__": 601}], "_prefab": {"__id__": 603}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 598}, "_enabled": true, "__prefab": {"__id__": 600}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c3nRzP5u1HDq5i8iJONvJK"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 598}, "_enabled": true, "__prefab": {"__id__": 602}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b2b368d6-0116-49df-b247-2071ce06562a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5598GlY8FLxacoKLGpG0ig"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "daXtFZFgJCQqZZx/ZqcJCw", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 591}, "_enabled": true, "__prefab": {"__id__": 605}, "_contentSize": {"__type__": "cc.Size", "width": 72, "height": 54}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9bl3x5VLpCsZwgzk2iqX2v"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 591}, "_enabled": true, "__prefab": {"__id__": 607}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "201519a4-0749-4c1d-b9b6-215b22f244ab@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9fZ4B/wSlFXLkQd1tK70nz"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8c4BmjYAxEqZGzkx5r6D9R", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 566}, "_enabled": true, "__prefab": {"__id__": 610}, "_contentSize": {"__type__": "cc.Size", "width": 91, "height": 110}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "badSgyGqRJbaOFjYHd8K7x"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0f4yPGwsZHaYbLuu+KlfY4", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "houzi_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 565}, "_children": [{"__id__": 613}, {"__id__": 619}, {"__id__": 625}, {"__id__": 631}, {"__id__": 637}], "_active": true, "_components": [{"__id__": 655}], "_prefab": {"__id__": 657}, "_lpos": {"__type__": "cc.Vec3", "x": -2.261, "y": -243.154, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "ani_houzi", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 612}, "_children": [], "_active": true, "_components": [{"__id__": 614}, {"__id__": 616}], "_prefab": {"__id__": 618}, "_lpos": {"__type__": "cc.Vec3", "x": -21.242, "y": -35.46, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 613}, "_enabled": true, "__prefab": {"__id__": 615}, "_contentSize": {"__type__": "cc.Size", "width": 119, "height": 130.49000549316406}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.6867226993336397, "y": 0.1132653775062142}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e7MasNONBJZJ+XBOq2sMMk"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 613}, "_enabled": true, "__prefab": {"__id__": 617}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "2577d58c-43f0-4b90-a7e2-0ee43f24bb6f", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "animation2", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3dnnKoRcRO+Lnyf7Tr/7GA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a8T1FFGeFJDrjI4bUNIZ6U", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ani_baibaodai", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 612}, "_children": [], "_active": true, "_components": [{"__id__": 620}, {"__id__": 622}], "_prefab": {"__id__": 624}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 619}, "_enabled": true, "__prefab": {"__id__": 621}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f9NGE4YwpJMKI7hwbWdotm"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 619}, "_enabled": true, "__prefab": {"__id__": 623}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "93bfeae4-cccb-441b-933c-09ce71f1073c", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "animtion1", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1d0U75gRJAyY0iu4vInznG"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "13xlQ6iFZP1LCAXTXlsfVy", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ani_xi", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 612}, "_children": [], "_active": true, "_components": [{"__id__": 626}, {"__id__": 628}], "_prefab": {"__id__": 630}, "_lpos": {"__type__": "cc.Vec3", "x": 2.2609999999999957, "y": 5.548000000000059, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 625}, "_enabled": true, "__prefab": {"__id__": 627}, "_contentSize": {"__type__": "cc.Size", "width": 230.4600067138672, "height": 555.8699951171875}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.3650133946404309}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "21ocTq5TlPNYzz4stvtIhx"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 625}, "_enabled": true, "__prefab": {"__id__": 629}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "6ffa53fc-3c39-4fd5-b754-6812a429d274", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "animation2", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f3yTnrK+pKI5ABBbuuMLE/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5fYjfdAxhOVa6h5Mmi/H0q", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_self", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 612}, "_children": [], "_active": true, "_components": [{"__id__": 632}, {"__id__": 634}], "_prefab": {"__id__": 636}, "_lpos": {"__type__": "cc.Vec3", "x": -34.321, "y": 82.572, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 631}, "_enabled": true, "__prefab": {"__id__": 633}, "_contentSize": {"__type__": "cc.Size", "width": 28.99204796334708, "height": 32.98}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c6ERdwKDBFa7x2HMcNxeCr"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 631}, "_enabled": true, "__prefab": {"__id__": 635}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 124, "g": 242, "b": 72, "a": 255}, "_string": "我", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26.208333333333332, "_fontSize": 25, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 23, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 48, "g": 103, "b": 180, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "61zAQ29xZJs7saBhXyqlBt"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ffk6N5EUtALZtYKOCfli8D", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "img_qipao", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 612}, "_children": [{"__id__": 638}, {"__id__": 644}], "_active": true, "_components": [{"__id__": 650}, {"__id__": 652}], "_prefab": {"__id__": 654}, "_lpos": {"__type__": "cc.Vec3", "x": -23.918, "y": 63.74, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "img_emoji_cry", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 637}, "_children": [], "_active": true, "_components": [{"__id__": 639}, {"__id__": 641}], "_prefab": {"__id__": 643}, "_lpos": {"__type__": "cc.Vec3", "x": 36.605, "y": 27.921, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 638}, "_enabled": true, "__prefab": {"__id__": 640}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ec/94p8HJNi4lVZrD4H2qL"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 638}, "_enabled": true, "__prefab": {"__id__": 642}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "60adef43-67ee-4fe4-aab9-8e953aa24102@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2794M1ZoNCE6jTmEWFmPwO"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "21RqV4BQNH3pxgvsC3KNZj", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "img_emoji_smile", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 637}, "_children": [], "_active": true, "_components": [{"__id__": 645}, {"__id__": 647}], "_prefab": {"__id__": 649}, "_lpos": {"__type__": "cc.Vec3", "x": 36.605, "y": 27.921, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 644}, "_enabled": true, "__prefab": {"__id__": 646}, "_contentSize": {"__type__": "cc.Size", "width": 35, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6cjn1Fr4lIU79w28UghYK/"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 644}, "_enabled": true, "__prefab": {"__id__": 648}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b2b368d6-0116-49df-b247-2071ce06562a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4egFfSkyBNsKW8W8SmKDQv"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c1VJF7ZmROK7SqYhNiDBLT", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 637}, "_enabled": true, "__prefab": {"__id__": 651}, "_contentSize": {"__type__": "cc.Size", "width": 72, "height": 54}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ffmbJvfD1G04xaRY0nUuia"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 637}, "_enabled": true, "__prefab": {"__id__": 653}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "201519a4-0749-4c1d-b9b6-215b22f244ab@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6awEiZePJOPIus9ac+89gp"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b80D0x1bpN+ZjvoPAVX/sm", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 612}, "_enabled": true, "__prefab": {"__id__": 656}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "24yEGsv0RLFIT2ahDCPM9C"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "29d4mMkyhOWbMcCYzwyKGn", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 565}, "_prefab": {"__id__": 659}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 658}, "asset": {"__uuid__": "ebe26d9f-9d41-4412-a791-b043cb06c858", "__expectedType__": "cc.Prefab"}, "fileId": "96elUVidxPRqijnRGfo8vE", "instance": {"__id__": 660}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "83MImnzppI7atxQk1bOvyx", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 661}, {"__id__": 663}, {"__id__": 664}, {"__id__": 665}, {"__id__": 666}, {"__id__": 668}, {"__id__": 670}, {"__id__": 671}, {"__id__": 673}, {"__id__": 674}, {"__id__": 676}, {"__id__": 677}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 662}, "propertyPath": ["_name"], "value": "hulu"}, {"__type__": "cc.TargetInfo", "localID": ["96elUVidxPRqijnRGfo8vE"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 662}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 662}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 662}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 667}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["ddAQ5xzpdMtYI/eBlxjBY6"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 669}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["52PTmDLKRBQ5mCweNJHDzg"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 669}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -2.359, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 672}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["f4T4X9+ypGzIW72M5/9sfX"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 662}, "propertyPath": ["_active"], "value": true}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 675}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["01Zf2mXShLmbeTh6rpky1u"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 675}, "propertyPath": ["_name"], "value": "ani_bg"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 678}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 36.49598080099861, "height": 34.24}}, {"__type__": "cc.TargetInfo", "localID": ["85QU562IhLooxJJeUfFnfs"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 565}, "_prefab": {"__id__": 680}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 679}, "asset": {"__uuid__": "ddfa3a35-1513-4b06-970d-3018496ac69e", "__expectedType__": "cc.Prefab"}, "fileId": "ddw48bgTVJQaI4eRLCZMBo", "instance": {"__id__": 681}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "3btp0KHZ1APoOPXJSAf6be", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 682}, {"__id__": 684}, {"__id__": 685}, {"__id__": 686}, {"__id__": 687}, {"__id__": 689}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 683}, "propertyPath": ["_name"], "value": "lbl_cd"}, {"__type__": "cc.TargetInfo", "localID": ["ddw48bgTVJQaI4eRLCZMBo"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 683}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -3.989, "y": -51, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 683}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 683}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 688}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 45.506763937363374, "height": 54.400000000000006}}, {"__type__": "cc.TargetInfo", "localID": ["94RD/5HzNB4bdksbVCxbeX"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 683}, "propertyPath": ["_active"], "value": true}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 565}, "_enabled": true, "__prefab": {"__id__": 691}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f6vJbdkAdAmYyEAUMDyGMD"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "37qyTh/FZC4ZaDxq/U3O41", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 46}, "_enabled": true, "__prefab": {"__id__": 694}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "51TGOs/4VJJJ5OpSfFzm23"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "91dr/HsP5DsLLdFC3hXZdu", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ui_btn", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 9}, "_children": [{"__id__": 697}, {"__id__": 703}, {"__id__": 721}, {"__id__": 739}, {"__id__": 757}, {"__id__": 775}, {"__id__": 793}, {"__id__": 811}], "_active": true, "_components": [{"__id__": 847}, {"__id__": 849}], "_prefab": {"__id__": 851}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -575.5215000000001, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "img_dtline_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 696}, "_children": [], "_active": true, "_components": [{"__id__": 698}, {"__id__": 700}], "_prefab": {"__id__": 702}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 697}, "_enabled": true, "__prefab": {"__id__": 699}, "_contentSize": {"__type__": "cc.Size", "width": 380, "height": 1}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "71yRxPptxOtp+DoeEaZvvq"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 697}, "_enabled": true, "__prefab": {"__id__": 701}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e02be298-bb13-469f-ab42-fe9a16f31bb9@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "835MJBt8xBZqsGgnjCftuB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "91QkKiwvZFnYK0acpeFF/S", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_log", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 696}, "_children": [{"__id__": 704}, {"__id__": 710}], "_active": true, "_components": [{"__id__": 716}, {"__id__": 718}], "_prefab": {"__id__": 720}, "_lpos": {"__type__": "cc.Vec3", "x": -286, "y": 84, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 703}, "_children": [], "_active": true, "_components": [{"__id__": 705}, {"__id__": 707}], "_prefab": {"__id__": 709}, "_lpos": {"__type__": "cc.Vec3", "x": 3.6899999999999977, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 704}, "_enabled": true, "__prefab": {"__id__": 706}, "_contentSize": {"__type__": "cc.Size", "width": 101, "height": 102}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "04820qMTdDgqmJDTQXVBKJ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 704}, "_enabled": true, "__prefab": {"__id__": 708}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "56541ba2-2272-485e-88b0-033c34b36afb@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "98ITNp6VdAbJTD+fHFO4Gr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "02NKzJg2xEopnzLZBc/T8f", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 703}, "_children": [], "_active": true, "_components": [{"__id__": 711}, {"__id__": 713}], "_prefab": {"__id__": 715}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -40.787, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 710}, "_enabled": true, "__prefab": {"__id__": 712}, "_contentSize": {"__type__": "cc.Size", "width": 79.19999694824219, "height": 44.32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "afqPBKJylE4LmmaPJksvo6"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 710}, "_enabled": true, "__prefab": {"__id__": 714}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 242, "g": 220, "b": 72, "a": 255}, "_string": "记 录", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 32, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 67, "g": 39, "b": 12, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "96f92TynVL7pYVyycupnwl"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6fRCxD1w5BQbNgyV++kU6V", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 703}, "_enabled": true, "__prefab": {"__id__": 717}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 110}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8f1tjDi8RHiLWUdPtmPW2H"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 703}, "_enabled": true, "__prefab": {"__id__": 719}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "905pDv7qlPC75mCoTAh+t8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ccTUVYdB9Dbol8qMDHrtqa", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_refresh", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 696}, "_children": [{"__id__": 722}, {"__id__": 728}], "_active": true, "_components": [{"__id__": 734}, {"__id__": 736}], "_prefab": {"__id__": 738}, "_lpos": {"__type__": "cc.Vec3", "x": -180, "y": 84, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 721}, "_children": [], "_active": true, "_components": [{"__id__": 723}, {"__id__": 725}], "_prefab": {"__id__": 727}, "_lpos": {"__type__": "cc.Vec3", "x": 2, "y": -7, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 722}, "_enabled": true, "__prefab": {"__id__": 724}, "_contentSize": {"__type__": "cc.Size", "width": 101, "height": 89}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2ctAQud+xHcZk4GNRkNxyW"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 722}, "_enabled": true, "__prefab": {"__id__": 726}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2155501a-bf7a-4a35-b5a4-381ffacef638@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "51SWt9q2hIq4v4od+4vU95"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3dpYlYqTBC0JTh5YakBz+s", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 721}, "_children": [], "_active": true, "_components": [{"__id__": 729}, {"__id__": 731}], "_prefab": {"__id__": 733}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -40.787, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 728}, "_enabled": true, "__prefab": {"__id__": 730}, "_contentSize": {"__type__": "cc.Size", "width": 79.19999694824219, "height": 44.32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3bwfsb8RtBE67nCmIwmXnj"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 728}, "_enabled": true, "__prefab": {"__id__": 732}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 242, "g": 220, "b": 72, "a": 255}, "_string": "刷 新", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 32, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 67, "g": 39, "b": 12, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "18rUPSQX1DmZP+W7kXjYjI"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0cEH5V6hNPNYPxqc3bCYZe", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 721}, "_enabled": true, "__prefab": {"__id__": 735}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 110}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "56ZVSzhC9KrbrMJr8ZiDq1"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 721}, "_enabled": true, "__prefab": {"__id__": 737}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "91MsdF39xI/ptIYsvz/J+J"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "24gZo+2SlOeZEFz6m+dtUF", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_shop", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 696}, "_children": [{"__id__": 740}, {"__id__": 746}], "_active": true, "_components": [{"__id__": 752}, {"__id__": 754}], "_prefab": {"__id__": 756}, "_lpos": {"__type__": "cc.Vec3", "x": -74, "y": 84, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 739}, "_children": [], "_active": true, "_components": [{"__id__": 741}, {"__id__": 743}], "_prefab": {"__id__": 745}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -6, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 740}, "_enabled": true, "__prefab": {"__id__": 742}, "_contentSize": {"__type__": "cc.Size", "width": 101, "height": 89}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a3w5LHprpFsbrr0AHuC9Vj"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 740}, "_enabled": true, "__prefab": {"__id__": 744}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "96a66e7a-c555-48c8-9567-09e7a675c181@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8flD2JlVNKVKR3mAxOekfZ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "25v+CFA/ZHk6OHAxV92BB/", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 739}, "_children": [], "_active": true, "_components": [{"__id__": 747}, {"__id__": 749}], "_prefab": {"__id__": 751}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -40.787, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 746}, "_enabled": true, "__prefab": {"__id__": 748}, "_contentSize": {"__type__": "cc.Size", "width": 79.19999694824219, "height": 44.32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a5pxVKiXxKhbZ5eUadeIi9"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 746}, "_enabled": true, "__prefab": {"__id__": 750}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 242, "g": 220, "b": 72, "a": 255}, "_string": "商 店", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 32, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 67, "g": 39, "b": 12, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6dYH1kc8FJ1JTXMskP8lT6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "740AtimLdFXaTSJOS5FTvo", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 739}, "_enabled": true, "__prefab": {"__id__": 753}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 110}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6ejaQusZVNU74Je4uwMLE1"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 739}, "_enabled": true, "__prefab": {"__id__": 755}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9eAC0GhR1J5olSZIhR1oFh"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "50DIoA4J5Dwpu5bC8MEobR", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_preview", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 696}, "_children": [{"__id__": 758}, {"__id__": 764}], "_active": true, "_components": [{"__id__": 770}, {"__id__": 772}], "_prefab": {"__id__": 774}, "_lpos": {"__type__": "cc.Vec3", "x": 29.679, "y": 84, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 757}, "_children": [], "_active": true, "_components": [{"__id__": 759}, {"__id__": 761}], "_prefab": {"__id__": 763}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -1.83, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 758}, "_enabled": true, "__prefab": {"__id__": 760}, "_contentSize": {"__type__": "cc.Size", "width": 101, "height": 97}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6dNB97R/dDDrIRaXo5K06M"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 758}, "_enabled": true, "__prefab": {"__id__": 762}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "21a227e8-bd0a-414c-8a49-2edf746ea83f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c45ZrQ07FN5Y9SDHLmqtA6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bb2b+/JTNL0Kb/0CxA4/+v", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 757}, "_children": [], "_active": true, "_components": [{"__id__": 765}, {"__id__": 767}], "_prefab": {"__id__": 769}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -40.787, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 764}, "_enabled": true, "__prefab": {"__id__": 766}, "_contentSize": {"__type__": "cc.Size", "width": 79.19999694824219, "height": 44.32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d1VGhkhdxIMaxVZKdqhCnQ"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 764}, "_enabled": true, "__prefab": {"__id__": 768}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 242, "g": 220, "b": 72, "a": 255}, "_string": "预 览", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 32, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 67, "g": 39, "b": 12, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a8kuziaDZLNJOIsz1maU8Q"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6fyu5AZcZFBaa77M37RTuU", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 757}, "_enabled": true, "__prefab": {"__id__": 771}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 110}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "54yQJMwtNEzop75KwVWExO"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 757}, "_enabled": true, "__prefab": {"__id__": 773}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": {"__id__": 757}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6cpjRg7BJMer28y6e59ADW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "caWAfW57hBBJqpzmnNplRa", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_cjjj", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 696}, "_children": [{"__id__": 776}, {"__id__": 782}], "_active": true, "_components": [{"__id__": 788}, {"__id__": 790}], "_prefab": {"__id__": 792}, "_lpos": {"__type__": "cc.Vec3", "x": 265, "y": 84, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 775}, "_children": [], "_active": true, "_components": [{"__id__": 777}, {"__id__": 779}], "_prefab": {"__id__": 781}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -6, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 776}, "_enabled": true, "__prefab": {"__id__": 778}, "_contentSize": {"__type__": "cc.Size", "width": 91, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1dQhi43p5M+62KutT5sO0+"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 776}, "_enabled": true, "__prefab": {"__id__": 780}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "521a4a4f-b03a-4ccf-8a71-523b4333aed9@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2df/IMPI9Dq4YioIHJy8C+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "64AkdzUDNCFpOizW0VGGoW", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 775}, "_children": [], "_active": true, "_components": [{"__id__": 783}, {"__id__": 785}], "_prefab": {"__id__": 787}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -40.787, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 782}, "_enabled": true, "__prefab": {"__id__": 784}, "_contentSize": {"__type__": "cc.Size", "width": 132, "height": 44.32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "67u6JTn1NJIZHEnm1C6htF"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 782}, "_enabled": true, "__prefab": {"__id__": 786}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 242, "g": 220, "b": 72, "a": 255}, "_string": "采集基金", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 32, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 67, "g": 39, "b": 12, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8exHBL9n1LMpNxD5BbsAeL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9ezZq3CApNobsybp7or2Q3", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 775}, "_enabled": true, "__prefab": {"__id__": 789}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 110}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2cLPF2+YdBmaKfShGem0HK"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 775}, "_enabled": true, "__prefab": {"__id__": 791}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9dieB2EKRPLqocMypg4hKM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "97lPNW/CdGw6PEFHGMAN41", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_find", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 696}, "_children": [{"__id__": 794}, {"__id__": 800}], "_active": true, "_components": [{"__id__": 806}, {"__id__": 808}], "_prefab": {"__id__": 810}, "_lpos": {"__type__": "cc.Vec3", "x": 265, "y": 210, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 793}, "_children": [], "_active": true, "_components": [{"__id__": 795}, {"__id__": 797}], "_prefab": {"__id__": 799}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 1, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 794}, "_enabled": true, "__prefab": {"__id__": 796}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 89}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4bdcAiEHdNGLAR4O3REP8I"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 794}, "_enabled": true, "__prefab": {"__id__": 798}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "a5cc6882-adc2-4abc-a46c-498bd70071a9@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b3uFa80R1MK5LNfK7PsN85"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f8o9MU1WpEJ6ll4EbDqkHq", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 793}, "_children": [], "_active": true, "_components": [{"__id__": 801}, {"__id__": 803}], "_prefab": {"__id__": 805}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -40.787, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 800}, "_enabled": true, "__prefab": {"__id__": 802}, "_contentSize": {"__type__": "cc.Size", "width": 79.19999694824219, "height": 44.32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c16pG1R85IAbLb8QLm2fCG"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 800}, "_enabled": true, "__prefab": {"__id__": 804}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 242, "g": 220, "b": 72, "a": 255}, "_string": "探 寻", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 32, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 67, "g": 39, "b": 12, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "899SKBS0dG4p5yIw5GGwJF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c05jS+alxAtqdtzdPWBrfQ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 793}, "_enabled": true, "__prefab": {"__id__": 807}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 110}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6bMBtC6nFJWJ6pP6cNJF0c"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 793}, "_enabled": true, "__prefab": {"__id__": 809}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1ezyTjwntNRYxTB5akAe7S"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d469JAdR5Ljpn2AmllL0Kx", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_manage", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 696}, "_children": [{"__id__": 812}, {"__id__": 818}, {"__id__": 824}, {"__id__": 830}, {"__id__": 836}], "_active": true, "_components": [{"__id__": 842}, {"__id__": 844}], "_prefab": {"__id__": 846}, "_lpos": {"__type__": "cc.Vec3", "x": 2, "y": 179.754, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "img_guanli", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 811}, "_children": [], "_active": true, "_components": [{"__id__": 813}, {"__id__": 815}], "_prefab": {"__id__": 817}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 5.544, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 812}, "_enabled": true, "__prefab": {"__id__": 814}, "_contentSize": {"__type__": "cc.Size", "width": 290, "height": 44}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "212J5NRlVAgLakxQaVETZo"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 812}, "_enabled": true, "__prefab": {"__id__": 816}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3839715a-984d-409a-86a0-6133773b0a45@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0d1kKHh9pJvqxeUHuKhSir"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e13H0OaCVIia/1wklAzUIP", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "img_houtou_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 811}, "_children": [], "_active": true, "_components": [{"__id__": 819}, {"__id__": 821}], "_prefab": {"__id__": 823}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 11.544, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 818}, "_enabled": true, "__prefab": {"__id__": 820}, "_contentSize": {"__type__": "cc.Size", "width": 74, "height": 63}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c20K/cnvxKXaH3tgT+dDlm"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 818}, "_enabled": true, "__prefab": {"__id__": 822}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6d4b0efe-2ba0-4c94-ba48-f8c7952115d6@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aemQBUXKdJr5OUoh6rEOTL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "35gsCgqM9DPYUFr8+Mg9r5", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_xian", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 811}, "_children": [], "_active": true, "_components": [{"__id__": 825}, {"__id__": 827}], "_prefab": {"__id__": 829}, "_lpos": {"__type__": "cc.Vec3", "x": -40.866, "y": 8.544, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 824}, "_enabled": true, "__prefab": {"__id__": 826}, "_contentSize": {"__type__": "cc.Size", "width": 97.5, "height": 28.98}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aeaz2fLDNJprmCFp8O3DVF"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 824}, "_enabled": true, "__prefab": {"__id__": 828}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 124, "g": 242, "b": 72, "a": 255}, "_string": "闲 [10]", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 23, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "70BcySgXtG+pADTAu07oOB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "86hzinQyVONLdATQ8QNout", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_zong", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 811}, "_children": [], "_active": true, "_components": [{"__id__": 831}, {"__id__": 833}], "_prefab": {"__id__": 835}, "_lpos": {"__type__": "cc.Vec3", "x": 38.972, "y": 8.544, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 830}, "_enabled": true, "__prefab": {"__id__": 832}, "_contentSize": {"__type__": "cc.Size", "width": 97.5, "height": 28.98}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5cwQ8erYxAmr1ljfqRRW6E"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 830}, "_enabled": true, "__prefab": {"__id__": 834}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 185, "b": 254, "a": 255}, "_string": "总 [20]", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 23, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "60q141D7JG2Y9v42mY2dJD"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0a1myTV15P27ZHRHHuO+x/", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lbl_mgr", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 811}, "_children": [], "_active": true, "_components": [{"__id__": 837}, {"__id__": 839}], "_prefab": {"__id__": 841}, "_lpos": {"__type__": "cc.Vec3", "x": -5.537, "y": -31.337, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 836}, "_enabled": true, "__prefab": {"__id__": 838}, "_contentSize": {"__type__": "cc.Size", "width": 76.5, "height": 56.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "965dNFJqpAeZ071wuQ6pZ6"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 836}, "_enabled": true, "__prefab": {"__id__": 840}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 242, "g": 220, "b": 72, "a": 255}, "_string": "管 理", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 67, "g": 39, "b": 12, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "10hZacYDdPZ5icBBtmMHOY"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ba9CO1niBGbZ2t8OynyqvI", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 811}, "_enabled": true, "__prefab": {"__id__": 843}, "_contentSize": {"__type__": "cc.Size", "width": 290, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "46FPXiO19POJb6uZcjb5E1"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 811}, "_enabled": true, "__prefab": {"__id__": 845}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "99JlRKqZZCJrmqsrOs6udN"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e7cwMetz9Foamzmmdnj7ML", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 696}, "_enabled": true, "__prefab": {"__id__": 848}, "_contentSize": {"__type__": "cc.Size", "width": 684, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0fUIUaw01PrJgNQJ<PERSON><PERSON>yev"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 696}, "_enabled": true, "__prefab": {"__id__": 850}, "_alignFlags": 44, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "05fNm1g2xFya7MN9wGoiYc"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "61Cm+vQFJI64TSNhziOgaE", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 9}, "_enabled": true, "__prefab": {"__id__": 853}, "_contentSize": {"__type__": "cc.Size", "width": 684, "height": 1151.0430000000001}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3fzZZdO6NP84dbU0s9Dz0+"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 9}, "_enabled": true, "__prefab": {"__id__": 855}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 1, "_bottom": -1, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "443xz0LJRKg4aAlGkvlh/+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "72Vi9ak2xG4J9FGC0sK6X7", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_residue", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 2}}, "_parent": {"__id__": 8}, "_children": [{"__id__": 858}], "_active": true, "_components": [{"__id__": 864}, {"__id__": 866}], "_prefab": {"__id__": 868}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 506.454, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lbl_residue", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 857}, "_children": [], "_active": true, "_components": [{"__id__": 859}, {"__id__": 861}], "_prefab": {"__id__": 863}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 858}, "_enabled": true, "__prefab": {"__id__": 860}, "_contentSize": {"__type__": "cc.Size", "width": 299.25, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7dKiut2d5I8LoxXvcoU9CX"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 858}, "_enabled": true, "__prefab": {"__id__": 862}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "今日可采集次数1/1", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 35, "_fontSize": 35, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "83hsfVHZJIDIXpgsVKkheX"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "30HxR3gqtPJIDdCPiukGx3", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 857}, "_enabled": true, "__prefab": {"__id__": 865}, "_contentSize": {"__type__": "cc.Size", "width": 510, "height": 51}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e3Zx8jlilJA4Ycrujbam3Q"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 857}, "_enabled": true, "__prefab": {"__id__": 867}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "7fc2d4e8-5549-462e-bfa5-4a7d7e5dae24@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "94ufsvoFRCdYI8pLeYsM04"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6fhTEhwcFADZvKPF5qVJJE", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 8}, "_enabled": true, "__prefab": {"__id__": 870}, "_contentSize": {"__type__": "cc.Size", "width": 684, "height": 1151.0430000000001}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eaWQ3o6/tFYq6wOv7pEwoG"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 8}, "_enabled": true, "__prefab": {"__id__": 872}, "_alignFlags": 45, "_target": null, "_left": 33, "_right": 33, "_top": 169.27899999999988, "_bottom": 13.532, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 40, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dbRQCjte9Ggq+1E5cOcWvh"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 8}, "_enabled": true, "__prefab": {"__id__": 874}, "_type": 2, "_inverted": false, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "20uWiQq9FLEYGXjb7BwnRi"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 8}, "_enabled": true, "__prefab": {"__id__": 876}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "64zm3XnLdEhI5aGLaZp1vO"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9ffoAr1CFKQoFFCc+XwXS3", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ani", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 7}, "_children": [], "_active": true, "_components": [{"__id__": 879}, {"__id__": 881}], "_prefab": {"__id__": 883}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0.822, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 878}, "_enabled": true, "__prefab": {"__id__": 880}, "_contentSize": {"__type__": "cc.Size", "width": 898.5599975585938, "height": 1199.75}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.3800525295226391, "y": 0.47801625338612214}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8cTfVmyntES6HV+no54+fp"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 878}, "_enabled": true, "__prefab": {"__id__": 882}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "86b3d4db-d79e-419b-85e3-298d94562145", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "animation1", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bareDsReBCGKjekRdmPRo5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "79vOPK6FBE46a0Y+cYVee9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 7}, "_enabled": true, "__prefab": {"__id__": 885}, "_contentSize": {"__type__": "cc.Size", "width": 683, "height": 1147}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d2isdGu0FF7pN7jAildgYI"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "29xSptFshNA7jxIHQwzzxu", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.MountedChildrenInfo", "targetInfo": {"__id__": 888}, "nodes": [{"__id__": 889}]}, {"__type__": "cc.TargetInfo", "localID": ["8d9SnxGQhLO52jTVFOf8SD"]}, {"__type__": "cc.Node", "_name": "btn_xiangxixinxi", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 2}}, "_parent": {"__id__": 890}, "_children": [], "_active": true, "_components": [{"__id__": 917}, {"__id__": 919}, {"__id__": 921}], "_prefab": {"__id__": 923}, "_lpos": {"__type__": "cc.Vec3", "x": -139.235, "y": -54.108999999999924, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "dialog_head", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 891}, {"__id__": 899}, {"__id__": 889}], "_active": true, "_components": [{"__id__": 910}, {"__id__": 912}, {"__id__": 914}], "_prefab": {"__id__": 916}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 560.427, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "dialog_title", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 890}, "_children": [], "_active": true, "_components": [{"__id__": 892}, {"__id__": 894}, {"__id__": 896}], "_prefab": {"__id__": 898}, "_lpos": {"__type__": "cc.Vec3", "x": -264, "y": -52.00000000000001, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 891}, "_enabled": true, "__prefab": {"__id__": 893}, "_contentSize": {"__type__": "cc.Size", "width": 188, "height": 64.7}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "beMyl2ExxOuKrkO90pFIia"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 891}, "_enabled": true, "__prefab": {"__id__": 895}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "洞天福地", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 45, "_fontSize": 45, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 45, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 47, "g": 102, "b": 178, "a": 255}, "_outlineWidth": 4, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bb43HmgHRIsbkUVIAcdjyS"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 891}, "_enabled": true, "__prefab": {"__id__": 897}, "_alignFlags": 1, "_target": null, "_left": 0, "_right": 0, "_top": 126.15, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "edSF1SXAREsbAw8y0reh5O"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "28cNbKFtJKLL9k1nD3pOWI", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "dialog_close", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 890}, "_children": [], "_active": true, "_components": [{"__id__": 900}, {"__id__": 902}, {"__id__": 904}, {"__id__": 906}], "_prefab": {"__id__": 909}, "_lpos": {"__type__": "cc.Vec3", "x": 331, "y": -53.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 899}, "_enabled": true, "__prefab": {"__id__": 901}, "_contentSize": {"__type__": "cc.Size", "width": 78, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5dEGHE89tAEp4WR9Ug5/LE"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 899}, "_enabled": true, "__prefab": {"__id__": 903}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8e27d803-97dc-403c-bae5-609f4c45539b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "caeUIgmr9I5KI1BHdh4pGs"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 899}, "_enabled": true, "__prefab": {"__id__": 905}, "_alignFlags": 33, "_target": null, "_left": 0, "_right": 5, "_top": 121, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 33, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1cy8fqXs9ED5mTW/Gkk2mY"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 899}, "_enabled": true, "__prefab": {"__id__": 907}, "clickEvents": [{"__id__": 908}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "630kdrTEhE3r2bQfHu5867"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 2}, "component": "", "_componentId": "37daazP0QhHJq5Z8nQt0ily", "handler": "onClickClose", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1fOxWCfPxPAqI5OgnftRyd", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 890}, "_enabled": true, "__prefab": {"__id__": 911}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 213}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c81ZpQbBZMrp/EQrEebYzT"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 890}, "_enabled": true, "__prefab": {"__id__": 913}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "1876394a-9418-43ce-a671-ebfb941873a7@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e838MZ9zZJnIVTWDI/LTuG"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 890}, "_enabled": true, "__prefab": {"__id__": 915}, "_alignFlags": 1, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c0wAUzxMZLVaRR5ASN6AjK"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8d9SnxGQhLO52jTVFOf8SD", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 889}, "_enabled": true, "__prefab": {"__id__": 918}, "_contentSize": {"__type__": "cc.Size", "width": 45, "height": 45}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "945MGFaxpHgYpzitx0lbwu"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 889}, "_enabled": true, "__prefab": {"__id__": 920}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "15970a7b-7b45-475c-9675-180c125cec2f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c5TOzLvNtES7Hh9CgiRz96"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 889}, "_enabled": true, "__prefab": {"__id__": 922}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "62XFzDOCxPc6ExejLPnwQ5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c1x7yijDNBMbREpTOkCfVm", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 925}, "propertyPath": ["_name"], "value": "DialogPrimary"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 925}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 61.073, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 925}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 925}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 930}, "propertyPath": ["_lockFlags"], "value": 40}, {"__type__": "cc.TargetInfo", "localID": ["dbRQCjte9Ggq+1E5cOcWvh"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 932}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 750, "height": 1333.854}}, {"__type__": "cc.TargetInfo", "localID": ["dcMfg1dj1MdZqre7xUgJqz"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 934}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 716, "height": 1183.854}}, {"__type__": "cc.TargetInfo", "localID": ["44g2VAc1VIU42rC+WZOf2b"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 936}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 684, "height": 1151.0430000000001}}, {"__type__": "cc.TargetInfo", "localID": ["eaWQ3o6/tFYq6wOv7pEwoG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 938}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 560.427, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["8d9SnxGQhLO52jTVFOf8SD"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 925}, "propertyPath": ["_active"], "value": true}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 941}, "propertyPath": ["_enabled"], "value": true}, {"__type__": "cc.TargetInfo", "localID": ["20uWiQq9FLEYGXjb7BwnRi"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 943}, "propertyPath": ["_userRadius"], "value": 10}, {"__type__": "cc.TargetInfo", "localID": ["f2ofLYMWBPU4DygV2KdYZ4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 945}, "propertyPath": ["_string"], "value": "洞天福地"}, {"__type__": "cc.TargetInfo", "localID": ["bb43HmgHRIsbkUVIAcdjyS"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 945}, "propertyPath": ["_isBold"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 930}, "propertyPath": ["_bottom"], "value": 13.532}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 949}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -77.87349999999992, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["9ffoAr1CFKQoFFCc+XwXS3"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 951}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 188, "height": 64.7}}, {"__type__": "cc.TargetInfo", "localID": ["beMyl2ExxOuKrkO90pFIia"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 953}, "_contentSize": {"__type__": "cc.Size", "width": 712, "height": 1260}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8cDr6haYJIRLMNFbKFQ17y"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "59YxqUl0ZAjLFpgRhgOwFP", "instance": null, "targetOverrides": [{"__id__": 955}], "nestedPrefabInstanceRoots": [{"__id__": 2}, {"__id__": 679}, {"__id__": 658}, {"__id__": 551}, {"__id__": 530}, {"__id__": 423}, {"__id__": 402}, {"__id__": 295}, {"__id__": 274}, {"__id__": 163}, {"__id__": 140}, {"__id__": 24}]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 2}, "sourceInfo": {"__id__": 956}, "propertyPath": ["graphics"], "target": {"__id__": 2}, "targetInfo": {"__id__": 957}}, {"__type__": "cc.TargetInfo", "localID": ["f2ofLYMWBPU4DygV2KdYZ4"]}, {"__type__": "cc.TargetInfo", "localID": ["64zm3XnLdEhI5aGLaZp1vO"]}]