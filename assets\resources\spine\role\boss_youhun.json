{"skeleton": {"hash": "xaFVLIEOtTe3Okd3UNKskpoi+Z8=", "spine": "3.8.75", "x": -328.53, "y": -9.43, "width": 387.74, "height": 270.53, "images": "./images/", "audio": "D:/spine导出/boss动画/幽魂（金池）"}, "bones": [{"name": "root", "scaleX": -1}, {"name": "bone", "parent": "root", "length": 47.8, "rotation": 2.02, "x": 4.56, "y": 68.93}, {"name": "bone2", "parent": "bone", "length": 26.13, "rotation": 88.59, "x": -9.87, "y": -0.78}, {"name": "bone3", "parent": "bone2", "length": 20.56, "rotation": -4.53, "x": 26.13}, {"name": "bone4", "parent": "bone3", "length": 16.38, "rotation": 9.82, "x": 20.56}, {"name": "bone5", "parent": "bone2", "rotation": -4.53, "x": 43.31, "y": -49.08}, {"name": "bone6", "parent": "bone5", "length": 22.83, "rotation": -162.56}, {"name": "bone7", "parent": "bone6", "length": 22.1, "rotation": -20.83, "x": 22.83}, {"name": "bone31", "parent": "bone2", "rotation": -4.53, "x": 43.61, "y": 29.49}, {"name": "bone9", "parent": "bone31", "length": 30.44, "rotation": 159.96, "x": -2.16, "y": 2.5}, {"name": "bone10", "parent": "bone9", "length": 26.46, "rotation": 144.61, "x": 25.28, "y": 2.62}, {"name": "bone11", "parent": "bone10", "length": 23.15, "rotation": 69.84, "x": 26.46}, {"name": "bone12", "parent": "bone", "length": 16.21, "rotation": -117.7, "x": -15.05, "y": -19.99}, {"name": "bone13", "parent": "bone", "length": 20.65, "rotation": -58.12, "x": -11.06, "y": -18.73}, {"name": "bone14", "parent": "bone", "length": 14.36, "rotation": -88.65, "x": -12.25, "y": -28.53}, {"name": "bone15", "parent": "bone14", "length": 13.17, "rotation": 7.7, "x": 14.36}, {"name": "bone16", "parent": "bone", "length": 22.85, "rotation": -75.59, "x": 10.02, "y": -18.91}, {"name": "bone17", "parent": "bone16", "length": 19.77, "rotation": -1.6, "x": 22.85}, {"name": "bone18", "parent": "bone", "length": 20, "rotation": -94.81, "x": -28.92, "y": -22.88}, {"name": "bone19", "parent": "bone18", "length": 16.26, "rotation": -7.16, "x": 20.1, "y": -0.14}, {"name": "bone20", "parent": "bone10", "length": 19.09, "rotation": -106.13, "x": -2.27, "y": -2.21}, {"name": "bone21", "parent": "bone20", "length": 20.05, "rotation": -0.71, "x": 19.09}, {"name": "bone22", "parent": "bone5", "length": 24.4, "rotation": -178.83, "x": 6.66, "y": 6.46, "color": "98c235ff"}, {"name": "bone23", "parent": "bone22", "length": 30.62, "rotation": 3.11, "x": 24.4, "color": "98c235ff"}, {"name": "bone24", "parent": "bone23", "length": 23.27, "rotation": 3.96, "x": 30.62, "color": "98c235ff"}, {"name": "bone8", "parent": "bone3", "length": 20.09, "rotation": 179.83, "x": 14.84, "y": 25.14, "color": "d21e1eff"}, {"name": "bone25", "parent": "bone8", "length": 18.4, "rotation": -18.27, "x": 20.09, "color": "d21e1eff"}, {"name": "bone26", "parent": "bone25", "length": 10.95, "rotation": -3.47, "x": 21.73, "y": -0.17, "color": "d21e1eff"}, {"name": "bone27", "parent": "bone31", "length": 14.47, "rotation": 170.03, "x": 7.37, "y": -6.87, "color": "aff722ff"}, {"name": "bone28", "parent": "bone27", "length": 18.14, "rotation": 11.51, "x": 14.47, "color": "aff722ff"}, {"name": "bone29", "parent": "bone28", "length": 18.9, "rotation": -0.82, "x": 18.14, "color": "aff722ff"}, {"name": "bone30", "parent": "bone29", "length": 24.16, "rotation": 2.85, "x": 18.75, "y": -0.01, "color": "aff722ff"}, {"name": "bone32", "parent": "root", "x": 217.33, "y": 15.86, "scaleX": 0.7767, "scaleY": 0.7767}, {"name": "bone33", "parent": "root", "x": 217.33, "y": 15.86, "scaleX": 0.7767, "scaleY": 0.7767}, {"name": "bone34", "parent": "root", "x": 20.9, "y": 48.34, "color": "10ff00ff"}, {"name": "bone35", "parent": "root", "x": 6.36, "y": -24.28, "color": "e72424ff"}, {"name": "bone36", "parent": "root", "x": 6.36, "y": -24.28, "color": "e72424ff"}, {"name": "bone37", "parent": "root", "x": 8.75, "y": 19.64}, {"name": "bone38", "parent": "bone4", "rotation": -95.91, "x": 74.8, "y": -43.66}, {"name": "bone39", "parent": "bone4", "rotation": -95.91, "x": 85.53, "y": -55.71, "scaleX": 0.7856, "scaleY": 0.7856}], "slots": [{"name": "dilie", "bone": "bone37"}, {"name": "x4", "bone": "root"}, {"name": "s22", "bone": "bone26"}, {"name": "j2", "bone": "root", "attachment": "j2"}, {"name": "j1", "bone": "root", "attachment": "j1"}, {"name": "b1", "bone": "root", "attachment": "b1"}, {"name": "k1", "bone": "root", "attachment": "k1"}, {"name": "ss1", "bone": "root", "attachment": "ss1"}, {"name": "y2", "bone": "root", "attachment": "y2"}, {"name": "y1", "bone": "root", "attachment": "y1"}, {"name": "x1", "bone": "bone9", "attachment": "x1"}, {"name": "t1", "bone": "bone4", "attachment": "t1"}, {"name": "s1", "bone": "bone10", "attachment": "s1"}, {"name": "liu", "bone": "bone4", "attachment": "liu"}, {"name": "s2", "bone": "bone6", "attachment": "s2"}, {"name": "111light", "bone": "bone32", "color": "ffffff00", "attachment": "111light", "blend": "additive"}, {"name": "111light2", "bone": "bone33", "color": "ffffff00", "attachment": "111light", "blend": "additive"}, {"name": "tt2", "bone": "bone34", "blend": "additive"}, {"name": "ring", "bone": "bone35", "blend": "additive"}, {"name": "ring4", "bone": "bone35", "blend": "additive"}, {"name": "ring2", "bone": "bone36", "blend": "additive"}, {"name": "ring3", "bone": "bone36", "blend": "additive"}, {"name": "dilie1", "bone": "bone37", "blend": "additive"}, {"name": "han", "bone": "bone38", "color": "ffffff00", "attachment": "han", "blend": "additive"}, {"name": "han2", "bone": "bone39", "color": "ffffff00", "attachment": "han", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"ring2": {"ring": {"width": 94, "height": 43}}, "ring3": {"ring": {"width": 94, "height": 43}}, "ring4": {"ring": {"width": 94, "height": 43}}, "j1": {"j1": {"type": "mesh", "uvs": [0.29914, 0, 0.53045, 0, 0.71688, 0.05831, 0.79283, 0.26028, 0.82735, 0.41822, 0.83771, 0.59688, 0.91712, 0.74188, 1, 0.83251, 1, 0.93349, 0.80664, 0.96197, 0.6064, 0.99045, 0.29569, 0.98786, 0.237, 0.87393, 0.25426, 0.72376, 0.14033, 0.58135, 0.05748, 0.4467, 0.01259, 0.24215, 0.0264, 0.09456, 0.35783, 0.14894, 0.41307, 0.33536, 0.47866, 0.53474, 0.54771, 0.70822, 0.58914, 0.85063], "triangles": [11, 22, 10, 10, 22, 9, 11, 12, 22, 22, 13, 21, 13, 22, 12, 8, 9, 7, 6, 7, 9, 9, 22, 6, 22, 21, 6, 21, 5, 6, 13, 20, 21, 13, 14, 20, 21, 20, 5, 20, 4, 5, 20, 14, 19, 14, 15, 19, 20, 19, 4, 15, 16, 19, 19, 3, 4, 16, 18, 19, 3, 18, 2, 2, 18, 1, 18, 3, 19, 16, 17, 18, 17, 0, 18, 18, 0, 1], "vertices": [1, 16, -6.11, 1.92, 1, 1, 16, -3.36, 11.24, 1, 2, 16, 1.99, 17.82, 0.99507, 17, -21.35, 17.23, 0.00493, 2, 16, 13.74, 17.68, 0.86752, 17, -9.6, 17.42, 0.13248, 2, 16, 22.63, 16.57, 0.53006, 17, -0.68, 16.56, 0.46994, 2, 16, 32.35, 14.16, 0.0928, 17, 9.1, 14.42, 0.9072, 2, 16, 41.08, 15.06, 0.00126, 17, 17.8, 15.57, 0.99874, 1, 17, 23.6, 17.63, 1, 1, 17, 29.07, 16.19, 1, 1, 17, 28.53, 7.93, 1, 1, 17, 27.92, -0.61, 1, 1, 17, 24.44, -13.19, 1, 2, 16, 40.1, -14.43, 0, 17, 17.64, -13.94, 1, 2, 16, 32.24, -11.35, 0.06413, 17, 9.7, -11.09, 0.93587, 2, 16, 23.23, -13.69, 0.58245, 17, 0.76, -13.67, 0.41755, 2, 16, 15.02, -14.89, 0.93915, 17, -7.42, -15.11, 0.06085, 1, 16, 3.5, -13.46, 1, 1, 16, -4.27, -10.57, 1, 1, 16, 2.59, 1.92, 1, 2, 16, 13.26, 1.2, 0.99772, 17, -9.62, 0.93, 0.00228, 2, 16, 24.75, 0.68, 0.01316, 17, 1.88, 0.73, 0.98684, 1, 17, 12.01, 1.05, 1, 1, 17, 20.17, 0.69, 1], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 0, 36, 36, 38, 38, 40, 40, 42, 42, 44], "width": 40, "height": 54}}, "j2": {"j2": {"type": "mesh", "uvs": [0.62788, 0, 0.79954, 0.01525, 0.91895, 0.07725, 0.9911, 0.23125, 0.98612, 0.41725, 0.9715, 0.49866, 0.95378, 0.59725, 0.94426, 0.66616, 0.9289, 0.77725, 0.87666, 0.91325, 0.79456, 0.97925, 0.62788, 0.97325, 0.50349, 0.97525, 0.34427, 0.99125, 0.14774, 0.97125, 0, 0.89125, 0.01091, 0.77525, 0.14027, 0.74125, 0.25969, 0.72925, 0.27461, 0.57525, 0.28818, 0.49707, 0.30447, 0.40325, 0.28456, 0.17525, 0.33183, 0.03525, 0.64399, 0.17317, 0.62094, 0.40664, 0.62094, 0.58452, 0.56102, 0.79205, 0.38124, 0.85505, 0.12309, 0.86247], "triangles": [13, 28, 12, 13, 14, 28, 9, 10, 27, 11, 12, 27, 12, 28, 27, 10, 11, 27, 15, 29, 14, 14, 29, 28, 9, 27, 8, 15, 16, 29, 29, 18, 28, 29, 17, 18, 29, 16, 17, 28, 18, 27, 18, 19, 27, 27, 26, 8, 27, 19, 26, 8, 26, 7, 7, 26, 6, 19, 20, 26, 26, 20, 21, 6, 26, 5, 25, 26, 21, 26, 25, 5, 5, 25, 4, 4, 25, 3, 3, 25, 24, 24, 2, 3, 24, 1, 2, 24, 25, 22, 25, 21, 22, 22, 23, 24, 23, 0, 24, 24, 0, 1], "vertices": [1, 18, -8.73, -0.36, 1, 1, 18, -8.3, 6.71, 1, 1, 18, -5.38, 11.76, 1, 1, 18, 2.32, 15.09, 1, 1, 18, 11.81, 15.35, 1, 2, 18, 15.98, 14.95, 0.73221, 19, -5.97, 14.47, 0.26779, 2, 18, 21.04, 14.47, 0.40793, 19, -0.89, 14.62, 0.59207, 2, 18, 24.57, 14.25, 0.25176, 19, 2.64, 14.84, 0.74824, 1, 19, 8.33, 15.2, 1, 1, 19, 15.53, 14.29, 1, 1, 19, 19.43, 11.56, 1, 2, 18, 40.84, 2.06, 0.03454, 19, 20.3, 4.77, 0.96546, 2, 18, 41.19, -3.03, 0.00055, 19, 21.29, -0.23, 0.99945, 1, 19, 23.22, -6.52, 1, 1, 19, 23.61, -14.63, 1, 1, 19, 20.63, -21.3, 1, 1, 19, 14.73, -21.89, 1, 1, 19, 12.11, -16.96, 1, 1, 19, 10.66, -12.25, 1, 2, 18, 21.28, -13.39, 0.05171, 19, 2.82, -13, 0.94829, 2, 18, 17.27, -13.03, 0.28101, 19, -1.21, -13.14, 0.71899, 2, 18, 12.46, -12.6, 0.55619, 19, -6.04, -13.31, 0.44381, 2, 18, 0.88, -13.98, 0.95942, 19, -17.35, -16.12, 0.04058, 2, 18, -6.35, -12.39, 0.99678, 19, -24.72, -15.45, 0.00322, 1, 18, 0.06, 0.73, 1, 1, 18, 12, 0.37, 1, 2, 18, 21.06, 0.81, 0.40867, 19, 0.83, 1.07, 0.59133, 2, 18, 31.75, -1.13, 0.00433, 19, 11.68, 0.48, 0.99567, 1, 19, 16.12, -6.23, 1, 1, 19, 18.32, -16.59, 1], "hull": 24, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 42, 44, 44, 46, 0, 46, 0, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 38, 40, 40, 42, 40, 52, 52, 42, 38, 52, 8, 10, 10, 12, 52, 10, 12, 14, 14, 16, 52, 14], "width": 39, "height": 49}}, "b1": {"b1": {"type": "mesh", "uvs": [0, 0.45169, 0, 0.2589, 0.04912, 0.10259, 0.16033, 0, 0.3746, 0, 0.4831, 0, 0.71907, 0, 0.92521, 0.03224, 1, 0.14948, 1, 0.35009, 1, 0.47774, 0.96589, 0.72264, 0.86282, 0.88677, 0.64855, 0.98317, 0.42342, 1, 0.17118, 0.95451, 0.022, 0.8503, 0, 0.68617, 0.43156, 0.13125, 0.38545, 0.34748, 0.37189, 0.59498, 0.39901, 0.81382, 0.74077, 0.13646, 0.76247, 0.36311, 0.73263, 0.59498, 0.67838, 0.84769, 0.1983, 0.1573, 0.17118, 0.37874, 0.18474, 0.58717, 0.20101, 0.8008], "triangles": [18, 4, 5, 22, 6, 7, 26, 3, 4, 26, 4, 18, 2, 3, 26, 1, 2, 26, 19, 26, 18, 5, 6, 22, 18, 5, 22, 22, 7, 8, 8, 23, 22, 9, 23, 8, 27, 1, 26, 27, 26, 19, 0, 1, 27, 23, 9, 10, 0, 27, 28, 18, 23, 19, 23, 18, 22, 24, 23, 10, 24, 19, 23, 19, 20, 27, 20, 28, 27, 20, 19, 24, 17, 0, 28, 11, 24, 10, 29, 28, 20, 17, 28, 29, 21, 20, 24, 29, 20, 21, 25, 21, 24, 12, 25, 24, 16, 17, 29, 11, 12, 24, 15, 16, 29, 13, 21, 25, 13, 25, 12, 14, 21, 13, 15, 29, 21, 14, 15, 21], "vertices": [3, 2, 29.95, 27.62, 0.33311, 3, 1.62, 27.83, 0.58689, 8, -13.48, -2.95, 0.08, 3, 2, 44.6, 27.46, 0.04309, 3, 16.24, 28.83, 0.59691, 8, 1.14, -1.94, 0.36, 3, 2, 56.44, 23.75, 0.00267, 3, 28.33, 26.07, 0.63732, 8, 13.24, -4.71, 0.36, 2, 3, 36.67, 18.5, 0.64, 8, 21.57, -12.28, 0.36, 1, 3, 37.74, 2.9, 1, 1, 3, 38.28, -5, 1, 2, 2, 63.71, -25.24, 0.02014, 3, 39.45, -22.19, 0.97986, 3, 2, 61.1, -40.26, 0.03574, 3, 38.04, -37.37, 0.53226, 5, 17.03, 10.2, 0.432, 3, 2, 52.13, -45.62, 0.06055, 3, 29.52, -43.43, 0.50745, 5, 8.52, 4.14, 0.432, 3, 2, 36.89, -45.46, 0.15514, 3, 14.31, -44.47, 0.41286, 5, -6.69, 3.1, 0.432, 2, 2, 27.18, -45.36, 0.43821, 3, 4.63, -45.13, 0.56179, 2, 2, 8.6, -42.67, 0.74746, 3, -14.11, -43.92, 0.25254, 2, 2, -3.79, -35.01, 0.892, 3, -27.07, -37.26, 0.108, 2, 2, -10.95, -19.29, 0.98443, 3, -35.44, -22.16, 0.01557, 1, 2, -12.05, -2.84, 1, 2, 2, -8.4, 15.53, 0.99973, 3, -35.65, 12.76, 0.00027, 2, 2, -0.36, 26.34, 0.97325, 3, -28.49, 24.16, 0.02675, 2, 2, 12.13, 27.81, 0.84883, 3, -16.16, 26.62, 0.15117, 1, 3, 28.07, -1.93, 1, 1, 3, 11.44, 0.3, 1, 1, 2, 18.77, 0.59, 1, 1, 2, 2.11, -1.21, 1, 2, 2, 53.32, -26.71, 0.04977, 3, 29.22, -24.48, 0.95023, 2, 2, 36.08, -28.11, 0.25557, 3, 12.14, -27.24, 0.74443, 2, 2, 18.48, -25.74, 0.67042, 3, -5.59, -26.27, 0.32958, 2, 2, -0.68, -21.58, 0.94998, 3, -25.02, -23.63, 0.05002, 2, 2, 52.17, 12.9, 0.00193, 3, 24.93, 14.92, 0.99807, 2, 2, 35.36, 15.06, 0.15653, 3, 8, 15.75, 0.84347, 2, 2, 19.51, 14.24, 0.74691, 3, -7.73, 13.68, 0.25309, 2, 2, 3.26, 13.23, 0.98787, 3, -23.85, 11.38, 0.01213], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 10, 36, 36, 38, 38, 40, 40, 42, 12, 44, 44, 46, 46, 48, 48, 50, 8, 52, 52, 54, 54, 56, 56, 58], "width": 71, "height": 74}}, "ss1": {"ss1": {"type": "mesh", "uvs": [0.08478, 0.07551, 0, 0.06392, 0, 0.31876, 0.23604, 0.41528, 0.17677, 0.53884, 0.1134, 0.68942, 0.11953, 0.82456, 0.26057, 0.93653, 0.37709, 0.93653, 0.40979, 0.84001, 0.40276, 0.68089, 0.42688, 0.80922, 0.45716, 0.91136, 0.51158, 1, 0.5954, 0.94469, 0.63511, 0.79747, 0.6704, 0.61969, 0.63363, 0.5308, 0.57334, 0.47525, 0.66305, 0.46136, 0.7954, 0.40858, 0.90716, 0.35025, 1, 0.27525, 1, 0, 0.87628, 0.06969, 0.7454, 0.15025, 0.60716, 0.20858, 0.45422, 0.2308, 0.38413, 0.22945, 0.31011, 0.22802, 0.19099, 0.15025, 0.27652, 0.3174, 0.51251, 0.3551, 0.39442, 0.45446], "triangles": [8, 7, 9, 7, 5, 4, 7, 4, 10, 7, 10, 9, 4, 3, 10, 7, 6, 5, 13, 12, 14, 14, 12, 15, 17, 15, 18, 18, 15, 11, 11, 15, 12, 11, 10, 18, 15, 17, 16, 18, 10, 33, 10, 3, 33, 33, 3, 31, 3, 30, 31, 31, 28, 33, 2, 0, 30, 31, 30, 29, 31, 29, 28, 18, 32, 26, 32, 27, 26, 19, 18, 26, 19, 25, 20, 20, 25, 21, 25, 24, 21, 21, 24, 22, 24, 23, 22, 19, 26, 25, 33, 28, 27, 33, 27, 32, 2, 1, 0, 3, 2, 30, 18, 33, 32], "vertices": [1, 1, -32.3, -7.73, 1, 1, 1, -38.05, -7.11, 1, 1, 1, -38.37, -16.28, 1, 1, 1, -22.45, -20.32, 1, 1, 12, 9.48, -8.11, 1, 1, 12, 16.24, -9.65, 1, 1, 12, 20.44, -7.17, 1, 2, 13, 11.84, -19.59, 0.00065, 12, 19.92, 3.22, 0.99935, 2, 13, 16.26, -13.02, 0.04053, 12, 16.48, 10.37, 0.95947, 2, 13, 14.61, -9.23, 0.09016, 12, 12.39, 10.86, 0.90984, 2, 13, 9.59, -6.43, 0.54298, 12, 7.43, 7.95, 0.45702, 2, 13, 14.34, -7.65, 0.95227, 12, 10.89, 11.43, 0.04773, 2, 13, 18.54, -7.99, 0.99629, 12, 13.31, 14.88, 0.00371, 1, 13, 23.25, -6.7, 1, 1, 13, 24.78, -0.86, 1, 1, 13, 21.89, 4.34, 1, 1, 13, 17.92, 9.9, 1, 1, 13, 13.87, 9.61, 1, 1, 1, 0.39, -23.28, 1, 1, 1, 6.51, -23, 1, 1, 1, 15.57, -21.42, 1, 1, 1, 23.24, -19.59, 1, 1, 1, 29.64, -17.11, 1, 1, 1, 29.99, -7.21, 1, 1, 1, 21.49, -9.42, 1, 1, 1, 12.5, -12, 1, 1, 1, 3.03, -13.77, 1, 1, 1, -7.39, -14.2, 1, 1, 1, -12.15, -13.99, 1, 1, 1, -17.18, -13.76, 1, 1, 1, -25.18, -10.67, 1, 1, 1, -19.58, -16.89, 1, 1, 1, -3.59, -18.81, 1, 2, 13, 2.51, -2.36, 0.68436, 12, 0.33, 3.91, 0.31564], "hull": 31, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 58, 60, 0, 60, 6, 62, 54, 64, 20, 66, 54, 56, 56, 58, 66, 56], "width": 66, "height": 34}}, "han": {"han": {"x": 0.05, "y": 0.14, "width": 13, "height": 19}}, "y1": {"y1": {"type": "mesh", "uvs": [0.99395, 0.02665, 0.69895, 0.07459, 0.53506, 0.14834, 0.50884, 0.27002, 0.50228, 0.45808, 0.47606, 0.6314, 0.50228, 0.79918, 0.50228, 0.9983, 0.20728, 1, 0, 0.93561, 0.00406, 0.80287, 0.03684, 0.60927, 0.08273, 0.44333, 0.12861, 0.27002, 0.19417, 0.12621, 0.31217, 0.03034, 0.63339, 0, 1, 0, 0.39739, 0.07827, 0.33839, 0.15202, 0.29906, 0.28109, 0.2925, 0.44702, 0.25973, 0.62402, 0.22039, 0.8084, 0.24662, 0.94668], "triangles": [8, 24, 7, 8, 9, 24, 24, 6, 7, 9, 23, 24, 24, 23, 6, 9, 10, 23, 6, 23, 22, 22, 23, 10, 6, 22, 5, 10, 11, 22, 5, 22, 4, 4, 22, 21, 22, 11, 21, 11, 12, 21, 21, 20, 4, 4, 20, 3, 21, 12, 20, 12, 13, 20, 3, 20, 19, 20, 13, 19, 3, 19, 2, 13, 14, 19, 2, 19, 18, 19, 14, 18, 2, 18, 1, 14, 15, 18, 1, 18, 16, 18, 15, 16, 1, 16, 0, 0, 16, 17], "vertices": [1, 28, -11.48, 12.1, 1, 1, 28, -5.1, 5.47, 1, 1, 28, 2.84, 2.88, 1, 2, 28, 14.35, 4.99, 0.40401, 29, 0.88, 4.92, 0.59599, 2, 29, 18.92, 5.49, 0.37449, 30, 0.71, 5.5, 0.62551, 2, 30, 17.36, 5.73, 0.84919, 31, -1.11, 5.8, 0.15081, 1, 31, 15, 6.6, 1, 1, 31, 34.11, 6.72, 1, 1, 31, 34.33, -1.24, 1, 1, 31, 28.18, -6.88, 1, 2, 30, 34.51, -6.08, 0.00065, 31, 15.43, -6.85, 0.99935, 2, 30, 15.9, -6.23, 0.78369, 31, -3.16, -6.08, 0.21631, 2, 29, 17.98, -5.89, 0.51284, 30, -0.07, -5.89, 0.48716, 2, 28, 16.81, -4.97, 0.27133, 29, 1.31, -5.34, 0.72867, 1, 28, 2.98, -6.57, 1, 1, 28, -6.72, -5.69, 1, 1, 28, -11.62, 2.03, 1, 1, 28, -14, 11.64, 1, 1, 28, -2.8, -2.35, 1, 1, 28, 4.45, -2.19, 1, 1, 29, 2.18, -0.7, 1, 2, 29, 18.1, -0.21, 0.51816, 30, -0.03, -0.21, 0.48184, 2, 30, 16.98, -0.15, 0.99694, 31, -1.78, -0.05, 0.00306, 1, 31, 15.93, -1, 1, 1, 31, 29.2, -0.21, 1], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 32, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48], "width": 25, "height": 94}}, "y2": {"y2": {"type": "mesh", "uvs": [0.09864, 0, 0.42348, 0, 0.76379, 0.05647, 1, 0.14142, 1, 0.31296, 0.81535, 0.42895, 0.74316, 0.55637, 0.8102, 0.723, 1, 0.92394, 0.67098, 0.97622, 0.16567, 1, 0.02645, 0.82429, 0.01067, 0.68199, 0, 0.58578, 0.00067, 0.3832, 0.10379, 0.17573, 0.20692, 0.07444, 0.57301, 0.1839, 0.45442, 0.30315, 0.39254, 0.47632, 0.41317, 0.66256, 0.50598, 0.82592], "triangles": [12, 13, 20, 20, 6, 7, 11, 12, 20, 21, 20, 7, 11, 20, 21, 21, 7, 8, 9, 21, 8, 10, 11, 21, 10, 21, 9, 19, 14, 18, 19, 18, 5, 6, 19, 5, 13, 14, 19, 20, 19, 6, 13, 19, 20, 16, 0, 1, 2, 16, 1, 17, 2, 3, 17, 16, 2, 15, 16, 17, 18, 15, 17, 17, 3, 4, 18, 17, 4, 14, 15, 18, 5, 18, 4], "vertices": [1, 22, -10.5, -11.92, 1, 1, 22, -11, -1.54, 1, 1, 22, -5.82, 9.62, 1, 2, 22, 2.38, 17.58, 0.99993, 23, -21.03, 18.75, 7e-05, 2, 22, 19.69, 18.41, 0.81782, 23, -3.71, 18.64, 0.18218, 2, 22, 31.67, 13.07, 0.19909, 23, 7.97, 12.65, 0.80091, 2, 23, 20.82, 10.26, 0.91287, 24, -9.06, 10.91, 0.08713, 2, 23, 37.67, 12.3, 0.0189, 24, 7.88, 11.78, 0.9811, 1, 24, 28.58, 16.31, 1, 1, 24, 33.05, 5.41, 1, 1, 24, 34.23, -10.89, 1, 1, 24, 16.19, -14, 1, 2, 23, 33.36, -13.26, 0.21312, 24, 1.82, -13.42, 0.78688, 2, 23, 23.64, -13.54, 0.78899, 24, -7.89, -13.02, 0.21101, 2, 22, 28.31, -13.19, 0.18136, 23, 3.18, -13.39, 0.81864, 2, 22, 7.22, -10.9, 0.99941, 23, -17.75, -9.95, 0.00059, 1, 22, -3.16, -8.1, 1, 1, 22, 7.32, 4.13, 1, 2, 22, 19.54, 0.92, 0.99955, 23, -4.81, 1.18, 0.00045, 1, 23, 12.67, -0.91, 1, 2, 23, 31.48, -0.37, 0.11607, 24, 0.84, -0.43, 0.88393, 1, 24, 17.52, 1.29, 1], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32, 4, 34, 34, 36, 36, 38, 38, 40, 40, 42], "width": 30, "height": 99}}, "liu": {"liu": {"x": 121.87, "y": -4.11, "rotation": -95.91, "width": 55, "height": 50}}, "s1": {"s1": {"type": "mesh", "uvs": [0.01744, 0.42785, 0.12079, 0.3358, 0.22414, 0.23705, 0.35134, 0.16006, 0.49841, 0.13998, 0.44276, 0.08475, 0.40301, 0.02282, 0.56599, 0, 0.76076, 0, 0.91181, 0.0496, 1, 0.12994, 1, 0.21864, 0.97144, 0.31404, 0.86014, 0.36593, 0.76871, 0.40107, 0.76076, 0.54668, 0.74884, 0.69732, 0.74486, 0.8764, 0.62164, 1, 0.47456, 1, 0.27184, 0.92996, 0.16451, 0.81113, 0.06116, 0.69062, 0, 0.55673, 0.59381, 0.45463, 0.43084, 0.49982, 0.44276, 0.22701, 0.44674, 0.40442, 0.41494, 0.66049, 0.48251, 0.85632], "triangles": [16, 28, 15, 21, 22, 28, 29, 28, 16, 21, 28, 29, 17, 29, 16, 20, 21, 29, 19, 20, 29, 18, 19, 29, 17, 18, 29, 23, 0, 25, 28, 23, 25, 28, 25, 15, 22, 23, 28, 5, 6, 7, 4, 5, 7, 4, 9, 10, 4, 7, 8, 9, 4, 8, 10, 11, 4, 11, 26, 4, 12, 26, 11, 2, 3, 26, 13, 26, 12, 14, 26, 13, 27, 26, 14, 2, 26, 27, 1, 2, 27, 0, 1, 27, 24, 27, 14, 25, 0, 27, 25, 27, 24, 15, 24, 14, 25, 24, 15, 26, 3, 4], "vertices": [3, 10, -5.22, 4.48, 0.59991, 11, -6.71, 31.28, 3e-05, 20, -5.6, -4.7, 0.40006, 1, 10, 2.79, 9.9, 1, 1, 10, 11.13, 15.86, 1, 1, 10, 19.24, 19.56, 1, 2, 10, 25.27, 18.2, 0.768, 11, 16.68, 7.39, 0.232, 2, 10, 26.03, 23.85, 0.00764, 11, 22.24, 8.62, 0.99236, 1, 11, 28.31, 9.11, 1, 1, 11, 29.26, 2.31, 1, 1, 11, 27.84, -5.35, 1, 1, 11, 22.11, -10.44, 1, 1, 11, 13.96, -12.52, 1, 2, 10, 38.72, 1.54, 0.00386, 11, 5.67, -10.98, 0.99614, 2, 10, 33.12, -5.67, 0.32943, 11, -3.03, -8.21, 0.67057, 4, 10, 26.77, -7.64, 0.86129, 11, -7.07, -2.93, 0.13013, 20, -2.85, 29.41, 0.00846, 21, -22.3, 29.14, 0.00012, 4, 10, 21.93, -8.65, 0.93444, 11, -9.68, 1.27, 0.00078, 20, -0.53, 25.03, 0.06065, 21, -19.93, 24.79, 0.00413, 3, 10, 14.6, -20.39, 0.4371, 20, 12.78, 21.26, 0.4104, 21, -6.57, 21.18, 0.1525, 3, 10, 6.9, -32.46, 0.06873, 20, 26.51, 17.21, 0.2021, 21, 7.21, 17.3, 0.72917, 3, 10, -1.91, -47.01, 0.0001, 20, 42.94, 12.79, 0.00022, 21, 23.69, 13.09, 0.99968, 1, 21, 33.92, 5.5, 1, 1, 21, 32.52, -0.22, 1, 1, 21, 24.12, -6.5, 1, 2, 20, 31.12, -8.13, 0.00123, 21, 12.13, -7.98, 0.99877, 2, 20, 19, -9.26, 0.53733, 21, 0.03, -9.26, 0.46267, 1, 20, 6.08, -8.44, 1, 3, 10, 13.31, -9.46, 0.76651, 20, 2.64, 16.99, 0.21612, 21, -16.66, 16.78, 0.01737, 3, 10, 5.52, -9.83, 0.47442, 20, 5.16, 9.6, 0.51167, 21, -14.05, 9.43, 0.01392, 2, 10, 19.14, 12.22, 0.728, 11, 8.95, 11.08, 0.272, 3, 10, 10.68, -2.36, 0.95868, 20, -3.45, 12.49, 0.04099, 21, -22.69, 12.21, 0.00033, 3, 10, -2.81, -22.64, 0.0312, 20, 19.78, 5.16, 0.43199, 21, 0.63, 5.16, 0.53681, 1, 21, 19.34, 3.35, 1], "hull": 24, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 0, 46, 28, 48, 48, 50, 8, 52, 52, 54, 54, 50, 50, 56, 56, 58], "width": 38, "height": 93}}, "s2": {"s2": {"type": "mesh", "uvs": [0.47272, 0, 0.74356, 0.0468, 0.94397, 0.21636, 0.97994, 0.35378, 1, 0.43044, 0.9777, 0.59824, 0.96564, 0.68903, 0.81397, 0.90098, 0.57564, 1, 0.26689, 0.98365, 0.06647, 0.80984, 0, 0.66147, 0, 0.55973, 0.08814, 0.53642, 0.13689, 0.46011, 0.19494, 0.42197, 0.16182, 0.32986, 0.14782, 0.19154, 0.24582, 0.05869, 0.51882, 0.06006, 0.58182, 0.18332, 0.64832, 0.33123, 0.66582, 0.43121, 0.60982, 0.57365, 0.53282, 0.70102, 0.36132, 0.84345], "triangles": [7, 8, 25, 8, 9, 25, 9, 10, 25, 25, 24, 7, 7, 24, 6, 25, 10, 24, 10, 11, 24, 11, 13, 24, 24, 23, 6, 24, 13, 23, 6, 23, 5, 11, 12, 13, 23, 22, 5, 5, 22, 4, 13, 14, 23, 14, 15, 23, 23, 15, 22, 15, 21, 22, 22, 3, 4, 22, 21, 3, 15, 16, 21, 21, 2, 3, 16, 20, 21, 21, 20, 2, 16, 17, 20, 20, 1, 2, 20, 18, 19, 20, 17, 18, 20, 19, 1, 18, 0, 19, 19, 0, 1], "vertices": [1, 6, -12.9, 2.55, 1, 1, 6, -6.44, 11.03, 1, 2, 6, 10.42, 14.39, 0.99923, 7, -16.72, 9.04, 0.00077, 2, 6, 23.01, 12.7, 0.67407, 7, -4.35, 11.93, 0.32593, 2, 6, 30.04, 11.75, 0.24032, 7, 2.56, 13.54, 0.75968, 1, 7, 17.97, 14.71, 1, 1, 7, 26.31, 15.34, 1, 1, 7, 46.35, 12.41, 1, 1, 7, 56.47, 5.05, 1, 1, 7, 56.4, -6.16, 1, 1, 7, 41.45, -15.35, 1, 1, 7, 28.22, -19.46, 1, 2, 6, 33.19, -26.03, 0.00473, 7, 18.93, -20.65, 0.99527, 2, 6, 31.84, -22.45, 0.02063, 7, 16.4, -17.78, 0.97937, 2, 6, 25.43, -19.1, 0.14187, 7, 9.22, -16.93, 0.85813, 2, 6, 22.5, -16.25, 0.31167, 7, 5.47, -15.3, 0.68833, 2, 6, 13.99, -15.42, 0.77287, 7, -2.78, -17.56, 0.22713, 2, 6, 1.49, -12.94, 0.99321, 7, -15.34, -19.68, 0.00679, 1, 6, -9.56, -6.65, 1, 1, 6, -7.14, 2.88, 1, 1, 6, 4.41, 2.43, 1, 1, 6, 18.2, 1.57, 1, 1, 7, 4.16, 1.62, 1, 1, 7, 17.41, 1.29, 1, 1, 7, 29.39, 0.03, 1, 1, 7, 43.17, -4.43, 1], "hull": 19, "edges": [0, 2, 2, 4, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36, 0, 38, 38, 40, 40, 34, 40, 42, 4, 6, 6, 8, 42, 6, 42, 44, 44, 46, 8, 10, 10, 12, 46, 48, 48, 50], "width": 34, "height": 90}}, "dilie": {"dilie": {"x": -1.56, "y": -12.5, "width": 249, "height": 118}}, "111light": {"111light": {"x": -43.34, "y": 102.94, "width": 373, "height": 271}}, "k1": {"k1": {"type": "mesh", "uvs": [0.00846, 0.00984, 0.13528, 0.0534, 0.24625, 0.11251, 0.3774, 0.14673, 0.52439, 0.12651, 0.6686, 0.07513, 0.81385, 0.0298, 0.9171, 0, 1, 0.06002, 1, 0.21302, 1, 0.38302, 0.8821, 0.43213, 0.7316, 0.47558, 0.6546, 0.5228, 0.6126, 0.65502, 0.61435, 0.81558, 0.61785, 1, 0.5006, 1, 0.39035, 1, 0.2556, 1, 0.2451, 0.83446, 0.20485, 0.63991, 0.18035, 0.52091, 0.06485, 0.47558, 0, 0.44346, 0, 0.26024, 0, 0.09402, 0.4201, 0.83069, 0.3886, 0.63235, 0.37285, 0.36602, 0.57935, 0.34524, 0.78585, 0.28291, 0.1506, 0.32069], "triangles": [27, 14, 15, 20, 28, 27, 18, 19, 20, 27, 18, 20, 17, 27, 15, 18, 27, 17, 17, 15, 16, 21, 22, 28, 14, 28, 13, 27, 28, 14, 20, 21, 28, 23, 32, 22, 28, 30, 13, 23, 24, 32, 22, 32, 29, 29, 3, 4, 30, 29, 4, 13, 30, 12, 6, 7, 8, 31, 5, 6, 9, 6, 8, 31, 6, 9, 30, 4, 5, 30, 5, 31, 31, 9, 10, 11, 31, 10, 12, 30, 31, 12, 31, 11, 26, 0, 1, 25, 26, 1, 32, 1, 2, 25, 1, 32, 29, 2, 3, 32, 2, 29, 24, 25, 32, 28, 29, 30, 22, 29, 28], "vertices": [1, 1, -37.06, 4.54, 1, 1, 1, -28.54, 1.5, 1, 1, 1, -21.13, -2.49, 1, 1, 1, -12.29, -4.96, 1, 1, 1, -2.26, -4.04, 1, 1, 1, 7.65, -1.15, 1, 1, 1, 17.63, 1.36, 1, 1, 1, 24.71, 2.98, 1, 1, 1, 30.21, -0.99, 1, 1, 1, 29.87, -10.63, 1, 1, 1, 29.49, -21.33, 1, 1, 1, 21.37, -24.14, 1, 1, 1, 11.05, -26.51, 1, 1, 1, 5.71, -29.3, 1, 3, 1, 2.56, -37.53, 0.06181, 14, 9.34, 14.6, 0.72543, 15, -3.01, 15.14, 0.21275, 3, 1, 2.32, -47.64, 0.00263, 14, 19.45, 14.12, 0.19955, 15, 6.94, 13.31, 0.79783, 2, 14, 31.06, 13.68, 0.00013, 15, 18.39, 11.32, 0.99987, 1, 15, 16.86, 3.49, 1, 1, 15, 15.42, -3.87, 1, 1, 15, 13.66, -12.86, 1, 2, 14, 19.16, -11.01, 0.23085, 15, 3.29, -11.56, 0.76915, 2, 14, 6.76, -13.02, 0.95399, 15, -9.27, -11.89, 0.04601, 1, 1, -26.52, -28.05, 1, 1, 1, -34.26, -24.91, 1, 1, 1, -38.6, -22.74, 1, 1, 1, -38.19, -11.2, 1, 1, 1, -37.82, -0.74, 1, 3, 1, -10.91, -48.12, 0, 14, 19.62, 0.88, 0.00031, 15, 5.34, 0.17, 0.99969, 1, 14, 7.02, -0.52, 1, 1, 1, -13.09, -18.75, 1, 1, 1, 0.99, -17.94, 1, 1, 1, 15.16, -14.51, 1, 1, 1, -28.09, -15.37, 1], "hull": 27, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 0, 52, 34, 54, 36, 54, 54, 56, 56, 58, 58, 60, 60, 62, 58, 64], "width": 66, "height": 61}}, "111light2": {"111light": {"x": -43.34, "y": 102.94, "width": 373, "height": 271}}, "dilie1": {"dilie1": {"x": 2.1, "y": -14.81, "width": 209, "height": 85}}, "ring": {"ring": {"width": 94, "height": 43}}, "tt2": {"tt2": {"x": -11.8, "y": -1.29, "rotation": -178.03, "width": 152, "height": 167}}, "s22": {"s22": {"x": 7.35, "y": -1.15, "rotation": 115.82, "width": 17, "height": 21}}, "x1": {"x1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32.22, 29.27, 46.03, -1.8, -1.49, -22.92, -15.3, 8.15], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 50}}, "han2": {"han": {"x": 0.05, "y": 0.14, "width": 13, "height": 19}}, "x4": {"x4": {"type": "mesh", "uvs": [0.46232, 0.05354, 0.67432, 0.00894, 0.78562, 0, 1, 0.05741, 1, 0.21448, 1, 0.38511, 1, 0.58483, 0.85452, 0.73801, 0.70082, 0.91834, 0.51002, 0.99978, 0.22912, 0.98427, 0.01182, 0.91446, 0, 0.71668, 0.03832, 0.55186, 0.19202, 0.40256, 0.25009, 0.3158, 0.31922, 0.21254, 0.67432, 0.15243, 0.61072, 0.31143, 0.50472, 0.50145, 0.38812, 0.69341, 0.20262, 0.87374], "triangles": [8, 9, 21, 11, 21, 10, 9, 10, 21, 21, 20, 8, 8, 20, 7, 11, 12, 21, 21, 12, 20, 7, 20, 6, 12, 13, 20, 20, 19, 6, 20, 13, 19, 19, 5, 6, 13, 14, 19, 19, 14, 18, 19, 18, 5, 18, 14, 15, 18, 4, 5, 15, 16, 18, 18, 17, 4, 18, 16, 17, 17, 3, 4, 16, 0, 17, 0, 1, 17, 17, 2, 3, 17, 1, 2], "vertices": [1, 25, -4.51, -6.06, 1, 1, 25, -8.61, 0.02, 1, 1, 25, -9.58, 3.3, 1, 1, 25, -5.34, 10.05, 1, 2, 25, 7.51, 10.97, 0.99968, 26, -15.38, 6.47, 0.00032, 2, 25, 21.46, 11.97, 0.48634, 26, -2.44, 11.79, 0.51366, 2, 25, 37.8, 13.13, 0.0011, 26, 12.7, 18.02, 0.9989, 1, 26, 25.98, 18.76, 1, 1, 26, 41.41, 20.12, 1, 1, 26, 49.76, 17.37, 1, 1, 26, 51.79, 9.09, 1, 1, 26, 48.98, 0.89, 1, 1, 26, 34.11, -5.61, 1, 1, 26, 21.18, -9.69, 1, 2, 25, 24.62, -12.11, 0.10705, 26, 8.1, -10.08, 0.89295, 2, 25, 17.4, -10.88, 0.56896, 26, 0.86, -11.17, 0.43104, 2, 25, 8.8, -9.41, 0.96534, 26, -7.76, -12.48, 0.03466, 1, 25, 3.13, 0.86, 1, 2, 25, 16.27, -0.11, 0.99878, 26, -3.59, -1.3, 0.00122, 1, 26, 12.03, 1.68, 1, 1, 26, 27.92, 4.43, 1, 1, 26, 43.71, 4.91, 1], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 32, 4, 34, 34, 36, 36, 38, 38, 40, 40, 42, 28, 30, 30, 32], "width": 28, "height": 80}}, "t1": {"t1": {"x": 64.96, "y": -16.71, "rotation": -95.91, "width": 130, "height": 142}}}}], "events": {"atk": {}}, "animations": {"boss_attack1": {"slots": {"111light2": {"color": [{"time": 0.5333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "color": "ffffff00"}]}, "111light": {"color": [{"time": 0.5333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.8, "color": "ffffff00"}]}}, "bones": {"bone21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 31.25, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2333, "angle": 25.49, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -28.02, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone14": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -28.12, "curve": "stepped"}, {"time": 0.4, "angle": -28.12, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 22.69, "curve": 0.25, "c3": 0.75}, {"time": 0.8667}]}, "bone17": {"rotate": [{"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 55.21, "curve": 0.25, "c3": 0.75}, {"time": 0.8667}], "translate": [{"curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.1, "x": -2.9, "y": 0.74, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 0.2}]}, "bone15": {"rotate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -28.12, "curve": "stepped"}, {"time": 0.5333, "angle": -28.12, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 22.69, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.8667}]}, "bone3": {"rotate": [{"angle": -0.36, "curve": "stepped"}, {"time": 0.0667, "angle": -0.36, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -7.26, "curve": "stepped"}, {"time": 0.4667, "angle": -7.26, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 46.14, "curve": 0.365, "c2": 0.46, "c3": 0.709, "c4": 0.84}, {"time": 0.7667, "angle": 3.61, "curve": 0.356, "c2": 0.65, "c3": 0.691}, {"time": 0.8667, "angle": -0.36}], "translate": [{"x": 0.31}]}, "bone31": {"translate": [{"x": 0.71, "y": 0.74}]}, "bone5": {"rotate": [{"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 15.99, "curve": 0.25, "c3": 0.75}, {"time": 0.8667}], "translate": [{"x": 1.41, "y": -2, "curve": "stepped"}, {"time": 0.4, "x": 1.41, "y": -2, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 12.43, "y": 14.17, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.8667, "x": 1.41, "y": -2}]}, "bone12": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -20.08, "curve": "stepped"}, {"time": 0.5667, "angle": -20.08, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 26.69, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.8667}]}, "bone18": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -67.23, "curve": "stepped"}, {"time": 0.4, "angle": -67.23, "curve": 0.25, "c3": 0.75}, {"time": 0.6}], "translate": [{"curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.1, "x": -0.01, "y": 6.99, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.2667}]}, "bone9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -40.83, "curve": "stepped"}, {"time": 0.4, "angle": -40.83, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -21.36, "curve": 0.25, "c3": 0.75}, {"time": 0.8667}]}, "bone6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 63.1, "curve": "stepped"}, {"time": 0.4, "angle": 63.1, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 54.41, "curve": 0.25, "c3": 0.75}, {"time": 0.8667}]}, "bone30": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 17.66, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -10.18, "curve": "stepped"}, {"time": 0.7333, "angle": -10.18, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.8667}]}, "bone13": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -20.08, "curve": "stepped"}, {"time": 0.5, "angle": -20.08, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 26.69, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.8667}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -18.41, "curve": "stepped"}, {"time": 0.4, "angle": -18.41, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 2.37, "curve": 0.25, "c3": 0.75}, {"time": 0.8667}]}, "bone4": {"rotate": [{"angle": -8.66, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 0.1, "angle": -0.9, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3667, "angle": -17, "curve": "stepped"}, {"time": 0.4667, "angle": -17, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 56.74, "curve": "stepped"}, {"time": 0.6667, "angle": 56.74, "curve": 0.328, "c2": 0.32, "c3": 0.688, "c4": 0.73}, {"time": 0.8667, "angle": -8.66}], "translate": [{"x": 6.8, "y": 0.73, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 0.1, "x": 0.77, "y": 0.09, "curve": "stepped"}, {"time": 0.4667, "x": 0.77, "y": 0.09, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -2.27, "y": -5.21, "curve": "stepped"}, {"time": 0.6667, "x": -2.27, "y": -5.21, "curve": 0.328, "c2": 0.32, "c3": 0.688, "c4": 0.73}, {"time": 0.8667, "x": 6.8, "y": 0.73}]}, "bone16": {"rotate": [{"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -15.8, "curve": 0.25, "c3": 0.75}, {"time": 0.8667}], "translate": [{"curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.1, "x": -0.01, "y": 2.99, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 0.2, "y": 0.14, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2667}]}, "bone28": {"rotate": [{"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -14.49, "curve": 0.25, "c3": 0.75}, {"time": 0.8667}]}, "bone7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -11.7, "curve": "stepped"}, {"time": 0.4, "angle": -11.7, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 12.07, "curve": 0.25, "c3": 0.75}, {"time": 0.8667}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 31.25, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2333, "angle": 5.77, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -28.02, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}]}, "bone10": {"rotate": [{"angle": -4.43, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2667, "angle": -7.55, "curve": "stepped"}, {"time": 0.4, "angle": -7.55, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 19.58, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.8667, "angle": -4.43}]}, "bone19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 20.71, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667}]}, "bone23": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -15.5, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 9.36, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.8667}]}, "bone": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -24.2, "curve": "stepped"}, {"time": 0.4, "angle": -24.2, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 14.68, "curve": 0.25, "c3": 0.75}, {"time": 0.8667}], "translate": [{"curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.1, "x": 9.74, "y": -9.57, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 0.2, "x": 17.25, "y": 19.45, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2667, "x": 22.7, "y": 3.7, "curve": "stepped"}, {"time": 0.4, "x": 22.7, "y": 3.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5667, "x": 2.52, "y": 48.91, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6, "x": -17.67, "y": 10.28, "curve": 0.25, "c3": 0.75}, {"time": 0.8667}]}, "bone24": {"rotate": [{"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -15.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone29": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -6.7, "curve": "stepped"}, {"time": 0.5, "angle": -6.7, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 17.66, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.8667}]}, "bone32": {"rotate": [{"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 6.44}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -230.59, "y": 3.93, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": -566.97, "y": 3.93}], "scale": [{"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": 1.411, "y": 1.411}]}, "bone33": {"rotate": [{"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 6.44}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -230.59, "y": 3.93}], "scale": [{"time": 0.5333, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.8, "x": 1.345, "y": 1.345, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.8667}]}}, "events": [{"time": 0.7667, "name": "atk"}]}, "boss_attack3": {"slots": {"dilie": {"color": [{"time": 0.7, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "color": "ffffff00"}], "attachment": [{"time": 0.7, "name": "dilie"}]}, "ring": {"color": [{"time": 0.7, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1, "color": "ffffff00"}], "attachment": [{"time": 0.7, "name": "ring"}]}, "dilie1": {"color": [{"time": 0.7667, "color": "ffffffde", "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "color": "ffffff00"}], "attachment": [{"time": 0.7667, "name": "dilie1"}]}, "ring4": {"color": [{"time": 0.7, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1, "color": "ffffff00"}]}, "tt2": {"color": [{"time": 0.7, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.9, "color": "ffffff00"}], "attachment": [{"time": 0.7, "name": "tt2"}]}}, "bones": {"bone": {"rotate": [{"time": 0.2}, {"time": 0.3, "angle": 179.34}, {"time": 0.4, "angle": -20.84}, {"time": 0.5, "angle": 137.91}, {"time": 0.6667, "angle": -177.36, "curve": "stepped"}, {"time": 0.8333, "angle": -177.36, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.9333, "angle": 75.66, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.0333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "y": -14.63, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": 167.66, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "y": 236.32, "curve": "stepped"}, {"time": 0.6667, "y": 236.32, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "y": 62.26, "curve": "stepped"}, {"time": 0.8333, "y": 62.26, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "y": 117.52, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.0333}]}, "bone2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -0.17, "y": -4.96, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone3": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -12.17, "y": 0.13, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -19.08, "y": -9.36, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -37.06, "y": -8.34, "curve": "stepped"}, {"time": 0.9667, "x": -37.06, "y": -8.34, "curve": 0.25, "c3": 0.75}, {"time": 1.0333}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 0.88, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "y": 1.34, "curve": "stepped"}, {"time": 0.7667, "y": 1.34, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}]}, "bone4": {"rotate": [{"angle": -8.66, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 32.06, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 3.14, "curve": "stepped"}, {"time": 0.9667, "angle": 3.14, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -8.66}], "translate": [{"x": 6.8, "y": 0.73, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 0.1333, "x": -8.76, "y": -0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -25.07, "y": -0.56, "curve": "stepped"}, {"time": 0.9667, "x": -25.07, "y": -0.56, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 6.8, "y": 0.73}]}, "bone5": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -12.56, "y": -1.54, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 55.88, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -24.4, "curve": "stepped"}, {"time": 0.9667, "angle": -24.4, "curve": 0.25, "c3": 0.75}, {"time": 1.1667}]}, "bone7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -39.17, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -24.4, "curve": "stepped"}, {"time": 0.9667, "angle": -24.4, "curve": 0.25, "c3": 0.75}, {"time": 1.1667}]}, "bone9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -6.29, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -18.35, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone16": {"rotate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -42.06, "curve": "stepped"}, {"time": 0.9667, "angle": -42.06, "curve": 0.25, "c3": 0.75}, {"time": 1.0333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 0.19, "y": 5.43, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone17": {"rotate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 92.87, "curve": "stepped"}, {"time": 0.9667, "angle": 92.87, "curve": 0.25, "c3": 0.75}, {"time": 1.0333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -8.02, "y": 2.36, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone18": {"rotate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -32.12, "curve": "stepped"}, {"time": 0.9667, "angle": -32.12, "curve": 0.25, "c3": 0.75}, {"time": 1.0333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 0.22, "y": 6.27, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone19": {"rotate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 124.21, "curve": "stepped"}, {"time": 0.9667, "angle": 124.21, "curve": 0.25, "c3": 0.75}, {"time": 1.0333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -6.26, "y": -0.31, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -14.07, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -11.77, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone24": {"rotate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -24.4, "curve": "stepped"}, {"time": 0.9667, "angle": -24.4, "curve": 0.25, "c3": 0.75}, {"time": 1.1667}]}, "bone34": {"translate": [{"time": 0.7, "y": 20.31}], "scale": [{"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 2.125, "y": 1.723}]}, "bone35": {"translate": [{"time": 0.7, "x": 8.59, "y": 30.46}], "scale": [{"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 5.862, "y": 5.862}]}, "bone36": {"translate": [{"time": 0.7, "x": 8.59, "y": 39.83}]}, "bone37": {"translate": [{"time": 0.7, "x": 7.03}]}}, "events": [{"time": 0.7, "name": "atk"}]}, "boss_idle": {"slots": {"han2": {"color": [{"time": 0.2333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "color": "ffffff6d", "curve": "stepped"}, {"time": 1.2333, "color": "ffffff6d", "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "color": "ffffff00"}]}, "111light2": {"attachment": [{"name": null}]}, "111light": {"attachment": [{"name": null}]}, "han": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "color": "ffffff6d", "curve": "stepped"}, {"time": 0.8667, "color": "ffffff6d", "curve": 0.25, "c3": 0.75}, {"time": 1, "color": "ffffff00"}]}}, "bones": {"bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -1.26, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone3": {"rotate": [{"angle": -0.36, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.26, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -0.36}], "translate": [{"x": 0.31, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 4.18, "y": 0.03, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.31}]}, "bone4": {"rotate": [{"angle": -8.66, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 0.9333, "angle": -0.9, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.9333, "angle": -8.84, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 2, "angle": -8.66}], "translate": [{"x": 6.8, "y": 0.73, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 0.9333, "x": 0.77, "y": 0.09, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.9333, "x": 6.94, "y": 0.74, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 2, "x": 6.8, "y": 0.73}]}, "bone5": {"translate": [{"x": 1.41, "y": -2, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.97, "y": -2.8, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 1.41, "y": -2}]}, "bone6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -6.18, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -6.18, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 3.78, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone10": {"rotate": [{"angle": -4.43, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -6.19, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -4.43}]}, "bone11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -6.19, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone12": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 12.76, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone13": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 12.76, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone14": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 12.76, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 12.76, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -15.47, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -15.47, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone31": {"translate": [{"x": 0.71, "y": 0.74, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.99, "y": 1.03, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 0.71, "y": 0.74}]}, "bone38": {"translate": [{"x": 22.94, "y": 1.65, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -19.72, "y": -0.6}]}, "bone39": {"translate": [{"x": 22.94, "y": 1.65, "curve": "stepped"}, {"time": 0.2333, "x": 22.94, "y": 1.65, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "x": -19.72, "y": -0.6}]}}}, "die": {"bones": {"bone": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -89.97}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 50.4, "y": 120.76, "curve": "stepped"}, {"time": 0.1667, "x": 50.4, "y": 120.76, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 50.4, "y": -34.43, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 50.4, "y": -28.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 50.4, "y": -34.43}]}, "bone2": {"translate": [{"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -6.21, "y": 0.22}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 15.29}]}, "bone4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 14.31, "curve": "stepped"}, {"time": 0.2667, "angle": 14.31, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3, "angle": 6.13, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3667, "angle": -14.51}], "translate": [{"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -2.24, "y": -11.12}]}, "bone6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -54.87, "curve": "stepped"}, {"time": 0.3333, "angle": -54.87, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "bone7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -10.64, "curve": "stepped"}, {"time": 0.3333, "angle": -10.64, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "bone9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -38.26, "curve": "stepped"}, {"time": 0.3333, "angle": -38.26, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}], "translate": [{"time": 0.3, "curve": 0.382, "c2": 0.58, "c3": 0.732}, {"time": 0.4333, "x": 1.64, "y": -24.12}]}, "bone10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 28.48, "curve": "stepped"}, {"time": 0.3333, "angle": 28.48, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -48.05}]}, "bone11": {"rotate": [{"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -53.88}]}, "bone12": {"translate": [{"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 7.82, "y": -0.28}]}, "bone13": {"translate": [{"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 8.09, "y": -0.29}]}, "bone14": {"translate": [{"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 9.27, "y": -0.33}]}, "bone16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -42.48, "curve": "stepped"}, {"time": 0.3, "angle": -42.48, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -6.46, "y": 0.23}]}, "bone18": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -42.48, "curve": "stepped"}, {"time": 0.3, "angle": -42.48, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -6.46, "y": 0.23, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 25.93, "y": -0.93}]}, "bone20": {"rotate": [{"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 47.66}]}, "bone27": {"translate": [{"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 1.43, "y": -21.09}]}}, "deform": {"default": {"b1": {"b1": [{"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "vertices": [-0.19909, -17.54889, -3.47037, -17.20337, 1.18901, -17.50966, -0.19909, -17.54889, -3.47037, -17.20337, 1.18901, -17.50966, -0.19909, -17.54889, -3.47037, -17.20337, 1.18901, -17.50966, 0, 0, 0, 0, -3.47037, -17.20337, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.19909, -17.54889, -3.47037, -17.20337, -0.19909, -17.54889, -3.47037, -17.20337, -0.19909, -17.54889, -3.47037, -17.20337, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.19909, -17.54889, -3.47037, -17.20337, -0.19909, -17.54889, -3.47037, -17.20337, -0.19909, -17.54889, -3.47037, -17.20337, -0.19909, -17.54889, -3.47037, -17.20337]}]}, "k1": {"k1": [{"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "vertices": [16.45946, -0.59063, 16.45946, -0.59063, 16.45946, -0.59063, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16.45946, -0.59063, 16.45946, -0.59063, 16.45946, -0.59063, 16.45946, -0.59063, 16.45946, -0.59063]}]}, "ss1": {"ss1": [{"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "vertices": [7.55514, -0.27113, 7.55514, -0.27113, 7.55514, -0.27113, 7.55514, -0.27113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7.55514, -0.27113, 7.55514, -0.27113, 7.55514, -0.27113]}]}}}, "drawOrder": [{"offsets": [{"slot": "y1", "offset": 1}]}]}, "hurt": {"bones": {"bone4": {"rotate": [{"angle": 6.84, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": 9.07, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 1.03, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": 6.84}]}, "bone2": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -18.17, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone3": {"rotate": [{"angle": -3.74, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -10.17, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": -3.74}]}, "bone6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 24.39, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone7": {"rotate": [{"angle": 10.69, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 29.05, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 10.69}]}, "bone9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 12.98, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone10": {"rotate": [{"angle": 4.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 12.98, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 4.77}]}, "bone11": {"rotate": [{"angle": -19.52, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -22.44, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": -19.52}]}, "bone12": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 27.11, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone13": {"rotate": [{"angle": 9.97, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 27.11, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 9.97}]}, "bone14": {"rotate": [{"angle": 9.97, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 27.11, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 9.97}]}, "bone15": {"rotate": [{"angle": 23.58, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 27.11, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": 23.58}]}, "bone20": {"rotate": [{"angle": -13.85, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 32.53, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -13.85}]}, "bone21": {"rotate": [{"angle": -13.85, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 32.53, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -13.85}]}, "bone22": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 6.46, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone23": {"rotate": [{"angle": 2.38, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 6.46, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 2.38}]}, "bone24": {"rotate": [{"angle": 5.62, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 6.46, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": 5.62}]}, "bone28": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 3.37, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone29": {"rotate": [{"angle": 1.24, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 3.37, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 1.24}]}, "bone30": {"rotate": [{"angle": 2.93, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 3.37, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": 2.93}]}}}}}