import { _decorator, v3 } from "cc";
import GameObject from "../../../lib/object/GameObject";
import { ActionEffect, BuffDetail, CallSkillDetail, HurtDetail, STUN_TYPE_ENUM } from "../FightDefine";
import buffSection from "../../../lib/object/buffSection";
import { GORole } from "../role/GORole";
import { BuffSpecialManager } from "../manager/BuffSpecialManager";
import FightManager from "../manager/FightManager";
import { JsonMgr } from "../../mgr/JsonMgr";
import DirectSection from "../../../lib/object/DirectSection";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
import StateSection, { STATE } from "../section/StateSection";

const { ccclass, property } = _decorator;

@ccclass("GOSkill")
export default abstract class GOSkill extends GameObject {
  protected _detail: CallSkillDetail = null;
  protected _effectAudioTime1;
  protected _effectAudioTime2;

  protected onInit(detail: CallSkillDetail) {
    super.onInit(detail);
  }

  protected onInitDetail(detail: CallSkillDetail) {
    if (FightManager.instance.fightOver == true) {
      this.remove();
      return;
    }
    detail.src.onMsg("skill", this.doBullet.bind(this));
    this.onEnter(detail);
  }

  protected async onEnter(detail: CallSkillDetail) {
    this._detail = new CallSkillDetail();
    this._detail.actionType = detail.actionType;
    this._detail.resolveBack = detail.resolveBack;
    this._detail.movementInfo = detail.movementInfo;
    this._detail.skillId = detail.skillId;
    this._detail.target = detail.target;
    this._detail.src = detail.src;
    let skilldb = JsonMgr.instance.jsonList.c_skillShow[detail.skillId];

    this._effectAudioTime1 = skilldb["effectAudioTime1"];
    this._effectAudioTime2 = skilldb["effectAudioTime2"];
  }
  protected onUpdate(detail: CallSkillDetail, dt) {}
  protected onExit(detail: CallSkillDetail) {}
  protected doBullet() {
    this._detail.src.offMsg("skill", this.doBullet.bind(this));
  }

  protected onRemove(): void {
    this._detail.src.offMsg("skill", this.doBullet.bind(this));
  }

  protected updateSelf(dt: any): void {
    let section = this._detail.src.getSection(StateSection);

    if (section) {
      let id = section.getFSMState();

      if (id == STATE.ATK1 || id == STATE.ATK2_1 || id == STATE.ATK2_2 || id == STATE.ATK3) {
        if (this._effectAudioTime1 >= 0) {
          this._effectAudioTime1 -= dt;
          if (this._effectAudioTime1 <= 0) {
            this.playEffect1();
          }
        }

        if (this._effectAudioTime2 >= 0) {
          this._effectAudioTime2 -= dt;
          if (this._effectAudioTime2 <= 0) {
            this.playEffect2();
          }
        }
      }
    }
  }

  private playEffect1() {
    let skilldb = JsonMgr.instance.jsonList.c_skillShow[this._detail.skillId];
    let effectAudio1 = skilldb["effectAudio1"];
    if (effectAudio1 <= 0) {
      return;
    }
    AudioMgr.instance.playEffect(effectAudio1);
  }

  private playEffect2() {
    let skilldb = JsonMgr.instance.jsonList.c_skillShow[this._detail.skillId];
    let effectAudio2 = skilldb["effectAudio2"];
    if (effectAudio2 <= 0) {
      return;
    }
    AudioMgr.instance.playEffect(effectAudio2);
  }
}
