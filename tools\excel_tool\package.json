{"name": "excel-to-json-converter-js", "version": "1.0.0", "description": "Excel转JSON工具 - JavaScript简化版本", "main": "excel-converter.js", "scripts": {"test": "node example.js", "convert": "node excel-converter.js", "start": "node start.js", "quick-start": "node quick-start.js", "read-row4": "node read-row4.js", "row-example": "node row-reader-example.js"}, "keywords": ["excel", "json", "converter", "xlsx", "xls", "data", "format", "javascript"], "author": "FM Game Team", "license": "MIT", "dependencies": {"xlsx": "^0.18.5"}, "engines": {"node": ">=12.0.0"}}