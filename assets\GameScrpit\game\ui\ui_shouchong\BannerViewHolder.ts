import { _decorator, instantiate, Label, Prefab, Node, sp } from "cc";
import { ViewHolder } from "../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { BannerLayoutManager } from "db://assets/platform/src/core/ui/adapter_view/layout_manager/BannerLayoutManager";
const { ccclass, property } = _decorator;

@ccclass("BannerViewHolder")
export class BannerViewHolder extends ViewHolder {
  @property(Label)
  lblPage: Label;
  @property(Node)
  heroImage: Node;
  updateView(data: any, position: number, layoutManager: BannerLayoutManager) {
    this.lblPage.string = `${position}`;
    this.heroImage.removeAllChildren();
    this.assetMgr.loadPrefab(BundleEnum.BUNDLE_COMMON_HERO_FULL, `prefabs/hero_${data}`, (prefab: Prefab) => {
      let node = instantiate(prefab);
      node.walk((child) => {
        child.layer = 8;
      });
      this.heroImage.addChild(node);
      let spine = node.getComponentInChildren(sp.Skeleton);
      if (spine && spine._skeleton) {
        spine.setAnimation(0, "animation1", true);
        if (position == layoutManager.foucsPosition) {
          spine.paused = false;
        } else {
          spine.paused = true;
        }
      }
    });
  }
}
