import { Prefab } from "cc";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>s<PERSON><PERSON><PERSON> } from "../../../platform/src/ResHelper";
import { JsonMgr } from "../../game/mgr/JsonMgr";
import { CityModule } from "../city/CityModule";
import { TaskType } from "./MainTaskData";
import { MainTaskModule } from "./MainTaskModule";
import { instantiate } from "cc";
import { TipsMgr } from "../../../platform/src/TipsHelper";

export class MainTaskService {
  // 已显示过的tipsId
  showNoticeId = -1;

  // 返回主线任务需要的次数
  public getNeedCount(): number {
    let configTaskMain = MainTaskModule.data.getConfigTaskMain(MainTaskModule.data.mainTaskMsg.taskTimelineId);

    if ([TaskType.关卡通关_2, TaskType.建造x_5, TaskType.建造地图大装饰_63].includes(configTaskMain.taskId)) {
      return 1;
    }

    if ([TaskType.升级x_6, TaskType.建筑招募_8, TaskType.三界小家_66].includes(configTaskMain.taskId)) {
      return configTaskMain.finishList[1];
    }

    return configTaskMain.finishList[0];
  }

  public getCompleteNum(): number {
    let configTaskMain = MainTaskModule.data.getConfigTaskMain(MainTaskModule.data.mainTaskMsg.taskTimelineId);
    if (TaskType.关卡通关_2 == configTaskMain.taskId) {
      if (MainTaskModule.data.mainTaskMsg.completeNum >= configTaskMain.finishList[0]) {
        return 1;
      } else {
        return 0;
      }
    }
    return MainTaskModule.data.mainTaskMsg.completeNum;
  }

  // 格式任务描述
  public getTaskDesc(showComplete = true): string {
    let configTaskMain = MainTaskModule.data.getConfigTaskMain(MainTaskModule.data.mainTaskMsg.taskTimelineId);
    let configTask = MainTaskModule.data.getConfigTask(configTaskMain.taskId);

    let needCount = this.getNeedCount() + "";
    let complateNum = this.getCompleteNum();

    let rs = "";
    if (configTaskMain.taskId == TaskType.关卡通关_2) {
      let configFight = JsonMgr.instance.jsonList.c_copyMain[configTaskMain.finishList[0]];
      rs = configTask.des.replace("s%", configFight.des);
    } else if (configTaskMain.taskId == TaskType.战将升级_4) {
      rs = configTask.des
        .replace("s%", configTaskMain.finishList[0] + "")
        .replace("s%", configTaskMain.finishList[1] + "");
    } else if (configTaskMain.taskId == TaskType.建造x_5) {
      let configCity = CityModule.data.getConfigBuild(configTaskMain.finishList[0]);
      rs = configTask.des.replace("s%", configCity.name);
    } else if (configTaskMain.taskId == TaskType.升级x_6 || configTaskMain.taskId == TaskType.建筑招募_8) {
      let configCity = CityModule.data.getConfigBuild(configTaskMain.finishList[0]);
      rs = configTask.des.replace("s%", configCity.name).replace("s%", needCount);
    } else if (configTaskMain.taskId == TaskType.建造地图大装饰_63) {
      let cfg = JsonMgr.instance.jsonList.c_buildTrimReward[configTaskMain.finishList[0]];
      rs = configTask.des.replace("s%", cfg.name);
    } else if (configTaskMain.taskId == TaskType.三界小家_66) {
      let cfg = JsonMgr.instance.jsonList.c_home[configTaskMain.finishList[0]];
      rs = configTask.des.replace("s%", cfg.name).replace("s%", needCount);
    } else {
      rs = configTask.des.replace("s%", needCount).replace("s%", needCount);
    }

    if (showComplete) {
      if (Number(complateNum) > Number(needCount)) {
        complateNum = Number(needCount);
      }

      rs = rs + `\n(${complateNum}/${needCount})`;
    }

    return rs;
  }

  public getTaskTitle() {
    let configTaskMain = MainTaskModule.data.getConfigTaskMain(MainTaskModule.data.mainTaskMsg.taskTimelineId);
    let configTask = MainTaskModule.data.getConfigTask(configTaskMain.taskId);
    let str = configTask.title;

    let needCount = this.getNeedCount() + "";
    if (configTaskMain.taskId == TaskType.建造x_5) {
      let configCity = CityModule.data.getConfigBuild(configTaskMain.finishList[0]);
      str = configTask.title.replace("s%", configCity.name);
    } else if (configTaskMain.taskId == TaskType.升级x_6) {
      let configCity = CityModule.data.getConfigBuild(configTaskMain.finishList[0]);
      str = configTask.des.replace("s%", configCity.name).replace("s%", needCount);
    } else if (configTaskMain.taskId == TaskType.建造地图大装饰_63) {
      let cfg = JsonMgr.instance.jsonList.c_buildTrimReward[configTaskMain.finishList[0]];
      str = configTask.title.replace("s%", cfg.name);
    }

    return str;
  }

  // 显示tips弹窗
  public showTipsComplete() {
    if (this.showNoticeId == MainTaskModule.data.mainTaskMsg.taskTimelineId) {
      return;
    }

    if (this.getNeedCount() <= this.getCompleteNum()) {
      this.showNoticeId = MainTaskModule.data.mainTaskMsg.taskTimelineId;
      ResHelper.preLoadResSync(BundleEnum.BUNDLE_G_MAINTASK, "prefab/tip_task_finish").then((pb: Prefab) => {
        let nodeTip = instantiate(pb);
        TipsMgr.showTipNode(nodeTip, 3);
      });
    }
  }
}
