import {
  Asset,
  AssetManager,
  Label,
  Material,
  Sprite,
  SpriteAtlas,
  SpriteFrame,
  assetManager,
  isValid,
  Node,
  sp,
  Prefab,
  instantiate,
  RichText,
  TTFFont,
  tween,
  color,
  math,
  Color,
} from "cc";
import { UINode } from "../ui/UINode";
import BundleUtils from "../utils/BundleUtils";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { LRUAssetManager } from "./LRUAssetManager";
// import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
// const log = Logger.getLoger(LOG_LEVEL.WARN);
const log = Logger.getLoger(LOG_LEVEL.WARN);
type RState = { count: number; complete: boolean; on: Function[] };
export default class ResMgr {
  /**
   * 加载预制体
   * @param url
   * @param callback
   */
  public static async loadPrefab(url: string, callback: Function, self: UINode = null) {
    let pathList = url.split("?");
    let bundle = (await this.getBundleSync(pathList[0])) as AssetManager.Bundle;
    bundle.load(pathList[1], (err, prefab) => {
      if (err) {
        log.error(err);
        return;
      }

      if (self) {
        prefab.addRef();
        self.addDeps(prefab.uuid);
      }

      if (callback != null) {
        callback(prefab);
      }
    });
  }

  /**
   * 加载预制体
   * @param url
   * @param callback
   */
  public static async loadPrefabSync(url: string, self: UINode = null): Promise<Prefab> {
    let pathList = url.split("?");
    let bundle = await this.getBundleSync(pathList[0]);

    return new Promise((resolve, reject) => {
      bundle.load<Prefab>(pathList[1], (err, prefab) => {
        if (err) {
          log.error(err);
          reject(err);
          return;
        }

        if (self) {
          prefab.addRef();
          self.addDeps(prefab.uuid);
        }
        resolve(prefab);
      });
    });
  }

  public static async getBundleSync(bundleName: string): Promise<AssetManager.Bundle> {
    let bundle: AssetManager.Bundle = assetManager.getBundle(bundleName);
    if (bundle) {
      return bundle;
    }

    return new Promise((resolve, reject) => {
      assetManager.loadBundle(bundleName, (err: Error, bundle: AssetManager.Bundle) => {
        if (err) {
          log.error("asset bund 404 ", err);
          reject(err);
          return;
        }
        resolve(bundle);
      });
    });
  }

  /**下载分包 */
  public static loadBundle(name, callback: Function = null) {
    assetManager.loadBundle(name, function (err, bundle) {
      if (err) {
        log.error("分包资源下载失败，分包名---", name);
        log.error(err);
        return;
      }
      // log.log(bundle.config.name, "分包下载完成");
      callback && callback();
    });
  }

  /**
   * 加载spine
   * @param url
   * @param skt
   * @param callback
   */
  public static async loadSpine(url: string, skt: sp.Skeleton, self: UINode = null, callback?: Function) {
    let pathList = url.split("?");
    let bundle = (await this.getBundleSync(pathList[0])) as AssetManager.Bundle;
    bundle.load(pathList[1], sp.SkeletonData, function (err, spData) {
      if (!err) {
        if (skt != null) {
          if (!isValid(skt.node)) {
            return;
          }

          if (self) {
            spData.addRef();
            // log.log("atlas.uuid===========", atlas.uuid);
            self.addDeps(spData.uuid);
          }
          skt.skeletonData = spData;
        }
        if (callback != null) {
          callback(spData);
        }
      }
    });
  }
  /**
   * 加载图集中的图片
   * @param url
   * @param sub
   * @param sp
   * @param callback
   */
  public static async loadSpriteFrame(url: string, sub: string, sp: Sprite, self: UINode = null, callback?: Function) {
    let pathList = url.split("?");
    let bundle = (await this.getBundleSync(pathList[0])) as AssetManager.Bundle;
    bundle.load(
      pathList[1],
      SpriteAtlas,
      function (err, atlas) {
        if (err || !sp || !sp.isValid) {
          if (err) {
            log.error(err);
          }
          return;
        }
        if (atlas) {
          var sf = atlas.getSpriteFrame(sub);
          if (sf) {
            sp.spriteFrame = sf;

            if (self) {
              atlas.addRef();
              // log.log("atlas.uuid===========", atlas.uuid);
              self.addDeps(atlas.uuid);
            }
            if (callback) {
              callback(sp.spriteFrame);
            }
          }
        }
      }.bind(this)
    );
  }

  /**
   * 加载图片
   * @param url
   * @param sp
   * @param self 传入计算引用计数
   * @param callback
   */
  public static async loadImage(url: string, sp: Sprite, self: any = null, callback?: Function) {
    let pathList = url.split("?");
    let bundle = (await this.getBundleSync(pathList[0])) as AssetManager.Bundle;
    bundle.load(
      pathList[1] + "/spriteFrame",
      SpriteFrame,
      function (err, lsp) {
        if (err || !lsp || !sp || !sp.isValid || !sp.node.isValid) {
          callback && callback(null, err);
          log.warn("sprite 不存在", err);
          return;
        }

        sp.spriteFrame = lsp;

        if (self) {
          lsp.addRef();
          //log.log("lsp.uuid===========", lsp.uuid);
          self.addDeps(lsp.uuid);
        }
        if (callback) {
          callback(sp.spriteFrame);
        }
      }.bind(this)
    );
  }

  /**
   * 加载Texture
   * @param url
   * @param sp
   * @param callback
   */
  public static async loadTexture(url: string, sp: any, callback?: Function) {
    let pathList = url.split("?");
    let bundle = (await this.getBundleSync(pathList[0])) as AssetManager.Bundle;
    bundle.load(
      pathList[1],
      function (err, texture) {
        if (err || !texture || !sp || !sp.isValid) return;
        sp.texture = texture;
        if (callback) {
          callback(sp.texture);
        }
      }.bind(this)
    );
  }

  public static async loadFont(url: string, lab: RichText, callback?: Function) {
    let pathList = url.split("?");
    let bundle = (await this.getBundleSync(pathList[0])) as AssetManager.Bundle;
    bundle.load(pathList[1], (err, font) => {
      if (err || !font || !lab || !lab.isValid) {
        return;
      }
      lab.font = font as TTFFont;
      if (callback) {
        callback(lab.font);
      }
    });
  }

  public static async loadMaterial(url: string, callback?: Function) {
    let pathList = url.split("?");
    let bundle = (await this.getBundleSync(pathList[0])) as AssetManager.Bundle;
    bundle.load(pathList[1], Material, function (err, material) {
      if (!err) {
        if (callback != null) {
          callback(err, material);
        }
      }
    });
  }

  private static states: Map<string, Map<string, RState>> = new Map<string, Map<string, RState>>();

  public static loadFile(bundleName: string, path: string, type = Asset): Promise<Asset> {
    if (!this.existFile(bundleName, path)) return null;
    return new Promise(async (complete: Function) => {
      let state = this.getState(bundleName, path, true);
      if (state.complete) {
        let bundle = (await BundleUtils.getBundleN(bundleName)) as AssetManager.Bundle;
        complete(bundle.get(path, type));
      } else {
        state.on.push(complete);
        if (state.count == 0) {
          let resBundle = (await BundleUtils.getBundleN(bundleName)) as AssetManager.Bundle;
          resBundle.load(path, type, (error: Error, asset: Asset) => {
            if (error) {
              log.log(error.message);
              return;
            }
            state.complete = true;
            if (state.count == 0) {
              state.on.forEach((on: Function) => {
                on(null);
              });
              this._releaseFile(bundleName, path);
            } else {
              state.on.forEach((on: Function) => {
                on(asset);
              });
              state.on = [];
            }
          });
        }
      }
      state.count++;
    });
  }

  public static releaseFile(bundleName: string, path: string): void {
    let state = this.getState(bundleName, path);
    if (state && state.count > 0) {
      if (--state.count == 0 && state.complete) {
        this._releaseFile(bundleName, path);
      }
    }
  }

  public static removeFile(bundle, path) {
    this._releaseFile(bundle, path);
  }

  private static async _releaseFile(bundleName: string, path: string): Promise<void> {
    let state = this.getState(bundleName, path);
    if (state && state.complete) {
      let bundle = (await BundleUtils.getBundleN(bundleName)) as AssetManager.Bundle;
      bundle.release(path);
      this.states.get(bundleName).delete(path);
    }
  }

  private static getState(bundleName: string, key: string, create?: boolean): RState {
    let bundleState = this.states.get(bundleName);
    if (!bundleState) {
      bundleState = new Map<string, RState>();
    }

    let state = bundleState.get(key);
    if (create && !state) {
      state = { count: 0, complete: false, on: [] };
      bundleState.set(key, state);
      this.states.set(bundleName, bundleState);
    }
    return state;
  }

  public static async existFile(bundleName: string, path: string): Promise<boolean> {
    let assetWith = null;
    let dirname = this.dirname(path) || "";
    let basename = this.basename(path) || path;
    let resBundle = (await BundleUtils.getBundleN(bundleName)) as AssetManager.Bundle;

    let dirWith = resBundle.getDirWithPath(dirname);
    for (let i = 0; i < dirWith.length; i++) {
      if (basename == this.basename(dirWith[i].path)) {
        assetWith = dirWith[i];
        break;
      }
    }
    if (!assetWith) {
      return false;
    }
    return true;
  }

  public static basename(path: string, extname?: string): string {
    let index = path.indexOf("?");
    if (index > 0) path = path.substring(0, index);
    let reg = /(\/|\\)([^\/\\]+)$/g;
    let result = reg.exec(path.replace(/(\/|\\)$/, ""));
    if (!result) return path;
    let baseName = result[2];
    if (extname && path.substring(path.length - extname.length).toLowerCase() === extname.toLowerCase())
      return baseName.substring(0, baseName.length - extname.length);
    return baseName;
  }

  public static dirname(path: string): string {
    var temp = /((.*)(\/|\\|\\\\))?(.*?\..*$)?/.exec(path);
    return temp ? temp[2] : "";
  }

  /**
   *
   * @param bundleName bundle名称
   * @param path 资源全路径
   * @param type 类型
   * @param dependence 依赖的bundle列表
   * @param callback 加载完成的回调
   */
  public static async preLoadRes(
    bundleName: string,
    path: string,
    type: typeof Asset,
    dependence: [],
    callback?: Function
  ) {
    // 获取或加载依赖的bundle
    if (dependence && dependence.length > 0) {
      for (let i = 0; i < dependence.length; i++) {
        let bundle = await BundleUtils.getBundleN(dependence[i]);
        if (!bundle) {
          throw new Error(`依赖的bundle不存在:${dependence[i]}`);
        }
      }
    }

    // 获取或加载目标bundle
    let bundle = await BundleUtils.getBundleN(bundleName);
    if (!bundle) {
      throw new Error(`目标bundle不存在:${bundleName}`);
    }

    bundle.preload(path, type, null, (err, data) => {
      if (err) {
        log.error(err);
        return;
      }
      callback && callback(data);
    });
  }

  public static async setSpriteFrame(
    bundleName: string,
    path: string,
    sprite: Sprite,
    callback?: (sf: SpriteFrame, error?: any) => void,
    isAni: boolean = false
  ) {
    if (!path) {
      log.error("path is null");
    }

    LRUAssetManager.instance.getSpriteFrame(bundleName, path, sprite, (sf: SpriteFrame, error?: any) => {
      callback?.(sf, error);
    });

    // // 获取或加载目标bundle
    // let bundle = await BundleUtils.getBundleN(bundleName);
    // if (!bundle) {
    //   throw new Error(`目标bundle不存在:${bundleName}`);
    // }
    // bundle.load(`${path}/spriteFrame`, SpriteFrame, null, (err, data) => {
    //   if (err) {
    //     log.error(err);
    //     return;
    //   }
    //   try {
    //     if (sprite && !isValid(sprite.node)) {
    //       log.warn("sprite.node is null");
    //       return;
    //     }
    //     if (isAni) {
    //       // 过渡动画
    //       let oc = new Color(sprite.color);
    //       let ocColor = new Color(oc.r, oc.g, oc.b, oc.a);
    //       let color = new Color(128, 128, 128, 128);
    //       tween(ocColor)
    //         .to(
    //           0.1,
    //           { a: color.a, r: color.r, g: color.g, b: color.b },
    //           {
    //             onUpdate: (target) => {
    //               isValid(sprite) && (sprite.color = target);
    //             },
    //           }
    //         )
    //         .call(() => {
    //           isValid(sprite) && (sprite.spriteFrame = data);
    //         })
    //         .to(
    //           0.1,
    //           { a: oc.a, r: oc.r, g: oc.g, b: oc.b },
    //           {
    //             onUpdate: (target) => {
    //               isValid(sprite) && (sprite.color = target);
    //             },
    //           }
    //         )
    //         .start();
    //     } else {
    //       sprite.spriteFrame = data;
    //     }

    //     sprite.node.on(Node.EventType.NODE_DESTROYED, () => {
    //       // log.log(`sprite node destroyed ${data.refCount}`);
    //       data.decRef();
    //     });
    //     data.addRef();

    //     // log.log(bundleName, path, sprite);
    //     callback && callback(data);
    //   } catch (error) {
    //     log.warn(error);
    //   }
    // });
  }
  public static async setNodePrefab(bundleName: string, path: string, parent: Node, callback?: Function) {
    let bundle = await BundleUtils.getBundleN(bundleName);
    if (!bundle) {
      throw new Error(`目标bundle不存在:${bundleName}`);
    }
    bundle.load(path, Prefab, null, (err, data) => {
      if (err) {
        log.error(err);
        return;
      }

      let inst = instantiate(data);
      try {
        inst.once(Node.EventType.NODE_DESTROYED, () => {
          // when node destroyed must release the prefab refrence
          data && data.decRef();
        });

        data.addRef();

        if (!isValid(parent)) {
          inst.destroy();
          return;
        }
        if (parent) {
          inst.walk((child) => {
            child.layer = parent.layer;
          });
        }
        if (callback && callback(inst)) {
          // do nothing
          return;
        }
        parent.addChild(inst);
      } catch (error) {
        // 当节点被销毁时，可能会发生异常
        log.error(error);
        inst.destroy();
      }
    });
  }
}
