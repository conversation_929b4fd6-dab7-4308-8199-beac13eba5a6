import { _decorator, Component, Node } from "cc";
import { BadgeMgr, BadgeType } from "../../game/mgr/BadgeMgr";
import { PostModule } from "./postModule";
import TickerMgr from "../../lib/ticker/TickerMgr";
import { JsonMgr } from "../../game/mgr/JsonMgr";
const { ccclass, property } = _decorator;

export class PostService {
  private _tickId: number = null;

  public init() {
    // 触发红点更新的事件
    if (this._tickId) {
      TickerMgr.clearInterval(this._tickId);
    }
    this._tickId = TickerMgr.setInterval(5, this.updatePopover.bind(this), true);
  }

  public schedule() {}

  public updatePopover() {
    // todo 是否有奖励
    let hasReward = false;
    if (PostModule.data.rewardList.length > 0) {
      hasReward = true;
    }
    BadgeMgr.instance.setShowById(BadgeType.btn_tiao_zhan.btn_post.btn_post_award_get.id, hasReward);

    // todo 每日首次货运开启前有红点提示
    let hasFirstPost = false;
    if (PostModule.data.dailyCount <= 0) {
      hasFirstPost = true;
    }
    BadgeMgr.instance.setShowById(BadgeType.btn_tiao_zhan.btn_post.btn_go_post.id, hasFirstPost);

    const c_post = JsonMgr.instance.jsonList.c_post;
    const list = Object.keys(c_post);
    const maxLevelIs = PostModule.data.message.level >= list.length;
    if (!maxLevelIs && PostModule.data.message.historyCount >= c_post[PostModule.data.message.level + 1].need) {
      BadgeMgr.instance.setShowById(BadgeType.btn_tiao_zhan.btn_post.btn_lv_add.id, true);
    } else {
      BadgeMgr.instance.setShowById(BadgeType.btn_tiao_zhan.btn_post.btn_lv_add.id, false);
    }
  }
}
