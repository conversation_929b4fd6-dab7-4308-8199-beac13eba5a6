import { _decorator, Label, Layout, Node, RichText, UITransform, Widget } from "cc";
import { ViewHolder } from "../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { ChatPackMessage } from "../../net/protocol/Chat";
import { TimeUtils } from "../../../lib/utils/TimeUtils";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { MarketType } from "../../../module/chat/ChatConstant";
import { ClubModule } from "../../../module/club/ClubModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "../../../lib/utils/FmUtils";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;
@ccclass("MineViewHolder")
export class MineViewHolder extends ViewHolder {
  @property(Label)
  private lblTime: Label;
  @property(Label)
  private lblContent: Label;
  @property(Label)
  private lblName: Label;
  @property(RichText)
  private richPlayerTitle: RichText;
  @property(Node)
  private nodePlayerTitle: Node;
  @property(Label)
  private lblContentSigle: Label;
  @property(Node)
  private avatar: Node;
  @property(Node)
  private clubPosition: Node;
  public updateData(lastData: ChatPackMessage, data: ChatPackMessage, nextData: ChatPackMessage, marketType: number) {
    // log.log("MineViewHolder", data);
    // this.lblTime.string = data.time;
    const diffMs = Date.now() - (lastData?.createTime ?? Date.now());
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    if (diffDays >= 3) {
      this.lblTime.node.parent.active = false;
    } else {
      this.lblTime.node.parent.active = true;
      this.lblTime.string = TimeUtils.getTimeAgo(data.createTime);
    }
    this.lblName.string = data.sender.nickname;
    this.lblContentSigle.string = data.content;
    this.lblContentSigle.updateRenderData(true);
    FmUtils.setHeaderNode(this.avatar, data.sender);
    if (this.lblContentSigle.getComponent(UITransform).width > 360) {
      this.lblContentSigle.node.active = false;
      this.lblContentSigle.destroyRenderData();
      this.lblContent.string = data.content;
      this.lblContent.node.active = true;
      this.lblContent.updateRenderData(true);
      this.lblContent.node.parent.getComponent(UITransform).width = 369 + 20;
      this.lblContent.node.parent.getComponent(UITransform).height = this.lblContent.getComponent(UITransform).height;
      this.lblContent.getComponent(Widget).updateAlignment();
    } else {
      this.lblContentSigle.node.active = true;
      this.lblContent.node.active = false;
      let width = this.lblContentSigle.getComponent(UITransform).width;
      this.lblContentSigle.node.parent.getComponent(UITransform).width = width + 30;
      this.lblContentSigle.node.parent.getComponent(UITransform).height =
        this.lblContentSigle.getComponent(UITransform).height;
      this.lblContentSigle.getComponent(Widget).updateAlignment();
    }
    if (marketType == MarketType.CLUB && ClubModule.data.clubMessage) {
      this.clubPosition.active = true;
      if (ClubModule.data.position == 1) {
        this.clubPosition.getChildByName("lbl_position1").active = true;
        this.clubPosition.getChildByName("lbl_position2").active = false;
      } else if (ClubModule.data.position == 2) {
        this.clubPosition.getChildByName("lbl_position1").active = false;
        this.clubPosition.getChildByName("lbl_position2").active = true;
      } else {
        this.clubPosition.active = false;
      }
    } else {
      this.clubPosition.active = false;
    }
    let configLeader = PlayerModule.data.getConfigLeaderData(data.sender.level);
    try {
      this.nodePlayerTitle.destroyAllChildren();
      if (data.sender.avatarList[3] != -1) {
        PlayerModule.service.createTitle(this.nodePlayerTitle, data.sender.avatarList[3], (titleNode: Node) => {
          this.richPlayerTitle.node.active = false;
        });
      } else {
        this.richPlayerTitle.node.active = true;
        this.richPlayerTitle.string = `${configLeader.jingjie2}`;
      }
    } catch (error) {}
    this.node.getComponent(Layout).updateLayout(true);
  }
}
