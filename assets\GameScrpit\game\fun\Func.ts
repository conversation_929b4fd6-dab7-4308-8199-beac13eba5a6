import { Node, _decorator } from "cc";
import MsgMgr from "../../lib/event/MsgMgr";
import { JsonMgr } from "../mgr/JsonMgr";
import ToolExt from "../common/ToolExt";
import { GameDirector } from "../GameDirector";

const { ccclass, property } = _decorator;

@ccclass("Func")
export default abstract class Func {
  protected _id: number;
  protected _openMap: Map<number, number> = new Map<number, number>();

  public get openMap() {
    return this._openMap;
  }

  public abstract uiName();
  public abstract funcName();
  public abstract showUI(args?);

  /**注册打开界面事件后回调 */
  private uiOnOpen(callback?) {
    MsgMgr.once("ON_" + this.uiName() + "_SHOW", callback || function () {}.bind(this), this);
  }

  public tick(dt) {}

  public do(id, callback?, args?) {
    if (!GameDirector.instance.isSystemOpen(id)) {
      return;
    }
    this.uiOnOpen((node) => {
      if (callback) {
        callback();
      }
      let c_jump: any = JsonMgr.instance.jsonList.c_jump;
      let jumpDb = c_jump[id];
      if (jumpDb.callback != "") {
        node[jumpDb.callback]();
      }
    });
    //ToolExt.JumpToExit(this.uiName());
    this.showUI(args);
  }
}
