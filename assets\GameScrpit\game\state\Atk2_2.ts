import { _decorator, isValid } from "cc";
import { FSMState } from "../fight/FSM/FSMState";
import { FSMBoard, STATE } from "../fight/section/StateSection";
import { AnimationSection } from "../fight/section/AnimationSection";
import { CallSkillDetail } from "../fight/FightDefine";
import FightManager, { scaleList } from "../fight/manager/FightManager";
import SkillManager from "../fight/manager/SkillManager";
import TickerMgr from "../../lib/ticker/TickerMgr";
import { PlayerModule } from "../../module/player/PlayerModule";
interface TimeHandle {
  time: number;
  handle: Function;
  isHandleOver: boolean;
  skillId: number;
}
const { ccclass, property } = _decorator;

@ccclass
export default class Atk2_2 extends FSMState {
  private timeHandle: Array<TimeHandle> = [];
  public async onEnter(board: FSMBoard) {
    let time = board.sub.getSection(AnimationSection).playAction(4, false);
  }

  private playSound(board: FSMBoard) {}

  // private onSkillOver(board: FSMBoard) {
  //   board.sub.emitMsg("OnSwitchState", STATE.IDLE);
  //   board.actOverCallback();
  // }

  public update(board: FSMBoard, dt) {
    this.updateHandle(dt);
  }

  public onExit(board: any): void {}

  public updateHandle(dt) {}

  private getSkillObj(roleId: number) {}
}
