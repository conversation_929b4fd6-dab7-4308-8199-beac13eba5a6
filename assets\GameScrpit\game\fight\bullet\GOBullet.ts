import { _decorator, v2 } from "cc";
import GameObject from "../../../lib/object/GameObject";
import { ActionEffect, ActionType, BuffDetail, CallBulletDetail, HurtDetail, RecoverDetail } from "../FightDefine";
import { JsonMgr } from "../../mgr/JsonMgr";
import FightManager from "../manager/FightManager";
import { GORole } from "../role/GORole";
import BulletManager from "../manager/BulletManager";
import { TipManager } from "../manager/TipManager";
import { STATE } from "../section/StateSection";
import HPSection from "../../../lib/object/HpSection";
import { AudioMgr, AudioName } from "../../../../platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.STOP);
const { ccclass, property } = _decorator;
@ccclass("GOBullet")
export class GOBullet extends GameObject {
  private _detail: CallBulletDetail;
  public get detail() {
    return this._detail;
  }

  private _src: GORole;
  public get src() {
    return this._src;
  }
  private _target: GORole;
  public get target() {
    return this._target;
  }

  private _movementInfo: { [key: number]: ActionEffect };
  public get movementInfo() {
    return this._movementInfo;
  }

  private _resolveBack: Function;
  public get resolveBack() {
    return this._resolveBack;
  }

  private _bulletId: number;
  public get bulletId() {
    return this._bulletId;
  }

  private _actionType: ActionType;
  public get actionType() {
    return this._actionType;
  }

  public async onInitDetail(detail: CallBulletDetail) {
    if (FightManager.instance.fightOver == true) {
      this.remove();
      return;
    }

    //log.log("初始化子弹=====", detail);
    this.onInit();
    this.onEnter();
    this._detail = detail;

    this._src = detail.src;
    this._target = detail.target;
    this._bulletId = detail.bulletId;
    this._actionType = detail.actionType;
    this._movementInfo = detail.movementInfo;
    this._resolveBack = detail.resolveBack;

    this.setPosVec3(v2(detail.src.getPosVec2().x, detail.src.getPosVec2().y));
  }

  public onEnter(): void {
    this.onMsg("OnAnimationStart", this.AnimationStart.bind(this));
    this.onMsg("OnAnimationEvent", this.PlayAnimation.bind(this));
    this.onMsg("OnAnimationCompleted", this.AnimationCompleted.bind(this));
  }

  public onExit() {
    this.offMsg("OnAnimationStart", this.AnimationStart.bind(this));
    this.offMsg("OnAnimationEvent", this.PlayAnimation.bind(this));
    this.offMsg("OnAnimationCompleted", this.AnimationCompleted.bind(this));
  }

  protected async AnimationStart(detail) {
    log.log("动画开始播放", detail);
  }
  protected async PlayAnimation(event) {
    //log.log("动画播放事件", event);
  }
  protected async AnimationCompleted(data) {
    // log.log("动画播放结束", data);
  }

  public updateSelf(dt) {}

  /**返回位置索引 */
  public getDir() {
    return this._src.getDir();
  }

  protected hurtRoleMsg() {
    switch (this.actionType) {
      case ActionType.NORMAL_ATTACK:
        AudioMgr.instance.playEffect(AudioName.Effect.受击);
        this.normaLhurt();
        break;
      case ActionType.COMBO:
        AudioMgr.instance.playEffect(AudioName.Effect.受击);
        this.comboHurt();
        break;
      case ActionType.COUNTER:
        AudioMgr.instance.playEffect(AudioName.Effect.受击);
        this.counterHurt();
        break;
      case ActionType.CRITICAL:
        AudioMgr.instance.playEffect(511);
        this.criticalHurt();
        break;
      case ActionType.DOOGE:
        this.doogeHurt();
        break;

      case ActionType.FORBIDON:
        break;
      default:
        break;
    }

    let hurtInfo = this.movementInfo[this.target.getDir()];
    if (!hurtInfo) return;
    let stun = hurtInfo.c || 0;
    if (stun) {
      let param: BuffDetail = {
        buffId: JsonMgr.instance.jsonList.c_spineShow[this.src.getSpineId()].stunBuffId,
        attachment: this.target,
        roundCount: stun,
      };
      this.target.emitMsg("OnBuff", param);
    }
  }

  /**普通伤害 */
  private normaLhurt() {
    let hurtInfo = this.movementInfo[this.target.getDir()];
    if (!hurtInfo) return;
    let hurt = hurtInfo.a || 0;
    if (hurt > 0) {
      this.target.emitMsg("OnSwitchState", STATE.HURT);
      let hurtDetail = new HurtDetail();
      hurtDetail.hurtRole = this.target;
      hurtDetail.hurt = hurt;
      hurtDetail.stun = hurtInfo.c || 0;
      hurtDetail.isCrit = false;
      hurtDetail.isDouble = false;
      hurtDetail.isDodge = false;
      hurtDetail.backpunch = false;
      this.target.emitMsg("OnRoleHurt", hurtDetail);
    }
  }

  /**连击 */
  private comboHurt() {
    let hurtInfo = this.movementInfo[this.target.getDir()];
    if (!hurtInfo) return;
    let hurt = hurtInfo.a || 0;
    if (hurt > 0) {
      this.target.emitMsg("OnSwitchState", STATE.HURT);
      let hurtDetail = new HurtDetail();
      hurtDetail.hurtRole = this.target;
      hurtDetail.hurt = hurt;
      hurtDetail.stun = hurtInfo.c || 0;
      hurtDetail.isCrit = false;
      hurtDetail.isDouble = true;
      hurtDetail.isDodge = false;
      hurtDetail.backpunch = false;
      this.target.emitMsg("OnRoleHurt", hurtDetail);
      // let path = "resources?prefab/effectLab/hint_lab_double";
      // FightManager.instance.getSection(TipManager).callTip(path, this.src);
    }
  }

  /**反击 */
  private counterHurt() {
    let hurtInfo = this.movementInfo[this.target.getDir()];
    if (!hurtInfo) return;
    let hurt = hurtInfo.a || 0;
    if (hurt > 0) {
      this.target.emitMsg("OnSwitchState", STATE.HURT);
      let hurtDetail = new HurtDetail();
      hurtDetail.hurtRole = this.target;
      hurtDetail.hurt = hurt;
      hurtDetail.stun = hurtInfo.c || 0;
      hurtDetail.isCrit = false;
      hurtDetail.isDouble = false;
      hurtDetail.isDodge = false;
      hurtDetail.backpunch = false;
      this.target.emitMsg("OnRoleHurt", hurtDetail);
      // let path = "resources?prefab/effectLab/hint_lab_backpunch";
      // FightManager.instance.getSection(TipManager).callTip(path, this.src);
    }
  }

  /**暴击 -- 暴击文本在伤害飘字里*/
  private criticalHurt() {
    let hurtInfo = this.movementInfo[this.target.getDir()];
    if (!hurtInfo) return;
    let hurt = hurtInfo.a || 0;
    if (hurt > 0) {
      this.target.emitMsg("OnSwitchState", STATE.HURT);
      let hurtDetail = new HurtDetail();
      hurtDetail.hurtRole = this.target;
      hurtDetail.hurt = hurt;
      hurtDetail.stun = hurtInfo.c || 0;
      hurtDetail.isCrit = true;
      hurtDetail.isDouble = false;
      hurtDetail.isDodge = false;
      hurtDetail.backpunch = false;
      this.target.emitMsg("OnRoleHurt", hurtDetail);
    }
  }

  /**闪避(未命中) -- */
  private doogeHurt() {
    let hurtInfo = this.movementInfo[this.target.getDir()];
    if (!hurtInfo) return;
    let hurt = hurtInfo.a || 0;
    if (hurt <= 0) {
      this.target.dodge();
      let path = "resources?prefab/effectLab/hint_lab_miss";
      FightManager.instance.getSection(TipManager).callTip(path, this.target);
    }
  }

  protected atkRoleMsg() {
    this.normalAtk();
    this.src.getSection(HPSection).setRenderActive(true);
    // switch (this.actionType) {
    //   case ActionType.NORMAL_ATTACK:
    //     this.normalAtk();
    //     break;
    //   case ActionType.COMBO:
    //     this.normalAtk();
    //     break;
    //   case ActionType.COUNTER:
    //     this.normalAtk();
    //     break;
    //   case ActionType.FORBIDON:
    //     break;
    // }
  }

  /**攻击方普通攻击 --- 吸血 -- 每一种攻击都是会有吸血的 */
  private normalAtk() {
    let atkInfo = this.movementInfo[this.src.getDir()];
    if (!atkInfo) return;
    let recover = atkInfo.b || 0;
    if (recover > 0) {
      let recoverDetail = new RecoverDetail();
      recoverDetail.recover = recover;
      recoverDetail.goRole = this.src;
      this.src.emitMsg("onRoleRecover", recoverDetail);
      let path = "resources?prefab/effectLab/hint_lab_suck";
      FightManager.instance.getSection(TipManager).callTip(path, this.src);
    }
  }

  /**攻击方连击 */
  private comboAtk() {
    //log.log("空");
  }

  /**攻击方反击 */
  private counterAtk() {
    //log.log("空");
  }

  protected loadHeBullet() {
    let bulletDB = JsonMgr.instance.jsonList.c_bulletShow[this._detail.bulletId];

    let bulletDetail: CallBulletDetail = {
      goSkill: this._detail.goSkill,
      actionType: this._detail.actionType,
      movementInfo: this.movementInfo,
      resolveBack: this.resolveBack,
      bulletId: bulletDB.heBulletId,
      skillId: this._detail.skillId,
      target: this.target,
      src: this.src,
    };
    FightManager.instance.getSection(BulletManager).doCallObject(bulletDetail);
  }

  public remove(): void {
    this._detail.goSkill;
    if (this._detail.goSkill && this._detail.goSkill.isValid == true) {
      this._detail.goSkill.remove();
    }
    super.remove();
  }
}
