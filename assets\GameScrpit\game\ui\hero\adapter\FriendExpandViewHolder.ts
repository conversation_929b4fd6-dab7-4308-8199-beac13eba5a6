import { _decorator, Label, Node } from "cc";
import { ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import TipMgr from "../../../../lib/tips/TipMgr";
import { HeroModule } from "../../../../module/hero/HeroModule";
import { JsonMgr } from "../../../mgr/JsonMgr";
import { UIMgr } from "../../../../lib/ui/UIMgr";
import { FriendModule } from "../../../../module/friend/FriendModule";
import Formate from "../../../../lib/utils/Formate";
import { divide, times } from "../../../../lib/utils/NumbersUtils";
import { FriendRouteItem } from "../../../../module/friend/FriendRoute";
import { AttrEnum, HeroAttrEnum } from "../../../GameDefine";
import GuideMgr from "db://assets/GameScrpit/ext_guide/GuideMgr";
const { ccclass, property } = _decorator;
@ccclass("FriendExpandViewHolder")
export class FriendExpandViewHolder extends ViewHolder {
  @property(Label)
  tagName: Label;
  @property(Label)
  itemPowerAdd: Label;
  @property(Label)
  itemAttackAdd: Label;
  @property(Label)
  itemAttackPercentAdd: Label;
  @property(Label)
  itemDefenceAdd: Label;
  @property(Label)
  itemDefencePercentAdd: Label;
  @property(Label)
  itemBloodAdd: Label;
  @property(Label)
  itemBloodPercentAdd: Label;
  @property(Node)
  btnGo: Node;
  @property(Node)
  btnGet: Node;
  @property(Node)
  nodeOwned: Node;
  @property(Node)
  tips: Node;

  _friend_id: number;
  _heroId: number;

  private onClickGo() {
    UIMgr.instance.showDialog(FriendRouteItem.UIFriendMain);
  }
  private onClickGet() {
    // TipMgr.showTip("功能暂未开放");
    let friend = FriendModule.config.getFriendById(this._friend_id);

    if (friend.labelType.length > 0 && friend.labelType[0] == 1) {
      //仙友目标
      GuideMgr.startGuide({ stepId: 60 });
      return;
    }
    switch (friend.unlock) {
      case 1: //游历累计偶遇次数
        GuideMgr.startGuide({ stepId: 21 });
        break;
      case 2: //VIP等级
        GuideMgr.startGuide({ stepId: 61 });
        break;
      case 3: //主角等级
        GuideMgr.startGuide({ stepId: 25 });
        break;
      case 4: //建筑总等级
        TipMgr.showTip(`建筑总等级达到${friend.unlockNum}级解锁`);
        break;
      case 5: //繁荣度
        TipMgr.showTip(`繁荣度达到${friend.unlockNum}解锁`);
        break;
      case 6: //累计徒弟结伴数量
        TipMgr.showTip(`累计徒弟结伴数量达到${friend.unlockNum}解锁`);
        break;
      case 7: //累计获得灵兽数量
        TipMgr.showTip(`累计获得灵兽数量达到${friend.unlockNum}解锁`);
        break;
      case 8: //首充次数
        break;
      case 9: //累计徒弟成年数
        TipMgr.showTip(`累计徒弟成年数达到${friend.unlockNum}解锁`);
        break;
      case 10: //演武场累计胜利
        TipMgr.showTip(`演武场累计胜利达到${friend.unlockNum}解锁`);
        break;
      case 11: //幸运商店
        GuideMgr.startGuide({ stepId: 36 });
        break;
      case 12: //七日登录
        TipMgr.showTip("请关注限时任务开启");
        break;
      case 13: //福地采集次数47
        GuideMgr.startGuide({ stepId: 42 });
        break;
      case 14: //活动
        TipMgr.showTip("请关注限时任务开启");

        break;
    }
  }
  public updateData(heroId: number, friendId: number) {
    this._friend_id = friendId;
    this._heroId = heroId;
    let attrAdd = FriendModule.service.getHeroAttrAddByFriendId(this._friend_id);
    let friend = FriendModule.config.getFriendById(this._friend_id);
    let friendMessage = FriendModule.data.getFriendMessage(friendId);
    let heroMessage = HeroModule.data.getHeroMessage(heroId);
    this.tagName.string = friend.name;
    if (friendMessage) {
      this.btnGet.active = false;
      this.btnGo.active = true;
      this.tips.active = false;
      this.nodeOwned.active = true;
      if (heroMessage) {
        this.itemPowerAdd.node.active = true;
        this.itemPowerAdd.string = `战力+${Formate.format(this.calcHeroPowerAdd(this._heroId, this._friend_id))}`;
      } else {
        this.itemPowerAdd.node.active = false;
      }
      this.itemAttackAdd.string = `攻击+${attrAdd[AttrEnum.攻击_2]}`;
      this.itemAttackPercentAdd.string = `攻击+${divide(attrAdd[HeroAttrEnum.战将攻击百分比_12], 100)}%`;
      this.itemDefenceAdd.string = `防御+${attrAdd[AttrEnum.防御_3]}`;
      this.itemDefencePercentAdd.string = `防御+${divide(attrAdd[HeroAttrEnum.战将防御百分比_13], 100)}%`;
      this.itemBloodAdd.string = `生命+${attrAdd[AttrEnum.生命_1]}`;
      this.itemBloodPercentAdd.string = `生命+${divide(attrAdd[HeroAttrEnum.战将生命百分比_11], 100)}%`;
    } else {
      if (heroMessage) {
        this.itemPowerAdd.node.active = true;
        this.itemPowerAdd.string = `战力+0`;
      } else {
        this.itemPowerAdd.node.active = false;
      }
      this.btnGet.active = true;
      this.btnGo.active = false;
      this.tips.active = true;
      this.nodeOwned.active = false;
    }
  }
  private calcHeroPowerAdd(heroId: number, friendId: number): number {
    // let hero = HeroModule.config.getHeroInfo(heroId);
    let heroBaseAttr = HeroModule.service.getHeroBaseAttr(heroId); //英雄基础属性
    let friendAttrAdd = FriendModule.service.getHeroAttrAddByFriendId(friendId);
    // 挚友属性增加部分
    let attack =
      times(heroBaseAttr[AttrEnum.攻击_2], friendAttrAdd[HeroAttrEnum.战将攻击百分比_12] / 10000) +
      friendAttrAdd[AttrEnum.攻击_2];
    let defense =
      times(heroBaseAttr[AttrEnum.防御_3], friendAttrAdd[HeroAttrEnum.战将防御百分比_13] / 10000) +
      friendAttrAdd[AttrEnum.防御_3];
    let blood =
      times(heroBaseAttr[AttrEnum.生命_1], friendAttrAdd[HeroAttrEnum.战将生命百分比_11] / 10000) +
      friendAttrAdd[AttrEnum.生命_1];
    let attackPowerRate = JsonMgr.instance.jsonList.c_attribute[2].powerRate1List[1];
    let defensePowerRate = JsonMgr.instance.jsonList.c_attribute[3].powerRate1List[1];
    let bloodPowerRate = JsonMgr.instance.jsonList.c_attribute[1].powerRate1List[1];
    let powerAdd = attack * attackPowerRate + defense * defensePowerRate + blood * bloodPowerRate;
    return powerAdd;
  }
}
