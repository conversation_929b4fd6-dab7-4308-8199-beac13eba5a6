import { _decorator, Component, EventTouch, Label, Node } from "cc";
import { ListView } from "../../common/ListView";
import { SoulTuJianAdaper } from "./adapter/SoulTuJianAdaper";
import { JsonMgr } from "../../mgr/JsonMgr";
import { SoulTuJianAttrAdapter } from "./adapter/SoulTuJianAttrAdapter";
import { SoulModule } from "../../../module/soul/SoulModule";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { SoulTujianAttr } from "./SoulTujianAttr";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { UIClubTips } from "../../common/UIClubTips";
import { BadgeMgr, BadgeType } from "../../mgr/BadgeMgr";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { AdapterView } from "../../../../platform/src/core/ui/adapter_view/AdapterView";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

const PLANTNUM = 5;
@ccclass("UISoulDetailTujian")
export class UISoulDetailTujian extends Component {
  @property(AdapterView)
  private listView: AdapterView;
  @property(Node)
  private nodeTab: Node;

  @property(Node)
  private viewholder: Node;
  @property(Node)
  private listAttrTips: Node;
  @property(Node)
  private attrTipsViewholder: Node;
  @property(Node)
  private nodeTips: Node;
  @property(Node)
  private bgYiShiYong: Node;
  @property(Node)
  private btnApply: Node;

  @property(Node)
  private nodeAttrs: Node;
  @property(Node)
  private nodeUnlockTips: Node;

  @property(Node)
  private attrs_hit: Node;

  private _adapter: SoulTuJianAdaper;
  private _attrAdapter: SoulTuJianAttrAdapter;
  private _curPlanIndex: number;
  private _curTabIndex: number;
  private _soulPictureDatas: any[][] = [];
  protected start(): void {
    //======初始化数据-开始========
    Object.values(JsonMgr.instance.jsonList.c_soulPicture).forEach((item: any) => {
      // this._adapter.addItem(item);
      let i1 = Math.floor((item.id - 2001) / 1000);
      if (!this._soulPictureDatas[i1]) {
        this._soulPictureDatas[i1] = [];
      }
      this._soulPictureDatas[i1].push(item);
    });
    //======初始化数据-结束========

    //======初始化页面-开始========
    this._adapter = new SoulTuJianAdaper(this, this.viewholder);
    this.listView.setAdapter(this._adapter);
    this._curPlanIndex = 0;
    for (let i = 0; i < SoulModule.data.warriorSoulManageMsg.planList.length; i++) {
      let plan = SoulModule.data.warriorSoulManageMsg.planList[i];
      if (plan.use) {
        this._curPlanIndex = i;
        break;
      }
    }
    for (let i = 0; i < SoulModule.data.warriorSoulManageMsg.planList.length && i < PLANTNUM; i++) {
      this.nodeTab.getChildByName("tab" + (i + 1)).getChildByName("node_normal").active = true;
      this.nodeTab.getChildByName("tab" + (i + 1)).getChildByName("node_disable").active = false;
    }
    this.onSelectTab(0);
    this._attrAdapter = new SoulTuJianAttrAdapter(this.attrTipsViewholder);
    this.listAttrTips.getComponent(ListView).setAdapter(this._attrAdapter);
    //======初始化页面-结束========

    MsgMgr.on(MsgEnum.ON_SOUL_UPDATE, this.onSoulUpdate, this);
    MsgMgr.on(MsgEnum.ON_SOUL_TUJIAN_UPDATE, this.onSoulTujianUpdate, this);
    for (let i = 1; i <= 5; i++) {
      BadgeMgr.instance.setBadgeId(
        this.nodeTab.getChildByName(`tab${i}`),
        BadgeType.UITerritory.btn_soul.btn_tab_tujian[`tab${i}`].id
      );
    }
  }

  protected onDestroy(): void {
    MsgMgr.off(MsgEnum.ON_SOUL_UPDATE, this.onSoulUpdate, this);
    MsgMgr.off(MsgEnum.ON_SOUL_TUJIAN_UPDATE, this.onSoulTujianUpdate, this);
  }

  private onSoulTujianUpdate() {
    for (let i = 0; i < SoulModule.data.warriorSoulManageMsg.planList.length && i < PLANTNUM; i++) {
      let plan = SoulModule.data.warriorSoulManageMsg.planList[i];
      if (i != this._curTabIndex) {
        this.nodeTab.getChildByName("tab" + (i + 1)).getChildByName("node_normal").active = true;
        this.nodeTab.getChildByName("tab" + (i + 1)).getChildByName("node_disable").active = false;
      }
      if (plan.use) {
        this._curPlanIndex = i;
      }
    }
    let curPlan = SoulModule.data.warriorSoulManageMsg.planList[this._curTabIndex];
    let map: { [key: number]: number } = {};
    // Object.values(curPlan.pictureMap).forEach((val) => {
    //   if (map[val.attrId]) {
    //     map[val.attrId] += val.attrAdd;
    //   } else {
    //     map[val.attrId] = val.attrAdd;
    //   }
    // });
    // let keys = Object.keys(map);
    let attrs = Object.values(curPlan.pictureMap);

    if (attrs.length <= 0) {
      this.attrs_hit.active = true;
      this.nodeAttrs.active = false;
    } else {
      this.attrs_hit.active = false;
      this.nodeAttrs.active = true;
    }

    let i = 0;
    for (; i < attrs.length; i++) {
      this.nodeAttrs.children[i].active = true;
      this.nodeAttrs.children[i]
        .getComponent(SoulTujianAttr)
        .setAttrId(this._soulPictureDatas[this._curTabIndex][0].id, attrs[i].attrId, attrs[i].attrAdd);
    }
    for (; i < this.nodeAttrs.children.length; i++) {
      this.nodeAttrs.children[i].active = false;
    }
    // 更新使用状态
    if (this._curPlanIndex == this._curTabIndex) {
      this.bgYiShiYong.active = true;
      this.btnApply.active = false;
    } else {
      this.bgYiShiYong.active = false;
      this.btnApply.active = true;
    }
  }
  /**
   * 当兽魂数据更新或页面初始化的时候刷新页面信息
   */
  private onSoulUpdate() {
    this._adapter.notifyDataSetChanged("DATAONLY");
    this.onSoulTujianUpdate();
  }
  /**
   * 点击空白处关闭属性详细信息
   */
  private onClickTipsBlank() {
    this.nodeTips.active = false;
  }
  /**
   * tab 切换的时候更新tab状态和页面显示信息
   */
  private onSelectTab(index: number) {
    this._curTabIndex = index;
    log.log("onSelectTab", this._soulPictureDatas);
    this._adapter.setData(this._soulPictureDatas[index], this._curTabIndex);

    this.nodeTab.getChildByName("tab" + (index + 1)).getChildByName("node_select").active = true;
    this.nodeTab.getChildByName("tab" + (index + 1)).getChildByName("node_normal").active = false;

    this.onSoulTujianUpdate();
  }
  /**
   * tab 点击事件
   */
  private onClickTab(event: EventTouch, cusIndex: string) {
    let index = Number(cusIndex);

    if (index < SoulModule.data.warriorSoulManageMsg.planList.length) {
      for (let i = 0; i < SoulModule.data.warriorSoulManageMsg.planList.length && i < PLANTNUM; i++) {
        this.nodeTab.getChildByName("tab" + (i + 1)).getChildByName("node_normal").active = true;
        this.nodeTab.getChildByName("tab" + (i + 1)).getChildByName("node_select").active = false;
      }
      this.onSelectTab(index);
    } else {
      let wolrd = event.target.getWorldPosition();
      let args = {
        worldx: wolrd.x,
        worldy: wolrd.y - 25,
        itemList: [],
        // keepMask: true,
      };
      this.nodeUnlockTips.active = true;
      this.nodeUnlockTips.getComponent(UIClubTips).setTips(args);
    }
  }

  private onClickApply() {
    if (this._curPlanIndex == this._curTabIndex) {
      TipsMgr.showTip("当前已激活");
    } else {
      AudioMgr.instance.playEffect(1011);
      SoulModule.api.workPlan(this._curTabIndex, (res) => {
        TipsMgr.showTip("方案替换成功");
      });
      // SoulModule.api.useBackUpPictureSkill(this._curTabIndex + 1, (res) => {
      //   if (res.code == 200) {
      //     TipsMgr.showTip("激活成功");
      //     this.onSoulUpdate();
      //   }
      // });
    }
  }
  private onClickCloseXilian(e: EventTouch) {
    //
    if (e.target.name === "node_xilian") {
      e.target.active = false;
    }
  }
  private onClickTest1() {
    SoulModule.api.testAddSoulTemplateId(41001);
    SoulModule.api.testAddSoulTemplateId(41002);
    SoulModule.api.testAddSoulTemplateId(41003);
    SoulModule.api.testAddSoulTemplateId(41004);
    SoulModule.api.testAddSoulTemplateId(41005);
    SoulModule.api.getAll();
  }
  private onClickTest2() {
    SoulModule.api.testAddSoulTemplateId(41011);
    SoulModule.api.testAddSoulTemplateId(41012);
    SoulModule.api.testAddSoulTemplateId(41013);
    SoulModule.api.testAddSoulTemplateId(41014);
    SoulModule.api.testAddSoulTemplateId(41015);
    SoulModule.api.getAll();
  }
  private onClickTest3() {
    SoulModule.api.testAddSoulTemplateId(41021);
    SoulModule.api.testAddSoulTemplateId(41022);
    SoulModule.api.testAddSoulTemplateId(41023);
    SoulModule.api.testAddSoulTemplateId(41024);
    SoulModule.api.testAddSoulTemplateId(41025);
    SoulModule.api.getAll();
  }
  private onClickTest4() {
    SoulModule.api.testAddSoulTemplateId(41031);
    SoulModule.api.testAddSoulTemplateId(41032);
    SoulModule.api.testAddSoulTemplateId(41033);
    SoulModule.api.testAddSoulTemplateId(41034);
    SoulModule.api.testAddSoulTemplateId(41035);
    SoulModule.api.getAll();
  }
  /**
   * 显示属性概率详细信息
   * @param data
   */
  public showTips(data: any) {
    this.nodeTips.active = true;
    this._attrAdapter.setData(data);
  }
}
