{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "1ebd6d6a-1e51-481f-899b-ddad10a927ed", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "1ebd6d6a-1e51-481f-899b-ddad10a927ed@6c48a", "displayName": "bg_baoXiangHao<PERSON>_jianBian", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "1ebd6d6a-1e51-481f-899b-ddad10a927ed", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "1ebd6d6a-1e51-481f-899b-ddad10a927ed@f9941", "displayName": "bg_baoXiangHao<PERSON>_jianBian", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 81, "height": 70, "rawWidth": 81, "rawHeight": 70, "borderTop": 0, "borderBottom": 11, "borderLeft": 18, "borderRight": 16, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-40.5, -35, 0, 40.5, -35, 0, -40.5, 35, 0, 40.5, 35, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 70, 81, 70, 0, 0, 81, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-40.5, -35, 0], "maxPos": [40.5, 35, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "1ebd6d6a-1e51-481f-899b-ddad10a927ed@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "1ebd6d6a-1e51-481f-899b-ddad10a927ed@6c48a"}}