import { _decorator, Label, Node, tween, v3 } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import MsgMgr from "../../../lib/event/MsgMgr";
import TipMgr from "../../../lib/tips/TipMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import ToolExt from "../../common/ToolExt";
import MsgEnum from "../../event/MsgEnum";
import { ApiHandler } from "../../mgr/ApiHandler";
import { JsonMgr } from "../../mgr/JsonMgr";
import { PostActionSubCmd } from "../../net/cmd/CmdData";
import { DialogZero } from "../../GameDefine";
import { PostTrainMessage } from "../../net/protocol/Post";
import { PostModule } from "../../../module/post/postModule";
import data from "../../../lib/data/data";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { PostAudioName } from "../../../module/post/postConfig";

const { ccclass, property } = _decorator;

@ccclass("UIPostLevelUp")
export class UIPostLevelUp extends UINode {
  protected _openAct: boolean = true; // 打开动作

  private _data: PostTrainMessage = null;
  private _callback = null;

  private _level1Node: Node;
  private _time1Node: Node;
  private _level2Node: Node;
  private _newTimeNode: Node;
  private _goCountNode: Node;
  private _maxLevelNode: Node;
  private _maxTimeNode: Node;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_POST}?prefab/ui/UIPostLevelUp`;
  }

  public init(args: any): void {
    super.init(args);
    this._data = args.data;
    this._callback = args.callback;
  }

  protected onEvtShow(): void {
    this.initMain();
  }

  private initMain() {
    const c_post = JsonMgr.instance.jsonList.c_post;
    const list = Object.keys(c_post);
    const maxLevelIs = this._data.level >= list.length;

    if (maxLevelIs) {
      this.main2Init();
    } else {
      this.main1Init();
    }
  }

  private main1Init() {
    this.getNode("main1").active = true;
    this.getNode("main2").active = false;

    const c_post = JsonMgr.instance.jsonList.c_post;

    this._level1Node = this.getNode("level1");
    this._time1Node = this.getNode("time1");
    this._level2Node = this.getNode("level2");
    this._newTimeNode = this.getNode("newTime");
    this._goCountNode = this.getNode("goCount");

    this._level1Node.getComponent(Label).string = `${this._data.level}级`;
    this._time1Node.getComponent(Label).string = `货运时间：${this.getHour(c_post[this._data.level].time)}`;

    this._level2Node.getComponent(Label).string = `${this._data.level + 1}级`;
    this._newTimeNode.getComponent(Label).string = `${this.getHour(c_post[this._data.level + 1].time)}`;

    this._goCountNode.getComponent(Label).string = `${this._data.historyCount}/${c_post[this._data.level + 1].need}`;

    if (this._data.historyCount < c_post[this._data.level + 1].need) {
      ToolExt.setLabColor(this._goCountNode.getComponent(Label), "#FF0000");
    } else {
      ToolExt.setLabColor(this._goCountNode.getComponent(Label), "#00af04");
    }
  }

  private main2Init() {
    this.getNode("main1").active = false;
    this.getNode("main2").active = true;

    const c_post = JsonMgr.instance.jsonList.c_post;

    this._maxLevelNode = this.getNode("max_level");
    this._maxTimeNode = this.getNode("maxTime");

    this._maxLevelNode.getComponent(Label).string = `${this._data.level}级`;
    this._maxTimeNode.getComponent(Label).string = `货运时间：${this.getHour(c_post[this._data.level].time)}`;
  }

  private getHour(time: number): string {
    return `${Math.floor(time / 60 / 60)}h`;
  }

  private on_click_btn_level() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    const nextLevel = this._data.level + 1;
    const c_post = JsonMgr.instance.jsonList.c_post;

    if (this._data.historyCount < c_post[nextLevel].need) {
      TipMgr.showTip("升级条件不满足");
      return;
    }

    PostModule.api.levelUp((data) => {
      AudioMgr.instance.playEffect(PostAudioName.Effect.升级成功);
      this._data = data;
      this._callback();
      UIMgr.instance.back();
    });
  }

  private on_click_btn_close() {
    // 关闭窗口 effect_guanbiyinxiao
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
}
