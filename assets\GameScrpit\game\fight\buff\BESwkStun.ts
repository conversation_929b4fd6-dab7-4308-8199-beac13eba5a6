import { _decorator, Component, instantiate, isValid, Node, Prefab, sp, UITransform, v3 } from "cc";
import BEBuff from "./BEBuff";
import GameObject from "../../../lib/object/GameObject";
import { GOBuff } from "./GOBuff";
import ResMgr from "../../../lib/common/ResMgr";
import RenderSection from "../../../lib/object/RenderSection";
import DirectSection, { DIRECT } from "../../../lib/object/DirectSection";
const { ccclass, property } = _decorator;

@ccclass("BESwkStun")
export class BESwkStun extends BEBuff {
  private _prefab: Prefab;
  private _render: Node;
  public static sectionName(): string {
    return "BESwkStun";
  }

  public onInit(go: GameObject, args) {
    super.onInit(go, args);
    this.ready();
  }

  public onStart() {
    this.playPrefab();
  }

  protected playPrefab() {
    let role = (this.getSub() as GOBuff).getDetail().attachment;
    let render = role.getSection(RenderSection).getRender();

    ResMgr.loadPrefab("resources?prefab/effect/swk_stun", async (prefab) => {
      if (!isValid(role)) return;

      this._prefab = prefab;

      let node = instantiate(prefab);
      this._render = node;

      let y = render.getComponent(UITransform).height / 2;
      let x = 0;
      render.addChild(node);
      node.walk((child) => (child.layer = render.layer));

      let dir = role.getSection(DirectSection).getDirect();
      if (dir == DIRECT.LEFT) {
        node.setScale(-1, 1, 1);
      } else {
        node.setScale(1, 1, 1);
      }

      node.setPosition(v3(x, y, 1));
      node.getComponent(sp.Skeleton).setAnimation(0, "1", false);
      node.getComponent(sp.Skeleton).setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
        if ("1" == trackEntry.animation.name) {
          node.getComponent(sp.Skeleton).setCompleteListener(null);
          node.getComponent(sp.Skeleton).setAnimation(0, "2", true);
        }
      });
    });
  }

  public async removeBe() {
    let self = this;
    let role = (self.getSub() as GOBuff).getDetail().attachment;
    if (role.getRoundDetail().b == 1) {
      self._render.getComponent(sp.Skeleton).setAnimation(0, "3", false);
      await new Promise(async (res) => {
        self._render.getComponent(sp.Skeleton).setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
          if ("3" == trackEntry.animation.name) {
            self._render.getComponent(sp.Skeleton).setCompleteListener(null);
            self._render.removeFromParent();
            self._render.destroy();
            self.getSub().remove();
            res(1);
          }
        });
      });
    }
  }

  public updateSelf(dt: any): void {}

  //updateSelf
  public onRemove(): void {
    if (this._prefab) {
      this._prefab.decRef();
    }
  }
}
