{"skeleton": {"hash": "k3TEYTLmFirV5PCshHvqrvyisaU=", "spine": "3.8.75", "x": -94.74, "y": -11.41, "width": 212.71, "height": 346.25, "images": "./images/", "audio": "D:/spine导出/主角换皮动画/主角女/女主角3"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "y": -11.93}, {"name": "bone2", "parent": "bone", "length": 75.15, "rotation": 0.7, "x": 8.02, "y": 177.87, "color": "4a00ffff"}, {"name": "bone3", "parent": "bone2", "length": 35, "rotation": 94.23, "x": -4.95, "y": -1.66}, {"name": "bone4", "parent": "bone3", "length": 34.8, "rotation": 3.81, "x": 36.95, "y": 0.29}, {"name": "bone5", "parent": "bone4", "length": 10.59, "rotation": -1.33, "x": 35.16, "y": 0.11}, {"name": "bone6", "parent": "bone5", "rotation": -5.37, "x": 25.57, "y": -15.54}, {"name": "bone13", "parent": "bone6", "x": 27.12, "y": -3.13}, {"name": "bone14", "parent": "bone13", "length": 11.27, "rotation": 59.56, "x": 0.95, "y": 6.03}, {"name": "bone15", "parent": "bone14", "length": 14.53, "rotation": 96.05, "x": 11.22, "y": 1.62}, {"name": "bone16", "parent": "bone15", "length": 15, "rotation": 28.73, "x": 15.53, "y": 0.41}, {"name": "bone22", "parent": "bone5", "length": 40, "rotation": 175.76, "x": 49.95, "y": -32.07, "color": "ff5d5dff"}, {"name": "bone31", "parent": "bone4", "x": 25.58, "y": 25.06}, {"name": "bone36", "parent": "bone31", "length": 37.62, "rotation": 153.83, "x": -1.83, "y": 1.87}, {"name": "bone37", "parent": "bone36", "length": 40, "rotation": 23.95, "x": 37.62}, {"name": "bone38", "parent": "bone37", "length": 16.25, "rotation": 1.18, "x": 40.38, "y": 0.51}, {"name": "bone62", "parent": "bone38", "length": 42.77, "rotation": 69.27, "x": 6.61, "y": 0.58}, {"name": "bone32", "parent": "bone4", "x": 14.68, "y": -22.32}, {"name": "bone33", "parent": "bone32", "length": 35.85, "rotation": -175.38, "x": -0.05, "y": 3.23}, {"name": "bone34", "parent": "bone33", "length": 35.66, "rotation": -1.06, "x": 34.77, "y": -0.14}, {"name": "bone35", "parent": "bone34", "length": 15.47, "rotation": -1.56, "x": 35.83, "y": -0.04}, {"name": "bone39", "parent": "bone2", "x": -18.01, "y": -12.03}, {"name": "bone7", "parent": "bone2", "x": -3.5, "y": -16.79}, {"name": "bone44", "parent": "bone7", "length": 40, "rotation": -94.44, "x": 14.19, "y": -3.88, "color": "52f94bff"}, {"name": "bone45", "parent": "bone44", "length": 40, "rotation": -0.31, "x": 40.27, "y": -0.78, "color": "52f94bff"}, {"name": "bone46", "parent": "bone45", "length": 40, "rotation": -2.1, "x": 39.99, "y": 0.48, "color": "52f94bff"}, {"name": "bone49", "parent": "bone7", "length": 40, "rotation": -85.33, "x": -8.35, "y": -1.39, "color": "52f94bff"}, {"name": "bone50", "parent": "bone49", "length": 40, "rotation": -7.67, "x": 41.05, "y": -0.62, "color": "52f94bff"}, {"name": "bone51", "parent": "bone50", "length": 40, "rotation": -6.83, "x": 40.34, "y": -0.08, "color": "52f94bff"}, {"name": "bone52", "parent": "bone2", "length": 73.79, "rotation": -97.23, "x": -24.56, "y": -16.22}, {"name": "bone53", "parent": "bone52", "length": 56.02, "rotation": -0.27, "x": 73.79}, {"name": "bone54", "parent": "bone53", "length": 20, "rotation": 26.74, "x": 56.63, "y": -0.26}, {"name": "bone56", "parent": "bone2", "length": 76.08, "rotation": -85.57, "x": 3.27, "y": -15.97}, {"name": "bone57", "parent": "bone56", "length": 55.47, "rotation": -8.81, "x": 76.08}, {"name": "bone58", "parent": "bone57", "length": 20, "rotation": 65.38, "x": 55.47}, {"name": "bone8", "parent": "bone2", "length": 45, "rotation": -101.42, "x": -25.58, "y": -5.13, "color": "ff6d6dff"}, {"name": "bone9", "parent": "bone8", "length": 41, "rotation": -0.81, "x": 45.29, "y": 0.04, "color": "ff6d6dff"}, {"name": "bone10", "parent": "bone9", "length": 40, "rotation": -7.34, "x": 41.55, "y": 0.16, "color": "ff6d6dff"}, {"name": "bone11", "parent": "bone2", "length": 40, "rotation": -85.39, "x": 14.26, "y": -9.94, "color": "ff6d6dff"}, {"name": "bone12", "parent": "bone11", "length": 40, "rotation": -6.93, "x": 39.84, "y": -0.38, "color": "ff6d6dff"}, {"name": "bone18", "parent": "bone12", "length": 40, "rotation": -1.41, "x": 39.74, "y": 0.19, "color": "ff6d6dff"}, {"name": "bone25", "parent": "bone5", "length": 40, "rotation": 155.29, "x": 57.97, "y": 28.06, "color": "ff5d5dff"}, {"name": "bone26", "parent": "bone25", "length": 40, "rotation": 2.29, "x": 41.71, "y": -0.24, "color": "ff5d5dff"}, {"name": "bone27", "parent": "bone26", "length": 40, "rotation": 3.12, "x": 41.04, "y": -0.16, "color": "ff5d5dff"}, {"name": "bone28", "parent": "bone22", "length": 40, "rotation": 2.29, "x": 41.71, "y": -0.24, "color": "ff5d5dff"}, {"name": "bone29", "parent": "bone28", "length": 40, "rotation": 3.12, "x": 41.04, "y": -0.16, "color": "ff5d5dff"}, {"name": "bone17", "parent": "bone2", "length": 12.55, "rotation": -68.8, "x": 15.54, "y": -5.06}, {"name": "bone19", "parent": "bone17", "length": 12.4, "rotation": -9.61, "x": 12.48, "y": 0.16}, {"name": "bone20", "parent": "bone19", "length": 14.14, "rotation": -5.46, "x": 12.4}, {"name": "bone21", "parent": "bone2", "length": 13.51, "rotation": -128.49, "x": -18.02, "y": -1.41}, {"name": "bone23", "parent": "bone21", "length": 14.87, "rotation": 9.36, "x": 13.42, "y": -0.07}, {"name": "bone24", "parent": "bone23", "length": 14.73, "rotation": 5.41, "x": 14.93, "y": -0.11}, {"name": "bone63", "parent": "root", "x": 49.46, "y": 384.69, "scaleX": 1.7354, "scaleY": 1.7354}, {"name": "bone30", "parent": "bone2", "length": 78.52, "rotation": -76.14, "x": -23.61, "y": -15.74, "color": "4a00ffff"}, {"name": "bone64", "parent": "bone30", "length": 64.73, "rotation": -47.71, "x": 78.52, "color": "4a00ffff"}, {"name": "bone65", "parent": "bone2", "length": 78.69, "rotation": -68.77, "x": 11.25, "y": -20.11, "color": "4a00ffff"}, {"name": "bone66", "parent": "bone65", "length": 63.22, "rotation": -53.75, "x": 78.69, "color": "4a00ffff"}, {"name": "bone55", "parent": "bone", "length": 36.19, "rotation": -1.44, "x": -43.83, "y": 13.59}, {"name": "bone59", "parent": "bone55", "length": 32.53, "rotation": 91.44, "x": -0.75, "y": -0.32}, {"name": "rjio1", "parent": "bone59", "rotation": -90, "x": 74.53, "y": -19.64, "color": "ff3f00ff"}, {"name": "rjio2", "parent": "bone59", "rotation": -90, "x": 18.48, "y": -12.54, "color": "ff3f00ff"}, {"name": "rjio3", "parent": "rjio2", "x": 6.57, "y": -18.51, "transform": "noScale", "color": "ff3f00ff"}, {"name": "bone60", "parent": "bone", "length": 41.19, "rotation": -0.63, "x": 7.39, "y": 13.13}, {"name": "bone61", "parent": "bone60", "length": 34.53, "rotation": 92.9, "x": -0.3, "y": -0.16}, {"name": "ljio1", "parent": "bone61", "rotation": -92.27, "x": 72.59, "y": -13.96, "color": "ff3f00ff"}, {"name": "ljio2", "parent": "bone61", "rotation": -92.27, "x": 17.48, "y": -7.74, "color": "ff3f00ff"}, {"name": "ljio3", "parent": "ljio2", "x": 17.39, "y": -9.58, "transform": "noScale", "color": "ff3f00ff"}], "slots": [{"name": "qub1", "bone": "bone8", "attachment": "qub1"}, {"name": "tfs1", "bone": "bone22", "attachment": "tfs1"}, {"name": "ss2", "bone": "bone33", "attachment": "ss2"}, {"name": "jio2", "bone": "bone56", "attachment": "jio2"}, {"name": "jio1", "bone": "bone52", "attachment": "jio1"}, {"name": "tfs3", "bone": "bone25", "attachment": "tfs2"}, {"name": "bd", "bone": "bone3", "attachment": "bd"}, {"name": "qub4", "bone": "bone49", "attachment": "qub4"}, {"name": "qub3", "bone": "bone2", "attachment": "qub3"}, {"name": "qub2", "bone": "bone2", "attachment": "qub2"}, {"name": "ya<PERSON>i", "bone": "bone2", "attachment": "ya<PERSON>i"}, {"name": "ts2", "bone": "bone5", "attachment": "ts2"}, {"name": "tou", "bone": "bone5", "attachment": "tou"}, {"name": "ts1", "bone": "bone5", "attachment": "ts1"}, {"name": "tf1", "bone": "bone5", "attachment": "tf1"}, {"name": "tf2", "bone": "bone5", "attachment": "tf2"}, {"name": "biyan", "bone": "bone6"}, {"name": "liuh2", "bone": "root", "attachment": "liuh2"}, {"name": "liuh1", "bone": "bone13", "attachment": "liuh1"}, {"name": "erduo", "bone": "bone5", "attachment": "erduo"}, {"name": "sss2", "bone": "bone33"}, {"name": "yiyi4", "bone": "bone2"}, {"name": "tt2", "bone": "bone25"}, {"name": "zanzi", "bone": "bone5"}, {"name": "jio4", "bone": "bone56"}, {"name": "jio3", "bone": "bone52"}, {"name": "yiyi2", "bone": "bone2"}, {"name": "body", "bone": "bone3"}, {"name": "tou2", "bone": "bone5"}, {"name": "toufa111", "bone": "bone5"}, {"name": "erduo2", "bone": "bone5"}, {"name": "yi2", "bone": "bone2"}, {"name": "mutou", "bone": "bone62"}, {"name": "biyan2", "bone": "bone6"}, {"name": "ss3", "bone": "bone36"}, {"name": "tf234", "bone": "bone14"}, {"name": "wuqi", "bone": "bone62", "attachment": "wuqi"}, {"name": "ss1", "bone": "bone36", "attachment": "ss1"}, {"name": "mutou3", "bone": "bone63"}, {"name": "xuanz", "bone": "bone63", "color": "ffffff6f", "blend": "additive"}], "ik": [{"name": "ljio1", "order": 7, "bones": ["bone56"], "target": "ljio1", "compress": true, "stretch": true}, {"name": "ljio2", "order": 8, "bones": ["bone57"], "target": "ljio2", "compress": true, "stretch": true}, {"name": "ljio3", "order": 9, "bones": ["bone58"], "target": "ljio3"}, {"name": "ljio4", "order": 5, "bones": ["bone65", "bone66"], "target": "ljio2", "bendPositive": false}, {"name": "rjio1", "order": 2, "bones": ["bone52"], "target": "rjio1", "compress": true, "stretch": true}, {"name": "rjio2", "order": 3, "bones": ["bone53"], "target": "rjio2", "compress": true, "stretch": true}, {"name": "rjio3", "order": 4, "bones": ["bone54"], "target": "rjio3"}, {"name": "rjio4", "bones": ["bone30", "bone64"], "target": "rjio2", "bendPositive": false}], "transform": [{"name": "ljio5", "order": 6, "bones": ["ljio1"], "target": "bone66", "rotation": 121.97, "x": 14.16, "y": -25.86, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}, {"name": "rjio5", "order": 1, "bones": ["rjio1"], "target": "bone64", "rotation": 123.57, "x": 14.1, "y": -25.08, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}], "skins": [{"name": "default", "attachments": {"tf2": {"tf2": {"x": 30.86, "y": 15.56, "rotation": -97.41, "width": 15, "height": 23}}, "mutou3": {"mutou": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [56.4, -51.34, -68.62, 12.5, -48.9, 51.12, 76.12, -12.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 165, "height": 120}}, "bd": {"bd": {"type": "mesh", "uvs": [0.26808, 0.19364, 0.27277, 0.12176, 0.2962, 0.05366, 0.43679, 0, 0.58911, 0.04042, 0.62426, 0.13878, 0.63832, 0.2239, 0.72267, 0.29011, 0.86327, 0.33739, 0.8703, 0.39982, 0.88202, 0.43386, 0.96872, 0.50007, 1, 0.57006, 0.99449, 0.65896, 0.95232, 0.76489, 0.92185, 0.92, 0.87733, 0.98431, 0.77657, 1, 0.61488, 1, 0.40868, 0.97485, 0.26808, 0.90675, 0.2423, 0.85001, 0.24933, 0.81218, 0.16732, 0.75732, 0.07124, 0.66085, 0, 0.52466, 0, 0.3355, 0.09702, 0.25606, 0.18372, 0.24471, 0.23059, 0.22201, 0.46491, 0.11987, 0.49069, 0.22769, 0.57036, 0.35631, 0.69221, 0.48305, 0.74845, 0.59843, 0.71096, 0.75921, 0.7133, 0.91054, 0.75548, 0.39982, 0.79532, 0.47926, 0.87733, 0.56817, 0.89139, 0.67031, 0.85859, 0.78002, 0.82578, 0.92567, 0.47194, 0.89162, 0.43914, 0.77435, 0.35947, 0.64194, 0.33838, 0.50385, 0.2259, 0.37144, 0.17435, 0.51331, 0.2259, 0.65707, 0.34072, 0.79705, 0.35244, 0.83109, 0.3829, 0.90865, 0.54224, 0.62302, 0.56099, 0.76678, 0.58442, 0.92, 0.49069, 0.48494, 0.41102, 0.36577, 0.35478, 0.25795, 0.35478, 0.17283, 0.36181, 0.0896], "triangles": [34, 39, 40, 40, 39, 13, 13, 39, 12, 34, 38, 39, 34, 33, 38, 39, 11, 12, 39, 10, 11, 39, 38, 10, 33, 37, 38, 38, 9, 10, 38, 37, 9, 37, 8, 9, 37, 7, 8, 23, 24, 49, 24, 48, 49, 24, 25, 48, 49, 48, 46, 25, 26, 48, 48, 47, 46, 48, 26, 47, 26, 27, 47, 27, 28, 47, 47, 29, 58, 47, 28, 29, 29, 0, 58, 58, 59, 31, 58, 0, 59, 59, 30, 31, 31, 30, 5, 0, 1, 59, 59, 60, 30, 59, 1, 60, 30, 4, 5, 1, 2, 60, 60, 3, 30, 30, 3, 4, 60, 2, 3, 44, 45, 53, 49, 46, 45, 45, 56, 53, 45, 46, 56, 53, 33, 34, 53, 56, 33, 46, 57, 56, 57, 47, 58, 57, 46, 47, 56, 32, 33, 56, 57, 32, 33, 32, 37, 32, 7, 37, 57, 31, 32, 57, 58, 31, 32, 6, 7, 32, 31, 6, 31, 5, 6, 18, 36, 17, 17, 42, 16, 17, 36, 42, 55, 18, 43, 18, 19, 43, 18, 55, 36, 16, 42, 15, 20, 52, 19, 19, 52, 43, 15, 42, 41, 36, 55, 54, 42, 36, 41, 15, 41, 14, 55, 43, 54, 54, 35, 36, 36, 35, 41, 20, 51, 52, 52, 51, 43, 20, 21, 51, 51, 22, 50, 22, 51, 21, 51, 44, 43, 43, 44, 54, 51, 50, 44, 50, 22, 49, 22, 23, 49, 50, 45, 44, 50, 49, 45, 14, 41, 40, 54, 44, 53, 54, 53, 35, 41, 35, 40, 14, 40, 13, 35, 34, 40, 35, 53, 34], "vertices": [3, 4, 39.28, 13.59, 0.04403, 5, 3.81, 13.57, 0.72002, 12, 13.7, -11.46, 0.23595, 2, 5, 9.68, 12.49, 0.93473, 12, 19.55, -12.68, 0.06527, 2, 5, 15.08, 10.2, 0.98027, 12, 24.9, -15.09, 0.01973, 1, 5, 18.29, 0.29, 1, 2, 4, 48.58, -9.6, 0.03638, 5, 13.64, -9.4, 0.96362, 3, 4, 40.15, -10.69, 0.31086, 5, 5.24, -10.68, 0.68043, 17, 25.47, 11.64, 0.00871, 3, 4, 33.03, -10.54, 0.76301, 5, -1.88, -10.7, 0.14818, 17, 18.35, 11.78, 0.0888, 3, 4, 26.73, -15.29, 0.58123, 5, -8.06, -15.6, 0.00057, 17, 12.06, 7.03, 0.4182, 2, 4, 21.42, -24.01, 0.10183, 17, 6.74, -1.68, 0.89817, 2, 4, 16.23, -23.68, 0.01464, 17, 1.55, -1.36, 0.98536, 2, 3, 51.84, -22.8, 0.02735, 17, -1.36, -1.71, 0.97265, 2, 3, 45.86, -28.11, 0.18479, 17, -7.68, -6.61, 0.81521, 2, 3, 39.9, -29.7, 0.30073, 17, -13.74, -7.8, 0.69927, 2, 3, 32.58, -28.7, 0.46751, 17, -20.97, -6.32, 0.53249, 2, 3, 24.06, -25.13, 0.70577, 17, -29.23, -2.19, 0.29423, 2, 3, 11.41, -21.99, 0.90814, 17, -41.65, 1.79, 0.09186, 2, 3, 6.35, -18.55, 0.94003, 17, -46.47, 5.55, 0.05997, 2, 3, 5.63, -11.72, 0.96607, 17, -46.73, 12.42, 0.03393, 2, 3, 6.56, -0.92, 0.9993, 17, -45.08, 23.13, 0.0007, 3, 3, 9.83, 12.66, 0.95909, 4, -26.24, 14.14, 0.01996, 12, -51.82, -10.92, 0.02096, 3, 3, 16.27, 21.56, 0.82837, 4, -19.22, 22.59, 0.09431, 12, -44.8, -2.46, 0.07731, 3, 3, 21.11, 22.88, 0.74427, 4, -14.3, 23.58, 0.14107, 12, -39.88, -1.47, 0.11465, 3, 3, 24.2, 22.14, 0.63808, 4, -11.27, 22.64, 0.19682, 12, -36.85, -2.42, 0.1651, 3, 3, 29.21, 27.22, 0.37083, 4, -5.94, 27.38, 0.29142, 12, -31.51, 2.32, 0.33775, 3, 3, 37.74, 32.94, 0.15876, 4, 2.96, 32.52, 0.26119, 12, -22.62, 7.47, 0.58006, 3, 3, 49.41, 36.73, 0.03881, 4, 14.86, 35.52, 0.10185, 12, -10.72, 10.47, 0.85934, 3, 3, 65.06, 35.38, 4e-05, 5, -5.55, 32.9, 0.00293, 12, 4.8, 8.08, 0.99703, 3, 4, 35.9, 25.71, 0.00474, 5, 0.15, 25.6, 0.08575, 12, 10.33, 0.65, 0.90951, 3, 4, 35.95, 19.82, 0.05606, 5, 0.33, 19.72, 0.25636, 12, 10.37, -5.23, 0.68758, 3, 4, 37.33, 16.43, 0.07943, 5, 1.8, 16.37, 0.4695, 12, 11.76, -8.62, 0.45107, 3, 4, 43.33, -0.37, 0.00132, 5, 8.18, -0.3, 0.99868, 17, 28.65, 21.95, 0, 2, 4, 34.22, -0.72, 0.99877, 17, 19.54, 21.6, 0.00123, 2, 4, 22.86, -4.37, 0.92029, 17, 8.18, 17.95, 0.07971, 3, 3, 48.87, -9.78, 0.04285, 4, 11.22, -10.84, 0.55083, 17, -3.46, 11.48, 0.40632, 3, 3, 39, -12.71, 0.37138, 4, 1.18, -13.11, 0.25335, 17, -13.5, 9.21, 0.37527, 3, 3, 25.92, -9.06, 0.89748, 4, -11.63, -8.6, 0.00442, 17, -26.31, 13.73, 0.0981, 2, 3, 13.39, -8.13, 0.97228, 17, -38.75, 15.48, 0.02772, 3, 3, 55.38, -14.59, 0.0018, 4, 17.4, -16.08, 0.30416, 17, 2.72, 6.24, 0.69403, 3, 3, 48.58, -16.69, 0.06855, 4, 10.48, -17.72, 0.1698, 17, -4.2, 4.61, 0.76166, 3, 3, 40.76, -21.53, 0.28283, 4, 2.35, -22.03, 0.04609, 17, -12.33, 0.3, 0.67108, 3, 3, 32.23, -21.74, 0.5373, 4, -6.17, -21.67, 0.01621, 17, -20.85, 0.65, 0.44649, 3, 3, 23.35, -18.76, 0.78372, 4, -14.84, -18.11, 0.00078, 17, -29.52, 4.21, 0.2155, 2, 3, 11.49, -15.53, 0.93514, 17, -41.13, 8.22, 0.06486, 3, 3, 16.35, 7.84, 0.96636, 4, -20.06, 8.9, 0.01796, 12, -45.63, -16.16, 0.01568, 3, 3, 26.24, 9.2, 0.81357, 4, -10.1, 9.59, 0.13335, 12, -35.68, -15.46, 0.05308, 3, 3, 37.64, 13.57, 0.25771, 4, 1.57, 13.2, 0.5445, 12, -24, -11.86, 0.19779, 4, 3, 49.18, 13.99, 0.02392, 4, 13.12, 12.85, 0.65422, 5, -22.33, 12.22, 0.00093, 12, -12.46, -12.2, 0.32092, 4, 3, 60.78, 20.55, 0.00038, 4, 25.12, 18.63, 0.20687, 5, -10.46, 18.28, 0.06801, 12, -0.45, -6.43, 0.72474, 3, 3, 49.35, 25.01, 0.04493, 4, 14.01, 23.83, 0.25182, 12, -11.57, -1.22, 0.70325, 3, 3, 37.16, 22.59, 0.23968, 4, 1.69, 22.23, 0.36196, 12, -23.89, -2.82, 0.39836, 3, 3, 24.93, 15.93, 0.70826, 4, -10.96, 16.4, 0.17645, 12, -36.54, -8.66, 0.11529, 3, 3, 22.04, 15.39, 0.79197, 4, -13.87, 16.05, 0.12294, 12, -39.45, -9.01, 0.08509, 3, 3, 15.45, 13.91, 0.91353, 4, -20.55, 15.01, 0.04682, 12, -46.12, -10.04, 0.03965, 3, 3, 38.15, 1.23, 0.001, 4, 1.26, 0.86, 0.99633, 12, -24.31, -24.2, 0.00267, 3, 3, 26.16, 1.01, 0.99402, 4, -10.72, 1.43, 0.00449, 12, -36.3, -23.63, 0.00149, 3, 3, 13.35, 0.54, 0.99976, 4, -23.53, 1.81, 6e-05, 12, -49.11, -23.25, 0.00018, 2, 4, 13.12, 2.53, 0.97427, 12, -12.46, -22.53, 0.02573, 3, 4, 23.7, 6.3, 0.82636, 5, -11.6, 5.92, 0.03647, 12, -1.87, -18.76, 0.13717, 3, 4, 33.12, 8.66, 0.30192, 5, -2.24, 8.5, 0.50598, 12, 7.54, -16.39, 0.19211, 3, 4, 40.1, 7.59, 0.00495, 5, 4.77, 7.59, 0.91315, 12, 14.53, -17.47, 0.08191, 2, 5, 11.56, 6.23, 0.98258, 12, 21.28, -18.98, 0.01742], "hull": 30, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 0, 58, 6, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 14, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 36, 86, 86, 38, 86, 88, 88, 90, 90, 92, 92, 94, 94, 58, 94, 52, 96, 98, 98, 100, 100, 102, 102, 104, 106, 108, 108, 110, 106, 112, 112, 114, 114, 116, 116, 118, 118, 120], "width": 67, "height": 83}}, "yaodai": {"yaodai": {"x": -6.63, "y": 11.48, "rotation": -0.7, "width": 48, "height": 51}}, "liuh2": {"liuh2": {"type": "mesh", "uvs": [0.94016, 0.40687, 0.98836, 0.32598, 0.91825, 0.15995, 0.78239, 0.08332, 0.61586, 0, 0.44784, 0.01131, 0.32663, 0.01947, 0.19078, 0.15144, 0.05931, 0.25361, 0.05054, 0.46647, 0.01548, 0.62824, 0.00672, 0.81555, 0.16448, 0.74318, 0.25651, 0.87941, 0.37922, 0.98158, 0.53698, 1, 0.54575, 0.86238, 0.67722, 0.79852, 0.62463, 0.61121, 0.63778, 0.44092, 0.65092, 0.34301, 0.79554, 0.35152, 0.79116, 0.21104, 0.57642, 0.16847, 0.41428, 0.24935, 0.36169, 0.52607, 0.3836, 0.79852], "triangles": [16, 15, 26, 15, 14, 26, 14, 13, 26, 13, 12, 26, 17, 16, 18, 11, 10, 12, 12, 25, 26, 16, 26, 18, 26, 25, 18, 10, 9, 12, 12, 9, 25, 18, 25, 19, 9, 8, 25, 25, 8, 24, 25, 24, 19, 24, 8, 7, 19, 24, 20, 24, 23, 20, 7, 6, 24, 24, 5, 23, 24, 6, 5, 0, 21, 1, 20, 22, 21, 21, 22, 1, 20, 23, 22, 22, 2, 1, 23, 3, 22, 22, 3, 2, 23, 4, 3, 23, 5, 4], "vertices": [1, 8, -7.33, 3.38, 1, 1, 8, -7.43, 0.11, 1, 1, 8, -2.57, -3.87, 1, 1, 8, 2.77, -4.04, 1, 1, 8, 9.14, -3.91, 1, 2, 8, 13.98, -0.84, 0.58599, 9, -2.74, -2.48, 0.41401, 2, 8, 17.47, 1.37, 0.08548, 9, -0.91, -6.18, 0.91452, 1, 9, 5.12, -8.7, 1, 2, 9, 10.13, -11.48, 0.99018, 10, -10.45, -7.83, 0.00982, 2, 9, 17.13, -8.92, 0.63922, 10, -3.08, -8.95, 0.36078, 2, 9, 22.82, -7.87, 0.16304, 10, 2.41, -10.77, 0.83696, 2, 9, 29, -5.66, 0.01355, 10, 8.89, -11.79, 0.98645, 2, 9, 24.62, -1.66, 0.01855, 10, 6.97, -6.18, 0.98145, 1, 10, 12.06, -3.6, 1, 1, 10, 16.08, 0.15, 1, 1, 10, 17.31, 5.41, 1, 2, 9, 23.55, 11.92, 0.00378, 10, 12.56, 6.24, 0.99622, 2, 9, 19.78, 15.2, 0.02584, 10, 10.84, 10.93, 0.97416, 3, 8, -1.3, 14.77, 0.0177, 9, 14.4, 11.06, 0.21817, 10, 4.12, 9.88, 0.76414, 3, 8, 1.15, 9.31, 0.17595, 9, 8.71, 9.21, 0.5561, 10, -1.75, 10.99, 0.26795, 3, 8, 2.38, 6.09, 0.55055, 9, 5.37, 8.32, 0.38945, 10, -5.11, 11.81, 0.06001, 3, 8, -2.09, 4.01, 0.97403, 9, 3.78, 12.98, 0.02441, 10, -4.26, 16.67, 0.00155, 1, 8, 0.38, -0.24, 1, 3, 8, 7.52, 1.92, 0.72665, 9, 0.69, 3.65, 0.27251, 10, -11.46, 9.98, 0.00085, 1, 9, 5.4, -0.37, 1, 3, 8, 7.99, 16.4, 0.00108, 9, 15.04, 1.66, 0.32394, 10, 0.17, 1.33, 0.67499, 2, 9, 23.58, 5.97, 0.00019, 10, 9.73, 1.01, 0.99981], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 0, 42, 2, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 30], "width": 34, "height": 35}}, "liuh1": {"liuh1": {"x": -17.48, "y": -13.7, "rotation": -92.05, "width": 25, "height": 47}}, "tfs1": {"tfs1": {"type": "mesh", "uvs": [0.15127, 0, 0.35527, 0.01708, 0.42127, 0.13487, 0.46327, 0.26738, 0.60127, 0.38701, 0.81127, 0.52689, 0.99727, 0.68149, 1, 0.80112, 1, 0.90419, 0.76327, 0.98517, 0.27127, 1, 0, 0.9226, 0, 0.75143, 0.06727, 0.59499, 0.06127, 0.44591, 0.00127, 0.28395, 0, 0.12935, 0.00127, 0.041, 0.18127, 0.12751, 0.21727, 0.27658, 0.32527, 0.41278, 0.42127, 0.57843, 0.54727, 0.73671, 0.56527, 0.88947], "triangles": [10, 23, 9, 10, 11, 23, 9, 23, 8, 23, 12, 22, 22, 12, 13, 12, 23, 11, 23, 7, 8, 23, 22, 7, 22, 6, 7, 22, 21, 6, 13, 21, 22, 21, 5, 6, 13, 14, 21, 14, 20, 21, 5, 20, 4, 5, 21, 20, 20, 14, 15, 20, 15, 19, 20, 3, 4, 20, 19, 3, 19, 16, 18, 19, 15, 16, 19, 2, 3, 19, 18, 2, 18, 1, 2, 16, 17, 18, 17, 0, 18, 18, 0, 1], "vertices": [1, 11, -22.79, 5.56, 1, 1, 11, -18.19, 15.08, 1, 1, 11, 1.29, 14.81, 1, 1, 11, 22.91, 12.92, 1, 2, 11, 43.34, 16.14, 0.6475, 44, 2.28, 16.29, 0.3525, 3, 11, 67.68, 22.29, 0.02762, 44, 26.85, 21.47, 0.94567, 45, -13, 22.37, 0.02671, 2, 44, 53.48, 24.94, 0.218, 45, 13.79, 24.39, 0.782, 2, 44, 72.53, 20.74, 0.00727, 45, 32.58, 19.16, 0.99273, 1, 45, 48.73, 14.54, 1, 1, 45, 58.17, -0.46, 1, 1, 45, 53.73, -24.78, 1, 1, 45, 37.87, -34.35, 1, 2, 44, 53.52, -26.21, 0.09178, 45, 11.05, -26.69, 0.90822, 2, 44, 29.41, -17.27, 0.90941, 45, -12.55, -16.44, 0.09059, 2, 11, 47.84, -12.16, 0.00166, 44, 5.65, -12.16, 0.99834, 2, 11, 21.33, -10.28, 0.982, 44, -20.76, -9.23, 0.018, 1, 11, -3.45, -5.73, 1, 1, 11, -17.6, -3.04, 1, 1, 11, -2.09, 3.23, 1, 1, 11, 22.13, 0.56, 1, 2, 11, 44.94, 1.8, 0.15066, 44, 3.31, 1.91, 0.84934, 2, 11, 72.37, 1.58, 0.00013, 44, 30.71, 0.59, 0.99987, 2, 44, 57.26, 1, 0.00034, 45, 16.26, 0.28, 0.99966, 1, 45, 40.45, -5.7, 1], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 0, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46], "width": 50, "height": 163}}, "ss1": {"ss1": {"type": "mesh", "uvs": [0.86557, 0.00153, 1, 0.04509, 1, 0.15253, 0.97167, 0.25126, 0.90638, 0.33402, 0.89005, 0.40661, 0.82884, 0.46033, 0.72681, 0.47195, 0.76762, 0.5068, 0.8574, 0.5823, 0.988, 0.65489, 1, 0.71297, 0.91862, 0.76669, 0.84516, 0.80008, 0.90157, 0.84823, 0.92805, 0.89911, 0.94394, 0.97261, 0.78503, 1, 0.51486, 1, 0.36124, 0.96507, 0.36654, 0.8878, 0.31357, 0.82938, 0.17054, 0.81242, 0.07519, 0.7653, 0.12816, 0.67484, 0.11757, 0.58815, 0.01692, 0.52596, 0, 0.47507, 0.08049, 0.41477, 0.02751, 0.35823, 0.20232, 0.30169, 0.2447, 0.24704, 0.2553, 0.16223, 0.16524, 0.05292, 0.21292, 0.01711, 0.39303, 0.019, 0.47778, 0.06234, 0.5093, 0.09496, 0.56373, 0.03075, 0.67546, 0, 0.79865, 0.06642, 0.6726, 0.19179, 0.56373, 0.30085, 0.4606, 0.39156, 0.42049, 0.47106, 0.452, 0.56279, 0.50643, 0.67083, 0.54499, 0.75222, 0.58618, 0.8145, 0.61707, 0.90608], "triangles": [18, 49, 17, 17, 15, 16, 17, 49, 15, 18, 19, 49, 19, 20, 49, 49, 14, 15, 14, 48, 13, 14, 49, 48, 20, 48, 49, 20, 21, 48, 48, 21, 47, 21, 22, 47, 48, 47, 13, 22, 23, 47, 47, 24, 46, 24, 47, 23, 13, 47, 12, 12, 47, 11, 47, 46, 11, 46, 10, 11, 24, 25, 46, 25, 45, 46, 46, 9, 10, 46, 45, 9, 45, 25, 44, 45, 8, 9, 25, 26, 44, 45, 7, 8, 45, 44, 7, 44, 27, 28, 44, 26, 27, 5, 6, 7, 7, 44, 43, 44, 28, 43, 5, 7, 43, 28, 30, 43, 28, 29, 30, 43, 42, 5, 5, 42, 4, 43, 30, 42, 4, 42, 3, 30, 31, 42, 42, 41, 3, 42, 31, 41, 3, 41, 2, 31, 32, 41, 32, 37, 41, 41, 40, 2, 41, 37, 40, 32, 33, 36, 32, 36, 37, 35, 33, 34, 33, 35, 36, 40, 1, 2, 37, 38, 40, 38, 39, 40, 40, 0, 1, 40, 39, 0], "vertices": [1, 13, -11.64, -0.63, 1, 1, 13, -8.81, 5.47, 1, 1, 13, 1.85, 8.82, 1, 1, 13, 11.96, 10.89, 1, 1, 13, 20.9, 11.16, 1, 2, 13, 28.28, 12.85, 0.96331, 14, -3.32, 15.53, 0.03669, 2, 13, 34.29, 12.36, 0.82292, 14, 1.98, 12.65, 0.17708, 2, 13, 36.57, 9.12, 0.52552, 14, 2.75, 8.76, 0.47448, 2, 13, 39.58, 11.65, 0.14228, 14, 6.52, 9.85, 0.85772, 3, 13, 46.08, 17.17, 0.00093, 14, 14.7, 12.26, 0.99754, 15, -25.44, 12.28, 0.00153, 2, 14, 22.75, 16.2, 0.96753, 15, -17.31, 16.05, 0.03247, 2, 14, 28.8, 15.96, 0.9145, 15, -11.26, 15.69, 0.0855, 2, 14, 34.01, 12.33, 0.76981, 15, -6.13, 11.95, 0.23019, 2, 14, 37.15, 9.24, 0.44729, 15, -3.05, 8.79, 0.55271, 2, 14, 42.36, 10.74, 0.03872, 15, 2.19, 10.19, 0.96128, 1, 15, 7.56, 10.45, 1, 2, 14, 55.39, 10.83, 9e-05, 15, 15.22, 10.01, 0.99991, 2, 14, 57.55, 4.66, 0.00019, 15, 17.25, 3.8, 0.99981, 2, 14, 56.42, -5.27, 0.05095, 15, 15.91, -6.1, 0.94905, 2, 14, 52.16, -10.5, 0.19341, 15, 11.55, -11.25, 0.80659, 2, 14, 44.2, -9.39, 0.64867, 15, 3.62, -9.98, 0.35133, 2, 14, 37.94, -10.65, 0.98834, 15, -2.67, -11.11, 0.01166, 2, 14, 35.59, -15.71, 1, 15, -5.13, -16.11, 0, 2, 14, 30.32, -18.66, 1, 15, -10.45, -18.95, 0, 1, 14, 21.2, -15.64, 1, 2, 13, 54.86, -8.77, 0.01759, 14, 12.19, -15.01, 0.98241, 2, 13, 49.8, -14.26, 0.13829, 14, 5.34, -17.97, 0.86171, 2, 13, 44.94, -16.44, 0.27307, 14, 0.02, -17.99, 0.72693, 2, 13, 38.06, -15.48, 0.62289, 14, -5.88, -14.32, 0.37711, 2, 13, 33.04, -19.11, 0.84259, 14, -11.94, -15.6, 0.15741, 2, 13, 25.49, -14.7, 0.97412, 14, -17.05, -8.51, 0.02588, 2, 13, 19.6, -14.9, 0.9998, 14, -22.52, -6.3, 0.0002, 1, 13, 11.07, -17.17, 1, 1, 13, 1.22, -23.75, 1, 1, 13, -2.86, -23.19, 1, 1, 13, -4.67, -16.77, 1, 1, 13, -1.31, -12.43, 1, 1, 13, 1.58, -10.3, 1, 1, 13, -5.4, -10.38, 1, 1, 13, -9.69, -7.39, 1, 1, 13, -4.46, -0.97, 1, 1, 13, 9.38, -1.52, 1, 1, 13, 21.4, -1.96, 1, 2, 13, 31.55, -2.78, 0.99871, 14, -6.68, -0.08, 0.00129, 2, 13, 39.88, -1.72, 0.16963, 14, 1.37, -2.49, 0.83037, 1, 14, 10.98, -2.41, 1, 1, 14, 22.37, -1.69, 1, 1, 14, 30.94, -1.23, 1, 1, 14, 37.55, -0.45, 1, 2, 14, 47.14, -0.4, 0.0403, 15, 6.74, -1.05, 0.9597], "hull": 40, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 0, 78, 0, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98], "width": 37, "height": 104}}, "ss2": {"ss2": {"type": "mesh", "uvs": [0.11581, 0.01092, 0.26662, 0.00894, 0.36392, 0.01389, 0.57311, 0.01982, 0.66554, 0.06037, 0.64365, 0.1207, 0.64365, 0.21268, 0.68257, 0.28883, 0.82122, 0.38081, 0.72635, 0.45103, 0.7896, 0.53609, 0.86743, 0.6162, 1, 0.71006, 0.95871, 0.78113, 0.87546, 0.82006, 0.94622, 0.8776, 0.95038, 0.94529, 0.8713, 1, 0.72563, 1, 0.55498, 1, 0.41763, 0.9656, 0.37184, 0.89621, 0.38433, 0.84713, 0.2553, 0.80821, 0.10547, 0.75406, 0.1679, 0.65929, 0.19703, 0.56452, 0.17206, 0.48329, 0.13044, 0.38683, 0.09714, 0.30898, 0.03887, 0.24637, 0.01806, 0.17698, 0, 0.10083, 0, 0.04329, 0.20535, 0.08052, 0.28443, 0.19221, 0.34686, 0.31406, 0.42594, 0.43929, 0.48838, 0.56452, 0.52584, 0.69144, 0.57578, 0.79636, 0.61324, 0.86067, 0.65902, 0.92667], "triangles": [16, 17, 42, 19, 42, 18, 17, 18, 42, 19, 20, 42, 20, 21, 42, 42, 15, 16, 21, 41, 42, 15, 42, 41, 15, 41, 14, 21, 22, 41, 22, 40, 41, 41, 40, 14, 22, 23, 40, 14, 40, 13, 24, 25, 23, 23, 25, 39, 23, 39, 40, 38, 25, 26, 25, 38, 39, 40, 39, 13, 13, 39, 12, 12, 39, 11, 11, 38, 10, 11, 39, 38, 26, 27, 38, 27, 37, 38, 38, 9, 10, 38, 37, 9, 27, 28, 37, 9, 37, 8, 28, 36, 37, 37, 7, 8, 37, 36, 7, 28, 29, 36, 29, 35, 36, 36, 6, 7, 36, 35, 6, 29, 30, 35, 30, 31, 35, 35, 5, 6, 5, 2, 3, 2, 5, 34, 31, 34, 35, 5, 35, 34, 2, 34, 1, 31, 32, 34, 5, 3, 4, 32, 33, 34, 33, 0, 34, 34, 0, 1], "vertices": [1, 18, -8.59, -3.85, 1, 1, 18, -7.48, 1.62, 1, 1, 18, -6.21, 5.02, 1, 1, 18, -3.9, 12.42, 1, 1, 18, 0.49, 14.9, 1, 1, 18, 5.64, 12.84, 1, 1, 18, 13.78, 10.91, 1, 1, 18, 20.86, 10.71, 1, 1, 18, 30.19, 13.76, 1, 2, 18, 35.59, 8.87, 0.89002, 19, 0.66, 9.02, 0.10998, 2, 18, 43.66, 9.36, 0.22296, 19, 8.72, 9.66, 0.77704, 2, 18, 51.42, 10.47, 0.01685, 19, 16.45, 10.92, 0.98315, 1, 19, 25.84, 13.89, 1, 2, 19, 31.84, 11.02, 0.99464, 20, -4.29, 10.94, 0.00536, 2, 19, 34.64, 7.26, 0.81042, 20, -1.38, 7.26, 0.18958, 2, 19, 40.32, 8.7, 0.17564, 20, 4.25, 8.85, 0.82436, 2, 19, 46.37, 7.54, 0.00745, 20, 10.33, 7.86, 0.99255, 1, 20, 14.68, 4.06, 1, 2, 19, 49.46, -1.65, 0.00226, 20, 13.67, -1.24, 0.99774, 2, 19, 48.12, -7.82, 0.08308, 20, 12.5, -7.44, 0.91692, 2, 19, 43.97, -12.12, 0.26138, 20, 8.47, -11.85, 0.73862, 2, 19, 37.44, -12.43, 0.59508, 20, 1.96, -12.34, 0.40492, 2, 19, 33.18, -11.02, 0.89345, 20, -2.35, -11.06, 0.10655, 2, 19, 28.7, -14.93, 0.9995, 20, -6.72, -15.09, 0.0005, 1, 19, 22.71, -19.3, 1, 1, 19, 14.77, -15.2, 1, 1, 19, 6.58, -12.31, 1, 2, 18, 33.71, -11.76, 0.0794, 19, -0.84, -11.64, 0.9206, 2, 18, 24.81, -11.23, 0.67311, 19, -9.75, -11.28, 0.32689, 2, 18, 17.63, -10.79, 0.94461, 19, -16.93, -10.97, 0.05539, 2, 18, 11.59, -11.57, 0.9952, 19, -22.96, -11.86, 0.0048, 1, 18, 5.27, -10.86, 1, 1, 18, -1.63, -9.91, 1, 1, 18, -6.72, -8.7, 1, 1, 18, -1.67, -2.09, 1, 1, 18, 8.9, -1.59, 1, 2, 18, 20.22, -1.91, 0.98809, 19, -14.51, -2.04, 0.01191, 2, 18, 31.98, -1.7, 0.57899, 19, -2.75, -1.61, 0.42101, 1, 19, 8.87, -1.78, 1, 1, 19, 20.45, -2.89, 1, 1, 19, 30.17, -3.12, 1, 2, 19, 36.19, -3.01, 0.61113, 20, 0.44, -2.96, 0.38887, 2, 19, 42.42, -2.63, 0.03981, 20, 6.66, -2.42, 0.96019], "hull": 34, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 0, 66, 0, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84], "width": 37, "height": 91}}, "tfs3": {"tfs2": {"type": "mesh", "uvs": [0.81252, 0, 1, 0.02268, 1, 0.1366, 1, 0.31508, 0.93055, 0.47458, 0.81252, 0.63787, 0.68465, 0.78027, 0.71416, 0.8828, 0.77809, 0.96635, 0.53219, 0.99483, 0.33547, 1, 0.0994, 0.96255, 0, 0.91318, 0.06498, 0.79356, 0.07973, 0.63407, 0.18301, 0.50116, 0.3994, 0.35306, 0.55678, 0.23344, 0.63055, 0.11572, 0.6453, 0.02268, 0.81251, 0.1366, 0.77809, 0.27331, 0.66006, 0.4347, 0.5371, 0.5885, 0.42891, 0.71951, 0.36497, 0.85052], "triangles": [9, 10, 25, 10, 11, 25, 9, 7, 8, 9, 25, 7, 11, 12, 25, 12, 13, 25, 25, 6, 7, 25, 24, 6, 25, 13, 24, 13, 14, 24, 6, 24, 5, 24, 14, 23, 24, 23, 5, 5, 23, 4, 14, 15, 23, 23, 22, 4, 23, 15, 22, 15, 16, 22, 4, 22, 3, 22, 16, 21, 22, 21, 3, 21, 16, 17, 21, 2, 3, 21, 20, 2, 21, 17, 20, 17, 18, 20, 20, 1, 2, 18, 19, 20, 20, 0, 1, 20, 19, 0], "vertices": [1, 41, -17.16, -9.58, 1, 1, 41, -17.14, 2.41, 1, 1, 41, 0.05, 7.76, 1, 2, 41, 26.97, 16.14, 0.99002, 42, -14.07, 16.95, 0.00998, 2, 41, 52.29, 19.59, 0.15955, 42, 11.37, 19.39, 0.84045, 2, 42, 38.15, 19.12, 0.68699, 43, -1.84, 19.41, 0.31301, 2, 42, 61.9, 17.41, 0.0125, 43, 21.79, 16.41, 0.9875, 1, 43, 37.27, 21.51, 1, 1, 43, 49.38, 28.05, 1, 1, 43, 56.88, 14.3, 1, 1, 43, 60.15, 2.72, 1, 1, 43, 57.32, -12.59, 1, 1, 43, 50.94, -20.13, 1, 1, 43, 31.63, -20.14, 1, 2, 42, 49.15, -24.21, 0.19645, 43, 6.79, -24.46, 0.80355, 2, 42, 27.23, -23.57, 0.88125, 43, -15.06, -22.62, 0.11875, 2, 41, 43.59, -17.05, 0.16245, 42, 1.21, -16.88, 0.83755, 2, 41, 22.69, -13.51, 0.96703, 42, -19.53, -12.5, 0.03297, 1, 41, 3.6, -14.74, 1, 1, 41, -10.71, -18.25, 1, 1, 41, 3.45, -3.16, 1, 1, 41, 24.69, 1.26, 1, 2, 41, 51.18, 1.96, 0.00962, 42, 9.55, 1.82, 0.99038, 1, 42, 34.97, 0.87, 1, 1, 43, 15.61, -0.83, 1, 1, 43, 36.67, -0.38, 1], "hull": 20, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 0, 38, 0, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50], "width": 61, "height": 158}}, "tou": {"tou": {"x": 43.41, "y": -4.23, "rotation": -97.41, "width": 72, "height": 86}}, "wuqi": {"wuqi": {"x": 54.74, "y": 1.71, "rotation": -35.56, "width": 165, "height": 120}}, "ts2": {"ts2": {"x": 72.14, "y": -39.23, "rotation": -97.41, "width": 31, "height": 43}}, "jio1": {"jio1": {"type": "mesh", "uvs": [0.55369, 0.00309, 0.74944, 0.0215, 0.98231, 0.06292, 1, 0.1519, 1, 0.24701, 0.94181, 0.33752, 0.84056, 0.4334, 0.75917, 0.52051, 0.72357, 0.62973, 0.61677, 0.72076, 0.55002, 0.79863, 0.55892, 0.85122, 0.77701, 0.90747, 0.89258, 0.94972, 0.87751, 0.99426, 0.71168, 1, 0.43531, 0.99883, 0.25943, 0.96457, 0.11371, 0.92688, 0, 0.89605, 0.01321, 0.82867, 0.02326, 0.767, 0.00818, 0.68934, 0, 0.61967, 0.01823, 0.55572, 0.07351, 0.52146, 0.04838, 0.44723, 0.04838, 0.38784, 0, 0.29419, 0, 0.18227, 0.03833, 0.09205, 0.20416, 0.03495, 0.38506, 0.00411, 0.54586, 0.11032, 0.52073, 0.23366, 0.47048, 0.33873, 0.43028, 0.45179, 0.38506, 0.55343, 0.3348, 0.66193, 0.29963, 0.74872, 0.27953, 0.84237, 0.34485, 0.89833, 0.56093, 0.94858], "triangles": [18, 19, 41, 19, 40, 41, 41, 40, 11, 19, 20, 40, 20, 21, 40, 13, 14, 15, 15, 16, 42, 16, 17, 42, 13, 15, 42, 17, 41, 42, 17, 18, 41, 42, 12, 13, 12, 42, 11, 42, 41, 11, 40, 10, 11, 40, 39, 10, 40, 21, 39, 10, 39, 9, 21, 22, 39, 39, 38, 9, 39, 22, 38, 9, 38, 8, 22, 23, 38, 38, 23, 37, 38, 37, 8, 37, 23, 24, 8, 37, 7, 24, 25, 37, 37, 36, 7, 37, 25, 36, 25, 26, 36, 7, 36, 6, 6, 36, 35, 35, 36, 27, 36, 26, 27, 6, 35, 5, 27, 28, 35, 35, 34, 5, 35, 28, 34, 5, 34, 4, 28, 29, 34, 34, 3, 4, 34, 33, 3, 34, 29, 33, 29, 30, 33, 33, 2, 3, 30, 31, 33, 31, 32, 33, 33, 1, 2, 33, 0, 1, 33, 32, 0], "vertices": [1, 29, -3.41, 14.65, 1, 1, 29, -0.99, 17.51, 1, 1, 29, 3.01, 22.53, 1, 1, 29, 10.65, 29.31, 1, 1, 29, 27.39, 28.97, 1, 1, 29, 43.27, 26.32, 1, 2, 29, 60.06, 21.94, 0.94613, 30, -13.86, 21.81, 0.05387, 2, 29, 75.32, 18.37, 0.44996, 30, 1.36, 18.39, 0.55004, 2, 29, 94.51, 16.56, 0.00237, 30, 20.47, 16.75, 0.99763, 1, 30, 36.36, 12.31, 1, 2, 30, 49.97, 9.49, 0.99264, 31, -1.2, 11.75, 0.00736, 2, 30, 59.18, 9.74, 0.36836, 31, 7.02, 7.58, 0.63164, 2, 30, 69.12, 18.36, 0.00059, 31, 19.87, 10.41, 0.99941, 1, 31, 28.58, 10.85, 1, 1, 31, 35.1, 6.52, 1, 1, 31, 32.75, 0.24, 1, 1, 31, 27.19, -9.32, 1, 1, 31, 18.53, -12.54, 1, 1, 31, 9.93, -14.43, 1, 2, 30, 66.79, -12.7, 0.00881, 31, 3, -15.77, 0.99119, 2, 30, 54.99, -12.04, 0.44547, 31, -7.05, -9.57, 0.55453, 2, 30, 44.2, -11.52, 0.94659, 31, -16.29, -3.96, 0.05341, 1, 30, 30.6, -11.97, 1, 1, 30, 18.39, -12.17, 1, 2, 29, 80.92, -11.38, 0.0221, 30, 7.2, -11.32, 0.9779, 2, 29, 74.94, -9.05, 0.32678, 30, 1.23, -9.04, 0.67322, 2, 29, 61.86, -9.79, 0.99848, 30, -11.78, -9.9, 0.00152, 1, 29, 51.41, -9.58, 1, 1, 29, 34.89, -11.19, 1, 1, 29, 14.61, -7.97, 1, 1, 29, 2.32, -2.78, 1, 1, 29, 1.51, 1.82, 1, 1, 29, -1.43, 8.25, 1, 1, 29, 2.97, 11.29, 1, 1, 29, 24.66, 9.85, 1, 1, 29, 43.1, 7.47, 1, 2, 29, 62.97, 5.46, 0.97738, 30, -10.82, 5.36, 0.02262, 2, 29, 80.82, 3.29, 0.00918, 30, 6.96, 3.36, 0.99082, 1, 30, 25.94, 1.14, 1, 2, 30, 41.12, -0.43, 0.99992, 31, -13.71, 7.25, 8e-05, 1, 31, 0.23, -1.43, 1, 1, 31, 10.06, -3.92, 1, 1, 31, 21.95, -0.65, 1], "hull": 33, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 0, 64, 0, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84], "width": 40, "height": 176}}, "jio2": {"jio2": {"type": "mesh", "uvs": [0.3507, 0, 0.54008, 0.02257, 0.7024, 0.13329, 0.68888, 0.23219, 0.64379, 0.37981, 0.61222, 0.47576, 0.61673, 0.54514, 0.58968, 0.60862, 0.54459, 0.70309, 0.48597, 0.80938, 0.4995, 0.84628, 0.62124, 0.89352, 0.76102, 0.90238, 0.9504, 0.92452, 0.99099, 0.96733, 0.8512, 0.99981, 0.61673, 0.99981, 0.48146, 0.98209, 0.24248, 0.97914, 0.10721, 0.93781, 0.14779, 0.87286, 0.13877, 0.802, 0.07564, 0.709, 0.0531, 0.63814, 0.09819, 0.57319, 0.0531, 0.51267, 0.02474, 0.41018, 0, 0.32076, 0, 0.20709, 0, 0.08752, 0.11171, 0.02109, 0.32364, 0.09638, 0.34168, 0.21005, 0.33266, 0.33552, 0.34168, 0.46395, 0.34619, 0.58205, 0.33266, 0.67357, 0.31913, 0.76657, 0.31462, 0.86695, 0.36422, 0.926, 0.56262, 0.93633, 0.77455, 0.95552], "triangles": [18, 39, 17, 18, 19, 39, 20, 39, 19, 40, 39, 11, 39, 10, 11, 16, 41, 15, 15, 41, 14, 17, 40, 16, 16, 40, 41, 17, 39, 40, 41, 13, 14, 41, 40, 12, 40, 11, 12, 41, 12, 13, 39, 20, 38, 39, 38, 10, 20, 21, 38, 38, 9, 10, 38, 37, 9, 38, 21, 37, 9, 37, 8, 21, 22, 37, 37, 36, 8, 37, 22, 36, 22, 23, 36, 8, 36, 7, 36, 23, 35, 36, 35, 7, 35, 23, 24, 7, 35, 6, 35, 24, 25, 35, 25, 34, 6, 34, 5, 6, 35, 34, 25, 26, 34, 5, 34, 4, 26, 33, 34, 34, 33, 4, 26, 27, 33, 4, 33, 3, 33, 32, 3, 33, 27, 32, 27, 28, 32, 3, 32, 2, 28, 31, 32, 32, 31, 2, 28, 29, 31, 31, 1, 2, 29, 30, 31, 31, 0, 1, 31, 30, 0], "vertices": [1, 32, -17.3, 6.63, 1, 1, 32, -12.59, 16.67, 1, 1, 32, 6.73, 23.9, 1, 1, 32, 23.22, 21.67, 1, 1, 32, 47.7, 16.98, 1, 2, 32, 63.6, 13.81, 0.99309, 33, -14.55, 11.63, 0.00691, 2, 32, 75.23, 13.01, 0.64707, 33, -2.93, 12.71, 0.35293, 2, 32, 85.71, 10.57, 0.06289, 33, 7.83, 11.98, 0.93711, 1, 33, 23.86, 10.63, 1, 2, 33, 41.92, 8.69, 0.92611, 34, 2.24, 15.94, 0.07389, 2, 33, 48.06, 9.87, 0.56003, 34, 5.88, 10.85, 0.43997, 2, 33, 55.51, 17.11, 0.02212, 34, 15.57, 7.1, 0.97788, 1, 34, 23.03, 9.49, 1, 1, 34, 33.95, 11.23, 1, 1, 34, 39.37, 5.99, 1, 1, 34, 35.25, -2.49, 1, 1, 34, 23.94, -8.7, 1, 1, 34, 15.99, -9.66, 1, 2, 33, 71.35, -2.65, 0.06755, 34, 4.22, -15.54, 0.93245, 2, 33, 64.95, -10.56, 0.33716, 34, -5.64, -13.02, 0.66284, 2, 33, 53.89, -9.11, 0.91934, 34, -8.94, -2.37, 0.08066, 1, 33, 42.04, -10.45, 1, 1, 33, 26.68, -15.02, 1, 2, 32, 88.01, -19.26, 0.03693, 33, 14.88, -17.11, 0.96307, 2, 32, 77.37, -15.82, 0.39381, 33, 3.81, -15.41, 0.60619, 2, 32, 67.02, -17.38, 0.88236, 33, -6.17, -18.6, 0.11764, 1, 32, 49.73, -17.39, 1, 1, 32, 34.65, -17.4, 1, 1, 32, 15.63, -15.69, 1, 1, 32, -4.38, -13.89, 1, 1, 32, -14.95, -6.78, 1, 1, 32, -1.31, 3.7, 1, 1, 32, 17.8, 2.98, 1, 1, 32, 38.75, 0.6, 1, 1, 32, 60.29, -0.84, 1, 2, 32, 80.07, -2.36, 0.02951, 33, 4.32, -1.7, 0.97049, 1, 33, 19.73, -1.35, 1, 1, 33, 35.39, -0.98, 1, 1, 33, 52.25, -0.03, 1, 1, 34, 5.8, -4.49, 1, 1, 34, 16.2, -0.76, 1, 1, 34, 27.97, 2.01, 1], "hull": 31, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 0, 60, 0, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82], "width": 55, "height": 168}}, "xuanz": {"xuanz": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-104.92, 12.46, -21.22, -104.34, 105.49, -13.54, 21.79, 103.26], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 236, "height": 256}}, "biyan": {"biyan": {"x": 1.32, "y": 5, "rotation": -92.05, "width": 51, "height": 21}}, "erduo": {"erduo": {"x": 32.15, "y": 25.47, "rotation": -97.41, "width": 19, "height": 21}}, "qub1": {"qub1": {"type": "mesh", "uvs": [0.7229, 0, 0.88319, 0.03275, 0.99319, 0.15893, 1, 0.31665, 1, 0.52063, 1, 0.71831, 1, 0.90547, 0.94919, 1, 0.69147, 1, 0.4149, 0.98118, 0.1289, 0.9959, 0, 0.97066, 0.0629, 0.79402, 0.1289, 0.64261, 0.18548, 0.45544, 0.2389, 0.26828, 0.3269, 0.10215, 0.46205, 0, 0.68519, 0.12318, 0.6789, 0.31244, 0.62862, 0.51222, 0.57519, 0.69939, 0.55319, 0.84449, 0.2829, 0.82136, 0.33005, 0.6384, 0.38662, 0.46386, 0.4589, 0.2809, 0.5249, 0.10215, 0.84862, 0.14, 0.87376, 0.32296, 0.84548, 0.52064, 0.8329, 0.7099, 0.80776, 0.86131], "triangles": [21, 24, 20, 23, 13, 24, 23, 24, 21, 12, 13, 23, 22, 23, 21, 9, 23, 22, 10, 12, 23, 10, 23, 9, 11, 12, 10, 14, 15, 25, 19, 25, 26, 20, 25, 19, 24, 14, 25, 24, 25, 20, 13, 14, 24, 27, 17, 0, 16, 17, 27, 26, 16, 27, 15, 16, 26, 18, 26, 27, 19, 26, 18, 25, 15, 26, 21, 20, 31, 5, 31, 4, 32, 21, 31, 22, 21, 32, 32, 31, 5, 32, 5, 6, 8, 22, 32, 9, 22, 8, 7, 32, 6, 8, 32, 7, 30, 20, 19, 29, 30, 19, 4, 30, 29, 31, 20, 30, 31, 30, 4, 18, 27, 0, 18, 0, 1, 28, 18, 1, 28, 1, 2, 19, 18, 28, 29, 28, 2, 29, 2, 3, 19, 28, 29, 29, 3, 4], "vertices": [2, 38, -9.77, -11.51, 0.7065, 35, -9.38, 26.24, 0.2935, 2, 38, -3.98, 2.6, 0.99519, 35, -7.71, 41.4, 0.00481, 1, 38, 14.03, 10.98, 1, 1, 38, 35.45, 9.61, 1, 3, 38, 63.07, 7.04, 0.01122, 39, 22.16, 10.17, 0.98415, 40, -17.83, 9.55, 0.00463, 1, 40, 9.02, 10.97, 1, 1, 40, 34.44, 12.31, 1, 1, 40, 47.52, 8.37, 1, 3, 39, 88.12, -16.06, 0.00422, 40, 48.76, -15.05, 0.81192, 37, 30.6, 53.98, 0.18386, 3, 39, 86.27, -41.29, 0.01043, 40, 47.53, -40.32, 0.2972, 37, 36.31, 29.34, 0.69237, 2, 40, 50.91, -66.2, 0.01003, 37, 46.62, 5.35, 0.98997, 1, 37, 47.17, -6.86, 1, 2, 36, 62.78, -11.85, 0.00439, 37, 22.58, -9.21, 0.99561, 2, 36, 41.4, -10.08, 0.84344, 37, 1.15, -10.18, 0.15656, 2, 35, 60.57, -10.3, 0.10473, 36, 15.43, -10.12, 0.89527, 1, 35, 34.66, -10.25, 1, 1, 35, 10.97, -6.58, 1, 2, 38, -11.97, -35.15, 0.04583, 35, -4.97, 2.92, 0.95417, 4, 38, 6.59, -16.48, 0.60072, 39, -31.07, -19.99, 0.04179, 35, 7.72, 25.99, 0.3296, 36, -37.93, 25.41, 0.02789, 5, 38, 32.17, -19.43, 0.13252, 39, -5.32, -19.84, 0.52483, 35, 33.12, 30.21, 0.09003, 36, -12.59, 29.99, 0.25011, 37, -57.52, 22.67, 0.0025, 4, 39, 21.97, -23.65, 0.58397, 40, -17.18, -24.26, 0.00056, 36, 14.94, 30.94, 0.32168, 37, -30.33, 27.12, 0.09379, 4, 39, 47.55, -27.79, 0.31347, 40, 8.49, -27.77, 0.20836, 36, 40.86, 31.25, 0.05947, 37, -4.67, 30.75, 0.41869, 3, 39, 67.33, -29.23, 0.07446, 40, 28.3, -28.73, 0.44608, 37, 14.65, 35.23, 0.47947, 3, 39, 64.88, -53.91, 0.014, 40, 26.46, -53.46, 0.06757, 37, 19.63, 10.94, 0.91843, 4, 39, 39.89, -50.32, 0.05501, 40, 1.39, -50.49, 0.01524, 36, 37.18, 7.74, 0.2684, 37, -5.3, 6.96, 0.66136, 3, 39, 16.02, -45.84, 0.10055, 36, 12.9, 8.04, 0.86678, 37, -29.43, 4.15, 0.03267, 5, 38, 26.04, -38.97, 0.0303, 39, -9.04, -39.97, 0.10999, 35, 32.62, 9.74, 0.52634, 36, -12.8, 9.52, 0.33297, 37, -55.1, 2.33, 0.0004, 4, 38, 2.39, -30.74, 0.16648, 39, -33.51, -34.65, 0.01731, 35, 7.62, 11.12, 0.80083, 36, -37.82, 10.55, 0.01538, 4, 38, 10.25, -1.88, 0.98194, 39, -29.2, -5.06, 0.00361, 35, 7.2, 41.02, 0.01257, 36, -38.66, 40.44, 0.00188, 5, 38, 35.24, -1.91, 0.61315, 39, -4.39, -2.07, 0.37717, 35, 31.23, 47.9, 0.00166, 36, -14.73, 47.65, 0.00798, 37, -61.9, 39.91, 3e-05, 3, 39, 22.56, -3.89, 0.9675, 36, 12.12, 50.5, 0.02472, 37, -35.62, 46.16, 0.00778, 4, 39, 48.32, -4.3, 0.13329, 40, 8.68, -4.28, 0.83735, 36, 37.57, 54.52, 0.00346, 37, -10.9, 53.4, 0.0259, 3, 39, 68.97, -6.01, 0.01019, 40, 29.37, -5.48, 0.94982, 37, 9.33, 57.89, 0.03999], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 0, 36, 36, 38, 38, 40, 40, 42, 42, 44, 20, 46, 46, 48, 48, 50, 50, 52, 52, 54, 56, 58, 58, 60, 60, 62, 62, 64], "width": 91, "height": 136}}, "qub2": {"qub2": {"type": "mesh", "uvs": [0.13308, 0.00484, 0.33424, 0, 0.56824, 0.04941, 0.72013, 0.18631, 0.9254, 0.33753, 1, 0.57949, 1, 0.76096, 0.96234, 0.91536, 0.89255, 1, 0.75298, 0.97904, 0.71192, 0.90741, 0.65035, 0.75141, 0.55182, 0.57153, 0.32603, 0.39961, 0.11666, 0.28023, 0, 0.18312, 0, 0.05737, 0.26035, 0.08284, 0.44919, 0.22133, 0.58877, 0.37414, 0.6914, 0.52537, 0.80224, 0.66704, 0.83508, 0.77688, 0.86382, 0.92173], "triangles": [9, 23, 8, 8, 23, 7, 9, 10, 23, 7, 23, 22, 23, 10, 22, 7, 22, 6, 10, 11, 22, 6, 22, 21, 22, 11, 21, 21, 5, 6, 11, 12, 21, 21, 20, 5, 12, 20, 21, 20, 4, 5, 20, 13, 19, 20, 12, 13, 20, 19, 4, 19, 3, 4, 19, 13, 18, 13, 14, 18, 19, 18, 3, 14, 15, 18, 15, 17, 18, 18, 2, 3, 18, 17, 2, 15, 16, 17, 16, 0, 17, 17, 1, 2, 17, 0, 1], "vertices": [1, 46, -6.01, -2.8, 1, 1, 46, -4.8, 0.83, 1, 1, 46, -0.9, 4.06, 1, 2, 46, 6.4, 4.23, 0.89002, 47, -6.67, 3, 0.10998, 1, 47, 1.4, 5.24, 1, 2, 47, 13.28, 4.1, 0.46697, 48, 0.49, 4.16, 0.53303, 1, 48, 9.32, 3.11, 1, 1, 48, 16.74, 1.5, 1, 1, 48, 20.7, -0.31, 1, 1, 48, 19.37, -2.82, 1, 1, 48, 15.79, -3.18, 1, 1, 48, 8.06, -3.43, 1, 3, 46, 22.72, -5.78, 0.00832, 47, 11.09, -4.14, 0.72571, 48, -0.91, -4.25, 0.26597, 2, 46, 13.31, -6.62, 0.7978, 47, 1.95, -6.54, 0.2022, 1, 46, 6.4, -8.12, 1, 1, 46, 1.15, -8.41, 1, 1, 46, -4.56, -6.11, 1, 1, 46, -1.56, -1.98, 1, 1, 46, 6.07, -1.19, 1, 2, 46, 14.01, -1.52, 0.47439, 47, 1.79, -1.4, 0.52561, 3, 46, 21.61, -2.47, 0.00462, 47, 9.45, -1.07, 0.99043, 48, -2.84, -1.34, 0.00495, 1, 48, 4.3, -0.08, 1, 1, 48, 9.72, -0.1, 1, 1, 48, 16.83, -0.4, 1], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32, 0, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46], "width": 19, "height": 49}}, "qub3": {"qub3": {"type": "mesh", "uvs": [0.78787, 0.02995, 0.91087, 0.07231, 1, 0.17721, 0.84187, 0.28614, 0.64687, 0.43138, 0.44287, 0.61091, 0.23887, 0.81465, 0.01987, 1, 0, 0.85096, 0, 0.57662, 0.07087, 0.35069, 0.23287, 0.13283, 0.40387, 0, 0.64387, 0, 0.63187, 0.13283, 0.43087, 0.27201, 0.26587, 0.43339, 0.17287, 0.61495, 0.09787, 0.76019], "triangles": [18, 7, 8, 18, 6, 7, 8, 9, 18, 18, 17, 6, 6, 17, 5, 18, 9, 17, 5, 17, 16, 17, 9, 16, 9, 10, 16, 5, 16, 4, 16, 15, 4, 16, 10, 15, 10, 11, 15, 15, 11, 14, 4, 15, 3, 15, 14, 3, 3, 14, 1, 3, 1, 2, 1, 14, 0, 11, 12, 14, 14, 13, 0, 14, 12, 13], "vertices": [1, 49, -5.24, 2.21, 1, 1, 49, -6.24, 7.51, 1, 1, 49, -3.56, 13.98, 1, 2, 49, 5.21, 12.98, 0.99875, 50, -5.98, 14.21, 0.00125, 2, 49, 16.53, 12.13, 0.59778, 50, 5.05, 11.53, 0.40222, 3, 49, 29.63, 12.22, 0.02638, 50, 18, 9.49, 0.76982, 51, 3.96, 9.27, 0.20379, 2, 50, 32.18, 8.12, 0.00843, 51, 17.94, 6.57, 0.99157, 1, 51, 31.18, 2.91, 1, 1, 51, 23.53, -1.19, 1, 1, 51, 8.88, -7.41, 1, 2, 50, 11.63, -10.45, 0.24834, 51, -4.26, -9.99, 0.75166, 3, 49, 12.74, -11.24, 0.06492, 50, -2.49, -10.91, 0.885, 51, -18.36, -9.11, 0.05008, 3, 49, 2.56, -10.69, 0.64157, 50, -12.44, -8.71, 0.35834, 51, -28.06, -5.99, 9e-05, 2, 49, -3.17, -3.29, 0.97875, 50, -16.9, -0.48, 0.02125, 1, 49, 3.2, 1.06, 1, 2, 50, 0.93, -0.27, 0.99925, 51, -13.95, 1.15, 0.00075, 2, 50, 12.23, -1.48, 0.50578, 51, -2.82, -1.11, 0.49422, 1, 51, 8.29, -0.33, 1, 1, 51, 17.19, 0.27, 1], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26, 0, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 14], "width": 39, "height": 58}}, "qub4": {"qub4": {"type": "mesh", "uvs": [0.50547, 0, 0.72188, 0.00404, 0.90223, 0.01368, 1, 0.12315, 1, 0.26327, 1, 0.39114, 0.96126, 0.53827, 0.89895, 0.66876, 0.87272, 0.77297, 0.90523, 0.90528, 0.96579, 0.99077, 0.71923, 0.9873, 0.46403, 0.98846, 0.18286, 0.99539, 0, 1, 0.00984, 0.90528, 0.05742, 0.75625, 0.10933, 0.64072, 0.16989, 0.50902, 0.15691, 0.36461, 0.12663, 0.23637, 0.04877, 0.10582, 0.11798, 0.01571, 0.28668, 0, 0.53756, 0.09543, 0.58082, 0.23753, 0.65003, 0.36923, 0.60677, 0.5171, 0.53757, 0.65689, 0.51161, 0.76664, 0.43375, 0.90643], "triangles": [16, 17, 28, 29, 16, 28, 30, 15, 16, 29, 30, 16, 13, 15, 30, 13, 30, 12, 14, 15, 13, 29, 28, 7, 8, 29, 7, 9, 30, 29, 9, 29, 8, 11, 30, 9, 12, 30, 11, 11, 9, 10, 6, 27, 5, 28, 27, 6, 7, 28, 6, 27, 18, 26, 28, 18, 27, 17, 18, 28, 24, 3, 25, 2, 24, 1, 3, 24, 2, 25, 3, 4, 26, 25, 4, 26, 4, 5, 27, 26, 5, 24, 0, 1, 22, 24, 21, 24, 20, 21, 23, 0, 24, 23, 24, 22, 20, 24, 25, 19, 20, 25, 19, 25, 26, 18, 19, 26], "vertices": [2, 26, -27.39, 10.2, 0.66402, 23, -29.39, -16.92, 0.33598, 2, 26, -25.87, 19.41, 0.46928, 23, -29.35, -7.6, 0.53072, 2, 26, -23.6, 26.98, 0.37127, 23, -28.31, 0.24, 0.62873, 2, 26, -5.66, 29.52, 0.10289, 23, -10.99, 5.59, 0.89711, 1, 23, 11.52, 7.06, 1, 1, 23, 32.06, 8.4, 1, 2, 23, 55.81, 8.28, 0.05394, 24, 15.49, 9.15, 0.94606, 2, 24, 36.64, 7.96, 0.26273, 25, -3.63, 7.35, 0.73727, 1, 25, 13.17, 8.02, 1, 1, 25, 34.2, 11.69, 1, 1, 25, 47.61, 15.76, 1, 2, 25, 48.19, 5.16, 0.99295, 28, 46.45, 22.91, 0.00705, 2, 25, 49.55, -5.73, 0.62299, 28, 48.38, 12.11, 0.37701, 2, 25, 51.95, -17.64, 0.05816, 28, 51.4, 0.35, 0.94184, 1, 28, 53.38, -7.3, 1, 1, 28, 38.25, -9.3, 1, 1, 28, 14.24, -11.08, 1, 2, 27, 34.48, -11.3, 0.56142, 28, -4.48, -11.83, 0.43858, 1, 27, 13.19, -9.54, 1, 2, 26, 29.65, -10.21, 0.87949, 27, -10.02, -11.03, 0.12051, 1, 26, 8.97, -9.58, 1, 2, 26, -12.27, -10.94, 0.97749, 23, -11.11, -35.41, 0.02251, 2, 26, -26.43, -6.62, 0.88988, 23, -25.78, -33.39, 0.11012, 2, 26, -28.27, 0.84, 0.82588, 23, -28.78, -26.31, 0.17412, 2, 26, -11.97, 10.14, 0.62665, 23, -14.15, -14.55, 0.37335, 2, 26, 10.99, 9.85, 0.54229, 23, 8.56, -11.2, 0.45771, 3, 26, 32.38, 10.83, 0.39292, 23, 29.52, -6.85, 0.47312, 24, -10.71, -6.12, 0.13396, 3, 26, 55.9, 6.75, 0.00218, 27, 13.74, 9.28, 0.36484, 24, 13.17, -6.3, 0.63299, 3, 27, 36.34, 7.21, 0.39963, 24, 35.83, -7.68, 0.5228, 28, -4.83, 6.76, 0.07757, 3, 24, 53.53, -7.55, 0.03944, 25, 13.82, -7.52, 0.48571, 28, 12.79, 8.46, 0.47485, 2, 25, 36.56, -8.44, 0.51303, 28, 35.54, 8.73, 0.48697], "hull": 24, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 0, 46, 0, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60], "width": 43, "height": 161}}, "ts1": {"ts1": {"x": 74.31, "y": 35.62, "rotation": -97.41, "width": 42, "height": 60}}, "tf1": {"tf1": {"x": 59.78, "y": -6.36, "rotation": -97.41, "width": 77, "height": 58}}}}], "events": {"atk": {}, "hurt": {}}, "animations": {"boss_attack1": {"bones": {"bone17": {"rotate": [{"angle": -9.77, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1, "angle": -20.44, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.2, "angle": -9.77, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4667, "angle": -36.08, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.5, "angle": -9.77}]}, "bone53": {"rotate": [{"angle": 0.27, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -80.07, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -113.18, "curve": "stepped"}, {"time": 0.3667, "angle": -113.18, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 0.27}]}, "bone57": {"rotate": [{"angle": 2.44, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -92.43, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 8.81, "curve": "stepped"}, {"time": 0.3667, "angle": 8.81, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 2.44}]}, "bone36": {"rotate": [{"angle": -1.62, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "angle": -30.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 74.78, "curve": "stepped"}, {"time": 0.3667, "angle": 74.78, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -1.62}]}, "bone31": {"translate": [{"x": 1.82, "y": 1.09}]}, "bone16": {"rotate": [{"angle": -3.87, "curve": "stepped"}, {"time": 0.1, "angle": -3.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "angle": -30.18, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.5, "angle": -3.87}]}, "bone45": {"rotate": [{"angle": 3.04, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1333, "angle": -7.78, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2333, "angle": 3.04}]}, "bone9": {"rotate": [{"angle": 10.74, "curve": "stepped"}, {"time": 0.1, "angle": 10.74, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3667, "angle": -15.57, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.5, "angle": 10.74}]}, "bone44": {"rotate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -38.41, "curve": 0.342, "c2": 0.36, "c3": 0.682, "c4": 0.72}, {"time": 0.5}]}, "bone20": {"rotate": [{"angle": -8.6, "curve": 0.332, "c2": 0.33, "c3": 0.676, "c4": 0.7}, {"time": 0.2667, "angle": -34.91, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -8.6}]}, "bone23": {"rotate": [{"angle": -11.51, "curve": "stepped"}, {"time": 0.1, "angle": -11.51, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3667, "angle": -37.82, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.5, "angle": -11.51}]}, "bone10": {"rotate": [{"angle": 1.64, "curve": "stepped"}, {"time": 0.1667, "angle": 1.64, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333, "angle": -24.67, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 0.5, "angle": 1.64}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -34.77, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -46.51, "curve": "stepped"}, {"time": 0.3667, "angle": -46.51, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone24": {"rotate": [{"angle": -16.08, "curve": "stepped"}, {"time": 0.2, "angle": -16.08, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -42.39, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.5, "angle": -16.08}]}, "bone52": {"rotate": [{"angle": -4.77, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -1.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 68.61, "curve": "stepped"}, {"time": 0.3667, "angle": 68.61, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -4.77}]}, "bone37": {"rotate": [{"angle": -4.1, "curve": "stepped"}, {"time": 0.1, "angle": -4.1, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2667, "angle": 58.42, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 44.1, "curve": "stepped"}, {"time": 0.4667, "angle": 44.1, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5, "angle": -4.1}]}, "bone49": {"rotate": [{"angle": 7.66, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.2667, "angle": -30.76, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.5, "angle": 7.66}]}, "bone5": {"rotate": [{"angle": 1.99}], "translate": [{"x": 1.09, "y": -0.24}]}, "bone19": {"rotate": [{"angle": -2.76}]}, "bone11": {"rotate": [{"angle": 1.66}]}, "bone30": {"rotate": [{"angle": -14.58, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "angle": -40.89, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -14.58}]}, "bone4": {"rotate": [{"angle": 0.79, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "angle": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 17.71, "curve": "stepped"}, {"time": 0.3667, "angle": 17.71, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 0.79}], "translate": [{"x": 0.51, "y": -0.08}]}, "bone34": {"rotate": [{"angle": 1.22, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1667, "angle": 117.67, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 31.39, "curve": "stepped"}, {"time": 0.3667, "angle": 31.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 1.22}]}, "bone50": {"rotate": [{"angle": 10.7, "curve": "stepped"}, {"time": 0.1, "angle": 10.7, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -27.71, "curve": 0.349, "c2": 0.38, "c3": 0.693, "c4": 0.75}, {"time": 0.5, "angle": 10.7}]}, "bone8": {"rotate": [{"angle": 16.71, "curve": "stepped"}, {"time": 0.0667, "angle": 16.71, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -9.6, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.5, "angle": 16.71}]}, "bone33": {"rotate": [{"angle": 0.48, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "angle": -18.61, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -70.9, "curve": "stepped"}, {"time": 0.3667, "angle": -70.9, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 0.48}]}, "bone51": {"rotate": [{"angle": 7.67, "curve": "stepped"}, {"time": 0.2, "angle": 7.67, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4667, "angle": -30.75, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 0.5, "angle": 7.67}]}, "bone21": {"rotate": [{"angle": -13.37, "curve": "stepped"}, {"time": 0.1, "angle": -13.37, "curve": 0.287, "c2": 0.13, "c3": 0.632, "c4": 0.52}, {"time": 0.3667, "angle": -39.68, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.5, "angle": -13.37}]}, "bone32": {"translate": [{"x": 1.11, "y": -1.69}]}, "bone54": {"rotate": [{"angle": 4.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 76.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 38.17, "curve": "stepped"}, {"time": 0.3667, "angle": 38.17, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 4.08}]}, "bone58": {"rotate": [{"angle": -9.52, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 30.07, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 3.55, "curve": "stepped"}, {"time": 0.3667, "angle": 3.55, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -9.52}]}, "bone12": {"rotate": [{"angle": -4.33, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.0333, "angle": -30.64, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -4.33}]}, "bone56": {"rotate": [{"angle": -0.69, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 54.59, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -19.21, "curve": "stepped"}, {"time": 0.3667, "angle": -19.21, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -0.69}]}, "bone2": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -17.31, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -24.9, "y": -51.39, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 48.13, "y": -2.04, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 36.43, "y": -2.04, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone46": {"rotate": [{"angle": 7.67, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1, "angle": -30.75, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 0.1333, "angle": -23.33, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "angle": 7.67}]}, "bone62": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 28.58, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -57.6, "curve": "stepped"}, {"time": 0.3667, "angle": -57.6, "curve": 0.25, "c3": 0.75}, {"time": 0.5}], "scale": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 1.235, "y": 1.832, "curve": "stepped"}, {"time": 0.3667, "x": 1.235, "y": 1.832, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "bone55": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -57.64, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 53.48, "curve": "stepped"}, {"time": 0.3333, "x": 53.48, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "bone59": {"rotate": [{"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -46.55, "curve": "stepped"}, {"time": 0.3667, "angle": -46.55, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 10.67, "y": 48.52, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 48.52, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "bone25": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 11.29, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -14.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "bone26": {"rotate": [{"angle": -4.1, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 11.29, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -14.44, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5333, "angle": -4.1}]}, "bone27": {"rotate": [{"angle": -10.34, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 11.29, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -14.44, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.5333, "angle": -10.34}]}, "bone22": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 11.29, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -14.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "bone28": {"rotate": [{"angle": -4.1, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 11.29, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -14.44, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5333, "angle": -4.1}]}, "bone29": {"rotate": [{"angle": -10.34, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 11.29, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -14.44, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.5333, "angle": -10.34}]}}, "events": [{"time": 0.2667, "name": "atk"}]}, "boss_attack3": {"slots": {"xuanz": {"attachment": [{"time": 0.2667, "name": "xuanz"}, {"time": 0.5333, "name": null}]}, "mutou3": {"attachment": [{"time": 0.2667, "name": "mutou"}, {"time": 0.5333, "name": null}]}}, "bones": {"bone63": {"rotate": [{"time": 0.2667}, {"time": 0.3333, "angle": -120}, {"time": 0.4, "angle": 120}, {"time": 0.4333}, {"time": 0.5333, "angle": -120}, {"time": 0.5667, "angle": 120}, {"time": 0.6667}], "translate": [{"x": -6.78, "y": -184.11, "curve": "stepped"}, {"time": 0.2667, "x": -6.78, "y": -184.11, "curve": 0.321, "c2": 0.51, "c3": 0.75}, {"time": 0.6667, "x": 777.72, "y": -196.09}]}, "bone2": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 5.42, "curve": "stepped"}, {"time": 0.2, "angle": 5.42, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.2667, "angle": -28, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.7}], "translate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "x": -11.2, "y": -18.17, "curve": "stepped"}, {"time": 0.2, "x": -11.2, "y": -18.17, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.2667, "x": 8.84, "y": -18.08, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.7}]}, "bone3": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 9.89, "curve": "stepped"}, {"time": 0.2, "angle": 9.89, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.2667, "angle": 3.63, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.7}]}, "bone4": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2667, "angle": 9.89, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.3333, "angle": 3.63, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.7}]}, "bone5": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "angle": 9.26, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.4, "angle": 3.09, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.7}]}, "bone8": {"rotate": [{"angle": -4.8, "curve": 0.381, "c2": 0.59, "c3": 0.727}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -22.94, "curve": 0.242, "c3": 0.661, "c4": 0.65}, {"time": 0.7, "angle": -4.8}]}, "bone9": {"rotate": [{"angle": -15.55, "curve": 0.328, "c2": 0.32, "c3": 0.672, "c4": 0.68}, {"time": 0.1, "angle": -7.38, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -22.94, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 0.7, "angle": -15.55}]}, "bone10": {"rotate": [{"angle": -22.94, "curve": 0.273, "c3": 0.619, "c4": 0.41}, {"time": 0.1, "angle": -18.14, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -22.94}]}, "bone11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -22.94, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone12": {"rotate": [{"angle": -7.38, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -22.94, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 0.7, "angle": -7.38}]}, "bone15": {"rotate": [{"curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 0.3333, "angle": -17.69, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.7}]}, "bone16": {"rotate": [{"curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 0.3333, "angle": -17.69, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.7}]}, "bone17": {"rotate": [{"curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 0.3333, "angle": 31.55, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.7}]}, "bone18": {"rotate": [{"angle": -18.14, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -22.94, "curve": 0.273, "c3": 0.619, "c4": 0.41}, {"time": 0.7, "angle": -18.14}]}, "bone22": {"rotate": [{"curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 0.3333, "angle": -17.69, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.7}]}, "bone23": {"rotate": [{"curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 0.3333, "angle": -17.69, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.7}]}, "bone24": {"rotate": [{"curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 0.3333, "angle": -17.69, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.7}]}, "bone25": {"rotate": [{"curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 0.3333, "angle": -17.69, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.7}]}, "bone26": {"rotate": [{"curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 0.3333, "angle": -17.69, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.7}]}, "bone27": {"rotate": [{"curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 0.3333, "angle": -17.69, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.7}]}, "bone28": {"rotate": [{"curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 0.3333, "angle": -17.69, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.7}]}, "bone29": {"rotate": [{"curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 0.3333, "angle": -17.69, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.7}]}, "bone33": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "angle": 15.99, "curve": "stepped"}, {"time": 0.2, "angle": 15.99, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3667, "angle": -39.49, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7}]}, "bone34": {"rotate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2667, "angle": 62.67, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.4333, "angle": -10.67, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7}]}, "bone36": {"rotate": [{"curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 0.1, "angle": 37.6, "curve": 0.317, "c2": 0.27, "c3": 0.652, "c4": 0.61}, {"time": 0.1667, "angle": -177.28, "curve": "stepped"}, {"time": 0.2, "angle": -177.28, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.2667, "angle": 71.15, "curve": 0.327, "c2": 0.31, "c3": 0.663, "c4": 0.66}, {"time": 0.7}]}, "bone37": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 9.47, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 4.74, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3, "angle": 18.04, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.7}]}, "bone52": {"rotate": [{"angle": -4.77}]}, "bone53": {"rotate": [{"angle": 0.27}]}, "bone54": {"rotate": [{"angle": 4.08}]}, "bone56": {"rotate": [{"angle": -0.69}]}, "bone57": {"rotate": [{"angle": 2.44}]}, "bone58": {"rotate": [{"angle": -9.52}]}, "bone62": {"rotate": [{"time": 0.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": -37.52, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7}]}, "bone44": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 22.43, "curve": 0.25, "c3": 0.75}, {"time": 0.7}], "translate": [{"time": 0.3667, "x": 4.44, "y": 6.97}]}, "bone45": {"rotate": [{"angle": -8.22, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -22.34, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.7, "angle": -8.22}]}, "bone46": {"rotate": [{"angle": -14.12, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -22.34, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.7, "angle": -14.12}]}, "bone49": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -22.34, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "bone50": {"rotate": [{"angle": -8.22, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -22.34, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.7, "angle": -8.22}]}, "bone51": {"rotate": [{"angle": -14.12, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -22.34, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.7, "angle": -14.12}]}}, "deform": {"default": {"mutou3": {"mutou": [{"time": 0.2667, "vertices": [-36.79663, -20.50189, 18.82589, 37.68137, 36.79654, 20.50163, -18.82587, -37.68157]}]}}}, "events": [{"time": 0.4667, "name": "atk"}, {"time": 0.4667, "name": "hurt"}]}, "boss_idle": {"bones": {"bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 0.91, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone4": {"translate": [{"x": 0.51, "y": -0.08, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.8, "y": -0.29, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.51, "y": -0.08}]}, "bone5": {"rotate": [{"angle": 1.99, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 2.78, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 1.99}], "translate": [{"x": 1.09, "y": -0.24, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.52, "y": -0.34, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 1.09, "y": -0.24}]}, "bone8": {"rotate": [{"angle": 16.71, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -4.33, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 16.71}]}, "bone9": {"rotate": [{"angle": 10.74, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 16.71, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -4.33, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 10.74}]}, "bone10": {"rotate": [{"angle": 1.64, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 16.71, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -4.33, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 1.64}]}, "bone11": {"rotate": [{"angle": 1.66, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.3333, "angle": 10.74, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6667, "angle": 16.71, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -4.33, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 1.66}]}, "bone12": {"rotate": [{"angle": -4.33, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3333, "angle": 1.64, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1, "angle": 16.71, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -4.33}]}, "bone15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -13.64, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone16": {"rotate": [{"angle": -3.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -13.64, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -3.87}]}, "bone17": {"rotate": [{"angle": -0.54}]}, "bone19": {"rotate": [{"angle": 6.46}]}, "bone20": {"rotate": [{"angle": 0.63}]}, "bone21": {"rotate": [{"angle": 2}]}, "bone23": {"rotate": [{"angle": 3.86}]}, "bone24": {"rotate": [{"angle": -0.7}]}, "bone31": {"translate": [{"x": 1.82, "y": 1.09, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 3.63, "y": 2.17, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 1.82, "y": 1.09}]}, "bone32": {"translate": [{"x": 1.11, "y": -1.69, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 2.22, "y": -3.38, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 1.11, "y": -1.69}]}, "bone33": {"rotate": [{"angle": 0.48, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.7, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 0.48}]}, "bone34": {"rotate": [{"angle": 1.22, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 1.7, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 1.22}]}, "bone36": {"rotate": [{"angle": -1.62, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -5.72, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -1.62}]}, "bone37": {"rotate": [{"angle": -4.1, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -5.72, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -4.1}]}, "bone44": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 10.7, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone45": {"rotate": [{"angle": 3.04, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 10.7, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 3.04}]}, "bone46": {"rotate": [{"angle": 7.67, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 10.7, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 7.67}]}, "bone49": {"rotate": [{"angle": 7.66, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.3333, "angle": 3.04, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 10.7, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 7.66}]}, "bone50": {"rotate": [{"angle": 10.7, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3333, "angle": 7.67, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 10.7}]}, "bone51": {"rotate": [{"angle": 7.67, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 10.7, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 7.67}]}, "bone52": {"rotate": [{"angle": -4.77}]}, "bone53": {"rotate": [{"angle": 0.27}]}, "bone54": {"rotate": [{"angle": 4.08}]}, "bone56": {"rotate": [{"angle": -0.69}]}, "bone57": {"rotate": [{"angle": 2.44}]}, "bone58": {"rotate": [{"angle": -9.52}]}, "bone25": {"rotate": [{"angle": -1.49, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -11.42, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2, "angle": -1.49}]}, "bone26": {"rotate": [{"angle": -6.2, "curve": 0.336, "c2": 0.34, "c3": 0.676, "c4": 0.69}, {"time": 0.2, "angle": -3.24, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -11.42, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 2, "angle": -6.2}]}, "bone27": {"rotate": [{"angle": -10.67, "curve": 0.304, "c2": 0.22, "c3": 0.644, "c4": 0.58}, {"time": 0.2, "angle": -8.18, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -11.42, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 2, "angle": -10.67}]}, "bone22": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -11.42, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone28": {"rotate": [{"angle": -3.24, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -11.42, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -3.24}]}, "bone29": {"rotate": [{"angle": -8.18, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -11.42, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -8.18}]}}}, "die": {"slots": {"biyan": {"attachment": [{"time": 0.2, "name": "biyan"}]}}, "bones": {"bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -23.3, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 52.43, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 103.17, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 84.48}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": 11.86, "y": -31.86, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 0.74, "y": 42.24, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 0.74, "y": 57.8, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 0.74, "y": 8.15, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 3.7, "y": -124.49, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 3.7, "y": -99.05, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 3.7, "y": -124.49}]}, "bone19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -11.24}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -11.24}]}, "bone21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -11.24}]}, "bone33": {"rotate": [{"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 45.03, "curve": "stepped"}, {"time": 0.3333, "angle": 45.03, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -24.05}]}, "bone34": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 30.81, "curve": "stepped"}, {"time": 0.3667, "angle": 30.81, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 3.76}]}, "bone36": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 73.21, "curve": "stepped"}, {"time": 0.3, "angle": 73.21, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 22.07}]}, "bone37": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -15.22, "curve": "stepped"}, {"time": 0.3, "angle": -15.22, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -17.67}]}, "bone52": {"rotate": [{"angle": -4.77}]}, "bone53": {"rotate": [{"angle": 0.27}]}, "bone54": {"rotate": [{"angle": 4.08}]}, "bone56": {"rotate": [{"angle": -0.69}]}, "bone57": {"rotate": [{"angle": 2.44}]}, "bone58": {"rotate": [{"angle": -9.52}]}, "bone62": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -45.22, "curve": "stepped"}, {"time": 0.3, "angle": -45.22, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -72.27}]}}}, "hurt": {"slots": {"biyan": {"attachment": [{"time": 0.1667, "name": "biyan"}, {"time": 0.3333, "name": null}]}}, "bones": {"bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -16.61, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 23.86, "curve": 0.25, "c3": 0.75}, {"time": 0.3}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -1.12, "y": -8.93, "curve": "stepped"}, {"time": 0.2, "x": -1.12, "y": -8.93, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -18.47, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -2.04, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone4": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -18.47, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 6.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone5": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 10.95, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone8": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -10.24, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 17.43, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "bone9": {"rotate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -10.24, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 17.43, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone10": {"rotate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -10.24, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 17.43, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone11": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -10.24, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 17.43, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone12": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -10.24, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 17.43, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "bone18": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -10.24, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 17.43, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "bone33": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 17.7, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone34": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 41.05, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone36": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 62, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone37": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 13.62, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone52": {"rotate": [{"angle": -4.77, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 21.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -4.77}]}, "bone53": {"rotate": [{"angle": 0.27, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -20.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -52.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 0.27}]}, "bone54": {"rotate": [{"angle": 4.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 7.23, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 29.7, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 4.08}]}, "bone56": {"rotate": [{"angle": -0.69, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 35.34, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -14.05, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -0.69}]}, "bone57": {"rotate": [{"angle": 2.44, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -40.9, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -21.82, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 2.44}]}, "bone58": {"rotate": [{"angle": -9.52, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 14.41, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 4.23, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -9.52}]}, "bone62": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -39.15, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone49": {"rotate": [{"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.18, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 25.26, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone50": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -16.18, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone51": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -16.18, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone44": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -16.18, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 25.26, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone45": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -16.18, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 25.26, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone46": {"rotate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -16.18, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone25": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -9.86, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 24.08, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone26": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -9.86, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 24.08, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "bone27": {"rotate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -9.86, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 24.08, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}]}, "bone22": {"rotate": [{"angle": -2.8, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": -9.86, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 24.08, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": "stepped"}, {"time": 0.6333, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "angle": -2.8}]}, "bone28": {"rotate": [{"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -9.86, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 24.08, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone29": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -9.86, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 24.08, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}}}, "run1": {"bones": {"ljio1": {"translate": [{"x": -1.78, "y": 58.06}]}, "bone": {"translate": [{"y": 9.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": -8.51, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": 9.08, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": -8.51, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": 9.08}]}, "bone22": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -24.03, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone23": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -15.97, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone24": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -15.97, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone25": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -31.42, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone26": {"rotate": [{"angle": -7.61, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -31.42, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": -7.61}]}, "bone27": {"rotate": [{"angle": -19.86, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -31.42, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -19.86}]}, "bone28": {"rotate": [{"angle": -7.61, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -31.42, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": -7.61}]}, "bone29": {"rotate": [{"angle": -19.86, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -31.42, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -19.86}]}, "bone30": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -23.36, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone17": {"rotate": [{"angle": 40.19, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}], "translate": [{"x": -3.4, "y": 6.03}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone8": {"rotate": [{"angle": 22.52, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 29.02, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 11.35, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 29.02, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 11.35, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 22.52}]}, "bone9": {"rotate": [{"angle": -12.27, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.0667, "angle": -5.76, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1667, "angle": 5.41, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -12.27, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 5.41, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -12.27}]}, "bone10": {"rotate": [{"angle": 7.61, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 0.0667, "angle": -1.18, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "angle": -3.48, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 14.2, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.4333, "angle": -3.48, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 14.2, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": 7.61}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.65, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone4": {"rotate": [{"angle": 4.82, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 9.65, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 4.82}]}, "bone5": {"rotate": [{"angle": 7.87, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 9.65, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.6667, "angle": 7.87}]}, "bone11": {"rotate": [{"angle": 30.25, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 12.58, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 30.25, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 12.58, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 30.25}]}, "bone12": {"rotate": [{"angle": 1.81, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "angle": 12.98, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -4.69, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 12.98, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -4.69, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": 1.81}]}, "bone18": {"rotate": [{"angle": -7.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": -10.21, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 7.47, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 0.3667, "angle": -10.21, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 7.47, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -7.91}]}, "bone66": {"rotate": [{"angle": -26.49}]}, "bone38": {"rotate": [{"angle": 16.21}]}, "bone60": {"translate": [{"x": 41.64, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -16.45, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -103.75, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -28.84, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 41.64}]}, "bone61": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -2.22, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -67.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -50.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}], "translate": [{"x": 0.48, "y": 33.04, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -0.79, "y": 70.42, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 50.92, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 0.48, "y": 33.04}]}, "bone59": {"rotate": [{"angle": -58.5, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -19.4, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 60.49, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -2.91, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -58.5}], "translate": [{"x": -1.86, "y": 67.28, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 16.3, "y": 58.28, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 3.7, "y": 52.45, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 6.63, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -1.86, "y": 67.28}]}, "bone55": {"translate": [{"x": -65.06, "y": -0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -9.58, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 100.15, "y": 0.29, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 16.92, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -65.06, "y": -0.4}]}, "bone2": {"rotate": [{"angle": -22.85}]}, "bone35": {"rotate": [{"angle": 35.12}]}, "bone44": {"rotate": [{"angle": 43.58, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 17.22, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 43.58, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 17.22, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 43.58}], "translate": [{"x": -1.02, "y": 18.54}]}, "bone45": {"rotate": [{"angle": -12.65, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "angle": 4.02, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2667, "angle": -22.34, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 4.02, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -22.34, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -12.65}]}, "bone46": {"rotate": [{"angle": -13.43, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": -16.86, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 9.5, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -16.86, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 9.5, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -13.43}]}, "bone49": {"rotate": [{"angle": -0.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "angle": 16.52, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2667, "angle": -9.85, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 16.52, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -9.85, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -0.15}], "translate": [{"x": -3.95, "y": 11.12}]}, "bone50": {"rotate": [{"angle": -14.51, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": -17.94, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.1, "angle": -8.24, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "angle": 8.43, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3667, "angle": -17.94, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 8.43, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -14.51}]}, "bone51": {"rotate": [{"angle": 8.41, "curve": 0.306, "c2": 0.24, "c3": 0.694, "c4": 0.76}, {"time": 0.1, "angle": -11.06, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "angle": -14.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 11.87, "curve": 0.306, "c2": 0.24, "c3": 0.694, "c4": 0.76}, {"time": 0.4667, "angle": -14.49, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 11.87, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": 8.41}]}, "bone36": {"rotate": [{"angle": 24.85, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -17.86, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 24.85}]}, "bone37": {"rotate": [{"angle": 25.91, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "angle": 24.85, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 27.74, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 25.91}]}, "bone34": {"rotate": [{"angle": 53.22, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "angle": 43.77, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 69.46, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 53.22}]}, "bone33": {"rotate": [{"angle": -54.26, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -8.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -54.26}]}}}, "run2": {"bones": {"bone37": {"rotate": [{"angle": 54.37, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": 60.29, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 48.45, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 54.37}]}, "bone": {"rotate": [{"angle": -13.48}], "translate": [{"y": 9.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 9.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 9.08}]}, "ljio1": {"translate": [{"x": -1.78, "y": 58.06}]}, "bone36": {"rotate": [{"angle": 22.12, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.0667, "angle": 55, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -42.92, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.3667, "angle": 22.12}]}, "bone34": {"rotate": [{"angle": 75.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": 74.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 76.02, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 75.4}]}, "bone16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone61": {"rotate": [{"curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.1, "angle": -2.22, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1667, "angle": -67.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": -50.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667}], "translate": [{"y": 52.03, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "y": 2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 96.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "y": 50.92, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 52.03}]}, "bone62": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 19.53, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone4": {"rotate": [{"angle": 4.82, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 9.65, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 4.82}]}, "bone55": {"translate": [{"x": -96.78, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "x": -9.58, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.1667, "x": 130.93, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "x": 16.92, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "x": -96.78}]}, "bone60": {"translate": [{"x": 76.42, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "x": -16.45, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.1667, "x": -126.69, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "x": -28.84, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "x": 76.42}]}, "bone33": {"rotate": [{"angle": -43.64, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.0667, "angle": -87.06, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 42.24, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.3667, "angle": -43.64}]}, "bone59": {"rotate": [{"angle": -58.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "angle": -19.4, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.1667, "angle": 60.49, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "angle": -2.91, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "angle": -58.5}], "translate": [{"y": 107.65, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 0.1, "x": 16.3, "y": 58.28, "curve": 0.323, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 0.1667, "x": 4.28, "y": 69.07, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "y": 6.63, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "y": 107.65}]}, "bone23": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -15.97, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone24": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -15.97, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone30": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -23.36, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone17": {"rotate": [{"angle": 55.92}], "translate": [{"x": -4.84, "y": 14.39}]}, "bone19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone5": {"rotate": [{"angle": 7.87, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 9.65, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3667, "angle": 7.87}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 9.65, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone2": {"rotate": [{"angle": -41.23}]}, "bone18": {"rotate": [{"angle": -14.88, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": 1.27, "curve": 0.334, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 0.1667, "angle": -21.27, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 1.27, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -21.27, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -14.88}]}, "bone9": {"rotate": [{"angle": -16.69, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": -0.55, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 0.1667, "angle": -23.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -0.55, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -23.08, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -16.69}]}, "bone10": {"rotate": [{"angle": -8.63, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": 7.51, "curve": 0.32, "c2": 0.29, "c3": 0.657, "c4": 0.63}, {"time": 0.1667, "angle": -15.02, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 7.51, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -15.02, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -8.63}]}, "bone11": {"rotate": [{"angle": 34.44, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 11.9, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 34.44, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 11.9, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 34.44}]}, "bone8": {"rotate": [{"angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 10.16, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 10.16, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 32.7}]}, "bone12": {"rotate": [{"angle": -8.66, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": 7.48, "curve": 0.337, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.1667, "angle": -15.05, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 7.48, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -15.05, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -8.66}]}, "bone46": {"rotate": [{"angle": -4.42, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": 13.06, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -11.35, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 13.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -11.35, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -4.42}]}, "bone44": {"rotate": [{"angle": 69.61, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 45.2, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 69.61, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 45.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 69.61}], "translate": [{"x": -4.22, "y": 18.29}]}, "bone45": {"rotate": [{"angle": -0.14, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "angle": 6.78, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -17.63, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 6.78, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -17.63, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": -0.14}]}, "bone49": {"rotate": [{"angle": 40.5, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 16.09, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 40.5, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 16.09, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 40.5}]}, "bone50": {"rotate": [{"angle": 9.13, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "angle": 16.06, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -8.35, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 16.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -8.35, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": 9.13}]}, "bone51": {"rotate": [{"angle": -0.66, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": 16.83, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -7.58, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 16.83, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -7.58, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -0.66}]}, "bone22": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -23.53, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone28": {"rotate": [{"angle": -8.65, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -23.53, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": -8.65}]}, "bone29": {"rotate": [{"angle": -23.53, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -23.53}]}, "bone25": {"rotate": [{"angle": -21.26, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "angle": -23.53, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.3667, "angle": -21.26}]}, "bone26": {"rotate": [{"angle": -11.76, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": -23.53, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.2, "angle": -8.65, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": -11.76}]}, "bone27": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -23.53, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}}}, "show_time": {"bones": {"bone37": {"rotate": [{"angle": 54.37, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": 60.29, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 48.45, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 54.37}]}, "bone": {"rotate": [{"angle": -13.48}], "translate": [{"y": 9.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 9.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 9.08}]}, "ljio1": {"translate": [{"x": -1.78, "y": 58.06}]}, "bone36": {"rotate": [{"angle": 22.12, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.0667, "angle": 55, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -42.92, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.3667, "angle": 22.12}]}, "bone34": {"rotate": [{"angle": 75.4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": 74.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 76.02, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 75.4}]}, "bone16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone61": {"rotate": [{"curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.1, "angle": -2.22, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1667, "angle": -67.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": -50.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667}], "translate": [{"y": 52.03, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "y": 2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 96.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "y": 50.92, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 52.03}]}, "bone62": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 19.53, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone4": {"rotate": [{"angle": 4.82, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 9.65, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": 4.82}]}, "bone55": {"translate": [{"x": -96.78, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "x": -9.58, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.1667, "x": 130.93, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "x": 16.92, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "x": -96.78}]}, "bone60": {"translate": [{"x": 76.42, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "x": -16.45, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.1667, "x": -126.69, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "x": -28.84, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "x": 76.42}]}, "bone33": {"rotate": [{"angle": -43.64, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.0667, "angle": -87.06, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 42.24, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.3667, "angle": -43.64}]}, "bone59": {"rotate": [{"angle": -58.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1, "angle": -19.4, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.1667, "angle": 60.49, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "angle": -2.91, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "angle": -58.5}], "translate": [{"y": 107.65, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 0.1, "x": 16.3, "y": 58.28, "curve": 0.323, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 0.1667, "x": 4.28, "y": 69.07, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.2667, "y": 6.63, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3667, "y": 107.65}]}, "bone23": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -15.97, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone24": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -15.97, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone30": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -23.36, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone17": {"rotate": [{"angle": 55.92}], "translate": [{"x": -4.84, "y": 14.39}]}, "bone19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone5": {"rotate": [{"angle": 7.87, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 9.65, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3667, "angle": 7.87}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 9.65, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone2": {"rotate": [{"angle": -41.23}]}, "bone18": {"rotate": [{"angle": -14.88, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": 1.27, "curve": 0.334, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 0.1667, "angle": -21.27, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 1.27, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -21.27, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -14.88}]}, "bone9": {"rotate": [{"angle": -16.69, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": -0.55, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 0.1667, "angle": -23.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -0.55, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -23.08, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -16.69}]}, "bone10": {"rotate": [{"angle": -8.63, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": 7.51, "curve": 0.32, "c2": 0.29, "c3": 0.657, "c4": 0.63}, {"time": 0.1667, "angle": -15.02, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 7.51, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -15.02, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -8.63}]}, "bone11": {"rotate": [{"angle": 34.44, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 11.9, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 34.44, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 11.9, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 34.44}]}, "bone8": {"rotate": [{"angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 10.16, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 32.7, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 10.16, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 32.7}]}, "bone12": {"rotate": [{"angle": -8.66, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": 7.48, "curve": 0.337, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.1667, "angle": -15.05, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 7.48, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -15.05, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -8.66}]}, "bone46": {"rotate": [{"angle": -4.42, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": 13.06, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -11.35, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 13.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -11.35, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -4.42}]}, "bone44": {"rotate": [{"angle": 69.61, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 45.2, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 69.61, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 45.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 69.61}], "translate": [{"x": -4.22, "y": 18.29}]}, "bone45": {"rotate": [{"angle": -0.14, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "angle": 6.78, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -17.63, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 6.78, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -17.63, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": -0.14}]}, "bone49": {"rotate": [{"angle": 40.5, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 16.09, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 40.5, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 16.09, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 40.5}]}, "bone50": {"rotate": [{"angle": 9.13, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "angle": 16.06, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -8.35, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 16.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -8.35, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": 9.13}]}, "bone51": {"rotate": [{"angle": -0.66, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": 16.83, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -7.58, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 16.83, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -7.58, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -0.66}]}, "bone22": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -23.53, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone28": {"rotate": [{"angle": -8.65, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -23.53, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": -8.65}]}, "bone29": {"rotate": [{"angle": -23.53, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -23.53}]}, "bone25": {"rotate": [{"angle": -21.26, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "angle": -23.53, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.3667, "angle": -21.26}]}, "bone26": {"rotate": [{"angle": -11.76, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": -23.53, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.2, "angle": -8.65, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": -11.76}]}, "bone27": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -23.53, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}}}}}