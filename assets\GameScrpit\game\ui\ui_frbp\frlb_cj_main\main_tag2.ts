import { _decorator, Component, Node } from "cc";
import { main_tag } from "./main_tag";
import { GuideRouteEnum } from "db://assets/GameScrpit/ext_guide/GuideDefine";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import GuideMgr from "db://assets/GameScrpit/ext_guide/GuideMgr";
const { ccclass, property } = _decorator;

@ccclass("main_tag2")
export class main_tag2 extends main_tag {
  click_btn_task_go(event) {
    // 处理按钮点击事件，例如打开任务界面或执行其他操作
    GuideMgr.startGuide({ stepId: 26 });
  }
}
