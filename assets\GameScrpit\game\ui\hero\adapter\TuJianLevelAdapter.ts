import { instantiate, Node } from "cc";
import { ListAdapter } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { TuJianLevelViewHolder } from "./TuJianLevelViewHolder";
import { IConfigHeroPicture } from "../../../JsonDefine";

export class TuJianLevelAdapter extends ListAdapter {
  private viewHolder: Node;
  private _datas: IConfigHeroPicture;

  constructor(viewHolder: Node) {
    super();
    this.viewHolder = viewHolder;
  }
  public setData(data: IConfigHeroPicture) {
    this._datas = data;
    this.notifyDataSetChanged();
  }
  onCreateView(viewType: number): Node {
    let item = instantiate(this.viewHolder);
    item.active = true;
    return item;
  }
  onBindData(node: Node, position: number): void {
    node.getComponent(TuJianLevelViewHolder).updateData(position, this._datas);
  }
  getCount(): number {
    return this._datas.level.length;
  }
}
