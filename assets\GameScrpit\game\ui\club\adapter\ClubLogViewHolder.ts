import { _decorator, instantiate, Label, Layout, Node, RichText } from "cc";
import { ListAdapter, ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { ClubLogMessage } from "../../../net/protocol/Club";
import { JsonMgr } from "../../../mgr/JsonMgr";
import { TimeUtils } from "db://assets/GameScrpit/lib/utils/TimeUtils";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;
@ccclass("ClubLogViewHolder")
export class ClubLogViewHolder extends ViewHolder {
  @property(RichText)
  log_txt: RichText;

  @property(Label)
  lblLogDate: Label;

  _data: ClubLogMessage;
  public init() {}
  /**
   * 格式化时间戳为指定格式的字符串。
   * @param timestamp 时间戳，单位为毫秒。
   * @returns 返回格式化的日期时间字符串。
   */
  formatTimestamp(timestamp: number): string {
    const date = new Date(timestamp);
    // 检查 date 是否为有效日期
    if (isNaN(date.getTime())) {
      throw new Error("Invalid timestamp");
    }
    const year = date.getFullYear();
    const month = `0${date.getMonth() + 1}`.slice(-2); // 月份从0开始，需要加1，并补零
    const day = `0${date.getDate()}`.slice(-2);
    const hour = `0${date.getHours()}`.slice(-2);
    const minute = `0${date.getMinutes()}`.slice(-2);
    const second = `0${date.getSeconds()}`.slice(-2);

    return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
  }

  public updateData(data: any, position: number) {
    this._data = data;
    // log.log("updateData_log", this._data);
    let str = `<color=#d46b0e>${this._data.name}</color>`;
    switch (this._data.eventType) {
      case 1:
        if (this._data.param1 == 0) {
          str += `向战盟贡献了绵薄之力，助力战盟发展，战盟经验增加 <color=#d46b0e>${this._data.param2}</color>。`;
        } else {
          str += `向战盟捐献了<color=#d46b0e>${this._data.param1}</color>仙玉，助力战盟发展，战盟经验增加 <color=#d46b0e>${this._data.param2}</color>。`;
        }
        break;
      case 2: {
        let roleId = JsonMgr.instance.jsonList.c_unionBoss[this._data.param1 + 1].monsterId;
        let monster = JsonMgr.instance.jsonList.c_monsterShow[roleId];
        str = `战盟Boss '${monster.name}' 已被唤醒,众人速速前往击杀。`;
        break;
      }

      case 3:
        str += `与本战盟志同道合，正式加入本战盟。`;
        break;
      case 4:
        str += `与本战盟志不同道不合，正式退出本战盟。`;
        break;
      case 5: {
        let roleId = JsonMgr.instance.jsonList.c_unionBoss[this._data.param1 + 1].monsterId;
        let monster = JsonMgr.instance.jsonList.c_monsterShow[roleId];
        str = `战盟Boss '${monster.name}' 已被玩家<color=#d46b0e>${this._data.name}</color>击杀，实在是功不可没。`;
        break;
      }
    }
    this.lblLogDate.getComponent(Label).string = TimeUtils.getTimeAgo(this._data.timeStamp);
    this.log_txt.string = str;
  }
}
export class ClubLogAdapter extends ListAdapter {
  private item: Node;
  private data: any[];
  public constructor(item: Node) {
    super();
    this.item = item;
  }
  public setData(data: any[]) {
    this.data = data;
    this.notifyDataSetChanged();
  }

  getViewType(position: number): number {
    return 0;
  }
  onCreateView(viewType: number): Node {
    let item = instantiate(this.item);
    item.active = true;
    item.getComponent(ClubLogViewHolder).init();
    return item;
  }
  onBindData(view: Node, position: number): void {
    view.getComponent(ClubLogViewHolder).updateData(this.data[position], position);
  }
  getCount(): number {
    return this.data.length;
  }
}
