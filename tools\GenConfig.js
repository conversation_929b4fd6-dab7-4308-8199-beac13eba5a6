const fs = require("fs");
const path = require("path");

// 从指定文件夹读取所有 JSON 文件并生成 TypeScript 接口到 JsonDefine.ts
function processJsonFilesInDirectory(directoryPath, genDir) {
  fs.readdir(directoryPath, (err, files) => {
    if (err) {
      console.error("读取文件夹失败:", err);
      return;
    }

    // 筛选出所有 JSON 文件
    const jsonFiles = files.filter((file) => file.endsWith(".json"));

    if (jsonFiles.length === 0) {
      console.log("文件夹中没有 JSON 文件。");
      return;
    }

    let tsInterfaces = "";
    let jsonConstClass = "export class JsonConst {\n";
    jsonConstImport = "import {\n";
    let imports = new Set(); // 用于记录已经导入的接口，避免重复导入
    // 遍历每个 JSON 文件并生成 TypeScript 接口
    jsonFiles.forEach((file) => {
      const filePath = path.join(directoryPath, file);
      const interfaceName = generateInterfaceName(file);

      // 读取并处理文件
      const jsonData = JSON.parse(fs.readFileSync(filePath, "utf8"));
      const tsInterface = generateTypeScriptInterface(jsonData, interfaceName);

      // 将接口添加到 tsInterfaces 字符串中
      tsInterfaces += tsInterface + "\n";
      // 生成对应的 JsonConst 类中的静态变量
      jsonConstClass += `  ${file.replace(".json", "")}: { [key: number]: ${interfaceName} };\n`;

      // 为该接口添加 import 语句，避免重复导入
      if (!imports.has(interfaceName)) {
        imports.add(interfaceName);
        jsonConstImport += `  ${interfaceName},\n`;
      }
    });
    jsonConstImport = jsonConstImport.slice(0, -1);
    // 完成 JsonConst 类的定义
    jsonConstClass += "}\n";

    // console.log(imports);
    // imports.values();
    // for (let i = 0; i < imports.size; i++) {
    //   console.log(imports[i]);
    // }
    jsonConstImport += `\n} from "./JsonDefine";\n`;
    jsonConstClass = `${jsonConstImport}` + jsonConstClass;
    // 将所有生成的接口写入 JsonDefine.ts 文件
    const outputFilePath = path.join(genDir, "JsonDefine.ts");
    fs.writeFileSync(outputFilePath, tsInterfaces);
    console.log("所有接口已生成并保存到 JsonDefine.ts");

    // 将 JsonConst 类写入 JsonConst.ts 文件
    const jsonConstFilePath = path.join(genDir, "JsonConst.ts");
    fs.writeFileSync(jsonConstFilePath, jsonConstClass);
    console.log("JsonConst 类已生成并保存到 JsonConst.ts");
  });
}

// 根据文件名动态生成接口名称
function generateInterfaceName(fileName) {
  const baseName = fileName.replace(".json", "").replace("c_", "").replace("C_", ""); // 去掉文件扩展名
  return `IConfig${baseName.charAt(0).toUpperCase() + baseName.slice(1)}`; // 转换为大写开头并添加 Config 后缀
}

// 用于生成 TypeScript 接口的函数
function generateTypeScriptInterface(jsonData, interfaceName) {
  let result = `export interface ${interfaceName} {\n`;

  // 获取第一个对象（假设它们具有相同的结构）
  const firstObject = Array.isArray(jsonData) ? jsonData[0] : jsonData[Object.keys(jsonData)[0]];

  // 遍历第一个对象的字段
  for (const key in firstObject) {
    if (firstObject.hasOwnProperty(key)) {
      const value = firstObject[key];
      const valueType = determineType(value);

      // 直接输出属性和类型
      result += `  ${key}: ${valueType};\n`;
    }
  }

  result += "}\n";
  return result;
}

// 用于确定数据类型的函数
function determineType(value) {
  if (Array.isArray(value)) {
    // 如果是数组，递归确定数组元素的类型
    // 检查数组元素是否也是数组，支持二维及更高维数组
    return `${determineType(value[0])}[]`; // 二维数组类型
    // if (Array.isArray(value[0])) {
    // } else {
    //   return `${determineType(value[0])}[]`; // 一维数组类型
    // }
  } else if (typeof value === "object" && value !== null) {
    // 如果是对象，递归生成接口类型
    return `{\n${generateTypeScriptInterface(value).replace("interface IData", "").replace("}\n", "")}}`;
  } else {
    // 基本数据类型
    return typeof value === "number"
      ? "number"
      : typeof value === "string"
      ? "string"
      : typeof value === "boolean"
      ? "boolean"
      : "any";
  }
}

// 从指定文件夹读取所有 JSON 文件并生成接口到 JsonDefine.ts
const folderPath = "../assets/bundle_common_json/json"; // 指定文件夹路径
const genDir = "../assets/GameScrpit/game"; //生成的文件路径
processJsonFilesInDirectory(folderPath, genDir);
