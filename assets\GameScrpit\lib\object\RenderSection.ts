import { Color, Label, Layers, Node, Prefab, UIOpacity, UITransform, Vec3, instantiate, isValid, sp, v3 } from "cc";
import { DIRECT } from "./DirectSection";
import GameObject from "./GameObject";
import { Section } from "./Section";
import MsgMgr from "../event/MsgMgr";
import FightManager, { scaleList } from "../../game/fight/manager/FightManager";
import { JsonMgr } from "../../game/mgr/JsonMgr";
import { GORole } from "../../game/fight/role/GORole";
import ToolExt from "../../game/common/ToolExt";
import { IConfigBloodPlace } from "../../game/JsonDefine";
import HPSection from "./HpSection";
import { BundleEnum } from "../../game/bundleEnum/BundleEnum";

export class PlayAnimationDetail {
  public name: string;
  public isLoop: boolean;
  public speed: number;
}

export default class RenderSection extends Section {
  private _prefab2: Prefab;
  /**角色层节点 ---- role*/
  private _Node: Node;
  /**实例组件的上层 --- renderPoint */
  private _renderPoint: Node;

  /**这是具体实例形象 带spine组件的 */
  private _render: Node;
  /**buff点 */
  private _buffPoint: Node;

  /**动画速率 */
  protected _timeScale: number = 1;

  private _spineId: number;

  /**统一加载完成回调 */
  private _initcallBack;

  /**缩放比例 */
  private _dbScale: Vec3;



  public onStart(): void {
    super.onStart();
    this.emitMsg("OnRenderLoaded", this._render);
  }

  public onInit(go: GameObject, args) {
    super.onInit(go, args);
    this.onMsg("OnDirectChange", this.onDirectChange.bind(this));
    this.onMsg("OnPlayAnimation", this.onPlayAnimation.bind(this));

    this._timeScale = scaleList[FightManager.instance.speed];

    this._initcallBack = args.callBack;
    this._spineId = args.spineId;
    this._dbScale = args.dbScale;

    if (args.render) {
      this._Node = args.render;
      this._Node.setParent(this.getSub());
      this._Node.setPosition(v3(0, 0, 0));
      // this._Node.layer = Layers.Enum["UILayer"];
      this._Node.walk((child) => (child.layer = this.getSub().layer));

      this._renderPoint = this._Node.getChildByName("renderPoint");

      this._renderPoint.setScale(this._dbScale);
      this._render = this._renderPoint.getChildByName("render");

      this.ready();
      this._initcallBack && this._initcallBack();
    } else {
      let path = "prefab/role_point";

      this.assetMgr.loadPrefab(BundleEnum.RESOURCES, path, (prefab) => {
        if (isValid(this.getSub()) == false) return;
        this._Node = instantiate(prefab);
        this.getSub().addChild(this._Node);
        this._Node.getChildByName("sd").active = false;
        // this._Node.layer = Layers.Enum["UILayer"];
        this._Node.walk((child) => (child.layer = this.getSub().layer));
        let db = JsonMgr.instance.jsonList.c_spineShow[this._spineId];
        this.initRender(db.prefabPath);
      });
    }
  }

  /**更改形象的朝向 --- 朝向是更改上层节点，不更改render --- 怪物名字的根节点也在这里，所以要一起修改*/
  protected async onDirectChange(direct) {
    if (direct == DIRECT.LEFT) {
      this._Node.getChildByName("sd").setScale(v3(-1, 1, 1));
      this._renderPoint.setScale(this._dbScale.x * -1, this._dbScale.y, 1);
    } else if (direct == DIRECT.RIGHT) {
      this._Node.getChildByName("sd").setScale(v3(1, 1, 1));
      this._renderPoint.setScale(this._dbScale.x * 1, this._dbScale.y, 1);
    }
  }

  /**播放形象的动画 */
  protected onPlayAnimation(detail) {
    let name = (detail as PlayAnimationDetail).name;
    let isLoop = (detail as PlayAnimationDetail).isLoop;

    let ske = this._render.getComponent(sp.Skeleton);

    /**动画开始播放消息事件 */
    ske.setStartListener((animation) => {
      this.emitMsg("OnAnimationStart", detail);
    });

    /**动画播放中特殊的消息事件 */
    ske.setEventListener((animation, event) => {
      this.emitMsg("OnAnimationEvent", event);
      if (event["data"].name == "PlaySound") {
        MsgMgr.emit("ON_PLAY_SOUND", event);
      }
      if (event["data"].name == "atk") {
        this.emitMsg("skill", event);
      }
    });

    /**动画播放结束的消息事件 */
    ske.setCompleteListener((data) => {
      this.emitMsg("OnAnimationCompleted", data);
    });

    this.setTimeScale(scaleList[FightManager.instance.speed]);
    ske.setAnimation(0, name, isLoop);
  }

  public setTimeScale(scale) {
    let role = this.getSub() as GORole;
    if (role.fightShowTime == false) {
      scale = scaleList[0];
    }

    this._timeScale = scale;
    this._render.getComponent(sp.Skeleton).timeScale = scale;
  }

  protected async initRender(path: string) {
    let pathList = path.split("?");
    this.assetMgr.loadPrefab(pathList[0], pathList[1], this.onLoadRender.bind(this));
  }

  protected onLoadRender(prefab) {
    if (isValid(this.getSub()) == false) return;
    this._renderPoint = instantiate(prefab);
    this._renderPoint.name = "renderPoint";
    this._Node.addChild(this._renderPoint);

    this._renderPoint.setScale(this._dbScale);
    this._renderPoint.setSiblingIndex(1);
    this._render = this._renderPoint.getChildByName("render");
    this._render.getComponent(sp.Skeleton).setAnimation(0, "boss_idle", true);
    this._initcallBack && this._initcallBack();

    this.ready();
  }

  public updateSelf(dt: any): void {
    this._render.getComponent(sp.Skeleton).timeScale = this._timeScale;

    if (this._render) {
      let sd = this._Node.getChildByName("sd");
      if (sd.activeInHierarchy == false) {
        sd.active = true;
      }

      sd.setWorldPosition(v3(this._render.getWorldPosition().x, sd.getWorldPosition().y));
    }
  }

  public onPause() {
    if (this._render.getComponent(sp.Skeleton)) {
      this._render.getComponent(sp.Skeleton).timeScale = 0;
    }
  }
  public onResume() {
    if (this._render.getComponent(sp.Skeleton)) {
      this._render.getComponent(sp.Skeleton).timeScale = this._timeScale;
    }
  }

  public static sectionName(): string {
    return "RenderSection";
  }

  public getNode() {
    return this._Node;
  }

  public getHpPoint() {
    if (!this.getSub().getSection(HPSection) || !this.getSub().getSection(HPSection).getRender()) {
      return null;
    }
    return this.getSub().getSection(HPSection).getRender();
  }

  public getPlacePos(sceneId: number, db: IConfigBloodPlace) {
    switch (sceneId) {
      case 1:
        //关卡高度
        return db.place01 || 270;
      case 2:
        //演武场高度
        return db.place02 || 270;
      case 3:
        //天荒古境狩猎
        return db.place03 || 270;
      case 4:
        //古境BOSS
        return db.place04 || 270;
      case 5:
        //战盟首领
        return db.place05 || 270;
      case 6:
        //时空裂隙
        return db.place06 || 270;
      default:
        return 260;
    }
  }

  public getSkinDb(id: number) {
    let db1 = JsonMgr.instance.jsonList.c_leaderSkin[id];
    if (db1) {
      return db1;
    } else {
      return JsonMgr.instance.jsonList.c_monsterShow[id];
    }
  }

  public getBuffPoint() {
    if (!this._buffPoint) {
      let node = new Node("buffPoint");
      node.addComponent(UITransform);
      node.layer = this.getSub().layer;
      this._buffPoint = node;
      this._Node.addChild(this._buffPoint);
      this._buffPoint.setPosition(0, this.getRenderNonePos(), 0);
    }
    return this._buffPoint;
  }

  public getRender(): Node {
    return this._render;
  }

  public getHurtEffect() {
    return this._render.getChildByName("hurt_effect");
  }

  public getRenderScale() {
    return this._render.getScale();
  }

  public getDbScale() {
    return this._dbScale;
  }

  private getRenderNonePos() {
    let hpPoint = this.getHpPoint();
    let pos = ToolExt.transferOfAxes(hpPoint, this._Node).y - 50;
    return pos;
  }

  public onRemove(): void {
    if (this._prefab2) {
      this._prefab2.decRef();
    }
    this.offMsg("OnDirectChange", this.onDirectChange.bind(this));
    this.offMsg("OnPlayAnimation", this.onPlayAnimation.bind(this));

    if (this._render) {
      this._render.getComponent(sp.Skeleton).color = new Color(255, 255, 255, 255);
      let ske = this._render.getComponent(sp.Skeleton);
      ske.setAnimation(0, "boss_idle", true);
      ske.setStartListener(null);
      ske.setEventListener(null);
      ske.setCompleteListener(null);
    }
  }
}
