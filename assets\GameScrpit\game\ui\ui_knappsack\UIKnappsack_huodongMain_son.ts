import { _decorator, is<PERSON><PERSON><PERSON>, tween, v3 } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { JsonMgr } from "../../mgr/JsonMgr";
import { PlayerModule } from "../../../module/player/PlayerModule";
import ToolExt from "../../common/ToolExt";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { ItemType1Enum } from "../../../module/player/PlayerData";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import FmUtils from "../../../lib/utils/FmUtils";
import { dtTime } from "../../BoutStartUp";
import { Sleep } from "../../GameDefine";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
import { KnappsackAudioName } from "../../../module/player/PlayerConfig";

const { ccclass, property } = _decorator;

@ccclass("UIKnappsack_huodongMain_son")
export class UIKnappsack_huodongMain_son extends UINode {
  protected _isAddToBottom: boolean = true;
  protected _isSetParent: boolean = true;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_KNAPSACK}?prefab/ui/UIKnappsack_huodongMain_son`;
  }

  private _huoMainIndex = 0;

  protected onEvtShow(): void {
    this.initHuoDongMain();
  }
  /**活动 */
  private async initHuoDongMain() {
    let huoItem = PlayerModule.data.huoItem;
    let list = Object.keys(huoItem);
    this["huodongScrollView"].active = list.length > 0 ? true : false;
    this["huodongwithout"].active = list.length > 0 ? false : true;
    for (let i = 0; i < list.length; i++) {
      if (i > 2 && i % 4 == 0) {
        await Sleep(0.01);
        if (isValid(this.node) == false) {
          return;
        }
      }
      let node = ToolExt.clone(this["btn_item"], this);
      this["huodongcontent"].addChild(node);
      node.active = true;

      node.setScale(0, 0, 1);
      tween(node)
        .to(0.2, { scale: v3(1, 1, 1) })
        .start();

      let c_info = JsonMgr.instance.getConfigItem(Number(list[i]));
      /**设置显示数量 */
      let myItemNum = huoItem[list[i]];
      /**设置品质和图标 */
      FmUtils.setItemNode(node, c_info.id, myItemNum, false);
      /**设置node的类型界面 */
      node["itemType"] = c_info.type1;
      node["itemId"] = c_info.id;
      this._huoMainIndex++;
    }
  }

  private on_click_btn_item(event) {
    AudioMgr.instance.playEffect(KnappsackAudioName.Effect.点击道具图标);
    let ItemType = event.node["itemType"];
    let itemId = event.node["itemId"];
    this.btnItemSwitch(ItemType, itemId);
  }

  private btnItemSwitch(ItemType: ItemType1Enum, itemId: number) {
    switch (ItemType) {
      case ItemType1Enum.Look_Item:
        UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, { itemId: itemId });
        break;

      case ItemType1Enum.Add_Item:
        UIMgr.instance.showDialog(PlayerRouteName.UIItemUse, { itemId: itemId });
        break;

      case ItemType1Enum.Join_Item:
        UIMgr.instance.showDialog(PlayerRouteName.UIItemJoinPop, { itemId: itemId });
        break;

      case ItemType1Enum.Get_GD_Item:
        UIMgr.instance.showDialog(PlayerRouteName.UIItemUse, { itemId: itemId });
        break;

      case ItemType1Enum.Get_SJ_Item:
        UIMgr.instance.showDialog(PlayerRouteName.UIItemUse, { itemId: itemId });
        break;

      case ItemType1Enum.Get_ZX_Item:
        UIMgr.instance.showDialog(PlayerRouteName.UIItemUseZx, { itemId: itemId });
        break;
    }
  }
}
