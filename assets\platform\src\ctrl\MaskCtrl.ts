import { _decorator, Component, Node } from "cc";
import { RouteItem, RouteMgr, RoutePublic } from "../RouteHelper";
import { EventMgr, MsgEnum } from "../EventHelper";

const { ccclass, property } = _decorator;

@ccclass("MaskCtrl")
export class MaskCtrl extends Component {
  private stopClickClose: boolean = false;

  public routeMgr: RouteMgr = null;

  start() {
    EventMgr.on(MsgEnum.ROUTE_UPDATE, this.onRouteUpdate, this);
  }

  onDestroy(): void {
    EventMgr.off(MsgEnum.ROUTE_UPDATE, this.onRouteUpdate, this);
  }

  onBack(e) {
    if (!this.stopClickClose && this.routeMgr) {
      this.routeMgr.back();
    }
  }

  onRouteUpdate(routeItem: RouteItem) {
    if (routeItem.key == RoutePublic.mask.key) {
      this.stopClickClose = routeItem.args?.stopClickClose;
    }
  }
}
