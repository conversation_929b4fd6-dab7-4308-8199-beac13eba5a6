import { _decorator, instantiate, Label, math, Node } from "cc";
import ToolExt from "db://assets/GameScrpit/game/common/ToolExt";
import { IConfigFriend } from "db://assets/GameScrpit/game/JsonDefine";
import ResMgr from "db://assets/GameScrpit/lib/common/ResMgr";
import { FoucsLayoutManager } from "db://assets/platform/src/core/ui/adapter_view/layout_manager/FoucsLayoutManager";
import { FoucsLinearLayoutManager } from "db://assets/platform/src/core/ui/adapter_view/layout_manager/FoucsLinearLayoutManager";
import { ListAdapter, ViewHolder } from "db://assets/platform/src/core/ui/adapter_view/ListAdapter";
import { FriendModule } from "../../../friend/FriendModule";
import { FriendGoalModule } from "../FriendGoalModule";
const { ccclass, property } = _decorator;
@ccclass("CardViewHolder")
export class CardViewHolder extends ViewHolder {
  //
  updateData(data: IConfigFriend) {
    ToolExt.setItemIcon(this.getNode("bg_friend_img"), data.id);
    this.getNode("node_duigou").active = false;
    if (FriendModule.data.getFriendMessage(data.id)) {
      this.getNode("node_duigou").active = true;
    }
    // else if (
    //   FriendGoalModule.data.friendLabel &&
    //   FriendGoalModule.data.friendLabel.labelMessageMap[data.id] &&
    //   FriendGoalModule.data.friendLabel.labelMessageMap[data.id].count >= data.unlockNum
    // ) {
    //   this.getNode("btn_obtain").active = true;
    //   this.getNode("bg_tiaojian_yidacheng").active = true;
    // } else {
    //   this.getNode("btn_go").active = true;
    //   this.getNode("bg_tiaojian_weidacheng").active = true;
    //   this.getNode("lbl_tiaojian").getComponent(Label).color = math.color("ffffff");
    // }
  }
  onItemClick() {
    this.node.parent.getComponent(FoucsLinearLayoutManager).foucsTo(this.position, this.node);
  }
}
export class CardAdapter extends ListAdapter {
  private _item: Node;
  private _datas: IConfigFriend[];
  constructor(node: Node) {
    super();
    this._item = node;
  }
  setDatas(datas: IConfigFriend[]) {
    this._datas = datas;
    this.notifyDataSetChanged();
  }
  onCreateView(viewType: number): Node {
    let item = instantiate(this._item);
    item.active = true;
    return item;
  }
  onBindData(node: Node, position: number): void {
    node.getComponent(CardViewHolder).updateData(this._datas[position]);
  }
  getCount(): number {
    return this._datas.length;
  }
}
