import { _decorator, Label, Node } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import Formate from "../../../lib/utils/Formate";
import { divide } from "../../../lib/utils/NumbersUtils";
import { UIHeroSkillItem } from "./UIHeroSkillItem";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { HeroModule } from "../../../module/hero/HeroModule";
import { Star } from "../../common/Star";
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Wed Jul 03 2024 15:10:07 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/hero/UIHeroImprintPromotion.ts
 *
 */

@ccclass("UIHeroImprintPromotion")
export class UIHeroImprintPromotion extends UINode {
  private _heroId: number;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_HERO}?prefab/ui/UIHeroImprintPromotion`;
  }
  //=================================================
  public init(args: any): void {
    super.init(args);
    this._heroId = args.heroId;
  }
  protected onEvtShow(): void {
    super.onEvtShow();
    this.refreshUI();
    this.node.on(
      Node.EventType.TOUCH_END,
      () => {
        UIMgr.instance.back();
      },
      this
    );
  }
  private refreshUI() {
    let heroInfo = HeroModule.config.getHeroInfo(this._heroId);
    let level = HeroModule.data.getHeroImprintsLv(this._heroId);
    let nextSkillLvinfo = HeroModule.config.getHeroImprintLvInfo(level);
    let skillInfoId = heroInfo.talentList[level - 1];
    let skillInfo = HeroModule.config.getHeroSkillData(skillInfoId);
    this.getNode("UIHeroSkillItem").getComponent(UIHeroSkillItem).open(skillInfo, 1, this._heroId);
    // 保持与上方格式一致，即使没有使用 Formate.format()，也统一使用模板字符串和空值合并运算符
    this.getNode("improved_title").getComponent(Label).string = `${level}级印记`;

    this.getNode("improved_attack").getComponent(Label).string = `+${Formate.format(
      nextSkillLvinfo?.powerAdd1[1][1] ?? 0
    )}`;
    this.getNode("improved_blood").getComponent(Label).string = `+${Formate.format(
      nextSkillLvinfo?.powerAdd1[0][1] ?? 0
    )}`;
    this.getNode("improved_defense").getComponent(Label).string = `+${Formate.format(
      nextSkillLvinfo?.powerAdd1[2][1] ?? 0
    )}`;
    this.getNode("improved_attack_per").getComponent(Label).string = `+${divide(nextSkillLvinfo?.powerAdd2, 100)}%`;
    this.getNode("improved_blood_per").getComponent(Label).string = `+${divide(nextSkillLvinfo?.powerAdd2, 100)}%`;
    this.getNode("improved_defense_per").getComponent(Label).string = `+${divide(nextSkillLvinfo?.powerAdd2, 100)}%`;
    this.getNode("imprint_star").getComponent(Star).setStar(level);
  }
}
