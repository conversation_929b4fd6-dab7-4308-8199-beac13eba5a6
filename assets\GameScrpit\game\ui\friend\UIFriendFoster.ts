import { _decorator, EventTouch, instantiate, Label, Node, sp, Sprite, tween, UIOpacity, Vec3 } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import ResMgr from "../../../lib/common/ResMgr";
import { FriendGiftResponse } from "../../net/protocol/Friend";
import { FriendModule } from "../../../module/friend/FriendModule";
import { FriendRouteItem } from "../../../module/friend/FriendRoute";
import { FriendBellesColorVertical, FriendBellesColorVerticalURL } from "../../../module/friend/FriendConstant";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { AudioItem, AudioMgr, AudioName } from "../../../../platform/src/AudioHelper";
import ToolExt from "../../common/ToolExt";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import { IConfigFriend } from "../../JsonDefine";
import { FriendAudioName } from "../../../module/friend/FriendConfig";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { LangMgr } from "../../mgr/LangMgr";
import Formate from "../../../lib/utils/Formate";
import MsgEnum from "../../event/MsgEnum";
import MsgMgr from "../../../lib/event/MsgMgr";
import FmUtils from "../../../lib/utils/FmUtils";
import { SpineLoopController } from "db://assets/platform/src/core/ui/components/SpineLoopController";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Sun Jun 23 2024 15:53:58 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/friend/UIFriendFoster.ts
 *
 */

@ccclass("UIFriendFoster")
export class UIFriendFoster extends UINode {
  protected _openAct: boolean = true;
  private _content: Node;
  // private _itemNode: Prefab;
  private _friendName: Label;
  private _talent: Label;
  private _friendship: Label;
  private _bellsName: Label;
  private _friend_bells: Sprite;
  private _friend_color: Sprite;

  private _friendIds: number[];
  private _index: number;

  private _indexItem: number = 0;

  private _itemSelect: number = 0;

  private _itemList: number[] = []; //赠送列表
  private _itemAudio: AudioItem;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_FRIEND}?prefab/ui/UIFriendFoster`;
  }
  public init(args: any): void {
    super.init(args);
    this._friendIds = args[0];
    this._index = args[1];
    let friend: IConfigFriend = FriendModule.config.getFriendById(this._friendIds[this._index]);
    this._itemList = this._itemList.concat(friend.addItemList[0]);
    this._itemList = this._itemList.concat(friend.addItemList[1]);
    FriendModule.viewModel.onViewModelChange = () => {
      this._index = FriendModule.viewModel.index;
      this.refreshUI();
    };
  }

  protected onRegEvent(): void {
    MsgMgr.on(MsgEnum.ON_FRIEND_UPDATE, this.up_item11_num_up, this);
  }

  protected onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_FRIEND_UPDATE, this.up_item11_num_up, this);
  }

  protected onEvtShow(): void {
    super.onEvtShow();
    this._content = this.getNode("content");
    this._friendName = this.getNode("friend_name").getComponent(Label);
    this._talent = this.getNode("talent").getComponent(Label);
    this._friendship = this.getNode("friendship").getComponent(Label);
    this._bellsName = this.getNode("bells_name").getComponent(Label);
    this._friend_bells = this.getNode("friend_bells").getComponent(Sprite);
    this._friend_color = this.getNode("friend_color").getComponent(Sprite);
    this.refreshUI();
    this.playVoice();

    this.getNode("item4_des").active = false;
    this.getNode("item11_des").active = false;
    this.getNode("item11_des_top").active = false;
    this.up_item11_num_up();
  }
  private playVoice() {
    if (this._itemAudio) {
      this._itemAudio.release();
    }
    let friend: IConfigFriend = FriendModule.config.getFriendById(this._friendIds[this._index]);
    if (friend.voice && friend.voice.length > 0) {
      this._itemAudio = AudioMgr.instance.playVoice(friend.voice[0]);
    }
  }
  protected onEvtHide(): void {
    if (this._itemAudio) {
      this._itemAudio.release();
    }
  }
  private refreshUI() {
    FriendModule.service.registerFriendSkillBadge(this.getNode("btn_skill"), this._friendIds[this._index]);

    let friend = FriendModule.config.getFriendById(this._friendIds[this._index]);
    let friendMessage = FriendModule.data.getFriendMessage(friend.id);
    this._friendName.string = `${friend.name}`;
    this._talent.string = `${friendMessage.karma}`;
    this._friendship.string = `${friendMessage.destiny}`;
    this["check_box"].getChildByName("check").active = FriendModule.viewModel.setting_ten_give_gift;
    // log.log(`=+++++++++++++++++${FriendModule.viewModel.setting_ten_give_gift}+++++++++++++++++++`);
    let name = FriendModule.config.getFriendBellesByFameLevel(friendMessage.fameLv)?.name;
    ResMgr.setNodePrefab(
      BundleEnum.BUNDLE_COMMON_FRIEND,
      `prefab/friend_${friend.id}`,
      this.getNode("friend_img"),
      (child: Node) => {
        if (child.name === this.getNode("friend_img").children[0]?.name) {
          child.destroy();
          return true;
        }
        this.getNode("friend_img").destroyAllChildren();
        child.setScale(1.2, 1.2);
      }
    );
    // ResMgr.loadPrefab(`${BundleEnum.BUNDLE_COMMON_FRIEND}?prefab/friend_${friend.id}`, (prefab) => {
    //   this.getNode("friend_img").removeAllChildren();
    //   let item = instantiate(prefab);
    //   item.setScale(1.2, 1.2);
    //   this.getNode("friend_img").addChild(item);
    // });
    ResMgr.loadSpriteFrame(
      FriendBellesColorVerticalURL,
      FriendBellesColorVertical[`${friendMessage.fameLv}`],
      this._friend_bells,
      this
    );
    this._bellsName.string = `${name}`;
    ResMgr.setSpriteFrame(BundleEnum.BUNDLE_COMMON_UI, `atlas_imgs/pingzhi_${friend.color}`, this._friend_color);
  }
  private on_click_btn_back() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
  private on_click_switch_left() {
    AudioMgr.instance.playEffect(1076);
    this._index = (this._index == 0 ? this._friendIds.length : this._index) - 1;
    this.refreshUI();
    this.playVoice();
  }
  private on_click_switch_right() {
    AudioMgr.instance.playEffect(1076);
    this._index = (this._index + 1) % this._friendIds.length;
    this.refreshUI();
    this.playVoice();
  }
  private on_click_btn_skill() {
    AudioMgr.instance.playEffect(FriendAudioName.Effect.点击技能);
    UIMgr.instance.showDialog(FriendRouteItem.UIFriendSkillUpgrade, [this._friendIds, this._index]);
  }
  private on_click_btn_belles() {
    AudioMgr.instance.playEffect(FriendAudioName.Effect.点击美名);
    UIMgr.instance.showDialog(FriendRouteItem.UIFriendBelles, { friendId: this._friendIds[this._index] }, () => {
      this.refreshUI();
    });
  }

  public tick(dt: any): void {
    if (this._indexItem < this._itemList.length) {
      let item = instantiate(this.getNode("Item"));
      this._content.addChild(item);
      if (this._indexItem == this._itemSelect) {
        item.getChildByName("background").active = true;
      } else {
        item.getChildByName("background").active = false;
      }
      let num = PlayerModule.data.getItemNum(this._itemList[this._indexItem]);
      FmUtils.setItemNode(item, this._itemList[this._indexItem], num, true);
      item.on(Node.EventType.TOUCH_END, this.on_item_click, this);
      if (this._itemList[this._indexItem] > 1063) {
        //tianming
        ToolExt.setItemIcon(item.getChildByName("bg_foster_icon"), 10, this);
      } else {
        //yinguo
        ToolExt.setItemIcon(item.getChildByName("bg_foster_icon"), 4, this);
      }
      this._indexItem++;
    }
  }
  //=================================================

  private on_item_click(e: EventTouch) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let index = e.target.getSiblingIndex();
    if (e.target.getChildByName("background").active) {
      e.target.getComponent("ItemCtrl").showDetail();
      return;
    }
    this._itemSelect = index;
    this._content.children.forEach((item) => {
      item.getChildByName("background").active = false;
    });
    e.target.getChildByName("background").active = true;
  }

  private on_click_give_gift() {
    // AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let itemId = this._itemList[this._itemSelect];
    if (PlayerModule.data.getItemNum(itemId) < 1) {
      UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
        itemId: itemId,
        needNum: 1,
      });
      return;
    }
    let curActive = this["check_box"].getChildByName("check").active;
    if (itemId > 1063) {
      this.getNode("effect_friendship").active = true;
      this.getNode("effect_friendship").getComponent(SpineLoopController).play();
    } else {
      this.getNode("effect_talent").active = true;
      this.getNode("effect_talent").getComponent(SpineLoopController).play();
    }
    this.getNode("effect_zengsong").active = true;
    this.getNode("effect_zengsong").getComponent(SpineLoopController).play();

    let friend = FriendModule.config.getFriendById(this._friendIds[this._index]);
    let friendMessage = FriendModule.data.getFriendMessage(friend.id);
    let orKarma = friendMessage?.karma || 0;
    let orDestiny = friendMessage?.destiny || 0;

    FriendModule.api.giveGift(this._friendIds[this._index], itemId, curActive, (data: FriendGiftResponse) => {
      AudioMgr.instance.playEffect(FriendAudioName.Effect.点击赠送);
      // this._content.children[this._itemSelect].getComponent(UIFriendItem1).init(itemId);

      let num = PlayerModule.data.getItemNum(itemId);
      FmUtils.setItemNode(this._content.children[this._itemSelect], itemId, num, true);
      if (itemId > 1063) {
        this.piaozi(this.getNode("effect_friendship"), friendMessage.destiny - orDestiny);
      } else {
        this.piaozi(this.getNode("effect_talent"), friendMessage.karma - orKarma);
      }
      this.refreshUI();
    });
  }
  private piaozi(desNode: Node, val: number) {
    let node = instantiate(this.getNode("lbl_add_effect"));
    node.parent = this.node;
    let desPosition = desNode.getWorldPosition();
    desPosition.x += 100;
    desPosition.y += 20;
    node.getComponent(Label).string = `+${val}`;
    node.setWorldPosition(desPosition);
    tween(node)
      .hide()
      .delay(0.3)
      .show()
      .by(
        0.8,
        { position: new Vec3(0, 100, 0) },
        {
          easing: "sineOut",
          onUpdate: (target, ratio) => {
            node.getComponent(UIOpacity).opacity = 255 * this.transform(ratio);
          },
        }
      )
      .destroySelf()
      .start();
  }
  private transform(ratio) {
    if (ratio < 0.8) {
      return 1;
    }
    return 1 - (ratio - 0.8) / 0.2;
  }
  private on_click_ten_combo() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let curActive = this["check_box"].getChildByName("check").active;
    this["check_box"].getChildByName("check").active = !curActive;
    this["check_box"].getComponent(Sprite).grayscale = curActive;
    FriendModule.viewModel.setting_ten_give_gift = !curActive;
  }

  on_click_btn_open_item4_des() {
    this.getNode("lbl_item4").getComponent(Label).string = LangMgr.txMsgCode(267, []);
    this.getNode("item4_des").active = true;
  }

  on_click_btn_close_item4_des() {
    this.getNode("item4_des").active = false;
  }

  on_click_btn_open_item11_des() {
    this.getNode("lbl_item11").getComponent(Label).string = LangMgr.txMsgCode(268, []);
    this.getNode("item11_des").active = true;
  }

  on_click_btn_close_item11_des() {
    this.getNode("item11_des").active = false;
  }

  private up_item11_num_up() {
    let id = this._friendIds[this._index];
    let data = FriendModule.data.getFriendMessage(id);
    this.getNode("lbl_item_11_num").getComponent(Label).string = Formate.format(data.friendShip);
  }

  on_click_btn_open_item11_des_top() {
    this.getNode("lbl_item11_top").getComponent(Label).string = LangMgr.txMsgCode(269, []);
    this.getNode("item11_des_top").active = true;
  }

  on_click_btn_close_item11_des_top() {
    this.getNode("item11_des_top").active = false;
  }
}
