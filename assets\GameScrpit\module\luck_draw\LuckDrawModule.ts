import { GameData } from "../../game/GameData";
import { UILottery } from "../../game/ui/ui_lottery/UILottery";
import { UILotteryHint } from "../../game/ui/ui_lottery/UILotteryHint";
import { UILotteryProb } from "../../game/ui/ui_lottery/UILotteryProb";
import { UILotteryShop } from "../../game/ui/ui_lottery/UILotteryShop";
import data from "../../lib/data/data";
import { Recording, RecordingMap } from "../../lib/ui/recordingMap";
import { LuckDrawApi } from "./LuckDrawApi";
import { LuckDrawData } from "./LuckDrawData";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class LuckDrawModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): LuckDrawModule {
    if (!GameData.instance.LuckDrawModule) {
      GameData.instance.LuckDrawModule = new LuckDrawModule();
      GameData.instance.LuckDrawModule.onViewLoad();
    }
    return GameData.instance.LuckDrawModule;
  }
  private _data = new LuckDrawData();
  private _api = new LuckDrawApi();
  public static get data() {
    return this.instance._data;
  }
  public static get api() {
    return this.instance._api;
  }
  protected saveKey(): string {
    return this.constructor.name;
  }
  public init(data?: any, completedCallback?: Function) {
    this._data = new LuckDrawData();
    this._api = new LuckDrawApi();

    // 初始化模块
    LuckDrawModule.api.getDraw(() => {
      log.log("玲珑保定数据收到了");
      completedCallback && completedCallback();
    });
  }

  protected onViewLoad(): void {
    let data = new Recording();
    data = {
      node: UILottery,
      uiName: "UILottery",
      keep: false,
      relevanceUIList: [],
    };
    RecordingMap.instance.addRecording(data.uiName, data);

    data = new Recording();
    data = {
      node: UILotteryProb,
      uiName: "UILotteryProb",
      keep: false,
      relevanceUIList: [,],
    };
    RecordingMap.instance.addRecording(data.uiName, data);

    data = new Recording();
    data = {
      node: UILotteryShop,
      uiName: "UILotteryShop",
      keep: false,
      relevanceUIList: [,],
    };
    RecordingMap.instance.addRecording(data.uiName, data);

    data = new Recording();
    data = {
      node: UILotteryHint,
      uiName: "UILotteryHint",
      keep: false,
      relevanceUIList: [,],
    };
    RecordingMap.instance.addRecording(data.uiName, data);
  }
}
