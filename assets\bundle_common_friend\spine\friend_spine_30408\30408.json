{"skeleton": {"hash": "sZDqem2Mp5PpyNsjEKyOKsQWHVA", "spine": "3.8.75", "x": -429.75, "y": -704.9, "width": 857.79, "height": 1322.95, "images": "./images/", "audio": "D:/仙友spine/九天玄女"}, "bones": [{"name": "root", "scaleX": 0.82, "scaleY": 0.82}, {"name": "bone", "parent": "root", "length": 398.06, "rotation": -90.3, "x": 649.11, "y": 276.22}, {"name": "st", "parent": "bone", "length": 296.49, "rotation": 150.66, "x": 845.82, "y": -686.91}, {"name": "st2", "parent": "st", "x": 461.1, "y": -26.85}, {"name": "st3", "parent": "st", "x": 534.32, "y": 183.56, "color": "abe323ff"}, {"name": "s2", "parent": "st3", "length": 203.57, "rotation": -174.02, "x": -22.32, "y": 13.81}, {"name": "s3", "parent": "s2", "length": 139.8, "rotation": -5.98, "x": 226.96, "y": -1.19}, {"name": "s4", "parent": "s3", "length": 139.12, "rotation": 11.2, "x": 161.87, "y": -9.14}, {"name": "s5", "parent": "s4", "length": 180.86, "rotation": -6.94, "x": 169.6, "y": 1.32}, {"name": "s6", "parent": "s2", "length": 131.72, "rotation": -33.6, "x": 343.94, "y": -62.26}, {"name": "s7", "parent": "s6", "length": 124.83, "rotation": 5.83, "x": 143.84, "y": -3.09}, {"name": "s8", "parent": "s7", "length": 121.36, "rotation": -1.07, "x": 140.48, "y": 2.7}, {"name": "l1", "parent": "st3", "x": 173.67, "y": 41.48}, {"name": "l2", "parent": "l1", "x": 93.36, "y": -18.18}, {"name": "f1", "parent": "l1", "length": 99.42, "rotation": 74.64, "x": 146.9, "y": 87.86}, {"name": "f3", "parent": "f1", "length": 41.91, "rotation": -157.04, "x": -5.89, "y": -77.83}, {"name": "f4", "parent": "f3", "length": 34.76, "rotation": -3.16, "x": 48.79, "y": -2.2}, {"name": "f5", "parent": "f4", "length": 30.91, "rotation": -13.73, "x": 42.54, "y": -3.5}, {"name": "f6", "parent": "f5", "length": 26.05, "rotation": -67.58, "x": 44.09, "y": -6.01}, {"name": "f7", "parent": "f6", "length": 37.15, "rotation": -54.61, "x": 33.37, "y": -8.44}, {"name": "f8", "parent": "f7", "length": 37.97, "rotation": -52.96, "x": 43.85, "y": -3.27}, {"name": "f2", "parent": "l1", "length": 46.45, "rotation": -115.08, "x": 112.03, "y": -34.16}, {"name": "f9", "parent": "f2", "length": 51.75, "rotation": -6.42, "x": 55.17, "y": -0.5}, {"name": "f10", "parent": "f9", "length": 41.06, "rotation": -14.51, "x": 64.6, "y": 0.31}, {"name": "f11", "parent": "f10", "length": 46.84, "rotation": -23.44, "x": 51.14, "y": -1.63}, {"name": "f12", "parent": "f11", "length": 39.52, "rotation": -48.34, "x": 53.67, "y": -1.72}, {"name": "f14", "parent": "f1", "length": 113.91, "rotation": 112.91, "x": -31.92, "y": 186.86}, {"name": "f15", "parent": "f14", "length": 111.26, "rotation": -5.91, "x": 134.3, "y": -3}, {"name": "f16", "parent": "f15", "length": 110.4, "rotation": -12.77, "x": 134.31, "y": -5.5}, {"name": "f17", "parent": "f16", "length": 104.98, "rotation": -27, "x": 129.2, "y": 3.8}, {"name": "f18", "parent": "f17", "length": 84.81, "rotation": -44.68, "x": 132.73, "y": -1.18}, {"name": "f19", "parent": "f18", "length": 73.89, "rotation": 14.33, "x": 97.26, "y": -2.77}, {"name": "f13", "parent": "f1", "length": 71.31, "rotation": 100.15, "x": 45.37, "y": 228.89}, {"name": "f20", "parent": "f13", "length": 99.54, "rotation": -59.36, "x": 85.86, "y": -2.6}, {"name": "f21", "parent": "f20", "length": 101.73, "rotation": -52.1, "x": 109.08, "y": -1.39}, {"name": "f22", "parent": "f21", "length": 84.34, "rotation": 0.2, "x": 110.72, "y": 0.29}, {"name": "f23", "parent": "f22", "length": 79.21, "rotation": 25.15, "x": 87.25, "y": 4.34}, {"name": "f24", "parent": "f23", "length": 74.81, "rotation": 18.87, "x": 85.49, "y": -2.33}, {"name": "f25", "parent": "f1", "length": 86.93, "rotation": 107.82, "x": 1.21, "y": 234.84}, {"name": "f26", "parent": "f25", "length": 79.86, "rotation": -15.7, "x": 107.56, "y": -5.85}, {"name": "f27", "parent": "f26", "length": 83.84, "rotation": -24.39, "x": 90.31, "y": 2.57}, {"name": "f28", "parent": "f27", "length": 77.82, "rotation": -7.95, "x": 99.2, "y": 1.5}, {"name": "f29", "parent": "f28", "length": 98.39, "rotation": -27.05, "x": 100.79, "y": -7.14}, {"name": "f30", "parent": "f29", "length": 113.64, "rotation": -53.29, "x": 102.65, "y": -9.77}, {"name": "f32", "parent": "f1", "length": 118.1, "rotation": -2.54, "x": 116.6, "y": 110.29}, {"name": "f31", "parent": "f32", "length": 61.24, "rotation": 90.09, "x": 10.48, "y": 78.57}, {"name": "f33", "parent": "f31", "length": 38.89, "rotation": 1.81, "x": 64.77, "y": -0.72}, {"name": "f34", "parent": "f33", "length": 34.34, "rotation": 15.39, "x": 46.3, "y": 2.27}, {"name": "f35", "parent": "f34", "length": 36.87, "rotation": 16.69, "x": 40.57, "y": -2.09}, {"name": "f37", "parent": "f32", "length": 55.89, "rotation": 55.51, "x": 29.65, "y": 90.27}, {"name": "f38", "parent": "f37", "length": 38.58, "rotation": 25.72, "x": 61.42, "y": 2.21}, {"name": "f39", "parent": "f38", "length": 36.9, "rotation": 19.44, "x": 41.14, "y": 0.51}, {"name": "f40", "parent": "f39", "length": 41.29, "rotation": 12.6, "x": 41.99, "y": 0.59}, {"name": "f41", "parent": "f32", "length": 70.44, "rotation": 7.43, "x": 161.55, "y": 60.78}, {"name": "f42", "parent": "f41", "length": 57.68, "rotation": -6.18, "x": 80.92, "y": 3.03}, {"name": "f43", "parent": "f42", "length": 53.81, "rotation": 4.08, "x": 62.36, "y": 0.63}, {"name": "f44", "parent": "f43", "length": 73.71, "rotation": -8.67, "x": 58.83, "y": 1.06}, {"name": "f36", "parent": "f32", "length": 45.64, "rotation": 58.26, "x": 85.32, "y": 89.88}, {"name": "f45", "parent": "f36", "length": 32.43, "rotation": 20.01, "x": 52.44, "y": 1.18}, {"name": "f46", "parent": "f45", "length": 25.71, "rotation": 29.09, "x": 43.64, "y": 2.27}, {"name": "f48", "parent": "f32", "length": 49.06, "rotation": 17.39, "x": 105.56, "y": -71.15}, {"name": "f49", "parent": "f48", "length": 50.74, "rotation": -22.61, "x": 53.59, "y": -2.97}, {"name": "f50", "parent": "f49", "length": 53.17, "rotation": -8.14, "x": 59.42, "y": 4.07}, {"name": "f51", "parent": "f50", "length": 65.37, "rotation": -13.69, "x": 56.82, "y": 0.74}, {"name": "f52", "parent": "f51", "length": 41.19, "rotation": -7.52, "x": 70.61, "y": -2.28}, {"name": "f47", "parent": "f1", "length": 30.72, "rotation": 139.14, "x": -44.72, "y": 114}, {"name": "f53", "parent": "f47", "length": 18.84, "rotation": 3.99, "x": 37.42, "y": 0.4}, {"name": "f54", "parent": "f53", "length": 21.21, "rotation": -27.7, "x": 23.67, "y": -0.69}, {"name": "f55", "parent": "f54", "length": 20.44, "rotation": -14.81, "x": 28.76, "y": 1.27}, {"name": "f56", "parent": "f55", "length": 21.5, "rotation": -17.33, "x": 22.91, "y": -0.46}, {"name": "f58", "parent": "f1", "length": 22.88, "rotation": 129.64, "x": -165.36, "y": 104.87}, {"name": "f59", "parent": "f58", "length": 24.35, "rotation": -9.9, "x": 28.11, "y": -2.37}, {"name": "f60", "parent": "f59", "length": 22.12, "rotation": -17.92, "x": 28.91, "y": -3.18}, {"name": "s1", "parent": "st", "length": 238.21, "rotation": -39.67, "x": 347.63, "y": -36.29}, {"name": "s9", "parent": "s1", "length": 28.94, "rotation": 53.5, "x": 283.79, "y": -37}, {"name": "s10", "parent": "s9", "length": 24.08, "rotation": 47.82, "x": 32.95, "y": 1.14}, {"name": "s11", "parent": "s10", "length": 26.72, "rotation": 37.67, "x": 27.44, "y": -0.74}, {"name": "s12", "parent": "s1", "length": 32.95, "rotation": 126.97, "x": 308.61, "y": 22.82}, {"name": "s13", "parent": "s1", "length": 27.61, "rotation": 97.39, "x": 330.56, "y": -9.03}, {"name": "s14", "parent": "s13", "length": 26.47, "rotation": 13.02, "x": 33.11, "y": -0.04}, {"name": "s15", "parent": "s1", "length": 41.46, "rotation": 37.58, "x": 350.75, "y": 0.46}, {"name": "bone2", "parent": "root", "length": 222.03, "rotation": -87.93, "x": 470.82, "y": 725.18}, {"name": "zs6", "parent": "bone2", "length": 69.01, "rotation": -0.73, "x": -11.56, "y": -107.03}, {"name": "zs7", "parent": "zs6", "length": 41.99, "rotation": -43.22, "x": 9.35, "y": -15.32}, {"name": "zs8", "parent": "zs7", "length": 49.41, "rotation": 2.63, "x": 46.47, "y": -1.77}, {"name": "zs9", "parent": "zs8", "length": 39.35, "rotation": -9.64, "x": 55.67, "y": -2.55}, {"name": "zs10", "parent": "zs9", "length": 48.17, "rotation": -0.65, "x": 43.61, "y": -0.86}, {"name": "zs11", "parent": "zs6", "length": 45.81, "rotation": -29.42, "x": 76.95, "y": -6.12}, {"name": "zs12", "parent": "zs11", "length": 34.26, "rotation": 3.93, "x": 54, "y": 0.7}, {"name": "zs13", "parent": "zs12", "length": 34.51, "rotation": 9.68, "x": 41.21, "y": -1.02}, {"name": "zs5", "parent": "bone2", "length": 79.71, "rotation": -7.78, "x": 201.86, "y": -109.94}, {"name": "zs14", "parent": "zs5", "length": 76.8, "rotation": -6.76, "x": 87.88, "y": 5.17}, {"name": "zs15", "parent": "zs14", "length": 102.51, "rotation": -3.03, "x": 90.35, "y": 22.2}, {"name": "zs16", "parent": "zs15", "length": 88.24, "rotation": -3.08, "x": 103.56, "y": 20.49}, {"name": "zs17", "parent": "zs16", "length": 77.44, "rotation": 0.36, "x": 58.71, "y": 22.35}, {"name": "zs18", "parent": "zs17", "length": 80.75, "rotation": 6.04, "x": 70.41, "y": 18.46}, {"name": "bone3", "parent": "root", "length": 64.17, "rotation": -0.86, "x": -16.7, "y": 808.07}, {"name": "zw3", "parent": "bone3", "x": 122.5, "y": -143.56}, {"name": "sp1", "parent": "l1", "length": 11.87, "rotation": -165.42, "x": -37.59, "y": 63.69}, {"name": "bone4", "parent": "root", "length": 245.94, "rotation": 2.37, "x": 277.19, "y": -757.98}, {"name": "x1", "parent": "bone4", "length": 130.26, "rotation": 54.47, "x": -424.62, "y": -27.52}, {"name": "x2", "parent": "x1", "length": 119.21, "rotation": 3.15, "x": 144.02, "y": 5.52}, {"name": "x3", "parent": "x2", "length": 96.67, "rotation": -44.29, "x": 129.48, "y": 2.57}, {"name": "x4", "parent": "x3", "length": 116.05, "rotation": -0.45, "x": 104.45, "y": 0.83}, {"name": "x5", "parent": "x4", "length": 121.39, "rotation": 19.01, "x": 129.44, "y": -0.64}, {"name": "x6", "parent": "x5", "length": 96.32, "rotation": 26.85, "x": 136.73, "y": 1.86}, {"name": "x7", "parent": "x6", "length": 65.49, "rotation": 26.34, "x": 108.34, "y": 8.3}, {"name": "bone5", "parent": "root", "length": 51.45, "rotation": 5.44, "x": -127.06, "y": 801.67}, {"name": "zs2", "parent": "bone5", "x": -9.21, "y": -156.74}, {"name": "bone6", "parent": "root", "length": 57.19, "rotation": 5.71, "x": -326.25, "y": 795.96}, {"name": "zs1", "parent": "bone6", "x": -44.49, "y": -158.96}, {"name": "bone7", "parent": "root", "length": 71.64, "rotation": 9.14, "x": 184.32, "y": 732.87}, {"name": "zs4", "parent": "bone7", "x": 16.11, "y": -299.04}, {"name": "zs19", "parent": "bone2", "length": 17.68, "rotation": -73.33, "x": -30.48, "y": -183.89}, {"name": "zs20", "parent": "zs19", "length": 16.05, "rotation": 7.82, "x": 18.15, "y": -0.48}, {"name": "zs21", "parent": "zs20", "length": 16.63, "rotation": 11.13, "x": 19.52, "y": 0.27}, {"name": "zs22", "parent": "bone2", "length": 53.38, "rotation": 130.06, "x": 1324.13, "y": -260.26}, {"name": "zs23", "parent": "zs22", "length": 52.81, "rotation": -7.31, "x": 74.68, "y": 1.07}, {"name": "st4", "parent": "st", "x": 422.99, "y": -33.74}, {"name": "zs24", "parent": "bone2", "length": 79.71, "rotation": -7.78, "x": 201.86, "y": -109.94}, {"name": "zs25", "parent": "zs24", "length": 76.8, "rotation": -6.76, "x": 87.88, "y": 5.17}, {"name": "zs26", "parent": "zs25", "length": 102.51, "rotation": -3.03, "x": 90.35, "y": 22.2}, {"name": "zs27", "parent": "zs26", "length": 88.24, "rotation": -3.08, "x": 103.56, "y": 20.49}, {"name": "zs28", "parent": "zs27", "length": 77.44, "rotation": 0.36, "x": 58.71, "y": 22.35}, {"name": "zs29", "parent": "zs28", "length": 80.75, "rotation": 6.04, "x": 70.41, "y": 18.46}, {"name": "zs30", "parent": "bone2", "length": 79.71, "rotation": -7.78, "x": 201.86, "y": -109.94}, {"name": "zs31", "parent": "zs30", "length": 76.8, "rotation": -6.76, "x": 87.88, "y": 5.17}, {"name": "zs32", "parent": "zs31", "length": 102.51, "rotation": -3.03, "x": 90.35, "y": 22.2}, {"name": "zs33", "parent": "zs32", "length": 88.24, "rotation": -3.08, "x": 103.56, "y": 20.49}, {"name": "zs34", "parent": "zs33", "length": 77.44, "rotation": 0.36, "x": 58.71, "y": 22.35}, {"name": "zs35", "parent": "zs34", "length": 80.75, "rotation": 6.04, "x": 70.41, "y": 18.46}, {"name": "zs36", "parent": "bone2", "length": 53.38, "rotation": 130.06, "x": 1320.79, "y": -260.14}, {"name": "zs37", "parent": "zs36", "length": 52.81, "rotation": -7.31, "x": 74.68, "y": 1.07}, {"name": "zs38", "parent": "bone2", "length": 53.38, "rotation": 130.06, "x": 1327.47, "y": -260.38}, {"name": "zs39", "parent": "zs38", "length": 52.81, "rotation": -7.31, "x": 74.68, "y": 1.07}, {"name": "bone8", "parent": "root", "length": 71.64, "rotation": 9.14, "x": 184.32, "y": 732.2}, {"name": "zs40", "parent": "bone8", "x": 16.11, "y": -299.04}, {"name": "bone10", "parent": "root", "length": 51.45, "rotation": 5.44, "x": -127.06, "y": 799.12}, {"name": "zs3", "parent": "bone10", "x": -9.21, "y": -156.74}, {"name": "bone9", "parent": "root", "length": 64.17, "rotation": -0.86, "x": -16.7, "y": 807.95}, {"name": "zw4", "parent": "bone9", "x": 122.5, "y": -143.56}, {"name": "bone11", "parent": "root", "length": 245.94, "rotation": 2.37, "x": 277.19, "y": -757.98}, {"name": "x8", "parent": "bone11", "length": 130.26, "rotation": 54.47, "x": -424.62, "y": -27.52}, {"name": "x9", "parent": "x8", "length": 119.21, "rotation": 3.15, "x": 144.02, "y": 5.52}, {"name": "x10", "parent": "x9", "length": 96.67, "rotation": -44.29, "x": 129.48, "y": 2.57}, {"name": "x11", "parent": "x10", "length": 116.05, "rotation": -0.45, "x": 104.45, "y": 0.83}, {"name": "x12", "parent": "x11", "length": 121.39, "rotation": 19.01, "x": 129.44, "y": -0.64}, {"name": "x13", "parent": "x12", "length": 96.32, "rotation": 26.85, "x": 136.73, "y": 1.86}, {"name": "x14", "parent": "x13", "length": 65.49, "rotation": 26.34, "x": 108.34, "y": 8.3}, {"name": "bone12", "parent": "root", "length": 245.94, "rotation": 2.37, "x": 277.19, "y": -757.98}, {"name": "x15", "parent": "bone12", "length": 130.26, "rotation": 54.47, "x": -424.62, "y": -27.52}, {"name": "x16", "parent": "x15", "length": 119.21, "rotation": 3.15, "x": 144.02, "y": 5.52}, {"name": "x17", "parent": "x16", "length": 96.67, "rotation": -44.29, "x": 129.48, "y": 2.57}, {"name": "x18", "parent": "x17", "length": 116.05, "rotation": -0.45, "x": 104.45, "y": 0.83}, {"name": "x19", "parent": "x18", "length": 121.39, "rotation": 19.01, "x": 129.44, "y": -0.64}, {"name": "x20", "parent": "x19", "length": 96.32, "rotation": 26.85, "x": 136.73, "y": 1.86}, {"name": "x21", "parent": "x20", "length": 65.49, "rotation": 26.34, "x": 108.34, "y": 8.3}, {"name": "bone13", "parent": "root", "x": 945.7, "y": -281.15}, {"name": "st5", "parent": "st", "length": 92.72, "rotation": -149.69, "x": 135.15, "y": -113.09}, {"name": "st6", "parent": "st5", "length": 76.75, "rotation": -11.19, "x": 102.29, "y": -10.89}, {"name": "st7", "parent": "st6", "length": 81.03, "rotation": -22.54, "x": 89.4, "y": -3.13}, {"name": "st8", "parent": "st7", "length": 62.51, "rotation": -24.62, "x": 88.53, "y": 2.31}], "slots": [{"name": "bg", "bone": "root", "attachment": "bg"}, {"name": "zs1", "bone": "zs1", "attachment": "zs1"}, {"name": "zs2", "bone": "zs2", "attachment": "zs2"}, {"name": "zs3", "bone": "zs3", "attachment": "zs2"}, {"name": "zw3", "bone": "zw3", "attachment": "zw3"}, {"name": "zw4", "bone": "zw4", "attachment": "zw3"}, {"name": "zs4", "bone": "zs4", "attachment": "zs4"}, {"name": "zs13", "bone": "zs40", "attachment": "zs4"}, {"name": "zs5", "bone": "zs5", "attachment": "zs5"}, {"name": "zs9", "bone": "zs24", "attachment": "zs5"}, {"name": "zs10", "bone": "zs30", "attachment": "zs5"}, {"name": "zs6", "bone": "zs6", "attachment": "zs6"}, {"name": "zs7", "bone": "zs19", "attachment": "zs7"}, {"name": "f9", "bone": "f58", "attachment": "f9"}, {"name": "s1", "bone": "s1", "attachment": "s1"}, {"name": "st", "bone": "st", "attachment": "st"}, {"name": "zs8", "bone": "zs22", "attachment": "zs8"}, {"name": "zs11", "bone": "zs36", "attachment": "zs8"}, {"name": "zs12", "bone": "zs38", "attachment": "zs8"}, {"name": "f1", "bone": "f3", "attachment": "f1"}, {"name": "l1", "bone": "l1", "attachment": "l1"}, {"name": "f2", "bone": "f2", "attachment": "f2"}, {"name": "f3", "bone": "f14", "attachment": "f3"}, {"name": "sp1", "bone": "sp1", "attachment": "sp1"}, {"name": "f4", "bone": "f32", "attachment": "f4"}, {"name": "f5", "bone": "f48", "attachment": "f5"}, {"name": "f6", "bone": "f41", "attachment": "f6"}, {"name": "f7", "bone": "f31", "attachment": "f7"}, {"name": "f8", "bone": "f37", "attachment": "f8"}, {"name": "s2", "bone": "s2", "attachment": "s2"}, {"name": "x1", "bone": "x1", "attachment": "x1"}, {"name": "x2", "bone": "x8", "attachment": "x1"}, {"name": "x3", "bone": "x15", "attachment": "x1"}], "transform": [{"name": "st3", "bones": ["st2"], "target": "st3", "x": -87.69, "y": -218.64, "rotateMix": -0.357, "translateMix": -0.357, "scaleMix": -0.357, "shearMix": -0.357}], "skins": [{"name": "default", "attachments": {"zs12": {"zs8": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [31.46, -138.67, -106.47, -13.88, -8.51, 94.38, 129.41, -30.41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 66, "height": 52}}, "bg": {"bg": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [375, -681.54, -376, -681.54, -376, 753.71, 375, 753.71], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 270, "height": 453}}, "st": {"st": {"type": "mesh", "uvs": [0.45024, 0, 0.61752, 0.02152, 0.67328, 0.0965, 0.73073, 0.17376, 0.76289, 0.21701, 0.80967, 0.27992, 0.86156, 0.3497, 0.84742, 0.39473, 0.8264, 0.46166, 0.81581, 0.49539, 0.7092, 0.56441, 0.65665, 0.59842, 0.68075, 0.6474, 0.72883, 0.70456, 0.78556, 0.77199, 0.83662, 0.8327, 0.89351, 0.90033, 0.97735, 1, 0.72428, 1, 0.43197, 1, 0.1866, 1, 0, 1, 0, 0.94132, 0, 0.87897, 0, 0.8012, 0, 0.7268, 0.05851, 0.6828, 0.13994, 0.62156, 0.21074, 0.5683, 0.26409, 0.51327, 0.26723, 0.44897, 0.27036, 0.38465, 0.27362, 0.31778, 0.27686, 0.25121, 0.27988, 0.18913, 0.32374, 0.13569, 0.37343, 0.07513, 0.43509, 0, 0.59079, 0.162, 0.53664, 0.20564, 0.6623, 0.21939, 0.57067, 0.26889, 0.71991, 0.27634, 0.56419, 0.32952, 0.7615, 0.33948, 0.5844, 0.39366, 0.75089, 0.39425, 0.5578, 0.45527, 0.72094, 0.46315, 0.52849, 0.51363, 0.70663, 0.5193, 0.23167, 0.56909, 0.4991, 0.57068, 0.63267, 0.56918, 0.28988, 0.61485, 0.48388, 0.61171, 0.33289, 0.68078, 0.5267, 0.67436, 0.33075, 0.7295, 0.55354, 0.7226, 0.30061, 0.79515, 0.52985, 0.79078, 0.2622, 0.84906, 0.49507, 0.842, 0.20757, 0.92691, 0.4615, 0.91359], "triangles": [20, 64, 19, 19, 64, 65, 64, 20, 22, 20, 21, 22, 22, 23, 64, 18, 16, 17, 19, 65, 18, 18, 65, 16, 64, 62, 65, 65, 15, 16, 65, 63, 15, 65, 62, 63, 15, 63, 61, 63, 60, 61, 15, 61, 14, 14, 59, 13, 14, 61, 59, 59, 12, 13, 59, 57, 12, 57, 11, 12, 31, 32, 43, 32, 33, 41, 33, 39, 41, 33, 34, 39, 39, 38, 40, 40, 38, 3, 34, 35, 39, 39, 35, 38, 38, 2, 3, 35, 36, 38, 38, 36, 2, 1, 2, 36, 36, 37, 0, 36, 0, 1, 47, 45, 48, 8, 46, 7, 48, 45, 46, 50, 48, 9, 9, 48, 8, 48, 46, 8, 31, 43, 45, 41, 39, 40, 46, 45, 44, 6, 7, 44, 7, 46, 44, 45, 43, 44, 44, 5, 6, 43, 42, 44, 44, 42, 5, 43, 41, 42, 42, 4, 5, 41, 40, 42, 42, 40, 4, 43, 32, 41, 40, 3, 4, 64, 23, 62, 23, 24, 62, 62, 60, 63, 62, 24, 60, 24, 25, 60, 60, 58, 61, 60, 25, 58, 61, 58, 59, 58, 57, 59, 58, 56, 57, 25, 26, 58, 58, 26, 56, 56, 27, 54, 56, 26, 27, 56, 55, 57, 56, 54, 55, 57, 55, 11, 54, 28, 51, 54, 27, 28, 55, 54, 52, 55, 53, 11, 55, 52, 53, 52, 54, 51, 11, 53, 10, 51, 29, 52, 52, 49, 53, 52, 29, 49, 53, 50, 10, 53, 49, 50, 51, 28, 29, 10, 50, 9, 49, 47, 50, 50, 47, 48, 47, 49, 30, 49, 29, 30, 45, 47, 31, 47, 30, 31], "vertices": [2, 3, 243.48, 300.62, 8e-05, 4, 175.42, 93.15, 0.99992, 2, 3, 265.16, 213.76, 0.01665, 4, 197.1, 6.29, 0.98335, 2, 3, 212.67, 153.24, 0.09604, 4, 144.61, -54.23, 0.90396, 2, 3, 158.6, 90.89, 0.34202, 4, 90.54, -116.58, 0.65798, 2, 3, 128.33, 55.98, 0.53998, 4, 60.27, -151.49, 0.46002, 1, 3, 84.3, 5.21, 1, 1, 3, 35.46, -51.11, 1, 3, 2, 458.64, -89.49, 0.00474, 3, -7.63, -65.58, 0.60673, 118, 35.65, -55.75, 0.38853, 4, 2, 394.6, -111, 0.18476, 3, -71.66, -87.09, 0.01001, 118, -28.39, -77.26, 0.80451, 158, -225.04, 129.14, 0.00071, 3, 2, 362.33, -121.83, 0.30399, 118, -60.65, -88.1, 0.69187, 158, -191.72, 122.21, 0.00414, 3, 2, 275.67, -104.99, 0.66836, 118, -147.31, -71.26, 0.24536, 158, -125.4, 63.93, 0.08628, 3, 2, 232.96, -96.69, 0.61708, 118, -190.03, -62.96, 0.06353, 158, -92.71, 35.21, 0.31939, 3, 2, 166.49, -135.81, 0.18518, 118, -256.5, -102.07, 0.0049, 158, -15.59, 35.43, 0.80992, 1, 158, 44.75, 50.36, 1, 3, 158, 115.96, 67.98, 0.90917, 159, -1.9, 80.03, 0.07633, 160, -116.2, 41.82, 0.0145, 4, 158, 180.05, 83.84, 0.54387, 159, 57.9, 108.02, 0.29902, 160, -71.7, 90.6, 0.15511, 161, -182.44, 13.49, 0.002, 4, 158, 251.46, 101.51, 0.26742, 159, 124.53, 139.21, 0.3006, 160, -22.11, 144.94, 0.41405, 161, -160.01, 83.55, 0.01793, 4, 158, 356.69, 127.55, 0.17487, 159, 222.71, 185.17, 0.2463, 160, 50.96, 225.01, 0.55089, 161, -126.95, 186.79, 0.02794, 4, 158, 334.32, -2.03, 0.10065, 159, 225.9, 53.71, 0.1507, 160, 104.29, 104.82, 0.63202, 161, -28.39, 99.75, 0.11664, 1, 161, 85.45, -0.78, 1, 3, 2, -252.41, -18.42, 0.21587, 160, 217.6, -150.54, 0.02121, 161, 181.01, -85.17, 0.76293, 2, 2, -286.39, 72.39, 0.32395, 161, 253.68, -149.35, 0.67605, 3, 2, -231.65, 92.87, 0.38549, 160, 203.5, -262.86, 0.01157, 161, 215, -193.16, 0.60294, 3, 2, -173.49, 114.63, 0.55526, 160, 146.73, -288.05, 0.02735, 161, 173.89, -239.7, 0.41739, 3, 2, -100.94, 141.78, 0.80327, 160, 75.93, -319.46, 0.01389, 161, 122.61, -297.77, 0.18284, 3, 2, -31.54, 167.75, 0.94, 160, 8.2, -349.52, 0.0003, 161, 73.56, -353.31, 0.05969, 2, 2, 20.17, 154.64, 0.97993, 161, 21.76, -366.04, 0.02007, 3, 2, 87.1, 129.88, 0.99976, 4, -447.22, -53.68, 3e-05, 161, -48.95, -375.64, 0.0002, 2, 2, 151.18, 122.15, 0.99533, 4, -383.14, -61.41, 0.00467, 4, 2, 212.6, 123.26, 0.96039, 3, -253.66, 147.17, 0.0006, 118, -210.39, 157, 4e-05, 4, -321.72, -60.3, 0.03897, 4, 2, 269.97, 151.76, 0.82589, 3, -196.29, 175.67, 0.01357, 118, -153.01, 185.49, 0.01253, 4, -264.35, -31.8, 0.14802, 4, 2, 327.37, 180.26, 0.57933, 3, -138.89, 204.17, 0.04275, 118, -95.61, 214, 0.03892, 4, -206.95, -3.3, 0.33899, 4, 2, 387.04, 209.9, 0.30418, 3, -79.23, 233.8, 0.05185, 118, -35.95, 243.63, 0.03493, 4, -147.28, 26.34, 0.60905, 4, 2, 446.45, 239.4, 0.10344, 3, -19.82, 263.31, 0.01741, 118, 23.46, 273.14, 0.01163, 4, -87.87, 55.84, 0.86751, 3, 2, 501.84, 266.91, 0.01776, 118, 78.85, 300.65, 0.00074, 4, -32.48, 83.35, 0.9815, 2, 2, 559.55, 271.64, 0.00027, 4, 25.23, 88.09, 0.99973, 1, 4, 90.61, 93.45, 1, 1, 4, 171.74, 100.11, 1, 2, 3, 134.97, 160.64, 0.15456, 4, 66.91, -46.83, 0.84544, 4, 2, 549.66, 141.29, 0.00318, 3, 83.4, 165.2, 0.15243, 118, 126.67, 175.03, 0.00103, 4, 15.34, -42.27, 0.84336, 3, 2, 568.07, 77.17, 0.00058, 3, 101.81, 101.08, 0.71478, 4, 33.75, -106.39, 0.28464, 4, 2, 502.22, 96.21, 0.01977, 3, 35.96, 120.12, 0.70153, 118, 79.24, 129.95, 0.01148, 4, -32.09, -87.35, 0.26722, 1, 3, 65.65, 48.1, 1, 4, 2, 447.27, 70.96, 0.04357, 3, -18.99, 94.87, 0.81495, 118, 24.29, 104.7, 0.04375, 4, -87.05, -112.59, 0.09773, 1, 3, 20.16, -0.39, 1, 4, 2, 395.7, 31.82, 0.22331, 3, -70.56, 55.73, 0.38818, 118, -27.28, 65.56, 0.31293, 4, -138.62, -151.74, 0.07559, 2, 3, -30.65, -21.02, 0.53639, 118, 12.63, -11.19, 0.46361, 4, 2, 335, 15.36, 0.72058, 3, -131.26, 39.27, 0.01558, 118, -87.99, 49.09, 0.23273, 4, -199.32, -168.2, 0.03111, 3, 2, 367.68, -63.25, 0.29316, 118, -55.31, -29.51, 0.7051, 158, -225.9, 74.33, 0.00174, 3, 2, 276.49, 1.65, 0.99971, 3, -189.77, 25.56, 2e-05, 4, -257.83, -181.91, 0.00027, 3, 2, 314.76, -82.81, 0.55619, 118, -108.22, -49.08, 0.41915, 158, -170.34, 64.51, 0.02467, 2, 2, 155.57, 112.17, 0.99441, 4, -378.75, -71.39, 0.00559, 3, 2, 219.12, -11.41, 0.97728, 118, -203.87, 22.33, 0.00158, 158, -123.81, -45.4, 0.02115, 3, 2, 252.89, -72.06, 0.74769, 118, -170.1, -38.33, 0.10848, 158, -122.35, 24.01, 0.14382, 2, 2, 129.41, 64.13, 0.99935, 4, -404.91, -119.43, 0.00065, 3, 2, 179.3, -23.52, 0.90173, 118, -243.69, 10.21, 0.00025, 158, -83.32, -55.04, 0.09802, 1, 2, 72.01, 21.82, 1, 3, 2, 113.3, -70.25, 0.37569, 158, -2.75, -48.01, 0.6118, 159, -95.85, -56.79, 0.01252, 4, 2, 26.18, 5.86, 0.97859, 158, 34.05, -157.68, 0.00754, 159, -38.47, -157.24, 0.01249, 160, -59.05, -191.35, 0.00138, 4, 2, 73.18, -100.15, 0.10457, 158, 46.97, -42.45, 0.82956, 159, -48.15, -41.69, 0.06586, 160, -112.28, -88.32, 1e-05, 5, 2, -40.56, -2.39, 0.79621, 158, 95.82, -184.24, 0.0011, 159, 27.28, -171.31, 0.07769, 160, 7.08, -179.14, 0.08647, 161, 1.55, -198.89, 0.03853, 4, 2, 5.26, -112.42, 0.09516, 158, 111.8, -66.13, 0.14598, 159, 20.04, -52.34, 0.73565, 160, -45.21, -72.03, 0.02321, 4, 2, -97.84, -2.52, 0.55427, 159, 81.44, -189.95, 0.05361, 160, 64.24, -175.6, 0.19755, 161, 52.05, -171.86, 0.19458, 4, 2, -48.85, -113.37, 0.09603, 159, 71.47, -69.17, 0.65116, 160, 8.75, -67.86, 0.25239, 161, -43.3, -97.04, 0.00042, 4, 2, -180.41, -3.11, 0.30707, 159, 159.65, -216.45, 0.0042, 160, 146.63, -170.1, 0.11579, 161, 124.65, -132.53, 0.57295, 4, 2, -121.74, -122.03, 0.03415, 159, 143.18, -84.87, 0.02143, 160, 80.99, -54.89, 0.94223, 161, 16.98, -55.14, 0.00219], "hull": 38, "edges": [0, 74, 0, 2, 22, 24, 56, 58, 72, 74, 2, 4, 72, 4, 68, 70, 70, 72, 70, 76, 4, 6, 76, 6, 68, 78, 78, 80, 6, 8, 80, 8, 66, 68, 66, 82, 82, 84, 8, 10, 10, 12, 84, 10, 64, 66, 64, 86, 86, 88, 88, 12, 62, 64, 62, 90, 90, 92, 12, 14, 92, 14, 58, 60, 60, 62, 60, 94, 94, 96, 14, 16, 16, 18, 96, 16, 58, 98, 98, 100, 100, 18, 102, 104, 104, 106, 18, 20, 20, 22, 106, 20, 54, 56, 54, 108, 108, 110, 110, 22, 50, 52, 52, 54, 52, 112, 112, 114, 114, 24, 50, 116, 116, 118, 24, 26, 118, 26, 48, 50, 48, 120, 120, 122, 26, 28, 122, 28, 46, 48, 46, 124, 124, 126, 28, 30, 126, 30, 42, 44, 44, 46, 44, 128, 128, 130, 30, 32, 32, 34, 130, 32, 40, 42, 38, 40, 34, 36, 36, 38], "width": 313, "height": 600}}, "f1": {"f1": {"type": "mesh", "uvs": [0.39554, 0, 0.53018, 0.05693, 0.67842, 0.17964, 0.78807, 0.27041, 0.82005, 0.29688, 0.86505, 0.33413, 0.89156, 0.35059, 0.92615, 0.37206, 0.96506, 0.41648, 0.99709, 0.45305, 0.99764, 0.48056, 0.99842, 0.52015, 0.93527, 0.56353, 0.88566, 0.59761, 0.81263, 0.59705, 0.72399, 0.59636, 0.71048, 0.56756, 0.69225, 0.5287, 0.673, 0.48766, 0.6555, 0.45036, 0.629, 0.39387, 0.39885, 0.52279, 0.40569, 0.58058, 0.4093, 0.61103, 0.41752, 0.6804, 0.42555, 0.74819, 0.42942, 0.78083, 0.43884, 0.86034, 0.42344, 0.91239, 0.41539, 0.93961, 0.40816, 0.96405, 0.40042, 0.99019, 0.33997, 0.99449, 0.2705, 0.99944, 0.26756, 0.98826, 0.28378, 0.96759, 0.29764, 0.94995, 0.32795, 0.91134, 0.34859, 0.88505, 0.37277, 0.85425, 0.29292, 0.8299, 0.15574, 0.78806, 0.03551, 0.7514, 0.02468, 0.69547, 0.01392, 0.63991, 0.00153, 0.57598, 0.07939, 0.46116, 0.07377, 0.39507, 0.06791, 0.32624, 0.06121, 0.2474, 0.13287, 0.135, 0.15908, 0.18121, 0.40535, 0.08233, 0.18644, 0.24228, 0.42628, 0.17103, 0.19288, 0.32225, 0.46974, 0.27718, 0.21542, 0.39932, 0.51159, 0.36588, 0.22668, 0.47203, 0.16069, 0.61162, 0.32487, 0.55636, 0.15425, 0.68578, 0.30717, 0.62907, 0.15103, 0.73958, 0.31038, 0.68433, 0.25083, 0.77884, 0.35545, 0.71922, 0.37003, 0.77799, 0.38021, 0.8201, 0.39461, 0.85532, 0.36241, 0.89973, 0.35054, 0.92577, 0.33189, 0.95793, 0.30223, 0.97937, 0.74575, 0.34484, 0.84234, 0.37821, 0.91968, 0.42885, 0.95024, 0.48959, 0.92885, 0.52824, 0.8555, 0.53376, 0.75465, 0.49787, 0.69149, 0.44725, 0.72566, 0.47464, 0.81127, 0.51802, 0.93402, 0.45735, 0.88123, 0.40367, 0.89007, 0.37471, 0.65595, 0.27322, 0.67417, 0.32209], "triangles": [36, 37, 73, 74, 36, 73, 35, 36, 74, 32, 73, 31, 74, 73, 32, 74, 33, 34, 74, 34, 35, 33, 74, 32, 37, 38, 71, 28, 71, 70, 72, 37, 71, 29, 71, 28, 72, 71, 29, 73, 37, 72, 30, 72, 29, 73, 72, 30, 31, 73, 30, 39, 40, 69, 27, 70, 69, 39, 69, 70, 71, 38, 39, 28, 70, 27, 71, 39, 70, 68, 25, 26, 69, 68, 26, 40, 68, 69, 27, 69, 26, 61, 21, 22, 63, 61, 22, 63, 22, 23, 65, 63, 23, 24, 65, 23, 67, 65, 24, 67, 24, 25, 68, 67, 25, 66, 65, 67, 66, 67, 68, 83, 82, 76, 18, 19, 82, 18, 82, 83, 81, 83, 76, 84, 81, 76, 17, 18, 83, 17, 83, 81, 16, 17, 81, 16, 81, 84, 14, 15, 16, 14, 84, 80, 14, 16, 84, 80, 84, 86, 79, 80, 85, 12, 13, 80, 12, 80, 79, 14, 80, 13, 78, 85, 9, 10, 78, 9, 78, 10, 11, 78, 79, 85, 79, 78, 11, 12, 79, 11, 7, 86, 87, 77, 7, 8, 77, 86, 7, 85, 77, 8, 85, 8, 9, 86, 84, 76, 77, 80, 86, 77, 85, 80, 75, 4, 5, 6, 76, 5, 87, 6, 7, 76, 75, 5, 87, 76, 6, 86, 76, 87, 82, 20, 76, 20, 75, 76, 88, 2, 3, 88, 56, 2, 89, 88, 3, 75, 89, 3, 75, 3, 4, 58, 88, 89, 58, 89, 75, 19, 20, 82, 62, 63, 65, 40, 66, 68, 64, 62, 65, 41, 64, 66, 62, 60, 63, 43, 44, 62, 64, 43, 62, 62, 44, 60, 52, 0, 1, 50, 0, 52, 54, 52, 1, 51, 50, 52, 54, 1, 2, 54, 51, 52, 53, 51, 54, 49, 50, 51, 49, 51, 53, 2, 56, 54, 56, 55, 53, 56, 53, 54, 48, 49, 53, 48, 53, 55, 58, 56, 88, 47, 48, 55, 57, 55, 56, 57, 56, 58, 47, 55, 57, 46, 47, 57, 59, 57, 58, 46, 57, 59, 59, 58, 20, 21, 59, 20, 61, 59, 21, 45, 46, 59, 60, 45, 59, 60, 59, 61, 44, 45, 60, 63, 60, 61, 42, 43, 64, 42, 64, 41, 41, 66, 40, 66, 64, 65, 20, 58, 75], "vertices": [1, 14, 152.98, -90.57, 1, 2, 14, 100.7, -109.51, 0.64625, 15, -85.78, 70.75, 0.35375, 2, 14, 25.58, -112.79, 0.12517, 15, -15.33, 44.46, 0.87483, 2, 15, 36.78, 25.02, 0.98314, 16, -13.5, 26.52, 0.01686, 2, 15, 51.98, 19.35, 0.63991, 16, 1.99, 21.7, 0.36009, 2, 15, 73.37, 11.37, 0.03625, 16, 23.79, 14.91, 0.96375, 3, 15, 85.11, 8.77, 0.00037, 16, 35.66, 12.97, 0.90112, 17, -10.59, 14.36, 0.09851, 2, 16, 51.15, 10.43, 0.20507, 17, 5.06, 15.57, 0.79493, 1, 17, 27.94, 10.41, 1, 2, 17, 46.77, 6.16, 0.21789, 18, -10.22, 7.12, 0.78211, 1, 18, 0.64, 10.55, 1, 1, 18, 16.27, 15.49, 1, 2, 18, 40.2, -2.05, 0.12279, 19, -1.25, 9.27, 0.87721, 1, 19, 20.87, 16.61, 1, 2, 19, 46.64, 7.55, 0.29687, 20, -6.96, 8.74, 0.70313, 2, 14, -108.47, -2.85, 0.1446, 20, 20.66, 27.08, 0.8554, 2, 14, -96.46, -7.71, 0.18, 20, 31.53, 20.03, 0.82, 4, 14, -80.26, -14.27, 0.32553, 15, 43.68, -87.55, 0.00732, 16, -0.39, -85.49, 0.00393, 20, 46.19, 10.53, 0.66322, 5, 14, -63.16, -21.19, 0.37177, 15, 30.64, -74.5, 0.0692, 16, -14.14, -73.19, 0.04941, 17, -38.52, -81.15, 0.001, 20, 61.67, 0.49, 0.50862, 5, 14, -47.61, -27.48, 0.4054, 15, 18.77, -62.64, 0.17269, 16, -26.63, -62, 0.08199, 17, -53.31, -73.25, 0.001, 20, 75.74, -8.63, 0.33892, 4, 14, -24.07, -37.01, 0.65157, 15, 0.81, -44.68, 0.24754, 16, -45.56, -45.06, 0.02779, 20, 97.06, -22.45, 0.0731, 3, 14, -0.94, 61.6, 0.57504, 65, -67.39, 11, 0.42494, 66, -103.82, 17.86, 3e-05, 2, 14, -19.67, 76.7, 0.27047, 65, -43.34, 11.83, 0.72953, 2, 14, -29.54, 84.66, 0.15981, 65, -30.67, 12.26, 0.84019, 2, 14, -52.02, 102.8, 0.0068, 65, -1.81, 13.25, 0.9932, 2, 65, 26.4, 14.22, 0.82621, 66, -10.03, 14.55, 0.17379, 2, 65, 39.98, 14.68, 0.11596, 66, 3.55, 14.07, 0.88404, 3, 66, 36.63, 12.9, 7e-05, 67, 5.16, 18.06, 0.9897, 68, -27.11, 10.19, 0.01024, 2, 67, 27.39, 19.85, 0.3181, 68, -6.07, 17.61, 0.6819, 2, 67, 39.02, 20.79, 0.03109, 68, 4.92, 21.49, 0.96891, 2, 68, 14.8, 24.97, 0.98347, 69, -15.32, 21.86, 0.01653, 2, 68, 25.37, 28.69, 0.91554, 69, -6.34, 28.56, 0.08446, 2, 68, 39.6, 11.04, 0.26667, 69, 12.51, 15.95, 0.73333, 2, 68, 55.97, -9.25, 9e-05, 69, 34.17, 1.46, 0.99991, 1, 69, 32.16, -2.85, 1, 1, 69, 22.1, -5.81, 1, 2, 68, 33.32, -12.44, 0.01452, 69, 13.51, -8.33, 0.98548, 3, 67, 38.94, -13.94, 0.03107, 68, 13.73, -12.11, 0.78641, 69, -5.29, -13.85, 0.18252, 2, 67, 26.1, -10.32, 0.46209, 68, 0.39, -11.89, 0.53791, 3, 65, 68.77, -8.64, 0.00426, 66, 30.64, -11.2, 0.08272, 67, 11.06, -6.07, 0.91302, 1, 14, -62.83, 179.51, 1, 1, 14, -14.3, 203.54, 1, 1, 14, 28.22, 224.61, 1, 1, 14, 47.46, 211.1, 1, 1, 14, 66.57, 197.68, 1, 1, 14, 88.56, 182.24, 1, 1, 14, 101.58, 128.04, 1, 1, 14, 122.42, 110.18, 1, 1, 14, 144.12, 91.57, 1, 1, 14, 168.97, 70.27, 1, 1, 14, 182.92, 18.41, 1, 1, 14, 162.46, 25.01, 1, 1, 14, 126.28, -69.07, 1, 1, 14, 137.35, 35.65, 1, 1, 14, 94.78, -48.63, 1, 1, 14, 112.23, 57.36, 1, 1, 14, 52.21, -29.05, 1, 1, 14, 83.71, 73.96, 1, 1, 14, 15.18, -14.15, 1, 1, 14, 59.45, 92.26, 1, 1, 14, 36.04, 150.58, 1, 3, 14, 8.79, 90.99, 0.53952, 65, -55.53, -17.6, 0.4532, 66, -93.98, -11.5, 0.00729, 1, 14, 16.03, 173.99, 1, 3, 14, -7.81, 116.95, 0.50078, 65, -25.98, -26.38, 0.47658, 66, -65.11, -22.31, 0.02264, 1, 14, 1.13, 190.6, 1, 3, 14, -24.83, 132.28, 0.40178, 65, -3.08, -26.83, 0.54227, 66, -42.3, -24.35, 0.05596, 1, 14, -36.75, 175.7, 1, 3, 14, -46.97, 130.58, 0.00229, 65, 12.55, -11.06, 0.95137, 66, -25.61, -9.71, 0.04635, 2, 65, 37.21, -7.38, 0.248, 66, -0.76, -7.75, 0.752, 3, 65, 54.87, -4.85, 0.01121, 66, 17.04, -6.45, 0.89738, 67, -3.19, -8.18, 0.09141, 2, 67, 8.74, 1.78, 0.99925, 68, -19.49, -4.63, 0.00075, 3, 67, 30.1, -3.41, 0.16416, 68, 2.49, -4.19, 0.8353, 69, -18.39, -9.64, 0.00055, 3, 67, 41.74, -3.98, 0.00526, 68, 13.89, -1.76, 0.98412, 69, -8.22, -3.93, 0.01062, 1, 69, 5.5, 2.2, 1, 2, 68, 42.41, -4.15, 0.00656, 69, 19.71, 2.29, 0.99344, 6, 14, -40.59, -82.24, 0.00268, 15, 33.67, -9.48, 0.79874, 16, -14.69, -8.1, 0.15547, 17, -54.5, -18.06, 0.00231, 19, 36.51, -99.34, 0.00042, 20, 72.26, -63.73, 0.04038, 4, 16, 23.87, -5.22, 0.91548, 17, -17.72, -6.11, 0.06302, 19, 6.8, -74.58, 0.00592, 20, 34.6, -72.53, 0.01558, 4, 17, 17.95, -4.24, 0.95639, 18, -11.6, -23.49, 0.02112, 19, -13.78, -45.38, 0.01709, 20, -1.1, -71.37, 0.00539, 4, 17, 42.64, -16.62, 0.09481, 18, 9.26, -5.38, 0.86246, 19, -16.46, -17.89, 0.03976, 20, -24.66, -56.95, 0.00297, 4, 17, 46.47, -34.1, 0.0245, 18, 26.88, -8.51, 0.3875, 19, -3.71, -5.34, 0.5843, 20, -27, -39.21, 0.0037, 5, 16, 55.75, -61.39, 0.00976, 17, 26.57, -53.11, 0.07127, 18, 36.86, -34.16, 0.0044, 19, 22.99, -12.05, 0.74955, 20, -5.56, -21.95, 0.16502, 6, 14, -87.74, -39.79, 0.00029, 15, 60.53, -66.96, 0.01285, 16, 15.29, -64.01, 0.07471, 17, -12.11, -65.25, 0.0418, 19, 53.87, -38.32, 0.03974, 20, 34.01, -13.12, 0.83062, 6, 14, -56.22, -37.91, 0.02769, 15, 30.77, -56.4, 0.22584, 16, -15.01, -55.11, 0.15674, 17, -43.65, -63.79, 0.00911, 19, 69.43, -65.79, 0.00232, 20, 65.32, -17.24, 0.5783, 6, 14, -73.27, -38.93, 0.0053, 15, 46.87, -62.11, 0.07559, 16, 1.38, -59.92, 0.1265, 17, -26.59, -64.58, 0.02718, 19, 61.01, -50.93, 0.01425, 20, 48.38, -15.01, 0.75117, 5, 16, 38.01, -62.54, 0.03968, 17, 9.61, -58.43, 0.09277, 18, 35.31, -51.87, 0.00018, 19, 36.53, -23.57, 0.29649, 20, 11.79, -18.07, 0.57088, 4, 17, 29.54, -10.05, 0.66875, 18, -1.81, -14.99, 0.26807, 19, -15.04, -32.48, 0.05445, 20, -12.15, -64.6, 0.00873, 5, 16, 41.52, -8.57, 0.07648, 17, 0.22, -5.17, 0.89361, 18, -17.51, -40.24, 0.00125, 19, -3.55, -59.89, 0.01419, 20, 16.65, -71.95, 0.01448, 2, 16, 39.41, 3.69, 0.65466, 17, -4.75, 6.24, 0.34534, 3, 14, 4.13, -79.46, 0.04379, 15, -8.59, 5.4, 0.95616, 20, 116.7, -69.47, 4e-05, 4, 14, -15, -69.97, 0.03681, 15, 5.32, -10.8, 0.92345, 16, -42.93, -10.98, 0.01452, 20, 99.71, -56.53, 0.02522], "hull": 51, "edges": [0, 100, 0, 2, 40, 42, 66, 68, 90, 92, 98, 100, 98, 102, 102, 104, 104, 2, 96, 98, 96, 106, 106, 108, 2, 4, 108, 4, 92, 94, 94, 96, 94, 110, 110, 112, 4, 6, 92, 114, 114, 116, 90, 118, 118, 40, 88, 90, 88, 120, 120, 122, 122, 42, 84, 86, 86, 88, 86, 124, 124, 126, 42, 44, 126, 44, 84, 128, 128, 130, 44, 46, 130, 46, 82, 84, 82, 132, 132, 134, 46, 48, 134, 48, 78, 80, 80, 82, 80, 136, 48, 50, 136, 50, 78, 138, 50, 52, 52, 54, 138, 52, 78, 140, 140, 54, 76, 78, 76, 142, 54, 56, 142, 56, 74, 76, 74, 144, 56, 58, 144, 58, 72, 74, 72, 146, 58, 60, 60, 62, 146, 60, 68, 70, 70, 72, 70, 148, 62, 64, 64, 66, 148, 64, 10, 150, 150, 116, 152, 40, 150, 152, 156, 158, 158, 160, 164, 40, 38, 40, 164, 38, 162, 166, 166, 164, 36, 38, 166, 36, 34, 36, 162, 34, 160, 168, 168, 162, 30, 32, 32, 34, 168, 32, 26, 28, 28, 30, 168, 28, 160, 26, 22, 24, 24, 26, 158, 24, 156, 22, 18, 20, 20, 22, 156, 20, 154, 170, 170, 156, 14, 16, 16, 18, 170, 16, 154, 14, 152, 172, 172, 154, 14, 174, 174, 152, 172, 174, 10, 12, 12, 14, 174, 12, 152, 10, 6, 8, 8, 10, 150, 8, 6, 176, 176, 112], "width": 224, "height": 248}}, "f2": {"f2": {"type": "mesh", "uvs": [0.39995, 0.05062, 0.45828, 0.09445, 0.54864, 0.16237, 0.61711, 0.21384, 0.68136, 0.26213, 0.74878, 0.3128, 0.79732, 0.34929, 0.86226, 0.39809, 0.94915, 0.46341, 1, 0.50163, 1, 0.56441, 1, 0.62461, 0.93652, 0.68001, 0.86188, 0.74514, 0.79806, 0.80084, 0.72818, 0.86182, 0.67133, 0.91144, 0.62093, 0.95542, 0.56984, 1, 0.48391, 1, 0.39201, 1, 0.26186, 0.97334, 0.15415, 0.95127, 0.06244, 0.93248, 0.11025, 0.9107, 0.18864, 0.875, 0.27742, 0.83456, 0.36959, 0.79257, 0.4579, 0.75235, 0.53202, 0.71858, 0.61546, 0.68058, 0.58972, 0.62571, 0.56542, 0.5739, 0.54373, 0.52768, 0.51677, 0.47019, 0.40811, 0.41417, 0.30978, 0.36347, 0.20452, 0.3092, 0.0916, 0.25098, 0.07088, 0.19372, 0.04622, 0.12556, 0.02241, 0.05976, 0.00091, 0.00036, 0.21591, 0.08433, 0.25511, 0.14617, 0.30411, 0.20041, 0.38496, 0.24706, 0.46091, 0.29588, 0.53931, 0.34361, 0.61771, 0.40328, 0.66671, 0.45319, 0.70591, 0.5096, 0.72986, 0.56002, 0.73581, 0.61183, 0.74176, 0.66277, 0.70209, 0.72776, 0.66837, 0.7866, 0.58815, 0.83279, 0.50511, 0.87895, 0.42561, 0.91963, 0.32668, 0.93918], "triangles": [58, 27, 57, 26, 27, 58, 16, 57, 15, 58, 57, 16, 59, 26, 58, 25, 26, 59, 60, 25, 59, 24, 25, 60, 22, 24, 60, 23, 24, 22, 17, 58, 16, 59, 58, 17, 21, 22, 60, 19, 20, 60, 60, 18, 19, 21, 60, 20, 60, 59, 18, 17, 18, 59, 30, 31, 54, 55, 30, 54, 29, 30, 55, 13, 54, 12, 55, 54, 13, 56, 29, 55, 28, 29, 56, 14, 55, 13, 56, 55, 14, 57, 28, 56, 27, 28, 57, 15, 56, 14, 57, 56, 15, 50, 7, 8, 51, 50, 8, 51, 8, 9, 33, 34, 50, 33, 50, 51, 52, 51, 9, 33, 51, 52, 52, 9, 10, 32, 33, 52, 53, 52, 10, 32, 52, 53, 53, 10, 11, 31, 32, 53, 12, 54, 53, 31, 53, 54, 11, 12, 53, 46, 3, 4, 47, 46, 4, 37, 46, 47, 47, 4, 5, 48, 47, 5, 48, 5, 6, 36, 37, 47, 36, 47, 48, 49, 48, 6, 49, 6, 7, 35, 36, 48, 35, 48, 49, 50, 49, 7, 34, 35, 49, 34, 49, 50, 43, 41, 42, 0, 43, 42, 43, 0, 1, 40, 41, 43, 44, 43, 1, 40, 43, 44, 44, 1, 2, 39, 40, 44, 45, 44, 2, 39, 44, 45, 45, 2, 3, 46, 45, 3, 38, 39, 45, 38, 45, 46, 37, 38, 46], "vertices": [1, 21, 5.38, 38.01, 1, 2, 21, 19.12, 36.86, 0.98723, 22, -39.99, 33.1, 0.01277, 2, 21, 40.41, 35.08, 0.78359, 22, -18.64, 33.71, 0.21641, 2, 21, 56.54, 33.74, 0.39581, 22, -2.46, 34.18, 0.60419, 3, 21, 71.68, 32.48, 0.10448, 22, 12.72, 34.61, 0.89377, 23, -58.81, 20.21, 0.00175, 3, 21, 87.56, 31.15, 0.00771, 22, 28.66, 35.07, 0.94581, 23, -43.5, 24.65, 0.04647, 3, 21, 99, 30.2, 1e-05, 22, 40.13, 35.4, 0.8364, 23, -32.48, 27.84, 0.16359, 2, 22, 55.47, 35.84, 0.52301, 23, -17.73, 32.11, 0.47699, 2, 22, 76.01, 36.43, 0.11666, 23, 2, 37.83, 0.88334, 3, 22, 88.02, 36.78, 0.02971, 23, 13.54, 41.17, 0.96886, 24, -51.52, 24.31, 0.00143, 3, 22, 102.92, 28.57, 0.00029, 23, 30.03, 36.95, 0.94931, 24, -34.72, 27, 0.0504, 2, 23, 45.83, 32.91, 0.76414, 24, -18.61, 29.57, 0.23586, 2, 23, 58.49, 21.81, 0.35149, 24, -2.58, 24.42, 0.64851, 2, 23, 73.37, 8.76, 0.00504, 24, 16.26, 18.37, 0.99496, 1, 24, 32.38, 13.19, 1, 2, 24, 50.02, 7.52, 0.70177, 25, -9.33, 3.42, 0.29823, 1, 25, 3.66, 11.07, 1, 1, 25, 15.18, 17.86, 1, 1, 25, 26.84, 24.74, 1, 1, 25, 35.53, 19.19, 1, 1, 25, 44.83, 13.25, 1, 1, 25, 54.1, -1.25, 1, 1, 25, 61.77, -13.24, 1, 1, 25, 68.3, -23.46, 1, 2, 24, 74.82, -63.61, 0.00121, 25, 60.29, -25.34, 0.99879, 2, 24, 63.78, -55.85, 0.02239, 25, 47.16, -28.43, 0.97761, 2, 24, 51.27, -47.06, 0.12694, 25, 32.28, -31.93, 0.87306, 2, 24, 38.29, -37.93, 0.40045, 25, 16.83, -35.57, 0.59955, 2, 24, 25.85, -29.19, 0.74743, 25, 2.03, -39.05, 0.25257, 3, 23, 56.59, -27.81, 9e-05, 24, 15.41, -21.85, 0.93431, 25, -10.39, -41.97, 0.06559, 3, 23, 49.09, -15.55, 0.17229, 24, 3.66, -13.59, 0.82499, 25, -24.37, -45.26, 0.00272, 2, 23, 33.92, -14.86, 0.90498, 24, -10.54, -18.99, 0.09502, 1, 23, 19.6, -14.21, 1, 2, 22, 67.78, -14.58, 0.05178, 23, 6.82, -13.62, 0.94822, 2, 22, 52.58, -9.9, 0.78779, 23, -9.08, -12.9, 0.21221, 1, 22, 32.99, -13.99, 1, 2, 21, 68.35, -19.79, 0.02851, 22, 15.26, -17.7, 0.97149, 2, 21, 49.05, -21.61, 0.53996, 22, -3.72, -21.66, 0.46004, 2, 21, 28.34, -23.56, 0.97746, 22, -24.08, -25.91, 0.02254, 1, 21, 14.24, -16.63, 1, 1, 21, -2.55, -8.38, 1, 1, 21, -18.76, -0.41, 1, 1, 21, -33.39, 6.78, 1, 1, 21, 0.09, 14.7, 1, 1, 21, 16.48, 8.86, 1, 2, 21, 31.88, 5.18, 0.99957, 22, -23.77, 3.04, 0.00043, 2, 21, 47.81, 5.8, 0.7338, 22, -8.02, 5.43, 0.2662, 2, 21, 63.87, 5.6, 0.01468, 22, 7.97, 7.03, 0.98532, 2, 22, 23.84, 9.03, 0.9994, 23, -41.64, -1.77, 0.0006, 2, 22, 42.54, 9.46, 0.95091, 23, -23.64, 3.33, 0.04909, 2, 22, 57.22, 8.09, 0.51543, 23, -9.08, 5.68, 0.48457, 2, 22, 72.88, 4.83, 0.00437, 23, 6.89, 6.45, 0.99563, 1, 23, 20.84, 5.84, 1, 1, 23, 34.62, 3.05, 1, 2, 23, 48.17, 0.32, 0.24886, 24, -3.5, 0.61, 0.75114, 2, 24, 14.64, -1.31, 0.99947, 25, -26.25, -28.89, 0.00053, 2, 24, 31.03, -2.79, 0.98855, 25, -14.25, -17.63, 0.01145, 2, 24, 44.91, -10.31, 0.59608, 25, 0.6, -12.26, 0.40392, 2, 24, 58.83, -18.18, 0.0646, 25, 15.73, -7.08, 0.9354, 2, 24, 71.23, -25.85, 0.00419, 25, 29.7, -2.93, 0.99581, 2, 24, 78.34, -36.74, 0.00181, 25, 42.56, -4.86, 0.99819], "hull": 43, "edges": [0, 84, 82, 84, 82, 86, 0, 2, 86, 2, 80, 82, 80, 88, 2, 4, 88, 4, 76, 78, 78, 80, 78, 90, 4, 6, 90, 6, 76, 92, 6, 8, 92, 8, 74, 76, 74, 94, 8, 10, 94, 10, 72, 74, 72, 96, 10, 12, 96, 12, 68, 70, 70, 72, 70, 98, 12, 14, 98, 14, 68, 100, 14, 16, 16, 18, 100, 16, 66, 68, 66, 102, 102, 18, 64, 66, 64, 104, 18, 20, 20, 22, 104, 20, 60, 62, 62, 64, 62, 106, 106, 22, 60, 108, 22, 24, 108, 24, 58, 60, 58, 110, 24, 26, 110, 26, 56, 58, 56, 112, 26, 28, 112, 28, 54, 56, 54, 114, 28, 30, 114, 30, 52, 54, 52, 116, 30, 32, 116, 32, 50, 52, 50, 118, 32, 34, 34, 36, 118, 34, 46, 48, 48, 50, 48, 120, 120, 36, 40, 42, 120, 42, 42, 44, 44, 46, 36, 38, 38, 40], "width": 72, "height": 162}}, "zs10": {"zs5": {"type": "mesh", "uvs": [0.98793, 0, 0.98784, 0.07091, 0.98773, 0.14927, 0.98766, 0.20333, 0.9876, 0.24956, 0.98753, 0.30128, 0.98747, 0.34738, 0.98741, 0.39005, 0.98736, 0.43144, 0.9873, 0.47446, 0.98724, 0.51649, 0.98718, 0.56359, 0.98711, 0.61454, 0.98704, 0.66421, 0.98696, 0.7228, 0.98691, 0.76301, 0.98685, 0.80766, 0.98679, 0.85231, 0.98672, 0.90394, 0.98664, 0.96613, 0.69181, 1, 0.28852, 1, 0.23182, 0.96359, 0.15707, 0.91559, 0.09598, 0.87636, 0, 0.81472, 0, 0.76862, 0.05471, 0.73056, 0.12829, 0.67937, 0.19266, 0.6346, 0.26646, 0.58326, 0.34096, 0.53143, 0.43689, 0.49237, 0.54862, 0.44688, 0.51265, 0.42291, 0.45567, 0.38493, 0.3842, 0.33729, 0.31929, 0.29403, 0.27203, 0.26253, 0.21518, 0.22465, 0.43026, 0.15841, 0.94462, 0, 0.74458, 0.11401, 0.63803, 0.17591, 0.60844, 0.22684, 0.69723, 0.2715, 0.70906, 0.31224, 0.71035, 0.36266, 0.7344, 0.40342, 0.71997, 0.44099, 0.6911, 0.48274, 0.67667, 0.52095, 0.65262, 0.56868, 0.63818, 0.62345, 0.58045, 0.67695, 0.52272, 0.72216, 0.5023, 0.7665, 0.53919, 0.81185, 0.6077, 0.86348, 0.54446, 0.91789, 0.52338, 0.96213], "triangles": [21, 60, 20, 20, 60, 19, 21, 22, 60, 60, 59, 19, 19, 59, 18, 60, 22, 59, 59, 22, 23, 59, 58, 18, 58, 59, 24, 59, 23, 24, 18, 58, 17, 58, 57, 17, 17, 57, 16, 24, 57, 58, 24, 25, 57, 25, 56, 57, 25, 26, 56, 57, 56, 16, 16, 56, 15, 56, 55, 15, 15, 55, 14, 26, 27, 56, 56, 27, 55, 27, 28, 55, 55, 54, 14, 14, 54, 13, 55, 28, 54, 28, 29, 54, 54, 53, 13, 54, 29, 53, 13, 53, 12, 29, 30, 53, 53, 52, 12, 12, 52, 11, 53, 30, 52, 30, 31, 52, 52, 51, 11, 52, 31, 51, 11, 51, 10, 31, 32, 51, 51, 50, 10, 51, 32, 50, 10, 50, 9, 32, 33, 50, 50, 49, 9, 50, 33, 49, 9, 49, 8, 33, 34, 49, 49, 48, 8, 49, 34, 48, 8, 48, 7, 34, 35, 48, 35, 47, 48, 48, 47, 7, 7, 47, 6, 35, 36, 47, 36, 46, 47, 47, 46, 6, 6, 46, 5, 36, 37, 46, 37, 45, 46, 46, 45, 5, 5, 45, 4, 37, 38, 45, 38, 44, 45, 45, 44, 4, 38, 39, 44, 4, 44, 3, 44, 43, 3, 44, 39, 43, 39, 40, 43, 3, 43, 2, 2, 43, 42, 43, 40, 42, 42, 40, 41, 2, 42, 1, 42, 41, 1, 1, 41, 0], "vertices": [1, 125, -47.22, 1.16, 1, 1, 125, -7.78, 5.1, 1, 1, 125, 35.81, 9.45, 1, 2, 125, 65.88, 12.45, 0.95252, 126, -22.71, 4.64, 0.04748, 1, 126, 2.52, 10.22, 1, 2, 126, 30.75, 16.45, 0.99954, 127, -59.21, -8.89, 0.00046, 2, 126, 55.91, 22.01, 0.90084, 127, -34.38, -2, 0.09916, 2, 126, 79.2, 27.16, 0.28257, 127, -11.39, 4.37, 0.71743, 1, 127, 10.91, 10.55, 1, 2, 127, 34.08, 16.97, 0.99887, 128, -69.18, -7.25, 0.00113, 2, 127, 56.73, 23.25, 0.92953, 128, -46.91, 0.24, 0.07047, 2, 127, 82.09, 30.28, 0.4328, 128, -21.96, 8.62, 0.5672, 2, 128, 5.04, 17.69, 0.98456, 129, -53.7, -4.32, 0.01544, 2, 128, 31.36, 26.54, 0.65696, 129, -27.32, 4.36, 0.34304, 2, 129, 3.79, 14.6, 0.99979, 130, -66.66, 3.17, 0.00021, 2, 129, 25.14, 21.62, 0.93726, 130, -44.69, 7.91, 0.06274, 2, 129, 48.85, 29.42, 0.4574, 130, -20.29, 13.17, 0.5426, 1, 130, 4.11, 18.43, 1, 1, 130, 32.32, 24.52, 1, 1, 130, 66.3, 31.85, 1, 1, 130, 89.41, 14.52, 1, 1, 130, 95.71, -14.65, 1, 2, 129, 149.11, 3.62, 0.02299, 130, 76.7, -23.05, 0.97701, 2, 129, 125.36, -10.03, 0.26556, 130, 51.64, -34.12, 0.73444, 3, 128, 164.78, 1.83, 0.01831, 129, 105.94, -21.18, 0.6964, 130, 31.16, -43.16, 0.2853, 2, 128, 134.39, -15.89, 0.27042, 129, 75.43, -38.71, 0.72958, 2, 128, 109.96, -24.1, 0.71084, 129, 50.96, -46.77, 0.28916, 3, 127, 190.48, -11.26, 0.0004, 128, 88.51, -27.04, 0.98063, 129, 29.48, -49.57, 0.01897, 2, 127, 161.45, -13.66, 0.05349, 128, 59.65, -31, 0.94651, 2, 127, 136.06, -15.76, 0.33865, 128, 34.41, -34.46, 0.66135, 2, 127, 106.95, -18.17, 0.9664, 128, 5.46, -38.43, 0.0336, 1, 127, 77.56, -20.6, 1, 1, 127, 54.62, -19.6, 1, 2, 126, 117.24, 2.32, 0.06286, 127, 27.9, -18.43, 0.93714, 2, 126, 104.73, -3.17, 0.32426, 127, 15.7, -24.57, 0.67574, 2, 126, 84.91, -11.88, 0.92477, 127, -3.63, -34.31, 0.07523, 2, 125, 144.84, -24.54, 0.0091, 126, 60.05, -22.79, 0.9909, 2, 125, 121.25, -31.72, 0.14694, 126, 37.48, -32.7, 0.85306, 2, 125, 104.08, -36.95, 0.43715, 126, 21.04, -39.92, 0.56285, 2, 125, 83.42, -43.25, 0.79586, 126, 1.27, -48.6, 0.20414, 1, 125, 44.99, -31.09, 1, 1, 125, -46.9, -2.03, 1, 1, 125, 17.98, -10.42, 1, 1, 125, 53.2, -14.82, 1, 2, 125, 81.75, -14.17, 0.83511, 126, -3.82, -19.92, 0.16489, 2, 125, 105.93, -5.15, 0.07741, 126, 19.14, -8.12, 0.92259, 2, 125, 128.51, -2.01, 0.00252, 126, 41.19, -2.34, 0.99748, 2, 126, 68.69, 3.84, 0.96541, 127, -20.66, -19.48, 0.03459, 2, 126, 90.55, 10.49, 0.35092, 127, 0.82, -11.67, 0.64908, 2, 126, 111.29, 13.99, 0.03738, 127, 21.34, -7.09, 0.96262, 2, 126, 134.53, 16.94, 6e-05, 127, 44.4, -2.91, 0.99994, 2, 127, 65.27, 1.77, 0.99533, 128, -37.22, -20.75, 0.00467, 2, 127, 91.46, 7.19, 0.81247, 128, -11.37, -13.94, 0.18753, 2, 127, 121.24, 14.34, 0.09774, 128, 17.99, -5.19, 0.90226, 2, 128, 47.7, 0.29, 0.99683, 129, -11.15, -21.99, 0.00317, 2, 128, 73.02, 4.29, 0.86124, 129, 14.19, -18.15, 0.13876, 2, 128, 96.99, 10.76, 0.38802, 129, 38.21, -11.83, 0.61198, 2, 128, 120.15, 21.42, 0.00583, 129, 61.43, -1.31, 0.99417, 2, 129, 87.26, 12.53, 0.23975, 130, 16.13, -7.67, 0.76025, 2, 129, 117.62, 17.6, 0.03226, 130, 46.85, -5.82, 0.96774, 2, 129, 141.59, 23.85, 0.00153, 130, 71.35, -2.13, 0.99847], "hull": 42, "edges": [0, 82, 38, 40, 40, 42, 50, 52, 78, 80, 80, 82, 80, 84, 0, 2, 84, 2, 78, 86, 2, 4, 86, 4, 76, 78, 76, 88, 4, 6, 88, 6, 74, 76, 74, 90, 6, 8, 90, 8, 72, 74, 72, 92, 8, 10, 92, 10, 70, 72, 70, 94, 10, 12, 94, 12, 66, 68, 68, 70, 68, 96, 12, 14, 96, 14, 66, 98, 14, 16, 98, 16, 62, 64, 64, 66, 64, 100, 16, 18, 100, 18, 62, 102, 18, 20, 102, 20, 60, 62, 60, 104, 20, 22, 104, 22, 58, 60, 58, 106, 22, 24, 106, 24, 56, 58, 56, 108, 24, 26, 108, 26, 52, 54, 54, 56, 54, 110, 26, 28, 110, 28, 52, 112, 28, 30, 112, 30, 50, 114, 30, 32, 114, 32, 48, 50, 48, 116, 32, 34, 116, 34, 46, 48, 46, 118, 34, 36, 36, 38, 118, 36, 42, 44, 44, 46, 44, 120, 120, 38], "width": 44, "height": 335}}, "s1": {"s1": {"type": "mesh", "uvs": [1, 0.12954, 0.91317, 0.35049, 0.89719, 0.39116, 0.86632, 0.4697, 0.84128, 0.53344, 0.82908, 0.56446, 0.80646, 0.62204, 0.74577, 0.65363, 0.67325, 0.69139, 0.59739, 0.73088, 0.54109, 0.76019, 0.47017, 0.79712, 0.34682, 0.86133, 0.16305, 0.95701, 0.08047, 1, 0.05597, 1, 0.04266, 0.95669, 0.02012, 0.88329, 0, 0.8178, 0, 0.75417, 0.1503, 0.61097, 0.32035, 0.5285, 0.41005, 0.485, 0.50139, 0.4407, 0.52458, 0.37635, 0.56911, 0.30497, 0.60794, 0.24272, 0.63023, 0.207, 0.66107, 0.15756, 0.69543, 0.10248, 0.74052, 0.03021, 0.75937, 0, 0.78928, 0, 0.82061, 0.01137, 0.98035, 0.06936, 1, 0.07649, 0.51996, 0.48968, 0.52075, 0.60258, 0.5691, 0.47275, 0.57862, 0.57717, 0.63252, 0.46004, 0.65471, 0.58705, 0.68483, 0.43041, 0.71495, 0.55601, 0.69831, 0.32033, 0.73635, 0.42759, 0.78673, 0.55658, 0.80577, 0.41476, 0.77997, 0.31143, 0.69068, 0.21781, 0.84397, 0.39268, 0.80329, 0.2761, 0.71747, 0.14893, 0.87593, 0.34293, 0.8422, 0.23165, 0.79061, 0.10977, 0.89974, 0.28906, 0.92901, 0.19367, 0.95927, 0.1186, 0.7658, 0.25638, 0.79507, 0.29436, 0.82632, 0.40299, 0.82781, 0.27846, 0.86154, 0.36943, 0.86105, 0.20957, 0.89776, 0.33145, 0.83079, 0.05943, 0.81045, 0.11419, 0.76082, 0.06246, 0.72265, 0.12744, 0.94637, 0.08946, 0.90917, 0.17248, 0.87159, 0.24456, 0.13739, 0.70968, 0.32244, 0.60517, 0.41692, 0.54346, 0.14074, 0.77836, 0.3202, 0.66887, 0.43481, 0.61313, 0.15137, 0.84903, 0.32971, 0.73357, 0.45326, 0.68281, 0.52972, 0.67207], "triangles": [1, 57, 0, 1, 56, 57, 57, 58, 0, 57, 72, 71, 57, 56, 72, 72, 64, 71, 57, 71, 58, 71, 70, 58, 71, 66, 70, 58, 35, 0, 58, 34, 35, 58, 70, 34, 34, 70, 33, 4, 50, 63, 2, 3, 53, 51, 62, 50, 50, 62, 63, 3, 63, 53, 2, 65, 1, 2, 53, 65, 63, 62, 53, 53, 62, 54, 65, 56, 1, 54, 72, 53, 53, 72, 65, 65, 72, 56, 62, 51, 54, 54, 51, 55, 54, 64, 72, 54, 67, 64, 55, 69, 68, 55, 67, 54, 71, 64, 66, 64, 67, 66, 69, 30, 68, 67, 55, 66, 68, 32, 55, 66, 32, 33, 66, 55, 32, 30, 31, 68, 68, 31, 32, 33, 70, 66, 60, 59, 51, 49, 52, 59, 51, 59, 52, 55, 52, 69, 55, 51, 52, 49, 28, 52, 28, 29, 52, 52, 29, 69, 69, 29, 30, 42, 26, 44, 44, 26, 27, 27, 49, 44, 44, 49, 48, 49, 59, 48, 27, 28, 49, 45, 44, 48, 47, 48, 60, 61, 60, 50, 50, 60, 51, 48, 59, 60, 8, 43, 7, 7, 46, 6, 7, 43, 46, 61, 5, 6, 5, 50, 4, 43, 45, 46, 46, 61, 6, 46, 45, 47, 43, 42, 45, 47, 61, 46, 61, 50, 5, 4, 63, 3, 45, 48, 47, 61, 47, 60, 79, 13, 14, 14, 15, 16, 14, 16, 79, 13, 79, 12, 16, 17, 79, 17, 76, 79, 17, 18, 76, 79, 80, 12, 11, 80, 81, 11, 12, 80, 79, 76, 80, 76, 18, 73, 11, 82, 10, 11, 81, 82, 18, 19, 73, 76, 77, 80, 76, 73, 77, 10, 82, 9, 73, 19, 20, 80, 78, 81, 80, 77, 78, 82, 39, 9, 9, 41, 8, 9, 39, 41, 77, 73, 74, 8, 41, 43, 81, 37, 82, 81, 78, 37, 82, 37, 39, 78, 74, 75, 78, 77, 74, 74, 73, 20, 37, 78, 36, 20, 21, 74, 74, 21, 75, 78, 75, 36, 37, 36, 39, 39, 40, 41, 41, 42, 43, 41, 40, 42, 36, 38, 39, 39, 38, 40, 21, 22, 75, 75, 23, 36, 75, 22, 23, 38, 36, 24, 36, 23, 24, 40, 38, 25, 38, 24, 25, 42, 40, 26, 40, 25, 26, 42, 44, 45], "vertices": [1, 80, 42.42, -12.31, 1, 3, 74, 52.85, -36.07, 0.00291, 78, -8.65, -12.66, 0.61542, 80, -15.2, -9.04, 0.38167, 4, 74, 42.82, -32.59, 0.0825, 75, -18.37, -29.95, 0.01398, 78, -13.46, -3.19, 0.84164, 80, -25.8, -8.44, 0.06188, 3, 74, 23.44, -25.85, 0.59054, 75, -26.39, -11.07, 0.05378, 78, -22.76, 15.1, 0.35568, 3, 74, 7.71, -20.39, 0.92566, 75, -32.9, 4.25, 0.00083, 78, -30.3, 29.93, 0.07351, 2, 74, 0.06, -17.73, 0.97782, 78, -33.97, 37.16, 0.02218, 2, 73, 285.66, -55.98, 0.0124, 74, -14.15, -12.79, 0.9876, 2, 73, 261.64, -54.14, 0.26161, 74, -26.95, 7.61, 0.73839, 2, 73, 232.94, -51.94, 0.69363, 74, -42.26, 31.99, 0.30637, 2, 73, 202.91, -49.64, 0.93, 74, -58.27, 57.49, 0.07, 2, 73, 180.63, -47.93, 0.98706, 74, -70.15, 76.42, 0.01294, 2, 73, 152.56, -45.78, 0.99994, 74, -85.12, 100.27, 6e-05, 1, 73, 103.74, -42.03, 1, 1, 73, 31, -36.46, 1, 1, 73, -1.68, -33.95, 1, 1, 73, -10.41, -30.65, 1, 1, 73, -11.88, -20.19, 1, 1, 73, -14.37, -2.46, 1, 1, 73, -16.59, 13.36, 1, 1, 73, -11.78, 26.1, 1, 1, 73, 52.62, 34.54, 1, 1, 73, 119.46, 28.16, 1, 1, 73, 154.72, 24.8, 1, 2, 73, 190.63, 21.38, 0.98722, 76, 83.61, 67.19, 0.01278, 2, 73, 203.76, 31.14, 0.92382, 76, 80.11, 51.21, 0.07618, 2, 73, 225.03, 39.44, 0.70742, 76, 69.51, 31, 0.29258, 4, 73, 243.57, 46.68, 0.40083, 75, 66.97, 46.67, 0, 76, 60.26, 13.36, 0.58582, 77, 58.17, 37.61, 0.01335, 3, 73, 254.22, 50.83, 0.21976, 76, 54.95, 3.24, 0.6983, 77, 55.09, 26.61, 0.08194, 3, 73, 268.95, 56.58, 0.04411, 76, 47.61, -10.76, 0.51873, 77, 50.82, 11.39, 0.43715, 4, 73, 285.36, 62.98, 0.00013, 76, 39.42, -26.36, 0.03855, 77, 46.07, -5.58, 0.89046, 79, 51, 24.75, 0.07086, 2, 77, 39.83, -27.83, 0.31361, 79, 51.36, 1.64, 0.68639, 3, 77, 37.22, -37.14, 0.1447, 79, 51.52, -8.02, 0.85418, 80, 17.77, 80.25, 0.00113, 3, 77, 27.6, -43.24, 0.03834, 79, 44.03, -16.61, 0.94568, 80, 23.77, 70.56, 0.01598, 2, 79, 34.35, -24.01, 0.92456, 80, 27.97, 59.12, 0.07544, 2, 79, -15.01, -61.72, 0.00325, 80, 49.43, 0.83, 0.99675, 1, 80, 52.07, -6.34, 1, 2, 73, 193.55, 9.07, 0.99542, 76, 73.34, 74.57, 0.00458, 2, 73, 185.29, -13.64, 0.99721, 74, -39.81, 93.07, 0.00279, 2, 73, 212.34, 5.85, 0.98814, 76, 57.04, 64.66, 0.01186, 2, 73, 207.84, -16.34, 0.97538, 74, -28.57, 73.34, 0.02462, 2, 73, 235.91, -0.14, 0.99924, 74, 1.14, 60.42, 0.00076, 3, 73, 234.22, -28.55, 0.77671, 74, -22.7, 44.87, 0.2219, 75, -4.96, 70.61, 0.00139, 4, 73, 256.79, -1.25, 0.56041, 74, 12.68, 42.97, 0.17407, 75, 17.39, 43.11, 0.104, 76, 18.84, 40.85, 0.16152, 4, 73, 258.04, -30.45, 0.41101, 74, -10.05, 24.6, 0.5669, 75, -11.49, 47.62, 0.01873, 76, -1.26, 62.07, 0.00336, 4, 73, 269.92, 18.98, 0.18012, 74, 36.74, 44.45, 0.04387, 75, 34.64, 26.27, 0.13064, 76, 22.2, 16.98, 0.64537, 4, 73, 275.37, -7.62, 0.19955, 74, 18.61, 24.25, 0.44517, 75, 7.5, 26.14, 0.25182, 76, 0.64, 33.47, 0.10346, 3, 73, 283.58, -40.22, 0.00334, 74, -2.72, -1.74, 0.99643, 78, -24.89, 50.6, 0.00024, 2, 74, 28.46, -0.45, 0.99653, 78, -1.53, 29.92, 0.00347, 4, 75, 19.77, -1.12, 0.958, 76, -6.31, 4.39, 0.01394, 77, -5.07, 14.96, 0.01896, 78, 22.61, 28.18, 0.0091, 3, 73, 274.95, 40.53, 0.01906, 76, 32.55, -2.58, 0.78708, 77, 34.39, 16.24, 0.19385, 4, 74, 36.97, -13.16, 0.2416, 75, -7.9, -12.58, 0.29297, 77, -34.97, 16.61, 0.0009, 78, -4.21, 14.85, 0.46454, 5, 75, 21.47, -12.66, 0.31415, 76, -12.02, -5.79, 0.03858, 77, -8.53, 3.82, 0.39126, 79, -4.01, 18.2, 0.00377, 78, 25.1, 16.79, 0.25224, 2, 77, 33.66, -1.67, 0.97943, 79, 37.99, 24.96, 0.02057, 2, 78, -0.54, -0.9, 0.98218, 80, -21.29, 3.88, 0.01782, 2, 78, 26.52, -0.77, 0.9926, 80, -7.79, 27.33, 0.0074, 2, 79, 25.99, -1.55, 0.99741, 80, 4.05, 57.77, 0.00259, 3, 79, -30.25, -7.67, 0.00039, 78, 5.36, -14.33, 0.33241, 80, -6.71, 2.23, 0.6672, 3, 79, -22.2, -29.49, 0.02629, 78, 18.12, -33.78, 0.01143, 80, 16.52, 3.48, 0.96228, 3, 79, -17.67, -48.74, 0.01038, 78, 26.87, -51.51, 3e-05, 80, 36.24, 2.12, 0.98959, 2, 76, 2.84, -4.78, 0.68714, 77, 5.79, 7.9, 0.31286, 4, 75, 19.82, -7.94, 0.57502, 76, -10.44, -1.04, 0.06173, 77, -7.97, 8.8, 0.21777, 78, 23.13, 21.39, 0.14547, 4, 74, 33.02, -7.3, 0.38323, 75, -6.21, -5.71, 0.40732, 77, -30.47, 22.07, 0.00052, 78, -2.99, 21.82, 0.20893, 4, 75, 16.09, -20.32, 0.18118, 77, -16.69, -0.75, 0.14127, 79, -10.53, 11.49, 0.00141, 78, 20.26, 8.78, 0.67615, 4, 74, 43.59, -18.25, 0.08976, 75, -7.23, -20.9, 0.12574, 77, -37.97, 8.82, 0.00064, 78, -2.97, 6.6, 0.78386, 3, 79, -7.75, -7.74, 0.29702, 78, 27.31, -9.33, 0.50527, 80, 0, 23.71, 0.19771, 3, 74, 55.17, -29.31, 0.00048, 78, -2.29, -9.39, 0.66639, 80, -14.82, -1.9, 0.33313, 2, 79, 24.05, -20.17, 0.88394, 80, 21.27, 50.41, 0.11606, 2, 79, 20.31, -6.63, 0.97305, 80, 7.23, 50.84, 0.02695, 3, 77, 29.6, -26.14, 0.23472, 79, 41.08, 0.35, 0.76503, 80, 6.69, 72.75, 0.00025, 2, 77, 34.45, -6.61, 0.87227, 79, 40.16, 20.45, 0.12773, 3, 79, -9.74, -49.14, 0.07988, 78, 34.69, -50.11, 0.00013, 80, 38.96, 9.58, 0.91998, 3, 79, -13.81, -26.78, 0.15775, 78, 25.68, -29.24, 0.06391, 80, 16.4, 12.29, 0.77834, 3, 79, -16.03, -5.85, 0.05188, 78, 18.81, -9.35, 0.6695, 80, -4.25, 16.36, 0.27862, 1, 73, 40.55, 16.51, 1, 1, 73, 114.41, 12.53, 1, 1, 73, 152.75, 12.17, 1, 1, 73, 36.56, 2.31, 1, 1, 73, 108.8, 0.08, 1, 1, 73, 153.86, -4.18, 1, 1, 73, 35, -13.27, 1, 1, 73, 107.3, -14.15, 1, 1, 73, 155.17, -20.61, 1, 2, 73, 183.24, -28.76, 0.99089, 74, -53.19, 85.73, 0.00911], "hull": 36, "edges": [0, 70, 28, 30, 36, 38, 38, 40, 46, 48, 62, 64, 46, 72, 72, 74, 48, 76, 76, 78, 18, 20, 78, 18, 48, 50, 50, 80, 80, 82, 16, 18, 82, 16, 50, 52, 52, 84, 84, 86, 12, 14, 14, 16, 86, 14, 52, 54, 54, 88, 88, 90, 90, 92, 92, 12, 92, 94, 94, 96, 96, 98, 54, 56, 98, 56, 10, 12, 10, 100, 100, 102, 102, 104, 56, 58, 104, 58, 6, 106, 106, 108, 108, 110, 58, 60, 60, 62, 0, 2, 2, 112, 112, 114, 114, 116, 116, 70, 118, 120, 120, 122, 122, 12, 124, 126, 6, 8, 8, 10, 126, 8, 2, 4, 4, 6, 130, 4, 64, 66, 66, 132, 132, 134, 60, 136, 136, 110, 64, 136, 136, 138, 66, 68, 68, 70, 68, 140, 140, 142, 128, 144, 144, 130, 142, 144, 36, 146, 146, 148, 148, 150, 150, 72, 34, 36, 34, 152, 152, 154, 154, 156, 156, 74, 30, 32, 32, 34, 32, 158, 158, 160, 160, 162, 20, 164, 164, 74, 162, 164, 20, 22, 22, 24, 24, 26, 26, 28, 40, 42, 42, 44, 44, 46], "width": 228, "height": 128}}, "f5": {"f5": {"type": "mesh", "uvs": [0.10779, 0, 0.12409, 0.06324, 0.14015, 0.12556, 0.15198, 0.17146, 0.16881, 0.23673, 0.17249, 0.24827, 0.25845, 0.305, 0.32399, 0.34825, 0.39971, 0.39823, 0.46785, 0.44319, 0.56444, 0.50694, 0.62883, 0.54943, 0.67369, 0.53645, 0.74717, 0.51518, 0.87352, 0.47862, 0.91784, 0.51044, 0.99883, 0.56861, 0.95185, 0.61563, 0.90586, 0.66166, 0.86695, 0.7006, 0.88173, 0.75771, 0.89311, 0.80163, 0.90163, 0.83456, 0.91105, 0.87094, 0.86934, 0.91413, 0.82501, 0.96002, 0.78639, 1, 0.76567, 1, 0.75896, 0.95224, 0.75369, 0.91469, 0.74682, 0.86581, 0.67253, 0.83474, 0.5931, 0.80152, 0.52364, 0.77247, 0.40839, 0.76737, 0.24569, 0.76018, 0.07927, 0.75283, 0.06117, 0.69307, 0.03527, 0.60752, 0.0113, 0.52831, 0.00988, 0.47767, 0.00741, 0.38972, 0.00589, 0.33543, 0.00388, 0.26383, 0.00165, 0.18406, 0, 0.1252, 0, 0.06409, 0.07914, 0, 0.06707, 0.07806, 0.08063, 0.1821, 0.11793, 0.30463, 0.162, 0.41214, 0.23997, 0.51617, 0.32472, 0.59594, 0.40609, 0.65836, 0.49423, 0.70806, 0.60949, 0.76355, 0.07504, 0.13922, 0.10034, 0.24684, 0.1423, 0.3641, 0.211, 0.47752, 0.54681, 0.73337, 0.85007, 0.57747, 0.80769, 0.66532, 0.81277, 0.76705, 0.82464, 0.85259, 0.79921, 0.92657, 0.82842, 0.62233, 0.5939, 0.62776, 0.69881, 0.66549, 0.80967, 0.705, 0.56812, 0.68557, 0.69077, 0.7244, 0.68575, 0.78126, 0.81884, 0.81081, 0.7309, 0.83059, 0.81488, 0.881, 0.78146, 0.96544], "triangles": [48, 46, 47, 47, 0, 1, 48, 47, 1, 45, 46, 48, 48, 1, 2, 57, 48, 2, 45, 48, 57, 57, 2, 3, 49, 57, 3, 44, 45, 57, 44, 57, 49, 49, 3, 4, 58, 44, 49, 58, 49, 4, 58, 43, 44, 5, 50, 58, 5, 58, 4, 43, 58, 50, 50, 5, 6, 42, 43, 50, 59, 50, 6, 42, 50, 59, 59, 6, 7, 41, 42, 59, 51, 59, 7, 51, 7, 8, 51, 40, 41, 51, 41, 59, 40, 51, 60, 60, 51, 8, 60, 8, 9, 52, 60, 9, 52, 9, 10, 39, 40, 60, 52, 38, 39, 52, 39, 60, 53, 37, 38, 53, 38, 52, 36, 37, 53, 53, 52, 10, 53, 10, 11, 11, 54, 53, 68, 54, 11, 11, 12, 63, 68, 11, 69, 71, 54, 68, 71, 68, 69, 55, 54, 71, 36, 53, 54, 35, 36, 54, 34, 54, 55, 35, 54, 34, 62, 13, 14, 62, 14, 15, 62, 15, 16, 17, 62, 16, 67, 13, 62, 67, 62, 17, 12, 13, 67, 18, 67, 17, 63, 12, 67, 63, 67, 18, 69, 11, 63, 19, 63, 18, 70, 63, 19, 69, 63, 70, 72, 71, 69, 72, 69, 70, 61, 55, 71, 56, 61, 71, 72, 56, 71, 20, 64, 70, 20, 70, 19, 72, 70, 64, 33, 55, 61, 33, 61, 56, 34, 55, 33, 73, 56, 72, 73, 72, 64, 32, 33, 56, 31, 32, 56, 64, 20, 21, 74, 64, 21, 73, 64, 74, 75, 73, 74, 74, 21, 22, 73, 31, 56, 31, 73, 75, 65, 74, 22, 75, 74, 65, 30, 75, 65, 31, 75, 30, 65, 22, 23, 76, 30, 65, 76, 65, 23, 24, 76, 23, 29, 30, 76, 66, 29, 76, 66, 76, 24, 28, 29, 66, 25, 66, 24, 77, 28, 66, 25, 77, 66, 27, 28, 77, 26, 77, 25, 27, 77, 26], "vertices": [1, 64, 67.22, -12.5, 1, 1, 64, 46.24, -13.26, 1, 1, 64, 25.56, -14.01, 1, 2, 63, 78.95, -18.07, 0.00057, 64, 10.33, -14.56, 0.99943, 3, 62, 108.78, -28.39, 0.00067, 63, 57.38, -16.01, 0.71223, 64, -11.33, -15.35, 0.28711, 3, 62, 105.08, -27.27, 0.00323, 63, 53.52, -15.8, 0.85183, 64, -15.18, -15.64, 0.14494, 3, 61, 133.21, -41.85, 0.00574, 62, 79.55, -35, 0.18471, 63, 30.54, -29.35, 0.80955, 3, 61, 113.11, -44.92, 0.05205, 62, 60.08, -40.9, 0.5459, 63, 13.02, -39.68, 0.40206, 3, 61, 89.88, -48.48, 0.23073, 62, 37.59, -47.7, 0.6989, 63, -7.22, -51.62, 0.07037, 4, 60, 97.4, -77.2, 0.00077, 61, 68.98, -51.68, 0.52511, 62, 17.36, -53.83, 0.47094, 63, -25.43, -62.36, 0.00318, 3, 60, 68.3, -70, 0.06112, 61, 39.34, -56.22, 0.83367, 62, -11.33, -62.51, 0.10521, 3, 60, 48.9, -65.19, 0.26714, 61, 19.6, -59.24, 0.72163, 62, -30.46, -68.3, 0.01123, 3, 60, 42.39, -73.89, 0.39469, 61, 16.92, -69.77, 0.60391, 62, -31.61, -79.11, 0.00139, 2, 60, 31.71, -88.14, 0.49346, 61, 12.54, -87.03, 0.50654, 2, 60, 13.35, -112.63, 0.54702, 61, 5.01, -116.7, 0.45298, 2, 60, -0.42, -108.59, 0.55972, 61, -9.25, -118.27, 0.44028, 2, 60, -25.59, -101.22, 0.58653, 61, -35.32, -121.14, 0.41347, 2, 60, -24.26, -82.66, 0.60774, 61, -41.23, -103.49, 0.39226, 2, 60, -22.95, -64.5, 0.66894, 61, -47, -86.22, 0.33106, 2, 60, -21.84, -49.13, 0.77876, 61, -51.89, -71.61, 0.22124, 2, 60, -34.07, -34.63, 0.92069, 61, -68.75, -62.93, 0.07931, 2, 60, -43.48, -23.49, 0.97334, 61, -81.72, -56.26, 0.02666, 2, 60, -50.54, -15.13, 0.99191, 61, -91.45, -51.26, 0.00809, 2, 60, -58.33, -5.9, 0.99881, 61, -102.19, -45.73, 0.00119, 1, 60, -57.38, 10.98, 1, 1, 60, -56.37, 28.92, 1, 1, 60, -55.49, 44.55, 1, 1, 60, -51.49, 46.87, 1, 1, 60, -42.36, 34.12, 1, 1, 60, -35.17, 24.09, 1, 1, 60, -25.82, 11.04, 1, 1, 60, -6.39, 10.58, 1, 1, 60, 14.38, 10.09, 1, 1, 60, 32.55, 9.65, 1, 3, 60, 55.61, 21.12, 0.57101, 61, -7.4, 23.02, 0.3934, 62, -68.83, 9.31, 0.03559, 3, 60, 88.16, 37.31, 0.05787, 61, 16.43, 50.48, 0.55458, 62, -49.13, 39.87, 0.38754, 4, 60, 121.46, 53.88, 0.0016, 61, 40.8, 78.58, 0.30513, 62, -28.98, 71.13, 0.69327, 63, -100.02, 48.08, 1e-05, 4, 60, 134.77, 39.01, 5e-05, 61, 58.8, 69.96, 0.22525, 62, -9.94, 65.15, 0.76968, 63, -80.1, 46.78, 0.00502, 3, 61, 84.56, 57.63, 0.05092, 62, 17.31, 56.59, 0.8836, 63, -51.6, 44.91, 0.06548, 3, 61, 108.42, 46.22, 0.00052, 62, 42.54, 48.67, 0.71995, 63, -25.21, 43.19, 0.27953, 2, 62, 57.17, 40.89, 0.45157, 63, -9.16, 39.09, 0.54843, 2, 62, 82.57, 27.39, 0.01672, 63, 18.71, 31.98, 0.98328, 1, 63, 35.91, 27.59, 1, 2, 63, 58.6, 21.8, 0.97029, 64, -15.06, 22.3, 0.02971, 2, 63, 83.89, 15.35, 0.19096, 64, 10.85, 19.21, 0.80904, 2, 63, 102.54, 10.59, 0.00257, 64, 29.97, 16.93, 0.99743, 1, 64, 49.76, 14.19, 1, 1, 64, 68.1, -6.17, 1, 1, 64, 43.18, 0, 1, 2, 63, 79.82, -1.8, 0.01281, 64, 9.07, 1.68, 0.98719, 1, 63, 38.98, 0.83, 1, 1, 63, 2.48, 0.69, 1, 2, 62, 21.21, 2.18, 0.99892, 63, -34.93, -7.03, 0.00108, 2, 61, 48.52, 3.95, 0.87565, 62, -10.77, -1.65, 0.12435, 3, 60, 73.96, -9.44, 0.00106, 61, 21.29, 1.86, 0.99569, 62, -37.43, -7.58, 0.00325, 2, 60, 48.8, -5.26, 0.47597, 61, -3.54, -3.96, 0.52403, 2, 60, 17.46, -2.49, 0.99228, 61, -33.54, -13.44, 0.00772, 2, 63, 93.67, -4.33, 0.00033, 64, 23.13, 0.99, 0.99967, 2, 63, 58.25, -0.41, 0.99079, 64, -12.51, 0.23, 0.00921, 1, 63, 18.79, 0.75, 1, 2, 62, 35.4, 1.68, 0.99631, 63, -21.03, -4.16, 0.00369, 2, 60, 34.5, -4, 0.94167, 61, -17.22, -8.28, 0.05833, 2, 60, 1.64, -82.05, 0.57501, 61, -17.56, -92.97, 0.42499, 2, 60, -4.62, -52.46, 0.70196, 61, -34.71, -68.07, 0.29804, 2, 60, -22.31, -24.27, 0.93314, 61, -61.88, -48.84, 0.06686, 2, 60, -38.65, -1.41, 0.99701, 61, -85.75, -34.02, 0.00299, 1, 60, -45.9, 22.35, 1, 2, 60, -1.56, -66.94, 0.62168, 61, -26.32, -80.25, 0.37832, 3, 60, 42.77, -39.14, 0.31484, 61, 3.92, -37.54, 0.68488, 62, -49.05, -49.04, 0.00027, 2, 60, 16.35, -40.22, 0.64697, 61, -20.06, -48.7, 0.35303, 2, 60, -11.52, -41.47, 0.80061, 61, -45.31, -60.57, 0.19939, 2, 60, 38.25, -19.9, 0.53485, 61, -7.66, -21.52, 0.46515, 2, 60, 8.22, -22.66, 0.85769, 61, -34.32, -35.62, 0.14231, 2, 60, -0.15, -6.02, 0.9855, 61, -48.44, -23.48, 0.0145, 2, 60, -30.67, -12.58, 0.98013, 61, -74.09, -41.26, 0.01987, 2, 60, -16.96, 2.87, 0.99791, 61, -67.38, -21.73, 0.00209, 2, 60, -41.43, 7.71, 0.9998, 61, -91.83, -26.67, 0.0002, 1, 60, -48.86, 35.33, 1], "hull": 48, "edges": [0, 94, 8, 10, 52, 54, 90, 92, 92, 94, 94, 96, 104, 106, 106, 108, 108, 110, 60, 62, 112, 62, 92, 96, 0, 2, 96, 2, 96, 114, 114, 98, 90, 114, 2, 4, 114, 4, 88, 90, 88, 98, 4, 6, 6, 8, 98, 6, 86, 88, 98, 116, 116, 100, 86, 116, 116, 8, 84, 86, 84, 100, 10, 12, 100, 12, 82, 84, 100, 118, 118, 102, 82, 118, 12, 14, 118, 14, 78, 80, 80, 82, 80, 102, 14, 16, 102, 16, 102, 120, 120, 104, 78, 120, 16, 18, 120, 18, 76, 78, 76, 104, 18, 20, 20, 22, 104, 20, 72, 74, 74, 76, 74, 106, 106, 22, 72, 108, 108, 22, 70, 72, 70, 108, 66, 68, 68, 70, 68, 110, 62, 64, 64, 66, 64, 112, 110, 122, 122, 112, 66, 122, 28, 30, 30, 32, 30, 124, 26, 28, 26, 124, 124, 32, 22, 24, 24, 26, 124, 134, 134, 126, 24, 134, 32, 34, 134, 34, 22, 126, 34, 36, 36, 38, 126, 36, 22, 136, 136, 138, 126, 140, 140, 128, 138, 140, 140, 38, 122, 142, 142, 136, 142, 144, 144, 128, 38, 40, 128, 40, 128, 148, 148, 130, 146, 148, 40, 42, 148, 42, 150, 130, 42, 44, 44, 46, 130, 44, 130, 152, 152, 132, 60, 152, 152, 46, 58, 60, 132, 58, 46, 48, 132, 48, 54, 56, 56, 58, 54, 154, 154, 132, 56, 154, 48, 50, 50, 52, 154, 50], "width": 133, "height": 196}}, "f6": {"f6": {"type": "mesh", "uvs": [0.10401, 0.09392, 0.16067, 0.14, 0.19072, 0.16444, 0.28776, 0.18475, 0.34272, 0.19626, 0.40094, 0.20845, 0.49407, 0.25619, 0.58018, 0.30034, 0.6725, 0.34766, 0.73568, 0.38006, 0.78999, 0.4079, 0.85669, 0.44209, 0.90407, 0.46638, 0.83117, 0.54272, 0.80997, 0.55316, 0.84827, 0.60992, 0.89291, 0.67609, 0.93456, 0.73782, 0.96554, 0.78373, 0.99917, 0.83359, 0.94493, 0.88775, 0.81137, 0.92595, 0.67945, 0.96368, 0.55247, 1, 0.54986, 1, 0.55165, 0.90657, 0.55332, 0.81928, 0.5552, 0.72139, 0.43421, 0.72318, 0.35869, 0.70703, 0.2535, 0.68452, 0.21817, 0.63539, 0.15443, 0.54676, 0.23879, 0.52734, 0.32536, 0.50741, 0.31177, 0.47488, 0.29065, 0.42431, 0.27507, 0.38699, 0.25487, 0.33863, 0.20344, 0.29162, 0.16397, 0.25554, 0.10056, 0.19756, 0.02695, 0.13027, 0.0018, 0, 0.00511, 0, 0.04182, 0.04335, 0.06273, 0.10321, 0.10957, 0.13659, 0.15641, 0.18248, 0.2145, 0.23254, 0.26696, 0.26731, 0.3213, 0.30624, 0.38875, 0.35631, 0.45995, 0.39802, 0.54426, 0.45365, 0.61359, 0.49398, 0.44683, 0.56629, 0.69041, 0.52735, 0.52365, 0.61218, 0.75974, 0.56907, 0.65294, 0.64695, 0.741, 0.69284, 0.28757, 0.60801, 0.38313, 0.64138, 0.50304, 0.65946, 0.6342, 0.68449, 0.63795, 0.71231, 0.63608, 0.77627, 0.74475, 0.74707, 0.84968, 0.73873, 0.83469, 0.69701, 0.6417, 0.84272, 0.76536, 0.80796, 0.85342, 0.79683, 0.63233, 0.89696, 0.74287, 0.86358, 0.84593, 0.84967, 0.76349, 0.89835, 0.83469, 0.88305], "triangles": [46, 45, 0, 45, 43, 44, 42, 45, 46, 42, 43, 45, 1, 47, 0, 46, 0, 47, 48, 47, 1, 48, 1, 2, 47, 42, 46, 41, 47, 48, 41, 42, 47, 49, 2, 3, 48, 2, 49, 40, 48, 49, 41, 48, 40, 50, 49, 3, 50, 3, 4, 39, 40, 49, 39, 49, 50, 38, 39, 50, 51, 50, 4, 51, 4, 5, 52, 51, 5, 38, 50, 51, 6, 52, 5, 37, 38, 51, 37, 51, 52, 53, 52, 6, 36, 37, 52, 53, 36, 52, 53, 6, 7, 54, 53, 7, 54, 7, 8, 53, 35, 36, 55, 54, 8, 34, 35, 53, 34, 53, 54, 56, 34, 54, 56, 54, 55, 62, 33, 34, 62, 34, 56, 32, 33, 62, 31, 32, 62, 63, 62, 56, 63, 56, 58, 64, 63, 58, 30, 31, 62, 30, 62, 63, 29, 30, 63, 28, 63, 64, 28, 64, 27, 29, 63, 28, 55, 8, 9, 57, 55, 9, 57, 9, 10, 59, 57, 10, 14, 59, 10, 13, 11, 12, 13, 14, 11, 11, 14, 10, 58, 56, 55, 58, 55, 57, 60, 58, 57, 60, 57, 59, 65, 64, 58, 60, 65, 58, 61, 60, 59, 65, 60, 61, 59, 14, 15, 61, 59, 15, 70, 61, 15, 70, 15, 16, 66, 65, 61, 27, 64, 65, 27, 65, 66, 68, 61, 70, 66, 61, 68, 23, 25, 74, 22, 23, 74, 24, 25, 23, 67, 27, 66, 67, 66, 68, 26, 27, 67, 71, 67, 72, 26, 67, 71, 75, 71, 72, 71, 25, 26, 74, 71, 75, 77, 75, 78, 74, 25, 71, 77, 22, 74, 77, 74, 75, 22, 77, 21, 69, 70, 16, 69, 16, 17, 68, 70, 69, 73, 69, 17, 73, 17, 18, 72, 68, 69, 73, 72, 69, 67, 68, 72, 76, 72, 73, 76, 73, 18, 76, 18, 19, 75, 72, 76, 78, 75, 76, 20, 76, 19, 78, 76, 20, 21, 77, 78, 21, 78, 20], "vertices": [1, 56, 82.69, -5.81, 1, 1, 56, 58.78, -7.18, 1, 2, 55, 103.2, -13.7, 0.00045, 56, 46.09, -7.91, 0.99955, 2, 55, 77.48, -26.51, 0.23624, 56, 22.59, -24.45, 0.76376, 2, 55, 62.91, -33.76, 0.65519, 56, 9.28, -33.81, 0.34481, 3, 54, 112.66, -37.34, 0.01133, 55, 47.47, -41.45, 0.91804, 56, -4.82, -43.74, 0.07063, 3, 53, 156.79, -49.31, 0.0044, 54, 81.07, -43.86, 0.2414, 55, 15.5, -45.71, 0.7542, 3, 53, 127.1, -52.15, 0.07633, 54, 51.86, -49.89, 0.6919, 55, -14.07, -49.64, 0.23177, 3, 53, 95.27, -55.21, 0.37628, 54, 20.54, -56.35, 0.61229, 55, -45.76, -53.87, 0.01143, 2, 53, 73.48, -57.3, 0.68115, 54, -0.89, -60.78, 0.31885, 2, 53, 54.76, -59.09, 0.8805, 54, -19.32, -64.58, 0.1195, 2, 53, 31.76, -61.3, 0.98527, 54, -41.94, -69.25, 0.01473, 1, 53, 15.42, -62.86, 1, 1, 53, 12.43, -27.06, 1, 1, 53, 14.46, -20.1, 1, 2, 57, 4.05, -82.83, 0.02574, 53, -7.93, -10.46, 0.97426, 3, 57, -3.73, -55.5, 0.40063, 58, -72.17, -34.04, 9e-05, 53, -34.03, 0.77, 0.59928, 2, 57, -10.99, -30, 0.78757, 53, -58.38, 11.25, 0.21243, 2, 57, -16.39, -11.04, 0.95913, 53, -76.49, 19.05, 0.04087, 1, 57, -22.25, 9.56, 1, 2, 57, -3.19, 27.12, 0.99885, 58, -43.4, 43.41, 0.00115, 3, 57, 36.94, 34.46, 0.6329, 58, -3.18, 36.57, 0.36255, 59, -24.24, 52.74, 0.00455, 3, 57, 76.57, 41.7, 0.0135, 58, 36.54, 29.82, 0.48844, 59, 7.19, 27.53, 0.49805, 2, 58, 74.77, 23.33, 0, 59, 37.45, 3.27, 1, 1, 59, 37.82, 2.62, 1, 4, 58, 56.65, -7.64, 0.05591, 59, 6.55, -14.98, 0.94122, 53, -16.67, 131.12, 0.00231, 54, -110.81, 116.83, 0.00056, 5, 57, 101.59, -19.46, 0.00175, 58, 39.12, -36.21, 0.75646, 59, -22.66, -31.42, 0.11468, 53, 4.56, 105.18, 0.09971, 54, -86.91, 93.33, 0.02739, 6, 57, 94.07, -56.3, 0.02076, 58, 19.46, -68.25, 0.3786, 59, -55.42, -49.86, 0.00019, 53, 28.37, 76.09, 0.36861, 54, -60.11, 66.97, 0.23094, 55, -117.45, 74.88, 0.0009, 4, 58, 49.45, -85.27, 0.16963, 53, 54.3, 98.83, 0.23488, 54, -36.78, 92.37, 0.58281, 55, -92.37, 98.56, 0.01267, 4, 58, 64.78, -101.6, 0.12436, 53, 74.76, 107.95, 0.13999, 54, -17.42, 103.64, 0.70912, 55, -72.26, 108.43, 0.02653, 4, 58, 86.14, -124.34, 0.09244, 53, 103.25, 120.66, 0.0571, 54, 9.54, 119.35, 0.80463, 55, -44.26, 122.17, 0.04583, 4, 58, 85.16, -145.71, 0.07987, 53, 123.11, 112.72, 0.02787, 54, 30.13, 113.59, 0.83186, 55, -24.12, 114.97, 0.0604, 3, 58, 83.39, -184.25, 0.06712, 54, 67.29, 103.21, 0.853, 55, 12.2, 101.97, 0.07988, 4, 58, 58.91, -178.37, 0.06355, 53, 145.35, 77.2, 0.00276, 54, 56.07, 80.68, 0.83905, 55, -0.59, 80.29, 0.09463, 4, 58, 33.79, -172.35, 0.03932, 53, 131.41, 55.45, 0.00625, 54, 44.55, 57.55, 0.73781, 55, -13.72, 58.05, 0.21662, 4, 58, 30.74, -185.07, 0.01794, 53, 142.42, 48.39, 0.0002, 54, 56.26, 51.72, 0.53926, 55, -2.46, 51.4, 0.4426, 3, 58, 26, -204.84, 0.00482, 54, 74.46, 42.65, 0.20556, 55, 15.05, 41.06, 0.78962, 4, 58, 22.49, -219.42, 0.00118, 54, 87.88, 35.96, 0.04368, 55, 27.97, 33.43, 0.94254, 56, -35.39, 27.35, 0.0126, 3, 58, 17.96, -238.33, 3e-05, 55, 44.71, 23.55, 0.79491, 56, -17.35, 20.1, 0.20506, 2, 55, 67.7, 20.03, 0.03013, 56, 5.91, 20.09, 0.96987, 1, 56, 23.75, 20.07, 1, 1, 56, 52.43, 20.06, 1, 1, 56, 85.71, 20.04, 1, 1, 56, 129.05, -5.96, 1, 1, 56, 128.46, -6.69, 1, 1, 56, 108.94, -4.31, 1, 1, 56, 87.34, 5.57, 1, 1, 56, 68.98, 3.29, 1, 1, 56, 46.88, 4.05, 1, 1, 56, 21.52, 3.33, 1, 1, 56, 1.73, 0.15, 1, 2, 55, 39.05, 1.62, 0.99441, 56, -19.64, -2.43, 0.00559, 2, 58, -11.38, -213, 7e-05, 55, 11.89, 2.93, 0.99993, 3, 58, -20.64, -188.87, 5e-05, 54, 48.42, 0.8, 0.99785, 55, -13.9, 1.16, 0.0021, 2, 53, 97, -0.53, 0.00791, 54, 16.37, -1.81, 0.99209, 2, 53, 71.91, -1.42, 0.92551, 54, -8.48, -5.4, 0.07449, 4, 58, 15.58, -135.23, 0.06477, 53, 90.36, 50.44, 0.12678, 54, 4.29, 48.15, 0.76365, 55, -54.55, 51.52, 0.0448, 1, 53, 46.91, -5.73, 1, 5, 57, 95.11, -99.17, 0.00061, 58, 5.76, -108.9, 0.11769, 53, 62.27, 49.81, 0.38968, 54, -23.58, 44.49, 0.48482, 55, -82.61, 49.86, 0.00719, 1, 53, 21.48, -6.2, 1, 4, 57, 61.39, -79.21, 0.03774, 58, -19.09, -78.6, 0.15633, 53, 25.49, 36.28, 0.70377, 54, -58.69, 27.08, 0.10217, 4, 57, 40, -57.23, 0.22533, 58, -31.67, -50.63, 0.14235, 53, -5.06, 33.58, 0.61303, 54, -88.77, 21.11, 0.01929, 4, 58, 62.79, -144.64, 0.07873, 53, 114.76, 91.93, 0.03617, 54, 24.07, 92.03, 0.82009, 55, -31.7, 93.89, 0.065, 4, 58, 45.92, -119.71, 0.1092, 53, 85.67, 84.19, 0.13086, 54, -4.01, 81.19, 0.7256, 55, -60.48, 85.08, 0.03434, 5, 57, 104.26, -82.43, 0.00165, 58, 20.09, -96.29, 0.17953, 53, 55.06, 67.48, 0.33978, 54, -32.65, 61.28, 0.47206, 55, -90.46, 67.26, 0.00697, 4, 57, 69.32, -66.03, 0.05899, 58, -7.14, -68.93, 0.27557, 53, 20.28, 50.74, 0.53984, 54, -65.42, 40.9, 0.1256, 4, 57, 70.25, -55.34, 0.08439, 58, -2.6, -59.2, 0.37136, 53, 12.58, 58.22, 0.44243, 54, -73.88, 47.51, 0.10182, 5, 57, 75.34, -31.3, 0.07596, 58, 10.41, -38.36, 0.66321, 59, -48.79, -19.34, 0.00363, 53, -2.83, 77.35, 0.21123, 54, -91.27, 64.87, 0.04597, 4, 57, 42.83, -36.56, 0.43783, 58, -21.95, -32.18, 0.2214, 53, -19.3, 48.82, 0.32748, 54, -104.56, 34.73, 0.0133, 4, 57, 12.85, -34.15, 0.67222, 58, -49.29, -19.66, 0.01162, 53, -40.1, 27.11, 0.31593, 54, -122.91, 10.9, 0.00023, 4, 57, 14.07, -50.69, 0.41187, 58, -53.8, -35.61, 0.01942, 53, -26.51, 17.61, 0.56812, 54, -108.37, 2.92, 0.00059, 5, 57, 78.51, -5.94, 0.0024, 58, 22.06, -15.6, 0.93093, 59, -27.54, -5.12, 0.01772, 53, -20.5, 95.84, 0.04063, 54, -110.82, 81.34, 0.00832, 4, 57, 41.4, -12.5, 0.81485, 58, -15.06, -9.08, 0.11745, 53, -38.85, 62.92, 0.06568, 54, -125.52, 46.64, 0.00202, 2, 57, 15.95, -12.03, 0.93649, 53, -55.29, 43.48, 0.06351, 2, 58, 35, 0.94, 0.82279, 59, -8.2, 3.04, 0.17721, 3, 57, 51.67, 7.3, 0.16314, 58, 1.36, 6.01, 0.8368, 59, -35.12, 23.83, 6e-05, 2, 57, 21.82, 7.51, 0.99198, 58, -26.61, 16.42, 0.00802, 3, 57, 48.38, 21.5, 0.39065, 58, 3.13, 20.49, 0.59784, 59, -26.54, 35.62, 0.01151, 2, 57, 27.35, 19.51, 0.89649, 58, -17.31, 25.8, 0.10351], "hull": 46, "edges": [24, 26, 26, 28, 38, 40, 46, 48, 54, 56, 84, 86, 86, 88, 88, 90, 90, 92, 92, 84, 0, 90, 0, 94, 82, 84, 94, 82, 0, 2, 2, 4, 2, 96, 80, 82, 96, 80, 4, 98, 76, 78, 78, 80, 98, 78, 4, 6, 6, 100, 100, 76, 6, 8, 8, 10, 8, 102, 74, 76, 102, 74, 10, 104, 72, 74, 104, 72, 10, 12, 12, 106, 68, 70, 70, 72, 106, 70, 12, 14, 14, 108, 108, 68, 14, 16, 16, 110, 110, 112, 16, 18, 18, 114, 114, 116, 18, 20, 20, 118, 118, 120, 20, 22, 22, 24, 22, 28, 28, 30, 122, 30, 64, 124, 124, 126, 126, 128, 128, 130, 130, 122, 54, 132, 132, 122, 60, 62, 62, 64, 64, 66, 66, 68, 56, 58, 58, 60, 52, 54, 52, 134, 134, 136, 136, 138, 138, 34, 122, 140, 30, 32, 32, 34, 140, 32, 48, 50, 50, 52, 50, 142, 142, 144, 144, 146, 34, 36, 36, 38, 146, 36, 46, 148, 148, 150, 150, 152, 152, 38, 44, 46, 44, 154, 154, 156, 156, 40, 40, 42, 42, 44], "width": 171, "height": 230}}, "f7": {"f7": {"type": "mesh", "uvs": [0.88199, 0, 1, 0.02704, 1, 0.04475, 0.87163, 0.18189, 0.77317, 0.28708, 0.69209, 0.3737, 0.60343, 0.46842, 0.53252, 0.54417, 0.45449, 0.62754, 0.35207, 0.73696, 0.29167, 0.80149, 0.24122, 0.85539, 0.19736, 0.90224, 0.14819, 0.95477, 0.10585, 1, 0.08912, 1, 0.00793, 0.93953, 0.00698, 0.87561, 0.00578, 0.79514, 0.00428, 0.69463, 0.02934, 0.65295, 0.08214, 0.56508, 0.13057, 0.4845, 0.21653, 0.34147, 0.29493, 0.28737, 0.40415, 0.21202, 0.508, 0.14037, 0.61075, 0.06948, 0.71145, 0, 0.83596, 0.06385, 0.73437, 0.12678, 0.63061, 0.19996, 0.53767, 0.27899, 0.42959, 0.3507, 0.33448, 0.43266, 0.23505, 0.53218, 0.17912, 0.61073, 0.11428, 0.68684, 0.08401, 0.75709, 0.08185, 0.83905, 0.07104, 0.91661], "triangles": [13, 14, 40, 16, 40, 15, 14, 15, 40, 12, 13, 39, 16, 17, 40, 13, 40, 39, 40, 17, 39, 11, 12, 38, 17, 18, 39, 37, 10, 11, 38, 12, 39, 39, 18, 38, 36, 9, 10, 18, 19, 38, 37, 11, 38, 38, 19, 37, 10, 37, 36, 19, 20, 37, 37, 20, 36, 20, 21, 36, 8, 9, 35, 9, 36, 35, 7, 8, 34, 36, 21, 35, 21, 22, 35, 8, 35, 34, 35, 22, 34, 22, 23, 34, 6, 7, 33, 7, 34, 33, 5, 6, 32, 32, 6, 33, 34, 23, 33, 23, 24, 33, 33, 24, 32, 24, 25, 32, 5, 32, 31, 32, 25, 31, 4, 5, 31, 31, 30, 4, 4, 30, 3, 25, 26, 31, 31, 26, 30, 30, 29, 3, 3, 29, 2, 26, 27, 30, 30, 27, 29, 27, 28, 29, 29, 0, 2, 29, 28, 0, 0, 1, 2], "vertices": [1, 45, -0.98, -9.76, 1, 1, 45, -8.77, 4.44, 1, 1, 45, -6.48, 6.95, 1, 1, 45, 23.62, 15.06, 1, 3, 45, 46.71, 21.29, 0.96049, 46, -17.35, 22.57, 0.03943, 47, -55.98, 36.46, 8e-05, 3, 45, 65.72, 26.41, 0.45948, 46, 1.81, 27.09, 0.50956, 47, -36.3, 35.74, 0.03096, 3, 45, 86.51, 32.02, 0.05869, 46, 22.77, 32.04, 0.65064, 47, -14.79, 34.95, 0.29066, 3, 45, 103.14, 36.5, 0.00263, 46, 39.53, 35.99, 0.30297, 47, 2.42, 34.31, 0.6944, 3, 46, 57.97, 40.35, 0.04259, 47, 21.36, 33.61, 0.90223, 48, -8.15, 39.72, 0.05518, 2, 47, 46.21, 32.7, 0.45204, 48, 15.39, 31.71, 0.54796, 2, 47, 60.87, 32.16, 0.14634, 48, 29.28, 26.98, 0.85366, 2, 47, 73.11, 31.71, 0.0323, 48, 40.88, 23.03, 0.9677, 2, 47, 83.76, 31.32, 0.00364, 48, 50.96, 19.6, 0.99636, 1, 48, 62.27, 15.75, 1, 1, 48, 72, 12.44, 1, 1, 48, 72.51, 10.33, 1, 1, 48, 63.7, -2.66, 1, 1, 48, 51.8, -5.66, 1, 1, 48, 36.82, -9.43, 1, 1, 48, 18.1, -14.15, 1, 1, 48, 9.56, -12.86, 1, 2, 47, 35.39, -14.24, 0.51998, 48, -8.45, -10.15, 0.48002, 3, 46, 68.88, -8.73, 0.00474, 47, 18.85, -16.6, 0.98303, 48, -24.97, -7.66, 0.01223, 2, 46, 41.69, -20.56, 0.78535, 47, -10.5, -20.78, 0.21465, 2, 46, 27.14, -20.86, 0.9934, 47, -24.61, -17.21, 0.0066, 2, 45, 72.31, -21.77, 0.09647, 46, 6.87, -21.27, 0.90353, 2, 45, 53.06, -22.78, 0.72072, 46, -12.4, -21.67, 0.27928, 2, 45, 34.02, -23.77, 0.98552, 46, -31.46, -22.06, 0.01448, 1, 45, 15.35, -24.75, 1, 1, 45, 11.71, -4.77, 1, 1, 45, 29.61, -4.8, 1, 2, 45, 49.05, -3.57, 0.97973, 46, -15.79, -2.35, 0.02027, 1, 46, 3.45, 0.05, 1, 2, 46, 23.12, 0.08, 0.99997, 47, -22.92, 4.04, 3e-05, 2, 46, 42.97, 2.68, 0.3852, 47, -3.1, 1.29, 0.6148, 1, 47, 19.92, -0.25, 1, 2, 47, 36.61, 1.07, 0.75218, 48, -2.89, 4.16, 0.24782, 1, 48, 13.29, -0.6, 1, 1, 48, 27.33, -1.26, 1, 2, 47, 80.84, 12.23, 0.00023, 48, 42.69, 2.16, 0.99977, 1, 48, 57.5, 4.29, 1], "hull": 29, "edges": [0, 56, 0, 2, 2, 4, 28, 30, 30, 32, 54, 56, 54, 58, 4, 6, 58, 6, 52, 54, 52, 60, 6, 8, 60, 8, 50, 52, 50, 62, 8, 10, 62, 10, 46, 48, 48, 50, 48, 64, 10, 12, 64, 12, 46, 66, 12, 14, 66, 14, 44, 46, 44, 68, 14, 16, 68, 16, 42, 44, 42, 70, 16, 18, 70, 18, 38, 40, 40, 42, 40, 72, 18, 20, 72, 20, 38, 74, 20, 22, 74, 22, 36, 38, 36, 76, 22, 24, 76, 24, 32, 34, 34, 36, 34, 78, 24, 26, 26, 28, 78, 26, 32, 80, 80, 28], "width": 78, "height": 115}}, "f8": {"f8": {"type": "mesh", "uvs": [0.41691, 0.02572, 0.52382, 0.01919, 0.64171, 0.01199, 0.74413, 0.00573, 0.83796, 0, 1, 0, 1, 0.06659, 0.8675, 0.15874, 0.77481, 0.2232, 0.69624, 0.27784, 0.60647, 0.34027, 0.51758, 0.40208, 0.40641, 0.47939, 0.3555, 0.5148, 0.29983, 0.55351, 0.2393, 0.59561, 0.20124, 0.62207, 0.18026, 0.68665, 0.16058, 0.74724, 0.13351, 0.83055, 0.12105, 0.86892, 0.10892, 0.94211, 0.09932, 1, 0.06784, 1, 0.03656, 0.93982, 0, 0.86948, 0, 0.80914, 0, 0.69762, 0.02246, 0.65256, 0.05795, 0.58137, 0.10064, 0.49574, 0.15169, 0.39336, 0.1933, 0.30989, 0.25497, 0.18618, 0.3324, 0.03088, 0.869, 0.06046, 0.7847, 0.07422, 0.69337, 0.08797, 0.60029, 0.11941, 0.50721, 0.14889, 0.42467, 0.16854, 0.37374, 0.21373, 0.29646, 0.31788, 0.23851, 0.40434, 0.18406, 0.50849, 0.14367, 0.58905, 0.11733, 0.67159, 0.10152, 0.72661, 0.08396, 0.78949, 0.07342, 0.85433, 0.07518, 0.91722], "triangles": [21, 22, 50, 22, 23, 50, 23, 24, 50, 21, 50, 20, 24, 25, 50, 25, 49, 50, 50, 49, 20, 25, 26, 49, 20, 49, 19, 49, 48, 19, 49, 26, 48, 19, 48, 18, 18, 48, 47, 26, 27, 48, 48, 27, 47, 47, 46, 18, 18, 46, 17, 27, 28, 47, 47, 28, 46, 16, 17, 45, 28, 29, 46, 17, 46, 45, 46, 29, 45, 15, 16, 44, 16, 45, 44, 29, 30, 45, 45, 30, 44, 14, 15, 43, 42, 13, 14, 43, 15, 44, 41, 12, 13, 30, 31, 44, 44, 31, 43, 42, 14, 43, 31, 32, 43, 43, 32, 42, 41, 13, 42, 32, 33, 42, 40, 11, 12, 12, 41, 40, 40, 39, 11, 11, 39, 10, 39, 38, 10, 42, 33, 41, 40, 41, 34, 41, 33, 34, 34, 0, 40, 40, 0, 39, 39, 1, 38, 39, 0, 1, 10, 38, 9, 38, 37, 9, 9, 37, 8, 8, 36, 7, 8, 37, 36, 7, 35, 6, 7, 36, 35, 38, 2, 37, 38, 1, 2, 37, 3, 36, 37, 2, 3, 36, 4, 35, 36, 3, 4, 35, 5, 6, 35, 4, 5], "vertices": [2, 49, 80.31, -16.91, 0.02436, 50, 8.73, -25.43, 0.97564, 2, 49, 63.24, -15.47, 0.43598, 50, -6.02, -16.72, 0.56402, 2, 49, 44.42, -13.87, 0.99897, 50, -22.29, -7.11, 0.00103, 1, 49, 28.07, -12.48, 1, 1, 49, 13.09, -11.21, 1, 1, 49, -12.59, -7.62, 1, 1, 49, -11.27, 1.81, 1, 1, 49, 11.55, 11.92, 1, 2, 49, 27.52, 18.99, 0.95882, 50, -23.26, 29.83, 0.04118, 2, 49, 41.05, 24.99, 0.71448, 50, -8.46, 29.36, 0.28552, 3, 49, 56.51, 31.84, 0.24099, 50, 8.44, 28.82, 0.73847, 51, -21.41, 37.58, 0.02053, 3, 49, 71.82, 38.62, 0.02421, 50, 25.18, 28.29, 0.77912, 51, -5.81, 31.51, 0.19667, 2, 50, 46.11, 27.62, 0.23085, 51, 13.71, 23.91, 0.76915, 3, 50, 55.7, 27.32, 0.06151, 51, 22.65, 20.43, 0.93375, 52, -14.55, 23.58, 0.00473, 3, 50, 66.18, 26.98, 0.00404, 51, 32.42, 16.63, 0.87621, 52, -5.84, 17.74, 0.11975, 2, 51, 43.05, 12.49, 0.33607, 52, 3.62, 11.38, 0.66393, 2, 51, 49.73, 9.89, 0.03662, 52, 9.58, 7.39, 0.96338, 1, 52, 19.38, 8.13, 1, 1, 52, 28.57, 8.82, 1, 1, 52, 41.21, 9.77, 1, 1, 52, 47.03, 10.2, 1, 1, 52, 57.37, 12.74, 1, 1, 52, 65.55, 14.74, 1, 1, 52, 67.62, 10.15, 1, 1, 52, 61.83, 2.05, 1, 1, 52, 55.07, -7.42, 1, 1, 52, 47.2, -10.96, 1, 1, 52, 32.66, -17.52, 1, 1, 52, 25.31, -16.89, 1, 2, 51, 58.83, -11.94, 0.00229, 52, 13.7, -15.9, 0.99771, 2, 51, 44.93, -13.83, 0.30925, 52, -0.27, -14.71, 0.69075, 2, 51, 28.32, -16.08, 0.97848, 52, -16.98, -13.28, 0.02152, 2, 50, 61.04, -11.46, 0.02022, 51, 14.78, -17.91, 0.97978, 2, 50, 43.01, -20.71, 0.66089, 51, -5.3, -20.63, 0.33911, 1, 50, 20.39, -32.31, 1, 1, 49, 9.36, -1.96, 1, 1, 49, 23, -1.89, 1, 1, 49, 37.74, -1.96, 1, 2, 49, 53.11, 0.42, 0.99161, 50, -8.25, 2, 0.00839, 1, 50, 6.48, -2.76, 1, 1, 50, 19.02, -7.74, 1, 2, 50, 29.39, -6.89, 0.99915, 51, -13.55, -3.07, 0.00085, 2, 50, 47.94, -1.35, 0.01098, 51, 5.79, -4.02, 0.98902, 1, 51, 21.24, -4.02, 1, 2, 51, 38.38, -2.05, 0.75972, 52, -4.1, -1.79, 0.24028, 1, 52, 9.06, -2.95, 1, 1, 52, 21.55, -1.94, 1, 1, 52, 29.76, -1.01, 1, 1, 52, 39.12, 0.13, 1, 1, 52, 48.26, 2.4, 1, 1, 52, 56.34, 6.35, 1], "hull": 35, "edges": [8, 10, 10, 12, 44, 46, 8, 70, 12, 14, 70, 14, 8, 6, 6, 72, 14, 16, 72, 16, 6, 4, 4, 74, 16, 18, 74, 18, 4, 2, 2, 76, 18, 20, 76, 20, 2, 0, 0, 68, 0, 78, 20, 22, 78, 22, 68, 80, 22, 24, 80, 24, 66, 68, 66, 82, 24, 26, 82, 26, 64, 66, 64, 84, 26, 28, 84, 28, 62, 64, 62, 86, 28, 30, 30, 32, 86, 30, 60, 62, 60, 88, 88, 32, 58, 60, 58, 90, 32, 34, 90, 34, 54, 56, 56, 58, 56, 92, 34, 36, 92, 36, 54, 94, 94, 36, 50, 52, 52, 54, 52, 96, 36, 38, 38, 40, 96, 38, 50, 98, 98, 40, 46, 48, 48, 50, 48, 100, 40, 42, 42, 44, 100, 42, 100, 44], "width": 96, "height": 85}}, "f9": {"f9": {"type": "mesh", "uvs": [0.67494, 0.42522, 0.66872, 0.45327, 0.66321, 0.47815, 0.656, 0.51074, 0.64799, 0.54687, 0.61441, 0.56724, 0.58554, 0.58475, 0.55087, 0.60412, 0.51241, 0.6256, 0.49269, 0.59603, 0.48147, 0.57921, 0.48208, 0.54999, 0.48267, 0.5221, 0.50427, 0.49236, 0.52553, 0.4631, 0.55437, 0.43994, 0.57967, 0.41961, 0.64835, 0.38906, 0.61915, 0.42593, 0.60317, 0.45391, 0.5943, 0.47662, 0.57743, 0.50671, 0.56589, 0.53206, 0.53926, 0.5574, 0.52772, 0.58064, 0.51973, 0.60017], "triangles": [23, 11, 12, 22, 23, 12, 24, 10, 11, 23, 24, 11, 6, 23, 5, 24, 23, 6, 9, 10, 24, 25, 9, 24, 7, 24, 6, 25, 24, 7, 8, 9, 25, 8, 25, 7, 21, 14, 20, 13, 14, 21, 21, 20, 3, 22, 13, 21, 12, 13, 22, 4, 21, 3, 22, 21, 4, 5, 22, 4, 23, 22, 5, 18, 16, 17, 0, 18, 17, 1, 18, 0, 19, 16, 18, 19, 18, 1, 15, 16, 19, 20, 15, 19, 14, 15, 20, 2, 19, 1, 20, 19, 2, 3, 20, 2], "vertices": [1, 70, -4.61, 17.1, 1, 1, 70, 8.51, 16.6, 1, 2, 70, 20.14, 16.17, 0.91846, 71, -11.04, 16.89, 0.08154, 2, 70, 35.38, 15.6, 0.30182, 71, 4.07, 18.95, 0.69818, 3, 70, 52.28, 14.96, 0.01734, 71, 20.83, 21.23, 0.97919, 72, -15.2, 20.74, 0.00347, 2, 71, 32.39, 14.77, 0.74487, 72, -2.22, 18.16, 0.25513, 2, 71, 42.32, 9.22, 0.22887, 72, 8.94, 15.93, 0.77113, 2, 71, 53.51, 2.36, 0.00948, 72, 21.7, 12.84, 0.99052, 1, 72, 35.85, 9.41, 1, 1, 72, 27.35, -2.65, 1, 1, 72, 22.51, -9.51, 1, 2, 71, 34.28, -22.57, 0.05551, 72, 11.07, -16.79, 0.94449, 2, 71, 21.75, -25.82, 0.36162, 72, 0.15, -23.74, 0.63838, 3, 70, 30.8, -26.89, 0.06128, 71, 6.87, -23.69, 0.7181, 72, -14.66, -26.3, 0.22062, 3, 70, 16.74, -22.32, 0.44692, 71, -7.78, -21.61, 0.51891, 72, -29.24, -28.82, 0.03417, 3, 70, 5.29, -15.4, 0.87275, 71, -20.24, -16.76, 0.12619, 72, -42.59, -28.04, 0.00107, 2, 70, -4.75, -9.32, 0.99372, 71, -31.18, -12.5, 0.00628, 1, 70, -20.63, 8.23, 1, 1, 70, -2.85, 1.8, 1, 2, 70, 10.49, -1.38, 0.99293, 71, -17.53, -2.05, 0.00707, 3, 70, 21.21, -2.84, 0.8328, 71, -6.72, -1.65, 0.16651, 72, -34.38, -9.5, 0.00068, 2, 71, 7.98, -2.46, 0.98801, 72, -20.14, -5.75, 0.01199, 2, 71, 20.16, -2.44, 0.91364, 72, -8.55, -1.98, 0.08636, 1, 72, 5.31, -1.7, 1, 2, 71, 44.68, -6.67, 0.00163, 72, 16.08, 1.53, 0.99837, 1, 72, 24.87, 4.65, 1], "hull": 18, "edges": [32, 34, 34, 0, 32, 36, 36, 0, 28, 30, 30, 32, 30, 38, 0, 2, 38, 2, 28, 40, 2, 4, 40, 4, 24, 26, 26, 28, 26, 42, 4, 6, 6, 8, 42, 6, 24, 44, 44, 8, 20, 22, 22, 24, 22, 46, 8, 10, 10, 12, 46, 10, 20, 48, 48, 12, 16, 18, 18, 20, 18, 50, 12, 14, 14, 16, 50, 14], "width": 165, "height": 278}}, "zw3": {"zw3": {"type": "mesh", "uvs": [1, 0.01616, 1, 0.04792, 0.94961, 0.2198, 0.65522, 0.29626, 0.62371, 0.52132, 0.48486, 0.76627, 0.44973, 0.83018, 0.34795, 0.91645, 0.23589, 0.92644, 0.15846, 0.94666, 0.09497, 0.96862, 0.04479, 0.96758, 0.01686, 0.94918, 0.04045, 0.90347, 0.11757, 0.86673, 0.20875, 0.83378, 0.25187, 0.78861, 0.32078, 0.789, 0.46896, 0.49594, 0.88987, 0.00275], "triangles": [19, 0, 1, 2, 19, 1, 3, 19, 2, 18, 19, 3, 4, 18, 3, 5, 18, 4, 5, 17, 18, 6, 17, 5, 7, 17, 6, 15, 7, 8, 14, 15, 8, 17, 15, 16, 7, 15, 17, 9, 14, 8, 10, 13, 14, 10, 11, 13, 12, 13, 11, 14, 9, 10], "vertices": [8.12, 16.33, 8.17, 12.87, 3.67, -5.94, -24.17, -14.69, -26.79, -39.27, -39.58, -66.16, -42.81, -73.18, -52.34, -82.72, -62.97, -83.97, -70.29, -86.29, -76.29, -88.77, -81.05, -88.73, -83.74, -86.76, -81.57, -81.75, -74.31, -77.63, -65.7, -73.91, -61.68, -68.93, -55.13, -68.87, -41.54, -36.72, -2.36, 17.63], "hull": 20, "edges": [0, 38, 0, 2, 2, 4, 4, 6, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 6, 8, 8, 10], "width": 57, "height": 65}}, "zw4": {"zw3": {"type": "mesh", "uvs": [0.98687, 0, 1, 0.04842, 0.94812, 0.22199, 0.76418, 0.22233, 0.64726, 0.52143, 0.49452, 0.73532, 0.43407, 0.84399, 0.37252, 0.90401, 0.29376, 0.92581, 0.19709, 0.9444, 0.10836, 0.96262, 0.04601, 0.96955, 0.01888, 0.93272, 0.05173, 0.90102, 0.10112, 0.87907, 0.14485, 0.85036, 0.21452, 0.82992, 0.26576, 0.78495, 0.28979, 0.82449, 0.45602, 0.52661, 0.86365, 0.01724], "triangles": [1, 2, 20, 1, 20, 0, 3, 20, 2, 20, 3, 19, 4, 19, 3, 5, 19, 4, 5, 18, 19, 16, 17, 18, 6, 18, 5, 7, 18, 6, 8, 18, 7, 16, 18, 8, 9, 15, 16, 9, 16, 8, 14, 15, 9, 10, 14, 9, 13, 14, 10, 11, 12, 13, 10, 11, 13], "vertices": [6.85, 18.07, 8.17, 12.81, 3.53, -6.18, -13.94, -6.48, -24.56, -39.24, -38.71, -62.77, -44.28, -74.7, -50.03, -81.33, -57.47, -83.82, -66.63, -85.98, -75.02, -88.1, -80.93, -88.94, -83.57, -84.97, -80.5, -81.46, -75.85, -79, -71.74, -75.81, -65.16, -73.48, -60.36, -68.51, -58.02, -72.78, -42.71, -40.08, -4.83, 16.02], "hull": 21, "edges": [0, 40, 0, 2, 2, 4, 4, 6, 26, 28, 28, 30, 20, 22, 18, 20, 14, 16, 16, 18, 12, 14, 10, 12, 6, 8, 8, 10, 38, 40, 36, 38, 34, 36, 30, 32, 32, 34, 22, 24, 24, 26], "width": 57, "height": 65}}, "zs1": {"zs1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [72.59, -24.82, -22.93, -15.27, -16.56, 48.41, 78.96, 38.86], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 57, "height": 38}}, "zs2": {"zs2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [124, -62.59, -48.22, -46.19, -38.08, 60.33, 134.14, 43.93], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 64}}, "zs3": {"zs2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [124, -62.59, -48.22, -46.19, -38.08, 60.33, 134.14, 43.93], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 64}}, "zs4": {"zs4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [56.37, -54.23, -45.32, -37.86, -25.14, 87.52, 76.55, 71.16], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 61, "height": 76}}, "zs5": {"zs5": {"type": "mesh", "uvs": [0.98793, 0, 0.98784, 0.07091, 0.98773, 0.14927, 0.98766, 0.20333, 0.9876, 0.24956, 0.98753, 0.30128, 0.98747, 0.34738, 0.98741, 0.39005, 0.98736, 0.43144, 0.9873, 0.47446, 0.98724, 0.51649, 0.98718, 0.56359, 0.98711, 0.61454, 0.98704, 0.66421, 0.98696, 0.7228, 0.98691, 0.76301, 0.98685, 0.80766, 0.98679, 0.85231, 0.98672, 0.90394, 0.98664, 0.96613, 0.69181, 1, 0.28852, 1, 0.23182, 0.96359, 0.15707, 0.91559, 0.09598, 0.87636, 0, 0.81472, 0, 0.76862, 0.05471, 0.73056, 0.12829, 0.67937, 0.19266, 0.6346, 0.26646, 0.58326, 0.34096, 0.53143, 0.43689, 0.49237, 0.54862, 0.44688, 0.51265, 0.42291, 0.45567, 0.38493, 0.3842, 0.33729, 0.31929, 0.29403, 0.27203, 0.26253, 0.21518, 0.22465, 0.43026, 0.15841, 0.94462, 0, 0.74458, 0.11401, 0.63803, 0.17591, 0.60844, 0.22684, 0.69723, 0.2715, 0.70906, 0.31224, 0.71035, 0.36266, 0.7344, 0.40342, 0.71997, 0.44099, 0.6911, 0.48274, 0.67667, 0.52095, 0.65262, 0.56868, 0.63818, 0.62345, 0.58045, 0.67695, 0.52272, 0.72216, 0.5023, 0.7665, 0.53919, 0.81185, 0.6077, 0.86348, 0.54446, 0.91789, 0.52338, 0.96213], "triangles": [21, 60, 20, 20, 60, 19, 21, 22, 60, 60, 59, 19, 19, 59, 18, 60, 22, 59, 59, 22, 23, 59, 58, 18, 58, 59, 24, 59, 23, 24, 18, 58, 17, 58, 57, 17, 17, 57, 16, 24, 57, 58, 24, 25, 57, 25, 56, 57, 25, 26, 56, 57, 56, 16, 16, 56, 15, 56, 55, 15, 15, 55, 14, 26, 27, 56, 56, 27, 55, 27, 28, 55, 55, 54, 14, 14, 54, 13, 55, 28, 54, 28, 29, 54, 54, 53, 13, 54, 29, 53, 13, 53, 12, 29, 30, 53, 53, 52, 12, 12, 52, 11, 53, 30, 52, 30, 31, 52, 52, 51, 11, 52, 31, 51, 11, 51, 10, 31, 32, 51, 51, 50, 10, 51, 32, 50, 10, 50, 9, 32, 33, 50, 50, 49, 9, 50, 33, 49, 9, 49, 8, 33, 34, 49, 49, 48, 8, 49, 34, 48, 8, 48, 7, 34, 35, 48, 35, 47, 48, 48, 47, 7, 7, 47, 6, 35, 36, 47, 36, 46, 47, 47, 46, 6, 6, 46, 5, 36, 37, 46, 37, 45, 46, 46, 45, 5, 5, 45, 4, 37, 38, 45, 38, 44, 45, 45, 44, 4, 38, 39, 44, 4, 44, 3, 44, 43, 3, 44, 39, 43, 39, 40, 43, 3, 43, 2, 2, 43, 42, 43, 40, 42, 42, 40, 41, 2, 42, 1, 42, 41, 1, 1, 41, 0], "vertices": [1, 90, -47.22, 1.16, 1, 1, 90, -7.78, 5.1, 1, 1, 90, 35.81, 9.45, 1, 2, 90, 65.88, 12.45, 0.95252, 91, -22.71, 4.64, 0.04748, 1, 91, 2.52, 10.22, 1, 2, 91, 30.75, 16.45, 0.99954, 92, -59.21, -8.89, 0.00046, 2, 91, 55.91, 22.01, 0.90084, 92, -34.38, -2, 0.09916, 2, 91, 79.2, 27.16, 0.28257, 92, -11.39, 4.37, 0.71743, 1, 92, 10.91, 10.55, 1, 2, 92, 34.08, 16.97, 0.99887, 93, -69.18, -7.25, 0.00113, 2, 92, 56.73, 23.25, 0.92953, 93, -46.91, 0.24, 0.07047, 2, 92, 82.09, 30.28, 0.4328, 93, -21.96, 8.62, 0.5672, 2, 93, 5.04, 17.69, 0.98456, 94, -53.7, -4.32, 0.01544, 2, 93, 31.36, 26.54, 0.65696, 94, -27.32, 4.36, 0.34304, 2, 94, 3.79, 14.6, 0.99979, 95, -66.66, 3.17, 0.00021, 2, 94, 25.14, 21.62, 0.93726, 95, -44.69, 7.91, 0.06274, 2, 94, 48.85, 29.42, 0.4574, 95, -20.29, 13.17, 0.5426, 1, 95, 4.11, 18.43, 1, 1, 95, 32.32, 24.52, 1, 1, 95, 66.3, 31.85, 1, 1, 95, 89.41, 14.52, 1, 1, 95, 95.71, -14.65, 1, 2, 94, 149.11, 3.62, 0.02299, 95, 76.7, -23.05, 0.97701, 2, 94, 125.36, -10.03, 0.26556, 95, 51.64, -34.12, 0.73444, 3, 93, 164.78, 1.83, 0.01831, 94, 105.94, -21.18, 0.6964, 95, 31.16, -43.16, 0.2853, 2, 93, 134.39, -15.89, 0.27042, 94, 75.43, -38.71, 0.72958, 2, 93, 109.96, -24.1, 0.71084, 94, 50.96, -46.77, 0.28916, 3, 92, 190.48, -11.26, 0.0004, 93, 88.51, -27.04, 0.98063, 94, 29.48, -49.57, 0.01897, 2, 92, 161.45, -13.66, 0.05349, 93, 59.65, -31, 0.94651, 2, 92, 136.06, -15.76, 0.33865, 93, 34.41, -34.46, 0.66135, 2, 92, 106.95, -18.17, 0.9664, 93, 5.46, -38.43, 0.0336, 1, 92, 77.56, -20.6, 1, 1, 92, 54.62, -19.6, 1, 2, 91, 117.24, 2.32, 0.06286, 92, 27.9, -18.43, 0.93714, 2, 91, 104.73, -3.17, 0.32426, 92, 15.7, -24.57, 0.67574, 2, 91, 84.91, -11.88, 0.92477, 92, -3.63, -34.31, 0.07523, 2, 90, 144.84, -24.54, 0.0091, 91, 60.05, -22.79, 0.9909, 2, 90, 121.25, -31.72, 0.14694, 91, 37.48, -32.7, 0.85306, 2, 90, 104.08, -36.95, 0.43715, 91, 21.04, -39.92, 0.56285, 2, 90, 83.42, -43.25, 0.79586, 91, 1.27, -48.6, 0.20414, 1, 90, 44.99, -31.09, 1, 1, 90, -46.9, -2.03, 1, 1, 90, 17.98, -10.42, 1, 1, 90, 53.2, -14.82, 1, 2, 90, 81.75, -14.17, 0.83511, 91, -3.82, -19.92, 0.16489, 2, 90, 105.93, -5.15, 0.07741, 91, 19.14, -8.12, 0.92259, 2, 90, 128.51, -2.01, 0.00252, 91, 41.19, -2.34, 0.99748, 2, 91, 68.69, 3.84, 0.96541, 92, -20.66, -19.48, 0.03459, 2, 91, 90.55, 10.49, 0.35092, 92, 0.82, -11.67, 0.64908, 2, 91, 111.29, 13.99, 0.03738, 92, 21.34, -7.09, 0.96262, 2, 91, 134.53, 16.94, 6e-05, 92, 44.4, -2.91, 0.99994, 2, 92, 65.27, 1.77, 0.99533, 93, -37.22, -20.75, 0.00467, 2, 92, 91.46, 7.19, 0.81247, 93, -11.37, -13.94, 0.18753, 2, 92, 121.24, 14.34, 0.09774, 93, 17.99, -5.19, 0.90226, 2, 93, 47.7, 0.29, 0.99683, 94, -11.15, -21.99, 0.00317, 2, 93, 73.02, 4.29, 0.86124, 94, 14.19, -18.15, 0.13876, 2, 93, 96.99, 10.76, 0.38802, 94, 38.21, -11.83, 0.61198, 2, 93, 120.15, 21.42, 0.00583, 94, 61.43, -1.31, 0.99417, 2, 94, 87.26, 12.53, 0.23975, 95, 16.13, -7.67, 0.76025, 2, 94, 117.62, 17.6, 0.03226, 95, 46.85, -5.82, 0.96774, 2, 94, 141.59, 23.85, 0.00153, 95, 71.35, -2.13, 0.99847], "hull": 42, "edges": [0, 82, 38, 40, 40, 42, 50, 52, 78, 80, 80, 82, 80, 84, 0, 2, 84, 2, 78, 86, 2, 4, 86, 4, 76, 78, 76, 88, 4, 6, 88, 6, 74, 76, 74, 90, 6, 8, 90, 8, 72, 74, 72, 92, 8, 10, 92, 10, 70, 72, 70, 94, 10, 12, 94, 12, 66, 68, 68, 70, 68, 96, 12, 14, 96, 14, 66, 98, 14, 16, 98, 16, 62, 64, 64, 66, 64, 100, 16, 18, 100, 18, 62, 102, 18, 20, 102, 20, 60, 62, 60, 104, 20, 22, 104, 22, 58, 60, 58, 106, 22, 24, 106, 24, 56, 58, 56, 108, 24, 26, 108, 26, 52, 54, 54, 56, 54, 110, 26, 28, 110, 28, 52, 112, 28, 30, 112, 30, 50, 114, 30, 32, 114, 32, 48, 50, 48, 116, 32, 34, 116, 34, 46, 48, 46, 118, 34, 36, 36, 38, 118, 36, 42, 44, 44, 46, 44, 120, 120, 38], "width": 44, "height": 335}}, "s2": {"s2": {"type": "mesh", "uvs": [0.91349, 0.00215, 0.99113, 0.00195, 0.99684, 0.06507, 1, 0.09996, 1, 0.10672, 0.99626, 0.13383, 0.98617, 0.20708, 0.96691, 0.34687, 0.96154, 0.38587, 0.95755, 0.41481, 0.93075, 0.44671, 0.90101, 0.48212, 0.86756, 0.52194, 0.84364, 0.55041, 0.80997, 0.59953, 0.79329, 0.62388, 0.77702, 0.68306, 0.7596, 0.74646, 0.74202, 0.8104, 0.7251, 0.872, 0.70598, 0.94154, 0.68992, 1, 0.58472, 0.99981, 0.48013, 0.99963, 0.35675, 0.99941, 0.27149, 0.99926, 0.19598, 0.99912, 0.22038, 0.96653, 0.24117, 0.93877, 0.27102, 0.89889, 0.29955, 0.86079, 0.34568, 0.79917, 0.38684, 0.74419, 0.30577, 0.79071, 0.24333, 0.82654, 0.20091, 0.85089, 0.1549, 0.87729, 0.13977, 0.88598, 0.11482, 0.9003, 0.10065, 0.90843, 0.08355, 0.92024, 0.06492, 0.93312, 0.01985, 0.90569, 0.01705, 0.87621, 0.01315, 0.83528, 0.0063, 0.76337, 0.0005, 0.7025, 0.02805, 0.68725, 0.04649, 0.67704, 0.08855, 0.65376, 0.104, 0.64077, 0.11423, 0.6114, 0.12673, 0.57552, 0.14095, 0.53471, 0.15748, 0.48726, 0.20747, 0.481, 0.28635, 0.47113, 0.35629, 0.46238, 0.42739, 0.45349, 0.49432, 0.44511, 0.53204, 0.42372, 0.57803, 0.39763, 0.57868, 0.34873, 0.57937, 0.29692, 0.58065, 0.19994, 0.58173, 0.11898, 0.70615, 0.14162, 0.70325, 0.10731, 0.7022, 0.09489, 0.71602, 0.07398, 0.73917, 0.03894, 0.76606, 0.02655, 0.7821, 0.01916, 0.81847, 0.0024, 0.77274, 0.05837, 0.8093, 0.0812, 0.86134, 0.13204, 0.91462, 0.18964, 0.93714, 0.23413, 0.95188, 0.28879, 0.79609, 0.02979, 0.84274, 0.0564, 0.90724, 0.10389, 0.95614, 0.15355, 0.90048, 0.03288, 0.96186, 0.07775, 0.95406, 0.02199, 0.97851, 0.03724, 0.75691, 0.10258, 0.81933, 0.14309, 0.87343, 0.19537, 0.914, 0.24285, 0.93325, 0.29058, 0.76592, 0.13814, 0.83325, 0.17801, 0.88494, 0.2287, 0.76388, 0.15864, 0.82917, 0.20079, 0.8727, 0.24579, 0.91282, 0.29762, 0.93935, 0.36882, 0.64078, 0.17573, 0.72919, 0.21959, 0.78836, 0.25946, 0.83733, 0.31528, 0.86794, 0.37827, 0.89582, 0.43294, 0.62989, 0.24954, 0.69451, 0.27403, 0.75164, 0.32188, 0.80333, 0.37884, 0.84481, 0.45459, 0.65778, 0.30083, 0.73191, 0.36178, 0.77204, 0.42557, 0.81217, 0.49449, 0.61425, 0.35722, 0.69419, 0.41992, 0.73939, 0.47626, 0.7734, 0.54347, 0.63735, 0.46934, 0.69042, 0.52296, 0.74824, 0.58846, 0.56582, 0.50203, 0.63738, 0.5748, 0.71763, 0.63688, 0.49997, 0.51931, 0.58024, 0.6158, 0.67002, 0.68757, 0.70647, 0.42969, 0.66635, 0.49802, 0.61234, 0.54691, 0.55333, 0.58556, 0.43973, 0.53288, 0.47134, 0.6155, 0.51767, 0.6735, 0.61033, 0.74517, 0.37357, 0.56126, 0.41083, 0.63571, 0.45529, 0.71017, 0.56642, 0.81035, 0.29055, 0.55223, 0.35069, 0.66775, 0.52393, 0.86976, 0.47621, 0.89987, 0.42326, 0.93054, 0.37946, 0.96394, 0.31556, 0.97964, 0.25258, 0.98492, 0.21796, 0.52995, 0.21419, 0.56147, 0.21984, 0.62609, 0.25811, 0.71067, 0.15272, 0.53783, 0.1615, 0.60245, 0.17844, 0.73641, 0.11759, 0.73274, 0.06552, 0.7196, 0.09437, 0.80891, 0.03101, 0.77581, 0.05987, 0.8231, 0.05234, 0.8667, 0.08747, 0.88351, 0.0467, 0.89875, 0.30342, 0.57696, 0.31608, 0.60128, 0.15518, 0.5117], "triangles": [157, 48, 49, 47, 48, 157, 154, 155, 50, 50, 155, 156, 49, 50, 156, 157, 49, 156, 152, 155, 151, 46, 159, 45, 157, 46, 47, 157, 159, 46, 33, 152, 32, 157, 158, 159, 156, 158, 157, 35, 158, 156, 160, 159, 158, 34, 155, 152, 34, 152, 33, 155, 35, 156, 44, 45, 159, 44, 159, 160, 34, 35, 155, 161, 44, 160, 43, 44, 161, 36, 158, 35, 37, 162, 160, 158, 37, 160, 161, 160, 162, 36, 37, 158, 163, 43, 161, 163, 161, 162, 38, 162, 37, 42, 43, 163, 39, 162, 38, 40, 163, 162, 39, 40, 162, 41, 163, 40, 42, 163, 41, 149, 55, 56, 166, 54, 55, 149, 166, 55, 166, 53, 54, 153, 53, 166, 153, 166, 149, 141, 56, 57, 137, 141, 57, 149, 56, 141, 133, 137, 57, 150, 153, 149, 150, 149, 141, 52, 53, 153, 164, 141, 137, 150, 141, 164, 165, 164, 137, 154, 153, 150, 52, 153, 154, 51, 52, 154, 151, 150, 164, 151, 164, 165, 154, 150, 151, 138, 137, 134, 165, 137, 138, 142, 165, 138, 142, 138, 139, 142, 152, 151, 142, 151, 165, 154, 50, 51, 154, 151, 155, 32, 142, 139, 152, 142, 32, 120, 123, 60, 120, 60, 61, 59, 60, 123, 126, 59, 123, 58, 59, 126, 133, 58, 126, 57, 58, 133, 132, 126, 123, 134, 133, 126, 134, 126, 132, 137, 133, 134, 140, 139, 136, 140, 136, 18, 140, 32, 139, 143, 32, 140, 31, 32, 143, 19, 140, 18, 144, 31, 143, 145, 30, 31, 144, 145, 31, 146, 29, 30, 19, 143, 140, 20, 143, 19, 145, 146, 30, 147, 28, 29, 146, 147, 29, 148, 27, 28, 147, 148, 28, 26, 27, 148, 25, 148, 147, 26, 148, 25, 24, 147, 146, 25, 147, 24, 144, 22, 145, 144, 21, 22, 23, 145, 22, 146, 145, 23, 24, 146, 23, 144, 143, 21, 20, 21, 143, 132, 123, 131, 132, 131, 124, 127, 132, 124, 127, 135, 134, 127, 134, 132, 16, 125, 15, 128, 124, 125, 128, 125, 16, 127, 124, 128, 135, 127, 128, 139, 138, 134, 139, 134, 135, 136, 135, 128, 17, 128, 16, 136, 139, 135, 17, 136, 128, 18, 136, 17, 116, 62, 63, 116, 112, 113, 61, 62, 116, 117, 116, 113, 61, 116, 117, 114, 113, 110, 129, 117, 113, 114, 129, 113, 111, 105, 106, 114, 110, 111, 120, 61, 117, 118, 129, 114, 11, 106, 10, 111, 106, 11, 115, 114, 111, 118, 114, 115, 129, 130, 120, 129, 120, 117, 130, 129, 118, 12, 111, 11, 115, 111, 12, 121, 130, 118, 119, 118, 115, 121, 118, 119, 131, 123, 120, 131, 120, 130, 131, 130, 121, 13, 115, 12, 119, 115, 13, 124, 131, 121, 122, 121, 119, 124, 121, 122, 14, 119, 13, 122, 119, 14, 15, 122, 14, 125, 124, 122, 125, 122, 15, 86, 0, 1, 80, 72, 73, 84, 73, 0, 84, 0, 86, 87, 86, 1, 81, 73, 84, 80, 73, 81, 80, 74, 71, 80, 71, 72, 70, 71, 74, 87, 1, 2, 69, 70, 74, 85, 86, 87, 85, 87, 2, 84, 86, 85, 75, 80, 81, 74, 80, 75, 85, 2, 3, 88, 69, 74, 88, 74, 75, 68, 69, 88, 82, 84, 85, 81, 84, 82, 85, 3, 4, 67, 68, 88, 76, 81, 82, 75, 81, 76, 5, 85, 4, 83, 82, 85, 89, 93, 88, 75, 89, 88, 67, 88, 93, 66, 67, 93, 76, 89, 75, 5, 83, 85, 96, 66, 93, 94, 96, 93, 101, 65, 66, 90, 94, 89, 94, 93, 89, 83, 76, 82, 77, 76, 83, 76, 90, 89, 77, 90, 76, 64, 65, 101, 97, 96, 94, 95, 97, 94, 6, 83, 5, 77, 83, 6, 102, 66, 96, 102, 96, 97, 101, 66, 102, 91, 95, 90, 95, 94, 90, 78, 77, 6, 77, 91, 90, 78, 91, 77, 98, 97, 95, 107, 64, 101, 107, 101, 102, 103, 102, 97, 103, 97, 98, 108, 107, 102, 108, 102, 103, 79, 78, 6, 92, 91, 78, 79, 92, 78, 95, 91, 92, 63, 64, 107, 92, 98, 95, 99, 98, 92, 112, 107, 108, 63, 107, 112, 104, 103, 98, 104, 98, 99, 109, 108, 103, 109, 103, 104, 7, 79, 6, 112, 116, 63, 109, 112, 108, 113, 112, 109, 100, 99, 92, 7, 92, 79, 92, 8, 100, 105, 104, 99, 105, 99, 100, 110, 109, 104, 110, 104, 105, 113, 109, 110, 92, 7, 8, 9, 100, 8, 106, 105, 100, 10, 106, 100, 9, 10, 100, 111, 110, 105], "vertices": [1, 5, -80.98, -8.82, 1, 1, 5, -101.19, 36.91, 1, 1, 5, -58.21, 59.76, 1, 1, 5, -34.45, 72.4, 1, 1, 5, -29.69, 74.48, 1, 1, 5, -9.62, 80.65, 1, 1, 5, 44.58, 97.3, 1, 2, 5, 148.02, 129.09, 0.85804, 6, -92.08, 121.35, 0.14196, 2, 5, 176.87, 137.95, 0.73446, 6, -64.31, 133.17, 0.26554, 2, 5, 198.29, 144.53, 0.64277, 6, -43.69, 141.94, 0.35723, 2, 5, 227.68, 138.57, 0.49955, 6, -13.83, 139.08, 0.50045, 3, 5, 260.31, 131.96, 0.30897, 6, 19.3, 135.89, 0.68822, 7, -111.67, 169.97, 0.00281, 3, 5, 297, 124.52, 0.13589, 6, 56.57, 132.31, 0.83755, 7, -75.81, 159.21, 0.02656, 4, 5, 323.24, 119.2, 0.05936, 6, 83.22, 129.75, 0.86398, 7, -50.17, 151.52, 0.07631, 8, -236.31, 122.53, 0.00035, 4, 5, 366.54, 114.5, 0.00857, 6, 126.77, 129.59, 0.73806, 7, -7.48, 142.9, 0.24435, 8, -192.89, 119.13, 0.00901, 4, 5, 388, 112.17, 0.00159, 6, 148.36, 129.5, 0.60892, 7, 13.68, 138.62, 0.36512, 8, -171.37, 117.44, 0.02436, 3, 6, 193.1, 142.91, 0.33683, 7, 60.17, 143.08, 0.55753, 8, -125.76, 127.49, 0.10564, 3, 6, 241.02, 157.26, 0.1537, 7, 109.97, 147.85, 0.55494, 8, -76.9, 138.24, 0.29136, 3, 6, 289.35, 171.74, 0.05747, 7, 160.2, 152.66, 0.3803, 8, -27.63, 149.09, 0.56222, 3, 6, 335.92, 185.69, 0.01679, 7, 208.58, 157.3, 0.18521, 8, 19.84, 159.54, 0.79799, 3, 6, 388.49, 201.44, 0.00194, 7, 263.21, 162.53, 0.05044, 8, 73.44, 171.34, 0.94762, 2, 7, 309.13, 166.93, 0.01455, 8, 118.49, 181.26, 0.98545, 2, 7, 330.42, 102.62, 0.0013, 8, 147.39, 119.99, 0.9987, 1, 8, 176.13, 59.07, 1, 1, 8, 210.03, -12.79, 1, 1, 8, 233.46, -62.45, 1, 1, 8, 254.21, -106.43, 1, 1, 8, 224.83, -102.97, 1, 1, 8, 199.8, -100.03, 1, 2, 8, 163.86, -95.8, 0.99962, 10, 209.75, 148.23, 0.00038, 3, 8, 129.51, -91.76, 0.99485, 10, 177.12, 136.77, 0.00492, 11, 34.12, 134.73, 0.00024, 5, 7, 232.72, -92.22, 0.01063, 8, 73.97, -85.23, 0.92579, 9, 255.54, 127.17, 0.00134, 10, 124.35, 118.24, 0.05185, 11, -18.29, 115.22, 0.01039, 5, 7, 184.23, -80.44, 0.11816, 8, 24.4, -79.4, 0.45687, 9, 210.38, 105.94, 0.02528, 10, 77.27, 101.71, 0.29854, 11, -65.06, 97.8, 0.10115, 5, 7, 234.68, -118.66, 0.01656, 8, 79.1, -111.23, 0.07037, 9, 273.64, 107.8, 0.0007, 10, 140.39, 97.14, 0.24934, 11, -1.86, 94.42, 0.66302, 4, 7, 273.54, -148.09, 0.00096, 8, 121.24, -135.76, 0.01473, 10, 189.01, 93.62, 0.03837, 11, 46.82, 91.8, 0.94595, 3, 8, 149.86, -152.41, 0.0032, 10, 222.04, 91.22, 0.00189, 11, 79.89, 90.03, 0.99492, 2, 8, 180.9, -170.48, 0.00016, 11, 115.76, 88.11, 0.99984, 2, 8, 191.11, -176.42, 1e-05, 11, 127.55, 87.47, 0.99999, 1, 11, 147, 86.43, 1, 1, 11, 158.05, 85.84, 1, 1, 11, 172.32, 86.34, 1, 1, 11, 187.87, 86.9, 1, 1, 11, 198.05, 52.5, 1, 1, 11, 185.69, 33.41, 1, 1, 11, 168.53, 6.91, 1, 1, 11, 138.37, -39.65, 1, 2, 10, 251.82, -78.46, 0.00134, 11, 112.84, -79.07, 0.99866, 2, 10, 230.64, -76.57, 0.01219, 11, 91.62, -77.57, 0.98781, 2, 10, 216.46, -75.3, 0.03393, 11, 77.42, -76.57, 0.96607, 2, 10, 184.12, -72.42, 0.18031, 11, 45.03, -74.29, 0.81969, 2, 10, 170.11, -74.03, 0.32904, 11, 31.06, -76.16, 0.67096, 2, 10, 150.88, -87.58, 0.58939, 11, 12.08, -90.07, 0.41061, 2, 10, 127.38, -104.13, 0.80818, 11, -11.1, -107.06, 0.19182, 2, 10, 100.66, -122.96, 0.93963, 11, -37.46, -126.39, 0.06037, 2, 10, 69.58, -144.86, 0.98669, 11, -68.12, -148.86, 0.01331, 3, 9, 198.1, -126.77, 5e-05, 10, 41.42, -128.55, 0.99726, 11, -96.59, -133.09, 0.00268, 2, 9, 151.27, -105.68, 0.07601, 10, -3.03, -102.81, 0.92399, 2, 9, 109.74, -86.99, 0.39236, 10, -42.45, -80, 0.60764, 2, 9, 67.52, -67.98, 0.81426, 10, -82.51, -56.8, 0.18574, 3, 6, 124.09, -105.82, 0.00019, 9, 27.78, -50.09, 0.97904, 10, -120.23, -34.96, 0.02076, 4, 5, 314.55, -103.7, 0.00409, 6, 97.78, -92.84, 0.07854, 9, -1.55, -50.79, 0.91676, 10, -149.48, -32.69, 0.0006, 3, 5, 284.29, -84.63, 0.0597, 6, 65.7, -77.02, 0.44264, 9, -37.3, -51.65, 0.49766, 3, 5, 249.67, -99.34, 0.28696, 6, 32.8, -95.26, 0.52596, 9, -57.99, -83.06, 0.18709, 3, 5, 213, -114.93, 0.59297, 6, -2.05, -114.57, 0.33356, 9, -79.91, -116.33, 0.07347, 3, 5, 144.36, -144.1, 0.93176, 6, -67.28, -150.73, 0.06076, 9, -120.94, -178.62, 0.00747, 2, 5, 87.06, -168.45, 0.98962, 6, -121.73, -180.92, 0.01038, 2, 5, 70.85, -88.07, 0.99813, 6, -146.22, -102.66, 0.00187, 2, 5, 47.43, -100.37, 0.99998, 6, -168.24, -117.34, 2e-05, 1, 5, 38.95, -104.82, 1, 1, 5, 20.65, -103.13, 1, 1, 5, -10.01, -100.29, 1, 1, 5, -25.69, -88.25, 1, 1, 5, -35.04, -81.07, 1, 1, 5, -56.25, -64.79, 1, 1, 5, -5, -74.49, 1, 1, 5, 1.63, -45.88, 1, 1, 5, 23.99, 0.51, 1, 1, 5, 50.78, 49.71, 1, 2, 5, 76.3, 76.73, 0.99841, 6, -157.96, 61.81, 0.00159, 2, 5, 110.99, 102.29, 0.96831, 6, -126.11, 90.85, 0.03169, 1, 5, -31.17, -69.54, 1, 1, 5, -24.48, -33.81, 1, 1, 5, -7.71, 18.89, 1, 1, 5, 14.63, 63.06, 1, 1, 5, -55.97, -7.01, 1, 1, 5, -40.23, 43.04, 1, 1, 5, -77.49, 21.23, 1, 1, 5, -73.07, 40.36, 1, 1, 5, 30.23, -70.18, 1, 1, 5, 42.63, -20.86, 1, 1, 5, 65.47, 27.19, 1, 2, 5, 88.43, 65.77, 0.99756, 6, -144.76, 52.17, 0.00244, 2, 5, 117.06, 91.86, 0.96702, 6, -118.99, 81.1, 0.03298, 1, 5, 52.95, -53.89, 1, 1, 5, 63.63, -1.87, 1, 1, 5, 85.97, 44.26, 1, 1, 5, 67.92, -48.77, 1, 1, 5, 80.73, 2.75, 1, 2, 5, 101.17, 42.32, 0.99929, 6, -129.64, 30.17, 0.00071, 2, 5, 127.3, 81.98, 0.96098, 6, -107.78, 72.34, 0.03902, 2, 5, 170.59, 119.6, 0.78341, 6, -68.64, 114.26, 0.21659, 3, 5, 111.77, -116.11, 0.9738, 6, -102.6, -126.29, 0.02419, 9, -163.57, -173.34, 0.00201, 3, 5, 119.81, -50.42, 0.98748, 6, -101.45, -60.12, 0.0113, 9, -193.23, -114.18, 0.00122, 3, 5, 132.6, -3.21, 0.99987, 6, -93.64, -11.84, 0.0001, 9, -208.71, -67.78, 2e-05, 2, 5, 159.25, 42.9, 0.96404, 6, -71.93, 36.8, 0.03596, 2, 5, 195.71, 80.4, 0.70301, 6, -39.58, 77.89, 0.29699, 2, 5, 227.01, 113.72, 0.48169, 6, -11.91, 114.29, 0.51831, 3, 5, 166.57, -99.74, 0.83524, 6, -49.8, -104.31, 0.14282, 9, -126.98, -129.38, 0.02193, 3, 5, 167.12, -54.07, 0.88619, 6, -54.01, -58.83, 0.10153, 9, -151.8, -91.04, 0.01228, 3, 5, 186.05, -5.61, 0.98759, 6, -40.23, -8.66, 0.01165, 9, -162.85, -40.19, 0.00076, 2, 5, 212.81, 42.46, 0.59796, 6, -18.62, 41.94, 0.40204, 3, 5, 255.44, 90.31, 0.27689, 6, 18.8, 93.97, 0.72132, 7, -120.31, 128.94, 0.00178, 3, 5, 195.49, -67.47, 0.669, 6, -24.4, -69.2, 0.28999, 9, -120.76, -86.49, 0.041, 3, 5, 219.25, -4.93, 0.2423, 6, -7.28, -4.52, 0.75655, 9, -135.58, -21.25, 0.00115, 2, 5, 253.81, 38.43, 0.11087, 6, 22.58, 42.2, 0.88913, 3, 5, 291.98, 83.37, 0.10464, 6, 55.86, 90.87, 0.87624, 7, -84.56, 118.69, 0.01912, 3, 5, 246.46, -75.74, 0.24192, 6, 27.15, -72.12, 0.57767, 9, -73.73, -65.18, 0.18041, 3, 5, 269.96, -9.24, 0.00059, 6, 43.6, -3.53, 0.99385, 9, -90.96, 3.22, 0.00556, 3, 5, 297.96, 34.82, 0.01582, 6, 66.86, 43.2, 0.98059, 7, -83.03, 69.8, 0.00359, 4, 5, 336.5, 75.62, 0.01879, 6, 100.95, 87.79, 0.87405, 7, -40.93, 106.92, 0.10635, 8, -221.75, 79.37, 0.00081, 2, 6, 94.73, -16.55, 0.79936, 9, -39.62, 15.39, 0.20064, 2, 6, 113.67, 33.55, 0.96601, 7, -38.99, 51.24, 0.03399, 4, 5, 374.7, 74.67, 0.00177, 6, 139.04, 90.82, 0.70067, 7, -2.98, 102.49, 0.28842, 8, -183.54, 79.56, 0.00915, 3, 6, 139.36, -44.16, 0.04917, 7, -28.89, -29.98, 0.11385, 9, 12.72, 11.63, 0.83698, 3, 6, 165.21, 23.57, 0.41464, 7, 9.63, 31.43, 0.58511, 8, -162.44, 10.55, 0.00026, 3, 6, 181.15, 92.1, 0.42282, 7, 38.58, 95.56, 0.53751, 8, -141.45, 77.71, 0.03967, 3, 7, -2.87, -66.01, 0.00231, 9, 55.58, -0.12, 0.99735, 10, -87.5, 11.92, 0.00034, 3, 6, 210.81, 7.19, 0.01358, 7, 51.18, 6.5, 0.98615, 8, -118.18, -9.18, 0.00027, 3, 6, 230.19, 84.73, 0.17023, 7, 85.25, 78.8, 0.71541, 8, -93.09, 66.71, 0.11436, 2, 5, 273.66, 1.03, 0.0006, 6, 46.22, 7.06, 0.9994, 1, 6, 104.67, 10.59, 1, 2, 6, 154.54, -1.05, 0.38574, 7, -5.62, 9.36, 0.61426, 2, 7, 34.6, -17.3, 0.85194, 9, 54.22, 61.31, 0.14806, 2, 9, 93.85, -12.32, 0.94892, 10, -50.67, -4.11, 0.05108, 4, 7, 73.13, -60.11, 0.43442, 8, -88.33, -72.64, 0.00465, 9, 111.08, 52.12, 0.50086, 10, -26.98, 58.26, 0.06007, 4, 7, 106.02, -17.7, 0.91971, 9, 110.11, 105.78, 0.05378, 10, -22.5, 111.74, 0.02628, 11, -164.99, 105.96, 0.00023, 3, 6, 287.7, 73.22, 0.0352, 7, 139.43, 56.34, 0.59799, 8, -36.6, 50.96, 0.36681, 2, 9, 141.49, -17.01, 0.03651, 10, -3.75, -13.61, 0.96349, 5, 7, 100.2, -92.16, 0.21391, 8, -57.59, -101.19, 0.03478, 9, 152.27, 44.13, 0.31487, 10, 13.18, 46.12, 0.43316, 11, -128.09, 41.03, 0.00328, 5, 7, 145.47, -46.89, 0.45353, 8, -18.12, -50.78, 0.29794, 9, 159.15, 107.77, 0.07341, 10, 26.49, 108.74, 0.16168, 11, -115.96, 103.88, 0.01345, 3, 6, 345.25, 73.43, 0.00501, 7, 195.93, 45.37, 0.10301, 8, 20.81, 46.9, 0.89198, 1, 10, 33.73, -52.37, 1, 5, 7, 135.82, -121.11, 0.08329, 8, -18.73, -125.62, 0.0691, 9, 198.17, 43.91, 0.05203, 10, 58.82, 41.24, 0.7557, 11, -82.37, 37, 0.03987, 3, 6, 398.49, 72.24, 8e-05, 7, 247.92, 33.85, 0.00374, 8, 73.81, 41.76, 0.99619, 1, 8, 107.91, 23.91, 1, 1, 8, 143.83, 3.21, 1, 1, 8, 179.12, -11.27, 1, 1, 8, 207.67, -43.27, 1, 1, 8, 228.72, -78.18, 1, 2, 10, 59.6, -94.91, 0.98865, 11, -79.04, -99.11, 0.01135, 2, 10, 76.61, -77.47, 0.96834, 11, -62.36, -81.35, 0.03166, 2, 10, 104.74, -36.35, 0.92187, 11, -35, -39.71, 0.07813, 5, 7, 185.99, -167.24, 0.00629, 8, 36.65, -165.35, 0.02179, 9, 266.18, 39.43, 4e-05, 10, 126.02, 29.88, 0.48243, 11, -14.97, 26.9, 0.48946, 2, 10, 96.23, -116.36, 0.93992, 11, -42.02, -119.87, 0.06008, 2, 10, 122.79, -73.98, 0.75925, 11, -16.26, -77, 0.24075, 2, 8, 76.52, -203.22, 0.00109, 11, 37.79, 11.37, 0.99891, 2, 10, 207.36, -13.27, 0.00635, 11, 67.16, -14.72, 0.99365, 2, 10, 227.28, -42.07, 0.00846, 11, 87.62, -43.15, 0.99154, 1, 11, 114.68, 22.66, 1, 1, 11, 131.56, -22.37, 1, 1, 11, 138.95, 17.79, 1, 1, 11, 163.2, 41.44, 1, 1, 11, 153.12, 65.47, 1, 1, 11, 181.09, 58.78, 1, 1, 10, 39.1, -32.33, 1, 1, 10, 44.38, -12.62, 1, 2, 10, 82.46, -131.08, 0.97422, 11, -55.51, -134.85, 0.02578], "hull": 74, "edges": [6, 8, 82, 84, 98, 100, 130, 132, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 14, 144, 146, 146, 160, 160, 148, 144, 160, 160, 162, 162, 164, 164, 166, 12, 14, 166, 12, 146, 168, 168, 170, 8, 10, 10, 12, 170, 10, 2, 0, 0, 146, 0, 172, 172, 174, 2, 4, 4, 6, 174, 4, 140, 142, 142, 144, 142, 148, 140, 148, 136, 138, 138, 140, 138, 176, 176, 178, 178, 180, 180, 182, 182, 184, 14, 16, 16, 18, 184, 16, 132, 134, 134, 136, 134, 186, 186, 188, 188, 190, 190, 184, 132, 192, 192, 194, 194, 196, 196, 198, 198, 200, 18, 20, 200, 20, 130, 202, 202, 204, 204, 206, 206, 208, 208, 210, 210, 212, 20, 22, 212, 22, 128, 130, 128, 214, 214, 216, 216, 218, 218, 220, 220, 222, 22, 24, 24, 26, 222, 24, 126, 128, 126, 224, 224, 226, 226, 228, 228, 230, 230, 26, 122, 124, 124, 126, 124, 232, 232, 234, 236, 238, 26, 28, 28, 30, 238, 28, 122, 240, 242, 244, 244, 30, 118, 120, 120, 122, 120, 246, 248, 250, 30, 32, 250, 32, 118, 252, 254, 256, 32, 34, 256, 34, 226, 234, 234, 240, 240, 246, 246, 252, 234, 258, 258, 236, 226, 258, 240, 260, 260, 242, 258, 260, 246, 262, 262, 248, 260, 262, 252, 264, 264, 254, 262, 264, 116, 118, 116, 266, 266, 268, 268, 270, 270, 272, 34, 36, 272, 36, 114, 116, 114, 274, 274, 276, 276, 278, 278, 280, 36, 38, 280, 38, 112, 114, 112, 282, 284, 64, 64, 286, 38, 40, 40, 42, 286, 40, 62, 64, 62, 288, 288, 42, 60, 62, 60, 290, 42, 44, 290, 44, 58, 60, 58, 292, 44, 46, 292, 46, 56, 58, 56, 294, 46, 48, 294, 48, 52, 54, 54, 56, 54, 296, 48, 50, 50, 52, 296, 50, 108, 110, 110, 112, 110, 298, 298, 300, 300, 302, 302, 304, 64, 66, 304, 66, 306, 308, 308, 310, 66, 68, 310, 68, 98, 312, 68, 70, 312, 70, 96, 98, 96, 314, 314, 316, 70, 72, 316, 72, 92, 318, 318, 320, 72, 74, 320, 74, 88, 322, 322, 324, 74, 76, 76, 78, 324, 76, 84, 86, 86, 88, 86, 326, 78, 80, 80, 82, 326, 80, 88, 90, 90, 92, 282, 328, 284, 330, 330, 328, 330, 302, 328, 300, 300, 306, 302, 308, 308, 104, 104, 106, 106, 108, 306, 106, 100, 102, 102, 104, 274, 282, 282, 298, 108, 332, 332, 306, 298, 332, 266, 274, 252, 266, 264, 268, 268, 276, 284, 304, 304, 310, 310, 312, 312, 314, 92, 94, 94, 96, 314, 94], "width": 386, "height": 461}}, "zs7": {"zs7": {"type": "mesh", "uvs": [0.66193, 0.02721, 1, 0.02852, 1, 0.05252, 0.86472, 0.18069, 0.63133, 0.40183, 0.43137, 0.59128, 0.27823, 0.73638, 0.13679, 0.8704, 0.06061, 0.94257, 0, 1, 0, 0.79844, 0.04953, 0.69033, 0.11285, 0.55216, 0.19175, 0.37996, 0.2884, 0.16901, 0.35392, 0.02602, 0.53939, 0.18358, 0.44874, 0.38679, 0.28424, 0.57341, 0.15835, 0.68538, 0.08449, 0.80149], "triangles": [18, 13, 17, 12, 13, 18, 19, 12, 18, 11, 12, 19, 6, 19, 18, 6, 18, 5, 20, 11, 19, 10, 11, 20, 7, 20, 19, 7, 19, 6, 8, 10, 20, 8, 20, 7, 9, 10, 8, 14, 15, 16, 13, 14, 17, 5, 18, 17, 3, 0, 1, 2, 3, 1, 16, 15, 0, 16, 0, 3, 17, 14, 16, 4, 16, 3, 17, 16, 4, 5, 17, 4], "vertices": [1, 113, -0.13, -1.47, 1, 1, 113, -27.01, 7.69, 1, 1, 113, -26.75, 8.47, 1, 1, 113, -14.59, 8.94, 1, 1, 113, 6.4, 9.76, 1, 3, 113, 24.37, 10.47, 0.34008, 114, 7.65, 9.99, 0.64815, 115, -9.77, 11.84, 0.01177, 3, 113, 38.14, 11.01, 0.00121, 114, 21.36, 8.65, 0.19378, 115, 3.43, 7.87, 0.80501, 1, 115, 15.61, 4.21, 1, 1, 115, 22.18, 2.24, 1, 1, 115, 27.4, 0.68, 1, 1, 115, 23.21, -4.75, 1, 1, 115, 17.67, -5.11, 1, 1, 115, 10.59, -5.58, 1, 2, 114, 22.44, -5.43, 0.11118, 115, 1.76, -6.16, 0.88882, 2, 114, 11.97, -8.22, 0.97973, 115, -9.05, -6.87, 0.02027, 1, 114, 4.88, -10.1, 1, 1, 113, 11.33, 0.26, 1, 2, 113, 20.76, 4.35, 0.53479, 114, 3.24, 4.43, 0.46521, 3, 113, 35.88, 5.92, 0.00267, 114, 18.43, 3.92, 0.36987, 115, -0.36, 3.8, 0.62747, 1, 115, 10.33, 0.34, 1, 1, 115, 17.66, -0.33, 1], "hull": 16, "edges": [2, 4, 18, 20, 2, 0, 0, 30, 28, 30, 28, 32, 4, 6, 32, 6, 26, 28, 26, 34, 6, 8, 34, 8, 24, 26, 24, 36, 8, 10, 36, 10, 20, 22, 22, 24, 22, 38, 10, 12, 38, 12, 20, 40, 12, 14, 40, 14, 14, 16, 16, 18], "width": 50, "height": 20}}, "zs8": {"zs8": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [34.49, -137.75, -103.43, -12.96, -5.48, 95.3, 132.45, -29.49], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 66, "height": 52}}, "zs9": {"zs5": {"type": "mesh", "uvs": [0.98793, 0, 0.98784, 0.07091, 0.98773, 0.14927, 0.98766, 0.20333, 0.9876, 0.24956, 0.98753, 0.30128, 0.98747, 0.34738, 0.98741, 0.39005, 0.98736, 0.43144, 0.9873, 0.47446, 0.98724, 0.51649, 0.98718, 0.56359, 0.98711, 0.61454, 0.98704, 0.66421, 0.98696, 0.7228, 0.98691, 0.76301, 0.98685, 0.80766, 0.98679, 0.85231, 0.98672, 0.90394, 0.98664, 0.96613, 0.69181, 1, 0.28852, 1, 0.23182, 0.96359, 0.15707, 0.91559, 0.09598, 0.87636, 0, 0.81472, 0, 0.76862, 0.05471, 0.73056, 0.12829, 0.67937, 0.19266, 0.6346, 0.26646, 0.58326, 0.34096, 0.53143, 0.43689, 0.49237, 0.54862, 0.44688, 0.51265, 0.42291, 0.45567, 0.38493, 0.3842, 0.33729, 0.31929, 0.29403, 0.27203, 0.26253, 0.21518, 0.22465, 0.43026, 0.15841, 0.94462, 0, 0.74458, 0.11401, 0.63803, 0.17591, 0.60844, 0.22684, 0.69723, 0.2715, 0.70906, 0.31224, 0.71035, 0.36266, 0.7344, 0.40342, 0.71997, 0.44099, 0.6911, 0.48274, 0.67667, 0.52095, 0.65262, 0.56868, 0.63818, 0.62345, 0.58045, 0.67695, 0.52272, 0.72216, 0.5023, 0.7665, 0.53919, 0.81185, 0.6077, 0.86348, 0.54446, 0.91789, 0.52338, 0.96213], "triangles": [21, 60, 20, 20, 60, 19, 21, 22, 60, 60, 59, 19, 19, 59, 18, 60, 22, 59, 59, 22, 23, 59, 58, 18, 58, 59, 24, 59, 23, 24, 18, 58, 17, 58, 57, 17, 17, 57, 16, 24, 57, 58, 24, 25, 57, 25, 56, 57, 25, 26, 56, 57, 56, 16, 16, 56, 15, 56, 55, 15, 15, 55, 14, 26, 27, 56, 56, 27, 55, 27, 28, 55, 55, 54, 14, 14, 54, 13, 55, 28, 54, 28, 29, 54, 54, 53, 13, 54, 29, 53, 13, 53, 12, 29, 30, 53, 53, 52, 12, 12, 52, 11, 53, 30, 52, 30, 31, 52, 52, 51, 11, 52, 31, 51, 11, 51, 10, 31, 32, 51, 51, 50, 10, 51, 32, 50, 10, 50, 9, 32, 33, 50, 50, 49, 9, 50, 33, 49, 9, 49, 8, 33, 34, 49, 49, 48, 8, 49, 34, 48, 8, 48, 7, 34, 35, 48, 35, 47, 48, 48, 47, 7, 7, 47, 6, 35, 36, 47, 36, 46, 47, 47, 46, 6, 6, 46, 5, 36, 37, 46, 37, 45, 46, 46, 45, 5, 5, 45, 4, 37, 38, 45, 38, 44, 45, 45, 44, 4, 38, 39, 44, 4, 44, 3, 44, 43, 3, 44, 39, 43, 39, 40, 43, 3, 43, 2, 2, 43, 42, 43, 40, 42, 42, 40, 41, 2, 42, 1, 42, 41, 1, 1, 41, 0], "vertices": [1, 119, -47.22, 1.16, 1, 1, 119, -7.78, 5.1, 1, 1, 119, 35.81, 9.45, 1, 2, 119, 65.88, 12.45, 0.95252, 120, -22.71, 4.64, 0.04748, 1, 120, 2.52, 10.22, 1, 2, 120, 30.75, 16.45, 0.99954, 121, -59.21, -8.89, 0.00046, 2, 120, 55.91, 22.01, 0.90084, 121, -34.38, -2, 0.09916, 2, 120, 79.2, 27.16, 0.28257, 121, -11.39, 4.37, 0.71743, 1, 121, 10.91, 10.55, 1, 2, 121, 34.08, 16.97, 0.99887, 122, -69.18, -7.25, 0.00113, 2, 121, 56.73, 23.25, 0.92953, 122, -46.91, 0.24, 0.07047, 2, 121, 82.09, 30.28, 0.4328, 122, -21.96, 8.62, 0.5672, 2, 122, 5.04, 17.69, 0.98456, 123, -53.7, -4.32, 0.01544, 2, 122, 31.36, 26.54, 0.65696, 123, -27.32, 4.36, 0.34304, 2, 123, 3.79, 14.6, 0.99979, 124, -66.66, 3.17, 0.00021, 2, 123, 25.14, 21.62, 0.93726, 124, -44.69, 7.91, 0.06274, 2, 123, 48.85, 29.42, 0.4574, 124, -20.29, 13.17, 0.5426, 1, 124, 4.11, 18.43, 1, 1, 124, 32.32, 24.52, 1, 1, 124, 66.3, 31.85, 1, 1, 124, 89.41, 14.52, 1, 1, 124, 95.71, -14.65, 1, 2, 123, 149.11, 3.62, 0.02299, 124, 76.7, -23.05, 0.97701, 2, 123, 125.36, -10.03, 0.26556, 124, 51.64, -34.12, 0.73444, 3, 122, 164.78, 1.83, 0.01831, 123, 105.94, -21.18, 0.6964, 124, 31.16, -43.16, 0.2853, 2, 122, 134.39, -15.89, 0.27042, 123, 75.43, -38.71, 0.72958, 2, 122, 109.96, -24.1, 0.71084, 123, 50.96, -46.77, 0.28916, 3, 121, 190.48, -11.26, 0.0004, 122, 88.51, -27.04, 0.98063, 123, 29.48, -49.57, 0.01897, 2, 121, 161.45, -13.66, 0.05349, 122, 59.65, -31, 0.94651, 2, 121, 136.06, -15.76, 0.33865, 122, 34.41, -34.46, 0.66135, 2, 121, 106.95, -18.17, 0.9664, 122, 5.46, -38.43, 0.0336, 1, 121, 77.56, -20.6, 1, 1, 121, 54.62, -19.6, 1, 2, 120, 117.24, 2.32, 0.06286, 121, 27.9, -18.43, 0.93714, 2, 120, 104.73, -3.17, 0.32426, 121, 15.7, -24.57, 0.67574, 2, 120, 84.91, -11.88, 0.92477, 121, -3.63, -34.31, 0.07523, 2, 119, 144.84, -24.54, 0.0091, 120, 60.05, -22.79, 0.9909, 2, 119, 121.25, -31.72, 0.14694, 120, 37.48, -32.7, 0.85306, 2, 119, 104.08, -36.95, 0.43715, 120, 21.04, -39.92, 0.56285, 2, 119, 83.42, -43.25, 0.79586, 120, 1.27, -48.6, 0.20414, 1, 119, 44.99, -31.09, 1, 1, 119, -46.9, -2.03, 1, 1, 119, 17.98, -10.42, 1, 1, 119, 53.2, -14.82, 1, 2, 119, 81.75, -14.17, 0.83511, 120, -3.82, -19.92, 0.16489, 2, 119, 105.93, -5.15, 0.07741, 120, 19.14, -8.12, 0.92259, 2, 119, 128.51, -2.01, 0.00252, 120, 41.19, -2.34, 0.99748, 2, 120, 68.69, 3.84, 0.96541, 121, -20.66, -19.48, 0.03459, 2, 120, 90.55, 10.49, 0.35092, 121, 0.82, -11.67, 0.64908, 2, 120, 111.29, 13.99, 0.03738, 121, 21.34, -7.09, 0.96262, 2, 120, 134.53, 16.94, 6e-05, 121, 44.4, -2.91, 0.99994, 2, 121, 65.27, 1.77, 0.99533, 122, -37.22, -20.75, 0.00467, 2, 121, 91.46, 7.19, 0.81247, 122, -11.37, -13.94, 0.18753, 2, 121, 121.24, 14.34, 0.09774, 122, 17.99, -5.19, 0.90226, 2, 122, 47.7, 0.29, 0.99683, 123, -11.15, -21.99, 0.00317, 2, 122, 73.02, 4.29, 0.86124, 123, 14.19, -18.15, 0.13876, 2, 122, 96.99, 10.76, 0.38802, 123, 38.21, -11.83, 0.61198, 2, 122, 120.15, 21.42, 0.00583, 123, 61.43, -1.31, 0.99417, 2, 123, 87.26, 12.53, 0.23975, 124, 16.13, -7.67, 0.76025, 2, 123, 117.62, 17.6, 0.03226, 124, 46.85, -5.82, 0.96774, 2, 123, 141.59, 23.85, 0.00153, 124, 71.35, -2.13, 0.99847], "hull": 42, "edges": [0, 82, 38, 40, 40, 42, 50, 52, 78, 80, 80, 82, 80, 84, 0, 2, 84, 2, 78, 86, 2, 4, 86, 4, 76, 78, 76, 88, 4, 6, 88, 6, 74, 76, 74, 90, 6, 8, 90, 8, 72, 74, 72, 92, 8, 10, 92, 10, 70, 72, 70, 94, 10, 12, 94, 12, 66, 68, 68, 70, 68, 96, 12, 14, 96, 14, 66, 98, 14, 16, 98, 16, 62, 64, 64, 66, 64, 100, 16, 18, 100, 18, 62, 102, 18, 20, 102, 20, 60, 62, 60, 104, 20, 22, 104, 22, 58, 60, 58, 106, 22, 24, 106, 24, 56, 58, 56, 108, 24, 26, 108, 26, 52, 54, 54, 56, 54, 110, 26, 28, 110, 28, 52, 112, 28, 30, 112, 30, 50, 114, 30, 32, 114, 32, 48, 50, 48, 116, 32, 34, 116, 34, 46, 48, 46, 118, 34, 36, 36, 38, 118, 36, 42, 44, 44, 46, 44, 120, 120, 38], "width": 44, "height": 335}}, "zs11": {"zs8": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [27.8, -142.72, -110.13, -17.93, -12.17, 90.34, 125.75, -34.45], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 66, "height": 52}}, "zs13": {"zs4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [56.37, -54.23, -45.32, -37.86, -25.14, 87.52, 76.55, 71.16], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 61, "height": 76}}, "f4": {"f4": {"type": "mesh", "uvs": [0.83844, 0, 0.95338, 0.05009, 1, 0.07041, 0.97075, 0.1426, 0.95652, 0.1777, 0.88473, 0.19827, 0.82886, 0.21428, 0.77572, 0.30076, 0.63135, 0.53569, 0.51778, 0.7205, 0.45518, 0.82237, 0.48672, 0.86011, 0.51043, 0.88847, 0.44026, 0.91332, 0.30118, 0.96258, 0.19945, 0.99861, 0.12594, 0.99711, 0.18134, 0.93753, 0.25964, 0.85331, 0.32104, 0.78727, 0.28367, 0.67139, 0.21374, 0.57527, 0.15498, 0.49451, 0.09201, 0.40797, 0.03151, 0.32482, 0, 0.28151, 0, 0.2352, 0.09259, 0.18392, 0.17729, 0.137, 0.25184, 0.09571, 0.32231, 0.11683, 0.44087, 0.15237, 0.54604, 0.1839, 0.61222, 0.20374, 0.6611, 0.21839, 0.66, 0.19481, 0.65799, 0.15163, 0.65502, 0.08788, 0.73104, 0.04221, 0.80129, 0, 0.22417, 0.17644, 0.11321, 0.24337, 0.28206, 0.19782, 0.17351, 0.26383, 0.39905, 0.22757, 0.26879, 0.31217, 0.49553, 0.27684, 0.35804, 0.36795, 0.55463, 0.32983, 0.44608, 0.44047, 0.83526, 0.04535, 0.76048, 0.0909, 0.88833, 0.0909, 0.76772, 0.14854, 0.86541, 0.14018, 0.76772, 0.18759, 0.72551, 0.22385, 0.65595, 0.33837, 0.59691, 0.43742, 0.54134, 0.48963, 0.47188, 0.6101, 0.35207, 0.85639, 0.28261, 0.89387, 0.40416, 0.8952], "triangles": [50, 39, 0, 38, 39, 50, 50, 0, 1, 52, 50, 1, 52, 1, 2, 51, 38, 50, 37, 38, 51, 51, 50, 52, 54, 51, 52, 3, 52, 2, 54, 52, 3, 53, 51, 54, 36, 37, 51, 36, 51, 53, 40, 28, 29, 40, 29, 30, 4, 54, 3, 55, 53, 54, 36, 53, 55, 35, 36, 55, 42, 40, 30, 42, 30, 31, 5, 54, 4, 55, 54, 5, 6, 55, 5, 56, 34, 35, 55, 56, 35, 56, 55, 6, 44, 42, 31, 44, 31, 32, 40, 41, 27, 40, 27, 28, 26, 27, 41, 43, 41, 40, 43, 40, 42, 46, 44, 32, 46, 32, 33, 25, 26, 41, 7, 56, 6, 45, 43, 42, 45, 42, 44, 24, 25, 41, 48, 46, 33, 48, 33, 34, 57, 48, 34, 57, 34, 56, 57, 56, 7, 47, 45, 44, 47, 44, 46, 47, 46, 48, 43, 23, 24, 43, 24, 41, 23, 43, 45, 58, 48, 57, 49, 47, 48, 49, 48, 58, 59, 49, 58, 22, 23, 45, 22, 45, 47, 7, 8, 58, 59, 58, 8, 7, 58, 57, 47, 49, 21, 47, 21, 22, 60, 49, 59, 60, 59, 8, 20, 21, 49, 60, 20, 49, 9, 60, 8, 19, 20, 60, 9, 19, 60, 10, 19, 9, 61, 19, 10, 18, 19, 61, 62, 18, 61, 63, 61, 10, 63, 10, 11, 13, 63, 11, 13, 11, 12, 17, 18, 62, 63, 14, 62, 63, 62, 61, 15, 17, 62, 14, 63, 13, 14, 15, 62, 16, 17, 15], "vertices": [1, 44, 96.55, -182.04, 1, 1, 44, 59.39, -194.16, 1, 1, 44, 44.32, -199.08, 1, 1, 44, 29.72, -173.98, 1, 1, 44, 22.62, -161.77, 1, 1, 44, 31.13, -140.76, 1, 1, 44, 37.76, -124.41, 1, 1, 44, 23.88, -90.39, 1, 1, 44, -13.84, 2.03, 1, 1, 44, -43.51, 74.74, 1, 1, 44, -59.86, 114.82, 1, 1, 44, -76.86, 117.71, 1, 1, 44, -89.63, 119.89, 1, 1, 44, -82.65, 141.65, 1, 1, 44, -68.82, 184.79, 1, 1, 44, -58.7, 216.35, 1, 1, 44, -43.59, 232.02, 1, 1, 44, -37.77, 204.47, 1, 1, 44, -29.56, 165.54, 1, 1, 44, -23.12, 135.01, 1, 1, 44, 17.18, 113.14, 1, 1, 44, 58.39, 103.5, 1, 1, 44, 93.01, 95.4, 1, 1, 44, 130.11, 86.72, 1, 1, 44, 165.75, 78.38, 1, 1, 44, 184.32, 74.03, 1, 1, 44, 197.44, 62.03, 1, 1, 44, 193.47, 28.51, 1, 1, 44, 189.84, -2.15, 1, 1, 44, 186.64, -29.13, 1, 1, 44, 166.58, -39.04, 1, 1, 44, 132.82, -55.72, 1, 1, 44, 102.87, -70.52, 1, 1, 44, 84.03, -79.83, 1, 1, 44, 70.11, -86.71, 1, 1, 44, 77.01, -92.58, 1, 1, 44, 89.65, -103.33, 1, 1, 44, 108.3, -119.21, 1, 1, 44, 106.05, -147.65, 1, 1, 44, 103.98, -173.93, 1, 1, 44, 169.3, -2.16, 1, 1, 44, 172.51, 39.42, 1, 1, 44, 151.68, -9.26, 1, 1, 44, 154.66, 31.55, 1, 1, 44, 119.87, -27.1, 1, 1, 44, 121.93, 23.28, 1, 1, 44, 86.63, -35.4, 1, 1, 44, 88.29, 18.24, 1, 1, 44, 59.81, -34.57, 1, 1, 44, 50.16, 17.81, 1, 1, 44, 84.34, -169.6, 1, 1, 44, 86.37, -141.46, 1, 1, 44, 60.83, -169.38, 1, 1, 44, 68.6, -128.1, 1, 1, 44, 51.45, -151.6, 1, 1, 44, 57.54, -117.98, 1, 1, 44, 55.7, -99.36, 1, 1, 44, 37.15, -54.48, 1, 1, 44, 20.88, -15.92, 1, 1, 44, 17.19, 9.75, 1, 1, 44, -3.06, 56.15, 1, 1, 44, -48.9, 146.15, 1, 1, 44, -45.64, 171.04, 1, 1, 44, -70.3, 144.84, 1], "hull": 40, "edges": [0, 78, 30, 32, 38, 40, 50, 52, 58, 60, 60, 80, 80, 82, 48, 50, 82, 48, 56, 58, 52, 54, 54, 56, 60, 62, 62, 84, 84, 86, 46, 48, 86, 46, 62, 64, 64, 88, 88, 90, 44, 46, 90, 44, 64, 66, 66, 68, 66, 92, 92, 94, 40, 42, 42, 44, 94, 42, 68, 96, 96, 98, 98, 40, 74, 76, 76, 78, 76, 100, 0, 2, 2, 4, 100, 2, 74, 102, 102, 104, 104, 4, 72, 74, 72, 106, 106, 108, 4, 6, 6, 8, 108, 6, 68, 70, 70, 72, 70, 110, 8, 10, 10, 12, 110, 10, 68, 112, 112, 12, 112, 114, 114, 116, 116, 118, 118, 120, 120, 38, 20, 122, 122, 124, 124, 30, 36, 38, 32, 34, 34, 36, 20, 22, 22, 24, 22, 126, 28, 30, 126, 28, 24, 26, 26, 28, 12, 14, 14, 16, 16, 18, 18, 20], "width": 177, "height": 230}}, "f3": {"f3": {"type": "mesh", "uvs": [0.15512, 0.0296, 0.20023, 0.05608, 0.26828, 0.09602, 0.31868, 0.12561, 0.34281, 0.15066, 0.38515, 0.19462, 0.43663, 0.24808, 0.47549, 0.28842, 0.50253, 0.3165, 0.56238, 0.33101, 0.61721, 0.3443, 0.64985, 0.35221, 0.68629, 0.30177, 0.7191, 0.25636, 0.76746, 0.18942, 0.8234, 0.22375, 0.89238, 0.26608, 0.93923, 0.29484, 1, 0.33213, 1, 0.34879, 0.98531, 0.3971, 0.96059, 0.43763, 0.92493, 0.49611, 0.8896, 0.55404, 0.86169, 0.59981, 0.82249, 0.66409, 0.78086, 0.73234, 0.75646, 0.77236, 0.72673, 0.82111, 0.69468, 0.87366, 0.65108, 0.91327, 0.60501, 0.95513, 0.55562, 1, 0.48657, 1, 0.39241, 0.98058, 0.30367, 0.96228, 0.25781, 0.92504, 0.21279, 0.88848, 0.16442, 0.84921, 0.13286, 0.82359, 0.07446, 0.8783, 0.03575, 0.93554, 0.01557, 0.8982, 0.0134, 0.83787, 0.01162, 0.78822, 0.01002, 0.74338, 0.00478, 0.5972, 0.00355, 0.56285, 0.00247, 0.53274, 0.00151, 0.50608, 0, 0.46391, 0, 0.4296, 0, 0.38751, 0, 0.36345, 0, 0.33574, 0.06626, 0.31611, 0.12682, 0.29816, 0.19937, 0.27666, 0.28441, 0.25145, 0.33608, 0.23614, 0.27438, 0.18517, 0.2211, 0.14115, 0.1803, 0.13114, 0.11577, 0.11532, 0.05951, 0.10151, 0.01375, 0.09029, 0.00716, 0.06094, 0, 0.02899, 0, 0.00091, 0.04187, 0.00082, 0.10587, 0.00069, 0.76694, 0.27243, 0.80227, 0.30743, 0.85199, 0.32743, 0.89386, 0.35343, 0.92527, 0.37843, 0.95275, 0.38843, 0.73553, 0.31243, 0.78133, 0.33743, 0.82058, 0.36343, 0.86769, 0.39043, 0.8991, 0.41643, 0.92919, 0.43142, 0.69628, 0.35743, 0.74077, 0.37843, 0.77871, 0.40043, 0.82713, 0.43243, 0.86115, 0.45643, 0.89386, 0.47643, 0.64263, 0.38845, 0.69366, 0.41743, 0.73161, 0.44643, 0.77479, 0.48943, 0.82974, 0.50843, 0.86507, 0.52843, 0.56328, 0.3983, 0.48415, 0.39688, 0.42179, 0.36486, 0.37897, 0.31293, 0.62892, 0.36463, 0.55665, 0.36858, 0.49615, 0.35435, 0.4496, 0.32305, 0.40678, 0.28108, 0.38537, 0.24053, 0.34349, 0.19643, 0.29601, 0.15232, 0.23737, 0.11817, 0.18524, 0.09114, 0.13405, 0.06411, 0.08285, 0.04704, 0.03072, 0.03708, 0.66649, 0.44289, 0.57713, 0.4436, 0.48125, 0.41942, 0.42726, 0.39452, 0.34628, 0.38385, 0.26716, 0.39025, 0.21224, 0.40306, 0.18524, 0.43151, 0.2141, 0.47704, 0.25692, 0.51972, 0.33325, 0.54747, 0.43657, 0.54889, 0.49708, 0.52755, 0.552, 0.49269, 0.59761, 0.4621, 0.73724, 0.52541, 0.67952, 0.56383, 0.60971, 0.5994, 0.53245, 0.61647, 0.4496, 0.65346, 0.36303, 0.67623, 0.26902, 0.67765, 0.1871, 0.65844, 0.13777, 0.63568, 0.09309, 0.61149, 0.04096, 0.59797, 0.69044, 0.49833, 0.62637, 0.53355, 0.5533, 0.57393, 0.48698, 0.60485, 0.39929, 0.62719, 0.31161, 0.64008, 0.24304, 0.63578, 0.18346, 0.61688, 0.13962, 0.60142, 0.08117, 0.57994, 0.03396, 0.56963, 0.06543, 0.54987, 0.10815, 0.5662, 0.1711, 0.58853, 0.23742, 0.60228, 0.29138, 0.60399, 0.37794, 0.59712, 0.46112, 0.57994, 0.52857, 0.54901, 0.58365, 0.51551, 0.62861, 0.48716, 0.35433, 0.35057, 0.2644, 0.3394, 0.17784, 0.34026, 0.12389, 0.34885, 0.05644, 0.37033, 0.03845, 0.415, 0.03059, 0.47084, 0.05307, 0.52152, 0.29363, 0.30504, 0.21382, 0.30847, 0.1385, 0.31792, 0.07105, 0.33768, 0.08792, 0.4107, 0.06881, 0.46912, 0.10253, 0.5241, 0.14525, 0.55932, 0.19358, 0.57479, 0.25878, 0.58767, 0.15424, 0.40297, 0.16323, 0.46311, 0.1992, 0.5052, 0.2318, 0.54214, 0.30262, 0.57049, 0.58182, 0.47269, 0.54768, 0.45537, 0.47461, 0.44077, 0.39705, 0.45194, 0.32848, 0.47943, 0.30037, 0.51379, 0.29479, 0.53349, 0.81859, 0.5619, 0.76576, 0.60399, 0.70955, 0.6521, 0.66346, 0.6942, 0.6095, 0.72598, 0.51733, 0.74145, 0.43639, 0.74059, 0.35433, 0.72341, 0.31274, 0.70623, 0.78711, 0.53699, 0.72978, 0.57565, 0.66908, 0.6186, 0.61625, 0.65296, 0.56004, 0.65812, 0.51283, 0.65382, 0.43077, 0.69506, 0.51395, 0.69935, 0.58702, 0.69248, 0.66009, 0.6564, 0.7073, 0.61516, 0.75676, 0.57822, 0.81297, 0.54386, 0.84556, 0.59445, 0.63115, 0.90463, 0.51847, 0.93723, 0.41673, 0.93054, 0.33577, 0.90212, 0.2756, 0.85028, 0.24607, 0.80012, 0.81165, 0.62037, 0.7657, 0.67722, 0.70444, 0.73073, 0.6388, 0.77169, 0.56332, 0.79761, 0.47908, 0.81433, 0.39375, 0.81517, 0.31608, 0.80932, 0.1673, 0.78925, 0.10604, 0.79009, 0.05462, 0.80764, 0.07541, 0.7232, 0.14323, 0.70565, 0.23513, 0.70732, 0.10385, 0.6605, 0.05681, 0.69311, 0.22835, 0.66811, 0.15855, 0.68725, 0.11041, 0.69562, 0.76789, 0.63709, 0.71538, 0.68893, 0.65302, 0.73073, 0.59067, 0.75915, 0.48565, 0.76751, 0.37734, 0.77337, 0.27232, 0.74996, 0.19465, 0.74243, 0.12135, 0.74661, 0.05134, 0.75915, 0.75148, 0.73073, 0.70335, 0.78591, 0.62458, 0.84025, 0.54144, 0.87118, 0.45173, 0.87787, 0.38172, 0.86784, 0.31936, 0.84777], "triangles": [63, 109, 108, 63, 64, 109, 64, 110, 109, 110, 64, 66, 108, 109, 1, 110, 66, 111, 66, 64, 65, 109, 0, 1, 109, 110, 0, 66, 67, 111, 111, 69, 110, 110, 70, 0, 110, 69, 70, 111, 67, 69, 67, 68, 69, 61, 107, 106, 107, 2, 106, 61, 62, 107, 62, 108, 107, 62, 63, 108, 107, 108, 2, 108, 1, 2, 160, 168, 167, 167, 59, 98, 103, 59, 104, 167, 168, 58, 168, 57, 58, 167, 58, 59, 59, 105, 104, 105, 5, 104, 6, 104, 5, 59, 60, 105, 60, 106, 105, 106, 4, 105, 105, 4, 5, 60, 61, 106, 106, 3, 4, 106, 2, 3, 123, 186, 185, 186, 116, 185, 186, 117, 116, 185, 115, 184, 185, 116, 115, 184, 115, 114, 114, 115, 96, 101, 96, 97, 116, 97, 115, 96, 115, 97, 117, 160, 116, 116, 159, 97, 116, 160, 159, 159, 98, 97, 97, 102, 101, 97, 98, 102, 101, 102, 8, 160, 167, 159, 159, 167, 98, 98, 103, 102, 102, 7, 8, 102, 103, 7, 103, 98, 59, 103, 6, 7, 103, 104, 6, 125, 184, 183, 183, 113, 182, 184, 114, 183, 183, 114, 113, 113, 114, 95, 114, 96, 95, 113, 89, 112, 113, 95, 89, 112, 89, 90, 90, 89, 83, 83, 89, 11, 96, 100, 95, 95, 99, 89, 95, 100, 99, 96, 101, 100, 89, 99, 11, 100, 10, 99, 100, 9, 10, 100, 101, 9, 99, 10, 11, 101, 8, 9, 90, 83, 84, 78, 84, 77, 11, 12, 83, 84, 83, 77, 83, 12, 77, 78, 77, 72, 12, 13, 77, 77, 71, 72, 77, 13, 71, 72, 15, 16, 72, 71, 15, 15, 71, 14, 71, 13, 14, 147, 150, 146, 46, 148, 137, 137, 148, 147, 46, 47, 148, 150, 174, 151, 148, 149, 147, 147, 149, 150, 149, 148, 48, 149, 173, 150, 150, 173, 174, 148, 47, 48, 175, 174, 179, 174, 173, 179, 48, 166, 149, 149, 166, 173, 180, 179, 121, 48, 49, 166, 166, 172, 173, 173, 178, 179, 173, 172, 178, 49, 165, 166, 166, 165, 172, 187, 121, 120, 121, 179, 120, 187, 120, 186, 49, 50, 165, 179, 178, 120, 120, 117, 186, 178, 119, 120, 119, 118, 120, 120, 118, 117, 172, 165, 164, 164, 165, 51, 172, 171, 178, 172, 164, 171, 165, 50, 51, 171, 177, 178, 178, 177, 119, 119, 177, 118, 51, 52, 164, 164, 163, 171, 164, 52, 163, 171, 162, 177, 171, 163, 162, 177, 161, 118, 118, 160, 117, 118, 161, 160, 177, 162, 161, 52, 53, 163, 163, 170, 162, 163, 53, 170, 53, 54, 170, 162, 169, 161, 162, 170, 169, 161, 168, 160, 161, 169, 168, 54, 55, 170, 169, 55, 56, 169, 170, 55, 168, 169, 57, 169, 56, 57, 243, 231, 197, 233, 46, 137, 231, 133, 197, 230, 235, 231, 235, 234, 231, 231, 234, 133, 230, 236, 235, 233, 232, 236, 236, 232, 235, 232, 137, 136, 232, 233, 137, 235, 134, 234, 232, 135, 235, 235, 135, 134, 234, 144, 133, 133, 144, 143, 234, 134, 144, 232, 136, 135, 135, 145, 134, 134, 145, 144, 144, 153, 143, 145, 152, 144, 144, 152, 153, 135, 146, 145, 135, 136, 146, 146, 151, 145, 145, 151, 152, 137, 147, 136, 136, 147, 146, 152, 176, 153, 153, 176, 181, 151, 175, 152, 152, 175, 176, 146, 150, 151, 151, 174, 175, 175, 180, 176, 176, 180, 181, 180, 175, 179, 181, 180, 188, 180, 121, 188, 188, 121, 187, 243, 197, 196, 196, 204, 195, 195, 204, 205, 196, 132, 204, 196, 197, 132, 197, 133, 132, 204, 131, 205, 131, 203, 205, 204, 132, 131, 133, 143, 132, 132, 142, 131, 132, 143, 142, 131, 142, 141, 143, 154, 142, 143, 153, 154, 142, 155, 141, 142, 154, 155, 153, 181, 154, 181, 122, 154, 154, 123, 155, 154, 122, 123, 181, 188, 122, 188, 187, 122, 187, 186, 122, 123, 122, 186, 202, 203, 130, 203, 131, 130, 131, 141, 130, 202, 130, 129, 130, 140, 129, 130, 141, 140, 200, 129, 128, 141, 156, 140, 141, 155, 156, 129, 140, 139, 155, 124, 156, 155, 123, 124, 140, 156, 157, 156, 125, 157, 156, 124, 125, 124, 185, 184, 124, 123, 185, 125, 124, 184, 129, 139, 128, 140, 157, 139, 128, 138, 127, 128, 139, 138, 139, 158, 138, 139, 157, 158, 127, 138, 92, 125, 182, 157, 158, 182, 126, 158, 157, 182, 158, 112, 138, 138, 91, 92, 138, 112, 91, 125, 183, 182, 92, 91, 86, 158, 126, 112, 182, 113, 126, 126, 113, 112, 112, 90, 91, 91, 85, 86, 85, 90, 84, 85, 91, 90, 80, 85, 79, 85, 84, 79, 84, 78, 79, 79, 78, 73, 79, 73, 74, 73, 78, 72, 73, 72, 16, 41, 42, 40, 42, 43, 40, 43, 228, 40, 39, 228, 227, 39, 40, 228, 38, 39, 226, 43, 44, 228, 39, 227, 226, 44, 246, 228, 228, 246, 227, 226, 244, 217, 227, 245, 226, 227, 246, 245, 226, 245, 244, 44, 45, 246, 246, 229, 245, 246, 45, 229, 244, 231, 243, 245, 230, 244, 245, 229, 230, 45, 233, 229, 233, 45, 46, 244, 230, 231, 229, 236, 230, 229, 233, 236, 214, 34, 215, 34, 35, 215, 35, 36, 215, 215, 252, 214, 36, 216, 215, 36, 37, 216, 216, 253, 215, 215, 253, 252, 37, 38, 216, 252, 253, 224, 38, 217, 216, 216, 217, 253, 38, 226, 217, 217, 225, 253, 253, 225, 224, 217, 243, 225, 225, 243, 242, 217, 244, 243, 33, 213, 32, 32, 213, 31, 34, 214, 33, 33, 214, 213, 31, 212, 30, 31, 213, 212, 214, 251, 213, 213, 250, 212, 213, 251, 250, 214, 252, 251, 30, 212, 29, 250, 249, 212, 212, 249, 29, 252, 224, 251, 251, 223, 250, 251, 224, 223, 29, 249, 28, 250, 222, 249, 250, 223, 222, 249, 248, 28, 249, 222, 221, 225, 242, 224, 223, 224, 241, 223, 241, 222, 241, 224, 242, 222, 241, 240, 242, 243, 196, 242, 195, 241, 242, 196, 195, 241, 195, 194, 195, 205, 194, 249, 221, 248, 28, 248, 27, 222, 240, 221, 27, 248, 247, 248, 220, 247, 248, 221, 220, 27, 247, 26, 221, 239, 220, 221, 240, 239, 241, 194, 240, 240, 193, 239, 240, 194, 193, 194, 206, 193, 194, 205, 206, 247, 219, 26, 26, 219, 25, 220, 239, 238, 239, 193, 192, 247, 220, 219, 238, 239, 192, 219, 220, 238, 193, 206, 192, 205, 202, 206, 205, 203, 202, 206, 207, 192, 191, 192, 207, 191, 238, 192, 206, 201, 207, 206, 202, 201, 219, 238, 237, 237, 238, 191, 129, 201, 202, 191, 207, 208, 207, 201, 200, 201, 129, 200, 207, 200, 208, 25, 219, 218, 219, 237, 218, 211, 25, 218, 211, 24, 25, 190, 191, 208, 190, 208, 209, 191, 190, 237, 218, 237, 189, 211, 218, 189, 199, 208, 200, 208, 199, 209, 199, 200, 128, 189, 237, 190, 189, 209, 210, 189, 190, 209, 24, 211, 23, 211, 189, 23, 209, 198, 210, 209, 199, 198, 199, 127, 198, 199, 128, 127, 189, 94, 23, 189, 210, 94, 23, 94, 22, 210, 93, 94, 210, 198, 93, 127, 92, 198, 198, 92, 93, 94, 93, 88, 93, 92, 87, 94, 88, 22, 93, 87, 88, 22, 82, 21, 22, 88, 82, 92, 86, 87, 82, 87, 81, 82, 88, 87, 87, 86, 81, 82, 76, 21, 21, 76, 20, 86, 85, 80, 86, 80, 81, 82, 81, 76, 76, 81, 75, 81, 80, 75, 20, 76, 19, 80, 74, 75, 80, 79, 74, 76, 75, 19, 19, 75, 18, 18, 75, 74, 74, 73, 17, 74, 17, 18, 73, 16, 17], "vertices": [1, 37, 10.06, -37.6, 1, 2, 36, 75.3, -30.99, 0.29714, 37, -18.91, -23.82, 0.70286, 3, 35, 122.71, -7.14, 0.11264, 36, 27.22, -25.45, 0.875, 37, -62.62, -3.04, 0.01235, 2, 35, 88.73, -18.56, 0.89862, 36, -8.39, -21.36, 0.10138, 1, 35, 65.8, -19.55, 1, 1, 35, 25.56, -21.28, 1, 2, 34, 87.41, -23.18, 0.96802, 35, -23.39, -23.39, 0.03198, 2, 33, 120.44, -56.52, 0.01633, 34, 50.48, -24.9, 0.98367, 6, 40, -6.33, -183.81, 0, 41, -78.9, -198.12, 0, 42, -73.19, -251.8, 0, 43, 88.93, -285.64, 0, 33, 103.71, -36.97, 0.28943, 34, 24.78, -26.1, 0.71057, 2, 33, 69.26, -28.78, 0.93458, 34, -2.86, -48.24, 0.06542, 2, 32, 86.77, -45.88, 0.00648, 33, 37.69, -21.27, 0.99352, 2, 32, 81.04, -27.43, 0.24215, 33, 18.91, -16.8, 0.75785, 2, 32, 38.75, -31.84, 0.99109, 33, 1.15, -55.43, 0.00891, 1, 32, 0.68, -35.81, 1, 2, 38, -79.71, -74.28, 0.00674, 32, -55.45, -41.66, 0.99326, 3, 26, -43.08, -77.48, 0.05893, 38, -71.59, -34.64, 0.09524, 32, -52.69, -1.29, 0.84583, 3, 26, -28.76, -29.68, 0.51523, 38, -61.57, 14.24, 0.24735, 32, -49.29, 48.49, 0.23742, 3, 26, -19.04, 2.78, 0.96576, 38, -54.76, 47.44, 0.02203, 32, -46.98, 82.3, 0.01222, 1, 26, -6.43, 44.89, 1, 1, 26, 4.96, 49.51, 1, 1, 26, 41.12, 55.25, 1, 2, 26, 74.07, 53.58, 0.9902, 27, -65.74, 50.07, 0.0098, 2, 26, 121.62, 51.18, 0.63087, 27, -18.19, 52.58, 0.36913, 2, 26, 168.73, 48.8, 0.10222, 27, 28.91, 55.07, 0.89778, 3, 26, 205.96, 46.91, 0.00385, 27, 66.13, 57.03, 0.99577, 28, -80.31, 45.92, 0.00039, 2, 27, 118.39, 59.79, 0.71158, 28, -29.95, 60.15, 0.28842, 3, 27, 173.89, 62.72, 0.12733, 28, 23.53, 75.28, 0.8309, 29, -126.61, 15.72, 0.04178, 3, 27, 206.42, 64.44, 0.02282, 28, 54.88, 84.14, 0.81295, 29, -102.7, 37.85, 0.16423, 2, 28, 93.07, 94.94, 0.54708, 29, -73.57, 64.81, 0.45292, 3, 28, 134.25, 106.59, 0.22399, 29, -42.17, 93.88, 0.77581, 30, -191.19, -55.4, 0.0002, 3, 28, 172.45, 107.06, 0.05736, 29, -8.34, 111.64, 0.93191, 30, -179.63, -18.98, 0.01073, 3, 28, 212.81, 107.56, 0.00339, 29, 27.39, 130.4, 0.94174, 30, -167.42, 19.49, 0.05487, 2, 29, 65.7, 150.52, 0.88032, 30, -154.32, 60.73, 0.11968, 2, 29, 101.76, 135.79, 0.79314, 30, -118.33, 75.61, 0.20686, 2, 29, 145.49, 102.44, 0.4938, 30, -63.78, 82.65, 0.5062, 2, 29, 186.72, 71, 0.14863, 30, -12.36, 89.28, 0.85137, 2, 29, 200.26, 35.78, 0.02267, 30, 22.03, 73.77, 0.97733, 2, 30, 55.81, 58.53, 0.99529, 31, -24.99, 69.65, 0.00471, 2, 30, 92.09, 42.17, 0.72445, 31, 6.11, 44.82, 0.27555, 2, 30, 115.76, 31.49, 0.16988, 31, 26.41, 28.62, 0.83012, 2, 30, 130.78, 81.39, 0.00077, 31, 53.31, 73.24, 0.99923, 1, 31, 68.94, 118.15, 1, 1, 31, 84.11, 92.48, 1, 1, 31, 91.61, 48.58, 1, 2, 31, 97.79, 12.44, 0.99329, 42, 123.41, 147.19, 0.00671, 2, 31, 103.37, -20.19, 0.80783, 42, 131.33, 115.05, 0.19217, 3, 31, 121.55, -126.56, 0.0579, 42, 157.13, 10.26, 0.40303, 43, 16.51, 55.65, 0.53906, 3, 31, 125.82, -151.56, 0.00999, 42, 163.19, -14.36, 0.10765, 43, 39.87, 45.79, 0.88236, 3, 31, 129.57, -173.47, 0.00056, 42, 168.51, -35.95, 0.01341, 43, 60.36, 37.15, 0.98603, 1, 43, 78.49, 29.5, 1, 1, 43, 107.18, 17.4, 1, 3, 43, 130.22, 6.92, 0.98956, 34, 112.54, 256.03, 0.00162, 35, 2.71, 255.73, 0.00882, 4, 41, 208.52, -219.77, 1e-05, 43, 158.51, -5.93, 0.93476, 34, 138.39, 238.8, 0.013, 35, 28.5, 238.41, 0.05224, 3, 43, 174.67, -13.28, 0.90427, 34, 153.16, 228.95, 0.01952, 35, 43.23, 228.51, 0.07621, 3, 43, 193.28, -21.74, 0.88271, 34, 170.18, 217.61, 0.02496, 35, 60.21, 217.11, 0.09233, 4, 41, 158.94, -261.19, 0.00088, 43, 191.01, -61.76, 0.79043, 34, 161.51, 178.48, 0.05963, 35, 51.41, 178.01, 0.14906, 6, 40, 183.88, -278.18, 0, 41, 122.53, -265.28, 0.00348, 42, 136.75, -220.02, 0.0014, 43, 188.94, -98.34, 0.62741, 34, 153.58, 142.71, 0.12082, 35, 43.36, 142.27, 0.24688, 6, 40, 140.01, -277, 2e-05, 41, 78.92, -270.18, 0.00542, 42, 100.14, -244.21, 0.00282, 43, 186.45, -142.15, 0.40238, 34, 144.09, 99.86, 0.1883, 35, 33.71, 99.45, 0.40105, 5, 41, 27.8, -275.93, 0.00313, 42, 57.22, -272.58, 0.00146, 43, 183.54, -193.51, 0.15599, 34, 132.96, 49.64, 0.17417, 35, 22.41, 49.27, 0.66525, 6, 41, -3.26, -279.42, 0.00048, 42, 31.14, -289.81, 0.00018, 43, 181.77, -224.72, 0.02554, 34, 126.2, 19.12, 0.01157, 35, 15.55, 18.77, 0.9618, 36, -58.78, 43.54, 0.00044, 2, 35, 66.17, 26.69, 0.67455, 36, -9.58, 29.19, 0.32545, 1, 36, 32.9, 16.8, 1, 1, 36, 56.43, 22.3, 1, 2, 36, 93.65, 31.01, 0.47946, 37, 18.51, 28.91, 0.52054, 2, 36, 126.1, 38.6, 0.05357, 37, 51.67, 25.6, 0.94643, 2, 36, 152.49, 44.78, 0.00357, 37, 78.64, 22.9, 0.99643, 1, 37, 86.81, 2.5, 1, 1, 37, 95.7, -19.7, 1, 1, 37, 100.04, -39.97, 1, 1, 37, 76.96, -44.98, 1, 1, 37, 41.69, -52.64, 1, 2, 26, 2.19, -93.48, 0.0015, 32, -5.01, -6.89, 0.9985, 3, 26, 18.63, -65.3, 0.00584, 38, -11.21, -17.04, 0.50929, 32, 4.8, 24.22, 0.48487, 3, 26, 21.76, -33.76, 0.34413, 38, -10.89, 14.65, 0.62563, 32, 0.88, 55.67, 0.03025, 2, 26, 30.65, -4.66, 0.9383, 38, -4.61, 44.43, 0.0617, 1, 26, 41.09, 18.69, 1, 1, 26, 42.09, 35.82, 1, 1, 32, 29.34, -4.56, 1, 2, 38, 13.88, -17.43, 0.62453, 32, 29.71, 27.18, 0.37547, 2, 26, 53.03, -40.18, 0.1482, 38, 20.84, 11.03, 0.8518, 2, 26, 61.5, -8.07, 0.88097, 38, 26.43, 43.77, 0.11903, 1, 26, 72.62, 15.56, 1, 2, 26, 76.49, 35.45, 0.99037, 27, -61.47, 32.29, 0.00963, 1, 32, 69.24, -3.74, 1, 4, 38, 51.25, -23.96, 0.52356, 39, -49.31, -32.66, 0.00246, 32, 67.62, 25.7, 0.42277, 33, -33.65, -1.27, 0.05121, 3, 26, 87.22, -51.79, 0.01766, 27, -41.8, -53.38, 0.00015, 38, 55.92, 2.5, 0.98219, 3, 26, 98.83, -17.61, 0.68812, 27, -33.78, -18.19, 0.00132, 38, 64.45, 37.58, 0.31056, 2, 26, 108.02, 6.83, 0.99842, 27, -27.15, 7.07, 0.00158, 2, 26, 114.76, 29.48, 0.73334, 27, -22.78, 30.29, 0.26666, 4, 38, 83.11, -69.81, 0.00382, 39, -6.23, -68.19, 0.06223, 40, -58.7, -104.31, 0.00085, 33, 21, 10.17, 0.9331, 4, 38, 88.99, -34.44, 0.19284, 39, -10.14, -32.55, 0.31862, 32, 106.42, 20.35, 0.02379, 33, -9.27, 29.39, 0.46475, 5, 27, 0.64, -60.9, 0.00084, 38, 98.25, -5.63, 0.38473, 39, -9.02, -2.3, 0.58545, 32, 111.75, 50.14, 0.00169, 33, -32.19, 49.16, 0.02729, 4, 26, 148.91, -29.14, 0.0102, 27, 17.22, -24.49, 0.60867, 38, 115.36, 30.53, 0.09959, 39, -2.34, 37.14, 0.28153, 2, 26, 150.24, 4.86, 0.04531, 27, 15.05, 9.46, 0.95469, 2, 26, 156.42, 28.87, 0.13987, 27, 18.72, 33.98, 0.86013, 7, 39, 29.55, -96.04, 0.08472, 40, -14.61, -114.9, 0.02833, 41, -96.62, -131.01, 0.00107, 42, -119.49, -200.09, 0.00015, 43, 19.8, -291.85, 0.0001, 33, 65.1, 20.71, 0.88469, 34, -44.45, -21.12, 0.00093, 7, 39, 59.15, -129.45, 0.02916, 40, 26.14, -133.11, 0.05817, 41, -53.74, -143.41, 0.01598, 42, -75.66, -191.64, 0.00479, 43, 39.22, -251.66, 0.00919, 33, 109.69, 22.94, 0.44336, 34, -18.83, 15.43, 0.43936, 7, 39, 65.77, -171.31, 0.00149, 40, 49.46, -168.49, 0.0226, 41, -25.76, -175.23, 0.01804, 42, -36.27, -207.25, 0.00899, 43, 75.28, -229.41, 0.036, 34, 20.34, 31.59, 0.90348, 35, -90.27, 31.61, 0.00939, 6, 40, 56.93, -213.17, 0.00388, 41, -12.19, -218.45, 0.0068, 42, -4.53, -239.57, 0.00434, 43, 120.16, -223.29, 0.05043, 34, 65.63, 30.43, 0.89274, 35, -44.99, 30.29, 0.04182, 2, 32, 95.31, -31.88, 0.01872, 33, 30.01, -6.79, 0.98128, 5, 40, -19.64, -136.57, 6e-05, 41, -98.6, -153.17, 1e-05, 42, -111.18, -220.73, 0, 43, 41.31, -297.52, 0, 33, 70.44, -0.89, 0.99993, 2, 33, 105.25, -8.85, 0.43867, 34, 3.53, -7.6, 0.56133, 6, 40, 23.07, -190.89, 0.0001, 41, -48.8, -201.06, 0.00016, 42, -45.05, -240.74, 0.0001, 43, 96.88, -256.47, 0.00074, 34, 37.31, 1.42, 0.9988, 35, -73.4, 1.39, 9e-05, 5, 40, 33.37, -228.79, 0.00011, 41, -33.35, -237.18, 0.00032, 42, -14.87, -265.87, 0.0002, 43, 135.07, -247.3, 0.00395, 34, 76.48, 4.34, 0.99543, 2, 34, 108.08, -2.22, 0.32737, 35, -2.65, -2.5, 0.67263, 1, 35, 37.55, -1.03, 1, 2, 35, 79.5, 3.04, 0.88091, 36, -7.57, 2.13, 0.11908, 3, 35, 118.86, 16.45, 0.00478, 36, 33.76, -2.47, 0.99445, 37, -48.99, 16.6, 0.00077, 3, 35, 151.81, 29.73, 6e-05, 36, 69.23, -4.45, 0.95728, 37, -16.07, 3.25, 0.04266, 1, 37, 16.35, -10.2, 1, 1, 37, 47.22, -16.47, 1, 1, 37, 77.51, -17.5, 1, 4, 38, 112.71, -39.49, 0.02286, 39, 14.06, -30.99, 0.61645, 40, -55.59, -62.05, 0.00075, 33, 4.63, 49.25, 0.35994, 7, 39, 48.74, -67.56, 0.32895, 40, -8.9, -81.04, 0.10004, 41, -95.65, -96.69, 0.00205, 42, -134.23, -169.08, 0.00032, 43, -13.88, -285.13, 0.00032, 33, 54.85, 53.48, 0.55302, 34, -76.61, -9.07, 0.01529, 8, 39, 72.45, -119.33, 0.06046, 40, 34.08, -118.4, 0.12019, 41, -47.91, -127.75, 0.03066, 42, -77.6, -175.03, 0.00849, 43, 24.75, -243.29, 0.01433, 33, 110.1, 39.65, 0.39643, 34, -31.76, 26.02, 0.36926, 35, -142.39, 26.22, 0.0002, 8, 39, 79.71, -154.15, 0.01097, 40, 55.07, -147.11, 0.06208, 41, -23.16, -153.28, 0.03799, 42, -43.94, -186.52, 0.0163, 43, 54.08, -223.17, 0.04388, 33, 141.81, 23.57, 0.06453, 34, 0.42, 41.16, 0.75603, 35, -110.16, 41.25, 0.00822, 8, 39, 105.01, -192.98, 0.00076, 40, 94.15, -172.02, 0.03855, 41, 18.99, -172.55, 0.04989, 42, 2.37, -184.51, 0.03327, 43, 80.15, -184.85, 0.15364, 33, 187.94, 19.07, 0.00366, 34, 32.31, 74.8, 0.64783, 35, -78.15, 74.78, 0.0724, 6, 40, 137.14, -184.91, 0.02224, 41, 63.35, -179.37, 0.04702, 42, 44.97, -170.42, 0.0473, 43, 94.31, -142.27, 0.34157, 34, 53.13, 114.55, 0.39829, 35, -57.19, 114.46, 0.14357, 6, 40, 169.36, -188.16, 0.01294, 41, 95.71, -178.14, 0.03642, 42, 73.23, -154.6, 0.04965, 43, 98.53, -110.16, 0.51243, 34, 62.45, 145.56, 0.25278, 35, -47.77, 145.44, 0.13578, 6, 40, 191.51, -174.67, 0.01082, 41, 115.79, -161.72, 0.0355, 42, 83.65, -130.85, 0.06533, 43, 85.71, -87.61, 0.62125, 34, 53.42, 169.88, 0.17913, 35, -56.71, 169.79, 0.08797, 7, 40, 189.49, -137.39, 0.01982, 41, 108.62, -125.08, 0.07206, 42, 60.61, -101.47, 0.17268, 43, 48.39, -88.52, 0.52043, 33, 257.24, 93.14, 6e-05, 34, 16.43, 174.98, 0.17163, 35, -93.68, 175.01, 0.04332, 7, 40, 179.38, -99.01, 0.03239, 41, 93.31, -88.46, 0.16088, 42, 30.31, -75.82, 0.34719, 43, 9.72, -97.47, 0.29801, 33, 230.84, 122.78, 0.00161, 34, -23.17, 172.36, 0.14197, 35, -133.29, 172.53, 0.01794, 8, 39, 198.5, -116.2, 0.0009, 40, 147.59, -63.49, 0.07636, 41, 56.91, -57.67, 0.46472, 42, -16.1, -64.96, 0.23592, 43, -26.74, -128.19, 0.08912, 33, 186.4, 140.03, 0.00941, 34, -64.09, 147.89, 0.11785, 35, -174.29, 148.2, 0.00572, 8, 39, 159.62, -72.78, 0.01517, 40, 94.24, -40.01, 0.41549, 41, 0.83, -41.79, 0.40479, 42, -73.27, -76.31, 0.0261, 43, -51.81, -180.81, 0.01767, 33, 128.21, 136.8, 0.03717, 34, -97.29, 99.99, 0.08301, 35, -207.66, 100.41, 0.00061, 7, 39, 124.85, -58.49, 0.08293, 40, 56.68, -41.35, 0.67335, 41, -36.18, -48.31, 0.06251, 42, -103.27, -98.95, 0.00692, 43, -51.59, -218.39, 0.00694, 33, 95.33, 118.58, 0.09533, 34, -103.11, 62.86, 0.07202, 7, 39, 84.93, -53.3, 0.33223, 40, 18.17, -53.11, 0.37284, 41, -72.7, -65.28, 0.00831, 42, -128.07, -130.67, 0.00146, 43, -40.99, -257.23, 0.00179, 33, 66.33, 90.65, 0.23873, 34, -98.89, 22.82, 0.04464, 7, 39, 50.88, -49.81, 0.50531, 40, -14.28, -63.99, 0.09565, 41, -103.33, -80.55, 0.0004, 42, -148.42, -158.19, 5e-05, 43, -31.08, -289.99, 3e-05, 33, 42.33, 66.25, 0.3898, 34, -94.37, -11.11, 0.00877, 3, 27, 50.62, -30.72, 0.57463, 39, 31.53, 39.69, 0.42041, 40, -68.86, 9.54, 0.00496, 4, 27, 90.93, -46.14, 0.31419, 28, -33.32, -49.23, 0.08652, 39, 74.46, 35.13, 0.33643, 40, -27.88, 23.11, 0.26286, 4, 27, 132.6, -68.58, 0.01615, 28, 12.27, -61.9, 0.26526, 40, 18.58, 32.11, 0.71773, 41, -84.08, 19.17, 0.00087, 4, 28, 50.28, -86.67, 0.17287, 29, -29.26, -116.44, 0.00429, 40, 63.64, 26.89, 0.74736, 41, -38.73, 20.23, 0.07549, 6, 28, 101.47, -104.22, 0.11351, 29, 24.32, -108.84, 0.0797, 30, -1.37, -152.78, 0.0027, 31, -132.68, -120.94, 1e-05, 40, 117.28, 34.02, 0.06353, 41, 13.42, 34.71, 0.74055, 6, 28, 146.08, -130.22, 0.02581, 29, 75.87, -111.76, 0.11962, 30, 37.33, -118.6, 0.0475, 31, -86.73, -97.4, 0.01813, 41, 64.91, 38.49, 0.75985, 42, -52.7, 24.33, 0.0291, 6, 28, 181.5, -169.69, 0.00075, 29, 125.35, -130.84, 0.03458, 30, 85.94, -97.38, 0.06988, 31, -34.38, -88.86, 0.12723, 41, 116.45, 25.98, 0.17724, 42, -1.11, 36.62, 0.59032, 4, 29, 162.76, -161.44, 0.00152, 30, 134.05, -92.83, 0.00731, 31, 13.36, -96.36, 0.17985, 42, 47.04, 32.58, 0.81132, 2, 31, 43.28, -109.06, 0.10307, 42, 77.8, 22.08, 0.89693, 2, 31, 70.75, -123.17, 0.04219, 42, 106.22, 9.99, 0.95781, 3, 31, 101.27, -128.88, 0.05151, 42, 137.07, 6.49, 0.43065, 43, 7.54, 37.31, 0.51784, 2, 27, 45.36, -63.41, 0.05345, 39, 34.85, 6.75, 0.94655, 3, 39, 78.48, -2.04, 0.99548, 33, 22.28, 117.64, 0.00429, 34, -147.24, 4.64, 0.00023, 3, 28, 18.82, -98.27, 0.00913, 40, 40.66, 2.48, 0.99051, 41, -58.11, -7.13, 0.00036, 4, 28, 60.54, -111.69, 0.02981, 29, -8.76, -134.07, 0.00308, 40, 83.98, 9.07, 0.81699, 41, -16.12, 5.4, 0.15012, 5, 28, 105.32, -138.37, 0.00943, 29, 43.25, -137.52, 0.01457, 30, 32.26, -159.86, 0.00172, 31, -101.85, -136.12, 0.00017, 41, 35.9, 8.72, 0.97411, 6, 28, 144.82, -169.61, 0.00068, 29, 92.63, -147.42, 0.009, 30, 74.33, -132.18, 0.00851, 31, -54.24, -119.71, 0.00862, 41, 86.15, 5.3, 0.73616, 42, -18.7, 4.42, 0.23703, 4, 29, 127.23, -164.98, 0.00201, 30, 111.28, -120.34, 0.00571, 31, -15.51, -117.38, 0.0255, 42, 19.77, 9.54, 0.96679, 3, 30, 147.66, -120.39, 9e-05, 31, 19.73, -126.44, 0.00751, 42, 55.56, 3.05, 0.99241, 6, 40, 263.69, -68.96, 1e-05, 41, 172.66, -47.04, 5e-05, 42, 82.15, -2.85, 0.95556, 43, -17.8, -12.3, 0.04399, 34, -36.64, 260.84, 0.00034, 35, -146.46, 261.06, 4e-05, 3, 31, 80.7, -145.27, 0.00686, 42, 117.73, -11.34, 0.08448, 43, 10.28, 11.16, 0.90866, 3, 31, 108.13, -149.03, 0.01202, 42, 145.37, -13.11, 0.1212, 43, 28.22, 32.25, 0.86678, 3, 31, 92.62, -165.98, 0.00077, 42, 131.12, -31.13, 0.00693, 43, 34.15, 10.05, 0.9923, 6, 40, 270.02, -99.79, 0.0001, 41, 183.19, -76.7, 0.00046, 42, 105.02, -24.48, 0.01081, 43, 13.21, -6.89, 0.9865, 34, -5.17, 261.19, 0.00182, 35, -114.98, 261.3, 0.00031, 6, 40, 243.65, -70.87, 0.00055, 41, 153.07, -51.7, 0.00306, 42, 66.82, -15.92, 0.81145, 43, -16.48, -32.39, 0.17704, 34, -38.58, 240.8, 0.00702, 35, -148.46, 241.02, 0.00088, 6, 40, 213.06, -47.06, 0.00173, 41, 119.49, -32.35, 0.02184, 42, 28.12, -13.95, 0.93316, 43, -41.2, -62.25, 0.03305, 34, -67.76, 215.3, 0.00947, 35, -177.74, 215.62, 0.00076, 7, 40, 185.49, -34.14, 0.00509, 41, 90.39, -23.36, 0.29664, 42, -1.89, -19.18, 0.66007, 43, -54.95, -89.43, 0.02133, 33, 206.88, 183.37, 0.00016, 34, -85.7, 190.68, 0.01591, 35, -195.76, 191.07, 0.00079, 8, 39, 208.2, -72.8, 2e-05, 40, 138.5, -19.95, 0.02263, 41, 41.89, -15.81, 0.91567, 42, -48.52, -34.5, 0.03169, 43, -70.53, -135.97, 0.00886, 33, 158.57, 174.73, 0.0022, 34, -108.56, 147.25, 0.01857, 35, -218.77, 147.71, 0.00036, 8, 39, 166.99, -47.04, 0.00331, 40, 90.33, -13.52, 0.59613, 41, -6.71, -16.1, 0.37014, 42, -91.67, -56.86, 0.00366, 43, -78.4, -183.93, 0.00268, 33, 112.71, 158.63, 0.00829, 34, -124.03, 101.18, 0.01578, 35, -234.4, 101.7, 1e-05, 7, 39, 124.38, -34.7, 0.05914, 40, 46.42, -19.87, 0.87886, 41, -49.31, -28.46, 0.00167, 42, -123.99, -87.24, 0.00088, 43, -73.36, -228, 0.0011, 33, 76.45, 133.08, 0.03953, 34, -126.14, 56.87, 0.01882, 6, 39, 85.12, -28.76, 0.45471, 40, 8.22, -30.67, 0.42481, 42, -149.1, -118, 4e-05, 43, -63.71, -266.52, 0.00011, 33, 47.29, 106.13, 0.10856, 34, -122.8, 17.3, 0.01177, 4, 39, 52.53, -24.41, 0.80886, 40, -23.26, -40.17, 0.03308, 33, 23.53, 83.4, 0.15617, 34, -119.46, -15.4, 0.00189, 6, 40, 80.47, -192.92, 0.01718, 41, 8.34, -195.14, 0.02421, 42, 3.15, -209.48, 0.01611, 43, 100.63, -199.14, 0.11744, 34, 50.22, 57.4, 0.73533, 35, -60.3, 57.31, 0.08973, 6, 40, 124.07, -220.12, 0.00752, 41, 55.27, -216.05, 0.01918, 42, 54.46, -206.76, 0.01654, 43, 129.12, -156.38, 0.30561, 34, 85.22, 95.03, 0.37721, 35, -25.17, 94.82, 0.27395, 6, 40, 169.34, -238.4, 0.00205, 41, 102.64, -227.9, 0.00989, 42, 102.03, -195.77, 0.00923, 43, 148.74, -111.68, 0.54573, 34, 111.77, 136, 0.18163, 35, 1.52, 135.7, 0.25147, 6, 40, 199.86, -244.31, 0.00064, 41, 133.68, -229.53, 0.00491, 42, 130.42, -183.11, 0.00414, 43, 155.57, -81.35, 0.70278, 34, 123.37, 164.83, 0.10614, 35, 13.22, 164.5, 0.1814, 6, 40, 241.07, -244.39, 5e-05, 41, 174.5, -223.91, 0.0009, 42, 164.22, -159.54, 0.00042, 43, 156.88, -40.16, 0.87016, 34, 131.29, 205.28, 0.03685, 35, 21.27, 204.92, 0.09161, 6, 40, 263.16, -217.9, 5e-05, 41, 192.73, -194.62, 0.00059, 42, 167.13, -125.17, 0.00037, 43, 131.06, -17.28, 0.95494, 34, 109.48, 232.01, 0.01341, 35, -0.44, 231.72, 0.03064, 1, 43, 95.38, 3.81, 1, 1, 43, 56.08, 7.74, 1, 6, 40, 99.07, -237.14, 0.00291, 41, 32.86, -236.37, 0.00943, 42, 43.74, -235.04, 0.00667, 43, 145.39, -181.88, 0.19802, 34, 97.17, 67.24, 0.39679, 35, -13.31, 67, 0.38618, 6, 40, 141.57, -252.2, 0.00115, 41, 77.04, -245.4, 0.00803, 42, 87.19, -223, 0.00575, 43, 161.7, -139.85, 0.40505, 34, 120.03, 106.1, 0.21774, 35, 9.68, 105.78, 0.36229, 6, 40, 183.44, -262.18, 0.00023, 41, 119.89, -249.5, 0.00431, 42, 127.21, -207.16, 0.00246, 43, 172.93, -98.3, 0.62344, 34, 137.79, 145.32, 0.12678, 35, 27.57, 144.93, 0.24278, 6, 40, 224.16, -263.43, 3e-05, 41, 160.39, -245.1, 0.00106, 42, 161.29, -184.83, 0.00031, 43, 175.4, -57.63, 0.80472, 34, 146.76, 185.06, 0.05645, 35, 36.68, 184.64, 0.13744, 6, 40, 236.21, -210.05, 0.00079, 41, 164.94, -190.57, 0.0044, 42, 140.55, -134.19, 0.00592, 43, 122.4, -43.99, 0.87424, 34, 96.65, 207.04, 0.05009, 35, -13.36, 206.79, 0.06456, 6, 40, 262.81, -174.45, 0.00034, 41, 186.36, -151.63, 0.00182, 42, 141.92, -89.77, 0.00352, 43, 87.62, -16.34, 0.97415, 34, 66.75, 239.92, 0.01213, 35, -43.14, 239.78, 0.00804, 6, 40, 260.94, -129.67, 0.00061, 41, 178.33, -107.55, 0.00309, 42, 114.72, -54.17, 0.01761, 43, 42.81, -16.86, 0.96395, 34, 22.44, 246.6, 0.01145, 35, -87.43, 246.61, 0.0033, 6, 40, 248.77, -96.39, 0.00109, 41, 161.67, -76.27, 0.00577, 42, 85.65, -33.88, 0.31576, 43, 9.17, -28.04, 0.66014, 34, -12.55, 240.97, 0.01477, 35, -122.44, 241.11, 0.00247, 6, 40, 228.03, -75.33, 0.00251, 41, 138.22, -58.28, 0.0181, 42, 56.59, -28.52, 0.7153, 43, -12.5, -48.14, 0.23996, 34, -37.17, 224.62, 0.02141, 35, -147.11, 224.84, 0.00271, 7, 40, 197.79, -52.35, 0.00685, 41, 105.09, -39.7, 0.11385, 42, 18.64, -27.04, 0.78204, 43, -36.37, -77.68, 0.06713, 33, 226.11, 172.71, 0.00011, 34, -65.48, 199.3, 0.02794, 35, -175.51, 199.61, 0.00208, 6, 40, 199.5, -200.86, 0.00471, 41, 127.32, -186.54, 0.01741, 42, 105.21, -147.71, 0.02696, 43, 112.12, -80.41, 0.69171, 34, 80.64, 172.75, 0.14127, 35, -29.48, 172.56, 0.11794, 6, 40, 211.97, -157.96, 0.00783, 41, 133.74, -142.34, 0.03035, 42, 90.82, -105.42, 0.08379, 43, 69.62, -66.66, 0.7167, 34, 40.9, 193.14, 0.11444, 35, -69.15, 193.09, 0.04689, 6, 40, 205.26, -121.47, 0.01322, 41, 122.05, -107.12, 0.05976, 42, 64.4, -79.38, 0.24682, 43, 32.94, -72.27, 0.54619, 34, 3.8, 193.5, 0.10993, 35, -106.25, 193.57, 0.02408, 7, 40, 198.84, -89.22, 0.0153, 41, 111.23, -76.07, 0.10043, 42, 40.64, -56.64, 0.49122, 43, 0.52, -77.73, 0.30076, 33, 243.75, 140.32, 0.00022, 34, -29.08, 193.32, 0.08155, 35, -139.13, 193.51, 0.01052, 7, 40, 170.08, -54.49, 0.02777, 41, 77.95, -45.65, 0.39377, 42, -2.83, -44.68, 0.43321, 43, -35.06, -105.43, 0.0757, 33, 202.38, 158.25, 0.0022, 34, -68.64, 171.69, 0.06369, 35, -178.77, 172.02, 0.00366, 7, 39, 62.66, -51.02, 0.47124, 40, -3.05, -60.22, 0.16818, 41, -92.73, -75.26, 0.00164, 42, -141.38, -148.67, 0.00028, 43, -34.51, -278.66, 0.00032, 33, 50.64, 74.69, 0.33989, 34, -95.94, 0.63, 0.01845, 7, 39, 66.4, -73.82, 0.27845, 40, 9.78, -79.45, 0.18769, 41, -77.37, -92.53, 0.00979, 42, -119.84, -157.06, 0.00182, 43, -14.91, -266.41, 0.00226, 33, 70.78, 63.36, 0.46299, 34, -74.62, 9.57, 0.05701, 8, 39, 86.55, -111.36, 0.08181, 40, 43.63, -105.31, 0.19089, 41, -40.27, -113.47, 0.0505, 42, -77.28, -158.84, 0.01304, 43, 11.95, -233.36, 0.0199, 33, 112.67, 55.64, 0.31355, 34, -42.8, 37.88, 0.32962, 35, -153.38, 38.12, 0.00068, 8, 39, 122.35, -137.8, 0.02062, 40, 87.16, -114.61, 0.16248, 41, 4.13, -116.66, 0.13578, 42, -36.29, -141.49, 0.05346, 43, 22.55, -190.12, 0.08565, 33, 155.7, 67.08, 0.0818, 34, -25.39, 78.85, 0.44722, 35, -135.83, 79.03, 0.01299, 8, 39, 163.54, -152.34, 0.00317, 40, 130.67, -110.84, 0.10105, 41, 46.7, -106.91, 0.20802, 42, -2.81, -113.45, 0.14307, 43, 20.09, -146.52, 0.18462, 33, 192.77, 90.15, 0.01897, 34, -20.82, 122.28, 0.31596, 35, -131.11, 122.44, 0.02513, 8, 39, 192.91, -146.7, 0.00063, 40, 155.09, -93.58, 0.06636, 41, 68.5, -86.43, 0.25459, 42, 7.29, -85.3, 0.26202, 43, 3.56, -121.59, 0.20063, 33, 206.72, 116.61, 0.00775, 34, -33.12, 149.54, 0.19118, 35, -143.33, 149.74, 0.01684, 8, 39, 205.7, -139.11, 0.00019, 40, 163.61, -81.39, 0.05098, 41, 75.25, -73.18, 0.28204, 42, 7.28, -70.43, 0.34, 43, -8.37, -112.71, 0.17207, 33, 208.79, 131.34, 0.0049, 34, -43.47, 160.22, 0.13858, 35, -153.64, 160.46, 0.01124, 2, 26, 189.17, 13.87, 0.00544, 27, 52.84, 22.43, 0.99456, 1, 27, 94.26, 10.72, 1, 1, 28, 4.95, 6.14, 1, 3, 27, 180.13, -8.96, 0.00023, 28, 45.45, 6.75, 0.99734, 29, -75.96, -35.39, 0.00243, 3, 28, 83.09, -0.97, 0.99784, 40, 54.78, 118.23, 0.00146, 41, -60.13, 109.47, 0.0007, 4, 28, 125.69, -32.89, 0.4309, 29, 13.52, -34.28, 0.42671, 40, 107.14, 108.66, 0.02365, 41, -6.95, 107.24, 0.11874, 7, 28, 155.02, -67.87, 0.12626, 29, 55.54, -52.14, 0.54114, 30, -19.05, -90.51, 0.02448, 31, -134.4, -56.23, 0.00254, 40, 149, 90.44, 0.00607, 41, 37.03, 94.98, 0.29817, 42, -103.22, 61.96, 0.00134, 6, 28, 175.65, -111.2, 0.02844, 29, 93.59, -81.38, 0.27556, 30, 28.57, -84.55, 0.15737, 31, -86.79, -62.24, 0.04186, 41, 78.54, 70.91, 0.43331, 42, -55.3, 59.4, 0.06347, 6, 28, 181.37, -137.24, 0.00872, 29, 110.51, -101.99, 0.12944, 30, 55.09, -87.31, 0.15984, 31, -61.77, -71.47, 0.09583, 41, 97.99, 52.66, 0.39911, 42, -29.69, 51.99, 0.20707, 3, 27, 44.95, -1.87, 0.98923, 38, 143.4, 52.75, 0.00016, 39, 18.65, 66.12, 0.01062, 4, 27, 85.32, -17.02, 0.81354, 28, -45.23, -22.07, 0.02095, 39, 61.56, 61.84, 0.11327, 40, -50.66, 42.1, 0.05225, 4, 27, 129.38, -32.36, 0.09783, 28, 1.14, -27.29, 0.63019, 39, 108.08, 58.32, 0.0075, 40, -6.83, 58.11, 0.26448, 3, 28, 39.8, -33.29, 0.71406, 40, 30.45, 69.99, 0.27164, 41, -77.56, 58.33, 0.0143, 4, 28, 63.39, -54.81, 0.52164, 29, -32.04, -82.1, 0.012, 40, 61.16, 61.25, 0.36291, 41, -45.93, 53.92, 0.10345, 4, 28, 78.38, -77.04, 0.30841, 29, -8.59, -95.11, 0.04392, 40, 84.5, 48.04, 0.34838, 41, -21, 44.06, 0.29929, 7, 28, 131.65, -92.22, 0.13872, 29, 45.76, -84.44, 0.24382, 30, -3.28, -120.35, 0.01958, 31, -126.51, -89.05, 0.00286, 40, 138.94, 58.23, 0.01797, 41, 31.52, 61.68, 0.57641, 42, -92.99, 29.79, 0.00064, 4, 28, 103.41, -54.62, 0.47994, 29, 3.53, -63.76, 0.15481, 40, 96.89, 79.28, 0.11646, 41, -13.04, 76.71, 0.24879, 4, 28, 72.65, -26.72, 0.82953, 29, -36.53, -52.87, 0.00325, 40, 56.92, 90.52, 0.11873, 41, -54.18, 82.32, 0.04849, 3, 28, 25.57, -12.91, 0.91303, 40, 8.63, 81.88, 0.0864, 41, -100.82, 67.09, 0.00057, 4, 27, 117.02, -14.52, 0.49301, 28, -14.86, -12.62, 0.41443, 39, 91.56, 72.39, 0.01315, 40, -27.69, 64.1, 0.07941, 4, 27, 79.85, -2.69, 0.984, 28, -53.73, -9.3, 0.00117, 39, 52.6, 74.28, 0.01017, 40, -63.96, 49.74, 0.00465, 2, 26, 178.03, 5.92, 0.00659, 27, 42.58, 13.38, 0.99341, 3, 26, 205.71, 37, 0.00326, 27, 66.91, 47.15, 0.99635, 28, -77.37, 36.45, 0.00039, 3, 28, 174.96, 94.38, 0.04902, 29, -0.35, 101.48, 0.93884, 30, -166.8, -20.59, 0.01215, 2, 29, 67.58, 99.71, 0.88044, 30, -117.26, 25.93, 0.11956, 2, 29, 118.83, 73.44, 0.58414, 30, -62.35, 43.28, 0.41586, 2, 29, 153.16, 36.75, 0.14276, 30, -12.14, 41.34, 0.85724, 1, 30, 33.84, 18.95, 1, 5, 29, 171.53, -52.07, 0.01111, 30, 63.37, -8.9, 0.92011, 31, -34.35, 2.44, 0.04352, 41, 152.03, 110.07, 0.01043, 42, -7.66, 127.69, 0.01483, 2, 27, 92.78, 39.24, 0.96139, 28, -50.39, 34.45, 0.03861, 3, 27, 141.99, 36.07, 0.36993, 28, -1.7, 42.23, 0.62767, 29, -134.08, -25.17, 0.0024, 3, 27, 193.08, 24.11, 0.02058, 28, 50.77, 41.86, 0.92106, 29, -87.16, -1.69, 0.05836, 2, 28, 97.85, 33.57, 0.73262, 29, -41.46, 12.3, 0.26738, 1, 29, 5.19, 13.9, 1, 1, 29, 53.84, 7.35, 1, 6, 28, 212.41, -50.13, 0.00268, 29, 98.62, -10.28, 0.95778, 30, -17.85, -30.46, 0.01627, 31, -118.38, 1.66, 0.00061, 41, 74.32, 142.06, 0.02163, 42, -91.42, 120.85, 0.00103, 6, 28, 237.75, -86.12, 0.00069, 29, 137.54, -30.85, 0.14524, 30, 24.29, -17.71, 0.80103, 31, -74.4, 3.58, 0.00837, 41, 115.58, 126.71, 0.03443, 42, -47.69, 125.93, 0.01024, 2, 30, 107.49, 0.65, 0.0025, 31, 10.76, 0.78, 0.9975, 2, 30, 139.19, 14.42, 0.00601, 31, 44.88, 6.28, 0.99399, 2, 30, 161.04, 37.47, 0.00065, 31, 71.75, 23.21, 0.99935, 2, 31, 68.96, -40.14, 0.68026, 42, 98.45, 92.67, 0.31974, 5, 29, 198.84, -138.55, 0.00061, 30, 143.6, -51.18, 0.0029, 31, 32.93, -58.38, 0.53496, 41, 190.32, 27.86, 0.00061, 42, 63.82, 71.88, 0.46092, 6, 28, 210.57, -169.86, 6e-05, 29, 151.33, -117.8, 0.02949, 30, 95.24, -69.84, 0.11604, 31, -18.56, -64.48, 0.31912, 41, 140.52, 42.27, 0.06991, 42, 12.91, 62.08, 0.46538, 2, 31, 59.63, -88.22, 0.25898, 42, 92.6, 44.04, 0.74102, 2, 31, 82.49, -60.65, 0.48007, 42, 113.42, 73.19, 0.51993, 5, 29, 143.92, -146.03, 0.00995, 30, 109.82, -95.12, 0.0323, 31, -10.68, -92.59, 0.16616, 41, 136.83, 13.32, 0.01794, 42, 22.79, 34.62, 0.77365, 5, 29, 185.71, -147.84, 0.001, 30, 140.81, -67.03, 0.00495, 31, 26.29, -73.04, 0.39423, 41, 178.5, 16.94, 0.00046, 42, 58.26, 56.78, 0.59936, 2, 31, 52.3, -63.09, 0.48205, 42, 83.48, 68.58, 0.51795, 2, 27, 115.26, 23.25, 0.8162, 28, -24.93, 23.83, 0.1838, 3, 27, 162.94, 15.07, 0.06169, 28, 23.38, 26.38, 0.93186, 29, -104.54, -27.91, 0.00645, 2, 28, 69.71, 19.9, 0.96616, 29, -60.32, -12.66, 0.03384, 2, 28, 108.57, 6.97, 0.95652, 29, -19.83, -6.54, 0.04348, 4, 28, 151.93, -33.85, 0.13407, 29, 37.34, -23.23, 0.77254, 40, 131.06, 119.5, 0.00437, 41, 15.24, 121.28, 0.08901, 6, 28, 195.09, -77.29, 0.02135, 29, 95.52, -42.34, 0.58817, 30, 2.49, -55.43, 0.19121, 31, -104.85, -27.57, 0.01195, 41, 75.4, 109.87, 0.17235, 42, -75.82, 92.67, 0.01497, 6, 28, 220.7, -133.42, 0.00128, 29, 143.81, -80.74, 0.09108, 30, 63.83, -48.77, 0.40859, 31, -43.77, -36.3, 0.20816, 41, 128.26, 78.05, 0.12395, 42, -14.27, 88.37, 0.16694, 5, 29, 182.27, -102.45, 0.00892, 30, 106.43, -37.17, 0.07979, 31, 0.38, -35.6, 0.66501, 41, 169.2, 61.5, 0.017, 42, 29.72, 92.25, 0.22928, 2, 31, 40.87, -26.7, 0.8321, 42, 69.46, 104.05, 0.1679, 2, 31, 78.65, -11.96, 0.92124, 42, 106.08, 121.48, 0.07876, 3, 27, 180.62, 47.53, 0.0866, 28, 33.45, 61.95, 0.86127, 29, -111.72, 8.35, 0.05213, 3, 27, 229.32, 42.69, 0.00174, 28, 82.01, 67.99, 0.70536, 29, -71.19, 35.77, 0.2929, 2, 28, 141.4, 60.54, 0.16333, 29, -14.89, 56.1, 0.83667, 3, 28, 189.31, 39.95, 0.00055, 29, 37.15, 59.49, 0.97938, 30, -110.62, -24.07, 0.02008, 2, 29, 85.85, 44.93, 0.87874, 30, -65.75, -0.18, 0.12126, 2, 29, 119.61, 23.14, 0.51771, 30, -26.42, 8.06, 0.48229, 1, 30, 11.73, 7.81, 1], "hull": 71, "edges": [36, 38, 38, 40, 64, 66, 78, 80, 80, 82, 82, 84, 134, 136, 28, 30, 30, 32, 32, 34, 34, 36, 26, 28, 26, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 40, 22, 24, 24, 26, 24, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 40, 42, 164, 42, 22, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 42, 44, 176, 44, 20, 22, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 44, 46, 188, 46, 178, 190, 190, 192, 192, 194, 194, 196, 196, 118, 16, 18, 18, 20, 14, 16, 12, 14, 10, 12, 118, 120, 120, 122, 6, 8, 8, 10, 6, 4, 122, 124, 4, 2, 124, 126, 2, 0, 0, 140, 126, 128, 128, 130, 136, 138, 138, 140, 130, 132, 132, 134, 20, 198, 198, 178, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 208, 210, 210, 212, 212, 214, 214, 216, 216, 218, 218, 220, 220, 222, 182, 224, 224, 226, 226, 228, 228, 230, 230, 232, 232, 234, 234, 236, 236, 238, 238, 240, 240, 242, 244, 246, 246, 248, 248, 250, 252, 224, 184, 254, 254, 256, 256, 258, 258, 260, 260, 262, 262, 264, 264, 266, 268, 270, 270, 272, 272, 274, 274, 92, 182, 276, 276, 278, 278, 280, 280, 282, 282, 284, 284, 286, 286, 288, 288, 290, 290, 292, 292, 294, 294, 296, 92, 94, 296, 94, 94, 96, 96, 298, 298, 300, 300, 302, 302, 304, 304, 306, 306, 308, 308, 310, 310, 312, 312, 314, 314, 316, 316, 224, 194, 318, 318, 320, 320, 322, 322, 324, 324, 326, 326, 104, 326, 328, 328, 330, 330, 332, 332, 298, 96, 98, 98, 100, 100, 102, 102, 104, 116, 118, 114, 116, 112, 114, 108, 110, 110, 112, 196, 334, 334, 336, 336, 338, 338, 340, 104, 106, 106, 108, 340, 106, 324, 342, 342, 344, 344, 346, 346, 348, 348, 350, 350, 352, 352, 306, 322, 354, 354, 356, 356, 358, 358, 360, 360, 362, 362, 308, 250, 364, 364, 252, 364, 366, 366, 368, 368, 370, 370, 372, 372, 374, 242, 376, 376, 244, 374, 376, 188, 378, 378, 380, 380, 382, 382, 384, 384, 386, 386, 388, 388, 390, 390, 392, 392, 394, 394, 266, 186, 396, 396, 398, 398, 400, 400, 402, 402, 404, 404, 406, 406, 262, 264, 408, 408, 410, 410, 412, 412, 414, 414, 416, 416, 418, 418, 420, 420, 188, 46, 422, 422, 50, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 58, 424, 424, 426, 426, 428, 428, 430, 430, 432, 432, 434, 378, 436, 436, 438, 438, 440, 440, 442, 442, 444, 444, 446, 446, 448, 448, 450, 450, 434, 434, 452, 452, 454, 454, 456, 84, 86, 456, 86, 90, 92, 90, 458, 458, 460, 460, 462, 462, 394, 270, 464, 464, 466, 466, 90, 266, 468, 468, 268, 468, 470, 470, 472, 472, 458, 378, 474, 474, 476, 476, 478, 478, 480, 480, 482, 482, 484, 484, 486, 486, 488, 488, 490, 490, 492, 86, 88, 88, 90, 492, 88, 438, 494, 494, 496, 496, 498, 498, 500, 500, 502, 502, 504, 504, 506, 506, 434, 76, 78, 74, 76, 70, 72, 72, 74, 66, 68, 68, 70, 60, 62, 62, 64], "width": 338, "height": 442}}, "zs6": {"zs6": {"type": "mesh", "uvs": [0.99511, 0.08043, 0.99523, 0.20867, 0.99532, 0.30396, 0.99542, 0.40794, 0.99551, 0.50313, 0.93594, 0.58473, 0.87691, 0.6656, 0.8058, 0.763, 0.73246, 0.86347, 0.68599, 0.92713, 0.63279, 1, 0.61119, 1, 0.61976, 0.92484, 0.62824, 0.85047, 0.641, 0.73861, 0.65099, 0.65095, 0.66407, 0.53628, 0.67634, 0.4287, 0.55266, 0.50199, 0.43538, 0.57149, 0.31638, 0.64201, 0.19259, 0.71538, 0.07237, 0.76266, 0, 0.79112, 0, 0.74833, 0.05034, 0.69764, 0.11437, 0.63317, 0.17108, 0.57608, 0.25938, 0.48718, 0.34247, 0.40352, 0.43077, 0.31462, 0.53738, 0.20728, 0.63183, 0.11219, 0.69783, 0.04574, 0.73888, 0.0044, 0.99503, 0.00342, 0.83514, 0.06658, 0.8111, 0.14802, 0.74774, 0.25545, 0.61447, 0.32649, 0.90505, 0.39061, 0.86354, 0.48244, 0.49553, 0.41506, 0.42877, 0.49804, 0.33045, 0.56388, 0.22777, 0.64186, 0.12727, 0.68864, 0.07265, 0.73196, 0.80892, 0.56908, 0.76522, 0.65572, 0.7106, 0.74236, 0.67565, 0.84632], "triangles": [9, 10, 12, 10, 11, 12, 12, 13, 9, 13, 51, 9, 9, 51, 8, 13, 14, 51, 8, 51, 50, 7, 8, 50, 51, 14, 50, 50, 49, 7, 7, 49, 6, 14, 15, 50, 50, 15, 49, 49, 48, 6, 49, 15, 16, 6, 48, 5, 48, 49, 16, 48, 41, 5, 5, 41, 4, 48, 16, 41, 16, 17, 41, 4, 41, 3, 41, 40, 3, 41, 17, 40, 23, 24, 22, 21, 47, 46, 21, 22, 47, 22, 24, 47, 24, 25, 47, 47, 25, 46, 21, 45, 20, 21, 46, 45, 25, 26, 46, 46, 26, 45, 20, 45, 44, 26, 27, 45, 45, 27, 44, 20, 44, 19, 27, 28, 44, 44, 43, 19, 19, 43, 18, 44, 28, 43, 43, 42, 18, 28, 29, 43, 43, 29, 42, 18, 42, 17, 42, 39, 17, 17, 39, 40, 29, 30, 42, 42, 30, 39, 39, 38, 40, 30, 31, 39, 39, 31, 38, 38, 37, 1, 31, 32, 38, 38, 32, 37, 37, 36, 0, 32, 33, 37, 37, 33, 36, 33, 34, 36, 36, 34, 35, 40, 2, 3, 40, 38, 2, 2, 38, 1, 1, 37, 0, 36, 35, 0], "vertices": [1, 82, 0.43, 10.65, 1, 1, 82, 30.18, 9.97, 1, 1, 82, 52.28, 9.47, 1, 2, 82, 76.39, 8.92, 0.61456, 87, -7.87, 12.82, 0.38544, 2, 82, 98.47, 8.42, 0.01275, 87, 11.6, 23.23, 0.98725, 2, 87, 33.46, 22.47, 0.9192, 88, -18.99, 23.13, 0.0808, 2, 87, 55.13, 21.71, 0.26023, 88, 2.57, 20.89, 0.73977, 2, 88, 28.54, 18.19, 0.92038, 89, -9.26, 21.07, 0.07962, 2, 88, 55.33, 15.41, 0.04237, 89, 16.68, 13.83, 0.95763, 1, 89, 33.12, 9.24, 1, 1, 89, 51.94, 3.98, 1, 1, 89, 52.93, 0.13, 1, 1, 89, 35.65, -2.7, 1, 1, 89, 18.55, -5.5, 1, 2, 88, 35.79, -11.79, 0.60878, 89, -7.16, -9.71, 0.39122, 3, 84, 72.56, 52.52, 0.00122, 87, 71.7, -16.56, 0.03143, 88, 16.48, -18.43, 0.96735, 4, 83, 95.13, 38.06, 0.00235, 84, 50.44, 37.55, 0.07743, 87, 47.09, -26.96, 0.67216, 88, -8.79, -27.12, 0.24806, 5, 82, 79.83, -49.88, 0.001, 83, 75.04, 23.08, 0.03586, 84, 29.68, 23.51, 0.57868, 87, 24, -36.71, 0.3819, 88, -32.48, -35.27, 0.00256, 3, 84, 57.25, 16.64, 0.5973, 85, -1.66, 19.18, 0.3975, 87, 49.72, -48.79, 0.00521, 2, 84, 83.38, 10.13, 0.0014, 85, 25.2, 17.14, 0.9986, 2, 85, 52.45, 15.07, 0.19705, 86, 8.67, 16.03, 0.80295, 1, 86, 37.04, 14.2, 1, 1, 86, 60.99, 8.19, 1, 1, 86, 75.41, 4.57, 1, 1, 86, 68.96, -2.98, 1, 1, 86, 54.29, -5.92, 1, 1, 86, 35.61, -9.65, 1, 1, 86, 19.08, -12.96, 1, 2, 85, 36.74, -18.89, 0.65701, 86, -6.67, -18.11, 0.34299, 3, 84, 64.02, -27.76, 0.04361, 85, 12.46, -23.46, 0.9548, 86, -30.89, -22.95, 0.00159, 2, 84, 37.77, -28.23, 0.70557, 85, -13.35, -28.32, 0.29443, 3, 83, 53.86, -30.25, 0.10018, 84, 6.07, -28.79, 0.89625, 85, -44.5, -34.18, 0.00357, 2, 83, 25.83, -32.04, 0.75181, 84, -22.01, -29.29, 0.24819, 3, 82, -8.9, -43.85, 0.00492, 83, 6.25, -33.29, 0.95605, 84, -41.63, -29.63, 0.03904, 3, 82, -18.31, -36.07, 0.04469, 83, -5.94, -34.06, 0.94694, 84, -53.83, -29.85, 0.00837, 1, 82, -17.43, 11.05, 1, 3, 82, -3.47, -18.71, 0.19355, 83, -7.02, -11.25, 0.80593, 84, -53.87, -7.01, 0.00052, 2, 83, 10, -1.93, 0.99882, 84, -36.44, 1.52, 0.00118, 4, 82, 39.96, -35.81, 0.05067, 83, 36.34, 6.03, 0.92443, 84, -9.76, 8.26, 0.00353, 87, -17.64, -44.03, 0.02137, 2, 84, 18.52, -0.31, 0.99988, 85, -37, -4.01, 0.00012, 4, 82, 71.98, -7.61, 0.31191, 83, 40.37, 48.51, 0.01561, 84, -3.79, 50.51, 0.00738, 87, -3.6, -3.74, 0.6651, 3, 83, 61.33, 57.05, 0.00025, 84, 17.55, 58.08, 0.00103, 87, 18.8, -0.45, 0.99872, 2, 84, 48.28, -4.25, 0.78723, 85, -7, -2.92, 0.21277, 2, 84, 70.96, -1.59, 0.00527, 85, 14.91, 3.5, 0.99473, 2, 85, 38.59, 3.12, 0.94747, 86, -5.07, 3.92, 0.05253, 2, 85, 64.72, 4.32, 0.00084, 86, 21.05, 5.42, 0.99916, 1, 86, 42.16, 1.68, 1, 1, 86, 56.33, 2.8, 1, 1, 87, 41.26, 0.14, 1, 2, 87, 62.78, 2.5, 0.00046, 88, 8.89, 1.2, 0.99954, 1, 88, 31.34, 0.25, 1, 2, 88, 55.98, 4.25, 0.00398, 89, 15.44, 2.71, 0.99602], "hull": 36, "edges": [20, 22, 46, 48, 68, 70, 66, 68, 66, 72, 0, 70, 72, 0, 64, 66, 64, 74, 0, 2, 74, 2, 62, 64, 62, 76, 2, 4, 76, 4, 60, 62, 60, 78, 78, 80, 4, 6, 6, 8, 80, 6, 58, 60, 34, 82, 82, 8, 34, 84, 84, 58, 56, 58, 56, 86, 34, 36, 86, 36, 54, 56, 54, 88, 36, 38, 88, 38, 52, 54, 52, 90, 38, 40, 40, 42, 90, 40, 48, 50, 50, 52, 50, 92, 92, 42, 48, 94, 42, 44, 44, 46, 94, 44, 32, 34, 32, 96, 8, 10, 96, 10, 30, 32, 30, 98, 10, 12, 98, 12, 28, 30, 28, 100, 12, 14, 100, 14, 26, 28, 26, 102, 14, 16, 102, 16, 22, 24, 24, 26, 16, 18, 18, 20, 24, 18], "width": 110, "height": 139}}, "sp1": {"sp1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [41.39, 28.94, 51, -6.79, -0.17, -20.56, -9.79, 15.16], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 22, "height": 31}}, "l1": {"l1": {"type": "mesh", "uvs": [0.71603, 0, 0.96749, 0.24052, 0.97661, 0.36654, 0.98956, 0.54557, 1, 0.68982, 1, 0.86243, 0.94278, 1, 0.79896, 1, 0.63718, 0.9915, 0.37555, 0.97774, 0.16903, 0.96689, 0.11044, 0.78425, 0.05808, 0.62101, 0, 0.43993, 0, 0.32981, 0, 0.20058, 0.09396, 0.17199, 0.24392, 0.12636, 0.41128, 0.07543, 0.53988, 0.0363, 0.65915, 0, 0.57867, 0.24685, 0.67485, 0.38236, 0.79612, 0.50461, 0.89231, 0.59298, 0.86303, 0.71818, 0.88603, 0.85663, 0.45322, 0.29251, 0.5724, 0.48988, 0.67485, 0.56942, 0.73967, 0.64601, 0.77312, 0.77857, 0.78567, 0.89051, 0.2504, 0.37989, 0.43231, 0.64207, 0.54312, 0.77463, 0.60376, 0.89099, 0.10821, 0.45795, 0.10111, 0.59718, 0.25249, 0.69804, 0.33822, 0.81587, 0.73758, 0.21934, 0.91949, 0.48594, 0.82331, 0.16043, 0.90067, 0.28857, 0.15435, 0.54421, 0.22746, 0.50301, 0.31061, 0.47055, 0.42185, 0.44605, 0.52248, 0.46484, 0.19821, 0.61571, 0.31691, 0.6151, 0.39715, 0.5946, 0.45625, 0.56966, 0.4941, 0.52725, 0.74096, 0.42364, 0.74096, 0.36244, 0.78721, 0.30469, 0.85106, 0.27276, 0.89185, 0.27396, 0.94251, 0.33152, 0.91557, 0.36244, 0.86507, 0.41506, 0.80547, 0.42727, 0.21238, 0.55775, 0.27382, 0.53302, 0.3335, 0.51076, 0.42301, 0.47367, 0.49322, 0.47861, 0.77231, 0.36486, 0.8039, 0.32406, 0.86533, 0.29562, 0.21708, 0.59764, 0.3337, 0.59388, 0.38706, 0.58261, 0.44423, 0.55898, 0.47243, 0.52945, 0.80019, 0.40649, 0.85279, 0.38555, 0.89547, 0.35548, 0.93178, 0.32051], "triangles": [6, 32, 26, 6, 26, 5, 8, 32, 7, 6, 7, 32, 9, 36, 8, 8, 36, 32, 10, 40, 9, 36, 9, 40, 10, 11, 40, 5, 25, 4, 5, 26, 25, 11, 12, 39, 12, 13, 38, 38, 13, 37, 15, 16, 33, 37, 13, 14, 15, 33, 37, 16, 17, 33, 37, 14, 15, 17, 18, 27, 18, 19, 21, 19, 20, 41, 41, 20, 43, 43, 20, 0, 1, 43, 0, 36, 40, 35, 36, 31, 32, 36, 35, 31, 32, 31, 26, 31, 25, 26, 11, 39, 40, 40, 39, 34, 40, 34, 35, 34, 51, 52, 51, 34, 39, 39, 12, 38, 35, 30, 31, 31, 30, 25, 30, 35, 34, 29, 34, 53, 30, 34, 29, 29, 54, 28, 54, 29, 53, 25, 24, 4, 25, 30, 24, 38, 50, 39, 39, 50, 51, 24, 3, 4, 30, 23, 24, 30, 29, 23, 34, 52, 53, 50, 72, 51, 50, 38, 72, 52, 73, 74, 52, 51, 73, 51, 72, 73, 38, 64, 72, 72, 65, 73, 72, 64, 65, 38, 45, 64, 38, 37, 45, 52, 75, 53, 52, 74, 75, 74, 73, 66, 24, 23, 42, 24, 42, 3, 42, 23, 63, 73, 65, 66, 74, 66, 75, 53, 76, 54, 53, 75, 76, 29, 28, 23, 66, 67, 75, 75, 67, 76, 64, 46, 65, 64, 45, 46, 42, 2, 3, 45, 37, 46, 66, 65, 47, 54, 76, 28, 76, 67, 68, 28, 76, 68, 65, 46, 47, 66, 47, 67, 63, 62, 42, 23, 22, 55, 23, 55, 63, 47, 46, 33, 68, 49, 28, 23, 28, 22, 22, 28, 27, 42, 62, 2, 2, 62, 61, 49, 68, 48, 47, 48, 67, 68, 67, 48, 46, 37, 33, 47, 33, 48, 27, 28, 49, 49, 48, 27, 48, 33, 27, 55, 77, 63, 63, 77, 62, 22, 56, 55, 55, 69, 77, 55, 56, 69, 77, 78, 62, 61, 78, 79, 61, 62, 78, 77, 69, 78, 69, 70, 78, 78, 70, 79, 27, 21, 22, 22, 21, 56, 33, 17, 27, 61, 60, 2, 60, 1, 2, 69, 56, 70, 61, 79, 60, 56, 21, 57, 56, 57, 70, 57, 21, 41, 70, 71, 79, 79, 80, 60, 80, 71, 44, 80, 79, 71, 60, 80, 1, 70, 57, 71, 80, 44, 1, 57, 58, 71, 57, 41, 58, 71, 59, 44, 71, 58, 59, 27, 18, 21, 44, 59, 1, 1, 59, 43, 41, 43, 58, 59, 58, 43, 21, 19, 41], "vertices": [1, 12, 180.01, 53.17, 1, 2, 13, 62, 15.95, 0.784, 12, 155.36, -2.23, 0.216, 2, 13, 40.41, 2.16, 0.6768, 12, 133.77, -16.01, 0.3232, 2, 13, 9.74, -17.42, 0.80068, 12, 103.1, -35.6, 0.19932, 2, 13, -14.97, -33.2, 0.63912, 12, 78.39, -51.38, 0.36088, 1, 12, 47.93, -68.71, 1, 1, 12, 19.62, -75.41, 1, 1, 12, 9.44, -57.54, 1, 1, 12, -0.5, -36.58, 1, 1, 12, -16.58, -2.68, 1, 2, 13, -122.63, 42.25, 0.00085, 12, -29.27, 24.07, 0.99915, 1, 12, -1.19, 49.69, 1, 1, 12, 23.91, 72.59, 1, 1, 12, 51.74, 97.99, 1, 1, 12, 71.17, 109.05, 1, 1, 12, 93.97, 122.02, 1, 1, 12, 105.66, 113.22, 1, 1, 12, 124.32, 99.16, 1, 1, 12, 145.15, 83.48, 1, 1, 12, 161.14, 71.42, 1, 1, 12, 175.98, 60.24, 1, 2, 13, 33.38, 63.64, 0.94684, 12, 126.74, 45.46, 0.05316, 1, 13, 16.28, 38.08, 1, 1, 13, 3.28, 10.73, 1, 2, 13, -5.5, -10.1, 0.95228, 12, 87.86, -28.27, 0.04772, 2, 13, -29.66, -19.03, 0.71302, 12, 63.7, -37.21, 0.28698, 2, 13, -52.46, -35.79, 0.58122, 12, 40.9, -53.97, 0.41878, 2, 13, 16.45, 74.64, 0.88192, 12, 109.81, 56.47, 0.11808, 2, 13, -9.94, 40.01, 0.95817, 12, 83.42, 21.84, 0.04183, 2, 13, -16.73, 19.29, 0.944, 12, 76.63, 1.12, 0.056, 2, 13, -25.66, 3.55, 0.93749, 12, 67.7, -14.63, 0.06251, 2, 13, -46.68, -13.92, 0.84716, 12, 46.68, -32.1, 0.15284, 2, 13, -65.54, -26.72, 0.59726, 12, 27.82, -44.89, 0.40274, 2, 13, -13.31, 91.08, 0.73423, 12, 80.05, 72.9, 0.26577, 2, 13, -46.7, 42.14, 0.84879, 12, 46.66, 23.97, 0.15121, 2, 13, -62.25, 15.06, 0.74898, 12, 31.11, -3.12, 0.25102, 2, 13, -78.49, -4.16, 0.584, 12, 14.87, -22.33, 0.416, 2, 13, -37.14, 100.91, 0.56, 12, 56.22, 82.73, 0.44, 2, 13, -62.21, 87.81, 0.7524, 12, 31.15, 69.64, 0.2476, 2, 13, -69.29, 58.87, 0.75125, 12, 24.07, 40.7, 0.24875, 2, 13, -84.02, 36.39, 0.7508, 12, 9.34, 18.21, 0.2492, 2, 13, 49.47, 46.65, 0.93289, 12, 142.83, 28.47, 0.06711, 2, 13, 15.3, -2.73, 0.88317, 12, 108.66, -20.9, 0.11683, 2, 13, 65.93, 41.91, 0.48446, 12, 159.29, 23.73, 0.51554, 2, 13, 48.79, 19.43, 0.81954, 12, 142.15, 1.25, 0.18046, 2, 13, -49.1, 86.52, 0.70705, 12, 44.26, 68.34, 0.29295, 2, 13, -36.66, 81.57, 0.73973, 12, 56.7, 63.39, 0.26027, 2, 13, -25.05, 74.49, 0.77896, 12, 68.31, 56.31, 0.22104, 2, 13, -12.86, 63.12, 0.85604, 12, 80.5, 44.95, 0.14396, 2, 13, -9.05, 48.73, 0.92461, 12, 84.31, 30.56, 0.07539, 2, 13, -58.61, 73.88, 0.7282, 12, 34.75, 55.71, 0.2718, 2, 13, -50.11, 59.19, 0.78986, 12, 43.25, 41.02, 0.21014, 2, 13, -40.81, 51.28, 0.83228, 12, 52.55, 33.1, 0.16772, 2, 13, -32.23, 46.44, 0.87342, 12, 61.13, 28.26, 0.12658, 2, 13, -22.07, 45.99, 0.90183, 12, 71.29, 27.82, 0.09817, 2, 13, 13.67, 25.72, 0.98922, 12, 107.03, 7.54, 0.01078, 2, 13, 24.47, 31.86, 0.96321, 12, 117.83, 13.68, 0.03679, 2, 13, 37.93, 31.91, 0.92762, 12, 131.29, 13.73, 0.07238, 2, 13, 48.07, 27.18, 0.85641, 12, 141.43, 9, 0.14359, 2, 13, 50.75, 21.99, 0.78133, 12, 144.11, 3.82, 0.21867, 2, 13, 44.17, 9.92, 0.7409, 12, 137.53, -8.26, 0.2591, 2, 13, 36.82, 10.16, 0.77187, 12, 130.18, -8.02, 0.22813, 2, 13, 23.96, 11.15, 0.90388, 12, 117.32, -7.02, 0.09612, 2, 13, 17.59, 17.33, 0.94951, 12, 110.95, -0.84, 0.05049, 2, 13, -47.38, 77.94, 0.73373, 12, 45.98, 59.77, 0.26627, 2, 13, -38.67, 72.79, 0.7642, 12, 54.69, 54.61, 0.2358, 2, 13, -30.52, 67.61, 0.79357, 12, 62.84, 49.43, 0.20643, 2, 13, -17.65, 60.21, 0.85574, 12, 75.71, 42.03, 0.14426, 2, 13, -13.55, 50.99, 0.90377, 12, 79.81, 32.81, 0.09623, 2, 13, 26.26, 27.72, 0.93238, 12, 119.62, 9.55, 0.06762, 2, 13, 35.69, 27.89, 0.905, 12, 129.05, 9.72, 0.095, 2, 13, 45.05, 23.11, 0.84954, 12, 138.41, 4.94, 0.15046, 2, 13, -54.09, 73.35, 0.73743, 12, 39.27, 55.18, 0.26257, 2, 13, -45.17, 59.24, 0.79788, 12, 48.19, 41.06, 0.20212, 2, 13, -39.41, 53.74, 0.82621, 12, 53.95, 35.56, 0.17379, 2, 13, -31.2, 49.01, 0.86584, 12, 62.16, 30.83, 0.13416, 2, 13, -23.99, 48.47, 0.8868, 12, 69.37, 30.29, 0.1132, 2, 13, 20.88, 20.08, 0.94012, 12, 114.24, 1.9, 0.05988, 2, 13, 28.3, 15.64, 0.86262, 12, 121.66, -2.53, 0.13738, 2, 13, 36.62, 13.36, 0.79585, 12, 129.98, -4.82, 0.20415, 2, 13, 45.36, 12.35, 0.76106, 12, 138.72, -5.82, 0.23894], "hull": 21, "edges": [0, 40, 0, 2, 8, 10, 10, 12, 12, 14, 36, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 12, 34, 36, 34, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 14, 30, 32, 32, 34, 32, 66, 68, 70, 70, 72, 14, 16, 72, 16, 30, 74, 74, 76, 76, 78, 78, 80, 16, 18, 18, 20, 80, 18, 24, 26, 76, 24, 20, 22, 22, 24, 78, 22, 26, 28, 28, 30, 36, 38, 38, 40, 38, 82, 6, 8, 84, 6, 0, 86, 2, 4, 4, 6, 76, 90, 90, 92, 66, 94, 92, 94, 94, 96, 96, 98, 98, 56, 76, 100, 100, 102, 104, 68, 102, 104, 104, 106, 106, 108, 108, 56, 110, 112, 82, 114, 112, 114, 114, 116, 86, 118, 118, 88, 116, 118, 118, 2, 4, 120, 2, 120, 120, 122, 124, 84, 122, 124, 124, 126, 126, 110, 76, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 56, 110, 138, 138, 140, 140, 142, 142, 88, 88, 2, 76, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 56, 110, 154, 154, 156, 156, 158, 88, 160, 160, 120, 158, 160, 160, 2], "width": 85, "height": 121}}, "x1": {"x1": {"type": "mesh", "uvs": [0.85433, 0.01694, 0.87554, 0.03461, 0.90185, 0.05652, 0.92696, 0.07743, 0.92603, 0.09129, 0.92407, 0.12063, 0.92173, 0.15561, 0.91946, 0.18953, 0.95478, 0.20274, 0.97534, 0.25381, 0.99566, 0.30429, 0.99776, 0.36723, 0.99917, 0.40924, 0.94459, 0.44255, 0.89331, 0.47384, 0.82597, 0.50258, 0.77725, 0.52336, 0.73405, 0.5418, 0.67284, 0.56791, 0.60939, 0.59499, 0.56241, 0.61503, 0.50556, 0.61797, 0.46894, 0.61987, 0.42963, 0.6219, 0.38766, 0.62407, 0.37762, 0.64411, 0.36172, 0.67585, 0.35508, 0.6905, 0.34094, 0.72172, 0.32586, 0.75502, 0.30671, 0.79728, 0.28321, 0.84916, 0.26003, 0.90033, 0.23868, 0.94746, 0.21502, 0.9997, 0.06162, 0.99543, 0, 0.98987, 0, 0.9848, 0.03449, 0.93215, 0.05974, 0.89362, 0.09164, 0.84493, 0.12737, 0.79041, 0.15237, 0.75226, 0.18328, 0.70507, 0.20801, 0.66733, 0.2355, 0.62537, 0.26843, 0.59433, 0.3033, 0.56146, 0.34854, 0.54233, 0.4058, 0.51813, 0.45308, 0.49815, 0.48784, 0.48345, 0.53362, 0.4641, 0.57838, 0.44518, 0.63352, 0.42188, 0.68105, 0.40178, 0.71458, 0.37255, 0.74936, 0.34223, 0.77117, 0.32321, 0.79525, 0.28237, 0.81686, 0.24571, 0.83591, 0.21339, 0.80995, 0.17914, 0.79273, 0.15642, 0.77665, 0.1352, 0.76773, 0.10834, 0.75863, 0.08095, 0.7508, 0.05739, 0.74372, 0.03607, 0.74041, 0.02611, 0.75534, 0.01311, 0.78442, 0.00853, 0.83471, 0.0006, 0.11756, 0.93505, 0.16644, 0.9374, 0.14893, 0.89283, 0.19417, 0.89596, 0.1752, 0.84358, 0.22773, 0.84749, 0.19928, 0.7951, 0.25983, 0.79823, 0.227, 0.75523, 0.28755, 0.75523, 0.24597, 0.70988, 0.30506, 0.7177, 0.27296, 0.6747, 0.32184, 0.6833, 0.29412, 0.63873, 0.34008, 0.65906, 0.32184, 0.60902, 0.35103, 0.62935, 0.34957, 0.57775, 0.3824, 0.59573, 0.39263, 0.56026, 0.42238, 0.58519, 0.44293, 0.54693, 0.45592, 0.58113, 0.48566, 0.53534, 0.4954, 0.57359, 0.5246, 0.52433, 0.53867, 0.56374, 0.57328, 0.50984, 0.58897, 0.55099, 0.62142, 0.49419, 0.64846, 0.52606, 0.67064, 0.47506, 0.6993, 0.5052, 0.71607, 0.45129, 0.74636, 0.48665, 0.75339, 0.4084, 0.79936, 0.46347, 0.79017, 0.37247, 0.84534, 0.4368, 0.81505, 0.34406, 0.89239, 0.40608, 0.84479, 0.29943, 0.93241, 0.36666, 0.86373, 0.26291, 0.96378, 0.34116, 0.87725, 0.23045, 0.93945, 0.26291, 0.86535, 0.19336, 0.93512, 0.22524, 0.87238, 0.16843, 0.84155, 0.13771, 0.90591, 0.14815, 0.82547, 0.11098, 0.88503, 0.11136, 0.808, 0.08308, 0.87219, 0.08613, 0.79694, 0.05633, 0.86006, 0.05785, 0.7866, 0.03263, 0.84936, 0.03607, 0.78125, 0.01849, 0.84009, 0.01926], "triangles": [134, 70, 71, 135, 72, 0, 71, 72, 135, 134, 71, 135, 132, 134, 135, 133, 135, 0, 133, 0, 1, 132, 135, 133, 68, 69, 70, 68, 70, 134, 68, 134, 132, 130, 132, 133, 67, 68, 132, 67, 132, 130, 131, 133, 1, 131, 1, 2, 130, 133, 131, 131, 2, 3, 66, 67, 130, 128, 130, 131, 66, 130, 128, 129, 131, 3, 128, 131, 129, 4, 129, 3, 65, 66, 128, 126, 128, 129, 65, 128, 126, 127, 129, 4, 126, 129, 127, 5, 127, 4, 64, 65, 126, 124, 126, 127, 64, 126, 124, 125, 127, 5, 124, 127, 125, 6, 125, 5, 63, 64, 124, 123, 124, 125, 63, 124, 123, 62, 63, 123, 7, 125, 6, 123, 125, 7, 121, 62, 123, 121, 123, 7, 61, 62, 121, 121, 7, 122, 119, 121, 122, 61, 121, 119, 122, 7, 8, 122, 8, 9, 120, 122, 9, 119, 122, 120, 117, 61, 119, 117, 119, 120, 60, 61, 117, 115, 60, 117, 59, 60, 115, 120, 9, 10, 118, 120, 10, 117, 120, 118, 113, 59, 115, 58, 59, 113, 116, 117, 118, 115, 117, 116, 114, 113, 115, 118, 10, 11, 111, 58, 113, 116, 114, 115, 112, 113, 114, 111, 113, 112, 116, 12, 13, 11, 116, 118, 12, 116, 11, 114, 116, 13, 14, 114, 13, 112, 114, 14, 57, 58, 111, 109, 57, 111, 56, 57, 109, 107, 56, 109, 55, 56, 107, 110, 111, 112, 109, 111, 110, 108, 107, 109, 105, 54, 55, 105, 55, 107, 110, 108, 109, 103, 54, 105, 15, 110, 112, 15, 112, 14, 106, 105, 107, 106, 107, 108, 16, 108, 110, 16, 110, 15, 104, 105, 106, 17, 106, 108, 17, 108, 16, 18, 104, 106, 18, 106, 17, 103, 53, 54, 101, 52, 53, 101, 53, 103, 99, 51, 52, 99, 52, 101, 104, 103, 105, 97, 50, 51, 97, 51, 99, 95, 50, 97, 102, 101, 103, 102, 103, 104, 100, 99, 101, 100, 101, 102, 98, 97, 99, 98, 99, 100, 96, 97, 98, 19, 102, 104, 19, 104, 18, 20, 100, 102, 20, 102, 19, 21, 98, 100, 21, 100, 20, 22, 98, 21, 95, 49, 50, 93, 48, 49, 93, 49, 95, 91, 48, 93, 47, 48, 91, 96, 95, 97, 94, 93, 95, 94, 95, 96, 92, 91, 93, 92, 93, 94, 89, 47, 91, 22, 96, 98, 23, 94, 96, 23, 96, 22, 24, 92, 94, 23, 24, 94, 90, 91, 92, 90, 92, 24, 89, 91, 90, 25, 90, 24, 46, 47, 89, 87, 46, 89, 45, 46, 87, 88, 89, 90, 88, 90, 25, 87, 89, 88, 85, 45, 87, 44, 45, 85, 26, 88, 25, 86, 87, 88, 85, 87, 86, 27, 88, 26, 86, 88, 27, 83, 44, 85, 43, 44, 83, 84, 85, 86, 83, 85, 84, 28, 86, 27, 84, 86, 28, 29, 84, 28, 82, 83, 84, 82, 84, 29, 81, 43, 83, 81, 83, 82, 42, 43, 81, 79, 42, 81, 30, 82, 29, 80, 81, 82, 80, 82, 30, 79, 81, 80, 41, 42, 79, 77, 41, 79, 40, 41, 77, 78, 79, 80, 77, 79, 78, 31, 80, 30, 78, 80, 31, 75, 40, 77, 39, 40, 75, 76, 77, 78, 75, 77, 76, 32, 78, 31, 76, 78, 32, 73, 39, 75, 38, 39, 73, 74, 75, 76, 73, 75, 74, 33, 76, 32, 74, 76, 33, 35, 36, 37, 35, 38, 73, 35, 37, 38, 34, 74, 33, 73, 74, 34, 35, 73, 34], "vertices": [1, 106, 215.52, 20.57, 1, 1, 106, 205.07, 5.82, 1, 1, 106, 192.11, -12.48, 1, 1, 106, 179.74, -29.94, 1, 1, 106, 171.02, -29.71, 1, 1, 106, 152.56, -29.2, 1, 2, 105, 238.02, 40.58, 0.00749, 106, 130.54, -28.61, 0.99251, 2, 105, 218.64, 31.63, 0.20254, 106, 109.19, -28.03, 0.79746, 2, 105, 222.85, 6.81, 0.6241, 106, 101.96, -52.14, 0.3759, 2, 105, 201.45, -20.8, 0.92256, 106, 70.53, -67.39, 0.07744, 2, 105, 180.3, -48.09, 0.99835, 106, 39.47, -82.46, 0.00165, 1, 105, 146.38, -68.42, 1, 2, 104, 284.16, -15.4, 0.00498, 105, 123.73, -81.99, 0.99502, 2, 104, 242.03, -12.01, 0.08644, 105, 87.68, -59.94, 0.91356, 2, 104, 202.44, -8.82, 0.3748, 105, 53.8, -39.21, 0.6252, 2, 104, 154.83, 1.78, 0.86118, 105, 16.1, -8.25, 0.13882, 2, 104, 120.38, 9.45, 0.99138, 105, -11.16, 14.15, 0.00862, 2, 103, 209.08, 43.98, 0.00811, 104, 89.84, 16.25, 0.99189, 2, 103, 165.03, 39, 0.39177, 104, 46.56, 25.88, 0.60823, 2, 103, 119.35, 33.83, 0.94506, 104, 1.69, 35.87, 0.05494, 2, 102, 190.22, 30.16, 0.0101, 103, 85.54, 30, 0.9899, 2, 102, 152.89, 38.74, 0.22664, 103, 48.14, 38.29, 0.77336, 2, 102, 128.85, 44.26, 0.66931, 103, 24.05, 43.63, 0.33069, 3, 101, 238.29, -33.44, 0.01407, 102, 103.03, 50.2, 0.96516, 103, -1.81, 49.36, 0.02077, 2, 101, 222.98, -9.66, 0.23309, 102, 75.47, 56.53, 0.76692, 2, 101, 208.71, -10.1, 0.57113, 102, 65.56, 46.25, 0.42887, 2, 101, 186.1, -10.8, 0.90684, 102, 49.87, 29.96, 0.09316, 3, 100, 320.28, 3.68, 0.00109, 101, 175.89, -11.53, 0.96777, 102, 43.07, 22.31, 0.03115, 2, 100, 298.66, 0.92, 0.01992, 101, 154.15, -13.09, 0.98008, 2, 100, 275.6, -2.02, 0.10011, 101, 130.97, -14.76, 0.89989, 2, 100, 246.34, -5.75, 0.40223, 101, 101.54, -16.88, 0.59777, 2, 100, 210.41, -10.33, 0.89767, 101, 65.42, -19.47, 0.10233, 1, 100, 174.98, -14.84, 1, 1, 100, 142.34, -19, 1, 1, 100, 106.17, -23.61, 1, 1, 100, 51.94, 64.28, 1, 1, 100, 32.19, 100.91, 1, 1, 100, 34.85, 102.65, 1, 1, 100, 75.23, 101.3, 1, 1, 100, 104.78, 100.31, 1, 2, 100, 142.12, 99.06, 0.98308, 101, 3.24, 93.5, 0.01692, 2, 100, 183.94, 97.66, 0.79306, 101, 44.92, 89.81, 0.20694, 2, 100, 213.19, 96.68, 0.40641, 101, 74.08, 87.22, 0.59359, 2, 100, 249.38, 95.47, 0.02907, 101, 110.15, 84.02, 0.97093, 1, 101, 139, 81.46, 1, 2, 101, 171.07, 78.61, 0.99911, 102, -23.33, 83.47, 0.00089, 2, 101, 199.03, 69.17, 0.78695, 102, 3.28, 96.24, 0.21305, 2, 101, 228.64, 59.18, 0.00697, 102, 31.46, 109.76, 0.99303, 1, 102, 64.03, 113.08, 1, 2, 102, 105.23, 117.27, 0.93328, 103, -0.14, 116.45, 0.06672, 2, 102, 139.26, 120.74, 0.2713, 103, 33.86, 120.18, 0.7287, 2, 102, 164.29, 123.29, 0.01372, 103, 58.86, 122.93, 0.98628, 1, 103, 91.78, 126.55, 1, 2, 103, 123.97, 130.08, 0.93181, 104, 37.4, 125.37, 0.06819, 2, 103, 163.62, 134.44, 0.36301, 104, 76.31, 116.58, 0.63699, 2, 103, 197.81, 138.2, 0.03686, 104, 109.86, 109, 0.96314, 3, 103, 224.4, 149.97, 0.00026, 104, 138.84, 111.47, 0.98563, 105, 51.38, 96.84, 0.01411, 2, 104, 168.9, 114.03, 0.77742, 105, 79.37, 85.54, 0.22258, 2, 104, 187.76, 115.63, 0.43811, 105, 96.91, 78.46, 0.56189, 3, 104, 215.59, 127.71, 0.06539, 105, 127.2, 76.66, 0.90916, 106, 47.23, 52.89, 0.02545, 3, 104, 240.57, 138.55, 0.00193, 105, 154.38, 75.04, 0.80428, 106, 70.87, 39.39, 0.19379, 2, 105, 178.35, 73.62, 0.29298, 106, 91.72, 27.48, 0.70702, 2, 105, 188.74, 99.31, 0.00547, 106, 112.44, 45.89, 0.99453, 1, 106, 126.17, 58.1, 1, 1, 106, 139.01, 69.51, 1, 1, 106, 155.59, 76.25, 1, 1, 106, 172.5, 83.13, 1, 1, 106, 187.05, 89.05, 1, 1, 106, 200.21, 94.41, 1, 1, 106, 206.36, 96.91, 1, 1, 106, 214.97, 87.23, 1, 1, 106, 218.71, 67.82, 1, 1, 106, 225.19, 34.23, 1, 1, 100, 104.28, 53.5, 1, 1, 100, 121.04, 25.15, 1, 2, 100, 138.02, 50.33, 0.99942, 101, -3.52, 45.07, 0.00058, 1, 100, 153.03, 23.77, 1, 2, 100, 173.59, 52.45, 0.97848, 101, 32.1, 45.23, 0.02152, 1, 100, 190.87, 21.51, 1, 2, 100, 207.93, 55.53, 0.74555, 101, 66.57, 46.42, 0.25445, 2, 100, 228.58, 20.34, 0.58509, 101, 85.25, 10.15, 0.41491, 1, 101, 97.58, 42.79, 1, 2, 100, 261.39, 19.49, 0.12637, 101, 117.96, 7.5, 0.87363, 1, 101, 128.63, 45.97, 1, 2, 100, 287.57, 22.52, 0.02215, 101, 144.27, 9.08, 0.97785, 1, 101, 156.85, 41.29, 1, 3, 100, 311.83, 24.88, 0.00117, 101, 168.62, 10.1, 0.9981, 102, 22.76, 32.72, 0.00073, 2, 101, 183.53, 40.26, 0.99806, 102, 12.38, 64.71, 0.00194, 2, 101, 187.94, 7.08, 0.92641, 102, 38.7, 44.05, 0.07359, 2, 101, 209.02, 33.43, 0.74068, 102, 35.39, 77.62, 0.25932, 2, 101, 207.78, 10.04, 0.63462, 102, 50.84, 60.02, 0.36538, 1, 102, 58.67, 91.48, 1, 2, 101, 236.62, 2.31, 0.07086, 102, 76.88, 74.62, 0.92914, 1, 102, 89.54, 94.2, 1, 2, 101, 255.82, -17.68, 0.00469, 102, 104.58, 73.72, 0.99531, 2, 102, 124.4, 93.1, 0.96932, 103, 19.22, 92.42, 0.03068, 2, 102, 126.99, 70.06, 0.88228, 103, 21.99, 69.4, 0.11772, 1, 103, 48.87, 91.88, 1, 2, 102, 153.85, 67.42, 0.14178, 103, 48.87, 66.98, 0.85822, 1, 103, 75.98, 91.66, 1, 2, 102, 183.56, 65.49, 0.00746, 103, 78.6, 65.29, 0.99254, 1, 103, 109.98, 91.82, 1, 2, 103, 113.36, 64.1, 0.98813, 104, 5.89, 66.44, 0.01187, 1, 103, 143.82, 92.77, 1, 2, 103, 156.11, 68.67, 0.55268, 104, 47.79, 56.84, 0.44732, 2, 103, 178.94, 95.65, 0.02968, 104, 78.16, 74.91, 0.97032, 2, 103, 192.57, 72.31, 0.00318, 104, 83.44, 48.41, 0.99682, 1, 104, 111.83, 70.03, 1, 1, 104, 116.18, 40.21, 1, 2, 104, 147.75, 78.16, 0.99935, 105, 44.29, 63.09, 0.00065, 2, 104, 153.85, 32.16, 0.93738, 105, 28.96, 19.3, 0.06262, 2, 104, 180.91, 82.87, 0.76862, 105, 76.01, 52.32, 0.23138, 2, 104, 188.85, 28.58, 0.5758, 105, 58.57, 0.29, 0.4242, 2, 104, 204.79, 88.19, 0.11308, 105, 99.71, 46.28, 0.88692, 2, 104, 225.89, 26.69, 0.13065, 105, 90.76, -18.11, 0.86935, 3, 104, 237.12, 100.08, 0.00296, 105, 133.93, 42.29, 0.99013, 106, 38.01, 19.11, 0.00691, 2, 104, 262.08, 31.99, 0.00912, 105, 125.44, -29.74, 0.99088, 2, 105, 160.16, 42.21, 0.95197, 106, 61.48, 7.4, 0.04803, 1, 105, 149.67, -40.49, 1, 2, 105, 182.41, 44.09, 0.63761, 106, 82.25, -0.79, 0.36239, 2, 105, 184.78, -2.41, 0.94022, 106, 63.75, -43.51, 0.05978, 2, 105, 198.94, 62.36, 0.01436, 106, 105.17, 8.25, 0.98564, 2, 105, 204.09, 11.57, 0.69138, 106, 87.25, -39.55, 0.30862, 1, 106, 121.02, 4.22, 1, 1, 106, 139.37, 25.8, 1, 2, 105, 236.98, 52.17, 0.00065, 106, 134.75, -17.76, 0.99935, 1, 106, 155.66, 37.36, 1, 1, 106, 157.2, -2.69, 1, 1, 106, 172.64, 49.88, 1, 1, 106, 172.64, 6.64, 1, 1, 106, 189.1, 58.06, 1, 1, 106, 190.02, 15.58, 1, 1, 106, 203.65, 65.68, 1, 1, 106, 203.37, 23.38, 1, 1, 106, 212.36, 69.67, 1, 1, 106, 213.64, 30.08, 1], "hull": 73, "edges": [14, 16, 68, 70, 70, 72, 72, 74, 138, 140, 74, 76, 76, 146, 146, 148, 66, 68, 148, 66, 76, 78, 78, 150, 150, 152, 64, 66, 152, 64, 78, 80, 80, 154, 154, 156, 62, 64, 156, 62, 80, 82, 82, 158, 158, 160, 60, 62, 160, 60, 82, 84, 84, 162, 162, 164, 58, 60, 164, 58, 84, 86, 86, 166, 166, 168, 56, 58, 168, 56, 86, 88, 88, 90, 88, 170, 170, 172, 52, 54, 54, 56, 172, 54, 90, 174, 174, 176, 176, 52, 90, 92, 92, 94, 92, 178, 178, 180, 48, 50, 50, 52, 180, 50, 94, 182, 182, 184, 184, 48, 94, 96, 96, 186, 186, 188, 46, 48, 188, 46, 96, 98, 98, 190, 190, 192, 44, 46, 192, 44, 98, 100, 100, 194, 194, 196, 40, 42, 42, 44, 196, 42, 100, 102, 102, 198, 198, 200, 200, 40, 102, 104, 104, 202, 202, 204, 38, 40, 204, 38, 104, 106, 106, 206, 206, 208, 36, 38, 208, 36, 106, 108, 108, 110, 108, 210, 210, 212, 34, 36, 212, 34, 110, 214, 214, 216, 110, 112, 112, 218, 218, 220, 28, 30, 220, 30, 30, 32, 32, 34, 216, 32, 112, 114, 114, 116, 114, 222, 222, 224, 224, 28, 116, 226, 226, 228, 24, 26, 26, 28, 228, 26, 116, 118, 118, 230, 230, 232, 118, 120, 120, 122, 120, 234, 234, 236, 20, 22, 22, 24, 236, 22, 122, 238, 238, 240, 240, 20, 122, 124, 124, 242, 242, 244, 16, 18, 18, 20, 244, 18, 124, 126, 126, 128, 126, 246, 246, 14, 128, 248, 248, 250, 12, 14, 250, 12, 128, 130, 130, 252, 252, 254, 10, 12, 254, 10, 130, 132, 132, 256, 256, 258, 6, 8, 8, 10, 258, 8, 132, 134, 134, 260, 260, 262, 262, 6, 134, 136, 136, 138, 136, 264, 264, 266, 266, 2, 140, 268, 268, 270, 2, 0, 0, 144, 270, 0, 140, 142, 142, 144, 2, 4, 4, 6], "width": 241, "height": 225}}, "x2": {"x1": {"type": "mesh", "uvs": [0.85433, 0.01694, 0.87554, 0.03461, 0.90185, 0.05652, 0.92696, 0.07743, 0.92603, 0.09129, 0.92407, 0.12063, 0.92173, 0.15561, 0.91946, 0.18953, 0.95478, 0.20274, 0.97534, 0.25381, 0.99566, 0.30429, 0.99776, 0.36723, 0.99917, 0.40924, 0.94459, 0.44255, 0.89331, 0.47384, 0.82597, 0.50258, 0.77725, 0.52336, 0.73405, 0.5418, 0.67284, 0.56791, 0.60939, 0.59499, 0.56241, 0.61503, 0.50556, 0.61797, 0.46894, 0.61987, 0.42963, 0.6219, 0.38766, 0.62407, 0.37762, 0.64411, 0.36172, 0.67585, 0.35508, 0.6905, 0.34094, 0.72172, 0.32586, 0.75502, 0.30671, 0.79728, 0.28321, 0.84916, 0.26003, 0.90033, 0.23868, 0.94746, 0.21502, 0.9997, 0.06162, 0.99543, 0, 0.98987, 0, 0.9848, 0.03449, 0.93215, 0.05974, 0.89362, 0.09164, 0.84493, 0.12737, 0.79041, 0.15237, 0.75226, 0.18328, 0.70507, 0.20801, 0.66733, 0.2355, 0.62537, 0.26843, 0.59433, 0.3033, 0.56146, 0.34854, 0.54233, 0.4058, 0.51813, 0.45308, 0.49815, 0.48784, 0.48345, 0.53362, 0.4641, 0.57838, 0.44518, 0.63352, 0.42188, 0.68105, 0.40178, 0.71458, 0.37255, 0.74936, 0.34223, 0.77117, 0.32321, 0.79525, 0.28237, 0.81686, 0.24571, 0.83591, 0.21339, 0.80995, 0.17914, 0.79273, 0.15642, 0.77665, 0.1352, 0.76773, 0.10834, 0.75863, 0.08095, 0.7508, 0.05739, 0.74372, 0.03607, 0.74041, 0.02611, 0.75534, 0.01311, 0.78442, 0.00853, 0.83471, 0.0006, 0.11756, 0.93505, 0.16644, 0.9374, 0.14893, 0.89283, 0.19417, 0.89596, 0.1752, 0.84358, 0.22773, 0.84749, 0.19928, 0.7951, 0.25983, 0.79823, 0.227, 0.75523, 0.28755, 0.75523, 0.24597, 0.70988, 0.30506, 0.7177, 0.27296, 0.6747, 0.32184, 0.6833, 0.29412, 0.63873, 0.34008, 0.65906, 0.32184, 0.60902, 0.35103, 0.62935, 0.34957, 0.57775, 0.3824, 0.59573, 0.39263, 0.56026, 0.42238, 0.58519, 0.44293, 0.54693, 0.45592, 0.58113, 0.48566, 0.53534, 0.4954, 0.57359, 0.5246, 0.52433, 0.53867, 0.56374, 0.57328, 0.50984, 0.58897, 0.55099, 0.62142, 0.49419, 0.64846, 0.52606, 0.67064, 0.47506, 0.6993, 0.5052, 0.71607, 0.45129, 0.74636, 0.48665, 0.75339, 0.4084, 0.79936, 0.46347, 0.79017, 0.37247, 0.84534, 0.4368, 0.81505, 0.34406, 0.89239, 0.40608, 0.84479, 0.29943, 0.93241, 0.36666, 0.86373, 0.26291, 0.96378, 0.34116, 0.87725, 0.23045, 0.93945, 0.26291, 0.86535, 0.19336, 0.93512, 0.22524, 0.87238, 0.16843, 0.84155, 0.13771, 0.90591, 0.14815, 0.82547, 0.11098, 0.88503, 0.11136, 0.808, 0.08308, 0.87219, 0.08613, 0.79694, 0.05633, 0.86006, 0.05785, 0.7866, 0.03263, 0.84936, 0.03607, 0.78125, 0.01849, 0.84009, 0.01926], "triangles": [134, 70, 71, 135, 72, 0, 71, 72, 135, 134, 71, 135, 132, 134, 135, 133, 135, 0, 133, 0, 1, 132, 135, 133, 68, 69, 70, 68, 70, 134, 68, 134, 132, 130, 132, 133, 67, 68, 132, 67, 132, 130, 131, 133, 1, 131, 1, 2, 130, 133, 131, 131, 2, 3, 66, 67, 130, 128, 130, 131, 66, 130, 128, 129, 131, 3, 128, 131, 129, 4, 129, 3, 65, 66, 128, 126, 128, 129, 65, 128, 126, 127, 129, 4, 126, 129, 127, 5, 127, 4, 64, 65, 126, 124, 126, 127, 64, 126, 124, 125, 127, 5, 124, 127, 125, 6, 125, 5, 63, 64, 124, 123, 124, 125, 63, 124, 123, 62, 63, 123, 7, 125, 6, 123, 125, 7, 121, 62, 123, 121, 123, 7, 61, 62, 121, 121, 7, 122, 119, 121, 122, 61, 121, 119, 122, 7, 8, 122, 8, 9, 120, 122, 9, 119, 122, 120, 117, 61, 119, 117, 119, 120, 60, 61, 117, 115, 60, 117, 59, 60, 115, 120, 9, 10, 118, 120, 10, 117, 120, 118, 113, 59, 115, 58, 59, 113, 116, 117, 118, 115, 117, 116, 114, 113, 115, 118, 10, 11, 111, 58, 113, 116, 114, 115, 112, 113, 114, 111, 113, 112, 116, 12, 13, 11, 116, 118, 12, 116, 11, 114, 116, 13, 14, 114, 13, 112, 114, 14, 57, 58, 111, 109, 57, 111, 56, 57, 109, 107, 56, 109, 55, 56, 107, 110, 111, 112, 109, 111, 110, 108, 107, 109, 105, 54, 55, 105, 55, 107, 110, 108, 109, 103, 54, 105, 15, 110, 112, 15, 112, 14, 106, 105, 107, 106, 107, 108, 16, 108, 110, 16, 110, 15, 104, 105, 106, 17, 106, 108, 17, 108, 16, 18, 104, 106, 18, 106, 17, 103, 53, 54, 101, 52, 53, 101, 53, 103, 99, 51, 52, 99, 52, 101, 104, 103, 105, 97, 50, 51, 97, 51, 99, 95, 50, 97, 102, 101, 103, 102, 103, 104, 100, 99, 101, 100, 101, 102, 98, 97, 99, 98, 99, 100, 96, 97, 98, 19, 102, 104, 19, 104, 18, 20, 100, 102, 20, 102, 19, 21, 98, 100, 21, 100, 20, 22, 98, 21, 95, 49, 50, 93, 48, 49, 93, 49, 95, 91, 48, 93, 47, 48, 91, 96, 95, 97, 94, 93, 95, 94, 95, 96, 92, 91, 93, 92, 93, 94, 89, 47, 91, 22, 96, 98, 23, 94, 96, 23, 96, 22, 24, 92, 94, 23, 24, 94, 90, 91, 92, 90, 92, 24, 89, 91, 90, 25, 90, 24, 46, 47, 89, 87, 46, 89, 45, 46, 87, 88, 89, 90, 88, 90, 25, 87, 89, 88, 85, 45, 87, 44, 45, 85, 26, 88, 25, 86, 87, 88, 85, 87, 86, 27, 88, 26, 86, 88, 27, 83, 44, 85, 43, 44, 83, 84, 85, 86, 83, 85, 84, 28, 86, 27, 84, 86, 28, 29, 84, 28, 82, 83, 84, 82, 84, 29, 81, 43, 83, 81, 83, 82, 42, 43, 81, 79, 42, 81, 30, 82, 29, 80, 81, 82, 80, 82, 30, 79, 81, 80, 41, 42, 79, 77, 41, 79, 40, 41, 77, 78, 79, 80, 77, 79, 78, 31, 80, 30, 78, 80, 31, 75, 40, 77, 39, 40, 75, 76, 77, 78, 75, 77, 76, 32, 78, 31, 76, 78, 32, 73, 39, 75, 38, 39, 73, 74, 75, 76, 73, 75, 74, 33, 76, 32, 74, 76, 33, 35, 36, 37, 35, 38, 73, 35, 37, 38, 34, 74, 33, 73, 74, 34, 35, 73, 34], "vertices": [1, 148, 200.35, 32.11, 1, 1, 148, 189.9, 17.36, 1, 1, 148, 176.94, -0.94, 1, 1, 148, 164.57, -18.4, 1, 1, 148, 155.85, -18.17, 1, 1, 148, 137.39, -17.67, 1, 2, 147, 219.31, 44.19, 0.00749, 148, 115.37, -17.07, 0.99251, 2, 147, 199.92, 35.24, 0.20254, 148, 94.02, -16.49, 0.79746, 2, 147, 204.14, 10.42, 0.6241, 148, 86.79, -40.6, 0.3759, 2, 147, 182.74, -17.18, 0.92256, 148, 55.36, -55.85, 0.07744, 2, 147, 161.59, -44.47, 0.99835, 148, 24.3, -70.92, 0.00165, 1, 147, 127.67, -64.81, 1, 2, 146, 265.83, -20.63, 0.00498, 147, 105.02, -78.38, 0.99502, 2, 146, 223.7, -17.24, 0.08644, 147, 68.97, -56.33, 0.91356, 2, 146, 184.12, -14.05, 0.3748, 147, 35.08, -35.6, 0.6252, 2, 146, 136.5, -3.45, 0.86118, 147, -2.61, -4.64, 0.13882, 2, 146, 102.05, 4.22, 0.99138, 147, -29.88, 17.77, 0.00862, 2, 145, 193.46, 33.07, 0.00811, 146, 71.51, 11.02, 0.99189, 2, 145, 149.4, 28.08, 0.39177, 146, 28.23, 20.65, 0.60823, 2, 145, 103.73, 22.92, 0.94506, 146, -16.64, 30.64, 0.05494, 2, 144, 174.52, 19.37, 0.0101, 145, 69.91, 19.09, 0.9899, 2, 144, 137.18, 27.95, 0.22664, 145, 32.51, 27.38, 0.77336, 2, 144, 113.14, 33.47, 0.66931, 145, 8.43, 32.71, 0.33069, 3, 143, 219.51, -30.19, 0.01407, 144, 87.32, 39.41, 0.96516, 145, -17.43, 38.44, 0.02077, 2, 143, 204.2, -6.41, 0.23309, 144, 59.76, 45.74, 0.76692, 2, 143, 189.93, -6.85, 0.57113, 144, 49.85, 35.46, 0.42887, 2, 143, 167.32, -7.55, 0.90684, 144, 34.16, 19.17, 0.09316, 3, 142, 301.35, 5.88, 0.00109, 143, 157.11, -8.29, 0.96777, 144, 27.36, 11.52, 0.03115, 2, 142, 279.73, 3.13, 0.01992, 143, 135.37, -9.85, 0.98008, 2, 142, 256.67, 0.19, 0.10011, 143, 112.19, -11.52, 0.89989, 2, 142, 227.41, -3.54, 0.40223, 143, 82.76, -13.63, 0.59777, 2, 142, 191.48, -8.12, 0.89767, 143, 46.64, -16.23, 0.10233, 1, 142, 156.05, -12.64, 1, 1, 142, 123.41, -16.8, 1, 1, 142, 87.24, -21.41, 1, 1, 142, 33.01, 66.49, 1, 1, 142, 13.26, 103.11, 1, 1, 142, 15.92, 104.86, 1, 1, 142, 56.3, 103.5, 1, 1, 142, 85.85, 102.52, 1, 2, 142, 123.19, 101.27, 0.98308, 143, -15.54, 96.75, 0.01692, 2, 142, 165.01, 99.87, 0.79306, 143, 26.14, 93.05, 0.20694, 2, 142, 194.26, 98.89, 0.40641, 143, 55.3, 90.46, 0.59359, 2, 142, 230.45, 97.67, 0.02907, 143, 91.37, 87.26, 0.97093, 1, 143, 120.21, 84.7, 1, 2, 143, 152.29, 81.86, 0.99911, 144, -39.04, 72.68, 0.00089, 2, 143, 180.25, 72.42, 0.78695, 144, -12.43, 85.45, 0.21305, 2, 143, 209.86, 62.42, 0.00697, 144, 15.75, 98.97, 0.99303, 1, 144, 48.32, 102.29, 1, 2, 144, 89.52, 106.48, 0.93328, 145, -15.76, 105.53, 0.06672, 2, 144, 123.55, 109.95, 0.2713, 145, 18.24, 109.27, 0.7287, 2, 144, 148.58, 112.5, 0.01372, 145, 43.24, 112.02, 0.98628, 1, 145, 76.16, 115.63, 1, 2, 145, 108.35, 119.17, 0.93181, 146, 19.07, 120.14, 0.06819, 2, 145, 148, 123.53, 0.36301, 146, 57.98, 111.35, 0.63699, 2, 145, 182.18, 127.28, 0.03686, 146, 91.53, 103.77, 0.96314, 3, 145, 208.78, 139.05, 0.00026, 146, 120.51, 106.23, 0.98563, 147, 32.67, 100.45, 0.01411, 2, 146, 150.58, 108.8, 0.77742, 147, 60.65, 89.15, 0.22258, 2, 146, 169.43, 110.4, 0.43811, 147, 78.2, 82.07, 0.56189, 3, 146, 197.27, 122.48, 0.06539, 147, 108.49, 80.27, 0.90916, 148, 32.07, 64.43, 0.02545, 3, 146, 222.25, 133.32, 0.00193, 147, 135.67, 78.66, 0.80428, 148, 55.71, 50.93, 0.19379, 2, 147, 159.64, 77.23, 0.29298, 148, 76.55, 39.02, 0.70702, 2, 147, 170.03, 102.92, 0.00547, 148, 97.27, 57.43, 0.99453, 1, 148, 111.01, 69.64, 1, 1, 148, 123.84, 81.05, 1, 1, 148, 140.42, 87.79, 1, 1, 148, 157.33, 94.67, 1, 1, 148, 171.88, 100.59, 1, 1, 148, 185.05, 105.95, 1, 1, 148, 191.2, 108.45, 1, 1, 148, 199.8, 98.77, 1, 1, 148, 203.54, 79.36, 1, 1, 148, 210.02, 45.77, 1, 1, 142, 85.35, 55.71, 1, 1, 142, 102.11, 27.36, 1, 2, 142, 119.09, 52.53, 0.99942, 143, -22.3, 48.32, 0.00058, 1, 142, 134.1, 25.97, 1, 2, 142, 154.66, 54.65, 0.97848, 143, 13.32, 48.48, 0.02152, 1, 142, 171.94, 23.72, 1, 2, 142, 189, 57.74, 0.74555, 143, 47.79, 49.67, 0.25445, 2, 142, 209.65, 22.55, 0.58509, 143, 66.47, 13.4, 0.41491, 1, 143, 78.8, 46.03, 1, 2, 142, 242.46, 21.7, 0.12637, 143, 99.18, 10.74, 0.87363, 1, 143, 109.85, 49.22, 1, 2, 142, 268.64, 24.72, 0.02215, 143, 125.48, 12.32, 0.97785, 1, 143, 138.07, 44.54, 1, 3, 142, 292.9, 27.09, 0.00117, 143, 149.84, 13.35, 0.9981, 144, 7.05, 21.93, 0.00073, 2, 143, 164.75, 43.5, 0.99806, 144, -3.33, 53.92, 0.00194, 2, 143, 169.16, 10.33, 0.92641, 144, 22.99, 33.26, 0.07359, 2, 143, 190.23, 36.67, 0.74068, 144, 19.68, 66.83, 0.25932, 2, 143, 189, 13.28, 0.63462, 144, 35.13, 49.23, 0.36538, 1, 144, 42.96, 80.69, 1, 2, 143, 217.84, 5.56, 0.07086, 144, 61.17, 63.83, 0.92914, 1, 144, 73.83, 83.41, 1, 2, 143, 237.04, -14.44, 0.00469, 144, 88.87, 62.93, 0.99531, 2, 144, 108.69, 82.31, 0.96932, 145, 3.59, 81.51, 0.03068, 2, 144, 111.28, 59.27, 0.88228, 145, 6.37, 58.49, 0.11772, 1, 145, 33.25, 80.96, 1, 2, 144, 138.14, 56.63, 0.14178, 145, 33.25, 56.06, 0.85822, 1, 145, 60.35, 80.74, 1, 2, 144, 167.85, 54.7, 0.00746, 145, 62.97, 54.37, 0.99254, 1, 145, 94.35, 80.9, 1, 2, 145, 97.74, 53.19, 0.98813, 146, -12.44, 61.21, 0.01187, 1, 145, 128.19, 81.86, 1, 2, 145, 140.49, 57.76, 0.55268, 146, 29.46, 51.61, 0.44732, 2, 145, 163.31, 84.73, 0.02968, 146, 59.83, 69.68, 0.97032, 2, 145, 176.94, 61.39, 0.00318, 146, 65.12, 43.18, 0.99682, 1, 146, 93.5, 64.8, 1, 1, 146, 97.85, 34.98, 1, 2, 146, 129.42, 72.92, 0.99935, 147, 25.58, 66.7, 0.00065, 2, 146, 135.53, 26.93, 0.93738, 147, 10.25, 22.91, 0.06262, 2, 146, 162.59, 77.64, 0.76862, 147, 57.3, 55.93, 0.23138, 2, 146, 170.53, 23.35, 0.5758, 147, 39.85, 3.91, 0.4242, 2, 146, 186.47, 82.96, 0.11308, 147, 81, 49.89, 0.88692, 2, 146, 207.56, 21.46, 0.13065, 147, 72.04, -14.5, 0.86935, 3, 146, 218.79, 94.85, 0.00296, 147, 115.22, 45.9, 0.99013, 148, 22.84, 30.65, 0.00691, 2, 146, 243.76, 26.76, 0.00912, 147, 106.73, -26.13, 0.99088, 2, 147, 141.45, 45.82, 0.95197, 148, 46.32, 18.94, 0.04803, 1, 147, 130.95, -36.88, 1, 2, 147, 163.69, 47.7, 0.63761, 148, 67.09, 10.75, 0.36239, 2, 147, 166.07, 1.2, 0.94022, 148, 48.58, -31.97, 0.05978, 2, 147, 180.22, 65.97, 0.01436, 148, 90, 19.79, 0.98564, 2, 147, 185.38, 15.18, 0.69138, 148, 72.09, -28.01, 0.30862, 1, 148, 105.85, 15.76, 1, 1, 148, 124.2, 37.34, 1, 2, 147, 218.27, 55.78, 0.00065, 148, 119.58, -6.22, 0.99935, 1, 148, 140.5, 48.9, 1, 1, 148, 142.04, 8.84, 1, 1, 148, 157.48, 61.42, 1, 1, 148, 157.48, 18.18, 1, 1, 148, 173.93, 69.6, 1, 1, 148, 174.86, 27.12, 1, 1, 148, 188.49, 77.21, 1, 1, 148, 188.2, 34.92, 1, 1, 148, 197.2, 81.21, 1, 1, 148, 198.48, 41.62, 1], "hull": 73, "edges": [14, 16, 68, 70, 70, 72, 72, 74, 138, 140, 74, 76, 76, 146, 146, 148, 66, 68, 148, 66, 76, 78, 78, 150, 150, 152, 64, 66, 152, 64, 78, 80, 80, 154, 154, 156, 62, 64, 156, 62, 80, 82, 82, 158, 158, 160, 60, 62, 160, 60, 82, 84, 84, 162, 162, 164, 58, 60, 164, 58, 84, 86, 86, 166, 166, 168, 56, 58, 168, 56, 86, 88, 88, 90, 88, 170, 170, 172, 52, 54, 54, 56, 172, 54, 90, 174, 174, 176, 176, 52, 90, 92, 92, 94, 92, 178, 178, 180, 48, 50, 50, 52, 180, 50, 94, 182, 182, 184, 184, 48, 94, 96, 96, 186, 186, 188, 46, 48, 188, 46, 96, 98, 98, 190, 190, 192, 44, 46, 192, 44, 98, 100, 100, 194, 194, 196, 40, 42, 42, 44, 196, 42, 100, 102, 102, 198, 198, 200, 200, 40, 102, 104, 104, 202, 202, 204, 38, 40, 204, 38, 104, 106, 106, 206, 206, 208, 36, 38, 208, 36, 106, 108, 108, 110, 108, 210, 210, 212, 34, 36, 212, 34, 110, 214, 214, 216, 110, 112, 112, 218, 218, 220, 28, 30, 220, 30, 30, 32, 32, 34, 216, 32, 112, 114, 114, 116, 114, 222, 222, 224, 224, 28, 116, 226, 226, 228, 24, 26, 26, 28, 228, 26, 116, 118, 118, 230, 230, 232, 118, 120, 120, 122, 120, 234, 234, 236, 20, 22, 22, 24, 236, 22, 122, 238, 238, 240, 240, 20, 122, 124, 124, 242, 242, 244, 16, 18, 18, 20, 244, 18, 124, 126, 126, 128, 126, 246, 246, 14, 128, 248, 248, 250, 12, 14, 250, 12, 128, 130, 130, 252, 252, 254, 10, 12, 254, 10, 130, 132, 132, 256, 256, 258, 6, 8, 8, 10, 258, 8, 132, 134, 134, 260, 260, 262, 262, 6, 134, 136, 136, 138, 136, 264, 264, 266, 266, 2, 140, 268, 268, 270, 2, 0, 0, 144, 270, 0, 140, 142, 142, 144, 2, 4, 4, 6], "width": 241, "height": 225}}, "x3": {"x1": {"type": "mesh", "uvs": [0.85433, 0.01694, 0.87554, 0.03461, 0.90185, 0.05652, 0.92696, 0.07743, 0.92603, 0.09129, 0.92407, 0.12063, 0.92173, 0.15561, 0.91946, 0.18953, 0.95478, 0.20274, 0.97534, 0.25381, 0.99566, 0.30429, 0.99776, 0.36723, 0.99917, 0.40924, 0.94459, 0.44255, 0.89331, 0.47384, 0.82597, 0.50258, 0.77725, 0.52336, 0.73405, 0.5418, 0.67284, 0.56791, 0.60939, 0.59499, 0.56241, 0.61503, 0.50556, 0.61797, 0.46894, 0.61987, 0.42963, 0.6219, 0.38766, 0.62407, 0.37762, 0.64411, 0.36172, 0.67585, 0.35508, 0.6905, 0.34094, 0.72172, 0.32586, 0.75502, 0.30671, 0.79728, 0.28321, 0.84916, 0.26003, 0.90033, 0.23868, 0.94746, 0.21502, 0.9997, 0.06162, 0.99543, 0, 0.98987, 0, 0.9848, 0.03449, 0.93215, 0.05974, 0.89362, 0.09164, 0.84493, 0.12737, 0.79041, 0.15237, 0.75226, 0.18328, 0.70507, 0.20801, 0.66733, 0.2355, 0.62537, 0.26843, 0.59433, 0.3033, 0.56146, 0.34854, 0.54233, 0.4058, 0.51813, 0.45308, 0.49815, 0.48784, 0.48345, 0.53362, 0.4641, 0.57838, 0.44518, 0.63352, 0.42188, 0.68105, 0.40178, 0.71458, 0.37255, 0.74936, 0.34223, 0.77117, 0.32321, 0.79525, 0.28237, 0.81686, 0.24571, 0.83591, 0.21339, 0.80995, 0.17914, 0.79273, 0.15642, 0.77665, 0.1352, 0.76773, 0.10834, 0.75863, 0.08095, 0.7508, 0.05739, 0.74372, 0.03607, 0.74041, 0.02611, 0.75534, 0.01311, 0.78442, 0.00853, 0.83471, 0.0006, 0.11756, 0.93505, 0.16644, 0.9374, 0.14893, 0.89283, 0.19417, 0.89596, 0.1752, 0.84358, 0.22773, 0.84749, 0.19928, 0.7951, 0.25983, 0.79823, 0.227, 0.75523, 0.28755, 0.75523, 0.24597, 0.70988, 0.30506, 0.7177, 0.27296, 0.6747, 0.32184, 0.6833, 0.29412, 0.63873, 0.34008, 0.65906, 0.32184, 0.60902, 0.35103, 0.62935, 0.34957, 0.57775, 0.3824, 0.59573, 0.39263, 0.56026, 0.42238, 0.58519, 0.44293, 0.54693, 0.45592, 0.58113, 0.48566, 0.53534, 0.4954, 0.57359, 0.5246, 0.52433, 0.53867, 0.56374, 0.57328, 0.50984, 0.58897, 0.55099, 0.62142, 0.49419, 0.64846, 0.52606, 0.67064, 0.47506, 0.6993, 0.5052, 0.71607, 0.45129, 0.74636, 0.48665, 0.75339, 0.4084, 0.79936, 0.46347, 0.79017, 0.37247, 0.84534, 0.4368, 0.81505, 0.34406, 0.89239, 0.40608, 0.84479, 0.29943, 0.93241, 0.36666, 0.86373, 0.26291, 0.96378, 0.34116, 0.87725, 0.23045, 0.93945, 0.26291, 0.86535, 0.19336, 0.93512, 0.22524, 0.87238, 0.16843, 0.84155, 0.13771, 0.90591, 0.14815, 0.82547, 0.11098, 0.88503, 0.11136, 0.808, 0.08308, 0.87219, 0.08613, 0.79694, 0.05633, 0.86006, 0.05785, 0.7866, 0.03263, 0.84936, 0.03607, 0.78125, 0.01849, 0.84009, 0.01926], "triangles": [134, 70, 71, 135, 72, 0, 71, 72, 135, 134, 71, 135, 132, 134, 135, 133, 135, 0, 133, 0, 1, 132, 135, 133, 68, 69, 70, 68, 70, 134, 68, 134, 132, 130, 132, 133, 67, 68, 132, 67, 132, 130, 131, 133, 1, 131, 1, 2, 130, 133, 131, 131, 2, 3, 66, 67, 130, 128, 130, 131, 66, 130, 128, 129, 131, 3, 128, 131, 129, 4, 129, 3, 65, 66, 128, 126, 128, 129, 65, 128, 126, 127, 129, 4, 126, 129, 127, 5, 127, 4, 64, 65, 126, 124, 126, 127, 64, 126, 124, 125, 127, 5, 124, 127, 125, 6, 125, 5, 63, 64, 124, 123, 124, 125, 63, 124, 123, 62, 63, 123, 7, 125, 6, 123, 125, 7, 121, 62, 123, 121, 123, 7, 61, 62, 121, 121, 7, 122, 119, 121, 122, 61, 121, 119, 122, 7, 8, 122, 8, 9, 120, 122, 9, 119, 122, 120, 117, 61, 119, 117, 119, 120, 60, 61, 117, 115, 60, 117, 59, 60, 115, 120, 9, 10, 118, 120, 10, 117, 120, 118, 113, 59, 115, 58, 59, 113, 116, 117, 118, 115, 117, 116, 114, 113, 115, 118, 10, 11, 111, 58, 113, 116, 114, 115, 112, 113, 114, 111, 113, 112, 116, 12, 13, 11, 116, 118, 12, 116, 11, 114, 116, 13, 14, 114, 13, 112, 114, 14, 57, 58, 111, 109, 57, 111, 56, 57, 109, 107, 56, 109, 55, 56, 107, 110, 111, 112, 109, 111, 110, 108, 107, 109, 105, 54, 55, 105, 55, 107, 110, 108, 109, 103, 54, 105, 15, 110, 112, 15, 112, 14, 106, 105, 107, 106, 107, 108, 16, 108, 110, 16, 110, 15, 104, 105, 106, 17, 106, 108, 17, 108, 16, 18, 104, 106, 18, 106, 17, 103, 53, 54, 101, 52, 53, 101, 53, 103, 99, 51, 52, 99, 52, 101, 104, 103, 105, 97, 50, 51, 97, 51, 99, 95, 50, 97, 102, 101, 103, 102, 103, 104, 100, 99, 101, 100, 101, 102, 98, 97, 99, 98, 99, 100, 96, 97, 98, 19, 102, 104, 19, 104, 18, 20, 100, 102, 20, 102, 19, 21, 98, 100, 21, 100, 20, 22, 98, 21, 95, 49, 50, 93, 48, 49, 93, 49, 95, 91, 48, 93, 47, 48, 91, 96, 95, 97, 94, 93, 95, 94, 95, 96, 92, 91, 93, 92, 93, 94, 89, 47, 91, 22, 96, 98, 23, 94, 96, 23, 96, 22, 24, 92, 94, 23, 24, 94, 90, 91, 92, 90, 92, 24, 89, 91, 90, 25, 90, 24, 46, 47, 89, 87, 46, 89, 45, 46, 87, 88, 89, 90, 88, 90, 25, 87, 89, 88, 85, 45, 87, 44, 45, 85, 26, 88, 25, 86, 87, 88, 85, 87, 86, 27, 88, 26, 86, 88, 27, 83, 44, 85, 43, 44, 83, 84, 85, 86, 83, 85, 84, 28, 86, 27, 84, 86, 28, 29, 84, 28, 82, 83, 84, 82, 84, 29, 81, 43, 83, 81, 83, 82, 42, 43, 81, 79, 42, 81, 30, 82, 29, 80, 81, 82, 80, 82, 30, 79, 81, 80, 41, 42, 79, 77, 41, 79, 40, 41, 77, 78, 79, 80, 77, 79, 78, 31, 80, 30, 78, 80, 31, 75, 40, 77, 39, 40, 75, 76, 77, 78, 75, 77, 76, 32, 78, 31, 76, 78, 32, 73, 39, 75, 38, 39, 73, 74, 75, 76, 73, 75, 74, 33, 76, 32, 74, 76, 33, 35, 36, 37, 35, 38, 73, 35, 37, 38, 34, 74, 33, 73, 74, 34, 35, 73, 34], "vertices": [1, 156, 197.92, 32, 1, 1, 156, 187.47, 17.25, 1, 1, 156, 174.51, -1.04, 1, 1, 156, 162.14, -18.51, 1, 1, 156, 153.41, -18.27, 1, 1, 156, 134.95, -17.77, 1, 2, 155, 217.17, 43.02, 0.00749, 156, 112.93, -17.18, 0.99251, 2, 155, 197.79, 34.06, 0.20254, 156, 91.59, -16.6, 0.79746, 2, 155, 202, 9.25, 0.6241, 156, 84.35, -40.71, 0.3759, 2, 155, 180.6, -18.36, 0.92256, 156, 52.93, -55.96, 0.07744, 2, 155, 159.45, -45.65, 0.99835, 156, 21.86, -71.03, 0.00165, 1, 155, 125.53, -65.99, 1, 2, 154, 264.46, -22.65, 0.00498, 155, 102.89, -79.56, 0.99502, 2, 154, 222.33, -19.26, 0.08644, 155, 66.83, -57.5, 0.91356, 2, 154, 182.74, -16.07, 0.3748, 155, 32.95, -36.78, 0.6252, 2, 154, 135.13, -5.47, 0.86118, 155, -4.75, -5.81, 0.13882, 2, 154, 100.68, 2.2, 0.99138, 155, -32.01, 16.59, 0.00862, 2, 153, 192.82, 30.71, 0.00811, 154, 70.14, 9, 0.99189, 2, 153, 148.76, 25.73, 0.39177, 154, 26.86, 18.63, 0.60823, 2, 153, 103.09, 20.56, 0.94506, 154, -18.01, 28.62, 0.05494, 2, 152, 173.85, 17.02, 0.0101, 153, 69.27, 16.74, 0.9899, 2, 152, 136.52, 25.6, 0.22664, 153, 31.87, 25.02, 0.77336, 2, 152, 112.48, 31.13, 0.66931, 153, 7.79, 30.36, 0.33069, 3, 151, 217.4, -31.41, 0.01407, 152, 86.66, 37.06, 0.96516, 153, -18.08, 36.09, 0.02077, 2, 151, 202.09, -7.63, 0.23309, 152, 59.1, 43.39, 0.76692, 2, 151, 187.81, -8.07, 0.57113, 152, 49.19, 33.11, 0.42887, 2, 151, 165.2, -8.77, 0.90684, 152, 33.49, 16.82, 0.09316, 3, 150, 299.31, 4.55, 0.00109, 151, 155, -9.51, 0.96777, 152, 26.7, 9.17, 0.03115, 2, 150, 277.69, 1.79, 0.01992, 151, 133.26, -11.07, 0.98008, 2, 150, 254.63, -1.15, 0.10011, 151, 110.08, -12.74, 0.89989, 2, 150, 225.36, -4.88, 0.40223, 151, 80.65, -14.85, 0.59777, 2, 150, 189.44, -9.45, 0.89767, 151, 44.53, -17.45, 0.10233, 1, 150, 154, -13.97, 1, 1, 150, 121.37, -18.13, 1, 1, 150, 85.19, -22.74, 1, 1, 150, 30.97, 65.15, 1, 1, 150, 11.21, 101.78, 1, 1, 150, 13.88, 103.52, 1, 1, 150, 54.25, 102.17, 1, 1, 150, 83.8, 101.18, 1, 2, 150, 121.14, 99.93, 0.98308, 151, -17.65, 95.53, 0.01692, 2, 150, 162.96, 98.53, 0.79306, 151, 24.03, 91.83, 0.20694, 2, 150, 192.22, 97.55, 0.40641, 151, 53.19, 89.24, 0.59359, 2, 150, 228.41, 96.34, 0.02907, 151, 89.26, 86.04, 0.97093, 1, 151, 118.1, 83.48, 1, 2, 151, 150.17, 80.64, 0.99911, 152, -39.7, 70.33, 0.00089, 2, 151, 178.13, 71.2, 0.78695, 152, -13.09, 83.1, 0.21305, 2, 151, 207.75, 61.2, 0.00697, 152, 15.09, 96.62, 0.99303, 1, 152, 47.66, 99.94, 1, 2, 152, 88.86, 104.14, 0.93328, 153, -16.4, 103.18, 0.06672, 2, 152, 122.89, 107.6, 0.2713, 153, 17.6, 106.91, 0.7287, 2, 152, 147.92, 110.15, 0.01372, 153, 42.6, 109.66, 0.98628, 1, 153, 75.52, 113.28, 1, 2, 153, 107.7, 116.81, 0.93181, 154, 17.7, 118.13, 0.06819, 2, 153, 147.36, 121.17, 0.36301, 154, 56.61, 109.33, 0.63699, 2, 153, 181.54, 124.93, 0.03686, 154, 90.16, 101.75, 0.96314, 3, 153, 208.14, 136.7, 0.00026, 154, 119.14, 104.22, 0.98563, 155, 30.54, 99.27, 0.01411, 2, 154, 149.2, 106.78, 0.77742, 155, 58.52, 87.97, 0.22258, 2, 154, 168.06, 108.39, 0.43811, 155, 76.07, 80.89, 0.56189, 3, 154, 195.89, 120.46, 0.06539, 155, 106.35, 79.09, 0.90916, 156, 29.63, 64.33, 0.02545, 3, 154, 220.87, 131.3, 0.00193, 155, 133.53, 77.48, 0.80428, 156, 53.27, 50.82, 0.19379, 2, 155, 157.5, 76.05, 0.29298, 156, 74.12, 38.91, 0.70702, 2, 155, 167.89, 101.75, 0.00547, 156, 94.83, 57.32, 0.99453, 1, 156, 108.57, 69.53, 1, 1, 156, 121.4, 80.94, 1, 1, 156, 137.99, 87.68, 1, 1, 156, 154.9, 94.56, 1, 1, 156, 169.45, 100.48, 1, 1, 156, 182.61, 105.84, 1, 1, 156, 188.76, 108.34, 1, 1, 156, 197.36, 98.66, 1, 1, 156, 201.11, 79.25, 1, 1, 156, 207.59, 45.66, 1, 1, 150, 83.31, 54.37, 1, 1, 150, 100.07, 26.03, 1, 2, 150, 117.05, 51.2, 0.99942, 151, -24.42, 47.1, 0.00058, 1, 150, 132.06, 24.64, 1, 2, 150, 152.61, 53.32, 0.97848, 151, 11.21, 47.26, 0.02152, 1, 150, 169.9, 22.38, 1, 2, 150, 186.96, 56.4, 0.74555, 151, 45.67, 48.45, 0.25445, 2, 150, 207.61, 21.21, 0.58509, 151, 64.35, 12.17, 0.41491, 1, 151, 76.69, 44.81, 1, 2, 150, 240.42, 20.36, 0.12637, 151, 97.07, 9.52, 0.87363, 1, 151, 107.74, 48, 1, 2, 150, 266.6, 23.39, 0.02215, 151, 123.37, 11.1, 0.97785, 1, 151, 135.95, 43.32, 1, 3, 150, 290.86, 25.75, 0.00117, 151, 147.73, 12.13, 0.9981, 152, 6.39, 19.58, 0.00073, 2, 151, 162.63, 42.28, 0.99806, 152, -3.99, 51.57, 0.00194, 2, 151, 167.05, 9.11, 0.92641, 152, 22.33, 30.91, 0.07359, 2, 151, 188.12, 35.45, 0.74068, 152, 19.02, 64.48, 0.25932, 2, 151, 186.89, 12.06, 0.63462, 152, 34.47, 46.88, 0.36538, 1, 152, 42.3, 78.34, 1, 2, 151, 215.73, 4.34, 0.07086, 152, 60.51, 61.49, 0.92914, 1, 152, 73.17, 81.06, 1, 2, 151, 234.92, -15.66, 0.00469, 152, 88.21, 60.58, 0.99531, 2, 152, 108.03, 79.96, 0.96932, 153, 2.95, 79.15, 0.03068, 2, 152, 110.62, 56.92, 0.88228, 153, 5.73, 56.14, 0.11772, 1, 153, 32.61, 78.61, 1, 2, 152, 137.48, 54.28, 0.14178, 153, 32.61, 53.71, 0.85822, 1, 153, 59.71, 78.39, 1, 2, 152, 167.19, 52.35, 0.00746, 153, 62.33, 52.02, 0.99254, 1, 153, 93.71, 78.55, 1, 2, 153, 97.1, 50.84, 0.98813, 154, -13.81, 59.2, 0.01187, 1, 153, 127.55, 79.5, 1, 2, 153, 139.84, 55.4, 0.55268, 154, 28.09, 49.59, 0.44732, 2, 153, 162.67, 82.38, 0.02968, 154, 58.46, 67.66, 0.97032, 2, 153, 176.3, 59.04, 0.00318, 154, 63.74, 41.16, 0.99682, 1, 154, 92.13, 62.79, 1, 1, 154, 96.48, 32.96, 1, 2, 154, 128.05, 70.91, 0.99935, 155, 23.44, 65.52, 0.00065, 2, 154, 134.15, 24.91, 0.93738, 155, 8.11, 21.73, 0.06262, 2, 154, 161.21, 75.63, 0.76862, 155, 55.16, 54.75, 0.23138, 2, 154, 169.15, 21.33, 0.5758, 155, 37.72, 2.73, 0.4242, 2, 154, 185.09, 80.94, 0.11308, 155, 78.86, 48.71, 0.88692, 2, 154, 206.19, 19.45, 0.13065, 155, 69.91, -15.68, 0.86935, 3, 154, 217.42, 92.84, 0.00296, 155, 113.08, 44.72, 0.99013, 156, 20.4, 30.54, 0.00691, 2, 154, 242.38, 24.74, 0.00912, 155, 104.6, -27.31, 0.99088, 2, 155, 139.31, 44.64, 0.95197, 156, 43.88, 18.83, 0.04803, 1, 155, 128.82, -38.06, 1, 2, 155, 161.56, 46.52, 0.63761, 156, 64.65, 10.65, 0.36239, 2, 155, 163.93, 0.02, 0.94022, 156, 46.14, -32.08, 0.05978, 2, 155, 178.09, 64.79, 0.01436, 156, 87.57, 19.68, 0.98564, 2, 155, 183.24, 14, 0.69138, 156, 69.65, -28.12, 0.30862, 1, 156, 103.41, 15.65, 1, 1, 156, 121.76, 37.23, 1, 2, 155, 216.13, 54.6, 0.00065, 156, 117.14, -6.33, 0.99935, 1, 156, 138.06, 48.79, 1, 1, 156, 139.6, 8.74, 1, 1, 156, 155.04, 61.31, 1, 1, 156, 155.04, 18.07, 1, 1, 156, 171.49, 69.49, 1, 1, 156, 172.42, 27.01, 1, 1, 156, 186.05, 77.11, 1, 1, 156, 185.77, 34.81, 1, 1, 156, 194.76, 81.1, 1, 1, 156, 196.04, 41.51, 1], "hull": 73, "edges": [14, 16, 68, 70, 70, 72, 72, 74, 138, 140, 74, 76, 76, 146, 146, 148, 66, 68, 148, 66, 76, 78, 78, 150, 150, 152, 64, 66, 152, 64, 78, 80, 80, 154, 154, 156, 62, 64, 156, 62, 80, 82, 82, 158, 158, 160, 60, 62, 160, 60, 82, 84, 84, 162, 162, 164, 58, 60, 164, 58, 84, 86, 86, 166, 166, 168, 56, 58, 168, 56, 86, 88, 88, 90, 88, 170, 170, 172, 52, 54, 54, 56, 172, 54, 90, 174, 174, 176, 176, 52, 90, 92, 92, 94, 92, 178, 178, 180, 48, 50, 50, 52, 180, 50, 94, 182, 182, 184, 184, 48, 94, 96, 96, 186, 186, 188, 46, 48, 188, 46, 96, 98, 98, 190, 190, 192, 44, 46, 192, 44, 98, 100, 100, 194, 194, 196, 40, 42, 42, 44, 196, 42, 100, 102, 102, 198, 198, 200, 200, 40, 102, 104, 104, 202, 202, 204, 38, 40, 204, 38, 104, 106, 106, 206, 206, 208, 36, 38, 208, 36, 106, 108, 108, 110, 108, 210, 210, 212, 34, 36, 212, 34, 110, 214, 214, 216, 110, 112, 112, 218, 218, 220, 28, 30, 220, 30, 30, 32, 32, 34, 216, 32, 112, 114, 114, 116, 114, 222, 222, 224, 224, 28, 116, 226, 226, 228, 24, 26, 26, 28, 228, 26, 116, 118, 118, 230, 230, 232, 118, 120, 120, 122, 120, 234, 234, 236, 20, 22, 22, 24, 236, 22, 122, 238, 238, 240, 240, 20, 122, 124, 124, 242, 242, 244, 16, 18, 18, 20, 244, 18, 124, 126, 126, 128, 126, 246, 246, 14, 128, 248, 248, 250, 12, 14, 250, 12, 128, 130, 130, 252, 252, 254, 10, 12, 254, 10, 130, 132, 132, 256, 256, 258, 6, 8, 8, 10, 258, 8, 132, 134, 134, 260, 260, 262, 262, 6, 134, 136, 136, 138, 136, 264, 264, 266, 266, 2, 140, 268, 268, 270, 2, 0, 0, 144, 270, 0, 140, 142, 142, 144, 2, 4, 4, 6], "width": 241, "height": 225}}}}], "animations": {"animation1": {"slots": {"zs9": {"color": [{"color": "ffffff7f"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 2.5, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffff7f"}]}, "zs12": {"color": [{"color": "ffffffbf"}, {"time": 0.4, "color": "ffffffff"}, {"time": 2.0667, "color": "ffffff00"}, {"time": 3.3333, "color": "ffffffbf"}]}, "x1": {"color": [{"color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffff00"}]}, "zs10": {"color": [{"color": "ffffffff"}, {"time": 1.6667, "color": "ffffff00"}, {"time": 3.3333, "color": "ffffffff"}]}, "zs13": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "color": "ffffffff"}]}, "x3": {"color": [{"color": "ffffffbf"}, {"time": 0.4, "color": "ffffffff"}, {"time": 2.0667, "color": "ffffff00"}, {"time": 3.3333, "color": "ffffffbf"}]}, "zw3": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "color": "ffffff00"}]}, "zs8": {"color": [{"color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffff00"}]}, "zs4": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "color": "ffffff00"}]}, "zs3": {"color": [{"color": "ffffffff"}, {"time": 1.6667, "color": "ffffff00"}, {"time": 3.3333, "color": "ffffffff"}]}, "zs1": {"color": [{"color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffff00"}]}, "x2": {"color": [{"color": "ffffff9f"}, {"time": 1.0333, "color": "ffffff00"}, {"time": 2.7, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffff9f"}]}, "zs2": {"color": [{"color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffff00"}]}, "zs11": {"color": [{"color": "ffffff9f"}, {"time": 1.0333, "color": "ffffff00"}, {"time": 2.7, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffff9f"}]}, "zw4": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "color": "ffffffff"}]}, "zs5": {"color": [{"color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffff00"}]}}, "bones": {"bone": {"translate": [{"x": 0.02, "y": 15.38, "curve": 0.349, "c2": 0.35, "c3": 0.684, "c4": 0.69}, {"time": 0.1, "x": 0.02, "y": 12.14, "curve": 0.372, "c2": 0.38, "c3": 0.708, "c4": 0.72}, {"time": 0.2667, "x": 0.01, "y": 6.85, "curve": 0.356, "c2": 0.36, "c3": 0.69, "c4": 0.7}, {"time": 0.3333, "x": 0.01, "y": 4.8, "curve": 0.42, "c2": 0.46, "c3": 0.759, "c4": 0.8}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 0.06, "y": 38.4, "curve": 0.237, "c2": 0.16, "c3": 0.694, "c4": 0.67}, {"time": 3.3333, "x": 0.02, "y": 15.38}]}, "l1": {"rotate": [{"angle": -1.17, "curve": 0.347, "c2": 0.38, "c3": 0.685, "c4": 0.73}, {"time": 0.3333, "angle": -0.37, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "angle": -3.6, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.3333, "angle": -1.17}], "translate": [{"x": -7.41, "y": -4.21, "curve": 0.319, "c2": 0.29, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "x": -7.8, "y": -4.44, "curve": 0.332, "c2": 0.33, "c3": 0.668, "c4": 0.67}, {"time": 0.5333, "x": -8.06, "y": -4.59, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 1.3667, "x": -8.81, "y": -5.02, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "x": -7.05, "y": -4.01, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 3.3333, "x": -7.41, "y": -4.21}]}, "l2": {"translate": [{"x": -0.37, "y": -0.59, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "x": -4.37, "y": -3.79, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 3.3333, "x": -0.37, "y": -0.59}], "shear": [{"x": -0.37, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "x": -2.4, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 3.3333, "x": -0.37}]}, "f1": {"translate": [{"x": -0.1, "y": -0.42, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "x": -0.66, "y": -2.75, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 3.3333, "x": -0.1, "y": -0.42}], "scale": [{"x": 0.999, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.94, "curve": 0.245, "c3": 0.707, "c4": 0.82}, {"time": 3.3333, "x": 0.999}], "shear": [{}, {"time": 1.6667, "x": 1.2}, {"time": 3.3333}]}, "st2": {"rotate": [{"angle": 8.97, "curve": 0.27, "c2": 0.25, "c3": 0.751}, {"time": 1.6667, "angle": -0.01, "curve": 0.243, "c3": 0.658, "c4": 0.63}, {"time": 2.7333, "angle": 6.73, "curve": 0.351, "c2": 0.4, "c3": 0.687, "c4": 0.75}, {"time": 3, "angle": 8.26, "curve": 0.351, "c2": 0.44, "c3": 0.686, "c4": 0.79}, {"time": 3.1667, "angle": 8.88, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 3.3, "angle": 9.07, "curve": 0.326, "c2": 0.26, "c3": 0.659, "c4": 0.6}, {"time": 3.3333, "angle": 8.97}], "translate": [{"x": 17.4, "y": 2.07, "curve": 0.27, "c2": 0.25, "c3": 0.752}, {"time": 1.6667, "x": 0.43, "y": -4.67, "curve": 0.243, "c3": 0.658, "c4": 0.63}, {"time": 2.7333, "x": 12.16, "y": 0.17, "curve": 0.351, "c2": 0.4, "c3": 0.687, "c4": 0.75}, {"time": 3, "x": 15.87, "y": 1.51, "curve": 0.351, "c2": 0.44, "c3": 0.686, "c4": 0.79}, {"time": 3.1667, "x": 17.37, "y": 2.06, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 3.3, "x": 17.85, "y": 2.23, "curve": 0.324, "c2": 0.28, "c3": 0.658, "c4": 0.61}, {"time": 3.3333, "x": 17.4, "y": 2.07}]}, "st": {"rotate": [{"angle": 0.03, "curve": 0.345, "c2": 0.66, "c3": 0.679}, {"time": 0.0667, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 0.1667, "angle": 0.03, "curve": 0.315, "c2": 0.2, "c3": 0.649, "c4": 0.54}, {"time": 0.3, "angle": 0.15, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 2.1667, "angle": 2.4, "curve": 0.248, "c3": 0.733, "c4": 0.93}, {"time": 3.3333, "angle": 0.03}]}, "st4": {"rotate": [{"angle": -0.9, "curve": 0.27, "c2": 0.25, "c3": 0.756}, {"time": 1.7333, "angle": -15.6, "curve": 0.32, "c2": 0.04, "c3": 0.663, "c4": 0.65}, {"time": 2.8667, "angle": -0.51, "curve": 0.347, "c2": 0.41, "c3": 0.682, "c4": 0.75}, {"time": 3, "angle": -0.18, "curve": 0.357, "c2": 0.65, "c3": 0.692}, {"time": 3.1667, "curve": 0.3, "c3": 0.637, "c4": 0.36}, {"time": 3.3333, "angle": -0.9}], "translate": [{"x": 0.38, "y": 3.02, "curve": 0.27, "c2": 0.25, "c3": 0.754}, {"time": 1.8333, "x": -20.55, "y": -31.65, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 2.7333, "x": -6.82, "y": 1.06, "curve": 0.342, "c2": 0.37, "c3": 0.676, "c4": 0.7}, {"time": 2.8667, "x": -3.96, "y": 2.13, "curve": 0.345, "c2": 0.39, "c3": 0.679, "c4": 0.72}, {"time": 3, "x": -1.43, "y": 3.07, "curve": 0.353, "c2": 0.48, "c3": 0.688, "c4": 0.82}, {"time": 3.1667, "x": 0.6, "y": 3.83, "curve": 0.347, "c2": 0.66, "c3": 0.68}, {"time": 3.2333, "x": 0.95, "y": 3.96, "curve": 0.313, "c3": 0.647, "c4": 0.35}, {"time": 3.3333, "x": 0.38, "y": 3.02}]}, "s2": {"rotate": [{"angle": 0.31, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 6, "curve": 0.245, "c3": 0.707, "c4": 0.82}, {"time": 3.3333, "angle": 0.31}], "translate": [{"x": 2.04, "curve": 0.346, "c2": 0.38, "c3": 0.683, "c4": 0.72}, {"time": 0.3, "x": 0.93, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 6, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 3.3333, "x": 2.04}]}, "s6": {"rotate": [{"angle": -6.39, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 10.63, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -6.39}]}, "s7": {"rotate": [{"angle": -1.56, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6333, "angle": -6.39, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": 10.63, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 3.3333, "angle": -1.56}]}, "s8": {"rotate": [{"angle": 5.8, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.2333, "angle": -6.39, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": 10.63, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 3.3333, "angle": 5.8}]}, "s4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 7.49, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "s5": {"rotate": [{"angle": 4.29, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 1.0333, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 7.49, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 3.3333, "angle": 4.29}]}, "s1": {"rotate": [{"angle": 14.77, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 1.0333, "angle": 7.2, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 20.4, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 3.3333, "angle": 14.77}]}, "st3": {"rotate": [{"angle": -0.51, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": -1.2, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 3.3333, "angle": -0.51}]}, "s9": {"rotate": [{"angle": 1.65, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": 5.81, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 3.3333, "angle": 1.65}]}, "s10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 8.21, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "s11": {"rotate": [{"angle": 1.65, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": 5.81, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 3.3333, "angle": 1.65}]}, "s12": {"rotate": [{"angle": 4.17, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": 14.71, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 3.3333, "angle": 4.17}]}, "s13": {"rotate": [{"angle": 1.65, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": 5.81, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 3.3333, "angle": 1.65}]}, "s14": {"rotate": [{"angle": 1.65, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": 5.81, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 3.3333, "angle": 1.65}]}, "s15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 11.81, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "f9": {"rotate": [{"angle": -4.38, "curve": 0.353, "c2": 0.41, "c3": 0.757}, {"time": 0.9667, "angle": 13.05, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 2, "angle": -1.19, "curve": 0.347, "c2": 0.38, "c3": 0.688, "c4": 0.74}, {"time": 2.4, "angle": -7.81, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 2.8333, "angle": -11.62, "curve": 0.264, "c3": 0.618, "c4": 0.43}, {"time": 3.3333, "angle": -4.38}]}, "f10": {"rotate": [{"angle": -11.09, "curve": 0.278, "c2": 0.12, "c3": 0.754}, {"time": 1.3667, "angle": 11.17, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 4.72, "curve": 0.324, "c2": 0.31, "c3": 0.665, "c4": 0.66}, {"time": 2.4, "angle": -1.9, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 3.2333, "angle": -11.62, "curve": 0.313, "c3": 0.647, "c4": 0.35}, {"time": 3.3333, "angle": -11.09}]}, "f11": {"rotate": [{"angle": -9.54, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.3333, "angle": -11.62, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 8.16, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 2, "angle": 7.22, "curve": 0.298, "c2": 0.2, "c3": 0.64, "c4": 0.56}, {"time": 2.4, "angle": 2.55, "curve": 0.333, "c2": 0.33, "c3": 0.703, "c4": 0.78}, {"time": 3.3333, "angle": -9.54}]}, "f12": {"rotate": [{"angle": -1.36, "curve": 0.382, "c2": 0.55, "c3": 0.741}, {"time": 0.7333, "angle": -11.62, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 2, "angle": 14.58, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 2.2, "angle": 16.78, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 2.4, "angle": 15.31, "curve": 0.281, "c2": 0.16, "c3": 0.653, "c4": 0.62}, {"time": 3.3333, "angle": -1.36}]}, "f2": {"rotate": [{"angle": -1.2}, {"time": 0.5333, "angle": -0.52, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 2, "angle": -2.11, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 2.4, "angle": -2.4}, {"time": 3.3333, "angle": -1.2}]}, "f58": {"rotate": [{"angle": -2.88}, {"time": 0.5333, "angle": 0.32, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 2, "angle": -7.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 2.4, "angle": -8.49}, {"time": 3.3333, "angle": -2.88}]}, "f59": {"rotate": [{"angle": -7.3, "curve": 0.322, "c2": 0.29, "c3": 0.757}, {"time": 1.1667, "angle": 0.32, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 2, "angle": -3.45, "curve": 0.335, "c2": 0.34, "c3": 0.676, "c4": 0.69}, {"time": 2.4, "angle": -5.99, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 3.0333, "angle": -8.49, "curve": 0.283, "c3": 0.625, "c4": 0.39}, {"time": 3.3333, "angle": -7.3}]}, "f60": {"rotate": [{"angle": -7.56, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.3333, "angle": -8.49, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 0.32, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 2, "angle": -0.1, "curve": 0.298, "c2": 0.2, "c3": 0.64, "c4": 0.56}, {"time": 2.4, "angle": -2.18, "curve": 0.333, "c2": 0.33, "c3": 0.703, "c4": 0.78}, {"time": 3.3333, "angle": -7.56}]}, "f3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 1.96, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}], "shear": [{"curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": -8.4, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "f4": {"rotate": [{"angle": 0.1, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 1.96, "curve": 0.245, "c3": 0.707, "c4": 0.82}, {"time": 3.3333, "angle": 0.1}], "shear": [{"x": -0.43, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -8.4, "curve": 0.245, "c3": 0.707, "c4": 0.82}, {"time": 3.3333, "x": -0.43}]}, "f5": {"rotate": [{"angle": 0.3, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 1.96, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 3.3333, "angle": 0.3}], "shear": [{"x": -1.3, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "x": -8.4, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 3.3333, "x": -1.3}]}, "f6": {"rotate": [{"angle": 0.56, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": 1.96, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 3.3333, "angle": 0.56}], "shear": [{"x": -2.38, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "x": -8.4, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 3.3333, "x": -2.38}]}, "f7": {"rotate": [{"angle": 0.84, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": 1.96, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 3.3333, "angle": 0.84}], "shear": [{"x": -3.58, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "x": -8.4, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 3.3333, "x": -3.58}]}, "f8": {"rotate": [{"angle": 1.12, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 1.0333, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 1.96, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 3.3333, "angle": 1.12}], "shear": [{"x": -4.82, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 1.0333, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": -8.4, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 3.3333, "x": -4.82}]}, "f53": {"rotate": [{"angle": -6.87, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -1.98, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -6.87}]}, "f54": {"rotate": [{"angle": -1.19, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.5, "angle": -6.87, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": 21.15, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 3.3333, "angle": -1.19}]}, "f55": {"rotate": [{"angle": 2.91, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 1, "angle": -6.87, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": 11.11, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 3.3333, "angle": 2.91}]}, "f56": {"rotate": [{"angle": 4.86, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.5, "angle": -6.87, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "angle": 6.61, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 3.3333, "angle": 4.86}]}, "f13": {"rotate": [{"angle": -2.65}]}, "f20": {"rotate": [{"angle": -2.65, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 2.86, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -2.65}]}, "f21": {"rotate": [{"angle": -2.06, "curve": 0.372, "c2": 0.62, "c3": 0.712}, {"time": 0.3333, "angle": -2.65, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 2.86, "curve": 0.243, "c3": 0.686, "c4": 0.74}, {"time": 3.3333, "angle": -2.06}]}, "f22": {"rotate": [{"angle": -0.93, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.6667, "angle": -2.65, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 2.86, "curve": 0.244, "c3": 0.645, "c4": 0.58}, {"time": 3.3333, "angle": -0.93}]}, "f23": {"rotate": [{"angle": 0.35, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 1, "angle": -2.65, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": 2.86, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 3.3333, "angle": 0.35}]}, "f24": {"rotate": [{"angle": 1.6, "curve": 0.342, "c2": 0.37, "c3": 0.757}, {"time": 1.3333, "angle": -2.65, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "angle": 2.86, "curve": 0.27, "c3": 0.619, "c4": 0.41}, {"time": 3.3333, "angle": 1.6}]}, "f25": {"rotate": [{"angle": -1.71, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -4.26, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -1.71}]}, "f27": {"rotate": [{"angle": 0.63, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.7333, "angle": -1.71, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "angle": 4.64, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.3333, "angle": 0.63}]}, "f28": {"rotate": [{"angle": 2.3, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.1333, "angle": -1.71, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": 4.64, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.3333, "angle": 2.3}]}, "f29": {"rotate": [{"angle": 5.73, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.5, "angle": -1.71, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "angle": 6.84, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 3.3333, "angle": 5.73}]}, "f30": {"rotate": [{"angle": 10.91, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -1.71, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 10.91}]}, "f26": {"rotate": [{"angle": -1.1, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3667, "angle": -1.71, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 2.93, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.3333, "angle": -1.1}]}, "f14": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 1.17, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "f15": {"rotate": [{"angle": 0.21, "curve": 0.372, "c2": 0.62, "c3": 0.712}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 2.01, "curve": 0.243, "c3": 0.686, "c4": 0.74}, {"time": 3.3333, "angle": 0.21}]}, "f16": {"rotate": [{"angle": 1.09, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 3.51, "curve": 0.244, "c3": 0.645, "c4": 0.58}, {"time": 3.3333, "angle": 1.09}]}, "f17": {"rotate": [{"angle": 1.65, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": 3.03, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 3.3333, "angle": 1.65}]}, "f18": {"rotate": [{"angle": 3.71, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.2333, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": 5.19, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 3.3333, "angle": 3.71}]}, "f19": {"rotate": [{"angle": 7.37, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "angle": 8.47, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 3.3333, "angle": 7.37}]}, "sp1": {"rotate": [{"angle": -18, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 10.8, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -18}]}, "x1": {"rotate": [{"angle": -0.47, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 1.4667, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "angle": -0.55, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 3.3333, "angle": -0.47}]}, "x2": {"rotate": [{"angle": -6.42, "curve": 0.269, "c2": 0.1, "c3": 0.698, "c4": 0.78}, {"time": 1.4667, "angle": -0.57, "curve": 0.369, "c2": 0.63, "c3": 0.707}, {"time": 1.7333, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "angle": -6.55, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 3.3333, "angle": -6.42}]}, "x3": {"rotate": [{"angle": 7.69, "curve": 0.363, "c2": 0.64, "c3": 0.698}, {"time": 0.1667, "angle": 8.13, "curve": 0.243, "c3": 0.653, "c4": 0.62}, {"time": 1.4667, "angle": 2.08, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 2.0333, "curve": 0.245, "c3": 0.706, "c4": 0.81}, {"time": 3.3333, "angle": 7.69}]}, "x4": {"rotate": [{"angle": 5.63, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 0.4667, "angle": 7.61, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1.4667, "angle": 3.47, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 2.3333, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 3.3333, "angle": 5.63}]}, "x5": {"rotate": [{"angle": 2.22, "curve": 0.374, "c2": 0.49, "c3": 0.751}, {"time": 0.7333, "angle": 4.61, "curve": 0.26, "c3": 0.618, "c4": 0.45}, {"time": 1.4667, "angle": 3.04, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 2.6333, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 3.3333, "angle": 2.22}]}, "x6": {"rotate": [{"angle": 0.96, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 1.0333, "angle": 4.26, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 1.4667, "angle": 3.61, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 2.9, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 3.3333, "angle": 0.96}]}, "x7": {"rotate": [{"angle": 0.2, "curve": 0.284, "c2": 0.15, "c3": 0.754}, {"time": 1.3333, "angle": 6.29, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 1.4667, "angle": 6.15, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 3.2, "curve": 0.308, "c3": 0.643, "c4": 0.35}, {"time": 3.3333, "angle": 0.2}]}, "f31": {"rotate": [{"angle": -5.84, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.4, "angle": -6.96, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 2.74, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 3.3333, "angle": -5.84}]}, "f33": {"rotate": [{"angle": -3.87, "curve": 0.347, "c2": 0.38, "c3": 0.688, "c4": 0.74}, {"time": 0.4, "angle": -5.84, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.8333, "angle": -6.96, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": 0.34, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 3.3333, "angle": -3.87}]}, "f34": {"rotate": [{"angle": -1.73, "curve": 0.324, "c2": 0.31, "c3": 0.665, "c4": 0.66}, {"time": 0.4, "angle": -3.85, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 1.2333, "angle": -6.96, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": 0.34, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 3.3333, "angle": -1.73}]}, "f35": {"rotate": [{"angle": -0.01, "curve": 0.298, "c2": 0.2, "c3": 0.64, "c4": 0.56}, {"time": 0.4, "angle": -1.73, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.6667, "angle": -6.96, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "angle": 0.34, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 3.3333, "angle": -0.01}]}, "f37": {"rotate": [{"angle": -3.06, "curve": 0.347, "c2": 0.38, "c3": 0.688, "c4": 0.74}, {"time": 0.4, "angle": -4.99, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.8333, "angle": -6.1, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": 3.49, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 3.3333, "angle": -3.06}]}, "f38": {"rotate": [{"angle": -0.96, "curve": 0.324, "c2": 0.31, "c3": 0.665, "c4": 0.66}, {"time": 0.4, "angle": -3.06, "curve": 0.347, "c2": 0.38, "c3": 0.688, "c4": 0.74}, {"time": 0.8333, "angle": -4.99, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 1.2333, "angle": -6.1, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": 1.09, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 3.3333, "angle": -0.96}]}, "f39": {"rotate": [{"angle": 0.74, "curve": 0.298, "c2": 0.2, "c3": 0.64, "c4": 0.56}, {"time": 0.4, "angle": -0.95, "curve": 0.324, "c2": 0.31, "c3": 0.665, "c4": 0.66}, {"time": 0.8333, "angle": -3.03, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 1.6667, "angle": -6.1, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "angle": 1.09, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 3.3333, "angle": 0.74}]}, "f40": {"rotate": [{"angle": 0.53, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.2, "angle": 1.09, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 0.4, "angle": 0.74, "curve": 0.298, "c2": 0.2, "c3": 0.64, "c4": 0.56}, {"time": 0.8333, "angle": -0.95, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.0667, "angle": -6.1, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 3.3333, "angle": 0.53}]}, "f41": {"rotate": [{"angle": -0.24}]}, "f42": {"rotate": [{"angle": 0.98, "curve": 0.344, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 0.2, "angle": 0.43, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.6333, "angle": -0.24, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": 7.71, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 3.3333, "angle": 0.98}]}, "f43": {"rotate": [{"angle": 3.21, "curve": 0.332, "c2": 0.33, "c3": 0.668, "c4": 0.67}, {"time": 0.2, "angle": 2.33, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 1.0333, "angle": -0.24, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 5.78, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 3.3333, "angle": 3.21}]}, "f44": {"rotate": [{"angle": 6.68, "curve": 0.321, "c2": 0.29, "c3": 0.656, "c4": 0.63}, {"time": 0.2, "angle": 5.6, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.4667, "angle": -0.24, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "angle": 7.92, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 3.3333, "angle": 6.68}]}, "f48": {"rotate": [{"angle": -0.24}]}, "f49": {"rotate": [{"angle": 1.6, "curve": 0.338, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 0.2, "angle": 0.98, "curve": 0.344, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 0.4, "angle": 0.43, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.8333, "angle": -0.24, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": 6.51, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 3.3333, "angle": 1.6}]}, "f50": {"rotate": [{"angle": 4.08, "curve": 0.327, "c2": 0.31, "c3": 0.662, "c4": 0.65}, {"time": 0.2, "angle": 3.21, "curve": 0.332, "c2": 0.33, "c3": 0.668, "c4": 0.67}, {"time": 0.4, "angle": 2.33, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 1.2333, "angle": -0.24, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": 5.78, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 3.3333, "angle": 4.08}]}, "f51": {"rotate": [{"angle": 7.53, "curve": 0.313, "c2": 0.24, "c3": 0.648, "c4": 0.58}, {"time": 0.2, "angle": 6.68, "curve": 0.321, "c2": 0.29, "c3": 0.656, "c4": 0.63}, {"time": 0.4, "angle": 5.6, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.6667, "angle": -0.24}, {"time": 3.1333, "angle": 7.92, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 3.3333, "angle": 7.53}]}, "f52": {"rotate": [{"angle": 6.82, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.2, "angle": 7.39, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 6.57, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 3.3333, "angle": 6.82}]}, "f32": {"scale": [{"x": 0.997, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.4}, {"time": 1.8667, "x": 0.98, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 3.3333, "x": 0.997}], "shear": [{"x": 1.11, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "x": 7.2, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 3.3333, "x": 1.11}]}, "f36": {"rotate": [{"angle": 9.06, "curve": 0.293, "c2": 0.18, "c3": 0.755}, {"time": 1.6667}, {"time": 3.1333, "angle": 9.55, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 3.3333, "angle": 9.06}]}, "f45": {"rotate": [{"angle": 8.31, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3, "angle": 9.55, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1.6667, "angle": 1.93, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 2.1667, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.3333, "angle": 8.31}]}, "f46": {"rotate": [{"angle": 4.23, "curve": 0.37, "c2": 0.48, "c3": 0.752}, {"time": 0.8, "angle": 9.55, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1.6667, "angle": 5.2, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 2.6667, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 3.3333, "angle": 4.23}]}, "zs5": {"scale": [{}, {"time": 3.3, "x": 1.84, "y": 1.8, "curve": "stepped"}, {"time": 3.3333}]}, "zs24": {"scale": [{"x": 1.532, "y": 1.289}, {"time": 0.8, "x": 1.7, "y": 1.38, "curve": "stepped"}, {"time": 0.8333}, {"time": 3.3333, "x": 1.532, "y": 1.289}]}, "zs30": {"scale": [{"x": 1.354, "y": 1.192}, {"time": 1.6333, "x": 1.7, "y": 1.38, "curve": "stepped"}, {"time": 1.6667}, {"time": 3.3333, "x": 1.354, "y": 1.192}]}, "zs22": {"scale": [{}, {"time": 3.3, "x": 1.22, "y": 1.18}, {"time": 3.3333}]}, "zs36": {"scale": [{"x": 1.153, "y": 1.125}, {"time": 1, "x": 1.22, "y": 1.18}, {"time": 1.0333}, {"time": 3.3333, "x": 1.153, "y": 1.125}]}, "zs38": {"scale": [{"x": 1.084, "y": 1.068}, {"time": 2.0333, "x": 1.22, "y": 1.18}, {"time": 2.0667}, {"time": 3.3333, "x": 1.084, "y": 1.068}]}, "zs7": {"rotate": [{"angle": -1.6, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": -8.67, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 3.3333, "angle": -1.6}]}, "zs8": {"rotate": [{"angle": -4.3, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.4, "angle": -1.6, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.8333}, {"time": 2.5, "angle": -8.67, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": -4.3}]}, "zs9": {"rotate": [{"angle": -7.05, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 0.8333, "angle": -1.6, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.2333}, {"time": 2.9, "angle": -8.67, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 3.3333, "angle": -7.05}]}, "zs10": {"rotate": [{"angle": -8.67, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.2333, "angle": -1.6, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -8.67}]}, "zs11": {"rotate": [{"angle": 2.35, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 4.69, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": 2.35}]}, "zs12": {"rotate": [{"angle": 4.69, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.8333, "angle": 2.35, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667}, {"time": 3.3333, "angle": 4.69}]}, "zs13": {"rotate": [{"angle": 2.35}, {"time": 0.8333, "angle": 4.69, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6667, "angle": 2.35, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5}, {"time": 3.3333, "angle": 2.35}]}, "zw3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -36, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "angle": -27.6}, {"time": 3.3333}]}, "bone7": {"translate": [{"x": 15.29, "y": 16.48, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "x": -41.53, "y": -57.05}, {"time": 3.3333, "x": 15.29, "y": 16.48}]}, "bone3": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 7.67, "y": -22.12, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "x": 52.4, "y": -106.8}, {"time": 3.3333}]}, "bone8": {"translate": [{"x": -6.01, "y": -10.41, "curve": 0.376, "c2": 0.5, "c3": 0.75}, {"time": 1.6333, "x": -26.61, "y": -36.42}, {"time": 1.6667, "x": 15.29, "y": 16.48, "curve": 0.25, "c3": 0.626, "c4": 0.5}, {"time": 3.3333, "x": -6.01, "y": -10.41}]}, "zs20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -8.32, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "zs21": {"rotate": [{"angle": -6.78, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.2333, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "angle": -8.32, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 3.3333, "angle": -6.78}]}, "bone5": {"translate": [{"x": -3.98, "y": 41.81}, {"time": 3.3, "x": -67.98, "y": -66.75}, {"time": 3.3333, "x": -3.98, "y": 41.81}]}, "bone10": {"translate": [{"x": -36.39, "y": -13.16}, {"time": 1.6333, "x": -67.98, "y": -66.75}, {"time": 1.6667, "x": -3.98, "y": 41.81}, {"time": 3.3333, "x": -36.39, "y": -13.16}]}, "bone6": {"translate": [{"x": 76.62, "y": 57.91, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "x": 28.26, "y": -61.49}, {"time": 3.3333, "x": 76.62, "y": 57.91}]}, "zw4": {"rotate": [{"angle": -36, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": -27.6}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -36}]}, "bone9": {"translate": [{"x": -20.99, "y": -65.69, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "x": 52.4, "y": -106.8}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -20.99, "y": -65.69}]}, "bone4": {"translate": [{}, {"time": 3.3, "x": 48}, {"time": 3.3333}], "scale": [{}, {"time": 3.3, "x": 1.24, "y": 1.08}, {"time": 3.3333}]}, "x8": {"rotate": [{"angle": -0.32}, {"time": 0.6333, "angle": -0.55, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 1.0333, "angle": -0.47, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 2.5}, {"time": 3.3333, "angle": -0.32}]}, "x14": {"rotate": [{"angle": 3.08, "curve": 0.376, "c2": 0.51, "c3": 0.749}, {"time": 0.9, "curve": 0.308, "c3": 0.643, "c4": 0.35}, {"time": 1.0333, "angle": 0.2, "curve": 0.284, "c2": 0.15, "c3": 0.754}, {"time": 2.3667, "angle": 6.29, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 2.5, "angle": 6.15, "curve": 0.272, "c2": 0.11, "c3": 0.637, "c4": 0.56}, {"time": 3.3333, "angle": 3.08}]}, "x13": {"rotate": [{"angle": 1.22, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6333, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 1.0333, "angle": 0.96, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 2.0667, "angle": 4.26, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 2.5, "angle": 3.61, "curve": 0.307, "c2": 0.25, "c3": 0.67, "c4": 0.68}, {"time": 3.3333, "angle": 1.22}]}, "x12": {"rotate": [{"angle": 0.5, "curve": 0.372, "c2": 0.62, "c3": 0.712}, {"time": 0.3333, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 1.0333, "angle": 2.22, "curve": 0.374, "c2": 0.49, "c3": 0.751}, {"time": 1.8, "angle": 4.61, "curve": 0.26, "c3": 0.618, "c4": 0.45}, {"time": 2.5, "angle": 3.04, "curve": 0.339, "c2": 0.35, "c3": 0.702, "c4": 0.78}, {"time": 3.3333, "angle": 0.5}]}, "x11": {"rotate": [{"angle": 0.03, "curve": 0.34, "c2": 0.66, "c3": 0.674}, {"time": 0.0333, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 1.0333, "angle": 5.63, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 1.5, "angle": 7.61, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2.5, "angle": 3.47, "curve": 0.374, "c2": 0.49, "c3": 0.74, "c4": 0.96}, {"time": 3.3333, "angle": 0.03}]}, "x10": {"rotate": [{"angle": 0.84, "curve": 0.303, "c2": 0.23, "c3": 0.716, "c4": 0.84}, {"time": 1.0333, "angle": 7.69, "curve": 0.363, "c2": 0.64, "c3": 0.698}, {"time": 1.2, "angle": 8.13, "curve": 0.243, "c3": 0.653, "c4": 0.62}, {"time": 2.5, "angle": 2.08, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 3.0667, "curve": 0.289, "c3": 0.628, "c4": 0.38}, {"time": 3.3333, "angle": 0.84}]}, "x9": {"rotate": [{"angle": -2.17, "curve": 0.358, "c2": 0.43, "c3": 0.756}, {"time": 0.9, "angle": -6.55, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 1.0333, "angle": -6.42, "curve": 0.269, "c2": 0.1, "c3": 0.698, "c4": 0.78}, {"time": 2.5, "angle": -0.57, "curve": 0.369, "c2": 0.63, "c3": 0.707}, {"time": 2.8, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 3.3333, "angle": -2.17}]}, "bone11": {"translate": [{"x": 33.42}, {"time": 1, "x": 48}, {"time": 1.0333}, {"time": 3.3333, "x": 33.42}], "scale": [{"x": 1.167, "y": 1.056}, {"time": 1, "x": 1.24, "y": 1.08}, {"time": 1.0333}, {"time": 3.3333, "x": 1.167, "y": 1.056}]}, "x15": {"rotate": [{"angle": -0.03, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.2}, {"time": 1.6667, "angle": -0.55, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 2.0667, "angle": -0.47, "curve": 0.316, "c2": 0.27, "c3": 0.719, "c4": 0.84}, {"time": 3.3333, "angle": -0.03}]}, "x21": {"rotate": [{"angle": 6.18, "curve": 0.35, "c2": 0.65, "c3": 0.684}, {"time": 0.0667, "angle": 6.29, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 0.2, "angle": 6.15, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 1.9667, "curve": 0.308, "c3": 0.643, "c4": 0.35}, {"time": 2.0667, "angle": 0.2, "curve": 0.28, "c2": 0.14, "c3": 0.732, "c4": 0.9}, {"time": 3.3333, "angle": 6.18}]}, "x20": {"rotate": [{"angle": 4.06, "curve": 0.313, "c2": 0.24, "c3": 0.648, "c4": 0.58}, {"time": 0.2, "angle": 3.61, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 1.6667, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 2.0667, "angle": 0.96, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 3.1333, "angle": 4.26, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 3.3333, "angle": 4.06}]}, "x19": {"rotate": [{"angle": 3.68, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.2, "angle": 3.04, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.3667, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 2.0667, "angle": 2.22, "curve": 0.374, "c2": 0.49, "c3": 0.751}, {"time": 2.8333, "angle": 4.61, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 3.3333, "angle": 3.68}]}, "x18": {"rotate": [{"angle": 4.59, "curve": 0.331, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 0.2, "angle": 3.47, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 1.0667, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 2.0667, "angle": 5.63, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 2.5333, "angle": 7.61, "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 3.3333, "angle": 4.59}]}, "x17": {"rotate": [{"angle": 3.22, "curve": 0.339, "c2": 0.35, "c3": 0.674, "c4": 0.69}, {"time": 0.2, "angle": 2.08, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 0.8, "curve": 0.245, "c3": 0.706, "c4": 0.81}, {"time": 2.0667, "angle": 7.69, "curve": 0.363, "c2": 0.64, "c3": 0.698}, {"time": 2.2333, "angle": 8.13, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 3.3333, "angle": 3.22}]}, "x16": {"rotate": [{"angle": -1.32, "curve": 0.348, "c2": 0.39, "c3": 0.684, "c4": 0.74}, {"time": 0.2, "angle": -0.57, "curve": 0.369, "c2": 0.63, "c3": 0.707}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": -6.55, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 2.0667, "angle": -6.42, "curve": 0.267, "c2": 0.1, "c3": 0.67, "c4": 0.68}, {"time": 3.3333, "angle": -1.32}]}, "bone12": {"translate": [{"x": 18.23, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "x": 48, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 18.23}], "scale": [{"x": 1.091, "y": 1.03, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "x": 1.24, "y": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 1.091, "y": 1.03}]}, "st5": {"rotate": [{"angle": -1.44, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 0.97, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -1.44}]}, "st6": {"rotate": [{"angle": -1.02, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.4, "angle": -1.44, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": 0.97, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 3.3333, "angle": -1.02}]}, "st7": {"rotate": [{"angle": -0.3, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 0.8, "angle": -1.44, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": 0.97, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 3.3333, "angle": -0.3}]}, "st8": {"rotate": [{"angle": 0.45, "curve": 0.34, "c2": 0.36, "c3": 0.757}, {"time": 1.2, "angle": -1.44, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "angle": 0.97, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 3.3333, "angle": 0.45}]}}, "deform": {"default": {"f1": {"f1": [{"curve": 0.25, "c3": 0.75}, {"time": 1.4667, "vertices": [-11.88708, 21.25546, 5.74817, 24.96155, -15.90178, -22.53998, -3.61892, 10.48859, -1.15058, -11.20685, -0.32238, 0.7963, -0.3461, 0.7413, -4.95421, 1.66528, -5.04173, 0.85629, -1.8888, -0.06955, -1.90378, -0.37509, -0.20037, -1.41037, -0.1652, -1.43723, 0.14615, -1.4227, -1.89436, -2.58859, -1.30689, -3.08151, -2.25931, -2.2666, -1.70185, -1.70721, 0.8884, -2.16441, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.11417, 1.74747, -2.68235, -1.08588, -2.67791, -1.51596, 2.34252, 2.16812, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.69183, -0.48565, 1.76263, -0.03571, 1.6953, 0.48175, -3.50806, -5.39326, -1.77885, -6.18241, 0.16826, -9.65167, 2.98189, -9.18014, 3.57624, -12.90337, 8.53521, -10.31688, 3.83321, -15.97672, 10.02517, -13.01716, 2.3862, -6.46465, 4.81884, -4.92593, 0, 0, 0, 0, 0, 0, -1.8333, 1.87408, -1.02975, 2.79008, -2.07967, 2.126, -0.12957, 2.27599, -0.78942, 2.13871, -1.59422, 1.62963, 0, 0, 0, 0, 0.40662, 2.4114, 0.54084, 2.38485, -0.08775, 2.44385, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.55949, 5.69386, -5.62553, 11.35211, 12.30393, 1.52596, -11.29595, 7.11684, -0.91691, -1.69443, -7.36314, -2.52751, 0.98399, -2.13391, -8.06686, -5.54727, -2.26942, 3.90044, 0.67924, 1.16696, 0.2507, -1.32737, 0.13058, -1.40821, -10.57817, 1.6035, 2.93322, -1.05038, -2.90567, -1.12458, -2.96458, -0.96037, 0.73016, 2.84898, -2.60855, -2.62051, 0.26158, 3.6904, 0.46332, 3.67005, 0, 0, -2.18117, -1.80284, 0.47056, 2.7905, 0.62581, 2.75978, 0.20284, 1.20175, 0.26943, 1.18848, 0.47601, 2.82267, 0.6331, 2.79158, -0.10268, 2.86066, -0.24857, 1.29333, -0.616, 1.16423, 0.02715, -0.33193, 0.12283, -0.30941, 0.23863, -0.23226, 0.07074, -1.24153, 0.43058, -1.16654, 0.86953, -0.88889, 0, 0, 0, 0, 0, 0, -3.97087, -4.11804, 5.39051, 2.84567, 5.36729, 3.71597, 4.48084, 5.29581, -6.63074, 0.63747, -4.55229, -5.01532, 2.66905, -1.92734, 3.0416, -0.95367, -0.85019, 3.17073, -3.03324, 1.15063, 2.3777, 2.38556, -1.2424, 3.02501, -3.17984, 0.59698, -2.41389, -2.2345, 2.11877, 2.1257, -1.10687, 2.69533, -2.83371, 0.53194, -2.15111, -1.99097, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.17255, 3.18311, -1.65753, 4.03578, -4.24268, 0.79636, -3.22086, -2.98145, 2.74583, 1.55435, 2.36556, 2.37332, -1.23602, 3.00963, -3.16357, 0.59396, -2.40155, -2.22305, -0.48888, -0.27698, -0.42137, -0.42261, -6.90208, -6.03479, 8.89722, 3.86157, -7.70456, -7.45789, -0.02389, -5.9115, 2.53856, 5.71835, 2.41229, 6.11313, -1.14378, -6.55811], "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "st": {"st": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "offset": 56, "vertices": [-17.01035, -16.74297, -11.93459, -20.66916, 23.23026, 5.47623, 25.74139, -2.46231, 28.6864, -11.85208, 30.38019, -6.35381, 30.47607, 5.86496, 31.37131, -20.26212, 34.57086, -14.12393, 37.34161, 0.31467, 33.56009, 16.38434, 34.40289, -29.65236, 39.28314, -22.79343, 45.03485, -5.8642, 43.16547, 14.12585, 38.84995, -43.51818, 46.21259, -35.60052, 56.37067, -15.00409, 57.33414, 10.76587, 17.14423, -22.12419, 20.93219, -18.57996, 26.48077, -9.06049, 27.80096, 3.242, -17.93326, 12.34381, 6.10669, -11.55055, -6.52057, 11.3248, -10.76343, 7.4104, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.17279, 10.75398, -0.77003, 10.94409, -7.44147, -8.06251, 2.17279, 10.75398, -11.65912, -6.16417, 13.16376, -0.78561, 13.08112, 1.65822, 5.80518, -2.29974, -3.7681, 4.97965, -4.62244, 4.19966, -5.88794, 2.0907, -24.40021, -0.96046, 21.35019, -11.84873, 23.16846, -7.70338, 24.34119, 1.83704, -0.94336, -14.41423, 8.2894, 11.82924, 5.96313, 13.15709, 0.41852, 14.43936, -5.84329, 13.21472, -21.42548, -8.38547, 22.66327, -3.9586, 23.00311, 0.29352, 21.10187, 9.15057, -18.78845, -11.02455, 21.4657, 3.69299, 18.3717, 11.6926, 11.54126, 18.47736, -20.01056, -14.47925, 23.82764, 6.49471, 19.46912, 15.18939, 11.02432, 22.10431, -1.95639, -6.37787, 4.07648, 5.27982, 1.71881, 6.44476, -1.22336, 6.56244, -6.85382, -16.9986, 12.40369, 13.49146, 6.229, 17.23486, -1.80307, 18.24298], "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "x1": {"x1": [{"curve": 0.25, "c3": 0.75}, {"time": 1.4667, "vertices": [-5.98218, 6.73369, -4.70291, 5.29332, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.80249, -9.84055, -7.65088, -6.79539, -2.80249, -9.84055, -7.65088, -6.79539, -5.57965, -10.84525, -10.53519, -6.15085, -3.48676, -12.24377, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.48448, -1.8649, -0.52762, -1.85303, -1.44058, -1.2795, 0.55226, -2.12582, -0.60147, -2.11188, -1.6424, -1.45837, -1.04568, -3.44437, -2.73465, -2.34401, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -8.36328, 9.41322, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17.43907, -8.70264, 0, 0, 0, 0, 19.55959, -8.40314, 19.09003, -9.4173, 0, 0, 0, 0, 20.19793, -8.44391, 0, 0, 0, 0, 20.12329, -6.19568, 0, 0, 0, 0, 19.99287, -3.37854, 0, 0, 0, 0, 19.79699, -0.57599, 0, 0, 0, 0, 19.38443, 2.27771, 18.82396, -5.1702, 0, 0, 0, 0, 3.52959, -1.09775, 0, 0, 4.29237, -5.9718, 0.67389, -7.31982, 3.53413, 0.20407, 3.15039, -1.61353, 4.59169, -10.42331, -1.32458, -11.30859, 2.57629, -5.16977, -0.39941, -5.76031, 5.17003, -13.56326, -2.41751, -14.30884, 2.12535, -10.92908, -3.70752, -10.49612, 6.74556, -17.88889, -3.2522, -18.83594, -12.86502, -14.14156, 2.88123, -16.45395, -5.85638, -15.64157, -3.51483, -22.34113, -14.9697, -16.95758, -7.74658, -19.16943, -1.80411, -19.34375, -11.9173, -15.34827, -3.34967, -11.76147, -9.14392, -8.12161, 4.50641, -6.28311, 0.41876, -7.7204, -1.74338, -6.12186, -4.75937, -4.22754, 0.86572, -5.03525, 0, 0, 4.97189, 0.26373, 4.32794, -2.44827, 0, 0, 1.5105, 1.07475, 0, 0, -0.25464, 3.51071, 0, 0, -1.98267, 6.2748, 0, 0, -3.46484, 8.38449, 0, 0, -4.72119, 9.99858], "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "s2": {"s2": [{"offset": 14, "vertices": [0.2544, 0, 0.25301, 0.02648, 0.2544, 0.14967, 0.23743, 0.17533, 0.17585, 0.10604, 0.16384, 0.12375, 0, 0.13156, -0.0137, 0.13084, 0, 0.14225, -0.01481, 0.14148, 0.01297, 0.14166, 0, 0.13811, -0.01438, 0.13736, 0.01259, 0.13753, 0, -0.05258, 0.00547, -0.0523, -0.00479, -0.05236, 0.0016, -0.05256, 0, -0.08553, 0.0089, -0.08507, -0.00779, -0.08517, 0.00257, -0.08549, 0, -0.05673, 0.0059, -0.05642, -0.00517, -0.05649, 0.00172, -0.05671, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.45384, -0.64445, -0.38228, -0.68936, -0.44047, -0.59746, -0.37402, -0.64121, -0.4097, -0.49251, -0.35467, -0.53357, -0.40652, -0.44742, -0.35635, -0.4884, -0.4432, -0.38193, -0.39982, -0.42722, -0.4917, -0.30112, -0.45671, -0.35206, -0.54899, -0.20707, -0.52374, -0.26469, -0.61565, -0.0948, -0.60204, -0.1602, 0, 0, 0, 0, 0, 0, 0.33923, -0.32064, 0.28137, -0.37257, 0.31825, -0.35455, 0.25504, -0.4025, 0.3391, 0.09842, 0.3508, 0.04071, 0.60561, 0.00371, 0.57811, 0.18066, 0.60014, 0.08213, 0.60665, 0.08521, 0.59444, 0.14785, 0.52528, 0.31524, 0.5704, 0.22361, 0.01197, 0.35638, -0.02522, 0.35566, -0.12806, 0.3328, -0.395, -0.17309, -0.37484, -0.21328, -0.29613, -0.3135, -0.24263, -0.09257, -0.2317, -0.11737, -0.18728, -0.17989, -0.5383, -0.09051, -0.52608, -0.14621, -0.46035, -0.2933, -0.6887, 0.41265, -0.72802, 0.33859, 0.05735, 0.41224, 0.014, 0.4158, 0.18204, 0, 0.18105, 0.01894, 0.18204, 0, 0.26073, 0, 0.26073, 0, 0.43877, 0, 0.43877, 0, 0, 0, 0.26073, 0, 0.25331, 0, 0.23339, 0, 0.37527, 0, 0.38596, 0, 0.38386, 0.04017, 0.31113, 0, 0.30943, 0.03238, 0.43877, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.26073, 0, 0.21169, 0, 0.2544, 0, 0.2544, 0, 0.25301, 0.02648, 0.2544, 0, 0.25301, 0.02648, 0.10336, 0, 0.2544, 0, 0.2544, 0, 0.19715, 0.51748, 0.37981, 0.39038, 0.2544, 0, 0.25301, 0.02648, 0.2544, 0, 0.25301, 0.02648, 0.11855, 0, 0.1179, 0.01233, -0.24773, 0.12696, -0.25976, 0.10031, -0.27769, 0.02031, 0.15227, 0.31082, 0.11905, 0.32493, 0.01888, 0.34563, 0.25714, 0.25554, 0.22911, 0.28087, 0.13701, 0.33567, 0.36756, 0.27787, 0.3366, 0.31457, 0.23345, 0.01948, 0.23014, 0.04367, 0.15344, -0.18644, 0.17201, -0.16946, -0.20171, -0.14856, -0.18524, -0.16887, -0.12778, -0.21543, -0.08106, 0.17536, -0.09892, 0.1659, -0.14309, 0.12985, 0.01126, 0.20432, -0.01011, 0.20434, -0.06939, 0.19255, 0.24097, 0.0584, 0.23354, 0.08311, 0.04217, -0.27256, 0.07029, -0.26673, 0.01718, -0.27528, -0.11807, 0.17074, -0.13526, 0.15744, -0.17535, 0.11117, 0.10383, 0.04198, 0.09882, 0.05247, 0.0792, 0.07923, 0.19802, -0.1152, 0.20891, -0.09401, 0.03489, -0.30348, 0.06626, -0.29824, 0.00711, -0.30541, -0.26241, 0.14577, -0.27618, 0.11762, -0.2985, 0.03183, 0.13729, 0.13404, 0.12257, 0.14758, 0.07408, 0.177, 0, 0.20592, -0.02144, 0.20479, 0.01876, 0.20506, 0.02667, -0.15161, 0.04228, -0.14804, 0.01276, -0.15342, 0.03133, -0.15075, 0.00024, 0.32622, -0.09511, 0.31213, 0, 0, 0, 0, 0.02493, -0.18719, 0.04426, -0.1836, 0.00779, -0.18869, 0.03064, -0.18637, 0.16395, 0.49063, 0.25618, 0.44943, 0.01338, 0.51716, 0, 0, 0, 0, 0, 0, 0.01508, -0.44219, -0.07106, -0.43668, -0.01763, -0.44207, 0.08409, 0.48963, -0.15733, 0.4713, -0.0768, 0.49082, 0.10427, 0.05933, 0.11382, 0.03794, 0.10842, 0.05142, 0.01888, -0.41421, -0.0619, -0.40997, -0.01176, -0.41444, 0.11876, 0.13983, 0.10354, 0.15141, 0.09266, 0.25415, 0.1092, 0.34298, 0.17378, 0.31524, 0.1499, 0.21223, 0.03175, 0.25791, -0.16811, 0.42548, -0.09509, 0.44747, 0.13053, 0.12404, 0.11465, 0.13891, 0.0564, 0.17101, 0.08405, 0.15926, 0.22569, 0.07522, 0.16335, 0.17294, 0.18979, 0.14342, 0.17333, 0.16291, 0.02496, -0.38137, -0.04956, -0.37893, -0.00326, -0.38214, -0.14586, 0.20085, -0.11044, 0.22227, 0.04254, 0.02418, 0.03939, 0.02915, 0.02605, 0.04143, 0.03261, 0.03652, 0.02842, 0.03979, 0.17321, 0.05773, 0.16499, 0.07825, 0.12537, 0.13273, 0.14567, 0.11007, 0.13302, 0.12503, 0.02435, -0.3486, -0.04379, -0.34667, -0.00143, -0.34943, -0.28596, -0.0001, -0.02963, -0.11138, -0.01588, -0.11414, 0.0265, -0.11215, 0.00754, -0.115, 0.01974, -0.11354, 0.02459, -0.31828, -0.03765, -0.31698, 0.00106, -0.3192, 0.01094, -0.29965, 0, 0, 0, 0, 0, 0, 0, 0, -0.44032, -0.07596, -0.42973, -0.1227, -0.385, -0.1396, -0.36791, -0.18004, -0.25153, -0.24916, -0.22349, -0.27468, -0.14376, -0.31791, -0.1042, -0.33296, 0.02351, -0.3481, -0.03458, -0.34718, 0.00273, -0.3489, -0.52482, -0.19396, -0.50111, -0.24906, -0.38584, -0.29521, -0.35208, -0.33485, -0.01471, -0.38994, 0.10534, -0.37572, -0.0286, -0.45584, 0.02027, -0.4563, -0.33428, -0.6539, -0.26241, -0.68595, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.21848, -0.02478, -0.15214, -0.04906, -0.57187, -0.14302, -0.55334, -0.20346], "curve": 0.273, "c2": 0.1, "c3": 0.753}, {"time": 1.3667, "offset": 14, "vertices": [13.03793, 5e-05, 12.96674, 1.35684, 13.03792, 7.67079, 12.168, 8.98572, 9.01231, 5.43434, 8.397, 6.34229, -3e-05, -0.71162, 0.07443, -0.70787, -5e-05, -3.96657, 0.41379, -3.94505, -1.78607, -3.72708, 0, 7.07803, -0.73691, 7.03947, 0.64505, 7.04852, 6.7447, -2.69476, 6.9881, -1.97822, 6.33643, -4.15112, 6.77606, -3.49629, 6.63615, -4.38328, 7.056, -3.66883, 6.07751, -5.80927, 6.72003, -5.17115, 8.59461, -2.45407, 8.80307, -1.54621, 8.22198, -4.32314, 8.67419, -3.47883, 7.38962, 0.77357, 7.25168, -1.6169, 7.37622, -0.88416, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -23.25909, -33.02798, -19.59198, -35.32954, -22.57397, -30.61993, -19.16864, -32.86188, -20.99689, -25.24113, -18.1767, -27.34534, -20.83423, -22.93013, -18.2627, -25.03033, -22.71399, -19.57407, -20.49097, -21.89487, -25.19971, -15.43219, -23.40607, -18.04292, -28.1358, -10.61238, -26.84137, -13.56526, -31.552, -4.85834, -30.85425, -8.21028, -9.15146, -9.80162, -10.46625, -8.38312, -9.3775, -9.58522, 17.38562, -16.43286, 14.42029, -19.09419, 16.31052, -18.17053, 13.07062, -20.6281, 17.26111, 8.48515, 18.35852, 5.50851, 31.03757, 0.19013, 29.62781, 9.25864, 30.75708, 4.20927, 15.40279, 17.96623, 13.44565, 19.46948, 7.13205, 22.44003, 10.55536, 20.52547, -4.272, 32.45547, -7.6286, 31.8334, -16.66943, 28.15445, -8.25845, -0.48614, -8.16385, -1.34373, -7.48642, -3.62502, -0.32, -5.60599, 0.26324, -5.61073, 1.86902, -5.21806, 19.03642, 0.45663, 18.87778, 2.42794, 17.21097, 8.09174, 31.2434, -0.21861, 31.08905, 3.02866, 25.40129, -0.03862, 25.25919, 2.59746, 15.89282, 5.51108, 15.23218, 7.1354, 9.32977, 8e-05, 13.36231, 4e-05, 13.36231, 4e-05, 22.48672, 7e-05, 22.48672, 7e-05, 8.12066, -0.63556, 13.36231, 4e-05, 12.98215, 7e-05, 11.96137, 0.00021, 19.23263, 8e-05, 19.7805, 9e-05, 19.67255, 2.0585, 15.94521, 5e-05, 15.85825, 1.6594, 22.48672, 7e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13.36231, 4e-05, 10.84914, 6e-05, 13.03793, 5e-05, 13.03793, 5e-05, 12.96674, 1.35684, 13.03793, 5e-05, 12.96674, 1.35684, 14.27518, 3e-05, 22.47401, 8e-05, 16.85598, 6e-05, 28.71745, 37.43008, 26.92947, 28.79725, 31.25092, 21.41778, 28.85056, 24.55426, 35.51624, 15.68124, 33.69022, 19.29287, 21.09016, 6.02544, 20.34756, 8.18781, 28.51667, 1.33512, 28.21326, 4.28812, 25.66327, 12.59483, 15.88168, 23.32115, 13.36533, 24.84495, 5.46207, 27.69753, 16.26406, 21.28896, 13.95779, 22.86319, 6.61435, 25.94724, 18.83719, 14.24093, 17.25049, 16.12147, 23.78117, 0.99833, 23.54739, 3.46817, 23.64397, -9.5548, 24.50967, -7.04196, 5.77808, 5.13715, 5.20618, 5.70498, 3.207, 7.0451, 5.52441, 15.67936, 3.86118, 16.16636, -1.09363, 16.63106, 7.27936, 19.42194, 5.21817, 20.07079, -0.94577, 20.74303, 12.34967, 9.83542, 11.25656, 11.06464, 9.70383, -7.75512, 10.45639, -6.70466, 9.59363, -9.68468, 0.3782, 17.45095, -1.44455, 17.39437, -6.52457, 16.23573, 2.29705, 18.39801, 0.36572, 18.53358, -5.16208, 17.77859, 6.42796, 2.05053, 6.17825, 2.70617, 8.54809, -15.55346, 10.11929, -14.58078, 6.96161, -17.12347, -7.58021, 26.18869, -10.26596, 25.25584, -17.31723, 21.14177, -1.41707, 10.96007, -2.54874, 10.75415, -5.59619, 9.4834, -3.48637, 1.36965, -3.6102, 0.99954, -4.44144, 2.30121, 1.36707, -7.7699, 2.16705, -7.58691, 0.65411, -7.86284, 1.60547, -7.72603, -10.49731, 17.45684, -15.13013, 13.57071, 4.28281, 7.19267, 6.34805, 5.45829, 1.27785, -9.59321, 2.26837, -9.40955, 0.3992, -9.67036, 1.57025, -9.55122, -13.8056, 23.73058, -8.37595, 28.75646, -20.09314, 18.54037, 2.51013, 7.7993, 4.85977, 6.59768, 4.17645, 7.04977, -0.05429, -14.75346, -1.91, -14.61785, 0.04346, -14.76015, -6.1517, 21.04165, -14.16592, 14.74048, -11.32959, 16.71646, 5.34384, 3.04042, 5.83334, 1.94427, 5.5564, 2.63522, 0.96774, -21.22797, -3.17242, -21.01077, -0.6026, -21.24004, -0.31717, 11.06647, -1.46643, 10.9747, -3.728, 10.86108, -4.46603, 17.4769, -0.66595, 19.2619, 0.62619, 16.20811, -7.2149, 13.40346, -4.37881, 11.96817, -2.0954, 12.58794, 1.69064, 6.79282, 0.85669, 7.0526, -1.41501, 6.188, -0.32318, 6.2319, 11.56635, 3.85523, 8.37149, 8.86319, 9.72693, 7.35005, 8.88318, 8.34937, 1.27908, -19.54499, -2.5401, -19.42035, -0.16718, -19.58482, -7.4751, 10.29366, -5.66028, 11.39143, 2.03659, -0.27034, 2.02533, -0.02295, 2.10391, 0.81592, 2.245, 0.46727, 2.19452, 0.71507, 8.87711, 2.9588, 8.45557, 4.01019, 6.42496, 6.80251, 7.46564, 5.64116, 6.81726, 6.408, 1.24774, -17.8658, -2.24411, -17.76696, -0.07343, -17.90805, -14.65515, -0.0051, 3.78894, -13.16698, 5.21136, -12.74122, 10.03006, -8.68135, 8.54663, -10.04483, 9.61237, -8.95421, 1.26038, -16.31182, -1.92981, -16.24511, 0.05408, -16.35919, 0.56061, -15.35699, 0, 0, 0, 0, 0, 0, 0, 0, -22.56653, -3.89294, -22.0238, -6.2881, -19.73114, -7.15436, -18.85547, -9.22702, -12.89105, -12.7695, -11.45367, -14.07718, -7.36746, -16.29301, -5.34033, -17.06413, 1.20502, -17.83984, -1.77222, -17.79289, 0.13977, -17.88109, -26.89691, -9.94032, -25.68182, -12.76437, -19.77399, -15.12938, -18.04407, -17.16081, -0.75378, -19.9846, 5.39862, -19.25554, -1.46594, -23.36191, 1.03876, -23.38521, -17.13202, -33.51227, -13.44855, -35.155, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -11.19708, -1.27014, -7.797, -2.51447, -29.30823, -7.32985, -28.35852, -10.42725], "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "curve": 0.316, "c3": 0.65, "c4": 0.35}, {"time": 3.3333, "offset": 14, "vertices": [0.2544, 0, 0.25301, 0.02648, 0.2544, 0.14967, 0.23743, 0.17533, 0.17585, 0.10604, 0.16384, 0.12375, 0, 0.13156, -0.0137, 0.13084, 0, 0.14225, -0.01481, 0.14148, 0.01297, 0.14166, 0, 0.13811, -0.01438, 0.13736, 0.01259, 0.13753, 0, -0.05258, 0.00547, -0.0523, -0.00479, -0.05236, 0.0016, -0.05256, 0, -0.08553, 0.0089, -0.08507, -0.00779, -0.08517, 0.00257, -0.08549, 0, -0.05673, 0.0059, -0.05642, -0.00517, -0.05649, 0.00172, -0.05671, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.45384, -0.64445, -0.38228, -0.68936, -0.44047, -0.59746, -0.37402, -0.64121, -0.4097, -0.49251, -0.35467, -0.53357, -0.40652, -0.44742, -0.35635, -0.4884, -0.4432, -0.38193, -0.39982, -0.42722, -0.4917, -0.30112, -0.45671, -0.35206, -0.54899, -0.20707, -0.52374, -0.26469, -0.61565, -0.0948, -0.60204, -0.1602, 0, 0, 0, 0, 0, 0, 0.33923, -0.32064, 0.28137, -0.37257, 0.31825, -0.35455, 0.25504, -0.4025, 0.3391, 0.09842, 0.3508, 0.04071, 0.60561, 0.00371, 0.57811, 0.18066, 0.60014, 0.08213, 0.60665, 0.08521, 0.59444, 0.14785, 0.52528, 0.31524, 0.5704, 0.22361, 0.01197, 0.35638, -0.02522, 0.35566, -0.12806, 0.3328, -0.395, -0.17309, -0.37484, -0.21328, -0.29613, -0.3135, -0.24263, -0.09257, -0.2317, -0.11737, -0.18728, -0.17989, -0.5383, -0.09051, -0.52608, -0.14621, -0.46035, -0.2933, -0.6887, 0.41265, -0.72802, 0.33859, 0.05735, 0.41224, 0.014, 0.4158, 0.18204, 0, 0.18105, 0.01894, 0.18204, 0, 0.26073, 0, 0.26073, 0, 0.43877, 0, 0.43877, 0, 0, 0, 0.26073, 0, 0.25331, 0, 0.23339, 0, 0.37527, 0, 0.38596, 0, 0.38386, 0.04017, 0.31113, 0, 0.30943, 0.03238, 0.43877, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.26073, 0, 0.21169, 0, 0.2544, 0, 0.2544, 0, 0.25301, 0.02648, 0.2544, 0, 0.25301, 0.02648, 0.10336, 0, 0.2544, 0, 0.2544, 0, 0.19715, 0.51748, 0.37981, 0.39038, 0.2544, 0, 0.25301, 0.02648, 0.2544, 0, 0.25301, 0.02648, 0.11855, 0, 0.1179, 0.01233, -0.24773, 0.12696, -0.25976, 0.10031, -0.27769, 0.02031, 0.15227, 0.31082, 0.11905, 0.32493, 0.01888, 0.34563, 0.25714, 0.25554, 0.22911, 0.28087, 0.13701, 0.33567, 0.36756, 0.27787, 0.3366, 0.31457, 0.23345, 0.01948, 0.23014, 0.04367, 0.15344, -0.18644, 0.17201, -0.16946, -0.20171, -0.14856, -0.18524, -0.16887, -0.12778, -0.21543, -0.08106, 0.17536, -0.09892, 0.1659, -0.14309, 0.12985, 0.01126, 0.20432, -0.01011, 0.20434, -0.06939, 0.19255, 0.24097, 0.0584, 0.23354, 0.08311, 0.04217, -0.27256, 0.07029, -0.26673, 0.01718, -0.27528, -0.11807, 0.17074, -0.13526, 0.15744, -0.17535, 0.11117, 0.10383, 0.04198, 0.09882, 0.05247, 0.0792, 0.07923, 0.19802, -0.1152, 0.20891, -0.09401, 0.03489, -0.30348, 0.06626, -0.29824, 0.00711, -0.30541, -0.26241, 0.14577, -0.27618, 0.11762, -0.2985, 0.03183, 0.13729, 0.13404, 0.12257, 0.14758, 0.07408, 0.177, 0, 0.20592, -0.02144, 0.20479, 0.01876, 0.20506, 0.02667, -0.15161, 0.04228, -0.14804, 0.01276, -0.15342, 0.03133, -0.15075, 0.00024, 0.32622, -0.09511, 0.31213, 0, 0, 0, 0, 0.02493, -0.18719, 0.04426, -0.1836, 0.00779, -0.18869, 0.03064, -0.18637, 0.16395, 0.49063, 0.25618, 0.44943, 0.01338, 0.51716, 0, 0, 0, 0, 0, 0, 0.01508, -0.44219, -0.07106, -0.43668, -0.01763, -0.44207, 0.08409, 0.48963, -0.15733, 0.4713, -0.0768, 0.49082, 0.10427, 0.05933, 0.11382, 0.03794, 0.10842, 0.05142, 0.01888, -0.41421, -0.0619, -0.40997, -0.01176, -0.41444, 0.11876, 0.13983, 0.10354, 0.15141, 0.09266, 0.25415, 0.1092, 0.34298, 0.17378, 0.31524, 0.1499, 0.21223, 0.03175, 0.25791, -0.16811, 0.42548, -0.09509, 0.44747, 0.13053, 0.12404, 0.11465, 0.13891, 0.0564, 0.17101, 0.08405, 0.15926, 0.22569, 0.07522, 0.16335, 0.17294, 0.18979, 0.14342, 0.17333, 0.16291, 0.02496, -0.38137, -0.04956, -0.37893, -0.00326, -0.38214, -0.14586, 0.20085, -0.11044, 0.22227, 0.04254, 0.02418, 0.03939, 0.02915, 0.02605, 0.04143, 0.03261, 0.03652, 0.02842, 0.03979, 0.17321, 0.05773, 0.16499, 0.07825, 0.12537, 0.13273, 0.14567, 0.11007, 0.13302, 0.12503, 0.02435, -0.3486, -0.04379, -0.34667, -0.00143, -0.34943, -0.28596, -0.0001, -0.02963, -0.11138, -0.01588, -0.11414, 0.0265, -0.11215, 0.00754, -0.115, 0.01974, -0.11354, 0.02459, -0.31828, -0.03765, -0.31698, 0.00106, -0.3192, 0.01094, -0.29965, 0, 0, 0, 0, 0, 0, 0, 0, -0.44032, -0.07596, -0.42973, -0.1227, -0.385, -0.1396, -0.36791, -0.18004, -0.25153, -0.24916, -0.22349, -0.27468, -0.14376, -0.31791, -0.1042, -0.33296, 0.02351, -0.3481, -0.03458, -0.34718, 0.00273, -0.3489, -0.52482, -0.19396, -0.50111, -0.24906, -0.38584, -0.29521, -0.35208, -0.33485, -0.01471, -0.38994, 0.10534, -0.37572, -0.0286, -0.45584, 0.02027, -0.4563, -0.33428, -0.6539, -0.26241, -0.68595, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.21848, -0.02478, -0.15214, -0.04906, -0.57187, -0.14302, -0.55334, -0.20346]}]}, "f2": {"f2": [{"vertices": [10.99312, -7.77732, 13.75286, -9.72807, 14.30099, -8.90125, -0.84401, -7.6976, -0.38988, -7.73223, -4.29254, -6.06918, -3.92816, -6.31053, -3.42208, -4.83856, -3.1316, -5.03082, -1.11949, -5.81917, -1.40771, -1.99031, -1.28821, -2.06944, -0.46054, -2.39376, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.71707, 0.88679, -3.78781, -0.50492, -3.00734, 2.45782, 1.23432, 3.23556, 1.04187, 3.30323, -3.53351, 2.49917, -3.67443, 2.28726, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.12288, -1.50158, 7.64939, -5.41218, 6.49958, -4.59749, 4.78384, -3.38392, 4.97457, -3.09616, 2.25907, -1.59803, 2.3492, -1.4622, -0.2713, 0.19167, -0.28215, 0.17581], "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "vertices": [12.63718, -8.94044, 15.80965, -11.18294, 16.43976, -10.23247, -0.97024, -8.8488, -0.44819, -8.88861, -4.9345, -6.97685, -4.51563, -7.25429, -3.93387, -5.56218, -3.59994, -5.7832, -1.28692, -6.68945, -1.61823, -2.28796, -1.48087, -2.37894, -0.52941, -2.75175, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.27297, 1.01941, -4.35429, -0.58043, -3.4571, 2.82539, 1.41891, 3.71945, 1.19769, 3.79724, -4.06195, 2.87292, -4.22396, 2.62933, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.44036, -1.72615, 8.79338, -6.22159, 7.47161, -5.28506, 5.49929, -3.89, 5.71853, -3.5592, 2.59693, -1.83702, 2.70054, -1.68088, -0.31187, 0.22034, -0.32435, 0.2021], "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.3333, "vertices": [10.99312, -7.77732, 13.75286, -9.72807, 14.30099, -8.90125, -0.84401, -7.6976, -0.38988, -7.73223, -4.29254, -6.06918, -3.92816, -6.31053, -3.42208, -4.83856, -3.1316, -5.03082, -1.11949, -5.81917, -1.40771, -1.99031, -1.28821, -2.06944, -0.46054, -2.39376, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.71707, 0.88679, -3.78781, -0.50492, -3.00734, 2.45782, 1.23432, 3.23556, 1.04187, 3.30323, -3.53351, 2.49917, -3.67443, 2.28726, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.12288, -1.50158, 7.64939, -5.41218, 6.49958, -4.59749, 4.78384, -3.38392, 4.97457, -3.09616, 2.25907, -1.59803, 2.3492, -1.4622, -0.2713, 0.19167, -0.28215, 0.17581]}]}, "f3": {"f3": [{"curve": 0.25, "c3": 0.75}, {"time": 1.4667, "vertices": [1.35477, 9.95871, -1.5119, 9.93649, 1.35477, 9.95871, -5.41342, 8.46883, -1.5119, 9.93649, 1.35477, 9.95871, -5.41342, 8.46883, -1.5119, 9.93649, -5.41342, 8.46883, -5.41342, 8.46883, -5.50349, 8.41011, -5.41342, 8.46883, 2.45328, 14.83176, -9.80925, 11.39243, 9.13055, 10.78362, 7.40881, 12.03157, 0.72107, 14.11162, -10.844, 9.05811, 2.52449, 14.04448, -9.15935, 10.94232, 15.00571, 14.7273, -1.68176, 20.95926, 8.73132, 6.98787, -1.00793, 11.13772, 3.58755, 6.91557, -3.78662, 6.80848, -3.63786, 4.54544, -5.79814, -0.52477, -2.9124, 3.63907, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.96967, -8e-05, -2.91949, -0.54213, -2.84612, -0.84711, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.19298, 2.01783, -2.33597, 1.85071, -2.20978, 4.01757, -2.50019, 3.84372, -3.17737, 3.30637, -2.71503, 6.61983, -3.90924, 5.99278, -3.01917, 9.52413, -4.75296, 8.78849, -8.14578, 5.78752, -3.21934, 11.24631, -5.2728, 10.4426, -9.3429, 7.04176, -5.90849, 12.45985, -10.80429, 8.57097, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -76.68669, 64.0303, -97.07861, 23.58826, -85.98767, -50.86487, -116.90405, 23.36718, -100.03587, -64.8533, -61.43185, 16.12469, -55.24075, -31.32965, -43.99423, 11.20445, -39.32111, -22.69733, -25.92947, -4.76667, -15.23962, -21.51721, -18.20782, 2.63512, -14.87672, -10.8269, -14.57692, 1.37207, -13.77454, 4.94836, -12.85365, 5.91019, -10.97812, 8.91678, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -21.75632, -5.44043, -20.88672, -8.19409, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.66847, 2.33787, 2.28729, 2.71179, 0.7041, 3.47714, -2.35557, 2.65277, -1.94261, 2.96866, -1.91089, 2.98944, 8.36539, 2.37849, 6.1951, 6.10352, -1.1537, 8.61978, -0.44926, 7.77782, -0.36734, 7.78107, -0.53342, -8.35733, 3.54553, -7.58702, 8.19426, -1.7276, 6.0309, -4.56953, 5.97983, -4.63557, 3.5863, -6.66362, 6.24445, -4.4248, 3.91238, -6.5777, 2.67419, -4.49603, 3.186, -5.35622, 4.16205, -6.99708, 2.01761, -7.88747, 3.66302, -6.1584, 1.77576, -6.94206, 0, 0, 0, 0, 0, 0, 0, 0, 1.35477, 9.95871, 1.35477, 9.95871, 1.35477, 9.95871, -2.3027, 7.7258, -4.41107, 6.74815, -4.90003, -3.53656, -5.512, -4.54832, -3.68695, -4.78656, -3.85898, -3e-05, -3.794, -0.70453, -3.69854, -1.10097, -9.6082, -6e-05, -9.4464, -1.75428, -8.53724, -1.62814, 0, 0, -2.20434, 7.40498, -0.65201, -3.88686, 1.07649, -3.61612, -17.24944, 10.03233, -18.79084, 6.7146, -9.40588, -1.27608, -9.37384, -3.01926, -12.83112, 0.13274, 0, 0, 0, 0, 2.67311, 8.85684, -1.87343, 2.69131, -2.45589, 2.17325, -0.83241, 2.79645, -2.79234, 0.84911, -22.0185, 18.11145, -23.29462, 16.43835, -24.1809, 14.39274, -10.16489, -0.04899, -10.13341, -0.79855, -10.3442, -1.95139, -9.98581, 2.72645, -10.15959, 1.98257, 0, 0, 0, 0, 7.08722, 9.13103, 4.73243, 10.54242, 0.2406, 11.54202, -5.11369, 9.73741, -6.84401, 6.40254, -8.16283, 4.60686, -6.16698, 5.96205, -8.37634, -1.85197, -23.01957, 17.89194, -23.91334, 15.84773, -26.97931, 9.75424, -26.3125, 12.55731, -24.99687, -15.01173, -11.3439, 2.8825, -11.52543, 2.03796, -12.03847, 0.71574, -11.86855, -2.14815, -11.95142, -0.30103, -11.8965, -1.18173, 0, 0, 0, 0, 4.91992, 10.62638, 0.38045, 11.69234, -1.37628, 11.61459, -6.78549, 9.52768, -11.70305, 0.29178, -5.05391, 9.93393, -10.86252, 2.49167, 7.93211, 3.17136, 6.06612, 6.01528, 5.09579, 6.85565, 1.1774, 8.46136, -6.06092, 6.0184, 2.48944, 8.18298, -4.6828, 7.15771, 5.22897, 3.74288, 3.36014, 5.47835, 2.50214, 5.91803, -0.64783, 6.39339, -5.50983, 3.31253, -4.85689, 4.37924, -4.80829, 4.43176, -2.48747, 12.6242, -4.35275, 12.10815, -9.6337, 8.52998, -12.6075, -2.57114, -14.32704, -0.21242, -14.33014, -0.05978, 5.92079, 4.73856, -0.6835, 7.5526, 2.2074, 5.84652, 1.30569, 6.11173, -1.79025, 5.98752, -5.86116, 2.16852, -0.56317, 6.22385, -0.68143, 7.52921, -6.21495, 4.30469, 2.81909, 7.46683, 1.66714, 7.80545, -2.28647, 7.6468, -7.48543, 2.76947, -6.56131, 4.54454, -6.51212, 4.61433, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -9.61347, 2.12395, -9.84509, -0.20147, -8.99213, -4.02854, -5.8555, -6.28148, 1.78757, -3.382, 2.96362, -2.41009, 3.28433, -1.93801, 3.81827, -0.11888, 2.39562, 2.97696, 3.4046, -0.38818, 2.48242, 2.36693, 4.90938, 1.24003, 4.02602, 3.06624, 3.5144, 3.63639, 1.34238, 4.88011, -3.0872, 4.00433, 1.36058, 4.83801, -2.83896, 4.14995, -2.80362, 4.18018, 4.60336, 1.49293, 3.64978, 3.17544, 3.12732, 3.68669, 0.97729, 4.73833, -3.19624, 3.62515, 1.24612, 4.66655, -2.78078, 3.95271, -2.74747, 3.98143, 0.05477, 6.88528, -2.64105, 6.36925, -3.56891, 5.90364, -5.95972, 3.46501, -6.33937, -2.69147, -5.18378, 4.78827, -6.99788, -0.90283, -7.01727, -0.82841, 2.10995, -0.79286, 2.20148, -0.4682, 2.16037, 0.64925, 0.78015, 2.10944, 1.46706, 2.60797, 1.48676, 2.59415, 0.58093, -6.0842, 1.48343, -5.92877, 4.15283, -4.4852, 6.08129, 0.61481, 6.4931, 0.35606, 6.48862, 0.28822, -2.66133, -8.86774, -1.30368, -9.16728, 3.2627, -8.66479, 8.88657, -2.60767, 8.53606, -3.01421, 8.49521, -3.10449, -0.5751, -18.72859, 2.23666, -18.60381, 10.90085, -15.24077, 18.7334, -0.45676, 9.3911, -16.21107, 18.46391, -3.18323, 18.42044, -3.37926, 1.56676, -19.04567, 4.40182, -18.59609, 12.79651, -14.1945, 19.03618, 1.68753, 11.37775, -15.34995, 19.07668, -1.10655, 19.05606, -1.30838, -1.93709, -16.17683, 4.54985, -15.64698, 6.84183, -14.78841, 13.10803, -9.68298, 15.61779, 4.64828, 12.11794, -10.88871, 16.12791, 2.32172, 16.14389, 2.15088, 4.20234, -10.50208, 7.97826, -8.02173, 9.08743, -6.73494, 11.20932, -1.5405, 7.96996, 8.0253, 11.00534, -2.60277, 9.05585, 6.78067, 9.12021, 6.68561, 5.26259, -3.68392, 6.28685, -1.326, 6.40836, -0.36721, 5.80286, 2.75844, 1.2904, 6.28836, 5.91206, 2.50652, 1.86829, 6.14688, 11.69005, 3.77209, 9.28783, 8.04723, 7.96994, 9.34926, 2.50656, 12.03171, -8.10517, 9.22305, 3.81952, 12.00838, -6.76451, 10.63377, 15.15369, 5.61727, 11.74637, 11.0979, 9.94363, 12.73348, 2.61105, 15.94749, -11.17435, 11.66531, 4.11484, 15.62598, -9.35109, 13.18013, 0.41441, -5.6072, 1.96362, -5.26037, 3.86345, -4.0757, 0.71848, -9.72162, 2.52831, -9.41432, 3.651, -9.02506, 6.88409, -6.88582, -2.40187, -12.21446, -0.06966, -12.44779, 6.29135, -10.8353, 7.84012, -9.77167, 11.59268, -3.15034, 11.79025, 2.29321, 11.47084, 3.49053, 10.81342, 5.16826, 10.54182, -10.74227, 14.19952, -4.98351, 13.64301, 6.32562, 14.78583, 2.74326, 14.61441, -3.5056, 14.96973, -1.27919, 10.34323, -13.35991, 15.17557, -7.42013, 16.0459, 5.24988, 16.85608, 1.10422, 17.44461, -2.88751, 16.69171, 5.85168, 6.39507, 0.49034, 5.51898, 3.26452, 1.67369, 6.17459, 3.16367, 5.56625, 5.69495, 5.00779, 2.5936, 7.1315, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.04297, 10.7009, -7.4729, 9.1714, 2.30207, 6.49196, -3.37856, 6.00246, -6.77266, 1.25601, 4.87349, -1.08597, 5.54471, 2.88934, 5.04419, 3.68548, 6.48947, -4.06114, 7.61407, -0.77582, 8.64317, 1.25804, 8.35095, 2.53586, -0.50182, 2.4815, -1.54785, 2.00861, -2.5127, 0.35263, -2.33154, 0.96417, -1.03488, 2.97155, -2.42284, 0.59194, -2.43652, -0.53582, -1.36836, -2.09259, -1.82901, -1.68805, -1.06784, 0.27555, -1.06393, -0.26884, 1.18561, 1.03999, 0.12277, 1.57233, 0.50964, 1.49258, 0.14975, 1.5699, -17.74199, 4.61554, -16.02856, 8.87573, -17.04956, 6.73961, -8.76521, 16.30933, -11.11697, 14.80849, -16.85864, 7.65326, -16.24924, -8.8783, -17.37398, -6.40381, -17.44785, -6.21463, -15.30234, 10.93936, -16.59509, 8.87967, -17.07093, -7.92969, -15.14172, 11.87631, -16.55655, 9.8298, -17.80688, -7.32812, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.72442, -1.22313, -2.94846, -2.58384, -2.52814, -2.99712, -0.77765, -3.84241, 2.60352, -2.93243, -1.14139, -3.75071, 2.14684, -3.28027, 2.11011, -3.3028, -2.4414, -0.80145, -1.9328, -1.69352, -1.65744, -1.9646, -0.51001, -2.51869, 1.70666, -1.92252, -0.74881, -2.45852, 1.40698, -2.15034, 1.38239, -2.1649, -0.91698, -0.30067, -0.726, -0.63619, -0.62314, -0.73805, -0.19138, -0.94604, 0.64125, -0.72266, -0.28194, -0.92344, 0.52838, -0.80771, 0.56241, 0.18483, 0.44525, 0.38976, 0.11761, 0.58011, -0.39254, 0.44186, 0.17168, 0.56619, -0.32408, 0.49532, 1.77863, 0.58399, 1.40829, 1.23311, 0.54459, 1.79065, -1.02472, 1.56633, 5.19006, 2.6445, 4.73468, 3.39133, 2.52509, 5.24963, -2.68557, 5.16382, -1.63316, 5.48543, -1.57321, 5.50348, 2.10246, 1.84132, 1.80179, 2.13589, 0.5553, 2.73911, -1.85509, 2.08932, -1.53008, 2.33885, -1.5062, 2.35533, 1.34642, 1.17882, 1.15366, 1.36732, 0.35599, 1.7538, -1.18752, 1.33762, -0.97963, 1.49767, -0.96472, 1.50854, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9.06844, 4.82713, 8.24197, 6.13354, 4.28333, 9.33736, -4.88758, 9.03607, -3.96228, 9.10959, -3.86572, 9.15039, 7.04001, 5.64086, 6.1138, 6.63337, 2.17694, 8.75433, -5.68758, 7.00214, -4.64951, 7.68373, -4.56757, 7.73282, 3.00208, 2.63009, 2.57312, 3.05073, 0.79202, 3.91176, -2.65, 2.98428, -2.18544, 3.33969, -2.14978, 3.36307, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.27849, -0.24475, -0.23929, -0.28382, -0.0726, -0.36351, 0.24662, -0.27786, 0.20332, -0.31006, 0.19867, -0.31165, -1.56998, -1.37589, -1.34637, -1.59623, -0.41382, -2.04619, 1.38673, -1.5618, 1.14313, -1.74669, 1.12323, -1.75851, -2.23735, -1.9605, -1.91907, -2.27417, -0.59015, -2.9156, 1.97558, -2.22501, 1.62888, -2.48906, 1.60092, -2.50601, -2.82408, -2.47451, -2.42151, -2.87041, -0.745, -3.68018, 2.49336, -2.80862, -1.09352, -3.59225, 2.05605, -3.14178, 2.02084, -3.16321, -2.93379, -2.57072, -2.51593, -2.98202, -0.77383, -3.82306, 2.59037, -2.91751, -1.13599, -3.73191, 2.13593, -3.26389, 2.09955, -3.2863, 14.03259, 3.87616, 11.39616, 9.05713, 9.90424, 10.6635, 3.57028, 14.11211, -9.13098, 11.32947, 4.89488, 13.70776, -7.37904, 12.54842, 2.96367, -0.67661, 2.97746, 0.54934, 2.85202, 0.99117, 2.03244, 2.2419, -0.55669, 2.97995, 1.22978, 2.28829, -0.96655, 2.41321, 10.25481, -5.58823, 11.61479, -1.14043, 11.64532, 0.61132, 9.927, 6.13437, 1.05701, 11.62323, 10.22908, 4.51577, 3.09526, 10.74768, 3.20428, 10.71365, 17.23608, -10.72311, 20.05945, -3.15515, 20.29904, -0.11844, 17.86719, 9.65065, 2.99825, 20.07315, 19.41814, 6.53317, 7.4404, 19.09305, 7.6427, 19.01212, 8.18606, -10.2836, 11.56888, -6.27928, 12.3746, -4.47814, 13.01028, 2.02025, 6.19, 11.59692, 13.91118, 0.2191, 8.75427, 10.81961, 8.86572, 10.72729, -2.55283, -16.99791, 4.30244, -16.64666, 6.74597, -15.81482, 13.51691, -10.62836, 16.61694, 4.40915, 12.38681, -12.06269, 17.20119, 1.77525, 17.21185, 1.592, -3.99287, -17.16446, 3.04463, -17.36049, 5.61087, -16.70801, 12.94955, -11.95821, 17.34111, 3.15421, 11.74379, -13.13857, 17.61424, 0.59227, 17.61157, 0.40538, 1.93338, -4.763, 2.27959, -4.60745, -4.53854, -0.53678, -3.34262, -2.53155, -2.82848, -1.41108, -2.51357, -1.91677, -1.40857, -2.82973, -2.86938, -5.62226, 0.55775, -6.29006, 1.4917, -6.13673, -2.45366, -1.85825, -1.38074, -2.75072, -1.0869, -2.87935, -0.64212, -3.00969, -3.26921, -2.47595, -1.83966, -3.66493, 1.24232, -3.90823, 0.23181, -4.09329, -1.44817, -3.83647, -0.85553, -4.01014, 1.17471, -3.92871, -4.56212, -3.45514, -2.56711, -5.11469, 1.73334, -5.45395, 0.32422, -5.71326, -1.1947, -5.59649, 1.6394, -5.48282, -3.28381, -2.48703, -1.84775, -3.68152, 1.24773, -3.92581, 0.23337, -4.11218, -0.85974, -4.02832, 1.18008, -3.94647, -9.34192, 2.18439, -9.525, 1.1478, -9.52654, -1.13404, 6.60794, 3.26923, 5.877, 4.45103, 5.09606, 5.32762, 2.60405, 6.89725, 8.52428, 4.21737, 7.58134, 5.74189, 6.5739, 6.87271, 3.35922, 8.8975, -3.05321, 8.63542, -7.18108, 5.70594, -7.95822, 4.5674, 1.40602, 13.89711, -4.87393, 13.093, -6.20222, 12.53633, -8.01425, 11.46809, 5.80208, 13.03453, -0.55081, 14.26044, -2.03409, 14.14327, -4.13528, 13.68169, 7.12589, 5.39695, 4.00961, 7.98943, -2.70728, 8.51926, -0.50714, 8.9249, 3.15738, 8.36293, 1.86685, 8.74212, -2.56079, 8.56454, -0.26291, 11.85606, -5.47299, 10.52455, -5.91101, 10.50639, -7.4252, 9.50131, -5.44011, 5.04408, -7.10889, 2.12468, -6.85608, 2.01756, -7.08362, 0.96523, -3.95834, 4.37102, -5.47983, 2.17647, -5.74738, 1.3291, -3.41185, 3.12202, -3.93762, 2.42626, -4.06307, 2.20875, -4.60324, 0.44051, -3.9377, 1.3433, -4.12021, 0.58043, -4.14465, 0.35581, -3.95285, -1.29655, -4.68344, -0.10933, -4.66273, -0.45432, -1.67576, 4.03434, -1.96893, 3.89985, -2.66603, 3.46115, -6.03951, 16.83537, -12.85428, 12.43869, -17.88402, -0.07101, -19.40414, 8.05772, -19.52121, -7.78049, -17.38943, 10.65948, -19.8933, -4.51126, -5.88541, -1.13214, -3.42567, -4.92157, 0, 0, 0.98184, -8.67551, 6.75381, -5.53531, 5.16736, -7.03769, 3.1131, -8.15728, 6.65009, -5.65794, -0.95669, -3.11005, -0.35832, -3.23467, -0.87664, -0.36638, -0.79442, -0.52473, -0.48224, -0.82118, 6.32968, -6.71201, 7.47456, -5.40563, 9.09225, -1.54997, 14.60782, -13.08319, 18.88107, -5.28912, 20.76331, -6.50784, 7.14795, -4.74361, -3.81342, -3.73506, -1.77341, -5.03345, 2.23065, -4.84888, 0.957, -5.24991, -0.48325, -5.31318, 2.13077, -4.89093, -4.67255, -0.33693, -4.04294, -2.36402, -1.25163, -4.5166, -2.3349, -4.06375, -3.33313, -3.29717, -1.33792, -4.49216, -0.55125, -6.91077, -2.24942, -6.55765, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15.64491, -14.77231, 18.13809, -11.57464, 9.03104, -12.50341, 11.21613, -10.58689, 14.73846, -4.54248, 5.0791, -9.03083, 8.54495, -5.8569, 0.45392, -7.17821, 3.5773, -6.23718, -2.86736, -3.22088, -1.15048, -4.15405, -1.15102, -4.15515, -0.51773, -4.28181, -5.99696, 0.92057, -5.78732, -1.82074, -2.86862, -5.34576, -4.09512, -4.46622, -5.45169, -2.66774, -3.49838, -4.95584, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -9.95694, 5.0134, -10.62584, -3.36389, -11.1171, -0.621, -10.59641, 3.46631, -10.95572, -2.04572, -14.724, -0.96411, -14.48969, -2.84966, -18.57803, -2.28192, -18.14117, -4.6526, -2.22573, 9.92815, -4.04932, 9.33425, -7.75595, 6.58771, -2.12717, 12.54199, -4.44318, 11.92011, -9.25128, 8.73386, -1.71939, 16.94477, -9.0264, 14.44551, 0.83228, 20.73444, -8.41098, 18.97203, -19.26381, 7.70831, 6.07489, 12.10461, -4.10162, 12.9071, 4.71207, 8.34505, -2.45235, 9.26401, -1.50639, 4.74072], "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "x3": {"x1": [{"vertices": [-5.52033, 6.21382, -4.33983, 4.88466, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.58613, -9.08082, -7.0602, -6.27077, -2.58613, -9.08082, -7.0602, -6.27077, -5.14888, -10.00795, -9.72183, -5.67598, -3.21757, -11.29851, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.44708, -1.72092, -0.48688, -1.70997, -1.32936, -1.18071, 0.50962, -1.9617, -0.55504, -1.94883, -1.5156, -1.34578, -0.96495, -3.17845, -2.52352, -2.16304, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -7.71761, 8.68649, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16.09271, -8.03076, 0, 0, 0, 0, 18.04952, -7.75439, 17.61622, -8.69025, 0, 0, 0, 0, 18.63858, -7.79201, 0, 0, 0, 0, 18.5697, -5.71735, 0, 0, 0, 0, 18.44935, -3.1177, 0, 0, 0, 0, 18.26859, -0.53152, 0, 0, 0, 0, 17.88788, 2.10186, 17.37068, -4.77104, 0, 0, 0, 0, 3.25709, -1.013, 0, 0, 3.96099, -5.51076, 0.62186, -6.75471, 3.26129, 0.18832, 2.90717, -1.48896, 4.2372, -9.61859, -1.22232, -10.43553, 2.37739, -4.77065, -0.36858, -5.3156, 4.77088, -12.51613, -2.23087, -13.20415, 1.96127, -10.08531, -3.42129, -9.68579, 6.22478, -16.5078, -3.00112, -17.38174, -11.8718, -13.04978, 2.65878, -15.18365, -5.40425, -14.43399, -3.24347, -20.61631, -13.81398, -15.6484, -7.14852, -17.68949, -1.66482, -17.85034, -10.99724, -14.16333, -3.09106, -10.85345, -8.43798, -7.4946, 4.1585, -5.79803, 0.38643, -7.12436, -1.60878, -5.64923, -4.39193, -3.90116, 0.79889, -4.64651, 0, 0, 4.58805, 0.24337, 3.99381, -2.25926, 0, 0, 1.39388, 0.99178, 0, 0, -0.23498, 3.23967, 0, 0, -1.8296, 5.79036, 0, 0, -3.19735, 7.73718, 0, 0, -4.3567, 9.22666], "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.2, "vertices": [-5.98218, 6.73369, -4.70291, 5.29332, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.80249, -9.84055, -7.65088, -6.79539, -2.80249, -9.84055, -7.65088, -6.79539, -5.57965, -10.84525, -10.53519, -6.15085, -3.48676, -12.24377, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.48448, -1.8649, -0.52762, -1.85303, -1.44058, -1.2795, 0.55226, -2.12582, -0.60147, -2.11188, -1.6424, -1.45837, -1.04568, -3.44437, -2.73465, -2.34401, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -8.36328, 9.41322, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17.43907, -8.70264, 0, 0, 0, 0, 19.55959, -8.40314, 19.09003, -9.4173, 0, 0, 0, 0, 20.19793, -8.44391, 0, 0, 0, 0, 20.12329, -6.19568, 0, 0, 0, 0, 19.99287, -3.37854, 0, 0, 0, 0, 19.79699, -0.57599, 0, 0, 0, 0, 19.38443, 2.27771, 18.82396, -5.1702, 0, 0, 0, 0, 3.52959, -1.09775, 0, 0, 4.29237, -5.9718, 0.67389, -7.31982, 3.53413, 0.20407, 3.15039, -1.61353, 4.59169, -10.42331, -1.32458, -11.30859, 2.57629, -5.16977, -0.39941, -5.76031, 5.17003, -13.56326, -2.41751, -14.30884, 2.12535, -10.92908, -3.70752, -10.49612, 6.74556, -17.88889, -3.2522, -18.83594, -12.86502, -14.14156, 2.88123, -16.45395, -5.85638, -15.64157, -3.51483, -22.34113, -14.9697, -16.95758, -7.74658, -19.16943, -1.80411, -19.34375, -11.9173, -15.34827, -3.34967, -11.76147, -9.14392, -8.12161, 4.50641, -6.28311, 0.41876, -7.7204, -1.74338, -6.12186, -4.75937, -4.22754, 0.86572, -5.03525, 0, 0, 4.97189, 0.26373, 4.32794, -2.44827, 0, 0, 1.5105, 1.07475, 0, 0, -0.25464, 3.51071, 0, 0, -1.98267, 6.2748, 0, 0, -3.46484, 8.38449, 0, 0, -4.72119, 9.99858], "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 3.3333, "vertices": [-5.52033, 6.21382, -4.33983, 4.88466, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.58613, -9.08082, -7.0602, -6.27077, -2.58613, -9.08082, -7.0602, -6.27077, -5.14888, -10.00795, -9.72183, -5.67598, -3.21757, -11.29851, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.44708, -1.72092, -0.48688, -1.70997, -1.32936, -1.18071, 0.50962, -1.9617, -0.55504, -1.94883, -1.5156, -1.34578, -0.96495, -3.17845, -2.52352, -2.16304, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -7.71761, 8.68649, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16.09271, -8.03076, 0, 0, 0, 0, 18.04952, -7.75439, 17.61622, -8.69025, 0, 0, 0, 0, 18.63858, -7.79201, 0, 0, 0, 0, 18.5697, -5.71735, 0, 0, 0, 0, 18.44935, -3.1177, 0, 0, 0, 0, 18.26859, -0.53152, 0, 0, 0, 0, 17.88788, 2.10186, 17.37068, -4.77104, 0, 0, 0, 0, 3.25709, -1.013, 0, 0, 3.96099, -5.51076, 0.62186, -6.75471, 3.26129, 0.18832, 2.90717, -1.48896, 4.2372, -9.61859, -1.22232, -10.43553, 2.37739, -4.77065, -0.36858, -5.3156, 4.77088, -12.51613, -2.23087, -13.20415, 1.96127, -10.08531, -3.42129, -9.68579, 6.22478, -16.5078, -3.00112, -17.38174, -11.8718, -13.04978, 2.65878, -15.18365, -5.40425, -14.43399, -3.24347, -20.61631, -13.81398, -15.6484, -7.14852, -17.68949, -1.66482, -17.85034, -10.99724, -14.16333, -3.09106, -10.85345, -8.43798, -7.4946, 4.1585, -5.79803, 0.38643, -7.12436, -1.60878, -5.64923, -4.39193, -3.90116, 0.79889, -4.64651, 0, 0, 4.58805, 0.24337, 3.99381, -2.25926, 0, 0, 1.39388, 0.99178, 0, 0, -0.23498, 3.23967, 0, 0, -1.8296, 5.79036, 0, 0, -3.19735, 7.73718, 0, 0, -4.3567, 9.22666]}]}, "s1": {"s1": [{"curve": 0.25, "c3": 0.75}, {"time": 1.4667, "vertices": [44.39878, 87.30377, 60.12927, 47.2822, 76.10037, -7.61688, 50.36063, 58.14166, 59.47063, 42.02142, 69.13763, -23.73498, 71.97931, -10.95171, 50.75889, 52.80756, 57.73048, 31.54681, 59.59903, -28.50813, 63.46286, -17.29376, 56.66338, 22.85748, 51.90662, -32.77215, 56.66953, -22.8159, 56.27985, 18.63806, 53.46777, -25.59103, 21.33201, 52.59396, 55.69012, 10.78116, 24.30357, 40.17242, 46.89732, 1.52719, 27.17048, 25.09607, 35.82179, -9.08673, 29.8514, 9.39453, 24.12123, -19.88455, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -8.18138, -1.48532, 6.68823, 4.94102, -10.3317, -1.37997, 8.31177, 6.0083, -15.40531, 7.53827, 16.99789, 0.4859, -19.5994, 14.20087, 20.21683, 13.09051, 23.84824, -3.44418, 23.65546, 4.35398, -22.64996, 17.8114, 28.25397, -5.20918, 28.39966, 4.09834, -28.53268, 24.12738, 36.44327, -8.04655, 37.07687, 4.04108, -35.93859, 33.10266, 47.23199, -12.54181, 48.75253, 3.24297, 45.91449, 16.69264, 60.496, 2.6079, 57.29218, 19.368, 65.41254, 2.48006, 62.02631, 20.61319, 0.23404, 65.44135, 69.72501, -1.61546, 67.27579, 17.89149, 4.50984, 69.59186, 72.15689, 13.88626, 9.87405, 72.9935, 95.53577, -6.62651, 36.79716, 89.05594, 40.05784, 91.36438, -1.867, 0.70468, 1.98651, 0.19086, 0, 0, 0, 0, 0.46969, 6.49347, 2.42145, -6.04991, 12.24364, 7.24045, 12.72705, -6.29913, 0.15002, 19.20059, 16.16589, 10.35056, 15.10229, 22.29663, 26.90314, -0.49637, 15.35553, -22.38158, -0.80474, 29.7796, 24.51326, 16.91205, 28.08234, -10.15345, 14.47379, -26.11069, 12.97842, 34.62366, 36.07875, 7.99704, 27.67554, -24.82204, 4.51624, -36.88208, -11.01116, 31.82327, 20.66978, 26.57941, 33.65976, -1.30045, 24.49594, -23.11908, -0.49192, 39.67026, 32.97343, 22.03616, 37.24399, -14.00275, 18.83698, -35.03552, 13.8569, 48.92435, 48.54359, 15.04892, 45.40408, -22.81371, 43.6218, 30.62595, 52.65726, -8.17525, 48.82547, -4.98002, 33.48749, -35.86682, 43.21393, -23.29541, 48.80014, 3.85576, -21.64707, 29.30777, 32.76251, -15.88335, 36.10526, -4.57436, 49.41719, 36.65543, 58.8446, -18.73321, 47.22278, -39.8493, 61.0137, -7.84879, 56.30457, -3.56734, 40.04669, -39.72372, 50.67062, -24.84966, 55.44275, -9.57256, 55.87836, 6.55751, 48.74579, -3.61758, 47.79166, 10.14537, 69.14307, -4.85832, 44.09157, 53.96939, 65.36166, 8.80971, 30.61907, 58.70721, 62.1506, 6.70546, 13.59093, 61.14352, 71.30463, -21.81189, 74.57251, -0.67201, 43.70502, 60.89194, 80.6723, -14.85428, 81.58148, 8.66794, 39.97609, 72.13162, 89.50363, -9.82735, 88.62534, 16.00279, 37.95331, 82.23236, 37.73315, -31.02856, 45.68436, -17.34347, 53.24854, -4.51431, 37.12317, -38.42535, 47.48233, -24.55602, 53.05023, 5.09145, 46.59016, 33.86868, 54.90948, -18.08027, 43.83258, -37.7415, 57.0437, -7.89668, 61.04349, -6.14349, 54.06815, -29.04516, 59.8432, -12.63483, 60.96991, 4.87044, 51.71906, 40.56702, 63.38107, -18.29504, 51.58524, -41.17826, 65.38379, -6.6263, 69.35541, -9.9167, 69.32068, 10.1825, 31.73586, 62.77762, 56.39343, 47.46185, 73.53345, -4.89748, 46.63129, 57.58154, 71.53464, 8.09479, 15.20712, 70.56665, 65.43689, 4.49005, 16.74416, 63.58469, 61.0058, -2.86777, 59.28363, 14.28508, 5.40837, 60.81683, 51.03516, -2.62809, 49.70108, 11.72472, 88.72836, -5.76845, 86.72708, 19.6758, 33.8119, 82.76654, 78.52576, -10.92232, 78.40363, 11.82883, 35.53366, 71.29849, 69.15887, -14.5061, 70.43756, 5.72449, 36.05743, 61.13748], "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "f6": {"f6": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "offset": 118, "vertices": [1.15732, 5.97797, 3.30726, 5.11249, 5.61233, 2.36124, -3.92279, 4.65648, -4.18579, 4.4214, -3.677, 4.85251, 3.09717, 4.78778, -3.67371, 4.36073, -3.91992, 4.14047, -3.44348, 4.54425, 1.98435, 3.0676, -2.35373, 2.79398, -2.51154, 2.65286, -2.2063, 2.91158, 3.12827, 4.83594, -3.71066, 4.40468, -3.95947, 4.18239, -3.47821, 4.59024, 4.18925, 6.47586, -4.96902, 5.89838, -5.30225, 5.60077, -4.65765, 6.14687, 2.06782, 3.19598, -2.61627, 2.7623, -2.29718, 3.03197, 5.11261, 7.90344, -6.06448, 7.19865, -6.4711, 6.83537, -5.68451, 7.50194, 4.42, 6.83286, -5.24286, 6.22346, -5.59433, 5.90936, -4.91443, 6.48561, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.53211, -2.15274, 2.21729, 0.00391, 2.20721, -0.23764, -0.14597, 0.44696, -0.17276, 0.43533, 0, 0, 0, 0, 4.69256, 7.25372, -5.56561, 6.60658, -5.93909, 6.27284, -5.21667, 6.88467, 0.44531, -3.30589, 0.85596, 4.42142, 2.44609, 3.78134, -2.90146, 3.44406, -3.09586, 3.27014, -2.71973, 3.58907, 2.96042, -3.51398, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.06782, 3.19598, -2.45117, 2.9104, -2.61627, 2.7623, -2.29718, 3.03197, 2.06782, 3.19598, -2.45117, 2.9104, -2.61627, 2.7623, -2.29718, 3.03197, 0.72336, 3.73764, 2.06782, 3.19598, -2.45117, 2.9104, -2.61627, 2.7623, -2.29718, 3.03197], "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "f5": {"f5": [{}, {"time": 1.6667, "offset": 70, "vertices": [-4.12225, -4.83145, -2.19412, -5.95999, -2.41953, 1.45325, -2.77356, 0.52539, -2.7233, -2.36334, -1.73691, -3.1601, 0.99368, -2.90317, 1.93689, -2.37886, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.00079, -9.22491, -1.5098, -10.38063, 0.24847, -8.45715, 3.14865, -7.84192, 2.93076, -5.56677, 4.66821, -4.20332, -0.07086, -0.05573, -0.05502, -0.0737, 1.6449, 0.40137, -2.32831, -8.83337, 0.86157, -9.08772, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.28708, -7.38184, 4.68863, -6.12688, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.892, -0.59726, -0.63745, -0.86615, 0, 0, 0, 0, 0.58853, 0.11963, 0.50311, 0.31921]}, {"time": 3.3333}]}, "x2": {"x1": [{"vertices": [-3.43026, 3.86118, -2.69671, 3.03526, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.60698, -5.64269, -4.38711, -3.89657, -1.60698, -5.64269, -4.38711, -3.89657, -3.19944, -6.2188, -6.04101, -3.52697, -1.99935, -7.02073, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.27781, -1.06936, -0.30254, -1.06255, -0.82605, -0.73368, 0.31667, -1.21897, -0.34489, -1.21098, -0.94177, -0.83625, -0.59961, -1.97504, -1.56808, -1.34408, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.79561, 5.39766, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9.99978, -4.9902, 0, 0, 0, 0, 11.21571, -4.81847, 10.94647, -5.4, 0, 0, 0, 0, 11.58175, -4.84184, 0, 0, 0, 0, 11.53895, -3.55268, 0, 0, 0, 0, 11.46417, -1.9373, 0, 0, 0, 0, 11.35184, -0.33028, 0, 0, 0, 0, 11.11528, 1.30607, 10.7939, -2.96466, 0, 0, 0, 0, 2.02391, -0.62946, 0, 0, 2.4613, -3.42431, 0.38642, -4.19728, 2.02652, 0.11702, 1.80647, -0.92522, 2.63293, -5.97686, -0.75953, -6.48449, 1.47728, -2.96441, -0.22903, -3.30304, 2.96456, -7.77735, -1.38623, -8.20487, 1.2187, -6.26687, -2.12594, -6.01861, 3.86799, -10.25771, -1.86485, -10.80076, -7.37697, -8.10895, 1.65213, -9.4349, -3.35812, -8.96907, -2.01545, -12.81068, -8.58381, -9.72369, -4.44199, -10.99199, -1.0345, -11.09195, -6.83353, -8.80089, -1.92074, -6.74418, -5.24324, -4.65703, 2.58403, -3.60282, 0.24012, -4.42697, -0.99967, -3.51035, -2.72908, -2.42412, 0.49642, -2.88727, 0, 0, 2.85095, 0.15123, 2.4817, -1.40387, 0, 0, 0.86614, 0.61628, 0, 0, -0.14601, 2.01309, 0, 0, -1.13689, 3.59805, 0, 0, -1.98679, 4.80777, 0, 0, -2.70719, 5.73331], "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 1.0333}, {"time": 2.5, "vertices": [-5.98218, 6.73369, -4.70291, 5.29332, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.80249, -9.84055, -7.65088, -6.79539, -2.80249, -9.84055, -7.65088, -6.79539, -5.57965, -10.84525, -10.53519, -6.15085, -3.48676, -12.24377, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.48448, -1.8649, -0.52762, -1.85303, -1.44058, -1.2795, 0.55226, -2.12582, -0.60147, -2.11188, -1.6424, -1.45837, -1.04568, -3.44437, -2.73465, -2.34401, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -8.36328, 9.41322, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17.43907, -8.70264, 0, 0, 0, 0, 19.55959, -8.40314, 19.09003, -9.4173, 0, 0, 0, 0, 20.19793, -8.44391, 0, 0, 0, 0, 20.12329, -6.19568, 0, 0, 0, 0, 19.99287, -3.37854, 0, 0, 0, 0, 19.79699, -0.57599, 0, 0, 0, 0, 19.38443, 2.27771, 18.82396, -5.1702, 0, 0, 0, 0, 3.52959, -1.09775, 0, 0, 4.29237, -5.9718, 0.67389, -7.31982, 3.53413, 0.20407, 3.15039, -1.61353, 4.59169, -10.42331, -1.32458, -11.30859, 2.57629, -5.16977, -0.39941, -5.76031, 5.17003, -13.56326, -2.41751, -14.30884, 2.12535, -10.92908, -3.70752, -10.49612, 6.74556, -17.88889, -3.2522, -18.83594, -12.86502, -14.14156, 2.88123, -16.45395, -5.85638, -15.64157, -3.51483, -22.34113, -14.9697, -16.95758, -7.74658, -19.16943, -1.80411, -19.34375, -11.9173, -15.34827, -3.34967, -11.76147, -9.14392, -8.12161, 4.50641, -6.28311, 0.41876, -7.7204, -1.74338, -6.12186, -4.75937, -4.22754, 0.86572, -5.03525, 0, 0, 4.97189, 0.26373, 4.32794, -2.44827, 0, 0, 1.5105, 1.07475, 0, 0, -0.25464, 3.51071, 0, 0, -1.98267, 6.2748, 0, 0, -3.46484, 8.38449, 0, 0, -4.72119, 9.99858], "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 3.3333, "vertices": [-3.43026, 3.86118, -2.69671, 3.03526, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.60698, -5.64269, -4.38711, -3.89657, -1.60698, -5.64269, -4.38711, -3.89657, -3.19944, -6.2188, -6.04101, -3.52697, -1.99935, -7.02073, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.27781, -1.06936, -0.30254, -1.06255, -0.82605, -0.73368, 0.31667, -1.21897, -0.34489, -1.21098, -0.94177, -0.83625, -0.59961, -1.97504, -1.56808, -1.34408, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.79561, 5.39766, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9.99978, -4.9902, 0, 0, 0, 0, 11.21571, -4.81847, 10.94647, -5.4, 0, 0, 0, 0, 11.58175, -4.84184, 0, 0, 0, 0, 11.53895, -3.55268, 0, 0, 0, 0, 11.46417, -1.9373, 0, 0, 0, 0, 11.35184, -0.33028, 0, 0, 0, 0, 11.11528, 1.30607, 10.7939, -2.96466, 0, 0, 0, 0, 2.02391, -0.62946, 0, 0, 2.4613, -3.42431, 0.38642, -4.19728, 2.02652, 0.11702, 1.80647, -0.92522, 2.63293, -5.97686, -0.75953, -6.48449, 1.47728, -2.96441, -0.22903, -3.30304, 2.96456, -7.77735, -1.38623, -8.20487, 1.2187, -6.26687, -2.12594, -6.01861, 3.86799, -10.25771, -1.86485, -10.80076, -7.37697, -8.10895, 1.65213, -9.4349, -3.35812, -8.96907, -2.01545, -12.81068, -8.58381, -9.72369, -4.44199, -10.99199, -1.0345, -11.09195, -6.83353, -8.80089, -1.92074, -6.74418, -5.24324, -4.65703, 2.58403, -3.60282, 0.24012, -4.42697, -0.99967, -3.51035, -2.72908, -2.42412, 0.49642, -2.88727, 0, 0, 2.85095, 0.15123, 2.4817, -1.40387, 0, 0, 0.86614, 0.61628, 0, 0, -0.14601, 2.01309, 0, 0, -1.13689, 3.59805, 0, 0, -1.98679, 4.80777, 0, 0, -2.70719, 5.73331]}]}, "zs6": {"zs6": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "offset": 144, "vertices": [12.409, -6.15942, 12.72662, 5.48421, 12.55621, 5.85406, 13.85535, 0.16157, 11.94543, 3.9254, 10.88773, 6.28992, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11.61499, 1.93053, 10.97296, 4.26968, 11.70105, 0.39563, 11.37054, 2.78464], "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "l1": {"l1": [{"curve": 0.25, "c3": 0.75}, {"time": 1.4667, "offset": 18, "vertices": [5.0909, 2.89719, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.11945, 3.07819, 5.117, 2.91207, -2.0159, -1.21225, -2.01469, -1.14656, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.19377, -0.71775, -1.19304, -0.67895, 1.22989, 0.73954, 1.22949, 0.69969, -2.0159, -1.21225, -2.01469, -1.14656], "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 1.8667, "offset": 18, "vertices": [4.31812, 2.45741, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.34234, 2.61094, 4.34026, 2.47003, -1.7099, -1.02824, -1.70887, -0.97252, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.01256, -0.6088, -1.01194, -0.57589, 1.0432, 0.62728, 1.04286, 0.59348, -1.7099, -1.02824, -1.70887, -0.97252], "curve": 0.312, "c2": 0.26, "c3": 0.653, "c4": 0.62}, {"time": 2.3, "offset": 2, "vertices": [-5.28848, -3.36076, -5.28543, -3.1744, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.91918, 1.66129, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.93555, 1.76508, 2.93415, 1.66982, -3.87999, -2.34065, -3.87768, -2.20681, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.68452, -0.41157, -0.6841, -0.38932, 0.70523, 0.42406, 0.70501, 0.40121, -2.75088, -1.65861, -2.74918, -1.56457, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.0132, -1.21604, -2.01196, -1.14502, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.58989, -1.5641, -2.58837, -1.47309, -2.75967, -1.66706, -2.758, -1.56962, -3.01076, -1.81868, -3.009, -1.71248, -3.01672, -1.8223, -3.01491, -1.71582, -3.45268, -2.08566, -3.45071, -1.96386, -2.00723, -1.21226, -2.00609, -1.14174, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.55406, -0.33437, -0.55377, -0.31516, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.4957, -2.89771, -2.49408, -2.80964, -3.9919, -4.19645, -3.98943, -4.05579, -4.13455, -5.48035, -4.13203, -5.33562, -8.04666, -3.35476, -8.04185, -3.07122, -4.17723, -2.79916, -4.17467, -2.65188, -2.66936, -1.61217, -2.66779, -1.51825, -5.38295, -3.25127, -5.37968, -3.06157, -3.72916, -0.76906, -3.72714, -0.63808, 1.24249, 0.77107, 1.24178, 0.7268, 1.74043, 2.91232, 1.7392, 2.85017, 2.79131, 3.07417, 2.78941, 2.97496, 2.87317, 3.49512, 2.87121, 3.39281, 0.25119, 1.33736, 0.25089, 1.32794, 1.56142, 1.53688, 1.5603, 1.48121, 1.96614, 2.39304, 1.96487, 2.32315, 0.62741, 1.86275, 0.62686, 1.83979, -1.46835, -0.8868, -1.46752, -0.83519], "curve": 0.342, "c2": 0.36, "c3": 0.692, "c4": 0.75}, {"time": 2.9, "offset": 18, "vertices": [-1.16414, -0.73257, -1.5638, 1.70711, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.95245, -0.1277, 1.95216, -0.17305, -0.30803, -0.18523, -0.30784, -0.17519, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.18241, -0.10967, -0.1823, -0.10374, 0.18793, 0.113, 0.18787, 0.10691, -0.30803, -0.18523, -0.30784, -0.17519], "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 3.3333}]}, "f4": {"f4": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "vertices": [-8.47862, 2.96127, -2.01707, 1.84538, -3.27837, 2.9993, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.36813, -0.30084, 3.20477, -1.52144, 3.08215, -2.43861, 1.51064, -1.99599, -1.60434, -1.11845, -10.88113, 5.92592, -12.03795, 6.96468, -10.08672, 7.44963, -0.33022, 1.73286, 1.83257, 2.27071, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.78931, 0.50089, -5.78931, 0.50089, -5.78931, 0.50089, -9.78998, 4.16109, -1.34126, 7.17163, -1.16734, 2.50253, 0.88531, 8.1781, -4.35297, 3.90964, 1.62181, -1.82201, 1.41313, 4.74539, 4.28453, -3.91983, -2.0517, -1.2921, 0, 0, 0, 0, -5.78931, 0.50089, -14.62634, 8.58578, -2.29478, 2.0995, -9.71416, 8.88734, -4.37862, 4.00594, -2.67279, 2.44527, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.10318, 0.40193, -0.6815, 0.57117, 1.25168, -1.12292], "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}}}}, "animation2": {"slots": {"zs7": {"attachment": [{"name": null}]}, "zs9": {"color": [{"color": "ffffff7f"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 2.5, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffff7f"}], "attachment": [{"name": null}]}, "zs12": {"color": [{"color": "ffffffbf"}, {"time": 0.4, "color": "ffffffff"}, {"time": 2.0667, "color": "ffffff00"}, {"time": 3.3333, "color": "ffffffbf"}], "attachment": [{"name": null}]}, "x1": {"color": [{"color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffff00"}]}, "zs10": {"color": [{"color": "ffffffff"}, {"time": 1.6667, "color": "ffffff00"}, {"time": 3.3333, "color": "ffffffff"}], "attachment": [{"name": null}]}, "zs13": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "color": "ffffffff"}], "attachment": [{"name": null}]}, "x3": {"color": [{"color": "ffffffbf"}, {"time": 0.4, "color": "ffffffff"}, {"time": 2.0667, "color": "ffffff00"}, {"time": 3.3333, "color": "ffffffbf"}]}, "zw3": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "color": "ffffff00"}], "attachment": [{"name": null}]}, "zs8": {"color": [{"color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffff00"}], "attachment": [{"name": null}]}, "zs4": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "color": "ffffff00"}], "attachment": [{"name": null}]}, "zs3": {"color": [{"color": "ffffffff"}, {"time": 1.6667, "color": "ffffff00"}, {"time": 3.3333, "color": "ffffffff"}], "attachment": [{"name": null}]}, "zs1": {"color": [{"color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffff00"}], "attachment": [{"name": null}]}, "bg": {"attachment": [{"name": null}]}, "zs2": {"color": [{"color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffff00"}], "attachment": [{"name": null}]}, "x2": {"color": [{"color": "ffffff9f"}, {"time": 1.0333, "color": "ffffff00"}, {"time": 2.7, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffff9f"}]}, "zs6": {"attachment": [{"name": null}]}, "zs11": {"color": [{"color": "ffffff9f"}, {"time": 1.0333, "color": "ffffff00"}, {"time": 2.7, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffff9f"}], "attachment": [{"name": null}]}, "zw4": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "color": "ffffffff"}], "attachment": [{"name": null}]}, "zs5": {"color": [{"color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffff00"}], "attachment": [{"name": null}]}}, "bones": {"bone": {"translate": [{"x": 0.02, "y": 15.38, "curve": 0.349, "c2": 0.35, "c3": 0.684, "c4": 0.69}, {"time": 0.1, "x": 0.02, "y": 12.14, "curve": 0.372, "c2": 0.38, "c3": 0.708, "c4": 0.72}, {"time": 0.2667, "x": 0.01, "y": 6.85, "curve": 0.356, "c2": 0.36, "c3": 0.69, "c4": 0.7}, {"time": 0.3333, "x": 0.01, "y": 4.8, "curve": 0.42, "c2": 0.46, "c3": 0.759, "c4": 0.8}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 0.06, "y": 38.4, "curve": 0.237, "c2": 0.16, "c3": 0.694, "c4": 0.67}, {"time": 3.3333, "x": 0.02, "y": 15.38}]}, "l1": {"rotate": [{"angle": -1.17, "curve": 0.347, "c2": 0.38, "c3": 0.685, "c4": 0.73}, {"time": 0.3333, "angle": -0.37, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "angle": -3.6, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.3333, "angle": -1.17}], "translate": [{"x": -7.41, "y": -4.21, "curve": 0.319, "c2": 0.29, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "x": -7.8, "y": -4.44, "curve": 0.332, "c2": 0.33, "c3": 0.668, "c4": 0.67}, {"time": 0.5333, "x": -8.06, "y": -4.59, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 1.3667, "x": -8.81, "y": -5.02, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "x": -7.05, "y": -4.01, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 3.3333, "x": -7.41, "y": -4.21}]}, "l2": {"translate": [{"x": -0.37, "y": -0.59, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "x": -4.37, "y": -3.79, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 3.3333, "x": -0.37, "y": -0.59}], "shear": [{"x": -0.37, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "x": -2.4, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 3.3333, "x": -0.37}]}, "f1": {"translate": [{"x": -0.1, "y": -0.42, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "x": -0.66, "y": -2.75, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 3.3333, "x": -0.1, "y": -0.42}], "scale": [{"x": 0.999, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.94, "curve": 0.245, "c3": 0.707, "c4": 0.82}, {"time": 3.3333, "x": 0.999}], "shear": [{}, {"time": 1.6667, "x": 1.2}, {"time": 3.3333}]}, "st2": {"rotate": [{"angle": 8.97, "curve": 0.27, "c2": 0.25, "c3": 0.751}, {"time": 1.6667, "angle": -0.01, "curve": 0.243, "c3": 0.658, "c4": 0.63}, {"time": 2.7333, "angle": 6.73, "curve": 0.351, "c2": 0.4, "c3": 0.687, "c4": 0.75}, {"time": 3, "angle": 8.26, "curve": 0.351, "c2": 0.44, "c3": 0.686, "c4": 0.79}, {"time": 3.1667, "angle": 8.88, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 3.3, "angle": 9.07, "curve": 0.326, "c2": 0.26, "c3": 0.659, "c4": 0.6}, {"time": 3.3333, "angle": 8.97}], "translate": [{"x": 17.4, "y": 2.07, "curve": 0.27, "c2": 0.25, "c3": 0.752}, {"time": 1.6667, "x": 0.43, "y": -4.67, "curve": 0.243, "c3": 0.658, "c4": 0.63}, {"time": 2.7333, "x": 12.16, "y": 0.17, "curve": 0.351, "c2": 0.4, "c3": 0.687, "c4": 0.75}, {"time": 3, "x": 15.87, "y": 1.51, "curve": 0.351, "c2": 0.44, "c3": 0.686, "c4": 0.79}, {"time": 3.1667, "x": 17.37, "y": 2.06, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 3.3, "x": 17.85, "y": 2.23, "curve": 0.324, "c2": 0.28, "c3": 0.658, "c4": 0.61}, {"time": 3.3333, "x": 17.4, "y": 2.07}]}, "st": {"rotate": [{"angle": 0.03, "curve": 0.345, "c2": 0.66, "c3": 0.679}, {"time": 0.0667, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 0.1667, "angle": 0.03, "curve": 0.315, "c2": 0.2, "c3": 0.649, "c4": 0.54}, {"time": 0.3, "angle": 0.15, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 2.1667, "angle": 2.4, "curve": 0.248, "c3": 0.733, "c4": 0.93}, {"time": 3.3333, "angle": 0.03}]}, "st4": {"rotate": [{"angle": -0.9, "curve": 0.27, "c2": 0.25, "c3": 0.756}, {"time": 1.7333, "angle": -15.6, "curve": 0.32, "c2": 0.04, "c3": 0.663, "c4": 0.65}, {"time": 2.8667, "angle": -0.51, "curve": 0.347, "c2": 0.41, "c3": 0.682, "c4": 0.75}, {"time": 3, "angle": -0.18, "curve": 0.357, "c2": 0.65, "c3": 0.692}, {"time": 3.1667, "curve": 0.3, "c3": 0.637, "c4": 0.36}, {"time": 3.3333, "angle": -0.9}], "translate": [{"x": 0.38, "y": 3.02, "curve": 0.27, "c2": 0.25, "c3": 0.754}, {"time": 1.8333, "x": -20.55, "y": -31.65, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 2.7333, "x": -6.82, "y": 1.06, "curve": 0.342, "c2": 0.37, "c3": 0.676, "c4": 0.7}, {"time": 2.8667, "x": -3.96, "y": 2.13, "curve": 0.345, "c2": 0.39, "c3": 0.679, "c4": 0.72}, {"time": 3, "x": -1.43, "y": 3.07, "curve": 0.353, "c2": 0.48, "c3": 0.688, "c4": 0.82}, {"time": 3.1667, "x": 0.6, "y": 3.83, "curve": 0.347, "c2": 0.66, "c3": 0.68}, {"time": 3.2333, "x": 0.95, "y": 3.96, "curve": 0.313, "c3": 0.647, "c4": 0.35}, {"time": 3.3333, "x": 0.38, "y": 3.02}]}, "s2": {"rotate": [{"angle": 0.31, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 6, "curve": 0.245, "c3": 0.707, "c4": 0.82}, {"time": 3.3333, "angle": 0.31}], "translate": [{"x": 2.04, "curve": 0.346, "c2": 0.38, "c3": 0.683, "c4": 0.72}, {"time": 0.3, "x": 0.93, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 6, "curve": 0.244, "c3": 0.64, "c4": 0.57}, {"time": 3.3333, "x": 2.04}]}, "s6": {"rotate": [{"angle": -6.39, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 10.63, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -6.39}]}, "s7": {"rotate": [{"angle": -1.56, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6333, "angle": -6.39, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": 10.63, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 3.3333, "angle": -1.56}]}, "s8": {"rotate": [{"angle": 5.8, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.2333, "angle": -6.39, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": 10.63, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 3.3333, "angle": 5.8}]}, "s4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 7.49, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "s5": {"rotate": [{"angle": 4.29, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 1.0333, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 7.49, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 3.3333, "angle": 4.29}]}, "s1": {"rotate": [{"angle": 14.77, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 1.0333, "angle": 7.2, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 20.4, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 3.3333, "angle": 14.77}]}, "st3": {"rotate": [{"angle": -0.51, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": -1.2, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 3.3333, "angle": -0.51}]}, "st8": {"rotate": [{"angle": 0.45, "curve": 0.34, "c2": 0.36, "c3": 0.757}, {"time": 1.2, "angle": -1.44, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "angle": 0.97, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 3.3333, "angle": 0.45}]}, "s10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 8.21, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "s9": {"rotate": [{"angle": 1.65, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": 5.81, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 3.3333, "angle": 1.65}]}, "s12": {"rotate": [{"angle": 4.17, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": 14.71, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 3.3333, "angle": 4.17}]}, "s11": {"rotate": [{"angle": 1.65, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": 5.81, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 3.3333, "angle": 1.65}]}, "s13": {"rotate": [{"angle": 1.65, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": 5.81, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 3.3333, "angle": 1.65}]}, "s15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 11.81, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "f9": {"rotate": [{"angle": -4.38, "curve": 0.353, "c2": 0.41, "c3": 0.757}, {"time": 0.9667, "angle": 13.05, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 2, "angle": -1.19, "curve": 0.347, "c2": 0.38, "c3": 0.688, "c4": 0.74}, {"time": 2.4, "angle": -7.81, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 2.8333, "angle": -11.62, "curve": 0.264, "c3": 0.618, "c4": 0.43}, {"time": 3.3333, "angle": -4.38}]}, "f10": {"rotate": [{"angle": -11.09, "curve": 0.278, "c2": 0.12, "c3": 0.754}, {"time": 1.3667, "angle": 11.17, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 4.72, "curve": 0.324, "c2": 0.31, "c3": 0.665, "c4": 0.66}, {"time": 2.4, "angle": -1.9, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 3.2333, "angle": -11.62, "curve": 0.313, "c3": 0.647, "c4": 0.35}, {"time": 3.3333, "angle": -11.09}]}, "f11": {"rotate": [{"angle": -9.54, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.3333, "angle": -11.62, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 8.16, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 2, "angle": 7.22, "curve": 0.298, "c2": 0.2, "c3": 0.64, "c4": 0.56}, {"time": 2.4, "angle": 2.55, "curve": 0.333, "c2": 0.33, "c3": 0.703, "c4": 0.78}, {"time": 3.3333, "angle": -9.54}]}, "f12": {"rotate": [{"angle": -1.36, "curve": 0.382, "c2": 0.55, "c3": 0.741}, {"time": 0.7333, "angle": -11.62, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 2, "angle": 14.58, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 2.2, "angle": 16.78, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 2.4, "angle": 15.31, "curve": 0.281, "c2": 0.16, "c3": 0.653, "c4": 0.62}, {"time": 3.3333, "angle": -1.36}]}, "f2": {"rotate": [{"angle": -1.2}, {"time": 0.5333, "angle": -0.52, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 2, "angle": -2.11, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 2.4, "angle": -2.4}, {"time": 3.3333, "angle": -1.2}]}, "f58": {"rotate": [{"angle": -2.88}, {"time": 0.5333, "angle": 0.32, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 2, "angle": -7.13, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 2.4, "angle": -8.49}, {"time": 3.3333, "angle": -2.88}]}, "f59": {"rotate": [{"angle": -7.3, "curve": 0.322, "c2": 0.29, "c3": 0.757}, {"time": 1.1667, "angle": 0.32, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 2, "angle": -3.45, "curve": 0.335, "c2": 0.34, "c3": 0.676, "c4": 0.69}, {"time": 2.4, "angle": -5.99, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 3.0333, "angle": -8.49, "curve": 0.283, "c3": 0.625, "c4": 0.39}, {"time": 3.3333, "angle": -7.3}]}, "f60": {"rotate": [{"angle": -7.56, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.3333, "angle": -8.49, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 0.32, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 2, "angle": -0.1, "curve": 0.298, "c2": 0.2, "c3": 0.64, "c4": 0.56}, {"time": 2.4, "angle": -2.18, "curve": 0.333, "c2": 0.33, "c3": 0.703, "c4": 0.78}, {"time": 3.3333, "angle": -7.56}]}, "f3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 1.96, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}], "shear": [{"curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": -8.4, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "st7": {"rotate": [{"angle": -0.3, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 0.8, "angle": -1.44, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": 0.97, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 3.3333, "angle": -0.3}]}, "s14": {"rotate": [{"angle": 1.65, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": 5.81, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 3.3333, "angle": 1.65}]}, "f4": {"rotate": [{"angle": 0.1, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 1.96, "curve": 0.245, "c3": 0.707, "c4": 0.82}, {"time": 3.3333, "angle": 0.1}], "shear": [{"x": -0.43, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -8.4, "curve": 0.245, "c3": 0.707, "c4": 0.82}, {"time": 3.3333, "x": -0.43}]}, "f5": {"rotate": [{"angle": 0.3, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 1.96, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 3.3333, "angle": 0.3}], "shear": [{"x": -1.3, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "x": -8.4, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 3.3333, "x": -1.3}]}, "f6": {"rotate": [{"angle": 0.56, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": 1.96, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 3.3333, "angle": 0.56}], "shear": [{"x": -2.38, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "x": -8.4, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 3.3333, "x": -2.38}]}, "f7": {"rotate": [{"angle": 0.84, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": 1.96, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 3.3333, "angle": 0.84}], "shear": [{"x": -3.58, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "x": -8.4, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 3.3333, "x": -3.58}]}, "f53": {"rotate": [{"angle": -6.87, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -1.98, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -6.87}]}, "f54": {"rotate": [{"angle": -1.19, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.5, "angle": -6.87, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": 21.15, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 3.3333, "angle": -1.19}]}, "f55": {"rotate": [{"angle": 2.91, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 1, "angle": -6.87, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": 11.11, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 3.3333, "angle": 2.91}]}, "f56": {"rotate": [{"angle": 4.86, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.5, "angle": -6.87, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "angle": 6.61, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 3.3333, "angle": 4.86}]}, "f13": {"rotate": [{"angle": -2.65}]}, "f20": {"rotate": [{"angle": -2.65, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 2.86, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -2.65}]}, "f21": {"rotate": [{"angle": -2.06, "curve": 0.372, "c2": 0.62, "c3": 0.712}, {"time": 0.3333, "angle": -2.65, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 2.86, "curve": 0.243, "c3": 0.686, "c4": 0.74}, {"time": 3.3333, "angle": -2.06}]}, "f22": {"rotate": [{"angle": -0.93, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.6667, "angle": -2.65, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 2.86, "curve": 0.244, "c3": 0.645, "c4": 0.58}, {"time": 3.3333, "angle": -0.93}]}, "f23": {"rotate": [{"angle": 0.35, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 1, "angle": -2.65, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": 2.86, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 3.3333, "angle": 0.35}]}, "f24": {"rotate": [{"angle": 1.6, "curve": 0.342, "c2": 0.37, "c3": 0.757}, {"time": 1.3333, "angle": -2.65, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "angle": 2.86, "curve": 0.27, "c3": 0.619, "c4": 0.41}, {"time": 3.3333, "angle": 1.6}]}, "f25": {"rotate": [{"angle": -1.71, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -4.26, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -1.71}]}, "f27": {"rotate": [{"angle": 0.63, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.7333, "angle": -1.71, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "angle": 4.64, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.3333, "angle": 0.63}]}, "f28": {"rotate": [{"angle": 2.3, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.1333, "angle": -1.71, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": 4.64, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.3333, "angle": 2.3}]}, "f29": {"rotate": [{"angle": 5.73, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.5, "angle": -1.71, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "angle": 6.84, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 3.3333, "angle": 5.73}]}, "f30": {"rotate": [{"angle": 10.91, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -1.71, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 10.91}]}, "f26": {"rotate": [{"angle": -1.1, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3667, "angle": -1.71, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 2.93, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.3333, "angle": -1.1}]}, "f14": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 1.17, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "f15": {"rotate": [{"angle": 0.21, "curve": 0.372, "c2": 0.62, "c3": 0.712}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 2.01, "curve": 0.243, "c3": 0.686, "c4": 0.74}, {"time": 3.3333, "angle": 0.21}]}, "f16": {"rotate": [{"angle": 1.09, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 3.51, "curve": 0.244, "c3": 0.645, "c4": 0.58}, {"time": 3.3333, "angle": 1.09}]}, "f17": {"rotate": [{"angle": 1.65, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": 3.03, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 3.3333, "angle": 1.65}]}, "f18": {"rotate": [{"angle": 3.71, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.2333, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": 5.19, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 3.3333, "angle": 3.71}]}, "f19": {"rotate": [{"angle": 7.37, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "angle": 8.47, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 3.3333, "angle": 7.37}]}, "sp1": {"rotate": [{"angle": -18, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 10.8, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -18}]}, "bone12": {"translate": [{"x": 18.23, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "x": 48, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 18.23}], "scale": [{"x": 1.091, "y": 1.03, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "x": 1.24, "y": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 1.091, "y": 1.03}]}, "st5": {"rotate": [{"angle": -1.44, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 0.97, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -1.44}]}, "st6": {"rotate": [{"angle": -1.02, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.4, "angle": -1.44, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": 0.97, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 3.3333, "angle": -1.02}]}, "f8": {"rotate": [{"angle": 1.12, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 1.0333, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 1.96, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 3.3333, "angle": 1.12}], "shear": [{"x": -4.82, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 1.0333, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": -8.4, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 3.3333, "x": -4.82}]}, "x2": {"rotate": [{"angle": -6.42, "curve": 0.269, "c2": 0.1, "c3": 0.698, "c4": 0.78}, {"time": 1.4667, "angle": -0.57, "curve": 0.369, "c2": 0.63, "c3": 0.707}, {"time": 1.7333, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "angle": -6.55, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 3.3333, "angle": -6.42}]}, "x3": {"rotate": [{"angle": 7.69, "curve": 0.363, "c2": 0.64, "c3": 0.698}, {"time": 0.1667, "angle": 8.13, "curve": 0.243, "c3": 0.653, "c4": 0.62}, {"time": 1.4667, "angle": 2.08, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 2.0333, "curve": 0.245, "c3": 0.706, "c4": 0.81}, {"time": 3.3333, "angle": 7.69}]}, "x4": {"rotate": [{"angle": 5.63, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 0.4667, "angle": 7.61, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1.4667, "angle": 3.47, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 2.3333, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 3.3333, "angle": 5.63}]}, "x5": {"rotate": [{"angle": 2.22, "curve": 0.374, "c2": 0.49, "c3": 0.751}, {"time": 0.7333, "angle": 4.61, "curve": 0.26, "c3": 0.618, "c4": 0.45}, {"time": 1.4667, "angle": 3.04, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 2.6333, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 3.3333, "angle": 2.22}]}, "x6": {"rotate": [{"angle": 0.96, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 1.0333, "angle": 4.26, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 1.4667, "angle": 3.61, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 2.9, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 3.3333, "angle": 0.96}]}, "x7": {"rotate": [{"angle": 0.2, "curve": 0.284, "c2": 0.15, "c3": 0.754}, {"time": 1.3333, "angle": 6.29, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 1.4667, "angle": 6.15, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 3.2, "curve": 0.308, "c3": 0.643, "c4": 0.35}, {"time": 3.3333, "angle": 0.2}]}, "x1": {"rotate": [{"angle": -0.47, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 1.4667, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "angle": -0.55, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 3.3333, "angle": -0.47}]}, "f33": {"rotate": [{"angle": -3.87, "curve": 0.347, "c2": 0.38, "c3": 0.688, "c4": 0.74}, {"time": 0.4, "angle": -5.84, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.8333, "angle": -6.96, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": 0.34, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 3.3333, "angle": -3.87}]}, "f34": {"rotate": [{"angle": -1.73, "curve": 0.324, "c2": 0.31, "c3": 0.665, "c4": 0.66}, {"time": 0.4, "angle": -3.85, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 1.2333, "angle": -6.96, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": 0.34, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 3.3333, "angle": -1.73}]}, "f35": {"rotate": [{"angle": -0.01, "curve": 0.298, "c2": 0.2, "c3": 0.64, "c4": 0.56}, {"time": 0.4, "angle": -1.73, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.6667, "angle": -6.96, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "angle": 0.34, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 3.3333, "angle": -0.01}]}, "f31": {"rotate": [{"angle": -5.84, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.4, "angle": -6.96, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 2.74, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 3.3333, "angle": -5.84}]}, "f38": {"rotate": [{"angle": -0.96, "curve": 0.324, "c2": 0.31, "c3": 0.665, "c4": 0.66}, {"time": 0.4, "angle": -3.06, "curve": 0.347, "c2": 0.38, "c3": 0.688, "c4": 0.74}, {"time": 0.8333, "angle": -4.99, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 1.2333, "angle": -6.1, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": 1.09, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 3.3333, "angle": -0.96}]}, "f39": {"rotate": [{"angle": 0.74, "curve": 0.298, "c2": 0.2, "c3": 0.64, "c4": 0.56}, {"time": 0.4, "angle": -0.95, "curve": 0.324, "c2": 0.31, "c3": 0.665, "c4": 0.66}, {"time": 0.8333, "angle": -3.03, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 1.6667, "angle": -6.1, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "angle": 1.09, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 3.3333, "angle": 0.74}]}, "f40": {"rotate": [{"angle": 0.53, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.2, "angle": 1.09, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 0.4, "angle": 0.74, "curve": 0.298, "c2": 0.2, "c3": 0.64, "c4": 0.56}, {"time": 0.8333, "angle": -0.95, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.0667, "angle": -6.1, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 3.3333, "angle": 0.53}]}, "f41": {"rotate": [{"angle": -0.24}]}, "f37": {"rotate": [{"angle": -3.06, "curve": 0.347, "c2": 0.38, "c3": 0.688, "c4": 0.74}, {"time": 0.4, "angle": -4.99, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.8333, "angle": -6.1, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": 3.49, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 3.3333, "angle": -3.06}]}, "f43": {"rotate": [{"angle": 3.21, "curve": 0.332, "c2": 0.33, "c3": 0.668, "c4": 0.67}, {"time": 0.2, "angle": 2.33, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 1.0333, "angle": -0.24, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 5.78, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 3.3333, "angle": 3.21}]}, "f44": {"rotate": [{"angle": 6.68, "curve": 0.321, "c2": 0.29, "c3": 0.656, "c4": 0.63}, {"time": 0.2, "angle": 5.6, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.4667, "angle": -0.24, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "angle": 7.92, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 3.3333, "angle": 6.68}]}, "f48": {"rotate": [{"angle": -0.24}]}, "f49": {"rotate": [{"angle": 1.6, "curve": 0.338, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 0.2, "angle": 0.98, "curve": 0.344, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 0.4, "angle": 0.43, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.8333, "angle": -0.24, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": 6.51, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 3.3333, "angle": 1.6}]}, "f42": {"rotate": [{"angle": 0.98, "curve": 0.344, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 0.2, "angle": 0.43, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.6333, "angle": -0.24, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": 7.71, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 3.3333, "angle": 0.98}]}, "f51": {"rotate": [{"angle": 7.53, "curve": 0.313, "c2": 0.24, "c3": 0.648, "c4": 0.58}, {"time": 0.2, "angle": 6.68, "curve": 0.321, "c2": 0.29, "c3": 0.656, "c4": 0.63}, {"time": 0.4, "angle": 5.6, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.6667, "angle": -0.24}, {"time": 3.1333, "angle": 7.92, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 3.3333, "angle": 7.53}]}, "f50": {"rotate": [{"angle": 4.08, "curve": 0.327, "c2": 0.31, "c3": 0.662, "c4": 0.65}, {"time": 0.2, "angle": 3.21, "curve": 0.332, "c2": 0.33, "c3": 0.668, "c4": 0.67}, {"time": 0.4, "angle": 2.33, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 1.2333, "angle": -0.24, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": 5.78, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 3.3333, "angle": 4.08}]}, "f32": {"scale": [{"x": 0.997, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.4}, {"time": 1.8667, "x": 0.98, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 3.3333, "x": 0.997}], "shear": [{"x": 1.11, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "x": 7.2, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 3.3333, "x": 1.11}]}, "f36": {"rotate": [{"angle": 9.06, "curve": 0.293, "c2": 0.18, "c3": 0.755}, {"time": 1.6667}, {"time": 3.1333, "angle": 9.55, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 3.3333, "angle": 9.06}]}, "f45": {"rotate": [{"angle": 8.31, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3, "angle": 9.55, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1.6667, "angle": 1.93, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 2.1667, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.3333, "angle": 8.31}]}, "f46": {"rotate": [{"angle": 4.23, "curve": 0.37, "c2": 0.48, "c3": 0.752}, {"time": 0.8, "angle": 9.55, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1.6667, "angle": 5.2, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 2.6667, "curve": 0.253, "c3": 0.622, "c4": 0.48}, {"time": 3.3333, "angle": 4.23}]}, "zs5": {"scale": [{}, {"time": 3.3, "x": 1.84, "y": 1.8, "curve": "stepped"}, {"time": 3.3333}]}, "zs24": {"scale": [{"x": 1.532, "y": 1.289}, {"time": 0.8, "x": 1.7, "y": 1.38, "curve": "stepped"}, {"time": 0.8333}, {"time": 3.3333, "x": 1.532, "y": 1.289}]}, "zs30": {"scale": [{"x": 1.354, "y": 1.192}, {"time": 1.6333, "x": 1.7, "y": 1.38, "curve": "stepped"}, {"time": 1.6667}, {"time": 3.3333, "x": 1.354, "y": 1.192}]}, "zs22": {"scale": [{}, {"time": 3.3, "x": 1.22, "y": 1.18}, {"time": 3.3333}]}, "zs36": {"scale": [{"x": 1.153, "y": 1.125}, {"time": 1, "x": 1.22, "y": 1.18}, {"time": 1.0333}, {"time": 3.3333, "x": 1.153, "y": 1.125}]}, "zs38": {"scale": [{"x": 1.084, "y": 1.068}, {"time": 2.0333, "x": 1.22, "y": 1.18}, {"time": 2.0667}, {"time": 3.3333, "x": 1.084, "y": 1.068}]}, "f52": {"rotate": [{"angle": 6.82, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.2, "angle": 7.39, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 6.57, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 3.3333, "angle": 6.82}]}, "zs8": {"rotate": [{"angle": -4.3, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.4, "angle": -1.6, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.8333}, {"time": 2.5, "angle": -8.67, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": -4.3}]}, "zs9": {"rotate": [{"angle": -7.05, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 0.8333, "angle": -1.6, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.2333}, {"time": 2.9, "angle": -8.67, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 3.3333, "angle": -7.05}]}, "zs10": {"rotate": [{"angle": -8.67, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.2333, "angle": -1.6, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -8.67}]}, "zs11": {"rotate": [{"angle": 2.35, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 4.69, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": 2.35}]}, "zs12": {"rotate": [{"angle": 4.69, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.8333, "angle": 2.35, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667}, {"time": 3.3333, "angle": 4.69}]}, "zs13": {"rotate": [{"angle": 2.35}, {"time": 0.8333, "angle": 4.69, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6667, "angle": 2.35, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5}, {"time": 3.3333, "angle": 2.35}]}, "zw3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -36, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "angle": -27.6}, {"time": 3.3333}]}, "bone7": {"translate": [{"x": 15.29, "y": 16.48, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "x": -41.53, "y": -57.05}, {"time": 3.3333, "x": 15.29, "y": 16.48}]}, "bone3": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 7.67, "y": -22.12, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "x": 52.4, "y": -106.8}, {"time": 3.3333}]}, "bone8": {"translate": [{"x": -6.01, "y": -10.41, "curve": 0.376, "c2": 0.5, "c3": 0.75}, {"time": 1.6333, "x": -26.61, "y": -36.42}, {"time": 1.6667, "x": 15.29, "y": 16.48, "curve": 0.25, "c3": 0.626, "c4": 0.5}, {"time": 3.3333, "x": -6.01, "y": -10.41}]}, "zs20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -8.32, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "zs21": {"rotate": [{"angle": -6.78, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.2333, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "angle": -8.32, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 3.3333, "angle": -6.78}]}, "bone5": {"translate": [{"x": -3.98, "y": 41.81}, {"time": 3.3, "x": -67.98, "y": -66.75}, {"time": 3.3333, "x": -3.98, "y": 41.81}]}, "bone10": {"translate": [{"x": -36.39, "y": -13.16}, {"time": 1.6333, "x": -67.98, "y": -66.75}, {"time": 1.6667, "x": -3.98, "y": 41.81}, {"time": 3.3333, "x": -36.39, "y": -13.16}]}, "bone6": {"translate": [{"x": 76.62, "y": 57.91, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "x": 28.26, "y": -61.49}, {"time": 3.3333, "x": 76.62, "y": 57.91}]}, "zw4": {"rotate": [{"angle": -36, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": -27.6}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -36}]}, "bone9": {"translate": [{"x": -20.99, "y": -65.69, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "x": 52.4, "y": -106.8}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -20.99, "y": -65.69}]}, "bone4": {"translate": [{}, {"time": 3.3, "x": 48}, {"time": 3.3333}], "scale": [{}, {"time": 3.3, "x": 1.24, "y": 1.08}, {"time": 3.3333}]}, "x8": {"rotate": [{"angle": -0.32}, {"time": 0.6333, "angle": -0.55, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 1.0333, "angle": -0.47, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 2.5}, {"time": 3.3333, "angle": -0.32}]}, "x14": {"rotate": [{"angle": 3.08, "curve": 0.376, "c2": 0.51, "c3": 0.749}, {"time": 0.9, "curve": 0.308, "c3": 0.643, "c4": 0.35}, {"time": 1.0333, "angle": 0.2, "curve": 0.284, "c2": 0.15, "c3": 0.754}, {"time": 2.3667, "angle": 6.29, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 2.5, "angle": 6.15, "curve": 0.272, "c2": 0.11, "c3": 0.637, "c4": 0.56}, {"time": 3.3333, "angle": 3.08}]}, "x13": {"rotate": [{"angle": 1.22, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6333, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 1.0333, "angle": 0.96, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 2.0667, "angle": 4.26, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 2.5, "angle": 3.61, "curve": 0.307, "c2": 0.25, "c3": 0.67, "c4": 0.68}, {"time": 3.3333, "angle": 1.22}]}, "x12": {"rotate": [{"angle": 0.5, "curve": 0.372, "c2": 0.62, "c3": 0.712}, {"time": 0.3333, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 1.0333, "angle": 2.22, "curve": 0.374, "c2": 0.49, "c3": 0.751}, {"time": 1.8, "angle": 4.61, "curve": 0.26, "c3": 0.618, "c4": 0.45}, {"time": 2.5, "angle": 3.04, "curve": 0.339, "c2": 0.35, "c3": 0.702, "c4": 0.78}, {"time": 3.3333, "angle": 0.5}]}, "x11": {"rotate": [{"angle": 0.03, "curve": 0.34, "c2": 0.66, "c3": 0.674}, {"time": 0.0333, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 1.0333, "angle": 5.63, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 1.5, "angle": 7.61, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2.5, "angle": 3.47, "curve": 0.374, "c2": 0.49, "c3": 0.74, "c4": 0.96}, {"time": 3.3333, "angle": 0.03}]}, "x10": {"rotate": [{"angle": 0.84, "curve": 0.303, "c2": 0.23, "c3": 0.716, "c4": 0.84}, {"time": 1.0333, "angle": 7.69, "curve": 0.363, "c2": 0.64, "c3": 0.698}, {"time": 1.2, "angle": 8.13, "curve": 0.243, "c3": 0.653, "c4": 0.62}, {"time": 2.5, "angle": 2.08, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 3.0667, "curve": 0.289, "c3": 0.628, "c4": 0.38}, {"time": 3.3333, "angle": 0.84}]}, "x9": {"rotate": [{"angle": -2.17, "curve": 0.358, "c2": 0.43, "c3": 0.756}, {"time": 0.9, "angle": -6.55, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 1.0333, "angle": -6.42, "curve": 0.269, "c2": 0.1, "c3": 0.698, "c4": 0.78}, {"time": 2.5, "angle": -0.57, "curve": 0.369, "c2": 0.63, "c3": 0.707}, {"time": 2.8, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 3.3333, "angle": -2.17}]}, "bone11": {"translate": [{"x": 33.42}, {"time": 1, "x": 48}, {"time": 1.0333}, {"time": 3.3333, "x": 33.42}], "scale": [{"x": 1.167, "y": 1.056}, {"time": 1, "x": 1.24, "y": 1.08}, {"time": 1.0333}, {"time": 3.3333, "x": 1.167, "y": 1.056}]}, "x15": {"rotate": [{"angle": -0.03, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.2}, {"time": 1.6667, "angle": -0.55, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 2.0667, "angle": -0.47, "curve": 0.316, "c2": 0.27, "c3": 0.719, "c4": 0.84}, {"time": 3.3333, "angle": -0.03}]}, "x21": {"rotate": [{"angle": 6.18, "curve": 0.35, "c2": 0.65, "c3": 0.684}, {"time": 0.0667, "angle": 6.29, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 0.2, "angle": 6.15, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 1.9667, "curve": 0.308, "c3": 0.643, "c4": 0.35}, {"time": 2.0667, "angle": 0.2, "curve": 0.28, "c2": 0.14, "c3": 0.732, "c4": 0.9}, {"time": 3.3333, "angle": 6.18}]}, "x20": {"rotate": [{"angle": 4.06, "curve": 0.313, "c2": 0.24, "c3": 0.648, "c4": 0.58}, {"time": 0.2, "angle": 3.61, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 1.6667, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 2.0667, "angle": 0.96, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 3.1333, "angle": 4.26, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 3.3333, "angle": 4.06}]}, "x19": {"rotate": [{"angle": 3.68, "curve": 0.323, "c2": 0.3, "c3": 0.658, "c4": 0.64}, {"time": 0.2, "angle": 3.04, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.3667, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 2.0667, "angle": 2.22, "curve": 0.374, "c2": 0.49, "c3": 0.751}, {"time": 2.8333, "angle": 4.61, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 3.3333, "angle": 3.68}]}, "x18": {"rotate": [{"angle": 4.59, "curve": 0.331, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 0.2, "angle": 3.47, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 1.0667, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 2.0667, "angle": 5.63, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 2.5333, "angle": 7.61, "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 3.3333, "angle": 4.59}]}, "x17": {"rotate": [{"angle": 3.22, "curve": 0.339, "c2": 0.35, "c3": 0.674, "c4": 0.69}, {"time": 0.2, "angle": 2.08, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 0.8, "curve": 0.245, "c3": 0.706, "c4": 0.81}, {"time": 2.0667, "angle": 7.69, "curve": 0.363, "c2": 0.64, "c3": 0.698}, {"time": 2.2333, "angle": 8.13, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 3.3333, "angle": 3.22}]}, "x16": {"rotate": [{"angle": -1.32, "curve": 0.348, "c2": 0.39, "c3": 0.684, "c4": 0.74}, {"time": 0.2, "angle": -0.57, "curve": 0.369, "c2": 0.63, "c3": 0.707}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": -6.55, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 2.0667, "angle": -6.42, "curve": 0.267, "c2": 0.1, "c3": 0.67, "c4": 0.68}, {"time": 3.3333, "angle": -1.32}]}, "zs7": {"rotate": [{"angle": -1.6, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": -8.67, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 3.3333, "angle": -1.6}]}}, "deform": {"default": {"f1": {"f1": [{"curve": 0.25, "c3": 0.75}, {"time": 1.4667, "vertices": [-11.88708, 21.25546, 5.74817, 24.96155, -15.90178, -22.53998, -3.61892, 10.48859, -1.15058, -11.20685, -0.32238, 0.7963, -0.3461, 0.7413, -4.95421, 1.66528, -5.04173, 0.85629, -1.8888, -0.06955, -1.90378, -0.37509, -0.20037, -1.41037, -0.1652, -1.43723, 0.14615, -1.4227, -1.89436, -2.58859, -1.30689, -3.08151, -2.25931, -2.2666, -1.70185, -1.70721, 0.8884, -2.16441, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.11417, 1.74747, -2.68235, -1.08588, -2.67791, -1.51596, 2.34252, 2.16812, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.69183, -0.48565, 1.76263, -0.03571, 1.6953, 0.48175, -3.50806, -5.39326, -1.77885, -6.18241, 0.16826, -9.65167, 2.98189, -9.18014, 3.57624, -12.90337, 8.53521, -10.31688, 3.83321, -15.97672, 10.02517, -13.01716, 2.3862, -6.46465, 4.81884, -4.92593, 0, 0, 0, 0, 0, 0, -1.8333, 1.87408, -1.02975, 2.79008, -2.07967, 2.126, -0.12957, 2.27599, -0.78942, 2.13871, -1.59422, 1.62963, 0, 0, 0, 0, 0.40662, 2.4114, 0.54084, 2.38485, -0.08775, 2.44385, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.55949, 5.69386, -5.62553, 11.35211, 12.30393, 1.52596, -11.29595, 7.11684, -0.91691, -1.69443, -7.36314, -2.52751, 0.98399, -2.13391, -8.06686, -5.54727, -2.26942, 3.90044, 0.67924, 1.16696, 0.2507, -1.32737, 0.13058, -1.40821, -10.57817, 1.6035, 2.93322, -1.05038, -2.90567, -1.12458, -2.96458, -0.96037, 0.73016, 2.84898, -2.60855, -2.62051, 0.26158, 3.6904, 0.46332, 3.67005, 0, 0, -2.18117, -1.80284, 0.47056, 2.7905, 0.62581, 2.75978, 0.20284, 1.20175, 0.26943, 1.18848, 0.47601, 2.82267, 0.6331, 2.79158, -0.10268, 2.86066, -0.24857, 1.29333, -0.616, 1.16423, 0.02715, -0.33193, 0.12283, -0.30941, 0.23863, -0.23226, 0.07074, -1.24153, 0.43058, -1.16654, 0.86953, -0.88889, 0, 0, 0, 0, 0, 0, -3.97087, -4.11804, 5.39051, 2.84567, 5.36729, 3.71597, 4.48084, 5.29581, -6.63074, 0.63747, -4.55229, -5.01532, 2.66905, -1.92734, 3.0416, -0.95367, -0.85019, 3.17073, -3.03324, 1.15063, 2.3777, 2.38556, -1.2424, 3.02501, -3.17984, 0.59698, -2.41389, -2.2345, 2.11877, 2.1257, -1.10687, 2.69533, -2.83371, 0.53194, -2.15111, -1.99097, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.17255, 3.18311, -1.65753, 4.03578, -4.24268, 0.79636, -3.22086, -2.98145, 2.74583, 1.55435, 2.36556, 2.37332, -1.23602, 3.00963, -3.16357, 0.59396, -2.40155, -2.22305, -0.48888, -0.27698, -0.42137, -0.42261, -6.90208, -6.03479, 8.89722, 3.86157, -7.70456, -7.45789, -0.02389, -5.9115, 2.53856, 5.71835, 2.41229, 6.11313, -1.14378, -6.55811], "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "st": {"st": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "offset": 56, "vertices": [-17.01035, -16.74297, -11.93459, -20.66916, 23.23026, 5.47623, 25.74139, -2.46231, 28.6864, -11.85208, 30.38019, -6.35381, 30.47607, 5.86496, 31.37131, -20.26212, 34.57086, -14.12393, 37.34161, 0.31467, 33.56009, 16.38434, 34.40289, -29.65236, 39.28314, -22.79343, 45.03485, -5.8642, 43.16547, 14.12585, 38.84995, -43.51818, 46.21259, -35.60052, 56.37067, -15.00409, 57.33414, 10.76587, 17.14423, -22.12419, 20.93219, -18.57996, 26.48077, -9.06049, 27.80096, 3.242, -17.93326, 12.34381, 6.10669, -11.55055, -6.52057, 11.3248, -10.76343, 7.4104, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.17279, 10.75398, -0.77003, 10.94409, -7.44147, -8.06251, 2.17279, 10.75398, -11.65912, -6.16417, 13.16376, -0.78561, 13.08112, 1.65822, 5.80518, -2.29974, -3.7681, 4.97965, -4.62244, 4.19966, -5.88794, 2.0907, -24.40021, -0.96046, 21.35019, -11.84873, 23.16846, -7.70338, 24.34119, 1.83704, -0.94336, -14.41423, 8.2894, 11.82924, 5.96313, 13.15709, 0.41852, 14.43936, -5.84329, 13.21472, -21.42548, -8.38547, 22.66327, -3.9586, 23.00311, 0.29352, 21.10187, 9.15057, -18.78845, -11.02455, 21.4657, 3.69299, 18.3717, 11.6926, 11.54126, 18.47736, -20.01056, -14.47925, 23.82764, 6.49471, 19.46912, 15.18939, 11.02432, 22.10431, -1.95639, -6.37787, 4.07648, 5.27982, 1.71881, 6.44476, -1.22336, 6.56244, -6.85382, -16.9986, 12.40369, 13.49146, 6.229, 17.23486, -1.80307, 18.24298], "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "x1": {"x1": [{"curve": 0.25, "c3": 0.75}, {"time": 1.4667, "vertices": [-5.98218, 6.73369, -4.70291, 5.29332, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.80249, -9.84055, -7.65088, -6.79539, -2.80249, -9.84055, -7.65088, -6.79539, -5.57965, -10.84525, -10.53519, -6.15085, -3.48676, -12.24377, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.48448, -1.8649, -0.52762, -1.85303, -1.44058, -1.2795, 0.55226, -2.12582, -0.60147, -2.11188, -1.6424, -1.45837, -1.04568, -3.44437, -2.73465, -2.34401, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -8.36328, 9.41322, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17.43907, -8.70264, 0, 0, 0, 0, 19.55959, -8.40314, 19.09003, -9.4173, 0, 0, 0, 0, 20.19793, -8.44391, 0, 0, 0, 0, 20.12329, -6.19568, 0, 0, 0, 0, 19.99287, -3.37854, 0, 0, 0, 0, 19.79699, -0.57599, 0, 0, 0, 0, 19.38443, 2.27771, 18.82396, -5.1702, 0, 0, 0, 0, 3.52959, -1.09775, 0, 0, 4.29237, -5.9718, 0.67389, -7.31982, 3.53413, 0.20407, 3.15039, -1.61353, 4.59169, -10.42331, -1.32458, -11.30859, 2.57629, -5.16977, -0.39941, -5.76031, 5.17003, -13.56326, -2.41751, -14.30884, 2.12535, -10.92908, -3.70752, -10.49612, 6.74556, -17.88889, -3.2522, -18.83594, -12.86502, -14.14156, 2.88123, -16.45395, -5.85638, -15.64157, -3.51483, -22.34113, -14.9697, -16.95758, -7.74658, -19.16943, -1.80411, -19.34375, -11.9173, -15.34827, -3.34967, -11.76147, -9.14392, -8.12161, 4.50641, -6.28311, 0.41876, -7.7204, -1.74338, -6.12186, -4.75937, -4.22754, 0.86572, -5.03525, 0, 0, 4.97189, 0.26373, 4.32794, -2.44827, 0, 0, 1.5105, 1.07475, 0, 0, -0.25464, 3.51071, 0, 0, -1.98267, 6.2748, 0, 0, -3.46484, 8.38449, 0, 0, -4.72119, 9.99858], "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "s2": {"s2": [{"offset": 14, "vertices": [0.2544, 0, 0.25301, 0.02648, 0.2544, 0.14967, 0.23743, 0.17533, 0.17585, 0.10604, 0.16384, 0.12375, 0, 0.13156, -0.0137, 0.13084, 0, 0.14225, -0.01481, 0.14148, 0.01297, 0.14166, 0, 0.13811, -0.01438, 0.13736, 0.01259, 0.13753, 0, -0.05258, 0.00547, -0.0523, -0.00479, -0.05236, 0.0016, -0.05256, 0, -0.08553, 0.0089, -0.08507, -0.00779, -0.08517, 0.00257, -0.08549, 0, -0.05673, 0.0059, -0.05642, -0.00517, -0.05649, 0.00172, -0.05671, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.45384, -0.64445, -0.38228, -0.68936, -0.44047, -0.59746, -0.37402, -0.64121, -0.4097, -0.49251, -0.35467, -0.53357, -0.40652, -0.44742, -0.35635, -0.4884, -0.4432, -0.38193, -0.39982, -0.42722, -0.4917, -0.30112, -0.45671, -0.35206, -0.54899, -0.20707, -0.52374, -0.26469, -0.61565, -0.0948, -0.60204, -0.1602, 0, 0, 0, 0, 0, 0, 0.33923, -0.32064, 0.28137, -0.37257, 0.31825, -0.35455, 0.25504, -0.4025, 0.3391, 0.09842, 0.3508, 0.04071, 0.60561, 0.00371, 0.57811, 0.18066, 0.60014, 0.08213, 0.60665, 0.08521, 0.59444, 0.14785, 0.52528, 0.31524, 0.5704, 0.22361, 0.01197, 0.35638, -0.02522, 0.35566, -0.12806, 0.3328, -0.395, -0.17309, -0.37484, -0.21328, -0.29613, -0.3135, -0.24263, -0.09257, -0.2317, -0.11737, -0.18728, -0.17989, -0.5383, -0.09051, -0.52608, -0.14621, -0.46035, -0.2933, -0.6887, 0.41265, -0.72802, 0.33859, 0.05735, 0.41224, 0.014, 0.4158, 0.18204, 0, 0.18105, 0.01894, 0.18204, 0, 0.26073, 0, 0.26073, 0, 0.43877, 0, 0.43877, 0, 0, 0, 0.26073, 0, 0.25331, 0, 0.23339, 0, 0.37527, 0, 0.38596, 0, 0.38386, 0.04017, 0.31113, 0, 0.30943, 0.03238, 0.43877, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.26073, 0, 0.21169, 0, 0.2544, 0, 0.2544, 0, 0.25301, 0.02648, 0.2544, 0, 0.25301, 0.02648, 0.10336, 0, 0.2544, 0, 0.2544, 0, 0.19715, 0.51748, 0.37981, 0.39038, 0.2544, 0, 0.25301, 0.02648, 0.2544, 0, 0.25301, 0.02648, 0.11855, 0, 0.1179, 0.01233, -0.24773, 0.12696, -0.25976, 0.10031, -0.27769, 0.02031, 0.15227, 0.31082, 0.11905, 0.32493, 0.01888, 0.34563, 0.25714, 0.25554, 0.22911, 0.28087, 0.13701, 0.33567, 0.36756, 0.27787, 0.3366, 0.31457, 0.23345, 0.01948, 0.23014, 0.04367, 0.15344, -0.18644, 0.17201, -0.16946, -0.20171, -0.14856, -0.18524, -0.16887, -0.12778, -0.21543, -0.08106, 0.17536, -0.09892, 0.1659, -0.14309, 0.12985, 0.01126, 0.20432, -0.01011, 0.20434, -0.06939, 0.19255, 0.24097, 0.0584, 0.23354, 0.08311, 0.04217, -0.27256, 0.07029, -0.26673, 0.01718, -0.27528, -0.11807, 0.17074, -0.13526, 0.15744, -0.17535, 0.11117, 0.10383, 0.04198, 0.09882, 0.05247, 0.0792, 0.07923, 0.19802, -0.1152, 0.20891, -0.09401, 0.03489, -0.30348, 0.06626, -0.29824, 0.00711, -0.30541, -0.26241, 0.14577, -0.27618, 0.11762, -0.2985, 0.03183, 0.13729, 0.13404, 0.12257, 0.14758, 0.07408, 0.177, 0, 0.20592, -0.02144, 0.20479, 0.01876, 0.20506, 0.02667, -0.15161, 0.04228, -0.14804, 0.01276, -0.15342, 0.03133, -0.15075, 0.00024, 0.32622, -0.09511, 0.31213, 0, 0, 0, 0, 0.02493, -0.18719, 0.04426, -0.1836, 0.00779, -0.18869, 0.03064, -0.18637, 0.16395, 0.49063, 0.25618, 0.44943, 0.01338, 0.51716, 0, 0, 0, 0, 0, 0, 0.01508, -0.44219, -0.07106, -0.43668, -0.01763, -0.44207, 0.08409, 0.48963, -0.15733, 0.4713, -0.0768, 0.49082, 0.10427, 0.05933, 0.11382, 0.03794, 0.10842, 0.05142, 0.01888, -0.41421, -0.0619, -0.40997, -0.01176, -0.41444, 0.11876, 0.13983, 0.10354, 0.15141, 0.09266, 0.25415, 0.1092, 0.34298, 0.17378, 0.31524, 0.1499, 0.21223, 0.03175, 0.25791, -0.16811, 0.42548, -0.09509, 0.44747, 0.13053, 0.12404, 0.11465, 0.13891, 0.0564, 0.17101, 0.08405, 0.15926, 0.22569, 0.07522, 0.16335, 0.17294, 0.18979, 0.14342, 0.17333, 0.16291, 0.02496, -0.38137, -0.04956, -0.37893, -0.00326, -0.38214, -0.14586, 0.20085, -0.11044, 0.22227, 0.04254, 0.02418, 0.03939, 0.02915, 0.02605, 0.04143, 0.03261, 0.03652, 0.02842, 0.03979, 0.17321, 0.05773, 0.16499, 0.07825, 0.12537, 0.13273, 0.14567, 0.11007, 0.13302, 0.12503, 0.02435, -0.3486, -0.04379, -0.34667, -0.00143, -0.34943, -0.28596, -0.0001, -0.02963, -0.11138, -0.01588, -0.11414, 0.0265, -0.11215, 0.00754, -0.115, 0.01974, -0.11354, 0.02459, -0.31828, -0.03765, -0.31698, 0.00106, -0.3192, 0.01094, -0.29965, 0, 0, 0, 0, 0, 0, 0, 0, -0.44032, -0.07596, -0.42973, -0.1227, -0.385, -0.1396, -0.36791, -0.18004, -0.25153, -0.24916, -0.22349, -0.27468, -0.14376, -0.31791, -0.1042, -0.33296, 0.02351, -0.3481, -0.03458, -0.34718, 0.00273, -0.3489, -0.52482, -0.19396, -0.50111, -0.24906, -0.38584, -0.29521, -0.35208, -0.33485, -0.01471, -0.38994, 0.10534, -0.37572, -0.0286, -0.45584, 0.02027, -0.4563, -0.33428, -0.6539, -0.26241, -0.68595, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.21848, -0.02478, -0.15214, -0.04906, -0.57187, -0.14302, -0.55334, -0.20346], "curve": 0.273, "c2": 0.1, "c3": 0.753}, {"time": 1.3667, "offset": 14, "vertices": [13.03793, 5e-05, 12.96674, 1.35684, 13.03792, 7.67079, 12.168, 8.98572, 9.01231, 5.43434, 8.397, 6.34229, -3e-05, -0.71162, 0.07443, -0.70787, -5e-05, -3.96657, 0.41379, -3.94505, -1.78607, -3.72708, 0, 7.07803, -0.73691, 7.03947, 0.64505, 7.04852, 6.7447, -2.69476, 6.9881, -1.97822, 6.33643, -4.15112, 6.77606, -3.49629, 6.63615, -4.38328, 7.056, -3.66883, 6.07751, -5.80927, 6.72003, -5.17115, 8.59461, -2.45407, 8.80307, -1.54621, 8.22198, -4.32314, 8.67419, -3.47883, 7.38962, 0.77357, 7.25168, -1.6169, 7.37622, -0.88416, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -23.25909, -33.02798, -19.59198, -35.32954, -22.57397, -30.61993, -19.16864, -32.86188, -20.99689, -25.24113, -18.1767, -27.34534, -20.83423, -22.93013, -18.2627, -25.03033, -22.71399, -19.57407, -20.49097, -21.89487, -25.19971, -15.43219, -23.40607, -18.04292, -28.1358, -10.61238, -26.84137, -13.56526, -31.552, -4.85834, -30.85425, -8.21028, -9.15146, -9.80162, -10.46625, -8.38312, -9.3775, -9.58522, 17.38562, -16.43286, 14.42029, -19.09419, 16.31052, -18.17053, 13.07062, -20.6281, 17.26111, 8.48515, 18.35852, 5.50851, 31.03757, 0.19013, 29.62781, 9.25864, 30.75708, 4.20927, 15.40279, 17.96623, 13.44565, 19.46948, 7.13205, 22.44003, 10.55536, 20.52547, -4.272, 32.45547, -7.6286, 31.8334, -16.66943, 28.15445, -8.25845, -0.48614, -8.16385, -1.34373, -7.48642, -3.62502, -0.32, -5.60599, 0.26324, -5.61073, 1.86902, -5.21806, 19.03642, 0.45663, 18.87778, 2.42794, 17.21097, 8.09174, 31.2434, -0.21861, 31.08905, 3.02866, 25.40129, -0.03862, 25.25919, 2.59746, 15.89282, 5.51108, 15.23218, 7.1354, 9.32977, 8e-05, 13.36231, 4e-05, 13.36231, 4e-05, 22.48672, 7e-05, 22.48672, 7e-05, 8.12066, -0.63556, 13.36231, 4e-05, 12.98215, 7e-05, 11.96137, 0.00021, 19.23263, 8e-05, 19.7805, 9e-05, 19.67255, 2.0585, 15.94521, 5e-05, 15.85825, 1.6594, 22.48672, 7e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13.36231, 4e-05, 10.84914, 6e-05, 13.03793, 5e-05, 13.03793, 5e-05, 12.96674, 1.35684, 13.03793, 5e-05, 12.96674, 1.35684, 14.27518, 3e-05, 22.47401, 8e-05, 16.85598, 6e-05, 28.71745, 37.43008, 26.92947, 28.79725, 31.25092, 21.41778, 28.85056, 24.55426, 35.51624, 15.68124, 33.69022, 19.29287, 21.09016, 6.02544, 20.34756, 8.18781, 28.51667, 1.33512, 28.21326, 4.28812, 25.66327, 12.59483, 15.88168, 23.32115, 13.36533, 24.84495, 5.46207, 27.69753, 16.26406, 21.28896, 13.95779, 22.86319, 6.61435, 25.94724, 18.83719, 14.24093, 17.25049, 16.12147, 23.78117, 0.99833, 23.54739, 3.46817, 23.64397, -9.5548, 24.50967, -7.04196, 5.77808, 5.13715, 5.20618, 5.70498, 3.207, 7.0451, 5.52441, 15.67936, 3.86118, 16.16636, -1.09363, 16.63106, 7.27936, 19.42194, 5.21817, 20.07079, -0.94577, 20.74303, 12.34967, 9.83542, 11.25656, 11.06464, 9.70383, -7.75512, 10.45639, -6.70466, 9.59363, -9.68468, 0.3782, 17.45095, -1.44455, 17.39437, -6.52457, 16.23573, 2.29705, 18.39801, 0.36572, 18.53358, -5.16208, 17.77859, 6.42796, 2.05053, 6.17825, 2.70617, 8.54809, -15.55346, 10.11929, -14.58078, 6.96161, -17.12347, -7.58021, 26.18869, -10.26596, 25.25584, -17.31723, 21.14177, -1.41707, 10.96007, -2.54874, 10.75415, -5.59619, 9.4834, -3.48637, 1.36965, -3.6102, 0.99954, -4.44144, 2.30121, 1.36707, -7.7699, 2.16705, -7.58691, 0.65411, -7.86284, 1.60547, -7.72603, -10.49731, 17.45684, -15.13013, 13.57071, 4.28281, 7.19267, 6.34805, 5.45829, 1.27785, -9.59321, 2.26837, -9.40955, 0.3992, -9.67036, 1.57025, -9.55122, -13.8056, 23.73058, -8.37595, 28.75646, -20.09314, 18.54037, 2.51013, 7.7993, 4.85977, 6.59768, 4.17645, 7.04977, -0.05429, -14.75346, -1.91, -14.61785, 0.04346, -14.76015, -6.1517, 21.04165, -14.16592, 14.74048, -11.32959, 16.71646, 5.34384, 3.04042, 5.83334, 1.94427, 5.5564, 2.63522, 0.96774, -21.22797, -3.17242, -21.01077, -0.6026, -21.24004, -0.31717, 11.06647, -1.46643, 10.9747, -3.728, 10.86108, -4.46603, 17.4769, -0.66595, 19.2619, 0.62619, 16.20811, -7.2149, 13.40346, -4.37881, 11.96817, -2.0954, 12.58794, 1.69064, 6.79282, 0.85669, 7.0526, -1.41501, 6.188, -0.32318, 6.2319, 11.56635, 3.85523, 8.37149, 8.86319, 9.72693, 7.35005, 8.88318, 8.34937, 1.27908, -19.54499, -2.5401, -19.42035, -0.16718, -19.58482, -7.4751, 10.29366, -5.66028, 11.39143, 2.03659, -0.27034, 2.02533, -0.02295, 2.10391, 0.81592, 2.245, 0.46727, 2.19452, 0.71507, 8.87711, 2.9588, 8.45557, 4.01019, 6.42496, 6.80251, 7.46564, 5.64116, 6.81726, 6.408, 1.24774, -17.8658, -2.24411, -17.76696, -0.07343, -17.90805, -14.65515, -0.0051, 3.78894, -13.16698, 5.21136, -12.74122, 10.03006, -8.68135, 8.54663, -10.04483, 9.61237, -8.95421, 1.26038, -16.31182, -1.92981, -16.24511, 0.05408, -16.35919, 0.56061, -15.35699, 0, 0, 0, 0, 0, 0, 0, 0, -22.56653, -3.89294, -22.0238, -6.2881, -19.73114, -7.15436, -18.85547, -9.22702, -12.89105, -12.7695, -11.45367, -14.07718, -7.36746, -16.29301, -5.34033, -17.06413, 1.20502, -17.83984, -1.77222, -17.79289, 0.13977, -17.88109, -26.89691, -9.94032, -25.68182, -12.76437, -19.77399, -15.12938, -18.04407, -17.16081, -0.75378, -19.9846, 5.39862, -19.25554, -1.46594, -23.36191, 1.03876, -23.38521, -17.13202, -33.51227, -13.44855, -35.155, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -11.19708, -1.27014, -7.797, -2.51447, -29.30823, -7.32985, -28.35852, -10.42725], "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "curve": 0.316, "c3": 0.65, "c4": 0.35}, {"time": 3.3333, "offset": 14, "vertices": [0.2544, 0, 0.25301, 0.02648, 0.2544, 0.14967, 0.23743, 0.17533, 0.17585, 0.10604, 0.16384, 0.12375, 0, 0.13156, -0.0137, 0.13084, 0, 0.14225, -0.01481, 0.14148, 0.01297, 0.14166, 0, 0.13811, -0.01438, 0.13736, 0.01259, 0.13753, 0, -0.05258, 0.00547, -0.0523, -0.00479, -0.05236, 0.0016, -0.05256, 0, -0.08553, 0.0089, -0.08507, -0.00779, -0.08517, 0.00257, -0.08549, 0, -0.05673, 0.0059, -0.05642, -0.00517, -0.05649, 0.00172, -0.05671, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.45384, -0.64445, -0.38228, -0.68936, -0.44047, -0.59746, -0.37402, -0.64121, -0.4097, -0.49251, -0.35467, -0.53357, -0.40652, -0.44742, -0.35635, -0.4884, -0.4432, -0.38193, -0.39982, -0.42722, -0.4917, -0.30112, -0.45671, -0.35206, -0.54899, -0.20707, -0.52374, -0.26469, -0.61565, -0.0948, -0.60204, -0.1602, 0, 0, 0, 0, 0, 0, 0.33923, -0.32064, 0.28137, -0.37257, 0.31825, -0.35455, 0.25504, -0.4025, 0.3391, 0.09842, 0.3508, 0.04071, 0.60561, 0.00371, 0.57811, 0.18066, 0.60014, 0.08213, 0.60665, 0.08521, 0.59444, 0.14785, 0.52528, 0.31524, 0.5704, 0.22361, 0.01197, 0.35638, -0.02522, 0.35566, -0.12806, 0.3328, -0.395, -0.17309, -0.37484, -0.21328, -0.29613, -0.3135, -0.24263, -0.09257, -0.2317, -0.11737, -0.18728, -0.17989, -0.5383, -0.09051, -0.52608, -0.14621, -0.46035, -0.2933, -0.6887, 0.41265, -0.72802, 0.33859, 0.05735, 0.41224, 0.014, 0.4158, 0.18204, 0, 0.18105, 0.01894, 0.18204, 0, 0.26073, 0, 0.26073, 0, 0.43877, 0, 0.43877, 0, 0, 0, 0.26073, 0, 0.25331, 0, 0.23339, 0, 0.37527, 0, 0.38596, 0, 0.38386, 0.04017, 0.31113, 0, 0.30943, 0.03238, 0.43877, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.26073, 0, 0.21169, 0, 0.2544, 0, 0.2544, 0, 0.25301, 0.02648, 0.2544, 0, 0.25301, 0.02648, 0.10336, 0, 0.2544, 0, 0.2544, 0, 0.19715, 0.51748, 0.37981, 0.39038, 0.2544, 0, 0.25301, 0.02648, 0.2544, 0, 0.25301, 0.02648, 0.11855, 0, 0.1179, 0.01233, -0.24773, 0.12696, -0.25976, 0.10031, -0.27769, 0.02031, 0.15227, 0.31082, 0.11905, 0.32493, 0.01888, 0.34563, 0.25714, 0.25554, 0.22911, 0.28087, 0.13701, 0.33567, 0.36756, 0.27787, 0.3366, 0.31457, 0.23345, 0.01948, 0.23014, 0.04367, 0.15344, -0.18644, 0.17201, -0.16946, -0.20171, -0.14856, -0.18524, -0.16887, -0.12778, -0.21543, -0.08106, 0.17536, -0.09892, 0.1659, -0.14309, 0.12985, 0.01126, 0.20432, -0.01011, 0.20434, -0.06939, 0.19255, 0.24097, 0.0584, 0.23354, 0.08311, 0.04217, -0.27256, 0.07029, -0.26673, 0.01718, -0.27528, -0.11807, 0.17074, -0.13526, 0.15744, -0.17535, 0.11117, 0.10383, 0.04198, 0.09882, 0.05247, 0.0792, 0.07923, 0.19802, -0.1152, 0.20891, -0.09401, 0.03489, -0.30348, 0.06626, -0.29824, 0.00711, -0.30541, -0.26241, 0.14577, -0.27618, 0.11762, -0.2985, 0.03183, 0.13729, 0.13404, 0.12257, 0.14758, 0.07408, 0.177, 0, 0.20592, -0.02144, 0.20479, 0.01876, 0.20506, 0.02667, -0.15161, 0.04228, -0.14804, 0.01276, -0.15342, 0.03133, -0.15075, 0.00024, 0.32622, -0.09511, 0.31213, 0, 0, 0, 0, 0.02493, -0.18719, 0.04426, -0.1836, 0.00779, -0.18869, 0.03064, -0.18637, 0.16395, 0.49063, 0.25618, 0.44943, 0.01338, 0.51716, 0, 0, 0, 0, 0, 0, 0.01508, -0.44219, -0.07106, -0.43668, -0.01763, -0.44207, 0.08409, 0.48963, -0.15733, 0.4713, -0.0768, 0.49082, 0.10427, 0.05933, 0.11382, 0.03794, 0.10842, 0.05142, 0.01888, -0.41421, -0.0619, -0.40997, -0.01176, -0.41444, 0.11876, 0.13983, 0.10354, 0.15141, 0.09266, 0.25415, 0.1092, 0.34298, 0.17378, 0.31524, 0.1499, 0.21223, 0.03175, 0.25791, -0.16811, 0.42548, -0.09509, 0.44747, 0.13053, 0.12404, 0.11465, 0.13891, 0.0564, 0.17101, 0.08405, 0.15926, 0.22569, 0.07522, 0.16335, 0.17294, 0.18979, 0.14342, 0.17333, 0.16291, 0.02496, -0.38137, -0.04956, -0.37893, -0.00326, -0.38214, -0.14586, 0.20085, -0.11044, 0.22227, 0.04254, 0.02418, 0.03939, 0.02915, 0.02605, 0.04143, 0.03261, 0.03652, 0.02842, 0.03979, 0.17321, 0.05773, 0.16499, 0.07825, 0.12537, 0.13273, 0.14567, 0.11007, 0.13302, 0.12503, 0.02435, -0.3486, -0.04379, -0.34667, -0.00143, -0.34943, -0.28596, -0.0001, -0.02963, -0.11138, -0.01588, -0.11414, 0.0265, -0.11215, 0.00754, -0.115, 0.01974, -0.11354, 0.02459, -0.31828, -0.03765, -0.31698, 0.00106, -0.3192, 0.01094, -0.29965, 0, 0, 0, 0, 0, 0, 0, 0, -0.44032, -0.07596, -0.42973, -0.1227, -0.385, -0.1396, -0.36791, -0.18004, -0.25153, -0.24916, -0.22349, -0.27468, -0.14376, -0.31791, -0.1042, -0.33296, 0.02351, -0.3481, -0.03458, -0.34718, 0.00273, -0.3489, -0.52482, -0.19396, -0.50111, -0.24906, -0.38584, -0.29521, -0.35208, -0.33485, -0.01471, -0.38994, 0.10534, -0.37572, -0.0286, -0.45584, 0.02027, -0.4563, -0.33428, -0.6539, -0.26241, -0.68595, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.21848, -0.02478, -0.15214, -0.04906, -0.57187, -0.14302, -0.55334, -0.20346]}]}, "f2": {"f2": [{"vertices": [10.99312, -7.77732, 13.75286, -9.72807, 14.30099, -8.90125, -0.84401, -7.6976, -0.38988, -7.73223, -4.29254, -6.06918, -3.92816, -6.31053, -3.42208, -4.83856, -3.1316, -5.03082, -1.11949, -5.81917, -1.40771, -1.99031, -1.28821, -2.06944, -0.46054, -2.39376, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.71707, 0.88679, -3.78781, -0.50492, -3.00734, 2.45782, 1.23432, 3.23556, 1.04187, 3.30323, -3.53351, 2.49917, -3.67443, 2.28726, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.12288, -1.50158, 7.64939, -5.41218, 6.49958, -4.59749, 4.78384, -3.38392, 4.97457, -3.09616, 2.25907, -1.59803, 2.3492, -1.4622, -0.2713, 0.19167, -0.28215, 0.17581], "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "vertices": [12.63718, -8.94044, 15.80965, -11.18294, 16.43976, -10.23247, -0.97024, -8.8488, -0.44819, -8.88861, -4.9345, -6.97685, -4.51563, -7.25429, -3.93387, -5.56218, -3.59994, -5.7832, -1.28692, -6.68945, -1.61823, -2.28796, -1.48087, -2.37894, -0.52941, -2.75175, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.27297, 1.01941, -4.35429, -0.58043, -3.4571, 2.82539, 1.41891, 3.71945, 1.19769, 3.79724, -4.06195, 2.87292, -4.22396, 2.62933, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.44036, -1.72615, 8.79338, -6.22159, 7.47161, -5.28506, 5.49929, -3.89, 5.71853, -3.5592, 2.59693, -1.83702, 2.70054, -1.68088, -0.31187, 0.22034, -0.32435, 0.2021], "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.3333, "vertices": [10.99312, -7.77732, 13.75286, -9.72807, 14.30099, -8.90125, -0.84401, -7.6976, -0.38988, -7.73223, -4.29254, -6.06918, -3.92816, -6.31053, -3.42208, -4.83856, -3.1316, -5.03082, -1.11949, -5.81917, -1.40771, -1.99031, -1.28821, -2.06944, -0.46054, -2.39376, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.71707, 0.88679, -3.78781, -0.50492, -3.00734, 2.45782, 1.23432, 3.23556, 1.04187, 3.30323, -3.53351, 2.49917, -3.67443, 2.28726, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.12288, -1.50158, 7.64939, -5.41218, 6.49958, -4.59749, 4.78384, -3.38392, 4.97457, -3.09616, 2.25907, -1.59803, 2.3492, -1.4622, -0.2713, 0.19167, -0.28215, 0.17581]}]}, "f3": {"f3": [{"curve": 0.25, "c3": 0.75}, {"time": 1.4667, "vertices": [1.35477, 9.95871, -1.5119, 9.93649, 1.35477, 9.95871, -5.41342, 8.46883, -1.5119, 9.93649, 1.35477, 9.95871, -5.41342, 8.46883, -1.5119, 9.93649, -5.41342, 8.46883, -5.41342, 8.46883, -5.50349, 8.41011, -5.41342, 8.46883, 2.45328, 14.83176, -9.80925, 11.39243, 9.13055, 10.78362, 7.40881, 12.03157, 0.72107, 14.11162, -10.844, 9.05811, 2.52449, 14.04448, -9.15935, 10.94232, 15.00571, 14.7273, -1.68176, 20.95926, 8.73132, 6.98787, -1.00793, 11.13772, 3.58755, 6.91557, -3.78662, 6.80848, -3.63786, 4.54544, -5.79814, -0.52477, -2.9124, 3.63907, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.96967, -8e-05, -2.91949, -0.54213, -2.84612, -0.84711, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.19298, 2.01783, -2.33597, 1.85071, -2.20978, 4.01757, -2.50019, 3.84372, -3.17737, 3.30637, -2.71503, 6.61983, -3.90924, 5.99278, -3.01917, 9.52413, -4.75296, 8.78849, -8.14578, 5.78752, -3.21934, 11.24631, -5.2728, 10.4426, -9.3429, 7.04176, -5.90849, 12.45985, -10.80429, 8.57097, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -76.68669, 64.0303, -97.07861, 23.58826, -85.98767, -50.86487, -116.90405, 23.36718, -100.03587, -64.8533, -61.43185, 16.12469, -55.24075, -31.32965, -43.99423, 11.20445, -39.32111, -22.69733, -25.92947, -4.76667, -15.23962, -21.51721, -18.20782, 2.63512, -14.87672, -10.8269, -14.57692, 1.37207, -13.77454, 4.94836, -12.85365, 5.91019, -10.97812, 8.91678, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -21.75632, -5.44043, -20.88672, -8.19409, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.66847, 2.33787, 2.28729, 2.71179, 0.7041, 3.47714, -2.35557, 2.65277, -1.94261, 2.96866, -1.91089, 2.98944, 8.36539, 2.37849, 6.1951, 6.10352, -1.1537, 8.61978, -0.44926, 7.77782, -0.36734, 7.78107, -0.53342, -8.35733, 3.54553, -7.58702, 8.19426, -1.7276, 6.0309, -4.56953, 5.97983, -4.63557, 3.5863, -6.66362, 6.24445, -4.4248, 3.91238, -6.5777, 2.67419, -4.49603, 3.186, -5.35622, 4.16205, -6.99708, 2.01761, -7.88747, 3.66302, -6.1584, 1.77576, -6.94206, 0, 0, 0, 0, 0, 0, 0, 0, 1.35477, 9.95871, 1.35477, 9.95871, 1.35477, 9.95871, -2.3027, 7.7258, -4.41107, 6.74815, -4.90003, -3.53656, -5.512, -4.54832, -3.68695, -4.78656, -3.85898, -3e-05, -3.794, -0.70453, -3.69854, -1.10097, -9.6082, -6e-05, -9.4464, -1.75428, -8.53724, -1.62814, 0, 0, -2.20434, 7.40498, -0.65201, -3.88686, 1.07649, -3.61612, -17.24944, 10.03233, -18.79084, 6.7146, -9.40588, -1.27608, -9.37384, -3.01926, -12.83112, 0.13274, 0, 0, 0, 0, 2.67311, 8.85684, -1.87343, 2.69131, -2.45589, 2.17325, -0.83241, 2.79645, -2.79234, 0.84911, -22.0185, 18.11145, -23.29462, 16.43835, -24.1809, 14.39274, -10.16489, -0.04899, -10.13341, -0.79855, -10.3442, -1.95139, -9.98581, 2.72645, -10.15959, 1.98257, 0, 0, 0, 0, 7.08722, 9.13103, 4.73243, 10.54242, 0.2406, 11.54202, -5.11369, 9.73741, -6.84401, 6.40254, -8.16283, 4.60686, -6.16698, 5.96205, -8.37634, -1.85197, -23.01957, 17.89194, -23.91334, 15.84773, -26.97931, 9.75424, -26.3125, 12.55731, -24.99687, -15.01173, -11.3439, 2.8825, -11.52543, 2.03796, -12.03847, 0.71574, -11.86855, -2.14815, -11.95142, -0.30103, -11.8965, -1.18173, 0, 0, 0, 0, 4.91992, 10.62638, 0.38045, 11.69234, -1.37628, 11.61459, -6.78549, 9.52768, -11.70305, 0.29178, -5.05391, 9.93393, -10.86252, 2.49167, 7.93211, 3.17136, 6.06612, 6.01528, 5.09579, 6.85565, 1.1774, 8.46136, -6.06092, 6.0184, 2.48944, 8.18298, -4.6828, 7.15771, 5.22897, 3.74288, 3.36014, 5.47835, 2.50214, 5.91803, -0.64783, 6.39339, -5.50983, 3.31253, -4.85689, 4.37924, -4.80829, 4.43176, -2.48747, 12.6242, -4.35275, 12.10815, -9.6337, 8.52998, -12.6075, -2.57114, -14.32704, -0.21242, -14.33014, -0.05978, 5.92079, 4.73856, -0.6835, 7.5526, 2.2074, 5.84652, 1.30569, 6.11173, -1.79025, 5.98752, -5.86116, 2.16852, -0.56317, 6.22385, -0.68143, 7.52921, -6.21495, 4.30469, 2.81909, 7.46683, 1.66714, 7.80545, -2.28647, 7.6468, -7.48543, 2.76947, -6.56131, 4.54454, -6.51212, 4.61433, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -9.61347, 2.12395, -9.84509, -0.20147, -8.99213, -4.02854, -5.8555, -6.28148, 1.78757, -3.382, 2.96362, -2.41009, 3.28433, -1.93801, 3.81827, -0.11888, 2.39562, 2.97696, 3.4046, -0.38818, 2.48242, 2.36693, 4.90938, 1.24003, 4.02602, 3.06624, 3.5144, 3.63639, 1.34238, 4.88011, -3.0872, 4.00433, 1.36058, 4.83801, -2.83896, 4.14995, -2.80362, 4.18018, 4.60336, 1.49293, 3.64978, 3.17544, 3.12732, 3.68669, 0.97729, 4.73833, -3.19624, 3.62515, 1.24612, 4.66655, -2.78078, 3.95271, -2.74747, 3.98143, 0.05477, 6.88528, -2.64105, 6.36925, -3.56891, 5.90364, -5.95972, 3.46501, -6.33937, -2.69147, -5.18378, 4.78827, -6.99788, -0.90283, -7.01727, -0.82841, 2.10995, -0.79286, 2.20148, -0.4682, 2.16037, 0.64925, 0.78015, 2.10944, 1.46706, 2.60797, 1.48676, 2.59415, 0.58093, -6.0842, 1.48343, -5.92877, 4.15283, -4.4852, 6.08129, 0.61481, 6.4931, 0.35606, 6.48862, 0.28822, -2.66133, -8.86774, -1.30368, -9.16728, 3.2627, -8.66479, 8.88657, -2.60767, 8.53606, -3.01421, 8.49521, -3.10449, -0.5751, -18.72859, 2.23666, -18.60381, 10.90085, -15.24077, 18.7334, -0.45676, 9.3911, -16.21107, 18.46391, -3.18323, 18.42044, -3.37926, 1.56676, -19.04567, 4.40182, -18.59609, 12.79651, -14.1945, 19.03618, 1.68753, 11.37775, -15.34995, 19.07668, -1.10655, 19.05606, -1.30838, -1.93709, -16.17683, 4.54985, -15.64698, 6.84183, -14.78841, 13.10803, -9.68298, 15.61779, 4.64828, 12.11794, -10.88871, 16.12791, 2.32172, 16.14389, 2.15088, 4.20234, -10.50208, 7.97826, -8.02173, 9.08743, -6.73494, 11.20932, -1.5405, 7.96996, 8.0253, 11.00534, -2.60277, 9.05585, 6.78067, 9.12021, 6.68561, 5.26259, -3.68392, 6.28685, -1.326, 6.40836, -0.36721, 5.80286, 2.75844, 1.2904, 6.28836, 5.91206, 2.50652, 1.86829, 6.14688, 11.69005, 3.77209, 9.28783, 8.04723, 7.96994, 9.34926, 2.50656, 12.03171, -8.10517, 9.22305, 3.81952, 12.00838, -6.76451, 10.63377, 15.15369, 5.61727, 11.74637, 11.0979, 9.94363, 12.73348, 2.61105, 15.94749, -11.17435, 11.66531, 4.11484, 15.62598, -9.35109, 13.18013, 0.41441, -5.6072, 1.96362, -5.26037, 3.86345, -4.0757, 0.71848, -9.72162, 2.52831, -9.41432, 3.651, -9.02506, 6.88409, -6.88582, -2.40187, -12.21446, -0.06966, -12.44779, 6.29135, -10.8353, 7.84012, -9.77167, 11.59268, -3.15034, 11.79025, 2.29321, 11.47084, 3.49053, 10.81342, 5.16826, 10.54182, -10.74227, 14.19952, -4.98351, 13.64301, 6.32562, 14.78583, 2.74326, 14.61441, -3.5056, 14.96973, -1.27919, 10.34323, -13.35991, 15.17557, -7.42013, 16.0459, 5.24988, 16.85608, 1.10422, 17.44461, -2.88751, 16.69171, 5.85168, 6.39507, 0.49034, 5.51898, 3.26452, 1.67369, 6.17459, 3.16367, 5.56625, 5.69495, 5.00779, 2.5936, 7.1315, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.04297, 10.7009, -7.4729, 9.1714, 2.30207, 6.49196, -3.37856, 6.00246, -6.77266, 1.25601, 4.87349, -1.08597, 5.54471, 2.88934, 5.04419, 3.68548, 6.48947, -4.06114, 7.61407, -0.77582, 8.64317, 1.25804, 8.35095, 2.53586, -0.50182, 2.4815, -1.54785, 2.00861, -2.5127, 0.35263, -2.33154, 0.96417, -1.03488, 2.97155, -2.42284, 0.59194, -2.43652, -0.53582, -1.36836, -2.09259, -1.82901, -1.68805, -1.06784, 0.27555, -1.06393, -0.26884, 1.18561, 1.03999, 0.12277, 1.57233, 0.50964, 1.49258, 0.14975, 1.5699, -17.74199, 4.61554, -16.02856, 8.87573, -17.04956, 6.73961, -8.76521, 16.30933, -11.11697, 14.80849, -16.85864, 7.65326, -16.24924, -8.8783, -17.37398, -6.40381, -17.44785, -6.21463, -15.30234, 10.93936, -16.59509, 8.87967, -17.07093, -7.92969, -15.14172, 11.87631, -16.55655, 9.8298, -17.80688, -7.32812, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.72442, -1.22313, -2.94846, -2.58384, -2.52814, -2.99712, -0.77765, -3.84241, 2.60352, -2.93243, -1.14139, -3.75071, 2.14684, -3.28027, 2.11011, -3.3028, -2.4414, -0.80145, -1.9328, -1.69352, -1.65744, -1.9646, -0.51001, -2.51869, 1.70666, -1.92252, -0.74881, -2.45852, 1.40698, -2.15034, 1.38239, -2.1649, -0.91698, -0.30067, -0.726, -0.63619, -0.62314, -0.73805, -0.19138, -0.94604, 0.64125, -0.72266, -0.28194, -0.92344, 0.52838, -0.80771, 0.56241, 0.18483, 0.44525, 0.38976, 0.11761, 0.58011, -0.39254, 0.44186, 0.17168, 0.56619, -0.32408, 0.49532, 1.77863, 0.58399, 1.40829, 1.23311, 0.54459, 1.79065, -1.02472, 1.56633, 5.19006, 2.6445, 4.73468, 3.39133, 2.52509, 5.24963, -2.68557, 5.16382, -1.63316, 5.48543, -1.57321, 5.50348, 2.10246, 1.84132, 1.80179, 2.13589, 0.5553, 2.73911, -1.85509, 2.08932, -1.53008, 2.33885, -1.5062, 2.35533, 1.34642, 1.17882, 1.15366, 1.36732, 0.35599, 1.7538, -1.18752, 1.33762, -0.97963, 1.49767, -0.96472, 1.50854, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9.06844, 4.82713, 8.24197, 6.13354, 4.28333, 9.33736, -4.88758, 9.03607, -3.96228, 9.10959, -3.86572, 9.15039, 7.04001, 5.64086, 6.1138, 6.63337, 2.17694, 8.75433, -5.68758, 7.00214, -4.64951, 7.68373, -4.56757, 7.73282, 3.00208, 2.63009, 2.57312, 3.05073, 0.79202, 3.91176, -2.65, 2.98428, -2.18544, 3.33969, -2.14978, 3.36307, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.27849, -0.24475, -0.23929, -0.28382, -0.0726, -0.36351, 0.24662, -0.27786, 0.20332, -0.31006, 0.19867, -0.31165, -1.56998, -1.37589, -1.34637, -1.59623, -0.41382, -2.04619, 1.38673, -1.5618, 1.14313, -1.74669, 1.12323, -1.75851, -2.23735, -1.9605, -1.91907, -2.27417, -0.59015, -2.9156, 1.97558, -2.22501, 1.62888, -2.48906, 1.60092, -2.50601, -2.82408, -2.47451, -2.42151, -2.87041, -0.745, -3.68018, 2.49336, -2.80862, -1.09352, -3.59225, 2.05605, -3.14178, 2.02084, -3.16321, -2.93379, -2.57072, -2.51593, -2.98202, -0.77383, -3.82306, 2.59037, -2.91751, -1.13599, -3.73191, 2.13593, -3.26389, 2.09955, -3.2863, 14.03259, 3.87616, 11.39616, 9.05713, 9.90424, 10.6635, 3.57028, 14.11211, -9.13098, 11.32947, 4.89488, 13.70776, -7.37904, 12.54842, 2.96367, -0.67661, 2.97746, 0.54934, 2.85202, 0.99117, 2.03244, 2.2419, -0.55669, 2.97995, 1.22978, 2.28829, -0.96655, 2.41321, 10.25481, -5.58823, 11.61479, -1.14043, 11.64532, 0.61132, 9.927, 6.13437, 1.05701, 11.62323, 10.22908, 4.51577, 3.09526, 10.74768, 3.20428, 10.71365, 17.23608, -10.72311, 20.05945, -3.15515, 20.29904, -0.11844, 17.86719, 9.65065, 2.99825, 20.07315, 19.41814, 6.53317, 7.4404, 19.09305, 7.6427, 19.01212, 8.18606, -10.2836, 11.56888, -6.27928, 12.3746, -4.47814, 13.01028, 2.02025, 6.19, 11.59692, 13.91118, 0.2191, 8.75427, 10.81961, 8.86572, 10.72729, -2.55283, -16.99791, 4.30244, -16.64666, 6.74597, -15.81482, 13.51691, -10.62836, 16.61694, 4.40915, 12.38681, -12.06269, 17.20119, 1.77525, 17.21185, 1.592, -3.99287, -17.16446, 3.04463, -17.36049, 5.61087, -16.70801, 12.94955, -11.95821, 17.34111, 3.15421, 11.74379, -13.13857, 17.61424, 0.59227, 17.61157, 0.40538, 1.93338, -4.763, 2.27959, -4.60745, -4.53854, -0.53678, -3.34262, -2.53155, -2.82848, -1.41108, -2.51357, -1.91677, -1.40857, -2.82973, -2.86938, -5.62226, 0.55775, -6.29006, 1.4917, -6.13673, -2.45366, -1.85825, -1.38074, -2.75072, -1.0869, -2.87935, -0.64212, -3.00969, -3.26921, -2.47595, -1.83966, -3.66493, 1.24232, -3.90823, 0.23181, -4.09329, -1.44817, -3.83647, -0.85553, -4.01014, 1.17471, -3.92871, -4.56212, -3.45514, -2.56711, -5.11469, 1.73334, -5.45395, 0.32422, -5.71326, -1.1947, -5.59649, 1.6394, -5.48282, -3.28381, -2.48703, -1.84775, -3.68152, 1.24773, -3.92581, 0.23337, -4.11218, -0.85974, -4.02832, 1.18008, -3.94647, -9.34192, 2.18439, -9.525, 1.1478, -9.52654, -1.13404, 6.60794, 3.26923, 5.877, 4.45103, 5.09606, 5.32762, 2.60405, 6.89725, 8.52428, 4.21737, 7.58134, 5.74189, 6.5739, 6.87271, 3.35922, 8.8975, -3.05321, 8.63542, -7.18108, 5.70594, -7.95822, 4.5674, 1.40602, 13.89711, -4.87393, 13.093, -6.20222, 12.53633, -8.01425, 11.46809, 5.80208, 13.03453, -0.55081, 14.26044, -2.03409, 14.14327, -4.13528, 13.68169, 7.12589, 5.39695, 4.00961, 7.98943, -2.70728, 8.51926, -0.50714, 8.9249, 3.15738, 8.36293, 1.86685, 8.74212, -2.56079, 8.56454, -0.26291, 11.85606, -5.47299, 10.52455, -5.91101, 10.50639, -7.4252, 9.50131, -5.44011, 5.04408, -7.10889, 2.12468, -6.85608, 2.01756, -7.08362, 0.96523, -3.95834, 4.37102, -5.47983, 2.17647, -5.74738, 1.3291, -3.41185, 3.12202, -3.93762, 2.42626, -4.06307, 2.20875, -4.60324, 0.44051, -3.9377, 1.3433, -4.12021, 0.58043, -4.14465, 0.35581, -3.95285, -1.29655, -4.68344, -0.10933, -4.66273, -0.45432, -1.67576, 4.03434, -1.96893, 3.89985, -2.66603, 3.46115, -6.03951, 16.83537, -12.85428, 12.43869, -17.88402, -0.07101, -19.40414, 8.05772, -19.52121, -7.78049, -17.38943, 10.65948, -19.8933, -4.51126, -5.88541, -1.13214, -3.42567, -4.92157, 0, 0, 0.98184, -8.67551, 6.75381, -5.53531, 5.16736, -7.03769, 3.1131, -8.15728, 6.65009, -5.65794, -0.95669, -3.11005, -0.35832, -3.23467, -0.87664, -0.36638, -0.79442, -0.52473, -0.48224, -0.82118, 6.32968, -6.71201, 7.47456, -5.40563, 9.09225, -1.54997, 14.60782, -13.08319, 18.88107, -5.28912, 20.76331, -6.50784, 7.14795, -4.74361, -3.81342, -3.73506, -1.77341, -5.03345, 2.23065, -4.84888, 0.957, -5.24991, -0.48325, -5.31318, 2.13077, -4.89093, -4.67255, -0.33693, -4.04294, -2.36402, -1.25163, -4.5166, -2.3349, -4.06375, -3.33313, -3.29717, -1.33792, -4.49216, -0.55125, -6.91077, -2.24942, -6.55765, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15.64491, -14.77231, 18.13809, -11.57464, 9.03104, -12.50341, 11.21613, -10.58689, 14.73846, -4.54248, 5.0791, -9.03083, 8.54495, -5.8569, 0.45392, -7.17821, 3.5773, -6.23718, -2.86736, -3.22088, -1.15048, -4.15405, -1.15102, -4.15515, -0.51773, -4.28181, -5.99696, 0.92057, -5.78732, -1.82074, -2.86862, -5.34576, -4.09512, -4.46622, -5.45169, -2.66774, -3.49838, -4.95584, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -9.95694, 5.0134, -10.62584, -3.36389, -11.1171, -0.621, -10.59641, 3.46631, -10.95572, -2.04572, -14.724, -0.96411, -14.48969, -2.84966, -18.57803, -2.28192, -18.14117, -4.6526, -2.22573, 9.92815, -4.04932, 9.33425, -7.75595, 6.58771, -2.12717, 12.54199, -4.44318, 11.92011, -9.25128, 8.73386, -1.71939, 16.94477, -9.0264, 14.44551, 0.83228, 20.73444, -8.41098, 18.97203, -19.26381, 7.70831, 6.07489, 12.10461, -4.10162, 12.9071, 4.71207, 8.34505, -2.45235, 9.26401, -1.50639, 4.74072], "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "x3": {"x1": [{"vertices": [-5.52033, 6.21382, -4.33983, 4.88466, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.58613, -9.08082, -7.0602, -6.27077, -2.58613, -9.08082, -7.0602, -6.27077, -5.14888, -10.00795, -9.72183, -5.67598, -3.21757, -11.29851, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.44708, -1.72092, -0.48688, -1.70997, -1.32936, -1.18071, 0.50962, -1.9617, -0.55504, -1.94883, -1.5156, -1.34578, -0.96495, -3.17845, -2.52352, -2.16304, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -7.71761, 8.68649, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16.09271, -8.03076, 0, 0, 0, 0, 18.04952, -7.75439, 17.61622, -8.69025, 0, 0, 0, 0, 18.63858, -7.79201, 0, 0, 0, 0, 18.5697, -5.71735, 0, 0, 0, 0, 18.44935, -3.1177, 0, 0, 0, 0, 18.26859, -0.53152, 0, 0, 0, 0, 17.88788, 2.10186, 17.37068, -4.77104, 0, 0, 0, 0, 3.25709, -1.013, 0, 0, 3.96099, -5.51076, 0.62186, -6.75471, 3.26129, 0.18832, 2.90717, -1.48896, 4.2372, -9.61859, -1.22232, -10.43553, 2.37739, -4.77065, -0.36858, -5.3156, 4.77088, -12.51613, -2.23087, -13.20415, 1.96127, -10.08531, -3.42129, -9.68579, 6.22478, -16.5078, -3.00112, -17.38174, -11.8718, -13.04978, 2.65878, -15.18365, -5.40425, -14.43399, -3.24347, -20.61631, -13.81398, -15.6484, -7.14852, -17.68949, -1.66482, -17.85034, -10.99724, -14.16333, -3.09106, -10.85345, -8.43798, -7.4946, 4.1585, -5.79803, 0.38643, -7.12436, -1.60878, -5.64923, -4.39193, -3.90116, 0.79889, -4.64651, 0, 0, 4.58805, 0.24337, 3.99381, -2.25926, 0, 0, 1.39388, 0.99178, 0, 0, -0.23498, 3.23967, 0, 0, -1.8296, 5.79036, 0, 0, -3.19735, 7.73718, 0, 0, -4.3567, 9.22666], "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.2, "vertices": [-5.98218, 6.73369, -4.70291, 5.29332, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.80249, -9.84055, -7.65088, -6.79539, -2.80249, -9.84055, -7.65088, -6.79539, -5.57965, -10.84525, -10.53519, -6.15085, -3.48676, -12.24377, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.48448, -1.8649, -0.52762, -1.85303, -1.44058, -1.2795, 0.55226, -2.12582, -0.60147, -2.11188, -1.6424, -1.45837, -1.04568, -3.44437, -2.73465, -2.34401, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -8.36328, 9.41322, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17.43907, -8.70264, 0, 0, 0, 0, 19.55959, -8.40314, 19.09003, -9.4173, 0, 0, 0, 0, 20.19793, -8.44391, 0, 0, 0, 0, 20.12329, -6.19568, 0, 0, 0, 0, 19.99287, -3.37854, 0, 0, 0, 0, 19.79699, -0.57599, 0, 0, 0, 0, 19.38443, 2.27771, 18.82396, -5.1702, 0, 0, 0, 0, 3.52959, -1.09775, 0, 0, 4.29237, -5.9718, 0.67389, -7.31982, 3.53413, 0.20407, 3.15039, -1.61353, 4.59169, -10.42331, -1.32458, -11.30859, 2.57629, -5.16977, -0.39941, -5.76031, 5.17003, -13.56326, -2.41751, -14.30884, 2.12535, -10.92908, -3.70752, -10.49612, 6.74556, -17.88889, -3.2522, -18.83594, -12.86502, -14.14156, 2.88123, -16.45395, -5.85638, -15.64157, -3.51483, -22.34113, -14.9697, -16.95758, -7.74658, -19.16943, -1.80411, -19.34375, -11.9173, -15.34827, -3.34967, -11.76147, -9.14392, -8.12161, 4.50641, -6.28311, 0.41876, -7.7204, -1.74338, -6.12186, -4.75937, -4.22754, 0.86572, -5.03525, 0, 0, 4.97189, 0.26373, 4.32794, -2.44827, 0, 0, 1.5105, 1.07475, 0, 0, -0.25464, 3.51071, 0, 0, -1.98267, 6.2748, 0, 0, -3.46484, 8.38449, 0, 0, -4.72119, 9.99858], "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 3.3333, "vertices": [-5.52033, 6.21382, -4.33983, 4.88466, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.58613, -9.08082, -7.0602, -6.27077, -2.58613, -9.08082, -7.0602, -6.27077, -5.14888, -10.00795, -9.72183, -5.67598, -3.21757, -11.29851, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.44708, -1.72092, -0.48688, -1.70997, -1.32936, -1.18071, 0.50962, -1.9617, -0.55504, -1.94883, -1.5156, -1.34578, -0.96495, -3.17845, -2.52352, -2.16304, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -7.71761, 8.68649, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16.09271, -8.03076, 0, 0, 0, 0, 18.04952, -7.75439, 17.61622, -8.69025, 0, 0, 0, 0, 18.63858, -7.79201, 0, 0, 0, 0, 18.5697, -5.71735, 0, 0, 0, 0, 18.44935, -3.1177, 0, 0, 0, 0, 18.26859, -0.53152, 0, 0, 0, 0, 17.88788, 2.10186, 17.37068, -4.77104, 0, 0, 0, 0, 3.25709, -1.013, 0, 0, 3.96099, -5.51076, 0.62186, -6.75471, 3.26129, 0.18832, 2.90717, -1.48896, 4.2372, -9.61859, -1.22232, -10.43553, 2.37739, -4.77065, -0.36858, -5.3156, 4.77088, -12.51613, -2.23087, -13.20415, 1.96127, -10.08531, -3.42129, -9.68579, 6.22478, -16.5078, -3.00112, -17.38174, -11.8718, -13.04978, 2.65878, -15.18365, -5.40425, -14.43399, -3.24347, -20.61631, -13.81398, -15.6484, -7.14852, -17.68949, -1.66482, -17.85034, -10.99724, -14.16333, -3.09106, -10.85345, -8.43798, -7.4946, 4.1585, -5.79803, 0.38643, -7.12436, -1.60878, -5.64923, -4.39193, -3.90116, 0.79889, -4.64651, 0, 0, 4.58805, 0.24337, 3.99381, -2.25926, 0, 0, 1.39388, 0.99178, 0, 0, -0.23498, 3.23967, 0, 0, -1.8296, 5.79036, 0, 0, -3.19735, 7.73718, 0, 0, -4.3567, 9.22666]}]}, "s1": {"s1": [{"curve": 0.25, "c3": 0.75}, {"time": 1.4667, "vertices": [44.39878, 87.30377, 60.12927, 47.2822, 76.10037, -7.61688, 50.36063, 58.14166, 59.47063, 42.02142, 69.13763, -23.73498, 71.97931, -10.95171, 50.75889, 52.80756, 57.73048, 31.54681, 59.59903, -28.50813, 63.46286, -17.29376, 56.66338, 22.85748, 51.90662, -32.77215, 56.66953, -22.8159, 56.27985, 18.63806, 53.46777, -25.59103, 21.33201, 52.59396, 55.69012, 10.78116, 24.30357, 40.17242, 46.89732, 1.52719, 27.17048, 25.09607, 35.82179, -9.08673, 29.8514, 9.39453, 24.12123, -19.88455, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -8.18138, -1.48532, 6.68823, 4.94102, -10.3317, -1.37997, 8.31177, 6.0083, -15.40531, 7.53827, 16.99789, 0.4859, -19.5994, 14.20087, 20.21683, 13.09051, 23.84824, -3.44418, 23.65546, 4.35398, -22.64996, 17.8114, 28.25397, -5.20918, 28.39966, 4.09834, -28.53268, 24.12738, 36.44327, -8.04655, 37.07687, 4.04108, -35.93859, 33.10266, 47.23199, -12.54181, 48.75253, 3.24297, 45.91449, 16.69264, 60.496, 2.6079, 57.29218, 19.368, 65.41254, 2.48006, 62.02631, 20.61319, 0.23404, 65.44135, 69.72501, -1.61546, 67.27579, 17.89149, 4.50984, 69.59186, 72.15689, 13.88626, 9.87405, 72.9935, 95.53577, -6.62651, 36.79716, 89.05594, 40.05784, 91.36438, -1.867, 0.70468, 1.98651, 0.19086, 0, 0, 0, 0, 0.46969, 6.49347, 2.42145, -6.04991, 12.24364, 7.24045, 12.72705, -6.29913, 0.15002, 19.20059, 16.16589, 10.35056, 15.10229, 22.29663, 26.90314, -0.49637, 15.35553, -22.38158, -0.80474, 29.7796, 24.51326, 16.91205, 28.08234, -10.15345, 14.47379, -26.11069, 12.97842, 34.62366, 36.07875, 7.99704, 27.67554, -24.82204, 4.51624, -36.88208, -11.01116, 31.82327, 20.66978, 26.57941, 33.65976, -1.30045, 24.49594, -23.11908, -0.49192, 39.67026, 32.97343, 22.03616, 37.24399, -14.00275, 18.83698, -35.03552, 13.8569, 48.92435, 48.54359, 15.04892, 45.40408, -22.81371, 43.6218, 30.62595, 52.65726, -8.17525, 48.82547, -4.98002, 33.48749, -35.86682, 43.21393, -23.29541, 48.80014, 3.85576, -21.64707, 29.30777, 32.76251, -15.88335, 36.10526, -4.57436, 49.41719, 36.65543, 58.8446, -18.73321, 47.22278, -39.8493, 61.0137, -7.84879, 56.30457, -3.56734, 40.04669, -39.72372, 50.67062, -24.84966, 55.44275, -9.57256, 55.87836, 6.55751, 48.74579, -3.61758, 47.79166, 10.14537, 69.14307, -4.85832, 44.09157, 53.96939, 65.36166, 8.80971, 30.61907, 58.70721, 62.1506, 6.70546, 13.59093, 61.14352, 71.30463, -21.81189, 74.57251, -0.67201, 43.70502, 60.89194, 80.6723, -14.85428, 81.58148, 8.66794, 39.97609, 72.13162, 89.50363, -9.82735, 88.62534, 16.00279, 37.95331, 82.23236, 37.73315, -31.02856, 45.68436, -17.34347, 53.24854, -4.51431, 37.12317, -38.42535, 47.48233, -24.55602, 53.05023, 5.09145, 46.59016, 33.86868, 54.90948, -18.08027, 43.83258, -37.7415, 57.0437, -7.89668, 61.04349, -6.14349, 54.06815, -29.04516, 59.8432, -12.63483, 60.96991, 4.87044, 51.71906, 40.56702, 63.38107, -18.29504, 51.58524, -41.17826, 65.38379, -6.6263, 69.35541, -9.9167, 69.32068, 10.1825, 31.73586, 62.77762, 56.39343, 47.46185, 73.53345, -4.89748, 46.63129, 57.58154, 71.53464, 8.09479, 15.20712, 70.56665, 65.43689, 4.49005, 16.74416, 63.58469, 61.0058, -2.86777, 59.28363, 14.28508, 5.40837, 60.81683, 51.03516, -2.62809, 49.70108, 11.72472, 88.72836, -5.76845, 86.72708, 19.6758, 33.8119, 82.76654, 78.52576, -10.92232, 78.40363, 11.82883, 35.53366, 71.29849, 69.15887, -14.5061, 70.43756, 5.72449, 36.05743, 61.13748], "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "f6": {"f6": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "offset": 118, "vertices": [1.15732, 5.97797, 3.30726, 5.11249, 5.61233, 2.36124, -3.92279, 4.65648, -4.18579, 4.4214, -3.677, 4.85251, 3.09717, 4.78778, -3.67371, 4.36073, -3.91992, 4.14047, -3.44348, 4.54425, 1.98435, 3.0676, -2.35373, 2.79398, -2.51154, 2.65286, -2.2063, 2.91158, 3.12827, 4.83594, -3.71066, 4.40468, -3.95947, 4.18239, -3.47821, 4.59024, 4.18925, 6.47586, -4.96902, 5.89838, -5.30225, 5.60077, -4.65765, 6.14687, 2.06782, 3.19598, -2.61627, 2.7623, -2.29718, 3.03197, 5.11261, 7.90344, -6.06448, 7.19865, -6.4711, 6.83537, -5.68451, 7.50194, 4.42, 6.83286, -5.24286, 6.22346, -5.59433, 5.90936, -4.91443, 6.48561, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.53211, -2.15274, 2.21729, 0.00391, 2.20721, -0.23764, -0.14597, 0.44696, -0.17276, 0.43533, 0, 0, 0, 0, 4.69256, 7.25372, -5.56561, 6.60658, -5.93909, 6.27284, -5.21667, 6.88467, 0.44531, -3.30589, 0.85596, 4.42142, 2.44609, 3.78134, -2.90146, 3.44406, -3.09586, 3.27014, -2.71973, 3.58907, 2.96042, -3.51398, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.06782, 3.19598, -2.45117, 2.9104, -2.61627, 2.7623, -2.29718, 3.03197, 2.06782, 3.19598, -2.45117, 2.9104, -2.61627, 2.7623, -2.29718, 3.03197, 0.72336, 3.73764, 2.06782, 3.19598, -2.45117, 2.9104, -2.61627, 2.7623, -2.29718, 3.03197], "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "f5": {"f5": [{}, {"time": 1.6667, "offset": 70, "vertices": [-4.12225, -4.83145, -2.19412, -5.95999, -2.41953, 1.45325, -2.77356, 0.52539, -2.7233, -2.36334, -1.73691, -3.1601, 0.99368, -2.90317, 1.93689, -2.37886, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.00079, -9.22491, -1.5098, -10.38063, 0.24847, -8.45715, 3.14865, -7.84192, 2.93076, -5.56677, 4.66821, -4.20332, -0.07086, -0.05573, -0.05502, -0.0737, 1.6449, 0.40137, -2.32831, -8.83337, 0.86157, -9.08772, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.28708, -7.38184, 4.68863, -6.12688, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.892, -0.59726, -0.63745, -0.86615, 0, 0, 0, 0, 0.58853, 0.11963, 0.50311, 0.31921]}, {"time": 3.3333}]}, "x2": {"x1": [{"vertices": [-3.43026, 3.86118, -2.69671, 3.03526, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.60698, -5.64269, -4.38711, -3.89657, -1.60698, -5.64269, -4.38711, -3.89657, -3.19944, -6.2188, -6.04101, -3.52697, -1.99935, -7.02073, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.27781, -1.06936, -0.30254, -1.06255, -0.82605, -0.73368, 0.31667, -1.21897, -0.34489, -1.21098, -0.94177, -0.83625, -0.59961, -1.97504, -1.56808, -1.34408, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.79561, 5.39766, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9.99978, -4.9902, 0, 0, 0, 0, 11.21571, -4.81847, 10.94647, -5.4, 0, 0, 0, 0, 11.58175, -4.84184, 0, 0, 0, 0, 11.53895, -3.55268, 0, 0, 0, 0, 11.46417, -1.9373, 0, 0, 0, 0, 11.35184, -0.33028, 0, 0, 0, 0, 11.11528, 1.30607, 10.7939, -2.96466, 0, 0, 0, 0, 2.02391, -0.62946, 0, 0, 2.4613, -3.42431, 0.38642, -4.19728, 2.02652, 0.11702, 1.80647, -0.92522, 2.63293, -5.97686, -0.75953, -6.48449, 1.47728, -2.96441, -0.22903, -3.30304, 2.96456, -7.77735, -1.38623, -8.20487, 1.2187, -6.26687, -2.12594, -6.01861, 3.86799, -10.25771, -1.86485, -10.80076, -7.37697, -8.10895, 1.65213, -9.4349, -3.35812, -8.96907, -2.01545, -12.81068, -8.58381, -9.72369, -4.44199, -10.99199, -1.0345, -11.09195, -6.83353, -8.80089, -1.92074, -6.74418, -5.24324, -4.65703, 2.58403, -3.60282, 0.24012, -4.42697, -0.99967, -3.51035, -2.72908, -2.42412, 0.49642, -2.88727, 0, 0, 2.85095, 0.15123, 2.4817, -1.40387, 0, 0, 0.86614, 0.61628, 0, 0, -0.14601, 2.01309, 0, 0, -1.13689, 3.59805, 0, 0, -1.98679, 4.80777, 0, 0, -2.70719, 5.73331], "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 1.0333}, {"time": 2.5, "vertices": [-5.98218, 6.73369, -4.70291, 5.29332, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.80249, -9.84055, -7.65088, -6.79539, -2.80249, -9.84055, -7.65088, -6.79539, -5.57965, -10.84525, -10.53519, -6.15085, -3.48676, -12.24377, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.48448, -1.8649, -0.52762, -1.85303, -1.44058, -1.2795, 0.55226, -2.12582, -0.60147, -2.11188, -1.6424, -1.45837, -1.04568, -3.44437, -2.73465, -2.34401, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -8.36328, 9.41322, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17.43907, -8.70264, 0, 0, 0, 0, 19.55959, -8.40314, 19.09003, -9.4173, 0, 0, 0, 0, 20.19793, -8.44391, 0, 0, 0, 0, 20.12329, -6.19568, 0, 0, 0, 0, 19.99287, -3.37854, 0, 0, 0, 0, 19.79699, -0.57599, 0, 0, 0, 0, 19.38443, 2.27771, 18.82396, -5.1702, 0, 0, 0, 0, 3.52959, -1.09775, 0, 0, 4.29237, -5.9718, 0.67389, -7.31982, 3.53413, 0.20407, 3.15039, -1.61353, 4.59169, -10.42331, -1.32458, -11.30859, 2.57629, -5.16977, -0.39941, -5.76031, 5.17003, -13.56326, -2.41751, -14.30884, 2.12535, -10.92908, -3.70752, -10.49612, 6.74556, -17.88889, -3.2522, -18.83594, -12.86502, -14.14156, 2.88123, -16.45395, -5.85638, -15.64157, -3.51483, -22.34113, -14.9697, -16.95758, -7.74658, -19.16943, -1.80411, -19.34375, -11.9173, -15.34827, -3.34967, -11.76147, -9.14392, -8.12161, 4.50641, -6.28311, 0.41876, -7.7204, -1.74338, -6.12186, -4.75937, -4.22754, 0.86572, -5.03525, 0, 0, 4.97189, 0.26373, 4.32794, -2.44827, 0, 0, 1.5105, 1.07475, 0, 0, -0.25464, 3.51071, 0, 0, -1.98267, 6.2748, 0, 0, -3.46484, 8.38449, 0, 0, -4.72119, 9.99858], "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 3.3333, "vertices": [-3.43026, 3.86118, -2.69671, 3.03526, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.60698, -5.64269, -4.38711, -3.89657, -1.60698, -5.64269, -4.38711, -3.89657, -3.19944, -6.2188, -6.04101, -3.52697, -1.99935, -7.02073, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.27781, -1.06936, -0.30254, -1.06255, -0.82605, -0.73368, 0.31667, -1.21897, -0.34489, -1.21098, -0.94177, -0.83625, -0.59961, -1.97504, -1.56808, -1.34408, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.79561, 5.39766, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9.99978, -4.9902, 0, 0, 0, 0, 11.21571, -4.81847, 10.94647, -5.4, 0, 0, 0, 0, 11.58175, -4.84184, 0, 0, 0, 0, 11.53895, -3.55268, 0, 0, 0, 0, 11.46417, -1.9373, 0, 0, 0, 0, 11.35184, -0.33028, 0, 0, 0, 0, 11.11528, 1.30607, 10.7939, -2.96466, 0, 0, 0, 0, 2.02391, -0.62946, 0, 0, 2.4613, -3.42431, 0.38642, -4.19728, 2.02652, 0.11702, 1.80647, -0.92522, 2.63293, -5.97686, -0.75953, -6.48449, 1.47728, -2.96441, -0.22903, -3.30304, 2.96456, -7.77735, -1.38623, -8.20487, 1.2187, -6.26687, -2.12594, -6.01861, 3.86799, -10.25771, -1.86485, -10.80076, -7.37697, -8.10895, 1.65213, -9.4349, -3.35812, -8.96907, -2.01545, -12.81068, -8.58381, -9.72369, -4.44199, -10.99199, -1.0345, -11.09195, -6.83353, -8.80089, -1.92074, -6.74418, -5.24324, -4.65703, 2.58403, -3.60282, 0.24012, -4.42697, -0.99967, -3.51035, -2.72908, -2.42412, 0.49642, -2.88727, 0, 0, 2.85095, 0.15123, 2.4817, -1.40387, 0, 0, 0.86614, 0.61628, 0, 0, -0.14601, 2.01309, 0, 0, -1.13689, 3.59805, 0, 0, -1.98679, 4.80777, 0, 0, -2.70719, 5.73331]}]}, "zs6": {"zs6": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "offset": 144, "vertices": [12.409, -6.15942, 12.72662, 5.48421, 12.55621, 5.85406, 13.85535, 0.16157, 11.94543, 3.9254, 10.88773, 6.28992, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11.61499, 1.93053, 10.97296, 4.26968, 11.70105, 0.39563, 11.37054, 2.78464], "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "l1": {"l1": [{"curve": 0.25, "c3": 0.75}, {"time": 1.4667, "offset": 18, "vertices": [5.0909, 2.89719, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.11945, 3.07819, 5.117, 2.91207, -2.0159, -1.21225, -2.01469, -1.14656, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.19377, -0.71775, -1.19304, -0.67895, 1.22989, 0.73954, 1.22949, 0.69969, -2.0159, -1.21225, -2.01469, -1.14656], "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 1.8667, "offset": 18, "vertices": [4.31812, 2.45741, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.34234, 2.61094, 4.34026, 2.47003, -1.7099, -1.02824, -1.70887, -0.97252, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.01256, -0.6088, -1.01194, -0.57589, 1.0432, 0.62728, 1.04286, 0.59348, -1.7099, -1.02824, -1.70887, -0.97252], "curve": 0.312, "c2": 0.26, "c3": 0.653, "c4": 0.62}, {"time": 2.3, "offset": 2, "vertices": [-5.28848, -3.36076, -5.28543, -3.1744, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.91918, 1.66129, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.93555, 1.76508, 2.93415, 1.66982, -3.87999, -2.34065, -3.87768, -2.20681, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.68452, -0.41157, -0.6841, -0.38932, 0.70523, 0.42406, 0.70501, 0.40121, -2.75088, -1.65861, -2.74918, -1.56457, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.0132, -1.21604, -2.01196, -1.14502, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.58989, -1.5641, -2.58837, -1.47309, -2.75967, -1.66706, -2.758, -1.56962, -3.01076, -1.81868, -3.009, -1.71248, -3.01672, -1.8223, -3.01491, -1.71582, -3.45268, -2.08566, -3.45071, -1.96386, -2.00723, -1.21226, -2.00609, -1.14174, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.55406, -0.33437, -0.55377, -0.31516, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.4957, -2.89771, -2.49408, -2.80964, -3.9919, -4.19645, -3.98943, -4.05579, -4.13455, -5.48035, -4.13203, -5.33562, -8.04666, -3.35476, -8.04185, -3.07122, -4.17723, -2.79916, -4.17467, -2.65188, -2.66936, -1.61217, -2.66779, -1.51825, -5.38295, -3.25127, -5.37968, -3.06157, -3.72916, -0.76906, -3.72714, -0.63808, 1.24249, 0.77107, 1.24178, 0.7268, 1.74043, 2.91232, 1.7392, 2.85017, 2.79131, 3.07417, 2.78941, 2.97496, 2.87317, 3.49512, 2.87121, 3.39281, 0.25119, 1.33736, 0.25089, 1.32794, 1.56142, 1.53688, 1.5603, 1.48121, 1.96614, 2.39304, 1.96487, 2.32315, 0.62741, 1.86275, 0.62686, 1.83979, -1.46835, -0.8868, -1.46752, -0.83519], "curve": 0.342, "c2": 0.36, "c3": 0.692, "c4": 0.75}, {"time": 2.9, "offset": 18, "vertices": [-1.16414, -0.73257, -1.5638, 1.70711, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.95245, -0.1277, 1.95216, -0.17305, -0.30803, -0.18523, -0.30784, -0.17519, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.18241, -0.10967, -0.1823, -0.10374, 0.18793, 0.113, 0.18787, 0.10691, -0.30803, -0.18523, -0.30784, -0.17519], "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 3.3333}]}, "f4": {"f4": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "vertices": [-8.47862, 2.96127, -2.01707, 1.84538, -3.27837, 2.9993, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.36813, -0.30084, 3.20477, -1.52144, 3.08215, -2.43861, 1.51064, -1.99599, -1.60434, -1.11845, -10.88113, 5.92592, -12.03795, 6.96468, -10.08672, 7.44963, -0.33022, 1.73286, 1.83257, 2.27071, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.78931, 0.50089, -5.78931, 0.50089, -5.78931, 0.50089, -9.78998, 4.16109, -1.34126, 7.17163, -1.16734, 2.50253, 0.88531, 8.1781, -4.35297, 3.90964, 1.62181, -1.82201, 1.41313, 4.74539, 4.28453, -3.91983, -2.0517, -1.2921, 0, 0, 0, 0, -5.78931, 0.50089, -14.62634, 8.58578, -2.29478, 2.0995, -9.71416, 8.88734, -4.37862, 4.00594, -2.67279, 2.44527, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.10318, 0.40193, -0.6815, 0.57117, 1.25168, -1.12292], "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}}}}}}