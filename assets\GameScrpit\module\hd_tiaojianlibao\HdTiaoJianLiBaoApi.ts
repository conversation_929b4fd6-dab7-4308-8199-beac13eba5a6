import MsgEnum from "../../game/event/MsgEnum";
import { <PERSON>piHandler, ApiHandlerFail, ApiHandlerSuccess } from "../../game/mgr/ApiHandler";
import { ActivityCmd } from "../../game/net/cmd/CmdData";
import { WindowPackMessage, WindowRecordRequest } from "../../game/net/protocol/Activity";
import { LongValue } from "../../game/net/protocol/ExternalMessage";
import MsgMgr from "../../lib/event/MsgMgr";
import { ActivityModule } from "../activity/ActivityModule";
import { HdTiaoJianLiBaoModule } from "./HdTiaoJianLiBaoModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class HdTiaoJianLiBaoApi {
  // 接口方法，数据模块在这里改好，然后通知上层，下面这个是个例子
  // public improveSkill(friendId: number, rank: number, consumeitem: boolean, success?: ApiHandlerSuccess) {
  //   let data: FriendCitySkillRequest = {
  //     friendId: friendId,
  //     rank: rank,
  //     consumeItem: consumeitem,
  //   };
  //   log.log("apiImproveSkill", friendId, rank, consumeitem);
  //   ApiHandler.instance.request(
  //     FriendCitySkillMessage,
  //     FriendSubCmd.improveSkill,
  //     FriendCitySkillRequest.encode(data),
  //     (data: FriendCitySkillMessage) => {
  //       log.log(data);
  //       FriendModule.data.updateFriendCitySkill(friendId, rank, data);
  //       success && success(data);
  //     },
  //     (errorCode: any, msg: string, data: any) => {
  //       log.log(`${errorCode}`);
  //       log.log(data);
  //     }
  //   );
  // }

  /**
   * 弹窗
   * @param activityId
   * @param success
   */
  public windowPack(activityId: number, success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    ApiHandler.instance.request(
      WindowPackMessage,
      ActivityCmd.windowPack,
      LongValue.encode({ value: activityId }),
      (data: WindowPackMessage) => {
        HdTiaoJianLiBaoModule.data.windowPackMessage = data;
        success && success(data);
      },
      error
    );
  }
  /** 活动ID
   * activityId: number;
   * 弹窗类型
   * type: number;
   */
  public recordWindowUp(activityId: number, type: number, success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    ApiHandler.instance.request(
      WindowPackMessage,
      ActivityCmd.recordWindowUp,
      WindowRecordRequest.encode({ activityId: activityId, type: type }),
      (data: WindowPackMessage) => {
        HdTiaoJianLiBaoModule.data.windowPackMessage = data;

        MsgMgr.emit(MsgEnum.ON_ACTIVITY_TIAOJIAN_UPDATE, data);
        success && success(data);
      },
      error
    );
  }

  public testReset(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(WindowPackMessage, ActivityCmd.testResetWindows, null, (data: WindowPackMessage) => {
      success && success(data);
    });
  }
}
