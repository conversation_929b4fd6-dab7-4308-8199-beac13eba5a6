import { _decorator, <PERSON><PERSON>, Sprite } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { MailModule } from "../../../module/mail/MailModule";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { MailListAdapter } from "./adapter/MailListAdapter";
import { Label } from "cc";
import { BoolValue } from "../../net/protocol/ExternalMessage";
import { ReadResponse } from "../../net/protocol/Mail";
import { PublicRouteName } from "../../../module/player/PlayerConstant";
import { ConfirmMsg } from "../UICostConfirm";
import { TipsMgr } from "../../../../platform/src/TipsHelper";
import { MailAudioName } from "../../../module/mail/MailConfig";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { BadgeMgr, BadgeType } from "../../mgr/BadgeMgr";
import { AdapterView } from "../../../../platform/src/core/ui/adapter_view/AdapterView";
const { ccclass, property } = _decorator;

/**
 *
 * hopewsw
 * Sun Jul 28 2024 16:04:35 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/ui_mail/UIMailBox.ts
 *
 */
@ccclass("UIMailBox")
export class UIMailBox extends UINode {
  protected _openAct: boolean = true;
  private showType: number = 1;

  private _mailListAdapter: MailListAdapter;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_MAIL}?prefab/ui/UIMailBox`;
  }

  protected onRegEvent(): void {
    MsgMgr.on(MsgEnum.ON_MAIL_UPDATE, this.refresh, this);
  }

  protected onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_MAIL_UPDATE, this.refresh, this);
  }

  //=================================================
  public init(args: any): void {
    super.init(args);
  }

  protected onEvtShow(): void {
    super.onEvtShow();

    // 初始化时候加载
    MailModule.api.syncDailyMail();
    MailModule.api.syncSysMail();

    this._mailListAdapter = new MailListAdapter(this.getNode("node_mail"));
    this.getNode("list").getComponent(AdapterView).setAdapter(this._mailListAdapter);
    this.onSwitchTab(1);
    BadgeMgr.instance.setBadgeId(this.getNode("btn_tab_daily"), BadgeType.UIMain.btn_email.btn_tab_daily.id);
    BadgeMgr.instance.setBadgeId(this.getNode("btn_tab_system"), BadgeType.UIMain.btn_email.btn_tab_system.id);
  }

  private refresh() {
    if (this.showType == 1) {
      this._mailListAdapter.setDatas(MailModule.data.sysMailList);
      this.getNode("lbl_stat").getComponent(Label).string = `当前邮件：${MailModule.data.sysMailList.length}/50`;
    } else {
      this._mailListAdapter.setDatas(MailModule.data.dailyMailList);
      this.getNode("lbl_stat").getComponent(Label).string = `当前邮件：${MailModule.data.dailyMailList.length}/50`;
    }

    let hasRead = false;
    if (this.showType == 2) {
      for (let i = 0; i < MailModule.data.dailyMailList.length; i++) {
        const mailMsg = MailModule.data.dailyMailList[i];
        if (!mailMsg.read) {
          hasRead = true;
          break;
        }
      }
    } else {
      for (let i = 0; i < MailModule.data.sysMailList.length; i++) {
        const mailMsg = MailModule.data.sysMailList[i];
        if (!mailMsg.read) {
          hasRead = true;
          break;
        }
      }
    }

    this.getNode("btn_email_read").getComponent(Button).interactable = hasRead;
    this.getNode("btn_email_read").getComponent(Sprite).grayscale = !hasRead;
  }

  private onSwitchTab(idx: number) {
    this.showType = idx;
    this.getNode("btn_tab_system").getChildByName("node_check").active = idx == 1;
    this.getNode("btn_tab_system").getChildByName("node_uncheck").active = idx != 1;

    this.getNode("btn_tab_daily").getChildByName("node_check").active = idx == 2;
    this.getNode("btn_tab_daily").getChildByName("node_uncheck").active = idx != 2;

    this.refresh();
  }

  private on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  private on_click_btn_tab_system() {
    AudioMgr.instance.playEffect(MailAudioName.Effect.点击下方页签);
    this.onSwitchTab(1);
  }

  private on_click_btn_tab_daily() {
    AudioMgr.instance.playEffect(MailAudioName.Effect.点击下方页签);
    this.onSwitchTab(2);
  }

  private on_click_btn_email_delete() {
    TipsMgr.setEnableTouch(false, 3);

    AudioMgr.instance.playEffect(MailAudioName.Effect.点击删除已读);

    if (this.showType == 1) {
      if (MailModule.data.sysMailList.length == 0) {
        TipsMgr.showTip("暂无邮件");
        TipsMgr.setEnableTouch(true);
        return;
      }
    } else {
      if (MailModule.data.dailyMailList.length == 0) {
        TipsMgr.showTip("暂无邮件");
        TipsMgr.setEnableTouch(true);
        return;
      }
    }

    let msg: ConfirmMsg = {
      msg: "确认删除邮件？",
      itemList: [],
    };

    TipsMgr.setEnableTouch(true);
    UIMgr.instance.showDialog(PublicRouteName.UICostConfirm, msg, (resp) => {
      if (resp?.ok) {
        TipsMgr.setEnableTouch(false, 3);
        MailModule.api.deleteAll(this.showType == 2, (resp: BoolValue) => {
          TipsMgr.setEnableTouch(true);
          this.getNode("btn_email_delete").getComponent(Button).interactable = false;
          this.getNode("btn_email_delete").getComponent(Sprite).grayscale = true;
          TipsMgr.showTip("删除邮件成功");
        });
      }
    });
  }

  private on_click_btn_email_read() {
    AudioMgr.instance.playEffect(MailAudioName.Effect.点击一键领取);
    this.getNode("btn_email_read").getComponent(Button).interactable = false;
    this.getNode("btn_email_read").getComponent(Sprite).grayscale = true;

    let hasRead = false;
    if (this.showType == 2) {
      for (let i = 0; i < MailModule.data.dailyMailList.length; i++) {
        const mailMsg = MailModule.data.dailyMailList[i];
        if (!mailMsg.read) {
          hasRead = true;
          break;
        }
      }
    } else {
      for (let i = 0; i < MailModule.data.sysMailList.length; i++) {
        const mailMsg = MailModule.data.sysMailList[i];
        if (!mailMsg.read) {
          hasRead = true;
          break;
        }
      }
    }

    if (!hasRead) {
      TipsMgr.showTip("没有可领取的邮件");
      return;
    }

    MailModule.api.readAll(this.showType == 2, (resp: ReadResponse) => {
      if (!resp.idList.length) {
        TipsMgr.showTip("没有可领取的邮件");
      }

      if (resp.rewardList.length) {
        MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: resp.rewardList });
      }
    });
  }
}
