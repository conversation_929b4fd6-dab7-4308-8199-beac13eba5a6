import GameObject from "./GameObject";
import { Section } from "./Section";

export default class NotifySection extends Section {
    public dict:Map<string, Array<any>> = new Map<string, Array<any>>();
    public static sectionName():string {
        return "NotifySection";
    }
    public onInit(go:GameObject, args) {
        super.onInit(go, args);
        this.ready();
    }
    public onNotify(name:string, func) {
        if (!this.dict.has(name)) {
            this.dict.set(name, new Array<any>());
        }
        this.dict.get(name).push(func);
    }

    public offNotify(name:string, func) {
        if (this.dict.has(name)) {
            let index = this.dict.get(name).indexOf(func);
            this.dict.get(name).splice(index, 1);
        }
    }

    public notify(name:string, args = null) {
        let funcArray = this.dict.get(name);
        if (funcArray == null) { return; }
        //if (this.getSub().getState() == GO_STATE.PAUSE) { return; }
        for (let i = 0; i < funcArray.length; i++) {
            funcArray[i](args);
        }
    }

    onRemove() {
        this.clear();
    }

    public clear() {
        this.dict.clear();
    }
}
