import { JsonMgr, Shop_Type_Enum } from "../../game/mgr/JsonMgr";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export const ClubAudioName = {
  Effect: {
    点击下方战盟页签: 1521,
    点击战盟首领图标: 1522,
    点击战盟任务图标: 1523,
    点击战盟商店图标: 1524,
    点击上方捐献日志管理按钮: 1525,
    点击左下方成员排行按钮: 1526,
    点击战盟旗帜图标: 1527,
    战盟首领点击进入战斗按钮: 1528,
    战盟首领点击宝箱领取奖励: 1529,
    战盟首领领取击杀奖励: 1530,
    战盟任务点击前往按钮: 1531,
    战盟任务点击宝箱领取奖励: 1532,
    战盟任务点击领取奖励: 1533,
    战盟商店点击兑换: 1534,
    管理点击盟主让位成员任免退盟按钮: 1535,
  },
  Sound: {
    战盟主界面音乐: 503,
  },
};

export class UnionConfig {
  id: number;
  exp: number;
  num: number;
  cost: number[][];
  decrease: number;
}
// c_unionTask
export class UnionTaskConfig {
  id: number;
  taskId: number;
  finish: number;
  rewardList: number[][];
  taskReward: number[][];
}
//c_unionDonate
export class UnionDonateConfig {
  id: number;
  name: string;
  costList: number[];
  max: number;
  cold: number;
  rewardList: number[][];
}
//c_shop
// id:number	itemsList:number[][]			type:number	unlockType:number	unlockNum:number	rmbId:number	cointype:number	coinPrice:number	priceAdd:number	maxtype:number	max:number	sort:number
export class ShopConfig {
  id: number;
  itemsList: number[][];
  type: number;
  unlockType: number;
  unlockNum: number;
  rmbId: number;
  cointype: number;
  coinPrice: number;
  priceAdd: number;
  maxtype: number;
  max: number;
  sort: number;
}
//c_unionBoss
export class BossConfig {
  id: number;
  name: string;
  unlock: number;
  monsterPower: number[][];
  rewardKill: number[][];
  rewardFloor: number[][];
  rewardChallenge: number[][];
  num: number[];
  round: number;
  free: number;
  buy: number;
  costList: number[];
  harm: number[];
}

export class ClubConfig {
  private _shopList: ShopConfig[] = [];

  public getClubCreateCost() {
    let union = JsonMgr.instance.jsonList.c_union[1];
    // log.log(union);
    return union.cost[0];
  }
  public getClubNameCost() {
    let union = JsonMgr.instance.jsonList.c_union[1];
    // log.log(union);
    return union.nameCost[0];
  }
  public getUnionMaxNumber(level: number): number {
    let union = JsonMgr.instance.jsonList.c_union[level];
    return union?.num ?? 0;
  }
  public getUnionExp(level: number): number {
    let union = JsonMgr.instance.jsonList.c_union[level];
    return union.exp;
  }
  public getTaskList(): UnionTaskConfig[] {
    let taskList = [];
    Object.values(JsonMgr.instance.jsonList.c_unionTask).forEach((val) => {
      taskList.push(val);
    });
    return taskList;
  }
  public getMaxActive() {
    let taskReward = this.getUnionTaskConfig(1).taskReward;
    if (
      Array.isArray(taskReward) &&
      taskReward.length > 0 &&
      Array.isArray(taskReward[taskReward.length - 1]) &&
      taskReward[taskReward.length - 1].length > 2
    ) {
      return taskReward[taskReward.length - 1][2] ?? 0;
    } else {
      return 0;
    }
  }
  public getActiveByLevel(level: number) {
    let taskReward = this.getUnionTaskConfig(1).taskReward;
    // log.log(taskReward);
    if (
      Array.isArray(taskReward) &&
      taskReward.length > 0 &&
      Array.isArray(taskReward[level]) &&
      taskReward[level].length > 2
    ) {
      return taskReward[level][2] ?? 0;
    } else {
      return this.getMaxActive();
    }
  }
  //根据活跃度等级获取奖励ID列表
  public getActiveRewardListByLevel(level: number) {
    let rewardList = []; //目前只有一个奖励,奇数位为ID 偶数位为数量
    let taskReward = this.getUnionTaskConfig(1).taskReward;
    rewardList.push(taskReward[level][0]); //id
    rewardList.push(taskReward[level][1]); //数量
    return rewardList;
  }
  //根据任务id获取奖励ID列表
  public getTaskRewardListByLevel(taskId: number) {
    let taskReward = this.getUnionTaskConfig(taskId).rewardList;
    return taskReward;
  }
  public getTaskConfig(id: number) {
    return JsonMgr.instance.jsonList.c_task[id];
  }

  public getUnionTaskConfig(id: number) {
    return JsonMgr.instance.jsonList.c_unionTask[id];
  }

  public getDonateConfig(id: number): UnionDonateConfig {
    return JsonMgr.instance.jsonList.c_unionDonate[id];
  }
  public getClubShopList(): ShopConfig[] {
    if (this._shopList.length == 0) {
      Object.values(JsonMgr.instance.jsonList.c_shop).forEach((val: ShopConfig) => {
        if (val.type == 11) {
          this._shopList.push(val);
        }
      });
    }
    return this._shopList;
  }

  public getClubBossList(): BossConfig[] {
    let bossList = [];
    Object.values(JsonMgr.instance.jsonList.c_unionBoss).forEach((val) => {
      bossList.push(val);
    });
    return bossList;
  }

  public getClubBossHarmList(): number[] {
    let harmList = [];
    Object.values(JsonMgr.instance.jsonList.c_unionBossReward).forEach((val: any) => {
      // log.log(val);
      harmList.push(val.harm);
    });
    return harmList;
  }
  public getClubBossAwardList(id: number): number[] {
    let itemList = [];
    let rewardList = JsonMgr.instance.jsonList.c_unionBossReward[id].rewardList;
    for (let i = 0; i < rewardList.length; i++) {
      itemList.push(rewardList[i][0]);
      itemList.push(rewardList[i][1]);
    }
    return itemList;
  }
}
