import { ModelGuideId } from "../../game/GameDefine";
import { <PERSON><PERSON><PERSON>and<PERSON> } from "../../game/mgr/ApiHandler";
import { LuckDrawActionSubCmd } from "../../game/net/cmd/CmdData";
import { IntValue } from "../../game/net/protocol/ExternalMessage";
import { LuckDrawResponse, LuckTrainResponse } from "../../game/net/protocol/LuckDraw";
import { LuckDrawModule } from "./LuckDrawModule";

export class LuckDrawApi {
  public getDraw(success?) {
    ApiHandler.instance.request(LuckTrainResponse, LuckDrawActionSubCmd.getDraw, null, (data: LuckTrainResponse) => {
      LuckDrawModule.data.message = data;
      success && success(data);
    });
  }

  public oneDraw(success?) {
    ApiHandler.instance.request(LuckDrawResponse, LuckDrawActionSubCmd.oneDraw, null, (data: LuckDrawResponse) => {
      LuckDrawModule.data.train = data.luckTrain;
      LuckDrawModule.data.recordList = data.recordList;
      success && success(data);
    });
  }

  public tenDraw(success?) {
    ApiHandler.instance.request(LuckDrawResponse, LuckDrawActionSubCmd.tenDraw, null, (data: LuckDrawResponse) => {
      LuckDrawModule.data.train = data.luckTrain;
      LuckDrawModule.data.recordList = data.recordList;
      success && success(data);
    });
  }

  public video(success?) {
    ApiHandler.instance.request(LuckDrawResponse, LuckDrawActionSubCmd.video, null, (data: LuckTrainResponse) => {
      LuckDrawModule.data.train = data.train;
      success && success(data);
    });
  }

  public updateGuideId() {
    let data: IntValue = {
      value: ModelGuideId.玲珑夺宝,
    };
    ApiHandler.instance.request(
      IntValue,
      LuckDrawActionSubCmd.updateGuideId,
      IntValue.encode(data),
      (data: IntValue) => {
        LuckDrawModule.data.message.train.guideId = data.value;
      }
    );
  }
}
