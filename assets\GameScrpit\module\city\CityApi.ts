import MsgEnum from "../../game/event/MsgEnum";
import { ApiHandler, ApiHandlerFail, ApiHandlerSuccess } from "../../game/mgr/ApiHandler";
import { CitySubCmd, EnergyFactorySubCmd } from "../../game/net/cmd/CmdData";
import {
  CityAchieveResponse,
  CityAggregateMessage,
  CityChapterResponse,
  CityCreateResponse,
  CityHireResponse,
  CityMessage,
  DecorationManualReward,
  EnergyFactoryMessage,
} from "../../game/net/protocol/City";
import { RewardMessage } from "../../game/net/protocol/Comm";
import { IntValue, LongValue, LongValueList, StringValue } from "../../game/net/protocol/ExternalMessage";
import MsgMgr from "../../lib/event/MsgMgr";
import { CityModule } from "./CityModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export default class CityApi {
  /**
   * 指定一个城池增加一名雇员
   * @param id cityId
   * @param success
   */
  public hireOneWorker(id: number, success?: ApiHandlerSuccess) {
    let data: LongValue = {
      value: id,
    };
    ApiHandler.instance.requestSync(
      CityHireResponse,
      CitySubCmd.hireOneWorker,
      LongValue.encode(data),
      (data: CityHireResponse) => {
        // 人数更新
        CityModule.data.updateEnergyHire(id, data.energyHire);
        // 更新宝箱数量
        CityModule.data.updateBoxCount(id, data.totalBoxCount);

        // CityModule.data.updateBoxCount(id, CityModule.data.cityAggregateMessage.boxTotalCount + 1);

        success && success(data);
      }
    );
  }

  /**
   * 指定一个城池增加十名雇员
   * @param id cityId
   * @param success
   */
  public hireTenWorker(id: number, success?: ApiHandlerSuccess) {
    let data: LongValue = {
      value: id,
    };
    ApiHandler.instance.requestSync(
      CityMessage,
      CitySubCmd.hireTenWorker,
      LongValue.encode(data),
      (data: CityMessage) => {
        CityModule.data.setCityData(data);
        success && success(data);
      }
    );
  }

  /**
   * 创建城池
   * @param id cityId
   * @param success
   */
  public createCity(id: number, success?: ApiHandlerSuccess) {
    let data: LongValue = {
      value: id,
    };
    ApiHandler.instance.requestSync(
      CityCreateResponse,
      CitySubCmd.create,
      LongValue.encode(data),
      (data: CityCreateResponse) => {
        CityModule.data.setCityData(data.cityMessage);
        MsgMgr.emit(MsgEnum.ON_CITY_LEVEL_UP);
        success && success(data);
      }
    );
  }

  /**
   * 升级城池
   * @param id cityId
   * @param success
   */
  public upgradeCity(id: number, success?: ApiHandlerSuccess) {
    let data: LongValue = {
      value: id,
    };
    ApiHandler.instance.requestSync(CityMessage, CitySubCmd.upgrade, LongValue.encode(data), (data: CityMessage) => {
      CityModule.data.setCityData(data);
      MsgMgr.emit(MsgEnum.ON_CITY_LEVEL_UP);
      success && success(data);
    });
  }

  /**
   * 汇总其他的统计信息返回给前端(奖励，迷雾。。)
   * @param success
   */
  public aggregateInfo(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(CityAggregateMessage, CitySubCmd.aggregateInfo, null, (data: CityAggregateMessage) => {
      CityModule.data.cityAggregateMessage = data;
      log.log("汇总其他的统计信息返回给前端", data);
      success && success(data);
    });
  }
  /**
   * 获取城池绑定的通关奖励
   * @param id cityId
   * @param success
   */
  public taskChapterReward(id: number, success?: ApiHandlerSuccess) {
    let data: LongValue = {
      value: id,
    };
    ApiHandler.instance.requestSync(
      CityChapterResponse,
      CitySubCmd.taskChapterReward,
      LongValue.encode(data),
      (data: CityChapterResponse) => {
        CityModule.data.cityAggregateMessage.rewardCityList = data.rewardCityList;
        success && success(data.resAddList);
      }
    );
  }
  /**
   *
   */
  public achievementReward(success?: ApiHandlerSuccess) {
    ApiHandler.instance.requestSync(
      CityAchieveResponse,
      CitySubCmd.achievementReward,
      null,
      (data: CityAchieveResponse) => {
        CityModule.data.cityAggregateMessage.nextCityLevelRewardIndex = data.nextCityLevelRewardIndex;
        success && success(data.resAddList);
      }
    );
  }

  /**
   * 记录城池已打开遮盖迷雾
   * @param id cityId
   * @param success
   */
  public openFog(id: number, success?: ApiHandlerSuccess) {
    let data: LongValue = {
      value: id,
    };
    ApiHandler.instance.request(LongValueList, CitySubCmd.openFog, LongValue.encode(data), (data: LongValueList) => {
      CityModule.data.cityAggregateMessage.openFogCityList = data.values;
      success && success(data);
    });
  }

  /**==================== EnergyFactoryAction  ==================== */
  public getEnergyFactory(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(
      EnergyFactoryMessage,
      EnergyFactorySubCmd.getEnergyFactory,
      null,
      (data: EnergyFactoryMessage) => {
        CityModule.data.energyFactoryMsg = data;
        MsgMgr.emit(MsgEnum.ON_ENERGYFACTORY_UPDATE);

        success && success(data);
      }
    );
  }

  public upgradeEnergyFactory(success?: ApiHandlerSuccess) {
    ApiHandler.instance.requestSync(
      EnergyFactoryMessage,
      EnergyFactorySubCmd.upgradeEnergyFactory,
      null,
      (data: EnergyFactoryMessage) => {
        CityModule.data.energyFactoryMsg = data;
        success && success(data);
        MsgMgr.emit(MsgEnum.ON_ENERGYFACTORY_UPDATE);
      }
    );
  }

  public takeAutoReward(success?: ApiHandlerSuccess) {
    ApiHandler.instance.requestSync(RewardMessage, EnergyFactorySubCmd.takeAutoReward, null, (data: RewardMessage) => {
      CityModule.data.energyFactoryMsg.autoTake = true;
      success && success(data);
      MsgMgr.emit(MsgEnum.ON_ENERGYFACTORY_UPDATE);
    });
  }

  // ==================重写========================

  /**获取已有的所有城池的信息 */
  public getAllCity(success?: ApiHandlerSuccess) {
    ApiHandler.instance.list(CityMessage, CitySubCmd.getAllCity, null, (data: CityMessage[]) => {
      CityModule.data.cityMessageList = data;
      MsgMgr.emit(MsgEnum.ON_CITY_UPDATE);
      success && success(data);
    });
  }

  //领取奖励
  public getTrimAward(id: number, success?: ApiHandlerSuccess) {
    let data: IntValue = {
      value: id,
    };

    ApiHandler.instance.requestSync(
      RewardMessage,
      CitySubCmd.getTrimAward,
      IntValue.encode(data),
      (data: RewardMessage) => {
        CityModule.data.addCityTrimAward(id);
        success && success(data);
      }
    );
  }

  /**建造额外的城池，如弟子宫和福地createOtherCity */
  public createOtherCity(id: number, success?: ApiHandlerSuccess) {
    let data: LongValue = {
      value: id,
    };
    ApiHandler.instance.requestSync(
      LongValue,
      CitySubCmd.createOtherCity,
      LongValue.encode(data),
      (data: LongValue) => {
        CityModule.data.cityAggregateMessage.otherCreateCityList.push(data.value);
        success && success(data);
      }
    );
  }

  public levelUpSmallHomeBuild(id: number, success?: ApiHandlerSuccess) {
    let data: LongValue = {
      value: id,
    };
    ApiHandler.instance.requestSync(
      LongValue,
      CitySubCmd.levelUpSmallHomeBuild,
      LongValue.encode(data),
      (data: LongValue) => {
        log.log("升级或者解锁后，建筑" + id + "的等级" + data.value);
        CityModule.data.cityAggregateMessage.smallHomeLevelMap[id] = data.value;
        log.log("三家小家等级数据结构====", CityModule.data.cityAggregateMessage.smallHomeLevelMap);
        MsgMgr.emit(MsgEnum.ON_SANJIE_XIAJIA_BG_UPDATE);
        success && success(data);
      }
    );
  }

  public takeSmallHomeBuildLvReward(id: number, success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    let data: LongValue = {
      value: id,
    };
    ApiHandler.instance.requestSync(
      RewardMessage,
      CitySubCmd.takeSmallHomeBuildLvReward,
      LongValue.encode(data),
      (data: RewardMessage) => {
        CityModule.data.cityAggregateMessage.smallHomeRewardList.push(id);
        success && success(data);
      },
      (errorCode: any, msg: string[], data: any) => {
        log.error(`${errorCode}`);
        log.error(data);
        log.log("我的家具等级====", CityModule.data.cityAggregateMessage.smallHomeLevelMap);
        log.log("我领取过的等级奖励====", CityModule.data.cityAggregateMessage.smallHomeRewardList);
        log.log("我要领取的====", id);
        error && error(errorCode, msg, data);
        return true;
      }
    );
  }

  public takeAllCityLevelReward(lv: number, success?: ApiHandlerSuccess) {
    let data: LongValue = {
      value: lv,
    };
    ApiHandler.instance.requestSync(
      RewardMessage,
      CitySubCmd.takeAllCityLevelReward,
      LongValue.encode(data),
      (data: RewardMessage) => {
        CityModule.data.cityAggregateMessage.smallHomeAllLevelReward.push(lv);
        success && success(data);
      }
    );
  }

  public getOneBoxReward(success?: ApiHandlerSuccess) {
    ApiHandler.instance.requestSync(RewardMessage, CitySubCmd.getOneBoxReward, null, (data: RewardMessage) => {
      CityModule.data.updateBoxCount(-1, CityModule.data.cityAggregateMessage.boxTotalCount - 1);
      success && success(data);
    });
  }

  public getAllBoxReward(success?: ApiHandlerSuccess) {
    ApiHandler.instance.requestSync(RewardMessage, CitySubCmd.getAllBoxReward, null, (data: RewardMessage) => {
      CityModule.data.updateBoxCount(-1, 0);
      success && success(data);
    });
  }

  public lvBoxLevel(success?: ApiHandlerSuccess) {
    ApiHandler.instance.requestSync(IntValue, CitySubCmd.lvBoxLevel, null, (data: IntValue) => {
      CityModule.data.cityAggregateMessage.boxLevel = data.value;
      MsgMgr.emit(MsgEnum.ON_CITY_HAO_ZHAO_UPDATE);
      success && success(data);
    });
  }

  public activeDecoration(id: number, success?: ApiHandlerSuccess) {
    let data: LongValue = {
      value: id,
    };
    ApiHandler.instance.requestSync(
      DecorationManualReward,
      CitySubCmd.activeDecoration,
      LongValue.encode(data),
      (data: DecorationManualReward) => {
        CityModule.data.cityAggregateMessage.decorationIdList = data.decorationIdList;
        CityModule.data.cityAggregateMessage.activeLockIdList =
          CityModule.data.cityAggregateMessage.activeLockIdList.filter((dId) => {
            return dId != id;
          });
        MsgMgr.emit(MsgEnum.ON_CITY_HAO_ZHAO_UPDATE);
        success && success(data);
      }
    );
  }
}
