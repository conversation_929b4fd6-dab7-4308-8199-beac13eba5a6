import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../game/mgr/ApiHandler";
import { FriendSubCmd } from "../../game/net/cmd/CmdData";
import { LongValue } from "../../game/net/protocol/ExternalMessage";
import {
  FriendKarmaUpdateMessage,
  FriendSubLabelMessage,
  FriendUnlockCitySkillResponse,
  FriendVitalityMessage,
} from "../../game/net/protocol/Friend";
import { FriendModule } from "./FriendModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class FriendSubscriber {
  private onVitality(data: FriendVitalityMessage) {
    log.log("FriendService.onVitality", data);
    FriendModule.data.vitalityMessage = data;

    //更新数据后再向页面发起刷新广播通知
  }
  private onKarma(data: FriendKarmaUpdateMessage) {
    log.log("FriendService.onKarma", data);
    // FriendModule.data.vitalityMessage = data;

    //更新数据后再向页面发起刷新广播通知
  }
  private onObtainFriend(data: FriendSubLabelMessage) {
    log.log("FriendService.onSubLabel", data);
    // FriendModule.data.vitalityMessage = data;
    FriendModule.api.getOneFriend(data.friendId);

    //更新数据后再向页面发起刷新广播通知
  }
  public onFriendProcess(data: LongValue) {
    log.log("FriendService.onFriendProcess", data);
  }
  public onUnlockCitySkill(data: FriendUnlockCitySkillResponse) {
    log.log("FriendService.onUnlockCitySkill", data);
    let friend = FriendModule.data.getFriendMessage(data.friendId);
    for (let i = 0; i < data.citySkillList.length; i++) {
      friend.citySkillList.push(data.citySkillList[i]);
    }
    FriendModule.data.setFriendMessage(data.friendId, friend);
  }
  public register() {
    //订阅服务器消息
    ApiHandler.instance.subscribe(FriendVitalityMessage, FriendSubCmd.notifyVitality, this.onVitality.bind(this));
    ApiHandler.instance.subscribe(FriendKarmaUpdateMessage, FriendSubCmd.notifyKarma, this.onKarma);
    ApiHandler.instance.subscribe(FriendSubLabelMessage, FriendSubCmd.notifyObtainFriend, this.onObtainFriend);
    ApiHandler.instance.subscribe(LongValue, FriendSubCmd.notifyFriendProcess, this.onFriendProcess);
    ApiHandler.instance.subscribe(FriendUnlockCitySkillResponse, FriendSubCmd.notifySkillAdd, this.onUnlockCitySkill);
  }

  public unRegister() {
    //取消订阅服务器消息
    ApiHandler.instance.unSubscribe(FriendSubCmd.notifyVitality, this.onVitality.bind(this));
    ApiHandler.instance.unSubscribe(FriendSubCmd.notifyKarma, this.onKarma);
    ApiHandler.instance.unSubscribe(FriendSubCmd.notifyObtainFriend, this.onObtainFriend);
    ApiHandler.instance.unSubscribe(FriendSubCmd.notifyFriendProcess, this.onFriendProcess);
    ApiHandler.instance.unSubscribe(FriendSubCmd.notifySkillAdd, this.onUnlockCitySkill);
  }
}
