{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "ebd609cc-db5c-4af1-a2ff-3c3724356873", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "ebd609cc-db5c-4af1-a2ff-3c3724356873@6c48a", "displayName": "bg_shangdian<PERSON>ang", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "ebd609cc-db5c-4af1-a2ff-3c3724356873", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "ebd609cc-db5c-4af1-a2ff-3c3724356873@f9941", "displayName": "bg_shangdian<PERSON>ang", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 112, "height": 775, "rawWidth": 112, "rawHeight": 775, "borderTop": 0, "borderBottom": 0, "borderLeft": 22, "borderRight": 22, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-56, -387.5, 0, 56, -387.5, 0, -56, 387.5, 0, 56, 387.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 775, 112, 775, 0, 0, 112, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-56, -387.5, 0], "maxPos": [56, 387.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "ebd609cc-db5c-4af1-a2ff-3c3724356873@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "ebd609cc-db5c-4af1-a2ff-3c3724356873@6c48a"}}