import { Button, Node } from "cc";

/**
 * 回调事件列表
 */
export interface LoadResCallBackMap {
  /** 成功回调 */
  cbSuccess?: Function;

  /** 失败回调 */
  cbFail?: Function;

  /** 下载资源进度回调, num%比进度, loadStep: '下载bundle'/'加载res' */
  cbProgress?: (num: number, loadStep: number) => void;
}

/**
 * UI帮助类
 */
export class NodeHelper {
  /**
   * 把节点树转为列表
   *
   * @param node 要列表化的节点
   * @return 返回节点map
   */
  public static treeToMap(node: Node): Map<string, Node> {
    let map = new Map();
    for (let i = 0; i < node.children.length; i++) {
      let child = node.children[i];
      let name = child.name;
      if (name == "name" || name == "view" || name == "node") {
        name = "default_" + name;
      }
      map.set(name, child);
      this.treeToMap(child);
    }
    return map;
  }

  /**
   * 自动绑定事件
   *
   */
  public static autoBind(nodeMap: Map<string, Node>, self: any) {
    nodeMap.forEach((val, key) => {
      let button = val.getComponent(Button);
      if (button != null) {
        //button.zoomScale = 1.2;
        this.onRegisterClick(button, self);
      }
    });
  }

  //注册按钮监听
  private static onRegisterClick(button: Button, self) {
    if (self["on_click_" + button.node.name] == null) {
      return;
    }
    button.node.on(
      "click",
      function (evt) {
        self["on_click_" + button.node.name] && self["on_click_" + button.node.name](evt);
      },
      self
    );
    button.node.on(
      Node.EventType.TOUCH_START,
      function (evt) {
        self["on_click_" + button.node.name + "_start"] && self["on_click_" + button.node.name + "_start"](evt);
      },
      self
    );
    button.node.on(
      Node.EventType.TOUCH_CANCEL,
      function (evt) {
        self["on_click_" + button.node.name + "_cancel"] && self["on_click_" + button.node.name + "_cancel"](evt);
      },
      self
    );
    button.node.on(
      Node.EventType.TOUCH_MOVE,
      function (evt) {
        self.on_click_common_move(evt);
        self["on_click_" + button.node.name + "_move"] && self["on_click_" + button.node.name + "_move"](evt);
      },
      self
    );
  }

  /**
   * 资源加载方法
   * @param bundleName bundle名称
   * @param resPath 资源相对路径
   * @param type 资源类型
   * @param  callback 加载完成回调
   */
  public static loadRes(bundleName: string, resPath: string, type: any, callback: LoadResCallBackMap) {}

  /**
   * 释放资源
   * @param bundleName 资源名称
   * @param resPath 资源相对路径
   */
  public static releaseRes(bundleName: string, resPath: string) {}
}
