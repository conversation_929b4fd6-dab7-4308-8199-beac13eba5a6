{"skeleton": {"hash": "XTH1kr/6Vi9zINaWgBCD9niIHik", "spine": "3.8.75", "x": -311.74, "y": -651.08, "width": 690.13, "height": 1221.91, "images": "./images/", "audio": "D:/仙友spine/蝎尾针"}, "bones": [{"name": "root", "scaleX": 0.82, "scaleY": 0.82}, {"name": "bone", "parent": "root", "length": 273.08, "rotation": -89.1, "x": 519.86, "y": 98.19}, {"name": "st", "parent": "bone", "length": 437.15, "rotation": 1.57, "x": 172.94, "y": -638.97}, {"name": "st2", "parent": "st", "length": 235.53, "rotation": 23.01, "x": 482.72, "y": 7.9}, {"name": "bone2", "parent": "bone", "length": 77.61, "rotation": 172.3, "x": 128.14, "y": -631.67}, {"name": "bone3", "parent": "bone2", "x": 171.57, "y": -23.68}, {"name": "bone4", "parent": "bone3", "length": 44.82, "rotation": 13.14, "x": 112.53, "y": -89.82}, {"name": "t1", "parent": "bone4", "length": 54.55, "rotation": -40.44, "x": 56.5, "y": 2.69}, {"name": "t2", "parent": "t1", "x": 1.99, "y": 75.51}, {"name": "t4", "parent": "t1", "length": 59.41, "rotation": -107.61, "x": 206.63, "y": 68.84}, {"name": "t7", "parent": "t1", "length": 37.07, "rotation": -138.26, "x": 69.42, "y": -15.07}, {"name": "t6", "parent": "t1", "length": 120.45, "rotation": -126.12, "x": 55.41, "y": -19.14}, {"name": "t8", "parent": "t6", "length": 105.33, "rotation": 16.32, "x": 127.3, "y": -0.62}, {"name": "t9", "parent": "t8", "length": 98.51, "rotation": 36.45, "x": 108.46, "y": 4.3}, {"name": "t10", "parent": "t9", "length": 74.81, "rotation": -27.86, "x": 110.31, "y": 1.85}, {"name": "t11", "parent": "t10", "length": 88.06, "rotation": -44.68, "x": 81.53, "y": -6.65}, {"name": "t12", "parent": "t11", "length": 93.85, "rotation": -49.09, "x": 93.41, "y": -5.91}, {"name": "t13", "parent": "t12", "length": 69.83, "rotation": -38, "x": 101.77, "y": -5.3}, {"name": "t14", "parent": "t13", "length": 52.33, "rotation": -66.06, "x": 78.97, "y": -6.38}, {"name": "s3", "parent": "bone2", "length": 236.07, "rotation": 176.82, "x": 238.99, "y": -195.38}, {"name": "s4", "parent": "s3", "length": 199.5, "rotation": -127.85, "x": 235.8, "y": -19.88}, {"name": "s5", "parent": "s4", "length": 138.38, "rotation": 162.83, "x": 64.58, "y": 48.32}, {"name": "s6", "parent": "s5", "length": 124.31, "rotation": 27.09, "x": 180.58, "y": 14.66}, {"name": "s7", "parent": "s6", "length": 114.36, "rotation": 24.56, "x": 148.21, "y": 3.08}, {"name": "s8", "parent": "s7", "length": 90.91, "rotation": -23.64, "x": 143.37, "y": -3.78}, {"name": "s10", "parent": "s4", "length": 42.16, "rotation": 26.73, "x": 214.98, "y": 2.86}, {"name": "s9", "parent": "s10", "length": 25.47, "rotation": -3.72, "x": 47.08, "y": 1.42}, {"name": "s11", "parent": "s9", "length": 16.4, "rotation": 2.58, "x": 25.61, "y": -0.01}, {"name": "s1", "parent": "bone2", "length": 226.92, "rotation": 154.32, "x": 243.26, "y": 15.44}, {"name": "s12", "parent": "s1", "length": 163.61, "rotation": 175.81, "x": 231.27, "y": 6.97}, {"name": "s14", "parent": "s12", "length": 59.27, "rotation": -36.89, "x": 172.4, "y": -0.54}, {"name": "s13", "parent": "s14", "length": 20.85, "rotation": -13.74, "x": 65.74, "y": -0.91}, {"name": "s15", "parent": "s13", "length": 35.45, "rotation": -11.44, "x": 25.59, "y": -3.41}, {"name": "s2", "parent": "s1", "length": 103.59, "rotation": 24.01, "x": 248.44, "y": 1.41}, {"name": "s16", "parent": "s2", "length": 109.19, "rotation": -5.98, "x": 122.24, "y": 0.57}, {"name": "s17", "parent": "s16", "length": 101.21, "rotation": -7.69, "x": 125.05, "y": -0.41}, {"name": "s18", "parent": "s17", "length": 104.52, "rotation": 14.35, "x": 116.38, "y": 0.29}, {"name": "t5", "parent": "t1", "length": 17.75, "rotation": -122.59, "x": -41.8, "y": 81.79}, {"name": "t15", "parent": "t1", "length": 32.27, "rotation": 121.79, "x": 78.62, "y": 148.71}, {"name": "t16", "parent": "t15", "length": 23.61, "rotation": 71.68, "x": 34.78, "y": 2.18}, {"name": "t17", "parent": "t16", "length": 19.54, "rotation": -12.43, "x": 27.69, "y": 0.98}, {"name": "t18", "parent": "t17", "length": 18.17, "rotation": -23.03, "x": 22.56, "y": -0.83}, {"name": "t19", "parent": "t18", "length": 15.38, "rotation": 37.34, "x": 22.43, "y": 1.21}, {"name": "ps2", "parent": "bone2", "length": 20.53, "rotation": 139.95, "x": 264.62, "y": -71.05}, {"name": "ps3", "parent": "ps2", "length": 15.06, "rotation": 12.83, "x": 22.75, "y": -0.37}, {"name": "bone5", "parent": "bone", "length": 106.41, "rotation": 22.85, "x": 559.48, "y": -486.38}, {"name": "bone6", "parent": "bone5", "length": 92.43, "rotation": 5.25, "x": 143.5, "y": -0.36}, {"name": "bone7", "parent": "bone6", "length": 142.44, "rotation": 34.61, "x": 134.04, "y": 14.8}, {"name": "ps", "parent": "st", "length": 89.2, "rotation": -6.49, "x": -19.85, "y": -21.99}, {"name": "ps4", "parent": "ps", "length": 77, "rotation": 2.23, "x": 95.53, "y": -0.52}, {"name": "ps5", "parent": "ps4", "length": 73.85, "rotation": 15.74, "x": 86.64, "y": -0.66}, {"name": "ps6", "parent": "ps5", "length": 60.64, "rotation": 27.84, "x": 80.09, "y": 1.92}, {"name": "ps7", "parent": "ps6", "length": 47.18, "rotation": -18.36, "x": 68.5, "y": -0.58}, {"name": "ps8", "parent": "ps7", "length": 40.93, "rotation": -3.5, "x": 53.18, "y": 0.54}, {"name": "ps9", "parent": "ps8", "length": 39.27, "rotation": 39.11, "x": 48.65, "y": 4.36}, {"name": "ps10", "parent": "ps9", "length": 46.06, "rotation": 23.76, "x": 49.58, "y": -2.23}, {"name": "t3", "parent": "t1", "length": 132.71, "rotation": -80.65, "x": 131.32, "y": 160.68}], "slots": [{"name": "bg", "bone": "root", "attachment": "bg"}, {"name": "t6", "bone": "t6", "attachment": "t6"}, {"name": "t7", "bone": "t7", "attachment": "t7"}, {"name": "s1", "bone": "s1", "attachment": "s1"}, {"name": "st", "bone": "st", "attachment": "st"}, {"name": "ps", "bone": "ps", "attachment": "ps"}, {"name": "ps2", "bone": "ps2", "attachment": "ps2"}, {"name": "s2", "bone": "s12"}, {"name": "s4", "bone": "s14"}, {"name": "s5", "bone": "s13"}, {"name": "s3", "bone": "s3", "attachment": "s3"}, {"name": "s6", "bone": "s10"}, {"name": "s7", "bone": "s9"}, {"name": "t4", "bone": "t5", "attachment": "t4"}, {"name": "t1", "bone": "t1", "attachment": "t1"}, {"name": "t3", "bone": "t4", "attachment": "t3"}, {"name": "t2", "bone": "t15", "attachment": "t2"}, {"name": "rrrpng", "bone": "s12", "attachment": "rrrpng"}, {"name": "www", "bone": "s4", "attachment": "www"}], "skins": [{"name": "default", "attachments": {"t4": {"t4": {"type": "mesh", "uvs": [0.05919, 0, 0.35179, 0.28956, 0.64164, 0.57641, 1, 0.93105, 1, 0.93836, 0.79118, 0.90365, 0.37672, 0.83601, 0.05388, 0.78332, 0.05241, 0.5409, 0.04912, 0], "triangles": [1, 8, 9, 1, 9, 0, 8, 1, 2, 6, 7, 8, 2, 6, 8, 3, 5, 2, 6, 2, 5, 5, 3, 4], "vertices": [1, 37, -12.47, 1.72, 1, 1, 37, 2.98, 6.84, 1, 1, 37, 18.29, 11.92, 1, 1, 37, 37.22, 18.2, 1, 1, 37, 37.5, 18.08, 1, 1, 37, 33.1, 11.57, 1, 1, 37, 24.42, -1.39, 1, 1, 37, 17.66, -11.49, 1, 1, 37, 8.29, -7.51, 1, 1, 37, -12.62, 1.37, 1], "hull": 10, "edges": [0, 18, 6, 8, 8, 10, 14, 16, 16, 18, 0, 2, 16, 2, 10, 12, 12, 14, 2, 4, 4, 6, 12, 4], "width": 37, "height": 42}}, "t6": {"t6": {"type": "mesh", "uvs": [0.10213, 0.00162, 0.1405, 0.01088, 0.20151, 0.10713, 0.24962, 0.18302, 0.30679, 0.2732, 0.34539, 0.3289, 0.37496, 0.37158, 0.46779, 0.39051, 0.57639, 0.41265, 0.70248, 0.43836, 0.87247, 0.47302, 0.94047, 0.56744, 0.96971, 0.60806, 0.98849, 0.67748, 1, 0.72002, 1, 0.72187, 0.9834, 0.77212, 0.94994, 0.87339, 0.83655, 0.92572, 0.75121, 0.96511, 0.67199, 0.96493, 0.59145, 0.96475, 0.51943, 0.98168, 0.4415, 1, 0.37694, 1, 0.29434, 0.94824, 0.27394, 0.84353, 0.31769, 0.76051, 0.34226, 0.7493, 0.40814, 0.80406, 0.44527, 0.83492, 0.50639, 0.88572, 0.55848, 0.92901, 0.59132, 0.89625, 0.63613, 0.85156, 0.67841, 0.80939, 0.73243, 0.75551, 0.79039, 0.6977, 0.84328, 0.64495, 0.712, 0.60295, 0.57977, 0.56064, 0.46102, 0.52264, 0.34349, 0.48504, 0.21533, 0.44403, 0.17066, 0.38192, 0.11654, 0.30666, 0.05091, 0.21541, 0.02509, 0.17951, 0.01191, 0.138, 0, 0.10051, 0, 0.07674, 0.05365, 0.00038, 0.12869, 0.14217, 0.17667, 0.21443, 0.22293, 0.31102, 0.32145, 0.40436, 0.37713, 0.45343, 0.53134, 0.47607, 0.67196, 0.48663, 0.81589, 0.53516, 0.89212, 0.61642, 0.8947, 0.70485, 0.89041, 0.78812, 0.84415, 0.84636, 0.72935, 0.88842], "triangles": [31, 25, 30, 29, 30, 26, 29, 26, 27, 27, 28, 29, 26, 30, 25, 22, 23, 31, 23, 24, 31, 25, 31, 24, 22, 32, 21, 22, 31, 32, 32, 33, 21, 33, 20, 21, 18, 19, 64, 19, 20, 64, 64, 20, 33, 64, 33, 34, 18, 63, 17, 18, 64, 63, 34, 35, 64, 64, 35, 63, 63, 62, 17, 35, 36, 63, 63, 36, 62, 17, 62, 16, 36, 37, 62, 62, 61, 16, 62, 37, 61, 16, 61, 15, 61, 14, 15, 61, 13, 14, 37, 38, 61, 38, 60, 61, 61, 60, 13, 60, 12, 13, 60, 38, 59, 38, 39, 59, 60, 11, 12, 60, 59, 11, 39, 58, 59, 59, 10, 11, 59, 9, 10, 40, 58, 39, 41, 57, 40, 40, 57, 58, 58, 9, 59, 41, 56, 57, 57, 8, 58, 58, 8, 9, 56, 7, 57, 57, 7, 8, 42, 56, 41, 42, 43, 56, 43, 55, 56, 55, 6, 56, 56, 6, 7, 43, 44, 55, 44, 54, 55, 55, 5, 6, 55, 54, 5, 44, 45, 54, 54, 4, 5, 4, 54, 53, 54, 45, 53, 45, 46, 53, 53, 3, 4, 53, 46, 52, 46, 47, 52, 53, 52, 3, 52, 2, 3, 47, 48, 52, 50, 2, 52, 52, 48, 49, 2, 50, 1, 1, 50, 0, 0, 50, 51, 52, 49, 50], "vertices": [1, 11, -15.07, 18.34, 1, 1, 11, -6.46, 28.33, 1, 1, 11, 46.36, 30.14, 1, 1, 11, 88.01, 31.58, 1, 2, 11, 137.5, 33.28, 0.26989, 12, 19.31, 29.67, 0.73011, 2, 11, 168.42, 35.32, 0.00657, 12, 49.57, 22.94, 0.99343, 2, 12, 72.75, 17.79, 0.89464, 13, -20.71, 32.07, 0.10536, 2, 12, 98.1, 36.18, 0.09562, 13, 10.61, 31.8, 0.90438, 1, 13, 47.25, 31.48, 1, 2, 13, 89.8, 31.12, 0.75433, 14, -31.81, 16.28, 0.24567, 1, 14, 19.12, 42.65, 1, 2, 14, 68.71, 24.31, 1, 18, 84.05, -242.61, 0, 3, 14, 90.04, 16.42, 0.57355, 15, -10.17, 22.39, 0.42645, 18, 61.33, -241.63, 0, 2, 15, 25.23, 28.42, 1, 18, 27.02, -231.01, 0, 2, 15, 46.93, 32.11, 1, 18, 5.99, -224.51, 0, 2, 15, 47.87, 32.11, 1, 18, 5.15, -224.08, 0, 1, 15, 73.5, 26.78, 1, 3, 15, 125.15, 16.04, 0.15703, 16, 4.2, 38.36, 0.84297, 18, -56.53, -174.84, 0, 2, 16, 49.18, 34.69, 1, 18, -63.9, -130.31, 0, 1, 16, 83.04, 31.93, 1, 2, 16, 102.2, 15.21, 0.86618, 17, -12.29, 16.42, 0.13382, 2, 16, 121.68, -1.79, 0.00904, 17, 13.53, 15.02, 0.99096, 1, 17, 37.05, 22.47, 1, 2, 17, 62.51, 30.53, 0.97949, 18, -40.41, -0.06, 0.02051, 2, 17, 83.21, 29.48, 0.77508, 18, -31.05, 18.43, 0.22492, 2, 17, 108.35, 1.77, 0.02829, 18, 4.47, 30.16, 0.97171, 1, 18, 55.08, 11.88, 1, 1, 18, 86.51, -19.78, 1, 1, 18, 88.04, -29.4, 1, 2, 17, 68.13, -69.82, 0.03621, 18, 53.58, -35.65, 0.96379, 2, 17, 57.03, -53.49, 0.17685, 18, 34.15, -39.17, 0.82315, 2, 17, 38.75, -26.62, 0.7401, 18, 2.17, -44.97, 0.2599, 3, 16, 117.74, -22.5, 0.00096, 17, 23.17, -3.72, 0.99685, 18, -25.08, -49.92, 0.00219, 3, 16, 98.83, -28.22, 0.33181, 17, 11.8, -19.87, 0.66817, 18, -14.93, -66.87, 2e-05, 3, 16, 73.03, -36.03, 0.86463, 17, -3.73, -41.91, 0.13536, 18, -1.09, -90, 1e-05, 4, 15, 92.51, -71.12, 0.02568, 16, 48.69, -43.39, 0.96145, 17, -18.37, -62.69, 0.01287, 18, 11.96, -111.82, 0, 3, 15, 65.03, -53.78, 0.32016, 16, 17.59, -52.8, 0.67984, 18, 28.64, -139.7, 0, 3, 15, 35.55, -35.17, 0.83879, 16, -15.78, -62.89, 0.16121, 18, 46.54, -169.62, 0, 4, 13, 164.52, -55.83, 0.00261, 14, 74.88, -25.67, 0.18593, 15, 8.65, -18.2, 0.80825, 16, -46.23, -72.1, 0.00321, 4, 13, 117.89, -48.04, 0.30447, 14, 30.02, -40.57, 0.67188, 15, -12.78, -60.34, 0.02365, 18, 101.02, -169, 0, 3, 13, 70.93, -40.19, 0.97336, 14, -15.17, -55.58, 0.02664, 18, 139.44, -140.88, 0, 3, 12, 151.27, -5.28, 0.02422, 13, 28.75, -33.14, 0.97578, 18, 173.95, -115.62, 0, 3, 12, 113.55, -24.47, 0.8933, 13, -12.99, -26.16, 0.1067, 18, 208.1, -90.63, 0, 2, 12, 72.41, -45.38, 1, 18, 245.35, -63.37, 0, 2, 12, 38.37, -38.31, 1, 18, 280.09, -64.89, 0, 3, 11, 132.89, -29.97, 0.18344, 12, -2.88, -29.73, 0.81656, 18, 322.17, -66.73, 0, 3, 11, 81.96, -34.04, 0.99803, 12, -52.9, -19.33, 0.00197, 18, 373.21, -68.96, 0, 2, 11, 61.93, -35.64, 1, 18, 393.29, -69.83, 0, 2, 11, 40.58, -32.46, 1, 18, 414.09, -75.62, 0, 2, 11, 21.29, -29.58, 1, 18, 432.88, -80.85, 0, 2, 11, 9.89, -25.48, 1, 18, 443.69, -86.32, 0, 1, 11, -20.93, 3.91, 1, 1, 11, 55.27, 2.1, 1, 1, 11, 95.16, 4.12, 1, 2, 12, 19.04, -3.45, 1, 18, 304.76, -96.19, 0, 2, 12, 76.13, -5.94, 1, 18, 248.01, -102.91, 0, 2, 12, 106.88, -6.24, 1, 18, 217.61, -107.55, 0, 2, 13, 43.16, -3.71, 1, 18, 184.95, -146.49, 0, 2, 13, 87.83, 4.7, 0.96649, 14, -21.2, -7.99, 0.03351, 1, 14, 28.88, 7.45, 1, 3, 13, 175.11, -37.25, 0.00016, 14, 75.56, -4.29, 0.64128, 15, -5.91, -2.52, 0.35856, 2, 15, 39.19, -1.69, 0.99627, 16, -38.69, -38.21, 0.00373, 3, 15, 81.66, -3.07, 0.92027, 16, -9.84, -7.02, 0.07973, 18, -9.1, -177.44, 0, 2, 15, 111.36, -17.92, 0.00249, 16, 20.83, 5.7, 0.99751, 3, 16, 62.73, -2.22, 0.99787, 17, -32.66, -21.61, 0.00213, 18, -31.38, -108.21, 0], "hull": 52, "edges": [0, 102, 0, 2, 28, 30, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 98, 100, 100, 102, 94, 96, 96, 98, 96, 104, 2, 4, 104, 4, 92, 94, 92, 106, 4, 6, 106, 6, 90, 92, 90, 108, 6, 8, 108, 8, 86, 88, 88, 90, 88, 110, 110, 12, 86, 112, 12, 14, 112, 14, 82, 114, 14, 16, 114, 16, 80, 82, 80, 116, 16, 18, 18, 20, 116, 18, 76, 78, 78, 80, 78, 118, 118, 20, 76, 120, 20, 22, 22, 24, 120, 22, 74, 76, 74, 122, 24, 26, 26, 28, 122, 26, 72, 74, 72, 124, 30, 32, 32, 34, 124, 32, 70, 72, 70, 126, 126, 34, 68, 70, 68, 128, 34, 36, 36, 38, 128, 36, 64, 66, 66, 68, 38, 40, 40, 42, 66, 40, 62, 64, 42, 44, 44, 46, 60, 62, 56, 58, 58, 60, 82, 84, 84, 86, 8, 10, 10, 12], "width": 321, "height": 510}}, "t7": {"t7": {"type": "mesh", "uvs": [0.93938, 0.19998, 0.95684, 0.34162, 0.97229, 0.46695, 1, 0.69177, 1, 0.8356, 0.6439, 1, 0.41946, 1, 0.1753, 0.82741, 0, 0.70351, 0, 0.44059, 0.03634, 0.33923, 0.09918, 0.16394, 0.15744, 0.00141, 0.92032, 0.04533], "triangles": [12, 13, 0, 11, 12, 0, 11, 0, 1, 10, 11, 1, 10, 1, 2, 9, 10, 2, 8, 9, 2, 8, 2, 3, 7, 8, 3, 7, 3, 4, 5, 6, 7, 4, 5, 7], "vertices": [1, 10, 8.03, 18.68, 1, 1, 10, 15.7, 18.32, 1, 1, 10, 22.48, 18.01, 1, 1, 10, 34.66, 17.44, 1, 1, 10, 42.35, 16.4, 1, 1, 10, 49.35, 1.81, 1, 1, 10, 48.22, -6.64, 1, 1, 10, 37.75, -14.6, 1, 1, 10, 30.23, -20.31, 1, 1, 10, 16.16, -18.42, 1, 1, 10, 10.92, -16.33, 1, 1, 10, 1.85, -12.7, 1, 1, 10, -6.55, -9.34, 1, 1, 10, -0.34, 19.08, 1], "hull": 14, "edges": [6, 8, 8, 10, 10, 12, 16, 18, 24, 26, 22, 24, 0, 26, 22, 0, 18, 20, 20, 22, 0, 2, 20, 2, 2, 4, 4, 6, 18, 4, 16, 6, 12, 14, 14, 16, 14, 8], "width": 38, "height": 54}}, "bg": {"bg": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [380, -739.13, -371, -739.13, -371, 696.13, 380, 696.13], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 300, "height": 503}}, "ps2": {"ps2": {"type": "mesh", "uvs": [0.88951, 0, 0.99226, 0.0407, 0.72111, 0.56959, 0.1613, 0.96347, 0.03482, 0.83421, 0.28452, 0.33753], "triangles": [2, 3, 5, 3, 4, 5, 2, 0, 1, 2, 5, 0], "vertices": [1, 43, -4.21, -2.27, 1, 1, 43, -6.02, 1.66, 1, 2, 43, 16.16, 9.86, 0.94321, 44, -4.15, 11.44, 0.05679, 1, 44, 21.13, 2.15, 1, 1, 44, 19.6, -4.83, 1, 2, 43, 22.24, -8.56, 0.32723, 44, -2.32, -7.87, 0.67277], "hull": 6, "edges": [0, 10, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10], "width": 39, "height": 40}}, "www": {"www": {"type": "mesh", "uvs": [0.03657, 0.00681, 0.06211, 0.01416, 0.1002, 0.04664, 0.12402, 0.05878, 0.18081, 0.0751, 0.2569, 0.11904, 0.32705, 0.14897, 0.44108, 0.18188, 0.4736, 0.19094, 0.49556, 0.17421, 0.58019, 0.19576, 0.57457, 0.22157, 0.72229, 0.28731, 0.87646, 0.46471, 0.99516, 0.6374, 1, 0.6841, 1, 0.72825, 0.98109, 0.83944, 0.95176, 0.92909, 0.87604, 1, 0.86236, 1, 0.84496, 0.97929, 0.75879, 0.78141, 0.75745, 0.76412, 0.68964, 0.64344, 0.63937, 0.57875, 0.47374, 0.53819, 0.34077, 0.47677, 0.13745, 0.46529, 0.11976, 0.45857, 0.0579, 0.41789, 0.06543, 0.37089, 0.12429, 0.37122, 0.15741, 0.33279, 0.06645, 0.26292, 0.01799, 0.16488, 0.01856, 0.10272, 0.02448, 0.07803, 0.00609, 0.03597, 0.00596, 0.00709], "triangles": [18, 19, 21, 19, 20, 21, 21, 22, 18, 18, 22, 17, 16, 17, 23, 17, 22, 23, 16, 23, 15, 14, 23, 13, 23, 14, 15, 23, 24, 13, 24, 25, 13, 25, 12, 13, 33, 5, 6, 33, 34, 4, 33, 4, 5, 4, 34, 3, 34, 35, 3, 3, 35, 36, 2, 36, 37, 36, 2, 3, 37, 1, 2, 37, 0, 1, 37, 38, 0, 38, 39, 0, 28, 33, 27, 27, 33, 6, 27, 6, 7, 29, 32, 28, 28, 32, 33, 29, 30, 32, 32, 30, 31, 12, 25, 11, 27, 8, 26, 25, 26, 11, 11, 8, 9, 11, 26, 8, 27, 7, 8, 11, 9, 10], "vertices": [1, 27, 43.9, -16.79, 1, 1, 27, 39.71, -17.39, 1, 1, 27, 32.31, -15.5, 1, 1, 27, 28.12, -15.38, 1, 2, 26, 45.13, -15.88, 0.00059, 27, 18.79, -16.73, 0.99941, 3, 25, 77.44, -16.03, 0.00035, 26, 31.43, -15.44, 0.21492, 27, 5.12, -15.67, 0.78473, 3, 25, 65.4, -16.18, 0.04857, 26, 19.42, -16.38, 0.7758, 27, -6.92, -16.07, 0.17563, 2, 25, 46.61, -18.5, 0.63507, 26, 0.82, -19.91, 0.36493, 2, 25, 41.27, -19.2, 0.86232, 26, -4.46, -20.95, 0.13768, 2, 25, 38.82, -22.66, 0.94404, 26, -6.69, -24.56, 0.05596, 1, 25, 25.02, -24.75, 1, 1, 25, 24.56, -21.05, 1, 2, 25, -0.94, -21.03, 0.9186, 20, 223.6, -16.35, 0.0814, 2, 25, -33.04, -6.8, 0.00514, 20, 188.53, -18.08, 0.99486, 1, 20, 157.78, -16.01, 1, 1, 20, 152.41, -12.2, 1, 1, 20, 147.83, -8.05, 1, 1, 20, 138.34, 4.66, 1, 1, 20, 132.2, 16.58, 1, 1, 20, 133.03, 32.28, 1, 1, 20, 134.51, 33.91, 1, 1, 20, 138.53, 34.04, 1, 1, 20, 168.38, 25.73, 1, 1, 20, 170.32, 24.27, 1, 2, 25, -13.99, 27.38, 0.0861, 20, 190.17, 21.02, 0.9139, 3, 25, -3.18, 21.84, 0.48097, 26, -51.48, 17.11, 0.00052, 20, 202.32, 20.93, 0.51851, 3, 25, 23.74, 26.15, 0.83698, 26, -24.9, 23.16, 0.15681, 20, 224.43, 36.89, 0.00621, 3, 25, 46.81, 25.83, 0.19888, 26, -1.86, 24.34, 0.77362, 27, -26.34, 25.57, 0.0275, 2, 26, 28.53, 36.63, 0.62067, 27, 4.57, 36.47, 0.37933, 2, 26, 31.51, 36.97, 0.60766, 27, 7.56, 36.68, 0.39234, 2, 26, 42.94, 35.99, 0.58396, 27, 18.93, 35.18, 0.41604, 2, 26, 44.6, 29.51, 0.58164, 27, 20.3, 28.63, 0.41836, 2, 26, 35.98, 25.57, 0.56469, 27, 11.51, 25.09, 0.43531, 2, 26, 33.4, 18.45, 0.38642, 27, 8.62, 18.09, 0.61358, 2, 26, 50.8, 15.72, 0.00338, 27, 25.87, 14.58, 0.99662, 1, 27, 38.29, 4.83, 1, 1, 27, 41.5, -3.26, 1, 1, 27, 41.93, -6.82, 1, 1, 27, 46.9, -11.15, 1, 1, 27, 48.45, -14.88, 1], "hull": 40, "edges": [0, 78, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78], "width": 161, "height": 140}}, "s1": {"s1": {"type": "mesh", "uvs": [0.60447, 0.12873, 0.66652, 0.09812, 0.72861, 0.0675, 0.77523, 0.04451, 0.82815, 0.01842, 0.92987, 0, 0.93668, 0, 0.9395, 0.00675, 0.90482, 0.04981, 0.87157, 0.09107, 0.84532, 0.12365, 0.8179, 0.15768, 0.78677, 0.19633, 0.76625, 0.22179, 0.7424, 0.2514, 0.71019, 0.29138, 0.68322, 0.32485, 0.65953, 0.35426, 0.62725, 0.39432, 0.62577, 0.43573, 0.62424, 0.47885, 0.6228, 0.51922, 0.62124, 0.56297, 0.60802, 0.55974, 0.52721, 0.61911, 0.47154, 0.66001, 0.41598, 0.70083, 0.437, 0.71747, 0.46667, 0.74097, 0.50593, 0.77205, 0.53535, 0.79533, 0.56796, 0.82115, 0.59478, 0.84238, 0.63617, 0.87514, 0.57514, 0.85022, 0.52203, 0.82854, 0.46467, 0.80512, 0.41023, 0.78289, 0.34024, 0.75432, 0.27999, 0.72972, 0.29236, 0.76169, 0.30534, 0.79525, 0.32089, 0.83544, 0.33201, 0.86417, 0.34372, 0.89443, 0.35738, 0.92974, 0.39195, 0.94254, 0.45162, 0.96464, 0.47892, 0.97474, 0.47701, 1, 0.47216, 1, 0.41457, 0.99512, 0.37152, 0.98324, 0.32209, 0.96961, 0.23776, 0.94636, 0.20748, 0.93165, 0.17529, 0.91144, 0.12119, 0.87747, 0.06344, 0.84121, 0.03952, 0.79256, 0.01076, 0.73409, 0.0051, 0.70071, 0, 0.67061, 0, 0.63958, 0, 0.59223, 0.04343, 0.57401, 0.12838, 0.53185, 0.19707, 0.49776, 0.27098, 0.46108, 0.33849, 0.42757, 0.32321, 0.3723, 0.31165, 0.33047, 0.29976, 0.28747, 0.28512, 0.23451, 0.27364, 0.19299, 0.26324, 0.15539, 0.25569, 0.12806, 0.25339, 0.09769, 0.29704, 0.07654, 0.36457, 0.04381, 0.36434, 0.07097, 0.36417, 0.09146, 0.4515, 0.12686, 0.53755, 0.16173, 0.8498, 0.05231, 0.79593, 0.08798, 0.75727, 0.11567, 0.70691, 0.14806, 0.66709, 0.1822, 0.40356, 0.1348, 0.44572, 0.16155, 0.53942, 0.19253, 0.66357, 0.21037, 0.47149, 0.2221, 0.62141, 0.24041, 0.39368, 0.25054, 0.57054, 0.27823, 0.42531, 0.29325, 0.53423, 0.31296, 0.42297, 0.33172, 0.52369, 0.34157, 0.46396, 0.37865, 0.48036, 0.42965, 0.43468, 0.46898, 0.55766, 0.47602, 0.389, 0.50982, 0.55063, 0.51921, 0.31872, 0.55513, 0.52721, 0.5617, 0.25196, 0.58856, 0.45225, 0.61438, 0.177, 0.62409, 0.38197, 0.65084, 0.13718, 0.66305, 0.32458, 0.68651, 0.1852, 0.71702, 0.14477, 0.75353, 0.14056, 0.79572, 0.15881, 0.83566, 0.16302, 0.87222, 0.21075, 0.90766, 0.34269, 0.96261, 0.41147, 0.97555, 0.38464, 0.72281, 0.3975, 0.74856, 0.43749, 0.77832], "triangles": [61, 113, 115, 39, 115, 114, 60, 61, 115, 116, 60, 115, 116, 115, 39, 40, 116, 39, 59, 60, 116, 117, 59, 116, 117, 116, 40, 117, 40, 41, 118, 117, 41, 118, 41, 42, 58, 59, 117, 58, 117, 118, 119, 118, 42, 119, 42, 43, 57, 58, 118, 119, 57, 118, 120, 119, 43, 120, 43, 44, 56, 119, 120, 57, 119, 56, 120, 44, 45, 55, 56, 120, 54, 55, 120, 45, 54, 120, 121, 54, 45, 121, 45, 46, 53, 54, 121, 122, 46, 47, 121, 46, 122, 52, 121, 122, 53, 121, 52, 48, 51, 122, 48, 122, 47, 52, 122, 51, 50, 51, 48, 49, 50, 48, 109, 66, 107, 65, 66, 109, 109, 107, 110, 110, 108, 24, 111, 65, 109, 64, 65, 111, 63, 64, 111, 112, 109, 110, 111, 109, 112, 25, 110, 24, 112, 110, 25, 113, 63, 111, 62, 63, 113, 114, 111, 112, 113, 111, 114, 61, 62, 113, 26, 112, 25, 114, 112, 26, 115, 113, 114, 123, 114, 26, 123, 26, 27, 39, 114, 123, 124, 123, 27, 124, 27, 28, 38, 39, 123, 38, 123, 124, 125, 124, 28, 125, 28, 29, 37, 124, 125, 38, 124, 37, 36, 125, 29, 36, 29, 30, 37, 125, 36, 35, 36, 30, 35, 30, 31, 34, 31, 32, 35, 31, 34, 34, 32, 33, 69, 101, 102, 19, 102, 18, 103, 69, 102, 68, 69, 103, 104, 102, 19, 103, 102, 104, 20, 104, 19, 105, 68, 103, 67, 68, 105, 106, 103, 104, 106, 104, 20, 105, 103, 106, 21, 106, 20, 107, 67, 105, 66, 67, 107, 23, 106, 21, 108, 105, 106, 108, 106, 23, 107, 105, 108, 22, 23, 21, 110, 107, 108, 23, 24, 108, 72, 73, 95, 97, 95, 96, 72, 95, 97, 98, 97, 96, 98, 96, 16, 71, 72, 97, 99, 71, 97, 99, 97, 98, 100, 99, 98, 17, 100, 98, 16, 17, 98, 70, 71, 99, 101, 99, 100, 70, 99, 101, 18, 100, 17, 101, 100, 18, 69, 70, 101, 102, 101, 18, 7, 8, 4, 84, 3, 4, 5, 6, 7, 7, 4, 5, 8, 84, 4, 80, 78, 79, 85, 3, 84, 2, 3, 85, 9, 84, 8, 85, 84, 9, 81, 78, 80, 86, 2, 85, 1, 2, 86, 10, 85, 9, 86, 85, 10, 81, 76, 77, 81, 77, 78, 89, 81, 82, 76, 81, 89, 87, 1, 86, 0, 1, 87, 75, 76, 89, 11, 86, 10, 87, 86, 11, 90, 89, 82, 90, 82, 83, 88, 0, 87, 83, 0, 88, 91, 83, 88, 90, 83, 91, 90, 74, 75, 90, 75, 89, 12, 87, 11, 88, 87, 12, 92, 91, 88, 92, 88, 12, 13, 92, 12, 93, 90, 91, 74, 90, 93, 95, 73, 74, 94, 91, 92, 93, 91, 94, 93, 95, 74, 14, 92, 13, 94, 92, 14, 96, 93, 94, 95, 93, 96, 15, 94, 14, 96, 94, 15, 16, 96, 15], "vertices": [1, 28, 120.18, -25.78, 1, 1, 28, 88.21, -22.07, 1, 1, 28, 56.23, -18.36, 1, 1, 28, 32.22, -15.58, 1, 1, 28, 4.96, -12.42, 1, 1, 28, -25.58, 7.57, 1, 1, 28, -26.77, 9.45, 1, 1, 28, -22.62, 13.19, 1, 1, 28, 13.11, 22.49, 1, 1, 28, 47.35, 31.4, 1, 1, 28, 74.39, 38.44, 1, 1, 28, 102.63, 45.79, 1, 2, 28, 134.7, 54.13, 0.99968, 33, -82.45, 94.44, 0.00032, 2, 28, 155.83, 59.63, 0.98898, 33, -60.91, 90.87, 0.01102, 2, 28, 180.4, 66.03, 0.92467, 33, -35.87, 86.71, 0.07533, 2, 28, 213.57, 74.66, 0.67275, 33, -2.05, 81.1, 0.32725, 3, 28, 241.35, 81.89, 0.37718, 33, 26.27, 76.4, 0.61929, 34, -103.35, 65.42, 0.00353, 3, 28, 265.75, 88.24, 0.17536, 33, 51.14, 72.27, 0.78626, 34, -78.18, 63.9, 0.03838, 3, 28, 299, 96.89, 0.03556, 33, 85.03, 66.65, 0.71735, 34, -43.89, 61.84, 0.24709, 3, 28, 327.76, 114.63, 0.00202, 33, 118.52, 71.15, 0.34745, 34, -11.05, 69.81, 0.65052, 3, 33, 153.4, 75.84, 0.07473, 34, 23.15, 78.1, 0.91979, 35, -111.49, 64.17, 0.00548, 3, 33, 186.05, 80.23, 0.00518, 34, 55.17, 85.87, 0.93712, 35, -80.8, 76.15, 0.05771, 3, 33, 221.44, 84.98, 0, 34, 89.86, 94.28, 0.81993, 35, -47.55, 89.13, 0.18007, 2, 34, 88.39, 89.44, 0.82108, 35, -48.36, 84.14, 0.17892, 2, 34, 141.9, 75.94, 0.31345, 35, 6.48, 77.92, 0.68655, 3, 34, 178.76, 66.64, 0.05566, 35, 44.25, 73.64, 0.94006, 36, -51.7, 88.94, 0.00428, 3, 34, 215.55, 57.36, 0.00047, 35, 81.95, 69.37, 0.89911, 36, -16.23, 75.46, 0.10042, 2, 35, 91.94, 80.86, 0.85655, 36, -3.71, 84.11, 0.14345, 2, 35, 106.04, 97.07, 0.85779, 36, 13.96, 96.32, 0.14221, 2, 35, 124.69, 118.52, 0.88921, 36, 37.35, 112.48, 0.11079, 2, 35, 138.66, 134.59, 0.91176, 36, 54.87, 124.59, 0.08824, 2, 35, 154.15, 152.41, 0.93041, 36, 74.3, 138.01, 0.06959, 2, 35, 166.89, 167.06, 0.94026, 36, 90.27, 149.05, 0.05974, 2, 35, 186.55, 189.68, 0.9463, 36, 114.92, 166.08, 0.0537, 2, 35, 175.24, 163.53, 0.94083, 36, 97.49, 143.55, 0.05917, 2, 35, 165.4, 140.77, 0.92783, 36, 82.31, 123.94, 0.07216, 2, 35, 154.77, 116.2, 0.90455, 36, 65.92, 102.77, 0.09545, 2, 35, 144.68, 92.87, 0.8708, 36, 50.36, 82.67, 0.1292, 2, 35, 131.72, 62.89, 0.78077, 36, 30.37, 56.84, 0.21923, 2, 35, 120.55, 37.07, 0.45735, 36, 13.15, 34.6, 0.54265, 2, 35, 143.19, 50.65, 0.05535, 36, 38.45, 42.14, 0.94465, 2, 35, 166.95, 64.9, 0.00134, 36, 65, 50.06, 0.99866, 1, 36, 96.81, 59.54, 1, 1, 36, 119.54, 66.32, 1, 1, 36, 143.49, 73.47, 1, 1, 36, 171.43, 81.8, 1, 1, 36, 180.25, 94.42, 1, 1, 36, 195.46, 116.19, 1, 1, 36, 202.42, 126.15, 1, 1, 36, 222.93, 128.33, 1, 1, 36, 223.14, 126.76, 1, 1, 36, 221.75, 107.56, 1, 1, 36, 214.06, 92.3, 1, 1, 36, 205.23, 74.77, 1, 1, 36, 190.16, 44.88, 1, 1, 36, 179.62, 33.44, 1, 1, 36, 164.7, 20.78, 1, 1, 36, 139.64, -0.51, 1, 1, 36, 112.89, -23.23, 1, 1, 36, 74.61, -36.36, 1, 2, 35, 157.04, -43.13, 0.04174, 36, 28.62, -52.14, 0.95826, 2, 35, 132.51, -55.11, 0.26237, 36, 1.89, -57.67, 0.73763, 2, 35, 110.38, -65.92, 0.57472, 36, -22.23, -62.66, 0.42528, 3, 34, 201.1, -86.83, 0.0036, 35, 86.93, -75.46, 0.81169, 36, -47.31, -66.09, 0.18471, 3, 34, 163.69, -96.47, 0.04369, 35, 51.15, -90.02, 0.92002, 36, -85.59, -71.33, 0.03628, 3, 34, 145.74, -86.43, 0.10174, 35, 32.02, -82.48, 0.88492, 36, -102.25, -59.27, 0.01334, 2, 34, 105.49, -68.12, 0.48168, 35, -10.32, -69.72, 0.51832, 2, 34, 72.95, -53.31, 0.87248, 35, -44.55, -59.4, 0.12752, 2, 34, 37.93, -37.38, 0.99877, 35, -81.38, -48.29, 0.00123, 2, 33, 125.78, -22.75, 0.17196, 34, 5.95, -22.82, 0.82804, 2, 33, 81.9, -34.33, 0.99546, 34, -36.48, -38.92, 0.00454, 2, 28, 310.47, -18.14, 0.00432, 33, 48.7, -43.1, 0.99568, 2, 28, 282.96, -40.26, 0.13142, 33, 14.57, -52.11, 0.86858, 2, 28, 249.07, -67.51, 0.61753, 33, -27.47, -63.22, 0.38247, 2, 28, 222.51, -88.87, 0.87864, 33, -60.43, -71.92, 0.12136, 2, 28, 198.45, -108.22, 0.97198, 33, -90.28, -79.8, 0.02802, 2, 28, 180.97, -122.28, 0.99376, 33, -111.97, -85.53, 0.00624, 2, 28, 160.46, -136.22, 0.99977, 33, -136.37, -89.92, 0.00023, 1, 28, 138.24, -133.45, 1, 1, 28, 103.85, -129.16, 1, 1, 28, 122.59, -117.32, 1, 1, 28, 136.72, -108.39, 1, 1, 28, 145.75, -68.79, 1, 1, 28, 154.65, -29.77, 1, 1, 28, 24.49, 8.41, 1, 1, 28, 58.51, 9.18, 1, 1, 28, 84.36, 10.65, 1, 1, 28, 115.5, 10.95, 1, 1, 28, 145.99, 14.93, 1, 2, 28, 159.64, -78.54, 0.99762, 33, -113.65, -36.9, 0.00238, 2, 28, 170.65, -55.18, 0.9939, 33, -94.09, -20.04, 0.0061, 1, 28, 175.52, -15.76, 1, 2, 28, 165.99, 26.3, 0.99726, 33, -65.19, 56.29, 0.00274, 2, 28, 207.8, -21.54, 0.97297, 33, -46.46, -4.43, 0.02703, 2, 28, 194.08, 27.84, 0.96102, 33, -38.91, 46.26, 0.03898, 2, 28, 241.04, -30.54, 0.51208, 33, -19.76, -26.18, 0.48792, 2, 28, 229.05, 30.38, 0.65009, 33, -5.93, 34.36, 0.34991, 1, 33, 13.19, -10.81, 1, 2, 28, 259.33, 35.59, 0.18186, 33, 23.85, 26.79, 0.81814, 1, 33, 44.34, -6.95, 1, 3, 28, 280.87, 45.21, 0.06687, 33, 47.45, 26.82, 0.92558, 34, -77.12, 18.31, 0.00754, 3, 28, 316.89, 44.99, 0.00307, 33, 80.25, 11.95, 0.96994, 34, -42.94, 6.94, 0.02699, 3, 28, 349.11, 71.85, 0.00011, 33, 120.62, 23.39, 0.27463, 34, -3.99, 22.52, 0.72526, 1, 34, 30.82, 16.07, 1, 3, 33, 154.33, 53.96, 0.06489, 34, 26.35, 56.44, 0.93213, 35, -105.42, 43.13, 0.00299, 2, 34, 66.82, 9.92, 0.99903, 35, -59.09, 2.45, 0.00097, 3, 33, 189.52, 56.88, 0.00192, 34, 61.04, 63.01, 0.94205, 35, -71.92, 54.29, 0.05602, 2, 34, 108.36, -3.1, 0.97134, 35, -16.18, -4.9, 0.02866, 2, 34, 96.54, 64.25, 0.75399, 35, -36.91, 60.26, 0.24601, 2, 34, 140.22, -17.44, 0.04327, 35, 17.31, -14.84, 0.95673, 2, 34, 144.27, 51.24, 0.21649, 35, 12.14, 53.76, 0.78351, 3, 34, 174.41, -33.94, 0.00835, 35, 53.4, -26.62, 0.98077, 36, -67.68, -10.46, 0.01088, 3, 34, 178.82, 36.41, 0.01525, 35, 48.36, 43.69, 0.9818, 36, -55.14, 58.91, 0.00296, 2, 35, 87.76, -26.69, 0.84841, 36, -34.42, -19.05, 0.15159, 2, 35, 82.4, 37.28, 0.9277, 36, -23.76, 44.26, 0.0723, 2, 35, 122.63, 4.45, 0.0092, 36, 7.09, 2.48, 0.9908, 1, 36, 38.39, -6.58, 1, 1, 36, 72.69, -3.27, 1, 1, 36, 104.17, 7.06, 1, 1, 36, 133.54, 12.47, 1, 1, 36, 160.07, 31.85, 1, 1, 36, 198.66, 80.68, 1, 1, 36, 206.07, 104.39, 1, 2, 35, 102.42, 66.64, 0.82879, 36, 2.92, 67.74, 0.17121, 2, 35, 120.3, 78.45, 0.83071, 36, 23.17, 74.75, 0.16929, 2, 35, 137.87, 99.72, 0.8768, 36, 45.46, 91, 0.1232], "hull": 84, "edges": [8, 10, 10, 12, 12, 14, 44, 46, 96, 98, 98, 100, 100, 102, 108, 110, 128, 130, 152, 154, 8, 6, 6, 168, 14, 16, 168, 16, 6, 4, 4, 170, 16, 18, 170, 18, 4, 2, 2, 172, 18, 20, 172, 20, 2, 0, 0, 166, 0, 174, 20, 22, 174, 22, 166, 176, 22, 24, 176, 24, 154, 156, 156, 158, 158, 160, 160, 162, 156, 160, 150, 152, 150, 178, 162, 164, 164, 166, 178, 164, 148, 150, 148, 180, 180, 166, 180, 182, 182, 184, 24, 26, 184, 26, 148, 186, 186, 188, 26, 28, 188, 28, 146, 148, 146, 190, 190, 192, 28, 30, 192, 30, 144, 146, 144, 194, 194, 196, 30, 32, 196, 32, 142, 144, 142, 198, 198, 200, 32, 34, 34, 36, 200, 34, 138, 140, 140, 142, 140, 202, 202, 36, 138, 204, 36, 38, 204, 38, 136, 138, 136, 206, 206, 208, 38, 40, 208, 40, 134, 136, 134, 210, 210, 212, 40, 42, 42, 44, 212, 42, 130, 132, 132, 134, 132, 214, 214, 216, 216, 46, 130, 218, 218, 220, 46, 48, 220, 48, 128, 222, 222, 224, 48, 50, 50, 52, 224, 50, 124, 126, 126, 128, 126, 226, 226, 228, 228, 52, 120, 122, 122, 124, 122, 230, 230, 78, 120, 232, 78, 80, 232, 80, 116, 118, 118, 120, 118, 234, 80, 82, 234, 82, 116, 236, 82, 84, 236, 84, 114, 116, 114, 238, 84, 86, 238, 86, 110, 112, 112, 114, 112, 240, 86, 88, 88, 90, 240, 88, 108, 90, 106, 108, 106, 242, 90, 92, 242, 92, 102, 104, 104, 106, 104, 244, 92, 94, 94, 96, 244, 94, 78, 246, 52, 54, 246, 54, 76, 78, 76, 248, 54, 56, 248, 56, 74, 76, 74, 250, 56, 58, 250, 58, 72, 74, 58, 60, 72, 60, 70, 72, 60, 62, 70, 62, 66, 68, 68, 70, 62, 64, 64, 66, 68, 64], "width": 327, "height": 816}}, "s2": {"s2": {"type": "mesh", "uvs": [0.77696, 0, 0.89618, 0.00936, 1, 0.1019, 0.99968, 0.18963, 0.90883, 0.29118, 0.82647, 0.38325, 0.72163, 0.50044, 0.63129, 0.60143, 0.51398, 0.73256, 0.43082, 0.82552, 0.35049, 0.91531, 0.21804, 0.99521, 0.05189, 0.9947, 0, 0.7725, 0, 0.63675, 0.16281, 0.50107, 0.27395, 0.40845, 0.38319, 0.31741, 0.49617, 0.22326, 0.63366, 0.10867, 0.76406, 0, 0.72552, 0.18221, 0.63382, 0.28855, 0.52129, 0.40441, 0.41084, 0.506, 0.28997, 0.59805, 0.19202, 0.74725], "triangles": [21, 19, 20, 2, 0, 1, 18, 19, 21, 22, 18, 21, 2, 3, 0, 3, 21, 0, 21, 20, 0, 3, 4, 21, 4, 22, 21, 5, 22, 4, 23, 18, 22, 17, 18, 23, 6, 22, 5, 23, 22, 6, 23, 16, 17, 24, 16, 23, 15, 16, 24, 25, 15, 24, 7, 23, 6, 24, 23, 7, 14, 15, 25, 8, 24, 7, 25, 24, 8, 26, 14, 25, 26, 25, 8, 13, 14, 26, 9, 26, 8, 10, 26, 9, 12, 13, 26, 11, 12, 26, 10, 11, 26], "vertices": [1, 29, 178.54, 20.33, 1, 1, 29, 186.58, 6.84, 1, 1, 29, 181.94, -13.57, 1, 1, 29, 169.81, -22.55, 1, 1, 29, 148.69, -23.44, 1, 1, 29, 129.55, -24.24, 1, 1, 29, 105.18, -25.27, 1, 1, 29, 84.18, -26.15, 1, 1, 29, 56.91, -27.3, 1, 1, 29, 37.58, -28.11, 1, 1, 29, 18.91, -28.89, 1, 1, 29, -2.48, -23.18, 1, 1, 29, -15.41, -5.67, 1, 1, 29, 11.18, 22.6, 1, 1, 29, 29.91, 36.55, 1, 1, 29, 61.37, 33.38, 1, 1, 29, 82.84, 31.22, 1, 1, 29, 103.95, 29.09, 1, 1, 29, 125.77, 26.9, 1, 1, 29, 152.34, 24.22, 1, 1, 29, 177.53, 21.68, 1, 1, 29, 149.38, 7.02, 1, 1, 29, 127.54, 5.73, 1, 1, 29, 102.75, 5.65, 1, 1, 29, 80.09, 6.82, 1, 1, 29, 57.94, 10.06, 1, 1, 29, 29.69, 5.02, 1], "hull": 21, "edges": [0, 40, 20, 22, 22, 24, 24, 26, 26, 28, 38, 40, 38, 42, 6, 8, 42, 8, 36, 38, 36, 44, 8, 10, 44, 10, 34, 36, 34, 46, 10, 12, 46, 12, 32, 34, 32, 48, 12, 14, 48, 14, 28, 30, 30, 32, 30, 50, 14, 16, 50, 16, 28, 52, 16, 18, 18, 20, 52, 18, 0, 2, 2, 4, 4, 6], "width": 131, "height": 172}}, "s3": {"s3": {"type": "mesh", "uvs": [0.4183, 0, 0.46323, 0.05085, 0.4592, 0.08169, 0.45352, 0.12519, 0.44907, 0.15925, 0.44376, 0.19988, 0.43381, 0.27605, 0.42268, 0.36122, 0.38485, 0.43884, 0.43805, 0.51406, 0.46348, 0.57122, 0.52841, 0.56101, 0.62092, 0.54648, 0.66816, 0.53906, 0.73582, 0.59836, 0.78869, 0.6447, 0.86125, 0.63548, 0.93454, 0.62618, 0.9491, 0.59671, 0.97113, 0.55211, 0.98751, 0.51895, 0.99834, 0.53933, 0.99847, 0.59684, 0.99866, 0.68062, 0.99886, 0.7724, 0.99905, 0.85385, 0.93466, 0.86739, 0.87062, 0.88086, 0.74185, 0.90795, 0.62763, 0.93197, 0.67658, 0.9544, 0.73353, 0.98049, 0.79906, 0.98079, 0.87388, 0.98113, 0.81954, 0.99132, 0.78452, 0.99315, 0.72505, 0.99625, 0.65314, 1, 0.59804, 1, 0.55794, 0.97597, 0.50642, 0.9451, 0.52741, 0.88242, 0.54857, 0.81924, 0.50486, 0.77509, 0.44244, 0.80219, 0.35168, 0.84161, 0.26885, 0.75909, 0.2199, 0.71032, 0.1382, 0.62894, 0.06916, 0.56016, 0, 0.49127, 0, 0.44181, 0.00329, 0.40952, 0.0178, 0.26685, 0.07594, 0.21349, 0.09184, 0.23379, 0.10919, 0.21668, 0.11962, 0.18403, 0.11598, 0.15686, 0.15621, 0.18816, 0.24793, 0.21982, 0.25725, 0.17725, 0.26663, 0.13439, 0.27536, 0.0945, 0.2841, 0.0546, 0.32164, 0.03275, 0.37792, 0, 0.40141, 0.04472, 0.39374, 0.077, 0.38416, 0.11534, 0.37745, 0.15165, 0.3693, 0.19372, 0.35685, 0.2432, 0.09202, 0.23945, 0.19112, 0.34427, 0.31504, 0.40113, 0.23567, 0.25398, 0.34497, 0.30557, 0.12655, 0.20991, 0.16532, 0.24911, 0.14471, 0.29464, 0.05759, 0.29229, 0.10521, 0.37467, 0.07728, 0.45475, 0.1717, 0.41555, 0.1584, 0.50458, 0.28141, 0.46986, 0.25482, 0.58073, 0.35722, 0.51914, 0.30801, 0.64849, 0.42105, 0.58409, 0.37184, 0.70672, 0.47358, 0.62273, 0.4656, 0.72296, 0.55736, 0.64177, 0.55736, 0.73752, 0.6571, 0.65073, 0.64513, 0.78343, 0.71628, 0.65141, 0.7216, 0.80148, 0.67106, 0.85243, 0.56313, 0.93825, 0.5704, 0.90395, 0.61865, 0.96602, 0.78989, 0.70169, 0.7881, 0.82354, 0.87862, 0.70269, 0.87742, 0.79847, 0.93519, 0.68464, 0.93816, 0.82906, 0.96972, 0.65004], "triangles": [100, 99, 28, 28, 105, 27, 28, 99, 105, 26, 27, 109, 27, 105, 107, 26, 109, 25, 109, 27, 107, 109, 24, 25, 108, 24, 109, 107, 108, 109, 107, 105, 104, 105, 99, 104, 99, 98, 104, 108, 107, 106, 107, 104, 106, 108, 23, 24, 104, 16, 106, 106, 16, 108, 98, 15, 104, 104, 15, 16, 16, 17, 108, 108, 110, 23, 108, 17, 110, 110, 22, 23, 17, 18, 110, 110, 18, 22, 18, 19, 22, 19, 21, 22, 19, 20, 21, 38, 103, 37, 37, 30, 36, 37, 103, 30, 38, 39, 103, 36, 31, 35, 36, 30, 31, 35, 32, 34, 35, 31, 32, 34, 32, 33, 39, 101, 103, 39, 40, 101, 103, 29, 30, 103, 101, 29, 40, 41, 101, 101, 102, 29, 101, 41, 102, 29, 100, 28, 29, 102, 100, 100, 102, 97, 97, 102, 41, 41, 42, 97, 100, 97, 99, 97, 98, 99, 97, 96, 98, 42, 95, 97, 42, 43, 95, 44, 93, 43, 97, 95, 96, 43, 93, 95, 93, 94, 95, 95, 94, 96, 98, 14, 15, 14, 98, 13, 94, 12, 96, 98, 96, 13, 96, 12, 13, 94, 11, 12, 45, 91, 44, 45, 46, 91, 44, 91, 93, 46, 89, 91, 46, 47, 89, 93, 92, 94, 93, 91, 92, 47, 87, 89, 47, 48, 87, 91, 90, 92, 91, 89, 90, 89, 88, 90, 89, 87, 88, 92, 11, 94, 90, 10, 92, 92, 10, 11, 90, 9, 10, 90, 88, 9, 87, 86, 88, 88, 8, 9, 88, 86, 8, 48, 85, 87, 48, 49, 85, 87, 85, 86, 49, 83, 85, 49, 50, 83, 85, 84, 86, 85, 83, 84, 50, 51, 83, 86, 75, 8, 86, 84, 75, 51, 52, 83, 83, 82, 84, 83, 52, 82, 84, 74, 75, 84, 82, 74, 52, 81, 82, 52, 53, 81, 82, 80, 74, 82, 81, 80, 81, 73, 80, 74, 80, 76, 76, 80, 79, 80, 73, 79, 81, 53, 73, 53, 54, 73, 60, 76, 59, 73, 78, 79, 78, 59, 79, 76, 79, 59, 73, 56, 78, 54, 55, 73, 73, 55, 56, 78, 56, 57, 59, 78, 58, 58, 78, 57, 8, 75, 7, 75, 77, 7, 75, 74, 77, 7, 77, 6, 74, 76, 77, 77, 76, 72, 77, 72, 6, 72, 76, 60, 6, 72, 5, 5, 72, 71, 72, 60, 71, 71, 60, 61, 71, 70, 5, 5, 70, 4, 71, 61, 70, 61, 62, 70, 4, 70, 3, 70, 69, 3, 70, 62, 69, 62, 63, 69, 69, 68, 3, 3, 68, 2, 69, 63, 68, 63, 64, 68, 68, 67, 2, 2, 67, 1, 64, 65, 68, 68, 65, 67, 67, 0, 1, 65, 66, 67, 67, 66, 0], "vertices": [1, 19, -13.03, 12.28, 1, 1, 19, 16.73, 43.79, 1, 1, 19, 37.91, 45.16, 1, 1, 19, 67.78, 47.1, 1, 1, 19, 91.17, 48.61, 1, 1, 19, 119.07, 50.42, 1, 1, 19, 171.37, 53.8, 1, 3, 19, 229.86, 57.59, 0.9742, 21, 86.99, 132.11, 0.00635, 22, -29.83, 147.19, 0.01945, 4, 19, 285.93, 45.33, 0.52121, 20, -82.25, -0.43, 0.00532, 21, 125.89, 89.93, 0.20168, 22, -14.41, 91.92, 0.27179, 4, 19, 331.29, 84.43, 0.10821, 21, 185.48, 95.96, 0.05115, 22, 41.39, 70.15, 0.83882, 23, -69.27, 105.41, 0.00182, 4, 19, 367.25, 105.63, 0.01382, 21, 227.1, 92.72, 0.00036, 22, 76.96, 48.31, 0.8763, 23, -45.99, 70.76, 0.10953, 3, 19, 353.9, 141.26, 0.00052, 22, 102.18, 76.8, 0.49669, 23, -11.21, 86.18, 0.50279, 2, 22, 138.11, 117.39, 0.14443, 23, 38.34, 108.16, 0.85557, 3, 22, 156.46, 138.11, 0.08522, 23, 63.64, 119.38, 0.90991, 24, -122.42, 80.86, 0.00486, 3, 22, 212.13, 130.06, 0.01748, 23, 110.93, 88.91, 0.85294, 24, -66.89, 71.9, 0.12958, 3, 22, 255.63, 123.76, 0.00017, 23, 147.87, 65.1, 0.38687, 24, -23.49, 64.91, 0.61296, 2, 23, 187.09, 80.89, 0.04747, 24, 6.1, 95.09, 0.95253, 2, 23, 226.69, 96.82, 0.00058, 24, 35.99, 125.57, 0.99942, 1, 24, 30.56, 146.72, 1, 1, 24, 22.34, 178.72, 1, 1, 24, 16.23, 202.51, 1, 1, 24, 29.6, 195.13, 1, 1, 24, 53.33, 163.76, 1, 1, 24, 87.89, 118.05, 1, 1, 24, 125.76, 67.98, 1, 1, 24, 159.36, 23.54, 1, 1, 24, 135.31, -6.18, 1, 2, 23, 231.09, -81.17, 0.0192, 24, 111.39, -35.72, 0.9808, 2, 23, 163.19, -116.32, 0.37043, 24, 63.29, -95.15, 0.62957, 2, 23, 102.97, -147.5, 0.81589, 24, 20.63, -147.86, 0.18411, 2, 23, 133.95, -155.92, 0.88225, 24, 52.38, -143.15, 0.11775, 2, 23, 169.99, -165.71, 0.91125, 24, 89.32, -137.67, 0.08875, 2, 23, 206.76, -157.19, 0.93702, 24, 119.59, -115.12, 0.06298, 2, 23, 248.74, -147.47, 0.94601, 24, 154.15, -89.37, 0.05399, 2, 23, 219.9, -161.48, 0.94106, 24, 133.35, -113.78, 0.05894, 2, 23, 200.56, -167.35, 0.93269, 24, 117.99, -126.91, 0.06731, 2, 23, 167.72, -177.32, 0.9083, 24, 91.9, -149.21, 0.0917, 2, 23, 128.02, -189.38, 0.88302, 24, 60.36, -176.18, 0.11698, 2, 23, 97.13, -196.71, 0.87385, 24, 35.01, -195.28, 0.12615, 2, 23, 70.87, -186.05, 0.87123, 24, 6.67, -196.05, 0.12877, 2, 23, 37.12, -172.36, 0.87431, 24, -29.73, -197.03, 0.12569, 2, 23, 38.98, -127.85, 0.87136, 24, -45.87, -155.52, 0.12864, 3, 22, 219.87, -55.41, 0.00806, 23, 40.86, -82.99, 0.91527, 24, -62.14, -113.67, 0.07667, 3, 22, 181.45, -47.06, 0.21257, 23, 9.4, -59.42, 0.78173, 24, -100.42, -104.69, 0.0057, 2, 22, 164.48, -83.78, 0.66279, 23, -21.31, -85.76, 0.33721, 2, 22, 139.8, -137.17, 0.86798, 23, -65.95, -124.06, 0.13202, 3, 21, 296.2, -63.18, 0.01583, 22, 67.48, -121.96, 0.95652, 23, -125.4, -80.16, 0.02765, 3, 21, 254.06, -74.65, 0.10435, 22, 24.74, -112.97, 0.89479, 23, -160.54, -54.21, 0.00087, 2, 21, 183.72, -93.78, 0.56569, 22, -46.6, -97.96, 0.43431, 2, 21, 124.28, -109.94, 0.92752, 22, -106.88, -85.29, 0.07248, 2, 21, 64.74, -126.14, 0.99961, 22, -167.26, -72.59, 0.00039, 1, 21, 34.08, -111.85, 1, 2, 20, 80.15, 149.01, 1e-05, 21, 14.86, -100.8, 0.99999, 2, 20, 146.86, 77.31, 0.45765, 21, -70.05, -51.99, 0.54235, 2, 20, 151.44, 27.98, 0.988, 21, -88.98, -6.22, 0.012, 2, 20, 135, 30.52, 0.99138, 21, -72.53, -3.78, 0.00862, 2, 20, 136.96, 15.25, 0.99647, 21, -78.91, 10.22, 0.00353, 2, 20, 149.49, -4.19, 0.99999, 21, -96.62, 25.1, 1e-05, 1, 20, 164.67, -15.12, 1, 2, 19, 139.89, -114.09, 0.00255, 20, 133.24, -17.92, 0.99745, 2, 19, 152.06, -58.31, 0.4028, 20, 81.73, -42.53, 0.5972, 2, 19, 122.45, -58.07, 0.81789, 20, 99.71, -66.06, 0.18211, 2, 19, 92.64, -57.83, 0.95598, 20, 117.81, -89.75, 0.04402, 2, 19, 64.9, -57.61, 0.99408, 20, 134.66, -111.79, 0.00592, 1, 19, 37.14, -57.38, 1, 1, 19, 18.68, -38.67, 1, 1, 19, -9, -10.63, 1, 1, 19, 18.78, 8, 1, 1, 19, 41.29, 7.47, 1, 1, 19, 68.07, 6.58, 1, 1, 19, 93.21, 7.08, 1, 1, 19, 122.36, 7.45, 1, 1, 19, 156.93, 6.25, 1, 2, 20, 132.06, 33.04, 0.79205, 21, -68.98, -5.33, 0.20795, 2, 20, 40.6, 38.86, 0.27609, 21, 20.12, 16.12, 0.72391, 4, 19, 267.5, 1.26, 0.53952, 20, -36.14, 12.06, 0.16032, 21, 85.53, 64.38, 0.24991, 22, -61.98, 87.56, 0.05024, 2, 19, 176.29, -61.21, 0.17076, 20, 69.15, -21.62, 0.82924, 2, 19, 200.13, 6.91, 0.9999, 22, -88.54, 149.72, 0.0001, 2, 20, 133.69, 4.73, 0.99139, 21, -78.89, 21.24, 0.00861, 2, 20, 98.82, 6.18, 0.94983, 21, -45.16, 30.15, 0.05017, 2, 20, 83.71, 35.89, 0.39852, 21, -21.94, 6.23, 0.60148, 2, 20, 118.59, 72, 0.29433, 21, -44.6, -38.57, 0.70567, 1, 21, 18.06, -37.52, 1, 1, 21, 60.91, -75.24, 1, 1, 21, 59.58, -14.62, 1, 2, 21, 111.54, -47.29, 0.97906, 22, -89.68, -23.71, 0.02094, 4, 19, 317.15, -9.67, 0.1062, 20, -57.99, 57.98, 0.02927, 21, 119.96, 26.96, 0.75787, 22, -48.37, 38.57, 0.10666, 2, 21, 182.22, -18.96, 0.31622, 22, -13.86, -30.68, 0.68378, 3, 19, 342.78, 39.17, 0.10638, 21, 168.95, 52.29, 0.13616, 22, 6.79, 38.8, 0.75746, 2, 21, 237.17, -10.77, 0.03348, 22, 38.79, -48.41, 0.96652, 4, 19, 380.16, 83.09, 0.01383, 21, 224.76, 66.85, 9e-05, 22, 63.09, 26.35, 0.96735, 23, -67.74, 56.55, 0.01873, 3, 21, 288.8, 5.72, 0.00011, 22, 92.27, -57.24, 0.94904, 23, -75.95, -31.61, 0.05085, 3, 19, 400.94, 117.47, 0.00092, 22, 103.2, 24.09, 0.87544, 23, -32.2, 37.82, 0.12363, 2, 22, 141.7, -32.82, 0.53416, 23, -20.84, -29.95, 0.46584, 2, 22, 149.28, 43.47, 0.20962, 23, 17.76, 36.29, 0.79038, 3, 22, 189.52, -8.2, 0.01045, 23, 32.88, -27.43, 0.98583, 24, -91.73, -65.97, 0.00372, 3, 22, 198.37, 73.93, 0.0229, 23, 75.08, 43.59, 0.96452, 24, -81.56, 16.01, 0.01258, 2, 23, 89.33, -46.32, 0.87006, 24, -32.45, -60.64, 0.12994, 3, 22, 225.55, 94.51, 0.00504, 23, 108.35, 51, 0.82588, 24, -54.05, 36.15, 0.16908, 2, 23, 135.03, -48.16, 0.39637, 24, 10.16, -44, 0.60363, 2, 23, 114.76, -88.79, 0.63583, 24, 7.88, -89.35, 0.36417, 2, 23, 67.82, -160.26, 0.86501, 24, -6.46, -173.64, 0.13499, 2, 23, 66.48, -136.46, 0.8536, 24, -17.23, -152.38, 0.1464, 2, 23, 103.32, -171.36, 0.8655, 24, 30.51, -169.57, 0.1345, 2, 23, 157.54, 27.33, 0.14147, 24, 0.51, 34.19, 0.85853, 2, 23, 175.79, -54, 0.10037, 24, 49.84, -33.01, 0.89963, 2, 23, 207.43, 38.47, 0.00496, 24, 41.75, 64.39, 0.99505, 1, 24, 80.61, 11.65, 1, 1, 24, 60.34, 93.86, 1, 1, 24, 121.15, 15.98, 1, 1, 24, 61.99, 124.73, 1], "hull": 67, "edges": [0, 132, 0, 2, 14, 16, 16, 18, 18, 20, 40, 42, 66, 68, 74, 76, 84, 86, 100, 102, 118, 120, 128, 130, 130, 132, 130, 134, 134, 2, 128, 136, 2, 4, 136, 4, 126, 128, 126, 138, 4, 6, 138, 6, 124, 126, 124, 140, 6, 8, 140, 8, 120, 122, 122, 124, 122, 142, 8, 10, 142, 10, 120, 144, 10, 12, 12, 14, 144, 12, 108, 146, 148, 150, 150, 16, 118, 152, 152, 154, 154, 14, 114, 116, 116, 118, 116, 156, 156, 146, 118, 158, 146, 160, 160, 148, 158, 160, 146, 162, 102, 104, 162, 104, 104, 106, 106, 108, 160, 164, 164, 166, 98, 100, 166, 98, 148, 168, 168, 170, 96, 98, 170, 96, 150, 172, 172, 174, 94, 96, 174, 94, 16, 176, 176, 178, 90, 92, 92, 94, 178, 92, 18, 180, 180, 182, 20, 184, 184, 186, 186, 86, 182, 90, 20, 22, 22, 188, 188, 190, 190, 84, 22, 24, 24, 26, 24, 192, 192, 194, 80, 82, 82, 84, 194, 82, 26, 196, 196, 198, 198, 200, 200, 58, 80, 202, 202, 58, 82, 204, 204, 58, 76, 78, 78, 80, 78, 206, 58, 60, 60, 62, 206, 60, 72, 74, 62, 64, 64, 66, 68, 70, 70, 72, 30, 208, 208, 210, 56, 58, 210, 56, 30, 32, 32, 34, 32, 212, 212, 214, 54, 56, 214, 54, 34, 216, 216, 218, 50, 52, 52, 54, 218, 52, 34, 36, 36, 220, 220, 46, 46, 48, 48, 50, 42, 44, 44, 46, 36, 38, 38, 40, 26, 28, 28, 30, 86, 88, 88, 90, 112, 114, 108, 110, 110, 112], "width": 576, "height": 684}}, "st": {"st": {"type": "mesh", "uvs": [0.51689, 0, 0.56326, 0.04831, 0.60546, 0.09228, 0.64119, 0.12951, 0.63584, 0.15762, 0.63058, 0.1853, 0.60715, 0.21293, 0.5728, 0.25345, 0.52714, 0.30731, 0.49177, 0.34902, 0.46776, 0.39609, 0.48054, 0.4432, 0.49371, 0.49174, 0.50726, 0.54167, 0.5212, 0.59308, 0.53541, 0.64544, 0.57538, 0.72535, 0.62532, 0.77811, 0.69481, 0.8515, 0.7612, 0.86809, 0.8252, 0.90277, 0.91094, 0.94924, 1, 0.99751, 1, 0.99926, 0.87554, 0.99928, 0.71736, 0.99932, 0.49725, 0.99936, 0.33044, 0.9994, 0.25102, 1, 0.2392, 0.94132, 0.2254, 0.8728, 0.21215, 0.80698, 0.19858, 0.73963, 0.18466, 0.6705, 0.17016, 0.59848, 0.15827, 0.53944, 0.14645, 0.48078, 0.16208, 0.43252, 0.17855, 0.38167, 0.1975, 0.32315, 0.2151, 0.2688, 0.22977, 0.22347, 0.24096, 0.18892, 0.25283, 0.15228, 0.27025, 0.12926, 0.29912, 0.09112, 0.33127, 0.07676, 0.37871, 0.05558, 0.50315, 0, 0.42413, 0.05783, 0.52486, 0.0554, 0.41965, 0.09029, 0.51516, 0.09659, 0.38011, 0.11112, 0.51367, 0.12517, 0.34952, 0.14165, 0.50546, 0.1557, 0.33758, 0.16813, 0.48382, 0.18364, 0.31221, 0.19285, 0.47711, 0.21271, 0.30176, 0.23306, 0.47039, 0.25535, 0.29654, 0.28445, 0.46741, 0.30335, 0.28684, 0.33187, 0.44204, 0.34786, 0.26744, 0.39021, 0.41145, 0.3936, 0.25923, 0.44012, 0.40622, 0.44157, 0.2555, 0.48702, 0.40771, 0.49187, 0.25923, 0.54117, 0.41368, 0.54117, 0.2555, 0.59825, 0.43607, 0.59631, 0.2555, 0.66629, 0.46144, 0.65999, 0.25923, 0.73038, 0.49725, 0.71972, 0.26371, 0.80181, 0.55247, 0.78679, 0.31519, 0.87181, 0.59425, 0.85098, 0.36444, 0.9396, 0.63603, 0.91344], "triangles": [26, 86, 25, 25, 20, 24, 25, 86, 20, 23, 24, 22, 24, 20, 21, 24, 21, 22, 86, 19, 20, 85, 84, 86, 86, 18, 19, 86, 84, 18, 84, 17, 18, 84, 82, 17, 81, 80, 82, 82, 16, 17, 82, 80, 16, 79, 78, 80, 80, 15, 16, 80, 78, 15, 78, 14, 15, 28, 29, 27, 27, 85, 26, 27, 29, 85, 26, 85, 86, 29, 83, 85, 29, 30, 83, 85, 83, 84, 30, 81, 83, 30, 31, 81, 83, 82, 84, 83, 81, 82, 31, 32, 81, 32, 79, 81, 81, 79, 80, 32, 33, 79, 33, 77, 79, 79, 77, 78, 77, 33, 75, 33, 34, 75, 77, 76, 78, 77, 75, 76, 78, 76, 14, 75, 34, 73, 75, 74, 76, 75, 73, 74, 73, 34, 35, 76, 13, 14, 76, 74, 13, 74, 12, 13, 73, 72, 74, 74, 72, 12, 35, 71, 73, 73, 71, 72, 35, 36, 71, 72, 71, 70, 71, 69, 70, 72, 11, 12, 72, 70, 11, 36, 37, 71, 71, 37, 69, 70, 10, 11, 70, 68, 10, 69, 67, 70, 70, 67, 68, 37, 38, 69, 69, 38, 67, 68, 66, 10, 10, 66, 9, 67, 65, 68, 68, 65, 66, 38, 39, 67, 67, 39, 65, 66, 64, 9, 9, 64, 8, 64, 66, 63, 66, 65, 63, 65, 39, 63, 39, 40, 63, 64, 62, 8, 8, 62, 7, 64, 63, 62, 63, 40, 61, 63, 61, 62, 61, 40, 41, 62, 60, 7, 62, 61, 60, 41, 42, 61, 61, 42, 59, 59, 57, 60, 60, 57, 58, 57, 59, 43, 59, 42, 43, 57, 55, 58, 58, 55, 56, 43, 44, 57, 57, 44, 55, 55, 53, 56, 44, 45, 55, 55, 45, 53, 56, 53, 54, 60, 58, 6, 58, 56, 5, 61, 59, 60, 7, 60, 6, 6, 58, 5, 5, 56, 4, 56, 54, 4, 4, 54, 3, 3, 54, 2, 54, 52, 2, 54, 51, 52, 52, 1, 2, 52, 50, 1, 51, 49, 52, 52, 49, 50, 46, 47, 51, 51, 47, 49, 50, 48, 0, 50, 49, 48, 49, 47, 48, 50, 0, 1, 53, 46, 51, 45, 46, 53, 53, 51, 54], "vertices": [1, 6, 72.45, -29.09, 1, 2, 6, 16.09, -56.09, 0.99675, 4, 312.52, -164.47, 0.00325, 3, 6, -35.23, -80.67, 0.9217, 5, 96.57, -176.39, 0.0429, 4, 268.14, -200.07, 0.0354, 3, 6, -78.66, -101.47, 0.82157, 5, 59, -206.52, 0.10008, 4, 230.57, -230.2, 0.07834, 3, 6, -108.92, -94.27, 0.74605, 5, 27.89, -206.39, 0.13969, 4, 199.46, -230.07, 0.11426, 4, 6, -138.71, -87.19, 0.65408, 5, -2.73, -206.26, 0.18068, 4, 168.84, -229.94, 0.16471, 2, -174.19, 262.65, 0.00052, 4, 6, -167.02, -67.23, 0.53121, 5, -34.83, -193.27, 0.22209, 4, 136.74, -216.95, 0.24199, 2, -144.59, 244.66, 0.00471, 4, 6, -208.53, -37.98, 0.33075, 5, -81.91, -174.22, 0.23189, 4, 89.66, -197.9, 0.40432, 2, -101.2, 218.27, 0.03304, 4, 6, -263.71, 0.91, 0.13203, 5, -144.48, -148.89, 0.13683, 4, 27.09, -172.57, 0.56724, 2, -43.52, 183.2, 0.16389, 5, 6, -306.45, 31.03, 0.04798, 5, -192.95, -129.28, 0.05204, 4, -21.37, -152.96, 0.49272, 2, 1.15, 156.03, 0.40665, 45, -357.76, 154.45, 0.0006, 5, 6, -355.93, 53.75, 0.00964, 5, -246.3, -118.4, 0.00805, 4, -74.73, -142.08, 0.22657, 2, 52.05, 136.7, 0.74765, 45, -317.34, 117.96, 0.00808, 5, 6, -408.34, 50.41, 0.0008, 5, -296.58, -133.57, 0.00019, 4, -125.01, -157.25, 0.0603, 2, 104.12, 143.58, 0.9036, 45, -266.33, 105.47, 0.0351, 3, 4, -176.81, -172.89, 0.00981, 2, 157.77, 150.66, 0.89072, 45, -213.77, 92.6, 0.09947, 3, 4, -230.12, -188.97, 0.00019, 2, 212.97, 157.95, 0.76736, 45, -159.69, 79.36, 0.23245, 2, 2, 269.78, 165.46, 0.53803, 45, -104.02, 65.73, 0.46197, 2, 2, 327.66, 173.1, 0.21132, 45, -47.31, 51.85, 0.78868, 1, 45, 44.47, 42.6, 1, 2, 45, 111.83, 51.86, 0.66721, 46, -26.76, 54.89, 0.33279, 2, 46, 67.75, 59.16, 0.75758, 47, -29.36, 74.16, 0.24242, 2, 46, 106.62, 91.74, 0.2699, 47, 21.14, 78.9, 0.7301, 2, 46, 162.05, 113.19, 0.02802, 47, 78.94, 65.06, 0.97198, 1, 47, 156.38, 46.53, 1, 1, 47, 236.82, 27.28, 1, 1, 47, 237.68, 25.56, 1, 1, 47, 158.2, -13.91, 1, 3, 3, 328.44, 162.64, 0.03032, 46, 217.5, -5.45, 0.00204, 47, 57.19, -64.07, 0.96765, 3, 3, 260.97, 20.94, 0.86082, 46, 141.47, -142.74, 0.07218, 47, -83.38, -133.88, 0.067, 1, 3, 209.84, -86.44, 1, 1, 3, 186.08, -137.85, 1, 1, 3, 124.29, -117.74, 1, 2, 2, 567.55, -58.48, 0.0005, 3, 52.14, -94.26, 0.9995, 2, 2, 494.94, -64.81, 0.24075, 3, -17.17, -71.7, 0.75925, 2, 2, 420.64, -71.28, 0.92373, 3, -88.09, -48.62, 0.07627, 1, 2, 344.38, -77.93, 1, 1, 2, 264.93, -84.86, 1, 1, 2, 199.8, -90.54, 1, 1, 2, 135.09, -96.18, 1, 1, 2, 82.63, -82.76, 1, 2, 4, -83.43, 64.55, 0.01485, 2, 27.35, -68.63, 0.98515, 2, 4, -18.02, 58.74, 0.53198, 2, -36.26, -52.37, 0.46802, 2, 4, 42.72, 53.35, 0.98727, 2, -95.34, -37.26, 0.01273, 2, 5, -78.19, 72.54, 0.13018, 4, 93.38, 48.85, 0.86982, 2, 5, -39.57, 69.11, 0.62954, 4, 132, 45.43, 0.37046, 1, 5, 1.37, 65.47, 1, 1, 5, 27.95, 56.13, 1, 1, 6, -9.84, 136.28, 1, 1, 6, 3.3, 111.75, 1, 1, 6, 22.68, 75.57, 1, 1, 6, 73.53, -19.36, 1, 1, 6, 16.65, 43.65, 1, 2, 6, 11.36, -28.02, 0.99851, 4, 301.54, -138.21, 0.00149, 2, 6, -18.42, 50.76, 0.84533, 5, 83.05, -44.58, 0.15467, 3, 6, -32.82, -16.15, 0.91023, 5, 84.25, -113.01, 0.07319, 4, 255.82, -136.69, 0.01658, 2, 6, -38.05, 81.31, 0.09005, 5, 56.99, -19.29, 0.90995, 3, 6, -63.9, -11.63, 0.60497, 5, 52.95, -115.67, 0.3564, 4, 224.52, -139.35, 0.03862, 1, 5, 21.13, -1.6, 1, 3, 6, -96.56, -2.11, 0.28002, 5, 18.98, -113.83, 0.66692, 4, 190.55, -137.51, 0.05306, 1, 5, -8.75, 3.41, 1, 4, 6, -125.35, 16.61, 0.24571, 5, -13.31, -102.14, 0.62649, 4, 158.26, -125.83, 0.12691, 2, -180.51, 158.19, 0.0009, 2, 5, -37.84, 18.16, 0.77906, 4, 133.73, -5.52, 0.22094, 4, 6, -156.54, 24.89, 0.20642, 5, -45.57, -101.17, 0.53515, 4, 126, -124.85, 0.25133, 2, -148.83, 152.03, 0.0071, 3, 6, -164.94, 151.62, 0.00111, 5, -82.56, 20.32, 0.08745, 4, 89.01, -3.36, 0.91145, 4, 6, -202.54, 34.82, 0.17245, 5, -92.63, -101.96, 0.22842, 4, 78.95, -125.64, 0.54516, 2, -102.26, 145.23, 0.05396, 4, 6, -220.61, 161.55, 0.0008, 5, -139.04, 17.34, 0.00128, 4, 32.54, -6.34, 0.99705, 2, -75.67, 20.02, 0.00087, 4, 6, -254.69, 42.76, 0.09026, 5, -145.21, -106.09, 0.10922, 4, 26.36, -129.77, 0.6045, 2, -49.7, 140.84, 0.19602, 3, 6, -271.6, 174.17, 0.00035, 4, -19.99, -5.64, 0.58474, 2, -23.95, 10.86, 0.41492, 5, 6, -301.27, 66.13, 0.03163, 5, -195.89, -93.92, 0.03437, 4, -24.31, -117.6, 0.46752, 2, -1.65, 120.66, 0.46572, 45, -373.2, 122.5, 0.00076, 1, 2, 39.44, -5.71, 1, 5, 6, -348.77, 93.36, 0.00638, 5, -248.33, -78.2, 0.00459, 4, -76.76, -101.89, 0.19648, 2, 47.59, 96.71, 0.78725, 45, -336.02, 82.31, 0.0053, 1, 2, 93.94, -13.92, 1, 5, 6, -400.71, 102.87, 0.00057, 5, -301.08, -80.75, 9e-05, 4, -129.51, -104.43, 0.05082, 2, 100.05, 90.72, 0.92824, 45, -289.31, 57.69, 0.02028, 1, 2, 145.29, -18.8, 1, 3, 4, -184.22, -112.02, 0.00711, 2, 155.27, 89.4, 0.93474, 45, -238.33, 36.42, 0.05815, 1, 2, 204.8, -18.7, 1, 2, 2, 209.54, 91.32, 0.86275, 45, -187.07, 18.52, 0.13725, 1, 2, 267.29, -24.05, 1, 2, 2, 270.71, 104.66, 0.66902, 45, -125.22, 8.74, 0.33098, 1, 2, 341.93, -27.27, 1, 2, 2, 341.35, 119.72, 0.30957, 45, -53.94, -2.86, 0.69043, 2, 2, 412.36, -27.64, 0.9883, 3, -78.66, -5.21, 0.0117, 3, 2, 407.97, 142.41, 0.0102, 3, -16.22, 153.03, 0.00429, 45, 16.38, -5.9, 0.98551, 2, 2, 490.85, -27.83, 0.17811, 3, -6.48, -36.07, 0.82189, 1, 45, 99.64, 0.47, 1, 1, 3, 78.7, -35.99, 1, 2, 3, 143.64, 153.46, 0.00681, 46, 32.49, -3.27, 0.99319, 1, 3, 161, -36.32, 1, 3, 3, 218.36, 150.86, 0.03863, 46, 106.91, -10.46, 0.73012, 47, -36.67, -5.38, 0.23125], "hull": 49, "edges": [0, 96, 18, 20, 30, 32, 36, 38, 44, 46, 54, 56, 94, 96, 94, 98, 98, 100, 0, 2, 100, 2, 90, 92, 92, 94, 92, 102, 102, 104, 2, 4, 4, 6, 104, 4, 90, 106, 106, 108, 108, 6, 86, 88, 88, 90, 88, 110, 110, 112, 6, 8, 8, 10, 112, 8, 86, 114, 114, 116, 116, 10, 84, 86, 84, 118, 118, 120, 10, 12, 120, 12, 82, 84, 82, 122, 122, 124, 12, 14, 124, 14, 80, 82, 80, 126, 126, 128, 14, 16, 16, 18, 128, 16, 78, 80, 78, 130, 130, 132, 132, 18, 76, 78, 76, 134, 134, 136, 136, 20, 72, 74, 74, 76, 74, 138, 138, 140, 20, 22, 140, 22, 72, 142, 142, 144, 22, 24, 144, 24, 70, 72, 70, 146, 146, 148, 24, 26, 148, 26, 68, 70, 68, 150, 150, 152, 26, 28, 28, 30, 152, 28, 66, 68, 66, 154, 154, 156, 156, 30, 64, 66, 64, 158, 158, 160, 160, 32, 62, 64, 62, 162, 162, 164, 32, 34, 34, 36, 164, 34, 60, 62, 60, 166, 166, 168, 168, 36, 56, 58, 58, 60, 58, 170, 170, 172, 38, 40, 172, 40, 52, 54, 50, 52, 40, 42, 42, 44, 46, 48, 48, 50], "width": 713, "height": 1098}}, "s5": {"s5": {"type": "mesh", "uvs": [0.55939, 0.06966, 0.72091, 0.12412, 0.89318, 0.43725, 1, 0.63143, 1, 0.7, 0.89045, 0.79739, 0.74173, 0.92958, 0.66252, 1, 0.62908, 1, 0.48307, 0.98385, 0.33167, 0.9671, 0.1947, 0.95195, 0.07095, 0.93826, 0.03676, 0.66595, 0, 0.37313, 0, 0.15445, 0.1213, 0.11076, 0.25021, 0.06433, 0.39205, 0.01324, 0.14981, 0.33537, 0.17928, 0.62515, 0.29947, 0.33414, 0.32193, 0.68256, 0.4536, 0.40993, 0.48299, 0.75105, 0.58493, 0.3518, 0.60976, 0.65989, 0.75727, 0.55783, 0.88613, 0.60051, 0.74399, 0.39946], "triangles": [6, 7, 26, 26, 7, 8, 10, 24, 9, 26, 8, 24, 8, 9, 24, 11, 22, 10, 10, 22, 24, 5, 27, 28, 5, 6, 27, 6, 26, 27, 5, 28, 4, 26, 24, 23, 28, 3, 4, 24, 22, 23, 22, 21, 23, 23, 25, 26, 26, 29, 27, 26, 25, 29, 28, 2, 3, 28, 27, 2, 27, 29, 2, 23, 0, 25, 23, 18, 0, 25, 1, 29, 2, 29, 1, 25, 0, 1, 12, 20, 11, 11, 20, 22, 12, 13, 20, 20, 21, 22, 13, 19, 20, 13, 14, 19, 20, 19, 21, 21, 18, 23, 14, 15, 19, 15, 16, 19, 19, 17, 21, 19, 16, 17, 21, 17, 18], "vertices": [2, 31, 39.42, 22.87, 0.31978, 32, 8.34, 28.51, 0.68022, 2, 31, 53.17, 19.32, 0.10716, 32, 22.52, 27.75, 0.89284, 2, 31, 67.22, 1.77, 0.00293, 32, 39.77, 13.33, 0.99707, 1, 32, 50.47, 4.39, 1, 1, 32, 51.04, 0.73, 1, 1, 32, 42.54, -5.92, 1, 1, 32, 31.01, -14.95, 1, 1, 32, 24.87, -19.76, 1, 1, 32, 22.03, -20.2, 1, 2, 31, 30.67, -26.15, 0.00619, 32, 9.49, -21.28, 0.99381, 2, 31, 17.7, -24.67, 0.20331, 32, -3.52, -22.4, 0.79669, 2, 31, 5.97, -23.33, 0.61625, 32, -15.28, -23.41, 0.38375, 2, 31, -4.63, -22.12, 0.81296, 32, -25.91, -24.33, 0.18704, 2, 31, -6.91, -7.3, 0.9708, 32, -31.09, -10.25, 0.0292, 1, 31, -9.37, 8.64, 1, 1, 31, -8.84, 20.44, 1, 2, 31, 1.68, 22.33, 0.99778, 32, -28.54, 20.49, 0.00222, 2, 31, 12.87, 24.34, 0.91858, 32, -17.97, 24.68, 0.08142, 2, 31, 25.18, 26.56, 0.68008, 32, -6.35, 29.29, 0.31992, 1, 31, 3.59, 10.11, 1, 2, 31, 5.43, -5.64, 0.934, 32, -19.32, -6.18, 0.066, 2, 31, 16.45, 9.6, 0.89945, 32, -11.54, 10.94, 0.10055, 2, 31, 17.55, -9.28, 0.2719, 32, -6.72, -7.35, 0.7281, 2, 31, 29.51, 4.92, 0.27274, 32, 2.19, 8.95, 0.72726, 1, 32, 7.53, -8.86, 1, 2, 31, 40.94, 7.56, 0.13252, 32, 12.86, 13.79, 0.86748, 1, 32, 17.55, -2.31, 1, 2, 31, 55.25, -4.22, 0.00334, 32, 29.23, 5.09, 0.99666, 2, 31, 66.22, -7.01, 2e-05, 32, 40.53, 4.53, 0.99998, 2, 31, 54.49, 4.38, 0.02704, 32, 26.78, 13.37, 0.97296], "hull": 19, "edges": [6, 8, 14, 16, 28, 30, 30, 32, 32, 38, 38, 40, 22, 24, 40, 22, 32, 34, 34, 36, 34, 42, 42, 44, 20, 22, 44, 20, 36, 46, 46, 48, 16, 18, 18, 20, 48, 18, 2, 0, 0, 36, 0, 50, 50, 52, 52, 16, 12, 14, 54, 12, 2, 4, 4, 6, 4, 56, 8, 10, 10, 12, 56, 10, 2, 58, 58, 54, 24, 26, 26, 28], "width": 86, "height": 54}}, "s6": {"s6": {"type": "mesh", "uvs": [0.7204, 0.17367, 0.90271, 0.27248, 1, 0.32521, 1, 0.40582, 0.90404, 0.78762, 0.85308, 0.99037, 0.65551, 0.93994, 0.49798, 0.89973, 0.29411, 0.84769, 0.16284, 0.81419, 0, 0.77262, 0, 0.72283, 0.14106, 0.06332, 0.34836, 0.06321, 0.51645, 0.06313, 0.27874, 0.29532, 0.23179, 0.52684, 0.44389, 0.32446, 0.37103, 0.56732, 0.65436, 0.37303, 0.56208, 0.62722, 0.8357, 0.42808, 0.73531, 0.6677], "triangles": [15, 12, 13, 17, 13, 14, 17, 14, 0, 15, 13, 17, 19, 17, 0, 19, 0, 1, 1, 2, 3, 21, 19, 1, 21, 1, 3, 16, 12, 15, 18, 15, 17, 16, 15, 18, 20, 17, 19, 18, 17, 20, 22, 19, 21, 20, 19, 22, 11, 12, 16, 9, 10, 11, 4, 21, 3, 22, 21, 4, 16, 9, 11, 9, 16, 18, 8, 9, 18, 8, 18, 20, 7, 8, 20, 6, 20, 22, 6, 22, 4, 7, 20, 6, 5, 6, 4], "vertices": [1, 25, 16.22, -23.27, 1, 1, 25, 3.26, -21.59, 1, 1, 25, -3.65, -20.7, 1, 1, 25, -5.48, -15.96, 1, 1, 25, -8.5, 8.65, 1, 1, 25, -10.11, 21.73, 1, 1, 25, 2.65, 23.24, 1, 1, 25, 12.82, 24.45, 1, 1, 25, 25.98, 26.02, 1, 1, 25, 34.46, 27.03, 1, 1, 25, 44.97, 28.28, 1, 1, 25, 46.1, 25.35, 1, 1, 25, 52.77, -16.61, 1, 1, 25, 40.59, -21.32, 1, 1, 25, 30.71, -25.14, 1, 1, 25, 39.42, -6.1, 1, 1, 25, 36.92, 8.58, 1, 1, 25, 29.05, -8.13, 1, 1, 25, 27.82, 7.8, 1, 1, 25, 15.58, -10.05, 1, 1, 25, 15.24, 6.98, 1, 1, 25, 3.67, -10.93, 1, 1, 25, 4.14, 5.43, 1], "hull": 15, "edges": [4, 6, 20, 22, 22, 24, 24, 26, 26, 28, 26, 30, 30, 32, 18, 20, 32, 18, 28, 34, 34, 36, 16, 18, 36, 16, 0, 28, 0, 38, 38, 40, 14, 16, 40, 14, 0, 2, 2, 4, 2, 42, 42, 44, 10, 12, 12, 14, 44, 12, 6, 8, 8, 10], "width": 63, "height": 63}}, "s7": {"s7": {"type": "mesh", "uvs": [0.50912, 0.19254, 0.68368, 0.25617, 0.87366, 0.32541, 1, 0.37146, 1, 0.48007, 0.93751, 0.77173, 0.88871, 0.9995, 0.71961, 0.99114, 0.55098, 0.98281, 0.3541, 0.97308, 0.18466, 0.96471, 0.13324, 0.7056, 0.06418, 0.35767, 0, 0.03428, 0.08635, 0, 0.20404, 0.06347, 0.30628, 0.11861, 0.77074, 0.53746, 0.74074, 0.79369, 0.60574, 0.51999, 0.57274, 0.83446, 0.37774, 0.46175, 0.35974, 0.86649, 0.21874, 0.38022, 0.13774, 0.20551], "triangles": [8, 22, 20, 9, 22, 8, 9, 10, 22, 10, 11, 22, 20, 22, 21, 22, 11, 21, 11, 23, 21, 11, 12, 23, 21, 0, 19, 21, 23, 0, 12, 24, 23, 23, 16, 0, 23, 24, 16, 12, 13, 24, 13, 14, 24, 24, 15, 16, 24, 14, 15, 20, 21, 19, 19, 0, 1, 5, 17, 4, 6, 18, 5, 7, 18, 6, 8, 20, 7, 7, 20, 18, 20, 19, 18, 18, 17, 5, 18, 19, 17, 17, 2, 4, 2, 17, 1, 17, 19, 1, 2, 3, 4], "vertices": [1, 27, 12.58, -17.26, 1, 2, 26, 29.88, -17.18, 0.52085, 27, -3.21, -17.93, 0.47915, 2, 26, 13.44, -18.58, 0.98261, 27, -19.7, -18.59, 0.01739, 1, 26, 4.44, -19.25, 1, 1, 26, 1.34, -12.55, 1, 2, 26, -3.24, 7.18, 0.99945, 27, -35.21, 7.9, 0.00055, 2, 26, -6.74, 22.6, 0.98709, 27, -38.01, 23.46, 0.01291, 2, 26, 4.24, 26.85, 0.89514, 27, -26.85, 27.22, 0.10486, 2, 26, 17.67, 31.42, 0.43286, 27, -13.23, 31.17, 0.56714, 1, 27, 1.96, 35.73, 1, 1, 27, 12.52, 39.44, 1, 1, 27, 22.34, 24.42, 1, 1, 27, 35.51, 4.25, 1, 1, 27, 47.76, -14.5, 1, 1, 27, 43.37, -18.82, 1, 1, 27, 34.54, -17.76, 1, 1, 27, 26.88, -16.85, 1, 2, 26, 14.12, -2.57, 0.89706, 27, -18.3, -2.62, 0.10294, 2, 26, 8.61, 14.08, 0.89619, 27, -23.06, 14.26, 0.10381, 2, 26, 26.79, 1.23, 0.55474, 27, -5.47, 0.61, 0.44526, 2, 26, 20.45, 21.64, 0.4563, 27, -10.89, 21.28, 0.5437, 1, 27, 13.68, 2.96, 1, 1, 27, 4.36, 28.88, 1, 1, 27, 25.49, 1.8, 1, 1, 27, 34.94, -7.17, 1], "hull": 17, "edges": [6, 8, 26, 28, 6, 4, 4, 34, 34, 36, 12, 14, 36, 14, 8, 10, 10, 12, 4, 2, 2, 38, 38, 40, 14, 16, 40, 16, 2, 0, 0, 32, 0, 42, 42, 44, 16, 18, 18, 20, 44, 18, 32, 46, 20, 22, 46, 22, 28, 30, 30, 32, 30, 48, 22, 24, 24, 26, 48, 24], "width": 66, "height": 68}}, "ps": {"ps": {"type": "mesh", "uvs": [0, 0, 0.03336, 0, 0.21789, 0.04073, 0.194, 0.08412, 0.16793, 0.13148, 0.14257, 0.17754, 0.12286, 0.21333, 0.09655, 0.26113, 0.07725, 0.29618, 0.05596, 0.33485, 0.07517, 0.38638, 0.09286, 0.43386, 0.11126, 0.48323, 0.12422, 0.518, 0.2242, 0.54148, 0.28499, 0.55576, 0.38422, 0.57907, 0.41602, 0.64115, 0.43444, 0.6771, 0.44697, 0.70156, 0.46674, 0.74017, 0.48994, 0.78545, 0.5518, 0.80806, 0.60653, 0.82805, 0.64666, 0.84272, 0.67794, 0.85415, 0.77775, 0.86302, 0.86725, 0.87097, 0.9375, 0.87721, 1, 0.88277, 1, 0.8955, 0.9014, 0.9389, 0.82984, 0.9704, 0.76259, 1, 0.69349, 1, 0.64773, 0.9816, 0.59062, 0.95863, 0.51967, 0.9301, 0.49452, 0.90518, 0.4663, 0.87723, 0.43334, 0.84459, 0.39286, 0.80449, 0.35024, 0.76228, 0.35802, 0.72319, 0.32573, 0.70109, 0.28741, 0.67486, 0.23142, 0.63654, 0.1897, 0.60799, 0.12728, 0.56527, 0.07618, 0.5303, 0.02573, 0.49577, 0.02303, 0.44832, 0.01966, 0.3893, 0.01661, 0.33577, 0.01399, 0.28992, 0.01192, 0.25355, 0.00923, 0.20651, 0.00659, 0.1602, 0.00312, 0.09941, 0, 0.04469, 0.09672, 0.07454, 0.07811, 0.11418, 0.07114, 0.16666, 0.07114, 0.21272, 0.04542, 0.38891, 0.0551, 0.4413, 0.07285, 0.48886, 0.16887, 0.55908, 0.21994, 0.59821, 0.28126, 0.62644, 0.35953, 0.60861, 0.34823, 0.65866, 0.37889, 0.69046, 0.39422, 0.71684, 0.41036, 0.75201, 0.4386, 0.79509, 0.48976, 0.83194, 0.53817, 0.85126, 0.59062, 0.86724, 0.635, 0.89548, 0.67543, 0.94192, 0.7537, 0.89807, 0.74079, 0.96235, 0.82067, 0.91145], "triangles": [34, 82, 33, 33, 82, 32, 34, 35, 82, 35, 80, 82, 35, 36, 80, 31, 32, 83, 32, 82, 83, 82, 81, 83, 82, 80, 81, 80, 36, 79, 36, 37, 79, 80, 79, 81, 31, 83, 28, 31, 28, 30, 28, 83, 27, 81, 26, 83, 83, 26, 27, 79, 25, 81, 81, 25, 26, 28, 29, 30, 37, 38, 79, 38, 78, 79, 38, 77, 78, 38, 39, 77, 25, 78, 24, 25, 79, 78, 39, 76, 77, 39, 40, 76, 78, 23, 24, 78, 77, 23, 77, 22, 23, 77, 76, 22, 76, 21, 22, 40, 75, 76, 40, 41, 75, 76, 75, 21, 41, 42, 75, 42, 74, 75, 75, 74, 21, 74, 20, 21, 42, 43, 74, 74, 73, 20, 43, 73, 74, 73, 19, 20, 73, 43, 72, 43, 44, 72, 73, 72, 19, 72, 18, 19, 72, 44, 71, 44, 45, 71, 72, 71, 18, 71, 17, 18, 45, 69, 71, 71, 70, 17, 46, 69, 45, 71, 69, 70, 70, 16, 17, 47, 68, 46, 46, 68, 69, 69, 68, 70, 68, 15, 70, 70, 15, 16, 48, 67, 47, 47, 67, 68, 68, 14, 15, 68, 67, 14, 67, 48, 13, 67, 13, 14, 48, 49, 13, 50, 66, 49, 49, 66, 13, 66, 12, 13, 50, 51, 66, 51, 65, 66, 66, 65, 12, 65, 11, 12, 65, 52, 64, 65, 51, 52, 65, 64, 11, 64, 10, 11, 52, 53, 64, 64, 9, 10, 64, 53, 9, 53, 54, 9, 9, 54, 8, 8, 54, 7, 54, 55, 7, 55, 63, 7, 7, 63, 6, 55, 56, 63, 6, 63, 5, 56, 62, 63, 63, 62, 5, 56, 57, 62, 5, 62, 4, 62, 61, 4, 62, 57, 61, 57, 58, 61, 4, 61, 3, 3, 61, 60, 61, 58, 60, 58, 59, 60, 3, 60, 2, 2, 60, 1, 60, 59, 1, 59, 0, 1], "vertices": [1, 48, -16.54, -20.08, 1, 1, 48, -17.09, -12.33, 1, 2, 48, 0.46, 32, 1, 50, -163.62, 84.4, 0, 2, 48, 22.75, 27.99, 1, 50, -143.66, 73.7, 0, 2, 48, 47.08, 23.61, 1, 50, -121.86, 62.03, 0, 2, 48, 70.74, 19.35, 1, 50, -100.67, 50.68, 0, 3, 48, 89.13, 16.04, 0.85589, 49, -5.75, 16.79, 0.14411, 50, -84.2, 41.86, 0, 3, 48, 113.69, 11.62, 0.0033, 49, 18.61, 11.42, 0.9967, 50, -62.2, 30.08, 0, 2, 49, 36.48, 7.48, 1, 50, -46.07, 21.44, 0, 2, 49, 56.19, 3.13, 1, 50, -28.28, 11.91, 0, 2, 49, 82.12, 8.42, 0.43213, 50, -1.89, 9.97, 0.56787, 1, 50, 22.41, 8.18, 1, 1, 50, 47.69, 6.32, 1, 2, 50, 65.49, 5.01, 0.97573, 51, -11.46, 9.55, 0.02427, 2, 50, 82.64, 24.76, 0.02398, 51, 12.92, 19.01, 0.97602, 1, 51, 27.74, 24.75, 1, 2, 51, 51.95, 34.13, 0.89042, 52, -26.65, 27.73, 0.10958, 2, 51, 80.31, 18.73, 0.1453, 52, 5.12, 22.04, 0.8547, 1, 52, 23.52, 18.75, 1, 2, 52, 36.03, 16.5, 0.95326, 53, -18.09, 14.88, 0.04674, 2, 52, 55.79, 12.96, 0.16035, 53, 1.85, 12.56, 0.83965, 2, 53, 25.23, 9.83, 0.94069, 54, -14.73, 19.01, 0.05931, 2, 53, 40.9, 19.48, 0.26874, 54, 3.52, 16.62, 0.73126, 2, 53, 54.75, 28.02, 0.00427, 54, 19.66, 14.51, 0.99573, 2, 54, 31.49, 12.96, 0.999, 55, -10.43, 21.18, 0.001, 2, 54, 40.72, 11.75, 0.75275, 55, -2.47, 16.36, 0.24725, 2, 54, 62.97, 19.86, 0.01454, 55, 21.16, 14.82, 0.98546, 1, 55, 42.35, 13.44, 1, 1, 55, 58.99, 12.36, 1, 1, 55, 73.79, 11.39, 1, 1, 55, 74.59, 5, 1, 1, 55, 54.55, -19.66, 1, 1, 55, 40.01, -37.57, 1, 1, 55, 26.34, -54.39, 1, 2, 54, 81.8, -49.67, 0.00324, 55, 10.37, -56.41, 0.99676, 2, 54, 67.86, -47.18, 0.03642, 55, -1.38, -48.51, 0.96358, 2, 54, 50.47, -44.06, 0.21037, 55, -16.04, -38.64, 0.78963, 2, 54, 28.87, -40.18, 0.59254, 55, -34.24, -26.39, 0.40746, 2, 54, 17.36, -32.39, 0.80303, 55, -41.64, -14.62, 0.19697, 3, 53, 67.01, -11.18, 0.04138, 54, 4.44, -23.64, 0.91452, 55, -49.94, -1.41, 0.0441, 3, 53, 48.87, -12.77, 0.56905, 54, -10.64, -13.43, 0.43036, 55, -59.62, 14.01, 0.00058, 1, 53, 26.58, -14.72, 1, 2, 52, 55.26, -16.39, 0.19166, 53, 3.11, -16.78, 0.80834, 1, 52, 37.84, -6.86, 1, 1, 52, 24.59, -9.32, 1, 2, 51, 73.06, -14.98, 0.06706, 52, 8.86, -12.24, 0.93294, 2, 51, 49.91, -11.79, 0.97571, 52, -14.12, -16.5, 0.02429, 1, 51, 32.66, -9.41, 1, 1, 51, 6.85, -5.85, 1, 1, 50, 68.84, -7.35, 1, 1, 50, 49.05, -14.55, 1, 1, 50, 25.59, -9.37, 1, 2, 49, 84, -4.46, 0.2038, 50, -3.58, -2.94, 0.7962, 2, 49, 56.95, -6.02, 1, 50, -30.04, 2.9, 0, 1, 49, 33.77, -7.35, 1, 1, 49, 15.4, -8.41, 1, 2, 48, 87.54, -10.61, 0.82874, 49, -8.38, -9.78, 0.17126, 1, 48, 64.21, -12.87, 1, 1, 48, 33.58, -15.83, 1, 1, 48, 6.02, -18.5, 1, 2, 48, 19.5, 5.04, 1, 50, -153.82, 52.87, 0, 2, 48, 39.81, 2.12, 1, 50, -135.41, 43.83, 0, 2, 48, 66.42, 2.36, 1, 50, -110.02, 35.85, 0, 3, 48, 89.67, 3.99, 0.89716, 49, -5.69, 4.74, 0.10284, 50, -87.4, 30.24, 0, 2, 49, 83.61, 1.53, 0.31739, 50, -2.32, 2.94, 0.68261, 1, 50, 23.95, -1.26, 1, 1, 50, 48.3, -3.05, 1, 2, 50, 88.18, 10.1, 0.0102, 51, 10.97, 3.46, 0.9898, 1, 51, 33.66, -0.86, 1, 1, 51, 53.84, 0.27, 1, 2, 51, 59.26, 19.88, 0.73651, 52, -15.22, 16.51, 0.26349, 1, 52, 6.97, 4.03, 1, 1, 52, 24.58, 4.18, 1, 2, 52, 38.25, 2.15, 0.99023, 53, -15.01, 0.69, 0.00977, 1, 53, 3, -1.84, 1, 1, 53, 25.74, -3.08, 1, 3, 53, 47.33, 1.77, 0.21644, 54, -2.66, -1.18, 0.78353, 55, -47.38, 22.01, 3e-05, 2, 54, 12.04, -3.76, 0.99468, 55, -34.97, 13.72, 0.00532, 2, 54, 26.68, -4.4, 0.97064, 55, -21.83, 7.23, 0.02936, 2, 54, 42.9, -11.34, 0.44913, 55, -9.78, -5.65, 0.55087, 2, 54, 63.07, -26.64, 0.04372, 55, 2.51, -27.78, 0.95628, 1, 55, 17.82, -3.48, 1, 2, 54, 81.45, -27.67, 0.00011, 55, 18.91, -36.13, 0.99989, 1, 55, 34.15, -8.24, 1], "hull": 60, "edges": [0, 118, 0, 2, 2, 4, 58, 60, 66, 68, 84, 86, 118, 120, 4, 6, 120, 6, 116, 118, 116, 122, 6, 8, 122, 8, 114, 116, 114, 124, 8, 10, 124, 10, 112, 114, 112, 126, 10, 12, 126, 12, 110, 112, 12, 14, 110, 14, 108, 110, 14, 16, 16, 18, 108, 16, 106, 108, 106, 18, 104, 106, 104, 128, 18, 20, 128, 20, 100, 102, 102, 104, 102, 130, 20, 22, 130, 22, 100, 132, 22, 24, 24, 26, 132, 24, 98, 100, 98, 26, 96, 98, 96, 134, 26, 28, 134, 28, 94, 96, 94, 136, 28, 30, 30, 32, 136, 30, 92, 94, 92, 138, 138, 140, 140, 32, 90, 92, 90, 142, 32, 34, 142, 34, 86, 88, 88, 90, 88, 144, 34, 36, 144, 36, 86, 146, 36, 38, 146, 38, 84, 148, 38, 40, 40, 42, 148, 40, 82, 84, 82, 150, 150, 42, 80, 82, 80, 152, 42, 44, 152, 44, 78, 80, 78, 154, 44, 46, 154, 46, 74, 76, 76, 78, 76, 156, 46, 48, 48, 50, 156, 48, 74, 158, 158, 50, 72, 74, 72, 160, 160, 162, 50, 52, 162, 52, 68, 70, 70, 72, 70, 164, 164, 166, 52, 54, 166, 54, 64, 66, 60, 62, 62, 64, 54, 56, 56, 58], "width": 233, "height": 506}}, "s4": {"s4": {"type": "mesh", "uvs": [0.92502, 0.09447, 0.99041, 0.76331, 0.9208, 0.79639, 0.78335, 0.8617, 0.63802, 0.93076, 0.49231, 1, 0.31149, 1, 0.19587, 0.81266, 0.0885, 0.63867, 0, 0.49527, 0, 0.46622, 0.20741, 0.26856, 0.39355, 0.19233, 0.56986, 0.12012, 0.70869, 0.06326, 0.83828, 0.01019, 0.33523, 0.53694, 0.39912, 0.73732, 0.4914, 0.47421, 0.56523, 0.67459, 0.65893, 0.39581, 0.72282, 0.61884, 0.82646, 0.34528, 0.87048, 0.57702], "triangles": [0, 22, 14, 0, 14, 15, 20, 13, 14, 20, 14, 22, 18, 12, 13, 18, 13, 20, 10, 8, 9, 16, 11, 12, 16, 12, 18, 11, 8, 10, 23, 22, 0, 1, 23, 0, 21, 20, 22, 23, 21, 22, 19, 18, 20, 11, 16, 8, 21, 19, 20, 17, 16, 18, 17, 18, 19, 2, 23, 1, 7, 8, 16, 7, 16, 17, 2, 3, 21, 2, 21, 23, 3, 4, 19, 3, 19, 21, 6, 7, 17, 5, 17, 19, 5, 19, 4, 6, 17, 5], "vertices": [1, 30, 70.14, 21.59, 1, 1, 30, 62.73, -22.25, 1, 1, 30, 56.71, -22.74, 1, 1, 30, 44.81, -23.73, 1, 1, 30, 32.23, -24.77, 1, 1, 30, 19.62, -25.81, 1, 1, 30, 5.57, -21.67, 1, 1, 30, 0.08, -7.16, 1, 1, 30, -5.01, 6.31, 1, 1, 30, -9.21, 17.42, 1, 1, 30, -8.66, 19.26, 1, 1, 30, 11.14, 27.02, 1, 1, 30, 27.02, 27.58, 1, 1, 30, 42.07, 28.11, 1, 1, 30, 53.92, 28.53, 1, 1, 30, 64.98, 28.92, 1, 1, 30, 16.06, 7.1, 1, 1, 30, 17.28, -7.05, 1, 1, 30, 29.36, 7.49, 1, 1, 30, 31.36, -6.89, 1, 1, 30, 43.84, 8.62, 1, 1, 30, 44.64, -6.97, 1, 1, 30, 57.8, 7.98, 1, 1, 30, 56.9, -7.7, 1], "hull": 16, "edges": [0, 30, 0, 2, 10, 12, 18, 20, 20, 22, 22, 32, 32, 34, 34, 10, 22, 24, 24, 36, 36, 38, 8, 10, 38, 8, 24, 26, 26, 40, 40, 42, 6, 8, 42, 6, 26, 28, 28, 30, 28, 44, 44, 46, 2, 4, 4, 6, 46, 4, 12, 14, 14, 16, 16, 18], "width": 81, "height": 66}}, "rrrpng": {"rrrpng": {"type": "mesh", "uvs": [0.79694, 0, 0.80704, 0, 0.81193, 0.03457, 0.8516, 0.03477, 0.91162, 0.029, 0.93812, 0.07057, 0.87719, 0.11891, 0.95935, 0.14754, 1, 0.15682, 1, 0.16545, 0.98853, 0.19439, 0.94657, 0.22437, 0.84357, 0.26392, 0.78379, 0.24922, 0.70089, 0.25018, 0.55346, 0.3236, 0.50984, 0.32373, 0.49854, 0.32809, 0.45328, 0.36683, 0.42551, 0.40225, 0.3492, 0.5064, 0.30115, 0.61958, 0.21079, 0.86397, 0.17711, 0.92176, 0.13511, 0.9751, 0.07772, 1, 0.04372, 1, 0.02021, 0.94997, 0, 0.84682, 0, 0.77583, 0.00066, 0.73407, 0.03098, 0.65539, 0.06723, 0.59011, 0.19287, 0.43813, 0.23883, 0.38859, 0.21688, 0.35806, 0.24472, 0.37677, 0.26094, 0.35012, 0.32106, 0.27276, 0.33376, 0.2531, 0.36084, 0.19653, 0.412, 0.13067, 0.42666, 0.11986, 0.48618, 0.10442, 0.50334, 0.06453, 0.57989, 0.06273, 0.66333, 0.03122, 0.62405, 0.01824, 0.63963, 0.00817, 0.65714, 0.00982, 0.6842, 0.03472, 0.81072, 0.03472], "triangles": [12, 6, 11, 12, 13, 6, 6, 2, 3, 51, 2, 6, 6, 13, 51, 11, 7, 10, 11, 6, 7, 10, 7, 9, 7, 8, 9, 6, 4, 5, 6, 3, 4, 14, 50, 13, 13, 50, 51, 0, 1, 51, 51, 1, 2, 16, 17, 43, 17, 40, 41, 17, 41, 42, 17, 42, 43, 16, 43, 15, 15, 45, 14, 14, 46, 50, 14, 45, 46, 15, 43, 45, 45, 43, 44, 47, 48, 46, 48, 49, 46, 50, 46, 49, 24, 25, 27, 25, 26, 27, 23, 24, 28, 24, 27, 28, 22, 23, 29, 31, 22, 29, 29, 30, 31, 31, 32, 22, 22, 32, 21, 29, 23, 28, 32, 33, 21, 21, 33, 20, 20, 33, 34, 34, 36, 20, 36, 37, 20, 20, 37, 19, 19, 37, 38, 38, 39, 19, 19, 39, 18, 34, 35, 36, 17, 18, 40, 40, 18, 39], "vertices": [2, 31, 33.2, 26.76, 0.62689, 32, 1.48, 31.08, 0.37311, 2, 31, 35.82, 26.66, 0.6257, 32, 4.06, 31.51, 0.3743, 2, 31, 36.82, 19.67, 0.57438, 32, 6.43, 24.85, 0.42562, 2, 31, 47.09, 19.24, 0.24797, 32, 16.57, 26.47, 0.75203, 2, 31, 62.66, 19.81, 0.06631, 32, 31.73, 30.12, 0.93369, 2, 31, 69.21, 11.2, 0.0498, 32, 39.85, 22.98, 0.9502, 2, 31, 53.07, 2.09, 0.04584, 32, 25.84, 10.84, 0.95416, 1, 32, 47.77, 8.59, 1, 1, 32, 58.46, 8.45, 1, 1, 32, 58.74, 6.74, 1, 1, 32, 56.74, 0.52, 1, 1, 32, 46.99, -7.18, 1, 2, 31, 43.27, -26.71, 0.00261, 32, 21.94, -19.33, 0.99739, 3, 30, 87.34, -30.04, 0.00954, 31, 27.9, -23.17, 0.22248, 32, 6.18, -18.9, 0.76799, 3, 30, 66.64, -24.34, 0.45108, 31, 6.44, -22.55, 0.44227, 32, -14.98, -22.55, 0.10665, 2, 29, 176.24, -38.52, 0.10797, 30, 25.87, -28.06, 0.89203, 2, 29, 169.39, -29.53, 0.34785, 30, 15, -24.99, 0.65215, 2, 29, 166.92, -27.73, 0.47529, 30, 11.94, -25.03, 0.52471, 2, 29, 153.63, -23.1, 0.92194, 30, -1.46, -29.31, 0.07806, 2, 29, 143.62, -21.68, 0.99776, 30, -10.33, -34.18, 0.00224, 1, 29, 114.99, -18.59, 1, 1, 29, 89.35, -22.44, 1, 1, 29, 36.07, -33.49, 1, 1, 29, 21.54, -33.57, 1, 1, 29, 6.43, -31.38, 1, 1, 29, -6.55, -22.57, 1, 1, 29, -11.87, -15.56, 1, 1, 29, -7.54, -4.63, 1, 1, 29, 5.81, 12.08, 1, 1, 29, 17.17, 20.7, 1, 1, 29, 23.96, 25.64, 1, 1, 29, 41.31, 28.95, 1, 1, 29, 57.44, 29.4, 1, 1, 29, 101.44, 21.95, 1, 1, 29, 116.57, 18.49, 1, 1, 29, 118.02, 26.73, 1, 1, 29, 119.39, 18.72, 1, 1, 29, 126.19, 18.61, 1, 2, 29, 147.99, 15.61, 0.99797, 30, -29.21, -1.74, 0.00203, 2, 29, 153.13, 15.37, 0.9831, 30, -24.97, 1.16, 0.0169, 2, 29, 166.43, 16.66, 0.76281, 30, -15.11, 10.18, 0.23719, 2, 29, 184.98, 14.11, 0.15191, 30, 1.27, 19.27, 0.84809, 2, 29, 189.01, 12.4, 0.08349, 30, 5.51, 20.32, 0.91651, 1, 30, 21.19, 19.08, 1, 1, 30, 27.66, 25.57, 1, 2, 30, 46.83, 20.49, 0.99733, 31, -23.45, 16.29, 0.00267, 2, 30, 69.35, 20.66, 0.61836, 31, -1.61, 21.8, 0.38164, 2, 30, 60.28, 25.95, 0.62989, 31, -11.68, 24.79, 0.37011, 2, 30, 64.71, 26.79, 0.6279, 31, -7.57, 26.66, 0.3721, 2, 30, 68.98, 25.23, 0.62409, 31, -3.05, 26.16, 0.37591, 2, 30, 74.35, 18.5, 0.47063, 31, 3.76, 20.89, 0.52937, 2, 31, 36.5, 19.65, 0.5827, 32, 6.12, 24.77, 0.4173], "hull": 52, "edges": [0, 102, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102], "width": 259, "height": 201}}, "t1": {"t1": {"type": "mesh", "uvs": [0.40484, 0, 0.49401, 0, 0.67115, 0.05175, 0.87129, 0.11022, 0.91448, 0.2422, 0.95915, 0.37875, 0.95471, 0.4606, 0.94932, 0.56008, 0.94558, 0.62906, 0.84191, 0.70384, 0.68395, 0.81779, 0.57072, 0.89947, 0.49512, 0.95401, 0.43136, 1, 0.30549, 1, 0.22671, 0.9091, 0.18707, 0.86336, 0.12513, 0.79188, 0.06451, 0.72193, 0, 0.6475, 0.00868, 0.48492, 0.01732, 0.32319, 0.025, 0.17948, 0.15713, 0.10392, 0.2543, 0.04834, 0.33884, 0, 0.27768, 0.16295, 0.24678, 0.3247, 0.25747, 0.39386, 0.39058, 0.35928, 0.5023, 0.43067, 0.63778, 0.57904, 0.81367, 0.62812, 0.11724, 0.41506, 0.06494, 0.52884, 0.05662, 0.60135, 0.13982, 0.2098, 0.35043, 0.06972, 0.13625, 0.30685, 0.52369, 0.08933, 0.71741, 0.1663, 0.86359, 0.35817, 0.48566, 0.1741, 0.66987, 0.27115, 0.82912, 0.43402, 0.44525, 0.27227, 0.61877, 0.40502, 0.79466, 0.51099, 0.79585, 0.14175, 0.08317, 0.63794, 0.13267, 0.60602, 0.19767, 0.60039, 0.26317, 0.64826, 0.26917, 0.69003, 0.20817, 0.71537, 0.13367, 0.70974, 0.23904, 0.50603, 0.36213, 0.47605, 0.088, 0.57161, 0.22174, 0.56474, 0.36945, 0.54725, 0.56663, 0.50112, 0.11522, 0.64799, 0.1645, 0.62874, 0.22073, 0.63836, 0.20651, 0.70418, 0.13374, 0.69176, 0.51248, 0.55612, 0.42807, 0.56776, 0.3861, 0.64162, 0.39373, 0.67117, 0.51296, 0.68684, 0.59117, 0.64028, 0.4171, 0.61163, 0.48577, 0.5794, 0.58211, 0.58835, 0.5783, 0.61521, 0.52631, 0.6649, 0.44428, 0.66804, 0.29561, 0.76862, 0.5053, 0.74507, 0.68209, 0.72046, 0.30761, 0.85066, 0.35803, 0.84312, 0.45084, 0.83831, 0.35292, 0.89593], "triangles": [8, 32, 7, 31, 47, 32, 32, 47, 7, 47, 31, 46, 7, 47, 6, 6, 47, 44, 47, 46, 44, 61, 30, 46, 33, 20, 38, 44, 41, 6, 6, 41, 5, 41, 44, 43, 46, 30, 45, 28, 38, 27, 28, 33, 38, 38, 20, 21, 30, 29, 45, 44, 46, 43, 46, 45, 43, 28, 27, 29, 41, 4, 5, 29, 27, 45, 4, 41, 40, 40, 41, 43, 4, 40, 48, 38, 36, 27, 27, 26, 45, 27, 36, 26, 38, 21, 36, 36, 21, 22, 45, 42, 43, 45, 26, 42, 43, 42, 40, 48, 3, 4, 36, 23, 26, 36, 22, 23, 26, 37, 42, 42, 39, 40, 42, 37, 39, 39, 2, 40, 40, 2, 48, 26, 23, 37, 48, 2, 3, 23, 24, 37, 37, 0, 39, 0, 1, 39, 39, 1, 2, 24, 25, 37, 37, 25, 0, 81, 80, 71, 81, 71, 72, 57, 28, 29, 71, 77, 72, 67, 61, 31, 76, 74, 75, 60, 57, 30, 56, 33, 28, 74, 67, 75, 77, 78, 74, 85, 83, 84, 84, 79, 80, 15, 82, 85, 15, 16, 82, 85, 82, 83, 82, 16, 79, 16, 17, 79, 82, 79, 83, 83, 79, 84, 17, 54, 79, 17, 55, 54, 17, 18, 55, 79, 70, 80, 54, 53, 79, 79, 53, 70, 18, 49, 55, 18, 19, 49, 55, 65, 54, 54, 65, 53, 55, 49, 66, 55, 66, 65, 66, 49, 62, 66, 63, 65, 65, 64, 53, 65, 63, 64, 66, 62, 63, 64, 52, 53, 53, 69, 70, 53, 52, 69, 70, 78, 71, 78, 70, 73, 70, 69, 73, 78, 73, 74, 52, 51, 59, 52, 60, 69, 52, 59, 60, 62, 50, 63, 62, 49, 50, 19, 35, 49, 19, 20, 35, 73, 69, 68, 63, 51, 64, 52, 64, 51, 49, 58, 50, 49, 35, 58, 63, 50, 51, 73, 68, 74, 68, 69, 60, 51, 50, 59, 35, 34, 58, 35, 20, 34, 59, 50, 58, 59, 58, 56, 59, 56, 60, 56, 58, 34, 56, 57, 60, 34, 33, 56, 34, 20, 33, 80, 70, 71, 74, 68, 67, 61, 68, 60, 71, 78, 77, 56, 28, 57, 67, 68, 61, 77, 74, 76, 75, 67, 31, 77, 76, 72, 76, 75, 31, 72, 76, 31, 60, 30, 61, 57, 29, 30, 84, 80, 10, 11, 85, 84, 72, 31, 81, 31, 61, 46, 12, 13, 85, 13, 14, 85, 12, 85, 11, 11, 84, 10, 80, 81, 10, 10, 81, 9, 81, 32, 9, 81, 31, 32, 9, 32, 8, 14, 15, 85], "vertices": [1, 56, 5.1, 55.8, 1, 1, 56, 26.15, 65.51, 1, 1, 56, 73.98, 71.77, 1, 1, 56, 128.02, 78.84, 1, 2, 7, 205.93, 17.38, 0.00014, 56, 153.52, 50.34, 0.99986, 2, 7, 181.12, -13.44, 0.054, 56, 179.9, 20.85, 0.946, 2, 7, 161.7, -25.2, 0.16357, 56, 188.34, -0.23, 0.83643, 2, 7, 138.09, -39.48, 0.36954, 56, 198.6, -25.84, 0.63046, 2, 7, 121.73, -49.39, 0.47632, 56, 205.72, -43.6, 0.52368, 2, 7, 89.46, -38.68, 0.70618, 56, 189.91, -73.69, 0.29382, 2, 7, 40.3, -22.36, 0.99924, 56, 165.82, -119.55, 0.00076, 1, 7, 5.06, -10.66, 1, 1, 7, -18.47, -2.86, 1, 1, 7, -38.31, 3.73, 1, 1, 7, -56.65, 30.83, 1, 2, 7, -47.28, 61.91, 0.50959, 8, -49.27, -13.6, 0.49041, 3, 7, -42.57, 77.54, 0.40344, 8, -44.56, 2.03, 0.59649, 56, 53.78, -185.09, 8e-05, 3, 7, -35.2, 101.98, 0.38252, 8, -37.19, 26.47, 0.60495, 56, 30.87, -173.85, 0.01253, 3, 7, -27.99, 125.9, 0.10558, 8, -29.98, 50.38, 0.84057, 56, 8.44, -162.85, 0.05385, 2, 8, -22.31, 75.83, 0.91458, 56, -15.42, -151.14, 0.08542, 2, 8, 16.25, 99.2, 0.60704, 56, -32.22, -109.3, 0.39296, 2, 8, 54.61, 122.46, 0.29669, 56, -48.93, -67.67, 0.70331, 2, 8, 88.69, 143.12, 0.12848, 56, -63.78, -30.69, 0.87152, 2, 8, 125.28, 126.4, 0.03316, 56, -41.35, 2.7, 0.96684, 2, 8, 152.2, 114.11, 0.00053, 56, -24.84, 27.26, 0.99947, 1, 56, -10.49, 48.62, 1, 2, 8, 129.31, 91.28, 0.00333, 56, -6.04, 0.97, 0.99667, 3, 7, 89.69, 148.33, 0.00161, 8, 87.71, 72.82, 0.19917, 56, 5.42, -43.08, 0.79922, 3, 7, 75.39, 135.29, 0.00519, 8, 73.4, 59.78, 0.52249, 56, 15.97, -59.32, 0.47232, 3, 7, 102.72, 112, 0.02012, 8, 100.73, 36.49, 0.24211, 56, 43.39, -36.13, 0.73776, 3, 7, 102.63, 76.86, 0.0655, 8, 100.64, 1.35, 0.49493, 56, 78.05, -41.93, 0.43956, 3, 7, 88.34, 24.65, 0.2073, 8, 86.35, -50.86, 0.65027, 56, 127.24, -64.51, 0.14242, 3, 7, 102.72, -20.84, 0.60386, 8, 100.73, -96.35, 7e-05, 56, 174.46, -57.72, 0.39607, 2, 8, 48.1, 86.68, 0.48984, 56, -14.69, -79.91, 0.51016, 2, 8, 14.38, 80.27, 0.8097, 56, -13.84, -114.23, 0.1903, 1, 8, -3.47, 70.8, 1, 2, 8, 98.47, 113.69, 0.10432, 56, -33.16, -25.82, 0.89568, 1, 56, 0.33, 32.34, 1, 2, 8, 75.69, 99.39, 0.23534, 56, -22.74, -50.62, 0.76466, 1, 56, 43.52, 46.27, 1, 1, 56, 98.18, 47.99, 1, 2, 7, 171.91, 10.33, 0.02194, 56, 154.95, 15.63, 0.97806, 1, 56, 44.37, 20.8, 1, 1, 56, 99.12, 16.43, 1, 3, 7, 149.49, 5.97, 0.10718, 8, 147.5, -69.54, 3e-05, 56, 155.61, -7.21, 0.8928, 3, 7, 130.65, 113.74, 0.00195, 8, 128.66, 38.23, 0.01263, 56, 46.21, -8.29, 0.98542, 3, 7, 125.49, 55.77, 0.07863, 8, 123.5, -19.75, 0.02815, 56, 102.57, -22.8, 0.89322, 3, 7, 126.81, 1.44, 0.29934, 8, 124.83, -74.07, 0.00174, 56, 156.39, -30.32, 0.69892, 1, 56, 113.86, 62.7, 1, 1, 8, -7.99, 59.4, 1, 1, 8, 6.54, 53.7, 1, 1, 8, 17.31, 40.58, 1, 1, 8, 15.88, 19.05, 1, 1, 8, 7.17, 11.27, 1, 1, 8, -7.54, 20.47, 1, 1, 8, -17.1, 37.38, 1, 1, 8, 44.98, 46.33, 1, 1, 8, 69.8, 24.48, 1, 1, 8, 7.93, 68.67, 1, 1, 8, 28.99, 40.94, 1, 1, 8, 54.54, 11.85, 1, 3, 7, 95.84, 52.07, 0.1043, 8, 93.85, -23.44, 0.67476, 56, 101.4, -52.65, 0.22094, 1, 8, -5.63, 50.95, 1, 1, 8, 5.97, 43.32, 1, 1, 8, 11.96, 29.72, 1, 1, 8, -5.21, 22.56, 1, 1, 8, -12.97, 40.16, 1, 1, 8, 73.35, -20.32, 1, 1, 8, 58.37, -3.96, 1, 1, 8, 35.31, -6.39, 1, 1, 8, 29.65, -12.62, 1, 1, 8, 43.43, -40.72, 1, 1, 8, 65.51, -50.34, 1, 1, 8, 46.71, -8.41, 1, 1, 8, 64.11, -18.19, 1, 1, 8, 76.1, -40.32, 1, 1, 8, 69.38, -43.67, 1, 1, 8, 50.41, -40.19, 1, 1, 8, 37.74, -23.02, 1, 1, 8, -7.01, -6.63, 1, 1, 8, 28.96, -48.12, 1, 3, 7, 62.36, -6.85, 0.91044, 8, 60.37, -82.36, 0.0542, 56, 154.1, -95.27, 0.03537, 1, 8, -24.08, -21.95, 1, 1, 8, -15, -31.63, 1, 1, 8, -0.37, -50.87, 1, 2, 7, -25.87, 36.78, 0.1084, 8, -27.86, -38.73, 0.8916], "hull": 26, "edges": [26, 28, 2, 0, 0, 50, 52, 54, 54, 56, 56, 58, 58, 60, 62, 64, 64, 16, 56, 66, 66, 68, 68, 70, 70, 38, 42, 44, 42, 72, 72, 52, 44, 46, 0, 74, 74, 52, 46, 74, 46, 48, 48, 50, 38, 40, 40, 42, 40, 76, 76, 54, 74, 78, 78, 80, 80, 82, 82, 10, 52, 84, 84, 86, 86, 88, 10, 12, 88, 12, 54, 90, 90, 92, 92, 94, 12, 14, 14, 16, 94, 14, 2, 4, 4, 6, 4, 96, 6, 8, 8, 10, 96, 8, 38, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 98, 68, 112, 112, 114, 114, 60, 70, 116, 116, 118, 118, 120, 60, 122, 122, 62, 120, 122, 36, 38, 36, 110, 124, 126, 126, 128, 128, 106, 106, 130, 130, 132, 132, 124, 62, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 62, 140, 146, 146, 148, 148, 150, 150, 62, 62, 152, 152, 154, 154, 156, 156, 140, 104, 138, 106, 140, 34, 36, 34, 158, 158, 160, 160, 162, 16, 18, 162, 18, 32, 34, 32, 164, 164, 166, 166, 168, 18, 20, 168, 20, 28, 30, 30, 32, 30, 170, 20, 22, 170, 22, 22, 24, 24, 26], "width": 260, "height": 277}}, "t2": {"t2": {"type": "mesh", "uvs": [0.99847, 0.2912, 0.81269, 0.28524, 0.65484, 0.28019, 0.59138, 0.36293, 0.53896, 0.4313, 0.42613, 0.57842, 0.37636, 0.66126, 0.30947, 0.77257, 0.24109, 0.88636, 0.17281, 1, 0.11847, 1, 0, 0.85341, 0, 0.79541, 0.02525, 0.76029, 0.07967, 0.68461, 0.14451, 0.59445, 0.20429, 0.51132, 0.28936, 0.39302, 0.39044, 0.25245, 0.50985, 0.08641, 0.56796, 0.0056, 0.75983, 0.00845, 0.94684, 0.01122], "triangles": [11, 12, 13, 13, 14, 8, 9, 11, 13, 9, 10, 11, 13, 8, 9, 15, 16, 6, 7, 15, 6, 14, 15, 7, 8, 14, 7, 5, 17, 4, 16, 17, 5, 6, 16, 5, 3, 18, 19, 4, 18, 3, 17, 18, 4, 21, 19, 20, 2, 19, 21, 1, 2, 21, 22, 1, 21, 1, 22, 0, 2, 3, 19], "vertices": [1, 38, -9.62, 9.88, 1, 2, 38, 8.04, 9.95, 0.99964, 39, -1.03, 27.83, 0.00036, 2, 38, 23.05, 10.02, 0.67093, 39, 3.75, 13.6, 0.32907, 3, 38, 28.71, 19.11, 0.08639, 39, 14.16, 11.08, 0.88863, 40, -15.39, 6.95, 0.02498, 3, 38, 33.4, 26.62, 0.00245, 39, 22.76, 8.99, 0.66206, 40, -6.54, 6.77, 0.3355, 1, 40, 12.5, 6.37, 1, 3, 40, 22.51, 7.24, 0.48482, 41, -3.21, 7.4, 0.51427, 42, -16.62, 20.48, 0.0009, 2, 41, 8.71, 13.74, 0.80915, 42, -3.3, 18.29, 0.19085, 2, 41, 20.9, 20.22, 0.21113, 42, 10.31, 16.05, 0.78887, 2, 41, 33.07, 26.69, 0.00555, 42, 23.91, 13.81, 0.99445, 1, 42, 25.57, 8.92, 1, 1, 42, 14.33, -6.78, 1, 1, 42, 8.46, -8.77, 1, 1, 42, 4.13, -7.71, 1, 2, 41, 21.58, -6.25, 0.53028, 42, -5.2, -5.41, 0.46972, 3, 39, 52.3, -19.92, 0.00069, 40, 28.53, -15.12, 0.08849, 41, 11.08, -10.82, 0.91082, 3, 39, 41.98, -17.74, 0.04178, 40, 17.98, -15.21, 0.5179, 41, 1.41, -15.03, 0.44032, 3, 39, 27.28, -14.64, 0.51021, 40, 2.96, -15.34, 0.46788, 41, -12.36, -21.03, 0.02191, 1, 39, 9.82, -10.95, 1, 2, 38, 37.65, -10.14, 0.83404, 39, -10.8, -6.6, 0.16596, 2, 38, 32.48, -19.01, 0.99842, 39, -20.84, -4.48, 0.00158, 1, 38, 14.25, -19.44, 1, 1, 38, -3.51, -19.86, 1], "hull": 23, "edges": [0, 44, 18, 20, 20, 22, 22, 24, 40, 42, 42, 44, 0, 2, 2, 4, 42, 2, 38, 40, 4, 6, 38, 6, 36, 38, 6, 8, 8, 10, 36, 8, 34, 36, 34, 10, 32, 34, 10, 12, 32, 12, 30, 32, 12, 14, 30, 14, 28, 30, 14, 16, 16, 18, 28, 16, 24, 26, 26, 28, 26, 18], "width": 95, "height": 107}}, "t3": {"t3": {"type": "mesh", "uvs": [0.98371, 0.29262, 0.98813, 0.62941, 0.99205, 0.92764, 0.80838, 0.97153, 0.68924, 1, 0.65677, 1, 0.64262, 0.87584, 0.43164, 0.64221, 0.1641, 0.34594, 0, 0.16422, 0, 0.1056, 0.18835, 0.08331, 0.4346, 0.05418, 0.64726, 0.02901, 0.86247, 0.00355, 0.73803, 0.21883, 0.85909, 0.43559, 0.57157, 0.26459, 0.68961, 0.50905, 0.8031, 0.71617, 0.33248, 0.3249, 0.53979, 0.56334, 0.72895, 0.77047], "triangles": [9, 10, 11, 15, 13, 14, 17, 12, 13, 17, 13, 15, 15, 14, 0, 20, 11, 12, 20, 12, 17, 8, 9, 11, 20, 8, 11, 16, 15, 0, 16, 18, 17, 16, 17, 15, 21, 20, 17, 21, 17, 18, 16, 0, 1, 18, 16, 1, 7, 20, 21, 8, 20, 7, 19, 18, 1, 21, 18, 19, 22, 21, 19, 7, 21, 22, 6, 7, 22, 19, 1, 2, 22, 19, 2, 3, 6, 22, 2, 3, 22, 4, 5, 6, 3, 4, 6], "vertices": [1, 9, 57.51, 64.95, 1, 1, 9, 95.35, 35.71, 1, 1, 9, 128.87, 9.82, 1, 1, 9, 120.9, -10.33, 1, 1, 9, 115.73, -23.41, 1, 1, 9, 113.46, -26.29, 1, 1, 9, 98.63, -16.62, 1, 1, 9, 57.81, -14.77, 1, 1, 9, 6.06, -12.43, 1, 1, 9, -25.68, -11, 1, 1, 9, -32.22, -5.84, 1, 1, 9, -21.51, 12.83, 1, 1, 9, -7.52, 37.23, 1, 1, 9, 4.57, 58.31, 1, 1, 9, 16.8, 79.64, 1, 1, 9, 32.08, 49.66, 1, 1, 9, 64.72, 41.32, 1, 1, 9, 25.53, 30.87, 1, 1, 9, 61.04, 19.83, 1, 1, 9, 92.07, 11.67, 1, 1, 9, 15.51, 4.35, 1, 1, 9, 56.6, 1.76, 1, 1, 9, 92.93, 0.31, 1], "hull": 15, "edges": [0, 28, 8, 10, 10, 12, 18, 20, 26, 28, 26, 30, 30, 32, 0, 2, 2, 4, 32, 2, 24, 26, 24, 34, 34, 36, 38, 4, 36, 38, 20, 22, 22, 24, 22, 40, 40, 42, 42, 44, 4, 6, 6, 8, 44, 6, 12, 14, 14, 16, 16, 18], "width": 113, "height": 142}}}}], "animations": {"animation1": {"bones": {"bone": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": -12, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "st2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "st": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s3": {"rotate": [{"angle": 0.38, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 6, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": 0.38}]}, "t1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -6, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}], "translate": [{"x": -0.37, "y": 0.06, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -1.98, "y": 0.3, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": -0.37, "y": 0.06}]}, "t6": {"rotate": [{"angle": 4.8, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -1.71}, {"time": 2, "angle": 11.32, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 4.8}]}, "t8": {"rotate": [{"angle": 5.36, "curve": 0.337, "c2": 0.35, "c3": 0.709, "c4": 0.81}, {"time": 0.6667, "angle": -1.05, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.8667, "angle": -1.71, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "angle": 8.6, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "angle": 5.36}]}, "t9": {"rotate": [{"angle": 9.77, "curve": 0.303, "c2": 0.24, "c3": 0.674, "c4": 0.69}, {"time": 0.6667, "angle": 1.49, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.0667, "angle": -1.71, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "angle": 11.5, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "angle": 9.77}]}, "t10": {"rotate": [{"angle": 12.48, "curve": 0.265, "c2": 0.08, "c3": 0.638, "c4": 0.56}, {"time": 0.6667, "angle": 4.52, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 1.2667, "angle": -1.71, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 12.65, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 2.6667, "angle": 12.48}]}, "t11": {"rotate": [{"angle": 11.34, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1333, "angle": 11.92, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": 6.91, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.4667, "angle": -1.71, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2.6667, "angle": 11.34}]}, "t12": {"rotate": [{"angle": 7.79, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": 9.94, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.6667, "angle": 7.79, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.6667, "angle": -1.71, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 7.79}]}, "t13": {"rotate": [{"angle": 1.97, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5333, "angle": 4.12, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.6667, "angle": 3.87, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1.8667, "angle": -1.71, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": 1.97}]}, "t14": {"rotate": [{"angle": 9.2, "curve": 0.362, "c2": 0.44, "c3": 0.735, "c4": 0.92}, {"time": 0.6667, "angle": 22.87, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.7333, "angle": 23.3, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": -1.71, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 2.6667, "angle": 9.2}]}, "s4": {"rotate": [{"angle": 2.01, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5}, {"time": 1.8333, "angle": 6, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": 2.01}]}, "s10": {"rotate": [{"angle": 4.03, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5}, {"time": 1.8333, "angle": 12, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": 4.03}], "translate": [{"x": -1.48, "y": -0.25, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": -4.41, "y": -0.74, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": -1.48, "y": -0.25}]}, "s12": {"rotate": [{"angle": -2.01}]}, "s13": {"rotate": [{}, {"time": 1.3333, "angle": -1.32}, {"time": 2.6667}]}, "s15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -8.52, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s1": {"rotate": [{"angle": 0.1, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 1.67, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": 0.1}]}, "s2": {"rotate": [{"angle": -2.08, "curve": 0.334, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 0.2333, "angle": -3.52, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.7333, "angle": -5.64, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": 0.66, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 2.6667, "angle": -2.08}]}, "s16": {"rotate": [{"angle": 2.02, "curve": 0.318, "c2": 0.28, "c3": 0.656, "c4": 0.63}, {"time": 0.2333, "angle": -0.05, "curve": 0.342, "c2": 0.36, "c3": 0.697, "c4": 0.76}, {"time": 0.7333, "angle": -4.41, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1, "angle": -5.64, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 3.75, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 2.02}]}, "s17": {"rotate": [{"angle": 5.67, "curve": 0.296, "c2": 0.14, "c3": 0.635, "c4": 0.5}, {"time": 0.2333, "angle": 4.01, "curve": 0.308, "c2": 0.25, "c3": 0.662, "c4": 0.65}, {"time": 0.7333, "angle": -1.43, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.2667, "angle": -5.64, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 5.8, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 2.6667, "angle": 5.67}]}, "s18": {"rotate": [{"angle": 4.82, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.2, "angle": 5.76, "curve": 0.325, "c3": 0.659, "c4": 0.34}, {"time": 0.2333, "angle": 5.72, "curve": 0.267, "c2": 0.05, "c3": 0.624, "c4": 0.49}, {"time": 0.7333, "angle": 1.57, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.5333, "angle": -5.64, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 2.6667, "angle": 4.82}]}, "s5": {"rotate": [{"angle": -4.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -9.24}, {"time": 2, "angle": 0.23, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -4.5}]}, "s6": {"rotate": [{"angle": -1.53, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 0.6667, "angle": -7.49, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1, "angle": -9.24, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 0.23, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -1.53}]}, "s7": {"rotate": [{"angle": 0.23, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": -4.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "angle": -9.24, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 0.23}]}, "s8": {"rotate": [{"angle": -1.52, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": 0.23, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.6667, "angle": -1.52, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.6667, "angle": -9.24, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -1.52}]}, "t15": {"rotate": [{"angle": 5.68, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": 7.38}, {"time": 1.6667, "angle": -1.84, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 5.68}]}, "t16": {"rotate": [{"angle": 2.8, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.3333, "angle": 5.68, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "angle": 7.38, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -1.84, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 2.8}]}, "t17": {"rotate": [{"angle": -0.15, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "angle": 2.77, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1, "angle": 7.38, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -1.84, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -0.15}]}, "t18": {"rotate": [{"angle": -1.84, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "angle": -0.14, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.3333, "angle": 7.38, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -1.84}]}, "t19": {"rotate": [{"angle": 5.68, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": 7.38}, {"time": 1.6667, "angle": -1.84, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 5.68}]}, "t4": {"shear": [{"x": 1.59, "y": -1.59, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 2.4, "y": -2.4, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "x": 1.59, "y": -1.59}]}, "t3": {"shear": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -1.2, "y": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "t7": {"rotate": [{"angle": -4.51, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -7.6, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 9.15, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -4.51}]}, "t5": {"rotate": [{"angle": 1.46, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667}, {"time": 1.5, "angle": 23.26, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": 1.46}], "translate": [{"x": 0.15, "y": -0.2, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 2.45, "y": -3.17, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "x": 0.15, "y": -0.2}]}, "bone4": {"rotate": [{"angle": -0.08, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -1.2, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": -0.08}]}, "ps": {"rotate": [{"angle": -5.79, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.09, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -5.79}]}, "ps4": {"rotate": [{"angle": -5.36, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "angle": -5.79, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 1.09, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": -5.36}]}, "ps5": {"rotate": [{"angle": -4.52, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -5.79, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 1.09, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -4.52}]}, "ps6": {"rotate": [{"angle": -3.48, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "angle": -5.79, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 1.09, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -3.48}]}, "ps7": {"rotate": [{"angle": -2.35, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -5.79, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 1.09, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -2.35}]}, "ps8": {"rotate": [{"angle": -1.22, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333, "angle": -5.79, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 1.09, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": -1.22}]}, "ps9": {"rotate": [{"angle": -0.18, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "angle": -5.79, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 1.09, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -0.18}]}, "ps10": {"rotate": [{"angle": 0.66, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 1.1667, "angle": -5.79, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 1.09, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.6667, "angle": 0.66}]}, "bone3": {"translate": [{"x": 2.86, "y": 3.63, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "x": 3.5, "y": 4.45, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 2.86, "y": 3.63}]}, "ps2": {"rotate": [{"angle": -5.65}, {"time": 1.3333, "angle": 0.14}, {"time": 2.6667, "angle": -5.65}], "translate": [{"x": 1.96, "y": 3.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "x": 2.42, "y": 3.67, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -0.08, "y": 0.89, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 1.96, "y": 3.15}]}, "ps3": {"rotate": [{"angle": -5.65}, {"time": 1.3333, "angle": 0.14}, {"time": 2.6667, "angle": -5.65}]}, "s14": {"rotate": [{"angle": -4.03}]}, "s9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 14.4, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -5.27, "y": -0.24, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s11": {"rotate": [{"angle": 15.14, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 12, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": 15.14}]}, "t2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}], "shear": [{}, {"time": 1.3333, "x": -1.2}, {"time": 2.6667}]}}, "deform": {"default": {"s7": {"s7": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 14, "vertices": [4.45122, 2.05941, 4.65831, 1.53353, 5.37772, 0.05796, 5.34807, -0.56113, 3.19969, -0.14005, 3.16153, -0.50702, 0.85814, -1.38623, 0.69267, -1.4757, 0.39896, -1.21102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.44833, -2.1939, 1.18582, -2.34586, 0, 0, 0, 0, 1.1689, -0.53981, 1.09856, -0.67057], "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "t1": {"t1": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "offset": 16, "vertices": [-0.03277, 2.83961, -2.83136, 0.371, -0.49989, 0.62122, -0.66942, -0.40602, -4.02356, 4.74638, -5.13112, -3.3111, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.55853, -0.69421, 0.74649, 0.45531, 0.60167, -0.74769, 0.80365, 0.49065, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.50008, -0.62155, 0.66869, 0.4075, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.60526, 2.78832, -0.60506, 2.78761, -2.83791, -0.20287, 0, 0, 0, 0, 0, 0, 0, 0, 0.73026, -0.90752, 2.71869, -1.08894, 1.35611, 2.53995, 0.66856, 0.40778, 2.71869, -1.08894, 1.35611, 2.53995, 2.45931, 1.27304, 1.76865, -1.10722, 0, 0, 0, 0, 0.82426, 1.39201, -0.54584, -0.49678, -0.22771, 1.04562, -0.22752, 1.04469, -1.0648, -0.07556, -0.5999, 0.74555, -0.59962, 0.74524, -0.80295, -0.48799, -0.49992, 0.62129, -0.49974, 0.62109, -0.66908, -0.40675, -3.01804, 1.46121, -3.01748, 1.46032, -1.75871, -2.78123, 0, 0, 0.55853, -0.69421, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.36015, -0.99925, -1.36034, -0.99931, 1.37241, 2.05351, 2.55109, 1.87389, 2.55121, 1.87352], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "offset": 16, "vertices": [-0.06554, 5.67921, -5.66272, 0.742, -0.99979, 1.24243, -1.33884, -0.81204, -8.04712, 9.49275, -10.26224, -6.62219, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.11707, -1.38843, 1.49298, 0.91061, 1.20334, -1.49538, 1.6073, 0.98129, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.00017, -1.2431, 1.33737, 0.815, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.21053, 5.57664, -1.21013, 5.57521, -5.67581, -0.40573, 0, 0, 0, 0, 0, 0, 0, 0, 1.46051, -1.81503, 5.43738, -2.17789, 2.71222, 5.0799, 1.33713, 0.81555, 5.43738, -2.17789, 2.71222, 5.0799, 4.91861, 2.54608, 3.53729, -2.21445, 0, 0, 0, 0, 1.64853, 2.78403, -1.09167, -0.99356, -0.45541, 2.09125, -0.45505, 2.08939, -2.12961, -0.15112, -1.1998, 1.4911, -1.19925, 1.49048, -1.6059, -0.97598, -0.99985, 1.24258, -0.99948, 1.24217, -1.33817, -0.81351, -6.03607, 2.92242, -6.03496, 2.92064, -3.51743, -5.56247, 0, 0, 1.11707, -1.38843], "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.6667, "offset": 16, "vertices": [-0.05345, 4.63159, -4.61814, 0.60513, -0.81536, 1.01325, -1.09187, -0.66225, -6.5627, 7.74166, -8.3692, -5.40062, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.911, -1.13231, 1.21758, 0.74264, 0.98136, -1.21953, 1.31081, 0.80028, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.81567, -1.01379, 1.09067, 0.66466, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.98722, 4.54794, -0.9869, 4.54677, -4.62882, -0.33089, 0, 0, 0, 0, 0, 0, 0, 0, 1.1911, -1.48022, 4.43437, -1.77614, 2.21191, 4.14283, 1.09047, 0.66511, 4.43437, -1.77614, 2.21191, 4.14283, 4.01129, 2.07642, 2.88478, -1.80596, 0, 0, 0, 0, 1.34443, 2.27047, -0.8903, -0.81028, -0.37141, 1.70548, -0.37111, 1.70397, -1.73677, -0.12325, -0.97848, 1.21605, -0.97803, 1.21554, -1.30966, -0.79595, -0.81541, 1.01337, -0.81511, 1.01303, -1.09132, -0.66344, -4.92262, 2.38334, -4.92171, 2.38188, -2.86858, -4.53638, 0, 0, 0.911, -1.13231], "curve": 0.318, "c2": 0.28, "c3": 0.656, "c4": 0.63}, {"time": 1.9, "offset": 16, "vertices": [-0.03943, 3.41661, -3.40669, 0.44639, -0.60147, 0.74745, -0.80544, -0.48852, -4.84114, 5.71083, -6.17375, -3.98391, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.67203, -0.83528, 0.89818, 0.54782, 0.72393, -0.89962, 0.96695, 0.59034, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.6017, -0.74785, 0.80456, 0.4903, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.72825, 3.35491, -0.72801, 3.35404, -3.41456, -0.24409, 0, 0, 0, 0, 0, 0, 0, 0, 0.87864, -1.09192, 3.27112, -1.31022, 1.63167, 3.05606, 0.80441, 0.49064, 3.27112, -1.31022, 1.63167, 3.05606, 2.95903, 1.53172, 2.12803, -1.33221, 0, 0, 0, 0, 0.99175, 1.67487, -0.65675, -0.59773, -0.27398, 1.25809, -0.27376, 1.25697, -1.28117, -0.09092, -0.7218, 0.89705, -0.72147, 0.89667, -0.96611, -0.58715, -0.60151, 0.74754, -0.60129, 0.74729, -0.80504, -0.48941, -3.6313, 1.75813, -3.63063, 1.75705, -2.11608, -3.34638, 0, 0, -2.1333, -2.96156, -5.43031, -5.09964, -7.46335, -8.74847, -2.8562, -2.16495, 0, 0, 2.23151, 1.69106, 3.03485, 2.29982, 0, 0, 0, 0, -3.57045, -2.70621, -3.57045, -2.70621, -3.36644, -2.5515, -2.09079, -4.23442, -2.09082, -4.23476, 3.94064, -2.70328, -2.10397, -1.59489, -7.83662, -3.83162, -7.34456, -3.73984, 2.95119, 1.11218, 3.0134, -1.08939, -4.70207, -3.56419, -8.78099, -6.65575, -3.78705, -2.87073, 0, 0, 0, 0, 0, 0, -5.12376, -7.8588, -5.84773, -6.42012, -0.37299, -0.28273, 1.40268, 1.06296, 3.08595, 2.33859, 2.10403, 1.5945], "curve": 0.333, "c2": 0.33, "c3": 0.672, "c4": 0.68}, {"time": 2.1667, "offset": 16, "vertices": [-0.02222, 1.9256, -1.92001, 0.25159, -0.33899, 0.42126, -0.45395, -0.27533, -2.72847, 3.21863, -3.47953, -2.24533, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.37875, -0.47076, 0.50621, 0.30875, 0.40801, -0.50702, 0.54497, 0.33272, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.33912, -0.42149, 0.45345, 0.27634, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.41044, 1.89083, -0.41031, 1.89034, -1.92445, -0.13757, 0, 0, 0, 0, 0, 0, 0, 0, 0.4952, -0.61541, 1.84361, -0.73844, 0.91961, 1.7224, 0.45337, 0.27652, 1.84361, -0.73844, 0.91961, 1.7224, 1.66771, 0.86328, 1.19936, -0.75083, 0, 0, 0, 0, 0.55895, 0.94396, -0.37014, -0.33688, -0.15441, 0.70906, -0.15429, 0.70843, -0.72207, -0.05124, -0.40681, 0.50558, -0.40662, 0.50536, -0.5445, -0.33092, -0.33901, 0.42131, -0.33889, 0.42117, -0.45372, -0.27583, -2.0466, 0.99088, -2.04622, 0.99028, -1.19262, -1.88602, 0, 0, 0.37875, -0.47076], "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 2.6667}]}, "s3": {"s3": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 14, "vertices": [6.63123, -4.3121, 3.05801, -7.2949, 0.4333, -7.89801, -1.90201, -19.85314, 17.50558, 9.5559, -12.73812, -15.34559, -17.14818, -10.18237, 3.0667, -7.9309, -1.92437, -8.28172, -4.59111, -7.15603, -6.32166, -5.68896, -7.94733, -0.27179, -6.72417, 4.24493, -4.9102, 6.25491, -3.08224, 7.3304, 1.74222, 2.65937, 3.17586, 0.16377, 3.10391, -0.68265, -10.32173, 12.19447, -6.73318, 14.48831, -16.78639, 18.12445, -11.40063, 21.91626, -21.08221, 12.87415, 8.42853, -3.80567, 7.1199, -5.89989, 9.14938, -1.36879, 5.61615, -7.1542, 3.52438, -8.38457, 7.3512, -5.35611, -7.23184, 13.95255, -13.3988, 8.20923, -12.21548, 25.94652, -23.86346, 15.90244, -29.91699, 13.327, -39.07742, 9.42618, -45.88843, 6.5255, -44.29721, 10.84283, -36.02686, 19.3365, -23.97864, 31.71022, -18.15939, 13.23256, -12.3024, 27.87241, -6.03558, 23.74712, 10.2681, 16.75273, 0.13928, 19.65329, 16.55594, 4.23584, 11.99094, 12.18639, 17.33701, 13.5255, 7.85678, 20.54153, 15.99759, 27.37128, -0.44214, 31.70572, 14.93005, 51.14998, -13.64423, 51.51115, 2.62939, 71.80301, -34.84796, 62.83653, -11.35745, 95.10866, -58.86618, 75.56049, 2.62589, 80.52798, -39.35959, 70.30432, 9.92813, 70.00278, -27.66937, 65.06677, 22.3251, 51.96553, -7.73489, 56.03104, 37.14981, 30.12082, 16.24619, 44.99121, 34.52386, 14.58519, 22.02295, 30.33034, 32.30206, 5.01399, 25.06696, 20.9879, 29.46326, -7.22725, 28.96271, 9.04137, -6.99359, 13.05048, -12.72714, 7.56107, 2.11975, 11.41409, 5.05933, 10.44691, -1.06308, 11.56064, 0.87672, 13.00725, 4.28, 12.31116, -2.69113, 12.75565, -10.00038, 30.46023, -1.59528, 32.01933, 2.38594, 26.662, 9.34656, 25.08153, -10.01792, 22.75817, -1.80118, 24.80005, 4.81424, 24.39287, -18.51906, 16.33805, -11.96381, 21.60427, -5.83075, 23.99716, 0.35451, 18.82964, 6.65152, 17.61941, 4.81292, 25.89296, 13.22118, 22.77741, 7.4691, 11.83129, 11.00586, 8.6397, 15.08199, 9.32406, -17.45908, -3.09405, 15.08199, 9.32406, 0, 0, 0, 0, 1.65544, 0.51111, -1.34909, -1.08646, -2.14724, 0.9104, 2.33194, -0.05294, 0, 0, 0, 0, 0, 0, 0, 0, -0.52551, 0.26024, 1.80995, 3.14175, -3.61681, -0.26389, -5.81265, -5.58365, 7.88165, -1.68345, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.04291, 1.1064, -0.55997, -1.41331, 0.81355, 0.59166, -0.53723, -0.8498, -2.74243, -16.41827, 15.13268, 6.93429, -11.5011, -12.03275, -14.87129, -7.47662, -0.49802, -0.76672, 0.91359, 0.01682, 15.54312, 0.53127, 9.60321, -12.23315, 0.49057, -3.44718, -1.72865, 3.0222, -0.95419, -0.75439, 0.60867, 1.05371, -0.32475, 2.48491, 1.21984, -2.18917, -15.74543, -1.84623, 13.95033, 7.53149, 17.49741, 9.50936, 11.60019, 18.37658, -1.04538, 1.70602, 15.39485, 5.83624, 16.46083, 0.33286, 2.70819, -8.12638, 5.21064, 6.79872, -2.33066, -8.24195, -4.9606, -6.98219, 1.40121, 1.34001, 1.76979, 0.7922, -1.62555, -8.29469, -6.00889, -5.94382, -7.65459, -3.58333, -1.50274, 6.07894, 0.62404, 6.23074, 4.81216, 0.16448, 4.07146, -2.57035, 2.9731, -3.78736, 1.86615, -4.43867, -3.53955, 13.82364, 1.30363, 14.20983, 5.01236, 13.35912, 8.55515, 0.29236, 5.28566, -6.73325, 3.31732, -7.89124, 0, 0, 0, 0, 7.59793, -9.67896, 4.76825, -11.34384, 9.49178, 2.39664, 9.78693, -0.19788, 8.48236, 4.8889, 13.54416, -17.25371, 8.50031, -20.22124, 17.72858, -12.91769, 20.14215, 4.15723, 15.10223, 13.96953, 14.20491, -18.09533, 8.91522, -21.20735, 18.59323, -13.54774, 9.78693, -0.19788, 8.48236, 4.8889, 0, 0, 0, 0, 23.6424, 2.53021, 18.93658, 14.38663, 19.34848, 11.0195, 10.87387, 19.43573, 25.52151, 15.258, 13.96817, 26.25401, 4.35254, -10.35835, 9.08127, -6.61656, 9.78693, -0.19788, 8.48236, 4.8889, 9.8306, 10.14468, 3.1778, 13.76822, -5.39157, -4.67609, -6.44025, 17.98442, -8.647, 6.30119, -24.79379, 23.6909], "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "st": {"st": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 144, "vertices": [1.56757, 2.56271, -6.8584, -31.31458, -3.45361, -22.90106, 7.03193, -11.03623, 0.52203, -13.07584, 5.53498, -0.65182, 4.45346, -3.34854, 0, 0, 0, 0, 6.71576, 4.66041, 6.71576, 4.66041, 12.12347, 4.54075, 6.68922, 3.45866, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.48343, -3.61277, 0.27974, -3.63411, 0.27965, -3.63432, 0, 0, 0, 0, 0, 0, 0, 0, -0.80533, -6.02137, 0.46622, -6.05696, 0.46615, -6.05714, 0.13446, 6.07352, 0, 0, 0, 0, 0, 0, 0, 0, -4.28577, -7.59896, -2.60903, -8.32491, 3.41859, 8.02633, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10.16138, -2.62962, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10.16138, -2.62962, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10.05499, -7.43641, 0, 0, 0, 0, 0, 0, -0.10639, -4.80678, 0, 0, 0, 0, -0.10639, -4.80678, -0.27928, -12.6179, -5.08292, -11.55197, -0.10639, -4.80678, -0.45217, -20.42902, -8.22949, -18.70329, -0.10639, -4.80687, -2.51077, -4.10052, -0.63835, -28.84103, -15.06393, -24.60281, -11.61813, -26.40468, 0, 0, 0, 0, -0.17572, -24.5686, 0, 0, -10.35156, -16.33197, -9.62952, -16.76237, 0, 0, -9.69296, -5.81448, -9.42657, -6.22655, -11.29773, 0.23376], "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s1": {"s1": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 102, "vertices": [-5.79477, -3.35471, -6.25058, -2.42416, -9.78232, -0.53732, -9.75916, 0.97124, -24.74069, -4.93918, -25.2247, -1.08189, -34.85818, -0.10479, -34.48151, 5.24583, -46.10049, 5.25592, -44.76978, 12.26534, -55.3591, 9.68307, -53.24069, 18.05908, -69.65125, 16.57475, -66.30829, 27.05954, -54.69901, 14.44098, -51.85937, 22.65988, -41.68109, 12.63264, -39.27234, 18.87801, -27.62469, 10.70128, -25.67786, 14.81552, -14.28699, 8.86736, -12.77905, 10.95955, -2.61563, 6.15636, -1.65112, 6.48778, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.61282, -5.63466, -6.40741, -3.70212, -12.95636, -0.36647, -15.95245, 1.15977, -17.33359, 6.69487, -16.91141, 6.814, -11.59714, 7.16682, -7.12057, 5.64384, -1.98154, 3.89505, -0.73364, 2.43295, -0.21008, 0.69511, -0.21008, 0.69511, -1.34012, 4.44353, -2.42676, 8.03954, -3.68137, 12.20439, -3.89105, 8.12513, -2.60132, 8.62541, -2.83005, 5.90919, -1.89221, 6.27312, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.43488, 1.11086, 4.1235, 1.97479, 4.37717, 1.31987, -1.94409, 7.76163, -3.45593, 7.21616, -2.30981, 7.66063, -1.48125, 5.91356, -2.63312, 5.49794, -1.11093, 4.43506, -1.97476, 4.12335, -1.48132, 5.91344, -2.63303, 5.49774, -0.23141, 9.17282, -1.48312, 9.05568, -1.03009, 9.46909, -2.31439, 9.23962, -8.08933, 14.88942, -1.83245, 16.84561, -0.48529, 21.50284, 7.71277, 20.07779, 12.28636, 24.98734, 20.85158, 18.45401, 4.58235, 24.27066, 13.45196, 20.71494, 15.77647, 8.5621, 17.84598, 1.93405, 17.59624, 6.57098, 18.7738, -0.59875, 19.42194, 4.21069, 19.56708, -3.47546, 20.50895, 1.6864, 23.04952, 11.06003, 25.59848, 5.39641, 27.52202, 1.1227, 11.42411, 1.95328, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12.49396, 3.69232, 12.96083, -1.32542, 7.30711, 3.96974, 8.26713, 0.89938, -4.46186, 1.87782, 7.97141, 4.33061, 9.01869, 0.98106, -3.60915, 6.64279, -0.81767, 7.51563, 8.87631, 14.52943, 13.72693, 10.0733, -7.89377, 13.99609, -1.99041, 15.94498, 2.74357, 15.44255, 8.39964, 13.24577, -0.67731, 17.96446, 0.94134, 11.08142, 5.0773, 9.8956, -1.80226, 16.56419, 5.18443, 8.19969, 7.90922, 5.61888, 7.06526, 6.64867, 3.2078, 12.09532, 7.55914, 9.97327, 6.12337, 10.91484, 1.93378, 11.6037, 6.19358, 10.00151, 4.76799, 10.75464, 1.36893, 3.07938, -7.36479, 5.28313, -8.01849, 4.22765, -8.70004, 2.53873, 7.1411, 9.12134, 5.17542, 10.3632, -8.12223, 5.20067, -8.75742, 4.0424, -9.38727, 2.20968, 4.20761, 8.38673, 2.44809, 9.05701, -14.27795, 2.65984, -14.5199, -0.24849, 7.1411, 9.12134, 5.17542, 10.3632, -14.67062, 2.56131, -14.88519, -0.42335, 7.1411, 9.12134, 5.17542, 10.3632, 6.69952, 9.44826, -14.67062, 2.56131, -14.88519, -0.42335, -14.77658, 1.86327, 5.17542, 10.3632, 6.69952, 9.44826, -14.88519, -0.42335, -14.77658, 1.86327, -3.07169, 6.41386, -2.05405, 6.80859, -2.05405, 6.80859, -2.05405, 6.80859, -2.05405, 6.80859, -0.63007, 2.08553, -0.63007, 2.08553, -3.34711, 1.86855, -10.12869, 2.99142, -2.82904, -0.62057, -2.9007, -0.17659, -5.89767, 3.15962, -5.3533, 4.02953, -16.88501, 4.42117, -16.02786, 6.96352], "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}}}}, "animation2": {"slots": {"bg": {"attachment": [{"name": null}]}}, "bones": {"bone": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": -12, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "st2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "st": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s3": {"rotate": [{"angle": 0.38, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 6, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": 0.38}]}, "t1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -6, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}], "translate": [{"x": -0.37, "y": 0.06, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -1.98, "y": 0.3, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": -0.37, "y": 0.06}]}, "t6": {"rotate": [{"angle": 4.8, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -1.71}, {"time": 2, "angle": 11.32, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 4.8}]}, "t8": {"rotate": [{"angle": 5.36, "curve": 0.337, "c2": 0.35, "c3": 0.709, "c4": 0.81}, {"time": 0.6667, "angle": -1.05, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.8667, "angle": -1.71, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "angle": 8.6, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "angle": 5.36}]}, "t9": {"rotate": [{"angle": 9.77, "curve": 0.303, "c2": 0.24, "c3": 0.674, "c4": 0.69}, {"time": 0.6667, "angle": 1.49, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1.0667, "angle": -1.71, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "angle": 11.5, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "angle": 9.77}]}, "t10": {"rotate": [{"angle": 12.48, "curve": 0.265, "c2": 0.08, "c3": 0.638, "c4": 0.56}, {"time": 0.6667, "angle": 4.52, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 1.2667, "angle": -1.71, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 12.65, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 2.6667, "angle": 12.48}]}, "t11": {"rotate": [{"angle": 11.34, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1333, "angle": 11.92, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": 6.91, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.4667, "angle": -1.71, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2.6667, "angle": 11.34}]}, "t12": {"rotate": [{"angle": 7.79, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": 9.94, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.6667, "angle": 7.79, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.6667, "angle": -1.71, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 7.79}]}, "t13": {"rotate": [{"angle": 1.97, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5333, "angle": 4.12, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.6667, "angle": 3.87, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 1.8667, "angle": -1.71, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "angle": 1.97}]}, "t14": {"rotate": [{"angle": 9.2, "curve": 0.362, "c2": 0.44, "c3": 0.735, "c4": 0.92}, {"time": 0.6667, "angle": 22.87, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.7333, "angle": 23.3, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": -1.71, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 2.6667, "angle": 9.2}]}, "s4": {"rotate": [{"angle": 2.01, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5}, {"time": 1.8333, "angle": 6, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": 2.01}]}, "s10": {"rotate": [{"angle": 4.03, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5}, {"time": 1.8333, "angle": 12, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": 4.03}], "translate": [{"x": -1.48, "y": -0.25, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": -4.41, "y": -0.74, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": -1.48, "y": -0.25}]}, "s12": {"rotate": [{"angle": -2.01}]}, "s13": {"rotate": [{}, {"time": 1.3333, "angle": -1.32}, {"time": 2.6667}]}, "s15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -8.52, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s1": {"rotate": [{"angle": 0.1, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 1.67, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": 0.1}]}, "s2": {"rotate": [{"angle": -2.08, "curve": 0.334, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 0.2333, "angle": -3.52, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.7333, "angle": -5.64, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": 0.66, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 2.6667, "angle": -2.08}]}, "s16": {"rotate": [{"angle": 2.02, "curve": 0.318, "c2": 0.28, "c3": 0.656, "c4": 0.63}, {"time": 0.2333, "angle": -0.05, "curve": 0.342, "c2": 0.36, "c3": 0.697, "c4": 0.76}, {"time": 0.7333, "angle": -4.41, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1, "angle": -5.64, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 3.75, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 2.02}]}, "s17": {"rotate": [{"angle": 5.67, "curve": 0.296, "c2": 0.14, "c3": 0.635, "c4": 0.5}, {"time": 0.2333, "angle": 4.01, "curve": 0.308, "c2": 0.25, "c3": 0.662, "c4": 0.65}, {"time": 0.7333, "angle": -1.43, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.2667, "angle": -5.64, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 5.8, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 2.6667, "angle": 5.67}]}, "s18": {"rotate": [{"angle": 4.82, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.2, "angle": 5.76, "curve": 0.325, "c3": 0.659, "c4": 0.34}, {"time": 0.2333, "angle": 5.72, "curve": 0.267, "c2": 0.05, "c3": 0.624, "c4": 0.49}, {"time": 0.7333, "angle": 1.57, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.5333, "angle": -5.64, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 2.6667, "angle": 4.82}]}, "s5": {"rotate": [{"angle": -4.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -9.24}, {"time": 2, "angle": 0.23, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -4.5}]}, "s6": {"rotate": [{"angle": -1.53, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 0.6667, "angle": -7.49, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1, "angle": -9.24, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 0.23, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -1.53}]}, "s7": {"rotate": [{"angle": 0.23, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": -4.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "angle": -9.24, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 0.23}]}, "s8": {"rotate": [{"angle": -1.52, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": 0.23, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.6667, "angle": -1.52, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.6667, "angle": -9.24, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -1.52}]}, "t15": {"rotate": [{"angle": 5.68, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": 7.38}, {"time": 1.6667, "angle": -1.84, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 5.68}]}, "t16": {"rotate": [{"angle": 2.8, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.3333, "angle": 5.68, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6667, "angle": 7.38, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -1.84, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 2.8}]}, "t17": {"rotate": [{"angle": -0.15, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "angle": 2.77, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1, "angle": 7.38, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -1.84, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -0.15}]}, "t18": {"rotate": [{"angle": -1.84, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "angle": -0.14, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.3333, "angle": 7.38, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -1.84}]}, "t19": {"rotate": [{"angle": 5.68, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": 7.38}, {"time": 1.6667, "angle": -1.84, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 5.68}]}, "t4": {"shear": [{"x": 1.59, "y": -1.59, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 2.4, "y": -2.4, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "x": 1.59, "y": -1.59}]}, "t3": {"shear": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -1.2, "y": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "t7": {"rotate": [{"angle": -4.51, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -7.6, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 9.15, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -4.51}]}, "t5": {"rotate": [{"angle": 1.46, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667}, {"time": 1.5, "angle": 23.26, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": 1.46}], "translate": [{"x": 0.15, "y": -0.2, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 2.45, "y": -3.17, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "x": 0.15, "y": -0.2}]}, "bone4": {"rotate": [{"angle": -0.08, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -1.2, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": -0.08}]}, "ps": {"rotate": [{"angle": -5.79, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.09, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -5.79}]}, "ps4": {"rotate": [{"angle": -5.36, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "angle": -5.79, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 1.09, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": -5.36}]}, "ps5": {"rotate": [{"angle": -4.52, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "angle": -5.79, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 1.09, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -4.52}]}, "ps6": {"rotate": [{"angle": -3.48, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "angle": -5.79, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 1.09, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -3.48}]}, "ps7": {"rotate": [{"angle": -2.35, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -5.79, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 1.09, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -2.35}]}, "ps8": {"rotate": [{"angle": -1.22, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333, "angle": -5.79, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 1.09, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": -1.22}]}, "ps9": {"rotate": [{"angle": -0.18, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "angle": -5.79, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 1.09, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -0.18}]}, "ps10": {"rotate": [{"angle": 0.66, "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 1.1667, "angle": -5.79, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 1.09, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.6667, "angle": 0.66}]}, "bone3": {"translate": [{"x": 2.86, "y": 3.63, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "x": 3.5, "y": 4.45, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 2.86, "y": 3.63}]}, "ps2": {"rotate": [{"angle": -5.65}, {"time": 1.3333, "angle": 0.14}, {"time": 2.6667, "angle": -5.65}], "translate": [{"x": 1.96, "y": 3.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "x": 2.42, "y": 3.67, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -0.08, "y": 0.89, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 1.96, "y": 3.15}]}, "ps3": {"rotate": [{"angle": -5.65}, {"time": 1.3333, "angle": 0.14}, {"time": 2.6667, "angle": -5.65}]}, "s14": {"rotate": [{"angle": -4.03}]}, "s9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 14.4, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -5.27, "y": -0.24, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s11": {"rotate": [{"angle": 15.14, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 12, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": 15.14}]}, "t2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}], "shear": [{}, {"time": 1.3333, "x": -1.2}, {"time": 2.6667}]}}, "deform": {"default": {"s7": {"s7": [{}, {"time": 1.3333, "offset": 14, "vertices": [4.45122, 2.05941, 4.65831, 1.53353, 5.37772, 0.05796, 5.34807, -0.56113, 3.19969, -0.14005, 3.16153, -0.50702, 0.85814, -1.38623, 0.69267, -1.4757, 0.39896, -1.21102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.44833, -2.1939, 1.18582, -2.34586, 0, 0, 0, 0, 1.1689, -0.53981, 1.09856, -0.67057]}, {"time": 2.6667}]}, "t1": {"t1": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "offset": 16, "vertices": [-0.03277, 2.83961, -2.83136, 0.371, -0.49989, 0.62122, -0.66942, -0.40602, -4.02356, 4.74638, -5.13112, -3.3111, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.55853, -0.69421, 0.74649, 0.45531, 0.60167, -0.74769, 0.80365, 0.49065, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.50008, -0.62155, 0.66869, 0.4075, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.60526, 2.78832, -0.60506, 2.78761, -2.83791, -0.20287, 0, 0, 0, 0, 0, 0, 0, 0, 0.73026, -0.90752, 2.71869, -1.08894, 1.35611, 2.53995, 0.66856, 0.40778, 2.71869, -1.08894, 1.35611, 2.53995, 2.45931, 1.27304, 1.76865, -1.10722, 0, 0, 0, 0, 0.82426, 1.39201, -0.54584, -0.49678, -0.22771, 1.04562, -0.22752, 1.04469, -1.0648, -0.07556, -0.5999, 0.74555, -0.59962, 0.74524, -0.80295, -0.48799, -0.49992, 0.62129, -0.49974, 0.62109, -0.66908, -0.40675, -3.01804, 1.46121, -3.01748, 1.46032, -1.75871, -2.78123, 0, 0, 0.55853, -0.69421, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.36015, -0.99925, -1.36034, -0.99931, 1.37241, 2.05351, 2.55109, 1.87389, 2.55121, 1.87352], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "offset": 16, "vertices": [-0.06554, 5.67921, -5.66272, 0.742, -0.99979, 1.24243, -1.33884, -0.81204, -8.04712, 9.49275, -10.26224, -6.62219, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.11707, -1.38843, 1.49298, 0.91061, 1.20334, -1.49538, 1.6073, 0.98129, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.00017, -1.2431, 1.33737, 0.815, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.21053, 5.57664, -1.21013, 5.57521, -5.67581, -0.40573, 0, 0, 0, 0, 0, 0, 0, 0, 1.46051, -1.81503, 5.43738, -2.17789, 2.71222, 5.0799, 1.33713, 0.81555, 5.43738, -2.17789, 2.71222, 5.0799, 4.91861, 2.54608, 3.53729, -2.21445, 0, 0, 0, 0, 1.64853, 2.78403, -1.09167, -0.99356, -0.45541, 2.09125, -0.45505, 2.08939, -2.12961, -0.15112, -1.1998, 1.4911, -1.19925, 1.49048, -1.6059, -0.97598, -0.99985, 1.24258, -0.99948, 1.24217, -1.33817, -0.81351, -6.03607, 2.92242, -6.03496, 2.92064, -3.51743, -5.56247, 0, 0, 1.11707, -1.38843], "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.6667, "offset": 16, "vertices": [-0.05345, 4.63159, -4.61814, 0.60513, -0.81536, 1.01325, -1.09187, -0.66225, -6.5627, 7.74166, -8.3692, -5.40062, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.911, -1.13231, 1.21758, 0.74264, 0.98136, -1.21953, 1.31081, 0.80028, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.81567, -1.01379, 1.09067, 0.66466, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.98722, 4.54794, -0.9869, 4.54677, -4.62882, -0.33089, 0, 0, 0, 0, 0, 0, 0, 0, 1.1911, -1.48022, 4.43437, -1.77614, 2.21191, 4.14283, 1.09047, 0.66511, 4.43437, -1.77614, 2.21191, 4.14283, 4.01129, 2.07642, 2.88478, -1.80596, 0, 0, 0, 0, 1.34443, 2.27047, -0.8903, -0.81028, -0.37141, 1.70548, -0.37111, 1.70397, -1.73677, -0.12325, -0.97848, 1.21605, -0.97803, 1.21554, -1.30966, -0.79595, -0.81541, 1.01337, -0.81511, 1.01303, -1.09132, -0.66344, -4.92262, 2.38334, -4.92171, 2.38188, -2.86858, -4.53638, 0, 0, 0.911, -1.13231], "curve": 0.318, "c2": 0.28, "c3": 0.656, "c4": 0.63}, {"time": 1.9, "offset": 16, "vertices": [-0.03943, 3.41661, -3.40669, 0.44639, -0.60147, 0.74745, -0.80544, -0.48852, -4.84114, 5.71083, -6.17375, -3.98391, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.67203, -0.83528, 0.89818, 0.54782, 0.72393, -0.89962, 0.96695, 0.59034, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.6017, -0.74785, 0.80456, 0.4903, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.72825, 3.35491, -0.72801, 3.35404, -3.41456, -0.24409, 0, 0, 0, 0, 0, 0, 0, 0, 0.87864, -1.09192, 3.27112, -1.31022, 1.63167, 3.05606, 0.80441, 0.49064, 3.27112, -1.31022, 1.63167, 3.05606, 2.95903, 1.53172, 2.12803, -1.33221, 0, 0, 0, 0, 0.99175, 1.67487, -0.65675, -0.59773, -0.27398, 1.25809, -0.27376, 1.25697, -1.28117, -0.09092, -0.7218, 0.89705, -0.72147, 0.89667, -0.96611, -0.58715, -0.60151, 0.74754, -0.60129, 0.74729, -0.80504, -0.48941, -3.6313, 1.75813, -3.63063, 1.75705, -2.11608, -3.34638, 0, 0, -2.1333, -2.96156, -5.43031, -5.09964, -7.46335, -8.74847, -2.8562, -2.16495, 0, 0, 2.23151, 1.69106, 3.03485, 2.29982, 0, 0, 0, 0, -3.57045, -2.70621, -3.57045, -2.70621, -3.36644, -2.5515, -2.09079, -4.23442, -2.09082, -4.23476, 3.94064, -2.70328, -2.10397, -1.59489, -7.83662, -3.83162, -7.34456, -3.73984, 2.95119, 1.11218, 3.0134, -1.08939, -4.70207, -3.56419, -8.78099, -6.65575, -3.78705, -2.87073, 0, 0, 0, 0, 0, 0, -5.12376, -7.8588, -5.84773, -6.42012, -0.37299, -0.28273, 1.40268, 1.06296, 3.08595, 2.33859, 2.10403, 1.5945], "curve": 0.333, "c2": 0.33, "c3": 0.672, "c4": 0.68}, {"time": 2.1667, "offset": 16, "vertices": [-0.02222, 1.9256, -1.92001, 0.25159, -0.33899, 0.42126, -0.45395, -0.27533, -2.72847, 3.21863, -3.47953, -2.24533, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.37875, -0.47076, 0.50621, 0.30875, 0.40801, -0.50702, 0.54497, 0.33272, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.33912, -0.42149, 0.45345, 0.27634, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.41044, 1.89083, -0.41031, 1.89034, -1.92445, -0.13757, 0, 0, 0, 0, 0, 0, 0, 0, 0.4952, -0.61541, 1.84361, -0.73844, 0.91961, 1.7224, 0.45337, 0.27652, 1.84361, -0.73844, 0.91961, 1.7224, 1.66771, 0.86328, 1.19936, -0.75083, 0, 0, 0, 0, 0.55895, 0.94396, -0.37014, -0.33688, -0.15441, 0.70906, -0.15429, 0.70843, -0.72207, -0.05124, -0.40681, 0.50558, -0.40662, 0.50536, -0.5445, -0.33092, -0.33901, 0.42131, -0.33889, 0.42117, -0.45372, -0.27583, -2.0466, 0.99088, -2.04622, 0.99028, -1.19262, -1.88602, 0, 0, 0.37875, -0.47076], "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 2.6667}]}, "s3": {"s3": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 14, "vertices": [6.63123, -4.3121, 3.05801, -7.2949, 0.4333, -7.89801, -1.90201, -19.85314, 17.50558, 9.5559, -12.73812, -15.34559, -17.14818, -10.18237, 3.0667, -7.9309, -1.92437, -8.28172, -4.59111, -7.15603, -6.32166, -5.68896, -7.94733, -0.27179, -6.72417, 4.24493, -4.9102, 6.25491, -3.08224, 7.3304, 1.74222, 2.65937, 3.17586, 0.16377, 3.10391, -0.68265, -10.32173, 12.19447, -6.73318, 14.48831, -16.78639, 18.12445, -11.40063, 21.91626, -21.08221, 12.87415, 8.42853, -3.80567, 7.1199, -5.89989, 9.14938, -1.36879, 5.61615, -7.1542, 3.52438, -8.38457, 7.3512, -5.35611, -7.23184, 13.95255, -13.3988, 8.20923, -12.21548, 25.94652, -23.86346, 15.90244, -29.91699, 13.327, -39.07742, 9.42618, -45.88843, 6.5255, -44.29721, 10.84283, -36.02686, 19.3365, -23.97864, 31.71022, -18.15939, 13.23256, -12.3024, 27.87241, -6.03558, 23.74712, 10.2681, 16.75273, 0.13928, 19.65329, 16.55594, 4.23584, 11.99094, 12.18639, 17.33701, 13.5255, 7.85678, 20.54153, 15.99759, 27.37128, -0.44214, 31.70572, 14.93005, 51.14998, -13.64423, 51.51115, 2.62939, 71.80301, -34.84796, 62.83653, -11.35745, 95.10866, -58.86618, 75.56049, 2.62589, 80.52798, -39.35959, 70.30432, 9.92813, 70.00278, -27.66937, 65.06677, 22.3251, 51.96553, -7.73489, 56.03104, 37.14981, 30.12082, 16.24619, 44.99121, 34.52386, 14.58519, 22.02295, 30.33034, 32.30206, 5.01399, 25.06696, 20.9879, 29.46326, -7.22725, 28.96271, 9.04137, -6.99359, 13.05048, -12.72714, 7.56107, 2.11975, 11.41409, 5.05933, 10.44691, -1.06308, 11.56064, 0.87672, 13.00725, 4.28, 12.31116, -2.69113, 12.75565, -10.00038, 30.46023, -1.59528, 32.01933, 2.38594, 26.662, 9.34656, 25.08153, -10.01792, 22.75817, -1.80118, 24.80005, 4.81424, 24.39287, -18.51906, 16.33805, -11.96381, 21.60427, -5.83075, 23.99716, 0.35451, 18.82964, 6.65152, 17.61941, 4.81292, 25.89296, 13.22118, 22.77741, 7.4691, 11.83129, 11.00586, 8.6397, 15.08199, 9.32406, -17.45908, -3.09405, 15.08199, 9.32406, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -13.05495, 2.51751, 13.22874, 20.34335, -24.2618, -0.45214, -0.04858, 1.41882, -1.14757, -0.83569, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -13.05495, 2.51751, 13.06181, 2.48243, 0.81355, 0.59166, -0.53723, -0.8498, -2.74243, -16.41827, 15.13268, 6.93429, -11.5011, -12.03275, -14.87129, -7.47662, 12.04832, 6.86752, -12.44166, 6.12608, 15.54312, 0.53127, 9.60321, -12.23315, -13.05495, 2.51751, 13.06181, 2.48243, -14.42876, 8.23696, 16.45106, -2.32505, -9.53589, 13.09611, 13.69871, -8.64797, -15.74543, -1.84623, 13.95033, 7.53149, 17.49741, 9.50936, 11.60019, 18.37658, -1.04538, 1.70602, 15.39485, 5.83624, 16.46083, 0.33286, 2.70819, -8.12638, 5.21064, 6.79872, -2.33066, -8.24195, -4.9606, -6.98219, 1.40121, 1.34001, 1.76979, 0.7922, -1.62555, -8.29469, -6.00889, -5.94382, -7.65459, -3.58333, -1.50274, 6.07894, 0.62404, 6.23074, 4.81216, 0.16448, 4.07146, -2.57035, 2.9731, -3.78736, 1.86615, -4.43867, -3.53955, 13.82364, 1.30363, 14.20983, 5.01236, 13.35912, 8.55515, 0.29236, 5.28566, -6.73325, 3.31732, -7.89124, 0, 0, 0, 0, 7.59793, -9.67896, 4.76825, -11.34384, 9.49178, 2.39664, 9.78693, -0.19788, 8.48236, 4.8889, 13.54416, -17.25371, 8.50031, -20.22124, 17.72858, -12.91769, 20.14215, 4.15723, 15.10223, 13.96953, 14.20491, -18.09533, 8.91522, -21.20735, 18.59323, -13.54774, 9.78693, -0.19788, 8.48236, 4.8889, 0, 0, 0, 0, 23.6424, 2.53021, 18.93658, 14.38663, 19.34848, 11.0195, 10.87387, 19.43573, 25.52151, 15.258, 13.96817, 26.25401, 4.35254, -10.35835, 9.08127, -6.61656, 9.78693, -0.19788, 8.48236, 4.8889, 9.8306, 10.14468, 3.1778, 13.76822, -5.39157, -4.67609, -6.44025, 17.98442, -8.647, 6.30119, -24.79379, 23.6909], "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "st": {"st": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 144, "vertices": [1.56757, 2.56271, -6.8584, -31.31458, -3.45361, -22.90106, 7.03193, -11.03623, 0.52203, -13.07584, 5.53498, -0.65182, 4.45346, -3.34854, 0, 0, 0, 0, 6.71576, 4.66041, 6.71576, 4.66041, 12.12347, 4.54075, 6.68922, 3.45866, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.48343, -3.61277, 0.27974, -3.63411, 0.27965, -3.63432, 0, 0, 0, 0, 0, 0, 0, 0, -0.80533, -6.02137, 0.46622, -6.05696, 0.46615, -6.05714, 0.13446, 6.07352, 0, 0, 0, 0, 0, 0, 0, 0, -4.28577, -7.59896, -2.60903, -8.32491, 3.41859, 8.02633, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10.16138, -2.62962, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10.16138, -2.62962, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10.05499, -7.43641, 0, 0, 0, 0, 0, 0, -0.10639, -4.80678, 0, 0, 0, 0, -0.10639, -4.80678, -0.27928, -12.6179, -5.08292, -11.55197, -0.10639, -4.80678, -0.45217, -20.42902, -8.22949, -18.70329, -0.10639, -4.80687, -2.51077, -4.10052, -0.63835, -28.84103, -15.06393, -24.60281, -11.61813, -26.40468, 0, 0, 0, 0, -0.17572, -24.5686, 0, 0, -10.35156, -16.33197, -9.62952, -16.76237, 0, 0, -9.69296, -5.81448, -9.42657, -6.22655, -11.29773, 0.23376], "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "s1": {"s1": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 102, "vertices": [-5.79477, -3.35471, -6.25058, -2.42416, -9.78232, -0.53732, -9.75916, 0.97124, -24.74069, -4.93918, -25.2247, -1.08189, -34.85818, -0.10479, -34.48151, 5.24583, -46.10049, 5.25592, -44.76978, 12.26534, -55.3591, 9.68307, -53.24069, 18.05908, -69.65125, 16.57475, -66.30829, 27.05954, -54.69901, 14.44098, -51.85937, 22.65988, -41.68109, 12.63264, -39.27234, 18.87801, -27.62469, 10.70128, -25.67786, 14.81552, -14.28699, 8.86736, -12.77905, 10.95955, -2.61563, 6.15636, -1.65112, 6.48778, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.61282, -5.63466, -6.40741, -3.70212, -12.95636, -0.36647, -15.95245, 1.15977, -17.33359, 6.69487, -16.91141, 6.814, -11.59714, 7.16682, -7.12057, 5.64384, -1.98154, 3.89505, -0.73364, 2.43295, -0.21008, 0.69511, -0.21008, 0.69511, -1.34012, 4.44353, -2.42676, 8.03954, -3.68137, 12.20439, -3.89105, 8.12513, -2.60132, 8.62541, -2.83005, 5.90919, -1.89221, 6.27312, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.43488, 1.11086, 4.1235, 1.97479, 4.37717, 1.31987, -1.94409, 7.76163, -3.45593, 7.21616, -2.30981, 7.66063, -1.48125, 5.91356, -2.63312, 5.49794, -1.11093, 4.43506, -1.97476, 4.12335, -1.48132, 5.91344, -2.63303, 5.49774, -0.23141, 9.17282, -1.48312, 9.05568, -1.03009, 9.46909, -2.31439, 9.23962, -8.08933, 14.88942, -1.83245, 16.84561, -0.48529, 21.50284, 7.71277, 20.07779, 12.28636, 24.98734, 20.85158, 18.45401, 4.58235, 24.27066, 13.45196, 20.71494, 15.77647, 8.5621, 17.84598, 1.93405, 17.59624, 6.57098, 18.7738, -0.59875, 19.42194, 4.21069, 19.56708, -3.47546, 20.50895, 1.6864, 23.04952, 11.06003, 25.59848, 5.39641, 27.52202, 1.1227, 11.42411, 1.95328, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12.49396, 3.69232, 12.96083, -1.32542, 7.30711, 3.96974, 8.26713, 0.89938, -4.46186, 1.87782, 7.97141, 4.33061, 9.01869, 0.98106, -3.60915, 6.64279, -0.81767, 7.51563, 8.87631, 14.52943, 13.72693, 10.0733, -7.89377, 13.99609, -1.99041, 15.94498, 2.74357, 15.44255, 8.39964, 13.24577, -0.67731, 17.96446, 0.94134, 11.08142, 5.0773, 9.8956, -1.80226, 16.56419, 5.18443, 8.19969, 7.90922, 5.61888, 7.06526, 6.64867, 3.2078, 12.09532, 7.55914, 9.97327, 6.12337, 10.91484, 1.93378, 11.6037, 6.19358, 10.00151, 4.76799, 10.75464, 1.36893, 3.07938, -7.36479, 5.28313, -8.01849, 4.22765, -8.70004, 2.53873, 7.1411, 9.12134, 5.17542, 10.3632, -8.12223, 5.20067, -8.75742, 4.0424, -9.38727, 2.20968, 4.20761, 8.38673, 2.44809, 9.05701, -14.27795, 2.65984, -14.5199, -0.24849, 7.1411, 9.12134, 5.17542, 10.3632, -14.67062, 2.56131, -14.88519, -0.42335, 7.1411, 9.12134, 5.17542, 10.3632, 6.69952, 9.44826, -14.67062, 2.56131, -14.88519, -0.42335, -14.77658, 1.86327, 5.17542, 10.3632, 6.69952, 9.44826, -14.88519, -0.42335, -14.77658, 1.86327, -3.07169, 6.41386, -2.05405, 6.80859, -2.05405, 6.80859, -2.05405, 6.80859, -2.05405, 6.80859, -0.63007, 2.08553, -0.63007, 2.08553, -3.34711, 1.86855, -10.12869, 2.99142, -2.82904, -0.62057, -2.9007, -0.17659, -5.89767, 3.15962, -5.3533, 4.02953, -16.88501, 4.42117, -16.02786, 6.96352], "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}}}}}}