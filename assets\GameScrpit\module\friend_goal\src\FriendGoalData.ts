import { JsonMgr } from "../../../game/mgr/JsonMgr";
import { FriendLabelMessage } from "../../../game/net/protocol/Friend";
import { times } from "../../../lib/utils/NumbersUtils";
import { FriendGoalModule } from "./FriendGoalModule";

export class FriendGoalData {
  private _friendLabel: FriendLabelMessage;

  get friendLabel() {
    return this._friendLabel;
  }

  setLabel(data: FriendLabelMessage) {
    this._friendLabel = data;
  }
}
