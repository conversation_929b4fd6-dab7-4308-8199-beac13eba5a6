import { _decorator, Component, Node } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import GameHttpApi from "../../httpNet/GameHttpApi";
const { ccclass, property } = _decorator;

/**
 *
 * hopewsw
 * Thu Oct 10 2024 14:00:19 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/ui_territory/UIActivity7Day.ts
 *
 */
@ccclass("UIActivity7Day")
export class UIActivity7Day extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_TERRITORY}?prefab/ui/UIActivity7Day`;
  }

  //=================================================
  public init(args: any): void {
    super.init(args);
  }

  protected onEvtShow(): void {
    super.onEvtShow();

    let cfg = GameHttpApi.getActivityConfig(1);
  }
}
