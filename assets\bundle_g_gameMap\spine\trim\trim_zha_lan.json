{"skeleton": {"hash": "FunKDyrOFC5wHt2LnCuwa47B7MU", "spine": "3.8.75", "x": -40.7, "y": -116.57, "width": 117, "height": 236, "images": "./images/", "audio": "E:/1/文件/PSD/动画拆分/人族装饰2/栅栏"}, "bones": [{"name": "root"}, {"name": "00000", "parent": "root", "x": -2.3, "y": -1.97}, {"name": "1", "parent": "00000", "length": 30, "rotation": 19.84, "x": -37.44, "y": -107.14}, {"name": "2", "parent": "00000", "length": 60.94, "rotation": 78.9, "x": 40.08, "y": -75.13}, {"name": "3", "parent": "00000", "length": 51.04, "rotation": 91.09, "x": 67.51, "y": -29.5}, {"name": "4", "parent": "00000", "length": 56.95, "rotation": 100.46, "x": 62.64, "y": 30.77}, {"name": "5", "parent": "00000", "length": 40, "rotation": 177.53, "x": 48.19, "y": 72.24}, {"name": "6", "parent": "00000", "length": 54.44, "rotation": 178.98, "x": 28.63, "y": 96.86}, {"name": "000", "parent": "00000"}, {"name": "1.1", "parent": "00000", "length": 60, "rotation": 19.84, "x": -11.6, "y": -97.81}], "slots": [{"name": "6", "bone": "6", "attachment": "6"}, {"name": "5", "bone": "5", "attachment": "5"}, {"name": "4", "bone": "4", "attachment": "4"}, {"name": "3", "bone": "3", "attachment": "3"}, {"name": "2", "bone": "2", "attachment": "2"}, {"name": "1", "bone": "1", "attachment": "1"}, {"name": "zhalan", "bone": "000", "color": "ecff006f", "blend": "additive"}, {"name": "canpo", "bone": "root"}], "skins": [{"name": "default", "attachments": {"zhalan": {"zhalan": {"x": 19.72, "y": 3.82, "width": 116, "height": 235}}, "canpo": {"canpo": {"x": 16.9, "y": 2.4, "width": 116, "height": 232}}, "1": {"1": {"type": "mesh", "uvs": [0.88322, 0, 0.90731, 0, 0.98966, 0.04766, 1, 0.06591, 1, 0.3753, 0.9652, 0.6287, 0.93029, 0.67811, 0.794, 0.67838, 0.79669, 0.70273, 0.66868, 0.79272, 0.48236, 0.92369, 0.37381, 1, 0, 1, 0, 0.82498, 0.0087, 0.54265, 0.07158, 0.45407, 0.15125, 0.46209, 0.1517, 0.69439, 0.27503, 0.6327, 0.4134, 0.5635, 0.59751, 0.45831, 0.78168, 0.35308, 0.8095, 0.0806], "triangles": [22, 0, 1, 1, 3, 22, 3, 1, 2, 4, 22, 3, 21, 22, 4, 5, 21, 4, 5, 7, 21, 6, 7, 5, 20, 21, 7, 9, 20, 7, 9, 7, 8, 19, 20, 9, 10, 19, 9, 18, 19, 10, 11, 18, 10, 17, 14, 15, 17, 15, 16, 13, 14, 17, 12, 13, 17, 17, 18, 11, 12, 17, 11], "vertices": [1, 9, 60.56, 23.03, 1, 1, 9, 62.51, 22.32, 1, 1, 9, 68.22, 17.28, 1, 1, 9, 68.69, 15.96, 1, 1, 9, 62.49, -1.21, 1, 1, 9, 54.6, -14.26, 1, 1, 9, 50.79, -15.98, 1, 1, 9, 39.76, -12.02, 1, 1, 9, 39.49, -13.45, 1, 1, 9, 27.33, -14.71, 1, 2, 2, 37.12, -16.54, 0.15867, 9, 9.64, -16.54, 0.84133, 2, 2, 26.81, -17.61, 0.54995, 9, -0.67, -17.61, 0.45005, 1, 2, -3.43, -6.7, 1, 1, 2, 0.07, 3.02, 1, 1, 2, 6.43, 18.43, 1, 1, 2, 13.29, 21.51, 1, 1, 2, 19.57, 18.74, 1, 1, 2, 14.96, 5.84, 1, 2, 2, 26.17, 5.66, 0.78378, 9, -1.31, 5.66, 0.21622, 1, 9, 11.27, 5.46, 1, 1, 9, 28.27, 5.93, 1, 1, 9, 45.28, 6.39, 1, 1, 9, 52.98, 20.71, 1], "hull": 23, "edges": [0, 44, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 42, 44, 34, 36, 36, 38, 38, 40, 40, 42, 20, 22, 16, 18, 18, 20, 22, 36], "width": 86, "height": 59}}, "2": {"2": {"x": 32.25, "y": -11.02, "rotation": -78.9, "width": 43, "height": 72}}, "3": {"3": {"x": 36.4, "y": -0.78, "rotation": -91.09, "width": 22, "height": 89}}, "4": {"4": {"x": 23.26, "y": 1.34, "rotation": -100.46, "width": 31, "height": 70}}, "5": {"5": {"x": 11.73, "y": -14.67, "rotation": -177.53, "width": 31, "height": 66}}, "6": {"6": {"x": 28.04, "y": -0.04, "rotation": -178.98, "width": 58, "height": 48}}}}], "animations": {"zha_lan": {}, "zha_lan_canpo": {"slots": {"2": {"attachment": [{"name": null}]}, "3": {"attachment": [{"name": null}]}, "4": {"attachment": [{"name": null}]}, "6": {"attachment": [{"name": null}]}, "5": {"attachment": [{"name": null}]}, "1": {"attachment": [{"name": null}]}, "canpo": {"attachment": [{"name": "canpo"}]}}}, "zha_lan_jiesuo": {"slots": {"1": {"attachment": [{"name": null}, {"time": 0.0333, "name": "1"}]}, "zhalan": {"color": [{"time": 0.5667, "color": "ebff00a7"}, {"time": 0.7, "color": "ebff0078"}, {"time": 0.8333, "color": "ebff0000"}], "attachment": [{"time": 0.5667, "name": "zhalan"}]}}, "bones": {"2": {"scale": [{"x": 0, "curve": "stepped"}, {"time": 0.1, "x": 0}, {"time": 0.2}]}, "3": {"scale": [{"x": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0}, {"time": 0.2667}]}, "4": {"scale": [{"x": 0, "curve": "stepped"}, {"time": 0.2667, "x": 0}, {"time": 0.3667}]}, "5": {"scale": [{"x": 0, "curve": "stepped"}, {"time": 0.3667, "x": 0}, {"time": 0.4667}]}, "6": {"scale": [{"x": 0, "curve": "stepped"}, {"time": 0.4667, "x": 0}, {"time": 0.5667}]}, "000": {"translate": [{"time": 0.5667}, {"time": 0.7, "x": 4.29, "y": 7.29}], "scale": [{"time": 0.5667}, {"time": 0.7, "x": 1.16, "y": 1.16}]}, "1.1": {"scale": [{"x": 0}, {"time": 0.1}]}}}}}