import { _decorator, Component, Node } from "cc";
import { <PERSON><PERSON><PERSON>and<PERSON> } from "../../game/mgr/ApiHandler";
import { HuntBossUpdateMessage } from "../../game/net/protocol/Hunt";
import { HuntActionSubCmd } from "../../game/net/cmd/CmdData";
const { ccclass, property } = _decorator;
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class HuntSubscriber {
  public register() {
    //订阅服务器消息
    // 	路由: 11 - 3  --- 广播推送: com.feamon.proto.maintask.MainTaskMessage (主动推送主线任务变更情况)
    ApiHandler.instance.subscribe(
      HuntBossUpdateMessage,
      HuntActionSubCmd.bossUpdateMessage,
      (data: HuntBossUpdateMessage) => {
        log.log("广播推送-----boss信息变化", data);
      }
    );
  }
  public unRegister() {
    //取消订阅服务器消息
  }
}
