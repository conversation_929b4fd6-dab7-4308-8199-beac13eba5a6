import { instantiate, Node } from "cc";
import { ListAdapter } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { PupilRankViewHolder } from "./PupilRankViewHolder";
import { PupilMessage } from "../../../../game/net/protocol/Pupil";

export class PupilRankAdapter extends ListAdapter {
  private item: Node;
  private _datas: PupilMessage[];

  constructor(item: Node) {
    super();
    this.item = item;
  }
  public setDatas(data: PupilMessage[]) {
    this._datas = data;
    this.notifyDataSetChanged();
  }

  getCount(): number {
    return this._datas.length;
  }

  getViewType(position: number): number {
    return 0;
  }
  onCreateView(viewType: number): Node {
    let item = instantiate(this.item);
    item.active = true;
    return item;
  }
  onBindData(node: Node, position: number): void {
    node.getComponent(PupilRankViewHolder).onItemRender(this._datas[position], position);
  }
}
