import { _decorator, Component, EditBox, Node } from "cc";
import { SceneLogin } from "../../script_game/scene_login/SceneLogin";
import { TipsMgr } from "../../platform/src/TipsHelper";
import CenterHttpApi from "../../GameScrpit/game/httpNet/CenterHttpApi";
import { RouteSceneLogin } from "../../script_game/scene_login/RouteSceneLogin";
import { GameData } from "../../GameScrpit/game/GameData";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";

const log = Logger.getLoger(LOG_LEVEL.STOP);
const { ccclass, property } = _decorator;

/**
 * ivan_huang
 * Wed Mar 05 2025 16:55:31 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/ui_game_health/UIGameHealthAuth.ts
 * https://docs.cocos.com/creator/3.8/manual/zh/
 *
 */

@ccclass("UIGameHealthAuth")
export class UIGameHealthAuth extends Component {
  @property(EditBox)
  private edtName: EditBox;
  @property(EditBox)
  private edtIDCard: EditBox;

  protected start(): void {}

  private onClickCommit() {
    let name = this.edtName.string;
    let idCard = this.edtIDCard.string;
    if (!name || name.trim() === "") {
      TipsMgr.showTip("请输入名称");
      return;
    }
    if (!idCard || idCard.trim() === "") {
      TipsMgr.showTip("请输入身份证");
      return;
    }
    if (!this.isValidChineseID(idCard)) {
      TipsMgr.showTip("请输入合法身份证号码");
      return;
    }
    this.realNameAuth(name, idCard);

    // SceneLogin.routeMgr.back();
  }

  /**
   * 当res.status == 0 时表示成功，res.status == 1 时表示认证中 其他情况表示失败
   * 当认证中的时候需要在第1,3,7,15秒继续请求这个接口完成最多4次查询，如果4次都失败则认证失败
   * @param name
   * @param idCard
   */
  private realNameAuth(name: string, idCard: string, times: number = 0) {
    if (times >= 4) {
      TipsMgr.showTip("实名认证失败");
      return;
    }
    let timeInterval = 1 << times;
    TipsMgr.setEnableTouch(false, timeInterval);

    // 网络请求
    CenterHttpApi.realNameAuth(name, idCard)
      .then((res: any) => {
        if (res.code === 200) {
          if (res.status == 0) {
            if (res.age < 16) {
              TipsMgr.showTip("未满16周岁");
              return;
            }
            TipsMgr.showTip("实名认证成功");
            GameData.instance.forceLogoutTime = res.force_logout_time;
            GameData.instance.age = res.age;
            SceneLogin.routeMgr.back();
            SceneLogin.routeMgr.showDialog(RouteSceneLogin.UIGameHealthAuthResult);
          } else if (res.status == 1) {
            setTimeout(() => {
              this.realNameAuth(name, idCard, times + 1);
            }, timeInterval * 1000);
          }
        } else {
          TipsMgr.showTip("实名认证失败");
        }
      })
      .catch((err: any) => {
        TipsMgr.showTip("实名认证失败");
      });
  }
  private isValidChineseID(id: string): boolean {
    // 基础正则校验
    if (!/^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dxX]$/i.test(id)) {
      log.log("长度不正确");
      return false;
    }
    // 提取日期部分
    const year = parseInt(id.slice(6, 10));
    const month = parseInt(id.slice(10, 12)) - 1; // JS月份从0开始
    const day = parseInt(id.slice(12, 14));
    log.log(year);
    // 日期对象验证
    const date = new Date(year, month, day);
    return date.getFullYear() === year && date.getMonth() === month && date.getDate() === day;
  }
}
