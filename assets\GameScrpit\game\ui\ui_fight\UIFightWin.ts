import { _decorator, Label, Layout, Node, ProgressBar, sp, Tween, tween, v3 } from "cc";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { UINode } from "../../../lib/ui/UINode";
import Formate from "../../../lib/utils/Formate";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import ToolExt from "../../common/ToolExt";
import { JsonMgr } from "../../mgr/JsonMgr";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { tweenTagEnum } from "../../GameDefine";
import FmUtils from "../../../lib/utils/FmUtils";
import { IConfigLeaderRecord } from "../../../module/player/PlayerConfig";
import { ItemEnum } from "../../../lib/common/ItemEnum";
import { AudioMgr, AudioName } from "../../../../platform/src/AudioHelper";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { FightModule } from "../../../module/fight/src/FightModule";
import { CityRouteName } from "../../../module/city/CityConstant";

const { ccclass, property } = _decorator;

@ccclass("UIFightWin")
export class UIFightWin extends UINode {
  protected _isAddToTop: boolean = true;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_FIGHT}?prefab/ui/UIFightWin`;
  }
  protected dependOn(): Array<BundleEnum> {
    return [BundleEnum.BUNDLE_COMMON_UI, BundleEnum.BUNDLE_COMMON_ITEM];
  }

  private _data: number[] = [];
  private _chapterId: number = null;

  public init(args: any): void {
    super.init(args);
    this._data = args.resAddList;
    this._chapterId = args.chapterId;
  }

  protected onEvtShow(): void {
    TipsMgr.setEnableTouch(true);
    AudioMgr.instance.playEffect(AudioName.Effect.战斗胜利);
    this.getNode("bg").scale = v3(0, 0, 0);
    let itemListMap = ToolExt.traAwardItemMapList(this._data);
    this.setGongDe(itemListMap);
    this.setBloom();
    this.getNode("victory_appear")
      .getComponent(sp.Skeleton)
      .setEventListener((animation, event) => {
        if (event["data"].name == "appear") {
          tween(this.getNode("bg"))
            .tag(tweenTagEnum.UIFightWin_Tag)
            .to(0.1, { scale: v3(1, 1, 1) })
            .call(() => {
              this.loadItem(itemListMap);
            })
            .start();

          this.getNode("victory_appear").getComponent(sp.Skeleton).setEventListener(null);
        }
      });
    this.getNode("victory_appear")
      .getComponent(sp.Skeleton)
      .setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
        //清空监听
        if ("victory_appear" == trackEntry.animation.name) {
          this.getNode("victory_appear").getComponent(sp.Skeleton).setAnimation(0, "victory_lasts", true);
          this.getNode("victory_appear").getComponent(sp.Skeleton).setCompleteListener(null);
        }
      });

    this.getNode("victory_appear").getComponent(sp.Skeleton).setAnimation(0, "victory_appear", false);

    // tween(this.getNode("bg"))
    //   .tag(tweenTagEnum.UIFightWin_Tag)
    //   .to(0.1, { scale: v3(1, 1, 1) })
    //   .call(() => {})
    //   .start();
  }

  private setGongDe(itemListMap: Array<{ id: number; num: number }>) {
    let gongDeData = null;
    for (let i = 0; i < itemListMap.length; i++) {
      if (itemListMap[i].id == 5) {
        gongDeData = itemListMap[i];
        break;
      }
    }
    if (gongDeData == null) {
      return;
    }
    let gongDe = PlayerModule.data.getItemNum(ItemEnum.功德_5);
    let level = PlayerModule.data.getPlayerInfo().level;

    let leaderInfo: IConfigLeaderRecord = PlayerModule.data.getConfigLeaderData(level + 1);
    let str = `${Formate.format(gongDe)}/${Formate.format(leaderInfo.virtue)}`;

    this.getNode("gongdeBarLab").getComponent(Label).string = str;
    this.getNode("gongDeAddLab").getComponent(Label).string = "+" + gongDeData.num;
    this.getNode("gongDeProgress").getComponent(ProgressBar).progress = gongDe / leaderInfo.virtue;
  }

  private setBloom() {
    let bloom = PlayerModule.data.playerBattleAttrResponse.speed;
    let level = PlayerModule.data.getPlayerInfo().level;
    let leaderInfo: IConfigLeaderRecord = PlayerModule.data.getConfigLeaderData(level + 1);

    this["bloomProgressBar"].getComponent(ProgressBar).progress = bloom / leaderInfo.speed;
    this["boolBarLab"].getComponent(Label).string = `${Formate.format(bloom)}/${Formate.format(leaderInfo.speed)}`;
  }

  private loadItem(itemListMap: Array<{ id: number; num: number }>) {
    let layerList = ToolExt.minuteItemLayer(itemListMap, 6);
    let index = 0;
    for (let i = 0; i < layerList.length; i++) {
      let rowList = layerList[i];
      let horLayer = ToolExt.clone(this.getNode("horLayer"), this);
      horLayer.active = true;
      this.getNode("itemContent").addChild(horLayer);
      if (layerList.length == 1 || i > 0) {
        horLayer.getComponent(Layout).resizeMode = Layout.ResizeMode.CONTAINER;
      }

      for (let i = 0; i < rowList.length; i++) {
        let item = ToolExt.clone(this.getNode("Item"), this);
        horLayer.addChild(item);
        item.active = true;
        this.itemAct(item, rowList[i], index);
        index++;
      }
    }
  }

  private itemAct(node: Node, info: any, index) {
    FmUtils.setItemNode(node, info.id, info.num);
    node.scale = v3(0, 0, 0);
    tween(node)
      .tag(tweenTagEnum.UIFightWin_Tag)
      .delay(0.08 * index)
      .to(0.16, { scale: v3(1, 1, 1) })
      .start();
  }

  private on_click_btn_close() {
    UIMgr.instance.back();
  }

  protected onEvtClose(): void {
    Tween.stopAllByTag(tweenTagEnum.UIFightWin_Tag);
    MsgMgr.emit(MsgEnum.ON_GUIDE_NEXT, "FIGHT_FINISH");
    this.getNode("victory_appear").getComponent(sp.Skeleton).setEventListener(null);
    this.getNode("victory_appear").getComponent(sp.Skeleton).setCompleteListener(null);

    const chapterId = FightModule.data.chapterId;
    if (chapterId % 10000 == 1) {
      let c_copyMain = JsonMgr.instance.jsonList.c_copyMain;
      let db = c_copyMain[this._chapterId];
      let cfgBuild = JsonMgr.instance.jsonList.c_build[db.buildId];
      if (cfgBuild.isShow) {
        TipsMgr.topRouteCtrl.show(CityRouteName.TopCityBuild, { cityId: cfgBuild.id });
      }
    }
  }
}
