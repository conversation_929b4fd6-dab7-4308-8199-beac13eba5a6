import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { GuideRouteEnum } from "../../../ext_guide/GuideDefine";
import { ApiHandler } from "../../../game/mgr/ApiHandler";
import { EventSubCmd } from "../../../game/net/cmd/CmdData";
import { EventTrainMessage } from "../../../game/net/protocol/WorldEvent";
import { EventActionModule } from "./EventActionModule";

export class EventActionSubscriber {
  private getNewEvent(res: EventTrainMessage) {
    EventActionModule.data.eventTrainMessage = res;
  }

  public register() {
    ApiHandler.instance.subscribe(EventTrainMessage, EventSubCmd.newEvent, this.getNewEvent);
  }
  public unRegister() {
    ApiHandler.instance.unSubscribe(EventSubCmd.newEvent, this.getNewEvent);
  }
}
