import { _decorator, Component, Label, Node, tween, UITransform, v2, v3, Vec3 } from "cc";
import { IUIModule, UIState } from "../interface/IUIModule";
import MsgEnum from "../../GameScrpit/game/event/MsgEnum";
import MsgMgr from "../../GameScrpit/lib/event/MsgMgr";
import FightManager from "../../GameScrpit/game/fight/manager/FightManager";
import TipMgr from "../../GameScrpit/lib/tips/TipMgr";
import { XiaoyouxiUIWin } from "./XiaoyouxiUIWin";
import { actionDB } from "../../GameScrpit/game/fight/section/AnimationSection";
import { TipsMgr } from "../../platform/src/TipsHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("XiaoyouxiUIFight")
export class XiaoyouxiUIFight extends Component {
  @property(Node)
  main: Node = null;

  @property(Node)
  world_bg: Node = null;

  @property(Node)
  roundLab: Node = null;

  @property(Node)
  fightPoint: Node = null;

  @property(Node)
  XiaoyouxiUIWin: Node = null;

  private data = null;

  private _round: number = 0;
  private _maxRound = null;
  // 抖动cd
  private shakeCd: number = 0;

  protected onRegEvent(): void {
    MsgMgr.on(MsgEnum.ON_FIGHT_ROUND_UPDATE, this.setRoundShow, this);
    MsgMgr.on(MsgEnum.ON_SHAKE_WORLD, this.shakeWorld, this);
    MsgMgr.on(MsgEnum.ON_FIGHT_END, this.fightEnd, this);
  }

  protected onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_FIGHT_ROUND_UPDATE, this.setRoundShow, this);
    MsgMgr.off(MsgEnum.ON_SHAKE_WORLD, this.shakeWorld, this);
    MsgMgr.off(MsgEnum.ON_FIGHT_END, this.fightEnd, this);
  }

  protected onLoad(): void {
    this.XiaoyouxiUIWin.active = false;
    this.onRegEvent();
  }

  protected onDestroy(): void {
    this.onDelEvent();
    // FightManager.instance.exit();
  }

  public init(args) {
    let data = {
      a: 1,
      b: 1,
      f: 15,
      c: { a: 11111, b: 1, c: 9820216, f: 9820216, d: args.playerId },
      d: { a: 22222, b: 2, c: 8303306, f: 8303306, d: args.bossId },
      e: [
        {
          a: 1,
          b: { "1": { a: 9820216, d: 9820216, b: 1 }, "2": { a: 8303306, d: 8303306, b: 1 } },
          c: [
            { a: 1, b: 2, c: 1, d: { "2": { a: 876531 } } },
            { a: 1, b: 2, c: 2, d: { "2": { a: 798532 } } },
            { a: 2, b: 1, c: 1, d: { "1": { a: 632901 } } },
            { a: 1, b: 2, c: 3, d: { "2": { a: 915422 } } },
          ],
        },

        {
          a: 2,
          b: { "1": { a: 9187315, d: 9820216, b: 1 }, "2": { a: 5712821, d: 8303306, b: 1 } },
          c: [
            { a: 1, b: 2, c: 4, d: { "2": { a: 1658554 } } },
            { a: 2, b: 1, c: 1, d: { "1": { a: 568231 } } },
            { a: 2, b: 1, c: 2, d: { "1": { a: 621022 } } },
          ],
        },
        {
          a: 3,
          b: { "1": { a: 7998062, d: 9820216, b: 1 }, "2": { a: 4054267, d: 8303306, b: 1 } },
          c: [
            { a: 1, b: 2, c: 1, d: { "2": { a: 853211, c: 1 } } },
            { a: 2, b: 1, c: 6, d: { "2": { c: 0 } } },
          ],
        },

        {
          a: 4,
          b: { "1": { a: 7998062, d: 9820216, b: 1 }, "2": { a: 3201056, d: 8303306, b: 1 } },
          c: [
            { a: 1, b: 2, c: 1, d: { "2": { a: 712024, c: 1 } } },
            { a: 2, b: 1, c: 6, d: { "2": { c: 0 } } },
          ],
        },

        {
          a: 5,
          b: { "1": { a: 7998062, d: 9820216, b: 1 }, "2": { a: 2489032, d: 8303306, b: 1 } },
          c: [
            { a: 1, b: 2, c: 1, d: { "2": { a: 975753 } } },
            { a: 1, b: 2, c: 2, d: { "2": { a: 801255 } } },
            { a: 1, b: 2, c: 2, d: { "2": { a: 712024 } } },
          ],
        },
      ],
    };

    this._maxRound = data.f;

    let posMap = new Map([
      [1, v2(-200, -200)],
      [2, v2(200, -200)],
    ]);
    FightManager.instance.start({
      main: this.node.getChildByName("main"),
      parent: this.fightPoint,
      fight: data,
      posMap: posMap,
      playId: 1,
      speed: 1,
      contentSize: this.main.getComponent(UITransform),
    });
  }

  protected update(dt: number): void {
    FightManager.instance.tick(dt);
  }

  private async setRoundShow(roundNumber: number) {
    this._round = roundNumber;
    this.roundLab.getComponent(Label).string = `第${this._round}/${this._maxRound}回合`;
  }

  private shakeWorld() {
    let ts = new Date().valueOf();
    if (this.shakeCd < ts) {
      // 0.2秒内不在抖动
      this.shakeCd = ts + 200;
      this.shake(this.world_bg, v3(0, 0));
    }
  }

  /** 抖动具体方法 */
  private shake(node: Node, endPos: Vec3, amplitude: number = 20) {
    const tickTime = 0.05;

    // x轴震动
    let t1 = tween(node)
      .set({ position: endPos })
      .by(tickTime / 2, { position: v3(-amplitude / 2, 0) })
      .by(tickTime, { position: v3(+amplitude, 0) })
      .by(tickTime, { position: v3(-amplitude / 2, 0) });

    let t2 = tween(node)
      .delay(tickTime / 4)
      .by(tickTime / 2, { position: v3(0, amplitude / 2) })
      .by(tickTime, { position: v3(0, -amplitude) })
      .by(tickTime, { position: v3(0, +amplitude / 2) })
      .set({ position: endPos });
    tween(node).parallel(t1, t2).start();
  }

  private fightEnd() {
    TipsMgr.setEnableTouch(true);
    //TipMgr.showTip("在这里写战斗结束后，该窗口要执行什么事件");
    log.error("在这里写战斗结束后，该窗口要执行什么事件");
    this.XiaoyouxiUIWin.active = true;
    this.XiaoyouxiUIWin.getComponent(XiaoyouxiUIWin).init();
    MsgMgr.off(MsgEnum.ON_FIGHT_END, this.fightEnd, this);
  }
}
