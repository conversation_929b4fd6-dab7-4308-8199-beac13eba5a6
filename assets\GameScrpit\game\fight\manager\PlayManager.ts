import { _decorator, isValid } from "cc";
import FightManager from "./FightManager";
import ManagerSection from "./ManagerSection";
import GameObject from "../../../lib/object/GameObject";
import GamePlay, { BattleSceneEnum } from "../play/GamePlay";
import { GPChapter } from "../play/GPChapter";
import { GPCompete } from "../play/GPCompete";
import { GPHuntPet } from "../play/GPHuntPet";
import { GPHuntBoss } from "../play/GPHuntBoss";
import { GPClubBoss } from "../play/GPClubBoss";
import { GPWatch } from "../play/GPWatch";
import { FightModule } from "../../../module/fight/src/FightModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { GPFracture } from "../play/GPFracture";
const log = Logger.getLoger(LOG_LEVEL.STOP);
const { ccclass, property } = _decorator;

class playData {
  id: number;
  name: string;
  scrpit: string;
}

const playMap: Map<BattleSceneEnum, any> = new Map([
  [BattleSceneEnum.NULL, GamePlay],
  [BattleSceneEnum.CHAPTER, GPChapter],
  [BattleSceneEnum.COMPETE, GPCompete],
  [BattleSceneEnum.HUNT_PET, GPHuntPet],
  [BattleSceneEnum.HUNT_BOSS, GPHuntBoss],
  [BattleSceneEnum.CLUB_BOSS, GPClubBoss],
  [BattleSceneEnum.FRACTURE_MONSTER, GPFracture],
  [BattleSceneEnum.WATCH, GPWatch],
]);

@ccclass("PlayManager")
export default class PlayManager extends ManagerSection {
  public static sectionName(): string {
    return "PlayManager";
  }

  private _playId: number;

  public curPlay: GamePlay = null;
  public onInit(go: GameObject, args) {
    super.onInit(go, args);
    //log.log("args", args);
    FightModule.instance.nameMap = args.nameMap;
    FightModule.instance.fightData = args.fight;
    FightModule.instance.posMap = args.posMap;
    FightModule.instance.goRoleNodeMap = args.goRoleNodeMap;
    this._playId = args.playId;
    this.createRoot("PlayRoot", FightManager.instance);
    this.ready();
  }

  public onStart(): void {
    super.onStart();
    this.callObject(this._playId);
  }

  public callObject(playId: BattleSceneEnum) {
    if (FightManager.instance.fightOver == true) {
      return;
    }
    if (isValid(this.root) == false) {
      return;
    }
    if (!playMap.has(playId)) {
      log.error("没有找到对应的玩法配置表id:" + playId);
      return;
    }
    let obj = new (playMap.get(playId))();
    this.curPlay = obj;
    this.addChild(obj);
  }

  public onRemove(): void {
    super.onRemove();
    //log.log("玩法管理器移除");
  }
}
