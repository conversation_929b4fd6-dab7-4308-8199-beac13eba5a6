import { _decorator, instantiate, Label, Node } from "cc";
import { ListAdapter, ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { ClubModule } from "../../../../module/club/ClubModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { AssistMessage, AssistResponse } from "../../../net/protocol/Assist";
import { LangMgr } from "../../../mgr/LangMgr";
import { PlayerModule } from "db://assets/GameScrpit/module/player/PlayerModule";
import { FmButton } from "../../../../../platform/src/core/ui/components/FmButton";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
import FmUtils from "db://assets/GameScrpit/lib/utils/FmUtils";
const log = Logger.getLoger(LOG_LEVEL.DEBUG);
const { ccclass, property } = _decorator;
@ccclass("ClubAssistViewHolder")
export class ClubAssistViewHolder extends ViewHolder {
  @property(Node)
  private btnHeader: Node = null; // 头像按钮
  @property(Node)
  private lblDes: Node = null; // 描述
  @property(Node)
  private lblHelpTimes: Node = null; // 求助次数

  @property(Node)
  private btnAssist: Node = null; // 求助按钮
  _data: AssistMessage;
  public init() {}
  public updateData(data: any) {
    this._data = data;
    let desStr = "";
    if (this._data.assistType == 1) {
      let args2 = LangMgr.txMsgCode(421);
      desStr = LangMgr.txMsgCode(500, [this._data.playerBaseMessage.nickname, args2]);
    } else if (this._data.assistType == 2) {
      desStr = LangMgr.txMsgCode(501, [this._data.playerBaseMessage.nickname, this._data.descMap['"enemyName"']]);
    } else if (this._data.assistType == 3) {
      // 服务端Map的key是双引号，需要转义
      let arg2 = this._data.descMap['"enemyName"'];
      desStr = LangMgr.txMsgCode(502, [this._data.playerBaseMessage.nickname, arg2]);
    }
    this.lblDes.getComponent(Label).string = desStr;
    FmUtils.setHeaderNode(this.btnHeader, this._data.playerBaseMessage);
    if (this._data.assistType == 1) {
      this.lblHelpTimes.getComponent(Label).string = "0%";
    } else {
      let progress = (1 - (this._data.progress < 0 ? 0 : this._data.progress) / this._data.maxProgress) * 100;
      if (progress > 100) {
        progress = 100;
      } else if (progress < 0) {
        progress = 0;
      }
      this.lblHelpTimes.getComponent(Label).string = `${Math.floor(progress)}%`;
    }
    let isAssist = false;
    for (let i = 0; i < this._data.assistMembers.length; i++) {
      let assistMember = this._data.assistMembers[i];
      if (assistMember.userId == PlayerModule.data.playerId) {
        isAssist = true;
        break;
      }
    }
    if (isAssist) {
      this.btnAssist.getComponent(FmButton).btnEnable = false;
    } else if (this._data.progress <= 0 && this._data.assistType != 1) {
      this.btnAssist.getComponent(FmButton).btnEnable = false;
    } else {
      this.btnAssist.getComponent(FmButton).btnEnable = true;
    }
  }

  private onClickAssist() {
    AudioMgr.instance.playEffect(1538);

    if (!this.btnAssist.getComponent(FmButton).btnEnable) {
      log.log("onClickAssist", "button is disable");
      return;
    }
    ClubModule.api.doAssist(this._data.id, (res: AssistResponse) => {
      log.log("doAssist", res);
      if (res.code != 0) {
        TipsMgr.showTip("协助失败");
      }
      if (res.assistMessage) {
        this._data = res.assistMessage;
      } else {
        this._data.progress = res.progress;
        this._data.assistMembers = res.assistMembers;
      }
      this.updateData(this._data);
    });
  }
}
export class ClubAssistAdapter extends ListAdapter {
  private item: Node;
  private data: any[];
  public constructor(item: Node) {
    super();
    this.item = item;
  }
  public setData(data: any[]) {
    this.data = data;
    this.notifyDataSetChanged();
  }

  getViewType(position: number): number {
    return 0;
  }
  onCreateView(viewType: number): Node {
    let item = instantiate(this.item);
    item.active = true;
    item.getComponent(ClubAssistViewHolder).init();
    return item;
  }
  onBindData(view: Node, position: number): void {
    view.getComponent(ClubAssistViewHolder).updateData(this.data[position]);
  }
  getCount(): number {
    return this.data.length;
  }
}
