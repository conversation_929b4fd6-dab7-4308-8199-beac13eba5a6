{"skeleton": {"hash": "Id7ByZjEpl4Zlu+mli/mwpKypGc", "spine": "3.8.75", "x": -66.68, "y": -45.46, "width": 121, "height": 115.61, "images": "./images/", "audio": "D:/建筑升级/粮仓"}, "bones": [{"name": "root"}, {"name": "yinz", "parent": "root", "x": -0.41, "y": -21.64}, {"name": "1", "parent": "yinz", "x": -1.42, "y": 6.84}, {"name": "3", "parent": "1", "x": 1.65, "y": 52.39}, {"name": "4", "parent": "root", "x": -35.57, "y": -22.58}, {"name": "5", "parent": "root", "x": 31.22, "y": -22.58}, {"name": "6", "parent": "root", "x": -2.06, "y": 28.39}, {"name": "guang", "parent": "root", "x": -3.47, "y": 6.96}, {"name": "fx", "parent": "root", "x": -3.1, "y": -31.48}], "slots": [{"name": "yinz", "bone": "yinz", "attachment": "yinz"}, {"name": "1", "bone": "1", "attachment": "1"}, {"name": "2", "bone": "3", "attachment": "2"}, {"name": "5", "bone": "6", "attachment": "5"}, {"name": "4", "bone": "5", "attachment": "4"}, {"name": "3", "bone": "4", "attachment": "3"}, {"name": "guang", "bone": "guang", "color": "fff168ff", "attachment": "guang", "blend": "additive"}, {"name": "fx", "bone": "fx", "attachment": "fx"}], "skins": [{"name": "default", "attachments": {"fx": {"fx": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [56.33, -13.98, -53.67, -13.98, -53.67, 48.02, 56.33, 48.02], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 110, "height": 62}}, "yinz": {"yinz": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [54.73, -16.21, -56.27, -16.21, -56.27, 15.79, 54.73, 15.79], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 111, "height": 32}}, "guang": {"guang": {"type": "mesh", "color": "ffffffbd", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [55.96, -45.63, -63.04, -45.63, -63.04, 60.37, 55.96, 60.37], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 119, "height": 106}}, "1": {"1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [37.14, -2.05, -58.86, -2.05, -58.86, 57.95, 37.14, 57.95], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 96, "height": 60}}, "2": {"2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [53.49, -26.44, -59.51, -26.44, -59.51, 32.56, 53.49, 32.56], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 113, "height": 59}}, "3": {"3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [20.89, -5.26, -31.11, -5.26, -31.11, 44.74, 20.89, 44.74], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 52, "height": 50}}, "4": {"4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [19.1, -5.26, -21.9, -5.26, -21.9, 53.74, 19.1, 53.74], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 41, "height": 59}}, "5": {"5": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [16.38, -16.24, -15.62, -16.24, -15.62, 14.76, 16.38, 14.76], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 31}}}}], "animations": {"liang_cang": {"slots": {"guang": {"color": [{"color": "ffffff00"}]}, "fx": {"attachment": [{"name": null}]}}, "bones": {"4": {"rotate": [{"angle": 1.2}]}, "guang": {"translate": [{"y": 4}]}}}, "liang_cang_canpo": {"slots": {"5": {"attachment": [{"name": null}]}, "2": {"attachment": [{"name": null}]}, "1": {"attachment": [{"name": null}]}, "yinz": {"attachment": [{"name": null}]}, "4": {"attachment": [{"name": null}]}, "guang": {"color": [{"color": "ffffff00"}], "attachment": [{"name": null}]}, "3": {"attachment": [{"name": null}]}}, "bones": {"4": {"rotate": [{"angle": 1.2}]}, "guang": {"translate": [{"y": 4}]}}}, "liang_cang_jiesuo": {"slots": {"guang": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.9, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1, "color": "ffffff00"}]}, "fx": {"attachment": [{"name": null}]}}, "bones": {"yinz": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "y": 8, "curve": 0.25, "c3": 0.75}, {"time": 0.2}], "scale": [{"x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 1.1, "y": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 0.96, "y": 0.98, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "1": {"translate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "y": 12, "curve": 0.25, "c3": 0.75}, {"time": 0.4}], "scale": [{"x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 0.6, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 0.96, "y": 0.98, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 1.02, "y": 1.02, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "3": {"translate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "y": 6, "curve": 0.25, "c3": 0.75}, {"time": 0.5}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 0, "y": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 1.16, "y": 0.98, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 0.96, "y": 1.04, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 1.04, "y": 1.02, "curve": 0.25, "c3": 0.75}, {"time": 0.7333}]}, "6": {"scale": [{"x": 0, "curve": "stepped"}, {"time": 0.5333, "x": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "4": {"rotate": [{"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 7.2}, {"time": 0.5667, "angle": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 1.2}], "translate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": 12, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}], "scale": [{"x": 0.8, "y": 0, "curve": "stepped"}, {"time": 0.2333, "x": 0.8, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 1.04, "y": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "5": {"translate": [{"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 6, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}], "scale": [{"x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 0.9, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 1.09, "y": 1.02, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5667}], "shear": [{"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 2.4, "y": 2.4}, {"time": 0.7667, "x": -2.4, "y": -3.6, "curve": 0.25, "c3": 0.75}, {"time": 0.8667}]}, "guang": {"translate": [{"y": 4}], "scale": [{"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 1.3, "y": 1.3}]}}}}}