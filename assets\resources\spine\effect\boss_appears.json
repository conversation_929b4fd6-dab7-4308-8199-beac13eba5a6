{"skeleton": {"hash": "SNMutwedkyRwY/WafydGujHoW5o=", "spine": "3.8.75", "x": -75, "y": -33, "width": 150, "height": 66, "images": "./002/", "audio": "D:/spine导出/boss出现动画/002"}, "bones": [{"name": "root"}, {"name": "ya<PERSON>l", "parent": "root"}, {"name": "yan111", "parent": "ya<PERSON>l", "x": -18.44, "y": 26.64}, {"name": "yan", "parent": "yan111"}, {"name": "yan2", "parent": "yan111"}, {"name": "yan3", "parent": "yan111"}, {"name": "yan4", "parent": "yan111"}, {"name": "yan5", "parent": "yan111"}, {"name": "yan6", "parent": "yan111"}, {"name": "yan112", "parent": "ya<PERSON>l", "x": -19.52, "y": 26.64, "scaleY": 1.91}, {"name": "yan7", "parent": "yan112"}, {"name": "yan8", "parent": "yan112"}, {"name": "yan9", "parent": "yan112"}, {"name": "yan10", "parent": "yan112"}, {"name": "yan11", "parent": "yan112"}, {"name": "yan12", "parent": "yan112"}, {"name": "bone", "parent": "root"}], "slots": [{"name": "ef/y", "bone": "yan", "color": "000000ff"}, {"name": "ef/y7", "bone": "yan7", "color": "000000ff"}, {"name": "ef/y2", "bone": "yan2", "color": "000000ff"}, {"name": "ef/y8", "bone": "yan8", "color": "000000ff"}, {"name": "ef/y3", "bone": "yan3", "color": "000000ff"}, {"name": "ef/y9", "bone": "yan9", "color": "000000ff"}, {"name": "ef/y4", "bone": "yan4", "color": "000000ff"}, {"name": "ef/y10", "bone": "yan10", "color": "000000ff"}, {"name": "ef/y5", "bone": "yan5", "color": "000000ff"}, {"name": "ef/y11", "bone": "yan11", "color": "000000ff"}, {"name": "ef/y6", "bone": "yan6", "color": "000000ff"}, {"name": "ef/y12", "bone": "yan12", "color": "000000ff"}, {"name": "ef/effect1_1_00000", "bone": "bone", "color": "000000ff", "dark": "000000", "attachment": "ef/effect1_1_00003"}], "skins": [{"name": "default", "attachments": {"ef/y2": {"ef/y": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -40, -40, -40, -40, 40, 40, 40], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 80, "height": 80}}, "ef/y3": {"ef/y": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -40, -40, -40, -40, 40, 40, 40], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 80, "height": 80}}, "ef/y4": {"ef/y": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -40, -40, -40, -40, 40, 40, 40], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 80, "height": 80}}, "ef/y5": {"ef/y": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -40, -40, -40, -40, 40, 40, 40], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 80, "height": 80}}, "ef/y": {"ef/y": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -40, -40, -40, -40, 40, 40, 40], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 80, "height": 80}}, "ef/y7": {"ef/y": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -40, -40, -40, -40, 40, 40, 40], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 80, "height": 80}}, "ef/y8": {"ef/y": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -40, -40, -40, -40, 40, 40, 40], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 80, "height": 80}}, "ef/y9": {"ef/y": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -40, -40, -40, -40, 40, 40, 40], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 80, "height": 80}}, "ef/y10": {"ef/y": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -40, -40, -40, -40, 40, 40, 40], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 80, "height": 80}}, "ef/y11": {"ef/y": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -40, -40, -40, -40, 40, 40, 40], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 80, "height": 80}}, "ef/y12": {"ef/y": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -40, -40, -40, -40, 40, 40, 40], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 80, "height": 80}}, "ef/y6": {"ef/y": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40, -40, -40, -40, -40, 40, 40, 40], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 80, "height": 80}}, "ef/effect1_1_00000": {"ef/effect1_1_00000": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [75, -33, -75, -33, -75, 33, 75, 33], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 150, "height": 66}, "ef/effect1_1_00001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [75, -33, -75, -33, -75, 33, 75, 33], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 150, "height": 66}, "ef/effect1_1_00002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [75, -33, -75, -33, -75, 33, 75, 33], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 150, "height": 66}, "ef/effect1_1_00003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [75, -33, -75, -33, -75, 33, 75, 33], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 150, "height": 66}, "ef/effect1_1_00004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [75, -33, -75, -33, -75, 33, 75, 33], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 150, "height": 66}, "ef/effect1_1_00005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [75, -33, -75, -33, -75, 33, 75, 33], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 150, "height": 66}, "ef/effect1_1_00006": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [75, -33, -75, -33, -75, 33, 75, 33], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 150, "height": 66}, "ef/effect1_1_00007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [75, -33, -75, -33, -75, 33, 75, 33], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 150, "height": 66}, "ef/effect1_1_00008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [75, -33, -75, -33, -75, 33, 75, 33], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 150, "height": 66}, "ef/effect1_1_00009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [75, -33, -75, -33, -75, 33, 75, 33], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 150, "height": 66}, "ef/effect1_1_00010": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [75, -33, -75, -33, -75, 33, 75, 33], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 150, "height": 66}, "ef/effect1_1_00011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [75, -33, -75, -33, -75, 33, 75, 33], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 150, "height": 66}, "ef/effect1_1_00012": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [75, -33, -75, -33, -75, 33, 75, 33], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 150, "height": 66}, "ef/effect1_1_00013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [75, -33, -75, -33, -75, 33, 75, 33], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 150, "height": 66}, "ef/effect1_1_00014": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [75, -33, -75, -33, -75, 33, 75, 33], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 150, "height": 66}, "ef/effect1_1_00015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [75, -33, -75, -33, -75, 33, 75, 33], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 150, "height": 66}}}}], "animations": {"animation": {"slots": {"ef/y3": {"color": [{"color": "0000006a"}, {"time": 0.3333, "color": "00000000", "curve": "stepped"}, {"time": 0.6667, "color": "00000000"}, {"time": 0.8333, "color": "0000007f", "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "color": "000000ff", "curve": 0.25, "c3": 0.75}, {"time": 1.1, "color": "00000000"}], "attachment": [{"name": "ef/y"}, {"time": 0.3333, "name": null}, {"time": 0.6667, "name": "ef/y"}]}, "ef/y9": {"color": [{"time": 0.1, "color": "000000ff"}, {"time": 0.4, "color": "0000006a", "curve": "stepped"}, {"time": 0.7333, "color": "00000000"}], "attachment": [{"name": "ef/y"}]}, "ef/y10": {"color": [{"color": "00000032"}, {"time": 0.2667, "color": "000000ff", "curve": "stepped"}, {"time": 0.7, "color": "000000ff"}, {"time": 0.8333, "color": "000000c3", "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "color": "00000077", "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 1.1, "color": "00000000"}], "attachment": [{"name": "ef/y"}]}, "ef/y": {"color": [{"color": "00000000"}, {"time": 0.3667, "color": "000000ff", "curve": "stepped"}, {"time": 0.8333, "color": "000000ff"}, {"time": 0.9667, "color": "000000aa", "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.1, "color": "00000000"}], "attachment": [{"name": "ef/y"}]}, "ef/y8": {"color": [{"color": "0000007f"}, {"time": 0.2333, "color": "00000000", "curve": "stepped"}, {"time": 0.5667, "color": "00000000"}, {"time": 0.8333, "color": "000000b9", "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "color": "000000ff", "curve": "stepped"}, {"time": 0.9667, "color": "000000ff", "curve": 0.25, "c3": 0.75}, {"time": 1.1, "color": "00000000"}], "attachment": [{"name": "ef/y"}]}, "ef/y4": {"color": [{"time": 0.2333, "color": "000000ff"}, {"time": 0.8, "color": "00000000"}], "attachment": [{"name": "ef/y"}, {"time": 0.8, "name": null}]}, "ef/y6": {"color": [{"time": 0.1, "color": "000000ff"}, {"time": 0.5, "color": "00000000", "curve": "stepped"}, {"time": 0.9333, "color": "00000000"}, {"time": 0.9667, "color": "00000038", "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 1.1, "color": "00000000"}], "attachment": [{"name": "ef/y"}, {"time": 0.5, "name": null}, {"time": 0.9333, "name": "ef/y"}]}, "ef/y11": {"color": [{"color": "00000000", "curve": "stepped"}, {"time": 0.2667, "color": "00000000"}, {"time": 0.6667, "color": "000000ff", "curve": "stepped"}, {"time": 0.9667, "color": "000000ff", "curve": 0.25, "c3": 0.75}, {"time": 1.1, "color": "00000000"}], "attachment": [{"name": "ef/y"}]}, "ef/y5": {"color": [{"color": "00000000"}, {"time": 0.4, "color": "000000ff", "curve": "stepped"}, {"time": 0.9333, "color": "000000ff"}, {"time": 0.9667, "color": "000000d4", "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.1, "color": "00000000"}], "attachment": [{"name": "ef/y"}]}, "ef/y12": {"color": [{"color": "0000008d"}, {"time": 0.1333, "color": "000000ff", "curve": "stepped"}, {"time": 0.6667, "color": "000000ff"}, {"time": 0.8333, "color": "00000094", "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "color": "0000002a", "curve": 0.25, "c3": 0.75}, {"time": 1.1, "color": "00000000"}], "attachment": [{"name": "ef/y"}]}, "ef/effect1_1_00000": {"twoColor": [{"time": 0.9667, "light": "000000ff", "dark": "000000", "curve": 0.25, "c3": 0.75}, {"time": 1.1, "light": "00000000", "dark": "000000"}], "attachment": [{"name": "ef/effect1_1_00000"}, {"time": 0.0333, "name": "ef/effect1_1_00001"}, {"time": 0.0667, "name": "ef/effect1_1_00002"}, {"time": 0.1, "name": "ef/effect1_1_00003"}, {"time": 0.1333, "name": "ef/effect1_1_00004"}, {"time": 0.1667, "name": "ef/effect1_1_00006"}, {"time": 0.2, "name": "ef/effect1_1_00005"}, {"time": 0.2333, "name": "ef/effect1_1_00006"}, {"time": 0.2667, "name": "ef/effect1_1_00007"}, {"time": 0.3, "name": "ef/effect1_1_00008"}, {"time": 0.3333, "name": "ef/effect1_1_00009"}, {"time": 0.3667, "name": "ef/effect1_1_00010"}, {"time": 0.4, "name": "ef/effect1_1_00011"}, {"time": 0.4333, "name": "ef/effect1_1_00012"}, {"time": 0.4667, "name": "ef/effect1_1_00013"}, {"time": 0.5, "name": "ef/effect1_1_00014"}, {"time": 0.5333, "name": "ef/effect1_1_00015"}, {"time": 0.5667, "name": null}]}, "ef/y7": {"color": [{"color": "00000000", "curve": "stepped"}, {"time": 0.2333, "color": "00000000"}, {"time": 0.6, "color": "000000ff", "curve": "stepped"}, {"time": 0.9667, "color": "000000ff", "curve": 0.25, "c3": 0.75}, {"time": 1.1, "color": "00000000"}], "attachment": [{"time": 0.2333, "name": "ef/y"}]}, "ef/y2": {"color": [{"time": 0.3333, "color": "00000000"}, {"time": 0.7, "color": "000000ff", "curve": "stepped"}, {"time": 0.9667, "color": "000000ff", "curve": 0.25, "c3": 0.75}, {"time": 1.1, "color": "00000000"}], "attachment": [{"time": 0.3333, "name": "ef/y"}]}}, "bones": {"yan": {"rotate": [{"angle": -87.94}], "translate": [{"x": 26.44, "y": 12.15}, {"time": 0.8333, "x": 49.83, "y": 70.28, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1.1, "x": 57.47, "y": 89.26}], "scale": [{"x": 1.917, "y": 1.917}, {"time": 0.8333, "x": 2.61, "y": 2.61, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 2.748, "y": 2.748, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.1, "x": 2.815, "y": 2.815}]}, "yan2": {"rotate": [{"time": 0.3333, "angle": 69.08}, {"time": 0.8333, "angle": 95.21, "curve": 0.266, "c3": 0.618, "c4": 0.42}, {"time": 1.1, "angle": 106.85}], "translate": [{"time": 0.3333, "x": -9.61, "y": 26.57}, {"time": 0.8333, "x": -28.85, "y": 45.67, "curve": 0.266, "c3": 0.618, "c4": 0.42}, {"time": 1.1, "x": -37.42, "y": 54.18}], "scale": [{"time": 0.3333, "x": 1.292, "y": 1.292}, {"time": 0.8333, "x": 1.772, "y": 1.772, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 1.932, "y": 1.932, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 1.1, "x": 1.985, "y": 1.985}]}, "yan3": {"rotate": [{"angle": 166.89}], "translate": [{"x": 5.61, "y": 43.03}, {"time": 0.3333, "x": 7.48, "y": 56.12}, {"time": 0.6667, "y": 3.74}, {"time": 0.8333, "x": 0.93, "y": 10.29, "curve": 0.266, "c3": 0.618, "c4": 0.42}, {"time": 1.1, "x": 2.18, "y": 19.04}], "scale": [{"x": 1.889, "y": 1.889}, {"time": 0.3333, "x": 2.242, "y": 2.242}, {"time": 0.6667, "x": 0.83, "y": 0.83}, {"time": 0.8333, "x": 1.006, "y": 1.006, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 1.183, "y": 1.183, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 1.1, "x": 1.242, "y": 1.242}]}, "yan4": {"rotate": [{"angle": -30.53}, {"time": 0.8, "angle": -76.33}, {"time": 0.8333, "angle": -68.7, "curve": 0.245, "c3": 0.707, "c4": 0.82}, {"time": 1.1, "angle": -3.54}], "translate": [{"x": 8.98, "y": 21.7}, {"time": 0.8, "x": 22.45, "y": 48.64}, {"time": 0.8333, "x": 20.21, "y": 44.15, "curve": 0.245, "c3": 0.707, "c4": 0.82}, {"time": 1.1, "x": 1.04, "y": 5.82}], "scale": [{"x": 1.939, "y": 1.939}, {"time": 0.8, "x": 2.958, "y": 2.958}, {"time": 0.8333, "x": 2.788, "y": 2.788, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 1.939, "y": 1.939, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.1, "x": 1.385, "y": 1.385}]}, "yan5": {"rotate": [{"angle": -23.01}, {"time": 0.8333, "angle": -63.92, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1.1, "angle": -77.28}], "translate": [{"x": -2.53, "y": 17.76}, {"time": 0.8333, "x": -0.95, "y": 70.1, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1.1, "x": -0.43, "y": 87.19}], "scale": [{"x": 1.738, "y": 1.738}, {"time": 0.8333, "x": 2.46, "y": 2.46, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 2.604, "y": 2.604, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.1, "x": 2.674, "y": 2.674}]}, "yan6": {"rotate": [{"angle": 22.77}, {"time": 0.5, "angle": 49.97}, {"time": 0.8333, "angle": -1.64, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -17.12}, {"time": 1.1, "angle": -8.05}], "translate": [{"y": 48.18}, {"time": 0.5, "y": 69.79}, {"time": 0.8333, "y": 28.79, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "y": 16.49}, {"time": 1.1, "y": 23.69}], "scale": [{"x": 2.697, "y": 2.697}, {"time": 0.5, "x": 3.229, "y": 3.229}, {"time": 0.8333, "x": 2.218, "y": 2.218, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": 1.915, "y": 1.915}, {"time": 1, "x": 1.986, "y": 1.986, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 1.1, "x": 2.045, "y": 2.045}]}, "yan7": {"rotate": [{"angle": -87.94}], "translate": [{"x": 63.87, "y": 105.16, "curve": "stepped"}, {"time": 0.2333, "x": 26.44, "y": 12.15}, {"time": 0.8333, "x": 43.28, "y": 54, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 1.1, "x": 49.9, "y": 70.46}], "scale": [{"x": 3.025, "y": 3.025, "curve": "stepped"}, {"time": 0.2333, "x": 1.917, "y": 1.917}, {"time": 0.8333, "x": 2.416, "y": 2.416, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 2.554, "y": 2.554, "curve": 0.288, "c3": 0.628, "c4": 0.38}, {"time": 1.1, "x": 2.603, "y": 2.603}]}, "yan8": {"rotate": [{"angle": 126.57}, {"time": 0.2333, "angle": 138.77, "curve": "stepped"}, {"time": 0.5667, "angle": 69.08}, {"time": 0.8333, "angle": 83.02, "curve": 0.266, "c3": 0.618, "c4": 0.42}, {"time": 1.1, "angle": 94.65}], "translate": [{"x": -51.94, "y": 68.6}, {"time": 0.2333, "x": -60.92, "y": 77.51, "curve": "stepped"}, {"time": 0.5667, "x": -9.61, "y": 26.57}, {"time": 0.8333, "x": -19.87, "y": 36.76, "curve": 0.266, "c3": 0.618, "c4": 0.42}, {"time": 1.1, "x": -28.44, "y": 45.26}], "scale": [{"x": 2.347, "y": 2.347}, {"time": 0.2333, "x": 2.571, "y": 2.571, "curve": "stepped"}, {"time": 0.5667, "x": 1.292, "y": 1.292}, {"time": 0.8333, "x": 1.548, "y": 1.548, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 1.708, "y": 1.708, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 1.1, "x": 1.761, "y": 1.761}]}, "yan9": {"rotate": [{"angle": 166.89}], "translate": [{"x": 3.37, "y": 27.31}, {"time": 0.4, "x": 5.61, "y": 43.03}, {"time": 0.7333, "x": 7.48, "y": 56.12, "curve": "stepped"}, {"time": 0.8333, "x": 7.48, "y": 56.12, "curve": "stepped"}, {"time": 1.0667, "y": 3.74}, {"time": 1.1, "x": 0.19, "y": 5.05}], "scale": [{"x": 1.465, "y": 1.465}, {"time": 0.4, "x": 1.889, "y": 1.889}, {"time": 0.7333, "x": 2.242, "y": 2.242, "curve": "stepped"}, {"time": 1, "x": 2.242, "y": 2.242, "curve": "stepped"}, {"time": 1.0667, "x": 0.83, "y": 0.83}, {"time": 1.1, "x": 0.865, "y": 0.865}]}, "yan10": {"rotate": [{"angle": -3.82}, {"time": 0.4667, "angle": -30.53}, {"time": 0.8333, "angle": -51.52, "curve": 0.245, "c3": 0.64, "c4": 0.57}, {"time": 1.1, "angle": -67.7}], "translate": [{"x": 1.12, "y": 5.98}, {"time": 0.4667, "x": 8.98, "y": 21.7}, {"time": 0.8333, "x": 15.15, "y": 34.05, "curve": 0.245, "c3": 0.64, "c4": 0.57}, {"time": 1.1, "x": 19.91, "y": 43.57}], "scale": [{"x": 1.345, "y": 1.345}, {"time": 0.4667, "x": 1.939, "y": 1.939}, {"time": 0.8333, "x": 2.406, "y": 2.406, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 2.618, "y": 2.618, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 1.1, "x": 2.732, "y": 2.732}]}, "yan11": {"rotate": [{"angle": -88.47, "curve": "stepped"}, {"time": 0.2667, "angle": -23.01}, {"time": 0.8333, "angle": -50.83, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 1.1, "angle": -62.19}], "translate": [{"y": 101.51, "curve": "stepped"}, {"time": 0.2667, "x": -2.53, "y": 17.76}, {"time": 0.8333, "x": -1.45, "y": 53.35, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 1.1, "x": -1.02, "y": 67.89}], "scale": [{"x": 2.893, "y": 2.893, "curve": "stepped"}, {"time": 0.2667, "x": 1.738, "y": 1.738}, {"time": 0.8333, "x": 2.229, "y": 2.229, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 2.373, "y": 2.373, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 1.1, "x": 2.423, "y": 2.423}]}, "yan12": {"rotate": [{"angle": -8.05}, {"time": 0.5667, "angle": 22.77}, {"time": 0.8333, "angle": 37.28, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 49.97}, {"time": 1.1, "angle": 44.81}], "translate": [{"y": 23.69}, {"time": 0.5667, "y": 48.18}, {"time": 0.8333, "y": 59.71, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "y": 69.79}, {"time": 1.1, "y": 65.69}], "scale": [{"x": 2.093, "y": 2.093}, {"time": 0.5667, "x": 2.697, "y": 2.697}, {"time": 0.8333, "x": 2.981, "y": 2.981, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 3.158, "y": 3.158, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": 3.229, "y": 3.229}, {"time": 1.1, "x": 3.128, "y": 3.128}]}, "bone": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 1.605, "y": 1.605}]}, "yanall": {"scale": [{"x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}}}}}