import MsgEnum from "../../game/event/MsgEnum";
import { BadgeMgr, BadgeType } from "../../game/mgr/BadgeMgr";
import { MailMessage } from "../../game/net/protocol/Mail";
import MsgMgr from "../../lib/event/MsgMgr";
import { MailModule } from "./MailModule";

export class MailService {
  /**
   * 合并邮箱方法
   * @param mailBox 邮箱
   */
  public updateMerge(mailOldList: MailMessage[], mailList: MailMessage[]) {
    mailList.forEach((mail1: MailMessage) => {
      if (!mailOldList.find((mail2: MailMessage) => mail2.id == mail1.id)) {
        mailOldList.unshift(mail1);
      }
    });
    mailOldList.sort((a: MailMessage, b: MailMessage) => {
      if (a.read && !b.read) {
        return 1;
      } else if (!a.read && b.read) {
        return -1;
      }
      return b.sendTs - a.sendTs;
    });

    this.updatePopover();
  }

  // 设置已读状态
  public updateReadMail(mailIds: number[]) {
    for (let idx in mailIds) {
      let mailId = mailIds[idx];
      const mail = MailModule.data.dailyMailList.find((e) => e.id == mailId);
      if (mail) {
        mail.read = true;
      } else {
        const mail2 = MailModule.data.sysMailList.find((e) => e.id == mailId);
        if (mail2) {
          mail2.read = true;
        }
      }
    }

    this.updatePopover();
  }

  public deleteMail(mailIds: number[]) {
    for (let idx in mailIds) {
      let mailId = mailIds[idx];
      MailModule.data.dailyMailList = MailModule.data.dailyMailList.filter((e) => e.id != mailId);
      MailModule.data.sysMailList = MailModule.data.sysMailList.filter((e) => e.id != mailId);
    }

    this.updatePopover();
  }

  // 红点更新方法
  public updatePopover() {
    let unReadList = MailModule.data.dailyMailList.filter((e) => !e.read);
    if (unReadList?.length) {
      BadgeMgr.instance.setShowById(BadgeType.UIMain.btn_email.btn_tab_daily.id, true);
    } else {
      BadgeMgr.instance.setShowById(BadgeType.UIMain.btn_email.btn_tab_daily.id, false);
    }

    let unReadList2 = MailModule.data.sysMailList.filter((e) => !e.read);
    if (unReadList2?.length) {
      BadgeMgr.instance.setShowById(BadgeType.UIMain.btn_email.btn_tab_system.id, true);
    } else {
      BadgeMgr.instance.setShowById(BadgeType.UIMain.btn_email.btn_tab_system.id, false);
    }
  }
}
