{"skeleton": {"hash": "VZnlzUdv1UB/PTZJu/IiyfvxbKg=", "spine": "3.8.75", "x": -387.25, "y": -375.45, "width": 751, "height": 751.45, "images": "./images/", "audio": "D:/1.熊/2D项目东西/18号楼_3.8.75/卓越-青毛狮子"}, "bones": [{"name": "root", "scaleX": 0.5, "scaleY": 0.5}, {"name": "bone", "parent": "root", "length": 635.5, "y": -750}, {"name": "6", "parent": "bone", "length": 153.98, "rotation": 94.32, "x": -2.36, "y": 1268.86}, {"name": "<PERSON><PERSON><PERSON>", "parent": "bone", "length": 624.18, "rotation": 98.05, "x": -278.27, "y": 15.96}, {"name": "pzi", "parent": "bone", "length": 139.81, "rotation": -13.91, "x": 607.42, "y": 757.09}, {"name": "dajt", "parent": "bone", "length": 351.74, "rotation": 12.3, "x": 64.5, "y": 587.17}, {"name": "yao", "parent": "dajt", "length": 146.68, "rotation": 175.96, "x": -39.88, "y": 71.55}, {"name": "yao2", "parent": "yao", "length": 55.08, "rotation": -104.82, "x": -105.88, "y": -11.33, "color": "ef1acbff"}, {"name": "yao3", "parent": "yao", "length": 72.93, "rotation": -83.26, "x": -20.87, "y": -25.57, "color": "ef1acbff"}, {"name": "yao4", "parent": "yao", "length": 79.44, "rotation": -167.38, "x": -135.1, "y": 10.71, "color": "ef1acbff"}, {"name": "jiao1", "parent": "dajt", "x": -227.97, "y": 106.21, "color": "0c3fecff"}, {"name": "jiao2", "parent": "jiao1", "length": 340.83, "rotation": -91.49, "x": -2.27, "y": -10.4, "color": "0c3fecff"}, {"name": "jiaoz1", "parent": "jiao2", "length": 105.51, "rotation": 19.5, "x": 338.28, "y": -0.72, "color": "0c3fecff"}, {"name": "qbai22", "parent": "yao", "length": 122.85, "rotation": -72.42, "x": 67.77, "y": -17.13}, {"name": "qbai23", "parent": "qbai22", "length": 59.46, "rotation": -8.15, "x": 44.56, "y": -87.5}, {"name": "qbai24", "parent": "qbai22", "length": 146.44, "rotation": -5.24, "x": 125.27, "y": 3.11}, {"name": "tou", "parent": "qbai24", "length": 242.69, "rotation": -20.24, "x": 164.64, "y": -4.99}, {"name": "yanzhu2", "parent": "tou", "x": 101.09, "y": -92.2}, {"name": "yanzhu1", "parent": "tou", "x": 116.14, "y": 5.13}, {"name": "tou2", "parent": "tou", "length": 66.79, "rotation": 13.4, "x": 214.13, "y": -55.61, "color": "ee0000ff"}, {"name": "tou3", "parent": "tou2", "length": 57.66, "rotation": 63.64, "x": 68.79, "y": -14.12, "color": "ee0000ff"}, {"name": "tou4", "parent": "tou", "length": 59.61, "rotation": 91.55, "x": 220.21, "y": 66.82, "color": "ee0000ff"}, {"name": "tou5", "parent": "tou4", "length": 74.34, "rotation": 6.8, "x": 59.61, "color": "ee0000ff"}, {"name": "tou6", "parent": "tou", "length": 52.96, "rotation": -91.08, "x": 161.98, "y": -138.04, "color": "ee0000ff"}, {"name": "tou7", "parent": "tou", "length": 61.37, "rotation": 136.82, "x": 81.45, "y": 107.55, "color": "ee0000ff"}, {"name": "tou8", "parent": "tou7", "length": 64.21, "rotation": 0.74, "x": 61.37, "color": "ee0000ff"}, {"name": "tou9", "parent": "tou7", "length": 40.79, "rotation": -5.47, "x": 125.58, "y": 0.83, "color": "ee0000ff"}, {"name": "tou10", "parent": "tou9", "length": 41.04, "rotation": -41.71, "x": 40.79, "color": "ee0000ff"}, {"name": "tou11", "parent": "tou", "length": 46.91, "rotation": -171.43, "x": -16.08, "y": -63.71, "color": "ee0000ff"}, {"name": "tou12", "parent": "tou11", "length": 47.04, "rotation": 4.91, "x": 46.91, "color": "ee0000ff"}, {"name": "tou13", "parent": "tou11", "length": 45.65, "rotation": 20.61, "x": 93.78, "y": 4.03, "color": "ee0000ff"}, {"name": "tou14", "parent": "tou", "length": 51.63, "rotation": -133.75, "x": 9.53, "y": -145.22, "color": "ee0000ff"}, {"name": "tou15", "parent": "tou14", "length": 45.9, "rotation": -12.57, "x": 51.63, "color": "ee0000ff"}, {"name": "tou16", "parent": "tou14", "length": 40.81, "rotation": -5.7, "x": 96.44, "y": -9.99, "color": "ee0000ff"}, {"name": "tou17", "parent": "tou", "length": 58.76, "rotation": 154.86, "x": 10.06, "y": 22.48, "color": "ee0000ff"}, {"name": "tou18", "parent": "tou17", "length": 51.52, "rotation": 19.44, "x": 58.76, "color": "ee0000ff"}, {"name": "tou19", "parent": "tou17", "length": 52.15, "rotation": 20.26, "x": 107.35, "y": 17.15, "color": "ee0000ff"}, {"name": "yziyanbai33", "parent": "dajt", "x": 446.89, "y": -1.2}, {"name": "zuiy1yzhu", "parent": "dajt", "x": -528.55, "y": 133.59}, {"name": "yziyanzhu33", "parent": "dajt", "x": -598.51, "y": 560.22}, {"name": "qbai2", "parent": "yao", "length": 71.13, "rotation": 105.48, "x": -15.25, "y": 33.1}, {"name": "qbai3", "parent": "qbai2", "length": 62.92, "rotation": -4.25, "x": 71.13}, {"name": "qbai4", "parent": "qbai2", "length": 64.52, "rotation": -1.27, "x": 133.88, "y": -4.67}, {"name": "qbai5", "parent": "qbai2", "length": 56.05, "rotation": -5.22, "x": 197.74, "y": -5.75}, {"name": "qbai6", "parent": "qbai2", "length": 48.52, "rotation": -6.14, "x": 253.91, "y": -10.2}, {"name": "qbai7", "parent": "yao", "length": 71.13, "rotation": 115.03, "x": -94.68, "y": 31.43}, {"name": "qbai8", "parent": "qbai7", "length": 62.92, "rotation": -4.25, "x": 71.13}, {"name": "qbai9", "parent": "qbai2", "length": 64.52, "rotation": 8.28, "x": 152.39, "y": 94.61}, {"name": "qbai10", "parent": "qbai2", "length": 56.05, "rotation": 4.33, "x": 215.54, "y": 104.14}, {"name": "qbai11", "parent": "qbai2", "length": 48.52, "rotation": 3.42, "x": 271.67, "y": 109.08}, {"name": "qbai25", "parent": "yao", "length": 138.01, "rotation": 99.58, "x": -54.9, "y": 104.52}, {"name": "qbai26", "parent": "qbai25", "length": 109.5, "rotation": -5.6, "x": 139.51, "y": 0.85}, {"name": "qbai27", "parent": "qbai25", "length": 94.22, "rotation": 6.5, "x": 247.03, "y": -7.25}, {"name": "qbai28", "parent": "qbai25", "length": 98.97, "rotation": 29.01, "x": 344.88, "y": 5.55}, {"name": "yao5", "parent": "yao", "length": 174, "rotation": -172.52, "x": -130.04, "y": 51.28, "color": "0c3fecff"}, {"name": "qbai", "parent": "yao5", "length": 392.29, "rotation": -69.36, "x": 171.84, "y": 0.61, "color": "0c3fecff"}, {"name": "jiaoz2", "parent": "qbai", "length": 108.19, "rotation": 29.72, "x": 390.72, "y": -0.24, "color": "0c3fecff"}, {"name": "hup2", "parent": "dajt", "length": 107.75, "rotation": -158.41, "x": -629.97, "y": -137.88}, {"name": "hup3", "parent": "hup2", "length": 116.57, "rotation": -20.65, "x": 107.75}, {"name": "hup4", "parent": "hup3", "length": 89.6, "rotation": -9.82, "x": 116.57}, {"name": "dajt2", "parent": "dajt", "x": -672.84, "y": -85.62}, {"name": "dajt3", "parent": "dajt", "x": -611.71, "y": -12.73}, {"name": "dajt4", "parent": "dajt", "x": -799.58, "y": -83.6}, {"name": "hup", "parent": "dajt", "x": -72.57, "y": -536.13, "color": "fb9108ff"}, {"name": "hup5", "parent": "dajt", "x": 490.29, "y": -570.05, "color": "fb9108ff"}, {"name": "hup6", "parent": "dajt", "x": 198.41, "y": -555.25, "color": "fb9108ff"}, {"name": "qbai29", "parent": "qbai24", "x": 127.48, "y": 161.88}, {"name": "dabi1", "parent": "qbai29", "length": 120.64, "rotation": 69.64, "x": -3.24, "y": 6.25}, {"name": "shoubi1_2", "parent": "dabi1", "length": 91.57, "rotation": 71.67, "x": 120.65, "y": -0.02}, {"name": "bone10", "parent": "bone", "length": 28.83, "rotation": -165.35, "x": -466.79, "y": 736.8, "color": "ff3f00ff"}, {"name": "shoubi1", "parent": "bone10", "length": 61.39, "rotation": 75.91, "x": 19.14, "y": 4.02}, {"name": "shoubi1_3", "parent": "bone10", "length": 62.31, "rotation": 97.25, "x": -43.51, "y": 25.94}, {"name": "qbai30", "parent": "qbai24", "x": -3.12, "y": -199.37}, {"name": "dabi2", "parent": "qbai30", "length": 177, "rotation": -153.58, "x": -1.72, "y": 3.79}, {"name": "shou2", "parent": "dabi2", "length": 236.55, "rotation": 87.47, "x": 175.61, "y": 0.05}, {"name": "shouz2", "parent": "shou2", "length": 156.19, "rotation": 19.42, "x": 237.69, "y": -0.33}, {"name": "wu2", "parent": "bone", "length": 97.29, "rotation": -82.6, "x": 756.91, "y": 274.72, "color": "57e419ff"}, {"name": "wu3", "parent": "wu2", "x": -99.99, "y": -268.83, "color": "57e419ff"}, {"name": "wu4", "parent": "wu2", "x": 49.13, "y": -386.87, "color": "57e419ff"}, {"name": "wu5", "parent": "wu2", "x": 72.16, "y": -645.67, "color": "57e419ff"}, {"name": "wu6", "parent": "wu2", "x": -0.93, "y": -842.65, "color": "57e419ff"}, {"name": "hup7", "parent": "bone", "length": 92.77, "rotation": -93.01, "x": -803.75, "y": 281.84, "color": "70137cff"}, {"name": "hup8", "parent": "hup7", "x": 73.36, "y": 169.93, "color": "70137cff"}, {"name": "hup9", "parent": "hup7", "x": 203.93, "y": 170.63, "color": "70137cff"}, {"name": "hup10", "parent": "hup7", "x": 241.39, "y": 396.8, "color": "70137cff"}, {"name": "hup11", "parent": "hup7", "x": 166.58, "y": 626.31, "color": "70137cff"}, {"name": "hup12", "parent": "hup7", "x": 219.62, "y": 800.85, "color": "70137cff"}, {"name": "wu7", "parent": "bone", "length": 153.01, "rotation": -90, "x": -793.06, "y": 579.41, "color": "771919ff"}, {"name": "wu8", "parent": "wu7", "x": 108.76, "y": 86.45, "color": "771919ff"}, {"name": "wu9", "parent": "wu7", "x": 212.08, "y": 206.5, "color": "771919ff"}, {"name": "wu10", "parent": "wu7", "x": 130.41, "y": 205.51, "color": "771919ff"}, {"name": "wu11", "parent": "wu7", "x": 196.12, "y": 315.95, "color": "771919ff"}, {"name": "wu12", "parent": "wu7", "x": 225.64, "y": 563.91, "color": "771919ff"}, {"name": "wu13", "parent": "wu7", "x": 202.02, "y": 688.88, "color": "771919ff"}, {"name": "wu14", "parent": "bone", "length": 184.61, "rotation": -92.7, "x": 772.77, "y": 945.29, "color": "198dc3ff"}, {"name": "wu15", "parent": "wu14", "x": -91.44, "y": -160.72, "color": "198dc3ff"}, {"name": "bone11", "parent": "bone", "length": 73.91, "rotation": -87.68, "x": 737.81, "y": 796.18}, {"name": "wu16", "parent": "bone11", "rotation": -5.03, "x": -14.88, "y": -158.62, "color": "198dc3ff"}, {"name": "wu17", "parent": "bone11", "rotation": -5.03, "x": 74.95, "y": -308.65, "color": "198dc3ff"}, {"name": "wu18", "parent": "bone11", "rotation": -5.03, "x": 63.44, "y": -436.92, "color": "198dc3ff"}, {"name": "wu19", "parent": "wu14", "x": 47.31, "y": -291.77, "color": "198dc3ff"}, {"name": "wu20", "parent": "wu14", "x": -2.66, "y": -463.29, "color": "198dc3ff"}, {"name": "wu21", "parent": "wu14", "x": 103.24, "y": -513.83, "color": "198dc3ff"}, {"name": "bone2", "parent": "bone", "length": 324.09, "rotation": 91.09, "x": -814.51, "y": 1152.63, "color": "d100faff"}, {"name": "bone12", "parent": "bone", "length": 123.07, "rotation": -91.23, "x": -789.16, "y": 1052.57}, {"name": "wu22", "parent": "bone12", "rotation": -177.68, "x": 67.54, "y": 98.36, "color": "d100faff"}, {"name": "wu23", "parent": "bone12", "rotation": -177.68, "x": 105.63, "y": 206.43, "color": "d100faff"}, {"name": "bone3", "parent": "bone12", "rotation": -177.68, "x": -63.3, "y": 111.09, "color": "d100faff"}, {"name": "bone4", "parent": "bone12", "rotation": -177.68, "x": -36.82, "y": 253.1, "color": "d100faff"}, {"name": "bone5", "parent": "bone12", "rotation": -177.68, "x": -15.1, "y": 399.68, "color": "d100faff"}, {"name": "bone6", "parent": "bone2", "x": 189.39, "y": -158.5, "color": "d100faff"}, {"name": "bone7", "parent": "bone2", "x": 124.01, "y": -326.67, "color": "d100faff"}, {"name": "bone8", "parent": "bone2", "x": 3.61, "y": -364.79, "color": "d100faff"}, {"name": "bone9", "parent": "bone2", "x": -9.8, "y": -579.03, "color": "d100faff"}, {"name": "qbai31", "parent": "qbai24", "length": 78.19, "rotation": -125.67, "x": 79.91, "y": -209.39}, {"name": "tou20", "parent": "tou", "length": 12.53, "rotation": -94.11, "x": 37.67, "y": -58.39}, {"name": "qbai12", "parent": "qbai", "x": 136.73, "y": 91.67, "color": "0c3fecff"}, {"name": "qbai13", "parent": "qbai", "x": 347.83, "y": 69.4, "color": "0c3fecff"}], "slots": [{"name": "7", "bone": "bone", "attachment": "7"}, {"name": "6", "bone": "6", "attachment": "6"}, {"name": "5", "bone": "bone", "attachment": "5"}, {"name": "yy2", "bone": "bone", "attachment": "yy2"}, {"name": "yy", "bone": "<PERSON><PERSON><PERSON>", "attachment": "yy"}, {"name": "4", "bone": "bone", "attachment": "4"}, {"name": "3", "bone": "bone", "attachment": "3"}, {"name": "2", "bone": "bone", "attachment": "2"}, {"name": "1", "bone": "bone", "attachment": "1"}, {"name": "pzi", "bone": "pzi", "attachment": "pzi"}, {"name": "dajt", "bone": "pzi", "attachment": "dajt"}, {"name": "wu8", "bone": "bone6", "attachment": "wu8"}, {"name": "wu7", "bone": "wu14", "attachment": "wu7"}, {"name": "wu6", "bone": "wu22", "attachment": "wu6"}, {"name": "wu5", "bone": "bone3", "attachment": "wu5"}, {"name": "wu4", "bone": "bone11", "attachment": "wu4"}, {"name": "hup2", "bone": "hup2", "attachment": "hup2"}, {"name": "yziyanbai33", "bone": "dajt", "attachment": "yziyanbai33"}, {"name": "yziyanzhu33", "bone": "yziyanzhu33", "attachment": "yziyanzhu33"}, {"name": "yziyanbai22", "bone": "dajt", "attachment": "yziyanbai22"}, {"name": "yziyanzhu22", "bone": "yziyanbai33", "attachment": "yziyanzhu22"}, {"name": "yziyanzhu23", "bone": "yziyanbai33", "blend": "additive"}, {"name": "hup<PERSON><PERSON>", "bone": "dajt", "attachment": "hup<PERSON><PERSON>"}, {"name": "hup", "bone": "hup7", "attachment": "hup"}, {"name": "dabi2", "bone": "dabi2", "attachment": "dabi2"}, {"name": "shou2", "bone": "shou2", "attachment": "shou2"}, {"name": "shouz2", "bone": "shouz2", "attachment": "shouz2"}, {"name": "body", "bone": "qbai22", "attachment": "body"}, {"name": "qbai22", "bone": "hup7", "attachment": "qbai22"}, {"name": "qbai", "bone": "qbai", "attachment": "qbai"}, {"name": "qbai2", "bone": "qbai2", "attachment": "qbai2"}, {"name": "yao", "bone": "yao", "attachment": "yao"}, {"name": "hqbai", "bone": "qbai", "attachment": "hqbai"}, {"name": "jiaoz2", "bone": "jiaoz2", "attachment": "jiaoz2"}, {"name": "jiaoz1", "bone": "jiaoz1", "attachment": "jiaoz1"}, {"name": "jiao1", "bone": "jiao1", "attachment": "jiao1"}, {"name": "dabi1", "bone": "dabi1", "attachment": "dabi1"}, {"name": "shoubi1_2", "bone": "shoubi1", "attachment": "shoubi1_2"}, {"name": "shoubi1", "bone": "shoubi1", "attachment": "shoubi1"}, {"name": "zuiy1yanbai", "bone": "dajt", "attachment": "zuiy1yanbai"}, {"name": "zuiy1yzhu", "bone": "zuiy1yzhu", "attachment": "zuiy1yzhu"}, {"name": "zuiy1yzhu2", "bone": "zuiy1yzhu", "attachment": "zuiy1yzhu", "blend": "additive"}, {"name": "zuiy1", "bone": "dajt", "attachment": "zuiy1"}, {"name": "shouz1", "bone": "shoubi1", "attachment": "shouz1"}, {"name": "tou", "bone": "tou", "attachment": "tou"}, {"name": "yanzhu2", "bone": "yanzhu2", "attachment": "yanzhu2"}, {"name": "yanzhu1", "bone": "yanzhu1", "attachment": "yanzhu1"}, {"name": "biyan", "bone": "tou"}, {"name": "wu3", "bone": "wu7", "attachment": "wu3"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON>", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "wu2", "bone": "wu2", "attachment": "wu2"}, {"name": "wu1", "bone": "hup7", "attachment": "wu1"}], "ik": [{"name": "shou1", "bones": ["dabi1", "shoubi1_2"], "target": "bone10", "stretch": true}], "skins": [{"name": "default", "attachments": {"hup": {"hup": {"type": "mesh", "uvs": [1, 0.30129, 1, 0.60217, 1, 1, 0.83183, 1, 0.64059, 1, 0.50145, 1, 0.34319, 1, 0.21358, 1, 0.11138, 0.92444, 0.05349, 0.68445, 0.07149, 0.49605, 0.17788, 0.38076, 0.36038, 0.25703, 0.51646, 0.15697, 0.67893, 0.05528, 0.79842, 0, 0.93572, 1e-05, 0.55053, 0.33258, 0.59786, 0.65778, 0.43412, 0.69355, 0.27796, 0.81062, 0.76636, 0.58249, 0.9194, 0.47844, 0.96114, 0.71465, 0.81792, 0.89743, 0.38003, 0.4145, 0.20407, 0.52979, 0.72067, 0.22962, 0.8508, 0.13402], "triangles": [22, 16, 0, 22, 0, 1, 23, 22, 1, 24, 22, 23, 23, 1, 2, 3, 23, 2, 24, 21, 22, 3, 24, 23, 4, 18, 21, 4, 21, 24, 5, 18, 4, 4, 24, 3, 5, 19, 18, 8, 26, 20, 7, 8, 20, 6, 20, 19, 7, 20, 6, 6, 19, 5, 21, 28, 22, 19, 17, 18, 18, 27, 21, 20, 25, 19, 21, 27, 28, 27, 15, 28, 17, 13, 14, 19, 25, 17, 26, 11, 12, 26, 12, 25, 26, 9, 10, 26, 10, 11, 27, 14, 15, 17, 14, 27, 25, 12, 13, 25, 13, 17, 20, 26, 25, 8, 9, 26, 18, 17, 27, 28, 15, 16, 22, 28, 16], "vertices": [3, 5, 559.96, -544.21, 0.33812, 65, 361.55, 11.03, 0.05257, 64, 69.67, 25.83, 0.60931, 3, 5, 543.94, -617.71, 0.10063, 65, 345.54, -62.46, 0.11142, 64, 53.65, -47.66, 0.78795, 4, 5, 522.76, -714.89, 0.02562, 63, 595.33, -178.76, 0.0067, 65, 324.36, -159.64, 0.2215, 64, 32.47, -144.84, 0.74618, 4, 5, 381.62, -684.12, 0.02, 63, 454.19, -147.99, 0.04868, 65, 183.21, -128.88, 0.41699, 64, -108.68, -114.07, 0.51433, 4, 5, 221.11, -649.14, 0.082, 63, 293.68, -113.01, 0.16028, 65, 22.7, -93.89, 0.47236, 64, -269.18, -79.09, 0.28536, 4, 5, 104.33, -623.69, 0.105, 63, 176.9, -87.56, 0.44387, 65, -94.07, -68.44, 0.37199, 64, -385.96, -53.64, 0.07914, 4, 5, -28.5, -594.74, 0.11833, 63, 44.07, -58.61, 0.68183, 65, -226.91, -39.49, 0.18804, 64, -518.79, -24.69, 0.0118, 3, 5, -137.28, -571.03, 0.14444, 63, -64.71, -34.9, 0.77737, 65, -335.69, -15.78, 0.07819, 3, 5, -219.03, -533.88, 0.34333, 63, -146.46, 2.25, 0.61148, 65, -417.44, 21.37, 0.04519, 3, 5, -254.84, -464.67, 0.65667, 63, -182.27, 71.46, 0.32825, 65, -453.25, 90.58, 0.01508, 2, 5, -229.7, -421.94, 0.85556, 63, -157.13, 114.19, 0.14444, 3, 5, -134.27, -413.24, 0.88167, 63, -61.7, 122.89, 0.10325, 65, -332.68, 142.01, 0.01508, 3, 5, 25.49, -416.4, 0.8925, 63, 98.06, 119.73, 0.05528, 65, -172.92, 138.85, 0.05222, 4, 5, 161.81, -420.51, 0.8925, 63, 234.38, 115.62, 0.01742, 65, -36.6, 134.74, 0.07474, 64, -328.48, 149.54, 0.01534, 3, 5, 303.59, -425.39, 0.87687, 65, 105.18, 129.86, 0.05494, 64, -186.71, 144.66, 0.06819, 3, 5, 406.81, -433.75, 0.81187, 65, 208.41, 121.5, 0.01716, 64, -83.48, 136.3, 0.17097, 3, 5, 522.05, -458.86, 0.64938, 65, 323.65, 96.38, 0.01716, 64, 31.76, 111.18, 0.33347, 4, 5, 181.06, -469.64, 0.678, 63, 253.63, 66.49, 0.06259, 65, -17.35, 85.61, 0.19765, 64, -309.24, 100.41, 0.06176, 4, 5, 203.46, -557.73, 0.32, 63, 276.03, -21.6, 0.14147, 65, 5.06, -2.48, 0.38002, 64, -286.83, 12.32, 0.15851, 4, 5, 64.14, -536.52, 0.324, 63, 136.71, -0.39, 0.34258, 65, -134.27, 18.73, 0.29332, 64, -426.16, 33.53, 0.0401, 4, 5, -73.17, -536.55, 0.326, 63, -0.6, -0.42, 0.53378, 65, -271.57, 18.7, 0.13644, 64, -563.46, 33.5, 0.00378, 4, 5, 348.89, -570.16, 0.322, 63, 421.46, -34.03, 0.03373, 65, 150.49, -14.92, 0.3051, 64, -141.4, -0.11, 0.33917, 4, 5, 482.88, -572.74, 0.324, 63, 555.45, -36.61, 0.00429, 65, 284.47, -17.5, 0.14253, 64, -7.41, -2.69, 0.52918, 4, 5, 505.33, -638.08, 0.0865, 63, 577.9, -101.95, 0.00429, 65, 306.93, -82.83, 0.19882, 64, 15.04, -68.03, 0.71039, 4, 5, 375.4, -656.52, 0.08, 63, 447.97, -120.4, 0.04323, 65, 177, -101.28, 0.39992, 64, -114.89, -86.48, 0.47685, 4, 5, 33.59, -458.46, 0.676, 63, 106.16, 77.67, 0.16754, 65, -164.82, 96.79, 0.14514, 64, -456.7, 111.59, 0.01133, 3, 5, -120.23, -454.44, 0.674, 63, -47.66, 81.69, 0.26766, 65, -318.63, 100.81, 0.05834, 4, 5, 329.34, -475.61, 0.676, 63, 401.91, 60.52, 0.01286, 65, 130.93, 79.63, 0.15178, 64, -160.96, 94.44, 0.15936, 3, 5, 443.64, -476.06, 0.6615, 65, 245.23, 79.19, 0.06202, 64, -46.65, 93.99, 0.27648], "hull": 17, "edges": [26, 34, 34, 36, 36, 38, 38, 40, 40, 16, 16, 14, 12, 14, 40, 12, 10, 12, 38, 10, 8, 10, 36, 8, 36, 42, 42, 44, 44, 0, 44, 46, 0, 2, 2, 4, 46, 2, 46, 48, 48, 8, 42, 48, 4, 6, 6, 8, 6, 48, 46, 4, 34, 50, 50, 52, 52, 40, 50, 38, 52, 18, 18, 20, 20, 22, 22, 24, 24, 50, 22, 52, 24, 26, 34, 54, 54, 56, 56, 30, 30, 28, 28, 26, 28, 54, 54, 42, 56, 44, 30, 32, 56, 32, 32, 0, 18, 16], "width": 429, "height": 125}}, "hupyzi": {"hupyzi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [466.57, -606.43, -866.15, -315.97, -627.22, 780.3, 705.5, 489.83], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 545, "height": 448}}, "dajt": {"dajt": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [123.32, 1.61, -98.97, -53.45, -138.16, 104.76, 84.12, 159.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 229, "height": 163}}, "yziyanbai22": {"yziyanbai22": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [478.89, -38.02, 412.45, -23.54, 424.38, 31.18, 490.82, 16.7], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 68, "height": 56}}, "hup2": {"hup2": {"type": "mesh", "uvs": [1, 0.12001, 1, 0.29985, 1, 0.47242, 1, 0.5772, 1, 0.76071, 0.94007, 0.84108, 0.77193, 0.97222, 0.55464, 0.97347, 0.34909, 1, 0.15921, 1, 0, 1, 0, 0.9035, 0, 0.81665, 0, 0.68694, 0, 0.55949, 0.04436, 0.55949, 0.13894, 0.55949, 0.1582, 0.5103, 0.27666, 0.54864, 0.42935, 0.54489, 0.44502, 0.49866, 0.58771, 0.5187, 0.52647, 0.46153, 0.67408, 0.45229, 0.61708, 0.41705, 0.67563, 0.38056, 0.6827, 0.25399, 0.77157, 0.06932, 0.85288, 0, 0.94364, 0, 1, 0, 0.64644, 0.56868, 0.77955, 0.63741, 0.87743, 0.75361, 0.71712, 0.80979, 0.63099, 0.68733, 0.47634, 0.60986, 0.48025, 0.75106, 0.53311, 0.85727, 0.34322, 0.64235, 0.30015, 0.78854, 0.13963, 0.80229, 0.15529, 0.88726, 0.33148, 0.89475, 0.209, 0.67464, 0.09515, 0.6903, 0.89431, 0.55193, 0.74779, 0.49265, 0.79778, 0.4059, 0.9115, 0.45673, 0.81695, 0.25519, 0.87557, 0.10312, 0.91528, 0.28537], "triangles": [44, 18, 39, 16, 17, 18, 13, 14, 15, 45, 15, 16, 44, 16, 18, 39, 18, 19, 39, 19, 36, 39, 36, 37, 40, 44, 39, 45, 16, 44, 13, 15, 45, 41, 45, 44, 12, 13, 45, 40, 39, 37, 41, 44, 40, 12, 45, 41, 23, 24, 25, 23, 25, 48, 47, 23, 48, 21, 23, 47, 21, 22, 23, 36, 19, 21, 19, 20, 21, 31, 21, 47, 36, 21, 31, 35, 36, 31, 31, 47, 32, 50, 25, 26, 48, 25, 50, 47, 48, 49, 51, 28, 29, 27, 28, 51, 29, 30, 0, 51, 29, 0, 50, 26, 27, 51, 50, 27, 50, 51, 0, 52, 50, 0, 52, 0, 1, 48, 50, 52, 49, 48, 52, 49, 52, 1, 49, 1, 2, 46, 47, 49, 46, 49, 2, 46, 2, 3, 32, 47, 46, 3, 33, 32, 3, 32, 46, 33, 3, 4, 33, 35, 32, 5, 33, 4, 34, 33, 5, 35, 31, 32, 37, 36, 35, 33, 34, 35, 37, 35, 34, 38, 37, 34, 40, 37, 38, 6, 34, 5, 38, 34, 6, 7, 38, 6, 43, 38, 7, 42, 41, 40, 12, 41, 42, 43, 40, 38, 42, 40, 43, 9, 42, 43, 9, 11, 42, 8, 43, 7, 9, 43, 8, 11, 12, 42, 9, 10, 11], "vertices": [1, 5, -497.79, 106.23, 1, 1, 5, -517.59, 15.39, 1, 1, 5, -536.59, -71.79, 1, 4, 57, -80.95, 17.88, 0.09067, 5, -548.12, -124.72, 0.83733, 60, 124.71, -39.1, 0.03962, 61, 63.59, -111.99, 0.03238, 4, 57, -28.05, 96.64, 0.14111, 5, -568.33, -217.42, 0.84556, 60, 104.51, -131.8, 0.00651, 61, 43.38, -204.69, 0.00683, 4, 58, -132.38, 78.47, 0.01563, 57, 11.53, 120.1, 0.32771, 5, -596.5, -253.8, 0.64667, 60, 76.34, -168.18, 0.01, 5, 58, -62.84, 131.76, 0.10063, 57, 95.4, 145.45, 0.55125, 5, -665.15, -308.23, 0.33812, 60, 7.68, -222.61, 0.00648, 62, 134.43, -224.63, 0.00352, 5, 59, -127.65, 95.58, 0.01563, 58, 7.11, 115.96, 0.3225, 57, 155.29, 106.01, 0.55125, 5, -735.35, -293.59, 0.10063, 62, 64.23, -209.99, 0.01, 5, 59, -59.12, 105.23, 0.09542, 58, 76.28, 113.78, 0.55646, 57, 219.24, 79.57, 0.3225, 5, -804.54, -292.55, 0.01563, 62, -4.97, -208.95, 0.01, 4, 59, 3.43, 101.5, 0.29431, 58, 137.27, 99.42, 0.59507, 57, 271.26, 44.64, 0.10063, 62, -66.19, -195.6, 0.01, 4, 59, 55.88, 98.37, 0.45463, 58, 188.41, 87.39, 0.5037, 57, 314.87, 15.34, 0.02083, 62, -117.52, -184.42, 0.02083, 4, 59, 52.9, 48.56, 0.44743, 58, 176.99, 38.83, 0.44194, 57, 287.06, -26.07, 0.01, 62, -106.9, -135.67, 0.10063, 4, 59, 50.23, 3.74, 0.33042, 58, 166.7, -4.88, 0.32146, 57, 262.02, -63.35, 0.01, 62, -97.34, -91.8, 0.33812, 3, 59, 46.23, -63.2, 0.19646, 58, 151.34, -70.16, 0.14687, 62, -83.06, -26.28, 0.65667, 3, 59, 42.3, -128.97, 0.09028, 58, 136.25, -134.3, 0.05417, 62, -69.03, 38.1, 0.85556, 4, 59, 27.69, -128.1, 0.04333, 58, 122, -130.95, 0.0725, 60, -181.47, 37, 0.0068, 62, -54.72, 34.99, 0.87737, 4, 58, 91.62, -123.8, 0.058, 57, 149.83, -148.15, 0.024, 60, -150.97, 30.36, 0.05694, 62, -24.23, 28.34, 0.86106, 4, 58, 79.61, -147.1, 0.01333, 57, 130.37, -165.72, 0.01333, 60, -139.34, 53.85, 0.08626, 62, -12.6, 51.83, 0.88708, 5, 58, 46.09, -118.85, 0.024, 57, 108.97, -127.47, 0.056, 60, -105.37, 26.16, 0.20188, 61, -166.5, -46.73, 0.00341, 62, 21.37, 24.14, 0.71471, 5, 57, 66.06, -100.98, 0.056, 5, -728.56, -68.29, 0.02267, 60, -55.72, 17.32, 0.5525, 61, -116.85, -55.57, 0.03538, 62, 71.02, 15.31, 0.33345, 5, 57, 48.45, -117.95, 0.01333, 5, -718.42, -46.04, 0.01111, 60, -45.59, 39.58, 0.72424, 61, -106.71, -33.31, 0.05215, 62, 81.16, 37.56, 0.19916, 5, 57, 15.14, -83.09, 0.02, 5, -674.62, -66.19, 0.04556, 60, -1.78, 19.42, 0.68298, 61, -62.91, -53.47, 0.13187, 62, 124.96, 17.41, 0.11959, 4, 5, -688.07, -33.01, 0.02444, 60, -15.23, 52.61, 0.72563, 61, -76.36, -20.28, 0.20907, 62, 111.51, 50.59, 0.04086, 4, 5, -639.46, -38.72, 0.08468, 60, 33.38, 46.9, 0.5878, 61, -27.75, -25.99, 0.30019, 62, 160.12, 44.89, 0.02733, 4, 5, -653.96, -16.91, 0.11158, 60, 18.88, 68.71, 0.37903, 61, -42.25, -4.18, 0.50584, 62, 145.62, 66.69, 0.00356, 3, 5, -631.06, -2.59, 0.32177, 60, 41.77, 83.03, 0.16743, 61, -19.35, 10.14, 0.51079, 3, 5, -614.85, 60.85, 0.62522, 60, 57.99, 146.47, 0.04656, 61, -3.14, 73.58, 0.32822, 1, 5, -565.86, 147.89, 1, 1, 5, -532.01, 177.19, 1, 1, 5, -502.75, 170.81, 1, 1, 5, -484.58, 166.85, 1, 5, 57, 13.45, -50.83, 0.096, 5, -661.19, -95.57, 0.22721, 60, 11.65, -9.95, 0.45359, 61, -49.48, -82.84, 0.1184, 62, 138.39, -11.97, 0.1048, 6, 58, -104.94, -36.17, 0.008, 57, -3.2, 3.16, 0.218, 5, -625.83, -139.64, 0.454, 60, 47, -54.02, 0.21167, 61, -14.12, -126.91, 0.06018, 62, 173.75, -56.04, 0.04815, 6, 58, -122.62, 29.71, 0.008, 57, 3.48, 71.04, 0.292, 5, -607.07, -205.22, 0.62, 60, 65.77, -119.6, 0.05926, 61, 4.64, -192.49, 0.01229, 62, 192.51, -121.61, 0.00845, 5, 58, -64.47, 45.87, 0.0865, 57, 63.59, 65.65, 0.5335, 5, -664.94, -222.33, 0.3, 60, 7.9, -136.71, 0.04499, 62, 134.64, -138.72, 0.03501, 6, 58, -51.3, -22.27, 0.056, 57, 51.89, -2.75, 0.398, 5, -679.23, -154.42, 0.226, 60, -6.4, -68.8, 0.17024, 61, -67.52, -141.69, 0.01229, 62, 120.35, -70.82, 0.13747, 6, 58, -10.8, -72.95, 0.024, 57, 71.92, -64.45, 0.2, 5, -720.57, -104.42, 0.096, 60, -47.73, -18.8, 0.36461, 61, -108.86, -91.69, 0.03121, 62, 79.01, -20.82, 0.28418, 6, 59, -110, -20.66, 0.008, 58, 4.67, -1.59, 0.218, 57, 111.55, -3.14, 0.398, 5, -734.85, -176.02, 0.056, 60, -62.01, -90.4, 0.07142, 62, 64.73, -92.41, 0.24858, 6, 59, -124.13, 35.19, 0.008, 58, 0.26, 55.85, 0.292, 57, 127.69, 52.17, 0.5335, 5, -729.5, -233.38, 0.0865, 60, -56.66, -147.76, 0.01555, 62, 70.08, -149.78, 0.06445, 6, 58, 35.81, -66.66, 0.096, 57, 117.75, -75, 0.2, 5, -767.06, -111.47, 0.024, 60, -94.23, -25.86, 0.16275, 61, -155.35, -98.74, 0.0041, 62, 32.52, -27.87, 0.51315, 6, 59, -49.51, -4.86, 0.052, 58, 66.96, 3.66, 0.4, 57, 171.69, -20.18, 0.218, 5, -797.04, -182.29, 0.008, 60, -124.21, -96.68, 0.01555, 62, 2.54, -98.69, 0.30645, 4, 59, 3.79, -0.92, 0.203, 58, 120.15, -1.56, 0.417, 57, 219.63, -43.82, 0.056, 62, -50.73, -94.36, 0.324, 4, 59, 1.25, 43.24, 0.26167, 58, 125.18, 42.39, 0.56533, 57, 239.83, -4.47, 0.0865, 62, -55.04, -138.38, 0.0865, 5, 59, -56.56, 50.57, 0.0845, 58, 69.47, 59.48, 0.5355, 57, 193.73, 31.16, 0.292, 5, -798.64, -238.15, 0.008, 62, 0.94, -154.54, 0.08, 5, 59, -23, -65.43, 0.026, 58, 82.75, -60.55, 0.2, 57, 163.83, -85.84, 0.096, 60, -141.06, -32.74, 0.03987, 62, -14.32, -34.75, 0.63813, 5, 59, 14.99, -59.6, 0.0915, 58, 121.18, -61.28, 0.2105, 57, 199.53, -100.07, 0.024, 60, -179.49, -32.64, 0.00518, 62, -52.75, -34.66, 0.66882, 5, 57, -59.28, -12.41, 0.056, 5, -579.42, -104.52, 0.63763, 60, 93.42, -18.91, 0.15802, 61, 32.29, -91.79, 0.1399, 62, 220.16, -20.92, 0.00845, 5, 57, -36.23, -64.82, 0.024, 5, -620.14, -64.28, 0.32786, 60, 52.7, 21.34, 0.35485, 61, -8.43, -51.55, 0.27002, 62, 179.44, 19.32, 0.02327, 4, 5, -594.47, -23.98, 0.48196, 60, 78.37, 61.64, 0.13997, 61, 17.24, -11.25, 0.37526, 62, 205.11, 59.62, 0.00282, 4, 57, -91.43, -50.11, 0.008, 5, -563.4, -57.64, 0.74603, 60, 109.44, 27.98, 0.06011, 61, 48.31, -44.91, 0.18586, 3, 5, -571.69, 50.81, 0.70764, 60, 101.14, 136.42, 0.0346, 61, 40.02, 63.53, 0.25776, 1, 5, -536.05, 123.51, 1, 3, 5, -543.31, 28.65, 0.86226, 60, 129.52, 114.27, 0.01171, 61, 68.4, 41.38, 0.12603], "hull": 31, "edges": [42, 62, 62, 64, 64, 66, 66, 10, 10, 12, 12, 68, 68, 70, 70, 72, 72, 38, 38, 40, 40, 42, 62, 72, 64, 70, 66, 68, 70, 74, 74, 76, 76, 14, 14, 12, 68, 76, 72, 78, 78, 36, 36, 38, 38, 42, 78, 74, 74, 80, 80, 82, 82, 84, 18, 20, 84, 18, 16, 18, 16, 14, 76, 86, 86, 84, 20, 22, 84, 22, 80, 86, 86, 16, 78, 88, 88, 90, 90, 26, 90, 82, 80, 88, 88, 32, 32, 30, 26, 28, 30, 28, 30, 90, 32, 34, 34, 36, 36, 32, 64, 92, 92, 6, 6, 66, 6, 8, 10, 8, 92, 94, 94, 46, 46, 42, 94, 62, 94, 96, 96, 50, 50, 48, 48, 46, 46, 44, 44, 42, 96, 98, 6, 4, 98, 4, 98, 92, 96, 100, 100, 102, 102, 54, 54, 52, 52, 50, 52, 100, 100, 104, 4, 2, 104, 2, 104, 98, 2, 0, 0, 60, 102, 0, 0, 104, 58, 60, 102, 58, 56, 58, 56, 54, 22, 24, 24, 26, 82, 24], "width": 165, "height": 258}}, "shouz2": {"shouz2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [43.96, -133.32, -40.89, 40.03, 181.85, 149.07, 266.7, -24.28], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 193, "height": 248}}, "yy2": {"yy2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [727.51, 0, -774.49, 0, -774.49, 506, 727.51, 506], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 600, "height": 202}}, "wu1": {"wu1": {"type": "mesh", "uvs": [0.2492, 0.20639, 0.27874, 0.49481, 0.24523, 0.73263, 0.31523, 0.83255, 0.43012, 0.84567, 0.56916, 0.67182, 0.76407, 0.56685, 0.83829, 0.82116, 0.89642, 0.91136, 0.91505, 0.87063, 0.90752, 0.83754, 0.89477, 0.84556, 0.90522, 0.84456, 0.90418, 0.88066, 0.88369, 0.87966, 0.87174, 0.85128, 0.86339, 0.79655, 0.94682, 0.71619, 1, 0.79163, 1, 0.91956, 0.97096, 1, 0.8164, 1, 0.73023, 1, 0.69705, 0.83911, 0.57942, 1, 0.43218, 1, 0.22017, 1, 0.07152, 0.93928, 0.04622, 0.65554, 0.09259, 0.49998, 0.05361, 0.42289, 0, 0.39993, 0, 0.18015, 0, 0, 0.12815, 0, 0.07002, 0.20312, 0.17124, 0.41141, 0.14333, 0.6129, 0.16316, 0.79987, 0.27556, 0.94244, 0.44038, 0.94244, 0.59515, 0.77679, 0.72509, 0.74562, 0.81298, 0.97533, 0.93335, 0.95401], "triangles": [35, 32, 33, 34, 35, 33, 36, 35, 34, 31, 32, 35, 30, 31, 35, 36, 30, 35, 0, 36, 34, 29, 30, 36, 36, 0, 1, 37, 29, 36, 37, 36, 1, 28, 29, 37, 2, 37, 1, 38, 37, 2, 38, 27, 28, 39, 2, 3, 38, 2, 39, 26, 38, 39, 27, 38, 26, 38, 28, 37, 39, 3, 40, 26, 39, 25, 41, 40, 4, 41, 4, 5, 3, 4, 40, 25, 39, 40, 24, 40, 41, 25, 40, 24, 41, 5, 42, 24, 41, 23, 42, 5, 6, 7, 42, 6, 23, 41, 42, 43, 42, 7, 22, 23, 42, 43, 22, 42, 22, 43, 21, 43, 7, 8, 10, 16, 17, 10, 11, 16, 15, 16, 11, 9, 10, 17, 44, 9, 17, 14, 15, 11, 13, 11, 12, 14, 11, 13, 18, 44, 17, 19, 44, 18, 8, 9, 44, 43, 8, 44, 20, 44, 19, 21, 44, 20, 21, 43, 44], "vertices": [4, 84, -240.78, -164.58, 0.00763, 83, -203.32, 61.59, 0.22548, 82, -72.75, 62.29, 0.42627, 81, 0.61, 232.21, 0.34062, 4, 84, -144.41, -135.46, 0.05711, 83, -106.95, 90.7, 0.45947, 82, 23.63, 91.4, 0.3828, 81, 96.98, 261.33, 0.10063, 5, 85, 12.35, -387.95, 0.00775, 84, -62.47, -158.43, 0.21, 83, -25.01, 67.73, 0.57411, 82, 105.57, 68.44, 0.19252, 81, 178.92, 238.36, 0.01563, 4, 85, 43.18, -329.33, 0.05778, 84, -31.63, -99.82, 0.43839, 83, 5.83, 126.35, 0.45012, 82, 136.4, 127.05, 0.0537, 5, 86, -10.33, -410.36, 0.00788, 85, 42.72, -235.82, 0.21179, 84, -32.1, -6.31, 0.55683, 83, 5.36, 219.86, 0.2165, 82, 135.93, 220.56, 0.007, 4, 86, -75.12, -300.58, 0.05846, 85, -22.08, -126.04, 0.44026, 84, -96.89, 103.48, 0.44213, 83, -59.43, 329.64, 0.05914, 4, 86, -118.99, -144.21, 0.21528, 85, -65.94, 30.33, 0.56313, 84, -140.76, 259.85, 0.21359, 83, -103.3, 486.02, 0.008, 3, 86, -36.07, -79.42, 0.50128, 85, 16.97, 95.12, 0.44026, 84, -57.84, 324.63, 0.05847, 3, 86, -8.01, -30.62, 0.77775, 85, 45.03, 143.92, 0.21437, 84, -29.78, 373.44, 0.00788, 2, 86, -22.6, -16.22, 0.92612, 85, 30.44, 158.33, 0.07388, 2, 86, -33.48, -22.92, 0.98622, 85, 19.56, 151.63, 0.01378, 1, 86, -30.22, -33.13, 1, 1, 86, -31.01, -24.66, 1, 1, 86, -18.74, -24.87, 1, 1, 86, -18.2, -41.52, 1, 1, 86, -27.3, -51.73, 1, 2, 86, -45.47, -59.48, 0.99173, 85, 7.57, 115.06, 0.00827, 2, 86, -76.24, 6.82, 0.95156, 85, -23.2, 181.37, 0.04844, 2, 86, -52.97, 51.34, 0.97796, 85, 0.07, 225.89, 0.02204, 2, 86, -9.66, 53.62, 0.92612, 85, 43.38, 228.17, 0.07388, 2, 86, 18.81, 31.48, 0.78038, 85, 71.85, 206.02, 0.21962, 3, 86, 25.41, -94.01, 0.51418, 85, 78.45, 80.54, 0.47028, 84, 3.64, 310.05, 0.01554, 3, 86, 29.09, -163.96, 0.26754, 85, 82.14, 10.58, 0.65772, 84, 7.32, 240.09, 0.07473, 4, 86, -23.95, -193.77, 0.10909, 85, 29.09, -19.23, 0.6667, 84, -45.73, 210.29, 0.21621, 83, -8.26, 436.45, 0.008, 4, 86, 35.54, -286.41, 0.02184, 85, 88.58, -111.86, 0.47688, 84, 13.77, 117.65, 0.44213, 83, 51.23, 343.82, 0.05914, 4, 85, 94.87, -231.4, 0.21966, 84, 20.06, -1.89, 0.55683, 83, 57.52, 224.28, 0.2165, 82, 188.09, 224.98, 0.007, 4, 85, 103.93, -403.53, 0.05778, 84, 29.12, -174.01, 0.43839, 83, 66.58, 52.16, 0.45012, 82, 197.15, 52.86, 0.0537, 5, 85, 89.73, -525.29, 0.00775, 84, 14.91, -295.78, 0.21, 83, 52.38, -69.61, 0.57411, 82, 182.95, -68.91, 0.19252, 81, 256.3, 101.02, 0.01563, 4, 84, -80.06, -321.38, 0.05711, 83, -42.6, -95.21, 0.45947, 82, 87.97, -94.51, 0.3828, 81, 161.33, 75.42, 0.10063, 4, 84, -134.7, -286.5, 0.00763, 83, -97.24, -60.33, 0.22548, 82, 33.33, -59.63, 0.42627, 81, 106.68, 110.3, 0.34062, 3, 83, -121.67, -93.35, 0.06255, 82, 8.9, -92.65, 0.28329, 81, 82.25, 77.28, 0.65417, 3, 83, -127.16, -137.29, 0.0115, 82, 3.42, -136.59, 0.13294, 81, 76.77, 33.34, 0.85556, 3, 83, -201.56, -141.2, 0.00552, 82, -70.98, -140.5, 0.12365, 81, 2.37, 29.42, 0.87083, 3, 83, -262.55, -144.41, 0.0115, 82, -131.97, -143.71, 0.13294, 81, -58.62, 26.21, 0.85556, 3, 83, -268.02, -40.37, 0.06255, 82, -137.45, -39.67, 0.28329, 81, -64.09, 130.26, 0.65417, 3, 83, -196.78, -83.94, 0.05492, 82, -66.2, -83.24, 0.27508, 81, 7.15, 86.68, 0.67, 4, 84, -168.05, -224.22, 0.0039, 83, -130.59, 1.94, 0.21882, 82, -0.01, 2.64, 0.44928, 81, 73.34, 172.57, 0.328, 4, 84, -98.64, -243.3, 0.04948, 83, -61.18, -17.13, 0.46841, 82, 69.39, -16.43, 0.38911, 81, 142.74, 153.5, 0.093, 5, 85, 38.62, -453.38, 0.00397, 84, -36.2, -223.86, 0.20371, 83, 1.27, 2.3, 0.59404, 82, 131.84, 3.01, 0.19028, 81, 205.19, 172.93, 0.008, 4, 85, 82.08, -359.58, 0.05016, 84, 7.26, -130.07, 0.44571, 83, 44.73, 96.1, 0.45805, 82, 175.3, 96.8, 0.04608, 5, 86, 21.99, -400.31, 0.00403, 85, 75.04, -225.77, 0.20553, 84, 0.22, 3.75, 0.57712, 83, 37.68, 229.92, 0.20974, 82, 168.26, 230.62, 0.00358, 4, 86, -40.7, -277.61, 0.0395, 85, 12.34, -103.07, 0.45912, 84, -62.47, 126.45, 0.44986, 83, -25.01, 352.61, 0.05152, 4, 86, -56.8, -172.67, 0.1834, 85, -3.76, 1.87, 0.60516, 84, -78.57, 231.38, 0.20734, 83, -41.11, 457.55, 0.0041, 3, 86, 17.21, -97.22, 0.49844, 85, 70.25, 77.32, 0.45702, 84, -4.57, 306.84, 0.04454, 3, 86, 4.84, 0.12, 0.78945, 85, 57.89, 174.66, 0.20652, 84, -16.93, 404.18, 0.00403], "hull": 35, "edges": [62, 60, 60, 70, 66, 68, 70, 68, 62, 64, 64, 66, 64, 70, 70, 72, 72, 58, 58, 60, 72, 0, 0, 68, 72, 74, 74, 76, 76, 54, 54, 56, 56, 58, 56, 74, 74, 2, 2, 4, 4, 76, 2, 0, 76, 78, 78, 80, 80, 50, 80, 8, 8, 6, 6, 4, 6, 78, 50, 52, 78, 52, 52, 54, 80, 82, 82, 84, 84, 46, 48, 50, 46, 48, 48, 82, 82, 10, 10, 8, 10, 12, 12, 84, 84, 86, 86, 14, 14, 12, 46, 44, 42, 44, 86, 42, 86, 88, 88, 16, 16, 14, 40, 42, 88, 40, 40, 38, 88, 34, 34, 32, 38, 36, 34, 36, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16], "width": 325, "height": 135}}, "wu2": {"wu2": {"type": "mesh", "uvs": [0.95835, 0.16033, 1, 0.21551, 1, 0.46384, 1, 0.73582, 0.91505, 0.66881, 0.81145, 0.55844, 0.71094, 0.47566, 0.66145, 0.60968, 0.63671, 0.86984, 0.45714, 1, 0.26238, 1, 0.16241, 0.88889, 0.10625, 0.74816, 0.07199, 0.90344, 0, 0.86947, 0, 0.65353, 0.11101, 0.4764, 0.29663, 0.59044, 0.31765, 0.80789, 0.45187, 0.81236, 0.54857, 0.58603, 0.47899, 0.36924, 0.51146, 0.16821, 0.61816, 0, 0.83, 0, 0.92742, 0.41654, 0.80526, 0.25493, 0.6862, 0.21945, 0.5795, 0.47566, 0.57641, 0.72005, 0.2773, 0.88392, 0.44573, 0.92194, 0.20334, 0.66809, 0.07865, 0.64867], "triangles": [12, 13, 33, 13, 14, 33, 14, 15, 33, 12, 33, 32, 33, 16, 32, 33, 15, 16, 9, 10, 31, 10, 30, 31, 10, 11, 30, 30, 18, 31, 11, 32, 30, 18, 30, 17, 30, 32, 17, 31, 18, 19, 11, 12, 32, 32, 16, 17, 21, 22, 28, 29, 28, 7, 20, 21, 28, 9, 29, 8, 9, 31, 29, 31, 19, 29, 8, 29, 7, 19, 20, 29, 29, 20, 28, 7, 28, 6, 6, 26, 5, 28, 27, 6, 6, 27, 26, 27, 22, 23, 27, 28, 22, 26, 27, 24, 27, 23, 24, 4, 2, 3, 4, 25, 2, 4, 5, 25, 5, 26, 25, 25, 1, 2, 25, 0, 1, 25, 26, 0, 26, 24, 0], "vertices": [3, 76, -85.61, -53.43, 0.77971, 77, 14.38, 215.4, 0.21191, 78, -134.74, 333.44, 0.00838, 2, 76, -63.31, -21.42, 0.91884, 77, 36.68, 247.41, 0.08116, 2, 76, 16.97, -31.85, 0.93185, 77, 116.96, 236.98, 0.06815, 2, 76, 104.89, -43.27, 0.91884, 77, 204.88, 225.56, 0.08116, 3, 76, 74.14, -110.46, 0.77971, 77, 174.13, 158.37, 0.21191, 78, 25.01, 276.41, 0.00838, 3, 76, 27.37, -191.2, 0.50382, 77, 127.36, 77.63, 0.43499, 78, -21.76, 195.67, 0.06119, 4, 76, -10.15, -270.56, 0.2235, 77, 89.84, -1.72, 0.54723, 78, -59.28, 116.31, 0.22177, 79, -82.31, 375.11, 0.0075, 4, 76, 27.88, -316.96, 0.05914, 77, 127.87, -48.13, 0.42938, 78, -21.25, 69.91, 0.45505, 79, -44.28, 328.71, 0.05642, 5, 76, 109.34, -348.27, 0.008, 77, 209.33, -79.44, 0.19818, 78, 60.21, 38.6, 0.57799, 79, 37.18, 297.4, 0.2112, 80, 110.27, 494.38, 0.00462, 4, 77, 232.19, -232.89, 0.05506, 78, 83.07, -114.86, 0.45131, 79, 60.04, 143.95, 0.45796, 80, 133.13, 340.93, 0.03566, 4, 77, 211.35, -393.38, 0.00725, 78, 62.23, -275.35, 0.21818, 79, 39.19, -16.54, 0.63079, 80, 112.28, 180.44, 0.14378, 3, 78, 15.61, -353.06, 0.05983, 79, -7.43, -94.26, 0.58563, 80, 65.66, 102.72, 0.35455, 3, 78, -35.9, -393.44, 0.00813, 79, -58.93, -134.63, 0.38234, 80, 14.16, 62.35, 0.60953, 2, 79, -12.4, -169.39, 0.2348, 80, 60.69, 27.59, 0.7652, 2, 79, -31.08, -227.29, 0.21783, 80, 42.01, -30.31, 0.78217, 2, 79, -100.9, -218.22, 0.2348, 80, -27.81, -21.24, 0.7652, 3, 78, -123.24, -378.1, 0.00813, 79, -146.28, -119.3, 0.38234, 80, -73.19, 77.68, 0.60953, 3, 78, -66.51, -229.93, 0.05983, 79, -89.54, 28.87, 0.58563, 80, -16.45, 225.85, 0.35455, 3, 78, 6.04, -221.74, 0.22543, 79, -17, 37.07, 0.63079, 80, 56.09, 234.05, 0.14378, 4, 77, 170.97, -229.35, 0.02011, 78, 21.85, -111.31, 0.48627, 79, -1.19, 147.49, 0.45796, 80, 71.9, 344.47, 0.03566, 4, 77, 108.15, -140.16, 0.10288, 78, -40.97, -22.12, 0.67879, 79, -64.01, 236.68, 0.2137, 80, 9.08, 433.66, 0.00462, 4, 76, -69.38, -457.23, 0.01067, 77, 30.61, -188.4, 0.24579, 78, -118.51, -70.37, 0.67138, 79, -141.54, 188.44, 0.07217, 4, 76, -130.89, -422.03, 0.05914, 77, -30.9, -153.2, 0.44168, 78, -180.02, -35.16, 0.48438, 79, -203.05, 223.64, 0.0148, 3, 76, -173.85, -327.04, 0.2235, 77, -73.86, -58.21, 0.54965, 78, -222.98, 59.82, 0.22685, 3, 76, -151.18, -152.46, 0.50382, 77, -51.19, 116.37, 0.43499, 78, -200.31, 234.4, 0.06119, 3, 76, -6.09, -89.67, 0.79134, 77, 93.9, 179.16, 0.20437, 78, -55.22, 297.2, 0.00429, 3, 76, -71.41, -183.56, 0.50413, 77, 28.58, 85.28, 0.44231, 78, -120.54, 203.31, 0.05356, 4, 76, -95.62, -280.19, 0.21332, 77, 4.37, -11.36, 0.56772, 78, -144.75, 106.68, 0.21512, 79, -167.79, 365.48, 0.00384, 4, 76, -24.21, -378.87, 0.05152, 77, 75.78, -110.04, 0.43339, 78, -73.34, 8, 0.47229, 79, -96.38, 266.8, 0.0428, 5, 76, 54.46, -391.68, 0.0041, 77, 154.45, -122.85, 0.17114, 78, 5.33, -4.81, 0.61897, 79, -17.7, 253.99, 0.20343, 80, 55.39, 450.97, 0.00237, 4, 77, 175.42, -376.22, 0.00371, 78, 26.3, -258.18, 0.21149, 79, 3.26, 0.62, 0.65012, 80, 76.35, 197.6, 0.13468, 4, 77, 205.73, -239.01, 0.037, 78, 56.61, -120.98, 0.46988, 79, 33.58, 137.82, 0.46258, 80, 106.67, 334.81, 0.03054, 3, 78, -51.39, -310.06, 0.0522, 79, -74.43, -51.26, 0.59571, 80, -1.34, 145.72, 0.35209, 3, 78, -71.01, -412.01, 0.00416, 79, -94.05, -153.2, 0.37973, 80, -20.96, 43.78, 0.61611], "hull": 25, "edges": [4, 50, 50, 0, 4, 2, 0, 2, 50, 8, 4, 6, 8, 6, 50, 52, 52, 54, 54, 12, 12, 10, 10, 8, 10, 52, 52, 48, 48, 0, 46, 48, 46, 54, 54, 56, 56, 14, 14, 16, 16, 58, 58, 40, 40, 42, 42, 44, 44, 46, 44, 56, 14, 12, 56, 58, 40, 38, 38, 36, 36, 60, 60, 20, 60, 62, 62, 58, 38, 62, 18, 20, 62, 18, 18, 16, 60, 64, 64, 22, 22, 20, 64, 34, 34, 36, 64, 66, 66, 28, 28, 26, 26, 24, 24, 22, 24, 66, 66, 32, 32, 34, 28, 30, 32, 30], "width": 332, "height": 130}}, "wu3": {"wu3": {"type": "mesh", "uvs": [0.15992, 0.24559, 0.19559, 0.45198, 0.30148, 0.48572, 0.35164, 0.66631, 0.35387, 0.73973, 0.37255, 0.70835, 0.43432, 0.62587, 0.52891, 0.81145, 0.64955, 0.73928, 0.81363, 0.74615, 0.87584, 0.85385, 0.93987, 0.81287, 0.93093, 0.76947, 0.88503, 0.79211, 0.86568, 0.73304, 0.89996, 0.67003, 0.99999, 0.7104, 0.99999, 0.87225, 0.97481, 0.96934, 0.88333, 1, 0.78081, 0.95923, 0.6428, 0.97297, 0.52504, 0.99999, 0.40923, 0.93517, 0.3214, 0.93173, 0.23349, 0.9362, 0.12314, 0.8211, 0.14989, 0.72981, 0.0217, 0.74767, 0, 0.67623, 0, 0.26147, 0, 0, 0.13651, 0, 0.06072, 0.28131, 0.0975, 0.577, 0.26916, 0.64448, 0.24018, 0.75164, 0.27696, 0.8588, 0.35518, 0.82176, 0.42756, 0.78395, 0.51636, 0.91455, 0.67175, 0.82348, 0.79336, 0.87503, 0.89164, 0.92936, 0.97203, 0.82525, 0.94088, 0.73304], "triangles": [3, 35, 2, 36, 27, 35, 35, 1, 2, 27, 34, 35, 36, 35, 3, 26, 27, 36, 34, 1, 35, 33, 30, 31, 32, 33, 31, 0, 33, 32, 1, 33, 0, 34, 29, 30, 34, 30, 33, 28, 29, 34, 1, 34, 33, 28, 34, 27, 5, 38, 4, 4, 36, 3, 25, 26, 36, 4, 37, 36, 37, 4, 38, 24, 37, 38, 25, 36, 37, 25, 37, 24, 41, 7, 8, 21, 40, 41, 5, 39, 38, 39, 5, 6, 7, 39, 6, 40, 39, 7, 41, 40, 7, 23, 38, 39, 23, 39, 40, 22, 40, 21, 23, 40, 22, 24, 38, 23, 41, 8, 9, 21, 41, 20, 42, 41, 9, 42, 9, 10, 42, 10, 43, 20, 41, 42, 20, 42, 43, 19, 20, 43, 19, 43, 18, 45, 15, 16, 14, 15, 45, 12, 14, 45, 13, 14, 12, 44, 11, 12, 44, 45, 16, 44, 12, 45, 17, 44, 16, 18, 44, 17, 43, 11, 44, 43, 44, 18, 43, 10, 11], "vertices": [4, 89, -234.62, -77.74, 0.0075, 88, -131.3, 42.31, 0.2885, 87, -22.54, 128.76, 0.60837, 90, -152.95, -76.76, 0.09562, 4, 89, -154.75, -53.16, 0.05643, 88, -51.43, 66.88, 0.3254, 87, 57.33, 153.33, 0.33397, 90, -73.08, -52.18, 0.2842, 5, 91, -125.73, -89.65, 0.00825, 89, -141.69, 19.8, 0.20757, 88, -38.37, 139.85, 0.22177, 87, 70.38, 226.29, 0.11885, 90, -60.02, 20.78, 0.44355, 5, 91, -55.84, -55.09, 0.06051, 89, -71.8, 54.36, 0.43312, 88, 31.52, 174.4, 0.085, 87, 140.27, 260.85, 0.01563, 90, 9.87, 55.34, 0.40575, 4, 91, -27.43, -53.56, 0.22735, 89, -43.39, 55.89, 0.54723, 88, 59.93, 175.94, 0.01563, 90, 38.28, 56.88, 0.2098, 4, 92, -69.09, -288.66, 0.009, 91, -39.57, -40.69, 0.49993, 89, -55.53, 68.76, 0.43125, 90, 26.14, 69.75, 0.05982, 4, 92, -101.01, -246.1, 0.06458, 91, -71.49, 1.87, 0.72151, 89, -87.45, 111.32, 0.20578, 90, -5.78, 112.31, 0.00812, 4, 93, -5.58, -305.89, 0.0075, 92, -29.19, -180.93, 0.23136, 91, 0.33, 67.04, 0.70539, 89, -15.63, 176.49, 0.05574, 4, 93, -33.51, -222.77, 0.05642, 92, -57.12, -97.8, 0.4678, 91, -27.6, 150.17, 0.4684, 89, -43.56, 259.62, 0.00737, 3, 93, -30.85, -109.72, 0.21582, 92, -54.46, 15.25, 0.58179, 91, -24.94, 263.22, 0.20239, 3, 93, 10.83, -66.86, 0.49362, 92, -12.78, 58.11, 0.45471, 91, 16.74, 306.08, 0.05167, 3, 93, -5.03, -22.74, 0.78108, 92, -28.65, 102.23, 0.2123, 91, 0.87, 350.2, 0.00663, 2, 93, -21.83, -28.9, 0.94017, 92, -45.44, 96.07, 0.05983, 2, 93, -13.06, -60.52, 0.98917, 92, -36.68, 64.44, 0.01083, 2, 93, -35.92, -73.86, 0.9948, 92, -59.54, 51.11, 0.0052, 1, 93, -60.31, -50.24, 1, 2, 93, -44.69, 18.68, 0.97747, 92, -68.3, 143.65, 0.02253, 2, 93, 17.95, 18.68, 0.88842, 92, -5.67, 143.65, 0.11158, 3, 93, 55.53, 1.33, 0.72802, 92, 31.91, 126.3, 0.26315, 91, 61.43, 374.27, 0.00883, 3, 93, 67.39, -61.7, 0.48047, 92, 43.77, 63.27, 0.46786, 91, 73.29, 311.24, 0.05167, 3, 93, 51.61, -132.33, 0.21312, 92, 28, -7.36, 0.5845, 91, 57.52, 240.61, 0.20239, 4, 93, 56.93, -227.42, 0.05643, 92, 33.32, -102.46, 0.4678, 91, 62.84, 145.51, 0.4684, 89, 46.87, 254.96, 0.00737, 4, 93, 67.39, -308.55, 0.0075, 92, 43.77, -183.59, 0.23136, 91, 73.29, 64.38, 0.70539, 89, 57.33, 173.83, 0.05574, 4, 92, 18.69, -263.39, 0.06458, 91, 48.21, -15.42, 0.72151, 89, 32.25, 94.03, 0.20578, 90, 113.92, 95.02, 0.00812, 4, 92, 17.36, -323.9, 0.009, 91, 46.88, -75.93, 0.49993, 89, 30.92, 33.52, 0.43125, 90, 112.59, 34.5, 0.05982, 4, 91, 48.6, -136.5, 0.22735, 89, 32.64, -27.05, 0.54723, 87, 244.72, 179.44, 0.01563, 90, 114.32, -26.07, 0.2098, 5, 91, 4.06, -212.53, 0.06051, 89, -11.9, -103.08, 0.43312, 88, 91.42, 16.96, 0.03813, 87, 200.18, 103.41, 0.0625, 90, 69.77, -102.1, 0.40575, 5, 91, -31.27, -194.1, 0.00825, 89, -47.23, -84.65, 0.20757, 88, 56.09, 35.4, 0.12115, 87, 164.85, 121.84, 0.21948, 90, 34.44, -83.67, 0.44355, 4, 89, -40.32, -172.97, 0.05643, 88, 63, -52.92, 0.19415, 87, 171.76, 33.52, 0.46002, 90, 41.36, -171.99, 0.28941, 4, 89, -67.96, -187.92, 0.01, 88, 35.36, -67.88, 0.14067, 87, 144.11, 18.57, 0.73156, 90, 13.71, -186.94, 0.11778, 3, 88, -125.16, -67.88, 0.15092, 87, -16.4, 18.57, 0.81575, 90, -146.8, -186.94, 0.03333, 2, 88, -226.35, -67.88, 0.09139, 87, -117.59, 18.57, 0.90861, 3, 88, -226.35, 26.18, 0.15389, 87, -117.59, 112.63, 0.82528, 90, -247.99, -92.88, 0.02083, 4, 89, -220.8, -146.09, 0.0048, 88, -117.48, -26.04, 0.2779, 87, -8.72, 60.4, 0.61897, 90, -139.12, -145.11, 0.09832, 4, 89, -106.36, -120.75, 0.0488, 88, -3.04, -0.7, 0.29214, 87, 105.71, 85.75, 0.37786, 90, -24.69, -119.76, 0.2812, 5, 91, -64.29, -111.93, 0.00422, 89, -80.25, -2.48, 0.20158, 88, 23.07, 117.57, 0.18744, 87, 131.82, 204.02, 0.14056, 90, 1.42, -1.49, 0.4662, 5, 91, -22.82, -131.89, 0.05288, 89, -38.78, -22.44, 0.44024, 88, 64.54, 97.6, 0.05802, 87, 173.3, 184.05, 0.03498, 90, 42.89, -21.46, 0.41388, 4, 91, 18.65, -106.55, 0.21708, 89, 2.69, 2.9, 0.56772, 88, 106.01, 122.95, 0.008, 90, 84.36, 3.88, 0.2072, 4, 92, -25.2, -300.63, 0.00461, 91, 4.32, -52.66, 0.50502, 89, -11.64, 56.79, 0.43817, 90, 70.03, 57.78, 0.0522, 4, 92, -39.84, -250.76, 0.05696, 91, -10.32, -2.79, 0.73912, 89, -26.28, 106.66, 0.19976, 90, 55.4, 107.65, 0.00416, 4, 93, 34.32, -314.54, 0.00384, 92, 10.71, -189.57, 0.22452, 91, 40.23, 58.4, 0.72352, 89, 24.27, 167.85, 0.04812, 4, 93, -0.92, -207.47, 0.0488, 92, -24.54, -82.51, 0.47734, 91, 4.98, 165.46, 0.47008, 89, -10.98, 274.91, 0.00378, 3, 93, 19.03, -123.68, 0.2058, 92, -4.59, 1.28, 0.60156, 91, 24.93, 249.25, 0.19264, 3, 93, 40.05, -55.97, 0.49615, 92, 16.44, 69, 0.45981, 91, 45.96, 316.97, 0.04404, 3, 93, -0.24, -0.58, 0.81333, 92, -23.85, 124.39, 0.18327, 91, 5.67, 372.36, 0.00339, 2, 93, -35.92, -22.04, 0.9595, 92, -59.54, 102.92, 0.0405], "hull": 33, "edges": [60, 62, 60, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 50, 50, 52, 52, 54, 54, 70, 70, 4, 4, 6, 6, 72, 72, 52, 6, 8, 8, 74, 4, 2, 2, 68, 68, 56, 56, 54, 58, 60, 56, 58, 2, 0, 62, 64, 0, 64, 0, 66, 74, 76, 76, 78, 78, 12, 12, 10, 10, 76, 10, 8, 76, 48, 48, 46, 46, 78, 48, 50, 78, 80, 80, 82, 82, 84, 84, 40, 40, 42, 42, 82, 82, 16, 16, 18, 18, 84, 16, 14, 14, 12, 14, 80, 80, 44, 44, 46, 44, 42, 84, 86, 86, 88, 88, 22, 22, 20, 20, 18, 20, 86, 86, 38, 38, 40, 88, 34, 34, 36, 36, 38, 88, 90, 90, 24, 24, 26, 26, 28, 28, 30, 34, 32, 30, 32, 24, 22, 90, 32, 90, 28], "width": 275, "height": 154}}, "wu4": {"wu4": {"type": "mesh", "uvs": [1, 0.06918, 1, 0.17296, 1, 0.36948, 1, 0.58587, 1, 0.78239, 0.9081, 0.72277, 0.75651, 0.69172, 0.70752, 0.76896, 0.73395, 0.93532, 0.57244, 0.99999, 0.38532, 0.97084, 0.27992, 0.83588, 0.16619, 0.87252, 0.04584, 0.88132, 0, 0.81243, 0, 0.71861, 0.03541, 0.67317, 0.07617, 0.76845, 0.15576, 0.75086, 0.28182, 0.66584, 0.39547, 0.72635, 0.49832, 0.76527, 0.53505, 0.66128, 0.54612, 0.42023, 0.6994, 0.31495, 0.91096, 0.41364, 0.93555, 0.25783, 0.89467, 0.20983, 0.87955, 0.13321, 0.92952, 0, 1, 0, 0.92666, 0.57704, 0.96521, 0.15971, 0.97092, 0.08243, 0.92381, 0.14425, 0.94614, 0.16065, 0.92116, 0.19578, 0.96356, 0.24261, 0.93782, 0.1829, 0.94447, 0.19955, 0.94382, 0.18273, 0.9608, 0.17663, 0.9793, 0.21555, 0.95405, 0.22211, 0.70591, 0.52738, 0.60229, 0.71022, 0.52081, 0.89621, 0.40134, 0.82646, 0.27802, 0.75526, 0.15197, 0.83441, 0.04963, 0.81536], "triangles": [6, 44, 31, 44, 24, 25, 4, 5, 3, 5, 31, 3, 3, 25, 2, 3, 31, 25, 25, 26, 2, 26, 37, 2, 5, 6, 31, 44, 25, 31, 26, 36, 43, 37, 43, 42, 36, 39, 43, 36, 38, 39, 42, 43, 41, 41, 43, 40, 42, 41, 1, 1, 41, 32, 36, 27, 34, 38, 40, 39, 43, 39, 40, 36, 34, 38, 34, 27, 28, 40, 38, 35, 40, 35, 41, 35, 38, 34, 41, 35, 32, 32, 33, 1, 33, 0, 1, 32, 35, 33, 33, 35, 34, 33, 34, 29, 34, 28, 29, 0, 33, 30, 33, 29, 30, 37, 42, 2, 42, 1, 2, 27, 36, 26, 26, 43, 37, 49, 48, 11, 48, 18, 19, 13, 49, 12, 13, 50, 49, 13, 14, 50, 12, 49, 11, 50, 17, 49, 49, 18, 48, 49, 17, 18, 17, 50, 15, 50, 14, 15, 15, 16, 17, 9, 46, 45, 10, 47, 46, 10, 11, 47, 11, 48, 47, 48, 20, 47, 47, 20, 21, 48, 19, 20, 10, 46, 9, 47, 21, 46, 9, 7, 8, 6, 7, 44, 44, 7, 45, 45, 22, 44, 22, 23, 44, 23, 24, 44, 9, 45, 7, 45, 46, 21, 45, 21, 22], "vertices": [1, 96, -157.84, -3.91, 1, 1, 96, -124.45, -5.26, 1, 2, 97, -59.37, 146.15, 0.0825, 96, -61.22, -7.82, 0.9175, 3, 97, 10.23, 149.44, 0.11337, 98, -92.4, 291.03, 0.00496, 96, 8.4, -10.64, 0.88167, 3, 97, 73.44, 152.43, 0.13411, 98, -29.19, 294.02, 0.01033, 96, 71.63, -13.21, 0.85556, 4, 97, 56.42, 105.81, 0.29486, 98, -46.21, 247.4, 0.0483, 99, -45.98, 376.18, 0.00018, 96, 50.59, -58.16, 0.65667, 4, 97, 50, 29.93, 0.50349, 98, -52.63, 171.52, 0.15364, 99, -52.4, 300.29, 0.00225, 96, 37.55, -133.18, 0.34062, 4, 97, 75.99, 6.73, 0.59095, 98, -26.64, 148.32, 0.2998, 99, -26.41, 277.1, 0.00862, 96, 61.41, -158.57, 0.10063, 3, 97, 128.88, 22.41, 0.59603, 98, 26.25, 164, 0.39273, 99, 26.48, 292.77, 0.01123, 3, 97, 153.48, -56.95, 0.37553, 98, 50.85, 84.64, 0.56397, 99, 51.07, 213.41, 0.0605, 3, 97, 148.5, -150.48, 0.15756, 98, 45.87, -8.89, 0.61509, 99, 46.09, 119.89, 0.22734, 3, 97, 107.57, -204.96, 0.03829, 98, 4.93, -63.37, 0.45278, 99, 5.16, 65.41, 0.50892, 3, 97, 122.03, -260.98, 0.0055, 98, 19.39, -119.39, 0.20841, 99, 19.62, 9.39, 0.78609, 2, 98, 25.05, -179.12, 0.05575, 99, 25.28, -50.34, 0.94425, 2, 98, 3.97, -202.97, 0.00983, 99, 4.2, -74.19, 0.99017, 2, 98, -26.2, -204.39, 0.00472, 99, -25.98, -75.62, 0.99528, 2, 98, -41.65, -187.47, 0.00983, 99, -41.42, -58.69, 0.99017, 2, 98, -11.96, -165.75, 0.05575, 99, -11.74, -36.97, 0.94425, 3, 97, 83.14, -268.01, 0.0055, 98, -19.49, -126.42, 0.20841, 99, -19.27, 2.35, 0.78609, 3, 97, 52.83, -206.6, 0.04554, 98, -49.8, -65.01, 0.44553, 99, -49.57, 63.77, 0.50892, 3, 97, 69.62, -149.14, 0.1851, 98, -33.01, -7.55, 0.58755, 99, -32.78, 121.22, 0.22734, 3, 97, 79.72, -97.39, 0.44702, 98, -22.91, 44.2, 0.49247, 99, -22.68, 172.98, 0.0605, 4, 97, 45.41, -80.7, 0.59907, 98, -57.22, 60.89, 0.34422, 99, -56.99, 189.67, 0.02588, 96, 23.28, -242.99, 0.03083, 4, 97, -32.38, -78.85, 0.6583, 98, -135.01, 62.74, 0.20088, 99, -134.78, 191.51, 0.00887, 96, -54.05, -234.33, 0.13194, 4, 97, -69.84, -4.21, 0.53926, 98, -172.47, 137.38, 0.09249, 99, -172.25, 266.16, 0.00179, 96, -84.83, -156.69, 0.36646, 3, 97, -43.07, 102.53, 0.23431, 98, -145.71, 244.12, 0.01719, 96, -48.81, -52.7, 0.7485, 1, 96, -98.44, -38.44, 1, 1, 96, -114.71, -58.15, 1, 1, 96, -139.66, -64.68, 1, 1, 96, -181.52, -38.07, 1, 1, 96, -180.09, -3, 1, 4, 97, 9.11, 112.83, 0.2784, 98, -93.52, 254.42, 0.03517, 99, -93.29, 383.19, 0.00043, 96, 4.08, -47.02, 0.686, 1, 96, -129.41, -22.4, 1, 1, 96, -154.16, -18.55, 1, 1, 96, -135.22, -42.8, 1, 1, 96, -129.49, -31.9, 1, 1, 96, -118.7, -44.79, 1, 1, 96, -102.77, -24.3, 1, 1, 96, -122.5, -36.33, 1, 1, 96, -117.01, -33.24, 1, 1, 96, -122.44, -33.34, 1, 1, 96, -124.06, -24.81, 1, 1, 96, -111.16, -16.12, 1, 1, 96, -109.56, -28.76, 1, 4, 97, -1.67, 2.26, 0.52341, 98, -104.3, 143.85, 0.14025, 99, -104.08, 272.63, 0.00384, 96, -16.35, -156.22, 0.3325, 4, 97, 59.57, -46.5, 0.56598, 98, -43.06, 95.09, 0.33361, 99, -42.83, 223.86, 0.01991, 96, 40.39, -210.16, 0.0805, 3, 97, 121.31, -84.21, 0.42546, 98, 18.68, 57.38, 0.52124, 99, 18.91, 186.15, 0.05331, 3, 97, 101.69, -144.7, 0.16528, 98, -0.95, -3.11, 0.61764, 99, -0.72, 125.66, 0.21708, 3, 97, 81.68, -207.13, 0.03792, 98, -20.95, -65.54, 0.45245, 99, -20.72, 63.24, 0.50963, 3, 97, 110.1, -268.63, 0.00282, 98, 7.47, -127.04, 0.2011, 99, 7.7, 1.74, 0.79608, 2, 98, 3.75, -178.24, 0.04812, 99, 3.97, -49.46, 0.95188], "hull": 31, "edges": [50, 62, 62, 10, 10, 8, 8, 6, 62, 6, 6, 4, 50, 4, 4, 2, 2, 64, 64, 66, 58, 60, 66, 58, 2, 0, 0, 60, 0, 66, 66, 68, 68, 56, 56, 58, 68, 70, 70, 64, 68, 72, 74, 52, 52, 54, 54, 56, 54, 72, 72, 76, 76, 78, 76, 70, 78, 80, 80, 82, 82, 84, 84, 74, 72, 86, 86, 80, 62, 88, 88, 12, 10, 12, 88, 48, 48, 50, 88, 90, 90, 44, 44, 46, 46, 48, 90, 14, 14, 16, 16, 18, 18, 92, 92, 42, 42, 40, 40, 94, 94, 20, 20, 18, 94, 92, 92, 90, 44, 42, 94, 96, 96, 98, 98, 24, 24, 22, 22, 20, 22, 96, 96, 38, 38, 36, 36, 98, 38, 40, 98, 100, 100, 30, 30, 32, 32, 34, 34, 36, 34, 100, 100, 26, 28, 30, 26, 28, 26, 24, 14, 12, 52, 50], "width": 498, "height": 322}}, "wu5": {"wu5": {"type": "mesh", "uvs": [0.06675, 0.10143, 0.20285, 0.06795, 0.35361, 0.23851, 0.47547, 0.50064, 0.63828, 0.45409, 0.77243, 0.65252, 0.91668, 0.69488, 1, 0.79287, 1, 0.91291, 1, 1, 0.89927, 0.90556, 0.74887, 0.92935, 0.61268, 0.77991, 0.43553, 0.9269, 0.31367, 0.76031, 0.20921, 0.79525, 0.08201, 0.87132, 0, 0.76481, 0, 0.35096, 0, 0, 0.0782, 0.51528, 0.20412, 0.50615, 0.47547, 0.68437, 0.33313, 0.55208, 0.62497, 0.62557, 0.77652, 0.79706, 0.8962, 0.80757], "triangles": [0, 1, 21, 15, 20, 21, 21, 20, 0, 15, 16, 20, 18, 19, 0, 20, 18, 0, 17, 18, 20, 17, 20, 16, 21, 1, 2, 15, 21, 14, 23, 21, 2, 14, 21, 23, 23, 2, 3, 14, 23, 22, 22, 23, 3, 13, 14, 22, 24, 3, 4, 22, 3, 24, 12, 22, 24, 13, 22, 12, 26, 25, 5, 24, 5, 25, 6, 26, 5, 26, 6, 7, 8, 10, 26, 8, 26, 7, 11, 12, 25, 10, 11, 25, 10, 25, 26, 10, 8, 9, 24, 4, 5, 25, 12, 24], "vertices": [4, 109, 148.41, 349.1, 0.00346, 108, 120.77, 203.53, 0.05239, 107, 88.55, 62.7, 0.28999, 104, -149.24, 44.85, 0.65417, 4, 109, 154.11, 280.93, 0.02545, 108, 126.47, 135.36, 0.18123, 107, 94.25, -5.47, 0.4527, 104, -157.69, 112.73, 0.34062, 4, 109, 117.03, 206.25, 0.09675, 108, 89.39, 60.67, 0.37131, 107, 57.18, -80.16, 0.43132, 104, -123.67, 188.86, 0.10063, 3, 109, 61.1, 146.37, 0.2212, 108, 33.46, 0.8, 0.56762, 107, 1.24, -140.03, 0.21118, 3, 109, 69.27, 64.79, 0.49618, 108, 41.63, -80.78, 0.44468, 107, 9.42, -221.61, 0.05915, 3, 109, 26.53, -1.48, 0.7765, 108, -1.11, -147.05, 0.2155, 107, -33.32, -287.88, 0.008, 2, 109, 16.31, -73.42, 0.94086, 108, -11.34, -219, 0.05914, 2, 109, -4.96, -114.68, 0.98933, 108, -32.61, -260.26, 0.01067, 2, 109, -30.05, -114.21, 0.99488, 108, -57.69, -259.78, 0.00512, 2, 109, -48.25, -113.86, 0.98933, 108, -75.89, -259.44, 0.01067, 2, 109, -27.55, -63.88, 0.94086, 108, -55.19, -209.46, 0.05914, 3, 109, -31.09, 11.4, 0.7765, 108, -58.73, -134.18, 0.2155, 107, -90.95, -275, 0.008, 3, 109, 1.43, 78.89, 0.49618, 108, -26.21, -66.69, 0.44468, 107, -58.42, -207.51, 0.05915, 3, 109, -27.59, 168.03, 0.2212, 108, -55.24, 22.46, 0.56762, 107, -87.45, -118.37, 0.21118, 4, 109, 8.38, 228.29, 0.09675, 108, -19.27, 82.71, 0.37131, 107, -51.48, -58.12, 0.43132, 104, -14.21, 171.24, 0.10063, 4, 109, 2.07, 280.65, 0.02545, 108, -25.57, 135.07, 0.18123, 107, -57.79, -5.76, 0.4527, 104, -5.79, 119.18, 0.34062, 4, 109, -12.62, 344.54, 0.00346, 108, -40.26, 198.96, 0.05239, 107, -72.47, 58.14, 0.28999, 104, 11.47, 55.94, 0.65417, 3, 108, -17.22, 239.54, 0.01067, 107, -49.43, 98.71, 0.13378, 104, -9.9, 14.46, 0.85556, 3, 108, 69.26, 237.89, 0.00512, 107, 37.05, 97.06, 0.12405, 104, -96.38, 12.6, 0.87083, 3, 108, 142.6, 236.49, 0.01067, 107, 110.38, 95.67, 0.13378, 104, -169.71, 11.02, 0.85556, 4, 109, 61.82, 345.03, 0.00167, 108, 34.18, 199.45, 0.04819, 107, 1.96, 58.63, 0.28014, 104, -62.88, 52.43, 0.67, 4, 109, 62.53, 282.04, 0.02168, 108, 34.89, 136.46, 0.17445, 107, 2.67, -4.36, 0.47587, 104, -66.14, 115.34, 0.328, 3, 109, 22.7, 147.1, 0.20935, 108, -4.94, 1.53, 0.58367, 107, -37.15, -139.3, 0.20698, 4, 109, 51.7, 217.73, 0.08986, 108, 24.06, 72.16, 0.37601, 107, -8.15, -68.67, 0.44114, 104, -57.93, 180.03, 0.093, 3, 109, 33.57, 72.13, 0.49587, 108, 5.92, -73.45, 0.45261, 107, -26.29, -214.27, 0.05152, 3, 109, -3.71, -2.95, 0.78668, 108, -31.35, -148.53, 0.20922, 107, -63.57, -289.35, 0.0041, 2, 109, -7.05, -62.74, 0.94848, 108, -34.69, -208.31, 0.05152], "hull": 20, "edges": [36, 38, 34, 36, 34, 32, 32, 40, 40, 0, 0, 38, 36, 40, 40, 42, 42, 2, 2, 0, 42, 30, 30, 32, 2, 4, 4, 6, 6, 44, 44, 26, 26, 28, 28, 30, 42, 46, 46, 44, 44, 48, 48, 50, 50, 22, 22, 24, 24, 26, 24, 48, 48, 8, 8, 10, 10, 50, 8, 6, 4, 46, 46, 28, 50, 52, 18, 16, 52, 16, 18, 20, 20, 22, 20, 52, 52, 12, 12, 10, 16, 14, 12, 14], "width": 500, "height": 209}}, "wu6": {"wu6": {"type": "mesh", "uvs": [0.52428, 0.37686, 0.69111, 0.47629, 0.84404, 0.39344, 1, 0.56468, 1, 0.75801, 1, 1, 0.93614, 1, 0.85447, 0.77458, 0.70675, 1, 0.45477, 1, 0.33312, 0.52601, 0.22537, 0.52048, 0.12805, 0.67515, 0, 0.81325, 0, 0.39896, 0, 0, 0.1072, 0, 0.19583, 0, 0.39047, 0, 0.09677, 0.39896, 0.20278, 0.26639, 0.32964, 0.2222, 0.51385, 0.69172, 0.71196, 0.72487, 0.86837, 0.63648], "triangles": [19, 20, 11, 16, 17, 20, 12, 19, 11, 13, 14, 19, 14, 15, 19, 19, 15, 16, 13, 19, 12, 19, 16, 20, 9, 10, 22, 0, 22, 21, 11, 21, 10, 22, 10, 21, 21, 18, 0, 21, 17, 18, 11, 20, 21, 20, 17, 21, 6, 4, 5, 7, 24, 6, 6, 24, 4, 8, 23, 7, 8, 22, 23, 7, 23, 24, 24, 3, 4, 22, 1, 23, 23, 2, 24, 23, 1, 2, 24, 2, 3, 9, 22, 8, 22, 0, 1], "vertices": [2, 106, 28.76, 48.92, 0.53163, 105, -13.68, -57.52, 0.46837, 2, 106, 19.56, 4.54, 0.78633, 105, -22.88, -101.9, 0.21367, 2, 106, 25.74, -36.42, 0.94222, 105, -16.7, -142.86, 0.05778, 2, 106, 10.57, -77.78, 0.98967, 105, -31.87, -184.21, 0.01033, 2, 106, -5.67, -77.47, 0.99504, 105, -48.11, -183.91, 0.00496, 1, 106, -25.99, -77.08, 1, 2, 106, -25.67, -60.04, 0.98967, 105, -68.11, -166.47, 0.01033, 2, 106, -6.32, -38.59, 0.94222, 105, -48.76, -145.03, 0.05778, 2, 106, -24.5, 1.2, 0.78634, 105, -66.94, -105.24, 0.21366, 2, 106, -23.22, 68.47, 0.53165, 105, -65.66, -37.97, 0.46835, 3, 106, 17.21, 100.19, 0.23429, 105, -25.23, -6.25, 0.54662, 104, 92.5, 105.63, 0.21909, 3, 106, 18.22, 128.94, 0.06171, 105, -24.22, 22.5, 0.42936, 104, 92.66, 76.86, 0.50892, 3, 106, 5.72, 155.17, 0.00831, 105, -36.71, 48.73, 0.20806, 104, 106.2, 51.16, 0.78364, 2, 105, -47.66, 83.13, 0.07918, 104, 118.54, 17.22, 0.92082, 2, 105, -12.87, 82.47, 0.06625, 104, 83.74, 16.48, 0.93375, 2, 105, 20.64, 81.83, 0.07918, 104, 50.24, 15.75, 0.92082, 3, 106, 62.53, 159.65, 0.00831, 105, 20.09, 53.22, 0.20806, 104, 49.62, 44.37, 0.78364, 3, 106, 62.08, 135.99, 0.06171, 105, 19.64, 29.56, 0.42937, 104, 49.11, 68.03, 0.50892, 3, 106, 61.09, 84.03, 0.23428, 105, 18.65, -22.4, 0.54662, 104, 48, 119.98, 0.21909, 3, 106, 29.08, 163.07, 0.00422, 105, -13.36, 56.64, 0.20064, 104, 83.19, 42.31, 0.79514, 3, 106, 39.67, 134.56, 0.05346, 105, -2.76, 28.13, 0.4369, 104, 71.45, 70.37, 0.50963, 3, 106, 42.74, 100.63, 0.22267, 105, 0.3, -5.81, 0.56447, 104, 67.01, 104.15, 0.21286, 2, 106, 2.37, 52.2, 0.52749, 105, -40.07, -54.23, 0.47251, 2, 106, -1.42, -0.63, 0.79354, 105, -43.86, -107.07, 0.20646, 2, 106, 5.21, -42.52, 0.94984, 105, -37.23, -148.96, 0.05016], "hull": 19, "edges": [26, 24, 24, 38, 30, 32, 38, 32, 26, 28, 28, 30, 28, 38, 38, 40, 40, 42, 42, 20, 20, 22, 22, 24, 22, 40, 32, 34, 40, 34, 34, 36, 36, 42, 42, 44, 44, 46, 46, 16, 16, 18, 18, 20, 18, 44, 44, 0, 0, 2, 2, 46, 0, 36, 46, 48, 10, 8, 48, 8, 10, 12, 12, 14, 14, 16, 14, 48, 48, 4, 4, 2, 8, 6, 4, 6], "width": 267, "height": 84}}, "wu7": {"wu7": {"type": "mesh", "uvs": [0.97574, 0.0495, 1, 0.12084, 1, 0.7045, 1, 0.86879, 0.86226, 0.84502, 0.81547, 0.7288, 0.74816, 0.72092, 0.6871, 0.75206, 0.68263, 0.84922, 0.55062, 0.94542, 0.3598, 0.88587, 0.30491, 0.77135, 0.29112, 0.97498, 0.13109, 1, 0.03171, 1, 1e-05, 0.92889, 1e-05, 0.83753, 0.03231, 0.75988, 0.10394, 0.78875, 0.10648, 0.82955, 0.08032, 0.86244, 0.06098, 0.82357, 0.05757, 0.85646, 0.08627, 0.88166, 0.12716, 0.82632, 0.09057, 0.62476, 0.16245, 0.44153, 0.37941, 0.33617, 0.37157, 0.61331, 0.48919, 0.61102, 0.55634, 0.45374, 0.57114, 0.19218, 0.70436, 0, 0.86226, 0, 0.86063, 0.39324, 0.93236, 0.59601, 0.65256, 0.56832, 0.73417, 0.37134, 0.53363, 0.7599, 0.34412, 0.70951, 0.21342, 0.59499, 0.21735, 0.80113, 0.12978, 0.92252, 0.03248, 0.90186, 0.04142, 0.7983], "triangles": [41, 24, 25, 42, 41, 12, 44, 17, 18, 19, 21, 44, 19, 44, 18, 16, 17, 44, 22, 44, 21, 43, 16, 44, 20, 21, 19, 22, 43, 44, 43, 22, 23, 42, 24, 41, 23, 24, 42, 43, 23, 42, 15, 16, 43, 14, 15, 43, 14, 43, 42, 13, 42, 12, 14, 42, 13, 12, 41, 11, 39, 40, 28, 40, 26, 27, 28, 40, 27, 25, 26, 40, 11, 40, 39, 40, 41, 25, 11, 41, 40, 11, 39, 10, 38, 36, 7, 36, 38, 29, 39, 28, 29, 38, 39, 29, 10, 39, 38, 9, 38, 8, 10, 38, 9, 36, 29, 30, 8, 38, 7, 7, 36, 6, 31, 32, 37, 30, 31, 37, 36, 30, 37, 36, 37, 6, 37, 32, 33, 6, 37, 34, 34, 37, 33, 34, 0, 35, 33, 0, 34, 0, 1, 35, 35, 1, 2, 5, 6, 34, 35, 5, 34, 4, 5, 35, 4, 35, 2, 4, 2, 3], "vertices": [2, 94, -180.76, -66.75, 0.89375, 95, -89.32, 93.97, 0.10625, 2, 94, -159.78, -52.86, 0.97917, 95, -68.34, 107.86, 0.02083, 2, 94, 16.88, -44.51, 0.9875, 95, 108.31, 116.2, 0.0125, 1, 94, 66.6, -42.17, 1, 3, 94, 62.86, -115.57, 0.94583, 95, 154.29, 45.15, 0.04367, 100, 15.55, 176.2, 0.0105, 3, 94, 28.85, -142.04, 0.79028, 95, 120.29, 18.68, 0.13499, 100, -18.45, 149.72, 0.07473, 3, 94, 28.15, -177.86, 0.48458, 95, 119.59, -17.14, 0.30009, 100, -19.15, 113.91, 0.21533, 4, 94, 39.11, -209.8, 0.23458, 95, 130.55, -49.08, 0.27693, 100, -8.2, 81.97, 0.47394, 101, 41.77, 253.49, 0.01455, 4, 94, 68.63, -210.78, 0.09028, 95, 160.07, -50.06, 0.17505, 100, 21.32, 80.98, 0.66336, 101, 71.29, 252.51, 0.07131, 5, 94, 101.05, -279.42, 0.02083, 95, 192.49, -118.7, 0.08702, 100, 53.75, 12.34, 0.67854, 101, 103.71, 183.87, 0.20228, 102, -2.19, 234.41, 0.01133, 4, 95, 179.25, -220.77, 0.02149, 100, 40.51, -89.72, 0.50203, 101, 90.47, 81.8, 0.39974, 102, -15.43, 132.35, 0.07674, 3, 100, 7.22, -120.47, 0.2654, 101, 57.19, 51.05, 0.46503, 102, -48.72, 101.59, 0.26957, 3, 100, 69.2, -124.88, 0.0751, 101, 119.16, 46.65, 0.396, 102, 13.26, 97.19, 0.5289, 3, 100, 80.78, -209.4, 0.011, 101, 130.75, -37.88, 0.19623, 102, 24.84, 12.66, 0.79277, 2, 101, 133.24, -90.59, 0.05362, 102, 27.33, -40.05, 0.94638, 2, 101, 112.51, -108.42, 0.0095, 102, 6.6, -57.88, 0.9905, 1, 102, -21.05, -59.18, 1, 1, 102, -45.36, -43.16, 1, 1, 102, -38.41, -4.76, 1, 1, 102, -26.13, -2.82, 1, 1, 102, -15.52, -16.23, 1, 1, 102, -26.8, -27.04, 1, 2, 101, 89.15, -78.92, 0.01092, 102, -16.76, -28.38, 0.98908, 2, 101, 96.05, -63.34, 0.05362, 102, -9.85, -12.8, 0.94638, 2, 101, 78.28, -42.44, 0.21006, 102, -27.63, 8.1, 0.78994, 3, 100, -31.78, -236.26, 0.02585, 101, 18.19, -64.73, 0.45706, 102, -87.71, -14.19, 0.51709, 3, 100, -89.04, -200.75, 0.1173, 101, -39.07, -29.23, 0.64244, 102, -144.97, 21.32, 0.24026, 4, 95, 12.38, -218.23, 0.01033, 100, -126.36, -87.18, 0.26607, 101, -76.39, 84.34, 0.64232, 102, -182.3, 134.88, 0.08128, 4, 95, 96.46, -218.43, 0.05778, 100, -42.28, -87.38, 0.46573, 101, 7.68, 84.15, 0.45835, 102, -98.22, 134.69, 0.01813, 3, 95, 92.82, -156.07, 0.21084, 100, -45.92, -25.02, 0.57801, 101, 4.04, 146.5, 0.21116, 4, 94, -47.9, -283.42, 0.05333, 95, 43.54, -122.7, 0.43248, 100, -95.21, 8.35, 0.45844, 101, -45.24, 179.87, 0.05575, 4, 94, -127.44, -279.31, 0.14444, 95, -36, -118.59, 0.58801, 100, -174.75, 12.46, 0.25771, 101, -124.78, 183.98, 0.00983, 3, 94, -188.94, -211.4, 0.37521, 95, -97.51, -50.68, 0.5157, 100, -236.25, 80.37, 0.10909, 3, 94, -192.9, -127.65, 0.675, 95, -101.46, 33.07, 0.3019, 100, -240.21, 164.12, 0.0231, 3, 94, -73.84, -122.89, 0.68563, 95, 17.6, 37.83, 0.27161, 100, -121.15, 168.88, 0.04276, 3, 94, -14.27, -81.94, 0.9225, 95, 77.17, 78.78, 0.07246, 100, -61.57, 209.82, 0.00504, 4, 94, -15.64, -230.75, 0.1395, 95, 75.8, -70.03, 0.35917, 100, -62.94, 61.02, 0.45911, 101, -12.98, 232.54, 0.04222, 4, 94, -77.3, -190.28, 0.4245, 95, 14.14, -29.56, 0.39109, 100, -124.61, 101.49, 0.18063, 101, -74.64, 273.01, 0.00378, 5, 94, 45.33, -291.09, 0.026, 95, 136.77, -130.37, 0.15577, 100, -1.98, 0.68, 0.61537, 101, 47.99, 172.2, 0.19742, 102, -57.92, 222.74, 0.00544, 4, 95, 126.26, -231.61, 0.039, 100, -12.48, -100.56, 0.47307, 101, 37.49, 70.96, 0.4335, 102, -68.42, 121.51, 0.05443, 4, 95, 94.88, -302.57, 0.00496, 100, -43.87, -171.52, 0.19056, 101, 6.1, 0, 0.56739, 102, -99.8, 50.55, 0.23709, 3, 100, 18.43, -166.49, 0.04652, 101, 68.39, 5.03, 0.43136, 102, -37.51, 55.57, 0.52212, 3, 100, 57.36, -211.2, 0.00528, 101, 107.33, -39.68, 0.19318, 102, 1.42, 10.86, 0.80154, 2, 101, 103.51, -91.58, 0.03846, 102, -2.39, -41.04, 0.96154, 2, 101, 71.95, -88.32, 0.00507, 102, -33.96, -37.78, 0.99493], "hull": 34, "edges": [68, 70, 4, 70, 70, 8, 4, 6, 8, 6, 70, 0, 4, 2, 0, 2, 64, 66, 64, 62, 62, 60, 60, 72, 72, 14, 10, 8, 68, 74, 74, 72, 74, 64, 14, 12, 12, 10, 74, 12, 66, 0, 66, 68, 60, 58, 58, 76, 76, 18, 18, 16, 16, 14, 72, 76, 76, 78, 78, 56, 56, 58, 78, 20, 20, 18, 78, 80, 80, 52, 52, 54, 54, 56, 80, 82, 82, 84, 84, 26, 24, 26, 82, 24, 24, 22, 22, 20, 52, 50, 50, 48, 48, 84, 82, 50, 84, 86, 86, 46, 46, 48, 26, 28, 28, 30, 86, 28, 30, 32, 34, 32, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 44, 86, 86, 88, 88, 38], "width": 531, "height": 303}}, "wu8": {"wu8": {"type": "mesh", "uvs": [0.49425, 0.29471, 0.48309, 0.62695, 0.50284, 0.76593, 0.63328, 0.64917, 0.77495, 0.76427, 0.89146, 0.74255, 1, 0.82724, 1, 0.91193, 1, 1, 0.88202, 0.95102, 0.77924, 1, 0.62126, 1, 0.43759, 1, 0.30966, 0.84628, 0.34486, 0.66821, 0.2693, 0.5314, 0.20405, 0.64215, 0.10188, 0.74639, 0, 0.62695, 0, 0.349, 0, 0.07321, 0.05036, 0.1275, 0.12678, 0, 0.29592, 0, 0.08471, 0.42717, 0.17057, 0.25345, 0.29163, 0.29905, 0.41355, 0.49449, 0.4041, 0.79416, 0.46678, 0.89839, 0.63414, 0.81204, 0.78096, 0.92062, 0.88829, 0.85547], "triangles": [24, 25, 16, 21, 22, 25, 18, 24, 17, 18, 19, 24, 19, 21, 24, 19, 20, 21, 17, 24, 16, 24, 21, 25, 16, 25, 15, 15, 25, 26, 26, 25, 23, 25, 22, 23, 28, 27, 1, 28, 14, 27, 14, 15, 27, 1, 27, 0, 27, 26, 0, 15, 26, 27, 26, 23, 0, 13, 14, 28, 28, 1, 2, 13, 28, 12, 12, 29, 11, 11, 29, 30, 12, 28, 29, 29, 2, 30, 29, 28, 2, 2, 3, 30, 9, 7, 8, 10, 31, 9, 10, 11, 31, 11, 30, 31, 9, 32, 7, 9, 31, 32, 30, 4, 31, 31, 4, 32, 32, 6, 7, 32, 5, 6, 32, 4, 5, 30, 3, 4], "vertices": [5, 113, 179.1, 193.19, 0.00788, 112, 165.69, -21.04, 0.21179, 111, 45.29, -59.16, 0.56067, 110, -20.09, -227.33, 0.20404, 103, 169.3, -385.83, 0.01563, 4, 113, 88.23, 202.66, 0.05854, 112, 74.82, -11.57, 0.44019, 111, -45.59, -49.69, 0.44349, 110, -110.96, -217.86, 0.05778, 4, 113, 49.89, 189.7, 0.22, 112, 36.49, -24.53, 0.55841, 111, -83.92, -62.65, 0.21384, 110, -149.29, -230.82, 0.00775, 3, 113, 80.16, 98.71, 0.4998, 112, 66.75, -115.52, 0.44174, 111, -53.66, -153.64, 0.05847, 3, 113, 46.76, 1.16, 0.77875, 112, 33.35, -213.08, 0.21337, 111, -87.06, -251.2, 0.00788, 2, 113, 51.17, -79.69, 0.94229, 112, 37.76, -293.92, 0.05771, 2, 113, 26.54, -154.45, 0.98967, 112, 13.13, -368.68, 0.01033, 2, 113, 3.33, -154, 0.99506, 112, -10.07, -368.24, 0.00494, 2, 113, -20.79, -153.54, 0.98863, 112, -34.2, -367.78, 0.01137, 2, 113, -5.82, -72.05, 0.93995, 112, -19.22, -286.29, 0.06005, 3, 113, -17.88, -0.59, 0.77407, 112, -31.28, -214.82, 0.21805, 111, -151.69, -252.94, 0.00788, 3, 113, -15.79, 108.87, 0.49434, 112, -29.2, -105.36, 0.4472, 111, -149.61, -143.48, 0.05847, 4, 113, -13.37, 236.14, 0.21532, 112, -26.78, 21.9, 0.56309, 111, -147.18, -16.22, 0.21384, 110, -212.56, -184.39, 0.00775, 4, 113, 30.43, 323.97, 0.0562, 112, 17.03, 109.74, 0.44253, 111, -103.38, 71.62, 0.44349, 110, -168.75, -96.55, 0.05778, 5, 113, 78.75, 298.65, 0.0071, 112, 65.34, 84.42, 0.21257, 111, -55.06, 46.3, 0.56067, 110, -120.44, -121.87, 0.20404, 103, 68.95, -280.37, 0.01563, 4, 112, 103.82, 136.06, 0.05778, 111, -16.59, 97.94, 0.44349, 110, -81.96, -70.23, 0.3981, 103, 107.42, -228.74, 0.10063, 4, 112, 74.34, 181.85, 0.00775, 111, -46.07, 143.73, 0.21384, 110, -111.44, -24.44, 0.43779, 103, 77.95, -182.95, 0.34062, 3, 111, -73.27, 215.06, 0.05847, 110, -138.65, 46.89, 0.28737, 103, 50.74, -111.61, 0.65417, 3, 111, -39.21, 285.03, 0.0105, 110, -104.58, 116.86, 0.13394, 103, 84.8, -41.65, 0.85556, 3, 111, 36.94, 283.58, 0.00504, 110, -28.44, 115.41, 0.12413, 103, 160.95, -43.1, 0.87083, 3, 111, 112.49, 282.14, 0.0105, 110, 47.11, 113.97, 0.13394, 103, 236.5, -44.53, 0.85556, 3, 111, 96.95, 247.53, 0.05847, 110, 31.58, 79.36, 0.28737, 103, 220.96, -79.15, 0.65417, 4, 112, 251.28, 232.03, 0.00775, 111, 130.87, 193.92, 0.21384, 110, 65.5, 25.75, 0.43779, 103, 254.88, -132.76, 0.34062, 4, 112, 249.04, 114.84, 0.05778, 111, 128.64, 76.72, 0.44349, 110, 63.26, -91.45, 0.3981, 103, 252.65, -249.95, 0.10063, 3, 111, 14.4, 225.3, 0.05084, 110, -50.97, 57.12, 0.27916, 103, 138.42, -101.38, 0.67, 4, 112, 181.27, 203.02, 0.00397, 111, 60.86, 164.9, 0.20747, 110, -4.51, -3.27, 0.46056, 103, 184.87, -161.78, 0.328, 4, 112, 167.18, 119.38, 0.05016, 111, 46.77, 81.26, 0.45122, 110, -18.6, -86.91, 0.40562, 103, 170.78, -245.42, 0.093, 5, 113, 125.43, 250.16, 0.00404, 112, 112.03, 35.92, 0.20552, 111, -8.38, -2.2, 0.58088, 110, -73.75, -170.37, 0.20156, 103, 115.63, -328.87, 0.008, 4, 113, 43.46, 258.26, 0.04937, 112, 30.06, 44.03, 0.44925, 111, -90.35, 5.91, 0.45122, 110, -155.72, -162.26, 0.05016, 4, 113, 14.08, 215.38, 0.20861, 112, 0.67, 1.15, 0.57995, 111, -119.73, -36.97, 0.20747, 110, -185.11, -205.14, 0.00397, 3, 113, 35.53, 98.97, 0.49606, 112, 22.12, -115.27, 0.4531, 111, -98.28, -153.39, 0.05084, 3, 113, 3.85, -2.19, 0.78761, 112, -9.56, -216.43, 0.20836, 111, -129.97, -254.55, 0.00403, 2, 113, 20.28, -76.89, 0.94837, 112, 6.87, -291.13, 0.05163], "hull": 24, "edges": [38, 40, 36, 38, 36, 34, 34, 48, 48, 42, 42, 40, 38, 48, 48, 50, 50, 44, 44, 42, 50, 32, 32, 34, 50, 52, 52, 54, 54, 28, 28, 30, 30, 32, 30, 52, 44, 46, 52, 46, 46, 0, 0, 54, 54, 56, 56, 58, 56, 2, 2, 0, 2, 4, 4, 58, 58, 24, 24, 26, 26, 28, 56, 26, 58, 60, 60, 62, 62, 20, 20, 22, 22, 24, 60, 22, 60, 6, 6, 4, 6, 8, 8, 62, 62, 64, 16, 14, 64, 14, 14, 12, 12, 10, 10, 8, 10, 64, 64, 18, 18, 20, 18, 16], "width": 693, "height": 274}}, "pzi": {"pzi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [134.27, -45.54, -114.22, -107.1, -143.31, 10.35, 105.18, 71.91], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 121}}, "yy": {"yy": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-40.14, -169.83, -4.99, 78.69, 195.02, 50.4, 159.87, -198.13], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 251, "height": 202}}, "body": {"body": {"type": "mesh", "uvs": [0.99445, 0.182, 1, 0.35669, 0.97054, 0.51446, 0.98143, 0.66194, 1, 0.76424, 0.79886, 0.83024, 0.50097, 0.93914, 0.38206, 1, 0.28476, 0.92594, 0.19954, 0.83743, 0.08778, 0.72134, 0, 0.34177, 0, 0.11902, 0.18843, 0.03322, 0.56679, 0, 0.783, 0, 0.98957, 0.02866, 0.75922, 0.69659, 0.40968, 0.80714, 0.36644, 0.45404, 0.64511, 0.34844, 0.91981, 0.35482, 0.72482, 0.5128, 0.39117, 0.65595, 0.84292, 0.1554, 0.5981, 0.1393, 0.25746, 0.1964], "triangles": [25, 14, 15, 24, 15, 16, 25, 15, 24, 24, 16, 0, 26, 13, 14, 26, 14, 25, 26, 11, 12, 26, 12, 13, 20, 25, 24, 21, 24, 0, 20, 24, 21, 21, 0, 1, 19, 26, 25, 19, 25, 20, 19, 10, 11, 19, 11, 26, 22, 20, 21, 2, 21, 1, 22, 21, 2, 23, 19, 20, 23, 20, 22, 17, 22, 2, 17, 2, 3, 23, 22, 17, 10, 19, 23, 18, 23, 17, 9, 10, 23, 9, 23, 18, 8, 9, 18, 5, 17, 3, 5, 3, 4, 6, 18, 17, 6, 17, 5, 7, 8, 18, 7, 18, 6], "vertices": [4, 6, -154.21, -190.08, 0.02752, 13, 97.84, -263.84, 0.19595, 15, -2.95, -268.35, 0.51252, 114, 96.21, -32.94, 0.264, 3, 6, -147.54, -123.97, 0.08, 13, 36.83, -237.52, 0.4784, 15, -66.1, -247.71, 0.4416, 4, 6, -123.71, -66.85, 0.18816, 13, -10.43, -197.56, 0.5319, 15, -116.81, -212.22, 0.11994, 14, -38.83, -116.74, 0.16, 2, 6, -121.29, -10.57, 0.576, 13, -63.35, -178.25, 0.424, 1, 6, -125.3, 29.29, 1, 1, 6, -17.79, 39.03, 1, 1, 6, 142.04, 57.65, 1, 1, 6, 206.79, 71.62, 1, 2, 6, 253.01, 36.47, 0.576, 13, 4.84, 192.78, 0.424, 3, 6, 292.2, -3.21, 0.224, 13, 54.5, 218.16, 0.63322, 15, -90.11, 207.69, 0.14278, 3, 6, 343.6, -55.24, 0.08, 13, 119.63, 251.45, 0.4784, 15, -28.29, 246.78, 0.4416, 2, 13, 269.42, 229.83, 0.192, 15, 122.85, 238.93, 0.808, 2, 13, 345.6, 192.94, 0.08, 15, 202.08, 209.15, 0.92, 2, 13, 332.08, 90.2, 0.08, 15, 198, 105.61, 0.92, 2, 13, 257.37, -93.07, 0.08, 15, 140.33, -83.71, 0.92, 3, 13, 208.19, -194.64, 0.06976, 15, 100.62, -189.36, 0.80224, 114, -28.35, 5.15, 0.128, 3, 13, 151.39, -286.95, 0.04288, 15, 52.49, -286.46, 0.49312, 114, 78.6, 22.66, 0.464, 3, 6, -4.61, -14.2, 0.48384, 13, -24.65, -68.11, 0.35616, 14, -71.26, 9.38, 0.16, 2, 6, 181.99, 1.16, 0.576, 13, 17.06, 114.42, 0.424, 4, 6, 185.06, -134.87, 0.07488, 13, 147.66, 76.26, 0.44778, 15, 15.62, 74.88, 0.41334, 14, 78.84, 176.72, 0.064, 4, 6, 35.34, -153.68, 0.0672, 13, 120.38, -72.16, 0.40186, 15, 2, -75.4, 0.37094, 14, 72.88, 25.94, 0.16, 4, 6, -106.22, -130.68, 0.07488, 13, 55.71, -200.16, 0.44778, 15, -50.71, -208.77, 0.41334, 14, 27.01, -109.94, 0.064, 4, 6, 3.13, -85.9, 0.1559, 13, 46.04, -82.39, 0.44072, 15, -71.1, -92.38, 0.09938, 14, 0.74, 5.27, 0.304, 4, 6, 183.3, -57.08, 0.18816, 13, 72.98, 98.08, 0.5319, 15, -60.75, 89.79, 0.11994, 14, 1.81, 187.73, 0.16, 5, 6, -77.39, -211.44, 0.02965, 13, 141.41, -197.06, 0.21793, 15, 34.34, -197.86, 0.63109, 14, 111.4, -94.72, 0.02534, 114, 17.2, -43.74, 0.096, 4, 6, 48.2, -235.86, 0.02687, 13, 202.61, -84.71, 0.20867, 15, 85.03, -80.39, 0.7005, 14, 156.05, 25.18, 0.06397, 4, 6, 227.29, -239.93, 0.02903, 13, 260.57, 84.79, 0.22261, 15, 127.28, 93.69, 0.72354, 14, 189.4, 201.18, 0.02482], "hull": 17, "edges": [6, 34, 34, 36, 36, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 34, 10, 12, 36, 38, 40, 38, 20, 28, 30, 30, 32, 2, 42, 42, 40, 28, 26, 26, 24, 22, 24, 22, 20, 6, 4, 4, 42, 34, 44, 44, 40, 4, 44, 36, 46, 46, 38, 44, 46, 16, 18, 18, 20, 46, 18, 2, 4, 2, 0, 0, 32, 30, 48, 48, 42, 0, 48, 28, 50, 50, 40, 48, 50, 26, 52, 52, 38, 50, 52, 52, 22], "width": 522, "height": 380}}, "jiao1": {"jiao1": {"type": "mesh", "uvs": [0.74348, 0.11159, 0.86432, 0.22907, 0.8483, 0.26511, 0.83323, 0.29902, 0.79913, 0.36417, 0.73394, 0.40177, 0.7407, 0.61757, 0.74501, 0.76677, 0.76223, 0.8739, 0.80672, 0.98314, 0.70769, 0.96087, 0.61009, 0.96511, 0.50387, 1, 0.40053, 1, 0.38043, 0.90784, 0.36464, 0.80283, 0.26274, 0.65575, 0.19656, 0.4887, 0.07095, 0.45581, 0, 0.40061, 0, 0.30191, 0.07413, 0.13743, 0.22835, 0, 0.50818, 0, 0.1137, 0.2855, 0.44108, 0.16672, 0.67799, 0.23019, 0.09162, 0.2138, 0.23948, 0.10454, 0.49864, 0.06224, 0.70692, 0.15388, 0.26431, 0.21103, 0.1266, 0.38414, 0.29672, 0.29603, 0.45889, 0.26666, 0.65286, 0.29486, 0.31262, 0.38884, 0.46843, 0.34772, 0.65445, 0.35947, 0.49095, 0.50833, 0.5254, 0.70348, 0.35747, 0.5656, 0.64453, 0.55499, 0.65458, 0.72647, 0.42206, 0.7392, 0.57851, 0.84526, 0.67324, 0.84844, 0.48665, 0.86753], "triangles": [30, 29, 0, 27, 21, 28, 1, 2, 30, 20, 21, 27, 28, 22, 29, 25, 28, 29, 25, 29, 30, 31, 28, 25, 31, 27, 28, 26, 25, 30, 2, 26, 30, 24, 27, 31, 3, 26, 2, 20, 27, 24, 19, 20, 24, 21, 22, 28, 30, 0, 1, 29, 22, 23, 29, 23, 0, 34, 25, 26, 31, 25, 34, 35, 34, 26, 33, 31, 34, 24, 31, 33, 35, 26, 3, 37, 34, 35, 33, 34, 37, 4, 38, 35, 37, 35, 38, 3, 4, 35, 32, 24, 33, 36, 33, 37, 32, 33, 36, 32, 19, 24, 5, 38, 4, 18, 19, 32, 17, 32, 36, 18, 32, 17, 39, 37, 38, 36, 37, 39, 41, 17, 36, 42, 39, 38, 42, 38, 5, 39, 41, 36, 42, 5, 6, 16, 17, 41, 40, 39, 42, 43, 40, 42, 41, 39, 40, 44, 16, 41, 6, 43, 42, 40, 44, 41, 43, 6, 7, 15, 16, 44, 45, 40, 43, 46, 45, 43, 44, 40, 45, 47, 15, 44, 7, 46, 43, 45, 47, 44, 46, 7, 8, 14, 15, 47, 10, 46, 8, 11, 45, 46, 11, 46, 10, 47, 45, 11, 10, 8, 9, 12, 13, 14, 47, 12, 14, 11, 12, 47], "vertices": [2, 11, 0.23, 109.96, 0.208, 10, 107.65, -13.5, 0.792, 2, 11, 56.92, 138.13, 0.208, 10, 134.34, -70.9, 0.792, 2, 11, 71.15, 130.25, 0.352, 10, 126.08, -84.92, 0.648, 2, 11, 84.54, 122.83, 0.496, 10, 118.32, -98.12, 0.504, 2, 11, 109.98, 106.97, 0.696, 10, 101.81, -123.13, 0.304, 2, 11, 121.94, 83.65, 0.84, 10, 78.18, -134.48, 0.16, 2, 11, 213.28, 68.4, 0.92, 10, 60.56, -225.4, 0.08, 1, 11, 276.42, 57.75, 1, 1, 11, 322.58, 54.5, 1, 1, 11, 371.26, 59.57, 1, 1, 11, 355.99, 30.52, 1, 1, 11, 351.98, -0.21, 1, 1, 11, 360.37, -36.09, 1, 1, 11, 354.23, -68.27, 1, 1, 11, 314.2, -67.12, 1, 1, 11, 269.01, -63.59, 1, 2, 11, 200.98, -83.5, 0.92, 10, -90.97, -209.14, 0.08, 2, 11, 126.65, -90.67, 0.84, 10, -96.21, -134.65, 0.16, 2, 11, 105.33, -127.14, 0.696, 10, -132.1, -112.38, 0.304, 2, 11, 77.85, -144.79, 0.496, 10, -149.04, -84.46, 0.504, 2, 11, 36.26, -136.85, 0.352, 10, -140.02, -43.08, 0.648, 2, 11, -28.65, -100.55, 0.208, 10, -102.03, 20.85, 0.792, 2, 11, -77.4, -41.47, 0.208, 10, -41.71, 68.05, 0.792, 2, 11, -60.77, 45.66, 0.208, 10, 44.96, 49.16, 0.792, 2, 11, 36.1, -100.13, 0.496, 10, -103.3, -43.88, 0.504, 2, 11, 5.49, 11.36, 0.496, 10, 8.94, -16.19, 0.504, 2, 11, 46.32, 80.03, 0.496, 10, 76.53, -58.79, 0.504, 2, 11, 4.57, -101.24, 0.352, 10, -103.59, -12.33, 0.648, 2, 11, -32.69, -46.41, 0.352, 10, -47.81, 23.48, 0.648, 2, 11, -35.11, 37.68, 0.352, 10, 36.32, 23.71, 0.648, 2, 11, 15.88, 95.17, 0.352, 10, 92.46, -28.76, 0.648, 2, 11, 13.66, -47.25, 0.496, 10, -49.85, -22.83, 0.504, 2, 11, 78.43, -104.05, 0.696, 10, -108.32, -86.1, 0.304, 2, 11, 51.41, -43.99, 0.696, 10, -47.58, -60.65, 0.304, 2, 11, 48.67, 8.87, 0.696, 10, 5.33, -59.29, 0.304, 2, 11, 72.08, 67, 0.696, 10, 62.83, -84.2, 0.304, 2, 11, 91.47, -46.5, 0.84, 10, -51.13, -100.63, 0.16, 2, 11, 83.4, 5.32, 0.84, 10, 0.88, -93.91, 0.16, 2, 11, 99.4, 62.3, 0.84, 10, 57.42, -111.39, 0.16, 2, 11, 152.41, -0.58, 0.92, 10, -6.81, -162.75, 0.08, 1, 11, 236.69, -5.54, 1, 2, 11, 168.61, -46.75, 0.92, 10, -53.39, -177.74, 0.08, 2, 11, 181.2, 43.49, 0.92, 10, 36.49, -192.68, 0.08, 1, 11, 254.06, 32.83, 1, 1, 11, 245.61, -40.6, 1, 1, 11, 299.6, -0.41, 1, 1, 11, 306.57, 28.83, 1, 1, 11, 303.52, -30.8, 1], "hull": 24, "edges": [38, 48, 50, 52, 52, 6, 2, 0, 0, 46, 44, 46, 44, 42, 38, 40, 42, 40, 40, 54, 54, 56, 56, 58, 58, 60, 6, 4, 4, 2, 60, 4, 0, 60, 60, 52, 46, 58, 58, 50, 44, 56, 48, 62, 62, 50, 56, 62, 42, 54, 54, 48, 48, 64, 64, 34, 34, 36, 38, 36, 36, 64, 64, 66, 66, 68, 68, 70, 70, 8, 8, 6, 52, 70, 50, 68, 62, 66, 66, 72, 72, 34, 72, 74, 74, 76, 76, 10, 10, 8, 68, 74, 70, 76, 74, 78, 78, 80, 72, 82, 82, 32, 32, 34, 82, 78, 78, 84, 84, 12, 12, 10, 76, 84, 80, 86, 86, 14, 14, 12, 84, 86, 82, 88, 88, 30, 30, 32, 88, 80, 80, 90, 90, 22, 22, 20, 20, 18, 18, 16, 16, 14, 16, 92, 92, 90, 86, 92, 92, 20, 90, 94, 94, 28, 28, 30, 88, 94, 94, 24, 24, 26, 26, 28, 24, 22], "width": 317, "height": 429}}, "yziyanbai33": {"yziyanbai33": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-565.87, 526.41, -628.41, 540.04, -616.48, 594.76, -553.95, 581.13], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 56}}, "dabi1": {"dabi1": {"type": "mesh", "uvs": [0.73936, 0.14854, 0.92463, 0.39934, 1, 0.65894, 0.964, 0.74914, 0.82854, 0.87188, 0.65695, 1, 0.51171, 1, 0.30837, 0.94848, 1e-05, 0.66191, 0, 0.34562, 0.08046, 0.15245, 0.29247, 0.07602, 0.43747, 0, 0.53094, 0, 0.85052, 0.45214, 0.62589, 0.19034, 0.57393, 0.22522, 0.75431, 0.52363, 0.3687, 0.26283, 0.6212, 0.60672, 0.46702, 0.74257, 0.17207, 0.44114], "triangles": [18, 11, 16, 10, 11, 18, 21, 9, 10, 18, 21, 10, 19, 16, 17, 18, 16, 19, 21, 18, 19, 8, 9, 21, 20, 21, 19, 8, 21, 20, 19, 17, 4, 5, 20, 19, 7, 8, 20, 5, 6, 20, 7, 20, 6, 19, 4, 5, 11, 12, 16, 17, 16, 14, 4, 17, 3, 3, 17, 14, 2, 3, 14, 15, 13, 0, 12, 13, 15, 16, 12, 15, 14, 0, 1, 15, 0, 14, 14, 16, 15, 14, 1, 2], "vertices": [1, 66, 17.43, -1.25, 1, 1, 66, -47.84, -18.08, 1, 1, 66, -106.84, -12.73, 1, 2, 66, -122.77, 1.21, 0.832, 67, -45.86, 110.49, 0.168, 2, 66, -138.15, 37.01, 0.496, 67, -17.54, 137.26, 0.504, 2, 66, -152.1, 80.29, 0.208, 67, 18.3, 165.24, 0.792, 1, 67, 48.51, 165.13, 1, 1, 67, 90.96, 153.62, 1, 1, 67, 155.15, 90.31, 1, 1, 67, 154.87, 20.73, 1, 2, 66, 64.67, 128.19, 0.208, 67, 137.86, -21.82, 0.792, 2, 66, 65.07, 80.71, 0.496, 67, 93.42, -38.52, 0.504, 2, 66, 70.19, 46.37, 0.832, 67, 62.94, -55.15, 0.168, 1, 66, 63.35, 28.03, 1, 2, 66, -53.26, 0.47, 0.832, 67, -22.64, 44.97, 0.168, 2, 66, 17.15, 24.2, 0.832, 67, 23.88, -12.98, 0.168, 2, 66, 13.74, 37.09, 0.496, 67, 34.8, -5.34, 0.504, 2, 66, -60.95, 24.74, 0.496, 67, -2.49, 60.55, 0.504, 2, 66, 20.91, 80.2, 0.208, 67, 77.74, 2.77, 0.792, 2, 66, -68.4, 57.12, 0.208, 67, 25.34, 78.68, 0.792, 1, 67, 57.62, 108.46, 1, 1, 67, 118.99, 41.89, 1], "hull": 14, "edges": [26, 0, 0, 2, 2, 4, 4, 6, 6, 28, 28, 30, 24, 26, 30, 24, 0, 30, 2, 28, 24, 22, 22, 32, 32, 34, 34, 8, 8, 6, 30, 32, 28, 34, 22, 20, 20, 18, 20, 36, 36, 38, 38, 10, 10, 8, 34, 38, 38, 40, 40, 14, 10, 12, 14, 12, 12, 40, 40, 42, 42, 18, 36, 32, 36, 42, 16, 18, 42, 16, 16, 14], "width": 209, "height": 220}}, "dabi2": {"dabi2": {"type": "mesh", "uvs": [0.53064, 0.06908, 0.6442, 0.13128, 0.79181, 0.17798, 0.89292, 0.18103, 0.98795, 0.34116, 0.99999, 0.69038, 1, 0.89579, 0.73519, 1, 0.4339, 1, 0.1961, 0.97622, 0.04029, 0.80861, 0, 0.55225, 0, 0.21403, 0.18321, 0.03006, 0.44194, 0, 0.26945, 0.19731, 0.14378, 0.55968, 0.46019, 0.32038, 0.41368, 0.69095, 0.73721, 0.37318, 0.76148, 0.75596], "triangles": [7, 20, 6, 20, 5, 6, 5, 20, 19, 19, 4, 5, 19, 3, 4, 19, 2, 3, 17, 16, 15, 17, 15, 0, 10, 16, 18, 7, 8, 20, 9, 18, 8, 8, 18, 20, 9, 10, 18, 20, 18, 19, 18, 17, 19, 18, 16, 17, 17, 1, 19, 19, 1, 2, 17, 0, 1, 10, 11, 16, 15, 16, 12, 12, 13, 15, 15, 14, 0, 15, 13, 14, 16, 11, 12], "vertices": [2, 72, 6.82, -75.69, 0.512, 73, 27.73, 74.98, 0.488, 3, 72, -14.39, -89.68, 0.18304, 73, 52.94, 78.07, 0.69696, 74, 72.54, 125.99, 0.12, 2, 73, 80.13, 88.08, 0.592, 74, 83.74, 99.27, 0.408, 2, 73, 94.01, 100.02, 0.264, 74, 96.28, 85.92, 0.736, 2, 73, 132.8, 83.64, 0.264, 74, 81.62, 46.45, 0.736, 2, 73, 191.53, 23.8, 0.264, 74, 24.43, -14.86, 0.736, 2, 73, 225.14, -12.26, 0.264, 74, -10.12, -50.02, 0.736, 2, 73, 207.12, -63.24, 0.592, 74, -61.84, -34.27, 0.408, 3, 72, -196.16, 19.3, 0.18304, 73, 167.23, -100.41, 0.69696, 74, -100.74, 3.95, 0.12, 2, 72, -175.68, 57.58, 0.32, 73, 131.85, -125.58, 0.68, 2, 72, -128.1, 69.83, 0.512, 73, 83.79, -115.38, 0.488, 1, 72, -67.94, 55.01, 1, 1, 72, 8.04, 26.45, 1, 1, 72, 37.71, -20.12, 1, 1, 72, 27.99, -66.5, 1, 2, 72, -5.36, -20.61, 0.512, 73, 14.12, 20.23, 0.488, 2, 72, -78.77, 31.28, 0.512, 73, 56.77, -58.9, 0.488, 3, 72, -45.15, -42.54, 0.18304, 73, 59.51, 22.16, 0.69696, 74, 16.97, 116.96, 0.12, 3, 72, -125.44, -3.37, 0.18304, 73, 113.98, -48.65, 0.69696, 74, -51.36, 59.42, 0.12, 2, 73, 104.83, 47.07, 0.592, 74, 43.86, 72.78, 0.408, 2, 73, 170.68, -17.14, 0.592, 74, -17.39, 4.17, 0.408], "hull": 15, "edges": [0, 28, 28, 26, 26, 24, 22, 24, 0, 30, 30, 32, 32, 20, 20, 22, 26, 30, 24, 32, 0, 2, 2, 34, 34, 36, 36, 16, 16, 18, 32, 36, 20, 18, 30, 34, 34, 38, 38, 40, 40, 36, 40, 10, 38, 8, 8, 6, 6, 4, 4, 2, 4, 38, 14, 16, 40, 14, 10, 12, 14, 12, 8, 10], "width": 181, "height": 240}}, "1": {"1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [727.51, 756, 594.51, 756, 594.51, 1123, 727.51, 1123], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 133, "height": 367}}, "shouz1": {"shouz1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [89.73, 65.93, 88.42, -69.06, -24.58, -67.96, -23.27, 67.03], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 135, "height": 113}}, "3": {"3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-507.49, 406, -691.49, 406, -691.49, 725, -507.49, 725], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 184, "height": 319}}, "4": {"4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-647.49, 456, -774.49, 456, -774.49, 756, -647.49, 756], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 127, "height": 300}}, "5": {"5": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [727.51, 0, -774.49, 0, -774.49, 516, 727.51, 516], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 600, "height": 206}}, "6": {"6": {"type": "mesh", "uvs": [0.97419, 0.04819, 0.95824, 0.25722, 0.95079, 0.54575, 0.9476, 0.75038, 0.97313, 0.84246, 0.92207, 0.95092, 0.86569, 0.88544, 0.84832, 0.91398, 0.84549, 1, 0.13167, 1, 0.12745, 0.8923, 0.11341, 0.82094, 0.10346, 0.92893, 0.03863, 0.93784, 0.02821, 0.80423, 0.04442, 0.54591, 0.02358, 0.1718, 0.00825, 0.06011, 0, 0, 0.0861, 0, 0.89654, 0, 1, 0, 0.09304, 0.19407, 0.09536, 0.55259, 0.08763, 0.043, 0.25858, 0.03387, 0.4373, 0.04206, 0.61388, 0.04001, 0.8894, 0.03182, 0.89015, 0.24699, 0.88377, 0.55803, 0.87101, 0.75652, 0.28936, 0.93783, 0.68055, 0.94466, 0.48467, 0.49471, 0.20664, 0.45808, 0.75001, 0.4825, 0.20156, 0.16747, 0.5418, 0.16258, 0.77667, 0.16503, 0.77159, 0.79265, 0.51006, 0.82928, 0.20283, 0.73648], "triangles": [8, 9, 33, 9, 32, 33, 8, 33, 7, 9, 10, 32, 4, 5, 6, 4, 6, 3, 3, 6, 31, 33, 40, 7, 32, 41, 33, 40, 33, 36, 12, 13, 11, 10, 42, 32, 32, 42, 34, 32, 34, 41, 34, 42, 35, 11, 13, 14, 7, 40, 6, 10, 11, 42, 6, 40, 31, 33, 41, 36, 41, 34, 36, 11, 14, 23, 14, 15, 23, 11, 23, 42, 31, 40, 30, 31, 30, 3, 30, 40, 36, 3, 30, 2, 35, 26, 34, 26, 37, 25, 42, 23, 35, 2, 30, 29, 35, 23, 22, 23, 15, 22, 15, 16, 22, 30, 36, 29, 2, 29, 1, 34, 38, 36, 34, 26, 38, 36, 38, 39, 36, 39, 29, 39, 38, 27, 22, 37, 35, 26, 35, 37, 1, 29, 0, 39, 28, 29, 29, 28, 0, 16, 24, 22, 22, 24, 37, 16, 17, 24, 37, 24, 25, 39, 27, 28, 38, 26, 27, 17, 19, 24, 17, 18, 19, 28, 20, 0, 0, 20, 21, 24, 19, 25, 20, 27, 26, 28, 27, 20, 26, 19, 20, 25, 19, 26], "vertices": [1, 1, 384.06, 1481.18, 1, 1, 1, 370.8, 1390.88, 1, 1, 1, 364.62, 1266.24, 1, 1, 1, 361.96, 1177.84, 1, 1, 1, 383.18, 1138.06, 1, 1, 1, 340.75, 1091.2, 1, 1, 1, 293.9, 1119.49, 1, 1, 1, 279.46, 1107.16, 1, 1, 1, 277.11, 1070, 1, 1, 1, -316.07, 1070, 1, 1, 1, -319.58, 1116.53, 1, 1, 1, -331.24, 1147.36, 1, 1, 1, -339.51, 1100.7, 1, 1, 1, -393.39, 1096.85, 1, 1, 1, -402.04, 1154.57, 1, 1, 1, -388.58, 1266.17, 1, 1, 1, -405.89, 1427.78, 1, 1, 1, -418.63, 1476.03, 1, 1, 1, -425.49, 1502, 1, 1, 1, -353.94, 1502, 1, 1, 1, 319.53, 1502, 1, 1, 1, 405.51, 1502, 1, 1, 1, -348.17, 1418.16, 1, 1, 1, -346.25, 1263.28, 1, 1, 1, -352.67, 1483.42, 1, 1, 1, -210.61, 1487.37, 1, 1, 1, -62.1, 1483.83, 1, 1, 1, 84.65, 1484.72, 1, 1, 1, 313.6, 1488.25, 1, 1, 1, 314.23, 1395.3, 1, 1, 1, 308.92, 1260.93, 1, 1, 1, 298.32, 1175.18, 1, 1, 1, -185.03, 1096.86, 1, 1, 1, 140.05, 1093.91, 1, 2, 1, -22.73, 1288.28, 0.512, 2, 20.91, 18.85, 0.488, 2, 1, -253.77, 1304.11, 0.864, 2, 54.07, 248.05, 0.136, 2, 1, 197.77, 1293.56, 0.864, 2, 9.57, -201.41, 0.136, 2, 1, -257.99, 1429.65, 0.904, 2, 179.58, 242.81, 0.096, 2, 1, 24.75, 1431.76, 0.864, 2, 160.41, -39.29, 0.136, 2, 1, 219.92, 1430.71, 0.904, 2, 144.67, -233.83, 0.096, 2, 1, 215.7, 1159.57, 0.904, 2, -125.38, -209.22, 0.096, 2, 1, -1.63, 1143.75, 0.864, 2, -124.81, 8.69, 0.136, 2, 1, -256.94, 1183.84, 0.904, 2, -65.62, 260.26, 0.096], "hull": 22, "edges": [36, 38, 44, 46, 46, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 38, 48, 48, 44, 36, 34, 34, 32, 48, 34, 48, 50, 50, 52, 52, 54, 54, 56, 56, 0, 0, 42, 38, 40, 40, 42, 40, 56, 56, 58, 58, 60, 60, 62, 62, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 58, 2, 60, 4, 62, 6, 22, 28, 30, 46, 22, 20, 20, 18, 12, 14, 16, 18, 14, 16, 20, 64, 64, 66, 66, 14], "width": 332, "height": 172}}, "7": {"7": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [727.51, 0, -774.49, 0, -774.49, 1502, 727.51, 1502], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 600, "height": 600}}, "qbai": {"qbai": {"type": "mesh", "uvs": [0.47042, 0.06762, 0.56369, 0.21763, 0.65882, 0.38094, 0.82577, 0.63295, 0.90039, 0.72979, 1, 0.83233, 0.87987, 0.85702, 0.75862, 0.94816, 0.70266, 1, 0.60566, 1, 0.45644, 0.84183, 0.3156, 0.67526, 0.17943, 0.48538, 0.12161, 0.39612, 0.07684, 0.31257, 0.03207, 0.19485, 0, 0.08851, 0.06191, 0.02964, 0.17197, 0, 0.36223, 0, 0.22047, 0.06572, 0.12534, 0.18345, 0.31, 0.098, 0.1813, 0.22713, 0.06378, 0.1018, 0.40887, 0.21384, 0.25218, 0.34486, 0.52638, 0.41702, 0.40141, 0.52525, 0.70639, 0.70701, 0.5497, 0.79435, 0.78101, 0.79056, 0.65976, 0.895], "triangles": [16, 17, 24, 15, 24, 21, 24, 18, 20, 14, 15, 21, 20, 18, 19, 21, 24, 20, 15, 16, 24, 24, 17, 18, 32, 29, 31, 9, 30, 32, 22, 20, 19, 22, 19, 0, 25, 22, 0, 25, 0, 1, 23, 20, 22, 23, 22, 25, 26, 23, 25, 13, 14, 23, 26, 13, 23, 27, 25, 1, 26, 25, 27, 12, 13, 26, 28, 26, 27, 12, 26, 28, 11, 12, 28, 28, 27, 29, 30, 11, 28, 29, 30, 28, 10, 11, 30, 32, 30, 29, 10, 30, 9, 23, 21, 20, 14, 21, 23, 27, 1, 2, 31, 3, 4, 31, 29, 3, 29, 2, 3, 27, 2, 29, 6, 4, 5, 7, 31, 6, 7, 8, 32, 6, 31, 4, 7, 32, 31, 8, 9, 32], "vertices": [4, 55, 56.33, 108.82, 0.56138, 54, 293.53, -13.75, 0.28512, 116, -80.4, 17.15, 0.121, 117, -291.5, 39.42, 0.0325, 4, 55, 135.13, 103.24, 0.46859, 54, 316.08, -89.47, 0.13299, 116, -1.59, 11.57, 0.23492, 117, -212.69, 33.84, 0.1635, 5, 56, -101.82, 167.6, 0.02575, 55, 219.2, 94.84, 0.35591, 54, 337.84, -171.1, 0.03842, 116, 82.47, 3.17, 0.23132, 117, -128.63, 25.44, 0.3486, 5, 56, 12.76, 95.72, 0.14887, 55, 354.35, 89.22, 0.29257, 54, 380.21, -299.56, 0.00415, 116, 217.62, -2.46, 0.11832, 117, 6.52, 19.82, 0.4361, 4, 56, 61.13, 70.01, 0.37572, 55, 409.09, 90.87, 0.30228, 116, 272.37, -0.8, 0.03, 117, 61.27, 21.47, 0.292, 2, 56, 120.87, 46.58, 0.72, 55, 472.59, 100.14, 0.28, 2, 56, 75.57, 14.49, 0.72, 55, 449.16, 49.81, 0.28, 2, 56, 41.79, -44.85, 0.72, 55, 449.24, -18.47, 0.28, 2, 56, 27.96, -76.21, 0.72, 55, 452.78, -52.56, 0.28, 4, 56, -12.22, -94.01, 0.46436, 55, 426.72, -87.94, 0.45699, 53, 192.84, 237.73, 0.024, 52, 185.8, 295.09, 0.05466, 5, 56, -102.54, -57.05, 0.09423, 55, 329.95, -100.62, 0.59862, 53, 95.39, 242.86, 0.024, 52, 93.8, 262.51, 0.05466, 51, 144.51, 278.77, 0.22849, 3, 55, 232.44, -108.02, 0.77662, 52, -0.02, 234.94, 0.06127, 51, 58.54, 232.14, 0.16211, 2, 55, 127.82, -107.57, 0.864, 54, 116.22, -156.92, 0.136, 2, 55, 80.31, -105.1, 0.704, 54, 101.78, -111.58, 0.296, 2, 55, 38.34, -99.38, 0.544, 54, 92.35, -70.3, 0.456, 2, 55, -15.86, -84.64, 0.208, 54, 87.04, -14.37, 0.792, 2, 55, -62.58, -68.27, 0.08, 54, 85.9, 35.12, 0.92, 2, 55, -67.04, -30.15, 0.08, 54, 120, 52.72, 0.92, 2, 55, -48.09, 17.81, 0.208, 54, 171.56, 51.89, 0.792, 2, 55, 3.03, 87.21, 0.544, 54, 254.52, 28.51, 0.456, 2, 55, -11.51, 18.16, 0.544, 54, 184.77, 17.78, 0.456, 2, 55, 5.11, -47.61, 0.544, 54, 129.08, -20.95, 0.456, 4, 55, 24.11, 42.29, 0.62448, 54, 219.91, -7.05, 0.28808, 116, -112.61, -49.38, 0.07464, 117, -323.71, -27.11, 0.0128, 2, 55, 35.79, -38.73, 0.704, 54, 148.21, -46.54, 0.296, 2, 55, -40.68, -48.51, 0.208, 54, 112.1, 21.58, 0.792, 5, 56, -235.47, 189.71, 0.00396, 55, 92.17, 47.78, 0.62738, 54, 249.04, -68.81, 0.13658, 116, -44.55, -43.89, 0.14879, 117, -255.65, -21.62, 0.08328, 2, 55, 97.02, -43.95, 0.864, 54, 164.9, -105.67, 0.136, 7, 56, -150.16, 128.62, 0.03896, 55, 196.54, 37.01, 0.53178, 54, 275.74, -170.28, 0.03696, 52, -65.37, 369.3, 0.00245, 51, -33.51, 349.83, 0.00648, 116, 59.82, -54.66, 0.1576, 117, -151.28, -32.39, 0.22576, 8, 56, -182.41, 61.64, 0.04273, 55, 201.74, -37.14, 0.73893, 54, 208.18, -201.28, 0.03677, 53, -19.15, 328.59, 0.00096, 52, -44.82, 297.87, 0.01689, 51, 1.55, 284.29, 0.04805, 116, 65.02, -128.81, 0.04751, 117, -146.08, -106.54, 0.06816, 8, 56, -23.33, 43.68, 0.15054, 55, 348.81, 26.13, 0.48569, 54, 319.22, -316.61, 0.00413, 53, 136.97, 364.07, 0.00096, 52, 85.81, 390.41, 0.00219, 51, 109.89, 402.15, 0.00914, 116, 212.08, -65.54, 0.07151, 117, 0.98, -43.27, 0.27584, 8, 56, -72.47, -20.62, 0.16759, 55, 338, -54.07, 0.63431, 54, 240.35, -334.76, 0.00544, 53, 111.76, 287.16, 0.00696, 52, 91.97, 309.72, 0.0183, 51, 132.82, 324.54, 0.06132, 116, 201.28, -145.75, 0.01871, 117, -9.82, -123.47, 0.08736, 6, 56, 22.64, 23.38, 0.31239, 55, 398.79, 31.29, 0.4449, 53, 187.05, 360.06, 0.0015, 52, 133.62, 405.88, 0.00342, 116, 262.06, -60.38, 0.0117, 117, 50.96, -38.11, 0.2261, 6, 56, -8.74, -41.36, 0.34208, 55, 403.63, -40.49, 0.55388, 53, 178.76, 288.59, 0.0087, 52, 153.32, 336.68, 0.01981, 51, 187.16, 363.76, 0.01142, 117, 55.8, -109.9, 0.0641], "hull": 20, "edges": [38, 40, 40, 42, 42, 28, 28, 30, 30, 32, 32, 34, 36, 38, 34, 36, 38, 0, 0, 44, 44, 46, 46, 26, 26, 28, 30, 48, 48, 36, 36, 40, 40, 44, 34, 48, 48, 42, 42, 46, 0, 2, 2, 50, 50, 52, 52, 24, 24, 26, 46, 52, 44, 50, 50, 54, 54, 56, 56, 22, 22, 24, 52, 56, 54, 4, 4, 2, 54, 58, 58, 6, 6, 4, 56, 60, 60, 20, 20, 22, 60, 58, 58, 62, 62, 8, 8, 6, 62, 64, 64, 18, 18, 20, 60, 64, 16, 18, 16, 14, 14, 12, 12, 10, 10, 8], "width": 453, "height": 445}}, "dahuap": {"dahuap": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-36.41, -137.09, 14.01, 219.37, 727.9, 118.39, 677.48, -238.06], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 180, "height": 360}}, "biyan": {"biyan": {"x": 108.13, "y": -49.07, "rotation": -90.36, "width": 136, "height": 47}}, "yziyanzhu22": {"yziyanzhu22": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [12.3, -19.22, -18.97, -12.4, -11.73, 20.82, 19.54, 14], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 34}}, "zuiy1yanbai": {"zuiy1yanbai": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-503.99, 87.16, -579.23, 103.56, -565.6, 166.09, -490.36, 149.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 64}}, "zuiy1yzhu": {"zuiy1yzhu": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [12.5, -17.2, -14.85, -11.24, -8.68, 17.1, 18.68, 11.14], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 28, "height": 29}}, "yziyanzhu23": {"yziyanzhu22": {"x": 0.29, "y": 0.8, "rotation": -12.3, "width": 32, "height": 34}}, "2": {"2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [727.51, 383, 356.51, 383, 356.51, 789, 727.51, 789], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 185, "height": 203}}, "qbai2": {"qbai2": {"type": "mesh", "uvs": [0.5096, 0.03181, 0.65645, 0.10883, 0.61078, 0.18365, 0.66376, 0.17602, 0.78653, 0.26533, 0.79045, 0.38843, 0.86444, 0.39766, 0.89407, 0.51164, 0.86058, 0.71903, 0.78586, 0.8995, 0.60421, 0.95807, 0.42257, 1, 0.23624, 0.82362, 0.15771, 0.70239, 0.06697, 0.65378, 0.01444, 0.3685, 0, 0.14514, 0, 0.08132, 0.07568, 0.07116, 0.17227, 0.0582, 0.26336, 0.03401, 0.3547, 0, 0.46035, 0, 0.24415, 0.30478, 0.37255, 0.28351, 0.48448, 0.24169, 0.56864, 0.19548, 0.38514, 0.05932, 0.2947, 0.10223, 0.19018, 0.11966, 0.29545, 0.48619, 0.43099, 0.45722, 0.5567, 0.41619, 0.64903, 0.37153, 0.70697, 0.46688, 0.67162, 0.61412, 0.52037, 0.67688, 0.35242, 0.70343, 0.13635, 0.5586, 0.56428, 0.82351, 0.3968, 0.83617, 0.71114, 0.73802, 0.78199, 0.59238, 0.1113, 0.34163, 0.0879, 0.13336, 0.23603, 0.62542, 0.3245, 0.59696, 0.47668, 0.56952, 0.61769, 0.52123], "triangles": [43, 16, 44, 43, 44, 29, 23, 29, 28, 16, 18, 44, 16, 17, 18, 44, 19, 29, 44, 18, 19, 24, 28, 27, 25, 27, 0, 28, 21, 27, 28, 20, 21, 27, 22, 0, 27, 21, 22, 29, 20, 28, 29, 19, 20, 24, 27, 25, 32, 25, 26, 2, 0, 1, 31, 24, 25, 25, 0, 26, 26, 0, 2, 30, 23, 24, 43, 15, 16, 43, 29, 23, 15, 43, 38, 30, 43, 23, 23, 28, 24, 30, 24, 31, 26, 2, 33, 33, 2, 3, 33, 3, 4, 32, 26, 33, 31, 25, 32, 47, 31, 32, 48, 32, 33, 33, 4, 5, 35, 34, 42, 34, 33, 5, 36, 48, 35, 35, 48, 34, 48, 33, 34, 7, 5, 6, 42, 34, 5, 41, 36, 35, 47, 32, 48, 36, 47, 48, 41, 35, 42, 41, 39, 36, 7, 42, 5, 8, 42, 7, 10, 39, 41, 41, 42, 8, 9, 41, 8, 10, 41, 9, 30, 38, 43, 14, 15, 38, 46, 30, 31, 45, 38, 30, 14, 38, 13, 40, 37, 36, 46, 31, 47, 12, 13, 37, 37, 47, 36, 13, 38, 45, 13, 45, 37, 45, 46, 37, 37, 46, 47, 46, 45, 30, 12, 37, 40, 40, 36, 39, 11, 40, 39, 12, 40, 11, 11, 39, 10], "vertices": [5, 40, 31.26, 128.71, 0.00371, 47, -114.95, 51.2, 0.008, 46, -54.53, 45.15, 0.04, 45, 20.1, 49.07, 0.2034, 6, -147.64, 28.88, 0.74489, 3, 46, -5.11, 83.55, 0.04444, 45, 72.24, 83.7, 0.42963, 6, -201.08, 61.47, 0.52593, 5, 40, 91.57, 145.52, 0.00464, 47, -52.85, 59.14, 0.03083, 46, 7.07, 56.31, 0.15278, 45, 82.36, 55.63, 0.5973, 6, -179.94, 82.51, 0.21444, 4, 47, -43.9, 78.04, 0.12778, 46, 15.02, 75.65, 0.27731, 45, 91.72, 74.33, 0.55046, 6, -200.84, 83.09, 0.04444, 6, 41, 57.12, 202.89, 0.00496, 48, -65.08, 99.15, 0.03958, 47, 5.77, 103.74, 0.3075, 46, 63.29, 103.9, 0.33032, 45, 141.96, 98.92, 0.30764, 6, -244.37, 118.2, 0.01, 6, 42, 41.65, 189.34, 0.0084, 49, -87.33, 79.98, 0.02917, 48, -29.7, 82.03, 0.14097, 47, 39.88, 84.22, 0.49368, 46, 98.37, 86.18, 0.23403, 45, 175.63, 78.65, 0.09375, 5, 49, -71.47, 104.44, 0.10694, 48, -13.45, 106.24, 0.29537, 47, 57.76, 107.25, 0.47269, 46, 115.02, 110.11, 0.10417, 45, 194.01, 101.27, 0.02083, 6, 44, -44.74, 211.69, 0.0085, 43, 14.98, 213.06, 0.00504, 49, -33.82, 98.17, 0.23546, 48, 24.09, 99.38, 0.4192, 47, 94.75, 97.82, 0.30264, 46, 152.45, 102.61, 0.02917, 6, 44, 14.35, 179.17, 0.04113, 43, 73.55, 179.6, 0.01922, 49, 19.05, 56.3, 0.3195, 48, 76.29, 56.66, 0.43558, 47, 143.87, 51.61, 0.17625, 46, 203.91, 59.02, 0.00833, 6, 44, 60.37, 133.84, 0.11692, 43, 118.83, 133.54, 0.09218, 42, 191.62, 125.37, 0.01638, 49, 56.9, 3.96, 0.33184, 48, 113.3, 3.73, 0.33594, 47, 177.15, -3.75, 0.10675, 6, 44, 56.64, 60.32, 0.20729, 43, 113.94, 60.09, 0.23024, 42, 181.67, 52.43, 0.06954, 49, 41.03, -67.93, 0.24146, 48, 96.28, -67.89, 0.19789, 47, 155.24, -74.03, 0.05358, 7, 44, 47.86, -11.59, 0.23942, 43, 104.01, -11.68, 0.36223, 42, 166.82, -18.48, 0.15887, 41, 230.48, -9.77, 0.02083, 49, 20.43, -137.39, 0.12121, 48, 74.58, -137.02, 0.08214, 47, 128.82, -141.5, 0.01529, 8, 44, -27.86, -64.2, 0.18628, 43, 27.46, -63.07, 0.33903, 42, 86.91, -64.47, 0.27274, 41, 153.08, -59.86, 0.08472, 40, 219.35, -71.06, 0.01736, 49, -62.97, -176.69, 0.04726, 48, -9.44, -174.99, 0.04007, 47, 42.39, -173.59, 0.01254, 10, 44, -74.03, -81.85, 0.04733, 43, -18.98, -79.98, 0.14128, 42, 39.41, -78.15, 0.41867, 41, 106.35, -75.99, 0.24595, 40, 171.55, -83.67, 0.08912, 49, -111.44, -186.43, 0.00475, 48, -58.05, -183.95, 0.00964, 47, -6.73, -179.18, 0.02448, 46, 65.53, -179.29, 0.0028, 6, 19.6, 220.76, 0.01597, 8, 44, -99.56, -111.06, 0.01042, 43, -44.98, -108.78, 0.05208, 42, 11.49, -105.09, 0.29031, 41, 79.87, -104.35, 0.31524, 40, 143.04, -109.99, 0.20972, 47, -38.73, -201.12, 0.00344, 46, 34.71, -202.86, 0.0042, 6, 52.57, 200.31, 0.11458, 6, 43, -137.81, -99.41, 0.01042, 42, -80.47, -89.34, 0.1, 41, -12.79, -93.41, 0.21583, 40, 51.45, -92.2, 0.22964, 45, 3.33, -172.13, 0.00536, 6, 59.88, 107.29, 0.43875, 4, 42, -148.48, -67.35, 0.01563, 41, -81.85, -74.98, 0.05937, 40, -16.06, -68.7, 0.08438, 6, 55.24, 35.96, 0.84062, 1, 6, 52.32, 15.82, 1, 1, 6, 22.49, 16.87, 1, 1, 6, -15.57, 18.22, 1, 1, 6, -52.02, 15.71, 1, 1, 6, -89.01, 10.12, 1, 1, 6, -130, 16.07, 1, 7, 42, -64.85, 1.64, 0.07876, 41, -1.92, -1.74, 0.17781, 40, 69.09, -1.59, 0.21074, 47, -96.3, -83.2, 0.0119, 46, -28.91, -88.1, 0.04819, 45, 35.76, -85.71, 0.07059, 6, -32.15, 100.11, 0.402, 7, 42, -51.89, 50.74, 0.04013, 41, 8.47, 47.97, 0.11565, 40, 83.14, 47.21, 0.14112, 47, -75.36, -36.92, 0.03987, 46, -10.41, -40.79, 0.11635, 45, 57.72, -39.91, 0.14688, 6, -82.94, 100.62, 0.4, 7, 42, -47.44, 96.39, 0.0121, 41, 10.53, 93.79, 0.04781, 40, 88.59, 92.75, 0.06541, 47, -63.4, 7.35, 0.0679, 46, -0.78, 4.04, 0.17619, 45, 70.66, 4.09, 0.23859, 6, -128.28, 93.73, 0.392, 7, 41, 7.63, 129.81, 0.0119, 40, 88.38, 128.88, 0.01914, 48, -124.93, 34.28, 0.00667, 47, -58.41, 43.14, 0.06867, 46, 2.35, 40.04, 0.18543, 45, 76.45, 39.76, 0.35353, 6, -163.05, 83.88, 0.35467, 6, 41, -57.31, 76.48, 0.00794, 40, 19.66, 80.52, 0.01542, 47, -133.38, 5.18, 0.008, 46, -70.54, -1.77, 0.04006, 45, 0.65, 3.47, 0.06458, 6, -98.1, 30.56, 0.864, 7, 42, -116.99, 44.64, 0.00403, 41, -56.23, 38.49, 0.02394, 40, 17.92, 42.56, 0.03885, 47, -140.57, -32.13, 0.00397, 46, -75.78, -39.4, 0.02406, 45, -7.36, -33.67, 0.04115, 6, -61.05, 39.01, 0.864, 6, 42, -127.5, 4.65, 0.008, 41, -64.65, -1.99, 0.03994, 40, 6.51, 2.82, 0.06342, 46, -90.81, -77.92, 0.00806, 45, -25.21, -70.97, 0.01658, 6, -19.7, 38.63, 0.864, 8, 43, -67.23, -6.88, 0.01222, 42, -3.69, -1.89, 0.27306, 41, 59.34, -2.08, 0.26607, 40, 130.16, -6.48, 0.17349, 47, -36.57, -96.83, 0.05905, 46, 31.44, -98.6, 0.07971, 45, 95.18, -100.66, 0.04973, 6, -43.75, 160.27, 0.08667, 9, 43, -59.12, 46.44, 0.00403, 42, 8.08, 50.74, 0.16181, 41, 68.35, 51.09, 0.17558, 40, 143.09, 45.88, 0.1115, 48, -76.65, -52.63, 0.00397, 47, -16.23, -46.88, 0.15552, 46, 49.16, -47.67, 0.18042, 45, 116.62, -51.18, 0.11917, 6, -97.65, 158.75, 0.088, 8, 42, 14.81, 101.28, 0.06029, 41, 72.45, 101.91, 0.07962, 40, 150.94, 96.26, 0.04627, 48, -65.01, -2.98, 0.02133, 47, -1.2, 1.84, 0.24305, 46, 61.63, 1.77, 0.27372, 45, 132.73, -2.8, 0.18773, 6, -148.3, 152.88, 0.088, 9, 42, 15.48, 140.17, 0.01478, 41, 71.09, 140.78, 0.0199, 40, 152.47, 135.12, 0.01114, 49, -117.43, 33.11, 0.00667, 48, -60.55, 35.65, 0.03889, 47, 5.92, 40.07, 0.27577, 46, 66.75, 40.33, 0.31548, 45, 140.69, 35.27, 0.24536, 6, -186.16, 143.98, 0.072, 9, 43, -21.85, 148.04, 0.01008, 42, 52.27, 149.53, 0.04581, 41, 107.34, 152.05, 0.00992, 49, -80, 39.44, 0.05056, 48, -23.02, 41.38, 0.17316, 47, 43.75, 43.21, 0.45187, 46, 104.36, 45.42, 0.18763, 45, 178.58, 37.56, 0.06431, 6, -204.27, 177.34, 0.00667, 9, 44, -39.95, 118.69, 0.01414, 43, 18.29, 119.99, 0.04686, 42, 90.38, 118.78, 0.13326, 41, 146.99, 123.32, 0.0119, 49, -44.54, 5.66, 0.06252, 48, 11.9, 7.05, 0.18336, 47, 76.22, 6.55, 0.4353, 46, 138.7, 10.51, 0.09798, 45, 210.23, 0.19, 0.01467, 10, 44, -38.8, 56.12, 0.03881, 43, 18.45, 57.41, 0.11116, 42, 86.22, 56.34, 0.30244, 41, 146.09, 60.75, 0.05235, 40, 221.33, 49.74, 0.00371, 49, -53.79, -56.23, 0.03786, 48, 1.67, -54.69, 0.11795, 47, 61.76, -54.34, 0.28868, 46, 127.42, -51.05, 0.04276, 45, 194.42, -60.36, 0.00429, 9, 44, -50.63, -9.19, 0.04762, 43, 5.57, -7.71, 0.15034, 42, 68.89, -7.74, 0.47308, 41, 132.12, -4.14, 0.14066, 40, 202.58, -13.93, 0.0238, 49, -76.3, -118.67, 0.00988, 48, -21.84, -116.76, 0.03874, 47, 34.03, -114.64, 0.10581, 46, 102.87, -112.72, 0.01008, 9, 44, -120.28, -75.96, 0.00694, 43, -65.14, -73.35, 0.04583, 42, -6.17, -68.36, 0.34156, 41, 60.32, -68.58, 0.30664, 40, 126.19, -72.87, 0.17951, 47, -50.05, -161.96, 0.01497, 46, 21.37, -164.34, 0.01619, 45, 80.25, -165.48, 0.01072, 6, 21.3, 174.17, 0.07764, 8, 44, 11, 58.38, 0.13057, 43, 68.27, 58.88, 0.20903, 42, 136.02, 54.37, 0.16348, 41, 195.93, 61.37, 0.00397, 49, -4.31, -62.26, 0.14143, 48, 51.04, -61.51, 0.1923, 47, 110.54, -64.54, 0.15519, 46, 176.67, -58.7, 0.00403, 7, 44, -5.01, -5.42, 0.17515, 43, 51.25, -4.66, 0.31978, 42, 114.67, -7.84, 0.26192, 41, 177.84, -1.87, 0.04111, 49, -30.68, -122.52, 0.06402, 48, 23.71, -121.34, 0.0835, 47, 79.16, -122.35, 0.05452, 7, 44, 2.41, 121.5, 0.06368, 43, 60.69, 122.13, 0.08467, 42, 132.82, 117.99, 0.06029, 49, -2.3, 1.41, 0.20832, 48, 54.07, 2.12, 0.31667, 47, 117.95, -1.27, 0.24505, 46, 180.78, 4.87, 0.02133, 8, 44, -33.47, 162.03, 0.01659, 43, 25.45, 163.22, 0.0201, 42, 100.5, 161.41, 0.01478, 49, -30.96, 47.33, 0.22257, 48, 26.14, 48.49, 0.38596, 47, 93.29, 46.91, 0.2861, 46, 153.64, 51.7, 0.04722, 45, 228.19, 40.16, 0.00667, 7, 43, -133.88, -60.68, 0.00556, 42, -73.89, -50.98, 0.10328, 41, -8.21, -54.75, 0.20785, 40, 58.88, -53.99, 0.24186, 46, -43.91, -139.33, 0.01142, 45, 17.01, -135.69, 0.02086, 6, 21.07, 104.26, 0.40917, 5, 42, -138.79, -34.07, 0.01333, 41, -73.9, -41.24, 0.046, 40, -5.63, -35.65, 0.07038, 45, -43.56, -106.89, 0.00429, 6, 20.6, 37.19, 0.866, 9, 44, -88.15, -45.16, 0.015, 43, -32.52, -43.07, 0.0845, 42, 28.46, -40.39, 0.51486, 41, 93.44, -38.86, 0.23924, 40, 161.43, -45.68, 0.06911, 48, -65.27, -145.31, 0.00661, 47, -11.26, -140.13, 0.05258, 46, 58.97, -140.53, 0.01142, 6, -14.31, 200.87, 0.00667, 10, 44, -86.32, -9.35, 0.00667, 43, -30.11, -7.3, 0.0552, 42, 33.32, -4.87, 0.4709, 41, 96.45, -3.13, 0.20559, 40, 167.08, -10.28, 0.05647, 48, -56.96, -110.44, 0.01058, 47, -0.57, -105.91, 0.12554, 46, 67.86, -105.8, 0.04819, 45, 130.96, -110.54, 0.01286, 6, -49.94, 196.87, 0.008, 11, 44, -76.62, 50.15, 0.00435, 43, -19.47, 52.05, 0.03343, 42, 48.03, 53.6, 0.30479, 41, 108.1, 56.03, 0.11962, 40, 183.09, 47.85, 0.03514, 49, -92.07, -55.83, 0.00365, 48, -36.61, -53.68, 0.04257, 47, 23.64, -50.69, 0.29254, 46, 89.17, -49.39, 0.11905, 45, 156.39, -55.87, 0.03686, 6, -110.23, 196.78, 0.008, 10, 43, -16.52, 109.35, 0.0121, 42, 54.91, 110.56, 0.13126, 41, 112.01, 113.27, 0.04781, 40, 191.24, 104.65, 0.01114, 49, -80.55, 0.38, 0.01467, 48, -24.19, 2.34, 0.08946, 47, 39.89, 4.34, 0.43596, 46, 102.53, 6.41, 0.19008, 45, 173.86, -1.22, 0.05953, 6, -167.14, 189.47, 0.008], "hull": 23, "edges": [46, 48, 48, 50, 50, 52, 52, 4, 4, 2, 2, 0, 0, 54, 54, 56, 38, 58, 58, 46, 56, 58, 32, 30, 32, 34, 38, 40, 42, 54, 42, 40, 42, 44, 44, 0, 40, 56, 56, 48, 54, 50, 46, 60, 60, 62, 62, 64, 64, 66, 66, 8, 8, 6, 4, 6, 50, 64, 48, 62, 52, 66, 0, 52, 66, 68, 68, 70, 68, 10, 10, 8, 70, 72, 72, 74, 76, 60, 72, 78, 78, 80, 80, 74, 80, 22, 78, 20, 20, 22, 20, 18, 18, 82, 82, 70, 82, 78, 68, 84, 84, 14, 14, 12, 12, 10, 84, 82, 84, 16, 16, 18, 16, 14, 80, 24, 24, 22, 74, 26, 26, 24, 76, 26, 76, 28, 28, 26, 30, 28, 46, 86, 86, 30, 76, 86, 32, 88, 88, 58, 86, 88, 34, 36, 36, 38, 88, 36, 74, 90, 90, 76, 90, 26, 60, 92, 92, 74, 90, 92, 62, 94, 94, 72, 92, 94, 64, 96, 96, 70, 94, 96, 96, 68], "width": 392, "height": 319}}, "tou": {"tou": {"type": "mesh", "uvs": [0.63154, 0.07703, 0.7174, 0.10761, 0.77608, 0.19579, 0.82822, 0.29563, 0.87738, 0.34545, 0.87804, 0.38696, 0.83412, 0.36432, 0.84395, 0.40281, 0.83732, 0.44308, 0.85137, 0.49429, 0.90345, 0.49564, 0.90638, 0.52057, 0.85446, 0.53065, 0.92881, 0.54321, 0.94102, 0.57464, 0.89131, 0.6041, 0.93887, 0.60939, 0.97048, 0.57215, 0.98198, 0.60277, 0.94677, 0.65985, 0.91138, 0.65842, 0.92542, 0.70293, 0.91474, 0.75861, 0.93212, 0.77829, 0.97986, 0.74718, 0.96027, 0.82022, 0.89917, 0.9059, 0.84356, 0.89508, 0.82613, 0.86508, 0.77341, 0.89497, 0.75335, 0.94494, 0.69983, 0.93394, 0.71608, 0.88883, 0.66639, 0.90973, 0.65684, 0.96804, 0.60237, 1, 0.39764, 0.97158, 0.35319, 0.92609, 0.39764, 0.91472, 0.38988, 0.85054, 0.36589, 0.89684, 0.32144, 0.89278, 0.32708, 0.80992, 0.3332, 0.76265, 0.25568, 0.75269, 0.29288, 0.80479, 0.23154, 0.83953, 0.19294, 0.74755, 0.10057, 0.77035, 0.02705, 0.76926, 0, 0.73328, 0, 0.65748, 0.05155, 0.59561, 0.08549, 0.46755, 0.01856, 0.44476, 0.0261, 0.41546, 0.08926, 0.41437, 0.11659, 0.27763, 0.16354, 0.21318, 0.10474, 0.23295, 0.11046, 0.18862, 0.18019, 0.17663, 0.24056, 0.05261, 0.32173, 0.07418, 0.38574, 0.03284, 0.46595, 0.06759, 0.52683, 0.05381, 0.58904, 0.0692, 0.54184, 0.03781, 0.48333, 0.05772, 0.50195, 0, 0.59303, 0, 0.60331, 0.28469, 0.66403, 0.27345, 0.68336, 0.32485, 0.74435, 0.38363, 0.77617, 0.41035, 0.78811, 0.45538, 0.75894, 0.49965, 0.74369, 0.55461, 0.73261, 0.59083, 0.70493, 0.64962, 0.64751, 0.66205, 0.58122, 0.63533, 0.50431, 0.64831, 0.44481, 0.62416, 0.42606, 0.56479, 0.39095, 0.4819, 0.32333, 0.44755, 0.29416, 0.41091, 0.32001, 0.33992, 0.38432, 0.36969, 0.43338, 0.31702, 0.50233, 0.29947, 0.50829, 0.23993, 0.49392, 0.49283, 0.49613, 0.52657, 0.50908, 0.55266, 0.52509, 0.57855, 0.53463, 0.58757, 0.5676, 0.58417, 0.59625, 0.56931, 0.61866, 0.58184, 0.63613, 0.5917, 0.65195, 0.58981, 0.67074, 0.57121, 0.68524, 0.54236, 0.68458, 0.51883, 0.69381, 0.53288, 0.70074, 0.56476, 0.69546, 0.60309, 0.47852, 0.5101, 0.47391, 0.54274, 0.47391, 0.56817, 0.45182, 0.58829, 0.45808, 0.6141, 0.53185, 0.5932, 0.5686, 0.58801, 0.59684, 0.57622, 0.61805, 0.5858, 0.63678, 0.5953, 0.40258, 0.73843, 0.43856, 0.91878, 0.43931, 0.87566, 0.33243, 0.69864, 0.3963, 0.79382, 0.43668, 0.80889, 0.45444, 0.74665, 0.36409, 0.62395, 0.42113, 0.67602, 0.48247, 0.69138, 0.54144, 0.67698, 0.62339, 0.68859, 0.62912, 0.77661, 0.64728, 0.87232, 0.56073, 0.91294, 0.57275, 0.95924, 0.73232, 0.83052, 0.48971, 0.93469, 0.51795, 0.72687, 0.6265, 0.7363, 0.50608, 0.77912, 0.49528, 0.84809, 0.63857, 0.82642, 0.57388, 0.68779, 0.56568, 0.73791, 0.56049, 0.78909, 0.55903, 0.84695, 0.68271, 0.70458, 0.69969, 0.75653, 0.71216, 0.7979, 0.76052, 0.6236, 0.83259, 0.70207, 0.85922, 0.79586, 0.89829, 0.82231, 0.73734, 0.68103, 0.78051, 0.74967, 0.8038, 0.79858, 0.84721, 0.75354, 0.80716, 0.56104, 0.83741, 0.6193, 0.79447, 0.66056, 0.87587, 0.67809, 0.90357, 0.71707, 0.74693, 0.29261, 0.77118, 0.35375, 0.72903, 0.2434, 0.67374, 0.19199, 0.59655, 0.18571, 0.37748, 0.18083, 0.25773, 0.18322, 0.23015, 0.23235, 0.36597, 0.09096, 0.53554, 0.1309, 0.56274, 0.19093, 0.47037, 0.16287, 0.42874, 0.20398, 0.59236, 0.12072, 0.69205, 0.15661, 0.34463, 0.24482, 0.2296, 0.35017, 0.28521, 0.47606, 0.21075, 0.57591, 0.13817, 0.67901, 0.0769, 0.73653, 0.1477, 0.40569, 0.27865, 0.6444, 0.32954, 0.55918, 0.61691, 0.37879, 0.60776, 0.48766], "triangles": [51, 184, 50, 52, 184, 51, 49, 50, 184, 49, 184, 48, 183, 52, 53, 52, 183, 184, 48, 184, 183, 47, 183, 186, 44, 47, 186, 44, 186, 124, 44, 124, 43, 48, 183, 47, 46, 47, 44, 46, 44, 45, 185, 180, 181, 182, 185, 181, 183, 182, 186, 61, 62, 63, 170, 61, 63, 58, 60, 61, 171, 61, 170, 58, 61, 171, 59, 60, 58, 171, 57, 58, 180, 57, 171, 185, 57, 180, 56, 57, 185, 53, 55, 56, 54, 55, 53, 53, 56, 185, 53, 185, 182, 182, 183, 53, 172, 63, 64, 172, 170, 63, 68, 70, 71, 68, 69, 70, 67, 68, 71, 67, 71, 0, 172, 64, 65, 177, 67, 0, 173, 66, 67, 173, 67, 177, 65, 66, 173, 178, 0, 1, 177, 0, 178, 6, 3, 4, 6, 4, 5, 25, 23, 24, 154, 22, 23, 154, 23, 25, 28, 153, 154, 27, 28, 154, 26, 154, 25, 27, 154, 26, 21, 163, 20, 158, 162, 163, 22, 163, 21, 158, 163, 22, 153, 158, 22, 157, 156, 158, 157, 158, 153, 154, 153, 22, 28, 157, 153, 15, 12, 13, 15, 13, 14, 160, 12, 15, 20, 15, 16, 16, 17, 18, 19, 16, 18, 20, 16, 19, 161, 151, 160, 162, 160, 15, 162, 15, 20, 152, 161, 160, 162, 152, 160, 163, 162, 20, 156, 155, 161, 156, 161, 152, 158, 152, 162, 156, 152, 158, 29, 137, 157, 32, 134, 137, 28, 29, 157, 32, 137, 29, 33, 134, 32, 30, 32, 29, 31, 32, 30, 136, 134, 33, 34, 136, 33, 35, 136, 34, 138, 136, 35, 35, 36, 138, 150, 149, 156, 137, 150, 156, 150, 143, 133, 157, 137, 156, 143, 147, 146, 142, 141, 147, 134, 143, 150, 134, 150, 137, 147, 143, 134, 135, 147, 134, 142, 147, 135, 138, 142, 135, 136, 135, 134, 138, 135, 136, 149, 148, 155, 149, 155, 156, 140, 148, 149, 133, 140, 149, 146, 139, 145, 141, 139, 146, 133, 146, 145, 133, 145, 140, 150, 133, 149, 147, 141, 146, 143, 146, 133, 122, 38, 123, 36, 38, 122, 37, 38, 36, 36, 122, 138, 125, 43, 121, 126, 125, 127, 42, 43, 125, 142, 126, 141, 39, 42, 125, 39, 125, 126, 123, 126, 142, 39, 126, 123, 40, 41, 42, 39, 40, 42, 38, 39, 123, 138, 123, 142, 122, 123, 138, 121, 124, 129, 127, 129, 130, 127, 130, 139, 121, 129, 127, 43, 124, 121, 141, 127, 139, 125, 121, 127, 126, 127, 141, 175, 65, 173, 172, 65, 175, 169, 172, 175, 172, 169, 170, 167, 168, 177, 174, 173, 177, 168, 174, 177, 175, 173, 174, 178, 167, 177, 178, 1, 2, 176, 169, 175, 94, 175, 174, 176, 175, 94, 166, 178, 2, 167, 178, 166, 179, 170, 169, 171, 170, 179, 73, 168, 167, 73, 167, 166, 72, 168, 73, 174, 168, 72, 94, 174, 72, 164, 166, 2, 164, 2, 3, 94, 92, 176, 93, 94, 72, 93, 92, 94, 179, 169, 176, 92, 179, 176, 74, 73, 166, 74, 166, 164, 90, 171, 179, 91, 90, 179, 180, 171, 90, 165, 164, 3, 165, 3, 6, 92, 91, 179, 74, 188, 72, 74, 72, 73, 75, 74, 164, 75, 164, 165, 76, 165, 6, 76, 6, 7, 75, 165, 76, 89, 180, 90, 8, 76, 7, 91, 89, 90, 88, 89, 91, 77, 76, 8, 181, 180, 89, 181, 89, 88, 95, 87, 91, 88, 91, 87, 188, 93, 72, 188, 95, 93, 95, 92, 93, 189, 95, 188, 95, 91, 92, 78, 75, 76, 78, 76, 77, 111, 87, 95, 75, 188, 74, 107, 75, 78, 107, 188, 75, 189, 188, 107, 96, 95, 189, 111, 95, 96, 12, 9, 10, 12, 10, 11, 108, 107, 78, 106, 107, 108, 111, 86, 87, 112, 111, 96, 97, 96, 189, 112, 96, 97, 79, 108, 78, 187, 88, 87, 181, 88, 187, 77, 9, 78, 9, 77, 8, 159, 9, 12, 159, 78, 9, 79, 78, 159, 109, 108, 79, 106, 108, 109, 112, 86, 111, 187, 87, 86, 113, 86, 112, 113, 112, 97, 189, 100, 97, 101, 189, 102, 107, 105, 189, 107, 106, 105, 105, 106, 109, 182, 181, 187, 102, 118, 101, 100, 98, 97, 189, 101, 100, 189, 105, 102, 118, 100, 101, 119, 118, 102, 99, 98, 100, 117, 100, 118, 114, 86, 113, 104, 102, 105, 103, 119, 102, 80, 109, 79, 104, 103, 102, 116, 98, 99, 117, 116, 99, 117, 99, 100, 120, 103, 104, 119, 103, 120, 110, 105, 109, 110, 109, 80, 115, 114, 113, 160, 159, 12, 151, 79, 159, 151, 159, 160, 80, 79, 151, 128, 187, 86, 85, 86, 114, 85, 114, 115, 128, 86, 85, 83, 117, 118, 83, 118, 119, 186, 182, 187, 186, 187, 128, 113, 98, 115, 98, 113, 97, 84, 98, 116, 84, 115, 98, 83, 84, 116, 83, 116, 117, 85, 115, 84, 81, 110, 80, 81, 80, 151, 82, 120, 104, 104, 105, 110, 82, 104, 110, 82, 110, 81, 83, 119, 120, 82, 83, 120, 129, 128, 85, 131, 84, 83, 155, 81, 151, 155, 151, 161, 144, 131, 83, 132, 144, 83, 82, 132, 83, 130, 85, 84, 130, 84, 131, 129, 85, 130, 124, 186, 128, 124, 128, 129, 148, 82, 81, 148, 81, 155, 132, 82, 148, 139, 130, 131, 140, 132, 148, 145, 131, 144, 139, 131, 145, 140, 145, 144, 140, 144, 132], "vertices": [2, 19, 47, -32.26, 0.632, 20, -25.93, 11.46, 0.368, 2, 23, -17.36, 90.41, 0.368, 19, 22.04, -73.88, 0.632, 3, 16, 211.14, -153.8, 0.51744, 23, 14.84, 49.46, 0.32256, 19, -25.66, -94.82, 0.16, 2, 16, 164.14, -181.66, 0.544, 23, 43.58, 2.99, 0.456, 2, 16, 140.61, -208.06, 0.152, 23, 70.42, -20.05, 0.848, 2, 16, 121.14, -208.29, 0.152, 23, 71.01, -39.51, 0.848, 2, 16, 131.91, -184.64, 0.544, 23, 47.16, -29.19, 0.456, 2, 16, 113.82, -189.84, 0.712, 23, 52.7, -47.17, 0.288, 2, 16, 94.96, -186.14, 0.904, 23, 49.36, -66.1, 0.096, 3, 16, 70.89, -193.57, 0.86133, 31, -7.51, 77.76, 0.10417, 23, 57.24, -90.02, 0.0345, 3, 16, 70.08, -221.69, 0.92252, 31, 13.36, 96.62, 0.06481, 23, 85.37, -90.3, 0.01267, 3, 16, 58.38, -223.2, 0.84785, 31, 22.54, 89.21, 0.14815, 23, 87.1, -101.97, 0.004, 4, 16, 53.83, -195.14, 0.60637, 28, -49.55, 140.38, 0.00357, 31, 5.42, 66.52, 0.38439, 23, 59.13, -107.05, 0.00567, 3, 16, 47.69, -235.24, 0.39126, 31, 38.64, 89.81, 0.60741, 23, 99.34, -112.44, 0.00133, 2, 16, 32.9, -241.75, 0.24148, 31, 53.56, 83.63, 0.75852, 5, 16, 19.25, -214.82, 0.27676, 28, -12.43, 154.69, 0.00429, 31, 43.55, 55.15, 0.71016, 32, -19.89, 52.07, 0.008, 23, 79.45, -141.25, 0.0008, 3, 16, 16.61, -240.48, 0.06583, 31, 63.91, 70.98, 0.91854, 32, -3.46, 71.96, 0.01563, 2, 16, 33.97, -257.66, 0.01667, 31, 64.32, 95.4, 0.98333, 2, 31, 78.7, 89.23, 0.97917, 32, 7, 92.98, 0.02083, 3, 16, -7.08, -244.6, 0.0125, 31, 83.27, 56.72, 0.89688, 32, 18.54, 62.24, 0.09063, 5, 16, -6.29, -225.49, 0.01, 29, -18.55, 163.63, 0.00456, 31, 68.92, 44.08, 0.64731, 32, 7.28, 46.78, 0.32563, 33, -32.75, 51.06, 0.0125, 5, 29, 3.53, 165.99, 0.00496, 30, 3.02, 171.58, 0.0052, 31, 88.77, 34.11, 0.345, 32, 28.83, 41.38, 0.56484, 33, -12.01, 43.12, 0.08, 5, 29, 27.51, 154.15, 0.0119, 30, 22.9, 153.68, 0.02048, 31, 102.51, 11.17, 0.144, 32, 47.23, 21.98, 0.56612, 33, 3.95, 21.66, 0.2575, 4, 30, 35.56, 157.3, 0.0169, 31, 115.68, 10.91, 0.0225, 32, 60.14, 24.59, 0.32706, 33, 17.07, 22.71, 0.63354, 2, 32, 62.48, 54.12, 0.14861, 33, 22.93, 51.75, 0.85139, 3, 30, 60.16, 160.83, 0.02253, 32, 84.94, 26.18, 0.10663, 33, 41.89, 21.32, 0.87083, 4, 29, 92.57, 129.46, 0.00827, 30, 78.85, 112.32, 0.05061, 32, 99.77, -23.66, 0.08973, 33, 50.65, -29.93, 0.85139, 5, 29, 80.45, 101.51, 0.04068, 30, 59.63, 88.69, 0.11696, 31, 118.55, -61.74, 0.00544, 32, 78.75, -45.7, 0.20338, 33, 27.14, -49.3, 0.63354, 6, 28, 103.02, 100.92, 0.00429, 29, 64.54, 95.74, 0.14755, 30, 42.75, 87.44, 0.2445, 31, 102.04, -57.98, 0.04077, 32, 61.82, -45.63, 0.30739, 33, 10.35, -47.2, 0.2555, 6, 28, 112.45, 70.62, 0.008, 29, 71.34, 64.75, 0.21209, 30, 40.91, 55.76, 0.58595, 31, 90.98, -87.73, 0.01306, 32, 57.5, -77.06, 0.12691, 33, 2.3, -77.89, 0.054, 4, 29, 91.51, 48.62, 0.08878, 30, 55.96, 34.78, 0.84764, 32, 70.85, -99.16, 0.05024, 33, 12.91, -101.43, 0.01333, 3, 29, 79.58, 21.8, 0.05556, 30, 37.22, 12.18, 0.92578, 32, 50.4, -120.21, 0.01867, 4, 29, 61.14, 35.38, 0.13742, 30, 23.14, 30.24, 0.8169, 32, 37.78, -101.1, 0.03568, 33, -20.16, -99.4, 0.01, 6, 35, 65.9, 147.16, 0.00922, 36, 16.48, 146.94, 0.02917, 28, 110.32, 12.45, 0.008, 29, 64.24, 6.98, 0.26859, 30, 18.44, 2.06, 0.6762, 32, 30.88, -128.82, 0.00883, 3, 36, 44.15, 143.95, 0.11111, 29, 89.56, -4.57, 0.12639, 30, 39.69, -15.91, 0.7625, 4, 35, 111.27, 116.67, 0.02202, 36, 61.4, 115.81, 0.27083, 29, 97.08, -36.72, 0.10402, 30, 38.23, -48.89, 0.60312, 4, 35, 108.27, 5.36, 0.11161, 36, 56.82, 4.55, 0.59583, 29, 57.69, -140.87, 0.02173, 30, -27.87, -138.5, 0.27083, 3, 35, 89.26, -20.52, 0.14583, 36, 37.44, -21.06, 0.74306, 30, -58.26, -148.86, 0.11111, 5, 34, 134.86, 29.91, 0.00833, 35, 81.71, 2.88, 0.31483, 36, 30.23, 2.45, 0.63021, 29, 31.79, -134.49, 0.01017, 30, -51.07, -125.35, 0.03646, 6, 34, 109.28, 13.5, 0.05942, 35, 52.14, -4.09, 0.51111, 36, 0.55, -4.1, 0.37773, 29, 1.57, -131.36, 0.02894, 24, 93.59, 157.56, 0.0078, 25, 34.25, 157.13, 0.015, 4, 34, 134.43, 10.84, 0.01111, 35, 74.96, -14.97, 0.50013, 36, 23.22, -15.3, 0.48333, 29, 19.55, -149.14, 0.00542, 5, 34, 142.76, -11.76, 0.01067, 35, 75.3, -39.05, 0.50139, 36, 23.21, -39.38, 0.46528, 24, 133.24, 143.91, 0.006, 25, 73.72, 142.97, 0.01667, 7, 16, -75.34, 90.47, 0.0042, 34, 106.2, -25.27, 0.05693, 35, 36.32, -39.62, 0.5367, 36, -15.77, -39.4, 0.31424, 29, -25.04, -159.73, 0.00407, 24, 102.66, 119.74, 0.02303, 25, 42.83, 119.19, 0.06083, 8, 16, -53.2, 87.03, 0.02957, 34, 84.69, -31.57, 0.18008, 35, 13.94, -38.4, 0.41448, 36, -38.13, -37.85, 0.08583, 29, -45.78, -151.22, 0.0039, 24, 84.15, 107.09, 0.07857, 25, 24.17, 106.79, 0.20089, 26, -51.37, 101.83, 0.00667, 8, 16, -48.26, 128.86, 0.03459, 34, 97.99, -71.53, 0.14216, 35, 13.18, -80.51, 0.19822, 36, -39.49, -79.96, 0.01, 22, 41.15, 263.68, 0.00325, 24, 109.18, 73.21, 0.10436, 25, 48.75, 72.59, 0.46352, 26, -23.23, 70.49, 0.04389, 6, 16, -72.82, 108.93, 0.0056, 34, 111.75, -43.05, 0.05502, 35, 35.64, -58.24, 0.08667, 24, 113.45, 104.56, 0.06234, 25, 53.43, 103.88, 0.76074, 26, -21.97, 102.1, 0.02963, 7, 16, -88.9, 142.15, 0.00836, 34, 140.43, -66.3, 0.0312, 35, 54.94, -89.7, 0.03333, 22, 60.2, 301.96, 0.00542, 24, 147.91, 91.33, 0.07711, 25, 87.71, 90.2, 0.76402, 26, 13.6, 92.22, 0.08056, 7, 16, -45.64, 162.72, 0.0469, 34, 110, -103.3, 0.05494, 35, 13.93, -114.47, 0.04333, 22, 74.28, 256.17, 0.02946, 24, 130.43, 46.72, 0.14266, 25, 69.66, 45.83, 0.47369, 26, 0.46, 46.15, 0.20903, 3, 25, 111.02, 15.96, 0.168, 26, 44.81, 20.94, 0.41267, 27, -10.93, 18.3, 0.41933, 2, 26, 74.11, -5.86, 0.232, 27, 28.77, 17.79, 0.768, 2, 26, 73.78, -28.18, 0.232, 27, 43.37, 0.91, 0.768, 2, 26, 50.12, -54.71, 0.232, 27, 43.37, -34.64, 0.768, 3, 25, 67.93, -58.6, 0.168, 26, 10.04, -57.85, 0.41267, 27, 15.53, -63.65, 0.41933, 6, 16, 86.05, 219.92, 0.01144, 21, 156.66, 129.97, 0.01869, 22, 111.75, 117.57, 0.45938, 24, 73.54, -85.09, 0.12175, 25, 11.07, -85.24, 0.17832, 26, -43.6, -90.49, 0.21042, 5, 21, 192.42, 118.09, 0.0056, 22, 145.86, 101.54, 0.71568, 24, 90.26, -118.87, 0.04671, 25, 27.35, -119.23, 0.15145, 26, -23.74, -122.52, 0.08056, 4, 22, 139.75, 88.57, 0.8298, 24, 77.41, -125.22, 0.02598, 25, 14.42, -125.42, 0.11181, 26, -35.92, -130.07, 0.03241, 5, 21, 153.8, 105.12, 0.023, 22, 105.96, 93.23, 0.73015, 24, 53.86, -100.55, 0.07339, 25, -8.81, -100.44, 0.11652, 26, -61.72, -107.76, 0.05694, 7, 16, 175.02, 202.56, 0.01892, 19, 21.77, 260.21, 0.00173, 21, 136.91, 41.51, 0.09557, 22, 81.66, 32.07, 0.66515, 24, -3.22, -133.31, 0.134, 25, -66.3, -132.46, 0.06587, 26, -115.41, -145.81, 0.01875, 6, 16, 205.08, 177.01, 0.05503, 19, 45.1, 228.39, 0.01382, 21, 110.56, 12.15, 0.21279, 22, 52.03, 6.03, 0.65352, 24, -42.62, -135.25, 0.05332, 25, -105.73, -133.9, 0.01152, 5, 16, 196.01, 208.82, 0.00939, 19, 43.64, 261.44, 0.00616, 21, 142.61, 20.36, 0.12599, 22, 84.82, 10.39, 0.84372, 24, -14.24, -152.24, 0.01475, 4, 16, 216.78, 205.6, 0.00782, 19, 63.1, 253.49, 0.02181, 21, 138.82, -0.32, 0.15556, 22, 78.61, -9.7, 0.81481, 5, 16, 222.16, 167.91, 0.05479, 19, 59.61, 215.58, 0.07112, 21, 101, -4.69, 0.30377, 22, 40.54, -9.55, 0.55964, 24, -61.31, -140.31, 0.01068, 4, 16, 280.12, 134.95, 0.02116, 19, 108.35, 170.09, 0.11541, 21, 66.49, -61.73, 0.34788, 22, -0.48, -62.11, 0.51556, 4, 16, 269.73, 91.18, 0.06633, 19, 88.1, 129.91, 0.21124, 21, 23.01, -50.16, 0.36244, 22, -42.28, -45.48, 0.36, 4, 16, 288.9, 56.49, 0.01333, 19, 98.71, 91.73, 0.47316, 21, -12.18, -68.39, 0.35351, 22, -79.38, -59.41, 0.16, 2, 19, 72.57, 53.54, 0.752, 21, -54.92, -50.66, 0.248, 1, 19, 71.03, 20.07, 1, 2, 19, 56.03, -10.84, 0.632, 20, -2.73, 12.89, 0.368, 2, 19, 76.39, 10.41, 0.288, 20, 25.36, 4.08, 0.712, 2, 19, 74.84, 43.32, 0.144, 20, 54.15, 20.08, 0.856, 2, 19, 98.74, 27.12, 0.144, 20, 50.25, -8.53, 0.856, 2, 19, 87.04, -20.65, 0.288, 20, 2.25, -19.26, 0.712, 1, 16, 170.04, -60.24, 1, 1, 16, 175.11, -93.06, 1, 1, 16, 150.93, -103.35, 1, 1, 16, 123.16, -136.11, 1, 1, 16, 110.52, -153.22, 1, 1, 16, 89.36, -159.53, 1, 1, 16, 68.69, -143.64, 1, 1, 16, 42.97, -135.25, 1, 1, 16, 26.02, -129.16, 1, 1, 16, -1.46, -114.04, 1, 1, 16, -7.09, -82.99, 1, 1, 16, 5.67, -47.28, 1, 1, 16, -0.15, -5.71, 1, 1, 16, 11.37, 26.35, 1, 1, 16, 39.28, 36.3, 1, 1, 16, 78.28, 55.01, 1, 1, 16, 94.62, 91.43, 1, 1, 16, 111.9, 107.07, 1, 1, 16, 145.1, 92.9, 1, 1, 16, 130.92, 58.26, 1, 1, 16, 155.46, 31.61, 1, 1, 16, 163.45, -5.67, 1, 1, 16, 191.36, -9.07, 1, 1, 16, 72.8, -0.56, 1, 1, 16, 56.97, -1.65, 1, 1, 16, 44.69, -8.57, 1, 2, 16, 32.49, -17.14, 0.792, 115, -40.78, -8.13, 0.208, 2, 16, 28.23, -22.26, 0.76801, 115, -35.36, -12.01, 0.23199, 2, 16, 29.71, -40.07, 0.62401, 115, -17.7, -9.26, 0.37599, 2, 16, 36.58, -55.59, 0.62401, 115, -2.72, -1.29, 0.37599, 2, 16, 30.63, -67.65, 0.62401, 115, 9.74, -6.36, 0.37599, 2, 16, 25.94, -77.06, 0.76801, 115, 19.46, -10.36, 0.23199, 2, 16, 26.78, -85.6, 0.792, 115, 27.92, -8.91, 0.208, 1, 16, 35.44, -95.81, 1, 1, 16, 48.91, -103.72, 1, 1, 16, 59.95, -103.44, 1, 1, 16, 53.33, -108.38, 1, 1, 16, 38.36, -112.02, 1, 1, 16, 20.4, -109.06, 1, 1, 16, 64.75, 7.81, 1, 1, 16, 49.46, 10.4, 1, 1, 16, 37.53, 10.47, 1, 1, 16, 28.17, 22.46, 1, 1, 16, 16.05, 19.15, 1, 1, 16, 25.6, -20.74, 1, 1, 16, 27.9, -40.6, 1, 1, 16, 33.34, -55.89, 1, 1, 16, 28.77, -67.31, 1, 1, 16, 24.25, -77.4, 1, 8, 16, -42.07, 49.5, 0.12433, 34, 58.67, -2.31, 0.43637, 35, -0.85, -2.15, 0.26851, 36, -52.4, -1.4, 0.01333, 28, 8.84, -115.81, 0.03296, 29, -47.85, -112.13, 0.01171, 24, 50.36, 126.86, 0.05924, 25, -9.37, 126.99, 0.05356, 5, 34, 127.33, 50.78, 0.01, 35, 81.56, 25.06, 0.30506, 36, 30.39, 24.63, 0.56625, 29, 38.93, -113.49, 0.04056, 30, -38.52, -107.06, 0.07813, 6, 34, 108.8, 42.67, 0.06715, 35, 61.39, 23.58, 0.48813, 36, 10.2, 23.44, 0.26517, 28, 75.5, -106.2, 0.01352, 29, 19.39, -108.26, 0.14354, 30, -55.91, -96.74, 0.0225, 9, 16, -23.17, 87.26, 0.16014, 34, 57.6, -44.53, 0.27756, 35, -15.92, -41.6, 0.20519, 36, -68.03, -40.63, 0.00833, 28, -15.48, -150.33, 0.00347, 22, -3.65, 244.89, 0.00341, 24, 62.41, 86.38, 0.12616, 25, 2.16, 86.36, 0.20463, 26, -71.04, 79.14, 0.01111, 8, 16, -68.03, 53.05, 0.01875, 34, 83.68, 5.5, 0.24423, 35, 25.33, -3.11, 0.50323, 36, -26.24, -2.73, 0.10467, 28, 33.97, -123.19, 0.01248, 29, -23.43, -121.63, 0.03077, 24, 71.72, 142.02, 0.03253, 25, 12.18, 141.88, 0.05333, 8, 16, -75.23, 31.29, 0.008, 34, 80.96, 28.26, 0.24192, 35, 30.34, 19.26, 0.46516, 36, -20.91, 19.56, 0.07267, 28, 44.34, -102.75, 0.06144, 29, -11.36, -102.15, 0.13418, 24, 62.08, 162.82, 0.00864, 25, 2.82, 162.8, 0.008, 8, 16, -46.11, 21.51, 0.09942, 34, 50.44, 24.73, 0.42954, 35, 0.38, 26.09, 0.25153, 36, -50.76, 26.82, 0.008, 28, 16.99, -88.74, 0.13088, 29, -37.4, -85.86, 0.05914, 24, 34.15, 150.02, 0.01483, 25, -25.28, 150.36, 0.00667, 6, 16, 11.75, 69.94, 0.55485, 34, 18.63, -43.68, 0.29128, 35, -52.38, -27.83, 0.05356, 28, -47.43, -128.01, 0.00416, 24, 25.1, 75.12, 0.0506, 25, -35.3, 75.58, 0.04556, 6, 16, -12.86, 39.29, 0.46502, 34, 27.9, -5.48, 0.3817, 35, -30.93, 5.1, 0.07733, 28, -18.53, -101.37, 0.03296, 24, 22.08, 114.31, 0.02965, 25, -37.81, 114.81, 0.01333, 6, 16, -20.28, 6.21, 0.41811, 34, 20.56, 27.61, 0.37485, 35, -26.83, 38.76, 0.06829, 28, -6.27, -69.76, 0.1184, 29, -58.95, -64.96, 0.01171, 24, 4.85, 143.51, 0.00864, 1, 16, -13.73, -25.67, 1, 1, 16, -19.45, -69.89, 1, 7, 16, -60.75, -72.72, 0.09, 34, 23.66, 116.27, 0.03104, 35, 5.6, 121.32, 0.01229, 28, 45.51, 2.26, 0.54211, 29, -1.2, 2.37, 0.29842, 30, -45.8, 15.34, 0.008, 31, -3.77, -100.9, 0.01814, 6, 35, 49.39, 135.25, 0.03662, 28, 91.38, 4.98, 0.08, 29, 44.74, 1.15, 0.53422, 30, -1.91, 1.74, 0.30832, 31, 34.19, -126.79, 0.01306, 32, 10.56, -127.55, 0.02778, 7, 34, 97.2, 109.53, 0.01248, 35, 72.7, 90.49, 0.14781, 36, 22.47, 90.18, 0.0225, 28, 102.95, -44.15, 0.06952, 29, 52.05, -48.78, 0.49866, 30, -8.37, -48.32, 0.245, 32, 0.18, -176.94, 0.00403, 5, 35, 93.72, 98.97, 0.04194, 36, 43.61, 98.35, 0.07813, 28, 125.4, -41.11, 0.01, 29, 74.69, -47.68, 0.29681, 30, 13.71, -53.38, 0.57313, 7, 35, 25.6, 179.16, 0.0041, 28, 79.14, 53.39, 0.07058, 29, 36.68, 50.44, 0.45941, 30, 3.67, 51.36, 0.26496, 31, 54.09, -81, 0.05754, 32, 20.03, -78.52, 0.12742, 33, -35.08, -74.86, 0.016, 6, 34, 122.53, 78.98, 0.0504, 35, 86.42, 53.25, 0.37504, 36, 35.66, 52.75, 0.07667, 28, 107.07, -83.62, 0.0521, 29, 52.78, -88.46, 0.3758, 30, -18.41, -86.71, 0.07, 5, 16, -37.05, -12.84, 0.40403, 34, 27.64, 51.99, 0.25261, 35, -12.04, 59.38, 0.04038, 28, 13.15, -53.42, 0.26336, 29, -38.21, -50.33, 0.03962, 5, 16, -41.84, -71.43, 0.40533, 34, 7.09, 107.06, 0.03104, 28, 26.62, 3.8, 0.45578, 29, -19.89, 5.52, 0.08704, 31, -17.79, -88.14, 0.02081, 6, 16, -61.51, -6.27, 0.088, 34, 52.58, 56.43, 0.28928, 35, 12.95, 55.27, 0.16149, 28, 36.36, -63.56, 0.29984, 29, -15.95, -62.42, 0.15851, 24, 26.37, 180.82, 0.00288, 7, 16, -93.82, -0.24, 0.008, 34, 84.39, 64.69, 0.15456, 35, 45.7, 52.47, 0.32356, 36, -5.07, 52.55, 0.018, 28, 67.41, -74.34, 0.15944, 29, 14.07, -75.82, 0.31844, 30, -52.26, -64.07, 0.018, 8, 16, -84.15, -77.68, 0.008, 34, 42.74, 130.69, 0.01152, 35, 28.39, 128.57, 0.03258, 28, 69.38, 3.68, 0.29277, 29, 22.71, 1.74, 0.53827, 30, -22.96, 8.26, 0.08, 31, 15.98, -114.38, 0.02477, 32, -9.91, -119.39, 0.0121, 1, 16, -18.91, -43.16, 1, 6, 16, -42.39, -38.58, 0.4, 34, 21.54, 77.56, 0.1136, 35, -9.28, 85.52, 0.01229, 28, 22.27, -28.76, 0.40269, 29, -27.01, -26.55, 0.06771, 31, -41.14, -111.25, 0.00371, 6, 16, -66.37, -35.62, 0.088, 34, 44.51, 85.07, 0.12512, 35, 14.88, 84.96, 0.06086, 28, 45.55, -35.26, 0.46488, 29, -4.38, -35.01, 0.25314, 30, -58.98, -19.8, 0.008, 7, 16, -93.5, -34.66, 0.008, 34, 69.48, 95.72, 0.05856, 35, 41.97, 86.7, 0.13782, 28, 72.23, -40.25, 0.25344, 29, 21.78, -42.27, 0.47582, 30, -35.76, -33.86, 0.062, 31, -8.62, -150.88, 0.00435, 6, 16, -27.16, -101.87, 0.42111, 34, -19.13, 128.38, 0.00384, 28, 16.64, 36.09, 0.38666, 29, -27.07, 38.55, 0.09365, 31, -5.95, -56.48, 0.09138, 32, -43.91, -67.66, 0.00336, 7, 16, -51.58, -110.89, 0.10917, 34, -0.86, 146.92, 0.0048, 28, 42.13, 41.37, 0.5048, 29, -1.22, 41.63, 0.30166, 30, -35.2, 53.13, 0.01, 31, 17.45, -67.88, 0.05949, 32, -18.59, -73.69, 0.01008, 7, 16, -71.02, -117.5, 0.02333, 35, 11.38, 166.89, 0.0041, 28, 62.34, 45.01, 0.25968, 29, 19.23, 43.52, 0.47464, 30, -15, 49.42, 0.06848, 31, 35.67, -77.36, 0.11006, 32, 1.26, -78.98, 0.05971, 5, 16, 10.56, -144.13, 0.57911, 28, -14.36, 83.5, 0.08475, 29, -53.9, 88.44, 0.02527, 31, -1.5, -0.01, 0.29951, 32, -51.86, -11.57, 0.01136, 7, 16, -26.49, -182.82, 0.08267, 28, 28.03, 116.23, 0.04992, 29, -8.86, 117.42, 0.12237, 30, -22.05, 128.16, 0.01248, 31, 52.06, -0.02, 0.47085, 32, 0.43, 0.07, 0.25371, 33, -45.14, 5.51, 0.008, 5, 29, 37.3, 120.86, 0.05875, 30, 23.32, 118.99, 0.092, 31, 92.73, -22.12, 0.07906, 32, 44.93, -12.64, 0.47419, 33, -2.48, -12.44, 0.296, 5, 29, 54.39, 138.38, 0.0129, 30, 44.51, 131.23, 0.04738, 31, 116.58, -16.64, 0.008, 32, 67.01, -2.11, 0.27072, 33, 20.71, -4.62, 0.661, 6, 16, -16.3, -131.44, 0.44259, 28, 10.31, 66.95, 0.1696, 29, -30.74, 69.84, 0.08657, 30, -55.99, 88.28, 0.00347, 31, 7.91, -28.19, 0.26506, 32, -36.54, -37.03, 0.03272, 7, 16, -48.64, -154.55, 0.09911, 28, 45.72, 84.98, 0.12851, 29, 6.09, 84.77, 0.28471, 30, -16.49, 92.69, 0.03648, 31, 46.96, -35.57, 0.28796, 32, 3.18, -35.73, 0.15523, 33, -46.69, -30.37, 0.008, 7, 16, -71.66, -166.98, 0.01333, 28, 70.34, 93.85, 0.03482, 29, 31.38, 91.5, 0.29475, 30, 9.67, 92.32, 0.13896, 31, 71.86, -43.6, 0.15668, 32, 29.23, -38.15, 0.29946, 33, -21.12, -35.89, 0.062, 7, 16, -50.68, -190.55, 0.008, 28, 53.11, 120.28, 0.01286, 29, 16.47, 119.31, 0.1255, 30, 2.85, 123.13, 0.04896, 31, 74.38, -12.15, 0.25525, 32, 24.85, -6.91, 0.46942, 33, -21.73, -4.34, 0.08, 6, 16, 39.74, -169.5, 0.63653, 28, -39.44, 112.93, 0.02739, 29, -76.36, 119.91, 0.00304, 31, -3.35, 38.61, 0.32423, 32, -62.06, 25.72, 0.008, 23, 33.76, -121.62, 0.0008, 5, 16, 12.31, -185.67, 0.29067, 28, -9.91, 124.83, 0.02739, 29, -45.92, 129.23, 0.01034, 31, 27.29, 29.97, 0.59761, 32, -30.28, 23.96, 0.074, 5, 16, -6.89, -162.35, 0.31244, 28, 5.61, 98.92, 0.09332, 29, -32.68, 102.09, 0.04851, 31, 23.73, -0.02, 0.4783, 32, -27.23, -6.09, 0.06742, 6, 16, -15.39, -206.26, 0.072, 28, 20.55, 141.06, 0.01286, 29, -14.18, 142.8, 0.03085, 31, 61.32, 24.2, 0.57219, 32, 4.19, 25.73, 0.3041, 33, -38.34, 30.53, 0.008, 6, 16, -33.77, -221.1, 0.008, 29, 7.15, 152.95, 0.03085, 30, 2.97, 158.04, 0.01248, 31, 84.75, 21.19, 0.31506, 32, 27.72, 27.89, 0.56162, 33, -14.72, 29.86, 0.072, 1, 16, 165.83, -137.77, 1, 1, 16, 137.08, -150.69, 1, 1, 16, 188.97, -128.25, 1, 1, 16, 213.28, -98.55, 1, 1, 16, 216.49, -56.89, 1, 2, 16, 219.52, 61.39, 0.704, 21, -5.41, 0.83, 0.296, 5, 16, 218.81, 126.06, 0.29125, 19, 46.64, 175.65, 0.07302, 21, 59.26, -0.2, 0.369, 22, -0.37, -0.16, 0.24423, 24, -87.5, -107.49, 0.0225, 6, 16, 195.86, 141.1, 0.2786, 19, 27.81, 195.6, 0.01545, 21, 74.91, 22.33, 0.32892, 22, 17.84, 20.36, 0.27397, 24, -60.47, -102.76, 0.09794, 25, -123.15, -101.18, 0.00512, 5, 16, 261.71, 67.34, 0.30906, 19, 74.77, 108.59, 0.25069, 21, -0.6, -41.5, 0.30029, 22, -64.7, -34.09, 0.136, 24, -158.96, -94.03, 0.00397, 2, 16, 242.4, -24.11, 0.456, 19, 34.79, 24.1, 0.544, 1, 16, 214.15, -38.61, 1, 1, 16, 227.63, 11.18, 1, 1, 16, 208.49, 33.78, 1, 2, 16, 246.98, -54.82, 0.456, 19, 32.13, -6.84, 0.544, 3, 16, 229.81, -108.54, 0.61363, 23, -30.77, 67.26, 0.184, 19, 2.98, -55.12, 0.20237, 2, 16, 189.62, 79.32, 0.704, 21, 13.32, 30.23, 0.296, 7, 16, 140.61, 141.75, 0.24981, 19, -25.79, 209.03, 0.00208, 21, 77.05, 77.55, 0.19112, 22, 26.49, 74.94, 0.21232, 24, -19.74, -65.42, 0.30222, 25, -81.94, -64.37, 0.03412, 26, -138.33, -79.81, 0.00833, 2, 16, 81.38, 112.09, 0.528, 24, 3.16, -3.26, 0.472, 8, 16, 34.8, 152.6, 0.19437, 34, 32.87, -128.31, 0.03837, 35, -67.12, -112.37, 0.00667, 21, 90.75, 183.02, 0.0231, 22, 52.58, 178.05, 0.13643, 24, 64.84, -0.94, 0.34185, 25, 3.47, -0.98, 0.18454, 26, -60.29, -7.55, 0.07467, 9, 16, -13.3, 192.1, 0.038, 34, 93.2, -143.63, 0.01618, 35, -15.33, -146.9, 0.00624, 21, 131.53, 230.04, 0.00692, 22, 98.65, 219.91, 0.14795, 24, 126.95, 3.18, 0.17585, 25, 65.62, 2.33, 0.28673, 26, 1.15, 2.47, 0.25813, 27, -31.24, -24.54, 0.064, 3, 25, 107.82, -4.15, 0.168, 26, 43.79, 0.59, 0.41267, 27, 1.85, 2.44, 0.41933, 7, 16, 114.85, 186.14, 0.0809, 34, -25.34, -192.67, 0.0071, 21, 122.12, 102.1, 0.09334, 22, 74.15, 93.98, 0.38227, 24, 29.42, -80.17, 0.2627, 25, -32.98, -79.75, 0.12179, 26, -87.99, -89.8, 0.05189, 8, 16, 2.45, 116.13, 0.25331, 34, 46.67, -81.55, 0.16463, 35, -38.54, -72.88, 0.05222, 21, 55.17, 216.35, 0.00403, 22, 21.2, 215.35, 0.03135, 24, 63.49, 47.79, 0.20685, 25, 2.74, 47.76, 0.23572, 26, -66.29, 40.83, 0.05189, 2, 16, 42.24, 88.4, 0.752, 34, -1.13, -73.35, 0.248, 1, 16, 125.86, -67.31, 1, 2, 16, 74.84, -62.04, 0.92, 115, 0.98, 37.33, 0.08], "hull": 72, "edges": [144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 144, 190, 192, 192, 194, 194, 196, 196, 198, 200, 202, 202, 204, 204, 206, 206, 208, 208, 210, 210, 212, 212, 214, 214, 216, 216, 218, 218, 220, 190, 222, 222, 224, 224, 226, 226, 228, 228, 230, 230, 168, 196, 232, 234, 236, 236, 238, 238, 240, 240, 208, 78, 76, 76, 74, 74, 72, 72, 244, 244, 76, 244, 246, 246, 78, 78, 84, 84, 82, 82, 80, 80, 78, 242, 248, 248, 86, 86, 84, 242, 250, 250, 78, 86, 250, 250, 252, 252, 254, 254, 242, 252, 246, 172, 256, 256, 248, 170, 258, 258, 242, 256, 258, 168, 260, 260, 254, 258, 260, 166, 262, 264, 164, 268, 66, 268, 270, 270, 272, 272, 66, 66, 68, 68, 70, 70, 272, 268, 274, 274, 58, 58, 64, 64, 66, 64, 62, 62, 60, 60, 58, 246, 276, 276, 270, 168, 262, 260, 278, 264, 280, 280, 266, 262, 278, 278, 282, 282, 254, 282, 284, 284, 252, 284, 276, 266, 286, 286, 268, 262, 288, 288, 264, 166, 288, 278, 290, 290, 280, 288, 290, 266, 292, 292, 282, 290, 292, 284, 294, 294, 286, 292, 294, 294, 270, 280, 296, 296, 298, 298, 300, 300, 274, 300, 286, 266, 298, 164, 296, 160, 302, 50, 52, 52, 54, 54, 56, 56, 306, 306, 44, 44, 46, 46, 48, 48, 50, 306, 308, 308, 50, 46, 308, 308, 54, 302, 310, 310, 296, 162, 310, 310, 312, 312, 304, 312, 300, 312, 314, 314, 56, 56, 58, 274, 314, 304, 316, 316, 306, 314, 316, 44, 42, 42, 40, 220, 162, 218, 160, 302, 318, 318, 24, 24, 30, 30, 320, 302, 322, 322, 304, 320, 322, 322, 310, 318, 320, 304, 324, 324, 40, 320, 324, 316, 326, 326, 42, 324, 326, 326, 44, 318, 156, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 32, 38, 154, 16, 16, 18, 18, 20, 20, 22, 22, 24, 18, 24, 152, 14, 14, 16, 14, 12, 12, 6, 6, 328, 328, 330, 330, 152, 330, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 332, 332, 328, 328, 148, 332, 146, 332, 334, 334, 336, 336, 144, 334, 146, 2, 4, 2, 0, 0, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 0, 142, 136, 134, 132, 132, 130, 338, 340, 340, 122, 122, 120, 120, 118, 118, 116, 116, 122, 116, 342, 342, 340, 340, 344, 344, 130, 130, 128, 344, 126, 126, 128, 126, 122, 126, 124, 124, 122, 130, 346, 346, 348, 348, 188, 348, 336, 348, 350, 188, 352, 352, 338, 350, 352, 350, 346, 350, 344, 336, 354, 354, 134, 346, 354, 334, 356, 356, 2, 354, 356, 356, 332, 338, 358, 358, 184, 358, 342, 358, 180, 342, 360, 360, 362, 362, 364, 364, 366, 366, 368, 368, 100, 360, 370, 370, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 114, 370, 112, 106, 370, 364, 106, 366, 106, 104, 104, 368, 368, 96, 96, 94, 94, 366, 100, 102, 104, 102, 96, 98, 98, 100, 248, 372, 372, 364, 94, 372, 94, 92, 92, 90, 90, 88, 88, 94, 88, 248, 256, 374, 374, 362, 372, 374, 374, 174, 70, 72, 88, 86, 144, 376, 376, 378, 378, 202, 236, 166, 232, 234, 198, 200], "width": 540, "height": 469}}, "zuiy1yzhu2": {"zuiy1yzhu": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [12.5, -17.2, -14.85, -11.24, -8.68, 17.1, 18.68, 11.14], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 28, "height": 29}}, "hqbai": {"hqbai": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [523, 63, 445.9, -41.66, 382.3, 5.19, 459.4, 109.86], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 130, "height": 79}}, "shoubi1_2": {"shoubi1_2": {"type": "mesh", "uvs": [0.5896, 0.0332, 0.87529, 0.17116, 1, 0.37607, 1, 0.5891, 1, 0.83053, 0.95185, 1, 0.69977, 1, 0.50184, 0.96443, 0.2927, 0.86502, 0.0705, 0.63373, 0, 0.48766, 0.03876, 0.2584, 0.19255, 0.06366, 0.31696, 1e-05, 0.29831, 0.15087, 0.52611, 0.17319, 0.70537, 0.36187, 0.71097, 0.59924, 0.08917, 0.31926, 0.65309, 0.84473, 0.58586, 0.09812, 0.83794, 0.28477, 0.90516, 0.62562, 0.83421, 0.88937, 0.31769, 0.03899], "triangles": [23, 17, 22, 16, 20, 21, 21, 22, 16, 14, 12, 24, 20, 15, 24, 18, 11, 12, 10, 11, 18, 6, 19, 23, 19, 17, 23, 22, 17, 16, 15, 20, 16, 17, 15, 16, 15, 14, 24, 18, 12, 14, 9, 10, 18, 9, 15, 17, 19, 8, 17, 17, 8, 9, 7, 19, 6, 9, 14, 15, 14, 9, 18, 7, 8, 19, 23, 22, 4, 5, 23, 4, 6, 23, 5, 20, 0, 1, 21, 20, 1, 21, 1, 2, 21, 2, 22, 3, 22, 2, 22, 3, 4, 24, 13, 0, 12, 13, 24, 20, 24, 0], "vertices": [2, 67, 102.36, -33.29, 0.744, 68, -36.89, 6.89, 0.256, 2, 67, 37.95, -4.27, 0.744, 68, -29.88, 77.2, 0.256, 2, 67, 9.98, 38.5, 0.744, 68, 1.75, 117.33, 0.256, 2, 67, 10.21, 82.81, 0.744, 68, 43.83, 131.22, 0.256, 2, 67, 10.47, 133.03, 0.744, 68, 91.52, 146.97, 0.256, 2, 67, 21.54, 168.22, 0.744, 68, 128.4, 147.68, 0.256, 2, 67, 78.68, 167.84, 0.488, 68, 146.24, 93.39, 0.512, 1, 68, 153.06, 48.2, 1, 1, 68, 148.08, -3.15, 1, 1, 68, 117.95, -65.83, 1, 2, 67, 236.16, 60.29, 0.488, 68, 94.43, -90.14, 0.512, 2, 67, 227.11, 12.66, 0.488, 68, 46.4, -96.72, 0.512, 2, 67, 192.1, -27.63, 0.488, 68, -2.94, -76.37, 0.512, 2, 67, 163.94, -40.52, 0.744, 68, -24.13, -53.78, 0.256, 1, 68, 6.49, -48.08, 1, 1, 68, -5.09, 2.3, 1, 1, 68, 19.63, 53, 1, 1, 68, 66.17, 69.53, 1, 1, 68, 54.46, -82.13, 1, 1, 68, 118.77, 72.96, 1, 2, 67, 103.26, -19.91, 0.488, 68, -23.91, 10.31, 0.512, 2, 67, 46.53, 19.27, 0.488, 68, -4.84, 76.56, 0.512, 2, 67, 31.78, 90.26, 0.488, 68, 57.76, 113.15, 0.512, 2, 67, 48.16, 145.02, 0.488, 68, 114.88, 115.06, 0.512, 2, 67, 163.78, -32.58, 0.488, 68, -16.66, -51.1, 0.512], "hull": 14, "edges": [28, 30, 30, 32, 32, 34, 28, 36, 36, 18, 18, 16, 16, 14, 34, 38, 38, 14, 36, 22, 22, 20, 20, 18, 22, 24, 24, 26, 26, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 14, 12, 40, 42, 42, 44, 44, 46, 46, 12, 38, 46, 46, 8, 34, 44, 44, 6, 32, 42, 42, 2, 0, 40, 40, 30, 26, 48, 48, 28, 24, 48, 48, 40], "width": 113, "height": 104}}, "jiaoz1": {"jiaoz1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [163.89, 30.08, 85.18, -104.6, -29.65, -37.5, 49.07, 97.19], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 156, "height": 133}}, "jiaoz2": {"jiaoz2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [152.22, -16.76, 34.28, -69.03, 1.06, 5.94, 119, 58.21], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 129, "height": 82}}, "zuiy1": {"zuiy1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-409.97, -307.93, -689.41, -247.02, -575.91, 273.75, -296.47, 212.85], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 143, "height": 266}}, "yao": {"yao": {"type": "mesh", "uvs": [0.62189, 0.06703, 0.75353, 0.1408, 0.72695, 0.44407, 0.74206, 0.53031, 0.81214, 0.40834, 0.91794, 0.31917, 0.99149, 0.31482, 1, 0.46489, 0.90892, 0.53449, 0.82826, 0.68238, 0.75672, 0.90639, 0.59047, 1, 0.37787, 1, 0.21829, 0.84574, 0.11936, 0.72276, 0, 0.44954, 0.10923, 0.26101, 0.25986, 0.10801, 0.48645, 0.05063, 0.52189, 0.38123, 0.62695, 0.41129, 0.32948, 0.49872, 0.19531, 0.59435, 0.6497, 0.63233, 0.55543, 0.62775, 0.37487, 0.73929, 0.28117, 0.76324, 0.37614, 0.8501, 0.59148, 0.88681, 0.70836, 0.77372, 0.91327, 0.43076, 0.81878, 0.52116], "triangles": [30, 31, 5, 8, 9, 30, 30, 5, 6, 30, 6, 7, 8, 30, 7, 30, 9, 31, 5, 31, 4, 9, 10, 29, 9, 29, 31, 20, 0, 1, 19, 18, 0, 22, 16, 17, 21, 17, 18, 31, 3, 4, 2, 20, 1, 28, 23, 29, 10, 11, 28, 10, 28, 29, 28, 24, 23, 21, 18, 19, 24, 19, 20, 21, 22, 17, 15, 16, 22, 14, 15, 22, 25, 21, 19, 25, 19, 24, 26, 22, 21, 26, 21, 25, 13, 22, 26, 14, 22, 13, 27, 25, 24, 26, 25, 27, 27, 24, 28, 12, 27, 28, 26, 27, 12, 13, 26, 12, 11, 12, 28, 19, 0, 20, 23, 20, 2, 23, 2, 3, 24, 20, 23, 29, 23, 3, 29, 3, 31], "vertices": [4, 6, -90.86, -83.97, 0.49257, 8, 49.79, -76.35, 0.12239, 7, 66.39, 33.1, 0.37992, 9, -22.49, 102.06, 0.00512, 3, 6, -140.38, -63.14, 0.52454, 8, 23.3, -123.09, 0.05583, 7, 58.92, -20.1, 0.41963, 4, 6, -122.01, -9.73, 0.67055, 8, -27.6, -98.59, 0.02184, 7, 2.58, -16.01, 0.27247, 9, -8.3, 22.8, 0.03514, 3, 6, -125.65, 6.75, 0.75485, 7, -12.42, -23.74, 0.09542, 9, -8.35, 5.93, 0.14973, 3, 6, -156.25, -11.36, 0.55053, 7, 12.92, -48.69, 0.01563, 9, 25.47, 16.92, 0.43385, 2, 6, -199.95, -21.51, 0.3291, 9, 70.33, 17.28, 0.6709, 2, 6, -228.82, -18.12, 0.15087, 9, 97.76, 7.67, 0.84913, 2, 6, -228.2, 9.54, 0.08919, 9, 91.11, -19.19, 0.91081, 2, 6, -190.77, 16.97, 0.16375, 9, 52.96, -18.27, 0.83625, 2, 6, -155.35, 39.18, 0.34247, 9, 13.55, -32.2, 0.65753, 2, 6, -121.5, 75.69, 0.6271, 9, -27.46, -60.44, 0.3729, 2, 6, -54.05, 83.21, 0.83132, 9, -94.93, -53.04, 0.16868, 2, 6, 29.06, 71.14, 0.94848, 9, -173.39, -23.12, 0.05152, 3, 6, 87.38, 34.15, 0.96981, 8, -46.61, 114.51, 0.02219, 9, -222.23, 25.72, 0.008, 2, 6, 122.82, 6.27, 0.90319, 8, -14.76, 146.44, 0.09681, 3, 6, 162.3, -49.99, 0.80969, 8, 45.73, 179.04, 0.17997, 7, -31.2, 269.15, 0.01033, 3, 6, 114.64, -77.93, 0.71807, 8, 67.89, 128.44, 0.24306, 7, 8, 230.22, 0.03888, 3, 6, 51.74, -97.09, 0.59579, 8, 79.54, 63.72, 0.27785, 7, 42.61, 174.31, 0.12636, 3, 6, -38.34, -94.63, 0.51683, 8, 66.53, -25.45, 0.22551, 7, 63.26, 86.59, 0.25765, 4, 6, -43.51, -32.74, 0.7081, 8, 4.47, -23.32, 0.1318, 7, 4.76, 65.78, 0.14781, 9, -79.89, 62.41, 0.01229, 4, 6, -83.79, -21.34, 0.68735, 8, -11.58, -61.98, 0.06124, 7, 4.03, 23.92, 0.23092, 9, -43.07, 42.48, 0.02048, 3, 6, 34.79, -22.39, 0.76777, 8, 3.36, 55.66, 0.17148, 7, -25.28, 138.83, 0.06076, 3, 6, 89.76, -12.68, 0.84587, 8, 0.17, 111.38, 0.13901, 7, -48.72, 189.48, 0.01513, 4, 6, -86.87, 19.98, 0.80668, 8, -52.98, -60.2, 0.0131, 7, -35.13, 10.37, 0.0714, 9, -49.09, 1.49, 0.10882, 4, 6, -50.14, 13.8, 0.87592, 8, -42.54, -24.44, 0.0391, 7, -38.55, 47.46, 0.0389, 9, -83.58, 15.54, 0.04608, 4, 6, 23.38, 23.76, 0.9224, 8, -43.8, 49.73, 0.05242, 7, -66.97, 115.99, 0.0129, 9, -157.5, 21.88, 0.01229, 3, 6, 60.63, 22.78, 0.95659, 8, -38.46, 86.62, 0.03931, 9, -193.64, 30.97, 0.0041, 3, 6, 25.79, 43.9, 0.9559, 8, -63.52, 54.49, 0.0121, 9, -164.25, 2.75, 0.032, 4, 6, -57.42, 62.77, 0.87136, 8, -92.01, -25.93, 0.00403, 7, -84.02, 27.91, 0.00397, 9, -87.17, -33.83, 0.12064, 3, 6, -106.08, 48.92, 0.71758, 7, -58.19, -15.6, 0.008, 9, -36.66, -30.94, 0.27442, 2, 6, -195.2, -1.57, 0.20303, 9, 61.33, -1.14, 0.79697, 2, 6, -155.88, 9.44, 0.48566, 9, 20.56, -3.3, 0.51434], "hull": 19, "edges": [38, 40, 40, 4, 4, 2, 2, 0, 0, 36, 36, 34, 34, 42, 42, 38, 38, 36, 0, 40, 42, 44, 44, 28, 28, 30, 30, 32, 32, 34, 32, 44, 4, 6, 6, 46, 46, 48, 48, 50, 50, 52, 52, 26, 26, 28, 44, 52, 42, 50, 38, 48, 40, 46, 26, 24, 24, 54, 54, 50, 52, 54, 54, 56, 22, 24, 56, 22, 48, 56, 56, 58, 58, 46, 58, 20, 20, 22, 20, 18, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 10, 60, 60, 16, 18, 60, 60, 12, 18, 62, 62, 8, 58, 62, 62, 10], "width": 395, "height": 183}}, "yziyanzhu33": {"yziyanzhu33": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [10.98, -15.78, -16.38, -9.82, -10.41, 17.54, 16.94, 11.58], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 28, "height": 28}}, "shoubi1": {"shoubi1": {"type": "mesh", "uvs": [0.81139, 0.15451, 0.86506, 0.45475, 0.79827, 0.47765, 0.78395, 0.50183, 0.78515, 0.54126, 0.83878, 0.54252, 0.92839, 0.52846, 1, 0.73227, 0.93761, 0.84893, 0.82158, 0.96901, 0.71748, 0.97604, 0.6595, 0.92544, 0.70437, 0.82363, 0.7215, 0.76179, 0.67934, 0.74492, 0.63322, 0.75335, 0.5513, 0.74186, 0.33469, 0.80743, 0.26005, 0.74654, 0.18999, 0.69139, 0.13631, 0.58579, 0.10292, 0.40895, 0.05521, 0.28555, 0.29164, 1e-05, 0.63129, 0, 0.68496, 0.5349, 0.45, 0.49292, 0.41183, 0.37333, 0.71716, 0.42931, 0.73863, 0.31226, 0.36054, 0.18123, 0.47812, 0.62165, 0.66748, 0.60155, 0.74918, 0.6381, 0.85196, 0.74914, 0.81243, 0.87282], "triangles": [10, 35, 9, 11, 12, 10, 9, 35, 8, 10, 12, 35, 35, 34, 8, 12, 13, 35, 8, 34, 7, 35, 13, 34, 7, 34, 6, 34, 13, 33, 34, 33, 6, 13, 14, 33, 3, 28, 2, 25, 28, 3, 25, 3, 4, 32, 31, 25, 16, 31, 32, 15, 16, 32, 30, 23, 24, 22, 23, 30, 29, 24, 0, 30, 24, 29, 27, 30, 29, 21, 22, 30, 27, 21, 30, 28, 27, 29, 29, 0, 1, 2, 28, 29, 1, 2, 29, 26, 27, 28, 25, 26, 28, 20, 21, 27, 20, 27, 26, 25, 31, 26, 19, 20, 26, 26, 18, 19, 31, 18, 26, 17, 18, 31, 17, 31, 16, 32, 25, 4, 32, 4, 5, 33, 32, 5, 33, 5, 6, 14, 32, 33, 14, 15, 32], "vertices": [1, 68, 4.81, 45.39, 1, 1, 68, 52.98, 71.97, 1, 1, 68, 60.88, 61.06, 1, 1, 68, 65.87, 59.8, 1, 1, 68, 72.54, 62.22, 1, 2, 68, 69.56, 72.08, 0.704, 71, -1.76, 29.47, 0.296, 2, 68, 61.81, 87.65, 0.456, 71, 2.31, 46.38, 0.544, 2, 68, 92.41, 112.11, 0.328, 71, 41.48, 45.45, 0.672, 2, 68, 116.09, 107.24, 0.16, 71, 56.49, 26.51, 0.84, 2, 68, 143.56, 92.78, 0.04, 71, 68.23, -2.23, 0.96, 2, 68, 150.97, 74.18, 0.04, 71, 61.95, -21.24, 0.96, 2, 68, 145.77, 60.77, 0.04, 71, 49.35, -28.17, 0.96, 2, 68, 125.67, 63.26, 0.16, 71, 35.56, -13.34, 0.84, 2, 68, 114.07, 62.93, 0.328, 71, 26.46, -6.14, 0.672, 2, 68, 113.7, 54.29, 0.456, 71, 20.62, -12.52, 0.544, 2, 68, 117.89, 46.35, 0.704, 71, 18.73, -21.3, 0.296, 1, 68, 120.81, 30.75, 1, 1, 68, 144.95, -5.11, 1, 1, 68, 138.98, -22.14, 1, 1, 68, 133.72, -38.01, 1, 1, 68, 118.85, -53.71, 1, 1, 68, 90.59, -69.69, 1, 1, 68, 72.32, -85.29, 1, 1, 68, 9.36, -58.11, 1, 1, 68, -10.89, 3.88, 1, 1, 68, 77.43, 43.58, 1, 1, 68, 84.26, -1.65, 1, 1, 68, 66.07, -15.3, 1, 1, 68, 57.44, 43.55, 1, 1, 68, 36.14, 40.93, 1, 1, 68, 36.26, -35.4, 1, 1, 68, 104.61, 10.68, 1, 2, 68, 89.88, 44.11, 0.704, 71, -4.17, -5, 0.296, 2, 68, 91.26, 61.07, 0.456, 71, 7.78, 7.1, 0.544, 2, 68, 104.13, 86.03, 0.328, 71, 33.69, 17.95, 0.672, 2, 68, 127.65, 85.73, 0.16, 71, 51.51, 2.6, 0.84], "hull": 25, "edges": [8, 50, 50, 52, 52, 38, 38, 40, 40, 54, 54, 56, 56, 6, 6, 8, 6, 4, 4, 2, 2, 0, 0, 58, 58, 56, 4, 58, 58, 60, 60, 42, 42, 40, 42, 44, 44, 46, 46, 48, 48, 0, 38, 36, 36, 34, 34, 32, 32, 62, 62, 36, 62, 52, 52, 54, 54, 60, 60, 46, 62, 64, 64, 66, 66, 12, 12, 10, 10, 8, 10, 64, 64, 30, 30, 28, 28, 66, 30, 32, 56, 50, 12, 14, 14, 68, 68, 26, 26, 28, 66, 68, 68, 70, 70, 24, 24, 26, 70, 16, 16, 14, 70, 20, 20, 22, 22, 24, 20, 18, 18, 16], "width": 192, "height": 180}}, "yanzhu1": {"yanzhu1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-6.2, -5.2, -6.13, 4.8, 7.87, 4.71, 7.8, -5.29], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 10, "height": 14}}, "yanzhu2": {"yanzhu2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-6.76, -4.78, -6.7, 5.22, 6.3, 5.13, 6.24, -4.87], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 10, "height": 13}}, "shou2": {"shou2": {"type": "mesh", "uvs": [1, 0.07572, 0.99999, 0.1654, 0.90982, 0.30596, 0.83272, 0.42025, 0.83284, 0.51494, 0.84545, 0.8053, 0.68635, 0.91878, 0.48677, 1, 0.30514, 0.99998, 0.18841, 0.99997, 0, 0.8276, 0.01112, 0.50221, 0.13749, 0.2828, 0.24079, 0.26212, 0.29438, 0.25139, 0.39651, 0.20622, 0.51937, 0.13113, 0.60144, 0.2008, 0.67048, 0.13009, 0.72444, 0.03335, 0.79483, 0, 0.9406, 0, 0.21125, 0.49363, 0.3455, 0.82779, 0.10037, 0.51327, 0.13345, 0.84035, 0.3524, 0.39813, 0.45637, 0.71519, 0.62334, 0.5333, 0.46109, 0.36976, 0.65961, 0.36313, 0.83788, 0.13888, 0.73827, 0.25906, 0.88003, 0.06593], "triangles": [13, 24, 12, 22, 13, 14, 24, 11, 12, 13, 22, 24, 10, 11, 24, 25, 24, 22, 25, 22, 23, 10, 24, 25, 8, 9, 25, 10, 25, 9, 23, 8, 25, 8, 23, 7, 32, 18, 31, 17, 18, 32, 30, 17, 32, 16, 17, 29, 16, 29, 15, 29, 17, 30, 26, 14, 15, 26, 15, 29, 3, 32, 2, 30, 32, 3, 28, 29, 30, 4, 28, 30, 4, 30, 3, 27, 26, 29, 27, 29, 28, 28, 4, 5, 6, 27, 28, 5, 6, 28, 26, 22, 14, 22, 26, 27, 23, 22, 27, 7, 27, 6, 23, 27, 7, 32, 31, 2, 2, 31, 1, 18, 19, 31, 19, 20, 31, 31, 33, 1, 33, 20, 21, 33, 21, 0, 31, 20, 33, 1, 33, 0], "vertices": [1, 75, 41.34, -48.87, 1, 2, 75, 15.56, -61.48, 0.704, 74, 272.8, -53.15, 0.296, 2, 75, -38.28, -53.81, 0.184, 74, 219.48, -63.81, 0.816, 1, 74, 175.2, -71.57, 1, 2, 74, 153.99, -93.22, 0.888, 73, 275.52, 149.78, 0.112, 2, 74, 91.92, -162.49, 0.832, 73, 341.99, 84.72, 0.168, 2, 74, 28, -150.59, 0.728, 73, 327.29, 21.38, 0.272, 2, 74, -38.48, -121.71, 0.512, 73, 295.51, -43.75, 0.488, 2, 74, -82.39, -78.56, 0.232, 73, 250.46, -85.73, 0.768, 1, 73, 221.5, -112.7, 1, 1, 73, 137.17, -115.89, 1, 1, 73, 68.95, -37.14, 1, 1, 73, 52.42, 43.43, 1, 2, 74, 67.53, 105.16, 0.232, 73, 73.53, 72.15, 0.768, 2, 74, 82.9, 94.88, 0.512, 73, 84.48, 87.04, 0.488, 2, 74, 117.72, 80.92, 0.728, 73, 99.96, 121.22, 0.272, 2, 74, 164.27, 68.87, 0.888, 73, 114.05, 167.2, 0.112, 1, 74, 168.49, 33.47, 1, 2, 75, -23.4, 43.81, 0.184, 74, 201.04, 33.2, 0.816, 2, 75, 12.44, 40.99, 0.704, 74, 235.79, 42.46, 0.296, 1, 75, 32.52, 24.25, 1, 1, 75, 54.24, -20.13, 1, 2, 74, 8.47, 59.33, 0.512, 73, 116.71, 11.12, 0.488, 2, 74, -34.02, -48.84, 0.512, 73, 222.9, -36.09, 0.488, 2, 74, -22.75, 81.19, 0.232, 73, 93.49, -19.1, 0.768, 2, 74, -88.11, -1.32, 0.232, 73, 173.05, -88.03, 0.768, 2, 74, 64.02, 47.59, 0.728, 73, 130.88, 66.1, 0.272, 2, 74, 18.05, -49.48, 0.728, 73, 225.84, 15.9, 0.272, 2, 74, 99.22, -47.63, 0.888, 73, 227.57, 97.07, 0.112, 2, 74, 96.66, 28.25, 0.888, 73, 151.65, 97.86, 0.112, 1, 74, 146.15, -17.41, 1, 2, 75, -0.98, -8.4, 0.704, 74, 239.55, -8.58, 0.296, 2, 75, -50.37, 5.03, 0.184, 74, 188.51, -12.34, 0.816, 1, 75, 26.27, -10.96, 1], "hull": 22, "edges": [28, 44, 44, 46, 46, 14, 24, 22, 22, 20, 20, 18, 28, 26, 26, 24, 26, 48, 48, 50, 14, 16, 16, 18, 50, 16, 20, 50, 50, 46, 22, 48, 48, 44, 44, 52, 52, 30, 30, 28, 52, 54, 54, 12, 12, 14, 46, 54, 54, 56, 56, 8, 8, 10, 10, 12, 56, 58, 58, 32, 32, 30, 58, 52, 32, 34, 34, 60, 60, 6, 6, 8, 34, 36, 36, 38, 38, 62, 62, 2, 2, 4, 4, 6, 36, 64, 64, 4, 62, 64, 64, 60, 38, 40, 40, 42, 2, 0, 42, 0, 40, 66, 66, 0, 42, 66, 66, 62], "width": 339, "height": 320}}, "qbai22": {"qbai22": {"type": "mesh", "uvs": [0.73343, 0.07348, 0.781, 0.25153, 0.85501, 0.47046, 0.93123, 0.60246, 0.96693, 0.68018, 1, 0.75217, 0.9246, 0.87452, 0.78643, 0.85866, 0.66727, 1, 0.5662, 1, 0.46351, 1, 0.32624, 0.90759, 0.30352, 0.7464, 0.25414, 0.59097, 0.16225, 0.40954, 0.1102, 0.32764, 0.03606, 0.234, 0, 0.1772, 0.03811, 0.15528, 0.11524, 0.12091, 0.18431, 0.09362, 0.22645, 0.08082, 0.33146, 0.05295, 0.47064, 0.02066, 0.48725, 0.07717, 0.57216, 0.04611, 0.62187, 0, 0.69588, 0, 0.27716, 0.16934, 0.29935, 0.26813, 0.28684, 0.27226, 0.26506, 0.22462, 0.24206, 0.27989, 0.26264, 0.34927, 0.29048, 0.32635, 0.20421, 0.1755, 0.1181, 0.20111, 0.16994, 0.2923, 0.21194, 0.37161, 0.36054, 0.13872, 0.39447, 0.26182, 0.5211, 0.16642, 0.46555, 0.49852, 0.50588, 0.70144, 0.56066, 0.84782, 0.59743, 0.39781, 0.67248, 0.5993, 0.74556, 0.73458, 0.86421, 0.65015, 0.79315, 0.5268, 0.71583, 0.31753, 0.64838, 0.1588, 0.34221, 0.55246, 0.38604, 0.72807, 0.46782, 0.87149, 0.88533, 0.72864], "triangles": [5, 6, 55, 14, 38, 13, 13, 38, 33, 33, 34, 52, 14, 37, 38, 14, 15, 37, 38, 32, 33, 38, 37, 32, 33, 32, 34, 15, 36, 37, 15, 16, 36, 34, 29, 40, 32, 30, 34, 34, 30, 29, 37, 35, 32, 37, 36, 35, 32, 31, 30, 32, 35, 31, 30, 31, 29, 31, 28, 29, 29, 28, 39, 16, 18, 36, 16, 17, 18, 31, 35, 28, 18, 19, 36, 36, 19, 35, 19, 20, 35, 35, 21, 28, 35, 20, 21, 29, 39, 40, 13, 33, 52, 52, 40, 42, 52, 34, 40, 40, 24, 41, 40, 39, 24, 28, 22, 39, 28, 21, 22, 39, 23, 24, 39, 22, 23, 40, 41, 45, 12, 13, 52, 53, 52, 42, 11, 12, 53, 54, 53, 43, 12, 52, 53, 43, 53, 42, 54, 44, 9, 10, 11, 54, 11, 53, 54, 54, 43, 44, 10, 54, 9, 44, 8, 9, 41, 25, 51, 41, 24, 25, 25, 26, 51, 51, 27, 0, 51, 26, 27, 42, 40, 45, 46, 42, 45, 44, 43, 46, 46, 43, 42, 44, 46, 47, 47, 8, 44, 47, 7, 8, 45, 41, 51, 7, 47, 55, 47, 48, 55, 47, 49, 48, 47, 46, 49, 55, 3, 4, 55, 48, 3, 49, 2, 48, 48, 2, 3, 46, 50, 49, 46, 45, 50, 49, 50, 2, 50, 1, 2, 45, 51, 50, 50, 51, 1, 51, 0, 1, 5, 55, 4, 7, 55, 6], "vertices": [2, 55, 18.98, 11.94, 0.464, 54, 189.7, -12.95, 0.536, 6, 55, 120.62, -15.5, 0.69986, 54, 199.84, -117.74, 0.24195, 51, -81.33, 270.87, 0.01586, 50, 84.99, 278.36, 0.0269, 6, -343.52, 141.99, 0.00768, 56, -242.13, 120.65, 0.00775, 6, 55, 253, -39.2, 0.81466, 54, 224.32, -249.97, 0.06789, 52, 5.73, 306.54, 0.0169, 51, 49.16, 303.36, 0.02586, 50, 218.03, 297.96, 0.0169, 56, -138.92, 34.44, 0.05778, 6, 55, 347.86, -33.06, 0.73359, 54, 263.49, -336.58, 0.00838, 53, 125.27, 306.04, 0.0156, 52, 97.22, 332.32, 0.0269, 51, 133.22, 347.74, 0.01586, 56, -53.5, -7.25, 0.19968, 4, 55, 399.33, -35.38, 0.52188, 53, 175.47, 294.4, 0.0416, 52, 148.05, 340.79, 0.0169, 56, -9.94, -34.79, 0.41962, 4, 55, 447.02, -37.52, 0.37639, 53, 221.98, 283.62, 0.064, 52, 195.14, 348.64, 0.00867, 56, 30.41, -60.3, 0.55094, 5, 55, 465.39, -126.25, 0.35687, 53, 223.91, 193.03, 0.129, 52, 231.61, 265.69, 0.0498, 51, 278.59, 310.75, 0.00488, 56, 2.37, -146.46, 0.45945, 6, 55, 392.55, -210.4, 0.33772, 53, 136.98, 123.53, 0.21218, 52, 177.92, 168.2, 0.14004, 51, 246.52, 204.18, 0.03771, 50, 404.77, 180.01, 0.00416, 56, -102.6, -183.42, 0.26819, 6, 55, 398.5, -333.63, 0.16733, 53, 120.43, 1.27, 0.37362, 52, 209.43, 48.92, 0.28463, 51, 302.33, 94.15, 0.07964, 50, 449.59, 65.06, 0.01, 56, -158.53, -293.39, 0.08478, 6, 55, 350.37, -398.97, 0.04585, 53, 61.22, -54.23, 0.46531, 52, 175.99, -25.02, 0.35181, 51, 285.12, 14.84, 0.10833, 50, 424.72, -12.19, 0.01, 56, -232.72, -326.27, 0.0187, 5, 55, 301.46, -465.37, 0.01083, 53, 1.05, -110.63, 0.46463, 52, 141.99, -100.16, 0.38009, 51, 267.63, -65.75, 0.12361, 50, 399.45, -90.69, 0.02083, 6, 55, 195.09, -523.91, 0.0048, 53, -114.19, -148.87, 0.29431, 52, 50.17, -179.6, 0.35756, 51, 194.49, -162.67, 0.24271, 50, 317.21, -180.02, 0.085, 11, 463.03, 101.34, 0.01563, 7, 55, 112.76, -485.92, 0.00512, 53, -188.23, -96.54, 0.09542, 52, -38.27, -159.61, 0.24271, 51, 103.83, -161.66, 0.31863, 50, 227.08, -170.17, 0.2375, 11, 372.37, 100.08, 0.09862, 10, 88.08, -385.25, 0.002, 8, 55, 20.29, -467.05, 0.00968, 53, -275.73, -61.17, 0.01563, 52, -132.64, -160.44, 0.085, 51, 11.72, -182.24, 0.2375, 50, 133.41, -181.67, 0.30645, 6, 102.03, 266.31, 0.00512, 11, 280.81, 77.18, 0.32535, 10, 67.57, -293.13, 0.01528, 5, 52, -254.14, -186.46, 0.01563, 51, -101.62, -233.15, 0.075, 50, 15.64, -221.28, 0.175, 11, 168.78, 23.44, 0.67478, 10, 16.76, -179.74, 0.0596, 2, 11, 116.63, -9.17, 0.872, 10, -14.47, -126.75, 0.128, 2, 11, 54.78, -57.97, 0.752, 10, -61.65, -63.66, 0.248, 2, 11, 18.62, -80.55, 0.536, 10, -83.27, -26.91, 0.464, 2, 11, 12.49, -48.23, 0.536, 10, -50.8, -21.63, 0.464, 2, 11, 5.49, 16.16, 0.536, 10, 13.74, -16.32, 0.464, 2, 11, 1.12, 73.46, 0.536, 10, 71.14, -13.43, 0.464, 3, 6, 83.66, -15.06, 0.57152, 11, 0.53, 108.02, 0.248, 10, 105.7, -13.75, 0.18048, 1, 6, -2, -18.15, 1, 1, 6, -115.15, -19.7, 1, 2, 54, -1.12, 38.73, 0.28, 6, -123.88, 13.03, 0.72, 2, 55, -69.97, -83.39, 0.216, 54, 69.14, 36.7, 0.784, 2, 55, -66.75, -36.18, 0.28, 54, 114.45, 50.32, 0.72, 2, 55, -31.5, 11.67, 0.464, 54, 171.65, 34.2, 0.536, 1, 11, 56.08, 138.88, 1, 1, 11, 112.88, 146.18, 1, 1, 11, 113.23, 135.89, 1, 1, 11, 84.17, 123.62, 1, 2, 11, 110.63, 99.77, 0.872, 10, 94.59, -123.59, 0.128, 8, 55, -82.89, -382.57, 0.01881, 54, -215.4, -56.64, 0.00411, 52, -251.17, -99.33, 0.008, 51, -116.98, -147.33, 0.058, 50, 8.72, -134.37, 0.14933, 6, 76.15, 135.49, 0.02493, 11, 151.28, 108.84, 0.69088, 10, 102.6, -164.46, 0.04595, 7, 55, -79.8, -357.08, 0.03137, 54, -190.46, -50.55, 0.01873, 51, -124.58, -122.81, 0.01467, 50, 3.55, -109.22, 0.07249, 6, 52.21, 126.21, 0.0823, 11, 143.06, 133.17, 0.772, 10, 127.13, -156.88, 0.00845, 2, 11, 48.43, 80.7, 0.752, 10, 77.14, -60.92, 0.248, 2, 11, 49.33, 10.14, 0.752, 10, 6.58, -59.98, 0.248, 2, 11, 106.49, 41.61, 0.872, 10, 36.55, -117.94, 0.128, 6, 55, -97.13, -422.65, 0.00488, 51, -113.58, -189.73, 0.02562, 50, 7.97, -176.89, 0.115, 6, 118.2, 141.83, 0.00512, 11, 155.73, 66.55, 0.7825, 10, 60.2, -167.82, 0.06688, 1, 6, -18.32, 31.98, 1, 8, 55, -58.9, -268.76, 0.08527, 54, -100.44, -38.99, 0.06933, 52, -251.43, 16.98, 0.00667, 51, -141.61, -33.66, 0.04198, 50, -4.71, -18.84, 0.11205, 6, -35.54, 103.02, 0.29877, 11, 123.79, 221.86, 0.38507, 10, 216.29, -139.93, 0.00085, 7, 55, -40.91, -155.72, 0.22804, 54, 11.69, -15.98, 0.24497, 52, -257.42, 131.29, 0.00416, 51, -171.42, 76.85, 0.03438, 50, -23.59, 94.06, 0.10193, 6, -143.71, 65.6, 0.30207, 11, 91.21, 331.59, 0.08444, 9, 55, 79.96, -300.16, 0.17704, 54, -80.88, -180, 0.0483, 53, -186.72, 92.09, 0.008, 52, -109.08, 15.23, 0.06048, 51, -2.06, -5.54, 0.19209, 50, 136.93, -4.47, 0.26209, 6, -73.29, 240.28, 0.10956, 11, 262.59, 253.48, 0.14142, 10, 244.29, -279.51, 0.00102, 8, 55, 189.19, -340.4, 0.19981, 54, -80.05, -296.4, 0.01396, 53, -86.63, 32.66, 0.06448, 52, 6.14, -1.35, 0.19696, 51, 114.08, 2.39, 0.27485, 50, 253.28, -7.9, 0.19363, 6, -89.27, 355.59, 0.02698, 11, 378.49, 264.33, 0.02933, 7, 55, 280.21, -352.82, 0.18648, 53, 0.63, 3.9, 0.22562, 52, 97.75, 5.49, 0.297, 51, 202.23, 28.28, 0.19542, 50, 343.54, 9.27, 0.06048, 6, -121.22, 441.72, 0.0041, 56, -270.77, -251.41, 0.0309, 8, 55, 98.09, -181.99, 0.38476, 54, 36.1, -155.32, 0.13236, 53, -147.41, 205, 0.00384, 52, -115.99, 134.58, 0.03648, 51, -33.82, 109.71, 0.11885, 50, 116.55, 113.34, 0.17941, 6, -186.05, 200.58, 0.10963, 11, 227.93, 367.9, 0.03467, 8, 55, 223.22, -199.31, 0.45888, 54, 63.98, -278.53, 0.03584, 53, -27.51, 165.21, 0.03552, 52, 10, 143.74, 0.12192, 51, 87.45, 145.07, 0.17875, 50, 240.7, 136.69, 0.12192, 6, -229.75, 319.11, 0.02726, 56, -244.16, -89.84, 0.0199, 8, 55, 318.04, -196.27, 0.42797, 54, 100.24, -366.2, 0.00493, 53, 66.28, 150.96, 0.12896, 52, 102.1, 166.48, 0.18608, 51, 172.74, 186.6, 0.11885, 50, 329.63, 169.71, 0.03648, 6, -277.11, 401.31, 0.00307, 56, -160.31, -134.22, 0.09366, 7, 55, 337.09, -91.97, 0.64144, 54, 204.56, -347.27, 0.00627, 53, 103.98, 250.06, 0.05184, 52, 98.98, 272.46, 0.08296, 51, 147.49, 289.58, 0.04742, 50, 314.54, 274.66, 0.01248, 56, -92.05, -53.08, 0.15758, 8, 55, 248.53, -97.6, 0.71027, 54, 168.08, -266.37, 0.05622, 53, 15.87, 260.62, 0.01152, 52, 13.54, 248.48, 0.04896, 51, 68.97, 248.23, 0.07982, 50, 232.36, 241.17, 0.04896, 6, -331.38, 293.5, 0.00922, 56, -171.75, -14.06, 0.03503, 7, 55, 118.87, -79.2, 0.60608, 54, 139.61, -138.54, 0.20484, 52, -117.11, 239.44, 0.01248, 51, -56.89, 212.01, 0.04742, 50, 103.58, 217.4, 0.08296, 6, -286.5, 170.46, 0.04224, 56, -275.24, 66.2, 0.00397, 6, 55, 16.33, -70.94, 0.39265, 54, 111.21, -39.67, 0.45203, 51, -153.85, 177.63, 0.01171, 50, 3.73, 192.64, 0.04763, 6, -245.47, 76.13, 0.08266, 11, 106.24, 432.77, 0.01333, 9, 55, 45.15, -397.53, 0.04677, 54, -184.27, -181.74, 0.00903, 53, -238.65, 2.67, 0.008, 52, -122.83, -87.26, 0.0785, 51, 5.98, -108.63, 0.22521, 50, 134.87, -107.85, 0.31315, 6, 29, 255.47, 0.028, 11, 273.22, 150.62, 0.28129, 10, 141.19, -287.45, 0.01005, 8, 55, 143.93, -426.58, 0.05114, 53, -146.8, -43.85, 0.0845, 52, -20.16, -95.07, 0.22798, 51, 108.01, -94.76, 0.33171, 50, 237.77, -103.99, 0.22598, 6, 8.07, 356.28, 0.00819, 11, 374.87, 167.06, 0.06948, 10, 154.97, -389.49, 0.00102, 6, 55, 246.5, -420.58, 0.04767, 53, -44.84, -56.6, 0.27631, 52, 78.91, -67.81, 0.36231, 51, 199.16, -47.35, 0.22721, 50, 333.11, -65.7, 0.0785, 11, 464.8, 216.75, 0.008, 5, 55, 381.97, -103.96, 0.46967, 53, 145.93, 230.11, 0.10768, 52, 145.38, 270.09, 0.05616, 51, 193.35, 296.98, 0.01171, 56, -59.02, -85.74, 0.35478], "hull": 28, "edges": [56, 58, 58, 60, 60, 62, 60, 64, 64, 66, 66, 68, 68, 58, 60, 68, 62, 70, 70, 64, 70, 38, 38, 40, 40, 42, 40, 70, 42, 56, 38, 36, 36, 34, 34, 32, 32, 72, 72, 38, 56, 62, 70, 72, 72, 74, 74, 30, 30, 32, 74, 64, 66, 76, 76, 28, 28, 30, 74, 76, 42, 44, 44, 78, 78, 56, 78, 80, 80, 58, 78, 48, 48, 46, 46, 44, 48, 82, 82, 80, 80, 84, 84, 86, 86, 88, 88, 16, 82, 90, 90, 92, 92, 94, 94, 14, 14, 16, 94, 88, 22, 24, 22, 20, 26, 24, 26, 28, 10, 12, 12, 14, 94, 96, 96, 6, 96, 98, 98, 92, 98, 4, 4, 2, 2, 100, 100, 90, 90, 84, 100, 98, 92, 86, 6, 4, 82, 102, 102, 100, 102, 0, 0, 54, 0, 2, 52, 54, 52, 50, 50, 48, 50, 102, 84, 104, 104, 26, 66, 104, 86, 106, 106, 24, 104, 106, 88, 108, 108, 22, 106, 108, 16, 18, 18, 20, 108, 18, 68, 80, 6, 8, 8, 10, 12, 110, 110, 96, 8, 110, 110, 14], "width": 321, "height": 220}}}}], "animations": {"animation1": {"slots": {"wu1": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1, "color": "ffffff00"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.8, "color": "ffffffff"}, {"time": 4, "color": "ffffff00", "curve": "stepped"}, {"time": 5, "color": "ffffff00"}, {"time": 6, "color": "ffffffff", "curve": "stepped"}, {"time": 6.8, "color": "ffffffff"}, {"time": 8, "color": "ffffff00"}]}, "wu3": {"color": [{"time": 0.6667, "color": "ffffffff"}, {"time": 1.6667, "color": "ffffff00"}, {"time": 2.6667, "color": "ffffff00"}, {"time": 3.5, "color": "ffffffff", "curve": "stepped"}, {"time": 4.6667, "color": "ffffffff"}, {"time": 5.6667, "color": "ffffff00"}, {"time": 6.6667, "color": "ffffff00"}, {"time": 7.5, "color": "ffffffff"}]}, "wu4": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1, "color": "ffffff00"}, {"time": 2, "color": "ffffffeb"}, {"time": 2.9333, "color": "ffffffee"}, {"time": 4, "color": "ffffff00", "curve": "stepped"}, {"time": 5.7333, "color": "ffffff00"}, {"time": 6, "color": "ffffffeb"}, {"time": 6.4333, "color": "ffffffff"}, {"time": 6.9333, "color": "ffffffee"}, {"time": 7.5, "color": "ffffffff"}, {"time": 8, "color": "ffffff00"}]}, "wu2": {"color": [{"color": "ffffff00"}, {"time": 2.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 4.2333, "color": "ffffffff"}, {"time": 6.6667, "color": "ffffff00"}]}, "wu5": {"color": [{"color": "ffffffbe"}, {"time": 1.9667, "color": "ffffff00", "curve": "stepped"}, {"time": 2, "color": "ffffff00"}, {"time": 3.9667, "color": "ffffffff", "curve": "stepped"}, {"time": 7.3333, "color": "ffffffff"}, {"time": 8, "color": "ffffffbe"}]}, "wu8": {"color": [{"time": 0.3333, "color": "ffffffff"}, {"time": 1.3, "color": "ffffff00", "curve": "stepped"}, {"time": 1.3333, "color": "ffffff00"}, {"time": 2.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 4.3333, "color": "ffffffff"}, {"time": 5.3, "color": "ffffff00", "curve": "stepped"}, {"time": 5.3333, "color": "ffffff00"}, {"time": 6.3333, "color": "ffffffff"}]}, "wu6": {"color": [{"color": "ffffffbe"}, {"time": 1.9667, "color": "ffffff00", "curve": "stepped"}, {"time": 2, "color": "ffffff00"}, {"time": 3.9667, "color": "ffffffff", "curve": "stepped"}, {"time": 7.3333, "color": "ffffffff"}, {"time": 8, "color": "ffffffbe"}]}, "zuiy1yzhu2": {"color": [{"color": "ffffffff"}, {"time": 2, "color": "ffffff2e"}, {"time": 4, "color": "ffffffff"}, {"time": 5.8333, "color": "ffffff2e", "curve": "stepped"}, {"time": 6, "color": "ffffff2e"}, {"time": 8, "color": "ffffffff"}]}, "biyan": {"color": [{"time": 3.1667, "color": "ffffff00"}, {"time": 3.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3667, "color": "ffffffff"}, {"time": 3.5, "color": "ffffff00", "curve": "stepped"}, {"time": 7.1667, "color": "ffffff00"}, {"time": 7.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 7.3667, "color": "ffffffff"}, {"time": 7.5, "color": "ffffff00"}], "attachment": [{"time": 3.1667, "name": "biyan"}, {"time": 3.5, "name": null}, {"time": 7.1667, "name": "biyan"}, {"time": 7.5, "name": null}]}, "wu7": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00"}, {"time": 1.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1, "color": "ffffffff"}, {"time": 3.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 4.5, "color": "ffffff00"}, {"time": 5.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 6.1, "color": "ffffffff"}, {"time": 7.1667, "color": "ffffff00"}]}, "yziyanzhu23": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 2, "color": "ffffff00"}, {"time": 2.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.4, "color": "ffffffff"}, {"time": 3.6, "color": "ffffff00", "curve": "stepped"}, {"time": 6, "color": "ffffff00"}, {"time": 6.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 7.4, "color": "ffffffff"}, {"time": 7.6, "color": "ffffff00"}], "attachment": [{"time": 2.0667, "name": "yziyanzhu22"}]}}, "bones": {"6": {"rotate": [{"angle": 0.93, "curve": 0.381, "c2": 0.54, "c3": 0.744}, {"time": 0.8333, "angle": -9.17, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "angle": 16.74, "curve": 0.246, "c3": 0.635, "c4": 0.55}, {"time": 4, "angle": 0.93, "curve": 0.381, "c2": 0.54, "c3": 0.744}, {"time": 4.8333, "angle": -9.17, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 16.74, "curve": 0.246, "c3": 0.635, "c4": 0.55}, {"time": 8, "angle": 0.93}], "translate": [{"x": -7.1, "y": -4.06, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": -38.47, "y": -21.98, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 4, "x": -7.1, "y": -4.06, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 4.5, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "x": -38.47, "y": -21.98, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "x": -7.1, "y": -4.06}], "scale": [{"x": 1.127, "y": 1.127, "curve": 0.365, "c2": 0.45, "c3": 0.754}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": 1.209, "y": 1.209, "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 4, "x": 1.127, "y": 1.127, "curve": 0.365, "c2": 0.45, "c3": 0.754}, {"time": 5.1667, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "x": 1.209, "y": 1.209, "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 8, "x": 1.127, "y": 1.127}]}, "dahuap": {"rotate": [{"time": 2}, {"time": 2.0667, "angle": -1.41}, {"time": 2.1333, "angle": 0.4}, {"time": 2.2, "angle": -0.31}, {"time": 2.3333, "angle": -0.29}, {"time": 2.4, "angle": 3.31}, {"time": 2.5, "angle": -3.87}, {"time": 2.6, "curve": "stepped"}, {"time": 4.3667}, {"time": 4.4333, "angle": -1.41}, {"time": 4.5, "angle": 0.4}, {"time": 4.5667, "angle": -0.31}, {"time": 4.7, "angle": -0.29}, {"time": 4.7667, "angle": 3.31}, {"time": 4.8667, "angle": -3.87}, {"time": 4.9667}, {"time": 5.1, "angle": -3.87}, {"time": 5.2667}]}, "yao": {"rotate": [{"time": 4.6667, "curve": 0.326, "c2": 0.31, "c3": 0.664, "c4": 0.66}, {"time": 5, "angle": -1.24, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 5.0333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 0.81, "y": 3.71, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 0.81, "y": 3.71, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4.6667, "x": 0.61, "y": 2.81, "curve": 0.352, "c2": 0.41, "c3": 0.689, "c4": 0.75}, {"time": 4.8333, "x": 1.03, "y": 4.73, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 5, "x": 1.66, "y": 7.64, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 5.0333, "x": -0.12, "y": -0.57, "curve": 0.314, "c2": 0.26, "c3": 0.65, "c4": 0.6}, {"time": 5.1, "x": 0.61, "y": 2.8, "curve": 0.326, "c2": 0.31, "c3": 0.661, "c4": 0.65}, {"time": 5.3, "x": 0.23, "y": 1.06, "curve": 0.332, "c2": 0.33, "c3": 0.676, "c4": 0.7}, {"time": 5.6667, "x": 0.23, "y": 1.06, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7, "x": 0.81, "y": 3.71, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "yao2": {"rotate": [{"angle": -4.11, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -8.22, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": -4.11, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -8.22, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -4.11, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.5, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": -8.22, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6, "angle": -4.11, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.5, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "angle": -8.22, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -4.11}]}, "yao3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -5.38, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -5.38, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -5.38, "curve": 0.25, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -5.38, "curve": 0.25, "c3": 0.75}, {"time": 8}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": -14.83, "y": 10.32, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -14.83, "y": 10.32, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": -14.83, "y": 10.32, "curve": 0.25, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7, "x": -14.83, "y": 10.32, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "yao4": {"rotate": [{"angle": 3.21, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": -1.25, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 3.68, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": 3.21, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": -1.25, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 3.68, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": 3.21, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 4.8333, "angle": -1.25, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "angle": 3.68, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 6, "angle": 3.21, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 6.8333, "angle": -1.25, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "angle": 3.68, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 8, "angle": 3.21}], "translate": [{"x": 7.85, "y": 3.29, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333}, {"time": 1.8333, "x": 8.69, "y": 3.64, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "x": 7.85, "y": 3.29, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333}, {"time": 3.8333, "x": 8.69, "y": 3.64, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "x": 7.85, "y": 3.29, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 4.8333}, {"time": 5.8333, "x": 8.69, "y": 3.64, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 6, "x": 7.85, "y": 3.29, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 6.8333}, {"time": 7.8333, "x": 8.69, "y": 3.64, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 8, "x": 7.85, "y": 3.29}]}, "jiao1": {"translate": [{"x": -18.64, "y": 10.33, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "x": -20.63, "y": 11.43, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "x": -18.64, "y": 10.33, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "x": -20.63, "y": 11.43, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "x": -18.64, "y": 10.33, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "x": -20.63, "y": 11.43, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 6, "x": -18.64, "y": 10.33, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 6.1667, "x": -20.63, "y": 11.43, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 8, "x": -18.64, "y": 10.33}]}, "jiao2": {"rotate": [{"angle": 1.33, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 1.47, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 1.33, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": 1.47, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": 1.33, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "angle": 1.47, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 6, "angle": 1.33, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 6.1667, "angle": 1.47, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 8, "angle": 1.33}], "translate": [{"x": 8.07, "y": -13.26, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "x": 8.93, "y": -14.67, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "x": 8.07, "y": -13.26, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "x": 8.93, "y": -14.67, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "x": 8.07, "y": -13.26, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "x": 8.93, "y": -14.67, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 6, "x": 8.07, "y": -13.26, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 6.1667, "x": 8.93, "y": -14.67, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 8, "x": 8.07, "y": -13.26}]}, "jiaoz1": {"rotate": [{"angle": -1.13, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": -1.26, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": -1.13, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": -1.26, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": -1.13, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "angle": -1.26, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 6, "angle": -1.13, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 6.1667, "angle": -1.26, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 8, "angle": -1.13}], "translate": [{"x": -2.66, "y": 2.14, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "x": -2.94, "y": 2.37, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "x": -2.66, "y": 2.14, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "x": -2.94, "y": 2.37, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "x": -2.66, "y": 2.14, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "x": -2.94, "y": 2.37, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 6, "x": -2.66, "y": 2.14, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 6.1667, "x": -2.94, "y": 2.37, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 8, "x": -2.66, "y": 2.14}]}, "qbai22": {"translate": [{"x": -0.04, "y": -0.29, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": -0.21, "y": -1.42, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 2, "x": -0.04, "y": -0.29, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 2.2667, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "x": -0.21, "y": -1.42, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 4, "x": -0.04, "y": -0.29, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 4.2667, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 4.6667, "x": -0.08, "y": -0.58, "curve": 0.332, "c2": 0.33, "c3": 0.669, "c4": 0.67}, {"time": 4.8333, "x": -1.12, "y": -7.69, "curve": 0.344, "c2": 0.37, "c3": 0.681, "c4": 0.71}, {"time": 5, "x": -0.17, "y": -1.16}, {"time": 5.0333, "x": 2.49, "y": 5.19}, {"time": 5.1, "x": -0.21, "y": -1.42}, {"time": 5.3, "x": -0.19, "y": -1.32}, {"time": 5.6667, "x": -0.13, "y": -0.9, "curve": 0.335, "c2": 0.34, "c3": 0.685, "c4": 0.72}, {"time": 6, "x": -0.04, "y": -0.29, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 6.2667, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "x": -0.21, "y": -1.42, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 8, "x": -0.04, "y": -0.29}]}, "qbai23": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 12.85, "y": -6.22, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 12.85, "y": -6.22, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4.6667, "x": 9.2, "y": -4.46, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 5, "x": 12.85, "y": -6.22, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 5.0333, "x": 20.14, "y": -15.81, "curve": 0.316, "c2": 0.17, "c3": 0.65, "c4": 0.51}, {"time": 5.1, "x": -1.88, "y": -0.9, "curve": 0.296, "c2": 0.18, "c3": 0.638, "c4": 0.55}, {"time": 5.3333, "x": 11.77, "y": -15.14, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7, "x": 12.85, "y": -6.22, "curve": 0.25, "c3": 0.75}, {"time": 8}], "scale": [{"x": 1.396, "y": 1.179, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 1.793, "y": 1.358, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 1.793, "y": 1.358, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 1.396, "y": 1.179, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.5, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": 1.793, "y": 1.358, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "x": 1.793, "y": 1.358, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": 1.396, "y": 1.179}]}, "qbai24": {"rotate": [{"time": 5.0333}, {"time": 5.1, "angle": -2.31}, {"time": 5.3}], "translate": [{"x": 1.29, "y": -0.62, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 2.58, "y": -1.25, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 1.29, "y": -0.62, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 2.58, "y": -1.25, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 1.29, "y": -0.62, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.5, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4.6667, "x": 0.34, "y": -0.16, "curve": 0.315, "c2": 0.27, "c3": 0.652, "c4": 0.61}, {"time": 4.8333, "x": -3.3, "y": -0.18, "curve": 0.327, "c2": 0.31, "c3": 0.664, "c4": 0.66}, {"time": 5, "x": 9.13, "y": -8.24}, {"time": 5.0333, "x": 8.07, "y": -3.91}, {"time": 5.1, "x": -6.99, "y": 3.39}, {"time": 5.3, "x": 1.91, "y": -2.74}, {"time": 6, "x": 1.29, "y": -0.62, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.5, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "x": 2.58, "y": -1.25, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": 1.29, "y": -0.62}]}, "tou": {"rotate": [{"time": 4.6667, "curve": 0.328, "c2": 0.32, "c3": 0.663, "c4": 0.66}, {"time": 4.8333, "angle": -1.24, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 5, "angle": 0.43}, {"time": 5.0333, "angle": 5.08}, {"time": 5.1, "angle": 3.99}, {"time": 5.2, "angle": 1.16}, {"time": 5.4667, "angle": 3.55}, {"time": 6}], "translate": [{"x": 3.09, "y": -1.16, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.7, "x": -4.02, "y": 1.51, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "x": 5.36, "y": -2.02, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2, "x": 3.09, "y": -1.16, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 2.7, "x": -4.02, "y": 1.51, "curve": 0.25, "c3": 0.75}, {"time": 3.7, "x": 5.36, "y": -2.02, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 4, "x": 3.09, "y": -1.16, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 4.6667, "x": -4.02, "y": 1.51, "curve": 0.266, "c3": 0.618, "c4": 0.42}, {"time": 5, "x": 3.17, "y": -1.08}, {"time": 5.0333, "x": 5.03, "y": -9.78}, {"time": 5.1, "x": 2.16, "y": -0.81}, {"time": 5.3, "x": 3.21, "y": -1.21}, {"time": 5.6667, "x": 0.06, "y": -4.38, "curve": 0.343, "c2": 0.66, "c3": 0.677}, {"time": 6, "x": 3.09, "y": -1.16, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 6.7, "x": -4.02, "y": 1.51, "curve": 0.25, "c3": 0.75}, {"time": 7.7, "x": 5.36, "y": -2.02, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 8, "x": 3.09, "y": -1.16}]}, "yanzhu2": {"translate": [{"time": 0.8333}, {"time": 0.9, "x": -3.72, "y": -5.7, "curve": "stepped"}, {"time": 1.6667, "x": -3.72, "y": -5.7}, {"time": 1.7333, "x": -2.51, "y": -8.16, "curve": "stepped"}, {"time": 3.2667, "x": -2.51, "y": -8.16}, {"time": 3.3333, "curve": "stepped"}, {"time": 4.5}, {"time": 4.5667, "x": -6.13, "y": -3.86, "curve": "stepped"}, {"time": 7.2667, "x": -6.13, "y": -3.86}, {"time": 7.3333}], "scale": [{"time": 0.8333}, {"time": 0.9, "x": 1.172, "y": 1.172, "curve": "stepped"}, {"time": 3.2667, "x": 1.172, "y": 1.172}, {"time": 3.3333, "curve": "stepped"}, {"time": 4.5}, {"time": 4.5667, "x": 1.505, "y": 1.505, "curve": "stepped"}, {"time": 7.2667, "x": 1.505, "y": 1.505}, {"time": 7.3333}]}, "yanzhu1": {"translate": [{"time": 0.8333}, {"time": 0.9, "x": -3.72, "y": -5.7, "curve": "stepped"}, {"time": 1.6667, "x": -3.72, "y": -5.7}, {"time": 1.7333, "x": -2.51, "y": -8.16, "curve": "stepped"}, {"time": 3.2667, "x": -2.51, "y": -8.16}, {"time": 3.3333, "curve": "stepped"}, {"time": 4.5}, {"time": 4.5667, "x": -6.13, "y": -3.86, "curve": "stepped"}, {"time": 7.2667, "x": -6.13, "y": -3.86}, {"time": 7.3333}], "scale": [{"time": 0.8333}, {"time": 0.9, "x": 1.172, "y": 1.172, "curve": "stepped"}, {"time": 3.2667, "x": 1.172, "y": 1.172}, {"time": 3.3333, "curve": "stepped"}, {"time": 4.5}, {"time": 4.5667, "x": 1.505, "y": 1.505, "curve": "stepped"}, {"time": 7.2667, "x": 1.505, "y": 1.505}, {"time": 7.3333}]}, "tou2": {"rotate": [{"time": 4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5, "angle": 15.21, "curve": 0.334, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 5.1, "angle": -1.51, "curve": 0.343, "c2": 0.36, "c3": 0.681, "c4": 0.71}, {"time": 5.4667, "angle": -17.69, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 6}], "translate": [{"x": 18.74, "y": -1.62, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "x": 20.74, "y": -1.79, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "x": 18.74, "y": -1.62, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "x": 20.74, "y": -1.79, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "x": 18.74, "y": -1.62, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "x": 20.74, "y": -1.79, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 5.4667, "x": 12.4, "y": -10.97, "curve": 0.328, "c2": 0.32, "c3": 0.705, "c4": 0.79}, {"time": 6, "x": 18.74, "y": -1.62, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 6.1667, "x": 20.74, "y": -1.79, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 8, "x": 18.74, "y": -1.62}]}, "tou3": {"rotate": [{"angle": -2.7, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -33.09, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 9.34, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -2.7, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": -33.09, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 9.34, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -2.7, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.6667, "angle": -33.09, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": 9.34, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "angle": -2.7, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 6.6667, "angle": -33.09, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": 9.34, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "angle": -2.7}], "translate": [{"x": 1.34, "y": -6.88, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.4333, "x": 3.92, "y": -18.9, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": -2.34, "y": 10.29, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2, "x": 1.34, "y": -6.88, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 2.4333, "x": 3.92, "y": -18.9, "curve": 0.25, "c3": 0.75}, {"time": 3.4333, "x": -2.34, "y": 10.29, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 4, "x": 1.34, "y": -6.88, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 4.4333, "x": 3.92, "y": -18.9, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "x": -2.34, "y": 10.29, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 6, "x": 1.34, "y": -6.88, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 6.4333, "x": 3.92, "y": -18.9, "curve": 0.25, "c3": 0.75}, {"time": 7.4333, "x": -2.34, "y": 10.29, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 8, "x": 1.34, "y": -6.88}]}, "tou4": {"rotate": [{"time": 4, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 4.9333, "angle": -8.09, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 5.0667, "angle": 8.92, "curve": 0.334, "c2": 0.34, "c3": 0.667, "c4": 0.67}, {"time": 5.1333, "angle": -7.63, "curve": 0.341, "c2": 0.36, "c3": 0.678, "c4": 0.7}, {"time": 5.4333, "angle": -22.77, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 6}], "translate": [{"x": 16.7, "y": 16.49, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -7.99, "y": -0.95, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 16.7, "y": 16.49, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -7.99, "y": -0.95, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 16.7, "y": 16.49, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": -7.99, "y": -0.95, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": 16.7, "y": 16.49, "curve": 0.25, "c3": 0.75}, {"time": 7, "x": -7.99, "y": -0.95, "curve": 0.25, "c3": 0.75}, {"time": 8, "x": 16.7, "y": 16.49}]}, "tou5": {"rotate": [{"angle": 6.67, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -9.29, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 13, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 6.67, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": -9.29, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 13, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 6.67, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.6667, "angle": -9.29, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": 13, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "angle": 6.67, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 6.6667, "angle": -9.29, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": 13, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "angle": 6.67}], "translate": [{"x": 29.29, "y": -23.36, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": 39.82, "y": -40.88, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 2.69, "y": 20.88, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 29.29, "y": -23.36, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "x": 39.82, "y": -40.88, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 2.69, "y": 20.88, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": 29.29, "y": -23.36, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "x": 39.82, "y": -40.88, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 2.69, "y": 20.88, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "x": 29.29, "y": -23.36, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.3333, "x": 39.82, "y": -40.88, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": 2.69, "y": 20.88, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "x": 29.29, "y": -23.36}]}, "tou6": {"rotate": [{"angle": -6.65, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 6.81, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -6.65, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 6.81, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -6.65, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 4.9, "angle": -2.1, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 5, "angle": 6.81, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 5.0667, "angle": -12.67, "curve": 0.295, "c2": 0.15, "c3": 0.635, "c4": 0.52}, {"time": 5.2667, "angle": 14.82, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 6, "angle": -6.65, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": 6.81, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -6.65}], "translate": [{"x": 7.98, "y": -4.76, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": 28.11, "y": -16.78, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 7.98, "y": -4.76, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "x": 28.11, "y": -16.78, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": 7.98, "y": -4.76, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.6667, "x": 28.11, "y": -16.78, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "x": 7.98, "y": -4.76, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 6.6667, "x": 28.11, "y": -16.78, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "x": 7.98, "y": -4.76}]}, "tou7": {"rotate": [{"time": 4, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 5, "angle": 0.32, "curve": 0.331, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 5.0333, "angle": 0.36}, {"time": 5.1, "angle": -2.5}, {"time": 5.3, "angle": -11.1}, {"time": 5.6667, "angle": -11.18, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 6, "angle": -13.04, "curve": 0.334, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 6.3333, "angle": -3.41, "curve": 0.338, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 6.7}], "translate": [{"x": 13.56, "y": 9.66, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "x": 15.01, "y": 10.7, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "x": 13.56, "y": 9.66, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "x": 15.01, "y": 10.7, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "x": 13.56, "y": 9.66, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "x": 15.01, "y": 10.7, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5, "x": -10.64, "y": -6, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.0333, "x": 18.46, "y": 21.44}, {"time": 5.1, "x": 32.01, "y": 30.72}, {"time": 5.3, "x": 25.17, "y": 22.67}, {"time": 5.6667, "x": 22.33, "y": 19.32, "curve": 0.342, "c2": 0.36, "c3": 0.678, "c4": 0.7}, {"time": 6, "x": 17.94, "y": 14.15, "curve": 0.346, "c2": 0.39, "c3": 0.681, "c4": 0.72}, {"time": 6.3333, "x": 13.56, "y": 9.66, "curve": 0.297, "c2": 0.21, "c3": 0.651, "c4": 0.62}, {"time": 6.7, "x": 6.93, "y": 4.94, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 7.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 8, "x": 13.56, "y": 9.66}]}, "tou8": {"rotate": [{"time": 5, "curve": 0.328, "c2": 0.31, "c3": 0.661, "c4": 0.65}, {"time": 5.0333, "angle": 9.31}, {"time": 5.1, "angle": 17.1}, {"time": 5.3, "angle": 5.18}, {"time": 5.6667, "angle": -14.84, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 6, "angle": -15.35, "curve": 0.334, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 6.3333}], "translate": [{"x": 3.12, "y": -17.37, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": 4.36, "y": -24.25, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 3.12, "y": -17.37, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "x": 4.36, "y": -24.25, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": 3.12, "y": -17.37, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "x": 4.36, "y": -24.25, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 5, "x": 5.79, "y": -12.33, "curve": 0.353, "c2": 0.4, "c3": 0.692, "c4": 0.76}, {"time": 5.0333, "x": 2.1, "y": 9.18}, {"time": 5.1, "x": 2.51, "y": -0.52}, {"time": 5.3, "x": 2.59, "y": -10.69}, {"time": 5.6667, "x": 2.93, "y": -12.9, "curve": 0.331, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 6, "x": 6.12, "y": -7.36, "curve": 0.338, "c2": 0.35, "c3": 0.674, "c4": 0.69}, {"time": 6.3333, "x": 4.36, "y": -24.25, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 6.7, "x": 2.94, "y": -16.36, "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 7.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "x": 3.12, "y": -17.37}]}, "tou9": {"rotate": [{"time": 5, "curve": 0.328, "c2": 0.31, "c3": 0.661, "c4": 0.65}, {"time": 5.0333, "angle": 6.18}, {"time": 5.1, "angle": 44.26}, {"time": 5.3, "angle": 34.42}, {"time": 5.6667, "angle": -0.36, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 6, "angle": -19.91, "curve": 0.334, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 6.3333, "angle": -15.56, "curve": 0.338, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 6.7}], "translate": [{"x": 3.25, "y": -22.44, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "x": 6.51, "y": -44.89, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 3.25, "y": -22.44, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "x": 6.51, "y": -44.89, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 3.25, "y": -22.44, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.5, "x": 6.51, "y": -44.89, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 5, "x": 7.07, "y": -34.15, "curve": 0.336, "c2": 0.34, "c3": 0.676, "c4": 0.69}, {"time": 5.0333, "x": 1.88, "y": 30.86}, {"time": 5.1, "x": -0.99, "y": 27.57}, {"time": 5.3, "x": 6.61, "y": -4.3}, {"time": 5.6667, "x": 14.22, "y": -20.59, "curve": 0.327, "c2": 0.31, "c3": 0.66, "c4": 0.64}, {"time": 6, "x": 6.65, "y": -28.38, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 6.3333, "x": 10.68, "y": -29.47, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 6.7, "x": 5.66, "y": -39.05, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 7.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": 3.25, "y": -22.44}]}, "tou10": {"rotate": [{"angle": 16.75, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": -16.38, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 20.29, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": 16.75, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": -16.38, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 20.29, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": 16.75, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 4.8333, "angle": -16.38, "curve": "stepped"}, {"time": 5, "angle": -16.38, "curve": 0.311, "c2": 0.2, "c3": 0.646, "c4": 0.55}, {"time": 5.0333, "angle": -24.35}, {"time": 5.1, "angle": 2.28}, {"time": 5.3, "angle": 8.04}, {"time": 5.6667, "angle": 26.79, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 6, "angle": -0.37, "curve": 0.338, "c2": 0.35, "c3": 0.672, "c4": 0.68}, {"time": 6.3333, "angle": -16.38, "curve": "stepped"}, {"time": 6.8333, "angle": -16.38, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "angle": 20.29, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 8, "angle": 16.75}], "translate": [{"time": 5.3}, {"time": 5.6667, "x": -0.11, "y": 6.28, "curve": 0.348, "c2": 0.45, "c3": 0.682, "c4": 0.79}, {"time": 6}]}, "tou11": {"rotate": [{"time": 4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5, "angle": -9.43, "curve": 0.334, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 5.1667, "angle": 17.34, "curve": 0.343, "c2": 0.36, "c3": 0.679, "c4": 0.71}, {"time": 5.4667, "angle": 20.7, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 6}], "translate": [{"x": 0.78, "y": -6.57, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -1.56, "y": 12.33, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 0.78, "y": -6.57, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -1.56, "y": 12.33, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 0.78, "y": -6.57, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": -1.56, "y": 12.33, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": 0.78, "y": -6.57, "curve": 0.25, "c3": 0.75}, {"time": 7, "x": -1.56, "y": 12.33, "curve": 0.25, "c3": 0.75}, {"time": 8, "x": 0.78, "y": -6.57}]}, "tou12": {"translate": [{"x": -2.13, "y": 5.76, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "x": -2.78, "y": 8.75, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 3.99, "y": -22.23, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "x": -2.13, "y": 5.76, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "x": -2.78, "y": 8.75, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": 3.99, "y": -22.23, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "x": -2.13, "y": 5.76, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "x": -2.78, "y": 8.75, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": 3.99, "y": -22.23, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 6, "x": -2.13, "y": 5.76, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 6.1667, "x": -2.78, "y": 8.75, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "x": 3.99, "y": -22.23, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 8, "x": -2.13, "y": 5.76}]}, "tou13": {"rotate": [{"angle": -6.4, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 8.21, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -12.19, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -6.4, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": 8.21, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -12.19, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -6.4, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.6667, "angle": 7.98, "curve": 0.274, "c2": 0.07, "c3": 0.625, "c4": 0.48}, {"time": 5, "angle": 1.88, "curve": 0.327, "c2": 0.31, "c3": 0.664, "c4": 0.66}, {"time": 5.0333, "angle": -2.42, "curve": 0.337, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 5.1, "angle": -6.07, "curve": 0.341, "c2": 0.36, "c3": 0.676, "c4": 0.7}, {"time": 5.3, "angle": -8.28, "curve": 0.38, "c2": 0.6, "c3": 0.725}, {"time": 5.6667, "angle": -12.19, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "angle": -6.4, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 6.6667, "angle": 8.21, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": -12.19, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "angle": -6.4}], "translate": [{"x": 1.91, "y": -8.66, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "x": -2.44, "y": 16.17, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "x": 9.39, "y": -51.34, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "x": 1.91, "y": -8.66, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.4, "x": -2.44, "y": 16.17, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "x": 9.39, "y": -51.34, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "x": 1.91, "y": -8.66, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.4, "x": -2.44, "y": 16.17, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 4.6667, "x": 0.42, "y": -0.18, "curve": 0.32, "c2": 0.29, "c3": 0.668, "c4": 0.67}, {"time": 5, "x": 5.32, "y": -28.11, "curve": 0.346, "c2": 0.38, "c3": 0.683, "c4": 0.72}, {"time": 5.0333, "x": 7.55, "y": -40.86, "curve": 0.356, "c2": 0.44, "c3": 0.693, "c4": 0.79}, {"time": 5.1, "x": 9, "y": -49.13, "curve": 0.357, "c2": 0.65, "c3": 0.692}, {"time": 5.3, "x": 9.39, "y": -51.34, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 5.6667, "x": 6.98, "y": -37.63, "curve": 0.315, "c2": 0.28, "c3": 0.665, "c4": 0.66}, {"time": 6, "x": 1.91, "y": -8.66, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.4, "x": -2.44, "y": 16.17, "curve": 0.25, "c3": 0.75}, {"time": 7.4, "x": 9.39, "y": -51.34, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8, "x": 1.91, "y": -8.66}]}, "tou14": {"rotate": [{"time": 4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5, "angle": -15.23, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 5.0667}], "translate": [{"x": 0.79, "y": -0.93, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": 8.98, "y": -7.45, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -2.45, "y": 1.66, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 0.79, "y": -0.93, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "x": 8.98, "y": -7.45, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": -2.45, "y": 1.66, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": 0.79, "y": -0.93, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.6667, "x": 8.98, "y": -7.45, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": -2.45, "y": 1.66, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "x": 0.79, "y": -0.93, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 6.6667, "x": 8.98, "y": -7.45, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "x": -2.45, "y": 1.66, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "x": 0.79, "y": -0.93}]}, "tou15": {"translate": [{"x": 2.57, "y": -5.23, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "x": 7.02, "y": 22.45, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 2.09, "y": -8.19, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "x": 2.57, "y": -5.23, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "x": 7.02, "y": 22.45, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 2.09, "y": -8.19, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "x": 2.57, "y": -5.23, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 4.8333, "x": 7.02, "y": 22.45, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "x": 2.09, "y": -8.19, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 6, "x": 2.57, "y": -5.23, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 6.8333, "x": 7.02, "y": 22.45, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": 2.09, "y": -8.19, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 8, "x": 2.57, "y": -5.23}]}, "tou16": {"rotate": [{"angle": -5.85, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -13.84, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 14.32, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -5.85, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": -13.84, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 14.32, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -5.85, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "angle": -13.84, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 14.32, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "angle": -5.85, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.3333, "angle": -13.84, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": 14.32, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "angle": -5.85}], "translate": [{"x": 5.31, "y": -17.57, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 15.6, "y": 38.47, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 5.31, "y": -17.57, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 15.6, "y": 38.47, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 5.31, "y": -17.57, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 15.6, "y": 38.47, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": 5.31, "y": -17.57, "curve": 0.25, "c3": 0.75}, {"time": 7, "x": 15.6, "y": 38.47, "curve": 0.25, "c3": 0.75}, {"time": 8, "x": 5.31, "y": -17.57}]}, "tou17": {"rotate": [{"time": 4, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 4.9333, "angle": 20.7, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 5.0667}], "translate": [{"x": 2.92, "y": 6.55, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "x": 5.83, "y": 13.1, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 2.92, "y": 6.55, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "x": 5.83, "y": 13.1, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 2.92, "y": 6.55, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.5, "x": 5.83, "y": 13.1, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6, "x": 2.92, "y": 6.55, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.5, "x": 5.83, "y": 13.1, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": 2.92, "y": 6.55}]}, "tou18": {"translate": [{"x": 0.37, "y": -1.32, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": 4.47, "y": -21.45, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -1.26, "y": 6.65, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 0.37, "y": -1.32, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "x": 4.47, "y": -21.45, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": -1.26, "y": 6.65, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": 0.37, "y": -1.32, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.6667, "x": 4.47, "y": -21.45, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": -1.26, "y": 6.65, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "x": 0.37, "y": -1.32, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 6.6667, "x": 4.47, "y": -21.45, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "x": -1.26, "y": 6.65, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "x": 0.37, "y": -1.32}]}, "tou19": {"rotate": [{"angle": 4.8, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 7.13, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.1667, "angle": -17.02, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 4.8, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": 7.13, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.1667, "angle": -17.02, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": 4.8, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "angle": 7.13, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.1667, "angle": -17.02, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 6, "angle": 4.8, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 6.1667, "angle": 7.13, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.1667, "angle": -17.02, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 8, "angle": 4.8}], "translate": [{"x": -12.41, "y": 12.53, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "x": 17.49, "y": -39.85, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": -15.6, "y": 18.12, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "x": -12.41, "y": 12.53, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "x": 17.49, "y": -39.85, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": -15.6, "y": 18.12, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "x": -12.41, "y": 12.53, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 4.8333, "x": 17.49, "y": -39.85, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "x": -15.6, "y": 18.12, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 6, "x": -12.41, "y": 12.53, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 6.8333, "x": 17.49, "y": -39.85, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": -15.6, "y": 18.12, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 8, "x": -12.41, "y": 12.53}]}, "yziyanbai33": {"rotate": [{"time": 2}, {"time": 2.1, "angle": 7.51}, {"time": 2.2, "angle": 1.96}, {"time": 2.2667, "angle": 7.51}, {"time": 2.3667, "angle": 1.96}, {"time": 2.4333, "angle": 7.51}, {"time": 2.5333, "angle": 1.96}, {"time": 2.6, "angle": 7.51}, {"time": 2.7, "angle": 1.96}, {"time": 2.7667, "angle": 7.51}, {"time": 2.8667, "angle": 1.96}, {"time": 2.9333, "angle": 7.51}, {"time": 3.0333, "angle": 1.96}, {"time": 3.1, "angle": 7.51}, {"time": 3.2, "angle": 1.96}, {"time": 3.2667, "angle": 7.51}, {"time": 4, "curve": "stepped"}, {"time": 6}, {"time": 6.1, "angle": 7.51}, {"time": 6.2, "angle": 1.96}, {"time": 6.2667, "angle": 7.51}, {"time": 6.3667, "angle": 1.96}, {"time": 6.4333, "angle": 7.51}, {"time": 6.5333, "angle": 1.96}, {"time": 6.6, "angle": 7.51}, {"time": 6.7, "angle": 1.96}, {"time": 6.7667, "angle": 7.51}, {"time": 6.8667, "angle": 1.96}, {"time": 6.9333, "angle": 7.51}, {"time": 7.0333, "angle": 1.96}, {"time": 7.1, "angle": 7.51}, {"time": 7.2, "angle": 1.96}, {"time": 7.2667, "angle": 7.51}, {"time": 8}], "translate": [{"time": 0.9667}, {"time": 1.0333, "x": 10.02, "y": -3.85, "curve": "stepped"}, {"time": 2, "x": 10.02, "y": -3.85}, {"time": 2.0667, "x": 12.81, "y": -0.23, "curve": "stepped"}, {"time": 3.4, "x": 12.81, "y": -0.23}, {"time": 3.4667, "curve": "stepped"}, {"time": 4.9667}, {"time": 5.0333, "x": 10.02, "y": -3.85, "curve": "stepped"}, {"time": 6, "x": 10.02, "y": -3.85}, {"time": 6.0667, "x": 12.81, "y": -0.23, "curve": "stepped"}, {"time": 7.4, "x": 12.81, "y": -0.23}, {"time": 7.4667}], "scale": [{"time": 0.9667}, {"time": 1.0333, "x": 1.069, "y": 1.069, "curve": "stepped"}, {"time": 2, "x": 1.069, "y": 1.069}, {"time": 2.0667, "curve": "stepped"}, {"time": 4.9667}, {"time": 5.0333, "x": 1.069, "y": 1.069, "curve": "stepped"}, {"time": 6, "x": 1.069, "y": 1.069}, {"time": 6.0667}]}, "zuiy1yzhu": {"rotate": [{"time": 5.5667}, {"time": 5.6333, "angle": -18.9}, {"time": 5.7}], "translate": [{"time": 0.5}, {"time": 0.5667, "x": -3.58, "y": -6.66, "curve": "stepped"}, {"time": 1.3333, "x": -3.58, "y": -6.66}, {"time": 1.4, "x": -7.76, "y": -3.95, "curve": "stepped"}, {"time": 1.9333, "x": -7.76, "y": -3.95}, {"time": 2, "x": -3.58, "y": -6.66, "curve": "stepped"}, {"time": 3.3333, "x": -3.58, "y": -6.66}, {"time": 3.4, "curve": "stepped"}, {"time": 4.5}, {"time": 4.5667, "x": -3.58, "y": -6.66, "curve": "stepped"}, {"time": 5.0333, "x": -3.58, "y": -6.66}, {"time": 5.1, "x": 7.69, "y": 15.41, "curve": "stepped"}, {"time": 5.5667, "x": 7.69, "y": 15.41}, {"time": 5.6333, "x": 3.05, "y": 7.81}, {"time": 5.7, "x": -2.71, "y": -4.96}, {"time": 5.7667, "x": -4, "y": -8.59}, {"time": 5.8333, "x": -3.58, "y": -6.66, "curve": "stepped"}, {"time": 7.3333, "x": -3.58, "y": -6.66}, {"time": 7.4}], "scale": [{"time": 5.5667}, {"time": 5.6333, "x": 0.761, "y": 1.209}, {"time": 5.7}, {"time": 5.7667, "x": 1.134, "y": 0.93}, {"time": 5.8333}]}, "yziyanzhu33": {"translate": [{"time": 1}, {"time": 1.1, "x": 3.56, "y": -3.21, "curve": "stepped"}, {"time": 2.6667, "x": 3.56, "y": -3.21}, {"time": 2.7667, "curve": "stepped"}, {"time": 5}, {"time": 5.1, "x": 3.56, "y": -3.21, "curve": "stepped"}, {"time": 6.6667, "x": 3.56, "y": -3.21}, {"time": 6.7667}]}, "qbai2": {"rotate": [{"time": 5}, {"time": 5.0333, "angle": -3.92}, {"time": 5.1, "angle": 2.83}, {"time": 5.3333, "angle": 8.78}, {"time": 6}], "translate": [{"x": -0.43, "y": -0.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": 2.93, "y": 2.19, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -8.93, "y": -7.42, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -0.43, "y": -0.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "x": 2.93, "y": 2.19, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -8.93, "y": -7.42, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": -0.43, "y": -0.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "x": 2.93, "y": 2.19, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": -8.93, "y": -7.42, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "x": -0.43, "y": -0.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.3333, "x": 2.93, "y": 2.19, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": -8.93, "y": -7.42, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "x": -0.43, "y": -0.54}]}, "qbai3": {"translate": [{"x": -2.56, "y": 2.91, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5333, "x": 1.33, "y": -3.41, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "x": -5.82, "y": 8.21, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 2, "x": -2.56, "y": 2.91, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 2.5333, "x": 1.33, "y": -3.41, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "x": -5.82, "y": 8.21, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 4, "x": -2.56, "y": 2.91, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 4.5333, "x": 1.33, "y": -3.41, "curve": 0.25, "c3": 0.75}, {"time": 5.5333, "x": -5.82, "y": 8.21, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 6, "x": -2.56, "y": 2.91, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 6.5333, "x": 1.33, "y": -3.41, "curve": 0.25, "c3": 0.75}, {"time": 7.5333, "x": -5.82, "y": 8.21, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 8, "x": -2.56, "y": 2.91}]}, "qbai4": {"translate": [{"x": -10.07, "y": 12.11, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 0.7333, "x": 2.31, "y": -7.61, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "x": -13.21, "y": 17.12, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 2, "x": -10.07, "y": 12.11, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 2.7333, "x": 2.31, "y": -7.61, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "x": -13.21, "y": 17.12, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 4, "x": -10.07, "y": 12.11, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 4.7333, "x": 2.31, "y": -7.61, "curve": 0.25, "c3": 0.75}, {"time": 5.7333, "x": -13.21, "y": 17.12, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 6, "x": -10.07, "y": 12.11, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 6.7333, "x": 2.31, "y": -7.61, "curve": 0.25, "c3": 0.75}, {"time": 7.7333, "x": -13.21, "y": 17.12, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 8, "x": -10.07, "y": 12.11}]}, "qbai5": {"translate": [{"x": -17.76, "y": 26.02, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 0.9333, "x": 6.8, "y": -12.42, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "x": -18.33, "y": 26.92, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 2, "x": -17.76, "y": 26.02, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 2.9333, "x": 6.8, "y": -12.42, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "x": -18.33, "y": 26.92, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 4, "x": -17.76, "y": 26.02, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 4.9333, "x": 6.8, "y": -12.42, "curve": 0.25, "c3": 0.75}, {"time": 5.9333, "x": -18.33, "y": 26.92, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 6, "x": -17.76, "y": 26.02, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 6.9333, "x": 6.8, "y": -12.42, "curve": 0.25, "c3": 0.75}, {"time": 7.9333, "x": -18.33, "y": 26.92, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 8, "x": -17.76, "y": 26.02}]}, "qbai6": {"rotate": [{"angle": 8.62, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "angle": 17.49, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -6.63, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "angle": 8.62, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.4, "angle": 17.49, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "angle": -6.63, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "angle": 8.62, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.4, "angle": 17.49, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "angle": -6.63, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6, "angle": 8.62, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.4, "angle": 17.49, "curve": 0.25, "c3": 0.75}, {"time": 7.4, "angle": -6.63, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8, "angle": 8.62}], "translate": [{"x": -24.46, "y": 39.6, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "x": -26.9, "y": 43.88, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": 8.22, "y": -17.76, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2, "x": -24.46, "y": 39.6, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 2.1333, "x": -26.9, "y": 43.88, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "x": 8.22, "y": -17.76, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 4, "x": -24.46, "y": 39.6, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 4.1333, "x": -26.9, "y": 43.88, "curve": 0.25, "c3": 0.75}, {"time": 5.1333, "x": 8.22, "y": -17.76, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 6, "x": -24.46, "y": 39.6, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 6.1333, "x": -26.9, "y": 43.88, "curve": 0.25, "c3": 0.75}, {"time": 7.1333, "x": 8.22, "y": -17.76, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 8, "x": -24.46, "y": 39.6}]}, "qbai7": {"rotate": [{"time": 5}, {"time": 5.0333, "angle": -3.92}, {"time": 5.1, "angle": 2.83}, {"time": 5.3333, "angle": 8.78}, {"time": 6}], "translate": [{"x": -5.56, "y": -4.7, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": 2.93, "y": 2.19, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -8.93, "y": -7.43, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -5.56, "y": -4.7, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "x": 2.93, "y": 2.19, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": -8.93, "y": -7.43, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": -5.56, "y": -4.7, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.6667, "x": 2.93, "y": 2.19, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": -8.93, "y": -7.43, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "x": -5.56, "y": -4.7, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 6.6667, "x": 2.93, "y": 2.19, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "x": -8.93, "y": -7.43, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "x": -5.56, "y": -4.7}]}, "qbai8": {"translate": [{"x": -3.88, "y": 7.85, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "x": 0.74, "y": -3.59, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": -4.37, "y": 9.07, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "x": -3.88, "y": 7.85, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "x": 0.74, "y": -3.59, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": -4.37, "y": 9.07, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "x": -3.88, "y": 7.85, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 4.8333, "x": 0.74, "y": -3.59, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "x": -4.37, "y": 9.07, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 6, "x": -3.88, "y": 7.85, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 6.8333, "x": 0.74, "y": -3.59, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": -4.37, "y": 9.07, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 8, "x": -3.88, "y": 7.85}]}, "qbai9": {"translate": [{"x": -13.21, "y": 17.12, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 2.31, "y": -7.61, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -13.21, "y": 17.12, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 2.31, "y": -7.61, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -13.21, "y": 17.12, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 2.31, "y": -7.61, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": -13.21, "y": 17.12, "curve": 0.25, "c3": 0.75}, {"time": 7, "x": 2.31, "y": -7.61, "curve": 0.25, "c3": 0.75}, {"time": 8, "x": -13.21, "y": 17.12}]}, "qbai10": {"translate": [{"x": -15.91, "y": 23.12, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "x": -18.33, "y": 26.92, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 6.8, "y": -12.42, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "x": -15.91, "y": 23.12, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "x": -18.33, "y": 26.92, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": 6.8, "y": -12.42, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "x": -15.91, "y": 23.12, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "x": -18.33, "y": 26.92, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": 6.8, "y": -12.42, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 6, "x": -15.91, "y": 23.12, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 6.1667, "x": -18.33, "y": 26.92, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "x": 6.8, "y": -12.42, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 8, "x": -15.91, "y": 23.12}]}, "qbai11": {"rotate": [{"angle": 0.3, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.5667, "angle": 14.93, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": -9.95, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 2, "angle": 0.3, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 2.5667, "angle": 14.93, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": -9.95, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 4, "angle": 0.3, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 4.5667, "angle": 14.93, "curve": 0.25, "c3": 0.75}, {"time": 5.5667, "angle": -9.95, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 6, "angle": 0.3, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 6.5667, "angle": 14.93, "curve": 0.25, "c3": 0.75}, {"time": 7.5667, "angle": -9.95, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 8, "angle": 0.3}], "translate": [{"x": -16.93, "y": 26.39, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": -26.9, "y": 43.88, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 8.22, "y": -17.76, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -16.93, "y": 26.39, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "x": -26.9, "y": 43.88, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 8.22, "y": -17.76, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": -16.93, "y": 26.39, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "x": -26.9, "y": 43.88, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 8.22, "y": -17.76, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "x": -16.93, "y": 26.39, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.3333, "x": -26.9, "y": 43.88, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": 8.22, "y": -17.76, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "x": -16.93, "y": 26.39}]}, "qbai25": {"rotate": [{"time": 5}, {"time": 5.1, "angle": 5.02, "curve": 0.34, "c2": 0.35, "c3": 0.677, "c4": 0.7}, {"time": 5.4, "angle": 7.46, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 6}], "translate": [{"x": -3.04, "y": -1.84, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": -31.54, "y": -19.05, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": -31.54, "y": -19.05, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "x": -3.04, "y": -1.84, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": -31.54, "y": -19.05, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "x": -31.54, "y": -19.05, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 8, "x": -3.04, "y": -1.84}]}, "qbai26": {"translate": [{"x": -1.59, "y": 1.44, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "x": 3.86, "y": -10.16, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "x": -10.96, "y": 21.37, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "x": -1.59, "y": 1.44, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.4, "x": 3.86, "y": -10.16, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "x": -10.96, "y": 21.37, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "x": -1.59, "y": 1.44, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.4, "x": 3.86, "y": -10.16, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "x": -10.96, "y": 21.37, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6, "x": -1.59, "y": 1.44, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.4, "x": 3.86, "y": -10.16, "curve": 0.25, "c3": 0.75}, {"time": 7.4, "x": -10.96, "y": 21.37, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8, "x": -1.59, "y": 1.44}]}, "qbai27": {"rotate": [{"angle": 6.2, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": -11.2, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 8.06, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": 6.2, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": -11.2, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 8.06, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": 6.2, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 4.8333, "angle": -11.2, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "angle": 8.06, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 6, "angle": 6.2, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 6.8333, "angle": -11.2, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "angle": 8.06, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 8, "angle": 6.2}], "translate": [{"x": -8.38, "y": 19.35, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": 9.34, "y": -20.85, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -15.4, "y": 35.28, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -8.38, "y": 19.35, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "x": 9.34, "y": -20.85, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": -15.4, "y": 35.28, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": -8.38, "y": 19.35, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.6667, "x": 9.34, "y": -20.85, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": -15.4, "y": 35.28, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "x": -8.38, "y": 19.35, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 6.6667, "x": 9.34, "y": -20.85, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "x": -15.4, "y": 35.28, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "x": -8.38, "y": 19.35}]}, "qbai28": {"rotate": [{"angle": 6.21, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 8.31, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -13.39, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 6.21, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": 8.31, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -13.39, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": 6.21, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "angle": 8.31, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "angle": -13.39, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 6, "angle": 6.21, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 6.1667, "angle": 8.31, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "angle": -13.39, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 8, "angle": 6.21}], "translate": [{"x": -11.02, "y": 58.69, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.9, "x": 9.46, "y": -31.58, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "x": -11.93, "y": 62.7, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2, "x": -11.02, "y": 58.69, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 2.9, "x": 9.46, "y": -31.58, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "x": -11.93, "y": 62.7, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 4, "x": -11.02, "y": 58.69, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 4.9, "x": 9.46, "y": -31.58, "curve": 0.25, "c3": 0.75}, {"time": 5.9, "x": -11.93, "y": 62.7, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 6, "x": -11.02, "y": 58.69, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 6.9, "x": 9.46, "y": -31.58, "curve": 0.25, "c3": 0.75}, {"time": 7.9, "x": -11.93, "y": 62.7, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 8, "x": -11.02, "y": 58.69}]}, "yao5": {"rotate": [{"angle": 0.31, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333}, {"time": 1.3333, "angle": 1.11, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 0.31, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333}, {"time": 3.3333, "angle": 1.11, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 0.31, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333}, {"time": 5.3333, "angle": 1.11, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "angle": 0.31, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.3333}, {"time": 7.3333, "angle": 1.11, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "angle": 0.31}], "translate": [{"x": -1.68, "y": -0.3, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333}, {"time": 1.3333, "x": -5.92, "y": -1.06, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -1.68, "y": -0.3, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333}, {"time": 3.3333, "x": -5.92, "y": -1.06, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": -1.68, "y": -0.3, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333}, {"time": 5.3333, "x": -5.92, "y": -1.06, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "x": -1.68, "y": -0.3, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.3333}, {"time": 7.3333, "x": -5.92, "y": -1.06, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "x": -1.68, "y": -0.3}]}, "qbai": {"rotate": [{"angle": -0.44, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333}, {"time": 1.3333, "angle": -1.56, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -0.44, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333}, {"time": 3.3333, "angle": -1.56, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -0.44, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333}, {"time": 5.3333, "angle": -1.56, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "angle": -0.44, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.3333}, {"time": 7.3333, "angle": -1.56, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "angle": -0.44}], "translate": [{"x": -0.67, "y": -0.36, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333}, {"time": 1.3333, "x": -2.37, "y": -1.27, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -0.67, "y": -0.36, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333}, {"time": 3.3333, "x": -2.37, "y": -1.27, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": -0.67, "y": -0.36, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333}, {"time": 5.3333, "x": -2.37, "y": -1.27, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "x": -0.67, "y": -0.36, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.3333}, {"time": 7.3333, "x": -2.37, "y": -1.27, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "x": -0.67, "y": -0.36}]}, "hup2": {"translate": [{"x": -0.17, "y": 0.14, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.3333}, {"time": 2.3333, "x": -1.75, "y": 1.41, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "x": -0.17, "y": 0.14, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.3333}, {"time": 6.3333, "x": -1.75, "y": 1.41, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 8, "x": -0.17, "y": 0.14}]}, "hup3": {"translate": [{"x": 0.79, "y": -0.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6667}, {"time": 2.6667, "x": 2.78, "y": -3.08, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": 0.79, "y": -0.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.6667}, {"time": 6.6667, "x": 2.78, "y": -3.08, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "x": 0.79, "y": -0.87}]}, "hup4": {"translate": [{"x": 2.48, "y": -2.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1}, {"time": 3, "x": 4.95, "y": -5.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 2.48, "y": -2.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5}, {"time": 7, "x": 4.95, "y": -5.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": 2.48, "y": -2.65}]}, "dajt2": {"translate": [{"x": -17.85, "y": -2.19, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.3333}, {"time": 3.3333, "x": -24.92, "y": -3.06, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": -17.85, "y": -2.19, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 5.3333}, {"time": 7.3333, "x": -24.92, "y": -3.06, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "x": -17.85, "y": -2.19}]}, "dajt3": {"translate": [{"x": -3.67, "y": -2.64, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6667}, {"time": 2.6667, "x": -12.94, "y": -9.31, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": -3.67, "y": -2.64, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.6667}, {"time": 6.6667, "x": -12.94, "y": -9.31, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "x": -3.67, "y": -2.64}]}, "dajt4": {"translate": [{"x": -15.05, "y": 3.28, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -15.05, "y": 3.28, "curve": 0.25, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 8, "x": -15.05, "y": 3.28}]}, "hup": {"translate": [{"x": -5.13, "y": -0.02, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6667}, {"time": 2.6667, "x": -18.06, "y": -0.07, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": -5.13, "y": -0.02, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.6667}, {"time": 6.6667, "x": -18.06, "y": -0.07, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "x": -5.13, "y": -0.02}]}, "hup5": {"translate": [{"x": 7.12, "y": 0.45, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1, "x": 14.23, "y": 0.91, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 7.12, "y": 0.45, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5, "x": 14.23, "y": 0.91, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": 7.12, "y": 0.45}]}, "hup6": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.196, "y": 1.196, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": 1.196, "y": 1.196, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "dabi1": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 2.88, "y": -3.11, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 2.88, "y": -3.11, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4.6667, "x": 2.08, "y": -2.25, "curve": 0.351, "c2": 0.4, "c3": 0.689, "c4": 0.75}, {"time": 4.8333, "x": 2.6, "y": -2.81, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 5, "x": 2.88, "y": -3.11, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 5.0333, "x": -13.02, "y": -10.24, "curve": 0.305, "c2": 0.18, "c3": 0.642, "c4": 0.53}, {"time": 5.1, "x": 2.51, "y": -2.7, "curve": 0.311, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 5.3, "x": -0.89, "y": -5.34, "curve": 0.332, "c2": 0.33, "c3": 0.676, "c4": 0.7}, {"time": 5.6667, "x": 0.82, "y": -0.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7, "x": 2.88, "y": -3.11, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "shoubi1": {"rotate": [{"angle": 0.96, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 3.37, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 0.96, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 3.37, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 0.96, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4.6667, "angle": 0.96, "curve": 0.325, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 4.8333, "angle": 1.68, "curve": 0.337, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 5, "angle": 2.42, "curve": 0.339, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 5.0333, "angle": -5.08, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 5.1, "angle": 3.15, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 5.6667, "angle": 2.41, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 6, "angle": 0.96, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": 3.37, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "angle": 0.96}], "translate": [{"x": 0.36, "y": 0.12, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.27, "y": 0.42, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.36, "y": 0.12, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 1.27, "y": 0.42, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": 0.36, "y": 0.12, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4.6667, "x": 0.36, "y": 0.12, "curve": 0.325, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 4.8333, "x": 3.3, "y": 11.6, "curve": 0.337, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 5, "x": 1.44, "y": -6.96, "curve": 0.339, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 5.0333, "x": 3.18, "y": -7.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 5.1, "x": 1.19, "y": 0.39, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 5.6667, "x": 0.91, "y": 0.3, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 6, "x": 0.36, "y": 0.12, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": 1.27, "y": 0.42, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "x": 0.36, "y": 0.12}]}, "shoubi1_3": {"rotate": [{"time": 0.3667}, {"time": 0.6333, "angle": 28.23}, {"time": 0.7333, "curve": "stepped"}, {"time": 0.8667}, {"time": 1.2667, "angle": 28.23}, {"time": 1.3667}, {"time": 1.5333, "angle": 28.23}, {"time": 1.6333, "curve": "stepped"}, {"time": 2.3667}, {"time": 2.6333, "angle": 28.23}, {"time": 2.7333, "curve": "stepped"}, {"time": 2.8667}, {"time": 3.2667, "angle": 28.23}, {"time": 3.3667}, {"time": 3.5333, "angle": 28.23}, {"time": 3.6333, "curve": "stepped"}, {"time": 4.8333}, {"time": 5, "angle": 24.6}, {"time": 5.0333, "angle": 23}, {"time": 5.1, "curve": "stepped"}, {"time": 6.3667}, {"time": 6.6333, "angle": 28.23}, {"time": 6.7333, "curve": "stepped"}, {"time": 6.8667}, {"time": 7.2667, "angle": 28.23}, {"time": 7.3667}, {"time": 7.5333, "angle": 28.23}, {"time": 7.6333}], "translate": [{"time": 4.8333}, {"time": 5, "x": -0.43, "y": -6.16}, {"time": 5.0333}]}, "dabi2": {"rotate": [{"angle": 2.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 5.01, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 2.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 5.01, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 2.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.5, "angle": -6.68, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4.6667, "angle": -5.22, "curve": 0.311, "c2": 0.25, "c3": 0.649, "c4": 0.6}, {"time": 4.8333, "angle": -2.41, "curve": 0.325, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 5, "angle": 1.6, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 5.0333, "angle": -1, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 5.1, "angle": 2.96, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 5.3, "angle": -2.49, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.6667, "angle": 1.45, "curve": 0.299, "c2": 0.22, "c3": 0.649, "c4": 0.61}, {"time": 6, "angle": 2.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.5, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "angle": 5.01, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": 2.5}]}, "shou2": {"rotate": [{"angle": 2.36, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": -0.65, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 2.68, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": 2.36, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": -0.65, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 2.68, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": 2.36, "curve": 0.299, "c2": 0.22, "c3": 0.701, "c4": 0.78}, {"time": 4.6667, "angle": -0.32, "curve": 0.371, "c2": 0.63, "c3": 0.71}, {"time": 4.8333, "angle": -1.42, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 5, "angle": 0.75, "curve": 0.328, "c2": 0.31, "c3": 0.661, "c4": 0.64}, {"time": 5.0333, "angle": 5.94, "curve": 0.324, "c2": 0.3, "c3": 0.658, "c4": 0.63}, {"time": 5.1, "angle": 0.02, "curve": 0.318, "c2": 0.28, "c3": 0.658, "c4": 0.64}, {"time": 5.3, "angle": 0.85, "curve": 0.349, "c2": 0.38, "c3": 0.703, "c4": 0.79}, {"time": 5.6667, "angle": 2.84, "curve": 0.371, "c2": 0.63, "c3": 0.71}, {"time": 6, "angle": 2.36, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 6.8333, "angle": -0.65, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "angle": 2.68, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 8, "angle": 2.36}], "translate": [{"x": 3.71, "y": -3.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "x": 7.42, "y": -7.96, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 3.71, "y": -3.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "x": 7.42, "y": -7.96, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 3.71, "y": -3.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.5, "x": 7.42, "y": -7.96, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4.6667, "x": 6.71, "y": -7.2, "curve": 0.311, "c2": 0.25, "c3": 0.649, "c4": 0.6}, {"time": 4.8333, "x": 8.08, "y": -8.38, "curve": 0.325, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 5, "x": 3.71, "y": -3.98, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 5.0333, "x": 3.38, "y": -3.63, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 5.3, "x": 9.13, "y": -14.4, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.6667, "x": 7.58, "y": -10.37, "curve": 0.299, "c2": 0.22, "c3": 0.649, "c4": 0.61}, {"time": 6, "x": 3.71, "y": -3.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.5, "x": 7.42, "y": -7.96, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": 3.71, "y": -3.98}]}, "shouz2": {"rotate": [{"angle": 3.15, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 4.39, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -8.38, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 3.15, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": 4.39, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -8.38, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": 3.15, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "angle": 4.39, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.6667, "angle": -2.07, "curve": 0.338, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 4.8333, "angle": -5.51, "curve": 0.351, "c2": 0.4, "c3": 0.689, "c4": 0.75}, {"time": 5, "angle": -13.5, "curve": 0.339, "c2": 0.36, "c3": 0.673, "c4": 0.7}, {"time": 5.0333, "angle": -9.32, "curve": 0.348, "c2": 0.43, "c3": 0.682, "c4": 0.77}, {"time": 5.1, "angle": -1.47, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 5.3, "angle": -8.6, "curve": 0.291, "c2": 0.19, "c3": 0.645, "c4": 0.59}, {"time": 5.6667, "angle": -3.15, "curve": 0.351, "c2": 0.39, "c3": 0.701, "c4": 0.78}, {"time": 6, "angle": 3.15, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 6.1667, "angle": 4.39, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "angle": -8.38, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 8, "angle": 3.15}]}, "wu2": {"translate": [{"x": 81.68, "y": 27.02}, {"time": 6.6667, "x": 8.61}, {"time": 8, "x": 81.68, "y": 27.02}]}, "wu3": {"translate": [{}, {"time": 6.6667, "x": -122.24, "y": -132.28}, {"time": 8}], "scale": [{}, {"time": 6.6667, "x": 1.179, "y": 1.179}, {"time": 8}]}, "wu4": {"translate": [{}, {"time": 6.6667, "x": 73.25, "y": -211.78}, {"time": 8}]}, "wu5": {"translate": [{}, {"time": 6.6667, "x": -177.66, "y": -191.48}, {"time": 8}]}, "wu6": {"rotate": [{}, {"time": 3.4667, "angle": 12.25}, {"time": 6.6667}], "translate": [{}, {"time": 6.6667, "x": 78.77, "y": -292.77}, {"time": 8}]}, "hup7": {"translate": [{"x": -15.56}, {"time": 1, "x": -86.25}, {"time": 4, "x": -15.56}, {"time": 5, "x": -86.25}, {"time": 8, "x": -15.56}]}, "hup8": {"translate": [{"x": -2.56, "y": 85.56}, {"time": 1}, {"time": 4, "x": -2.56, "y": 85.56}, {"time": 5}, {"time": 8, "x": -2.56, "y": 85.56}]}, "hup9": {"translate": [{"x": 43.14, "y": 105.5}, {"time": 1}, {"time": 4, "x": 43.14, "y": 105.5}, {"time": 5}, {"time": 8, "x": 43.14, "y": 105.5}]}, "hup10": {"translate": [{"x": 6.95, "y": 163.97}, {"time": 1}, {"time": 4, "x": 6.95, "y": 163.97}, {"time": 5}, {"time": 8, "x": 6.95, "y": 163.97}]}, "hup11": {"translate": [{"x": -6.13, "y": 116.54}, {"time": 1}, {"time": 4, "x": -6.13, "y": 116.54}, {"time": 5}, {"time": 8, "x": -6.13, "y": 116.54}]}, "hup12": {"translate": [{"x": -15.44, "y": 293.29}, {"time": 1}, {"time": 4, "x": -15.44, "y": 293.29}, {"time": 5}, {"time": 8, "x": -15.44, "y": 293.29}]}, "wu7": {"translate": [{"x": -40.71}, {"time": 1.6667, "x": -26.81}, {"time": 2.6667, "x": -51.82}, {"time": 4, "x": -40.71}, {"time": 5.6667, "x": -26.81}, {"time": 6.6667, "x": -51.82}, {"time": 8, "x": -40.71}]}, "wu8": {"translate": [{"x": 16.72, "y": 23.83}, {"time": 1.6667, "x": 37.61, "y": 53.62}, {"time": 2.6667}, {"time": 4, "x": 16.72, "y": 23.83}, {"time": 5.6667, "x": 37.61, "y": 53.62}, {"time": 6.6667}, {"time": 8, "x": 16.72, "y": 23.83}]}, "wu9": {"translate": [{"x": 36.94, "y": 49.26}, {"time": 1.6667, "x": 83.12, "y": 110.83}, {"time": 2.6667}, {"time": 4, "x": 36.94, "y": 49.26}, {"time": 5.6667, "x": 83.12, "y": 110.83}, {"time": 6.6667}, {"time": 8, "x": 36.94, "y": 49.26}]}, "wu10": {"translate": [{"x": -27.94, "y": 68.62}, {"time": 1.6667, "x": -62.86, "y": 154.39}, {"time": 2.6667}, {"time": 4, "x": -27.94, "y": 68.62}, {"time": 5.6667, "x": -62.86, "y": 154.39}, {"time": 6.6667}, {"time": 8, "x": -27.94, "y": 68.62}]}, "wu11": {"translate": [{"x": 21.55, "y": 84.15}, {"time": 1.6667, "x": 48.49, "y": 189.34}, {"time": 2.6667}, {"time": 4, "x": 21.55, "y": 84.15}, {"time": 5.6667, "x": 48.49, "y": 189.34}, {"time": 6.6667}, {"time": 8, "x": 21.55, "y": 84.15}]}, "wu12": {"translate": [{"x": -24.63, "y": 49.26}, {"time": 1.6667, "x": -55.42, "y": 110.83}, {"time": 2.6667}, {"time": 4, "x": -24.63, "y": 49.26}, {"time": 5.6667, "x": -55.42, "y": 110.83}, {"time": 6.6667}, {"time": 8, "x": -24.63, "y": 49.26}]}, "wu13": {"translate": [{"x": 10.26, "y": 112.88}, {"time": 1.6667, "x": 23.09, "y": 253.99}, {"time": 2.6667}, {"time": 4, "x": 10.26, "y": 112.88}, {"time": 5.6667, "x": 23.09, "y": 253.99}, {"time": 6.6667}, {"time": 8, "x": 10.26, "y": 112.88}]}, "wu14": {"translate": [{"x": 47.34}, {"time": 0.5, "x": 78.45}, {"time": 3.5, "x": 16.23}, {"time": 4, "x": 47.34}, {"time": 4.5, "x": 78.45}, {"time": 7.5, "x": 16.23}, {"time": 8, "x": 47.34}]}, "wu15": {"translate": [{"x": -18.35, "y": -41.49}, {"time": 0.5}, {"time": 3.1667, "x": -48.93, "y": -110.63}, {"time": 4, "x": -18.35, "y": -41.49}, {"time": 4.5}, {"time": 7.1667, "x": -48.93, "y": -110.63}, {"time": 8, "x": -18.35, "y": -41.49}]}, "wu16": {"translate": [{"x": -46.13, "y": -48.89}, {"time": 1}, {"time": 4, "x": -46.13, "y": -48.89}, {"time": 5}, {"time": 8, "x": -46.13, "y": -48.89}], "scale": [{"x": 1.214, "y": 1.214}, {"time": 1}, {"time": 4, "x": 1.214, "y": 1.214}, {"time": 5}, {"time": 8, "x": 1.214, "y": 1.214}]}, "wu17": {"translate": [{"x": 36.55, "y": -77.85}, {"time": 1}, {"time": 4, "x": 36.55, "y": -77.85}, {"time": 5}, {"time": 8, "x": 36.55, "y": -77.85}]}, "wu18": {"translate": [{"x": -89.77, "y": -260.27}, {"time": 1}, {"time": 4, "x": -89.77, "y": -260.27}, {"time": 5}, {"time": 8, "x": -89.77, "y": -260.27}]}, "wu19": {"translate": [{"x": 17.54, "y": -48.93}, {"time": 0.5}, {"time": 3.1667, "x": 46.78, "y": -130.48}, {"time": 4, "x": 17.54, "y": -48.93}, {"time": 4.5}, {"time": 7.1667, "x": 46.78, "y": -130.48}, {"time": 8, "x": 17.54, "y": -48.93}]}, "wu20": {"translate": [{"x": -3.57, "y": -46.96}, {"time": 0.5}, {"time": 3.1667, "x": -9.53, "y": -125.22}, {"time": 4, "x": -3.57, "y": -46.96}, {"time": 4.5}, {"time": 7.1667, "x": -9.53, "y": -125.22}, {"time": 8, "x": -3.57, "y": -46.96}]}, "wu21": {"translate": [{"x": 39.57, "y": -72.29}, {"time": 0.5}, {"time": 3.1667, "x": 105.52, "y": -192.79}, {"time": 4, "x": 39.57, "y": -72.29}, {"time": 4.5}, {"time": 7.1667, "x": 105.52, "y": -192.79}, {"time": 8, "x": 39.57, "y": -72.29}]}, "bone2": {"translate": [{"x": -35.64, "y": -2.96}, {"time": 1.3, "x": -15.07, "y": -25.16}, {"time": 1.3333, "x": -77.84, "y": 42.57}, {"time": 4, "x": -35.64, "y": -2.96}, {"time": 5.3, "x": -15.07, "y": -25.16}, {"time": 5.3333, "x": -77.84, "y": 42.57}, {"time": 8, "x": -35.64, "y": -2.96}]}, "wu22": {"translate": [{"x": 53.73, "y": 5.01}, {"time": 1.9667, "x": 62.15, "y": 27.96}, {"time": 2, "x": 28.06, "y": -65}, {"time": 8, "x": 53.73, "y": 5.01}]}, "wu23": {"translate": [{"x": -64.88, "y": 77.31}, {"time": 1.9667, "x": -79.05, "y": 139.01}, {"time": 2, "x": -21.65, "y": -110.93}, {"time": 8, "x": -64.88, "y": 77.31}]}, "bone3": {"translate": [{"x": 7.31, "y": -8.18}, {"time": 1.9667}, {"time": 2, "x": 29.6, "y": -33.12}, {"time": 8, "x": 7.31, "y": -8.18}], "scale": [{"x": 1.439, "y": 1.439}, {"time": 1.9667, "x": 1.583, "y": 1.583}, {"time": 2}, {"time": 8, "x": 1.439, "y": 1.439}]}, "bone4": {"translate": [{"x": 4.99, "y": 36.62}, {"time": 1.9667, "x": 21.42, "y": 65.11}, {"time": 2, "x": -45.14, "y": -50.3}, {"time": 8, "x": 4.99, "y": 36.62}]}, "bone5": {"translate": [{"x": 6.12, "y": 52.39}, {"time": 1.9667, "x": -2.86, "y": 133.04}, {"time": 2, "x": 33.52, "y": -193.65}, {"time": 8, "x": 6.12, "y": 52.39}]}, "bone6": {"translate": [{"x": 33.19, "y": -63.16}, {"time": 1.3, "x": 49.37, "y": -93.96}, {"time": 1.3333}, {"time": 4, "x": 33.19, "y": -63.16}, {"time": 5.3, "x": 49.37, "y": -93.96}, {"time": 5.3333}, {"time": 8, "x": 33.19, "y": -63.16}]}, "bone7": {"translate": [{"x": 9.57, "y": -72.09}, {"time": 1.3, "x": 14.24, "y": -107.24}, {"time": 1.3333}, {"time": 4, "x": 9.57, "y": -72.09}, {"time": 5.3, "x": 14.24, "y": -107.24}, {"time": 5.3333}, {"time": 8, "x": 9.57, "y": -72.09}]}, "bone8": {"translate": [{"x": 10.09, "y": -126.82}, {"time": 1.3, "x": 15.01, "y": -188.65}, {"time": 1.3333}, {"time": 4, "x": 10.09, "y": -126.82}, {"time": 5.3, "x": 15.01, "y": -188.65}, {"time": 5.3333}, {"time": 8, "x": 10.09, "y": -126.82}]}, "bone9": {"translate": [{"x": -10.55, "y": -143.62}, {"time": 1.3, "x": -15.7, "y": -213.64}, {"time": 1.3333}, {"time": 4, "x": -10.55, "y": -143.62}, {"time": 5.3, "x": -15.7, "y": -213.64}, {"time": 5.3333}, {"time": 8, "x": -10.55, "y": -143.62}]}, "bone10": {"rotate": [{"time": 4.6667}, {"time": 4.8333, "angle": -1.49}, {"time": 5, "angle": -10.7}, {"time": 5.0333}], "translate": [{"time": 4.6667}, {"time": 4.8333, "x": -6.43, "y": 38.59}, {"time": 5, "y": 57.89}, {"time": 5.0333}]}, "qbai31": {"rotate": [{"angle": 3.13, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -7.07, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 7.17, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 3.13, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": -7.07, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 7.17, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 3.13, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.6667, "angle": -7.07, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": 7.17, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "angle": 3.13, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 6.6667, "angle": -7.07, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": 7.17, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "angle": 3.13}]}, "tou20": {"translate": [{"x": -8.18, "y": 0.05, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 9.95, "y": -0.06, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -8.18, "y": 0.05, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": 9.95, "y": -0.06, "curve": 0.25, "c3": 0.75}, {"time": 8, "x": -8.18, "y": 0.05}]}, "qbai12": {"translate": [{"x": 14.27, "y": 14.22, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": -15.62, "y": -28.39, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 26.11, "y": 31.1, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": -15.62, "y": -28.39, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 26.11, "y": 31.1, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": 14.27, "y": 14.22, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.6667, "x": -15.62, "y": -28.39, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": 26.11, "y": 31.1, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": -15.62, "y": -28.39, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "x": 26.11, "y": 31.1, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "x": 14.27, "y": 14.22}]}, "qbai13": {"translate": [{"x": -0.4, "y": 11.48, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": -2.34, "y": 27.63, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 4.51, "y": -29.29, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": -2.34, "y": 27.63, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 4.51, "y": -29.29, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": -0.4, "y": 11.48, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "x": -2.34, "y": 27.63, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 4.51, "y": -29.29, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "x": -2.34, "y": 27.63, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": 4.51, "y": -29.29, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "x": -0.4, "y": 11.48}]}, "bone11": {"translate": [{}, {"time": 1, "x": 49.5}, {"time": 4}, {"time": 5, "x": 49.5}, {"time": 8}], "scale": [{}, {"time": 1, "x": 0.931, "y": 0.931}, {"time": 4}, {"time": 5, "x": 0.931, "y": 0.931}, {"time": 8}]}, "bone12": {"translate": [{"x": -12.53, "y": -10.25}, {"time": 1.9667}, {"time": 2, "x": -50.75, "y": -41.53}, {"time": 8, "x": -12.53, "y": -10.25}], "scale": [{"x": 1.17, "y": 1.17}, {"time": 1.9667, "x": 1.226, "y": 1.226}, {"time": 2}, {"time": 8, "x": 1.17, "y": 1.17}]}}, "deform": {"default": {"yao": {"yao": [{"offset": 54, "vertices": [-1.69577, -2.43774, 2.16005, 2.03769, -0.62004, -4.27136, 1.48358, 4.0531, -0.33387, -2.30002, 0.79886, 2.18246, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.33387, -2.30002, 2.29141, -0.38815, 0.79886, 2.18246, -0.62004, -4.27136, 4.25543, -0.72085, 4.24191, 0.79875, 1.48358, 4.0531, -1.69577, -2.43774, 2.85641, -0.81352, 2.16005, 2.03769], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "offset": 54, "vertices": [-3.39155, -4.87549, 4.3201, 4.07538, -1.24007, -8.54272, 2.96716, 8.1062, -0.66774, -4.60004, 1.59772, 4.36493, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.66774, -4.60004, 4.58282, -0.77631, 1.59772, 4.36493, -1.24007, -8.54272, 8.51086, -1.4417, 8.48383, 1.5975, 2.96716, 8.1062, -3.39155, -4.87549, 5.71283, -1.62704, 4.3201, 4.07538], "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "offset": 54, "vertices": [-1.69577, -2.43774, 2.16005, 2.03769, -0.62004, -4.27136, 1.48358, 4.0531, -0.33387, -2.30002, 0.79886, 2.18246, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.33387, -2.30002, 2.29141, -0.38815, 0.79886, 2.18246, -0.62004, -4.27136, 4.25543, -0.72085, 4.24191, 0.79875, 1.48358, 4.0531, -1.69577, -2.43774, 2.85641, -0.81352, 2.16005, 2.03769], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "offset": 54, "vertices": [-3.39155, -4.87549, 4.3201, 4.07538, -1.24007, -8.54272, 2.96716, 8.1062, -0.66774, -4.60004, 1.59772, 4.36493, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.66774, -4.60004, 4.58282, -0.77631, 1.59772, 4.36493, -1.24007, -8.54272, 8.51086, -1.4417, 8.48383, 1.5975, 2.96716, 8.1062, -3.39155, -4.87549, 5.71283, -1.62704, 4.3201, 4.07538], "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "offset": 54, "vertices": [-1.69577, -2.43774, 2.16005, 2.03769, -0.62004, -4.27136, 1.48358, 4.0531, -0.33387, -2.30002, 0.79886, 2.18246, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.33387, -2.30002, 2.29141, -0.38815, 0.79886, 2.18246, -0.62004, -4.27136, 4.25543, -0.72085, 4.24191, 0.79875, 1.48358, 4.0531, -1.69577, -2.43774, 2.85641, -0.81352, 2.16005, 2.03769], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.5, "curve": 0.25, "c3": 0.75}, {"time": 6, "offset": 54, "vertices": [-1.69577, -2.43774, 2.16005, 2.03769, -0.62004, -4.27136, 1.48358, 4.0531, -0.33387, -2.30002, 0.79886, 2.18246, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.33387, -2.30002, 2.29141, -0.38815, 0.79886, 2.18246, -0.62004, -4.27136, 4.25543, -0.72085, 4.24191, 0.79875, 1.48358, 4.0531, -1.69577, -2.43774, 2.85641, -0.81352, 2.16005, 2.03769], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.5, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "offset": 54, "vertices": [-3.39155, -4.87549, 4.3201, 4.07538, -1.24007, -8.54272, 2.96716, 8.1062, -0.66774, -4.60004, 1.59772, 4.36493, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.66774, -4.60004, 4.58282, -0.77631, 1.59772, 4.36493, -1.24007, -8.54272, 8.51086, -1.4417, 8.48383, 1.5975, 2.96716, 8.1062, -3.39155, -4.87549, 5.71283, -1.62704, 4.3201, 4.07538], "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "offset": 54, "vertices": [-1.69577, -2.43774, 2.16005, 2.03769, -0.62004, -4.27136, 1.48358, 4.0531, -0.33387, -2.30002, 0.79886, 2.18246, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.33387, -2.30002, 2.29141, -0.38815, 0.79886, 2.18246, -0.62004, -4.27136, 4.25543, -0.72085, 4.24191, 0.79875, 1.48358, 4.0531, -1.69577, -2.43774, 2.85641, -0.81352, 2.16005, 2.03769]}]}, "yy2": {"yy2": [{"curve": 0.25, "c3": 0.75}, {"time": 2, "offset": 1, "vertices": [-2.47601, 0, -2.47601, 0, -2.47601, 0, -2.47601], "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 6, "offset": 1, "vertices": [-2.47601, 0, -2.47601, 0, -2.47601, 0, -2.47601], "curve": 0.25, "c3": 0.75}, {"time": 8}]}}}}, "animation2": {"slots": {"wu1": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1, "color": "ffffff00"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.8, "color": "ffffffff"}, {"time": 4, "color": "ffffff00", "curve": "stepped"}, {"time": 5, "color": "ffffff00"}, {"time": 6, "color": "ffffffff", "curve": "stepped"}, {"time": 6.8, "color": "ffffffff"}, {"time": 8, "color": "ffffff00"}], "attachment": [{"name": null}]}, "hup": {"attachment": [{"name": null}]}, "wu3": {"color": [{"time": 0.6667, "color": "ffffffff"}, {"time": 1.6667, "color": "ffffff00"}, {"time": 2.6667, "color": "ffffff00"}, {"time": 3.5, "color": "ffffffff", "curve": "stepped"}, {"time": 4.6667, "color": "ffffffff"}, {"time": 5.6667, "color": "ffffff00"}, {"time": 6.6667, "color": "ffffff00"}, {"time": 7.5, "color": "ffffffff"}], "attachment": [{"name": null}]}, "wu4": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1, "color": "ffffff00"}, {"time": 2, "color": "ffffffeb"}, {"time": 2.9333, "color": "ffffffee"}, {"time": 4, "color": "ffffff00", "curve": "stepped"}, {"time": 5.7333, "color": "ffffff00"}, {"time": 6, "color": "ffffffeb"}, {"time": 6.4333, "color": "ffffffff"}, {"time": 6.9333, "color": "ffffffee"}, {"time": 7.5, "color": "ffffffff"}, {"time": 8, "color": "ffffff00"}], "attachment": [{"name": null}]}, "wu2": {"color": [{"color": "ffffff00"}, {"time": 2.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 4.2333, "color": "ffffffff"}, {"time": 6.6667, "color": "ffffff00"}], "attachment": [{"name": null}]}, "pzi": {"attachment": [{"name": null}]}, "wu5": {"color": [{"color": "ffffffbe"}, {"time": 1.9667, "color": "ffffff00", "curve": "stepped"}, {"time": 2, "color": "ffffff00"}, {"time": 3.9667, "color": "ffffffff", "curve": "stepped"}, {"time": 7.3333, "color": "ffffffff"}, {"time": 8, "color": "ffffffbe"}], "attachment": [{"name": null}]}, "hup2": {"attachment": [{"name": null}]}, "3": {"attachment": [{"name": null}]}, "5": {"attachment": [{"name": null}]}, "4": {"attachment": [{"name": null}]}, "dajt": {"attachment": [{"name": null}]}, "wu8": {"color": [{"time": 0.3333, "color": "ffffffff"}, {"time": 1.3, "color": "ffffff00", "curve": "stepped"}, {"time": 1.3333, "color": "ffffff00"}, {"time": 2.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 4.3333, "color": "ffffffff"}, {"time": 5.3, "color": "ffffff00", "curve": "stepped"}, {"time": 5.3333, "color": "ffffff00"}, {"time": 6.3333, "color": "ffffffff"}], "attachment": [{"name": null}]}, "wu6": {"color": [{"color": "ffffffbe"}, {"time": 1.9667, "color": "ffffff00", "curve": "stepped"}, {"time": 2, "color": "ffffff00"}, {"time": 3.9667, "color": "ffffffff", "curve": "stepped"}, {"time": 7.3333, "color": "ffffffff"}, {"time": 8, "color": "ffffffbe"}], "attachment": [{"name": null}]}, "yy2": {"attachment": [{"name": null}]}, "1": {"attachment": [{"name": null}]}, "6": {"attachment": [{"name": null}]}, "zuiy1yzhu2": {"color": [{"color": "ffffffff"}, {"time": 2, "color": "ffffff2e"}, {"time": 4, "color": "ffffffff"}, {"time": 5.8333, "color": "ffffff2e", "curve": "stepped"}, {"time": 6, "color": "ffffff2e"}, {"time": 8, "color": "ffffffff"}]}, "biyan": {"color": [{"time": 3.1667, "color": "ffffff00"}, {"time": 3.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3667, "color": "ffffffff"}, {"time": 3.5, "color": "ffffff00", "curve": "stepped"}, {"time": 7.1667, "color": "ffffff00"}, {"time": 7.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 7.3667, "color": "ffffffff"}, {"time": 7.5, "color": "ffffff00"}], "attachment": [{"time": 3.1667, "name": "biyan"}, {"time": 3.5, "name": null}, {"time": 7.1667, "name": "biyan"}, {"time": 7.5, "name": null}]}, "7": {"attachment": [{"name": null}]}, "2": {"attachment": [{"name": null}]}, "wu7": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00"}, {"time": 1.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1, "color": "ffffffff"}, {"time": 3.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 4.5, "color": "ffffff00"}, {"time": 5.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 6.1, "color": "ffffffff"}, {"time": 7.1667, "color": "ffffff00"}], "attachment": [{"name": null}]}, "yziyanzhu23": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 2, "color": "ffffff00"}, {"time": 2.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.4, "color": "ffffffff"}, {"time": 3.6, "color": "ffffff00", "curve": "stepped"}, {"time": 6, "color": "ffffff00"}, {"time": 6.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 7.4, "color": "ffffffff"}, {"time": 7.6, "color": "ffffff00"}], "attachment": [{"time": 2.0667, "name": "yziyanzhu22"}]}}, "bones": {"6": {"rotate": [{"angle": 0.93, "curve": 0.381, "c2": 0.54, "c3": 0.744}, {"time": 0.8333, "angle": -9.17, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "angle": 16.74, "curve": 0.246, "c3": 0.635, "c4": 0.55}, {"time": 4, "angle": 0.93, "curve": 0.381, "c2": 0.54, "c3": 0.744}, {"time": 4.8333, "angle": -9.17, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": 16.74, "curve": 0.246, "c3": 0.635, "c4": 0.55}, {"time": 8, "angle": 0.93}], "translate": [{"x": -7.1, "y": -4.06, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": -38.47, "y": -21.98, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 4, "x": -7.1, "y": -4.06, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 4.5, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "x": -38.47, "y": -21.98, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 8, "x": -7.1, "y": -4.06}], "scale": [{"x": 1.127, "y": 1.127, "curve": 0.365, "c2": 0.45, "c3": 0.754}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": 1.209, "y": 1.209, "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 4, "x": 1.127, "y": 1.127, "curve": 0.365, "c2": 0.45, "c3": 0.754}, {"time": 5.1667, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "x": 1.209, "y": 1.209, "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 8, "x": 1.127, "y": 1.127}]}, "dahuap": {"rotate": [{"time": 2}, {"time": 2.0667, "angle": -1.41}, {"time": 2.1333, "angle": 0.4}, {"time": 2.2, "angle": -0.31}, {"time": 2.3333, "angle": -0.29}, {"time": 2.4, "angle": 3.31}, {"time": 2.5, "angle": -3.87}, {"time": 2.6, "curve": "stepped"}, {"time": 4.3667}, {"time": 4.4333, "angle": -1.41}, {"time": 4.5, "angle": 0.4}, {"time": 4.5667, "angle": -0.31}, {"time": 4.7, "angle": -0.29}, {"time": 4.7667, "angle": 3.31}, {"time": 4.8667, "angle": -3.87}, {"time": 4.9667}, {"time": 5.1, "angle": -3.87}, {"time": 5.2667}]}, "yao": {"rotate": [{"time": 4.6667, "curve": 0.326, "c2": 0.31, "c3": 0.664, "c4": 0.66}, {"time": 5, "angle": -1.24, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 5.0333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 0.81, "y": 3.71, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 0.81, "y": 3.71, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 4.6667, "x": 0.61, "y": 2.81, "curve": 0.352, "c2": 0.41, "c3": 0.689, "c4": 0.75}, {"time": 4.8333, "x": 1.03, "y": 4.73, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 5, "x": 1.66, "y": 7.64, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 5.0333, "x": -0.12, "y": -0.57, "curve": 0.314, "c2": 0.26, "c3": 0.65, "c4": 0.6}, {"time": 5.1, "x": 0.61, "y": 2.8, "curve": 0.326, "c2": 0.31, "c3": 0.661, "c4": 0.65}, {"time": 5.3, "x": 0.23, "y": 1.06, "curve": 0.332, "c2": 0.33, "c3": 0.676, "c4": 0.7}, {"time": 5.6667, "x": 0.23, "y": 1.06, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7, "x": 0.81, "y": 3.71, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "yao2": {"rotate": [{"angle": -4.11, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -8.22, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": -4.11, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -8.22, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -4.11, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.5, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "angle": -8.22, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6, "angle": -4.11, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.5, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "angle": -8.22, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": -4.11}]}, "yao3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -5.38, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -5.38, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -5.38, "curve": 0.25, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": -5.38, "curve": 0.25, "c3": 0.75}, {"time": 8}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": -14.83, "y": 10.32, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -14.83, "y": 10.32, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": -14.83, "y": 10.32, "curve": 0.25, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7, "x": -14.83, "y": 10.32, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "yao4": {"rotate": [{"angle": 3.21, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": -1.25, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 3.68, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": 3.21, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": -1.25, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 3.68, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": 3.21, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 4.8333, "angle": -1.25, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "angle": 3.68, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 6, "angle": 3.21, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 6.8333, "angle": -1.25, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "angle": 3.68, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 8, "angle": 3.21}], "translate": [{"x": 7.85, "y": 3.29, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333}, {"time": 1.8333, "x": 8.69, "y": 3.64, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "x": 7.85, "y": 3.29, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333}, {"time": 3.8333, "x": 8.69, "y": 3.64, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "x": 7.85, "y": 3.29, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 4.8333}, {"time": 5.8333, "x": 8.69, "y": 3.64, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 6, "x": 7.85, "y": 3.29, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 6.8333}, {"time": 7.8333, "x": 8.69, "y": 3.64, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 8, "x": 7.85, "y": 3.29}]}, "jiao1": {"translate": [{"x": -18.64, "y": 10.33, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "x": -20.63, "y": 11.43, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "x": -18.64, "y": 10.33, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "x": -20.63, "y": 11.43, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "x": -18.64, "y": 10.33, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "x": -20.63, "y": 11.43, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 6, "x": -18.64, "y": 10.33, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 6.1667, "x": -20.63, "y": 11.43, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 8, "x": -18.64, "y": 10.33}]}, "jiao2": {"rotate": [{"angle": 1.33, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 1.47, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 1.33, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": 1.47, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": 1.33, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "angle": 1.47, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 6, "angle": 1.33, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 6.1667, "angle": 1.47, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 8, "angle": 1.33}], "translate": [{"x": 8.07, "y": -13.26, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "x": 8.93, "y": -14.67, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "x": 8.07, "y": -13.26, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "x": 8.93, "y": -14.67, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "x": 8.07, "y": -13.26, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "x": 8.93, "y": -14.67, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 6, "x": 8.07, "y": -13.26, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 6.1667, "x": 8.93, "y": -14.67, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 8, "x": 8.07, "y": -13.26}]}, "jiaoz1": {"rotate": [{"angle": -1.13, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": -1.26, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": -1.13, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": -1.26, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": -1.13, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "angle": -1.26, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 6, "angle": -1.13, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 6.1667, "angle": -1.26, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 8, "angle": -1.13}], "translate": [{"x": -2.66, "y": 2.14, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "x": -2.94, "y": 2.37, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "x": -2.66, "y": 2.14, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "x": -2.94, "y": 2.37, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "x": -2.66, "y": 2.14, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "x": -2.94, "y": 2.37, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 6, "x": -2.66, "y": 2.14, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 6.1667, "x": -2.94, "y": 2.37, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 8, "x": -2.66, "y": 2.14}]}, "qbai22": {"translate": [{"x": -0.04, "y": -0.29, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": -0.21, "y": -1.42, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 2, "x": -0.04, "y": -0.29, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 2.2667, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "x": -0.21, "y": -1.42, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 4, "x": -0.04, "y": -0.29, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 4.2667, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 4.6667, "x": -0.08, "y": -0.58, "curve": 0.332, "c2": 0.33, "c3": 0.669, "c4": 0.67}, {"time": 4.8333, "x": -1.12, "y": -7.69, "curve": 0.344, "c2": 0.37, "c3": 0.681, "c4": 0.71}, {"time": 5, "x": -0.17, "y": -1.16}, {"time": 5.0333, "x": 2.49, "y": 5.19}, {"time": 5.1, "x": -0.21, "y": -1.42}, {"time": 5.3, "x": -0.19, "y": -1.32}, {"time": 5.6667, "x": -0.13, "y": -0.9, "curve": 0.335, "c2": 0.34, "c3": 0.685, "c4": 0.72}, {"time": 6, "x": -0.04, "y": -0.29, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 6.2667, "curve": 0.25, "c3": 0.75}, {"time": 7.2667, "x": -0.21, "y": -1.42, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 8, "x": -0.04, "y": -0.29}]}, "qbai23": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 12.85, "y": -6.22, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 12.85, "y": -6.22, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4.6667, "x": 9.2, "y": -4.46, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 5, "x": 12.85, "y": -6.22, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 5.0333, "x": 20.14, "y": -15.81, "curve": 0.316, "c2": 0.17, "c3": 0.65, "c4": 0.51}, {"time": 5.1, "x": -1.88, "y": -0.9, "curve": 0.296, "c2": 0.18, "c3": 0.638, "c4": 0.55}, {"time": 5.3333, "x": 11.77, "y": -15.14, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7, "x": 12.85, "y": -6.22, "curve": 0.25, "c3": 0.75}, {"time": 8}], "scale": [{"x": 1.396, "y": 1.179, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 1.793, "y": 1.358, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 1.793, "y": 1.358, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 1.396, "y": 1.179, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.5, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "x": 1.793, "y": 1.358, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "x": 1.793, "y": 1.358, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": 1.396, "y": 1.179}]}, "qbai24": {"rotate": [{"time": 5.0333}, {"time": 5.1, "angle": -2.31}, {"time": 5.3}], "translate": [{"x": 1.29, "y": -0.62, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 2.58, "y": -1.25, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 1.29, "y": -0.62, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 2.58, "y": -1.25, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 1.29, "y": -0.62, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.5, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 4.6667, "x": 0.34, "y": -0.16, "curve": 0.315, "c2": 0.27, "c3": 0.652, "c4": 0.61}, {"time": 4.8333, "x": -3.3, "y": -0.18, "curve": 0.327, "c2": 0.31, "c3": 0.664, "c4": 0.66}, {"time": 5, "x": 9.13, "y": -8.24}, {"time": 5.0333, "x": 8.07, "y": -3.91}, {"time": 5.1, "x": -6.99, "y": 3.39}, {"time": 5.3, "x": 1.91, "y": -2.74}, {"time": 6, "x": 1.29, "y": -0.62, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.5, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "x": 2.58, "y": -1.25, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": 1.29, "y": -0.62}]}, "tou": {"rotate": [{"time": 4.6667, "curve": 0.328, "c2": 0.32, "c3": 0.663, "c4": 0.66}, {"time": 4.8333, "angle": -1.24, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 5, "angle": 0.43}, {"time": 5.0333, "angle": 5.08}, {"time": 5.1, "angle": 3.99}, {"time": 5.2, "angle": 1.16}, {"time": 5.4667, "angle": 3.55}, {"time": 6}], "translate": [{"x": 3.09, "y": -1.16, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.7, "x": -4.02, "y": 1.51, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "x": 5.36, "y": -2.02, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2, "x": 3.09, "y": -1.16, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 2.7, "x": -4.02, "y": 1.51, "curve": 0.25, "c3": 0.75}, {"time": 3.7, "x": 5.36, "y": -2.02, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 4, "x": 3.09, "y": -1.16, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 4.6667, "x": -4.02, "y": 1.51, "curve": 0.266, "c3": 0.618, "c4": 0.42}, {"time": 5, "x": 3.17, "y": -1.08}, {"time": 5.0333, "x": 5.03, "y": -9.78}, {"time": 5.1, "x": 2.16, "y": -0.81}, {"time": 5.3, "x": 3.21, "y": -1.21}, {"time": 5.6667, "x": 0.06, "y": -4.38, "curve": 0.343, "c2": 0.66, "c3": 0.677}, {"time": 6, "x": 3.09, "y": -1.16, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 6.7, "x": -4.02, "y": 1.51, "curve": 0.25, "c3": 0.75}, {"time": 7.7, "x": 5.36, "y": -2.02, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 8, "x": 3.09, "y": -1.16}]}, "yanzhu2": {"translate": [{"time": 0.8333}, {"time": 0.9, "x": -3.72, "y": -5.7, "curve": "stepped"}, {"time": 1.6667, "x": -3.72, "y": -5.7}, {"time": 1.7333, "x": -2.51, "y": -8.16, "curve": "stepped"}, {"time": 3.2667, "x": -2.51, "y": -8.16}, {"time": 3.3333, "curve": "stepped"}, {"time": 4.5}, {"time": 4.5667, "x": -6.13, "y": -3.86, "curve": "stepped"}, {"time": 7.2667, "x": -6.13, "y": -3.86}, {"time": 7.3333}], "scale": [{"time": 0.8333}, {"time": 0.9, "x": 1.172, "y": 1.172, "curve": "stepped"}, {"time": 3.2667, "x": 1.172, "y": 1.172}, {"time": 3.3333, "curve": "stepped"}, {"time": 4.5}, {"time": 4.5667, "x": 1.505, "y": 1.505, "curve": "stepped"}, {"time": 7.2667, "x": 1.505, "y": 1.505}, {"time": 7.3333}]}, "yanzhu1": {"translate": [{"time": 0.8333}, {"time": 0.9, "x": -3.72, "y": -5.7, "curve": "stepped"}, {"time": 1.6667, "x": -3.72, "y": -5.7}, {"time": 1.7333, "x": -2.51, "y": -8.16, "curve": "stepped"}, {"time": 3.2667, "x": -2.51, "y": -8.16}, {"time": 3.3333, "curve": "stepped"}, {"time": 4.5}, {"time": 4.5667, "x": -6.13, "y": -3.86, "curve": "stepped"}, {"time": 7.2667, "x": -6.13, "y": -3.86}, {"time": 7.3333}], "scale": [{"time": 0.8333}, {"time": 0.9, "x": 1.172, "y": 1.172, "curve": "stepped"}, {"time": 3.2667, "x": 1.172, "y": 1.172}, {"time": 3.3333, "curve": "stepped"}, {"time": 4.5}, {"time": 4.5667, "x": 1.505, "y": 1.505, "curve": "stepped"}, {"time": 7.2667, "x": 1.505, "y": 1.505}, {"time": 7.3333}]}, "tou2": {"rotate": [{"time": 4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5, "angle": 15.21, "curve": 0.334, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 5.1, "angle": -1.51, "curve": 0.343, "c2": 0.36, "c3": 0.681, "c4": 0.71}, {"time": 5.4667, "angle": -17.69, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 6}], "translate": [{"x": 18.74, "y": -1.62, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "x": 20.74, "y": -1.79, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "x": 18.74, "y": -1.62, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "x": 20.74, "y": -1.79, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "x": 18.74, "y": -1.62, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "x": 20.74, "y": -1.79, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 5.4667, "x": 12.4, "y": -10.97, "curve": 0.328, "c2": 0.32, "c3": 0.705, "c4": 0.79}, {"time": 6, "x": 18.74, "y": -1.62, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 6.1667, "x": 20.74, "y": -1.79, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 8, "x": 18.74, "y": -1.62}]}, "tou3": {"rotate": [{"angle": -2.7, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -33.09, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 9.34, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -2.7, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": -33.09, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 9.34, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -2.7, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.6667, "angle": -33.09, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": 9.34, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "angle": -2.7, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 6.6667, "angle": -33.09, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": 9.34, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "angle": -2.7}], "translate": [{"x": 1.34, "y": -6.88, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.4333, "x": 3.92, "y": -18.9, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": -2.34, "y": 10.29, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2, "x": 1.34, "y": -6.88, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 2.4333, "x": 3.92, "y": -18.9, "curve": 0.25, "c3": 0.75}, {"time": 3.4333, "x": -2.34, "y": 10.29, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 4, "x": 1.34, "y": -6.88, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 4.4333, "x": 3.92, "y": -18.9, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "x": -2.34, "y": 10.29, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 6, "x": 1.34, "y": -6.88, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 6.4333, "x": 3.92, "y": -18.9, "curve": 0.25, "c3": 0.75}, {"time": 7.4333, "x": -2.34, "y": 10.29, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 8, "x": 1.34, "y": -6.88}]}, "tou4": {"rotate": [{"time": 4, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 4.9333, "angle": -8.09, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 5.0667, "angle": 8.92, "curve": 0.334, "c2": 0.34, "c3": 0.667, "c4": 0.67}, {"time": 5.1333, "angle": -7.63, "curve": 0.341, "c2": 0.36, "c3": 0.678, "c4": 0.7}, {"time": 5.4333, "angle": -22.77, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 6}], "translate": [{"x": 16.7, "y": 16.49, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -7.99, "y": -0.95, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 16.7, "y": 16.49, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -7.99, "y": -0.95, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 16.7, "y": 16.49, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": -7.99, "y": -0.95, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": 16.7, "y": 16.49, "curve": 0.25, "c3": 0.75}, {"time": 7, "x": -7.99, "y": -0.95, "curve": 0.25, "c3": 0.75}, {"time": 8, "x": 16.7, "y": 16.49}]}, "tou5": {"rotate": [{"angle": 6.67, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -9.29, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 13, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 6.67, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": -9.29, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 13, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 6.67, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.6667, "angle": -9.29, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": 13, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "angle": 6.67, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 6.6667, "angle": -9.29, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": 13, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "angle": 6.67}], "translate": [{"x": 29.29, "y": -23.36, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": 39.82, "y": -40.88, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 2.69, "y": 20.88, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 29.29, "y": -23.36, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "x": 39.82, "y": -40.88, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 2.69, "y": 20.88, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": 29.29, "y": -23.36, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "x": 39.82, "y": -40.88, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 2.69, "y": 20.88, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "x": 29.29, "y": -23.36, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.3333, "x": 39.82, "y": -40.88, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": 2.69, "y": 20.88, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "x": 29.29, "y": -23.36}]}, "tou6": {"rotate": [{"angle": -6.65, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 6.81, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -6.65, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 6.81, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -6.65, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 4.9, "angle": -2.1, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 5, "angle": 6.81, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 5.0667, "angle": -12.67, "curve": 0.295, "c2": 0.15, "c3": 0.635, "c4": 0.52}, {"time": 5.2667, "angle": 14.82, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 6, "angle": -6.65, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": 6.81, "curve": 0.25, "c3": 0.75}, {"time": 8, "angle": -6.65}], "translate": [{"x": 7.98, "y": -4.76, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": 28.11, "y": -16.78, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 7.98, "y": -4.76, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "x": 28.11, "y": -16.78, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": 7.98, "y": -4.76, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.6667, "x": 28.11, "y": -16.78, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "x": 7.98, "y": -4.76, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 6.6667, "x": 28.11, "y": -16.78, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "x": 7.98, "y": -4.76}]}, "tou7": {"rotate": [{"time": 4, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 5, "angle": 0.32, "curve": 0.331, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 5.0333, "angle": 0.36}, {"time": 5.1, "angle": -2.5}, {"time": 5.3, "angle": -11.1}, {"time": 5.6667, "angle": -11.18, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 6, "angle": -13.04, "curve": 0.334, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 6.3333, "angle": -3.41, "curve": 0.338, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 6.7}], "translate": [{"x": 13.56, "y": 9.66, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "x": 15.01, "y": 10.7, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "x": 13.56, "y": 9.66, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "x": 15.01, "y": 10.7, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "x": 13.56, "y": 9.66, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "x": 15.01, "y": 10.7, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 5, "x": -10.64, "y": -6, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.0333, "x": 18.46, "y": 21.44}, {"time": 5.1, "x": 32.01, "y": 30.72}, {"time": 5.3, "x": 25.17, "y": 22.67}, {"time": 5.6667, "x": 22.33, "y": 19.32, "curve": 0.342, "c2": 0.36, "c3": 0.678, "c4": 0.7}, {"time": 6, "x": 17.94, "y": 14.15, "curve": 0.346, "c2": 0.39, "c3": 0.681, "c4": 0.72}, {"time": 6.3333, "x": 13.56, "y": 9.66, "curve": 0.297, "c2": 0.21, "c3": 0.651, "c4": 0.62}, {"time": 6.7, "x": 6.93, "y": 4.94, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 7.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 8, "x": 13.56, "y": 9.66}]}, "tou8": {"rotate": [{"time": 5, "curve": 0.328, "c2": 0.31, "c3": 0.661, "c4": 0.65}, {"time": 5.0333, "angle": 9.31}, {"time": 5.1, "angle": 17.1}, {"time": 5.3, "angle": 5.18}, {"time": 5.6667, "angle": -14.84, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 6, "angle": -15.35, "curve": 0.334, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 6.3333}], "translate": [{"x": 3.12, "y": -17.37, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": 4.36, "y": -24.25, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 3.12, "y": -17.37, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "x": 4.36, "y": -24.25, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": 3.12, "y": -17.37, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "x": 4.36, "y": -24.25, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 5, "x": 5.79, "y": -12.33, "curve": 0.353, "c2": 0.4, "c3": 0.692, "c4": 0.76}, {"time": 5.0333, "x": 2.1, "y": 9.18}, {"time": 5.1, "x": 2.51, "y": -0.52}, {"time": 5.3, "x": 2.59, "y": -10.69}, {"time": 5.6667, "x": 2.93, "y": -12.9, "curve": 0.331, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 6, "x": 6.12, "y": -7.36, "curve": 0.338, "c2": 0.35, "c3": 0.674, "c4": 0.69}, {"time": 6.3333, "x": 4.36, "y": -24.25, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 6.7, "x": 2.94, "y": -16.36, "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 7.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "x": 3.12, "y": -17.37}]}, "tou9": {"rotate": [{"time": 5, "curve": 0.328, "c2": 0.31, "c3": 0.661, "c4": 0.65}, {"time": 5.0333, "angle": 6.18}, {"time": 5.1, "angle": 44.26}, {"time": 5.3, "angle": 34.42}, {"time": 5.6667, "angle": -0.36, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 6, "angle": -19.91, "curve": 0.334, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 6.3333, "angle": -15.56, "curve": 0.338, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 6.7}], "translate": [{"x": 3.25, "y": -22.44, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "x": 6.51, "y": -44.89, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 3.25, "y": -22.44, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "x": 6.51, "y": -44.89, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 3.25, "y": -22.44, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.5, "x": 6.51, "y": -44.89, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 5, "x": 7.07, "y": -34.15, "curve": 0.336, "c2": 0.34, "c3": 0.676, "c4": 0.69}, {"time": 5.0333, "x": 1.88, "y": 30.86}, {"time": 5.1, "x": -0.99, "y": 27.57}, {"time": 5.3, "x": 6.61, "y": -4.3}, {"time": 5.6667, "x": 14.22, "y": -20.59, "curve": 0.327, "c2": 0.31, "c3": 0.66, "c4": 0.64}, {"time": 6, "x": 6.65, "y": -28.38, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 6.3333, "x": 10.68, "y": -29.47, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 6.7, "x": 5.66, "y": -39.05, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 7.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": 3.25, "y": -22.44}]}, "tou10": {"rotate": [{"angle": 16.75, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": -16.38, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 20.29, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": 16.75, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": -16.38, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 20.29, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": 16.75, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 4.8333, "angle": -16.38, "curve": "stepped"}, {"time": 5, "angle": -16.38, "curve": 0.311, "c2": 0.2, "c3": 0.646, "c4": 0.55}, {"time": 5.0333, "angle": -24.35}, {"time": 5.1, "angle": 2.28}, {"time": 5.3, "angle": 8.04}, {"time": 5.6667, "angle": 26.79, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 6, "angle": -0.37, "curve": 0.338, "c2": 0.35, "c3": 0.672, "c4": 0.68}, {"time": 6.3333, "angle": -16.38, "curve": "stepped"}, {"time": 6.8333, "angle": -16.38, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "angle": 20.29, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 8, "angle": 16.75}], "translate": [{"time": 5.3}, {"time": 5.6667, "x": -0.11, "y": 6.28, "curve": 0.348, "c2": 0.45, "c3": 0.682, "c4": 0.79}, {"time": 6}]}, "tou11": {"rotate": [{"time": 4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5, "angle": -9.43, "curve": 0.334, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 5.1667, "angle": 17.34, "curve": 0.343, "c2": 0.36, "c3": 0.679, "c4": 0.71}, {"time": 5.4667, "angle": 20.7, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 6}], "translate": [{"x": 0.78, "y": -6.57, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -1.56, "y": 12.33, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 0.78, "y": -6.57, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -1.56, "y": 12.33, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 0.78, "y": -6.57, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": -1.56, "y": 12.33, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": 0.78, "y": -6.57, "curve": 0.25, "c3": 0.75}, {"time": 7, "x": -1.56, "y": 12.33, "curve": 0.25, "c3": 0.75}, {"time": 8, "x": 0.78, "y": -6.57}]}, "tou12": {"translate": [{"x": -2.13, "y": 5.76, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "x": -2.78, "y": 8.75, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 3.99, "y": -22.23, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "x": -2.13, "y": 5.76, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "x": -2.78, "y": 8.75, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": 3.99, "y": -22.23, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "x": -2.13, "y": 5.76, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "x": -2.78, "y": 8.75, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": 3.99, "y": -22.23, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 6, "x": -2.13, "y": 5.76, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 6.1667, "x": -2.78, "y": 8.75, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "x": 3.99, "y": -22.23, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 8, "x": -2.13, "y": 5.76}]}, "tou13": {"rotate": [{"angle": -6.4, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 8.21, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -12.19, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -6.4, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": 8.21, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -12.19, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -6.4, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.6667, "angle": 7.98, "curve": 0.274, "c2": 0.07, "c3": 0.625, "c4": 0.48}, {"time": 5, "angle": 1.88, "curve": 0.327, "c2": 0.31, "c3": 0.664, "c4": 0.66}, {"time": 5.0333, "angle": -2.42, "curve": 0.337, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 5.1, "angle": -6.07, "curve": 0.341, "c2": 0.36, "c3": 0.676, "c4": 0.7}, {"time": 5.3, "angle": -8.28, "curve": 0.38, "c2": 0.6, "c3": 0.725}, {"time": 5.6667, "angle": -12.19, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "angle": -6.4, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 6.6667, "angle": 8.21, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": -12.19, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "angle": -6.4}], "translate": [{"x": 1.91, "y": -8.66, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "x": -2.44, "y": 16.17, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "x": 9.39, "y": -51.34, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "x": 1.91, "y": -8.66, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.4, "x": -2.44, "y": 16.17, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "x": 9.39, "y": -51.34, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "x": 1.91, "y": -8.66, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.4, "x": -2.44, "y": 16.17, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 4.6667, "x": 0.42, "y": -0.18, "curve": 0.32, "c2": 0.29, "c3": 0.668, "c4": 0.67}, {"time": 5, "x": 5.32, "y": -28.11, "curve": 0.346, "c2": 0.38, "c3": 0.683, "c4": 0.72}, {"time": 5.0333, "x": 7.55, "y": -40.86, "curve": 0.356, "c2": 0.44, "c3": 0.693, "c4": 0.79}, {"time": 5.1, "x": 9, "y": -49.13, "curve": 0.357, "c2": 0.65, "c3": 0.692}, {"time": 5.3, "x": 9.39, "y": -51.34, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 5.6667, "x": 6.98, "y": -37.63, "curve": 0.315, "c2": 0.28, "c3": 0.665, "c4": 0.66}, {"time": 6, "x": 1.91, "y": -8.66, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.4, "x": -2.44, "y": 16.17, "curve": 0.25, "c3": 0.75}, {"time": 7.4, "x": 9.39, "y": -51.34, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8, "x": 1.91, "y": -8.66}]}, "tou14": {"rotate": [{"time": 4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5, "angle": -15.23, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 5.0667}], "translate": [{"x": 0.79, "y": -0.93, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": 8.98, "y": -7.45, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -2.45, "y": 1.66, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 0.79, "y": -0.93, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "x": 8.98, "y": -7.45, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": -2.45, "y": 1.66, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": 0.79, "y": -0.93, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.6667, "x": 8.98, "y": -7.45, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": -2.45, "y": 1.66, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "x": 0.79, "y": -0.93, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 6.6667, "x": 8.98, "y": -7.45, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "x": -2.45, "y": 1.66, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "x": 0.79, "y": -0.93}]}, "tou15": {"translate": [{"x": 2.57, "y": -5.23, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "x": 7.02, "y": 22.45, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 2.09, "y": -8.19, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "x": 2.57, "y": -5.23, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "x": 7.02, "y": 22.45, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 2.09, "y": -8.19, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "x": 2.57, "y": -5.23, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 4.8333, "x": 7.02, "y": 22.45, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "x": 2.09, "y": -8.19, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 6, "x": 2.57, "y": -5.23, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 6.8333, "x": 7.02, "y": 22.45, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": 2.09, "y": -8.19, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 8, "x": 2.57, "y": -5.23}]}, "tou16": {"rotate": [{"angle": -5.85, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -13.84, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 14.32, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -5.85, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": -13.84, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 14.32, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -5.85, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "angle": -13.84, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 14.32, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "angle": -5.85, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.3333, "angle": -13.84, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": 14.32, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "angle": -5.85}], "translate": [{"x": 5.31, "y": -17.57, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 15.6, "y": 38.47, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 5.31, "y": -17.57, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 15.6, "y": 38.47, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 5.31, "y": -17.57, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 15.6, "y": 38.47, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": 5.31, "y": -17.57, "curve": 0.25, "c3": 0.75}, {"time": 7, "x": 15.6, "y": 38.47, "curve": 0.25, "c3": 0.75}, {"time": 8, "x": 5.31, "y": -17.57}]}, "tou17": {"rotate": [{"time": 4, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 4.9333, "angle": 20.7, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 5.0667}], "translate": [{"x": 2.92, "y": 6.55, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "x": 5.83, "y": 13.1, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 2.92, "y": 6.55, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "x": 5.83, "y": 13.1, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 2.92, "y": 6.55, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.5, "x": 5.83, "y": 13.1, "curve": 0.25, "c3": 0.75}, {"time": 5.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 6, "x": 2.92, "y": 6.55, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.5, "x": 5.83, "y": 13.1, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": 2.92, "y": 6.55}]}, "tou18": {"translate": [{"x": 0.37, "y": -1.32, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": 4.47, "y": -21.45, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -1.26, "y": 6.65, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 0.37, "y": -1.32, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "x": 4.47, "y": -21.45, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": -1.26, "y": 6.65, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": 0.37, "y": -1.32, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.6667, "x": 4.47, "y": -21.45, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": -1.26, "y": 6.65, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "x": 0.37, "y": -1.32, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 6.6667, "x": 4.47, "y": -21.45, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "x": -1.26, "y": 6.65, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "x": 0.37, "y": -1.32}]}, "tou19": {"rotate": [{"angle": 4.8, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 7.13, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.1667, "angle": -17.02, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 4.8, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": 7.13, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.1667, "angle": -17.02, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": 4.8, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "angle": 7.13, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.1667, "angle": -17.02, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 6, "angle": 4.8, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 6.1667, "angle": 7.13, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 7.1667, "angle": -17.02, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 8, "angle": 4.8}], "translate": [{"x": -12.41, "y": 12.53, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "x": 17.49, "y": -39.85, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": -15.6, "y": 18.12, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "x": -12.41, "y": 12.53, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "x": 17.49, "y": -39.85, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": -15.6, "y": 18.12, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "x": -12.41, "y": 12.53, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 4.8333, "x": 17.49, "y": -39.85, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "x": -15.6, "y": 18.12, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 6, "x": -12.41, "y": 12.53, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 6.8333, "x": 17.49, "y": -39.85, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": -15.6, "y": 18.12, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 8, "x": -12.41, "y": 12.53}]}, "yziyanbai33": {"rotate": [{"time": 2}, {"time": 2.1, "angle": 7.51}, {"time": 2.2, "angle": 1.96}, {"time": 2.2667, "angle": 7.51}, {"time": 2.3667, "angle": 1.96}, {"time": 2.4333, "angle": 7.51}, {"time": 2.5333, "angle": 1.96}, {"time": 2.6, "angle": 7.51}, {"time": 2.7, "angle": 1.96}, {"time": 2.7667, "angle": 7.51}, {"time": 2.8667, "angle": 1.96}, {"time": 2.9333, "angle": 7.51}, {"time": 3.0333, "angle": 1.96}, {"time": 3.1, "angle": 7.51}, {"time": 3.2, "angle": 1.96}, {"time": 3.2667, "angle": 7.51}, {"time": 4, "curve": "stepped"}, {"time": 6}, {"time": 6.1, "angle": 7.51}, {"time": 6.2, "angle": 1.96}, {"time": 6.2667, "angle": 7.51}, {"time": 6.3667, "angle": 1.96}, {"time": 6.4333, "angle": 7.51}, {"time": 6.5333, "angle": 1.96}, {"time": 6.6, "angle": 7.51}, {"time": 6.7, "angle": 1.96}, {"time": 6.7667, "angle": 7.51}, {"time": 6.8667, "angle": 1.96}, {"time": 6.9333, "angle": 7.51}, {"time": 7.0333, "angle": 1.96}, {"time": 7.1, "angle": 7.51}, {"time": 7.2, "angle": 1.96}, {"time": 7.2667, "angle": 7.51}, {"time": 8}], "translate": [{"time": 0.9667}, {"time": 1.0333, "x": 10.02, "y": -3.85, "curve": "stepped"}, {"time": 2, "x": 10.02, "y": -3.85}, {"time": 2.0667, "x": 12.81, "y": -0.23, "curve": "stepped"}, {"time": 3.4, "x": 12.81, "y": -0.23}, {"time": 3.4667, "curve": "stepped"}, {"time": 4.9667}, {"time": 5.0333, "x": 10.02, "y": -3.85, "curve": "stepped"}, {"time": 6, "x": 10.02, "y": -3.85}, {"time": 6.0667, "x": 12.81, "y": -0.23, "curve": "stepped"}, {"time": 7.4, "x": 12.81, "y": -0.23}, {"time": 7.4667}], "scale": [{"time": 0.9667}, {"time": 1.0333, "x": 1.069, "y": 1.069, "curve": "stepped"}, {"time": 2, "x": 1.069, "y": 1.069}, {"time": 2.0667, "curve": "stepped"}, {"time": 4.9667}, {"time": 5.0333, "x": 1.069, "y": 1.069, "curve": "stepped"}, {"time": 6, "x": 1.069, "y": 1.069}, {"time": 6.0667}]}, "zuiy1yzhu": {"rotate": [{"time": 5.5667}, {"time": 5.6333, "angle": -18.9}, {"time": 5.7}], "translate": [{"time": 0.5}, {"time": 0.5667, "x": -3.58, "y": -6.66, "curve": "stepped"}, {"time": 1.3333, "x": -3.58, "y": -6.66}, {"time": 1.4, "x": -7.76, "y": -3.95, "curve": "stepped"}, {"time": 1.9333, "x": -7.76, "y": -3.95}, {"time": 2, "x": -3.58, "y": -6.66, "curve": "stepped"}, {"time": 3.3333, "x": -3.58, "y": -6.66}, {"time": 3.4, "curve": "stepped"}, {"time": 4.5}, {"time": 4.5667, "x": -3.58, "y": -6.66, "curve": "stepped"}, {"time": 5.0333, "x": -3.58, "y": -6.66}, {"time": 5.1, "x": 7.69, "y": 15.41, "curve": "stepped"}, {"time": 5.5667, "x": 7.69, "y": 15.41}, {"time": 5.6333, "x": 3.05, "y": 7.81}, {"time": 5.7, "x": -2.71, "y": -4.96}, {"time": 5.7667, "x": -4, "y": -8.59}, {"time": 5.8333, "x": -3.58, "y": -6.66, "curve": "stepped"}, {"time": 7.3333, "x": -3.58, "y": -6.66}, {"time": 7.4}], "scale": [{"time": 5.5667}, {"time": 5.6333, "x": 0.761, "y": 1.209}, {"time": 5.7}, {"time": 5.7667, "x": 1.134, "y": 0.93}, {"time": 5.8333}]}, "yziyanzhu33": {"translate": [{"time": 1}, {"time": 1.1, "x": 3.56, "y": -3.21, "curve": "stepped"}, {"time": 2.6667, "x": 3.56, "y": -3.21}, {"time": 2.7667, "curve": "stepped"}, {"time": 5}, {"time": 5.1, "x": 3.56, "y": -3.21, "curve": "stepped"}, {"time": 6.6667, "x": 3.56, "y": -3.21}, {"time": 6.7667}]}, "qbai2": {"rotate": [{"time": 5}, {"time": 5.0333, "angle": -3.92}, {"time": 5.1, "angle": 2.83}, {"time": 5.3333, "angle": 8.78}, {"time": 6}], "translate": [{"x": -0.43, "y": -0.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": 2.93, "y": 2.19, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -8.93, "y": -7.42, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -0.43, "y": -0.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "x": 2.93, "y": 2.19, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -8.93, "y": -7.42, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": -0.43, "y": -0.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "x": 2.93, "y": 2.19, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": -8.93, "y": -7.42, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "x": -0.43, "y": -0.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.3333, "x": 2.93, "y": 2.19, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": -8.93, "y": -7.42, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "x": -0.43, "y": -0.54}]}, "qbai3": {"translate": [{"x": -2.56, "y": 2.91, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5333, "x": 1.33, "y": -3.41, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "x": -5.82, "y": 8.21, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 2, "x": -2.56, "y": 2.91, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 2.5333, "x": 1.33, "y": -3.41, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "x": -5.82, "y": 8.21, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 4, "x": -2.56, "y": 2.91, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 4.5333, "x": 1.33, "y": -3.41, "curve": 0.25, "c3": 0.75}, {"time": 5.5333, "x": -5.82, "y": 8.21, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 6, "x": -2.56, "y": 2.91, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 6.5333, "x": 1.33, "y": -3.41, "curve": 0.25, "c3": 0.75}, {"time": 7.5333, "x": -5.82, "y": 8.21, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 8, "x": -2.56, "y": 2.91}]}, "qbai4": {"translate": [{"x": -10.07, "y": 12.11, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 0.7333, "x": 2.31, "y": -7.61, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "x": -13.21, "y": 17.12, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 2, "x": -10.07, "y": 12.11, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 2.7333, "x": 2.31, "y": -7.61, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "x": -13.21, "y": 17.12, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 4, "x": -10.07, "y": 12.11, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 4.7333, "x": 2.31, "y": -7.61, "curve": 0.25, "c3": 0.75}, {"time": 5.7333, "x": -13.21, "y": 17.12, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 6, "x": -10.07, "y": 12.11, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 6.7333, "x": 2.31, "y": -7.61, "curve": 0.25, "c3": 0.75}, {"time": 7.7333, "x": -13.21, "y": 17.12, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 8, "x": -10.07, "y": 12.11}]}, "qbai5": {"translate": [{"x": -17.76, "y": 26.02, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 0.9333, "x": 6.8, "y": -12.42, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "x": -18.33, "y": 26.92, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 2, "x": -17.76, "y": 26.02, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 2.9333, "x": 6.8, "y": -12.42, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "x": -18.33, "y": 26.92, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 4, "x": -17.76, "y": 26.02, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 4.9333, "x": 6.8, "y": -12.42, "curve": 0.25, "c3": 0.75}, {"time": 5.9333, "x": -18.33, "y": 26.92, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 6, "x": -17.76, "y": 26.02, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 6.9333, "x": 6.8, "y": -12.42, "curve": 0.25, "c3": 0.75}, {"time": 7.9333, "x": -18.33, "y": 26.92, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 8, "x": -17.76, "y": 26.02}]}, "qbai6": {"rotate": [{"angle": 8.62, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "angle": 17.49, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -6.63, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "angle": 8.62, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.4, "angle": 17.49, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "angle": -6.63, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "angle": 8.62, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.4, "angle": 17.49, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "angle": -6.63, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6, "angle": 8.62, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.4, "angle": 17.49, "curve": 0.25, "c3": 0.75}, {"time": 7.4, "angle": -6.63, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8, "angle": 8.62}], "translate": [{"x": -24.46, "y": 39.6, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "x": -26.9, "y": 43.88, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": 8.22, "y": -17.76, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2, "x": -24.46, "y": 39.6, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 2.1333, "x": -26.9, "y": 43.88, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "x": 8.22, "y": -17.76, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 4, "x": -24.46, "y": 39.6, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 4.1333, "x": -26.9, "y": 43.88, "curve": 0.25, "c3": 0.75}, {"time": 5.1333, "x": 8.22, "y": -17.76, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 6, "x": -24.46, "y": 39.6, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 6.1333, "x": -26.9, "y": 43.88, "curve": 0.25, "c3": 0.75}, {"time": 7.1333, "x": 8.22, "y": -17.76, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 8, "x": -24.46, "y": 39.6}]}, "qbai7": {"rotate": [{"time": 5}, {"time": 5.0333, "angle": -3.92}, {"time": 5.1, "angle": 2.83}, {"time": 5.3333, "angle": 8.78}, {"time": 6}], "translate": [{"x": -5.56, "y": -4.7, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": 2.93, "y": 2.19, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -8.93, "y": -7.43, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -5.56, "y": -4.7, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "x": 2.93, "y": 2.19, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": -8.93, "y": -7.43, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": -5.56, "y": -4.7, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.6667, "x": 2.93, "y": 2.19, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": -8.93, "y": -7.43, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "x": -5.56, "y": -4.7, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 6.6667, "x": 2.93, "y": 2.19, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "x": -8.93, "y": -7.43, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "x": -5.56, "y": -4.7}]}, "qbai8": {"translate": [{"x": -3.88, "y": 7.85, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "x": 0.74, "y": -3.59, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": -4.37, "y": 9.07, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "x": -3.88, "y": 7.85, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "x": 0.74, "y": -3.59, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": -4.37, "y": 9.07, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "x": -3.88, "y": 7.85, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 4.8333, "x": 0.74, "y": -3.59, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "x": -4.37, "y": 9.07, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 6, "x": -3.88, "y": 7.85, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 6.8333, "x": 0.74, "y": -3.59, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "x": -4.37, "y": 9.07, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 8, "x": -3.88, "y": 7.85}]}, "qbai9": {"translate": [{"x": -13.21, "y": 17.12, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 2.31, "y": -7.61, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -13.21, "y": 17.12, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 2.31, "y": -7.61, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -13.21, "y": 17.12, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": 2.31, "y": -7.61, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": -13.21, "y": 17.12, "curve": 0.25, "c3": 0.75}, {"time": 7, "x": 2.31, "y": -7.61, "curve": 0.25, "c3": 0.75}, {"time": 8, "x": -13.21, "y": 17.12}]}, "qbai10": {"translate": [{"x": -15.91, "y": 23.12, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "x": -18.33, "y": 26.92, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 6.8, "y": -12.42, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "x": -15.91, "y": 23.12, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "x": -18.33, "y": 26.92, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": 6.8, "y": -12.42, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "x": -15.91, "y": 23.12, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "x": -18.33, "y": 26.92, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": 6.8, "y": -12.42, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 6, "x": -15.91, "y": 23.12, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 6.1667, "x": -18.33, "y": 26.92, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "x": 6.8, "y": -12.42, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 8, "x": -15.91, "y": 23.12}]}, "qbai11": {"rotate": [{"angle": 0.3, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.5667, "angle": 14.93, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": -9.95, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 2, "angle": 0.3, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 2.5667, "angle": 14.93, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": -9.95, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 4, "angle": 0.3, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 4.5667, "angle": 14.93, "curve": 0.25, "c3": 0.75}, {"time": 5.5667, "angle": -9.95, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 6, "angle": 0.3, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 6.5667, "angle": 14.93, "curve": 0.25, "c3": 0.75}, {"time": 7.5667, "angle": -9.95, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 8, "angle": 0.3}], "translate": [{"x": -16.93, "y": 26.39, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": -26.9, "y": 43.88, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 8.22, "y": -17.76, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -16.93, "y": 26.39, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "x": -26.9, "y": 43.88, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 8.22, "y": -17.76, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": -16.93, "y": 26.39, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "x": -26.9, "y": 43.88, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 8.22, "y": -17.76, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "x": -16.93, "y": 26.39, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.3333, "x": -26.9, "y": 43.88, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": 8.22, "y": -17.76, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "x": -16.93, "y": 26.39}]}, "qbai25": {"rotate": [{"time": 5}, {"time": 5.1, "angle": 5.02, "curve": 0.34, "c2": 0.35, "c3": 0.677, "c4": 0.7}, {"time": 5.4, "angle": 7.46, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 6}], "translate": [{"x": -3.04, "y": -1.84, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": -31.54, "y": -19.05, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": -31.54, "y": -19.05, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "x": -3.04, "y": -1.84, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "x": -31.54, "y": -19.05, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "x": -31.54, "y": -19.05, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 8, "x": -3.04, "y": -1.84}]}, "qbai26": {"translate": [{"x": -1.59, "y": 1.44, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "x": 3.86, "y": -10.16, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "x": -10.96, "y": 21.37, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "x": -1.59, "y": 1.44, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.4, "x": 3.86, "y": -10.16, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "x": -10.96, "y": 21.37, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 4, "x": -1.59, "y": 1.44, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.4, "x": 3.86, "y": -10.16, "curve": 0.25, "c3": 0.75}, {"time": 5.4, "x": -10.96, "y": 21.37, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 6, "x": -1.59, "y": 1.44, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 6.4, "x": 3.86, "y": -10.16, "curve": 0.25, "c3": 0.75}, {"time": 7.4, "x": -10.96, "y": 21.37, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8, "x": -1.59, "y": 1.44}]}, "qbai27": {"rotate": [{"angle": 6.2, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": -11.2, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 8.06, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": 6.2, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": -11.2, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 8.06, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": 6.2, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 4.8333, "angle": -11.2, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "angle": 8.06, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 6, "angle": 6.2, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 6.8333, "angle": -11.2, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "angle": 8.06, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 8, "angle": 6.2}], "translate": [{"x": -8.38, "y": 19.35, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": 9.34, "y": -20.85, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -15.4, "y": 35.28, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -8.38, "y": 19.35, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "x": 9.34, "y": -20.85, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": -15.4, "y": 35.28, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": -8.38, "y": 19.35, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.6667, "x": 9.34, "y": -20.85, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": -15.4, "y": 35.28, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "x": -8.38, "y": 19.35, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 6.6667, "x": 9.34, "y": -20.85, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "x": -15.4, "y": 35.28, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "x": -8.38, "y": 19.35}]}, "qbai28": {"rotate": [{"angle": 6.21, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 8.31, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -13.39, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 6.21, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": 8.31, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -13.39, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": 6.21, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "angle": 8.31, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "angle": -13.39, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 6, "angle": 6.21, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 6.1667, "angle": 8.31, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "angle": -13.39, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 8, "angle": 6.21}], "translate": [{"x": -11.02, "y": 58.69, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.9, "x": 9.46, "y": -31.58, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "x": -11.93, "y": 62.7, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2, "x": -11.02, "y": 58.69, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 2.9, "x": 9.46, "y": -31.58, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "x": -11.93, "y": 62.7, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 4, "x": -11.02, "y": 58.69, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 4.9, "x": 9.46, "y": -31.58, "curve": 0.25, "c3": 0.75}, {"time": 5.9, "x": -11.93, "y": 62.7, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 6, "x": -11.02, "y": 58.69, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 6.9, "x": 9.46, "y": -31.58, "curve": 0.25, "c3": 0.75}, {"time": 7.9, "x": -11.93, "y": 62.7, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 8, "x": -11.02, "y": 58.69}]}, "yao5": {"rotate": [{"angle": 0.31, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333}, {"time": 1.3333, "angle": 1.11, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 0.31, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333}, {"time": 3.3333, "angle": 1.11, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 0.31, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333}, {"time": 5.3333, "angle": 1.11, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "angle": 0.31, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.3333}, {"time": 7.3333, "angle": 1.11, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "angle": 0.31}], "translate": [{"x": -1.68, "y": -0.3, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333}, {"time": 1.3333, "x": -5.92, "y": -1.06, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -1.68, "y": -0.3, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333}, {"time": 3.3333, "x": -5.92, "y": -1.06, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": -1.68, "y": -0.3, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333}, {"time": 5.3333, "x": -5.92, "y": -1.06, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "x": -1.68, "y": -0.3, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.3333}, {"time": 7.3333, "x": -5.92, "y": -1.06, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "x": -1.68, "y": -0.3}]}, "qbai": {"rotate": [{"angle": -0.44, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333}, {"time": 1.3333, "angle": -1.56, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -0.44, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333}, {"time": 3.3333, "angle": -1.56, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -0.44, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333}, {"time": 5.3333, "angle": -1.56, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "angle": -0.44, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.3333}, {"time": 7.3333, "angle": -1.56, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "angle": -0.44}], "translate": [{"x": -0.67, "y": -0.36, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333}, {"time": 1.3333, "x": -2.37, "y": -1.27, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -0.67, "y": -0.36, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333}, {"time": 3.3333, "x": -2.37, "y": -1.27, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": -0.67, "y": -0.36, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333}, {"time": 5.3333, "x": -2.37, "y": -1.27, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 6, "x": -0.67, "y": -0.36, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.3333}, {"time": 7.3333, "x": -2.37, "y": -1.27, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "x": -0.67, "y": -0.36}]}, "hup2": {"translate": [{"x": -0.17, "y": 0.14, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.3333}, {"time": 2.3333, "x": -1.75, "y": 1.41, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "x": -0.17, "y": 0.14, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.3333}, {"time": 6.3333, "x": -1.75, "y": 1.41, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 8, "x": -0.17, "y": 0.14}]}, "hup3": {"translate": [{"x": 0.79, "y": -0.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6667}, {"time": 2.6667, "x": 2.78, "y": -3.08, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": 0.79, "y": -0.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.6667}, {"time": 6.6667, "x": 2.78, "y": -3.08, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "x": 0.79, "y": -0.87}]}, "hup4": {"translate": [{"x": 2.48, "y": -2.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1}, {"time": 3, "x": 4.95, "y": -5.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 2.48, "y": -2.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5}, {"time": 7, "x": 4.95, "y": -5.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": 2.48, "y": -2.65}]}, "dajt2": {"translate": [{"x": -17.85, "y": -2.19, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.3333}, {"time": 3.3333, "x": -24.92, "y": -3.06, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": -17.85, "y": -2.19, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 5.3333}, {"time": 7.3333, "x": -24.92, "y": -3.06, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "x": -17.85, "y": -2.19}]}, "dajt3": {"translate": [{"x": -3.67, "y": -2.64, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6667}, {"time": 2.6667, "x": -12.94, "y": -9.31, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": -3.67, "y": -2.64, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.6667}, {"time": 6.6667, "x": -12.94, "y": -9.31, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "x": -3.67, "y": -2.64}]}, "dajt4": {"translate": [{"x": -15.05, "y": 3.28, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -15.05, "y": 3.28, "curve": 0.25, "c3": 0.75}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 8, "x": -15.05, "y": 3.28}]}, "hup": {"translate": [{"x": -5.13, "y": -0.02, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6667}, {"time": 2.6667, "x": -18.06, "y": -0.07, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": -5.13, "y": -0.02, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.6667}, {"time": 6.6667, "x": -18.06, "y": -0.07, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "x": -5.13, "y": -0.02}]}, "hup5": {"translate": [{"x": 7.12, "y": 0.45, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1, "x": 14.23, "y": 0.91, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 7.12, "y": 0.45, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5, "x": 14.23, "y": 0.91, "curve": 0.25, "c3": 0.75}, {"time": 7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": 7.12, "y": 0.45}]}, "hup6": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.196, "y": 1.196, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": 1.196, "y": 1.196, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "dabi1": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 2.88, "y": -3.11, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 2.88, "y": -3.11, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4.6667, "x": 2.08, "y": -2.25, "curve": 0.351, "c2": 0.4, "c3": 0.689, "c4": 0.75}, {"time": 4.8333, "x": 2.6, "y": -2.81, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 5, "x": 2.88, "y": -3.11, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 5.0333, "x": -13.02, "y": -10.24, "curve": 0.305, "c2": 0.18, "c3": 0.642, "c4": 0.53}, {"time": 5.1, "x": 2.51, "y": -2.7, "curve": 0.311, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 5.3, "x": -0.89, "y": -5.34, "curve": 0.332, "c2": 0.33, "c3": 0.676, "c4": 0.7}, {"time": 5.6667, "x": 0.82, "y": -0.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6, "curve": 0.25, "c3": 0.75}, {"time": 7, "x": 2.88, "y": -3.11, "curve": 0.25, "c3": 0.75}, {"time": 8}]}, "shoubi1": {"rotate": [{"angle": 0.96, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 3.37, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 0.96, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 3.37, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 0.96, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4.6667, "angle": 0.96, "curve": 0.325, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 4.8333, "angle": 1.68, "curve": 0.337, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 5, "angle": 2.42, "curve": 0.339, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 5.0333, "angle": -5.08, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 5.1, "angle": 3.15, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 5.6667, "angle": 2.41, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 6, "angle": 0.96, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": 3.37, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "angle": 0.96}], "translate": [{"x": 0.36, "y": 0.12, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 1.27, "y": 0.42, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.36, "y": 0.12, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 1.27, "y": 0.42, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": 0.36, "y": 0.12, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4.6667, "x": 0.36, "y": 0.12, "curve": 0.325, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 4.8333, "x": 3.3, "y": 11.6, "curve": 0.337, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 5, "x": 1.44, "y": -6.96, "curve": 0.339, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 5.0333, "x": 3.18, "y": -7.67, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 5.1, "x": 1.19, "y": 0.39, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 5.6667, "x": 0.91, "y": 0.3, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 6, "x": 0.36, "y": 0.12, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 6.3333, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": 1.27, "y": 0.42, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "x": 0.36, "y": 0.12}]}, "shoubi1_3": {"rotate": [{"time": 0.3667}, {"time": 0.6333, "angle": 28.23}, {"time": 0.7333, "curve": "stepped"}, {"time": 0.8667}, {"time": 1.2667, "angle": 28.23}, {"time": 1.3667}, {"time": 1.5333, "angle": 28.23}, {"time": 1.6333, "curve": "stepped"}, {"time": 2.3667}, {"time": 2.6333, "angle": 28.23}, {"time": 2.7333, "curve": "stepped"}, {"time": 2.8667}, {"time": 3.2667, "angle": 28.23}, {"time": 3.3667}, {"time": 3.5333, "angle": 28.23}, {"time": 3.6333, "curve": "stepped"}, {"time": 4.8333}, {"time": 5, "angle": 24.6}, {"time": 5.0333, "angle": 23}, {"time": 5.1, "curve": "stepped"}, {"time": 6.3667}, {"time": 6.6333, "angle": 28.23}, {"time": 6.7333, "curve": "stepped"}, {"time": 6.8667}, {"time": 7.2667, "angle": 28.23}, {"time": 7.3667}, {"time": 7.5333, "angle": 28.23}, {"time": 7.6333}], "translate": [{"time": 4.8333}, {"time": 5, "x": -0.43, "y": -6.16}, {"time": 5.0333}]}, "dabi2": {"rotate": [{"angle": 2.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 5.01, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 2.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 5.01, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 2.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.5, "angle": -6.68, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4.6667, "angle": -5.22, "curve": 0.311, "c2": 0.25, "c3": 0.649, "c4": 0.6}, {"time": 4.8333, "angle": -2.41, "curve": 0.325, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 5, "angle": 1.6, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 5.0333, "angle": -1, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 5.1, "angle": 2.96, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 5.3, "angle": -2.49, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.6667, "angle": 1.45, "curve": 0.299, "c2": 0.22, "c3": 0.649, "c4": 0.61}, {"time": 6, "angle": 2.5, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.5, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "angle": 5.01, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "angle": 2.5}]}, "shou2": {"rotate": [{"angle": 2.36, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": -0.65, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 2.68, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": 2.36, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": -0.65, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 2.68, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": 2.36, "curve": 0.299, "c2": 0.22, "c3": 0.701, "c4": 0.78}, {"time": 4.6667, "angle": -0.32, "curve": 0.371, "c2": 0.63, "c3": 0.71}, {"time": 4.8333, "angle": -1.42, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 5, "angle": 0.75, "curve": 0.328, "c2": 0.31, "c3": 0.661, "c4": 0.64}, {"time": 5.0333, "angle": 5.94, "curve": 0.324, "c2": 0.3, "c3": 0.658, "c4": 0.63}, {"time": 5.1, "angle": 0.02, "curve": 0.318, "c2": 0.28, "c3": 0.658, "c4": 0.64}, {"time": 5.3, "angle": 0.85, "curve": 0.349, "c2": 0.38, "c3": 0.703, "c4": 0.79}, {"time": 5.6667, "angle": 2.84, "curve": 0.371, "c2": 0.63, "c3": 0.71}, {"time": 6, "angle": 2.36, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 6.8333, "angle": -0.65, "curve": 0.25, "c3": 0.75}, {"time": 7.8333, "angle": 2.68, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 8, "angle": 2.36}], "translate": [{"x": 3.71, "y": -3.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "x": 7.42, "y": -7.96, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 3.71, "y": -3.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "x": 7.42, "y": -7.96, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "x": 3.71, "y": -3.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.5, "x": 7.42, "y": -7.96, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4.6667, "x": 6.71, "y": -7.2, "curve": 0.311, "c2": 0.25, "c3": 0.649, "c4": 0.6}, {"time": 4.8333, "x": 8.08, "y": -8.38, "curve": 0.325, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 5, "x": 3.71, "y": -3.98, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 5.0333, "x": 3.38, "y": -3.63, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 5.3, "x": 9.13, "y": -14.4, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 5.6667, "x": 7.58, "y": -10.37, "curve": 0.299, "c2": 0.22, "c3": 0.649, "c4": 0.61}, {"time": 6, "x": 3.71, "y": -3.98, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.5, "x": 7.42, "y": -7.96, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "x": 3.71, "y": -3.98}]}, "shouz2": {"rotate": [{"angle": 3.15, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 4.39, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -8.38, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 3.15, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": 4.39, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -8.38, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": 3.15, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.1667, "angle": 4.39, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.6667, "angle": -2.07, "curve": 0.338, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 4.8333, "angle": -5.51, "curve": 0.351, "c2": 0.4, "c3": 0.689, "c4": 0.75}, {"time": 5, "angle": -13.5, "curve": 0.339, "c2": 0.36, "c3": 0.673, "c4": 0.7}, {"time": 5.0333, "angle": -9.32, "curve": 0.348, "c2": 0.43, "c3": 0.682, "c4": 0.77}, {"time": 5.1, "angle": -1.47, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 5.3, "angle": -8.6, "curve": 0.291, "c2": 0.19, "c3": 0.645, "c4": 0.59}, {"time": 5.6667, "angle": -3.15, "curve": 0.351, "c2": 0.39, "c3": 0.701, "c4": 0.78}, {"time": 6, "angle": 3.15, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 6.1667, "angle": 4.39, "curve": 0.25, "c3": 0.75}, {"time": 7.1667, "angle": -8.38, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 8, "angle": 3.15}]}, "wu2": {"translate": [{"x": 81.68, "y": 27.02}, {"time": 6.6667, "x": 8.61}, {"time": 8, "x": 81.68, "y": 27.02}]}, "wu3": {"translate": [{}, {"time": 6.6667, "x": -122.24, "y": -132.28}, {"time": 8}], "scale": [{}, {"time": 6.6667, "x": 1.179, "y": 1.179}, {"time": 8}]}, "wu4": {"translate": [{}, {"time": 6.6667, "x": 73.25, "y": -211.78}, {"time": 8}]}, "wu5": {"translate": [{}, {"time": 6.6667, "x": -177.66, "y": -191.48}, {"time": 8}]}, "wu6": {"rotate": [{}, {"time": 3.4667, "angle": 12.25}, {"time": 6.6667}], "translate": [{}, {"time": 6.6667, "x": 78.77, "y": -292.77}, {"time": 8}]}, "hup7": {"translate": [{"x": -15.56}, {"time": 1, "x": -86.25}, {"time": 4, "x": -15.56}, {"time": 5, "x": -86.25}, {"time": 8, "x": -15.56}]}, "hup8": {"translate": [{"x": -2.56, "y": 85.56}, {"time": 1}, {"time": 4, "x": -2.56, "y": 85.56}, {"time": 5}, {"time": 8, "x": -2.56, "y": 85.56}]}, "hup9": {"translate": [{"x": 43.14, "y": 105.5}, {"time": 1}, {"time": 4, "x": 43.14, "y": 105.5}, {"time": 5}, {"time": 8, "x": 43.14, "y": 105.5}]}, "hup10": {"translate": [{"x": 6.95, "y": 163.97}, {"time": 1}, {"time": 4, "x": 6.95, "y": 163.97}, {"time": 5}, {"time": 8, "x": 6.95, "y": 163.97}]}, "hup11": {"translate": [{"x": -6.13, "y": 116.54}, {"time": 1}, {"time": 4, "x": -6.13, "y": 116.54}, {"time": 5}, {"time": 8, "x": -6.13, "y": 116.54}]}, "hup12": {"translate": [{"x": -15.44, "y": 293.29}, {"time": 1}, {"time": 4, "x": -15.44, "y": 293.29}, {"time": 5}, {"time": 8, "x": -15.44, "y": 293.29}]}, "wu7": {"translate": [{"x": -40.71}, {"time": 1.6667, "x": -26.81}, {"time": 2.6667, "x": -51.82}, {"time": 4, "x": -40.71}, {"time": 5.6667, "x": -26.81}, {"time": 6.6667, "x": -51.82}, {"time": 8, "x": -40.71}]}, "wu8": {"translate": [{"x": 16.72, "y": 23.83}, {"time": 1.6667, "x": 37.61, "y": 53.62}, {"time": 2.6667}, {"time": 4, "x": 16.72, "y": 23.83}, {"time": 5.6667, "x": 37.61, "y": 53.62}, {"time": 6.6667}, {"time": 8, "x": 16.72, "y": 23.83}]}, "wu9": {"translate": [{"x": 36.94, "y": 49.26}, {"time": 1.6667, "x": 83.12, "y": 110.83}, {"time": 2.6667}, {"time": 4, "x": 36.94, "y": 49.26}, {"time": 5.6667, "x": 83.12, "y": 110.83}, {"time": 6.6667}, {"time": 8, "x": 36.94, "y": 49.26}]}, "wu10": {"translate": [{"x": -27.94, "y": 68.62}, {"time": 1.6667, "x": -62.86, "y": 154.39}, {"time": 2.6667}, {"time": 4, "x": -27.94, "y": 68.62}, {"time": 5.6667, "x": -62.86, "y": 154.39}, {"time": 6.6667}, {"time": 8, "x": -27.94, "y": 68.62}]}, "wu11": {"translate": [{"x": 21.55, "y": 84.15}, {"time": 1.6667, "x": 48.49, "y": 189.34}, {"time": 2.6667}, {"time": 4, "x": 21.55, "y": 84.15}, {"time": 5.6667, "x": 48.49, "y": 189.34}, {"time": 6.6667}, {"time": 8, "x": 21.55, "y": 84.15}]}, "wu12": {"translate": [{"x": -24.63, "y": 49.26}, {"time": 1.6667, "x": -55.42, "y": 110.83}, {"time": 2.6667}, {"time": 4, "x": -24.63, "y": 49.26}, {"time": 5.6667, "x": -55.42, "y": 110.83}, {"time": 6.6667}, {"time": 8, "x": -24.63, "y": 49.26}]}, "wu13": {"translate": [{"x": 10.26, "y": 112.88}, {"time": 1.6667, "x": 23.09, "y": 253.99}, {"time": 2.6667}, {"time": 4, "x": 10.26, "y": 112.88}, {"time": 5.6667, "x": 23.09, "y": 253.99}, {"time": 6.6667}, {"time": 8, "x": 10.26, "y": 112.88}]}, "wu14": {"translate": [{"x": 47.34}, {"time": 0.5, "x": 78.45}, {"time": 3.5, "x": 16.23}, {"time": 4, "x": 47.34}, {"time": 4.5, "x": 78.45}, {"time": 7.5, "x": 16.23}, {"time": 8, "x": 47.34}]}, "wu15": {"translate": [{"x": -18.35, "y": -41.49}, {"time": 0.5}, {"time": 3.1667, "x": -48.93, "y": -110.63}, {"time": 4, "x": -18.35, "y": -41.49}, {"time": 4.5}, {"time": 7.1667, "x": -48.93, "y": -110.63}, {"time": 8, "x": -18.35, "y": -41.49}]}, "wu16": {"translate": [{"x": -46.13, "y": -48.89}, {"time": 1}, {"time": 4, "x": -46.13, "y": -48.89}, {"time": 5}, {"time": 8, "x": -46.13, "y": -48.89}], "scale": [{"x": 1.214, "y": 1.214}, {"time": 1}, {"time": 4, "x": 1.214, "y": 1.214}, {"time": 5}, {"time": 8, "x": 1.214, "y": 1.214}]}, "wu17": {"translate": [{"x": 36.55, "y": -77.85}, {"time": 1}, {"time": 4, "x": 36.55, "y": -77.85}, {"time": 5}, {"time": 8, "x": 36.55, "y": -77.85}]}, "wu18": {"translate": [{"x": -89.77, "y": -260.27}, {"time": 1}, {"time": 4, "x": -89.77, "y": -260.27}, {"time": 5}, {"time": 8, "x": -89.77, "y": -260.27}]}, "wu19": {"translate": [{"x": 17.54, "y": -48.93}, {"time": 0.5}, {"time": 3.1667, "x": 46.78, "y": -130.48}, {"time": 4, "x": 17.54, "y": -48.93}, {"time": 4.5}, {"time": 7.1667, "x": 46.78, "y": -130.48}, {"time": 8, "x": 17.54, "y": -48.93}]}, "wu20": {"translate": [{"x": -3.57, "y": -46.96}, {"time": 0.5}, {"time": 3.1667, "x": -9.53, "y": -125.22}, {"time": 4, "x": -3.57, "y": -46.96}, {"time": 4.5}, {"time": 7.1667, "x": -9.53, "y": -125.22}, {"time": 8, "x": -3.57, "y": -46.96}]}, "wu21": {"translate": [{"x": 39.57, "y": -72.29}, {"time": 0.5}, {"time": 3.1667, "x": 105.52, "y": -192.79}, {"time": 4, "x": 39.57, "y": -72.29}, {"time": 4.5}, {"time": 7.1667, "x": 105.52, "y": -192.79}, {"time": 8, "x": 39.57, "y": -72.29}]}, "bone2": {"translate": [{"x": -35.64, "y": -2.96}, {"time": 1.3, "x": -15.07, "y": -25.16}, {"time": 1.3333, "x": -77.84, "y": 42.57}, {"time": 4, "x": -35.64, "y": -2.96}, {"time": 5.3, "x": -15.07, "y": -25.16}, {"time": 5.3333, "x": -77.84, "y": 42.57}, {"time": 8, "x": -35.64, "y": -2.96}]}, "wu22": {"translate": [{"x": 53.73, "y": 5.01}, {"time": 1.9667, "x": 62.15, "y": 27.96}, {"time": 2, "x": 28.06, "y": -65}, {"time": 8, "x": 53.73, "y": 5.01}]}, "wu23": {"translate": [{"x": -64.88, "y": 77.31}, {"time": 1.9667, "x": -79.05, "y": 139.01}, {"time": 2, "x": -21.65, "y": -110.93}, {"time": 8, "x": -64.88, "y": 77.31}]}, "bone3": {"translate": [{"x": 7.31, "y": -8.18}, {"time": 1.9667}, {"time": 2, "x": 29.6, "y": -33.12}, {"time": 8, "x": 7.31, "y": -8.18}], "scale": [{"x": 1.439, "y": 1.439}, {"time": 1.9667, "x": 1.583, "y": 1.583}, {"time": 2}, {"time": 8, "x": 1.439, "y": 1.439}]}, "bone4": {"translate": [{"x": 4.99, "y": 36.62}, {"time": 1.9667, "x": 21.42, "y": 65.11}, {"time": 2, "x": -45.14, "y": -50.3}, {"time": 8, "x": 4.99, "y": 36.62}]}, "bone5": {"translate": [{"x": 6.12, "y": 52.39}, {"time": 1.9667, "x": -2.86, "y": 133.04}, {"time": 2, "x": 33.52, "y": -193.65}, {"time": 8, "x": 6.12, "y": 52.39}]}, "bone6": {"translate": [{"x": 33.19, "y": -63.16}, {"time": 1.3, "x": 49.37, "y": -93.96}, {"time": 1.3333}, {"time": 4, "x": 33.19, "y": -63.16}, {"time": 5.3, "x": 49.37, "y": -93.96}, {"time": 5.3333}, {"time": 8, "x": 33.19, "y": -63.16}]}, "bone7": {"translate": [{"x": 9.57, "y": -72.09}, {"time": 1.3, "x": 14.24, "y": -107.24}, {"time": 1.3333}, {"time": 4, "x": 9.57, "y": -72.09}, {"time": 5.3, "x": 14.24, "y": -107.24}, {"time": 5.3333}, {"time": 8, "x": 9.57, "y": -72.09}]}, "bone8": {"translate": [{"x": 10.09, "y": -126.82}, {"time": 1.3, "x": 15.01, "y": -188.65}, {"time": 1.3333}, {"time": 4, "x": 10.09, "y": -126.82}, {"time": 5.3, "x": 15.01, "y": -188.65}, {"time": 5.3333}, {"time": 8, "x": 10.09, "y": -126.82}]}, "bone9": {"translate": [{"x": -10.55, "y": -143.62}, {"time": 1.3, "x": -15.7, "y": -213.64}, {"time": 1.3333}, {"time": 4, "x": -10.55, "y": -143.62}, {"time": 5.3, "x": -15.7, "y": -213.64}, {"time": 5.3333}, {"time": 8, "x": -10.55, "y": -143.62}]}, "bone10": {"rotate": [{"time": 4.6667}, {"time": 4.8333, "angle": -1.49}, {"time": 5, "angle": -10.7}, {"time": 5.0333}], "translate": [{"time": 4.6667}, {"time": 4.8333, "x": -6.43, "y": 38.59}, {"time": 5, "y": 57.89}, {"time": 5.0333}]}, "qbai31": {"rotate": [{"angle": 3.13, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -7.07, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 7.17, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 3.13, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": -7.07, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 7.17, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 3.13, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.6667, "angle": -7.07, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "angle": 7.17, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 6, "angle": 3.13, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 6.6667, "angle": -7.07, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "angle": 7.17, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "angle": 3.13}]}, "tou20": {"translate": [{"x": -8.18, "y": 0.05, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 9.95, "y": -0.06, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -8.18, "y": 0.05, "curve": 0.25, "c3": 0.75}, {"time": 6, "x": 9.95, "y": -0.06, "curve": 0.25, "c3": 0.75}, {"time": 8, "x": -8.18, "y": 0.05}]}, "qbai12": {"translate": [{"x": 14.27, "y": 14.22, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": -15.62, "y": -28.39, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 26.11, "y": 31.1, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": -15.62, "y": -28.39, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 26.11, "y": 31.1, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": 14.27, "y": 14.22, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 4.6667, "x": -15.62, "y": -28.39, "curve": 0.25, "c3": 0.75}, {"time": 5.6667, "x": 26.11, "y": 31.1, "curve": 0.25, "c3": 0.75}, {"time": 6.6667, "x": -15.62, "y": -28.39, "curve": 0.25, "c3": 0.75}, {"time": 7.6667, "x": 26.11, "y": 31.1, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 8, "x": 14.27, "y": 14.22}]}, "qbai13": {"translate": [{"x": -0.4, "y": 11.48, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": -2.34, "y": 27.63, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 4.51, "y": -29.29, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": -2.34, "y": 27.63, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 4.51, "y": -29.29, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "x": -0.4, "y": 11.48, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 4.3333, "x": -2.34, "y": 27.63, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "x": 4.51, "y": -29.29, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "x": -2.34, "y": 27.63, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": 4.51, "y": -29.29, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 8, "x": -0.4, "y": 11.48}]}, "bone11": {"translate": [{}, {"time": 1, "x": 49.5}, {"time": 4}, {"time": 5, "x": 49.5}, {"time": 8}], "scale": [{}, {"time": 1, "x": 0.931, "y": 0.931}, {"time": 4}, {"time": 5, "x": 0.931, "y": 0.931}, {"time": 8}]}, "bone12": {"translate": [{"x": -12.53, "y": -10.25}, {"time": 1.9667}, {"time": 2, "x": -50.75, "y": -41.53}, {"time": 8, "x": -12.53, "y": -10.25}], "scale": [{"x": 1.17, "y": 1.17}, {"time": 1.9667, "x": 1.226, "y": 1.226}, {"time": 2}, {"time": 8, "x": 1.17, "y": 1.17}]}}, "deform": {"default": {"yao": {"yao": [{"offset": 54, "vertices": [-1.69577, -2.43774, 2.16005, 2.03769, -0.62004, -4.27136, 1.48358, 4.0531, -0.33387, -2.30002, 0.79886, 2.18246, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.33387, -2.30002, 2.29141, -0.38815, 0.79886, 2.18246, -0.62004, -4.27136, 4.25543, -0.72085, 4.24191, 0.79875, 1.48358, 4.0531, -1.69577, -2.43774, 2.85641, -0.81352, 2.16005, 2.03769], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "offset": 54, "vertices": [-3.39155, -4.87549, 4.3201, 4.07538, -1.24007, -8.54272, 2.96716, 8.1062, -0.66774, -4.60004, 1.59772, 4.36493, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.66774, -4.60004, 4.58282, -0.77631, 1.59772, 4.36493, -1.24007, -8.54272, 8.51086, -1.4417, 8.48383, 1.5975, 2.96716, 8.1062, -3.39155, -4.87549, 5.71283, -1.62704, 4.3201, 4.07538], "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "offset": 54, "vertices": [-1.69577, -2.43774, 2.16005, 2.03769, -0.62004, -4.27136, 1.48358, 4.0531, -0.33387, -2.30002, 0.79886, 2.18246, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.33387, -2.30002, 2.29141, -0.38815, 0.79886, 2.18246, -0.62004, -4.27136, 4.25543, -0.72085, 4.24191, 0.79875, 1.48358, 4.0531, -1.69577, -2.43774, 2.85641, -0.81352, 2.16005, 2.03769], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "offset": 54, "vertices": [-3.39155, -4.87549, 4.3201, 4.07538, -1.24007, -8.54272, 2.96716, 8.1062, -0.66774, -4.60004, 1.59772, 4.36493, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.66774, -4.60004, 4.58282, -0.77631, 1.59772, 4.36493, -1.24007, -8.54272, 8.51086, -1.4417, 8.48383, 1.5975, 2.96716, 8.1062, -3.39155, -4.87549, 5.71283, -1.62704, 4.3201, 4.07538], "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "offset": 54, "vertices": [-1.69577, -2.43774, 2.16005, 2.03769, -0.62004, -4.27136, 1.48358, 4.0531, -0.33387, -2.30002, 0.79886, 2.18246, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.33387, -2.30002, 2.29141, -0.38815, 0.79886, 2.18246, -0.62004, -4.27136, 4.25543, -0.72085, 4.24191, 0.79875, 1.48358, 4.0531, -1.69577, -2.43774, 2.85641, -0.81352, 2.16005, 2.03769], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.5, "curve": 0.25, "c3": 0.75}, {"time": 6, "offset": 54, "vertices": [-1.69577, -2.43774, 2.16005, 2.03769, -0.62004, -4.27136, 1.48358, 4.0531, -0.33387, -2.30002, 0.79886, 2.18246, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.33387, -2.30002, 2.29141, -0.38815, 0.79886, 2.18246, -0.62004, -4.27136, 4.25543, -0.72085, 4.24191, 0.79875, 1.48358, 4.0531, -1.69577, -2.43774, 2.85641, -0.81352, 2.16005, 2.03769], "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 6.5, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "offset": 54, "vertices": [-3.39155, -4.87549, 4.3201, 4.07538, -1.24007, -8.54272, 2.96716, 8.1062, -0.66774, -4.60004, 1.59772, 4.36493, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.66774, -4.60004, 4.58282, -0.77631, 1.59772, 4.36493, -1.24007, -8.54272, 8.51086, -1.4417, 8.48383, 1.5975, 2.96716, 8.1062, -3.39155, -4.87549, 5.71283, -1.62704, 4.3201, 4.07538], "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 8, "offset": 54, "vertices": [-1.69577, -2.43774, 2.16005, 2.03769, -0.62004, -4.27136, 1.48358, 4.0531, -0.33387, -2.30002, 0.79886, 2.18246, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.33387, -2.30002, 2.29141, -0.38815, 0.79886, 2.18246, -0.62004, -4.27136, 4.25543, -0.72085, 4.24191, 0.79875, 1.48358, 4.0531, -1.69577, -2.43774, 2.85641, -0.81352, 2.16005, 2.03769]}]}, "yy2": {"yy2": [{"curve": 0.25, "c3": 0.75}, {"time": 2, "offset": 1, "vertices": [-2.47601, 0, -2.47601, 0, -2.47601, 0, -2.47601], "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 6, "offset": 1, "vertices": [-2.47601, 0, -2.47601, 0, -2.47601, 0, -2.47601], "curve": 0.25, "c3": 0.75}, {"time": 8}]}}}}}}