import { _decorator, is<PERSON>ali<PERSON>, Label, Sprite, UITransform } from "cc";
import { UINode } from "../../../../../lib/ui/UINode";
import { BundleEnum } from "../../../../../game/bundleEnum/BundleEnum";
import { UIMgr } from "../../../../../lib/ui/UIMgr";
import { JsonMgr } from "../../../../../game/mgr/JsonMgr";
import Formate from "../../../../../lib/utils/Formate";
import ToolExt from "../../../../../game/common/ToolExt";
import { PupilModule } from "../../PupilModule";
import { DialogZero } from "../../../../../game/GameDefine";
import { <PERSON>upil<PERSON>ni } from "./PupilAni";
import { AudioMgr, AudioName } from "../../../../../../platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UIPupilAddResPop")
export class UIPupilAddResPop extends UINode {
  protected _isAddToBottom: boolean = true;
  public zOrder(): number {
    return DialogZero.UIPupilAddResPop;
  }
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_PUPIL}?prefab/ui/UIPupilAddResPop`;
  }
  protected dependOn(): BundleEnum[] {
    return [BundleEnum.BUNDLE_COMMON_UI];
  }

  private _pupilId: number = null;

  public init(param: number) {
    super.init(param);
    this._pupilId = param["pupilId"];
    log.log("init", this._pupilId);
  }

  protected async onEvtShow(): Promise<void> {
    AudioMgr.instance.playEffect(AudioName.Effect.获得奖励);
    let pupilMsg = PupilModule.data.allPupilMap[this._pupilId];

    // 设置弟子称号
    let talentId = pupilMsg.ownInfo.talentId;
    let sf = await this.assetMgr.loadSpriteFrameSync(BundleEnum.BUNDLE_G_PUPIL, `images/DZ_dizichenghao${talentId}`);
    if (!isValid(this.node)) {
      return;
    }
    this.getNode("attr_bg").getComponent(Sprite).spriteFrame = sf;

    PupilModule.service.setPupilNameNode(this.getNode("pupil_name_lab"), pupilMsg.ownInfo.nameId);

    // 天生属性
    let init_attr = ToolExt.traAwardItemMapList(pupilMsg.ownInfo.initAttrList);
    this.getNode("base_attr_lay").removeAllChildren();
    let contentSize = this.getNode("base_attr_lay").getComponent(UITransform).contentSize;
    this.getNode("base_attr_lay")
      .getComponent(UITransform)
      .setContentSize(init_attr.length == 1 ? contentSize.x / 2 : contentSize.x, contentSize.y);

    for (let i = 0; i < init_attr.length; i++) {
      let attr_node = ToolExt.clone(this.getNode("attr_node"), this);

      attr_node["attr_name_lab"].getComponent(Label).string = `${
        JsonMgr.instance.jsonList.c_attribute[init_attr[i].id]["name"]
      }`;
      attr_node["attr_num_lab"].getComponent(Label).string = `+${Formate.formatDecimal(init_attr[i].num * 100)}%`;
      attr_node.active = true;
      this.getNode("base_attr_lay").addChild(attr_node);
    }

    // 形象
    this.getNode("PupilAni")
      .getComponent(PupilAni)
      .setAniByNameId(pupilMsg.ownInfo.nameId, pupilMsg.ownInfo.adultAttrList.length);
  }

  private on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
}
