import { _decorator, director, instantiate, v3, Node, tween, Vec3 } from "cc";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
import { NodeTool } from "../lib/utils/NodeTool";
import { Sleep } from "../game/GameDefine";
import FmUtils from "../lib/utils/FmUtils";
const { ccclass, property } = _decorator;

/**
 * 道具飞行api
 */
@ccclass("TopGetItem")
export class TopGetItem extends BaseCtrl {
  @property(Node)
  nodeAni: Node;

  @property(Node)
  nodeItem: Node;

  @property(Node)
  nodeTypeList: Node[] = [];

  private _nodeUIMain: Node;
  private _playItemIdList: number[] = [];
  private _wPosition: Vec3;

  init(args: any): void {
    this._playItemIdList = args.itemList;
    this._wPosition = args.startPos;
  }

  protected onLoad(): void {
    this._nodeUIMain = NodeTool.findByName(director.getScene(), "UIMain");
  }

  protected async start(): Promise<void> {
    super.start();

    this.node.setWorldPosition(this._wPosition);

    for (let i = 0; i + 1 < this._playItemIdList.length; i += 2) {
      const nodeItem = instantiate(this.nodeItem);
      nodeItem.active = true;
      nodeItem.parent = this.nodeAni;

      FmUtils.setItemNode(nodeItem, this._playItemIdList[i], this._playItemIdList[i + 1]);
      nodeItem.setPosition(v3(0, 0, 0));
    }

    // 默认值
    const posList = [
      v3(0, 100, 0),
      v3(-75, 80, 0),
      v3(80, 60, 0),
      v3(75, 150, 0),
      v3(30, 15, 0),
      v3(-35, 130, 0),
      v3(23, 180, 0),
    ];

    // 随机阵型
    const nodeType = this.nodeTypeList[Math.floor(Math.random() * this.nodeTypeList.length)];
    if (nodeType) {
      for (let i = 0; i < nodeType.children.length; i++) {
        posList[i] = nodeType.children[i].getPosition();
      }
    }

    // 终点
    const nodeEnd = NodeTool.findByName(this._nodeUIMain, "btn_knapsack");
    const posEnd = nodeEnd.getWorldPosition();

    for (let i = 0; i < this.nodeAni.children.length; i++) {
      tween(this.nodeAni.children[i])
        .to(0.3, { position: posList[i % posList.length] }, { easing: "sineIn" })
        .delay(0.3)
        // .by(0.25, { position: v3(0, 20, 0) }, { easing: "sineInOut" })
        // .by(0.25, { position: v3(0, -20, 0) }, { easing: "sineInOut" })
        // .by(0.25, { position: v3(0, 20, 0) }, { easing: "sineInOut" })
        .to(0.3, { worldPosition: posEnd, scale: v3(0.4, 0.4, 1) }, { easing: "sineOut" })
        .delay(0.1)
        .destroySelf()
        .start();
      await Sleep(0.1);
    }
    await Sleep(2);
    this.closeBack();
  }
}
