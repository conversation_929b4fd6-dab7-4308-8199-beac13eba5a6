[{"__type__": "cc.Prefab", "_name": "UIFundMain", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "UIFundMain", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [{"__id__": 240}], "_prefab": {"__id__": 242}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "main", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 9}, {"__id__": 217}, {"__id__": 229}], "_active": true, "_components": [{"__id__": 237}], "_prefab": {"__id__": 239}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_JJ", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}, {"__id__": 6}], "_prefab": {"__id__": 8}, "_lpos": {"__type__": "cc.Vec3", "x": 5, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 5}, "_contentSize": {"__type__": "cc.Size", "width": 729, "height": 1220}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "57S3fUvZZE0rNUweymG2IL"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 7}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "fd901f6d-8cd0-4bb6-8552-8b9d23889fc1@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f3UT/+Xh5Io6t+q4WcW0xP"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6fwYNXOTlJDIFOQeY0mgJ5", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "TY_bg_tanchuang_huodong_neikuang", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 10}], "_active": true, "_components": [{"__id__": 212}, {"__id__": 214}], "_prefab": {"__id__": 216}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -25, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "ScrollView_list", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 9}, "_children": [{"__id__": 11}], "_active": true, "_components": [{"__id__": 207}, {"__id__": 209}], "_prefab": {"__id__": 211}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 535.5439999999999, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "view_list", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 10}, "_children": [{"__id__": 12}], "_active": true, "_components": [{"__id__": 200}, {"__id__": 202}, {"__id__": 204}], "_prefab": {"__id__": 206}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "content_banner", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [{"__id__": 13}, {"__id__": 41}, {"__id__": 65}, {"__id__": 91}, {"__id__": 117}, {"__id__": 143}, {"__id__": 169}], "_active": true, "_components": [{"__id__": 195}, {"__id__": 197}], "_prefab": {"__id__": 199}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 12}, "_prefab": {"__id__": 14}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 13}, "asset": {"__uuid__": "172c1e8d-f70f-4aba-ad20-05ef8a9b30f8", "__expectedType__": "cc.Prefab"}, "fileId": "bbLgZIUxRIf632Adsqj32u", "instance": {"__id__": 15}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "1dmfZlMKBALpPHh/KV2K6/", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 16}, {"__id__": 18}, {"__id__": 20}, {"__id__": 22}, {"__id__": 24}, {"__id__": 26}, {"__id__": 28}, {"__id__": 30}, {"__id__": 32}, {"__id__": 34}, {"__id__": 36}, {"__id__": 38}, {"__id__": 40}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 17}, "propertyPath": ["_name"], "value": "FundBanner001"}, {"__type__": "cc.TargetInfo", "localID": ["bbLgZIUxRIf632Adsqj32u"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 19}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -86, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["bbLgZIUxRIf632Adsqj32u"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 21}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["bbLgZIUxRIf632Adsqj32u"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 23}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["bbLgZIUxRIf632Adsqj32u"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 25}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 638, "height": 162}}, {"__type__": "cc.TargetInfo", "localID": ["6fgUHIEiNFAqbDciHfrDJs"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 27}, "propertyPath": ["_spriteFrame"], "value": {"__uuid__": "428375f9-7699-4ff3-8510-0d2bd238b52d@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["8dnc16D4JKDL7it/kPdq3g"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 29}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 214, "height": 68.78}}, {"__type__": "cc.TargetInfo", "localID": ["cfTkACS3FGObLH7tUqUWFn"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 31}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 280, "y": 25, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["f6/EnC8HNHEZuXSk9oiH1L"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 33}, "propertyPath": ["uiName"], "value": "UIDayRecharge"}, {"__type__": "cc.TargetInfo", "localID": ["430jFENr1MrpLovlYRDNw5"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 35}, "propertyPath": ["_active"], "value": true}, {"__type__": "cc.TargetInfo", "localID": ["bbLgZIUxRIf632Adsqj32u"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 37}, "propertyPath": ["activityId"], "value": 10701}, {"__type__": "cc.TargetInfo", "localID": ["430jFENr1MrpLovlYRDNw5"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 39}, "propertyPath": ["_string"], "value": "每日礼包"}, {"__type__": "cc.TargetInfo", "localID": ["a7H5hi8H5OSY6vGPh73zVa"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 27}, "propertyPath": ["_sizeMode"], "value": 1}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 12}, "_prefab": {"__id__": 42}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 41}, "asset": {"__uuid__": "172c1e8d-f70f-4aba-ad20-05ef8a9b30f8", "__expectedType__": "cc.Prefab"}, "fileId": "bbLgZIUxRIf632Adsqj32u", "instance": {"__id__": 43}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "47TDes+M9EZ6CI4C4lG6wV", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 44}, {"__id__": 46}, {"__id__": 48}, {"__id__": 50}, {"__id__": 52}, {"__id__": 54}, {"__id__": 56}, {"__id__": 58}, {"__id__": 60}, {"__id__": 62}, {"__id__": 64}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 45}, "propertyPath": ["_name"], "value": "FundBanner002"}, {"__type__": "cc.TargetInfo", "localID": ["bbLgZIUxRIf632Adsqj32u"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 47}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -273, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["bbLgZIUxRIf632Adsqj32u"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 49}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["bbLgZIUxRIf632Adsqj32u"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 51}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["bbLgZIUxRIf632Adsqj32u"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 53}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 638, "height": 162}}, {"__type__": "cc.TargetInfo", "localID": ["6fgUHIEiNFAqbDciHfrDJs"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 55}, "propertyPath": ["_spriteFrame"], "value": {"__uuid__": "69cd2c5c-cfb7-49a3-95b6-bd3eee013baf@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["8dnc16D4JKDL7it/kPdq3g"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 57}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 161, "height": 68.78}}, {"__type__": "cc.TargetInfo", "localID": ["cfTkACS3FGObLH7tUqUWFn"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 59}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 288.037, "y": 25, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["f6/EnC8HNHEZuXSk9oiH1L"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 61}, "propertyPath": ["uiName"], "value": "UICardMain"}, {"__type__": "cc.TargetInfo", "localID": ["430jFENr1MrpLovlYRDNw5"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 63}, "propertyPath": ["_string"], "value": "特权卡"}, {"__type__": "cc.TargetInfo", "localID": ["a7H5hi8H5OSY6vGPh73zVa"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 55}, "propertyPath": ["_sizeMode"], "value": 1}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 12}, "_prefab": {"__id__": 66}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 65}, "asset": {"__uuid__": "172c1e8d-f70f-4aba-ad20-05ef8a9b30f8", "__expectedType__": "cc.Prefab"}, "fileId": "bbLgZIUxRIf632Adsqj32u", "instance": {"__id__": 67}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "f7gzNvU4VFO5ehXjIbYBmB", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 68}, {"__id__": 70}, {"__id__": 72}, {"__id__": 74}, {"__id__": 76}, {"__id__": 78}, {"__id__": 80}, {"__id__": 82}, {"__id__": 84}, {"__id__": 86}, {"__id__": 88}, {"__id__": 90}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 69}, "propertyPath": ["_name"], "value": "FundBanner003"}, {"__type__": "cc.TargetInfo", "localID": ["bbLgZIUxRIf632Adsqj32u"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 71}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -460, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["bbLgZIUxRIf632Adsqj32u"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 73}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["bbLgZIUxRIf632Adsqj32u"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 75}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["bbLgZIUxRIf632Adsqj32u"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 77}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 638, "height": 162}}, {"__type__": "cc.TargetInfo", "localID": ["6fgUHIEiNFAqbDciHfrDJs"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 79}, "propertyPath": ["_spriteFrame"], "value": {"__uuid__": "34ffb91a-d460-4482-a3fd-8681a0331dbd@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["8dnc16D4JKDL7it/kPdq3g"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 81}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 375.64996337890625, "height": 68.78}}, {"__type__": "cc.TargetInfo", "localID": ["cfTkACS3FGObLH7tUqUWFn"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 83}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 280, "y": 25, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["f6/EnC8HNHEZuXSk9oiH1L"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 85}, "propertyPath": ["uiName"], "value": "UIFriendFund"}, {"__type__": "cc.TargetInfo", "localID": ["430jFENr1MrpLovlYRDNw5"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 87}, "propertyPath": ["activityId"], "value": 10403}, {"__type__": "cc.TargetInfo", "localID": ["430jFENr1MrpLovlYRDNw5"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 89}, "propertyPath": ["_string"], "value": "仙友 - 结识基金"}, {"__type__": "cc.TargetInfo", "localID": ["a7H5hi8H5OSY6vGPh73zVa"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 79}, "propertyPath": ["_sizeMode"], "value": 1}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 12}, "_prefab": {"__id__": 92}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 91}, "asset": {"__uuid__": "172c1e8d-f70f-4aba-ad20-05ef8a9b30f8", "__expectedType__": "cc.Prefab"}, "fileId": "bbLgZIUxRIf632Adsqj32u", "instance": {"__id__": 93}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "abIcsjaYZInKebnOyG5iAb", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 94}, {"__id__": 96}, {"__id__": 98}, {"__id__": 100}, {"__id__": 102}, {"__id__": 104}, {"__id__": 106}, {"__id__": 108}, {"__id__": 110}, {"__id__": 112}, {"__id__": 114}, {"__id__": 116}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 95}, "propertyPath": ["_name"], "value": "FundBanner004"}, {"__type__": "cc.TargetInfo", "localID": ["bbLgZIUxRIf632Adsqj32u"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 97}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -647, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["bbLgZIUxRIf632Adsqj32u"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 99}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["bbLgZIUxRIf632Adsqj32u"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 101}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["bbLgZIUxRIf632Adsqj32u"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 103}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 638, "height": 162}}, {"__type__": "cc.TargetInfo", "localID": ["6fgUHIEiNFAqbDciHfrDJs"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 105}, "propertyPath": ["_spriteFrame"], "value": {"__uuid__": "8287db96-690b-4f87-bce0-ca25b1eca512@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["8dnc16D4JKDL7it/kPdq3g"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 107}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 375.64996337890625, "height": 68.78}}, {"__type__": "cc.TargetInfo", "localID": ["cfTkACS3FGObLH7tUqUWFn"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 109}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 280, "y": 25, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["f6/EnC8HNHEZuXSk9oiH1L"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 111}, "propertyPath": ["uiName"], "value": "UIRoleFund"}, {"__type__": "cc.TargetInfo", "localID": ["430jFENr1MrpLovlYRDNw5"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 113}, "propertyPath": ["activityId"], "value": 10401}, {"__type__": "cc.TargetInfo", "localID": ["430jFENr1MrpLovlYRDNw5"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 115}, "propertyPath": ["_string"], "value": "角色 - 成长基金"}, {"__type__": "cc.TargetInfo", "localID": ["a7H5hi8H5OSY6vGPh73zVa"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 105}, "propertyPath": ["_sizeMode"], "value": 1}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 12}, "_prefab": {"__id__": 118}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 117}, "asset": {"__uuid__": "172c1e8d-f70f-4aba-ad20-05ef8a9b30f8", "__expectedType__": "cc.Prefab"}, "fileId": "bbLgZIUxRIf632Adsqj32u", "instance": {"__id__": 119}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "eaugIrsrVAl6iNu937XlQc", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 120}, {"__id__": 122}, {"__id__": 124}, {"__id__": 126}, {"__id__": 128}, {"__id__": 130}, {"__id__": 132}, {"__id__": 134}, {"__id__": 136}, {"__id__": 138}, {"__id__": 140}, {"__id__": 142}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 121}, "propertyPath": ["_name"], "value": "FundBanner005"}, {"__type__": "cc.TargetInfo", "localID": ["bbLgZIUxRIf632Adsqj32u"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 123}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -834, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["bbLgZIUxRIf632Adsqj32u"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 125}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["bbLgZIUxRIf632Adsqj32u"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 127}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["bbLgZIUxRIf632Adsqj32u"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 129}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 640, "height": 162}}, {"__type__": "cc.TargetInfo", "localID": ["6fgUHIEiNFAqbDciHfrDJs"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 131}, "propertyPath": ["_spriteFrame"], "value": {"__uuid__": "a3b45411-3e1c-4f01-b5fd-02eec6e6e9ee@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["8dnc16D4JKDL7it/kPdq3g"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 133}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 375.64996337890625, "height": 68.78}}, {"__type__": "cc.TargetInfo", "localID": ["cfTkACS3FGObLH7tUqUWFn"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 135}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 280, "y": 25, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["f6/EnC8HNHEZuXSk9oiH1L"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 137}, "propertyPath": ["uiName"], "value": "UIDhamaFund"}, {"__type__": "cc.TargetInfo", "localID": ["430jFENr1MrpLovlYRDNw5"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 139}, "propertyPath": ["activityId"], "value": 10404}, {"__type__": "cc.TargetInfo", "localID": ["430jFENr1MrpLovlYRDNw5"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 141}, "propertyPath": ["_string"], "value": "至宝 - 进阶基金"}, {"__type__": "cc.TargetInfo", "localID": ["a7H5hi8H5OSY6vGPh73zVa"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 131}, "propertyPath": ["_sizeMode"], "value": 1}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 12}, "_prefab": {"__id__": 144}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 143}, "asset": {"__uuid__": "172c1e8d-f70f-4aba-ad20-05ef8a9b30f8", "__expectedType__": "cc.Prefab"}, "fileId": "bbLgZIUxRIf632Adsqj32u", "instance": {"__id__": 145}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "bayGUCPmVH7Jdt6hxzGhGR", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 146}, {"__id__": 148}, {"__id__": 150}, {"__id__": 152}, {"__id__": 154}, {"__id__": 156}, {"__id__": 158}, {"__id__": 160}, {"__id__": 162}, {"__id__": 164}, {"__id__": 166}, {"__id__": 168}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 147}, "propertyPath": ["_name"], "value": "FundBanner006"}, {"__type__": "cc.TargetInfo", "localID": ["bbLgZIUxRIf632Adsqj32u"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 149}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -1021, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["bbLgZIUxRIf632Adsqj32u"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 151}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["bbLgZIUxRIf632Adsqj32u"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 153}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["bbLgZIUxRIf632Adsqj32u"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 155}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 638, "height": 162}}, {"__type__": "cc.TargetInfo", "localID": ["6fgUHIEiNFAqbDciHfrDJs"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 157}, "propertyPath": ["_spriteFrame"], "value": {"__uuid__": "f5d98ca3-93eb-4c57-a384-b0bfb27bdc89@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["8dnc16D4JKDL7it/kPdq3g"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 159}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 375.64996337890625, "height": 68.78}}, {"__type__": "cc.TargetInfo", "localID": ["cfTkACS3FGObLH7tUqUWFn"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 161}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 280, "y": 25, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["f6/EnC8HNHEZuXSk9oiH1L"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 163}, "propertyPath": ["uiName"], "value": "UIHeroFund"}, {"__type__": "cc.TargetInfo", "localID": ["430jFENr1MrpLovlYRDNw5"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 165}, "propertyPath": ["activityId"], "value": 10402}, {"__type__": "cc.TargetInfo", "localID": ["430jFENr1MrpLovlYRDNw5"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 167}, "propertyPath": ["_string"], "value": "战将 - 等级基金"}, {"__type__": "cc.TargetInfo", "localID": ["a7H5hi8H5OSY6vGPh73zVa"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 157}, "propertyPath": ["_sizeMode"], "value": 1}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 12}, "_prefab": {"__id__": 170}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 169}, "asset": {"__uuid__": "172c1e8d-f70f-4aba-ad20-05ef8a9b30f8", "__expectedType__": "cc.Prefab"}, "fileId": "bbLgZIUxRIf632Adsqj32u", "instance": {"__id__": 171}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "155F3rBDpOsZm3cEVbn8cB", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 172}, {"__id__": 174}, {"__id__": 176}, {"__id__": 178}, {"__id__": 180}, {"__id__": 182}, {"__id__": 184}, {"__id__": 186}, {"__id__": 188}, {"__id__": 190}, {"__id__": 192}, {"__id__": 194}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 173}, "propertyPath": ["_name"], "value": "FundBanner007"}, {"__type__": "cc.TargetInfo", "localID": ["bbLgZIUxRIf632Adsqj32u"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 175}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -1208, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["bbLgZIUxRIf632Adsqj32u"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 177}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["bbLgZIUxRIf632Adsqj32u"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 179}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["bbLgZIUxRIf632Adsqj32u"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 181}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 638, "height": 162}}, {"__type__": "cc.TargetInfo", "localID": ["6fgUHIEiNFAqbDciHfrDJs"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 183}, "propertyPath": ["_spriteFrame"], "value": {"__uuid__": "29b25952-ffb5-47ad-bae1-8cd88ca8d7a3@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["8dnc16D4JKDL7it/kPdq3g"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 185}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 375.64996337890625, "height": 68.78}}, {"__type__": "cc.TargetInfo", "localID": ["cfTkACS3FGObLH7tUqUWFn"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 187}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 280, "y": 25, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["f6/EnC8HNHEZuXSk9oiH1L"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 189}, "propertyPath": ["uiName"], "value": "UILevelFund"}, {"__type__": "cc.TargetInfo", "localID": ["430jFENr1MrpLovlYRDNw5"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 191}, "propertyPath": ["activityId"], "value": 10405}, {"__type__": "cc.TargetInfo", "localID": ["430jFENr1MrpLovlYRDNw5"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 193}, "propertyPath": ["_string"], "value": "关卡 - 冲锋基金"}, {"__type__": "cc.TargetInfo", "localID": ["a7H5hi8H5OSY6vGPh73zVa"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 183}, "propertyPath": ["_sizeMode"], "value": 1}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 196}, "_contentSize": {"__type__": "cc.Size", "width": 733, "height": 1289}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "feNmOfdvlMhIO5eQOkpqZM"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 198}, "_resizeMode": 1, "_layoutType": 2, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 5, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 25, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8cEsTJ9URBaIfbluPQlm7m"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ffqjH/oiNHkZIXsjx2a85P", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": true, "__prefab": {"__id__": 201}, "_contentSize": {"__type__": "cc.Size", "width": 733, "height": 1100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dfe83FZChFs5N8qtBlqh6Z"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": false, "__prefab": {"__id__": 203}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "34QlVsAXNLGp1MiRG4z3g7"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": false, "__prefab": {"__id__": 205}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "33CiV3vgRLSKm6h/egiNzV"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "30bxdRb0dOY69PdrJwU/Cg", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 10}, "_enabled": true, "__prefab": {"__id__": 208}, "_contentSize": {"__type__": "cc.Size", "width": 733, "height": 1100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9e/4eEiVVFiI3iQCrnIsm/"}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 10}, "_enabled": true, "__prefab": {"__id__": 210}, "bounceDuration": 0.23, "brake": 0.75, "elastic": true, "inertia": true, "horizontal": false, "vertical": true, "cancelInnerEvents": true, "scrollEvents": [], "_content": {"__id__": 12}, "_horizontalScrollBar": null, "_verticalScrollBar": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2faSf4qSFFKaT0klmYWFM7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8cNKOPJZNA0ZwV9xVEsjzi", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 9}, "_enabled": true, "__prefab": {"__id__": 213}, "_contentSize": {"__type__": "cc.Size", "width": 661, "height": 1133}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cd0tQFcuhGx4FsFpz+K6gG"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 9}, "_enabled": true, "__prefab": {"__id__": 215}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "250357c5-736c-4c9c-a75a-b1b8647d8cfd@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "80dndoCGFGOL40sB/FM+aA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fa/GyZUBtKuavij2qiPB82", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "TY_bg_zhuangshi_02", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 218}], "_active": true, "_components": [{"__id__": 224}, {"__id__": 226}], "_prefab": {"__id__": 228}, "_lpos": {"__type__": "cc.Vec3", "x": -156.497, "y": 537.745, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "zi_meiri<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 217}, "_children": [], "_active": true, "_components": [{"__id__": 219}, {"__id__": 221}], "_prefab": {"__id__": 223}, "_lpos": {"__type__": "cc.Vec3", "x": -191.361, "y": 0.303, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 218}, "_enabled": true, "__prefab": {"__id__": 220}, "_contentSize": {"__type__": "cc.Size", "width": 334, "height": 124.74}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "83uHMNvpBCGISMrvoQh1eo"}, {"__type__": "cc.RichText", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 218}, "_enabled": true, "__prefab": {"__id__": 222}, "_lineHeight": 99, "_string": "<outline color=#ffa200 width=3><color=#ffffff><size=91>充</size></color></outline><outline color=#ffa200 width=3><color=#ffffff><size=77>值好礼</size></color></outline>", "_horizontalAlign": 1, "_verticalAlign": 2, "_fontSize": 99, "_fontColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_maxWidth": 0, "_fontFamily": "<PERSON><PERSON>", "_font": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_userDefinedFont": {"__uuid__": "a785388a-2293-4ed6-b6ae-caf5e2210f25", "__expectedType__": "cc.TTFFont"}, "_cacheMode": 0, "_imageAtlas": null, "_handleTouchEvent": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a1LBObJPxCwoCV3rIF0Q7H"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8eP5xlGFlKHqhp20Bkka2y", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 217}, "_enabled": true, "__prefab": {"__id__": 225}, "_contentSize": {"__type__": "cc.Size", "width": 407, "height": 176}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e302cPIfBNN66aLAVWiI/z"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 217}, "_enabled": true, "__prefab": {"__id__": 227}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "41d4e0c2-61eb-4d08-94fe-e48ab611739d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "afjUQvUiFJTaQbfQtASeRN"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "59zlz3atRGTapI8NmutklC", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_close", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 230}, {"__id__": 232}, {"__id__": 234}], "_prefab": {"__id__": 236}, "_lpos": {"__type__": "cc.Vec3", "x": 332.082, "y": 571.278, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 229}, "_enabled": true, "__prefab": {"__id__": 231}, "_contentSize": {"__type__": "cc.Size", "width": 78, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "69UtiwgFlKxKgwIPxoULNC"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 229}, "_enabled": true, "__prefab": {"__id__": 233}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8e27d803-97dc-403c-bae5-609f4c45539b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7cdbWx+oBHt4xuRe0oCWOX"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 229}, "_enabled": true, "__prefab": {"__id__": 235}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "deTgZTOpxESr/U+/68EB1S"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "eal2wNESVOyoB8okffIdzV", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 238}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4c9UdctptErLmUI+CEveXJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f3naOb5ERA6I++dwL72WA5", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 241}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1203}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "87esEexbdHCJ+SA6QWD8Nh"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "03V21guM1IGqEstqo+CVnG", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": [{"__id__": 169}, {"__id__": 143}, {"__id__": 117}, {"__id__": 91}, {"__id__": 65}, {"__id__": 41}, {"__id__": 13}]}]