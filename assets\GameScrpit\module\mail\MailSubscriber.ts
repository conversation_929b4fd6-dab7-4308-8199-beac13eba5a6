import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../game/mgr/ApiHandler";
import { MailMessage } from "../../game/net/protocol/Mail";
import { MailSubCmd } from "./MailConstant";
import { MailModule } from "./MailModule";

export class MailSubscriber {
  // 同步邮件
  private pushSendMailCallback() {
    MailModule.api.syncSysMail();
    MailModule.api.syncDailyMail();
  }

  public register() {
    //订阅服务器消息
    ApiHandler.instance.subscribe(MailMessage, MailSubCmd.pushSendMail, this.pushSendMailCallback);
  }

  public unRegister() {
    //取消订阅服务器消息
    ApiHandler.instance.unSubscribe(MailSubCmd.pushSendMail, this.pushSendMailCallback);
  }
}
