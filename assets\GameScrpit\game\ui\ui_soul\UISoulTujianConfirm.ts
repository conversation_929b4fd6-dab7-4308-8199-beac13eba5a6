import { _decorator, Component, Node } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Thu Dec 19 2024 20:11:26 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/ui_soul/UISoulTujianConfirm.ts
 *
 */

@ccclass("UISoulTujianConfirm")
export class UISoulTujianConfirm extends UINode {
  protected _openAct: boolean = true;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_SOUL}?prefab/ui/UISoulTujianConfirm`;
  }
  dialog_args: any;
  //=================================================
  public init(args: any): void {
    super.init(args);
    this.dialog_args = args;
  }
  protected onEvtShow(): void {
    super.onEvtShow();

    AudioMgr.instance.playEffect(AudioName.Effect.二级弹窗提示);
  }
  private on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
  private on_click_btn_confirm() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    UIMgr.instance.back();
    if (this.dialog_args.callback) {
      this.dialog_args.callback(true);
    }
  }
  private on_click_btn_cancel() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    UIMgr.instance.back();
    if (this.dialog_args.callback) {
      this.dialog_args.callback(false);
    }
  }
}
