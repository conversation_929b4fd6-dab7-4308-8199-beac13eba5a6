{"skeleton": {"hash": "KD1XBdhV5PLkcprkXfUMWK6RiEY", "spine": "3.8.99", "x": -485.53, "y": -108.23, "width": 821, "height": 656, "images": "./images/", "audio": "D:/spine导出/灵兽动画/鸾鸟"}, "bones": [{"name": "root", "scaleX": -1}, {"name": "bone", "parent": "root", "x": 12.82, "y": -16.25, "scaleX": 0.6555, "scaleY": 0.6555, "color": "b90505ff"}, {"name": "bone2", "parent": "bone", "length": 126.74, "rotation": -0.43, "x": -43.26, "y": 294.25}, {"name": "bone3", "parent": "bone2", "length": 42.03, "rotation": 158.34, "x": -5.7, "y": 0.27}, {"name": "bone4", "parent": "bone3", "length": 47.16, "rotation": -20, "x": 42.03}, {"name": "bone5", "parent": "bone4", "length": 45.16, "rotation": -29.7, "x": 47.16}, {"name": "bone6", "parent": "bone5", "length": 33.13, "rotation": -4.41, "x": 45.16}, {"name": "bone7", "parent": "bone6", "length": 25.77, "rotation": 47.39, "x": 33.13}, {"name": "bone8", "parent": "bone7", "length": 24.84, "rotation": 117.51, "x": 25.77}, {"name": "bone9", "parent": "bone8", "x": 14.64, "y": -8.92}, {"name": "bone10", "parent": "bone7", "length": 16.24, "rotation": -104.18, "x": 24.1, "y": -3.72}, {"name": "bone11", "parent": "bone10", "length": 13.44, "rotation": 23.74, "x": 16.24}, {"name": "bone12", "parent": "bone11", "length": 14.52, "rotation": 2.32, "x": 13.44}, {"name": "bone13", "parent": "bone2", "length": 45.63, "rotation": -29.38, "x": 6.41, "y": -6.31}, {"name": "bone14", "parent": "bone13", "length": 46.52, "rotation": -6.12, "x": 45.63}, {"name": "bone15", "parent": "bone14", "length": 20.23, "rotation": -45.33, "x": 45.57, "y": -11.13}, {"name": "bone16", "parent": "bone15", "length": 22.52, "rotation": -36.19, "x": 20.23}, {"name": "bone17", "parent": "bone14", "length": 27.75, "rotation": 31.95, "x": 42.08, "y": 10.07}, {"name": "bone18", "parent": "bone17", "length": 25.85, "rotation": 26.72, "x": 27.75}, {"name": "bone19", "parent": "bone3", "length": 90.3, "rotation": 156.24, "x": -5.05, "y": 30.8}, {"name": "bone20", "parent": "bone19", "length": 103.4, "rotation": -147.77, "x": 90.13, "y": 0.16}, {"name": "bone21", "parent": "bone20", "length": 17.14, "rotation": 38.07, "x": 103.4}, {"name": "bone22", "parent": "bone21", "length": 15.82, "rotation": 56.09, "x": 17.14}, {"name": "bone23", "parent": "bone20", "length": 10.16, "rotation": 128.4, "x": 102.48, "y": 6.83}, {"name": "bone24", "parent": "bone23", "length": 8.26, "rotation": -48.4, "x": 10.06, "y": -0.21}, {"name": "bone25", "parent": "bone3", "length": 88.64, "rotation": 150.88, "x": 1.33, "y": 46.11}, {"name": "bone26", "parent": "bone25", "length": 90.58, "rotation": -64.66, "x": 88.64}, {"name": "bone27", "parent": "bone26", "length": 14.4, "rotation": 12.26, "x": 90.58}, {"name": "bone28", "parent": "bone27", "length": 11.57, "rotation": 30.62, "x": 14.4}, {"name": "bone29", "parent": "bone28", "length": 8.78, "rotation": 8.89, "x": 11.57}, {"name": "bone30", "parent": "bone26", "length": 10.51, "rotation": 130.79, "x": 90.22, "y": 3.84}, {"name": "bone31", "parent": "bone30", "length": 10.66, "rotation": -68.55, "x": 10.51}, {"name": "light", "parent": "root", "x": -2.63, "y": 201.35, "color": "00f1ffff"}, {"name": "bone32", "parent": "bone3", "length": 59.84, "rotation": -146.14, "x": 8.91, "y": -34.99}, {"name": "bone33", "parent": "bone32", "length": 70.01, "rotation": 63.59, "x": 58.17, "y": 0.97}, {"name": "bone34", "parent": "bone33", "length": 65.91, "rotation": 13.58, "x": 70.01}, {"name": "bone35", "parent": "bone32", "length": 41.36, "rotation": -33.41, "x": 63.57, "y": -26.96}, {"name": "bone36", "parent": "bone35", "length": 45.91, "rotation": -1.85, "x": 41.36}, {"name": "bone37", "parent": "bone33", "length": 60.66, "rotation": -80.55, "x": 31.29, "y": -47.33}, {"name": "bone38", "parent": "bone37", "length": 43.75, "rotation": 28.18, "x": 60.66}, {"name": "bone39", "parent": "bone38", "length": 59.13, "rotation": 20.76, "x": 43.75}, {"name": "bone40", "parent": "bone34", "length": 58.98, "rotation": -60.52, "x": 35.85, "y": -24.36}, {"name": "bone41", "parent": "bone40", "length": 74.86, "rotation": 12.28, "x": 58.98}, {"name": "bone42", "parent": "bone41", "length": 85.25, "rotation": 25.67, "x": 74.86}, {"name": "bone43", "parent": "bone3", "length": 41.99, "rotation": -87.86, "x": 70.05, "y": -0.56}, {"name": "bone44", "parent": "bone43", "length": 40.39, "rotation": 25.9, "x": 41.99}, {"name": "bone45", "parent": "bone44", "length": 69.46, "rotation": -0.76, "x": 40.39}, {"name": "bone46", "parent": "bone45", "length": 49.01, "rotation": -12.98, "x": 38.21, "y": -11.89}, {"name": "bone47", "parent": "bone46", "length": 57.16, "rotation": 28.54, "x": 49.01}, {"name": "bone48", "parent": "bone47", "length": 60.02, "rotation": 15.24, "x": 57.16}, {"name": "bone49", "parent": "bone44", "length": 41.48, "rotation": -38.58, "x": 26.48, "y": -18.92}, {"name": "bone50", "parent": "bone49", "length": 45.5, "rotation": 17.94, "x": 41.48}, {"name": "bone51", "parent": "bone50", "length": 51, "rotation": 5.21, "x": 45.5}, {"name": "bone52", "parent": "bone43", "length": 36.26, "rotation": -44.97, "x": 29.59, "y": -20.11}, {"name": "bone53", "parent": "bone52", "length": 36.81, "rotation": 21.07, "x": 36.26}, {"name": "bone54", "parent": "bone53", "length": 33.35, "rotation": 21.04, "x": 36.81}, {"name": "bone55", "parent": "bone8", "length": 44.47, "rotation": -165.01, "x": -10.84, "y": -10.3}, {"name": "bone56", "parent": "bone55", "length": 36.95, "rotation": -5.87, "x": 44.47}, {"name": "bone57", "parent": "bone56", "length": 35.39, "rotation": -37.34, "x": 36.95}, {"name": "bone58", "parent": "bone57", "length": 26.6, "rotation": -28.76, "x": 35.39}, {"name": "bone59", "parent": "bone8", "length": 22.23, "rotation": 134.58, "x": -4.14, "y": -7.48}, {"name": "bone60", "parent": "bone59", "length": 30.29, "rotation": 41.66, "x": 22.23}, {"name": "bone61", "parent": "bone60", "length": 23.68, "rotation": -34.02, "x": 30.13, "y": -0.01}, {"name": "bone62", "parent": "bone8", "length": 31.55, "rotation": 163.32, "x": 9.18, "y": -26.5}, {"name": "bone63", "parent": "bone62", "length": 29.66, "rotation": -22.29, "x": 31.55}, {"name": "bone64", "parent": "bone63", "length": 31.94, "rotation": 27.19, "x": 29.42, "y": -0.2}, {"name": "bone65", "parent": "bone64", "length": 37.1, "rotation": 23.82, "x": 31.75, "y": 0.12}, {"name": "bone66", "parent": "bone14", "length": 97.4, "rotation": 11.41, "x": 25.13, "y": -11.99, "color": "3cff2bff"}, {"name": "bone67", "parent": "bone66", "length": 113.81, "rotation": 31.5, "x": 98.93, "y": -1.36, "color": "6bff14ff"}, {"name": "bone68", "parent": "bone67", "length": 100.27, "rotation": -28.02, "x": 113.2, "y": -0.48, "color": "6bff14ff"}, {"name": "bone69", "parent": "bone68", "length": 94.38, "rotation": -39.87, "x": 99.87, "y": 0.27, "color": "6bff14ff"}, {"name": "bone70", "parent": "bone69", "length": 90.72, "rotation": -66.05, "x": 94.38, "color": "6bff14ff"}, {"name": "bone71", "parent": "bone14", "length": 95.88, "rotation": -34.34, "x": 40.45, "y": -3.52, "color": "6bff14ff"}, {"name": "bone72", "parent": "bone71", "length": 63.6, "rotation": 17.8, "x": 95.88, "color": "6bff14ff"}, {"name": "bone73", "parent": "bone72", "length": 63.97, "rotation": 61.53, "x": 63.6, "color": "6bff14ff"}, {"name": "bone74", "parent": "bone73", "length": 85.4, "rotation": 47.34, "x": 63.97, "color": "6bff14ff"}, {"name": "bone75", "parent": "bone74", "length": 88.68, "rotation": 8.03, "x": 86.49, "y": 4.14, "color": "6bff14ff"}, {"name": "bone76", "parent": "bone75", "length": 82.6, "rotation": -36.76, "x": 89.52, "y": 0.04, "color": "6bff14ff"}, {"name": "bone77", "parent": "bone76", "length": 79.92, "rotation": -31.55, "x": 82.6, "color": "6bff14ff"}, {"name": "bone78", "parent": "bone77", "length": 83.57, "rotation": -43.16, "x": 81.79, "y": -1.07, "color": "6bff14ff"}, {"name": "bone79", "parent": "bone14", "length": 49.8, "rotation": -34.74, "x": 30.58, "y": 15.02, "color": "5eff36ff"}, {"name": "bone80", "parent": "bone79", "length": 55.87, "rotation": -31.54, "x": 49.8, "color": "5eff36ff"}, {"name": "bone81", "parent": "bone80", "length": 41.72, "rotation": 33.02, "x": 55.87, "color": "5eff36ff"}, {"name": "bone82", "parent": "bone14", "length": 38.61, "rotation": -1.27, "x": 32.93, "y": 16.04, "color": "5eff36ff"}, {"name": "bone83", "parent": "bone82", "length": 35.53, "rotation": 45.64, "x": 38.61, "color": "5eff36ff"}, {"name": "bone84", "parent": "bone14", "length": 77.17, "rotation": 8.37, "x": 27.44, "y": 6.74, "color": "6bff14ff"}, {"name": "bone85", "parent": "bone84", "length": 79.85, "rotation": 41.93, "x": 76.94, "y": 0.44, "color": "6bff14ff"}, {"name": "bone86", "parent": "bone85", "length": 71.52, "rotation": 45.68, "x": 79.85, "color": "6bff14ff"}, {"name": "bone87", "parent": "bone86", "length": 81.72, "rotation": -1.92, "x": 71.52, "color": "6bff14ff"}, {"name": "bone88", "parent": "bone87", "length": 73.63, "rotation": -26.91, "x": 81.72, "color": "6bff14ff"}, {"name": "bone89", "parent": "bone88", "length": 66.49, "rotation": -37.91, "x": 77.26, "y": -1.35, "color": "6bff14ff"}, {"name": "bone90", "parent": "bone", "x": 217.43, "y": 84.01, "color": "b90505ff"}, {"name": "bone91", "parent": "bone90", "x": -100.77, "y": 63.3, "color": "b90505ff"}, {"name": "bone92", "parent": "bone90", "x": -51.48, "y": 110.4, "color": "b90505ff"}, {"name": "bone93", "parent": "bone90", "x": 25.74, "y": 135.59, "color": "b90505ff"}, {"name": "bone94", "parent": "bone90", "x": 96.38, "y": 142.16, "color": "b90505ff"}, {"name": "bone95", "parent": "bone90", "x": 174.15, "y": 187.07, "color": "b90505ff"}, {"name": "bone96", "parent": "bone90", "x": 152.79, "y": 276.34, "color": "b90505ff"}, {"name": "bone97", "parent": "bone90", "x": 92.19, "y": 345.53, "color": "b90505ff"}, {"name": "bone98", "parent": "bone90", "x": 16.1, "y": 415.06, "color": "b90505ff"}, {"name": "bone99", "parent": "bone90", "x": -76.67, "y": 174.97, "color": "b90505ff"}, {"name": "bone100", "parent": "bone90", "x": -33.96, "y": 240.97, "color": "b90505ff"}, {"name": "bone101", "parent": "bone90", "x": -429.99, "y": 145.56, "color": "b90505ff"}, {"name": "bone102", "parent": "bone90", "x": -510.19, "y": 209.72, "color": "b90505ff"}, {"name": "bone103", "parent": "bone", "x": -253.65, "y": 87.57, "color": "b90505ff"}, {"name": "bone104", "parent": "bone103", "x": -19.36, "y": 46.72, "color": "b90505ff"}, {"name": "bone105", "parent": "bone103", "x": 55.72, "y": 171.59, "color": "b90505ff"}, {"name": "bone106", "parent": "bone103", "x": 151.21, "y": 216.48, "color": "b90505ff"}, {"name": "bone107", "parent": "bone103", "x": 270.37, "y": 245.04, "color": "b90505ff"}, {"name": "bone108", "parent": "bone103", "x": 383.81, "y": 292.38, "color": "b90505ff"}], "slots": [{"name": "tx2", "bone": "light", "color": "ffffffbb", "attachment": "tx2", "blend": "additive"}, {"name": "tx3", "bone": "bone103", "attachment": "tx3"}, {"name": "cb2", "bone": "bone43", "attachment": "cb2"}, {"name": "jio2", "bone": "bone25", "attachment": "jio2"}, {"name": "ym7", "bone": "bone71", "attachment": "ym7"}, {"name": "ym6", "bone": "bone66", "attachment": "ym6"}, {"name": "ym5", "bone": "bone", "attachment": "ym5"}, {"name": "body", "bone": "bone2", "attachment": "body"}, {"name": "ym4", "bone": "bone79", "attachment": "ym4"}, {"name": "jio1", "bone": "bone19", "attachment": "jio1"}, {"name": "tou", "bone": "bone4", "attachment": "tou"}, {"name": "biyan", "bone": "bone8"}, {"name": "eye", "bone": "bone9", "attachment": "eye"}, {"name": "ym3", "bone": "bone59", "attachment": "ym3"}, {"name": "ym2", "bone": "bone62", "attachment": "ym2"}, {"name": "ym1", "bone": "bone55", "attachment": "ym1"}, {"name": "cb1", "bone": "bone32", "attachment": "cb1"}, {"name": "tx1", "bone": "bone90", "attachment": "tx1"}], "skins": [{"name": "default", "attachments": {"biyan": {"biyan": {"x": 12.43, "y": -8.55, "rotation": 91.3, "width": 10, "height": 27}}, "body": {"body": {"type": "mesh", "uvs": [0.05412, 0.1604, 0.11552, 0.0699, 0.22808, 0.03169, 0.36988, 0.04778, 0.51168, 0.09806, 0.62424, 0.19861, 0.70611, 0.35949, 0.7368, 0.51636, 0.80405, 0.59277, 0.88737, 0.58674, 1, 0.53297, 1, 0.55213, 1, 0.57768, 0.95971, 0.65911, 0.88543, 0.72777, 0.80535, 0.74374, 0.8274, 0.79643, 0.81927, 0.87307, 0.76704, 0.94332, 0.71598, 0.98484, 0.63937, 1, 0.66027, 0.92256, 0.66955, 0.82197, 0.62777, 0.72777, 0.52694, 0.72024, 0.40773, 0.7101, 0.27624, 0.69488, 0.16195, 0.64078, 0.06478, 0.55513, 0.01958, 0.40558, 0.0142, 0.2664, 0.15895, 0.27518, 0.27989, 0.35837, 0.41112, 0.41323, 0.54106, 0.50704, 0.64913, 0.58846, 0.72504, 0.70173, 0.73791, 0.81678, 0.71861, 0.91944, 0.24792, 0.11825, 0.39432, 0.18076, 0.50791, 0.2641, 0.60004, 0.37174, 0.66314, 0.43772, 0.07754, 0.40126, 0.18229, 0.5089, 0.30345, 0.57488, 0.4284, 0.59051, 0.52179, 0.61308, 0.76652, 0.64729, 0.85557, 0.65589, 0.94618, 0.61075], "triangles": [19, 20, 38, 19, 38, 18, 38, 20, 21, 21, 22, 38, 18, 38, 37, 17, 18, 37, 38, 22, 37, 17, 37, 16, 37, 22, 36, 37, 15, 16, 37, 36, 15, 13, 50, 51, 13, 14, 50, 13, 51, 12, 50, 9, 51, 12, 51, 11, 10, 11, 51, 10, 51, 9, 36, 49, 15, 15, 50, 14, 15, 49, 50, 49, 8, 50, 50, 8, 9, 49, 7, 8, 22, 23, 36, 23, 35, 36, 35, 23, 48, 36, 35, 49, 35, 7, 49, 35, 43, 7, 35, 34, 43, 43, 6, 7, 24, 25, 48, 25, 47, 48, 23, 24, 48, 25, 46, 47, 48, 34, 35, 48, 47, 34, 46, 33, 47, 47, 33, 34, 33, 41, 34, 34, 42, 43, 34, 41, 42, 43, 42, 6, 33, 40, 41, 42, 5, 6, 42, 41, 5, 41, 4, 5, 41, 40, 4, 28, 44, 45, 28, 29, 44, 44, 31, 45, 29, 30, 44, 44, 30, 31, 31, 39, 32, 30, 0, 31, 0, 1, 31, 31, 1, 39, 1, 2, 39, 39, 2, 3, 26, 46, 25, 26, 27, 46, 27, 45, 46, 27, 28, 45, 45, 32, 46, 46, 32, 33, 45, 31, 32, 32, 40, 33, 32, 39, 40, 39, 3, 40, 40, 3, 4], "vertices": [1, 4, 48.84, 9.99, 1, 2, 3, 83.34, -28.25, 0.00733, 4, 48.48, -12.42, 0.99267, 3, 3, 59.97, -45.2, 0.19977, 4, 32.31, -36.34, 0.7971, 13, -78.92, 35.91, 0.00313, 3, 3, 26.16, -55.78, 0.58274, 4, 4.16, -57.84, 0.29463, 13, -46.83, 50.94, 0.12263, 5, 3, -9.98, -60.62, 0.43819, 4, -28.14, -74.75, 0.04489, 13, -11.67, 60.59, 0.51488, 14, -63.43, 54.14, 0.00192, 17, -66.21, 93.23, 0.00012, 5, 3, -42.8, -54.29, 0.13951, 4, -61.14, -80.04, 0.0013, 13, 21.7, 58.73, 0.78396, 14, -30.06, 55.84, 0.06332, 17, -36.98, 77.01, 0.01191, 4, 3, -72.63, -34.98, 0.00709, 13, 53.86, 43.59, 0.53784, 14, 3.53, 44.22, 0.35627, 17, -14.63, 49.37, 0.0988, 3, 13, 74.6, 22.75, 0.05622, 14, 26.38, 25.71, 0.43209, 17, -5.04, 21.58, 0.51169, 2, 17, 12.62, 8.94, 0.98493, 18, -9.49, 14.79, 0.01507, 2, 17, 33.24, 11.47, 0.04942, 18, 10.07, 7.77, 0.95058, 1, 18, 39.69, 5.9, 1, 1, 18, 38.35, 2.7, 1, 1, 18, 36.56, -1.56, 1, 1, 18, 21.61, -11.28, 1, 4, 14, 78.8, 16.44, 0.02246, 17, 34.53, -14.03, 0.35566, 18, -0.25, -15.58, 0.61675, 15, 3.75, 43.02, 0.00512, 5, 14, 64.35, 2.4, 0.31525, 17, 14.84, -18.3, 0.37954, 18, -19.76, -10.54, 0.00584, 15, 3.58, 22.87, 0.28239, 16, -26.94, 8.63, 0.01698, 4, 14, 74.39, -2.1, 0.23309, 17, 20.98, -27.43, 0.08794, 15, 13.84, 26.84, 0.54645, 16, -21, 17.89, 0.13252, 4, 14, 80.89, -14.52, 0.11413, 17, 19.92, -41.41, 0.01251, 15, 27.24, 22.73, 0.47687, 16, -7.76, 22.49, 0.39649, 3, 14, 77.82, -32.45, 0.01472, 15, 37.83, 7.95, 0.06634, 16, 9.52, 16.81, 0.91894, 1, 16, 22.05, 8.99, 1, 2, 15, 43.14, -25.03, 1e-05, 16, 33.27, -6.68, 0.99999, 2, 15, 30.08, -17.76, 0.01452, 16, 18.44, -8.52, 0.98548, 3, 14, 45.27, -28.91, 0.00703, 15, 12.43, -12.71, 0.77517, 16, 1.21, -14.86, 0.2178, 3, 13, 70.06, -23.94, 0.03947, 14, 26.84, -21.2, 0.57847, 15, -6, -20.4, 0.38206, 4, 3, -55.85, 42.3, 0.02329, 13, 47.6, -35.24, 0.51384, 14, 5.71, -34.83, 0.42442, 15, -11.17, -45, 0.03845, 5, 3, -27.65, 51.76, 0.24378, 4, -83.19, 24.8, 7e-05, 13, 20.93, -48.4, 0.69508, 14, -19.4, -50.76, 0.06056, 15, -17.49, -74.06, 0.00052, 4, 3, 3.72, 61.52, 0.62115, 4, -57.04, 44.71, 0.0401, 13, -8.85, -62.29, 0.33827, 14, -47.53, -67.74, 0.00048, 3, 3, 33.77, 63.15, 0.6661, 4, -29.36, 56.51, 0.23253, 13, -38.41, -67.93, 0.10137, 3, 3, 62.02, 57.88, 0.44831, 4, -1.01, 61.23, 0.53493, 13, -67.11, -66.51, 0.01676, 3, 3, 82.63, 37.03, 0.16309, 4, 25.48, 48.68, 0.83682, 13, -90.33, -48.61, 9e-05, 2, 3, 93.34, 14.19, 0.01697, 4, 43.36, 30.89, 0.98303, 2, 3, 59.35, 2.11, 0.00772, 4, 15.55, 7.91, 0.99228, 2, 3, 25.78, 4.74, 0.99828, 13, -38.33, -8.98, 0.00172, 2, 3, -8.23, 1.66, 0.37516, 13, -5.04, -1.36, 0.62484, 5, 3, -44.6, 5.22, 4e-05, 13, 31.47, -0.01, 0.99988, 14, -14.08, -1.52, 6e-05, 17, -53.78, 19.88, 2e-05, 15, -48.77, -35.66, 0, 2, 14, 16.36, 2.34, 0.98194, 17, -25.91, 7.05, 0.01806, 2, 14, 43.69, -3.18, 0.83314, 15, -6.98, 4.26, 0.16686, 4, 14, 58.5, -18.16, 0.04007, 17, -1, -32.65, 0.00262, 15, 14.09, 4.26, 0.95263, 16, -7.47, -0.18, 0.00467, 2, 14, 65.51, -36.03, 0.0008, 16, 11.24, 4.11, 0.9992, 3, 3, 49.5, -32.54, 0.27003, 4, 18.15, -28.02, 0.72419, 13, -66.84, 24.77, 0.00578, 3, 3, 11.47, -35.76, 0.66982, 4, -16.49, -44.06, 0.1337, 13, -29.59, 33.08, 0.19649, 5, 3, -20.41, -32.42, 0.27261, 4, -47.59, -51.82, 0.00956, 13, 2.45, 34.04, 0.70846, 14, -46.56, 29.25, 0.00801, 17, -65.06, 63.18, 0.00135, 4, 3, -49, -22.99, 0.02374, 13, 32.04, 28.54, 0.80432, 14, -16.55, 26.93, 0.14408, 17, -40.83, 45.33, 0.02786, 4, 3, -68.05, -17.83, 0.001, 13, 51.61, 25.99, 0.42259, 14, 3.18, 26.48, 0.46326, 17, -24.32, 34.51, 0.11315, 3, 3, 69.55, 30.88, 0.20228, 4, 15.3, 38.43, 0.79687, 13, -78.19, -40.76, 0.00085, 3, 3, 38.05, 39.13, 0.65286, 4, -17.12, 35.41, 0.29375, 13, -45.88, -44.7, 0.05339, 4, 3, 5.61, 38.85, 0.65637, 4, -47.51, 24.05, 0.02498, 13, -13.76, -40.07, 0.31841, 14, -54.79, -46.17, 0.00024, 4, 3, -24.28, 29.77, 0.18056, 13, 14.64, -27.06, 0.78372, 14, -27.93, -30.21, 0.03556, 15, -38.11, -65.68, 0.00016, 4, 3, -47.37, 24.81, 0.01913, 13, 36.85, -19.05, 0.72261, 14, -6.7, -19.88, 0.24745, 15, -30.53, -43.32, 0.0108, 3, 14, 46.28, 10.86, 0.0753, 17, 3.98, -1.55, 0.92468, 15, -15.15, 15.97, 3e-05, 4, 14, 65.14, 22.61, 0.00474, 17, 26.21, -1.57, 0.77939, 18, -2.08, -0.71, 0.21448, 15, -10.24, 37.64, 0.00139, 1, 18, 21.89, -1.9, 1], "hull": 31, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 0, 60, 0, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 40, 2, 78, 78, 80, 80, 82, 82, 84, 84, 86, 60, 88, 88, 90, 90, 92, 92, 94, 94, 96, 98, 100, 100, 102, 102, 20], "width": 249, "height": 181}}, "cb1": {"cb1": {"type": "mesh", "uvs": [0, 0.78409, 0.07997, 0.8125, 0.16283, 0.81433, 0.24914, 0.77767, 0.27331, 0.70527, 0.25375, 0.62646, 0.23533, 0.56689, 0.25605, 0.51557, 0.29748, 0.48166, 0.37804, 0.456, 0.4724, 0.3836, 0.55987, 0.28737, 0.6738, 0.2333, 0.78197, 0.17923, 0.84556, 0.10224, 0.8637, 0, 0.88086, 0, 0.90061, 0, 0.93929, 0.0637, 0.97388, 0.16739, 1, 0.3332, 1, 0.48519, 1, 0.62567, 0.95063, 0.75399, 0.85286, 0.85966, 0.74112, 0.94864, 0.64195, 1, 0.48831, 1, 0.33048, 0.98534, 0.17405, 0.96755, 0.0665, 0.95754, 0, 0.86077, 0.09065, 0.87774, 0.19364, 0.8847, 0.30012, 0.85689, 0.40136, 0.75542, 0.40834, 0.62058, 0.45373, 0.55107, 0.56021, 0.46489, 0.68065, 0.38009, 0.81332, 0.28835, 0.89187, 0.19243, 0.89885, 0.09929, 0.3399, 0.93975, 0.49035, 0.86637, 0.54865, 0.75854, 0.58439, 0.63723, 0.69722, 0.55936, 0.82887, 0.45602, 0.85708, 0.60129, 0.77809, 0.7196, 0.72731, 0.80197, 0.64833, 0.89932], "triangles": [31, 1, 32, 31, 0, 1, 50, 49, 23, 23, 49, 22, 50, 47, 49, 49, 21, 22, 49, 48, 21, 52, 51, 25, 25, 51, 24, 51, 50, 24, 24, 50, 23, 51, 45, 50, 28, 43, 27, 27, 43, 44, 28, 29, 43, 43, 34, 44, 26, 52, 25, 26, 27, 52, 27, 44, 52, 43, 33, 34, 43, 29, 33, 30, 32, 29, 29, 32, 33, 30, 31, 32, 32, 2, 33, 34, 33, 3, 32, 1, 2, 34, 35, 44, 33, 2, 3, 44, 45, 52, 52, 45, 51, 44, 35, 45, 45, 46, 50, 46, 45, 36, 45, 35, 36, 34, 3, 35, 3, 4, 35, 35, 4, 36, 48, 20, 21, 48, 40, 20, 39, 12, 40, 40, 41, 20, 41, 19, 20, 12, 13, 40, 40, 13, 41, 13, 14, 41, 41, 42, 19, 41, 14, 42, 42, 18, 19, 14, 15, 42, 15, 16, 42, 18, 16, 17, 18, 42, 16, 46, 47, 50, 37, 38, 46, 46, 38, 47, 47, 48, 49, 38, 39, 47, 47, 39, 48, 38, 10, 39, 39, 40, 48, 10, 11, 39, 11, 12, 39, 4, 5, 36, 36, 37, 46, 5, 6, 36, 6, 7, 36, 7, 8, 36, 36, 8, 37, 37, 8, 9, 37, 9, 38, 9, 10, 38], "vertices": [1, 2, -40.54, 64.16, 1, 2, 33, -11, 20.16, 0.304, 2, -16.07, 53.46, 0.696, 3, 34, -7.87, 45.85, 0.01047, 33, 13.6, 14.32, 0.70953, 2, 9.21, 52.94, 0.28, 2, 34, 12.37, 23.93, 0.74183, 33, 42.24, 22.7, 0.25817, 2, 35, -22.54, 29.94, 0.05292, 34, 41.06, 23.81, 0.94708, 2, 35, 7.53, 36.47, 0.68954, 34, 68.76, 37.22, 0.31046, 2, 35, 30.23, 42.5, 0.95566, 34, 89.41, 48.42, 0.04434, 2, 35, 50, 36.55, 0.99844, 34, 110.03, 47.27, 0.00156, 1, 35, 63.22, 24.16, 1, 2, 35, 73.5, -0.23, 0.96309, 42, -50.55, 56.71, 0.03691, 3, 35, 101.76, -28.49, 0.36783, 42, -10.64, 58.96, 0.62071, 43, -51.53, 90.17, 0.01146, 3, 35, 139.1, -54.48, 0.05777, 42, 33.61, 69.5, 0.78183, 43, -7.07, 80.51, 0.1604, 3, 35, 160.45, -88.84, 0.00359, 42, 73.46, 62.54, 0.42518, 43, 25.83, 56.97, 0.57123, 2, 42, 111.98, 56.72, 0.04054, 43, 58.03, 35.05, 0.95946, 1, 43, 92.82, 29.1, 1, 1, 43, 130.91, 39.72, 1, 1, 43, 133.01, 34.93, 1, 1, 43, 135.42, 29.41, 1, 1, 43, 117.8, 8.82, 1, 1, 43, 85.64, -16.76, 1, 3, 42, 123.94, -31.35, 0.05421, 43, 30.66, -49.51, 0.88874, 40, 148.94, 76.64, 0.05705, 3, 42, 85.97, -75.48, 0.33017, 43, -22.67, -72.85, 0.22481, 40, 108.69, 34.59, 0.44502, 3, 42, 50.89, -116.27, 0.02041, 43, -71.97, -94.41, 0.00565, 40, 71.48, -4.28, 0.97395, 2, 39, 79.05, -18.03, 0.02658, 40, 26.62, -29.37, 0.97342, 4, 38, 112.82, -21.56, 0.00303, 37, 93.96, 71.51, 0.07869, 39, 35.79, -43.64, 0.81997, 40, -22.9, -37.99, 0.0983, 3, 38, 81.97, -58.59, 0.1037, 37, 76.29, 26.66, 0.5234, 39, -8.89, -61.7, 0.37291, 3, 38, 53.62, -80.92, 0.00794, 37, 56.4, -3.44, 0.97406, 39, -44.42, -68, 0.018, 2, 37, 13.42, -22.12, 0.80906, 36, 54.06, -22.54, 0.19094, 2, 33, 50.3, -60.23, 0.06591, 36, 7.25, -35.08, 0.93409, 2, 33, 4.98, -43.83, 0.7644, 36, -39.62, -46.34, 0.2356, 2, 33, -26.35, -33.38, 0.96209, 36, -71.52, -54.88, 0.03791, 2, 33, -38.65, 7.04, 0.576, 2, -40.32, 34.79, 0.424, 3, 33, -12.91, -4.97, 0.53213, 36, -75.95, -23.76, 0.00387, 2, -12.62, 28.49, 0.464, 2, 33, 17.3, -13.98, 0.9019, 36, -45.77, -14.65, 0.0981, 2, 33, 51.27, -10.18, 0.79843, 36, -19.51, 7.23, 0.20157, 5, 42, -119.94, -34.87, 0.0006, 34, 32.35, -18.83, 0.58547, 38, -27.94, 5.73, 0.30182, 33, 89.43, 21.57, 0.09737, 36, -5.14, 54.75, 0.01474, 5, 35, 10.65, -10.63, 0.8767, 42, -84.65, 2.89, 0.02698, 34, 82.86, -7.84, 0.0446, 38, -30.49, 57.36, 0.04948, 39, -53.25, 93.6, 0.00225, 6, 35, 37.52, -23.98, 0.77974, 42, -56.79, 14.05, 0.16986, 34, 112.11, -14.5, 0.0012, 38, -19.11, 85.12, 0.0444, 39, -30.12, 112.7, 0.00476, 40, -29.13, 131.57, 4e-05, 5, 35, 71.13, -55.84, 0.20854, 42, -10.64, 17.89, 0.78598, 43, -69.31, 53.16, 0.00042, 38, 10.24, 120.93, 0.00471, 39, 12.67, 130.41, 0.00035, 3, 35, 104.28, -91.97, 0.00908, 42, 38.38, 18.55, 0.93823, 43, -24.84, 32.52, 0.0527, 2, 42, 91.97, 18.81, 0.04421, 43, 23.57, 9.54, 0.95579, 2, 42, 134.09, 31.03, 6e-05, 43, 66.83, 2.31, 0.99994, 1, 43, 100.37, 14.66, 1, 2, 33, 56.68, -43.72, 0.04274, 36, 3.48, -17.79, 0.95726, 4, 38, 2.94, -34.13, 0.37183, 33, 107.33, -25.57, 0.06874, 37, -6.41, 25.06, 0.18402, 36, 35.76, 25.26, 0.37541, 6, 35, -41.39, -54.4, 0.01275, 42, -86.66, -65.07, 0.01267, 34, 42.55, -62.6, 0.01469, 38, 16.91, 8.61, 0.95218, 39, -34.5, 28.25, 0.00736, 40, -63.15, 54.14, 0.00036, 6, 35, 5.27, -64.44, 0.18247, 42, -48.1, -36.96, 0.28216, 34, 90.26, -61.4, 0.03641, 38, 23.56, 55.86, 0.36816, 39, -6.32, 66.76, 0.09647, 40, -23.15, 80.17, 0.03433, 6, 35, 35.73, -98.29, 0.01868, 42, -2.56, -36.79, 0.66258, 34, 127.82, -87.16, 7e-05, 38, 55.13, 88.68, 0.07361, 39, 37.01, 80.78, 0.08087, 40, 22.33, 77.92, 0.16419, 5, 42, 53.69, -32.97, 0.75029, 43, -33.36, -20.55, 0.06436, 38, 91.54, 131.73, 0.00028, 39, 89.43, 101.53, 0.00355, 40, 78.7, 78.75, 0.18152, 5, 42, 23.93, -80.76, 0.23458, 43, -80.88, -50.73, 0.00607, 38, 105.14, 77.1, 0.00826, 39, 75.63, 46.95, 0.03636, 40, 46.45, 32.61, 0.71473, 5, 35, -25.18, -124.09, 0.00163, 42, -23.88, -99.4, 0.03978, 38, 85.25, 29.79, 0.0293, 39, 35.75, 14.65, 0.64109, 40, -2.29, 16.53, 0.28821, 3, 38, 72.69, -3.03, 0.03567, 37, 50.03, 76.5, 0.02643, 39, 9.17, -8.35, 0.9379, 3, 38, 52.07, -42.34, 0.30397, 37, 42.81, 32.7, 0.54243, 39, -27.57, -33.26, 0.15359], "hull": 32, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 0, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 32, 58, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 40, 98, 100, 100, 102, 102, 104, 104, 54], "width": 305, "height": 383}}, "cb2": {"cb2": {"type": "mesh", "uvs": [0.11566, 0.91459, 0.23831, 0.84096, 0.27116, 0.76286, 0.21859, 0.67696, 0.16822, 0.62118, 0.25802, 0.57878, 0.25583, 0.49176, 0.22516, 0.37016, 0.19669, 0.26641, 0.09813, 0.1727, 0.01491, 0.06448, 0, 0, 0.05214, 0, 0.31058, 0.0444, 0.54493, 0.11245, 0.73767, 0.17158, 0.88441, 0.24967, 0.95888, 0.34004, 1, 0.45941, 1, 0.56651, 1, 0.67807, 0.96545, 0.82199, 0.88441, 0.93132, 0.76614, 1, 0.60845, 1, 0.37629, 0.97371, 0.21421, 0.94694, 0.38105, 0.87955, 0.48002, 0.7664, 0.37225, 0.61964, 0.48222, 0.50313, 0.45143, 0.35525, 0.43164, 0.24546, 0.30847, 0.14687, 0.14572, 0.06845, 0.56034, 0.91134, 0.68298, 0.78305, 0.66984, 0.64138, 0.73116, 0.49301, 0.69174, 0.34464, 0.61728, 0.21078, 0.75963, 0.90464, 0.83628, 0.77189, 0.86256, 0.64026, 0.86694, 0.51867, 0.87789, 0.40265, 0.83847, 0.30672, 0.76456, 0.23023], "triangles": [42, 43, 20, 42, 37, 43, 43, 19, 20, 43, 37, 44, 43, 44, 19, 44, 18, 19, 22, 41, 21, 35, 36, 41, 41, 42, 21, 41, 36, 42, 21, 42, 20, 42, 36, 37, 36, 28, 37, 24, 41, 23, 23, 41, 22, 25, 35, 24, 24, 35, 41, 25, 27, 35, 27, 28, 35, 35, 28, 36, 18, 44, 45, 30, 39, 38, 30, 31, 39, 44, 38, 45, 38, 39, 45, 45, 17, 18, 39, 46, 45, 45, 46, 17, 32, 40, 39, 39, 47, 46, 39, 40, 47, 46, 16, 17, 46, 47, 16, 40, 15, 47, 16, 47, 15, 40, 14, 15, 37, 30, 38, 44, 37, 38, 28, 29, 37, 8, 33, 32, 8, 9, 33, 32, 33, 14, 9, 34, 33, 9, 10, 34, 33, 13, 14, 33, 34, 13, 13, 34, 12, 12, 34, 11, 34, 10, 11, 6, 7, 31, 7, 8, 31, 8, 32, 31, 31, 32, 39, 32, 14, 40, 29, 30, 37, 2, 3, 29, 3, 5, 29, 3, 4, 5, 29, 5, 30, 5, 6, 30, 6, 31, 30, 27, 2, 28, 2, 29, 28, 25, 26, 27, 26, 1, 27, 26, 0, 1, 1, 2, 27], "vertices": [1, 44, -7.33, 25.87, 1, 2, 44, 21.63, 15.11, 0.97844, 45, -11.71, 22.49, 0.02156, 2, 44, 46.95, 18.6, 0.13323, 45, 12.58, 14.57, 0.86677, 2, 45, 40.81, 20.23, 0.46771, 46, 0.15, 20.24, 0.53229, 2, 45, 59.42, 26.55, 0.07479, 46, 18.67, 26.8, 0.92521, 2, 45, 71.39, 10.58, 0.0006, 46, 30.86, 10.99, 0.9994, 1, 46, 58.62, 8.83, 1, 3, 46, 97.83, 10.28, 0.30244, 47, 53.12, 35, 0.00185, 48, 20.33, 28.78, 0.69571, 3, 46, 131.31, 11.9, 0.00849, 48, 53.02, 21.35, 0.5828, 49, 1.62, 21.69, 0.40871, 1, 49, 35.32, 17.06, 1, 1, 49, 71.31, 7.68, 1, 1, 49, 89.43, -2.49, 1, 1, 49, 84.44, -9.36, 1, 3, 48, 112.87, -21.19, 0.00094, 49, 48.18, -35.09, 0.98139, 52, 122.09, 80.49, 0.01766, 3, 48, 78.97, -49.19, 0.17429, 49, 8.11, -53.19, 0.5648, 52, 106.89, 39.23, 0.26092, 3, 48, 50.14, -71.86, 0.14934, 49, -25.66, -67.48, 0.13968, 52, 93.39, 5.13, 0.71097, 4, 48, 18.29, -85.37, 0.00441, 49, -59.94, -72.14, 0.00977, 52, 72.68, -22.57, 0.98479, 55, 144.28, 48.56, 0.00103, 3, 51, 95.03, -34.94, 0.00418, 52, 46.15, -39.3, 0.95792, 55, 122.32, 26.16, 0.0379, 3, 51, 59.78, -51.1, 0.15827, 52, 9.57, -52.19, 0.57648, 55, 89.71, 5.18, 0.26525, 3, 51, 26.62, -59.79, 0.13584, 52, -24.23, -57.82, 0.12411, 55, 58.12, -8.1, 0.74004, 2, 54, 68.22, -11.43, 0.01786, 55, 25.21, -21.94, 0.98214, 3, 53, 79.4, -25.46, 0.03515, 54, 31.1, -39.27, 0.87381, 55, -19.43, -34.6, 0.09104, 2, 53, 52.6, -51.55, 0.41841, 54, -3.28, -53.98, 0.58159, 2, 53, 25.83, -63.28, 0.69323, 54, -32.48, -55.3, 0.30677, 3, 44, -5.63, -58.96, 0.03274, 53, 2.55, -52.38, 0.83159, 54, -50.29, -36.76, 0.13567, 3, 44, -10.63, -20.52, 0.58084, 53, -28.16, -28.72, 0.41784, 54, -70.44, -3.64, 0.00132, 1, 44, -11.59, 7.23, 1, 2, 44, 17.96, -10.97, 0.6228, 53, -14.68, -1.76, 0.3772, 5, 44, 57.5, -13.79, 0.00649, 45, 7.93, -19.18, 0.32717, 50, -14.34, -11.77, 0.3502, 53, 15.28, 24.19, 0.27305, 54, -10.88, 30.12, 0.04309, 4, 46, 16.15, -6.37, 0.81607, 47, -22.73, 0.43, 0.08142, 50, 15.75, 28.34, 0.09957, 51, -15.75, 34.89, 0.00294, 4, 47, 16.64, -12.28, 0.76204, 50, 56.81, 33.34, 0.00045, 51, 24.86, 27, 0.22807, 52, -18.1, 28.76, 0.00944, 2, 48, 11.72, -7.4, 0.91841, 52, 27.75, 41.49, 0.08159, 3, 48, 45.72, -16.83, 0.77036, 49, -15.47, -13.23, 0.09808, 52, 61.87, 50.45, 0.13156, 3, 48, 82.33, -9.24, 0.00982, 49, 21.86, -15.54, 0.96334, 52, 89.69, 75.44, 0.02684, 2, 49, 57.75, -8.83, 0.99974, 52, 110.08, 105.73, 0.00026, 3, 44, 18.37, -41.92, 0.03081, 53, 7.47, -23.36, 0.91911, 54, -35.26, -11.45, 0.05008, 4, 45, -0.81, -51.53, 0.00034, 50, -1, -42.51, 0.02258, 53, 42.98, 5.34, 1e-05, 54, 8.19, 2.57, 0.97706, 4, 50, 36.03, -16.26, 0.6107, 51, -10.19, -13.8, 0.10954, 54, 39.41, 35.52, 0.06798, 55, 15.17, 32.22, 0.21178, 3, 51, 38.27, -11.44, 0.7386, 52, -8.24, -10.73, 0.18363, 55, 62.81, 41.41, 0.07777, 3, 47, 71.51, -39.25, 0.00194, 48, 1.01, -45.23, 0.0295, 52, 37.54, 3.41, 0.96856, 3, 48, 45.37, -49.06, 0.275, 49, -24.28, -44.24, 0.16598, 52, 77.79, 22.43, 0.55902, 2, 53, 37.8, -35.19, 0.52842, 54, -11.21, -33.4, 0.47158, 2, 54, 28.08, -12.98, 0.92414, 55, -12.81, -8.98, 0.07586, 4, 50, 53.27, -42.53, 0.01404, 51, -1.88, -44.09, 0.0211, 54, 61.42, 13.11, 9e-05, 55, 27.68, 3.4, 0.96477, 3, 51, 35.94, -34.93, 0.36023, 52, -12.7, -33.91, 0.25705, 55, 63.82, 17.82, 0.38272, 3, 51, 72.3, -27.25, 0.04404, 52, 24.21, -29.57, 0.86939, 55, 98.74, 30.57, 0.08656, 3, 49, -70.31, -55.36, 0.00035, 52, 53.44, -18.18, 0.99307, 55, 124.55, 48.39, 0.00658, 3, 48, 31.04, -69.3, 0.06644, 49, -43.43, -60, 0.04965, 52, 75.6, -2.28, 0.88391], "hull": 27, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 0, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 22, 22, 24, 50, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 48, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94], "width": 163, "height": 320}}, "eye": {"eye": {"x": -2.72, "y": 0.85, "rotation": 91.3, "width": 15, "height": 30}}, "jio1": {"jio1": {"type": "mesh", "uvs": [0.37796, 0, 0.47433, 0, 0.57237, 0.04348, 0.67207, 0.15008, 0.73355, 0.31648, 0.81497, 0.41008, 0.81663, 0.51148, 0.86648, 0.62328, 0.87978, 0.68048, 0.92491, 0.77734, 0.97668, 0.84234, 1, 0.89834, 0.99657, 0.96902, 0.96549, 1, 0.90214, 0.98956, 0.78308, 0.94804, 0.65124, 0.89499, 0.50684, 0.83408, 0.36967, 0.7815, 0.23854, 0.72397, 0.2655, 0.78917, 0.25815, 0.9004, 0.19442, 0.96368, 0.18049, 0.8929, 0.1867, 0.81516, 0.16186, 0.78184, 0.14145, 0.84847, 0.13702, 0.91372, 0.14944, 0.9637, 0.13436, 0.99563, 0.09798, 1, 0.05362, 1, 0.00837, 0.904, 0, 0.78184, 0.03321, 0.69716, 0.10508, 0.61664, 0.18227, 0.56111, 0.26123, 0.59304, 0.37649, 0.64282, 0.51842, 0.69901, 0.65524, 0.75209, 0.76338, 0.80249, 0.8416, 0.83669, 0.77833, 0.74489, 0.71161, 0.66749, 0.6817, 0.70169, 0.63684, 0.62789, 0.5333, 0.55589, 0.44012, 0.42089, 0.38835, 0.26429, 0.3642, 0.10229, 0.46212, 0.12691, 0.55373, 0.28781, 0.63787, 0.4136, 0.72387, 0.54817, 0.79491, 0.65056, 0.85287, 0.73247, 0.89587, 0.83486, 0.93514, 0.91969, 0.84352, 0.89336, 0.74443, 0.86411, 0.65095, 0.82901, 0.50699, 0.76172, 0.36864, 0.70614, 0.25086, 0.64763, 0.17981, 0.64178, 0.10503, 0.70614, 0.06016, 0.79683, 0.0695, 0.91384, 0.2198, 0.88941, 0.21939, 0.80929, 0.19874, 0.74662], "triangles": [22, 69, 21, 22, 23, 69, 23, 24, 69, 21, 69, 70, 20, 21, 70, 24, 70, 69, 70, 24, 71, 24, 25, 71, 70, 19, 20, 70, 71, 19, 71, 25, 65, 71, 65, 19, 27, 29, 30, 30, 31, 68, 31, 32, 68, 29, 27, 28, 27, 30, 68, 26, 27, 68, 68, 32, 67, 26, 68, 67, 32, 33, 67, 33, 34, 67, 26, 67, 66, 67, 34, 66, 25, 26, 66, 25, 66, 65, 66, 35, 65, 66, 34, 35, 64, 65, 36, 65, 35, 36, 14, 58, 13, 13, 58, 12, 15, 59, 14, 14, 59, 58, 12, 58, 11, 16, 60, 15, 15, 60, 59, 59, 57, 58, 17, 61, 16, 16, 61, 60, 59, 60, 42, 60, 41, 42, 59, 42, 57, 41, 60, 40, 17, 62, 61, 17, 18, 62, 61, 62, 40, 60, 61, 40, 40, 62, 39, 19, 63, 18, 18, 63, 62, 62, 63, 39, 19, 64, 63, 19, 65, 64, 63, 64, 38, 63, 38, 39, 38, 64, 37, 37, 64, 36, 58, 10, 11, 58, 57, 10, 57, 9, 10, 42, 56, 57, 42, 43, 56, 57, 56, 9, 56, 8, 9, 43, 55, 56, 43, 44, 55, 56, 7, 8, 56, 55, 7, 45, 46, 44, 44, 54, 55, 44, 46, 54, 55, 6, 7, 55, 54, 6, 46, 53, 54, 46, 47, 53, 47, 52, 53, 47, 48, 52, 54, 5, 6, 54, 4, 5, 54, 53, 4, 48, 49, 52, 4, 53, 3, 53, 52, 3, 49, 51, 52, 52, 2, 3, 52, 51, 2, 49, 50, 51, 50, 0, 51, 51, 1, 2, 51, 0, 1], "vertices": [2, 19, -20.37, -0.01, 1, 22, -67.47, 28.3, 0, 2, 19, -11.44, 9.19, 1, 22, -69.57, 40.94, 0, 2, 19, 0.3, 15.97, 1, 22, -68.07, 54.41, 0, 2, 19, 16.03, 19.17, 1, 22, -61.31, 68.98, 0, 2, 19, 31.88, 15.18, 1, 22, -48.71, 79.37, 0, 2, 19, 45.13, 17.41, 1, 22, -42.64, 91.36, 0, 2, 19, 51.47, 11.57, 1, 22, -34.17, 93, 0, 2, 19, 62.9, 9.71, 1, 22, -25.89, 101.1, 0, 2, 19, 67.62, 7.59, 1, 22, -21.39, 103.64, 0, 2, 19, 77.71, 6.16, 1, 22, -14.25, 110.92, 0, 2, 19, 86.47, 7.25, 1, 22, -9.93, 118.62, 0, 3, 19, 92.05, 6.16, 0.82594, 20, -4.82, -4.05, 0.17406, 22, -5.75, 122.46, 0, 2, 19, 96.04, 1.65, 0.45098, 20, -5.79, 1.9, 0.54902, 2, 19, 95.05, -3.15, 0.18204, 20, -2.4, 5.43, 0.81796, 1, 20, 6, 6.55, 1, 2, 20, 22.22, 6.85, 1, 22, 3.16, 94.7, 0, 2, 20, 40.33, 6.6, 1, 22, 1.6, 76.66, 0, 2, 20, 60.21, 6.09, 1, 22, -0.35, 56.86, 0, 2, 20, 78.99, 6.05, 1, 22, -1.76, 38.14, 0, 2, 20, 97.09, 5.4, 0.48649, 23, 2.22, 5.11, 0.51351, 3, 20, 92.3, 9.94, 0.04243, 23, 8.76, 6.04, 0.87573, 24, -5.54, 3.18, 0.08184, 3, 22, 10.65, 25.17, 0, 23, 16.93, 1.19, 0.01427, 24, 3.51, 6.07, 0.98573, 3, 22, 17.34, 17.69, 0, 23, 18.26, -8.76, 7e-05, 24, 11.84, 0.46, 0.99993, 3, 22, 11.71, 14.87, 8e-05, 23, 12.02, -7.92, 0.0339, 24, 7.07, -3.64, 0.96601, 4, 21, 7.85, 12.35, 0.024, 22, 5.06, 14.6, 0.02082, 23, 6.37, -4.4, 0.66219, 24, 0.68, -5.54, 0.29298, 4, 21, 9.68, 8.4, 0.24347, 22, 2.81, 10.88, 0.20457, 23, 2.41, -6.21, 0.5404, 24, -0.59, -9.7, 0.01157, 3, 21, 14.5, 12.43, 0.09107, 22, 8.84, 9.13, 0.77788, 23, 6.42, -11.05, 0.13106, 3, 21, 17.33, 17.24, 0.00686, 22, 14.41, 9.46, 0.9679, 23, 11.21, -13.91, 0.02524, 2, 22, 18.33, 11.79, 0.993, 23, 15.76, -14.19, 0.007, 2, 22, 21.34, 10.26, 0.99681, 23, 17.38, -17.15, 0.00319, 2, 22, 22.5, 5.54, 0.99948, 23, 15.69, -21.7, 0.00052, 1, 22, 23.47, -0.27, 1, 1, 22, 16.41, -7.55, 1, 1, 22, 6.35, -10.36, 1, 2, 21, 22.28, -5.23, 0.20288, 22, -1.48, -7.18, 0.79712, 2, 21, 10.75, -7.51, 0.99883, 22, -9.8, 1.12, 0.00117, 2, 20, 107.63, -6.29, 0.26801, 21, -0.55, -7.56, 0.73199, 1, 20, 96.78, -6.13, 1, 1, 20, 80.89, -5.62, 1, 1, 20, 61.42, -5.43, 1, 1, 20, 42.67, -5.33, 1, 1, 20, 27.68, -4.55, 1, 2, 19, 73.61, -5.3, 0.40794, 20, 16.89, -4.18, 0.59206, 1, 19, 62.15, -5.91, 1, 1, 19, 51.25, -7.69, 1, 1, 19, 50.57, -12.57, 1, 1, 19, 41.91, -12.48, 1, 1, 19, 27.93, -18.1, 1, 1, 19, 11.06, -19, 1, 1, 19, -3.29, -14.67, 1, 1, 19, -15.4, -7.38, 1, 2, 19, -4.83, 0.51, 1, 22, -58.67, 41.12, 0, 1, 19, 13.47, -0.28, 1, 2, 19, 28.94, 0.3, 1, 22, -38.47, 68.18, 0, 2, 19, 45.11, 0.54, 1, 22, -29.07, 81.34, 0, 2, 19, 57.94, 1.26, 1, 22, -22.04, 92.09, 0, 2, 19, 68.3, 1.94, 1, 22, -16.44, 100.84, 0, 2, 19, 78.53, -0.02, 0.98948, 20, 9.91, -6.03, 0.01052, 3, 19, 87.34, -1.29, 0.10169, 20, 3.13, -0.25, 0.89831, 22, -2.54, 114.25, 0, 2, 20, 15.5, 0.44, 1, 22, -2.74, 101.86, 0, 2, 20, 28.9, 1.13, 1, 22, -3.03, 88.45, 0, 2, 20, 41.68, 1.16, 1, 22, -3.93, 75.7, 0, 2, 20, 61.64, 0.11, 1, 22, -6.42, 55.87, 0, 1, 20, 80.63, -0.15, 1, 1, 20, 97.03, -1.29, 1, 2, 20, 106.33, 0.45, 0.00867, 21, 2.58, -1.45, 0.99133, 1, 21, 13.9, -0.59, 1, 1, 22, 6.29, -2.26, 1, 2, 22, 15.9, 0.61, 0.99946, 23, 7.46, -22.07, 0.00054, 1, 24, 4.7, 1.03, 1, 2, 23, 7.74, -0.24, 0.9773, 24, -1.52, -1.75, 0.0227, 4, 21, 3.98, 7.7, 0.0066, 22, -0.95, 15.22, 0.00569, 23, 1.75, -0.51, 0.98717, 24, -5.3, -6.4, 0.00054], "hull": 51, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 0, 100, 0, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 44, 138, 138, 140, 140, 142, 142, 130], "width": 133, "height": 85}}, "jio2": {"jio2": {"type": "mesh", "uvs": [0.06735, 0.0063, 0.18735, 0, 0.32356, 0.00997, 0.43059, 0.0614, 0.55708, 0.12508, 0.65113, 0.18752, 0.72897, 0.26222, 0.87167, 0.32099, 1, 0.3663, 1, 0.39691, 0.95924, 0.44834, 0.82951, 0.55487, 0.5214, 0.7814, 0.6641, 0.77528, 0.71924, 0.81691, 0.68032, 0.89528, 0.61546, 0.87936, 0.57329, 0.8463, 0.48852, 0.85052, 0.45568, 0.87945, 0.51917, 0.92656, 0.61549, 0.95136, 0.56952, 0.98111, 0.48195, 1, 0.36373, 0.99434, 0.2893, 0.95218, 0.24771, 0.88606, 0.26741, 0.83978, 0.34184, 0.78522, 0.58484, 0.5819, 0.81252, 0.39758, 0.55419, 0.28187, 0.42284, 0.27526, 0.3652, 0.22282, 0.18277, 0.18424, 0.07574, 0.12547, 0.0198, 0.05475, 0.19916, 0.06314, 0.32439, 0.12198, 0.48303, 0.19133, 0.64723, 0.27329, 0.83647, 0.3584, 0.91996, 0.39202, 0.89213, 0.42459, 0.68341, 0.57275, 0.43015, 0.78079, 0.36162, 0.84697, 0.35038, 0.89193, 0.40206, 0.94283, 0.4515, 0.81473, 0.57059, 0.80879, 0.64924, 0.8283], "triangles": [48, 19, 20, 22, 20, 21, 48, 20, 22, 24, 25, 48, 23, 24, 48, 22, 23, 48, 48, 47, 19, 25, 26, 47, 25, 47, 48, 27, 28, 46, 19, 46, 18, 26, 27, 46, 47, 26, 46, 47, 46, 19, 51, 50, 13, 14, 51, 13, 17, 50, 51, 16, 17, 51, 15, 51, 14, 16, 51, 15, 50, 12, 13, 49, 12, 50, 18, 49, 50, 17, 18, 50, 46, 49, 18, 43, 30, 42, 9, 43, 42, 10, 43, 9, 43, 44, 30, 11, 43, 10, 11, 44, 43, 44, 29, 30, 44, 45, 29, 44, 12, 45, 11, 12, 44, 45, 28, 29, 49, 45, 12, 45, 49, 46, 45, 46, 28, 37, 1, 2, 37, 2, 3, 0, 1, 37, 36, 0, 37, 38, 37, 3, 38, 3, 4, 35, 36, 37, 35, 37, 38, 34, 35, 38, 39, 38, 4, 39, 4, 5, 33, 34, 38, 39, 33, 38, 40, 39, 5, 40, 5, 6, 32, 33, 39, 31, 32, 39, 40, 31, 39, 7, 40, 6, 41, 40, 7, 41, 7, 8, 42, 41, 8, 42, 8, 9, 41, 31, 40, 30, 41, 42, 30, 31, 41], "vertices": [1, 25, -11.1, -2.38, 1, 1, 25, -6.5, 5.31, 1, 1, 25, 1.34, 11.94, 1, 1, 25, 14.16, 11.8, 1, 1, 25, 29.75, 11.28, 1, 1, 25, 43.65, 9.03, 1, 1, 25, 58.67, 4.35, 1, 1, 25, 74.27, 5.36, 1, 2, 25, 87.14, 7.2, 0.94112, 26, -7.15, 1.72, 0.05888, 2, 25, 91.81, 3.44, 0.42852, 26, -1.75, 4.33, 0.57148, 1, 26, 8.63, 6.02, 1, 1, 26, 31.61, 6.49, 1, 2, 26, 81.51, 5.34, 0.51227, 30, 6.82, 5.62, 0.48773, 2, 30, 17.34, 4.06, 0.23468, 31, -1.28, 7.83, 0.76532, 1, 31, 7.71, 6.28, 1, 1, 31, 18.37, -5.15, 1, 2, 30, 8.6, -14.73, 0.00725, 31, 13.01, -7.17, 0.99275, 3, 30, 7.26, -7.66, 0.32347, 31, 5.94, -5.84, 0.67197, 27, 3.73, 13.87, 0.00455, 4, 30, 0.98, -6.85, 0.66226, 31, 2.89, -11.38, 0.07127, 27, 6.01, 7.96, 0.24082, 28, -3.16, 11.13, 0.02566, 5, 30, -2.83, -11.7, 0.11048, 31, 6.01, -16.7, 0.00211, 27, 12.1, 6.93, 0.4469, 28, 1.55, 7.14, 0.4307, 29, -8.8, 8.6, 0.0098, 4, 30, -0.67, -21.83, 0.00018, 27, 19.97, 13.67, 0.00143, 28, 11.75, 8.93, 0.32563, 29, 1.56, 8.8, 0.67276, 2, 28, 18.49, 14.32, 0.02755, 29, 9.05, 13.08, 0.97245, 2, 28, 23.07, 9.36, 0.00101, 29, 12.8, 7.48, 0.99899, 1, 29, 13.3, 0.03, 1, 2, 28, 21.09, -5.96, 0.01591, 29, 8.48, -7.35, 0.98409, 2, 28, 11.57, -8.8, 0.59189, 29, -1.36, -8.7, 0.40811, 2, 27, 16.97, -7.72, 0.48068, 28, -1.72, -7.95, 0.51932, 3, 26, 100, -6.58, 0.01528, 27, 7.81, -8.44, 0.97501, 28, -9.97, -3.9, 0.00971, 2, 26, 87.98, -6.29, 0.80179, 27, -3.87, -5.6, 0.19821, 1, 26, 44.28, -7.49, 1, 2, 25, 83.22, -7.46, 0.55556, 26, 4.42, -8.09, 0.44444, 1, 25, 53.57, -8.15, 1, 1, 25, 46.47, -14.91, 1, 1, 25, 35.79, -11.79, 1, 1, 25, 21.43, -17.58, 1, 1, 25, 7.49, -16.53, 1, 1, 25, -5.9, -11.08, 1, 1, 25, 3.69, -1.76, 1, 1, 25, 18.49, -1.76, 1, 1, 25, 36.44, -1.13, 1, 1, 25, 56.57, -1.73, 1, 2, 25, 78.35, -1.26, 0.99274, 26, -3.27, -9.85, 0.00726, 2, 25, 87.35, -0.58, 0.73039, 26, -0.03, -1.41, 0.26961, 2, 25, 91.04, -6.18, 0.00751, 26, 6.61, -0.48, 0.99249, 1, 26, 39.48, -1.71, 1, 2, 26, 84.35, -0.79, 0.99975, 27, -6.25, 0.55, 0.00025, 2, 26, 98.23, 0.3, 0.00038, 27, 7.54, -1.33, 0.99962, 2, 27, 16.31, -0.06, 0.12298, 28, 1.61, -1.02, 0.87702, 2, 28, 12.26, -0.29, 0.33678, 29, 0.64, -0.39, 0.66322, 2, 26, 89.64, 3.53, 0.08973, 30, 0.14, 0.64, 0.91027, 2, 30, 8.96, -0.51, 0.87835, 31, -0.1, -1.64, 0.12165, 1, 31, 6.43, 0.78, 1], "hull": 37, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 0, 72, 0, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 44, 92, 98, 98, 100, 100, 102], "width": 74, "height": 196}}, "tou": {"tou": {"type": "mesh", "uvs": [0.86073, 0.60064, 0.68591, 0.58153, 0.55655, 0.49962, 0.52625, 0.39586, 0.49711, 0.31577, 0.55189, 0.29848, 0.602, 0.24114, 0.52275, 0.24843, 0.44117, 0.2284, 0.36192, 0.18927, 0.37707, 0.12283, 0.39222, 0.05275, 0.39688, 0, 0.37474, 0, 0.33394, 0.0209, 0.29781, 0.08005, 0.24537, 0.12829, 0.16961, 0.14649, 0.09036, 0.18108, 0.02043, 0.24478, 0, 0.32214, 0, 0.39586, 0.01577, 0.43409, 0, 0.48414, 0, 0.54239, 0.00878, 0.59973, 0.03908, 0.62794, 0.06938, 0.57151, 0.11251, 0.516, 0.16961, 0.46321, 0.22789, 0.42317, 0.25586, 0.38403, 0.31063, 0.435, 0.32812, 0.51873, 0.33014, 0.61734, 0.32165, 0.73136, 0.33099, 0.84406, 0.35476, 0.94217, 0.40485, 1, 0.4787, 0.98327, 0.55426, 0.9541, 0.65424, 0.9362, 0.77139, 0.92294, 0.77903, 0.83942, 0.77733, 0.78837, 0.87241, 0.74595, 0.95306, 0.70551, 1, 0.68496, 1, 0.6485, 0.03714, 0.54414, 0.06239, 0.48599, 0.09899, 0.4239, 0.13433, 0.35491, 0.17345, 0.27635, 0.27346, 0.26586, 0.36003, 0.32531, 0.4063, 0.42321, 0.42869, 0.52229, 0.466, 0.60854, 0.50033, 0.71111, 0.60033, 0.80552, 0.69437, 0.85797, 0.60033, 0.65399, 0.73467, 0.66798, 0.85258, 0.67497, 0.94811, 0.6773, 0.42122, 0.69712, 0.42869, 0.81018, 0.45555, 0.91624, 0.55108, 0.87428, 0.5063, 0.79503, 0.3558, 0.05052, 0.32577, 0.10816, 0.297, 0.16092, 0.23569, 0.20489, 0.52846, 0.27719, 0.44213, 0.27328, 0.35705, 0.24006], "triangles": [11, 10, 71, 72, 15, 71, 15, 14, 71, 11, 71, 12, 71, 13, 12, 71, 14, 13, 9, 73, 10, 10, 73, 72, 73, 16, 72, 16, 15, 72, 10, 72, 71, 53, 74, 54, 74, 18, 17, 74, 73, 77, 77, 73, 9, 74, 16, 73, 74, 17, 16, 27, 26, 49, 26, 25, 49, 25, 24, 49, 27, 49, 28, 49, 50, 28, 50, 49, 23, 49, 24, 23, 28, 50, 29, 23, 22, 50, 50, 51, 29, 50, 22, 51, 29, 51, 30, 22, 21, 51, 51, 52, 30, 51, 21, 52, 30, 52, 31, 21, 20, 52, 52, 53, 31, 31, 53, 54, 53, 52, 19, 52, 20, 19, 19, 18, 53, 53, 18, 74, 31, 54, 55, 54, 77, 55, 77, 8, 76, 54, 74, 77, 77, 9, 8, 33, 32, 56, 56, 3, 2, 56, 32, 55, 32, 31, 55, 56, 4, 3, 4, 55, 76, 4, 56, 55, 55, 77, 76, 4, 75, 5, 4, 76, 75, 5, 75, 6, 76, 7, 75, 75, 7, 6, 76, 8, 7, 35, 66, 67, 67, 66, 59, 62, 60, 59, 35, 34, 66, 62, 59, 58, 59, 66, 58, 66, 34, 58, 62, 1, 63, 1, 62, 2, 34, 57, 58, 34, 33, 57, 62, 58, 2, 58, 57, 2, 2, 57, 56, 57, 33, 56, 39, 38, 68, 38, 37, 68, 39, 68, 40, 68, 69, 40, 40, 69, 41, 37, 36, 68, 41, 61, 42, 41, 69, 61, 42, 61, 43, 36, 67, 68, 69, 67, 70, 69, 68, 67, 69, 60, 61, 69, 70, 60, 43, 61, 44, 36, 35, 67, 61, 60, 44, 67, 59, 70, 60, 63, 44, 60, 62, 63, 60, 70, 59, 44, 64, 45, 44, 63, 64, 46, 64, 65, 46, 45, 64, 46, 65, 47, 65, 48, 47, 48, 65, 0, 65, 64, 0, 64, 63, 0, 63, 1, 0], "vertices": [2, 4, 28.09, -49.34, 0.78205, 5, 7.89, -52.3, 0.21795, 2, 4, 48.41, -35.58, 0.38849, 5, 18.72, -30.28, 0.61151, 3, 4, 71.52, -34.35, 0.01782, 5, 38.18, -17.76, 0.79516, 6, -5.59, -18.24, 0.18702, 3, 5, 57.04, -19.53, 0.07071, 6, 13.35, -18.56, 0.92859, 7, -27.05, 1.99, 0.00071, 3, 6, 28.16, -18.02, 0.81432, 7, -16.63, -8.55, 0.1747, 10, 14.65, -38.3, 0.01097, 3, 6, 29.33, -26.15, 0.73808, 7, -21.82, -14.91, 0.25221, 10, 22.1, -41.78, 0.00971, 3, 6, 37.58, -35.35, 0.73688, 7, -23, -27.21, 0.25913, 10, 34.31, -39.91, 0.00399, 3, 6, 38.95, -24.35, 0.68988, 7, -13.97, -20.77, 0.29013, 10, 25.85, -32.74, 0.01998, 4, 6, 45.12, -14.18, 0.34906, 7, -2.32, -18.43, 0.51327, 10, 20.73, -22.01, 0.12983, 11, -4.76, -21.96, 0.00784, 4, 6, 54.51, -5.15, 0.01486, 7, 10.69, -19.22, 0.30108, 10, 18.31, -9.2, 0.40029, 11, -1.81, -9.26, 0.28377, 3, 7, 14.54, -30.6, 0.0039, 11, 10.05, -7.35, 0.90928, 12, -3.69, -7.2, 0.08683, 1, 12, 8.86, -5.59, 1, 1, 12, 18.03, -3.47, 1, 1, 12, 17.14, -0.53, 1, 1, 12, 11.93, 3.81, 1, 3, 10, 26.45, 10.57, 0.00361, 11, 13.6, 5.56, 0.4834, 12, 0.39, 5.55, 0.513, 4, 8, -20.57, 5.81, 0.01823, 10, 15.2, 10.05, 0.50803, 11, 3.09, 9.61, 0.47276, 12, -9.95, 10.03, 0.00098, 3, 8, -17.09, -4.64, 0.22683, 10, 5.65, 15.54, 0.72917, 11, -3.44, 18.49, 0.044, 3, 7, 44.46, -2.31, 0.00159, 8, -10.68, -15.52, 0.67277, 10, -6.36, 19.4, 0.32564, 2, 8, 0.87, -24.98, 0.96151, 10, -21.28, 18.78, 0.03849, 1, 8, 14.71, -27.5, 1, 1, 8, 27.82, -27.21, 1, 1, 8, 34.58, -24.86, 1, 1, 8, 43.53, -26.85, 1, 1, 8, 53.9, -26.61, 1, 1, 8, 64.08, -25.16, 1, 1, 8, 69, -20.84, 1, 1, 8, 58.86, -16.85, 1, 1, 8, 48.85, -11.09, 1, 1, 8, 39.27, -3.36, 1, 3, 6, 18.52, 22.88, 0.01813, 7, 6.95, 26.24, 0.00411, 8, 31.96, 4.57, 0.97776, 3, 6, 24.36, 17.44, 0.27029, 7, 6.9, 18.26, 0.09574, 8, 24.91, 8.3, 0.63397, 4, 5, 59.79, 11.12, 0.0076, 6, 13.73, 12.21, 0.90605, 7, -4.14, 22.54, 0.01184, 8, 33.81, 16.12, 0.07451, 3, 5, 44.87, 13.47, 0.59787, 6, -1.32, 13.4, 0.40184, 8, 48.65, 18.89, 0.00028, 3, 4, 80.83, 2.3, 0.00028, 5, 28.11, 18.68, 0.99873, 6, -18.43, 17.32, 0.00099, 2, 4, 68.1, 18.15, 0.19447, 5, 9.2, 26.15, 0.80553, 2, 4, 53.7, 32.17, 0.71625, 5, -10.26, 31.18, 0.28375, 2, 4, 39.54, 42.91, 0.94935, 5, -27.88, 33.5, 0.05065, 2, 4, 27.47, 45.89, 0.99028, 5, -39.84, 30.1, 0.00972, 2, 4, 21.85, 36.79, 0.99734, 5, -40.21, 19.42, 0.00266, 1, 4, 17.54, 25.9, 1, 1, 4, 9.36, 14.22, 1, 1, 4, -1.14, 1.56, 1, 2, 4, 8.03, -10.19, 0.9912, 5, -28.93, -28.24, 0.0088, 2, 4, 14.3, -16.77, 0.93636, 5, -20.23, -30.85, 0.06364, 2, 4, 9.55, -31.24, 0.86004, 5, -17.19, -45.76, 0.13996, 2, 4, 6.06, -44.09, 0.85353, 5, -13.85, -58.66, 0.14647, 2, 4, 3.67, -51.18, 0.85561, 5, -12.42, -66, 0.14439, 2, 4, 8.02, -56, 0.85421, 5, -6.25, -68.03, 0.14579, 1, 8, 54.09, -21.44, 1, 1, 8, 43.67, -18.17, 1, 1, 8, 32.5, -13.34, 1, 1, 8, 20.11, -8.7, 1, 1, 8, 6.01, -3.59, 1, 2, 7, 14.89, -1.35, 0.94721, 10, -0.05, -9.51, 0.05279, 3, 6, 31.06, 0.88, 0.85757, 7, -0.75, 2.12, 0.13671, 8, 14.13, 22.54, 0.00572, 2, 5, 57.63, -2.17, 0.00124, 6, 12.6, -1.2, 0.99876, 1, 5, 39.9, 0.38, 1, 1, 5, 23.7, 0.26, 1, 1, 5, 4.87, 1.43, 1, 1, 4, 30.51, 1.98, 1, 2, 4, 14.55, 0.15, 0.99999, 5, -28.39, -16.02, 1e-05, 2, 4, 48.59, -18.03, 0.30384, 5, 10.18, -14.95, 0.69616, 2, 4, 33.06, -28.7, 0.6925, 5, 1.98, -31.91, 0.3075, 2, 4, 20.07, -38.76, 0.81923, 5, -4.32, -47.09, 0.18077, 2, 4, 9.93, -47.36, 0.85138, 5, -8.87, -59.58, 0.14862, 2, 4, 61.92, 4.35, 0.0488, 5, 10.67, 11.09, 0.9512, 2, 4, 47.66, 18.59, 0.70337, 5, -8.77, 16.4, 0.29663, 2, 4, 32.23, 30.1, 0.96927, 5, -27.88, 18.75, 0.03073, 2, 4, 27.39, 15.66, 0.99691, 5, -24.93, 3.8, 0.00309, 2, 4, 41.46, 9.36, 0.92101, 5, -9.58, 5.31, 0.07899, 1, 12, 7.77, -0.63, 1, 2, 10, 25.44, 4.31, 0.00045, 11, 10.16, 0.24, 0.99955, 3, 8, -14.92, 13.12, 0.00026, 10, 15.85, 0.84, 0.55131, 11, -0.02, 0.92, 0.44843, 3, 8, -6.9, 4.77, 0.02091, 10, 4.31, 1.73, 0.97487, 11, -10.22, 6.39, 0.00422, 3, 6, 33.79, -23.89, 0.72096, 7, -17.14, -16.66, 0.26512, 10, 22.65, -36.81, 0.01392, 4, 6, 37.33, -12.41, 0.53693, 7, -6.29, -11.49, 0.40333, 10, 14.98, -27.56, 0.0595, 11, -12.25, -24.72, 0.00024, 4, 6, 45.89, -2.33, 0.03982, 7, 6.92, -10.97, 0.67189, 10, 11.24, -14.88, 0.26899, 11, -10.57, -11.6, 0.01929], "hull": 49, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 0, 52, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 116, 124, 124, 126, 126, 128, 128, 130, 116, 132, 132, 134, 134, 136, 80, 138, 138, 140, 24, 142, 142, 144, 144, 146, 146, 148, 12, 150, 150, 152, 152, 154], "width": 139, "height": 178}}, "tx1": {"tx1": {"type": "mesh", "uvs": [0.14971, 0.78644, 0.55185, 0.77879, 0.57524, 0.70611, 0.60692, 0.63981, 0.64616, 0.6143, 0.64238, 0.53908, 0.65521, 0.46257, 0.67709, 0.42815, 0.68464, 0.50465, 0.7035, 0.56203, 0.70576, 0.6194, 0.70048, 0.64873, 0.92607, 0.63598, 0.92808, 0.58119, 0.91399, 0.50632, 0.89083, 0.44336, 0.86163, 0.41188, 0.83343, 0.39996, 0.79316, 0.34041, 0.75036, 0.27745, 0.71764, 0.20172, 0.70304, 0.10984, 0.70556, 0.03922, 0.71764, 0, 0.73778, 0, 0.76144, 0.02135, 0.78158, 0.08346, 0.77604, 0.14217, 0.78913, 0.20087, 0.82034, 0.25107, 0.86112, 0.28851, 0.90039, 0.32594, 0.94067, 0.39303, 0.95023, 0.47131, 0.97339, 0.50534, 1, 0.54278, 1, 0.60659, 0.99504, 0.69762, 0.97591, 0.77675, 0.94721, 0.84056, 0.90593, 0.87119, 0.88227, 0.8865, 0.8576, 0.93585, 0.8143, 0.92479, 0.77554, 0.89927, 0.73303, 0.87769, 0.72018, 0.8379, 0.67041, 0.86051, 0.62867, 0.90392, 0.59388, 0.9609, 0.58154, 1, 0.57274, 1, 0.56309, 1, 0.57525, 0.94179, 0.60377, 0.88085, 0.63438, 0.83337, 0.67463, 0.79368, 0.71656, 0.75613, 0.76646, 0.72849, 0.82462, 0.70361, 0.87821, 0.69316, 0.90814, 0.68335, 0.92601, 0.63698, 0.7003, 0.65053, 0.67639, 0.67778, 0.65205, 0.69754, 0.61794, 0.7297, 0.58991, 0.77202, 0.57271, 0.80677, 0.5615, 0.8194, 0.55066, 0.81498, 0.55177, 0.77967, 0.14975, 0.78723, 0.1423, 0.80041, 0.12428, 0.80041, 0.10881, 0.7822, 0.1045, 0.75473, 0.10117, 0.72957, 0.09314, 0.71501, 0.08276, 0.71832, 0.06374, 0.70721, 0.04155, 0.69065, 0.02897, 0.6645, 0.01542, 0.6465, 0.00089, 0.64159, 0, 0.61104, 0.00251, 0.58868, 0.01509, 0.56522, 0.03414, 0.55159, 0.02897, 0.58595, 0.03349, 0.61431, 0.04769, 0.63559, 0.06641, 0.6585, 0.08707, 0.67322, 0.1074, 0.69013, 0.11999, 0.70977, 0.1145, 0.73541, 0.12386, 0.75941, 0.13322, 0.78013, 0.14158, 0.78622, 0.01522, 0.58489, 0.01348, 0.61174, 0.02564, 0.63692, 0.04303, 0.66042, 0.06338, 0.68349, 0.08399, 0.69273, 0.09939, 0.70364, 0.10833, 0.7158, 0.10758, 0.73469, 0.11428, 0.76028, 0.12446, 0.78756, 0.13713, 0.79385, 0.56135, 0.79257, 0.57773, 0.75588, 0.59867, 0.71533, 0.62381, 0.67736, 0.65948, 0.65268, 0.67675, 0.61214, 0.67291, 0.56187, 0.66524, 0.51242, 0.6686, 0.46783, 0.58708, 0.95093, 0.61058, 0.9042, 0.64145, 0.86059, 0.68246, 0.82633, 0.7244, 0.81231, 0.76771, 0.81153, 0.82537, 0.81708, 0.87763, 0.78924, 0.9282, 0.74795, 0.95547, 0.68747, 0.96853, 0.61354, 0.96342, 0.54441, 0.93615, 0.48296, 0.91286, 0.42344, 0.88161, 0.36775, 0.83616, 0.32838, 0.80264, 0.2871, 0.76515, 0.23237, 0.74753, 0.15844, 0.73163, 0.08163, 0.72594, 0.02307], "triangles": [81, 104, 80, 80, 104, 105, 105, 104, 93, 81, 103, 104, 81, 82, 103, 104, 92, 93, 104, 103, 92, 83, 102, 82, 82, 102, 103, 103, 91, 92, 103, 102, 91, 102, 83, 101, 83, 84, 101, 84, 85, 101, 102, 101, 90, 102, 90, 91, 89, 101, 100, 101, 89, 90, 85, 86, 101, 101, 86, 100, 86, 87, 100, 89, 100, 88, 100, 87, 88, 74, 111, 73, 111, 99, 73, 72, 99, 0, 72, 73, 99, 74, 110, 111, 74, 75, 110, 110, 98, 111, 111, 98, 99, 75, 109, 110, 109, 97, 110, 110, 97, 98, 72, 0, 71, 75, 76, 109, 97, 109, 96, 96, 109, 108, 109, 76, 108, 76, 77, 108, 108, 107, 96, 96, 107, 95, 108, 77, 107, 77, 78, 107, 107, 78, 106, 79, 105, 78, 79, 80, 105, 106, 94, 107, 107, 94, 95, 78, 105, 106, 94, 106, 93, 106, 105, 93, 70, 112, 69, 69, 112, 68, 70, 71, 112, 67, 68, 113, 71, 1, 112, 68, 112, 113, 112, 1, 113, 71, 0, 1, 1, 2, 113, 67, 114, 66, 67, 113, 114, 113, 2, 114, 66, 115, 65, 66, 114, 115, 115, 114, 3, 114, 2, 3, 65, 116, 64, 65, 115, 116, 115, 4, 116, 115, 3, 4, 63, 64, 117, 64, 116, 117, 116, 4, 117, 63, 11, 62, 63, 117, 11, 11, 117, 10, 117, 9, 10, 4, 118, 117, 4, 5, 118, 117, 118, 9, 118, 8, 9, 5, 119, 118, 118, 119, 8, 5, 6, 119, 119, 120, 8, 119, 6, 120, 120, 7, 8, 120, 6, 7, 19, 20, 138, 20, 139, 138, 28, 139, 27, 28, 138, 139, 20, 21, 139, 21, 140, 139, 139, 140, 27, 27, 140, 26, 21, 22, 140, 140, 25, 26, 22, 141, 140, 25, 141, 24, 25, 140, 141, 22, 23, 141, 141, 23, 24, 16, 17, 135, 17, 136, 135, 17, 18, 136, 136, 30, 135, 135, 30, 31, 18, 137, 136, 18, 19, 137, 137, 29, 136, 136, 29, 30, 19, 138, 137, 138, 28, 137, 137, 28, 29, 132, 14, 133, 132, 13, 14, 132, 133, 34, 132, 34, 35, 34, 133, 33, 15, 134, 14, 14, 134, 133, 133, 134, 33, 134, 32, 33, 134, 15, 135, 15, 16, 135, 32, 134, 31, 134, 135, 31, 39, 129, 38, 129, 60, 61, 129, 130, 38, 38, 130, 37, 129, 61, 130, 130, 131, 37, 37, 131, 36, 61, 62, 130, 130, 62, 131, 131, 62, 12, 62, 11, 12, 12, 13, 131, 13, 132, 131, 131, 132, 36, 132, 35, 36, 41, 42, 127, 42, 43, 127, 43, 44, 127, 127, 128, 41, 41, 128, 40, 40, 128, 39, 128, 129, 39, 128, 127, 59, 59, 127, 58, 129, 128, 60, 128, 59, 60, 45, 126, 44, 44, 126, 127, 45, 46, 126, 126, 46, 125, 46, 124, 125, 124, 56, 125, 56, 57, 125, 125, 57, 126, 57, 58, 126, 127, 126, 58, 48, 122, 123, 48, 123, 47, 123, 122, 54, 54, 55, 123, 47, 123, 124, 47, 124, 46, 124, 123, 55, 55, 56, 124, 49, 50, 121, 121, 50, 51, 121, 51, 53, 51, 52, 53, 49, 122, 48, 49, 121, 122, 121, 53, 122, 53, 54, 122], "vertices": [3, 100, -334.54, -35.35, 0.00294, 102, 18.78, -5.94, 0.99401, 103, 98.98, -70.1, 0.00305, 3, 100, -42.99, -32.07, 0.99798, 102, 310.33, -2.66, 0.00152, 103, 390.53, -66.81, 0.0005, 3, 100, -26.03, -0.89, 0.99975, 102, 327.29, 28.52, 0.00019, 103, 407.49, -35.64, 7e-05, 4, 95, -176.11, 60.36, 0, 96, -253.88, 15.46, 8e-05, 101, -45.77, -38.45, 0.11788, 100, -3.06, 27.55, 0.88204, 4, 95, -147.67, 71.3, 0, 96, -225.43, 26.4, 0.00065, 101, -17.32, -27.51, 0.60383, 100, 25.39, 38.49, 0.39551, 2, 101, -20.06, 4.77, 0.98708, 100, 22.65, 70.77, 0.01292, 1, 101, -10.76, 37.59, 1, 1, 101, 5.11, 52.36, 1, 1, 101, 10.58, 19.54, 1, 4, 95, -106.09, 93.73, 0, 96, -183.86, 48.82, 0.0008, 101, 24.25, -5.08, 0.9552, 100, 66.96, 60.92, 0.044, 4, 95, -104.45, 69.12, 0, 96, -182.22, 24.21, 0.00389, 101, 25.89, -29.69, 0.82313, 100, 68.6, 36.31, 0.17298, 4, 95, -108.28, 56.53, 0, 96, -186.05, 11.63, 0.00861, 101, 22.06, -42.28, 0.74228, 100, 64.77, 23.73, 0.24911, 5, 95, 55.27, 62, 0.06479, 96, -22.5, 17.1, 0.81861, 97, -1.14, -72.17, 0.11426, 101, 185.62, -36.81, 0.00234, 100, 228.32, 29.2, 0, 5, 95, 56.73, 85.51, 0.00057, 96, -21.04, 40.6, 0.56765, 97, 0.32, -48.67, 0.43142, 101, 187.07, -13.3, 0.00037, 100, 229.78, 52.7, 0, 4, 96, -31.26, 72.72, 0.09357, 97, -9.9, -16.55, 0.90642, 101, 176.85, 18.82, 1e-05, 100, 219.56, 84.82, 0, 2, 97, -26.69, 10.46, 0.75216, 98, 33.91, -58.72, 0.24784, 2, 97, -47.86, 23.97, 0.36447, 98, 12.74, -45.22, 0.63553, 3, 97, -68.3, 29.08, 0.14103, 98, -7.7, -40.11, 0.85607, 99, 68.39, -109.64, 0.0029, 3, 97, -97.5, 54.63, 0.0005, 98, -36.9, -14.56, 0.86926, 99, 39.19, -84.09, 0.13025, 3, 97, -128.52, 81.64, 0, 98, -67.92, 12.45, 0.47218, 99, 8.17, -57.08, 0.52781, 3, 97, -152.25, 114.12, 0, 98, -91.65, 44.94, 0.08627, 99, -15.56, -24.59, 0.91373, 2, 97, -162.83, 153.54, 0, 99, -26.14, 14.83, 1, 1, 99, -24.32, 45.12, 1, 1, 99, -15.56, 61.95, 1, 1, 99, -0.96, 61.95, 1, 1, 99, 16.2, 52.79, 1, 2, 98, -45.29, 95.67, 0.00372, 99, 30.8, 26.14, 0.99628, 3, 97, -109.91, 139.67, 0, 98, -49.31, 70.49, 0.07066, 99, 26.78, 0.96, 0.92934, 3, 97, -100.42, 114.49, 0, 98, -39.82, 45.3, 0.38108, 99, 36.27, -24.23, 0.61892, 2, 98, -17.19, 23.77, 0.79772, 99, 58.9, -45.76, 0.20228, 2, 97, -48.22, 76.89, 0.04015, 98, 12.38, 7.71, 0.95985, 2, 97, -19.75, 60.83, 0.38197, 98, 40.85, -8.35, 0.61803, 2, 97, 9.45, 32.05, 0.83809, 98, 70.05, -37.13, 0.16191, 4, 96, -4.98, 87.74, 0.07863, 97, 16.38, -1.53, 0.91803, 98, 76.98, -70.71, 0.00334, 100, 245.84, 99.84, 0, 4, 96, 11.81, 73.14, 0.35223, 97, 33.17, -16.13, 0.64777, 101, 219.92, 19.24, 0, 100, 262.63, 85.24, 0, 3, 96, 31.1, 57.08, 0.56653, 97, 52.46, -32.19, 0.43347, 100, 281.92, 69.18, 0, 3, 96, 31.1, 29.71, 0.77341, 97, 52.46, -59.56, 0.22659, 100, 281.92, 41.81, 0, 3, 95, 105.27, 35.56, 0.01931, 96, 27.51, -9.35, 0.97395, 97, 48.87, -98.62, 0.00674, 2, 95, 91.4, 1.62, 0.25886, 96, 13.64, -43.29, 0.74114, 3, 95, 70.6, -25.76, 0.52822, 96, -7.17, -70.67, 0.47178, 100, 243.65, -58.57, 0, 4, 94, 111.32, -32.33, 0.00787, 95, 40.67, -38.9, 0.76785, 96, -37.1, -83.81, 0.22429, 100, 213.72, -71.71, 0, 3, 94, 94.16, -38.9, 0.05383, 95, 23.51, -45.47, 0.85229, 96, -54.25, -90.38, 0.09388, 3, 94, 76.28, -60.07, 0.1622, 95, 5.63, -66.64, 0.82153, 96, -72.14, -111.55, 0.01627, 2, 94, 44.89, -55.32, 0.35614, 95, -25.76, -61.89, 0.64386, 3, 93, 94, -19.18, 0.00053, 94, 16.78, -44.37, 0.69568, 95, -53.87, -50.94, 0.30379, 3, 93, 63.18, -9.93, 0.05339, 94, -14.04, -35.12, 0.9155, 95, -84.69, -41.69, 0.03111, 2, 93, 53.87, 7.15, 0.19303, 94, -23.35, -18.05, 0.80697, 2, 93, 17.79, -2.55, 0.83473, 94, -59.43, -27.75, 0.16527, 2, 92, 36.81, 25.92, 0.29569, 93, -12.48, -21.18, 0.70431, 2, 92, 11.59, 1.48, 0.94358, 93, -37.7, -45.62, 0.05642, 1, 92, 2.65, -15.3, 1, 2, 92, -3.74, -15.3, 0.99971, 93, -53.03, -62.4, 0.00029, 2, 92, -10.73, -15.3, 0.99949, 93, -60.02, -62.4, 0.00051, 2, 92, -1.91, 9.68, 0.96223, 93, -51.2, -37.42, 0.03777, 2, 92, 18.76, 35.82, 0.40946, 93, -30.53, -11.28, 0.59054, 2, 92, 40.95, 56.19, 0.01156, 93, -8.34, 9.09, 0.98844, 2, 93, 20.85, 26.11, 0.61185, 94, -56.37, 0.92, 0.38815, 2, 93, 51.25, 42.23, 0.16203, 94, -25.97, 17.03, 0.83797, 2, 94, 10.2, 28.89, 0.80995, 95, -60.45, 22.32, 0.19005, 4, 94, 52.37, 39.56, 0.20705, 95, -18.28, 32.99, 0.77927, 96, -96.05, -11.92, 0.01368, 100, 154.77, 0.18, 0, 5, 94, 91.22, 44.04, 0.00093, 95, 20.57, 37.47, 0.7093, 96, -57.2, -7.44, 0.28969, 101, 150.91, -61.34, 8e-05, 100, 193.62, 4.66, 0, 5, 95, 42.28, 41.68, 0.36584, 96, -35.49, -3.22, 0.62228, 97, -14.13, -92.49, 0.0114, 101, 172.62, -57.13, 0.00048, 100, 215.33, 8.88, 0, 5, 95, 55.23, 61.58, 0.0662, 96, -22.54, 16.67, 0.81914, 97, -1.18, -72.6, 0.11203, 101, 185.57, -37.23, 0.00263, 100, 228.28, 28.77, 0, 4, 95, -108.41, 55.76, 0, 96, -186.18, 10.85, 0.00765, 101, 21.94, -43.05, 0.74195, 100, 64.64, 22.95, 0.2504, 4, 95, -125.75, 44.07, 0, 96, -203.51, -0.83, 0.0022, 101, 4.6, -54.74, 0.54931, 100, 47.31, 11.26, 0.44849, 4, 95, -143.39, 35.6, 0, 96, -221.16, -9.31, 0.00068, 101, -13.05, -63.21, 0.28363, 100, 29.66, 2.79, 0.71569, 5, 95, -168.12, 21.8, 0, 96, -245.89, -23.11, 0, 100, 4.93, -11.01, 0.99996, 102, 358.25, 18.4, 3e-05, 103, 438.45, -45.76, 1e-05, 3, 100, -15.39, -29.17, 0.99933, 102, 337.93, 0.24, 0.0005, 103, 418.13, -63.91, 0.00017, 3, 100, -27.86, -44.07, 0.99857, 102, 325.46, -14.66, 0.00107, 103, 405.66, -78.82, 0.00036, 3, 100, -35.99, -49.49, 0.99848, 102, 317.33, -20.08, 0.00114, 103, 397.53, -84.24, 0.00038, 3, 100, -43.85, -47.6, 0.99845, 102, 309.47, -18.18, 0.00116, 103, 389.67, -82.34, 0.00039, 3, 100, -43.05, -32.45, 0.99784, 102, 310.28, -3.04, 0.00163, 103, 390.47, -67.19, 0.00054, 3, 100, -334.51, -35.69, 0.003, 102, 18.81, -6.28, 0.99392, 103, 99.01, -70.44, 0.00308, 3, 100, -339.91, -41.34, 0.00088, 102, 13.41, -11.93, 0.99741, 103, 93.61, -76.09, 0.0017, 2, 100, -352.98, -41.34, 7e-05, 102, 0.35, -11.93, 0.99993, 2, 102, -10.87, -4.12, 0.99576, 103, 69.33, -68.28, 0.00424, 3, 100, -367.32, -21.75, 0, 102, -13.99, 7.66, 0.93759, 103, 66.2, -56.49, 0.0624, 3, 100, -369.73, -10.96, 1e-05, 102, -16.41, 18.46, 0.80398, 103, 63.79, -45.7, 0.19601, 3, 100, -375.55, -4.71, 1e-05, 102, -22.23, 24.7, 0.66549, 103, 57.97, -39.45, 0.33451, 3, 100, -383.08, -6.13, 1e-05, 102, -29.76, 23.28, 0.57056, 103, 50.44, -40.87, 0.42944, 3, 100, -396.87, -1.36, 1e-05, 102, -43.55, 28.05, 0.41164, 103, 36.65, -36.11, 0.58836, 3, 100, -412.95, 5.74, 0, 102, -59.63, 35.15, 0.24426, 103, 20.57, -29, 0.75574, 3, 100, -422.07, 16.96, 0, 102, -68.75, 46.37, 0.121, 103, 11.45, -17.78, 0.879, 3, 100, -431.9, 24.68, 0, 102, -78.58, 54.09, 0.02163, 103, 1.62, -10.06, 0.97837, 1, 103, -8.91, -7.96, 1, 1, 103, -9.56, 5.15, 1, 1, 103, -7.74, 14.74, 1, 1, 103, 1.38, 24.8, 1, 1, 103, 15.19, 30.65, 1, 3, 100, -422.07, 50.66, 0, 102, -68.75, 80.07, 0.0033, 103, 11.45, 15.91, 0.9967, 3, 100, -418.8, 38.49, 0, 102, -65.47, 67.9, 0.03174, 103, 14.72, 3.74, 0.96826, 3, 100, -408.5, 29.36, 0, 102, -55.18, 58.77, 0.14213, 103, 25.02, -5.38, 0.85787, 3, 100, -394.93, 19.54, 1e-05, 102, -41.61, 48.95, 0.3293, 103, 38.59, -15.21, 0.67069, 3, 100, -379.95, 13.22, 1e-05, 102, -26.63, 42.63, 0.52773, 103, 53.57, -21.53, 0.47226, 3, 100, -365.21, 5.96, 1e-05, 102, -11.89, 35.38, 0.7066, 103, 68.31, -28.78, 0.29339, 3, 100, -356.09, -2.46, 1e-05, 102, -2.76, 26.95, 0.78561, 103, 77.43, -37.21, 0.21438, 3, 100, -360.06, -13.46, 0, 102, -6.74, 15.95, 0.86986, 103, 73.46, -48.2, 0.13014, 2, 102, 0.05, 5.66, 0.99093, 103, 80.24, -58.5, 0.00907, 2, 100, -346.49, -32.65, 0.00024, 102, 6.83, -3.23, 0.99976, 3, 100, -340.43, -35.26, 0.00113, 102, 12.89, -5.85, 0.9971, 103, 93.09, -70, 0.00177, 1, 103, 1.47, 16.37, 1, 1, 103, 0.21, 4.85, 1, 3, 100, -424.49, 28.79, 0, 102, -71.16, 58.2, 0.04353, 103, 9.03, -5.95, 0.95647, 3, 100, -411.88, 18.71, 0, 102, -58.56, 48.12, 0.17884, 103, 21.64, -16.03, 0.82116, 3, 100, -397.12, 8.81, 1e-05, 102, -43.8, 38.22, 0.36505, 103, 36.4, -25.93, 0.63494, 3, 100, -382.18, 4.85, 1e-05, 102, -28.86, 34.26, 0.54358, 103, 51.34, -29.89, 0.45641, 3, 100, -371.02, 0.17, 1e-05, 102, -17.7, 29.58, 0.68648, 103, 62.5, -34.57, 0.31351, 3, 100, -364.54, -5.05, 1e-05, 102, -11.22, 24.36, 0.7796, 103, 68.98, -39.8, 0.22039, 3, 100, -365.08, -13.15, 0, 102, -11.76, 16.26, 0.85224, 103, 68.44, -47.9, 0.14775, 2, 102, -6.9, 5.28, 0.97014, 103, 73.3, -58.88, 0.02986, 2, 100, -352.84, -35.83, 4e-05, 102, 0.48, -6.42, 0.99996, 3, 100, -343.66, -38.53, 0.00063, 102, 9.66, -9.12, 0.99815, 103, 89.86, -73.28, 0.00122, 3, 100, -36.1, -37.98, 0.99838, 102, 317.22, -8.57, 0.00122, 103, 397.42, -72.73, 0.00041, 3, 100, -24.23, -22.24, 0.99929, 102, 329.1, 7.17, 0.00053, 103, 409.29, -56.99, 0.00018, 3, 100, -9.04, -4.85, 0.9999, 102, 344.28, 24.56, 8e-05, 103, 424.48, -39.59, 3e-05, 4, 95, -163.87, 44.25, 0, 96, -241.63, -0.65, 0.00015, 101, -33.52, -54.56, 0.11418, 100, 9.18, 11.44, 0.88567, 4, 95, -138.01, 54.84, 0, 96, -215.77, 9.93, 0.00137, 101, -7.66, -43.97, 0.49906, 100, 35.05, 22.03, 0.49957, 4, 95, -125.48, 72.23, 0, 96, -203.25, 27.32, 0.00186, 101, 4.86, -26.58, 0.78655, 100, 47.57, 39.42, 0.21159, 4, 95, -128.27, 93.8, 0, 96, -206.03, 48.89, 0.00023, 101, 2.08, -5.01, 0.97043, 100, 44.78, 60.99, 0.02934, 1, 101, -3.49, 16.2, 1, 1, 101, -1.05, 35.33, 1, 2, 92, 6.66, 5.75, 0.94007, 93, -42.63, -41.34, 0.05993, 2, 92, 23.7, 25.8, 0.4735, 93, -25.59, -21.3, 0.5265, 2, 92, 46.08, 44.51, 0.02908, 93, -3.21, -2.59, 0.97092, 2, 93, 26.52, 12.11, 0.62312, 94, -50.7, -13.08, 0.37688, 2, 93, 56.93, 18.12, 0.16663, 94, -20.29, -7.07, 0.83337, 2, 94, 11.11, -6.73, 0.88418, 95, -59.54, -13.31, 0.11582, 2, 94, 52.91, -9.12, 0.26264, 95, -17.74, -15.69, 0.73736, 3, 95, 20.15, -3.74, 0.84394, 96, -57.61, -48.65, 0.15606, 100, 193.21, -36.55, 0, 4, 95, 56.81, 13.97, 0.4036, 96, -20.95, -30.94, 0.59636, 101, 187.16, -84.84, 4e-05, 100, 229.86, -18.84, 0, 2, 95, 76.58, 39.92, 0.03588, 96, -1.18, -4.99, 0.96412, 4, 96, 8.29, 26.72, 0.79536, 97, 29.65, -62.54, 0.20448, 101, 216.4, -27.18, 0.00015, 100, 259.11, 38.82, 0, 4, 96, 4.58, 56.38, 0.46094, 97, 25.94, -32.89, 0.53904, 101, 212.69, 2.48, 3e-05, 100, 255.4, 68.48, 0, 4, 96, -15.19, 82.74, 0.05971, 97, 6.17, -6.53, 0.94029, 101, 192.92, 28.84, 0, 100, 235.63, 94.84, 0, 2, 97, -10.72, 19.01, 0.82098, 98, 49.88, -50.18, 0.17902, 2, 97, -33.37, 42.9, 0.38819, 98, 27.23, -26.29, 0.61181, 3, 97, -66.32, 59.79, 0.02214, 98, -5.72, -9.4, 0.96713, 99, 70.37, -78.93, 0.01072, 2, 98, -30.02, 8.31, 0.79259, 99, 46.07, -61.22, 0.20741, 3, 97, -117.81, 100.98, 0, 98, -57.21, 31.79, 0.3843, 99, 18.88, -37.74, 0.6157, 3, 97, -130.58, 132.69, 0, 98, -69.98, 63.51, 0.04424, 99, 6.12, -6.03, 0.95576, 1, 99, -5.42, 26.92, 1, 1, 99, -9.54, 52.05, 1], "hull": 100, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 0, 198, 176, 200, 200, 202, 202, 204, 204, 206, 206, 208, 208, 210, 210, 212, 212, 214, 214, 216, 216, 218, 218, 220, 220, 222, 140, 224, 224, 226, 226, 228, 228, 230, 230, 232, 232, 234, 234, 236, 236, 238, 238, 240, 102, 242, 242, 244, 244, 246, 246, 248, 248, 250, 250, 252, 252, 254, 254, 256, 256, 258, 258, 260, 260, 262, 262, 264, 264, 266, 266, 268, 268, 270, 270, 272, 272, 274, 274, 276, 276, 278, 278, 280, 280, 282], "width": 362, "height": 214}}, "tx2": {"tx2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [488.16, -309.58, -332.84, -309.58, -332.84, 346.42, 488.16, 346.42], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 328, "height": 262}}, "tx3": {"tx3": {"type": "mesh", "uvs": [0, 1, 0, 0.87777, 0.01624, 0.71825, 0.0495, 0.58803, 0.12288, 0.40898, 0.25016, 0.2755, 0.36598, 0.18435, 0.51046, 0.12575, 0.64722, 0.09832, 0.7533, 0.06101, 0.81869, 0, 0.91981, 0, 1, 0, 1, 0.05158, 1, 0.14868, 0.95401, 0.15767, 0.84634, 0.17565, 0.78048, 0.24577, 0.73994, 0.3159, 0.62721, 0.35546, 0.49041, 0.35186, 0.39288, 0.42199, 0.30294, 0.51909, 0.20186, 0.60535, 0.13195, 0.69315, 0.08221, 0.79048, 0.07145, 0.884, 0.02723, 0.87592, 0.04319, 0.75223, 0.09227, 0.64248, 0.16222, 0.51357, 0.26408, 0.41253, 0.36594, 0.31846, 0.46657, 0.26445, 0.56474, 0.23309, 0.67274, 0.20348, 0.7543, 0.16172, 0.82368, 0.09606, 0.9316, 0.07417], "triangles": [11, 12, 13, 38, 11, 13, 37, 10, 11, 37, 11, 38, 9, 10, 37, 38, 13, 14, 15, 38, 14, 16, 37, 38, 16, 38, 15, 36, 37, 16, 36, 9, 37, 8, 9, 36, 35, 8, 36, 34, 7, 8, 34, 8, 35, 17, 36, 16, 17, 18, 35, 17, 35, 36, 19, 34, 35, 19, 35, 18, 20, 34, 19, 33, 6, 7, 33, 7, 34, 32, 5, 6, 32, 6, 33, 20, 33, 34, 31, 5, 32, 20, 21, 32, 20, 32, 33, 31, 32, 21, 22, 31, 21, 4, 5, 31, 30, 4, 31, 30, 31, 22, 3, 4, 30, 23, 30, 22, 29, 3, 30, 29, 30, 23, 24, 29, 23, 2, 3, 29, 28, 2, 29, 28, 29, 24, 25, 28, 24, 28, 1, 2, 27, 28, 25, 27, 1, 28, 26, 27, 25, 0, 1, 27, 0, 27, 26], "vertices": [1, 105, -7.8, -47.78, 1, 1, 105, -7.8, -9.04, 1, 2, 105, -0.49, 41.53, 0.7301, 106, -75.58, -83.34, 0.2699, 2, 105, 14.47, 82.81, 0.36187, 106, -60.61, -42.06, 0.63813, 3, 105, 47.5, 139.57, 0.01904, 106, -27.59, 14.7, 0.97975, 107, -123.08, -30.19, 0.00121, 2, 106, 29.69, 57.01, 0.46323, 107, -65.8, 12.13, 0.53677, 3, 106, 81.8, 85.91, 0.02247, 107, -13.69, 41.02, 0.94573, 108, -132.85, 12.46, 0.0318, 2, 107, 51.33, 59.6, 0.4955, 108, -67.83, 31.03, 0.5045, 3, 107, 112.87, 68.29, 0.03527, 108, -6.29, 39.73, 0.92303, 109, -119.73, -7.61, 0.04171, 2, 108, 41.45, 51.56, 0.56447, 109, -71.99, 4.22, 0.43553, 2, 108, 70.88, 70.89, 0.2242, 109, -42.57, 23.56, 0.7758, 2, 108, 116.38, 70.89, 0.00383, 109, 2.93, 23.56, 0.99617, 1, 109, 39.02, 23.56, 1, 1, 109, 39.02, 7.21, 1, 1, 109, 39.02, -23.57, 1, 2, 108, 131.77, 20.91, 0.00143, 109, 18.32, -26.42, 0.99857, 2, 108, 83.32, 15.21, 0.30976, 109, -30.13, -32.12, 0.69024, 2, 108, 53.68, -7.02, 0.75834, 109, -59.77, -54.35, 0.24166, 2, 108, 35.44, -29.25, 0.93392, 109, -78.01, -76.58, 0.06608, 2, 107, 103.87, -13.22, 0.12086, 108, -15.29, -41.79, 0.87914, 2, 107, 42.31, -12.08, 0.72391, 108, -76.85, -40.65, 0.27609, 3, 106, 93.91, 10.58, 0.13962, 107, -1.58, -34.31, 0.85928, 108, -120.74, -62.88, 0.00109, 3, 105, 128.52, 104.67, 0.00049, 106, 53.44, -20.2, 0.69117, 107, -42.05, -65.09, 0.30834, 3, 105, 83.04, 77.32, 0.11133, 106, 7.95, -47.55, 0.88094, 107, -87.54, -92.44, 0.00773, 2, 105, 51.58, 49.49, 0.42996, 106, -23.51, -75.38, 0.57004, 2, 105, 29.19, 18.64, 0.82834, 106, -45.89, -106.23, 0.17166, 2, 105, 24.35, -11.01, 0.99309, 106, -50.73, -135.88, 0.00691, 1, 105, 4.45, -8.45, 1, 2, 105, 11.63, 30.76, 0.78819, 106, -63.45, -94.11, 0.21181, 2, 105, 33.72, 65.55, 0.41047, 106, -41.36, -59.32, 0.58953, 2, 105, 65.2, 106.42, 0.06703, 106, -9.89, -18.45, 0.93297, 2, 106, 35.95, 13.58, 0.66503, 107, -59.54, -31.31, 0.33497, 2, 106, 81.78, 43.4, 0.07669, 107, -13.71, -1.49, 0.92331, 2, 107, 31.58, 15.63, 0.7671, 108, -87.58, -12.94, 0.2329, 2, 107, 75.76, 25.57, 0.31749, 108, -43.4, -3, 0.68251, 2, 108, 5.2, 6.39, 0.97803, 109, -108.25, -40.95, 0.02197, 2, 108, 41.9, 19.63, 0.70569, 109, -71.55, -27.71, 0.29431, 2, 108, 73.12, 40.44, 0.30291, 109, -40.33, -6.89, 0.69709, 1, 109, 8.24, 0.04, 1], "hull": 27, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 0, 52, 0, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76], "width": 225, "height": 158}}, "ym1": {"ym1": {"type": "mesh", "uvs": [1, 0.00946, 0.82403, 0.02062, 0.57567, 0.06527, 0.3151, 0.13702, 0.09931, 0.23906, 0, 0.3698, 0, 0.51489, 0.10746, 0.65041, 0.16446, 0.78275, 0.22146, 0.89276, 0.12374, 1, 0.22146, 1, 0.36803, 0.96451, 0.53496, 0.92146, 0.49424, 0.83855, 0.38838, 0.74129, 0.27438, 0.65201, 0.18888, 0.52127, 0.1726, 0.40488, 0.26624, 0.30602, 0.43724, 0.20876, 0.62453, 0.12904, 0.84845, 0.07643, 1, 0.04932, 1, 0.02831, 0.33594, 0.86481, 0.2807, 0.76204, 0.18862, 0.65387, 0.08947, 0.51405, 0.07934, 0.3833, 0.18236, 0.26982, 0.37321, 0.17193, 0.6046, 0.09785, 0.84612, 0.04825], "triangles": [21, 31, 32, 3, 2, 32, 21, 32, 22, 22, 32, 33, 32, 2, 33, 22, 33, 23, 2, 1, 33, 33, 24, 23, 33, 1, 24, 1, 0, 24, 19, 29, 30, 5, 4, 30, 19, 30, 20, 30, 31, 20, 30, 4, 31, 4, 3, 31, 20, 31, 21, 31, 3, 32, 17, 27, 28, 6, 28, 7, 28, 18, 17, 6, 29, 28, 6, 5, 29, 28, 29, 18, 18, 29, 19, 29, 5, 30, 12, 11, 9, 11, 10, 9, 9, 25, 12, 12, 25, 13, 25, 14, 13, 9, 8, 25, 8, 26, 25, 25, 26, 14, 26, 15, 14, 8, 27, 26, 8, 7, 27, 27, 16, 26, 26, 16, 15, 16, 27, 17, 27, 7, 28], "vertices": [1, 59, 39.99, -0.6, 1, 1, 59, 30.77, 3.22, 1, 1, 59, 15.58, 5.11, 1, 2, 58, 35.39, 4.62, 0.73517, 59, -2.22, 4.05, 0.26483, 1, 58, 16.74, 7.94, 1, 2, 57, 37.32, 4.21, 0.6888, 58, -2.27, 3.57, 0.3112, 1, 57, 16.76, 7.04, 1, 2, 56, 41.61, 4.03, 0.94387, 57, -3.26, 3.71, 0.05613, 1, 56, 22.47, 5.4, 1, 1, 56, 6.43, 6.03, 1, 1, 56, -7.17, 14.97, 1, 1, 56, -8.47, 9.66, 1, 1, 56, -5.48, 0.48, 1, 1, 56, -1.71, -10.06, 1, 1, 56, 10.35, -10.65, 1, 1, 56, 25.26, -8.18, 1, 2, 56, 39.18, -5, 0.99164, 57, -4.76, -5.52, 0.00836, 1, 57, 14.42, -3.32, 1, 2, 57, 31.03, -4.68, 0.88464, 58, -1.87, -7.31, 0.11536, 1, 58, 13.01, -4.91, 1, 2, 58, 29.83, -6.39, 0.86895, 59, -1.8, -8.27, 0.13105, 1, 59, 13.12, -4.09, 1, 1, 59, 27.74, -4.28, 1, 1, 59, 37, -5.45, 1, 1, 59, 38.58, -2.89, 1, 1, 56, 8.8, -1.15, 1, 1, 56, 23.81, -1.62, 1, 1, 56, 40.06, -0.27, 1, 1, 57, 16.2, 2.06, 1, 1, 57, 34.8, 0.08, 1, 1, 58, 15.2, 1.73, 1, 2, 58, 32.65, -0.67, 0.97514, 59, -2.08, -1.91, 0.02486, 1, 59, 14.51, 0.29, 1, 1, 59, 29.75, -0.79, 1], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48, 24, 50, 50, 52, 52, 54, 56, 54, 58, 56, 60, 58, 60, 62, 62, 64, 64, 66], "width": 56, "height": 143}}, "ym2": {"ym2": {"type": "mesh", "uvs": [0.09149, 1, 0.16032, 0.97963, 0.18866, 0.90684, 0.27774, 0.8264, 0.51257, 0.7485, 0.72715, 0.66167, 0.8972, 0.57357, 0.99842, 0.47907, 1, 0.35777, 1, 0.27349, 0.92149, 0.18283, 0.80813, 0.07174, 0.71501, 0.01172, 0.62665, 0.00116, 0.5806, 0.03929, 0.64967, 0.1001, 0.72019, 0.18908, 0.74465, 0.28303, 0.74897, 0.37471, 0.70698, 0.46871, 0.63601, 0.54855, 0.45384, 0.63361, 0.23381, 0.69404, 0.05874, 0.76642, 0, 0.84999, 0, 0.91565, 0, 0.99101, 0.07582, 0.91694, 0.11332, 0.844, 0.21959, 0.77697, 0.37169, 0.72177, 0.57589, 0.64883, 0.76758, 0.5588, 0.84259, 0.47271, 0.87801, 0.36693, 0.87176, 0.27427, 0.81551, 0.18161, 0.72591, 0.08436], "triangles": [17, 35, 34, 35, 9, 8, 17, 16, 35, 16, 36, 35, 35, 10, 9, 35, 36, 10, 16, 15, 36, 15, 37, 36, 37, 11, 36, 10, 36, 11, 15, 14, 37, 37, 14, 13, 13, 12, 37, 37, 12, 11, 5, 32, 6, 32, 33, 6, 6, 33, 7, 20, 19, 32, 32, 19, 33, 33, 34, 7, 7, 34, 8, 19, 18, 33, 33, 18, 34, 18, 17, 34, 34, 35, 8, 4, 29, 30, 29, 22, 30, 4, 31, 5, 4, 30, 31, 30, 21, 31, 30, 22, 21, 5, 31, 32, 31, 20, 32, 31, 21, 20, 1, 0, 27, 0, 26, 27, 26, 25, 27, 1, 27, 2, 2, 27, 28, 28, 27, 24, 27, 25, 24, 2, 28, 3, 24, 23, 28, 28, 29, 3, 28, 23, 29, 4, 3, 29, 29, 23, 22], "vertices": [1, 63, -1.92, -3.39, 1, 1, 63, 1.47, -5.25, 1, 1, 63, 10.83, -3.44, 1, 2, 63, 21.9, -3.68, 0.99932, 64, -7.53, -7.07, 0.00068, 2, 63, 34.51, -9.71, 0.07457, 64, 6.42, -7.87, 0.92543, 2, 64, 20.72, -7.28, 0.82924, 65, -10.97, -2.32, 0.17076, 1, 65, 1.76, -6.52, 1, 1, 65, 14.67, -7.78, 1, 1, 65, 30.04, -4.28, 1, 2, 65, 40.71, -1.8, 0.16292, 66, 7.43, -5.37, 0.83708, 1, 66, 19.6, -4.4, 1, 1, 66, 34.66, -2.53, 1, 1, 66, 43.04, -0.23, 1, 1, 66, 45.06, 3.07, 1, 1, 66, 40.54, 5.85, 1, 1, 66, 32.25, 4.54, 1, 1, 66, 20.34, 3.86, 1, 1, 66, 8.16, 5.15, 1, 2, 65, 25.57, 5.25, 0.85785, 66, -3.58, 7.19, 0.14215, 1, 65, 13.27, 4.16, 1, 2, 64, 29.52, 5.08, 0.55731, 65, 2.51, 4.65, 0.44269, 1, 64, 16.26, 3.63, 1, 2, 63, 37.71, 3.34, 0.01313, 64, 4.43, 5.43, 0.98687, 2, 63, 26.55, 7.27, 0.99338, 64, -7.39, 4.83, 0.00662, 1, 63, 15.47, 6.2, 1, 1, 63, 7.35, 3.57, 1, 1, 63, -1.97, 0.54, 1, 1, 63, 8.15, 0.56, 1, 1, 63, 17.64, 2.02, 1, 1, 63, 27.28, 0.57, 1, 2, 63, 36.03, -3.14, 0.01051, 64, 5.33, -1.21, 0.98949, 2, 64, 17.98, -1.47, 0.98993, 65, -10.75, 4.1, 0.01007, 1, 65, 2.43, -0.91, 1, 1, 65, 14.03, -1.37, 1, 2, 65, 27.75, 0.33, 0.98608, 66, -3.58, 1.81, 0.01392, 2, 65, 39.42, 3.3, 0.00147, 66, 8.31, -0.19, 0.99853, 1, 66, 20.57, -0.16, 1, 1, 66, 33.68, 1.09, 1], "hull": 27, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 0, 52, 0, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 26], "width": 41, "height": 130}}, "ym3": {"ym3": {"type": "mesh", "uvs": [0, 0.95928, 0.07445, 0.8865, 0.22054, 0.75858, 0.33086, 0.57773, 0.37558, 0.37483, 0.4203, 0.16972, 0.6141, 0.02195, 0.88243, 0, 1, 0.03739, 1, 0.05945, 1, 0.09473, 0.88243, 0.10135, 0.70354, 0.1609, 0.57236, 0.26897, 0.52167, 0.37703, 0.54254, 0.5645, 0.52465, 0.74094, 0.38751, 0.86003, 0.19371, 0.97913, 0, 1, 0.83648, 0.06015, 0.63658, 0.10136, 0.49567, 0.22014, 0.44651, 0.38498, 0.4334, 0.59345, 0.36459, 0.75829, 0.21384, 0.87465, 0.09915, 0.94252], "triangles": [7, 8, 9, 20, 6, 7, 20, 7, 9, 11, 20, 9, 10, 11, 9, 21, 6, 20, 12, 21, 20, 12, 20, 11, 5, 6, 21, 22, 5, 21, 22, 21, 12, 13, 22, 12, 4, 5, 22, 22, 13, 23, 22, 23, 4, 13, 14, 23, 23, 14, 15, 3, 4, 23, 24, 3, 23, 15, 24, 23, 16, 24, 15, 25, 3, 24, 25, 24, 16, 2, 3, 25, 17, 25, 16, 26, 2, 25, 26, 1, 2, 27, 1, 26, 0, 1, 27, 18, 27, 26, 26, 25, 17, 18, 26, 17, 19, 0, 27, 19, 27, 18], "vertices": [1, 60, -4.46, 1.75, 1, 1, 60, 2.11, 2.86, 1, 2, 60, 14.25, 4.25, 0.97528, 61, -3.14, 8.48, 0.02472, 2, 60, 27.64, 9.78, 0.00069, 61, 10.54, 3.71, 0.99931, 2, 61, 25.51, 2.61, 0.99932, 62, -5.3, -0.41, 0.00068, 2, 61, 40.64, 1.53, 0.13891, 62, 7.84, 7.16, 0.86109, 1, 62, 22.82, 5.83, 1, 1, 62, 33.19, -4.41, 1, 1, 62, 35.08, -11.06, 1, 1, 62, 33.83, -12.07, 1, 1, 62, 31.83, -13.69, 1, 1, 62, 27.45, -9.07, 1, 1, 62, 17.99, -4.31, 1, 1, 62, 7.4, -3.79, 1, 2, 61, 26.04, -5.26, 0.6265, 62, -0.45, -6.63, 0.3735, 1, 61, 12.51, -7.59, 1, 2, 60, 27.09, -6.07, 0.24272, 61, -0.4, -7.76, 0.75728, 1, 60, 15.74, -7.32, 1, 1, 60, 2.16, -6.47, 1, 1, 60, -6.5, -0.41, 1, 1, 62, 28.22, -5.25, 1, 1, 62, 19.08, 1.24, 1, 2, 61, 37.33, -2.85, 0.05084, 62, 7.55, 1.68, 0.94916, 2, 61, 25.11, -1.27, 0.93876, 62, -3.46, -3.85, 0.06124, 1, 61, 9.89, -1.9, 1, 1, 60, 19.93, -1.06, 1, 1, 60, 8.18, -1.67, 1, 1, 60, 0.27, -1.03, 1], "hull": 20, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 0, 38, 18, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54], "width": 54, "height": 73}}, "ym4": {"ym4": {"type": "mesh", "uvs": [0, 0, 0.07186, 0.01597, 0.17864, 0.09476, 0.33548, 0.17172, 0.55237, 0.22486, 0.81264, 0.19555, 1, 0.1589, 1, 0.17539, 1, 0.19555, 0.85269, 0.2725, 0.62912, 0.30732, 0.43892, 0.29633, 0.26874, 0.24685, 0.35883, 0.34213, 0.37552, 0.47773, 0.32213, 0.61149, 0.24872, 0.72144, 0.26874, 0.82588, 0.38553, 0.91567, 0.57906, 0.97613, 0.52234, 1, 0.4556, 1, 0.26206, 0.95231, 0.14861, 0.89734, 0.11858, 0.81305, 0.11191, 0.69028, 0.17197, 0.5895, 0.20868, 0.44841, 0.17864, 0.30915, 0.0752, 0.16073, 0, 0.04712, 0.07155, 0.07097, 0.14513, 0.14481, 0.20602, 0.19218, 0.2593, 0.32314, 0.28975, 0.45829, 0.24154, 0.60458, 0.17558, 0.70907, 0.1705, 0.82192, 0.23647, 0.88741, 0.35572, 0.94314, 0.32527, 0.21865, 0.51048, 0.26045, 0.71853, 0.25627, 0.84285, 0.22422], "triangles": [38, 24, 37, 16, 38, 37, 38, 16, 17, 39, 38, 17, 23, 24, 38, 23, 38, 39, 39, 17, 18, 40, 39, 18, 22, 39, 40, 23, 39, 22, 18, 20, 40, 21, 40, 20, 22, 40, 21, 19, 20, 18, 35, 27, 34, 35, 13, 14, 26, 27, 35, 36, 26, 35, 14, 36, 35, 15, 36, 14, 25, 26, 36, 37, 25, 36, 16, 37, 36, 15, 16, 36, 24, 25, 37, 31, 30, 0, 1, 31, 0, 31, 1, 2, 32, 31, 2, 29, 31, 32, 30, 31, 29, 33, 29, 32, 34, 28, 33, 28, 29, 33, 12, 34, 33, 13, 34, 12, 27, 28, 34, 13, 35, 34, 7, 5, 6, 44, 5, 7, 8, 44, 7, 43, 4, 5, 43, 5, 44, 42, 41, 4, 42, 4, 43, 9, 44, 8, 43, 44, 9, 10, 42, 43, 10, 43, 9, 11, 42, 10, 32, 2, 3, 33, 32, 3, 41, 33, 3, 41, 3, 4, 12, 33, 41, 11, 41, 42, 12, 41, 11], "vertices": [1, 80, -24.21, -3.79, 1, 2, 83, -17.5, 11.04, 0.00543, 80, -19.34, 1.73, 0.99457, 2, 83, -1.18, 6.32, 0.79442, 80, -3.12, 6.79, 0.20558, 1, 83, 18.74, 4.72, 1, 2, 83, 40.71, 9.85, 0.07148, 84, 8.51, 5.38, 0.92852, 1, 84, 33.72, 6.77, 1, 1, 84, 52.25, 10.43, 1, 1, 84, 51.84, 7.61, 1, 1, 84, 51.32, 4.16, 1, 1, 84, 35.53, -6.96, 1, 1, 84, 13.63, -9.8, 1, 2, 83, 39.6, -6.51, 0.6034, 84, -3.96, -5.26, 0.3966, 2, 83, 21.55, -9.47, 0.36553, 80, 24.54, 6.15, 0.63447, 2, 80, 42.93, 8.77, 0.99995, 81, -10.44, 3.88, 5e-05, 1, 81, 12.15, 10.39, 1, 1, 81, 35.84, 10.32, 1, 2, 81, 55.91, 7.52, 0.26994, 82, 4.13, 6.29, 0.73006, 1, 82, 21.69, 1.64, 1, 1, 82, 40.15, 6.49, 1, 1, 82, 56.47, 19.96, 1, 1, 82, 58.41, 13.45, 1, 1, 82, 56.16, 7.53, 1, 1, 82, 41.91, -6.73, 1, 1, 82, 29.19, -13.42, 1, 1, 82, 14.55, -10.9, 1, 2, 81, 53.38, -6.32, 0.9157, 82, -5.53, -3.95, 0.0843, 1, 81, 35.14, -4.43, 1, 2, 80, 55.56, -10.78, 0.01519, 81, 10.54, -6.18, 0.98481, 1, 80, 31.88, -5.49, 1, 1, 80, 4.4, -6.26, 1, 1, 80, -16.51, -6.49, 1, 2, 83, -11.77, 3.45, 0.04034, 80, -10.37, -1.44, 0.95966, 2, 83, 1.52, -2.5, 0.19042, 80, 4, 0.92, 0.80958, 2, 83, 11.08, -5.53, 0.36109, 80, 13.65, 3.66, 0.63891, 1, 80, 36.7, 0.94, 1, 1, 81, 10.58, 1.71, 1, 1, 81, 36.29, 2.58, 1, 2, 81, 55.28, 0.28, 0.60311, 82, -0.34, 0.55, 0.39689, 1, 82, 17.73, -6.84, 1, 1, 82, 30.55, -5.01, 1, 1, 82, 43.59, 2.16, 1, 2, 83, 22.88, -2.33, 0.93531, 80, 21.72, 12.84, 0.06469, 1, 84, 3.68, -0.12, 1, 1, 84, 23.33, -2.31, 1, 1, 84, 35.83, 1.44, 1], "hull": 31, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 0, 60, 0, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 40, 66, 82, 82, 84, 84, 86, 86, 88], "width": 95, "height": 173}}, "ym5": {"ym5": {"type": "mesh", "uvs": [0, 0.70615, 0.05873, 0.76172, 0.11594, 0.82524, 0.21679, 0.83847, 0.33721, 0.75908, 0.41549, 0.63469, 0.46968, 0.43885, 0.55247, 0.22978, 0.66386, 0.07893, 0.76472, 0, 0.9303, 0, 1, 0, 1, 0.1742, 1, 0.288, 0.9017, 0.28535, 0.75869, 0.37533, 0.69848, 0.53942, 0.68795, 0.7829, 0.66988, 0.93375, 0.55699, 0.99462, 0.3899, 1, 0.24238, 1, 0.11895, 1, 0.04669, 0.90464, 0, 0.80936, 0, 0.74585, 0.06122, 0.85421, 0.1236, 0.90984, 0.22575, 0.92096, 0.31344, 0.90348, 0.40023, 0.86057, 0.50012, 0.7796, 0.54873, 0.56723, 0.60765, 0.39371, 0.69898, 0.22279, 0.82125, 0.14768, 0.92548, 0.15165], "triangles": [0, 1, 25, 26, 24, 25, 26, 1, 2, 26, 25, 1, 23, 24, 26, 27, 2, 3, 26, 2, 27, 23, 26, 27, 27, 3, 28, 22, 23, 27, 22, 27, 28, 30, 4, 5, 29, 3, 4, 29, 4, 30, 28, 3, 29, 21, 28, 29, 21, 22, 28, 20, 29, 30, 19, 20, 30, 21, 29, 20, 31, 5, 32, 31, 32, 17, 30, 5, 31, 18, 31, 17, 19, 31, 18, 19, 30, 31, 33, 7, 34, 6, 7, 33, 16, 33, 15, 32, 6, 33, 32, 33, 16, 5, 6, 32, 17, 32, 16, 34, 8, 9, 34, 9, 35, 7, 8, 34, 15, 34, 35, 33, 34, 15, 35, 9, 10, 36, 35, 10, 36, 10, 11, 12, 36, 11, 14, 35, 36, 36, 12, 13, 14, 36, 13, 15, 35, 14], "vertices": [1, 85, -18.89, 5.51, 1, 1, 85, 6.33, 5.19, 1, 1, 85, 31.81, 3.08, 1, 2, 86, 4.41, 19.9, 0.43189, 85, 66.93, 18.19, 0.56811, 2, 87, -0.96, 36.94, 0.1392, 86, 52.75, 25.13, 0.8608, 3, 88, -35.34, 23.49, 0.00048, 87, 36.99, 24.65, 0.94629, 86, 88.05, 43.69, 0.05323, 2, 88, 11.24, 28.31, 0.84, 87, 83.7, 27.92, 0.16, 2, 89, -25.6, 15.55, 0.00308, 88, 65.93, 25.46, 0.99692, 1, 89, 27.22, 21.46, 1, 1, 89, 68.62, 16.2, 1, 1, 90, 44.57, 15.82, 1, 1, 90, 70.73, 18.88, 1, 2, 90, 75.09, -18.32, 0.99778, 89, 125.25, -61.93, 0.00222, 2, 90, 77.94, -42.62, 0.98936, 89, 112.57, -82.86, 0.01064, 3, 90, 40.96, -46.38, 0.80431, 89, 81.09, -63.1, 0.19311, 88, 125.46, -92.97, 0.00258, 4, 90, -10.47, -71.88, 0.07174, 89, 24.83, -51.62, 0.61093, 88, 80.5, -57.27, 0.31644, 87, 150.06, -59.93, 0.00089, 4, 90, -28.98, -109.56, 0.00011, 89, -12.92, -69.99, 0.10425, 88, 38.52, -56.56, 0.75511, 87, 108.13, -57.82, 0.14053, 3, 89, -43.47, -112.69, 0.00038, 88, -8.04, -80.81, 0.29363, 87, 60.78, -80.49, 0.70599, 3, 88, -39.19, -92.13, 0.1449, 87, 29.27, -90.76, 0.85497, 86, 165.23, -42.48, 0.00013, 3, 88, -72.83, -62.79, 0.03775, 87, -3.37, -60.32, 0.88736, 86, 120.64, -44.56, 0.0749, 2, 87, -35.9, -6.17, 0.03523, 86, 59.17, -29.99, 0.96477, 2, 86, 5.16, -16.14, 0.91226, 85, 91.57, -8.12, 0.08774, 1, 85, 50.2, -29.7, 1, 1, 85, 16.5, -24.16, 1, 1, 85, -8.62, -14.16, 1, 1, 85, -14.94, -2.06, 1, 1, 85, 16.36, -12.01, 1, 1, 85, 42.79, -11.7, 1, 2, 86, 3.29, 1.88, 0.79908, 85, 78.13, 4.04, 0.20092, 1, 86, 36.33, -2.71, 1, 1, 86, 70.4, -1.92, 1, 3, 88, -44.91, -20.13, 0.01376, 87, 25.96, -18.62, 0.98357, 86, 111.3, 5.56, 0.00267, 3, 89, -64.42, -45.76, 0.00014, 88, 3.56, -11.64, 0.69268, 87, 74.69, -11.75, 0.30718, 3, 89, -26.04, -25.4, 0.02559, 88, 47.01, -10.86, 0.97303, 87, 118.14, -12.43, 0.00138, 3, 90, -36.71, -41.93, 0.00615, 89, 22.54, -11.87, 0.91698, 88, 96.44, -20.79, 0.07687, 3, 90, 7.32, -20.51, 0.50879, 89, 70.43, -22.03, 0.48827, 88, 134.55, -51.52, 0.00295, 2, 90, 46.55, -16.78, 0.96558, 89, 103.68, -43.18, 0.03442], "hull": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 0, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 24], "width": 378, "height": 215}}, "ym6": {"ym6": {"type": "mesh", "uvs": [0, 0, 0.02527, 0, 0.09252, 0.05526, 0.17966, 0.11504, 0.28146, 0.15662, 0.37982, 0.17222, 0.4852, 0.13458, 0.54503, 0.06548, 0.648, 0.07437, 0.7821, 0.12645, 0.87219, 0.24165, 0.94352, 0.37482, 0.99125, 0.53959, 1, 0.68788, 1, 0.80321, 0.96042, 0.89309, 0.87689, 0.96499, 0.80132, 1, 0.72375, 0.99045, 0.78342, 0.93952, 0.85004, 0.87661, 0.90374, 0.78224, 0.93457, 0.68039, 0.9455, 0.5321, 0.89479, 0.41077, 0.81524, 0.32389, 0.71083, 0.27146, 0.59747, 0.243, 0.4851, 0.27596, 0.3586, 0.28218, 0.23516, 0.255, 0.12527, 0.20151, 0.05127, 0.11715, 0, 0.03431, 0.07319, 0.08797, 0.15268, 0.15262, 0.25918, 0.2017, 0.37125, 0.22086, 0.48381, 0.20402, 0.56917, 0.16018, 0.63901, 0.15872, 0.75633, 0.20094, 0.85135, 0.28653, 0.92028, 0.39879, 0.96406, 0.53069, 0.96872, 0.68925, 0.94543, 0.7973, 0.85973, 0.92359, 0.788, 0.9699], "triangles": [44, 45, 22, 13, 45, 12, 46, 22, 45, 21, 22, 46, 45, 13, 14, 46, 45, 14, 15, 46, 14, 47, 20, 21, 47, 21, 46, 19, 20, 47, 15, 16, 47, 15, 47, 46, 48, 19, 47, 18, 19, 48, 17, 48, 47, 17, 47, 16, 18, 48, 17, 43, 42, 11, 24, 42, 43, 12, 44, 11, 43, 11, 44, 23, 43, 44, 24, 43, 23, 22, 23, 44, 12, 45, 44, 41, 40, 8, 9, 41, 8, 41, 9, 10, 27, 39, 40, 26, 40, 41, 27, 40, 26, 42, 41, 10, 25, 41, 42, 26, 41, 25, 42, 10, 11, 25, 42, 24, 8, 39, 7, 40, 39, 8, 6, 7, 39, 38, 5, 6, 38, 6, 39, 37, 4, 5, 37, 5, 38, 27, 28, 38, 27, 38, 39, 37, 38, 28, 29, 36, 37, 29, 37, 28, 30, 36, 29, 34, 33, 0, 34, 1, 2, 34, 0, 1, 32, 33, 34, 35, 2, 3, 34, 2, 35, 31, 34, 35, 32, 34, 31, 36, 3, 4, 35, 3, 36, 36, 4, 37, 30, 35, 36, 31, 35, 30], "vertices": [1, 67, -12.34, 4.55, 1, 1, 67, -4.13, 8.29, 1, 1, 67, 23.15, 6.34, 1, 1, 67, 57.33, 6.35, 1, 2, 67, 94.49, 12.46, 0.88668, 68, 3.43, 14.1, 0.11332, 1, 68, 37.84, 6.16, 1, 1, 68, 76.26, 10.44, 1, 2, 67, 171.15, 71.16, 0, 68, 99.46, 24.1, 1, 3, 67, 205.47, 84.49, 0, 68, 135.69, 17.54, 0.68653, 69, 11.39, 26.47, 0.31347, 3, 67, 254.14, 93.12, 0, 68, 181.7, -0.54, 0.01081, 69, 60.5, 32.14, 0.98919, 2, 69, 100.32, 18.2, 0.75949, 70, -11.16, 14.05, 0.24051, 2, 70, 28.81, 20.96, 0.99998, 71, -45.78, -51.42, 2e-05, 2, 67, 362.7, 35.01, 0, 70, 71.21, 16.86, 1, 3, 67, 380.13, 4.33, 0, 70, 103.44, 2.5, 0.0501, 71, 1.39, 9.29, 0.9499, 2, 67, 391.47, -20.55, 0, 71, 23.24, 25.73, 1, 2, 67, 387.45, -45.79, 0, 71, 48.75, 27.24, 1, 2, 67, 367.38, -73.66, 0, 71, 80.29, 13.66, 1, 1, 71, 103.15, -2.92, 1, 1, 71, 117.98, -26.4, 1, 1, 71, 95.53, -16.64, 1, 1, 71, 69.32, -6.6, 1, 1, 71, 39.92, -4.72, 1, 2, 70, 90.53, -17.05, 0.16183, 71, 14.02, -10.44, 0.83817, 3, 67, 347.11, 29.85, 0, 70, 61.72, 3.45, 0.99996, 71, -16.42, -28.45, 4e-05, 2, 70, 27.79, 1.61, 0.99999, 71, -28.51, -60.2, 1e-05, 2, 69, 88.34, -7.29, 0.97686, 70, -4.01, -13.19, 0.02314, 1, 69, 49.09, -9.07, 1, 1, 69, 8.9, -17.3, 1, 2, 68, 72.15, -22.81, 0.95932, 69, -25.74, -38.99, 0.04068, 2, 67, 131.89, -3.19, 0, 68, 27.15, -18.78, 1, 2, 67, 89.12, -15.61, 0.42122, 68, -15.81, -7.03, 0.57878, 2, 67, 48.17, -20.35, 0.99992, 68, -53.21, 10.33, 8e-05, 1, 67, 15.84, -13.12, 1, 1, 67, -8.96, -2.85, 1, 1, 67, 20.09, -3.58, 1, 2, 67, 52.26, -5.75, 0.99989, 68, -42.09, 20.64, 0.00011, 2, 67, 91.69, -0.56, 0.96857, 68, -5.76, 4.47, 0.03143, 1, 68, 33.4, -4.91, 1, 2, 68, 73.77, -5.83, 0.99003, 69, -32.29, -23.25, 0.00997, 2, 67, 188.3, 54.31, 0, 68, 105.28, 0.77, 1, 3, 67, 210.84, 64.97, 0, 68, 130.07, -1.92, 0.23419, 69, 15.57, 6.66, 0.76581, 3, 67, 253.1, 73.24, 0, 68, 170.42, -16.94, 0.00487, 69, 58.25, 12.36, 0.99513, 2, 69, 97.19, 5.6, 0.80341, 70, -5.48, 2.38, 0.19659, 2, 70, 29.74, 10.95, 0.99998, 71, -36.25, -54.64, 2e-05, 2, 67, 353, 32.9, 0, 70, 64.65, 9.4, 1, 2, 67, 370.1, -0.6, 0, 71, 8.37, 0.57, 1, 2, 67, 373.16, -27.35, 0, 71, 33.83, 9.32, 1, 2, 67, 357.74, -67.28, 0, 71, 76.14, 2.86, 1, 1, 71, 100.3, -11, 1], "hull": 34, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 0, 66, 0, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96], "width": 357, "height": 237}}, "ym7": {"ym7": {"type": "mesh", "uvs": [0.01292, 0.2799, 0.03547, 0.25842, 0.06854, 0.32389, 0.0951, 0.44765, 0.11364, 0.58982, 0.11915, 0.71972, 0.12316, 0.77188, 0.15323, 0.86701, 0.19833, 0.92633, 0.26047, 0.93554, 0.3216, 0.89462, 0.3657, 0.79439, 0.39928, 0.65937, 0.42584, 0.51413, 0.44538, 0.36071, 0.48296, 0.24001, 0.55412, 0.11318, 0.62829, 0.03442, 0.70095, 0, 0.77963, 0, 0.84678, 0.02624, 0.90741, 0.09068, 0.96754, 0.18887, 1, 0.29218, 1, 0.41799, 0.99661, 0.47424, 0.97656, 0.5397, 0.94299, 0.57141, 0.94249, 0.54482, 0.97105, 0.45788, 0.97205, 0.37707, 0.95802, 0.26763, 0.92244, 0.16739, 0.8598, 0.09579, 0.79265, 0.0692, 0.71799, 0.07432, 0.6495, 0.11271, 0.58536, 0.17714, 0.53324, 0.25386, 0.49502, 0.33485, 0.46295, 0.44021, 0.44892, 0.52203, 0.44741, 0.64682, 0.42536, 0.73069, 0.38628, 0.81865, 0.34669, 0.924, 0.26984, 1, 0.1885, 1, 0.11137, 0.94852, 0.07931, 0.83987, 0.08035, 0.73439, 0.07208, 0.62786, 0.04624, 0.50445, 0.01213, 0.3842, 0, 0.34201, 0.04274, 0.35957, 0.0699, 0.47164, 0.09115, 0.61504, 0.09824, 0.73073, 0.10414, 0.80665, 0.13248, 0.90427, 0.1927, 0.95849, 0.26532, 0.96693, 0.33145, 0.90909, 0.37455, 0.80665, 0.41352, 0.67891, 0.43654, 0.51864, 0.4578, 0.38608, 0.49322, 0.27521, 0.5688, 0.14265, 0.63669, 0.07758, 0.70931, 0.03419, 0.78784, 0.04022, 0.85692, 0.06673, 0.91655, 0.13421, 0.9626, 0.22098, 0.98327, 0.31016, 0.98681, 0.41982, 0.97677, 0.49815], "triangles": [26, 27, 78, 78, 27, 28, 26, 78, 25, 78, 28, 29, 25, 78, 77, 78, 29, 77, 25, 77, 24, 29, 30, 77, 24, 77, 76, 77, 30, 76, 76, 23, 24, 30, 31, 76, 23, 76, 75, 76, 31, 75, 75, 22, 23, 31, 32, 75, 22, 75, 74, 33, 74, 32, 74, 75, 32, 74, 73, 21, 22, 74, 21, 74, 33, 73, 36, 71, 35, 34, 73, 33, 70, 18, 71, 35, 72, 34, 35, 71, 72, 34, 72, 73, 72, 20, 73, 21, 73, 20, 71, 19, 72, 72, 19, 20, 71, 18, 19, 38, 69, 37, 16, 69, 15, 37, 70, 36, 37, 69, 70, 70, 69, 17, 69, 16, 17, 36, 70, 71, 70, 17, 18, 13, 66, 12, 42, 66, 41, 41, 66, 40, 67, 40, 66, 14, 67, 13, 13, 67, 66, 40, 67, 39, 39, 67, 68, 67, 14, 68, 14, 15, 68, 39, 68, 38, 69, 38, 68, 15, 69, 68, 46, 63, 45, 45, 64, 44, 45, 63, 64, 63, 11, 64, 63, 10, 11, 44, 64, 43, 43, 64, 65, 64, 11, 65, 11, 12, 65, 43, 65, 42, 66, 42, 65, 12, 66, 65, 46, 47, 62, 47, 61, 62, 46, 62, 63, 62, 10, 63, 62, 61, 9, 61, 8, 9, 62, 9, 10, 48, 60, 47, 47, 60, 61, 60, 7, 61, 61, 7, 8, 48, 49, 60, 49, 59, 60, 60, 59, 7, 59, 6, 7, 49, 50, 59, 50, 58, 59, 59, 58, 6, 58, 5, 6, 50, 51, 58, 51, 57, 58, 58, 57, 5, 57, 4, 5, 51, 52, 57, 52, 56, 57, 57, 56, 4, 56, 3, 4, 53, 55, 52, 52, 55, 56, 56, 55, 3, 55, 2, 3, 53, 54, 55, 54, 0, 55, 0, 1, 55, 55, 1, 2], "vertices": [1, 72, -12.18, -4.06, 1, 1, 72, -13.2, 7.02, 1, 1, 72, 5.29, 16.09, 1, 1, 72, 34.81, 18.11, 1, 1, 72, 66.91, 15.4, 1, 2, 72, 94.52, 8.11, 0.58647, 73, 1.18, 8.14, 0.41353, 1, 73, 11.33, 2.6, 1, 1, 73, 36.04, 0.56, 1, 2, 73, 58.63, 8.63, 0.44736, 74, 5.21, 8.49, 0.55264, 2, 73, 77.15, 29.42, 1e-05, 74, 32.32, 2.12, 0.99999, 2, 74, 60.72, 6.66, 0.52632, 75, 2.69, 6.9, 0.47368, 1, 75, 31.88, 2.63, 1, 2, 75, 64.82, 6.49, 0.96469, 76, -21.14, 5.35, 0.03531, 1, 76, 12.68, 8.36, 1, 1, 76, 46.76, 14.98, 1, 1, 76, 77.86, 11.23, 1, 1, 77, 25.03, 11.8, 1, 1, 77, 62.4, 11.67, 1, 1, 78, 8.58, 9.09, 1, 1, 78, 43.67, 11.47, 1, 1, 78, 74, 7.76, 1, 1, 79, 17.07, 11.33, 1, 1, 79, 51.13, 16.34, 1, 1, 79, 77.57, 11.54, 1, 1, 79, 97.73, -7.24, 1, 1, 79, 105.71, -16.74, 1, 1, 79, 110.1, -33.07, 1, 1, 79, 104.95, -48.78, 1, 1, 79, 100.54, -44.98, 1, 1, 79, 95.31, -22.66, 1, 1, 79, 82.66, -10.27, 1, 1, 79, 60.85, 1.47, 1, 1, 79, 33.94, 4.8, 1, 2, 78, 80.84, -7.04, 0.21854, 79, 3.39, -5, 0.78146, 1, 78, 50.5, -3.26, 1, 2, 77, 93.85, -14.69, 0.00934, 78, 17.28, -6.63, 0.99066, 1, 77, 62.83, -7.91, 1, 1, 77, 30.89, -7.09, 1, 2, 76, 84.82, -10.36, 0.51038, 77, 2.45, -11.15, 0.48962, 1, 76, 61.44, -2.6, 1, 1, 76, 34.44, 0.38, 1, 1, 76, 15.57, -1.69, 1, 1, 75, 79.01, -9.92, 1, 1, 75, 58.26, -11.87, 1, 1, 75, 32.55, -7.97, 1, 2, 74, 70.78, -1.46, 0.0618, 75, 3.54, -6, 0.9382, 2, 74, 34.24, -12.48, 0.99844, 75, -29.33, 13.41, 0.00156, 2, 73, 68.74, -4.69, 0.44403, 74, -1.67, -6.75, 0.55597, 1, 73, 38.79, -25.15, 1, 2, 72, 113.28, -17.54, 0.01061, 73, 11.2, -22.02, 0.98939, 2, 72, 91.69, -9.3, 0.71171, 73, -6.84, -7.58, 0.28829, 1, 72, 68.48, -4.9, 1, 1, 72, 39.14, -6.65, 1, 1, 72, 9.21, -12.1, 1, 1, 72, -1.32, -14.09, 1, 1, 72, 8.75, 2.6, 1, 1, 72, 35.95, 5.73, 1, 1, 72, 68.72, 4.07, 1, 2, 72, 93.64, -1.51, 0.66993, 73, -2.6, -0.75, 0.33007, 1, 73, 12.19, -8.79, 1, 1, 73, 36.86, -11.77, 1, 2, 73, 62.68, 2.34, 0.45003, 74, 1.62, 1.93, 0.54997, 2, 74, 33.38, -5.01, 0.99921, 75, -24.42, 19.1, 0.00079, 2, 74, 64.57, 2.84, 0.33905, 75, 2.49, 1.48, 0.66095, 1, 75, 31.83, -2.15, 1, 1, 75, 64.77, -1.18, 1, 1, 76, 13.86, 3.62, 1, 1, 76, 44.14, 7.57, 1, 1, 76, 72.88, 3.76, 1, 1, 77, 27.84, 3.04, 1, 1, 77, 61.34, 1.56, 1, 1, 78, 12.82, 1.88, 1, 1, 78, 47.93, 2.93, 1, 1, 78, 79.12, -0.78, 1, 1, 79, 26.83, 7.82, 1, 1, 79, 54.77, 9.94, 1, 1, 79, 75.35, 3.38, 1, 1, 79, 94.01, -11.83, 1, 1, 79, 103.5, -26.8, 1], "hull": 55, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 0, 108, 0, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 54], "width": 447, "height": 219}}}}], "events": {"atk": {}}, "animations": {"boss_attack1": {"slots": {"tx2": {"color": [{"color": "ffffffbb", "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 0.3333, "color": "ffffff65", "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.6667, "color": "ffffffbb"}]}}, "bones": {"bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 10.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.82, "curve": 0.25, "c3": 0.75, "c4": 1.33}, {"time": 0.4333, "angle": 10.71, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -15.11, "y": -15.11, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 91.58, "y": 57.87, "curve": 0.25, "c3": 0.75, "c4": 1.33}, {"time": 0.4333, "x": -63.17, "y": -28.99, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone32": {"rotate": [{"curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 0.1, "angle": 9.99, "curve": 0.295, "c2": 0.2, "c3": 0.647, "c4": 0.6}, {"time": 0.3333, "angle": -4.21, "curve": 0.337, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 0.4333, "angle": 29.37, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.6667}], "scale": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -1.233, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}]}, "bone33": {"rotate": [{"angle": -0.68, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 9.99, "curve": 0.295, "c2": 0.2, "c3": 0.647, "c4": 0.6}, {"time": 0.4333, "angle": -4.21, "curve": 0.353, "c2": 0.4, "c3": 0.705, "c4": 0.8}, {"time": 0.6667, "angle": -0.68}]}, "bone34": {"rotate": [{"angle": -1.09, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 9.99, "curve": 0.295, "c2": 0.2, "c3": 0.647, "c4": 0.6}, {"time": 0.4667, "angle": -4.21, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 0.6667, "angle": -1.09}]}, "bone43": {"rotate": [{"curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 0.1, "angle": 9.99, "curve": 0.295, "c2": 0.2, "c3": 0.647, "c4": 0.6}, {"time": 0.3333, "angle": -4.21, "curve": 0.337, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 0.4333, "angle": -23.85, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.6667}], "scale": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -0.843, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}]}, "bone44": {"rotate": [{"curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 0.1, "angle": 9.99, "curve": 0.295, "c2": 0.2, "c3": 0.647, "c4": 0.6}, {"time": 0.3333, "angle": -4.21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}]}, "bone45": {"rotate": [{"curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 0.1, "angle": 9.99, "curve": 0.295, "c2": 0.2, "c3": 0.647, "c4": 0.6}, {"time": 0.3333, "angle": -4.21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}]}, "bone35": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 10.29, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone36": {"rotate": [{"angle": 2.49, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 10.29, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": 2.49}]}, "bone53": {"rotate": [{"angle": 2.49, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 10.29, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": 2.49}]}, "bone52": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 10.29, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone54": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 10.29, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone37": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 10.29, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone38": {"rotate": [{"angle": 5.14, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 10.29, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 5.14}]}, "bone39": {"rotate": [{"angle": 9.85, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 10.29, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.6667, "angle": 9.85}]}, "bone40": {"rotate": [{"angle": 2.49, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 10.29, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": 2.49}]}, "bone41": {"rotate": [{"angle": 6.5, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 10.29, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": 6.5}]}, "bone42": {"rotate": [{"angle": 9.85, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 10.29, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.6667, "angle": 9.85}]}, "bone49": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 10.29, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone50": {"rotate": [{"angle": 5.14, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 10.29, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 5.14}]}, "bone51": {"rotate": [{"angle": 9.85, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 10.29, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.6667, "angle": 9.85}]}, "bone47": {"rotate": [{"angle": 6.5, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 10.29, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": 6.5}]}, "bone46": {"rotate": [{"angle": 2.49, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 10.29, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": 2.49}]}, "bone48": {"rotate": [{"angle": 9.85, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 10.29, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.6667, "angle": 9.85}]}, "bone4": {"rotate": [{"angle": 1.63, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -5.81, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 5.74, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "angle": 1.63}]}, "bone5": {"rotate": [{"angle": 4.11, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -5.81, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 5.74, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "angle": 4.11}]}, "bone6": {"rotate": [{"angle": 5.74, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -5.81, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 5.74}]}, "bone7": {"rotate": [{"angle": -0.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "angle": 5.74, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -5.81, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": -0.03}]}, "bone8": {"rotate": [{"angle": -5.81, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 5.74, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -5.81}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.81, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 5.74, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone13": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -5.86, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 6.75, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.6667, "angle": 1.25}]}, "bone14": {"rotate": [{"angle": 4.01, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -5.86, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 6.75, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.6667, "angle": 4.01}]}, "bone66": {"rotate": [{"angle": -0.42, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.3, "angle": -9.83, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 17.16, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.6667, "angle": -0.42}]}, "bone70": {"rotate": [{"angle": 13.71, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": 17.16, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -9.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6333, "angle": 7.23, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.6667, "angle": 13.71}]}, "bone69": {"rotate": [{"angle": 14.93, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -9.83, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 17.16, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": 14.93}]}, "bone68": {"rotate": [{"angle": 6.33, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -9.83, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 17.16, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6333, "angle": 10.85, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.6667, "angle": 6.33}]}, "bone67": {"rotate": [{"angle": 2.19, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -9.83, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 17.16, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6333, "angle": 6.31, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.6667, "angle": 2.19}]}, "bone71": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -9.8, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 6.63, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone78": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -9.8, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 6.63, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone77": {"rotate": [{"angle": -7.67, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": 6.63, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -9.8, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": -7.67}]}, "bone76": {"rotate": [{"angle": -8.53, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "angle": -9.8, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.2, "angle": 4.49, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2333, "angle": 6.63, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -8.53}]}, "bone75": {"rotate": [{"angle": 6.63, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -9.8, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 6.63}]}, "bone74": {"rotate": [{"angle": 4.19, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -9.8, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 6.63, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": 4.19}]}, "bone73": {"rotate": [{"angle": 2.44, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -9.8, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 6.63, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 2.44}]}, "bone72": {"rotate": [{"angle": 0.86, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -9.8, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 6.63, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": 0.86}]}, "bone84": {"rotate": [{"angle": -6.27, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -17.05, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -6.27}]}, "bone89": {"rotate": [{"angle": -16.32, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0333, "angle": -17.05, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 0.6667, "angle": -16.32}]}, "bone88": {"rotate": [{"angle": -16.32, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -17.05, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.6667, "angle": -16.32}]}, "bone87": {"rotate": [{"angle": -12.92, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -17.05, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.6667, "angle": -12.92}]}, "bone86": {"rotate": [{"angle": -8.52, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -17.05, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": -8.52}]}, "bone85": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -17.05, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone83": {"rotate": [{"angle": -2.22, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -17.05, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -2.22}]}, "bone82": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -17.05, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone19": {"rotate": [{"angle": 4.3, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -24.52, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 11.7, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 4.3}]}, "bone20": {"rotate": [{"angle": 10.18, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -24.52, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 11.7, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": 10.18}]}, "bone25": {"rotate": [{"angle": 7.4, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -24.52, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 11.7, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": 7.4}]}, "bone26": {"rotate": [{"angle": 6.99, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": 11.7, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -24.52, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": 6.99}]}, "bone108": {"translate": [{"x": 27.86, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "x": -19.26, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 55.27, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "x": 27.86}]}, "bone107": {"translate": [{"x": 27.86, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "x": -19.26, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 55.27, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "x": 27.86}]}, "bone106": {"translate": [{"x": -16.75, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 55.27, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -19.26, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "x": -16.75}]}, "bone105": {"translate": [{"x": -16.75, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 55.27, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -19.26, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "x": -16.75}]}, "bone104": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 55.27, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -19.26, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone102": {"translate": [{"x": 45.58, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "x": -19.26, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 55.27, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "x": 45.58}]}, "bone101": {"translate": [{"x": 45.58, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "x": -19.26, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 55.27, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "x": 45.58}]}, "bone100": {"translate": [{"x": 27.86, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "x": -19.26, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 55.27, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "x": 27.86}]}, "bone99": {"translate": [{"x": 27.86, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "x": -19.26, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 55.27, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "x": 27.86}]}, "bone98": {"translate": [{"x": 27.86, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "x": -19.26, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 55.27, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "x": 27.86}]}, "bone97": {"translate": [{"x": 27.86, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "x": -19.26, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 55.27, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "x": 27.86}]}, "bone96": {"translate": [{"x": -16.75, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 55.27, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -19.26, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "x": -16.75}]}, "bone95": {"translate": [{"x": -16.75, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 55.27, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -19.26, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "x": -16.75}]}, "bone94": {"translate": [{"x": -16.75, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 55.27, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -19.26, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "x": -16.75}]}, "bone93": {"translate": [{"x": -16.75, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 55.27, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -19.26, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "x": -16.75}]}, "bone92": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 55.27, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -19.26, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone91": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 55.27, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -19.26, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone103": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 26.84, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -29.32, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone90": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 26.84, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -29.32, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "light": {"scale": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "x": 0.858, "y": 0.858, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}]}}, "events": [{"time": 0.4333, "name": "atk"}]}, "boss_idle": {"slots": {"eye": {"attachment": [{"time": 0.8, "name": null}, {"time": 0.8333, "name": "eye"}]}, "tx2": {"color": [{"color": "ffffffbb", "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "color": "ffffff65", "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "color": "ffffffbb"}]}}, "bones": {"bone2": {"translate": [{"y": 23.64, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "y": 40.2, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 1.3333, "y": 23.64}]}, "bone3": {"rotate": [{"angle": -0.04, "curve": 0.344, "c2": 0.66, "c3": 0.677}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -3.88, "curve": 0.248, "c3": 0.736, "c4": 0.94}, {"time": 1.3333, "angle": -0.04}]}, "bone9": {"rotate": [{"curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 0.6333, "angle": -9.04, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.3333}], "scale": [{"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "y": 0.54, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "bone13": {"rotate": [{"angle": 6.35, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "angle": 11.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.9, "angle": -5.73, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.3333, "angle": 6.35}]}, "bone14": {"rotate": [{"angle": -0.94, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333, "angle": 11.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.1, "angle": -5.73, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "angle": -0.94}]}, "bone19": {"rotate": [{"angle": -1.41, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -14.6, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 1.3333, "angle": -1.41}]}, "bone20": {"rotate": [{"angle": -7.3, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -14.6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": -7.3}]}, "bone21": {"rotate": [{"angle": -13.19, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -14.6, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 1.3333, "angle": -13.19}]}, "bone22": {"rotate": [{"angle": -13.19, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1, "angle": -14.6, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 1.3333, "angle": -13.19}]}, "bone23": {"rotate": [{"angle": -13.19, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1, "angle": -14.6, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 1.3333, "angle": -13.19}]}, "bone24": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -14.6, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "bone25": {"rotate": [{"angle": -5.52, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -19.44, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.3333, "angle": -5.52}]}, "bone26": {"rotate": [{"angle": -13.92, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -19.44, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "angle": -13.92}]}, "bone27": {"rotate": [{"angle": -3.79, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -13.35, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.3333, "angle": -3.79}]}, "bone28": {"rotate": [{"angle": -9.56, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -13.35, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "angle": -9.56}]}, "bone29": {"rotate": [{"angle": -13.35, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -13.35}]}, "bone30": {"rotate": [{"angle": 7.53, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 20.46, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.3333, "angle": 7.53}]}, "bone31": {"rotate": [{"angle": 17.05, "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 20.46, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 1.3333, "angle": 17.05}]}, "bone108": {"translate": [{"x": 13.97, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333, "x": 49.22, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "x": 13.97}]}, "bone43": {"rotate": [{"angle": 5.62, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -51.14, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 5.62}]}, "bone44": {"rotate": [{"angle": 3.18, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0667, "angle": 5.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.7333, "angle": -55.45, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.1667, "angle": -11.71, "curve": 0.362, "c2": 0.45, "c3": 0.704, "c4": 0.82}, {"time": 1.3333, "angle": 3.18}]}, "bone45": {"rotate": [{"angle": -10.55, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.3667, "angle": 5.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1, "angle": -24.03, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.2333, "angle": -15.62, "curve": 0.326, "c2": 0.31, "c3": 0.662, "c4": 0.65}, {"time": 1.3333, "angle": -10.55}]}, "bone46": {"rotate": [{"angle": -14.38, "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 0.4333, "angle": 5.62, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -24.03, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 1.3333, "angle": -14.38}]}, "bone47": {"rotate": [{"angle": -23.7, "curve": 0.264, "c2": 0.06, "c3": 0.752}, {"time": 0.6333, "angle": 5.62, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.0667, "angle": -15.62, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.3, "angle": -24.03, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 1.3333, "angle": -23.7}]}, "bone48": {"rotate": [{"angle": -16.82, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": -24.03, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 5.62, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.0667, "angle": -2.79, "curve": 0.326, "c2": 0.31, "c3": 0.68, "c4": 0.71}, {"time": 1.3333, "angle": -16.82}]}, "bone49": {"rotate": [{"angle": -1.59, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": 5.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.8667, "angle": -24.03, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.0667, "angle": -15.62, "curve": 0.326, "c2": 0.31, "c3": 0.68, "c4": 0.71}, {"time": 1.3333, "angle": -1.59}]}, "bone50": {"rotate": [{"angle": -14.38, "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 0.4333, "angle": 5.62, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -24.03, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 1.3333, "angle": -14.38}]}, "bone51": {"rotate": [{"angle": -23.7, "curve": 0.264, "c2": 0.06, "c3": 0.752}, {"time": 0.6333, "angle": 5.62, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.0667, "angle": -15.62, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.3, "angle": -24.03, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 1.3333, "angle": -23.7}]}, "bone52": {"rotate": [{"angle": 5.44, "curve": 0.256, "c2": 0.03, "c3": 0.628, "c4": 0.52}, {"time": 0.6333, "angle": -24.03, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.0667, "angle": -2.79, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.3, "angle": 5.62, "curve": 0.328, "c3": 0.661, "c4": 0.34}, {"time": 1.3333, "angle": 5.44}]}, "bone53": {"rotate": [{"angle": -1.59, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": 5.62, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.8667, "angle": -24.03, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.0667, "angle": -15.62, "curve": 0.326, "c2": 0.31, "c3": 0.68, "c4": 0.71}, {"time": 1.3333, "angle": -1.59}]}, "bone54": {"rotate": [{"angle": -14.38, "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 0.4333, "angle": 5.62, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -24.03, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 1.3333, "angle": -14.38}]}, "bone58": {"rotate": [{"angle": 2.41, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 9.96, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "angle": 2.41}]}, "bone66": {"rotate": [{"angle": -0.43, "curve": 0.33, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 0.1333, "angle": 1.74, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5667, "angle": -4.15, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.8, "angle": -6.48, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "angle": -0.43}]}, "bone67": {"rotate": [{"angle": -3.79, "curve": 0.312, "c2": 0.26, "c3": 0.656, "c4": 0.63}, {"time": 0.3667, "angle": 1.74, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.5667, "angle": -0.59, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1, "angle": -6.48, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 1.3333, "angle": -3.79}]}, "bone68": {"rotate": [{"angle": -4.78, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.5667, "angle": 17.97, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": -6.48, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 1.3333, "angle": -4.78}]}, "bone69": {"rotate": [{"angle": -1.4, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "angle": -6.48, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5667, "angle": 21.53, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.8, "angle": 32.63, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.3333, "angle": -1.4}]}, "bone70": {"rotate": [{"angle": 20.38, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.3667, "angle": -6.48, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.5667, "angle": 7.53, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1, "angle": 42.89, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1.3333, "angle": 20.38}]}, "bone71": {"rotate": [{"angle": -2.77, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.2333, "angle": -6.48, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.9, "angle": 4.94, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 1.3333, "angle": -2.77}]}, "bone72": {"rotate": [{"angle": 1.7, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333, "angle": -6.48, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.1, "angle": 4.94, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "angle": 1.7}]}, "bone73": {"rotate": [{"angle": 4.94, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -6.48, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.94}]}, "bone74": {"rotate": [{"angle": 1.7, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "angle": 4.94, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -6.48, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.3333, "angle": 1.7}]}, "bone75": {"rotate": [{"angle": -3.24, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333, "angle": 4.94, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -6.48, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "angle": -3.24}]}, "bone76": {"rotate": [{"angle": -6.48, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 4.94, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -6.48}]}, "bone77": {"rotate": [{"angle": -3.24, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "angle": -6.48, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.9, "angle": 4.94, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.3333, "angle": -3.24}]}, "bone78": {"rotate": [{"angle": 1.7, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333, "angle": -6.48, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.1, "angle": 4.94, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "angle": 1.7}]}, "bone79": {"rotate": [{"angle": 1.22, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.2333, "angle": 4.94, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -6.48, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 1.3333, "angle": 1.22}]}, "bone80": {"rotate": [{"angle": -5.28, "curve": 0.287, "c2": 0.17, "c3": 0.643, "c4": 0.59}, {"time": 0.5, "angle": 4.94, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.9, "angle": -2.28, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.1667, "angle": -6.48, "curve": 0.301, "c3": 0.637, "c4": 0.36}, {"time": 1.3333, "angle": -5.28}]}, "bone81": {"rotate": [{"angle": -4.97, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "angle": -6.48, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.8, "angle": 4.94, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.9, "angle": 3.84, "curve": 0.297, "c2": 0.21, "c3": 0.693, "c4": 0.75}, {"time": 1.3333, "angle": -4.97}]}, "bone82": {"rotate": [{"angle": -6.48, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 4.94, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -6.48}]}, "bone83": {"rotate": [{"angle": -6.48, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 4.94, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -6.48}]}, "bone84": {"rotate": [{"angle": -3.54, "curve": 0.306, "c2": 0.24, "c3": 0.653, "c4": 0.62}, {"time": 0.4, "angle": 4.94, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.8333, "angle": -3.24, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.0667, "angle": -6.48, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.3333, "angle": -3.54}]}, "bone85": {"rotate": [{"angle": -6.34, "curve": 0.262, "c2": 0.06, "c3": 0.631, "c4": 0.53}, {"time": 0.6333, "angle": 4.94, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.8333, "angle": 1.7, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.2667, "angle": -6.48, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 1.3333, "angle": -6.34}]}, "bone86": {"rotate": [{"angle": -4.17, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1667, "angle": -6.48, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 4.94, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1.3333, "angle": -4.17}]}, "bone87": {"rotate": [{"angle": 0.74, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4, "angle": -6.48, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.8333, "angle": 1.7, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.0667, "angle": 4.94, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "angle": 0.74}]}, "bone88": {"rotate": [{"angle": 4.68, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 0.6333, "angle": -6.48, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.8333, "angle": -3.24, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.2667, "angle": 4.94, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 1.3333, "angle": 4.68}]}, "bone89": {"rotate": [{"angle": 0.94, "curve": 0.328, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.1667, "angle": 4.94, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -6.48, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 1.3333, "angle": 0.94}]}, "bone90": {"translate": [{"y": 11.33, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "y": 39.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.3333, "y": 11.33}]}, "bone91": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -33.26, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "bone92": {"translate": [{"x": -9.44, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": -33.26, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.3333, "x": -9.44}]}, "bone93": {"translate": [{"x": -23.82, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "x": -33.26, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "x": -23.82}]}, "bone94": {"translate": [{"x": -33.26, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -33.26}]}, "bone95": {"translate": [{"x": -23.82, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "x": -33.26, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.3333, "x": -23.82}]}, "bone96": {"translate": [{"x": -9.44, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333, "x": -33.26, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "x": -9.44}]}, "bone97": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -33.26, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "bone98": {"translate": [{"x": -9.44, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": -33.26, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.3333, "x": -9.44}]}, "bone99": {"translate": [{"x": -23.82, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "x": -33.26, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "x": -23.82}]}, "bone100": {"translate": [{"x": -33.26, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -33.26}]}, "bone101": {"translate": [{"x": -23.82, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "x": -33.26, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.3333, "x": -23.82}]}, "bone102": {"translate": [{"x": -9.44, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333, "x": -33.26, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "x": -9.44}]}, "bone103": {"translate": [{"y": 16.44, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "y": 39.91, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 1.3333, "y": 16.44}]}, "bone104": {"translate": [{"x": 11.92, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": 49.22, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "x": 11.92}]}, "bone105": {"translate": [{"x": 33.2, "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": 49.22, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 1.3333, "x": 33.2}]}, "bone106": {"translate": [{"x": 48.66, "curve": 0.264, "c2": 0.06, "c3": 0.752}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "x": 49.22, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 1.3333, "x": 48.66}]}, "bone107": {"translate": [{"x": 37.3, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "x": 49.22, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "x": 37.3}]}, "bone32": {"rotate": [{"angle": 26.49, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -23.11, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 26.49}]}, "bone33": {"rotate": [{"angle": -14.11, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -49.56, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.1, "angle": -35.5, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 1.3333, "angle": -14.11}]}, "bone34": {"rotate": [{"angle": -14.53, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -20.28, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "angle": -14.53}]}, "bone35": {"rotate": [{"angle": -5.76, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -20.28, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.3333, "angle": -5.76}]}, "bone37": {"rotate": [{"angle": -14.53, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -20.28, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "angle": -14.53}]}, "bone36": {"rotate": [{"angle": -14.53, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -20.28, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "angle": -14.53}]}, "bone38": {"rotate": [{"angle": -20.28, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -20.28}]}, "bone39": {"rotate": [{"angle": -14.53, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "angle": -20.28, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.3333, "angle": -14.53}]}, "bone41": {"rotate": [{"angle": -14.53, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "angle": -20.28, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.3333, "angle": -14.53}]}, "bone40": {"rotate": [{"angle": -20.28, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -20.28}]}, "bone42": {"rotate": [{"angle": -5.76, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333, "angle": -20.28, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "angle": -5.76}]}, "bone4": {"rotate": [{"angle": -3.12, "curve": 0.336, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 0.0333, "angle": -2.72, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -9.59, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 1.3333, "angle": -3.12}]}, "bone5": {"rotate": [{"angle": -7.27, "curve": 0.33, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.0333, "angle": -6.87, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -9.59, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": -7.27}]}, "bone6": {"rotate": [{"angle": -9.48, "curve": 0.344, "c2": 0.66, "c3": 0.677}, {"time": 0.0333, "angle": -9.59, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "curve": 0.248, "c3": 0.736, "c4": 0.94}, {"time": 1.3333, "angle": -9.48}]}, "bone8": {"rotate": [{"angle": -2.32, "curve": 0.33, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.0333, "angle": -2.72, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4667, "angle": -9.59, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.3333, "angle": -2.32}]}, "bone7": {"rotate": [{"angle": -6.87, "curve": 0.336, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.0333, "angle": -7.27, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2333, "angle": -9.59, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.3333, "angle": -6.87}]}, "bone10": {"rotate": [{"angle": 13.36, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -10.04, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 13.36}]}, "bone11": {"rotate": [{"angle": 13.36, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -10.04, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 13.36}]}, "bone12": {"rotate": [{"angle": 13.36, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -10.04, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 13.36}]}, "bone55": {"rotate": [{"angle": 13.36, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -10.04, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 13.36}]}, "bone56": {"rotate": [{"angle": 13.36, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -10.04, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 13.36}]}, "bone57": {"rotate": [{"angle": 13.36, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -10.04, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 13.36}]}, "bone59": {"rotate": [{"angle": 13.36, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -10.04, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 13.36}]}, "bone60": {"rotate": [{"angle": 13.36, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -10.04, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 13.36}]}, "bone61": {"rotate": [{"angle": 13.36, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -10.04, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 13.36}]}, "bone62": {"rotate": [{"angle": 13.36, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -10.04, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 13.36}]}, "bone63": {"rotate": [{"angle": 13.36, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -10.04, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 13.36}]}, "bone64": {"rotate": [{"angle": 13.36, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -10.04, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 13.36}]}, "bone65": {"rotate": [{"angle": 13.36, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -10.04, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 13.36}]}, "light": {"scale": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "x": 0.858, "y": 0.858, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333}]}}}, "die": {"slots": {"tx1": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2, "color": "ffffff00"}]}, "tx2": {"color": [{"color": "ffffffbb", "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "color": "ffffff00"}]}, "tx3": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2, "color": "ffffff00"}]}}, "bones": {"bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -103.72}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -33.49, "y": -17.32, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.2333, "x": 11.13, "y": 86.21, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.3333, "x": 41.57, "y": 94.69, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 41.57, "y": -207.87}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -11.35}]}, "bone13": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -9.74}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -23.56}]}, "bone26": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -96.45}]}, "bone32": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 70.31}]}, "bone66": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 123.19}]}, "bone71": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 161.45}]}, "bone73": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -73.05}]}, "bone79": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -147.68}]}, "bone82": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 127.87}]}, "bone84": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 108.86}]}}}, "hurt": {"slots": {"tx2": {"color": [{"color": "ffffffbb", "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "color": "ffffff65", "curve": 0.25, "c3": 0.75}, {"time": 0.5, "color": "ffffffbb"}]}}, "bones": {"bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 8.56, "curve": 0.25, "c3": 0.717, "c4": 1.24}, {"time": 0.2333, "angle": -14.02, "curve": 0.25, "c3": 0.75}, {"time": 0.5}], "translate": [{"curve": 0.25, "c3": 0.736, "c4": 1.43}, {"time": 0.1, "x": -20.94, "curve": 0.25, "c3": 0.736, "c4": 1.43}, {"time": 0.2333, "x": 109.63, "y": 18.32, "curve": 0.25, "c3": 0.736, "c4": 1.43}, {"time": 0.5}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 6.42, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -6.28, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone4": {"rotate": [{"angle": -1.16, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 6.42, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -6.28, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": -1.16}]}, "bone5": {"rotate": [{"angle": -3.14, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 6.42, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -6.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": -3.14}]}, "bone6": {"rotate": [{"angle": -5.12, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 6.42, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -6.28, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5, "angle": -5.12}]}, "bone7": {"rotate": [{"angle": -6.28, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 6.42, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -6.28}]}, "bone8": {"rotate": [{"angle": -3.43, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "angle": -6.28, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 6.42, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.5, "angle": -3.43}]}, "bone32": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 6.59, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -21.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone33": {"rotate": [{"angle": -3.95, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 6.59, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -21.39, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": -3.95}]}, "bone34": {"rotate": [{"angle": -10.7, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 6.59, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -21.39, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": -10.7}]}, "bone43": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 6.59, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -21.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone44": {"rotate": [{"angle": -3.95, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 6.59, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -21.39, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": -3.95}]}, "bone45": {"rotate": [{"angle": -10.7, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 6.59, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -21.39, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": -10.7}]}, "bone35": {"rotate": [{"angle": -0.66, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 7.37, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -10.52, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 0.5, "angle": -0.66}]}, "bone36": {"rotate": [{"angle": -5.26, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 7.37, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -10.52, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": -5.26}]}, "bone52": {"rotate": [{"angle": -0.66, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 7.37, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -10.52, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 0.5, "angle": -0.66}]}, "bone53": {"rotate": [{"angle": -5.26, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 7.37, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -10.52, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": -5.26}]}, "bone54": {"rotate": [{"angle": -8.58, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 7.37, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -10.52, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5, "angle": -8.58}]}, "bone37": {"rotate": [{"angle": -3.32, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.0667, "angle": -1.23, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 7.37, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -6.68, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": -3.32}]}, "bone38": {"rotate": [{"angle": -5.46, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.0667, "angle": -3.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 7.37, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -6.68, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5, "angle": -5.46}]}, "bone39": {"rotate": [{"angle": -6.68, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0667, "angle": -5.45, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2667, "angle": 7.37, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -6.68}]}, "bone40": {"rotate": [{"angle": -5.44, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 0.1333, "angle": -1.23, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 7.37, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -6.68, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5, "angle": -5.44}]}, "bone41": {"rotate": [{"angle": -6.68, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "angle": -3.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": 7.37, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -6.68}]}, "bone42": {"rotate": [{"angle": -3.53, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "angle": -6.68, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1333, "angle": -5.45, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 7.37, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.5, "angle": -3.53}]}, "bone46": {"rotate": [{"angle": -5.44, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 0.1333, "angle": -1.23, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 7.37, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -6.68, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5, "angle": -5.44}]}, "bone47": {"rotate": [{"angle": -6.68, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "angle": -3.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": 7.37, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -6.68}]}, "bone48": {"rotate": [{"angle": -3.53, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "angle": -6.68, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1333, "angle": -5.45, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 7.37, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.5, "angle": -3.53}]}, "bone49": {"rotate": [{"angle": -3.32, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.0667, "angle": -1.23, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 7.37, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -6.68, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": -3.32}]}, "bone50": {"rotate": [{"angle": -5.46, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.0667, "angle": -3.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 7.37, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -6.68, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5, "angle": -5.46}]}, "bone51": {"rotate": [{"angle": -6.68, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0667, "angle": -5.45, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2667, "angle": 7.37, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -6.68}]}, "bone13": {"rotate": [{"angle": -0.2, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 6.15, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -1.08, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": -0.2}]}, "bone14": {"rotate": [{"angle": -0.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 6.15, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -1.08, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": -0.54}]}, "bone107": {"translate": [{}, {"time": 0.2333, "x": 44.6}, {"time": 0.5}]}, "bone108": {"translate": [{"x": 33.45}, {"time": 0.2}, {"time": 0.4333, "x": 44.6}, {"time": 0.5, "x": 33.45}]}, "bone106": {"translate": [{"x": 22.3}, {"time": 0.1333}, {"time": 0.3667, "x": 44.6}, {"time": 0.5, "x": 22.3}]}, "bone104": {"translate": [{}, {"time": 0.2333, "x": 44.6}, {"time": 0.5}]}, "bone105": {"translate": [{"x": 11.15}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 44.6}, {"time": 0.5, "x": 11.15}]}, "bone102": {"translate": [{"x": 33.45}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 44.6}, {"time": 0.5, "x": 33.45}]}, "bone101": {"translate": [{"x": 33.45}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 44.6}, {"time": 0.5, "x": 33.45}]}, "bone100": {"translate": [{"x": 33.45}, {"time": 0.2}, {"time": 0.4333, "x": 44.6}, {"time": 0.5, "x": 33.45}]}, "bone99": {"translate": [{}, {"time": 0.2333, "x": 44.6}, {"time": 0.5}]}, "bone98": {"translate": [{}, {"time": 0.2333, "x": 44.6}, {"time": 0.5}]}, "bone97": {"translate": [{"x": 33.45}, {"time": 0.0667, "x": 22.3}, {"time": 0.2}, {"time": 0.4333, "x": 44.6}, {"time": 0.5, "x": 33.45}]}, "bone96": {"translate": [{"x": 26.51, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1, "x": 44.6}, {"time": 0.1667, "x": 33.45}, {"time": 0.2333, "x": 22.3}, {"time": 0.3667, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.5, "x": 26.51}]}, "bone95": {"translate": [{"x": 22.3}, {"time": 0.1333}, {"time": 0.3667, "x": 44.6}, {"time": 0.5, "x": 22.3}]}, "bone94": {"translate": [{}, {"time": 0.2333, "x": 44.6}, {"time": 0.5}]}, "bone93": {"translate": [{"x": 11.15}, {"time": 0.0667}, {"time": 0.3, "x": 44.6}, {"time": 0.5, "x": 11.15}]}, "bone92": {"translate": [{"x": 11.15}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 44.6}, {"time": 0.5, "x": 11.15}]}, "bone91": {"translate": [{}, {"time": 0.2333, "x": 44.6}, {"time": 0.5}]}, "bone90": {"translate": [{"curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 0.2667, "x": 101.13, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.5}]}, "bone103": {"translate": [{"curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 0.2667, "x": 101.13, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.5}]}, "light": {"scale": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "x": 0.858, "y": 0.858, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5}]}}}}}