import { instantiate } from "cc";
import { UITransform } from "cc";
import { _decorator, Component, Node } from "cc";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "../../../../../lib/utils/FmUtils";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("TipReward")
export class TipReward extends Component {
  // 奖励道具列表
  public rewardItemList: number[];

  @property(UITransform)
  private uiTransformBg: UITransform;

  @property(Node)
  private nodeLayoutGridItem: Node;

  @property(Node)
  private nodeLayoutRemainder: Node;

  start() {
    // 道具数量
    let itemCount = this.rewardItemList.length / 2;

    // 要展现的行数
    let line = Math.ceil(itemCount / 5);

    // 预计高度
    let height = line * 130 + 140;

    // 设置背景大小
    this.uiTransformBg.setContentSize(750, height);

    // 设置关闭位置
    this.node.getChildByName("lbl_close").setPosition(0, 60 - 130 * line);
    log.log(60 - 130 * line);

    // 加入道具
    this.nodeLayoutGridItem.children.forEach((child) => {
      child.active = false;
    });

    for (let iLine = 1; iLine < line; iLine++) {
      for (let i = 0; i < 5; i++) {
        let itemId = this.rewardItemList[(iLine - 1) * 5 + i];
        let itemNum = this.rewardItemList[(iLine - 1) * 5 + i + 1];

        let nodeItem = this.nodeLayoutGridItem.children[i];
        if (!nodeItem || !nodeItem.name.startsWith("node_item")) {
          nodeItem = instantiate(this.nodeLayoutGridItem.children[0]);
          this.nodeLayoutGridItem.addChild(nodeItem);
        }
        nodeItem.setSiblingIndex(i);
        FmUtils.setItemNode(nodeItem.getChildByName("Item"), itemId, itemNum);
        nodeItem.active = true;
      }
    }

    // 最后一行
    this.nodeLayoutRemainder.children.forEach((child) => {
      child.active = false;
    });

    if (0 < itemCount % 5) {
      this.nodeLayoutRemainder.parent.active = true;
    }

    for (let i = 0; i < itemCount % 5; i++) {
      let itemId = this.rewardItemList[(line - 1) * 5 * 2 + i];
      let itemNum = this.rewardItemList[(line - 1) * 5 * 2 + i + 1];

      let nodeItem = this.nodeLayoutRemainder.children[i];
      if (!nodeItem || !nodeItem.name.startsWith("node_item")) {
        nodeItem = instantiate(this.nodeLayoutRemainder.children[0]);
        this.nodeLayoutRemainder.addChild(nodeItem);
      }
      nodeItem.setSiblingIndex(i);
      FmUtils.setItemNode(nodeItem.getChildByName("Item"), itemId, itemNum);
      nodeItem.active = true;
    }
  }

  private onClose() {
    this.node.destroy();
  }
}
