import { _decorator, Component, find, Label, Node, RichText, v3 } from "cc";
import { ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { CompeteBattlerMessage } from "../../../net/protocol/Compete";
import { PlayerModule } from "db://assets/GameScrpit/module/player/PlayerModule";
import Formate from "db://assets/GameScrpit/lib/utils/Formate";
import FmUtils from "db://assets/GameScrpit/lib/utils/FmUtils";
const { ccclass, property } = _decorator;

@ccclass("AzstViewHolder")
export class AzstViewHolder extends ViewHolder {
  private _rank_comp: Label;
  private _messageName_comp: Label;
  private _messagePoint_comp: Label;
  private _messageLevel_comp: RichText;

  private _titleTarget: Node;

  private _btn_header: Node;

  public init() {
    this._btn_header = find("btn_header", this.node);

    this._rank_comp = find("rank", this.node).getComponent(Label);
    this._messageName_comp = find("messageName", this.node).getComponent(Label);
    this._messagePoint_comp = find("messagePoint", this.node).getComponent(Label);
    this._messageLevel_comp = find("messageLevel", this.node).getComponent(RichText);
    this._titleTarget = find("titleTarget", this.node);
  }
  updateData(data: CompeteBattlerMessage, position: number) {
    FmUtils.setHeaderNode(this._btn_header, data.simpleMessage, data.isRobot);

    this.node["userId"] = data.simpleMessage.userId;

    this._messageName_comp.string = data.simpleMessage.nickname;
    this._rank_comp.string = String(position + 4);

    let leaderdb = PlayerModule.data.getConfigLeaderData(data.simpleMessage.level);
    this._messageLevel_comp.string = leaderdb.jingjie2;

    this._titleTarget.destroyAllChildren();
    if (data.simpleMessage.avatarList[3] != -1) {
      PlayerModule.service.createTitle(this._titleTarget, data.simpleMessage.avatarList[3], (titleNode: Node) => {
        this._messageLevel_comp.enabled = false;
        titleNode.setScale(v3(0.65, 0.65, 1));
        titleNode.setPosition(v3(0, -10, 0));
      });
    } else {
      this._messageLevel_comp.enabled = true;
    }

    this._messagePoint_comp.string = Formate.format(data.point);
  }
}
