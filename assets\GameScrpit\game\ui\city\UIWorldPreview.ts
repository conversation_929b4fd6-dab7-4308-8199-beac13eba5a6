import { _decorator, Node, ScrollView, Sprite, Vec2 } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { CityRouteName } from "../../../module/city/CityConstant";
import { JsonMgr } from "../../mgr/JsonMgr";
import { CityModule } from "../../../module/city/CityModule";
import { TipsMgr } from "../../../../platform/src/TipsHelper";
import { LangMgr } from "../../mgr/LangMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { BadgeMgr, BadgeType } from "../../mgr/BadgeMgr";
const { ccclass, property } = _decorator;

/**
 * hopewsw
 * Fri Jan 24 2025 20:28:03 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/city/UIWorldPreview.ts
 * https://docs.cocos.com/creator/3.8/manual/zh/
 *
 */

const enum ItemStatus {
  Finish = 0,
  Current = 1,
  NotStart = 2,
}

@ccclass("UIWorldPreview")
export class UIWorldPreview extends UINode {
  // _openAct: boolean = true;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_MAJORCITY}?prefab/ui/UIWorldPreview`;
  }

  //=================================================
  public init(args: any): void {
    super.init(args);
  }

  protected onEvtShow(): void {
    let currentBuildShowId = 1;

    for (let i = 1; i <= 5; i++) {
      const cfgBuildShow = JsonMgr.instance.jsonList.c_buildShow[i];
      let nodeItem = this.getNode("item0" + i);
      nodeItem["_level"] = i;
      BadgeMgr.instance.setBadgeId(nodeItem, BadgeType.UIMajorCity.btn_wu_zu_yu_lan[nodeItem.name].id);

      if (cfgBuildShow?.buildId && !CityModule.data.cityMessageMap.get(cfgBuildShow.buildId)?.level) {
        this.setStatus(nodeItem, ItemStatus.NotStart);
        nodeItem.on(
          Node.EventType.TOUCH_END,
          () => {
            AudioMgr.instance.playEffect(1901);
            let cityName = JsonMgr.instance.jsonList.c_build[cfgBuildShow.buildId].name;
            let str = LangMgr.txMsgCode(102, [cityName]);
            TipsMgr.showTip(str);
          },
          this
        );
      } else {
        const cfgBuildShowNext = JsonMgr.instance.jsonList.c_buildShow[i + 1];
        if (cfgBuildShowNext?.buildId && CityModule.data.cityMessageMap.get(cfgBuildShowNext.buildId)?.level) {
          this.setStatus(nodeItem, ItemStatus.Finish);
        } else {
          this.setStatus(nodeItem, ItemStatus.Current);
          currentBuildShowId = i;
        }
        nodeItem.on(
          Node.EventType.TOUCH_END,
          () => {
            AudioMgr.instance.playEffect(1901);
            UIMgr.instance.showDialog(CityRouteName.UIWorldPreviewDetail, { buildShowId: i });
          },
          this
        );
      }
    }

    //todo 计算滚动位置
    let y = (5 - currentBuildShowId) * 530;
    y = Math.min(2650, y);
    y = Math.max(0, y);

    this.getNode("scroll_view").getComponent(ScrollView).scrollToOffset(new Vec2(0, y), 1);
  }

  on_click_btn_back() {
    AudioMgr.instance.playEffect(520);

    UIMgr.instance.back();
  }

  setStatus(item: Node, status: ItemStatus) {
    item.getChildByName("bg").getComponent(Sprite).grayscale = status === ItemStatus.NotStart;
    item.getChildByName("bg_name").getComponent(Sprite).grayscale = status === ItemStatus.NotStart;
    item.getChildByName("node_current").active = status === ItemStatus.Current;
  }
}
