import { _decorator, Label, Sprite, Color, isValid, v3 } from "cc";
import ResMgr from "../../../lib/common/ResMgr";
import MsgMgr from "../../../lib/event/MsgMgr";
import TickerMgr from "../../../lib/ticker/TickerMgr";
import TipMgr from "../../../lib/tips/TipMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { UINode } from "../../../lib/ui/UINode";
import { GoodsModule } from "../../../module/goods/GoodsModule";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import ToolExt from "../../common/ToolExt";
import MsgEnum from "../../event/MsgEnum";
import { JsonMgr } from "../../mgr/JsonMgr";
import { BuyConfirm } from "../UIBuyConfirm";
import { PublicRouteName } from "../../../module/player/PlayerConstant";
import { dtTime } from "../../BoutStartUp";
import { LuckDrawModule } from "../../../module/luck_draw/LuckDrawModule";
import { Sleep } from "../../GameDefine";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { LuckDrawAudioName } from "../../../module/luck_draw/LuckDrawConfig";
import { FmButton } from "../../../../platform/src/core/ui/components/FmButton";
import { FriendRaceColorBG, FriendRaceColorFG } from "../../../module/friend/FriendConstant";

const { ccclass, property } = _decorator;

enum Lottery_Shop_Main {
  Hero_Main = "hero_Main",
  XianYou_Main = "XianYou_Main",
}

@ccclass("UILotteryShop")
export class UILotteryShop extends UINode {
  protected _openAct: boolean = true; //打开动作

  protected dependOn(): BundleEnum[] {
    return [
      BundleEnum.BUNDLE_COMMON_UI,
      BundleEnum.BUNDLE_COMMON_FRIEND,
      BundleEnum.BUNDLE_COMMON_HERO_HALF,
      BundleEnum.BUNDLE_COMMON_HERO_ICON,
    ];
  }

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_LOTTERY}?prefab/ui/UILotteryShop`;
  }

  private _heroListDB = [];

  private _friendListDB = [];

  private _curIndexMain: Lottery_Shop_Main = Lottery_Shop_Main.Hero_Main;

  protected async onEvtShow() {
    if (LuckDrawModule.data.message.train.guideId == 0) {
      LuckDrawModule.api.updateGuideId();
    }
    let c_shop = JsonMgr.instance.jsonList.c_shop;
    let list = Object.keys(c_shop);
    for (let i = 0; i < list.length; i++) {
      let info = c_shop[list[i]];
      if (info.type == 1) {
        this._heroListDB.push(info);
      } else if (info.type == 2) {
        this._friendListDB.push(info);
      }
    }

    await Sleep(0.1);
    this.loadHero();
    await Sleep(0.1);
    this.loadXianYou();
    this.setMyItem();
    this.changeMain();
  }

  private async loadHero() {
    let c_hero = JsonMgr.instance.jsonList.c_hero;
    for (let i = 0; i < this._heroListDB.length; i++) {
      await Sleep(0.1);
      if (isValid(this.node) == false) return;
      let info = this._heroListDB[i];
      let node = ToolExt.clone(this["heroItem"], this);
      node.setPosition(v3(0, 0, 0));
      this.getNode("heroContent").addChild(node);
      node.active = true;
      let itemsList = info.itemsList;
      let heroId = itemsList[0][0];
      let heroInfo = c_hero[heroId];
      node["lblName"].getComponent(Label).string = heroInfo.name;
      ToolExt.setHeroImageHalf(node["sp_hero"], heroInfo.id, this);
      ToolExt.setHeroBg(node["hero_bg"], heroInfo.color, this);
      ToolExt.setHeroBgTop(node["color_bord"], heroInfo.color, this);
      ToolExt.setRaceType(node["bgType"], heroInfo.type, this);
      this.setLvColor(heroInfo.color, node["lblevel"].getComponent(Label));

      node["num"].getComponent(Label).string = info.coinPrice;
      node["btn_heroBuy"]["shopId"] = info.id;
      node["lblevel"].getComponent(Label).string = 1;
      node["heroId"] = heroId;
      node["changeState"] = () => {
        if (GoodsModule.data.hasBuyCount(info.id) < info.max && PlayerModule.service.isShopBuy(info.itemsList[0][0])) {
          node["has"].active = false;
          node["btn_heroBuy"].active = true;
        } else {
          node["has"].active = true;
          node["btn_heroBuy"].active = false;
        }
      };

      if (i % 3 == 0) {
        node["bg_sdtc_neidi_xing"].active = false;
      }
      node["changeState"]();
    }
  }

  private async loadXianYou() {
    let c_friend = JsonMgr.instance.jsonList.c_friend;
    for (let i = 0; i < this._friendListDB.length; i++) {
      await Sleep(0.1);
      if (isValid(this.node) == false) return;
      let info = this._friendListDB[i];
      let node = ToolExt.clone(this["friendItem"], this);
      node.setPosition(v3(0, 0, 0));
      this.getNode("friendContent").addChild(node);
      node.active = true;

      let itemsList = info.itemsList;

      let friendId = itemsList[0][0];
      let friendInfo = c_friend[friendId];

      node["friend_name"].getComponent(Label).string = friendInfo.name;

      ResMgr.setSpriteFrame(
        BundleEnum.BUNDLE_COMMON_FRIEND,
        `half/friend_${friendInfo.id}`,
        node["friend_img"].getComponent(Sprite)
      );

      ResMgr.setSpriteFrame(
        BundleEnum.BUNDLE_COMMON_FRIENDICON,
        `atlas_colors/${FriendRaceColorBG[friendInfo.color]}`,
        node["color_background"].getComponent(Sprite)
      );
      ResMgr.setSpriteFrame(
        BundleEnum.BUNDLE_COMMON_FRIENDICON,
        `atlas_colors/${FriendRaceColorFG[friendInfo.color]}`,
        node["color_frontground"].getComponent(Sprite)
      );

      node["friendship"].getComponent(Label).string = friendInfo.friendlyFirst;
      node["talent"].getComponent(Label).string = friendInfo.talentFirst;

      node["num"].getComponent(Label).string = info.coinPrice;
      node["btn_friendBuy"]["shopId"] = info.id;
      node["changeState"] = () => {
        if (GoodsModule.data.hasBuyCount(info.id) < info.max && PlayerModule.service.isShopBuy(itemsList[0][0])) {
          node["has"].active = false;
          node["btn_friendBuy"].active = true;
        } else {
          node["has"].active = true;
          node["btn_friendBuy"].active = false;
        }
      };

      node["changeState"]();
    }
  }

  private setMyItem() {
    this["myItenm"].getComponent(Label).string = String(PlayerModule.data.getItemNum(1036));
  }

  private changeMain() {
    if (this._curIndexMain == Lottery_Shop_Main.Hero_Main) {
      this["heroScroll"].active = true;
      this["friendScroll"].active = false;

      this.getNode("btn_hero").getComponent(FmButton).selected = true;
      this.getNode("btn_friend").getComponent(FmButton).selected = false;
      let info = this._heroListDB[0];
      info.cointype;

      ToolExt.setItemIcon(this.getNode("item_title"), info.cointype);
    } else if (this._curIndexMain == Lottery_Shop_Main.XianYou_Main) {
      this["heroScroll"].active = false;
      this["friendScroll"].active = true;
      this.getNode("btn_hero").getComponent(FmButton).selected = false;
      this.getNode("btn_friend").getComponent(FmButton).selected = true;
      let info = this._friendListDB[0];
      info.cointype;

      ToolExt.setItemIcon(this.getNode("item_title"), info.cointype);
    }
  }

  private on_click_btn_close() {
    // 关闭窗口 effect_guanbiyinxiao
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  private on_click_btn_heroBuy(event) {
    AudioMgr.instance.playEffect(LuckDrawAudioName.Effect.兑换商店点击兑换);
    let shopId = event.node.shopId;
    let c_info_shop = JsonMgr.instance.jsonList.c_shop[shopId];
    if (c_info_shop.maxtype == 4 && PlayerModule.service.isShopBuy(c_info_shop.itemsList[0][0]) == false) {
      TipMgr.showTip("已拥有该物品，无法重复获得");
      return;
    }
    let c_shop = JsonMgr.instance.jsonList.c_shop;
    let info = c_shop[shopId];
    let need = info.coinPrice;
    if (need > PlayerModule.data.getItemNum(info.cointype)) {
      TipMgr.showTip("道具不足");
      return;
    }

    let numLimit = 1;
    const buyConfirm: BuyConfirm = {
      itemInfo: info.itemsList[0],
      moneyInfo: [info.cointype, info.coinPrice],
      maxNum: numLimit,
    };

    UIMgr.instance.showDialog(PublicRouteName.UIBuyConfirm, buyConfirm, (resp) => {
      if (resp.ok) {
        GoodsModule.api.redeemGoods(info.id, resp.num, (resp) => {
          MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: resp });
          if (isValid(this.node) == false) {
            return;
          }
          event.node.parent["changeState"]();
        });
      }
    });
  }

  private on_click_btn_friendBuy(event) {
    AudioMgr.instance.playEffect(LuckDrawAudioName.Effect.兑换商店点击兑换);
    let shopId = event.node.shopId;
    let c_info_shop = JsonMgr.instance.jsonList.c_shop[shopId];
    if (c_info_shop.maxtype == 4 && PlayerModule.service.isShopBuy(c_info_shop.itemsList[0][0]) == false) {
      TipMgr.showTip("已拥有该物品，无法重复获得");
      return;
    }

    let c_shop = JsonMgr.instance.jsonList.c_shop;
    let info = c_shop[shopId];
    let need = info.coinPrice;
    if (need > PlayerModule.data.getItemNum(info.cointype)) {
      TipMgr.showTip("道具不足");
      return;
    }

    let numLimit = 1;
    const buyConfirm: BuyConfirm = {
      itemInfo: info.itemsList[0],
      moneyInfo: [info.cointype, info.coinPrice],
      maxNum: numLimit,
    };

    UIMgr.instance.showDialog(PublicRouteName.UIBuyConfirm, buyConfirm, (resp) => {
      if (resp.ok) {
        GoodsModule.api.redeemGoods(info.id, resp.num, (resp) => {
          MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: resp });

          if (isValid(this.node) == false) {
            return;
          }
          event.node.parent["changeState"]();
        });
      }
    });
  }

  private on_click_btn_hero() {
    AudioMgr.instance.playEffect(LuckDrawAudioName.Effect.兑换商店页签);
    if (this._curIndexMain == Lottery_Shop_Main.Hero_Main) {
      return;
    }

    this._curIndexMain = Lottery_Shop_Main.Hero_Main;
    this.changeMain();
  }

  private on_click_btn_friend() {
    AudioMgr.instance.playEffect(LuckDrawAudioName.Effect.兑换商店页签);
    if (this._curIndexMain == Lottery_Shop_Main.XianYou_Main) {
      return;
    }
    this._curIndexMain = Lottery_Shop_Main.XianYou_Main;
    this.changeMain();
  }

  protected onEvtClose(): void {}

  private setLvColor(color, lab: Label) {
    let key = "";
    switch (color) {
      case 1:
        key = "#145526";
        break;
      case 2:
        key = "#133d77";
        break;
      case 3:
        key = "#6f135f";
        break;
      case 4:
        key = "#774c08";
        break;
      case 5:
        key = "#842815";
        break;
      default:
        break;
    }
    lab.outlineColor = new Color(key);
  }
}
