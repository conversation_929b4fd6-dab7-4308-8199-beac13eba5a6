import { _decorator } from "cc";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UINode } from "../../../lib/ui/UINode";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { CityModule } from "../../../module/city/CityModule";
import GuideMgr from "../../../ext_guide/GuideMgr";

const { ccclass, property } = _decorator;

@ccclass("UIHuntPrimitvieLose")
export class UIHuntPrimitvieLose extends UINode {
  protected _isAddToTop: boolean = true;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_HUNT}?prefab/ui/UIHuntPrimitvieLose`;
  }

  protected dependOn(): Array<BundleEnum> {
    return [BundleEnum.BUNDLE_COMMON_UI, BundleEnum.BUNDLE_COMMON_ITEM];
  }

  public init(args: any): void {
    super.init(args);
  }

  protected onEvtShow(): void {
    TipsMgr.setEnableTouch(true);
    AudioMgr.instance.playEffect(AudioName.Effect.战斗失败);
  }

  private on_click_btn_close() {
    UIMgr.instance.back();
  }

  protected onEvtClose(): void {}
}
