import { _decorator, Node } from "cc";
import PlantMgr from "./PlantMgr";

export class PlantUI {
  // 从对象池获取的node
  private _resMap: Map<string, Node[]> = new Map<string, Node[]>();

  public static create(): PlantUI {
    return new PlantUI();
  }

  public async getNode(path: string, layer: number = 8) {
    let node = await PlantMgr.instance.get(path, layer);
    let List = this._resMap.get(path);
    if (List == null) {
      List = [];
      this._resMap.set(path, List);
    }
    List.push(node);

    return node;
  }

  onRemove() {
    this._resMap.forEach((val, key) => {
      for (let i = 0; i < val.length; i++) {
        PlantMgr.instance.ret(key, val[i]);
      }
    });
  }
}
