import { _decorator, EmptyDevice, EventTouch, find, Input, instantiate, Label, Node, Sprite } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { PetMessage } from "../../net/protocol/Pet";
import ResMgr from "../../../lib/common/ResMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { divide } from "../../../lib/utils/NumbersUtils";
import { PetModule } from "../../../module/pet/PetModule";
import { HeroColorCard, HeroColorBord } from "../../../module/hero/HeroConstant";
import { JsonMgr } from "../../mgr/JsonMgr";
import { IConfigPetSkill } from "../../JsonDefine";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;
ccclass("UIHeroPetAwakeSuccess");
export class UIHeroPetAwakeSuccess extends UINode {
  private _pet: PetMessage;
  private _skill: IConfigPetSkill;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_PET}?prefab/ui/UIHeroPetAwakeSuccess`;
  }

  public init(args: any): void {
    super.init(args);
    this._pet = args;
    let index = this._pet.petSkillList.length - 1;
    let petSkill = PetModule.data.getHeroPetSkillList();
    this._skill = petSkill[index % petSkill.length];
  }
  protected onEvtShow(): void {
    this.refreshUI();
    this.node.on(Input.EventType.TOUCH_END, this.click_block, this);
  }
  private click_block(e: EventTouch) {
    UIMgr.instance.back();
  }
  private refreshUI() {
    let spirit = PetModule.config.getHeroPet(this._pet.petId);
    let petSkinList = [];
    petSkinList.push(spirit.firstSkin);
    petSkinList = petSkinList.concat(spirit.skin);
    let skinId = spirit.firstSkin;
    if (petSkinList.includes(this._pet.chosenSkinId)) {
      skinId = this._pet.chosenSkinId;
    }
    let petSkin = JsonMgr.instance.jsonList.c_petSkin[skinId];
    if (!petSkin) {
      log.log(`没有找到皮肤配置表信息：${skinId}`);
    }
    // let petSkin = JsonMgr.instance.jsonList.c_petSkin[this._pet.chosenSkinId];
    // if (!petSkin) {
    //   petSkin = JsonMgr.instance.jsonList.c_petSkin[spirit.firstSkin];
    // }
    this.getNode("lbl_name").getComponent(Label).string = `${petSkin.name}`;
    this.getNode("lbl_level").getComponent(Label).string = `${this._pet.level}`;
    this.getNode("lbl_wake_count").getComponent(Label).string = `${this._pet.awakeCount}`;
    this.getNode("lbl_wake_skill_add").getComponent(Label).string = `${divide(this._skill.firstNum, 100)}%`;

    ResMgr.setSpriteFrame(
      BundleEnum.BUNDLE_G_PET,
      `atlas_pet_skill/petskill_${this._skill.iconId}`,
      this.getNode("bg_icon").getComponent(Sprite)
    );
    ResMgr.setSpriteFrame(
      BundleEnum.BUNDLE_COMMON_HERO_ICON,
      `images/${HeroColorCard[`color_${petSkin.color}`]}`,
      this.getNode("improved").getChildByName("bg_pet_color").getComponent(Sprite)
    );

    ResMgr.setSpriteFrame(
      BundleEnum.BUNDLE_COMMON_HERO_ICON,
      `images/${HeroColorBord[`color_${petSkin.color}`]}`,
      this.getNode("improved").getChildByName("bg_border").getComponent(Sprite)
    );

    ResMgr.setSpriteFrame(
      BundleEnum.BUNDLE_COMMON_PET,
      `pet_card/${petSkin.cardId}`,
      this.getNode("bg_pet_img").getComponent(Sprite)
    );
  }
}
