import { <PERSON><PERSON><PERSON>and<PERSON> } from "../../game/mgr/ApiHandler";
import { ShengDianCmd } from "../../game/net/cmd/CmdData";
import { IntValue } from "../../game/net/protocol/ExternalMessage";
import { ShengDianModule } from "./ShengDianModule";

export class ShengDianSubscriber {
  private onYunShiChange(data: IntValue) {
    // 处理数据更新逻辑
    ShengDianModule.data.templeMessage.totalTreeVal = data.value;
  }

  public register() {
    //订阅服务器消息
    ApiHandler.instance.subscribe(IntValue, ShengDianCmd.yunShiNotice, this.onYunShiChange);
  }
  public unRegister() {
    //取消订阅服务器消息
    ApiHandler.instance.unSubscribe(ShengDianCmd.yunShiNotice, this.onYunShiChange);
  }
}
