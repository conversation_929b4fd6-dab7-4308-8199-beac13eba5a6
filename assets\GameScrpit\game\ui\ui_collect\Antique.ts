import { _decorator, Component, Node, Sprite } from "cc";
import { HeroModule } from "../../../module/hero/HeroModule";
import { CCInteger } from "cc";
const { ccclass, property } = _decorator;

@ccclass("Antique")
export class Antique extends Component {
  @property(CCInteger)
  heroId: number = 0;

  @property(CCInteger)
  shopType: number = 0;

  private _time = 0.3;
  protected onLoad(): void {
    this.setGray();
  }

  private setGray() {
    let info = HeroModule.data.getHeroMessage(this.heroId);
    if (info) {
      this.node.getComponent(Sprite).grayscale = false;
    } else {
      this.node.getComponent(Sprite).grayscale = true;
    }
  }

  protected update(dt: number): void {
    this._time -= dt;

    if (this._time > 0) return;
    this._time = 0.3;
    this.setGray();
  }
}
