// c_shop(商店)
export interface IConfigShop {
  id: number;
  /**道具id */
  itemsList: number[][];
  /**商店类型 */
  type: number;
  /**解锁条件 */
  unlockType: number;
  /**解锁数值 */
  unlockNum: number;
  /**国内充值ID */
  rmbId: number;
  /**货币id */
  cointype: number;
  /**货币价格 */
  coinPrice: number;
  /**每次购买价格增加 */
  priceAdd: number;
  /**限购类型 */
  maxtype: number;
  /**限购次数 */
  max: number;
  /**排序 */
  sort: number;
}
