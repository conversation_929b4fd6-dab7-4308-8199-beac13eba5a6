import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { ApiHandlerSuccess, ApiHandler } from "../../game/mgr/ApiHandler";
import { ActivityCmd } from "../../game/net/cmd/CmdData";
import { RedeemChosenRequest } from "../../game/net/protocol/Activity";
import { CommIntegerListMessage } from "../../game/net/protocol/Comm";
import { DisciplinesModule } from "../disciplines/DisciplinesModule";
import { ActivityID } from "./ActivityConstant";
import { AfterModule } from "../after/AfterModule";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class ActivityApi {
  // 接口方法，数据模块在这里改好，然后通知上层，下面这个是个例子
  // public improveSkill(friendId: number, rank: number, consumeitem: boolean, success?: ApiHandlerSuccess) {
  //   let data: FriendCitySkillRequest = {
  //     friendId: friendId,
  //     rank: rank,
  //     consumeItem: consumeitem,
  //   };
  //   log.log("apiImproveSkill", friendId, rank, consumeitem);
  //   ApiHandler.instance.request(
  //     FriendCitySkillMessage,
  //     FriendSubCmd.improveSkill,
  //     FriendCitySkillRequest.encode(data),
  //     (data: FriendCitySkillMessage) => {
  //       log.log(data);
  //       FriendModule.data.updateFriendCitySkill(friendId, rank, data);
  //       success && success(data);
  //     },
  //     (errorCode: any, msg: string, data: any) => {
  //       log.log(`${errorCode}`);
  //       log.log(data);
  //     }
  //   );
  // }
  public recordRedeemChosen(param: RedeemChosenRequest, success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(
      CommIntegerListMessage,
      ActivityCmd.recordRedeemChosen,
      RedeemChosenRequest.encode(param),
      (data: CommIntegerListMessage) => {
        log.log("自选礼包道具成功=======", data);
        if (param.activityId == ActivityID.XIUXING) {
          DisciplinesModule.data.setChosen(param.redeemId, data);
        } else if (param.activityId == ActivityID.AFTERID) {
          AfterModule.data.topUpMessage.chosenMap[param.redeemId] = { intList: data.intList };
        }
        success && success(data);
      },
      (errorCode: any, msg: string[], data: any) => {
        TipsMgr.showErrX(errorCode, msg);
        return true;
      }
    );
  }
}
