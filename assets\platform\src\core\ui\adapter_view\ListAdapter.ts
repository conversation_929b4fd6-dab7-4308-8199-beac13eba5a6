import { Node } from "cc";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";

export class ViewHolder extends BaseCtrl {
  private _viewType: number = 0;
  private _position: number = 0;
  public setSelect(select: boolean, expand: Node): boolean {
    return false;
  }
  public set position(value: number) {
    this._position = value;
  }
  public get position(): number {
    return this._position;
  }
  public get viewType(): number {
    return this._viewType;
  }
  public set viewType(value: number) {
    this._viewType = value;
  }
}
export abstract class ListAdapter {
  private _observer: (data?: any) => void;
  public notifyDataSetChanged(data?: any): void {
    this._observer && this._observer(data);
  }
  public clearData() {
    this._observer && this._observer("clear");
  }
  public setObserver(observer: (data: any) => void) {
    this._observer = observer;
  }
  getViewType(position: number): number {
    return 0;
  }
  getItem(position: number): any {
    return null;
  }
  abstract onCreateView(viewType: number): Node;
  abstract onBindData(node: Node, position: number): void;
  abstract getCount(): number;
}
