import { _decorator, Component, Node, Sprite } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import ResMgr from "../../../lib/common/ResMgr";
import { CityRouteName } from "../../../module/city/CityConstant";
import { CityModule } from "../../../module/city/CityModule";
import MsgMgr from "../../../lib/event/MsgMgr";
import { CityEvent } from "../../../module/city/CityEvent";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { BadgeMgr, BadgeType } from "../../mgr/BadgeMgr";
const { ccclass, property } = _decorator;

/**
 * hopewsw
 * Fri Jan 24 2025 21:01:51 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/city/UIWorldPreviewDetail.ts
 * https://docs.cocos.com/creator/3.8/manual/zh/
 *
 */

@ccclass("UIWorldPreviewDetail")
export class UIWorldPreviewDetail extends UINode {
  protected _openAct: boolean = true;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_MAJORCITY}?prefab/ui/UIWorldPreviewDetail`;
  }

  buildShowId: number = 0;

  //=================================================

  protected onRegEvent(): void {
    MsgMgr.on(CityEvent.ON_CITY_TRIM_AWARD, this.setAwardState, this);
  }

  protected onDelEvent(): void {
    MsgMgr.off(CityEvent.ON_CITY_TRIM_AWARD, this.setAwardState, this);
  }

  public init(args: any): void {
    super.init(args);
    this.buildShowId = args.buildShowId;
  }
  protected onEvtShow(): void {
    for (let i = 1; i <= 5; i++) {
      this.getNode("lbl_name_" + i).active = i == this.buildShowId;
    }

    BadgeMgr.instance.setBadgeId(
      this.getNode("btn_look_award").getChildByName("open"),
      BadgeType.UIMajorCity.btn_wu_zu_yu_lan["item0" + this.buildShowId].open.id
    );

    ResMgr.loadImage(
      `${BundleEnum.BUNDLE_G_MAJORCITY}?wu_zu_yu_lan/bg/bg_wuzuyulantu_${this.buildShowId}`,
      this.getNode("bg_world").getComponent(Sprite),
      this
    );

    this.setAwardState();
  }

  private setAwardState() {
    let bool = CityModule.data.cityAggregateMessage.raceRewardIdList.includes(this.buildShowId);
    if (bool == true) {
      this.getNode("btn_look_award").getChildByName("no_open").active = true;
      this.getNode("btn_look_award").getChildByName("open").active = false;
    } else {
      this.getNode("btn_look_award").getChildByName("no_open").active = false;
      this.getNode("btn_look_award").getChildByName("open").active = true;
    }
  }

  private on_click_btn_look_city() {
    AudioMgr.instance.playEffect(1905);
    UIMgr.instance.showDialog(CityRouteName.UIWorldPreviewCity, { buildShowId: this.buildShowId });
  }

  private on_click_btn_look_trim() {
    AudioMgr.instance.playEffect(1904);
    UIMgr.instance.showDialog(CityRouteName.UIWorldPreviewTrim, { buildShowId: this.buildShowId });
  }

  private on_click_btn_look_award() {
    AudioMgr.instance.playEffect(1902);
    UIMgr.instance.showDialog(CityRouteName.UIWorldPreviewAward, { buildShowId: this.buildShowId });
  }

  on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
}
