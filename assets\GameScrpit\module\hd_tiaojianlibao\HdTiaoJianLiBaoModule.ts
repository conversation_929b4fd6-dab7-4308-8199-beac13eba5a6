import { GameData } from "../../game/GameData";
import data from "../../lib/data/data";
import { HdTiaoJianLiBaoApi } from "./HdTiaoJianLiBaoApi";
import { HdTiaoJianLiBaoConfig } from "./HdTiaoJianLiBaoConfig";
import { HdTiaoJianLiBaoData } from "./HdTiaoJianLiBaoData";
import { HdTiaoJianLiBaoRoute } from "./HdTiaoJianLiBaoRoute";
import { HdTiaoJianLiBaoService } from "./HdTiaoJianLiBaoService";
import { HdTiaoJianLiBaoSubscriber } from "./HdTiaoJianLiBaoSubscriber";
import { HdTiaoJianLiBaoViewModel } from "./HdTiaoJianLiBaoViewModel";

export class HdTiaoJianLiBaoModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): HdTiaoJianLiBaoModule {
    if (!GameData.instance.HdTiaoJianLiBaoModule) {
      GameData.instance.HdTiaoJianLiBaoModule = new HdTiaoJianLiBaoModule();
    }
    return GameData.instance.HdTiaoJianLiBaoModule;
  }

  private _data = new HdTiaoJianLiBaoData();
  private _api = new HdTiaoJianLiBaoApi();
  private _config = new HdTiaoJianLiBaoConfig();
  private _route = new HdTiaoJianLiBaoRoute();
  private _service = new HdTiaoJianLiBaoService();
  private _subscriber = new HdTiaoJianLiBaoSubscriber();
  private _viewModel = new HdTiaoJianLiBaoViewModel();
  public static get data() {
    return this.instance._data;
  }
  public static get api() {
    return this.instance._api;
  }
  public static get config() {
    return this.instance._config;
  }
  public static get service() {
    return this.instance._service;
  }
  public static get viewModel() {
    return this.instance._viewModel;
  }

  public init(data?: any) {
    if (this._subscriber) {
      this._subscriber.unRegister();
    }
    this._data = new HdTiaoJianLiBaoData();
    this._api = new HdTiaoJianLiBaoApi();
    this._config = new HdTiaoJianLiBaoConfig();
    this._route = new HdTiaoJianLiBaoRoute();
    this._service = new HdTiaoJianLiBaoService();
    this._subscriber = new HdTiaoJianLiBaoSubscriber();
    this._viewModel = new HdTiaoJianLiBaoViewModel();

    // 初始化模块
    this._data.init();
    this._route.init();
    
    this._subscriber.register();
  }

  protected saveKey(): string {
    return this.constructor.name;
  }
}
