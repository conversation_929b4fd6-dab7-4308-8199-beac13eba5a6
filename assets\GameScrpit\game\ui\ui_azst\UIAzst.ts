import { _decorator, find, Label, Node, RichText, v3 } from "cc";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import ToolExt from "../../common/ToolExt";
import { CompeteOwnRankMessage } from "../../net/protocol/Compete";
import { AzstModule } from "../../../module/azst/AzstModule";
import Formate from "../../../lib/utils/Formate";
import { PlayerModule } from "../../../module/player/PlayerModule";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import { BadgeMgr, BadgeType } from "../../mgr/BadgeMgr";
import { AzstAudioName, AzstMsgEnum } from "../../../module/azst/AzstConfig";
import FmUtils from "../../../lib/utils/FmUtils";
import { ListView } from "../../common/ListView";
import { AzstAdapter } from "./adapter_azst/AzstAdapter";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { Sleep } from "../../GameDefine";
import { dtTime } from "../../BoutStartUp";

const { ccclass, property } = _decorator;
/**演武场挑战券道具id */
export const pkNeedItemId: number = 1082;

@ccclass("UIAzst")
export class UIAzst extends UINode {
  protected _openAct: boolean = true; //打开动作
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_AZST}?prefab/ui/UIAzst`;
  }

  private _value_set = [];

  protected onRegEvent(): void {
    MsgMgr.on(MsgEnum.ON_EXIST_ITEM_CHANGE, this.setItemNum, this);
    MsgMgr.on(AzstMsgEnum.POINT_CHANGE, this.initMain, this);
  }

  protected onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_EXIST_ITEM_CHANGE, this.setItemNum, this);
    MsgMgr.off(AzstMsgEnum.POINT_CHANGE, this.initMain, this);
  }

  protected onEvtShow(): void {
    this.getNode("top_three_bg").children.forEach((val) => {
      val.active = false;
    });
    this["myItemNum"].getComponent(Label).string = "";
    // 红点提示
    BadgeMgr.instance.setBadgeId(this.getNode("btn_showPk"), BadgeType.btn_tiao_zhan.btn_yan_wu_chang.btn_showPk.id);
    BadgeMgr.instance.setBadgeId(this.getNode("btn_showLog"), BadgeType.btn_tiao_zhan.btn_yan_wu_chang.btn_showLog.id);

    this.initMain();
  }

  private setItemNum(id?: number) {
    if (id && id != pkNeedItemId) {
      return;
    }
    let itemNum = PlayerModule.data.getItemNum(pkNeedItemId);
    this["myItemNum"].getComponent(Label).string =
      Formate.format(itemNum) + "/" + AzstModule.data.azstMessage.ticketSize;
  }

  private initMain() {
    AzstModule.api.getRankInfo(async (res: CompeteOwnRankMessage) => {
      this.initMyRank();
      this.setItemNum();

      this.initFrontThree();
      await Sleep(dtTime);
      if (this.node.isValid == false) {
        return;
      }
      this._value_set = [];
      let rankList = AzstModule.data.rankList;
      for (let i = 3; i < rankList.length; i++) {
        this._value_set.push(rankList[i]);
      }

      let adapter = new AzstAdapter(this.getNode("btn_rankMessage"));
      this.getNode("list_view_level").getComponent(ListView).setAdapter(adapter);
      adapter.setData(this._value_set);
    });
  }

  private async initFrontThree() {
    let rankList = AzstModule.data.rankList;
    for (let i = 0; i < 3 && i < rankList.length; i++) {
      await Sleep(dtTime);
      if (this.node.isValid == false) {
        return;
      }

      let node = this.getNode("btn_front" + (i + 1));
      node.active = true;
      let info = rankList[i];
      let simpleMessage = info.simpleMessage;
      node["userId"] = simpleMessage.userId;
      find("lan_front_name", node).getComponent(Label).string = simpleMessage.nickname;
      find("power", node).getComponent(Label).string = Formate.format(info.point);

      let leaderdb = PlayerModule.data.getConfigLeaderData(simpleMessage.level);
      let frontLevel = find("frontLevel", node);
      frontLevel.destroyAllChildren();
      frontLevel.getComponent(RichText).string = leaderdb.jingjie2;

      if (simpleMessage.avatarList[3] != -1) {
        PlayerModule.service.createTitle(frontLevel, simpleMessage.avatarList[3], (node: Node, db) => {
          frontLevel.getComponent(RichText).enabled = false;
          node.setScale(v3(0.7, 0.7, 1));
        });
      } else {
        frontLevel.getComponent(RichText).enabled = true;
      }

      let id = simpleMessage.avatarList[0] == -1 ? 1701 : simpleMessage.avatarList[0];
      node.getChildByName("spinePoint").destroyAllChildren();
      ToolExt.loadUIRole(node.getChildByName("spinePoint"), id, simpleMessage.horseId, "renderScale5", this);
    }
  }

  private initMyRank() {
    if (AzstModule.data.rank > 99 || AzstModule.data.rank <= -1) {
      find("rank", this.getNode("myMessage")).getComponent(Label).string = "未上榜";
    } else {
      find("rank", this.getNode("myMessage")).getComponent(Label).string = String(AzstModule.data.rank);
    }
    find("messageName", this.getNode("myMessage")).getComponent(Label).string =
      PlayerModule.data.getPlayerInfo().nickname;

    let leaderdb = PlayerModule.data.getConfigLeaderData(PlayerModule.data.getPlayerInfo().level);
    find("messageLevel", this.getNode("myMessage")).getComponent(RichText).string = leaderdb.jingjie2;

    find("messagePower", this.getNode("myMessage")).getComponent(Label).string = Formate.format(AzstModule.data.point);

    let btn_header = find("btn_header", this.getNode("myMessage"));

    let simpleMessage = ToolExt.newPlayerBaseMessage();
    simpleMessage.avatarList = PlayerModule.data.getMyAvatarList();
    simpleMessage.nickname = PlayerModule.data.getPlayerInfo().nickname;
    simpleMessage.userId = PlayerModule.data.getPlayerInfo().id;
    simpleMessage.sex = PlayerModule.data.sex;
    simpleMessage.level = PlayerModule.data.getPlayerInfo().level;
    simpleMessage.vipLevel = PlayerModule.data.getPlayerInfo().vipLevel;

    FmUtils.setHeaderNode(btn_header, simpleMessage);
  }

  private on_click_btn_showPk() {
    AudioMgr.instance.playEffect(AzstAudioName.Effect.点击挑战按钮);
    UIMgr.instance.showDialog("UIAzstPk");
  }

  private on_click_btn_showLog() {
    AudioMgr.instance.playEffect(AzstAudioName.Effect.点击日志按钮);
    UIMgr.instance.showDialog("UIAzstLog");
  }

  private on_click_btn_showAward() {
    AudioMgr.instance.playEffect(AzstAudioName.Effect.点击奖励预览);
    UIMgr.instance.showDialog("UIAzstAward");
  }

  private on_click_btn_addItem() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
      itemId: pkNeedItemId,
    });
  }

  private on_click_btn_other1(event) {
    this.ShowOtherMsg(event.node.parent.userId);
  }

  private on_click_btn_other2(event) {
    this.ShowOtherMsg(event.node.parent.userId);
  }

  private on_click_btn_other3(event) {
    this.ShowOtherMsg(event.node.parent.userId);
  }

  private ShowOtherMsg(id) {
    AudioMgr.instance.playEffect(518);
    UIMgr.instance.showDialog(PlayerRouteName.UIPlayerOtherMsg, { userId: id });
  }

  private on_click_btn_close() {
    // 关闭窗口 effect_guanbiyinxiao
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  private on_click_btn_wenhao() {
    AudioMgr.instance.playEffect(522);
    UIMgr.instance.showDialog(PlayerRouteName.UIHelpPop, { titleId: -1, desId: 10 });
  }

  // protected onEvtClose(): void {}
}
