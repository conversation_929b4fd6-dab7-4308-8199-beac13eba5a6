import { _decorator, Label, Sprite, v3 } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { sonhai_activityId, SonhaiModule } from "../../../module/sonhai/SonhaiModule";
import { MainTaskModule } from "../../../module/mainTask/MainTaskModule";
import ToolExt from "../../common/ToolExt";
import { ActivityTakeRequest, ActivityTakeResponse } from "../../net/protocol/Activity";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { SonhaiMsgEnum } from "../../../module/sonhai/SonhaiConfig";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import GuideMgr from "../../../ext_guide/GuideMgr";
import FmUtils from "../../../lib/utils/FmUtils";
import { AdventureTaskVO } from "../../../module/activity/ActivityConfig";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

class TaskInfo {
  /**服务端索引 */
  sevenIndex: number;
  /**任务的索引 */
  taskIndex: number;
  /**任务完成的进度 */
  taskVal: number;
  /**任务的状态 */
  state: TaskState;
}

enum TaskState {
  /**未完成 */
  UNFINISHED = 0,
  /**可领取 */
  UNRECEIVEABLE = 1,
  /**领取 */
  RECEIVE = 2,
}

@ccclass("UISonhaiTask")
export class UISonhaiTask extends UINode {
  protected _openAct: boolean = true; //打开动作
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_HD_SONHAI}?prefab/ui/UISonhaiTask`;
  }
  protected onRegEvent(): void {
    MsgMgr.on(SonhaiMsgEnum.DAILY_ACTIVITY_TASK_CHANGE, this.loadTask, this);
  }

  protected onDelEvent(): void {
    MsgMgr.off(SonhaiMsgEnum.DAILY_ACTIVITY_TASK_CHANGE, this.loadTask, this);
  }

  protected async onEvtShow() {
    this.loadTask();
  }

  private async loadTask() {
    let taskIndexList = SonhaiModule.data.adventureMessage.taskIndexList;
    let targetValList = SonhaiModule.data.adventureMessage.targetValList;
    let taskTakeList = SonhaiModule.data.adventureMessage.taskTakeList;
    let db = await SonhaiModule.data.getSonhaiDb(sonhai_activityId);
    let taskVO: AdventureTaskVO = db.taskVO;

    let arr0 = [];
    let arr1 = [];
    let arr2 = [];
    for (let i = 0; i < taskIndexList.length; i++) {
      let obj: TaskInfo = Object.create(TaskInfo);
      obj.taskIndex = taskIndexList[i];
      obj.taskVal = targetValList[i];
      obj.sevenIndex = i;

      let avTaskInfo = taskVO.taskList[obj.taskIndex];
      let state = null;
      if (taskTakeList.indexOf(i) != -1) {
        state = TaskState.RECEIVE;
      } else {
        if (obj.taskVal >= avTaskInfo[1]) {
          state = TaskState.UNRECEIVEABLE;
        } else {
          state = TaskState.UNFINISHED;
        }
      }
      obj.state = state;

      switch (state) {
        case TaskState.UNFINISHED:
          arr0.push(obj);
          break;
        case TaskState.UNRECEIVEABLE:
          arr1.push(obj);
          break;
        case TaskState.RECEIVE:
          arr2.push(obj);
          break;
      }
    }

    let list: TaskInfo[] = arr1.concat(arr0, arr2);

    for (let i = 0; i < list.length; i++) {
      let taskInfo = list[i];
      let index = taskInfo.taskIndex;
      let avTaskInfo = taskVO.taskList[index];
      let avTaskRewardList = taskVO.rewardList[index];
      let taskdb = MainTaskModule.data.getConfigTask(avTaskInfo[0]);
      let task_item = this.getNode("content_task").children[i];
      if (!task_item) {
        task_item = ToolExt.clone(this.getNode("task_item"), this);
        task_item.setPosition(v3(0, 0, 0));
        this.getNode("content_task").addChild(task_item);
        task_item.active = true;
      }
      task_item.name = "task_item_" + index;
      let str = taskdb.des;
      str = str.replace("s%", String(avTaskInfo[1]));
      task_item["task_des"].getComponent(Label).string = str;

      let val = taskInfo.taskVal > avTaskInfo[1] ? avTaskInfo[1] : taskInfo.taskVal;
      task_item["task_val_num"].getComponent(Label).string = val + "/" + avTaskInfo[1];
      task_item["spr_bar_progress"].getComponent(Sprite).fillRange = val / avTaskInfo[1];

      task_item["btn_task_get"]["index"] = taskInfo.sevenIndex;
      task_item["btn_task_go"]["taskdb"] = taskdb;

      let task_award_list = ToolExt.traAwardItemMapList(avTaskRewardList);
      let info = task_award_list[0];
      task_item["task_award_item"];
      FmUtils.setItemNode(task_item["task_award_item"], info.id, info.num);

      switch (taskInfo.state) {
        case TaskState.UNFINISHED:
          task_item["btn_yilingqu"].active = false;
          task_item["btn_task_get"].active = false;
          task_item["btn_task_go"].active = true;
          break;
        case TaskState.UNRECEIVEABLE:
          task_item["btn_yilingqu"].active = false;
          task_item["btn_task_get"].active = true;
          task_item["btn_task_go"].active = false;
          break;
        case TaskState.RECEIVE:
          task_item["btn_yilingqu"].active = true;
          task_item["btn_task_get"].active = false;
          task_item["btn_task_go"].active = false;
          break;
      }
    }
  }

  private on_click_btn_task_get(event) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let param: ActivityTakeRequest = {
      activityId: sonhai_activityId,
      index: event.node["index"],
      takeAll: false,
    };
    SonhaiModule.api.takeAdventureDailyTaskReward(param, (res: ActivityTakeResponse) => {
      log.log("领取探险任务奖励===回调==========", res);
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: res.rewardList });
    });
  }

  private on_click_btn_task_go(event) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let taskdb = event.node["taskdb"];
    GuideMgr.startGuide({ stepId: taskdb.guideType });
  }

  private on_click_btn_add_item() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
  }

  private on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
}
