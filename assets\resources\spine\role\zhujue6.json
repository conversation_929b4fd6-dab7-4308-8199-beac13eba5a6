{"skeleton": {"hash": "K/ZLB5+YQfFLCv5pUqrAgpBut6w=", "spine": "3.8.75", "x": -132.25, "y": 10.19, "width": 268, "height": 442.65, "images": "./images/", "audio": "D:/spine导出/主角换皮动画/主角/主角6"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "x": -12.45, "y": 327.12, "color": "d8ff00ff"}, {"name": "bone2", "parent": "root", "length": 48.9, "rotation": 179.1, "x": -3.75, "y": 194.38}, {"name": "bone3", "parent": "bone2", "length": 38.03, "rotation": -84.81, "x": -5.1, "y": -1.35}, {"name": "bone4", "parent": "bone3", "length": 32.57, "rotation": 9.75, "x": 38.03}, {"name": "bone5", "parent": "bone4", "length": 18.74, "rotation": -19.84, "x": 32.57}, {"name": "bone6", "parent": "bone2", "length": 20.46, "rotation": 81.63, "x": 19.37, "y": 9.38}, {"name": "bone7", "parent": "bone6", "length": 33.12, "rotation": 3.56, "x": 20.46}, {"name": "bone8", "parent": "bone7", "length": 38.95, "rotation": -0.36, "x": 33.12}, {"name": "bone9", "parent": "bone2", "length": 34.75, "rotation": 126.01, "x": -22.73, "y": 13.87}, {"name": "bone10", "parent": "bone9", "length": 27.11, "rotation": -18.32, "x": 34.75}, {"name": "bone11", "parent": "bone10", "length": 29.58, "rotation": -34.21, "x": 27.11}, {"name": "bone12", "parent": "bone2", "length": 34.25, "rotation": 105.49, "x": -3.99, "y": 11.47}, {"name": "bone13", "parent": "bone12", "length": 34.28, "rotation": -14.97, "x": 34.25}, {"name": "bone14", "parent": "bone13", "length": 39.59, "rotation": -6.87, "x": 34.28}, {"name": "bone15", "parent": "bone14", "length": 31.59, "rotation": -12.49, "x": 39.59}, {"name": "bone16", "parent": "bone2", "x": -38.21, "y": 11.6}, {"name": "bone17", "parent": "bone2", "x": 33.96, "y": 15.13}, {"name": "bone18", "parent": "bone2", "x": -10.66, "y": 27.73}, {"name": "bone19", "parent": "bone18", "length": 22.14, "rotation": 74.86, "x": -0.27, "y": 17.71}, {"name": "bone20", "parent": "bone2", "length": 50.91, "rotation": 94.69, "x": 26.71, "y": 103.8}, {"name": "bone21", "parent": "bone20", "length": 27.14, "rotation": -10.92, "x": 50.62, "y": 0.33}, {"name": "bone22", "parent": "bone2", "length": 27.79, "rotation": 74.26, "x": -39.31, "y": 98.17}, {"name": "bone23", "parent": "bone22", "length": 19.98, "rotation": 66.61, "x": 27.4, "y": 0.21}, {"name": "bone24", "parent": "bone23", "length": 14.78, "rotation": 12.93, "x": 19.98}, {"name": "bone25", "parent": "bone3", "length": 43.15, "rotation": -134.89, "x": 11.4, "y": -36.31, "color": "76ff00ff"}, {"name": "bone26", "parent": "bone25", "length": 48.78, "rotation": 76.49, "x": 42.41, "y": 0.06, "color": "76ff00ff"}, {"name": "bone27", "parent": "bone26", "length": 26.75, "rotation": 28.79, "x": 48.78, "color": "76ff00ff"}, {"name": "bone28", "parent": "bone27", "length": 23.11, "rotation": 8.31, "x": 26.75, "color": "76ff00ff"}, {"name": "bone29", "parent": "bone27", "length": 17.21, "rotation": 29.65, "x": 23.76, "y": 12.39}, {"name": "bone30", "parent": "bone3", "length": 41, "rotation": -81.45, "x": 58.19, "y": -53.92, "color": "76ff00ff"}, {"name": "bone31", "parent": "bone30", "length": 48.42, "rotation": 73.57, "x": 41, "color": "76ff00ff"}, {"name": "bone32", "parent": "bone31", "length": 18.32, "rotation": 39.04, "x": 48.42, "color": "76ff00ff"}, {"name": "bone33", "parent": "bone32", "length": 15.93, "rotation": 11.48, "x": 18.32, "color": "76ff00ff"}, {"name": "bone34", "parent": "bone3", "length": 28.81, "rotation": 58.26, "x": 61.42, "y": 63.05, "color": "76ff00ff"}, {"name": "bone35", "parent": "bone34", "length": 45.34, "rotation": -38.8, "x": 28.81, "color": "76ff00ff"}, {"name": "bone36", "parent": "bone35", "length": 19.24, "rotation": -2.5, "x": 45.34, "color": "76ff00ff"}, {"name": "bone37", "parent": "bone36", "length": 21.64, "rotation": -16.85, "x": 19.24, "color": "76ff00ff"}, {"name": "bone38", "parent": "bone36", "length": 15.75, "rotation": -39.69, "x": 13.44, "y": -13.06, "color": "76ff00ff"}, {"name": "bone39", "parent": "bone3", "length": 41.54, "rotation": 142.02, "x": 21.2, "y": 46.43, "color": "76ff00ff"}, {"name": "bone40", "parent": "bone39", "length": 47.81, "rotation": -98.33, "x": 41.54, "color": "76ff00ff"}, {"name": "bone41", "parent": "bone40", "length": 22.62, "rotation": -22.87, "x": 47.81, "color": "76ff00ff"}, {"name": "bone42", "parent": "bone3", "x": 30.83, "y": -24.39, "color": "f50000ff"}, {"name": "bone43", "parent": "bone3", "x": 31.08, "y": 21.81, "color": "f50000ff"}, {"name": "bone44", "parent": "bone3", "x": 49.23, "y": -45.22, "color": "f50000ff"}, {"name": "bone45", "parent": "bone3", "x": 32.25, "y": -74.2, "color": "f50000ff"}, {"name": "bone46", "parent": "bone3", "x": 63.2, "y": -64.94, "color": "f50000ff"}, {"name": "bone47", "parent": "bone3", "x": 111.75, "y": -66.65, "color": "f50000ff"}, {"name": "bone48", "parent": "bone3", "x": 124.1, "y": -9.31, "color": "f50000ff"}, {"name": "bone49", "parent": "bone3", "x": 123.75, "y": 58.96, "color": "f50000ff"}, {"name": "bone50", "parent": "bone3", "x": 72.52, "y": 86.89, "color": "f50000ff"}, {"name": "bone51", "parent": "bone3", "x": 57.4, "y": 64.04, "color": "f50000ff"}, {"name": "bone52", "parent": "bone3", "x": 63.38, "y": 33.07, "color": "f50000ff"}, {"name": "bone53", "parent": "bone3", "x": 8.93, "y": -33.4, "color": "f50000ff"}, {"name": "bone54", "parent": "bone3", "x": 8.9, "y": 26.07, "color": "f50000ff"}, {"name": "bone55", "parent": "bone3", "x": -13.28, "y": 58.35, "color": "f50000ff"}, {"name": "bone56", "parent": "bone3", "x": -34.93, "y": 83.79, "color": "f50000ff"}, {"name": "bone57", "parent": "bone3", "x": 9.03, "y": 80.23, "color": "f50000ff"}, {"name": "bone58", "parent": "bone3", "x": 4.32, "y": 118.54, "color": "f50000ff"}, {"name": "bone59", "parent": "bone3", "x": -32.39, "y": -53.28, "color": "f50000ff"}, {"name": "bone60", "parent": "bone3", "x": 4.59, "y": -76.73, "color": "f50000ff"}, {"name": "bone61", "parent": "bone3", "x": -10.39, "y": -106.51, "color": "f50000ff"}, {"name": "bone62", "parent": "bone3", "x": -32.66, "y": -126.66, "color": "f50000ff"}, {"name": "bone63", "parent": "bone4", "x": 33.7, "y": 25.3}, {"name": "bone64", "parent": "bone4", "x": 19.59, "y": -24.77}, {"name": "bone65", "parent": "bone5", "length": 7.23, "rotation": -52.49, "x": 67.14, "y": -15.14}, {"name": "bone66", "parent": "bone65", "length": 17.73, "rotation": -94.35, "x": 7.23}, {"name": "bone67", "parent": "bone66", "length": 20.35, "rotation": -36.56, "x": 17.65, "y": -0.16}, {"name": "bone68", "parent": "bone67", "length": 15.45, "rotation": 22.76, "x": 20.56, "y": -0.15}, {"name": "bone69", "parent": "bone68", "length": 7.97, "rotation": -14.85, "x": 15.45}, {"name": "bone70", "parent": "bone5", "length": 8.45, "rotation": 50.81, "x": 65.82, "y": 3.28}, {"name": "bone71", "parent": "bone70", "length": 16.28, "rotation": 93.15, "x": 8.58, "y": 0.13}, {"name": "bone72", "parent": "bone71", "length": 19.47, "rotation": 21.73, "x": 16.28}, {"name": "bone73", "parent": "bone72", "length": 16.55, "rotation": 14.47, "x": 19.47}, {"name": "bone74", "parent": "bone73", "length": 13.58, "rotation": 7.18, "x": 16.55}, {"name": "bone75", "parent": "bone5", "x": 113.31, "y": 65.78}, {"name": "bone76", "parent": "bone5", "x": 106.57, "y": 46.18}, {"name": "bone77", "parent": "bone5", "x": 93.83, "y": 28.33}, {"name": "bone78", "parent": "bone5", "x": 107.41, "y": 7.51}, {"name": "bone79", "parent": "bone5", "x": 96.6, "y": -9.6}, {"name": "bone80", "parent": "bone5", "x": 90.75, "y": 62.22}, {"name": "bone81", "parent": "bone5", "x": 72.44, "y": 50.89}, {"name": "bone82", "parent": "bone5", "x": 64.55, "y": 38.99}, {"name": "bone83", "parent": "bone5", "x": 67.68, "y": 65.51}, {"name": "bone84", "parent": "bone5", "x": 54.19, "y": 51.4}, {"name": "bone85", "parent": "bone", "color": "d8ff00ff"}, {"name": "bone86", "parent": "bone5", "x": 36.64, "y": -10.39}, {"name": "bone87", "parent": "root", "x": 14.14, "y": 43.44}], "slots": [{"name": "piaodai4", "bone": "root", "attachment": "piaodai4"}, {"name": "piaodai3", "bone": "root", "attachment": "piaodai3"}, {"name": "guanglun", "bone": "bone", "attachment": "guanglun"}, {"name": "guanglun2", "bone": "bone85", "color": "ffffff00", "attachment": "guanglun", "blend": "additive"}, {"name": "s4", "bone": "root", "attachment": "s4"}, {"name": "s3", "bone": "root", "attachment": "s3"}, {"name": "s2", "bone": "root", "attachment": "s2"}, {"name": "s1", "bone": "root", "attachment": "s1"}, {"name": "j1", "bone": "root", "attachment": "j1"}, {"name": "j2", "bone": "root", "attachment": "j2"}, {"name": "bd", "bone": "root", "attachment": "bd"}, {"name": "piaodai2", "bone": "root", "attachment": "piaodai2"}, {"name": "piaodai1", "bone": "root", "attachment": "piaodai1"}, {"name": "p<PERSON><PERSON><PERSON>", "bone": "root", "attachment": "p<PERSON><PERSON><PERSON>"}, {"name": "bd4", "bone": "root", "attachment": "bd4"}, {"name": "bd3", "bone": "root", "attachment": "bd3"}, {"name": "bd2", "bone": "root", "attachment": "bd2"}, {"name": "bd1", "bone": "root", "attachment": "bd1"}, {"name": "sho<PERSON><PERSON>", "bone": "root", "attachment": "sho<PERSON><PERSON>"}, {"name": "er", "bone": "bone5", "attachment": "er"}, {"name": "biyan", "bone": "bone86"}, {"name": "toufa2", "bone": "root", "attachment": "toufa2"}, {"name": "toufa1", "bone": "root", "attachment": "toufa1"}, {"name": "wuqi", "bone": "bone87"}, {"name": "wuqi2", "bone": "bone87", "color": "ffffff66", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"bd": {"bd": {"type": "mesh", "uvs": [0.36384, 0.11497, 0.36384, 0.05156, 0.41137, 0.00928, 0.48826, 0, 0.55397, 0.01853, 0.58333, 0.06873, 0.58473, 0.14535, 0.65463, 0.18763, 0.7455, 0.21933, 0.84616, 0.23519, 0.91327, 0.28671, 0.93983, 0.36466, 0.9622, 0.44921, 0.98038, 0.57207, 0.98038, 0.66851, 1, 0.74777, 0.96919, 0.7993, 0.88531, 0.80854, 0.79863, 0.81647, 0.80422, 0.87196, 0.80842, 0.91952, 0.7469, 0.98161, 0.64764, 1, 0.56096, 1, 0.43653, 0.99482, 0.32189, 0.9499, 0.28694, 0.90631, 0.30092, 0.86139, 0.28554, 0.82572, 0.24919, 0.82704, 0.20865, 0.81251, 0.15832, 0.72267, 0.09121, 0.63416, 0.06465, 0.55225, 0.01852, 0.45845, 0.01432, 0.36333, 0.04228, 0.26029, 0.11638, 0.20744, 0.20166, 0.20612, 0.30651, 0.16781, 0.58613, 0.34352, 0.61548, 0.43599, 0.62108, 0.5364, 0.533, 0.54697, 0.37083, 0.54961, 0.27995, 0.5113, 0.71335, 0.54432, 0.8224, 0.53904, 0.81541, 0.39108, 0.8224, 0.47166, 0.81261, 0.64605, 0.79304, 0.73324, 0.31071, 0.5932, 0.36104, 0.68304, 0.40018, 0.79665, 0.51762, 0.80854, 0.62667, 0.81911, 0.72453, 0.81515, 0.3918, 0.88385, 0.50783, 0.90763, 0.63506, 0.90763, 0.74271, 0.89706, 0.62248, 0.67775, 0.78745, 0.3171, 0.6784, 0.28275, 0.58752, 0.27218, 0.47009, 0.27086, 0.38481, 0.27614, 0.28415, 0.30653, 0.29114, 0.399, 0.28974, 0.45713, 0.13595, 0.30388, 0.14574, 0.42014, 0.16671, 0.53111, 0.23242, 0.64076, 0.27995, 0.73456, 0.86154, 0.31313, 0.8895, 0.38447, 0.89649, 0.48223, 0.89929, 0.5615, 0.89789, 0.66586, 0.8895, 0.74117, 0.49106, 0.06344, 0.49246, 0.15856, 0.3862, 0.2048, 0.58613, 0.21273], "triangles": [24, 59, 23, 25, 58, 24, 21, 61, 20, 25, 26, 58, 61, 19, 20, 23, 60, 22, 22, 60, 21, 32, 73, 74, 74, 45, 52, 74, 73, 45, 32, 33, 73, 52, 45, 44, 73, 33, 72, 45, 70, 44, 70, 69, 44, 33, 34, 72, 45, 73, 70, 70, 73, 72, 34, 35, 72, 70, 72, 69, 35, 71, 72, 72, 68, 69, 72, 71, 68, 69, 68, 67, 35, 36, 71, 71, 38, 68, 68, 39, 67, 68, 38, 39, 36, 37, 71, 71, 37, 38, 13, 78, 12, 47, 78, 79, 13, 79, 78, 47, 46, 49, 49, 46, 41, 47, 49, 78, 49, 41, 48, 48, 41, 63, 63, 41, 64, 12, 78, 77, 78, 49, 77, 49, 48, 77, 77, 11, 12, 48, 76, 77, 48, 63, 76, 77, 76, 11, 76, 10, 11, 64, 8, 63, 63, 9, 76, 63, 8, 9, 76, 9, 10, 65, 7, 64, 64, 7, 8, 67, 39, 84, 65, 85, 7, 66, 83, 85, 66, 84, 83, 83, 6, 85, 85, 6, 7, 39, 0, 84, 84, 0, 83, 0, 82, 83, 6, 82, 5, 6, 83, 82, 82, 1, 2, 82, 0, 1, 82, 4, 5, 2, 3, 82, 82, 3, 4, 40, 43, 44, 66, 69, 67, 66, 44, 69, 43, 41, 42, 43, 40, 41, 40, 44, 66, 46, 42, 41, 41, 40, 64, 40, 65, 64, 40, 66, 65, 67, 84, 66, 66, 85, 65, 23, 59, 60, 24, 58, 59, 60, 61, 21, 58, 26, 27, 59, 56, 60, 59, 55, 56, 59, 58, 55, 60, 57, 61, 60, 56, 57, 19, 57, 18, 19, 61, 57, 58, 54, 55, 58, 27, 54, 27, 28, 54, 28, 29, 75, 29, 30, 75, 28, 75, 54, 57, 56, 62, 17, 18, 51, 56, 55, 62, 18, 57, 51, 57, 62, 51, 30, 31, 75, 17, 81, 16, 17, 51, 81, 62, 55, 53, 16, 81, 15, 14, 81, 80, 81, 14, 15, 75, 53, 54, 53, 55, 54, 62, 53, 43, 43, 53, 44, 81, 51, 80, 31, 74, 75, 75, 74, 53, 51, 50, 80, 51, 62, 50, 31, 32, 74, 74, 52, 53, 53, 52, 44, 43, 42, 62, 62, 46, 50, 62, 42, 46, 80, 13, 14, 80, 79, 13, 80, 50, 79, 50, 47, 79, 50, 46, 47], "vertices": [3, 4, 49.61, 8.52, 0.00073, 5, 13.13, 13.8, 0.86285, 63, 15.9, -16.78, 0.13642, 2, 5, 20.01, 14.5, 0.96566, 63, 22.61, -18.45, 0.03434, 2, 5, 25.09, 10.09, 0.99046, 63, 25.89, -24.32, 0.00954, 2, 5, 26.9, 2.32, 0.9999, 63, 24.95, -32.25, 0.0001, 1, 5, 25.57, -4.62, 1, 2, 5, 20.43, -8.18, 0.99628, 64, 29.43, 10.13, 0.00372, 3, 4, 40.88, -12.75, 0.04929, 5, 12.14, -9.17, 0.86055, 64, 21.29, 12.02, 0.09017, 3, 4, 34.66, -18.62, 0.16146, 5, 8.28, -16.8, 0.38448, 64, 15.07, 6.15, 0.45406, 3, 4, 29.04, -26.86, 0.03131, 5, 5.79, -26.46, 0.06581, 64, 9.45, -2.09, 0.90288, 3, 3, 68.69, -31.76, 0.00922, 5, 5.12, -36.95, 0.00038, 64, 5.26, -11.73, 0.99041, 2, 3, 62.58, -38.24, 0.05158, 64, -1.87, -17.07, 0.94842, 2, 3, 53.9, -40.33, 0.1447, 64, -10.77, -17.67, 0.8553, 3, 3, 44.54, -41.94, 0.30084, 4, -0.68, -42.43, 0.00167, 64, -20.27, -17.67, 0.69749, 3, 3, 31.04, -42.8, 0.53656, 4, -14.13, -41, 0.0004, 64, -33.72, -16.24, 0.46304, 2, 3, 20.56, -42.02, 0.68002, 64, -43.92, -13.69, 0.31998, 2, 3, 11.79, -43.39, 0.73899, 64, -52.79, -13.55, 0.26101, 2, 3, 6.43, -39.8, 0.75613, 64, -57.47, -9.11, 0.24387, 2, 3, 6.07, -31.11, 0.79431, 64, -56.35, -0.48, 0.20569, 2, 3, 5.88, -22.14, 0.89974, 64, -55.02, 8.39, 0.10026, 1, 2, -27.29, -3.17, 1, 1, 2, -27.81, 2, 1, 1, 2, -21.58, 8.87, 1, 1, 2, -11.39, 11.04, 1, 1, 2, -2.46, 11.18, 1, 1, 2, 10.36, 10.81, 1, 1, 2, 22.25, 6.1, 1, 1, 2, 25.92, 1.41, 1, 3, 3, 4.83, 29.34, 0.8918, 4, -27.75, 34.54, 0.01287, 63, -61.45, 9.24, 0.09533, 3, 3, 8.82, 30.63, 0.80436, 4, -23.59, 35.13, 0.03264, 63, -57.3, 9.84, 0.163, 3, 3, 8.96, 34.38, 0.73954, 4, -22.83, 38.8, 0.04542, 63, -56.53, 13.5, 0.21504, 3, 3, 10.85, 38.42, 0.69787, 4, -20.28, 42.47, 0.05349, 63, -53.98, 17.17, 0.24865, 3, 3, 21.01, 42.86, 0.53984, 4, -9.52, 45.12, 0.08279, 63, -43.22, 19.83, 0.37737, 3, 3, 31.14, 49.03, 0.34772, 4, 1.52, 49.49, 0.09161, 63, -32.19, 24.19, 0.56067, 3, 3, 40.25, 51.09, 0.21988, 4, 10.84, 49.98, 0.07179, 63, -22.86, 24.68, 0.70833, 3, 3, 50.8, 55.07, 0.10434, 4, 21.91, 52.11, 0.02769, 63, -11.79, 26.81, 0.86797, 3, 3, 61.17, 54.72, 0.04469, 4, 32.08, 50.01, 0.00384, 63, -1.63, 24.72, 0.95147, 2, 3, 72.16, 51.01, 0.00908, 63, 8.57, 19.2, 0.99092, 3, 3, 77.33, 42.97, 0.00045, 5, 0.53, 38.14, 0.00026, 63, 12.31, 10.4, 0.99929, 2, 5, 1.56, 29.41, 0.04771, 63, 10.32, 1.84, 0.95229, 3, 4, 45.45, 15.65, 0.02269, 5, 6.8, 19.09, 0.4722, 63, 11.75, -9.65, 0.50511, 3, 4, 19.89, -7.65, 0.77799, 5, -9.34, -11.5, 0.02289, 64, 0.3, 17.12, 0.19912, 3, 3, 48.64, -6.43, 0.03706, 4, 9.38, -8.14, 0.77625, 64, -10.21, 16.63, 0.18669, 3, 3, 37.69, -6.19, 0.5532, 4, -1.38, -6.04, 0.36437, 64, -20.97, 18.72, 0.08244, 3, 3, 37.22, 2.94, 0.56117, 4, -0.3, 3.04, 0.42905, 63, -34, -22.26, 0.00978, 3, 3, 38.18, 19.62, 0.35977, 4, 3.47, 19.31, 0.3834, 63, -30.23, -5.98, 0.25683, 3, 3, 43.04, 28.64, 0.23068, 4, 9.79, 27.38, 0.27526, 63, -23.91, 2.08, 0.49406, 3, 3, 36.12, -15.6, 0.55162, 4, -4.52, -15.05, 0.17496, 64, -24.11, 9.71, 0.27342, 3, 3, 35.85, -26.85, 0.48475, 4, -6.69, -26.09, 0.05372, 64, -26.28, -1.32, 0.46153, 3, 3, 51.99, -27.33, 0.13158, 4, 9.13, -29.3, 0.04153, 64, -10.46, -4.54, 0.82689, 3, 3, 43.17, -27.39, 0.31082, 4, 0.43, -27.87, 0.06903, 64, -19.15, -3.1, 0.62015, 3, 3, 24.29, -24.97, 0.72124, 4, -17.76, -22.28, 0.01187, 64, -37.35, 2.48, 0.26688, 3, 3, 14.97, -22.25, 0.83976, 4, -26.49, -18.02, 0.00029, 64, -46.08, 6.75, 0.15995, 3, 3, 33.9, 26.15, 0.43854, 4, 0.36, 26.47, 0.22521, 63, -33.34, 1.18, 0.33624, 3, 3, 23.75, 21.72, 0.71174, 4, -10.39, 23.82, 0.10467, 63, -44.1, -1.48, 0.1836, 3, 3, 11.1, 18.62, 0.90305, 4, -23.38, 22.91, 0.01663, 63, -57.09, -2.39, 0.08032, 3, 3, 8.9, 6.66, 0.98803, 4, -27.58, 11.49, 0.00031, 63, -61.28, -13.81, 0.01166, 2, 3, 6.92, -4.46, 0.99345, 64, -51.01, 25.64, 0.00655, 2, 3, 6.59, -14.54, 0.95074, 64, -53.03, 15.76, 0.04926, 3, 3, 1.69, 20.19, 0.94905, 4, -32.39, 26.05, 0.00283, 63, -66.1, 0.76, 0.04812, 2, 3, -1.79, 8.47, 0.99189, 63, -71.51, -10.21, 0.00811, 2, 3, -2.77, -4.6, 0.99795, 64, -60.58, 27.14, 0.00205, 2, 3, -2.45, -15.74, 0.98016, 64, -62.15, 16.1, 0.01984, 2, 3, 22.31, -5.18, 0.97689, 64, -35.95, 22.32, 0.02311, 3, 3, 60.24, -25.06, 0.02049, 4, 17.65, -28.46, 0.00178, 64, -1.94, -3.7, 0.97773, 3, 4, 24.01, -18.48, 0.20944, 5, -1.78, -20.29, 0.09222, 64, 4.42, 6.29, 0.69834, 3, 4, 27.4, -9.67, 0.5411, 5, -1.58, -10.86, 0.21337, 64, 7.81, 15.09, 0.24553, 3, 4, 30.47, 2.03, 0.95247, 5, -2.67, 1.19, 0.02054, 63, -3.23, -23.27, 0.02699, 4, 3, 67.8, 15.96, 0.00014, 4, 32.04, 10.69, 0.43572, 5, -4.13, 9.87, 0.23607, 63, -1.66, -14.61, 0.32808, 4, 3, 65.27, 26.54, 0.00394, 4, 31.34, 21.55, 0.12479, 5, -8.47, 19.85, 0.03774, 63, -2.36, -3.75, 0.83353, 4, 3, 55.16, 26.58, 0.05809, 4, 21.39, 23.29, 0.29572, 5, -18.43, 18.12, 0.00018, 63, -12.32, -2, 0.64602, 3, 3, 48.86, 27.2, 0.13224, 4, 15.28, 24.97, 0.3169, 63, -18.43, -0.33, 0.55085, 3, 3, 66.7, 41.74, 0.01249, 4, 35.32, 36.29, 0.00062, 63, 1.62, 10.99, 0.9869, 3, 3, 53.99, 41.69, 0.08683, 4, 22.79, 38.38, 0.0554, 63, -10.92, 13.09, 0.85777, 3, 3, 41.76, 40.44, 0.23267, 4, 10.53, 39.22, 0.11561, 63, -23.18, 13.92, 0.65172, 3, 3, 29.34, 34.58, 0.46964, 4, -2.71, 35.55, 0.12591, 63, -36.41, 10.26, 0.40445, 3, 3, 18.78, 30.46, 0.68551, 4, -13.81, 33.28, 0.07016, 63, -47.52, 7.99, 0.24432, 3, 3, 60.1, -32.71, 0.05059, 4, 16.22, -35.97, 2e-05, 64, -3.37, -11.21, 0.94939, 3, 3, 52.13, -35, 0.15495, 4, 7.98, -36.88, 0.00494, 64, -11.61, -12.11, 0.8401, 3, 3, 41.45, -34.92, 0.34984, 4, -2.53, -34.99, 0.0151, 64, -22.12, -10.23, 0.63506, 3, 3, 32.82, -34.56, 0.51839, 4, -10.99, -33.18, 0.0103, 64, -30.58, -8.41, 0.47131, 3, 3, 21.48, -33.57, 0.69208, 4, -21.99, -30.28, 0.00137, 64, -41.58, -5.51, 0.30655, 2, 3, 13.36, -32.09, 0.76708, 64, -49.33, -2.68, 0.23292, 2, 5, 20.05, 1.33, 0.99928, 63, 18.17, -30.85, 0.00072, 2, 5, 9.75, 0.14, 0.99965, 63, 8.08, -28.47, 0.00035, 3, 4, 39.55, 8.66, 0.09407, 5, 3.62, 10.52, 0.65216, 63, 5.85, -16.64, 0.25377, 3, 4, 33.72, -11.11, 0.23143, 5, 4.85, -10.06, 0.55981, 64, 14.13, 13.66, 0.20877], "hull": 40, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 0, 78, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 84, 92, 92, 94, 96, 98, 98, 94, 94, 100, 100, 102, 90, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 116, 118, 118, 120, 120, 122, 112, 124, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 142, 144, 144, 146, 146, 148, 148, 150, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 6, 164, 164, 166, 166, 168, 166, 170], "width": 103, "height": 109}}, "piaodai": {"piaodai": {"type": "mesh", "uvs": [0.014, 1, 0.04771, 0.93018, 0.09766, 0.85218, 0.13012, 0.72618, 0.12263, 0.57318, 0.10233, 0.53407, 0.12677, 0.43621, 0.18899, 0.34532, 0.27924, 0.30609, 0.37635, 0.2937, 0.46144, 0.29576, 0.54122, 0.30521, 0.62973, 0.32601, 0.72787, 0.363, 0.81736, 0.40923, 0.87605, 0.49708, 0.89914, 0.57798, 0.87508, 0.66352, 0.84141, 0.75598, 0.82505, 0.87388, 0.83179, 0.95248, 0.84814, 0.98715, 0.86065, 0.93861, 0.88285, 0.84832, 0.9402, 0.76155, 0.98411, 0.63736, 1, 0.51146, 0.99048, 0.35324, 0.94374, 0.20012, 0.87435, 0.10315, 0.78867, 0.04531, 0.69449, 0.02149, 0.60315, 0.00277, 0.51322, 0, 0.42531, 0, 0.31293, 0, 0.21488, 0.01914, 0.14227, 0.05849, 0.08274, 0.12011, 0.03298, 0.23583, 0.00675, 0.35026, 0, 0.48011, 0.01318, 0.59583, 0.03512, 0.66654, 0.03565, 0.72182, 0.01906, 0.79768, 0, 0.8774, 0, 0.94297, 0, 1, 0.01211, 0.94811, 0.03298, 0.87482, 0.06027, 0.79382, 0.07739, 0.69611, 0.0699, 0.59582, 0.0501, 0.49425, 0.0715, 0.35025, 0.13947, 0.24226, 0.21867, 0.18311, 0.31609, 0.14972, 0.42136, 0.1444, 0.51998, 0.14706, 0.61639, 0.1657, 0.71612, 0.19498, 0.81031, 0.24557, 0.90046, 0.32415, 0.94006, 0.47119, 0.94366, 0.61246, 0.89806, 0.71914, 0.86516, 0.79486, 0.84781, 0.87267, 0.84376, 0.94771], "triangles": [11, 61, 12, 12, 61, 62, 10, 60, 11, 11, 60, 61, 9, 59, 10, 10, 59, 60, 9, 58, 59, 61, 31, 62, 60, 32, 61, 61, 32, 31, 58, 35, 59, 60, 59, 33, 59, 34, 33, 60, 33, 32, 59, 35, 34, 42, 54, 53, 42, 41, 54, 53, 54, 5, 6, 5, 55, 41, 40, 54, 5, 54, 55, 54, 40, 55, 6, 56, 7, 6, 55, 56, 40, 39, 55, 55, 38, 56, 55, 39, 38, 7, 57, 8, 7, 56, 57, 8, 58, 9, 8, 57, 58, 56, 37, 57, 56, 38, 37, 37, 36, 57, 57, 36, 58, 36, 35, 58, 48, 49, 0, 0, 49, 1, 48, 47, 49, 1, 49, 50, 50, 49, 46, 49, 47, 46, 1, 51, 2, 1, 50, 51, 46, 45, 50, 50, 45, 51, 51, 52, 2, 2, 52, 3, 45, 44, 51, 51, 44, 52, 52, 4, 3, 44, 43, 52, 43, 53, 52, 4, 53, 5, 4, 52, 53, 43, 42, 53, 20, 70, 21, 21, 70, 22, 20, 19, 70, 70, 69, 22, 70, 19, 69, 22, 69, 23, 19, 18, 69, 69, 68, 23, 69, 18, 68, 23, 67, 24, 23, 68, 67, 67, 68, 17, 24, 66, 25, 24, 67, 66, 68, 18, 17, 67, 16, 66, 67, 17, 16, 25, 66, 26, 16, 65, 66, 66, 65, 26, 16, 15, 65, 65, 27, 26, 15, 64, 65, 15, 14, 64, 65, 64, 27, 13, 63, 14, 14, 63, 64, 12, 62, 13, 13, 62, 63, 64, 28, 27, 63, 29, 64, 64, 29, 28, 62, 30, 63, 63, 30, 29, 62, 31, 30], "vertices": [1, 50, -7.14, 2.5, 1, 2, 50, -2.24, -4.12, 0.98898, 49, -53.47, 23.81, 0.01102, 2, 50, 3.05, -13.78, 0.89145, 49, -48.17, 14.14, 0.10855, 2, 50, 12.28, -20.5, 0.76255, 49, -38.95, 7.43, 0.23745, 2, 50, 24.13, -20, 0.59018, 49, -27.1, 7.93, 0.40982, 2, 50, 27.42, -16.48, 0.46718, 49, -23.81, 11.45, 0.53282, 2, 50, 34.59, -21.55, 0.19148, 49, -16.64, 6.38, 0.80852, 3, 50, 40.71, -33.55, 0.01485, 49, -10.52, -5.63, 0.95402, 48, -10.87, 62.64, 0.03112, 2, 49, -8.75, -22.5, 0.72489, 48, -9.11, 45.77, 0.27511, 2, 49, -9.15, -40.49, 0.36364, 48, -9.5, 27.78, 0.63636, 2, 49, -10.48, -56.17, 0.10021, 48, -10.83, 12.1, 0.89979, 3, 47, -0.32, 54.77, 0.04409, 49, -12.31, -70.83, 0.00253, 48, -12.66, -2.57, 0.95338, 2, 47, -3.14, 38.56, 0.32255, 48, -15.49, -18.78, 0.67745, 2, 47, -7.34, 20.67, 0.74259, 48, -19.68, -36.67, 0.25741, 3, 47, -12.12, 4.43, 0.94924, 46, 36.43, 2.72, 0.03224, 48, -24.47, -52.91, 0.01852, 2, 47, -19.68, -5.89, 0.77763, 46, 28.87, -7.6, 0.22237, 2, 47, -26.21, -9.69, 0.534, 46, 22.34, -11.4, 0.466, 2, 47, -32.45, -4.76, 0.2667, 46, 16.1, -6.47, 0.7333, 2, 47, -39.08, 1.99, 0.07745, 46, 9.47, 0.28, 0.92255, 2, 47, -47.91, 5.68, 0.00028, 46, 0.64, 3.97, 0.99972, 2, 47, -54.04, 4.89, 0, 46, -5.49, 3.18, 1, 2, 47, -56.92, 2.08, 0, 46, -8.37, 0.37, 1, 2, 47, -53.37, -0.51, 0, 46, -4.82, -2.22, 1, 2, 47, -46.74, -5.12, 0.06693, 46, 1.81, -6.84, 0.93307, 2, 47, -40.88, -16.21, 0.30707, 46, 7.67, -17.92, 0.69293, 2, 47, -31.95, -25.02, 0.51969, 46, 16.6, -26.73, 0.48031, 2, 47, -22.5, -28.68, 0.65035, 46, 26.05, -30.39, 0.34965, 2, 47, -10.22, -27.83, 0.78491, 46, 38.33, -29.54, 0.21509, 2, 47, 2.18, -20.09, 0.91729, 46, 50.73, -21.8, 0.08271, 3, 47, 10.59, -7.85, 0.99162, 46, 59.14, -9.56, 0.00575, 48, -1.76, -65.19, 0.00262, 2, 47, 16.22, 7.63, 0.86903, 48, 3.87, -49.71, 0.13097, 2, 47, 19.35, 24.86, 0.53034, 48, 7, -32.48, 0.46966, 2, 47, 22.05, 41.61, 0.17193, 48, 9.7, -15.73, 0.82807, 3, 47, 23.51, 58.18, 0.00274, 49, 11.51, -67.43, 0.01647, 48, 11.16, 0.84, 0.98079, 2, 49, 12.73, -51.21, 0.19659, 48, 12.38, 17.06, 0.80341, 2, 49, 14.28, -30.48, 0.57558, 48, 13.93, 37.79, 0.42442, 2, 49, 14.17, -12.28, 0.87154, 48, 13.82, 55.99, 0.12846, 3, 50, 63.38, -26.59, 0.00011, 49, 12.15, 1.34, 0.98681, 48, 11.8, 69.61, 0.01308, 2, 50, 59.47, -15.25, 0.03395, 49, 8.24, 12.68, 0.96605, 2, 50, 51.28, -5.4, 0.13812, 49, 0.05, 22.53, 0.86188, 2, 50, 42.85, 0.1, 0.24684, 49, -8.38, 28.02, 0.75316, 2, 50, 32.97, 2.09, 0.37293, 49, -18.25, 30.02, 0.62707, 2, 50, 23.91, 0.32, 0.51253, 49, -27.32, 28.25, 0.48747, 2, 50, 18.17, -3.32, 0.66754, 49, -33.05, 24.61, 0.33246, 2, 50, 13.92, -3.1, 0.80264, 49, -37.31, 24.83, 0.19736, 2, 50, 8.33, 0.4, 0.94008, 49, -42.9, 28.33, 0.05992, 2, 50, 2.47, 4.38, 0.99695, 49, -48.76, 32.3, 0.00305, 1, 50, -2.57, 4.75, 1, 1, 50, -6.94, 5.08, 1, 1, 50, -3.13, 2.55, 1, 2, 50, 2.21, -1.72, 0.98391, 49, -49.02, 26.21, 0.01609, 2, 50, 8.05, -7.22, 0.8862, 49, -43.17, 20.71, 0.1138, 2, 50, 15.32, -10.94, 0.74205, 49, -35.91, 16.98, 0.25795, 2, 50, 23.12, -10.14, 0.57086, 49, -28.1, 17.79, 0.42914, 2, 50, 31.2, -7.07, 0.39474, 49, -20.03, 20.86, 0.60526, 2, 50, 41.96, -11.85, 0.18755, 49, -9.27, 16.08, 0.81245, 2, 50, 49.31, -25.01, 0.01958, 49, -1.92, 2.92, 0.98042, 2, 49, 1.53, -12.03, 0.88711, 48, 1.17, 56.24, 0.11289, 2, 49, 2.74, -30.2, 0.57507, 48, 2.39, 38.07, 0.42493, 2, 49, 1.69, -49.65, 0.20922, 48, 1.34, 18.62, 0.79078, 2, 49, 0.13, -67.83, 0.0011, 48, -0.23, 0.44, 0.9989, 2, 47, 9.36, 40.1, 0.23812, 48, -2.99, -17.24, 0.76188, 2, 47, 5.73, 21.87, 0.65374, 48, -6.62, -35.47, 0.34626, 3, 47, 0.54, 4.79, 0.96173, 46, 49.09, 3.08, 0.00043, 48, -11.81, -52.55, 0.03784, 2, 47, -6.74, -11.39, 0.89956, 46, 41.81, -13.1, 0.10044, 2, 47, -18.58, -17.85, 0.70513, 46, 29.97, -19.56, 0.29487, 2, 47, -29.48, -17.7, 0.50062, 46, 19.07, -19.41, 0.49938, 2, 47, -37.04, -8.67, 0.24655, 46, 11.51, -10.39, 0.75345, 2, 47, -42.39, -2.17, 0.07884, 46, 6.15, -3.88, 0.92116, 2, 47, -48.13, 1.48, 0.00207, 46, 0.42, -0.23, 0.99793, 1, 46, -5.29, 0.95, 1], "hull": 49, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 0, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 42], "width": 185, "height": 77}}, "j1": {"j1": {"type": "mesh", "uvs": [0.46821, 0, 0.20109, 0.03064, 0.01665, 0.08123, 0.02301, 0.17518, 0.04845, 0.30527, 0.07389, 0.43898, 0.11204, 0.54919, 0.15021, 0.61966, 0.09297, 0.70097, 0.04845, 0.80938, 0.03573, 0.91056, 0.11841, 0.95031, 0.40461, 0.98464, 0.84344, 0.98825, 0.91341, 0.90333, 0.99608, 0.83286, 0.91976, 0.74975, 0.90704, 0.64676, 0.93248, 0.56907, 0.84981, 0.51306, 0.88797, 0.37393, 0.94521, 0.23661, 0.97064, 0.09207, 0.81801, 0.0198, 0.46821, 0.09388, 0.44913, 0.20771, 0.44277, 0.34141, 0.46185, 0.47511, 0.46184, 0.61424, 0.45549, 0.74614, 0.41733, 0.88888], "triangles": [14, 13, 30, 13, 12, 30, 12, 11, 30, 11, 10, 30, 10, 9, 30, 14, 30, 15, 15, 29, 16, 29, 15, 30, 30, 9, 29, 9, 8, 29, 29, 17, 16, 29, 28, 17, 17, 28, 18, 29, 8, 28, 28, 8, 7, 7, 6, 28, 28, 19, 18, 28, 27, 19, 28, 6, 27, 6, 5, 27, 19, 27, 20, 5, 26, 27, 27, 26, 20, 5, 4, 26, 20, 26, 21, 26, 25, 21, 26, 4, 25, 4, 3, 25, 21, 25, 22, 22, 25, 24, 25, 3, 24, 24, 2, 1, 24, 3, 2, 1, 0, 24, 24, 23, 22, 24, 0, 23], "vertices": [1, 20, -6.04, 1.95, 1, 1, 20, -3.79, -4.89, 1, 1, 20, 0.35, -9.78, 1, 1, 20, 8.61, -10.17, 1, 1, 20, 20.08, -10.29, 1, 1, 20, 31.86, -10.44, 1, 1, 20, 41.6, -10.13, 1, 2, 20, 47.85, -9.58, 0.97338, 21, -0.85, -10.25, 0.02662, 2, 20, 54.89, -11.48, 0.55412, 21, 6.43, -10.79, 0.44588, 2, 20, 64.34, -13.23, 0.08009, 21, 16.04, -10.71, 0.91991, 2, 20, 73.2, -14.13, 0.00196, 21, 24.91, -9.92, 0.99804, 1, 21, 28.12, -7.43, 1, 1, 21, 30.23, 0.04, 1, 1, 21, 29.19, 10.97, 1, 1, 21, 21.56, 11.77, 1, 1, 21, 15.15, 13.06, 1, 1, 21, 8.13, 10.26, 1, 2, 20, 51.48, 9.14, 0.01929, 21, -0.83, 8.82, 0.98071, 2, 20, 44.7, 10.23, 0.39043, 21, -7.69, 8.6, 0.60957, 2, 20, 39.65, 8.49, 0.78691, 21, -12.32, 5.94, 0.21309, 2, 20, 27.49, 10.25, 0.99969, 21, -24.59, 5.36, 0.00031, 1, 20, 15.53, 12.48, 1, 1, 20, 2.88, 13.95, 1, 1, 20, -3.72, 10.57, 1, 1, 20, 2.21, 1.41, 1, 1, 20, 12.17, 0.27, 1, 1, 20, 23.9, -0.67, 1, 1, 20, 35.67, -0.97, 1, 1, 20, 47.89, -1.78, 1, 2, 20, 59.46, -2.7, 0.03717, 21, 9.25, -1.3, 0.96283, 2, 20, 71.93, -4.49, 0.00011, 21, 21.83, -0.69, 0.99989], "hull": 24, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 0, 46, 0, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60], "width": 25, "height": 88}}, "j2": {"j2": {"type": "mesh", "uvs": [0.19953, 0.18346, 0.23882, 0.06387, 0.38086, 0, 0.64682, 0, 0.77375, 0.03339, 0.77375, 0.12249, 0.66495, 0.24677, 0.5773, 0.36401, 0.51988, 0.46249, 0.58033, 0.56097, 0.68308, 0.65008, 0.83721, 0.70635, 0.9581, 0.80952, 1, 0.9127, 0.88255, 0.97132, 0.73144, 0.96897, 0.5229, 0.92207, 0.37482, 0.84001, 0.28113, 0.76732, 0.11793, 0.73918, 0.03633, 0.68525, 6e-05, 0.59145, 0.07562, 0.46718, 0.13002, 0.35463, 0.17837, 0.26552, 0.47153, 0.19049, 0.36273, 0.31711, 0.28717, 0.44842, 0.24486, 0.58208, 0.42922, 0.69228, 0.58637, 0.77201, 0.73748, 0.83063, 0.87953, 0.88925], "triangles": [31, 10, 11, 31, 11, 12, 32, 31, 12, 32, 12, 13, 16, 30, 31, 15, 16, 31, 15, 31, 32, 14, 32, 13, 15, 32, 14, 29, 8, 9, 29, 9, 10, 29, 18, 28, 30, 29, 10, 30, 10, 31, 17, 18, 29, 17, 29, 30, 16, 17, 30, 3, 4, 5, 25, 2, 3, 25, 3, 5, 1, 2, 25, 0, 1, 25, 6, 25, 5, 26, 0, 25, 24, 0, 26, 7, 25, 6, 26, 25, 7, 26, 23, 24, 27, 23, 26, 8, 26, 7, 27, 26, 8, 22, 23, 27, 28, 22, 27, 21, 22, 28, 20, 21, 28, 8, 28, 27, 29, 28, 8, 19, 20, 28, 18, 19, 28], "vertices": [1, 22, 5.89, -8.96, 1, 1, 22, -1.26, -9.25, 1, 1, 22, -6.64, -4.19, 1, 1, 22, -10.07, 7.28, 1, 1, 22, -9.85, 13.31, 1, 1, 22, -4.9, 14.79, 1, 2, 22, 3.41, 12.16, 0.99969, 23, 1.45, 26.77, 0.00031, 2, 22, 11.06, 10.33, 0.95542, 23, 2.8, 19.02, 0.04458, 2, 22, 17.27, 9.49, 0.66855, 23, 4.49, 12.99, 0.33145, 3, 22, 21.96, 13.73, 0.14795, 23, 10.25, 10.36, 0.83861, 24, -7.16, 12.28, 0.01344, 3, 22, 25.59, 19.64, 0.00401, 23, 17.12, 9.38, 0.69661, 24, -0.69, 9.78, 0.29938, 2, 23, 24.53, 11.34, 0.14331, 24, 6.97, 10.04, 0.85669, 2, 23, 32.54, 10.26, 0.00235, 24, 14.54, 7.19, 0.99765, 1, 24, 18.94, 2.72, 1, 1, 24, 15.78, -2.72, 1, 1, 24, 9.67, -5.69, 1, 2, 23, 21.74, -7.34, 0.45893, 24, 0.08, -7.54, 0.54107, 3, 22, 40.12, 9.5, 0.03109, 23, 13.58, -7.98, 0.96007, 24, -8.03, -6.34, 0.00884, 2, 22, 37.29, 4.26, 0.3127, 23, 7.64, -7.46, 0.6873, 2, 22, 37.82, -3.25, 0.94447, 23, 0.97, -10.93, 0.05553, 1, 22, 35.88, -7.66, 1, 1, 22, 31.13, -10.78, 1, 1, 22, 23.25, -9.59, 1, 1, 22, 16.3, -9.11, 1, 1, 22, 10.72, -8.51, 1, 1, 22, 2.78, 2.88, 1, 2, 22, 11.22, 0.3, 0.99956, 23, -6.34, 14.9, 0.00044, 1, 22, 19.49, -0.78, 1, 1, 22, 27.46, -0.38, 1, 1, 23, 9.94, 0.16, 1, 2, 23, 18.33, 1.17, 0.90659, 24, -1.35, 1.51, 0.09341, 2, 23, 25.73, 2.94, 0.01636, 24, 6.26, 1.58, 0.98364, 1, 24, 13.49, 1.46, 1], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48, 6, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64], "width": 45, "height": 58}}, "piaodai1": {"piaodai1": {"type": "mesh", "uvs": [0.7162, 0, 0.71483, 0.10709, 0.72852, 0.20004, 0.81068, 0.24355, 0.90515, 0.30684, 0.97909, 0.45715, 1, 0.63515, 0.98457, 0.81513, 0.90241, 0.93775, 0.7874, 1, 0.63542, 0.97137, 0.49712, 0.88435, 0.39717, 0.75975, 0.32871, 0.61933, 0.25203, 0.57384, 0.16988, 0.59758, 0.11785, 0.73009, 0.09731, 0.8804, 0.08225, 1, 0.04391, 1, 0.00283, 0.96742, 0, 0.83886, 0, 0.67075, 0.02337, 0.50462, 0.11511, 0.35233, 0.22602, 0.29893, 0.35335, 0.3108, 0.48069, 0.40573, 0.58612, 0.49869, 0.67923, 0.58175, 0.74358, 0.59362, 0.75317, 0.53429, 0.69292, 0.45913, 0.61077, 0.37804, 0.57517, 0.2574, 0.59023, 0.10907, 0.63678, 0.02402, 0.6806, 0, 0.66828, 0.11697, 0.66417, 0.2752, 0.7518, 0.3642, 0.84217, 0.4532, 0.88325, 0.62526, 0.82574, 0.78151, 0.6806, 0.77557, 0.55052, 0.69646, 0.44099, 0.58966, 0.36157, 0.47297, 0.25066, 0.42551, 0.13291, 0.45913, 0.07677, 0.58966, 0.05349, 0.74393, 0.05212, 0.86853], "triangles": [17, 18, 52, 18, 19, 52, 19, 20, 52, 20, 21, 52, 52, 51, 17, 17, 51, 16, 52, 21, 51, 21, 22, 51, 16, 51, 50, 51, 22, 50, 16, 50, 15, 22, 23, 50, 11, 12, 45, 12, 46, 45, 12, 13, 46, 45, 46, 28, 13, 47, 46, 13, 14, 47, 14, 15, 49, 46, 27, 28, 46, 47, 27, 15, 50, 49, 50, 23, 49, 49, 48, 14, 14, 48, 47, 23, 24, 49, 48, 26, 47, 47, 26, 27, 49, 24, 48, 24, 25, 48, 48, 25, 26, 10, 44, 9, 9, 43, 8, 9, 44, 43, 10, 11, 44, 8, 43, 7, 11, 45, 44, 43, 42, 7, 7, 42, 6, 44, 30, 43, 43, 30, 42, 45, 29, 44, 44, 29, 30, 45, 28, 29, 42, 5, 6, 30, 31, 42, 31, 41, 42, 42, 41, 5, 31, 40, 41, 41, 4, 5, 41, 3, 4, 32, 40, 31, 33, 39, 32, 32, 39, 40, 40, 3, 41, 33, 34, 39, 40, 39, 2, 2, 39, 38, 38, 1, 2, 40, 2, 3, 39, 34, 38, 34, 35, 38, 35, 36, 38, 38, 37, 1, 38, 36, 37, 1, 37, 0], "vertices": [1, 46, 5.25, -5.31, 1, 2, 46, 0.45, -4.87, 0.98279, 45, 31.4, 4.4, 0.01721, 2, 46, -3.79, -5.44, 0.85625, 45, 27.17, 3.83, 0.14375, 2, 46, -6.14, -10.62, 0.61515, 45, 24.81, -1.35, 0.38485, 2, 46, -9.44, -16.53, 0.42686, 45, 21.52, -7.26, 0.57314, 2, 46, -16.54, -20.82, 0.25078, 45, 14.41, -11.55, 0.74922, 2, 46, -24.63, -21.57, 0.10757, 45, 6.32, -12.31, 0.89243, 2, 46, -32.63, -19.97, 0.02083, 45, -1.68, -10.7, 0.97917, 1, 45, -6.78, -4.96, 1, 2, 45, -9.02, 2.7, 0.97311, 44, -26, -26.28, 0.02689, 2, 45, -6.99, 12.46, 0.79486, 44, -23.98, -16.53, 0.20514, 2, 45, -2.42, 21.13, 0.52081, 44, -19.4, -7.86, 0.47919, 2, 45, 3.66, 27.19, 0.27002, 44, -13.32, -1.8, 0.72998, 3, 45, 10.29, 31.15, 0.05829, 44, -6.69, 2.17, 0.92863, 42, 11.71, -18.66, 0.01307, 3, 45, 12.71, 35.97, 0.00159, 44, -4.27, 6.98, 0.87825, 42, 14.13, -13.84, 0.12016, 2, 44, -4.94, 12.39, 0.62243, 42, 13.46, -8.43, 0.37757, 2, 44, -10.63, 16.21, 0.25745, 42, 7.77, -4.62, 0.74255, 2, 44, -17.28, 18.05, 0.02164, 42, 1.12, -2.78, 0.97836, 1, 42, -4.17, -1.4, 1, 1, 42, -3.98, 1.09, 1, 1, 42, -2.32, 3.64, 1, 2, 44, -14.94, 24.21, 0.04958, 42, 3.46, 3.39, 0.95042, 2, 44, -7.4, 23.65, 0.27968, 42, 11, 2.82, 0.72032, 2, 44, -0.05, 21.57, 0.51564, 42, 18.35, 0.75, 0.48436, 2, 44, 6.33, 15.11, 0.74533, 42, 24.73, -5.71, 0.25467, 2, 44, 8.19, 7.75, 0.89181, 42, 26.59, -13.08, 0.10819, 3, 45, 24.02, 28.52, 0.00012, 44, 7.04, -0.47, 0.99006, 42, 25.44, -21.29, 0.00982, 2, 45, 19.14, 20.58, 0.10715, 44, 2.16, -8.4, 0.89285, 3, 46, -16.5, 4.79, 0.00192, 45, 14.46, 14.06, 0.37033, 44, -2.52, -14.92, 0.62775, 3, 46, -20.68, -0.96, 0.02909, 45, 10.28, 8.31, 0.66177, 44, -6.7, -20.68, 0.30913, 3, 46, -21.52, -5.09, 0.13135, 45, 9.43, 4.17, 0.76871, 44, -7.55, -24.81, 0.09993, 3, 46, -18.91, -5.91, 0.32334, 45, 12.05, 3.35, 0.64982, 44, -4.93, -25.63, 0.02684, 3, 46, -15.24, -2.26, 0.62178, 45, 15.71, 7.01, 0.37554, 44, -1.27, -21.98, 0.00268, 3, 46, -11.2, 2.79, 0.83465, 45, 19.75, 12.06, 0.16535, 44, 2.77, -16.93, 0, 2, 46, -5.62, 4.69, 0.94106, 45, 25.34, 13.96, 0.05894, 2, 46, 0.97, 3.22, 0.99985, 45, 31.92, 12.48, 0.00015, 1, 46, 4.56, -0.09, 1, 1, 46, 5.42, -3.01, 1, 2, 46, 0.23, -1.82, 0.9924, 45, 31.19, 7.45, 0.0076, 2, 46, -6.85, -1.02, 0.86756, 45, 24.11, 8.25, 0.13244, 3, 46, -11.27, -6.4, 0.62055, 45, 19.69, 2.87, 0.37863, 44, 2.71, -26.11, 0.00082, 3, 46, -15.7, -11.95, 0.36247, 45, 15.25, -2.69, 0.63486, 44, -1.73, -31.67, 0.00266, 3, 46, -23.62, -14.04, 0.12801, 45, 7.33, -4.77, 0.86923, 44, -9.65, -33.76, 0.00276, 2, 46, -30.35, -9.78, 0.00462, 45, 0.6, -0.52, 0.99538, 3, 46, -29.38, -0.4, 0.00592, 45, 1.57, 8.87, 0.79345, 44, -15.41, -20.12, 0.20064, 3, 46, -25.2, 7.77, 0.00071, 45, 5.76, 17.03, 0.47127, 44, -11.23, -11.95, 0.52802, 2, 45, 11.08, 23.78, 0.17446, 44, -5.9, -5.21, 0.82554, 2, 45, 16.7, 28.53, 0.00481, 44, -0.28, -0.45, 0.99519, 2, 44, 2.39, 6.57, 0.90085, 42, 20.79, -14.25, 0.09915, 2, 44, 1.45, 14.32, 0.69637, 42, 19.85, -6.5, 0.30363, 2, 44, -4.13, 18.4, 0.45725, 42, 14.27, -2.43, 0.54275, 2, 44, -10.94, 20.43, 0.18382, 42, 7.46, -0.4, 0.81618, 2, 44, -16.52, 20.93, 0.02364, 42, 1.88, 0.11, 0.97636], "hull": 38, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 0, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104], "width": 65, "height": 45}}, "piaodai2": {"piaodai2": {"type": "mesh", "uvs": [0.06903, 0, 0.12632, 0.11208, 0.19537, 0.19787, 0.3129, 0.15083, 0.451, 0.04566, 0.57882, 0.00138, 0.73895, 0.02352, 0.89027, 0.11762, 0.98283, 0.29473, 1, 0.54934, 0.95932, 0.67941, 0.9226, 0.73199, 0.94316, 0.87036, 0.927, 0.98106, 0.8844, 1, 0.84914, 0.93678, 0.808, 0.78734, 0.80947, 0.62406, 0.86089, 0.47738, 0.82857, 0.4165, 0.74336, 0.39436, 0.62289, 0.43864, 0.50242, 0.52996, 0.38048, 0.59638, 0.22182, 0.59085, 0.10429, 0.53273, 0.01173, 0.34731, 0, 0.16189, 0.02642, 0.04843, 0.08519, 0.27536, 0.17921, 0.38329, 0.32613, 0.38329, 0.46422, 0.2892, 0.6082, 0.21724, 0.75511, 0.22278, 0.87411, 0.26706, 0.93288, 0.41373, 0.91525, 0.57978, 0.86089, 0.72645, 0.8844, 0.88973], "triangles": [26, 29, 25, 25, 29, 30, 30, 29, 2, 26, 27, 29, 1, 29, 28, 28, 29, 27, 29, 1, 2, 1, 28, 0, 24, 31, 23, 22, 23, 32, 25, 30, 24, 24, 30, 31, 23, 31, 32, 22, 32, 21, 30, 2, 31, 2, 3, 31, 31, 3, 32, 3, 4, 32, 33, 32, 4, 37, 18, 36, 36, 8, 9, 18, 19, 36, 32, 33, 21, 21, 33, 20, 19, 35, 36, 35, 19, 34, 36, 35, 8, 19, 20, 34, 20, 33, 34, 35, 7, 8, 33, 4, 5, 35, 34, 7, 33, 6, 34, 34, 6, 7, 33, 5, 6, 15, 39, 14, 14, 39, 13, 13, 39, 12, 15, 16, 39, 16, 38, 39, 39, 11, 12, 39, 38, 11, 16, 17, 38, 10, 11, 37, 11, 38, 37, 38, 17, 37, 10, 37, 9, 17, 18, 37, 37, 36, 9], "vertices": [2, 51, 17.73, 21.61, 0.00016, 50, 2.61, -1.24, 0.99984, 2, 51, 12.58, 17.34, 0.15811, 50, -2.54, -5.51, 0.84189, 2, 51, 8.48, 12.04, 0.54409, 50, -6.64, -10.81, 0.45591, 3, 52, 3.8, 33.36, 0.03018, 51, 9.79, 2.4, 0.90935, 50, -5.34, -20.45, 0.06047, 2, 52, 7.48, 21.87, 0.32296, 51, 13.46, -9.09, 0.67704, 2, 52, 8.6, 11.4, 0.68262, 51, 14.58, -19.56, 0.31738, 3, 43, 38.98, 9.8, 0.00207, 52, 6.68, -1.46, 0.99007, 51, 12.66, -32.42, 0.00785, 2, 43, 34.03, -2.12, 0.13346, 52, 1.73, -13.38, 0.86654, 2, 43, 25.88, -9.03, 0.32111, 52, -6.42, -20.29, 0.67889, 2, 43, 14.85, -9.59, 0.54688, 52, -17.45, -20.86, 0.45312, 2, 43, 9.52, -5.89, 0.6881, 52, -22.78, -17.15, 0.3119, 2, 43, 7.49, -2.75, 0.82274, 52, -24.81, -14.02, 0.17726, 2, 43, 1.43, -3.97, 0.98513, 52, -30.87, -15.23, 0.01487, 1, 43, -3.22, -2.31, 1, 1, 43, -3.77, 1.19, 1, 2, 43, -0.84, 3.84, 0.99363, 52, -33.15, -7.42, 0.00637, 2, 43, 5.81, 6.68, 0.90424, 52, -26.49, -4.58, 0.09576, 2, 43, 12.8, 6.04, 0.76373, 52, -19.5, -5.23, 0.23627, 2, 43, 18.78, 1.41, 0.46127, 52, -13.52, -9.85, 0.53873, 3, 43, 21.59, 3.83, 0.23296, 52, -10.71, -7.44, 0.767, 51, -4.73, -38.4, 5e-05, 3, 43, 23.05, 10.64, 0.04267, 52, -9.25, -0.62, 0.92688, 51, -3.26, -31.59, 0.03045, 3, 43, 21.89, 20.51, 1e-05, 52, -10.41, 9.25, 0.66866, 51, -4.43, -21.71, 0.33133, 2, 52, -13.6, 19.27, 0.24839, 51, -7.62, -11.69, 0.75161, 3, 52, -15.71, 29.34, 0.02443, 51, -9.73, -1.63, 0.96924, 50, -24.85, -24.48, 0.00633, 2, 51, -8.53, 11.17, 0.77514, 50, -23.65, -11.68, 0.22486, 2, 51, -5.33, 20.48, 0.49557, 50, -20.45, -2.38, 0.50443, 2, 51, 3.18, 27.36, 0.19955, 50, -11.94, 4.5, 0.80045, 2, 51, 11.21, 27.71, 0.04485, 50, -3.91, 4.86, 0.95515, 1, 50, 0.79, 2.36, 1, 2, 51, 5.83, 21.19, 0.23627, 50, -9.3, -1.66, 0.76373, 2, 51, 0.63, 13.94, 0.59199, 50, -14.49, -8.91, 0.40801, 2, 51, -0.26, 2.08, 0.96942, 50, -15.38, -20.77, 0.03058, 2, 52, -3.05, 21.58, 0.25239, 51, 2.94, -9.38, 0.74761, 2, 52, -0.83, 9.72, 0.72346, 51, 5.15, -21.24, 0.27654, 3, 43, 30.34, 9.14, 0.02275, 52, -1.96, -2.13, 0.97605, 51, 4.02, -33.09, 0.00121, 2, 43, 27.72, -0.33, 0.19176, 52, -4.58, -11.6, 0.80824, 2, 43, 21.08, -4.61, 0.39984, 52, -11.22, -15.87, 0.60016, 2, 43, 14.06, -2.65, 0.62178, 52, -18.24, -13.91, 0.37822, 2, 43, 8.1, 2.21, 0.84969, 52, -24.2, -9.05, 0.15031, 2, 43, 0.96, 0.84, 0.99068, 52, -31.34, -10.42, 0.00932], "hull": 29, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 0, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78], "width": 81, "height": 43}}, "piaodai3": {"piaodai3": {"type": "mesh", "uvs": [0.03113, 0.10782, 0.0657, 0.16282, 0.09294, 0.29299, 0.12542, 0.44882, 0.16837, 0.59549, 0.22389, 0.71099, 0.26685, 0.78799, 0.30561, 0.79532, 0.31608, 0.74216, 0.30037, 0.61932, 0.29827, 0.45249, 0.30875, 0.27466, 0.36008, 0.12249, 0.45113, 0.02587, 0.53532, 0, 0.65418, 0.02587, 0.73833, 0.0952, 0.79234, 0.23965, 0.83514, 0.41442, 0.83616, 0.55887, 0.85042, 0.68905, 0.89526, 0.77109, 0.94316, 0.74612, 1, 0.69467, 1, 0.7778, 0.97972, 0.8767, 0.93221, 0.94837, 0.85522, 0.96127, 0.7725, 0.9197, 0.69633, 0.8165, 0.65947, 0.6703, 0.63326, 0.53414, 0.60378, 0.40657, 0.55054, 0.37217, 0.50549, 0.38651, 0.47273, 0.47251, 0.47109, 0.60867, 0.4629, 0.7477, 0.43915, 0.87097, 0.40557, 0.97274, 0.33104, 1, 0.24258, 0.98564, 0.16641, 0.91684, 0.11399, 0.80934, 0.07467, 0.6789, 0.04355, 0.54274, 0.0198, 0.41804, 0.00669, 0.30051, 0, 0.20447, 0.00751, 0.13567, 0.037, 0.19444, 0.04683, 0.30481, 0.06239, 0.41661, 0.09269, 0.53414, 0.12382, 0.63734, 0.15986, 0.74197, 0.20245, 0.82367, 0.25486, 0.88387, 0.31465, 0.8939, 0.37363, 0.8423, 0.39001, 0.74197, 0.38919, 0.6187, 0.38509, 0.46534, 0.39656, 0.32631, 0.44734, 0.24604, 0.52903, 0.20951, 0.62159, 0.21667, 0.69039, 0.29981, 0.7338, 0.47181, 0.74462, 0.60584, 0.77538, 0.74468, 0.82233, 0.81693, 0.88386, 0.85376, 0.94538, 0.82401, 0.97938, 0.76168], "triangles": [50, 0, 1, 49, 0, 50, 48, 49, 50, 50, 1, 2, 47, 48, 50, 51, 47, 50, 2, 51, 50, 52, 51, 2, 46, 47, 51, 46, 51, 52, 52, 2, 3, 53, 52, 3, 45, 46, 52, 45, 52, 53, 53, 3, 4, 44, 45, 53, 54, 53, 4, 44, 53, 54, 37, 60, 61, 55, 54, 4, 55, 4, 5, 8, 9, 61, 8, 61, 60, 55, 43, 44, 55, 44, 54, 56, 55, 5, 56, 5, 6, 59, 8, 60, 7, 8, 59, 38, 60, 37, 59, 60, 38, 57, 56, 6, 58, 7, 59, 57, 6, 7, 58, 57, 7, 56, 43, 55, 42, 43, 56, 39, 59, 38, 58, 59, 39, 57, 42, 56, 41, 42, 57, 40, 58, 39, 41, 57, 58, 40, 41, 58, 65, 13, 14, 66, 65, 14, 15, 66, 14, 64, 12, 13, 64, 13, 65, 63, 11, 12, 64, 63, 12, 33, 65, 66, 34, 64, 65, 33, 34, 65, 63, 64, 34, 32, 33, 66, 10, 11, 63, 62, 10, 63, 35, 62, 63, 34, 35, 63, 35, 61, 62, 36, 61, 35, 9, 10, 62, 9, 62, 61, 36, 37, 61, 66, 15, 16, 67, 66, 16, 67, 16, 17, 32, 66, 67, 68, 67, 17, 68, 17, 18, 32, 67, 68, 31, 32, 68, 68, 18, 19, 69, 68, 19, 31, 68, 69, 30, 31, 69, 69, 19, 20, 70, 69, 20, 30, 69, 70, 29, 30, 70, 71, 70, 20, 28, 29, 70, 74, 22, 23, 74, 23, 24, 71, 20, 21, 73, 22, 74, 21, 22, 73, 72, 71, 21, 72, 21, 73, 24, 73, 74, 25, 73, 24, 28, 70, 71, 26, 72, 73, 26, 73, 25, 27, 71, 72, 27, 72, 26, 28, 71, 27], "vertices": [1, 53, 7.62, -0.59, 1, 1, 53, 4.06, -3.96, 1, 2, 59, 37.37, 13.66, 0.06432, 53, -3.94, -6.23, 0.93568, 2, 59, 27.79, 10.96, 0.31716, 53, -13.52, -8.93, 0.68284, 2, 59, 18.68, 7.12, 0.63705, 53, -22.63, -12.77, 0.36295, 3, 60, -25.64, 25.27, 0.00043, 59, 11.33, 1.82, 0.87374, 53, -29.98, -18.06, 0.12583, 3, 60, -30.59, 21.12, 0.03254, 59, 6.39, -2.33, 0.94457, 53, -34.93, -22.21, 0.02289, 3, 60, -31.33, 17.1, 0.16173, 59, 5.65, -6.35, 0.83668, 53, -35.67, -26.24, 0.00159, 3, 60, -28.23, 15.76, 0.33313, 59, 8.74, -7.69, 0.66686, 53, -32.57, -27.58, 1e-05, 2, 60, -20.76, 16.86, 0.57664, 59, 16.22, -6.59, 0.42336, 2, 60, -10.76, 16.33, 0.79584, 59, 26.22, -7.12, 0.20416, 2, 60, -0.2, 14.43, 0.94483, 59, 36.77, -9.02, 0.05517, 3, 61, 23.48, 38.14, 3e-05, 60, 8.5, 8.37, 0.99736, 59, 45.48, -15.08, 0.0026, 2, 61, 28.55, 28.18, 0.05921, 60, 13.56, -1.59, 0.94079, 2, 61, 29.43, 19.25, 0.2011, 60, 14.45, -10.52, 0.7989, 2, 61, 26.95, 6.92, 0.48765, 60, 11.97, -22.85, 0.51235, 2, 61, 22.14, -1.58, 0.68214, 60, 7.16, -31.35, 0.31786, 2, 61, 13.08, -6.59, 0.86932, 60, -1.91, -36.36, 0.13068, 3, 62, 24.55, 9.87, 0.01392, 61, 2.28, -10.29, 0.98061, 60, -12.7, -40.06, 0.00546, 2, 62, 15.89, 10.41, 0.14854, 61, -6.37, -9.74, 0.85146, 2, 62, 7.99, 9.5, 0.46312, 61, -14.27, -10.65, 0.53688, 2, 62, 2.73, 5.17, 0.83885, 61, -19.53, -14.98, 0.16115, 2, 62, 3.85, 0.04, 0.99458, 61, -18.41, -20.11, 0.00542, 2, 62, 6.48, -6.14, 0.99998, 61, -15.78, -26.29, 2e-05, 2, 62, 1.51, -5.76, 1, 61, -20.75, -25.92, 0, 1, 62, -4.25, -3.2, 1, 2, 62, -8.16, 2.1, 0.9584, 61, -30.43, -18.05, 0.0416, 2, 62, -8.33, 10.22, 0.8145, 61, -30.59, -9.94, 0.1855, 2, 62, -5.2, 18.69, 0.59296, 61, -27.46, -1.46, 0.40704, 2, 62, 1.58, 26.2, 0.35517, 61, -20.68, 6.05, 0.64483, 3, 62, 10.61, 29.41, 0.153, 61, -11.65, 9.26, 0.84487, 60, -26.63, -20.52, 0.00214, 3, 62, 18.97, 31.54, 0.02586, 61, -3.29, 11.39, 0.90974, 60, -18.28, -18.38, 0.0644, 4, 62, 26.83, 34.06, 6e-05, 61, 4.57, 13.9, 0.6601, 60, -10.41, -15.87, 0.33982, 59, 26.56, -39.32, 2e-05, 3, 61, 7.05, 19.32, 0.32456, 60, -7.94, -10.45, 0.67079, 59, 29.04, -33.9, 0.00465, 3, 61, 6.54, 24.1, 0.10291, 60, -8.44, -5.67, 0.85897, 59, 28.54, -29.12, 0.03812, 3, 61, 1.65, 27.92, 0.00905, 60, -13.33, -1.85, 0.81816, 59, 23.65, -25.3, 0.17279, 2, 60, -21.46, -1.07, 0.61166, 59, 15.51, -24.52, 0.38834, 2, 60, -29.72, 0.41, 0.41428, 59, 7.26, -23.04, 0.58572, 2, 60, -36.91, 3.45, 0.25343, 59, 0.07, -20, 0.74657, 2, 60, -42.73, 7.43, 0.15567, 59, -5.75, -16.02, 0.84433, 2, 60, -43.78, 15.35, 0.07355, 59, -6.8, -8.1, 0.92645, 2, 60, -42.22, 24.55, 0.00071, 59, -5.25, 1.1, 0.99929, 2, 59, -0.53, 8.77, 0.95261, 53, -41.85, -11.12, 0.04739, 2, 59, 6.31, 13.77, 0.82849, 53, -35, -6.11, 0.17151, 2, 59, 14.43, 17.3, 0.6259, 53, -26.89, -2.58, 0.3741, 2, 59, 22.82, 19.95, 0.37637, 53, -18.5, 0.06, 0.62363, 2, 59, 30.46, 21.88, 0.1649, 53, -10.85, 1.99, 0.8351, 2, 59, 37.6, 22.72, 0.03104, 53, -3.72, 2.84, 0.96896, 1, 53, 2.08, 3.11, 1, 1, 53, 6.14, 2.01, 1, 1, 53, 2.39, -0.81, 1, 2, 59, 37.03, 18.54, 0.04825, 53, -4.29, -1.35, 0.95175, 2, 59, 30.22, 17.41, 0.19533, 53, -11.1, -2.47, 0.80467, 2, 59, 22.95, 14.77, 0.41941, 53, -18.37, -5.12, 0.58059, 2, 59, 16.53, 11.97, 0.629, 53, -24.79, -7.91, 0.371, 2, 59, 9.98, 8.67, 0.81603, 53, -31.33, -11.22, 0.18397, 3, 60, -32.22, 28.03, 0.00022, 59, 4.76, 4.58, 0.93676, 53, -36.55, -15.31, 0.06302, 3, 60, -36.23, 22.81, 0.00482, 59, 0.75, -0.64, 0.99422, 53, -40.57, -20.53, 0.00096, 2, 60, -37.3, 16.59, 0.08478, 59, -0.32, -6.86, 0.91522, 2, 60, -34.68, 10.19, 0.22184, 59, 2.3, -13.26, 0.77816, 2, 60, -28.8, 8.02, 0.38062, 59, 8.18, -15.43, 0.61938, 2, 60, -21.42, 7.55, 0.57691, 59, 15.56, -15.9, 0.42309, 2, 60, -12.21, 7.29, 0.79685, 59, 24.77, -16.16, 0.20315, 2, 60, -3.98, 5.47, 0.94364, 59, 32.99, -17.98, 0.05636, 2, 61, 15.4, 29.56, 0.00328, 60, 0.42, -0.21, 0.99672, 3, 61, 16.95, 20.84, 0.19671, 60, 1.96, -8.93, 0.80326, 59, 38.94, -32.38, 3e-05, 2, 61, 15.79, 11.19, 0.50428, 60, 0.81, -18.58, 0.49572, 2, 61, 10.28, 4.36, 0.77463, 60, -4.71, -25.42, 0.22537, 3, 62, 21.91, 20.74, 0.00156, 61, -0.35, 0.58, 0.99691, 60, -15.34, -29.19, 0.00153, 2, 62, 13.8, 20.2, 0.14526, 61, -8.46, 0.05, 0.85474, 2, 62, 5.25, 17.61, 0.40264, 61, -17.01, -2.55, 0.59736, 2, 62, 0.56, 13.01, 0.6262, 61, -21.7, -7.14, 0.3738, 2, 62, -2.12, 6.74, 0.85562, 61, -24.39, -13.42, 0.14438, 2, 62, -0.83, 0.16, 0.99755, 61, -23.09, -19.99, 0.00245, 1, 62, 2.64, -3.68, 1], "hull": 50, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 0, 98, 0, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148], "width": 105, "height": 60}}, "piaodai4": {"piaodai4": {"type": "mesh", "uvs": [1, 0.10371, 0.95363, 0.10125, 0.90128, 0.16774, 0.80767, 0.21207, 0.70772, 0.31057, 0.65061, 0.45095, 0.60301, 0.60117, 0.56017, 0.75386, 0.50623, 0.84005, 0.47133, 0.82774, 0.46181, 0.76124, 0.49354, 0.59871, 0.54907, 0.45833, 0.55383, 0.28841, 0.51099, 0.12341, 0.40945, 0.01751, 0.2984, 0, 0.16513, 0.00766, 0.062, 0.11602, 0.01441, 0.25639, 0, 0.41647, 0, 0.53714, 0.03662, 0.63565, 0.08897, 0.68736, 0.1096, 0.60363, 0.14768, 0.45587, 0.21907, 0.36229, 0.29046, 0.33274, 0.33806, 0.35983, 0.34758, 0.44848, 0.33013, 0.56669, 0.31743, 0.70706, 0.33013, 0.83266, 0.38566, 0.94594, 0.4634, 0.99274, 0.53955, 0.99027, 0.61729, 0.93363, 0.66013, 0.79818, 0.69503, 0.67012, 0.74263, 0.52975, 0.78864, 0.41893, 0.84416, 0.36229, 0.91873, 0.32289, 0.98061, 0.26378, 1, 0.19236, 0.96668, 0.1753, 0.90072, 0.23798, 0.81456, 0.29022, 0.74456, 0.38007, 0.6988, 0.48664, 0.65168, 0.63918, 0.60995, 0.78127, 0.55341, 0.88783, 0.47668, 0.915, 0.39995, 0.85022, 0.38649, 0.71231, 0.41341, 0.56186, 0.4363, 0.43649, 0.43091, 0.28813, 0.35687, 0.18157, 0.23303, 0.16485, 0.12533, 0.24843, 0.07014, 0.39052, 0.06341, 0.54933, 0.07553, 0.62455], "triangles": [60, 17, 16, 61, 18, 17, 60, 61, 17, 19, 18, 61, 26, 61, 60, 26, 60, 27, 62, 19, 61, 25, 62, 61, 20, 19, 62, 26, 25, 61, 63, 21, 20, 62, 63, 20, 63, 62, 25, 24, 63, 25, 64, 63, 24, 22, 21, 63, 22, 63, 64, 23, 64, 24, 22, 64, 23, 59, 16, 15, 59, 15, 14, 60, 16, 59, 58, 59, 14, 58, 14, 13, 27, 60, 59, 28, 27, 59, 58, 28, 59, 57, 58, 13, 28, 58, 57, 29, 28, 57, 12, 57, 13, 56, 29, 57, 30, 29, 56, 11, 57, 12, 56, 57, 11, 55, 30, 56, 55, 56, 11, 31, 30, 55, 10, 55, 11, 32, 31, 55, 54, 55, 10, 54, 10, 9, 32, 55, 54, 52, 8, 7, 52, 7, 51, 53, 9, 8, 53, 8, 52, 54, 9, 53, 36, 51, 37, 52, 51, 36, 33, 32, 54, 33, 54, 53, 35, 53, 52, 35, 52, 36, 34, 33, 53, 34, 53, 35, 47, 4, 3, 48, 4, 47, 40, 48, 47, 41, 40, 47, 5, 4, 48, 49, 5, 48, 39, 49, 48, 39, 48, 40, 49, 6, 5, 50, 49, 39, 50, 6, 49, 38, 50, 39, 51, 7, 6, 50, 51, 6, 37, 51, 50, 38, 37, 50, 45, 1, 0, 2, 1, 45, 45, 0, 44, 46, 3, 2, 46, 2, 45, 43, 45, 44, 46, 45, 43, 47, 3, 46, 42, 46, 43, 41, 47, 46, 42, 41, 46], "vertices": [1, 54, 4.4, -3.38, 1, 2, 54, 4.93, 1.42, 0.99886, 55, 27.11, -30.87, 0.00114, 2, 54, 0.89, 7.18, 0.9046, 55, 23.07, -25.11, 0.0954, 2, 54, -1.34, 17.11, 0.60511, 55, 20.84, -15.18, 0.39489, 2, 54, -7.15, 27.97, 0.25929, 55, 15.03, -4.32, 0.74071, 2, 54, -16.08, 34.6, 0.04067, 55, 6.1, 2.31, 0.95933, 2, 55, -3.57, 8, 0.87299, 56, 18.08, -17.44, 0.12701, 2, 55, -13.43, 13.21, 0.47396, 56, 8.21, -12.23, 0.52604, 3, 55, -18.77, 19.23, 0.13605, 56, 2.87, -6.21, 0.86251, 57, -41.09, -2.65, 0.00143, 3, 55, -17.68, 22.79, 0.02175, 56, 3.97, -2.65, 0.94119, 57, -40, 0.91, 0.03706, 3, 55, -13.16, 23.45, 0.00035, 56, 8.48, -2, 0.82376, 57, -35.48, 1.56, 0.17589, 2, 56, 19.1, -6.1, 0.44773, 57, -24.87, -2.54, 0.55227, 2, 56, 28.04, -12.56, 0.18821, 57, -15.92, -9, 0.81179, 2, 56, 39.36, -13.91, 0.04324, 57, -4.61, -10.35, 0.95676, 2, 57, 6.75, -6.73, 0.99635, 58, 11.47, -45.04, 0.00365, 2, 57, 14.62, 3.27, 0.88124, 58, 19.33, -35.04, 0.11876, 2, 57, 16.65, 14.7, 0.64898, 58, 21.37, -23.61, 0.35102, 2, 57, 17.18, 28.56, 0.33684, 58, 21.89, -9.75, 0.66316, 2, 57, 10.74, 39.79, 0.11734, 58, 15.46, 1.49, 0.88266, 2, 57, 1.73, 45.43, 0.01152, 58, 6.45, 7.13, 0.98848, 1, 58, -4.14, 9.42, 1, 1, 58, -12.2, 10.03, 1, 1, 58, -19.06, 6.72, 1, 1, 58, -22.93, 1.55, 1, 1, 58, -17.49, -1, 1, 2, 57, -12.63, 32.61, 0.02618, 58, -7.92, -5.69, 0.97382, 3, 56, 37.03, 21.18, 0.00115, 57, -6.94, 24.74, 0.26759, 58, -2.22, -13.57, 0.73126, 3, 56, 38.45, 13.63, 0.01974, 57, -5.52, 17.19, 0.60104, 58, -0.8, -21.12, 0.37922, 3, 56, 36.27, 8.83, 0.08987, 57, -7.7, 12.39, 0.77276, 58, -2.98, -25.92, 0.13738, 3, 56, 30.27, 8.28, 0.26797, 57, -13.7, 11.84, 0.70407, 58, -8.98, -26.46, 0.02796, 3, 56, 22.51, 10.69, 0.51182, 57, -21.46, 14.25, 0.48635, 58, -16.74, -24.06, 0.00183, 2, 56, 13.23, 12.71, 0.74314, 57, -30.74, 16.26, 0.25686, 2, 56, 4.74, 12.02, 0.89134, 57, -39.23, 15.58, 0.10866, 2, 56, -3.26, 6.83, 0.98965, 57, -47.23, 10.39, 0.01035, 2, 55, -28.64, 24.44, 0.01996, 56, -6.99, -1, 0.98004, 2, 55, -29.07, 16.53, 0.12546, 56, -7.42, -8.91, 0.87454, 2, 55, -25.89, 8.18, 0.29409, 56, -4.24, -17.26, 0.70591, 2, 55, -17.17, 3.06, 0.57081, 56, 4.47, -22.38, 0.42919, 2, 55, -8.89, -1.2, 0.86806, 56, 12.76, -26.64, 0.13194, 3, 54, -22.06, 25.45, 0.05082, 55, 0.12, -6.84, 0.94887, 56, 21.77, -32.28, 0.00031, 2, 54, -15.02, 20.12, 0.29145, 55, 7.17, -12.16, 0.70855, 2, 54, -11.66, 14.08, 0.56711, 55, 10.52, -18.21, 0.43289, 2, 54, -9.61, 6.15, 0.83372, 55, 12.57, -26.14, 0.16628, 2, 54, -6.14, -0.56, 0.96833, 55, 16.04, -32.85, 0.03167, 2, 54, -1.52, -2.93, 0.99924, 55, 20.66, -35.22, 0.00076, 2, 54, -0.12, 0.44, 0.99768, 55, 22.06, -31.85, 0.00232, 2, 54, -3.8, 7.59, 0.85125, 55, 18.38, -24.7, 0.14875, 2, 54, -6.62, 16.79, 0.55037, 55, 15.56, -15.5, 0.44963, 2, 54, -12.08, 24.5, 0.24536, 55, 10.1, -7.79, 0.75464, 2, 54, -18.84, 29.78, 0.04858, 55, 3.34, -2.51, 0.95142, 2, 55, -6.48, 3.14, 0.86812, 56, 15.16, -22.3, 0.13188, 2, 55, -15.65, 8.18, 0.52263, 56, 5.99, -17.26, 0.47737, 2, 55, -22.33, 14.58, 0.22091, 56, -0.69, -10.86, 0.77909, 2, 55, -23.55, 22.67, 0.03489, 56, -1.9, -2.77, 0.96511, 2, 56, 3.02, 4.87, 0.94505, 57, -40.94, 8.42, 0.05495, 2, 56, 12.34, 5.57, 0.74621, 57, -31.63, 9.13, 0.25379, 3, 56, 22.18, 2.02, 0.45502, 57, -21.78, 5.58, 0.54388, 58, -17.07, -32.72, 0.0011, 3, 56, 30.38, -0.98, 0.22157, 57, -13.58, 2.58, 0.77149, 58, -8.87, -35.72, 0.00694, 3, 56, 40.33, -1.16, 0.0372, 57, -3.63, 2.4, 0.94357, 58, 1.09, -35.91, 0.01923, 3, 56, 48.03, 5.98, 0.00357, 57, 4.06, 9.54, 0.8147, 58, 8.78, -28.76, 0.18173, 3, 56, 50.11, 18.74, 0.00016, 57, 6.15, 22.3, 0.44391, 58, 10.86, -16, 0.55593, 2, 57, 1.4, 33.89, 0.11209, 58, 6.12, -4.42, 0.88791, 1, 58, -2.95, 2.02, 1, 1, 58, -13.51, 3.51, 1, 1, 58, -18.63, 2.63, 1], "hull": 45, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 0, 88, 0, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128], "width": 104, "height": 67}}, "guanglun": {"guanglun": {"x": 2.21, "y": 7.73, "width": 244, "height": 236}}, "s1": {"s1": {"type": "mesh", "uvs": [0.6653, 0.44466, 0.70223, 0.37072, 0.74069, 0.32196, 0.833, 0.31567, 0.91761, 0.33454, 0.96684, 0.40219, 1, 0.52488, 0.973, 0.57836, 0.91146, 0.69949, 0.8453, 0.80174, 0.76684, 0.91971, 0.68992, 1, 0.55761, 0.97477, 0.39816, 0.86918, 0.26642, 0.7272, 0.17919, 0.61071, 0.14003, 0.51424, 0.09374, 0.47601, 0.0369, 0.37029, 0, 0.30449, 0, 0.17495, 0.08516, 0.03513, 0.18772, 0, 0.31441, 0.07008, 0.40089, 0.20785, 0.38882, 0.30654, 0.3667, 0.3929, 0.42904, 0.47721, 0.50747, 0.543, 0.58388, 0.58618, 0.63014, 0.51627, 0.6603, 0.65609, 0.70655, 0.80825, 0.83124, 0.47721, 0.7508, 0.65404, 0.6822, 0.72813, 0.57383, 0.80414, 0.40088, 0.68077, 0.30034, 0.51833, 0.23598, 0.4258, 0.1837, 0.27776], "triangles": [40, 21, 22, 40, 22, 23, 40, 23, 24, 20, 21, 40, 19, 20, 40, 25, 40, 24, 18, 19, 40, 26, 40, 25, 39, 40, 26, 18, 40, 39, 17, 18, 39, 16, 17, 39, 16, 39, 38, 38, 39, 26, 38, 26, 27, 15, 16, 38, 37, 38, 27, 37, 27, 28, 14, 15, 38, 37, 14, 38, 29, 37, 28, 36, 29, 31, 36, 37, 29, 13, 14, 37, 13, 37, 36, 12, 13, 36, 32, 12, 36, 33, 2, 3, 33, 3, 4, 33, 4, 5, 1, 2, 33, 0, 1, 33, 33, 5, 6, 7, 33, 6, 33, 30, 0, 8, 34, 33, 34, 30, 33, 31, 29, 30, 34, 31, 30, 7, 8, 33, 35, 31, 34, 9, 34, 8, 32, 35, 34, 36, 31, 35, 9, 32, 34, 36, 35, 32, 10, 32, 9, 11, 32, 10, 11, 12, 32], "vertices": [2, 39, 11.81, -16.39, 0.97145, 40, 20.53, -27.04, 0.02855, 2, 39, 4.47, -17.25, 0.99761, 40, 22.43, -34.18, 0.00239, 1, 39, -1.08, -16.74, 1, 1, 39, -6.21, -10.07, 1, 1, 39, -9.08, -2.73, 1, 1, 39, -6.56, 4.34, 1, 1, 39, 0.86, 12.91, 1, 1, 39, 6.18, 13.5, 1, 1, 39, 18.26, 14.82, 1, 1, 39, 29.17, 14.86, 1, 2, 39, 41.86, 14.75, 0.90664, 40, -14.64, -1.81, 0.09336, 2, 39, 51.69, 12.89, 0.59798, 40, -14.22, 8.18, 0.40202, 2, 39, 56.5, 1.62, 0.14238, 40, -3.77, 14.57, 0.85762, 1, 40, 13.3, 17.3, 1, 1, 40, 30.66, 15.94, 1, 2, 40, 43.5, 13.55, 0.93294, 41, -9.24, 10.81, 0.06706, 2, 40, 51.89, 9.56, 0.35942, 41, 0.05, 10.39, 0.64058, 2, 40, 57.3, 9.85, 0.0711, 41, 4.92, 12.76, 0.9289, 1, 41, 15.63, 13.45, 1, 1, 41, 22.36, 14.01, 1, 1, 41, 32.8, 9.12, 1, 1, 41, 40.78, -3.18, 1, 1, 41, 39.65, -12.96, 1, 1, 41, 29.11, -20.75, 1, 2, 40, 52.51, -26.59, 0.02032, 41, 14.67, -22.67, 0.97968, 2, 40, 47.45, -19.33, 0.11762, 41, 7.18, -17.95, 0.88238, 2, 40, 43.8, -12.27, 0.49565, 41, 1.07, -12.87, 0.50435, 3, 39, 26.15, -32.68, 0.00061, 40, 34.56, -10.49, 0.96226, 41, -8.13, -14.82, 0.03713, 2, 39, 27.06, -23.49, 0.05659, 40, 25.34, -10.92, 0.94341, 2, 39, 26.4, -15.57, 0.44238, 40, 17.6, -12.72, 0.55762, 2, 39, 18.89, -15.52, 0.86184, 40, 18.64, -20.16, 0.13816, 2, 39, 27.72, -6.33, 0.79072, 40, 8.27, -12.75, 0.20928, 1, 39, 36.65, 4.68, 1, 1, 39, 5.85, -2.22, 1, 1, 39, 23, 0.42, 1, 2, 39, 31.95, -1.12, 0.9681, 40, 2.5, -9.32, 0.0319, 1, 40, 5.3, 2.3, 1, 1, 40, 24.34, 4.68, 1, 1, 40, 40.81, 0.06, 1, 2, 40, 50.68, -2.14, 0.0298, 41, 3.47, -0.85, 0.9702, 2, 40, 63.03, -8.74, 0.0001, 41, 17.42, -2.14, 0.9999], "hull": 31, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 0, 60, 58, 62, 64, 22, 8, 66, 66, 68, 62, 70, 70, 64, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80], "width": 91, "height": 89}}, "s2": {"s2": {"type": "mesh", "uvs": [0.02458, 0.52505, 0.09254, 0.46293, 0.24241, 0.44395, 0.27726, 0.49227, 0.29817, 0.53713, 0.35219, 0.58544, 0.38004, 0.6298, 0.39731, 0.65438, 0.42968, 0.63728, 0.51062, 0.59347, 0.59912, 0.53363, 0.65955, 0.47699, 0.67574, 0.45241, 0.68006, 0.41608, 0.67333, 0.33561, 0.68286, 0.26113, 0.67439, 0.16777, 0.68392, 0.09644, 0.72418, 0.07441, 0.77715, 0.09329, 0.78622, 0.15355, 0.79043, 0.19473, 0.78894, 0.24032, 0.80379, 0.23149, 0.82087, 0.21997, 0.80008, 0.19546, 0.79736, 0.15993, 0.80676, 0.13247, 0.83399, 0.12439, 0.84449, 0.11675, 0.83681, 0.07239, 0.84894, 0.03318, 0.88112, 0.00671, 0.91528, 0.01259, 0.92592, 0.04175, 0.93186, 0.0719, 0.96427, 0.06338, 1, 0.07459, 1, 0.11428, 0.9891, 0.1682, 0.96122, 0.22039, 0.95993, 0.27863, 0.94503, 0.317, 0.92417, 0.33344, 0.92843, 0.37559, 0.92502, 0.41859, 0.90077, 0.43813, 0.90015, 0.49866, 0.88455, 0.55116, 0.85212, 0.5981, 0.81282, 0.61169, 0.80239, 0.64766, 0.74873, 0.7328, 0.68928, 0.81025, 0.61263, 0.88306, 0.51407, 0.94037, 0.45306, 0.95896, 0.403, 0.98839, 0.35764, 1, 0.32009, 0.98529, 0.23249, 0.92175, 0.13505, 0.85916, 0.05472, 0.79136, 0, 0.73007, 0, 0.61403, 0.11661, 0.58664, 0.20352, 0.68053, 0.29043, 0.77702, 0.35891, 0.81744, 0.46426, 0.7731, 0.57619, 0.69617, 0.6789, 0.61011, 0.75264, 0.53188, 0.8198, 0.4393, 0.86194, 0.34411, 0.88827, 0.24241, 0.89749, 0.19286, 0.91309, 0.11443, 0.73143, 0.15843, 0.73405, 0.24902, 0.74842, 0.3409, 0.77195, 0.40561], "triangles": [78, 18, 19, 78, 19, 20, 17, 18, 78, 16, 17, 78, 21, 78, 20, 21, 79, 78, 22, 79, 21, 16, 78, 79, 15, 16, 79, 80, 79, 22, 15, 79, 80, 22, 23, 74, 36, 37, 38, 35, 31, 34, 34, 32, 33, 34, 31, 32, 35, 30, 31, 77, 30, 35, 29, 30, 77, 38, 35, 36, 38, 77, 35, 39, 77, 38, 76, 29, 77, 76, 77, 39, 28, 29, 76, 28, 25, 26, 27, 28, 26, 76, 25, 28, 76, 24, 25, 40, 76, 39, 75, 24, 76, 75, 76, 40, 41, 75, 40, 42, 75, 41, 43, 75, 42, 75, 23, 24, 74, 23, 75, 14, 15, 80, 13, 14, 80, 74, 75, 43, 80, 22, 74, 74, 43, 44, 81, 80, 74, 13, 80, 81, 44, 46, 74, 45, 46, 44, 73, 81, 74, 46, 73, 74, 47, 73, 46, 81, 12, 13, 72, 81, 73, 48, 72, 73, 72, 12, 81, 11, 12, 72, 47, 48, 73, 49, 72, 48, 50, 72, 49, 71, 11, 72, 10, 11, 71, 71, 72, 50, 51, 71, 50, 70, 9, 10, 70, 10, 71, 52, 71, 51, 70, 71, 52, 70, 69, 8, 70, 8, 9, 7, 8, 69, 68, 67, 7, 53, 70, 52, 69, 68, 7, 54, 70, 53, 69, 70, 54, 55, 69, 54, 56, 68, 69, 65, 1, 2, 65, 2, 3, 65, 3, 4, 0, 1, 65, 64, 0, 65, 66, 65, 4, 66, 4, 5, 66, 5, 6, 63, 64, 65, 63, 65, 66, 67, 66, 6, 67, 6, 7, 62, 63, 66, 61, 62, 66, 61, 66, 67, 60, 61, 67, 60, 67, 68, 55, 56, 69, 59, 60, 68, 68, 57, 59, 56, 57, 68, 58, 59, 57], "vertices": [1, 25, -3, 1.21, 1, 1, 25, -1.92, 10.48, 1, 2, 25, 8.32, 21.8, 0.99394, 26, 13.18, 38.23, 0.00606, 2, 25, 14.2, 20.35, 0.9778, 26, 13.15, 32.18, 0.0222, 2, 25, 18.78, 18.25, 0.93188, 26, 12.17, 27.23, 0.06812, 2, 25, 26.13, 18.06, 0.7716, 26, 13.7, 20.04, 0.2284, 2, 25, 31.21, 16.46, 0.55386, 26, 13.33, 14.73, 0.44614, 2, 25, 34.16, 15.69, 0.31085, 26, 13.27, 11.67, 0.68915, 2, 25, 35.51, 19.14, 0.09047, 26, 16.95, 11.17, 0.90953, 2, 25, 38.81, 27.86, 0.00027, 26, 26.19, 10, 0.99973, 3, 26, 37.01, 9.7, 0.96268, 27, -5.65, 14.17, 0.03567, 29, -24.68, 16.1, 0.00165, 2, 26, 45.34, 10.8, 0.55411, 27, 2.18, 11.13, 0.44589, 2, 26, 48.13, 11.88, 0.29654, 27, 5.15, 10.72, 0.70346, 3, 26, 50.66, 14.62, 0.10883, 27, 8.69, 11.91, 0.88317, 29, -13.34, 7.04, 0.008, 3, 26, 54.92, 21.67, 0.00502, 27, 15.81, 16.04, 0.91443, 29, -5.11, 7.1, 0.08055, 2, 27, 23.09, 18.41, 0.0037, 29, 2.4, 5.56, 0.9963, 1, 29, 11.96, 5.7, 1, 1, 29, 19.14, 4.19, 1, 1, 29, 21.07, -0.04, 1, 1, 29, 18.75, -5.23, 1, 2, 28, 12.59, 11.98, 0.00114, 29, 12.55, -5.67, 0.99886, 2, 28, 8.7, 10.34, 0.03086, 29, 8.33, -5.78, 0.96914, 3, 27, 29.59, 9.64, 0.0369, 28, 4.21, 9.12, 0.26343, 29, 3.71, -5.28, 0.69967, 3, 27, 31.05, 8.67, 0.03365, 28, 5.51, 7.95, 0.55513, 29, 4.49, -6.84, 0.41122, 3, 27, 32.85, 7.61, 0.00739, 28, 7.14, 6.65, 0.82614, 29, 5.53, -8.65, 0.16646, 2, 28, 8.91, 9.39, 0.93903, 29, 8.18, -6.75, 0.06097, 2, 28, 12.3, 10.71, 0.97825, 29, 11.82, -6.75, 0.02175, 2, 28, 15.25, 10.62, 0.98978, 29, 14.54, -7.91, 0.01022, 2, 28, 16.85, 8.23, 0.99605, 29, 15.15, -10.71, 0.00395, 2, 28, 17.9, 7.45, 0.99885, 29, 15.85, -11.83, 0.00115, 1, 28, 22, 9.51, 1, 1, 28, 26.19, 9.51, 1, 1, 28, 29.72, 7.19, 1, 1, 28, 30.15, 3.72, 1, 1, 28, 27.62, 1.82, 1, 1, 28, 24.86, 0.35, 1, 1, 28, 26.65, -2.53, 1, 1, 28, 26.61, -6.32, 1, 1, 28, 22.74, -7.5, 1, 1, 28, 17.16, -8.06, 1, 1, 28, 11.24, -6.92, 1, 2, 27, 33.45, -7.65, 0.06961, 28, 5.52, -8.53, 0.93039, 2, 27, 29.27, -7.96, 0.28815, 28, 1.34, -8.24, 0.71185, 2, 27, 26.85, -6.77, 0.56864, 28, -0.88, -6.71, 0.43136, 2, 27, 23.15, -9, 0.92449, 28, -4.87, -8.38, 0.07551, 2, 27, 19.04, -10.56, 0.99505, 28, -9.16, -9.34, 0.00495, 1, 27, 16.19, -9.2, 1, 2, 26, 63.73, -5.23, 0.00506, 27, 10.58, -11.78, 0.99494, 2, 26, 59.31, -8.65, 0.07614, 27, 5.06, -12.65, 0.92386, 2, 26, 53.85, -10.61, 0.29294, 27, -0.66, -11.74, 0.70706, 2, 26, 49.83, -9.4, 0.62438, 27, -3.61, -8.74, 0.37562, 2, 26, 46.82, -11.76, 0.89089, 27, -7.38, -9.36, 0.10911, 2, 25, 66.32, 32.72, 0.00472, 26, 37.34, -15.62, 0.99528, 2, 25, 66.9, 22.81, 0.04972, 26, 27.84, -18.5, 0.95028, 2, 25, 65.86, 12.13, 0.2169, 26, 17.22, -19.97, 0.7831, 2, 25, 62.1, 1.22, 0.61145, 26, 5.73, -18.87, 0.38855, 2, 25, 58.66, -4.23, 0.86732, 26, -0.38, -16.8, 0.13268, 2, 25, 56.77, -9.8, 0.98733, 26, -6.23, -16.26, 0.01267, 1, 25, 54.06, -13.68, 1, 1, 25, 50.21, -15.01, 1, 1, 25, 39.27, -15.85, 1, 1, 25, 27.65, -17.41, 1, 1, 25, 16.99, -17.44, 1, 1, 25, 8.72, -16.29, 1, 1, 25, 1.02, -7.3, 1, 1, 25, 8.14, 2.49, 1, 2, 25, 21.04, 0.93, 0.99838, 26, -4.15, 20.99, 0.00162, 1, 25, 34.11, -0.83, 1, 2, 25, 42.04, 0.54, 0.09254, 26, 0.38, 0.47, 0.90746, 2, 25, 47.18, 10.9, 0.03489, 26, 11.65, -2.1, 0.96511, 2, 25, 50.66, 24.21, 0.00315, 26, 25.41, -2.37, 0.99685, 1, 26, 38.96, -1.34, 1, 3, 26, 49.67, 0.76, 0.15382, 27, 1.14, 0.23, 0.84595, 29, -25.67, 0.62, 0.00023, 1, 27, 12.58, -1.86, 1, 2, 27, 23.18, -1.55, 0.98252, 28, -3.76, -1.02, 0.01748, 1, 28, 6.94, -0.53, 1, 2, 28, 12.04, 0.06, 0.99984, 29, 7.7, -16.58, 0.00016, 1, 28, 20.15, 0.89, 1, 1, 29, 12.47, -0.12, 1, 1, 29, 3.24, 0.32, 1, 3, 27, 18.57, 8.95, 0.90012, 28, -6.8, 10.04, 0.00214, 29, -6.22, -0.42, 0.09775, 3, 26, 58.8, 10.05, 0.00272, 27, 13.62, 3.98, 0.87225, 29, -12.98, -2.29, 0.12503], "hull": 65, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 0, 128, 0, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 36, 156, 156, 158, 158, 160, 160, 162], "width": 101, "height": 102}}, "s3": {"s3": {"type": "mesh", "uvs": [0.0105, 0.70204, 0, 0.77385, 0.02723, 0.92007, 0.14672, 0.97229, 0.38093, 1, 0.58884, 0.96838, 0.75852, 0.92529, 0.83499, 0.89396, 0.95448, 0.89004, 0.98316, 0.82346, 1, 0.70204, 1, 0.59107, 1, 0.46182, 0.9975, 0.34693, 0.99276, 0.32475, 0.98077, 0.2686, 0.92103, 0.16546, 0.76808, 0.08582, 0.6223, 0.01924, 0.51715, 0, 0.40243, 0, 0.28772, 0, 0.31162, 0.0806, 0.35942, 0.14196, 0.46691, 0.15163, 0.57512, 0.14371, 0.51618, 0.17696, 0.49686, 0.19649, 0.43213, 0.19438, 0.36451, 0.20335, 0.35291, 0.23449, 0.39834, 0.26748, 0.44662, 0.28601, 0.51188, 0.29172, 0.59018, 0.31881, 0.69304, 0.32491, 0.69609, 0.33639, 0.64808, 0.43269, 0.60232, 0.52991, 0.55147, 0.64935, 0.5413, 0.68361, 0.47012, 0.68361, 0.3091, 0.68546, 0.24131, 0.7012, 0.11758, 0.69472, 0.45916, 0.05688, 0.67876, 0.11306, 0.7955, 0.20418, 0.85943, 0.29225, 0.85387, 0.34084, 0.81218, 0.45625, 0.77882, 0.56558, 0.77048, 0.69314, 0.71489, 0.77362, 0.51476, 0.80551, 0.28405, 0.82525, 0.10337, 0.82677, 0.43382, 0.23469, 0.54475, 0.25232, 0.67988, 0.22918], "triangles": [33, 32, 58, 31, 57, 32, 32, 57, 58, 31, 30, 57, 57, 27, 58, 30, 29, 57, 29, 28, 57, 57, 28, 27, 58, 27, 26, 26, 25, 59, 25, 46, 59, 46, 17, 47, 25, 24, 45, 24, 23, 45, 25, 45, 46, 23, 22, 45, 46, 45, 18, 45, 19, 18, 46, 18, 17, 22, 21, 45, 21, 20, 45, 45, 20, 19, 48, 49, 35, 34, 59, 35, 48, 59, 47, 48, 35, 59, 48, 15, 14, 33, 58, 34, 34, 58, 59, 48, 47, 15, 47, 16, 15, 59, 58, 26, 59, 46, 47, 47, 17, 16, 53, 52, 9, 9, 52, 10, 52, 53, 39, 52, 11, 10, 52, 51, 11, 52, 39, 51, 39, 38, 51, 11, 51, 12, 51, 50, 12, 51, 38, 50, 38, 37, 50, 12, 49, 13, 12, 50, 49, 37, 36, 50, 50, 36, 49, 49, 14, 13, 49, 48, 14, 49, 36, 35, 3, 55, 4, 4, 54, 5, 4, 55, 54, 2, 56, 3, 3, 56, 55, 5, 54, 6, 54, 53, 6, 6, 53, 7, 2, 1, 56, 7, 53, 8, 8, 53, 9, 56, 1, 44, 56, 43, 55, 56, 44, 43, 44, 1, 0, 55, 42, 54, 42, 41, 54, 55, 43, 42, 54, 40, 53, 54, 41, 40, 53, 40, 39], "vertices": [1, 30, -2.56, 17.64, 1, 1, 30, -4.89, 10.21, 1, 1, 30, -6.83, -5.54, 1, 1, 30, -1.21, -12.61, 1, 1, 30, 11.6, -18.59, 1, 1, 30, 24.32, -17.99, 1, 2, 30, 35.11, -15.68, 0.97513, 31, -16.7, 1.21, 0.02487, 2, 30, 40.26, -13.38, 0.83082, 31, -13.04, -3.08, 0.16918, 2, 30, 47.23, -14.53, 0.60566, 31, -12.18, -10.09, 0.39434, 2, 30, 50.48, -7.9, 0.36286, 31, -4.89, -11.33, 0.63714, 1, 31, 8.26, -11.49, 1, 1, 31, 20.22, -10.74, 1, 1, 31, 34.15, -9.87, 1, 2, 31, 46.52, -8.94, 0.78093, 32, -7.1, -5.75, 0.21907, 2, 31, 48.9, -8.51, 0.59131, 32, -4.99, -6.91, 0.40869, 2, 31, 54.9, -7.43, 0.12581, 32, 0.36, -9.85, 0.87419, 2, 32, 11.48, -13.44, 0.93344, 33, -9.38, -11.81, 0.06656, 2, 32, 23.72, -11.08, 0.26314, 33, 3.08, -11.93, 0.73686, 2, 32, 34.57, -8.24, 0.00034, 33, 14.28, -11.31, 0.99966, 1, 33, 20.23, -8.59, 1, 1, 33, 25.17, -3.97, 1, 1, 33, 30.12, 0.65, 1, 1, 33, 23.14, 6.05, 1, 1, 33, 16.56, 8.96, 1, 1, 33, 11.21, 5.39, 1, 2, 32, 25.23, 1.82, 0.00149, 33, 7.13, 0.41, 0.99851, 2, 32, 24.32, 6.74, 0.14182, 33, 7.22, 5.41, 0.85818, 2, 32, 23.26, 8.89, 0.24269, 33, 6.61, 7.73, 0.75731, 2, 32, 25.66, 11.87, 0.26307, 33, 9.56, 10.17, 0.73693, 2, 32, 27.19, 15.68, 0.26469, 33, 11.81, 13.6, 0.73531, 2, 32, 24.84, 18.19, 0.27446, 33, 10.01, 16.52, 0.72554, 2, 32, 20.39, 18.07, 0.32176, 33, 5.62, 17.3, 0.67824, 2, 32, 17.1, 16.91, 0.39141, 33, 2.17, 16.81, 0.60859, 3, 31, 50.68, 20.03, 0.00154, 32, 14.37, 14.13, 0.5343, 33, -1.06, 14.63, 0.46416, 3, 31, 48.05, 15.23, 0.03094, 32, 9.31, 12.06, 0.75626, 33, -6.43, 13.62, 0.2128, 3, 31, 47.77, 9.13, 0.29105, 32, 5.25, 7.5, 0.67545, 33, -11.32, 9.96, 0.0335, 3, 31, 46.54, 8.88, 0.48459, 32, 4.14, 8.07, 0.50095, 33, -12.3, 10.74, 0.01446, 2, 31, 35.99, 11.05, 0.99434, 32, -2.69, 16.41, 0.00566, 2, 30, 35.61, 28.01, 0.0189, 31, 25.34, 13.09, 0.9811, 2, 30, 29.82, 16.09, 0.35561, 31, 12.28, 15.27, 0.64439, 2, 30, 28.41, 12.62, 0.6462, 31, 8.55, 15.64, 0.3538, 2, 30, 24.32, 13.55, 0.87509, 31, 8.28, 19.83, 0.12491, 2, 30, 15.01, 15.47, 0.9918, 31, 7.49, 29.3, 0.0082, 2, 30, 10.73, 14.7, 0.99922, 31, 5.54, 33.19, 0.00078, 1, 30, 3.77, 17, 1, 1, 33, 18.53, -1.77, 1, 2, 32, 24.38, -5.08, 0.10043, 33, 4.92, -6.18, 0.89957, 2, 32, 12.37, -4.98, 0.96876, 33, -6.83, -3.7, 0.03124, 2, 31, 51.91, -0.44, 0.02774, 32, 2.43, -2.54, 0.97226, 1, 31, 46.65, -0.44, 1, 1, 31, 34.05, 1.23, 1, 2, 30, 44.91, 21.94, 0.00332, 31, 22.15, 2.45, 0.99668, 2, 30, 41.37, 8.61, 0.05311, 31, 8.37, 2.08, 0.94689, 2, 30, 36.24, 0.87, 0.94843, 31, -0.51, 4.81, 0.05157, 2, 30, 23.96, 0.13, 0.99963, 31, -4.69, 16.38, 0.00037, 1, 30, 10.22, 1.08, 1, 1, 30, -0.21, 3.29, 1, 2, 32, 22.06, 14.31, 0.30105, 33, 6.51, 13.28, 0.69895, 3, 31, 55.05, 18.36, 0.0002, 32, 16.71, 10.08, 0.51139, 33, 0.43, 10.2, 0.48841, 3, 31, 58.04, 10.56, 0.00058, 32, 14.12, 2.14, 0.92893, 33, -3.69, 2.93, 0.07049], "hull": 45, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 26, 28, 28, 30, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 0, 88, 42, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 16, 78, 106, 106, 108, 108, 110, 110, 112, 58, 114, 114, 116, 116, 118], "width": 59, "height": 108}}, "s4": {"s4": {"type": "mesh", "uvs": [0.94507, 0.73041, 1, 0.79038, 1, 0.89167, 0.93401, 0.96298, 0.78886, 1, 0.67551, 0.97352, 0.5428, 0.93057, 0.47092, 0.90707, 0.40871, 0.88924, 0.36586, 0.84467, 0.25804, 0.75472, 0.19583, 0.64209, 0.13363, 0.51891, 0.11704, 0.45328, 0.09216, 0.40628, 0.06157, 0.3301, 0.03079, 0.24851, 0.06023, 0.21713, 0, 0.16928, 0, 0.09946, 0.0067, 0.06573, 0.05488, 0.0218, 0.08432, 0, 0.20877, 0.00218, 0.31985, 0.01552, 0.3685, 0.06703, 0.37144, 0.09651, 0.35379, 0.11685, 0.34026, 0.1553, 0.33433, 0.17649, 0.32198, 0.19459, 0.2961, 0.2108, 0.2958, 0.22045, 0.31316, 0.22821, 0.33051, 0.22097, 0.34051, 0.20839, 0.33874, 0.1889, 0.34698, 0.16821, 0.36698, 0.14632, 0.41287, 0.12371, 0.45963, 0.12268, 0.4914, 0.13923, 0.48963, 0.19096, 0.48776, 0.23896, 0.46941, 0.27964, 0.44647, 0.29881, 0.44417, 0.3405, 0.41837, 0.37311, 0.39084, 0.39429, 0.42333, 0.44239, 0.47366, 0.50689, 0.53336, 0.55139, 0.60842, 0.59439, 0.66386, 0.63239, 0.69457, 0.65839, 0.70907, 0.70039, 0.75085, 0.71761, 0.83719, 0.72204, 0.85285, 0.87758, 0.71315, 0.84288, 0.62506, 0.79384, 0.56577, 0.75991, 0.48252, 0.70335, 0.38101, 0.60451, 0.30014, 0.51777, 0.25196, 0.41489, 0.2382, 0.30495, 0.40509, 0.16072, 0.39305, 0.21014, 0.37929, 0.26461, 0.32939, 0.29285, 0.19606, 0.06555, 0.17006, 0.14527, 0.18006, 0.22148, 0.20806, 0.26017], "triangles": [67, 38, 39, 67, 39, 40, 37, 38, 67, 41, 67, 40, 42, 67, 41, 68, 35, 36, 67, 36, 37, 68, 67, 42, 67, 68, 36, 43, 68, 42, 68, 34, 35, 69, 68, 43, 69, 34, 68, 33, 34, 69, 44, 69, 43, 70, 33, 69, 45, 69, 44, 33, 74, 32, 70, 74, 33, 45, 70, 69, 46, 70, 45, 47, 70, 46, 48, 70, 47, 71, 22, 23, 71, 23, 24, 21, 22, 71, 71, 24, 25, 27, 71, 25, 27, 25, 26, 71, 20, 21, 72, 71, 27, 71, 19, 20, 72, 19, 71, 28, 72, 27, 18, 19, 72, 29, 72, 28, 30, 72, 29, 31, 72, 30, 17, 18, 72, 72, 73, 17, 72, 31, 73, 32, 73, 31, 74, 73, 32, 16, 73, 74, 73, 16, 17, 74, 70, 66, 15, 74, 66, 15, 16, 74, 14, 15, 66, 48, 65, 66, 48, 66, 70, 14, 66, 65, 13, 14, 65, 49, 64, 65, 49, 65, 48, 64, 49, 50, 13, 65, 64, 12, 13, 64, 63, 64, 50, 63, 50, 51, 11, 12, 64, 11, 64, 63, 52, 62, 63, 52, 63, 51, 62, 52, 53, 10, 11, 63, 10, 63, 62, 54, 62, 53, 54, 61, 62, 9, 10, 62, 9, 62, 61, 61, 54, 55, 60, 61, 55, 60, 55, 56, 59, 60, 56, 1, 58, 57, 1, 57, 0, 59, 56, 57, 58, 59, 57, 8, 9, 61, 8, 61, 60, 58, 1, 2, 7, 8, 60, 6, 7, 60, 59, 6, 60, 3, 58, 2, 5, 6, 59, 5, 59, 58, 4, 5, 58, 3, 4, 58], "vertices": [2, 34, 5.65, -17.64, 0.99959, 35, -7, -28.25, 0.00041, 1, 34, -0.87, -13.19, 1, 1, 34, -6.29, -2.76, 1, 1, 34, -6.12, 6.65, 1, 1, 34, 0.66, 15.01, 1, 1, 34, 8.92, 15.84, 1, 2, 34, 19.22, 15.58, 0.98621, 35, -17.23, 6.13, 0.01379, 2, 34, 24.82, 15.41, 0.89479, 35, -12.77, 9.51, 0.10521, 2, 34, 29.52, 15.53, 0.75704, 35, -9.17, 12.55, 0.24296, 2, 34, 34.49, 12.28, 0.46636, 35, -3.26, 13.13, 0.53364, 2, 34, 45.81, 6.4, 0.0111, 35, 9.24, 15.64, 0.9889, 1, 35, 22.9, 14.25, 1, 2, 35, 37.68, 12.37, 0.96452, 36, -8.19, 12.02, 0.03548, 2, 35, 45.11, 10.34, 0.60518, 36, -0.68, 10.32, 0.39482, 2, 35, 50.78, 9.69, 0.16239, 36, 5.01, 9.92, 0.83761, 2, 36, 14, 8.65, 0.96864, 37, -7.52, 6.76, 0.03136, 2, 36, 23.58, 7.17, 0.2666, 37, 2.08, 8.12, 0.7334, 2, 36, 26.25, 3.99, 0.03397, 37, 5.55, 5.85, 0.96603, 1, 37, 11.4, 9.51, 1, 1, 37, 19.48, 8.89, 1, 1, 37, 23.34, 8.13, 1, 1, 37, 28.17, 4.47, 1, 1, 37, 30.54, 2.28, 1, 1, 37, 29.64, -6.13, 1, 2, 37, 27.52, -13.55, 0.9998, 38, 26.73, 11.95, 0.0002, 2, 37, 21.31, -16.39, 0.99373, 38, 22.11, 6.92, 0.00627, 2, 37, 17.88, -16.32, 0.98666, 38, 18.93, 5.65, 0.01334, 2, 37, 15.62, -14.95, 0.97532, 38, 16.31, 6.04, 0.02468, 2, 37, 11.24, -13.69, 0.93, 38, 11.79, 5.5, 0.07, 3, 36, 23.89, -15.09, 0.00106, 37, 8.83, -13.1, 0.88945, 38, 9.33, 5.11, 0.10949, 3, 36, 22.23, -13.55, 0.00842, 37, 6.8, -12.1, 0.834, 38, 7.07, 5.24, 0.15757, 3, 36, 21.12, -11.23, 0.04315, 37, 5.06, -10.2, 0.67141, 38, 4.73, 6.32, 0.28544, 3, 36, 20.08, -10.8, 0.07442, 37, 3.94, -10.09, 0.49373, 38, 3.66, 5.98, 0.43185, 3, 36, 18.82, -11.58, 0.06662, 37, 2.95, -11.2, 0.23333, 38, 3.18, 4.58, 0.70005, 3, 36, 19.17, -12.98, 0.02351, 37, 3.7, -12.44, 0.09677, 38, 4.35, 3.72, 0.87971, 3, 36, 20.29, -14.14, 0.00484, 37, 5.1, -13.23, 0.0293, 38, 5.95, 3.54, 0.96586, 3, 36, 22.44, -14.85, 0.0001, 37, 7.37, -13.29, 0.00401, 38, 8.06, 4.37, 0.99589, 1, 38, 10.51, 4.6, 1, 1, 38, 13.35, 4.11, 1, 1, 38, 16.83, 1.98, 1, 1, 38, 17.95, -1, 1, 1, 38, 16.81, -3.66, 1, 1, 38, 11.08, -5.44, 1, 1, 38, 5.76, -7.08, 1, 3, 35, 53.89, -19.71, 0.00517, 36, 9.4, -19.32, 0.01464, 38, 0.88, -7.39, 0.98019, 3, 35, 52.49, -17.38, 0.02076, 36, 7.9, -17.06, 0.07316, 38, -1.72, -6.61, 0.90608, 3, 35, 48.12, -15.29, 0.09439, 36, 3.45, -15.16, 0.2406, 38, -6.36, -7.99, 0.66502, 3, 35, 45.37, -12.16, 0.23412, 36, 0.56, -12.15, 0.32512, 38, -10.5, -7.53, 0.44077, 3, 35, 43.87, -9.46, 0.48647, 36, -1.05, -9.52, 0.28311, 38, -13.42, -6.53, 0.23041, 3, 35, 37.87, -9.24, 0.94057, 36, -7.06, -9.55, 0.02573, 38, -18.02, -10.39, 0.03371, 2, 35, 29.65, -9.36, 0.99982, 38, -24.03, -16, 0.00018, 2, 34, 40.07, -23.16, 0.0047, 35, 23.29, -10.99, 0.9953, 2, 34, 33.24, -21.09, 0.06212, 35, 16.67, -13.66, 0.93788, 2, 34, 27.86, -18.91, 0.19562, 35, 11.11, -15.33, 0.80438, 2, 34, 24.62, -17.2, 0.32139, 35, 7.51, -16.03, 0.67861, 2, 34, 21.5, -13.33, 0.60784, 35, 2.66, -14.97, 0.39216, 2, 34, 18.06, -12.87, 0.84012, 35, -0.32, -16.76, 0.15988, 2, 34, 12.61, -15.12, 0.97408, 35, -3.15, -21.93, 0.02592, 1, 34, 3.35, 0.4, 1, 1, 34, 13.63, 1.21, 1, 2, 34, 21.57, -1.08, 0.98725, 35, -4.97, -5.37, 0.01275, 2, 34, 26.96, -2.71, 0.65981, 35, 0.26, -3.27, 0.34019, 2, 34, 35.01, -5.92, 0.00547, 35, 8.55, -0.73, 0.99453, 1, 35, 21.82, 0.97, 1, 1, 35, 33.25, 1.95, 1, 2, 35, 45.49, 0.14, 0.44879, 36, 0.14, 0.15, 0.55121, 3, 36, 12.37, -3.6, 0.80828, 37, -5.53, -5.44, 0.01424, 38, -6.87, 6.6, 0.17748, 1, 38, 12.59, 1.12, 1, 3, 36, 18.8, -17.4, 0, 37, 4.63, -16.78, 0.00013, 38, 6.89, 0.09, 0.99986, 2, 35, 57.96, -14.8, 0.00071, 38, 0.6, -1.02, 0.99929, 4, 35, 56.33, -10.38, 0.00575, 36, 11.43, -9.89, 0.26317, 37, -4.61, -11.73, 0.01154, 38, -3.58, 1.16, 0.71954, 2, 37, 22.38, -4.71, 0.99935, 38, 18.57, 18.1, 0.00065, 2, 37, 13.29, -2.24, 0.99344, 38, 9.23, 16.85, 0.00656, 3, 36, 22.82, -3.42, 0.02622, 37, 4.43, -2.24, 0.9296, 38, 1.06, 13.41, 0.04418, 3, 36, 17.95, -3.57, 0.53841, 37, -0.19, -3.79, 0.33185, 38, -2.59, 10.18, 0.12974], "hull": 58, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 0, 114, 4, 116, 116, 118, 110, 120, 120, 16, 108, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 80, 134, 134, 136, 136, 138, 138, 140, 48, 142, 142, 144, 144, 146, 146, 148], "width": 68, "height": 116}}, "wuqi2": {"wuqi": {"x": 1.3, "width": 313, "height": 66}}, "er": {"er": {"type": "mesh", "uvs": [0.66687, 1, 0.76499, 0.96069, 0.84629, 0.90698, 0.89956, 0.80507, 0.92059, 0.68526, 0.88835, 0.63981, 0.89956, 0.58748, 0.91358, 0.5379, 0.95984, 0.48556, 0.97526, 0.43461, 1, 0.37677, 0.98227, 0.31066, 0.88554, 0.22665, 0.75658, 0.1151, 0.6136, 0.04073, 0.50286, 0.02558, 0.39492, 0, 0.24773, 0, 0.09914, 0, 0, 0, 0, 0.07929, 0.01784, 0.14402, 0.05849, 0.18258, 0.04307, 0.27348, 0.05429, 0.36437, 0, 0.35886, 0.01083, 0.43185, 0.07391, 0.49245, 0.12157, 0.5737, 0.10615, 0.65634, 0.11456, 0.72933, 0.19166, 0.81884, 0.28418, 0.87393, 0.37109, 0.91662, 0.42436, 0.90285, 0.52669, 0.95381, 0.36688, 0.78579, 0.36829, 0.63568, 0.47903, 0.51999, 0.65846, 0.49383, 0.82246, 0.52137, 0.06444, 0.05409, 0.21053, 0.09484, 0.39449, 0.18875, 0.51533, 0.32342, 0.54779, 0.11079, 0.74078, 0.27558, 0.82194, 0.43505, 0.1979, 0.2933, 0.2484, 0.4634, 0.28087, 0.52365, 0.2484, 0.73096, 0.20872, 0.57858, 0.15281, 0.46163], "triangles": [10, 47, 11, 7, 40, 47, 2, 1, 3, 38, 44, 39, 38, 49, 44, 39, 36, 38, 37, 50, 38, 31, 51, 32, 51, 52, 37, 1, 5, 3, 5, 35, 39, 0, 35, 1, 39, 35, 36, 34, 33, 36, 5, 1, 35, 33, 32, 36, 35, 34, 36, 38, 36, 37, 32, 51, 36, 36, 51, 37, 40, 5, 39, 39, 47, 40, 5, 40, 6, 6, 40, 7, 3, 5, 4, 8, 7, 47, 39, 46, 47, 8, 47, 9, 10, 9, 47, 39, 44, 46, 47, 46, 12, 11, 47, 12, 44, 45, 46, 46, 45, 13, 46, 13, 12, 13, 45, 14, 16, 15, 45, 45, 15, 14, 49, 43, 44, 49, 48, 43, 44, 43, 45, 43, 16, 45, 48, 42, 43, 43, 42, 16, 16, 42, 17, 42, 18, 17, 22, 41, 42, 22, 21, 41, 21, 20, 41, 41, 18, 42, 20, 19, 41, 41, 19, 18, 24, 23, 48, 23, 22, 48, 48, 22, 42, 53, 27, 24, 27, 26, 24, 53, 24, 48, 26, 25, 24, 53, 48, 49, 52, 49, 50, 50, 49, 38, 52, 50, 37, 31, 30, 51, 51, 29, 52, 52, 29, 28, 29, 51, 30, 28, 53, 52, 52, 53, 49, 28, 27, 53], "vertices": [1, 5, 8.94, -8.96, 1, 1, 5, 14.51, -19.44, 1, 1, 5, 21.53, -27.88, 1, 1, 5, 33.69, -32.64, 1, 1, 5, 47.52, -33.6, 1, 1, 5, 52.31, -29.48, 1, 1, 5, 58.37, -30.13, 1, 1, 5, 64.15, -31.12, 1, 1, 5, 70.61, -35.67, 1, 1, 5, 76.56, -36.8, 1, 1, 5, 83.4, -38.89, 1, 4, 84, 36.51, -87.55, 0.00155, 82, 26.15, -75.14, 0.08343, 77, -3.13, -64.47, 3e-05, 79, -5.9, -26.55, 0.91499, 2, 82, 34.58, -63.4, 0.02066, 79, 2.53, -14.8, 0.97934, 2, 78, 2.91, -16.26, 0.39113, 79, 13.72, 0.85, 0.60887, 4, 76, 10.56, -38.13, 0.03377, 77, 23.3, -20.28, 0.0216, 78, 9.72, 0.53, 0.94052, 79, 20.54, 17.64, 0.0041, 3, 76, 11.03, -25.62, 0.24308, 77, 23.77, -7.77, 0.20229, 78, 10.19, 13.05, 0.55463, 3, 76, 12.7, -13.3, 0.60757, 77, 25.44, 4.55, 0.1825, 78, 11.87, 25.37, 0.20993, 4, 75, 4.3, -16.5, 0.16962, 76, 11.04, 3.1, 0.81683, 77, 23.78, 20.95, 0.00212, 78, 10.2, 41.77, 0.01143, 2, 75, 2.61, 0.05, 0.98727, 76, 9.35, 19.66, 0.01273, 2, 80, 24.05, 14.67, 0.03662, 75, 1.49, 11.1, 0.96338, 2, 80, 15.06, 13.75, 0.1966, 75, -7.5, 10.19, 0.8034, 3, 80, 7.92, 11.02, 0.45231, 75, -14.64, 7.45, 0.54682, 76, -7.9, 27.06, 0.00087, 4, 83, 27.08, 2.75, 0.00109, 80, 4.01, 6.04, 0.75536, 75, -18.55, 2.48, 0.23451, 76, -11.82, 22.08, 0.00905, 3, 81, 11.84, 18.04, 0.09311, 83, 16.6, 3.42, 0.14539, 80, -6.48, 6.71, 0.7615, 3, 81, 1.66, 15.74, 0.12543, 83, 6.42, 1.12, 0.7034, 80, -16.66, 4.42, 0.17117, 2, 83, 6.43, 7.24, 0.96883, 80, -16.65, 10.53, 0.03117, 2, 84, 11.76, 19.29, 0.00099, 83, -1.73, 5.19, 0.99901, 3, 84, 5.6, 11.57, 0.34226, 81, -12.65, 12.08, 0.07131, 83, -7.89, -2.54, 0.58643, 4, 84, -3.08, 5.32, 0.95038, 81, -21.32, 5.83, 0.0036, 83, -16.56, -8.79, 0.04551, 79, -45.49, 66.32, 0.00051, 3, 84, -12.62, 6.08, 0.95115, 82, -22.98, 18.49, 0.03981, 79, -55.03, 67.08, 0.00905, 3, 84, -20.81, 4.3, 0.86707, 82, -31.17, 16.71, 0.11255, 79, -63.21, 65.31, 0.02038, 3, 84, -30.09, -5.32, 0.6779, 82, -40.44, 7.09, 0.26952, 79, -72.49, 55.68, 0.05258, 1, 5, 18.91, 35.14, 1, 1, 5, 15.05, 24.96, 1, 1, 5, 17.21, 19.19, 1, 1, 5, 12.59, 7.2, 1, 1, 5, 29.84, 26.94, 1, 1, 5, 46.88, 28.52, 1, 1, 5, 61.26, 17.51, 1, 1, 5, 66.26, -2.18, 1, 1, 5, 64.99, -20.77, 1, 3, 80, 18.65, 6.86, 0.1083, 75, -3.91, 3.3, 0.89102, 76, 2.83, 22.9, 0.00068, 5, 81, 34, 1.44, 0.00335, 80, 15.68, -9.88, 0.10933, 75, -6.88, -13.45, 0.1912, 76, -0.14, 6.15, 0.69501, 77, 12.6, 24.01, 0.00111, 5, 81, 25.43, -20.14, 0.00888, 80, 7.11, -31.47, 0.00548, 76, -8.71, -15.43, 0.16913, 77, 4.03, 2.42, 0.76807, 78, -9.55, 23.24, 0.04845, 6, 84, 29.77, -35.67, 0.0001, 82, 19.41, -23.26, 0.23334, 81, 11.53, -35.16, 0.00301, 77, -9.87, -12.59, 0.49839, 78, -23.45, 8.22, 0.08108, 79, -12.64, 25.33, 0.18408, 4, 82, 43.9, -24.43, 0.0001, 76, 1.87, -31.61, 0.07472, 77, 14.61, -13.76, 0.1752, 78, 1.03, 7.06, 0.74997, 5, 84, 37.75, -60.24, 0.00056, 82, 27.39, -47.83, 0.0422, 77, -1.89, -37.16, 0.03342, 78, -15.47, -16.35, 0.01208, 79, -4.66, 0.76, 0.91174, 1, 5, 74.78, -19.72, 1, 6, 82, 19.23, 12.46, 0.0194, 81, 11.34, 0.56, 0.4622, 83, 16.11, -14.06, 0.01813, 80, -6.97, -10.77, 0.33686, 76, -22.79, 5.27, 0.06849, 77, -10.05, 23.12, 0.09492, 5, 84, 10.87, -7.54, 0.12437, 82, 0.51, 4.87, 0.6214, 81, -7.38, -7.03, 0.24586, 83, -2.61, -21.65, 0.00555, 77, -28.77, 15.53, 0.00282, 4, 84, 4.4, -11.85, 0.25715, 82, -5.95, 0.56, 0.73056, 81, -13.84, -11.34, 0.0017, 79, -38, 49.15, 0.01059, 1, 5, 34.72, 40.77, 1, 3, 84, -2.64, -4.45, 0.8155, 82, -13, 7.96, 0.17812, 79, -45.05, 56.55, 0.00638, 4, 84, 9.99, 3.13, 0.33335, 82, -0.37, 15.54, 0.04841, 81, -8.26, 3.64, 0.35955, 83, -3.5, -10.98, 0.25869], "hull": 36, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 0, 70, 68, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 12, 38, 82, 82, 84, 84, 86, 86, 88, 32, 90, 90, 92, 92, 94, 44, 96, 96, 98, 98, 100, 72, 102, 102, 104, 104, 106], "width": 112, "height": 114}}, "shoiushi": {"shoiushi": {"type": "mesh", "uvs": [0.03608, 0.03504, 0.10526, 0.02471, 0.15484, 0.07768, 0.22401, 0.12536, 0.29204, 0.15847, 0.39465, 0.1876, 0.4742, 0.13463, 0.56874, 0.10152, 0.67135, 0.12933, 0.72093, 0.1823, 0.78203, 0.16376, 0.82123, 0.10285, 0.8489, 0.01676, 0.90655, 0, 0.97688, 0.02074, 1, 0.09712, 1, 0.16948, 0.96377, 0.22094, 0.92877, 0.29009, 0.87277, 0.34959, 0.80697, 0.38979, 0.75517, 0.39462, 0.69777, 0.47985, 0.62917, 0.52166, 0.61797, 0.55382, 0.63253, 0.78945, 0.49232, 1, 0.45483, 0.89309, 0.48676, 0.71291, 0.57145, 0.55345, 0.53535, 0.51358, 0.44512, 0.47531, 0.39653, 0.39558, 0.29519, 0.37485, 0.18691, 0.32542, 0.09112, 0.27599, 0.01477, 0.18191, 0, 0.09899, 0.105, 0.14683, 0.20912, 0.23293, 0.31601, 0.27758, 0.40763, 0.29193, 0.74914, 0.29512, 0.83243, 0.24569, 0.91295, 0.14364, 0.93239, 0.08783, 0.59223, 0.55361, 0.54476, 0.74856, 0.57005, 0.29785], "triangles": [47, 26, 27, 47, 25, 26, 27, 28, 47, 47, 24, 25, 24, 47, 46, 46, 47, 29, 47, 28, 29, 24, 46, 23, 46, 29, 30, 33, 40, 32, 32, 40, 41, 40, 5, 41, 41, 5, 6, 40, 4, 5, 23, 46, 30, 22, 23, 48, 48, 23, 30, 30, 31, 48, 21, 48, 42, 21, 22, 48, 21, 42, 20, 20, 42, 43, 6, 7, 48, 48, 9, 42, 48, 8, 9, 48, 7, 8, 42, 9, 10, 31, 32, 48, 48, 32, 41, 41, 6, 48, 33, 34, 40, 34, 39, 40, 34, 35, 39, 39, 4, 40, 39, 3, 4, 35, 38, 39, 35, 36, 38, 39, 38, 3, 3, 38, 2, 36, 37, 38, 37, 0, 38, 38, 1, 2, 38, 0, 1, 19, 20, 43, 19, 43, 18, 42, 10, 43, 18, 43, 17, 10, 11, 43, 43, 44, 17, 43, 11, 44, 17, 44, 16, 44, 45, 16, 45, 15, 16, 44, 11, 45, 45, 11, 12, 45, 12, 13, 45, 14, 15, 45, 13, 14], "vertices": [1, 17, 3.34, -6.73, 1, 2, 17, -2.53, -7.59, 0.99792, 18, 42.09, -20.19, 0.00208, 2, 17, -6.81, -3.74, 0.95588, 18, 37.82, -16.34, 0.04412, 2, 17, -12.74, -0.3, 0.80139, 18, 31.88, -12.9, 0.19861, 2, 17, -18.56, 2.06, 0.59959, 18, 26.06, -10.54, 0.40041, 2, 17, -27.32, 4.07, 0.2756, 18, 17.31, -8.52, 0.7244, 2, 17, -34.02, 0.05, 0.09411, 18, 10.61, -12.55, 0.90589, 3, 16, 30.16, 1, 0.01073, 17, -42.01, -2.53, 0.02364, 18, 2.61, -15.13, 0.96563, 3, 16, 21.41, 2.92, 0.0878, 17, -50.76, -0.61, 0.00124, 18, -6.14, -13.21, 0.91097, 2, 16, 17.13, 6.78, 0.26257, 18, -10.41, -9.35, 0.73743, 2, 16, 11.96, 5.32, 0.59738, 18, -15.59, -10.81, 0.40262, 2, 16, 8.7, 0.76, 0.87789, 18, -18.85, -15.37, 0.12211, 2, 16, 6.45, -5.64, 0.99225, 18, -21.1, -21.77, 0.00775, 1, 16, 1.57, -6.96, 1, 1, 16, -4.43, -5.52, 1, 2, 16, -6.49, 0.1, 0.99872, 18, -34.03, -16.03, 0.00128, 2, 16, -6.57, 5.45, 0.97322, 18, -34.12, -10.68, 0.02678, 2, 16, -3.55, 9.31, 0.89729, 18, -31.1, -6.82, 0.10271, 2, 16, -0.66, 14.47, 0.7469, 18, -28.2, -1.66, 0.2531, 2, 16, 4.03, 18.95, 0.57093, 18, -23.51, 2.82, 0.42907, 2, 16, 9.58, 22.01, 0.39719, 18, -17.97, 5.88, 0.60281, 2, 16, 13.97, 22.44, 0.2478, 18, -13.57, 6.31, 0.7522, 1, 18, -8.79, 12.69, 1, 1, 18, -3.01, 15.88, 1, 4, 16, 25.49, 38.06, 0.00167, 17, -46.68, 34.53, 0.0011, 18, -2.06, 21.93, 0.25094, 19, -1.13, 1.59, 0.74629, 1, 19, 16.49, 7.91, 1, 1, 19, 34.75, 0.77, 1, 1, 19, 28.03, -4.48, 1, 1, 19, 14.47, -5.56, 1, 4, 16, 29.44, 38.1, 0.00036, 17, -42.73, 34.57, 0.00266, 18, 1.9, 21.97, 0.25055, 19, -0.07, -2.21, 0.74642, 1, 18, 4.97, 15.41, 1, 1, 18, 12.69, 12.7, 1, 2, 17, -27.72, 19.46, 0.21273, 18, 16.91, 6.86, 0.78727, 2, 17, -19.08, 18.06, 0.45733, 18, 25.54, 5.46, 0.54267, 2, 17, -9.82, 14.55, 0.71998, 18, 34.81, 1.95, 0.28002, 2, 17, -1.62, 11.02, 0.90801, 18, 43, -1.58, 0.09199, 2, 17, 4.98, 4.16, 0.99678, 18, 49.6, -8.44, 0.00322, 1, 17, 6.33, -1.95, 1, 2, 17, -2.65, 1.44, 0.97212, 18, 41.97, -11.15, 0.02788, 2, 17, -11.6, 7.68, 0.7432, 18, 33.03, -4.92, 0.2568, 2, 17, -20.74, 10.84, 0.46125, 18, 23.89, -1.76, 0.53875, 2, 17, -28.54, 11.78, 0.24194, 18, 16.08, -0.82, 0.75806, 2, 16, 14.6, 15.09, 0.31463, 18, -12.94, -1.04, 0.68537, 2, 16, 7.58, 11.32, 0.6173, 18, -19.96, -4.81, 0.3827, 2, 16, 0.86, 3.66, 0.946, 18, -26.69, -12.47, 0.054, 1, 16, -0.73, -0.5, 1, 4, 16, 27.68, 38.14, 0.00083, 17, -44.5, 34.61, 0.00169, 18, 0.13, 22.01, 0.23886, 19, -0.48, -0.5, 0.75862, 1, 19, 15.64, -0.09, 1, 2, 17, -42.35, 12.03, 0.01396, 18, 2.27, -0.57, 0.98604], "hull": 38, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 0, 74, 0, 76, 76, 78, 78, 80, 80, 82, 84, 86, 86, 88, 88, 90, 48, 92, 92, 58, 92, 94, 94, 52], "width": 85, "height": 74}}, "bd1": {"bd1": {"type": "mesh", "uvs": [0.19695, 0, 0.29538, 0.09708, 0.41287, 0.1348, 0.57004, 0.12851, 0.6907, 0.0908, 0.77961, 0, 0.83676, 0.11594, 0.87804, 0.34223, 0.86217, 0.41765, 0.91456, 0.5528, 0.97171, 0.69737, 1, 0.8828, 0.91456, 0.77908, 0.79072, 0.77594, 0.72404, 0.63765, 0.678, 0.45537, 0.67011, 0.40818, 0.62174, 0.43039, 0.55585, 0.43177, 0.49696, 0.40957, 0.46261, 0.37488, 0.44386, 0.47661, 0.405, 0.60661, 0.34201, 0.6809, 0.3085, 0.77906, 0.24149, 0.85069, 0.15974, 0.83212, 0.09273, 0.83743, 0.05252, 0.91171, 0.0217, 1, 0, 0.94355, 0.02706, 0.75518, 0.07263, 0.50314, 0.10881, 0.30682, 0.14902, 0.15559, 0.1785, 0.15029, 0.18386, 0.05743, 0.93164, 0.72651, 0.83927, 0.62986, 0.76933, 0.4888, 0.75482, 0.33729, 0.06863, 0.76047, 0.18344, 0.58545, 0.26129, 0.39215, 0.27977, 0.29549, 0.28372, 0.19623, 0.37609, 0.23802, 0.44471, 0.26153, 0.56875, 0.26676, 0.66376, 0.25631, 0.74954, 0.18317], "triangles": [46, 2, 47, 47, 2, 3, 48, 3, 49, 48, 47, 3, 49, 4, 50, 49, 3, 4, 50, 5, 6, 50, 4, 5, 20, 47, 48, 44, 45, 46, 44, 35, 45, 45, 1, 46, 46, 1, 2, 45, 35, 1, 35, 36, 1, 36, 0, 1, 17, 48, 49, 20, 46, 47, 49, 50, 40, 40, 50, 6, 46, 43, 44, 35, 33, 34, 43, 35, 44, 12, 37, 11, 37, 10, 11, 13, 38, 12, 12, 38, 37, 13, 14, 38, 38, 9, 37, 37, 9, 10, 14, 39, 38, 14, 15, 39, 38, 8, 9, 38, 39, 8, 15, 40, 39, 39, 40, 8, 15, 16, 40, 17, 18, 48, 18, 19, 48, 17, 49, 16, 8, 40, 7, 16, 49, 40, 40, 6, 7, 29, 30, 28, 30, 31, 28, 28, 41, 27, 28, 31, 41, 24, 25, 42, 27, 41, 26, 25, 26, 42, 26, 41, 42, 24, 42, 23, 42, 41, 32, 41, 31, 32, 42, 43, 23, 23, 43, 22, 22, 43, 21, 46, 21, 43, 32, 33, 42, 42, 33, 43, 35, 43, 33, 20, 21, 46, 19, 20, 48], "vertices": [2, 6, -13.4, -10.17, 0.2, 2, 27.48, -5.35, 0.8, 3, 6, -10.24, 0.02, 0.19627, 9, -35.68, -24.24, 0.00373, 2, 17.86, -0.75, 0.8, 3, 6, -10.25, 11.57, 0.15646, 9, -27.62, -15.98, 0.04354, 2, 6.43, 0.92, 0.8, 3, 6, -13.01, 26.56, 0.05791, 9, -19.1, -3.33, 0.14209, 2, -8.81, 0.37, 0.8, 3, 6, -16.72, 37.82, 0.00882, 9, -13.89, 7.31, 0.19118, 2, -20.48, -1.66, 0.8, 3, 6, -22.5, 45.61, 0.00028, 9, -12.57, 16.92, 0.19972, 2, -29.03, -6.24, 0.8, 2, 9, -4.73, 18.19, 0.2, 2, -34.67, -0.65, 0.8, 1, 9, 6.64, 15.09, 1, 1, 9, 8.78, 11.7, 1, 1, 9, 17.12, 12.05, 1, 1, 9, 26.1, 12.52, 1, 1, 9, 35.12, 9.54, 1, 1, 9, 26.19, 5.68, 1, 1, 9, 19.16, -4.06, 1, 1, 9, 9.9, -5.46, 1, 2, 6, 1.1, 39.48, 0.00802, 9, 0.02, -3.97, 0.99198, 2, 6, -1.05, 38.35, 0.04546, 9, -2.31, -3.27, 0.95454, 2, 6, 0.78, 33.9, 0.16463, 9, -4.12, -7.73, 0.83537, 2, 6, 1.87, 27.6, 0.3229, 9, -7.74, -13, 0.6771, 2, 6, 1.72, 21.79, 0.49671, 9, -11.91, -17.05, 0.50329, 2, 6, 0.58, 18.22, 0.6821, 9, -15.22, -18.8, 0.3179, 2, 6, 5.79, 17.23, 0.87243, 9, -12.19, -23.15, 0.12757, 2, 6, 12.69, 14.54, 0.9584, 9, -9.14, -29.9, 0.0416, 2, 6, 17.26, 9.09, 0.99237, 9, -9.68, -36.99, 0.00763, 2, 6, 22.53, 6.66, 0.99972, 9, -7.61, -42.42, 0.00028, 1, 6, 27.05, 0.81, 1, 1, 6, 27.42, -7.16, 1, 1, 6, 28.73, -13.53, 1, 1, 6, 32.95, -16.8, 1, 1, 6, 37.7, -19.05, 1, 1, 6, 35.31, -21.57, 1, 1, 6, 25.78, -20.47, 1, 1, 6, 12.88, -18.1, 1, 1, 6, 2.82, -16.18, 1, 2, 6, -5.12, -13.53, 0.2, 2, 32.01, 2.34, 0.8, 2, 6, -5.84, -10.75, 0.2, 2, 29.15, 2.04, 0.8, 2, 6, -10.42, -10.97, 0.2, 2, 28.7, -2.52, 0.8, 1, 9, 25.04, 8.51, 1, 1, 9, 16.01, 3.91, 1, 1, 9, 6.45, 2.33, 1, 2, 6, -5.81, 45.9, 0.00139, 9, -0.43, 5.45, 0.99861, 1, 6, 25.38, -16.45, 1, 1, 6, 15.13, -6.84, 1, 1, 6, 4.56, -0.91, 1, 3, 6, -0.4, 0.09, 0.19979, 9, -28.6, -31.07, 0.00021, 2, 19.22, 9, 0.8, 3, 6, -5.26, -0.31, 0.19753, 9, -32.36, -27.96, 0.00247, 2, 18.91, 4.13, 0.8, 3, 6, -4.69, 8.86, 0.17446, 9, -25.53, -21.81, 0.02554, 2, 9.92, 6.03, 0.8, 3, 6, -4.62, 15.62, 0.13981, 9, -20.76, -17.02, 0.06019, 2, 3.25, 7.08, 0.8, 3, 6, -6.31, 27.53, 0.05989, 9, -13.63, -7.33, 0.14011, 2, -8.79, 7.15, 0.8, 3, 6, -8.3, 36.54, 0.01659, 9, -8.75, 0.51, 0.18341, 2, -18, 6.49, 0.8, 3, 6, -13.18, 44.18, 0.00191, 9, -6.9, 9.37, 0.19809, 2, -26.26, 2.78, 0.8], "hull": 37, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 0, 72, 22, 74, 74, 76, 76, 78, 78, 80, 56, 82, 82, 84, 84, 86, 86, 88, 70, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100], "width": 97, "height": 49}}, "bd2": {"bd2": {"type": "mesh", "uvs": [0.07798, 0.12082, 0.2231, 0.06781, 0.4526, 0.04822, 0.68885, 0.07242, 0.78335, 0.16577, 0.8441, 0.27294, 0.9116, 0.38473, 0.97235, 0.49421, 1, 0.61061, 1, 0.71087, 0.88087, 0.83844, 0.74396, 0.93308, 0.60037, 0.99693, 0.41671, 1, 0.19966, 0.96615, 0.016, 0.92966, 0.00264, 0.85668, 0, 0.78713, 0.13955, 0.72328, 0.21302, 0.63206, 0.25309, 0.50435, 0.25309, 0.39717, 0.22303, 0.2763, 0.14289, 0.18394, 0.45678, 0.15315, 0.54027, 0.26718, 0.57366, 0.38576, 0.60037, 0.50549, 0.60705, 0.62521, 0.54694, 0.73696, 0.3733, 0.83388, 0.2798, 0.92396, 0.47348, 0.92282, 0.13955, 0.85896], "triangles": [14, 31, 13, 13, 32, 12, 13, 31, 32, 12, 32, 11, 14, 15, 31, 11, 32, 10, 15, 33, 31, 15, 16, 33, 31, 30, 32, 31, 33, 30, 32, 30, 10, 30, 33, 17, 33, 16, 17, 30, 29, 10, 17, 18, 30, 10, 29, 9, 30, 18, 29, 18, 19, 29, 29, 19, 28, 29, 28, 9, 20, 28, 19, 28, 8, 9, 28, 27, 8, 28, 20, 27, 27, 7, 8, 27, 21, 26, 27, 6, 7, 27, 26, 6, 27, 20, 21, 26, 21, 22, 26, 5, 6, 26, 22, 25, 26, 25, 5, 25, 22, 24, 25, 4, 5, 22, 23, 24, 25, 24, 4, 23, 0, 24, 24, 3, 4, 0, 1, 24, 1, 2, 24, 24, 2, 3], "vertices": [1, 12, -5.51, -19.17, 1, 1, 12, -11.87, -9.11, 1, 1, 12, -11.74, 4.13, 1, 1, 12, -4.57, 15.94, 1, 1, 12, 11.58, 17.2, 1, 2, 12, 29.44, 16.07, 0.79383, 13, -8.79, 14.28, 0.20617, 3, 12, 48.14, 15.11, 0.03733, 13, 9.52, 18.18, 0.95942, 14, -26.76, 15.09, 0.00325, 2, 13, 27.45, 21.7, 0.73413, 14, -9.37, 20.73, 0.26587, 2, 13, 46.53, 23.38, 0.11983, 14, 9.37, 24.68, 0.88017, 3, 13, 62.97, 23.49, 0.00135, 14, 25.68, 26.75, 0.97315, 15, -19.36, 23.11, 0.0255, 2, 14, 47.28, 22.77, 0.45378, 15, 2.58, 23.9, 0.54622, 2, 14, 63.64, 17.12, 0.04683, 15, 19.78, 21.92, 0.95317, 2, 14, 75.04, 10.47, 0.00015, 15, 32.35, 17.89, 0.99985, 1, 15, 36.3, 8.38, 1, 1, 15, 35.18, -4.94, 1, 1, 15, 33.02, -16.64, 1, 2, 14, 56.45, -25.64, 0.01882, 15, 22.01, -21.39, 0.98118, 2, 14, 45.15, -27.22, 0.13005, 15, 11.32, -25.38, 0.86995, 2, 14, 33.78, -20.79, 0.52914, 15, -1.18, -21.56, 0.47086, 3, 13, 50.34, -20.67, 0.02092, 14, 18.42, -18.6, 0.94007, 15, -16.65, -22.74, 0.03901, 2, 13, 29.38, -18.56, 0.68439, 14, -2.64, -19.02, 0.31561, 3, 12, 40.82, -21.09, 0.12001, 13, 11.8, -18.68, 0.87756, 14, -20.08, -21.23, 0.00243, 2, 12, 21.22, -17.73, 0.85316, 13, -8.01, -20.49, 0.14684, 2, 12, 5.43, -18.26, 0.99895, 13, -23.12, -25.08, 0.00105, 1, 12, 4.97, 0.03, 1, 2, 12, 24.24, -0.16, 0.99943, 13, -9.62, -2.74, 0.00057, 2, 12, 43.54, -3.25, 0.00103, 13, 9.81, -0.74, 0.99897, 1, 13, 29.44, 0.89, 1, 1, 14, 14.52, 3.15, 1, 1, 14, 33.12, 2.12, 1, 2, 14, 50.12, -5.52, 0.00178, 15, 11.48, -3.11, 0.99822, 1, 15, 27.15, -3.05, 1, 2, 14, 63.88, 1.89, 0.00035, 15, 23.31, 7.1, 0.99965, 2, 14, 55.85, -17.99, 0.0124, 15, 19.77, -14.04, 0.9876], "hull": 24, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 0, 46, 4, 48, 2, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 26, 64, 30, 66], "width": 56, "height": 164}}, "bd3": {"bd3": {"type": "mesh", "uvs": [0, 0.8236, 0.04228, 0.63655, 0.08767, 0.41322, 0.15413, 0.22339, 0.28056, 0.06706, 0.42159, 0, 0.61124, 0.03635, 0.71174, 0.13126, 0.79279, 0.29039, 0.87546, 0.46905, 0.9484, 0.62818, 1, 0.7873, 0.98082, 0.86268, 0.87384, 0.91293, 0.74578, 0.9548, 0.58044, 0.99668, 0.41672, 1, 0.27894, 0.94364, 0.11847, 0.94364, 0.02607, 0.89897, 0.48318, 0.23735, 0.52695, 0.5528, 0.56423, 0.79847, 0.65987, 0.2178, 0.71174, 0.44672, 0.76685, 0.7566, 0.25949, 0.24293, 0.25625, 0.48301, 0.25301, 0.72868], "triangles": [25, 22, 24, 25, 24, 9, 25, 9, 10, 12, 10, 11, 13, 25, 10, 12, 13, 10, 14, 22, 25, 14, 25, 13, 15, 22, 14, 23, 6, 7, 20, 5, 6, 20, 6, 23, 23, 7, 8, 24, 23, 8, 24, 8, 9, 21, 20, 23, 21, 23, 24, 22, 21, 24, 2, 3, 27, 27, 1, 2, 28, 1, 27, 21, 27, 20, 19, 0, 1, 18, 19, 1, 28, 18, 1, 21, 28, 27, 21, 16, 28, 28, 16, 17, 18, 28, 17, 21, 22, 16, 15, 16, 22, 26, 3, 4, 27, 3, 26, 4, 5, 20, 26, 4, 20, 27, 26, 20], "vertices": [1, 7, 37.23, -28.12, 1, 1, 7, 23.31, -24.24, 1, 2, 6, 28.45, -19.78, 0.08, 7, 6.75, -20.24, 0.92, 2, 6, 13.63, -13.85, 0.72473, 7, -7.67, -13.4, 0.27527, 1, 6, 0, -0.19, 1, 3, 6, -7.58, 16.29, 0.64196, 7, -26.97, 18, 0.00237, 9, -22.41, -14.47, 0.35566, 2, 6, -8.79, 39.92, 0.03565, 9, -6.75, 3.26, 0.96435, 1, 9, 6.01, 9.53, 1, 2, 9, 21.16, 11.16, 0.9992, 10, -16.4, 6.32, 0.0008, 2, 9, 37.58, 12.15, 0.34422, 10, -1.13, 12.42, 0.65578, 2, 9, 52.16, 12.96, 0.00049, 10, 12.45, 17.78, 0.99951, 1, 10, 25.27, 20.59, 1, 1, 10, 29.78, 16.75, 1, 1, 10, 29.41, 3, 1, 3, 7, 37.43, 64.84, 0.07685, 9, 56.95, -21.12, 0.02566, 10, 27.71, -13.07, 0.89749, 3, 7, 42.47, 44.74, 0.37904, 9, 47.63, -39.62, 0.1112, 10, 24.68, -33.57, 0.50975, 3, 7, 44.73, 24.56, 0.71749, 9, 36.15, -56.37, 0.078, 10, 19.05, -53.07, 0.20451, 3, 7, 42.39, 7.16, 0.94949, 9, 23, -68.01, 0.01427, 10, 10.23, -68.26, 0.03624, 1, 7, 44.37, -12.64, 1, 1, 7, 42.31, -24.36, 1, 4, 6, 8.05, 26.58, 0.3091, 7, -10.73, 27.3, 0.12178, 9, -4.04, -18.05, 0.56406, 10, -31.15, -29.33, 0.00506, 4, 6, 29.59, 35.59, 0.03465, 7, 11.33, 34.96, 0.36571, 9, 17.67, -26.67, 0.44681, 10, -7.83, -30.69, 0.15284, 4, 6, 46.3, 43.01, 0.00025, 7, 28.47, 41.32, 0.38318, 9, 34.79, -33.06, 0.19268, 10, 10.44, -31.37, 0.42388, 1, 9, 7.41, 0.68, 1, 4, 6, 18.36, 56.98, 0.00031, 7, 1.45, 57, 0.01258, 9, 24.59, -3.53, 0.95576, 10, -8.53, -6.55, 0.03135, 3, 7, 22.97, 66.02, 0.02718, 9, 46.78, -10.77, 0.02697, 10, 14.8, -6.45, 0.94585, 1, 6, 12.92, -0.73, 1, 4, 6, 30.04, 1.66, 0.00067, 7, 9.67, 1.06, 0.99621, 9, -5.75, -51.25, 0.00259, 10, -22.34, -61.38, 0.00053, 3, 7, 27.31, 2.42, 0.99111, 9, 8.49, -61.75, 0.00399, 10, -5.52, -66.87, 0.00491], "hull": 20, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 0, 38, 10, 40, 40, 42, 42, 44, 12, 46, 46, 48, 48, 50, 8, 52, 52, 54, 54, 56], "width": 124, "height": 72}}, "bd4": {"bd4": {"type": "mesh", "uvs": [0.09308, 0.17723, 0.07029, 0.31648, 0.05357, 0.49238, 0.0323, 0.66339, 0.00342, 0.86617, 0, 0.98099, 0.10372, 1, 0.19186, 0.959, 0.24505, 0.89793, 0.36055, 0.93457, 0.4806, 0.96145, 0.5733, 0.93701, 0.72071, 0.87838, 0.84229, 0.77822, 0.94107, 0.8002, 0.9973, 0.75379, 0.96842, 0.63164, 0.97602, 0.49483, 1, 0.3629, 0.99122, 0.187, 0.90308, 0.07707, 0.74959, 0.00133, 0.56874, 0, 0.40462, 0, 0.25417, 0.04286, 0.16451, 0.0844, 0.56115, 0.32381, 0.56571, 0.52658, 0.56874, 0.75623, 0.77086, 0.26762, 0.77542, 0.5388, 0.36511, 0.29694, 0.36663, 0.5217, 0.37422, 0.766, 0.1949, 0.30671, 0.19338, 0.51681, 0.19946, 0.73913, 0.07333, 0.85151, 0.09916, 0.68538, 0.1174, 0.45818, 0.89396, 0.29205, 0.87572, 0.52658, 0.897, 0.68782], "triangles": [5, 37, 6, 6, 37, 7, 5, 4, 37, 9, 33, 10, 10, 33, 28, 8, 7, 36, 9, 8, 33, 7, 37, 36, 8, 36, 33, 4, 3, 37, 37, 38, 36, 37, 3, 38, 36, 32, 33, 33, 27, 28, 33, 32, 27, 38, 35, 36, 36, 35, 32, 3, 2, 38, 38, 39, 35, 38, 2, 39, 32, 26, 27, 35, 34, 32, 34, 31, 32, 32, 31, 26, 35, 39, 34, 2, 1, 39, 39, 1, 34, 1, 0, 34, 31, 23, 26, 26, 23, 22, 0, 25, 34, 34, 24, 31, 34, 25, 24, 31, 24, 23, 10, 28, 11, 11, 28, 12, 13, 12, 30, 13, 42, 14, 14, 42, 15, 12, 28, 30, 42, 30, 41, 42, 13, 30, 28, 27, 30, 42, 16, 15, 42, 41, 16, 16, 41, 17, 30, 26, 29, 30, 29, 41, 27, 26, 30, 41, 40, 17, 21, 29, 22, 22, 29, 26, 41, 29, 40, 17, 40, 18, 40, 19, 18, 40, 20, 19, 40, 29, 20, 29, 21, 20], "vertices": [2, 7, 29.29, -22.06, 0.53248, 8, -3.69, -22.08, 0.46752, 2, 7, 40.53, -23.84, 0.22784, 8, 7.55, -23.8, 0.77216, 2, 7, 54.56, -24.57, 0.02729, 8, 21.6, -24.44, 0.97271, 1, 8, 35.32, -25.7, 1, 1, 8, 51.63, -27.65, 1, 1, 8, 60.7, -27.12, 1, 1, 8, 60.8, -13.86, 1, 2, 11, 72.69, -66.74, 0.00091, 8, 56.39, -3.07, 0.99909, 2, 11, 66.06, -61.74, 0.02211, 8, 50.88, 3.13, 0.97789, 2, 11, 64.43, -46.88, 0.17594, 8, 52.21, 18.02, 0.82406, 2, 11, 61.89, -31.7, 0.38982, 8, 52.71, 33.41, 0.61018, 2, 11, 56.53, -21.04, 0.57008, 8, 49.54, 44.91, 0.42992, 2, 11, 46.5, -4.57, 0.8685, 8, 42.96, 63.04, 0.1315, 2, 11, 34.33, 7.79, 0.99879, 8, 33.45, 77.56, 0.00121, 1, 11, 32.23, 20.28, 1, 1, 11, 26.59, 26, 1, 2, 10, 53.42, 5.83, 0.00034, 11, 18.48, 19.61, 0.99966, 2, 10, 43.35, 9.87, 0.08579, 11, 7.88, 17.29, 0.91421, 2, 10, 34.25, 15.8, 0.48951, 11, -2.98, 17.08, 0.51049, 2, 10, 20.62, 18.74, 0.945, 11, -15.9, 11.85, 0.055, 1, 10, 9.08, 10.53, 1, 4, 10, -2.28, -6.4, 0.93323, 11, -20.71, -21.82, 0.01194, 7, 7.17, 59.52, 0.05145, 8, -26.33, 59.36, 0.00338, 4, 10, -9.01, -28.36, 0.49653, 11, -13.93, -43.76, 0.06401, 7, 9.35, 36.66, 0.40353, 8, -24.01, 36.51, 0.03593, 4, 10, -15.03, -48.32, 0.1435, 11, -7.69, -63.65, 0.02426, 7, 11.42, 15.92, 0.81815, 8, -21.8, 15.78, 0.0141, 1, 7, 16.69, -2.76, 1, 2, 7, 21.09, -13.76, 0.85531, 8, -11.95, -13.84, 0.14469, 4, 10, 15.2, -36.67, 0.28706, 11, 10.77, -37.02, 0.2649, 7, 34.9, 38.24, 0.23008, 8, 1.53, 38.25, 0.21796, 4, 10, 30.7, -40.74, 0.10778, 11, 25.88, -31.67, 0.47173, 7, 50.78, 40.41, 0.07658, 8, 17.4, 40.52, 0.3439, 4, 10, 48.19, -45.61, 0.0135, 11, 43.07, -25.87, 0.57601, 7, 68.8, 42.6, 0.00884, 8, 35.4, 42.83, 0.40165, 4, 10, 18.64, -9.89, 0.73318, 11, -1.44, -12.94, 0.22551, 7, 27.83, 64.3, 0.02759, 8, -5.7, 64.27, 0.01371, 4, 10, 39.32, -15.52, 0.02706, 11, 18.82, -5.97, 0.94886, 7, 49.09, 67.01, 0.00627, 8, 15.54, 67.11, 0.01781, 4, 10, 5.98, -59.9, 0.05575, 11, 16.2, -61.41, 0.0462, 7, 35.26, 13.26, 0.38552, 8, 2.06, 13.27, 0.51253, 4, 10, 23.03, -64.84, 0.02493, 11, 33.08, -55.91, 0.10136, 7, 52.91, 15.22, 0.03357, 8, 19.69, 15.34, 0.84015, 4, 10, 41.79, -69.49, 0.00357, 11, 51.21, -49.21, 0.16858, 7, 72.02, 18.1, 0.00075, 8, 38.78, 18.34, 0.82711, 2, 7, 38.18, -8.17, 0.21434, 8, 5.11, -8.14, 0.78566, 2, 7, 54.72, -6.71, 0.00256, 8, 21.64, -6.58, 0.99744, 1, 8, 39.02, -3.95, 1, 1, 8, 49.54, -18.94, 1, 1, 8, 36.14, -17.07, 1, 2, 7, 51.07, -16.78, 0.04455, 8, 18.05, -16.66, 0.95545, 2, 10, 25, 4.52, 0.95584, 11, -4.28, 2.55, 0.04416, 1, 11, 14.09, 5.89, 1, 1, 11, 25.43, 12.28, 1], "hull": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 0, 50, 44, 52, 52, 54, 54, 56, 42, 58, 58, 60, 46, 62, 62, 64, 64, 66, 48, 68, 68, 70, 70, 72, 10, 74, 74, 76, 76, 78, 40, 80, 80, 82, 82, 84], "width": 127, "height": 79}}, "wuqi": {"wuqi": {"x": 1.3, "width": 313, "height": 66}}, "guanglun2": {"guanglun": {"x": 2.21, "y": 7.73, "width": 244, "height": 236}}, "toufa1": {"toufa1": {"type": "mesh", "uvs": [0, 0.16141, 0.07841, 0.09312, 0.23501, 0.04136, 0.41004, 0.0028, 0.58046, 0.04246, 0.70022, 0.10965, 0.8384, 0.19115, 0.93052, 0.3002, 0.97658, 0.3817, 0.92361, 0.4588, 0.86143, 0.52709, 0.7547, 0.59514, 0.68152, 0.6555, 0.67197, 0.71434, 0.77365, 0.76633, 0.83956, 0.82612, 0.83274, 0.90329, 0.78047, 0.96416, 0.63729, 0.98264, 0.4941, 0.95329, 0.44865, 0.89786, 0.46683, 0.85764, 0.57365, 0.89025, 0.62819, 0.89568, 0.63729, 0.85655, 0.52365, 0.8196, 0.43729, 0.77286, 0.3941, 0.70981, 0.41229, 0.62938, 0.4441, 0.54025, 0.5191, 0.47612, 0.58047, 0.40873, 0.48274, 0.33916, 0.43274, 0.27938, 0.3191, 0.30003, 0.28501, 0.23808, 0.13956, 0.21742, 0.03274, 0.19134, 0.24017, 0.15386, 0.43974, 0.14099, 0.62138, 0.20534, 0.70659, 0.2772, 0.7492, 0.3705, 0.74023, 0.44879, 0.64604, 0.51957, 0.58326, 0.60751, 0.54514, 0.68044, 0.54738, 0.74479, 0.65277, 0.78769, 0.73574, 0.84667, 0.69986, 0.92711, 0.58998, 0.93998, 0.50477, 0.90459], "triangles": [52, 20, 21, 16, 49, 15, 22, 52, 21, 49, 23, 24, 50, 49, 16, 50, 23, 49, 51, 22, 23, 51, 23, 50, 52, 22, 51, 19, 20, 52, 19, 52, 51, 17, 50, 16, 18, 51, 50, 18, 50, 17, 19, 51, 18, 46, 28, 45, 46, 45, 12, 27, 28, 46, 13, 46, 12, 47, 46, 13, 27, 46, 47, 26, 27, 47, 48, 47, 13, 48, 13, 14, 25, 26, 47, 25, 47, 48, 49, 48, 14, 49, 14, 15, 24, 25, 48, 24, 48, 49, 31, 32, 42, 43, 31, 42, 9, 43, 42, 30, 31, 43, 44, 30, 43, 10, 43, 9, 44, 43, 10, 29, 30, 44, 11, 44, 10, 45, 29, 44, 45, 44, 11, 28, 29, 45, 12, 45, 11, 39, 4, 5, 40, 39, 5, 40, 5, 6, 41, 40, 6, 33, 35, 39, 33, 39, 40, 33, 40, 41, 34, 35, 33, 41, 6, 7, 32, 33, 41, 42, 41, 7, 32, 41, 42, 42, 7, 8, 8, 9, 42, 39, 3, 4, 2, 3, 39, 38, 2, 39, 1, 2, 38, 0, 1, 38, 37, 0, 38, 36, 37, 38, 35, 38, 39, 36, 38, 35], "vertices": [1, 65, -7.65, 4.24, 1, 1, 65, -2.97, 6.89, 1, 1, 65, 3.31, 7.21, 1, 2, 65, 9.62, 6.44, 0.91202, 66, -6.6, 1.89, 0.08798, 2, 65, 12.97, 1.15, 0.27313, 66, -1.59, 5.63, 0.72687, 1, 66, 4.35, 7.01, 1, 1, 66, 11.44, 8.48, 1, 2, 66, 19.52, 7.72, 0.99988, 67, -3.19, 7.44, 0.00012, 2, 66, 25.21, 6.48, 0.8199, 67, 2.12, 9.84, 0.1801, 2, 66, 29.13, 2.49, 0.38121, 67, 7.65, 8.97, 0.61879, 2, 66, 32.38, -1.5, 0.09483, 67, 12.63, 7.7, 0.90517, 2, 66, 34.93, -6.78, 0.00389, 67, 17.83, 4.97, 0.99611, 2, 67, 22.33, 3.26, 0.55338, 68, 2.94, 2.46, 0.44662, 2, 67, 26.38, 3.6, 0.00182, 68, 6.82, 1.2, 0.99818, 2, 68, 11.09, 3.62, 0.9947, 69, -5.14, 2.38, 0.0053, 2, 68, 15.61, 4.77, 0.56133, 69, -1.07, 4.65, 0.43867, 2, 68, 20.74, 3.31, 0.00974, 69, 4.26, 4.55, 0.99026, 1, 69, 8.5, 2.92, 1, 1, 69, 9.88, -1.78, 1, 1, 69, 7.96, -6.55, 1, 1, 69, 4.17, -8.13, 1, 1, 69, 1.38, -7.59, 1, 1, 69, 3.55, -4.02, 1, 1, 69, 3.89, -2.21, 1, 2, 68, 16.09, -2.21, 0.32541, 69, 1.18, -1.97, 0.67459, 2, 68, 12.73, -5.26, 0.9944, 69, -1.28, -5.78, 0.0056, 1, 68, 8.93, -7.27, 1, 1, 68, 4.37, -7.64, 1, 2, 67, 21.97, -5.8, 0.00711, 68, -0.89, -5.76, 0.99289, 2, 67, 15.73, -5.75, 0.67699, 68, -6.62, -3.29, 0.32301, 2, 67, 10.97, -4.01, 0.97916, 68, -10.34, 0.15, 0.02084, 1, 67, 6.05, -2.76, 1, 3, 65, -0.54, -14.57, 0.01653, 66, 15.12, -6.64, 0.2468, 67, 1.83, -6.71, 0.73667, 3, 65, 0.23, -10.19, 0.19811, 66, 10.69, -6.21, 0.63389, 67, -1.98, -9, 0.168, 3, 65, -3.71, -9.43, 0.40525, 66, 10.24, -10.2, 0.53827, 67, 0.03, -12.47, 0.05648, 3, 65, -2.42, -5.21, 0.66381, 66, 5.92, -9.23, 0.31737, 67, -4.01, -14.27, 0.01882, 3, 65, -5.76, -1.47, 0.98439, 66, 2.45, -12.84, 0.01548, 67, -4.65, -19.24, 0.00013, 1, 65, -7.81, 1.91, 1, 1, 65, -0.63, 0.52, 1, 2, 65, 5.44, -2.19, 0.36878, 66, 2.32, -1.62, 0.63122, 1, 66, 9.02, 1.66, 1, 1, 66, 14.71, 1.88, 1, 2, 66, 21.08, 0.17, 0.53439, 67, 2.56, 2.31, 0.46561, 2, 66, 25.74, -2.57, 0.11175, 67, 7.94, 2.89, 0.88825, 2, 66, 28.65, -7.57, 0.00151, 67, 13.25, 0.6, 0.99849, 2, 67, 19.58, -0.47, 0.86066, 68, -1.03, 0.08, 0.13934, 1, 68, 3.56, -2.32, 1, 1, 68, 7.9, -3.29, 1, 1, 68, 11.59, -0.6, 1, 2, 68, 16.19, 1.11, 0.25446, 69, 0.43, 1.26, 0.74554, 1, 69, 6, 0.2, 1, 1, 69, 6.97, -3.4, 1, 1, 69, 4.59, -6.27, 1], "hull": 38, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 0, 74, 72, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104], "width": 33, "height": 69}}, "toufa2": {"toufa2": {"type": "mesh", "uvs": [1, 0.20595, 0.94159, 0.10537, 0.81779, 0.0107, 0.63938, 0.01465, 0.40272, 0.05606, 0.24615, 0.13298, 0.15149, 0.16256, 0.01313, 0.12312, 0.0459, 0.20398, 0.14314, 0.24795, 0.07714, 0.32161, 0.04114, 0.42019, 0.04114, 0.53286, 0.05314, 0.63794, 0.06714, 0.68994, 0.01914, 0.75386, 0.01914, 0.83944, 0.04114, 0.89903, 0.12114, 0.97269, 0.24114, 0.98894, 0.31914, 0.94669, 0.35914, 0.85136, 0.30914, 0.76578, 0.33514, 0.69753, 0.32114, 0.62061, 0.33514, 0.55669, 0.47314, 0.48953, 0.58314, 0.42669, 0.61914, 0.35086, 0.66914, 0.32811, 0.70514, 0.27611, 0.82114, 0.26636, 0.92113, 0.24361, 0.81114, 0.16995, 0.66514, 0.1212, 0.50714, 0.17861, 0.37714, 0.24578, 0.29714, 0.35303, 0.10514, 0.1927, 0.25714, 0.23169, 0.22714, 0.44511, 0.18714, 0.54911, 0.17914, 0.63903, 0.18114, 0.70728, 0.15514, 0.77769, 0.16314, 0.85461, 0.20714, 0.93477], "triangles": [44, 43, 22, 44, 15, 43, 16, 15, 44, 45, 44, 22, 45, 22, 21, 16, 44, 45, 17, 16, 45, 46, 45, 21, 17, 45, 46, 20, 46, 21, 18, 17, 46, 19, 46, 20, 18, 46, 19, 41, 12, 40, 25, 41, 40, 24, 41, 25, 13, 12, 41, 42, 13, 41, 24, 42, 41, 14, 13, 42, 42, 24, 23, 43, 42, 23, 14, 42, 43, 22, 43, 23, 43, 15, 14, 9, 38, 39, 37, 36, 28, 9, 39, 37, 10, 9, 37, 40, 11, 10, 27, 37, 28, 37, 40, 10, 26, 40, 37, 27, 26, 37, 12, 11, 40, 26, 25, 40, 35, 4, 34, 5, 4, 35, 38, 7, 6, 8, 7, 38, 36, 39, 5, 6, 5, 39, 38, 6, 39, 35, 36, 5, 8, 38, 9, 35, 34, 30, 30, 36, 35, 30, 28, 36, 29, 28, 30, 37, 39, 36, 34, 3, 2, 34, 2, 1, 4, 3, 34, 33, 34, 1, 33, 1, 0, 32, 33, 0, 31, 33, 32, 30, 34, 33, 30, 33, 31], "vertices": [1, 70, -6.63, -4.3, 1, 1, 70, 0.1, -7.81, 1, 1, 70, 8.33, -9.22, 1, 2, 70, 13.05, -4.1, 0.73363, 71, -4.47, -4.24, 0.26637, 1, 71, 3.91, -9.13, 1, 2, 71, 12.11, -9.98, 0.88137, 72, -7.56, -7.73, 0.11863, 2, 71, 16.16, -11.31, 0.65303, 72, -4.29, -10.46, 0.34697, 2, 71, 17.65, -17.22, 0.60765, 72, -5.11, -16.51, 0.39235, 2, 71, 21.13, -12.39, 0.57884, 72, -0.08, -13.3, 0.42116, 2, 71, 20.96, -7.45, 0.35006, 72, 1.59, -8.65, 0.64994, 2, 71, 26.63, -5.83, 0.01627, 72, 7.46, -9.25, 0.98373, 2, 72, 14.6, -8.12, 0.98124, 73, -6.74, -6.65, 0.01876, 2, 72, 22.22, -5.33, 0.2499, 73, 1.33, -5.85, 0.7501, 1, 73, 8.81, -4.64, 1, 2, 73, 12.49, -3.73, 0.97856, 74, -4.5, -3.19, 0.02144, 2, 73, 17.25, -5.14, 0.34867, 74, 0.05, -5.19, 0.65133, 1, 74, 6.21, -5.35, 1, 1, 74, 10.52, -4.61, 1, 1, 74, 15.91, -1.63, 1, 1, 74, 17.2, 3.02, 1, 1, 74, 14.24, 6.14, 1, 2, 73, 22.93, 8.75, 0.00265, 74, 7.42, 7.88, 0.99735, 2, 73, 16.99, 6.2, 0.35416, 74, 1.21, 6.1, 0.64584, 2, 73, 12, 6.73, 0.9081, 74, -3.67, 7.24, 0.0919, 2, 72, 24.4, 7.09, 0.02811, 73, 6.54, 5.64, 0.97189, 2, 72, 19.89, 6.02, 0.45917, 73, 1.91, 5.73, 0.54083, 4, 70, -6.54, 24.66, 0.0009, 71, 25.33, 13.74, 0.02007, 72, 13.5, 9.41, 0.96722, 73, -3.43, 10.61, 0.01182, 3, 70, -6.37, 18.43, 0.0183, 71, 19.1, 13.92, 0.13127, 72, 7.77, 11.89, 0.85043, 3, 70, -3.51, 13.58, 0.10793, 71, 14.09, 11.32, 0.38648, 72, 2.16, 11.33, 0.50559, 3, 70, -3.73, 11.04, 0.20276, 71, 11.57, 11.68, 0.48008, 72, -0.05, 12.59, 0.31716, 3, 70, -2.07, 7.4, 0.50026, 71, 7.85, 10.23, 0.40163, 72, -4.05, 12.62, 0.09811, 3, 70, -4.77, 3.71, 0.90581, 71, 4.31, 13.13, 0.0849, 72, -6.26, 16.63, 0.00929, 3, 70, -6.37, -0.21, 0.99606, 71, 0.49, 14.94, 0.00384, 72, -9.14, 19.73, 0.0001, 1, 70, 0.41, -0.93, 1, 2, 70, 6.92, 0.62, 0.84676, 71, 0.58, 1.63, 0.15324, 1, 71, 7.77, -0.21, 1, 2, 71, 14.75, -0.76, 0.99387, 72, -1.7, -0.14, 0.00613, 1, 72, 6.63, -0.41, 1, 2, 71, 18.98, -11.21, 0.58456, 72, -1.64, -11.41, 0.41544, 2, 71, 17.12, -4.92, 0.54331, 72, -1.04, -4.88, 0.45669, 1, 72, 13.79, -0.69, 1, 1, 73, 1.94, -0.07, 1, 1, 73, 8.41, 0.26, 1, 2, 73, 13.29, 0.82, 0.99092, 74, -3.13, 1.22, 0.00908, 1, 74, 1.91, 0.07, 1, 1, 74, 7.45, 0.23, 1, 1, 74, 13.27, 1.8, 1], "hull": 33, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 0, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 14, 76, 76, 78, 74, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92], "width": 39, "height": 72}}, "biyan": {"biyan": {"x": 0.98, "y": 6.77, "rotation": -84.19, "width": 51, "height": 21}}}}], "events": {"atk": {}}, "animations": {"boss_attack1": {"slots": {"wuqi": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.5, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.7, "color": "ffffff00"}], "attachment": [{"name": "wuqi"}]}, "wuqi2": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.5, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.7, "color": "ffffff00"}], "attachment": [{"name": "wuqi"}]}}, "bones": {"bone2": {"rotate": [{"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -8.82, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "y": -23.41, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "y": 17.7, "curve": "stepped"}, {"time": 0.6333, "y": 17.7, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": 31.98, "y": 2.28, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "bone": {"rotate": [{"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -8.82, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "y": -23.41, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "y": 17.7, "curve": "stepped"}, {"time": 0.6333, "y": 17.7, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": 31.98, "y": 2.28, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -3.25, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "bone4": {"rotate": [{"angle": -1.62, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 1.83, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -3.23, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.9333, "angle": -1.62}]}, "bone5": {"rotate": [{"angle": -3.23, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 1.83, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -3.23}]}, "bone6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -2.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -4.48, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "bone7": {"rotate": [{"angle": -2.24, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -2.39, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -4.48, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.9333, "angle": -2.24}]}, "bone8": {"rotate": [{"angle": -4.48, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -2.39, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -4.48}]}, "bone9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 1.95, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -1.53, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -7.12, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "bone10": {"rotate": [{"angle": -3.56, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 1.95, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -1.53, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -7.12, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.9333, "angle": -3.56}]}, "bone11": {"rotate": [{"angle": -7.12, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 1.95, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -1.53, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -7.12}]}, "bone12": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -12.87, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.6667, "angle": -7.65, "curve": 0.332, "c2": 0.33, "c3": 0.668, "c4": 0.67}, {"time": 0.7333, "angle": -13.1, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.9333}]}, "bone13": {"rotate": [{"angle": -2.89, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -12.87, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.9333, "angle": -2.89}]}, "bone14": {"rotate": [{"angle": -7.65, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -12.87, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.9333, "angle": -7.65}]}, "bone15": {"rotate": [{"angle": -12.56, "curve": 0.279, "c2": 0.12, "c3": 0.754}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -12.87, "curve": 0.312, "c3": 0.646, "c4": 0.35}, {"time": 0.9333, "angle": -12.56}]}, "bone18": {"translate": [{"x": 3.61, "y": 1.46, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 9.83, "y": 3.96, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.9333, "x": 3.61, "y": 1.46}]}, "bone19": {"rotate": [{"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -61.85, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.8, "angle": -22.75, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.8667}]}, "bone20": {"rotate": [{"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -7.25, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "bone21": {"rotate": [{"angle": -6.31, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -7.25, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.9333, "angle": -6.31}]}, "bone22": {"rotate": [{"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -9.14, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}], "translate": [{"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": 7.8, "y": -1.73, "curve": "stepped"}, {"time": 0.7667, "x": 7.8, "y": -1.73, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "bone23": {"rotate": [{"angle": -2.6, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -9.14, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.9333, "angle": -2.6}]}, "bone24": {"rotate": [{"angle": -9.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": "stepped"}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -9.14}]}, "bone25": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -4.22, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 0.5667, "angle": 4.68, "curve": 0.346, "c2": 0.37, "c3": 0.685, "c4": 0.73}, {"time": 0.7333, "angle": -18.23, "curve": 0.378, "c2": 0.61, "c3": 0.721}, {"time": 0.9333}]}, "bone26": {"rotate": [{"angle": -5.17, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -4.22, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 4.68, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -18.23, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.9333, "angle": -5.17}]}, "bone27": {"rotate": [{"angle": -9.35, "curve": 0.369, "c2": 0.63, "c3": 0.707}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -4.22, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 0.7, "angle": 4.68, "curve": 0.346, "c2": 0.37, "c3": 0.685, "c4": 0.73}, {"time": 0.8667, "angle": -18.23, "curve": 0.345, "c2": 0.38, "c3": 0.679, "c4": 0.72}, {"time": 0.9333, "angle": -9.35}]}, "bone28": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -4.22, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 0.5667, "angle": 4.68, "curve": 0.346, "c2": 0.37, "c3": 0.685, "c4": 0.73}, {"time": 0.7333, "angle": -18.23, "curve": 0.378, "c2": 0.61, "c3": 0.721}, {"time": 0.9333}]}, "bone29": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -4.22, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 0.5667, "angle": 4.68, "curve": 0.346, "c2": 0.37, "c3": 0.685, "c4": 0.73}, {"time": 0.7333, "angle": -18.23, "curve": 0.378, "c2": 0.61, "c3": 0.721}, {"time": 0.9333}]}, "bone30": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -15.34, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 0.5667, "angle": 0.21, "curve": 0.346, "c2": 0.37, "c3": 0.685, "c4": 0.73}, {"time": 0.7333, "angle": -20.04, "curve": 0.378, "c2": 0.61, "c3": 0.721}, {"time": 0.9333}]}, "bone31": {"rotate": [{"angle": -5.69, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -15.34, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 0.21, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -20.04, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.9333, "angle": -5.69}]}, "bone32": {"rotate": [{"angle": -10.28, "curve": 0.369, "c2": 0.63, "c3": 0.707}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -15.34, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 0.7, "angle": 0.21, "curve": 0.346, "c2": 0.37, "c3": 0.685, "c4": 0.73}, {"time": 0.8667, "angle": -20.04, "curve": 0.345, "c2": 0.38, "c3": 0.679, "c4": 0.72}, {"time": 0.9333, "angle": -10.28}]}, "bone33": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -15.34, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 0.5667, "angle": 0.21, "curve": 0.346, "c2": 0.37, "c3": 0.685, "c4": 0.73}, {"time": 0.7333, "angle": -20.04, "curve": 0.378, "c2": 0.61, "c3": 0.721}, {"time": 0.9333}]}, "bone34": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 7.04, "curve": 0.25, "c3": 0.75}, {"time": 0.5784, "angle": -3.42, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 5.66, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "bone35": {"rotate": [{"angle": 1.61, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 7.04, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -3.42, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 5.66, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.9333, "angle": 1.61}]}, "bone36": {"rotate": [{"angle": 4.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 7.04, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -3.42, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 5.66, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.9333, "angle": 4.05}]}, "bone37": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 7.04, "curve": 0.25, "c3": 0.75}, {"time": 0.5784, "angle": -3.42, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 5.66, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "bone38": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 7.04, "curve": 0.25, "c3": 0.75}, {"time": 0.5784, "angle": -3.42, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 5.66, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "bone39": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 7.04, "curve": 0.25, "c3": 0.75}, {"time": 0.5784, "angle": -3.42, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 5.66, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "bone40": {"rotate": [{"angle": 1.61, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 7.04, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -3.42, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 5.66, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.9333, "angle": 1.61}]}, "bone41": {"rotate": [{"angle": 4.05, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 7.04, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -3.42, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 5.66, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.9333, "angle": 4.05}]}, "bone44": {"translate": [{"x": -2.04, "y": -6.64, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -12.38, "y": 0.28, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 8.61, "y": -1.18, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -5.54, "y": -18.04, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.9333, "x": -2.04, "y": -6.64}]}, "bone45": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -12.38, "y": 0.28, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 8.61, "y": -1.18, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": -5.54, "y": -18.04, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "bone46": {"translate": [{"x": -2.04, "y": -6.64, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -12.38, "y": 0.28, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 8.61, "y": -1.18, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -5.54, "y": -18.04, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.9333, "x": -2.04, "y": -6.64}]}, "bone47": {"translate": [{"x": -4.82, "y": -15.69, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -12.38, "y": 0.28, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": 8.61, "y": -1.18, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": -5.54, "y": -18.04, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.9333, "x": -4.82, "y": -15.69}]}, "bone48": {"translate": [{"x": -4.17, "y": -16.41, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "x": -5.54, "y": -18.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -12.38, "y": 0.28, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 8.61, "y": -1.18, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.9333, "x": -4.17, "y": -16.41}]}, "bone49": {"translate": [{"x": -4.17, "y": -16.41, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "x": -5.54, "y": -18.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -12.38, "y": 0.28, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 8.61, "y": -1.18, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.9333, "x": -4.17, "y": -16.41}]}, "bone50": {"translate": [{"x": -4.82, "y": -15.69, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -12.38, "y": 0.28, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": 8.61, "y": -1.18, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": -5.54, "y": -18.04, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.9333, "x": -4.82, "y": -15.69}]}, "bone51": {"translate": [{"x": -2.04, "y": -6.64, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -12.38, "y": 0.28, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 8.61, "y": -1.18, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -5.54, "y": -18.04, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.9333, "x": -2.04, "y": -6.64}]}, "bone52": {"translate": [{"x": -2.04, "y": -6.64, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -12.38, "y": 0.28, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 8.61, "y": -1.18, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -5.54, "y": -18.04, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.9333, "x": -2.04, "y": -6.64}]}, "bone55": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -12.38, "y": 0.28, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 8.61, "y": -1.18, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": -5.54, "y": -18.04, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "bone56": {"translate": [{"x": -2.04, "y": -6.64, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -12.38, "y": 0.28, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 8.61, "y": -1.18, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -5.54, "y": -18.04, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.9333, "x": -2.04, "y": -6.64}]}, "bone57": {"translate": [{"x": -4.82, "y": -15.69, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -12.38, "y": 0.28, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": 8.61, "y": -1.18, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": -5.54, "y": -18.04, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.9333, "x": -4.82, "y": -15.69}]}, "bone58": {"translate": [{"x": -5.54, "y": -18.04, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -12.38, "y": 0.28, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": 8.61, "y": -1.18, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": -5.54, "y": -18.04}]}, "bone59": {"translate": [{"x": -2.04, "y": -6.64, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -12.38, "y": 0.28, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 8.61, "y": -1.18, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -5.54, "y": -18.04, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.9333, "x": -2.04, "y": -6.64}]}, "bone60": {"translate": [{"x": -4.82, "y": -15.69, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -12.38, "y": 0.28, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": 8.61, "y": -1.18, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": -5.54, "y": -18.04, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.9333, "x": -4.82, "y": -15.69}]}, "bone61": {"translate": [{"x": -5.54, "y": -18.04, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -12.38, "y": 0.28, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": 8.61, "y": -1.18, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": -5.54, "y": -18.04}]}, "bone62": {"translate": [{"x": -1.52, "y": -13.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "x": -5.54, "y": -18.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -12.38, "y": 0.28, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": 8.61, "y": -1.18, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.9333, "x": -1.52, "y": -13.26}]}, "bone75": {"translate": [{"x": -0.84, "y": -3.28, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "x": -1.94, "y": -6.42, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -5.48, "y": -0.95, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": 0.26, "y": -0.15, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.9333, "x": -0.84, "y": -3.28}]}, "bone76": {"translate": [{"x": -1.8, "y": -6.02, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.0333, "x": -1.94, "y": -6.42, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -5.48, "y": -0.95, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": 0.26, "y": -0.15, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 0.9333, "x": -1.8, "y": -6.02}]}, "bone77": {"translate": [{"x": -1.23, "y": -4.06, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -5.48, "y": -0.95, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 0.26, "y": -0.15, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": -1.94, "y": -6.42, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.9333, "x": -1.23, "y": -4.06}]}, "bone78": {"translate": [{"x": -1.23, "y": -4.06, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -5.48, "y": -0.95, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 0.26, "y": -0.15, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": -1.94, "y": -6.42, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.9333, "x": -1.23, "y": -4.06}]}, "bone80": {"translate": [{"x": -0.84, "y": -3.28, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "x": -1.94, "y": -6.42, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -5.48, "y": -0.95, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": 0.26, "y": -0.15, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.9333, "x": -0.84, "y": -3.28}]}, "bone81": {"translate": [{"x": -1.23, "y": -4.06, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -5.48, "y": -0.95, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 0.26, "y": -0.15, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": -1.94, "y": -6.42, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.9333, "x": -1.23, "y": -4.06}]}, "bone82": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -5.48, "y": -0.95, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.26, "y": -0.15, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": -1.94, "y": -6.42, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "bone83": {"translate": [{"x": -1.8, "y": -6.02, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.0333, "x": -1.94, "y": -6.42, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -5.48, "y": -0.95, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": 0.26, "y": -0.15, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 0.9333, "x": -1.8, "y": -6.02}]}, "bone84": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -5.48, "y": -0.95, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.26, "y": -0.15, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "bone87": {"rotate": [{"curve": 0.245, "c3": 0.638, "c4": 0.56}, {"time": 0.5667, "angle": -24.07}], "translate": [{"y": 76.84, "curve": 0.312, "c2": 0.27, "c3": 0.665, "c4": 0.66}, {"time": 0.5667, "y": 384.32, "curve": "stepped"}, {"time": 0.6333, "y": 384.32, "curve": 0.382, "c2": 0.55, "c3": 0.742}, {"time": 0.7, "x": 315.74, "y": 239.39}], "shear": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 179.36, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": -173.62}]}}, "events": [{"time": 0.7, "name": "atk"}]}, "boss_idle": {"slots": {"guanglun2": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1, "color": "ffffff3d", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffff00"}]}, "biyan": {"attachment": [{"time": 1.0667, "name": "biyan"}, {"time": 1.1667, "name": null}]}}, "bones": {"bone": {"translate": [{"y": 0.72, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "y": 10.35, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2, "y": 0.72}]}, "bone2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "y": 10.11, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone3": {"rotate": [{"angle": -1.03, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -1.03}]}, "bone4": {"rotate": [{"angle": -0.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -1.03, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 0.7, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -0.54}]}, "bone5": {"rotate": [{"angle": 0.21, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -1.03, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 0.7, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 0.21}]}, "bone6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -3.19, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone7": {"rotate": [{"angle": -1.31, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -3.19, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2, "angle": -1.31}]}, "bone8": {"rotate": [{"angle": -2.97, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -3.19, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 2, "angle": -2.97}]}, "bone9": {"rotate": [{"angle": 1.1, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 3.88, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 1.1}]}, "bone10": {"rotate": [{"angle": 3.24, "curve": 0.31, "c2": 0.26, "c3": 0.66, "c4": 0.65}, {"time": 0.3333, "angle": 1.6, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": 3.88, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 2, "angle": 3.24}]}, "bone11": {"rotate": [{"angle": 3.38, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "angle": 3.88, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 0.3333, "angle": 3.61, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 1.2, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2, "angle": 3.38}]}, "bone12": {"rotate": [{"angle": -1.8, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": -10.83, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 2, "angle": -1.8}]}, "bone13": {"rotate": [{"angle": -6.35, "curve": 0.334, "c2": 0.34, "c3": 0.676, "c4": 0.7}, {"time": 0.2333, "angle": -3.07, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": -10.83, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 2, "angle": -6.35}]}, "bone14": {"rotate": [{"angle": -10.39, "curve": 0.296, "c2": 0.18, "c3": 0.638, "c4": 0.55}, {"time": 0.2333, "angle": -7.75, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": -10.83, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2, "angle": -10.39}]}, "bone15": {"rotate": [{"angle": -9.03, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.2333, "angle": -10.83, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 2, "angle": -9.03}]}, "bone18": {"translate": [{"x": -0.04, "y": 2.7, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2667, "x": -0.05, "y": 3.39, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.9333, "x": -0.02, "y": 0.96, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.2667, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 2, "x": -0.04, "y": 2.7}]}, "bone19": {"rotate": [{"angle": -14.18, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "angle": -22.43, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "angle": -14.18}]}, "bone20": {"translate": [{"x": 0.48, "y": -0.83, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": 2.89, "y": -5.01, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 2, "x": 0.48, "y": -0.83}], "scale": [{"x": 0.994, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": 0.964, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 2, "x": 0.994}]}, "bone21": {"scale": [{"x": 0.901, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "x": 0.887, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "x": 0.901}]}, "bone22": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 5.62, "curve": 0.25, "c3": 0.75}, {"time": 2}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": -5.6, "y": -1.49, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone23": {"rotate": [{"angle": 2.56, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 5.62, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "angle": 2.56}]}, "bone24": {"rotate": [{"angle": 5.5, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 0.9333, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": 5.62, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 2, "angle": 5.5}]}, "bone25": {"rotate": [{"angle": -4.7, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0667, "angle": -4.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.0667, "angle": -0.87, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 2, "angle": -4.7}], "translate": [{"x": -0.05, "y": 2.89, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0667, "x": -0.05, "y": 3.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.0667, "x": 0.13, "y": -8.08, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 2, "x": -0.05, "y": 2.89}]}, "bone26": {"rotate": [{"angle": -3.68, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -4.79, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -0.87, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -3.68}]}, "bone27": {"rotate": [{"angle": -1.98, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -4.79, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -0.87, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -1.98}]}, "bone28": {"rotate": [{"angle": -0.87, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -4.79, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -0.87}]}, "bone29": {"rotate": [{"angle": -1.38, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "angle": -0.87, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -4.79, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2, "angle": -1.38}]}, "bone30": {"rotate": [{"angle": -4.41, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": -4.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.1667, "angle": -0.87, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": -4.41}], "translate": [{"x": -0.03, "y": 2.06, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "x": -0.05, "y": 3.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.1667, "x": 0.13, "y": -8.08, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "x": -0.03, "y": 2.06}]}, "bone31": {"rotate": [{"angle": -3.68, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -4.79, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -0.87, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -3.68}]}, "bone32": {"rotate": [{"angle": -1.98, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -4.79, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -0.87, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -1.98}]}, "bone33": {"rotate": [{"angle": -0.87, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -4.79, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -0.87}]}, "bone34": {"rotate": [{"angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 0.14, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 4.82}], "translate": [{"x": -3.13, "y": 0.24, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 8.05, "y": -0.6, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -3.13, "y": 0.24}]}, "bone35": {"rotate": [{"angle": 3.49, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 0.14, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 3.49}]}, "bone36": {"rotate": [{"angle": 1.47, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 0.14, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 1.47}]}, "bone37": {"rotate": [{"angle": 0.14, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 0.14}]}, "bone38": {"rotate": [{"angle": 2.06, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.5667, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": 0.14, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 2, "angle": 2.06}]}, "bone39": {"rotate": [{"angle": 4.62, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.9, "angle": 0.14, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": 4.82, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2, "angle": 4.62}], "translate": [{"x": -0.04, "y": 2.67, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.9, "x": 0.13, "y": -8.08, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "x": -0.05, "y": 3.14, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2, "x": -0.04, "y": 2.67}]}, "bone40": {"rotate": [{"angle": 3.49, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 0.14, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 3.49}]}, "bone41": {"rotate": [{"angle": 1.47, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 0.14, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 1.47}]}, "bone44": {"translate": [{"x": -4.82, "y": 0.42, "curve": 0.309, "c2": 0.26, "c3": 0.671, "c4": 0.68}, {"time": 0.4333, "x": -1.64, "y": 0.14, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": -5.77, "y": 0.5, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 2, "x": -4.82, "y": 0.42}]}, "bone45": {"translate": [{"x": -4.49, "y": 0.39, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "x": -4.69, "y": 0.41, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.4333, "x": -3.36, "y": 0.29, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.1, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2, "x": -4.49, "y": 0.39}]}, "bone46": {"translate": [{"x": -3.1, "y": 3.69, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.4333, "x": -5.27, "y": 6.27, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2, "x": -3.1, "y": 3.69}]}, "bone47": {"translate": [{"x": -4.7, "y": -1.42, "curve": 0.332, "c2": 0.33, "c3": 0.676, "c4": 0.7}, {"time": 0.2667, "x": -2.12, "y": -0.64, "curve": 0.351, "c2": 0.4, "c3": 0.689, "c4": 0.75}, {"time": 0.4333, "x": -0.71, "y": -0.21, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "x": -7.46, "y": -2.26, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "x": -4.7, "y": -1.42}]}, "bone48": {"translate": [{"x": -5.17, "y": 0.45, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -7.21, "y": 0.63, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -5.17, "y": 0.45}]}, "bone49": {"translate": [{"x": -0.65, "y": 0.47, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": -6.83, "y": 4.96, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.8333, "x": -1.94, "y": 1.41, "curve": 0.351, "c2": 0.4, "c3": 0.689, "c4": 0.75}, {"time": 2, "x": -0.65, "y": 0.47}]}, "bone50": {"translate": [{"x": -6.16, "y": -8.18, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -6.16, "y": -8.18}]}, "bone51": {"translate": [{"x": -4.65, "y": 0.41, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -6.49, "y": 0.57, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -4.65, "y": 0.41}]}, "bone52": {"translate": [{"x": -1.84, "y": 0.16, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -6.49, "y": 0.57, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -1.84, "y": 0.16}]}, "bone55": {"translate": [{"x": 1.64, "y": -0.14, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 5.77, "y": -0.5, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 1.64, "y": -0.14}]}, "bone56": {"translate": [{"x": 4.61, "y": 5.06, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 6.43, "y": 7.07, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 4.61, "y": 5.06}]}, "bone57": {"translate": [{"x": 6.46, "y": 7.43, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 6.46, "y": 7.43}]}, "bone58": {"translate": [{"x": 4.67, "y": 5.84, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": 6.53, "y": 8.15, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 4.67, "y": 5.84}]}, "bone59": {"translate": [{"x": -3.35, "y": -3.67, "curve": 0.334, "c2": 0.34, "c3": 0.676, "c4": 0.7}, {"time": 0.2333, "x": -1.62, "y": -1.78, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "x": -5.71, "y": -6.26, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 2, "x": -3.35, "y": -3.67}]}, "bone60": {"translate": [{"x": -5.45, "y": -5.63, "curve": 0.296, "c2": 0.18, "c3": 0.638, "c4": 0.55}, {"time": 0.2333, "x": -4.07, "y": -4.2, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "x": -5.68, "y": -5.86, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2, "x": -5.45, "y": -5.63}]}, "bone61": {"translate": [{"x": -4.68, "y": -4.23, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.2333, "x": -5.61, "y": -5.08, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 2, "x": -4.68, "y": -4.23}]}, "bone62": {"translate": [{"x": -2.5, "y": -4.22, "curve": 0.334, "c2": 0.34, "c3": 0.676, "c4": 0.7}, {"time": 0.2333, "x": -4.34, "y": -7.31, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.5667, "x": -6.06, "y": -10.2, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 2, "x": -2.5, "y": -4.22}]}, "bone85": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.235, "y": 1.235}]}, "bone63": {"translate": [{"x": 0.02, "y": 0.04, "curve": 0.344, "c2": 0.66, "c3": 0.677}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "x": 1.77, "y": 3.2, "curve": 0.248, "c3": 0.736, "c4": 0.94}, {"time": 2, "x": 0.02, "y": 0.04}]}, "bone64": {"translate": [{"y": -0.02, "curve": 0.344, "c2": 0.66, "c3": 0.677}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "x": 0.4, "y": -1.94, "curve": 0.248, "c3": 0.736, "c4": 0.94}, {"time": 2, "y": -0.02}]}, "bone66": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 6.09, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone67": {"rotate": [{"angle": 1.73, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 6.09, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 1.73}]}, "bone68": {"rotate": [{"angle": 4.36, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 6.09, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 4.36}]}, "bone69": {"rotate": [{"angle": 6.09, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 6.09}]}, "bone71": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -4.96, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone72": {"rotate": [{"angle": -1.41, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -4.96, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -1.41}]}, "bone73": {"rotate": [{"angle": -3.55, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -4.96, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -3.55}]}, "bone74": {"rotate": [{"angle": -4.96, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -4.96}]}, "bone79": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 3.66, "y": 2.75, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone78": {"translate": [{"x": 1.04, "y": 0.78, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 3.66, "y": 2.75, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 1.04, "y": 0.78}]}, "bone77": {"translate": [{"x": 3.58, "y": 2.68, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0667, "x": 3.66, "y": 2.75, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.4, "x": 2.62, "y": 1.97, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.0667, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 2, "x": 3.58, "y": 2.68}]}, "bone82": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 3.66, "y": 2.75, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone84": {"translate": [{"x": 1.04, "y": 0.78, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 3.66, "y": 2.75, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 1.04, "y": 0.78}]}, "bone81": {"translate": [{"x": 2.62, "y": 1.97, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 3.66, "y": 2.75, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 2.62, "y": 1.97}]}, "bone83": {"translate": [{"x": 3.66, "y": 2.75, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 3.66, "y": 2.75}]}, "bone80": {"translate": [{"x": 2.62, "y": 1.97, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": 3.66, "y": 2.75, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 2.62, "y": 1.97}]}, "bone76": {"translate": [{"x": 2.31, "y": 1.74, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "x": 3.66, "y": 2.75, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "x": 2.31, "y": 1.74}]}, "bone75": {"translate": [{"x": 0.75, "y": 0.56, "curve": 0.315, "c2": 0.28, "c3": 0.673, "c4": 0.69}, {"time": 0.4, "x": 2.62, "y": 1.97, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.7333, "x": 3.66, "y": 2.75, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 2, "x": 0.75, "y": 0.56}]}}}, "die": {"slots": {"piaodai3": {"color": [{"time": 0.1333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff00"}]}, "s3": {"color": [{"time": 0.1333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff00"}]}, "piaodai1": {"color": [{"time": 0.1333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff00"}]}, "s1": {"color": [{"time": 0.1333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff00"}]}, "piaodai2": {"color": [{"time": 0.1333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff00"}]}, "s4": {"color": [{"time": 0.1333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff00"}]}, "s2": {"color": [{"time": 0.1333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff00"}]}, "biyan": {"attachment": [{"time": 0.3, "name": "biyan"}]}, "guanglun": {"color": [{"color": "ffffff00"}]}, "piaodai4": {"color": [{"time": 0.1333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff00"}]}, "piaodai": {"color": [{"time": 0.1333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff00"}]}}, "bones": {"bone2": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 86.01}], "translate": [{"curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1, "y": -21.07, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3, "y": 18, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "y": -159.51, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "y": -138.03, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "y": -159.51}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -20.45, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 6.65, "curve": "stepped"}, {"time": 0.3333, "angle": 6.65, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -3.37}]}, "bone4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -20.45, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 6.65, "curve": "stepped"}, {"time": 0.3667, "angle": 6.65, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -3.37}]}, "bone5": {"rotate": [{"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 9.41}]}, "bone7": {"rotate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -14.78, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 16.66, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone8": {"rotate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -14.78, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 16.66, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone9": {"rotate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -14.78, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 16.66, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone10": {"rotate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -14.78, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 16.66, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone11": {"rotate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -14.78, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 16.66, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone12": {"rotate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -14.78, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 16.66, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone13": {"rotate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -14.78, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 16.66, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone14": {"rotate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -14.78, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 16.66, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone15": {"rotate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -14.78, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 16.66, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone22": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 25.1, "y": -12.79, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}}}, "hurt": {"bones": {"bone": {"rotate": [{"angle": 4.94, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -4.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 9.88, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": 4.94}], "translate": [{"x": -18.41, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -19.58, "y": -1.03, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2667, "x": -36.83, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "x": -18.41}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 3.39, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 6.53, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -29.48, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -10.07, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 3.08, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone4": {"rotate": [{"angle": 1.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -10.07, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 3.08, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": 1.54}]}, "bone5": {"rotate": [{"angle": 2.51, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -10.07, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 3.08, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "angle": 2.51}]}, "bone6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -6.63, "curve": "stepped"}, {"time": 0.2, "angle": -6.63, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone7": {"rotate": [{"angle": -3.32, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -6.63, "curve": "stepped"}, {"time": 0.2667, "angle": -6.63, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": -3.32}]}, "bone8": {"rotate": [{"angle": -6.63, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -6.63}]}, "bone9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -7.84, "curve": "stepped"}, {"time": 0.2, "angle": -7.84, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone10": {"rotate": [{"angle": -3.92, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -7.84, "curve": "stepped"}, {"time": 0.2667, "angle": -7.84, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": -3.92}]}, "bone11": {"rotate": [{"angle": -7.84, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -7.84}]}, "bone18": {"translate": [{"x": 1.73, "y": 0.43, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -4.25, "y": 7.69, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 9.37, "y": 2.32, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.3333, "x": 1.73, "y": 0.43}]}, "bone20": {"rotate": [{"angle": -3.32, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -6.63, "curve": "stepped"}, {"time": 0.2667, "angle": -6.63, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": -3.32}], "translate": [{"x": 4.75, "y": 0.07, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 9.5, "y": 0.15, "curve": "stepped"}, {"time": 0.2667, "x": 9.5, "y": 0.15, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "x": 4.75, "y": 0.07}]}, "bone21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -6.63, "curve": "stepped"}, {"time": 0.2, "angle": -6.63, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone22": {"rotate": [{"angle": -3.92, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -7.84, "curve": "stepped"}, {"time": 0.2667, "angle": -7.84, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": -3.92}], "translate": [{"x": 8.61, "y": 0.14, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 17.22, "y": 0.27, "curve": "stepped"}, {"time": 0.2667, "x": 17.22, "y": 0.27, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "x": 8.61, "y": 0.14}]}, "bone25": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -7.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 1.81, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone26": {"rotate": [{"angle": 6.74, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -7.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 13.48, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": 6.74}]}, "bone27": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -7.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 1.81, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone28": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -7.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 1.81, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone30": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -7.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 4.32, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone31": {"rotate": [{"angle": 2.16, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -7.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 4.32, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": 2.16}]}, "bone32": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -7.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 4.32, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone33": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -7.08, "curve": "stepped"}, {"time": 0.2, "angle": -7.08, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone34": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -7.47, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone35": {"rotate": [{"angle": -3.73, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -7.47, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": -3.73}]}, "bone36": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -7.47, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone37": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -7.47, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone39": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -7.47, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone40": {"rotate": [{"angle": -3.73, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -7.47, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": -3.73}]}, "bone58": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -7.47, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone75": {"translate": [{"x": -1.45, "y": 2.13, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "x": -1.02, "y": 5.09, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -2.52, "y": -5.35, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3333, "x": -1.45, "y": 2.13}]}, "bone76": {"translate": [{"x": -0.51, "y": 2.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -2.52, "y": -5.35, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -1.02, "y": 5.09, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "x": -0.51, "y": 2.54}]}, "bone77": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -2.52, "y": -5.35, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -1.02, "y": 5.09, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone78": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -2.52, "y": -5.35, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -1.02, "y": 5.09, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone80": {"translate": [{"x": -0.83, "y": 4.15, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -2.52, "y": -5.35, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -1.02, "y": 5.09, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "x": -0.83, "y": 4.15}]}, "bone81": {"translate": [{"x": -0.51, "y": 2.54, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -2.52, "y": -5.35, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -1.02, "y": 5.09, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "x": -0.51, "y": 2.54}]}, "bone82": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -2.52, "y": -5.35, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -1.02, "y": 5.09, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone83": {"translate": [{"x": -0.83, "y": 4.15, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -2.52, "y": -5.35, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -1.02, "y": 5.09, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.3333, "x": -0.83, "y": 4.15}]}, "bone84": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -2.52, "y": -5.35, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -1.02, "y": 5.09, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}}}, "run1": {"slots": {"guanglun2": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffff3d", "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "color": "ffffff00"}]}}, "bones": {"bone30": {"rotate": [{"angle": -4.41, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "angle": -4.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": -0.87, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.6667, "angle": -4.41}], "translate": [{"x": -0.03, "y": 2.06, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "x": -0.05, "y": 3.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "x": 0.13, "y": -8.08, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.6667, "x": -0.03, "y": 2.06}]}, "bone31": {"rotate": [{"angle": -3.68, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "angle": -4.79, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -0.87, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "angle": -3.68}]}, "bone32": {"rotate": [{"angle": -1.98, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "angle": -4.79, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -0.87, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "angle": -1.98}]}, "bone33": {"rotate": [{"angle": -0.87, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -4.79, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -0.87}]}, "bone2": {"rotate": [{"angle": -17.06}], "translate": [{"x": 5.21, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 5.69, "y": 10.11, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 5.21}]}, "bone": {"translate": [{"x": 27.49, "y": 2.7, "curve": 0.348, "c2": 0.43, "c3": 0.682, "c4": 0.77}, {"time": 0.3333, "x": 27.49, "y": 5.55, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.6667, "x": 27.49, "y": 2.7}]}, "bone85": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 1.235, "y": 1.235}]}, "bone84": {"translate": [{"x": 1.04, "y": 0.78, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 3.66, "y": 2.75, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "x": 1.04, "y": 0.78}]}, "bone62": {"translate": [{"x": -2.5, "y": -4.22, "curve": 0.334, "c2": 0.34, "c3": 0.676, "c4": 0.7}, {"time": 0.0667, "x": -4.34, "y": -7.31, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "x": -6.06, "y": -10.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.6667, "x": -2.5, "y": -4.22}]}, "bone73": {"rotate": [{"angle": -3.55, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -4.96, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "angle": -3.55}]}, "bone61": {"translate": [{"x": -4.68, "y": -4.23, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.0667, "x": -5.61, "y": -5.08, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 0.6667, "x": -4.68, "y": -4.23}]}, "bone83": {"translate": [{"x": 3.66, "y": 2.75, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 3.66, "y": 2.75}]}, "bone76": {"translate": [{"x": 2.31, "y": 1.74, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "x": 3.66, "y": 2.75, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "x": 2.31, "y": 1.74}]}, "bone22": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 5.62, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -5.6, "y": -1.49, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone5": {"rotate": [{"angle": 0.21, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "angle": -1.03, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 0.7, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "angle": 0.21}]}, "bone3": {"rotate": [{"angle": 5.42, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 5.42}]}, "bone6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -3.19, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone4": {"rotate": [{"angle": -0.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "angle": -1.03, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 0.7, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "angle": -0.54}]}, "bone8": {"rotate": [{"angle": -2.97, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -3.19, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 0.6667, "angle": -2.97}]}, "bone18": {"translate": [{"x": -0.04, "y": 2.7, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1, "x": -0.05, "y": 3.39, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3, "x": -0.02, "y": 0.96, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 0.6667, "x": -0.04, "y": 2.7}]}, "bone60": {"translate": [{"x": -5.45, "y": -5.63, "curve": 0.296, "c2": 0.18, "c3": 0.638, "c4": 0.55}, {"time": 0.0667, "x": -4.07, "y": -4.2, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -5.68, "y": -5.86, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.6667, "x": -5.45, "y": -5.63}]}, "bone12": {"rotate": [{"angle": -1.8, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -10.83, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 0.6667, "angle": -1.8}]}, "bone9": {"rotate": [{"angle": 1.1, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 3.88, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "angle": 1.1}]}, "bone11": {"rotate": [{"angle": 3.38, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "angle": 3.88, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 0.1, "angle": 3.61, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.4, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": 3.38}]}, "bone13": {"rotate": [{"angle": -6.35, "curve": 0.334, "c2": 0.34, "c3": 0.676, "c4": 0.7}, {"time": 0.0667, "angle": -3.07, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -10.83, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.6667, "angle": -6.35}]}, "bone14": {"rotate": [{"angle": -10.39, "curve": 0.296, "c2": 0.18, "c3": 0.638, "c4": 0.55}, {"time": 0.0667, "angle": -7.75, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -10.83, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.6667, "angle": -10.39}]}, "bone44": {"translate": [{"x": -4.82, "y": 0.42, "curve": 0.309, "c2": 0.26, "c3": 0.671, "c4": 0.68}, {"time": 0.1333, "x": -1.64, "y": 0.14, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": -5.77, "y": 0.5, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 0.6667, "x": -4.82, "y": 0.42}]}, "bone10": {"rotate": [{"angle": 3.24, "curve": 0.31, "c2": 0.26, "c3": 0.66, "c4": 0.65}, {"time": 0.1, "angle": 1.6, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 3.88, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 0.6667, "angle": 3.24}]}, "bone21": {"scale": [{"x": 0.901, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 0.887, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "x": 0.901}]}, "bone7": {"rotate": [{"angle": -1.31, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -3.19, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.6667, "angle": -1.31}]}, "bone19": {"rotate": [{"angle": -14.18, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "angle": -22.43, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -14.18}]}, "bone20": {"translate": [{"x": 0.48, "y": -0.83, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 2.89, "y": -5.01, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 0.6667, "x": 0.48, "y": -0.83}], "scale": [{"x": 0.994, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 0.964, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 0.6667, "x": 0.994}]}, "bone26": {"rotate": [{"angle": -3.68, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "angle": -4.79, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -0.87, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "angle": -3.68}]}, "bone37": {"rotate": [{"angle": 0.14, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 0.14}]}, "bone38": {"rotate": [{"angle": 2.06, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1667, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 0.14, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.6667, "angle": 2.06}]}, "bone24": {"rotate": [{"angle": 5.5, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 5.62, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 0.6667, "angle": 5.5}]}, "bone25": {"rotate": [{"angle": -4.7, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0333, "angle": -4.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": -0.87, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 0.6667, "angle": -4.7}], "translate": [{"x": -0.05, "y": 2.89, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0333, "x": -0.05, "y": 3.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "x": 0.13, "y": -8.08, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 0.6667, "x": -0.05, "y": 2.89}]}, "bone29": {"rotate": [{"angle": -1.38, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "angle": -0.87, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -4.79, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -1.38}]}, "bone36": {"rotate": [{"angle": 1.47, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 0.14, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "angle": 1.47}]}, "bone28": {"rotate": [{"angle": -0.87, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -4.79, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -0.87}]}, "bone34": {"rotate": [{"angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 0.14, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 4.82}], "translate": [{"x": -3.13, "y": 0.24, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 8.05, "y": -0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -3.13, "y": 0.24}]}, "bone27": {"rotate": [{"angle": -1.98, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "angle": -4.79, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -0.87, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "angle": -1.98}]}, "bone35": {"rotate": [{"angle": 3.49, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 0.14, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "angle": 3.49}]}, "bone23": {"rotate": [{"angle": 2.56, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 5.62, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 0.6667, "angle": 2.56}]}, "bone39": {"rotate": [{"angle": 4.62, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.2667, "angle": 0.14, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 4.82, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.6667, "angle": 4.62}], "translate": [{"x": -0.04, "y": 2.67, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.2667, "x": 0.13, "y": -8.08, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -0.05, "y": 3.14, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.6667, "x": -0.04, "y": 2.67}]}, "bone40": {"rotate": [{"angle": 3.49, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 0.14, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "angle": 3.49}]}, "bone48": {"translate": [{"x": -5.17, "y": 0.45, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -7.21, "y": 0.63, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "x": -5.17, "y": 0.45}]}, "bone45": {"translate": [{"x": -4.49, "y": 0.39, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0333, "x": -4.69, "y": 0.41, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1333, "x": -3.36, "y": 0.29, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 0.6667, "x": -4.49, "y": 0.39}]}, "bone81": {"translate": [{"x": 2.62, "y": 1.97, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 3.66, "y": 2.75, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "x": 2.62, "y": 1.97}]}, "bone80": {"translate": [{"x": 2.62, "y": 1.97, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "x": 3.66, "y": 2.75, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "x": 2.62, "y": 1.97}]}, "bone47": {"translate": [{"x": -4.7, "y": -1.42, "curve": 0.332, "c2": 0.33, "c3": 0.676, "c4": 0.7}, {"time": 0.1, "x": -2.12, "y": -0.64, "curve": 0.351, "c2": 0.4, "c3": 0.689, "c4": 0.75}, {"time": 0.1333, "x": -0.71, "y": -0.21, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -7.46, "y": -2.26, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "x": -4.7, "y": -1.42}]}, "bone46": {"translate": [{"x": -3.1, "y": 3.69, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1333, "x": -5.27, "y": 6.27, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.6667, "x": -3.1, "y": 3.69}]}, "bone41": {"rotate": [{"angle": 1.47, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 0.14, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "angle": 1.47}]}, "bone57": {"translate": [{"x": 6.46, "y": 7.43, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 6.46, "y": 7.43}]}, "bone49": {"translate": [{"x": -0.65, "y": 0.47, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -6.83, "y": 4.96, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6, "x": -1.94, "y": 1.41, "curve": 0.351, "c2": 0.4, "c3": 0.689, "c4": 0.75}, {"time": 0.6667, "x": -0.65, "y": 0.47}]}, "bone50": {"translate": [{"x": -6.16, "y": -8.18, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -6.16, "y": -8.18}]}, "bone51": {"translate": [{"x": -4.65, "y": 0.41, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -6.49, "y": 0.57, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "x": -4.65, "y": 0.41}]}, "bone52": {"translate": [{"x": -1.84, "y": 0.16, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -6.49, "y": 0.57, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "x": -1.84, "y": 0.16}]}, "bone56": {"translate": [{"x": 4.61, "y": 5.06, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 6.43, "y": 7.07, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "x": 4.61, "y": 5.06}]}, "bone59": {"translate": [{"x": -3.35, "y": -3.67, "curve": 0.334, "c2": 0.34, "c3": 0.676, "c4": 0.7}, {"time": 0.0667, "x": -1.62, "y": -1.78, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -5.71, "y": -6.26, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.6667, "x": -3.35, "y": -3.67}]}, "bone58": {"translate": [{"x": 4.67, "y": 5.84, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "x": 6.53, "y": 8.15, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "x": 4.67, "y": 5.84}]}, "bone55": {"translate": [{"x": 1.64, "y": -0.14, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 5.77, "y": -0.5, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "x": 1.64, "y": -0.14}]}, "bone15": {"rotate": [{"angle": -9.03, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.0667, "angle": -10.83, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 0.6667, "angle": -9.03}]}, "bone69": {"rotate": [{"angle": 6.09, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 6.09}]}, "bone64": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 0.4, "y": -1.94, "curve": 0.248, "c3": 0.736, "c4": 0.94}, {"time": 0.6667, "y": -0.02}]}, "bone63": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 1.77, "y": 3.2, "curve": 0.248, "c3": 0.736, "c4": 0.94}, {"time": 0.6667, "x": 0.02, "y": 0.04}]}, "bone68": {"rotate": [{"angle": 4.36, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 6.09, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "angle": 4.36}]}, "bone71": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -4.96, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone67": {"rotate": [{"angle": 1.73, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 6.09, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "angle": 1.73}]}, "bone66": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 6.09, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone78": {"translate": [{"x": 1.04, "y": 0.78, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 3.66, "y": 2.75, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "x": 1.04, "y": 0.78}]}, "bone74": {"rotate": [{"angle": -4.96, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -4.96}]}, "bone82": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 3.66, "y": 2.75, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone79": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 3.66, "y": 2.75, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone77": {"translate": [{"x": 3.58, "y": 2.68, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0333, "x": 3.66, "y": 2.75, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1333, "x": 2.62, "y": 1.97, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 0.6667, "x": 3.58, "y": 2.68}]}, "bone72": {"rotate": [{"angle": -1.41, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -4.96, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "angle": -1.41}]}, "bone75": {"translate": [{"x": 0.75, "y": 0.56, "curve": 0.315, "c2": 0.28, "c3": 0.673, "c4": 0.69}, {"time": 0.1333, "x": 2.62, "y": 1.97, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "x": 3.66, "y": 2.75, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 0.6667, "x": 0.75, "y": 0.56}]}}}, "run2": {"slots": {"guanglun2": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "color": "ffffff3d", "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "color": "ffffff00"}]}}, "bones": {"bone30": {"rotate": [{"angle": -4.41, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "angle": -4.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "angle": -0.87, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.2333, "angle": -4.41}], "translate": [{"x": -0.03, "y": 2.06, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "x": -0.05, "y": 3.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "x": 0.13, "y": -8.08, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.2333, "x": -0.03, "y": 2.06}]}, "bone31": {"rotate": [{"angle": -3.68, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "angle": -4.79, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -0.87, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2333, "angle": -3.68}]}, "bone32": {"rotate": [{"angle": -1.98, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": -4.79, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -0.87, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2333, "angle": -1.98}]}, "bone33": {"rotate": [{"angle": -0.87, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -4.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -0.87}]}, "bone2": {"rotate": [{"angle": -29.69}], "translate": [{"x": 6.05, "y": -1.66, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 5.69, "y": 10.11, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 5.21}]}, "bone": {"translate": [{"x": 27.49, "y": 2.7, "curve": 0.348, "c2": 0.43, "c3": 0.682, "c4": 0.77}, {"time": 0.1333, "x": 27.49, "y": 5.55, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.2333, "x": 27.49, "y": 2.7}]}, "bone85": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 1.235, "y": 1.235}]}, "bone84": {"translate": [{"x": 1.04, "y": 0.78, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 3.66, "y": 2.75, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2333, "x": 1.04, "y": 0.78}]}, "bone62": {"translate": [{"x": -2.5, "y": -4.22, "curve": 0.334, "c2": 0.34, "c3": 0.676, "c4": 0.7}, {"time": 0.0333, "x": -4.34, "y": -7.31, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "x": -6.06, "y": -10.2, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.2333, "x": -2.5, "y": -4.22}]}, "bone73": {"rotate": [{"angle": -3.55, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -4.96, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2333, "angle": -3.55}]}, "bone61": {"translate": [{"x": -4.68, "y": -4.23, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.0333, "x": -5.61, "y": -5.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 0.2333, "x": -4.68, "y": -4.23}]}, "bone83": {"translate": [{"x": 3.66, "y": 2.75, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 3.66, "y": 2.75}]}, "bone76": {"translate": [{"x": 2.31, "y": 1.74, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0333, "x": 3.66, "y": 2.75, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.2333, "x": 2.31, "y": 1.74}]}, "bone22": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 5.62, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -5.6, "y": -1.49, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "bone5": {"rotate": [{"angle": 0.21, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": -1.03, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 0.7, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2333, "angle": 0.21}]}, "bone3": {"rotate": [{"angle": 5.42, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 5.42}]}, "bone6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -3.19, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "bone4": {"rotate": [{"angle": -0.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "angle": -1.03, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 0.7, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2333, "angle": -0.54}]}, "bone8": {"rotate": [{"angle": -2.97, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -3.19, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 0.2333, "angle": -2.97}]}, "bone18": {"translate": [{"x": -0.04, "y": 2.7, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.0333, "x": -0.05, "y": 3.39, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.1, "x": -0.02, "y": 0.96, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1333, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 0.2333, "x": -0.04, "y": 2.7}]}, "bone60": {"translate": [{"x": -5.45, "y": -5.63, "curve": 0.296, "c2": 0.18, "c3": 0.638, "c4": 0.55}, {"time": 0.0333, "x": -4.07, "y": -4.2, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -5.68, "y": -5.86, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.2333, "x": -5.45, "y": -5.63}]}, "bone12": {"rotate": [{"angle": -1.8, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -10.83, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 0.2333, "angle": -1.8}]}, "bone9": {"rotate": [{"angle": 1.1, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 3.88, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2333, "angle": 1.1}]}, "bone11": {"rotate": [{"angle": 3.38, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": 3.61, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.1333, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.2333, "angle": 3.38}]}, "bone13": {"rotate": [{"angle": -6.35, "curve": 0.334, "c2": 0.34, "c3": 0.676, "c4": 0.7}, {"time": 0.0333, "angle": -3.07, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -10.83, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.2333, "angle": -6.35}]}, "bone14": {"rotate": [{"angle": -10.39, "curve": 0.296, "c2": 0.18, "c3": 0.638, "c4": 0.55}, {"time": 0.0333, "angle": -7.75, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -10.83, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.2333, "angle": -10.39}]}, "bone44": {"translate": [{"x": -4.82, "y": 0.42, "curve": 0.309, "c2": 0.26, "c3": 0.671, "c4": 0.68}, {"time": 0.0333, "x": -1.64, "y": 0.14, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -5.77, "y": 0.5, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 0.2333, "x": -4.82, "y": 0.42}]}, "bone10": {"rotate": [{"angle": 3.24, "curve": 0.31, "c2": 0.26, "c3": 0.66, "c4": 0.65}, {"time": 0.0333, "angle": 1.6, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 3.88, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 0.2333, "angle": 3.24}]}, "bone21": {"scale": [{"x": 0.901, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 0.887, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.2333, "x": 0.901}]}, "bone7": {"rotate": [{"angle": -1.31, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -3.19, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.2333, "angle": -1.31}]}, "bone19": {"rotate": [{"angle": -14.18, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0333, "angle": -22.43, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.2333, "angle": -14.18}]}, "bone20": {"translate": [{"x": 0.48, "y": -0.83, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 2.89, "y": -5.01, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 0.2333, "x": 0.48, "y": -0.83}], "scale": [{"x": 0.994, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 0.964, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 0.2333, "x": 0.994}]}, "bone26": {"rotate": [{"angle": -3.68, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "angle": -4.79, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -0.87, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2333, "angle": -3.68}]}, "bone37": {"rotate": [{"angle": 0.14, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 0.14}]}, "bone38": {"rotate": [{"angle": 2.06, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.0667, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 0.14, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.2333, "angle": 2.06}]}, "bone24": {"rotate": [{"angle": 5.5, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 5.5}]}, "bone25": {"rotate": [{"angle": -4.7, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0333, "angle": -4.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "angle": -0.87, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 0.2333, "angle": -4.7}], "translate": [{"x": -0.05, "y": 2.89, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0333, "x": -0.05, "y": 3.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "x": 0.13, "y": -8.08, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 0.2333, "x": -0.05, "y": 2.89}]}, "bone29": {"rotate": [{"angle": -1.38, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": -0.87, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -4.79, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.2333, "angle": -1.38}]}, "bone36": {"rotate": [{"angle": 1.47, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 0.14, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2333, "angle": 1.47}]}, "bone28": {"rotate": [{"angle": -0.87, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -4.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -0.87}]}, "bone34": {"rotate": [{"angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 0.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 4.82}], "translate": [{"x": -3.13, "y": 0.24, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 8.05, "y": -0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -3.13, "y": 0.24}]}, "bone27": {"rotate": [{"angle": -1.98, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": -4.79, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -0.87, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2333, "angle": -1.98}]}, "bone35": {"rotate": [{"angle": 3.49, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 0.14, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2333, "angle": 3.49}]}, "bone23": {"rotate": [{"angle": 2.56, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 5.62, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 0.2333, "angle": 2.56}]}, "bone39": {"rotate": [{"angle": 4.62, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.1, "angle": 0.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 4.82, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.2333, "angle": 4.62}], "translate": [{"x": -0.04, "y": 2.67, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.1, "x": 0.13, "y": -8.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -0.05, "y": 3.14, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.2333, "x": -0.04, "y": 2.67}]}, "bone40": {"rotate": [{"angle": 3.49, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 0.14, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2333, "angle": 3.49}]}, "bone48": {"translate": [{"x": -5.17, "y": 0.45, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -7.21, "y": 0.63, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2333, "x": -5.17, "y": 0.45}]}, "bone45": {"translate": [{"x": -4.49, "y": 0.39, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0333, "x": -3.36, "y": 0.29, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 0.2333, "x": -4.49, "y": 0.39}]}, "bone81": {"translate": [{"x": 2.62, "y": 1.97, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 3.66, "y": 2.75, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2333, "x": 2.62, "y": 1.97}]}, "bone80": {"translate": [{"x": 2.62, "y": 1.97, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "x": 3.66, "y": 2.75, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2333, "x": 2.62, "y": 1.97}]}, "bone47": {"translate": [{"x": -4.7, "y": -1.42, "curve": 0.332, "c2": 0.33, "c3": 0.676, "c4": 0.7}, {"time": 0.0333, "x": -0.71, "y": -0.21, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -7.46, "y": -2.26, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.2333, "x": -4.7, "y": -1.42}]}, "bone46": {"translate": [{"x": -3.1, "y": 3.69, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.0333, "x": -5.27, "y": 6.27, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.2333, "x": -3.1, "y": 3.69}]}, "bone41": {"rotate": [{"angle": 1.47, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 0.14, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2333, "angle": 1.47}]}, "bone57": {"translate": [{"x": 6.46, "y": 7.43, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 6.46, "y": 7.43}]}, "bone49": {"translate": [{"x": -0.65, "y": 0.47, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -6.83, "y": 4.96, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2, "x": -1.94, "y": 1.41, "curve": 0.351, "c2": 0.4, "c3": 0.689, "c4": 0.75}, {"time": 0.2333, "x": -0.65, "y": 0.47}]}, "bone50": {"translate": [{"x": -6.16, "y": -8.18, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -6.16, "y": -8.18}]}, "bone51": {"translate": [{"x": -4.65, "y": 0.41, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -6.49, "y": 0.57, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2333, "x": -4.65, "y": 0.41}]}, "bone52": {"translate": [{"x": -1.84, "y": 0.16, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -6.49, "y": 0.57, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2333, "x": -1.84, "y": 0.16}]}, "bone56": {"translate": [{"x": 4.61, "y": 5.06, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 6.43, "y": 7.07, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2333, "x": 4.61, "y": 5.06}]}, "bone59": {"translate": [{"x": -3.35, "y": -3.67, "curve": 0.334, "c2": 0.34, "c3": 0.676, "c4": 0.7}, {"time": 0.0333, "x": -1.62, "y": -1.78, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -5.71, "y": -6.26, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.2333, "x": -3.35, "y": -3.67}]}, "bone58": {"translate": [{"x": 4.67, "y": 5.84, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "x": 6.53, "y": 8.15, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2333, "x": 4.67, "y": 5.84}]}, "bone55": {"translate": [{"x": 1.64, "y": -0.14, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 5.77, "y": -0.5, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2333, "x": 1.64, "y": -0.14}]}, "bone15": {"rotate": [{"angle": -9.03, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.0333, "angle": -10.83, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 0.2333, "angle": -9.03}]}, "bone69": {"rotate": [{"angle": 6.09, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 6.09}]}, "bone64": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 0.4, "y": -1.94, "curve": 0.248, "c3": 0.736, "c4": 0.94}, {"time": 0.2333, "y": -0.02}]}, "bone63": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 1.77, "y": 3.2, "curve": 0.248, "c3": 0.736, "c4": 0.94}, {"time": 0.2333, "x": 0.02, "y": 0.04}]}, "bone68": {"rotate": [{"angle": 4.36, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 6.09, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2333, "angle": 4.36}]}, "bone71": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -4.96, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "bone67": {"rotate": [{"angle": 1.73, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 6.09, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2333, "angle": 1.73}]}, "bone66": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 6.09, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "bone78": {"translate": [{"x": 1.04, "y": 0.78, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 3.66, "y": 2.75, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2333, "x": 1.04, "y": 0.78}]}, "bone74": {"rotate": [{"angle": -4.96, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -4.96}]}, "bone82": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 3.66, "y": 2.75, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "bone79": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 3.66, "y": 2.75, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "bone77": {"translate": [{"x": 3.58, "y": 2.68, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0333, "x": 2.62, "y": 1.97, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 0.2333, "x": 3.58, "y": 2.68}]}, "bone72": {"rotate": [{"angle": -1.41, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -4.96, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2333, "angle": -1.41}]}, "bone75": {"translate": [{"x": 0.75, "y": 0.56, "curve": 0.315, "c2": 0.28, "c3": 0.673, "c4": 0.69}, {"time": 0.0333, "x": 2.62, "y": 1.97, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "x": 3.66, "y": 2.75, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 0.2333, "x": 0.75, "y": 0.56}]}}}, "show_time": {"slots": {"guanglun2": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "color": "ffffff3d", "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "color": "ffffff00"}]}}, "bones": {"bone30": {"rotate": [{"angle": -4.41, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "angle": -4.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "angle": -0.87, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.2333, "angle": -4.41}], "translate": [{"x": -0.03, "y": 2.06, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "x": -0.05, "y": 3.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "x": 0.13, "y": -8.08, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.2333, "x": -0.03, "y": 2.06}]}, "bone31": {"rotate": [{"angle": -3.68, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "angle": -4.79, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -0.87, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2333, "angle": -3.68}]}, "bone32": {"rotate": [{"angle": -1.98, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": -4.79, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -0.87, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2333, "angle": -1.98}]}, "bone33": {"rotate": [{"angle": -0.87, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -4.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -0.87}]}, "bone2": {"rotate": [{"angle": -29.69}], "translate": [{"x": 6.05, "y": -1.66, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 5.69, "y": 10.11, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 5.21}]}, "bone": {"translate": [{"x": 27.49, "y": 2.7, "curve": 0.348, "c2": 0.43, "c3": 0.682, "c4": 0.77}, {"time": 0.1333, "x": 27.49, "y": 5.55, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.2333, "x": 27.49, "y": 2.7}]}, "bone85": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 1.235, "y": 1.235}]}, "bone84": {"translate": [{"x": 1.04, "y": 0.78, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 3.66, "y": 2.75, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2333, "x": 1.04, "y": 0.78}]}, "bone62": {"translate": [{"x": -2.5, "y": -4.22, "curve": 0.334, "c2": 0.34, "c3": 0.676, "c4": 0.7}, {"time": 0.0333, "x": -4.34, "y": -7.31, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "x": -6.06, "y": -10.2, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.2333, "x": -2.5, "y": -4.22}]}, "bone73": {"rotate": [{"angle": -3.55, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -4.96, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2333, "angle": -3.55}]}, "bone61": {"translate": [{"x": -4.68, "y": -4.23, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.0333, "x": -5.61, "y": -5.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 0.2333, "x": -4.68, "y": -4.23}]}, "bone83": {"translate": [{"x": 3.66, "y": 2.75, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 3.66, "y": 2.75}]}, "bone76": {"translate": [{"x": 2.31, "y": 1.74, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0333, "x": 3.66, "y": 2.75, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.2333, "x": 2.31, "y": 1.74}]}, "bone22": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 5.62, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -5.6, "y": -1.49, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "bone5": {"rotate": [{"angle": 0.21, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": -1.03, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 0.7, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2333, "angle": 0.21}]}, "bone3": {"rotate": [{"angle": 5.42, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 5.42}]}, "bone6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -3.19, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "bone4": {"rotate": [{"angle": -0.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "angle": -1.03, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 0.7, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2333, "angle": -0.54}]}, "bone8": {"rotate": [{"angle": -2.97, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -3.19, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 0.2333, "angle": -2.97}]}, "bone18": {"translate": [{"x": -0.04, "y": 2.7, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.0333, "x": -0.05, "y": 3.39, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.1, "x": -0.02, "y": 0.96, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1333, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 0.2333, "x": -0.04, "y": 2.7}]}, "bone60": {"translate": [{"x": -5.45, "y": -5.63, "curve": 0.296, "c2": 0.18, "c3": 0.638, "c4": 0.55}, {"time": 0.0333, "x": -4.07, "y": -4.2, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -5.68, "y": -5.86, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.2333, "x": -5.45, "y": -5.63}]}, "bone12": {"rotate": [{"angle": -1.8, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -10.83, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 0.2333, "angle": -1.8}]}, "bone9": {"rotate": [{"angle": 1.1, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 3.88, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2333, "angle": 1.1}]}, "bone11": {"rotate": [{"angle": 3.38, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": 3.61, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.1333, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.2333, "angle": 3.38}]}, "bone13": {"rotate": [{"angle": -6.35, "curve": 0.334, "c2": 0.34, "c3": 0.676, "c4": 0.7}, {"time": 0.0333, "angle": -3.07, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -10.83, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.2333, "angle": -6.35}]}, "bone14": {"rotate": [{"angle": -10.39, "curve": 0.296, "c2": 0.18, "c3": 0.638, "c4": 0.55}, {"time": 0.0333, "angle": -7.75, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -10.83, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.2333, "angle": -10.39}]}, "bone44": {"translate": [{"x": -4.82, "y": 0.42, "curve": 0.309, "c2": 0.26, "c3": 0.671, "c4": 0.68}, {"time": 0.0333, "x": -1.64, "y": 0.14, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -5.77, "y": 0.5, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 0.2333, "x": -4.82, "y": 0.42}]}, "bone10": {"rotate": [{"angle": 3.24, "curve": 0.31, "c2": 0.26, "c3": 0.66, "c4": 0.65}, {"time": 0.0333, "angle": 1.6, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 3.88, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 0.2333, "angle": 3.24}]}, "bone21": {"scale": [{"x": 0.901, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 0.887, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.2333, "x": 0.901}]}, "bone7": {"rotate": [{"angle": -1.31, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -3.19, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.2333, "angle": -1.31}]}, "bone19": {"rotate": [{"angle": -14.18, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0333, "angle": -22.43, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.2333, "angle": -14.18}]}, "bone20": {"translate": [{"x": 0.48, "y": -0.83, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 2.89, "y": -5.01, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 0.2333, "x": 0.48, "y": -0.83}], "scale": [{"x": 0.994, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 0.964, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 0.2333, "x": 0.994}]}, "bone26": {"rotate": [{"angle": -3.68, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "angle": -4.79, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -0.87, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2333, "angle": -3.68}]}, "bone37": {"rotate": [{"angle": 0.14, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 0.14}]}, "bone38": {"rotate": [{"angle": 2.06, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.0667, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 0.14, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.2333, "angle": 2.06}]}, "bone24": {"rotate": [{"angle": 5.5, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 5.5}]}, "bone25": {"rotate": [{"angle": -4.7, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0333, "angle": -4.79, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "angle": -0.87, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 0.2333, "angle": -4.7}], "translate": [{"x": -0.05, "y": 2.89, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0333, "x": -0.05, "y": 3.14, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "x": 0.13, "y": -8.08, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 0.2333, "x": -0.05, "y": 2.89}]}, "bone29": {"rotate": [{"angle": -1.38, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": -0.87, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -4.79, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.2333, "angle": -1.38}]}, "bone36": {"rotate": [{"angle": 1.47, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 0.14, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2333, "angle": 1.47}]}, "bone28": {"rotate": [{"angle": -0.87, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -4.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -0.87}]}, "bone34": {"rotate": [{"angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 0.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 4.82}], "translate": [{"x": -3.13, "y": 0.24, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 8.05, "y": -0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -3.13, "y": 0.24}]}, "bone27": {"rotate": [{"angle": -1.98, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": -4.79, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -0.87, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2333, "angle": -1.98}]}, "bone35": {"rotate": [{"angle": 3.49, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 0.14, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2333, "angle": 3.49}]}, "bone23": {"rotate": [{"angle": 2.56, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 5.62, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 0.2333, "angle": 2.56}]}, "bone39": {"rotate": [{"angle": 4.62, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.1, "angle": 0.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 4.82, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.2333, "angle": 4.62}], "translate": [{"x": -0.04, "y": 2.67, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.1, "x": 0.13, "y": -8.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -0.05, "y": 3.14, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.2333, "x": -0.04, "y": 2.67}]}, "bone40": {"rotate": [{"angle": 3.49, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 0.14, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2333, "angle": 3.49}]}, "bone48": {"translate": [{"x": -5.17, "y": 0.45, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -7.21, "y": 0.63, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2333, "x": -5.17, "y": 0.45}]}, "bone45": {"translate": [{"x": -4.49, "y": 0.39, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0333, "x": -3.36, "y": 0.29, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 0.2333, "x": -4.49, "y": 0.39}]}, "bone81": {"translate": [{"x": 2.62, "y": 1.97, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 3.66, "y": 2.75, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2333, "x": 2.62, "y": 1.97}]}, "bone80": {"translate": [{"x": 2.62, "y": 1.97, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "x": 3.66, "y": 2.75, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2333, "x": 2.62, "y": 1.97}]}, "bone47": {"translate": [{"x": -4.7, "y": -1.42, "curve": 0.332, "c2": 0.33, "c3": 0.676, "c4": 0.7}, {"time": 0.0333, "x": -0.71, "y": -0.21, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -7.46, "y": -2.26, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.2333, "x": -4.7, "y": -1.42}]}, "bone46": {"translate": [{"x": -3.1, "y": 3.69, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.0333, "x": -5.27, "y": 6.27, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.2333, "x": -3.1, "y": 3.69}]}, "bone41": {"rotate": [{"angle": 1.47, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 0.14, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2333, "angle": 1.47}]}, "bone57": {"translate": [{"x": 6.46, "y": 7.43, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 6.46, "y": 7.43}]}, "bone49": {"translate": [{"x": -0.65, "y": 0.47, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -6.83, "y": 4.96, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2, "x": -1.94, "y": 1.41, "curve": 0.351, "c2": 0.4, "c3": 0.689, "c4": 0.75}, {"time": 0.2333, "x": -0.65, "y": 0.47}]}, "bone50": {"translate": [{"x": -6.16, "y": -8.18, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -6.16, "y": -8.18}]}, "bone51": {"translate": [{"x": -4.65, "y": 0.41, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -6.49, "y": 0.57, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2333, "x": -4.65, "y": 0.41}]}, "bone52": {"translate": [{"x": -1.84, "y": 0.16, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -6.49, "y": 0.57, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2333, "x": -1.84, "y": 0.16}]}, "bone56": {"translate": [{"x": 4.61, "y": 5.06, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 6.43, "y": 7.07, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2333, "x": 4.61, "y": 5.06}]}, "bone59": {"translate": [{"x": -3.35, "y": -3.67, "curve": 0.334, "c2": 0.34, "c3": 0.676, "c4": 0.7}, {"time": 0.0333, "x": -1.62, "y": -1.78, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -5.71, "y": -6.26, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.2333, "x": -3.35, "y": -3.67}]}, "bone58": {"translate": [{"x": 4.67, "y": 5.84, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "x": 6.53, "y": 8.15, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2333, "x": 4.67, "y": 5.84}]}, "bone55": {"translate": [{"x": 1.64, "y": -0.14, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 5.77, "y": -0.5, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2333, "x": 1.64, "y": -0.14}]}, "bone15": {"rotate": [{"angle": -9.03, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.0333, "angle": -10.83, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 0.2333, "angle": -9.03}]}, "bone69": {"rotate": [{"angle": 6.09, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 6.09}]}, "bone64": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 0.4, "y": -1.94, "curve": 0.248, "c3": 0.736, "c4": 0.94}, {"time": 0.2333, "y": -0.02}]}, "bone63": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 1.77, "y": 3.2, "curve": 0.248, "c3": 0.736, "c4": 0.94}, {"time": 0.2333, "x": 0.02, "y": 0.04}]}, "bone68": {"rotate": [{"angle": 4.36, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 6.09, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2333, "angle": 4.36}]}, "bone71": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -4.96, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "bone67": {"rotate": [{"angle": 1.73, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 6.09, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2333, "angle": 1.73}]}, "bone66": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 6.09, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "bone78": {"translate": [{"x": 1.04, "y": 0.78, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 3.66, "y": 2.75, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2333, "x": 1.04, "y": 0.78}]}, "bone74": {"rotate": [{"angle": -4.96, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -4.96}]}, "bone82": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 3.66, "y": 2.75, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "bone79": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 3.66, "y": 2.75, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "bone77": {"translate": [{"x": 3.58, "y": 2.68, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0333, "x": 2.62, "y": 1.97, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 0.2333, "x": 3.58, "y": 2.68}]}, "bone72": {"rotate": [{"angle": -1.41, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -4.96, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2333, "angle": -1.41}]}, "bone75": {"translate": [{"x": 0.75, "y": 0.56, "curve": 0.315, "c2": 0.28, "c3": 0.673, "c4": 0.69}, {"time": 0.0333, "x": 2.62, "y": 1.97, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "x": 3.66, "y": 2.75, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 0.2333, "x": 0.75, "y": 0.56}]}}}}}