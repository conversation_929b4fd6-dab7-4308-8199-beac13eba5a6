import { UITiaoJianLiBao } from "../../game/ui/ui_hd_tiaojianlibao/UITiaoJianLiBao";
import { Recording, RecordingMap } from "../../lib/ui/recordingMap";
export enum HdTiaoJianLiBaoRouteItem {
  UITiaoJianLiBao = "UITiaoJianLiBao",
}
export class HdTiaoJianLiBaoRoute {




  rotueTables: Recording[] = [
    {
      node: UITiaoJianLiBao,
      uiName: HdTiaoJianLiBaoRouteItem.UITiaoJianLiBao,
      keep: false,
      relevanceUIList: [],
    },
  ];

  public async init() {
    this.rotueTables.forEach((item) => {
      RecordingMap.instance.addRecording(item.uiName, item);
    });
  }
}
