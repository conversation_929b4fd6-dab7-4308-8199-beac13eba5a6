import { tween, Node, v3, UITransform } from "cc";
import { IRunModule, RunState, XiaoyouxiRoleBehavior, XiaoyouxiRoleType } from "./interface/IRunModule";
import { IUIModule, UIState } from "./interface/IUIModule";
import { QiyunXiaoyouxiData, QiyunXiaoyouxiTalkType } from "./QiyunXiaoyouxiData";
import MsgMgr from "../GameScrpit/lib/event/MsgMgr";
import { QiyunXiaoyouxiEvent } from "./QiyunXiaoyouxiEvent";
import { TipsMgr } from "../platform/src/TipsHelper";
import { GuideRouteEnum } from "../GameScrpit/ext_guide/GuideDefine";
import { Sleep } from "../GameScrpit/game/GameDefine";
import { SceneQiyunXiaoyouxi } from "./scene/SceneQiyunXiaoyouxi";
import { XiaoyouxiUIRun } from "./scripts/XiaoyouxiUIRun";
import { Xiao<PERSON>uxiUIMain } from "./scripts/XiaoyouxiUIMain";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
enum RoundState {
  IDLE = 0,

  STATE_ROUND_READY, // 冲撞准备-> 对话2-> 结束后出现冲的UI
  STATE_ROUND_1, // 回合1
  STATE_ROUND_2, // 回合2
  STATE_ROUND_3, // 回合3
  STATE_ROUND_4, // 回合4
  STATE_ROUND_5, // 回合5
  STATE_ROUND_BOSS, //
}

export class QiyunXiaoyouxiMgr {
  private static _instance: QiyunXiaoyouxiMgr = null;
  public static get instance(): QiyunXiaoyouxiMgr {
    if (this._instance == null) {
      this._instance = new QiyunXiaoyouxiMgr();
    }
    return this._instance;
  }
  private _currentRoundState: RoundState = RoundState.STATE_ROUND_READY;

  private _runCtrl: XiaoyouxiUIRun;
  private _sceneCtrl: SceneQiyunXiaoyouxi;
  private _uiCtrl: XiaoyouxiUIMain;
  public position: number = 0;
  private _cameraFollow = false;

  private nodeCamera: Node;

  private _roundTasks: { [key: number]: RoundMainState } = {};

  private onRunCallback(state: RunState, val: any) {
    if (state == RunState.ROLE_MOVE) {
      // if (this._cameraFollow) {
      //   if (val.x + 200 <= this.nodeCamera.worldPosition.x) {
      //     log.error(`onRunCallback: ${val.x} ${this.nodeCamera.worldPosition.x}`);
      //   }
      //   this.nodeCamera.setWorldPosition(v3(val.x + 200, val.y, val.z));
      // } else {
      //   log.log(`onRunCallback1: 状态未跟随`);
      // }
    } else {
      let lastRoundState = this._currentRoundState;
      this._currentRoundState = this._roundTasks[this._currentRoundState].doRunAction(state, val);
      if (lastRoundState != this._currentRoundState) {
        if (this._roundTasks[this._currentRoundState]) {
          this._roundTasks[this._currentRoundState].onStart();
        }
      }
      log.log(`${this._currentRoundState}==`);
    }
    //
  }

  public setCameraFollow(follow: boolean, nodeFollow: Node) {
    this._cameraFollow = follow;
    let worldPos = this.nodeCamera.getWorldPosition();
    // 改为父节点跟随方式
    if (follow) {
      this.nodeCamera.setParent(nodeFollow);
      this.nodeCamera.setWorldPosition(worldPos);
    } else {
      this.nodeCamera.setParent(this._sceneCtrl.node);
      this.nodeCamera.setWorldPosition(worldPos);
    }

    // this.nodeCamera.setParent("")
  }

  public showUI(show: boolean) {
    this._sceneCtrl.showUI(show);
  }

  public showRun(show: boolean) {
    this._sceneCtrl.showRun(show);
  }

  public showFight(show: boolean) {
    this._sceneCtrl.showFight(show);
  }

  public setCamera(node: Node) {
    this.nodeCamera = node;
  }

  private onUICallback(state: UIState) {
    //
    let lastRoundState = this._currentRoundState;
    this._currentRoundState = this._roundTasks[this._currentRoundState].doUIAction(state);
    if (lastRoundState != this._currentRoundState) {
      if (this._roundTasks[this._currentRoundState]) {
        this._roundTasks[this._currentRoundState].onStart();
      }
    }
  }

  public startGame(sceneCtrl: SceneQiyunXiaoyouxi, runCtl: XiaoyouxiUIRun, uiCtl: XiaoyouxiUIMain) {
    //
    this._sceneCtrl = sceneCtrl;
    this._runCtrl = runCtl;
    this._uiCtrl = uiCtl;
    this._runCtrl.onStart(this.onRunCallback.bind(this), 2000);
    this._runCtrl.setNodeCamera(this.nodeCamera);
    QiyunXiaoyouxiData.instance.init();

    this._uiCtrl.onStart(this.onUICallback.bind(this), this._runCtrl.getLeaderNode());

    this._roundTasks[RoundState.STATE_ROUND_READY] = new ReadyState(
      RoundState.STATE_ROUND_READY,
      this._runCtrl,
      this._uiCtrl,
      this.nodeCamera
    );
    this._roundTasks[RoundState.STATE_ROUND_1] = new Round1State(
      RoundState.STATE_ROUND_1,
      this._runCtrl,
      this._uiCtrl,
      this.nodeCamera
    );
    this._roundTasks[RoundState.STATE_ROUND_2] = new RoundNState(
      RoundState.STATE_ROUND_2,
      this._runCtrl,
      this._uiCtrl,
      this.nodeCamera
    );
    this._roundTasks[RoundState.STATE_ROUND_3] = new RoundNState(
      RoundState.STATE_ROUND_3,
      this._runCtrl,
      this._uiCtrl,
      this.nodeCamera
    );
    this._roundTasks[RoundState.STATE_ROUND_4] = new RoundNState(
      RoundState.STATE_ROUND_4,
      this._runCtrl,
      this._uiCtrl,
      this.nodeCamera
    );
    this._roundTasks[RoundState.STATE_ROUND_5] = new RoundBossState(
      RoundState.STATE_ROUND_5,
      this._runCtrl,
      this._uiCtrl,
      this.nodeCamera
    );
    // this._roundTasks[RoundState.STATE_ROUND_BOSS] = new RoundBossState(
    //   RoundState.STATE_ROUND_BOSS,
    //   this._runCtrl,
    //   this._uiCtrl,
    //   this.nodeCamera
    // );

    this._currentRoundState = this._roundTasks[this._currentRoundState].onStart();
  }

  public test1() {}
  public test2() {}
  public test3() {}
}
abstract class RoundMainState {
  protected _state: RoundState;
  protected _runCtrl: XiaoyouxiUIRun;
  protected _uiCtrl: XiaoyouxiUIMain;
  protected _nodeCamera: Node;
  constructor(state: RoundState, runCtrl: XiaoyouxiUIRun, uiCtrl: XiaoyouxiUIMain, nodeCamera: Node) {
    this._state = state;
    this._runCtrl = runCtrl;
    this._uiCtrl = uiCtrl;
    this._nodeCamera = nodeCamera;
  }

  abstract onStart(): RoundState;
  abstract doUIAction(state: UIState): RoundState;
  abstract doRunAction(state: RunState, val: any): RoundState;
}
class StateTask {
  action: () => void;
}
class ReadyState extends RoundMainState {
  stateTasks: StateTask[] = [
    {
      // 主角进场
      action: () => {
        this._runCtrl.roleDo(XiaoyouxiRoleType.ROLE_TYPE_LEAD, XiaoyouxiRoleBehavior.ROLE_IN);
      },
    },
    {
      // 妲己进场
      action: () => {
        this._runCtrl.roleDo(XiaoyouxiRoleType.ROLE_TYPE_DAJI, XiaoyouxiRoleBehavior.ROLE_IN);
      },
    },
    {
      // 妲己对话
      action: () => {
        this._runCtrl.roleDo(
          XiaoyouxiRoleType.ROLE_TYPE_DAJI,
          XiaoyouxiRoleBehavior.ROLE_TALK,
          QiyunXiaoyouxiData.instance.getTalkContent(QiyunXiaoyouxiTalkType.TALK_DAJI_开场)
        );
      },
    },
    // 镜头转场一
    {
      action: () => {
        // 调用云雾转场
        this.storyOne(() => {
          this._runCtrl.roleDo(XiaoyouxiRoleType.ROLE_TYPE_MONSTER, XiaoyouxiRoleBehavior.ROLE_IN);
        });
      },
    },
    {
      action: () => {
        this._runCtrl.roleDo(
          XiaoyouxiRoleType.ROLE_TYPE_MONSTER,
          XiaoyouxiRoleBehavior.ROLE_TALK,
          QiyunXiaoyouxiData.instance.getTalkContent(QiyunXiaoyouxiTalkType.TALK_MONSTER_开场)
        );
      },
    },
    // 镜头移动到BOSS
    {
      action: () => {
        // 小怪对话结束后，BOSS进场
        this.storyThree(() => {
          this._runCtrl.roleDo(XiaoyouxiRoleType.ROLE_TYPE_BOSS, XiaoyouxiRoleBehavior.ROLE_IN);
          // BOSS进场，当BOSS进场结束后
          // this.changeRoleState(RoleState.ROLE_BOSS_IN);
        });
      },
    },
    // BOSS 讲话
    {
      action: () => {
        this._runCtrl.roleDo(
          XiaoyouxiRoleType.ROLE_TYPE_BOSS,
          XiaoyouxiRoleBehavior.ROLE_TALK,
          QiyunXiaoyouxiData.instance.getTalkContent(QiyunXiaoyouxiTalkType.TALK_BOSS_开场)
        );
      },
    },
    // BOSS 离场
    {
      action: () => {
        this._runCtrl.roleDo(
          XiaoyouxiRoleType.ROLE_TYPE_BOSS,
          XiaoyouxiRoleBehavior.ROLE_OUT,
          QiyunXiaoyouxiData.instance.getTalkContent(QiyunXiaoyouxiTalkType.TALK_BOSS_开场)
        );

        tween(this._nodeCamera)
          .delay(0.5)
          .call(() => {
            // 小怪向前冲
            this._runCtrl.roleDo(XiaoyouxiRoleType.ROLE_TYPE_MONSTER, XiaoyouxiRoleBehavior.ROLE_RUN);
          })
          .delay(1.3)
          .call(() => {
            // 相机跟随
            QiyunXiaoyouxiMgr.instance.setCameraFollow(true, this._runCtrl.getMonster01Node());
          })
          .start();
      },
    },
    // 镜头回到主角身上
    {
      action: () => {
        this.storyTwo(() => {
          // 妲己说话
          this._runCtrl.roleDo(
            XiaoyouxiRoleType.ROLE_TYPE_DAJI,
            XiaoyouxiRoleBehavior.ROLE_TALK,
            QiyunXiaoyouxiData.instance.getTalkContent(QiyunXiaoyouxiTalkType.TALK_DAJI_进攻)
          );

          // 进入备战状态 显示妲己进场
          // this.changeGameState(RoundState.STATE_ROUND_READY_2);
          // this._runCtrl.roleDo(XiaoyouxiRoleType.ROLE_TYPE_DAJI, XiaoyouxiRoleBehavior.ROLE_IN);
        });
      },
    },
    // 妲己离场
    {
      action: () => {
        this._runCtrl.roleDo(XiaoyouxiRoleType.ROLE_TYPE_DAJI, XiaoyouxiRoleBehavior.ROLE_OUT);
      },
    },
    // 显示UI
    {
      action: async () => {
        TipsMgr.setEnableTouch(false, 1);
        this._uiCtrl.onShowUI(true);
        this._runCtrl.roleDo(XiaoyouxiRoleType.ROLE_TYPE_LEAD, XiaoyouxiRoleBehavior.ROLE_UI);
      },
    },
  ];
  taskIndex = 0;

  // 镜头一 主角进场后，镜头移动向怪物
  private storyOne(callback?: Function) {
    // 显示云雾转场
    TipsMgr.topRouteCtrl.show(GuideRouteEnum.TopLoading2, async () => {
      let worldX = this._runCtrl.getLeaderNode().worldPosition.x + 500;
      this._nodeCamera.setWorldPosition(worldX, this._nodeCamera.worldPosition.y, this._nodeCamera.worldPosition.z);
      await Sleep(0.5);
    });

    tween<Node>(this._nodeCamera)
      .delay(2)
      .to(
        2,
        {
          worldPosition: v3(
            this._runCtrl.getMonsterPosition().x,
            this._nodeCamera.worldPosition.y,
            this._nodeCamera.worldPosition.z
          ),
        },
        { easing: "cubicInOut" }
      )
      .call(() => {
        callback?.();
      })
      .start();
  }

  // 镜头二 镜头回到主角身上
  private storyTwo(callback?: Function) {
    // 显示云雾转场
    TipsMgr.topRouteCtrl.show(GuideRouteEnum.TopLoading2, async () => {
      QiyunXiaoyouxiMgr.instance.setCameraFollow(false, this._runCtrl.getLeaderNode());

      await Sleep(0.5);
    });

    tween<Node>(this._nodeCamera)
      .delay(0.5)
      .to(
        1,
        {
          worldPosition: v3(
            this._runCtrl.getLeaderPosition().x + 200,
            this._nodeCamera.worldPosition.y,
            this._nodeCamera.worldPosition.z
          ),
        },
        { easing: "cubicOut" }
      )
      .call(() => {
        callback?.();
      })
      .start();
  }

  //
  private storyThree(callback?: Function) {
    tween<Node>(this._nodeCamera)
      .to(
        1,
        {
          worldPosition: v3(
            this._runCtrl.getBossPosition().x,
            this._nodeCamera.worldPosition.y,
            this._nodeCamera.worldPosition.z
          ),
        },
        { easing: "cubicInOut" }
      )
      .call(() => {
        //
        callback?.();
      })
      .start();
  }
  onStart(): RoundState {
    this.stateTasks.shift()?.action();
    return this._state;
  }
  doUIAction(state: UIState): RoundState {
    //
    if (state == UIState.GO_RUN) {
      //
    } else if (state == UIState.UPGRADE) {
      //
    }
    return this._state;
  }
  doRunAction(state: RunState, val: any): RoundState {
    //
    // if (state == RunState.ACT_END) {
    //   //
    // } else if (state == RunState.TALK_END) {
    //   //
    // }
    this.stateTasks.shift()?.action();
    if (this.stateTasks.length == 0) {
      return RoundState.STATE_ROUND_1;
    }
    return this._state;
  }
}
class Round1State extends RoundMainState {
  onStart(): RoundState {
    QiyunXiaoyouxiData.instance.setCurrentTaskIndex(this._state - RoundState.STATE_ROUND_1);
    QiyunXiaoyouxiMgr.instance.setCameraFollow(true, this._runCtrl.getLeaderNode());
    log.log("RoundNState=======任务索引", this._state, QiyunXiaoyouxiData.instance.getCurrentTaskIndex());
    return this._state;
  }

  doUIAction(state: UIState): RoundState {
    if (state == UIState.GO_RUN) {
      let maxQiyun = QiyunXiaoyouxiData.instance.currentQiyun;
      log.log("当前最大气运", maxQiyun);
      this._runCtrl.roleDo(XiaoyouxiRoleType.ROLE_TYPE_LEAD, XiaoyouxiRoleBehavior.ROLE_RUN, maxQiyun);

      MsgMgr.emit(QiyunXiaoyouxiEvent.QIYUN_NOT_ENOUGH);
      return this._state;
    }
    return this._state;
  }

  doRunAction(state: RunState, val: any): RoundState {
    this._uiCtrl.onShowNvwa(true);
    return RoundState.STATE_ROUND_2;
  }
}

class RoundNState extends RoundMainState {
  stateTasks: StateTask[] = [
    // 妲己进场
    {
      action: () => {
        this._runCtrl.roleDo(XiaoyouxiRoleType.ROLE_TYPE_DAJI, XiaoyouxiRoleBehavior.ROLE_IN);
        this._uiCtrl.onShowTask(false);
        QiyunXiaoyouxiData.instance.consumeQiyun(QiyunXiaoyouxiData.instance.currentQiyun);
      },
    },
    // 妲己说话
    {
      action: () => {
        this._runCtrl.roleDo(
          XiaoyouxiRoleType.ROLE_TYPE_DAJI,
          XiaoyouxiRoleBehavior.ROLE_TALK,
          QiyunXiaoyouxiData.instance.getTalkContent(QiyunXiaoyouxiTalkType.TALK_DAJI_气运不足)
        );
      },
    },
    // 妲己离场
    {
      action: () => {
        this._runCtrl.roleDo(XiaoyouxiRoleType.ROLE_TYPE_DAJI, XiaoyouxiRoleBehavior.ROLE_OUT);
      },
    },
    {
      action: () => {
        this._uiCtrl.showFinger(this._uiCtrl.nodeBtnStatuary.worldPosition, 1);
        this._uiCtrl.onShowTask(true);
        this._uiCtrl.onShowNvwa(true);
      },
    },
    // 妲己进场
    {
      action: () => {
        this._uiCtrl.onShowTask(false);
        this._runCtrl.roleDo(XiaoyouxiRoleType.ROLE_TYPE_DAJI, XiaoyouxiRoleBehavior.ROLE_IN);
      },
    },
    // 妲己说话
    {
      action: () => {
        this._runCtrl.roleDo(
          XiaoyouxiRoleType.ROLE_TYPE_DAJI,
          XiaoyouxiRoleBehavior.ROLE_TALK,
          QiyunXiaoyouxiData.instance.getTalkContent(QiyunXiaoyouxiTalkType.TALK_DAJI_气运充足)
        );
      },
    },
    // 妲己离场
    {
      action: () => {
        this._runCtrl.roleDo(XiaoyouxiRoleType.ROLE_TYPE_DAJI, XiaoyouxiRoleBehavior.ROLE_OUT);
      },
    },
    // 主角升级后任务消失
    {
      action: async () => {
        // this._runCtrl.roleDo(XiaoyouxiRoleType.ROLE_TYPE_LEAD, XiaoyouxiRoleBehavior.ROLE_UPGRADE);
        this._uiCtrl.onShowTask(false);
        this._uiCtrl.onShowGo(true);
        await Sleep(0.4);
        this._uiCtrl.showFinger(this._uiCtrl.nodeBtnGo.worldPosition, 1);
      },
    },
    // 冲关结束
    {
      action: () => {},
    },
  ];
  onStart(): RoundState {
    QiyunXiaoyouxiData.instance.setCurrentTaskIndex(this._state - RoundState.STATE_ROUND_1);
    QiyunXiaoyouxiMgr.instance.setCameraFollow(true, this._runCtrl.getLeaderNode());
    log.log("RoundNState=======任务索引", this._state, QiyunXiaoyouxiData.instance.getCurrentTaskIndex());
    this.stateTasks.shift()?.action();
    return this._state;
  }
  doUIAction(state: UIState): RoundState {
    if (state == UIState.GO_RUN) {
      let maxQiyun = QiyunXiaoyouxiData.instance.currentQiyun;
      log.log("当前最大气运", maxQiyun);
      this._runCtrl.roleDo(XiaoyouxiRoleType.ROLE_TYPE_LEAD, XiaoyouxiRoleBehavior.ROLE_RUN, maxQiyun);

      MsgMgr.emit(QiyunXiaoyouxiEvent.QIYUN_NOT_ENOUGH);
      return this._state;
    } else {
      let lv = this._state;
      this._runCtrl.roleDo(XiaoyouxiRoleType.ROLE_TYPE_LEAD, XiaoyouxiRoleBehavior.ROLE_UPGRADE, lv);
    }
    return this._state;
  }
  doRunAction(state: RunState, val: any): RoundState {
    this.stateTasks.shift()?.action();
    if (this.stateTasks.length == 0) {
      return this._state + 1;
    }
    return this._state;
  }
}
class RoundBossState extends RoundMainState {
  stateTasks: StateTask[] = [
    // 妲己进场
    {
      action: () => {
        this._runCtrl.roleDo(XiaoyouxiRoleType.ROLE_TYPE_DAJI, XiaoyouxiRoleBehavior.ROLE_IN);
        this._uiCtrl.onShowTask(false);
        QiyunXiaoyouxiData.instance.consumeQiyun(QiyunXiaoyouxiData.instance.currentQiyun);
      },
    },
    // 妲己说话
    {
      action: () => {
        this._runCtrl.roleDo(
          XiaoyouxiRoleType.ROLE_TYPE_DAJI,
          XiaoyouxiRoleBehavior.ROLE_TALK,
          QiyunXiaoyouxiData.instance.getTalkContent(QiyunXiaoyouxiTalkType.TALK_DAJI_气运不足)
        );
      },
    },
    // 妲己离场
    {
      action: async () => {
        this._runCtrl.roleDo(XiaoyouxiRoleType.ROLE_TYPE_DAJI, XiaoyouxiRoleBehavior.ROLE_OUT);
        this._uiCtrl.onShowNvwa(true);
        await Sleep(0.4);
        this._uiCtrl.showFinger(this._uiCtrl.nodeBtnStatuary.worldPosition, 1);
      },
    },
    {
      action: () => {
        this._uiCtrl.onShowTask(true);
      },
    },
    // 妲己进场
    {
      action: () => {
        this._uiCtrl.onShowTask(false);
        this._runCtrl.roleDo(XiaoyouxiRoleType.ROLE_TYPE_DAJI, XiaoyouxiRoleBehavior.ROLE_IN);
      },
    },
    // 妲己说话
    {
      action: () => {
        this._runCtrl.roleDo(
          XiaoyouxiRoleType.ROLE_TYPE_DAJI,
          XiaoyouxiRoleBehavior.ROLE_TALK,
          QiyunXiaoyouxiData.instance.getTalkContent(QiyunXiaoyouxiTalkType.TALK_DAJI_气运充足)
        );
      },
    },
    // 妲己离场
    {
      action: () => {
        this._runCtrl.roleDo(XiaoyouxiRoleType.ROLE_TYPE_DAJI, XiaoyouxiRoleBehavior.ROLE_OUT);
      },
    },
    // 主角升级后任务消失
    {
      action: async () => {
        // this._runCtrl.roleDo(XiaoyouxiRoleType.ROLE_TYPE_LEAD, XiaoyouxiRoleBehavior.ROLE_UPGRADE);
        this._uiCtrl.onShowTask(false);
        this._uiCtrl.onShowGo(true);
        await Sleep(0.4);
        this._uiCtrl.showFinger(this._uiCtrl.nodeBtnGo.worldPosition, 1);
      },
    },
    {
      action: () => {
        QiyunXiaoyouxiMgr.instance.showUI(false);
        setTimeout(() => {
          this.storyEnd(() => {
            this._runCtrl.roleDo(XiaoyouxiRoleType.ROLE_TYPE_LEAD, XiaoyouxiRoleBehavior.ROLE_OUT);
            this._runCtrl.roleDo(XiaoyouxiRoleType.ROLE_TYPE_BOSS, XiaoyouxiRoleBehavior.ROLE_IN);
          });
          QiyunXiaoyouxiMgr.instance.setCameraFollow(false, this._runCtrl.getLeaderNode());
        }, 2000);
      },
    },
    {
      action: () => {
        this._runCtrl.roleDo(
          XiaoyouxiRoleType.ROLE_TYPE_BOSS,
          XiaoyouxiRoleBehavior.ROLE_TALK,
          QiyunXiaoyouxiData.instance.getTalkContent(QiyunXiaoyouxiTalkType.TALK_BOSS_终极挑战)
        );
      },
    },
    {
      action: () => {
        TipsMgr.topRouteCtrl.show(GuideRouteEnum.TopLoading2, async () => {
          await Sleep(0.2);
          QiyunXiaoyouxiMgr.instance.showFight(true);
        });
      },
    },
  ];
  // 镜头三 镜头加速到boss身上
  private storyEnd(callback?: Function) {
    // CameraThreeDis
    tween<Node>(this._nodeCamera)
      .to(
        3,
        {
          worldPosition: v3(
            // this._runCtrl.getBossPosition().x,
            // this._runCtrl.getBossPosition().x - 2000,
            this._runCtrl.getLeaderPosition().x + 2000,
            this._nodeCamera.worldPosition.y,
            this._nodeCamera.worldPosition.z
          ),
        },
        // { easing: "cubicInOut" }
        { easing: "cubicOut" }
      )
      .call(() => {
        //
        callback?.();
      })
      .start();
  }
  onStart(): RoundState {
    QiyunXiaoyouxiData.instance.setCurrentTaskIndex(this._state - RoundState.STATE_ROUND_1);
    log.log("RoundBossState=======任务索引", QiyunXiaoyouxiData.instance.getCurrentTaskIndex());
    this.stateTasks.shift()?.action();
    return this._state;
  }

  doUIAction(state: UIState): RoundState {
    if (state == UIState.GO_RUN) {
      let maxQiyun = QiyunXiaoyouxiData.instance.currentQiyun;
      log.log("当前最大气运", maxQiyun);
      this._runCtrl.roleDo(XiaoyouxiRoleType.ROLE_TYPE_LEAD, XiaoyouxiRoleBehavior.ROLE_RUN, maxQiyun);
      MsgMgr.emit(QiyunXiaoyouxiEvent.QIYUN_NOT_ENOUGH);
      return this._state;
    } else {
      let lv = this._state;
      this._runCtrl.roleDo(XiaoyouxiRoleType.ROLE_TYPE_LEAD, XiaoyouxiRoleBehavior.ROLE_UPGRADE, lv);
    }
    return this._state;
  }

  doRunAction(state: RunState, val: any): RoundState {
    this.stateTasks.shift()?.action();
    return this._state;
  }
}
