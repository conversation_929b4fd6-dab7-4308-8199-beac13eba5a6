[{"__type__": "cc.Prefab", "_name": "wcstuowei1", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "wcstuowei1", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}, {"__id__": 4}], "_prefab": {"__id__": 6}, "_lpos": {"__type__": "cc.Vec3", "x": 95.256, "y": -81.71, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 3}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b2mA+FOS1DA7qWrdABhX0L"}, {"__type__": "cc.MotionStreak", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 5}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_preview": true, "_fadeTime": 0.2, "_minSeg": 1.2, "_stroke": 64, "_texture": {"__uuid__": "ed959105-9642-42f7-8e5d-db4598ad1c86@6c48a", "__expectedType__": "cc.Texture2D"}, "_fastMode": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bapFBt7aZMPYtKPsqEere9"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "763eQiA/1JVbGNpmi5lkfC", "instance": null, "targetOverrides": null}]