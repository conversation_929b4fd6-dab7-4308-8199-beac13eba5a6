<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>bg_meiminglu_1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{736,140}</string>
                <key>spriteSourceSize</key>
                <string>{736,140}</string>
                <key>textureRect</key>
                <string>{{1,1},{736,140}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>bg_meiminglu_10.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{736,140}</string>
                <key>spriteSourceSize</key>
                <string>{736,140}</string>
                <key>textureRect</key>
                <string>{{1279,1},{736,140}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>bg_meiminglu_11.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{736,140}</string>
                <key>spriteSourceSize</key>
                <string>{736,140}</string>
                <key>textureRect</key>
                <string>{{1421,1},{736,140}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>bg_meiminglu_12.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{736,140}</string>
                <key>spriteSourceSize</key>
                <string>{736,140}</string>
                <key>textureRect</key>
                <string>{{1563,1},{736,140}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>bg_meiminglu_2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{736,140}</string>
                <key>spriteSourceSize</key>
                <string>{736,140}</string>
                <key>textureRect</key>
                <string>{{143,1},{736,140}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>bg_meiminglu_3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{736,140}</string>
                <key>spriteSourceSize</key>
                <string>{736,140}</string>
                <key>textureRect</key>
                <string>{{285,1},{736,140}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>bg_meiminglu_4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{736,140}</string>
                <key>spriteSourceSize</key>
                <string>{736,140}</string>
                <key>textureRect</key>
                <string>{{427,1},{736,140}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>bg_meiminglu_5.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{736,140}</string>
                <key>spriteSourceSize</key>
                <string>{736,140}</string>
                <key>textureRect</key>
                <string>{{569,1},{736,140}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>bg_meiminglu_6.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{736,140}</string>
                <key>spriteSourceSize</key>
                <string>{736,140}</string>
                <key>textureRect</key>
                <string>{{711,1},{736,140}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>bg_meiminglu_7.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{736,140}</string>
                <key>spriteSourceSize</key>
                <string>{736,140}</string>
                <key>textureRect</key>
                <string>{{853,1},{736,140}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>bg_meiminglu_8.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{736,140}</string>
                <key>spriteSourceSize</key>
                <string>{736,140}</string>
                <key>textureRect</key>
                <string>{{995,1},{736,140}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>bg_meiminglu_9.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{736,140}</string>
                <key>spriteSourceSize</key>
                <string>{736,140}</string>
                <key>textureRect</key>
                <string>{{1137,1},{736,140}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>btn_gantanhao_meiming.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{57,57}</string>
                <key>spriteSourceSize</key>
                <string>{57,57}</string>
                <key>textureRect</key>
                <string>{{1705,1},{57,57}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>friendBellesIcon2.png</string>
            <key>size</key>
            <string>{1763,738}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:03184400c85a16d36652243ec543b1ad:b729092a95d6ab3791835e7a259cca76:ae5eb0612d4578593a135d3311219ca9$</string>
            <key>textureFileName</key>
            <string>friendBellesIcon2.png</string>
        </dict>
    </dict>
</plist>
