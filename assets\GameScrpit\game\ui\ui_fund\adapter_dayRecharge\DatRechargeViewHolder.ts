import { _decorator, Input, instantiate, isValid, <PERSON>, Node, ScrollView } from "cc";
import { ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { Sleep } from "../../../GameDefine";
import { avId1, DayActivityModule } from "db://assets/GameScrpit/module/day/DayActivityModule";
import MsgMgr from "db://assets/GameScrpit/lib/event/MsgMgr";
import MsgEnum from "../../../event/MsgEnum";
import { BadgeMgr, BadgeType } from "../../../mgr/BadgeMgr";
import { RedeemMessage, RedeemRequest, RedeemResponse } from "../../../net/protocol/Activity";
import ToolExt from "../../../common/ToolExt";
import { PlayerModule } from "db://assets/GameScrpit/module/player/PlayerModule";
import GameHttpApi from "../../../httpNet/GameHttpApi";
import { UIMgr } from "db://assets/GameScrpit/lib/ui/UIMgr";
import { GoodsRouteName } from "db://assets/GameScrpit/module/goods/GoodsRoute";
import { DayActivityMsgEnum } from "db://assets/GameScrpit/module/day/DayActivityConfig";
import { dtTime } from "../../../BoutStartUp";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "db://assets/GameScrpit/lib/utils/FmUtils";
import { RedeemPackVO } from "db://assets/GameScrpit/module/activity/ActivityConfig";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;
const db_info: string = "db_info";
@ccclass("DatRechargeViewHolder")
export class DatRechargeViewHolder extends ViewHolder {
  private _redeemMessage: RedeemMessage = null;

  private _btn_goumai: Node;
  private _lbl_day_item_name: Node;
  private _lbl_price: Node;
  private _lbl_buy_max: Node;
  private _yishouqin: Node;
  private _ScrollView: Node;
  private _item_content: Node;
  private _type1_item: Node;
  public init() {
    this.onRegEvent();
    this._redeemMessage = DayActivityModule.data.dayMessage;
    this._btn_goumai = this.node.getChildByName("btn_goumai");
    this._btn_goumai.on(Input.EventType.TOUCH_END, this.on_click_btn_goumai, this);

    this._lbl_day_item_name = this.node.getChildByName("lbl_day_item_name");
    this._lbl_price = this.node.getChildByPath("btn_goumai/lbl_price");
    this._lbl_buy_max = this.node.getChildByName("lbl_buy_max");
    this._yishouqin = this.node.getChildByName("yishouqin");
    this._ScrollView = this.node.getChildByName("ScrollView");
    this._item_content = this._ScrollView.getChildByPath("view/item_content");
    this._type1_item = this.node.getChildByName("type1_item");
  }

  protected onDestroy(): void {
    this.onDelEvent();
  }

  private onRegEvent(): void {
    MsgMgr.on(MsgEnum.ON_ACTIVITY_DAILY_GIFT_BUT_UP, this.up_change_main, this);
  }
  private onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_ACTIVITY_DAILY_GIFT_BUT_UP, this.up_change_main, this);
  }

  private up_change_main(args: any) {
    this._redeemMessage.redeemMap = args.redeemMap;
  }

  updateData(data: RedeemPackVO, position: number) {
    //log.log("礼包单个信息====", data);
    let redem_num = this._redeemMessage.redeemMap[data.id] || 0;
    let show_bool = redem_num >= data.max ? true : false; //true 是代表已经售空
    // if (show_bool == true) {
    //   //log.log("已经售空", data);
    // }

    this.set_day_item_type1(data);
    this.load_item_type1(data.rewardList);

    if (data.price == 0 && data.adNum == 0 && !data.cost) {
      BadgeMgr.instance.setBadgeId(
        this._btn_goumai,
        BadgeType.UITerritory.btn_chongzhihaoli.FundBanner001.btn_type1["MLGB" + data.id].id
      );
    }
  }

  private set_day_item_type1(info: RedeemPackVO) {
    this._lbl_day_item_name.getComponent(Label).string = info.name || "没有名字";

    /**代表是免费礼包 ， 不要钱免费送 */
    if (info.adNum == 0 && info.price == 0) {
      this._lbl_price.getComponent(Label).string = "免费";
    } else if (info.price > 0) {
      /**收费礼包，要人民币 */
      this._lbl_price.getComponent(Label).string = (info.price % 10000) + "元";
    }

    let is_ad = info.adNum > 0 ? true : false;
    /**是广告礼包，判断广告次数情况 */
    if (is_ad == true) {
    }

    /**次数判断，是否显示购买按钮售空状态 */

    let redem_num = this._redeemMessage.redeemMap[info.id] || 0;
    let show_bool = redem_num >= info.max ? true : false; //true 是代表已经售空
    this._yishouqin.active = show_bool;
    this._btn_goumai.active = !show_bool;
    this._lbl_buy_max.active = !show_bool;
    if (show_bool == false) {
      this._lbl_buy_max.getComponent(Label).string =
        ToolExt.getMaxtypeLab(info.maxtype) + `(${info.max - redem_num}/${info.max})`;
    }
    this._btn_goumai[db_info] = info;
  }

  private async load_item_type1(list: number[]) {
    let rewardList = ToolExt.traAwardItemMapList(list);
    this._item_content.destroyAllChildren();
    for (let i = 0; i < rewardList.length; i++) {
      await Sleep(dtTime);
      if (isValid(this.node) == false) return;
      let node = instantiate(this._type1_item);
      node.active = true;

      this._item_content.addChild(node);
      FmUtils.setItemNode(node, rewardList[i].id, rewardList[i].num);
    }
    if (rewardList.length <= 3) {
      this._ScrollView.getComponent(ScrollView).enabled = false;
    }
  }

  private on_click_btn_goumai(event) {
    let node: Node = event.target;
    let info: RedeemPackVO = node[db_info];
    if (info.price == 0) {
      let param: RedeemRequest = {
        activityId: avId1,
        redeemId: info.id,
        count: 1,
      };

      DayActivityModule.api.buyFixedPack(param, (res: RedeemResponse) => {
        let rewardList = res.rewardList;
        MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: rewardList });
        MsgMgr.emit(DayActivityMsgEnum.REFRESH_DAY_GIFT_LIST, res.redeemMap);
      });
      return;
    }

    let goodsId = info.id;
    let goodsType = 8;
    let playerId = PlayerModule.data.playerId;
    let orderAmount = info.price % 10000;
    let goodsName = info.name;
    let platformType = "TEST";
    this.onPay(goodsId, goodsType, playerId, orderAmount, goodsName, platformType);
  }

  private onPay(
    goodsId: number,
    goodsType: number,
    playerId: number,
    orderAmount: number,
    goodsName: string,
    platformType: string
  ) {
    let data = {
      goodsId: goodsId,
      goodsType: goodsType,
      playerId: playerId,
      orderAmount: orderAmount,
      goodsName: goodsName,
      platformType: platformType,
    };

    // log.log("每日礼包购买参数=====", data);
    GameHttpApi.pay(data).then((resp: any) => {
      // window.open(resp.data.url);
      if (resp.code != 200) {
        let err = JSON.parse(resp.msg);
        log.log(err);
        return;
      }
      UIMgr.instance.showDialog(GoodsRouteName.UIPay, { url: resp.data.url });
    });
  }
}
