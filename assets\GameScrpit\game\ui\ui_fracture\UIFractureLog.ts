import { _decorator } from "cc";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { FractureModule } from "../../../module/fracture/FractureModule";
import { FractureLogMessage } from "../../net/protocol/Activity";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import { FractureLogAdapter } from "./adapter/FractureLogViewholder";
import { AdapterView } from "../../../../platform/src/core/ui/adapter_view/AdapterView";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { routeConfig } from "db://assets/platform/src/core/managers/RouteTableManager";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
const { ccclass, property } = _decorator;

/**
 * i<PERSON>_huang
 * Sun May 25 2025 10:39:17 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/ui_fracture/UIFractureLog.ts
 * https://docs.cocos.com/creator/3.8/manual/zh/
 *
 */
const log = Logger.getLoger(LOG_LEVEL.DEBUG);
@ccclass("UIFractureLog")
@routeConfig({
  bundle: BundleEnum.BUNDLE_G_FRACTURE,
  url: "prefab/ui/UIFractureLog",
  nextHop: [],
  exit: "",
})
export class UIFractureLog extends BaseCtrl {
  public playShowAni: boolean = true;
  private _adapter: FractureLogAdapter = null;
  protected start(): void {
    super.start();
    this._adapter = new FractureLogAdapter(this.getNode("log_viewholder"));
    this.getNode("node_log_list").getComponent(AdapterView).setAdapter(this._adapter);
    FractureModule.api.fractureLogList((data: FractureLogMessage[]) => {
      // log.log("fractureLogList", data);
      this._adapter.setDatas(data);
    });
    MsgMgr.on(MsgEnum.ON_FRACTURE_UPDATE, this.onFractureUpdate, this);
  }
  private onFractureUpdate() {
    log.log("assist occur onFractureUpdate");
    FractureModule.api.fractureLogList((data: FractureLogMessage[]) => {
      // log.log("fractureLogList", data);
      this._adapter.setDatas(data);
    });
  }
  protected onDestroy(): void {
    super.onDestroy();
    MsgMgr.off(MsgEnum.ON_FRACTURE_UPDATE, this.onFractureUpdate, this);
  }
  update(deltaTime: number) {}
}
