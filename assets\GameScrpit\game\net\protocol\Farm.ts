// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v4.25.1
// source: Farm.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { PlayerBaseMessage, PlayerSimpleMessage } from "./Player";

export const protobufPackage = "sim";

/**  */
export interface FarmBeeMetricMessage {
  /** 还可以雇佣的蜜蜂数量 */
  canHireBeeCnt: number;
  /** 空闲蜜蜂数量 */
  relaxBeeCnt: number;
  /** 总蜜蜂数量 */
  totalBeeCnt: number;
}

/**  */
export interface FarmCollectRequest {
  /** 采集的用户 */
  farmUserId: number;
  /** 采集的葫芦索引，从0开始 */
  rank: number;
  /** 葫芦的唯一标识 */
  identifyId: number;
  /** 采集的蜜蜂的数量 数量0就是找回 */
  beeCnf: number;
}

/**  */
export interface FarmCollectResponse {
  /** 当前正在进行采摘的福地指定的槽位信息 */
  farmSlot:
    | FarmSlotMessage
    | undefined;
  /** 剩余的采集次数 */
  remainFetch: number;
  /** 需要删除的采集记录 默认-1 */
  delDispatchKey: number;
  /** 需要更新的自己的的蜜蜂游历信息 指当前采集的信息 */
  updateDispatchMap: { [key: number]: FarmDispatchMessage };
  /** 自己福地的蜜蜂汇总统计信息 */
  farmBeeMetric: FarmBeeMetricMessage | undefined;
}

export interface FarmCollectResponse_UpdateDispatchMapEntry {
  key: number;
  value: FarmDispatchMessage | undefined;
}

/**  */
export interface FarmCollectorMessage {
  /** 当前采集用户的信息 */
  playerBaseMessage:
    | PlayerBaseMessage
    | undefined;
  /** 蜜蜂数量 */
  beeNum: number;
  /** 采集的结束时间 -1表示抢夺失败 */
  endTime: number;
  /** 开始时长 */
  startTime: number;
  /** 剩余的时长 */
  remainTime: number;
}

/**  */
export interface FarmDispatchMessage {
  /** 福地主人信息 */
  farmerMessage:
    | PlayerBaseMessage
    | undefined;
  /** 竞争对手的信息 */
  otherMessage:
    | PlayerBaseMessage
    | undefined;
  /** 葫芦的质量 */
  gourdId: number;
  /** 自己派遣的蜜蜂数量 */
  ownBeeCnt: number;
  /** 对手派遣的蜜蜂数量 */
  otherBeeCnt: number;
  /** 是否是己方获胜 */
  win: boolean;
  /** 采集结束时间 */
  endTime: number;
  /** 开始时长 */
  startTime: number;
  /** 剩余的时长 */
  remainTime: number;
}

/**  */
export interface FarmEnemyResponse {
  /** 仇恨值集合 */
  hatredMap: { [key: number]: number };
  /** 邻居 */
  farmList: FarmSimpleMessage[];
}

export interface FarmEnemyResponse_HatredMapEntry {
  key: number;
  value: number;
}

/**  */
export interface FarmFundBuyResponse {
  fundIndex: number;
  /**  */
  farmFundMessage: FarmFundMessage | undefined;
}

/**  */
export interface FarmFundMessage {
  /** 采集次数 */
  count: number;
  /** 是否已经充值 */
  recharge: boolean;
  /** 是否已经领取了基金成就奖励 */
  take: boolean;
}

/**  */
export interface FarmLogMessage {
  /** 福地主人 */
  farmUserMessage:
    | PlayerBaseMessage
    | undefined;
  /** 采集人员 */
  collectorMessage:
    | PlayerBaseMessage
    | undefined;
  /** 葫芦ID */
  gourdId: number;
  /** 采集结束的时间 */
  endTime: number;
}

/**  */
export interface FarmNeighborResponse {
  /** 下次冷却刷新的截止时间 */
  nextColdRefreshStamp: number;
  /** 邻居 */
  farmList: FarmSimpleMessage[];
}

/**  */
export interface FarmRecallAllResponse {
  /** 玩家的蜜蜂的统计信息 */
  farmBeeMetric:
    | FarmBeeMetricMessage
    | undefined;
  /** 撤回的蜜蜂所在的槽位的变更后的信息 */
  updateSlotList: FarmSlotMessage[];
}

/**  */
export interface FarmRecallResponse {
  /** 召回后的槽位信息 */
  farmSlot:
    | FarmSlotMessage
    | undefined;
  /** 玩家的dispatchMap 移除的蜜蜂派遣信息key */
  delDispatchKey: number;
  /** 玩家的蜜蜂的统计信息 */
  farmBeeMetric: FarmBeeMetricMessage | undefined;
}

/**  */
export interface FarmRewardMessage {
  /** 神迹技能生效的次数 */
  trigger: number;
  rewardList: number[];
}

/**  */
export interface FarmSimpleMessage {
  /** 福地主人信息 */
  simpleMessage:
    | PlayerSimpleMessage
    | undefined;
  /** 槽位信息 */
  slotList: FarmSlotMessage[];
}

/**  */
export interface FarmSlotMessage {
  userId: number;
  /** 槽位的索引 */
  rank: number;
  /** 葫芦品种ID；当为-1时，说明葫芦已经被采集完了，则下面几个属性都没有作用 */
  gourdId: number;
  /** 槽位里的葫芦的唯一标识 gourdId相等，但identifyId不同，就不是同一个葫芦 */
  identifyId: number;
  /** 表示单个蜜蜂在没有其他蜜蜂干扰的情况下完成采集需要的时间 */
  dragTime: number;
  /** 标识存活的截止时间，-1表示无过期时间 专属葫芦是有过期时间的 */
  deadlineStamp: number;
  /** 自身的采集信息 如果userID为-1表示福地主人没有在采集 */
  ownCollectorMessage:
    | FarmCollectorMessage
    | undefined;
  /** 其他玩家的采集信息 如果userID为-1表示没有第三方在采集 */
  otherCollectorMessage: FarmCollectorMessage | undefined;
}

/**  */
export interface FarmTrainMessage {
  /** 槽位的情况 */
  slotList: FarmSlotMessage[];
  /** 蜜蜂派遣情况 key:此次采集的葫芦的唯一标识 */
  dispatchMap: { [key: number]: FarmDispatchMessage };
  /** 蜜蜂统计信息 */
  beeMetric:
    | FarmBeeMetricMessage
    | undefined;
  /** 待领取的奖励 */
  rewardGourdList: number[];
  /** 仇恨值集合 */
  hatredMap: { [key: number]: number };
  /** 当日剩余的采集次数 */
  remainFetch: number;
  /** 总采集次数 */
  totalColCnt: number;
  /** 剩下的广告刷新的次数 */
  remainAdsRefreshCnt: number;
  /** 下次探寻的冷却时间 */
  nextColdRefreshStamp: number;
  /** 基金 */
  fundList: FarmFundMessage[];
}

export interface FarmTrainMessage_DispatchMapEntry {
  key: number;
  value: FarmDispatchMessage | undefined;
}

export interface FarmTrainMessage_HatredMapEntry {
  key: number;
  value: number;
}

/**  */
export interface FundRewardResponse {
  /** 福地基金列表 */
  fundList: FarmFundMessage[];
  /** 奖励列表 */
  rewardList: number[];
}

function createBaseFarmBeeMetricMessage(): FarmBeeMetricMessage {
  return { canHireBeeCnt: 0, relaxBeeCnt: 0, totalBeeCnt: 0 };
}

export const FarmBeeMetricMessage: MessageFns<FarmBeeMetricMessage> = {
  encode(message: FarmBeeMetricMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.canHireBeeCnt !== 0) {
      writer.uint32(8).int32(message.canHireBeeCnt);
    }
    if (message.relaxBeeCnt !== 0) {
      writer.uint32(16).int32(message.relaxBeeCnt);
    }
    if (message.totalBeeCnt !== 0) {
      writer.uint32(24).int32(message.totalBeeCnt);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FarmBeeMetricMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFarmBeeMetricMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.canHireBeeCnt = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.relaxBeeCnt = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.totalBeeCnt = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FarmBeeMetricMessage>, I>>(base?: I): FarmBeeMetricMessage {
    return FarmBeeMetricMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FarmBeeMetricMessage>, I>>(object: I): FarmBeeMetricMessage {
    const message = createBaseFarmBeeMetricMessage();
    message.canHireBeeCnt = object.canHireBeeCnt ?? 0;
    message.relaxBeeCnt = object.relaxBeeCnt ?? 0;
    message.totalBeeCnt = object.totalBeeCnt ?? 0;
    return message;
  },
};

function createBaseFarmCollectRequest(): FarmCollectRequest {
  return { farmUserId: 0, rank: 0, identifyId: 0, beeCnf: 0 };
}

export const FarmCollectRequest: MessageFns<FarmCollectRequest> = {
  encode(message: FarmCollectRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.farmUserId !== 0) {
      writer.uint32(8).int64(message.farmUserId);
    }
    if (message.rank !== 0) {
      writer.uint32(16).int32(message.rank);
    }
    if (message.identifyId !== 0) {
      writer.uint32(24).int64(message.identifyId);
    }
    if (message.beeCnf !== 0) {
      writer.uint32(32).int32(message.beeCnf);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FarmCollectRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFarmCollectRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.farmUserId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.rank = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.identifyId = longToNumber(reader.int64());
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.beeCnf = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FarmCollectRequest>, I>>(base?: I): FarmCollectRequest {
    return FarmCollectRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FarmCollectRequest>, I>>(object: I): FarmCollectRequest {
    const message = createBaseFarmCollectRequest();
    message.farmUserId = object.farmUserId ?? 0;
    message.rank = object.rank ?? 0;
    message.identifyId = object.identifyId ?? 0;
    message.beeCnf = object.beeCnf ?? 0;
    return message;
  },
};

function createBaseFarmCollectResponse(): FarmCollectResponse {
  return { farmSlot: undefined, remainFetch: 0, delDispatchKey: 0, updateDispatchMap: {}, farmBeeMetric: undefined };
}

export const FarmCollectResponse: MessageFns<FarmCollectResponse> = {
  encode(message: FarmCollectResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.farmSlot !== undefined) {
      FarmSlotMessage.encode(message.farmSlot, writer.uint32(10).fork()).join();
    }
    if (message.remainFetch !== 0) {
      writer.uint32(16).int32(message.remainFetch);
    }
    if (message.delDispatchKey !== 0) {
      writer.uint32(24).int64(message.delDispatchKey);
    }
    Object.entries(message.updateDispatchMap).forEach(([key, value]) => {
      FarmCollectResponse_UpdateDispatchMapEntry.encode({ key: key as any, value }, writer.uint32(34).fork()).join();
    });
    if (message.farmBeeMetric !== undefined) {
      FarmBeeMetricMessage.encode(message.farmBeeMetric, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FarmCollectResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFarmCollectResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.farmSlot = FarmSlotMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.remainFetch = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.delDispatchKey = longToNumber(reader.int64());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          const entry4 = FarmCollectResponse_UpdateDispatchMapEntry.decode(reader, reader.uint32());
          if (entry4.value !== undefined) {
            message.updateDispatchMap[entry4.key] = entry4.value;
          }
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.farmBeeMetric = FarmBeeMetricMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FarmCollectResponse>, I>>(base?: I): FarmCollectResponse {
    return FarmCollectResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FarmCollectResponse>, I>>(object: I): FarmCollectResponse {
    const message = createBaseFarmCollectResponse();
    message.farmSlot = (object.farmSlot !== undefined && object.farmSlot !== null)
      ? FarmSlotMessage.fromPartial(object.farmSlot)
      : undefined;
    message.remainFetch = object.remainFetch ?? 0;
    message.delDispatchKey = object.delDispatchKey ?? 0;
    message.updateDispatchMap = Object.entries(object.updateDispatchMap ?? {}).reduce<
      { [key: number]: FarmDispatchMessage }
    >((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = FarmDispatchMessage.fromPartial(value);
      }
      return acc;
    }, {});
    message.farmBeeMetric = (object.farmBeeMetric !== undefined && object.farmBeeMetric !== null)
      ? FarmBeeMetricMessage.fromPartial(object.farmBeeMetric)
      : undefined;
    return message;
  },
};

function createBaseFarmCollectResponse_UpdateDispatchMapEntry(): FarmCollectResponse_UpdateDispatchMapEntry {
  return { key: 0, value: undefined };
}

export const FarmCollectResponse_UpdateDispatchMapEntry: MessageFns<FarmCollectResponse_UpdateDispatchMapEntry> = {
  encode(message: FarmCollectResponse_UpdateDispatchMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== undefined) {
      FarmDispatchMessage.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FarmCollectResponse_UpdateDispatchMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFarmCollectResponse_UpdateDispatchMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = FarmDispatchMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FarmCollectResponse_UpdateDispatchMapEntry>, I>>(
    base?: I,
  ): FarmCollectResponse_UpdateDispatchMapEntry {
    return FarmCollectResponse_UpdateDispatchMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FarmCollectResponse_UpdateDispatchMapEntry>, I>>(
    object: I,
  ): FarmCollectResponse_UpdateDispatchMapEntry {
    const message = createBaseFarmCollectResponse_UpdateDispatchMapEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? FarmDispatchMessage.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseFarmCollectorMessage(): FarmCollectorMessage {
  return { playerBaseMessage: undefined, beeNum: 0, endTime: 0, startTime: 0, remainTime: 0 };
}

export const FarmCollectorMessage: MessageFns<FarmCollectorMessage> = {
  encode(message: FarmCollectorMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.playerBaseMessage !== undefined) {
      PlayerBaseMessage.encode(message.playerBaseMessage, writer.uint32(10).fork()).join();
    }
    if (message.beeNum !== 0) {
      writer.uint32(16).int32(message.beeNum);
    }
    if (message.endTime !== 0) {
      writer.uint32(24).int64(message.endTime);
    }
    if (message.startTime !== 0) {
      writer.uint32(32).int64(message.startTime);
    }
    if (message.remainTime !== 0) {
      writer.uint32(40).int64(message.remainTime);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FarmCollectorMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFarmCollectorMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.playerBaseMessage = PlayerBaseMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.beeNum = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.endTime = longToNumber(reader.int64());
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.startTime = longToNumber(reader.int64());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.remainTime = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FarmCollectorMessage>, I>>(base?: I): FarmCollectorMessage {
    return FarmCollectorMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FarmCollectorMessage>, I>>(object: I): FarmCollectorMessage {
    const message = createBaseFarmCollectorMessage();
    message.playerBaseMessage = (object.playerBaseMessage !== undefined && object.playerBaseMessage !== null)
      ? PlayerBaseMessage.fromPartial(object.playerBaseMessage)
      : undefined;
    message.beeNum = object.beeNum ?? 0;
    message.endTime = object.endTime ?? 0;
    message.startTime = object.startTime ?? 0;
    message.remainTime = object.remainTime ?? 0;
    return message;
  },
};

function createBaseFarmDispatchMessage(): FarmDispatchMessage {
  return {
    farmerMessage: undefined,
    otherMessage: undefined,
    gourdId: 0,
    ownBeeCnt: 0,
    otherBeeCnt: 0,
    win: false,
    endTime: 0,
    startTime: 0,
    remainTime: 0,
  };
}

export const FarmDispatchMessage: MessageFns<FarmDispatchMessage> = {
  encode(message: FarmDispatchMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.farmerMessage !== undefined) {
      PlayerBaseMessage.encode(message.farmerMessage, writer.uint32(10).fork()).join();
    }
    if (message.otherMessage !== undefined) {
      PlayerBaseMessage.encode(message.otherMessage, writer.uint32(18).fork()).join();
    }
    if (message.gourdId !== 0) {
      writer.uint32(24).int32(message.gourdId);
    }
    if (message.ownBeeCnt !== 0) {
      writer.uint32(32).int32(message.ownBeeCnt);
    }
    if (message.otherBeeCnt !== 0) {
      writer.uint32(40).int32(message.otherBeeCnt);
    }
    if (message.win !== false) {
      writer.uint32(48).bool(message.win);
    }
    if (message.endTime !== 0) {
      writer.uint32(56).int64(message.endTime);
    }
    if (message.startTime !== 0) {
      writer.uint32(64).int64(message.startTime);
    }
    if (message.remainTime !== 0) {
      writer.uint32(72).int64(message.remainTime);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FarmDispatchMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFarmDispatchMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.farmerMessage = PlayerBaseMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.otherMessage = PlayerBaseMessage.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.gourdId = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.ownBeeCnt = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.otherBeeCnt = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.win = reader.bool();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.endTime = longToNumber(reader.int64());
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.startTime = longToNumber(reader.int64());
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.remainTime = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FarmDispatchMessage>, I>>(base?: I): FarmDispatchMessage {
    return FarmDispatchMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FarmDispatchMessage>, I>>(object: I): FarmDispatchMessage {
    const message = createBaseFarmDispatchMessage();
    message.farmerMessage = (object.farmerMessage !== undefined && object.farmerMessage !== null)
      ? PlayerBaseMessage.fromPartial(object.farmerMessage)
      : undefined;
    message.otherMessage = (object.otherMessage !== undefined && object.otherMessage !== null)
      ? PlayerBaseMessage.fromPartial(object.otherMessage)
      : undefined;
    message.gourdId = object.gourdId ?? 0;
    message.ownBeeCnt = object.ownBeeCnt ?? 0;
    message.otherBeeCnt = object.otherBeeCnt ?? 0;
    message.win = object.win ?? false;
    message.endTime = object.endTime ?? 0;
    message.startTime = object.startTime ?? 0;
    message.remainTime = object.remainTime ?? 0;
    return message;
  },
};

function createBaseFarmEnemyResponse(): FarmEnemyResponse {
  return { hatredMap: {}, farmList: [] };
}

export const FarmEnemyResponse: MessageFns<FarmEnemyResponse> = {
  encode(message: FarmEnemyResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.hatredMap).forEach(([key, value]) => {
      FarmEnemyResponse_HatredMapEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join();
    });
    for (const v of message.farmList) {
      FarmSimpleMessage.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FarmEnemyResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFarmEnemyResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = FarmEnemyResponse_HatredMapEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.hatredMap[entry1.key] = entry1.value;
          }
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.farmList.push(FarmSimpleMessage.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FarmEnemyResponse>, I>>(base?: I): FarmEnemyResponse {
    return FarmEnemyResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FarmEnemyResponse>, I>>(object: I): FarmEnemyResponse {
    const message = createBaseFarmEnemyResponse();
    message.hatredMap = Object.entries(object.hatredMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.farmList = object.farmList?.map((e) => FarmSimpleMessage.fromPartial(e)) || [];
    return message;
  },
};

function createBaseFarmEnemyResponse_HatredMapEntry(): FarmEnemyResponse_HatredMapEntry {
  return { key: 0, value: 0 };
}

export const FarmEnemyResponse_HatredMapEntry: MessageFns<FarmEnemyResponse_HatredMapEntry> = {
  encode(message: FarmEnemyResponse_HatredMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FarmEnemyResponse_HatredMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFarmEnemyResponse_HatredMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FarmEnemyResponse_HatredMapEntry>, I>>(
    base?: I,
  ): FarmEnemyResponse_HatredMapEntry {
    return FarmEnemyResponse_HatredMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FarmEnemyResponse_HatredMapEntry>, I>>(
    object: I,
  ): FarmEnemyResponse_HatredMapEntry {
    const message = createBaseFarmEnemyResponse_HatredMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseFarmFundBuyResponse(): FarmFundBuyResponse {
  return { fundIndex: 0, farmFundMessage: undefined };
}

export const FarmFundBuyResponse: MessageFns<FarmFundBuyResponse> = {
  encode(message: FarmFundBuyResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.fundIndex !== 0) {
      writer.uint32(8).int32(message.fundIndex);
    }
    if (message.farmFundMessage !== undefined) {
      FarmFundMessage.encode(message.farmFundMessage, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FarmFundBuyResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFarmFundBuyResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.fundIndex = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.farmFundMessage = FarmFundMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FarmFundBuyResponse>, I>>(base?: I): FarmFundBuyResponse {
    return FarmFundBuyResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FarmFundBuyResponse>, I>>(object: I): FarmFundBuyResponse {
    const message = createBaseFarmFundBuyResponse();
    message.fundIndex = object.fundIndex ?? 0;
    message.farmFundMessage = (object.farmFundMessage !== undefined && object.farmFundMessage !== null)
      ? FarmFundMessage.fromPartial(object.farmFundMessage)
      : undefined;
    return message;
  },
};

function createBaseFarmFundMessage(): FarmFundMessage {
  return { count: 0, recharge: false, take: false };
}

export const FarmFundMessage: MessageFns<FarmFundMessage> = {
  encode(message: FarmFundMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.count !== 0) {
      writer.uint32(8).int32(message.count);
    }
    if (message.recharge !== false) {
      writer.uint32(16).bool(message.recharge);
    }
    if (message.take !== false) {
      writer.uint32(24).bool(message.take);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FarmFundMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFarmFundMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.count = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.recharge = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.take = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FarmFundMessage>, I>>(base?: I): FarmFundMessage {
    return FarmFundMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FarmFundMessage>, I>>(object: I): FarmFundMessage {
    const message = createBaseFarmFundMessage();
    message.count = object.count ?? 0;
    message.recharge = object.recharge ?? false;
    message.take = object.take ?? false;
    return message;
  },
};

function createBaseFarmLogMessage(): FarmLogMessage {
  return { farmUserMessage: undefined, collectorMessage: undefined, gourdId: 0, endTime: 0 };
}

export const FarmLogMessage: MessageFns<FarmLogMessage> = {
  encode(message: FarmLogMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.farmUserMessage !== undefined) {
      PlayerBaseMessage.encode(message.farmUserMessage, writer.uint32(10).fork()).join();
    }
    if (message.collectorMessage !== undefined) {
      PlayerBaseMessage.encode(message.collectorMessage, writer.uint32(18).fork()).join();
    }
    if (message.gourdId !== 0) {
      writer.uint32(24).int32(message.gourdId);
    }
    if (message.endTime !== 0) {
      writer.uint32(32).int64(message.endTime);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FarmLogMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFarmLogMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.farmUserMessage = PlayerBaseMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.collectorMessage = PlayerBaseMessage.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.gourdId = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.endTime = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FarmLogMessage>, I>>(base?: I): FarmLogMessage {
    return FarmLogMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FarmLogMessage>, I>>(object: I): FarmLogMessage {
    const message = createBaseFarmLogMessage();
    message.farmUserMessage = (object.farmUserMessage !== undefined && object.farmUserMessage !== null)
      ? PlayerBaseMessage.fromPartial(object.farmUserMessage)
      : undefined;
    message.collectorMessage = (object.collectorMessage !== undefined && object.collectorMessage !== null)
      ? PlayerBaseMessage.fromPartial(object.collectorMessage)
      : undefined;
    message.gourdId = object.gourdId ?? 0;
    message.endTime = object.endTime ?? 0;
    return message;
  },
};

function createBaseFarmNeighborResponse(): FarmNeighborResponse {
  return { nextColdRefreshStamp: 0, farmList: [] };
}

export const FarmNeighborResponse: MessageFns<FarmNeighborResponse> = {
  encode(message: FarmNeighborResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.nextColdRefreshStamp !== 0) {
      writer.uint32(8).int64(message.nextColdRefreshStamp);
    }
    for (const v of message.farmList) {
      FarmSimpleMessage.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FarmNeighborResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFarmNeighborResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.nextColdRefreshStamp = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.farmList.push(FarmSimpleMessage.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FarmNeighborResponse>, I>>(base?: I): FarmNeighborResponse {
    return FarmNeighborResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FarmNeighborResponse>, I>>(object: I): FarmNeighborResponse {
    const message = createBaseFarmNeighborResponse();
    message.nextColdRefreshStamp = object.nextColdRefreshStamp ?? 0;
    message.farmList = object.farmList?.map((e) => FarmSimpleMessage.fromPartial(e)) || [];
    return message;
  },
};

function createBaseFarmRecallAllResponse(): FarmRecallAllResponse {
  return { farmBeeMetric: undefined, updateSlotList: [] };
}

export const FarmRecallAllResponse: MessageFns<FarmRecallAllResponse> = {
  encode(message: FarmRecallAllResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.farmBeeMetric !== undefined) {
      FarmBeeMetricMessage.encode(message.farmBeeMetric, writer.uint32(10).fork()).join();
    }
    for (const v of message.updateSlotList) {
      FarmSlotMessage.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FarmRecallAllResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFarmRecallAllResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.farmBeeMetric = FarmBeeMetricMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.updateSlotList.push(FarmSlotMessage.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FarmRecallAllResponse>, I>>(base?: I): FarmRecallAllResponse {
    return FarmRecallAllResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FarmRecallAllResponse>, I>>(object: I): FarmRecallAllResponse {
    const message = createBaseFarmRecallAllResponse();
    message.farmBeeMetric = (object.farmBeeMetric !== undefined && object.farmBeeMetric !== null)
      ? FarmBeeMetricMessage.fromPartial(object.farmBeeMetric)
      : undefined;
    message.updateSlotList = object.updateSlotList?.map((e) => FarmSlotMessage.fromPartial(e)) || [];
    return message;
  },
};

function createBaseFarmRecallResponse(): FarmRecallResponse {
  return { farmSlot: undefined, delDispatchKey: 0, farmBeeMetric: undefined };
}

export const FarmRecallResponse: MessageFns<FarmRecallResponse> = {
  encode(message: FarmRecallResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.farmSlot !== undefined) {
      FarmSlotMessage.encode(message.farmSlot, writer.uint32(10).fork()).join();
    }
    if (message.delDispatchKey !== 0) {
      writer.uint32(16).int64(message.delDispatchKey);
    }
    if (message.farmBeeMetric !== undefined) {
      FarmBeeMetricMessage.encode(message.farmBeeMetric, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FarmRecallResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFarmRecallResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.farmSlot = FarmSlotMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.delDispatchKey = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.farmBeeMetric = FarmBeeMetricMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FarmRecallResponse>, I>>(base?: I): FarmRecallResponse {
    return FarmRecallResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FarmRecallResponse>, I>>(object: I): FarmRecallResponse {
    const message = createBaseFarmRecallResponse();
    message.farmSlot = (object.farmSlot !== undefined && object.farmSlot !== null)
      ? FarmSlotMessage.fromPartial(object.farmSlot)
      : undefined;
    message.delDispatchKey = object.delDispatchKey ?? 0;
    message.farmBeeMetric = (object.farmBeeMetric !== undefined && object.farmBeeMetric !== null)
      ? FarmBeeMetricMessage.fromPartial(object.farmBeeMetric)
      : undefined;
    return message;
  },
};

function createBaseFarmRewardMessage(): FarmRewardMessage {
  return { trigger: 0, rewardList: [] };
}

export const FarmRewardMessage: MessageFns<FarmRewardMessage> = {
  encode(message: FarmRewardMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.trigger !== 0) {
      writer.uint32(8).int32(message.trigger);
    }
    writer.uint32(18).fork();
    for (const v of message.rewardList) {
      writer.double(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FarmRewardMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFarmRewardMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.trigger = reader.int32();
          continue;
        }
        case 2: {
          if (tag === 17) {
            message.rewardList.push(reader.double());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.rewardList.push(reader.double());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FarmRewardMessage>, I>>(base?: I): FarmRewardMessage {
    return FarmRewardMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FarmRewardMessage>, I>>(object: I): FarmRewardMessage {
    const message = createBaseFarmRewardMessage();
    message.trigger = object.trigger ?? 0;
    message.rewardList = object.rewardList?.map((e) => e) || [];
    return message;
  },
};

function createBaseFarmSimpleMessage(): FarmSimpleMessage {
  return { simpleMessage: undefined, slotList: [] };
}

export const FarmSimpleMessage: MessageFns<FarmSimpleMessage> = {
  encode(message: FarmSimpleMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.simpleMessage !== undefined) {
      PlayerSimpleMessage.encode(message.simpleMessage, writer.uint32(10).fork()).join();
    }
    for (const v of message.slotList) {
      FarmSlotMessage.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FarmSimpleMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFarmSimpleMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.simpleMessage = PlayerSimpleMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.slotList.push(FarmSlotMessage.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FarmSimpleMessage>, I>>(base?: I): FarmSimpleMessage {
    return FarmSimpleMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FarmSimpleMessage>, I>>(object: I): FarmSimpleMessage {
    const message = createBaseFarmSimpleMessage();
    message.simpleMessage = (object.simpleMessage !== undefined && object.simpleMessage !== null)
      ? PlayerSimpleMessage.fromPartial(object.simpleMessage)
      : undefined;
    message.slotList = object.slotList?.map((e) => FarmSlotMessage.fromPartial(e)) || [];
    return message;
  },
};

function createBaseFarmSlotMessage(): FarmSlotMessage {
  return {
    userId: 0,
    rank: 0,
    gourdId: 0,
    identifyId: 0,
    dragTime: 0,
    deadlineStamp: 0,
    ownCollectorMessage: undefined,
    otherCollectorMessage: undefined,
  };
}

export const FarmSlotMessage: MessageFns<FarmSlotMessage> = {
  encode(message: FarmSlotMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== 0) {
      writer.uint32(8).int64(message.userId);
    }
    if (message.rank !== 0) {
      writer.uint32(16).int32(message.rank);
    }
    if (message.gourdId !== 0) {
      writer.uint32(24).int32(message.gourdId);
    }
    if (message.identifyId !== 0) {
      writer.uint32(32).int64(message.identifyId);
    }
    if (message.dragTime !== 0) {
      writer.uint32(40).int64(message.dragTime);
    }
    if (message.deadlineStamp !== 0) {
      writer.uint32(48).int64(message.deadlineStamp);
    }
    if (message.ownCollectorMessage !== undefined) {
      FarmCollectorMessage.encode(message.ownCollectorMessage, writer.uint32(58).fork()).join();
    }
    if (message.otherCollectorMessage !== undefined) {
      FarmCollectorMessage.encode(message.otherCollectorMessage, writer.uint32(66).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FarmSlotMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFarmSlotMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.userId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.rank = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.gourdId = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.identifyId = longToNumber(reader.int64());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.dragTime = longToNumber(reader.int64());
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.deadlineStamp = longToNumber(reader.int64());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.ownCollectorMessage = FarmCollectorMessage.decode(reader, reader.uint32());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.otherCollectorMessage = FarmCollectorMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FarmSlotMessage>, I>>(base?: I): FarmSlotMessage {
    return FarmSlotMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FarmSlotMessage>, I>>(object: I): FarmSlotMessage {
    const message = createBaseFarmSlotMessage();
    message.userId = object.userId ?? 0;
    message.rank = object.rank ?? 0;
    message.gourdId = object.gourdId ?? 0;
    message.identifyId = object.identifyId ?? 0;
    message.dragTime = object.dragTime ?? 0;
    message.deadlineStamp = object.deadlineStamp ?? 0;
    message.ownCollectorMessage = (object.ownCollectorMessage !== undefined && object.ownCollectorMessage !== null)
      ? FarmCollectorMessage.fromPartial(object.ownCollectorMessage)
      : undefined;
    message.otherCollectorMessage =
      (object.otherCollectorMessage !== undefined && object.otherCollectorMessage !== null)
        ? FarmCollectorMessage.fromPartial(object.otherCollectorMessage)
        : undefined;
    return message;
  },
};

function createBaseFarmTrainMessage(): FarmTrainMessage {
  return {
    slotList: [],
    dispatchMap: {},
    beeMetric: undefined,
    rewardGourdList: [],
    hatredMap: {},
    remainFetch: 0,
    totalColCnt: 0,
    remainAdsRefreshCnt: 0,
    nextColdRefreshStamp: 0,
    fundList: [],
  };
}

export const FarmTrainMessage: MessageFns<FarmTrainMessage> = {
  encode(message: FarmTrainMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.slotList) {
      FarmSlotMessage.encode(v!, writer.uint32(10).fork()).join();
    }
    Object.entries(message.dispatchMap).forEach(([key, value]) => {
      FarmTrainMessage_DispatchMapEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    if (message.beeMetric !== undefined) {
      FarmBeeMetricMessage.encode(message.beeMetric, writer.uint32(26).fork()).join();
    }
    writer.uint32(34).fork();
    for (const v of message.rewardGourdList) {
      writer.int32(v);
    }
    writer.join();
    Object.entries(message.hatredMap).forEach(([key, value]) => {
      FarmTrainMessage_HatredMapEntry.encode({ key: key as any, value }, writer.uint32(42).fork()).join();
    });
    if (message.remainFetch !== 0) {
      writer.uint32(48).int32(message.remainFetch);
    }
    if (message.totalColCnt !== 0) {
      writer.uint32(56).int32(message.totalColCnt);
    }
    if (message.remainAdsRefreshCnt !== 0) {
      writer.uint32(64).int64(message.remainAdsRefreshCnt);
    }
    if (message.nextColdRefreshStamp !== 0) {
      writer.uint32(72).int64(message.nextColdRefreshStamp);
    }
    for (const v of message.fundList) {
      FarmFundMessage.encode(v!, writer.uint32(82).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FarmTrainMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFarmTrainMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.slotList.push(FarmSlotMessage.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = FarmTrainMessage_DispatchMapEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.dispatchMap[entry2.key] = entry2.value;
          }
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.beeMetric = FarmBeeMetricMessage.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag === 32) {
            message.rewardGourdList.push(reader.int32());

            continue;
          }

          if (tag === 34) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.rewardGourdList.push(reader.int32());
            }

            continue;
          }

          break;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          const entry5 = FarmTrainMessage_HatredMapEntry.decode(reader, reader.uint32());
          if (entry5.value !== undefined) {
            message.hatredMap[entry5.key] = entry5.value;
          }
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.remainFetch = reader.int32();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.totalColCnt = reader.int32();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.remainAdsRefreshCnt = longToNumber(reader.int64());
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.nextColdRefreshStamp = longToNumber(reader.int64());
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.fundList.push(FarmFundMessage.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FarmTrainMessage>, I>>(base?: I): FarmTrainMessage {
    return FarmTrainMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FarmTrainMessage>, I>>(object: I): FarmTrainMessage {
    const message = createBaseFarmTrainMessage();
    message.slotList = object.slotList?.map((e) => FarmSlotMessage.fromPartial(e)) || [];
    message.dispatchMap = Object.entries(object.dispatchMap ?? {}).reduce<{ [key: number]: FarmDispatchMessage }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = FarmDispatchMessage.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.beeMetric = (object.beeMetric !== undefined && object.beeMetric !== null)
      ? FarmBeeMetricMessage.fromPartial(object.beeMetric)
      : undefined;
    message.rewardGourdList = object.rewardGourdList?.map((e) => e) || [];
    message.hatredMap = Object.entries(object.hatredMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.remainFetch = object.remainFetch ?? 0;
    message.totalColCnt = object.totalColCnt ?? 0;
    message.remainAdsRefreshCnt = object.remainAdsRefreshCnt ?? 0;
    message.nextColdRefreshStamp = object.nextColdRefreshStamp ?? 0;
    message.fundList = object.fundList?.map((e) => FarmFundMessage.fromPartial(e)) || [];
    return message;
  },
};

function createBaseFarmTrainMessage_DispatchMapEntry(): FarmTrainMessage_DispatchMapEntry {
  return { key: 0, value: undefined };
}

export const FarmTrainMessage_DispatchMapEntry: MessageFns<FarmTrainMessage_DispatchMapEntry> = {
  encode(message: FarmTrainMessage_DispatchMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== undefined) {
      FarmDispatchMessage.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FarmTrainMessage_DispatchMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFarmTrainMessage_DispatchMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = FarmDispatchMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FarmTrainMessage_DispatchMapEntry>, I>>(
    base?: I,
  ): FarmTrainMessage_DispatchMapEntry {
    return FarmTrainMessage_DispatchMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FarmTrainMessage_DispatchMapEntry>, I>>(
    object: I,
  ): FarmTrainMessage_DispatchMapEntry {
    const message = createBaseFarmTrainMessage_DispatchMapEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? FarmDispatchMessage.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseFarmTrainMessage_HatredMapEntry(): FarmTrainMessage_HatredMapEntry {
  return { key: 0, value: 0 };
}

export const FarmTrainMessage_HatredMapEntry: MessageFns<FarmTrainMessage_HatredMapEntry> = {
  encode(message: FarmTrainMessage_HatredMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FarmTrainMessage_HatredMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFarmTrainMessage_HatredMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FarmTrainMessage_HatredMapEntry>, I>>(base?: I): FarmTrainMessage_HatredMapEntry {
    return FarmTrainMessage_HatredMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FarmTrainMessage_HatredMapEntry>, I>>(
    object: I,
  ): FarmTrainMessage_HatredMapEntry {
    const message = createBaseFarmTrainMessage_HatredMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseFundRewardResponse(): FundRewardResponse {
  return { fundList: [], rewardList: [] };
}

export const FundRewardResponse: MessageFns<FundRewardResponse> = {
  encode(message: FundRewardResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.fundList) {
      FarmFundMessage.encode(v!, writer.uint32(10).fork()).join();
    }
    writer.uint32(18).fork();
    for (const v of message.rewardList) {
      writer.double(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FundRewardResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFundRewardResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.fundList.push(FarmFundMessage.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag === 17) {
            message.rewardList.push(reader.double());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.rewardList.push(reader.double());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FundRewardResponse>, I>>(base?: I): FundRewardResponse {
    return FundRewardResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FundRewardResponse>, I>>(object: I): FundRewardResponse {
    const message = createBaseFundRewardResponse();
    message.fundList = object.fundList?.map((e) => FarmFundMessage.fromPartial(e)) || [];
    message.rewardList = object.rewardList?.map((e) => e) || [];
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
