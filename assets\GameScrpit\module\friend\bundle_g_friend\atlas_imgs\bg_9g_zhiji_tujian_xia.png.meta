{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "a28ca583-08a6-4fdc-ba26-2ecbf992fbe4", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "a28ca583-08a6-4fdc-ba26-2ecbf992fbe4@6c48a", "displayName": "bg_9g_zhiji_tujian_xia", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "a28ca583-08a6-4fdc-ba26-2ecbf992fbe4", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "a28ca583-08a6-4fdc-ba26-2ecbf992fbe4@f9941", "displayName": "bg_9g_zhiji_tujian_xia", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 321, "height": 19, "rawWidth": 321, "rawHeight": 19, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-160.5, -9.5, 0, 160.5, -9.5, 0, -160.5, 9.5, 0, 160.5, 9.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 19, 321, 19, 0, 0, 321, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-160.5, -9.5, 0], "maxPos": [160.5, 9.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "a28ca583-08a6-4fdc-ba26-2ecbf992fbe4@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": true, "redirect": "a28ca583-08a6-4fdc-ba26-2ecbf992fbe4@6c48a"}}