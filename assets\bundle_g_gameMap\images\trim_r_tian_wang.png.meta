{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "d26ccee3-70b1-4101-9f63-72fdc17af3d7", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "d26ccee3-70b1-4101-9f63-72fdc17af3d7@6c48a", "displayName": "trim_r_tian_wang", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "d26ccee3-70b1-4101-9f63-72fdc17af3d7", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "d26ccee3-70b1-4101-9f63-72fdc17af3d7@f9941", "displayName": "trim_r_tian_wang", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 102, "height": 112, "rawWidth": 102, "rawHeight": 112, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-51, -56, 0, 51, -56, 0, -51, 56, 0, 51, 56, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 112, 102, 112, 0, 0, 102, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-51, -56, 0], "maxPos": [51, 56, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "d26ccee3-70b1-4101-9f63-72fdc17af3d7@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "d26ccee3-70b1-4101-9f63-72fdc17af3d7@6c48a"}}