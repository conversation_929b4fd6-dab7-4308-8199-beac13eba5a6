{"skeleton": {"hash": "0BwrF9uOv4HcX+hXLeTWDjg4mCY=", "spine": "3.8.75", "x": -354.98, "y": -94.24, "width": 686.47, "height": 583.4, "images": "./images/", "audio": "D:/spine导出/灵兽动画/穷奇"}, "bones": [{"name": "root", "scaleX": -1.5216, "scaleY": 1.5216}, {"name": "bone", "parent": "root", "length": 298.62, "rotation": -90, "y": -152.95, "scaleX": 0.6576, "scaleY": 0.6576}, {"name": "bone2", "parent": "bone", "length": 87.38, "rotation": 90.47, "x": -349.73, "y": 423.59}, {"name": "bone3", "parent": "bone2", "length": 106.74, "rotation": 0.68, "x": -325.13, "y": 4.1}, {"name": "bone4", "parent": "bone3", "length": 53.68, "rotation": 67.05, "x": -15.13, "y": 4.78, "color": "559effff"}, {"name": "bone5", "parent": "bone4", "length": 50.87, "rotation": 44.87, "x": 53.68, "color": "559effff"}, {"name": "bone6", "parent": "bone5", "length": 28.5, "rotation": 5.05, "x": 50.87}, {"name": "bone7", "parent": "bone6", "x": 52.95, "y": 9.14, "color": "abe323ff"}, {"name": "bone8", "parent": "bone6", "x": 64.39, "y": -0.04, "color": "abe323ff"}, {"name": "bone9", "parent": "bone7", "x": -40.9, "y": 4.89, "color": "abe323ff"}, {"name": "bone10", "parent": "bone3", "length": 47.58, "rotation": -143.23, "x": -20.31, "y": -1.88, "color": "559effff"}, {"name": "bone11", "parent": "bone10", "length": 53.84, "rotation": -18.02, "x": 47.58, "color": "559effff"}, {"name": "bone12", "parent": "bone11", "length": 85.23, "rotation": 46, "x": 66.09, "y": -20.56, "color": "90009dff"}, {"name": "bone13", "parent": "bone12", "length": 43.86, "rotation": -99.14, "x": 85.23, "color": "90009dff"}, {"name": "bone14", "parent": "bone13", "length": 56.9, "rotation": 131.54, "x": 43.86, "color": "90009dff"}, {"name": "bone15", "parent": "bone14", "length": 44.77, "rotation": 14.99, "x": 56.9, "color": "90009dff"}, {"name": "bone16", "parent": "bone15", "length": 25.42, "rotation": -4.66, "x": 2.54, "y": -36.11, "color": "90009dff"}, {"name": "bone17", "parent": "bone15", "length": 33.02, "rotation": -29.59, "x": 21.25, "y": 35.49, "color": "90009dff"}, {"name": "bone18", "parent": "root", "x": 6.2, "y": -242.69}, {"name": "bone19", "parent": "bone9", "length": 16.19, "rotation": -165.9, "x": -7.74, "y": -5, "color": "abe323ff"}, {"name": "bone20", "parent": "bone19", "length": 16.67, "rotation": 6.02, "x": 16.19, "color": "abe323ff"}, {"name": "bone21", "parent": "bone20", "length": 12.21, "rotation": 4.89, "x": 16.67, "color": "abe323ff"}, {"name": "bone22", "parent": "bone9", "length": 13.39, "rotation": -123.83, "x": 3.74, "y": -17.43, "color": "abe323ff"}, {"name": "bone23", "parent": "bone22", "length": 16.99, "rotation": -11.78, "x": 13.39, "color": "abe323ff"}, {"name": "bone24", "parent": "bone9", "length": 11.79, "rotation": 148.64, "x": -8.87, "y": 4.67, "color": "abe323ff"}, {"name": "bone25", "parent": "bone24", "length": 9.64, "rotation": 17.92, "x": 11.55, "y": 0.21, "color": "abe323ff"}, {"name": "bone26", "parent": "bone9", "length": 10.21, "rotation": 107.76, "x": -5.96, "y": 9.16, "color": "abe323ff"}, {"name": "bone27", "parent": "bone9", "length": 13.32, "rotation": -117.17, "x": 7, "y": -25.46, "color": "abe323ff"}, {"name": "bone28", "parent": "bone9", "length": 14.54, "rotation": -130.46, "x": -4.69, "y": -9.65, "color": "abe323ff"}, {"name": "bone29", "parent": "bone5", "x": 58.27, "y": 62.53, "color": "559effff"}, {"name": "bone30", "parent": "bone5", "x": 10.68, "y": -43.52, "color": "559effff"}, {"name": "zm1", "parent": "bone3", "x": 97.84, "y": -37.07, "color": "ff0000ff"}, {"name": "zm2", "parent": "bone3", "x": -154.46, "y": 35.92, "color": "ff0000ff"}, {"name": "jio1", "parent": "bone2", "x": -513.34, "y": -197.81, "color": "35ff00ff"}, {"name": "target1", "parent": "jio1", "rotation": -0.47, "x": -12.63, "y": 115.82, "color": "ff3f00ff"}, {"name": "target2", "parent": "jio1", "rotation": -0.47, "x": -6.26, "y": 58.94, "color": "ff3f00ff"}, {"name": "weiba", "parent": "bone10", "length": 72.42, "rotation": -52.12, "x": 38.65, "y": -51.2, "color": "f939f2ff"}, {"name": "bone34", "parent": "weiba", "length": 72.74, "rotation": 27.99, "x": 72.42, "color": "f939f2ff"}, {"name": "bone35", "parent": "bone34", "length": 67.08, "rotation": 63.52, "x": 72.74, "color": "f939f2ff"}, {"name": "bone36", "parent": "bone35", "length": 71.41, "rotation": 52.27, "x": 67.08, "color": "f939f2ff"}, {"name": "bone37", "parent": "bone36", "length": 89.83, "rotation": 34.17, "x": 71.41, "color": "f939f2ff"}, {"name": "bone31", "parent": "bone11", "length": 73.35, "rotation": 71.44, "x": -15.14, "y": 3.53, "color": "90009dff"}, {"name": "bone32", "parent": "bone31", "length": 36.15, "rotation": -113.65, "x": 71.98, "y": -3.4, "color": "90009dff"}, {"name": "bone33", "parent": "bone32", "length": 25.27, "rotation": 127.05, "x": 36.15, "color": "90009dff"}, {"name": "bone38", "parent": "bone33", "length": 29.14, "rotation": 42.27, "x": 25.27, "color": "90009dff"}, {"name": "bone39", "parent": "bone2", "x": -382.9, "y": -139.72, "color": "35ff00ff"}, {"name": "target3", "parent": "bone39", "rotation": -0.47, "x": -20.61, "y": 57.46, "color": "ff3f00ff"}, {"name": "target4", "parent": "bone39", "rotation": -0.47, "x": -15.06, "y": 32.78, "color": "ff3f00ff"}, {"name": "bone40", "parent": "bone30", "length": 94.34, "rotation": 144.13, "x": -16.41, "y": 15.23, "color": "559effff"}, {"name": "bone41", "parent": "bone40", "length": 94.22, "rotation": 27.46, "x": 94.34, "color": "559effff"}, {"name": "bone42", "parent": "bone41", "length": 33.09, "rotation": -22.33, "x": 97.48, "y": 0.88, "transform": "noRotationOrReflection", "color": "559effff"}, {"name": "bone43", "parent": "bone42", "length": 32.73, "rotation": 10.42, "x": 33.09, "color": "559effff"}, {"name": "bone44", "parent": "bone41", "length": 29.02, "rotation": -124.76, "x": 103.66, "y": -12.83, "transform": "noRotationOrReflection", "color": "559effff"}, {"name": "bone151", "parent": "bone2", "length": 93.7, "rotation": 1.9, "x": -268.22, "y": -119.97}, {"name": "target5", "parent": "bone151", "rotation": -2.37, "x": -4.15, "y": -0.66, "color": "ff3f00ff"}, {"name": "bone45", "parent": "bone5", "length": 76.47, "rotation": -73.8, "x": 38.66, "y": 11.93, "color": "559effff"}, {"name": "bone46", "parent": "bone45", "length": 84.5, "rotation": 3.89, "x": 76.47, "color": "559effff"}, {"name": "bone47", "parent": "bone46", "length": 66.88, "rotation": 26.42, "x": 84.5, "color": "559effff"}, {"name": "bone48", "parent": "bone47", "length": 78.15, "rotation": -128.62, "x": 27.36, "y": -38.45, "color": "559effff"}, {"name": "bone49", "parent": "bone48", "length": 70.4, "rotation": -16.11, "x": 78.15, "color": "559effff"}, {"name": "bone50", "parent": "bone46", "length": 93.39, "rotation": -108.01, "x": 44.17, "y": -48.59, "color": "559effff"}, {"name": "bone51", "parent": "bone50", "length": 106, "rotation": -20.69, "x": 93.39, "color": "559effff"}, {"name": "bone52", "parent": "bone45", "length": 88.4, "rotation": -105.55, "x": 38.12, "y": -34.66, "color": "559effff"}, {"name": "bone53", "parent": "bone52", "length": 89.68, "rotation": -13.13, "x": 88.4, "color": "559effff"}, {"name": "bone54", "parent": "bone5", "length": 95.72, "rotation": 36.02, "x": 75.66, "y": 44.8, "color": "559effff"}, {"name": "bone55", "parent": "bone54", "length": 85.36, "rotation": -39.77, "x": 95.72, "color": "559effff"}, {"name": "bone56", "parent": "bone55", "length": 91.47, "rotation": -45.45, "x": 85.36, "color": "559effff"}, {"name": "bone57", "parent": "bone56", "length": 91.98, "rotation": 129.61, "x": 35.18, "y": 45.23, "color": "559effff"}, {"name": "bone58", "parent": "bone57", "length": 102.29, "rotation": 21.1, "x": 91.98, "color": "559effff"}, {"name": "bone59", "parent": "bone55", "length": 86.02, "rotation": 85.47, "x": 42.04, "y": 39.04, "color": "559effff"}, {"name": "bone60", "parent": "bone59", "length": 123.24, "rotation": 25.38, "x": 87.54, "y": -0.4, "color": "559effff"}, {"name": "bone61", "parent": "bone54", "length": 72.98, "rotation": 63.92, "x": 73.03, "y": 49.12, "color": "559effff"}, {"name": "bone62", "parent": "bone61", "length": 78.65, "rotation": -4.4, "x": 72.98, "color": "559effff"}, {"name": "bone63", "parent": "bone29", "length": 92.16, "rotation": 105.51, "x": 7.22, "y": 15.65, "color": "559effff"}, {"name": "bone64", "parent": "bone63", "length": 65.63, "rotation": 128.6, "x": 92.16, "color": "559effff"}, {"name": "bone65", "parent": "bone64", "length": 39.56, "rotation": 3.9, "x": 65.63, "color": "559effff"}, {"name": "bone66", "parent": "bone65", "length": 25.61, "rotation": -42.16, "x": 40.82, "y": -0.58, "color": "559effff"}, {"name": "bone67", "parent": "bone65", "length": 24.3, "rotation": -29.04, "x": 54.6, "y": 17.87, "color": "559effff"}, {"name": "bone68", "parent": "bone65", "length": 20.85, "rotation": -60.65, "x": 25.7, "y": -20.41, "color": "559effff"}, {"name": "bone69", "parent": "bone65", "length": 24.56, "rotation": -54.99, "x": 20.67, "y": -9.86, "color": "559effff"}, {"name": "bone70", "parent": "bone69", "length": 11.96, "rotation": 44.11, "x": 24.56, "color": "559effff"}, {"name": "yuanhuan", "parent": "bone2", "x": -371.35, "y": 193.23, "color": "7fff26ff"}, {"name": "bone71", "parent": "bone4", "x": 20.83, "y": -47.48, "color": "559effff"}, {"name": "bone72", "parent": "bone10", "x": -12.24, "y": 48.61, "color": "559effff"}, {"name": "bone73", "parent": "bone37", "length": 23.56, "rotation": -18.7, "x": 104.22, "y": -2.76, "color": "f939f2ff"}, {"name": "bone74", "parent": "bone73", "length": 26.12, "rotation": -37.43, "x": 23.56, "color": "f939f2ff"}, {"name": "bone75", "parent": "bone74", "length": 28.11, "rotation": 54.54, "x": 25.83, "y": -0.15, "color": "f939f2ff"}, {"name": "bone76", "parent": "bone75", "length": 30.3, "rotation": 7.22, "x": 28.11, "color": "f939f2ff"}, {"name": "bone77", "parent": "bone76", "length": 20.86, "rotation": -28.04, "x": 30.07, "y": -0.04, "color": "f939f2ff"}, {"name": "bone78", "parent": "bone77", "length": 20.68, "rotation": 55.67, "x": 20.86, "color": "f939f2ff"}, {"name": "bone79", "parent": "bone78", "length": 19.2, "rotation": 24.18, "x": 24.13, "y": 0.68, "color": "f939f2ff"}, {"name": "bone80", "parent": "bone79", "length": 19.43, "rotation": -0.58, "x": 18.55, "y": 0.17, "color": "f939f2ff"}, {"name": "bone81", "parent": "bone77", "length": 14.5, "rotation": 80.29, "x": 14.81, "y": 18.14, "color": "f939f2ff"}, {"name": "bone82", "parent": "bone74", "x": 11.5, "y": 40.43, "color": "f939f2ff"}, {"name": "bone83", "parent": "bone37", "length": 15.66, "rotation": -74.85, "x": 105.58, "y": -11, "color": "f939f2ff"}, {"name": "bone84", "parent": "bone83", "length": 24.13, "rotation": -55.6, "x": 15.66, "color": "f939f2ff"}, {"name": "bone85", "parent": "bone84", "length": 21.89, "rotation": 48.8, "x": 24.13, "color": "f939f2ff"}, {"name": "bone86", "parent": "bone85", "length": 41.04, "rotation": 87.34, "x": 21.55, "y": 0.26, "color": "f939f2ff"}, {"name": "bone87", "parent": "bone71", "length": 33.72, "rotation": 15, "x": 57.74, "y": 31.78, "color": "ff00aeff"}, {"name": "bone88", "parent": "bone87", "length": 42.12, "rotation": -32.58, "x": 33.72, "color": "ff00aeff"}, {"name": "bone89", "parent": "bone88", "length": 33.76, "rotation": 46.7, "x": 42.12, "color": "ff00aeff"}, {"name": "bone90", "parent": "bone89", "length": 28.62, "rotation": 5.08, "x": 32.54, "y": 0.16, "color": "ff00aeff"}, {"name": "bone91", "parent": "bone90", "length": 33.51, "rotation": -31.66, "x": 25.72, "y": -0.31, "color": "ff00aeff"}, {"name": "bone92", "parent": "bone89", "length": 27.9, "rotation": -27.95, "x": 12.35, "y": -22.02, "color": "ff00aeff"}, {"name": "bone93", "parent": "bone34", "length": 27.03, "rotation": -32.05, "x": 12.51, "y": -30.51, "color": "f939f2ff"}, {"name": "bone94", "parent": "bone93", "length": 27.54, "rotation": -12.99, "x": 27.78, "y": 0.25, "color": "f939f2ff"}, {"name": "bone95", "parent": "bone94", "length": 25.01, "rotation": 37.32, "x": 27.54, "color": "f939f2ff"}, {"name": "bone96", "parent": "bone34", "length": 38.88, "rotation": -17.7, "x": 47.49, "y": -19.62, "color": "f939f2ff"}, {"name": "bone97", "parent": "bone96", "length": 37.22, "rotation": 8, "x": 38.88, "color": "f939f2ff"}, {"name": "bone98", "parent": "bone97", "length": 29.8, "rotation": 21.62, "x": 37.22, "color": "f939f2ff"}, {"name": "bone99", "parent": "bone35", "length": 30.17, "rotation": -65.26, "x": 4.58, "y": -9.21, "color": "f939f2ff"}, {"name": "bone100", "parent": "bone99", "length": 34.03, "rotation": 23.03, "x": 30.17, "color": "f939f2ff"}, {"name": "bone101", "parent": "bone100", "length": 34.5, "rotation": 12.64, "x": 34.03, "color": "f939f2ff"}, {"name": "bone102", "parent": "bone35", "length": 29.57, "rotation": 23.62, "x": 41.26, "y": -28.55, "color": "f939f2ff"}, {"name": "bone103", "parent": "bone102", "length": 30.03, "rotation": -12.19, "x": 29.57, "color": "f939f2ff"}, {"name": "bone104", "parent": "bone103", "length": 25.81, "rotation": -3.14, "x": 30.03, "color": "f939f2ff"}, {"name": "bone105", "parent": "bone36", "length": 28.81, "rotation": -29.69, "x": 22.92, "y": -5.77, "color": "f939f2ff"}, {"name": "bone106", "parent": "bone105", "length": 25.11, "rotation": -12.91, "x": 28.81, "color": "f939f2ff"}, {"name": "bone107", "parent": "bone7", "length": 28.3, "rotation": 96.14, "x": -13.15, "y": 16.96, "color": "abe323ff"}, {"name": "bone108", "parent": "bone107", "length": 29.27, "rotation": -51.11, "x": 28.15, "y": -0.21, "color": "abe323ff"}, {"name": "bone109", "parent": "bone108", "length": 23.49, "rotation": -2.98, "x": 29.27, "color": "abe323ff"}, {"name": "bone110", "parent": "bone7", "length": 18.25, "rotation": 151.07, "x": -29.77, "y": 22.93, "color": "abe323ff"}, {"name": "bone111", "parent": "bone110", "length": 17.04, "rotation": -22.28, "x": 18.25, "color": "abe323ff"}, {"name": "bone112", "parent": "bone7", "length": 22.9, "rotation": 52.84, "x": -31.33, "y": 58.73, "color": "abe323ff"}, {"name": "bone113", "parent": "bone112", "length": 19.07, "rotation": -28.29, "x": 22.9, "color": "abe323ff"}, {"name": "bone114", "parent": "bone7", "length": 15.51, "rotation": 74.93, "x": 5.11, "y": 13.27, "color": "abe323ff"}, {"name": "bone115", "parent": "bone114", "length": 17.98, "rotation": -15.85, "x": 15.46, "y": -0.21, "color": "abe323ff"}, {"name": "bone116", "parent": "bone7", "length": 13.32, "rotation": 16.88, "x": 14.16, "y": 18.12, "color": "abe323ff"}, {"name": "bone117", "parent": "bone116", "length": 19.79, "rotation": -69.86, "x": 13.32, "color": "abe323ff"}, {"name": "bone118", "parent": "bone7", "length": 24.13, "rotation": -129.64, "x": -12.28, "y": -22.52, "color": "abe323ff"}, {"name": "bone119", "parent": "bone118", "length": 27.29, "rotation": -30.23, "x": 24.13, "color": "abe323ff"}, {"name": "bone120", "parent": "bone119", "length": 25.82, "rotation": 19.32, "x": 27.44, "y": -0.16, "color": "abe323ff"}, {"name": "bone121", "parent": "bone7", "length": 12.66, "rotation": -88.01, "x": -20.7, "y": -47.56, "color": "abe323ff"}, {"name": "bone122", "parent": "bone121", "length": 16.08, "rotation": -23.86, "x": 12.66, "color": "abe323ff"}, {"name": "bone123", "parent": "bone8", "length": 17.27, "rotation": -102.68, "x": -0.87, "y": -19.34, "color": "abe323ff"}, {"name": "bone124", "parent": "bone123", "length": 20.36, "rotation": -27.24, "x": 17.27, "color": "abe323ff"}, {"name": "bone125", "parent": "bone124", "length": 19.41, "rotation": 0.74, "x": 20.36, "color": "abe323ff"}, {"name": "bone126", "parent": "bone8", "length": 20.89, "rotation": -13.09, "x": 7.74, "y": -2.43, "color": "abe323ff"}, {"name": "bone127", "parent": "bone126", "length": 19.73, "rotation": -25.15, "x": 20.89, "color": "abe323ff"}, {"name": "bone128", "parent": "bone127", "length": 26.14, "rotation": 61.66, "x": 19.73, "color": "abe323ff"}, {"name": "bone129", "parent": "bone8", "length": 15.38, "rotation": -5.07, "x": 8.76, "y": 10.34, "color": "abe323ff"}, {"name": "bone130", "parent": "bone8", "length": 16.5, "rotation": -60.07, "x": 6.67, "y": -24.22, "color": "abe323ff"}, {"name": "bone131", "parent": "bone130", "length": 15.52, "rotation": 23.58, "x": 16.5, "color": "abe323ff"}, {"name": "bone132", "parent": "bone64", "length": 29.27, "rotation": -141.62, "x": 67.04, "y": -10.62, "color": "559effff"}, {"name": "bone133", "parent": "bone132", "length": 24.61, "rotation": -16.98, "x": 29.17, "y": -0.21, "color": "559effff"}, {"name": "bone134", "parent": "bone64", "length": 16.54, "rotation": -153.53, "x": 58.88, "y": -8.94, "color": "559effff"}, {"name": "bone135", "parent": "bone134", "length": 16.79, "rotation": -16.79, "x": 16.93, "y": -0.33, "color": "559effff"}, {"name": "bone136", "parent": "bone64", "length": 19.44, "rotation": 154.05, "x": 53.98, "y": -3.93, "color": "559effff"}, {"name": "bone137", "parent": "bone136", "length": 17.51, "rotation": 4.45, "x": 19.44, "color": "559effff"}, {"name": "bone138", "parent": "bone64", "length": 12.12, "rotation": 155.52, "x": 61.24, "y": 17.97, "color": "559effff"}, {"name": "bone139", "parent": "bone138", "length": 11.17, "rotation": -43.23, "x": 12.12, "color": "559effff"}, {"name": "bone140", "parent": "bone64", "length": 11.91, "rotation": 121.95, "x": 37.17, "y": 20.26, "color": "559effff"}, {"name": "bone141", "parent": "bone41", "length": 13.78, "rotation": -179.38, "x": 89.06, "y": 13.15, "color": "559effff"}, {"name": "bone142", "parent": "bone141", "length": 14.52, "rotation": -17.96, "x": 13.56, "y": -0.13, "color": "559effff"}, {"name": "bone143", "parent": "bone41", "length": 25.49, "rotation": 176.35, "x": 85.71, "y": 3.52, "color": "559effff"}, {"name": "bone144", "parent": "bone143", "length": 23.72, "rotation": -16.85, "x": 25.46, "y": -0.18, "color": "559effff"}, {"name": "bone145", "parent": "bone144", "length": 23.39, "rotation": -14.35, "x": 24.13, "y": -0.31, "color": "559effff"}, {"name": "bone146", "parent": "bone41", "length": 22.92, "rotation": -154.46, "x": 84.72, "y": -10.37, "color": "559effff"}, {"name": "bone147", "parent": "bone146", "length": 21.58, "rotation": 47.56, "x": 22.92, "color": "559effff"}, {"name": "bone148", "parent": "bone147", "length": 20.64, "rotation": -58.47, "x": 21.58, "color": "559effff"}, {"name": "bone149", "parent": "bone41", "length": 10.6, "rotation": -130.49, "x": 88.05, "y": -22.47, "color": "559effff"}, {"name": "bone150", "parent": "bone149", "length": 11.22, "rotation": -10.35, "x": 10.52, "y": 0.16, "color": "559effff"}, {"name": "bone152", "parent": "bone7", "x": 1.7, "y": -13.71, "color": "abe323ff"}, {"name": "bone153", "parent": "bone7", "x": 6.82, "y": -21.93, "color": "abe323ff"}, {"name": "bone154", "parent": "bone7", "x": 8.04, "y": -32.62, "color": "abe323ff"}, {"name": "bone155", "parent": "bone7", "x": 7.95, "y": 6.12, "color": "abe323ff"}, {"name": "bone156", "parent": "bone7", "x": 14.48, "y": 7.49, "color": "abe323ff"}, {"name": "bone157", "parent": "bone7", "x": 20.83, "y": 8.34, "color": "abe323ff"}, {"name": "bone158", "parent": "bone7", "length": 8.09, "rotation": -77.24, "x": 6.08, "y": -8.6, "color": "abe323ff"}, {"name": "bone159", "parent": "bone158", "length": 12.37, "rotation": -31.98, "x": 8.09, "color": "abe323ff"}, {"name": "bone160", "parent": "bone159", "length": 10.8, "rotation": 21.56, "x": 12.37, "color": "abe323ff"}, {"name": "bone161", "parent": "bone7", "length": 7.42, "rotation": 45.45, "x": 6.29, "y": -0.86, "color": "abe323ff"}, {"name": "bone162", "parent": "bone161", "length": 7.83, "rotation": 1.58, "x": 7.42, "color": "abe323ff"}, {"name": "bone163", "parent": "bone71", "length": 18.04, "rotation": 59.34, "x": -8.94, "y": 3.08, "color": "559effff"}, {"name": "bone164", "parent": "bone163", "length": 19.37, "rotation": 7.46, "x": 18.04, "color": "559effff"}, {"name": "bone165", "parent": "bone164", "length": 14.13, "rotation": -6.05, "x": 19.37, "color": "559effff"}, {"name": "bone166", "parent": "bone71", "length": 15.76, "rotation": -21.65, "x": -16.04, "y": -1.87, "color": "559effff"}, {"name": "bone167", "parent": "bone166", "length": 15.31, "rotation": 4.44, "x": 15.76, "color": "559effff"}, {"name": "bone168", "parent": "bone167", "length": 13.06, "rotation": -23.54, "x": 15.31, "color": "559effff"}, {"name": "bone169", "parent": "bone72", "length": 10.12, "rotation": -97.54, "x": 2.73, "y": 0.84, "color": "559effff"}, {"name": "bone170", "parent": "bone169", "length": 10.45, "rotation": 5.83, "x": 10.12, "color": "559effff"}, {"name": "bone171", "parent": "bone170", "length": 10.24, "rotation": -8.14, "x": 10.45, "color": "559effff"}, {"name": "bone172", "parent": "bone72", "length": 11.86, "rotation": 170.92, "x": -7.65, "y": -3.67, "color": "559effff"}, {"name": "bone173", "parent": "bone172", "length": 11.65, "rotation": 4.03, "x": 11.86, "color": "559effff"}, {"name": "bone174", "parent": "bone71", "length": 24.95, "rotation": 2.05, "x": 11.94, "y": 5.59, "color": "559effff"}, {"name": "bone175", "parent": "bone174", "length": 25, "rotation": 18.37, "x": 24.9, "y": 0.14, "color": "559effff"}, {"name": "bone176", "parent": "bone175", "length": 33.8, "rotation": -2.45, "x": 24.85, "color": "559effff"}, {"name": "bone177", "parent": "bone176", "length": 21.24, "rotation": 1.52, "x": 34, "y": 0.37, "color": "559effff"}, {"name": "bone178", "parent": "bone58", "length": 80.28, "rotation": 21.36, "x": 108.02, "y": 0.51, "color": "559effff"}, {"name": "bone179", "parent": "bone", "x": -187.06, "y": 170.93}, {"name": "bone180", "parent": "bone179", "x": -10.43, "y": -9.65}, {"name": "bone181", "parent": "bone179", "x": -21.2, "y": -45.05}, {"name": "bone182", "parent": "bone179", "x": -21.93, "y": -74.79}, {"name": "bone183", "parent": "bone", "x": -144.34, "y": -140.77}, {"name": "bone184", "parent": "bone", "x": -154.61, "y": -49}, {"name": "bone185", "parent": "bone183", "x": -32.67, "y": 33.01}, {"name": "bone186", "parent": "bone183", "x": -33.61, "y": -6.34}, {"name": "bone187", "parent": "bone183", "x": -56.59, "y": -31.83}, {"name": "bone188", "parent": "bone183", "x": -78.94, "y": -79.68}, {"name": "bone189", "parent": "bone183", "x": -96.25, "y": -139.32}, {"name": "bone190", "parent": "bone184", "x": -27.81, "y": -26.36}, {"name": "bone191", "parent": "bone184", "x": -25.56, "y": 1.56}, {"name": "bone192", "parent": "bone184", "x": -40.95, "y": 22.5}, {"name": "bone193", "parent": "bone184", "x": -60.87, "y": 31.53}, {"name": "bone194", "parent": "bone", "x": -131.94, "y": 35.48, "color": "aaffa0ff"}, {"name": "bone195", "parent": "bone194", "x": -110.25, "y": -220.4, "color": "aaffa0ff"}, {"name": "bone196", "parent": "bone194", "x": -88.07, "y": 179.62, "color": "aaffa0ff"}, {"name": "bone197", "parent": "bone194", "x": -89.05, "y": 10.92, "color": "aaffa0ff"}, {"name": "bone198", "parent": "bone194", "x": -88.5, "y": 78.63, "color": "aaffa0ff"}, {"name": "bone199", "parent": "bone194", "x": -78.5, "y": -92.32, "color": "aaffa0ff"}, {"name": "bone200", "parent": "bone194", "x": -88.58, "y": -161.89, "color": "aaffa0ff"}, {"name": "bone201", "parent": "bone194", "x": -83.49, "y": 222.02, "color": "aaffa0ff"}, {"name": "bone202", "parent": "bone", "x": -173.65, "y": 265}, {"name": "bone203", "parent": "bone202", "x": -59.32, "y": -72.76}, {"name": "bone204", "parent": "bone202", "x": -51.69, "y": -18.61}, {"name": "bone205", "parent": "bone202", "x": -48.42, "y": 23.18}, {"name": "bone206", "parent": "bone202", "x": -47.32, "y": 74.06}], "slots": [{"name": "qiongqi01", "bone": "yuanhuan", "attachment": "qiongqi01"}, {"name": "qiongqi02", "bone": "bone", "attachment": "qiongqi02"}, {"name": "qiongqi03", "bone": "bone", "attachment": "qiongqi03"}, {"name": "qiongqi04", "bone": "bone", "attachment": "qiongqi04"}, {"name": "qiongqi05", "bone": "bone", "attachment": "qiongqi05"}, {"name": "qiongqi067", "bone": "bone", "attachment": "qiongqi067", "blend": "additive"}, {"name": "qiongqi068", "bone": "bone", "attachment": "qiongqi068", "blend": "additive"}, {"name": "qiongqi06", "bone": "bone30", "attachment": "qiongqi06"}, {"name": "qiongqi07", "bone": "bone41", "attachment": "qiongqi07"}, {"name": "qiongqi08", "bone": "bone41", "attachment": "qiongqi08"}, {"name": "qiongqi09", "bone": "bone41", "attachment": "qiongqi09"}, {"name": "qiongqi010", "bone": "bone41", "attachment": "qiongqi010"}, {"name": "qiongqi011", "bone": "bone41", "attachment": "qiongqi011"}, {"name": "qiongqi012", "bone": "bone", "attachment": "qiongqi012"}, {"name": "qiongqi013", "bone": "bone", "attachment": "qiongqi013"}, {"name": "qiongqi014", "bone": "bone", "attachment": "qiongqi014"}, {"name": "qiongqi015", "bone": "bone", "attachment": "qiongqi015"}, {"name": "qiongqi016", "bone": "bone", "attachment": "qiongqi016"}, {"name": "qiongqi017", "bone": "bone", "attachment": "qiongqi017"}, {"name": "qiongqi018", "bone": "bone", "attachment": "qiongqi018"}, {"name": "qiongqi019", "bone": "bone", "attachment": "qiongqi019"}, {"name": "qiongqi020", "bone": "bone", "attachment": "qiongqi020"}, {"name": "qiongqi021", "bone": "bone29", "attachment": "qiongqi021"}, {"name": "qiongqi049", "bone": "bone", "attachment": "qiongqi049"}, {"name": "qiongqi022", "bone": "bone8", "attachment": "qiongqi022"}, {"name": "qiongqi023", "bone": "bone8", "attachment": "qiongqi023"}, {"name": "qiongqi024", "bone": "bone8", "attachment": "qiongqi024"}, {"name": "qiongqi025", "bone": "bone8", "attachment": "qiongqi025"}, {"name": "qiongqi047", "bone": "bone8", "attachment": "qiongqi047"}, {"name": "qiongqi026", "bone": "bone7", "attachment": "qiongqi026"}, {"name": "qiongqi027", "bone": "bone", "attachment": "qiongqi027"}, {"name": "qiongqi028", "bone": "bone7", "attachment": "qiongqi028"}, {"name": "qiongqi029", "bone": "bone7", "attachment": "qiongqi029"}, {"name": "qiongqi030", "bone": "bone7", "attachment": "qiongqi030"}, {"name": "qiongqi031", "bone": "bone7", "attachment": "qiongqi031"}, {"name": "qiongqi032", "bone": "bone7", "color": "ffffffe0", "attachment": "qiongqi032", "blend": "additive"}, {"name": "qiongqi033", "bone": "bone", "attachment": "qiongqi033"}, {"name": "qiongqi034", "bone": "bone", "attachment": "qiongqi034"}, {"name": "qiongqi035", "bone": "bone12", "attachment": "qiongqi035"}, {"name": "qiongqi036", "bone": "bone", "attachment": "qiongqi036"}, {"name": "qiongqi037", "bone": "bone"}, {"name": "qiongqi038", "bone": "bone64", "attachment": "qiongqi038"}, {"name": "qiongqi039", "bone": "bone140", "attachment": "qiongqi039"}, {"name": "qiongqi040", "bone": "bone64", "attachment": "qiongqi040"}, {"name": "qiongqi041", "bone": "bone64", "attachment": "qiongqi041"}, {"name": "qiongqi042", "bone": "bone", "attachment": "qiongqi042"}, {"name": "qiongqi043", "bone": "bone", "attachment": "qiongqi043"}, {"name": "qiongqi044", "bone": "bone", "attachment": "qiongqi044"}, {"name": "qiongqi046", "bone": "bone", "attachment": "qiongqi046"}, {"name": "qiongqi048", "bone": "bone7", "attachment": "qiongqi048"}, {"name": "qiongqi050", "bone": "bone", "attachment": "qiongqi050"}, {"name": "qiongqi051", "bone": "bone", "attachment": "qiongqi051"}, {"name": "qiongqi052", "bone": "bone", "attachment": "qiongqi052"}, {"name": "qiongqi053", "bone": "bone", "attachment": "qiongqi053"}, {"name": "qiongqi054", "bone": "bone72", "attachment": "qiongqi054"}, {"name": "qiongqi055", "bone": "bone72", "attachment": "qiongqi055"}, {"name": "qiongqi056", "bone": "bone71", "attachment": "qiongqi056"}, {"name": "qiongqi057", "bone": "bone71", "attachment": "qiongqi057"}, {"name": "qiongqi058", "bone": "bone71", "attachment": "qiongqi058"}, {"name": "qiongqi059", "bone": "bone", "attachment": "qiongqi059"}, {"name": "qiongqi060", "bone": "bone7", "attachment": "qiongqi060"}, {"name": "qiongqi062", "bone": "bone"}, {"name": "qiongqi063", "bone": "bone"}, {"name": "qiongqi064", "bone": "bone"}, {"name": "qiongqi065", "bone": "bone"}, {"name": "qiongqi066", "bone": "bone", "attachment": "qiongqi066"}, {"name": "qiongqi045", "bone": "bone", "attachment": "qiongqi045"}], "ik": [{"name": "target1", "order": 12, "bones": ["bone12", "bone13"], "target": "target1", "bendPositive": false}, {"name": "target2", "order": 15, "bones": ["bone13", "bone14"], "target": "target2"}, {"name": "target3", "order": 13, "bones": ["bone31", "bone32"], "target": "target3", "bendPositive": false}, {"name": "target4", "order": 14, "bones": ["bone32", "bone33"], "target": "target4"}, {"name": "target5", "order": 2, "bones": ["bone40", "bone41"], "target": "target5"}], "transform": [{"name": "bd1", "order": 9, "bones": ["bone11"], "target": "zm1", "rotation": -161.25, "x": -156.27, "y": 6.71, "rotateMix": -0.1, "translateMix": -0.1, "scaleMix": -0.1, "shearMix": -0.1}, {"name": "body", "bones": ["zm2"], "target": "zm1", "x": -239.77, "y": 70.22, "rotateMix": -0.798, "translateMix": -0.798, "scaleMix": -0.798, "shearMix": -0.798}, {"name": "cb1", "order": 4, "bones": ["bone45"], "target": "zm1", "rotation": 38.12, "x": -117.54, "y": 122.69, "rotateMix": -0.15, "translateMix": -0.15, "scaleMix": -0.15, "shearMix": -0.15}, {"name": "cb2", "order": 5, "bones": ["bone54"], "target": "zm1", "rotation": 147.94, "x": -161.85, "y": 144.75, "shearY": -360, "rotateMix": -0.15, "translateMix": -0.15, "scaleMix": -0.15, "shearMix": -0.15}, {"name": "<PERSON><PERSON><PERSON>", "order": 1, "bones": ["bone29", "bone30"], "target": "zm1", "rotation": 111.92, "x": -171.8, "y": 121.99, "shearY": -360, "rotateMix": -0.16, "translateMix": -0.16, "scaleMix": -0.16, "shearMix": -0.16}, {"name": "tou1", "order": 7, "bones": ["bone8"], "target": "bone7", "x": 11.44, "y": -9.18, "rotateMix": -0.573, "translateMix": -0.573, "scaleMix": -0.573, "shearMix": -0.573}, {"name": "ttt1", "order": 8, "bones": ["bone7"], "target": "zm1", "rotation": 116.97, "x": -143.19, "y": 181.52, "shearY": -360, "rotateMix": 0, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "tui1", "order": 11, "bones": ["bone12"], "target": "zm1", "rotation": -115.25, "x": -225.46, "y": 4.94, "rotateMix": -0.172, "translateMix": -0.172, "scaleMix": -0.172, "shearMix": -0.172}, {"name": "tui2", "order": 10, "bones": ["bone31"], "target": "zm1", "rotation": -90.26, "x": -140.8, "y": 8.24, "rotateMix": -0.196, "translateMix": -0.196, "scaleMix": -0.196, "shearMix": -0.196}, {"name": "weiba", "order": 3, "bones": ["weiba"], "target": "zm1", "rotation": 164.64, "x": -179.77, "y": 53.07, "shearY": -360, "rotateMix": -0.163, "translateMix": -0.163, "scaleMix": -0.163, "shearMix": -0.163}, {"name": "yuanhuan", "order": 6, "bones": ["yuanhuan"], "target": "zm1", "rotation": -0.68, "x": -141.8, "y": 226.74, "rotateMix": -0.326, "translateMix": -0.326, "scaleMix": -0.326, "shearMix": -0.326}], "skins": [{"name": "default", "attachments": {"qiongqi060": {"qiongqi060": {"type": "mesh", "uvs": [0, 0.27236, 0.1721, 0.2534, 0.34105, 0.13013, 0.5261, 0, 0.69706, 0, 0.864, 0.08904, 0.87607, 0.35138, 0.82378, 0.62636, 1, 0.85709, 1, 1, 0.82579, 1, 0.58241, 0.92979, 0.33099, 0.79388, 0.11578, 0.61056, 0.01521, 0.42408, 0.2216, 0.46145, 0.49686, 0.57199, 0.68954, 0.62967, 0.28496, 0.322, 0.53244, 0.30527, 0.70009, 0.30946, 0.89966, 0.8698, 0.68944, 0.81125, 0.41003, 0.67744], "triangles": [19, 3, 4, 20, 4, 5, 19, 4, 20, 20, 5, 6, 7, 20, 6, 17, 19, 20, 17, 20, 7, 2, 3, 19, 18, 2, 19, 21, 7, 8, 22, 17, 7, 21, 22, 7, 11, 23, 22, 12, 23, 11, 10, 22, 21, 11, 22, 10, 21, 8, 9, 10, 21, 9, 16, 18, 19, 16, 19, 17, 23, 15, 16, 12, 13, 23, 22, 16, 17, 23, 16, 22, 18, 1, 2, 15, 14, 0, 15, 1, 18, 15, 0, 1, 15, 18, 16, 13, 14, 15, 13, 15, 23], "vertices": [1, 129, -9.65, 1.81, 1, 2, 129, 4.98, 5.87, 0.96311, 132, -17.11, 10.36, 0.03689, 2, 129, 18.17, 15.6, 0.16441, 132, -0.79, 8.88, 0.83559, 2, 132, 16.95, 7.01, 0.2087, 133, 1.09, 8.15, 0.7913, 1, 133, 16.05, 6.51, 1, 3, 130, 19.8, 42.84, 0.03321, 131, 7.01, 43.11, 0.00483, 133, 30.11, -0.05, 0.96197, 3, 130, 30.37, 32.58, 0.20326, 131, 13.6, 29.94, 0.07624, 133, 29.57, -14.77, 0.7205, 3, 130, 37.19, 18.03, 0.23676, 131, 15.22, 13.95, 0.57703, 133, 23.31, -29.57, 0.18621, 3, 130, 57.36, 18.72, 6e-05, 131, 34.48, 7.92, 0.99987, 133, 37.32, -44.11, 6e-05, 1, 131, 37.54, 0.52, 1, 1, 131, 23.37, -5.33, 1, 1, 131, 2.07, -9.87, 1, 3, 129, 24.72, -21, 0.26959, 130, 11.08, -17.84, 0.55812, 131, -21.28, -11.27, 0.17229, 3, 129, 4.12, -14.72, 0.95603, 130, -9.88, -22.79, 0.04096, 131, -42.71, -9.01, 0.00301, 1, 129, -6.64, -6.25, 1, 3, 129, 11.58, -4.68, 0.96932, 130, -8.5, -10.36, 0.02877, 131, -37.29, 2.26, 0.00191, 3, 130, 13.7, 1.15, 0.98722, 132, -1.34, -19.41, 0.00582, 133, -4.95, -23.41, 0.00697, 4, 130, 28.5, 10.03, 0.55963, 131, 4.37, 9.27, 0.30586, 132, 11.7, -30.71, 0.00187, 133, 11.55, -28.47, 0.13264, 2, 129, 15.48, 4.09, 0.83345, 132, -10.45, 2.06, 0.16655, 3, 130, 6.09, 14.38, 0.30453, 132, 8.86, -8.06, 0.42185, 133, -0.21, -8.91, 0.27361, 4, 130, 17.25, 24.02, 0.24066, 131, -1.61, 26.2, 0.02169, 132, 21.5, -15.66, 0.01366, 133, 14.43, -10.75, 0.72399, 3, 130, 51.25, 12.31, 0.00303, 131, 26.59, 3.89, 0.99494, 133, 28.47, -43.85, 0.00203, 1, 131, 8.24, -0.14, 1, 3, 129, 30.24, -13.22, 0.0992, 130, 11.93, -8.35, 0.75204, 131, -17.34, -2.59, 0.14877], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28, 0, 30, 30, 32, 32, 34, 36, 38, 38, 40, 42, 44, 44, 46, 46, 26], "width": 88, "height": 56}}, "qiongqi020": {"qiongqi020": {"type": "mesh", "uvs": [0.48547, 0, 0.57905, 0.01778, 0.64849, 0.05946, 0.68471, 0.10249, 0.78433, 0.14014, 0.8915, 0.18854, 0.96698, 0.29745, 1, 0.40635, 1, 0.46955, 0.9232, 0.53005, 0.90811, 0.62417, 0.86132, 0.71425, 0.78584, 0.79089, 0.73, 0.85677, 0.6349, 0.93476, 0.53078, 0.9628, 0.38005, 0.97412, 0.20026, 0.98545, 0.06043, 0.94824, 0, 0.84633, 0, 0.72339, 0.06224, 0.61986, 0.10764, 0.50015, 0.13852, 0.36912, 0.13852, 0.25427, 0.26201, 0.16045, 0.36189, 0.14265, 0.40366, 0.03912, 0.54135, 0.04671, 0.59062, 0.12133, 0.67604, 0.23838, 0.76309, 0.38469, 0.79594, 0.55587, 0.7401, 0.7051, 0.65633, 0.81045, 0.60048, 0.87629, 0.4218, 0.24081, 0.47099, 0.42257, 0.50742, 0.57025, 0.49649, 0.68547, 0.4473, 0.80393, 0.38718, 0.88508, 0.28151, 0.25542, 0.27058, 0.39823, 0.26512, 0.54753, 0.22504, 0.67249, 0.19771, 0.76823, 0.15216, 0.87696], "triangles": [18, 47, 17, 16, 17, 41, 16, 41, 15, 41, 17, 47, 18, 19, 47, 47, 46, 41, 41, 46, 40, 47, 19, 46, 19, 20, 46, 46, 45, 40, 20, 21, 46, 46, 21, 45, 45, 21, 44, 41, 40, 15, 15, 35, 14, 15, 40, 35, 13, 35, 34, 13, 14, 35, 35, 40, 34, 13, 34, 12, 40, 39, 34, 34, 33, 12, 34, 39, 33, 40, 45, 39, 39, 38, 33, 45, 44, 39, 39, 44, 38, 29, 36, 26, 36, 29, 30, 29, 26, 28, 28, 27, 0, 27, 28, 26, 30, 3, 4, 30, 29, 3, 29, 28, 2, 29, 2, 3, 2, 28, 1, 28, 0, 1, 44, 21, 22, 44, 43, 37, 37, 43, 36, 44, 22, 43, 43, 42, 36, 22, 23, 43, 43, 23, 42, 23, 24, 42, 24, 25, 42, 42, 26, 36, 42, 25, 26, 8, 9, 7, 9, 31, 7, 31, 6, 7, 31, 5, 6, 5, 30, 4, 5, 31, 30, 37, 30, 31, 37, 36, 30, 12, 33, 11, 10, 11, 32, 11, 33, 32, 33, 38, 32, 10, 32, 9, 38, 44, 37, 38, 31, 32, 38, 37, 31, 32, 31, 9], "vertices": [5, 30, 104.02, 47.59, 0.00012, 29, 52.04, -68.77, 0.0004, 6, 58.65, -11.45, 0.80948, 31, -114.89, 193.17, 0.09, 32, 124.88, 122.95, 0.1, 5, 5, 98.63, -22.84, 9e-05, 30, 92.35, 31, 0.00974, 6, 45.56, -26.95, 0.80017, 31, -95.14, 188.54, 0.09, 32, 144.63, 118.31, 0.1, 5, 5, 83.74, -32.49, 0.02366, 30, 77.46, 21.34, 0.04549, 6, 29.88, -35.26, 0.74085, 31, -80.63, 178.32, 0.09, 32, 159.14, 108.1, 0.1, 5, 5, 71.31, -35.55, 0.10103, 30, 65.03, 18.29, 0.11551, 6, 17.23, -37.21, 0.59346, 31, -73.15, 167.93, 0.09, 32, 166.62, 97.71, 0.1, 5, 5, 54.79, -51.47, 0.20549, 30, 48.51, 2.37, 0.35547, 6, -0.63, -51.61, 0.24905, 31, -52.22, 158.55, 0.09, 32, 187.55, 88.32, 0.1, 5, 5, 35.29, -67.85, 0.09211, 30, 29, -14.02, 0.55595, 6, -21.5, -66.22, 0.06402, 31, -29.74, 146.57, 0.2, 32, 210.04, 76.35, 0.08791, 5, 4, 108.43, -47.67, 0.00524, 30, -1.11, -18.58, 0.70528, 6, -51.9, -68.11, 0.00157, 31, -14.26, 120.33, 0.2, 32, 225.51, 50.11, 0.08791, 4, 4, 86.97, -63.8, 0.11868, 30, -27.7, -14.87, 0.59341, 31, -7.78, 94.28, 0.2, 32, 231.99, 24.06, 0.08791, 4, 4, 73, -69.39, 0.19056, 30, -41.54, -8.97, 0.52153, 31, -8.09, 79.24, 0.2, 32, 231.69, 9.02, 0.08791, 6, 4, 53.59, -59.62, 0.37543, 5, -42.13, -42.19, 0.00325, 30, -48.41, 11.65, 0.33068, 10, -84.47, 22.23, 0.00273, 31, -24.65, 65.17, 0.2, 32, 215.12, -5.05, 0.08791, 5, 4, 31.6, -64.96, 0.52838, 30, -67.76, 23.37, 0.15142, 10, -68.18, 37.94, 0.03228, 31, -28.3, 42.84, 0.2, 32, 211.47, -27.38, 0.08791, 5, 4, 8.01, -63.72, 0.52046, 30, -83.6, 40.9, 0.06963, 10, -47.18, 48.75, 0.122, 31, -38.65, 21.61, 0.2, 32, 201.12, -48.62, 0.08791, 5, 4, -14.87, -55.64, 0.36238, 30, -94.11, 62.77, 0.02283, 10, -23.35, 53.31, 0.32688, 31, -55.02, 3.69, 0.2, 32, 184.75, -66.53, 0.08791, 6, 4, -33.82, -50.47, 0.18292, 30, -103.9, 79.8, 0.00571, 10, -4.37, 58.4, 0.52325, 11, -67.46, 39.47, 0.00021, 31, -67.17, -11.75, 0.2, 32, 172.6, -81.97, 0.08791, 6, 4, -58.54, -38.64, 0.04166, 30, -113.07, 105.62, 0.0001, 10, 22.94, 60.65, 0.73841, 11, -42.19, 50.06, 0.02984, 31, -87.7, -29.9, 0.09, 32, 152.07, -100.12, 0.1, 5, 4, -72.94, -20.62, 0.00265, 10, 44.45, 52.35, 0.66067, 11, -19.16, 48.82, 0.14668, 31, -109.9, -36.13, 0.09, 32, 129.87, -106.35, 0.1, 4, 10, 71.32, 34.84, 0.22795, 11, 11.8, 40.47, 0.58205, 31, -141.91, -38.18, 0.09, 32, 97.87, -108.4, 0.1, 3, 11, 48.56, 30.03, 0.81, 31, -180.07, -40.1, 0.09, 32, 59.7, -110.33, 0.1, 3, 11, 73.42, 11.61, 0.81, 31, -209.53, -30.65, 0.09, 32, 30.24, -100.88, 0.1, 5, 29, -92.95, 104.85, 0.01186, 10, 116.19, -38.67, 7e-05, 11, 77.21, -15.55, 0.70807, 31, -221.85, -6.15, 0.08, 32, 17.92, -76.37, 0.2, 5, 29, -66.03, 93.39, 0.06679, 10, 98.2, -61.75, 0.01358, 11, 67.24, -43.07, 0.63963, 31, -221.26, 23.11, 0.08, 32, 18.51, -47.11, 0.2, 6, 4, -34.05, 101.91, 0.00295, 29, -48.53, 71.59, 0.18588, 10, 72.65, -73.08, 0.06019, 11, 46.45, -61.74, 0.47098, 31, -207.57, 47.48, 0.08, 32, 32.2, -22.75, 0.2, 6, 4, -4.02, 103.56, 0.01201, 29, -26.09, 51.57, 0.40458, 10, 47.55, -89.64, 0.07977, 11, 27.7, -85.25, 0.22365, 31, -197.37, 75.77, 0.08, 32, 42.4, 5.55, 0.2, 6, 4, 27.37, 109.06, 0.00381, 29, 0.03, 33.33, 0.63848, 10, 23.22, -110.22, 0.02585, 11, 10.93, -112.35, 0.05186, 31, -190.2, 106.81, 0.08, 32, 49.57, 36.59, 0.2, 6, 29, 25.18, 22.62, 0.70898, 6, 39.95, 81.94, 0.00688, 10, 6.42, -131.78, 0.00077, 11, 1.62, -138.05, 0.00338, 31, -189.65, 134.14, 0.08, 32, 50.12, 63.92, 0.2, 4, 29, 35.47, -10.22, 0.614, 6, 47.3, 48.33, 0.196, 31, -163.03, 155.94, 0.09, 32, 76.74, 85.72, 0.1, 5, 5, 89.33, 31.17, 0.00027, 29, 31.07, -31.36, 0.28523, 6, 41.06, 27.66, 0.5245, 31, -141.77, 159.75, 0.09, 32, 98, 89.53, 0.1, 4, 29, 50.27, -49.16, 0.02496, 6, 58.61, 8.23, 0.78504, 31, -132.42, 184.21, 0.09, 32, 107.35, 113.98, 0.1, 4, 30, 89.15, 41.05, 0.00478, 6, 43.26, -16.66, 0.80522, 31, -103.27, 181.81, 0.09, 32, 136.5, 111.59, 0.1, 5, 5, 75, -15.44, 0.01342, 30, 68.72, 38.4, 0.02533, 6, 22.67, -17.5, 0.67334, 31, -93.19, 163.85, 0.2, 32, 146.58, 93.63, 0.08791, 5, 5, 42.28, -21.18, 0.42447, 30, 35.99, 32.65, 0.12621, 6, -10.43, -20.34, 0.1614, 31, -75.64, 135.63, 0.2, 32, 164.13, 65.41, 0.08791, 6, 4, 73.1, -15.25, 0.10391, 5, 3.01, -24.51, 0.34603, 30, -3.28, 29.32, 0.26171, 6, -49.84, -20.2, 0.00044, 31, -57.89, 100.45, 0.2, 32, 181.88, 30.22, 0.08791, 6, 4, 37.86, -36.85, 0.5537, 5, -37.21, -14.96, 0.00367, 30, -43.49, 38.88, 0.14609, 10, -59.41, 10.5, 0.00863, 31, -51.75, 59.57, 0.2, 32, 188.02, -10.65, 0.08791, 5, 4, 0.49, -39.05, 0.47711, 30, -71.53, 63.69, 0.03285, 10, -28.24, 31.24, 0.20212, 31, -64.3, 24.3, 0.2, 32, 175.47, -45.92, 0.08791, 6, 4, -29.39, -31.87, 0.14808, 30, -87.64, 89.85, 0.00369, 10, 1.17, 40.11, 0.55992, 11, -56.53, 23.79, 0.0004, 31, -82.56, -0.41, 0.2, 32, 157.21, -70.63, 0.08791, 6, 4, -48.33, -26.7, 0.03711, 30, -97.41, 106.88, 0.00015, 10, 20.14, 45.19, 0.65226, 11, -40.07, 34.49, 0.02256, 31, -94.72, -15.84, 0.2, 32, 145.06, -86.06, 0.08791, 7, 4, 78.02, 64.64, 0.00337, 5, 62.86, 28.64, 0.07525, 29, 4.6, -33.89, 0.26134, 6, 14.47, 27.47, 0.37201, 10, -42.92, -97.4, 0.00012, 31, -129.54, 136.14, 0.2, 32, 110.23, 65.91, 0.08791, 8, 4, 41.73, 38.9, 0.19507, 5, 18.98, 35.99, 0.26993, 29, -39.29, -26.53, 0.19457, 6, -28.6, 38.66, 0.00664, 10, -24.56, -56.87, 0.03875, 11, -51.01, -76.39, 0.00712, 31, -119.99, 92.68, 0.2, 32, 119.78, 22.46, 0.08791, 7, 4, 11.97, 18.67, 0.47092, 5, -16.39, 42.66, 0.02791, 29, -74.65, -19.87, 0.05134, 10, -9.06, -24.39, 0.15323, 11, -46.31, -40.71, 0.0087, 31, -112.97, 57.38, 0.2, 32, 126.8, -12.84, 0.08791, 7, 4, -14.35, 10.64, 0.01744, 5, -40.71, 55.54, 0.0005, 29, -98.97, -6.99, 0.00409, 10, 9.62, -4.18, 0.68825, 11, -34.8, -15.72, 0.0018, 31, -115.84, 30.01, 0.2, 32, 123.93, -40.21, 0.08791, 4, 10, 35.18, 11.65, 0.69186, 11, -15.39, 7.24, 0.02023, 31, -126.84, 2.03, 0.2, 32, 112.94, -68.19, 0.08791, 4, 10, 57.1, 19.05, 0.28926, 11, 3.17, 21.06, 0.52074, 31, -139.97, -17.02, 0.09, 32, 99.8, -87.24, 0.1, 5, 5, 71.32, 57.36, 0.00294, 29, 13.05, -5.17, 0.62838, 6, 25.42, 55.34, 0.07991, 31, -159.35, 133.26, 0.08878, 32, 80.42, 63.04, 0.2, 7, 4, 31.33, 80.49, 0.02851, 5, 40.96, 72.81, 0.0215, 29, -17.31, 10.28, 0.57868, 10, 5.39, -87.55, 0.04001, 11, -13.04, -96.3, 0.04251, 31, -162.35, 99.33, 0.08878, 32, 77.42, 29.1, 0.2, 7, 4, -2.09, 68.37, 0.05262, 5, 8.72, 87.8, 0.01363, 29, -49.55, 25.27, 0.30495, 10, 28.14, -60.23, 0.15773, 11, 0.15, -63.29, 0.18229, 31, -164.22, 63.82, 0.08878, 32, 75.55, -6.4, 0.2, 7, 4, -32.86, 65.22, 0.01076, 5, -15.31, 107.27, 0.00083, 29, -73.58, 44.75, 0.12711, 10, 53.12, -41.99, 0.13758, 11, 18.26, -38.22, 0.43494, 31, -173.32, 34.26, 0.08878, 32, 66.45, -35.96, 0.2, 6, 4, -56.17, 62.13, 0.00034, 29, -92.27, 59.01, 0.03498, 10, 71.7, -27.57, 0.02783, 11, 31.47, -18.76, 0.64807, 31, -179.57, 11.59, 0.08878, 32, 60.2, -58.63, 0.2, 3, 11, 49.36, 2.28, 0.71122, 31, -189.74, -14.08, 0.08878, 32, 50.03, -84.31, 0.2], "hull": 28, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 0, 54, 0, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 88, 76], "width": 212, "height": 238}}, "qiongqi021": {"qiongqi021": {"type": "mesh", "uvs": [0.33484, 0.41669, 0.31967, 0.23365, 0.40417, 0.09866, 0.58401, 0, 0.82667, 0, 0.94801, 0.1101, 1, 0.30687, 1, 0.5288, 0.90251, 0.66608, 0.78551, 0.70498, 0.66201, 0.84683, 0.46267, 0.95666, 0.27634, 1, 0.12467, 1, 0.02717, 0.91318, 0, 0.75531, 0.05967, 0.56999, 0.21134, 0.46016, 0.37446, 0.56465, 0.53645, 0.65306, 0.26344, 0.6646, 0.13785, 0.79722, 0.45455, 0.4205, 0.5947, 0.26289, 0.80037, 0.1591, 0.85315, 0.31286, 0.79127, 0.45125, 0.6493, 0.53774], "triangles": [12, 13, 21, 21, 15, 16, 13, 14, 21, 14, 15, 21, 11, 20, 19, 20, 17, 18, 19, 20, 18, 17, 0, 18, 11, 19, 10, 10, 19, 9, 19, 27, 9, 8, 9, 26, 9, 27, 26, 8, 26, 7, 18, 22, 19, 19, 22, 27, 18, 0, 22, 22, 23, 27, 27, 23, 26, 26, 25, 7, 25, 6, 7, 26, 23, 25, 23, 22, 1, 22, 0, 1, 23, 24, 25, 6, 25, 5, 25, 24, 5, 1, 2, 23, 2, 3, 23, 23, 3, 24, 24, 4, 5, 24, 3, 4, 16, 17, 20, 20, 11, 12, 12, 21, 20, 20, 21, 16], "vertices": [1, 73, 42.06, -17.22, 1, 1, 73, 29.36, -36.35, 1, 1, 73, 10.12, -42.59, 1, 1, 73, -16.13, -37.42, 1, 1, 73, -41.17, -17.44, 1, 1, 73, -45.11, 3.3, 1, 1, 73, -35.13, 26.81, 1, 1, 73, -17.83, 48.5, 1, 1, 73, 2.93, 53.88, 1, 1, 73, 18.04, 48.05, 1, 1, 73, 41.84, 51.74, 1, 2, 73, 70.97, 46.07, 0.81463, 74, 49.22, -12.17, 0.18537, 2, 73, 93.57, 34.96, 0.22762, 74, 26.44, -22.91, 0.77238, 1, 74, 6.92, -27.36, 1, 1, 74, -8.04, -19.63, 1, 2, 73, 103.01, -11.7, 0.08612, 74, -15.91, -1.18, 0.91388, 2, 73, 82.41, -24.89, 0.3893, 74, -13.37, 23.15, 0.6107, 1, 73, 58.2, -23.14, 1, 1, 73, 49.51, 0.5, 1, 1, 73, 39.69, 22.48, 1, 1, 73, 68.76, 1.13, 1, 2, 73, 92.05, 3.74, 0.4588, 74, 2.99, -2.25, 0.5412, 1, 73, 30.01, -6.99, 1, 1, 73, 3.26, -10.85, 1, 1, 73, -26.05, -4.06, 1, 1, 73, -19.51, 15.31, 1, 1, 73, -2.34, 23.73, 1, 1, 73, 19.05, 20.5, 1], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 0, 36, 36, 38, 36, 40, 40, 42, 36, 44, 44, 46, 46, 48, 50, 52, 52, 54], "width": 132, "height": 125}}, "qiongqi066": {"qiongqi066": {"type": "mesh", "uvs": [0.54004, 0.52966, 0.48161, 0.50932, 0, 0.34173, 0.29059, 0, 0.45349, 0, 1, 0.25061, 1, 0.32844, 0.81877, 0.50214, 0.77422, 0.54484, 0.82513, 0.64735, 0.55531, 0.76125, 0.45859, 0.87515, 0.30077, 0.90362, 0.22949, 0.96627, 0.06659, 1, 0, 1, 0, 0.89603, 0.16331, 0.86566, 0.15313, 0.78213, 0.37713, 0.72708, 0.30077, 0.61888, 0.66165, 0.5598, 0.45499, 0.74743, 0.23485, 0.88815, 0.60204, 0.26782, 0.67268, 0.52379, 0.60475, 0.64347, 0.34487, 0.82285, 0.15309, 0.94279], "triangles": [14, 28, 13, 14, 15, 28, 15, 16, 28, 13, 28, 12, 28, 23, 12, 28, 17, 23, 28, 16, 17, 12, 23, 27, 23, 17, 27, 11, 12, 27, 17, 18, 27, 11, 27, 10, 2, 3, 24, 24, 5, 6, 10, 27, 22, 27, 19, 22, 10, 26, 9, 10, 22, 26, 22, 19, 26, 19, 20, 26, 26, 21, 9, 21, 8, 9, 20, 0, 26, 26, 0, 21, 21, 25, 8, 21, 0, 25, 8, 25, 7, 0, 1, 25, 7, 25, 24, 25, 1, 24, 1, 2, 24, 7, 24, 6, 27, 18, 19, 3, 4, 24, 24, 4, 5], "vertices": [5, 5, -53.57, -6.92, 0.06568, 4, 20.6, -42.7, 0.60418, 10, -47.45, 24.26, 0.05014, 31, -65.62, 44.16, 0.1, 32, 196.68, -31.04, 0.18, 5, 5, -50.35, -5.5, 0.13656, 4, 21.87, -39.42, 0.56615, 10, -46.9, 20.78, 0.01728, 31, -68.15, 46.61, 0.1, 32, 194.15, -28.59, 0.18, 5, 5, -23.86, 6.25, 0.25945, 4, 32.36, -12.4, 0.45656, 10, -42.34, -7.84, 0.00399, 31, -88.94, 66.81, 0.1, 32, 173.36, -8.39, 0.18, 5, 5, 8.23, -21.32, 0.34121, 4, 74.55, -9.3, 0.33809, 10, -77.21, -31.79, 0.00071, 31, -75.34, 106.87, 0.15, 32, 186.96, 31.67, 0.17, 5, 5, 5.42, -27.91, 0.37702, 4, 77.21, -15.95, 0.30124, 10, -82.86, -27.39, 0.00174, 31, -68.17, 106.73, 0.15, 32, 194.13, 31.52, 0.17, 5, 5, -31.21, -38.45, 0.35761, 4, 58.69, -49.26, 0.35433, 10, -83.66, 10.72, 0.00806, 31, -44.73, 76.68, 0.1, 32, 217.57, 1.47, 0.18, 5, 5, -39.66, -34.85, 0.25447, 4, 50.16, -52.67, 0.44093, 10, -78.01, 17.96, 0.02459, 31, -44.91, 67.49, 0.1, 32, 217.39, -7.71, 0.18, 5, 5, -55.39, -19.48, 0.15704, 4, 28.17, -52.88, 0.51139, 10, -59.12, 29.23, 0.05157, 31, -53.3, 47.16, 0.1, 32, 209, -28.04, 0.18, 5, 5, -59.26, -15.7, 0.13309, 4, 22.76, -52.93, 0.52871, 10, -54.48, 32, 0.0582, 31, -55.36, 42.16, 0.1, 32, 206.94, -33.04, 0.18, 5, 5, -71.26, -13.02, 0.0612, 4, 12.36, -59.51, 0.50619, 10, -48.81, 42.92, 0.15261, 31, -53.36, 30.03, 0.1, 32, 208.94, -45.18, 0.18, 5, 5, -78.98, 3.17, 0.0167, 4, -4.53, -53.47, 0.435, 10, -31.19, 46.23, 0.2683, 31, -65.5, 16.83, 0.1, 32, 196.8, -58.38, 0.18, 5, 5, -89.67, 12.35, 0.00455, 4, -18.59, -54.51, 0.27812, 10, -19.57, 54.21, 0.43732, 31, -70.03, 3.48, 0.1, 32, 192.27, -71.73, 0.18, 5, 5, -90.04, 20.05, 0.00061, 4, -24.28, -49.32, 0.13511, 10, -12.03, 52.6, 0.58427, 31, -77.04, 0.26, 0.1, 32, 185.26, -74.95, 0.18, 5, 5, -95.61, 25.84, 4e-05, 4, -32.31, -49.15, 0.05972, 10, -5.01, 56.5, 0.66024, 31, -80.32, -7.07, 0.1, 32, 181.98, -82.27, 0.18, 3, 4, -38.67, -43.97, 0.03326, 10, 3.09, 55.24, 0.76674, 32, 174.73, -86.11, 0.2, 4, 4, -39.76, -41.25, 0.03999, 10, 5.4, 53.44, 0.68801, 31, -90.5, -10.85, 0.09, 32, 171.8, -86.05, 0.182, 4, 4, -28.37, -36.7, 0.08547, 10, -2.14, 43.76, 0.63453, 31, -90.25, 1.42, 0.1, 32, 172.05, -73.78, 0.18, 5, 5, -83.55, 23.86, 6e-05, 4, -22.37, -42.04, 0.16721, 10, -10.01, 45.35, 0.55273, 31, -83, 4.86, 0.1, 32, 179.3, -70.35, 0.18, 5, 5, -74.31, 20.41, 0.001, 4, -13.39, -37.96, 0.32946, 10, -15.71, 37.3, 0.38954, 31, -83.24, 14.72, 0.1, 32, 179.05, -60.48, 0.18, 5, 5, -72.19, 8.8, 0.00565, 4, -3.69, -44.7, 0.4829, 10, -27.48, 38.23, 0.23145, 31, -73.26, 21.02, 0.1, 32, 189.04, -54.19, 0.18, 5, 5, -59.13, 6.89, 0.02474, 4, 6.91, -36.84, 0.57621, 10, -32.68, 26.09, 0.11905, 31, -76.36, 33.85, 0.1, 32, 185.94, -41.35, 0.18, 4, 5, -58.94, -10.45, 0.14595, 4, 19.28, -48.99, 0.73024, 10, -49.49, 30.35, 0.03381, 31, -60.35, 40.5, 0.09, 4, 5, -75.75, 6.59, 0.00651, 4, -4.65, -48.77, 0.56813, 10, -28.71, 42.23, 0.27536, 31, -69.88, 18.55, 0.15, 4, 5, -87.23, 22.01, 5e-05, 4, -23.67, -45.94, 0.16031, 10, -10.86, 49.37, 0.74964, 31, -79.9, 2.14, 0.09, 4, 5, -26.21, -21.54, 0.38319, 4, 50.3, -33.76, 0.45951, 10, -68.59, 1.56, 0.0073, 31, -62.28, 75, 0.15, 4, 5, -55.22, -12.56, 0.16202, 4, 23.41, -47.86, 0.6482, 10, -52.48, 27.3, 0.03978, 31, -59.78, 44.74, 0.15, 4, 5, -67.04, -4.28, 0.06313, 4, 9.19, -50.33, 0.53889, 10, -41.45, 36.6, 0.13798, 31, -63.05, 30.68, 0.26, 4, 5, -82.03, 14.53, 0.00398, 4, -14.71, -47.58, 0.38427, 10, -19.42, 46.27, 0.46175, 31, -74.91, 9.75, 0.15, 4, 5, -91.75, 27.84, 3e-05, 4, -30.99, -45, 0.08641, 10, -4.06, 52.25, 0.82356, 31, -83.63, -4.23, 0.09], "hull": 21, "edges": [4, 6, 6, 8, 8, 10, 10, 12, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 42, 16, 0, 2, 2, 4, 38, 44, 44, 20, 34, 46, 46, 24, 48, 6, 8, 48, 12, 14, 14, 16, 48, 50, 50, 42, 0, 42, 42, 52, 52, 44, 44, 54, 54, 46, 46, 56], "width": 44, "height": 118}}, "qiongqi067": {"qiongqi067": {"type": "mesh", "uvs": [0.57865, 0, 0.77507, 0, 0.8521, 0.08687, 0.84054, 0.21671, 0.8001, 0.37089, 0.79433, 0.48585, 0.87713, 0.58052, 1, 0.63192, 1, 0.78475, 0.89254, 0.84696, 0.68456, 0.91323, 0.48622, 0.96463, 0.27248, 1, 0.09147, 1, 0, 0.94569, 0, 0.79557, 0, 0.61839, 0.03177, 0.44257, 0.11457, 0.31273, 0.11842, 0.23699, 0.259, 0.13015, 0.44964, 0.10175, 0.50163, 0.02736, 0.69281, 0.09802, 0.59911, 0.26254, 0.57861, 0.40033, 0.60203, 0.56897, 0.72209, 0.69648, 0.80993, 0.76846, 0.58154, 0.82399, 0.37657, 0.87952, 0.1511, 0.87335, 0.16867, 0.68003, 0.15695, 0.50111, 0.26822, 0.34069, 0.38828, 0.22964, 0.47612, 0.24815, 0.42342, 0.39005, 0.38535, 0.52579, 0.38828, 0.67797, 0.56982, 0.6862, 0.30043, 0.80137], "triangles": [16, 17, 33, 12, 30, 11, 11, 29, 10, 11, 30, 29, 10, 29, 28, 29, 39, 40, 29, 30, 41, 29, 40, 27, 10, 28, 9, 9, 28, 8, 29, 27, 28, 28, 7, 8, 28, 6, 7, 28, 27, 6, 13, 31, 12, 12, 31, 30, 13, 14, 31, 14, 15, 31, 31, 41, 30, 39, 29, 41, 41, 31, 32, 31, 15, 32, 41, 32, 39, 15, 16, 32, 40, 39, 26, 16, 33, 32, 32, 38, 39, 32, 33, 38, 25, 38, 37, 38, 26, 39, 40, 26, 27, 27, 26, 6, 26, 38, 25, 26, 5, 6, 26, 25, 5, 5, 25, 4, 33, 34, 38, 38, 34, 37, 17, 18, 33, 33, 18, 34, 37, 36, 25, 25, 36, 24, 34, 35, 37, 37, 35, 36, 18, 19, 34, 34, 19, 35, 35, 21, 36, 19, 20, 35, 35, 20, 21, 25, 24, 4, 4, 24, 3, 24, 36, 23, 22, 23, 21, 24, 23, 3, 23, 22, 0, 23, 36, 21, 3, 23, 2, 23, 1, 2, 23, 0, 1], "vertices": [2, 67, 52.99, -89.46, 0.96344, 68, -68.59, -69.43, 0.03656, 1, 67, 1.61, -77.14, 1, 1, 67, -10.78, -39.96, 1, 2, 67, 3.83, 7.67, 0.99255, 69, -20.36, -94.9, 0.00745, 4, 67, 28.18, 62.56, 0.47092, 68, -37, 81.34, 0.00233, 69, 5.24, -40.58, 0.51084, 70, -91.58, -1.03, 0.01592, 3, 69, 17.99, 1.59, 0.99385, 71, 11.43, -96.17, 0.00611, 72, -54, -100.61, 3e-05, 3, 69, 5.71, 42.33, 0.47752, 71, 12.5, -53.63, 0.51817, 72, -56.19, -58.11, 0.00431, 2, 69, -21.22, 69.81, 0.0774, 71, -4.5, -19.12, 0.9226, 3, 69, -6.27, 126.4, 3e-05, 71, 27.39, 29.97, 0.99944, 72, -47.75, 26.39, 0.00053, 3, 69, 27.77, 142.05, 0, 71, 64.61, 34.2, 0.72606, 72, -10.96, 33.46, 0.27394, 1, 72, 50.3, 28.96, 1, 2, 70, 119.64, 118.24, 0.0938, 72, 106.57, 20.69, 0.9062, 2, 70, 172.32, 91.49, 0.53406, 72, 163.53, 5.05, 0.46594, 2, 70, 209.52, 60.08, 0.75164, 72, 206.28, -18.27, 0.24836, 2, 70, 214.9, 28.31, 0.81742, 72, 217.92, -48.31, 0.18258, 3, 68, 232.52, 93.08, 0.00597, 70, 177.8, -15.62, 0.94878, 72, 190.39, -98.79, 0.04524, 3, 68, 194, 37.21, 0.13393, 70, 134.02, -67.46, 0.4276, 188, 93.45, 2.87, 0.43847, 3, 68, 148.74, -13.38, 0.35876, 70, 84.04, -113.4, 0.07151, 188, 32.87, -27.76, 0.56973, 2, 68, 102.18, -41.67, 0.78, 188, -20.8, -37.16, 0.22, 1, 68, 84.86, -64.97, 1, 2, 67, 148.23, -61.04, 0.04273, 68, 30.5, -77.19, 0.95727, 2, 67, 95.82, -59.66, 0.60481, 68, -17.9, -57.04, 0.39519, 2, 67, 75.58, -84.1, 0.905, 68, -45.58, -72.56, 0.095, 2, 67, 31.88, -45.8, 0.99807, 68, -72.56, -21.09, 0.00193, 4, 67, 71.08, 9.6, 0.94493, 68, -16.04, 16.48, 0.02009, 69, 46.92, -94.51, 0.02386, 70, -77.04, -67.61, 0.01112, 4, 67, 88.75, 59.64, 0.18199, 68, 18.45, 56.8, 0.24168, 69, 65.73, -44.9, 0.2919, 70, -38.78, -30.85, 0.28443, 4, 69, 76.13, 19.16, 0.89209, 70, -1.93, 22.56, 0.0382, 71, 72.15, -97.65, 0.02882, 72, 6.66, -97.43, 0.04089, 4, 69, 57.38, 74.62, 0.28889, 70, 4.91, 80.71, 0.02741, 71, 71.67, -39.11, 0.34347, 72, 1.69, -39.09, 0.34023, 4, 69, 41.57, 107.31, 0.00933, 70, 4.64, 117.02, 0.00027, 71, 66.87, -3.11, 0.95822, 72, -5.85, -3.57, 0.03218, 3, 69, 106.41, 112.18, 0.01751, 70, 65.3, 93.63, 0.05354, 72, 58.27, -14.32, 0.92895, 3, 69, 165.15, 118.66, 0.00027, 70, 121.15, 74.3, 0.37315, 72, 116.86, -22.05, 0.62658, 2, 70, 165.97, 33.37, 0.80453, 72, 168.97, -53.17, 0.19547, 2, 68, 170.05, 82.4, 0.05301, 70, 114.58, -20.15, 0.94699, 3, 68, 133.74, 24.2, 0.36099, 70, 72.78, -74.54, 0.21153, 188, 32.59, 12.69, 0.42747, 2, 68, 74.23, -9.4, 0.78, 188, -35.08, 3.08, 0.22, 2, 67, 123.29, -15.88, 0.03234, 68, 23.5, -26.08, 0.96766, 1, 68, 8.06, -6.83, 1, 4, 67, 128.43, 46.07, 0.0136, 68, 50.59, 29.86, 0.68267, 69, 105.08, -59.37, 0.00821, 70, -9.43, -60.79, 0.29552, 2, 68, 88.53, 66.85, 0.196, 70, 31.94, -27.68, 0.804, 3, 69, 142.39, 44.84, 0.01919, 70, 68.94, 17.36, 0.89861, 72, 77.13, -88.31, 0.0822, 4, 69, 95.97, 60.35, 0.28893, 70, 33.66, 51.28, 0.30829, 71, 103.87, -64.72, 0.04391, 72, 35.76, -62.16, 0.35887, 3, 69, 177.3, 84.49, 0.00066, 70, 117.49, 38.22, 0.73341, 72, 120.5, -58.14, 0.26593], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44, 2, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80], "width": 269, "height": 383}}, "qiongqi068": {"qiongqi068": {"type": "mesh", "uvs": [0.58176, 0, 0.36293, 0.04919, 0.20281, 0.15826, 0.06671, 0.30503, 0, 0.47873, 0, 0.6834, 0, 0.83555, 0.07739, 0.93789, 0.2722, 0.99579, 0.47235, 1, 0.74455, 0.99309, 0.8086, 0.87595, 0.88866, 0.71841, 0.89934, 0.56356, 0.97406, 0.46122, 1, 0.29964, 1, 0.18788, 0.81928, 0.07882, 0.68393, 0.13409, 0.81616, 0.28607, 0.79412, 0.44546, 0.68393, 0.58632, 0.64352, 0.74015, 0.59577, 0.87174, 0.36802, 0.87359, 0.1513, 0.83097, 0.166, 0.68826, 0.21007, 0.46956, 0.25783, 0.31017, 0.40843, 0.19155, 0.61046, 0.24344, 0.53332, 0.3843, 0.4672, 0.52516, 0.40843, 0.70308], "triangles": [8, 7, 24, 7, 25, 24, 7, 6, 25, 24, 25, 33, 6, 5, 25, 25, 26, 33, 25, 5, 26, 26, 5, 4, 8, 24, 9, 9, 23, 10, 9, 24, 23, 10, 23, 11, 23, 22, 11, 11, 22, 12, 23, 24, 33, 23, 33, 22, 12, 22, 21, 22, 33, 21, 12, 21, 13, 33, 32, 21, 33, 26, 32, 21, 32, 20, 26, 27, 32, 27, 26, 4, 32, 31, 20, 32, 27, 31, 4, 3, 27, 27, 28, 31, 27, 3, 28, 31, 28, 30, 30, 28, 29, 3, 2, 28, 28, 2, 29, 13, 21, 20, 13, 20, 14, 14, 20, 15, 20, 19, 15, 20, 31, 19, 31, 30, 19, 19, 16, 15, 16, 19, 18, 30, 18, 19, 16, 18, 17, 30, 29, 18, 2, 1, 29, 29, 1, 18, 1, 0, 18, 18, 0, 17], "vertices": [1, 58, -2.63, 14.28, 1, 2, 58, -7.44, -25.2, 0.8411, 60, -55.23, 43.96, 0.1589, 2, 58, 9.66, -66.45, 0.25921, 60, -34.04, 4.65, 0.74079, 2, 60, 0.07, -36.32, 0.92194, 62, -11.58, 49.16, 0.07806, 3, 60, 47.09, -70.63, 0.40113, 61, -18.37, -82.44, 0.00189, 62, 36.28, 16.03, 0.59698, 1, 63, 11.79, -8.61, 1, 1, 63, 60.99, -17.82, 1, 1, 63, 96.45, -11.38, 1, 2, 61, 154.75, -50.63, 0.28963, 63, 121.12, 16.9, 0.71037, 2, 61, 158.71, -17.61, 0.6958, 63, 128.6, 49.3, 0.3042, 2, 61, 159.96, 27.62, 0.97439, 63, 134.67, 94.14, 0.02561, 2, 59, 206.65, -46.16, 0.00145, 61, 122.37, 41.22, 0.99855, 2, 59, 159.95, -20.03, 0.07537, 61, 71.73, 58.5, 0.92463, 3, 59, 111.16, -5.26, 0.49115, 60, 135.8, 52.64, 0.00262, 61, 21.07, 64.23, 0.50623, 2, 59, 81.8, 15.36, 0.96964, 61, -11.53, 79.21, 0.03036, 2, 58, 117.63, 23.09, 0.02298, 59, 31.52, 33.14, 0.97702, 2, 58, 86.1, 42.01, 0.47035, 59, -4.02, 42.57, 0.52965, 2, 58, 39.9, 34.75, 0.99181, 59, -46.4, 22.77, 0.00819, 1, 58, 43.93, 6.12, 1, 1, 59, 19.38, 4.79, 1, 3, 59, 69.13, -12.19, 0.84871, 60, 93.21, 53.35, 0.09118, 61, -19.02, 49.84, 0.06012, 3, 59, 109.24, -41.75, 0.17429, 60, 127.38, 17.09, 0.00868, 61, 25.76, 28, 0.81703, 2, 59, 156.44, -61.2, 0.01186, 61, 75.69, 17.37, 0.98814, 2, 61, 118.24, 6.1, 0.97993, 63, 90.89, 77.2, 0.02007, 2, 61, 115.9, -31.64, 0.53956, 63, 84.53, 39.93, 0.46044, 2, 61, 99.12, -66.41, 0.04099, 63, 64.13, 7.15, 0.95901, 4, 60, 121.2, -74.99, 0.00139, 61, 52.5, -60.33, 0.15899, 62, 110.48, 13.52, 0.04431, 63, 18.43, 18.18, 0.79531, 4, 60, 59.18, -37.78, 0.61621, 61, -18.66, -47.44, 0.04681, 62, 47.55, 49.17, 0.33566, 63, -50.95, 38.61, 0.00132, 2, 60, 15.08, -8.32, 0.98511, 62, 2.73, 77.53, 0.01489, 3, 58, 36.61, -42.82, 0.45328, 59, -28.02, -52.66, 0.01443, 60, -9.62, 30.89, 0.5323, 3, 58, 68.5, -22.84, 0.5122, 59, -2.93, -24.62, 0.27019, 60, 20.09, 53.99, 0.21761, 3, 58, 101.65, -57.67, 0.01212, 59, 38.59, -48.88, 0.23523, 60, 56.59, 22.71, 0.75265, 4, 60, 93.88, -6.92, 0.28957, 61, 2.9, -6.31, 0.69063, 62, 81.47, 80.89, 0.01719, 63, -25.12, 77.2, 0.00261, 3, 61, 60.5, -20.59, 0.80032, 62, 131.13, 48.4, 0.0148, 63, 30.62, 56.84, 0.18488], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 0, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66], "width": 166, "height": 329}}, "qiongqi025": {"qiongqi025": {"type": "mesh", "uvs": [0, 0.92014, 0.15811, 1, 0.41069, 1, 0.67449, 1, 0.87656, 0.8885, 0.96356, 0.60114, 0.96075, 0.29268, 1, 0.01059, 0.60152, 0.1345, 0.35736, 0.26632, 0.09917, 0.42186, 0.00656, 0.66968, 0.69706, 0.22483, 0.46661, 0.50947, 0.18069, 0.82617], "triangles": [12, 8, 7, 6, 12, 7, 12, 13, 9, 12, 9, 8, 10, 9, 13, 14, 10, 13, 11, 10, 14, 12, 5, 13, 5, 12, 6, 4, 13, 5, 2, 14, 13, 0, 11, 14, 1, 0, 14, 13, 3, 2, 1, 14, 2, 4, 3, 13], "vertices": [-8.34, -11.25, -12.98, -14.33, -16.67, -21.23, -20.52, -28.45, -20.23, -35.71, -13.14, -42.55, -4.12, -47.28, 3.52, -52.74, 5.73, -39.91, 5.47, -31.19, 4.71, -21.71, -1.15, -15.32, 1.71, -41.12, -3.21, -30.39, -8.25, -17.65], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 0, 22, 14, 24, 24, 26, 26, 28], "width": 31, "height": 33}}, "qiongqi026": {"qiongqi026": {"type": "mesh", "uvs": [0.42039, 0, 0.58435, 0, 0.75286, 0, 0.92365, 0, 0.96464, 0.27889, 0.98742, 0.5308, 1, 0.78544, 0.90771, 0.9333, 0.75058, 1, 0.56613, 0.93604, 0.39761, 0.86211, 0.24276, 0.72246, 0.11068, 0.55544, 0, 0.28163, 0.11296, 0.13925, 0.28603, 0.05711, 0.19332, 0.30073, 0.40265, 0.41227, 0.6881, 0.561, 0.8451, 0.69543, 0.39694, 0.17439, 0.25277, 0.16614, 0.61148, 0.20122, 0.78998, 0.22598, 0.87065, 0.45918, 0.74364, 0.38695, 0.5188, 0.30028, 0.15151, 0.43854, 0.33687, 0.57268, 0.46731, 0.68205, 0.65268, 0.77905, 0.81573, 0.83889], "triangles": [24, 4, 5, 19, 18, 24, 19, 24, 5, 30, 18, 19, 19, 5, 6, 31, 30, 19, 31, 19, 6, 7, 31, 6, 8, 30, 31, 8, 31, 7, 9, 30, 8, 29, 17, 18, 30, 29, 18, 10, 11, 29, 9, 29, 30, 10, 29, 9, 28, 27, 17, 12, 27, 28, 11, 12, 28, 29, 11, 28, 21, 14, 15, 20, 15, 0, 21, 15, 20, 16, 14, 21, 13, 14, 16, 16, 21, 20, 27, 13, 16, 27, 16, 17, 12, 13, 27, 20, 0, 1, 22, 20, 1, 26, 20, 22, 26, 22, 25, 17, 20, 26, 17, 16, 20, 17, 26, 18, 28, 17, 29, 22, 1, 2, 23, 2, 3, 22, 2, 23, 23, 3, 4, 25, 22, 23, 24, 23, 4, 25, 23, 24, 18, 26, 25, 18, 25, 24], "vertices": [4, 118, 21.2, -43.88, 0.09615, 119, 29.62, -32.83, 0.542, 120, 2.06, -32.76, 0.36186, 124, 27.35, -50.13, 0, 4, 118, 7.51, -34.55, 0.37028, 119, 13.77, -37.62, 0.51832, 120, -13.52, -38.38, 0.1114, 124, 14.18, -60.18, 0, 4, 118, -6.55, -24.97, 0.77934, 119, -2.52, -42.56, 0.20577, 120, -29.53, -44.15, 0.01489, 124, 0.65, -70.5, 0, 3, 118, -20.81, -15.26, 0.95795, 119, -19.03, -47.55, 0.04203, 120, -45.76, -50, 1e-05, 2, 118, -11.04, 6.43, 0.83037, 121, -18.51, 9.91, 0.16963, 2, 118, -1.02, 25.22, 0.01481, 121, 2.61, 12.51, 0.98519, 2, 122, -0.03, 15.21, 0.59195, 121, 23.98, 14.08, 0.40805, 2, 122, 15.05, 11.5, 0.99514, 121, 36.53, 4.94, 0.00486, 2, 123, -22.92, 20.61, 0.08607, 122, 26.42, -0.9, 0.91393, 2, 123, -3.67, 18.23, 0.70415, 122, 28.79, -20.14, 0.29585, 3, 124, -14.74, 8.84, 0.00259, 123, 14.11, 14.77, 0.9855, 122, 29.75, -38.24, 0.01191, 2, 124, 4.81, 9, 0.93746, 123, 31.4, 5.65, 0.06254, 2, 120, 15.67, 21.74, 0.06622, 124, 23.93, 5.94, 0.93378, 2, 120, 33.98, 3.89, 0.97886, 124, 46.76, -5.57, 0.02114, 3, 119, 55.95, -12.63, 0.01127, 120, 27.3, -11.23, 0.98873, 124, 44.94, -22, 0, 4, 118, 35.12, -47.55, 0.01404, 119, 41.22, -24.3, 0.28442, 120, 13.2, -23.65, 0.70154, 124, 35.23, -38.09, 0, 2, 119, 44.25, -2, 0.00198, 120, 15.07, -1.22, 0.99802, 4, 118, 42.18, -16.27, 0.00042, 119, 21.3, 0.84, 0.98752, 124, 7.77, -21.51, 0.00807, 123, 19.55, -22.62, 0.004, 4, 118, 25.39, 10.29, 0.63547, 123, -10.89, -14.82, 0.13093, 122, -5.02, -21.16, 0.02655, 121, 5.57, -17.68, 0.20706, 4, 118, 18.64, 28.55, 0.00898, 123, -28.32, -6.16, 0.00836, 122, -0.85, -2.15, 0.13734, 121, 16.64, -1.66, 0.84532, 4, 118, 31.4, -33.1, 0.03696, 119, 27.64, -18.12, 0.55297, 120, -0.68, -18.18, 0.41007, 124, 20.35, -37.05, 0, 4, 118, 43.05, -41.88, 0.00194, 119, 41.78, -14.56, 0.1402, 120, 13.25, -13.89, 0.85786, 124, 32.35, -28.77, 0, 4, 118, 14.77, -19.04, 0.40456, 119, 6.25, -22.24, 0.55119, 120, -21.83, -23.41, 0.04425, 124, 1.75, -48.4, 0, 4, 118, 1.04, -7.17, 0.93225, 119, -11.61, -25.47, 0.06592, 120, -39.49, -27.56, 0.00183, 124, -13.84, -57.68, 0, 2, 118, 5.33, 13.6, 0.14547, 121, -3.24, 0.64, 0.85453, 3, 118, 12.52, 1.37, 0.97063, 123, -14.13, -30.14, 9e-05, 121, -9.12, -12.28, 0.02928, 4, 118, 27.19, -17.44, 0.06342, 119, 12.8, -11.56, 0.89507, 120, -15.84, -12.4, 0.04151, 124, 4.15, -36.11, 0, 2, 120, 15.12, 11.1, 0.46756, 124, 26.6, -4.37, 0.53244, 5, 118, 55.26, -8.87, 0.00284, 119, 23.76, 15.66, 0.19362, 120, -6.32, 15.35, 0.01973, 124, 4.88, -6.77, 0.66288, 123, 23.99, -8.27, 0.12093, 4, 118, 49.54, 6.14, 0.01046, 119, 8.48, 20.64, 0.01254, 124, -11.16, -7.45, 0.00139, 123, 9.54, -1.27, 0.97561, 4, 118, 38.66, 23.41, 0.0576, 123, -10.23, 3.83, 0.56037, 122, 13.23, -17.27, 0.3379, 121, 23.94, -21, 0.04414, 4, 118, 27.88, 36.84, 0.00027, 123, -27.29, 6.21, 0.00159, 122, 11.39, -0.15, 0.99772, 121, 28.73, -4.46, 0.00042], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30, 26, 32, 32, 34, 34, 36, 36, 38, 40, 42, 40, 44, 44, 46, 54, 56, 56, 58, 58, 60, 60, 62], "width": 101, "height": 84}}, "qiongqi027": {"qiongqi027": {"type": "mesh", "uvs": [0.25647, 0.1663, 0.32499, 0.11296, 0.49437, 0.05638, 0.6295, 0.00628, 0.78747, 0, 0.91689, 0, 0.90928, 0.10164, 0.87121, 0.14852, 0.96447, 0.19863, 1, 0.28915, 0.97969, 0.41523, 0.91499, 0.52515, 0.77795, 0.65931, 0.77034, 0.77893, 0.8084, 0.85005, 0.75702, 0.94381, 0.64663, 1, 0.48676, 1, 0.32689, 0.92118, 0.20699, 0.81449, 0.13086, 0.65931, 0.06615, 0.50252, 0.11183, 0.41523, 0.08229, 0.37581, 0.06003, 0.3461, 0.01857, 0.29077, 0.10421, 0.1954, 0.24602, 0.24259, 0.2625, 0.32658, 0.17735, 0.38607, 0.36963, 0.30208, 0.44517, 0.33825, 0.34766, 0.40357, 0.27349, 0.45489, 0.20345, 0.42923, 0.30067, 0.52741, 0.60564, 0.32078, 0.79143, 0.32078, 0.44193, 0.53481, 0.64243, 0.5395, 0.72337, 0.552, 0.71233, 0.66448, 0.67554, 0.78478, 0.60932, 0.88945, 0.49344, 0.90351, 0.38491, 0.89101, 0.25063, 0.79727, 0.16786, 0.64105, 0.24328, 0.56918, 0.16602, 0.48326, 0.24695, 0.48951, 0.33709, 0.47076, 0.44929, 0.42389, 0.613, 0.40983, 0.77671, 0.42702, 0.84477, 0.52544, 0.32405, 0.75303, 0.39689, 0.72716, 0.54125, 0.69454, 0.35045, 0.21557, 0.48691, 0.19608, 0.70372, 0.17116, 0.47416, 0.26107, 0.60807, 0.24157, 0.80957, 0.23399, 0.13682, 0.28706, 0.1636, 0.33255], "triangles": [19, 46, 18, 18, 46, 45, 16, 17, 43, 17, 44, 43, 16, 43, 15, 15, 43, 14, 43, 58, 42, 42, 14, 43, 42, 41, 13, 14, 42, 13, 43, 44, 58, 17, 45, 44, 45, 57, 44, 18, 45, 17, 46, 56, 45, 42, 58, 41, 41, 39, 40, 40, 39, 54, 13, 41, 12, 41, 40, 12, 2, 3, 61, 12, 40, 55, 12, 55, 11, 55, 54, 11, 54, 37, 10, 10, 37, 9, 9, 64, 8, 9, 37, 64, 63, 61, 64, 64, 7, 8, 64, 61, 7, 61, 4, 7, 61, 3, 4, 7, 4, 6, 6, 4, 5, 37, 63, 64, 11, 54, 10, 54, 53, 37, 55, 40, 54, 60, 2, 61, 19, 20, 46, 20, 47, 46, 45, 56, 57, 44, 57, 58, 48, 35, 57, 57, 56, 48, 57, 38, 58, 58, 38, 39, 57, 35, 38, 56, 47, 48, 22, 23, 29, 24, 65, 66, 24, 25, 65, 25, 26, 65, 65, 26, 27, 60, 1, 2, 66, 65, 27, 27, 26, 0, 59, 1, 60, 28, 66, 27, 28, 27, 30, 30, 59, 62, 62, 59, 60, 62, 60, 63, 27, 59, 30, 29, 66, 28, 21, 49, 47, 48, 49, 50, 49, 34, 50, 50, 34, 33, 51, 33, 32, 38, 52, 53, 51, 32, 52, 38, 51, 52, 35, 33, 51, 50, 33, 35, 47, 49, 48, 48, 50, 35, 35, 51, 38, 39, 53, 54, 36, 63, 37, 53, 36, 37, 63, 60, 61, 39, 38, 53, 21, 22, 49, 49, 22, 34, 52, 31, 53, 32, 30, 31, 31, 36, 53, 32, 31, 52, 32, 33, 28, 33, 34, 28, 22, 29, 34, 31, 30, 62, 36, 62, 63, 31, 62, 36, 32, 28, 30, 34, 29, 28, 27, 0, 59, 0, 1, 59, 23, 66, 29, 23, 24, 66, 20, 21, 47, 58, 39, 41, 46, 47, 56], "vertices": [2, 7, 14.95, -5.11, 0.69753, 8, 3.51, 4.08, 0.30247, 2, 7, 16.38, -10.69, 0.5682, 8, 4.94, -1.51, 0.4318, 2, 7, 15.07, -21.9, 0.30522, 8, 3.63, -12.72, 0.69478, 1, 8, 2.91, -21.83, 1, 1, 8, -1.31, -30.68, 1, 1, 8, -5.09, -37.76, 1, 1, 8, -11.41, -33.84, 1, 1, 8, -13.32, -30.15, 1, 1, 8, -19.27, -33.53, 1, 1, 8, -26.13, -32.35, 1, 1, 8, -33.66, -26.9, 1, 1, 8, -38.84, -19.58, 1, 1, 8, -43.48, -7.47, 1, 3, 7, -39.51, -12.12, 0.224, 8, -50.95, -2.94, 0.27755, 9, 1.39, -17.01, 0.49845, 2, 8, -56.65, -2.58, 0.40886, 9, -4.3, -16.64, 0.59114, 2, 8, -61.18, 3.46, 0.22901, 9, -8.84, -10.61, 0.77099, 1, 9, -9.23, -2.64, 1, 1, 9, -4.56, 6.1, 1, 1, 9, 5.19, 12.13, 1, 3, 7, -25.33, 19.9, 0.47492, 8, -36.78, 29.09, 0.17569, 9, 15.56, 15.02, 0.34939, 2, 7, -13.12, 18.73, 0.50611, 8, -24.56, 27.91, 0.49389, 2, 7, -1.13, 16.87, 0.73094, 8, -12.58, 26.05, 0.26906, 2, 7, 3.15, 11.37, 0.93303, 8, -8.29, 20.55, 0.06697, 2, 7, 6.55, 11.63, 0.82019, 8, -4.89, 20.81, 0.17981, 2, 7, 9.12, 11.82, 0.72088, 8, -2.33, 21, 0.27912, 2, 7, 13.89, 12.18, 0.51647, 8, 2.45, 21.37, 0.48353, 2, 7, 17.53, 4.22, 0.49633, 8, 6.08, 13.4, 0.50367, 2, 7, 10.34, -1.91, 0.92409, 8, -1.1, 7.27, 0.07591, 1, 7, 4.46, 0.08, 1, 1, 7, 3.11, 6.78, 1, 1, 7, 2.9, -6.62, 1, 1, 7, -1.63, -9.51, 1, 1, 7, -2.99, -1.93, 1, 1, 7, -4.13, 3.89, 1, 1, 7, -0.43, 6.84, 1, 1, 7, -9.59, 4.9, 1, 2, 7, -5.2, -18.88, 0.79056, 8, -16.64, -9.7, 0.20944, 3, 6, 42.32, -19.9, 0.00448, 7, -10.63, -29.04, 0.21077, 8, -22.07, -19.86, 0.78475, 1, 7, -14.2, -2.57, 1, 2, 7, -20.36, -13.37, 0.50719, 8, -31.8, -4.19, 0.49281, 3, 6, 29.42, -8.23, 0.01655, 7, -23.53, -17.36, 0.26665, 8, -34.97, -8.18, 0.7168, 3, 7, -30.45, -12.89, 0.04383, 8, -41.89, -3.71, 0.8475, 9, 10.45, -17.78, 0.10866, 1, 9, 3.78, -11.62, 1, 1, 9, -1.02, -4.4, 1, 1, 9, 1.46, 2.42, 1, 1, 9, 5.44, 7.92, 1, 3, 7, -25.5, 16.92, 0.40364, 8, -36.95, 26.11, 0.22784, 9, 15.4, 12.04, 0.36852, 2, 7, -13.02, 16.08, 0.6252, 8, -24.47, 25.26, 0.3748, 2, 7, -10.6, 9.48, 0.97146, 8, -22.05, 18.66, 0.02854, 1, 7, -2.81, 10.75, 1, 1, 7, -5.58, 6.54, 1, 1, 7, -7.01, 0.96, 1, 1, 7, -7.27, -6.79, 1, 2, 7, -11.15, -16.22, 0.78345, 8, -22.59, -7.04, 0.21655, 3, 6, 35.91, -15.44, 0.03711, 7, -17.04, -24.58, 0.26431, 8, -28.48, -15.4, 0.69858, 1, 8, -36.81, -15.73, 1, 3, 7, -24.8, 11.39, 0.50447, 8, -36.24, 20.57, 0.33239, 9, 16.1, 6.5, 0.16313, 1, 7, -25.26, 6.51, 1, 1, 7, -27.38, -2.5, 1, 2, 7, 9.03, -8.55, 0.96046, 8, -2.41, 0.63, 0.03954, 2, 7, 6.3, -16.68, 0.79773, 8, -5.15, -7.5, 0.20227, 2, 7, 1.57, -29.39, 0.25785, 8, -9.88, -20.21, 0.74215, 1, 7, 2.49, -13.75, 1, 2, 7, -0.17, -21.74, 0.65879, 8, -11.62, -12.56, 0.34121, 2, 7, -5.57, -33.02, 0.07126, 8, -17.02, -23.84, 0.92874, 2, 7, 10.67, 5.59, 0.81956, 8, -0.77, 14.77, 0.18044, 2, 7, 6.96, 5.69, 0.99331, 8, -4.48, 14.87, 0.00669], "hull": 27, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 50, 52, 0, 52, 0, 54, 54, 56, 58, 56, 56, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 58, 66, 70, 62, 72, 72, 74, 74, 18, 70, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 70, 42, 98, 98, 100, 102, 104, 104, 106, 106, 108, 108, 110, 92, 112, 112, 114, 114, 116, 54, 118, 118, 120, 120, 122, 124, 126, 126, 128, 54, 130, 48, 50, 130, 48, 44, 46, 46, 48, 132, 46], "width": 62, "height": 73}}, "qiongqi028": {"qiongqi028": {"type": "mesh", "uvs": [0, 0.13093, 0.24564, 0.15582, 0.44637, 0, 0.65001, 0.22338, 0.86819, 0.44738, 1, 0.69626, 0.86528, 0.98426, 0.57437, 1, 0.22819, 0.94871, 0.05074, 0.58604, 0, 0.33716, 0.2017, 0.41458, 0.5025, 0.58218, 0.79888, 0.71194], "triangles": [9, 11, 8, 8, 11, 12, 9, 10, 11, 11, 1, 12, 3, 1, 2, 1, 11, 0, 11, 10, 0, 8, 12, 7, 7, 13, 6, 7, 12, 13, 6, 13, 5, 13, 4, 5, 13, 12, 4, 12, 1, 3, 12, 3, 4], "vertices": [1, 172, 10.95, -5.71, 1, 2, 171, 13.21, -6.51, 0.04051, 172, 5.61, -6.66, 0.95949, 2, 171, 9.77, -10.44, 0.1967, 172, 2.06, -10.51, 0.8033, 2, 171, 4.33, -7.85, 0.58815, 172, -3.3, -7.77, 0.41185, 2, 171, -1.41, -5.34, 0.97257, 172, -8.98, -5.1, 0.02743, 1, 171, -5.46, -1.87, 1, 1, 171, -4.08, 3.94, 1, 2, 171, 1.97, 6.02, 0.92434, 172, -5.28, 6.17, 0.07566, 2, 171, 9.54, 7.29, 0.19282, 172, 2.32, 7.23, 0.80718, 1, 172, 7.77, 1.92, 1, 1, 172, 9.99, -2.12, 1, 1, 172, 5.35, -1.91, 1, 2, 171, 5.62, -0.74, 0.95417, 172, -1.82, -0.69, 0.04583, 1, 171, -1.3, -0.35, 1], "hull": 11, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20, 0, 22, 22, 24, 24, 26], "width": 22, "height": 18}}, "qiongqi029": {"qiongqi029": {"type": "mesh", "uvs": [0.02312, 0.84262, 0.0894, 1, 0.26962, 0.99406, 0.39183, 0.82651, 0.59898, 0.76528, 0.82269, 0.61706, 0.97598, 0.3174, 1, 0, 0.87448, 0.0564, 0.75848, 0.28195, 0.5534, 0.25295, 0.37733, 0.1144, 0.18055, 0.33995, 0.07283, 0.63317, 0.1977, 0.74984, 0.31928, 0.51927, 0.53578, 0.5115, 0.75894, 0.43119, 0.87052, 0.28352], "triangles": [18, 9, 8, 18, 8, 7, 6, 18, 7, 17, 9, 18, 17, 10, 9, 5, 17, 18, 5, 18, 6, 4, 17, 5, 10, 15, 11, 15, 12, 11, 17, 16, 10, 10, 16, 15, 4, 16, 17, 3, 15, 16, 3, 16, 4, 13, 12, 15, 14, 13, 15, 14, 15, 3, 0, 13, 14, 2, 14, 3, 1, 0, 14, 1, 14, 2], "vertices": [1, 168, -6.38, 3.62, 1, 1, 168, -7.06, -1.41, 1, 2, 168, -1.23, -6.24, 0.99418, 169, -4.6, -10.23, 0.00582, 2, 168, 5.61, -6.18, 0.61104, 169, 1.18, -6.56, 0.38896, 3, 168, 13.27, -10.63, 0.01089, 169, 10.03, -6.27, 0.88049, 170, -4.49, -4.97, 0.10861, 2, 169, 19.93, -3.77, 0.00068, 170, 5.64, -6.28, 0.99932, 1, 170, 15.29, -2.57, 1, 1, 170, 20.51, 4.3, 1, 1, 170, 15.19, 5.66, 1, 2, 169, 18.67, 5.59, 0.01299, 170, 7.9, 2.88, 0.98701, 2, 169, 10.28, 7.69, 0.79994, 170, 0.88, 7.93, 0.20006, 3, 168, 17.74, 8.75, 0.03311, 169, 3.55, 12.53, 0.96676, 170, -3.6, 14.9, 0.00013, 2, 168, 7.5, 9.56, 0.6255, 169, -5.56, 7.8, 0.3745, 2, 168, -1.1, 6.53, 0.99966, 169, -11.25, 0.68, 0.00034, 1, 168, 0.8, 0.72, 1, 2, 168, 8.74, 2.08, 0.39692, 169, -0.55, 2.11, 0.60308, 2, 169, 8.47, 0.91, 0.99745, 170, -3.3, 2.28, 0.00255, 1, 170, 5.88, -0.6, 1, 1, 170, 11.94, 0.46, 1], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26, 2, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 14], "width": 42, "height": 27}}, "qiongqi022": {"qiongqi022": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-53.94, -54.91, 20.07, 83.55, 134.72, 22.28, 60.71, -116.19], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 157, "height": 130}}, "qiongqi02": {"qiongqi02": {"type": "mesh", "uvs": [0.76582, 0, 0.84743, 0.033, 0.96332, 0.11743, 0.97965, 0.21973, 0.82621, 0.28467, 0.75276, 0.36423, 0.69237, 0.42593, 0.77724, 0.52173, 0.91762, 0.60454, 1, 0.69384, 1, 0.79126, 0.96985, 0.89356, 0.9013, 0.965, 0.82948, 0.98611, 0.70216, 0.98286, 0.5373, 0.97636, 0.35775, 0.98448, 0.18473, 1, 0.07373, 1, 0, 0.96337, 0, 0.81075, 0, 0.65812, 0, 0.57207, 0.0215, 0.38697, 0.07863, 0.24733, 0.23206, 0.20511, 0.35775, 0.14341, 0.3806, 0.02976, 0.4818, 0.05086, 0.63197, 0.07847, 0.51756, 0.11067, 0.63885, 0.13228, 0.72212, 0.14308, 0.65514, 0.26193, 0.60626, 0.37717, 0.58997, 0.4402, 0.65514, 0.57885, 0.77099, 0.71751, 0.8615, 0.80755, 0.24964, 0.34476, 0.37817, 0.30334, 0.53023, 0.26733, 0.12655, 0.52483, 0.30214, 0.4474, 0.47954, 0.42219, 0.12836, 0.6905, 0.26775, 0.60226, 0.47773, 0.56805, 0.11206, 0.87777, 0.26231, 0.77873, 0.42343, 0.71931, 0.60264, 0.6959, 0.18447, 0.9372, 0.34378, 0.86697, 0.54652, 0.83636, 0.67686, 0.85436, 0.81625, 0.88318], "triangles": [42, 39, 43, 42, 23, 39, 43, 39, 40, 23, 24, 39, 24, 25, 39, 39, 25, 40, 22, 23, 42, 43, 40, 44, 44, 41, 34, 44, 40, 41, 25, 26, 40, 40, 26, 41, 26, 30, 41, 41, 31, 33, 41, 30, 31, 26, 28, 30, 26, 27, 28, 30, 29, 31, 30, 28, 29, 18, 52, 17, 18, 48, 52, 18, 19, 48, 19, 20, 48, 52, 48, 49, 48, 45, 49, 48, 20, 45, 20, 21, 45, 49, 46, 50, 49, 45, 46, 45, 42, 46, 46, 43, 47, 46, 42, 43, 45, 21, 22, 45, 22, 42, 50, 47, 51, 50, 46, 47, 51, 47, 36, 47, 35, 36, 47, 44, 35, 47, 43, 44, 35, 44, 34, 17, 52, 16, 52, 53, 16, 16, 53, 15, 15, 53, 54, 52, 49, 53, 53, 50, 54, 53, 49, 50, 14, 56, 13, 13, 56, 12, 15, 55, 14, 14, 55, 56, 15, 54, 55, 12, 56, 11, 38, 56, 37, 54, 51, 55, 56, 55, 37, 55, 51, 37, 54, 50, 51, 51, 36, 37, 34, 33, 5, 34, 41, 33, 5, 33, 4, 33, 32, 4, 4, 2, 3, 2, 32, 1, 2, 4, 32, 33, 31, 32, 32, 0, 1, 0, 32, 29, 32, 31, 29, 36, 6, 7, 36, 35, 6, 35, 34, 6, 6, 34, 5, 56, 38, 11, 11, 38, 10, 10, 38, 9, 8, 9, 38, 38, 37, 8, 37, 7, 8, 37, 36, 7], "vertices": [2, 66, 141.28, 16.59, 0.99192, 67, -89.71, -63.48, 0.00808, 1, 66, 143.6, -16.63, 1, 1, 66, 134.09, -70.09, 1, 1, 66, 101.9, -92.75, 1, 2, 65, 86.66, -74.79, 0.02091, 66, 54.21, -51.54, 0.97909, 2, 65, 67.32, -38.58, 0.31943, 66, 14.84, -39.93, 0.68057, 2, 65, 52.75, -9.28, 0.96212, 66, -16.27, -29.76, 0.03788, 2, 64, 84.1, -26.12, 0.54741, 65, 7.78, -27.51, 0.45259, 1, 64, 22.41, -26.38, 1, 2, 64, -21.74, -13.26, 0.99518, 71, -97.7, 57.68, 0.00482, 2, 64, -40.76, 18.5, 0.85415, 71, -77.53, 88.73, 0.14585, 2, 64, -50.95, 57.7, 0.62205, 71, -46.8, 115.12, 0.37795, 3, 64, -42.66, 94.31, 0.43669, 71, -10.28, 123.77, 0.56072, 72, -92.51, 117.02, 0.0026, 3, 64, -23.49, 115.13, 0.30838, 71, 16.85, 115.71, 0.67424, 72, -64.83, 111.06, 0.01737, 3, 64, 18.44, 138.8, 0.09089, 71, 56.54, 88.46, 0.76317, 72, -23.17, 86.94, 0.14594, 3, 64, 73.17, 168.69, 0.00045, 71, 107.45, 52.44, 0.25389, 72, 30.35, 54.93, 0.74566, 2, 72, 91.41, 25.14, 0.98612, 70, 105.69, 125.64, 0.01388, 2, 72, 151.65, -1, 0.49568, 70, 159.46, 87.95, 0.50432, 2, 72, 188.49, -21.09, 0.24985, 70, 191.52, 60.88, 0.75015, 2, 72, 206.29, -46.66, 0.17191, 70, 203.83, 32.26, 0.82809, 3, 72, 178.52, -97.57, 0.03224, 70, 166.42, -12.05, 0.96415, 68, 220.84, 95.52, 0.00361, 3, 70, 129, -56.36, 0.48275, 68, 187.92, 47.77, 0.11385, 188, 91.63, 14.92, 0.4034, 3, 70, 107.9, -81.34, 0.24733, 68, 169.36, 20.85, 0.2136, 188, 64.54, -3.4, 0.53907, 3, 70, 56.31, -129.84, 0.00334, 68, 122.74, -32.45, 0.66988, 188, 1.71, -36.05, 0.32677, 2, 68, 74.84, -63.87, 0.95511, 188, -54.34, -47.88, 0.04489, 2, 67, 124.66, -34.72, 0.09679, 68, 17.99, -44.16, 0.90321, 2, 67, 73, -46.45, 0.94048, 68, -34.43, -36.5, 0.05952, 1, 67, 54.53, -86.43, 1, 2, 66, 76.65, 104.46, 0.00513, 67, 19.2, -69.71, 0.99487, 2, 66, 92.23, 48.88, 0.50597, 67, -33.56, -46.28, 0.49403, 2, 66, 62.2, 82.32, 0.02633, 67, 11.35, -44.46, 0.97367, 2, 66, 75.01, 37.54, 0.51054, 67, -31.31, -25.79, 0.48946, 2, 66, 85.19, 7.48, 0.96017, 67, -60.97, -14.46, 0.03983, 2, 66, 33.49, 10.32, 0.86086, 67, -25.82, 23.56, 0.13914, 4, 65, 81, 15.3, 0.63759, 66, -13.96, 7.63, 0.14143, 69, -20.59, -40.72, 0.14915, 67, 2.36, 61.84, 0.07182, 4, 65, 60.44, 29.04, 0.39925, 66, -38.18, 2.61, 0.00505, 69, -8.52, -19.13, 0.56095, 67, 13.93, 83.69, 0.03475, 4, 64, 112.55, 16.21, 0.10961, 65, 2.57, 23.23, 0.48046, 71, -12.18, -49.97, 0.17082, 69, -18.88, 38.1, 0.23911, 2, 64, 47.91, 38.92, 0.4076, 71, -20.21, 18.07, 0.5924, 2, 64, 0.98, 50.7, 0.58078, 71, -30.26, 65.4, 0.41922, 5, 69, 106.6, -87.05, 0.00449, 70, -19.93, -86.46, 0.00528, 67, 130.57, 18.43, 0.00038, 68, 42.64, 3.3, 0.76985, 188, -59.87, 26.41, 0.22, 7, 65, 136.01, 87.38, 0.00212, 66, -26.74, 97.39, 0.00059, 69, 55.61, -89.86, 0.05744, 70, -67.2, -67.13, 0.00239, 67, 79.66, 14.45, 0.68014, 68, -6.29, 17.92, 0.15208, 188, -100.11, 57.85, 0.10524, 4, 65, 129.9, 28.61, 0.02134, 66, 10.86, 51.81, 0.08625, 69, -3.46, -88.41, 0.03567, 67, 20.58, 14.55, 0.85674, 3, 70, 59.77, -64.2, 0.32141, 68, 119.79, 33.23, 0.4497, 188, 22.88, 26.18, 0.22889, 5, 69, 97.37, -44.27, 0.16174, 70, -9.92, -43.85, 0.27052, 67, 120.37, 60.99, 0.02254, 68, 48.44, 46.68, 0.39609, 188, -38.67, 64.7, 0.1491, 5, 65, 80.71, 66.16, 0.06246, 66, -50.41, 43.1, 0.01507, 69, 30.09, -36.41, 0.67152, 67, 52.93, 67.3, 0.21257, 68, -12.21, 76.85, 0.03837, 2, 70, 99.86, -15.66, 0.95391, 68, 154.96, 85.44, 0.04609, 2, 70, 37.98, -7.28, 0.97038, 68, 92.55, 87.74, 0.02962, 4, 65, 28.63, 85.15, 0.00011, 71, 41.82, -89.94, 0.06145, 72, -24.18, -92.07, 0.01699, 69, 44.91, 17, 0.92144, 2, 72, 153.53, -54.93, 0.19693, 70, 150.48, 34.74, 0.80307, 3, 72, 85.64, -60.77, 0.34398, 69, 144.08, 73.61, 0.02495, 70, 82.81, 42.63, 0.63107, 4, 71, 90.34, -52.92, 0.1177, 72, 21.37, -51.44, 0.4189, 69, 79.43, 67.33, 0.32614, 70, 21.71, 64.67, 0.13726, 5, 64, 106.73, 64.56, 0.00697, 65, -32.84, 56.67, 0.03508, 71, 28.69, -23.48, 0.79224, 72, -42.36, -26.81, 0.0039, 69, 11.67, 76.03, 0.1618, 2, 72, 140.31, -22, 0.46691, 70, 144.14, 69.65, 0.53309, 3, 72, 74.67, -16.59, 0.88988, 69, 122.87, 113.89, 0.0105, 70, 80.92, 88.11, 0.09963, 2, 71, 75.55, 9.72, 0.38797, 72, 1.82, 9.89, 0.61203, 3, 64, 51.72, 101.82, 0.03992, 71, 37.96, 42.3, 0.8838, 72, -38.16, 39.49, 0.07628, 3, 64, 0.89, 84.14, 0.33414, 71, -0.26, 80.18, 0.65574, 72, -79.17, 74.33, 0.01012], "hull": 30, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 0, 58, 56, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 22, 78, 80, 80, 82, 84, 86, 86, 88, 90, 92, 92, 94, 96, 98, 98, 100, 100, 102, 104, 106, 106, 108, 110, 112], "width": 378, "height": 380}}, "qiongqi056": {"qiongqi056": {"type": "mesh", "uvs": [0.00997, 0.18146, 0.13632, 0.04705, 0.42011, 0, 0.68111, 0, 0.77639, 0.22381, 0.96075, 0.41346, 1, 0.67861, 0.95246, 0.92533, 0.82403, 1, 0.62104, 0.86826, 0.34139, 0.73016, 0.14461, 0.57549, 0.06175, 0.33613, 0.44151, 0.29842, 0.6494, 0.514, 0.78169, 0.78279], "triangles": [1, 2, 13, 12, 0, 1, 13, 12, 1, 11, 12, 13, 13, 2, 3, 13, 3, 4, 14, 13, 4, 10, 11, 13, 14, 10, 13, 14, 4, 5, 14, 5, 6, 15, 14, 6, 9, 10, 14, 15, 9, 14, 7, 15, 6, 8, 15, 7, 9, 15, 8], "vertices": [1, 175, 19.31, 9.92, 1, 2, 174, 40.61, -3.16, 0.00248, 175, 21.45, -0.9, 0.99752, 2, 174, 31.47, -16.5, 0.37686, 175, 13.77, -15.13, 0.62314, 3, 173, 42.47, -23.86, 0.00613, 174, 21.13, -26.83, 0.74432, 175, 4.58, -26.5, 0.24955, 3, 173, 28.04, -19.5, 0.17638, 174, 7.39, -20.63, 0.78011, 175, -9.74, -21.78, 0.04351, 2, 173, 12.28, -20.41, 0.73812, 174, -8.36, -19.48, 0.26188, 2, 173, -2.31, -11.97, 0.99684, 174, -21.72, -9.23, 0.00316, 1, 173, -13.01, -0.39, 1, 1, 173, -12.36, 8.18, 1, 2, 173, 1.15, 12.14, 0.95936, 174, -15.17, 14.23, 0.04064, 3, 173, 17.59, 19.25, 0.35358, 174, 2.06, 19.15, 0.58664, 175, -19.24, 17.22, 0.05978, 3, 173, 32.03, 22.05, 0.03092, 174, 16.74, 20.05, 0.54077, 175, -4.73, 19.66, 0.4283, 2, 174, 30.68, 12.67, 0.03284, 175, 9.91, 13.79, 0.96716, 2, 174, 17.33, -4.05, 0.85221, 175, -1.61, -4.24, 0.14779, 2, 173, 17.88, -2.72, 0.63855, 174, -0.51, -2.68, 0.36145, 2, 173, -0.06, 1.72, 0.99975, 174, -17.72, 4.06, 0.00025], "hull": 13, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 0, 24, 2, 26, 4, 26, 26, 28, 28, 30], "width": 56, "height": 63}}, "qiongqi030": {"qiongqi030": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-8.04, -14.47, 6.57, 12.87, 18.04, 6.74, 3.43, -20.6], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 31, "height": 13}}, "qiongqi031": {"qiongqi031": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-8.92, -14, 5.22, 12.46, 18.45, 5.38, 4.31, -21.07], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 15}}, "qiongqi032": {"qiongqi032": {"type": "mesh", "uvs": [0, 0.26162, 0.1101, 0.42989, 0.16513, 0.56195, 0.21828, 0.68951, 0.31443, 0.91066, 0.49231, 0.79047, 0.56683, 0.57893, 0.68462, 0.32412, 0.76128, 0.21935, 0.82885, 0.12701, 1, 0, 1, 0.11739, 0.89856, 0.29047, 0.83597, 0.39369, 0.76154, 0.51643, 0.71106, 0.6222, 0.71347, 0.81931, 0.62212, 0.89624, 0.50398, 0.83145, 0.30883, 0.95052, 0.29064, 1, 0.1915, 1, 0.13698, 0.90697, 0.09492, 0.75744, 0.06008, 0.66296, 0.02483, 0.5674, 0, 0.43967, 0.06406, 0.46985, 0.15407, 0.73987, 0.21802, 0.89146, 0.11508, 0.6229, 0.58737, 0.75388, 0.64372, 0.60762, 0.70966, 0.41578, 0.78999, 0.30308, 0.87152, 0.21436], "triangles": [24, 25, 30, 25, 27, 30, 27, 1, 30, 30, 1, 2, 25, 26, 27, 1, 27, 0, 27, 26, 0, 22, 23, 28, 29, 28, 3, 23, 30, 28, 23, 24, 30, 28, 2, 3, 28, 30, 2, 21, 29, 20, 20, 29, 19, 21, 22, 29, 18, 19, 4, 19, 29, 4, 22, 28, 29, 4, 29, 3, 13, 35, 12, 13, 34, 35, 34, 9, 35, 34, 8, 9, 12, 35, 11, 11, 35, 10, 10, 35, 9, 14, 15, 33, 15, 32, 33, 33, 32, 7, 32, 6, 7, 14, 34, 13, 14, 33, 34, 34, 7, 8, 34, 33, 7, 18, 4, 5, 18, 31, 17, 16, 32, 15, 32, 16, 31, 16, 17, 31, 18, 5, 31, 5, 6, 31, 31, 6, 32], "vertices": [1, 167, 7.27, -0.18, 1, 2, 166, 7.06, -2.32, 0.07855, 167, 0.71, -3.16, 0.92145, 3, 165, 9.21, -1.85, 0.00725, 166, 2.68, -3.22, 0.68544, 167, -3.67, -4.07, 0.30731, 3, 165, 4.98, -2.72, 0.33896, 166, -1.54, -4.09, 0.65803, 167, -7.89, -4.94, 0.003, 2, 162, 3.81, 15.41, 0.11058, 165, -2.44, -4.42, 0.88942, 3, 162, 2.2, 5.78, 0.86885, 165, -4.05, -14.05, 0.13113, 166, -10.58, -15.42, 2e-05, 4, 162, 5.23, -0.23, 0.75526, 163, 0.11, 7.99, 0.24276, 165, -1.02, -20.06, 0.00198, 166, -7.55, -21.43, 0, 2, 163, 3.06, -0.53, 0.93936, 164, 1.84, 10.15, 0.06064, 3, 163, 3.58, -5.33, 0.49706, 164, 2.37, 5.35, 0.50294, 166, -4.07, -34.76, 0, 3, 163, 4.05, -9.56, 0.07137, 164, 2.83, 1.12, 0.92863, 166, -3.61, -38.99, 0, 1, 164, 1.54, -8.28, 1, 1, 164, -1.15, -6.85, 1, 3, 163, -1.41, -10.76, 0.04292, 164, -2.63, -0.07, 0.95708, 166, -9.07, -40.18, 0, 3, 163, -2.24, -6.62, 0.44656, 164, -3.46, 4.06, 0.55344, 166, -9.9, -36.05, 0, 4, 162, 1.89, -9.93, 0.02793, 163, -3.23, -1.71, 0.89679, 164, -4.45, 8.98, 0.07529, 166, -10.89, -31.13, 0, 2, 162, 0.7, -6.32, 0.42553, 163, -4.42, 1.91, 0.57447, 2, 162, -3.88, -4.01, 0.90973, 163, -9, 4.21, 0.09027, 3, 162, -3.4, 1.12, 0.99904, 165, -9.66, -18.71, 0.00096, 166, -16.18, -20.08, 0, 3, 162, 0.98, 5.75, 0.8818, 165, -5.27, -14.08, 0.11819, 166, -11.8, -15.46, 2e-05, 2, 162, 3.03, 16.16, 0.08295, 165, -3.22, -3.68, 0.91705, 2, 162, 2.34, 17.6, 0.0422, 165, -3.91, -2.23, 0.9578, 3, 162, 4.77, 22.14, 6e-05, 165, -1.48, 2.31, 0.97405, 166, -8.01, 0.94, 0.02589, 2, 165, 1.99, 3.67, 0.5856, 166, -4.54, 2.3, 0.4144, 3, 165, 6.45, 3.77, 0.03372, 166, -0.08, 2.39, 0.90463, 167, -6.43, 1.55, 0.06165, 2, 166, 2.94, 2.84, 0.5105, 167, -3.41, 1.99, 0.4895, 2, 166, 6, 3.28, 0.06094, 167, -0.35, 2.43, 0.93906, 1, 167, 3.19, 2.01, 1, 1, 167, 0.92, -0.56, 1, 2, 165, 5.4, 0.84, 0.14029, 166, -1.13, -0.53, 0.85971, 3, 162, 6.61, 19.6, 2e-05, 165, 0.36, -0.23, 0.97371, 166, -6.17, -1.61, 0.02627, 2, 166, 2.51, -0.18, 0.65958, 167, -3.84, -1.03, 0.34042, 4, 162, 0.71, 0.97, 0.98347, 163, -4.41, 9.19, 0.0096, 165, -5.54, -18.86, 0.00693, 166, -12.07, -20.23, 0, 2, 162, 2.69, -3.41, 0.58075, 163, -2.44, 4.82, 0.41925, 3, 163, 0.35, -0.56, 0.97493, 164, -0.87, 10.13, 0.02507, 166, -7.31, -29.98, 0, 3, 163, 0.96, -5.62, 0.50059, 164, -0.26, 5.06, 0.49941, 166, -6.7, -35.05, 0, 3, 163, 1, -10.45, 0.01263, 164, -0.22, 0.24, 0.98737, 166, -6.66, -39.88, 0], "hull": 27, "edges": [0, 2, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 50, 52, 0, 52, 0, 54, 56, 58, 54, 60, 60, 56, 2, 4, 4, 6, 46, 48, 48, 50, 62, 64, 64, 66, 66, 68, 68, 70, 70, 20, 24, 26, 26, 28, 14, 16, 16, 18], "width": 52, "height": 26}}, "qiongqi07": {"qiongqi07": {"type": "mesh", "uvs": [0, 0, 0.0907, 0, 0.13991, 0.24333, 0.2942, 0.39606, 0.55134, 0.40042, 0.7442, 0.48769, 0.8642, 0.72333, 1, 0.86951, 1, 0.96333, 0.93063, 1, 0.7592, 1, 0.63277, 0.78442, 0.42277, 0.72988, 0.18063, 0.83024, 0.04563, 0.61206, 0, 0.26515, 0.06683, 0.27094, 0.16936, 0.53846, 0.37443, 0.57108, 0.60192, 0.62654, 0.75251, 0.78966], "triangles": [16, 15, 0, 1, 16, 0, 2, 16, 1, 3, 16, 2, 17, 16, 3, 14, 15, 16, 14, 16, 17, 18, 3, 4, 17, 3, 18, 18, 4, 19, 12, 18, 19, 13, 17, 18, 13, 18, 12, 14, 17, 13, 19, 4, 5, 19, 5, 6, 20, 11, 19, 12, 19, 11, 6, 20, 19, 20, 6, 7, 20, 9, 10, 20, 7, 9, 11, 20, 10, 8, 9, 7], "vertices": [1, 159, 29.52, -4.84, 1, 1, 159, 27.16, -9.34, 1, 2, 158, 24.18, -14.87, 0.00489, 159, 14.03, -5.56, 0.99511, 2, 158, 15, -7.07, 0.71348, 159, 2.58, -9.31, 0.28652, 2, 157, 29.08, -4.81, 0.21276, 158, 0.61, -7.79, 0.78724, 2, 157, 18.6, -10.25, 0.99879, 158, -10.49, -3.73, 0.00121, 1, 157, 4.27, -7.42, 1, 1, 157, -6.75, -8.35, 1, 1, 157, -10.79, -5.13, 1, 1, 157, -9.94, -0.84, 1, 1, 157, -3.95, 6.66, 1, 2, 157, 9.73, 4.8, 0.9789, 158, -5.35, 12.97, 0.0211, 2, 157, 19.41, 12.12, 0.2369, 158, 6.58, 10.77, 0.7631, 3, 157, 23.55, 26.17, 0, 158, 19.74, 17.19, 0.98778, 159, -15.61, 7.42, 0.01221, 2, 158, 28.09, 5.72, 0.36808, 159, -1.47, 8.54, 0.63192, 1, 159, 16.61, 1.93, 1, 1, 159, 14.59, -1.23, 1, 2, 158, 21.45, 1.22, 0.73913, 159, -1.11, 0.52, 0.26087, 2, 157, 27.93, 8.79, 0.01373, 158, 9.87, 2.24, 0.98627, 2, 157, 17.6, 0.74, 0.98002, 158, -3.05, 4.43, 0.01998, 1, 157, 5.32, -0.26, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30, 0, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 18], "width": 56, "height": 55}}, "qiongqi034": {"qiongqi034": {"type": "mesh", "uvs": [0.53482, 0.43476, 0.56273, 0.4542, 0.64204, 0.50185, 0.77423, 0.49796, 0.89173, 0.43087, 0.91963, 0.31515, 0.86529, 0.21888, 0.71548, 0.18874, 0.5642, 0.18971, 0.47607, 0.14012, 0.44376, 0.06038, 0.43054, 9e-05, 0.32039, 0, 0.29248, 0.06621, 0.24107, 0.15956, 0.20289, 0.27139, 0.2117, 0.36766, 0.27926, 0.41434, 0.26751, 0.52325, 0.22785, 0.64771, 0.14414, 0.64771, 0, 0.68649, 0, 0.79296, 0, 0.90969, 0.13878, 0.97768, 0.28797, 1, 0.45072, 0.98409, 0.67547, 0.98538, 0.78797, 0.97309, 0.86341, 0.96485, 0.98934, 0.84042, 1, 0.74678, 0.92541, 0.64031, 0.81109, 0.60568, 0.72584, 0.62877, 0.64059, 0.62107, 0.58247, 0.52358, 0.55747, 0.46087, 0.39936, 0.42954, 0.41419, 0.52033, 0.42531, 0.64303, 0.45681, 0.77922, 0.52909, 0.6369, 0.65511, 0.69947, 0.81634, 0.72033, 0.86638, 0.83566, 0.33265, 0.64671, 0.23628, 0.74732, 0.11582, 0.78781, 0.14362, 0.89456], "triangles": [45, 28, 27, 29, 28, 45, 29, 45, 30, 44, 45, 43, 30, 45, 31, 45, 44, 31, 44, 32, 31, 43, 34, 44, 34, 33, 44, 44, 33, 32, 25, 24, 49, 41, 25, 49, 41, 49, 47, 24, 23, 49, 49, 22, 48, 49, 23, 22, 49, 48, 47, 22, 21, 48, 48, 20, 47, 20, 19, 47, 48, 21, 20, 47, 19, 46, 41, 26, 25, 27, 26, 41, 45, 27, 41, 43, 45, 41, 47, 46, 41, 41, 42, 43, 42, 35, 43, 43, 35, 34, 46, 40, 41, 41, 40, 42, 46, 19, 18, 46, 39, 40, 46, 18, 39, 40, 39, 42, 42, 36, 35, 42, 39, 36, 39, 37, 36, 18, 38, 39, 18, 17, 38, 39, 0, 37, 39, 38, 0, 8, 38, 16, 38, 17, 16, 8, 16, 15, 2, 1, 3, 3, 1, 4, 4, 1, 5, 0, 7, 5, 0, 5, 1, 0, 8, 7, 0, 38, 8, 6, 5, 7, 9, 15, 14, 8, 15, 9, 14, 13, 9, 13, 10, 9, 10, 12, 11, 10, 13, 12], "vertices": [2, 13, 17.62, 10.71, 0.52014, 14, 25.42, 12.54, 0.47986, 2, 13, 13.84, 11.6, 0.82927, 14, 28.59, 14.78, 0.17073, 2, 13, 3.68, 13.21, 0.99093, 14, 36.53, 21.32, 0.00907, 1, 13, -6.62, 5.78, 1, 1, 13, -10.73, -8.54, 1, 1, 13, -3.77, -24.04, 1, 1, 13, 8.24, -32.86, 1, 1, 13, 22.67, -28.63, 1, 1, 13, 34.74, -20.56, 1, 2, 13, 45.76, -21.94, 0.98017, 14, -17.67, 13.13, 0.01983, 2, 13, 54.69, -29.91, 0.98554, 14, -29.56, 11.73, 0.01446, 1, 13, 60.54, -36.52, 1, 2, 13, 69.39, -30.74, 0.99299, 14, -39.94, 1.28, 0.00701, 2, 13, 66.37, -21.24, 0.93974, 14, -30.82, -2.76, 0.06026, 2, 13, 63.08, -7.21, 0.63973, 14, -18.14, -9.6, 0.36027, 2, 13, 57.26, 8.36, 0.05697, 14, -2.63, -15.57, 0.94304, 1, 14, 11.31, -16.75, 1, 1, 14, 18.94, -11.31, 1, 2, 14, 34.4, -14.71, 0.97863, 16, -30.23, 25.35, 0.02137, 3, 14, 51.71, -21.09, 0.45514, 15, -10.46, -19.03, 0.10758, 16, -14.34, 15.97, 0.43729, 3, 14, 50.55, -29.04, 0.17766, 15, -13.64, -26.41, 0.05334, 16, -16.91, 8.35, 0.769, 3, 14, 54.11, -43.54, 0.02496, 15, -13.95, -41.34, 0.00017, 16, -16.01, -6.56, 0.97487, 1, 16, -1.38, -11.49, 1, 1, 16, 14.65, -16.9, 1, 1, 16, 28.26, -7.43, 1, 2, 15, 38.73, -33.94, 0.16509, 16, 35.9, 5.1, 0.83491, 2, 15, 42.8, -18.67, 0.66918, 16, 38.71, 20.64, 0.33082, 3, 15, 51.5, 1.07, 0.87765, 16, 45.79, 41.03, 8e-05, 17, 43.31, -15, 0.12227, 2, 15, 54.14, 11.69, 0.2735, 17, 40.35, -4.46, 0.7265, 2, 15, 55.91, 18.82, 0.01926, 17, 38.37, 2.61, 0.98074, 1, 17, 19.12, 12.65, 1, 1, 17, 5.51, 12.18, 1, 3, 14, 60.33, 45.33, 0.00173, 15, 15.04, 42.9, 0.00625, 17, -9.05, 3.38, 0.99202, 3, 14, 53.77, 35.2, 0.02845, 15, 6.09, 34.81, 0.10007, 17, -12.84, -8.08, 0.87149, 3, 14, 55.9, 26.62, 0.10692, 15, 5.93, 25.97, 0.27329, 17, -8.62, -15.85, 0.61979, 3, 14, 53.62, 18.68, 0.40463, 15, 1.66, 18.89, 0.3646, 17, -8.83, -24.11, 0.23077, 4, 13, 6.74, 18.98, 0.02535, 14, 38.82, 15.2, 0.94181, 15, -13.53, 19.36, 0.02041, 17, -22.27, -31.2, 0.01243, 3, 13, 13.73, 12.69, 0.20978, 14, 29.48, 14.14, 0.78988, 17, -31.05, -34.58, 0.00035, 1, 14, 22.79, -0.22, 1, 2, 14, 36.02, -0.72, 0.99963, 16, -26.12, 38.82, 0.00037, 3, 14, 53.78, -2.23, 0.94265, 15, -3.59, -1.35, 0.04227, 16, -8.92, 34.15, 0.01508, 2, 15, 15.75, -6.38, 0.91008, 16, 10.75, 30.7, 0.08992, 3, 14, 54.34, 7.76, 0.6199, 15, -0.46, 8.15, 0.33293, 17, -5.38, -34.5, 0.04717, 3, 14, 65.07, 18.41, 0.08797, 15, 12.66, 15.67, 0.5645, 17, 2.31, -21.48, 0.34753, 3, 14, 70.3, 33.29, 0.00717, 15, 21.56, 28.69, 0.0885, 17, 3.63, -5.76, 0.90433, 1, 17, 19.72, 0.84, 1, 3, 14, 53.02, -11.11, 0.68377, 15, -6.62, -9.73, 0.15402, 16, -11.26, 25.55, 0.1622, 3, 14, 66.12, -22.37, 0.10526, 15, 3.12, -24, 0.20948, 16, -0.4, 12.12, 0.68526, 1, 16, 1.47, -0.72, 1, 1, 16, 16.98, -3.14, 1], "hull": 38, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 0, 74, 76, 78, 78, 80, 80, 82, 82, 54, 84, 86, 86, 88, 88, 90, 54, 56, 56, 58, 90, 56, 92, 94, 94, 96, 96, 98], "width": 96, "height": 145}}, "qiongqi035": {"qiongqi035": {"x": 16.51, "y": -0.01, "rotation": 114.09, "width": 106, "height": 154}}, "qiongqi036": {"qiongqi036": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 74, 101.15, -24.37, 1, 1, 74, -14.88, -50.78, 1, 1, 74, -31.3, 21.38, 1, 1, 74, 84.73, 47.78, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 119, "height": 74}}, "qiongqi057": {"qiongqi057": {"type": "mesh", "uvs": [0, 0.89527, 0.05417, 1, 0.35835, 0.92618, 0.59435, 0.7267, 0.75955, 0.51037, 0.96146, 0.34461, 1, 0.22942, 0.92213, 0.02152, 0.72022, 0, 0.45013, 0.03837, 0.22986, 0.16199, 0.14857, 0.41766, 0.104, 0.6677, 0.07777, 0.82223, 0.20374, 0.85775, 0.32674, 0.64487, 0.40054, 0.39549, 0.61248, 0.21504, 0.8036, 0.18058], "triangles": [4, 18, 5, 4, 17, 18, 16, 9, 17, 5, 18, 6, 18, 7, 6, 17, 8, 18, 17, 9, 8, 18, 8, 7, 15, 16, 3, 3, 16, 4, 15, 11, 16, 16, 17, 4, 11, 10, 16, 16, 10, 9, 1, 14, 2, 1, 13, 14, 1, 0, 13, 14, 15, 2, 2, 15, 3, 13, 12, 14, 14, 12, 15, 12, 11, 15], "vertices": [1, 176, -4.17, 3.02, 1, 1, 176, -5.69, -1.78, 1, 2, 176, 5.98, -9.58, 0.93519, 177, -10.5, -8.8, 0.06481, 3, 176, 19.36, -11.53, 0.13726, 177, 2.7, -11.77, 0.85892, 178, -6.86, -15.83, 0.00382, 2, 177, 14.44, -11.83, 0.55942, 178, 3.93, -11.19, 0.44058, 2, 177, 25.57, -14.51, 0.02972, 178, 15.2, -9.2, 0.97028, 2, 177, 30.42, -12.81, 0.00171, 178, 18.97, -5.71, 0.99829, 1, 178, 19.89, 3.65, 1, 1, 178, 12.24, 8.64, 1, 2, 177, 21.08, 11.47, 0.29453, 178, 0.71, 12.82, 0.70547, 3, 176, 25.3, 16.69, 0.00123, 177, 10.8, 15.9, 0.81343, 178, -10.48, 12.78, 0.18534, 3, 176, 14.99, 11.96, 0.24168, 177, 0.16, 11.98, 0.75151, 178, -18.67, 4.94, 0.00681, 2, 176, 5.98, 6.19, 0.93283, 177, -9.27, 6.93, 0.06717, 1, 176, 0.46, 2.59, 1, 1, 176, 3.28, -2.56, 1, 2, 176, 13.57, -0.42, 0.98753, 177, -2.21, -0.25, 0.01247, 2, 177, 8.02, 3.76, 0.9878, 178, -8.18, 0.54, 0.0122, 2, 177, 19.91, 1.12, 0.0097, 178, 3.77, 2.86, 0.9903, 1, 178, 12.07, 0.18, 1], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26, 2, 28, 28, 30, 30, 32, 32, 34, 34, 36], "width": 45, "height": 42}}, "qiongqi038": {"qiongqi038": {"type": "mesh", "uvs": [0.11222, 0.06487, 0.17551, 0.27887, 0.1936, 0.51392, 0.10016, 0.72441, 0, 0.8507, 0.00974, 0.93841, 0.19963, 0.95244, 0.33526, 1, 0.59749, 0.94893, 0.7723, 0.8051, 1, 0.67179, 1, 0.52093, 0.82656, 0.35605, 0.68791, 0.02277, 0.50706, 0.15257, 0.37444, 0.00874, 0.1514, 0, 0.41908, 0.56917, 0.67115, 0.58518, 0.86365, 0.60118, 0.33658, 0.25443, 0.47407, 0.39313, 0.68949, 0.34512, 0.24491, 0.83057, 0.47407, 0.76122, 0.74907, 0.70254], "triangles": [6, 23, 7, 7, 24, 8, 7, 23, 24, 23, 6, 4, 6, 5, 4, 4, 3, 23, 3, 2, 23, 23, 17, 24, 23, 2, 17, 17, 21, 18, 17, 2, 21, 2, 20, 21, 2, 1, 20, 21, 20, 14, 1, 0, 20, 0, 16, 20, 20, 15, 14, 20, 16, 15, 18, 22, 12, 18, 21, 22, 21, 14, 22, 14, 13, 22, 12, 22, 13, 24, 17, 18, 8, 25, 9, 8, 24, 25, 9, 19, 10, 9, 25, 19, 24, 18, 25, 25, 18, 19, 19, 11, 10, 19, 18, 12, 19, 12, 11], "vertices": [2, 148, 28.29, 5.41, 0.9932, 146, 30.49, -31.67, 0.0068, 2, 148, 17.21, 13.66, 0.74669, 146, 25.29, -18.88, 0.25331, 4, 147, 25.56, 25.33, 0.0001, 148, 8.07, 24.78, 0.12989, 146, 23.22, -4.63, 0.85362, 144, 17.71, -20.94, 0.01638, 2, 146, 29.14, 8.55, 0.24007, 144, 26.18, -9.24, 0.75993, 2, 146, 35.82, 16.63, 0.00522, 144, 34.37, -2.68, 0.99478, 1, 144, 34.48, 2.71, 1, 2, 143, 51.15, -1.09, 0.00481, 144, 21.28, 5.57, 0.99519, 2, 143, 43.71, 5.68, 0.19187, 144, 12.19, 9.88, 0.80813, 1, 143, 25.57, 10.9, 1, 1, 143, 10.59, 8.34, 1, 1, 143, -7.5, 7.98, 1, 3, 143, -11.47, -0.32, 0.93746, 145, -17.71, 2.45, 0.02755, 147, -19.35, -10.18, 0.035, 4, 143, -4.7, -14.71, 0.1103, 145, -8.12, -10.23, 0.13029, 147, -3.45, -10.32, 0.75916, 148, -23.62, -8.51, 0.00025, 2, 147, 16.95, -20, 0.84091, 148, -4.03, -19.75, 0.15909, 2, 147, 22.01, -5.79, 0.24711, 148, 2.12, -5.97, 0.75289, 1, 148, 14.84, -7.91, 1, 2, 148, 28.22, 0.58, 0.99964, 146, 27.93, -35.77, 0.00036, 3, 147, 10.97, 17.94, 0.02932, 148, -7.05, 18.54, 0.01406, 146, 7.05, -2.14, 0.95662, 2, 143, 11.28, -6.86, 0.05802, 145, 5.9, 0.75, 0.94198, 2, 143, -0.63, -0.08, 0.99888, 147, -14.87, -0.31, 0.00112, 3, 147, 27.56, 6.63, 0.00697, 148, 8.61, 5.98, 0.848, 146, 13.95, -20.99, 0.14504, 3, 147, 14.65, 7.12, 0.53433, 148, -4.22, 7.47, 0.1258, 146, 3.74, -13.08, 0.33987, 2, 147, 4.55, -4.74, 0.99704, 148, -15.2, -3.57, 0.00296, 2, 146, 18.53, 14.45, 0.03216, 144, 16.99, -1.3, 0.96784, 3, 145, 22.03, 7.89, 0.09929, 146, 2.51, 9.34, 0.0629, 144, 0.27, -3.06, 0.83781, 1, 143, 9.38, 1.99, 1], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32, 4, 34, 34, 36, 36, 38, 40, 42, 42, 44, 8, 46, 46, 48, 48, 50], "width": 71, "height": 61}}, "qiongqi039": {"qiongqi039": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-9.75, -4.54, -2.87, 15.3, 29.26, 4.15, 22.37, -15.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 21, "height": 34}}, "qiongqi051": {"qiongqi051": {"type": "mesh", "uvs": [0, 0.0716, 0.19298, 0, 0.39619, 0.06902, 0.51402, 0.08451, 0.6643, 0.12581, 0.67284, 0.31425, 0.66942, 0.50527, 0.81287, 0.55948, 0.92216, 0.67306, 1, 0.78406, 1, 0.92862, 0.89996, 1, 0.77872, 1, 0.68479, 0.88474, 0.52427, 0.85892, 0.33301, 0.86667, 0.19127, 0.92088, 0.01367, 0.82795, 0, 0.64725, 0, 0.46397, 0.05466, 0.28327, 0.00172, 0.16711, 0.13112, 0.14673, 0.24849, 0.33944, 0.3092, 0.55356, 0.46096, 0.65145, 0.60868, 0.68816, 0.74629, 0.7218, 0.87377, 0.85334, 0.12707, 0.44956, 0.13922, 0.61474, 0.19992, 0.75851, 0.32943, 0.73404, 0.3092, 0.18344, 0.41037, 0.31803, 0.46703, 0.48015, 0.5581, 0.48627, 0.49536, 0.19567, 0.57226, 0.31803], "triangles": [28, 8, 9, 28, 9, 10, 11, 28, 10, 12, 28, 11, 27, 6, 7, 27, 7, 8, 28, 27, 8, 12, 13, 27, 28, 12, 27, 26, 36, 6, 26, 25, 36, 26, 6, 27, 14, 25, 26, 13, 26, 27, 14, 26, 13, 37, 2, 3, 4, 37, 3, 38, 4, 5, 38, 37, 4, 34, 2, 37, 34, 37, 38, 35, 34, 38, 36, 35, 38, 6, 38, 5, 36, 38, 6, 30, 29, 24, 30, 18, 19, 30, 19, 29, 25, 24, 35, 25, 35, 36, 32, 24, 25, 31, 30, 24, 17, 18, 30, 17, 30, 31, 15, 32, 25, 15, 25, 14, 31, 32, 15, 16, 17, 31, 16, 31, 15, 33, 2, 34, 23, 33, 34, 29, 20, 23, 24, 23, 34, 24, 34, 35, 29, 23, 24, 32, 31, 24, 22, 0, 1, 21, 0, 22, 33, 1, 2, 22, 1, 33, 20, 21, 22, 23, 22, 33, 20, 22, 23, 19, 20, 29], "vertices": [2, 84, -13.02, -3.5, 0.99994, 86, -50.85, 28.54, 6e-05, 3, 84, 4.02, 15.92, 0.98108, 85, -25.19, 0.77, 0.00241, 93, -36.68, -39.67, 0.01651, 4, 84, 29.08, 26.19, 0.30256, 85, -11.53, 24.15, 0.28757, 86, -1.88, 44.53, 0.00146, 93, -23.03, -16.29, 0.40841, 4, 84, 42.39, 33.87, 0.05917, 85, -5.62, 38.34, 0.1266, 86, 13.11, 47.95, 0.0002, 93, -17.12, -2.09, 0.81403, 3, 84, 60.44, 42.15, 1e-05, 93, -7.82, 15.45, 0.99981, 87, 11, 49.57, 0.00018, 3, 86, 38.81, 35.47, 0.00615, 93, 7.96, 11.6, 0.87225, 87, 15.08, 33.84, 0.1216, 4, 86, 43.42, 19.69, 0.02419, 93, 23.49, 6.2, 0.27106, 87, 17.67, 17.61, 0.65778, 88, -19.25, 9.75, 0.04698, 4, 93, 33.58, 22.56, 0.00714, 87, 36.85, 16.47, 0.22509, 88, -1.77, 17.76, 0.70004, 89, 1.9, 28.7, 0.06773, 2, 88, 15.42, 19.01, 0.47456, 89, 12.63, 15.21, 0.52544, 2, 88, 29.29, 17.88, 0.01753, 89, 19.52, 3.12, 0.98247, 1, 89, 15.88, -8.77, 1, 2, 88, 30.73, -4.75, 0.1435, 89, 1.65, -10.84, 0.8565, 3, 87, 39.47, -21.59, 0.03635, 88, 18.43, -14.59, 0.94069, 89, -13.42, -6.23, 0.02296, 2, 87, 25.64, -14.09, 0.61517, 88, 2.7, -14.48, 0.38483, 3, 85, 58.25, 19.44, 0.03112, 86, 34.77, -15.04, 0.27332, 87, 4.73, -15.76, 0.69555, 3, 85, 51.36, -4.45, 0.42993, 86, 11.31, -23.29, 0.56502, 87, -19.59, -20.99, 0.00505, 3, 84, 49.2, -49.12, 0.00451, 85, 50.22, -23.43, 0.29177, 86, -4.81, -33.37, 0.70372, 3, 84, 25.7, -55.8, 0.05101, 85, 35.61, -43.01, 0.02585, 86, -29.23, -32.83, 0.92315, 2, 84, 15.34, -44.08, 0.12794, 86, -35.68, -18.58, 0.87206, 3, 84, 6.31, -31.16, 0.32767, 85, 5.24, -35.22, 0.02704, 86, -40.51, -3.58, 0.64529, 3, 84, 3.24, -14.35, 0.79435, 85, -7.42, -23.75, 0.03967, 86, -38.51, 13.39, 0.16597, 3, 84, -8.13, -10.1, 0.9901, 85, -19.02, -27.28, 0.00019, 86, -48.12, 20.79, 0.00971, 2, 84, 4.66, 0.97, 0.99984, 93, -27.09, -51.15, 0.00016, 3, 84, 26.66, -3.88, 0.04069, 85, 4.82, -1.19, 0.95202, 86, -13.04, 16.51, 0.00729, 3, 85, 24.76, 0.75, 0.78085, 86, 0.11, 1.4, 0.21835, 93, 13.26, -39.68, 0.0008, 2, 85, 38.75, 17.01, 0.00202, 86, 21.47, -0.57, 0.99798, 3, 86, 40.72, 2.3, 0, 93, 36.08, -6.08, 0.00354, 87, 12.8, 0.7, 0.99646, 4, 93, 44.26, 10.09, 0.00063, 87, 30.92, 1.15, 0.32052, 88, 0.19, 1.45, 0.67636, 89, -10.46, 17.89, 0.00249, 2, 88, 20.2, 2.97, 0.28946, 89, 2.08, 2.22, 0.71054, 3, 84, 19.14, -20.68, 0.31551, 85, 9.06, -19.1, 0.18478, 86, -25.16, 2.66, 0.49971, 3, 84, 28.57, -31.42, 0.09274, 85, 23.08, -21.9, 0.03654, 86, -19.31, -10.38, 0.87072, 3, 84, 42.13, -37.03, 0.01266, 85, 37.25, -18.13, 0.24805, 86, -8.01, -19.73, 0.73929, 3, 85, 40.35, -1.44, 0.46553, 86, 7.37, -12.57, 0.53416, 87, -22.15, -9.87, 0.00031, 4, 84, 25.44, 11.64, 0.50105, 85, -5.58, 10.39, 0.35079, 86, -9.64, 31.7, 0.00268, 93, -17.07, -30.04, 0.14548, 5, 84, 42.85, 9.69, 0.03394, 85, 9.44, 19.42, 0.45491, 86, 6.43, 24.71, 0.15287, 93, -2.06, -21.01, 0.35711, 87, -18.4, 27.23, 0.00117, 4, 85, 24.95, 22.22, 0.10889, 86, 17.71, 13.69, 0.585, 93, 13.46, -18.22, 0.23957, 87, -8.59, 14.89, 0.06654, 4, 85, 29.04, 33.34, 0.01597, 86, 29.14, 16.82, 0.24566, 93, 17.55, -7.09, 0.36673, 87, 3.14, 16.55, 0.37164, 4, 84, 45.88, 24.64, 0.05041, 85, 2.75, 33.14, 0.157, 86, 13.73, 38.11, 0.02134, 93, -8.74, -7.3, 0.77125, 4, 85, 15.81, 39.48, 0.00992, 86, 26.46, 31.15, 0.04137, 93, 4.31, -0.96, 0.88823, 87, 2.28, 31.11, 0.06048], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 0, 42, 0, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 58, 60, 60, 62, 62, 64, 66, 68, 68, 70, 70, 72, 74, 76], "width": 130, "height": 86}}, "qiongqi06": {"qiongqi06": {"type": "mesh", "uvs": [0.29515, 0.2692, 0.3021, 0.13844, 0.34897, 0.02582, 0.53453, 0.00413, 0.71452, 0.02702, 0.77947, 0.121, 0.7739, 0.20655, 0.69226, 0.27884, 0.69782, 0.37282, 0.68669, 0.44993, 0.60875, 0.52463, 0.65514, 0.58608, 0.68298, 0.70536, 0.71267, 0.80777, 0.82957, 0.80777, 0.94091, 0.83187, 1, 0.89672, 1, 0.98005, 0.88632, 1, 0.75438, 0.99061, 0.59893, 0.973, 0.40734, 0.98122, 0.38746, 0.89907, 0.4706, 0.83452, 0.39107, 0.76175, 0.27539, 0.65613, 0.27539, 0.53994, 0.21394, 0.45075, 0.23924, 0.33925, 0.53677, 0.12555, 0.53136, 0.21932, 0.48081, 0.30488, 0.45013, 0.40569, 0.44651, 0.53579, 0.48442, 0.63308, 0.56024, 0.74209, 0.6162, 0.82765, 0.54219, 0.89212, 0.43929, 0.93432, 0.69563, 0.8886, 0.86532, 0.90033], "triangles": [40, 14, 15, 40, 15, 16, 40, 39, 14, 40, 16, 17, 19, 39, 40, 18, 19, 40, 17, 18, 40, 39, 36, 13, 37, 36, 39, 39, 13, 14, 20, 37, 39, 20, 39, 19, 37, 23, 36, 38, 22, 23, 37, 38, 23, 38, 37, 20, 21, 22, 38, 21, 38, 20, 33, 32, 10, 33, 26, 32, 34, 33, 10, 34, 10, 11, 25, 26, 33, 25, 33, 34, 34, 11, 12, 35, 34, 12, 24, 25, 34, 24, 34, 35, 35, 12, 13, 36, 35, 13, 23, 24, 35, 23, 35, 36, 29, 3, 4, 29, 4, 5, 2, 3, 29, 1, 2, 29, 6, 29, 5, 30, 1, 29, 30, 29, 6, 0, 1, 30, 7, 30, 6, 31, 0, 30, 31, 30, 7, 31, 7, 8, 31, 28, 0, 32, 31, 8, 32, 28, 31, 9, 32, 8, 27, 28, 32, 10, 32, 9, 32, 26, 27], "vertices": [1, 48, 30.69, -42.95, 1, 1, 48, -6.32, -50.03, 1, 1, 48, -39.89, -48.67, 1, 1, 48, -53.67, -16.22, 1, 1, 48, -54.7, 18.06, 1, 1, 48, -31, 35.9, 1, 1, 48, -6.74, 40.35, 1, 1, 48, 16.94, 30.08, 1, 2, 48, 43.11, 37.09, 0.99644, 49, -28.36, 56.54, 0.00356, 2, 48, 65.22, 39.98, 0.93123, 49, -7.4, 48.9, 0.06877, 2, 48, 89.43, 30.54, 0.44477, 49, 9.72, 29.36, 0.55523, 2, 48, 104.77, 42.92, 0.05531, 49, 29.04, 33.28, 0.94469, 2, 49, 63.59, 29.62, 0.98275, 50, 3.82, 44.27, 0.01725, 3, 49, 93.53, 27.53, 0.33571, 50, 19.45, 18.65, 0.54012, 51, -10.04, 20.81, 0.12417, 3, 49, 99.06, 48.68, 0.01158, 50, 39.89, 26.39, 0.14105, 51, 11.47, 24.72, 0.84737, 2, 50, 61.82, 27.27, 3e-05, 51, 33.19, 21.62, 0.99997, 1, 51, 47.41, 5.22, 1, 1, 51, 51.7, -18.39, 1, 3, 52, -8.12, 77.07, 0.00058, 50, 69.41, -21.64, 0.00276, 51, 31.81, -27.85, 0.99666, 3, 52, 3.11, 54.94, 0.05549, 50, 45.38, -27.84, 0.22315, 51, 7.05, -29.6, 0.72136, 3, 52, 14.76, 27.83, 0.48104, 50, 16.4, -33.38, 0.44895, 51, -22.45, -29.81, 0.07001, 1, 52, 36.34, -0.87, 1, 2, 49, 103.59, -37.95, 0.0011, 52, 18.56, -16.92, 0.9989, 2, 49, 89.53, -18.21, 0.51372, 52, -5.5, -14.08, 0.48628, 2, 49, 65.5, -27.3, 0.97948, 52, -14.91, -37.99, 0.02052, 2, 48, 140.18, -21.86, 0.00499, 49, 30.59, -40.53, 0.99501, 2, 48, 107.55, -29.27, 0.42658, 49, -1.78, -32.07, 0.57342, 2, 48, 85.05, -46.17, 0.92282, 49, -29.54, -36.69, 0.07718, 1, 48, 52.69, -48.67, 1, 1, 48, -19.66, -8.06, 1, 1, 48, 6.9, -3.06, 1, 1, 48, 33.02, -6.82, 1, 1, 48, 62.6, -5.98, 1, 1, 49, 5.16, -0.8, 1, 1, 49, 34.06, -1.03, 1, 2, 49, 68.02, 4.74, 0.9992, 50, -13.91, 26.26, 0.0008, 2, 49, 94.51, 8.63, 0.5612, 50, 4.6, 6.91, 0.4388, 2, 52, 1.06, 6.2, 0.73736, 50, -1.77, -15.35, 0.26264, 1, 52, 21.76, -3.26, 1, 3, 52, -15.48, 29.67, 0.02637, 50, 24.71, -4.25, 0.95408, 51, -9.01, -2.66, 0.01955, 2, 52, -30.01, 58.08, 0, 51, 22.82, -0.31, 1], "hull": 29, "edges": [4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 0, 56, 2, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 72, 78, 78, 80, 0, 2, 2, 4], "width": 187, "height": 288}}, "qiongqi015": {"qiongqi015": {"type": "mesh", "uvs": [0.03831, 0.00253, 0.29571, 0, 0.59931, 0.05253, 0.83361, 0.16853, 0.90621, 0.30853, 0.91281, 0.50453, 0.88311, 0.71053, 0.76431, 0.85053, 0.59601, 0.91053, 0.57291, 1, 0.46401, 1, 0.31881, 0.89653, 0.35841, 0.75853, 0.29571, 0.65053, 0.16041, 0.75453, 0, 0.76253, 0, 0.63453, 0, 0.48653, 0.12741, 0.35053, 0.07791, 0.21053, 0, 0.09053, 0.31839, 0.11275, 0.57862, 0.2867, 0.62837, 0.52095, 0.61306, 0.73664, 0.48295, 0.85493, 0.34135, 0.4305, 0.15766, 0.57661, 0.08495, 0.69954, 0.30691, 0.26119], "triangles": [12, 13, 24, 7, 24, 6, 25, 12, 24, 8, 25, 24, 11, 12, 25, 7, 8, 24, 10, 11, 25, 10, 25, 8, 9, 10, 8, 23, 4, 5, 27, 17, 26, 16, 17, 27, 13, 27, 26, 13, 26, 23, 28, 16, 27, 28, 27, 13, 6, 23, 5, 24, 13, 23, 24, 23, 6, 14, 28, 13, 15, 16, 28, 15, 28, 14, 21, 1, 2, 0, 1, 21, 21, 20, 0, 19, 20, 21, 29, 19, 21, 22, 21, 2, 22, 2, 3, 29, 21, 22, 22, 3, 4, 18, 19, 29, 26, 29, 22, 18, 29, 26, 17, 18, 26, 23, 22, 4, 26, 22, 23], "vertices": [1, 113, -15.87, -12.62, 1, 1, 113, -13.19, 2.59, 1, 2, 113, -4.62, 19.49, 0.99946, 114, -37.54, 11.83, 0.00054, 2, 113, 9.32, 31.12, 0.93501, 114, -26.37, 26.13, 0.06499, 3, 113, 23.75, 32.76, 0.74618, 114, -12.61, 30.79, 0.25339, 115, -44.27, 28.41, 0.00043, 3, 113, 42.88, 29.47, 0.27577, 114, 6.78, 31.62, 0.66966, 115, -24.95, 30.3, 0.05457, 3, 113, 62.56, 23.85, 0.0198, 114, 27.21, 30.28, 0.61675, 115, -4.48, 30.08, 0.36345, 2, 114, 41.22, 23.46, 0.27351, 115, 9.89, 24.04, 0.72649, 2, 114, 47.38, 13.5, 0.04394, 115, 16.58, 14.43, 0.95606, 2, 114, 56.27, 12.31, 3e-05, 115, 25.52, 13.72, 0.99997, 1, 115, 26.02, 7.21, 1, 1, 115, 16.48, -2.26, 1, 1, 115, 2.67, -0.94, 1, 2, 113, 50.05, -9.62, 0.00034, 114, 22.04, -5.08, 0.99966, 1, 114, 32.52, -12.97, 1, 1, 114, 33.52, -22.58, 1, 2, 113, 45.13, -26.74, 0.02835, 114, 20.85, -22.86, 0.97165, 2, 113, 30.74, -23.96, 0.24252, 114, 6.2, -23.18, 0.75748, 2, 113, 18.97, -13.91, 0.84505, 114, -7.43, -15.83, 0.15495, 1, 113, 4.8, -14.19, 1, 1, 113, -7.75, -16.53, 1, 1, 113, -1.97, 1.81, 1, 2, 113, 17.9, 13.87, 0.93104, 114, -14.34, 11.1, 0.06896, 3, 113, 41.24, 12.41, 0.14965, 114, 8.78, 14.59, 0.81891, 115, -22.02, 13.4, 0.03144, 3, 113, 62.03, 7.45, 0.00066, 114, 30.15, 14.14, 0.47538, 115, -0.66, 14.13, 0.52396, 2, 114, 42.03, 6.59, 0.01989, 115, 11.61, 7.24, 0.98011, 2, 113, 29.18, -2.8, 0.51717, 114, 0.21, -2.82, 0.48283, 2, 113, 41.29, -16.37, 0.06015, 114, 14.91, -13.52, 0.93985, 2, 113, 52.41, -22.96, 0.00282, 114, 27.17, -17.62, 0.99718, 1, 113, 12.33, -1.65, 1], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 0, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 20, 44, 52, 52, 54, 54, 56], "width": 60, "height": 99}}, "qiongqi058": {"qiongqi058": {"type": "mesh", "uvs": [0.54865, 0, 0.75951, 0, 0.73708, 0.15453, 0.77297, 0.32775, 0.94795, 0.46921, 1, 0.57747, 1, 0.71605, 0.8627, 0.84307, 0.61595, 0.94989, 0.31984, 1, 0, 1, 0, 0.85606, 0, 0.69151, 0, 0.53273, 0.14487, 0.36239, 0.25703, 0.18052, 0.40957, 0.05926, 0.54263, 0.06829, 0.50736, 0.18741, 0.45447, 0.35948, 0.43096, 0.52587, 0.47798, 0.70361, 0.41333, 0.86244], "triangles": [19, 15, 18, 18, 2, 3, 15, 16, 18, 18, 17, 2, 18, 16, 17, 2, 17, 1, 17, 0, 1, 17, 16, 0, 5, 20, 4, 20, 19, 4, 19, 3, 4, 13, 14, 20, 20, 14, 19, 14, 15, 19, 19, 18, 3, 21, 22, 12, 7, 21, 6, 21, 5, 6, 12, 20, 21, 5, 21, 20, 12, 13, 20, 8, 9, 22, 22, 9, 11, 9, 10, 11, 8, 22, 7, 7, 22, 21, 22, 11, 12], "vertices": [1, 187, 26.68, -0.92, 1, 1, 187, 26.99, -8.71, 1, 2, 186, 43.43, -7.98, 0.24242, 187, 9.2, -8.6, 0.75758, 1, 186, 23.64, -10.64, 1, 2, 185, 31.91, -18.51, 0.2336, 186, 7.84, -18.19, 0.7664, 3, 184, 49.95, -13.39, 0.00325, 185, 19.51, -20.73, 0.72263, 186, -4.45, -20.94, 0.27413, 3, 184, 34.95, -18.77, 0.21489, 185, 3.57, -21.12, 0.77283, 186, -20.35, -22, 0.01228, 2, 184, 19.48, -18.93, 0.80495, 185, -11.15, -16.39, 0.19505, 2, 184, 4.83, -14.49, 0.99992, 185, -23.65, -7.56, 8e-05, 1, 184, -4.29, -6.12, 1, 1, 184, -8.29, 5.02, 1, 2, 184, 7.29, 10.61, 0.98509, 185, -13.41, 15.48, 0.01491, 3, 184, 25.1, 17, 0.28972, 185, 5.5, 15.94, 0.70962, 186, -20.01, 15.1, 0.00067, 3, 184, 42.29, 23.17, 0.0007, 185, 23.76, 16.38, 0.62705, 186, -1.79, 16.32, 0.37224, 3, 185, 43.47, 11.49, 0.00251, 186, 18.11, 12.28, 0.88017, 187, -15.57, 12.33, 0.11732, 1, 187, 5.5, 9.03, 1, 1, 187, 19.66, 3.95, 1, 2, 186, 52.84, -0.14, 0.00035, 187, 18.82, -1.01, 0.99965, 2, 186, 39.09, 0.24, 0.01611, 187, 5.08, -0.26, 0.98389, 2, 186, 19.22, 0.87, 0.9931, 187, -14.77, 0.9, 0.0069, 2, 185, 24.93, 0.46, 0.46358, 186, 0.06, 0.46, 0.53642, 1, 185, 4.54, -1.77, 1, 1, 184, 11.77, -4.03, 1], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32, 2, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44], "width": 37, "height": 115}}, "qiongqi023": {"qiongqi023": {"type": "mesh", "uvs": [0.00353, 0, 0.15227, 0, 0.30662, 0.10139, 0.65182, 0.09383, 0.99982, 0.35861, 1, 0.69148, 0.79775, 1, 0.39643, 0.9903, 0.11859, 0.61961, 0.00072, 0.30187, 0.23582, 0.38277, 0.54735, 0.63585, 0.7906, 0.68762], "triangles": [10, 1, 2, 0, 1, 10, 10, 9, 0, 8, 9, 10, 11, 2, 3, 12, 11, 3, 10, 2, 11, 4, 12, 3, 12, 4, 5, 11, 8, 10, 7, 8, 11, 6, 12, 5, 11, 12, 6, 7, 11, 6], "vertices": [14.78, 22.76, 12.61, 18.69, 8.3, 15.57, 3.41, 6.05, -7.05, -0.59, -13.8, 3.01, -17.11, 11.89, -11.05, 22.75, 0.53, 26.33, 8.7, 26.11, 3.62, 20.56, -6.06, 14.78, -10.67, 8.69], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18, 0, 20, 20, 22, 22, 24], "width": 31, "height": 23}}, "qiongqi012": {"qiongqi012": {"type": "mesh", "uvs": [0, 0.11803, 0.04665, 0.05774, 0.12339, 0.07021, 0.2069, 0.19495, 0.31976, 0.09308, 0.48679, 0, 0.70122, 0.02863, 0.8863, 0.22197, 0.92693, 0.41947, 0.86373, 0.57539, 0.7572, 0.60889, 0.69183, 0.61058, 0.62966, 0.58237, 0.68772, 0.61942, 0.76543, 0.62742, 0.87252, 0.63267, 0.95532, 0.69804, 1, 0.79367, 1, 0.89415, 0.95794, 0.94257, 0.87383, 1, 0.75949, 1, 0.63726, 1, 0.58206, 0.94983, 0.45063, 0.92804, 0.31729, 0.88991, 0.21009, 0.73828, 0.26177, 0.63954, 0.2254, 0.56549, 0.12395, 0.53199, 0.04163, 0.44912, 0, 0.28162, 0.64051, 0.25969, 0.42155, 0.33588, 0.18068, 0.32915, 0.09553, 0.21935, 0.50183, 0.43448, 0.45561, 0.57117, 0.40208, 0.71683, 0.49697, 0.82663, 0.65754, 0.85128, 0.58456, 0.70338, 0.75973, 0.7594, 0.86434, 0.88938, 0.86678, 0.70562, 0.91544, 0.77061], "triangles": [21, 43, 20, 20, 43, 19, 43, 21, 40, 21, 22, 40, 22, 23, 40, 24, 39, 23, 23, 39, 40, 19, 43, 18, 24, 25, 39, 43, 45, 18, 45, 17, 18, 25, 38, 39, 25, 26, 38, 40, 42, 43, 45, 42, 44, 45, 43, 42, 39, 41, 40, 40, 41, 42, 39, 38, 41, 45, 16, 17, 45, 44, 16, 41, 13, 42, 42, 13, 44, 38, 37, 41, 13, 14, 44, 44, 15, 16, 44, 14, 15, 41, 12, 13, 41, 37, 12, 26, 27, 38, 38, 27, 37, 27, 28, 37, 29, 34, 28, 37, 28, 33, 28, 34, 33, 29, 30, 34, 34, 31, 35, 34, 30, 31, 34, 35, 3, 31, 0, 35, 2, 35, 1, 35, 2, 3, 1, 35, 0, 11, 12, 10, 9, 10, 8, 10, 12, 8, 12, 37, 36, 12, 36, 32, 8, 32, 7, 32, 8, 12, 36, 37, 33, 36, 33, 32, 34, 3, 33, 3, 4, 33, 33, 4, 32, 4, 5, 32, 32, 6, 7, 32, 5, 6], "vertices": [2, 42, 63.38, -5.34, 0.12773, 43, -20.67, -18.51, 0.87227, 2, 42, 62.1, -10.82, 0.13458, 43, -24.27, -14.19, 0.86542, 2, 42, 56.77, -11.99, 0.1478, 43, -21.99, -9.23, 0.8522, 2, 42, 47.76, -5.43, 0.40274, 43, -11.33, -5.99, 0.59726, 2, 42, 43.39, -15.59, 0.95017, 43, -16.81, 3.62, 0.04983, 1, 42, 35.26, -26.58, 1, 1, 42, 20.55, -30.26, 1, 1, 42, 2.98, -21.58, 1, 1, 42, -5.35, -8.78, 1, 2, 42, -5.75, 3.87, 0.9992, 44, 23.19, 20.97, 0.0008, 3, 42, 0.18, 9.05, 0.95646, 43, 28.9, 23.26, 0.00161, 44, 18.32, 14.77, 0.04192, 3, 42, 4.37, 10.91, 0.87096, 43, 27.86, 18.8, 0.0131, 44, 14.55, 12.17, 0.11594, 3, 42, 9.21, 10.57, 0.50887, 43, 24.68, 15.14, 0.06326, 44, 9.74, 11.6, 0.42787, 3, 42, 4.38, 11.64, 0.11468, 43, 28.43, 18.35, 0.01109, 44, 14.68, 11.45, 0.87423, 3, 42, -0.89, 10.14, 0.02331, 43, 30.41, 23.45, 0.00016, 44, 19.57, 13.91, 0.97653, 2, 42, -7.97, 7.66, 0.00102, 44, 26.08, 17.65, 0.99898, 1, 44, 33.64, 16.64, 1, 1, 44, 40.22, 12.25, 1, 1, 44, 44.38, 5.84, 1, 1, 44, 43.92, 1.15, 1, 1, 44, 41.35, -5.71, 1, 1, 44, 34.64, -10.07, 1, 1, 44, 27.46, -14.73, 1, 1, 44, 22.15, -13.64, 1, 2, 43, 46.89, -3.67, 0.01317, 44, 13.53, -17.26, 0.98683, 2, 43, 41.71, -11.96, 0.1267, 44, 4.12, -19.91, 0.8733, 2, 43, 28.66, -16.28, 0.52213, 44, -8.45, -14.33, 0.47787, 2, 43, 22.32, -10.88, 0.85404, 44, -9.5, -6.06, 0.14596, 2, 43, 16.23, -11.9, 0.99761, 44, -14.7, -2.73, 0.00239, 1, 43, 11.96, -18.13, 1, 2, 42, 51.13, 16.83, 0.00357, 43, 4.41, -22.09, 0.99643, 2, 42, 58.66, 6.16, 0.0781, 43, -8.65, -21.67, 0.9219, 1, 42, 17.81, -12.4, 1, 1, 42, 29.79, -1.23, 1, 2, 42, 45.59, 4.7, 0.10736, 43, -1.93, -10.36, 0.89264, 2, 42, 54.27, -0.76, 0.14833, 43, -11.52, -14, 0.85167, 3, 42, 21.75, 3.57, 0.81335, 43, 11.53, 9.34, 0.17505, 44, -3.89, 16.16, 0.0116, 3, 42, 20.8, 14.41, 0.09004, 43, 20.75, 3.57, 0.76657, 44, -0.95, 5.68, 0.14339, 2, 43, 30.5, -2.87, 0.18314, 44, 1.94, -5.64, 0.81686, 2, 43, 40.26, 1.43, 0.00321, 44, 12.05, -9.03, 0.99679, 1, 44, 22.5, -4.48, 1, 3, 42, 8.64, 20.28, 0.02147, 43, 32.77, 9.74, 0.00361, 44, 12.1, 2.17, 0.97492, 2, 42, -4.32, 19.57, 0.00175, 44, 24.7, 5.27, 0.99825, 1, 44, 36.22, 0.98, 1, 2, 42, -9.71, 12.94, 0.00023, 44, 28.76, 12.78, 0.99977, 1, 44, 34.31, 10.49, 1], "hull": 32, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 0, 62, 12, 64, 64, 66, 66, 68, 68, 70, 70, 2, 64, 72, 72, 74, 74, 76, 76, 78, 78, 80, 74, 82, 82, 84, 84, 86, 26, 88, 88, 90], "width": 70, "height": 76}}, "qiongqi040": {"qiongqi040": {"type": "mesh", "uvs": [0.08055, 0, 0.20748, 0, 0.56921, 0.25645, 0.92459, 0.49961, 1, 0.79487, 1, 1, 0.67074, 1, 0.27728, 0.92948, 0.15036, 0.66027, 0, 0.33461, 0, 0.07843, 0.29644, 0.38716, 0.72103, 0.77671], "triangles": [8, 9, 11, 0, 11, 10, 0, 1, 11, 11, 1, 2, 11, 9, 10, 6, 12, 5, 12, 4, 5, 6, 7, 12, 7, 8, 12, 12, 3, 4, 8, 11, 12, 12, 11, 3, 3, 11, 2], "vertices": [1, 150, 16.08, 2.12, 1, 1, 150, 15.53, -1.13, 1, 2, 149, 9.28, -9.41, 0.50501, 150, 4.37, -8.81, 0.49499, 2, 149, -3.67, -7.66, 0.99842, 150, -6.26, -16.4, 0.00158, 1, 149, -12.03, 0.07, 1, 1, 149, -16.75, 6.27, 1, 1, 149, -9.94, 11.46, 1, 1, 149, -0.18, 15.53, 1, 1, 149, 8.64, 9.39, 1, 2, 149, 19.25, 1.92, 0.0195, 150, 3.88, 6.28, 0.9805, 1, 150, 13.48, 4.68, 1, 2, 149, 11.91, -1.16, 0.39629, 150, 0.64, -0.99, 0.60371, 1, 149, -5.84, 3.92, 1], "hull": 11, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20, 0, 22, 22, 24], "width": 26, "height": 38}}, "qiongqi041": {"qiongqi041": {"x": 60.08, "y": 0.64, "rotation": 12.82, "width": 47, "height": 65}}, "qiongqi042": {"qiongqi042": {"type": "mesh", "uvs": [0.09936, 0, 0.28128, 0, 0.48786, 0.07557, 0.66978, 0.29157, 0.72219, 0.60757, 0.83011, 0.73157, 1, 0.80357, 1, 0.95957, 0.74069, 1, 0.42003, 1, 0.16719, 0.85157, 0.00069, 0.56357, 0, 0.18757, 0.18147, 0.31079, 0.34088, 0.63924, 0.5378, 0.77305, 0.73003, 0.84604], "triangles": [4, 15, 3, 16, 4, 5, 15, 4, 16, 5, 6, 7, 9, 14, 15, 8, 16, 5, 8, 5, 7, 15, 16, 8, 9, 15, 8, 13, 0, 1, 12, 0, 13, 11, 12, 13, 2, 13, 1, 14, 2, 3, 15, 14, 3, 14, 13, 2, 11, 13, 14, 10, 11, 14, 10, 14, 9], "vertices": [1, 79, -7.77, -6.1, 1, 1, 79, -3.93, 1.75, 1, 2, 79, 2.94, 9.42, 0.99789, 80, -8.96, 21.81, 0.00211, 2, 79, 13.96, 13.75, 0.86874, 80, 1.96, 17.25, 0.13126, 2, 79, 25.57, 10.86, 0.19363, 80, 8.29, 7.1, 0.80637, 2, 79, 31.97, 13.5, 0.00223, 80, 14.71, 4.54, 0.99777, 1, 80, 23.29, 4.79, 1, 1, 80, 25.24, -0.64, 1, 1, 80, 14.04, -6.26, 1, 2, 79, 32.23, -8.55, 0.29363, 80, -0.44, -11.48, 0.70637, 2, 79, 21.96, -17.03, 0.93927, 80, -13.72, -10.42, 0.06073, 1, 79, 8.87, -19.52, 1, 1, 79, -3.64, -13.43, 1, 1, 79, 4.29, -7.61, 1, 2, 79, 18.57, -6.09, 0.99138, 80, -8.54, -0.2, 0.00862, 1, 80, 2.03, -1.66, 1, 1, 80, 11.63, -1.08, 1], "hull": 13, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 0, 24, 0, 26, 26, 28, 28, 30, 30, 32], "width": 48, "height": 37}}, "qiongqi043": {"qiongqi043": {"type": "mesh", "uvs": [0.0086, 0.16271, 0.13976, 0.04545, 0.31234, 0, 0.50333, 0.01907, 0.59768, 0.08943, 0.65751, 0, 0.78176, 0.02493, 0.92903, 0.22428, 1, 0.45586, 0.96694, 0.5198, 0.85382, 0.39361, 0.72218, 0.35557, 0.75736, 0.46297, 0.76463, 0.74269, 0.68512, 0.80178, 0.6394, 0.67888, 0.58918, 0.5665, 0.4399, 0.5786, 0.45713, 0.69597, 0.44246, 0.95842, 0.35146, 1, 0.26402, 0.81644, 0.15587, 0.73729, 0.04542, 0.57899, 0, 0.33567, 0.15517, 0.22338, 0.32312, 0.28578, 0.54006, 0.4106, 0.19365, 0.46409, 0.36511, 0.65577], "triangles": [9, 10, 8, 10, 7, 8, 10, 11, 7, 11, 4, 6, 11, 6, 7, 6, 4, 5, 27, 4, 11, 14, 15, 13, 15, 12, 13, 15, 16, 12, 17, 27, 16, 17, 26, 27, 16, 27, 12, 27, 11, 12, 29, 17, 18, 20, 21, 19, 19, 21, 18, 18, 21, 29, 21, 22, 29, 22, 28, 29, 22, 23, 28, 29, 28, 17, 23, 24, 28, 28, 26, 17, 24, 25, 28, 28, 25, 26, 27, 26, 4, 24, 0, 25, 25, 2, 26, 26, 2, 3, 0, 1, 25, 25, 1, 2, 4, 26, 3], "vertices": [1, 75, -5.91, 5.63, 1, 2, 75, 4.81, 15.97, 0.99835, 77, -42.61, -25.83, 0.00165, 1, 75, 20.15, 21.74, 1, 1, 75, 37.92, 23.11, 1, 3, 75, 47.38, 19.4, 0.73319, 76, -8.55, 19.21, 0.01192, 77, -7.06, -2.17, 0.25489, 2, 75, 51.86, 26.71, 0.00429, 77, -6.68, 6.4, 0.99571, 1, 77, 3.55, 12.07, 1, 1, 77, 23.3, 9.02, 1, 1, 77, 38.9, -0.25, 1, 1, 77, 39.35, -5.82, 1, 1, 77, 25.38, -5.03, 1, 3, 75, 61.83, 2, 0.1052, 76, 13.84, 16.01, 0.7348, 77, 14.02, -10.37, 0.16, 1, 76, 22, 13.63, 1, 1, 76, 38.31, 1.33, 1, 1, 76, 37.02, -7.13, 1, 1, 76, 27.37, -4.8, 1, 2, 76, 18.05, -3.28, 0.99637, 77, 13.74, -30.12, 0.00363, 4, 75, 38.42, -18.15, 0.0011, 78, 4.27, 12.19, 0.552, 76, 10.01, -14.64, 0.44577, 77, 3.34, -39.35, 0.00113, 1, 78, 12.86, 10.7, 1, 2, 78, 30.34, 2.74, 0.84411, 76, 31.73, -31.87, 0.15589, 2, 78, 30.22, -6.25, 0.99959, 76, 28.78, -40.37, 0.00041, 1, 78, 14.83, -9.2, 1, 2, 75, 14.12, -33.69, 0.06405, 78, 5.9, -16.61, 0.93595, 2, 75, 2.18, -23.86, 0.42336, 78, -8.51, -22.2, 0.57664, 2, 75, -4.75, -6.97, 0.92599, 78, -26.63, -19.95, 0.07401, 1, 75, 8.24, 3.36, 1, 2, 75, 24.38, 1.28, 0.99846, 77, -18.37, -29.18, 0.00154, 1, 76, 6.33, 0.31, 1, 2, 75, 14.5, -13.44, 0.52764, 78, -11.56, -6.35, 0.47236, 3, 75, 32.42, -24.79, 0.00199, 78, 7.12, 3.71, 0.92476, 76, 10.03, -23.59, 0.07325], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48, 0, 50, 50, 52, 52, 54, 56, 58, 58, 36, 34, 36, 38, 40, 36, 38, 34, 32, 32, 30, 30, 28, 26, 28, 54, 24, 26, 24, 24, 22, 22, 20, 16, 18, 20, 18], "width": 93, "height": 73}}, "qiongqi044": {"qiongqi044": {"type": "mesh", "uvs": [1, 1, 0.66667, 1, 0.33333, 1, 0, 1, 0, 0, 0.33333, 0, 0.66667, 0, 1, 0], "triangles": [2, 3, 5, 3, 4, 5, 1, 2, 6, 2, 5, 6, 0, 1, 7, 1, 6, 7], "vertices": [1, 190, 9.1, 21.46, 1, 2, 190, 9.1, -12.54, 0.65191, 191, 19.87, 22.86, 0.34809, 3, 190, 9.1, -46.54, 0.00656, 191, 19.87, -11.14, 0.62447, 192, 20.6, 18.61, 0.36897, 2, 191, 19.87, -45.14, 0.00144, 192, 20.6, -15.39, 0.99856, 2, 191, -22.13, -45.14, 0.00271, 192, -21.4, -15.39, 0.99729, 3, 190, -32.9, -46.54, 0.00482, 191, -22.13, -11.14, 0.63142, 192, -21.4, 18.61, 0.36376, 2, 190, -32.9, -12.54, 0.47329, 191, -22.13, 22.86, 0.52671, 2, 190, -32.9, 21.46, 0.94927, 191, -22.13, 56.86, 0.05073], "hull": 8, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 0], "width": 102, "height": 42}}, "qiongqi045": {"qiongqi045": {"type": "mesh", "uvs": [1, 1, 0.66667, 1, 0.33333, 1, 0, 1, 0, 0, 0.33333, 0, 0.66667, 0, 1, 0], "triangles": [1, 6, 7, 0, 1, 7, 2, 5, 6, 1, 2, 6, 3, 4, 5, 2, 3, 5], "vertices": [3, 201, 9.77, 42.18, 0.10811, 202, 25.17, 21.24, 0.88615, 203, 45.08, 12.21, 0.00575, 2, 201, 9.77, 15.84, 0.52542, 202, 25.17, -5.09, 0.47458, 2, 200, 12.03, 17.43, 0.31487, 201, 9.77, -10.49, 0.68513, 2, 200, 12.03, -8.9, 0.99119, 201, 9.77, -36.82, 0.00881, 4, 200, -35.97, -8.9, 0.89093, 201, -38.23, -36.82, 0.05333, 202, -22.83, -57.76, 0.03226, 203, -2.92, -66.79, 0.02348, 4, 200, -35.97, 17.43, 0.50542, 201, -38.23, -10.49, 0.19868, 202, -22.83, -31.43, 0.18699, 203, -2.92, -40.46, 0.10891, 4, 200, -35.97, 43.76, 0.05719, 201, -38.23, 15.84, 0.04445, 202, -22.83, -5.09, 0.29475, 203, -2.92, -14.13, 0.60361, 2, 202, -22.83, 21.24, 0.00904, 203, -2.92, 12.21, 0.99096], "hull": 8, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 0], "width": 79, "height": 48}}, "qiongqi046": {"qiongqi046": {"type": "mesh", "uvs": [0, 0.34912, 0.03613, 0.49169, 0.12946, 0.71518, 0.29863, 0.90784, 0.51446, 1, 0.70307, 1, 0.92863, 1, 1, 1, 1, 0.86545, 0.89752, 0.72674, 0.81002, 0.51096, 0.69335, 0.27977, 0.57863, 0.14876, 0.4153, 0.03316, 0.2578, 0, 0.16252, 0, 0.09252, 0.0794, 0, 0.19885, 0.12284, 0.20613, 0.29137, 0.27058, 0.50425, 0.37604, 0.658, 0.58112, 0.79401, 0.76275, 0.57226, 0.74517, 0.40668, 0.61041, 0.20858, 0.45807], "triangles": [18, 16, 15, 17, 16, 18, 18, 15, 14, 19, 18, 14, 0, 17, 18, 25, 18, 19, 1, 0, 18, 1, 18, 25, 2, 1, 25, 2, 25, 3, 19, 14, 13, 20, 13, 12, 19, 13, 20, 24, 19, 20, 25, 19, 24, 24, 20, 23, 3, 25, 24, 3, 24, 4, 11, 20, 12, 21, 11, 10, 21, 20, 11, 23, 20, 21, 22, 21, 10, 23, 21, 22, 4, 24, 23, 4, 23, 5, 22, 10, 9, 5, 23, 22, 5, 22, 6, 6, 9, 8, 22, 9, 6, 6, 8, 7], "vertices": [2, 198, -12.07, -86.81, 0.0002, 199, 5.24, -27.17, 0.9998, 2, 198, 3.47, -79.01, 0.03999, 199, 20.78, -19.37, 0.96001, 3, 197, 5.48, -106.69, 0.0024, 198, 27.83, -58.85, 0.35451, 199, 45.14, 0.79, 0.64309, 4, 196, 3.51, -95.65, 0.00072, 197, 26.48, -70.15, 0.14722, 198, 48.83, -22.31, 0.68977, 199, 66.14, 37.33, 0.16228, 4, 196, 13.55, -49.03, 0.15004, 197, 36.53, -23.53, 0.59104, 198, 58.88, 24.31, 0.25744, 199, 76.19, 83.95, 0.00148, 4, 195, 12.61, -47.63, 0.00234, 196, 13.55, -8.29, 0.88687, 197, 36.53, 17.21, 0.10859, 198, 58.88, 65.05, 0.00219, 2, 195, 12.61, 1.09, 0.96687, 196, 13.55, 40.43, 0.03313, 1, 195, 12.61, 16.5, 1, 1, 195, -2.06, 16.5, 1, 3, 195, -17.18, -5.63, 0.66267, 196, -16.23, 33.71, 0.2756, 197, 6.74, 59.21, 0.06173, 4, 195, -40.7, -24.53, 0.11579, 196, -39.75, 14.81, 0.34508, 197, -16.78, 40.31, 0.52984, 198, 5.57, 88.15, 0.00928, 4, 195, -65.9, -49.73, 0.00235, 196, -64.95, -10.39, 0.02809, 197, -41.98, 15.11, 0.7463, 198, -19.63, 62.95, 0.22326, 3, 197, -56.26, -9.67, 0.44102, 198, -33.91, 38.17, 0.55606, 199, -16.6, 97.81, 0.00293, 3, 197, -68.86, -44.95, 0.06338, 198, -46.51, 2.89, 0.78173, 199, -29.2, 62.53, 0.15489, 2, 198, -50.12, -31.13, 0.40175, 199, -32.81, 28.51, 0.59825, 2, 198, -50.12, -51.71, 0.14741, 199, -32.81, 7.93, 0.85259, 2, 198, -41.47, -66.83, 0.02635, 199, -24.16, -7.19, 0.97365, 1, 199, -11.14, -27.17, 1, 2, 198, -27.65, -60.28, 0.01897, 199, -10.35, -0.64, 0.98103, 2, 198, -20.63, -23.88, 0.58834, 199, -3.32, 35.77, 0.41166, 3, 197, -31.48, -25.74, 0.34344, 198, -9.13, 22.11, 0.65568, 199, 8.18, 81.75, 0.00088, 4, 195, -33.05, -57.37, 0.00456, 196, -32.11, -18.02, 0.06743, 197, -9.13, 7.47, 0.88699, 198, 13.22, 55.32, 0.04102, 3, 195, -13.25, -27.99, 0.21375, 196, -12.31, 11.36, 0.64752, 197, 10.67, 36.85, 0.13873, 3, 196, -14.22, -36.54, 0.07013, 197, 8.75, -11.05, 0.8004, 198, 31.1, 36.8, 0.12947, 4, 196, -28.91, -72.31, 0.00021, 197, -5.94, -46.81, 0.16559, 198, 16.41, 1.03, 0.80785, 199, 33.72, 60.67, 0.02635, 3, 197, -22.54, -89.6, 1e-05, 198, -0.19, -41.76, 0.36565, 199, 17.12, 17.88, 0.63434], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50], "width": 216, "height": 109}}, "qiongqi047": {"qiongqi047": {"type": "mesh", "uvs": [0.15591, 0, 0.37768, 0.10006, 0.56471, 0.22972, 0.73838, 0.34811, 0.96816, 0.26167, 1, 0.4477, 0.94144, 0.68259, 0.8025, 0.85923, 0.55135, 0.96258, 0.24675, 1, 0.12385, 0.9513, 0.05972, 0.72017, 0.0143, 0.45334, 0, 0.18838, 0.03033, 0.0249, 0.13762, 0.18425, 0.22344, 0.36813, 0.32722, 0.65868, 0.37113, 0.85238, 0.6206, 0.69096, 0.72838, 0.50568, 0.46693, 0.39761, 0.36913, 0.29093, 0.52481, 0.57306, 0.40107, 0.52253], "triangles": [8, 18, 19, 9, 18, 8, 21, 2, 3, 20, 21, 3, 24, 16, 21, 23, 24, 21, 20, 23, 21, 15, 14, 0, 15, 0, 1, 13, 14, 15, 22, 15, 1, 22, 1, 2, 16, 15, 22, 21, 22, 2, 16, 22, 21, 16, 12, 13, 16, 13, 15, 16, 17, 12, 3, 4, 5, 20, 3, 5, 6, 20, 5, 19, 23, 20, 19, 20, 6, 7, 19, 6, 7, 8, 19, 17, 24, 23, 24, 17, 16, 17, 23, 19, 18, 17, 19, 11, 12, 17, 11, 17, 18, 10, 11, 18, 9, 10, 18], "vertices": [1, 139, 31.12, -15.16, 1, 3, 142, 33.43, 36.41, 0.00109, 139, 14.34, -16.85, 0.99516, 138, 41.37, 4.62, 0.00375, 4, 141, 28.9, 30.34, 0.00034, 142, 23.5, 22.85, 0.10292, 139, -2.37, -15.06, 0.5504, 138, 31.85, -9.24, 0.34634, 5, 137, 32.51, -29.84, 0.00081, 141, 25.64, 15.21, 0.02508, 142, 14.46, 10.29, 0.65771, 139, -17.78, -13.53, 0.03088, 138, 23.2, -22.07, 0.28553, 1, 142, 24.38, -3.12, 1, 1, 142, 7.93, -7.6, 1, 2, 141, 6.69, -11.92, 0.98991, 8, -0.32, -35.96, 0.01009, 3, 137, -13.48, -21.73, 0.02057, 141, -11.66, -12.88, 0.65388, 8, -10.31, -20.54, 0.32554, 3, 140, -18.62, -13.98, 0.01113, 141, -28.14, -4.22, 0.08517, 8, -11.03, -1.93, 0.9037, 2, 140, -14.12, 5.29, 0.63962, 8, -4.84, 16.86, 0.36038, 2, 140, -6.96, 10.8, 0.86417, 8, 2.78, 21.71, 0.13583, 3, 140, 14, 6.34, 0.99986, 139, -4.82, 39.99, 1e-05, 138, -17.76, 14.75, 0.00013, 4, 140, 37.48, -0.49, 0.22176, 137, 35.28, 17.4, 0.0038, 139, 12.56, 22.78, 0.34598, 138, 5.64, 21.87, 0.42847, 3, 140, 60.02, -9.09, 0.00165, 139, 28.27, 4.47, 0.99177, 138, 29.21, 27, 0.00659, 1, 139, 36, -8.39, 1, 1, 139, 21.61, -1.3, 1, 3, 140, 39.37, -15.85, 0.03454, 139, 6.9, 8.39, 0.57928, 138, 15.62, 10.05, 0.38618, 2, 140, 12.44, -11.61, 0.12317, 137, 12.04, 2.91, 0.87683, 3, 140, -4.88, -7.29, 0.26644, 137, -5.71, 4.77, 0.16992, 8, 3.26, 3.51, 0.56364, 5, 137, 4.33, -14.46, 0.3239, 141, -4.83, 5.1, 0.61611, 142, -17.5, 13.2, 0.00151, 138, -8.84, -20.13, 0.01728, 8, 8.68, -17.49, 0.04121, 4, 137, 18.83, -25.5, 0.03422, 141, 13.13, 8.17, 0.4397, 142, 0.18, 8.83, 0.36472, 138, 8.97, -23.96, 0.16136, 5, 137, 32.67, -11.89, 0.00756, 141, 12.62, 27.57, 0.02565, 142, 7.48, 26.82, 0.06671, 139, -6.97, 0.8, 0.00962, 138, 15.71, -5.76, 0.89046, 3, 142, 16.17, 34.42, 0.00467, 139, 3.97, -2.91, 0.989, 138, 24.17, 2.11, 0.00633, 4, 137, 16.29, -11.32, 0.41355, 141, 1.03, 15.98, 0.26519, 142, -7.78, 20.83, 0.04117, 138, 0.65, -12.21, 0.28009, 4, 137, 19.8, -4.82, 0.47589, 141, -1.33, 22.98, 0.05349, 142, -7.14, 28.2, 0.01084, 138, 1.06, -4.82, 0.45979], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44], "width": 64, "height": 91}}, "qiongqi048": {"qiongqi048": {"type": "mesh", "uvs": [0.16775, 0.42677, 0.28362, 0.20222, 0.50971, 0.02258, 0.73297, 0, 0.8884, 0.18331, 1, 0.46931, 1, 0.73404, 0.84601, 0.8924, 0.66232, 1, 0.41927, 1, 0.17906, 0.89476, 0, 0.78131, 0, 0.61113, 0.55582, 0.24547, 0.43179, 0.48998, 0.29891, 0.71411, 0.75958, 0.47701, 0.67542, 0.70856, 0.50931, 0.85304], "triangles": [0, 1, 14, 14, 1, 13, 16, 4, 5, 16, 13, 4, 1, 2, 13, 13, 3, 4, 13, 2, 3, 15, 14, 17, 17, 14, 16, 14, 13, 16, 10, 15, 18, 10, 11, 15, 11, 12, 15, 12, 0, 15, 15, 0, 14, 9, 18, 8, 8, 18, 7, 9, 10, 18, 18, 17, 7, 7, 17, 6, 18, 15, 17, 17, 16, 6, 16, 5, 6], "vertices": [4, 125, 27.18, -22.74, 0.00048, 126, 17.43, -18.47, 0.33122, 127, 23.61, 9.02, 0.21063, 128, -4.93, 12.77, 0.45767, 4, 125, 19.2, -33.56, 5e-05, 126, 12.7, -31.06, 0.00851, 127, 28.57, -3.48, 0.0003, 128, 8.52, 13.13, 0.99115, 1, 128, 21.86, 7.85, 1, 3, 125, -3.45, -39.73, 0, 127, 21.82, -25.96, 0.00086, 128, 27.3, -0.95, 0.99914, 3, 125, -8.14, -28.29, 0.00831, 127, 9.64, -23.89, 0.13498, 128, 21.16, -11.68, 0.8567, 3, 125, -9.59, -11.81, 0.29403, 127, -5.11, -16.4, 0.4585, 128, 9.05, -22.95, 0.24747, 3, 125, -6.3, 2.37, 0.97691, 127, -15.41, -6.1, 0.01887, 128, -4.16, -29.07, 0.00422, 1, 125, 2.57, 9.26, 1, 1, 125, 12.14, 13.11, 1, 2, 125, 23.03, 10.59, 0.82074, 126, 4.33, 12.46, 0.17926, 2, 125, 32.49, 2.45, 0.07501, 126, 15.65, 7.22, 0.92499, 3, 126, 24.18, 1.39, 0.9946, 127, 15.28, 28.27, 0.00356, 128, -25.86, 11.57, 0.00185, 4, 125, 36.99, -14.6, 0.0003, 126, 24.64, -7.96, 0.85196, 127, 21.9, 21.65, 0.09302, 128, -17.37, 15.51, 0.05472, 2, 125, 7.54, -28.42, 0, 128, 11.63, 0.77, 1, 4, 125, 16.13, -16.61, 4e-05, 126, 5.12, -15.59, 0.10058, 127, 12.56, 2.89, 0.87418, 128, -2.97, 0.29, 0.0252, 4, 125, 24.87, -5.98, 0.00156, 126, 10.63, -2.98, 0.91414, 127, 8.17, 15.93, 0.08097, 128, -16.73, 0.65, 0.00333, 3, 125, 1.28, -13.9, 0.07343, 127, 2.41, -8.28, 0.67944, 128, 4.01, -13.09, 0.24712, 3, 125, 7.93, -2.36, 0.74326, 126, -6.65, -4.13, 0.02516, 127, -3.86, 3.47, 0.23158, 2, 125, 17.17, 3.65, 0.98267, 126, 0.59, 4.18, 0.01733], "hull": 13, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 0, 24, 4, 26, 26, 28, 28, 30, 26, 32, 32, 34, 34, 36], "width": 46, "height": 55}}, "qiongqi049": {"qiongqi049": {"type": "mesh", "uvs": [0.12491, 0, 0.26636, 0, 0.46282, 0, 0.63571, 0, 0.80859, 0.06309, 0.87408, 0.18029, 0.88718, 0.28697, 1, 0.38614, 1, 0.50934, 1, 0.64457, 0.96052, 0.75125, 0.84002, 0.8384, 0.73525, 0.93006, 0.675, 1, 0.54926, 1, 0.36852, 1, 0.46282, 0.90752, 0.54141, 0.80384, 0.55974, 0.68063, 0.65928, 0.601, 0.6619, 0.50183, 0.44711, 0.49282, 0.25588, 0.42821, 0.03847, 0.39966, 0, 0.35759, 0, 0.25842, 0.05157, 0.17127, 0.02275, 0.05708, 0.60162, 0.91666, 0.69532, 0.82382, 0.75779, 0.71633, 0.81174, 0.61047, 0.82877, 0.47366, 0.73791, 0.3564, 0.65557, 0.21796, 0.50225, 0.09744, 0.20128, 0.16421, 0.34324, 0.25705, 0.51076, 0.345, 0.6215, 0.42969, 0.2609, 0.05346], "triangles": [12, 29, 11, 28, 17, 29, 28, 29, 12, 15, 16, 14, 14, 28, 13, 14, 16, 28, 13, 28, 12, 16, 17, 28, 39, 33, 32, 21, 38, 39, 20, 39, 32, 21, 39, 20, 32, 7, 8, 31, 20, 32, 31, 32, 8, 19, 20, 31, 31, 8, 9, 30, 19, 31, 30, 31, 9, 18, 19, 30, 10, 30, 9, 17, 18, 30, 29, 17, 30, 11, 30, 10, 29, 30, 11, 40, 0, 1, 35, 2, 3, 40, 27, 0, 36, 27, 40, 26, 27, 36, 4, 35, 3, 34, 4, 5, 34, 35, 4, 40, 2, 35, 2, 40, 1, 37, 35, 34, 35, 36, 40, 37, 36, 35, 34, 5, 6, 38, 37, 34, 33, 34, 6, 38, 34, 33, 37, 23, 24, 33, 6, 7, 24, 25, 37, 36, 25, 26, 22, 37, 38, 25, 36, 37, 22, 23, 37, 39, 38, 33, 32, 33, 7, 21, 22, 38], "vertices": [1, 5, 96, 14.93, 1, 1, 5, 89.74, 0.22, 1, 1, 5, 81.04, -20.2, 1, 1, 5, 73.38, -38.17, 1, 1, 5, 54.29, -51.28, 1, 2, 5, 30.15, -49.04, 0.99639, 4, 109.64, -13.48, 0.00361, 2, 5, 10.23, -42.16, 0.88104, 4, 90.68, -22.66, 0.11896, 2, 5, -12.73, -46.24, 0.53322, 4, 77.27, -41.75, 0.46678, 2, 5, -35.07, -36.73, 0.18958, 4, 54.74, -50.77, 0.81042, 2, 5, -59.58, -26.29, 0.01422, 4, 30, -60.66, 0.98578, 1, 4, 8.83, -64.33, 1, 2, 4, -12.16, -58.06, 0.59179, 10, -26.9, 54.04, 0.40821, 1, 10, -6.47, 61, 1, 1, 10, 7.37, 67.69, 1, 1, 10, 18.58, 58.96, 1, 1, 10, 34.69, 46.41, 1, 1, 10, 15.09, 38.58, 1, 2, 4, -18.37, -24.2, 0.6732, 10, -4.47, 27.93, 0.3268, 1, 4, 4.93, -17.11, 1, 2, 5, -36.59, 5.77, 0.00124, 4, 23.68, -21.73, 0.99876, 2, 5, -18.73, -2.16, 0.0617, 4, 41.92, -14.75, 0.9383, 2, 5, -7.59, 19.48, 0.11882, 4, 34.56, 8.45, 0.88118, 2, 5, 12.59, 34.37, 0.75513, 4, 38.35, 33.24, 0.24487, 2, 5, 27.39, 54.77, 0.92306, 4, 34.45, 58.14, 0.07694, 2, 5, 36.72, 55.52, 0.93785, 4, 40.53, 65.25, 0.06215, 2, 5, 54.69, 47.86, 0.97329, 4, 58.67, 72.51, 0.02671, 2, 5, 68.2, 35.78, 0.99517, 4, 76.77, 73.47, 0.00483, 1, 5, 90.18, 29.96, 1, 1, 10, 3.82, 49.64, 1, 2, 4, -15.57, -41.81, 0.59484, 10, -15.77, 41.72, 0.40516, 1, 4, 6.71, -40.5, 1, 2, 5, -45.06, -9.35, 0.01415, 4, 28.34, -38.41, 0.98585, 2, 5, -21.02, -21.68, 0.25927, 4, 54.08, -30.19, 0.74073, 2, 5, 4.26, -21.29, 0.83237, 4, 71.72, -12.08, 0.16763, 2, 5, 33, -23.41, 0.99979, 4, 93.58, 6.69, 0.00021, 1, 5, 61.63, -16.78, 1, 2, 5, 62.86, 19.67, 0.99812, 4, 84.35, 58.28, 0.00188, 2, 5, 39.74, 12.07, 0.98728, 4, 73.32, 36.6, 0.01272, 2, 5, 16.39, 1.45, 0.99203, 4, 64.27, 12.59, 0.00797, 2, 5, -3.87, -3.53, 0.33614, 4, 53.42, -5.23, 0.66386, 1, 5, 80.29, 4.92, 1], "hull": 28, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 0, 54, 28, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 72, 74, 74, 76, 76, 78], "width": 113, "height": 197}}, "qiongqi024": {"qiongqi024": {"type": "mesh", "uvs": [0, 0.67461, 0.07498, 0.35684, 0.23371, 0.09684, 0.40477, 0, 0.63192, 0, 0.8123, 0.12284, 0.95872, 0.28173, 1, 0.43773, 1, 0.57928, 0.91903, 0.84795, 0.7603, 0.9635, 0.59198, 1, 0.43735, 0.9895, 0.2964, 0.81328, 0.13903, 0.7295, 0.1303, 0.55002, 0.29561, 0.51581, 0.47388, 0.62529, 0.69267, 0.64582, 0.85311, 0.51923, 0.66187, 0.35501, 0.40905, 0.28658, 0.28102, 0.33106], "triangles": [20, 4, 5, 19, 5, 6, 19, 6, 7, 20, 5, 19, 19, 7, 8, 18, 20, 19, 17, 20, 18, 9, 19, 8, 10, 18, 19, 10, 19, 9, 11, 17, 18, 11, 18, 10, 21, 3, 4, 21, 22, 3, 21, 4, 20, 17, 21, 20, 16, 21, 17, 12, 13, 17, 12, 17, 11, 22, 2, 3, 1, 2, 22, 16, 22, 21, 15, 1, 22, 15, 22, 16, 0, 1, 15, 14, 15, 16, 0, 15, 14, 13, 16, 17, 14, 16, 13], "vertices": [1, 134, -10.69, -2.57, 1, 1, 134, -2.15, 6.93, 1, 2, 134, 11.97, 12.74, 0.9092, 135, -10.54, 8.91, 0.0908, 3, 134, 25.43, 12.64, 0.19614, 135, 1.47, 14.98, 0.79363, 136, -18.69, 15.22, 0.01023, 2, 135, 18.37, 18.51, 0.56657, 136, -1.74, 18.53, 0.43343, 2, 135, 32.7, 16.98, 0.06763, 136, 12.56, 16.82, 0.93237, 1, 136, 24.58, 13.34, 1, 1, 136, 28.73, 8.43, 1, 1, 136, 29.71, 3.43, 1, 1, 136, 25.53, -7.25, 1, 2, 135, 35.01, -13.45, 0.02836, 136, 14.48, -13.64, 0.97164, 3, 134, 29.56, -25.85, 0.02392, 135, 22.76, -17.36, 0.40205, 136, 2.18, -17.38, 0.57403, 3, 134, 18.33, -22.35, 0.17324, 135, 11.18, -19.39, 0.68378, 136, -9.43, -19.27, 0.14298, 3, 134, 9.69, -13.39, 0.66434, 135, -0.6, -15.37, 0.33072, 136, -21.16, -15.1, 0.00494, 2, 134, -1.03, -7.29, 0.9951, 135, -12.93, -14.86, 0.0049, 1, 134, 0.05, -0.89, 1, 2, 134, 12.49, -3.05, 0.91984, 135, -2.85, -4.9, 0.08016, 3, 134, 24.5, -10.46, 0.0671, 135, 11.22, -5.98, 0.89818, 136, -9.22, -5.87, 0.03472, 2, 135, 27.64, -3.31, 0.01263, 136, 7.25, -3.4, 0.98737, 1, 136, 18.34, 3.41, 1, 2, 135, 23.21, 6.46, 0.21969, 136, 2.94, 6.42, 0.78031, 3, 134, 23, 2.61, 0.06229, 135, 3.9, 4.94, 0.93735, 136, -16.39, 5.16, 0.00036, 1, 134, 13.19, 3.66, 1], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28, 0, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44], "width": 76, "height": 36}}, "qiongqi055": {"qiongqi055": {"type": "mesh", "uvs": [0, 0.88946, 0, 1, 0.18369, 1, 0.32049, 0.78801, 0.65409, 0.74546, 0.86769, 0.52946, 1, 0.2251, 1, 0, 0.84129, 0, 0.52449, 0.08438, 0.24369, 0.32656, 0.07089, 0.61128, 0.10039, 0.86072, 0.26381, 0.63069, 0.4773, 0.45458, 0.73296, 0.318, 0.91746, 0.11314], "triangles": [3, 14, 4, 4, 15, 5, 4, 14, 15, 6, 15, 16, 6, 5, 15, 14, 9, 15, 14, 10, 9, 15, 8, 16, 15, 9, 8, 16, 7, 6, 16, 8, 7, 1, 12, 2, 2, 12, 3, 1, 0, 12, 0, 11, 12, 12, 13, 3, 12, 11, 13, 3, 13, 14, 11, 10, 13, 13, 10, 14], "vertices": [1, 182, -3.75, -1.15, 1, 1, 182, -4.92, -3.28, 1, 1, 182, -0.09, -5.94, 1, 2, 182, 5.75, -3.83, 0.97968, 183, -6.36, -3.39, 0.02032, 2, 182, 14.97, -7.84, 0.04674, 183, 2.55, -8.04, 0.95326, 1, 183, 10.52, -7.53, 1, 1, 183, 17.48, -4.06, 1, 1, 183, 20.17, 0.1, 1, 1, 183, 16.17, 2.69, 1, 2, 182, 18.58, 6.77, 0.00044, 183, 7.18, 6.29, 0.99956, 2, 182, 8.63, 6.17, 0.70432, 183, -2.78, 6.38, 0.29568, 2, 182, 1.07, 3.19, 0.99999, 183, -10.54, 3.94, 1e-05, 1, 182, -0.8, -2.05, 1, 2, 182, 5.93, 0.02, 0.99995, 183, -5.91, 0.44, 5e-05, 2, 182, 13.41, 0.32, 0.00609, 183, 1.57, 0.21, 0.99391, 1, 183, 9.65, -1.43, 1, 1, 183, 16.74, -0.64, 1], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 0, 22, 2, 24, 24, 26, 26, 28, 28, 30, 30, 32], "width": 30, "height": 22}}, "qiongqi019": {"qiongqi019": {"type": "mesh", "uvs": [0.75838, 0.41351, 0.91474, 0.38504, 1, 0.29149, 1, 0.1749, 0.89659, 0.06779, 0.66624, 0.00407, 0.43171, 0, 0.22928, 0.04745, 0.05395, 0.17397, 0, 0.35541, 0, 0.56464, 0.0792, 0.73628, 0.24751, 0.85397, 0.47979, 0.92916, 0.65484, 0.97329, 0.78444, 1, 0.82316, 0.9782, 0.83157, 0.91608, 0.68009, 0.84743, 0.49662, 0.79839, 0.31989, 0.71176, 0.21217, 0.60878, 0.17514, 0.49926, 0.25593, 0.35214, 0.41078, 0.30637, 0.55385, 0.3358, 0.83296, 0.23665, 0.62236, 0.1678, 0.40967, 0.1435, 0.23035, 0.2002, 0.10732, 0.32575, 0.09064, 0.47155, 0.11775, 0.61736, 0.21367, 0.73076, 0.3492, 0.80163, 0.50559, 0.85631, 0.66406, 0.90896], "triangles": [16, 15, 36, 15, 14, 36, 16, 36, 17, 14, 13, 36, 36, 13, 35, 12, 34, 13, 13, 34, 35, 36, 18, 17, 36, 35, 18, 34, 19, 35, 35, 19, 18, 34, 20, 19, 11, 33, 12, 12, 33, 34, 33, 20, 34, 11, 32, 33, 11, 10, 32, 32, 21, 33, 33, 21, 20, 32, 22, 21, 10, 31, 32, 32, 31, 22, 10, 9, 31, 23, 22, 30, 22, 31, 30, 31, 9, 30, 9, 8, 30, 30, 29, 23, 30, 8, 29, 23, 29, 24, 29, 28, 24, 25, 24, 28, 8, 7, 29, 29, 7, 28, 28, 6, 27, 28, 7, 6, 25, 27, 0, 0, 26, 1, 0, 27, 26, 1, 26, 2, 27, 25, 28, 26, 3, 2, 26, 4, 3, 4, 26, 5, 26, 27, 5, 27, 6, 5], "vertices": [2, 36, 12.97, 39.51, 0.99991, 37, -33.95, 62.79, 9e-05, 1, 36, -16.19, 26.02, 1, 1, 36, -28.12, 2.93, 1, 1, 36, -22.17, -20.58, 1, 1, 36, 3.55, -37.05, 1, 2, 36, 51.91, -38.48, 0.9617, 37, -36.17, -24.35, 0.0383, 2, 36, 98.05, -27.68, 0.11574, 37, 9.64, -36.47, 0.88426, 1, 37, 51.7, -36.63, 1, 2, 37, 92.37, -19.51, 0.35848, 38, -8.71, -26.27, 0.64152, 1, 38, 30.5, -28.62, 1, 2, 38, 72.96, -19.06, 0.63618, 39, -11.47, -16.31, 0.36382, 1, 39, 26.23, -26.73, 1, 2, 39, 66.77, -16.13, 0.9462, 40, -12.9, -10.73, 0.0538, 1, 40, 36.52, -12.62, 1, 1, 40, 73.04, -11.54, 1, 1, 40, 99.72, -9.54, 1, 1, 40, 105.96, -3, 1, 1, 40, 103.98, 9.88, 1, 1, 40, 70.61, 15.02, 1, 2, 39, 89.92, 30.02, 0.00151, 40, 32.17, 14.45, 0.99849, 2, 39, 53.28, 13.99, 0.93002, 40, -7.14, 21.76, 0.06998, 2, 38, 72.5, 24.77, 0.01822, 39, 22.91, 10.87, 0.98178, 2, 38, 51.92, 12.47, 0.85341, 39, 0.59, 19.62, 0.14659, 2, 37, 61.58, 26.2, 0.38912, 38, 18.48, 21.67, 0.61088, 3, 36, 86.51, 35.14, 0.07187, 37, 28.93, 24.41, 0.91686, 38, 2.32, 50.09, 0.01127, 2, 36, 56.99, 33.98, 0.71766, 37, 2.32, 37.25, 0.28234, 1, 36, 7.39, 0.15, 1, 1, 36, 52.15, -3.29, 1, 1, 37, 21.07, -8.54, 1, 1, 37, 59.07, -5.72, 1, 1, 38, 19.72, -8.82, 1, 1, 38, 50.05, -5.45, 1, 1, 39, 12.13, -4.97, 1, 1, 39, 42.66, -5.07, 1, 2, 39, 71.47, 6.64, 0.29952, 40, 3.77, 5.47, 0.70048, 1, 40, 37.28, 3.39, 1, 1, 40, 71.08, 1.83, 1], "hull": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 0, 50, 4, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 32], "width": 202, "height": 208}}, "qiongqi050": {"qiongqi050": {"type": "mesh", "uvs": [0.41127, 0.02524, 0.4867, 0, 0.63984, 0.08035, 0.69241, 0.24568, 0.62156, 0.41279, 0.51413, 0.50346, 0.34499, 0.56035, 0.21013, 0.65279, 0.40442, 0.67413, 0.66041, 0.6919, 0.84784, 0.71324, 1, 0.75235, 0.92099, 0.88213, 0.75184, 1, 0.55299, 0.96568, 0.34499, 0.92657, 0.1347, 0.88924, 0, 0.83946, 0, 0.72213, 0, 0.60124, 0.10727, 0.48035, 0.21013, 0.36124, 0.34956, 0.27235, 0.46841, 0.21724, 0.50041, 0.14079, 0.5733, 0.11942, 0.57153, 0.24159, 0.49211, 0.3418, 0.41092, 0.42142, 0.28384, 0.46673, 0.15676, 0.56556, 0.07204, 0.67538, 0.1497, 0.77148, 0.36679, 0.79619, 0.60154, 0.83325, 0.78333, 0.85247], "triangles": [14, 34, 13, 13, 35, 12, 13, 34, 35, 14, 15, 34, 15, 33, 34, 15, 16, 33, 35, 10, 12, 12, 10, 11, 34, 9, 35, 35, 9, 10, 33, 8, 34, 34, 8, 9, 33, 7, 8, 16, 32, 33, 16, 17, 32, 17, 18, 32, 32, 7, 33, 18, 31, 32, 32, 31, 7, 18, 19, 31, 31, 30, 7, 31, 19, 30, 7, 30, 6, 19, 20, 30, 30, 29, 6, 30, 20, 29, 6, 28, 5, 6, 29, 28, 28, 27, 5, 5, 27, 4, 20, 21, 29, 29, 21, 28, 21, 22, 28, 28, 22, 27, 27, 26, 4, 22, 23, 27, 4, 26, 3, 27, 23, 26, 26, 25, 3, 25, 2, 3, 23, 24, 26, 26, 24, 25, 24, 0, 25, 0, 1, 25, 25, 1, 2], "vertices": [1, 94, -7.07, -7.78, 1, 1, 94, -8.97, -3.59, 1, 1, 94, -3.35, 5.09, 1, 1, 94, 8.5, 8.26, 1, 2, 94, 20.6, 4.53, 0.15615, 95, -0.94, 6.64, 0.84385, 1, 95, 7.67, 8.79, 1, 2, 95, 17.83, 7.02, 0.83811, 96, 1.13, 9.36, 0.16189, 3, 95, 27.8, 8.43, 0.01128, 96, 8.76, 2.8, 0.92619, 97, 1.94, 12.89, 0.06253, 2, 96, 8.79, 13.78, 0.19565, 97, 12.92, 13.37, 0.80435, 2, 96, 8.08, 28.16, 0.00355, 97, 27.25, 14.75, 0.99645, 1, 97, 37.85, 15.16, 1, 1, 97, 46.74, 13.96, 1, 1, 97, 44.1, 3.96, 1, 1, 97, 36.35, -6.12, 1, 1, 97, 24.95, -5.74, 1, 1, 97, 12.98, -5.1, 1, 2, 96, 26.2, 0.95, 0.80342, 97, 0.91, -4.62, 0.19658, 1, 96, 23.69, -7.01, 1, 1, 96, 15.32, -8.17, 1, 1, 96, 6.7, -9.37, 1, 2, 95, 25.8, -5.11, 0.53915, 96, -2.74, -4.62, 0.46085, 2, 94, 17.33, -18.58, 0.01247, 95, 16.28, -9.11, 0.98753, 2, 94, 10.79, -10.9, 0.31691, 95, 6.24, -10.18, 0.68309, 2, 94, 6.69, -4.32, 0.91512, 95, -1.5, -9.84, 0.08488, 1, 94, 1.15, -2.63, 1, 1, 94, -0.46, 1.42, 1, 1, 94, 8.33, 1.49, 1, 2, 94, 15.63, -2.82, 0.23766, 95, 2.31, -1.61, 0.76234, 1, 95, 9.26, 0.68, 1, 2, 94, 24.85, -14.31, 0, 95, 17, -0.5, 1, 1, 96, 2.95, -1.03, 1, 1, 96, 11.44, -4.64, 1, 2, 96, 17.69, 0.62, 0.93377, 97, 0.18, 3.87, 0.06623, 2, 96, 17.78, 12.91, 0.06304, 97, 12.46, 4.35, 0.93696, 2, 96, 18.62, 26.29, 0.00077, 97, 25.87, 4.14, 0.99923, 1, 97, 36.13, 4.64, 1], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48, 0, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70], "width": 56, "height": 72}}, "qiongqi016": {"qiongqi016": {"type": "mesh", "uvs": [0, 0.91365, 0, 1, 0.06606, 1, 0.16007, 0.91222, 0.25638, 0.82642, 0.3332, 0.70915, 0.4169, 0.63192, 0.55563, 0.67196, 0.7173, 0.68484, 0.84278, 0.61485, 0.90992, 0.48352, 0.95417, 0.29891, 0.89771, 0.12001, 0.77564, 0, 0.62458, 0, 0.45979, 0.06862, 0.34535, 0.22088, 0.27669, 0.38836, 0.20345, 0.56346, 0.118, 0.71381, 0.03865, 0.8261, 0.09872, 0.86988, 0.19245, 0.75297, 0.28617, 0.6019, 0.3626, 0.4688, 0.50247, 0.33031, 0.65675, 0.23499, 0.82258, 0.26557, 0.74039, 0.43463, 0.53419, 0.51017], "triangles": [23, 18, 17, 5, 23, 6, 22, 19, 18, 22, 18, 23, 22, 23, 5, 4, 22, 5, 21, 20, 19, 21, 19, 22, 3, 21, 22, 3, 22, 4, 0, 20, 21, 1, 0, 21, 2, 1, 21, 2, 21, 3, 16, 15, 25, 24, 17, 16, 25, 24, 16, 28, 29, 25, 24, 25, 29, 24, 23, 17, 6, 24, 29, 23, 24, 6, 8, 7, 29, 6, 29, 7, 26, 14, 13, 27, 26, 13, 15, 14, 26, 12, 27, 13, 27, 12, 11, 25, 15, 26, 28, 26, 27, 10, 27, 11, 28, 27, 10, 28, 25, 26, 9, 28, 10, 28, 8, 29, 9, 8, 28], "vertices": [1, 112, 50.53, -1.75, 1, 1, 112, 56.47, 3.65, 1, 1, 112, 51.32, 9.32, 1, 1, 112, 37.94, 11.9, 1, 2, 111, 54.71, 19.81, 0.00019, 112, 24.52, 14.8, 0.99981, 3, 110, 61.78, 30.83, 6e-05, 111, 41.15, 16.01, 0.14364, 112, 10.46, 14.06, 0.85629, 3, 110, 50.78, 25.84, 0.02919, 111, 29.08, 15.71, 0.73878, 112, -1.39, 16.41, 0.23203, 3, 110, 35.82, 32.84, 0.28058, 111, 18.05, 28.01, 0.71741, 112, -9.46, 30.82, 0.002, 2, 110, 17.73, 37.92, 0.62663, 111, 3.39, 39.76, 0.37337, 2, 110, 2.14, 34.6, 0.81955, 111, -12.26, 42.81, 0.18045, 2, 110, -8.03, 24.28, 0.9348, 111, -25.65, 37.29, 0.0652, 2, 110, -16.63, 8.56, 0.99743, 111, -39.72, 26.19, 0.00257, 1, 110, -13.7, -9.08, 1, 1, 110, -2.18, -22.95, 1, 2, 110, 14.95, -26.61, 0.97946, 111, -24.41, -18.53, 0.02054, 2, 110, 34.98, -24.36, 0.60823, 111, -5.1, -24.3, 0.39177, 2, 110, 50.92, -13.28, 0.06926, 111, 13.9, -20.34, 0.93074, 2, 111, 29.37, -12.17, 0.9484, 112, -7.21, -10.86, 0.0516, 1, 112, 10.56, -6.19, 1, 1, 112, 27.57, -4.12, 1, 1, 112, 41.49, -3.91, 1, 1, 112, 39.81, 3.99, 1, 1, 112, 24.46, 4.72, 1, 2, 111, 39.88, 4.71, 0.06474, 112, 6.75, 3.31, 0.93526, 1, 111, 25.52, -0.32, 1, 1, 111, 4.84, -1.54, 1, 1, 110, 15.87, -4.46, 1, 2, 110, -2.35, 2.34, 0.99868, 111, -29.01, 14.88, 0.00132, 2, 110, 10.25, 15.73, 0.87294, 111, -12.17, 22.26, 0.12706, 3, 110, 35.11, 17.6, 0.2021, 111, 11.44, 14.26, 0.79734, 112, -18.92, 18.86, 0.00055], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 2, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58], "width": 116, "height": 93}}, "qiongqi052": {"qiongqi052": {"type": "mesh", "uvs": [0, 1, 0.22983, 1, 0.62883, 1, 0.83941, 0.73624, 0.92808, 0.46264, 1, 0.23085, 1, 0, 0.69164, 0.07885, 0.3185, 0.26885, 0.04141, 0.53484, 0, 0.76664, 0.80568, 0.19951, 0.55795, 0.40935, 0.31022, 0.66716, 0.15867, 0.84703], "triangles": [3, 12, 4, 13, 8, 12, 12, 11, 4, 4, 11, 5, 12, 7, 11, 12, 8, 7, 11, 6, 5, 11, 7, 6, 1, 13, 2, 2, 13, 3, 0, 14, 1, 1, 14, 13, 0, 10, 14, 14, 10, 13, 10, 9, 13, 13, 12, 3, 13, 9, 8], "vertices": [1, 90, -3.96, -1.3, 1, 1, 90, 2.27, -6.75, 1, 2, 90, 13.08, -16.21, 0.98957, 91, -5.3, -16.44, 0.01043, 2, 90, 24.86, -14.26, 0.62371, 91, 6.46, -14.36, 0.37629, 2, 90, 33.57, -9.15, 0.11181, 91, 15.11, -9.17, 0.88819, 2, 90, 40.86, -4.75, 0.00124, 91, 22.36, -4.69, 0.99876, 1, 91, 27.62, 1.44, 1, 1, 91, 17.39, 6.57, 1, 2, 90, 21.52, 10.4, 0.01107, 91, 2.87, 10.26, 0.98893, 2, 90, 7.89, 9.97, 0.77291, 91, -10.77, 9.69, 0.22709, 2, 90, 1.42, 4.84, 0.98545, 91, -17.18, 4.5, 0.01455, 1, 91, 17.76, 0.69, 1, 1, 91, 6.21, 0.92, 1, 2, 90, 12.12, 0.11, 0.99794, 91, -6.43, -0.13, 0.00206, 1, 90, 3.87, -1.04, 1], "hull": 11, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20, 12, 22, 22, 24, 24, 26, 26, 28], "width": 36, "height": 35}}, "qiongqi053": {"qiongqi053": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 92, 8.86, -14.33, 1, 1, 92, -6.09, -1.04, 1, 1, 92, 7.86, 14.65, 1, 1, 92, 22.81, 1.37, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 20, "height": 21}}, "qiongqi010": {"qiongqi010": {"type": "mesh", "uvs": [0.07601, 0, 0.54558, 0.00655, 1, 0.3502, 1, 0.71698, 1, 1, 0.64329, 1, 0.23344, 0.72028, 0.00544, 0.34359, 0.39183, 0.30738, 0.73028, 0.64905], "triangles": [6, 8, 9, 6, 7, 8, 7, 0, 8, 8, 0, 1, 5, 9, 4, 9, 3, 4, 5, 6, 9, 9, 2, 3, 8, 1, 9, 9, 1, 2], "vertices": [2, 160, 25.54, -5.58, 0.00271, 161, 15.81, -2.95, 0.99729, 2, 160, 13.8, -11.51, 0.54251, 161, 5.33, -10.89, 0.45749, 1, 160, -1.13, -10.36, 1, 1, 160, -5.02, -2.87, 1, 1, 160, -8.02, 2.91, 1, 2, 160, 0.84, 7.51, 0.94988, 161, -10.84, 5.49, 0.05012, 2, 160, 13.99, 7.09, 0.03253, 161, 2.17, 7.44, 0.96747, 1, 161, 12.53, 4.5, 1, 2, 160, 14.43, -3.38, 0.2152, 161, 4.49, -2.78, 0.7848, 1, 160, 2.4, -0.78, 1], "hull": 8, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 0, 14, 14, 16, 16, 18], "width": 28, "height": 23}}, "qiongqi011": {"qiongqi011": {"type": "mesh", "uvs": [0.48077, 0, 0.72452, 0.08982, 1, 0.15649, 1, 0.44315, 1, 0.78315, 1, 1, 0.53764, 1, 0.04202, 0.88982, 0, 0.58649, 0, 0.24649, 0, 0.03982, 0.3472, 0.25642, 0.56959, 0.6163, 0.5943, 0.83425], "triangles": [3, 12, 11, 8, 9, 11, 3, 11, 2, 11, 1, 2, 1, 11, 0, 0, 11, 10, 11, 9, 10, 6, 13, 5, 13, 4, 5, 6, 7, 13, 13, 8, 12, 13, 7, 8, 13, 12, 4, 12, 3, 4, 12, 8, 11], "vertices": [1, 153, 19.45, 0.83, 1, 1, 153, 16.25, -3.32, 1, 1, 153, 13.99, -7.91, 1, 2, 152, 13.56, -9.33, 0.12209, 153, 2.84, -8.74, 0.87791, 2, 152, 0.68, -6.19, 0.98037, 153, -10.39, -9.74, 0.01963, 1, 152, -7.54, -4.19, 1, 1, 152, -5.79, 3, 1, 1, 152, 0.26, 9.69, 1, 2, 152, 11.91, 7.54, 0.99773, 153, -3.93, 6.79, 0.00227, 2, 152, 24.8, 4.41, 0.08569, 153, 9.29, 7.79, 0.91431, 2, 152, 32.63, 2.5, 0.00012, 153, 17.33, 8.39, 0.99988, 2, 152, 23.11, -0.9, 0.02155, 153, 9.32, 2.22, 0.97845, 2, 152, 8.63, -1.04, 0.95224, 153, -4.41, -2.38, 0.04776, 1, 152, 0.28, 0.59, 1], "hull": 11, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20, 0, 22, 22, 24, 24, 26], "width": 16, "height": 39}}, "qiongqi01": {"qiongqi01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [117.03, -127.47, -122.96, -125.5, -120.85, 132.49, 119.15, 130.52], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 240, "height": 258}}, "qiongqi013": {"qiongqi013": {"type": "mesh", "uvs": [0.44906, 0, 0.73754, 0.02296, 0.92421, 0.18808, 1, 0.27185, 1, 0.50159, 0.9327, 0.72176, 0.76583, 0.92039, 0.56219, 1, 0.39815, 1, 0.21431, 0.89646, 0.12381, 0.71697, 0, 0.54227, 0, 0.25031, 0.20583, 0.01817, 0.53602, 0.22677, 0.55138, 0.4997, 0.52988, 0.75703, 0.44694, 0.8896], "triangles": [8, 17, 7, 8, 9, 17, 7, 17, 16, 6, 7, 16, 14, 0, 1, 14, 1, 2, 15, 14, 2, 15, 2, 3, 15, 3, 4, 15, 11, 12, 15, 10, 11, 5, 15, 4, 13, 14, 15, 14, 13, 0, 16, 15, 5, 12, 13, 15, 16, 10, 15, 6, 16, 5, 17, 10, 16, 9, 10, 17], "vertices": [1, 41, -32.04, -4.12, 1, 1, 41, -28.68, 24.37, 1, 1, 41, -8.94, 42.4, 1, 1, 41, 1.04, 49.67, 1, 1, 41, 27.91, 49.04, 1, 1, 41, 53.51, 41.77, 1, 2, 41, 76.35, 24.72, 0.48932, 42, -27.51, -7.27, 0.51068, 2, 41, 85.19, 4.34, 0.13976, 42, -12.39, 9, 0.86024, 2, 41, 84.81, -11.89, 0.10656, 42, 2.63, 15.16, 0.89344, 2, 41, 72.28, -29.8, 0.43031, 42, 24.07, 10.86, 0.56969, 2, 41, 51.07, -38.27, 0.89811, 42, 40.33, -5.16, 0.10189, 1, 41, 30.35, -50.05, 1, 1, 41, -3.8, -49.25, 1, 1, 41, -30.47, -28.24, 1, 1, 41, -5.31, 3.87, 1, 1, 41, 26.65, 4.64, 1, 2, 41, 56.7, 1.81, 0.86256, 42, 1.36, -16.09, 0.13744, 2, 41, 72.01, -6.76, 0.43618, 42, 3.07, 1.38, 0.56382], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26, 0, 28, 28, 30, 30, 32, 32, 20, 32, 34], "width": 99, "height": 117}}, "qiongqi014": {"qiongqi014": {"type": "mesh", "uvs": [0, 0.40258, 0.16373, 0.21858, 0.46047, 0.10258, 0.81591, 0.04058, 0.78656, 0.25258, 0.86808, 0.51458, 1, 0.69258, 0.87786, 0.78658, 0.64634, 0.90457, 0.51917, 1, 0.37569, 1, 0.38873, 0.82058, 0.37895, 0.60258, 0.26156, 0.47858, 0.52915, 0.37315, 0.63823, 0.57082, 0.57377, 0.7685, 0.48948, 0.9084, 0.81178, 0.65294, 0.64319, 0.18763], "triangles": [10, 17, 9, 9, 17, 8, 10, 11, 17, 8, 17, 16, 17, 11, 16, 8, 16, 7, 11, 12, 16, 16, 18, 7, 7, 18, 6, 16, 15, 18, 16, 12, 15, 18, 5, 6, 18, 15, 5, 15, 12, 14, 12, 13, 14, 15, 14, 5, 14, 4, 5, 14, 13, 1, 13, 0, 1, 1, 2, 14, 14, 19, 4, 14, 2, 19, 4, 19, 3, 19, 2, 3], "vertices": [1, 116, 8.98, -23.94, 1, 1, 116, -3.32, -14.14, 1, 1, 116, -9.55, 0.8, 1, 1, 116, -11.32, 17.7, 1, 1, 116, 4.11, 13.64, 1, 2, 116, 24.12, 13.96, 0.68782, 117, -7.69, 12.56, 0.31218, 2, 116, 38.31, 17.64, 0.08082, 117, 5.32, 19.32, 0.91918, 2, 116, 44.29, 10.9, 0.00643, 117, 12.66, 14.08, 0.99357, 1, 117, 22.06, 3.91, 1, 1, 117, 29.51, -1.56, 1, 1, 117, 29.86, -8.15, 1, 1, 117, 16.39, -8.25, 1, 2, 116, 26.75, -9.34, 0.59189, 117, 0.08, -9.56, 0.40811, 2, 116, 16.66, -13.06, 0.99676, 117, -8.92, -15.44, 0.00324, 1, 116, 10.99, 0.42, 1, 2, 116, 26.46, 2.82, 0.80937, 117, -2.92, 2.22, 0.19063, 1, 117, 12.04, 0.04, 1, 1, 117, 22.72, -3.28, 1, 2, 116, 33.89, 9.63, 0.15238, 117, 2.81, 10.52, 0.84762, 1, 116, -1.82, 7.98, 1], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26, 2, 28, 28, 30, 30, 32, 32, 34], "width": 46, "height": 75}}, "qiongqi059": {"qiongqi059": {"type": "mesh", "uvs": [0.19354, 1, 0.32313, 0.86888, 0.48975, 0.74004, 0.823, 0.64055, 1, 0.52312, 1, 0.3747, 1, 0.23444, 0.86372, 0.11538, 0.64527, 0, 0.46754, 0, 0.29721, 0.12843, 0.15281, 0.29316, 0.25648, 0.42526, 0.25648, 0.55248, 0.07505, 0.64055, 0, 0.75798, 0, 0.87378, 0.12318, 1, 0.12986, 0.8651, 0.2153, 0.75333, 0.36806, 0.63929, 0.64251, 0.54007, 0.69947, 0.39181, 0.72018, 0.2561, 0.55228, 0.12392, 0.40426, 0.27827, 0.44655, 0.42595, 0.40426, 0.55502], "triangles": [25, 24, 23, 25, 10, 24, 23, 7, 6, 23, 24, 7, 10, 9, 24, 24, 8, 7, 24, 9, 8, 26, 12, 25, 12, 11, 25, 22, 25, 23, 11, 10, 25, 3, 21, 4, 21, 22, 4, 22, 5, 4, 22, 23, 5, 23, 6, 5, 21, 27, 26, 26, 27, 12, 27, 13, 12, 21, 26, 22, 22, 26, 25, 1, 19, 2, 15, 14, 19, 19, 20, 2, 19, 14, 20, 2, 20, 3, 20, 21, 3, 14, 13, 20, 20, 27, 21, 20, 13, 27, 1, 0, 18, 0, 17, 18, 17, 16, 18, 16, 15, 18, 18, 19, 1, 18, 15, 19], "vertices": [1, 98, -4.95, -3.72, 1, 2, 98, 18.06, -10.63, 0.94502, 99, -7.47, -17.39, 0.05498, 3, 98, 41.01, -20.31, 0.04082, 99, 17.08, -13.19, 0.95866, 103, -49.18, 9.23, 0.00052, 3, 99, 45.64, -21.65, 0.60676, 100, -13.34, -17.41, 0.0732, 103, -24.85, -7.96, 0.32004, 4, 99, 69.2, -19.26, 0.03642, 100, 4.56, -32.92, 0.00175, 103, -1.77, -13.27, 0.96183, 102, -32.27, -55.33, 0, 2, 103, 21.56, -4.48, 1, 102, -8.73, -47.11, 0, 2, 103, 43.62, 3.82, 0.66743, 102, 13.51, -39.33, 0.33257, 2, 103, 58.78, 20.3, 0.2002, 102, 29.07, -23.21, 0.7998, 2, 103, 71.23, 42.26, 0.00111, 102, 42.03, -1.56, 0.99889, 2, 101, 63.5, -10.85, 0.00041, 102, 37.69, 10.86, 0.99959, 2, 101, 45.13, 6.09, 0.41527, 102, 13.16, 15.64, 0.58473, 2, 100, 50.87, 24.34, 0.00075, 101, 20.4, 22.47, 0.99925, 3, 99, 47, 33.7, 0.03209, 100, 27.88, 19.56, 0.42682, 101, -2.93, 19.74, 0.54109, 3, 99, 30.48, 20.14, 0.59779, 100, 6.68, 22.28, 0.37611, 101, -23.8, 24.33, 0.0261, 3, 98, 53.97, 12.14, 0.0474, 99, 10.53, 21.14, 0.93273, 100, -6.29, 37.49, 0.01987, 2, 98, 33.72, 15.32, 0.76693, 99, -8.24, 12.91, 0.23307, 1, 98, 14.41, 13.02, 1, 1, 98, -5.57, 1.45, 1, 1, 98, 16.99, 3.65, 1, 1, 99, 2.47, 1.09, 1, 2, 99, 24.45, 4.51, 0.97006, 100, -8.84, 15.95, 0.02994, 3, 99, 50.22, -0.61, 0.00966, 100, 5.11, -6.31, 0.83081, 103, -13.76, 10.49, 0.15953, 4, 100, 29.27, -13.67, 0.56677, 103, 11.04, 15.32, 0.4075, 101, -4.47, -13.48, 0.0098, 102, -18.78, -27.06, 0.01593, 4, 100, 51.69, -18.09, 0.05808, 103, 32.92, 21.92, 0.34716, 101, 17.47, -19.87, 0.11685, 102, 3.24, -20.99, 0.4779, 2, 103, 49.32, 41.37, 0.0076, 102, 20.11, -1.93, 0.9924, 1, 101, 18.85, 3.76, 1, 3, 99, 55.84, 22.76, 0.00672, 100, 25.97, 5.63, 0.75048, 101, -6.06, 6.03, 0.2428, 3, 99, 37.09, 11.42, 0.48489, 100, 4.86, 11.49, 0.5022, 101, -26.56, 13.74, 0.01291], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 0, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54], "width": 74, "height": 168}}, "qiongqi05": {"qiongqi05": {"type": "mesh", "uvs": [0, 0.24658, 0, 0.55576, 0.11983, 0.82371, 0.32275, 0.901, 0.51824, 0.88554, 0.65434, 1, 0.76817, 1, 0.85973, 0.82371, 1, 0.66912, 1, 0.40632, 0.93397, 0.1126, 0.79292, 0.04046, 0.6197, 0.08169, 0.48607, 0.00954, 0.27326, 0, 0.11488, 0, 0.18532, 0.36539, 0.38851, 0.42024, 0.56913, 0.45941, 0.79866, 0.44374], "triangles": [16, 15, 14, 0, 15, 16, 1, 0, 16, 2, 1, 16, 17, 14, 13, 16, 14, 17, 17, 13, 18, 4, 17, 18, 3, 16, 17, 3, 17, 4, 2, 16, 3, 12, 11, 19, 18, 13, 12, 18, 12, 19, 5, 18, 19, 6, 5, 19, 4, 18, 5, 19, 11, 10, 19, 10, 9, 19, 9, 8, 7, 19, 8, 7, 6, 19], "vertices": [1, 213, -8.47, -14.5, 1, 2, 214, 10.18, -68.65, 0.04165, 213, 17.81, -14.5, 0.95835, 2, 214, 32.96, -47.44, 0.38067, 213, 40.59, 6.71, 0.61933, 3, 215, 36.26, -53.32, 0.08016, 214, 39.53, -11.53, 0.75912, 213, 47.16, 42.63, 0.16072, 4, 216, 33.85, -69.6, 0.02084, 215, 34.94, -18.72, 0.59879, 214, 38.21, 23.08, 0.37853, 213, 45.85, 77.23, 0.00184, 3, 216, 43.58, -45.51, 0.16897, 215, 44.67, 5.37, 0.77297, 214, 47.94, 47.17, 0.05806, 3, 216, 43.58, -25.36, 0.32538, 215, 44.67, 25.52, 0.66887, 214, 47.94, 67.31, 0.00575, 2, 216, 28.6, -9.15, 0.62358, 215, 29.69, 41.73, 0.37642, 2, 216, 15.46, 15.68, 0.99271, 215, 16.55, 66.55, 0.00729, 2, 216, -6.88, 15.68, 0.99976, 215, -5.79, 66.55, 0.00024, 2, 216, -31.85, 3.99, 0.85619, 215, -30.76, 54.87, 0.14381, 3, 216, -37.98, -20.98, 0.54164, 215, -36.89, 29.9, 0.45711, 214, -33.62, 71.69, 0.00125, 3, 216, -34.48, -51.64, 0.09205, 215, -33.39, -0.76, 0.71367, 214, -30.11, 41.03, 0.19428, 4, 216, -40.61, -75.29, 0.00106, 215, -39.52, -24.41, 0.3501, 214, -36.25, 17.38, 0.6288, 213, -28.61, 71.53, 0.02004, 3, 215, -40.33, -62.08, 0.00991, 214, -37.06, -20.28, 0.59304, 213, -29.43, 33.87, 0.39706, 2, 214, -37.06, -48.32, 0.18198, 213, -29.43, 5.83, 0.81802, 2, 214, -6, -35.85, 0.35001, 213, 1.63, 18.3, 0.64999, 2, 214, -1.34, 0.11, 0.99966, 213, 6.29, 54.26, 0.00034, 2, 215, -1.28, -9.71, 0.79801, 214, 1.99, 32.08, 0.20199, 2, 216, -3.7, -19.96, 0.58512, 215, -2.61, 30.92, 0.41488], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30, 0, 32, 32, 34, 34, 36, 36, 38], "width": 177, "height": 85}}, "qiongqi017": {"qiongqi017": {"type": "mesh", "uvs": [0.34712, 1, 0.52574, 1, 0.72855, 0.90131, 0.89043, 0.69052, 1, 0.59864, 1, 0.19328, 0.89043, 0, 0.72855, 0, 0.53504, 0, 0.34526, 0, 0.23548, 0.30678, 0.06802, 0.56621, 0, 0.85807, 0, 1, 0.11454, 1, 0.23176, 0.777, 0.15307, 0.68069, 0.34945, 0.49785, 0.57857, 0.41741, 0.78502, 0.3589, 0.91594, 0.33696], "triangles": [16, 11, 10, 16, 10, 17, 15, 16, 17, 16, 13, 12, 16, 12, 11, 14, 13, 16, 14, 16, 15, 17, 9, 8, 17, 8, 18, 10, 9, 17, 0, 15, 17, 1, 17, 18, 0, 17, 1, 20, 6, 5, 19, 7, 6, 19, 6, 20, 18, 8, 7, 18, 7, 19, 20, 5, 4, 3, 19, 20, 3, 20, 4, 2, 18, 19, 2, 19, 3, 1, 18, 2], "vertices": [3, 107, 64.49, 26.07, 0.00178, 108, 28.99, 22.26, 0.87903, 109, 0.55, 23.72, 0.11919, 3, 107, 42.75, 24.59, 0.24321, 108, 7.26, 23.81, 0.75662, 109, -19.08, 33.18, 0.00017, 2, 107, 18.34, 18.76, 0.94738, 108, -17.72, 21.44, 0.05262, 1, 107, -0.75, 8.58, 1, 1, 107, -13.83, 3.82, 1, 1, 107, -12.67, -13.16, 1, 1, 107, 1.22, -20.35, 1, 2, 107, 20.93, -19, 0.99106, 108, -20.42, -16.32, 0.00894, 2, 107, 44.48, -17.39, 0.2815, 108, 3.13, -18, 0.7185, 1, 108, 26.23, -19.65, 1, 2, 108, 40.5, -7.76, 0.48679, 109, 0.19, -8.42, 0.51321, 1, 109, 23.33, -7.46, 1, 1, 109, 36.12, -0.02, 1, 1, 109, 38.71, 5.35, 1, 1, 109, 26.12, 11.41, 1, 2, 108, 42.36, 11.91, 0.20237, 109, 9.17, 9.18, 0.79763, 2, 108, 51.65, 7.19, 0.00081, 109, 16.06, 1.37, 0.99919, 2, 108, 27.2, 1.24, 0.99527, 109, -8.86, 4.84, 0.00473, 2, 107, 37.99, -0.26, 0.85031, 108, -0.92, -0.14, 0.14969, 1, 107, 13.03, -4.43, 1, 1, 107, -2.85, -6.44, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30, 26, 32, 32, 34, 34, 36, 36, 38, 38, 40], "width": 122, "height": 42}}, "qiongqi018": {"qiongqi018": {"type": "mesh", "uvs": [0, 0.18311, 0, 0.35394, 0.10906, 0.44668, 0.2262, 0.38322, 0.33358, 0.51013, 0.34139, 0.78834, 0.46244, 0.94453, 0.61667, 1, 0.81972, 0.95918, 0.95639, 0.80299, 1, 0.54918, 0.8861, 0.25144, 0.69672, 0.15382, 0.49367, 0.09525, 0.37263, 0, 0.18129, 0, 0.0622, 0.10989, 0.14247, 0.2272, 0.32957, 0.21243, 0.47335, 0.40938, 0.66046, 0.57679, 0.84953, 0.58664], "triangles": [18, 15, 14, 17, 16, 15, 17, 15, 18, 1, 0, 16, 1, 16, 17, 3, 17, 18, 2, 1, 17, 2, 17, 3, 13, 18, 14, 19, 18, 13, 4, 18, 19, 3, 18, 4, 12, 19, 13, 5, 4, 19, 6, 5, 19, 6, 19, 20, 20, 19, 12, 21, 12, 11, 21, 11, 10, 20, 12, 21, 9, 21, 10, 8, 20, 21, 8, 21, 9, 7, 6, 20, 7, 20, 8], "vertices": [1, 106, 34.95, -0.73, 1, 1, 106, 35.71, 6.41, 1, 1, 106, 24.73, 11.49, 1, 2, 105, 31.11, 15.47, 0.11072, 106, 12.22, 10.14, 0.88928, 2, 105, 18.71, 14.18, 0.81409, 106, 1.57, 16.63, 0.18591, 3, 104, 44.76, 20.7, 0.0018, 105, 11.95, 23.75, 0.98464, 106, 1.99, 28.34, 0.01356, 2, 104, 30.63, 22.95, 0.15711, 105, -2.32, 22.76, 0.84289, 2, 104, 14.52, 20.09, 0.69444, 105, -17.38, 16.35, 0.30556, 2, 104, -5.19, 11.78, 0.99684, 105, -34.71, 3.83, 0.00316, 1, 104, -16.76, 1.06, 1, 1, 104, -17.77, -10.5, 1, 1, 104, -2.49, -18.63, 1, 1, 104, 17.68, -16.29, 1, 2, 104, 38.69, -11.95, 0.19365, 105, 13.38, -9.43, 0.80635, 3, 104, 52.02, -11.77, 6e-05, 105, 26.32, -6.26, 0.90124, 106, -4.77, -4.24, 0.0987, 1, 106, 15.21, -6.36, 1, 1, 106, 28.13, -3.1, 1, 1, 106, 20.27, 2.69, 1, 2, 105, 25.56, 3.71, 0.58683, 106, 0.67, 4.16, 0.41317, 2, 105, 8.36, 2.95, 0.99886, 106, -13.47, 13.98, 0.00114, 2, 104, 15.73, 1.77, 0.98463, 105, -12.09, -1.23, 0.01537, 1, 104, -3.26, -4.06, 1], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32, 2, 34, 34, 36, 36, 38, 38, 40, 40, 42], "width": 105, "height": 42}}, "qiongqi08": {"qiongqi08": {"type": "mesh", "uvs": [0.44932, 1, 0.71135, 1, 0.83632, 0.89737, 0.75569, 0.76136, 0.75972, 0.62114, 0.91694, 0.50476, 1, 0.42764, 1, 0.34351, 0.88469, 0.22433, 1, 0.17525, 1, 0.09392, 1, 0, 0.82019, 0, 0.71135, 0.11636, 0.41707, 0.20189, 0.19535, 0.33089, 0.27194, 0.46129, 0.18326, 0.60291, 0.03007, 0.73612, 0.08651, 0.85671, 0.30822, 0.93803, 0.45358, 0.85478, 0.39228, 0.73325, 0.46584, 0.61385, 0.6007, 0.46247, 0.61909, 0.31961, 0.63748, 0.21727, 0.87041, 0.11493], "triangles": [27, 12, 11, 10, 27, 11, 13, 12, 27, 27, 10, 9, 26, 14, 13, 26, 13, 27, 8, 27, 9, 26, 27, 8, 25, 14, 26, 25, 26, 8, 15, 14, 25, 25, 8, 7, 25, 7, 6, 24, 25, 6, 16, 15, 25, 24, 16, 25, 5, 24, 6, 23, 16, 24, 4, 23, 24, 5, 4, 24, 17, 16, 23, 22, 17, 23, 18, 17, 22, 4, 22, 23, 3, 22, 4, 21, 22, 3, 19, 18, 22, 19, 22, 21, 21, 3, 2, 20, 19, 21, 0, 20, 21, 1, 0, 21, 2, 1, 21], "vertices": [1, 154, -9.52, 6.27, 1, 1, 154, -10.89, -2, 1, 1, 154, -2.23, -7.49, 1, 2, 154, 10.54, -6.98, 0.9893, 155, -12.31, -10.84, 0.0107, 3, 154, 23.24, -9.22, 0.24714, 155, 0.49, -9.3, 0.73815, 156, -20.67, -14.56, 0.0147, 2, 155, 11.76, -12.9, 0.68063, 156, -8.86, -15.26, 0.31937, 2, 155, 19.14, -14.61, 0.2675, 156, -1.29, -15.09, 0.7325, 2, 155, 26.82, -13.61, 0.01746, 156, 5.9, -12.21, 0.98254, 1, 156, 14.71, -4.72, 1, 1, 156, 20.27, -6.47, 1, 1, 156, 27.22, -3.69, 1, 1, 156, 35.24, -0.48, 1, 1, 156, 33.11, 4.87, 1, 2, 155, 46.34, -1.74, 1e-05, 156, 21.87, 4.12, 0.99999, 2, 155, 37.32, 6.58, 0.17801, 156, 11.07, 9.94, 0.82199, 2, 155, 24.63, 12.07, 0.96943, 156, -2.59, 12.12, 0.03057, 2, 154, 40.3, 3.78, 0.07402, 155, 13.05, 8.09, 0.92598, 2, 154, 27.91, 8.71, 0.97942, 155, -0.23, 9.21, 0.02058, 1, 154, 16.62, 15.54, 1, 1, 154, 5.38, 15.57, 1, 1, 154, -3.16, 9.8, 1, 1, 154, 3.64, 3.96, 1, 1, 154, 14.99, 4.07, 1, 2, 154, 25.44, -0.05, 0.72362, 155, -0.06, 0.12, 0.27638, 2, 155, 14.31, -2.36, 0.92393, 156, -9.01, -4.41, 0.07607, 1, 156, 3.41, -0.08, 1, 2, 155, 36.83, -0.6, 0.02402, 156, 12.37, 2.87, 0.97598, 1, 156, 23.89, -0.55, 1], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 0, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 22], "width": 32, "height": 92}}, "qiongqi09": {"qiongqi09": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [110.57, 20.33, 94.99, -46.89, 54.08, -37.4, 69.65, 29.81], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 69, "height": 42}}, "qiongqi033": {"qiongqi033": {"type": "mesh", "uvs": [0.08509, 0.37864, 0.19752, 0.40198, 0.29297, 0.37631, 0.35449, 0.30398, 0.37358, 0.18964, 0.35661, 0.08464, 0.49661, 0, 0.68115, 0, 0.85297, 0.08464, 0.96752, 0.22464, 1, 0.43931, 0.95055, 0.56998, 0.83176, 0.67498, 0.85297, 0.83598, 0.99085, 0.90131, 0.9463, 1, 0.7554, 1, 0.60055, 0.93631, 0.43085, 0.97131, 0.29721, 1, 0.14024, 0.90831, 0.01721, 0.78931, 0, 0.64231, 0.04267, 0.49998, 0.41556, 0.57137, 0.61893, 0.74345, 0.7776, 0.89095, 0.51751, 0.34596, 0.71537, 0.45189, 0.8607, 0.52509, 0.55253, 0.18802, 0.76265, 0.24773, 0.19533, 0.54627, 0.20934, 0.77548, 0.12354, 0.50197, 0.07451, 0.68688, 0.28288, 0.5482, 0.32315, 0.75622, 0.40019, 0.86215, 0.64883, 0.57902, 0.45097, 0.46538], "triangles": [34, 0, 1, 23, 0, 34, 32, 34, 1, 35, 23, 34, 35, 34, 32, 22, 23, 35, 21, 22, 35, 35, 20, 21, 38, 37, 24, 19, 33, 37, 19, 37, 38, 19, 38, 18, 20, 33, 19, 36, 1, 2, 32, 1, 36, 33, 32, 36, 33, 36, 37, 35, 32, 33, 33, 20, 35, 13, 26, 12, 16, 17, 26, 15, 13, 14, 26, 13, 15, 16, 26, 15, 12, 39, 29, 25, 24, 39, 25, 39, 12, 26, 25, 12, 25, 38, 24, 17, 38, 25, 17, 25, 26, 18, 38, 17, 24, 36, 2, 40, 24, 2, 24, 40, 39, 37, 36, 24, 3, 4, 27, 40, 3, 27, 2, 3, 40, 39, 27, 28, 40, 27, 39, 29, 39, 28, 31, 8, 9, 28, 27, 31, 10, 29, 31, 10, 31, 9, 28, 31, 29, 11, 29, 10, 12, 29, 11, 5, 6, 30, 4, 5, 30, 27, 4, 30, 27, 30, 31, 30, 6, 7, 31, 7, 8, 30, 7, 31], "vertices": [3, 19, -15.59, -9.96, 0.01903, 24, -9.83, -10.43, 0.01618, 26, -4, -10.19, 0.96479, 4, 28, -12.49, -4.19, 0.01517, 19, -9.57, -5.4, 0.20883, 24, -8.85, -2.95, 0.24746, 26, -8.16, -3.89, 0.52854, 5, 22, -8.54, -11.9, 0.00361, 28, -6.67, -1.34, 0.32245, 19, -6.48, 0.3, 0.48274, 24, -10.75, 3.26, 0.12727, 26, -13.65, -0.44, 0.06392, 5, 22, -4.93, -7.18, 0.26103, 28, -3.63, 3.76, 0.70883, 19, -6.96, 6.22, 0.02748, 24, -15.31, 7.06, 0.00184, 26, -19.59, -0.54, 0.00081, 3, 27, -10.02, -6.28, 0.04772, 22, -4.36, -0.22, 0.87066, 28, -3.86, 10.73, 0.08163, 2, 27, -11.04, 0.04, 0.32268, 22, -6.11, 5.93, 0.67732, 2, 27, -1.71, 4.96, 0.96968, 22, 2.58, 11.91, 0.03032, 1, 27, 10.47, 4.76, 1, 2, 27, 21.72, -0.51, 0.52711, 23, 10.95, 11.67, 0.47289, 2, 27, 29.14, -9.04, 0.0415, 23, 20.69, 5.93, 0.9585, 4, 23, 26.6, -5.71, 0.82694, 28, 39.73, 4.94, 0.08746, 20, 14.82, 30.93, 0.08059, 21, 0.79, 30.98, 0.00501, 4, 23, 25.85, -14.16, 0.59495, 28, 38.22, -3.42, 0.18227, 20, 17.6, 22.91, 0.19708, 21, 2.88, 22.74, 0.02569, 4, 23, 20.26, -22.53, 0.20395, 28, 31.9, -11.25, 0.17893, 20, 15.95, 12.99, 0.42721, 21, 0.39, 13, 0.1899, 4, 23, 24.5, -31.32, 0.01024, 28, 35.33, -20.39, 0.0118, 20, 23.43, 6.71, 0.05655, 21, 7.31, 6.11, 0.92141, 1, 21, 16.94, 8.44, 1, 1, 21, 18.14, 1.94, 1, 3, 20, 25.18, -4.92, 0.00981, 21, 8.06, -5.62, 0.98991, 25, 23.65, 29.8, 0.00028, 3, 20, 15.01, -8.87, 0.7399, 21, -2.41, -8.7, 0.14763, 25, 17.37, 20.88, 0.11247, 3, 19, 26.08, -16.96, 0.01002, 20, 8.05, -17.9, 0.39183, 25, 16.56, 9.52, 0.59816, 3, 19, 21.42, -24.64, 2e-05, 20, 2.62, -25.06, 0.06178, 25, 15.99, 0.55, 0.9382, 3, 24, 21.69, -5, 0.41576, 25, 8.04, -8.08, 0.47094, 26, 16.29, 14.55, 0.1133, 3, 24, 15.02, -13.51, 0.37036, 25, -0.92, -14.13, 0.0017, 26, 16.81, 3.75, 0.62794, 1, 26, 11.27, -3.21, 1, 1, 26, 3.18, -7.13, 1, 3, 19, 7.63, -1.58, 0.93968, 24, 0.48, 12, 0.03639, 25, -6.91, 14.62, 0.02393, 4, 23, 8.1, -30.67, 0.00101, 28, 19.06, -18.27, 0.00429, 20, 8.21, 0.57, 0.99403, 21, -8.38, 1.29, 0.00067, 3, 23, 20.75, -35.96, 0.00047, 28, 31.18, -24.67, 0.00056, 21, 5.31, 0.49, 0.99897, 3, 22, 6.02, -8.61, 0.2443, 23, -5.45, -9.93, 0.00632, 28, 7.42, 3.6, 0.74939, 5, 23, 8.91, -12.07, 0.32748, 28, 21.54, 0.18, 0.55276, 19, 15.61, 17.89, 0.00188, 20, 1.31, 17.86, 0.11431, 21, -13.78, 19.1, 0.00357, 4, 23, 19.38, -13.38, 0.53454, 28, 31.85, -2.06, 0.22762, 20, 11.39, 20.97, 0.21137, 21, -3.47, 21.34, 0.02647, 2, 27, 1.79, -6.38, 0.07823, 22, 7.38, 1.05, 0.92177, 2, 27, 15.6, -10.2, 0.01431, 23, 8.21, 0.55, 0.98569, 2, 24, -0.2, -2.6, 0.54902, 26, -1.84, 2.03, 0.45098, 3, 24, 13.48, -0.9, 0.74636, 25, 1.49, -1.65, 0.2206, 26, 7.39, 12.27, 0.03303, 3, 19, -8.41, -13.05, 0.00754, 24, -2.59, -7.48, 0.01581, 26, -0.45, -3.22, 0.97665, 2, 24, 8.67, -10.09, 0.16022, 26, 9.77, 2.18, 0.83978, 3, 19, 0.71, -7.13, 0.30305, 24, -0.41, 3.17, 0.67787, 25, -10.47, 6.5, 0.01908, 4, 19, 11.74, -13.55, 0.14827, 20, -5.84, -13.01, 0.07289, 24, 11.9, 6.53, 0.02266, 25, 2.28, 5.91, 0.75618, 3, 19, 19.87, -14.05, 0.06091, 20, 2.18, -14.36, 0.31792, 25, 9.71, 9.22, 0.62118, 5, 23, 7.02, -20.67, 0.05382, 28, 18.88, -8.21, 0.41033, 19, 18.31, 9.52, 0.0787, 20, 3.11, 9.24, 0.45111, 21, -12.72, 10.36, 0.00603, 2, 28, 4.66, -4.34, 0.50138, 19, 4.49, 4.43, 0.49862], "hull": 24, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 0, 46, 4, 48, 48, 50, 50, 52, 52, 30, 6, 54, 54, 56, 56, 58, 8, 60, 60, 62, 2, 64, 64, 66, 66, 38, 68, 70, 70, 40, 72, 74, 74, 76, 78, 24, 78, 80], "width": 66, "height": 60}}, "qiongqi04": {"qiongqi04": {"type": "mesh", "uvs": [1, 0.52845, 1, 1, 0.8, 1, 0.6, 1, 0.4, 1, 0.2, 1, 0, 1, 0, 0.52244, 0, 0, 0.2, 0, 0.4, 0, 0.6, 0, 0.8, 0, 1, 0, 0.19297, 0.50439, 0.41204, 0.51642, 0.60715, 0.52845, 0.80226, 0.52244], "triangles": [6, 7, 14, 7, 8, 14, 6, 14, 5, 4, 5, 14, 14, 9, 10, 14, 8, 9, 15, 4, 14, 15, 14, 10, 4, 15, 3, 3, 15, 16, 15, 11, 16, 15, 10, 11, 3, 16, 2, 2, 16, 17, 16, 12, 17, 16, 11, 12, 2, 17, 1, 17, 12, 13, 17, 0, 1, 17, 13, 0], "vertices": [2, 211, -13.1, 10.24, 0.92622, 206, -8.51, 52.64, 0.07378, 2, 211, 51.04, 10.24, 0.84689, 206, 55.62, 52.64, 0.15311, 4, 211, 51.04, -85.36, 0.01134, 206, 55.62, -42.96, 0.55552, 208, 56.04, 58.03, 0.43284, 207, 56.59, 125.74, 0.0003, 4, 206, 55.62, -138.56, 0.00623, 208, 56.04, -37.57, 0.45014, 207, 56.59, 30.14, 0.52823, 209, 46.05, 133.38, 0.01541, 3, 207, 56.59, -65.46, 0.33717, 209, 46.05, 37.78, 0.66145, 210, 56.12, 107.35, 0.00138, 3, 209, 46.05, -57.82, 0.27336, 210, 56.12, 11.75, 0.6882, 205, 77.79, 70.26, 0.03845, 2, 210, 56.12, -83.85, 0.39016, 205, 77.79, -25.34, 0.60984, 2, 210, -8.83, -83.85, 0.04675, 205, 12.84, -25.34, 0.95325, 2, 210, -79.88, -83.85, 0.00252, 205, -58.21, -25.34, 0.99748, 4, 207, -79.41, -161.06, 0.02358, 209, -89.95, -57.82, 0.22813, 210, -79.88, 11.75, 0.48931, 205, -58.21, 70.26, 0.25897, 5, 208, -79.96, -133.17, 0.00411, 207, -79.41, -65.46, 0.4704, 209, -89.95, 37.78, 0.45736, 210, -79.88, 107.35, 0.06799, 205, -58.21, 165.86, 0.00013, 4, 206, -80.38, -138.56, 0.02983, 208, -79.96, -37.57, 0.43458, 207, -79.41, 30.14, 0.51317, 209, -89.95, 133.38, 0.02243, 4, 211, -84.96, -85.36, 0.03939, 206, -80.38, -42.96, 0.53794, 208, -79.96, 58.03, 0.41566, 207, -79.41, 125.74, 0.00701, 3, 211, -84.96, 10.24, 0.46963, 206, -80.38, 52.64, 0.52388, 208, -79.96, 153.63, 0.0065, 4, 207, -10.81, -164.42, 0.00032, 209, -21.36, -61.18, 0.10313, 210, -11.28, 8.39, 0.88244, 205, 10.39, 66.9, 0.01411, 3, 207, -9.17, -59.7, 0.45254, 209, -19.72, 43.53, 0.54545, 210, -9.65, 113.1, 0.00201, 2, 208, -8.09, -34.15, 0.49932, 207, -7.54, 33.56, 0.50068, 2, 206, -9.33, -41.88, 0.60398, 208, -8.91, 59.11, 0.39602], "hull": 14, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 12, 14, 14, 16, 14, 28, 28, 30, 30, 32, 32, 34, 2, 0, 0, 26, 34, 0], "width": 478, "height": 136}}, "qiongqi054": {"qiongqi054": {"type": "mesh", "uvs": [0.10795, 0, 0.25298, 0, 0.40131, 0.16078, 0.65843, 0.23054, 0.92872, 0.27384, 1, 0.56008, 1, 0.83429, 0.81665, 1, 0.63205, 1, 0.40791, 0.83189, 0.1442, 0.69719, 0.01565, 0.47348, 0, 0.2113, 0, 0.08622, 0.18831, 0.2237, 0.41268, 0.4111, 0.61813, 0.60245, 0.6776, 0.81156, 0.76681, 0.40519, 0.86683, 0.63007], "triangles": [14, 12, 13, 14, 0, 1, 14, 1, 2, 14, 13, 0, 15, 2, 3, 14, 2, 15, 11, 12, 14, 11, 14, 15, 18, 3, 4, 15, 3, 18, 18, 4, 5, 16, 15, 18, 10, 11, 15, 10, 15, 16, 19, 16, 18, 5, 19, 18, 17, 16, 19, 9, 10, 16, 17, 9, 16, 19, 5, 6, 17, 19, 6, 8, 9, 17, 7, 17, 6, 8, 17, 7], "vertices": [1, 181, 15.69, -1.45, 1, 1, 181, 13.85, -4.91, 1, 2, 180, 16.3, -6.54, 0.03176, 181, 6.71, -5.64, 0.96824, 3, 179, 21.26, -9.53, 0.04724, 180, 10.11, -10.61, 0.49209, 181, 1.17, -10.55, 0.46067, 3, 179, 16.19, -15.02, 0.19964, 180, 4.51, -15.55, 0.61663, 181, -3.68, -16.24, 0.18374, 3, 179, 6.08, -11.32, 0.70522, 180, -5.17, -10.85, 0.27261, 181, -13.93, -12.95, 0.02217, 2, 179, -2.68, -6.19, 0.99879, 180, -13.36, -4.86, 0.00121, 1, 179, -5.46, 1.18, 1, 1, 179, -2.94, 5.48, 1, 2, 179, 5.49, 7.56, 0.8118, 180, -3.84, 7.99, 0.1882, 3, 179, 13.39, 11.18, 0.20008, 180, 4.38, 10.79, 0.76894, 181, -7.53, 9.82, 0.03098, 3, 179, 22.28, 9.99, 0.00758, 180, 13.11, 8.7, 0.44822, 181, 1.41, 8.99, 0.54421, 2, 180, 21.19, 3.31, 0.00091, 181, 10.16, 4.8, 0.99909, 1, 181, 14.25, 2.62, 1, 1, 181, 7.37, 0.53, 1, 2, 180, 8.64, -1.31, 0.88428, 181, -1.6, -1.55, 0.11572, 3, 179, 9.94, -1.63, 0.60706, 180, -0.35, -1.61, 0.39038, 181, -10.46, -3.12, 0.00256, 2, 179, 2.45, 0.89, 0.99999, 180, -7.54, 1.67, 1e-05, 3, 179, 14.2, -8.79, 0.22725, 180, 3.17, -9.16, 0.63033, 181, -5.91, -10.09, 0.14242, 3, 179, 5.66, -6.91, 0.84264, 180, -5.14, -6.42, 0.15069, 181, -14.52, -8.56, 0.00667], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26, 0, 28, 28, 30, 30, 32, 32, 34, 36, 38], "width": 27, "height": 37}}, "qiongqi03": {"qiongqi03": {"type": "mesh", "uvs": [0.15283, 0.5491, 0.04234, 0.55166, 0, 0.52864, 0, 0.46084, 0.08305, 0.42886, 0.17997, 0.36746, 0.27302, 0.30478, 0.38739, 0.27024, 0.37576, 0.15639, 0.26139, 0.0515, 0.41259, 0, 0.59674, 0, 0.79447, 0, 0.86813, 0.03487, 0.70917, 0.09371, 0.81191, 0.16918, 0.8817, 0.25361, 1, 0.32524, 1, 0.43142, 1, 0.51712, 0.97087, 0.63992, 0.93985, 0.76401, 0.88945, 0.86634, 0.84293, 1, 0.71305, 1, 0.60256, 1, 0.43197, 0.96868, 0.32342, 0.85994, 0.22068, 0.76401, 0.18773, 0.64632, 0.27127, 0.49656, 0.37489, 0.41547, 0.50259, 0.36617, 0.56765, 0.28349, 0.62307, 0.18014, 0.63994, 0.12289, 0.7315, 0.18491, 0.81584, 0.23897, 0.68813, 0.2819, 0.78451, 0.31211, 0.88813, 0.38684, 0.94595, 0.46476, 0.6038, 0.37094, 0.74114, 0.42183, 0.83752, 0.49974, 0.88813, 0.56334, 0.46645, 0.46158, 0.65199, 0.51723, 0.76042, 0.59355, 0.81825, 0.73348, 0.82307, 0.85274, 0.40139, 0.55698, 0.5556, 0.63331, 0.65922, 0.74143, 0.67849, 0.86387, 0.23995, 0.54267, 0.31465, 0.61422, 0.41103, 0.71122, 0.48332, 0.82888, 0.53392, 0.9227], "triangles": [26, 59, 25, 25, 59, 54, 26, 58, 59, 26, 27, 58, 59, 58, 54, 58, 27, 57, 58, 57, 53, 27, 28, 57, 57, 29, 56, 57, 28, 29, 57, 52, 53, 56, 51, 57, 57, 51, 52, 29, 55, 56, 29, 0, 55, 56, 55, 51, 55, 30, 51, 51, 30, 46, 46, 30, 31, 30, 55, 0, 22, 23, 50, 23, 24, 50, 25, 54, 24, 24, 54, 50, 22, 50, 21, 58, 53, 54, 54, 53, 50, 53, 49, 50, 50, 49, 21, 21, 49, 20, 49, 53, 48, 53, 52, 48, 20, 49, 48, 52, 47, 48, 52, 51, 47, 48, 47, 44, 51, 46, 47, 47, 46, 42, 46, 32, 42, 47, 43, 44, 47, 42, 43, 46, 31, 32, 32, 33, 42, 20, 48, 45, 20, 45, 19, 48, 44, 45, 45, 41, 19, 45, 44, 41, 41, 18, 19, 44, 40, 41, 44, 43, 40, 41, 40, 18, 40, 17, 18, 43, 42, 39, 43, 39, 40, 39, 42, 38, 39, 16, 40, 40, 16, 17, 39, 37, 16, 39, 38, 37, 38, 36, 37, 38, 34, 36, 36, 15, 37, 16, 37, 15, 36, 14, 15, 33, 34, 38, 35, 34, 10, 8, 10, 34, 34, 7, 8, 34, 35, 36, 35, 14, 36, 35, 10, 11, 8, 9, 10, 35, 11, 14, 14, 12, 13, 14, 11, 12, 5, 6, 31, 31, 7, 32, 31, 6, 7, 42, 33, 38, 32, 7, 33, 33, 7, 34, 0, 1, 4, 3, 4, 1, 30, 0, 4, 1, 2, 3, 4, 5, 30, 30, 5, 31], "vertices": [2, 55, 5.24, -22.77, 0.56244, 62, -2.64, -34.87, 0.43756, 2, 55, -17.64, -5.36, 0.99935, 62, -13.28, -61.58, 0.00065, 1, 55, -20.42, 8.63, 1, 1, 55, -3.51, 29.31, 1, 1, 55, 21.18, 25.39, 1, 2, 55, 56, 28.17, 0.91919, 56, -18.51, 29.49, 0.08081, 2, 55, 90.36, 31.98, 0.20145, 56, 16.03, 30.96, 0.79855, 2, 56, 47.03, 20.54, 0.93292, 57, -24.41, 35.07, 0.06708, 2, 56, 75.51, 55.33, 0.20502, 57, 16.57, 53.55, 0.79498, 2, 56, 82.09, 105.82, 0.01147, 57, 44.93, 95.84, 0.98853, 2, 56, 124.65, 93.72, 0.00096, 57, 77.66, 66.07, 0.99904, 1, 57, 94.36, 21.2, 1, 1, 57, 112.29, -26.98, 1, 1, 57, 106.1, -49.72, 1, 2, 57, 69.95, -19.08, 0.88275, 58, -41.72, 21.19, 0.11725, 2, 57, 51.41, -54.48, 0.0563, 58, -2.48, 28.8, 0.9437, 1, 58, 35.38, 27.24, 1, 2, 58, 75.41, 39.1, 0.66676, 59, -13.48, 36.8, 0.33324, 2, 58, 111.28, 17.57, 0.0123, 59, 26.95, 26.08, 0.9877, 1, 59, 59.59, 17.42, 1, 3, 59, 104.42, -2.3, 0.57413, 60, 129.69, 56.76, 0.00231, 61, 13.9, 65.93, 0.42355, 2, 59, 149.61, -22.62, 0.10597, 61, 62.02, 54.08, 0.89403, 2, 59, 185.22, -45.62, 0.00697, 61, 101.19, 37.88, 0.99303, 2, 61, 152.76, 21.72, 0.98353, 63, 126.88, 89.05, 0.01647, 2, 61, 150.13, -11.94, 0.79985, 63, 120.67, 55.86, 0.20015, 2, 61, 147.89, -40.58, 0.41574, 63, 115.38, 27.62, 0.58426, 1, 63, 95.09, -13.71, 1, 2, 62, 127.32, -43.54, 0.0346, 63, 47.79, -33.57, 0.9654, 2, 62, 81.97, -52.79, 0.55265, 63, 5.72, -52.87, 0.44735, 3, 55, -11.99, -58.16, 0.01069, 62, 36.07, -41.98, 0.96882, 63, -41.43, -52.76, 0.02049, 4, 55, 42.18, -26.23, 0.23592, 56, -35.99, -23.85, 0.00103, 60, 1.26, -83.88, 0.00615, 62, -9.21, 1.65, 0.7569, 4, 55, 83.26, -18.55, 0.17919, 56, 5.52, -18.97, 0.52283, 60, -16.22, -45.91, 0.16483, 62, -27.62, 39.17, 0.13315, 4, 56, 43.02, -27.52, 0.48119, 58, 22.7, -80.1, 0.00689, 60, -19.68, -7.6, 0.50454, 62, -32.04, 77.38, 0.00738, 4, 56, 77.65, -15.33, 0.71742, 57, -12.96, -10.68, 0.06002, 58, 3.47, -48.83, 0.11195, 60, -41.98, 21.55, 0.11061, 2, 57, 30.23, -9.98, 0.83275, 58, -24.04, -15.52, 0.16725, 2, 57, 52.9, -6.22, 0.94297, 58, -41.12, -0.16, 0.05703, 2, 57, 38.31, -37.05, 0.14368, 58, -7.92, 7.68, 0.85632, 1, 58, 21.63, 15.53, 1, 4, 56, 100.92, -36.3, 0.11103, 57, -1.44, -39.81, 0.09272, 58, 19.05, -21.65, 0.69286, 60, -29.24, 50.17, 0.10338, 5, 56, 111.06, -62.12, 0.00681, 57, -3.86, -67.45, 0.00081, 58, 42.15, -6.28, 0.96202, 59, -32.84, -16.03, 0.00099, 60, -7.81, 67.8, 0.02937, 2, 58, 81.26, 1.67, 0.22722, 59, 2.52, 2.46, 0.77278, 1, 59, 36.05, 9.13, 1, 5, 56, 60.93, -46.89, 0.12542, 57, -41.97, -31.5, 0.00068, 58, 37.85, -58.5, 0.13754, 59, -22.48, -67.39, 0.00681, 60, -6.8, 15.41, 0.72955, 4, 56, 73.26, -85.94, 0.0012, 58, 73.41, -38.19, 0.24037, 59, 6.05, -38.01, 0.27438, 60, 26.52, 39.22, 0.48405, 4, 58, 112.63, -32.5, 0.00206, 59, 42.14, -21.65, 0.73294, 60, 64.96, 48.86, 0.26247, 61, -43.86, 35.66, 0.00252, 3, 59, 69.74, -15.36, 0.7896, 60, 93.24, 50.11, 0.1198, 61, -17.85, 46.83, 0.0906, 4, 55, 90.19, -47.69, 0.03138, 56, 10.45, -48.51, 0.12697, 60, 10.35, -32.09, 0.6024, 62, -1.41, 53.65, 0.23925, 3, 58, 93.72, -77.41, 0.00076, 59, 36.44, -70.05, 0.008, 60, 50.7, 2.26, 0.99124, 3, 59, 72.73, -50.51, 0.1495, 60, 89.9, 15, 0.63513, 61, -8.57, 12.8, 0.21537, 2, 59, 129.88, -50.1, 0.06717, 61, 47.57, 23.5, 0.93283, 2, 59, 175.62, -60.93, 0.0047, 61, 94.51, 21.09, 0.9953, 5, 55, 53.3, -66.08, 0.00135, 56, -27.6, -64.35, 0.00598, 60, 37.18, -63.37, 0.19576, 61, -30.2, -79.14, 0.002, 62, 26.2, 23.04, 0.79491, 4, 60, 81.44, -39.86, 0.37202, 61, 2.9, -41.51, 0.21748, 62, 69.86, 47.65, 0.37623, 63, -28.88, 42.2, 0.03427, 4, 60, 131.46, -33.58, 0.00728, 61, 47.47, -17.97, 0.839, 62, 119.7, 55.17, 0.02706, 63, 17.95, 60.84, 0.12666, 2, 61, 95.95, -16.73, 0.82901, 63, 66.29, 56.89, 0.17099, 2, 55, 24.37, -35.14, 0.11669, 62, 4.15, -13.12, 0.88331, 2, 62, 37.77, -6.68, 0.99998, 63, -47.79, -18, 2e-05, 4, 60, 93.26, -86.93, 0.00108, 61, 30.58, -81.37, 0.00221, 62, 82.84, 0.89, 0.99279, 63, -5.62, -0.4, 0.00392, 2, 61, 78.27, -66.24, 0.06167, 63, 43.41, 9.55, 0.93833, 2, 61, 116.14, -56, 0.13927, 63, 82.16, 15.68, 0.86073], "hull": 30, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 0, 58, 0, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 32, 76, 78, 78, 80, 80, 82, 84, 86, 86, 88, 88, 90, 92, 94, 94, 96, 96, 98, 98, 100, 102, 104, 104, 106, 106, 108, 110, 112, 112, 114, 114, 116, 116, 118], "width": 260, "height": 394}}}}], "events": {"atk": {}}, "animations": {"boss_attack1": {"bones": {"bone3": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 5.22, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -28.55, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone63": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 92.18, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 11.94, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone64": {"rotate": [{"angle": -12.09, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -18.53, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -78.35, "curve": 0.243, "c3": 0.674, "c4": 0.69}, {"time": 0.6, "angle": -12.09}]}, "bone65": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 10.7, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -23.59, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone69": {"rotate": [{"angle": -7.92, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 10.7, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -23.59, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.6, "angle": -7.92}]}, "bone66": {"rotate": [{"angle": -7.92, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 10.7, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -23.59, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.6, "angle": -7.92}]}, "bone67": {"rotate": [{"angle": -7.92, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 10.7, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -23.59, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.6, "angle": -7.92}]}, "bone4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -4.19, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 0.04, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone5": {"rotate": [{"angle": 0.01, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -4.19, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 0.04, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.6, "angle": 0.01}]}, "bone6": {"rotate": [{"angle": 0.02, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -4.19, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 0.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6, "angle": 0.02}]}, "zm2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 16.16, "y": -3.67, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -6.14, "y": -4.26, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone7": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -2.26, "y": -3.31, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -2.51, "y": 1.94, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone130": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -8.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone131": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -8.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone126": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -8.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone127": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -8.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone128": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -8.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone129": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -8.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone116": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -8.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone117": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -8.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone114": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -8.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone115": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -8.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone107": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -8.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone108": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -8.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone109": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -8.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone112": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -8.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone113": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -8.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone110": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -8.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone111": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -8.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone123": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 5.08, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.39, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone124": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 5.08, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.39, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone125": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 5.08, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.39, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone121": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 5.08, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.39, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone122": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 5.08, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.39, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone118": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 5.08, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.39, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone119": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 5.08, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.39, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone120": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 5.08, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.39, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone54": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -6.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 6.29, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone55": {"rotate": [{"angle": 1.16, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -6.49, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 6.29, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.6, "angle": 1.16}]}, "bone56": {"rotate": [{"angle": 3.14, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.49, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 6.29, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6, "angle": 3.14}]}, "bone61": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -6.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 6.29, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone62": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -6.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 6.29, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone59": {"rotate": [{"angle": 1.16, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -6.49, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 6.29, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.6, "angle": 1.16}]}, "bone60": {"rotate": [{"angle": 1.16, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -6.49, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 6.29, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.6, "angle": 1.16}]}, "bone57": {"rotate": [{"angle": 3.14, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.49, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 6.29, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6, "angle": 3.14}]}, "bone58": {"rotate": [{"angle": 3.14, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.49, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 6.29, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6, "angle": 3.14}]}, "bone178": {"rotate": [{"angle": 3.14, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.49, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 6.29, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6, "angle": 3.14}]}, "bone45": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 5.98, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -3.52, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone47": {"rotate": [{"angle": -1.76, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 5.98, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -3.52, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6, "angle": -1.76}]}, "bone52": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 5.98, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -3.52, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone53": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 5.98, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -3.52, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone50": {"rotate": [{"angle": -0.65, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 5.98, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -3.52, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.6, "angle": -0.65}]}, "bone51": {"rotate": [{"angle": -0.65, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 5.98, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -3.52, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.6, "angle": -0.65}]}, "bone48": {"rotate": [{"angle": -1.76, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 5.98, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -3.52, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6, "angle": -1.76}]}, "bone49": {"rotate": [{"angle": -1.76, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 5.98, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -3.52, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6, "angle": -1.76}]}, "bone46": {"rotate": [{"angle": -0.65, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 5.98, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -3.52, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.6, "angle": -0.65}]}, "bone10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -3.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 2.36, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone11": {"rotate": [{"angle": 0.79, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -3.49, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 2.36, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.6, "angle": 0.79}]}, "weiba": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -4.04, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 3.32, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone34": {"rotate": [{"angle": 0.21, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -4.04, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 3.32, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 0.6, "angle": 0.21}]}, "bone35": {"rotate": [{"angle": 0.61, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -4.04, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 3.32, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.6, "angle": 0.61}]}, "bone36": {"rotate": [{"angle": 1.66, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -4.04, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 3.32, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6, "angle": 1.66}]}, "bone37": {"rotate": [{"angle": 2.71, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -4.04, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 3.32, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.6, "angle": 2.71}]}, "bone73": {"rotate": [{"angle": 3.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.28, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 7.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6, "angle": 3.65}]}, "bone84": {"rotate": [{"angle": 5.94, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 0.1333, "angle": 1.35, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -5.28, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 7.3, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.6, "angle": 5.94}]}, "bone83": {"rotate": [{"angle": 3.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.28, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 7.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6, "angle": 3.65}]}, "bone85": {"rotate": [{"angle": 7.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "angle": 3.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -5.28, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 7.3}]}, "bone86": {"rotate": [{"angle": 4.98, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": 7.3, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.1333, "angle": 4.85, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -5.28, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.6, "angle": 4.98}]}, "bone74": {"rotate": [{"angle": 5.94, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 0.1333, "angle": 1.35, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -5.28, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 7.3, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.6, "angle": 5.94}]}, "bone75": {"rotate": [{"angle": 7.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "angle": 3.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -5.28, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 7.3}]}, "bone76": {"rotate": [{"angle": 4.98, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": 7.3, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.1333, "angle": 4.85, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -5.28, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.6, "angle": 4.98}]}, "bone82": {"rotate": [{"angle": 5.94, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 0.1333, "angle": 1.35, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -5.28, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 7.3, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.6, "angle": 5.94}]}, "bone77": {"rotate": [{"angle": 3.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.28, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 7.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6, "angle": 3.65}]}, "bone78": {"rotate": [{"angle": 3.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.28, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 7.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6, "angle": 3.65}]}, "bone81": {"rotate": [{"angle": 3.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.28, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 7.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6, "angle": 3.65}]}, "bone79": {"rotate": [{"angle": 3.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.28, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 7.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6, "angle": 3.65}]}, "bone80": {"rotate": [{"angle": 3.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.28, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 7.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6, "angle": 3.65}]}, "bone180": {"translate": [{"x": -1.61, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -5.67, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6, "x": -1.61}]}, "bone181": {"translate": [{"x": -4.06, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -5.67, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6, "x": -4.06}]}, "bone182": {"translate": [{"x": -5.67, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -5.67}]}, "bone179": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -2.65, "y": -7.94, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone186": {"translate": [{"x": 4.66, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 16.42, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6, "x": 4.66}]}, "bone187": {"translate": [{"x": 12.44, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 16.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.6, "x": 12.44}]}, "bone188": {"translate": [{"x": 16.42, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 16.42}]}, "bone189": {"translate": [{"x": 11.76, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "x": 16.42, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6, "x": 11.76}]}, "bone185": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 16.42, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone183": {"translate": [{"x": -3.03, "y": -3.03, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -10.69, "y": -10.69, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6, "x": -3.03, "y": -3.03}]}, "bone191": {"translate": [{"x": -3.83, "y": 2.73, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -5.34, "y": 3.82, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6, "x": -3.83, "y": 2.73}]}, "bone190": {"translate": [{"x": -1.52, "y": 1.08, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -5.34, "y": 3.82, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6, "x": -1.52, "y": 1.08}]}, "bone192": {"translate": [{"x": -5.34, "y": 3.82, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -5.34, "y": 3.82}]}, "bone193": {"translate": [{"x": -3.83, "y": 2.73, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "x": -5.34, "y": 3.82, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6, "x": -3.83, "y": 2.73}]}, "bone184": {"translate": [{"x": -0.46, "y": 1.25, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2, "x": -1.62, "y": 4.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6, "x": -0.46, "y": 1.25}]}, "bone195": {"translate": [{"x": -2.97, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -10.47, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6, "x": -2.97}]}, "bone200": {"translate": [{"x": -7.5, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -10.47, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6, "x": -7.5}]}, "bone197": {"translate": [{"x": -7.07, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.1, "x": -10.47, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 0.6, "x": -7.07}]}, "bone199": {"translate": [{"x": -10.47, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -10.47}]}, "bone198": {"translate": [{"x": -2.97, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2, "x": -10.47, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6, "x": -2.97}]}, "bone196": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -10.47, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "bone206": {"translate": [{"x": 9.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "x": 13.32, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6, "x": 9.54}]}, "bone205": {"translate": [{"x": 13.32, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 13.32}]}, "bone204": {"translate": [{"x": 9.54, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 13.32, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6, "x": 9.54}]}, "bone203": {"translate": [{"x": 3.78, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 13.32, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6, "x": 3.78}]}, "bone202": {"translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3, "x": -8.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6}]}}, "events": [{"time": 0.3, "name": "atk"}]}, "boss_idle": {"slots": {"qiongqi068": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1, "color": "ffffff00", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2, "color": "ffffffff"}]}, "qiongqi067": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1, "color": "ffffff00", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2, "color": "ffffffff"}]}}, "bones": {"bone4": {"rotate": [{"angle": 1.32, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.4667, "angle": 2.42, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "angle": 1.32}]}, "bone5": {"rotate": [{"angle": 0.31, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.8, "angle": 2.42, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.4667, "angle": 0.69, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.8, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "angle": 0.31}]}, "bone6": {"rotate": [{"angle": 0.16, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 2.42, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.4667, "angle": 1.73, "curve": 0.336, "c2": 0.34, "c3": 0.714, "c4": 0.82}, {"time": 2, "angle": 0.16}]}, "bone7": {"translate": [{"x": -0.51, "y": 0.48, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.8, "x": -3.89, "y": 3.66, "curve": 0.351, "c2": 0.39, "c3": 0.701, "c4": 0.78}, {"time": 1.4667, "x": -0.74, "y": 0.7, "curve": 0.371, "c2": 0.63, "c3": 0.71}, {"time": 1.8, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "x": -0.51, "y": 0.48}]}, "bone9": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": -4.96, "y": 0.55, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone10": {"rotate": [{"angle": -0.37, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.8, "angle": -2.87, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.4667, "angle": -0.81, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.8, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "angle": -0.37}]}, "bone11": {"rotate": [{"angle": -0.28, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -2.87, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.4667, "angle": -2.17, "curve": 0.328, "c2": 0.32, "c3": 0.705, "c4": 0.79}, {"time": 2, "angle": -0.28}]}, "bone12": {"rotate": [{"angle": -1.24}]}, "bone13": {"rotate": [{"angle": 1.88}]}, "bone14": {"rotate": [{"angle": -1.8}]}, "bone19": {"rotate": [{"angle": 0.29, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.4667, "angle": 10.22, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -11.56, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "angle": 0.29}]}, "bone20": {"rotate": [{"angle": -1.95, "curve": 0.319, "c2": 0.29, "c3": 0.66, "c4": 0.64}, {"time": 0.4333, "angle": 10.22, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.1, "angle": -5.38, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.4333, "angle": -11.56, "curve": 0.328, "c3": 0.661, "c4": 0.34}, {"time": 1.4667, "angle": -11.42, "curve": 0.277, "c2": 0.04, "c3": 0.623, "c4": 0.44}, {"time": 2, "angle": -1.95}]}, "bone21": {"rotate": [{"angle": -11.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1, "angle": 10.22, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.3333, "angle": 4.04, "curve": 0.326, "c2": 0.31, "c3": 0.662, "c4": 0.65}, {"time": 1.4667, "angle": 0.29, "curve": 0.338, "c2": 0.35, "c3": 0.68, "c4": 0.71}, {"time": 1.7, "angle": -6.26, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2, "angle": -11.56}]}, "bone22": {"rotate": [{"angle": 1.3, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.7, "angle": 5.38, "curve": 0.357, "c2": 0.41, "c3": 0.713, "c4": 0.83}, {"time": 1.4667, "angle": 0.57, "curve": 0.363, "c2": 0.64, "c3": 0.699}, {"time": 1.7, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2, "angle": 1.3}]}, "bone23": {"rotate": [{"angle": 0.02, "curve": 0.339, "c2": 0.66, "c3": 0.672}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 5.38, "curve": 0.34, "c2": 0.36, "c3": 0.681, "c4": 0.71}, {"time": 1.4667, "angle": 2.35, "curve": 0.348, "c2": 0.39, "c3": 0.684, "c4": 0.73}, {"time": 1.7, "angle": 1.03, "curve": 0.366, "c2": 0.57, "c3": 0.704, "c4": 0.93}, {"time": 2, "angle": 0.02}]}, "bone24": {"rotate": [{"angle": -7.39, "curve": 0.299, "c2": 0.22, "c3": 0.649, "c4": 0.61}, {"time": 0.6667, "angle": 10.22, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.4667, "angle": -8.72, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.6667, "angle": -11.56, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": -7.39}]}, "bone25": {"rotate": [{"angle": -11.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1, "angle": 10.22, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1.4667, "angle": 0.26, "curve": 0.336, "c2": 0.34, "c3": 0.676, "c4": 0.69}, {"time": 1.6667, "angle": -5.38, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2, "angle": -11.56}]}, "bone26": {"rotate": [{"angle": -10.66, "curve": 0.273, "c2": 0.11, "c3": 0.636, "c4": 0.56}, {"time": 0.8667, "angle": 10.22, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.4667, "angle": -3.55, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.8667, "angle": -11.56, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 2, "angle": -10.66}]}, "bone27": {"rotate": [{"angle": 0.37, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.8667, "angle": 5.38, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 1.4667, "angle": 1.38, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.8667, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 2, "angle": 0.37}]}, "bone28": {"rotate": [{"angle": -11.01, "curve": 0.267, "c2": 0.09, "c3": 0.634, "c4": 0.54}, {"time": 0.9, "angle": 10.22, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 1.4667, "angle": -2.59, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 1.9, "angle": -11.56, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 2, "angle": -11.01}]}, "zm1": {"translate": [{"x": 13.66, "y": 6.73, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": 0.93, "y": 6.51, "curve": 0.348, "c2": 0.43, "c3": 0.682, "c4": 0.77}, {"time": 1.3333, "x": 45.78, "y": 7.27, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 13.66, "y": 6.73}]}, "weiba": {"rotate": [{"angle": -0.44, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.8, "angle": -3.38, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.4667, "angle": -0.96, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.8, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "angle": -0.44}]}, "bone34": {"rotate": [{"angle": -0.23, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -3.38, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.4667, "angle": -2.42, "curve": 0.336, "c2": 0.34, "c3": 0.714, "c4": 0.82}, {"time": 2, "angle": -0.23}]}, "bone35": {"rotate": [{"angle": -1.54, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -3.38, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "angle": -1.54}]}, "bone36": {"rotate": [{"angle": -2.94, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.8, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.4667, "angle": -2.42, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.8, "angle": -3.38, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "angle": -2.94}]}, "bone37": {"rotate": [{"angle": -3.15, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "angle": -3.38, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.4667, "angle": -0.96, "curve": 0.336, "c2": 0.34, "c3": 0.714, "c4": 0.82}, {"time": 2, "angle": -3.15}]}, "bone31": {"rotate": [{"angle": -0.44}]}, "bone32": {"rotate": [{"angle": -1.23}]}, "bone33": {"rotate": [{"angle": 0.1}]}, "bone40": {"rotate": [{"angle": 1.17}]}, "bone41": {"rotate": [{"angle": -2.77}]}, "bone63": {"rotate": [{"angle": 1.52, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.4, "angle": 1.55, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.2, "angle": 11.88, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.8667, "angle": 3.37, "curve": 0.347, "c2": 0.38, "c3": 0.683, "c4": 0.73}, {"time": 2, "angle": 1.52}]}, "bone64": {"rotate": [{"angle": 6.45, "curve": 0.352, "c2": 0.4, "c3": 0.711, "c4": 0.82}, {"time": 0.4, "angle": 0.81, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": 11.88, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.8667, "angle": 8.51, "curve": 0.326, "c2": 0.31, "c3": 0.662, "c4": 0.65}, {"time": 2, "angle": 6.45}]}, "bone65": {"rotate": [{"angle": 11.1, "curve": 0.289, "c2": 0.18, "c3": 0.648, "c4": 0.6}, {"time": 0.4, "angle": 5.42, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": 11.88, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 2, "angle": 11.1}]}, "bone66": {"rotate": [{"angle": 10.36, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "angle": 11.88, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.4, "angle": 10.33, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.2, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.8667, "angle": 8.51, "curve": 0.347, "c2": 0.38, "c3": 0.683, "c4": 0.73}, {"time": 2, "angle": 10.36}]}, "bone67": {"rotate": [{"angle": 10.36, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "angle": 11.88, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.4, "angle": 10.33, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.2, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.8667, "angle": 8.51, "curve": 0.347, "c2": 0.38, "c3": 0.683, "c4": 0.73}, {"time": 2, "angle": 10.36}]}, "bone68": {"rotate": [{"angle": 10.36, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "angle": 11.88, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.4, "angle": 10.33, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.2, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.8667, "angle": 8.51, "curve": 0.347, "c2": 0.38, "c3": 0.683, "c4": 0.73}, {"time": 2, "angle": 10.36}]}, "bone69": {"rotate": [{"angle": 10.36, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "angle": 11.88, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.4, "angle": 10.33, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.2, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.8667, "angle": 8.51, "curve": 0.347, "c2": 0.38, "c3": 0.683, "c4": 0.73}, {"time": 2, "angle": 10.36}]}, "bone70": {"rotate": [{"angle": 16.5, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.3667, "angle": 24.44, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 0.4, "angle": 24.16, "curve": 0.264, "c2": 0.06, "c3": 0.752}, {"time": 1.3667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.8667, "angle": 12.22, "curve": 0.336, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 2, "angle": 16.5}]}, "bone73": {"rotate": [{"angle": -3.27, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 0.8, "angle": 10.27, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.4667, "angle": -0.3, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.8, "angle": -4.48, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2, "angle": -3.27}]}, "bone74": {"rotate": [{"angle": -3.48, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "angle": -4.48, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.1333, "angle": 10.27, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.4667, "angle": 6.08, "curve": 0.336, "c2": 0.34, "c3": 0.714, "c4": 0.82}, {"time": 2, "angle": -3.48}]}, "bone75": {"rotate": [{"angle": 2.24, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.4667, "angle": -4.48, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 10.27, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "angle": 2.24}]}, "bone76": {"rotate": [{"angle": 8.35, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.8, "angle": -4.48, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.4667, "angle": 6.08, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.8, "angle": 10.27, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "angle": 8.35}]}, "bone77": {"rotate": [{"angle": 9.27, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "angle": 10.27, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -4.48, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.4667, "angle": -0.3, "curve": 0.336, "c2": 0.34, "c3": 0.714, "c4": 0.82}, {"time": 2, "angle": 9.27}]}, "bone78": {"rotate": [{"angle": 1.48, "curve": 0.317, "c2": 0.28, "c3": 0.658, "c4": 0.64}, {"time": 0.4667, "angle": 10.27, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -4.48, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 2, "angle": 1.48}]}, "bone79": {"rotate": [{"angle": -4.44, "curve": 0.311, "c2": 0.1, "c3": 0.645, "c4": 0.45}, {"time": 0.1667, "angle": -3.27, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 0.9667, "angle": 10.27, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.6333, "angle": -0.3, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.9667, "angle": -4.48, "curve": 0.328, "c3": 0.661, "c4": 0.34}, {"time": 2, "angle": -4.44}]}, "bone80": {"rotate": [{"angle": -0.96, "curve": 0.355, "c2": 0.42, "c3": 0.693, "c4": 0.77}, {"time": 0.1667, "angle": -3.48, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.3, "angle": -4.48, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3, "angle": 10.27, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.6333, "angle": 6.08, "curve": 0.326, "c2": 0.31, "c3": 0.68, "c4": 0.71}, {"time": 2, "angle": -0.96}]}, "bone81": {"rotate": [{"angle": 1.48, "curve": 0.317, "c2": 0.28, "c3": 0.658, "c4": 0.64}, {"time": 0.4667, "angle": 10.27, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -4.48, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 2, "angle": 1.48}]}, "bone82": {"rotate": [{"angle": 2.24, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.4667, "angle": -4.48, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 10.27, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "angle": 2.24}]}, "bone83": {"rotate": [{"angle": -3.27, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 0.8, "angle": 10.27, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.4667, "angle": -0.3, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.8, "angle": -4.48, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2, "angle": -3.27}]}, "bone84": {"rotate": [{"angle": -3.48, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "angle": -4.48, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.1333, "angle": 10.27, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.4667, "angle": 6.08, "curve": 0.336, "c2": 0.34, "c3": 0.714, "c4": 0.82}, {"time": 2, "angle": -3.48}]}, "bone85": {"rotate": [{"angle": 2.24, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.4667, "angle": -4.48, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 10.27, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "angle": 2.24}]}, "bone86": {"rotate": [{"angle": 8.35, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.8, "angle": -4.48, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.4667, "angle": 6.08, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.8, "angle": 10.27, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "angle": 8.35}]}, "bone87": {"rotate": [{"angle": 2.17, "curve": 0.317, "c2": 0.28, "c3": 0.658, "c4": 0.64}, {"time": 0.4667, "angle": 5.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.4667, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 2, "angle": 2.17}]}, "bone88": {"rotate": [{"angle": 2.17, "curve": 0.317, "c2": 0.28, "c3": 0.658, "c4": 0.64}, {"time": 0.4667, "angle": 5.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.4667, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 2, "angle": 2.17}]}, "bone89": {"rotate": [{"angle": 2.17, "curve": 0.317, "c2": 0.28, "c3": 0.658, "c4": 0.64}, {"time": 0.4667, "angle": 5.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.4667, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 2, "angle": 2.17}]}, "bone90": {"rotate": [{"angle": 2.17, "curve": 0.317, "c2": 0.28, "c3": 0.658, "c4": 0.64}, {"time": 0.4667, "angle": 5.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.4667, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 2, "angle": 2.17}]}, "bone91": {"rotate": [{"angle": 2.17, "curve": 0.317, "c2": 0.28, "c3": 0.658, "c4": 0.64}, {"time": 0.4667, "angle": 5.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.4667, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 2, "angle": 2.17}]}, "bone92": {"rotate": [{"angle": 2.17, "curve": 0.317, "c2": 0.28, "c3": 0.658, "c4": 0.64}, {"time": 0.4667, "angle": 5.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.4667, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 2, "angle": 2.17}]}, "bone107": {"rotate": [{"angle": 6.98, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 0.8, "angle": -7.06, "curve": 0.351, "c2": 0.39, "c3": 0.701, "c4": 0.78}, {"time": 1.4667, "angle": 5.3, "curve": 0.371, "c2": 0.63, "c3": 0.71}, {"time": 1.8, "angle": 8.23, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2, "angle": 6.98}]}, "bone108": {"rotate": [{"angle": 7.63, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.1333, "angle": 8.23, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.1333, "angle": -7.06, "curve": 0.337, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 1.4667, "angle": -0.4, "curve": 0.368, "c2": 0.48, "c3": 0.713, "c4": 0.87}, {"time": 2, "angle": 7.63}]}, "bone109": {"rotate": [{"angle": 3.22, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.4667, "angle": 8.23, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.4667, "angle": -7.06, "curve": 0.344, "c2": 0.37, "c3": 0.688, "c4": 0.74}, {"time": 2, "angle": 3.22}]}, "bone110": {"rotate": [{"angle": 8.19, "curve": 0.339, "c2": 0.66, "c3": 0.672}, {"time": 0.0333, "angle": 8.23, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.0333, "angle": -7.06, "curve": 0.34, "c2": 0.36, "c3": 0.681, "c4": 0.71}, {"time": 1.4667, "angle": 1.54, "curve": 0.348, "c2": 0.39, "c3": 0.684, "c4": 0.73}, {"time": 1.7, "angle": 5.3, "curve": 0.366, "c2": 0.57, "c3": 0.704, "c4": 0.93}, {"time": 2, "angle": 8.19}]}, "bone111": {"rotate": [{"angle": 4.85, "curve": 0.373, "c2": 0.62, "c3": 0.713}, {"time": 0.3667, "angle": 8.23, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3667, "angle": -7.06, "curve": 0.334, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 1.4667, "angle": -5.02, "curve": 0.338, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 1.7, "angle": -0.4, "curve": 0.349, "c2": 0.39, "c3": 0.686, "c4": 0.74}, {"time": 2, "angle": 4.85}]}, "bone112": {"rotate": [{"angle": 0.86, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.6, "angle": 8.23, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 1.4667, "angle": -4.35, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 1.6, "angle": -7.06, "curve": 0.337, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 1.9333, "angle": -0.4, "curve": 0.336, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 2, "angle": 0.86}]}, "bone113": {"rotate": [{"angle": -5.7, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.9333, "angle": 8.23, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 1.4667, "angle": 2.05, "curve": 0.317, "c2": 0.28, "c3": 0.658, "c4": 0.64}, {"time": 1.9333, "angle": -7.06, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 2, "angle": -5.7}]}, "bone118": {"rotate": [{"angle": 4.09, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.4667, "angle": 13.74, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -7.43, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "angle": 4.09}]}, "bone119": {"rotate": [{"angle": -4.68, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.8, "angle": 13.74, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.4667, "angle": -1.42, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.8, "angle": -7.43, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "angle": -4.68}]}, "bone120": {"rotate": [{"angle": -5.99, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "angle": -7.43, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 13.74, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.4667, "angle": 7.74, "curve": 0.336, "c2": 0.34, "c3": 0.714, "c4": 0.82}, {"time": 2, "angle": -5.99}]}, "bone121": {"rotate": [{"angle": -6.04, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "angle": -7.43, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 13.74, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.4667, "angle": 7.72, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 1.8, "angle": -1.42, "curve": 0.356, "c2": 0.42, "c3": 0.696, "c4": 0.78}, {"time": 2, "angle": -6.04}]}, "bone122": {"rotate": [{"angle": 2.25, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.4667, "angle": -7.43, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 13.74, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.8, "angle": 7.74, "curve": 0.324, "c2": 0.31, "c3": 0.664, "c4": 0.66}, {"time": 2, "angle": 2.25}]}, "bone123": {"rotate": [{"angle": 2.17, "curve": 0.317, "c2": 0.28, "c3": 0.658, "c4": 0.64}, {"time": 0.4667, "angle": 5.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.4667, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 2, "angle": 2.17}]}, "bone124": {"rotate": [{"angle": -22.45, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 0.8, "angle": 19.08, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.4667, "angle": -13.31, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.8, "angle": -26.15, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2, "angle": -22.45}]}, "bone125": {"rotate": [{"angle": -23.08, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "angle": -26.15, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.1333, "angle": 19.08, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.4667, "angle": 6.24, "curve": 0.336, "c2": 0.34, "c3": 0.714, "c4": 0.82}, {"time": 2, "angle": -23.08}]}, "bone126": {"rotate": [{"angle": -2.54, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.4667, "angle": -14.77, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 12.05, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "angle": -2.54}]}, "bone127": {"rotate": [{"angle": 9.27, "curve": 0.332, "c2": 0.33, "c3": 0.676, "c4": 0.7}, {"time": 0.8, "angle": -11.79, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.4667, "angle": 6.95, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.8, "angle": 14.37, "curve": 0.329, "c2": 0.32, "c3": 0.663, "c4": 0.65}, {"time": 2, "angle": 9.27}]}, "bone128": {"rotate": [{"angle": 14.2, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "angle": 15.9, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.1333, "angle": -9.18, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.4667, "angle": -2.06, "curve": 0.336, "c2": 0.34, "c3": 0.714, "c4": 0.82}, {"time": 2, "angle": 14.2}]}, "bone129": {"rotate": [{"angle": 6.05, "curve": 0.349, "c2": 0.39, "c3": 0.686, "c4": 0.74}, {"time": 0.8, "angle": -14.77, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.4667, "angle": 4.44, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.8, "angle": 12.05, "curve": 0.336, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 2, "angle": 6.05}]}, "bone130": {"rotate": [{"angle": 11.04, "curve": 0.351, "c2": 0.4, "c3": 0.689, "c4": 0.75}, {"time": 0.9667, "angle": -14.77, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.4667, "angle": -1.36, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.9667, "angle": 12.05, "curve": 0.334, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 2, "angle": 11.04}]}, "bone131": {"rotate": [{"angle": 8.04, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3, "angle": 14.37, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 1.3, "angle": -11.79, "curve": 0.29, "c3": 0.629, "c4": 0.37}, {"time": 1.4667, "angle": -9.26, "curve": 0.295, "c2": 0.21, "c3": 0.667, "c4": 0.67}, {"time": 1.9667, "angle": 6.95, "curve": 0.336, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 2, "angle": 8.04}]}, "bone132": {"rotate": [{"angle": 9.73, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -7.29, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 9.73}]}, "bone133": {"rotate": [{"angle": 9.73, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -7.29, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 9.73}]}, "bone134": {"rotate": [{"angle": 9.73, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -7.29, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 9.73}]}, "bone135": {"rotate": [{"angle": 9.73, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -7.29, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 9.73}]}, "bone136": {"rotate": [{"angle": 9.73, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -7.29, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 9.73}]}, "bone137": {"rotate": [{"angle": 9.73, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -7.29, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 9.73}]}, "bone138": {"rotate": [{"angle": 9.73, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -7.29, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 9.73}]}, "bone139": {"rotate": [{"angle": 9.73, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -7.29, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 9.73}]}, "bone140": {"rotate": [{"angle": 9.73, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -7.29, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 9.73}]}, "bone141": {"rotate": [{"angle": 7.37, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "angle": 8.6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.1333, "angle": -10.3, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 1.7667, "angle": 2.45, "curve": 0.357, "c2": 0.42, "c3": 0.7, "c4": 0.79}, {"time": 2, "angle": 7.37}]}, "bone142": {"rotate": [{"angle": 0.79, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.4333, "angle": 8.6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.4333, "angle": -10.3, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.7667, "angle": -4.93, "curve": 0.324, "c2": 0.3, "c3": 0.666, "c4": 0.66}, {"time": 2, "angle": 0.79}]}, "bone143": {"rotate": [{"angle": 2.45, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.3667, "angle": 8.6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3667, "angle": -10.3, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 2, "angle": 2.45}]}, "bone144": {"rotate": [{"angle": -4.93, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 8.6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6667, "angle": -10.3, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -4.93}]}, "bone145": {"rotate": [{"angle": -10.3, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.6, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -10.3}]}, "bone146": {"rotate": [{"angle": -7.83, "curve": 0.302, "c2": 0.23, "c3": 0.664, "c4": 0.66}, {"time": 0.4333, "angle": 2.45, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.8, "angle": 8.6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.8, "angle": -10.3, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "angle": -7.83}]}, "bone147": {"rotate": [{"angle": -7.78, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 0.1, "angle": -10.3, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.4333, "angle": -4.93, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.1, "angle": 8.6, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 2, "angle": -7.78}]}, "bone148": {"rotate": [{"angle": -2.51, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.4333, "angle": -10.3, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": 8.6, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2, "angle": -2.51}]}, "bone149": {"rotate": [{"angle": -4.92, "curve": 0.324, "c2": 0.31, "c3": 0.671, "c4": 0.68}, {"time": 0.3, "angle": 2.45, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.6667, "angle": 8.6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6667, "angle": -10.3, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -4.92}]}, "bone150": {"rotate": [{"angle": -10.19, "curve": 0.276, "c2": 0.08, "c3": 0.625, "c4": 0.48}, {"time": 0.3, "angle": -4.93, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.9667, "angle": 8.6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.9667, "angle": -10.3, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 2, "angle": -10.19}]}, "bone153": {"translate": [{"x": -2, "y": 0.86, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": -2.96, "y": 1.01, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 0.41, "y": 0.5, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -2, "y": 0.86}]}, "bone154": {"translate": [{"x": 1.56, "y": 0.61, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": -2.96, "y": 1.01, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6667, "x": 3.35, "y": 0.45, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 1.56, "y": 0.61}]}, "bone156": {"translate": [{"x": -1.71, "y": 0.6, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": -2.96, "y": 1.01, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "x": 1.46, "y": -0.43, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -1.71, "y": 0.6}]}, "bone157": {"translate": [{"x": 0.75, "y": -0.47, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": -2.96, "y": 1.01, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6667, "x": 2.22, "y": -1.06, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 0.75, "y": -0.47}]}, "bone114": {"rotate": [{"angle": 12.18, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.8, "angle": -0.52, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.4667, "angle": 9.94, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.8, "angle": 14.09, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "angle": 12.18}]}, "bone115": {"rotate": [{"angle": 13.09, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "angle": 14.09, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -0.52, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.4667, "angle": 3.62, "curve": 0.336, "c2": 0.34, "c3": 0.714, "c4": 0.82}, {"time": 2, "angle": 13.09}]}, "bone116": {"rotate": [{"angle": 0.47, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "angle": -0.52, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.4667, "angle": 3.62, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.1333, "angle": 14.09, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.4667, "angle": 9.94, "curve": 0.336, "c2": 0.34, "c3": 0.714, "c4": 0.82}, {"time": 2, "angle": 0.47}]}, "bone117": {"rotate": [{"angle": 6.14, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.4667, "angle": -0.52, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 14.09, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "angle": 6.14}]}, "bone166": {"rotate": [{"angle": -3.4, "curve": 0.332, "c2": 0.33, "c3": 0.676, "c4": 0.7}, {"time": 0.2667, "angle": -6.43, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6, "angle": -8.9, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": -0.18, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "angle": -3.4}]}, "bone167": {"rotate": [{"angle": -0.35, "curve": 0.287, "c2": 0.13, "c3": 0.632, "c4": 0.52}, {"time": 0.2667, "angle": -2.65, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.9333, "angle": -8.9, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": -0.18, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 2, "angle": -0.35}]}, "bone168": {"rotate": [{"angle": -1.94, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2667, "angle": -0.18, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -8.9, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 2, "angle": -1.94}]}, "bone172": {"rotate": [{"angle": -6.43, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -8.9, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -0.18, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -6.43}]}, "bone173": {"rotate": [{"angle": -2.65, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -8.9, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -0.18, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -2.65}]}, "bone174": {"rotate": [{"angle": -6.43, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -8.9, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -0.18, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -6.43}]}, "bone175": {"rotate": [{"angle": -2.65, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -8.9, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -0.18, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -2.65}]}, "bone176": {"rotate": [{"angle": -0.18, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -8.9, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -0.18}]}, "bone177": {"rotate": [{"angle": -2.65, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -0.18, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -8.9, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -2.65}]}, "bone169": {"rotate": [{"angle": -1.58, "curve": 0.329, "c2": 0.32, "c3": 0.676, "c4": 0.69}, {"time": 0.3, "angle": 2.77, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6333, "angle": 5.94, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": -5.23, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 2, "angle": -1.58}]}, "bone170": {"rotate": [{"angle": -5.17, "curve": 0.276, "c2": 0.08, "c3": 0.625, "c4": 0.48}, {"time": 0.3, "angle": -2.06, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.9667, "angle": 5.94, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": -5.23, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 2, "angle": -5.17}]}, "bone171": {"rotate": [{"angle": -2.53, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3, "angle": -5.23, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": 5.94, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2, "angle": -2.53}]}, "bone163": {"rotate": [{"angle": 2.77, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 5.94, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -5.23, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 2.77}]}, "bone164": {"rotate": [{"angle": -2.06, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 5.94, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -5.23, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -2.06}]}, "bone165": {"rotate": [{"angle": -5.23, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 5.94, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -5.23}]}, "bone180": {"translate": [{"x": -1.61, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -5.67, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -1.61}]}, "bone181": {"translate": [{"x": -4.06, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -5.67, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -4.06}]}, "bone182": {"translate": [{"x": -5.67, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -5.67}]}, "bone179": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": -2.65, "y": -7.94, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone186": {"translate": [{"x": 4.66, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 16.42, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 4.66}]}, "bone187": {"translate": [{"x": 12.44, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "x": 16.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2, "x": 12.44}]}, "bone188": {"translate": [{"x": 16.42, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 16.42}]}, "bone189": {"translate": [{"x": 11.76, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": 16.42, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 11.76}]}, "bone185": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 16.42, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone183": {"translate": [{"x": -3.03, "y": -3.03, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -10.69, "y": -10.69, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -3.03, "y": -3.03}]}, "bone191": {"translate": [{"x": -3.83, "y": 2.73, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -5.34, "y": 3.82, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -3.83, "y": 2.73}]}, "bone190": {"translate": [{"x": -1.52, "y": 1.08, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -5.34, "y": 3.82, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -1.52, "y": 1.08}]}, "bone192": {"translate": [{"x": -5.34, "y": 3.82, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -5.34, "y": 3.82}]}, "bone193": {"translate": [{"x": -3.83, "y": 2.73, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": -5.34, "y": 3.82, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -3.83, "y": 2.73}]}, "bone184": {"translate": [{"x": -0.46, "y": 1.25, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": -1.62, "y": 4.39, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -0.46, "y": 1.25}]}, "bone195": {"translate": [{"x": -2.97, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -10.47, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -2.97}]}, "bone200": {"translate": [{"x": -7.5, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -10.47, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -7.5}]}, "bone197": {"translate": [{"x": -7.07, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.3667, "x": -10.47, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 2, "x": -7.07}]}, "bone199": {"translate": [{"x": -10.47, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -10.47}]}, "bone198": {"translate": [{"x": -2.97, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": -10.47, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -2.97}]}, "bone196": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": -10.47, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone201": {"translate": [{"x": -2.97, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -10.47, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -2.97}]}, "bone194": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": -14.69, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone206": {"translate": [{"x": 9.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": 13.32, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 9.54}]}, "bone205": {"translate": [{"x": 13.32, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 13.32}]}, "bone204": {"translate": [{"x": 9.54, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 13.32, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 9.54}]}, "bone203": {"translate": [{"x": 3.78, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 13.32, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 3.78}]}, "bone202": {"translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1, "x": -8.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2}]}, "bone54": {"rotate": [{"angle": 3.53, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -7.55, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 3.53}]}, "bone55": {"rotate": [{"angle": 0.38, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 3.53, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -7.55, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 0.38}]}, "bone56": {"rotate": [{"angle": -4.41, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 3.53, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -7.55, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -4.41}]}, "bone61": {"rotate": [{"angle": 3.53, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -7.55, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 3.53}]}, "bone62": {"rotate": [{"angle": 0.38, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 3.53, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -7.55, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 0.38}]}, "bone59": {"rotate": [{"angle": 0.38, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 3.53, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -7.55, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 0.38}]}, "bone60": {"rotate": [{"angle": -4.4, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.3333, "angle": 0.38, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6667, "angle": 3.53, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -7.55, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -4.4}]}, "bone57": {"rotate": [{"angle": -4.41, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 3.53, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -7.55, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -4.41}]}, "bone58": {"rotate": [{"angle": -7.55, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3333, "angle": -4.41, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1, "angle": 3.53, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -7.55}]}, "bone178": {"rotate": [{"angle": -7.55, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3333, "angle": -4.41, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1, "angle": 3.53, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -7.55}]}, "bone45": {"rotate": [{"angle": -2.3, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 4.54, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -2.3}]}, "bone46": {"rotate": [{"angle": -0.36, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -2.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 4.54, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -0.36}]}, "bone47": {"rotate": [{"angle": 2.6, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -2.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6667, "angle": 4.54, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 2.6}]}, "bone48": {"rotate": [{"angle": 2.6, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -2.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6667, "angle": 4.54, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 2.6}]}, "bone49": {"rotate": [{"angle": 4.54, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3333, "angle": 2.6, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1, "angle": -2.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 4.54}]}, "bone50": {"rotate": [{"angle": -0.36, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -2.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 4.54, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -0.36}]}, "bone51": {"rotate": [{"angle": 2.59, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.3333, "angle": -0.36, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6667, "angle": -2.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6667, "angle": 4.54, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 2.59}]}, "bone52": {"rotate": [{"angle": -2.3, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 4.54, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -2.3}]}, "bone53": {"rotate": [{"angle": -0.36, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -2.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 4.54, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -0.36}]}}}, "die": {"slots": {"qiongqi068": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff00"}]}, "qiongqi032": {"color": [{"color": "ffffffe0", "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "color": "ffffff00"}]}, "qiongqi044": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff00"}]}, "qiongqi05": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff00"}]}, "qiongqi067": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff00"}]}, "qiongqi045": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff00"}]}, "qiongqi046": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff00"}]}, "qiongqi04": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff00"}]}, "qiongqi01": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffff00"}]}}, "bones": {"bone3": {"translate": [{"curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 0.1333, "x": -36.07, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 0.2667, "x": 69.89, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.5, "y": -64.57}]}, "bone4": {"rotate": [{"curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 0.1667, "angle": 1.15, "curve": 0.326, "c2": 0.31, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "angle": -1.5, "curve": 0.341, "c2": 0.36, "c3": 0.678, "c4": 0.7}, {"time": 0.3333, "angle": -3.18, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.5}], "translate": [{"time": 0.2667, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.5, "x": -3.89, "y": 4.5}]}, "bone5": {"rotate": [{"curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 0.1667, "angle": 1.15, "curve": 0.326, "c2": 0.31, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "angle": -1.5, "curve": 0.341, "c2": 0.36, "c3": 0.678, "c4": 0.7}, {"time": 0.3333, "angle": -3.18, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.5, "angle": 0.4}], "translate": [{"time": 0.2667, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.5, "x": -46.3, "y": 7.35}]}, "bone6": {"rotate": [{"curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 0.1667, "angle": 1.15, "curve": 0.326, "c2": 0.31, "c3": 0.667, "c4": 0.67}, {"time": 0.2667, "angle": -1.5, "curve": 0.341, "c2": 0.36, "c3": 0.678, "c4": 0.7}, {"time": 0.3333, "angle": -3.18, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.5, "angle": 13.75}], "translate": [{"time": 0.2667, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.5, "x": -32, "y": 10.81}]}, "bone12": {"rotate": [{"angle": -1.24, "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 0.2667, "angle": -4.17, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.5, "angle": -1.24}]}, "bone13": {"rotate": [{"angle": 1.88, "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 0.2667, "angle": 7.86, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.5, "angle": 1.88}]}, "bone14": {"rotate": [{"angle": -1.8, "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 0.2667, "angle": -4.89, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.5, "angle": -1.8}]}, "jio1": {"translate": [{"time": 0.2667, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.5, "x": -29.67, "y": 37.71}]}, "bone36": {"rotate": [{"time": 0.2667, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.5, "angle": 37.47}]}, "bone31": {"rotate": [{"angle": -0.44, "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 0.2667, "angle": -4.4, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.5, "angle": -0.44}]}, "bone32": {"rotate": [{"angle": -1.23, "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 0.2667, "angle": 3.37, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.5, "angle": -1.23}]}, "bone33": {"rotate": [{"angle": 0.1, "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 0.2667, "angle": -0.54, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.5, "angle": 0.1}]}, "bone40": {"rotate": [{"angle": 1.17, "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 0.2667, "angle": -0.41, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.5, "angle": 1.17}]}, "bone41": {"rotate": [{"angle": -2.77, "curve": 0.256, "c3": 0.619, "c4": 0.46}, {"time": 0.2667, "angle": 2.97, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.5, "angle": -2.77}]}, "bone45": {"rotate": [{"time": 0.2667, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.5, "angle": -43.51}]}, "bone46": {"rotate": [{"time": 0.2667, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.5, "angle": -43.51}]}, "bone47": {"rotate": [{"time": 0.2667, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.5, "angle": -43.51}]}, "bone54": {"rotate": [{"time": 0.2667, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.5, "angle": 35.11}]}, "bone55": {"rotate": [{"time": 0.2667, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.5, "angle": 35.11}]}, "bone56": {"rotate": [{"time": 0.2667, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.5, "angle": 60.05}]}, "bone57": {"rotate": [{"time": 0.2667, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.5, "angle": 55.51}]}, "bone58": {"rotate": [{"time": 0.2667, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.5, "angle": -37.53}]}, "bone59": {"rotate": [{"time": 0.2667, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.5, "angle": 55.51}]}, "bone60": {"rotate": [{"time": 0.2667, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.5, "angle": 55.51}]}, "bone61": {"rotate": [{"time": 0.2667, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.5, "angle": 55.51}]}, "bone62": {"rotate": [{"time": 0.2667, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.5, "angle": 55.51}]}, "bone87": {"translate": [{"time": 0.2667, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.5, "x": -36.8, "y": 1.23}]}, "bone88": {"translate": [{"time": 0.2667, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.5, "x": -43.85, "y": 6.43}]}, "bone89": {"translate": [{"time": 0.2667, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.5, "x": -35.48, "y": -4.38}]}, "bone90": {"translate": [{"time": 0.2667, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.5, "x": -31.36, "y": 12.65}]}, "bone175": {"translate": [{"time": 0.2667, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.5, "x": -36.8, "y": 1.23}]}, "bone177": {"translate": [{"time": 0.2667, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.5, "x": -43.85, "y": 6.43}]}, "bone178": {"rotate": [{"time": 0.2667, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.5, "angle": -37.53}]}}}, "hurt": {"bones": {"bone3": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": -6.76, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 26.12, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 2.48, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -6.75, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone5": {"rotate": [{"angle": -1.92, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 2.48, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -6.75, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": -1.92}]}, "bone6": {"rotate": [{"angle": -4.84, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 2.48, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.75, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -4.84}]}, "bone12": {"rotate": [{"angle": -1.24}]}, "bone13": {"rotate": [{"angle": 1.88}]}, "bone14": {"rotate": [{"angle": -1.8}]}, "weiba": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 7.8, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone34": {"rotate": [{"angle": 0.52, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 4.03, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3667, "angle": 0.52}]}, "bone35": {"rotate": [{"angle": 4.55, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 12.37, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": 4.55}]}, "bone36": {"rotate": [{"angle": 14.93, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 23.61, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3667, "angle": 14.93}]}, "bone37": {"rotate": [{"angle": -5.96, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.85, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3667, "angle": -5.96}]}, "bone31": {"rotate": [{"angle": -0.44}]}, "bone32": {"rotate": [{"angle": -1.23}]}, "bone33": {"rotate": [{"angle": 0.1}]}, "bone40": {"rotate": [{"angle": 1.17}]}, "bone41": {"rotate": [{"angle": -2.77}]}, "bone45": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2013, "angle": 5.82, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone46": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2013, "angle": 5.82, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone47": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2013, "angle": 5.82, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone48": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2013, "angle": 5.82, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone49": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2013, "angle": 5.82, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone50": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2013, "angle": 5.82, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone51": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2013, "angle": 5.82, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone52": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2013, "angle": 5.82, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone53": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2013, "angle": 5.82, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone54": {"rotate": [{"curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 0.2, "angle": -6.47, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.3667}]}, "bone55": {"rotate": [{"curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 0.2, "angle": -6.47, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.3667}]}, "bone56": {"rotate": [{"curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 0.2, "angle": -6.47, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.3667}]}, "bone57": {"rotate": [{"curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 0.2, "angle": -6.47, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.3667}]}, "bone58": {"rotate": [{"curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 0.2, "angle": -6.47, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.3667}]}, "bone59": {"rotate": [{"curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 0.2, "angle": -6.47, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.3667}]}, "bone60": {"rotate": [{"curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 0.2, "angle": -6.47, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.3667}]}, "bone61": {"rotate": [{"curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 0.2, "angle": -6.47, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.3667}]}, "bone62": {"rotate": [{"curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 0.2, "angle": -6.47, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.3667}]}, "bone178": {"rotate": [{"curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 0.2, "angle": -6.47, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.3667}]}, "bone179": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -2.65, "y": -7.94, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "bone180": {"translate": [{"x": -1.61, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -5.67, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "x": -1.61}]}, "bone181": {"translate": [{"x": -4.06, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -5.67, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "x": -4.06}]}, "bone182": {"translate": [{"x": -5.67, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": "stepped"}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -5.67}]}, "bone183": {"translate": [{"x": -3.03, "y": -3.03, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -10.69, "y": -10.69, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "x": -3.03, "y": -3.03}]}, "bone184": {"translate": [{"x": -0.46, "y": 1.25, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "x": -1.62, "y": 4.39, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "x": -0.46, "y": 1.25}]}, "bone185": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 16.42, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "bone186": {"translate": [{"x": 4.66, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 16.42, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "x": 4.66}]}, "bone187": {"translate": [{"x": 12.44, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 16.42, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3667, "x": 12.44}]}, "bone188": {"translate": [{"x": 16.42, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": "stepped"}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 16.42}]}, "bone189": {"translate": [{"x": 11.76, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "x": 16.42, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "x": 11.76}]}, "bone190": {"translate": [{"x": -1.52, "y": 1.08, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -5.34, "y": 3.82, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "x": -1.52, "y": 1.08}]}, "bone191": {"translate": [{"x": -3.83, "y": 2.73, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -5.34, "y": 3.82, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "x": -3.83, "y": 2.73}]}, "bone192": {"translate": [{"x": -5.34, "y": 3.82, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": "stepped"}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -5.34, "y": 3.82}]}, "bone193": {"translate": [{"x": -3.83, "y": 2.73, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "x": -5.34, "y": 3.82, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "x": -3.83, "y": 2.73}]}, "bone195": {"translate": [{"x": -2.97, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -10.47, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "x": -2.97}]}, "bone196": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -10.47, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "bone197": {"translate": [{"x": -7.07, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.0667, "x": -10.47, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 0.3667, "x": -7.07}]}, "bone198": {"translate": [{"x": -2.97, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "x": -10.47, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.3, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "x": -2.97}]}, "bone199": {"translate": [{"x": -10.47, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": "stepped"}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -10.47}]}, "bone200": {"translate": [{"x": -7.5, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -10.47, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "x": -7.5}]}, "bone202": {"translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2, "x": -8.25, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2333}]}, "bone203": {"translate": [{"x": 3.78, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 13.32, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "x": 3.78}]}, "bone204": {"translate": [{"x": 9.54, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 13.32, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "x": 9.54}]}, "bone205": {"translate": [{"x": 13.32, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": "stepped"}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 13.32}]}, "bone206": {"translate": [{"x": 9.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "x": 13.32, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "x": 9.54}]}}}}}