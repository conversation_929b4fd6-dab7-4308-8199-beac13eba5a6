import { _decorator, Tween, tween, UIOpacity, UITransform, v3, Vec3 } from "cc";
import { GORole } from "./GORole";
import { PlayerBackInfo } from "../FightDefine";
import { JsonMgr } from "../../mgr/JsonMgr";
import RenderSection from "../../../lib/object/RenderSection";
import { AnimationSection } from "../section/AnimationSection";
import ResMgr from "../../../lib/common/ResMgr";
import FightManager, { scaleList } from "../manager/FightManager";
import { IConfigLeaderSkin } from "../../JsonDefine";
import { dtTime } from "../../BoutStartUp";

const { ccclass, property } = _decorator;

@ccclass("GOPlayer")
export class GOPlayer extends GORole {
  public onInitDetail(detail: PlayerBackInfo, renderName: string = ""): void {
    super.onInitDetail(detail);
    this._renderName = renderName;
    const db = JsonMgr.instance.jsonList.c_leaderSkin[detail.dbId];
    this._dbScale = this.getSceneScale(detail.sceneId, db);
    this._spineId = Number(db.spineId);
  }

  protected getSceneScale(sceneId: number, db: IConfigLeaderSkin): Vec3 {
    switch (sceneId) {
      case 1:
        return v3(db.renderScale9[0], db.renderScale9[1], db.renderScale9[2]);
      case 2:
        return v3(db.renderScale10[0], db.renderScale10[1], db.renderScale10[2]);
      case 3:
        return v3(db.renderScale11[0], db.renderScale11[1], db.renderScale11[2]);
      case 4:
        return v3(db.renderScale12[0], db.renderScale12[1], db.renderScale12[2]);
      case 5:
        return v3(db.renderScale13[0], db.renderScale13[1], db.renderScale13[2]);
      case 6:
        return v3(db.renderScale20[0], db.renderScale20[1], db.renderScale20[2]);
      case 101:
        return v3(db.renderScale9[0], db.renderScale9[1], db.renderScale9[2]);
      default:
        return v3(1, 1, 1); // 默认缩放比例
    }
  }

  public async createAllSection() {
    await this.createRenderSection();

    const render = this._renderSection.getRender();
    render.addComponent(UIOpacity);
    let width = render.getComponent(UITransform).width;
    render.setPosition(v3(-350 - width / 2, 0, 0));

    await super.createAllSection();

    await this.createHPSection();
  }

  protected async createRenderSection(): Promise<void> {
    return new Promise<void>(async (resolve) => {
      const param = {
        spineId: this._spineId,
        dbScale: this._dbScale,
        render: this._render,
        isBoss: false,
        callBack: resolve,
      };
      this._renderSection = await this.createSection(RenderSection, param);
    });
  }

  public fightStartAni() {
    const render = this._renderSection.getRender();
    const time = 0.5;
    this.getSection(AnimationSection).playAction(201, true);
    this._startTween = tween(render)
      .delay(dtTime)
      .to(time, { position: v3(0, 0, 0) })
      .call(() => {
        this.getSection(AnimationSection).playAction(1, true);
        this.fightShowTime = true;
      })
      .start();
  }
}
