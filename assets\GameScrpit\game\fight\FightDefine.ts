import { Node, Vec2 } from "cc";
import { GORole } from "./role/GORole";
import { PlayerSimpleMessage } from "../net/protocol/Player";
import GOSkill from "./skill/GOSkill";

/**战斗数据信息格式 */
export interface BattleRecorder {
  /**场景id */
  a: number;
  /**战斗版本 */
  b: number;
  /**挑战者 */
  c: BattlePlayerBackInfo;
  /**被挑战者 */
  d: BattlePlayerBackInfo;
  /**回合信息 */
  e: Array<RoundInfo>;
  /**最大回合数 */
  f: number;
}

/**行动类型 */
export enum ActionType {
  /**普通攻击 */
  NORMAL_ATTACK = 1,
  /**连击 */
  COMBO = 2,
  /**反击 */
  COUNTER = 3,
  /**暴击 */
  CRITICAL = 4,
  /**闪避 */
  DOOGE = 5,
  /**禁止行动 */
  FORBIDON = 6,
}

/**攻击效果 */
export interface ActionEffect {
  /**伤害 */
  a: number;
  /**回血 */
  b: number;
  /**眩晕回合数 */
  c: number;
}

/**创建用户形象使用的信息 */
export class PlayerBackInfo {
  /**用户初始信息 */
  battlePlayerBackInfo: BattlePlayerBackInfo;
  /**二维坐标 */
  pos: Vec2;
  /**位置朝向 */
  dir: number;
  /**获取配置信息的Id --- c_mosterShow   ------ c_lerderSkin */
  dbId: number;
  /**是否是用户角色 */
  isPlayer: boolean;
  /**sceneId */
  sceneId: number;
  /**提前准备的预制体角色 */
  render?: Node;
  /**用户角色名字 */
  playerName?: string;
}

/**战斗用户初始信息 */
export class BattlePlayerBackInfo {
  /**用户id */
  a: number;
  /**位置信息 */
  b: number;
  /**初始血量 */
  c: number;
  /*形象图片ID*/
  d: string;
  /**buff源自的技能和层叠数量 */
  e: { [key: number]: number };
  /**血槽 --- 最大血量 */
  f: number;
  /**形象来源表枚举 */
  g: RoleSourceEnum;
}

export enum RoleSourceEnum {
  /**主角皮肤 */
  C_LEADERSKIN = 1,
  /**怪物表现 */
  C_MONSTERSHOW = 2,
}

/**回合信息 */
export interface RoundInfo {
  /**当前是第几回合 */
  a: number;
  /**每回合初用户信息 */
  b: { [key: number]: BattlePlayerRoundInfo };
  /**回合里的战斗信息 */
  c: Array<ActionInfo>;
}

/**回合的用户信息 */
export class BattlePlayerRoundInfo {
  /**血量 */
  a: number;
  /**是否是眩晕状态 1正常 2眩晕 */
  b: number;
  /**buff来自的技能ID和重叠数量 */
  c: { [key: number]: number };
  /**每回合血槽 */
  d: number;
}

/**战斗信息 */
export interface ActionInfo {
  /**当前攻击对象 */
  a: number;
  /**防守者信息 */
  b: number;
  /**攻击类型 */
  c: ActionType;
  /**攻击效果 */
  d: { [key: number]: ActionEffect };
  /**变化的Buff列表 */
  e: { [key: number]: { [key: number]: number } };
}

export interface FightData {
  fightData: BattleRecorder;
  win: boolean;
  clubBossInfo?: any;
  /**演武场需要附带玩家信息 */
  userInfo?: PlayerSimpleMessage;
  /**演武场切磋时需要附带日志id */
  logId?: number;
  /**灵兽狩猎时需要凭证 */
  identify?: number;
  /**展示结果 */
  resAddList?: any;
  /**战斗列表,洪荒boss */
  buddyList?: any;
  /**灵兽入侵 */
  goRoleNodeMap?: Map<number, Node>;
}

export class BuffDetail {
  /**buffId */
  buffId: number;
  /**回合数 */
  roundCount: number;
  /**挂载者 */
  attachment: GORole;
}

/**技能脚本参数 */
export class CallSkillDetail {
  /**施法者 */
  src: GORole;
  /**技能id */
  skillId: number;
  /**受击者 */
  target: GORole; //可以有也可以没有
  /**攻击信息 */
  movementInfo: { [key: number]: ActionEffect };
  /**动作类型 */
  actionType: ActionType;
  /**技能结束回调 */
  resolveBack: Function;
}

/**子弹脚本参数 -- 和技能参数基本一直，只是作为延申 */
export class CallBulletDetail {
  /**GoSkill */
  goSkill: GOSkill;
  /**子弹id */
  bulletId: number;
  /**技能id */
  skillId: number;
  /**子弹释放者 */
  src: GORole;
  /**受击者 */
  target?: GORole; //可以有也可以没有
  /**攻击信息 */
  movementInfo: { [key: number]: ActionEffect };
  /**动作类型 */
  actionType: ActionType;
  /**技能结束回调,释放技能的地方继承下来，直到子弹销毁结束才调用 */
  resolveBack: Function;
}

/**受到的伤害效果 */
export class HurtDetail {
  /**受伤的角色 */
  hurtRole: GORole;
  /**扣血量 */
  hurt: number;
  /**眩晕  1 是正常状态 ---  2 是眩晕状态*/
  stun: number;
  /**眩晕类型 */
  stunType: STUN_TYPE_ENUM;
  /**是否是暴击 */
  isCrit: boolean;
  /**是否是连击 */
  isDouble: boolean;
  /**是否闪避 */
  isDodge: boolean;
  /**是否是反击 */
  backpunch: boolean;
}

export enum STUN_TYPE_ENUM {
  STUN_COMMON = "stun",
  STUN_SWK = "swk_stun",
}

/**吸血恢复血量 */
export class RecoverDetail {
  goRole: GORole;
  recover: number;
}

export const newFight = {
  a: 3,
  b: 1,
  f: 11,
  c: { a: 43000003, b: 1, c: 111202, f: 111202, d: 1701, e: {} },
  d: { a: 101, b: 2, c: 92400, f: 92400, d: 20001, e: {} },
  e: [
    {
      a: 1,
      b: { "1": { a: 111202, d: 111202, c: null }, "2": { a: 92400, d: 92400, c: null } },
      c: [
        { a: 2, b: 1, c: 1, d: { "1": { a: 9610, b: null, c: null } }, e: {} },
        { a: 1, b: 2, c: 1, d: { "2": { a: 14223, b: null, c: null } }, e: {} },
      ],
    },
    {
      a: 2,
      b: { "1": { a: 101592, d: 111202, c: null }, "2": { a: 78177, d: 92400, c: null } },
      c: [
        { a: 2, b: 1, c: 1, d: { "1": { a: 11445, b: null, c: null } }, e: {} },
        { a: 1, b: 2, c: 1, d: { "2": { a: 17068, b: null, c: null } }, e: {} },
      ],
    },
    {
      a: 3,
      b: { "1": { a: 90147, d: 111202, c: null }, "2": { a: 61109, d: 92400, c: null } },
      c: [
        { a: 2, b: 1, c: 1, d: { "1": { a: 10042, b: null, c: null } }, e: {} },
        { a: 1, b: 2, c: 1, d: { "2": { a: 16120, b: null, c: null } }, e: {} },
      ],
    },
    {
      a: 4,
      b: { "1": { a: 80105, d: 111202, c: null }, "2": { a: 44989, d: 92400, c: null } },
      c: [
        { a: 2, b: 1, c: 1, d: { "1": { a: 9718, b: null, c: null } }, e: {} },
        { a: 1, b: 2, c: 1, d: { "2": { a: 14065, b: null, c: null } }, e: {} },
      ],
    },
    {
      a: 5,
      b: { "1": { a: 70387, d: 111202, c: null }, "2": { a: 30924, d: 92400, c: null } },
      c: [
        { a: 2, b: 1, c: 1, d: { "1": { a: 9934, b: null, c: null } }, e: {} },
        { a: 1, b: 2, c: 1, d: { "2": { a: 16910, b: null, c: null } }, e: {} },
      ],
    },
    {
      a: 6,
      b: { "1": { a: 60453, d: 111202, c: null }, "2": { a: 14014, d: 92400, c: null } },
      c: [
        { a: 2, b: 1, c: 1, d: { "1": { a: 11661, b: null, c: null } }, e: {} },
        { a: 1, b: 2, c: 1, d: { "2": { a: 14855, b: null, c: null } }, e: {} },
      ],
    },
  ],
};

/**引导战斗 */
export const yinDaoFight: any = {
  a: 1, //战斗的场景类型调用脚本
  b: 1,
  f: 30,
  c: { a: 11111, b: 1, c: 9820216, f: 9820216, d: 1716, g: RoleSourceEnum.C_LEADERSKIN },
  d: { a: 22222, b: 2, c: 8303306, f: 8303306, d: 10005, g: RoleSourceEnum.C_MONSTERSHOW },
  e: [
    {
      a: 1,
      b: { "1": { a: 9820216, d: 9820216, b: 1 }, "2": { a: 8303306, d: 8303306, b: 1 } },
      c: [
        { a: 1, b: 2, c: 1, d: { "2": { a: 876531 } } },
        { a: 1, b: 2, c: 2, d: { "2": { a: 798532 } } },
        { a: 2, b: 1, c: 1, d: { "1": { a: 632901 } } },
        { a: 1, b: 2, c: 3, d: { "2": { a: 915422 } } },
      ],
    },

    {
      a: 2,
      b: { "1": { a: 9187315, d: 9820216, b: 1 }, "2": { a: 5712821, d: 8303306, b: 1 } },
      c: [
        { a: 1, b: 2, c: 4, d: { "2": { a: 1658554 } } },
        { a: 2, b: 1, c: 1, d: { "1": { a: 568231 } } },
        { a: 2, b: 1, c: 2, d: { "1": { a: 621022 } } },
      ],
    },
    {
      a: 3,
      b: { "1": { a: 7998062, d: 9820216, b: 1 }, "2": { a: 4054267, d: 8303306, b: 1 } },
      c: [
        { a: 1, b: 2, c: 1, d: { "2": { a: 853211, c: 1 } } },
        { a: 2, b: 1, c: 6, d: { "2": { c: 0 } } },
      ],
    },

    {
      a: 4,
      b: { "1": { a: 7998062, d: 9820216, b: 1 }, "2": { a: 3201056, d: 8303306, b: 1 } },
      c: [
        { a: 1, b: 2, c: 1, d: { "2": { a: 712024, c: 1 } } },
        { a: 2, b: 1, c: 6, d: { "2": { c: 0 } } },
      ],
    },

    {
      a: 5,
      b: { "1": { a: 7998062, d: 9820216, b: 1 }, "2": { a: 2489032, d: 8303306, b: 1 } },
      c: [
        { a: 1, b: 2, c: 1, d: { "2": { a: 975753 } } },
        { a: 1, b: 2, c: 2, d: { "2": { a: 801255 } } },
        { a: 1, b: 2, c: 2, d: { "2": { a: 712024 } } },
      ],
    },
  ],
};
