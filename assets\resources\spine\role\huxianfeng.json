{"skeleton": {"hash": "TVi76XljUPsC7nO+g+b7PqjS3dc=", "spine": "3.8.75", "x": -182.88, "y": -14.39, "width": 296, "height": 302.62, "images": "./image/", "audio": "D:/spine导出/boss动画/虎先锋"}, "bones": [{"name": "root", "scaleX": -1}, {"name": "bone49", "parent": "root", "x": 7.29, "y": -0.32}, {"name": "bone", "parent": "bone49", "length": 80.14, "rotation": 0.68, "x": -19.69, "y": 90.46}, {"name": "bone2", "parent": "bone", "length": 33.37, "rotation": 83.25, "x": -3.79, "y": -2.72}, {"name": "bone3", "parent": "bone2", "length": 25.43, "rotation": 7.66, "x": 34.74, "y": 0.5}, {"name": "bone4", "parent": "bone3", "length": 16.42, "rotation": 23.87, "x": 25.43}, {"name": "bone5", "parent": "bone3", "x": 22.28, "y": -62.42}, {"name": "bone6", "parent": "bone3", "x": 22.71, "y": 29.39}, {"name": "bone7", "parent": "bone", "length": 19.73, "rotation": -80.38, "x": -8.63, "y": -23.49}, {"name": "bone8", "parent": "bone7", "length": 20.49, "rotation": 0.62, "x": 19.73}, {"name": "bone12", "parent": "bone", "length": 36.57, "rotation": -143.92, "x": -22.65, "y": -15.2}, {"name": "bone13", "parent": "bone12", "length": 40.45, "rotation": 111.08, "x": 36.57}, {"name": "bone14", "parent": "bone13", "length": 14.41, "rotation": -88.8, "x": 40.45}, {"name": "bone15", "parent": "bone14", "length": 34.27, "rotation": -47.15, "x": 14.11, "y": -0.18}, {"name": "bone9", "parent": "bone", "length": 48.58, "rotation": -38.3, "x": 18.56, "y": -23.46}, {"name": "bone10", "parent": "bone9", "length": 21.23, "rotation": -5.36, "x": 48.58}, {"name": "bone11", "parent": "bone10", "length": 11.26, "rotation": -14.83, "x": 21.23}, {"name": "bone16", "parent": "bone11", "length": 17.53, "rotation": -7.18, "x": 11.26}, {"name": "bone17", "parent": "bone5", "length": 42.01, "rotation": 160.72, "x": -0.21, "y": 0.14}, {"name": "bone18", "parent": "bone17", "length": 38.01, "rotation": -80.66, "x": 42.01}, {"name": "bone19", "parent": "bone18", "length": 23.52, "rotation": 4.15, "x": 38.01}, {"name": "bone20", "parent": "bone18", "length": 13.53, "rotation": -29.4, "x": 42.66, "y": -5.59}, {"name": "bone21", "parent": "bone20", "length": 18, "rotation": -40.08, "x": 13.53}, {"name": "bone22", "parent": "bone6", "length": 34.56, "rotation": 61.08, "x": -0.41, "y": 3.58}, {"name": "bone23", "parent": "bone22", "length": 63.76, "rotation": -83.26, "x": 34.41, "y": 0.31}, {"name": "bone24", "parent": "bone23", "length": 62.48, "rotation": -99.94, "x": 57.86}, {"name": "bone25", "parent": "bone", "length": 31.18, "rotation": 10.21, "x": 16.63, "y": 11.93, "color": "ff0000ff"}, {"name": "bone26", "parent": "bone25", "length": 32.31, "rotation": 19.52, "x": 31.18, "color": "ff0000ff"}, {"name": "bone27", "parent": "bone26", "length": 30.69, "rotation": 37.87, "x": 32.31, "color": "ff0000ff"}, {"name": "bone28", "parent": "bone27", "length": 40.59, "rotation": 38.77, "x": 30.69, "color": "ff0000ff"}, {"name": "bone29", "parent": "bone28", "length": 32.28, "rotation": -27.83, "x": 40.59, "color": "ff0000ff"}, {"name": "bone30", "parent": "bone29", "length": 29.09, "rotation": -50.94, "x": 32.28, "color": "ff0000ff"}, {"name": "target2", "parent": "bone49", "x": 66.9, "y": -2.12, "color": "ff3f00ff"}, {"name": "target1", "parent": "target2", "x": -13.89, "y": 24.64, "color": "ff3f00ff"}, {"name": "target4", "parent": "bone49", "x": -79.59, "y": 12.44, "color": "ff3f00ff"}, {"name": "target3", "parent": "target4", "x": 42.11, "y": 19.71, "color": "ff3f00ff"}, {"name": "bone31", "parent": "bone", "length": 31.18, "rotation": -40.62, "x": 16.63, "y": 11.93, "scaleX": 0.9648, "scaleY": 0.9648, "color": "ff0000ff"}, {"name": "bone32", "parent": "bone31", "length": 32.31, "rotation": 19.52, "x": 31.18, "color": "ff0000ff"}, {"name": "bone33", "parent": "bone32", "length": 30.69, "rotation": 37.87, "x": 32.31, "color": "ff0000ff"}, {"name": "bone34", "parent": "bone33", "length": 40.59, "rotation": 38.77, "x": 30.69, "color": "ff0000ff"}, {"name": "bone35", "parent": "bone34", "length": 32.28, "rotation": -27.83, "x": 40.59, "color": "ff0000ff"}, {"name": "bone36", "parent": "bone35", "length": 29.09, "rotation": -50.94, "x": 32.28, "color": "ff0000ff"}, {"name": "bone37", "parent": "bone", "length": 31.18, "rotation": -18.49, "x": 16.63, "y": 11.93, "scaleX": 1.0497, "scaleY": 1.0497, "color": "ff0000ff"}, {"name": "bone38", "parent": "bone37", "length": 32.31, "rotation": 19.52, "x": 31.18, "color": "ff0000ff"}, {"name": "bone39", "parent": "bone38", "length": 30.69, "rotation": 37.87, "x": 32.31, "color": "ff0000ff"}, {"name": "bone40", "parent": "bone39", "length": 40.59, "rotation": 38.77, "x": 30.69, "color": "ff0000ff"}, {"name": "bone41", "parent": "bone40", "length": 32.28, "rotation": -27.83, "x": 40.59, "color": "ff0000ff"}, {"name": "bone42", "parent": "bone41", "length": 29.09, "rotation": -50.94, "x": 32.28, "color": "ff0000ff"}, {"name": "bone43", "parent": "bone4", "length": 16.04, "rotation": -71.83, "x": 53.17, "y": -67.22}, {"name": "bone44", "parent": "bone43", "length": 16.63, "rotation": 13.96, "x": 16.42, "y": 0.01}, {"name": "bone45", "parent": "bone44", "length": 18.73, "rotation": 26.61, "x": 16.63}, {"name": "bone46", "parent": "bone4", "length": 10.88, "rotation": 18.53, "x": 87.55, "y": 35.13}, {"name": "bone47", "parent": "bone46", "length": 17.77, "rotation": -27.21, "x": 11.08, "y": -0.19}, {"name": "bone50", "parent": "bone49", "x": -21.33, "y": 141.85, "scaleX": 1.7927, "scaleY": 1.7927}, {"name": "bone51", "parent": "bone50", "x": -0.39, "y": 0.24}, {"name": "bone52", "parent": "bone49", "x": -21.33, "y": 141.85, "scaleX": 1.7927, "scaleY": 1.7927}, {"name": "bone53", "parent": "bone52", "x": -0.39, "y": 0.24}, {"name": "bone48", "parent": "root", "x": -9.82, "y": 148.64, "scaleX": 1.7888, "scaleY": 1.7888}], "slots": [{"name": "weib", "bone": "bone25", "attachment": "weib"}, {"name": "weib2", "bone": "bone31", "attachment": "weib"}, {"name": "weib3", "bone": "bone37", "attachment": "weib"}, {"name": "body", "bone": "bone", "attachment": "body"}, {"name": "jiao", "bone": "bone9", "attachment": "jiao"}, {"name": "shou1_1", "bone": "bone6", "attachment": "shou1_1"}, {"name": "dao", "bone": "bone24", "attachment": "dao"}, {"name": "shou1", "bone": "bone23", "attachment": "shou1"}, {"name": "she<PERSON><PERSON>", "bone": "bone2", "attachment": "she<PERSON><PERSON>"}, {"name": "ya<PERSON>i", "bone": "bone", "attachment": "ya<PERSON>i"}, {"name": "tou", "bone": "bone4", "attachment": "tou"}, {"name": "shou2", "bone": "bone5", "attachment": "shou2"}, {"name": "feng", "bone": "bone50"}, {"name": "feng3", "bone": "bone52"}, {"name": "feng2", "bone": "bone51", "blend": "additive"}, {"name": "feng4", "bone": "bone53", "blend": "additive"}, {"name": "biyan", "bone": "bone4"}, {"name": "light", "bone": "bone48", "color": "ffb4005c", "blend": "additive"}], "ik": [{"name": "target1", "bones": ["bone9", "bone10"], "target": "target1"}, {"name": "target2", "order": 1, "bones": ["bone11", "bone16"], "target": "target2", "bendPositive": false}, {"name": "target3", "order": 2, "bones": ["bone12", "bone13"], "target": "target3"}, {"name": "target4", "order": 3, "bones": ["bone14", "bone15"], "target": "target4", "bendPositive": false}], "skins": [{"name": "default", "attachments": {"yaodai": {"yaodai": {"type": "mesh", "uvs": [0, 0.11153, 0.04841, 0.04084, 0.11507, 0.06976, 0.18173, 0.1533, 0.25608, 0.09868, 0.34325, 0.08904, 0.43555, 0.14045, 0.56887, 0.11153, 0.7227, 0.11153, 0.9355, 0.07619, 1, 0.23685, 1, 0.36217, 0.89191, 0.35574, 0.75347, 0.3686, 0.66886, 0.3686, 0.60989, 0.44893, 0.52529, 0.42965, 0.54836, 0.55818, 0.6022, 0.72206, 0.68424, 0.86666, 0.73296, 0.9727, 0.57143, 1, 0.37146, 1, 0.21763, 0.95664, 0.10482, 0.84738, 0.07405, 0.74456, 0.13815, 0.64494, 0.15353, 0.53248, 0.13302, 0.4168, 0.06892, 0.40716, 0, 0.33646, 0, 0.23042, 0.21506, 0.36538, 0.29711, 0.42644, 0.39197, 0.37181, 0.30223, 0.54533, 0.33813, 0.71242, 0.3612, 0.89237], "triangles": [36, 17, 18, 24, 25, 26, 37, 36, 18, 23, 36, 37, 36, 24, 26, 23, 24, 36, 37, 21, 22, 18, 21, 37, 23, 37, 22, 18, 19, 21, 21, 19, 20, 35, 17, 36, 27, 32, 33, 35, 33, 34, 27, 33, 35, 17, 35, 16, 26, 35, 36, 35, 26, 27, 16, 35, 34, 29, 3, 28, 31, 29, 30, 3, 29, 31, 32, 28, 3, 33, 32, 34, 16, 7, 14, 12, 8, 9, 12, 9, 10, 12, 10, 11, 14, 7, 8, 13, 14, 8, 12, 13, 8, 2, 31, 0, 2, 0, 1, 31, 2, 3, 32, 3, 4, 34, 4, 5, 6, 34, 5, 32, 4, 34, 16, 6, 7, 34, 6, 16, 15, 16, 14, 27, 28, 32], "vertices": [1, 2, -37.73, -0.83, 1, 1, 2, -33.11, 4.42, 1, 1, 2, -26.87, 2.17, 1, 1, 2, -20.68, -4.17, 1, 1, 2, -13.64, -0.15, 1, 1, 2, -5.44, 0.47, 1, 1, 2, 3.19, -3.49, 1, 1, 2, 15.74, -1.47, 1, 1, 2, 30.2, -1.64, 1, 1, 2, 50.24, 0.77, 1, 1, 2, 56.16, -11.35, 1, 1, 2, 56.04, -20.75, 1, 1, 2, 45.89, -20.15, 1, 1, 2, 32.87, -20.95, 1, 1, 2, 24.91, -20.86, 1, 1, 2, 19.3, -26.82, 1, 2, 2, 11.36, -25.28, 0.768, 8, 5.11, 19.41, 0.232, 1, 8, 14.98, 19.82, 1, 3, 2, 18.33, -47.29, 0.00117, 8, 27.98, 22.6, 0.26694, 9, 8.49, 22.51, 0.73189, 2, 8, 40.03, 28.25, 0.00713, 9, 20.6, 28.03, 0.99287, 1, 9, 29.27, 31.02, 1, 1, 9, 28.41, 15.72, 1, 1, 9, 24.85, -2.74, 1, 2, 8, 38.82, -16.11, 0.0256, 9, 18.91, -16.32, 0.9744, 2, 8, 28.86, -25.08, 0.18426, 9, 8.86, -25.18, 0.81574, 2, 8, 20.76, -26.55, 0.28658, 9, 0.74, -26.56, 0.71342, 2, 8, 14.48, -19.28, 0.55539, 9, -5.46, -19.22, 0.44461, 2, 8, 6.44, -16.35, 0.89962, 9, -13.47, -16.21, 0.10038, 2, 2, -25.49, -23.87, 0.704, 8, -2.44, -16.69, 0.296, 1, 2, -31.51, -23.08, 1, 1, 2, -37.93, -17.7, 1, 1, 2, -37.83, -9.75, 1, 1, 2, -17.74, -20.11, 1, 1, 8, 1.03, -1.65, 1, 1, 2, -1.12, -20.79, 1, 2, 8, 9.89, -2.77, 0.99413, 9, -9.87, -2.66, 0.00587, 2, 8, 22.83, -1.69, 0.03184, 9, 3.07, -1.73, 0.96816, 1, 9, 16.74, -2.15, 1], "hull": 32, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 0, 62, 56, 64, 64, 66, 66, 68, 68, 32, 66, 70, 70, 72, 72, 74], "width": 94, "height": 75}}, "shou1_1": {"shou1_1": {"type": "mesh", "uvs": [0.34179, 0.91281, 0.46386, 0.97998, 0.61709, 1, 0.83006, 0.9582, 0.97291, 0.85289, 1, 0.70221, 0.93915, 0.5733, 0.7989, 0.50794, 0.63527, 0.4916, 0.59019, 0.50826, 0.53831, 0.52743, 0.58185, 0.49921, 0.58106, 0.45273, 0.59056, 0.39296, 0.65151, 0.37968, 0.72916, 0.25363, 0.73589, 0.15098, 0.69278, 0.07281, 0.60387, 0.00972, 0.43681, 0, 0.28054, 0, 0.1768, 0.04362, 0.15524, 0.13968, 0.15929, 0.23856, 0.12561, 0.27529, 0.14177, 0.32049, 0.13186, 0.34844, 0.06816, 0.42131, 0.03534, 0.49958, 0.0141, 0.58595, 0, 0.65747, 0.02954, 0.7398, 0.12028, 0.80862, 0.23032, 0.86665, 0.72068, 0.80052, 0.41372, 0.72765, 0.25928, 0.61159, 0.27472, 0.45775, 0.35967, 0.33224, 0.44268, 0.14061], "triangles": [32, 31, 36, 31, 30, 36, 30, 29, 36, 36, 28, 37, 36, 37, 10, 37, 28, 27, 11, 10, 12, 10, 37, 12, 27, 26, 37, 37, 26, 38, 37, 38, 12, 38, 26, 25, 12, 38, 13, 14, 13, 15, 39, 15, 13, 25, 23, 38, 38, 23, 39, 21, 39, 22, 13, 38, 39, 39, 20, 19, 20, 39, 21, 25, 24, 23, 15, 39, 16, 39, 23, 22, 39, 17, 16, 39, 18, 17, 39, 19, 18, 3, 2, 34, 2, 1, 34, 34, 1, 35, 3, 34, 4, 1, 0, 35, 0, 33, 35, 33, 32, 35, 4, 34, 5, 32, 36, 35, 35, 10, 34, 10, 9, 34, 34, 9, 7, 34, 6, 5, 34, 7, 6, 7, 9, 8, 35, 36, 10, 29, 28, 36], "vertices": [2, 23, 9.88, 22.15, 0.92946, 24, -24.57, -21.79, 0.07054, 2, 23, -1.11, 24.26, 0.9926, 24, -27.96, -32.45, 0.0074, 1, 23, -11.85, 21.03, 1, 1, 23, -23.5, 10.16, 1, 1, 23, -27.66, -4.2, 1, 2, 23, -22.26, -18.88, 0.99997, 24, 12.4, -58.53, 3e-05, 2, 23, -12.27, -28.66, 0.98987, 24, 23.29, -49.76, 0.01013, 2, 23, -0.21, -30.01, 0.94828, 24, 26.04, -37.94, 0.05172, 2, 23, 11.03, -26.09, 0.83603, 24, 23.47, -26.32, 0.16397, 2, 23, 13.12, -23.08, 0.76962, 24, 20.73, -23.88, 0.23038, 2, 23, 15.53, -19.61, 0.54141, 24, 17.56, -21.08, 0.45859, 2, 23, 14.08, -23.63, 0.31202, 24, 21.39, -22.99, 0.68798, 2, 23, 16.33, -27.86, 0.18265, 24, 25.85, -21.25, 0.81735, 2, 23, 18.55, -33.64, 0.05163, 24, 31.85, -19.73, 0.94837, 2, 23, 15.28, -36.87, 0.01891, 24, 34.68, -23.36, 0.98109, 2, 23, 16.28, -50.97, 1e-05, 24, 48.8, -24.02, 0.99999, 1, 24, 58.86, -20.76, 1, 1, 24, 65.31, -15.02, 1, 1, 24, 69.14, -6.74, 1, 1, 24, 65.85, 4.87, 1, 1, 24, 61.89, 15.4, 1, 1, 24, 55.06, 20.81, 1, 1, 24, 45.25, 18.79, 1, 2, 23, 53.44, -33.51, 0.0008, 24, 35.82, 14.93, 0.9992, 2, 23, 53.86, -29.04, 0.00505, 24, 31.42, 15.87, 0.99495, 2, 23, 50.68, -25.44, 0.02247, 24, 27.47, 13.15, 0.97753, 2, 23, 50, -22.55, 0.05687, 24, 24.53, 12.8, 0.94313, 2, 23, 50.62, -13.78, 0.27387, 24, 15.89, 14.45, 0.72613, 2, 23, 49.02, -5.53, 0.62649, 24, 7.51, 13.83, 0.37351, 2, 23, 46.3, 3.07, 0.58933, 24, -1.35, 12.13, 0.41067, 2, 23, 43.82, 10.09, 0.10083, 24, -8.61, 10.49, 0.89917, 2, 23, 38.03, 16.64, 0.02103, 24, -15.8, 5.52, 0.97897, 2, 23, 28.97, 19.94, 0.36291, 24, -20.13, -3.09, 0.63709, 2, 23, 19.19, 21.61, 0.74469, 24, -22.94, -12.61, 0.25531, 1, 23, -9.05, -0.65, 1, 2, 23, 14.03, 2.83, 0.99855, 24, -4.9, -19.93, 0.00145, 2, 23, 29.4, -2.68, 0.73686, 24, 2.38, -5.32, 0.26314, 1, 24, 17.61, -0.79, 1, 2, 23, 36.19, -31.56, 0.00094, 24, 31.86, -1.97, 0.99906, 1, 24, 52.44, -0.62, 1], "hull": 34, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 20, 22, 16, 18, 18, 20, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 0, 66, 8, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78], "width": 72, "height": 103}}, "light": {"light": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -64, -64, -64, -64, 64, 64, 64], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 128, "height": 128}}, "dao": {"dao": {"x": 107.68, "y": -1.87, "rotation": 30.53, "width": 296, "height": 185}}, "shou1": {"shou1": {"x": 55.63, "y": -4.9, "rotation": -69.41, "width": 52, "height": 50}}, "shou2": {"shou2": {"type": "mesh", "uvs": [0.47218, 0.51654, 0.47104, 0.47823, 0.47838, 0.43213, 0.49894, 0.31698, 0.52458, 0.1372, 0.62711, 0.00525, 0.77508, 0, 0.91956, 0.04978, 1, 0.26915, 1, 0.5182, 0.92538, 0.61221, 0.89159, 0.62871, 0.87412, 0.73591, 0.80304, 0.83817, 0.77158, 0.87446, 0.76343, 0.94868, 0.73663, 0.97672, 0.64808, 0.95858, 0.53157, 0.99816, 0.38709, 0.96682, 0.30436, 0.96187, 0.20882, 0.95528, 0.12377, 0.91404, 0.02706, 0.88271, 0, 0.71742, 0, 0.54522, 0.04887, 0.49608, 0.01384, 0.45619, 0.00851, 0.35595, 0, 0.26756, 0.00318, 0.20181, 0.05725, 0.17917, 0.13339, 0.14252, 0.21335, 0.19426, 0.24152, 0.26325, 0.23619, 0.34086, 0.25827, 0.40984, 0.28797, 0.45188, 0.32071, 0.47236, 0.38924, 0.50686, 0.4395, 0.51979, 0.57734, 0.53428, 0.67063, 0.63895, 0.72225, 0.71655, 0.77128, 0.21121, 0.71459, 0.46496, 0.55219, 0.74041, 0.38366, 0.70787, 0.27488, 0.69052, 0.14311, 0.67317, 0.17988, 0.56256, 0.13699, 0.38905, 0.11247, 0.25459, 0.11247, 0.48665], "triangles": [53, 51, 50, 36, 51, 35, 36, 50, 51, 53, 26, 51, 51, 26, 27, 27, 28, 51, 28, 52, 51, 51, 52, 35, 28, 29, 52, 31, 29, 30, 29, 31, 52, 35, 52, 34, 34, 52, 33, 52, 32, 33, 52, 31, 32, 21, 48, 20, 22, 49, 21, 21, 49, 48, 22, 23, 49, 25, 26, 49, 49, 23, 24, 49, 24, 25, 49, 50, 48, 49, 53, 50, 49, 26, 53, 15, 16, 14, 14, 16, 17, 43, 46, 42, 14, 17, 43, 14, 43, 13, 13, 43, 12, 12, 43, 11, 11, 43, 45, 43, 42, 45, 42, 41, 45, 11, 45, 10, 10, 45, 9, 41, 1, 2, 45, 41, 3, 9, 45, 8, 45, 44, 8, 41, 2, 3, 45, 3, 44, 44, 3, 4, 44, 4, 5, 44, 7, 8, 44, 6, 7, 44, 5, 6, 41, 0, 1, 48, 38, 39, 48, 37, 38, 48, 50, 37, 50, 36, 37, 17, 18, 46, 20, 47, 19, 18, 19, 46, 19, 47, 46, 20, 48, 47, 43, 17, 46, 46, 47, 0, 47, 40, 0, 46, 0, 41, 47, 39, 40, 47, 48, 39, 46, 41, 42], "vertices": [3, 19, 20.07, -15.96, 0.58701, 21, -14.59, -20.12, 0.06058, 18, 29.51, -22.4, 0.35241, 1, 18, 26.74, -23.41, 1, 1, 18, 23.11, -23.73, 1, 1, 18, 13.99, -24.29, 1, 2, 19, 18.66, -45.69, 0.01841, 18, -0.05, -25.83, 0.98159, 2, 19, 9.08, -57.37, 0.00019, 18, -13.13, -18.27, 0.99981, 1, 18, -18.41, -3.03, 1, 1, 18, -19.55, 13.14, 1, 1, 18, -6.12, 26.62, 1, 1, 18, 12.15, 32.45, 1, 1, 18, 21.52, 26.9, 1, 1, 18, 23.85, 23.78, 1, 2, 19, -25.73, -5.61, 0.00141, 18, 32.29, 24.47, 0.99859, 2, 19, -19.21, 3.3, 0.10121, 18, 42.15, 19.49, 0.89879, 2, 19, -16.22, 6.56, 0.24617, 18, 45.85, 17.07, 0.75383, 2, 19, -16.17, 12.35, 0.40905, 18, 51.57, 17.96, 0.59095, 2, 19, -13.59, 14.91, 0.45666, 18, 54.51, 15.83, 0.54334, 2, 19, -3.84, 14.93, 0.77868, 18, 56.11, 6.21, 0.22132, 2, 19, 8.28, 19.79, 0.99971, 20, -28.22, 21.88, 0.00029, 2, 19, 24.21, 19.69, 0.88994, 20, -12.33, 20.63, 0.11006, 2, 19, 33.19, 20.62, 0.62793, 20, -3.31, 20.91, 0.37207, 2, 19, 43.57, 21.63, 0.26669, 20, 7.11, 21.17, 0.73331, 2, 19, 53.2, 19.83, 0.05941, 20, 16.59, 18.68, 0.94059, 2, 19, 63.98, 18.98, 0.00074, 20, 27.28, 17.05, 0.99926, 2, 20, 31.15, 4.57, 0.99501, 22, -12.82, 20.07, 0.00499, 1, 20, 32.13, -8.65, 1, 3, 21, 22.86, 6.88, 0.00781, 20, 27.09, -12.81, 0.73888, 22, 2.71, 11.27, 0.25331, 3, 21, 27.76, 6.78, 0.00038, 20, 31.12, -15.6, 0.11039, 22, 6.52, 14.35, 0.88923, 2, 20, 32.27, -23.25, 0.0188, 22, 14.19, 13.29, 0.9812, 2, 20, 33.69, -29.97, 0.00037, 22, 21.04, 12.77, 0.99963, 1, 22, 25.91, 11.36, 1, 1, 22, 26.37, 5.23, 1, 1, 22, 27.38, -3.48, 1, 2, 21, 22.91, -22.48, 0.00226, 22, 21.65, -11.16, 0.99774, 2, 21, 17.23, -20.16, 0.02964, 22, 15.81, -13.04, 0.97036, 3, 19, 47.49, -25.61, 0.00114, 21, 14.03, -15.07, 0.15926, 22, 10.09, -11.21, 0.8396, 3, 19, 44.34, -20.71, 0.02909, 21, 8.87, -12.35, 0.51887, 22, 4.39, -12.44, 0.45204, 4, 19, 40.66, -17.97, 0.12508, 21, 4.33, -11.77, 0.71128, 18, 30.87, -43.04, 0.00025, 22, 0.54, -14.92, 0.16338, 4, 19, 36.9, -16.93, 0.27967, 21, 0.55, -12.71, 0.66486, 18, 31.29, -39.16, 0.00296, 22, -1.75, -18.08, 0.05251, 4, 19, 29.13, -15.39, 0.61496, 21, -6.99, -15.18, 0.34486, 18, 31.55, -31.24, 0.03838, 22, -5.92, -24.82, 0.0018, 3, 19, 23.56, -15.2, 0.69793, 21, -11.93, -17.75, 0.15124, 18, 30.83, -25.72, 0.15083, 3, 19, 8.53, -16.28, 0.70693, 21, -24.49, -26.06, 0.00191, 18, 27.33, -11.06, 0.29116, 2, 19, -2.7, -9.78, 0.32, 18, 31.92, 1.07, 0.68, 2, 19, -9.13, -4.69, 0.01257, 18, 35.9, 8.25, 0.98743, 1, 18, -2.79, 1.52, 1, 1, 18, 17.7, 1.57, 1, 2, 19, 8.94, -0.18, 0.99716, 18, 43.28, -8.85, 0.00283, 1, 19, 27.48, 0.01, 1, 2, 19, 39.41, 0.41, 0.07131, 20, 1.42, 0.31, 0.92869, 3, 21, 6.39, 11.37, 0.00186, 20, 15.85, 0.03, 0.99799, 22, -12.78, 4.1, 0.00014, 2, 21, 8.43, 2.18, 0.86825, 20, 12.47, -8.76, 0.13175, 2, 20, 18.12, -21.74, 0.00019, 22, 8.74, 0.14, 0.99981, 1, 22, 19.43, 0.57, 1, 3, 21, 17.82, 2.06, 0.0275, 20, 20.23, -14.05, 0.56819, 22, 1.96, 4.34, 0.40432], "hull": 41, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 0, 80, 0, 82, 82, 84, 84, 86, 12, 88, 88, 90, 90, 84, 84, 92, 92, 94, 94, 96, 96, 98, 96, 100, 100, 102, 102, 104, 52, 106, 106, 100], "width": 109, "height": 77}}, "tou": {"tou": {"type": "mesh", "uvs": [0.77785, 0.23097, 0.83235, 0.20153, 0.87957, 0.16106, 0.90864, 0.09115, 0.95405, 0.10771, 1, 0.17026, 0.99583, 0.25489, 0.99038, 0.35239, 0.97948, 0.42598, 0.95586, 0.48669, 0.98493, 0.54924, 1, 0.59892, 1, 0.70194, 0.95768, 0.77737, 0.88866, 0.8436, 0.816, 0.90615, 0.71064, 0.94479, 0.57259, 0.98158, 0.47087, 1, 0.33464, 0.99446, 0.23655, 0.95951, 0.15844, 0.90064, 0.11121, 0.82153, 0.15598, 0.78222, 0.16291, 0.73216, 0.13256, 0.70845, 0.10829, 0.65576, 0.10482, 0.61448, 0.058, 0.57497, 0.01898, 0.51788, 0.0077, 0.45992, 0, 0.40811, 0, 0.35454, 0.02158, 0.31853, 0.05279, 0.34751, 0.07881, 0.38264, 0.11436, 0.40108, 0.08643, 0.32151, 0.09445, 0.24029, 0.10361, 0.15327, 0.1334, 0.06393, 0.16433, 0.00592, 0.21015, 0, 0.20901, 0.05349, 0.21932, 0.13935, 0.21817, 0.22405, 0.23535, 0.2751, 0.26628, 0.22985, 0.34646, 0.2017, 0.43812, 0.16951, 0.53657, 0.16732, 0.64913, 0.17609, 0.73138, 0.19362, 0.71407, 0.22578, 0.72265, 0.25957, 0.67691, 0.28764, 0.70463, 0.36626, 0.75314, 0.44628, 0.79334, 0.49261, 0.85433, 0.54455, 0.89175, 0.56139, 0.13218, 0.47014, 0.13634, 0.53051, 0.07535, 0.44768, 0.03654, 0.38311, 0.02545, 0.35363, 0.8169, 0.40136, 0.87373, 0.33678, 0.92224, 0.26659, 0.94165, 0.16551], "triangles": [64, 34, 35, 31, 64, 63, 64, 65, 34, 64, 32, 65, 65, 33, 34, 64, 31, 32, 30, 31, 63, 32, 33, 65, 29, 30, 63, 63, 64, 35, 63, 35, 36, 29, 62, 28, 63, 62, 29, 43, 41, 42, 40, 41, 43, 40, 43, 44, 39, 40, 44, 45, 39, 44, 53, 51, 52, 38, 39, 45, 54, 53, 0, 55, 51, 53, 55, 53, 54, 56, 55, 54, 38, 45, 46, 46, 37, 38, 36, 37, 46, 56, 54, 66, 57, 56, 66, 61, 36, 46, 63, 36, 61, 58, 57, 66, 59, 58, 66, 62, 61, 46, 9, 59, 66, 60, 59, 9, 10, 60, 9, 61, 62, 63, 27, 28, 62, 24, 25, 26, 60, 10, 11, 12, 60, 11, 26, 27, 24, 55, 24, 62, 24, 27, 62, 13, 60, 12, 14, 59, 60, 14, 60, 13, 20, 21, 23, 22, 23, 21, 59, 16, 58, 15, 59, 14, 56, 57, 24, 58, 17, 57, 15, 16, 59, 62, 46, 55, 20, 24, 57, 20, 23, 24, 55, 48, 49, 49, 50, 55, 55, 50, 51, 46, 47, 48, 46, 48, 55, 56, 24, 55, 20, 57, 19, 58, 16, 17, 57, 18, 19, 17, 18, 57, 1, 2, 68, 7, 68, 6, 6, 69, 5, 69, 4, 5, 69, 3, 4, 2, 3, 69, 68, 69, 6, 68, 2, 69, 67, 1, 68, 0, 1, 67, 67, 68, 7, 8, 67, 7, 66, 0, 67, 54, 0, 66, 8, 66, 67, 9, 66, 8], "vertices": [2, 48, 18.28, 21.12, 0.816, 49, 6.89, 20.04, 0.184, 3, 48, 27.68, 18.51, 0.264, 49, 15.38, 15.24, 0.61824, 50, 5.71, 14.18, 0.11776, 1, 50, 12.74, 7.39, 1, 1, 50, 24.06, 3.93, 1, 1, 50, 22.21, -3.47, 1, 1, 50, 13.24, -11.68, 1, 1, 50, 0.04, -12.36, 1, 3, 48, 29.51, -15.76, 0.224, 49, 8.89, -18.46, 0.58355, 50, -15.18, -13.04, 0.19245, 2, 48, 20.34, -22.88, 0.784, 49, -1.72, -23.15, 0.216, 1, 5, 30.83, -86.23, 1, 1, 5, 20.05, -86.18, 1, 1, 5, 12.03, -85, 1, 1, 5, -2.48, -78.09, 1, 1, 5, -10.23, -67, 1, 1, 5, -14.87, -52.71, 1, 1, 5, -18.75, -38.15, 1, 1, 5, -17.03, -20.53, 1, 1, 5, -12.84, 1.63, 1, 1, 5, -8.52, 17.38, 1, 1, 5, 1.51, 36.44, 1, 1, 5, 13.1, 48.09, 1, 1, 5, 26.7, 55.28, 1, 1, 5, 41.05, 56.72, 1, 1, 5, 43.54, 47.69, 1, 1, 5, 50.12, 43.35, 1, 1, 5, 55.52, 46.09, 1, 1, 5, 64.59, 46.01, 1, 1, 5, 70.64, 43.74, 1, 1, 51, -3.72, 14.58, 1, 2, 51, 6.96, 12.83, 0.744, 52, -9.61, 9.69, 0.256, 2, 51, 14.71, 7.83, 0.36, 52, -0.44, 8.79, 0.64, 2, 51, 21.37, 3.09, 0.024, 52, 7.65, 7.62, 0.976, 1, 52, 15.65, 5.2, 1, 1, 52, 20.05, 0.32, 1, 1, 52, 14.29, -3.1, 1, 2, 51, 15.58, -8.63, 0.848, 52, 7.86, -5.45, 0.152, 1, 5, 100.05, 28.07, 1, 1, 5, 113.16, 26.71, 1, 1, 5, 124.05, 20.12, 1, 1, 5, 135.69, 12.98, 1, 1, 5, 146.24, 2.74, 1, 1, 5, 152.31, -5.56, 1, 1, 5, 150.04, -12.5, 1, 1, 5, 142.58, -8.75, 1, 1, 5, 129.79, -4.46, 1, 1, 5, 117.93, 1.38, 1, 1, 5, 109.58, 2.36, 1, 1, 5, 113.85, -5.09, 1, 1, 5, 112.37, -18.42, 1, 1, 5, 110.67, -33.65, 1, 1, 5, 104.3, -47.84, 1, 1, 5, 95.42, -63.31, 1, 1, 5, 87.36, -73.87, 1, 1, 5, 84.01, -69.24, 1, 1, 5, 78.66, -68.2, 1, 1, 5, 77.82, -59.79, 1, 1, 5, 64.86, -58.47, 1, 1, 5, 50.29, -60.03, 1, 1, 5, 41.04, -62.65, 1, 1, 5, 29.58, -67.87, 1, 1, 5, 24.66, -72.08, 1, 1, 5, 89.11, 30.16, 1, 1, 5, 80.33, 33.61, 1, 1, 51, 8.66, -1.19, 1, 1, 52, 9.72, 0.96, 1, 1, 52, 14.63, 1.31, 1, 1, 48, 4.4, -2.37, 1, 1, 49, 1.07, -1.59, 1, 2, 49, 14.43, -2.2, 0.44, 50, -2.95, -0.98, 0.56, 1, 50, 13.04, -2.43, 1], "hull": 54, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 0, 106, 0, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 18, 72, 122, 122, 124, 124, 54, 122, 126, 126, 128, 128, 130, 114, 132, 132, 134, 134, 136, 136, 138], "width": 158, "height": 156}}, "shengti": {"shengti": {"type": "mesh", "uvs": [0.12868, 0.10057, 0.25589, 0.03912, 0.38696, 0.00042, 0.5373, 0, 0.73582, 0.01635, 0.89387, 0.05733, 1, 0.15293, 1, 0.34413, 0.96133, 0.51029, 0.91507, 0.65596, 0.86881, 0.80847, 0.80713, 0.92911, 0.64909, 0.98829, 0.44671, 1, 0.33299, 0.98601, 0.2424, 0.95414, 0.17687, 0.87675, 0.13832, 0.71514, 0.18843, 0.64913, 0.13061, 0.6332, 0.03424, 0.61727, 0, 0.50118, 0, 0.33502, 0.03809, 0.21666, 0.45249, 0.22576, 0.39852, 0.44428, 0.38888, 0.71514, 0.78015, 0.24625, 0.7628, 0.5285, 0.67992, 0.77433, 0.16916, 0.22576, 0.16916, 0.47159, 0.2424, 0.84944], "triangles": [16, 17, 32, 29, 12, 13, 12, 29, 11, 15, 32, 14, 15, 16, 32, 13, 14, 26, 14, 32, 26, 17, 18, 32, 29, 13, 26, 11, 29, 10, 32, 18, 26, 10, 29, 9, 26, 25, 29, 29, 25, 28, 29, 28, 9, 26, 18, 25, 9, 28, 8, 28, 27, 8, 8, 27, 7, 27, 6, 7, 27, 5, 6, 27, 4, 5, 19, 31, 18, 19, 20, 31, 20, 21, 31, 21, 22, 31, 31, 22, 30, 31, 30, 25, 30, 22, 23, 24, 30, 1, 23, 0, 30, 30, 0, 1, 28, 25, 24, 28, 24, 27, 27, 24, 3, 18, 31, 25, 4, 27, 3, 25, 30, 24, 24, 1, 2, 24, 2, 3], "vertices": [2, 4, 44.71, 35.95, 0.10114, 7, 22, 6.56, 0.89886, 3, 4, 50.72, 20, 0.4645, 7, 28.01, -9.39, 0.53433, 6, 28.44, 82.42, 0.00117, 3, 4, 54.33, 3.64, 0.78665, 7, 31.62, -25.74, 0.17813, 6, 32.05, 66.06, 0.03522, 4, 4, 53.86, -14.99, 0.8004, 7, 31.14, -44.38, 0.01685, 6, 31.58, 47.42, 0.18192, 3, 90.11, -7.18, 0.00083, 3, 4, 51.46, -39.55, 0.43939, 6, 29.18, 22.86, 0.5551, 3, 91.01, -31.84, 0.00551, 2, 4, 46.61, -59.03, 0.14929, 6, 24.33, 3.39, 0.85071, 2, 4, 36.21, -71.9, 0.02458, 6, 13.93, -9.48, 0.97542, 2, 6, -6.14, -8.93, 0.96919, 3, 60.25, -68.05, 0.03081, 2, 6, -23.44, -3.65, 0.7197, 3, 42.39, -65.13, 0.2803, 2, 6, -38.57, 2.51, 0.46946, 3, 26.58, -61.05, 0.53054, 2, 6, -54.42, 8.69, 0.29214, 3, 10.05, -57.04, 0.70786, 1, 2, 46.23, -12.02, 1, 1, 2, 26.56, -18, 1, 1, 2, 1.45, -18.93, 1, 1, 2, -12.63, -17.3, 1, 1, 2, -23.82, -13.82, 1, 1, 2, -31.85, -5.6, 1, 4, 4, -19.83, 36.54, 0.03022, 7, -42.54, 7.16, 0.05331, 3, 10.21, 34.07, 0.23647, 2, -36.43, 11.43, 0.68, 3, 4, -13.08, 30.14, 0.16204, 7, -35.79, 0.75, 0.34074, 3, 17.76, 28.63, 0.49722, 3, 4, -11.21, 37.26, 0.1427, 7, -33.92, 7.88, 0.62976, 3, 18.66, 35.93, 0.22755, 3, 4, -9.2, 49.16, 0.08034, 7, -31.91, 19.77, 0.81387, 3, 19.06, 47.99, 0.10579, 3, 4, 3.1, 53.07, 0.03914, 7, -19.61, 23.68, 0.90854, 3, 30.73, 53.51, 0.05232, 3, 4, 20.54, 52.58, 0.00038, 7, -2.17, 23.2, 0.99448, 3, 48.08, 55.35, 0.00515, 2, 4, 32.83, 47.51, 0.00019, 7, 10.12, 18.13, 0.99981, 4, 4, 30.45, -3.82, 0.94593, 7, 7.74, -33.21, 0.0113, 6, 8.17, 58.59, 0.03521, 3, 65.43, 0.77, 0.00756, 2, 4, 7.7, 3.5, 0.96435, 7, -15.01, -25.88, 0.03565, 3, 4, -20.7, 5.49, 0.01286, 7, -43.41, -23.9, 0.00891, 3, 13.49, 3.18, 0.97823, 3, 4, 27.17, -44.38, 0.22677, 6, 4.89, 18.04, 0.6887, 3, 67.58, -39.86, 0.08454, 3, 4, -2.39, -41.41, 0.0334, 6, -24.67, 21.01, 0.44967, 3, 37.89, -40.85, 0.51692, 2, 6, -50.19, 32, 0.17997, 3, 11.13, -33.37, 0.82003, 2, 4, 31.43, 31.3, 0.05261, 7, 8.72, 1.91, 0.94739, 3, 4, 5.62, 32.01, 0.17283, 7, -17.09, 2.63, 0.74159, 3, 36.04, 32.98, 0.08559, 4, 4, -34.29, 24.03, 0.00636, 7, -57, -5.35, 0.01113, 3, -2.45, 19.75, 0.2465, 2, -23.69, -2.83, 0.736], "hull": 24, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 0, 46, 6, 48, 48, 50, 50, 52, 52, 26, 8, 54, 54, 56, 56, 58, 2, 60, 60, 62, 62, 36, 36, 64], "width": 124, "height": 105}}, "body": {"body": {"x": 9, "y": -24.03, "rotation": -0.68, "width": 86, "height": 36}}, "weib": {"weib": {"type": "mesh", "uvs": [0.01112, 0.87527, 0, 0.79933, 0.06215, 0.79388, 0.14275, 0.83201, 0.25131, 0.84109, 0.36975, 0.82475, 0.46186, 0.77572, 0.53589, 0.69763, 0.59675, 0.57414, 0.59839, 0.47426, 0.57701, 0.31264, 0.58688, 0.19278, 0.66912, 0.09109, 0.76782, 0, 0.91422, 0, 1, 0.03958, 1, 0.128, 0.95154, 0.21396, 0.87367, 0.20659, 0.79804, 0.21642, 0.75354, 0.24834, 0.72685, 0.37851, 0.73575, 0.54797, 0.71795, 0.7027, 0.67114, 0.78391, 0.62452, 0.8648, 0.52663, 0.9483, 0.39983, 0.9876, 0.23743, 1, 0.11507, 0.99005, 0, 0.94585, 0.11388, 0.90161, 0.24479, 0.91608, 0.38744, 0.90401, 0.48828, 0.85364, 0.56722, 0.76037, 0.63994, 0.63854, 0.66914, 0.51146, 0.65852, 0.38919, 0.67019, 0.26317, 0.72385, 0.15211, 0.81789, 0.09785, 0.90839, 0.09646], "triangles": [14, 41, 13, 42, 14, 15, 42, 41, 14, 42, 15, 16, 41, 40, 13, 18, 41, 42, 17, 42, 16, 18, 42, 17, 19, 41, 18, 40, 12, 13, 11, 12, 40, 19, 40, 41, 20, 40, 19, 39, 11, 40, 39, 40, 20, 10, 11, 39, 21, 39, 20, 38, 10, 39, 38, 39, 21, 9, 10, 38, 37, 38, 21, 9, 38, 37, 37, 21, 22, 8, 9, 37, 36, 8, 37, 36, 37, 22, 7, 8, 36, 23, 36, 22, 23, 24, 36, 35, 7, 36, 24, 35, 36, 6, 7, 35, 34, 6, 35, 25, 35, 24, 34, 35, 25, 26, 34, 25, 5, 6, 34, 33, 5, 34, 33, 4, 5, 33, 34, 26, 27, 33, 26, 27, 32, 33, 0, 1, 2, 31, 2, 3, 0, 2, 31, 32, 3, 4, 31, 3, 32, 33, 32, 4, 30, 0, 31, 29, 31, 32, 30, 31, 29, 28, 29, 32, 27, 28, 32], "vertices": [1, 26, -4.82, 0.97, 1, 1, 26, -7.5, 10.2, 1, 1, 26, 0.93, 11.92, 1, 1, 26, 12.55, 8.54, 1, 2, 26, 27.56, 9.24, 0.67247, 27, -0.33, 9.92, 0.32753, 2, 27, 16.07, 8.37, 0.98826, 28, -7.68, 16.57, 0.01174, 2, 27, 29.81, 11.59, 0.34343, 28, 5.15, 10.68, 0.65657, 2, 28, 19.18, 9.03, 0.96426, 29, -3.32, 14.25, 0.03574, 2, 28, 36.43, 12.4, 0.03575, 29, 12.24, 6.08, 0.96425, 1, 29, 24.72, 6.03, 1, 2, 29, 44.88, 9.27, 0.24267, 30, -0.53, 10.2, 0.75733, 1, 30, 13.27, 16.19, 1, 2, 30, 29.86, 12.32, 0.91892, 31, -11.09, 5.89, 0.08108, 2, 30, 46.39, 5.83, 0.02349, 31, 4.36, 14.63, 0.97651, 1, 31, 24.23, 10.98, 1, 1, 31, 34.98, 3.98, 1, 1, 31, 32.99, -6.89, 1, 2, 30, 35.07, -29.24, 8e-05, 31, 24.47, -16.25, 0.99992, 2, 30, 30.73, -19.37, 0.05989, 31, 14.06, -13.41, 0.94011, 2, 30, 24.65, -10.8, 0.54774, 31, 3.58, -12.73, 0.45226, 3, 29, 53.27, -14.97, 0.00025, 30, 18.2, -7.32, 0.94995, 31, -3.18, -15.55, 0.0498, 2, 29, 36.95, -11.52, 0.60096, 30, 2.15, -11.89, 0.39904, 1, 29, 15.78, -13.06, 1, 2, 28, 34.71, -10.73, 0.47592, 29, -3.59, -10.88, 0.52408, 2, 28, 22.77, -12.22, 0.98654, 29, -13.83, -4.57, 0.01346, 2, 27, 49.31, -4.14, 0.00234, 28, 10.88, -13.71, 0.99766, 2, 27, 33.86, -11.4, 0.66776, 28, -5.78, -9.95, 0.33224, 1, 27, 15.71, -12.4, 1, 2, 26, 28.08, -10.71, 0.83625, 27, -6.51, -9.05, 0.16375, 1, 26, 11.17, -11.53, 1, 1, 26, -5.27, -7.98, 1, 1, 26, 9.66, -0.58, 1, 1, 26, 27.81, -0.17, 1, 1, 27, 16.31, -1.83, 1, 2, 27, 31.26, 1.29, 0.66536, 28, -0.03, 1.67, 0.33464, 1, 28, 15.9, 0.7, 1, 2, 28, 34.02, 2.68, 0.00062, 29, 4.27, 0, 0.99938, 1, 29, 20.21, -3.8, 1, 2, 29, 35.48, -2.11, 0.94278, 30, -3.54, -4.26, 0.05722, 1, 30, 11.06, 1.88, 1, 1, 30, 26.79, 2.04, 1, 1, 31, 8.95, 1.35, 1, 1, 31, 21.26, -0.73, 1], "hull": 31, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 0, 60, 0, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84], "width": 138, "height": 125}}, "weib2": {"weib": {"type": "mesh", "uvs": [0, 0.86985, 0.00387, 0.79314, 0.07743, 0.80818, 0.15645, 0.82924, 0.25862, 0.84127, 0.36352, 0.82322, 0.4548, 0.7781, 0.53245, 0.6999, 0.58149, 0.61116, 0.6101, 0.52393, 0.59103, 0.40511, 0.57332, 0.30735, 0.59512, 0.19455, 0.67277, 0.10281, 0.73543, 0.04265, 0.79538, 0, 0.90221, 0, 1, 0, 1, 0.11084, 1, 0.21048, 0.91923, 0.21424, 0.84431, 0.18792, 0.77108, 0.218, 0.75576, 0.28944, 0.73021, 0.37404, 0.74724, 0.4812, 0.74043, 0.61844, 0.70637, 0.74628, 0.64677, 0.84028, 0.57014, 0.93052, 0.46797, 0.97752, 0.32663, 1, 0.16826, 1, 0.04054, 0.98504, 0, 0.92676, 0.11605, 0.90941, 0.23287, 0.92568, 0.35159, 0.90575, 0.49024, 0.8553, 0.58224, 0.77554, 0.64118, 0.67531, 0.6493, 0.56733, 0.65331, 0.43442, 0.66019, 0.30642, 0.69865, 0.19996, 0.78497, 0.10336, 0.90247, 0.10288], "triangles": [46, 16, 17, 45, 14, 15, 46, 45, 15, 46, 15, 16, 46, 17, 18, 21, 45, 46, 45, 13, 14, 20, 46, 18, 20, 21, 46, 18, 19, 20, 21, 22, 45, 45, 44, 13, 22, 44, 45, 12, 13, 44, 23, 44, 22, 43, 12, 44, 43, 44, 23, 11, 12, 43, 24, 43, 23, 10, 11, 43, 42, 10, 43, 24, 42, 43, 42, 24, 25, 9, 10, 42, 41, 9, 42, 41, 42, 25, 8, 9, 41, 26, 41, 25, 40, 8, 41, 40, 41, 26, 27, 40, 26, 7, 8, 40, 39, 7, 40, 39, 40, 27, 6, 7, 39, 28, 39, 27, 38, 6, 39, 29, 38, 39, 29, 39, 28, 37, 4, 5, 38, 37, 5, 38, 5, 6, 30, 37, 38, 30, 38, 29, 31, 36, 37, 31, 37, 30, 0, 1, 2, 35, 2, 3, 0, 2, 35, 36, 3, 4, 36, 4, 37, 35, 3, 36, 34, 0, 35, 33, 34, 35, 32, 35, 36, 33, 35, 32, 32, 36, 31], "vertices": [1, 36, -6.42, 1.45, 1, 1, 36, -7.06, 11.03, 1, 1, 36, 3.24, 10.4, 1, 1, 36, 14.39, 9.12, 1, 2, 36, 28.57, 9.34, 0.61977, 37, 0.65, 9.68, 0.38023, 2, 37, 15.27, 8.74, 0.99592, 38, -8.08, 17.36, 0.00408, 2, 37, 28.79, 11.52, 0.46493, 38, 4.3, 11.25, 0.53507, 3, 37, 41.38, 18.73, 0.00183, 38, 18.66, 9.22, 0.96684, 39, -3.61, 14.72, 0.03133, 2, 38, 31.52, 11.07, 0.20891, 39, 7.58, 8.12, 0.79109, 1, 39, 18.54, 4.32, 1, 2, 39, 33.35, 7.17, 0.99353, 40, -9.75, 2.96, 0.00647, 2, 39, 45.53, 9.79, 0.25066, 40, -0.2, 10.96, 0.74934, 1, 40, 13.62, 15.09, 1, 2, 40, 28.82, 11.18, 0.94216, 41, -10.86, 4.36, 0.05784, 2, 40, 39.56, 7.2, 0.21318, 41, -1, 10.19, 0.78682, 1, 41, 8.1, 13.94, 1, 1, 41, 22.6, 11.28, 1, 1, 41, 35.88, 8.84, 1, 1, 41, 33.37, -4.78, 1, 1, 41, 31.12, -17.03, 1, 1, 41, 20.08, -15.48, 1, 2, 40, 30.83, -14.69, 0.10422, 41, 10.5, -10.38, 0.89578, 2, 40, 22.69, -7.63, 0.84223, 41, -0.12, -12.25, 0.15777, 3, 39, 48.14, -15.35, 0.02913, 40, 13.84, -10.06, 0.97051, 41, -3.81, -20.65, 0.00036, 2, 39, 37.51, -11.98, 0.54723, 40, 2.87, -12.03, 0.45277, 2, 39, 24.15, -14.52, 0.99013, 40, -7.76, -20.52, 0.00987, 2, 38, 44.8, -6.41, 0.02364, 39, 6.98, -13.83, 0.97636, 2, 38, 29.49, -12.97, 0.83306, 39, -9.06, -9.36, 0.16694, 1, 38, 15.19, -14.12, 1, 2, 37, 40.2, -10.54, 0.21418, 38, -0.24, -13.16, 0.78582, 2, 37, 25.16, -13.21, 0.96888, 38, -13.75, -6.04, 0.03112, 2, 36, 40.3, -9.21, 0.09158, 37, 5.51, -11.72, 0.90842, 1, 36, 18.6, -11.87, 1, 1, 36, 0.88, -12.16, 1, 1, 36, -5.56, -5.61, 1, 1, 36, 10.07, -1.51, 1, 1, 36, 26.32, -1.56, 1, 1, 37, 11.43, -0.97, 1, 2, 37, 31.48, 1.03, 0.64779, 38, -0.02, 1.33, 0.35221, 1, 38, 15.75, -2.1, 1, 2, 38, 30.59, -0.38, 0.55555, 39, -0.32, -0.24, 0.44445, 1, 39, 13.19, -1.16, 1, 2, 39, 29.81, -1.48, 0.99496, 40, -8.84, -6.34, 0.00504, 1, 40, 5.65, 0.5, 1, 1, 40, 19.88, 2.22, 1, 1, 41, 4.36, 1.5, 1, 1, 41, 20.32, -1.37, 1], "hull": 35, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 0, 68, 0, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92], "width": 138, "height": 125}}, "weib3": {"weib": {"type": "mesh", "uvs": [0, 0.88533, 0.00404, 0.79562, 0.07562, 0.80844, 0.16269, 0.83193, 0.26523, 0.84689, 0.36584, 0.82553, 0.45678, 0.79135, 0.53223, 0.71659, 0.58641, 0.6162, 0.60962, 0.53076, 0.58834, 0.41969, 0.56706, 0.29793, 0.59995, 0.189, 0.68508, 0.08647, 0.77795, 0.00317, 0.87856, 0, 0.9811, 0, 1, 0.09288, 1, 0.20822, 0.90564, 0.22958, 0.84954, 0.19754, 0.77214, 0.23813, 0.72571, 0.34065, 0.73732, 0.45173, 0.7528, 0.57989, 0.71797, 0.69523, 0.6638, 0.79776, 0.58834, 0.89388, 0.48773, 0.95369, 0.39099, 0.98786, 0.27104, 1, 0.15882, 0.98786, 0.06982, 0.97291, 0, 0.94514, 0.08897, 0.90446, 0.1814, 0.91811, 0.26702, 0.92492, 0.37591, 0.91438, 0.47849, 0.87825, 0.55674, 0.80996, 0.61162, 0.72588, 0.64555, 0.64001, 0.67491, 0.53852, 0.66486, 0.43706, 0.64592, 0.31676, 0.67493, 0.21328, 0.73585, 0.14243, 0.82187, 0.09736, 0.93025, 0.10557], "triangles": [47, 14, 15, 48, 15, 16, 48, 16, 17, 47, 15, 48, 47, 46, 14, 20, 47, 48, 46, 47, 20, 48, 17, 18, 19, 20, 48, 18, 19, 48, 46, 13, 14, 45, 12, 13, 46, 45, 13, 21, 46, 20, 45, 46, 21, 44, 12, 45, 11, 12, 44, 22, 45, 21, 44, 45, 22, 10, 11, 44, 43, 44, 22, 10, 44, 43, 43, 22, 23, 9, 10, 43, 42, 43, 23, 9, 43, 42, 42, 23, 24, 41, 9, 42, 41, 42, 24, 8, 9, 41, 25, 41, 24, 40, 8, 41, 40, 41, 25, 7, 8, 40, 26, 40, 25, 39, 7, 40, 39, 40, 26, 6, 7, 39, 27, 39, 26, 38, 39, 27, 38, 6, 39, 5, 6, 38, 37, 5, 38, 4, 5, 37, 36, 4, 37, 28, 38, 27, 37, 38, 28, 29, 37, 28, 30, 36, 37, 30, 37, 29, 0, 1, 2, 34, 2, 3, 0, 2, 34, 35, 3, 4, 34, 3, 35, 35, 4, 36, 33, 0, 34, 32, 33, 34, 31, 34, 35, 32, 34, 31, 35, 36, 30, 31, 35, 30], "vertices": [1, 42, -6.19, -0.47, 1, 1, 42, -7, 10.73, 1, 1, 42, 3, 10.34, 1, 1, 42, 15.28, 8.89, 1, 2, 42, 29.56, 8.76, 0.54339, 43, 1.39, 8.8, 0.45661, 2, 43, 15.52, 8.39, 0.99994, 44, -8.09, 16.93, 6e-05, 2, 43, 28.7, 9.84, 0.54218, 44, 3.2, 9.98, 0.45782, 3, 43, 40.89, 16.7, 0.00113, 44, 17.03, 7.91, 0.99087, 45, -5.7, 14.72, 0.008, 2, 44, 31.47, 10.15, 0.21179, 45, 6.96, 7.43, 0.78821, 1, 45, 17.68, 4.38, 1, 2, 45, 31.52, 7.51, 0.99852, 46, -11.53, 2.41, 0.00148, 2, 45, 46.7, 10.67, 0.23116, 46, 0.42, 12.29, 0.76884, 1, 46, 14.55, 14.83, 1, 2, 46, 31.43, 10.67, 0.84794, 47, -8.82, 6.06, 0.15206, 2, 46, 46.71, 4.42, 0.00979, 47, 5.67, 13.99, 0.99021, 1, 47, 19.39, 11.87, 1, 1, 47, 33.31, 9.31, 1, 1, 47, 33.78, -2.58, 1, 1, 47, 31.17, -16.76, 1, 2, 46, 30.32, -24.62, 0.00659, 47, 17.89, -17.03, 0.99341, 2, 46, 30.12, -15.9, 0.13329, 47, 10.99, -11.69, 0.86671, 2, 46, 20.55, -8.96, 0.86847, 47, -0.43, -14.75, 0.13153, 2, 45, 41.68, -11.3, 0.27031, 46, 6.23, -9.49, 0.72969, 2, 45, 27.82, -13.1, 0.9779, 46, -5.18, -17.55, 0.0221, 1, 45, 11.83, -15.47, 1, 2, 44, 35.43, -10.14, 0.37686, 45, -2.66, -10.87, 0.62314, 2, 44, 20.79, -12.55, 0.99449, 45, -15.58, -3.58, 0.00551, 2, 43, 43.64, -6.61, 0.02567, 44, 4.89, -12.18, 0.97433, 2, 43, 28.47, -10.9, 0.90166, 44, -9.72, -6.25, 0.09834, 1, 43, 14.51, -12.17, 1, 2, 42, 32.68, -10.14, 0.46418, 43, -1.98, -10.06, 0.53582, 1, 42, 17.13, -10.52, 1, 1, 42, 4.71, -10.16, 1, 1, 42, -5.28, -7.89, 1, 1, 42, 6.29, -1.35, 1, 1, 42, 19.16, -1.49, 1, 2, 42, 30.99, -0.89, 0.59627, 43, -0.48, -0.78, 0.40373, 1, 43, 14.47, -2.75, 1, 1, 43, 29.27, -1.41, 1, 1, 44, 10.2, -2.13, 1, 1, 44, 23.12, -1.27, 1, 1, 45, 4.1, -0.78, 1, 1, 45, 16.84, -4.64, 1, 2, 45, 29.5, -3.07, 0.99492, 46, -8.37, -7.89, 0.00508, 1, 46, 3.57, 1.61, 1, 1, 46, 16.84, 4.3, 1, 1, 46, 28.65, 1.17, 1, 1, 47, 9.5, 1.31, 1, 1, 47, 24.03, -2.4, 1], "hull": 34, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 0, 66, 0, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96], "width": 138, "height": 125}}, "jiao": {"jiao": {"type": "mesh", "uvs": [0.28911, 0, 0.47675, 0.02281, 0.55612, 0.03245, 0.62859, 0.05922, 0.69828, 0.10002, 0.74009, 0.19158, 0.76796, 0.28948, 0.82036, 0.31215, 0.86106, 0.38014, 0.88559, 0.49345, 0.89562, 0.57414, 0.92076, 0.62401, 0.94048, 0.69087, 0.98075, 0.7284, 1, 0.81777, 1, 0.9126, 0.90626, 0.92904, 0.82633, 0.94502, 0.76409, 0.93437, 0.74115, 0.85979, 0.74377, 0.76603, 0.74246, 0.69358, 0.69267, 0.64564, 0.65964, 0.60669, 0.62292, 0.56339, 0.53729, 0.53056, 0.44783, 0.4418, 0.4036, 0.33139, 0.39948, 0.17247, 0.44578, 0.07209, 0.4748, 0.02932, 0.28881, 0.00549, 0.32244, 0.03912, 0.34141, 0.1036, 0.35003, 0.18865, 0.33106, 0.28117, 0.29543, 0.34192, 0.26152, 0.36248, 0.28393, 0.398, 0.31382, 0.48678, 0.33377, 0.56491, 0.35073, 0.63848, 0.34508, 0.70745, 0.2725, 0.71359, 0.23386, 0.69366, 0.19145, 0.74884, 0.15469, 0.79635, 0.08588, 0.81781, 0.03216, 0.80708, 0.00671, 0.73044, 0, 0.61702, 0.03027, 0.55418, 0.06149, 0.55455, 0.09457, 0.54976, 0.12307, 0.5391, 0.14699, 0.53058, 0.12602, 0.51247, 0.10211, 0.47838, 0.06378, 0.44428, 0.03561, 0.4006, 0.00875, 0.33934, 0.01038, 0.26263, 0.02545, 0.19444, 0.06637, 0.11355, 0.13758, 0.03966, 0.19115, 0.00327, 0.24269, 0, 0.26301, 0.14875, 0.17046, 0.21374, 0.09684, 0.32149, 0.17993, 0.39503, 0.22305, 0.46857, 0.25144, 0.58316, 0.18308, 0.62079, 0.10946, 0.66526, 0.0411, 0.68407, 0.51943, 0.20895, 0.62241, 0.33246, 0.70223, 0.46016, 0.75759, 0.5439, 0.79621, 0.60671, 0.82453, 0.69254, 0.85929, 0.79093], "triangles": [74, 53, 54, 54, 55, 73, 74, 54, 73, 75, 51, 52, 50, 51, 75, 52, 53, 74, 75, 52, 74, 44, 73, 72, 49, 50, 75, 45, 73, 44, 74, 73, 45, 46, 74, 45, 48, 49, 75, 47, 75, 74, 47, 74, 46, 48, 75, 47, 72, 39, 40, 72, 73, 71, 41, 72, 40, 72, 43, 44, 72, 41, 43, 42, 43, 41, 81, 10, 11, 20, 21, 81, 12, 82, 81, 12, 81, 11, 14, 82, 13, 82, 14, 16, 13, 82, 12, 15, 16, 14, 20, 82, 19, 82, 20, 81, 82, 17, 19, 17, 18, 19, 16, 17, 82, 10, 80, 9, 81, 80, 10, 21, 80, 81, 7, 79, 78, 79, 7, 8, 79, 8, 9, 80, 79, 9, 23, 24, 78, 23, 78, 79, 22, 23, 79, 21, 22, 79, 21, 79, 80, 7, 78, 6, 60, 61, 69, 69, 68, 37, 70, 69, 37, 59, 60, 69, 58, 59, 69, 57, 58, 69, 71, 70, 37, 71, 37, 38, 70, 57, 69, 71, 38, 39, 56, 57, 70, 55, 56, 70, 71, 55, 70, 72, 71, 39, 73, 55, 71, 31, 66, 0, 31, 0, 30, 67, 66, 31, 67, 31, 32, 67, 32, 33, 67, 33, 34, 68, 64, 65, 63, 64, 68, 67, 68, 65, 67, 65, 66, 35, 67, 34, 69, 63, 68, 62, 63, 69, 61, 62, 69, 36, 67, 35, 37, 68, 67, 36, 37, 67, 30, 0, 1, 2, 30, 1, 76, 30, 2, 29, 30, 76, 28, 29, 76, 27, 28, 76, 3, 76, 2, 77, 3, 4, 77, 4, 5, 77, 5, 6, 77, 76, 3, 26, 27, 76, 26, 76, 77, 78, 77, 6, 25, 26, 77, 24, 25, 77, 78, 24, 77], "vertices": [3, 14, -38.12, -4.99, 0.02133, 10, -13.37, -4.43, 0.97833, 11, 13.82, 48.19, 0.00034, 3, 14, -10.77, 13.01, 0.97788, 10, -38.07, 17.06, 0.02209, 11, 42.76, 63.51, 2e-05, 2, 14, 0.8, 20.62, 0.99792, 10, -48.51, 26.15, 0.00208, 2, 14, 12.53, 26.05, 0.9999, 10, -56.9, 36, 0.0001, 2, 14, 24.8, 29.99, 0.80241, 15, -26.47, 27.64, 0.19759, 2, 14, 36.55, 26.67, 0.84, 15, -14.47, 25.43, 0.16, 3, 14, 46.78, 21.34, 0.664, 15, -3.78, 21.08, 0.33368, 16, -29.57, 13.97, 0.00232, 3, 14, 55.48, 24.98, 0.23831, 15, 4.54, 25.52, 0.72617, 16, -22.66, 20.4, 0.03552, 3, 14, 65.53, 23.54, 0.08067, 15, 14.68, 25.02, 0.79243, 16, -12.73, 22.51, 0.1269, 4, 14, 76.32, 16.54, 0.00389, 15, 26.07, 19.06, 0.54153, 16, -0.2, 19.67, 0.42079, 17, -13.83, 18.08, 0.03378, 3, 15, 33.24, 13.94, 0.17104, 16, 8.04, 16.54, 0.56654, 17, -5.27, 16.01, 0.26242, 3, 15, 40.07, 13.01, 0.02744, 16, 14.89, 17.4, 0.36855, 17, 1.42, 17.72, 0.60401, 2, 16, 22.77, 16.49, 0.1127, 17, 9.35, 17.8, 0.8873, 2, 16, 29.9, 20.28, 0.01751, 17, 15.96, 22.45, 0.98249, 1, 17, 26.04, 21.44, 1, 1, 17, 35.23, 17.15, 1, 1, 17, 29.93, 1.63, 1, 1, 17, 25.6, -11.7, 1, 2, 16, 28.46, -23.36, 0.0136, 17, 19.98, -21.03, 0.9864, 2, 16, 19.58, -22.49, 0.06237, 17, 11.07, -21.27, 0.93763, 3, 15, 27.9, -19.1, 0.03278, 16, 11.34, -16.76, 0.26813, 17, 2.17, -16.61, 0.6991, 4, 14, 69.66, -15.62, 0.00038, 15, 22.45, -13.58, 0.285, 16, 4.66, -12.82, 0.49254, 17, -4.95, -13.54, 0.22208, 4, 14, 59.67, -16.85, 0.06886, 15, 12.62, -15.74, 0.78337, 16, -4.3, -17.42, 0.13836, 17, -13.27, -19.23, 0.0094, 3, 14, 52.57, -17.05, 0.28135, 15, 5.57, -16.61, 0.69121, 16, -10.89, -20.06, 0.02744, 3, 14, 44.68, -17.28, 0.69384, 15, -2.26, -17.57, 0.30487, 16, -18.21, -23, 0.00129, 2, 14, 30.74, -23.6, 0.98867, 15, -15.56, -25.16, 0.01133, 1, 14, 12.61, -25.57, 1, 1, 14, -0.7, -20.92, 1, 2, 14, -11.65, -7.88, 0.99985, 10, -17.71, 21.85, 0.00015, 3, 14, -11.82, 5.54, 0.99409, 10, -30.59, 18.06, 0.0059, 11, 41.01, 56.18, 1e-05, 3, 14, -10.61, 12.25, 0.97603, 10, -37.38, 17.42, 0.02395, 11, 42.85, 62.74, 2e-05, 3, 14, -37.8, -5.49, 0.01968, 10, -12.98, -3.99, 0.97992, 11, 14.09, 47.67, 0.0004, 3, 14, -30.97, -4.76, 0.00465, 10, -15.51, 2.39, 0.98454, 11, 20.96, 47.74, 0.01081, 3, 14, -24.15, -8.22, 0.00124, 10, -14.03, 9.89, 0.95564, 11, 27.43, 43.65, 0.04312, 3, 14, -17.4, -14.51, 6e-05, 10, -9.78, 18.08, 0.87964, 11, 33.54, 36.75, 0.1203, 2, 10, -1.21, 24.04, 0.74126, 11, 36.02, 26.61, 0.25874, 2, 10, 7.64, 25.54, 0.5571, 11, 34.23, 17.81, 0.4429, 2, 10, 13.69, 23.77, 0.29455, 11, 30.41, 12.81, 0.70545, 3, 10, 12.84, 29.15, 0.09127, 11, 35.73, 11.67, 0.90652, 12, -11.76, -4.47, 0.00221, 3, 10, 14.36, 39.87, 0.00161, 11, 45.19, 6.39, 0.54935, 12, -6.29, 4.88, 0.44904, 2, 11, 52.58, 1.16, 0.05208, 12, -0.91, 12.15, 0.94792, 1, 12, 4.32, 18.73, 1, 2, 12, 11.16, 21.69, 0.99741, 13, -18.04, 12.71, 0.00259, 2, 12, 18.22, 11.2, 0.80184, 13, -5.54, 10.75, 0.19816, 2, 12, 19.85, 4.33, 0.31464, 13, 0.6, 7.28, 0.68536, 2, 12, 28.71, 1.04, 0.00024, 13, 9.03, 11.54, 0.99976, 1, 13, 16.34, 15.2, 1, 1, 13, 28.53, 14.98, 1, 1, 13, 37.44, 11.93, 1, 1, 13, 40.08, 2.99, 1, 1, 13, 38.73, -9.13, 1, 2, 11, 7.27, -25.98, 0.00205, 13, 32.19, -14.62, 0.99795, 2, 11, 11.88, -23.12, 0.01468, 13, 26.88, -13.46, 0.98532, 2, 11, 16.48, -19.62, 0.06781, 13, 21.14, -12.78, 0.93219, 3, 11, 20.08, -16.02, 0.20681, 12, 15.59, -20.7, 0.0069, 13, 16.05, -12.87, 0.78629, 3, 11, 23.11, -13.03, 0.49435, 12, 12.66, -17.61, 0.02819, 13, 11.79, -12.91, 0.47746, 3, 11, 18.99, -13.33, 0.77064, 12, 12.88, -21.73, 0.00867, 13, 14.96, -15.56, 0.22069, 3, 11, 13.53, -12.46, 0.92746, 12, 11.89, -27.18, 0.00045, 13, 18.28, -19.98, 0.07209, 2, 11, 5.94, -12.92, 0.99025, 13, 24.06, -24.93, 0.00975, 3, 10, 47.62, 3.51, 0.07225, 11, -0.7, -11.57, 0.92753, 13, 27.89, -30.51, 0.00022, 2, 10, 47.44, -4.54, 0.41971, 11, -8.14, -8.51, 0.58029, 2, 10, 42.3, -10.94, 0.81333, 11, -12.27, -1.41, 0.18667, 2, 10, 35.83, -15.22, 0.98869, 11, -13.94, 6.16, 0.01131, 1, 10, 24.95, -17.89, 1, 1, 10, 10.29, -16.81, 1, 2, 14, -51.41, -15.67, 0.00022, 10, 0.49, -14.35, 0.99978, 2, 14, -44.52, -9.92, 0.00304, 10, -6.9, -9.27, 0.99696, 3, 14, -32, -20.37, 5e-05, 10, -0.21, 5.6, 0.9527, 11, 18.45, 32.3, 0.04725, 2, 10, 16.86, 1.54, 0.97191, 11, 8.52, 17.84, 0.02809, 2, 10, 34.02, 3.11, 0.20576, 11, 3.82, 1.26, 0.79424, 2, 10, 27.15, 18.06, 0.04654, 11, 20.24, 2.3, 0.95346, 3, 11, 30.78, -0.37, 0.98885, 12, 0.16, -9.67, 0.00797, 13, -2.52, -16.67, 0.00318, 1, 12, 8.14, 0.87, 1, 3, 11, 33.57, -17.86, 0.04117, 12, 17.71, -7.25, 0.02445, 13, 7.64, -2.17, 0.93438, 2, 11, 25.26, -28.7, 0.00032, 13, 21.15, -0.15, 0.99968, 1, 13, 33.21, -0.63, 1, 2, 14, 7.27, 1.76, 0.99996, 10, -32.1, 37.46, 4e-05, 2, 14, 29.53, 2.23, 0.99973, 15, -19.17, 0.45, 0.00027, 2, 14, 48.87, -0.11, 0.45169, 15, 0.3, -0.08, 0.54831, 1, 15, 13.46, -0.07, 1, 2, 15, 22.96, -0.41, 0.14189, 16, 1.77, 0.05, 0.85811, 2, 16, 12.17, -0.68, 0.32657, 17, 0.98, -0.56, 0.67343, 2, 16, 24.3, -1.17, 4e-05, 17, 13.08, 0.47, 0.99996], "hull": 67, "edges": [4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 0, 2, 2, 4, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 0, 132, 66, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 74, 138, 58, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164], "width": 174, "height": 107}}, "feng": {"feng": {"x": 8.52, "y": 4.58, "width": 60, "height": 52}}, "feng2": {"feng": {"x": 8.9, "y": 4.34, "width": 60, "height": 52}}, "feng3": {"feng": {"x": 8.52, "y": 4.58, "width": 60, "height": 52}}, "feng4": {"feng": {"x": 8.9, "y": 4.34, "width": 60, "height": 52}}, "biyan": {"biyan": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1.01, -30.25, 38.62, 47.09, 68.3, 32.65, 30.69, -44.69], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 86, "height": 33}}}}], "events": {"atk": {}}, "animations": {"boss_attack1": {"bones": {"bone7": {"rotate": [{"angle": -3.25, "curve": "stepped"}, {"time": 0.2333, "angle": -3.25, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 23.44, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -3.25}]}, "bone43": {"rotate": [{"angle": -14.64}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -8.75, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.2667, "angle": 46.38, "curve": "stepped"}, {"time": 0.3667, "angle": 46.38, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "target2": {"translate": [{"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 3.15, "curve": 0.279, "c3": 0.622, "c4": 0.39}, {"time": 0.3667, "x": 3.24, "curve": 0.314, "c2": 0.27, "c3": 0.654, "c4": 0.63}, {"time": 0.4667, "x": 0.57, "curve": 0.333, "c2": 0.33, "c3": 0.671, "c4": 0.68}, {"time": 0.5333, "x": 0.25, "curve": 0.382, "c2": 0.55, "c3": 0.741}, {"time": 0.7}]}, "bone45": {"rotate": [{"angle": 10.88}]}, "bone23": {"rotate": [{"angle": -2.82, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2, "angle": 14.37, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 58.93, "curve": "stepped"}, {"time": 0.3667, "angle": 58.93, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -2.82}]}, "bone3": {"rotate": [{"angle": -0.38, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "angle": -22.8, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -9.47, "curve": "stepped"}, {"time": 0.3667, "angle": -9.47, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -0.38}], "translate": [{"x": 0.22, "y": 0.02}]}, "bone42": {"rotate": [{"angle": -2.55, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2, "angle": -20.71, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 19.96, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -2.55, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.7, "angle": -3.95}]}, "bone8": {"rotate": [{"angle": -11.5, "curve": "stepped"}, {"time": 0.3333, "angle": -11.5, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 15.19, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 0.7, "angle": 6.61}]}, "bone35": {"rotate": [{"angle": -1.52, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "angle": 19.92, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -1.52, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.6, "angle": -12.32, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.7, "angle": -19.69}]}, "bone16": {"rotate": [{"angle": -10.41, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -14.92, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -11.14, "curve": "stepped"}, {"time": 0.3667, "angle": -11.14, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -10.41}]}, "bone9": {"rotate": [{"angle": -2.07, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -50.42, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 14.3, "curve": "stepped"}, {"time": 0.3667, "angle": 14.3, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -2.07}]}, "bone46": {"rotate": [{"angle": 22.59}]}, "bone20": {"rotate": [{"angle": -5.97}]}, "bone21": {"rotate": [{"angle": -4.27}]}, "bone40": {"rotate": [{"angle": -4.28, "curve": 0.298, "c2": 0.2, "c3": 0.642, "c4": 0.58}, {"time": 0.1, "angle": 14.45, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -4.28, "curve": 0.298, "c2": 0.2, "c3": 0.642, "c4": 0.58}, {"time": 0.5667, "angle": -22.44, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.7, "angle": -0.52}]}, "bone10": {"rotate": [{"angle": 5.36, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 84.51, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 5.36}]}, "bone36": {"rotate": [{"angle": -3.88, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.2, "angle": -22.05, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 17.14, "curve": 0.243, "c3": 0.685, "c4": 0.73}, {"time": 0.6, "angle": -1.56, "curve": 0.373, "c2": 0.62, "c3": 0.713}, {"time": 0.6667, "angle": -3.88, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.7, "angle": -5.29}]}, "bone24": {"rotate": [{"angle": 9.98, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 43.32, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.2667, "angle": 12.39, "curve": "stepped"}, {"time": 0.3667, "angle": 12.39, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 9.98}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "y": -1, "curve": "stepped"}, {"time": 0.5, "y": -1, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "bone17": {"rotate": [{"angle": -1.69, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2, "angle": -56.97, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -22.08, "curve": "stepped"}, {"time": 0.3667, "angle": -22.08, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -1.69}]}, "bone39": {"rotate": [{"angle": -1.52, "curve": "stepped"}, {"time": 0.2667, "angle": -1.52, "curve": 0.324, "c2": 0.3, "c3": 0.668, "c4": 0.67}, {"time": 0.3667, "angle": -19.69, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 17.77, "curve": 0.273, "c3": 0.619, "c4": 0.41}, {"time": 0.7, "angle": 13.73}]}, "bone18": {"rotate": [{"angle": -4.27, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2, "angle": 50.36, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 24.34, "curve": "stepped"}, {"time": 0.3667, "angle": 24.34, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -4.27}]}, "bone5": {"translate": [{"x": -0.03, "y": -1.13}]}, "bone27": {"rotate": [{"angle": 10.89, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.1667, "angle": 1.2, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4, "angle": -16.97, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 23.06, "curve": 0.273, "c3": 0.619, "c4": 0.41}, {"time": 0.7333, "angle": 18.48, "curve": 0.316, "c2": 0.28, "c3": 0.661, "c4": 0.65}, {"time": 0.8333, "angle": 10.89}]}, "bone12": {"rotate": [{"angle": -0.9, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -20.21, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 9.35, "curve": "stepped"}, {"time": 0.3667, "angle": 9.35, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -0.9}]}, "bone13": {"rotate": [{"angle": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -25.49, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 23.53, "curve": "stepped"}, {"time": 0.3667, "angle": 23.53, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 1.1}]}, "bone11": {"rotate": [{"angle": 4.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -23.4, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -11.03, "curve": "stepped"}, {"time": 0.3667, "angle": -11.03, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 4.79}]}, "bone14": {"rotate": [{"angle": -8.17, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 37.74, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -40.85, "curve": "stepped"}, {"time": 0.3667, "angle": -40.85, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -8.17}]}, "bone6": {"translate": [{"x": 0.05, "y": 1.97}]}, "bone28": {"rotate": [{"angle": -2.05, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1, "angle": 19.31, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -2.05, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.5667, "angle": -20.22, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.7, "angle": 3.28}]}, "bone15": {"rotate": [{"angle": 11.05}]}, "bone37": {"rotate": [{"angle": 4.95, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2333, "angle": -13.22, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 27.46, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 4.95}]}, "bone22": {"rotate": [{"angle": -1.12, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2, "angle": -61.45, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 105.03, "curve": "stepped"}, {"time": 0.3667, "angle": 105.03, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -1.12}]}, "bone30": {"rotate": [{"angle": -4.56, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.2, "angle": -22.73, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 18.56, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -4.56, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.7, "angle": -5.97}]}, "bone34": {"rotate": [{"angle": 1.2, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": 6.82, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.2, "angle": 23.06, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 1.2, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -16.97, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.7, "angle": -14}]}, "bone": {"translate": [{"curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2, "x": 31.24, "y": 7.12, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -10.41, "y": -16.99, "curve": "stepped"}, {"time": 0.3667, "x": -10.41, "y": -16.99, "curve": 0.25, "c3": 0.75}, {"time": 0.7}], "scale": [{"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 1.09, "y": 1.09, "curve": "stepped"}, {"time": 0.3667, "x": 1.09, "y": 1.09, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}]}, "bone26": {"rotate": [{"angle": 5.41, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.2333, "angle": -12.76, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 28.03, "curve": 0.242, "c3": 0.661, "c4": 0.65}, {"time": 0.6, "angle": 10.14, "curve": 0.381, "c2": 0.59, "c3": 0.727}, {"time": 0.7, "angle": 5.41}]}, "bone41": {"rotate": [{"angle": -4.81, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.2333, "angle": 18.46, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -4.81, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -22.98}]}, "bone47": {"rotate": [{"angle": -2.12}]}, "bone4": {"rotate": [{"angle": 0.86, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.2, "angle": -20.22, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -27.96, "curve": "stepped"}, {"time": 0.3667, "angle": -27.96, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 0.86}], "translate": [{"x": 1.41, "y": -0.04}]}, "bone38": {"rotate": [{"angle": 1.82, "curve": "stepped"}, {"time": 0.1, "angle": 1.82, "curve": 0.349, "c2": 0.38, "c3": 0.693, "c4": 0.75}, {"time": 0.2, "angle": -16.35, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 21.67, "curve": 0.242, "c3": 0.661, "c4": 0.65}, {"time": 0.7, "angle": 5.97}]}, "bone29": {"rotate": [{"angle": -4.22, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.2333, "angle": 16.72, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -4.22, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -22.39}]}, "bone44": {"rotate": [{"angle": -3.66}]}, "bone31": {"rotate": [{"angle": 7.5, "curve": "stepped"}, {"time": 0.1, "angle": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -10.67, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 28.06, "curve": 0.242, "c3": 0.661, "c4": 0.65}, {"time": 0.7, "angle": 11.8}]}, "bone33": {"rotate": [{"angle": 3.93, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "angle": 21.57, "curve": 0.339, "c2": 0.35, "c3": 0.758}, {"time": 0.2333, "angle": 3.93, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4667, "angle": -14.24, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 26.23}]}, "bone32": {"rotate": [{"angle": 6.29, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1, "angle": 11.07, "curve": 0.381, "c2": 0.59, "c3": 0.727}, {"time": 0.2, "angle": 6.29, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.3, "angle": -11.88, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 29.14, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.7, "angle": 19.01}]}, "bone25": {"rotate": [{"angle": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -10.67, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 28.06, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 7.5, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.7, "angle": 0.13}]}, "bone50": {"translate": [{"x": 223.12}]}, "bone52": {"translate": [{"x": 223.12}]}}, "events": [{"time": 0.0333, "name": "atk"}]}, "boss_attack3": {"slots": {"feng": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.7, "color": "ffffff00"}], "attachment": [{"name": "feng"}]}, "feng3": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.7, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "color": "ffffff00"}], "attachment": [{"name": "feng"}]}, "feng4": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "color": "ffffff00"}], "attachment": [{"name": "feng"}]}, "light": {"color": [{"time": 0.5667, "color": "ffb30000", "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "color": "ffb300c2", "curve": 0.25, "c3": 0.75}, {"time": 0.9, "color": "ffb30000"}], "attachment": [{"time": 0.5667, "name": "light"}]}, "feng2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "color": "ffffff00"}], "attachment": [{"name": "feng"}]}}, "bones": {"bone50": {"translate": [{"x": 3.88, "y": 16.88}], "scale": [{"x": 0.398, "y": 0.398, "curve": "stepped"}, {"time": 0.5333, "x": 0.398, "y": 0.398, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": 0.876, "y": 0.876}]}, "bone51": {"scale": [{"x": 0.163, "y": 0.163, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": 1.306, "y": 1.306}]}, "bone36": {"rotate": [{"angle": -3.88}]}, "bone37": {"rotate": [{"angle": 4.95}]}, "bone38": {"rotate": [{"angle": 1.82}]}, "bone39": {"rotate": [{"angle": -1.52}]}, "bone45": {"rotate": [{"angle": 10.88}]}, "bone40": {"rotate": [{"angle": -4.28}]}, "bone46": {"rotate": [{"angle": 22.59}]}, "bone41": {"rotate": [{"angle": -4.81}]}, "bone": {"translate": [{"curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6333, "x": 7.12, "y": 6.44, "curve": 0.25, "c3": 0.75}, {"time": 1.2333}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -3.59, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone3": {"rotate": [{"angle": -0.38, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -3.97, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -0.38}], "translate": [{"x": 0.22, "y": 0.02}]}, "bone4": {"rotate": [{"angle": 0.86, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.4333, "angle": -8.07, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 0.86}], "translate": [{"x": 1.41, "y": -0.04}]}, "bone5": {"translate": [{"x": -0.03, "y": -1.13}]}, "bone6": {"translate": [{"x": 0.05, "y": 1.97}]}, "bone7": {"rotate": [{"angle": -3.25}]}, "bone8": {"rotate": [{"angle": -11.5}]}, "bone12": {"rotate": [{"angle": -0.9}]}, "bone13": {"rotate": [{"angle": 1.1}]}, "bone14": {"rotate": [{"angle": -8.17}]}, "bone15": {"rotate": [{"angle": 11.05}]}, "bone9": {"rotate": [{"angle": -2.07}]}, "bone10": {"rotate": [{"angle": 5.36}]}, "bone11": {"rotate": [{"angle": 4.79}]}, "bone16": {"rotate": [{"angle": -10.41}]}, "bone17": {"rotate": [{"angle": -1.69, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -40.87, "curve": "stepped"}, {"time": 0.4667, "angle": -40.87, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -1.69}]}, "bone18": {"rotate": [{"angle": -4.27, "curve": "stepped"}, {"time": 0.1333, "angle": -4.27, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.4667, "angle": -2.3, "curve": "stepped"}, {"time": 0.8, "angle": -2.3, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.0667, "angle": -4.27}]}, "bone20": {"rotate": [{"angle": -5.97, "curve": "stepped"}, {"time": 0.2333, "angle": -5.97, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 4.33, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.8, "angle": -3.47}]}, "bone21": {"rotate": [{"angle": -4.27, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2, "angle": 6.02, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -4.27, "curve": "stepped"}, {"time": 0.6667, "angle": -4.27, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.8, "angle": -0.49}]}, "bone22": {"rotate": [{"angle": -1.12, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 6.64, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -1.12}]}, "bone23": {"rotate": [{"angle": -2.82, "curve": "stepped"}, {"time": 0.2, "angle": -2.82, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 4.94, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -2.82}]}, "bone24": {"rotate": [{"angle": 9.98, "curve": "stepped"}, {"time": 0.3333, "angle": 9.98, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 14.53, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 9.98}]}, "bone43": {"rotate": [{"angle": -14.64}]}, "bone44": {"rotate": [{"angle": -3.66}]}, "bone47": {"rotate": [{"angle": -2.12}]}, "bone25": {"rotate": [{"angle": 7.5}]}, "bone26": {"rotate": [{"angle": 5.41}]}, "bone28": {"rotate": [{"angle": -2.05}]}, "bone29": {"rotate": [{"angle": -4.22}]}, "bone30": {"rotate": [{"angle": -4.56}]}, "bone31": {"rotate": [{"angle": 7.5}]}, "bone32": {"rotate": [{"angle": 6.29}]}, "bone33": {"rotate": [{"angle": 3.93}]}, "bone34": {"rotate": [{"angle": 1.2}]}, "bone35": {"rotate": [{"angle": -1.52}]}, "bone42": {"rotate": [{"angle": -2.55}]}, "bone52": {"translate": [{"x": 3.88, "y": 16.88}], "scale": [{"x": 0.398, "y": 0.398, "curve": "stepped"}, {"time": 0.7, "x": 0.398, "y": 0.398, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "x": 0.876, "y": 0.876}]}, "bone53": {"scale": [{"x": 0.163, "y": 0.163, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": 1.306, "y": 1.306}]}}, "deform": {"default": {"light": {"light": [{"time": 0.5667, "vertices": [-29.98382, 29.98383, 29.98383, 29.98383, 29.98383, -29.9838, -29.98382, -29.9838], "curve": 0.25, "c3": 0.75}, {"time": 0.9, "vertices": [-2.07363, 2.07367, 2.07364, 2.07367, 2.07364, -2.07355, -2.07363, -2.07355]}]}}}, "events": [{"time": 1.2333, "name": "atk"}]}, "boss_idle": {"bones": {"bone": {"translate": [{"curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3333, "x": 1.51, "y": 1.94, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1, "x": 5.31, "y": 6.82, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -1.35, "curve": 0.25, "c3": 0.75}, {"time": 2}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 0.02, "y": 1.58, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "bone3": {"rotate": [{"angle": -0.38, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.35, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -0.38}], "translate": [{"x": 0.22, "y": 0.02, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.78, "y": 0.08, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.22, "y": 0.02}]}, "bone4": {"rotate": [{"angle": 0.86, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.4667, "angle": 5.26, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 1.4667, "angle": -4.4, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "angle": 0.86}], "translate": [{"x": 1.41, "y": -0.04}]}, "bone5": {"translate": [{"x": -0.03, "y": -1.13, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -0.04, "y": -1.58, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -0.03, "y": -1.13}]}, "bone6": {"translate": [{"x": 0.05, "y": 1.97, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.08, "y": 2.76, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 0.05, "y": 1.97}]}, "bone7": {"rotate": [{"angle": -3.25, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": -19.56, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 2, "angle": -3.25}]}, "bone8": {"rotate": [{"angle": -11.5, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": -19.56, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 2, "angle": -11.5}]}, "bone12": {"rotate": [{"angle": -0.9}]}, "bone13": {"rotate": [{"angle": 1.1}]}, "bone14": {"rotate": [{"angle": -8.17}]}, "bone15": {"rotate": [{"angle": 11.05}]}, "bone9": {"rotate": [{"angle": -2.07}]}, "bone10": {"rotate": [{"angle": 5.36}]}, "bone11": {"rotate": [{"angle": 4.79}]}, "bone16": {"rotate": [{"angle": -10.41}]}, "bone17": {"rotate": [{"angle": -1.69, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -5.97, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -1.69}]}, "bone18": {"rotate": [{"angle": -4.27, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -5.97, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -4.27}]}, "bone20": {"rotate": [{"angle": -5.97, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -5.97}]}, "bone21": {"rotate": [{"angle": -4.27, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -5.97, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -4.27}]}, "bone22": {"rotate": [{"angle": -1.12, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.94, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -1.12}]}, "bone23": {"rotate": [{"angle": -2.82, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -3.94, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -2.82}]}, "bone24": {"rotate": [{"angle": 9.98, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 9.98}]}, "bone43": {"rotate": [{"angle": -14.64, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 12.01, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -14.64}]}, "bone44": {"rotate": [{"angle": -3.66, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.4333, "angle": -14.64, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": 12.01, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2, "angle": -3.66}]}, "bone45": {"rotate": [{"angle": 10.88, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.9, "angle": -14.64, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": 12.01, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2, "angle": 10.88}]}, "bone46": {"rotate": [{"angle": 22.59, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -9.91, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 22.59}]}, "bone47": {"rotate": [{"angle": -2.12, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 7.96, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -6.11, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -2.12}]}, "bone25": {"rotate": [{"angle": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -5.1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 7.5}]}, "bone26": {"rotate": [{"angle": 5.41, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 0.2333, "angle": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": -5.1, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 2, "angle": 5.41}]}, "bone27": {"rotate": [{"angle": 1.2, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -5.1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 1.2}]}, "bone28": {"rotate": [{"angle": -2.05, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.7, "angle": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": -5.1, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2, "angle": -2.05}]}, "bone29": {"rotate": [{"angle": -4.22, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.8667, "angle": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -5.1, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 2, "angle": -4.22}]}, "bone30": {"rotate": [{"angle": -4.56, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "angle": -5.1, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 7.5, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2, "angle": -4.56}]}, "bone31": {"rotate": [{"angle": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -5.1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 7.5}]}, "bone32": {"rotate": [{"angle": 6.29, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -5.1, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 6.29}]}, "bone33": {"rotate": [{"angle": 3.93, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -5.1, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 3.93}]}, "bone34": {"rotate": [{"angle": 1.2, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -5.1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 1.2}]}, "bone35": {"rotate": [{"angle": -1.52, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -5.1, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -1.52}]}, "bone36": {"rotate": [{"angle": -3.88, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -5.1, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": -3.88}]}, "bone37": {"rotate": [{"angle": 4.95, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2667, "angle": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -5.1, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 2, "angle": 4.95}]}, "bone38": {"rotate": [{"angle": 1.82, "curve": 0.349, "c2": 0.38, "c3": 0.693, "c4": 0.75}, {"time": 0.2667, "angle": 5.86, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.4667, "angle": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -5.1, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "angle": 1.82}]}, "bone39": {"rotate": [{"angle": -1.52, "curve": 0.324, "c2": 0.3, "c3": 0.668, "c4": 0.67}, {"time": 0.2667, "angle": 2.87, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6667, "angle": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -5.1, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -1.52}]}, "bone40": {"rotate": [{"angle": -4.28, "curve": 0.298, "c2": 0.2, "c3": 0.642, "c4": 0.58}, {"time": 0.2667, "angle": -0.46, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8667, "angle": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -5.1, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 2, "angle": -4.28}]}, "bone41": {"rotate": [{"angle": -4.81, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0667, "angle": -5.1, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.2667, "angle": -3.46, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.0667, "angle": 7.5, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 2, "angle": -4.81}]}, "bone42": {"rotate": [{"angle": -2.55, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2667, "angle": -5.1, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 7.5, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 2, "angle": -2.55}]}}}, "die": {"slots": {"biyan": {"attachment": [{"time": 0.0667, "name": "biyan"}]}}, "bones": {"bone49": {"rotate": [{"curve": 0.279, "c3": 0.622, "c4": 0.39}, {"time": 0.0667, "angle": -56.59, "curve": "stepped"}, {"time": 0.1333, "angle": -56.59, "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 0.3667, "angle": -95.24}], "translate": [{"curve": 0.279, "c3": 0.622, "c4": 0.39}, {"time": 0.0667, "x": -54.26, "y": 144.14, "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 0.1333, "x": -50.52, "y": 144.14, "curve": 0.329, "c2": 0.32, "c3": 0.758}, {"time": 0.3667, "x": -68.67, "y": 48.78}, {"time": 0.4, "x": -67.83, "y": 57.95, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -68.67, "y": 48.78}]}, "bone12": {"rotate": [{"angle": -0.9}]}, "bone13": {"rotate": [{"angle": 1.1}]}, "bone14": {"rotate": [{"angle": -8.17}]}, "bone15": {"rotate": [{"angle": 11.05}]}, "bone9": {"rotate": [{"angle": -2.07}]}, "bone10": {"rotate": [{"angle": 5.36}]}, "bone11": {"rotate": [{"angle": 4.79}]}, "bone16": {"rotate": [{"angle": -10.41}]}, "bone17": {"rotate": [{"curve": 0.259, "c3": 0.618, "c4": 0.45}, {"time": 0.1333, "angle": -28.67, "curve": 0.36, "c2": 0.43, "c3": 0.755}, {"time": 0.3667}]}, "bone18": {"rotate": [{"curve": 0.259, "c3": 0.618, "c4": 0.45}, {"time": 0.1333, "angle": 79.21, "curve": 0.36, "c2": 0.43, "c3": 0.755}, {"time": 0.3667, "angle": 41.87}]}, "bone22": {"rotate": [{"curve": 0.259, "c3": 0.618, "c4": 0.45}, {"time": 0.1333, "angle": 162.61, "curve": 0.36, "c2": 0.43, "c3": 0.755}, {"time": 0.3667, "angle": 121.11}]}, "bone23": {"rotate": [{"curve": 0.259, "c3": 0.618, "c4": 0.45}, {"time": 0.1333, "angle": -50.21, "curve": 0.36, "c2": 0.43, "c3": 0.755}, {"time": 0.3667, "angle": 87.22}]}, "bone24": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -86.35}]}, "bone25": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 79.72}]}, "target2": {"translate": [{"curve": 0.259, "c3": 0.618, "c4": 0.45}, {"time": 0.1333, "x": -94.12, "y": -15.62, "curve": 0.332, "c2": 0.33, "c3": 0.679, "c4": 0.7}, {"time": 0.2333, "x": -58.39, "y": -25.54, "curve": 0.382, "c2": 0.58, "c3": 0.732}, {"time": 0.3667, "x": -50.19, "y": -28.45}, {"time": 0.4, "x": -69.06, "y": -28.45, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -50.19, "y": -28.45}]}, "target4": {"translate": [{"curve": 0.259, "c3": 0.618, "c4": 0.45}, {"time": 0.1333, "x": -26.94, "y": 3.05, "curve": 0.332, "c2": 0.33, "c3": 0.679, "c4": 0.7}, {"time": 0.2333, "x": -32.37, "y": -18.31, "curve": 0.382, "c2": 0.58, "c3": 0.732}, {"time": 0.3667, "x": 31.77, "y": -35.41}, {"time": 0.4, "x": 12.9, "y": -35.41, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 31.77, "y": -35.41}]}, "bone31": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 84.86}]}, "bone37": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 78.91}]}, "bone2": {"rotate": [{"time": 0.3667, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.4333, "angle": 5.58, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4667}]}, "bone3": {"rotate": [{"time": 0.3667, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.4333, "angle": 5.58, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4667}]}}}, "hurt": {"slots": {"biyan": {"attachment": [{"time": 0.0667, "name": "biyan"}]}}, "bones": {"bone": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -5.61, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}], "translate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 8.26, "y": -5.16, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone2": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -11.87, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone3": {"rotate": [{"angle": -4.37, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -11.87, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": -4.37}]}, "bone4": {"rotate": [{"angle": -12.09, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -13.9, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": -12.09}]}, "bone7": {"rotate": [{"angle": -9.36, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "angle": -25.43, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.1667, "angle": -16.08, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2667, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "angle": -9.36}]}, "bone8": {"rotate": [{"angle": -3.31, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": -25.43, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1667, "angle": -22.13, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.3, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": -3.31}]}, "bone12": {"rotate": [{"angle": -0.9}]}, "bone13": {"rotate": [{"angle": 1.09}]}, "bone14": {"rotate": [{"angle": -8.13}]}, "bone15": {"rotate": [{"angle": 11}]}, "bone9": {"rotate": [{"angle": -2.07}]}, "bone10": {"rotate": [{"angle": 5.36}]}, "bone11": {"rotate": [{"angle": 4.93}]}, "bone16": {"rotate": [{"angle": -10.63}]}, "bone17": {"rotate": [{"angle": -2.82, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -7.66, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": -2.82}]}, "bone18": {"rotate": [{"angle": -6.66, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -7.66, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": -6.66}]}, "bone19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -7.66, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone22": {"rotate": [{"angle": 2.29, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 6.23, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 2.29}]}, "bone23": {"rotate": [{"angle": 5.42, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 6.23, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": 5.42}]}, "bone24": {"rotate": [{"angle": 6.23, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 6.23}]}, "bone25": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -14.53, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone26": {"rotate": [{"angle": -1.89, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -14.53, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": -1.89}]}, "bone27": {"rotate": [{"angle": -5.35, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -14.53, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": -5.35}]}, "bone28": {"rotate": [{"angle": -9.19, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -14.53, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "angle": -9.19}]}, "bone29": {"rotate": [{"angle": -12.64, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -14.53, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": -12.64}]}, "bone30": {"rotate": [{"angle": -14.53, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -14.53}]}, "bone31": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -14.53, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone32": {"rotate": [{"angle": -1.89, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -14.53, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": -1.89}]}, "bone33": {"rotate": [{"angle": -5.35, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -14.53, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": -5.35}]}, "bone34": {"rotate": [{"angle": -9.19, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -14.53, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "angle": -9.19}]}, "bone35": {"rotate": [{"angle": -12.64, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -14.53, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": -12.64}]}, "bone36": {"rotate": [{"angle": -14.53, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -14.53}]}, "bone37": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -14.53, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "bone38": {"rotate": [{"angle": -1.89, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -14.53, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": -1.89}]}, "bone39": {"rotate": [{"angle": -5.35, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -14.53, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": -5.35}]}, "bone40": {"rotate": [{"angle": -9.19, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -14.53, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "angle": -9.19}]}, "bone41": {"rotate": [{"angle": -12.64, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -14.53, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": -12.64}]}, "bone42": {"rotate": [{"angle": -14.53, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -14.53}]}, "bone43": {"rotate": [{"angle": -7.17, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -19.48, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": -7.17}]}, "bone44": {"rotate": [{"angle": -7.17, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -19.48, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": -7.17}]}, "bone45": {"rotate": [{"angle": -7.17, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -19.48, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": -7.17}]}, "bone46": {"rotate": [{"angle": -7.17, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -19.48, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": -7.17}]}, "bone47": {"rotate": [{"angle": -7.17, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -19.48, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": -7.17}]}}}}}