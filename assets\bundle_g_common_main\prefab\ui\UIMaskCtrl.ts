import { _decorator, Component, Node } from "cc";
import { UIMgr } from "../../../GameScrpit/lib/ui/UIMgr";
import { UIMask } from "../../../GameScrpit/game/ui/UIMask";
const { ccclass, property } = _decorator;

// 循环间隔
const maxCd = 1;

@ccclass("UIMaskCtrl")
export class UIMaskCtrl extends Component {
  cd: number = maxCd;

  start() {}

  update(dt: number): void {
    this.cd -= dt;
    if (this.cd <= 0) {
      this.cd = maxCd;
      (UIMgr.instance.getByName("UIMask") as UIMask).onShow();
    }
  }
}
