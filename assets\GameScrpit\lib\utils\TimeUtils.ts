import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class TimeUtils {
  // 与服务器的时间差，毫秒
  public static serverTimeBase: number;

  // 服务器时间
  public static get serverTime() {
    return new Date().getTime() - (TimeUtils?.serverTimeBase ?? 0);
  }

  // 时间格式化，加0版， 00:00:00， 超过24小时，显示:x天y小时
  public static timeformatHMS(colEndTime: number): string {
    //把剩余时间毫秒数转化为秒
    var allSecond = colEndTime < 0 ? 0 : colEndTime / 1000;
    let hours = Math.floor(allSecond / (60 * 60));
    let minutes = Math.floor((allSecond % (60 * 60)) / 60);
    let seconds = Math.floor(allSecond % 60);
    let h_str = hours < 10 ? `0${hours}` : `${hours}`;
    let m_str = minutes < 10 ? `0${minutes}` : `${minutes}`;
    let s_str = seconds < 10 ? `0${seconds}` : `${seconds}`;

    if (hours > 24) {
      return `${Math.floor(hours / 24)}天${hours % 24}小时`;
    } else if (hours > 0) {
      return `${h_str}:${m_str}:${s_str}`;
    } else {
      return `${m_str}:${s_str}`;
    }
  }

  // 取一天开始时间戳
  public static getDayBegin(ts: number = 0) {
    let nowTime = ts || TimeUtils.serverTime;
    nowTime += 8 * 3600 * 1000;
    let dayMs = nowTime % 86400000;
    return nowTime - dayMs - 8 * 3600 * 1000;
  }

  // 取当前小时数
  public static getHour(ts: number = 0) {
    let nowTime = ts || TimeUtils.serverTime;
    nowTime += 8 * 3600 * 1000;
    let dayMs = nowTime % 86400000;
    return Math.floor(dayMs / 3600000);
  }

  public static formatTimestamp(timestamp: number, format: string = "YYYY/MM/DD HH:mm:ss"): string {
    const date = new Date(timestamp);
    // 检查 date 是否为有效日期
    if (isNaN(date.getTime())) {
      throw new Error("Invalid timestamp");
    }
    const year = date.getFullYear();
    const month = `0${date.getMonth() + 1}`.slice(-2); // 月份从0开始，需要加1，并补零
    const day = `0${date.getDate()}`.slice(-2);
    const hour = `0${date.getHours()}`.slice(-2);
    const minute = `0${date.getMinutes()}`.slice(-2);
    const second = `0${date.getSeconds()}`.slice(-2);

    // 格式化日期
    return format
      .replace("YYYY", String(year))
      .replace("MM", month)
      .replace("DD", day)
      .replace("HH", hour)
      .replace("mm", minute)
      .replace("ss", second);
  }
  /**
   * 获取时间字符串转换为时间戳
   * @param date
   * @param format
   * @returns
   */
  public static getTimestampFromFormat(date: string, format: string = "YYYY/MM/DD HH:mm:ss"): number {
    // 获取当前日期

    // 获取当前日期
    const today = new Date();
    const currentYear = today.getFullYear();
    const currentMonth = today.getMonth(); // 0-based (0 = January)
    const currentDay = today.getDate();

    // 创建一个格式化映射关系
    const formatMap: { [key: string]: string } = {
      YYYY: "(\\d{4})", // 四位数的年份
      MM: "(\\d{2})", // 两位数的月份
      DD: "(\\d{2})", // 两位数的日期
      HH: "(\\d{2})", // 两位数的小时
      mm: "(\\d{2})", // 两位数的分钟
      ss: "(\\d{2})", // 两位数的秒钟
    };

    // 检查输入格式是否包含日期部分
    const hasDatePart = format.includes("YYYY") || format.includes("MM") || format.includes("DD");

    // 如果没有日期部分，自动使用当前日期
    if (!hasDatePart) {
      format = "YYYY/MM/DD " + format; // 自动补充当前日期部分
      date = `${currentYear}/${`0${String(currentMonth + 1)}`.slice(-2)}/${`0${String(currentDay)}`.slice(-2)} ${date}`;
      log.log(date);
    }

    // 生成正则表达式匹配 date 字符串
    let regexStr = format;
    for (const key in formatMap) {
      regexStr = regexStr.replace(key, formatMap[key]);
    }

    const regex = new RegExp(regexStr);

    // 使用正则表达式提取 date 字符串中的各个部分
    const matches = regex.exec(date);

    if (!matches) {
      throw new Error("Invalid date string format.");
    }

    // 获取各个部分的值
    let year = currentYear;
    let month = currentMonth;
    let day = currentDay;
    let hour = 0;
    let minute = 0;
    let second = 0;

    // 如果格式中包含年份、月份、日期，替换相应的值
    if (format.includes("YYYY")) year = parseInt(matches[1], 10);
    if (format.includes("MM")) month = parseInt(matches[2], 10) - 1; // 月份从 0 开始
    if (format.includes("DD")) day = parseInt(matches[3], 10);
    if (format.includes("HH")) hour = parseInt(matches[4], 10);
    if (format.includes("mm")) minute = parseInt(matches[5], 10);
    if (format.includes("ss")) second = parseInt(matches[6], 10);

    // 创建日期对象，使用当前日期和用户输入的时间部分
    const newDate = new Date(year, month, day, hour, minute, second);

    // 返回时间戳（单位：毫秒）
    return newDate.getTime();
  }

  /**时间戳转换倒计时
   * @param timeStamp 秒
   */
  public static formatTimeLeft(timeStamp: number): string {
    // 将剩余时间转换为天、小时、分钟和秒
    const days = Math.floor(timeStamp / (60 * 60 * 24));
    const hours = Math.floor((timeStamp % (60 * 60 * 24)) / (60 * 60));
    const minutes = Math.floor((timeStamp % (60 * 60)) / 60);
    const seconds = Math.floor(timeStamp % 60);

    // 构建倒计时字符串
    let countdownString = "";
    if (days > 0) {
      countdownString += `${days}:`;
    }

    if (hours > 0) {
      countdownString += `${hours}:`;
    } else if (days > 0) {
      if (minutes > 0 || seconds > 0) {
        countdownString += `00:`;
      }
    }

    if (minutes > 0) {
      countdownString += `${minutes}:`;
    } else if (hours > 0) {
      if (seconds > 0) {
        countdownString += `00:`;
      }
    }

    if (seconds > 0) {
      countdownString += `${seconds}`;
    } else {
      countdownString += `00`;
    }

    return countdownString.trim();
  }
  /**
   * 如果时间戳表示的是当天的时间，则输出 时:分。
   * 如果时间戳表示的是昨天，则输出 昨天。
   * 如果时间戳表示的是前天，则输出 前天。
   * 如果时间戳表示的是更早的日期，则输出类似 3天前 这样的格式。
   * @param timestamp
   * @returns
   */
  public static getTimeAgo(timestamp: number): string {
    const now = new Date();
    const targetDate = new Date(timestamp);

    // 计算两个日期的午夜时间（忽略时分秒）
    const getMidnight = (date: Date) => new Date(date.getFullYear(), date.getMonth(), date.getDate());
    const midnightNow = getMidnight(now);
    const midnightTarget = getMidnight(targetDate);

    // 计算天数差
    const diffMs = midnightNow.getTime() - midnightTarget.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      // 今天，格式化为HH:mm
      const hours = ("0" + targetDate.getHours()).slice(-2); // 例如 7 → "07"
      const minutes = ("0" + targetDate.getMinutes()).slice(-2);
      return `${hours}:${minutes}`;
    } else if (diffDays === 1) {
      return "昨天";
    } else if (diffDays === 2) {
      return "前天";
    } else {
      // 超过三天可扩展为实际天数（根据需求调整）
      // return `${diffDays}天前`;
      return "三天前";
    }
  }
}
