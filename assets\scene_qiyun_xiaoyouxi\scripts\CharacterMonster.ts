import { _decorator, sp, tween, UIOpacity } from "cc";
import { CharacterBase, AttrRole, RoleSideEnum, RoleStatusEnum } from "./CharacterBase";
import { AudioMgr } from "../../platform/src/AudioHelper";
const { ccclass, property } = _decorator;

// 角色控制器
// 控制角色的移动、动画、状态切换等
@ccclass("CharacterMonster")
export class CharacterMonster extends CharacterBase {
  public roleName: string = "monster";

  private _spineXiaoGuaiChong: sp.Skeleton;

  protected _attr: AttrRole = new AttrRole({
    walkSpeed: 100,
    runSpeed: 800,
    acceleration: 400,
    hpMax: 1,
    attack: 1,
    def: 0,
  });

  public initMonster(monsterAniName: string) {
    this._spineAniAttack = monsterAniName;
    this._spineAniIdle = monsterAniName;
    this._spineAniAttack = monsterAniName;
    this._spineAniDefault = monsterAniName;
    this._spineAniDie = monsterAniName;
    this._spineAniRun = monsterAniName;
    this._spineAniWalk = monsterAniName;
  }

  start() {
    super.start();
    this._spineXiaoGuaiChong = this.getNode("spine_xiaoguaichong").getComponent(sp.Skeleton);
  }

  update(dtTime: number) {
    super.update(dtTime);
  }

  public idle(side?: RoleSideEnum): void {
    super.idle(side);
    let opacity = this.spineCharacter.node.getComponent(UIOpacity);
    opacity.opacity = 255;
  }

  // 播放死亡动画
  public die(cbEnd?: Function) {
    if (this._status === RoleStatusEnum.die) {
      return;
    }

    // 渐隐
    let opacity = this.spineCharacter.node.getComponent(UIOpacity);
    opacity.opacity = 255;
    tween(opacity).to(0.2, { opacity: 0 }).start();

    this._status = RoleStatusEnum.die;
    this.getNode("PbMonsterBoom").active = true;
    let spine = this.getNode("PbMonsterBoom").getComponentInChildren(sp.Skeleton);

    spine.setAnimation(0, "animation", false);
    spine.setCompleteListener(() => {
      spine.setCompleteListener(null);
      this.getNode("PbMonsterBoom").active = false;
      cbEnd && cbEnd();
    });
  }

  /**
   * 出生动画
   */
  public born() {
    AudioMgr.instance.playEffect(1663);
    this.spineCharacter.setAnimation(0, this._spineAniIdle + "_1", false);
    this.spineCharacter.setCompleteListener(() => {
      this.spineCharacter.setCompleteListener(null);
      this.spineCharacter.setAnimation(0, this._spineAniDefault, true);
    });
  }

  /**
   *
   * 显示语音logo
   *
   * @param lastSecond 持续时间
   */
  public showSoundSpine(lastSecond: number = 0) {
    this._spineXiaoGuaiChong = this.getNode("spine_xiaoguaichong").getComponent(sp.Skeleton);
    this._spineXiaoGuaiChong.node.active = true;

    if (this._spineXiaoGuaiChong) {
      this._spineXiaoGuaiChong.setAnimation(0, "animation1", false);
      this._spineXiaoGuaiChong.setCompleteListener(() => {
        this._spineXiaoGuaiChong.setCompleteListener(null);
        this._spineXiaoGuaiChong.setAnimation(0, "animation2", true);
      });
    }

    if (lastSecond > 0) {
      tween(this._spineXiaoGuaiChong)
        .delay(lastSecond)
        .call(() => {
          this._spineXiaoGuaiChong.node.active = false;
        });
    }
  }

  public run(side?: RoleSideEnum): void {
    super.run(side);

    // this.showSoundSpine(0);
  }
}
