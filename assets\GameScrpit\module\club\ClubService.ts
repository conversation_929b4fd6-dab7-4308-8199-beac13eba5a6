import { instantiate } from "cc";
import {
  ClubAdjustPositionMessage,
  ClubApplyMessage,
  ClubBossBuddyMessage,
  ClubJoinResponse,
  ClubMessage,
  ClubRewardMessage,
} from "../../game/net/protocol/Club";
import MsgMgr from "../../lib/event/MsgMgr";
import { PlayerModule } from "../player/PlayerModule";
import { CLUB_POSITION, ClubEvent } from "./ClubConstant";
import { ClubModule } from "./ClubModule";
import { BadgeMgr, BadgeType, IBadgeCreate } from "../../game/mgr/BadgeMgr";
import TickerMgr from "../../lib/ticker/TickerMgr";
import { TimeUtils } from "../../lib/utils/TimeUtils";
import { LangMgr } from "../../game/mgr/LangMgr";
import TipMgr from "../../lib/tips/TipMgr";
import { UIClubConfirmDialog, UIClubConfirmDialogArgs } from "../../game/ui/club/UIClubConfirmDialog";
import { UIMgr } from "../../lib/ui/UIMgr";
import { ClubRouteItem } from "./ClubRoute";
import { CityRouteName } from "../city/CityConstant";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { RouteManager } from "db://assets/platform/src/core/managers/RouteManager";
const log = Logger.getLoger(LOG_LEVEL.WARN);
// 数据逻辑层
export class ClubService {
  private timerId = 0;
  public init() {
    MsgMgr.on(ClubEvent.CLUB_DATA_CHANGE, this.updateBadge, this);
    // this.updateBadge();
    let bossList = ClubModule.config.getClubBossList();
    let badgeList: IBadgeCreate[] = [];
    for (let i = 0; i < bossList.length; i++) {
      bossList[i];
      badgeList.push({ name: `boss_award_${i}` });
    }

    BadgeMgr.instance.addBadgeItemExt(BadgeType.UIClubMain.btn_boss, badgeList);
  }

  // 判断是否已领取伤害宝箱
  private checkBossHurtBoxIsTake(index: number, hurtRewardIndexList: number[]): boolean {
    let activeTask = hurtRewardIndexList;
    for (let i = 0; i < activeTask.length; i++) {
      if (activeTask[i] == index) {
        return true;
      }
    }
    return false;
  }
  // 判断是否有未领取的宝箱
  private checkHurtAward(totalHurt: number, hurtRewardIndexList: number[]): boolean {
    let harmAwardList = ClubModule.config.getClubBossHarmList();
    for (let i = 0; i < harmAwardList.length; i++) {
      if (totalHurt >= harmAwardList[i] && !this.checkBossHurtBoxIsTake(i, hurtRewardIndexList)) {
        return true;
      }
    }
    return false;
  }
  /**
   * Checks if the task box at the given index is taken.
   * @param index The index of the task box to check.
   * @returns True if the task box is taken, false otherwise.
   */
  private checkTaskBoxIsTake(index: number): boolean {
    let activeTask = ClubModule.data.clubFormMessage.active.activeTaskList;
    for (let i = 0; i < activeTask.length; i++) {
      if (activeTask[i] == index) {
        return true;
      }
    }
    return false;
  }
  private checkTaskBadge(): boolean {
    for (let i = 0; i < 5; i++) {
      if (ClubModule.data.clubFormMessage.active.activeVal >= ClubModule.config.getActiveByLevel(i)) {
        if (!this.checkTaskBoxIsTake(i)) {
          return true;
        }
      }
    }
    let taskList = ClubModule.config.getTaskList();
    for (let i = 0; i < taskList.length; i++) {
      // 是否有未领取的任务奖励
      let tast = taskList[i];
      let finishTimes = ClubModule.data.clubFormMessage.dailyTask.taskCntMap[tast.taskId];
      let isFinish = finishTimes >= tast.finish;
      let isTaked = false;
      for (let i = 0; i < ClubModule.data.clubFormMessage.dailyTask.completeTaskList.length; i++) {
        if (ClubModule.data.clubFormMessage.dailyTask.completeTaskList[i] == tast.id) {
          isTaked = true;
          break;
        }
      }
      if (isFinish && !isTaked) {
        return true;
      }
    }
    return false;
  }

  private updateBadge() {
    // ClubModule.data.clb
    if (!ClubModule.data.clubMessage) {
      return;
    }
    let joinTime = ClubModule.data.joinTime;
    if (joinTime && joinTime + 24 * 60 * 60 * 1000 > TimeUtils.serverTime) {
      // 加入联盟24小时内不显示红点
      return;
    }

    //是
    let buddyMessage = ClubModule.data.bossMessage;

    let donateMap = ClubModule.data.clubFormMessage.donateMap;
    let timeInterval = Math.ceil(((donateMap[1]?.nextDonateDeadline ?? 0) - TimeUtils.serverTime) / 1000);
    log.log(
      "-----------updateBadge-----------",
      timeInterval,
      donateMap[1]?.nextDonateDeadline ?? 0,
      TimeUtils.serverTime
    );
    if (this.timerId == 0) {
      if (timeInterval > 0) {
        log.log("++++++++++++updateBadge++++++++++++", timeInterval);
        this.timerId = TickerMgr.setInterval(
          timeInterval + 3,
          () => {
            log.log("============updateBadge============", timeInterval);
            TickerMgr.clearInterval(this.timerId);
            this.updateBadge();
            this.timerId = 0;
          },
          false
        );
      }
    }

    let isDonateBadgeShow = ClubModule.config.getDonateConfig(1).max > (donateMap[1]?.count ?? 0) && timeInterval < 0;
    BadgeMgr.instance.setShowById(BadgeType.UIClubMain.btn_donate.id, isDonateBadgeShow);

    BadgeMgr.instance.setShowById(
      BadgeType.UIClubMain.btn_boss.btn_hurt.id,
      this.checkHurtAward(buddyMessage.bossTrain.damageHp, buddyMessage.bossTrain.hurtRewardIndexList)
    );
    BadgeMgr.instance.setShowById(
      BadgeType.UIClubMain.btn_boss.btn_remaind.id,
      ClubModule.data.bossChance.remainCnt > 0 && buddyMessage?.bossTrain?.bossIndex >= 0
    );
    BadgeMgr.instance.setShowById(BadgeType.UIClubMain.btn_task.id, this.checkTaskBadge());

    if (buddyMessage?.bossTrain?.bossIndex >= 0) {
      let isShow =
        buddyMessage.bossTrain.killUserMessage && !buddyMessage.bossTrain.isTakeKill && buddyMessage.bossTrain.isHurt;
      BadgeMgr.instance.setShowById(
        BadgeType.UIClubMain.btn_boss[`boss_award_${buddyMessage.bossTrain.bossIndex}`].id,
        isShow
      );
    }
    ClubModule.api.listApply((data: ClubApplyMessage[]) => {
      BadgeMgr.instance.setShowById(BadgeType.UIClubMain.btn_apply.id, data.length > 0);
    });
    log.log("BadgeMgr.instance.isShow", BadgeMgr.instance.isShow(BadgeType.UIClubMain.btn_boss.id));
  }

  // 进入联盟24小时内不可操作
  public isInterceptOperation() {
    let joinTime = ClubModule.data.joinTime;
    if (joinTime && joinTime + 24 * 60 * 60 * 1000 > TimeUtils.serverTime) {
      let str = LangMgr.txMsgCode(113, [], ".新入盟成员24小时内不能操作.");
      TipMgr.showTip(str);
      return true;
    }
    return false;
  }

  /**
   * 获取当前联盟副盟主个数
   * */
  public getVicePresidentCount(): number {
    let count = 0;
    for (let i = 0; i < ClubModule.data.clubMessage?.memberList.length; i++) {
      if (ClubModule.data.clubMessage?.memberList[i].position == CLUB_POSITION.副盟主) {
        count++;
      }
    }
    return count;
  }

  // 显示踢出联盟提示
  public showExitedTips(force: boolean = false) {
    // 被踢出
    if (!force && !UIMgr.instance.getByName(ClubRouteItem.UIClubMain)) {
      return;
    }
    if (!ClubModule.data.clubFormMessage.popUpMessage) {
      return;
    }
    let callback = () => {
      if (UIMgr.instance.getByName(ClubRouteItem.UIClubMain)) {
        UIMgr.instance.showPage(CityRouteName.UIGameMap);
      }
      UIMgr.instance.showDialog(ClubRouteItem.UIClubList);
    };
    let clubName = ClubModule.data.clubFormMessage.popUpMessage?.clubName ?? "";
    let args: UIClubConfirmDialogArgs = {
      title: "提示",
      content: `您已被踢出'${clubName}'联盟`,
      callback: callback,
      isBtnCancel: false,
      isClose: false,
    };
    RouteManager.uiRouteCtrl.showRoute(UIClubConfirmDialog, { payload: args });
    ClubModule.api.clubLeaveShow();
  }
  // 更新玩家职位
  public updatePlayerClubInfo(clubMessage: ClubMessage) {
    if (clubMessage) {
      for (let i = 0; i < clubMessage.memberList.length; i++) {
        if (clubMessage.memberList[i].simpleMessage.userId == PlayerModule.data.playerId) {
          ClubModule.data.position = clubMessage.memberList[i].position;
          ClubModule.data.joinTime = clubMessage.memberList[i].joinTime;
        }
      }
    } else {
      ClubModule.data.position = 0;
    }
    MsgMgr.emit(ClubEvent.CLUB_POSITION_CHANGE);
  }
  public updateAllMemberPosition(data: ClubAdjustPositionMessage[]) {
    for (let i = 0; data && i < data.length; i++) {
      for (let j = 0; j < ClubModule.data.clubMessage?.memberList.length; j++) {
        if (data[i].userId == ClubModule.data.clubMessage.memberList[j].simpleMessage.userId) {
          ClubModule.data.clubMessage.memberList[j].position = data[i].position;
        }
      }
    }
    this.updatePlayerClubInfo(ClubModule.data.clubMessage);
  }
  public updateClubApplyState(data: ClubJoinResponse) {
    if (data) {
      ClubModule.data.clubFormMessage.applyMap = data.applyMap;
    }
    MsgMgr.emit(ClubEvent.CLUB_APPLY_STATE_CHANGE);
  }
  public updateClubTaskState(data: ClubRewardMessage) {
    if (data) {
      let member = ClubModule.data.clubMessage.memberList.find(
        (item) => item.simpleMessage.userId == PlayerModule.data.playerId
      );
      if (member) {
        member.activeVal = data.active.activeVal;
      }
      ClubModule.data.clubFormMessage.active = data.active;
      ClubModule.data.clubFormMessage.dailyTask = data.dailyTask;
      let clubMessage = ClubModule.data.clubFormMessage.clubMessage;
      clubMessage.level = data.clubLevel;
      clubMessage.exp = data.clubExp;
      ClubModule.data.clubMessage = clubMessage;
      MsgMgr.emit(ClubEvent.CLUB_ACTIVITE_CHANGE);
    }
  }

  //更新捐献信息
  public updateClubDonateState(data: ClubRewardMessage) {
    if (data) {
      for (let i = 0; i < ClubModule.data.clubMessage?.memberList?.length; i++) {
        if (ClubModule.data.clubMessage.memberList[i].simpleMessage.userId == PlayerModule.data.playerId) {
          ClubModule.data.clubMessage.memberList[i].todayContribute = data.todayContribute;
          ClubModule.data.clubMessage.memberList[i].totalContribute = data.totalContribute;
        }
      }
    }
  }
  public getMemberPosition(userId: number) {
    for (let i = 0; i < ClubModule.data.clubMessage?.memberList?.length; i++) {
      if (ClubModule.data.clubMessage.memberList[i].simpleMessage.userId == userId) {
        return ClubModule.data.clubMessage.memberList[i].position;
      }
    }
    return 0;
  }
}
