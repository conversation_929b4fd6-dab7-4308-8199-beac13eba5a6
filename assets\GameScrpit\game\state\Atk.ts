import { _decorator, isValid } from "cc";
import { FSMState } from "../fight/FSM/FSMState";
import { FSMBoard, STATE } from "../fight/section/StateSection";
import { AnimationSection } from "../fight/section/AnimationSection";
import { CallSkillDetail } from "../fight/FightDefine";
import FightManager from "../fight/manager/FightManager";
import SkillManager from "../fight/manager/SkillManager";
import { JsonMgr } from "../mgr/JsonMgr";
import HPSection from "../../lib/object/HpSection";
import { AudioMgr, AudioName } from "../../../platform/src/AudioHelper";
interface TimeHandle {
  time: number;
  handle: Function;
  isHandleOver: boolean;
  skillId: number;
}
const { ccclass, property } = _decorator;

@ccclass
export default class Atk extends FSMState {
  private timeHandle: Array<TimeHandle> = [];
  public async onEnter(board: FSMBoard) {
    //AudioMgr.instance.playEffect(AudioName.Effect.攻击1);
    let roundMovementInfo = board.roundMovementInfo;
    let detail: CallSkillDetail = {
      src: board.sub,
      target: roundMovementInfo.hurtRole,
      skillId: roundMovementInfo.skillId,
      actionType: roundMovementInfo.actionType,
      resolveBack: roundMovementInfo.resolveBack,
      movementInfo: roundMovementInfo.movementInfo,
    };
    if (FightManager.instance.fightOver == true) {
      return;
    }
    FightManager.instance.getSection(SkillManager).callSkill(detail);
    let skill_db = JsonMgr.instance.jsonList.c_skillShow[roundMovementInfo.skillId];
    board.sub.getSection(AnimationSection).playAction(2, false);
  }

  private playSound(board: FSMBoard) {}

  // private onSkillOver(board: FSMBoard) {
  //   board.sub.emitMsg("OnSwitchState", STATE.IDLE);
  //   board.actOverCallback();
  // }

  public update(board: FSMBoard, dt) {
    this.updateHandle(dt);
  }

  public onExit(board: any): void {}

  public updateHandle(dt) {}

  private getSkillObj(roleId: number) {}
}
