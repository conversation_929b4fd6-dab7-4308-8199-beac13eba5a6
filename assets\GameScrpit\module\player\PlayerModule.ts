import { GameData } from "../../game/GameData";
import data from "../../lib/data/data";
import Player<PERSON><PERSON> from "./PlayerApi";
import { PlayerData } from "./PlayerData";
import { PlayerRoute } from "./PlayerRoute";
import { PlayerService } from "./PlayerService";
import { PlayerSubscriber } from "./PlayerSubscriber";

export class PlayerModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): PlayerModule {
    if (!GameData.instance.PlayerModule) {
      GameData.instance.PlayerModule = new PlayerModule();
    }
    return GameData.instance.PlayerModule;
  }

  private _data = new PlayerData();
  private _api = new PlayerApi();
  private _service = new PlayerService();
  private _subscriber = new PlayerSubscriber();
  private _route = new PlayerRoute();

  public static get data() {
    return this.instance._data;
  }
  public static get api() {
    return this.instance._api;
  }

  public static get service() {
    return this.instance._service;
  }

  protected saveKey(): string {
    return this.constructor.name;
  }

  public init(data?: any, completedCallback?: Function) {
    if (this._subscriber) {
      this._subscriber.unRegister();
    }
    this._data = new PlayerData();
    this._api = new PlayerApi();
    this._service = new PlayerService();
    this._subscriber = new PlayerSubscriber();
    this._route = new PlayerRoute();
    this._subscriber.register();
    // 初始化模块
    this._data.clearItemMap();
    PlayerModule.api.getPlayerInfo((data) => {
      this._route.init();

      PlayerModule.service.init();
      PlayerModule.api.getPower();
      PlayerModule.api.getAllSkin();
      PlayerModule.api.getAllTitle();
      PlayerModule.api.getAllHeadFrame();
      PlayerModule.api.getGetAllHeadShow();
      PlayerModule.api.getAllBubble();

      PlayerModule.api.updateEquity(() => {
        completedCallback && completedCallback();
        this._ready = true;
      });
    });
  }
}
