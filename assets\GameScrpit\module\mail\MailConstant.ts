/** 常量放这这里 **/

import CmdMgr from "../../game/mgr/CmdMgr";
import { MainCmd } from "../../game/net/cmd/CmdData";

// 邮件
export enum MailSubCmd {
  // 客户端同步日常邮件
  syncDailyMail = CmdMgr.getMergeCmd(MainCmd.mailCmd, 1),
  // 客户端同步官方邮件
  syncSysMail = CmdMgr.getMergeCmd(MainCmd.mailCmd, 2),
  // 设置一封邮件已读
  readOne = CmdMgr.getMergeCmd(MainCmd.mailCmd, 3),
  // 设置全部已读 如果idList为空，说明没有可读/可领取奖励的邮件
  readAll = CmdMgr.getMergeCmd(MainCmd.mailCmd, 4),
  // 设置全部已读 如果idList为空，说明没有可读/可领取奖励的邮件
  deleteOne = CmdMgr.getMergeCmd(MainCmd.mailCmd, 5),
  // 设置全部已读 如果idList为空，说明没有可读/可领取奖励的邮件
  deleteReadAll = CmdMgr.getMergeCmd(MainCmd.mailCmd, 6),

  //  弃用
  getMailIdList = CmdMgr.getMergeCmd(MainCmd.mailCmd, 7),

  // 推送邮件
  pushSendMail = CmdMgr.getMergeCmd(MainCmd.mailCmd, 51),
}
