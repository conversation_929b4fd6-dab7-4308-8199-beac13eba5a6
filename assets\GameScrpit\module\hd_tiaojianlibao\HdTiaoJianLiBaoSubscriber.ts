import StorageMgr, { StorageKeyEnum } from "db://assets/platform/src/StorageHelper";
import MsgEnum from "../../game/event/MsgEnum";
import GameHttpApi from "../../game/httpNet/GameHttpApi";
import { <PERSON><PERSON>Hand<PERSON> } from "../../game/mgr/ApiHandler";
import { ActivityCmd } from "../../game/net/cmd/CmdData";
import { WindowsPackBuyResponse, WindowPackMessage } from "../../game/net/protocol/Activity";
import MsgMgr from "../../lib/event/MsgMgr";
import { UIMgr } from "../../lib/ui/UIMgr";
import { ArrayUtils } from "../../lib/utils/ArrayUtils";
import { HdTiaoJianLiBaoModule } from "./HdTiaoJianLiBaoModule";
import { HdTiaoJianLiBaoRouteItem } from "./HdTiaoJianLiBaoRoute";
import { ActivityModule } from "../activity/ActivityModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { TimeUtils } from "../../lib/utils/TimeUtils";
const log = Logger.getLoger(LOG_LEVEL.DEBUG);
const ACTIVITY_ID = 10601;

export class HdTiaoJianLiBaoSubscriber {
  private tiaoJianLiBaoBuyCallback(rs: WindowsPackBuyResponse) {
    //
    HdTiaoJianLiBaoModule.data.windowPackMessage = rs.windowPackMessage;
    MsgMgr.emit(MsgEnum.ON_ACTIVITY_TIAOJIANLIBAO_BUY, rs);
  }
  private tiaojianLiBaoChangeCallback(rs: WindowPackMessage) {
    HdTiaoJianLiBaoModule.data.windowPackMessage = rs;
  }
  public register() {
    //订阅服务器消息
    // 条件礼包购买成功推送
    ApiHandler.instance.subscribe(
      WindowsPackBuyResponse,
      ActivityCmd.tiaoJianLiBaoBuyNotice,
      this.tiaoJianLiBaoBuyCallback
    );
    MsgMgr.on(MsgEnum.ON_ACTIVITY_TANCHUANG, this.on_activity_tanChuang, this);
    ApiHandler.instance.subscribe(
      WindowPackMessage,
      ActivityCmd.tiaoJianLiBaoChangeNotice,
      this.tiaojianLiBaoChangeCallback
    );
  }
  public unRegister() {
    //取消订阅服务器消息
    MsgMgr.off(MsgEnum.ON_ACTIVITY_TANCHUANG, this.on_activity_tanChuang, this);
    ApiHandler.instance.unSubscribe(ActivityCmd.tiaoJianLiBaoBuyNotice, this.tiaoJianLiBaoBuyCallback);
    ApiHandler.instance.unSubscribe(ActivityCmd.tiaoJianLiBaoChangeNotice, this.tiaojianLiBaoChangeCallback);
  }
  private on_activity_tanChuang(itemId: any) {
    // if (!ActivityModule.service.checkActivityUnlock(ACTIVITY_ID)) {
    //   return;
    // }

    let callback = (res) => {
      // let res = JSON.parse(resStr);
      let dialogType = -1;
      // let storageValue = `${Date.now()}`; //${TimeUtils.formatTimestamp(new Date().getTime(), "YYYYMMDD")}
      let typeMap = new Map<number, number[]>(); //存储窗口类型对应的礼包充值id

      for (let i = 0; i < res.packList.length; i++) {
        //先将礼包按弹窗类型分类

        if (typeMap[res.packList[i].type]) {
          typeMap[res.packList[i].type].push(res.packList[i].id);
        } else {
          typeMap[res.packList[i].type] = [res.packList[i].id];
        }

        for (let j = 0; j < res.packList[i].itemIdList.length; j++) {
          if (itemId == res.packList[i].itemIdList[j]) {
            // let load_storageValue = StorageMgr.loadStr(
            //   `${StorageKeyEnum.TiaoJianLiBao_DAYMARK}_${res.packList[i].type}`
            // );
            // if (Number(storageValue) < Number(load_storageValue) + res.packList[i].timeCold) {
            //   //如果冷却时间没到就不再次触发
            //   return;
            // }
            dialogType = res.packList[i].type;
            break;
          }
        }
      }
      if (dialogType > 0) {
        log.log("触发了");
        HdTiaoJianLiBaoModule.api.windowPack(ACTIVITY_ID, (data: WindowPackMessage) => {
          // 准备数据
          let isAllCharge = ArrayUtils.isArraySubset(typeMap[dialogType], data.redeemList);
          log.log("触发了二", data);
          if (isAllCharge) {
            // 如果都充值了就不触发弹窗
            return;
          }
          let activity = ActivityModule.data.allActivityConfig[ACTIVITY_ID] as any;
          if (Object.keys(data.durationMap).length >= activity.popupMax) {
            // 如果已经触发了最大次数就不触发弹窗
            return;
          }
          if (TimeUtils.serverTime < data.coldMap[dialogType]) {
            // 如果服务器时间小于冷却时间，不触发
            return;
          }
          UIMgr.instance.showDialog(HdTiaoJianLiBaoRouteItem.UITiaoJianLiBao, { itemId: itemId });
          // StorageMgr.saveItem(`${StorageKeyEnum.TiaoJianLiBao_DAYMARK}_${dialogType}`, storageValue);
          MsgMgr.emit(MsgEnum.ON_ACTIVITY_TIAOJIAN_UPDATE, data);
          // WindowsPackBuyResponse
        });
      }
    };
    callback(ActivityModule.data.allActivityConfig[ACTIVITY_ID]);
  }
}
