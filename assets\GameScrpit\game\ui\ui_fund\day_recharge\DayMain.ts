import { _decorator, Component, Node } from "cc";
import { PlantUI } from "db://assets/GameScrpit/lib/plant/PlantUI";
import { AssetMgr } from "db://assets/platform/src/ResHelper";
const { ccclass, property } = _decorator;

@ccclass("DayMain")
export class DayMain extends Component {
  protected _assetMgr: AssetMgr = null;
  protected _plantUI: PlantUI = null;

  protected onLoad(): void {
    this._assetMgr = AssetMgr.create();
    this._plantUI = PlantUI.create();
  }

  onRemove() {
    this._assetMgr.release();
    this._plantUI.onRemove();
  }
}
