import { _decorator, find, isValid, Label, Node } from "cc";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { TopUpSignRequest, TopUpSignResponse } from "../../net/protocol/Activity";
import ToolExt from "../../common/ToolExt";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { JsonMgr } from "../../mgr/JsonMgr";
import { NodeTool } from "../../../lib/utils/NodeTool";
import { Sleep } from "../../GameDefine";
import { AfterModule } from "../../../module/after/AfterModule";
import { BadgeMgr, BadgeType } from "../../mgr/BadgeMgr";
import { dtTime } from "../../BoutStartUp";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { AfterAudioName, TopUpSignVO, TopUpVO } from "../../../module/after/AfterConfig";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "../../../lib/utils/FmUtils";
import { routeConfig } from "db://assets/platform/src/core/managers/RouteTableManager";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { ActivityModule } from "../../../module/activity/ActivityModule";
import { TopUpAdapter } from "./adapter_afterday/TopUpViewHolder";
import { AdapterView } from "../../../../platform/src/core/ui/adapter_view/AdapterView";
import { TopUpGiftAdapter } from "./adapter_afterday/TopUpGiftViewHolder";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;
const avId1: number = 10901;
const db_info = "db_info";
const TopUpRewardRequest_index = "TopUpRewardRequest_index";

enum Main_Type {
  type1 = 1,
  type2 = 2,
  type3 = 3,
}

@ccclass("UIAfterDay")
@routeConfig({
  bundle: BundleEnum.BUNDLE_HD_FUND,
  url: "prefab/ui/UIAfterDay",
  nextHop: [],
  exit: "dialog_close",
})
export class UIAfterDay extends BaseCtrl {
  public playShowAni: boolean = true;

  private _topUpVO: TopUpVO = null;
  private _cur_main_type: Main_Type = null;
  private _tickMgrIdList: number[] = [];
  private _topUpAdapter: TopUpAdapter = null;
  private _topUpGiftAdapter: TopUpGiftAdapter = null;

  private up_topUpMessage(args: any) {
    switch (this._cur_main_type) {
      case Main_Type.type1:
        /**更新累计充值金额的显示 */
        this.init_type1_main();
        /**刷新累计充值金额礼包的状态 */
        break;

      case Main_Type.type2:
        /**累天充值的刷新==== */
        this.set_lbl_day_fter_day();
        this.set_lbl_cd();
        this.init_award_list();
        /**累天充值的刷新==== */
        break;

      case Main_Type.type3:
        /**回馈礼包 */
        this.init_type3_main();
        break;
    }
  }

  private on_click_btn_type1() {
    AudioMgr.instance.playEffect(AfterAudioName.Effect.点击下方页签);
    if (this._cur_main_type == Main_Type.type1) return;
    this._cur_main_type = Main_Type.type1;
    this.initMain();
  }

  private on_click_btn_type2() {
    AudioMgr.instance.playEffect(AfterAudioName.Effect.点击下方页签);
    if (this._cur_main_type == Main_Type.type2) return;
    this._cur_main_type = Main_Type.type2;
    this.initMain();
  }

  private on_click_btn_type3() {
    AudioMgr.instance.playEffect(AfterAudioName.Effect.点击下方页签);
    if (this._cur_main_type == Main_Type.type3) return;
    this._cur_main_type = Main_Type.type3;
    this.initMain();
  }

  protected start(): void {
    super.start();
    this._cur_main_type = Main_Type.type1;
    this._topUpVO = ActivityModule.data.allActivityConfig[10901] as TopUpVO;
    this.initMain();

    MsgMgr.on(MsgEnum.ON_ACTIVITY_RECHARGE_UP, this.up_topUpMessage, this);

    BadgeMgr.instance.setBadgeId(this.getNode("btn_type1"), BadgeType.UITerritory.btn_lchk.btn_type1.id);
    BadgeMgr.instance.setBadgeId(this.getNode("btn_type2"), BadgeType.UITerritory.btn_lchk.btn_type2.id);
    BadgeMgr.instance.setBadgeId(this.getNode("btn_type3"), BadgeType.UITerritory.btn_lchk.btn_type3.id);
  }

  protected onDestroy(): void {
    MsgMgr.off(MsgEnum.ON_ACTIVITY_RECHARGE_UP, this.up_topUpMessage, this);
  }
  private initMain() {
    this.changeMainState();
    this.change_btn();
  }

  private changeMainState() {
    switch (this._cur_main_type) {
      case Main_Type.type1:
        this.init_type1_main();
        break;

      case Main_Type.type2:
        this.init_type2_main();
        break;

      case Main_Type.type3:
        this.init_type3_main();
        break;
    }
  }

  private change_btn() {
    this.getNode("lay_bottom_btn").children.forEach((val) => {
      val.getChildByName("no_pich").active = true;
      val.getChildByName("pich").active = false;
    });

    let node = null;
    switch (this._cur_main_type) {
      case Main_Type.type1:
        node = this.getNode("btn_type1");
        break;

      case Main_Type.type2:
        node = this.getNode("btn_type2");
        break;

      case Main_Type.type3:
        node = this.getNode("btn_type3");
        break;
    }

    node.getChildByName("pich").active = true;
    node.getChildByName("no_pich").active = false;
  }

  /**累计充值======================================================================== */
  private init_type1_main() {
    this.getNode("main_list").children.forEach((val) => {
      val.active = false;
    });
    this.getNode("main_type1").active = true;
    this.getNode("lbl_totalRecharge").getComponent(Label).string = AfterModule.data.totalRecharge + "贵族点";
    this.load_type1_list();
  }

  private async load_type1_list() {
    if (!this._topUpAdapter) {
      this._topUpAdapter = new TopUpAdapter(this.getNode("viewholder_type1"));
      this.getNode("list_top_up").getComponent(AdapterView).setAdapter(this._topUpAdapter);
    }
    this._topUpAdapter.setData(this._topUpVO.costRewardVOList);
  }

  /**累计充值======================================================================== */

  /**累天充值======================================================================== */
  private init_type2_main() {
    log.log("累天充值======", this._topUpVO.signVOList);
    this.getNode("main_list").children.forEach((val) => {
      val.active = false;
    });
    //this.getNode("award_content2").destroyAllChildren();
    this.getNode("main_type2").active = true;

    this.set_lbl_day_fter_day();
    this.set_lbl_cd();
    this.init_award_list();
  }

  private set_lbl_day_fter_day() {
    let signlist: Array<string> = Object.keys(AfterModule.data.signMap);
    let index = 0;
    for (let i = 0; i < signlist.length; i++) {
      let info = AfterModule.data.signMap[signlist[i]];
      if (info >= 0) {
        index++;
      }
    }

    this.getNode("lbl_day_fter_day").getComponent(Label).string = index + "天";
  }

  private set_lbl_cd() {
    FmUtils.setCd(this.getNode("lbl_time"), AfterModule.data.deadline);
  }

  private async init_award_list() {
    let signlist: Array<string> = Object.keys(AfterModule.data.signMap);
    let map = this.newSignMap();
    let index = 0;
    for (let i = 0; i < signlist.length; i++) {
      let info = AfterModule.data.signMap[signlist[i]];
      if (info >= 0) {
        index++;
      }
    }
    for (let i = 0; i < signlist.length; i++) {
      await Sleep(dtTime);
      if (isValid(this.node) == false) return;
      let info = map[signlist[i]];
      let node = this.getNode("award_content2").children[i];
      if (!node) {
        return;
      }

      let lbl_day: Node = find("day_hit/lbl_day", node);
      lbl_day.getComponent(Label).string = Math.min(index, info.day) + "/" + info.day + "天";

      let btn_type2_get_award = find("btn_type2_get_award", node);
      btn_type2_get_award[db_info] = info;

      this.update_type2_node_state(node, info);

      let rewardList = ToolExt.traAwardItemMapList(info.rewardList);

      let item_content = find("item_content", node);
      this.set_type2_item(item_content, rewardList);
    }
  }

  private async set_type2_item(item_content: Node, rewardList: Array<{ id; num }>) {
    for (let i = 0; i < rewardList.length; i++) {
      await Sleep(dtTime);
      if (isValid(this.node) == false) return;
      let node = item_content.children[i];
      FmUtils.setItemNode(node, rewardList[i].id, rewardList[i].num);
    }
  }

  /**节点刷新是否领取过的状态 */
  private update_type2_node_state(node: Node, info: TopUpSignVO) {
    if (AfterModule.data.signMap[info.id] == 1) {
      /**领取隐藏 */
      node.getChildByName("btn_type2_get_award").active = false;
      /**前往隐藏 */
      node.getChildByName("btn_type2_go_shop").active = false;
      /**提示标本隐藏 */
      node.getChildByName("day_hit").active = false;

      /**已经领取展示 */
      node.getChildByName("btn_yilingqu").active = true;
    } else if (AfterModule.data.signMap[info.id] == 0) {
      /**领取展示 */
      node.getChildByName("btn_type2_get_award").active = true;
      /**提示标本展示 */
      node.getChildByName("day_hit").active = true;
      /**前往隐藏 */
      node.getChildByName("btn_type2_go_shop").active = false;
      /**已经领取隐藏 */
      node.getChildByName("btn_yilingqu").active = false;
    } else if (AfterModule.data.signMap[info.id] == -1) {
      /**领取隐藏 */
      node.getChildByName("btn_type2_get_award").active = false;
      /**提示标本展示 */
      node.getChildByName("day_hit").active = true;
      /**前往展示 */
      node.getChildByName("btn_type2_go_shop").active = true;
      /**已经领取隐藏 */
      node.getChildByName("btn_yilingqu").active = false;
    }
  }

  private newSignMap() {
    let map = Object.create(null);
    for (let i = 0; i < this._topUpVO.signVOList.length; i++) {
      let info = this._topUpVO.signVOList[i];
      map[info.id] = info;
    }
    return map;
  }

  private on_click_btn_type2_get_award(event: Event) {
    AudioMgr.instance.playEffect(AfterAudioName.Effect.点击领取按钮);

    let info = event.target[db_info];
    let param: TopUpSignRequest = {
      activityId: avId1,
      signId: info.id,
    };

    AfterModule.api.topUpSign(param, (res: TopUpSignResponse) => {
      log.log("获取累充回馈信息=====", res);
      /** 一轮循环的签到状态 */
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: res.rewardList });

      this.set_lbl_day_fter_day();
      this.set_lbl_cd();
      this.init_award_list();
    });
  }

  private on_click_btn_type2_go_shop(event) {
    AudioMgr.instance.playEffect(AfterAudioName.Effect.点击前往按钮);
    function clickNode(nodePageName: string, nodeClickName: string) {
      if (nodeClickName) {
        let nodeParent = NodeTool.findByName(UIMgr.instance.uiRoot, nodePageName);
        if (!nodeParent) {
          return;
        }
        let nodeClick = NodeTool.findByName(nodeParent, nodeClickName);
        NodeTool.fakeClick(nodeClick);
      }
    }

    let cfgJump = JsonMgr.instance.getConfigJump(55);
    UIMgr.instance.showDialog(cfgJump.pageName, {}, null, () => {
      clickNode(cfgJump.pageName, cfgJump.nodeClick);
    });
  }
  /**累天充值======================================================================== */

  /**回馈礼包======================================================================== */
  private init_type3_main() {
    this.getNode("main_list").children.forEach((val) => {
      val.active = false;
    });
    if (!this._topUpGiftAdapter) {
      this._topUpGiftAdapter = new TopUpGiftAdapter(this.getNode("viewholder_type3"));
      this.getNode("list_gift").getComponent(AdapterView).setAdapter(this._topUpGiftAdapter);
    }
    this._topUpGiftAdapter.setData(this._topUpVO.redeemList[0]);
    this.getNode("main_type3").active = true;
  }

  private on_click_btn_close() {
    // 关闭窗口 effect_guanbiyinxiao
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    this.closeBack();
  }
}
