import { _decorator, CCInteger, Component, Label, Node, PrivateNode } from "cc";
import { MarketType } from "../../../module/chat/ChatConstant";
import { ChatModule } from "../../../module/chat/ChatModule";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { ChatRouteItem } from "../../../module/chat/ChatRoute";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { ChatAudioName } from "../../../module/chat/ChatConfig";
const { ccclass, property } = _decorator;

@ccclass("UITalk")
export class UITalk extends Component {
  @property(CCInteger)
  private chatType: number = MarketType.WORLD;
  @property(Label)
  private lblChatMsg: Label;
  start() {
    let chatMessage = ChatModule.data.getNewestMessage(this.chatType);
    if (chatMessage) {
      let chatContent = `${chatMessage.sender.nickname}:${chatMessage.content}`;
      if (chatContent.length > 20) {
        chatContent = chatContent.substring(0, 20) + "...";
      }
      this.lblChatMsg.string = chatContent;
    } else {
      this.lblChatMsg.string = "";
    }
    MsgMgr.on(MsgEnum.ON_CHAT_UNION_MESSAGE, this.onClubMessage, this);
    MsgMgr.on(MsgEnum.ON_CHAT_WORLD_MESSAGE, this.onWorldMessage, this);
  }

  update(deltaTime: number) {}

  private onClick() {
    AudioMgr.instance.playEffect(ChatAudioName.Effect.点击下方聊天栏);
    UIMgr.instance.showDialog(ChatRouteItem.UIChat, { chatType: this.chatType });
  }
  private onClubMessage() {
    if (this.chatType == MarketType.CLUB) {
      let chatMessage = ChatModule.data.getNewestMessage(this.chatType);
      if (chatMessage) {
        this.lblChatMsg.string = `${chatMessage.sender.nickname}:${chatMessage.content}`;
      } else {
        this.lblChatMsg.string = "";
      }
    }
  }
  private onWorldMessage() {
    if (this.chatType == MarketType.WORLD) {
      let chatMessage = ChatModule.data.getNewestMessage(this.chatType);
      if (chatMessage) {
        this.lblChatMsg.string = `${chatMessage.sender.nickname}:${chatMessage.content}`;
      } else {
        this.lblChatMsg.string = "";
      }
    }
  }
  protected onDestroy(): void {
    MsgMgr.off(MsgEnum.ON_CHAT_UNION_MESSAGE, this.onClubMessage, this);
    MsgMgr.off(MsgEnum.ON_CHAT_WORLD_MESSAGE, this.onWorldMessage, this);
  }
}
