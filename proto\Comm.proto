syntax = "proto3";
package sim;

// 
message BattleReplayMessage {
  // 是否胜利
  bool win = 1;
  // 录像回放
  string replay = 2;
}

// 
message CommIntegerListMessage {
  repeated int32 intList = 1;
}

// 
message CommIntegerMapMessage {
  map<int32,int32> intMap = 1;
}

// 
message CommLongListMessage {
  repeated int64 longList = 1;
}

// 
message CommLongMapMessage {
  map<int64,int64> map = 1;
}

// 
message ErrorMessage {
  // 错误码
  int32 code = 1;
  // 异常参数 如果有多个参数，以#分割，同异常处理机制
  string errorMgr = 2;
}

// 
message RewardMessage {
  // {30302, -1, 1036, 2,10601,-1,1002,5,3001,20}
  repeated int64 transformList = 1;
  // 获取道具列表 {30302, 1.6,10,10601,1}
  repeated double rewardList = 2;
}

// 
message SystemOpenMessage {
  repeated int64 newList = 1;
  repeated int64 totalList = 2;
}

