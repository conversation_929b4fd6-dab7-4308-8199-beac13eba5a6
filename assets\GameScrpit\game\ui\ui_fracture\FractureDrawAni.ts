import { _decorator, Component, instantiate, Node, tween, UIOpacity } from "cc";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import { FractureModule } from "../../../module/fracture/FractureModule";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
import FmUtils from "../../../lib/utils/FmUtils";
const { ccclass, property } = _decorator;
const log = Logger.getLoger(LOG_LEVEL.STOP);
@ccclass("FractureDrawAni")
export class FractureDrawAni extends Component {
  @property(Node)
  private nodeDraws: Node;

  private _drawCallback: Function = null;
  start() {}

  update(deltaTime: number) {}

  public init() {
    let floor = FractureModule.data.fractureData.floorId;
    let drawItemList = FractureModule.config.getFractureDrawConfig(floor);
    log.log("drawItemList", drawItemList);
    let i = 0;
    // let nodeDrawItems = this.getNode("node_draws");
    for (; i < drawItemList.length; i++) {
      let drawItem = drawItemList[i];
      let nodeDrawItem = this.nodeDraws.children[i];
      if (!nodeDrawItem) {
        nodeDrawItem = instantiate(this.nodeDraws.children[0]);
        this.nodeDraws.addChild(nodeDrawItem);
      }
      nodeDrawItem.getChildByName("node_light").getComponent(UIOpacity).opacity = 0;
      nodeDrawItem.active = true;
      FmUtils.setItemNode(nodeDrawItem, drawItem.rewardList[0], drawItem.rewardList[1]);
    }
    for (; i < this.nodeDraws.children.length; i++) {
      this.nodeDraws.children[i].active = false;
    }
    // this.playLottery(this.findIndexByItemId(1018)); // 假设第一个是中奖的
  }

  public startDraw(itemId: number, drawCallback: Function) {
    //
    this._drawCallback = drawCallback;
    this.playLottery(this.findIndexByItemId(itemId));
  }

  // 新增抽奖方法
  playLottery(winIndex: number) {
    const targetNode = this.nodeDraws.children[winIndex];
    const animationSequence: Node[] = [];

    // // 生成随机序列（相邻不重复）
    // let randomSequence: number[][] = [
    //   [0, 4, 8, 12],
    //   [1, 5, 9, 13],
    //   [2, 6, 10, 14],
    //   [3, 7, 11, 15],
    // ];
    // let prevIndex = -1;
    // let startIndex = winIndex % 4;
    // let max = startIndex + 16;
    // for (; startIndex < max; startIndex++) {
    //   let randomIndex = Math.floor(Math.random() * randomSequence[startIndex % 4].length);
    //   let nodeIndex = randomSequence[startIndex % 4][randomIndex];
    //   animationSequence.push(this.drawNodes[nodeIndex]);
    //   prevIndex = randomIndex;
    // }
    // animationSequence.push(targetNode);

    for (let i = 0; i < this.nodeDraws.children.length + winIndex + 1; i++) {
      animationSequence.push(this.nodeDraws.children[i % this.nodeDraws.children.length]);
    }
    log.log("animationSequence", animationSequence);

    // 执行动画
    for (let i = 0; i < animationSequence.length; i++) {
      const node = animationSequence[i];
      const isLast = i === animationSequence.length - 1;
      const duration = this.calculateDuration(i, animationSequence.length);
      const delay = this.calculateDelay(i, animationSequence.length);
      this.playNodeAnimation(node, duration, delay, isLast, i === 0);
    }
  }

  private calculateDuration(index: number, total: number) {
    const baseDuration = 1;
    // const slowStart = total - 3; // 最后3个开始减速
    return baseDuration;
    // if (index < slowStart) {
    //   // 前部保持快速（0.1s）
    // } else {
    //   // 后3个使用指数衰减公式减速（0.3s → 0.6s → 1.2s）
    //   const slowFactor = 0.1;
    //   const slowIndex = index - slowStart;
    //   return baseDuration + slowIndex * slowFactor;
    // }
  }
  private calculateDelay(index: number, total: number) {
    // 初始值4，初始差分4，差分递减1的算法
    const initialValue = 0.21;
    const initialDiff = 0.14;
    const decrement = 0.07;
    const quckStart = 3;
    let delay = 0;
    if (index < 3) {
      // 使用等差数列求和公式：S = n*a1 + n(n-1)d/2
      delay = initialValue + initialDiff * index - (index * (index - 1) * decrement) / 2;
    } else {
      delay = initialValue + initialDiff * 3 - (3 * (3 - 1) * decrement) / 2;
      delay += (index - 2) * decrement;
    }
    const slowStart = total - 4; // 最后3个开始减速

    if (index < slowStart) {
      // 前部保持快速（0.1s）
      return delay;
    } else {
      // 后3个使用指数衰减公式减速（0.3s → 0.6s → 1.2s）
      const slowFactor = 0.4;
      const slowIndex = index - slowStart;
      return delay + slowIndex * slowFactor;
    }
  }

  // 执行单个节点动画
  private playNodeAnimation(node: Node, duration: number, delay: number, isLast: boolean, isFirst: boolean) {
    // const item = node.getChildByName("item");
    const lightNode = node.getChildByName("node_light");
    log.log("playNodeAnimation", node.name, duration, delay, isLast);

    // 使用tween实现动画
    tween(lightNode.getComponent(UIOpacity))
      .delay(isFirst ? 0 : delay)
      .call(() => {
        AudioMgr.instance.playEffect(1777);
      })
      .set({ opacity: 255 })
      .call(() => {
        if (isLast) {
          this._drawCallback?.call(this);
        }
      })
      .to(duration, { opacity: isLast ? 255 : 0 }, { easing: "sineOut" })
      .start();

    // tween(item)
    //   .delay(delay)
    //   .to(duration, { scale: v3(1.2, 1.2, 1) })
    //   .to(duration, { scale: v3(1, 1, 1) })

    //   .start();
  }

  /** 新增根据itemId查找索引方法 */
  public findIndexByItemId(itemId: number): number {
    let floor = FractureModule.data.fractureData.floorId;
    let drawItemList = FractureModule.config.getFractureDrawConfig(floor);

    for (let i = 0; i < drawItemList.length; i++) {
      if (drawItemList[i].rewardList[0] === itemId) {
        return i;
      }
    }
    return -1;
  }
}
