{"skeleton": {"hash": "BYn8/Y3/vDjN6VSaQp1SoVHndII", "spine": "3.8.75", "x": -50.74, "y": 172.24, "width": 40.7, "height": 51.16, "images": "", "audio": ""}, "bones": [{"name": "jtroot", "scaleX": 1.22, "scaleY": 1.22}, {"name": "vv2", "parent": "jtroot", "x": -0.64, "y": -53.04}, {"name": "root", "parent": "vv2", "x": 0.93, "y": 43.29}, {"name": "nst", "parent": "root", "length": 27.64, "rotation": -169.17, "x": 11.74, "y": 24.59}, {"name": "nt", "parent": "nst", "length": 11.51, "rotation": 122.1, "x": 0.42, "y": -3.14}, {"name": "nj4", "parent": "nst", "length": 6.94, "rotation": 156.11, "x": 7.98, "y": 7.91}, {"name": "nj5", "parent": "nj4", "length": 3.82, "rotation": -28.82, "x": 6.54, "y": -0.46}, {"name": "nj1", "parent": "nst", "length": 6.58, "rotation": 123.57, "x": 10.81, "y": 4.91}, {"name": "nj2", "parent": "nj1", "length": 4.31, "rotation": -23.08, "x": 6.03, "y": -0.29}, {"name": "nj3", "parent": "nst", "length": 8.85, "rotation": 106.87, "x": 22.98, "y": -1.25}, {"name": "nj6", "parent": "nj3", "length": 4.37, "rotation": -18.66, "x": 8.37, "y": -0.05}, {"name": "nj7", "parent": "nst", "length": 8.79, "rotation": 56.89, "x": 19.43, "y": -1.17}, {"name": "nj8", "parent": "nj7", "length": 5.27, "rotation": -22.72, "x": 8.3, "y": 0.24}, {"name": "rst", "parent": "nst", "length": 11.67, "rotation": 57.99, "x": 9.57, "y": -12.64}, {"name": "rst2", "parent": "rst", "length": 5.13, "rotation": 14.59, "x": 10.69, "y": 0.38}, {"name": "rst3", "parent": "rst2", "length": 3.51, "rotation": -19.98, "x": 4.79, "y": 0.05}, {"name": "rt", "parent": "rst", "length": 8.34, "rotation": -145.93, "x": -3.48, "y": -1.62}], "slots": [{"name": "jtgaozi", "bone": "jtroot", "attachment": "jtgaozi"}, {"name": "jtjin", "bone": "jtroot"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "root"}, {"name": "nj4", "bone": "nj4"}, {"name": "nj3", "bone": "nj7"}, {"name": "nst", "bone": "nst"}, {"name": "nj2", "bone": "nj3"}, {"name": "nj1", "bone": "nj1"}, {"name": "nt", "bone": "nt"}, {"name": "rst", "bone": "rst"}, {"name": "rt", "bone": "rt"}], "skins": [{"name": "default", "attachments": {"rt": {"rt": {"type": "mesh", "uvs": [1, 0.24049, 1, 0.30615, 0.88015, 0.67624, 0.61041, 0.94628, 0.10425, 0.94478, 0.01458, 0.74163, 0.05643, 0.48269, 0.30339, 0.05492, 0.86224, 0.05494], "triangles": [8, 0, 1, 2, 8, 1, 4, 6, 7, 5, 6, 4, 3, 7, 8, 3, 8, 2, 4, 7, 3], "vertices": [5.34, -16.09, 4.36, -15.86, -0.36, -11.01, -2.55, -2.02, 0.94, 13.12, 4.59, 15.1, 8.18, 12.97, 12.88, 4.11, 9.06, -12.6], "hull": 9, "edges": [0, 16, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16], "width": 36, "height": 18}}, "jtjin": {"jtjin": {"x": -2.68, "y": -19.36, "scaleX": 0.86, "scaleY": 0.86, "width": 51, "height": 54}}, "nt": {"nt": {"type": "mesh", "uvs": [0.77788, 0.06696, 0.94607, 0.21213, 0.96503, 0.43272, 0.91701, 0.85269, 0.78758, 0.94074, 0.62418, 0.9399, 0.26111, 0.80765, 0.08032, 0.51117, 0.08263, 0.41893, 0.12813, 0.1614, 0.25639, 0.09631, 0.51133, 0.27216], "triangles": [11, 0, 1, 9, 10, 11, 11, 1, 2, 11, 8, 9, 6, 11, 2, 11, 7, 8, 6, 7, 11, 2, 5, 6, 3, 5, 2, 4, 5, 3], "vertices": [-4.2, 14.08, 1.13, 13.82, 5.94, 9.87, 13.92, 1.11, 13.93, -2.52, 11.64, -4.95, 3.85, -7.86, -4.77, -4.88, -6.64, -3.08, -11.31, 2.54, -10.86, 5.7, -3.69, 6.15], "hull": 12, "edges": [0, 22, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22], "width": 24, "height": 33}}, "nst": {"nst": {"type": "mesh", "uvs": [0.63366, 0.062, 0.82894, 0.06207, 0.89534, 0.1597, 0.97905, 0.28276, 0.97956, 0.51925, 0.97994, 0.69703, 0.83077, 0.84022, 0.6643, 1, 0.60553, 1, 0.45383, 0.92712, 0.29943, 0.85295, 0.13586, 0.77438, 0, 0.70911, 0, 0.66509, 0.08871, 0.49203, 0.15342, 0.30701, 0.21407, 0.13358, 0.30048, 0.1021, 0.41074, 0.06193], "triangles": [11, 12, 13, 10, 11, 14, 11, 13, 14, 2, 0, 1, 2, 4, 0, 4, 2, 3, 6, 4, 5, 17, 15, 16, 10, 15, 17, 10, 14, 15, 17, 18, 10, 18, 0, 9, 0, 4, 6, 0, 6, 9, 9, 10, 18, 8, 9, 6, 7, 8, 6], "vertices": [1, 3, 6.12, -8.65, 1, 1, 3, -1.4, -7.2, 1, 1, 3, -3.45, -4.1, 1, 1, 3, -6.05, -0.19, 1, 1, 3, -4.85, 6.15, 1, 1, 3, -3.96, 10.92, 1, 1, 3, 2.52, 13.66, 1, 1, 3, 9.75, 16.71, 1, 1, 3, 12.02, 16.28, 1, 1, 3, 17.49, 13.21, 1, 1, 3, 23.05, 10.08, 1, 1, 3, 28.95, 6.77, 1, 1, 3, 33.85, 4.02, 1, 1, 3, 33.62, 2.84, 1, 1, 3, 29.32, -1.14, 1, 1, 3, 25.88, -5.62, 1, 1, 3, 22.65, -9.82, 1, 1, 3, 19.16, -10.03, 1, 1, 3, 14.71, -10.29, 1], "hull": 19, "edges": [14, 16, 24, 26, 26, 28, 10, 12, 12, 14, 6, 8, 8, 10, 2, 4, 4, 6, 2, 0, 0, 36, 32, 34, 34, 36, 28, 30, 30, 32, 20, 22, 22, 24, 16, 18, 18, 20], "width": 46, "height": 32}}, "nj4": {"nj4": {"type": "mesh", "uvs": [0.58999, 0.16955, 0.7143, 0.22038, 0.81069, 0.33587, 0.87831, 0.41689, 0.94883, 0.50138, 0.94832, 0.64868, 0.94801, 0.73874, 0.94754, 0.87581, 0.81247, 0.87597, 0.67982, 0.87613, 0.56827, 0.87626, 0.43722, 0.76641, 0.32249, 0.72146, 0.18152, 0.66623, 0.04957, 0.56123, 0.05057, 0.41272, 0.05166, 0.25118, 0.05251, 0.12404, 0.17773, 0.12397, 0.31035, 0.12391, 0.47815, 0.12382], "triangles": [10, 11, 9, 5, 8, 9, 2, 3, 9, 7, 8, 6, 2, 9, 11, 5, 6, 8, 11, 1, 2, 3, 5, 9, 5, 3, 4, 11, 0, 1, 11, 12, 0, 13, 19, 12, 12, 20, 0, 12, 19, 20, 14, 15, 13, 13, 15, 19, 15, 18, 19, 16, 17, 18, 18, 15, 16], "vertices": [2, 5, 4.35, 3.93, 0.93036, 6, -4.03, 2.78, 0.06964, 2, 5, 6.44, 3.53, 0.63523, 6, -2.01, 3.44, 0.36477, 2, 5, 8.21, 2.19, 0.24185, 6, 0.19, 3.13, 0.75815, 2, 5, 9.45, 1.26, 0.06883, 6, 1.73, 2.91, 0.93117, 2, 5, 10.75, 0.28, 0.0111, 6, 3.33, 2.68, 0.9889, 1, 6, 4.54, 1.07, 1, 1, 6, 5.27, 0.09, 1, 1, 6, 6.4, -1.41, 1, 1, 6, 4.65, -2.73, 1, 2, 5, 7.17, -5.4, 0.0002, 6, 2.94, -4.03, 0.9998, 2, 5, 5.38, -5.66, 0.02333, 6, 1.5, -5.12, 0.97667, 2, 5, 3.07, -4.49, 0.29153, 6, -1.1, -5.2, 0.70847, 2, 5, 1.14, -4.15, 0.66473, 6, -2.95, -5.83, 0.33527, 2, 5, -1.23, -3.73, 0.91473, 6, -5.23, -6.61, 0.08527, 2, 5, -3.55, -2.62, 0.99041, 6, -7.8, -6.76, 0.00959, 2, 5, -3.82, -0.61, 0.9998, 6, -9, -5.13, 0.0002, 1, 5, -4.12, 1.57, 1, 1, 5, -4.36, 3.29, 1, 1, 5, -2.35, 3.58, 1, 1, 5, -0.23, 3.89, 1, 2, 5, 2.46, 4.28, 0.99976, 6, -5.85, 2.19, 0.00024], "hull": 21, "edges": [20, 22, 26, 28, 18, 20, 14, 16, 16, 18, 12, 14, 8, 10, 10, 12, 6, 8, 2, 4, 4, 6, 2, 0, 0, 40, 22, 24, 24, 26, 38, 40, 34, 36, 36, 38, 32, 34, 28, 30, 30, 32], "width": 19, "height": 16}}, "rst": {"rst": {"type": "mesh", "uvs": [0.2154, 0.0899, 0.39763, 0.04088, 0.54959, 0, 0.55962, 0, 0.68032, 0.07281, 0.83986, 0.16903, 0.95796, 0.24027, 0.95848, 0.34416, 0.85337, 0.34417, 0.74189, 0.34418, 0.65183, 0.40958, 0.62108, 0.5317, 0.58466, 0.67631, 0.55847, 0.78033, 0.46222, 0.86759, 0.39334, 0.93004, 0.31618, 1, 0.26027, 1, 0.19544, 0.90747, 0.12837, 0.81174, 0.0874, 0.75327, 0.08645, 0.53455, 0.08555, 0.32623, 0.08468, 0.12506], "triangles": [19, 20, 13, 14, 19, 13, 18, 19, 14, 15, 18, 14, 16, 17, 18, 15, 16, 18, 12, 21, 11, 20, 21, 12, 13, 20, 12, 22, 23, 0, 8, 5, 6, 8, 6, 7, 9, 4, 5, 9, 5, 8, 4, 2, 3, 10, 4, 9, 4, 10, 2, 11, 21, 22, 10, 1, 2, 10, 0, 1, 10, 11, 0, 0, 11, 22], "vertices": [1, 13, -0.82, -7.94, 1, 1, 13, -3.36, -5.09, 1, 1, 13, -5.47, -2.72, 1, 1, 13, -5.54, -2.53, 1, 1, 13, -4.55, 0.39, 1, 1, 13, -3.23, 4.26, 1, 1, 13, -2.25, 7.12, 1, 1, 13, 0.39, 8.15, 1, 1, 13, 1.13, 6.23, 1, 1, 13, 1.92, 4.19, 1, 2, 13, 4.22, 3.19, 0.98198, 14, -5.55, 4.35, 0.01802, 3, 13, 7.55, 3.83, 0.67193, 14, -2.18, 4.13, 0.32326, 15, -7.94, 1.46, 0.00481, 3, 13, 11.48, 4.59, 0.03706, 14, 1.82, 3.87, 0.75908, 15, -4.09, 2.58, 0.20386, 2, 14, 4.7, 3.69, 0.29485, 15, -1.33, 3.39, 0.70515, 1, 15, 1.65, 2.77, 1, 1, 15, 3.78, 2.32, 1, 1, 15, 6.16, 1.82, 1, 1, 15, 6.65, 0.84, 1, 3, 13, 20.12, -0.25, 6e-05, 14, 8.96, -2.98, 0.03801, 15, 4.96, -1.42, 0.96193, 3, 13, 18.16, -2.42, 0.02393, 14, 6.52, -4.59, 0.37361, 15, 3.21, -3.77, 0.60246, 3, 13, 16.96, -3.74, 0.07444, 14, 5.03, -5.57, 0.57354, 15, 2.14, -5.2, 0.35203, 3, 13, 11.4, -5.92, 0.66758, 14, -0.9, -6.27, 0.33038, 15, -3.18, -7.88, 0.00203, 2, 13, 6.11, -7.99, 0.99089, 14, -6.54, -6.94, 0.00911, 1, 13, 1, -9.98, 1], "hull": 24, "edges": [4, 6, 12, 14, 18, 20, 32, 34, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 0, 46, 0, 2, 2, 4, 6, 8, 8, 10, 10, 12, 14, 16, 16, 18], "width": 23, "height": 32}}, "nj1": {"nj1": {"type": "mesh", "uvs": [0.54556, 0.1399, 0.71095, 0.25055, 0.88573, 0.36749, 0.91488, 0.5023, 0.95376, 0.68215, 0.98948, 0.84734, 0.91363, 0.91246, 0.81167, 1, 0.71795, 1, 0.62052, 0.94247, 0.46629, 0.8514, 0.33809, 0.77569, 0.24827, 0.72266, 0.19338, 0.62455, 0.1177, 0.48929, 0.06494, 0.395, 0.01468, 0.30517, 0.08202, 0.18532, 0.14403, 0.07498, 0.38134, 0.03002], "triangles": [7, 8, 6, 8, 9, 6, 6, 9, 4, 5, 6, 4, 4, 9, 10, 4, 10, 3, 10, 11, 3, 11, 12, 13, 3, 1, 2, 3, 11, 1, 1, 11, 13, 1, 13, 14, 1, 14, 0, 19, 17, 18, 0, 17, 19, 14, 15, 0, 17, 0, 15, 15, 16, 17], "vertices": [1, 7, 1.09, 3.69, 1, 2, 7, 3.88, 4.11, 0.98861, 8, -3.7, 3.21, 0.01139, 2, 7, 6.83, 4.56, 0.77368, 8, -1.16, 4.78, 0.22632, 2, 7, 8.59, 3.4, 0.45625, 8, 0.91, 4.4, 0.54375, 2, 7, 10.93, 1.84, 0.07275, 8, 3.67, 3.89, 0.92725, 2, 7, 13.08, 0.42, 0.00142, 8, 6.21, 3.42, 0.99858, 1, 8, 6.77, 2.09, 1, 1, 8, 7.51, 0.31, 1, 1, 8, 7.05, -0.88, 1, 1, 8, 5.74, -1.8, 1, 2, 7, 8.13, -4.72, 0.01699, 8, 3.67, -3.25, 0.98301, 2, 7, 6.08, -5.16, 0.17035, 8, 1.96, -4.46, 0.82965, 2, 7, 4.64, -5.47, 0.33788, 8, 0.75, -5.3, 0.66212, 2, 7, 3.04, -4.95, 0.56881, 8, -0.92, -5.45, 0.43119, 2, 7, 0.84, -4.23, 0.86492, 8, -3.23, -5.66, 0.13508, 2, 7, -0.7, -3.73, 0.96189, 8, -4.84, -5.81, 0.03811, 2, 7, -2.16, -3.26, 0.99212, 8, -6.37, -5.94, 0.00788, 2, 7, -2.83, -1.32, 0.99995, 8, -7.75, -4.42, 5e-05, 1, 7, -3.45, 0.47, 1, 1, 7, -1.68, 3.27, 1], "hull": 20, "edges": [14, 16, 36, 38, 0, 38, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 10, 12, 12, 14], "width": 16, "height": 18}}, "nj2": {"nj2": {"type": "mesh", "uvs": [0.55318, 0.05037, 0.7148, 0.09268, 0.88337, 0.13682, 0.88329, 0.27261, 0.8832, 0.43351, 0.88314, 0.53714, 0.88308, 0.63259, 0.883, 0.76895, 0.88294, 0.87098, 0.71393, 0.99142, 0.55152, 0.94486, 0.43559, 0.82975, 0.32533, 0.72025, 0.19282, 0.58867, 0.05717, 0.45397, 0.05616, 0.34367, 0.05553, 0.27443, 0.21086, 0.15401, 0.39722, 0.00953], "triangles": [9, 10, 8, 8, 10, 7, 7, 10, 11, 7, 11, 6, 6, 11, 12, 6, 12, 5, 12, 13, 5, 17, 5, 13, 17, 4, 5, 4, 17, 0, 17, 18, 0, 17, 13, 15, 0, 3, 4, 13, 14, 15, 3, 0, 1, 15, 16, 17, 3, 1, 2], "vertices": [1, 9, -1.14, 3.53, 1, 1, 9, 0.65, 5.24, 1, 1, 9, 2.52, 7.02, 1, 2, 9, 4.77, 5.83, 0.98758, 10, -5.29, 4.42, 0.01242, 2, 9, 7.44, 4.43, 0.78387, 10, -2.31, 3.95, 0.21613, 2, 9, 9.16, 3.52, 0.40545, 10, -0.39, 3.64, 0.59455, 2, 9, 10.75, 2.69, 0.08679, 10, 1.38, 3.36, 0.91321, 1, 10, 3.9, 2.96, 1, 1, 10, 5.79, 2.66, 1, 1, 10, 7.64, -0.12, 1, 2, 9, 13.7, -4.29, 0.00345, 10, 6.4, -2.3, 0.99655, 2, 9, 11.01, -4.77, 0.10796, 10, 4.01, -3.62, 0.89204, 2, 9, 8.45, -5.23, 0.45694, 10, 1.73, -4.88, 0.54306, 2, 9, 5.37, -5.78, 0.86811, 10, -1.01, -6.39, 0.13189, 2, 9, 2.22, -6.35, 0.99011, 10, -3.81, -7.93, 0.00989, 2, 9, 0.38, -5.4, 0.99983, 10, -5.86, -7.62, 0.00017, 1, 9, -0.77, -4.81, 1, 1, 9, -1.73, -1.76, 1, 1, 9, -2.87, 1.89, 1], "hull": 19, "edges": [16, 18, 18, 20, 4, 6, 6, 8, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36, 0, 2, 2, 4, 12, 14, 14, 16, 8, 10, 10, 12], "width": 17, "height": 22}}, "nj3": {"nj3": {"type": "mesh", "uvs": [0.65949, 0.07289, 0.78411, 0.10727, 0.87289, 0.20823, 0.97212, 0.32108, 0.92638, 0.47426, 0.90221, 0.55518, 0.88364, 0.61738, 0.7965, 0.67713, 0.73917, 0.71643, 0.6502, 0.77743, 0.55763, 0.84091, 0.391, 0.95515, 0.16626, 0.95573, 0.0635, 0.87113, 0.09506, 0.71335, 0.10886, 0.64434, 0.12717, 0.55277, 0.14241, 0.47656, 0.15983, 0.38949, 0.1869, 0.25414, 0.34805, 0.12328, 0.47334, 0.02153], "triangles": [11, 12, 14, 10, 11, 14, 14, 12, 13, 16, 9, 10, 10, 14, 15, 17, 8, 9, 16, 10, 15, 9, 16, 17, 17, 18, 8, 8, 18, 7, 7, 18, 19, 4, 7, 19, 20, 4, 19, 2, 20, 0, 20, 21, 0, 6, 7, 5, 1, 2, 0, 4, 5, 7, 2, 4, 20, 4, 2, 3], "vertices": [1, 11, -2.44, -0.63, 1, 1, 11, -2.57, 1.38, 1, 2, 11, -1.33, 3.36, 0.99654, 12, -10.09, -0.84, 0.00346, 2, 11, 0.05, 5.57, 0.97297, 12, -9.67, 1.73, 0.02703, 2, 11, 2.97, 6.01, 0.87349, 12, -7.14, 3.27, 0.12651, 2, 11, 4.52, 6.24, 0.7622, 12, -5.81, 4.08, 0.2378, 2, 11, 5.7, 6.42, 0.67547, 12, -4.78, 4.7, 0.32453, 2, 11, 7.25, 5.61, 0.50646, 12, -3.04, 4.55, 0.49354, 2, 11, 8.26, 5.07, 0.34604, 12, -1.9, 4.45, 0.65396, 2, 11, 9.84, 4.24, 0.10837, 12, -0.12, 4.29, 0.89163, 2, 11, 11.48, 3.38, 0.00601, 12, 1.72, 4.13, 0.99399, 1, 12, 5.05, 3.83, 1, 1, 12, 7.49, 1.4, 1, 1, 12, 7.48, -0.83, 1, 2, 11, 11.96, -4.09, 0.0649, 12, 5.05, -2.58, 0.9351, 2, 11, 10.68, -4.39, 0.19904, 12, 3.98, -3.35, 0.80096, 2, 11, 8.98, -4.78, 0.4935, 12, 2.57, -4.37, 0.5065, 2, 11, 7.57, -5.11, 0.74384, 12, 1.4, -5.21, 0.25616, 2, 11, 5.96, -5.48, 0.92173, 12, 0.05, -6.18, 0.07827, 2, 11, 3.45, -6.06, 0.99746, 12, -2.04, -7.68, 0.00254, 1, 11, 0.25, -4.7, 1, 1, 11, -2.25, -3.64, 1], "hull": 22, "edges": [22, 24, 24, 26, 20, 22, 36, 38, 26, 28, 6, 8, 2, 4, 4, 6, 38, 40, 40, 42, 2, 0, 0, 42, 16, 18, 18, 20, 12, 14, 14, 16, 8, 10, 10, 12, 32, 34, 34, 36, 28, 30, 30, 32], "width": 18, "height": 22}}, "yingzi": {"nyingzi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [19.17, 3.52, -20.01, 2.05, -20.46, 13.98, 18.73, 15.44], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 46, "height": 14}}, "jtgaozi": {"jtgaozi": {"type": "mesh", "uvs": [1, 0.26533, 0.55395, 1, 0.39706, 1, 0, 0.34225, 0, 0.12794, 1, 0.15327], "triangles": [4, 5, 0, 3, 4, 0, 1, 2, 3, 0, 1, 3], "vertices": [-21.13, 178.45, -8.23, 142.92, -10.84, 141.18, -35.66, 164.18, -41.59, 173.1, -24.23, 183.12], "hull": 6, "edges": [0, 10, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10], "width": 20, "height": 50}}}}], "animations": {"fangniu_wa": {"slots": {"nj3": {"color": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 13.6667, "color": "ffffffff"}, {"time": 15, "color": "ffffff00"}], "attachment": [{"name": "nj3"}]}, "jtgaozi": {"attachment": [{"name": null}]}, "nj2": {"color": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 13.6667, "color": "ffffffff"}, {"time": 15, "color": "ffffff00"}], "attachment": [{"name": "nj2"}]}, "rt": {"color": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 13.6667, "color": "ffffffff"}, {"time": 15, "color": "ffffff00"}], "attachment": [{"name": "rt"}]}, "nj1": {"color": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 13.6667, "color": "ffffffff"}, {"time": 15, "color": "ffffff00"}], "attachment": [{"name": "nj1"}]}, "rst": {"color": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 13.6667, "color": "ffffffff"}, {"time": 15, "color": "ffffff00"}], "attachment": [{"name": "rst"}]}, "nt": {"color": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 13.6667, "color": "ffffffff"}, {"time": 15, "color": "ffffff00"}], "attachment": [{"name": "nt"}]}, "yingzi": {"color": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 13.6667, "color": "ffffffff"}, {"time": 15.0333, "color": "ffffff00", "curve": "stepped"}, {"time": 18.3333, "color": "ffffff00"}], "attachment": [{"name": "n<PERSON><PERSON>"}]}, "nst": {"color": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 13.6667, "color": "ffffffff"}, {"time": 15, "color": "ffffff00"}], "attachment": [{"name": "nst"}]}, "nj4": {"color": [{"color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 13.6667, "color": "ffffffff"}, {"time": 15, "color": "ffffff00"}], "attachment": [{"name": "nj4"}]}}, "bones": {"nst": {"rotate": [{"angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 1.2, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 4.8333, "angle": -8.4, "curve": "stepped"}, {"time": 7.3333, "angle": -8.4, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 13.3333, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 15, "angle": 1.2}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "y": -1.14, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 4.8333, "y": -4, "curve": "stepped"}, {"time": 7.3333, "y": -4, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 10, "y": -1.14, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.25, "c3": 0.75}, {"time": 13.3333, "y": -1.14, "curve": 0.25, "c3": 0.75}, {"time": 15}], "shear": [{"curve": 0.25, "c3": 0.75}, {"time": 1.0667, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "y": 1.87, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "x": -2.4, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": -1.8, "y": -1.5, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 9.4, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "y": 1.87, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.25, "c3": 0.75}, {"time": 12.7333, "y": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 14, "y": 1.87, "curve": 0.25, "c3": 0.75}, {"time": 15}]}, "rst": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "angle": 2.31, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "angle": -0.41, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.25, "c3": 0.75}, {"time": 13.3333, "angle": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 15}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -0.56, "y": 0.08, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": "stepped"}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 10, "x": -0.56, "y": 0.08, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.25, "c3": 0.75}, {"time": 13.3333, "x": -0.56, "y": 0.08, "curve": 0.25, "c3": 0.75}, {"time": 15}]}, "rst2": {"rotate": [{"angle": -5.46, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "angle": -7.2, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.6667, "angle": -1.74, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 2.1667, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "angle": -5.46, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 4.1, "angle": -7.2, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 5.8333, "angle": -1.74, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 6.6, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 8.3333, "angle": -5.46, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.8333, "angle": -7.2, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 10, "angle": -1.74, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 10.5, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 11.6667, "angle": -5.46, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 12.1667, "angle": -7.2, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 13.3333, "angle": -1.74, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 13.8333, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 15, "angle": -5.46}]}, "rst3": {"rotate": [{"angle": -10.15, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -27.6, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.3333, "angle": -10.15, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.3333, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": -27.6, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8.3333, "angle": -10.15, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 9, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": -27.6, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 11.6667, "angle": -10.15, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 12.3333, "curve": 0.25, "c3": 0.75}, {"time": 14, "angle": -27.6, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 15, "angle": -10.15}]}, "rt": {"rotate": [{"angle": 0.19, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6667, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -3.6, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.3333, "angle": 0.19, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 4.3333, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "angle": -9.59, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 8.3333, "angle": 0.19, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 9, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 10.6667, "angle": -3.6, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 11.6667, "angle": 0.19, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 12.3333, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 14, "angle": -3.6, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 15, "angle": 0.19}]}, "nt": {"rotate": [{"curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": 3, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.6667, "angle": 6, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 4.6, "angle": -13.2, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 5.1, "angle": -16.8, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 5.6, "angle": -13.2, "curve": 0.312, "c2": 0.27, "c3": 0.667, "c4": 0.67}, {"time": 6.1, "angle": -16.8, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 6.6, "angle": -13.2, "curve": 0.313, "c2": 0.27, "c3": 0.677, "c4": 0.7}, {"time": 7.0333, "angle": -21.36, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 7.3333, "angle": -18, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 8.3333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 9, "angle": 3, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 10, "angle": 6, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 12.3333, "angle": 3, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 13.3333, "angle": 6, "curve": 0.25, "c3": 0.75}, {"time": 15}], "translate": [{"x": -0.09, "y": -0.44, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 0.08, "y": 0.44, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -0.09, "y": -0.44, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": 0.11, "y": 0.59, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -0.09, "y": -0.44, "curve": 0.25, "c3": 0.75}, {"time": 4.6, "x": 0.19, "y": 1.91, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "x": 0.17, "y": 1.54, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "x": -0.09, "y": -0.44, "curve": 0.25, "c3": 0.75}, {"time": 9, "x": 0.08, "y": 0.44, "curve": 0.25, "c3": 0.75}, {"time": 10, "x": -0.09, "y": -0.44, "curve": 0.25, "c3": 0.75}, {"time": 10.8333, "x": 0.11, "y": 0.59, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "x": -0.09, "y": -0.44, "curve": 0.25, "c3": 0.75}, {"time": 12.3333, "x": 0.08, "y": 0.44, "curve": 0.25, "c3": 0.75}, {"time": 13.3333, "x": -0.09, "y": -0.44, "curve": 0.25, "c3": 0.75}, {"time": 14.1667, "x": 0.11, "y": 0.59, "curve": 0.25, "c3": 0.75}, {"time": 15, "x": -0.09, "y": -0.44}], "shear": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.2, "y": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 4.6, "y": -2.4, "curve": 0.25, "c3": 0.75}, {"time": 7.3333, "y": -1.84, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 10, "x": 1.2, "y": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.25, "c3": 0.75}, {"time": 13.3333, "x": 1.2, "y": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 15}]}, "nj1": {"rotate": [{"angle": -22.39, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 1.67, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -22.39, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.1, "angle": -2.16, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.8333, "angle": 22.58, "curve": "stepped"}, {"time": 7.3333, "angle": 22.58}, {"time": 8.3333, "angle": -22.39, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": 1.67, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "angle": -22.39, "curve": 0.25, "c3": 0.75}, {"time": 13.3333, "angle": 1.67, "curve": 0.25, "c3": 0.75}, {"time": 15, "angle": -22.39}]}, "nj2": {"rotate": [{"angle": -29.44, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "angle": -34.09, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": -2.99, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2, "angle": 1.67, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.3333, "angle": -29.44, "curve": "stepped"}, {"time": 8.3333, "angle": -29.44, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 8.6667, "angle": -34.09, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 10, "angle": -2.99, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 10.3333, "angle": 1.67, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 11.6667, "angle": -29.44, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 12, "angle": -34.09, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 13.3333, "angle": -2.99, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 13.6667, "angle": 1.67, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 15, "angle": -29.44}]}, "nj4": {"rotate": [{"angle": 1.67, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -22.39, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -11.53, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "angle": 20.8, "curve": "stepped"}, {"time": 7.3333, "angle": 20.8, "curve": 0.25, "c3": 0.75}, {"time": 8.3333, "angle": 1.67, "curve": 0.25, "c3": 0.75}, {"time": 10, "angle": -22.39, "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "angle": 1.67, "curve": 0.25, "c3": 0.75}, {"time": 13.3333, "angle": -22.39, "curve": 0.25, "c3": 0.75}, {"time": 15, "angle": 1.67}]}, "nj5": {"rotate": [{"angle": -2.99, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "angle": 1.67}, {"time": 2, "angle": -34.09, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.3333, "angle": -2.99, "curve": 0.343, "c2": 0.38, "c3": 0.677, "c4": 0.71}, {"time": 4.8333, "angle": -24.59, "curve": 0.367, "c2": 0.63, "c3": 0.704}, {"time": 8.3333, "angle": -2.99, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 8.6667, "angle": 1.67}, {"time": 10.3333, "angle": -34.09, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 11.6667, "angle": -2.99, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 12, "angle": 1.67}, {"time": 13.6667, "angle": -34.09, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 15, "angle": -2.99}]}, "nj3": {"rotate": [{"angle": -3.47, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "angle": 2.96, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -23.6, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "angle": -3.47, "curve": 0.341, "c2": 0.36, "c3": 0.676, "c4": 0.7}, {"time": 4.6, "angle": -16.67, "curve": "stepped"}, {"time": 7.3333, "angle": -16.67, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 8.3333, "angle": -3.47, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.8333, "angle": 2.96, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "angle": -23.6, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 11.6667, "angle": -3.47, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 12.1667, "angle": 2.96, "curve": 0.25, "c3": 0.75}, {"time": 13.8333, "angle": -23.6, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 15, "angle": -3.47}]}, "nj6": {"rotate": [{"angle": -6.65, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "angle": 2.96, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -36.71, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "angle": -6.65, "curve": "stepped"}, {"time": 8.3333, "angle": -6.65, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.8333, "angle": 2.96, "curve": 0.25, "c3": 0.75}, {"time": 10.5, "angle": -36.71, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 11.6667, "angle": -6.65, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 12.1667, "angle": 2.96, "curve": 0.25, "c3": 0.75}, {"time": 13.8333, "angle": -36.71, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 15, "angle": -6.65}]}, "nj7": {"rotate": [{"angle": 6.43, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5}, {"time": 2.1667, "angle": 26.57, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "angle": 6.43, "curve": 0.341, "c2": 0.36, "c3": 0.676, "c4": 0.7}, {"time": 4.6, "angle": 26.83, "curve": "stepped"}, {"time": 7.3333, "angle": 26.83, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 8.3333, "angle": 6.43, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.8333}, {"time": 10.5, "angle": 26.57, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 11.6667, "angle": 6.43, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 12.1667}, {"time": 13.8333, "angle": 26.57, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 15, "angle": 6.43}]}, "nj8": {"rotate": [{"angle": -3.5, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5, "angle": -13.1}, {"time": 2.1667, "angle": 26.57, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 3.3333, "angle": -3.5, "curve": "stepped"}, {"time": 8.3333, "angle": -3.5, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 8.8333, "angle": -13.1}, {"time": 10.5, "angle": 26.57, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 11.6667, "angle": -3.5, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 12.1667, "angle": -13.1}, {"time": 13.8333, "angle": 26.57, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 15, "angle": -3.5}]}, "root": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 13.3333, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 15}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": 88.05, "y": -7.63, "curve": "stepped"}, {"time": 7.5, "x": 88.05, "y": -7.63, "curve": 0.25, "c3": 0.75}, {"time": 15, "x": 231.28, "y": -20.55}]}}, "deform": {"default": {"nst": {"nst": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "offset": 22, "vertices": [0.84927, -3.34685, -0.47458, -2.69335, -0.6053, -3.12714, -0.1123, -0.73801], "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": "stepped"}, {"time": 8.3333, "curve": 0.25, "c3": 0.75}, {"time": 10, "offset": 22, "vertices": [0.84927, -3.34685, -0.47458, -2.69335, -0.6053, -3.12714, -0.1123, -0.73801], "curve": 0.25, "c3": 0.75}, {"time": 11.6667, "curve": 0.25, "c3": 0.75}, {"time": 13.3333, "offset": 22, "vertices": [0.84927, -3.34685, -0.47458, -2.69335, -0.6053, -3.12714, -0.1123, -0.73801], "curve": 0.25, "c3": 0.75}, {"time": 15}]}}}}, "shui_jin": {"slots": {"jtjin": {"attachment": [{"name": "jtjin"}]}, "jtgaozi": {"attachment": [{"name": null}]}}, "bones": {"jtroot": {"scale": [{"x": 1.098, "y": 1.098}]}}}}}