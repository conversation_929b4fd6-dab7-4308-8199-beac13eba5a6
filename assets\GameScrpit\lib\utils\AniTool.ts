import { v3 } from "cc";
import { Vec3 } from "cc";

/**
 * 动作工具类
 */
export default class AniTool {
  /**
   * 平滑移动
   * @param pos 当前位置
   * @param targetPos 目标位置
   * @param speed 移动速度
   * @param dt 时间间隔 秒
   * @return 下一帧的坐标
   */
  public static moveTo(pos: Vec3, targetPos: Vec3, speed: number, dt: number): Vec3 {
    // 距离
    let s = v3(targetPos.x - pos.x, targetPos.y - pos.y);

    // 倍率
    let r = (speed * dt) / s.length();

    // 最终坐标
    return v3(pos.x + s.x * r, pos.y + s.y * r);
  }
}
