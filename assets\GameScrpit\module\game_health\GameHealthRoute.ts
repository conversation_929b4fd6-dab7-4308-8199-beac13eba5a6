import { UIGameHealthPayLeftTips } from "../../game/ui/ui_game_health/UIGameHealthPayLeftTips";
import { UIGameHealthPayTips } from "../../game/ui/ui_game_health/UIGameHealthPayTips";
import { Recording, RecordingMap } from "../../lib/ui/recordingMap";

export enum GameHealthRouteItem {
  UIGameHealthPayLeftTips = "UIGameHealthPayLeftTips",
  UIGameHealthPayTips = "UIGameHealthPayTips",
}
export class GameHealthRoute {
  rotueTables: Recording[] = [
    {
      node: UIGameHealthPayLeftTips,
      uiName: GameHealthRouteItem.UIGameHealthPayLeftTips,
      keep: false,
      relevanceUIList: [],
    },
    {
      node: UIGameHealthPayTips,
      uiName: GameHealthRouteItem.UIGameHealthPayTips,
      keep: false,
      relevanceUIList: [],
    },
  ];

  public async init() {
    this.rotueTables.forEach((item) => {
      RecordingMap.instance.addRecording(item.uiName, item);
    });
  }
}
