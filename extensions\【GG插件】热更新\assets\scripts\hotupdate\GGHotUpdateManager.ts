import { game, native, path } from "cc";
import { GGHotUpdateInstance } from "./GGHotUpdateInstance";
import { GGHotUpdateConfig, GGHotUpdateInstanceEnum, GGHotUpdateInstanceOption, GGHotUpdateManagerConfig } from "./GGHotUpdateType";
import { ggLogger } from "./GGLogger";

/**
 * 热更新实例管理器
 *
 * <AUTHOR>
 * @created 2024-08-30 10:40:53
 */
class GGHotUpdateManager {
    /**
     * 热更新实例
     */
    private get instances(): Map<string, GGHotUpdateInstance> {
        if (this._instances == null) {
            this._instances = new Map();
        }
        return this._instances;
    }
    private _instances: Map<string, GGHotUpdateInstance> = null!;

    /**
     * 是否打印调试日志
     */
    get enableLog(): boolean {
        return this._enableLog;
    }
    private _enableLog: boolean = false;

    /**
     * 热更新文件的远程地址根目录
     *
     * e.g. http://192.168.0.1:8080/1.0.0
     */
    get remoteRootUrl(): string {
        return this._remoteRootUrl;
    }
    private _remoteRootUrl: string = "";

    /**
     * 热更新文件的本地存储根目录
     *
     * e.g. Android: ``/data/user/0/com.cocos.game/files/gg-hot-update``
     */
    get localRootDirPath(): string {
        return this._localRootDirPath;
    }
    private _localRootDirPath: string = "";

    /**
     * 当前版本，热更新包信息
     */
    get hotUpdateConfig(): GGHotUpdateConfig | null {
        return this._hotUpdateConfig;
    }
    private _hotUpdateConfig: GGHotUpdateConfig | null = null;

    /**
     * 初始化热更新管理器配置
     *
     * @param config 配置
     */
    init(config: GGHotUpdateManagerConfig) {
        this._enableLog = config.enableLog ?? false;
        this._remoteRootUrl = config.packageUrl;
        this._localRootDirPath = config.storageDirPath ?? path.join(native.fileUtils.getWritablePath(), "gg-hot-update");

        // 初始化日志输出
        ggLogger.enable = this._enableLog;

        // 初始化当前版本下，热更包bundle信息配置，按照一下顺序获取配置：
        // 1. 设备本地存储系统的 config.json
        // 2. 包体内置 config.json
        const bundleConfigPaths = [path.join(this.localRootDirPath, "gg.config.json"), "@assets/gg.config.json"];
        let bundleJsonText: string | null = null;
        ggLogger.debug(`初始化：当前热更包 Bundle 配置信息文件搜索路径如下：${JSON.stringify(bundleConfigPaths)}`);
        for (const bundleConfigPath of bundleConfigPaths) {
            ggLogger.debug(`初始化：尝试从路径 ${bundleConfigPath} 获取热更包Bundle配置信息：开始`);
            if (!native.fileUtils.isFileExist(bundleConfigPath)) {
                ggLogger.debug(`初始化：尝试从路径 ${bundleConfigPath} 获取热更包Bundle配置信息：失败，文件不存在`);
                continue;
            }

            bundleJsonText = native.fileUtils.getStringFromFile(bundleConfigPath);
            if (bundleJsonText) {
                try {
                    this._hotUpdateConfig = JSON.parse(bundleJsonText);
                    ggLogger.debug(`初始化：尝试从路径 ${bundleConfigPath} 获取热更包Bundle配置信息：成功`);
                } catch (error) {
                    ggLogger.error(`初始化：尝试从路径 ${bundleConfigPath} 获取热更包Bundle配置信息：失败，文件内容解析失败`);
                    ggLogger.error(error);
                }
            }
            if (this._hotUpdateConfig) {
                break;
            }
        }
        // 容错机制：如果没有读取到热更包配置，则生成一个默认配置
        if (!this._hotUpdateConfig) {
            ggLogger.warn(`初始化：没法解析到本地从文件中读取当前热更包 Bundle 配置信息，将初始化一个默认配置`);
            this._hotUpdateConfig = { bundles: {} };
            this._hotUpdateConfig.bundles[GGHotUpdateInstanceEnum.BuildIn] = { version: "0" };
        }
        ggLogger.debug(`初始化：当前热更包 Bundle 配置信息：${this._hotUpdateConfig ? JSON.stringify(this._hotUpdateConfig) : ""}`);
    }

    /**
     * 获取热更新实例
     *
     * @param bundleName 内置的热更新实例类型 或 子包Bundle名字
     * @param option 热更新实例配置
     */
    getInstance(bundleName: GGHotUpdateInstanceEnum | string, option?: GGHotUpdateInstanceOption): GGHotUpdateInstance {
        let instance = this.instances.get(bundleName);
        if (!instance) {
            instance = new GGHotUpdateInstance(
                bundleName,
                this._remoteRootUrl,
                this._localRootDirPath,
                option
                    ? option
                    : {
                          downloadMaxConcurrentTask: 24,
                          downloadProgressCallBackIntervalInMs: 16,
                          downloadSpeedCalculationIntervalInMs: 1000,
                      }
            );
            this.instances.set(bundleName, instance);
        }
        return instance;
    }

    /**
     * 重启游戏
     */
    restartGame() {
        // 销毁所有热更新实例
        this.instances.forEach((instance) => {
            instance.destroy();
        });
        // 重启游戏
        game.restart();
    }

    /**
     * 判断某个Bundle是否「为需要热更新的Bundle」
     */
    isHotUpdateBundle(bundleName: string): boolean {
        return this._hotUpdateConfig?.bundles[bundleName] != null;
    }
}

/**
 * 热更新实例管理器
 */
export const ggHotUpdateManager = new GGHotUpdateManager();
