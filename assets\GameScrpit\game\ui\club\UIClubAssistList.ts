import { _decorator } from "cc";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { AdapterView } from "../../../../platform/src/core/ui/adapter_view/AdapterView";
import { ClubAssistAdapter } from "./adapter/ClubAssistViewHolder";
import { ClubModule } from "../../../module/club/ClubModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import { routeConfig } from "db://assets/platform/src/core/managers/RouteTableManager";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
const { ccclass, property } = _decorator;
const log = Logger.getLoger(LOG_LEVEL.DEBUG);
@ccclass("UIClubAssistList")
@routeConfig({
  bundle: BundleEnum.BUNDLE_G_CLUB,
  url: "prefab/ui/UIClubAssistList",
  nextHop: [],
  exit: "",
})
export class UIClubAssistList extends BaseCtrl {
  public playShowAni: boolean = true;
  private _adapter: ClubAssistAdapter = null;
  start() {
    super.start();
    this._adapter = new ClubAssistAdapter(this.getNode("assist_viewholder"));
    this.getNode("node_list").getComponent(AdapterView).setAdapter(this._adapter);
    ClubModule.api.allAssistList((res: any) => {
      log.log("allAssistList", res);
      this._adapter.setData(res);
    });
  }

  onClickClose() {
    this.closeBack();
  }
  update(deltaTime: number) {}
}
