import { _decorator, find, instantiate, isValid, Label, Layers, Node, sp, Sprite, UITransform, v2, v3, Vec2 } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { HuntModule } from "../../../module/hunt/HuntModule";
import { JsonMgr } from "../../mgr/JsonMgr";
import Formate from "../../../lib/utils/Formate";
import ToolExt from "../../common/ToolExt";
import TipMgr from "../../../lib/tips/TipMgr";
import { PlayerModule } from "../../../module/player/PlayerModule";
import ResMgr from "../../../lib/common/ResMgr";
import FightManager from "../../fight/manager/FightManager";
import { FightData } from "../../fight/FightDefine";
import { FightRouteItem } from "../../../module/fight/src/FightModule";
import { UIMgr } from "../../../lib/ui/UIMgr";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { HuntRouteName } from "../../../module/hunt/HuntRoute";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { HuntAudioName } from "../../../module/hunt/HuntConfig";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { IConfigMonsterShow } from "../../JsonDefine";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;
const skipLabNum = 1;

const posMap = new Map([
  [1, v2(-200, -100)],
  [2, v2(200, -100)],
]);
@ccclass("UIHuntPrepareSpirit")
export class UIHuntPrepareSpirit extends UINode {
  protected _openAct: boolean = true; //打开动作
  private _args: FightData;
  private _maxRound: number;
  private _isCombat: boolean;
  private _round: number = 0;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_HUNT}?prefab/ui/UIHuntPrepareSpirit`;
  }

  protected onRegEvent(): void {
    MsgMgr.on(MsgEnum.ON_FIGHT_ROUND_UPDATE, this.setRoundShow, this);
  }
  protected onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_FIGHT_END, this.getAzstAward, this);
    MsgMgr.off(MsgEnum.ON_FIGHT_ROUND_UPDATE, this.setRoundShow, this);
  }

  protected onEvtShow(): void {
    this.initMain();
  }

  private initMain() {
    this.getNode("ui_layer1").active = true;
    this.getNode("btn_close").active = true;
    this.getNode("ui_layer2").active = false;
    this.loadPet(posMap.get(2));
    this.loadRole(posMap.get(1));
    this.setUiLayer1();
  }

  /**设置ui内容 */
  private setUiLayer1() {
    this.setSpiritIndexLab();
  }
  /**灵兽序列号 */
  private setSpiritIndexLab() {
    let progress = HuntModule.data.progress;
    let c_hunt = JsonMgr.instance.jsonList.c_hunt;
    let list = Object.keys(c_hunt);
    this.getNode("lab_spirit_index").getComponent(Label).string = progress + 1 + "/" + list.length;
  }

  /**创建角色 */
  private async loadRole(pos: Vec2) {
    if (HuntModule.data.chance <= 0) {
      let lose_ui = ToolExt.clone(this.getNode("lose_ui"), this);
      this.getNode("role_fightPoint").addChild(lose_ui);
      lose_ui.active = true;
      lose_ui.setPosition(pos.x, pos.y);
      return;
    }
    let spineRole = await ToolExt.loadUIRole(
      this.getNode("role_fightPoint"),
      PlayerModule.data.skin.skinId,
      -1,
      "renderScale8",
      this
    );

    let render = find("renderPoint/render", spineRole);
    render.getComponent(sp.Skeleton).timeScale = 1;
    spineRole.setPosition(pos.x, pos.y);
    render.setPosition(0, render.getPosition().y);

    this.setPlayerHpShow(spineRole);

    if (spineRole.getChildByName("spr_power_player")) {
      spineRole.getChildByName("spr_power_player").destroy();
    }

    let spr_power_player = ToolExt.clone(this.getNode("spr_power_player"), this);
    spineRole.addChild(spr_power_player);
    spr_power_player.active = true;
    let lab_power_player = find("lab_power_player", spr_power_player);
    lab_power_player.getComponent(Label).string = Formate.format(HuntModule.data.power);
  }
  /**设置玩家血条 */
  private setPlayerHpShow(spineRole: Node) {
    ResMgr.loadPrefab("resources?prefab/effect/hp", (prefab) => {
      if (isValid(this.node) == false) {
        return;
      }
      let hpNode = instantiate(prefab);
      hpNode.walk((child) => (child.layer = this.node.layer));
      this.getNode("role_fightPoint").addChild(hpNode);
      let skinDb = JsonMgr.instance.jsonList.c_leaderSkin[PlayerModule.data.skin.skinId];
      let posY = JsonMgr.instance.jsonList.c_bloodPlace[skinDb.placeId].place03;
      let render = spineRole.getChildByPath("renderPoint/render");
      let newPos = ToolExt.transferOfAxes(render, this.getNode("role_fightPoint"));
      let pos = v3(newPos.x, newPos.y + posY, 0);
      hpNode.setPosition(pos);

      let fillrange = HuntModule.data.remainHp / HuntModule.data.totalHp;
      let bar_node = hpNode.getChildByName("bar");
      bar_node.getComponent(Sprite).fillRange = fillrange;
      hpNode.getChildByName("lab_Name").active = false;
      hpNode.getChildByName("lbl_infinite").active = false;
    });
  }

  /**创建灵兽 */
  private async loadPet(pos: Vec2) {
    let c_hunt = JsonMgr.instance.jsonList.c_hunt;
    let progress = HuntModule.data.progress;
    let list = Object.keys(c_hunt);
    let info = c_hunt[list[progress]];
    let db1 = JsonMgr.instance.jsonList.c_monsterShow[info.monsterId];
    let spineRole = await ToolExt.loadUIRole(this.getNode("pet_fightPoint"), info.monsterId, -1, "renderScale7", this);
    spineRole.setPosition(v3(pos.x, pos.y, 1));
    let renderPoint = find("renderPoint", spineRole);
    let scale = v3(db1.renderScale7[0], db1.renderScale7[1], db1.renderScale7[2]);
    renderPoint.setScale(scale.x * -1, scale.y, 1);
    renderPoint.setSiblingIndex(1);
    this.setPetHpShow(spineRole, db1);
    this.setPetPower(spineRole);
  }

  /**展示灵兽的血条 */
  private setPetHpShow(spineRole: Node, info: IConfigMonsterShow) {
    ResMgr.loadPrefab("resources?prefab/effect/hp", (prefab) => {
      if (isValid(this.node) == false) {
        return;
      }
      let hpNode = instantiate(prefab);
      hpNode.walk((child) => (child.layer = this.node.layer));
      this.getNode("pet_fightPoint").addChild(hpNode);
      let posY = JsonMgr.instance.jsonList.c_bloodPlace[info.placeId].place03;
      let render = spineRole.getChildByPath("renderPoint/render");
      let newPos = ToolExt.transferOfAxes(render, this.getNode("pet_fightPoint"));
      let pos = v3(newPos.x, newPos.y + posY, 0);
      hpNode.setPosition(pos);

      let progress = HuntModule.data.progress;
      let c_hunt = JsonMgr.instance.jsonList.c_hunt;
      let list = Object.keys(c_hunt);
      let info_hunt = c_hunt[list[progress]];

      let attr = ToolExt.getDetailAttr(info_hunt.monsterPower);
      let fillrange = HuntModule.data.petRemainHp / attr[1];
      let bar_node = hpNode.getChildByName("bar");
      bar_node.getComponent(Sprite).fillRange = fillrange;
      hpNode.getChildByName("lab_Name").active = true;
      hpNode.getChildByName("lbl_infinite").active = false;

      hpNode.getChildByName("lab_Name").getComponent(Label).string = info.name;

      let icon_boss2 = ToolExt.clone(this.getNode("icon_boss2"));
      hpNode.getChildByName("lab_Name").addChild(icon_boss2);
      icon_boss2.active = true;
    });
  }

  /**展示灵兽战力 */
  private setPetPower(petNode: Node) {
    let spr_power_pet = ToolExt.clone(this.getNode("spr_power_pet"), this);
    petNode.addChild(spr_power_pet);
    spr_power_pet.active = true;
    let progress = HuntModule.data.progress;
    let c_hunt = JsonMgr.instance.jsonList.c_hunt;
    let list = Object.keys(c_hunt);
    let info = c_hunt[list[progress]];
    let lab_power_pet = find("lab_power_pet", spr_power_pet);
    lab_power_pet.getComponent(Label).string = Formate.format(ToolExt.levelBossPower(info.monsterPower));
  }

  private on_click_btn_petHunt() {
    AudioMgr.instance.playEffect(HuntAudioName.Effect.灵兽入侵战斗按钮);
    if (HuntModule.data.chance <= 0) {
      TipMgr.showTip("已战败");
      return;
    }

    // 初始化战斗界面状态
    this.getNode("ui_layer1").active = false;
    this.getNode("ui_layer2").active = true;
    this.getNode("skipLab").active = false;
    this.getNode("btn_fightSkip").active = false;
    TipsMgr.setEnableTouch(false, 3);

    // 发起战斗请求
    HuntModule.api.petHunt(this.handleFightResponse.bind(this));
  }

  /** 处理战斗响应数据 */
  private handleFightResponse(res: any) {
    // 解析战斗数据
    const fightData = JSON.parse(res.replay);
    log.log("战斗录像====", fightData);

    // 创建战斗参数
    const args: FightData = {
      fightData: fightData,
      win: res.win,
      identify: res.identify,
      resAddList: res.resAddList,
    };
    this._args = args;

    // 注册战斗结束监听
    MsgMgr.on(MsgEnum.ON_FIGHT_END, this.getAzstAward, this);

    this.initFightManager(args);
    TipsMgr.setEnableTouch(true);
    this._isCombat = true;
  }
  getAzstAward() {
    if (this._args.win == false) {
      UIMgr.instance.showDialog(
        HuntRouteName.UIHuntPrepareLose,
        {},
        () => {},
        () => {
          this.initMain();
          FightManager.instance.exit();
          MsgMgr.off(MsgEnum.ON_FIGHT_END, this.getAzstAward, this);
        }
      );
    } else {
      UIMgr.instance.showDialog(
        HuntRouteName.UIPrepareWin,
        { resAddList: this._args.resAddList },
        () => {},
        () => {
          this.initMain();
          FightManager.instance.exit();
          MsgMgr.off(MsgEnum.ON_FIGHT_END, this.getAzstAward, this);
        }
      );
    }
  }
  sssssssssssssssssssssssssssssssssss;
  /** 初始化战斗管理器 */
  private initFightManager(args: FightData) {
    this.getNode("role_fightPoint").destroyAllChildren();
    this.getNode("pet_fightPoint").destroyAllChildren();

    const posMap = new Map([
      [1, v2(-200, -100)],
      [2, v2(200, -100)],
    ]);

    log.log("灵兽入侵战数据内容=====", args);
    this._maxRound = args.fightData.f;

    FightManager.instance.start({
      main: this.node,
      parent: this.getNode("fightPoint"),
      fight: args.fightData,
      posMap: posMap,
      playId: args.fightData.a,
      contentSize: this.getNode("main").getComponent(UITransform),
    });
  }

  /**更新回合显示 */
  private async setRoundShow(roundNumber: number) {
    this._round = roundNumber;
    this["roundLab"].getComponent(Label).string = `第${this._round}/${this._maxRound}回合`;
    if (this._round == 0) {
      this.getNode("btn_fightSkip").active = false;
      this.getNode("skipLab").active = false;
      return;
    }
    if (this._round >= skipLabNum) {
      this.getNode("btn_fightSkip").active = true;
      this.getNode("skipLab").active = false;
    } else {
      this.getNode("btn_fightSkip").active = false;
      this.getNode("skipLab").active = true;
      this.getNode("skipLab").getComponent(Label).string = skipLabNum - this._round + "回合可后跳过战斗";
    }
  }
  /**时间倍数按钮的显示 */
  private setTimeScaleLab() {
    this["scaleTimeLab"].getComponent(Label).string = "x" + (PlayerModule.data.fightSpeed + 1);
  }

  private on_click_btn_fightSkip() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    if (this._round < skipLabNum) {
      TipMgr.showTip("第五回合后跳过战斗");
      return;
    }
    TipsMgr.setEnableTouch(false, 3);
    FightManager.instance.fightOver = true;
    MsgMgr.emit(MsgEnum.ON_FIGHT_SKIP);
  }
  private on_click_btn_timeScale() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    PlayerModule.service.changeFightSpeed();
    this.setTimeScaleLab();
  }
  private on_click_btn_close() {
    // 关闭窗口 effect_guanbiyinxiao
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  public tick(dt: any): void {
    if (this._isCombat == true) {
      FightManager.instance.tick(dt);
    }
  }

  protected onEvtClose(): void {
    FightManager.instance.exit();
  }
}
