import { _decorator, is<PERSON>alid } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { FarmEnemyResponse, FarmNeighborResponse } from "../../net/protocol/Farm";
import { FarmFarmAdapter } from "./adapter/FarmFarmAdapter";
import { ListView } from "../../common/ListView";
import { FarmInfo } from "./adapter/FarmFarmItemViewHolder";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { FarmModule } from "../../../module/farm/FarmModule";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
import { FarmAudioName } from "../../../module/farm/FarmConfig";
import { TimeUtils } from "../../../lib/utils/TimeUtils";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { LangMgr } from "../../mgr/LangMgr";
import FmUtils from "../../../lib/utils/FmUtils";
const { ccclass, property } = _decorator;

const log = Logger.getLoger(LOG_LEVEL.DEBUG);

@ccclass("UIFarmFind")
export class UIFarmFind extends UINode {
  protected _openAct: boolean = true;

  private _farmAdapter: FarmFarmAdapter;

  private _selectIdx: number = 1;

  private static _cd = 0;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_FARM}?prefab/ui/UIFarmFind`;
  }

  protected onRegEvent() {
    MsgMgr.on(MsgEnum.ON_FARM_FIND, this.refreshBack, this);
  }

  protected onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_FARM_FIND, this.refreshBack);
  }

  protected refreshBack() {
    if (isValid(this.node) == false) {
      return;
    }
    if (this._selectIdx == 2) {
      this.findCallFun();
    } else {
      this.findEnemy();
    }
  }

  protected onEvtShow(): void {
    this._farmAdapter = new FarmFarmAdapter(this.getNode("item"));
    this.getNode("list").getComponent(ListView).setAdapter(this._farmAdapter);
    this.findCallFun();
  }

  protected onEvtClose(): void {}

  private switchTab(tag: number) {
    this._selectIdx = tag;
    this.getNode("btn_find_enemy").getChildByName("node_check").active = tag == 1;
    this.getNode("btn_find_enemy").getChildByName("node_uncheck").active = tag != 1;

    this.getNode("btn_find").getChildByName("node_check").active = tag == 2;
    this.getNode("btn_find").getChildByName("node_uncheck").active = tag != 2;

    if (tag == 1) {
      FmUtils.setDialogTitle(this.getNode("DialogSub"), LangMgr.txMsgCode(473));
      this.getNode("btn_refresh").active = false;
      //this.getNode("list").getComponent(UITransform).height = 1060;
    } else {
      FmUtils.setDialogTitle(this.getNode("DialogSub"), LangMgr.txMsgCode(474));
      this.getNode("btn_refresh").active = true;
      //this.getNode("list").getComponent(UITransform).height = 960;
    }
  }

  private on_click_btn_find_enemy() {
    AudioMgr.instance.playEffect(FarmAudioName.Effect.探寻点击下方页签);
    this.findEnemy();
  }

  findEnemy() {
    this.switchTab(1);
    FmUtils.setDialogTitle(this.getNode("DialogSub"), LangMgr.txMsgCode(473));
    FarmModule.api.findEnemy((resp: FarmEnemyResponse) => {
      for (let idx in resp.farmList) {
        let farm: FarmInfo = resp.farmList[idx];
        farm.hatred = resp.hatredMap[farm.simpleMessage.userId];
      }
      this._farmAdapter.setDatas(resp.farmList);
    });
  }

  private setCd(ts: number) {
    FmUtils.setCd(this.getNode("lbl_cd"), ts, true);
  }

  private on_click_btn_find() {
    AudioMgr.instance.playEffect(FarmAudioName.Effect.探寻点击下方页签);
    this.findCallFun();
  }

  findCallFun() {
    this.switchTab(2);
    if (FarmModule.data.data1) {
      this._farmAdapter.setDatas(FarmModule.data.data1);
      this.setCd(UIFarmFind._cd);
      return;
    }

    // 冷却时间
    if (UIFarmFind._cd > TimeUtils.serverTime) {
      return;
    }

    this.onFindNeighbor();
  }

  private on_click_btn_refresh() {
    TipsMgr.setEnableTouch(false, 3);
    AudioMgr.instance.playEffect(FarmAudioName.Effect.探寻点击刷新);
    // 冷却时间
    if (UIFarmFind._cd > TimeUtils.serverTime) {
      TipsMgr.showTipX(187, [], LangMgr.txMsgCode(475));
      return;
    }
    this.onFindNeighbor();
  }

  private onFindNeighbor() {
    FarmModule.api.findNeighbor(true, (resp: FarmNeighborResponse) => {
      TipsMgr.setEnableTouch(true);
      FarmModule.data.data1 = resp.farmList;
      UIFarmFind._cd = resp.nextColdRefreshStamp + 1000;

      this.setCd(UIFarmFind._cd);
      this._farmAdapter.setDatas(FarmModule.data.data1);
    });
  }
}
