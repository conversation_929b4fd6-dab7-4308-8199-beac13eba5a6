{"skeleton": {"hash": "Z6WXwR6ABL5xFGMLzPAEavaNZL4=", "spine": "3.8.75", "x": -81.72, "y": -14.78, "width": 119, "height": 130.49, "images": "./images/", "audio": "D:/spine导出/洞天福地动画/猴子动画"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "x": -20.73, "y": -3.11}, {"name": "bone2", "parent": "bone", "length": 18.58, "rotation": 62.92, "x": -4.82, "y": 33.77}, {"name": "bone3", "parent": "bone2", "length": 11.99, "rotation": 18.06, "x": 18.58}, {"name": "bone4", "parent": "bone2", "length": 19.95, "rotation": -108.58, "x": 6.42, "y": 7.63}, {"name": "bone5", "parent": "bone4", "length": 22.22, "rotation": 72.42, "x": 19.37, "y": -0.35}, {"name": "bone6", "parent": "bone5", "length": 9.07, "rotation": -39.28, "x": 22.22}, {"name": "bone10", "parent": "bone2", "rotation": -36.16, "x": 13.45, "y": -31.33}, {"name": "bone7", "parent": "bone10", "length": 9.34, "rotation": -135.19, "x": 15.48, "y": 1.56}, {"name": "bone8", "parent": "bone7", "length": 10.83, "rotation": -16.7, "x": 9.34}, {"name": "bone9", "parent": "bone8", "length": 9.66, "rotation": -14.69, "x": 10.83}, {"name": "bone11", "parent": "bone", "length": 18.34, "rotation": -121.84, "x": -7.37, "y": 30.66}, {"name": "bone12", "parent": "bone11", "length": 14.76, "rotation": 158.71, "x": 15.55, "y": 2.32}, {"name": "bone13", "parent": "bone12", "length": 20.78, "rotation": -78.99, "x": 14.76}, {"name": "bone14", "parent": "bone13", "length": 16.83, "rotation": 89.89, "x": 20.8, "y": 0.23}, {"name": "bone15", "parent": "bone11", "length": 13.98, "rotation": -91.48, "x": 15.75, "y": -10.29}, {"name": "bone16", "parent": "bone15", "length": 16.01, "rotation": -54.38, "x": 13.98}, {"name": "bone17", "parent": "bone16", "length": 13.69, "rotation": 43.18, "x": 16.01}, {"name": "bone18", "parent": "bone17", "length": 16.21, "rotation": 93.13, "x": 13.69}, {"name": "bone19", "parent": "bone11", "x": -6.15, "y": 38.2}], "slots": [{"name": "sd", "bone": "bone", "color": "ffffff72", "attachment": "sd"}, {"name": "jio2", "bone": "bone19", "attachment": "jio2"}, {"name": "weiba", "bone": "bone15", "attachment": "weiba"}, {"name": "body", "bone": "bone2", "attachment": "body"}, {"name": "baibaodai", "bone": "bone10", "attachment": "baibaodai"}, {"name": "jiao", "bone": "bone12", "attachment": "jiao"}, {"name": "sjpi12", "bone": "bone10", "attachment": "sjpi11"}, {"name": "sjpi11", "bone": "bone10"}, {"name": "shou2", "bone": "bone", "attachment": "shou2"}, {"name": "tou", "bone": "bone3", "attachment": "tou"}, {"name": "<PERSON><PERSON>i", "bone": "bone", "attachment": "<PERSON><PERSON>i"}], "skins": [{"name": "default", "attachments": {"tou": {"tou": {"x": 31.98, "y": 2.67, "rotation": -80.98, "width": 88, "height": 73}}, "body": {"body": {"type": "mesh", "uvs": [0.042, 0.40745, 0.1212, 0.22412, 0.19512, 0.04323, 0.41952, 0, 0.70464, 0, 1, 0.09946, 1, 0.33901, 0.9264, 0.58834, 0.83136, 0.81568, 0.68616, 1, 0.38784, 1, 0.15816, 0.95012, 0, 0.68612, 0.57264, 0.19723, 0.4644, 0.4539, 0.37992, 0.72279], "triangles": [8, 9, 15, 11, 15, 10, 9, 10, 15, 11, 12, 15, 12, 0, 15, 8, 15, 14, 15, 0, 14, 13, 3, 4, 7, 8, 14, 7, 14, 6, 6, 14, 13, 0, 1, 14, 14, 1, 13, 13, 1, 2, 13, 2, 3, 6, 13, 5, 13, 4, 5], "vertices": [3, 2, -6.88, 20.14, 0.11501, 3, -17.96, 27.04, 0.06546, 11, 4.53, -18.97, 0.81953, 3, 2, 3.74, 21.12, 0.33379, 3, -7.56, 24.68, 0.23814, 11, -5.96, -20.83, 0.42806, 3, 2, 14.12, 22.28, 0.35278, 3, 2.67, 22.56, 0.46965, 11, -16.21, -22.85, 0.17757, 3, 2, 21.31, 13.35, 0.20076, 3, 6.73, 11.85, 0.75365, 11, -24.12, -14.55, 0.04559, 2, 2, 27.8, 0.66, 0.02919, 3, 8.97, -2.23, 0.97081, 2, 2, 29.74, -14.93, 0.61625, 3, 5.98, -17.66, 0.38375, 3, 2, 18.22, -20.82, 0.89778, 3, -6.8, -19.69, 0.09471, 11, -23.88, 19.77, 0.00751, 3, 2, 4.56, -23.68, 0.82465, 3, -20.67, -18.16, 0.00029, 11, -10.5, 23.75, 0.17506, 2, 2, -8.53, -25.03, 0.45947, 11, 2.44, 26.18, 0.54053, 2, 2, -20.7, -23.1, 0.21397, 11, 14.72, 25.27, 0.78603, 2, 2, -27.49, -9.82, 0.0395, 11, 22.59, 12.6, 0.9605, 2, 2, -30.32, 1.63, 1e-05, 11, 26.36, 1.42, 0.99999, 2, 3, -33.15, 26.76, 0.00202, 11, 18.43, -12.82, 0.99798, 3, 2, 15.31, 1.69, 0.85422, 3, -2.59, 2.62, 0.14357, 11, -19.11, -2.42, 0.00221, 3, 2, 0.51, 0.2, 0.99926, 3, -17.12, 5.79, 0.00047, 11, -4.48, 0.29, 0.00027, 2, 2, -14.34, -2.65, 0.02899, 11, 10.09, 4.36, 0.97101], "hull": 13, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 0, 24, 8, 26, 26, 28, 28, 30], "width": 50, "height": 54}}, "baibaodai": {"baibaodai": {"x": 3.8, "y": 1.27, "rotation": -26.75, "width": 52, "height": 47}}, "shenzi": {"shenzi": {"type": "mesh", "uvs": [0.52056, 0.36234, 0.34414, 0.3, 0.38824, 0.12484, 0.63253, 0.02094, 0.87681, 0, 1, 0.19015, 1, 0.38015, 0.9311, 0.53453, 0.75467, 0.53156, 0.69021, 0.73937, 0.57824, 0.91453, 0.35771, 1, 0.16771, 0.86703, 0.07949, 0.775, 0, 0.66218, 0.06931, 0.5464, 0.32378, 0.51375, 0.44253, 0.43062, 0.72413, 0.2139, 0.62235, 0.41578, 0.44253, 0.60578, 0.27628, 0.75718], "triangles": [21, 15, 16, 21, 16, 20, 13, 14, 15, 21, 13, 15, 12, 13, 21, 21, 20, 10, 11, 21, 10, 12, 21, 11, 19, 0, 18, 8, 19, 6, 20, 16, 17, 19, 20, 17, 17, 0, 19, 19, 8, 20, 9, 20, 8, 10, 20, 9, 18, 3, 4, 18, 4, 5, 2, 3, 18, 0, 1, 2, 18, 0, 2, 18, 5, 6, 19, 18, 6, 7, 8, 6], "vertices": [2, 8, 10.3, -3.66, 0.28465, 9, 1.98, -3.22, 0.71535, 2, 8, 9.97, -8.97, 0.74094, 9, 3.19, -8.41, 0.25906, 2, 8, 4.26, -9.57, 0.88204, 9, -2.11, -10.63, 0.11796, 2, 8, -1.05, -4.14, 0.99808, 9, -8.76, -6.95, 0.00192, 1, 8, -3.85, 2.14, 1, 2, 8, 0.83, 7.34, 0.99407, 9, -10.25, 4.58, 0.00593, 2, 8, 6.6, 9.26, 0.82771, 9, -5.28, 8.08, 0.17229, 2, 8, 11.9, 8.99, 0.57304, 9, -0.13, 9.35, 0.42696, 3, 8, 13.37, 4.28, 0.1677, 9, 2.63, 5.25, 0.83208, 10, -9.26, 3, 0.00022, 2, 9, 9.11, 7.6, 0.72938, 10, -3.59, 6.92, 0.27062, 2, 9, 15.5, 8.27, 0.18763, 10, 2.42, 9.18, 0.81237, 2, 9, 21.29, 4.79, 0.00361, 10, 8.9, 7.29, 0.99639, 1, 10, 10.22, 0.6, 1, 1, 10, 10.21, -3.24, 1, 1, 10, 9.58, -7.44, 1, 2, 9, 14.07, -10.17, 0.0077, 10, 5.71, -9.01, 0.9923, 2, 9, 9.11, -4.94, 0.52857, 10, -0.41, -5.22, 0.47143, 3, 8, 13.07, -5.04, 0.00085, 9, 5.02, -3.75, 0.97885, 10, -4.67, -5.1, 0.0203, 1, 8, 3.99, 0.25, 1, 1, 9, 1.74, 0.09, 1, 2, 9, 9.61, -0.53, 0.93575, 10, -1.05, -0.82, 0.06425, 1, 10, 5.63, -0.12, 1], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34], "width": 28, "height": 32}}, "sd": {"sd": {"x": 2.59, "y": 4.83, "width": 73, "height": 33}}, "shou2": {"shou2": {"type": "mesh", "uvs": [0.03184, 0.07628, 0.10859, 0, 0.2438, 0, 0.34979, 0.14328, 0.42531, 0.29194, 0.47039, 0.42803, 0.56868, 0.33954, 0.67625, 0.26341, 0.7848, 0.21827, 0.92083, 0.22633, 1, 0.42789, 1, 0.60123, 1, 0.7907, 0.86923, 0.62139, 0.78714, 0.60527, 0.66987, 0.75039, 0.53852, 0.93986, 0.48458, 1, 0.33682, 1, 0.1914, 0.8673, 0.05771, 0.6617, 0, 0.4803, 0, 0.21827, 0.11869, 0.2223, 0.20547, 0.42789, 0.28287, 0.62139, 0.39545, 0.74233, 0.54087, 0.6093, 0.66518, 0.48836, 0.7848, 0.43192, 0.92083, 0.46014], "triangles": [29, 8, 9, 30, 29, 9, 7, 8, 29, 10, 30, 9, 30, 10, 11, 13, 14, 29, 28, 29, 14, 30, 13, 29, 13, 30, 11, 13, 11, 12, 28, 6, 7, 28, 7, 29, 27, 5, 6, 27, 6, 28, 26, 25, 5, 26, 5, 27, 15, 28, 14, 27, 28, 15, 16, 26, 27, 16, 27, 15, 26, 19, 25, 17, 18, 26, 18, 19, 26, 23, 1, 2, 0, 1, 23, 22, 0, 23, 3, 24, 23, 3, 23, 2, 24, 3, 4, 21, 22, 23, 21, 23, 24, 25, 24, 4, 25, 4, 5, 20, 21, 24, 20, 24, 25, 19, 20, 25, 16, 17, 26], "vertices": [1, 4, -9.17, -0.2, 1, 1, 4, -7.96, 4.53, 1, 1, 4, -2.77, 9.85, 1, 2, 4, 4.59, 10.81, 0.97468, 5, 6.18, 17.47, 0.02532, 2, 4, 10.89, 10.46, 0.79458, 5, 7.75, 11.35, 0.20542, 2, 4, 15.74, 9.19, 0.27622, 5, 8, 6.34, 0.72378, 2, 4, 17.49, 15.04, 0.00207, 5, 14.1, 6.44, 0.99793, 2, 5, 20.48, 5.95, 0.92926, 6, -5.11, 3.51, 0.07074, 2, 5, 26.46, 4.55, 0.18664, 6, 0.4, 6.21, 0.81336, 1, 6, 7.76, 7.58, 1, 1, 6, 13.41, 2.23, 1, 1, 6, 14.61, -3.18, 1, 1, 6, 15.93, -9.1, 1, 2, 5, 24.8, -9.06, 0.02996, 6, 7.73, -5.37, 0.97004, 3, 4, 31.97, 17.69, 0.00188, 5, 21.01, -6.56, 0.42685, 6, 3.21, -5.85, 0.57126, 3, 4, 30.79, 9.83, 0.12942, 5, 13.16, -7.81, 0.85773, 6, -2.08, -11.78, 0.01285, 2, 4, 30.07, 0.42, 0.81317, 5, 3.98, -9.97, 0.18683, 2, 4, 29.38, -3.04, 0.86819, 5, 0.46, -10.35, 0.13181, 2, 4, 23.7, -8.86, 0.16874, 5, -6.8, -6.69, 0.83126, 2, 4, 15.07, -11.61, 0.21321, 5, -12.03, 0.7, 0.78679, 2, 4, 5.23, -12.27, 0.79985, 5, -15.63, 9.89, 0.20015, 2, 4, -1.14, -10.48, 0.94597, 5, -15.85, 16.5, 0.05403, 2, 4, -7.14, -4.62, 0.99838, 5, -12.08, 23.98, 0.00162, 2, 4, -2.49, -0.05, 0.99986, 5, -6.31, 20.93, 0.00014, 2, 4, 5.55, -1.23, 0.99047, 5, -5.01, 12.91, 0.00953, 2, 4, 12.96, -2.51, 0.83452, 5, -3.99, 5.46, 0.16548, 2, 4, 20.05, -0.79, 0.37446, 5, -0.21, -0.78, 0.62554, 2, 4, 22.6, 7.91, 0.01462, 5, 8.85, -0.58, 0.98538, 3, 4, 24.61, 15.5, 0.00027, 5, 16.7, -0.2, 0.99847, 6, -4.15, -3.65, 0.00126, 2, 5, 23.39, -1.55, 0.05345, 6, 1.88, -0.46, 0.94655, 1, 6, 9.38, 0.28, 1], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44, 0, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60], "width": 55, "height": 32}}, "weiba": {"weiba": {"type": "mesh", "uvs": [1, 0.70805, 1, 0.84467, 1, 1, 0.7647, 1, 0.5327, 0.89365, 0.4515, 0.70805, 0.3935, 0.47089, 0.4138, 0.32396, 0.3384, 0.34716, 0.292, 0.51214, 0.176, 0.66423, 0.08176, 0.64352, 0, 0.62556, 0, 0.40387, 0.0339, 0.22085, 0.1818, 0.02494, 0.408, 0, 0.6603, 0.03267, 0.7995, 0.28014, 0.7937, 0.48636, 0.8285, 0.67969, 0.93, 0.7132, 0.7995, 0.83178, 0.5994, 0.67454, 0.5762, 0.46831, 0.5878, 0.28787, 0.4631, 0.15898, 0.2398, 0.16156, 0.147, 0.32396, 0.118, 0.47605], "triangles": [6, 7, 24, 24, 25, 19, 23, 24, 19, 23, 19, 20, 5, 6, 24, 5, 24, 23, 4, 5, 23, 27, 15, 16, 14, 15, 27, 28, 14, 27, 8, 28, 27, 7, 8, 27, 13, 14, 28, 29, 13, 28, 9, 28, 8, 29, 28, 9, 12, 13, 29, 11, 12, 29, 10, 11, 29, 9, 10, 29, 26, 16, 17, 27, 16, 26, 25, 26, 17, 18, 25, 17, 7, 27, 26, 7, 26, 25, 24, 7, 25, 19, 25, 18, 22, 23, 20, 22, 20, 21, 21, 0, 1, 22, 21, 1, 4, 23, 22, 3, 4, 22, 22, 1, 2, 3, 22, 2], "vertices": [1, 15, 1.1, -7.94, 1, 1, 15, -2.27, -2.81, 1, 1, 15, -6.11, 3.03, 1, 1, 15, 1.75, 8.2, 1, 2, 15, 12.13, 9.3, 0.97886, 16, -8.64, 3.92, 0.02114, 3, 15, 19.44, 4.11, 0.25527, 17, -7.12, 16.05, 0.0003, 16, -0.16, 6.83, 0.74443, 3, 17, 2.02, 10.07, 0.21477, 18, 10.69, 11.11, 0.00152, 16, 10.59, 8.72, 0.78371, 3, 17, 6.08, 4.79, 0.6558, 18, 5.19, 7.34, 0.2267, 16, 17.17, 7.65, 0.1175, 3, 17, 7.49, 7.64, 0.21225, 18, 7.97, 5.77, 0.77999, 16, 16.25, 10.7, 0.00776, 2, 17, 3.61, 14.24, 0.00172, 18, 14.77, 9.29, 0.99828, 1, 18, 22.97, 10.33, 1, 1, 18, 24.76, 6.89, 1, 1, 18, 26.32, 3.9, 1, 1, 18, 18.84, -2.7, 1, 1, 18, 11.76, -7.12, 1, 2, 17, 22.13, 1.7, 0.0073, 18, 1.24, -8.52, 0.9927, 2, 17, 16.46, -5.45, 0.77094, 18, -5.59, -2.47, 0.22906, 1, 17, 8.24, -11.47, 1, 2, 17, -3.54, -7.44, 0.52845, 16, 18.52, -7.85, 0.47155, 3, 15, 13.48, -11.75, 0.04809, 17, -9.88, -0.66, 0.00118, 16, 9.26, -7.25, 0.95072, 2, 15, 7.54, -5.24, 0.80633, 16, 0.51, -8.29, 0.19367, 2, 15, 3.32, -6.21, 0.99055, 16, -1.16, -12.28, 0.00945, 1, 15, 4.75, 1.11, 1, 1, 16, 1.11, 0.86, 1, 2, 17, -3.11, 4.86, 0.02717, 16, 10.42, 1.42, 0.97283, 1, 17, 2.25, -1.25, 1, 1, 17, 9.88, -1.89, 1, 1, 18, 4.31, -2.71, 1, 1, 18, 12.25, -0.66, 1, 1, 18, 18.15, 2.99, 1], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 24, 26, 20, 22, 22, 24, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 0, 42, 2, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58], "width": 40, "height": 45}}, "jio2": {"jio2": {"x": -0.08, "y": -0.99, "rotation": 121.84, "width": 15, "height": 27}}, "jiao": {"jiao": {"type": "mesh", "uvs": [0, 0.3944, 0.10428, 0.1744, 0.27428, 0.00154, 0.44828, 0, 0.56828, 0.13983, 0.67828, 0.37869, 0.75228, 0.16497, 0.85228, 0.0424, 0.97228, 0.08954, 1, 0.24669, 1, 0.52954, 0.90028, 0.69926, 0.78628, 0.87526, 0.66428, 1, 0.58628, 1, 0.48028, 0.8564, 0.42628, 0.75269, 0.33028, 0.86268, 0.16828, 0.8344, 0.01228, 0.71811, 0, 0.55154, 0.12828, 0.52326, 0.31028, 0.31897, 0.42228, 0.22783, 0.50028, 0.4824, 0.60228, 0.67097, 0.68828, 0.73697, 0.78828, 0.52326, 0.89828, 0.31269], "triangles": [12, 13, 26, 12, 26, 11, 26, 27, 11, 26, 25, 27, 11, 27, 10, 25, 5, 27, 27, 28, 10, 28, 9, 10, 27, 5, 28, 5, 6, 28, 6, 7, 28, 28, 8, 9, 28, 7, 8, 13, 14, 26, 14, 25, 26, 14, 15, 25, 15, 16, 25, 16, 24, 25, 16, 22, 24, 25, 24, 5, 22, 23, 24, 24, 4, 5, 24, 23, 4, 23, 3, 4, 23, 2, 3, 16, 17, 21, 19, 21, 18, 17, 18, 21, 21, 22, 16, 19, 20, 21, 20, 0, 21, 0, 1, 21, 21, 1, 22, 1, 2, 22, 22, 2, 23], "vertices": [1, 12, -3.23, 9.66, 1, 1, 12, 5.98, 12.38, 1, 2, 12, 17.09, 11.61, 0.7813, 13, -10.95, 4.5, 0.2187, 3, 12, 24.78, 5.91, 0.19884, 13, -3.89, 10.96, 0.8006, 14, 10.68, 24.7, 0.00056, 3, 12, 27.12, -1.97, 0.00229, 13, 4.29, 11.76, 0.95563, 14, 11.49, 16.53, 0.04208, 2, 13, 14.39, 9.61, 0.34157, 14, 9.37, 6.43, 0.65843, 2, 13, 12.39, 17.89, 0.00249, 14, 17.64, 8.44, 0.99751, 1, 14, 24.52, 7.25, 1, 1, 14, 27.73, 1.26, 1, 1, 14, 24.68, -3.57, 1, 1, 14, 17.36, -10.22, 1, 1, 14, 9.27, -10.15, 1, 2, 13, 30.45, 0.71, 0.17249, 14, 0.5, -9.65, 0.82751, 2, 13, 28.4, -7.03, 0.83533, 14, -7.25, -7.62, 0.16467, 3, 12, 9.85, -26.65, 0.00055, 13, 25.22, -9.91, 0.97468, 14, -10.13, -4.44, 0.02476, 2, 12, 8.2, -19.13, 0.05031, 13, 17.52, -10.09, 0.94969, 2, 12, 8, -14.44, 0.26361, 13, 12.89, -9.39, 0.73639, 2, 12, 1.47, -14.35, 0.67314, 13, 11.55, -15.79, 0.32686, 2, 12, -5.06, -8.22, 0.93283, 13, 4.28, -21.03, 0.06717, 2, 12, -9.49, 0.19, 0.99999, 13, -4.81, -23.76, 1e-05, 1, 12, -6.53, 5.26, 1, 1, 12, -0.29, 1.82, 1, 1, 12, 12.01, 1.53, 1, 3, 12, 18.85, 0.39, 0.06458, 13, 0.4, 4.09, 0.93341, 14, 3.82, 20.4, 0.00202, 2, 13, 9.56, 0.36, 0.99745, 14, 0.1, 11.24, 0.00255, 2, 12, 17.46, -17.96, 0.00094, 13, 18.15, -0.78, 0.99906, 2, 13, 23.2, 0.68, 0.10724, 14, 0.46, -2.41, 0.89276, 1, 14, 9.69, -1.45, 1, 1, 14, 19.21, -0.98, 1], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 38, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56], "width": 55, "height": 35}}, "sjpi12": {"sjpi11": {"x": 2.87, "y": -8.34, "rotation": -26.75, "width": 11, "height": 11}}}}], "animations": {"animation1": {"slots": {"body": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "color": "ffffffff"}]}, "sd": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "color": "ffffff72"}]}, "tou": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "color": "ffffffff"}]}, "shenzi": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "color": "ffffffff"}]}, "shou2": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "color": "ffffffff"}]}, "jio2": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "color": "ffffffff"}]}, "jiao": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "color": "ffffffff"}]}, "baibaodai": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "color": "ffffffff"}]}, "weiba": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "color": "ffffffff"}]}, "sjpi11": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "color": "ffffffff"}]}}, "bones": {"bone": {"translate": [{"y": 111.23, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3, "y": 7.32, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667}], "scale": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 0.968, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}]}, "bone2": {"translate": [{"time": 0.2333, "curve": 0.319, "c3": 0.652, "c4": 0.34}, {"time": 0.3333, "x": -1.53, "y": 1.84, "curve": 0.27, "c2": 0.09, "c3": 0.753}, {"time": 0.4333}]}, "bone3": {"rotate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 1.57}], "translate": [{"time": 0.2333, "curve": 0.319, "c3": 0.652, "c4": 0.34}, {"time": 0.3333, "x": 2.18, "y": 1.11, "curve": 0.27, "c2": 0.09, "c3": 0.753}, {"time": 0.4333}]}, "bone4": {"rotate": [{"time": 0.2333, "curve": 0.319, "c3": 0.652, "c4": 0.34}, {"time": 0.3333, "angle": 5.69, "curve": 0.27, "c2": 0.09, "c3": 0.753}, {"time": 0.4333}]}, "bone5": {"rotate": [{"time": 0.2333, "curve": 0.319, "c3": 0.652, "c4": 0.34}, {"time": 0.3333, "angle": -1.91, "curve": 0.27, "c2": 0.09, "c3": 0.753}, {"time": 0.4333, "angle": 1.01}]}, "bone8": {"rotate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -5.03}]}, "bone9": {"rotate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -13.41}]}, "bone16": {"rotate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -1.37}], "translate": [{"time": 0.2333, "curve": 0.319, "c3": 0.652, "c4": 0.34}, {"time": 0.3333, "x": 1.18, "y": -1.79, "curve": 0.27, "c2": 0.09, "c3": 0.753}, {"time": 0.4333}]}, "bone17": {"rotate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -3.89}], "translate": [{"time": 0.2333, "curve": 0.319, "c3": 0.652, "c4": 0.34}, {"time": 0.3333, "x": 3.67, "y": -0.15, "curve": 0.27, "c2": 0.09, "c3": 0.753}, {"time": 0.4333}]}, "bone18": {"rotate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -6.68}], "translate": [{"time": 0.2333, "curve": 0.319, "c3": 0.652, "c4": 0.34}, {"time": 0.3333, "x": 2.62, "y": 2.57, "curve": 0.27, "c2": 0.09, "c3": 0.753}, {"time": 0.4333}]}, "bone19": {"shear": [{"time": 0.4667}]}}}, "animation2": {"bones": {"bone16": {"rotate": [{"angle": -1.37, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -10.56, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": -1.37}]}, "bone15": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -10.56, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "bone17": {"rotate": [{"angle": -3.89, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -10.56, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": -3.89}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 4.27, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "bone3": {"rotate": [{"angle": 1.57, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 4.27, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": 1.57}]}, "bone4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 2.14, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "bone5": {"rotate": [{"angle": 1.01, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 2.14, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 1.6667, "angle": 1.01}]}, "bone7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -23.15, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "bone8": {"rotate": [{"angle": -5.03, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -23.15, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 1.6667, "angle": -5.03}]}, "bone9": {"rotate": [{"angle": -13.41, "curve": 0.368, "c2": 0.47, "c3": 0.753}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": -23.15, "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 1.6667, "angle": -13.41}]}, "bone18": {"rotate": [{"angle": -6.68, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -10.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.6667, "angle": -6.68}]}}}}}