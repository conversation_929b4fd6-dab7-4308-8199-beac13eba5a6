{"skeleton": {"hash": "qCf3WnigIUJeUDjms5ubDLJvDpU=", "spine": "3.8.75", "x": -304, "y": -226.23, "width": 544, "height": 465.37, "images": "./images/", "audio": "D:/spine导出/游戏开始界面"}, "bones": [{"name": "root"}, {"name": "<PERSON><PERSON>", "parent": "root", "length": 365.5, "x": 538.92, "y": 606.24}, {"name": "niao1", "parent": "<PERSON><PERSON>", "length": 48.75, "rotation": 1.97, "x": -314.62, "y": -446.77}, {"name": "niao2", "parent": "<PERSON><PERSON>", "length": 61.47, "rotation": -173.39, "x": -783.09, "y": -712.71}, {"name": "niao3", "parent": "niao1", "length": 51.68, "rotation": 77.88, "x": -2.19, "y": 24.05}, {"name": "niao4", "parent": "niao1", "length": 50.13, "rotation": 98.49, "x": -30.77, "y": 24.41, "color": "abe323ff"}, {"name": "niao5", "parent": "niao2", "length": 55.25, "rotation": -44.56, "x": 10.21, "y": -17.16}, {"name": "niao6", "parent": "niao2", "length": 46.74, "rotation": -62.36, "x": -12.24, "y": -37.17, "color": "abe323ff"}, {"name": "logo", "parent": "root", "length": 150, "rotation": 0.05, "x": 10.97, "y": -212, "color": "ca00ffff"}, {"name": "logo2", "parent": "logo", "x": -1.65, "y": 221.15, "color": "ca00ffff"}, {"name": "logo3", "parent": "logo", "rotation": 12.88, "x": 3.22, "y": 227.14, "color": "ca00ffff"}, {"name": "logo4", "parent": "logo", "x": -70.62, "y": 258.35, "color": "ca00ffff"}, {"name": "logo5", "parent": "logo", "rotation": -7.81, "x": 0.03, "y": 273.34, "color": "ca00ffff"}, {"name": "logo6", "parent": "logo", "x": 78.95, "y": 227.35, "color": "ca00ffff"}, {"name": "logo7", "parent": "logo", "x": 8.31, "y": 206.25, "color": "ca00ffff"}, {"name": "logo8", "parent": "logo", "x": -0.27, "y": 191.48, "color": "ca00ffff"}, {"name": "logo9", "parent": "logo", "rotation": -0.65, "x": -7.49, "y": 212.8, "color": "ff0000ff"}, {"name": "yun1", "parent": "logo9", "x": -150.91, "y": -130.63, "color": "ff0000ff"}, {"name": "yun2", "parent": "logo9", "x": -41.1, "y": -105.05, "color": "ff0000ff"}, {"name": "yun3", "parent": "logo9", "x": 80.46, "y": -131.3, "color": "ff0000ff"}, {"name": "yun4", "parent": "logo9", "x": 169.55, "y": -119.15, "color": "ff0000ff"}, {"name": "yun5", "parent": "logo9", "x": -173.54, "y": -44.57, "color": "ff0000ff"}, {"name": "yun6", "parent": "logo9", "x": -81.52, "y": -31.12, "color": "ff0000ff"}, {"name": "yun7", "parent": "logo9", "x": 49.06, "y": -36.09, "color": "ff0000ff"}, {"name": "yun8", "parent": "logo9", "x": 167.12, "y": -42.65, "color": "ff0000ff"}, {"name": "yun9", "parent": "logo9", "x": -175.99, "y": 34.12, "color": "ff0000ff"}, {"name": "yun10", "parent": "logo9", "x": -62.34, "y": 56.49, "color": "ff0000ff"}, {"name": "yun11", "parent": "logo9", "x": 52.54, "y": 46.47, "color": "ff0000ff"}, {"name": "yun12", "parent": "logo9", "x": 128.94, "y": 25.35, "color": "ff0000ff"}, {"name": "yun14", "parent": "logo9", "x": -45.2, "y": 131.05, "color": "ff0000ff"}, {"name": "yun15", "parent": "logo9", "x": 61.81, "y": 146.46, "color": "ff0000ff"}, {"name": "yun16", "parent": "logo9", "x": 175.36, "y": 104.01, "color": "ff0000ff"}, {"name": "logo10", "parent": "logo6", "x": -1.25, "y": -13.73, "color": "ffbc00ff"}, {"name": "logo11", "parent": "logo6", "x": 0.98, "y": 11.39, "color": "ffbc00ff"}, {"name": "logo12", "parent": "logo6", "length": 11.77, "rotation": -9.87, "x": -51.46, "y": -36.06, "color": "ffbc00ff"}, {"name": "logo13", "parent": "logo6", "length": 13.29, "rotation": -157.36, "x": 67.39, "y": -39.51, "color": "ffbc00ff"}, {"name": "logo14", "parent": "logo13", "length": 13.12, "rotation": 12.62, "x": 13.29, "color": "ffbc00ff"}], "slots": [{"name": "logo/logocs01", "bone": "logo2", "attachment": "logo/logocs01"}, {"name": "logo/logocs05", "bone": "logo5", "attachment": "logo/logocs05"}, {"name": "logo/logocs04", "bone": "logo3", "attachment": "logo/logocs04"}, {"name": "logo/logocs02", "bone": "logo8", "attachment": "logo/logocs02"}, {"name": "logo/logocs03", "bone": "logo7", "attachment": "logo/logocs03"}, {"name": "logo/logocs07", "bone": "logo6", "attachment": "logo/logocs07"}, {"name": "logo/logocs06", "bone": "logo4", "attachment": "logo/logocs06"}, {"name": "kaishijm037", "bone": "niao4", "attachment": "kaishijm037"}, {"name": "kaishijm038", "bone": "niao1", "attachment": "kaishijm038"}, {"name": "kaishijm039", "bone": "niao3", "attachment": "kaishijm039"}, {"name": "kaishijm041", "bone": "niao6", "attachment": "kaishijm041"}, {"name": "kaishijm042", "bone": "niao2", "attachment": "kaishijm042"}, {"name": "kaishijm043", "bone": "niao5", "attachment": "kaishijm043"}, {"name": "logo/eye1", "bone": "logo6"}, {"name": "logo/eye2", "bone": "logo6"}], "transform": [{"name": "loongface", "order": 2, "bones": ["logo11"], "target": "logo10", "x": 2.03, "y": 22.74, "rotateMix": -0.976, "translateMix": -0.976, "scaleMix": -0.976, "shearMix": -0.976}, {"name": "niaocb1", "bones": ["niao3"], "target": "niao4", "rotation": -20.61, "x": -4.57, "y": -28.21, "shearY": 360, "rotateMix": -1, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "niaocb2", "order": 1, "bones": ["niao5"], "target": "niao6", "rotation": 17.8, "x": -7.32, "y": 29.18, "rotateMix": -1, "translateMix": 0, "scaleMix": 0, "shearMix": 0}], "skins": [{"name": "default", "attachments": {"logo/logocs01": {"logo/logocs01": {"type": "mesh", "uvs": [0.96824, 0.91286, 0.80038, 0.99999, 0.66667, 1, 0.51256, 0.91835, 0.34766, 0.91121, 0.26744, 0.98496, 0.16667, 1, 0.06708, 0.94699, 0.02044, 0.81635, 0, 0.6, 0.01477, 0.40274, 0.10459, 0.29728, 0.15753, 0.17958, 0.26889, 0.06135, 0.38106, 0.00413, 0.5, 0, 0.66667, 0, 0.80524, 0.0557, 0.91727, 0.10072, 1, 0.2, 0.9925, 0.30774, 0.92658, 0.36747, 0.88694, 0.46912, 0.95127, 0.5893, 0.99999, 0.68439, 0.99999, 0.82586, 0.83333, 0.8, 0.66667, 0.8, 0.5, 0.8, 0.33333, 0.8, 0.16667, 0.8, 0.83333, 0.6, 0.66667, 0.6, 0.5, 0.6, 0.33333, 0.6, 0.16667, 0.6, 0.83333, 0.4, 0.66667, 0.4, 0.5, 0.4, 0.33333, 0.4, 0.16667, 0.4, 0.83333, 0.2, 0.66667, 0.2, 0.5, 0.2, 0.33333, 0.2, 0.22572, 0.25586], "triangles": [41, 17, 18, 19, 41, 18, 20, 41, 19, 21, 41, 20, 42, 15, 16, 42, 16, 17, 42, 17, 41, 44, 13, 14, 45, 12, 13, 43, 14, 15, 44, 14, 43, 43, 15, 42, 44, 45, 13, 21, 36, 41, 22, 36, 21, 31, 32, 36, 22, 31, 36, 38, 43, 42, 37, 42, 41, 38, 42, 37, 36, 37, 41, 33, 38, 37, 32, 37, 36, 39, 44, 43, 45, 44, 39, 40, 45, 39, 39, 43, 38, 35, 40, 39, 34, 39, 38, 11, 12, 45, 40, 11, 45, 10, 11, 40, 10, 40, 35, 31, 22, 23, 24, 26, 31, 24, 31, 23, 33, 37, 32, 28, 33, 32, 27, 32, 31, 28, 32, 27, 35, 39, 34, 34, 38, 33, 30, 35, 34, 29, 34, 33, 30, 34, 29, 9, 10, 35, 8, 9, 35, 30, 8, 35, 27, 31, 26, 1, 27, 26, 2, 27, 1, 3, 27, 2, 25, 26, 24, 0, 26, 25, 1, 26, 0, 29, 33, 28, 4, 29, 28, 3, 28, 27, 4, 28, 3, 5, 29, 4, 7, 8, 30, 5, 30, 29, 6, 7, 30, 5, 6, 30], "vertices": [2, 20, 21.7, -13.17, 0.97847, 24, 24.13, -89.68, 0.02153, 2, 20, -48.12, -44.37, 0.3523, 19, 40.97, -32.23, 0.6477, 4, 18, 106.64, -59.12, 0.04412, 20, -104.01, -45.01, 0.00663, 19, -14.92, -32.87, 0.94685, 23, 16.48, -128.07, 0.00239, 3, 18, 41.91, -31.35, 0.58921, 19, -79.66, -5.11, 0.30971, 23, -48.25, -100.31, 0.10109, 4, 17, 82.77, -4.06, 0.27683, 18, -27.05, -29.64, 0.68621, 21, 105.39, -90.12, 0.00093, 22, 13.37, -103.57, 0.03603, 3, 17, 49.53, -30.17, 0.71255, 18, -60.28, -55.76, 0.27137, 22, -19.87, -129.69, 0.01609, 2, 17, 7.47, -35.9, 0.94613, 18, -102.34, -61.49, 0.05387, 2, 17, -34.36, -17.87, 0.92834, 21, -11.74, -103.94, 0.07166, 2, 17, -54.38, 27.5, 0.50666, 21, -31.75, -58.57, 0.49334, 3, 17, -63.78, 102.9, 0.00256, 21, -41.15, 16.84, 0.77541, 25, -38.7, -61.86, 0.22203, 3, 21, -35.76, 85.75, 0.04394, 25, -33.31, 7.05, 0.95568, 29, -164.09, -89.87, 0.00038, 4, 22, -90.65, 109.52, 0.0006, 25, 3.82, 44.28, 0.74732, 26, -109.83, 21.91, 0.15474, 29, -126.97, -52.64, 0.09734, 3, 25, 25.48, 85.61, 0.3968, 26, -88.17, 63.24, 0.26878, 29, -105.31, -11.32, 0.33441, 3, 25, 71.56, 127.39, 0.11834, 26, -42.09, 105.03, 0.11881, 29, -59.23, 30.47, 0.76285, 4, 25, 118.21, 147.89, 0.01265, 26, 4.57, 125.53, 0.00079, 29, -12.57, 50.97, 0.93464, 30, -119.58, 35.56, 0.05192, 3, 27, -60.62, 137.56, 0.00155, 29, 37.12, 52.98, 0.63635, 30, -69.89, 37.56, 0.3621, 3, 29, 106.79, 53.77, 0.05578, 30, -0.22, 38.35, 0.93258, 31, -113.78, 80.8, 0.01164, 4, 27, 67.19, 119.57, 0.00712, 28, -9.22, 140.68, 0.00864, 30, 57.92, 19.57, 0.61962, 31, -55.64, 62.02, 0.36463, 4, 27, 114.19, 104.39, 0.00119, 28, 37.79, 125.5, 0.00019, 30, 104.92, 4.39, 0.1965, 31, -8.63, 46.84, 0.80212, 2, 30, 139.89, -29.86, 0.00111, 31, 26.34, 12.59, 0.99889, 2, 28, 70.05, 53.61, 0.07558, 31, 23.63, -25.05, 0.92442, 4, 27, 119.14, 11.34, 0.00579, 28, 42.73, 32.46, 0.44713, 30, 109.87, -88.65, 0.00433, 31, -3.69, -46.21, 0.54276, 4, 23, 106.45, 58.23, 0.00109, 24, -11.62, 64.79, 0.14098, 28, 26.57, -3.21, 0.80419, 31, -19.85, -81.87, 0.05374, 2, 24, 15.75, 23.16, 0.86399, 28, 53.93, -44.84, 0.13601, 2, 20, 34.06, 66.71, 0.17719, 24, 36.49, -9.79, 0.82281, 2, 20, 34.62, 17.34, 0.74052, 24, 37.05, -59.16, 0.25948, 5, 20, -35.14, 25.57, 0.41467, 19, 53.95, 37.72, 0.24189, 23, 85.35, -57.49, 0.09045, 24, -32.71, -50.93, 0.2464, 28, 5.47, -118.93, 0.0066, 7, 18, 105.85, 10.68, 0.07966, 20, -104.8, 24.78, 0.00938, 19, -15.71, 36.93, 0.52569, 22, 146.27, -63.25, 0.00011, 23, 15.69, -58.28, 0.34988, 24, -102.37, -51.72, 0.03276, 28, -64.19, -119.72, 0.00252, 6, 18, 36.19, 9.89, 0.60077, 19, -85.38, 36.14, 0.14731, 22, 76.6, -64.04, 0.03704, 23, -53.97, -59.07, 0.21237, 26, 57.43, -151.65, 0.00195, 27, -57.45, -141.62, 0.00055, 5, 17, 76.34, 34.69, 0.17251, 18, -33.47, 9.1, 0.58233, 21, 98.96, -51.38, 0.0222, 22, 6.94, -64.83, 0.2225, 23, -123.63, -59.86, 0.00046, 4, 17, 6.68, 33.9, 0.56392, 18, -103.14, 8.31, 0.05343, 21, 29.3, -52.17, 0.27756, 22, -62.72, -65.62, 0.1051, 6, 20, -35.93, 95.37, 0.00661, 19, 53.16, 107.51, 0.0225, 23, 84.56, 12.31, 0.13415, 24, -33.5, 18.87, 0.48879, 27, 81.08, -70.25, 0.00512, 28, 4.68, -49.13, 0.34282, 7, 20, -105.59, 94.58, 0.00095, 19, -16.51, 106.72, 0.00808, 23, 14.9, 11.52, 0.77879, 24, -103.16, 18.08, 0.03524, 26, 126.3, -81.06, 0.00049, 27, 11.42, -71.04, 0.09212, 28, -64.98, -49.92, 0.08432, 6, 18, 35.4, 79.69, 0.12286, 19, -86.17, 105.93, 0.00427, 22, 75.81, 5.75, 0.23532, 23, -54.76, 10.73, 0.41472, 26, 56.64, -81.85, 0.11172, 27, -58.24, -71.83, 0.11111, 6, 18, -34.26, 78.9, 0.00733, 22, 6.15, 4.96, 0.92406, 23, -124.42, 9.94, 0.01737, 25, 100.62, -60.28, 0.00162, 26, -13.02, -82.64, 0.04345, 27, -127.9, -72.62, 0.00617, 6, 17, 5.89, 103.69, 0.01342, 18, -103.93, 78.11, 0.00202, 21, 28.51, 17.63, 0.53802, 22, -63.51, 4.17, 0.27482, 25, 30.96, -61.07, 0.14943, 26, -82.69, -83.43, 0.02229, 4, 27, 80.29, -0.45, 0.06215, 28, 3.89, 20.66, 0.76505, 30, 71.02, -100.45, 0.0223, 31, -42.53, -58, 0.15049, 5, 23, 14.11, 81.31, 0.01941, 27, 10.63, -1.24, 0.86499, 28, -65.77, 19.87, 0.09172, 30, 1.36, -101.24, 0.01624, 31, -112.19, -58.79, 0.00764, 7, 18, 34.61, 149.48, 0.00181, 22, 75.02, 75.55, 0.06574, 23, -55.55, 80.52, 0.08795, 26, 55.85, -12.06, 0.3824, 27, -59.03, -2.03, 0.3987, 29, 38.71, -86.61, 0.04875, 30, -68.3, -102.03, 0.01464, 7, 21, 97.38, 88.21, 0.00203, 22, 5.36, 74.76, 0.13212, 23, -125.22, 79.73, 0.00434, 25, 99.83, 9.52, 0.06817, 26, -13.82, -12.85, 0.78924, 27, -128.69, -2.82, 0.00253, 29, -30.95, -87.4, 0.00158, 5, 21, 27.72, 87.42, 0.01883, 22, -64.3, 73.97, 0.07597, 25, 30.17, 8.73, 0.68562, 26, -83.48, -13.64, 0.18829, 29, -100.62, -88.19, 0.0313, 4, 27, 79.5, 69.34, 0.06722, 28, 3.1, 90.46, 0.09927, 30, 70.23, -30.65, 0.28435, 31, -43.32, 11.8, 0.54917, 6, 26, 124.72, 58.53, 0.00838, 27, 9.84, 68.55, 0.26646, 28, -66.57, 89.67, 0.03181, 29, 107.58, -16.03, 0.03472, 30, 0.57, -31.44, 0.61287, 31, -112.99, 11, 0.04576, 5, 23, -56.34, 150.32, 2e-05, 26, 55.05, 57.74, 0.11921, 27, -59.82, 67.76, 0.17267, 29, 37.92, -16.82, 0.50791, 30, -69.09, -32.23, 0.20019, 4, 25, 99.04, 79.31, 0.08535, 26, -14.61, 56.95, 0.2916, 27, -129.49, 66.97, 0.00053, 29, -31.75, -17.61, 0.62253, 4, 22, -40.19, 124.55, 0.00614, 25, 54.28, 59.31, 0.35441, 26, -59.37, 36.94, 0.35853, 29, -76.51, -37.61, 0.28093], "hull": 26, "edges": [0, 2, 2, 4, 4, 6, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 26, 28, 28, 30, 30, 32, 38, 40, 48, 50, 50, 0, 44, 46, 46, 48, 40, 42, 42, 44, 36, 38, 32, 34, 34, 36, 24, 26, 20, 22, 22, 24, 6, 8, 8, 10], "width": 418, "height": 349}}, "logo/logocs02": {"logo/logocs02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [11.98, -123.73, -142.02, -123.73, -142.02, 4.27, 11.98, 4.27], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 154, "height": 128}}, "logo/logocs03": {"logo/logocs03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [162.98, -220.48, -16.02, -220.48, -16.02, 19.52, 162.98, 19.52], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 179, "height": 240}}, "logo/logocs04": {"logo/logocs04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [3.44, -57.5, -206.15, -9.57, -156.66, 206.84, 52.93, 158.91], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 215, "height": 222}}, "logo/logocs05": {"logo/logocs05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [144.37, -14.14, -11.17, -35.48, -29.52, 98.27, 126.02, 119.61], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 157, "height": 135}}, "logo/logocs06": {"logo/logocs06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [88.65, -30.53, -45.35, -30.53, -45.35, 25.47, 88.65, 25.47], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 134, "height": 56}}, "logo/logocs07": {"logo/logocs07": {"type": "mesh", "uvs": [0.17269, 0, 0.23517, 0.00017, 0.27128, 0.06278, 0.36118, 0.17215, 0.53351, 0.10533, 0.58062, 0.11, 0.63089, 0.2066, 0.68154, 0.21123, 0.81367, 0.10396, 0.83925, 0.07288, 0.89305, 0.07277, 0.94011, 0.13071, 0.9466, 0.21118, 0.94913, 0.3785, 0.98817, 0.5868, 0.98819, 0.66933, 0.98824, 0.83203, 0.96691, 0.88431, 0.85348, 0.97121, 0.7595, 0.97097, 0.70964, 0.90025, 0.68049, 0.81042, 0.62955, 0.81489, 0.51461, 0.79978, 0.41316, 0.78643, 0.36316, 0.77016, 0.32904, 0.86075, 0.26485, 0.88715, 0.21186, 0.87749, 0.13669, 0.84669, 0.11292, 0.7884, 0.1126, 0.73035, 0.0514, 0.72993, 0.03621, 0.7032, 0.00569, 0.57716, 0.00575, 0.48929, 0.04378, 0.44192, 0.05494, 0.34093, 0.12968, 0.34472, 0.08277, 0.24134, 0.1268, 0.0534, 0.1219, 0.67004, 0.17407, 0.63493, 0.24099, 0.62861, 0.29659, 0.64998, 0.33828, 0.67773, 0.3565, 0.73104, 0.70845, 0.74846, 0.76582, 0.72401, 0.83188, 0.71343, 0.85613, 0.68746, 0.86511, 0.63627, 0.86214, 0.61311, 0.88598, 0.63873, 0.92276, 0.6473, 0.91475, 0.72896, 0.86401, 0.78011, 0.77451, 0.8373, 0.18909, 0.73063, 0.26461, 0.76606, 0.30535, 0.79292, 0.18128, 0.08076, 0.21142, 0.18508, 0.27739, 0.26612, 0.30982, 0.29771, 0.88396, 0.18717, 0.8369, 0.27769, 0.73277, 0.36266, 0.8245, 0.40118, 0.8513, 0.48903, 0.85462, 0.53432, 0.19005, 0.33738, 0.16204, 0.37852, 0.15305, 0.44163, 0.28285, 0.44452, 0.43281, 0.44056, 0.51673, 0.44614, 0.59505, 0.46406, 0.73155, 0.49989, 0.34661, 0.44284, 0.66551, 0.48255, 0.79868, 0.51917, 0.19557, 0.44991, 0.18879, 0.53224, 0.26935, 0.55428, 0.34767, 0.57358, 0.42711, 0.59013, 0.50767, 0.60119, 0.58712, 0.61089, 0.66321, 0.61371, 0.7393, 0.61654, 0.79862, 0.60699, 0.29523, 0.35122, 0.34672, 0.33069, 0.43626, 0.31432, 0.52802, 0.30892, 0.61417, 0.33509, 0.6936, 0.36399, 0.7417, 0.3956, 0.80323, 0.42449, 0.52922, 0.20326, 0.57733, 0.21292, 0.4285, 0.20727, 0.4149, 0.74831, 0.506, 0.76472, 0.60531, 0.77362, 0.66366, 0.77745, 0.69629, 0.7754], "triangles": [55, 53, 54, 31, 32, 41, 41, 34, 83, 42, 41, 83, 35, 36, 34, 42, 83, 43, 33, 34, 41, 32, 33, 41, 41, 42, 58, 58, 42, 43, 59, 43, 44, 54, 14, 15, 53, 50, 51, 54, 53, 52, 28, 58, 59, 50, 55, 56, 19, 57, 18, 20, 21, 57, 17, 56, 16, 21, 107, 57, 107, 106, 47, 57, 49, 56, 21, 106, 107, 55, 50, 53, 51, 91, 52, 49, 50, 56, 57, 48, 49, 48, 91, 49, 48, 90, 91, 88, 77, 80, 88, 87, 77, 24, 25, 103, 103, 86, 87, 104, 88, 105, 24, 104, 23, 105, 88, 89, 22, 105, 106, 23, 104, 105, 23, 105, 22, 106, 105, 89, 89, 88, 80, 24, 103, 104, 104, 87, 88, 104, 103, 87, 86, 103, 45, 80, 77, 97, 89, 80, 78, 45, 85, 86, 103, 46, 45, 25, 46, 103, 91, 70, 52, 7, 8, 66, 67, 7, 66, 65, 11, 12, 66, 8, 65, 66, 12, 13, 66, 65, 12, 96, 6, 7, 58, 43, 59, 27, 59, 60, 60, 59, 46, 27, 60, 26, 29, 58, 28, 59, 45, 46, 25, 60, 46, 29, 30, 58, 30, 31, 58, 45, 44, 85, 44, 84, 85, 43, 83, 84, 73, 83, 36, 38, 36, 37, 36, 38, 73, 84, 82, 74, 83, 73, 82, 82, 72, 71, 86, 85, 75, 85, 79, 75, 74, 92, 79, 74, 71, 92, 79, 93, 75, 79, 92, 93, 71, 63, 92, 92, 64, 93, 92, 63, 64, 71, 38, 39, 71, 62, 63, 71, 39, 62, 93, 3, 102, 93, 64, 3, 64, 63, 3, 63, 62, 3, 39, 61, 62, 39, 40, 61, 62, 2, 3, 62, 61, 2, 40, 0, 61, 61, 1, 2, 61, 0, 1, 84, 74, 79, 71, 74, 82, 72, 38, 71, 85, 84, 79, 82, 73, 72, 73, 38, 72, 83, 82, 84, 36, 83, 34, 43, 84, 44, 31, 41, 58, 26, 60, 25, 59, 44, 45, 28, 59, 27, 86, 75, 76, 75, 93, 94, 93, 102, 94, 4, 102, 3, 75, 94, 76, 94, 102, 95, 95, 100, 101, 95, 102, 100, 6, 101, 5, 100, 102, 4, 101, 100, 5, 100, 4, 5, 96, 101, 6, 8, 9, 65, 65, 10, 11, 65, 9, 10, 68, 66, 13, 66, 68, 67, 76, 94, 95, 95, 101, 96, 97, 7, 67, 97, 96, 7, 69, 68, 13, 68, 98, 67, 97, 67, 98, 77, 76, 96, 77, 96, 97, 76, 95, 96, 99, 98, 68, 99, 68, 69, 81, 91, 90, 81, 78, 99, 91, 81, 70, 81, 90, 78, 80, 97, 98, 14, 69, 13, 78, 98, 99, 81, 69, 70, 14, 70, 69, 81, 99, 69, 78, 80, 98, 14, 54, 70, 90, 89, 78, 87, 86, 76, 87, 76, 77, 47, 89, 90, 89, 47, 106, 47, 90, 48, 49, 91, 50, 51, 50, 91, 54, 52, 70, 53, 51, 52, 47, 48, 57, 22, 106, 21, 18, 56, 17, 18, 57, 56, 107, 47, 57, 19, 20, 57, 55, 15, 16, 16, 56, 55, 55, 54, 15], "vertices": [2, 13, -57.52, 62.83, 0.74275, 33, -58.69, 49.12, 0.25725, 2, 13, -47.02, 62.81, 0.74449, 33, -48.2, 49.1, 0.25551, 2, 13, -40.95, 54.23, 0.74698, 33, -42.13, 40.52, 0.25302, 2, 13, -25.85, 39.25, 0.77281, 33, -27.03, 25.54, 0.22719, 2, 13, 3.1, 48.4, 0.7993, 33, 1.92, 34.69, 0.2007, 2, 13, 11.02, 47.76, 0.7981, 33, 9.84, 34.05, 0.2019, 3, 13, 19.46, 34.53, 0.78074, 36, -16.58, -85.22, 0.00064, 33, 18.28, 20.82, 0.21862, 3, 13, 27.97, 33.89, 0.75185, 36, -23.16, -79.79, 0.00206, 33, 26.79, 20.19, 0.24609, 2, 13, 50.17, 48.59, 0.70228, 33, 48.99, 34.88, 0.29772, 1, 13, 54.47, 52.85, 1, 2, 13, 63.5, 52.86, 0.69631, 33, 62.33, 39.15, 0.30369, 2, 13, 71.41, 44.92, 0.69269, 33, 70.23, 31.22, 0.30731, 2, 13, 72.5, 33.9, 0.68641, 33, 71.32, 20.19, 0.31359, 2, 13, 72.93, 10.98, 0.63451, 33, 71.75, -2.73, 0.36549, 2, 13, 79.48, -17.56, 0.36028, 33, 78.31, -31.27, 0.63972, 2, 13, 79.49, -28.87, 0.44818, 33, 78.31, -42.57, 0.55182, 3, 13, 79.5, -51.16, 0.82714, 36, -16.13, 19.4, 0.0223, 35, -6.69, 15.41, 0.15056, 3, 13, 75.91, -58.32, 0.8947, 36, -9.07, 23.18, 0.05439, 33, 74.74, -72.03, 0.05091, 4, 13, 56.86, -70.22, 0.73662, 36, 13.36, 21.9, 0.11834, 35, 21.55, 24.29, 0.10911, 33, 55.68, -83.93, 0.03593, 3, 13, 41.07, -70.19, 0.83108, 36, 26.23, 12.76, 0.16665, 35, 36.11, 18.18, 0.00227, 2, 13, 32.69, -60.5, 0.688, 36, 27.48, 0.01, 0.312, 4, 13, 27.79, -48.2, 0.61237, 36, 24.37, -12.86, 0.24223, 35, 39.89, -7.23, 0.00166, 33, 26.62, -61.9, 0.14374, 2, 13, 19.24, -48.81, 0.57227, 33, 18.06, -62.52, 0.42773, 2, 13, -0.07, -46.74, 0.56976, 33, -1.25, -60.45, 0.43024, 2, 13, -17.12, -44.91, 0.56752, 33, -18.29, -58.62, 0.43248, 5, 13, -25.52, -42.68, 0.33539, 34, 26.7, -2.07, 0.46512, 36, 64.72, -48.15, 0.02688, 32, -24.27, -28.95, 0.0359, 33, -26.69, -56.39, 0.13671, 3, 13, -31.25, -55.09, 0.61943, 34, 23.18, -15.28, 0.37964, 36, 76.56, -41.32, 0.00093, 2, 13, -42.03, -58.71, 0.656, 34, 13.17, -20.69, 0.344, 2, 13, -50.94, -57.38, 0.432, 34, 4.18, -20.91, 0.568, 2, 13, -63.56, -53.17, 0.704, 34, -8.99, -18.92, 0.296, 2, 13, -67.56, -45.18, 0.88, 34, -14.29, -11.74, 0.12, 3, 13, -67.61, -37.23, 0.704, 34, -15.71, -3.91, 0.00973, 33, -68.79, -50.93, 0.28627, 3, 13, -77.89, -37.17, 0.05512, 34, -25.85, -5.62, 0.04, 33, -79.07, -50.88, 0.90488, 3, 13, -80.45, -33.51, 0.07105, 34, -28.99, -2.45, 0.04, 33, -81.62, -47.21, 0.88895, 2, 13, -85.57, -16.24, 0.25574, 33, -86.75, -29.95, 0.74426, 2, 13, -85.56, -4.2, 0.36284, 33, -86.74, -17.91, 0.63716, 2, 13, -79.17, 2.29, 0.4603, 33, -80.35, -11.42, 0.5397, 2, 13, -77.3, 16.12, 0.55208, 33, -78.47, 2.42, 0.44792, 2, 13, -64.74, 15.6, 0.64216, 33, -65.92, 1.9, 0.35784, 2, 13, -72.62, 29.77, 0.71371, 33, -73.8, 16.06, 0.28629, 2, 13, -65.23, 55.52, 0.73957, 33, -66.4, 41.81, 0.26043, 4, 13, -66.05, -28.96, 0.06618, 34, -15.59, 4.49, 0.032, 32, -64.8, -15.23, 0.30163, 33, -67.23, -42.67, 0.60019, 3, 13, -57.29, -24.15, 0.16382, 32, -56.04, -10.42, 0.21958, 33, -58.46, -37.86, 0.61659, 4, 13, -46.04, -23.29, 0.26205, 36, 70.28, -75.83, 0.00113, 32, -44.8, -9.56, 0.19349, 33, -47.22, -37, 0.54332, 4, 13, -36.7, -26.22, 0.31902, 36, 64.34, -68.05, 0.01594, 32, -35.46, -12.48, 0.17464, 33, -37.88, -39.92, 0.4904, 4, 13, -29.7, -30.02, 0.31773, 36, 60.82, -60.9, 0.04411, 32, -28.45, -16.29, 0.25301, 33, -30.87, -43.73, 0.38515, 5, 13, -26.64, -37.32, 0.70213, 34, 24.68, 3.02, 0.20118, 36, 62.54, -53.17, 0.01142, 32, -25.39, -23.59, 0.02454, 33, -27.81, -51.03, 0.06073, 4, 13, 32.49, -39.71, 0.39409, 36, 15.64, -17.08, 0.02459, 32, 33.74, -25.98, 0.10366, 33, 31.31, -53.42, 0.47766, 2, 13, 42.13, -36.36, 0.84096, 33, 40.95, -50.07, 0.15904, 4, 13, 53.23, -34.91, 0.70163, 36, -4.07, -9.03, 0.012, 35, 11.3, -9.7, 0.012, 33, 52.05, -48.62, 0.27437, 2, 13, 57.3, -31.35, 0.74921, 33, 56.12, -45.06, 0.25079, 2, 13, 58.81, -24.34, 0.35949, 33, 57.63, -38.05, 0.64051, 2, 13, 58.31, -21.16, 0.87303, 33, 57.14, -34.87, 0.12697, 4, 13, 62.32, -24.67, 0.32007, 36, -17.4, -12.14, 0.004, 35, -1.03, -15.65, 0.004, 33, 61.14, -38.38, 0.67193, 2, 13, 68.49, -25.85, 0.25609, 33, 67.32, -39.56, 0.74391, 2, 13, 67.15, -37.04, 0.79924, 35, -0.73, -2.38, 0.20076, 2, 13, 58.63, -44.04, 0.68, 35, 9.83, 0.81, 0.32, 2, 13, 43.59, -51.88, 0.77158, 36, 13.6, -0.74, 0.22842, 3, 13, -54.76, -37.26, 0.536, 34, -3.04, -1.75, 0.42314, 32, -53.52, -23.53, 0.04086, 3, 13, -42.07, -42.12, 0.392, 34, 10.29, -4.36, 0.55936, 32, -40.83, -28.39, 0.04864, 3, 13, -35.23, -45.8, 0.59681, 34, 17.66, -6.81, 0.40111, 36, 74.45, -51.21, 0.00208, 2, 13, -56.07, 51.77, 0.85412, 32, -54.83, 65.5, 0.14588, 2, 13, -51.01, 37.47, 0.85072, 32, -49.76, 51.21, 0.14928, 2, 13, -39.93, 26.37, 0.8545, 32, -38.68, 40.1, 0.1455, 2, 13, -34.48, 22.04, 0.8593, 32, -33.23, 35.78, 0.1407, 2, 13, 61.98, 37.19, 0.82222, 32, 63.22, 50.92, 0.17778, 3, 13, 54.07, 24.79, 0.81893, 36, -39.22, -57.29, 0.00455, 32, 55.32, 38.52, 0.17652, 2, 13, 36.58, 13.15, 0.84476, 32, 37.82, 26.88, 0.15524, 1, 13, 51.99, 7.87, 1, 1, 13, 56.49, -4.17, 1, 1, 13, 57.05, -10.37, 1, 1, 13, -54.6, 16.61, 1, 1, 13, -59.3, 10.97, 1, 1, 13, -60.82, 2.33, 1, 3, 13, -39.01, 1.93, 0.82639, 36, 49.98, -92.36, 3e-05, 32, -37.76, 15.66, 0.17358, 3, 13, -13.82, 2.47, 0.7819, 36, 29.1, -78.26, 0.00104, 32, -12.57, 16.21, 0.21706, 2, 13, 0.28, 1.71, 0.71139, 32, 1.53, 15.44, 0.28861, 2, 13, 13.44, -0.75, 0.78219, 32, 14.69, 12.99, 0.21781, 2, 13, 36.37, -5.65, 0.83139, 32, 37.62, 8.08, 0.16861, 3, 13, -28.3, 2.16, 0.76145, 36, 41.1, -86.37, 0.00062, 32, -27.05, 15.89, 0.23793, 2, 13, 25.28, -3.28, 0.77099, 32, 26.52, 10.45, 0.22901, 2, 13, 47.65, -8.29, 0.76738, 32, 48.9, 5.44, 0.23262, 2, 13, -53.67, 1.19, 0.7515, 32, -52.43, 14.92, 0.2485, 2, 13, -54.81, -10.09, 0.64936, 32, -53.57, 3.65, 0.35064, 3, 13, -41.28, -13.11, 0.73038, 36, 60.51, -81.39, 0.00329, 32, -40.03, 0.63, 0.26633, 3, 13, -28.12, -15.75, 0.66099, 36, 51.3, -71.64, 0.02388, 32, -26.87, -2.02, 0.31514, 2, 13, -14.77, -18.02, 0.712, 32, -13.53, -4.29, 0.288, 3, 13, -1.24, -19.53, 0.71947, 35, 55.65, -44.86, 0.005, 32, 0.01, -5.8, 0.27553, 2, 13, 12.11, -20.86, 0.72596, 32, 13.35, -7.13, 0.27404, 2, 13, 24.89, -21.25, 0.71051, 32, 26.14, -7.52, 0.28949, 4, 13, 37.67, -21.63, 0.75123, 36, 0.97, -28.85, 0.008, 35, 20.54, -27.94, 0.008, 32, 38.92, -7.9, 0.23277, 2, 13, 47.64, -20.33, 0.6891, 32, 48.89, -6.6, 0.3109, 2, 13, -36.93, 14.71, 0.85823, 32, -35.68, 28.45, 0.14177, 2, 13, -28.28, 17.53, 0.78793, 32, -27.03, 31.26, 0.21207, 2, 13, -13.24, 19.77, 0.79149, 32, -11.99, 33.5, 0.20851, 2, 13, 2.18, 20.51, 0.70042, 32, 3.42, 34.24, 0.29958, 2, 13, 16.65, 16.92, 0.78147, 32, 17.9, 30.66, 0.21853, 2, 13, 30, 12.96, 0.77595, 32, 31.24, 26.7, 0.22405, 2, 13, 38.08, 8.63, 0.84935, 32, 39.32, 22.36, 0.15065, 2, 13, 48.41, 4.68, 0.80597, 32, 49.66, 18.41, 0.19403, 2, 13, 2.38, 34.98, 0.69911, 32, 3.63, 48.72, 0.30089, 3, 13, 10.46, 33.66, 0.84441, 36, -8.73, -89.71, 1e-05, 32, 11.71, 47.39, 0.15558, 2, 13, -14.54, 34.44, 0.84271, 32, -13.3, 48.17, 0.15729, 3, 13, -16.83, -39.69, 0.44798, 32, -15.58, -25.96, 0.2083, 33, -18, -53.39, 0.34372, 4, 13, -1.52, -41.94, 0.36576, 35, 64.53, -24.29, 0.00073, 32, -0.27, -28.2, 0.30929, 33, -2.7, -55.64, 0.32423, 3, 13, 15.16, -43.15, 0.43075, 32, 16.41, -29.42, 0.2533, 33, 13.99, -56.86, 0.31595, 3, 13, 24.97, -43.68, 0.43079, 32, 26.21, -29.95, 0.16382, 33, 23.79, -57.39, 0.40539, 4, 13, 30.45, -43.4, 0.76833, 36, 19.43, -15.25, 0.152, 35, 35.59, -10.64, 0.00845, 33, 29.27, -57.11, 0.07122], "hull": 41, "edges": [0, 80, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 62, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 50, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 28, 30, 30, 32, 108, 30, 108, 110, 110, 112, 112, 114, 82, 116, 116, 118, 118, 120, 0, 2, 0, 122, 122, 124, 124, 126, 126, 128, 20, 130, 130, 132, 132, 134, 136, 138, 138, 140, 142, 144, 144, 146, 150, 152, 152, 154, 148, 158, 158, 150, 154, 160, 160, 156, 156, 162, 148, 164, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 44, 46, 46, 48, 92, 206, 206, 208, 208, 210, 210, 212, 42, 214, 214, 94, 212, 214], "width": 168, "height": 137}}, "kaishijm037": {"kaishijm037": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-4.39, -3.56, -0.21, 19.06, 52.89, 9.25, 48.71, -13.37], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 23, "height": 54}}, "kaishijm038": {"kaishijm038": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [12.82, -25.79, -56.14, -23.41, -53.62, 49.54, 15.33, 47.16], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 69, "height": 73}}, "kaishijm039": {"kaishijm039": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-4.87, -19.89, -10.15, 9.64, 52.84, 20.92, 58.13, -8.61], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 64}}, "kaishijm041": {"kaishijm041": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-6.89, -1.71, 12.24, 26.39, 50.27, 0.51, 31.13, -27.6], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 34, "height": 46}}, "kaishijm042": {"kaishijm042": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-31.9, 30.27, 26.71, 23.48, 17.04, -59.96, -41.57, -53.17], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 59, "height": 84}}, "kaishijm043": {"kaishijm043": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-11.45, 2.82, 31.92, 36.64, 60.82, -0.42, 17.45, -34.24], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 55, "height": 47}}, "logo/eye2": {"logo/eye2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [53.36, -20.94, -54.64, -20.84, -54.6, 25.16, 53.4, 25.06], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 108, "height": 46}}, "logo/eye1": {"logo/eye1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [53.48, -19.12, -54.52, -19.12, -54.52, 26.88, 53.48, 26.88], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 108, "height": 46}}}}], "animations": {"logo_loading_01": {"slots": {"logo/logocs04": {"attachment": [{"name": null}, {"time": 0.3, "name": "logo/logocs04"}]}, "logo/logocs01": {"attachment": [{"name": null}, {"time": 0.1, "name": "logo/logocs01"}]}, "logo/logocs05": {"attachment": [{"name": null}, {"time": 0.5, "name": "logo/logocs05"}]}, "logo/logocs02": {"attachment": [{"name": null}, {"time": 0.7, "name": "logo/logocs02"}]}, "logo/logocs07": {"attachment": [{"name": null}, {"time": 1.2667, "name": "logo/logocs07"}]}, "logo/logocs06": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.8, "color": "ffffffff"}]}, "logo/logocs03": {"attachment": [{"name": null}, {"time": 0.9333, "name": "logo/logocs03"}]}}, "bones": {"niao4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 2.9333}]}, "niao6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 2.9333}]}, "niao1": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": -21.55, "y": -35.61, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "x": 33.73, "y": 14.99, "curve": 0.25, "c3": 0.75}, {"time": 2.9333}]}, "niao2": {"translate": [{"x": 15.28, "y": 37.44, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.2667, "x": -21.55, "y": -35.61, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "x": 18.74, "y": 45.91, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.9333, "x": 15.28, "y": 37.44}]}, "logo6": {"translate": [{"time": 1.2667, "y": -59.26, "curve": 0.25, "c3": 0.75}, {"time": 1.4667}], "scale": [{"x": 0.1, "y": 0.1, "curve": "stepped"}, {"time": 1.2667, "x": 0.1, "y": 0.1, "curve": 0.25, "c3": 0.169, "c4": 1.63}, {"time": 1.8667}]}, "logo5": {"translate": [{"x": 418.19, "y": 226.63, "curve": "stepped"}, {"time": 0.5, "x": 418.19, "y": 226.63, "curve": 0, "c2": 1.04, "c3": 0.526, "c4": 0.99}, {"time": 1}], "scale": [{"time": 0.5667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.7333, "x": 0.602, "curve": 0.339, "c2": 0.35, "c3": 0.679, "c4": 0.7}, {"time": 0.8667, "x": 1.11, "y": 1.374, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 1}]}, "logo4": {"translate": [{"x": 81.71, "y": 31.85, "curve": "stepped"}, {"time": 1.3333, "x": 81.71, "y": 31.85, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "x": -5.7, "y": 26.59}]}, "logo3": {"translate": [{"x": -445.17, "y": 260.36, "curve": "stepped"}, {"time": 0.3, "x": -445.17, "y": 260.36, "curve": 0, "c2": 1.04, "c3": 0.812}, {"time": 0.6667}], "scale": [{"time": 0.2667, "curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 0.4667, "x": 0.516, "y": 0.72, "curve": 0.533, "c2": 0.35, "c3": 0.692, "c4": 0.72}, {"time": 0.5667, "x": 1.041, "y": 1.095, "curve": 0.163, "c2": 0.59, "c3": 0.404}, {"time": 0.6667}]}, "logo8": {"translate": [{"x": -449.22, "y": -141.64, "curve": "stepped"}, {"time": 0.7, "x": -449.22, "y": -141.64, "curve": 0, "c2": 1.04, "c3": 0.494, "c4": 1.01}, {"time": 1.1333}], "scale": [{"time": 0.7, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.8667, "x": 0.659, "curve": 0.336, "c2": 0.34, "c3": 0.682, "c4": 0.71}, {"time": 1, "x": 1.245, "y": 1.43, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 1.1333}]}, "logo7": {"translate": [{"x": 415.49, "y": -141.65, "curve": "stepped"}, {"time": 0.9333, "x": 415.49, "y": -141.65, "curve": 0, "c2": 1.04, "c3": 0.502, "c4": 0.98}, {"time": 1.3333}], "scale": [{"time": 0.9667, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 1.1667, "x": 0.686, "curve": 0.336, "c2": 0.34, "c3": 0.682, "c4": 0.71}, {"time": 1.2667, "x": 0.989, "y": 1.122, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 1.3333}]}, "logo2": {"scale": [{"x": 0.067, "y": 0.067}]}, "yun15": {"translate": [{"x": -2.82, "y": 2.76, "curve": 0.38, "c2": 0.53, "c3": 0.744}, {"time": 0.5667, "x": -4.7, "y": 4.6, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.9, "x": -3.83, "y": 3.75, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.9, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 2.6667, "x": -2.82, "y": 2.76}], "scale": [{"x": 1.103, "y": 1.103, "curve": 0.38, "c2": 0.53, "c3": 0.744}, {"time": 0.5667, "x": 1.172, "y": 1.172, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.9, "x": 1.14, "y": 1.14, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.9, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 2.6667, "x": 1.103, "y": 1.103}]}, "yun7": {"translate": [{"x": -7.75, "y": 8.68, "curve": 0.312, "c2": 0.24, "c3": 0.648, "c4": 0.59}, {"time": 0.1667, "x": -6.72, "y": 7.52, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": -8.24, "y": 9.22, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.6667, "x": -7.75, "y": 8.68}], "scale": [{"x": 1.162, "y": 1.162, "curve": 0.312, "c2": 0.24, "c3": 0.648, "c4": 0.59}, {"time": 0.1667, "x": 1.14, "y": 1.14, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": 1.172, "y": 1.172, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.6667, "x": 1.162, "y": 1.162}]}, "yun8": {"translate": [{"x": 8.66, "y": 5.54, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "x": 9.24, "y": 5.91, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "x": 8.66, "y": 5.54}], "scale": [{"x": 1.161, "y": 1.161, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "x": 1.172, "y": 1.172, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "x": 1.161, "y": 1.161}]}, "yun16": {"translate": [{"x": 0.24, "y": 7.32, "curve": 0.35, "c2": 0.39, "c3": 0.757}, {"time": 0.9, "x": 0.88, "y": 26.76, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "x": 0.24, "y": 7.32}], "scale": [{"x": 1.047, "y": 1.047, "curve": 0.35, "c2": 0.39, "c3": 0.757}, {"time": 0.9, "x": 1.172, "y": 1.172, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "x": 1.047, "y": 1.047}]}, "yun9": {"translate": [{"x": 2.09, "y": 20.78, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 0.5, "x": 0.58, "y": 5.8, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 3.16, "y": 31.44, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "x": 2.09, "y": 20.78}], "scale": [{"x": 1.114, "y": 1.114, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 0.5, "x": 1.032, "y": 1.032, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 1.172, "y": 1.172, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "x": 1.114, "y": 1.114}]}, "yun4": {"translate": [{"x": 13.94, "y": 1.31, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 13.94, "y": 1.31}], "scale": [{"x": 1.172, "y": 1.172, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 1.172, "y": 1.172}]}, "yun12": {"translate": [{"x": 10.75, "y": 6.29, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "x": 16.18, "y": 9.47, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": 10.75, "y": 6.29}], "scale": [{"x": 1.114, "y": 1.114, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "x": 1.172, "y": 1.172, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": 1.114, "y": 1.114}]}, "yun11": {"translate": [{"x": 8.66, "y": 5.54, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "x": 9.24, "y": 5.91, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5, "x": 7.54, "y": 4.82, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.5, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "x": 8.66, "y": 5.54}], "scale": [{"x": 1.161, "y": 1.161, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "x": 1.172, "y": 1.172, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5, "x": 1.14, "y": 1.14, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.5, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "x": 1.161, "y": 1.161}]}, "yun10": {"translate": [{"x": 8.7, "y": 5.56, "curve": 0.289, "c2": 0.18, "c3": 0.644, "c4": 0.59}, {"time": 0.5, "x": 4.62, "y": 2.96, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": 9.24, "y": 5.91, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.6667, "x": 8.7, "y": 5.56}], "scale": [{"x": 1.162, "y": 1.162, "curve": 0.289, "c2": 0.18, "c3": 0.644, "c4": 0.59}, {"time": 0.5, "x": 1.086, "y": 1.086, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": 1.172, "y": 1.172, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.6667, "x": 1.162, "y": 1.162}]}, "yun5": {"translate": [{"x": -2.02, "y": 6.97, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 0.1667, "x": -1.11, "y": 3.85, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": -6.03, "y": 20.87, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": -2.02, "y": 6.97}], "scale": [{"x": 1.057, "y": 1.057, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 0.1667, "x": 1.032, "y": 1.032, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 1.172, "y": 1.172, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": 1.057, "y": 1.057}]}, "yun3": {"translate": [{"x": -17.11, "y": 3.61, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": -20.98, "y": 4.43, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": -17.11, "y": 3.61}], "scale": [{"x": 1.14, "y": 1.14, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 1.172, "y": 1.172, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": 1.14, "y": 1.14}]}, "yun2": {"translate": [{"x": 3.57, "y": -7.52, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 7.14, "y": -15.05, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 3.57, "y": -7.52}], "scale": [{"x": 1.086, "y": 1.086, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.172, "y": 1.172, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 1.086, "y": 1.086}]}, "yun6": {"translate": [{"x": -6.21, "y": 2.25, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.1667, "x": -4.67, "y": 1.7, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": -9.34, "y": 3.39, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "x": -6.21, "y": 2.25}], "scale": [{"x": 1.114, "y": 1.114, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.1667, "x": 1.086, "y": 1.086, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 1.172, "y": 1.172, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "x": 1.114, "y": 1.114}]}, "yun14": {"translate": [{"x": 8.29, "y": 5.3, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.2333, "x": 9.24, "y": 5.91, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.9, "x": 4.62, "y": 2.96, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.5667, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 2.6667, "x": 8.29, "y": 5.3}], "scale": [{"x": 1.154, "y": 1.154, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.2333, "x": 1.172, "y": 1.172, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.9, "x": 1.086, "y": 1.086, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.5667, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 2.6667, "x": 1.154, "y": 1.154}]}, "yun1": {"translate": [{"x": 0.01, "y": -0.64, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.04, "y": -3.49, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 0.01, "y": -0.64}], "scale": [{"x": 1.032, "y": 1.032, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.172, "y": 1.172, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 1.032, "y": 1.032}]}, "logo9": {"scale": [{"time": 0.1, "x": -0.03, "y": -0.03, "curve": 0.25, "c3": 0.357, "c4": 3.25}, {"time": 0.5}]}, "logo10": {"translate": [{"x": 0.01, "y": 9.09, "curve": "stepped"}, {"time": 1.5667, "x": 0.01, "y": 9.09, "curve": 0.25, "c3": 0.75}, {"time": 1.9333}]}}}, "logo_loading_02": {"slots": {"logo/eye2": {"attachment": [{"time": 1.4333, "name": "logo/eye2"}, {"time": 1.5, "name": null}]}, "logo/eye1": {"attachment": [{"time": 1.4, "name": "logo/eye1"}, {"time": 1.5333, "name": null}]}}, "bones": {"niao4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": 139.99, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "niao6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "angle": -165.01, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "niao1": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -21.55, "y": -35.61, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 33.73, "y": 14.99, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "niao2": {"translate": [{"x": 15.28, "y": 37.44, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.1667, "x": -21.55, "y": -35.61, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": 18.74, "y": 45.91, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": 15.28, "y": 37.44}]}, "logo4": {"translate": [{"x": -5.7, "y": 26.59}]}, "yun16": {"translate": [{"x": 0.24, "y": 7.32, "curve": 0.35, "c2": 0.39, "c3": 0.757}, {"time": 0.9, "x": 0.88, "y": 26.76, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "x": 0.24, "y": 7.32}], "scale": [{"x": 1.047, "y": 1.047, "curve": 0.35, "c2": 0.39, "c3": 0.757}, {"time": 0.9, "x": 1.172, "y": 1.172, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "x": 1.047, "y": 1.047}]}, "yun15": {"translate": [{"x": -2.82, "y": 2.76, "curve": 0.38, "c2": 0.53, "c3": 0.744}, {"time": 0.5667, "x": -4.7, "y": 4.6, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.9, "x": -3.83, "y": 3.75, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.9, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 2.6667, "x": -2.82, "y": 2.76}], "scale": [{"x": 1.103, "y": 1.103, "curve": 0.38, "c2": 0.53, "c3": 0.744}, {"time": 0.5667, "x": 1.172, "y": 1.172, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.9, "x": 1.14, "y": 1.14, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.9, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 2.6667, "x": 1.103, "y": 1.103}]}, "yun14": {"translate": [{"x": 8.29, "y": 5.3, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.2333, "x": 9.24, "y": 5.91, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.9, "x": 4.62, "y": 2.96, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.5667, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 2.6667, "x": 8.29, "y": 5.3}], "scale": [{"x": 1.154, "y": 1.154, "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.2333, "x": 1.172, "y": 1.172, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.9, "x": 1.086, "y": 1.086, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.5667, "curve": 0.243, "c3": 0.687, "c4": 0.74}, {"time": 2.6667, "x": 1.154, "y": 1.154}]}, "yun12": {"translate": [{"x": 10.75, "y": 6.29, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "x": 16.18, "y": 9.47, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": 10.75, "y": 6.29}], "scale": [{"x": 1.114, "y": 1.114, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "x": 1.172, "y": 1.172, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": 1.114, "y": 1.114}]}, "yun11": {"translate": [{"x": 8.66, "y": 5.54, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "x": 9.24, "y": 5.91, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5, "x": 7.54, "y": 4.82, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.5, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "x": 8.66, "y": 5.54}], "scale": [{"x": 1.161, "y": 1.161, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "x": 1.172, "y": 1.172, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5, "x": 1.14, "y": 1.14, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.5, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "x": 1.161, "y": 1.161}]}, "yun10": {"translate": [{"x": 8.7, "y": 5.56, "curve": 0.289, "c2": 0.18, "c3": 0.644, "c4": 0.59}, {"time": 0.5, "x": 4.62, "y": 2.96, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": 9.24, "y": 5.91, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.6667, "x": 8.7, "y": 5.56}], "scale": [{"x": 1.162, "y": 1.162, "curve": 0.289, "c2": 0.18, "c3": 0.644, "c4": 0.59}, {"time": 0.5, "x": 1.086, "y": 1.086, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": 1.172, "y": 1.172, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.6667, "x": 1.162, "y": 1.162}]}, "yun9": {"translate": [{"x": 2.09, "y": 20.78, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 0.5, "x": 0.58, "y": 5.8, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 3.16, "y": 31.44, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "x": 2.09, "y": 20.78}], "scale": [{"x": 1.114, "y": 1.114, "curve": 0.333, "c2": 0.33, "c3": 0.688, "c4": 0.73}, {"time": 0.5, "x": 1.032, "y": 1.032, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 1.172, "y": 1.172, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "x": 1.114, "y": 1.114}]}, "yun8": {"translate": [{"x": 8.66, "y": 5.54, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "x": 9.24, "y": 5.91, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "x": 8.66, "y": 5.54}], "scale": [{"x": 1.161, "y": 1.161, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "x": 1.172, "y": 1.172, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "x": 1.161, "y": 1.161}]}, "yun7": {"translate": [{"x": -7.75, "y": 8.68, "curve": 0.312, "c2": 0.24, "c3": 0.648, "c4": 0.59}, {"time": 0.1667, "x": -6.72, "y": 7.52, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": -8.24, "y": 9.22, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.6667, "x": -7.75, "y": 8.68}], "scale": [{"x": 1.162, "y": 1.162, "curve": 0.312, "c2": 0.24, "c3": 0.648, "c4": 0.59}, {"time": 0.1667, "x": 1.14, "y": 1.14, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": 1.172, "y": 1.172, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.6667, "x": 1.162, "y": 1.162}]}, "yun6": {"translate": [{"x": -6.21, "y": 2.25, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.1667, "x": -4.67, "y": 1.7, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": -9.34, "y": 3.39, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "x": -6.21, "y": 2.25}], "scale": [{"x": 1.114, "y": 1.114, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.1667, "x": 1.086, "y": 1.086, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 1.172, "y": 1.172, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "x": 1.114, "y": 1.114}]}, "yun5": {"translate": [{"x": -2.02, "y": 6.97, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 0.1667, "x": -1.11, "y": 3.85, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": -6.03, "y": 20.87, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": -2.02, "y": 6.97}], "scale": [{"x": 1.057, "y": 1.057, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 0.1667, "x": 1.032, "y": 1.032, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 1.172, "y": 1.172, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": 1.057, "y": 1.057}]}, "yun4": {"translate": [{"x": 13.94, "y": 1.31, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 13.94, "y": 1.31}], "scale": [{"x": 1.172, "y": 1.172, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 1.172, "y": 1.172}]}, "yun3": {"translate": [{"x": -17.11, "y": 3.61, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": -20.98, "y": 4.43, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": -17.11, "y": 3.61}], "scale": [{"x": 1.14, "y": 1.14, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 1.172, "y": 1.172, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": 1.14, "y": 1.14}]}, "yun2": {"translate": [{"x": 3.57, "y": -7.52, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 7.14, "y": -15.05, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 3.57, "y": -7.52}], "scale": [{"x": 1.086, "y": 1.086, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.172, "y": 1.172, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 1.086, "y": 1.086}]}, "yun1": {"translate": [{"x": 0.01, "y": -0.64, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.04, "y": -3.49, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 0.01, "y": -0.64}], "scale": [{"x": 1.032, "y": 1.032, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.172, "y": 1.172, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 1.032, "y": 1.032}]}}}}}