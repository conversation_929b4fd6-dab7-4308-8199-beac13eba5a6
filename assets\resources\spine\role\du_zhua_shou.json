{"skeleton": {"hash": "dgNZEw+ofdKhzy3fOW+z94bz4fk=", "spine": "3.8.75", "x": -186.1, "y": -52.98, "width": 365, "height": 239}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 155.61, "rotation": 1.14, "x": -52.54, "y": -36.53}, {"name": "bone2", "parent": "bone", "length": 126.45, "rotation": 122.22, "x": 34.55, "y": 103.93}, {"name": "bone3", "parent": "bone2", "length": 32.57, "rotation": -95.76, "x": -2.04, "y": -8.47}, {"name": "bone4", "parent": "bone3", "length": 25.75, "rotation": 15.85, "x": 32.57}, {"name": "bone5", "parent": "bone4", "length": 10.19, "rotation": -39.76, "x": 25.75}, {"name": "bone6", "parent": "bone2", "length": 45.24, "rotation": 49.98, "x": 8.68, "y": 9.41}, {"name": "bone7", "parent": "bone6", "length": 34.19, "rotation": 34.7, "x": 48.82, "y": 0.42}, {"name": "bone8", "parent": "bone7", "length": 43.3, "rotation": -28.91, "x": 34.19}, {"name": "bone9", "parent": "bone8", "length": 28.08, "rotation": -74.93, "x": 43.3}, {"name": "bone10", "parent": "bone9", "length": 25.99, "rotation": -4.02, "x": 28.08}, {"name": "bone11", "parent": "bone4", "rotation": 47.31, "x": -15.48, "y": 46.17}, {"name": "bone12", "parent": "bone4", "rotation": 47.31, "x": 47.82, "y": -10.88}, {"name": "bone13", "parent": "bone11", "length": 53.76, "rotation": 142.36, "x": -2.31, "y": 1.11}, {"name": "bone14", "parent": "bone13", "length": 59.55, "rotation": 72.95, "x": 53.76}, {"name": "bone15", "parent": "bone14", "length": 36.69, "rotation": 39.79, "x": 59.55}, {"name": "bone16", "parent": "bone15", "length": 35.96, "rotation": -26.25, "x": 37.13, "y": 0.37}, {"name": "bone26", "parent": "bone14", "length": 24.39, "rotation": -1.09, "x": 62.95, "y": -1.3}, {"name": "bone17", "parent": "bone26", "length": 44.2, "rotation": -2.52, "x": 25.15, "y": 0.56}, {"name": "bone27", "parent": "bone14", "length": 23.25, "rotation": -56.77, "x": 63.22, "y": -5.07}, {"name": "bone18", "parent": "bone27", "length": 30.34, "rotation": 49.16, "x": 27.68, "y": 2.15}, {"name": "bone19", "parent": "bone12", "length": 37.51, "rotation": -155.66, "x": -7.93, "y": -1.91}, {"name": "bone20", "parent": "bone19", "length": 60.83, "rotation": 11.31, "x": 37.51}, {"name": "bone21", "parent": "bone20", "length": 29.26, "rotation": 32.12, "x": 61.55, "y": 2.51}, {"name": "bone22", "parent": "bone21", "length": 37.34, "rotation": -55.75, "x": 29.26}, {"name": "bone23", "parent": "bone21", "length": 32.88, "rotation": -53.98, "x": 47.9, "y": 8.64}, {"name": "bone24", "parent": "bone20", "length": 21.24, "rotation": -77.91, "x": 61.95, "y": -8.8}, {"name": "bone25", "parent": "bone24", "length": 22.03, "rotation": 79.16, "x": 21.24}, {"name": "bone30", "parent": "bone2", "length": 56.43, "rotation": -141.89, "x": -11.35, "y": -2.66}, {"name": "bone31", "parent": "bone30", "length": 37.03, "rotation": -145.34, "x": 56.43}, {"name": "bone32", "parent": "bone31", "length": 42.57, "rotation": 126.44, "x": 37.03}, {"name": "bone33", "parent": "bone5", "rotation": -4.69, "x": 27.12, "y": 1.87}, {"name": "bone34", "parent": "bone5", "rotation": -4.69, "x": 24.68, "y": -13.45}, {"name": "bone35", "parent": "bone5", "rotation": -4.69, "x": 20.07, "y": -31.54}, {"name": "target2", "parent": "bone", "rotation": -1.14, "x": 60.16, "y": 67.6}, {"name": "target3", "parent": "target2", "x": 33.73, "y": -25.79}, {"name": "bone28", "parent": "bone2", "length": 60.1, "rotation": 97.95, "x": 30.53, "y": 24.67}, {"name": "bone29", "parent": "bone28", "length": 44.56, "rotation": 74.47, "x": 60.1}, {"name": "bone36", "parent": "bone29", "length": 20.54, "rotation": 61.68, "x": 44.56}, {"name": "target1", "parent": "bone", "rotation": -1.14, "x": -30.39, "y": 38.02}, {"name": "target4", "parent": "bone", "rotation": -1.14, "x": -8.52, "y": 36.9}, {"name": "dg01", "parent": "root", "x": 77.03, "y": 110.33, "scaleX": 1.4381, "scaleY": 1.4381}, {"name": "dg1", "parent": "root", "x": 49.45, "y": 110.69, "scaleX": 1.4381, "scaleY": 1.8489}, {"name": "dg2", "parent": "root", "x": 160.01, "y": 103.26, "scaleX": 1.1225, "scaleY": 1.4381}, {"name": "baoji", "parent": "root", "x": 406.65, "y": 159.43}, {"name": "baoji2", "parent": "root", "rotation": 42.92, "x": 406.65, "y": 159.43}], "slots": [{"name": "duzhuaguai_01", "bone": "root", "attachment": "duzhuaguai_01"}, {"name": "duzhuaguai_02", "bone": "bone19", "attachment": "duzhuaguai_02"}, {"name": "duzhuaguai_03", "bone": "root", "attachment": "duzhuaguai_03"}, {"name": "duzhuaguai_04", "bone": "root", "attachment": "duzhuaguai_04"}, {"name": "duzhuaguai_05", "bone": "root", "attachment": "duzhuaguai_05"}, {"name": "duzhuaguai_06", "bone": "root", "attachment": "duzhuaguai_06"}, {"name": "duzhuaguai_07", "bone": "root", "attachment": "duzhuaguai_07"}, {"name": "biyan", "bone": "bone5"}, {"name": "duzhuaguai_09", "bone": "root", "attachment": "duzhuaguai_09"}, {"name": "duzhuaguai_010", "bone": "root", "attachment": "duzhuaguai_010"}, {"name": "dg/dg_00024", "bone": "dg01"}, {"name": "dg/dg_25", "bone": "dg2"}, {"name": "dg/dg_24", "bone": "dg1"}, {"name": "bd/1_00075", "bone": "baoji"}, {"name": "bd/1_75", "bone": "baoji2"}], "ik": [{"name": "target1", "order": 2, "bones": ["bone28", "bone29"], "target": "target1"}, {"name": "target2", "bones": ["bone30", "bone31"], "target": "target2", "bendPositive": false}, {"name": "target3", "order": 1, "bones": ["bone32"], "target": "target3"}, {"name": "target4", "order": 3, "bones": ["bone36"], "target": "target4"}], "transform": [{"name": "face", "order": 4, "bones": ["bone33"], "target": "bone34", "x": 1.18, "y": 15.47, "rotateMix": -1, "translateMix": -1, "scaleMix": -1, "shearMix": -1}], "skins": [{"name": "default", "attachments": {"dg/dg_00024": {"dg/dg_00024": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [115, -112, -115, -112, -115, 112, 115, 112], "hull": 4}, "dg/dg_00025": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [115, -112, -115, -112, -115, 112, 115, 112], "hull": 4}, "dg/dg_00026": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [115, -112, -115, -112, -115, 112, 115, 112], "hull": 4}, "dg/dg_00028": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [115, -112, -115, -112, -115, 112, 115, 112], "hull": 4}, "dg/dg_00030": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [115, -112, -115, -112, -115, 112, 115, 112], "hull": 4}, "dg/dg_00032": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [115, -112, -115, -112, -115, 112, 115, 112], "hull": 4}, "dg/dg_00034": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [115, -112, -115, -112, -115, 112, 115, 112], "hull": 4}, "dg/dg_00036": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [115, -112, -115, -112, -115, 112, 115, 112], "hull": 4}}, "dg/dg_25": {"dg/dg_00024": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [115, -112, -115, -112, -115, 112, 115, 112], "hull": 4}, "dg/dg_00025": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [115, -112, -115, -112, -115, 112, 115, 112], "hull": 4}, "dg/dg_00026": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [115, -112, -115, -112, -115, 112, 115, 112], "hull": 4}, "dg/dg_00028": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [115, -112, -115, -112, -115, 112, 115, 112], "hull": 4}, "dg/dg_00030": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [115, -112, -115, -112, -115, 112, 115, 112], "hull": 4}, "dg/dg_00032": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [115, -112, -115, -112, -115, 112, 115, 112], "hull": 4}, "dg/dg_00034": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [115, -112, -115, -112, -115, 112, 115, 112], "hull": 4}, "dg/dg_00036": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [115, -112, -115, -112, -115, 112, 115, 112], "hull": 4}}, "bd/1_75": {"bd/1_00075": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [154, -114, -154, -114, -154, 114, 154, 114], "hull": 4}, "bd/1_00076": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [154, -114, -154, -114, -154, 114, 154, 114], "hull": 4}, "bd/1_00077": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [154, -114, -154, -114, -154, 114, 154, 114], "hull": 4}, "bd/1_00079": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [154, -114, -154, -114, -154, 114, 154, 114], "hull": 4}, "bd/1_00081": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [154, -114, -154, -114, -154, 114, 154, 114], "hull": 4}, "bd/1_00083": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [154, -114, -154, -114, -154, 114, 154, 114], "hull": 4}}, "duzhuaguai_01": {"duzhuaguai_01": {"type": "mesh", "uvs": [0.99202, 0.29297, 0.99404, 0.51047, 0.918, 0.71973, 0.72838, 0.87956, 0.52254, 0.96853, 0.34712, 1, 0.18996, 0.94052, 0.0987, 0.71808, 0.11188, 0.45774, 0.16968, 0.33416, 0.10478, 0.2353, 0, 0.21717, 0.04293, 0.14797, 0.16258, 0.12161, 0.27513, 0.20399, 0.30048, 0.3457, 0.25992, 0.43467, 0.27209, 0.53024, 0.34408, 0.60933, 0.2944, 0.44456, 0.4181, 0.31768, 0.49821, 0, 0.71216, 0, 0.83079, 0.14632, 0.92915, 0.18587, 0.76255, 0.24518, 0.64188, 0.40666, 0.51007, 0.55496, 0.40461, 0.63899, 0.92633, 0.35206, 0.79496, 0.5127, 0.67139, 0.64797, 0.51271, 0.75999, 0.35923, 0.7917, 0.21745, 0.71984, 0.18493, 0.50847, 0.21875, 0.36897, 0.19924, 0.23793, 0.1173, 0.17663], "triangles": [35, 9, 36, 16, 36, 15, 9, 37, 36, 36, 14, 15, 36, 37, 14, 9, 10, 37, 10, 38, 37, 38, 13, 37, 37, 13, 14, 10, 11, 38, 11, 12, 38, 38, 12, 13, 6, 7, 34, 7, 35, 34, 34, 35, 17, 7, 8, 35, 35, 16, 17, 8, 9, 35, 35, 36, 16, 5, 33, 4, 5, 6, 33, 33, 32, 4, 4, 32, 3, 6, 34, 33, 34, 18, 33, 33, 28, 32, 33, 18, 28, 28, 27, 32, 34, 17, 18, 27, 28, 19, 28, 18, 19, 19, 20, 27, 32, 31, 3, 3, 31, 30, 32, 27, 31, 27, 26, 31, 31, 26, 30, 27, 20, 26, 20, 21, 26, 26, 22, 25, 26, 21, 22, 25, 22, 23, 3, 30, 2, 2, 30, 1, 26, 25, 30, 30, 29, 1, 30, 25, 29, 29, 0, 1, 25, 23, 29, 29, 24, 0, 29, 23, 24], "vertices": [2, 6, 14.61, -25.57, 0.96678, 7, -42.93, -1.89, 0.03322, 1, 6, 12.1, -6.6, 1, 2, 6, 20.77, 12.95, 0.97864, 7, -15.93, 26.28, 0.02136, 3, 6, 46.07, 30.07, 0.28013, 7, 14.61, 25.95, 0.7149, 8, -29.68, 13.25, 0.00497, 3, 6, 74.39, 41.26, 0.00161, 7, 44.27, 19.02, 0.26858, 8, -0.37, 21.53, 0.7298, 1, 8, 24.67, 24.67, 1, 2, 8, 47.22, 19.78, 0.86948, 9, -18.08, 8.93, 0.13052, 2, 8, 60.57, 0.41, 0.03766, 9, 4.1, 16.78, 0.96234, 2, 9, 25.84, 9.33, 0.94452, 10, -2.89, 9.15, 0.05548, 1, 10, 6.36, -0.9, 1, 1, 10, 16.56, 6.69, 1, 1, 10, 20.78, 21.16, 1, 1, 10, 25.69, 14.04, 1, 1, 10, 24.95, -3.21, 1, 2, 9, 41.77, -18.78, 0.03018, 10, 14.97, -17.77, 0.96982, 2, 9, 28.79, -19.23, 0.23864, 10, 2.05, -19.13, 0.76136, 3, 8, 37.89, -24.88, 0.01618, 9, 22.62, -11.69, 0.67163, 10, -4.63, -12.04, 0.31219, 4, 7, 57.75, -31.86, 0.00011, 8, 36.03, -16.5, 0.22954, 9, 14.04, -11.31, 0.75873, 10, -13.21, -12.27, 0.01162, 3, 7, 51.94, -20.87, 0.05045, 8, 25.63, -9.69, 0.88702, 9, 4.77, -19.59, 0.06253, 2, 7, 51.39, -37.01, 0.19328, 8, 32.95, -24.08, 0.80672, 2, 7, 30.53, -38.55, 0.52248, 8, 15.43, -35.52, 0.47752, 3, 6, 87.74, -42.99, 0.01683, 7, 7.28, -57.84, 0.82415, 8, 4.4, -63.64, 0.15902, 3, 6, 57.35, -46.54, 0.19505, 7, -19.73, -43.46, 0.74683, 8, -26.19, -64.11, 0.05812, 3, 6, 39.01, -35.72, 0.59145, 7, -28.65, -24.12, 0.3972, 8, -43.35, -51.49, 0.01136, 3, 6, 24.63, -33.89, 0.87551, 7, -39.42, -14.44, 0.12418, 8, -57.46, -48.22, 0.00031, 3, 6, 47.69, -25.94, 0.43908, 7, -15.94, -21.03, 0.5405, 8, -33.72, -42.64, 0.02042, 3, 6, 63.18, -9.83, 0.02614, 7, 5.97, -16.6, 0.90317, 8, -16.68, -28.17, 0.0707, 2, 7, 28.74, -13.94, 0.57112, 8, 1.97, -14.84, 0.42888, 3, 7, 45.53, -14.5, 0.06172, 8, 16.93, -7.21, 0.93707, 9, 0.11, -27.34, 0.00121, 2, 6, 23.34, -19.32, 0.95861, 7, -32.19, -1.72, 0.04139, 3, 6, 40.35, -3.1, 0.99844, 7, -8.97, 1.93, 0.00151, 8, -38.71, -19.17, 5e-05, 2, 6, 56.52, 10.77, 0.04722, 7, 12.22, 4.13, 0.95278, 3, 6, 77.92, 23.2, 6e-05, 7, 36.89, 2.16, 0.2023, 8, 1.32, 3.2, 0.79764, 1, 8, 23.22, 6.32, 1, 2, 8, 43.59, 0.3, 0.48721, 9, -0.22, 0.36, 0.51279, 1, 9, 18.95, 0.3, 1, 2, 9, 29.67, -7.4, 0.29849, 10, 2.1, -7.27, 0.70151, 2, 9, 41.53, -7.52, 0.00125, 10, 13.94, -6.56, 0.99875, 1, 10, 21.32, 4.02, 1], "hull": 25}}, "duzhuaguai_02": {"duzhuaguai_02": {"type": "mesh", "uvs": [0.1378, 0.0436, 0.32599, 0, 0.56336, 0.02643, 0.76009, 0.17058, 0.85419, 0.34047, 0.8328, 0.48805, 0.91834, 0.57042, 0.96752, 0.74031, 0.90123, 0.89304, 0.70663, 0.9634, 0.45429, 0.90677, 0.37763, 0.82118, 0.30674, 0.74203, 0.23526, 0.67082, 0.12069, 0.55669, 0, 0.28899, 0.00736, 0.14484, 0.27357, 0.25346, 0.45227, 0.48772, 0.64334, 0.70636, 0.73534, 0.81143, 0.53024, 0.57695], "triangles": [12, 13, 21, 9, 20, 8, 9, 10, 20, 10, 19, 20, 10, 11, 19, 8, 20, 7, 11, 12, 19, 20, 19, 7, 12, 21, 19, 19, 6, 7, 19, 5, 6, 19, 21, 5, 13, 18, 21, 13, 14, 18, 21, 18, 5, 14, 17, 18, 14, 15, 17, 5, 18, 4, 3, 18, 17, 18, 3, 4, 3, 17, 2, 2, 17, 1, 15, 16, 17, 16, 0, 17, 17, 0, 1], "vertices": [1, 21, -22.54, -9.71, 1, 1, 21, -20.55, 2.87, 1, 1, 21, -12.07, 15.93, 1, 1, 21, 3.93, 22.56, 1, 2, 21, 18.99, 22.26, 0.98318, 22, -13.8, 25.46, 0.01682, 2, 21, 29.22, 15.93, 0.888, 22, -5, 17.25, 0.112, 2, 21, 37.62, 18.13, 0.60233, 22, 3.67, 17.76, 0.39767, 2, 21, 51.44, 15.19, 0.0163, 22, 16.64, 12.17, 0.9837, 1, 22, 24.04, 1.35, 1, 2, 21, 60.61, -7.83, 6e-05, 22, 21.12, -12.21, 0.99994, 1, 22, 7.69, -22.69, 1, 1, 22, -0.85, -22.58, 1, 2, 21, 33.34, -23.76, 0.152, 22, -8.74, -22.48, 0.848, 2, 21, 26.15, -25.52, 0.52912, 22, -16.14, -22.8, 0.47088, 2, 21, 14.62, -28.34, 0.80149, 22, -28, -23.31, 0.19851, 1, 21, -8.34, -26.25, 1, 1, 21, -18.72, -20.86, 1, 1, 21, -3.41, -8.93, 1, 2, 21, 18.7, -6.46, 0.9997, 22, -19.71, -2.65, 0.0003, 2, 21, 40.01, -2.72, 0.05687, 22, 1.92, -3.16, 0.94313, 1, 22, 12.32, -3.4, 1, 2, 21, 27.4, -4.93, 0.61494, 22, -10.88, -2.86, 0.38506], "hull": 17}}, "duzhuaguai_03": {"duzhuaguai_03": {"type": "mesh", "uvs": [0.39384, 0.76262, 0.41913, 0.73625, 0.45707, 0.74431, 0.53925, 0.78754, 0.62706, 0.77142, 0.72049, 0.86301, 0.73735, 1, 0.77669, 0.99124, 0.84272, 0.874, 0.84624, 0.77215, 0.88347, 0.85862, 0.87644, 0.95827, 0.92616, 0.94924, 0.98704, 0.84195, 1, 0.74122, 0.96605, 0.56605, 0.89992, 0.54306, 0.80756, 0.42154, 0.77712, 0.44672, 0.78237, 0.54306, 0.74563, 0.48941, 0.67075, 0.46874, 0.60289, 0.45, 0.60184, 0.4916, 0.50068, 0.407, 0.48534, 0.39416, 0.47484, 0.37446, 0.40767, 0.16973, 0.33945, 0.09309, 0.36254, 0, 0.24498, 0.02083, 0.10959, 0.04382, 0.00463, 0.1544, 0, 0.29563, 0.08335, 0.3876, 0.26078, 0.50578, 0.28193, 0.52509, 0.28511, 0.55268, 0.28352, 0.59295, 0.26183, 0.63102, 0.19995, 0.58137, 0.1899, 0.61447, 0.20138, 0.6868, 0.21506, 0.79705, 0.32147, 0.89206, 0.47214, 0.92314, 0.54024, 0.9036, 0.52151, 0.87608, 0.45852, 0.85299, 0.40148, 0.79705, 0.3751, 0.64965, 0.30019, 0.70559, 0.32062, 0.81747, 0.42532, 0.87519, 0.61929, 0.56708, 0.57672, 0.65143, 0.58609, 0.7269, 0.82018, 0.66386, 0.74272, 0.5653, 0.78118, 0.61425, 0.71377, 0.65765, 0.77847, 0.79439, 0.77506, 0.90982, 0.89424, 0.64255, 0.92743, 0.76331, 0.92658, 0.85921, 0.154, 0.16386, 0.40206, 0.46136, 0.46557, 0.57136, 0.47276, 0.6626], "triangles": [19, 18, 17, 19, 17, 16, 63, 16, 15, 16, 63, 19, 63, 15, 14, 64, 63, 14, 9, 63, 64, 13, 64, 14, 10, 9, 64, 65, 10, 64, 13, 65, 64, 65, 11, 10, 12, 65, 13, 12, 11, 65, 19, 63, 59, 59, 58, 19, 60, 58, 59, 56, 55, 60, 4, 56, 60, 3, 56, 4, 57, 61, 60, 57, 60, 59, 61, 57, 9, 4, 60, 61, 5, 4, 61, 8, 61, 9, 62, 5, 61, 8, 62, 61, 7, 62, 8, 6, 5, 62, 6, 62, 7, 57, 59, 63, 9, 57, 63, 23, 22, 21, 58, 21, 20, 58, 20, 19, 54, 23, 21, 54, 21, 58, 68, 24, 23, 68, 23, 54, 55, 68, 54, 60, 54, 58, 55, 54, 60, 69, 68, 55, 56, 69, 55, 52, 51, 0, 52, 0, 49, 43, 51, 52, 53, 49, 48, 52, 49, 53, 44, 52, 53, 43, 52, 44, 53, 48, 47, 46, 53, 47, 45, 53, 46, 44, 53, 45, 50, 38, 37, 41, 40, 39, 68, 50, 37, 39, 38, 50, 50, 68, 69, 42, 41, 39, 51, 39, 50, 42, 39, 51, 1, 50, 69, 0, 51, 50, 2, 1, 69, 1, 0, 50, 69, 56, 2, 3, 2, 56, 43, 42, 51, 28, 30, 29, 66, 31, 30, 66, 30, 28, 32, 31, 66, 33, 32, 66, 34, 33, 66, 27, 66, 28, 67, 27, 26, 67, 26, 25, 67, 66, 27, 35, 34, 66, 67, 35, 66, 36, 35, 67, 37, 36, 67, 24, 68, 67, 24, 67, 25, 37, 67, 68], "vertices": [4, 26, 17, 13.37, 0.22825, 27, 12.33, 6.68, 0.73566, 23, 1.06, -30.35, 0.02397, 24, 9.21, -40.4, 0.01212, 4, 26, 12.68, 13.63, 0.51393, 27, 11.78, 10.98, 0.33692, 23, 2.78, -26.38, 0.09912, 24, 6.9, -36.74, 0.05003, 4, 26, 10.34, 17.69, 0.51075, 27, 15.32, 14.04, 0.13567, 23, 7.4, -25.57, 0.22161, 24, 8.83, -32.47, 0.13197, 4, 26, 7.51, 28.46, 0.29112, 27, 25.37, 18.85, 0.03339, 23, 18.49, -26.6, 0.32111, 24, 15.92, -23.88, 0.35439, 4, 26, -0.94, 35.18, 0.09179, 27, 30.38, 28.4, 0.00602, 23, 27.69, -20.97, 0.18642, 24, 16.45, -13.1, 0.71578, 3, 26, -0.47, 50.69, 0.00028, 23, 42.1, -26.72, 0.00014, 24, 29.31, -4.43, 0.99958, 1, 24, 45.26, -5.95, 1, 1, 24, 45.32, -1.09, 1, 1, 24, 33.82, 9.71, 1, 1, 24, 22.4, 12.74, 1, 2, 24, 33.18, 14.92, 0.00319, 25, 29.65, -6.27, 0.99681, 1, 25, 40.63, -9.99, 1, 1, 25, 41.13, -3.91, 1, 1, 25, 30.93, 6.35, 1, 1, 25, 20.02, 10.8, 1, 1, 25, -0.68, 11.93, 1, 2, 23, 48.72, 15.77, 0.00696, 25, -5.28, 4.86, 0.99304, 2, 23, 33.16, 24.8, 0.04502, 25, -21.73, -2.42, 0.95498, 3, 23, 30.8, 20.73, 0.05002, 24, -16.27, 12.94, 0.00486, 25, -19.83, -6.72, 0.94512, 3, 23, 35.48, 10.56, 0.34475, 24, -5.23, 11.09, 0.23125, 25, -8.85, -8.91, 0.424, 3, 23, 29.07, 14.73, 0.80198, 24, -12.28, 8.13, 0.06643, 25, -15.99, -11.65, 0.13159, 2, 23, 19.76, 13.64, 0.98362, 25, -20.59, -19.82, 0.01638, 1, 23, 11.32, 12.66, 1, 1, 23, 12.97, 8.12, 1, 2, 22, 53.05, 12.27, 0.45832, 23, -2.01, 12.78, 0.54168, 2, 22, 50.75, 11.65, 0.63444, 23, -4.28, 13.48, 0.36556, 2, 22, 48.16, 11.99, 0.79043, 23, -6.3, 15.14, 0.20957, 1, 22, 24.22, 19.54, 1, 1, 22, 12.17, 18.18, 1, 1, 22, 5.13, 26.83, 1, 1, 22, -1.36, 13.95, 1, 1, 22, -8.94, -0.81, 1, 1, 22, -6.16, -18.65, 1, 1, 22, 6.7, -28.82, 1, 2, 22, 21.27, -27.04, 0.99347, 26, 9.31, -43.6, 0.00653, 1, 22, 45.04, -17.9, 1, 2, 22, 48.37, -17.17, 0.66266, 26, 5.33, -15.03, 0.33734, 2, 22, 51.17, -18.76, 0.32844, 26, 7.48, -12.62, 0.67156, 2, 22, 54.82, -21.69, 0.11053, 26, 11.1, -9.67, 0.88947, 3, 22, 56.81, -26.42, 0.01051, 26, 16.15, -8.71, 0.97514, 27, -9.51, 3.37, 0.01435, 1, 26, 16.79, -18.13, 1, 2, 26, 20.48, -16.5, 0.98498, 27, -16.35, -2.35, 0.01502, 2, 26, 25.84, -9.9, 0.70545, 27, -8.86, -6.38, 0.29455, 2, 26, 34.32, -0.19, 0.02401, 27, 2.27, -12.88, 0.97599, 1, 27, 18.87, -9.42, 1, 1, 27, 32.86, 2.81, 1, 1, 27, 36.1, 10.71, 1, 1, 27, 32.19, 10.87, 1, 1, 27, 25.41, 6.47, 1, 4, 26, 19.38, 16.71, 0.03455, 27, 16.06, 4.98, 0.96063, 23, 3.38, -33.72, 0.00315, 24, 13.31, -40.38, 0.00166, 4, 26, 8.69, 2.99, 0.93419, 27, 0.57, 12.9, 0.0439, 23, -5.85, -18.98, 0.01595, 24, -4.07, -39.71, 0.00595, 4, 26, 19.55, 0.5, 0.81393, 27, 0.17, 1.76, 0.18592, 23, -11.91, -28.34, 9e-05, 24, 0.25, -49.98, 5e-05, 1, 27, 11.95, -4.22, 1, 1, 27, 24.99, 1.72, 1, 2, 23, 18.14, 0.75, 0.99995, 25, -11.11, -28.71, 5e-05, 5, 22, 81.33, 2.84, 0.00355, 26, -7.32, 21.4, 0.14074, 27, 15.64, 32.08, 0.00834, 23, 16.93, -10.24, 0.63103, 24, 1.52, -15.96, 0.21634, 4, 26, -1.52, 28.05, 0.18, 27, 23.27, 27.63, 0.01461, 23, 21.19, -17.98, 0.34712, 24, 10.31, -16.79, 0.45827, 2, 23, 44.87, -0.81, 0.00282, 24, 9.45, 12.45, 0.99718, 2, 23, 31.96, 6.4, 0.6422, 24, -3.77, 5.84, 0.3578, 3, 23, 38.37, 2.82, 0.11676, 24, 2.79, 9.12, 0.87524, 25, -0.89, -11.12, 0.008, 4, 26, -17.77, 34.29, 0.00012, 27, 26.35, 44.77, 0, 24, 5.9, 0.05, 0.99769, 25, 1.93, -20.28, 0.00219, 2, 24, 23.1, 4.17, 0.91584, 25, 19.25, -16.69, 0.08417, 2, 24, 36.06, 0.81, 0.99941, 25, 32.11, -20.46, 0.00059, 1, 25, 5.72, 1.29, 1, 1, 25, 20.29, 1.66, 1, 2, 24, 34.4, 19.99, 5e-05, 25, 31.03, -1.24, 0.99995, 1, 22, 5.46, -4.75, 1, 2, 22, 51.04, -1.08, 0.97506, 26, -9.84, -9.04, 0.02494, 5, 22, 65.87, -2.47, 0.38171, 26, -5.37, 5.17, 0.35442, 27, 0.07, 27.11, 0.00288, 23, 1.01, -6.52, 0.25307, 24, -10.51, -27.02, 0.00792, 5, 22, 74.91, -8.05, 0.02123, 26, 1.98, 12.83, 0.55872, 27, 8.98, 21.33, 0.05512, 23, 5.7, -16.05, 0.28215, 24, 0.01, -28.52, 0.08278], "hull": 50}}, "duzhuaguai_04": {"duzhuaguai_04": {"type": "mesh", "uvs": [0.35724, 0.44648, 0.17378, 0.43947, 0.03113, 0.38287, 0, 0.28583, 0.02701, 0.1514, 0.14987, 0.05033, 0.31231, 0, 0.51845, 0.01798, 0.69937, 0.10515, 0.82837, 0.2596, 0.83837, 0.41405, 0.74137, 0.5256, 0.63794, 0.54678, 0.59461, 0.54227, 0.61601, 0.58571, 0.71074, 0.69882, 0.84633, 0.72991, 0.86393, 0.68169, 0.88877, 0.69311, 0.90637, 0.75782, 1, 0.85585, 0.96736, 0.97614, 0.84231, 0.99743, 0.63996, 0.96763, 0.58352, 0.88247, 0.50102, 0.76005, 0.37076, 0.61102, 0.30302, 0.49924, 0.32039, 0.46411, 0.48712, 0.5035, 0.18057, 0.25866, 0.45586, 0.25547, 0.64952, 0.38428, 0.4741, 0.41195, 0.44978, 0.54395, 0.55747, 0.68234, 0.68425, 0.78453, 0.82754, 0.88034], "triangles": [20, 21, 37, 19, 37, 16, 16, 18, 19, 16, 17, 18, 23, 37, 22, 37, 21, 22, 20, 37, 19, 23, 36, 37, 23, 24, 36, 25, 35, 24, 24, 35, 36, 37, 36, 16, 16, 36, 15, 36, 35, 15, 26, 34, 25, 25, 34, 35, 35, 14, 15, 34, 29, 35, 35, 13, 14, 27, 28, 26, 26, 28, 34, 28, 0, 34, 35, 29, 13, 11, 12, 32, 34, 0, 29, 12, 13, 32, 13, 29, 32, 0, 33, 29, 29, 33, 32, 11, 32, 10, 1, 30, 0, 0, 31, 33, 0, 30, 31, 1, 2, 30, 32, 9, 10, 33, 31, 32, 32, 8, 9, 32, 31, 8, 2, 3, 30, 3, 4, 30, 4, 5, 30, 30, 6, 31, 30, 5, 6, 31, 7, 8, 31, 6, 7], "vertices": [3, 28, 19.36, -16.26, 0.30307, 29, 39.83, -7.25, 0.57427, 30, -7.47, 2.14, 0.12267, 2, 28, -0.68, -22.26, 0.92245, 29, 59.8, -13.49, 0.07755, 2, 28, -17.78, -22.41, 0.98892, 29, 74.05, -22.92, 0.01108, 2, 28, -24, -14.97, 0.99693, 29, 75.06, -32.56, 0.00307, 1, 28, -25.03, -2.14, 1, 1, 28, -14.72, 11.21, 1, 1, 28, 1.36, 21.51, 1, 1, 28, 24.18, 27.36, 1, 1, 28, 46.31, 26.2, 1, 2, 28, 64.81, 17.23, 0.98966, 29, -16.58, -9.6, 0.01034, 2, 28, 70.44, 3.96, 0.70418, 29, -13.82, 4.55, 0.29582, 2, 28, 63.23, -9.38, 0.05053, 29, -0.39, 11.58, 0.94947, 2, 29, 11.5, 10.31, 0.92661, 30, 23.62, 14.17, 0.07339, 2, 29, 16.14, 8.59, 0.65822, 30, 19.44, 11.5, 0.34178, 2, 29, 14.88, 13.13, 0.21527, 30, 23.83, 9.77, 0.78473, 1, 30, 38.8, 7.98, 1, 1, 30, 52.83, 15.07, 1, 1, 30, 51.7, 19.85, 1, 1, 30, 54.6, 20.73, 1, 1, 30, 59.85, 17.17, 1, 1, 30, 73.86, 16.41, 1, 1, 30, 77.7, 5.26, 1, 1, 30, 67.58, -4.97, 1, 1, 30, 47.57, -16.78, 1, 1, 30, 37.65, -14.4, 1, 1, 30, 23.26, -11.07, 1, 2, 29, 42.44, 7.9, 0.00185, 30, 3.05, -9.08, 0.99815, 3, 28, 15.05, -22.87, 0.03004, 29, 47.1, -4.18, 0.4572, 30, -9.4, -5.51, 0.51277, 3, 28, 15.9, -19.14, 0.06873, 29, 44.31, -6.8, 0.51112, 30, -9.81, -1.72, 0.42015, 2, 29, 26.98, 1.83, 0.86196, 30, 7.51, 6.92, 0.13804, 2, 28, -5.27, -6.06, 0.98692, 29, 54.55, -29.48, 0.01308, 1, 28, 24.4, 4.15, 1, 2, 28, 49.14, -0.23, 0.96557, 29, 6.17, -3.89, 0.03443, 2, 28, 30.98, -9, 0.36958, 29, 26.13, -6.77, 0.63042, 2, 29, 32.09, 4.31, 0.18014, 30, 6.42, 1.35, 0.81986, 1, 30, 23.99, -1.42, 1, 1, 30, 41.24, -0.19, 1, 1, 30, 59.63, 2.66, 1], "hull": 29}}, "duzhuaguai_05": {"duzhuaguai_05": {"type": "mesh", "uvs": [0.32957, 0.71185, 0.50467, 0.71538, 0.64013, 0.688, 0.69117, 0.6638, 0.791, 0.61646, 0.8802, 0.53874, 0.9617, 0.40713, 0.95949, 0.24903, 0.84606, 0.14392, 0.80201, 0.00172, 0.65114, 0.11389, 0.54432, 0.01674, 0.50577, 0.10329, 0.42183, 0.00315, 0.39413, 1e-05, 0.29763, 0.12273, 0.29263, 0.21775, 0.15667, 0.17042, 0.19191, 0.32411, 0.08289, 0.40272, 0, 0.47602, 0.0036, 0.55993, 0.0906, 0.63148, 0.20403, 0.69242, 0.7029, 0.54757, 0.41106, 0.50429, 0.35049, 0.60056, 0.67537, 0.60851, 0.60489, 0.65886, 0.45181, 0.65709, 0.48815, 0.58466, 0.38237, 0.54625, 0.51488, 0.50441, 0.269, 0.64737, 0.28992, 0.43628, 0.21173, 0.52019, 0.11813, 0.56523, 0.42428, 0.36827, 0.58176, 0.34884, 0.78219, 0.36209, 0.8813, 0.38152, 0.74475, 0.46189, 0.86148, 0.46719, 0.56634, 0.43186, 0.42097, 0.43628, 0.33508, 0.48839, 0.27671, 0.55463, 0.19632, 0.59791, 0.80201, 0.25433, 0.56304, 0.20398, 0.38794, 0.26934, 0.23706, 0.35502, 0.14456, 0.44511, 0.05866, 0.49811, 0.75141, 0.12151, 0.70726, 0.19787, 0.58032, 0.14364, 0.42579, 0.12151, 0.46856, 0.20229, 0.34301, 0.1868, 0.36232, 0.22, 0.25194, 0.2698, 0.30372, 0.31717, 0.33335, 0.23881], "triangles": [54, 10, 9, 54, 9, 8, 8, 55, 54, 48, 8, 7, 48, 55, 8, 40, 48, 7, 40, 7, 6, 42, 40, 6, 56, 12, 11, 57, 13, 12, 14, 13, 57, 15, 14, 57, 10, 56, 11, 59, 15, 57, 55, 10, 54, 58, 57, 12, 58, 12, 56, 59, 57, 58, 49, 58, 56, 16, 15, 59, 60, 59, 58, 63, 16, 59, 60, 63, 59, 56, 10, 55, 49, 56, 55, 49, 55, 48, 50, 60, 58, 63, 60, 50, 50, 58, 49, 61, 17, 16, 61, 16, 63, 62, 61, 63, 62, 63, 50, 18, 17, 61, 51, 18, 61, 38, 49, 48, 50, 49, 38, 62, 51, 61, 37, 50, 38, 62, 50, 37, 43, 37, 38, 37, 34, 62, 44, 37, 43, 34, 51, 62, 44, 34, 37, 52, 18, 51, 52, 51, 34, 45, 34, 44, 25, 45, 44, 39, 38, 48, 39, 48, 40, 42, 39, 40, 41, 39, 42, 5, 42, 6, 41, 38, 39, 43, 38, 41, 24, 43, 41, 5, 24, 41, 5, 41, 42, 4, 24, 5, 32, 44, 43, 25, 44, 32, 32, 43, 24, 30, 25, 32, 27, 30, 32, 31, 25, 30, 24, 27, 32, 27, 24, 4, 29, 26, 30, 28, 30, 27, 29, 30, 28, 3, 27, 4, 28, 27, 3, 2, 28, 3, 1, 29, 28, 0, 29, 1, 1, 28, 2, 21, 20, 53, 53, 20, 19, 53, 19, 52, 35, 52, 34, 35, 34, 45, 36, 53, 52, 31, 45, 25, 46, 35, 45, 46, 45, 31, 35, 36, 52, 21, 53, 36, 47, 36, 35, 47, 35, 46, 26, 46, 31, 22, 21, 36, 22, 36, 47, 33, 47, 46, 33, 46, 26, 30, 26, 31, 23, 47, 33, 22, 47, 23, 0, 33, 26, 0, 26, 29, 23, 33, 0, 52, 19, 18], "vertices": [2, 6, -1.5, 24.35, 0.81688, 3, -31.33, -12.1, 0.18312, 2, 6, -29.06, 21.83, 0.12731, 3, -7.14, -25.53, 0.87269, 4, 6, -49.7, 13.99, 0.00026, 3, 14.33, -30.66, 0.9274, 4, -25.93, -24.51, 0.06583, 5, -24.05, -51.9, 0.00651, 3, 3, 23.68, -30.17, 0.78795, 4, -16.79, -26.6, 0.18526, 5, -15.69, -47.66, 0.02678, 3, 3, 41.98, -29.22, 0.31583, 4, 1.07, -30.68, 0.52913, 5, 0.65, -39.37, 0.15503, 4, 3, 61.57, -22.17, 0.02446, 4, 21.84, -29.25, 0.45077, 5, 15.7, -24.99, 0.50628, 12, -31.12, 6.64, 0.01849, 3, 4, 49.01, -19.29, 0.00186, 5, 30.22, 0.05, 0.07811, 12, -5.37, -6.58, 0.92003, 4, 4, 70.18, 3.56, 0.00737, 5, 31.87, 31.15, 0.14656, 11, 26.75, -91.85, 0.06061, 12, 25.78, -6.65, 0.78547, 4, 4, 71.41, 30.92, 0.26541, 5, 15.32, 52.97, 0.20149, 11, 47.7, -74.21, 0.16199, 12, 46.72, 11, 0.37111, 4, 4, 85.62, 56.04, 0.27699, 5, 10.18, 81.37, 0.19987, 11, 75.8, -67.62, 0.20058, 12, 74.83, 17.58, 0.32257, 4, 4, 53.12, 56.4, 0.13504, 5, -15.03, 60.86, 0.20011, 11, 54.02, -43.49, 0.41099, 12, 53.05, 41.71, 0.25387, 5, 3, 62.17, 93.54, 0.23809, 4, 54.03, 81.9, 0.144, 5, -30.64, 81.04, 0.10438, 11, 73.39, -26.87, 0.40267, 12, 72.42, 58.33, 0.11087, 6, 6, -15.25, -97.96, 0.06163, 3, 48.88, 81.25, 0.21897, 4, 37.88, 73.71, 0.144, 5, -37.82, 64.42, 0.07731, 11, 56.42, -20.56, 0.42095, 12, 55.45, 64.65, 0.07714, 6, 6, 0.21, -116.02, 0.09587, 3, 46.26, 104.88, 0.20398, 4, 41.82, 97.15, 0.144, 5, -49.78, 84.96, 0.0466, 11, 76.32, -7.56, 0.47028, 12, 75.35, 77.65, 0.03927, 6, 6, 4.63, -116.12, 0.09587, 3, 42.67, 107.46, 0.20393, 4, 39.07, 100.61, 0.144, 5, -54.11, 85.86, 0.04639, 11, 77, -3.19, 0.47078, 12, 76.03, 82.01, 0.03902, 6, 6, 16.97, -90.34, 0.25338, 3, 17.96, 93.1, 0.24941, 4, 11.38, 93.55, 0.144, 5, -70.88, 62.72, 0.02354, 11, 53.03, 12.38, 0.31033, 12, 52.06, 97.58, 0.01934, 6, 6, 15.58, -71.66, 0.26717, 7, -68.36, -40.33, 0.00013, 3, 8.58, 76.87, 0.112, 5, -72.88, 44.09, 0.01774, 11, 34.32, 13.42, 0.58934, 12, 33.35, 98.62, 0.01361, 5, 6, 38, -78.43, 0.27232, 3, -6.13, 95.09, 0.08, 5, -93.71, 54.77, 0.00217, 11, 43.93, 34.77, 0.6455, 12, 42.96, 119.98, 2e-05, 6, 6, 28.96, -49, 0.18113, 7, -44.47, -29.32, 0.01738, 4, -28.03, 76.23, 0.00025, 5, -90.11, 24.2, 0.00131, 11, 13.59, 29.61, 0.79953, 12, 12.61, 114.82, 0.0004, 3, 6, 44.27, -31.62, 0.8436, 7, -21.98, -23.75, 0.05189, 11, -1.67, 47.04, 0.1045, 3, 6, 55.61, -15.76, 0.44155, 7, -3.63, -17.16, 0.49096, 11, -15.93, 60.33, 0.06748, 2, 7, 3.63, -2.3, 0.99928, 11, -32.47, 59.98, 0.00072, 1, 6, 37.84, 13, 1, 2, 6, 18.64, 22.85, 0.99845, 3, -47.14, 0.49, 0.00155, 3, 3, 35.93, -10.74, 0.36217, 4, 0.3, -11.25, 0.58259, 5, -12.37, -24.93, 0.05524, 4, 6, -9.55, -17.76, 0.27201, 3, -0.98, 18.18, 0.41405, 4, -27.31, 26.65, 0.00262, 11, -22.37, -4.54, 0.31132, 2, 6, -2.25, 2.19, 0.92588, 3, -18.25, 5.8, 0.07412, 3, 3, 26.52, -19.36, 0.74481, 4, -11.11, -16.97, 0.2242, 5, -17.48, -36.63, 0.03099, 4, 6, -43.5, 8.93, 0.00159, 3, 12.05, -22.99, 0.95832, 4, -26.02, -16.51, 0.037, 5, -29.24, -45.81, 0.00309, 2, 6, -19.44, 11.39, 0.2516, 3, -9.22, -11.48, 0.7484, 1, 3, 2.48, -1.5, 1, 3, 6, -6.01, -9.02, 0.56725, 3, -8.83, 12.95, 0.30871, 11, -30.57, 0.11, 0.12404, 4, 6, -25.84, -19.64, 0.01286, 3, 13.54, 10.56, 0.81533, 4, -15.42, 15.35, 0.0286, 11, -22.61, -20.94, 0.14321, 2, 6, 9.47, 12.84, 0.98495, 3, -33.93, 3.6, 0.01505, 4, 6, 11.01, -28.85, 0.35928, 7, -47.75, -2.54, 0.00517, 3, -11.74, 38.92, 0.00843, 11, -8.72, 14.42, 0.62713, 3, 6, 21.37, -11, 0.86834, 7, -29.07, 6.25, 0.00422, 11, -25.08, 27, 0.12744, 3, 6, 35.03, -0.47, 0.9983, 7, -11.85, 7.13, 0.0006, 11, -33.76, 41.9, 0.0011, 4, 3, 13.28, 40.96, 0.04444, 5, -54.03, 13.16, 0.00946, 11, 4.4, -6.98, 0.93891, 12, 3.42, 78.22, 0.00719, 5, 3, 37.11, 32.82, 0.09208, 4, 13.33, 30.34, 0.38886, 5, -28.95, 15.37, 0.06727, 11, 7.89, -31.91, 0.40893, 12, 6.92, 53.29, 0.04286, 4, 4, 34.53, 6.66, 0.14218, 5, 2.48, 10.73, 0.65945, 11, 4.86, -63.54, 0.04434, 12, 3.89, 21.66, 0.15404, 3, 5, 17.86, 5.9, 0.37653, 11, 0.82, -79.15, 0.00348, 12, -0.15, 6.05, 0.62, 2, 4, 16.71, -3.54, 0.92287, 5, -4.69, -8.51, 0.07713, 4, 3, 65.47, -8.31, 0.0001, 4, 29.38, -16.99, 0.20948, 5, 13.65, -10.74, 0.70286, 12, -16.99, 9.41, 0.08756, 5, 3, 27.37, 19.46, 0.39553, 4, 0.31, 20.14, 0.32402, 5, -32.43, -0.79, 0.00441, 11, -8.43, -29.26, 0.27191, 12, -9.4, 55.95, 0.00414, 6, 6, -9.55, -31.25, 0.06671, 3, 6.61, 29.33, 0.20941, 4, -16.96, 35.3, 0.02562, 5, -55.41, -0.18, 0.00017, 11, -8.99, -6.28, 0.6979, 12, -9.97, 78.92, 0.00018, 4, 6, 2.74, -19.48, 0.498, 7, -49.22, 9.88, 0.00014, 3, -10.17, 26.52, 0.09217, 11, -19.08, 7.43, 0.4097, 3, 6, 10.38, -5.45, 0.94482, 3, -24.39, 19.22, 0.00116, 11, -32, 16.82, 0.05402, 1, 6, 22.01, 4.49, 1, 4, 4, 51.4, 19.92, 0.09382, 5, 6.98, 31.71, 0.31635, 11, 26.04, -66.96, 0.1405, 12, 25.07, 18.25, 0.44933, 5, 3, 47.7, 59.48, 0.00179, 4, 30.81, 53.09, 0.072, 5, -30.07, 44.04, 0.15861, 11, 36.46, -29.34, 0.61486, 12, 35.49, 55.87, 0.15274, 6, 6, -0.55, -63.31, 0.00041, 7, -76.87, -24.28, 2e-05, 3, 17.22, 60.89, 0.20043, 5, -58.5, 32.97, 0.03025, 11, 23.96, -1.5, 0.74464, 12, 22.99, 83.7, 0.02425, 6, 6, 21.17, -43.78, 0.20506, 7, -47.9, -20.59, 0.01563, 4, -27.04, 66.91, 0.00015, 5, -83.38, 17.67, 0.00075, 11, 7.4, 22.56, 0.77812, 12, 6.43, 107.76, 0.00029, 3, 6, 33.63, -24.46, 0.61581, 7, -26.66, -11.8, 0.08687, 11, -10.15, 37.41, 0.29732, 3, 6, 45.89, -12.51, 0.6022, 7, -9.77, -8.96, 0.33188, 11, -20.41, 51.12, 0.06592, 4, 4, 63.59, 44.41, 0.29239, 5, 0.68, 58.34, 0.19465, 11, 52.31, -59.31, 0.21271, 12, 51.34, 25.89, 0.30025, 4, 4, 48.18, 38.29, 0.15912, 5, -7.25, 43.78, 0.24547, 11, 37.36, -52.14, 0.29536, 12, 36.39, 33.07, 0.30005, 5, 3, 55.63, 68.75, 0.23619, 4, 40.97, 59.84, 0.16, 5, -26.57, 55.73, 0.11114, 11, 48.31, -32.23, 0.37249, 12, 47.34, 52.98, 0.12018, 6, 6, -3.11, -92.93, 0.09587, 3, 36.01, 83.93, 0.20533, 4, 26.24, 79.79, 0.144, 5, -50.66, 61.65, 0.0502, 11, 53, -7.87, 0.46007, 12, 52.03, 77.33, 0.04453, 6, 6, -11.67, -77.91, 0.08196, 3, 34.63, 66.69, 0.1444, 4, 20.2, 63.59, 0.12934, 5, -44.94, 45.34, 0.06357, 11, 37, -14.42, 0.52385, 12, 36.02, 70.79, 0.05688, 6, 6, 8.39, -78.64, 0.25338, 3, 18.46, 78.59, 0.15867, 4, 7.9, 79.45, 0.144, 5, -64.54, 49.66, 0.02436, 11, 40.31, 5.38, 0.39968, 12, 39.34, 90.58, 0.01992, 7, 6, 4.6, -72.49, 0.20893, 7, -77.87, -34.76, 0, 3, 18.14, 71.38, 0.176, 4, 5.62, 72.61, 0.11866, 5, -61.92, 42.94, 0.02385, 11, 33.73, 2.42, 0.45313, 12, 32.76, 87.62, 0.01943, 6, 6, 20.78, -60.73, 0.30682, 7, -57.87, -34.3, 0.00166, 3, -1.86, 70.76, 0.08, 5, -79.95, 34.27, 0.00551, 11, 24.16, 19.98, 0.60256, 12, 23.19, 105.19, 0.00345, 7, 6, 11.57, -52.41, 0.32256, 7, -60.7, -22.22, 0.00187, 3, 1.07, 58.7, 0.064, 4, -14.27, 65.08, 0.00733, 5, -72.39, 24.43, 0.00478, 11, 14.72, 11.93, 0.59613, 12, 13.74, 97.13, 0.00334, 6, 6, 8.71, -68.28, 0.23916, 7, -72.09, -33.65, 7e-05, 3, 12.36, 70.21, 0.2, 5, -66.72, 39.53, 0.01878, 11, 30.09, 7.04, 0.52721, 12, 29.12, 92.25, 0.01478], "hull": 24}}, "duzhuaguai_06": {"duzhuaguai_06": {"type": "mesh", "uvs": [0.04107, 0.3979, 0.051, 0.35101, 0.11458, 0.17087, 0.35303, 0.03021, 0.67294, 0.00183, 0.92926, 0.11041, 0.97496, 0.25724, 0.85574, 0.43985, 0.63916, 0.55337, 0.49212, 0.58792, 0.54884, 0.65688, 0.56442, 0.70434, 0.6352, 0.73423, 0.672, 0.70434, 0.76826, 0.7984, 0.78242, 0.93025, 0.68899, 0.874, 0.59571, 0.83666, 0.56555, 0.84151, 0.54545, 0.92266, 0.47955, 1, 0.45387, 0.98577, 0.42036, 0.90185, 0.37345, 0.86995, 0.33324, 0.88104, 0.30644, 0.98438, 0.26511, 0.99062, 0.18805, 0.9067, 0.1121, 0.9074, 0.0261, 0.86232, 0.0127, 0.73748, 0.01828, 0.6598, 0.04286, 0.58767, 0.0134, 0.48826, 0.33597, 0.60003, 0.15043, 0.54983, 0.69886, 0.14389, 0.59829, 0.29319, 0.45372, 0.43956, 0.34529, 0.52446, 0.18658, 0.40053, 0.25729, 0.25221, 0.48829, 0.099, 0.32015, 0.64434, 0.36729, 0.71655, 0.59326, 0.77623, 0.55222, 0.76285, 0.45475, 0.69405, 0.33471, 0.79662, 0.29059, 0.79089, 0.18082, 0.73036, 0.13875, 0.82529, 0.22801, 0.81255, 0.26289, 0.90875, 0.49784, 0.78643, 0.50092, 0.88645, 0.71124, 0.79726, 0.1685, 0.68131, 0.16132, 0.6125, 0.44141, 0.63607], "triangles": [24, 53, 52, 26, 53, 25, 27, 53, 26, 47, 54, 48, 47, 10, 11, 46, 47, 11, 46, 11, 12, 45, 46, 12, 54, 47, 46, 14, 56, 13, 12, 13, 56, 45, 12, 56, 17, 45, 56, 46, 18, 54, 18, 45, 17, 45, 18, 46, 23, 48, 54, 55, 23, 54, 16, 17, 56, 15, 16, 56, 18, 55, 54, 22, 23, 55, 19, 55, 18, 15, 56, 14, 21, 55, 20, 22, 55, 21, 55, 19, 20, 24, 48, 23, 25, 53, 24, 24, 49, 48, 52, 49, 24, 53, 27, 52, 27, 28, 51, 27, 51, 52, 28, 29, 51, 0, 35, 33, 35, 40, 39, 32, 33, 35, 34, 35, 39, 34, 39, 9, 58, 35, 34, 32, 35, 58, 59, 34, 9, 43, 58, 34, 43, 34, 59, 59, 9, 10, 31, 32, 58, 57, 58, 43, 31, 58, 57, 47, 59, 10, 44, 43, 59, 44, 59, 47, 50, 57, 43, 50, 43, 44, 30, 31, 57, 30, 57, 50, 48, 44, 47, 49, 50, 44, 48, 49, 44, 52, 50, 49, 51, 30, 50, 51, 50, 52, 29, 30, 51, 42, 3, 4, 36, 4, 5, 42, 4, 36, 41, 2, 3, 41, 3, 42, 36, 5, 6, 37, 42, 36, 37, 36, 6, 41, 42, 37, 1, 2, 41, 40, 1, 41, 0, 1, 40, 38, 41, 37, 40, 41, 38, 7, 37, 6, 38, 37, 7, 39, 40, 38, 40, 35, 0, 8, 38, 7, 9, 39, 38, 8, 9, 38], "vertices": [2, 36, 57.93, -10.61, 0.98736, 37, -10.81, -0.75, 0.01264, 1, 36, 53.51, -14.47, 1, 1, 36, 35.07, -28, 1, 1, 36, 9.76, -28.95, 1, 1, 36, -11.05, -15.31, 1, 1, 36, -16.96, 7.84, 1, 2, 36, -7.56, 23.83, 0.99765, 37, 4.84, 71.57, 0.00235, 2, 36, 14.29, 34.76, 0.94044, 37, 21.22, 53.44, 0.05956, 3, 36, 36.11, 34.29, 0.72174, 37, 26.62, 32.29, 0.27153, 38, 19.92, 31.12, 0.00673, 3, 36, 47.44, 30.03, 0.33337, 37, 25.54, 20.24, 0.57822, 38, 8.79, 26.35, 0.0884, 3, 36, 49.81, 39.33, 0.06975, 37, 35.14, 20.44, 0.52941, 38, 13.52, 17.99, 0.40085, 3, 36, 52.8, 44.54, 0.01988, 37, 40.96, 18.95, 0.27939, 38, 14.98, 12.17, 0.70073, 3, 36, 51.16, 50.92, 0.0013, 37, 46.67, 22.24, 0.03202, 38, 20.58, 8.7, 0.96668, 2, 37, 44.57, 26.41, 0.00542, 38, 23.25, 12.52, 0.99458, 1, 38, 31.16, 1.19, 1, 1, 38, 32.96, -15.1, 1, 1, 38, 25.47, -8.44, 1, 1, 38, 18.09, -4.12, 1, 1, 38, 15.8, -4.82, 1, 1, 38, 14.69, -14.94, 1, 1, 38, 10.03, -24.74, 1, 1, 38, 7.98, -23.07, 1, 1, 38, 4.95, -12.78, 1, 1, 38, 1.17, -8.99, 1, 1, 38, -1.86, -10.49, 1, 1, 38, -3.37, -23.39, 1, 1, 38, -6.51, -24.3, 1, 1, 38, -12.89, -14.16, 1, 1, 38, -18.73, -14.5, 1, 2, 37, 40.52, -26.89, 0.70379, 38, -25.59, -9.2, 0.29621, 2, 37, 26.14, -21.07, 0.752, 38, -27.29, 6.22, 0.248, 1, 37, 17.66, -16.48, 1, 1, 37, 10.43, -10.88, 1, 2, 36, 66.93, -3.61, 0.2243, 37, -1.65, -7.55, 0.7757, 3, 36, 57.45, 23.2, 0.11982, 37, 21.65, 8.76, 0.85363, 38, -3.16, 24.33, 0.02655, 1, 37, 9.82, -1.38, 1, 1, 36, -0.9, -0.77, 1, 2, 36, 17.15, 8.01, 0.99065, 37, -3.79, 43.53, 0.00935, 3, 36, 37.5, 14.27, 0.82486, 37, 7.69, 25.6, 0.17425, 38, 5.04, 44.6, 0.00089, 3, 36, 50.72, 16.65, 0.41149, 37, 13.53, 13.49, 0.58123, 38, -2.85, 33.72, 0.00728, 1, 36, 49.73, -2.96, 1, 1, 36, 33.49, -13.16, 1, 1, 36, 7.59, -15.66, 1, 3, 36, 62, 26.52, 0.03368, 37, 26.06, 5.27, 0.93831, 38, -4.14, 18.79, 0.02802, 3, 36, 65.19, 35.64, 0.00689, 37, 35.7, 4.63, 0.86183, 38, -0.12, 10, 0.13129, 3, 36, 57.03, 52.7, 0.00072, 37, 49.95, 17.07, 0.0203, 38, 17.58, 3.36, 0.97898, 3, 36, 58.3, 49.36, 0.00263, 37, 47.08, 14.95, 0.06919, 38, 14.35, 4.88, 0.92817, 3, 36, 58.3, 38, 0.03294, 37, 36.13, 11.91, 0.58297, 38, 6.48, 13.08, 0.38409, 2, 37, 43.55, -1.95, 0.65001, 38, -2.2, -0.03, 0.34999, 2, 37, 41.43, -4.7, 0.70181, 38, -5.63, 0.53, 0.29819, 2, 37, 30.99, -9.03, 0.752, 38, -14.39, 7.66, 0.248, 2, 37, 40.17, -17.08, 0.69895, 38, -17.12, -4.24, 0.30105, 2, 37, 41.74, -10.21, 0.68409, 38, -10.32, -2.36, 0.31591, 1, 38, -7.12, -14.16, 1, 3, 36, 63.38, 48.79, 0.00055, 37, 47.88, 9.9, 0.03055, 38, 10.29, 1.77, 0.96891, 1, 38, 11.07, -10.61, 1, 1, 38, 26.77, 1.14, 1, 1, 37, 25.1, -7.24, 1, 1, 37, 17.18, -4.01, 1, 3, 36, 54.31, 31.93, 0.11798, 37, 29.21, 14.12, 0.70692, 38, 5.15, 20.21, 0.17511], "hull": 34}}, "duzhuaguai_07": {"duzhuaguai_07": {"type": "mesh", "uvs": [0.51718, 0.93969, 0.57631, 0.96633, 0.61876, 1, 0.67031, 0.94439, 0.71579, 0.88173, 0.73095, 0.82376, 0.72489, 0.79399, 0.71276, 0.71253, 0.70945, 0.68986, 0.77983, 0.67104, 0.82619, 0.61029, 0.80349, 0.57142, 0.77984, 0.56209, 0.94763, 0.44278, 0.8315, 0.38389, 0.82827, 0.23055, 0.75193, 0.275, 0.72827, 0.17055, 0.70247, 0.11722, 0.69386, 0.04944, 0.59913, 0.0236, 0.48623, 0.00248, 0.45612, 0.04915, 0.35612, 0.15693, 0.3615, 0.2736, 0.24644, 0.15915, 0.18838, 0.32582, 0, 0.34675, 0, 0.38004, 0.12876, 0.4749, 0.18941, 0.54823, 0.14295, 0.6229, 0.17263, 0.7109, 0.23199, 0.73356, 0.26166, 0.8149, 0.28747, 0.92823, 0.34425, 0.91756, 0.34425, 0.98156, 0.43715, 0.94823, 0.55449, 0.84399, 0.55952, 0.82319, 0.57126, 0.72872, 0.57294, 0.69752, 0.57462, 0.59959, 0.5763, 0.51639, 0.57294, 0.35519, 0.59055, 0.19486, 0.59391, 0.08046, 0.79058, 0.49987, 0.82121, 0.43485, 0.81624, 0.39378, 0.74752, 0.38694, 0.19267, 0.42343, 0.26776, 0.45145, 0.23856, 0.35338, 0.24795, 0.2941, 0.42675, 0.61184, 0.26779, 0.55424, 0.27501, 0.70037, 0.37617, 0.81451, 0.45669, 0.84117, 0.63114, 0.85184, 0.62288, 0.81984, 0.45566, 0.81237, 0.38237, 0.78251, 0.30392, 0.68437, 0.29566, 0.58944, 0.4185, 0.63851, 0.50092, 0.65531, 0.49209, 0.68196, 0.63293, 0.69415, 0.63071, 0.72192, 0.68741, 0.46607, 0.43714, 0.40062, 0.73964, 0.63505, 0.65268, 0.65712, 0.42079, 0.58957, 0.32784, 0.54629, 0.57521, 0.57046, 0.62305, 0.5959, 0.72316, 0.52307, 0.49536, 0.57479, 0.40445, 0.47134, 0.37332, 0.50605, 0.69938, 0.58838, 0.61113, 0.91011, 0.5353, 0.89321, 0.44932, 0.89238, 0.39017, 0.65624, 0.44882, 0.75836, 0.56685, 0.76421, 0.63561, 0.77519], "triangles": [1, 85, 2, 2, 85, 3, 37, 36, 38, 0, 86, 1, 1, 86, 85, 38, 87, 0, 38, 36, 87, 85, 61, 3, 3, 61, 4, 0, 87, 86, 35, 34, 36, 36, 59, 87, 36, 34, 59, 86, 39, 85, 85, 39, 61, 87, 60, 86, 86, 60, 39, 87, 59, 60, 4, 61, 5, 61, 39, 62, 39, 40, 62, 61, 62, 5, 40, 39, 63, 59, 63, 60, 39, 60, 63, 62, 6, 5, 40, 90, 62, 40, 63, 90, 62, 91, 6, 34, 58, 59, 34, 33, 58, 59, 58, 64, 59, 64, 63, 64, 58, 65, 64, 89, 63, 64, 65, 88, 33, 32, 58, 58, 31, 57, 31, 58, 32, 58, 66, 65, 63, 89, 90, 64, 88, 89, 91, 90, 71, 90, 41, 71, 91, 71, 7, 89, 69, 90, 90, 69, 41, 88, 67, 89, 89, 67, 69, 41, 42, 71, 41, 69, 42, 7, 70, 8, 7, 71, 70, 71, 42, 70, 57, 31, 30, 58, 57, 66, 69, 68, 42, 75, 70, 42, 42, 68, 43, 70, 75, 8, 75, 42, 43, 8, 74, 9, 8, 75, 74, 65, 66, 88, 69, 67, 68, 10, 74, 11, 11, 74, 12, 74, 10, 9, 43, 79, 75, 75, 84, 74, 75, 79, 84, 88, 66, 67, 67, 56, 68, 43, 68, 76, 67, 66, 56, 74, 84, 12, 76, 56, 77, 77, 56, 57, 68, 56, 76, 76, 81, 43, 43, 78, 79, 43, 81, 78, 79, 80, 84, 77, 83, 76, 81, 76, 82, 56, 66, 57, 79, 78, 44, 79, 44, 72, 84, 80, 12, 80, 79, 72, 76, 83, 82, 78, 81, 44, 44, 81, 82, 13, 48, 49, 13, 12, 48, 12, 80, 48, 77, 57, 53, 57, 30, 53, 30, 52, 53, 30, 29, 52, 77, 53, 83, 80, 72, 48, 82, 73, 44, 73, 45, 44, 44, 45, 72, 83, 53, 82, 72, 51, 48, 49, 51, 50, 49, 48, 51, 29, 28, 52, 24, 73, 54, 72, 45, 51, 52, 54, 53, 82, 53, 54, 54, 73, 82, 24, 54, 55, 49, 14, 13, 49, 50, 14, 52, 27, 26, 52, 26, 54, 73, 24, 45, 14, 50, 16, 51, 45, 16, 16, 45, 46, 50, 51, 16, 16, 46, 17, 16, 15, 14, 52, 28, 27, 45, 24, 46, 54, 26, 55, 26, 25, 55, 55, 25, 24, 24, 23, 46, 23, 22, 46, 46, 18, 17, 46, 47, 18, 46, 22, 47, 47, 19, 18, 22, 21, 47, 47, 20, 19, 47, 21, 20, 62, 90, 91, 91, 7, 6], "vertices": [3, 33, 0.3, -3.18, 0.64, 32, -2.82, -21.59, 0.16, 31, -4, -37.05, 0.2, 3, 33, 5.83, -5.48, 0.64, 32, 2.72, -23.89, 0.16, 31, 1.54, -39.36, 0.2, 2, 33, 9.83, -8.45, 0.8, 31, 5.54, -42.32, 0.2, 2, 33, 14.54, -3.36, 0.8, 31, 10.24, -37.23, 0.2, 2, 33, 18.67, 2.35, 0.8, 31, 14.38, -31.52, 0.2, 2, 33, 19.99, 7.59, 0.8, 31, 15.7, -26.28, 0.2, 2, 33, 19.38, 10.26, 0.8, 31, 15.08, -23.61, 0.2, 2, 5, 39.57, -15.51, 0.8, 31, 13.83, -16.3, 0.2, 2, 5, 39.4, -13.45, 0.8, 31, 13.49, -14.26, 0.2, 2, 5, 46.04, -12.18, 0.8, 31, 20, -12.46, 0.2, 2, 5, 50.69, -7, 0.8, 31, 24.22, -6.91, 0.2, 2, 5, 48.81, -3.38, 0.8, 31, 22.05, -3.45, 0.2, 2, 5, 46.67, -2.4, 0.8, 31, 19.83, -2.65, 0.2, 2, 5, 62.93, 7.31, 0.8, 31, 35.25, 8.36, 0.2, 2, 5, 52.5, 13.3, 0.8, 31, 24.36, 13.47, 0.2, 2, 5, 53.09, 27.09, 0.8, 31, 23.82, 27.26, 0.2, 3, 5, 45.74, 23.55, 0.72, 32, 17.97, 38.6, 0.08, 31, 16.79, 23.14, 0.2, 3, 5, 44.15, 33.08, 0.72, 32, 15.61, 47.97, 0.08, 31, 14.43, 32.5, 0.2, 3, 5, 42.07, 38.02, 0.72, 32, 13.13, 52.72, 0.08, 31, 11.94, 37.26, 0.2, 3, 5, 41.66, 44.16, 0.72, 32, 12.22, 58.81, 0.08, 31, 11.04, 43.34, 0.2, 3, 5, 33.02, 47.05, 0.64, 32, 3.37, 60.98, 0.16, 31, 2.19, 45.51, 0.2, 3, 5, 22.66, 49.62, 0.72, 32, -7.16, 62.7, 0.08, 31, -8.34, 47.23, 0.2, 3, 5, 19.6, 45.61, 0.72, 32, -9.89, 58.45, 0.08, 31, -11.07, 42.98, 0.2, 3, 5, 9.69, 36.53, 0.72, 32, -19.02, 48.59, 0.08, 31, -20.2, 33.12, 0.2, 3, 5, 9.52, 26.02, 0.72, 32, -18.33, 38.1, 0.08, 31, -19.52, 22.63, 0.2, 2, 5, -0.5, 36.99, 0.8, 31, -30.4, 32.75, 0.2, 2, 5, -6.85, 22.36, 0.8, 31, -35.53, 17.66, 0.2, 2, 5, -24.46, 21.61, 0.8, 31, -53.02, 15.47, 0.2, 2, 5, -24.65, 18.62, 0.8, 31, -52.97, 12.47, 0.2, 2, 5, -13.25, 9.33, 0.8, 31, -40.85, 4.14, 0.2, 2, 5, -8.05, 2.38, 0.8, 31, -35.09, -2.36, 0.2, 3, 5, -12.79, -4.05, 0.3456, 33, -35, 24.72, 0.4544, 31, -39.29, -9.15, 0.2, 3, 5, -10.55, -12.13, 0.128, 33, -32.1, 16.85, 0.672, 31, -36.4, -17.02, 0.2, 2, 33, -26.55, 14.91, 0.8, 31, -30.84, -18.97, 0.2, 2, 33, -23.66, 7.63, 0.8, 31, -27.96, -26.24, 0.2, 2, 33, -21.08, -2.52, 0.8, 31, -25.38, -36.39, 0.2, 3, 33, -15.82, -1.47, 0.68, 32, -18.93, -19.88, 0.12, 31, -20.12, -35.34, 0.2, 3, 33, -15.72, -7.23, 0.68, 32, -18.83, -25.63, 0.12, 31, -20.02, -41.1, 0.2, 3, 33, -7.13, -4.08, 0.64, 32, -10.25, -22.49, 0.16, 31, -11.43, -37.95, 0.2, 2, 33, 3.62, 5.49, 0.8, 32, 0.5, -12.92, 0.2, 2, 33, 4.05, 7.37, 0.8, 32, 0.94, -11.04, 0.2, 2, 5, 26.35, -16.11, 0.8, 32, 1.88, -2.52, 0.2, 2, 5, 26.68, -13.32, 0.8, 32, 1.99, 0.29, 0.2, 2, 5, 27.41, -4.54, 0.8, 32, 1.99, 9.11, 0.2, 2, 5, 28.04, 2.93, 0.8, 32, 2.02, 16.6, 0.2, 2, 5, 28.67, 17.42, 0.8, 32, 1.45, 31.1, 0.2, 2, 5, 31.23, 31.72, 0.8, 32, 2.84, 45.56, 0.2, 2, 5, 32.2, 41.97, 0.8, 32, 2.97, 55.86, 0.2, 1, 5, 48.03, 3.13, 1, 1, 5, 51.25, 8.78, 1, 1, 5, 51.02, 12.5, 1, 2, 5, 44.68, 13.53, 0.9, 32, 17.74, 28.52, 0.1, 2, 5, -7.02, 13.57, 0.904, 32, -33.8, 24.35, 0.096, 2, 5, -0.21, 10.6, 0.9, 32, -26.77, 21.94, 0.1, 2, 5, -2.36, 19.59, 0.9, 32, -29.64, 30.72, 0.1, 2, 5, -1.14, 24.86, 0.9, 32, -28.86, 36.07, 0.1, 2, 5, 13.61, -4.75, 0.85, 32, -11.74, 7.77, 0.15, 2, 5, -0.81, 1.37, 0.9, 32, -26.61, 12.7, 0.1, 2, 33, -22.6, 17.96, 0.9, 32, -25.71, -0.44, 0.1, 2, 33, -13.01, 7.85, 0.85, 32, -16.13, -10.55, 0.15, 2, 33, -5.48, 5.58, 0.8, 32, -8.6, -12.82, 0.2, 2, 33, 10.75, 4.91, 0.8, 32, 7.64, -13.5, 0.2, 2, 33, 9.94, 7.77, 0.8, 32, 6.82, -10.63, 0.2, 2, 33, -5.62, 8.17, 0.8, 32, -8.74, -10.23, 0.2, 2, 33, -12.49, 10.74, 0.85, 32, -15.6, -7.66, 0.15, 2, 33, -19.93, 19.45, 0.9, 32, -23.05, 1.04, 0.1, 2, 5, 1.58, -1.96, 0.864, 32, -23.96, 9.57, 0.136, 2, 5, 12.69, -7.1, 0.85, 32, -12.46, 5.36, 0.15, 2, 5, 20.24, -9.1, 0.8, 32, -4.77, 3.98, 0.2, 2, 5, 19.27, -11.44, 0.8, 32, -5.55, 1.56, 0.2, 2, 5, 32.27, -13.38, 0.8, 32, 7.56, 0.69, 0.2, 2, 5, 31.9, -15.86, 0.8, 32, 7.4, -1.81, 0.2, 2, 5, 38.65, 6.78, 0.85, 32, 12.27, 21.31, 0.15, 2, 5, 15.8, 14.16, 0.85, 32, -11.1, 26.79, 0.15, 2, 5, 42.52, -8.71, 0.92, 32, 17.39, 6.18, 0.08, 2, 5, 34.32, -10.17, 0.816, 32, 9.34, 4.06, 0.184, 2, 5, 13.19, -2.72, 0.85, 32, -12.33, 9.76, 0.15, 2, 5, 4.81, 1.73, 0.856, 32, -21.04, 13.51, 0.144, 2, 5, 27.63, -1.92, 0.8, 32, 2, 11.73, 0.2, 2, 5, 31.92, -4.5, 0.8, 32, 6.49, 9.52, 0.2, 2, 5, 41.63, 1.45, 0.85, 32, 15.68, 16.24, 0.15, 2, 5, 20.19, -1.83, 0.8, 32, -5.42, 11.21, 0.2, 2, 5, 12.36, 8, 0.85, 32, -14.03, 20.38, 0.15, 2, 5, 9.27, 5.07, 0.896, 32, -16.87, 17.2, 0.104, 2, 5, 39.05, -4.28, 0.85, 32, 13.58, 10.32, 0.15, 2, 33, 8.98, -0.37, 0.8, 32, 5.87, -18.77, 0.2, 2, 33, 1.91, 1.03, 0.8, 32, -1.21, -17.38, 0.2, 2, 33, -6.09, 0.97, 0.8, 32, -9.2, -17.44, 0.2, 3, 5, 9.96, -8.52, 0.8432, 33, -11.96, 22.12, 0.0068, 32, -15.07, 3.71, 0.15, 3, 5, 14.81, -18.04, 0.7936, 33, -6.34, 13.02, 0.0064, 32, -9.46, -5.38, 0.2, 3, 5, 25.73, -19.28, 0.7808, 33, 4.64, 12.69, 0.0192, 32, 1.53, -5.72, 0.2, 2, 5, 32.05, -20.67, 0.8, 32, 7.94, -6.59, 0.2], "hull": 39}}, "duzhuaguai_09": {"duzhuaguai_09": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 13, 37.14, 71.55, 1, 1, 13, 88.74, 2.75, 1, 1, 13, 10.34, -56.05, 1, 1, 13, -41.26, 12.75, 1], "hull": 4}}, "duzhuaguai_010": {"duzhuaguai_010": {"type": "mesh", "uvs": [0, 0.0811, 0.07054, 0.07893, 0.11274, 0.02036, 0.23315, 0, 0.36077, 0.01819, 0.4781, 0.10388, 0.49354, 0.20149, 0.48222, 0.27524, 0.53882, 0.38262, 0.60027, 0.44245, 0.61779, 0.45475, 0.71589, 0.45598, 0.71239, 0.37229, 0.89457, 0.45229, 1, 0.63814, 0.99034, 0.85352, 0.90041, 0.96552, 0.86071, 0.97291, 0.8829, 0.87691, 0.83852, 0.7526, 0.77545, 0.70091, 0.78129, 0.79321, 0.7626, 0.86091, 0.80465, 1, 0.77312, 1, 0.5991, 0.86952, 0.52202, 0.77352, 0.49399, 0.73168, 0.47793, 0.73331, 0.48674, 0.82963, 0.55821, 0.95098, 0.52545, 0.96458, 0.3964, 0.90809, 0.29316, 0.78883, 0.28522, 0.66015, 0.30805, 0.55345, 0.29415, 0.50428, 0.16808, 0.39862, 0.08767, 0.26366, 0.08469, 0.1852, 0.0152, 0.12452, 0.10256, 0.12766, 0.26735, 0.14022, 0.29217, 0.22286, 0.35471, 0.36618, 0.45199, 0.49068, 0.70724, 0.58588, 0.72808, 0.66015, 0.70029, 0.71769, 0.72709, 0.79825, 0.68639, 0.6476, 0.57421, 0.59529, 0.50969, 0.62772, 0.45211, 0.66329, 0.35284, 0.65283, 0.37567, 0.78255, 0.44417, 0.87775, 0.57421, 0.66957, 0.65462, 0.81603, 0.7529, 0.93948, 0.83132, 0.48126, 0.90975, 0.67689, 0.93258, 0.86101, 0.57819, 0.52206, 0.38064, 0.57228], "triangles": [55, 53, 28, 33, 54, 55, 55, 28, 29, 56, 55, 29, 32, 55, 56, 33, 55, 32, 56, 29, 30, 31, 56, 30, 32, 56, 31, 57, 51, 50, 48, 50, 47, 48, 47, 20, 26, 27, 57, 49, 48, 20, 21, 49, 20, 48, 58, 57, 48, 57, 50, 58, 48, 49, 26, 57, 58, 22, 49, 21, 25, 26, 58, 22, 58, 49, 59, 58, 22, 25, 58, 59, 22, 23, 59, 23, 24, 59, 25, 59, 24, 60, 11, 12, 13, 60, 12, 46, 11, 60, 47, 46, 60, 61, 13, 14, 60, 13, 61, 20, 47, 60, 61, 20, 60, 19, 20, 61, 15, 61, 14, 62, 19, 61, 15, 62, 61, 18, 19, 62, 62, 17, 18, 16, 62, 15, 16, 17, 62, 63, 45, 8, 9, 63, 8, 63, 9, 10, 46, 10, 11, 63, 10, 46, 51, 63, 46, 50, 51, 46, 50, 46, 47, 63, 52, 45, 51, 52, 63, 64, 45, 52, 52, 51, 57, 27, 53, 52, 27, 52, 57, 28, 53, 27, 64, 35, 36, 45, 64, 36, 54, 35, 64, 53, 54, 64, 34, 35, 54, 52, 53, 64, 55, 54, 53, 33, 34, 54, 40, 0, 1, 41, 1, 2, 40, 1, 41, 42, 3, 4, 39, 40, 41, 5, 43, 42, 5, 42, 4, 43, 5, 6, 43, 38, 39, 41, 2, 3, 42, 41, 3, 7, 43, 6, 44, 43, 7, 44, 7, 8, 43, 39, 42, 42, 39, 41, 37, 43, 44, 37, 38, 43, 45, 44, 8, 36, 37, 44, 36, 44, 45], "vertices": [1, 14, -18.64, -20.79, 1, 1, 14, -13.18, -12.81, 1, 1, 14, -15.93, -3.66, 1, 1, 14, -8.35, 11.23, 1, 1, 14, 3.86, 23.97, 1, 1, 14, 22.32, 30.4, 1, 2, 14, 33.83, 24.64, 0.98829, 15, -4, 35.39, 0.01171, 2, 14, 40.66, 17.74, 0.91948, 15, -3.17, 25.72, 0.08052, 2, 14, 56.51, 15.78, 0.40612, 15, 7.76, 14.07, 0.59388, 3, 14, 67.75, 18.01, 0.03398, 15, 17.83, 8.59, 0.95491, 16, -20.95, -1.17, 0.01112, 3, 14, 70.46, 19, 0.00697, 15, 20.54, 7.62, 0.94764, 16, -18.08, -0.83, 0.04539, 2, 15, 33.62, 10.75, 0.09825, 16, -7.74, 7.75, 0.90175, 1, 16, -15.15, 15.73, 1, 1, 16, 10.6, 23.98, 1, 1, 16, 37.25, 14.93, 1, 1, 16, 54.38, -7.26, 1, 1, 16, 54.43, -26.33, 1, 1, 16, 50.91, -30.58, 1, 1, 16, 45.14, -19.11, 1, 3, 15, 59.32, -22.54, 0.00094, 16, 30.04, -10.74, 0.98393, 18, 30.02, 23.79, 0.01513, 3, 15, 49.3, -18.13, 0.0521, 16, 19.1, -11.22, 0.61988, 18, 19.71, 20.11, 0.32801, 3, 15, 53.01, -29.58, 0.01328, 16, 27.49, -19.84, 0.11829, 18, 30.26, 14.34, 0.86843, 3, 15, 52.68, -38.73, 0.00047, 16, 31.24, -28.2, 0.0143, 18, 36.31, 7.46, 0.98523, 1, 18, 54.66, 2.61, 1, 1, 18, 52.34, -1.03, 1, 3, 17, 49.83, -12.57, 0.00163, 18, 25.23, -12.04, 0.99753, 20, 30.44, 16.4, 0.00085, 4, 19, 27.23, 21.61, 0.03124, 17, 33.55, -14.07, 0.20051, 18, 9.03, -14.25, 0.68775, 20, 14.43, 13.07, 0.0805, 4, 19, 23.5, 16.1, 0.12274, 17, 26.89, -14.09, 0.36624, 18, 2.38, -14.57, 0.24422, 20, 7.82, 12.29, 0.26681, 4, 19, 24.47, 14.11, 0.14721, 17, 25.8, -16.02, 0.26169, 18, 1.38, -16.54, 0.09732, 20, 6.96, 10.25, 0.49378, 3, 17, 36.75, -22.21, 0.00336, 18, 12.59, -22.24, 0.00021, 20, 18.54, 5.34, 0.99643, 1, 20, 37.08, 6.43, 1, 1, 20, 36.49, 1.64, 1, 1, 20, 21.6, -10.4, 1, 2, 19, 40.17, -7.02, 0.10441, 20, 1.23, -15.44, 0.89559, 3, 14, 65.21, -33.54, 0.01505, 19, 24.9, -13.94, 0.85713, 20, -13.99, -8.42, 0.12782, 2, 14, 55.84, -22.85, 0.26238, 19, 10.82, -15.92, 0.73762, 2, 14, 49.55, -20.62, 0.55013, 19, 5.51, -19.96, 0.44987, 2, 14, 28.28, -26.49, 0.94375, 19, -1.24, -40.97, 0.05625, 2, 14, 7.61, -25.06, 0.99858, 19, -13.76, -57.47, 0.00142, 1, 14, -0.87, -19.38, 1, 1, 14, -12.85, -22.43, 1, 1, 14, -5.48, -13, 1, 1, 14, 9.14, 4.28, 1, 1, 14, 19.82, 0.71, 1, 2, 14, 39.93, -3.34, 0.975, 19, -14.21, -18.54, 0.025, 4, 14, 60.86, -2.1, 0.27041, 19, -3.78, -0.35, 0.20351, 17, -2.08, -0.84, 0.42594, 15, -0.34, -2.45, 0.10014, 4, 17, 28.12, 20.71, 0.00081, 15, 36.59, -5.91, 0.63446, 16, 2.3, -5.88, 0.23595, 18, 2.08, 20.26, 0.12878, 3, 15, 41.72, -14.58, 0.19183, 16, 10.73, -11.38, 0.42547, 18, 11.76, 17.48, 0.3827, 3, 15, 39.85, -22.76, 0.08178, 16, 12.67, -19.55, 0.17266, 18, 16.02, 10.25, 0.74556, 3, 15, 45.97, -32.02, 0.00966, 16, 22.25, -25.15, 0.06893, 18, 26.83, 7.73, 0.92141, 3, 15, 35.78, -14.39, 0.31459, 16, 5.32, -13.84, 0.18237, 18, 7.31, 13.54, 0.50304, 3, 17, 18.67, 5.08, 0.60115, 15, 19.22, -11.55, 0.24708, 18, -6.68, 4.22, 0.15178, 3, 19, 10.1, 13.34, 0.13367, 17, 17.05, -4.58, 0.8415, 20, -3.03, 20.62, 0.02483, 4, 19, 17.21, 7.59, 0.53631, 17, 16.32, -13.7, 0.31693, 18, -8.2, -14.63, 0.00425, 20, -2.73, 11.48, 0.14252, 3, 14, 69.89, -25.5, 0.01233, 19, 20.74, -5.61, 0.97451, 20, -10.41, 0.17, 0.01316, 1, 20, 5.91, -5.12, 1, 1, 20, 21.26, -2.77, 1, 4, 19, 12.06, 23.53, 0.00303, 17, 26.58, -0.46, 0.03816, 18, 1.47, -0.96, 0.95524, 20, 5.96, 25.8, 0.00356, 3, 17, 48.49, -2.35, 6e-05, 18, 23.45, -1.89, 0.99984, 20, 27.95, 26.4, 0.00011, 1, 18, 44.22, 0.86, 1, 1, 16, 6.44, 15.5, 1, 1, 16, 31.1, 3.08, 1, 1, 16, 48.99, -13.12, 1, 3, 17, 11.18, 10.98, 0.09067, 15, 17.42, -2.19, 0.89857, 18, -14.41, 9.79, 0.01075, 2, 14, 63.67, -16.25, 0.0971, 19, 9.6, -5.75, 0.9029], "hull": 41}}, "dg/dg_24": {"dg/dg_00024": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [115, -112, -115, -112, -115, 112, 115, 112], "hull": 4}, "dg/dg_00025": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [115, -112, -115, -112, -115, 112, 115, 112], "hull": 4}, "dg/dg_00026": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [115, -112, -115, -112, -115, 112, 115, 112], "hull": 4}, "dg/dg_00028": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [115, -112, -115, -112, -115, 112, 115, 112], "hull": 4}, "dg/dg_00030": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [115, -112, -115, -112, -115, 112, 115, 112], "hull": 4}, "dg/dg_00032": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [115, -112, -115, -112, -115, 112, 115, 112], "hull": 4}, "dg/dg_00034": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [115, -112, -115, -112, -115, 112, 115, 112], "hull": 4}, "dg/dg_00036": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [115, -112, -115, -112, -115, 112, 115, 112], "hull": 4}}, "bd/1_00075": {"bd/1_00075": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [154, -114, -154, -114, -154, 114, 154, 114], "hull": 4}, "bd/1_00076": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [154, -114, -154, -114, -154, 114, 154, 114], "hull": 4}, "bd/1_00077": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [154, -114, -154, -114, -154, 114, 154, 114], "hull": 4}, "bd/1_00079": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [154, -114, -154, -114, -154, 114, 154, 114], "hull": 4}, "bd/1_00081": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [154, -114, -154, -114, -154, 114, 154, 114], "hull": 4}, "bd/1_00083": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [154, -114, -154, -114, -154, 114, 154, 114], "hull": 4}}, "biyan": {"duzhuaguai_08": {"type": "mesh", "uvs": [0.00233, 0, 0.07599, 0, 0.20599, 0.18473, 0.34755, 0.45144, 0.50499, 0.62757, 0.67832, 0.52441, 0.8401, 0.31054, 0.93543, 0.17215, 1, 0.1797, 0.98599, 0.33821, 0.90366, 0.52189, 0.89499, 0.83892, 0.74766, 1, 0.63355, 0.99492, 0.54255, 0.8515, 0.49777, 0.69299, 0.35333, 0.80369, 0.15977, 0.7584, 0.02544, 0.54202, 0.04422, 0.2577, 0, 0.1168, 0.50146, 0.66124, 0.71613, 0.73352, 0.87464, 0.40062, 0.95464, 0.25352, 0.2311, 0.52191, 0.11458, 0.20575, 0.04736, 0.08475], "triangles": [13, 22, 12, 12, 22, 11, 13, 14, 22, 15, 21, 14, 14, 21, 22, 11, 22, 10, 17, 25, 16, 15, 16, 21, 17, 18, 25, 25, 18, 19, 25, 19, 26, 21, 4, 22, 4, 5, 22, 23, 22, 5, 23, 10, 22, 21, 16, 25, 25, 3, 21, 21, 3, 4, 5, 6, 23, 26, 2, 25, 25, 2, 3, 10, 23, 9, 23, 24, 9, 23, 6, 24, 9, 24, 8, 6, 7, 24, 19, 27, 26, 19, 20, 27, 24, 7, 8, 2, 26, 1, 26, 27, 1, 20, 0, 27, 27, 0, 1], "vertices": [3, 32, -26.72, 31.58, 0.0768, 31, -27.9, 16.11, 0.04, 5, 0.63, 20.2, 0.8832, 3, 32, -22.74, 31.65, 0.0768, 31, -23.92, 16.18, 0.04, 5, 4.6, 19.95, 0.8832, 2, 32, -15.62, 26.04, 0.168, 5, 11.24, 13.78, 0.832, 2, 32, -7.83, 17.91, 0.16, 5, 18.33, 5.04, 0.84, 2, 32, 0.76, 12.6, 0.272, 5, 26.46, -0.96, 0.728, 2, 32, 10.06, 15.96, 0.16, 5, 36.01, 1.63, 0.84, 2, 32, 18.68, 22.74, 0.168, 5, 45.16, 7.68, 0.832, 3, 32, 23.76, 27.12, 0.06144, 31, 22.57, 11.65, 0.04, 5, 50.57, 11.63, 0.89856, 3, 32, 27.25, 26.94, 0.06144, 31, 26.06, 11.48, 0.04, 5, 54.03, 11.17, 0.89856, 3, 32, 26.58, 22.02, 0.06144, 31, 25.39, 6.55, 0.04, 5, 52.96, 6.32, 0.89856, 3, 32, 22.23, 16.25, 0.06144, 31, 21.05, 0.78, 0.04, 5, 48.16, 0.92, 0.89856, 3, 32, 21.93, 6.41, 0.06144, 31, 20.75, -9.05, 0.04, 5, 47.06, -8.85, 0.89856, 2, 32, 14.06, 1.28, 0.104, 5, 38.8, -13.33, 0.896, 2, 32, 7.9, 1.33, 0.168, 5, 32.66, -12.77, 0.832, 2, 32, 2.91, 5.69, 0.16, 5, 28.04, -8.02, 0.84, 2, 32, 0.41, 10.56, 0.272, 5, 25.94, -2.96, 0.728, 2, 32, -7.33, 7, 0.16, 5, 17.94, -5.88, 0.84, 2, 32, -17.81, 8.22, 0.168, 5, 7.6, -3.81, 0.832, 3, 32, -25.18, 14.8, 0.0768, 31, -26.36, -0.67, 0.04, 5, 0.79, 3.35, 0.8832, 3, 32, -24.32, 23.63, 0.0768, 31, -25.5, 8.16, 0.04, 5, 2.37, 12.08, 0.8832, 3, 32, -26.78, 27.96, 0.0768, 31, -27.96, 12.49, 0.04, 5, 0.27, 16.6, 0.8832, 2, 32, 0.59, 11.55, 0.272, 5, 26.21, -1.99, 0.728, 2, 32, 12.22, 9.51, 0.168, 5, 37.63, -4.97, 0.832, 2, 32, 20.6, 19.98, 0.064, 5, 46.84, 4.78, 0.936, 2, 32, 24.84, 24.61, 0.064, 5, 51.44, 9.05, 0.936, 2, 32, -14.08, 15.62, 0.168, 5, 11.92, 3.26, 0.832, 2, 32, -20.54, 25.31, 0.08, 5, 6.27, 13.45, 0.92, 2, 32, -24.24, 28.99, 0.08, 5, 2.89, 17.42, 0.92], "hull": 21}}}}], "events": {"atk": {}}, "animations": {"boss_attack1": {"slots": {"dg/dg_25": {"attachment": [{"time": 0.4, "name": "dg/dg_00024"}, {"time": 0.4333, "name": "dg/dg_00025"}, {"time": 0.4667, "name": "dg/dg_00026"}, {"time": 0.5, "name": "dg/dg_00028"}, {"time": 0.5333, "name": "dg/dg_00030"}, {"time": 0.5667, "name": "dg/dg_00032"}, {"time": 0.6, "name": "dg/dg_00034"}, {"time": 0.6333, "name": "dg/dg_00036"}, {"time": 0.6667, "name": null}]}, "dg/dg_00024": {"attachment": [{"time": 0.4, "name": "dg/dg_00024"}, {"time": 0.4333, "name": "dg/dg_00025"}, {"time": 0.4667, "name": "dg/dg_00026"}, {"time": 0.5, "name": "dg/dg_00028"}, {"time": 0.5333, "name": "dg/dg_00030"}, {"time": 0.5667, "name": "dg/dg_00032"}, {"time": 0.6, "name": "dg/dg_00034"}, {"time": 0.6333, "name": "dg/dg_00036"}, {"time": 0.6667, "name": null}]}, "dg/dg_24": {"attachment": [{"time": 0.4, "name": "dg/dg_00024"}, {"time": 0.4333, "name": "dg/dg_00025"}, {"time": 0.4667, "name": "dg/dg_00026"}, {"time": 0.5, "name": "dg/dg_00028"}, {"time": 0.5333, "name": "dg/dg_00030"}, {"time": 0.5667, "name": "dg/dg_00032"}, {"time": 0.6, "name": "dg/dg_00034"}, {"time": 0.6333, "name": "dg/dg_00036"}, {"time": 0.6667, "name": null}]}}, "bones": {"root": {"rotate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}]}, "bone": {"rotate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 19.4, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -11.01, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -11.82, "y": -0.67, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -22.88, "y": 14.08, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 15.93, "y": -0.26, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -14.14, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 17.32, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -1.23, "curve": 0.25, "c3": 0.75}, {"time": 0.6}], "translate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.6}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.6}], "shear": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.6}]}, "bone4": {"rotate": [{"angle": 1.97, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 4.56, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -2.28, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 10.66, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.6, "angle": 1.97}], "translate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}]}, "bone5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -5.81, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 11.86, "curve": "stepped"}, {"time": 0.4333, "angle": 11.86, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}]}, "bone6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 11.18, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -16.25, "curve": 0.25, "c3": 0.75}, {"time": 0.6}], "translate": [{"curve": "stepped"}, {"time": 0.6}], "scale": [{"curve": "stepped"}, {"time": 0.6}], "shear": [{"curve": "stepped"}, {"time": 0.6}]}, "bone7": {"rotate": [{"angle": -4.61, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 11.18, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -16.25, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6, "angle": -4.61}], "translate": [{"curve": "stepped"}, {"time": 0.6}], "scale": [{"curve": "stepped"}, {"time": 0.6}], "shear": [{"curve": "stepped"}, {"time": 0.6}]}, "bone8": {"rotate": [{"angle": -11.64, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 11.18, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -16.25, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.6, "angle": -11.64}], "translate": [{"curve": "stepped"}, {"time": 0.6}], "scale": [{"curve": "stepped"}, {"time": 0.6}], "shear": [{"curve": "stepped"}, {"time": 0.6}]}, "bone9": {"rotate": [{"angle": -16.25, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 11.18, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -16.25}], "translate": [{"curve": "stepped"}, {"time": 0.6}], "scale": [{"curve": "stepped"}, {"time": 0.6}], "shear": [{"curve": "stepped"}, {"time": 0.6}]}, "bone10": {"rotate": [{"angle": -8.46, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "angle": -16.25, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 11.18, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6, "angle": -8.46}], "translate": [{"curve": "stepped"}, {"time": 0.6}], "scale": [{"curve": "stepped"}, {"time": 0.6}], "shear": [{"curve": "stepped"}, {"time": 0.6}]}, "bone11": {"rotate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}]}, "bone12": {"rotate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}]}, "bone13": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -19.36, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.2667, "angle": 106.21, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3667, "angle": 170.97, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 63.1, "curve": 0.25, "c3": 0.75}, {"time": 0.6}], "translate": [{"curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.6}], "scale": [{"curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.6}], "shear": [{"curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.6}]}, "bone14": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 38.53, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.2667, "angle": -8.31, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3667, "angle": -19.85, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -51.25, "curve": 0.25, "c3": 0.75}, {"time": 0.6}], "translate": [{"curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.6}], "scale": [{"curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.6}], "shear": [{"curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.6}]}, "bone15": {"rotate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}]}, "bone16": {"rotate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}]}, "bone26": {"rotate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}]}, "bone17": {"rotate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}]}, "bone27": {"rotate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}]}, "bone18": {"rotate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}]}, "bone19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -97, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.2333, "angle": 68, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3333, "angle": 111.68, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -39.95, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 123.12, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.2333, "angle": 36.35, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3333, "angle": 46.09, "curve": "stepped"}, {"time": 0.4333, "angle": 46.09, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}]}, "bone21": {"rotate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}]}, "bone22": {"rotate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}]}, "bone23": {"rotate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}]}, "bone24": {"rotate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}]}, "bone25": {"rotate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}]}, "bone30": {"rotate": [{"angle": 0.08, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 30.61, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 0.08}], "translate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}]}, "bone31": {"rotate": [{"angle": -0.66, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 35.46, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -0.66}], "translate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}]}, "bone32": {"rotate": [{"angle": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -55.04, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 0.6}], "translate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}]}, "bone33": {"rotate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}]}, "bone34": {"rotate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}]}, "bone35": {"rotate": [{"curve": "stepped"}, {"time": 0.6}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 0.94, "y": 6.38, "curve": "stepped"}, {"time": 0.4, "x": 0.94, "y": 6.38, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -2.69, "y": -12.78, "curve": 0.25, "c3": 0.75}, {"time": 0.6}], "scale": [{"curve": "stepped"}, {"time": 0.6}], "shear": [{"curve": "stepped"}, {"time": 0.6}]}, "target2": {"rotate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -10.33, "y": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 0.89, "y": 17.43, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 40.72, "y": -8.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}]}, "target3": {"rotate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}]}, "bone28": {"rotate": [{"angle": -0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 13.45, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -0.8}], "translate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}]}, "bone29": {"rotate": [{"angle": 0.94, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -30.43, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 0.94}], "translate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}]}, "bone36": {"rotate": [{"angle": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 28.74, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 0.62}], "translate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}]}, "target1": {"rotate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}]}, "target4": {"rotate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "translate": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}], "shear": [{"curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.5667}]}}, "events": [{"time": 0.4, "name": "atk"}]}, "boss_attack3": {"slots": {"dg/dg_25": {"attachment": [{"time": 0.4, "name": "dg/dg_00024"}, {"time": 0.4333, "name": "dg/dg_00025"}, {"time": 0.4667, "name": "dg/dg_00026"}, {"time": 0.5, "name": "dg/dg_00028"}, {"time": 0.5333, "name": "dg/dg_00030"}, {"time": 0.5667, "name": "dg/dg_00032"}, {"time": 0.6, "name": "dg/dg_00034"}, {"time": 0.6333, "name": "dg/dg_00036"}, {"time": 0.6667, "name": null}]}, "bd/1_75": {"attachment": [{"time": 0.4333, "name": "bd/1_00075"}, {"time": 0.4667, "name": "bd/1_00076"}, {"time": 0.5, "name": "bd/1_00077"}, {"time": 0.5333, "name": "bd/1_00079"}, {"time": 0.5667, "name": "bd/1_00081"}, {"time": 0.6, "name": "bd/1_00083"}, {"time": 0.6333, "name": null}]}, "dg/dg_00024": {"attachment": [{"time": 0.4, "name": "dg/dg_00024"}, {"time": 0.4333, "name": "dg/dg_00025"}, {"time": 0.4667, "name": "dg/dg_00026"}, {"time": 0.5, "name": "dg/dg_00028"}, {"time": 0.5333, "name": "dg/dg_00030"}, {"time": 0.5667, "name": "dg/dg_00032"}, {"time": 0.6, "name": "dg/dg_00034"}, {"time": 0.6333, "name": "dg/dg_00036"}, {"time": 0.6667, "name": null}]}, "dg/dg_24": {"attachment": [{"time": 0.4, "name": "dg/dg_00024"}, {"time": 0.4333, "name": "dg/dg_00025"}, {"time": 0.4667, "name": "dg/dg_00026"}, {"time": 0.5, "name": "dg/dg_00028"}, {"time": 0.5333, "name": "dg/dg_00030"}, {"time": 0.5667, "name": "dg/dg_00032"}, {"time": 0.6, "name": "dg/dg_00034"}, {"time": 0.6333, "name": "dg/dg_00036"}, {"time": 0.6667, "name": null}]}, "bd/1_00075": {"attachment": [{"time": 0.3667, "name": "bd/1_00075"}, {"time": 0.4, "name": "bd/1_00076"}, {"time": 0.4333, "name": "bd/1_00077"}, {"time": 0.4667, "name": "bd/1_00079"}, {"time": 0.5, "name": "bd/1_00081"}, {"time": 0.5333, "name": "bd/1_00083"}, {"time": 0.5667, "name": null}]}}, "bones": {"root": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "bone": {"rotate": [{"curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": 0.728, "c2": 0.02, "c3": 0.951, "c4": 0.85}, {"time": 0.3333, "x": 101.76, "y": 114.47, "curve": 0, "c2": 0.68, "c4": 0.33}, {"time": 0.4333, "x": 282.71, "y": 5.62, "curve": "stepped"}, {"time": 0.5333, "x": 282.71, "y": 5.62, "curve": 0.951, "c2": 0.04, "c3": 0.451, "c4": 0.95}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.6667}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 27.81, "curve": "stepped"}, {"time": 0.3333, "angle": 27.81, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -20.02, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 2.31, "y": -28.13, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -4.03, "y": 2.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 21.97, "y": 17.59, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "bone3": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "bone4": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "bone5": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "bone6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -17.89, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 10.45, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.6667}]}, "bone7": {"rotate": [{"angle": 1.93, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -17.89, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 10.45, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.6667, "angle": 1.93}], "translate": [{"curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.6667}]}, "bone8": {"rotate": [{"angle": 3.51, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -17.89, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 10.45, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.6667, "angle": 3.51}], "translate": [{"curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.6667}]}, "bone9": {"rotate": [{"angle": 5.22, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -17.89, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 10.45, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 5.22}], "translate": [{"curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.6667}]}, "bone10": {"rotate": [{"angle": 6.94, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -17.89, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 10.45, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.6667, "angle": 6.94}], "translate": [{"curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.6667}]}, "bone11": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "bone12": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "bone13": {"rotate": [{"angle": 4.41, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -29.77, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 136.87, "curve": "stepped"}, {"time": 0.3667, "angle": 136.87, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 57.09, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.6667, "angle": 4.41}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "bone14": {"rotate": [{"angle": -1.81, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 46.46, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 43.94, "curve": "stepped"}, {"time": 0.3667, "angle": 43.94, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -23.38, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.6667, "angle": -1.81}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "bone15": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "bone16": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "bone26": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "bone17": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "bone27": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "bone18": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "bone19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -82.94, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -2.65, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 97.86, "curve": "stepped"}, {"time": 0.3333, "angle": 97.86, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -19.48, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 103.33, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 94.31, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 69.32, "curve": "stepped"}, {"time": 0.3333, "angle": 69.32, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 57.09, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "bone21": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "bone22": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "bone23": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "bone24": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "bone25": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "bone30": {"rotate": [{"angle": 0.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -29.63, "curve": "stepped"}, {"time": 0.3333, "angle": -29.63, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 0.08}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "bone31": {"rotate": [{"angle": -0.66, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 10.02, "curve": "stepped"}, {"time": 0.3333, "angle": 10.02, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -0.66}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "bone32": {"rotate": [{"angle": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -8.17, "curve": "stepped"}, {"time": 0.3333, "angle": -8.17, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 0.6}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "bone33": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "bone34": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "bone35": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "target2": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "translate": [{"curve": "stepped"}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 28.09, "y": 18.63, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}]}, "target3": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}]}, "bone28": {"rotate": [{"angle": -0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -38.7, "curve": "stepped"}, {"time": 0.3333, "angle": -38.7, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -0.8}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "bone29": {"rotate": [{"angle": 0.94, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 29.06, "curve": "stepped"}, {"time": 0.3333, "angle": 29.06, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 0.94}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "bone36": {"rotate": [{"angle": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -17.41, "curve": "stepped"}, {"time": 0.3333, "angle": -17.41, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 0.62}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "target1": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 3.76, "y": 28.18, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}]}, "target4": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 3.09, "y": 21.26, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4333}]}, "dg1": {"translate": [{"time": 0.4, "x": 229.95, "y": 12.08}], "scale": [{"time": 0.4, "x": 1.474, "y": 1.474}]}, "dg01": {"translate": [{"time": 0.4, "x": 255.73, "y": 9.4}], "scale": [{"time": 0.4, "x": 1.474, "y": 1.474}]}, "dg2": {"translate": [{"time": 0.4, "x": 229.69, "y": 9.4}], "scale": [{"time": 0.4, "x": 1.474, "y": 1.474}]}, "baoji2": {"translate": [{"time": 0.4333, "x": 7.66, "y": -114.83}]}}, "events": [{"time": 0.4, "name": "atk"}]}, "boss_idle": {"slots": {"biyan": {"attachment": [{"time": 1.3333, "name": "duzhuaguai_08"}, {"time": 1.5, "name": null}]}}, "bones": {"root": {"rotate": [{"curve": "stepped"}, {"time": 2}], "translate": [{"curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 2}]}, "bone": {"rotate": [{"curve": "stepped"}, {"time": 2}], "translate": [{"curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 2}]}, "bone2": {"rotate": [{"curve": "stepped"}, {"time": 2}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 2.76, "y": 3.97, "curve": 0.25, "c3": 0.75}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 2}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 5.31, "curve": 0.25, "c3": 0.75}, {"time": 2}], "translate": [{"curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 2}]}, "bone4": {"rotate": [{"angle": 1.51, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 5.31, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 1.51}], "translate": [{"curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 2}]}, "bone5": {"rotate": [{"angle": 2.63, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 3.67, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 2.63}], "translate": [{"curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 2}]}, "bone6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -2.39, "curve": 0.25, "c3": 0.75}, {"time": 2}], "translate": [{"curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 2}]}, "bone7": {"rotate": [{"angle": -2.33, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -8.21, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -2.33}], "translate": [{"curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 2}]}, "bone8": {"rotate": [{"angle": -5.88, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -8.21, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -5.88}], "translate": [{"curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 2}]}, "bone9": {"rotate": [{"angle": -8.21, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -8.21}], "translate": [{"curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 2}]}, "bone10": {"rotate": [{"angle": -5.54, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.3667, "angle": -8.21, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 2, "angle": -5.54}], "translate": [{"curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 2}]}, "bone11": {"rotate": [{"curve": "stepped"}, {"time": 2}], "translate": [{"x": 0.22, "y": 3.59, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 0.45, "y": 7.19, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 0.22, "y": 3.59}], "scale": [{"curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 2}]}, "bone12": {"rotate": [{"curve": "stepped"}, {"time": 2}], "translate": [{"x": 0.81, "y": -1.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 1.63, "y": -2.69, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": 0.81, "y": -1.34}], "scale": [{"curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 2}]}, "bone13": {"rotate": [{"angle": 4.17, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 5.82, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 4.17}], "translate": [{"curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 2}]}, "bone14": {"rotate": [{"angle": 22.26, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 5.82, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 22.26}], "translate": [{"curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 2}]}, "bone15": {"rotate": [{"angle": 4.17, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 5.82, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 4.17}], "translate": [{"curve": "stepped"}, {"time": 1.3333, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.3333, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.3333, "curve": "stepped"}, {"time": 2}]}, "bone16": {"rotate": [{"angle": 1.65, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 5.82, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 1.65}], "translate": [{"curve": "stepped"}, {"time": 1.6667, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.6667, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.6667, "curve": "stepped"}, {"time": 2}]}, "bone26": {"rotate": [{"angle": 8.42, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 11.76, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 8.42}], "translate": [{"curve": "stepped"}, {"time": 1.3333, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.3333, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.3333, "curve": "stepped"}, {"time": 2}]}, "bone17": {"rotate": [{"angle": 3.34, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 11.76, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 3.34}], "translate": [{"curve": "stepped"}, {"time": 1.6667, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.6667, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.6667, "curve": "stepped"}, {"time": 2}]}, "bone27": {"rotate": [{"angle": 8.42, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 11.76, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 8.42}], "translate": [{"curve": "stepped"}, {"time": 1.3333, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.3333, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.3333, "curve": "stepped"}, {"time": 2}]}, "bone18": {"rotate": [{"angle": 3.34, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 11.76, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 3.34}], "translate": [{"curve": "stepped"}, {"time": 1.6667, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.6667, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.6667, "curve": "stepped"}, {"time": 2}]}, "bone19": {"rotate": [{"angle": 4.17, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 5.82, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 4.17}], "translate": [{"curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 2}]}, "bone20": {"rotate": [{"angle": 5.82, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 5.82}], "translate": [{"curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 2}]}, "bone21": {"rotate": [{"angle": 4.17, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 5.82, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 4.17}], "translate": [{"curve": "stepped"}, {"time": 1.3333, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.3333, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.3333, "curve": "stepped"}, {"time": 2}]}, "bone22": {"rotate": [{"angle": 1.65, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 5.82, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 1.65}], "translate": [{"curve": "stepped"}, {"time": 1.6667, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.6667, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.6667, "curve": "stepped"}, {"time": 2}]}, "bone23": {"rotate": [{"angle": 1.65, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 5.82, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 1.65}], "translate": [{"curve": "stepped"}, {"time": 1.6667, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.6667, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.6667, "curve": "stepped"}, {"time": 2}]}, "bone24": {"rotate": [{"angle": 16.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 22.7, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 16.26}], "translate": [{"curve": "stepped"}, {"time": 1.3333, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.3333, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.3333, "curve": "stepped"}, {"time": 2}]}, "bone25": {"rotate": [{"angle": 3.34, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 11.76, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 3.34}], "translate": [{"curve": "stepped"}, {"time": 1.6667, "curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 1.6667, "curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 1.6667, "curve": "stepped"}, {"time": 2}]}, "bone30": {"rotate": [{"curve": "stepped"}, {"time": 2}], "translate": [{"curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 2}]}, "bone31": {"rotate": [{"curve": "stepped"}, {"time": 2}], "translate": [{"curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 2}]}, "bone32": {"rotate": [{"curve": "stepped"}, {"time": 2}], "translate": [{"curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 2}]}, "bone33": {"rotate": [{"curve": "stepped"}, {"time": 2}], "translate": [{"curve": "stepped"}, {"time": 2}], "scale": [{"curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 2}]}, "bone34": {"rotate": [{"curve": "stepped"}, {"time": 2}], "translate": [{"x": 3.52, "y": 1.55, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "x": 3.89, "y": 1.72, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "x": 3.52, "y": 1.55}], "scale": [{"curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 2}]}, "bone35": {"rotate": [{"curve": "stepped"}, {"time": 2}], "translate": [{"x": -0.55, "y": -2.08, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "x": -1.49, "y": -5.66, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "x": -0.55, "y": -2.08}], "scale": [{"curve": "stepped"}, {"time": 2}], "shear": [{"curve": "stepped"}, {"time": 2}]}}}, "die": {"slots": {"biyan": {"attachment": [{"time": 0.1333, "name": "duzhuaguai_08"}]}}, "bones": {"root": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}]}, "bone": {"rotate": [{"curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 15.4, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 35.95, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.36, "curve": "stepped"}, {"time": 0.3667, "angle": -6.36, "curve": "stepped"}, {"time": 0.4, "angle": -6.36}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -10.53, "y": -0.9, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -39.5, "y": 18.61, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 12.3, "y": -48.92, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 14.81, "y": -34.03, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 12.3, "y": -48.92}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.4}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.4}]}, "bone3": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": 8.85, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4}]}, "bone4": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": 8.85, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4}]}, "bone5": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}]}, "bone6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 10.65}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -26.51}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone8": {"rotate": [{"angle": 20.5, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 23.95}], "translate": [{"curve": "stepped"}, {"time": 0.4}], "scale": [{"curve": "stepped"}, {"time": 0.4}], "shear": [{"curve": "stepped"}, {"time": 0.4}]}, "bone9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 75.93}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone10": {"rotate": [{}], "translate": [{}], "scale": [{}], "shear": [{}]}, "bone11": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}]}, "bone12": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}]}, "bone13": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 32.66, "curve": "stepped"}, {"time": 0.3333, "angle": 32.66, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": 41.51, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4, "angle": 32.66}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4}]}, "bone14": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 32.66, "curve": "stepped"}, {"time": 0.3333, "angle": 32.66, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": 17.8, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4, "angle": 32.66}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4}]}, "bone15": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}]}, "bone16": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}]}, "bone26": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}]}, "bone17": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}]}, "bone27": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}]}, "bone18": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}]}, "bone19": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 32.66, "curve": "stepped"}, {"time": 0.3333, "angle": 32.66, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": 18.04, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4, "angle": -18.48}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4}]}, "bone20": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 32.66, "curve": "stepped"}, {"time": 0.3333, "angle": 32.66, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": 22.73, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4, "angle": 86.44}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4}]}, "bone21": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}]}, "bone22": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}]}, "bone23": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}]}, "bone24": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}]}, "bone25": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}]}, "bone30": {"rotate": [{"angle": 0.08, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 124.52, "curve": "stepped"}, {"time": 0.3667, "angle": 124.52}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}]}, "bone31": {"rotate": [{"angle": -0.66, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -20.46, "curve": "stepped"}, {"time": 0.3667, "angle": -20.46}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}]}, "bone32": {"rotate": [{"angle": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -97.69, "curve": "stepped"}, {"time": 0.3667, "angle": -97.69}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}]}, "bone33": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}]}, "bone34": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}]}, "bone35": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}]}, "target2": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2, "x": 0.48, "y": 24.05, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}]}, "target3": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2, "x": 6.21, "y": 26.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}]}, "bone28": {"rotate": [{"angle": -0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -40.58, "curve": "stepped"}, {"time": 0.3667, "angle": -40.58}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}]}, "bone29": {"rotate": [{"angle": 0.94, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 46.07, "curve": "stepped"}, {"time": 0.3667, "angle": 46.07}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}]}, "bone36": {"rotate": [{"angle": 0.62, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 1.63, "curve": "stepped"}, {"time": 0.3667, "angle": 1.63}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}]}, "target1": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}]}, "target4": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667}]}}}, "hurt": {"slots": {"biyan": {"attachment": [{"time": 0.1, "name": "duzhuaguai_08"}]}}, "bones": {"bone26": {"rotate": [{"angle": 9.81, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "angle": 8.42, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "angle": 14.61, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.3333, "angle": 9.81}], "translate": [{"curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "bone25": {"rotate": [{"angle": 0.31, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1, "angle": 3.34, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2, "angle": -4.13, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.3333, "angle": 0.31}], "translate": [{"curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "bone30": {"rotate": [{"angle": 0.08, "curve": "stepped"}, {"time": 0.3333, "angle": 0.08}], "translate": [{"curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "bone31": {"rotate": [{"angle": -0.66, "curve": "stepped"}, {"time": 0.3333, "angle": -0.66}], "translate": [{"curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "bone6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -11.54, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "bone8": {"rotate": [{"angle": -8.47, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "angle": -5.88, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1667, "angle": -17.43, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.3333, "angle": -8.47}], "translate": [{"curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "bone4": {"rotate": [{"angle": 2.1, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "angle": 1.51, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1333, "angle": 9.17, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.3333, "angle": 2.1}], "translate": [{"curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "root": {"rotate": [{"curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "bone32": {"rotate": [{"angle": 0.6, "curve": "stepped"}, {"time": 0.3333, "angle": 0.6}], "translate": [{"curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "bone33": {"rotate": [{"curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "bone34": {"rotate": [{"curve": "stepped"}, {"time": 0.3333}], "translate": [{"x": 3.52, "y": 1.55, "curve": "stepped"}, {"time": 0.3333, "x": 3.52, "y": 1.55}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 22.36, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -10.22, "y": 1.48, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "bone7": {"rotate": [{"angle": -3.22, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "angle": -2.33, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1333, "angle": -13.87, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.3333, "angle": -3.22}], "translate": [{"curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "bone5": {"rotate": [{"angle": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 10.29, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 2.63}], "translate": [{"curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "bone": {"rotate": [{"curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -19.19, "y": -0.38, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 7.66, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "bone10": {"rotate": [{"angle": -12.4, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1333, "angle": -5.54, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.2333, "angle": -17.08, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.3333, "angle": -12.4}], "translate": [{"curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "bone13": {"rotate": [{"angle": 4.91, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "angle": 4.17, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "angle": 13.74, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.3333, "angle": 4.91}], "translate": [{"curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "bone21": {"rotate": [{"angle": 5.56, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "angle": 4.17, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "angle": 10.36, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.3333, "angle": 5.56}], "translate": [{"curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "bone27": {"rotate": [{"angle": 6.75, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "angle": 8.42, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "angle": 0.96, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.3333, "angle": 6.75}], "translate": [{"curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "bone9": {"rotate": [{"angle": -12.9, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1, "angle": -8.21, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -19.76, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.3333, "angle": -12.9}], "translate": [{"curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "bone18": {"rotate": [{"angle": 0.31, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1, "angle": 3.34, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2, "angle": -4.13, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.3333, "angle": 0.31}], "translate": [{"curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "bone15": {"rotate": [{"angle": 5.56, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "angle": 4.17, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "angle": 10.36, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.3333, "angle": 5.56}], "translate": [{"curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "bone17": {"rotate": [{"angle": 5.85, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1, "angle": 3.34, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2, "angle": 9.53, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.3333, "angle": 5.85}], "translate": [{"curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "bone22": {"rotate": [{"angle": 4.16, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1, "angle": 1.65, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2, "angle": 7.84, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.3333, "angle": 4.16}], "translate": [{"curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "bone24": {"rotate": [{"angle": 14.58, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "angle": 16.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "angle": 8.79, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.3333, "angle": 14.58}], "translate": [{"curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "bone19": {"rotate": [{"angle": 4.91, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "angle": 4.17, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "angle": 13.74, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.3333, "angle": 4.91}], "translate": [{"curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "bone23": {"rotate": [{"angle": 4.16, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1, "angle": 1.65, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2, "angle": 7.84, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.3333, "angle": 4.16}], "translate": [{"curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "bone11": {"rotate": [{"curve": "stepped"}, {"time": 0.3333}], "translate": [{"x": 0.22, "y": 3.59, "curve": "stepped"}, {"time": 0.3333, "x": 0.22, "y": 3.59}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "bone12": {"rotate": [{"curve": "stepped"}, {"time": 0.3333}], "translate": [{"x": 0.81, "y": -1.34, "curve": "stepped"}, {"time": 0.3333, "x": 0.81, "y": -1.34}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "bone16": {"rotate": [{"angle": 4.16, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1, "angle": 1.65, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2, "angle": 7.84, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.3333, "angle": 4.16}], "translate": [{"curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "bone20": {"rotate": [{"angle": 7.97, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "angle": 5.82, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 15.39, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.3333, "angle": 7.97}], "translate": [{"curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "bone14": {"rotate": [{"angle": 24.41, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "angle": 22.26, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 31.84, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.3333, "angle": 24.41}], "translate": [{"curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "bone35": {"rotate": [{"curve": "stepped"}, {"time": 0.3333}], "translate": [{"x": -0.55, "y": -2.08, "curve": "stepped"}, {"time": 0.3333, "x": -0.55, "y": -2.08}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "target2": {"rotate": [{"curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "target3": {"rotate": [{"curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "bone28": {"rotate": [{"angle": -0.8, "curve": "stepped"}, {"time": 0.3333, "angle": -0.8}], "translate": [{"curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "bone29": {"rotate": [{"angle": 0.94, "curve": "stepped"}, {"time": 0.3333, "angle": 0.94}], "translate": [{"curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "bone36": {"rotate": [{"angle": 0.62, "curve": "stepped"}, {"time": 0.3333, "angle": 0.62}], "translate": [{"curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "target1": {"rotate": [{"curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}, "target4": {"rotate": [{"curve": "stepped"}, {"time": 0.3333}], "translate": [{"curve": "stepped"}, {"time": 0.3333}], "scale": [{"curve": "stepped"}, {"time": 0.3333}], "shear": [{"curve": "stepped"}, {"time": 0.3333}]}}}}}