import { _decorator, Component, Label, Node, sp, Tween, tween, v3 } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import ToolExt from "../../common/ToolExt";
import { JsonMgr } from "../../mgr/JsonMgr";
import { tweenTagEnum } from "../../GameDefine";
import FmUtils from "../../../lib/utils/FmUtils";
import { UIMgr } from "../../../lib/ui/UIMgr";
import ResMgr from "../../../lib/common/ResMgr";
import { AudioMgr, AudioName } from "../../../../platform/src/AudioHelper";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
const { ccclass, property } = _decorator;

@ccclass("UIHuntBossStruck")
export class UIHuntBossStruck extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_HUNT}?prefab/ui/UIHuntBossStruck`;
  }

  private _monsterId: number = null;

  private _damageHp: number = null;

  private _award: any = null;

  public init(args: any): void {
    super.init(args);
    this._monsterId = args.monsterId;
    this._damageHp = args.damageHp;
    this._award = args.award;
  }

  protected onEvtShow(): void {
    TipsMgr.setEnableTouch(true);
    this.setBossHurtLab();
    this.setBossName();
    this.playeAction();
    AudioMgr.instance.playEffect(AudioName.Effect.战斗胜利);
  }

  private setBossHurtLab() {
    let db1 = JsonMgr.instance.jsonList.c_monsterShow[this._monsterId];
    this.getNode("lbl_hurt_boss").getComponent(Label).string = `您对${db1.name}造成了${this._damageHp}的伤害`;
  }

  private setBossName() {
    let db1 = JsonMgr.instance.jsonList.c_monsterShow[this._monsterId];
    let db2 = JsonMgr.instance.jsonList.c_spineShow[db1.spineId];
    this.getNode("lbl_boss_name").getComponent(Label).string = db1.name;

    ResMgr.loadSpine(db2.spineRes, this.getNode("skt_boss").getComponent(sp.Skeleton), null, () => {
      this.getNode("skt_boss").getComponent(sp.Skeleton).setAnimation(0, "die", false);
    });
  }

  private playeAction() {
    this.getNode("bg_spr").scale = v3(0, 0, 0);
    this.getNode("tytanchuang")
      .getComponent(sp.Skeleton)
      .setCompleteListener(() => {
        this.getNode("tytanchuang").getComponent(sp.Skeleton).setAnimation(0, "zi_tiaozhanchenggong_1", true);
      });
    this.getNode("tytanchuang").getComponent(sp.Skeleton).setAnimation(0, "zi_tiaozhanchenggong", false);
    tween(this.getNode("bg_spr"))
      .tag(tweenTagEnum.UIHuntBossStruck_Tag)
      .to(0.1, { scale: v3(1, 1, 1) })
      .call(() => {
        let list = ToolExt.traAwardItemMapList(this._award);
        this.loadItem(list);
      })
      .start();
  }

  private loadItem(layerList: Array<{ id: number; num: number }>) {
    for (let i = 0; i < layerList.length; i++) {
      let node = ToolExt.clone(this.getNode("item_top"), this);
      node.active = true;
      node["Item"].active = false;
      this.getNode("itemContent").addChild(node);

      this.itemAct(node["Item"], layerList[i].id, layerList[i].num, i);
    }
  }

  private itemAct(node: Node, id: number, num: number, index) {
    FmUtils.setItemNode(node, id, num);
    node.scale = v3(0, 0, 0);
    node.active = true;
    tween(node)
      .tag(tweenTagEnum.UIHuntBossStruck_Tag)
      .delay(0.08 * index)
      .to(0.16, { scale: v3(1, 1, 1) })
      .start();
  }

  private on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  protected onEvtClose(): void {
    Tween.stopAllByTag(tweenTagEnum.UIHuntBossStruck_Tag);
  }
}
