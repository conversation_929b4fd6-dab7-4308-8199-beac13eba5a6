{"skeleton": {"hash": "9LELONXMN+NPr1A0qbPPJgCDHxM=", "spine": "3.8.75", "x": -282.34, "y": -143.81, "width": 529, "height": 274, "images": "./images/", "audio": "D:/spine导出/场景部件/人族-圣仪纹砖"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "x": 1.49, "y": -229.52}, {"name": "bone2", "parent": "bone", "x": -20.43, "y": 222.95, "scaleX": 2, "scaleY": 2}, {"name": "bone3", "parent": "bone", "x": 7.51, "y": 243.11}], "slots": [{"name": "zhuan", "bone": "bone", "attachment": "zhuan"}, {"name": "zhuanlizi/zhuanlizi_06", "bone": "bone2"}, {"name": "zhuanlizi/zhuanlizi_6", "bone": "bone2", "color": "ffffff22", "blend": "additive"}, {"name": "zhuan2", "bone": "bone3", "color": "ffe100ff", "dark": "ffcd00", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"zhuanlizi/zhuanlizi_06": {"zhuanlizi/zhuanlizi_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_09": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_11": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_13": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_14": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_15": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_16": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_17": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_18": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_19": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_20": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_21": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_22": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_23": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_24": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_25": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_26": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_27": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_28": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_29": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_30": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}}, "zhuanlizi/zhuanlizi_6": {"zhuanlizi/zhuanlizi_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_09": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_11": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_13": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_14": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_15": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_16": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_17": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_18": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_19": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_20": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_21": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_22": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_23": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_24": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_25": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_26": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_27": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_28": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_29": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}, "zhuanlizi/zhuanlizi_30": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [132, -68, -131, -68, -131, 68, 132, 68], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 131, "height": 68}}, "zhuan": {"zhuan": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [245.17, 85.71, -283.83, 85.71, -283.83, 359.71, 245.17, 359.71], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 529, "height": 274}}, "zhuan2": {"zhuan": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [237.66, -157.4, -291.34, -157.4, -291.34, 116.6, 237.66, 116.6], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 529, "height": 274}}}}], "animations": {"di_zhuan": {}, "di_zhuan_jiesuo": {"slots": {"zhuan2": {"twoColor": [{"time": 0.7333, "light": "ffe10000", "dark": "ffcd00", "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "light": "ff93006e", "dark": "ffcd00", "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "light": "ffe10000", "dark": "ffcd00"}], "attachment": [{"time": 0.7333, "name": "zhuan"}]}, "zhuanlizi/zhuanlizi_06": {"attachment": [{"name": "zhuanlizi/zhuanlizi_06"}, {"time": 0.0333, "name": "zhuanlizi/zhuanlizi_07"}, {"time": 0.0667, "name": "zhuanlizi/zhuanlizi_08"}, {"time": 0.1, "name": "zhuanlizi/zhuanlizi_09"}, {"time": 0.1333, "name": "zhuanlizi/zhuanlizi_10"}, {"time": 0.1667, "name": "zhuanlizi/zhuanlizi_11"}, {"time": 0.2, "name": "zhuanlizi/zhuanlizi_12"}, {"time": 0.2333, "name": "zhuanlizi/zhuanlizi_13"}, {"time": 0.2667, "name": "zhuanlizi/zhuanlizi_14"}, {"time": 0.3, "name": "zhuanlizi/zhuanlizi_15"}, {"time": 0.3333, "name": "zhuanlizi/zhuanlizi_16"}, {"time": 0.3667, "name": "zhuanlizi/zhuanlizi_17"}, {"time": 0.4, "name": "zhuanlizi/zhuanlizi_18"}, {"time": 0.4333, "name": "zhuanlizi/zhuanlizi_19"}, {"time": 0.4667, "name": "zhuanlizi/zhuanlizi_20"}, {"time": 0.5, "name": "zhuanlizi/zhuanlizi_21"}, {"time": 0.5333, "name": "zhuanlizi/zhuanlizi_22"}, {"time": 0.5667, "name": "zhuanlizi/zhuanlizi_23"}, {"time": 0.6, "name": "zhuanlizi/zhuanlizi_24"}, {"time": 0.6333, "name": "zhuanlizi/zhuanlizi_25"}, {"time": 0.6667, "name": "zhuanlizi/zhuanlizi_26"}, {"time": 0.7, "name": "zhuanlizi/zhuanlizi_27"}, {"time": 0.7333, "name": "zhuanlizi/zhuanlizi_28"}, {"time": 0.7667, "name": "zhuanlizi/zhuanlizi_29"}, {"time": 0.8, "name": "zhuanlizi/zhuanlizi_30"}, {"time": 0.8333, "name": null}]}, "zhuan": {"attachment": [{"name": null}, {"time": 0.8333, "name": "zhuan"}]}, "zhuanlizi/zhuanlizi_6": {"attachment": [{"name": "zhuanlizi/zhuanlizi_06"}, {"time": 0.0333, "name": "zhuanlizi/zhuanlizi_07"}, {"time": 0.0667, "name": "zhuanlizi/zhuanlizi_08"}, {"time": 0.1, "name": "zhuanlizi/zhuanlizi_09"}, {"time": 0.1333, "name": "zhuanlizi/zhuanlizi_10"}, {"time": 0.1667, "name": "zhuanlizi/zhuanlizi_11"}, {"time": 0.2, "name": "zhuanlizi/zhuanlizi_12"}, {"time": 0.2333, "name": "zhuanlizi/zhuanlizi_13"}, {"time": 0.2667, "name": "zhuanlizi/zhuanlizi_14"}, {"time": 0.3, "name": "zhuanlizi/zhuanlizi_15"}, {"time": 0.3333, "name": "zhuanlizi/zhuanlizi_16"}, {"time": 0.3667, "name": "zhuanlizi/zhuanlizi_17"}, {"time": 0.4, "name": "zhuanlizi/zhuanlizi_18"}, {"time": 0.4333, "name": "zhuanlizi/zhuanlizi_19"}, {"time": 0.4667, "name": "zhuanlizi/zhuanlizi_20"}, {"time": 0.5, "name": "zhuanlizi/zhuanlizi_21"}, {"time": 0.5333, "name": "zhuanlizi/zhuanlizi_22"}, {"time": 0.5667, "name": "zhuanlizi/zhuanlizi_23"}, {"time": 0.6, "name": "zhuanlizi/zhuanlizi_24"}, {"time": 0.6333, "name": "zhuanlizi/zhuanlizi_25"}, {"time": 0.6667, "name": "zhuanlizi/zhuanlizi_26"}, {"time": 0.7, "name": "zhuanlizi/zhuanlizi_27"}, {"time": 0.7333, "name": "zhuanlizi/zhuanlizi_28"}, {"time": 0.7667, "name": "zhuanlizi/zhuanlizi_29"}, {"time": 0.8, "name": "zhuanlizi/zhuanlizi_30"}, {"time": 0.8333, "name": null}]}}, "bones": {"bone3": {"scale": [{"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 1.174, "y": 1.174}]}}, "deform": {"default": {"zhuan": {"zhuan": [{"time": 1.3333}]}}}}}}