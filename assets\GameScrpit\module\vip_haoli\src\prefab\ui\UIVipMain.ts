import { _decorator, clamp, EventTouch, instantiate, Label, Node, RichText, ScrollView, Sprite, UITransform } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { UIMgr } from "../../../../../lib/ui/UIMgr";
import { VipModule } from "../../VipModule";
import ResMgr from "../../../../../lib/common/ResMgr";
import { HeroModule } from "../../../../hero/HeroModule";
import { FriendModule } from "../../../../friend/FriendModule";
import { PlayerModule } from "../../../../player/PlayerModule";
import { ItemCtrl } from "../../../../../game/common/ItemCtrl";
import { RechargeRewardTakeResponse } from "../../../../../game/net/protocol/Activity";
import MsgMgr from "../../../../../lib/event/MsgMgr";
import MsgEnum from "../../../../../game/event/MsgEnum";
import { JsonMgr } from "../../../../../game/mgr/JsonMgr";
import { PlayerRouteName } from "../../../../player/PlayerConstant";
import { IntValueList } from "../../../../../game/net/protocol/ExternalMessage";
import { IConfigFriednFame, IConfigVipReward } from "../../../../../game/JsonDefine";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { VipAudioName } from "../../VipConfig";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import GuideMgr from "../../../../../ext_guide/GuideMgr";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { routeConfig } from "db://assets/platform/src/core/managers/RouteTableManager";
import { NodeTool } from "db://assets/GameScrpit/lib/utils/NodeTool";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Thu Nov 21 2024 16:06:28 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/ui_vip/UIVipMain.ts
 *
 */

@ccclass("UIVipMain")
@routeConfig({
  bundle: BundleEnum.BUNDLE_G_VIP,
  url: "prefab/ui/UIVipMain",
  nextHop: [],
  exit: "dialog_close",
})
export class UIVipMain extends BaseCtrl {
  public playShowAni: boolean = true;
  private _index: number = 0;
  private _takeList: number[];

  //=================================================
  public init(args: any): void {
    super.init(args);
  }
  protected start(): void {
    super.start();
    this.refreshUI();
    VipModule.api.rechargeGoods((data: IntValueList) => {
      log.log(data);
      this._takeList = data.values;
      this.refreshUI();
    });
  }
  private refreshUI() {
    let vipRewardList: IConfigVipReward[] = VipModule.config.getVipRewardList();
    if (this.getNode("node_select_tab").children.length > 0) {
      this.getNode("node_select_tab").children.forEach((val: Node, index: number) => {
        if (this._index == index) {
          val.getChildByName("select").active = true;
          val.setScale(1.3, 1.3);
        } else {
          val.setScale(1, 1);
          val.getChildByName("select").active = false;
        }
      });
    } else {
      vipRewardList.forEach((val: IConfigVipReward, index: number) => {
        // log.log("vipRewardList", vipRewardList);
        vipRewardList.push(val);
        let item = instantiate(this.getNode("item_friend_tab"));
        item.getChildByPath("bg_biaotidi/lbl_vip_lv").getComponent(Label).string = `${val.id}`;
        if (this._index == index) {
          item.getChildByName("select").active = true;
        } else {
          item.getChildByName("select").active = false;
        }
        let itemPath = `autoItem/item_${vipRewardList[index].reward1List[0][0]}`;
        if (vipRewardList[index].reward1List[0][0] > 20000) {
          itemPath = `autoItem/item_fr_${vipRewardList[index].reward1List[0][0]}`;
        }
        ResMgr.setSpriteFrame(
          BundleEnum.BUNDLE_COMMON_ITEM,
          itemPath,
          item.getChildByPath("Mask/bg_friend_img").getComponent(Sprite)
        );
        this.getNode("node_select_tab").addChild(item);
      });
    }
    let targetNode = this.getNode("node_select_tab").children[this._index];
    // let distance = curItem.getPosition().x / this.getNode("node_select_tab").getComponent(UITransform).width;
    // 获取 ScrollView 的视口大小
    let scrollViewSize = this.getNode("node_select_list").getComponent(UITransform).contentSize;
    // 获取 ScrollView 内容的大小
    let contentSize = this.getNode("node_select_tab").getComponent(UITransform).contentSize;

    let cons = scrollViewSize.width / 2 - 57.5;
    let scrollX = clamp(targetNode.position.x, cons, contentSize.width - cons);
    if (targetNode.position.x > cons && targetNode.position.x < contentSize.width - cons) {
      let distance = (scrollX - scrollViewSize.width / 2) / (contentSize.width - scrollViewSize.width);
      this.getNode("node_select_list").getComponent(ScrollView).scrollToPercentHorizontal(distance, 0.1, true);
    } else if (targetNode.position.x < cons) {
      this.getNode("node_select_list").getComponent(ScrollView).scrollToPercentHorizontal(0, 0.2, true);
    } else if (targetNode.position.x > contentSize.width - cons) {
      this.getNode("node_select_list").getComponent(ScrollView).scrollToPercentHorizontal(1, 0.2, true);
    }
    this.getNode("lbl_exp").getComponent(Label).string = `${vipRewardList[this._index].expNeed}`;

    this.getNode("btn_yilingqu").active = false;
    this.getNode("btn_get").active = false;
    this.getNode("btn_go").active = false;
    let nextGap = vipRewardList[this._index].expNeed - PlayerModule.data.getPlayerInfo().vipExp;
    if (nextGap > 0) {
      this.getNode("node_next_gap").active = true;
      let str = `<color=#becff0><outline color=#183d65 width=2>再获得<color=#ffe68e><outline color=#654018 width=2>贵族点${nextGap}</outline></color>到达</outline></color>`;
      this.getNode("lbl_next_gap").getComponent(RichText).string = str;
      this.getNode("lbl_next_vip").getComponent(Label).string = `${vipRewardList[this._index].id}`;
      this.getNode("btn_go").active = true;
    } else {
      this.getNode("node_next_gap").active = false;
      //已经达成
      let isTake = false;
      for (let i = 0; this._takeList && i < this._takeList.length; i++) {
        if (this._takeList[i] == this._index) {
          isTake = true;
          break;
        }
      }
      if (isTake) {
        this.getNode("btn_yilingqu").active = true;
      } else {
        this.getNode("btn_get").active = true;
      }
    }
    if (vipRewardList[this._index].reward1List[0][0] > 20000) {
      this.getNode("node_friend").active = true;
      this.getNode("node_hero").active = false;
      this.loadFriend(vipRewardList[this._index].reward1List[0][0]);
    } else {
      this.getNode("node_friend").active = false;
      this.getNode("node_hero").active = true;
      this.loadHero(vipRewardList[this._index].reward1List[0][0]);
    }
    this.refreshItem(vipRewardList[this._index]);
    this.getNode("lbl_cur_exp").getComponent(Label).string = `当前贵族点:${PlayerModule.data.getPlayerInfo().vipExp}`;
    this.updateProgress();
  }

  private updateProgress() {
    let vipRewardList: IConfigVipReward[] = VipModule.config.getVipRewardList();
    let index = -1;
    for (let i = 0; i < vipRewardList.length; i++) {
      if (PlayerModule.data.getPlayerInfo().vipExp >= vipRewardList[i].expNeed) {
        index = i;
        let item = this.getNode("node_select_tab").children[i];
        if (item) {
          let point = item.getChildByPath("node_point/node_progressed_point");
          if (point) {
            point.active = true;
          }
        }
      }
    }
    let progressStart = NodeTool.getBorderLeft(this.getNode("node_select_tab"));
    if (index == -1) {
      let progressMax = this.getNode("node_select_tab").children[0].position.x - progressStart;
      let ratio = PlayerModule.data.getPlayerInfo().vipExp / vipRewardList[0].expNeed;
      this.getNode("node_fg").getComponent(UITransform).width = progressMax * ratio;
    } else {
      let progressBase = this.getNode("node_select_tab").children[index].position.x - progressStart;
      let progressMax = progressBase;
      let ratio = 0;
      if (index < this.getNode("node_select_tab").children.length) {
        progressMax = this.getNode("node_select_tab").children[index + 1].position.x - progressStart;
        ratio =
          (PlayerModule.data.getPlayerInfo().vipExp - vipRewardList[index].expNeed) /
          (vipRewardList[index + 1].expNeed - vipRewardList[index].expNeed);
      }

      this.getNode("node_fg").getComponent(UITransform).width = progressBase + (progressMax - progressBase) * ratio;
    }
  }

  private refreshItem(vipReward: IConfigVipReward) {
    this.getNode("layout_item_content").removeAllChildren();
    vipReward.reward2List.forEach((val: number[], index: number) => {
      let item = instantiate(this.getNode("Item"));
      item.getComponent(ItemCtrl).setItemId(val[0], val[1], false);
      this.getNode("layout_item_content").addChild(item);
    });
  }
  private loadHero(heroId: number) {
    this.getNode("node_img").destroyAllChildren();
    ResMgr.setNodePrefab(
      BundleEnum.BUNDLE_COMMON_HERO_FULL,
      `prefabs/hero_${heroId}`,
      this.getNode("node_img"),
      (node: Node) => {
        node.setScale(1.4, 1.4);
      }
    );
    HeroModule.service.updateHeroRaceIcon(this.getNode("bg_race").getComponent(Sprite), heroId);
    let heroInfo = HeroModule.config.getHeroInfo(heroId);
    this.getNode("lbl_hero_zizhi").getComponent(Label).string = `资质:${heroInfo.quality}`;
    HeroModule.service.updateHeroColorBg(this.getNode("bg_color").getComponent(Sprite), heroId);
    this.getNode("lbl_name").getComponent(Label).string = `${heroInfo.name}`;
    this.getNode("btn_zhandouyulan").active = heroInfo.skinId > 0;
  }
  private loadFriend(friendId: number) {
    this.getNode("node_img").destroyAllChildren();
    ResMgr.setNodePrefab(
      BundleEnum.BUNDLE_COMMON_FRIEND,
      `prefab/friend_${friendId}`,
      this.getNode("node_img"),
      (node: Node) => {
        node.setScale(1.2, 1.2);
      }
    );
    let friendInfo = FriendModule.config.getFriendById(friendId);
    this.getNode("talent").getComponent(Label).string = `${friendInfo.talentFirst}`;
    this.getNode("friendship").getComponent(Label).string = `${friendInfo.friendlyFirst}`;
    ResMgr.setSpriteFrame(
      BundleEnum.BUNDLE_COMMON_UI,
      `atlas_imgs/pingzhi_${friendInfo.color}`,
      this.getNode("bg_color").getComponent(Sprite)
    );
    this.getNode("lbl_name").getComponent(Label).string = `${friendInfo.name}`;
  }
  private on_click_item(e: EventTouch) {
    AudioMgr.instance.playEffect(1364);

    // log.log("on_click_item");
    let target: Node = e.target;
    let index = target.getSiblingIndex();
    if (this._index == index) {
      return;
    }

    this._index = index;
    this.refreshUI();
  }
  private on_click_btn_back() {
    AudioMgr.instance.playEffect(520);
    UIMgr.instance.back();
  }
  private on_click_btn_left() {
    AudioMgr.instance.playEffect(1363);
    this._index--;
    if (this._index < 0) {
      this._index = VipModule.config.getVipRewardList().length - 1;
    }
    this.refreshUI();
  }
  private on_click_btn_right() {
    AudioMgr.instance.playEffect(1363);
    this._index++;
    if (this._index >= VipModule.config.getVipRewardList().length) {
      this._index = 0;
    }
    this.refreshUI();
  }
  private on_click_btn_go() {
    //充值
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    GuideMgr.startGuide({ stepId: 67 });
  }
  private on_click_btn_get() {
    //
    AudioMgr.instance.playEffect(VipAudioName.Effect.点击领取按钮);
    //领取
    VipModule.api.takeRechargeGoods(this._index, (data: RechargeRewardTakeResponse) => {
      this._takeList = data.takeList;
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardList });
      this.refreshUI();
    });
  }
  private on_click_node_hero_tips() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this.getNode("node_hero_tips").active = false;
  }
  private on_click_btn_yinji() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this.getNode("node_hero_tips").active = true;
  }
  private on_click_btn_meiming() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let vipRewardList: IConfigVipReward[] = VipModule.config.getVipRewardList();
    let friend = FriendModule.config.getFriendById(vipRewardList[this._index].reward1List[0][0]);
    let fame = this.formatFameEffect(friend.fameIdList[0]);
    this.getNode("node_friend_tips").getComponentInChildren(RichText).string = fame;
    this.getNode("node_friend_tips").active = true;
  }
  private on_click_node_friend_tips() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this.getNode("node_friend_tips").active = false;
  }

  private on_click_btn_zhandouyulan() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let vipRewardList: IConfigVipReward[] = VipModule.config.getVipRewardList();
    let heroInfo = HeroModule.config.getHeroInfo(vipRewardList[this._index].reward1List[0][0]);
    UIMgr.instance.showDialog(PlayerRouteName.UIWatchSkill, { roleId: heroInfo.skinId });
  }

  private formatFameEffect(fameId: number) {
    let fameEffect: IConfigFriednFame = JsonMgr.instance.jsonList.c_friednFame[fameId];
    let attr = JsonMgr.instance.jsonList.c_attribute[fameEffect.attrId];
    let strList = "";
    for (let i = 0; i < fameEffect.costList.length; i++) {
      let fameName = FriendModule.config.getFriendBellesByFameLevel(fameEffect.costList[i]);
      if (fameId == 1008 && i < fameEffect.addList.length) {
        strList += `<color=#FFFFFF>(${fameName.name})${attr.name
          .replace("+%s", `+${fameEffect.addList[i][0]}`)
          .replace("+%s", `+${fameEffect.addList[i][1]}`)
          .replace("+%s", `+${fameEffect.addList[i][2]}`)}</color><br/>`;
      } else if (fameId != 1008) {
        strList += `<color=#FFFFFF>(${fameName.name}) ${attr.name} +${fameEffect.addList[0][i]}</color><br/>`;
      }
    }
    // log.log(strList);
    return strList.slice(0, -5);
  }
}
