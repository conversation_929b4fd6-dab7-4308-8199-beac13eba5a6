{"skeleton": {"hash": "lHOFDseps7FWqzlI3sBi47c1zHg=", "spine": "3.8.75", "x": -384.9, "y": -763.25, "width": 771.36, "height": 1523.56, "images": "", "audio": ""}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "y": 142.43}, {"name": "ziall", "parent": "bone", "x": 1, "y": 69.99}, {"name": "zi1", "parent": "ziall", "x": -141.63, "y": 89.09, "scaleX": 0.9235, "scaleY": 0.9235, "color": "7cff00ff"}, {"name": "zi2", "parent": "ziall", "x": -46.64, "y": 88.66, "scaleX": 0.9235, "scaleY": 0.9235, "color": "7cff00ff"}, {"name": "zi3", "parent": "ziall", "x": 44.88, "y": 86.47, "scaleX": 0.9235, "scaleY": 0.9235, "color": "7cff00ff"}, {"name": "zi4", "parent": "ziall", "x": 140.89, "y": 89.09, "scaleX": 0.9235, "scaleY": 0.9235, "color": "7cff00ff"}, {"name": "di", "parent": "bone", "y": 158.27}, {"name": "jian", "parent": "bone", "y": 158.27}, {"name": "ziall2", "parent": "ziall", "x": -138.96, "y": 40.32}, {"name": "xian", "parent": "root", "y": 213.85}, {"name": "xian1", "parent": "xian", "x": -762.13, "y": 47.27, "scaleX": 1.8152, "scaleY": 1.3161}, {"name": "xian2", "parent": "xian", "x": -762.13, "y": 119.13, "scaleX": 1.8152, "scaleY": 0.43}, {"name": "xian3", "parent": "xian", "x": -772.03, "y": 166.6, "scaleX": 1.8152, "scaleY": 1.3161}, {"name": "xian4", "parent": "xian", "x": -760.39, "y": 7.41, "scaleX": 1.8152, "scaleY": 1.3161}, {"name": "xian5", "parent": "xian", "x": -772.03, "y": 157.83, "scaleX": 1.8152, "scaleY": 0.2058}, {"name": "xian6", "parent": "xian", "x": -772.03, "y": 86.76, "scaleX": 1.8152, "scaleY": 1.1059}, {"name": "xian7", "parent": "xian", "x": -772.03, "y": 78.31, "scaleX": 1.8152, "scaleY": 0.2058}, {"name": "spl_img_sz", "parent": "root", "scaleX": 9.1909, "scaleY": 9.1909}, {"name": "bone2", "parent": "root"}], "slots": [{"name": "cut", "bone": "root", "attachment": "cut"}, {"name": "hou2", "bone": "di", "attachment": "hou2"}, {"name": "jian", "bone": "jian", "attachment": "jian"}, {"name": "zi_1_4", "bone": "zi4", "attachment": "zi_1_4"}, {"name": "zi_1_3v", "bone": "zi3", "attachment": "zi_1_3v"}, {"name": "zi_1_2", "bone": "zi2", "attachment": "zi_1_2"}, {"name": "zi_1_1", "bone": "zi1", "attachment": "zi_1_1"}, {"name": "kaic03", "bone": "spl_img_sz", "blend": "additive"}, {"name": "light", "bone": "ziall2", "color": "ffffff00", "attachment": "light", "blend": "additive"}, {"name": "xian", "bone": "xian1", "color": "ffffffa2", "attachment": "xian"}, {"name": "xian2", "bone": "xian2", "color": "ffffffae", "attachment": "xian"}, {"name": "xian3", "bone": "xian3", "color": "ff9a00ae", "attachment": "xian", "blend": "additive"}, {"name": "xian6", "bone": "xian6", "color": "ff9a00ae", "attachment": "xian", "blend": "additive"}, {"name": "xian5", "bone": "xian5", "color": "ff9a00ae", "attachment": "xian", "blend": "additive"}, {"name": "xian7", "bone": "xian7", "color": "ff9a00ae", "attachment": "xian", "blend": "additive"}, {"name": "xian4", "bone": "xian4", "color": "ffffffaf", "attachment": "xian"}], "skins": [{"name": "default", "attachments": {"zi_1_3v": {"zi_1_3v": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [48, -53.5, -47, -53.5, -47, 53.5, 48, 53.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 65, "height": 65}}, "cut": {"cut": {"type": "clipping", "end": "cut", "vertexCount": 4, "vertices": [-383.66, 760.11, 385.3, 760.32, 386.46, -763.25, -384.9, -761.68], "color": "ce3a3aff"}}, "light": {"light": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [74.18, -25.01, -74.18, -25.01, -74.18, 121.49, 74.18, 121.49], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 48, "height": 47}}, "xian2": {"xian": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [182.02, -5.1, -171.98, -5.1, -171.98, 3.9, 182.02, 3.9], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 212, "height": 5}}, "zi_1_1": {"zi_1_1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [53.5, -53, -52.5, -53, -52.5, 54, 53.5, 54], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 65, "height": 63}}, "zi_1_2": {"zi_1_2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [47.5, -52.5, -46.5, -52.5, -46.5, 52.5, 47.5, 52.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 60, "height": 66}}, "xian3": {"xian": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [182.02, -5.1, -171.98, -5.1, -171.98, 3.9, 182.02, 3.9], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 212, "height": 5}}, "zi_1_4": {"zi_1_4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [57.5, -51.5, -57.5, -51.5, -57.5, 51.5, 57.5, 51.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 71, "height": 63}}, "xian5": {"xian": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [182.02, -5.1, -171.98, -5.1, -171.98, 3.9, 182.02, 3.9], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 212, "height": 5}}, "xian6": {"xian": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [182.02, -5.1, -171.98, -5.1, -171.98, 3.9, 182.02, 3.9], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 212, "height": 5}}, "xian7": {"xian": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [182.02, -5.1, -171.98, -5.1, -171.98, 3.9, 182.02, 3.9], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 212, "height": 5}}, "xian": {"xian": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [182.02, -5.1, -171.98, -5.1, -171.98, 3.9, 182.02, 3.9], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 212, "height": 5}}, "hou2": {"hou2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [922.86, -167, -922.86, -167, -922.86, 178, 922.86, 178], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 225, "height": 103}}, "jian": {"jian": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [264, -93, -264, -93, -264, 93, 264, 93], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 316, "height": 111}, "jian_mohu": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [338, -89, -338, -89, -338, 89, 338, 89], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 202, "height": 53}}, "kaic03": {"kaic03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [44, -87, -43, -87, -43, 88, 44, 88], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 87, "height": 175}, "kaic05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [44, -87, -43, -87, -43, 88, 44, 88], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 87, "height": 175}, "kaic07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [44, -87, -43, -87, -43, 88, 44, 88], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 87, "height": 175}, "kaic09": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [44, -87, -43, -87, -43, 88, 44, 88], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 87, "height": 175}, "kaic11": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [44, -87, -43, -87, -43, 88, 44, 88], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 87, "height": 175}, "kaic13": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [44, -87, -43, -87, -43, 88, 44, 88], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 87, "height": 175}, "kaic15": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [44, -87, -43, -87, -43, 88, 44, 88], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 87, "height": 175}, "kaic17": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [44, -87, -43, -87, -43, 88, 44, 88], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 87, "height": 175}, "kaic19": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [44, -87, -43, -87, -43, 88, 44, 88], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 87, "height": 175}, "kaic21": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [44, -87, -43, -87, -43, 88, 44, 88], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 87, "height": 175}, "kaic22": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [44, -87, -43, -87, -43, 88, 44, 88], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 87, "height": 175}}, "xian4": {"xian": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [182.02, -5.1, -171.98, -5.1, -171.98, 3.9, 182.02, 3.9], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 212, "height": 5}}}}], "events": {"appear": {}}, "animations": {"animation": {"slots": {"jian": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.2333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff"}], "attachment": [{"time": 0.9667, "name": "jian_mohu"}]}, "zi_1_3v": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "color": "ffffff00"}]}, "light": {"color": [{"time": 0.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.4, "color": "ffffff92", "curve": "stepped"}, {"time": 0.8667, "color": "ffffff92", "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "color": "ffffff00"}]}, "hou2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.2333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.0333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "color": "ffffff00"}]}, "zi_1_2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.2333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "color": "ffffff00"}]}, "kaic03": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "color": "ffffffff"}], "attachment": [{"name": "kaic22"}, {"time": 0.1, "name": "kaic21"}, {"time": 0.1333, "name": "kaic19"}, {"time": 0.2, "name": "kaic17"}, {"time": 0.2333, "name": "kaic15"}, {"time": 0.2667, "name": "kaic13"}, {"time": 0.3, "name": "kaic11"}, {"time": 0.3333, "name": "kaic09"}, {"time": 0.3667, "name": "kaic07"}, {"time": 0.4, "name": "kaic05"}, {"time": 0.4333, "name": "kaic03"}, {"time": 0.4667, "name": null}]}, "zi_1_1": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.4, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "color": "ffffff00"}]}, "zi_1_4": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.6, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "color": "ffffff00"}]}}, "bones": {"zi4": {"translate": [{"time": 0.5333, "curve": 0.372, "c3": 0.703, "c4": 0.35}, {"time": 0.5667, "x": 16.63, "y": 5.78, "curve": 0.607, "c2": 0.13, "c3": 0.336, "c4": 0.97}, {"time": 0.8}], "scale": [{"time": 0.3333, "x": 1.611, "y": 1.611, "curve": "stepped"}, {"time": 0.5333, "x": 1.611, "y": 1.611, "curve": 0.425, "c2": -0.45, "c3": 0.464, "c4": 3.26}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1.072, "y": 1.072, "curve": 0.25, "c3": 0.75}, {"time": 0.9}]}, "zi2": {"scale": [{"time": 0.3333, "x": 1.611, "y": 1.611, "curve": "stepped"}, {"time": 0.4, "x": 1.611, "y": 1.611, "curve": 0.425, "c2": -0.45, "c3": 0.464, "c4": 3.26}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": 1.072, "y": 1.072, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "zi1": {"scale": [{"time": 0.3333, "x": 1.611, "y": 1.611, "curve": 0.425, "c2": -0.45, "c3": 0.464, "c4": 3.26}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 1.072, "y": 1.072, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "ziall2": {"translate": [{"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": 281.52}]}, "zi3": {"scale": [{"time": 0.3333, "x": 1.611, "y": 1.611, "curve": "stepped"}, {"time": 0.4667, "x": 1.611, "y": 1.611, "curve": 0.425, "c2": -0.45, "c3": 0.464, "c4": 3.26}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 1.072, "y": 1.072, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}]}, "bone": {"translate": [{"time": 1.0333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 763.76, "curve": "stepped"}, {"time": 1.8, "x": 763.76}]}, "xian1": {"translate": [{"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1500.79, "curve": "stepped"}, {"time": 0.8667}]}, "xian2": {"translate": [{"time": 0.5667, "x": 1223.94, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6333, "x": 1500.79, "curve": "stepped"}, {"time": 0.6667, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.8667, "x": 1223.94}]}, "xian3": {"translate": [{"time": 0.5667, "x": 503.93, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.7333, "x": 1500.79, "curve": "stepped"}, {"time": 0.7667, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.8667, "x": 503.93}]}, "xian7": {"translate": [{"time": 0.5667, "x": 748.95, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7, "x": 1500.79, "curve": "stepped"}, {"time": 0.7333, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.8333, "x": 503.93, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.8667, "x": 748.95}]}, "xian5": {"translate": [{"time": 0.5667, "x": 503.93, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.7333, "x": 1500.79, "curve": "stepped"}, {"time": 0.7667, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.8667, "x": 503.93}]}, "xian6": {"translate": [{"time": 0.5667, "x": 748.95, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7, "x": 1500.79, "curve": "stepped"}, {"time": 0.7333, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.8333, "x": 503.93, "curve": 0.329, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.8667, "x": 748.95}]}, "xian4": {"translate": [{"time": 0.5667, "x": 750.39, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7, "x": 1500.79, "curve": "stepped"}, {"time": 0.7333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.8667, "x": 750.39}]}, "xian": {"translate": [{"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 1467.98}]}, "spl_img_sz": {"scale": [{"x": -1, "y": -1}]}, "bone2": {"scale": [{"time": 0.0333, "x": 0.832, "y": 0.832, "curve": "stepped"}, {"time": 0.0667, "x": 0.832, "y": 0.832, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 1.155, "y": 1.155, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}}, "events": [{"time": 0.4, "name": "appear"}]}}}