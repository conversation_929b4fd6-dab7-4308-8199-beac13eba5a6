{"skeleton": {"hash": "jhGCiw6ogd2H06XQFsfBhHNQlZg=", "spine": "3.8.75", "x": -90.04, "y": -227.91, "width": 321.73, "height": 488.95, "images": "./images/", "audio": "E:/1/文件/PSD/动画拆分/大地图建筑/冥族-铁链"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root"}, {"name": "bone2", "parent": "bone", "length": 34.85, "rotation": -89.06, "x": 8.64, "y": 239.35}, {"name": "bone3", "parent": "bone2", "length": 34.85, "x": 34.85}, {"name": "bone4", "parent": "bone3", "length": 34.85, "x": 34.85}, {"name": "bone5", "parent": "bone4", "length": 34.85, "x": 34.85}, {"name": "bone6", "parent": "bone5", "length": 34.85, "x": 34.85}, {"name": "bone7", "parent": "bone6", "length": 34.85, "x": 34.85}, {"name": "bone8", "parent": "bone7", "length": 34.85, "x": 34.85}, {"name": "bone9", "parent": "bone8", "length": 34.85, "x": 34.85}, {"name": "bone10", "parent": "bone9", "length": 34.85, "x": 34.85}, {"name": "bone11", "parent": "bone10", "length": 34.85, "x": 34.85}, {"name": "bone12", "parent": "bone11", "length": 34.85, "x": 34.85}, {"name": "bone13", "parent": "bone12", "length": 34.85, "x": 34.85}, {"name": "bone14", "parent": "bone13", "length": 34.85, "x": 34.85}, {"name": "bone15", "parent": "bone14", "length": 34.85, "x": 34.85}, {"name": "bone16", "parent": "bone15", "length": 34.85, "x": 34.85}, {"name": "bone17", "parent": "bone16", "length": 34.85, "x": 34.85}, {"name": "bone18", "parent": "bone17", "length": 34.85, "x": 34.85}, {"name": "bone19", "parent": "bone18", "length": 34.85, "x": 34.85}, {"name": "bone20", "parent": "bone19", "length": 34.85, "x": 34.85}, {"name": "bone21", "parent": "bone20", "length": 34.85, "x": 34.85}, {"name": "1", "parent": "bone13", "length": 41.78, "rotation": -18.98, "x": 18.86, "y": -10.98}, {"name": "2", "parent": "bone17", "length": 47.93, "rotation": -31.58, "x": 22.97, "y": -4.89}, {"name": "3", "parent": "1", "length": 41.78, "x": 41.78}, {"name": "4", "parent": "2", "length": 47.93, "x": 47.93}, {"name": "bone22", "parent": "root", "x": -19.63, "y": 40.96}], "slots": [{"name": "<PERSON><PERSON><PERSON>", "bone": "root", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "1", "bone": "root", "attachment": "1"}, {"name": "2", "bone": "root", "attachment": "2"}, {"name": "3", "bone": "root", "attachment": "3"}, {"name": "lujing", "bone": "root", "attachment": "lujing"}, {"name": "tl", "bone": "bone22", "attachment": "tl", "blend": "additive"}], "path": [{"name": "lujing", "bones": ["bone2", "bone3", "bone4", "bone5", "bone6", "bone7", "bone8", "bone9", "bone10", "bone11", "bone12", "bone13", "bone14", "bone15", "bone16", "bone17", "bone18", "bone19", "bone20", "bone21"], "target": "lujing", "spacing": 7.2}], "skins": [{"name": "default", "attachments": {"lujing": {"lujing": {"type": "path", "lengths": [59.81, 120.27, 275, 348.12, 829.91, 1386.22], "vertexCount": 18, "vertices": [34.42, 252.05, 8.64, 239.35, -5.75, 232.26, -34.5, 231.37, -49.91, 238.3, -64.46, 244.84, -47.94, 270.5, -13.26, 267.89, 37.3, 264.09, -3.91, 181.64, -67.65, 157.89, -111.36, 141.61, -54.56, 216.47, -46.77, 193.12, -38.99, 169.76, -30.4, -108.83, 178.93, -211.76, 388.26, -314.7]}}, "tl": {"tl": {"type": "mesh", "color": "f3ff00ff", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [251.32, -268.87, -63.68, -268.87, -63.68, 214.13, 251.32, 214.13], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 94, "height": 144}}, "1": {"1": {"type": "mesh", "uvs": [0.25301, 0.28454, 0.34645, 0.0472, 0.43889, 0.13168, 0.53782, 0.22209, 0.61323, 0.12372, 0.74065, 0.12412, 0.90893, 0.12465, 0.99704, 0.49304, 0.97552, 0.57573, 0.89093, 0.71784, 0.8124, 0.84974, 0.76147, 0.72612, 0.64333, 0.72583, 0.46614, 0.7254, 0.32424, 0.76763, 0.15216, 0.53528, 0.12024, 1, 0.11123, 1, 0.10226, 0.55443, 0.17638, 0.47914, 0.32397, 0.40066, 0.47616, 0.40066, 0.63335, 0.40696, 0.83046, 0.45736], "triangles": [21, 2, 3, 14, 19, 20, 14, 20, 13, 20, 2, 21, 10, 11, 9, 11, 23, 9, 23, 11, 22, 12, 13, 22, 11, 12, 22, 9, 23, 8, 8, 23, 7, 23, 6, 7, 22, 5, 23, 23, 5, 6, 3, 4, 22, 22, 4, 5, 21, 3, 22, 22, 13, 21, 20, 0, 1, 19, 0, 20, 13, 20, 21, 20, 1, 2, 15, 16, 18, 16, 17, 18, 18, 19, 15, 15, 19, 14], "vertices": [2, 3, 25.67, -6.86, 0.264, 2, 57.41, -33.09, 0.736, 2, 3, 17.31, -17.32, 0.024, 2, 44.75, -37.43, 0.976, 1, 2, 37.87, -30.28, 1, 1, 2, 30.5, -22.62, 1, 1, 2, 21.93, -22.78, 1, 1, 2, 10.4, -17.08, 1, 2, 3, -39.5, -20.34, 0.00024, 2, -4.84, -9.55, 0.99976, 1, 2, -6.31, 7.6, 1, 1, 2, -2.9, 9.61, 1, 1, 2, 7.28, 10.93, 1, 1, 2, 16.72, 12.16, 1, 1, 2, 19.15, 5.45, 1, 2, 3, -15.42, 6.45, 0.00537, 2, 29.85, 0.17, 0.99463, 1, 2, 45.9, -7.76, 1, 2, 3, 16.44, 11.58, 0.432, 2, 59.5, -12.58, 0.568, 1, 3, 34.72, 4.2, 1, 1, 3, 35.93, 23.03, 1, 1, 3, 36.83, 23.13, 1, 1, 3, 39.65, 5.51, 1, 2, 3, 32.53, 1.71, 0.272, 2, 67.8, -29.53, 0.728, 2, 3, 18.05, -3.02, 0.208, 2, 53.04, -25.76, 0.792, 1, 2, 39.25, -18.96, 1, 1, 2, 25.12, -11.72, 1, 2, 3, -33.05, -6.26, 0.00076, 2, 8.15, -1.11, 0.99924], "hull": 20, "edges": [6, 8, 12, 14, 14, 16, 20, 22, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 8, 10, 10, 12, 2, 4, 4, 6, 2, 0, 0, 38, 22, 24, 24, 26, 16, 18, 18, 20], "width": 101, "height": 40}}, "2": {"2": {"type": "mesh", "uvs": [0.58407, 0.33796, 0.64594, 0.23906, 0.72539, 0.11205, 0.79212, 0.00538, 0.89426, 0.02895, 0.95602, 0.07935, 0.99039, 0.17174, 0.99091, 0.2254, 0.92436, 0.30413, 0.80919, 0.44037, 0.7447, 0.51666, 0.69038, 0.57137, 0.55804, 0.70466, 0.4307, 0.83291, 0.28143, 0.92334, 0.15489, 1, 0.14323, 1, 0.0621, 0.94108, 0.00034, 0.87046, 0.05606, 0.74676, 0.13903, 0.71572, 0.25491, 0.62729, 0.30141, 0.59181, 0.44258, 0.48407, 0.51069, 0.45527], "triangles": [21, 22, 13, 14, 21, 13, 20, 21, 14, 17, 19, 20, 17, 20, 14, 18, 19, 17, 15, 16, 17, 14, 15, 17, 10, 0, 9, 11, 24, 0, 10, 11, 0, 12, 24, 11, 23, 24, 12, 22, 23, 12, 13, 22, 12, 5, 2, 4, 8, 5, 6, 8, 6, 7, 8, 2, 5, 4, 2, 3, 8, 1, 2, 9, 1, 8, 0, 1, 9], "vertices": [3, 6, 33.34, -24.78, 0.17382, 7, -1.25, -16.64, 0.82601, 8, -38.43, -22.86, 0.00017, 2, 6, 22.74, -21.43, 0.60031, 7, -12.29, -17.95, 0.39969, 2, 6, 9.12, -17.13, 0.93961, 7, -26.47, -19.63, 0.06039, 2, 6, -2.32, -13.52, 0.99703, 7, -38.38, -21.04, 0.00297, 1, 6, -3.18, -3.01, 1, 1, 6, -0.48, 4.31, 1, 1, 6, 6.76, 10.1, 1, 1, 6, 11.53, 11.58, 1, 1, 6, 20.47, 7.24, 1, 2, 6, 35.94, -0.27, 0.98179, 7, -8.96, 6.77, 0.01821, 2, 6, 44.61, -4.47, 0.13014, 7, 0.67, 6.5, 0.86986, 1, 7, 8.11, 5.69, 1, 1, 7, 26.23, 3.72, 1, 2, 7, 43.67, 1.82, 0.29902, 8, -1.19, 8.32, 0.70098, 1, 8, 16.06, 7.63, 1, 1, 8, 30.68, 7.04, 1, 1, 8, 31.69, 6.43, 1, 1, 8, 35.82, -2.52, 1, 1, 8, 37.71, -11.38, 1, 1, 8, 26.91, -18.26, 1, 1, 8, 18.25, -16.34, 1, 2, 7, 40.86, -24.12, 0.06481, 8, 3.98, -17.25, 0.93519, 2, 7, 35.29, -22.74, 0.21556, 8, -1.75, -17.62, 0.78444, 2, 7, 18.38, -18.53, 0.8323, 8, -19.14, -18.72, 0.1677, 3, 6, 45.92, -28.75, 0.00067, 7, 11.85, -15.09, 0.95462, 8, -26.41, -17.42, 0.04471], "hull": 25, "edges": [6, 8, 8, 10, 10, 12, 12, 14, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 46, 48, 2, 0, 0, 48, 44, 46, 40, 42, 42, 44, 26, 28, 28, 30, 24, 26, 20, 22, 22, 24, 18, 20, 14, 16, 16, 18, 2, 4, 4, 6], "width": 101, "height": 93}}, "3": {"3": {"type": "mesh", "uvs": [0.04042, 0.01993, 0.04957, 0.04201, 0.05868, 0.05371, 0.08614, 0.06451, 0.09605, 0.08491, 0.07593, 0.12523, 0.10322, 0.15703, 0.09005, 0.21199, 0.11704, 0.23111, 0.11693, 0.28431, 0.13176, 0.28705, 0.16354, 0.31857, 0.18188, 0.35629, 0.19814, 0.38971, 0.19059, 0.40801, 0.22351, 0.42911, 0.2234, 0.46448, 0.26135, 0.48099, 0.26123, 0.51933, 0.28782, 0.53531, 0.31557, 0.57645, 0.34512, 0.62028, 0.37191, 0.66, 0.41531, 0.67602, 0.45059, 0.7293, 0.45035, 0.75835, 0.53259, 0.7851, 0.56339, 0.83717, 0.63633, 0.85332, 0.67334, 0.88582, 0.69247, 0.88547, 0.72803, 0.88965, 0.83322, 0.93272, 0.97823, 0.95503, 0.98356, 0.95517, 0.98619, 0.96317, 0.97607, 0.9548, 0.80415, 0.92853, 0.79698, 0.97149, 0.76845, 0.97085, 0.74551, 0.95493, 0.73162, 0.95707, 0.65823, 0.95069, 0.62254, 0.91539, 0.6001, 0.91527, 0.54615, 0.90942, 0.48028, 0.8527, 0.41255, 0.82505, 0.37945, 0.78121, 0.37675, 0.77763, 0.37579, 0.81485, 0.38423, 0.85107, 0.39508, 0.89767, 0.39541, 0.92372, 0.35213, 0.93468, 0.32382, 0.92898, 0.30086, 0.91404, 0.29546, 0.89788, 0.29545, 0.86243, 0.28892, 0.82514, 0.28242, 0.78805, 0.30583, 0.75926, 0.3023, 0.75057, 0.30222, 0.71932, 0.30218, 0.70815, 0.29299, 0.69368, 0.27938, 0.67229, 0.26143, 0.64406, 0.2289, 0.59292, 0.2014, 0.55721, 0.16414, 0.50884, 0.13841, 0.4626, 0.11179, 0.41476, 0.11065, 0.41272, 0.10633, 0.44617, 0.10074, 0.48935, 0.09305, 0.54884, 0.04611, 0.54803, 0.03096, 0.52253, 0.02666, 0.48187, 0.02218, 0.43938, 0.0273, 0.39941, 0.03176, 0.36456, 0.05645, 0.34539, 0.0716, 0.33363, 0.07217, 0.31523, 0.0346, 0.267, 0.03386, 0.22954, 0.0163, 0.19094, 0.01215, 0.15845, 0.00638, 0.1133, 0.00652, 0.07454, 0.0067, 0.02621, 0.3755, 0.74208, 0.09655, 0.34945, 0.12097, 0.38373, 0.08414, 0.34265, 0.08863, 0.34022, 0.10324, 0.34826, 0.12522, 0.38258, 0.11532, 0.41023, 0.31819, 0.70798, 0.32462, 0.6914, 0.34258, 0.69628, 0.36456, 0.71042, 0.37662, 0.72211, 0.36938, 0.73502, 0.36831, 0.75631, 0.38265, 0.72176, 0.36871, 0.7078, 0.34915, 0.69541, 0.32476, 0.68791, 0.37635, 0.76622], "triangles": [53, 54, 52, 52, 54, 55, 55, 56, 52, 56, 57, 52, 52, 58, 51, 50, 51, 59, 58, 52, 57, 60, 49, 50, 49, 60, 61, 59, 51, 58, 50, 59, 60, 49, 61, 107, 107, 61, 106, 101, 104, 63, 107, 112, 49, 62, 106, 61, 107, 106, 93, 104, 106, 62, 101, 103, 104, 104, 62, 63, 108, 106, 105, 106, 104, 105, 105, 104, 109, 63, 64, 101, 101, 64, 102, 101, 102, 103, 112, 107, 93, 102, 64, 65, 38, 39, 37, 39, 40, 37, 36, 33, 35, 33, 34, 35, 31, 41, 42, 41, 31, 40, 40, 31, 37, 37, 32, 36, 32, 33, 36, 31, 32, 37, 42, 29, 30, 42, 30, 31, 42, 43, 29, 29, 43, 28, 28, 43, 44, 28, 44, 27, 44, 45, 27, 45, 46, 27, 27, 46, 26, 46, 47, 26, 47, 25, 26, 47, 48, 25, 25, 112, 93, 25, 93, 24, 25, 48, 112, 48, 49, 112, 103, 110, 104, 103, 102, 111, 102, 65, 111, 24, 93, 108, 108, 93, 106, 108, 23, 24, 108, 105, 109, 108, 109, 23, 104, 110, 109, 109, 110, 23, 110, 103, 111, 110, 22, 23, 110, 111, 22, 111, 65, 66, 111, 66, 22, 66, 21, 22, 66, 67, 21, 67, 20, 21, 67, 68, 20, 68, 19, 20, 19, 69, 18, 19, 68, 69, 69, 70, 18, 18, 70, 17, 17, 70, 16, 70, 71, 16, 16, 71, 15, 71, 72, 14, 14, 72, 100, 71, 14, 15, 14, 100, 99, 72, 73, 100, 14, 99, 13, 76, 77, 78, 78, 79, 75, 75, 79, 74, 79, 80, 74, 74, 80, 73, 73, 80, 81, 95, 73, 81, 95, 81, 82, 82, 83, 94, 83, 96, 94, 100, 73, 95, 94, 95, 82, 94, 98, 95, 96, 97, 94, 83, 84, 96, 75, 76, 78, 100, 95, 99, 99, 12, 13, 95, 98, 99, 99, 98, 12, 98, 11, 12, 94, 97, 98, 98, 97, 11, 96, 84, 97, 11, 85, 10, 84, 85, 97, 85, 11, 97, 10, 85, 9, 85, 86, 9, 9, 86, 8, 8, 87, 7, 8, 86, 87, 87, 88, 7, 7, 88, 6, 88, 89, 6, 89, 5, 6, 89, 90, 5, 5, 90, 4, 91, 4, 90, 4, 91, 3, 91, 2, 3, 91, 1, 2, 91, 92, 1, 92, 0, 1], "vertices": [2, 10, 13.73, 6.54, 0.99769, 11, -43.98, 1.03, 0.00231, 2, 10, 12.05, -3.54, 0.982, 11, -33.84, 2.3, 0.018, 2, 10, 12.29, -9.4, 0.83271, 11, -28.29, 4.2, 0.16729, 2, 10, 17.55, -17.11, 0.59018, 11, -22.4, 11.44, 0.40982, 2, 10, 16.38, -26.6, 0.38912, 11, -12.97, 13.04, 0.61088, 1, 11, 4.07, 4.74, 1, 1, 11, 19.24, 10.64, 1, 2, 11, 43.04, 3.44, 0.00743, 12, 0.2, 2.66, 0.99257, 1, 12, 10.01, 8.86, 1, 2, 12, 33.41, 4.58, 0.86999, 13, -8.65, 3.93, 0.13001, 2, 12, 35.38, 8.6, 0.48917, 13, -6.46, 7.84, 0.51083, 2, 12, 50.9, 15.19, 0.00331, 13, 9.39, 13.57, 0.99669, 2, 13, 27.03, 14.85, 0.88983, 14, -14.29, 14.48, 0.11017, 2, 13, 42.66, 15.98, 0.35499, 14, 1.38, 14.63, 0.64501, 2, 13, 50.1, 11.95, 0.0647, 14, 8.56, 10.13, 0.9353, 2, 14, 20.38, 16.53, 0.98511, 15, -20.85, 16.58, 0.01489, 2, 14, 35.49, 11.88, 0.54066, 15, -6.11, 10.86, 0.45934, 2, 14, 45.78, 20.28, 0.05732, 15, 4.75, 18.5, 0.94268, 2, 15, 20.72, 12.29, 0.95846, 16, -20.79, 12.33, 0.04154, 2, 15, 30.18, 16.93, 0.72908, 16, -10.99, 16.19, 0.27092, 2, 15, 50.24, 17.84, 0.02908, 16, 9.08, 15.45, 0.97092, 2, 16, 30.46, 14.66, 0.73566, 17, -10.52, 13.78, 0.26434, 1, 17, 8.71, 11.27, 1, 2, 17, 21.37, 18.39, 0.91515, 18, -18.89, 18.3, 0.08485, 2, 17, 47.06, 14.86, 0.18051, 18, 6.3, 12.12, 0.81949, 2, 17, 58.14, 8.1, 0.00128, 18, 16.62, 4.24, 0.99872, 2, 18, 40.59, 16.12, 0.10883, 19, 0, 13.8, 0.89117, 2, 19, 23.01, 4.19, 0.99301, 20, -18.93, 3.96, 0.00699, 2, 19, 42.9, 14.53, 0.05579, 20, 2.1, 11.73, 0.94421, 2, 20, 19.61, 7.25, 0.96745, 21, -21.33, 7.36, 0.03255, 2, 20, 23.84, 10.88, 0.86272, 21, -16.67, 10.42, 0.13728, 2, 20, 33.05, 15.95, 0.48822, 21, -6.89, 14.28, 0.51178, 1, 21, 29.27, 14.01, 1, 1, 21, 70.4, 27.72, 1, 1, 21, 71.76, 28.48, 1, 1, 21, 74.29, 25.84, 1, 1, 21, 69.82, 27.48, 1, 1, 21, 21.09, 11.15, 1, 1, 21, 29.42, -6.28, 1, 1, 21, 22.21, -10.41, 1, 1, 21, 12.79, -7.86, 1, 2, 20, 52.86, -6.78, 0.06108, 21, 9.85, -10.8, 0.93892, 2, 20, 34.48, -18.03, 0.89537, 21, -9.81, -19.61, 0.10463, 2, 19, 60.17, -7.55, 0.06874, 20, 16.47, -12.34, 0.93126, 2, 19, 55.62, -12.23, 0.22544, 20, 11.37, -16.41, 0.77456, 2, 19, 42.88, -21.77, 0.72307, 20, -2.46, -24.28, 0.27693, 3, 18, 55.51, -14.25, 0.17063, 19, 11.31, -18.09, 0.82925, 23, 65.27, 38.53, 0.00013, 3, 18, 33.77, -22.52, 0.87528, 19, -11.25, -23.79, 0.07779, 23, 53.07, 18.72, 0.04693, 3, 18, 12.32, -18.38, 0.91103, 19, -32.07, -17.2, 0.08097, 23, 33.55, 8.93, 0.008, 1, 23, 31.95, 8.13, 1, 2, 18, 23.68, -28.3, 0.0064, 23, 48.59, 7.99, 0.9936, 2, 23, 64.76, 10.57, 0.936, 25, 16.83, 10.57, 0.064, 2, 23, 85.57, 13.9, 0.912, 25, 37.63, 13.9, 0.088, 2, 23, 97.21, 14.09, 0.896, 25, 49.28, 14.09, 0.104, 2, 23, 102.21, 1.53, 0.704, 25, 54.28, 1.53, 0.296, 2, 23, 99.73, -6.73, 0.72, 25, 51.8, -6.73, 0.28, 2, 23, 93.1, -13.46, 0.824, 25, 45.17, -13.46, 0.176, 2, 23, 85.89, -15.09, 0.752, 25, 37.96, -15.09, 0.248, 2, 23, 70.05, -15.22, 0.784, 25, 22.11, -15.22, 0.216, 2, 23, 53.4, -17.25, 0.968, 25, 5.46, -17.25, 0.032, 1, 23, 36.83, -19.28, 1, 1, 23, 23.91, -12.57, 1, 1, 23, 20.03, -13.63, 1, 1, 23, 6.06, -13.77, 1, 1, 23, 1.07, -13.81, 1, 2, 16, 53.39, -13.29, 0.19966, 17, 9.73, -16.17, 0.80034, 3, 16, 43.06, -12.69, 0.47474, 17, -0.5, -14.62, 0.35395, 23, -14.91, -20.58, 0.17131, 3, 16, 29.42, -11.89, 0.96189, 17, -14.01, -12.57, 0.02039, 23, -27.48, -25.9, 0.01772, 2, 15, 48.02, -8.34, 0.03924, 16, 4.72, -10.46, 0.96076, 1, 15, 30.25, -10.05, 1, 2, 14, 49.4, -10.42, 0.09135, 15, 6.17, -12.38, 0.90865, 2, 22, 47.85, 19.08, 0.00337, 14, 27.45, -11.53, 0.99663, 3, 13, 47.72, -11.06, 0.0766, 22, 27.31, 9.3, 0.016, 14, 4.73, -12.68, 0.9074, 1, 22, 26.44, 8.89, 1, 3, 22, 41.44, 9.08, 0.76564, 14, 17.69, -18.31, 0.01036, 24, -0.33, 9.08, 0.224, 3, 13, 79.4, -21.93, 0, 22, 60.81, 9.33, 0.808, 24, 19.03, 9.33, 0.192, 1, 22, 87.49, 9.68, 1, 2, 22, 88.46, -3.95, 0.64, 24, 46.68, -3.95, 0.36, 2, 22, 77.54, -9.45, 0.704, 24, 35.76, -9.45, 0.296, 2, 22, 59.57, -12.45, 0.688, 24, 17.79, -12.45, 0.312, 2, 22, 40.79, -15.59, 0.896, 24, -0.99, -15.59, 0.104, 1, 22, 22.86, -15.83, 1, 1, 22, 7.23, -16.05, 1, 2, 13, 13.82, -19.52, 0.01055, 22, -1.99, -9.72, 0.98945, 2, 12, 52.74, -12.34, 0.10439, 13, 9.73, -14.02, 0.89561, 2, 12, 44.68, -10.71, 0.32011, 13, 1.77, -11.94, 0.67989, 2, 12, 21.51, -17.61, 0.99843, 22, -36.25, -19.44, 0.00157, 2, 11, 48.55, -13.84, 0.12205, 12, 5, -14.83, 0.87795, 2, 11, 30.75, -16.51, 0.87304, 12, -12.89, -16.78, 0.12696, 1, 11, 16.2, -15.7, 1, 2, 10, -12.63, -27.29, 0.12527, 11, -4.02, -14.56, 0.87473, 2, 10, -5.39, -11.55, 0.75459, 11, -21.17, -12.12, 0.24541, 2, 10, 3.64, 8.07, 0.99996, 11, -42.56, -9.08, 4e-05, 3, 17, 40.66, -6.79, 0.42443, 18, -2.32, -8.75, 0.50357, 23, 16.06, 7.64, 0.072, 1, 22, -1.32, 2.06, 1, 1, 22, 13.25, 10.62, 1, 1, 22, -3.99, -1.82, 1, 2, 12, 56.52, -7.99, 0.02762, 13, 13.74, -9.88, 0.97238, 1, 13, 18.23, -6.58, 1, 2, 13, 34.63, -3.92, 0.97923, 14, -7.88, -4.73, 0.02077, 2, 13, 45.99, -9.59, 0.12817, 14, 3.09, -11.1, 0.87183, 1, 23, 0.96, -9.16, 1, 1, 23, -6.47, -7.35, 1, 2, 16, 60.72, -0.8, 0.00932, 17, 18.18, -4.42, 0.99068, 1, 23, 1.94, 4.34, 1, 1, 23, 7.14, 7.9, 1, 1, 23, 12.93, 5.84, 1, 1, 23, 22.44, 5.6, 1, 1, 17, 33.96, -0.32, 1, 2, 17, 26.52, -0.57, 0.99877, 18, -15.73, -1.09, 0.00123, 2, 16, 61.2, 1.09, 0.00244, 17, 18.84, -2.58, 0.99756, 2, 16, 55.09, -3.84, 0.06049, 17, 12.3, -6.92, 0.93951, 2, 17, 50.02, -12.16, 0.45736, 18, 6.43, -15.06, 0.54264], "hull": 93, "edges": [0, 184, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 120, 122, 122, 124, 152, 154, 154, 156, 168, 170, 170, 172, 172, 174, 174, 176, 150, 152, 134, 136, 98, 100, 92, 94, 146, 148, 148, 150, 140, 142, 136, 138, 138, 140, 100, 102, 102, 104, 116, 118, 118, 120, 180, 182, 182, 184, 176, 178, 178, 180, 160, 162, 162, 164, 156, 158, 158, 160, 22, 24, 24, 26, 38, 40, 40, 42, 42, 44, 132, 134, 188, 190, 190, 146, 164, 166, 166, 168, 166, 192, 192, 188, 168, 194, 194, 196, 196, 198, 198, 200, 142, 144, 144, 146, 200, 144, 124, 126, 126, 128, 126, 202, 202, 204, 204, 206, 206, 208, 208, 210, 210, 212, 212, 214, 214, 98, 186, 216, 216, 218, 218, 220, 220, 222, 128, 130, 130, 132, 222, 130, 224, 186, 94, 96, 96, 98, 224, 96], "width": 291, "height": 447}}, "yingzi": {"yingzi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [111.96, -172.96, -90.04, -172.96, -90.04, 261.04, 111.96, 261.04], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 60, "height": 130}}}}], "animations": {"te_lian": {"slots": {"tl": {"color": [{"color": "ffffff00"}]}}, "bones": {"3": {"scale": [{"y": 0}]}, "4": {"scale": [{"y": 0}]}, "bone2": {"rotate": [{"angle": -64.71}]}, "bone3": {"rotate": [{"angle": -32.4}], "translate": [{"x": 4.56, "y": -13.54}]}, "bone4": {"rotate": [{"angle": -133.39}], "translate": [{"x": -17.01, "y": -22.57}]}, "bone5": {"rotate": [{"angle": -54.77}], "translate": [{"x": 1.97, "y": -20.3}]}, "bone6": {"rotate": [{"angle": -92.34}], "translate": [{"x": -12.69, "y": -29.88}]}, "bone7": {"rotate": [{"angle": -24.28}], "translate": [{"x": 6.47, "y": -10.12}]}, "bone8": {"rotate": [{"angle": -17.6}], "translate": [{"x": 7.44, "y": -6.47}]}, "bone9": {"rotate": [{"angle": -126.07}], "translate": [{"x": -0.01, "y": -14.1}]}, "bone10": {"rotate": [{"angle": -60.81}], "translate": [{"x": 1.77, "y": -24.07}]}, "bone11": {"rotate": [{"angle": -106.61}], "translate": [{"x": -34.68, "y": -35.31}]}, "bone12": {"rotate": [{"angle": 2.33}], "translate": [{"x": 8.1, "y": 0.78}]}, "bone13": {"rotate": [{"angle": 3.13}], "translate": [{"x": 7.41, "y": 1.12}]}, "bone14": {"rotate": [{"angle": 3.59}], "translate": [{"x": 7.35, "y": 1.3}]}, "bone15": {"rotate": [{"angle": 4.11}], "translate": [{"x": 7.51, "y": 1.49}]}, "bone16": {"rotate": [{"angle": 4.69}], "translate": [{"x": 7.61, "y": 1.7}]}, "bone17": {"rotate": [{"angle": 5.31}], "translate": [{"x": 7.36, "y": 1.91}]}, "bone18": {"rotate": [{"angle": 5.97}], "translate": [{"x": 7.21, "y": 2.15}]}, "bone19": {"rotate": [{"angle": 6.64}], "translate": [{"x": 7.34, "y": 2.41}]}, "bone20": {"rotate": [{"angle": 7.18}], "translate": [{"x": 7.44, "y": 2.63}]}, "bone21": {"rotate": [{"angle": 7.35}], "translate": [{"x": 6.86, "y": 2.67}]}, "bone22": {"scale": [{"x": 1.076, "y": 1.076}]}}}, "te_lian_jiesuo": {"slots": {"1": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffffff"}]}, "2": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffffff"}]}, "tl": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.3333, "color": "ffffff00"}, {"time": 1.4667, "color": "ffffffff"}, {"time": 1.5667, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 1.3333, "name": "tl"}]}, "3": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffffff"}]}, "yingzi": {"attachment": [{"name": null}, {"time": 0.6333, "name": "<PERSON><PERSON><PERSON>"}]}}, "bones": {"1": {"rotate": [{"time": 0.6333, "angle": -7.04, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 12.52, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -8.69, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 4.44, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -2.39, "curve": 0.25, "c3": 0.75}, {"time": 1.2333}], "scale": [{"x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "3": {"rotate": [{"time": 0.6333, "angle": -7.04, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 12.52, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -8.69, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 4.44, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -2.39, "curve": 0.25, "c3": 0.75}, {"time": 1.2333}], "scale": [{"time": 0.5667, "y": 0}]}, "2": {"rotate": [{"time": 0.7333, "angle": -7.04, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 12.52, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -8.69, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 4.44, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": -2.39, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7333, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.8667}]}, "4": {"rotate": [{"time": 0.7333, "angle": -7.04, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 12.52, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -8.69, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 4.44, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": -2.39, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}], "scale": [{"time": 0.6667, "y": 0}]}, "bone22": {"scale": [{"time": 1.3333}, {"time": 1.5, "x": 1.076, "y": 1.076}]}, "bone2": {"rotate": [{"time": 1.5667, "angle": -64.71}]}, "bone3": {"rotate": [{"time": 1.5667, "angle": -32.4}], "translate": [{"time": 1.5667, "x": 4.56, "y": -13.54}]}, "bone4": {"rotate": [{"time": 1.5667, "angle": -133.39}], "translate": [{"time": 1.5667, "x": -17.01, "y": -22.57}]}, "bone5": {"rotate": [{"time": 1.5667, "angle": -54.77}], "translate": [{"time": 1.5667, "x": 1.97, "y": -20.3}]}, "bone6": {"rotate": [{"time": 1.5667, "angle": -92.34}], "translate": [{"time": 1.5667, "x": -12.69, "y": -29.88}]}, "bone7": {"rotate": [{"time": 1.5667, "angle": -24.28}], "translate": [{"time": 1.5667, "x": 6.47, "y": -10.12}]}, "bone8": {"rotate": [{"time": 1.5667, "angle": -17.6}], "translate": [{"time": 1.5667, "x": 7.44, "y": -6.47}]}, "bone9": {"rotate": [{"time": 1.5667, "angle": -126.07}], "translate": [{"time": 1.5667, "x": -0.01, "y": -14.1}]}, "bone10": {"rotate": [{"time": 1.5667, "angle": -60.81}], "translate": [{"time": 1.5667, "x": 1.77, "y": -24.07}]}, "bone11": {"rotate": [{"time": 1.5667, "angle": -106.61}], "translate": [{"time": 1.5667, "x": -34.68, "y": -35.31}]}, "bone12": {"rotate": [{"time": 1.5667, "angle": 2.33}], "translate": [{"time": 1.5667, "x": 8.1, "y": 0.78}]}, "bone13": {"rotate": [{"time": 1.5667, "angle": 3.13}], "translate": [{"time": 1.5667, "x": 7.41, "y": 1.12}]}, "bone14": {"rotate": [{"time": 1.5667, "angle": 3.59}], "translate": [{"time": 1.5667, "x": 7.35, "y": 1.3}]}, "bone15": {"rotate": [{"time": 1.5667, "angle": 4.11}], "translate": [{"time": 1.5667, "x": 7.51, "y": 1.49}]}, "bone16": {"rotate": [{"time": 1.5667, "angle": 4.69}], "translate": [{"time": 1.5667, "x": 7.61, "y": 1.7}]}, "bone17": {"rotate": [{"time": 1.5667, "angle": 5.31}], "translate": [{"time": 1.5667, "x": 7.36, "y": 1.91}]}, "bone18": {"rotate": [{"time": 1.5667, "angle": 5.97}], "translate": [{"time": 1.5667, "x": 7.21, "y": 2.15}]}, "bone19": {"rotate": [{"time": 1.5667, "angle": 6.64}], "translate": [{"time": 1.5667, "x": 7.34, "y": 2.41}]}, "bone20": {"rotate": [{"time": 1.5667, "angle": 7.18}], "translate": [{"time": 1.5667, "x": 7.44, "y": 2.63}]}, "bone21": {"rotate": [{"time": 1.5667, "angle": 7.35}], "translate": [{"time": 1.5667, "x": 6.86, "y": 2.67}]}}, "path": {"lujing": {"spacing": [{"spacing": -35, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "spacing": 7.2}]}}}}}