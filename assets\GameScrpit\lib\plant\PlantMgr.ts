import { _decorator, Component, director, instantiate, is<PERSON><PERSON><PERSON>, Layers, Node, Prefab, UIOpacity, v3 } from "cc";
import ResMgr from "../common/ResMgr";
import { Sleep } from "../../game/GameDefine";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export default class PlantMgr extends Component {
  protected static _instance: PlantMgr = null;
  public static get instance(): PlantMgr {
    if (PlantMgr._instance == null) {
      let node = new Node();
      node.name = "PlantMgr";
      node.setPosition(v3(99999, 99999, 99999));
      node.addComponent(UIOpacity).opacity = 0;
      director.getScene().addChild(node);
      director.addPersistRootNode(node);
      node.active = true;
      PlantMgr._instance = node.addComponent(PlantMgr);
    }
    return PlantMgr._instance;
  }

  public dict: Map<string, Node[]> = new Map<string, Node[]>();
  public dickNum: Map<string, number> = new Map<string, number>();
  public dickPrefab: Map<string, Prefab> = new Map<string, Prefab>();

  onLoad() {}

  public async get(path: string, layer: number = 8): Promise<Node> {
    let nodeList = this.dict.get(path);
    let isEnough = nodeList != null && nodeList.length > 0;
    let node = null;
    if (isEnough) {
      node = nodeList[0];
      nodeList.splice(0, 1);
    } else {
      let prefab = null;
      if (this.dickPrefab.has(path)) {
        prefab = this.dickPrefab.get(path);
      } else {
        prefab = await ResMgr.loadPrefabSync(path);
        prefab.addRef();
      }
      node = instantiate(prefab);
    }

    node.walk((child) => (child.layer = layer));
    return node;
  }

  public ret(path: string, node: Node) {
    if (isValid(node) == false) {
      log.log(node);
    }
    node.removeFromParent();
    node.setPosition(v3(0, 0, 0));
    let nodeList = this.dict.get(path);
    if (nodeList == null) {
      nodeList = [];
      this.dict.set(path, nodeList);
    }
    this.node.addChild(node);
    nodeList.push(node);
  }

  public async size(path: string, num: number) {
    if (!this.dict.get(path)) {
      let nodeList = [];
      this.dict.set(path, nodeList);
      this.dickNum.set(path, num);
    } else {
      if (this.dickNum.has(path)) {
        this.dickNum.set(path, num);
      }
    }

    if (this.dict.get(path).length < num) {
      ResMgr.loadPrefab(path, async (prefab) => {
        this.dickPrefab.set(path, prefab);
        for (let i = 0; i < num; i++) {
          await Sleep(0.2);
          let ins = instantiate(prefab);
          prefab.addRef();
          this.ret(path, ins);
        }
      });
    }
  }
}
