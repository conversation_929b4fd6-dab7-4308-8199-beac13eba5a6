import {
  _decorator,
  assetManager,
  BlockInputEvents,
  Button,
  find,
  instantiate,
  isValid,
  Node,
  tween,
  UIOpacity,
  v3,
  Widget,
} from "cc";
import LNode from "../node/LNode";
import MsgMgr from "../event/MsgMgr";
import { BundleEnum } from "../../game/bundleEnum/BundleEnum";
import MsgEnum from "../../game/event/MsgEnum";
import { AssetMgr } from "db://assets/platform/src/ResHelper";
import ResMgr from "../common/ResMgr";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UINode")
export abstract class UINode extends LNode {
  protected _autoAdaptation: boolean = false; //防止ui位置显示问题
  protected _openAct: boolean = false; //打开动作
  protected _autoBind: boolean = true; //防止关注的结点过多。配合protected bindNodePath使用
  protected _isSwallowTouch: boolean = true; //是否可点击穿透，false为可以
  protected _isClearCache: boolean = true;
  protected _isGameCamera: boolean = false; //是否是游戏相机，true为是
  protected _resetLayer: boolean = true; // 重置层级
  protected _plantMgr: Map<string, Node[]> = new Map<string, Node[]>();

  private _assetMgr: AssetMgr;
  public get assetMgr(): AssetMgr {
    if (!this._assetMgr) {
      this._assetMgr = new AssetMgr();
    }
    return this._assetMgr;
  }

  public customParentNode: Node = null;

  //是否准备好了
  public get ready(): boolean {
    return this.node != null;
  }

  //是否显示
  private _isShow: boolean = false;
  public get isShow(): boolean {
    return this._isShow;
  }

  //预制体节点
  private _node: Node = null;
  public get node(): Node {
    return this._node;
  }
  //名字
  public get name(): string {
    let prefabPath = this.prefab();
    let array = prefabPath.split("/");
    let nameWithExt = array[array.length - 1];
    let name = nameWithExt.split(".")[0];
    return name;
  }
  //是否被删除
  private _isRemove: boolean = false;
  public get isRemove(): boolean {
    return this._isRemove;
  }
  ////////////////////////////////////////////////////////////////////////////////////////////////初始化

  /**子界面层 要带parent字段设置根节点 */
  public init(args: any) {
    if (!this.customParentNode) {
      this.customParentNode = args.parent;
    }

    if (!this.customParentNode) {
      log.error("父節點未设置");
    }

    this.loadPrefab();
  }
  private _deps = [];
  public addDeps(uuid: string) {
    this._deps.push(uuid);
  }

  //加载预制体
  private async loadPrefab() {
    let dependOnList = this.dependOn();
    for (let i = 0; i < dependOnList.length; i++) {
      let bundleName = dependOnList[i];
      await ResMgr.getBundleSync(bundleName);
    }

    const strList = this.prefab().split("?");
    const bundleName = strList[0];
    const prefabName = strList[1];

    const pb = await this.assetMgr.loadPrefabSync(bundleName, prefabName);
    if (this.isRemove) {
      log.warn("UI已经被删除");
      return;
    }

    this._node = instantiate(pb);
    if (this._openAct == true) {
      this.node.scale = v3(0.5, 0.5, 1);
    }

    // 重置层级
    if (this._resetLayer) {
      this._node.walk((child) => (child.layer = this.customParentNode.layer));
    }

    this.customParentNode.addChild(this.node);
    this.initProcess();
    MsgMgr.emit(MsgEnum.UIReady, this.name);
  }

  //初始化流程
  private initProcess() {
    if (this._autoBind) {
      this.autoBind();
    } else {
      this.manualBind();
    }

    if (this._isSwallowTouch) {
      this.swallowTouches();
    }

    this.registerEvent();
    this.onShowAct();
    this.onShow();
  }

  private swallowTouches() {
    this.node.addComponent(BlockInputEvents);
  }

  //便捷自动绑定使用节点
  protected autoBind() {
    this.loopChild(this.node);
  }
  /**
   * 手动绑定节点需要重写此方法
   */
  protected bindNodePath() {
    return {};
  }
  //便捷手动绑定使用节点
  protected manualBind() {
    let pathDict = this.bindNodePath();
    let keyList = Object.keys(pathDict);
    for (let i = 0; i < keyList.length; i++) {
      let name = keyList[i];
      let path = pathDict[name];
      if (name == "experienceNum") {
        log.error("错误");
      }
      let child = find(path, this._node);
      this[name] = child;
      log.log(child);
      this.onDefaultDealWith(child);
    }
  }
  //遍历寻找子节点
  private loopChild(parent: Node) {
    if (parent == null || parent.children == null || parent.children.length == 0) {
      return;
    }

    parent.walk((val) => {
      let name = val.name;
      if (name == "name" || name == "view" || name == "node") {
        name = "default_" + name;
      }
      this[name] = val;
      this.onDefaultDealWith(val);
    });
    // for (let i = 0; i < parent.children.length; i++) {
    //   let child = parent.children[i];
    //   this.loopChild(child);
    // }
  }
  //默认的节点处理
  private onDefaultDealWith(child: Node) {
    let button = child.getComponent(Button);
    if (button != null) {
      //button.zoomScale = 1.2;
      this.onRegisterClick(button);
    }
  }

  //注册按钮监听
  private onRegisterClick(button: Button) {
    if (this["on_click_" + button.node.name] == null) {
      return;
    }
    button.node.on(
      "click",
      function (evt) {
        try {
          this["on_click_" + button.node.name] && this["on_click_" + button.node.name](evt);
          //this.on_click_common();
        } catch (error) {
          log.error(error);
        }
      },
      this
    );

    button.node.on(
      Node.EventType.TOUCH_START,
      function (evt) {
        try {
          this["on_click_" + button.node.name + "_start"] && this["on_click_" + button.node.name + "_start"](evt);
        } catch (error) {
          log.error(error);
        }
      },
      this
    );
    button.node.on(
      Node.EventType.TOUCH_CANCEL,
      function (evt) {
        try {
          this["on_click_" + button.node.name + "_cancel"] && this["on_click_" + button.node.name + "_cancel"](evt);
        } catch (error) {
          log.error(error);
        }
      },
      this
    );
    button.node.on(
      Node.EventType.TOUCH_MOVE,
      function (evt) {
        try {
          this["on_click_" + button.node.name + "_move"] && this["on_click_" + button.node.name + "_move"](evt);
        } catch (error) {
          log.error(error);
        }
      },
      this
    );
  }

  private onEvtHideTry() {
    try {
      this.onEvtHide();
    } catch (error) {
      log.error(error);
    }
  }

  private registerEvent() {
    MsgMgr.on("ON_" + this.name + "_REFRESH", this.onRefresh, this);
    MsgMgr.on("ON_" + this.name + "_SHOW", this.onEvtShow, this);
    MsgMgr.on("ON_" + this.name + "_RESHOW", this.onEvtReshow, this);
    MsgMgr.on("ON_" + this.name + "_HIDE", this.onEvtHideTry, this);
    MsgMgr.on("ON_" + this.name + "_CLOSE", this.onEvtClose, this);
    this.onRegEvent();
  }

  private removeEvent() {
    MsgMgr.off("ON_" + this.name + "_REFRESH", this.onRefresh, this);
    MsgMgr.off("ON_" + this.name + "_SHOW", this.onEvtShow, this);
    MsgMgr.off("ON_" + this.name + "_RESHOW", this.onEvtReshow, this);
    MsgMgr.off("ON_" + this.name + "_HIDE", this.onEvtHideTry, this);
    MsgMgr.off("ON_" + this.name + "_CLOSE", this.onEvtClose, this);
    this.onDelEvent();
  }

  ////////////////////////////////////////////////////////////////////////////////////////////////
  //按钮点击通用
  protected on_click_common(button) {
    //AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
  }

  //按钮touchstart
  protected on_click_common_start(button) {}

  //按钮touchcancel
  protected on_click_common_cancel(button) {}

  protected on_click_common_move(button) {}

  //打开界面的动作
  protected onShowAct() {
    if (this._autoAdaptation || this._openAct == false) {
      return;
    }

    let uiOpaticy = this.node.getComponent(UIOpacity);
    if (uiOpaticy == null) {
      uiOpaticy = this.node.addComponent(UIOpacity);
    }

    uiOpaticy.opacity = 0;
    const t1 = tween(uiOpaticy).to(0.25, { opacity: 255 });

    const t2 = tween(this.node)
      .to(0.3, { scale: v3(1, 1, 1) }, { easing: "backOut" })
      .call(() => {
        this.node.getComponent(Widget)?.updateAlignment();
      });

    tween(this.node).parallel(t1, t2).start();
  }

  protected onCloseAct(actEndCall) {
    actEndCall && actEndCall();
    // if (this._autoAdaptation || this._openAct == false) {
    //   return;
    // }
    // let uiOpaticy = this.node.getComponent(UIOpacity);
    // if (uiOpaticy == null) {
    //   uiOpaticy = this.node.addComponent(UIOpacity);
    // }

    // const t1 = tween(uiOpaticy).to(0.15, { opacity: 0 });

    // const t2 = tween(this.node)
    //   .to(0.15, { scale: v3(0.5, 0.5, 1) })
    //   .call(() => {
    //     setTimeout(() => {
    //       actEndCall && actEndCall();
    //     }, 1);
    //   });

    // tween(this.node).parallel(t1, t2).start();
  }

  protected onShow() {
    //显示回调 防止别人重写时忘记调用 把他设为私有
    this._isShow = true;
    if (this.node) {
      this.node.active = true;
    }

    MsgMgr.emit("ON_" + this.name + "_SHOW", this);
  }

  public onReshow(args) {
    //隐藏后重新显示
    this.onShow();
    this.onShowAct();
    MsgMgr.emit("ON_" + this.name + "_REFRESH");
    MsgMgr.emit(MsgEnum.UIReady, this.name);
  }

  public onHide() {
    if (this._isShow == false) {
      return;
    }
    //隐藏
    MsgMgr.emit("ON_" + this.name + "_HIDE");

    this.onCloseAct(() => {
      this._isShow = false;
      if (this.node) {
        this.node.active = false;
      }
    });
  }

  public onClose() {
    // 添加防止重复调用保护
    if (this._isRemove) return;

    this._isRemove = true;
    //销毁

    MsgMgr.emit("ON_" + this.name + "_HIDE");

    this.onCloseAct(() => {
      MsgMgr.emit("ON_" + this.name + "_CLOSE");
      this.removeEvent();

      if (this._isClearCache) {
        this.clearCache();
      }

      // 资产管理器释放后置空
      if (this._assetMgr) {
        this._assetMgr.release();
        this._assetMgr = null;
      }

      if (isValid(this.node)) {
        this.node.destroy();
        this._node = null; // 清除节点引用
      }
    });
  }

  protected clearCache() {
    //依赖资源
    for (let i = 0; i < this._deps.length; i++) {
      let uuid = this._deps[i];
      let res = assetManager.assets.get(uuid);
      res && res.decRef();
      res = null;
    }
  }

  public sleepTick(dt) {
    //隐藏的时候调用
  }

  protected onRefresh() {
    //事件触发刷新界面
  }

  public getNode(name: string): Node {
    return this[name];
  }

  //============================================做界面需要重写的方法==========================================
  protected abstract prefab(): string;

  /**使用的公共的分包资源 */
  protected dependOn(): Array<BundleEnum> {
    return [BundleEnum.BUNDLE_COMMON_UI, BundleEnum.BUNDLE_COMMON_ITEM];
  }
  public zOrder(): number {
    return 0;
  }
  protected onRegEvent() {} //自己添加监听事件
  protected onDelEvent() {} //删除添加监听事件 有添加必须有删除
  protected onEvtShow() {} //初始化显示事件
  protected onEvtReshow() {} //重新显示界面事件 --- 不使用
  protected onEvtHide() {} //隐藏界面事件
  protected onEvtClose() {} //销毁关闭界面事件
  // public onUnfocus() {} //取消关注
  // public onFocus() {} //关注
}
