import { _decorator } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { Label } from "cc";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { GHttp } from "../../../lib/http/GHttp";
import { TipsMgr } from "../../../../platform/src/TipsHelper";
const { ccclass, property } = _decorator;

/**
 *
 * hopewsw
 * Mon Oct 28 2024 20:24:45 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/ui_shop/UIPay.ts
 *
 */
export interface IPayInfo {
  amount: number;
  itemNum: number;
  goodsName: string;
  orderNo: string;
  backUrl: string;
}

@ccclass("UIPay")
export class UIPay extends UINode {
  payInfo: IPayInfo = null;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_SHOP}?prefab/ui/UIPay`;
  }

  //=================================================
  public init(args: any): void {
    super.init(args);

    let url = args.url.split("?")[1];
    let params = url.split("&").reduce((acc, cur) => {
      const [key, value] = cur.split("=");
      acc[key] = value;
      return acc;
    }, {});

    this.payInfo = {
      amount: parseInt(params["orderAmount"]),
      itemNum: parseInt(params["itemNum"]),
      goodsName: params["goodsName"],
      orderNo: params["orderNo"],
      backUrl: params["backUrl"],
    };
  }
  protected onEvtShow(): void {
    super.onEvtShow();

    this.getNode("lbl_goods_name").getComponent(Label).string = this.payInfo.goodsName;
    this.getNode("lbl_amount").getComponent(Label).string = this.payInfo.amount.toString();

    TipsMgr.setEnableTouch(true);
  }

  private on_click_btn_pay() {
    GHttp.GET({
      url: `${this.payInfo.backUrl}?orderNo=${this.payInfo.orderNo}&result=success&amount=${this.payInfo.amount}`,
    }).then((resp: string) => {
      UIMgr.instance.back({ ok: true, resp: resp });
    });
  }

  private on_click_btn_cancel() {
    UIMgr.instance.back();
  }
}
