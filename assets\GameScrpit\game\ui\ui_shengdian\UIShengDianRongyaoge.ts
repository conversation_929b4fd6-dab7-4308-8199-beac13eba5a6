import { _decorator, Component, EventTouch, instantiate, Label, Node } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { ShengDianModule } from "../../../module/sheng_dian/ShengDianModule";
import { ItemCtrl } from "../../common/ItemCtrl";
import { ItemCost } from "../../common/ItemCost";
import { GoodsModule } from "../../../module/goods/GoodsModule";
import { ItemEnum } from "../../../lib/common/ItemEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import ToolExt from "../../common/ToolExt";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { JsonMgr } from "../../mgr/JsonMgr";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
const { ccclass, property } = _decorator;

/**
 * ivan_huang
 * Thu Jan 16 2025 16:12:49 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/ui_shengdian/UIShengDianRongyaoge.ts
 * https://docs.cocos.com/creator/3.8/manual/zh/
 *
 */

@ccclass("UIShengDianRongyaoge")
export class UIShengDianRongyaoge extends UINode {
  private _selectPosition: number = 0;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_SHENGDIAN}?prefab/ui/UIShengDianRongyaoge`;
  }

  //=================================================
  public init(args: any): void {
    super.init(args);
  }
  protected onEvtShow(): void {
    // do something

    this.refreshSkin();

    this.getNode("node_all_coin").getComponent(ItemCost).setItemId(ItemEnum.圣殿币_20005, 0);

    // this.getNode("lbl_skin_xiaoguo");
    GoodsModule.api.buyInfo((data) => {
      this.refreshSkin();
    });
  }

  private refreshIndicator() {
    let shopList = ShengDianModule.config.getShengDianShopList();
    for (let i = 0; i < shopList.length; i++) {
      if (!this.getNode("node_indicator").children[i]) {
        this.getNode("node_indicator").addChild(instantiate(this.getNode("node_indicator").children[0]));
      }
      if (i == this._selectPosition) {
        this.getNode("node_indicator").children[i].getChildByName("unselect").active = false;
        this.getNode("node_indicator").children[i].getChildByName("select").active = true;
      } else {
        this.getNode("node_indicator").children[i].getChildByName("unselect").active = true;
        this.getNode("node_indicator").children[i].getChildByName("select").active = false;
      }
    }
  }

  protected onEvtHide(): void {
    // do something
  }

  private refreshSkin() {
    let shopList = ShengDianModule.config.getShengDianShopList();

    // this.refreshItem();
    this.getNode("item_cost")
      .getComponent(ItemCost)
      .setItemId(shopList[this._selectPosition].cointype, shopList[this._selectPosition].coinPrice);

    if (GoodsModule.data.getGoodsRedeemMsgById(shopList[this._selectPosition].id) > 0) {
      this.getNode("bg_yigoumai").active = true;
      this.getNode("item_cost").active = false;
      this.getNode("btn_buy").active = false;
    } else {
      this.getNode("bg_yigoumai").active = false;
      this.getNode("item_cost").active = true;
      this.getNode("btn_buy").active = true;
    }

    this.refreshIndicator();
    //
    let skinId = shopList[this._selectPosition].itemsList[0][0];
    let skin = JsonMgr.instance.jsonList.c_leaderSkin[skinId];
    this.getNode("lbl_skin_name").getComponent(Label).string = skin.name;
    let descript = `解锁效果：${JsonMgr.instance.jsonList.c_attribute[skin.attrAdd[0][0]].name} +${skin.attrAdd[0][1]}`;
    this.getNode("lbl_skin_xiaoguo").getComponent(Label).string = descript;
    ToolExt.loadUIRole(this.getNode("node_skin"), skinId, 0, "renderScale18", this);
  }

  private onSelectSkin(e: EventTouch) {
    //
    let target: Node = e.target;
    if (target.getSiblingIndex() == this._selectPosition) {
      return;
    }
    this._selectPosition = target.getSiblingIndex();
    this.refreshSkin();
  }
  private on_click_btn_left() {
    let shopList = ShengDianModule.config.getShengDianShopList();
    this._selectPosition--;
    if (this._selectPosition < 0) {
      this._selectPosition = shopList.length - 1;
    }
    this.refreshSkin();
  }
  private on_click_btn_right() {
    let shopList = ShengDianModule.config.getShengDianShopList();
    this._selectPosition++;
    if (this._selectPosition >= shopList.length) {
      this._selectPosition = 0;
    }
    this.refreshSkin();
  }
  private on_click_btn_buy() {
    let shopList = ShengDianModule.config.getShengDianShopList();
    let shop = shopList[this._selectPosition];
    if (shop) {
      let itemNum = PlayerModule.data.getItemNum(shop.cointype);
      if (itemNum < shop.coinPrice) {
        UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, { itemId: shop.cointype });
      } else {
        GoodsModule.api.redeemGoods(shop.id, 1, (rewardList: number[]) => {
          MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: rewardList });
          this.refreshSkin();
        });
      }
    }
  }

  private on_click_btn_back() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  private on_click_btn_zhandouyulan() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    //
    let shopList = ShengDianModule.config.getShengDianShopList();
    //
    let skinId = shopList[this._selectPosition].itemsList[0][0];
    UIMgr.instance.showDialog(PlayerRouteName.UIWatchSkill, { roleId: skinId });
  }
}
