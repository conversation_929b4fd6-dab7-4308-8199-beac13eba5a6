import { GameData } from "../../game/GameData";
import data from "../../lib/data/data";
import { FriendApi } from "./FriendApi";
import { FriendConfig } from "./FriendConfig";
import { FriendData } from "./FriendData";
import { FriendRoute } from "./FriendRoute";
import { FriendService } from "./FriendService";
import { FriendSubscriber } from "./FriendSubscriber";
import { FriendViewModel } from "./FriendViewModel";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class FriendModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): FriendModule {
    if (!GameData.instance.FriendModule) {
      GameData.instance.FriendModule = new FriendModule();
    }
    return GameData.instance.FriendModule;
  }
  private _data = new FriendData();
  private _api = new FriendApi();
  private _config = new FriendConfig();
  private _service = new FriendService();
  private _viewModel = new FriendViewModel();
  private _route = new FriendRoute();
  private _subscriber = new FriendSubscriber();

  public static get data() {
    return this.instance._data;
  }

  public static get api() {
    return this.instance._api;
  }

  public static get config() {
    return this.instance._config;
  }
  public static get service() {
    return this.instance._service;
  }

  public static get viewModel() {
    return this.instance._viewModel;
  }

  public init(data?: any, completedCallback?: Function) {
    if (this._subscriber) {
      this._subscriber.unRegister();
    }
    this._data = new FriendData();
    this._api = new FriendApi();
    this._config = new FriendConfig();
    this._service = new FriendService();
    this._viewModel = new FriendViewModel();
    this._route = new FriendRoute();
    this._subscriber = new FriendSubscriber();

    // 初始化模块
    this._data.init();
    this._route.init();

    FriendModule.api.getAll(() => {
      completedCallback && completedCallback();
      this._service.init();
    });
    FriendModule.api.getPicture((data) => {
      log.log("仙友图鉴", data);
    });
    this._subscriber.register();
  }

  protected saveKey(): string {
    return this.constructor.name;
  }
}
