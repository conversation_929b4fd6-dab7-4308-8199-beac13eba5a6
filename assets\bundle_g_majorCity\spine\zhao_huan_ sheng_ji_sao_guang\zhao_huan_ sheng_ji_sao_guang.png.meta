{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "5806bfa8-5283-40a2-8094-d2863893a743", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "5806bfa8-5283-40a2-8094-d2863893a743@6c48a", "displayName": "<PERSON><PERSON>_huan_ sheng_ji_sao_guang", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "5806bfa8-5283-40a2-8094-d2863893a743", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "5806bfa8-5283-40a2-8094-d2863893a743@f9941", "displayName": "<PERSON><PERSON>_huan_ sheng_ji_sao_guang", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -1, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 342, "height": 39, "rawWidth": 344, "rawHeight": 39, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-171, -19.5, 0, 171, -19.5, 0, -171, 19.5, 0, 171, 19.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 39, 342, 39, 0, 0, 342, 0], "nuv": [0, 0, 0.9941860465116279, 0, 0, 1, 0.9941860465116279, 1], "minPos": [-171, -19.5, 0], "maxPos": [171, 19.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "5806bfa8-5283-40a2-8094-d2863893a743@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "5806bfa8-5283-40a2-8094-d2863893a743@6c48a"}}