{"skeleton": {"hash": "bXxK8VLBZVTjyTXrqbu+p8U6sO0=", "spine": "3.8.75", "x": -379.37, "y": -751.9, "width": 758.16, "height": 1504.74, "images": "./images/", "audio": "D:/spine导出/游戏开始界面"}, "bones": [{"name": "root"}, {"name": "hulu", "parent": "root", "length": 180.41, "rotation": 35.37, "x": -185.55, "y": 378.4}, {"name": "shui2", "parent": "hulu", "x": 179.7, "y": -7.03}, {"name": "shui3", "parent": "shui2", "x": 18, "y": -81.6}, {"name": "shui4", "parent": "shui2", "x": -37.74, "y": -96.61}, {"name": "shui5", "parent": "shui2", "x": 37.09, "y": -150.53}, {"name": "shui6", "parent": "shui2", "x": -9.71, "y": -171.88}, {"name": "shui7", "parent": "shui2", "x": -18.97, "y": -237.29}, {"name": "shui8", "parent": "shui2", "x": 5.89, "y": -304.78}, {"name": "shui9", "parent": "hulu", "x": 179.7, "y": -7.03}, {"name": "shui10", "parent": "shui9", "x": 18, "y": -81.6}, {"name": "shui11", "parent": "shui9", "x": -37.74, "y": -96.61}, {"name": "shui12", "parent": "shui9", "x": 37.09, "y": -150.53}, {"name": "shui13", "parent": "shui9", "x": -9.71, "y": -171.88}, {"name": "shui14", "parent": "shui9", "x": -18.97, "y": -237.29}, {"name": "shui15", "parent": "shui9", "x": 5.89, "y": -304.78}, {"name": "shui16", "parent": "hulu", "x": 179.7, "y": -7.03}, {"name": "shui17", "parent": "shui16", "x": 18, "y": -81.6}, {"name": "shui18", "parent": "shui16", "x": -37.74, "y": -96.61}, {"name": "shui19", "parent": "shui16", "x": 37.09, "y": -150.53}, {"name": "shui20", "parent": "shui16", "x": -9.71, "y": -171.88}, {"name": "shui21", "parent": "shui16", "x": -18.97, "y": -237.29}, {"name": "shui22", "parent": "shui16", "x": 5.89, "y": -304.78}, {"name": "shui23", "parent": "shui9", "x": 18, "y": -81.6}, {"name": "shui24", "parent": "shui9", "x": -37.74, "y": -96.61}, {"name": "shui25", "parent": "shui9", "x": 37.09, "y": -150.53}, {"name": "shui26", "parent": "shui9", "x": -9.71, "y": -171.88}, {"name": "shui27", "parent": "shui9", "x": -18.97, "y": -237.29}, {"name": "shui28", "parent": "shui9", "x": 5.89, "y": -304.78}, {"name": "shui29", "parent": "hulu", "x": 179.7, "y": -7.03}, {"name": "shui30", "parent": "shui29", "x": 18, "y": -81.6}, {"name": "shui31", "parent": "shui29", "x": -37.74, "y": -96.61}, {"name": "shui32", "parent": "shui29", "x": 37.09, "y": -150.53}, {"name": "shui33", "parent": "shui29", "x": -9.71, "y": -171.88}, {"name": "shui34", "parent": "shui29", "x": -18.97, "y": -237.29}, {"name": "shui35", "parent": "shui29", "x": 5.89, "y": -304.78}, {"name": "shui36", "parent": "shui29", "x": 18, "y": -81.6}, {"name": "shui37", "parent": "shui29", "x": -37.74, "y": -96.61}, {"name": "shui38", "parent": "shui29", "x": 37.09, "y": -150.53}, {"name": "shui39", "parent": "shui29", "x": -9.71, "y": -171.88}, {"name": "shui40", "parent": "shui29", "x": -18.97, "y": -237.29}, {"name": "shui41", "parent": "shui29", "x": 5.89, "y": -304.78}, {"name": "shui42", "parent": "hulu", "x": 179.7, "y": -7.03}, {"name": "shui43", "parent": "shui42", "x": 18, "y": -81.6}, {"name": "shui44", "parent": "shui42", "x": -37.74, "y": -96.61}, {"name": "shui45", "parent": "shui42", "x": 37.09, "y": -150.53}, {"name": "shui46", "parent": "shui42", "x": -9.71, "y": -171.88}, {"name": "shui47", "parent": "shui42", "x": -18.97, "y": -237.29}, {"name": "shui48", "parent": "shui42", "x": 5.89, "y": -304.78}, {"name": "fein<PERSON>o", "parent": "root", "length": 542.69, "rotation": -156.3, "x": 225.92, "y": 1055.53}, {"name": "bone", "parent": "root", "length": 181.51, "rotation": -0.34, "x": 261.5, "y": -255.63}, {"name": "huo1", "parent": "bone", "x": -14.4, "y": 119.16}, {"name": "bone2", "parent": "huo1", "length": 30, "rotation": 91.12, "x": -1.36, "y": 2.11}, {"name": "bone3", "parent": "bone2", "length": 30, "x": 30}, {"name": "bone4", "parent": "bone3", "length": 30, "x": 30}, {"name": "bone5", "parent": "bone4", "length": 30, "x": 30}, {"name": "pubu", "parent": "root", "length": 617.76, "x": 540.36, "y": 691.52}, {"name": "pubu1", "parent": "pubu", "x": -537.55, "y": -795.92}, {"name": "ren", "parent": "root", "length": 624.92, "rotation": 0.27, "x": 529.49, "y": 565.92}, {"name": "ren1", "parent": "ren", "x": -526.19, "y": -888.99, "color": "e80d0dff"}, {"name": "ren2", "parent": "ren1", "length": 21.91, "rotation": 95.66, "x": -39.43, "y": 58.42, "color": "e80d0dff"}, {"name": "ren3", "parent": "ren2", "length": 14.44, "rotation": -4.81, "x": 21.91, "color": "e80d0dff"}, {"name": "ren4", "parent": "ren3", "x": 5.6, "y": -17.66, "color": "e80d0dff"}, {"name": "ren5", "parent": "ren3", "x": 4.89, "y": 18.3, "color": "e80d0dff"}, {"name": "ren6", "parent": "ren5", "length": 17.54, "rotation": 168.65, "x": -11.56, "y": 2.49, "color": "e80d0dff"}, {"name": "ren7", "parent": "ren6", "length": 18.29, "rotation": 18.23, "x": 17.54, "color": "e80d0dff"}, {"name": "ren8", "parent": "ren1", "length": 32.02, "rotation": -93.31, "x": -38.33, "y": 50.78, "color": "e80d0dff"}, {"name": "ren9", "parent": "ren1", "length": 29.22, "rotation": 98.64, "x": -40.33, "y": 106.54, "color": "e80d0dff"}, {"name": "ren10", "parent": "ren1", "length": 16.1, "rotation": 99.85, "x": 35.3, "y": 62.03, "color": "e80d0dff"}, {"name": "ren11", "parent": "ren10", "length": 18.81, "rotation": -3.21, "x": 16.1, "color": "e80d0dff"}, {"name": "ren12", "parent": "ren11", "x": 6.71, "y": 8.31, "color": "e80d0dff"}, {"name": "ren13", "parent": "ren11", "x": 8.62, "y": -13.59, "color": "e80d0dff"}, {"name": "ren14", "parent": "ren13", "length": 14.06, "rotation": -47.01, "x": -4.06, "y": -12.34, "color": "e80d0dff"}, {"name": "ren15", "parent": "ren14", "length": 19.13, "rotation": 10.88, "x": 13.84, "y": -0.18, "color": "e80d0dff"}, {"name": "ren16", "parent": "ren11", "length": 19.86, "rotation": -2.83, "x": 15.78, "y": -6.19, "color": "e80d0dff"}, {"name": "ren17", "parent": "ren12", "length": 14.96, "rotation": 150.8, "x": -10.54, "y": 4.16, "color": "e80d0dff"}, {"name": "ren18", "parent": "ren17", "length": 14.7, "rotation": -54.32, "x": 14.96, "color": "e80d0dff"}, {"name": "ren19", "parent": "ren4", "length": 15.26, "rotation": -150.74, "x": -6.26, "y": -3.7, "color": "e80d0dff"}, {"name": "ren20", "parent": "ren19", "length": 16.4, "rotation": 17.99, "x": 15.26, "color": "e80d0dff"}, {"name": "target1", "parent": "root", "x": 6.71, "y": -270.99, "color": "ff3f00ff"}, {"name": "ren21", "parent": "ren10", "length": 19.06, "rotation": 172.65, "x": -5.69, "y": 0.22, "color": "e80d0dff"}, {"name": "ren22", "parent": "ren21", "length": 19.66, "rotation": -17.24, "x": 19.06, "color": "e80d0dff"}, {"name": "huo2", "parent": "bone", "x": -14.4, "y": 119.16}, {"name": "bone6", "parent": "huo2", "length": 30, "rotation": 91.12, "x": -1.36, "y": 2.11}, {"name": "bone7", "parent": "bone6", "length": 30, "x": 30}, {"name": "bone8", "parent": "bone7", "length": 30, "x": 30}, {"name": "bone9", "parent": "bone8", "length": 30, "x": 30}, {"name": "huo3", "parent": "bone", "x": -14.4, "y": 119.16}, {"name": "bone10", "parent": "huo3", "length": 30, "rotation": 91.12, "x": -1.36, "y": 2.11}, {"name": "bone11", "parent": "bone10", "length": 30, "x": 30}, {"name": "bone12", "parent": "bone11", "length": 30, "x": 30}, {"name": "bone13", "parent": "bone12", "length": 30, "x": 30}, {"name": "bone14", "parent": "root", "length": 372.67, "rotation": 102.96, "x": -216.94, "y": -378.96}, {"name": "yun1", "parent": "root", "length": 482.52, "x": 524.16, "y": -625.87, "color": "e9ff00ff"}, {"name": "yun2", "parent": "yun1", "length": 291.79, "rotation": 115.96, "x": -173.32, "y": -76.1, "color": "e9ff00ff"}, {"name": "bone15", "parent": "yun1", "x": -531.48, "y": -193.05, "color": "f7ff00ff"}, {"name": "yun3", "parent": "yun1", "length": 724.98, "rotation": 165.3, "x": -351.47, "y": 185.54, "color": "e9ff00ff"}, {"name": "yun4", "parent": "yun1", "length": 287.22, "rotation": 73.09, "x": -819, "y": -26.5, "color": "e9ff00ff"}, {"name": "yun5", "parent": "yun1", "length": 425.18, "rotation": 100.67, "x": -801.32, "y": 240.26, "color": "e9ff00ff"}, {"name": "yun6", "parent": "yun1", "length": 351.65, "rotation": 52.24, "x": -526.52, "y": 13.67, "color": "e9ff00ff"}, {"name": "yun7", "parent": "yun1", "length": 447.73, "rotation": 26.66, "x": -373.18, "y": 310.02, "color": "e9ff00ff"}, {"name": "yun8", "parent": "yun1", "length": 571.23, "rotation": 157.33, "x": -619.55, "y": 314.85, "color": "e9ff00ff"}, {"name": "bone16", "parent": "yun1", "x": -531.48, "y": -193.05, "color": "f7ff00ff"}, {"name": "yun9", "parent": "yun1", "length": 291.79, "rotation": 115.96, "x": -173.32, "y": -76.1, "color": "e9ff00ff"}, {"name": "yun10", "parent": "yun1", "length": 351.65, "rotation": 52.24, "x": -526.52, "y": 13.67, "color": "e9ff00ff"}, {"name": "yun11", "parent": "yun1", "length": 287.22, "rotation": 73.09, "x": -819, "y": -26.5, "color": "e9ff00ff"}, {"name": "yun12", "parent": "yun1", "length": 425.18, "rotation": 100.67, "x": -801.32, "y": 240.26, "color": "e9ff00ff"}, {"name": "yun13", "parent": "yun1", "length": 571.23, "rotation": 157.33, "x": -619.55, "y": 314.85, "color": "e9ff00ff"}, {"name": "yun14", "parent": "yun1", "length": 724.98, "rotation": 165.3, "x": -351.47, "y": 185.54, "color": "e9ff00ff"}, {"name": "yun15", "parent": "yun1", "length": 447.73, "rotation": 26.66, "x": -373.18, "y": 310.02, "color": "e9ff00ff"}, {"name": "bone17", "parent": "root", "length": 457.97, "rotation": 0.25, "x": 572.51, "y": -191.25, "color": "3fff00ff"}, {"name": "bone18", "parent": "bone17", "length": 112.76, "rotation": 34.79, "x": -328.82, "y": 93.05, "color": "3fff00ff"}, {"name": "bone19", "parent": "bone17", "length": 119.92, "rotation": 0.9, "x": -386.77, "y": 280.35, "color": "3fff00ff"}, {"name": "bone20", "parent": "bone17", "length": 441.6, "rotation": 48.05, "x": -498.68, "y": 186.11, "color": "3fff00ff"}, {"name": "bone21", "parent": "bone17", "length": 99.23, "rotation": -25.26, "x": -442.29, "y": 195.46, "color": "3fff00ff"}, {"name": "bone22", "parent": "bone17", "length": 84.31, "rotation": 44.75, "x": -412.42, "y": 347.16, "color": "3fff00ff"}, {"name": "bone23", "parent": "root", "length": 88.02, "rotation": 134.23, "x": 294.42, "y": 356.56, "color": "0bfcf5ff"}, {"name": "bone24", "parent": "root", "length": 55.11, "rotation": 97.59, "x": -162.86, "y": 114.6}, {"name": "bone25", "parent": "root", "length": 63.58, "rotation": 21.08, "x": 189.72, "y": 13.77}, {"name": "bone26", "parent": "bone17", "length": 99.23, "rotation": -25.26, "x": -442.29, "y": 195.46, "color": "3fff00ff"}, {"name": "bone27", "parent": "bone17", "length": 441.6, "rotation": 48.05, "x": -498.68, "y": 186.11, "color": "3fff00ff"}, {"name": "bone28", "parent": "bone17", "length": 119.92, "rotation": 0.9, "x": -386.77, "y": 280.35, "color": "3fff00ff"}, {"name": "bone29", "parent": "bone17", "length": 112.76, "rotation": 34.79, "x": -328.82, "y": 93.05, "color": "3fff00ff"}, {"name": "bone30", "parent": "root", "length": 343.25, "rotation": 180, "x": -828.2, "y": -27.08, "color": "0541ffff"}, {"name": "bone31", "parent": "bone30", "length": 601.33, "rotation": -46.42, "x": -602.67, "y": 48.66, "color": "0541ffff"}, {"name": "bone32", "parent": "bone30", "length": 135.37, "rotation": -107.88, "x": -741.27, "y": -277.25, "color": "0541ffff"}, {"name": "bone33", "parent": "bone30", "length": 97.81, "rotation": -149.04, "x": -523.32, "y": -514.98, "color": "0541ffff"}, {"name": "bone34", "parent": "bone30", "length": 132.5, "rotation": -111.96, "x": -816.39, "y": -133.12, "color": "0541ffff"}, {"name": "bone35", "parent": "bone30", "length": 108.37, "rotation": -152.28, "x": -545.22, "y": 15.07, "color": "0541ffff"}, {"name": "bone36", "parent": "bone30", "length": 601.33, "rotation": -46.42, "x": -602.67, "y": 48.66, "color": "0541ffff"}, {"name": "bone37", "parent": "bone30", "length": 108.37, "rotation": -152.28, "x": -545.22, "y": 15.07, "color": "0541ffff"}, {"name": "bone38", "parent": "bone30", "length": 132.5, "rotation": -111.96, "x": -816.39, "y": -133.12, "color": "0541ffff"}, {"name": "bone39", "parent": "bone30", "length": 135.37, "rotation": -107.88, "x": -741.27, "y": -277.25, "color": "0541ffff"}, {"name": "bone40", "parent": "root", "length": 400.28, "rotation": -127.43, "x": 324.8, "y": 706.74}], "slots": [{"name": "rootcut", "bone": "root", "attachment": "rootcut"}, {"name": "kaishijm01", "bone": "root", "attachment": "kaishijm01"}, {"name": "kaishijm02", "bone": "bone32", "attachment": "kaishijm02"}, {"name": "kaishijm2", "bone": "bone39", "attachment": "kaishijm02"}, {"name": "kaishijm03", "bone": "bone33", "attachment": "kaishijm03"}, {"name": "kaishijm04", "bone": "shui2", "attachment": "kaishijm04"}, {"name": "kaishijm9", "bone": "shui42", "attachment": "kaishijm04"}, {"name": "kaishijm4", "bone": "shui9", "attachment": "kaishijm4"}, {"name": "kaishijm7", "bone": "shui29", "attachment": "kaishijm4"}, {"name": "kaishijm6", "bone": "shui9", "attachment": "kaishijm04"}, {"name": "kaishijm8", "bone": "shui29", "attachment": "kaishijm04"}, {"name": "kaishijm5", "bone": "shui16", "attachment": "kaishijm04"}, {"name": "kaishijm05", "bone": "hulu", "attachment": "kaishijm05"}, {"name": "kaishijm06", "bone": "bone34", "attachment": "kaishijm06"}, {"name": "kaishijm12", "bone": "bone38", "attachment": "kaishijm06"}, {"name": "kaishijm07", "bone": "bone31", "attachment": "kaishijm07"}, {"name": "kaishijm10", "bone": "bone36", "attachment": "kaishijm07"}, {"name": "kaishijm08", "bone": "root", "color": "ffffff9c", "attachment": "kaishijm08"}, {"name": "kaishijm09", "bone": "bone24", "attachment": "kaishijm09"}, {"name": "kaishijm010", "bone": "bone23", "attachment": "kaishijm010"}, {"name": "kaishijm011", "bone": "bone20", "attachment": "kaishijm011"}, {"name": "kaishijm11", "bone": "bone27", "attachment": "kaishijm011"}, {"name": "kaishijm012", "bone": "bone22", "attachment": "kaishijm012"}, {"name": "kaishijm013", "bone": "bone19", "attachment": "kaishijm013"}, {"name": "kaishijm13", "bone": "bone28", "attachment": "kaishijm013"}, {"name": "kaishijm014", "bone": "bone25", "attachment": "kaishijm014"}, {"name": "kaishijm016", "bone": "bone21", "attachment": "kaishijm016"}, {"name": "kaishijm17", "bone": "bone26", "attachment": "kaishijm016"}, {"name": "kaishijm16", "bone": "bone18", "attachment": "kaishijm016"}, {"name": "kaishijm18", "bone": "bone29", "attachment": "kaishijm016"}, {"name": "kaishijm017", "bone": "root", "attachment": "kaishijm017"}, {"name": "kaishijm018", "bone": "root", "attachment": "kaishijm018"}, {"name": "kaishijm019", "bone": "bone35", "attachment": "kaishijm019"}, {"name": "kaishijm19", "bone": "bone37", "attachment": "kaishijm019"}, {"name": "kaishijm020", "bone": "yun5", "attachment": "kaishijm020"}, {"name": "kaishijm20", "bone": "yun12", "attachment": "kaishijm020"}, {"name": "kaishijm021", "bone": "root", "color": "ffffffcc", "attachment": "kaishijm021"}, {"name": "kaishijm022", "bone": "root", "attachment": "kaishijm022"}, {"name": "shuiliu/liushui_01", "bone": "pubu1", "attachment": "shuiliu/liushui_01", "blend": "additive"}, {"name": "kaishijm023", "bone": "bone", "attachment": "kaishijm023"}, {"name": "kaishijm024", "bone": "bone14", "attachment": "kaishijm024"}, {"name": "kaishijm025", "bone": "bone14", "color": "ffffffb3", "attachment": "kaishijm025", "blend": "additive"}, {"name": "kaishijm026", "bone": "root", "attachment": "kaishijm026"}, {"name": "kaishijm027", "bone": "yun8", "attachment": "kaishijm027"}, {"name": "kaishijm27", "bone": "yun13", "attachment": "kaishijm027"}, {"name": "kaishijm028", "bone": "yun7", "attachment": "kaishijm028"}, {"name": "kaishijm28", "bone": "yun15", "attachment": "kaishijm028"}, {"name": "kaishijm029", "bone": "yun4", "attachment": "kaishijm029"}, {"name": "kaishijm29", "bone": "yun11", "attachment": "kaishijm029"}, {"name": "kaishijm030", "bone": "yun2", "attachment": "kaishijm030"}, {"name": "kaishijm30", "bone": "yun9", "attachment": "kaishijm030"}, {"name": "kaishijm031", "bone": "yun6", "attachment": "kaishijm031"}, {"name": "kaishijm31", "bone": "yun10", "attachment": "kaishijm031"}, {"name": "kaishijm032", "bone": "bone15", "attachment": "kaishijm032"}, {"name": "kaishijm32", "bone": "bone16", "attachment": "kaishijm032"}, {"name": "kaishijm033", "bone": "fein<PERSON>o", "attachment": "kaishijm033"}, {"name": "kaishijm034", "bone": "root", "attachment": "kaishijm034"}, {"name": "light", "bone": "bone40", "color": "fffac76e", "attachment": "light", "blend": "additive"}, {"name": "kaishijm035", "bone": "root", "attachment": "kaishijm035"}, {"name": "kaishijm036", "bone": "yun3", "attachment": "kaishijm036"}, {"name": "kaishijm36", "bone": "yun14", "attachment": "kaishijm036"}, {"name": "kaishijm023_1", "bone": "bone2", "attachment": "kaishijm023_1"}, {"name": "kaishijm023_3", "bone": "bone10", "attachment": "kaishijm023_1"}, {"name": "kaishijm023_2", "bone": "bone6", "attachment": "kaishijm023_1"}, {"name": "kaishijm044", "bone": "root"}], "ik": [{"name": "target1", "bones": ["ren19", "ren20"], "target": "target1"}, {"name": "target2", "order": 1, "bones": ["ren17", "ren18"], "target": "target1", "bendPositive": false}], "skins": [{"name": "default", "attachments": {"kaishijm01": {"kaishijm01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [376, -488, -376, -488, -376, 751, 376, 751], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 376, "height": 619}}, "kaishijm02": {"kaishijm02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [16.61, -257.26, -130.45, 198.61, 250.24, 321.41, 397.29, -134.46], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 239, "height": 200}}, "kaishijm03": {"kaishijm03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [146.58, -163.63, -64.36, -37.07, 123.94, 276.77, 334.89, 150.21], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 123, "height": 183}}, "kaishijm04": {"kaishijm04": {"type": "mesh", "uvs": [0.08749, 0.00703, 0.17673, 0.0621, 0.1873, 0.10128, 0.27086, 0.15226, 0.26482, 0.24916, 0.30675, 0.19123, 0.34729, 0.2245, 0.32894, 0.27663, 0.37079, 0.2722, 0.40975, 0.26808, 0.43129, 0.27732, 0.42307, 0.28732, 0.4079, 0.2971, 0.36947, 0.32364, 0.33099, 0.35021, 0.40601, 0.41866, 0.43897, 0.44856, 0.56621, 0.4949, 0.5856, 0.43918, 0.55157, 0.39863, 0.58675, 0.38656, 0.60053, 0.36476, 0.62831, 0.31022, 0.65003, 0.29558, 0.65493, 0.3101, 0.58645, 0.38873, 0.61365, 0.42346, 0.65032, 0.41789, 0.66846, 0.45659, 0.65441, 0.47846, 0.62676, 0.53579, 0.49524, 0.67625, 0.4725, 0.69009, 0.43246, 0.7697, 0.39489, 0.79711, 0.39439, 0.82273, 0.33451, 0.84939, 0.30601, 0.81348, 0.32425, 0.7793, 0.27866, 0.72012, 0.24394, 0.7397, 0.22912, 0.7139, 0.21689, 0.61148, 0.16517, 0.5737, 0.11329, 0.62149, 0.10403, 0.52406, 0.11778, 0.50684, 0.13519, 0.44831, 0.15851, 0.36994, 0.09351, 0.34392, 0.03911, 0.30073, 0.03578, 0.28086, 0.12085, 0.20668, 0.12294, 0.17662, 0.04352, 0.10575, 0.04388, 0.06865, 0.02624, 0.05226, 0.00197, 0.04085, 0.0022, 0.02729, 0.03529, 0.00439, 0.04959, 0.03843, 0.11009, 0.07659, 0.15933, 0.12543, 0.18888, 0.18495, 0.20435, 0.26431, 0.2339, 0.36046, 0.28032, 0.45203, 0.33097, 0.54513, 0.39991, 0.60159, 0.45197, 0.64738, 0.42805, 0.5146, 0.50402, 0.57107, 0.59547, 0.52986, 0.63064, 0.44592, 0.30683, 0.61547, 0.37436, 0.69788, 0.37858, 0.77114, 0.1252, 0.28028], "triangles": [54, 61, 53, 54, 55, 61, 55, 60, 61, 60, 0, 61, 61, 0, 1, 55, 56, 60, 60, 56, 58, 56, 57, 58, 58, 59, 60, 60, 59, 0, 52, 63, 64, 64, 63, 4, 4, 63, 3, 52, 53, 63, 53, 62, 63, 62, 2, 63, 63, 2, 3, 53, 61, 62, 62, 1, 2, 62, 61, 1, 65, 7, 14, 65, 4, 7, 14, 7, 13, 13, 8, 12, 13, 7, 8, 12, 9, 11, 12, 8, 9, 11, 9, 10, 4, 5, 7, 7, 5, 6, 49, 77, 48, 48, 77, 64, 49, 50, 77, 50, 51, 77, 51, 52, 77, 77, 52, 64, 44, 45, 43, 45, 46, 43, 46, 47, 43, 43, 47, 66, 42, 43, 66, 65, 47, 48, 47, 65, 66, 66, 14, 15, 66, 65, 14, 48, 64, 65, 65, 64, 4, 40, 41, 39, 75, 39, 74, 39, 41, 74, 41, 42, 74, 75, 74, 68, 74, 67, 68, 74, 42, 67, 67, 42, 66, 68, 67, 70, 67, 15, 70, 67, 66, 15, 70, 15, 16, 37, 38, 36, 35, 36, 34, 34, 36, 38, 76, 34, 38, 34, 76, 33, 76, 38, 75, 38, 39, 75, 76, 75, 33, 33, 75, 32, 75, 69, 32, 75, 68, 69, 32, 69, 31, 30, 31, 72, 31, 71, 72, 31, 69, 71, 69, 68, 71, 68, 70, 71, 71, 17, 72, 71, 70, 17, 30, 72, 29, 72, 17, 73, 72, 73, 29, 73, 18, 26, 18, 73, 17, 70, 16, 17, 29, 73, 28, 73, 27, 28, 73, 26, 27, 18, 25, 26, 18, 19, 25, 25, 19, 20, 25, 21, 24, 21, 22, 24, 25, 20, 21, 22, 23, 24], "vertices": [2, 3, 2.27, 82.5, 0.06654, 2, 20.27, 0.9, 0.93346, 2, 3, 21.14, 42.02, 0.56003, 2, 39.14, -39.58, 0.43997, 2, 3, 15.79, 26.55, 0.75035, 2, 33.8, -55.05, 0.24965, 3, 6, 61.31, 79.12, 0.00044, 5, 14.5, 57.76, 0.05188, 3, 33.6, -11.16, 0.94768, 4, 6, 36.67, 48.95, 0.06963, 4, 64.7, -26.32, 0.05825, 5, -10.13, 27.6, 0.54498, 3, 8.96, -41.33, 0.32714, 3, 4, 93.02, -17.94, 0.00035, 5, 18.19, 35.98, 0.9731, 3, 37.28, -32.94, 0.02655, 2, 5, 24.84, 14.9, 0.99361, 3, 43.93, -54.03, 0.00639, 2, 5, 6.23, 2.47, 0.99803, 3, 25.33, -66.45, 0.00197, 1, 5, 22.1, -6.62, 1, 2, 6, 83.68, 6.27, 0, 5, 36.88, -15.08, 1, 2, 6, 89.18, -2.17, 0, 5, 42.37, -23.53, 1, 2, 6, 83.94, -3.37, 0, 5, 37.14, -24.73, 1, 1, 5, 29.49, -24.11, 1, 4, 8, 40.9, 131.14, 1e-05, 7, 65.76, 63.66, 0.00293, 6, 56.5, -1.75, 0.02956, 5, 9.7, -23.1, 0.96751, 6, 8, 21.08, 132.15, 0.00287, 7, 45.94, 64.66, 0.06263, 6, 36.68, -0.75, 0.38267, 4, 64.71, -76.02, 0.00059, 5, -10.12, -22.1, 0.55086, 3, 8.97, -91.03, 0.00038, 4, 8, 31.8, 90.87, 0.13553, 7, 56.66, 23.39, 0.49623, 6, 47.4, -42.02, 0.32116, 5, 0.6, -63.37, 0.04708, 4, 8, 36.55, 72.79, 0.33324, 7, 61.41, 5.31, 0.50919, 6, 52.15, -60.1, 0.14571, 5, 5.35, -81.45, 0.01187, 3, 8, 70.92, 25.6, 0.95205, 7, 95.78, -41.88, 0.04718, 6, 86.52, -107.29, 0.00077, 2, 8, 90.73, 38.94, 0.99816, 7, 115.59, -28.55, 0.00184, 1, 8, 88.08, 60.77, 1, 2, 8, 103.36, 55.85, 1, 7, 128.22, -11.63, 0, 2, 8, 113.31, 59.51, 1, 7, 138.17, -7.98, 0, 1, 8, 135.82, 70.35, 1, 1, 8, 146.93, 69.66, 1, 1, 8, 145.29, 63.68, 1, 1, 8, 102.75, 55.22, 1, 2, 8, 104.33, 37.01, 0.99977, 7, 129.19, -30.47, 0.00023, 1, 8, 118.63, 29.6, 1, 1, 8, 116.08, 12.38, 1, 2, 8, 106.02, 8.77, 0.99983, 7, 130.88, -58.72, 0.00017, 2, 8, 82.91, -3.02, 0.99371, 7, 107.77, -70.5, 0.00629, 1, 8, 3.65, -15.82, 1, 2, 8, -7.63, -14.62, 0.96489, 7, 17.23, -82.11, 0.03511, 2, 8, -40.31, -30.57, 0.77773, 7, -15.45, -98.05, 0.22227, 2, 8, -60, -30.07, 0.7112, 7, -35.14, -97.56, 0.2888, 2, 8, -66.12, -38.32, 0.70299, 7, -41.26, -105.81, 0.29701, 2, 8, -93.55, -31.96, 0.68632, 7, -68.69, -99.44, 0.31368, 2, 8, -95.33, -13.04, 0.67331, 7, -70.47, -80.52, 0.32669, 2, 8, -80.92, -6.46, 0.62487, 7, -56.06, -73.94, 0.37513, 3, 8, -83.35, 24.38, 0.30384, 7, -58.49, -43.11, 0.68839, 6, -67.75, -108.52, 0.00777, 3, 8, -100.22, 26.72, 0.22366, 7, -75.35, -40.77, 0.76336, 6, -84.62, -106.18, 0.01298, 3, 8, -99.48, 38.88, 0.20813, 7, -74.62, -28.6, 0.77241, 6, -83.88, -94.01, 0.01946, 3, 8, -80.04, 75.46, 0.03934, 7, -55.18, 7.97, 0.70941, 6, -64.44, -57.44, 0.25125, 4, 8, -89.61, 100.83, 0.0006, 7, -64.75, 33.34, 0.42903, 6, -74.02, -32.07, 0.56874, 4, -45.98, -107.34, 0.00163, 2, 7, -94.25, 30.78, 0.37502, 6, -103.52, -34.63, 0.62498, 3, 7, -74.92, 64.97, 0.33985, 6, -84.18, -0.44, 0.6529, 4, -56.15, -75.71, 0.00725, 3, 7, -66.04, 67.14, 0.32065, 6, -75.3, 1.73, 0.66674, 4, -47.27, -73.54, 0.01261, 3, 7, -46.28, 81.89, 0.19663, 6, -55.54, 16.48, 0.74277, 4, -27.51, -58.79, 0.0606, 5, 7, -19.81, 101.64, 0.02116, 6, -29.08, 36.24, 0.54206, 4, -1.04, -39.04, 0.39107, 5, -75.88, 14.88, 0.02472, 3, -56.79, -54.04, 0.02098, 3, 6, -46.09, 61.11, 0.06407, 4, -18.06, -14.16, 0.93424, 5, -92.9, 39.76, 0.00169, 1, 4, -27.33, 13.66, 1, 1, 4, -23.9, 21, 1, 5, 6, -4.53, 99.1, 0.00086, 4, 23.5, 23.83, 0.44295, 5, -51.34, 77.75, 0.00225, 3, -32.24, 8.82, 0.5072, 2, -14.24, -72.78, 0.04673, 3, 4, 31.22, 33.13, 0.14076, 3, -24.53, 18.12, 0.67459, 2, -6.52, -63.48, 0.18464, 3, 4, 19.5, 76.3, 0.00012, 3, -36.24, 61.29, 0.20448, 2, -18.24, -20.31, 0.7954, 2, 3, -27.5, 73.34, 0.0751, 2, -9.5, -8.27, 0.9249, 2, 3, -29.95, 83.14, 0.00545, 2, -11.95, 1.53, 0.99455, 1, 2, -17.91, 11.38, 1, 1, 2, -14.68, 15.75, 1, 1, 2, 2.37, 14.91, 1, 1, 2, -0.46, 0.17, 1, 3, 4, 49.88, 69.07, 0.00026, 3, -5.86, 54.07, 0.33501, 2, 12.14, -27.54, 0.66473, 3, 4, 56.01, 40.7, 0.00866, 3, 0.27, 25.7, 0.73227, 2, 18.27, -55.9, 0.25907, 5, 6, 24.64, 89.08, 0.003, 4, 52.67, 13.8, 0.03059, 5, -22.16, 67.72, 0.0067, 3, -3.07, -1.2, 0.95673, 2, 14.93, -82.81, 0.00298, 4, 6, 11.7, 59.23, 0.14508, 4, 39.74, -16.05, 0.26991, 5, -35.1, 37.88, 0.17544, 3, -16.01, -31.05, 0.40957, 4, 6, -0.14, 20.35, 0.70643, 4, 27.89, -54.92, 0.12586, 5, -46.94, -1, 0.12643, 3, -27.85, -69.93, 0.04128, 4, 8, -20.53, 111.61, 0.00018, 7, 4.33, 44.13, 0.29605, 6, -4.93, -21.28, 0.69997, 5, -51.73, -42.63, 0.00381, 3, 7, 0.69, 0.93, 0.99267, 6, -8.58, -64.48, 0.00727, 5, -55.38, -85.83, 6e-05, 2, 8, -12.83, 32.6, 0.49976, 7, 12.03, -34.89, 0.50024, 2, 8, -5, 4.52, 0.93754, 7, 19.86, -62.97, 0.06246, 4, 8, 17.34, 53.95, 0.4235, 7, 42.2, -13.53, 0.52083, 6, 32.94, -78.94, 0.05367, 5, -13.86, -100.29, 0.002, 3, 8, 31.18, 16.36, 0.89385, 7, 56.04, -51.13, 0.10514, 6, 46.78, -116.54, 0.00101, 2, 8, 73.18, 6.8, 0.98434, 7, 98.04, -60.69, 0.01566, 2, 8, 105.15, 25.39, 0.99971, 7, 130.01, -42.1, 0.00029, 3, 8, -49.07, 51.5, 0.13746, 7, -24.21, -15.99, 0.83026, 6, -33.47, -81.39, 0.03228, 2, 8, -44.25, 7.55, 0.60613, 7, -19.39, -59.94, 0.39387, 2, 8, -59.76, -17.47, 0.69306, 7, -34.9, -84.95, 0.30694, 5, 6, -20.08, 73.94, 0.0299, 4, 7.95, -1.34, 0.87638, 5, -66.88, 52.59, 0.01057, 3, -47.79, -16.34, 0.08298, 2, -29.79, -97.94, 0.00017], "hull": 60, "edges": [0, 118, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 116, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 140, 142, 142, 144, 144, 146, 148, 150, 150, 152, 56, 58, 70, 72, 92, 94, 94, 96, 24, 26, 26, 28, 14, 16, 16, 18], "width": 433, "height": 399}}, "kaishijm05": {"kaishijm05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [31.97, -213.29, -229.77, -27.46, -68.25, 200.04, 193.49, 14.2], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 192, "height": 167}}, "kaishijm031": {"kaishijm031": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [27.65, -442, -326.3, 14.96, 133.82, 371.35, 487.77, -85.6], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 289, "height": 291}}, "kaishijm07": {"kaishijm07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-366.72, -9.28, 210.28, 597.05, 789.81, 45.56, 212.81, -560.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 418, "height": 400}}, "kaishijm08": {"kaishijm08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [594.11, -931.62, -541.29, -931.62, -541.29, 285, 594.11, 285], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 75, "height": 103}}, "kaishijm030": {"kaishijm030": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-55.1, -1.16, 69.21, 254.18, 530.46, 29.63, 406.14, -225.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 142, "height": 256}}, "kaishijm035": {"kaishijm035": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [378, 289, -378, 289, -378, 751, 378, 751], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 378, "height": 231}}, "kaishijm036": {"kaishijm036": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-367.8, 135.8, 1193.39, 545.29, 1369.72, -126.97, -191.47, -536.46], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 484, "height": 208}}, "kaishijm032": {"kaishijm032": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [517.32, -51.08, -665.68, -51.08, -665.68, 553.92, 517.32, 553.92], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 591, "height": 302}}, "kaishijm033": {"kaishijm033": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [500.25, 228.15, 648.23, -70.16, 428.75, -179.04, 280.77, 119.27], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 333, "height": 245}}, "kaishijm10": {"kaishijm07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-366.72, -9.28, 210.28, 597.05, 789.81, 45.56, 212.81, -560.77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 418, "height": 400}}, "kaishijm11": {"kaishijm011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [208.45, -416.93, -155.41, -8.51, 237.33, 341.39, 601.2, -67.03], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 273, "height": 263}}, "kaishijm12": {"kaishijm06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [44.89, -243.4, -143.59, 224.03, 66.94, 308.92, 255.42, -158.51], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 252, "height": 113}}, "kaishijm13": {"kaishijm013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [160.84, -82.66, -46.12, -78.52, -43.32, 61.45, 163.64, 57.32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 70}}, "kaishijm021": {"kaishijm021": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [607.28, -985.48, -501.44, -985.48, -501.44, 116, 607.28, 116], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 150, "height": 156}}, "kaishijm16": {"kaishijm016": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [77.43, -120.72, -77.31, -12.2, 12.84, 116.34, 167.58, 7.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 94, "height": 78}}, "kaishijm17": {"kaishijm016": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [110.51, -106.6, -70.01, -50.61, -23.5, 99.35, 157.02, 43.36], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 94, "height": 78}}, "kaishijm18": {"kaishijm016": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [77.43, -120.72, -77.31, -12.2, 12.84, 116.34, 167.58, 7.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 94, "height": 78}}, "kaishijm19": {"kaishijm019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [119.49, -176.71, -100.05, -61.36, 7.4, 143.13, 226.94, 27.78], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 124, "height": 115}}, "kaishijm2": {"kaishijm02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [16.61, -257.26, -130.45, 198.61, 250.24, 321.41, 397.29, -134.46], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 239, "height": 200}}, "kaishijm4": {"kaishijm04": {"type": "mesh", "uvs": [0.08749, 0.00703, 0.17673, 0.0621, 0.1873, 0.10128, 0.27086, 0.15226, 0.26482, 0.24916, 0.30675, 0.19123, 0.34729, 0.2245, 0.32894, 0.27663, 0.37079, 0.2722, 0.40975, 0.26808, 0.43129, 0.27732, 0.42307, 0.28732, 0.4079, 0.2971, 0.36947, 0.32364, 0.33099, 0.35021, 0.40601, 0.41866, 0.43897, 0.44856, 0.56621, 0.4949, 0.5856, 0.43918, 0.55157, 0.39863, 0.58675, 0.38656, 0.60053, 0.36476, 0.62831, 0.31022, 0.65003, 0.29558, 0.65493, 0.3101, 0.58645, 0.38873, 0.61365, 0.42346, 0.65032, 0.41789, 0.66846, 0.45659, 0.65441, 0.47846, 0.62676, 0.53579, 0.49524, 0.67625, 0.4725, 0.69009, 0.43246, 0.7697, 0.39489, 0.79711, 0.39439, 0.82273, 0.33451, 0.84939, 0.30601, 0.81348, 0.32425, 0.7793, 0.27866, 0.72012, 0.24394, 0.7397, 0.22912, 0.7139, 0.21689, 0.61148, 0.16517, 0.5737, 0.11329, 0.62149, 0.10403, 0.52406, 0.11778, 0.50684, 0.13519, 0.44831, 0.15851, 0.36994, 0.09351, 0.34392, 0.03911, 0.30073, 0.03578, 0.28086, 0.12085, 0.20668, 0.12294, 0.17662, 0.04352, 0.10575, 0.04388, 0.06865, 0.02624, 0.05226, 0.00197, 0.04085, 0.0022, 0.02729, 0.03529, 0.00439, 0.04959, 0.03843, 0.11009, 0.07659, 0.15933, 0.12543, 0.18888, 0.18495, 0.20435, 0.26431, 0.2339, 0.36046, 0.28032, 0.45203, 0.33097, 0.54513, 0.39991, 0.60159, 0.45197, 0.64738, 0.42805, 0.5146, 0.50402, 0.57107, 0.59547, 0.52986, 0.63064, 0.44592, 0.30683, 0.61547, 0.37436, 0.69788, 0.37858, 0.77114, 0.1252, 0.28028], "triangles": [54, 61, 53, 54, 55, 61, 55, 60, 61, 60, 0, 61, 61, 0, 1, 55, 56, 60, 60, 56, 58, 56, 57, 58, 58, 59, 60, 60, 59, 0, 52, 63, 64, 64, 63, 4, 4, 63, 3, 52, 53, 63, 53, 62, 63, 62, 2, 63, 63, 2, 3, 53, 61, 62, 62, 1, 2, 62, 61, 1, 65, 7, 14, 65, 4, 7, 14, 7, 13, 13, 8, 12, 13, 7, 8, 12, 9, 11, 12, 8, 9, 11, 9, 10, 4, 5, 7, 7, 5, 6, 49, 77, 48, 48, 77, 64, 49, 50, 77, 50, 51, 77, 51, 52, 77, 77, 52, 64, 44, 45, 43, 45, 46, 43, 46, 47, 43, 43, 47, 66, 42, 43, 66, 65, 47, 48, 47, 65, 66, 66, 14, 15, 66, 65, 14, 48, 64, 65, 65, 64, 4, 40, 41, 39, 75, 39, 74, 39, 41, 74, 41, 42, 74, 75, 74, 68, 74, 67, 68, 74, 42, 67, 67, 42, 66, 68, 67, 70, 67, 15, 70, 67, 66, 15, 70, 15, 16, 37, 38, 36, 35, 36, 34, 34, 36, 38, 76, 34, 38, 34, 76, 33, 76, 38, 75, 38, 39, 75, 76, 75, 33, 33, 75, 32, 75, 69, 32, 75, 68, 69, 32, 69, 31, 30, 31, 72, 31, 71, 72, 31, 69, 71, 69, 68, 71, 68, 70, 71, 71, 17, 72, 71, 70, 17, 30, 72, 29, 72, 17, 73, 72, 73, 29, 73, 18, 26, 18, 73, 17, 70, 16, 17, 29, 73, 28, 73, 27, 28, 73, 26, 27, 18, 25, 26, 18, 19, 25, 25, 19, 20, 25, 21, 24, 21, 22, 24, 25, 20, 21, 22, 23, 24], "vertices": [2, 10, 2.27, 82.5, 0.06654, 9, 20.27, 0.9, 0.93346, 2, 10, 21.14, 42.02, 0.56003, 9, 39.14, -39.58, 0.43997, 2, 10, 15.79, 26.55, 0.75035, 9, 33.8, -55.05, 0.24965, 3, 13, 61.31, 79.12, 0.00044, 12, 14.5, 57.76, 0.05188, 10, 33.6, -11.16, 0.94768, 4, 13, 36.67, 48.95, 0.06963, 11, 64.7, -26.32, 0.05825, 12, -10.13, 27.6, 0.54498, 10, 8.96, -41.33, 0.32714, 3, 11, 93.02, -17.94, 0.00035, 12, 18.19, 35.98, 0.9731, 10, 37.28, -32.94, 0.02655, 2, 12, 24.84, 14.9, 0.99361, 10, 43.93, -54.03, 0.00639, 2, 12, 6.23, 2.47, 0.99803, 10, 25.33, -66.45, 0.00197, 1, 12, 22.1, -6.62, 1, 2, 13, 83.68, 6.27, 0, 12, 36.88, -15.08, 1, 2, 13, 89.18, -2.17, 0, 12, 42.37, -23.53, 1, 2, 13, 83.94, -3.37, 0, 12, 37.14, -24.73, 1, 1, 12, 29.49, -24.11, 1, 4, 15, 40.9, 131.14, 1e-05, 14, 65.76, 63.66, 0.00293, 13, 56.5, -1.75, 0.02956, 12, 9.7, -23.1, 0.96751, 6, 15, 21.08, 132.15, 0.00287, 14, 45.94, 64.66, 0.06263, 13, 36.68, -0.75, 0.38267, 11, 64.71, -76.02, 0.00059, 12, -10.12, -22.1, 0.55086, 10, 8.97, -91.03, 0.00038, 4, 15, 31.8, 90.87, 0.13553, 14, 56.66, 23.39, 0.49623, 13, 47.4, -42.02, 0.32116, 12, 0.6, -63.37, 0.04708, 4, 15, 36.55, 72.79, 0.33324, 14, 61.41, 5.31, 0.50919, 13, 52.15, -60.1, 0.14571, 12, 5.35, -81.45, 0.01187, 3, 15, 70.92, 25.6, 0.95205, 14, 95.78, -41.88, 0.04718, 13, 86.52, -107.29, 0.00077, 2, 15, 90.73, 38.94, 0.99816, 14, 115.59, -28.55, 0.00184, 1, 15, 88.08, 60.77, 1, 2, 15, 103.36, 55.85, 1, 14, 128.22, -11.63, 0, 2, 15, 113.31, 59.51, 1, 14, 138.17, -7.98, 0, 1, 15, 135.82, 70.35, 1, 1, 15, 146.93, 69.66, 1, 1, 15, 145.29, 63.68, 1, 1, 15, 102.75, 55.22, 1, 2, 15, 104.33, 37.01, 0.99977, 14, 129.19, -30.47, 0.00023, 1, 15, 118.63, 29.6, 1, 1, 15, 116.08, 12.38, 1, 2, 15, 106.02, 8.77, 0.99983, 14, 130.88, -58.72, 0.00017, 2, 15, 82.91, -3.02, 0.99371, 14, 107.77, -70.5, 0.00629, 1, 15, 3.65, -15.82, 1, 2, 15, -7.63, -14.62, 0.96489, 14, 17.23, -82.11, 0.03511, 2, 15, -40.31, -30.57, 0.77773, 14, -15.45, -98.05, 0.22227, 2, 15, -60, -30.07, 0.7112, 14, -35.14, -97.56, 0.2888, 2, 15, -66.12, -38.32, 0.70299, 14, -41.26, -105.81, 0.29701, 2, 15, -93.55, -31.96, 0.68632, 14, -68.69, -99.44, 0.31368, 2, 15, -95.33, -13.04, 0.67331, 14, -70.47, -80.52, 0.32669, 2, 15, -80.92, -6.46, 0.62487, 14, -56.06, -73.94, 0.37513, 3, 15, -83.35, 24.38, 0.30384, 14, -58.49, -43.11, 0.68839, 13, -67.75, -108.52, 0.00777, 3, 15, -100.22, 26.72, 0.22366, 14, -75.35, -40.77, 0.76336, 13, -84.62, -106.18, 0.01298, 3, 15, -99.48, 38.88, 0.20813, 14, -74.62, -28.6, 0.77241, 13, -83.88, -94.01, 0.01946, 3, 15, -80.04, 75.46, 0.03934, 14, -55.18, 7.97, 0.70941, 13, -64.44, -57.44, 0.25125, 4, 15, -89.61, 100.83, 0.0006, 14, -64.75, 33.34, 0.42903, 13, -74.02, -32.07, 0.56874, 11, -45.98, -107.34, 0.00163, 2, 14, -94.25, 30.78, 0.37502, 13, -103.52, -34.63, 0.62498, 3, 14, -74.92, 64.97, 0.33985, 13, -84.18, -0.44, 0.6529, 11, -56.15, -75.71, 0.00725, 3, 14, -66.04, 67.14, 0.32065, 13, -75.3, 1.73, 0.66674, 11, -47.27, -73.54, 0.01261, 3, 14, -46.28, 81.89, 0.19663, 13, -55.54, 16.48, 0.74277, 11, -27.51, -58.79, 0.0606, 5, 14, -19.81, 101.64, 0.02116, 13, -29.08, 36.24, 0.54206, 11, -1.04, -39.04, 0.39107, 12, -75.88, 14.88, 0.02472, 10, -56.79, -54.04, 0.02098, 3, 13, -46.09, 61.11, 0.06407, 11, -18.06, -14.16, 0.93424, 12, -92.9, 39.76, 0.00169, 1, 11, -27.33, 13.66, 1, 1, 11, -23.9, 21, 1, 5, 13, -4.53, 99.1, 0.00086, 11, 23.5, 23.83, 0.44295, 12, -51.34, 77.75, 0.00225, 10, -32.24, 8.82, 0.5072, 9, -14.24, -72.78, 0.04673, 3, 11, 31.22, 33.13, 0.14076, 10, -24.53, 18.12, 0.67459, 9, -6.52, -63.48, 0.18464, 3, 11, 19.5, 76.3, 0.00012, 10, -36.24, 61.29, 0.20448, 9, -18.24, -20.31, 0.7954, 2, 10, -27.5, 73.34, 0.0751, 9, -9.5, -8.27, 0.9249, 2, 10, -29.95, 83.14, 0.00545, 9, -11.95, 1.53, 0.99455, 1, 9, -17.91, 11.38, 1, 1, 9, -14.68, 15.75, 1, 1, 9, 2.37, 14.91, 1, 1, 9, -0.46, 0.17, 1, 3, 11, 49.88, 69.07, 0.00026, 10, -5.86, 54.07, 0.33501, 9, 12.14, -27.54, 0.66473, 3, 11, 56.01, 40.7, 0.00866, 10, 0.27, 25.7, 0.73227, 9, 18.27, -55.9, 0.25907, 5, 13, 24.64, 89.08, 0.003, 11, 52.67, 13.8, 0.03059, 12, -22.16, 67.72, 0.0067, 10, -3.07, -1.2, 0.95673, 9, 14.93, -82.81, 0.00298, 4, 13, 11.7, 59.23, 0.14508, 11, 39.74, -16.05, 0.26991, 12, -35.1, 37.88, 0.17544, 10, -16.01, -31.05, 0.40957, 4, 13, -0.14, 20.35, 0.70643, 11, 27.89, -54.92, 0.12586, 12, -46.94, -1, 0.12643, 10, -27.85, -69.93, 0.04128, 4, 15, -20.53, 111.61, 0.00018, 14, 4.33, 44.13, 0.29605, 13, -4.93, -21.28, 0.69997, 12, -51.73, -42.63, 0.00381, 3, 14, 0.69, 0.93, 0.99267, 13, -8.58, -64.48, 0.00727, 12, -55.38, -85.83, 6e-05, 2, 15, -12.83, 32.6, 0.49976, 14, 12.03, -34.89, 0.50024, 2, 15, -5, 4.52, 0.93754, 14, 19.86, -62.97, 0.06246, 4, 15, 17.34, 53.95, 0.4235, 14, 42.2, -13.53, 0.52083, 13, 32.94, -78.94, 0.05367, 12, -13.86, -100.29, 0.002, 3, 15, 31.18, 16.36, 0.89385, 14, 56.04, -51.13, 0.10514, 13, 46.78, -116.54, 0.00101, 2, 15, 73.18, 6.8, 0.98434, 14, 98.04, -60.69, 0.01566, 2, 15, 105.15, 25.39, 0.99971, 14, 130.01, -42.1, 0.00029, 3, 15, -49.07, 51.5, 0.13746, 14, -24.21, -15.99, 0.83026, 13, -33.47, -81.39, 0.03228, 2, 15, -44.25, 7.55, 0.60613, 14, -19.39, -59.94, 0.39387, 2, 15, -59.76, -17.47, 0.69306, 14, -34.9, -84.95, 0.30694, 5, 13, -20.08, 73.94, 0.0299, 11, 7.95, -1.34, 0.87638, 12, -66.88, 52.59, 0.01057, 10, -47.79, -16.34, 0.08298, 9, -29.79, -97.94, 0.00017], "hull": 60, "edges": [0, 118, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 116, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 140, 142, 142, 144, 144, 146, 148, 150, 150, 152, 56, 58, 70, 72, 92, 94, 94, 96, 24, 26, 26, 28, 14, 16, 16, 18], "width": 433, "height": 399}, "kaishijm4": {"type": "mesh", "path": "kaishijm04", "uvs": [0.08749, 0.00703, 0.17673, 0.0621, 0.1873, 0.10128, 0.27086, 0.15226, 0.26482, 0.24916, 0.30675, 0.19123, 0.34729, 0.2245, 0.32894, 0.27663, 0.37079, 0.2722, 0.40975, 0.26808, 0.43129, 0.27732, 0.42307, 0.28732, 0.4079, 0.2971, 0.36947, 0.32364, 0.33099, 0.35021, 0.40601, 0.41866, 0.43897, 0.44856, 0.56621, 0.4949, 0.5856, 0.43918, 0.55157, 0.39863, 0.58675, 0.38656, 0.60053, 0.36476, 0.62831, 0.31022, 0.65003, 0.29558, 0.65493, 0.3101, 0.58645, 0.38873, 0.61365, 0.42346, 0.65032, 0.41789, 0.66846, 0.45659, 0.65441, 0.47846, 0.62676, 0.53579, 0.49524, 0.67625, 0.4725, 0.69009, 0.43246, 0.7697, 0.39489, 0.79711, 0.39439, 0.82273, 0.33451, 0.84939, 0.30601, 0.81348, 0.32425, 0.7793, 0.27866, 0.72012, 0.24394, 0.7397, 0.22912, 0.7139, 0.21689, 0.61148, 0.16517, 0.5737, 0.11329, 0.62149, 0.10403, 0.52406, 0.11778, 0.50684, 0.13519, 0.44831, 0.15851, 0.36994, 0.09351, 0.34392, 0.03911, 0.30073, 0.03578, 0.28086, 0.12085, 0.20668, 0.12294, 0.17662, 0.04352, 0.10575, 0.04388, 0.06865, 0.02624, 0.05226, 0.00197, 0.04085, 0.0022, 0.02729, 0.03529, 0.00439, 0.04959, 0.03843, 0.11009, 0.07659, 0.15933, 0.12543, 0.18888, 0.18495, 0.20435, 0.26431, 0.2339, 0.36046, 0.28032, 0.45203, 0.33097, 0.54513, 0.39991, 0.60159, 0.45197, 0.64738, 0.42805, 0.5146, 0.50402, 0.57107, 0.59547, 0.52986, 0.63064, 0.44592, 0.30683, 0.61547, 0.37436, 0.69788, 0.37858, 0.77114, 0.1252, 0.28028], "triangles": [54, 61, 53, 54, 55, 61, 55, 60, 61, 60, 0, 61, 61, 0, 1, 55, 56, 60, 60, 56, 58, 56, 57, 58, 58, 59, 60, 60, 59, 0, 52, 63, 64, 64, 63, 4, 4, 63, 3, 52, 53, 63, 53, 62, 63, 62, 2, 63, 63, 2, 3, 53, 61, 62, 62, 1, 2, 62, 61, 1, 65, 7, 14, 65, 4, 7, 14, 7, 13, 13, 8, 12, 13, 7, 8, 12, 9, 11, 12, 8, 9, 11, 9, 10, 4, 5, 7, 7, 5, 6, 49, 77, 48, 48, 77, 64, 49, 50, 77, 50, 51, 77, 51, 52, 77, 77, 52, 64, 44, 45, 43, 45, 46, 43, 46, 47, 43, 43, 47, 66, 42, 43, 66, 65, 47, 48, 47, 65, 66, 66, 14, 15, 66, 65, 14, 48, 64, 65, 65, 64, 4, 40, 41, 39, 75, 39, 74, 39, 41, 74, 41, 42, 74, 75, 74, 68, 74, 67, 68, 74, 42, 67, 67, 42, 66, 68, 67, 70, 67, 15, 70, 67, 66, 15, 70, 15, 16, 37, 38, 36, 35, 36, 34, 34, 36, 38, 76, 34, 38, 34, 76, 33, 76, 38, 75, 38, 39, 75, 76, 75, 33, 33, 75, 32, 75, 69, 32, 75, 68, 69, 32, 69, 31, 30, 31, 72, 31, 71, 72, 31, 69, 71, 69, 68, 71, 68, 70, 71, 71, 17, 72, 71, 70, 17, 30, 72, 29, 72, 17, 73, 72, 73, 29, 73, 18, 26, 18, 73, 17, 70, 16, 17, 29, 73, 28, 73, 27, 28, 73, 26, 27, 18, 25, 26, 18, 19, 25, 25, 19, 20, 25, 21, 24, 21, 22, 24, 25, 20, 21, 22, 23, 24], "vertices": [2, 10, 2.27, 82.5, 0.06654, 9, 20.27, 0.9, 0.93346, 2, 10, 21.14, 42.02, 0.56003, 9, 39.14, -39.58, 0.43997, 2, 10, 15.79, 26.55, 0.75035, 9, 33.8, -55.05, 0.24965, 3, 13, 61.31, 79.12, 0.00044, 12, 14.5, 57.76, 0.05188, 10, 33.6, -11.16, 0.94768, 4, 13, 36.67, 48.95, 0.06963, 11, 64.7, -26.32, 0.05825, 12, -10.13, 27.6, 0.54498, 10, 8.96, -41.33, 0.32714, 3, 11, 93.02, -17.94, 0.00035, 12, 18.19, 35.98, 0.9731, 10, 37.28, -32.94, 0.02655, 2, 12, 24.84, 14.9, 0.99361, 10, 43.93, -54.03, 0.00639, 2, 12, 6.23, 2.47, 0.99803, 10, 25.33, -66.45, 0.00197, 1, 12, 22.1, -6.62, 1, 2, 13, 83.68, 6.27, 0, 12, 36.88, -15.08, 1, 2, 13, 89.18, -2.17, 0, 12, 42.37, -23.53, 1, 2, 13, 83.94, -3.37, 0, 12, 37.14, -24.73, 1, 1, 12, 29.49, -24.11, 1, 4, 15, 40.9, 131.14, 1e-05, 14, 65.76, 63.66, 0.00293, 13, 56.5, -1.75, 0.02956, 12, 9.7, -23.1, 0.96751, 6, 15, 21.08, 132.15, 0.00287, 14, 45.94, 64.66, 0.06263, 13, 36.68, -0.75, 0.38267, 11, 64.71, -76.02, 0.00059, 12, -10.12, -22.1, 0.55086, 10, 8.97, -91.03, 0.00038, 4, 15, 31.8, 90.87, 0.13553, 14, 56.66, 23.39, 0.49623, 13, 47.4, -42.02, 0.32116, 12, 0.6, -63.37, 0.04708, 4, 15, 36.55, 72.79, 0.33324, 14, 61.41, 5.31, 0.50919, 13, 52.15, -60.1, 0.14571, 12, 5.35, -81.45, 0.01187, 3, 15, 70.92, 25.6, 0.95205, 14, 95.78, -41.88, 0.04718, 13, 86.52, -107.29, 0.00077, 2, 15, 90.73, 38.94, 0.99816, 14, 115.59, -28.55, 0.00184, 1, 15, 88.08, 60.77, 1, 2, 15, 103.36, 55.85, 1, 14, 128.22, -11.63, 0, 2, 15, 113.31, 59.51, 1, 14, 138.17, -7.98, 0, 1, 15, 135.82, 70.35, 1, 1, 15, 146.93, 69.66, 1, 1, 15, 145.29, 63.68, 1, 1, 15, 102.75, 55.22, 1, 2, 15, 104.33, 37.01, 0.99977, 14, 129.19, -30.47, 0.00023, 1, 15, 118.63, 29.6, 1, 1, 15, 116.08, 12.38, 1, 2, 15, 106.02, 8.77, 0.99983, 14, 130.88, -58.72, 0.00017, 2, 15, 82.91, -3.02, 0.99371, 14, 107.77, -70.5, 0.00629, 1, 15, 3.65, -15.82, 1, 2, 15, -7.63, -14.62, 0.96489, 14, 17.23, -82.11, 0.03511, 2, 15, -40.31, -30.57, 0.77773, 14, -15.45, -98.05, 0.22227, 2, 15, -60, -30.07, 0.7112, 14, -35.14, -97.56, 0.2888, 2, 15, -66.12, -38.32, 0.70299, 14, -41.26, -105.81, 0.29701, 2, 15, -93.55, -31.96, 0.68632, 14, -68.69, -99.44, 0.31368, 2, 15, -95.33, -13.04, 0.67331, 14, -70.47, -80.52, 0.32669, 2, 15, -80.92, -6.46, 0.62487, 14, -56.06, -73.94, 0.37513, 3, 15, -83.35, 24.38, 0.30384, 14, -58.49, -43.11, 0.68839, 13, -67.75, -108.52, 0.00777, 3, 15, -100.22, 26.72, 0.22366, 14, -75.35, -40.77, 0.76336, 13, -84.62, -106.18, 0.01298, 3, 15, -99.48, 38.88, 0.20813, 14, -74.62, -28.6, 0.77241, 13, -83.88, -94.01, 0.01946, 3, 15, -80.04, 75.46, 0.03934, 14, -55.18, 7.97, 0.70941, 13, -64.44, -57.44, 0.25125, 4, 15, -89.61, 100.83, 0.0006, 14, -64.75, 33.34, 0.42903, 13, -74.02, -32.07, 0.56874, 11, -45.98, -107.34, 0.00163, 2, 14, -94.25, 30.78, 0.37502, 13, -103.52, -34.63, 0.62498, 3, 14, -74.92, 64.97, 0.33985, 13, -84.18, -0.44, 0.6529, 11, -56.15, -75.71, 0.00725, 3, 14, -66.04, 67.14, 0.32065, 13, -75.3, 1.73, 0.66674, 11, -47.27, -73.54, 0.01261, 3, 14, -46.28, 81.89, 0.19663, 13, -55.54, 16.48, 0.74277, 11, -27.51, -58.79, 0.0606, 5, 14, -19.81, 101.64, 0.02116, 13, -29.08, 36.24, 0.54206, 11, -1.04, -39.04, 0.39107, 12, -75.88, 14.88, 0.02472, 10, -56.79, -54.04, 0.02098, 3, 13, -46.09, 61.11, 0.06407, 11, -18.06, -14.16, 0.93424, 12, -92.9, 39.76, 0.00169, 1, 11, -27.33, 13.66, 1, 1, 11, -23.9, 21, 1, 5, 13, -4.53, 99.1, 0.00086, 11, 23.5, 23.83, 0.44295, 12, -51.34, 77.75, 0.00225, 10, -32.24, 8.82, 0.5072, 9, -14.24, -72.78, 0.04673, 3, 11, 31.22, 33.13, 0.14076, 10, -24.53, 18.12, 0.67459, 9, -6.52, -63.48, 0.18464, 3, 11, 19.5, 76.3, 0.00012, 10, -36.24, 61.29, 0.20448, 9, -18.24, -20.31, 0.7954, 2, 10, -27.5, 73.34, 0.0751, 9, -9.5, -8.27, 0.9249, 2, 10, -29.95, 83.14, 0.00545, 9, -11.95, 1.53, 0.99455, 1, 9, -17.91, 11.38, 1, 1, 9, -14.68, 15.75, 1, 1, 9, 2.37, 14.91, 1, 1, 9, -0.46, 0.17, 1, 3, 11, 49.88, 69.07, 0.00026, 10, -5.86, 54.07, 0.33501, 9, 12.14, -27.54, 0.66473, 3, 11, 56.01, 40.7, 0.00866, 10, 0.27, 25.7, 0.73227, 9, 18.27, -55.9, 0.25907, 5, 13, 24.64, 89.08, 0.003, 11, 52.67, 13.8, 0.03059, 12, -22.16, 67.72, 0.0067, 10, -3.07, -1.2, 0.95673, 9, 14.93, -82.81, 0.00298, 4, 13, 11.7, 59.23, 0.14508, 11, 39.74, -16.05, 0.26991, 12, -35.1, 37.88, 0.17544, 10, -16.01, -31.05, 0.40957, 4, 13, -0.14, 20.35, 0.70643, 11, 27.89, -54.92, 0.12586, 12, -46.94, -1, 0.12643, 10, -27.85, -69.93, 0.04128, 4, 15, -20.53, 111.61, 0.00018, 14, 4.33, 44.13, 0.29605, 13, -4.93, -21.28, 0.69997, 12, -51.73, -42.63, 0.00381, 3, 14, 0.69, 0.93, 0.99267, 13, -8.58, -64.48, 0.00727, 12, -55.38, -85.83, 6e-05, 2, 15, -12.83, 32.6, 0.49976, 14, 12.03, -34.89, 0.50024, 2, 15, -5, 4.52, 0.93754, 14, 19.86, -62.97, 0.06246, 4, 15, 17.34, 53.95, 0.4235, 14, 42.2, -13.53, 0.52083, 13, 32.94, -78.94, 0.05367, 12, -13.86, -100.29, 0.002, 3, 15, 31.18, 16.36, 0.89385, 14, 56.04, -51.13, 0.10514, 13, 46.78, -116.54, 0.00101, 2, 15, 73.18, 6.8, 0.98434, 14, 98.04, -60.69, 0.01566, 2, 15, 105.15, 25.39, 0.99971, 14, 130.01, -42.1, 0.00029, 3, 15, -49.07, 51.5, 0.13746, 14, -24.21, -15.99, 0.83026, 13, -33.47, -81.39, 0.03228, 2, 15, -44.25, 7.55, 0.60613, 14, -19.39, -59.94, 0.39387, 2, 15, -59.76, -17.47, 0.69306, 14, -34.9, -84.95, 0.30694, 5, 13, -20.08, 73.94, 0.0299, 11, 7.95, -1.34, 0.87638, 12, -66.88, 52.59, 0.01057, 10, -47.79, -16.34, 0.08298, 9, -29.79, -97.94, 0.00017], "hull": 60, "edges": [0, 118, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 116, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 140, 142, 142, 144, 144, 146, 148, 150, 150, 152, 56, 58, 70, 72, 92, 94, 94, 96, 24, 26, 26, 28, 14, 16, 16, 18], "width": 433, "height": 399}}, "kaishijm5": {"kaishijm04": {"type": "mesh", "uvs": [0.08749, 0.00703, 0.17673, 0.0621, 0.1873, 0.10128, 0.27086, 0.15226, 0.26482, 0.24916, 0.30675, 0.19123, 0.34729, 0.2245, 0.32894, 0.27663, 0.37079, 0.2722, 0.40975, 0.26808, 0.43129, 0.27732, 0.42307, 0.28732, 0.4079, 0.2971, 0.36947, 0.32364, 0.33099, 0.35021, 0.40601, 0.41866, 0.43897, 0.44856, 0.56621, 0.4949, 0.5856, 0.43918, 0.55157, 0.39863, 0.58675, 0.38656, 0.60053, 0.36476, 0.62831, 0.31022, 0.65003, 0.29558, 0.65493, 0.3101, 0.58645, 0.38873, 0.61365, 0.42346, 0.65032, 0.41789, 0.66846, 0.45659, 0.65441, 0.47846, 0.62676, 0.53579, 0.49524, 0.67625, 0.4725, 0.69009, 0.43246, 0.7697, 0.39489, 0.79711, 0.39439, 0.82273, 0.33451, 0.84939, 0.30601, 0.81348, 0.32425, 0.7793, 0.27866, 0.72012, 0.24394, 0.7397, 0.22912, 0.7139, 0.21689, 0.61148, 0.16517, 0.5737, 0.11329, 0.62149, 0.10403, 0.52406, 0.11778, 0.50684, 0.13519, 0.44831, 0.15851, 0.36994, 0.09351, 0.34392, 0.03911, 0.30073, 0.03578, 0.28086, 0.12085, 0.20668, 0.12294, 0.17662, 0.04352, 0.10575, 0.04388, 0.06865, 0.02624, 0.05226, 0.00197, 0.04085, 0.0022, 0.02729, 0.03529, 0.00439, 0.04959, 0.03843, 0.11009, 0.07659, 0.15933, 0.12543, 0.18888, 0.18495, 0.20435, 0.26431, 0.2339, 0.36046, 0.28032, 0.45203, 0.33097, 0.54513, 0.39991, 0.60159, 0.45197, 0.64738, 0.42805, 0.5146, 0.50402, 0.57107, 0.59547, 0.52986, 0.63064, 0.44592, 0.30683, 0.61547, 0.37436, 0.69788, 0.37858, 0.77114, 0.1252, 0.28028], "triangles": [54, 61, 53, 54, 55, 61, 55, 60, 61, 60, 0, 61, 61, 0, 1, 55, 56, 60, 60, 56, 58, 56, 57, 58, 58, 59, 60, 60, 59, 0, 52, 63, 64, 64, 63, 4, 4, 63, 3, 52, 53, 63, 53, 62, 63, 62, 2, 63, 63, 2, 3, 53, 61, 62, 62, 1, 2, 62, 61, 1, 65, 7, 14, 65, 4, 7, 14, 7, 13, 13, 8, 12, 13, 7, 8, 12, 9, 11, 12, 8, 9, 11, 9, 10, 4, 5, 7, 7, 5, 6, 49, 77, 48, 48, 77, 64, 49, 50, 77, 50, 51, 77, 51, 52, 77, 77, 52, 64, 44, 45, 43, 45, 46, 43, 46, 47, 43, 43, 47, 66, 42, 43, 66, 65, 47, 48, 47, 65, 66, 66, 14, 15, 66, 65, 14, 48, 64, 65, 65, 64, 4, 40, 41, 39, 75, 39, 74, 39, 41, 74, 41, 42, 74, 75, 74, 68, 74, 67, 68, 74, 42, 67, 67, 42, 66, 68, 67, 70, 67, 15, 70, 67, 66, 15, 70, 15, 16, 37, 38, 36, 35, 36, 34, 34, 36, 38, 76, 34, 38, 34, 76, 33, 76, 38, 75, 38, 39, 75, 76, 75, 33, 33, 75, 32, 75, 69, 32, 75, 68, 69, 32, 69, 31, 30, 31, 72, 31, 71, 72, 31, 69, 71, 69, 68, 71, 68, 70, 71, 71, 17, 72, 71, 70, 17, 30, 72, 29, 72, 17, 73, 72, 73, 29, 73, 18, 26, 18, 73, 17, 70, 16, 17, 29, 73, 28, 73, 27, 28, 73, 26, 27, 18, 25, 26, 18, 19, 25, 25, 19, 20, 25, 21, 24, 21, 22, 24, 25, 20, 21, 22, 23, 24], "vertices": [2, 17, 2.27, 82.5, 0.06654, 16, 20.27, 0.9, 0.93346, 2, 17, 21.14, 42.02, 0.56003, 16, 39.14, -39.58, 0.43997, 2, 17, 15.79, 26.55, 0.75035, 16, 33.8, -55.05, 0.24965, 3, 20, 61.31, 79.12, 0.00044, 19, 14.5, 57.76, 0.05188, 17, 33.6, -11.16, 0.94768, 4, 20, 36.67, 48.95, 0.06963, 18, 64.7, -26.32, 0.05825, 19, -10.13, 27.6, 0.54498, 17, 8.96, -41.33, 0.32714, 3, 18, 93.02, -17.94, 0.00035, 19, 18.19, 35.98, 0.9731, 17, 37.28, -32.94, 0.02655, 2, 19, 24.84, 14.9, 0.99361, 17, 43.93, -54.03, 0.00639, 2, 19, 6.23, 2.47, 0.99803, 17, 25.33, -66.45, 0.00197, 1, 19, 22.1, -6.62, 1, 2, 20, 83.68, 6.27, 0, 19, 36.88, -15.08, 1, 2, 20, 89.18, -2.17, 0, 19, 42.37, -23.53, 1, 2, 20, 83.94, -3.37, 0, 19, 37.14, -24.73, 1, 1, 19, 29.49, -24.11, 1, 4, 22, 40.9, 131.14, 1e-05, 21, 65.76, 63.66, 0.00293, 20, 56.5, -1.75, 0.02956, 19, 9.7, -23.1, 0.96751, 6, 22, 21.08, 132.15, 0.00287, 21, 45.94, 64.66, 0.06263, 20, 36.68, -0.75, 0.38267, 18, 64.71, -76.02, 0.00059, 19, -10.12, -22.1, 0.55086, 17, 8.97, -91.03, 0.00038, 4, 22, 31.8, 90.87, 0.13553, 21, 56.66, 23.39, 0.49623, 20, 47.4, -42.02, 0.32116, 19, 0.6, -63.37, 0.04708, 4, 22, 36.55, 72.79, 0.33324, 21, 61.41, 5.31, 0.50919, 20, 52.15, -60.1, 0.14571, 19, 5.35, -81.45, 0.01187, 3, 22, 70.92, 25.6, 0.95205, 21, 95.78, -41.88, 0.04718, 20, 86.52, -107.29, 0.00077, 2, 22, 90.73, 38.94, 0.99816, 21, 115.59, -28.55, 0.00184, 1, 22, 88.08, 60.77, 1, 2, 22, 103.36, 55.85, 1, 21, 128.22, -11.63, 0, 2, 22, 113.31, 59.51, 1, 21, 138.17, -7.98, 0, 1, 22, 135.82, 70.35, 1, 1, 22, 146.93, 69.66, 1, 1, 22, 145.29, 63.68, 1, 1, 22, 102.75, 55.22, 1, 2, 22, 104.33, 37.01, 0.99977, 21, 129.19, -30.47, 0.00023, 1, 22, 118.63, 29.6, 1, 1, 22, 116.08, 12.38, 1, 2, 22, 106.02, 8.77, 0.99983, 21, 130.88, -58.72, 0.00017, 2, 22, 82.91, -3.02, 0.99371, 21, 107.77, -70.5, 0.00629, 1, 22, 3.65, -15.82, 1, 2, 22, -7.63, -14.62, 0.96489, 21, 17.23, -82.11, 0.03511, 2, 22, -40.31, -30.57, 0.77773, 21, -15.45, -98.05, 0.22227, 2, 22, -60, -30.07, 0.7112, 21, -35.14, -97.56, 0.2888, 2, 22, -66.12, -38.32, 0.70299, 21, -41.26, -105.81, 0.29701, 2, 22, -93.55, -31.96, 0.68632, 21, -68.69, -99.44, 0.31368, 2, 22, -95.33, -13.04, 0.67331, 21, -70.47, -80.52, 0.32669, 2, 22, -80.92, -6.46, 0.62487, 21, -56.06, -73.94, 0.37513, 3, 22, -83.35, 24.38, 0.30384, 21, -58.49, -43.11, 0.68839, 20, -67.75, -108.52, 0.00777, 3, 22, -100.22, 26.72, 0.22366, 21, -75.35, -40.77, 0.76336, 20, -84.62, -106.18, 0.01298, 3, 22, -99.48, 38.88, 0.20813, 21, -74.62, -28.6, 0.77241, 20, -83.88, -94.01, 0.01946, 3, 22, -80.04, 75.46, 0.03934, 21, -55.18, 7.97, 0.70941, 20, -64.44, -57.44, 0.25125, 4, 22, -89.61, 100.83, 0.0006, 21, -64.75, 33.34, 0.42903, 20, -74.02, -32.07, 0.56874, 18, -45.98, -107.34, 0.00163, 2, 21, -94.25, 30.78, 0.37502, 20, -103.52, -34.63, 0.62498, 3, 21, -74.92, 64.97, 0.33985, 20, -84.18, -0.44, 0.6529, 18, -56.15, -75.71, 0.00725, 3, 21, -66.04, 67.14, 0.32065, 20, -75.3, 1.73, 0.66674, 18, -47.27, -73.54, 0.01261, 3, 21, -46.28, 81.89, 0.19663, 20, -55.54, 16.48, 0.74277, 18, -27.51, -58.79, 0.0606, 5, 21, -19.81, 101.64, 0.02116, 20, -29.08, 36.24, 0.54206, 18, -1.04, -39.04, 0.39107, 19, -75.88, 14.88, 0.02472, 17, -56.79, -54.04, 0.02098, 3, 20, -46.09, 61.11, 0.06407, 18, -18.06, -14.16, 0.93424, 19, -92.9, 39.76, 0.00169, 1, 18, -27.33, 13.66, 1, 1, 18, -23.9, 21, 1, 5, 20, -4.53, 99.1, 0.00086, 18, 23.5, 23.83, 0.44295, 19, -51.34, 77.75, 0.00225, 17, -32.24, 8.82, 0.5072, 16, -14.24, -72.78, 0.04673, 3, 18, 31.22, 33.13, 0.14076, 17, -24.53, 18.12, 0.67459, 16, -6.52, -63.48, 0.18464, 3, 18, 19.5, 76.3, 0.00012, 17, -36.24, 61.29, 0.20448, 16, -18.24, -20.31, 0.7954, 2, 17, -27.5, 73.34, 0.0751, 16, -9.5, -8.27, 0.9249, 2, 17, -29.95, 83.14, 0.00545, 16, -11.95, 1.53, 0.99455, 1, 16, -17.91, 11.38, 1, 1, 16, -14.68, 15.75, 1, 1, 16, 2.37, 14.91, 1, 1, 16, -0.46, 0.17, 1, 3, 18, 49.88, 69.07, 0.00026, 17, -5.86, 54.07, 0.33501, 16, 12.14, -27.54, 0.66473, 3, 18, 56.01, 40.7, 0.00866, 17, 0.27, 25.7, 0.73227, 16, 18.27, -55.9, 0.25907, 5, 20, 24.64, 89.08, 0.003, 18, 52.67, 13.8, 0.03059, 19, -22.16, 67.72, 0.0067, 17, -3.07, -1.2, 0.95673, 16, 14.93, -82.81, 0.00298, 4, 20, 11.7, 59.23, 0.14508, 18, 39.74, -16.05, 0.26991, 19, -35.1, 37.88, 0.17544, 17, -16.01, -31.05, 0.40957, 4, 20, -0.14, 20.35, 0.70643, 18, 27.89, -54.92, 0.12586, 19, -46.94, -1, 0.12643, 17, -27.85, -69.93, 0.04128, 4, 22, -20.53, 111.61, 0.00018, 21, 4.33, 44.13, 0.29605, 20, -4.93, -21.28, 0.69997, 19, -51.73, -42.63, 0.00381, 3, 21, 0.69, 0.93, 0.99267, 20, -8.58, -64.48, 0.00727, 19, -55.38, -85.83, 6e-05, 2, 22, -12.83, 32.6, 0.49976, 21, 12.03, -34.89, 0.50024, 2, 22, -5, 4.52, 0.93754, 21, 19.86, -62.97, 0.06246, 4, 22, 17.34, 53.95, 0.4235, 21, 42.2, -13.53, 0.52083, 20, 32.94, -78.94, 0.05367, 19, -13.86, -100.29, 0.002, 3, 22, 31.18, 16.36, 0.89385, 21, 56.04, -51.13, 0.10514, 20, 46.78, -116.54, 0.00101, 2, 22, 73.18, 6.8, 0.98434, 21, 98.04, -60.69, 0.01566, 2, 22, 105.15, 25.39, 0.99971, 21, 130.01, -42.1, 0.00029, 3, 22, -49.07, 51.5, 0.13746, 21, -24.21, -15.99, 0.83026, 20, -33.47, -81.39, 0.03228, 2, 22, -44.25, 7.55, 0.60613, 21, -19.39, -59.94, 0.39387, 2, 22, -59.76, -17.47, 0.69306, 21, -34.9, -84.95, 0.30694, 5, 20, -20.08, 73.94, 0.0299, 18, 7.95, -1.34, 0.87638, 19, -66.88, 52.59, 0.01057, 17, -47.79, -16.34, 0.08298, 16, -29.79, -97.94, 0.00017], "hull": 60, "edges": [0, 118, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 116, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 140, 142, 142, 144, 144, 146, 148, 150, 150, 152, 56, 58, 70, 72, 92, 94, 94, 96, 24, 26, 26, 28, 14, 16, 16, 18], "width": 433, "height": 399}}, "kaishijm6": {"kaishijm04": {"type": "mesh", "uvs": [0.08749, 0.00703, 0.17673, 0.0621, 0.1873, 0.10128, 0.27086, 0.15226, 0.26482, 0.24916, 0.30675, 0.19123, 0.34729, 0.2245, 0.32894, 0.27663, 0.37079, 0.2722, 0.40975, 0.26808, 0.43129, 0.27732, 0.42307, 0.28732, 0.4079, 0.2971, 0.36947, 0.32364, 0.33099, 0.35021, 0.40601, 0.41866, 0.43897, 0.44856, 0.56621, 0.4949, 0.5856, 0.43918, 0.55157, 0.39863, 0.58675, 0.38656, 0.60053, 0.36476, 0.62831, 0.31022, 0.65003, 0.29558, 0.65493, 0.3101, 0.58645, 0.38873, 0.61365, 0.42346, 0.65032, 0.41789, 0.66846, 0.45659, 0.65441, 0.47846, 0.62676, 0.53579, 0.49524, 0.67625, 0.4725, 0.69009, 0.43246, 0.7697, 0.39489, 0.79711, 0.39439, 0.82273, 0.33451, 0.84939, 0.30601, 0.81348, 0.32425, 0.7793, 0.27866, 0.72012, 0.24394, 0.7397, 0.22912, 0.7139, 0.21689, 0.61148, 0.16517, 0.5737, 0.11329, 0.62149, 0.10403, 0.52406, 0.11778, 0.50684, 0.13519, 0.44831, 0.15851, 0.36994, 0.09351, 0.34392, 0.03911, 0.30073, 0.03578, 0.28086, 0.12085, 0.20668, 0.12294, 0.17662, 0.04352, 0.10575, 0.04388, 0.06865, 0.02624, 0.05226, 0.00197, 0.04085, 0.0022, 0.02729, 0.03529, 0.00439, 0.04959, 0.03843, 0.11009, 0.07659, 0.15933, 0.12543, 0.18888, 0.18495, 0.20435, 0.26431, 0.2339, 0.36046, 0.28032, 0.45203, 0.33097, 0.54513, 0.39991, 0.60159, 0.45197, 0.64738, 0.42805, 0.5146, 0.50402, 0.57107, 0.59547, 0.52986, 0.63064, 0.44592, 0.30683, 0.61547, 0.37436, 0.69788, 0.37858, 0.77114, 0.1252, 0.28028], "triangles": [54, 61, 53, 54, 55, 61, 55, 60, 61, 60, 0, 61, 61, 0, 1, 55, 56, 60, 60, 56, 58, 56, 57, 58, 58, 59, 60, 60, 59, 0, 52, 63, 64, 64, 63, 4, 4, 63, 3, 52, 53, 63, 53, 62, 63, 62, 2, 63, 63, 2, 3, 53, 61, 62, 62, 1, 2, 62, 61, 1, 65, 7, 14, 65, 4, 7, 14, 7, 13, 13, 8, 12, 13, 7, 8, 12, 9, 11, 12, 8, 9, 11, 9, 10, 4, 5, 7, 7, 5, 6, 49, 77, 48, 48, 77, 64, 49, 50, 77, 50, 51, 77, 51, 52, 77, 77, 52, 64, 44, 45, 43, 45, 46, 43, 46, 47, 43, 43, 47, 66, 42, 43, 66, 65, 47, 48, 47, 65, 66, 66, 14, 15, 66, 65, 14, 48, 64, 65, 65, 64, 4, 40, 41, 39, 75, 39, 74, 39, 41, 74, 41, 42, 74, 75, 74, 68, 74, 67, 68, 74, 42, 67, 67, 42, 66, 68, 67, 70, 67, 15, 70, 67, 66, 15, 70, 15, 16, 37, 38, 36, 35, 36, 34, 34, 36, 38, 76, 34, 38, 34, 76, 33, 76, 38, 75, 38, 39, 75, 76, 75, 33, 33, 75, 32, 75, 69, 32, 75, 68, 69, 32, 69, 31, 30, 31, 72, 31, 71, 72, 31, 69, 71, 69, 68, 71, 68, 70, 71, 71, 17, 72, 71, 70, 17, 30, 72, 29, 72, 17, 73, 72, 73, 29, 73, 18, 26, 18, 73, 17, 70, 16, 17, 29, 73, 28, 73, 27, 28, 73, 26, 27, 18, 25, 26, 18, 19, 25, 25, 19, 20, 25, 21, 24, 21, 22, 24, 25, 20, 21, 22, 23, 24], "vertices": [2, 10, 2.27, 82.5, 0.06654, 9, 20.27, 0.9, 0.93346, 2, 10, 21.14, 42.02, 0.56003, 9, 39.14, -39.58, 0.43997, 2, 10, 15.79, 26.55, 0.75035, 9, 33.8, -55.05, 0.24965, 3, 13, 61.31, 79.12, 0.00044, 12, 14.5, 57.76, 0.05188, 10, 33.6, -11.16, 0.94768, 4, 13, 36.67, 48.95, 0.06963, 11, 64.7, -26.32, 0.05825, 12, -10.13, 27.6, 0.54498, 10, 8.96, -41.33, 0.32714, 3, 11, 93.02, -17.94, 0.00035, 12, 18.19, 35.98, 0.9731, 10, 37.28, -32.94, 0.02655, 2, 12, 24.84, 14.9, 0.99361, 10, 43.93, -54.03, 0.00639, 2, 12, 6.23, 2.47, 0.99803, 10, 25.33, -66.45, 0.00197, 1, 12, 22.1, -6.62, 1, 2, 13, 83.68, 6.27, 0, 12, 36.88, -15.08, 1, 2, 13, 89.18, -2.17, 0, 12, 42.37, -23.53, 1, 2, 13, 83.94, -3.37, 0, 12, 37.14, -24.73, 1, 1, 12, 29.49, -24.11, 1, 4, 15, 40.9, 131.14, 1e-05, 14, 65.76, 63.66, 0.00293, 13, 56.5, -1.75, 0.02956, 12, 9.7, -23.1, 0.96751, 6, 15, 21.08, 132.15, 0.00287, 14, 45.94, 64.66, 0.06263, 13, 36.68, -0.75, 0.38267, 11, 64.71, -76.02, 0.00059, 12, -10.12, -22.1, 0.55086, 10, 8.97, -91.03, 0.00038, 4, 15, 31.8, 90.87, 0.13553, 14, 56.66, 23.39, 0.49623, 13, 47.4, -42.02, 0.32116, 12, 0.6, -63.37, 0.04708, 4, 15, 36.55, 72.79, 0.33324, 14, 61.41, 5.31, 0.50919, 13, 52.15, -60.1, 0.14571, 12, 5.35, -81.45, 0.01187, 3, 15, 70.92, 25.6, 0.95205, 14, 95.78, -41.88, 0.04718, 13, 86.52, -107.29, 0.00077, 2, 15, 90.73, 38.94, 0.99816, 14, 115.59, -28.55, 0.00184, 1, 15, 88.08, 60.77, 1, 2, 15, 103.36, 55.85, 1, 14, 128.22, -11.63, 0, 2, 15, 113.31, 59.51, 1, 14, 138.17, -7.98, 0, 1, 15, 135.82, 70.35, 1, 1, 15, 146.93, 69.66, 1, 1, 15, 145.29, 63.68, 1, 1, 15, 102.75, 55.22, 1, 2, 15, 104.33, 37.01, 0.99977, 14, 129.19, -30.47, 0.00023, 1, 15, 118.63, 29.6, 1, 1, 15, 116.08, 12.38, 1, 2, 15, 106.02, 8.77, 0.99983, 14, 130.88, -58.72, 0.00017, 2, 15, 82.91, -3.02, 0.99371, 14, 107.77, -70.5, 0.00629, 1, 15, 3.65, -15.82, 1, 2, 15, -7.63, -14.62, 0.96489, 14, 17.23, -82.11, 0.03511, 2, 15, -40.31, -30.57, 0.77773, 14, -15.45, -98.05, 0.22227, 2, 15, -60, -30.07, 0.7112, 14, -35.14, -97.56, 0.2888, 2, 15, -66.12, -38.32, 0.70299, 14, -41.26, -105.81, 0.29701, 2, 15, -93.55, -31.96, 0.68632, 14, -68.69, -99.44, 0.31368, 2, 15, -95.33, -13.04, 0.67331, 14, -70.47, -80.52, 0.32669, 2, 15, -80.92, -6.46, 0.62487, 14, -56.06, -73.94, 0.37513, 3, 15, -83.35, 24.38, 0.30384, 14, -58.49, -43.11, 0.68839, 13, -67.75, -108.52, 0.00777, 3, 15, -100.22, 26.72, 0.22366, 14, -75.35, -40.77, 0.76336, 13, -84.62, -106.18, 0.01298, 3, 15, -99.48, 38.88, 0.20813, 14, -74.62, -28.6, 0.77241, 13, -83.88, -94.01, 0.01946, 3, 15, -80.04, 75.46, 0.03934, 14, -55.18, 7.97, 0.70941, 13, -64.44, -57.44, 0.25125, 4, 15, -89.61, 100.83, 0.0006, 14, -64.75, 33.34, 0.42903, 13, -74.02, -32.07, 0.56874, 11, -45.98, -107.34, 0.00163, 2, 14, -94.25, 30.78, 0.37502, 13, -103.52, -34.63, 0.62498, 3, 14, -74.92, 64.97, 0.33985, 13, -84.18, -0.44, 0.6529, 11, -56.15, -75.71, 0.00725, 3, 14, -66.04, 67.14, 0.32065, 13, -75.3, 1.73, 0.66674, 11, -47.27, -73.54, 0.01261, 3, 14, -46.28, 81.89, 0.19663, 13, -55.54, 16.48, 0.74277, 11, -27.51, -58.79, 0.0606, 5, 14, -19.81, 101.64, 0.02116, 13, -29.08, 36.24, 0.54206, 11, -1.04, -39.04, 0.39107, 12, -75.88, 14.88, 0.02472, 10, -56.79, -54.04, 0.02098, 3, 13, -46.09, 61.11, 0.06407, 11, -18.06, -14.16, 0.93424, 12, -92.9, 39.76, 0.00169, 1, 11, -27.33, 13.66, 1, 1, 11, -23.9, 21, 1, 5, 13, -4.53, 99.1, 0.00086, 11, 23.5, 23.83, 0.44295, 12, -51.34, 77.75, 0.00225, 10, -32.24, 8.82, 0.5072, 9, -14.24, -72.78, 0.04673, 3, 11, 31.22, 33.13, 0.14076, 10, -24.53, 18.12, 0.67459, 9, -6.52, -63.48, 0.18464, 3, 11, 19.5, 76.3, 0.00012, 10, -36.24, 61.29, 0.20448, 9, -18.24, -20.31, 0.7954, 2, 10, -27.5, 73.34, 0.0751, 9, -9.5, -8.27, 0.9249, 2, 10, -29.95, 83.14, 0.00545, 9, -11.95, 1.53, 0.99455, 1, 9, -17.91, 11.38, 1, 1, 9, -14.68, 15.75, 1, 1, 9, 2.37, 14.91, 1, 1, 9, -0.46, 0.17, 1, 3, 11, 49.88, 69.07, 0.00026, 10, -5.86, 54.07, 0.33501, 9, 12.14, -27.54, 0.66473, 3, 11, 56.01, 40.7, 0.00866, 10, 0.27, 25.7, 0.73227, 9, 18.27, -55.9, 0.25907, 5, 13, 24.64, 89.08, 0.003, 11, 52.67, 13.8, 0.03059, 12, -22.16, 67.72, 0.0067, 10, -3.07, -1.2, 0.95673, 9, 14.93, -82.81, 0.00298, 4, 13, 11.7, 59.23, 0.14508, 11, 39.74, -16.05, 0.26991, 12, -35.1, 37.88, 0.17544, 10, -16.01, -31.05, 0.40957, 4, 13, -0.14, 20.35, 0.70643, 11, 27.89, -54.92, 0.12586, 12, -46.94, -1, 0.12643, 10, -27.85, -69.93, 0.04128, 4, 15, -20.53, 111.61, 0.00018, 14, 4.33, 44.13, 0.29605, 13, -4.93, -21.28, 0.69997, 12, -51.73, -42.63, 0.00381, 3, 14, 0.69, 0.93, 0.99267, 13, -8.58, -64.48, 0.00727, 12, -55.38, -85.83, 6e-05, 2, 15, -12.83, 32.6, 0.49976, 14, 12.03, -34.89, 0.50024, 2, 15, -5, 4.52, 0.93754, 14, 19.86, -62.97, 0.06246, 4, 15, 17.34, 53.95, 0.4235, 14, 42.2, -13.53, 0.52083, 13, 32.94, -78.94, 0.05367, 12, -13.86, -100.29, 0.002, 3, 15, 31.18, 16.36, 0.89385, 14, 56.04, -51.13, 0.10514, 13, 46.78, -116.54, 0.00101, 2, 15, 73.18, 6.8, 0.98434, 14, 98.04, -60.69, 0.01566, 2, 15, 105.15, 25.39, 0.99971, 14, 130.01, -42.1, 0.00029, 3, 15, -49.07, 51.5, 0.13746, 14, -24.21, -15.99, 0.83026, 13, -33.47, -81.39, 0.03228, 2, 15, -44.25, 7.55, 0.60613, 14, -19.39, -59.94, 0.39387, 2, 15, -59.76, -17.47, 0.69306, 14, -34.9, -84.95, 0.30694, 5, 13, -20.08, 73.94, 0.0299, 11, 7.95, -1.34, 0.87638, 12, -66.88, 52.59, 0.01057, 10, -47.79, -16.34, 0.08298, 9, -29.79, -97.94, 0.00017], "hull": 60, "edges": [0, 118, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 116, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 140, 142, 142, 144, 144, 146, 148, 150, 150, 152, 56, 58, 70, 72, 92, 94, 94, 96, 24, 26, 26, 28, 14, 16, 16, 18], "width": 433, "height": 399}}, "kaishijm7": {"kaishijm4": {"type": "mesh", "path": "kaishijm04", "uvs": [0.08749, 0.00703, 0.17673, 0.0621, 0.1873, 0.10128, 0.27086, 0.15226, 0.26482, 0.24916, 0.30675, 0.19123, 0.34729, 0.2245, 0.32894, 0.27663, 0.37079, 0.2722, 0.40975, 0.26808, 0.43129, 0.27732, 0.42307, 0.28732, 0.4079, 0.2971, 0.36947, 0.32364, 0.33099, 0.35021, 0.40601, 0.41866, 0.43897, 0.44856, 0.56621, 0.4949, 0.5856, 0.43918, 0.55157, 0.39863, 0.58675, 0.38656, 0.60053, 0.36476, 0.62831, 0.31022, 0.65003, 0.29558, 0.65493, 0.3101, 0.58645, 0.38873, 0.61365, 0.42346, 0.65032, 0.41789, 0.66846, 0.45659, 0.65441, 0.47846, 0.62676, 0.53579, 0.49524, 0.67625, 0.4725, 0.69009, 0.43246, 0.7697, 0.39489, 0.79711, 0.39439, 0.82273, 0.33451, 0.84939, 0.30601, 0.81348, 0.32425, 0.7793, 0.27866, 0.72012, 0.24394, 0.7397, 0.22912, 0.7139, 0.21689, 0.61148, 0.16517, 0.5737, 0.11329, 0.62149, 0.10403, 0.52406, 0.11778, 0.50684, 0.13519, 0.44831, 0.15851, 0.36994, 0.09351, 0.34392, 0.03911, 0.30073, 0.03578, 0.28086, 0.12085, 0.20668, 0.12294, 0.17662, 0.04352, 0.10575, 0.04388, 0.06865, 0.02624, 0.05226, 0.00197, 0.04085, 0.0022, 0.02729, 0.03529, 0.00439, 0.04959, 0.03843, 0.11009, 0.07659, 0.15933, 0.12543, 0.18888, 0.18495, 0.20435, 0.26431, 0.2339, 0.36046, 0.28032, 0.45203, 0.33097, 0.54513, 0.39991, 0.60159, 0.45197, 0.64738, 0.42805, 0.5146, 0.50402, 0.57107, 0.59547, 0.52986, 0.63064, 0.44592, 0.30683, 0.61547, 0.37436, 0.69788, 0.37858, 0.77114, 0.1252, 0.28028], "triangles": [54, 61, 53, 54, 55, 61, 55, 60, 61, 60, 0, 61, 61, 0, 1, 55, 56, 60, 60, 56, 58, 56, 57, 58, 58, 59, 60, 60, 59, 0, 52, 63, 64, 64, 63, 4, 4, 63, 3, 52, 53, 63, 53, 62, 63, 62, 2, 63, 63, 2, 3, 53, 61, 62, 62, 1, 2, 62, 61, 1, 65, 7, 14, 65, 4, 7, 14, 7, 13, 13, 8, 12, 13, 7, 8, 12, 9, 11, 12, 8, 9, 11, 9, 10, 4, 5, 7, 7, 5, 6, 49, 77, 48, 48, 77, 64, 49, 50, 77, 50, 51, 77, 51, 52, 77, 77, 52, 64, 44, 45, 43, 45, 46, 43, 46, 47, 43, 43, 47, 66, 42, 43, 66, 65, 47, 48, 47, 65, 66, 66, 14, 15, 66, 65, 14, 48, 64, 65, 65, 64, 4, 40, 41, 39, 75, 39, 74, 39, 41, 74, 41, 42, 74, 75, 74, 68, 74, 67, 68, 74, 42, 67, 67, 42, 66, 68, 67, 70, 67, 15, 70, 67, 66, 15, 70, 15, 16, 37, 38, 36, 35, 36, 34, 34, 36, 38, 76, 34, 38, 34, 76, 33, 76, 38, 75, 38, 39, 75, 76, 75, 33, 33, 75, 32, 75, 69, 32, 75, 68, 69, 32, 69, 31, 30, 31, 72, 31, 71, 72, 31, 69, 71, 69, 68, 71, 68, 70, 71, 71, 17, 72, 71, 70, 17, 30, 72, 29, 72, 17, 73, 72, 73, 29, 73, 18, 26, 18, 73, 17, 70, 16, 17, 29, 73, 28, 73, 27, 28, 73, 26, 27, 18, 25, 26, 18, 19, 25, 25, 19, 20, 25, 21, 24, 21, 22, 24, 25, 20, 21, 22, 23, 24], "vertices": [2, 30, 2.27, 82.5, 0.06654, 29, 20.27, 0.9, 0.93346, 2, 30, 21.14, 42.02, 0.56003, 29, 39.14, -39.58, 0.43997, 2, 30, 15.79, 26.55, 0.75035, 29, 33.8, -55.05, 0.24965, 3, 33, 61.31, 79.12, 0.00044, 32, 14.5, 57.76, 0.05188, 30, 33.6, -11.16, 0.94768, 4, 33, 36.67, 48.95, 0.06963, 31, 64.7, -26.32, 0.05825, 32, -10.13, 27.6, 0.54498, 30, 8.96, -41.33, 0.32714, 3, 31, 93.02, -17.94, 0.00035, 32, 18.19, 35.98, 0.9731, 30, 37.28, -32.94, 0.02655, 2, 32, 24.84, 14.9, 0.99361, 30, 43.93, -54.03, 0.00639, 2, 32, 6.23, 2.47, 0.99803, 30, 25.33, -66.45, 0.00197, 1, 32, 22.1, -6.62, 1, 2, 33, 83.68, 6.27, 0, 32, 36.88, -15.08, 1, 2, 33, 89.18, -2.17, 0, 32, 42.37, -23.53, 1, 2, 33, 83.94, -3.37, 0, 32, 37.14, -24.73, 1, 1, 32, 29.49, -24.11, 1, 4, 35, 40.9, 131.14, 1e-05, 34, 65.76, 63.66, 0.00293, 33, 56.5, -1.75, 0.02956, 32, 9.7, -23.1, 0.96751, 6, 35, 21.08, 132.15, 0.00287, 34, 45.94, 64.66, 0.06263, 33, 36.68, -0.75, 0.38267, 31, 64.71, -76.02, 0.00059, 32, -10.12, -22.1, 0.55086, 30, 8.97, -91.03, 0.00038, 4, 35, 31.8, 90.87, 0.13553, 34, 56.66, 23.39, 0.49623, 33, 47.4, -42.02, 0.32116, 32, 0.6, -63.37, 0.04708, 4, 35, 36.55, 72.79, 0.33324, 34, 61.41, 5.31, 0.50919, 33, 52.15, -60.1, 0.14571, 32, 5.35, -81.45, 0.01187, 3, 35, 70.92, 25.6, 0.95205, 34, 95.78, -41.88, 0.04718, 33, 86.52, -107.29, 0.00077, 2, 35, 90.73, 38.94, 0.99816, 34, 115.59, -28.55, 0.00184, 1, 35, 88.08, 60.77, 1, 2, 35, 103.36, 55.85, 1, 34, 128.22, -11.63, 0, 2, 35, 113.31, 59.51, 1, 34, 138.17, -7.98, 0, 1, 35, 135.82, 70.35, 1, 1, 35, 146.93, 69.66, 1, 1, 35, 145.29, 63.68, 1, 1, 35, 102.75, 55.22, 1, 2, 35, 104.33, 37.01, 0.99977, 34, 129.19, -30.47, 0.00023, 1, 35, 118.63, 29.6, 1, 1, 35, 116.08, 12.38, 1, 2, 35, 106.02, 8.77, 0.99983, 34, 130.88, -58.72, 0.00017, 2, 35, 82.91, -3.02, 0.99371, 34, 107.77, -70.5, 0.00629, 1, 35, 3.65, -15.82, 1, 2, 35, -7.63, -14.62, 0.96489, 34, 17.23, -82.11, 0.03511, 2, 35, -40.31, -30.57, 0.77773, 34, -15.45, -98.05, 0.22227, 2, 35, -60, -30.07, 0.7112, 34, -35.14, -97.56, 0.2888, 2, 35, -66.12, -38.32, 0.70299, 34, -41.26, -105.81, 0.29701, 2, 35, -93.55, -31.96, 0.68632, 34, -68.69, -99.44, 0.31368, 2, 35, -95.33, -13.04, 0.67331, 34, -70.47, -80.52, 0.32669, 2, 35, -80.92, -6.46, 0.62487, 34, -56.06, -73.94, 0.37513, 3, 35, -83.35, 24.38, 0.30384, 34, -58.49, -43.11, 0.68839, 33, -67.75, -108.52, 0.00777, 3, 35, -100.22, 26.72, 0.22366, 34, -75.35, -40.77, 0.76336, 33, -84.62, -106.18, 0.01298, 3, 35, -99.48, 38.88, 0.20813, 34, -74.62, -28.6, 0.77241, 33, -83.88, -94.01, 0.01946, 3, 35, -80.04, 75.46, 0.03934, 34, -55.18, 7.97, 0.70941, 33, -64.44, -57.44, 0.25125, 4, 35, -89.61, 100.83, 0.0006, 34, -64.75, 33.34, 0.42903, 33, -74.02, -32.07, 0.56874, 31, -45.98, -107.34, 0.00163, 2, 34, -94.25, 30.78, 0.37502, 33, -103.52, -34.63, 0.62498, 3, 34, -74.92, 64.97, 0.33985, 33, -84.18, -0.44, 0.6529, 31, -56.15, -75.71, 0.00725, 3, 34, -66.04, 67.14, 0.32065, 33, -75.3, 1.73, 0.66674, 31, -47.27, -73.54, 0.01261, 3, 34, -46.28, 81.89, 0.19663, 33, -55.54, 16.48, 0.74277, 31, -27.51, -58.79, 0.0606, 5, 34, -19.81, 101.64, 0.02116, 33, -29.08, 36.24, 0.54206, 31, -1.04, -39.04, 0.39107, 32, -75.88, 14.88, 0.02472, 30, -56.79, -54.04, 0.02098, 3, 33, -46.09, 61.11, 0.06407, 31, -18.06, -14.16, 0.93424, 32, -92.9, 39.76, 0.00169, 1, 31, -27.33, 13.66, 1, 1, 31, -23.9, 21, 1, 5, 33, -4.53, 99.1, 0.00086, 31, 23.5, 23.83, 0.44295, 32, -51.34, 77.75, 0.00225, 30, -32.24, 8.82, 0.5072, 29, -14.24, -72.78, 0.04673, 3, 31, 31.22, 33.13, 0.14076, 30, -24.53, 18.12, 0.67459, 29, -6.52, -63.48, 0.18464, 3, 31, 19.5, 76.3, 0.00012, 30, -36.24, 61.29, 0.20448, 29, -18.24, -20.31, 0.7954, 2, 30, -27.5, 73.34, 0.0751, 29, -9.5, -8.27, 0.9249, 2, 30, -29.95, 83.14, 0.00545, 29, -11.95, 1.53, 0.99455, 1, 29, -17.91, 11.38, 1, 1, 29, -14.68, 15.75, 1, 1, 29, 2.37, 14.91, 1, 1, 29, -0.46, 0.17, 1, 3, 31, 49.88, 69.07, 0.00026, 30, -5.86, 54.07, 0.33501, 29, 12.14, -27.54, 0.66473, 3, 31, 56.01, 40.7, 0.00866, 30, 0.27, 25.7, 0.73227, 29, 18.27, -55.9, 0.25907, 5, 33, 24.64, 89.08, 0.003, 31, 52.67, 13.8, 0.03059, 32, -22.16, 67.72, 0.0067, 30, -3.07, -1.2, 0.95673, 29, 14.93, -82.81, 0.00298, 4, 33, 11.7, 59.23, 0.14508, 31, 39.74, -16.05, 0.26991, 32, -35.1, 37.88, 0.17544, 30, -16.01, -31.05, 0.40957, 4, 33, -0.14, 20.35, 0.70643, 31, 27.89, -54.92, 0.12586, 32, -46.94, -1, 0.12643, 30, -27.85, -69.93, 0.04128, 4, 35, -20.53, 111.61, 0.00018, 34, 4.33, 44.13, 0.29605, 33, -4.93, -21.28, 0.69997, 32, -51.73, -42.63, 0.00381, 3, 34, 0.69, 0.93, 0.99267, 33, -8.58, -64.48, 0.00727, 32, -55.38, -85.83, 6e-05, 2, 35, -12.83, 32.6, 0.49976, 34, 12.03, -34.89, 0.50024, 2, 35, -5, 4.52, 0.93754, 34, 19.86, -62.97, 0.06246, 4, 35, 17.34, 53.95, 0.4235, 34, 42.2, -13.53, 0.52083, 33, 32.94, -78.94, 0.05367, 32, -13.86, -100.29, 0.002, 3, 35, 31.18, 16.36, 0.89385, 34, 56.04, -51.13, 0.10514, 33, 46.78, -116.54, 0.00101, 2, 35, 73.18, 6.8, 0.98434, 34, 98.04, -60.69, 0.01566, 2, 35, 105.15, 25.39, 0.99971, 34, 130.01, -42.1, 0.00029, 3, 35, -49.07, 51.5, 0.13746, 34, -24.21, -15.99, 0.83026, 33, -33.47, -81.39, 0.03228, 2, 35, -44.25, 7.55, 0.60613, 34, -19.39, -59.94, 0.39387, 2, 35, -59.76, -17.47, 0.69306, 34, -34.9, -84.95, 0.30694, 5, 33, -20.08, 73.94, 0.0299, 31, 7.95, -1.34, 0.87638, 32, -66.88, 52.59, 0.01057, 30, -47.79, -16.34, 0.08298, 29, -29.79, -97.94, 0.00017], "hull": 60, "edges": [0, 118, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 116, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 140, 142, 142, 144, 144, 146, 148, 150, 150, 152, 56, 58, 70, 72, 92, 94, 94, 96, 24, 26, 26, 28, 14, 16, 16, 18], "width": 433, "height": 399}}, "kaishijm8": {"kaishijm04": {"type": "mesh", "uvs": [0.08749, 0.00703, 0.17673, 0.0621, 0.1873, 0.10128, 0.27086, 0.15226, 0.26482, 0.24916, 0.30675, 0.19123, 0.34729, 0.2245, 0.32894, 0.27663, 0.37079, 0.2722, 0.40975, 0.26808, 0.43129, 0.27732, 0.42307, 0.28732, 0.4079, 0.2971, 0.36947, 0.32364, 0.33099, 0.35021, 0.40601, 0.41866, 0.43897, 0.44856, 0.56621, 0.4949, 0.5856, 0.43918, 0.55157, 0.39863, 0.58675, 0.38656, 0.60053, 0.36476, 0.62831, 0.31022, 0.65003, 0.29558, 0.65493, 0.3101, 0.58645, 0.38873, 0.61365, 0.42346, 0.65032, 0.41789, 0.66846, 0.45659, 0.65441, 0.47846, 0.62676, 0.53579, 0.49524, 0.67625, 0.4725, 0.69009, 0.43246, 0.7697, 0.39489, 0.79711, 0.39439, 0.82273, 0.33451, 0.84939, 0.30601, 0.81348, 0.32425, 0.7793, 0.27866, 0.72012, 0.24394, 0.7397, 0.22912, 0.7139, 0.21689, 0.61148, 0.16517, 0.5737, 0.11329, 0.62149, 0.10403, 0.52406, 0.11778, 0.50684, 0.13519, 0.44831, 0.15851, 0.36994, 0.09351, 0.34392, 0.03911, 0.30073, 0.03578, 0.28086, 0.12085, 0.20668, 0.12294, 0.17662, 0.04352, 0.10575, 0.04388, 0.06865, 0.02624, 0.05226, 0.00197, 0.04085, 0.0022, 0.02729, 0.03529, 0.00439, 0.04959, 0.03843, 0.11009, 0.07659, 0.15933, 0.12543, 0.18888, 0.18495, 0.20435, 0.26431, 0.2339, 0.36046, 0.28032, 0.45203, 0.33097, 0.54513, 0.39991, 0.60159, 0.45197, 0.64738, 0.42805, 0.5146, 0.50402, 0.57107, 0.59547, 0.52986, 0.63064, 0.44592, 0.30683, 0.61547, 0.37436, 0.69788, 0.37858, 0.77114, 0.1252, 0.28028], "triangles": [54, 61, 53, 54, 55, 61, 55, 60, 61, 60, 0, 61, 61, 0, 1, 55, 56, 60, 60, 56, 58, 56, 57, 58, 58, 59, 60, 60, 59, 0, 52, 63, 64, 64, 63, 4, 4, 63, 3, 52, 53, 63, 53, 62, 63, 62, 2, 63, 63, 2, 3, 53, 61, 62, 62, 1, 2, 62, 61, 1, 65, 7, 14, 65, 4, 7, 14, 7, 13, 13, 8, 12, 13, 7, 8, 12, 9, 11, 12, 8, 9, 11, 9, 10, 4, 5, 7, 7, 5, 6, 49, 77, 48, 48, 77, 64, 49, 50, 77, 50, 51, 77, 51, 52, 77, 77, 52, 64, 44, 45, 43, 45, 46, 43, 46, 47, 43, 43, 47, 66, 42, 43, 66, 65, 47, 48, 47, 65, 66, 66, 14, 15, 66, 65, 14, 48, 64, 65, 65, 64, 4, 40, 41, 39, 75, 39, 74, 39, 41, 74, 41, 42, 74, 75, 74, 68, 74, 67, 68, 74, 42, 67, 67, 42, 66, 68, 67, 70, 67, 15, 70, 67, 66, 15, 70, 15, 16, 37, 38, 36, 35, 36, 34, 34, 36, 38, 76, 34, 38, 34, 76, 33, 76, 38, 75, 38, 39, 75, 76, 75, 33, 33, 75, 32, 75, 69, 32, 75, 68, 69, 32, 69, 31, 30, 31, 72, 31, 71, 72, 31, 69, 71, 69, 68, 71, 68, 70, 71, 71, 17, 72, 71, 70, 17, 30, 72, 29, 72, 17, 73, 72, 73, 29, 73, 18, 26, 18, 73, 17, 70, 16, 17, 29, 73, 28, 73, 27, 28, 73, 26, 27, 18, 25, 26, 18, 19, 25, 25, 19, 20, 25, 21, 24, 21, 22, 24, 25, 20, 21, 22, 23, 24], "vertices": [2, 30, 2.27, 82.5, 0.06654, 29, 20.27, 0.9, 0.93346, 2, 30, 21.14, 42.02, 0.56003, 29, 39.14, -39.58, 0.43997, 2, 30, 15.79, 26.55, 0.75035, 29, 33.8, -55.05, 0.24965, 3, 33, 61.31, 79.12, 0.00044, 32, 14.5, 57.76, 0.05188, 30, 33.6, -11.16, 0.94768, 4, 33, 36.67, 48.95, 0.06963, 31, 64.7, -26.32, 0.05825, 32, -10.13, 27.6, 0.54498, 30, 8.96, -41.33, 0.32714, 3, 31, 93.02, -17.94, 0.00035, 32, 18.19, 35.98, 0.9731, 30, 37.28, -32.94, 0.02655, 2, 32, 24.84, 14.9, 0.99361, 30, 43.93, -54.03, 0.00639, 2, 32, 6.23, 2.47, 0.99803, 30, 25.33, -66.45, 0.00197, 1, 32, 22.1, -6.62, 1, 2, 33, 83.68, 6.27, 0, 32, 36.88, -15.08, 1, 2, 33, 89.18, -2.17, 0, 32, 42.37, -23.53, 1, 2, 33, 83.94, -3.37, 0, 32, 37.14, -24.73, 1, 1, 32, 29.49, -24.11, 1, 4, 35, 40.9, 131.14, 1e-05, 34, 65.76, 63.66, 0.00293, 33, 56.5, -1.75, 0.02956, 32, 9.7, -23.1, 0.96751, 6, 35, 21.08, 132.15, 0.00287, 34, 45.94, 64.66, 0.06263, 33, 36.68, -0.75, 0.38267, 31, 64.71, -76.02, 0.00059, 32, -10.12, -22.1, 0.55086, 30, 8.97, -91.03, 0.00038, 4, 35, 31.8, 90.87, 0.13553, 34, 56.66, 23.39, 0.49623, 33, 47.4, -42.02, 0.32116, 32, 0.6, -63.37, 0.04708, 4, 35, 36.55, 72.79, 0.33324, 34, 61.41, 5.31, 0.50919, 33, 52.15, -60.1, 0.14571, 32, 5.35, -81.45, 0.01187, 3, 35, 70.92, 25.6, 0.95205, 34, 95.78, -41.88, 0.04718, 33, 86.52, -107.29, 0.00077, 2, 35, 90.73, 38.94, 0.99816, 34, 115.59, -28.55, 0.00184, 1, 35, 88.08, 60.77, 1, 2, 35, 103.36, 55.85, 1, 34, 128.22, -11.63, 0, 2, 35, 113.31, 59.51, 1, 34, 138.17, -7.98, 0, 1, 35, 135.82, 70.35, 1, 1, 35, 146.93, 69.66, 1, 1, 35, 145.29, 63.68, 1, 1, 35, 102.75, 55.22, 1, 2, 35, 104.33, 37.01, 0.99977, 34, 129.19, -30.47, 0.00023, 1, 35, 118.63, 29.6, 1, 1, 35, 116.08, 12.38, 1, 2, 35, 106.02, 8.77, 0.99983, 34, 130.88, -58.72, 0.00017, 2, 35, 82.91, -3.02, 0.99371, 34, 107.77, -70.5, 0.00629, 1, 35, 3.65, -15.82, 1, 2, 35, -7.63, -14.62, 0.96489, 34, 17.23, -82.11, 0.03511, 2, 35, -40.31, -30.57, 0.77773, 34, -15.45, -98.05, 0.22227, 2, 35, -60, -30.07, 0.7112, 34, -35.14, -97.56, 0.2888, 2, 35, -66.12, -38.32, 0.70299, 34, -41.26, -105.81, 0.29701, 2, 35, -93.55, -31.96, 0.68632, 34, -68.69, -99.44, 0.31368, 2, 35, -95.33, -13.04, 0.67331, 34, -70.47, -80.52, 0.32669, 2, 35, -80.92, -6.46, 0.62487, 34, -56.06, -73.94, 0.37513, 3, 35, -83.35, 24.38, 0.30384, 34, -58.49, -43.11, 0.68839, 33, -67.75, -108.52, 0.00777, 3, 35, -100.22, 26.72, 0.22366, 34, -75.35, -40.77, 0.76336, 33, -84.62, -106.18, 0.01298, 3, 35, -99.48, 38.88, 0.20813, 34, -74.62, -28.6, 0.77241, 33, -83.88, -94.01, 0.01946, 3, 35, -80.04, 75.46, 0.03934, 34, -55.18, 7.97, 0.70941, 33, -64.44, -57.44, 0.25125, 4, 35, -89.61, 100.83, 0.0006, 34, -64.75, 33.34, 0.42903, 33, -74.02, -32.07, 0.56874, 31, -45.98, -107.34, 0.00163, 2, 34, -94.25, 30.78, 0.37502, 33, -103.52, -34.63, 0.62498, 3, 34, -74.92, 64.97, 0.33985, 33, -84.18, -0.44, 0.6529, 31, -56.15, -75.71, 0.00725, 3, 34, -66.04, 67.14, 0.32065, 33, -75.3, 1.73, 0.66674, 31, -47.27, -73.54, 0.01261, 3, 34, -46.28, 81.89, 0.19663, 33, -55.54, 16.48, 0.74277, 31, -27.51, -58.79, 0.0606, 5, 34, -19.81, 101.64, 0.02116, 33, -29.08, 36.24, 0.54206, 31, -1.04, -39.04, 0.39107, 32, -75.88, 14.88, 0.02472, 30, -56.79, -54.04, 0.02098, 3, 33, -46.09, 61.11, 0.06407, 31, -18.06, -14.16, 0.93424, 32, -92.9, 39.76, 0.00169, 1, 31, -27.33, 13.66, 1, 1, 31, -23.9, 21, 1, 5, 33, -4.53, 99.1, 0.00086, 31, 23.5, 23.83, 0.44295, 32, -51.34, 77.75, 0.00225, 30, -32.24, 8.82, 0.5072, 29, -14.24, -72.78, 0.04673, 3, 31, 31.22, 33.13, 0.14076, 30, -24.53, 18.12, 0.67459, 29, -6.52, -63.48, 0.18464, 3, 31, 19.5, 76.3, 0.00012, 30, -36.24, 61.29, 0.20448, 29, -18.24, -20.31, 0.7954, 2, 30, -27.5, 73.34, 0.0751, 29, -9.5, -8.27, 0.9249, 2, 30, -29.95, 83.14, 0.00545, 29, -11.95, 1.53, 0.99455, 1, 29, -17.91, 11.38, 1, 1, 29, -14.68, 15.75, 1, 1, 29, 2.37, 14.91, 1, 1, 29, -0.46, 0.17, 1, 3, 31, 49.88, 69.07, 0.00026, 30, -5.86, 54.07, 0.33501, 29, 12.14, -27.54, 0.66473, 3, 31, 56.01, 40.7, 0.00866, 30, 0.27, 25.7, 0.73227, 29, 18.27, -55.9, 0.25907, 5, 33, 24.64, 89.08, 0.003, 31, 52.67, 13.8, 0.03059, 32, -22.16, 67.72, 0.0067, 30, -3.07, -1.2, 0.95673, 29, 14.93, -82.81, 0.00298, 4, 33, 11.7, 59.23, 0.14508, 31, 39.74, -16.05, 0.26991, 32, -35.1, 37.88, 0.17544, 30, -16.01, -31.05, 0.40957, 4, 33, -0.14, 20.35, 0.70643, 31, 27.89, -54.92, 0.12586, 32, -46.94, -1, 0.12643, 30, -27.85, -69.93, 0.04128, 4, 35, -20.53, 111.61, 0.00018, 34, 4.33, 44.13, 0.29605, 33, -4.93, -21.28, 0.69997, 32, -51.73, -42.63, 0.00381, 3, 34, 0.69, 0.93, 0.99267, 33, -8.58, -64.48, 0.00727, 32, -55.38, -85.83, 6e-05, 2, 35, -12.83, 32.6, 0.49976, 34, 12.03, -34.89, 0.50024, 2, 35, -5, 4.52, 0.93754, 34, 19.86, -62.97, 0.06246, 4, 35, 17.34, 53.95, 0.4235, 34, 42.2, -13.53, 0.52083, 33, 32.94, -78.94, 0.05367, 32, -13.86, -100.29, 0.002, 3, 35, 31.18, 16.36, 0.89385, 34, 56.04, -51.13, 0.10514, 33, 46.78, -116.54, 0.00101, 2, 35, 73.18, 6.8, 0.98434, 34, 98.04, -60.69, 0.01566, 2, 35, 105.15, 25.39, 0.99971, 34, 130.01, -42.1, 0.00029, 3, 35, -49.07, 51.5, 0.13746, 34, -24.21, -15.99, 0.83026, 33, -33.47, -81.39, 0.03228, 2, 35, -44.25, 7.55, 0.60613, 34, -19.39, -59.94, 0.39387, 2, 35, -59.76, -17.47, 0.69306, 34, -34.9, -84.95, 0.30694, 5, 33, -20.08, 73.94, 0.0299, 31, 7.95, -1.34, 0.87638, 32, -66.88, 52.59, 0.01057, 30, -47.79, -16.34, 0.08298, 29, -29.79, -97.94, 0.00017], "hull": 60, "edges": [0, 118, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 116, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 140, 142, 142, 144, 144, 146, 148, 150, 150, 152, 56, 58, 70, 72, 92, 94, 94, 96, 24, 26, 26, 28, 14, 16, 16, 18], "width": 433, "height": 399}}, "kaishijm9": {"kaishijm04": {"type": "mesh", "uvs": [0.08749, 0.00703, 0.17673, 0.0621, 0.1873, 0.10128, 0.27086, 0.15226, 0.26482, 0.24916, 0.30675, 0.19123, 0.34729, 0.2245, 0.32894, 0.27663, 0.37079, 0.2722, 0.40975, 0.26808, 0.43129, 0.27732, 0.42307, 0.28732, 0.4079, 0.2971, 0.36947, 0.32364, 0.33099, 0.35021, 0.40601, 0.41866, 0.43897, 0.44856, 0.56621, 0.4949, 0.5856, 0.43918, 0.55157, 0.39863, 0.58675, 0.38656, 0.60053, 0.36476, 0.62831, 0.31022, 0.65003, 0.29558, 0.65493, 0.3101, 0.58645, 0.38873, 0.61365, 0.42346, 0.65032, 0.41789, 0.66846, 0.45659, 0.65441, 0.47846, 0.62676, 0.53579, 0.49524, 0.67625, 0.4725, 0.69009, 0.43246, 0.7697, 0.39489, 0.79711, 0.39439, 0.82273, 0.33451, 0.84939, 0.30601, 0.81348, 0.32425, 0.7793, 0.27866, 0.72012, 0.24394, 0.7397, 0.22912, 0.7139, 0.21689, 0.61148, 0.16517, 0.5737, 0.11329, 0.62149, 0.10403, 0.52406, 0.11778, 0.50684, 0.13519, 0.44831, 0.15851, 0.36994, 0.09351, 0.34392, 0.03911, 0.30073, 0.03578, 0.28086, 0.12085, 0.20668, 0.12294, 0.17662, 0.04352, 0.10575, 0.04388, 0.06865, 0.02624, 0.05226, 0.00197, 0.04085, 0.0022, 0.02729, 0.03529, 0.00439, 0.04959, 0.03843, 0.11009, 0.07659, 0.15933, 0.12543, 0.18888, 0.18495, 0.20435, 0.26431, 0.2339, 0.36046, 0.28032, 0.45203, 0.33097, 0.54513, 0.39991, 0.60159, 0.45197, 0.64738, 0.42805, 0.5146, 0.50402, 0.57107, 0.59547, 0.52986, 0.63064, 0.44592, 0.30683, 0.61547, 0.37436, 0.69788, 0.37858, 0.77114, 0.1252, 0.28028], "triangles": [54, 61, 53, 54, 55, 61, 55, 60, 61, 60, 0, 61, 61, 0, 1, 55, 56, 60, 60, 56, 58, 56, 57, 58, 58, 59, 60, 60, 59, 0, 52, 63, 64, 64, 63, 4, 4, 63, 3, 52, 53, 63, 53, 62, 63, 62, 2, 63, 63, 2, 3, 53, 61, 62, 62, 1, 2, 62, 61, 1, 65, 7, 14, 65, 4, 7, 14, 7, 13, 13, 8, 12, 13, 7, 8, 12, 9, 11, 12, 8, 9, 11, 9, 10, 4, 5, 7, 7, 5, 6, 49, 77, 48, 48, 77, 64, 49, 50, 77, 50, 51, 77, 51, 52, 77, 77, 52, 64, 44, 45, 43, 45, 46, 43, 46, 47, 43, 43, 47, 66, 42, 43, 66, 65, 47, 48, 47, 65, 66, 66, 14, 15, 66, 65, 14, 48, 64, 65, 65, 64, 4, 40, 41, 39, 75, 39, 74, 39, 41, 74, 41, 42, 74, 75, 74, 68, 74, 67, 68, 74, 42, 67, 67, 42, 66, 68, 67, 70, 67, 15, 70, 67, 66, 15, 70, 15, 16, 37, 38, 36, 35, 36, 34, 34, 36, 38, 76, 34, 38, 34, 76, 33, 76, 38, 75, 38, 39, 75, 76, 75, 33, 33, 75, 32, 75, 69, 32, 75, 68, 69, 32, 69, 31, 30, 31, 72, 31, 71, 72, 31, 69, 71, 69, 68, 71, 68, 70, 71, 71, 17, 72, 71, 70, 17, 30, 72, 29, 72, 17, 73, 72, 73, 29, 73, 18, 26, 18, 73, 17, 70, 16, 17, 29, 73, 28, 73, 27, 28, 73, 26, 27, 18, 25, 26, 18, 19, 25, 25, 19, 20, 25, 21, 24, 21, 22, 24, 25, 20, 21, 22, 23, 24], "vertices": [2, 43, 2.27, 82.5, 0.06654, 42, 20.27, 0.9, 0.93346, 2, 43, 21.14, 42.02, 0.56003, 42, 39.14, -39.58, 0.43997, 2, 43, 15.79, 26.55, 0.75035, 42, 33.8, -55.05, 0.24965, 3, 46, 61.31, 79.12, 0.00044, 45, 14.5, 57.76, 0.05188, 43, 33.6, -11.16, 0.94768, 4, 46, 36.67, 48.95, 0.06963, 44, 64.7, -26.32, 0.05825, 45, -10.13, 27.6, 0.54498, 43, 8.96, -41.33, 0.32714, 3, 44, 93.02, -17.94, 0.00035, 45, 18.19, 35.98, 0.9731, 43, 37.28, -32.94, 0.02655, 2, 45, 24.84, 14.9, 0.99361, 43, 43.93, -54.03, 0.00639, 2, 45, 6.23, 2.47, 0.99803, 43, 25.33, -66.45, 0.00197, 1, 45, 22.1, -6.62, 1, 2, 46, 83.68, 6.27, 0, 45, 36.88, -15.08, 1, 2, 46, 89.18, -2.17, 0, 45, 42.37, -23.53, 1, 2, 46, 83.94, -3.37, 0, 45, 37.14, -24.73, 1, 1, 45, 29.49, -24.11, 1, 4, 48, 40.9, 131.14, 1e-05, 47, 65.76, 63.66, 0.00293, 46, 56.5, -1.75, 0.02956, 45, 9.7, -23.1, 0.96751, 6, 48, 21.08, 132.15, 0.00287, 47, 45.94, 64.66, 0.06263, 46, 36.68, -0.75, 0.38267, 44, 64.71, -76.02, 0.00059, 45, -10.12, -22.1, 0.55086, 43, 8.97, -91.03, 0.00038, 4, 48, 31.8, 90.87, 0.13553, 47, 56.66, 23.39, 0.49623, 46, 47.4, -42.02, 0.32116, 45, 0.6, -63.37, 0.04708, 4, 48, 36.55, 72.79, 0.33324, 47, 61.41, 5.31, 0.50919, 46, 52.15, -60.1, 0.14571, 45, 5.35, -81.45, 0.01187, 3, 48, 70.92, 25.6, 0.95205, 47, 95.78, -41.88, 0.04718, 46, 86.52, -107.29, 0.00077, 2, 48, 90.73, 38.94, 0.99816, 47, 115.59, -28.55, 0.00184, 1, 48, 88.08, 60.77, 1, 2, 48, 103.36, 55.85, 1, 47, 128.22, -11.63, 0, 2, 48, 113.31, 59.51, 1, 47, 138.17, -7.98, 0, 1, 48, 135.82, 70.35, 1, 1, 48, 146.93, 69.66, 1, 1, 48, 145.29, 63.68, 1, 1, 48, 102.75, 55.22, 1, 2, 48, 104.33, 37.01, 0.99977, 47, 129.19, -30.47, 0.00023, 1, 48, 118.63, 29.6, 1, 1, 48, 116.08, 12.38, 1, 2, 48, 106.02, 8.77, 0.99983, 47, 130.88, -58.72, 0.00017, 2, 48, 82.91, -3.02, 0.99371, 47, 107.77, -70.5, 0.00629, 1, 48, 3.65, -15.82, 1, 2, 48, -7.63, -14.62, 0.96489, 47, 17.23, -82.11, 0.03511, 2, 48, -40.31, -30.57, 0.77773, 47, -15.45, -98.05, 0.22227, 2, 48, -60, -30.07, 0.7112, 47, -35.14, -97.56, 0.2888, 2, 48, -66.12, -38.32, 0.70299, 47, -41.26, -105.81, 0.29701, 2, 48, -93.55, -31.96, 0.68632, 47, -68.69, -99.44, 0.31368, 2, 48, -95.33, -13.04, 0.67331, 47, -70.47, -80.52, 0.32669, 2, 48, -80.92, -6.46, 0.62487, 47, -56.06, -73.94, 0.37513, 3, 48, -83.35, 24.38, 0.30384, 47, -58.49, -43.11, 0.68839, 46, -67.75, -108.52, 0.00777, 3, 48, -100.22, 26.72, 0.22366, 47, -75.35, -40.77, 0.76336, 46, -84.62, -106.18, 0.01298, 3, 48, -99.48, 38.88, 0.20813, 47, -74.62, -28.6, 0.77241, 46, -83.88, -94.01, 0.01946, 3, 48, -80.04, 75.46, 0.03934, 47, -55.18, 7.97, 0.70941, 46, -64.44, -57.44, 0.25125, 4, 48, -89.61, 100.83, 0.0006, 47, -64.75, 33.34, 0.42903, 46, -74.02, -32.07, 0.56874, 44, -45.98, -107.34, 0.00163, 2, 47, -94.25, 30.78, 0.37502, 46, -103.52, -34.63, 0.62498, 3, 47, -74.92, 64.97, 0.33985, 46, -84.18, -0.44, 0.6529, 44, -56.15, -75.71, 0.00725, 3, 47, -66.04, 67.14, 0.32065, 46, -75.3, 1.73, 0.66674, 44, -47.27, -73.54, 0.01261, 3, 47, -46.28, 81.89, 0.19663, 46, -55.54, 16.48, 0.74277, 44, -27.51, -58.79, 0.0606, 5, 47, -19.81, 101.64, 0.02116, 46, -29.08, 36.24, 0.54206, 44, -1.04, -39.04, 0.39107, 45, -75.88, 14.88, 0.02472, 43, -56.79, -54.04, 0.02098, 3, 46, -46.09, 61.11, 0.06407, 44, -18.06, -14.16, 0.93424, 45, -92.9, 39.76, 0.00169, 1, 44, -27.33, 13.66, 1, 1, 44, -23.9, 21, 1, 5, 46, -4.53, 99.1, 0.00086, 44, 23.5, 23.83, 0.44295, 45, -51.34, 77.75, 0.00225, 43, -32.24, 8.82, 0.5072, 42, -14.24, -72.78, 0.04673, 3, 44, 31.22, 33.13, 0.14076, 43, -24.53, 18.12, 0.67459, 42, -6.52, -63.48, 0.18464, 3, 44, 19.5, 76.3, 0.00012, 43, -36.24, 61.29, 0.20448, 42, -18.24, -20.31, 0.7954, 2, 43, -27.5, 73.34, 0.0751, 42, -9.5, -8.27, 0.9249, 2, 43, -29.95, 83.14, 0.00545, 42, -11.95, 1.53, 0.99455, 1, 42, -17.91, 11.38, 1, 1, 42, -14.68, 15.75, 1, 1, 42, 2.37, 14.91, 1, 1, 42, -0.46, 0.17, 1, 3, 44, 49.88, 69.07, 0.00026, 43, -5.86, 54.07, 0.33501, 42, 12.14, -27.54, 0.66473, 3, 44, 56.01, 40.7, 0.00866, 43, 0.27, 25.7, 0.73227, 42, 18.27, -55.9, 0.25907, 5, 46, 24.64, 89.08, 0.003, 44, 52.67, 13.8, 0.03059, 45, -22.16, 67.72, 0.0067, 43, -3.07, -1.2, 0.95673, 42, 14.93, -82.81, 0.00298, 4, 46, 11.7, 59.23, 0.14508, 44, 39.74, -16.05, 0.26991, 45, -35.1, 37.88, 0.17544, 43, -16.01, -31.05, 0.40957, 4, 46, -0.14, 20.35, 0.70643, 44, 27.89, -54.92, 0.12586, 45, -46.94, -1, 0.12643, 43, -27.85, -69.93, 0.04128, 4, 48, -20.53, 111.61, 0.00018, 47, 4.33, 44.13, 0.29605, 46, -4.93, -21.28, 0.69997, 45, -51.73, -42.63, 0.00381, 3, 47, 0.69, 0.93, 0.99267, 46, -8.58, -64.48, 0.00727, 45, -55.38, -85.83, 6e-05, 2, 48, -12.83, 32.6, 0.49976, 47, 12.03, -34.89, 0.50024, 2, 48, -5, 4.52, 0.93754, 47, 19.86, -62.97, 0.06246, 4, 48, 17.34, 53.95, 0.4235, 47, 42.2, -13.53, 0.52083, 46, 32.94, -78.94, 0.05367, 45, -13.86, -100.29, 0.002, 3, 48, 31.18, 16.36, 0.89385, 47, 56.04, -51.13, 0.10514, 46, 46.78, -116.54, 0.00101, 2, 48, 73.18, 6.8, 0.98434, 47, 98.04, -60.69, 0.01566, 2, 48, 105.15, 25.39, 0.99971, 47, 130.01, -42.1, 0.00029, 3, 48, -49.07, 51.5, 0.13746, 47, -24.21, -15.99, 0.83026, 46, -33.47, -81.39, 0.03228, 2, 48, -44.25, 7.55, 0.60613, 47, -19.39, -59.94, 0.39387, 2, 48, -59.76, -17.47, 0.69306, 47, -34.9, -84.95, 0.30694, 5, 46, -20.08, 73.94, 0.0299, 44, 7.95, -1.34, 0.87638, 45, -66.88, 52.59, 0.01057, 43, -47.79, -16.34, 0.08298, 42, -29.79, -97.94, 0.00017], "hull": 60, "edges": [0, 118, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 116, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 140, 142, 142, 144, 144, 146, 148, 150, 150, 152, 56, 58, 70, 72, 92, 94, 94, 96, 24, 26, 26, 28, 14, 16, 16, 18], "width": 433, "height": 399}}, "light": {"light": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2486.99, -149.67, 593.23, -1385.74, -190.15, 24.46, 589.48, 1349.66], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 377, "height": 369}}, "kaishijm09": {"kaishijm09": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-83.31, -136.05, -39.43, 193.04, 210.36, 159.73, 166.48, -169.35], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 199, "height": 151}}, "kaishijm034": {"kaishijm034": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [378, -752, -378, -752, -378, -266, 378, -266], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 378, "height": 243}}, "kaishijm20": {"kaishijm020": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-121.23, -246.98, -19.92, 290.56, 540.22, 184.99, 438.91, -352.55], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 273, "height": 285}}, "kaishijm27": {"kaishijm027": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-209.64, 72.8, 549.77, 390, 947.91, -563.2, 188.49, -880.39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 411, "height": 516}}, "kaishijm28": {"kaishijm028": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [521.64, -409.74, -210.31, -42.3, 261.67, 897.88, 993.62, 530.44], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 409, "height": 526}}, "kaishijm29": {"kaishijm029": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-16.41, -367.51, -184.58, 185.48, 256.48, 319.61, 424.64, -233.39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 289, "height": 230}}, "kaishijm011": {"kaishijm011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [208.45, -416.93, -155.41, -8.51, 237.33, 341.39, 601.2, -67.03], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 273, "height": 263}}, "kaishijm012": {"kaishijm012": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [123.94, -193.41, -71.22, 1.75, 65.96, 138.93, 261.12, -56.23], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 165, "height": 116}}, "kaishijm013": {"kaishijm013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [160.84, -82.66, -46.12, -78.52, -43.32, 61.45, 163.64, 57.32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 70}}, "kaishijm014": {"kaishijm014": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [138.61, -124.98, -99.33, -33.28, -16.62, 181.34, 221.32, 89.63], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 153, "height": 138}}, "kaishijm016": {"kaishijm016": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [110.51, -106.6, -70.01, -50.61, -23.5, 99.35, 157.02, 43.36], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 94, "height": 78}}, "kaishijm017": {"kaishijm017": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [376, -428, 123, -428, 123, 77, 376, 77], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 126, "height": 252}}, "kaishijm018": {"kaishijm018": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-12, -68, -304, -68, -304, 122, -12, 122], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 146, "height": 95}}, "kaishijm019": {"kaishijm019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [119.49, -176.71, -100.05, -61.36, 7.4, 143.13, 226.94, 27.78], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 124, "height": 115}}, "rootcut": {"rootcut": {"type": "clipping", "end": "rootcut", "vertexCount": 4, "vertices": [-379.31, 752.83, 378.79, 752.63, 377.08, -751.88, -379.37, -751.9], "color": "ce3a3aff"}}, "kaishijm023_1": {"kaishijm023_1": {"type": "mesh", "uvs": [0.12202, 0.91018, 0.32132, 0.95447, 0.53096, 0.96612, 0.78461, 0.9195, 0.86484, 0.7773, 0.89849, 0.60247, 1, 0.47192, 0.97355, 0.30175, 0.83637, 0.16655, 0.60602, 0.04533, 0.4119, 0, 0.2799, 0.15955, 0.15308, 0.32506, 0.05214, 0.45561, 0.04179, 0.70737, 0.48179, 0.18053, 0.48696, 0.33439, 0.58014, 0.46726, 0.49214, 0.61878, 0.43779, 0.78663, 0.75873, 0.2901, 0.80791, 0.41831, 0.74579, 0.60014, 0.67591, 0.78196, 0.27473, 0.33206, 0.29544, 0.49524, 0.22038, 0.72368], "triangles": [0, 26, 1, 2, 23, 3, 24, 15, 16, 16, 15, 20, 24, 11, 15, 24, 12, 11, 20, 8, 7, 15, 9, 20, 20, 9, 8, 11, 10, 15, 15, 10, 9, 25, 12, 24, 12, 25, 13, 22, 17, 21, 25, 16, 17, 25, 24, 16, 21, 7, 6, 17, 20, 21, 17, 16, 20, 21, 20, 7, 3, 23, 4, 19, 18, 23, 23, 22, 4, 23, 18, 22, 4, 22, 5, 14, 13, 26, 26, 13, 25, 26, 25, 18, 18, 17, 22, 18, 25, 17, 6, 5, 21, 5, 22, 21, 1, 19, 2, 2, 19, 23, 1, 26, 19, 0, 14, 26, 19, 26, 18], "vertices": [4, 52, 1.3, 44.47, 0.26688, 53, -28.7, 44.47, 0.06689, 54, -58.7, 44.47, 0.00222, 51, -45.85, 2.54, 0.664, 3, 52, -5.76, 17.46, 0.32677, 53, -35.76, 17.46, 0.00923, 51, -18.7, -3.99, 0.664, 3, 52, -7.91, -11.03, 0.31838, 53, -37.91, -11.03, 0.01762, 51, 9.82, -5.58, 0.664, 4, 52, -1.34, -45.61, 0.18991, 53, -31.34, -45.61, 0.13654, 54, -61.34, -45.61, 0.00955, 51, 44.27, 1.67, 0.664, 4, 52, 19.99, -56.82, 0.32089, 53, -10.01, -56.82, 0.56933, 54, -40.01, -56.82, 0.10694, 55, -70.01, -56.82, 0.00285, 4, 52, 46.32, -61.75, 0.07189, 53, 16.32, -61.75, 0.55788, 54, -13.68, -61.75, 0.3141, 55, -43.68, -61.75, 0.05613, 4, 52, 65.84, -75.82, 0.00588, 53, 35.84, -75.82, 0.41157, 54, 5.84, -75.82, 0.42923, 55, -24.16, -75.82, 0.15333, 4, 52, 91.59, -72.58, 0, 53, 61.59, -72.58, 0.28374, 54, 31.59, -72.58, 0.43533, 55, 1.59, -72.58, 0.28092, 3, 53, 82.25, -54.2, 0.14059, 54, 52.25, -54.2, 0.33244, 55, 22.25, -54.2, 0.52697, 3, 53, 100.98, -23.12, 0.01178, 54, 70.98, -23.12, 0.04341, 55, 40.98, -23.12, 0.94481, 1, 55, 48.19, 3.18, 1, 3, 53, 84.34, 21.46, 0.00216, 54, 54.34, 21.46, 0.03149, 55, 24.34, 21.46, 0.96635, 4, 52, 89.58, 39.05, 0.00581, 53, 59.58, 39.05, 0.15218, 54, 29.58, 39.05, 0.45425, 55, -0.42, 39.05, 0.38776, 4, 52, 70.06, 53.04, 0.06639, 53, 40.06, 53.04, 0.39743, 54, 10.06, 53.04, 0.42578, 55, -19.94, 53.04, 0.1104, 4, 52, 32.07, 54.97, 0.42232, 53, 2.07, 54.97, 0.46578, 54, -27.93, 54.97, 0.10928, 55, -57.93, 54.97, 0.00262, 3, 53, 80.8, -5.95, 0.00203, 54, 50.8, -5.95, 0.00635, 55, 20.8, -5.95, 0.99162, 3, 53, 57.56, -6.34, 0.00827, 54, 27.56, -6.34, 0.76396, 55, -2.44, -6.34, 0.22777, 4, 52, 67.32, -18.74, 0, 53, 37.32, -18.74, 0.32042, 54, 7.32, -18.74, 0.64957, 55, -22.68, -18.74, 0.03, 3, 52, 44.61, -6.46, 0.00168, 53, 14.61, -6.46, 0.98114, 54, -15.39, -6.46, 0.01718, 1, 52, 19.37, 1.28, 1, 3, 53, 63.74, -43.39, 0.17782, 54, 33.74, -43.39, 0.41065, 55, 3.74, -43.39, 0.41153, 4, 52, 74.29, -49.81, 0.00283, 53, 44.29, -49.81, 0.35309, 54, 14.29, -49.81, 0.45958, 55, -15.71, -49.81, 0.1845, 4, 52, 46.96, -40.99, 0.07965, 53, 16.96, -40.99, 0.62402, 54, -13.04, -40.99, 0.26909, 55, -43.04, -40.99, 0.02723, 4, 52, 19.63, -31.11, 0.45986, 53, -10.37, -31.11, 0.49505, 54, -40.37, -31.11, 0.04509, 55, -70.37, -31.11, 0, 4, 52, 88.3, 22.52, 0.00041, 53, 58.3, 22.52, 0.06545, 54, 28.3, 22.52, 0.45262, 55, -1.7, 22.52, 0.48152, 4, 52, 63.63, 20.04, 0.02053, 53, 33.63, 20.04, 0.40089, 54, 3.63, 20.04, 0.53298, 55, -26.37, 20.04, 0.0456, 4, 52, 29.27, 30.71, 0.50274, 53, -0.73, 30.71, 0.44629, 54, -30.73, 30.71, 0.05075, 55, -60.73, 30.71, 0.00022], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28, 18, 30, 30, 32, 32, 34, 34, 36, 36, 38, 40, 42, 42, 44, 44, 46, 48, 50, 50, 52], "width": 136, "height": 151}}, "kaishijm023_2": {"kaishijm023_1": {"type": "mesh", "uvs": [0.12202, 0.91018, 0.32132, 0.95447, 0.53096, 0.96612, 0.78461, 0.9195, 0.86484, 0.7773, 0.89849, 0.60247, 1, 0.47192, 0.97355, 0.30175, 0.83637, 0.16655, 0.60602, 0.04533, 0.4119, 0, 0.2799, 0.15955, 0.15308, 0.32506, 0.05214, 0.45561, 0.04179, 0.70737, 0.48179, 0.18053, 0.48696, 0.33439, 0.58014, 0.46726, 0.49214, 0.61878, 0.43779, 0.78663, 0.75873, 0.2901, 0.80791, 0.41831, 0.74579, 0.60014, 0.67591, 0.78196, 0.27473, 0.33206, 0.29544, 0.49524, 0.22038, 0.72368], "triangles": [0, 26, 1, 2, 23, 3, 24, 15, 16, 16, 15, 20, 24, 11, 15, 24, 12, 11, 20, 8, 7, 15, 9, 20, 20, 9, 8, 11, 10, 15, 15, 10, 9, 25, 12, 24, 12, 25, 13, 22, 17, 21, 25, 16, 17, 25, 24, 16, 21, 7, 6, 17, 20, 21, 17, 16, 20, 21, 20, 7, 3, 23, 4, 19, 18, 23, 23, 22, 4, 23, 18, 22, 4, 22, 5, 14, 13, 26, 26, 13, 25, 26, 25, 18, 18, 17, 22, 18, 25, 17, 6, 5, 21, 5, 22, 21, 1, 19, 2, 2, 19, 23, 1, 26, 19, 0, 14, 26, 19, 26, 18], "vertices": [4, 83, 1.3, 44.47, 0.26688, 84, -28.7, 44.47, 0.06689, 85, -58.7, 44.47, 0.00222, 82, -45.85, 2.54, 0.664, 3, 83, -5.76, 17.46, 0.32677, 84, -35.76, 17.46, 0.00923, 82, -18.7, -3.99, 0.664, 3, 83, -7.91, -11.03, 0.31838, 84, -37.91, -11.03, 0.01762, 82, 9.82, -5.58, 0.664, 4, 83, -1.34, -45.61, 0.18991, 84, -31.34, -45.61, 0.13654, 85, -61.34, -45.61, 0.00955, 82, 44.27, 1.67, 0.664, 4, 83, 19.99, -56.82, 0.32089, 84, -10.01, -56.82, 0.56933, 85, -40.01, -56.82, 0.10694, 86, -70.01, -56.82, 0.00285, 4, 83, 46.32, -61.75, 0.07189, 84, 16.32, -61.75, 0.55788, 85, -13.68, -61.75, 0.3141, 86, -43.68, -61.75, 0.05613, 4, 83, 65.84, -75.82, 0.00588, 84, 35.84, -75.82, 0.41157, 85, 5.84, -75.82, 0.42923, 86, -24.16, -75.82, 0.15333, 4, 83, 91.59, -72.58, 0, 84, 61.59, -72.58, 0.28374, 85, 31.59, -72.58, 0.43533, 86, 1.59, -72.58, 0.28092, 3, 84, 82.25, -54.2, 0.14059, 85, 52.25, -54.2, 0.33244, 86, 22.25, -54.2, 0.52697, 3, 84, 100.98, -23.12, 0.01178, 85, 70.98, -23.12, 0.04341, 86, 40.98, -23.12, 0.94481, 1, 86, 48.19, 3.18, 1, 3, 84, 84.34, 21.46, 0.00216, 85, 54.34, 21.46, 0.03149, 86, 24.34, 21.46, 0.96635, 4, 83, 89.58, 39.05, 0.00581, 84, 59.58, 39.05, 0.15218, 85, 29.58, 39.05, 0.45425, 86, -0.42, 39.05, 0.38776, 4, 83, 70.06, 53.04, 0.06639, 84, 40.06, 53.04, 0.39743, 85, 10.06, 53.04, 0.42578, 86, -19.94, 53.04, 0.1104, 4, 83, 32.07, 54.97, 0.42232, 84, 2.07, 54.97, 0.46578, 85, -27.93, 54.97, 0.10928, 86, -57.93, 54.97, 0.00262, 3, 84, 80.8, -5.95, 0.00203, 85, 50.8, -5.95, 0.00635, 86, 20.8, -5.95, 0.99162, 3, 84, 57.56, -6.34, 0.00827, 85, 27.56, -6.34, 0.76396, 86, -2.44, -6.34, 0.22777, 4, 83, 67.32, -18.74, 0, 84, 37.32, -18.74, 0.32042, 85, 7.32, -18.74, 0.64957, 86, -22.68, -18.74, 0.03, 3, 83, 44.61, -6.46, 0.00168, 84, 14.61, -6.46, 0.98114, 85, -15.39, -6.46, 0.01718, 1, 83, 19.37, 1.28, 1, 3, 84, 63.74, -43.39, 0.17782, 85, 33.74, -43.39, 0.41065, 86, 3.74, -43.39, 0.41153, 4, 83, 74.29, -49.81, 0.00283, 84, 44.29, -49.81, 0.35309, 85, 14.29, -49.81, 0.45958, 86, -15.71, -49.81, 0.1845, 4, 83, 46.96, -40.99, 0.07965, 84, 16.96, -40.99, 0.62402, 85, -13.04, -40.99, 0.26909, 86, -43.04, -40.99, 0.02723, 4, 83, 19.63, -31.11, 0.45986, 84, -10.37, -31.11, 0.49505, 85, -40.37, -31.11, 0.04509, 86, -70.37, -31.11, 0, 4, 83, 88.3, 22.52, 0.00041, 84, 58.3, 22.52, 0.06545, 85, 28.3, 22.52, 0.45262, 86, -1.7, 22.52, 0.48152, 4, 83, 63.63, 20.04, 0.02053, 84, 33.63, 20.04, 0.40089, 85, 3.63, 20.04, 0.53298, 86, -26.37, 20.04, 0.0456, 4, 83, 29.27, 30.71, 0.50274, 84, -0.73, 30.71, 0.44629, 85, -30.73, 30.71, 0.05075, 86, -60.73, 30.71, 0.00022], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28, 18, 30, 30, 32, 32, 34, 34, 36, 36, 38, 40, 42, 42, 44, 44, 46, 48, 50, 50, 52], "width": 136, "height": 151}}, "kaishijm30": {"kaishijm030": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-55.1, -1.16, 69.21, 254.18, 530.46, 29.63, 406.14, -225.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 142, "height": 256}}, "kaishijm31": {"kaishijm031": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [27.65, -442, -326.3, 14.96, 133.82, 371.35, 487.77, -85.6], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 289, "height": 291}}, "kaishijm32": {"kaishijm032": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [517.32, -51.08, -665.68, -51.08, -665.68, 553.92, 517.32, 553.92], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 591, "height": 302}}, "kaishijm023_3": {"kaishijm023_1": {"type": "mesh", "uvs": [0.12202, 0.91018, 0.32132, 0.95447, 0.53096, 0.96612, 0.78461, 0.9195, 0.86484, 0.7773, 0.89849, 0.60247, 1, 0.47192, 0.97355, 0.30175, 0.83637, 0.16655, 0.60602, 0.04533, 0.4119, 0, 0.2799, 0.15955, 0.15308, 0.32506, 0.05214, 0.45561, 0.04179, 0.70737, 0.48179, 0.18053, 0.48696, 0.33439, 0.58014, 0.46726, 0.49214, 0.61878, 0.43779, 0.78663, 0.75873, 0.2901, 0.80791, 0.41831, 0.74579, 0.60014, 0.67591, 0.78196, 0.27473, 0.33206, 0.29544, 0.49524, 0.22038, 0.72368], "triangles": [0, 26, 1, 2, 23, 3, 24, 15, 16, 16, 15, 20, 24, 11, 15, 24, 12, 11, 20, 8, 7, 15, 9, 20, 20, 9, 8, 11, 10, 15, 15, 10, 9, 25, 12, 24, 12, 25, 13, 22, 17, 21, 25, 16, 17, 25, 24, 16, 21, 7, 6, 17, 20, 21, 17, 16, 20, 21, 20, 7, 3, 23, 4, 19, 18, 23, 23, 22, 4, 23, 18, 22, 4, 22, 5, 14, 13, 26, 26, 13, 25, 26, 25, 18, 18, 17, 22, 18, 25, 17, 6, 5, 21, 5, 22, 21, 1, 19, 2, 2, 19, 23, 1, 26, 19, 0, 14, 26, 19, 26, 18], "vertices": [4, 88, 1.3, 44.47, 0.26688, 89, -28.7, 44.47, 0.06689, 90, -58.7, 44.47, 0.00222, 87, -45.85, 2.54, 0.664, 3, 88, -5.76, 17.46, 0.32677, 89, -35.76, 17.46, 0.00923, 87, -18.7, -3.99, 0.664, 3, 88, -7.91, -11.03, 0.31838, 89, -37.91, -11.03, 0.01762, 87, 9.82, -5.58, 0.664, 4, 88, -1.34, -45.61, 0.18991, 89, -31.34, -45.61, 0.13654, 90, -61.34, -45.61, 0.00955, 87, 44.27, 1.67, 0.664, 4, 88, 19.99, -56.82, 0.32089, 89, -10.01, -56.82, 0.56933, 90, -40.01, -56.82, 0.10694, 91, -70.01, -56.82, 0.00285, 4, 88, 46.32, -61.75, 0.07189, 89, 16.32, -61.75, 0.55788, 90, -13.68, -61.75, 0.3141, 91, -43.68, -61.75, 0.05613, 4, 88, 65.84, -75.82, 0.00588, 89, 35.84, -75.82, 0.41157, 90, 5.84, -75.82, 0.42923, 91, -24.16, -75.82, 0.15333, 4, 88, 91.59, -72.58, 0, 89, 61.59, -72.58, 0.28374, 90, 31.59, -72.58, 0.43533, 91, 1.59, -72.58, 0.28092, 3, 89, 82.25, -54.2, 0.14059, 90, 52.25, -54.2, 0.33244, 91, 22.25, -54.2, 0.52697, 3, 89, 100.98, -23.12, 0.01178, 90, 70.98, -23.12, 0.04341, 91, 40.98, -23.12, 0.94481, 1, 91, 48.19, 3.18, 1, 3, 89, 84.34, 21.46, 0.00216, 90, 54.34, 21.46, 0.03149, 91, 24.34, 21.46, 0.96635, 4, 88, 89.58, 39.05, 0.00581, 89, 59.58, 39.05, 0.15218, 90, 29.58, 39.05, 0.45425, 91, -0.42, 39.05, 0.38776, 4, 88, 70.06, 53.04, 0.06639, 89, 40.06, 53.04, 0.39743, 90, 10.06, 53.04, 0.42578, 91, -19.94, 53.04, 0.1104, 4, 88, 32.07, 54.97, 0.42232, 89, 2.07, 54.97, 0.46578, 90, -27.93, 54.97, 0.10928, 91, -57.93, 54.97, 0.00262, 3, 89, 80.8, -5.95, 0.00203, 90, 50.8, -5.95, 0.00635, 91, 20.8, -5.95, 0.99162, 3, 89, 57.56, -6.34, 0.00827, 90, 27.56, -6.34, 0.76396, 91, -2.44, -6.34, 0.22777, 4, 88, 67.32, -18.74, 0, 89, 37.32, -18.74, 0.32042, 90, 7.32, -18.74, 0.64957, 91, -22.68, -18.74, 0.03, 3, 88, 44.61, -6.46, 0.00168, 89, 14.61, -6.46, 0.98114, 90, -15.39, -6.46, 0.01718, 1, 88, 19.37, 1.28, 1, 3, 89, 63.74, -43.39, 0.17782, 90, 33.74, -43.39, 0.41065, 91, 3.74, -43.39, 0.41153, 4, 88, 74.29, -49.81, 0.00283, 89, 44.29, -49.81, 0.35309, 90, 14.29, -49.81, 0.45958, 91, -15.71, -49.81, 0.1845, 4, 88, 46.96, -40.99, 0.07965, 89, 16.96, -40.99, 0.62402, 90, -13.04, -40.99, 0.26909, 91, -43.04, -40.99, 0.02723, 4, 88, 19.63, -31.11, 0.45986, 89, -10.37, -31.11, 0.49505, 90, -40.37, -31.11, 0.04509, 91, -70.37, -31.11, 0, 4, 88, 88.3, 22.52, 0.00041, 89, 58.3, 22.52, 0.06545, 90, 28.3, 22.52, 0.45262, 91, -1.7, 22.52, 0.48152, 4, 88, 63.63, 20.04, 0.02053, 89, 33.63, 20.04, 0.40089, 90, 3.63, 20.04, 0.53298, 91, -26.37, 20.04, 0.0456, 4, 88, 29.27, 30.71, 0.50274, 89, -0.73, 30.71, 0.44629, 90, -30.73, 30.71, 0.05075, 91, -60.73, 30.71, 0.00022], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28, 18, 30, 30, 32, 32, 34, 34, 36, 36, 38, 40, 42, 42, 44, 44, 46, 48, 50, 50, 52], "width": 136, "height": 151}}, "kaishijm36": {"kaishijm036": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-367.8, 135.8, 1193.39, 545.29, 1369.72, -126.97, -191.47, -536.46], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 484, "height": 208}}, "kaishijm020": {"kaishijm020": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-121.23, -246.98, -19.92, 290.56, 540.22, 184.99, 438.91, -352.55], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 273, "height": 285}}, "kaishijm06": {"kaishijm06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [44.89, -243.4, -143.59, 224.03, 66.94, 308.92, 255.42, -158.51], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 252, "height": 113}}, "kaishijm022": {"kaishijm022": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [356.5, -606, -393.5, -606, -393.5, 203, 356.5, 203], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 450, "height": 485}}, "kaishijm023": {"kaishijm023": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [93.24, -125.82, -122.75, -127.09, -125.03, 257.9, 90.96, 259.18], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 108, "height": 192}}, "kaishijm024": {"kaishijm024": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-194.6, -256.84, -93.45, 182.67, 392.84, 70.76, 291.69, -368.75], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 270, "height": 299}}, "kaishijm025": {"kaishijm025": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [42.12, -35.29, 70.38, 87.5, 324.73, 28.97, 296.47, -93.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 126, "height": 261}}, "kaishijm026": {"kaishijm026": {"type": "mesh", "uvs": [0.20826, 0.0055, 0.23628, 0.03173, 0.25261, 0.03391, 0.30454, 0.03379, 0.39961, 0.0908, 0.41584, 0.12022, 0.41542, 0.1714, 0.39914, 0.21669, 0.37656, 0.29961, 0.38013, 0.33449, 0.39921, 0.3487, 0.42782, 0.42619, 0.46589, 0.47703, 0.51983, 0.50067, 0.55265, 0.47276, 0.55306, 0.44818, 0.58025, 0.42548, 0.58845, 0.3954, 0.55455, 0.36476, 0.54753, 0.33185, 0.51216, 0.31371, 0.54121, 0.25588, 0.54096, 0.24482, 0.51041, 0.2352, 0.49466, 0.20661, 0.5028, 0.17557, 0.52819, 0.14299, 0.52819, 0.05929, 0.57507, 0.0217, 0.60086, 0.01621, 0.65771, 0.01714, 0.67037, 0.02364, 0.69215, 0.06281, 0.7041, 0.07374, 0.75756, 0.07381, 0.79257, 0.08514, 0.81152, 0.10221, 0.85611, 0.11841, 0.88077, 0.14242, 0.88061, 0.19927, 0.8551, 0.29681, 0.94835, 0.17852, 0.96863, 0.18192, 0.96875, 0.21708, 0.99905, 0.2554, 0.9544, 0.30615, 0.93929, 0.33895, 0.9364, 0.42702, 0.89777, 0.46204, 0.8679, 0.4546, 0.83166, 0.44557, 0.82336, 0.46363, 0.81817, 0.49712, 0.81206, 0.53657, 0.83452, 0.57093, 0.85276, 0.63795, 0.8719, 0.70828, 0.86761, 0.77399, 0.84735, 0.79277, 0.85093, 0.84376, 0.86182, 0.89185, 0.89905, 0.92573, 0.88379, 0.94396, 0.81317, 0.95654, 0.79302, 0.94597, 0.78049, 0.89421, 0.74634, 0.802, 0.70857, 0.80659, 0.68767, 0.86902, 0.6675, 0.92926, 0.66684, 0.96393, 0.63732, 0.97394, 0.58473, 0.96597, 0.58458, 0.926, 0.5961, 0.9059, 0.59731, 0.84605, 0.59845, 0.78987, 0.555, 0.78993, 0.53028, 0.77197, 0.54185, 0.74295, 0.59119, 0.67574, 0.62182, 0.60315, 0.64533, 0.54744, 0.56295, 0.5917, 0.52251, 0.63743, 0.36107, 0.4909, 0.35256, 0.51128, 0.35309, 0.5424, 0.37502, 0.62128, 0.3894, 0.67301, 0.40583, 0.7321, 0.39333, 0.83111, 0.39, 0.89348, 0.40498, 0.91495, 0.45465, 0.93859, 0.45209, 0.97218, 0.371, 0.98406, 0.32081, 0.98295, 0.31865, 0.9344, 0.28129, 0.81342, 0.24894, 0.77317, 0.22828, 0.84135, 0.20793, 0.90849, 0.20762, 0.92013, 0.23524, 0.96142, 0.22446, 0.98123, 0.18314, 0.99457, 0.12567, 0.99501, 0.12542, 0.93363, 0.12522, 0.8826, 0.11994, 0.84541, 0.11099, 0.78243, 0.09646, 0.76107, 0.11018, 0.70068, 0.0634, 0.67941, 0.05152, 0.58551, 0.04121, 0.53979, 0.05933, 0.47248, 0.07598, 0.41063, 0.08094, 0.3615, 0.09557, 0.35315, 0.08086, 0.27438, 0.04159, 0.27208, 0.08413, 0.24108, 0.09877, 0.21294, 0.08782, 0.15946, 0.04014, 0.15907, 0.00327, 0.13644, 0.00608, 0.12488, 0.04996, 0.12493, 0.07564, 0.10201, 0.03117, 0.07631, 0.0298, 0.06242, 0.07536, 0.06224, 0.12707, 0.01582, 0.14772, 0.00546, 0.23145, 0.35652, 0.2272, 0.42565, 0.2272, 0.51111, 0.22826, 0.60713, 0.23145, 0.70028, 0.12197, 0.39685, 0.10709, 0.43237, 0.0954, 0.47654, 0.07946, 0.54664, 0.08584, 0.58985, 0.0954, 0.65035, 0.14642, 0.49095, 0.15174, 0.53704, 0.13367, 0.64746, 0.16555, 0.43814, 0.36006, 0.41029, 0.40683, 0.46982, 0.48761, 0.5428, 0.54075, 0.53992, 0.59815, 0.50151, 0.6396, 0.4439, 0.29735, 0.393, 0.29204, 0.46022, 0.28885, 0.51591, 0.30479, 0.6129, 0.32818, 0.707, 0.33455, 0.76653, 0.34412, 0.82319, 0.35156, 0.92017, 0.39407, 0.9509, 0.1815, 0.70124, 0.17299, 0.77998, 0.16768, 0.83759, 0.16874, 0.91249, 0.17087, 0.9509, 0.23841, 0.28363, 0.25436, 0.17992, 0.25436, 0.07814, 0.16401, 0.25866, 0.15125, 0.32108, 0.71132, 0.21896, 0.70291, 0.31772, 0.71132, 0.42516, 0.71493, 0.52608, 0.72694, 0.60422, 0.72333, 0.68018, 0.72694, 0.75724, 0.80262, 0.79739, 0.81463, 0.87987, 0.84586, 0.9211, 0.65847, 0.79739, 0.64285, 0.86359, 0.63324, 0.94498, 0.67325, 0.61682, 0.65403, 0.68627, 0.61679, 0.7427, 0.79818, 0.61899, 0.80419, 0.6917, 0.83662, 0.75681, 0.78496, 0.3835, 0.85464, 0.3835, 0.89548, 0.32815, 0.93873, 0.26955, 0.95554, 0.24676, 0.63, 0.36396, 0.77295, 0.34877, 0.78016, 0.26521, 0.78977, 0.19141, 0.6228, 0.27715, 0.60958, 0.17079, 0.67085, 0.13607, 0.79578, 0.14692, 0.80615, 0.41865, 0.77558, 0.44627, 0.7715, 0.52266], "triangles": [183, 194, 58, 75, 76, 186, 184, 66, 183, 68, 186, 67, 72, 188, 71, 71, 188, 70, 72, 73, 188, 70, 188, 69, 63, 185, 62, 63, 64, 185, 185, 65, 184, 185, 64, 65, 73, 74, 188, 69, 188, 187, 62, 185, 61, 188, 74, 187, 69, 187, 68, 185, 60, 61, 185, 184, 60, 184, 65, 66, 184, 59, 60, 184, 183, 59, 187, 186, 68, 187, 75, 186, 74, 75, 187, 183, 58, 59, 66, 67, 182, 67, 186, 182, 66, 182, 183, 76, 191, 186, 183, 182, 194, 186, 191, 182, 58, 194, 57, 76, 77, 79, 77, 78, 79, 76, 79, 191, 57, 194, 56, 191, 190, 182, 190, 181, 182, 182, 193, 194, 182, 181, 193, 194, 193, 56, 79, 80, 191, 191, 80, 190, 193, 55, 56, 181, 192, 193, 193, 192, 55, 80, 81, 190, 190, 189, 181, 190, 81, 189, 181, 180, 192, 181, 189, 180, 192, 54, 55, 180, 210, 192, 192, 53, 54, 192, 210, 53, 180, 189, 82, 189, 81, 82, 82, 179, 180, 180, 179, 210, 179, 82, 156, 53, 210, 52, 156, 178, 179, 210, 179, 209, 210, 209, 52, 209, 179, 178, 52, 209, 51, 51, 209, 50, 156, 200, 178, 209, 178, 195, 197, 198, 45, 44, 198, 199, 44, 45, 198, 199, 198, 41, 41, 43, 199, 199, 43, 44, 198, 40, 41, 43, 41, 42, 197, 40, 198, 48, 49, 47, 47, 49, 196, 209, 208, 50, 49, 50, 196, 50, 208, 196, 47, 196, 46, 46, 196, 197, 208, 195, 196, 197, 196, 40, 40, 196, 201, 46, 197, 45, 208, 209, 195, 178, 201, 195, 178, 177, 201, 196, 195, 201, 201, 202, 40, 201, 177, 202, 177, 176, 202, 177, 204, 176, 40, 202, 39, 39, 202, 203, 204, 205, 176, 202, 176, 203, 205, 22, 25, 205, 25, 26, 24, 25, 22, 205, 206, 176, 176, 206, 207, 203, 176, 207, 206, 34, 207, 203, 207, 39, 39, 207, 38, 38, 207, 37, 207, 34, 35, 206, 33, 34, 206, 205, 27, 206, 27, 28, 32, 206, 29, 30, 32, 29, 28, 29, 206, 207, 36, 37, 207, 35, 36, 205, 26, 27, 206, 32, 33, 31, 32, 30, 200, 177, 178, 200, 204, 177, 19, 21, 204, 204, 22, 205, 204, 21, 22, 23, 24, 22, 17, 200, 156, 17, 18, 200, 18, 19, 200, 19, 204, 200, 20, 21, 19, 82, 154, 155, 154, 14, 155, 82, 155, 156, 155, 14, 16, 14, 15, 16, 155, 16, 156, 16, 17, 156, 152, 153, 84, 83, 153, 154, 83, 84, 153, 82, 83, 154, 153, 13, 154, 154, 13, 14, 152, 12, 153, 153, 12, 13, 85, 152, 84, 86, 158, 85, 152, 85, 151, 152, 11, 12, 85, 158, 151, 152, 151, 11, 158, 157, 151, 151, 10, 11, 151, 9, 10, 151, 157, 9, 166, 113, 149, 107, 170, 106, 107, 108, 170, 106, 170, 105, 96, 165, 95, 165, 96, 164, 164, 96, 98, 96, 97, 98, 105, 170, 104, 95, 165, 94, 170, 103, 104, 165, 93, 94, 165, 164, 93, 108, 169, 170, 170, 169, 103, 98, 163, 164, 98, 99, 163, 108, 109, 169, 164, 92, 93, 164, 163, 92, 103, 169, 102, 102, 169, 168, 169, 109, 168, 102, 168, 101, 92, 163, 91, 109, 110, 168, 110, 111, 168, 168, 167, 101, 101, 167, 100, 168, 111, 167, 90, 91, 162, 99, 162, 163, 91, 163, 162, 99, 100, 162, 111, 112, 167, 167, 166, 100, 166, 140, 100, 112, 113, 167, 167, 113, 166, 100, 161, 162, 100, 140, 161, 162, 161, 90, 161, 89, 90, 140, 160, 161, 161, 88, 89, 161, 160, 88, 139, 140, 166, 166, 149, 139, 140, 139, 160, 149, 148, 139, 160, 87, 88, 139, 159, 160, 160, 159, 87, 149, 145, 148, 114, 146, 113, 146, 115, 145, 146, 114, 115, 146, 145, 149, 115, 144, 145, 145, 144, 148, 115, 116, 144, 113, 146, 149, 116, 117, 144, 144, 143, 147, 144, 117, 143, 143, 142, 147, 147, 142, 150, 143, 117, 142, 117, 118, 142, 142, 141, 150, 142, 118, 141, 148, 138, 139, 139, 138, 159, 159, 86, 87, 148, 147, 138, 159, 158, 86, 159, 138, 158, 147, 150, 138, 150, 137, 138, 138, 137, 158, 158, 137, 157, 137, 150, 136, 144, 147, 148, 137, 136, 157, 136, 150, 141, 141, 175, 136, 171, 157, 136, 136, 175, 171, 8, 9, 157, 8, 157, 171, 118, 119, 141, 119, 120, 141, 141, 120, 175, 120, 121, 175, 175, 174, 171, 175, 121, 174, 8, 171, 7, 171, 172, 7, 171, 174, 172, 121, 123, 174, 121, 122, 123, 123, 124, 174, 174, 124, 172, 7, 172, 6, 172, 124, 173, 1, 173, 125, 134, 1, 130, 124, 125, 173, 6, 172, 5, 4, 173, 3, 2, 173, 1, 4, 172, 173, 5, 172, 4, 1, 135, 0, 1, 134, 135, 126, 129, 125, 129, 130, 125, 1, 125, 130, 126, 127, 129, 129, 127, 128, 131, 133, 130, 130, 133, 134, 173, 2, 3, 131, 132, 133], "vertices": [2, 67, 44.29, -3.29, 0.99916, 62, 65.02, 18.75, 0.00084, 2, 67, 39.04, -6.98, 0.99217, 62, 60.32, 14.39, 0.00783, 2, 67, 38.26, -9.49, 0.98381, 62, 59.88, 11.8, 0.01619, 2, 67, 37, -17.65, 0.95517, 62, 59.74, 3.54, 0.04483, 2, 67, 24.74, -31.02, 0.86129, 62, 49.42, -11.37, 0.13871, 2, 67, 19.23, -32.77, 0.83587, 62, 44.19, -13.85, 0.16413, 2, 67, 10.34, -31.31, 0.77878, 62, 35.18, -13.61, 0.22122, 2, 67, 2.87, -27.52, 0.69037, 62, 27.26, -10.86, 0.30963, 4, 67, -11, -21.71, 0.33287, 62, 12.74, -6.99, 0.62829, 61, 18.34, -24.65, 0.02901, 77, -13.21, 14.05, 0.00983, 4, 67, -17.15, -21.32, 0.12416, 62, 6.59, -7.44, 0.724, 61, 12.19, -25.09, 0.01477, 77, -8.03, 10.71, 0.13708, 4, 67, -20.09, -23.93, 0.05364, 62, 4.03, -10.42, 0.61232, 61, 9.63, -28.08, 0.00075, 77, -4.19, 11.56, 0.33329, 4, 62, -9.69, -14.7, 0.01694, 77, 9.35, 6.73, 0.94426, 78, -5.91, 6.73, 0.03876, 76, 15.2, -23.63, 4e-05, 3, 77, 20.13, 5.97, 0.06323, 78, 4.87, 5.97, 0.83372, 76, 12.64, -13.14, 0.10305, 4, 78, 13.44, 10.15, 0.18613, 76, 6.04, -6.25, 0.70924, 75, 15.52, -8.68, 0.10429, 70, -17.93, 20.33, 0.00033, 4, 78, 12.79, 17.29, 0.00209, 76, -0.56, -9.04, 0.08173, 75, 8.69, -6.53, 0.89204, 70, -13.68, 14.56, 0.02413, 3, 76, -2.12, -13.08, 0.01609, 75, 4.88, -8.59, 0.90307, 70, -9.39, 13.98, 0.08085, 2, 75, -0.72, -6.78, 0.76255, 70, -5.95, 9.2, 0.23745, 2, 75, -5.97, -8.24, 0.24172, 70, -0.85, 7.27, 0.75828, 4, 75, -8.04, -15.58, 0.02528, 70, 5.15, 11.97, 0.77409, 69, 11.86, 20.28, 0.20005, 74, -5.22, 26.25, 0.00058, 4, 75, -12.54, -19.39, 0.00114, 70, 11.04, 12.38, 0.63153, 69, 17.75, 20.69, 0.35031, 74, 0.64, 26.95, 0.01702, 3, 70, 14.88, 17.58, 0.55983, 69, 21.59, 25.89, 0.40568, 74, 4.22, 32.33, 0.03449, 3, 70, 24.43, 11.77, 0.35345, 69, 31.14, 20.08, 0.44479, 74, 14.04, 27, 0.20176, 3, 70, 26.37, 11.58, 0.2958, 69, 33.08, 19.89, 0.42784, 74, 15.99, 26.9, 0.27636, 3, 70, 28.64, 16.2, 0.24139, 69, 35.34, 24.51, 0.40009, 74, 18.02, 31.62, 0.35852, 3, 70, 33.93, 18.08, 0.21162, 69, 40.64, 26.39, 0.37907, 74, 23.22, 33.76, 0.40931, 3, 70, 39.2, 16.13, 0.17969, 69, 45.91, 24.44, 0.34506, 74, 28.58, 32.08, 0.47525, 3, 70, 44.41, 11.44, 0.12186, 69, 51.11, 19.75, 0.26853, 74, 34.01, 27.65, 0.60961, 3, 70, 59.03, 9.66, 0.04386, 69, 65.74, 17.97, 0.14644, 74, 48.7, 26.6, 0.80969, 3, 70, 64.7, 1.47, 0.02591, 69, 71.41, 9.78, 0.11143, 74, 54.77, 18.69, 0.86266, 3, 70, 65.17, -2.72, 0.02068, 69, 71.87, 5.59, 0.09925, 74, 55.44, 14.53, 0.88007, 3, 70, 63.92, -11.67, 0.01194, 69, 70.62, -3.36, 0.0749, 74, 54.63, 5.53, 0.91316, 4, 70, 62.54, -13.53, 0.01109, 69, 69.25, -5.22, 0.07157, 74, 53.35, 3.6, 0.91727, 72, 28.97, 61.44, 6e-05, 4, 70, 55.28, -16.14, 0.00821, 69, 61.99, -7.83, 0.05141, 74, 46.23, 0.64, 0.93861, 72, 25.93, 54.35, 0.00178, 4, 70, 53.14, -17.8, 0.00572, 69, 59.85, -9.49, 0.0369, 74, 44.17, -1.12, 0.95318, 72, 25.68, 51.65, 0.0042, 5, 70, 52.11, -26.23, 0.00016, 69, 58.81, -17.92, 0.0048, 74, 43.56, -9.6, 0.97681, 71, 50.2, -4.34, 0.00041, 72, 31.14, 45.15, 0.01782, 3, 74, 41.17, -15.01, 0.967, 71, 47.55, -9.62, 0.00273, 72, 33.2, 39.6, 0.03027, 3, 74, 37.96, -17.8, 0.94998, 71, 44.2, -12.25, 0.00661, 72, 32.85, 35.36, 0.04341, 4, 74, 34.61, -24.67, 0.9145, 71, 40.52, -18.95, 0.01576, 72, 35.23, 28.1, 0.06967, 73, 26.35, 23.74, 7e-05, 4, 74, 30.12, -28.28, 0.89167, 71, 35.85, -22.33, 0.02261, 72, 34.53, 22.38, 0.08485, 73, 24.57, 18.25, 0.00087, 4, 74, 20.14, -27.54, 0.81225, 71, 25.92, -21.1, 0.04761, 72, 26.86, 15.96, 0.13033, 73, 15.83, 13.39, 0.00981, 4, 74, 3.3, -22.27, 0.90681, 71, 9.37, -15.01, 0.01395, 72, 11.11, 8, 0.05379, 73, -1.14, 8.55, 0.02545, 1, 73, 24.27, 5.77, 1, 1, 73, 25.32, 2.67, 1, 1, 73, 19.93, -0.37, 1, 1, 73, 16.4, -7.87, 1, 2, 72, 20.03, -5.14, 0.02877, 73, 5.14, -6.03, 0.97123, 2, 72, 14.06, -7.02, 0.62057, 73, -1.07, -6.75, 0.37943, 2, 72, 1.91, -16.65, 0.98092, 68, 7.62, -38.26, 0.01908, 4, 71, -20.32, -18.25, 0.01534, 72, -6.76, -15.92, 0.92712, 68, 2.63, -31.13, 0.05643, 80, -12.27, 30.02, 0.00111, 4, 71, -18.45, -13.69, 0.054, 72, -8.82, -11.44, 0.8331, 68, 4.76, -26.68, 0.10561, 80, -13.81, 25.34, 0.00728, 5, 69, -7.56, -21.75, 0.0025, 71, -16.18, -8.16, 0.16968, 72, -11.32, -6.01, 0.51982, 68, 7.33, -21.29, 0.26952, 80, -15.67, 19.66, 0.03847, 5, 69, -10.55, -20.05, 0.00249, 71, -19.17, -6.47, 0.16392, 72, -14.6, -7.05, 0.30752, 68, 4.44, -19.43, 0.41134, 80, -12.56, 18.19, 0.11474, 5, 71, -24.92, -4.94, 0.09626, 72, -19.64, -10.22, 0.13932, 68, -1.22, -17.58, 0.4722, 80, -6.71, 17.08, 0.29187, 81, -29.67, 8.67, 0.00035, 5, 71, -31.7, -3.14, 0.03332, 72, -25.57, -13.95, 0.04282, 68, -7.89, -15.41, 0.26125, 80, 0.17, 15.77, 0.64366, 81, -22.71, 9.47, 0.01895, 5, 71, -38.13, -5.96, 0.00959, 72, -27.9, -20.57, 0.01232, 68, -14.47, -17.86, 0.08431, 80, 6.39, 19.05, 0.78888, 81, -17.74, 14.44, 0.1049, 5, 71, -50.19, -7.42, 0.00018, 72, -35.05, -30.39, 0.00025, 68, -26.59, -18.64, 0.00192, 80, 18.31, 21.37, 0.53497, 81, -7.05, 20.19, 0.46269, 2, 80, 30.82, 23.82, 0.17705, 81, 4.18, 26.23, 0.82295, 2, 80, 42.34, 22.58, 0.04183, 81, 15.55, 28.46, 0.95817, 1, 59, 57.23, 11.77, 1, 1, 59, 57.75, 2.8, 1, 1, 59, 59.44, -5.68, 1, 1, 59, 65.34, -11.67, 1, 1, 59, 62.89, -14.86, 1, 1, 59, 51.66, -17.02, 1, 1, 59, 48.46, -15.15, 1, 1, 59, 46.51, -6.03, 1, 1, 59, 41.16, 10.23, 1, 1, 59, 35.15, 9.45, 1, 1, 59, 31.77, -1.52, 1, 1, 59, 28.52, -12.11, 1, 1, 59, 28.38, -18.21, 1, 1, 59, 23.68, -19.95, 1, 1, 59, 15.33, -18.51, 1, 1, 59, 15.34, -11.47, 1, 1, 59, 17.18, -7.94, 1, 1, 59, 17.43, 2.59, 1, 1, 59, 17.66, 12.47, 1, 1, 81, 30.68, -18.96, 1, 2, 80, 39.39, -30.98, 0.00095, 81, 28.6, -23.56, 0.99905, 2, 80, 34.38, -28.89, 0.0093, 81, 23.2, -23.06, 0.9907, 3, 75, 36.83, 16.3, 0.00096, 80, 22.94, -20.49, 0.18636, 81, 9.78, -18.42, 0.81268, 5, 76, -2.89, 16.3, 0.0061, 75, 23.31, 14.29, 0.0424, 68, -14.1, 16.43, 0.00398, 80, 10.42, -15.01, 0.71525, 81, -3.8, -16.89, 0.23226, 5, 76, -9.81, 8.4, 0.08899, 75, 12.93, 12.75, 0.30277, 68, -5.11, 11.03, 0.15313, 80, 0.81, -10.8, 0.44481, 81, -14.23, -15.72, 0.01029, 4, 76, 5.18, 11.15, 0.91845, 75, 26.13, 5.14, 0.07457, 68, -10.47, 25.29, 0.00079, 80, 7.95, -24.26, 0.00618, 1, 76, 14.01, 16.46, 1, 4, 60, 4.89, -20.17, 0.23194, 66, -15.54, 17.63, 0.05631, 77, 11.77, -8.65, 0.60626, 78, -3.49, -8.65, 0.10548, 4, 60, 1.46, -18.45, 0.38861, 66, -11.89, 16.46, 0.18503, 77, 13.76, -11.93, 0.40408, 78, -1.5, -11.93, 0.02228, 4, 60, -4, -17.97, 0.33664, 66, -6.42, 16.84, 0.44456, 77, 18.12, -15.24, 0.21742, 78, 2.86, -15.24, 0.00138, 3, 60, -18.17, -20, 0.04044, 66, 7.26, 21.06, 0.92182, 77, 31.2, -21.06, 0.03774, 3, 60, -27.46, -21.34, 0.00168, 66, 16.23, 23.82, 0.98981, 77, 39.78, -24.88, 0.00851, 2, 66, 26.47, 26.98, 0.99954, 77, 49.57, -29.24, 0.00046, 1, 66, 43.98, 25.92, 1, 1, 66, 54.97, 25.98, 1, 1, 66, 58.61, 28.55, 1, 1, 66, 62.35, 36.66, 1, 1, 66, 68.28, 36.57, 1, 1, 66, 71.05, 23.8, 1, 1, 66, 71.28, 15.82, 1, 1, 66, 62.76, 15.03, 1, 1, 66, 41.81, 7.97, 1, 1, 66, 35.01, 2.46, 1, 2, 65, 55.09, 16.97, 0.00512, 66, 47.17, -0.19, 0.99488, 2, 65, 66.34, 12.12, 0.00012, 66, 59.14, -2.79, 0.99988, 1, 66, 61.19, -2.73, 1, 1, 66, 68.21, 2.04, 1, 1, 66, 71.79, 0.51, 1, 1, 66, 74.48, -5.93, 1, 1, 59, -57.69, -23.27, 1, 2, 65, 68.89, -1.48, 0.00034, 66, 64.26, -15.66, 0.99966, 2, 65, 60, -0.27, 0.00896, 66, 55.29, -16.17, 0.99104, 1, 66, 48.8, -17.35, 1, 1, 66, 37.8, -19.36, 1, 1, 66, 34.17, -21.87, 1, 2, 65, 27.96, 1.83, 0.61136, 66, 23.44, -20.25, 0.38864, 2, 65, 23.21, -5.02, 0.98407, 66, 20.1, -27.88, 0.01593, 2, 64, 25.23, -2.3, 0.00697, 65, 6.59, -4.59, 0.99303, 2, 64, 17.6, -5.34, 0.63235, 65, -1.61, -5.09, 0.36765, 1, 64, 5.43, -4.61, 1, 2, 63, -5.14, 5.21, 0.46876, 64, -5.75, -3.93, 0.53124, 3, 67, -14.47, 26.41, 0.02098, 63, 3.48, 4.26, 0.94717, 61, 8.37, 22.56, 0.03186, 3, 67, -13.38, 23.89, 0.05049, 63, 4.91, 1.9, 0.84465, 61, 9.8, 20.21, 0.10486, 3, 67, 0.68, 24.05, 0.40921, 63, 18.81, 3.97, 0.40392, 61, 23.7, 22.27, 0.18688, 3, 67, 2.05, 30.16, 0.42978, 63, 19.34, 10.2, 0.39634, 61, 24.23, 28.51, 0.17388, 3, 67, 6.39, 22.63, 0.5196, 63, 24.66, 3.33, 0.32501, 61, 29.55, 21.64, 0.1554, 3, 67, 10.92, 19.56, 0.71449, 63, 29.57, 0.91, 0.19455, 61, 34.46, 19.21, 0.09096, 3, 67, 20.49, 19.82, 0.93501, 63, 39.01, 2.47, 0.05216, 61, 43.9, 20.77, 0.01282, 3, 67, 21.73, 27.3, 0.97388, 63, 39.23, 10.04, 0.02535, 61, 44.12, 28.35, 0.00077, 2, 67, 26.57, 32.48, 0.97821, 63, 43.33, 15.83, 0.02179, 2, 67, 28.51, 31.72, 0.97824, 63, 45.35, 15.34, 0.02176, 3, 67, 27.42, 24.83, 0.97507, 63, 45.21, 8.37, 0.02411, 61, 50.1, 26.67, 0.00082, 3, 67, 30.78, 20.17, 0.98657, 63, 49.16, 4.2, 0.01313, 61, 54.05, 22.51, 0.0003, 3, 67, 36.34, 26.45, 0.99701, 63, 53.82, 11.18, 0.00299, 61, 58.71, 29.49, 0, 3, 67, 38.79, 26.29, 0.99709, 63, 56.27, 11.36, 0.00291, 61, 61.16, 29.66, 0, 2, 67, 37.7, 19.13, 0.99598, 63, 56.16, 4.11, 0.00402, 1, 67, 44.5, 9.74, 1, 1, 67, 45.79, 6.21, 1, 5, 67, -17.31, 2.64, 0.00466, 62, 3.18, 16.28, 0.04226, 61, 8.78, -1.38, 0.95154, 60, 30.54, -2.11, 0.00062, 77, -19.56, -10.28, 0.00093, 4, 62, -8.97, 17.19, 0.00136, 61, -3.37, -0.47, 0.01042, 60, 18.51, -0.19, 0.98651, 77, -10.4, -18.32, 0.00171, 3, 60, 3.55, 1.37, 0.9765, 64, 7.39, 22.87, 0.01822, 65, -2.49, 24.9, 0.00528, 4, 60, -13.28, 2.95, 0.01842, 64, 23.99, 26.03, 0.00495, 65, 14.27, 22.71, 0.04418, 66, 6.01, -2.38, 0.93245, 2, 65, 30.57, 20.93, 0.01195, 66, 22.35, -1, 0.98805, 4, 63, -2.86, -2.14, 0.73071, 61, 2.02, 16.16, 0.05378, 60, 25.28, 15.93, 0.05288, 64, -9.43, 2.83, 0.16262, 4, 63, -9.07, 0.34, 0.1839, 61, -4.18, 18.65, 0.00898, 60, 19.31, 18.93, 0.05252, 64, -2.86, 1.62, 0.7546, 4, 60, 11.76, 21.58, 0.02244, 64, 5.12, 1.17, 0.97717, 65, -11.43, 4.99, 0.0001, 66, -15.83, -24.69, 0.00029, 4, 60, -0.24, 25.38, 0.00493, 64, 17.71, 0.86, 0.44359, 65, 0.43, 0.76, 0.54945, 66, -3.37, -26.57, 0.00204, 4, 60, -7.91, 25.16, 0.00339, 64, 25.01, 3.21, 0.00447, 65, 8.1, 0.71, 0.98615, 66, 4.17, -25.15, 0.00598, 4, 60, -18.66, 24.75, 0.0004, 64, 35.22, 6.59, 0.00025, 65, 18.86, 0.73, 0.98019, 66, 14.72, -23.07, 0.01916, 5, 63, -19.5, -5.71, 0.01126, 60, 8.41, 13.78, 0.56721, 64, 6.17, 9.6, 0.36993, 65, -7.79, 12.67, 0.03577, 66, -13.72, -16.46, 0.01583, 4, 60, 0.25, 13.78, 0.46744, 64, 14.01, 11.87, 0.088, 65, 0.36, 12.38, 0.30062, 66, -5.67, -15.18, 0.14394, 4, 60, -18.78, 18.64, 0.00219, 64, 33.64, 12.49, 0.00082, 65, 19.2, 6.83, 0.11859, 66, 13.89, -17.02, 0.8784, 4, 63, -10.26, -8.93, 0.13908, 61, -5.38, 9.37, 0.0651, 60, 17.34, 9.79, 0.48318, 64, -3.51, 10.94, 0.31265, 2, 66, -29.7, 16.71, 6e-05, 77, 0.51, -0.03, 0.99994, 4, 60, 7.82, -27.79, 0.00516, 66, -19.63, 24.69, 0.00105, 77, 13.34, -0.64, 0.77555, 78, -1.92, -0.64, 0.21825, 2, 78, 16.11, 1.55, 0.43432, 76, 13.42, -1.08, 0.56568, 2, 76, 5.32, 1.38, 0.99958, 80, -1.32, -27.34, 0.00042, 3, 75, 9.55, 2.25, 0.93427, 68, 4.17, 16.99, 0.04493, 80, -7.63, -17.9, 0.02079, 4, 75, -2.51, 3.03, 0.6261, 70, -10.3, 0.22, 0.08768, 69, -3.59, 8.53, 0.02954, 68, 12.99, 8.72, 0.25668, 4, 62, -3.44, 5.93, 0.43716, 61, 2.15, -11.73, 0.22421, 60, 23.07, -11.87, 0.17374, 77, -8.04, -6, 0.16489, 4, 62, -15.26, 7, 0.01315, 60, 11.39, -9.81, 0.64165, 66, -20.35, 6.38, 0.00888, 77, 0.75, -13.96, 0.33632, 3, 60, 1.69, -8.29, 0.669, 66, -10.54, 6.39, 0.15605, 77, 8.15, -20.41, 0.17495, 3, 60, -15.55, -9.05, 0.03557, 66, 6.37, 9.83, 0.93312, 77, 23.15, -28.94, 0.03131, 2, 66, 22.72, 14.42, 0.99844, 77, 38.48, -36.23, 0.00156, 1, 66, 33.12, 15.99, 1, 1, 66, 43, 18.03, 1, 1, 66, 59.98, 20.12, 1, 1, 66, 65.03, 27.16, 1, 1, 66, 22.94, -8.92, 1, 1, 66, 36.85, -9.54, 1, 2, 65, 53.09, 7.52, 0.032, 66, 47.02, -9.85, 0.968, 2, 65, 66.17, 5.86, 0.0008, 66, 60.17, -8.98, 0.9992, 1, 66, 66.91, -8.28, 1, 3, 67, -4.81, -0.45, 0.59457, 62, 15.99, 14.92, 0.06503, 61, 21.58, -2.74, 0.34039, 2, 67, 12.83, -5.78, 0.95401, 62, 34.19, 12.03, 0.04599, 2, 67, 30.52, -8.55, 0.97882, 62, 52.1, 11.67, 0.02118, 3, 67, 1.36, 10.56, 0.60041, 63, 21.32, -9.3, 0.19271, 61, 26.21, 9, 0.20687, 3, 67, -9.18, 14.27, 0.14478, 63, 10.38, -7.06, 0.41383, 61, 15.27, 11.24, 0.44139, 4, 74, 18.6, -0.45, 0.99861, 71, 25.72, 6.04, 0.00044, 72, 6.87, 34.31, 0.00091, 73, -0.34, 35.19, 4e-05, 2, 69, 17.24, -4.13, 0.29205, 74, 1.36, 2.13, 0.70795, 5, 69, -1.69, -3.18, 0.25353, 74, -17.6, 2.14, 0.00267, 71, -10.31, 10.4, 0.0863, 72, -20.89, 10.94, 0.01117, 68, 14.23, -3.08, 0.64632, 4, 71, -28.01, 11.97, 0.00174, 72, -34.11, -0.94, 0.00178, 68, -3.35, -0.53, 0.37498, 80, -2.41, 0.44, 0.62149, 3, 71, -41.89, 11.73, 0.00016, 72, -43.4, -11.26, 0.00024, 80, 11.41, 1.68, 0.9996, 2, 80, 24.74, 0.46, 0.00497, 81, 5.29, 2.12, 0.99503, 1, 81, 18.28, 6.07, 1, 1, 59, 50.11, 11, 1, 1, 59, 51.95, -3.53, 1, 1, 59, 56.88, -10.81, 1, 1, 59, 27.19, 11.11, 1, 1, 59, 24.65, -0.53, 1, 1, 59, 23.06, -14.85, 1, 4, 76, -9.72, 21.39, 0.0019, 75, 21.4, 22.6, 0.01862, 80, 13.22, -6.95, 0.81505, 81, -3.52, -8.37, 0.16443, 3, 75, 33.56, 25.92, 0.0004, 80, 25.28, -10.6, 0.13546, 81, 9.08, -8.28, 0.86413, 2, 80, 34.91, -16.99, 0.01796, 81, 20.18, -11.53, 0.98204, 5, 71, -45.83, 0.8, 0.00092, 72, -38.09, -21.6, 0.00126, 68, -21.78, -10.68, 0.00723, 80, 14.56, 12.87, 0.72602, 81, -8.11, 10.96, 0.26457, 2, 80, 27.39, 13.2, 0.20897, 81, 4.04, 15.08, 0.79103, 2, 80, 39.08, 17.8, 0.05272, 81, 13.85, 22.94, 0.94728, 5, 69, 4.18, -15.69, 0.03089, 71, -4.44, -2.1, 0.65905, 72, -7.74, 6.7, 0.2182, 68, 19.39, -15.9, 0.09124, 80, -26.94, 12.77, 0.00062, 3, 72, -0.6, -1.77, 0.98857, 68, 17.44, -26.81, 0.01093, 80, -26.41, 23.84, 0.0005, 2, 72, 11.03, -0.46, 0.91661, 73, -2.82, 0.25, 0.08339, 1, 73, 9.54, -0.71, 1, 1, 73, 14.35, -1.09, 1, 2, 70, 3.85, 0.05, 0.75649, 69, 10.56, 8.36, 0.24351, 4, 74, -4.89, -8.59, 0.12252, 71, 1.86, -0.94, 0.81833, 72, -4.3, 12.1, 0.05869, 73, -15.49, 15.49, 0.00046, 4, 74, 9.7, -10.78, 0.75435, 71, 16.32, -3.85, 0.12455, 72, 7.69, 20.7, 0.11026, 73, -2.1, 21.67, 0.01084, 4, 74, 22.54, -13.23, 0.89902, 71, 29.03, -6.93, 0.02864, 72, 18.61, 27.89, 0.0696, 73, 9.98, 26.67, 0.00274, 3, 70, 19.16, -0.65, 0.2521, 69, 25.86, 7.65, 0.57755, 74, 9.39, 14.32, 0.17035, 3, 70, 37.99, -0.82, 0.09854, 69, 44.7, 7.49, 0.2331, 74, 28.21, 15.09, 0.66836, 4, 70, 42.89, -11.23, 0.02154, 69, 49.59, -2.92, 0.0711, 74, 33.61, 4.93, 0.90644, 72, 13.88, 48.63, 0.00093, 4, 74, 30.29, -14.74, 0.93816, 71, 36.69, -8.82, 0.01138, 72, 25.22, 32.21, 0.05033, 73, 17.28, 29.66, 0.00013, 5, 69, -2.37, -18.29, 0.01602, 71, -10.98, -4.7, 0.30356, 72, -10.3, 0.14, 0.4099, 68, 12.71, -18.13, 0.25503, 80, -20.6, 15.84, 0.01548, 5, 69, -6.61, -12.88, 0.03709, 71, -15.22, 0.71, 0.21698, 72, -17.15, 0.73, 0.18133, 68, 8.78, -12.49, 0.52751, 80, -15.98, 10.75, 0.03709, 5, 71, -28.49, 2.97, 0.03735, 72, -27.85, -7.44, 0.04334, 68, -4.34, -9.49, 0.39836, 80, -2.58, 9.45, 0.51968, 81, -23.47, 2.61, 0.00127], "hull": 136, "edges": [0, 270, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 100, 102, 106, 108, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 152, 154, 154, 156, 156, 158, 158, 160, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 204, 206, 206, 208, 208, 210, 210, 212, 212, 214, 222, 224, 224, 226, 226, 228, 228, 230, 230, 232, 236, 238, 238, 240, 240, 242, 242, 244, 244, 246, 246, 248, 248, 250, 250, 252, 252, 254, 254, 256, 256, 258, 258, 260, 260, 262, 262, 264, 264, 266, 266, 268, 268, 270, 160, 162, 162, 164, 108, 110, 110, 112, 272, 274, 274, 276, 276, 278, 278, 280, 282, 284, 232, 234, 234, 236, 284, 286, 286, 288, 288, 290, 290, 292, 294, 296, 296, 298, 302, 304, 304, 306, 306, 308, 308, 310, 310, 312, 314, 316, 316, 318, 318, 320, 320, 322, 174, 176, 176, 178, 178, 180, 322, 324, 324, 326, 326, 328, 328, 330, 332, 334, 334, 336, 200, 202, 202, 204, 218, 220, 220, 222, 214, 216, 216, 218, 336, 338, 338, 340, 272, 342, 342, 344, 344, 346, 348, 350, 352, 354, 354, 356, 356, 358, 358, 360, 360, 362, 362, 364, 366, 368, 368, 370, 372, 374, 374, 376, 134, 136, 136, 138, 148, 150, 150, 152, 378, 380, 380, 382, 384, 386, 386, 388, 390, 392, 392, 394, 394, 396, 404, 406, 408, 410, 416, 418, 418, 420, 102, 104, 104, 106, 96, 98, 98, 100], "width": 159, "height": 176}}, "kaishijm027": {"kaishijm027": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-209.64, 72.8, 549.77, 390, 947.91, -563.2, 188.49, -880.39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 411, "height": 516}}, "kaishijm028": {"kaishijm028": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [521.64, -409.74, -210.31, -42.3, 261.67, 897.88, 993.62, 530.44], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 409, "height": 526}}, "kaishijm029": {"kaishijm029": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-16.41, -367.51, -184.58, 185.48, 256.48, 319.61, 424.64, -233.39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 289, "height": 230}}, "kaishijm010": {"kaishijm010": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-70.84, 54.19, 90.28, 219.73, 249.36, 64.88, 88.24, -100.65], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 138, "height": 133}}, "shuiliu/liushui_01": {"shuiliu/liushui_01": {"type": "mesh", "uvs": [1, 0.63148, 1, 0.75142, 1, 0.86791, 1, 1, 0.5, 1, 0, 1, 0, 0.86791, 0, 0.75142, 0, 0.63148, 0, 0.5, 0, 0.36391, 0, 0.23935, 0, 0.12402, 0, 0, 0.5, 0, 1, 0, 1, 0.12402, 1, 0.24166, 1, 0.36737, 1, 0.5, 0.5, 0.5, 0.5, 0.2405, 0.5, 0.12402, 0.5, 0.36621, 0.5, 0.75258, 0.5, 0.62917, 0.5, 0.86675], "triangles": [22, 14, 15, 13, 14, 22, 12, 13, 22, 22, 15, 16, 11, 12, 22, 21, 22, 16, 11, 22, 21, 21, 16, 17, 10, 11, 21, 23, 21, 17, 10, 21, 23, 23, 17, 18, 9, 10, 23, 20, 23, 18, 9, 23, 20, 20, 18, 19, 25, 20, 19, 9, 20, 25, 8, 9, 25, 25, 19, 0, 24, 7, 8, 24, 25, 0, 1, 24, 0, 24, 8, 25, 2, 26, 24, 6, 7, 24, 26, 6, 24, 2, 24, 1, 4, 5, 6, 4, 26, 2, 4, 6, 26, 4, 2, 3], "vertices": [-26.06, -80.94, -26.06, -154.78, -24.64, -226.96, -13.73, -310.18, -75.6, -305.91, -134.14, -304.49, -117.55, -225.54, -105.7, -156.68, -96.69, -80.47, -90.06, 0, -87.21, 83.31, -77.57, 168.65, -62.49, 208.46, -4.4, 234.36, 10.62, 237.48, 13.12, 237.2, -8.37, 209.03, -20.84, 165.2, -26.06, 81.65, -26.06, 0, -61.59, 0, -52.37, 166.39, -38.56, 210.33, -58.06, 82.36, -68.01, -153.59, -64.69, -77.62, -70.86, -225.31], "hull": 20, "edges": [6, 8, 8, 10, 26, 28, 28, 30, 22, 42, 42, 34, 22, 24, 24, 26, 28, 44, 44, 42, 24, 44, 30, 32, 32, 34, 44, 32, 18, 20, 20, 22, 46, 42, 20, 46, 34, 36, 36, 38, 46, 36, 14, 48, 48, 2, 14, 16, 16, 18, 46, 50, 50, 48, 16, 50, 2, 0, 0, 38, 50, 0, 10, 12, 12, 14, 8, 52, 52, 48, 12, 52, 2, 4, 4, 6, 52, 4], "width": 64, "height": 64}, "shuiliu/liushui_1": {"type": "<PERSON><PERSON><PERSON>", "path": "shuiliu/liushui_01", "parent": "shuiliu/liushui_01", "width": 64, "height": 64}, "shuiliu/liushui_02": {"type": "<PERSON><PERSON><PERSON>", "parent": "shuiliu/liushui_01", "width": 64, "height": 64}, "shuiliu/liushui_03": {"type": "<PERSON><PERSON><PERSON>", "parent": "shuiliu/liushui_01", "width": 64, "height": 64}, "shuiliu/liushui_04": {"type": "<PERSON><PERSON><PERSON>", "parent": "shuiliu/liushui_01", "width": 64, "height": 64}, "shuiliu/liushui_05": {"type": "<PERSON><PERSON><PERSON>", "parent": "shuiliu/liushui_01", "width": 64, "height": 64}, "shuiliu/liushui_06": {"type": "<PERSON><PERSON><PERSON>", "parent": "shuiliu/liushui_01", "width": 64, "height": 64}, "shuiliu/liushui_07": {"type": "<PERSON><PERSON><PERSON>", "parent": "shuiliu/liushui_01", "width": 64, "height": 64}, "shuiliu/liushui_08": {"type": "<PERSON><PERSON><PERSON>", "parent": "shuiliu/liushui_01", "width": 64, "height": 64}, "shuiliu/liushui_09": {"type": "<PERSON><PERSON><PERSON>", "parent": "shuiliu/liushui_01", "width": 64, "height": 64}, "shuiliu/liushui_10": {"type": "<PERSON><PERSON><PERSON>", "parent": "shuiliu/liushui_01", "width": 64, "height": 64}, "shuiliu/liushui_11": {"type": "<PERSON><PERSON><PERSON>", "parent": "shuiliu/liushui_01", "width": 64, "height": 64}, "shuiliu/liushui_12": {"type": "<PERSON><PERSON><PERSON>", "parent": "shuiliu/liushui_01", "width": 64, "height": 64}, "shuiliu/liushui_13": {"type": "<PERSON><PERSON><PERSON>", "parent": "shuiliu/liushui_01", "width": 64, "height": 64}, "shuiliu/liushui_14": {"type": "<PERSON><PERSON><PERSON>", "parent": "shuiliu/liushui_01", "width": 64, "height": 64}, "shuiliu/liushui_15": {"type": "<PERSON><PERSON><PERSON>", "parent": "shuiliu/liushui_01", "width": 64, "height": 64}, "shuiliu/liushui_16": {"type": "<PERSON><PERSON><PERSON>", "parent": "shuiliu/liushui_01", "width": 64, "height": 64}}}}], "animations": {"game_loading_01": {"slots": {"kaishijm023_3": {"color": [{"color": "ffffff98", "curve": 0.38, "c2": 0.53, "c3": 0.744}, {"time": 0.5667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.9, "color": "ffffff00", "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 2.6667, "color": "ffffff98", "curve": 0.38, "c2": 0.53, "c3": 0.744}, {"time": 3.2333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4.5667, "color": "ffffff00", "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 5.3333, "color": "ffffff98"}]}, "kaishijm027": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffff00"}]}, "kaishijm020": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffff00"}]}, "kaishijm032": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffff00"}]}, "kaishijm013": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffff00"}]}, "kaishijm11": {"color": [{"color": "ffffffcb", "curve": 0.234, "c2": 0.57, "c3": 0.526}, {"time": 0.8667, "color": "ffffffff", "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 1.2, "color": "ffffffee", "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 3.5333, "color": "ffffff00", "curve": 0.603, "c2": 0.01, "c3": 0.732, "c4": 0.61}, {"time": 5.3333, "color": "ffffffcb"}]}, "kaishijm20": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffffff"}]}, "kaishijm29": {"color": [{"color": "ffffff98", "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 1.5333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 4.2, "color": "ffffffff", "curve": 0.256, "c3": 0.62, "c4": 0.47}, {"time": 5.3333, "color": "ffffff98"}]}, "kaishijm029": {"color": [{"color": "ffffff66", "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 1.5333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4.2, "color": "ffffff00", "curve": 0.256, "c3": 0.62, "c4": 0.47}, {"time": 5.3333, "color": "ffffff66"}]}, "kaishijm023_1": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffff00"}]}, "kaishijm6": {"color": [{"color": "ffffffa1", "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.1333, "color": "ffffffff", "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "color": "ffffffa1", "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.4667, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.8, "color": "ffffffff", "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.3333, "color": "ffffffa1"}]}, "kaishijm2": {"color": [{"color": "ffffff9c", "curve": 0.381, "c2": 0.54, "c3": 0.743}, {"time": 1.1, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.7667, "color": "ffffff00", "curve": 0.246, "c3": 0.635, "c4": 0.55}, {"time": 5.3333, "color": "ffffff9c"}]}, "kaishijm036": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffff00"}]}, "kaishijm04": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffff00"}]}, "kaishijm016": {"color": [{"color": "ffffff77", "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 1.2667, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 3.9333, "color": "ffffffff", "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 5.3333, "color": "ffffff77"}]}, "kaishijm02": {"color": [{"color": "ffffff62", "curve": 0.381, "c2": 0.54, "c3": 0.743}, {"time": 1.1, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 3.7667, "color": "ffffffff", "curve": 0.246, "c3": 0.635, "c4": 0.55}, {"time": 5.3333, "color": "ffffff62"}]}, "kaishijm27": {"color": [{"color": "fffffffd", "curve": 0.255, "c2": 0.02, "c3": 0.751}, {"time": 2.6333, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.3, "color": "ffffffff", "curve": 0.329, "c3": 0.663, "c4": 0.34}, {"time": 5.3333, "color": "fffffffd"}]}, "kaishijm023_2": {"color": [{"color": "ffffffb9", "curve": 0.35, "c2": 0.39, "c3": 0.757}, {"time": 0.9, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.2333, "color": "ffffffff", "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "color": "ffffffb9", "curve": 0.35, "c2": 0.39, "c3": 0.757}, {"time": 3.5667, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.9, "color": "ffffffff", "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 5.3333, "color": "ffffffb9"}]}, "kaishijm17": {"color": [{"color": "ffffffc1", "curve": 0.23, "c2": 0.57, "c3": 0.515}, {"time": 0.9333, "color": "ffffffff", "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 1.2667, "color": "ffffffee", "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 3.6, "color": "ffffff00", "curve": 0.598, "c2": 0.01, "c3": 0.743, "c4": 0.59}, {"time": 5.3333, "color": "ffffffc1"}]}, "kaishijm5": {"color": [{"color": "ffffffa1", "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.1333, "color": "ffffffff", "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "color": "ffffffa1", "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.4667, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.8, "color": "ffffffff", "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.3333, "color": "ffffffa1"}]}, "kaishijm36": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffffff"}]}, "kaishijm32": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffffff"}]}, "kaishijm8": {"color": [{"color": "ffffff6a", "curve": 0.24, "c2": 0.49, "c3": 0.451}, {"time": 0.7, "color": "ffffffff", "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.2333, "color": "ffffffa1", "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.0333, "color": "ffffff00", "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 2.6667, "color": "ffffff6a", "curve": 0.24, "c2": 0.49, "c3": 0.451}, {"time": 3.3667, "color": "ffffffff", "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.9, "color": "ffffffa1", "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.7, "color": "ffffff00", "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 5.3333, "color": "ffffff6a"}]}, "kaishijm031": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffff00"}]}, "kaishijm025": {"color": [{"color": "ffffffb3", "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffffb3", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffffb3"}]}, "kaishijm06": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffff00"}]}, "kaishijm18": {"color": [{"color": "ffffff1b", "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.4667, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 3.1333, "color": "ffffffff", "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 3.4667, "color": "ffffffee", "curve": 0.287, "c2": 0.17, "c3": 0.697, "c4": 0.77}, {"time": 5.3333, "color": "ffffff1b"}]}, "kaishijm13": {"color": [{"color": "ffffffee", "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 2.3333, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5, "color": "ffffffff", "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 5.3333, "color": "ffffffee"}]}, "kaishijm7": {"color": [{"color": "ffffff6a", "curve": 0.24, "c2": 0.49, "c3": 0.451}, {"time": 0.7, "color": "ffffffff", "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.2333, "color": "ffffffa1", "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.0333, "color": "ffffff00", "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 2.6667, "color": "ffffff6a", "curve": 0.24, "c2": 0.49, "c3": 0.451}, {"time": 3.3667, "color": "ffffffff", "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.9, "color": "ffffffa1", "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.7, "color": "ffffff00", "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 5.3333, "color": "ffffff6a"}]}, "kaishijm28": {"color": [{"color": "ffffff83", "curve": 0.376, "c2": 0.51, "c3": 0.749}, {"time": 1.3, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "color": "ffffff00", "curve": 0.249, "c3": 0.626, "c4": 0.51}, {"time": 5.3333, "color": "ffffff83"}]}, "kaishijm9": {"color": [{"color": "ffffff6b", "curve": 0.368, "c2": 0.47, "c3": 0.753}, {"time": 0.7667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.1, "color": "ffffff00", "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 2.6667, "color": "ffffff6b", "curve": 0.368, "c2": 0.47, "c3": 0.753}, {"time": 3.4333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4.7667, "color": "ffffff00", "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 5.3333, "color": "ffffff6b"}]}, "kaishijm019": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffff00"}]}, "kaishijm033": {"color": [{"color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.8667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.9, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.6667, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 3.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 4.5667, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.3333, "color": "ffffff00"}]}, "kaishijm4": {"color": [{"color": "ffffffa1", "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.1333, "color": "ffffffff", "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "color": "ffffffa1", "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.4667, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.8, "color": "ffffffff", "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.3333, "color": "ffffffa1"}]}, "kaishijm30": {"color": [{"color": "ffffffc1", "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.8, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "color": "ffffff00", "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 5.3333, "color": "ffffffc1"}]}, "kaishijm10": {"color": [{"color": "fffffffd", "curve": 0.337, "c2": 0.66, "c3": 0.671}, {"time": 0.0333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.7, "color": "ffffff00", "curve": 0.249, "c3": 0.745, "c4": 0.98}, {"time": 5.3333, "color": "fffffffd"}]}, "kaishijm16": {"color": [{"color": "ffffffc1", "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.8, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "color": "ffffff00", "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 5.3333, "color": "ffffffc1"}]}, "kaishijm011": {"color": [{"color": "ffffff6e", "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 1.2, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 3.8667, "color": "ffffffff", "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 5.3333, "color": "ffffff6e"}]}, "kaishijm19": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffffff"}]}, "shuiliu/liushui_01": {"attachment": [{"name": "shuiliu/liushui_1"}, {"time": 0.1, "name": "shuiliu/liushui_02"}, {"time": 0.1667, "name": "shuiliu/liushui_03"}, {"time": 0.2667, "name": "shuiliu/liushui_04"}, {"time": 0.3333, "name": "shuiliu/liushui_05"}, {"time": 0.4333, "name": "shuiliu/liushui_06"}, {"time": 0.5333, "name": "shuiliu/liushui_07"}, {"time": 0.6, "name": "shuiliu/liushui_08"}, {"time": 0.7, "name": "shuiliu/liushui_09"}, {"time": 0.7667, "name": "shuili<PERSON>/liushui_10"}, {"time": 0.8667, "name": "shui<PERSON><PERSON>/liushui_11"}, {"time": 0.9333, "name": "shuili<PERSON>/liushui_12"}, {"time": 1.0333, "name": "shui<PERSON><PERSON>/liushui_13"}, {"time": 1.1333, "name": "shui<PERSON><PERSON>/liushui_14"}, {"time": 1.2, "name": "shui<PERSON><PERSON>/liushui_15"}, {"time": 1.3, "name": "shui<PERSON><PERSON>/liushui_16"}, {"time": 1.3667, "name": "shuiliu/liushui_1"}, {"time": 1.4667, "name": "shuiliu/liushui_02"}, {"time": 1.5667, "name": "shuiliu/liushui_03"}, {"time": 1.6333, "name": "shuiliu/liushui_04"}, {"time": 1.7333, "name": "shuiliu/liushui_05"}, {"time": 1.8, "name": "shuiliu/liushui_06"}, {"time": 1.9, "name": "shuiliu/liushui_07"}, {"time": 1.9667, "name": "shuiliu/liushui_08"}, {"time": 2.0667, "name": "shuiliu/liushui_09"}, {"time": 2.1667, "name": "shuili<PERSON>/liushui_10"}, {"time": 2.2333, "name": "shui<PERSON><PERSON>/liushui_11"}, {"time": 2.3333, "name": "shuili<PERSON>/liushui_12"}, {"time": 2.4, "name": "shui<PERSON><PERSON>/liushui_13"}, {"time": 2.5, "name": "shui<PERSON><PERSON>/liushui_14"}, {"time": 2.6, "name": "shui<PERSON><PERSON>/liushui_15"}, {"time": 2.6667, "name": "shuiliu/liushui_1"}, {"time": 2.7667, "name": "shuiliu/liushui_02"}, {"time": 2.8333, "name": "shuiliu/liushui_03"}, {"time": 2.9333, "name": "shuiliu/liushui_04"}, {"time": 3, "name": "shuiliu/liushui_05"}, {"time": 3.1, "name": "shuiliu/liushui_06"}, {"time": 3.2, "name": "shuiliu/liushui_07"}, {"time": 3.2667, "name": "shuiliu/liushui_08"}, {"time": 3.3667, "name": "shuiliu/liushui_09"}, {"time": 3.4333, "name": "shuili<PERSON>/liushui_10"}, {"time": 3.5333, "name": "shui<PERSON><PERSON>/liushui_11"}, {"time": 3.6, "name": "shuili<PERSON>/liushui_12"}, {"time": 3.7, "name": "shui<PERSON><PERSON>/liushui_13"}, {"time": 3.8, "name": "shui<PERSON><PERSON>/liushui_14"}, {"time": 3.8667, "name": "shui<PERSON><PERSON>/liushui_15"}, {"time": 3.9667, "name": "shui<PERSON><PERSON>/liushui_16"}, {"time": 4.0333, "name": "shuiliu/liushui_1"}, {"time": 4.1333, "name": "shuiliu/liushui_02"}, {"time": 4.2333, "name": "shuiliu/liushui_03"}, {"time": 4.3, "name": "shuiliu/liushui_04"}, {"time": 4.4, "name": "shuiliu/liushui_05"}, {"time": 4.4667, "name": "shuiliu/liushui_06"}, {"time": 4.5667, "name": "shuiliu/liushui_07"}, {"time": 4.6333, "name": "shuiliu/liushui_08"}, {"time": 4.7333, "name": "shuiliu/liushui_09"}, {"time": 4.8333, "name": "shuili<PERSON>/liushui_10"}, {"time": 4.9, "name": "shui<PERSON><PERSON>/liushui_11"}, {"time": 5, "name": "shuili<PERSON>/liushui_12"}, {"time": 5.0667, "name": "shui<PERSON><PERSON>/liushui_13"}, {"time": 5.1667, "name": "shui<PERSON><PERSON>/liushui_14"}, {"time": 5.2667, "name": "shui<PERSON><PERSON>/liushui_15"}, {"time": 5.3333, "name": "shui<PERSON><PERSON>/liushui_16"}]}, "kaishijm12": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffffff"}]}, "kaishijm028": {"color": [{"color": "ffffff7b", "curve": 0.376, "c2": 0.51, "c3": 0.749}, {"time": 1.3, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 3.9667, "color": "ffffffff", "curve": 0.249, "c3": 0.626, "c4": 0.51}, {"time": 5.3333, "color": "ffffff7b"}]}, "light": {"color": [{"color": "fffac700", "curve": "stepped"}, {"time": 1.3333, "color": "fffac700", "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "color": "fffac76e", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "fffac700"}]}, "kaishijm07": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffff00"}]}, "kaishijm030": {"color": [{"color": "ffffff3d", "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.8, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 3.4667, "color": "ffffffff", "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 5.3333, "color": "ffffff3d"}]}, "kaishijm31": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffffff"}]}}, "bones": {"shui2": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6333, "x": 1.476, "y": 1.476, "curve": "stepped"}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "x": 1.476, "y": 1.476, "curve": "stepped"}, {"time": 5.3333}]}, "shui4": {"translate": [{"x": -0.15, "y": 2.61, "curve": 0.234, "c2": 0.58, "c3": 0.528}, {"time": 0.4333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.7667, "x": -0.77, "y": 13.48, "curve": 0.604, "c2": 0.01, "c3": 0.729, "c4": 0.61}, {"time": 2.6667, "x": -0.15, "y": 2.61, "curve": 0.234, "c2": 0.58, "c3": 0.528}, {"time": 3.1, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.4333, "x": -0.77, "y": 13.48, "curve": 0.604, "c2": 0.01, "c3": 0.729, "c4": 0.61}, {"time": 5.3333, "x": -0.15, "y": 2.61}]}, "shui3": {"translate": [{"curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.3333, "x": 2.97, "y": 11.47, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.6667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4, "x": 2.97, "y": 11.47, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.3333}]}, "shui5": {"translate": [{"x": 7.44, "y": 5.14, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 0.8, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.1333, "x": 10.15, "y": 7.01, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 2.6667, "x": 7.44, "y": 5.14, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 3.4667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.8, "x": 10.15, "y": 7.01, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 5.3333, "x": 7.44, "y": 5.14}]}, "shui6": {"translate": [{"x": 2.5, "y": 9.65, "curve": 0.309, "c2": 0.39, "c3": 0.422}, {"time": 0.9, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.2667, "x": 2.97, "y": 11.47, "curve": 0.474, "c2": 0.01, "c3": 0.771, "c4": 0.43}, {"time": 2.6667, "x": 2.5, "y": 9.65, "curve": 0.309, "c2": 0.39, "c3": 0.422}, {"time": 3.5667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.9333, "x": 2.97, "y": 11.47, "curve": 0.474, "c2": 0.01, "c3": 0.771, "c4": 0.43}, {"time": 5.3333, "x": 2.5, "y": 9.65}]}, "shui7": {"translate": [{"x": 2.43, "y": 2.94, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 0.8, "x": 9.1, "y": 10.99, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.1333, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 2.6667, "x": 2.43, "y": 2.94, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 3.4667, "x": 9.1, "y": 10.99, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.8, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 5.3333, "x": 2.43, "y": 2.94}]}, "shui8": {"translate": [{"x": 1.24, "y": 4.77, "curve": 0.24, "c2": 0.49, "c3": 0.451}, {"time": 0.7, "x": 2.97, "y": 11.47, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.0333, "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 2.6667, "x": 1.24, "y": 4.77, "curve": 0.24, "c2": 0.49, "c3": 0.451}, {"time": 3.3667, "x": 2.97, "y": 11.47, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.7, "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 5.3333, "x": 1.24, "y": 4.77}]}, "shui9": {"scale": [{"x": 1.369, "y": 1.369, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.7667, "x": 1.476, "y": 1.476, "curve": "stepped"}, {"time": 0.8, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 2.6667, "x": 1.369, "y": 1.369, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 3.4333, "x": 1.476, "y": 1.476, "curve": "stepped"}, {"time": 3.4667, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 5.3333, "x": 1.369, "y": 1.369}]}, "shui10": {"translate": [{"x": 2.18, "y": 8.4, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 0.8, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.1333, "x": 2.97, "y": 11.47, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 2.6667, "x": 2.18, "y": 8.4, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 3.4667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.8, "x": 2.97, "y": 11.47, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 5.3333, "x": 2.18, "y": 8.4}]}, "shui11": {"translate": [{"x": -0.77, "y": 13.33, "curve": 0.548, "c2": 0.12, "c3": 0.704, "c4": 0.65}, {"time": 0.8, "x": -0.15, "y": 2.61, "curve": 0.234, "c2": 0.58, "c3": 0.528}, {"time": 1.2333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.5667, "x": -0.77, "y": 13.48, "curve": 0.366, "c2": 0.03, "c3": 0.697, "c4": 0.38}, {"time": 2.6667, "x": -0.77, "y": 13.33, "curve": 0.548, "c2": 0.12, "c3": 0.704, "c4": 0.65}, {"time": 3.4667, "x": -0.15, "y": 2.61, "curve": 0.234, "c2": 0.58, "c3": 0.528}, {"time": 3.9, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.2333, "x": -0.77, "y": 13.48, "curve": 0.366, "c2": 0.03, "c3": 0.697, "c4": 0.38}, {"time": 5.3333, "x": -0.77, "y": 13.33}]}, "shui12": {"translate": [{"x": 9.44, "y": 6.52, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 0.2667, "x": 10.15, "y": 7.01, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 0.8, "x": 7.44, "y": 5.14, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 1.6, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 2.6667, "x": 9.44, "y": 6.52, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 2.9333, "x": 10.15, "y": 7.01, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 3.4667, "x": 7.44, "y": 5.14, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 4.2667, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 5.3333, "x": 9.44, "y": 6.52}]}, "shui13": {"translate": [{"x": 2.54, "y": 9.82, "curve": 0.242, "c2": 0.59, "c3": 0.545}, {"time": 0.3667, "x": 2.97, "y": 11.47, "curve": 0.474, "c2": 0.01, "c3": 0.771, "c4": 0.43}, {"time": 0.8, "x": 2.5, "y": 9.65, "curve": 0.309, "c2": 0.39, "c3": 0.422}, {"time": 1.7, "curve": 0.609, "c2": 0.01, "c3": 0.707, "c4": 0.64}, {"time": 2.6667, "x": 2.54, "y": 9.82, "curve": 0.242, "c2": 0.59, "c3": 0.545}, {"time": 3.0333, "x": 2.97, "y": 11.47, "curve": 0.474, "c2": 0.01, "c3": 0.771, "c4": 0.43}, {"time": 3.4667, "x": 2.5, "y": 9.65, "curve": 0.309, "c2": 0.39, "c3": 0.422}, {"time": 4.3667, "curve": 0.609, "c2": 0.01, "c3": 0.707, "c4": 0.64}, {"time": 5.3333, "x": 2.54, "y": 9.82}]}, "shui14": {"translate": [{"x": 0.64, "y": 0.77, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 0.2667, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 0.8, "x": 2.43, "y": 2.94, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 1.6, "x": 9.1, "y": 10.99, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 2.6667, "x": 0.64, "y": 0.77, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 2.9333, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 3.4667, "x": 2.43, "y": 2.94, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 4.2667, "x": 9.1, "y": 10.99, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 5.3333, "x": 0.64, "y": 0.77}]}, "shui15": {"translate": [{"x": 0.07, "y": 0.28, "curve": 0.288, "c2": 0.64, "c3": 0.617}, {"time": 0.1667, "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 0.8, "x": 1.24, "y": 4.77, "curve": 0.24, "c2": 0.49, "c3": 0.451}, {"time": 1.5, "x": 2.97, "y": 11.47, "curve": 0.605, "c2": 0.01, "c3": 0.589, "c4": 0.81}, {"time": 2.6667, "x": 0.07, "y": 0.28, "curve": 0.288, "c2": 0.64, "c3": 0.617}, {"time": 2.8333, "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 3.4667, "x": 1.24, "y": 4.77, "curve": 0.24, "c2": 0.49, "c3": 0.451}, {"time": 4.1667, "x": 2.97, "y": 11.47, "curve": 0.605, "c2": 0.01, "c3": 0.589, "c4": 0.81}, {"time": 5.3333, "x": 0.07, "y": 0.28}]}, "shui16": {"scale": [{"x": 1.369, "y": 1.369, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.7667, "x": 1.476, "y": 1.476, "curve": "stepped"}, {"time": 0.8, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 2.6667, "x": 1.369, "y": 1.369, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 3.4333, "x": 1.476, "y": 1.476, "curve": "stepped"}, {"time": 3.4667, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 5.3333, "x": 1.369, "y": 1.369}]}, "shui17": {"translate": [{"x": 2.18, "y": 8.4, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 0.8, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.1333, "x": 2.97, "y": 11.47, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 2.6667, "x": 2.18, "y": 8.4, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 3.4667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.8, "x": 2.97, "y": 11.47, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 5.3333, "x": 2.18, "y": 8.4}]}, "shui18": {"translate": [{"x": -0.77, "y": 13.33, "curve": 0.548, "c2": 0.12, "c3": 0.704, "c4": 0.65}, {"time": 0.8, "x": -0.15, "y": 2.61, "curve": 0.234, "c2": 0.58, "c3": 0.528}, {"time": 1.2333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.5667, "x": -0.77, "y": 13.48, "curve": 0.366, "c2": 0.03, "c3": 0.697, "c4": 0.38}, {"time": 2.6667, "x": -0.77, "y": 13.33, "curve": 0.548, "c2": 0.12, "c3": 0.704, "c4": 0.65}, {"time": 3.4667, "x": -0.15, "y": 2.61, "curve": 0.234, "c2": 0.58, "c3": 0.528}, {"time": 3.9, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.2333, "x": -0.77, "y": 13.48, "curve": 0.366, "c2": 0.03, "c3": 0.697, "c4": 0.38}, {"time": 5.3333, "x": -0.77, "y": 13.33}]}, "shui19": {"translate": [{"x": 9.44, "y": 6.52, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 0.2667, "x": 10.15, "y": 7.01, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 0.8, "x": 7.44, "y": 5.14, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 1.6, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 2.6667, "x": 9.44, "y": 6.52, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 2.9333, "x": 10.15, "y": 7.01, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 3.4667, "x": 7.44, "y": 5.14, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 4.2667, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 5.3333, "x": 9.44, "y": 6.52}]}, "shui20": {"translate": [{"x": 2.54, "y": 9.82, "curve": 0.242, "c2": 0.59, "c3": 0.545}, {"time": 0.3667, "x": 2.97, "y": 11.47, "curve": 0.474, "c2": 0.01, "c3": 0.771, "c4": 0.43}, {"time": 0.8, "x": 2.5, "y": 9.65, "curve": 0.309, "c2": 0.39, "c3": 0.422}, {"time": 1.7, "curve": 0.609, "c2": 0.01, "c3": 0.707, "c4": 0.64}, {"time": 2.6667, "x": 2.54, "y": 9.82, "curve": 0.242, "c2": 0.59, "c3": 0.545}, {"time": 3.0333, "x": 2.97, "y": 11.47, "curve": 0.474, "c2": 0.01, "c3": 0.771, "c4": 0.43}, {"time": 3.4667, "x": 2.5, "y": 9.65, "curve": 0.309, "c2": 0.39, "c3": 0.422}, {"time": 4.3667, "curve": 0.609, "c2": 0.01, "c3": 0.707, "c4": 0.64}, {"time": 5.3333, "x": 2.54, "y": 9.82}]}, "shui21": {"translate": [{"x": 0.64, "y": 0.77, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 0.2667, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 0.8, "x": 2.43, "y": 2.94, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 1.6, "x": 9.1, "y": 10.99, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 2.6667, "x": 0.64, "y": 0.77, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 2.9333, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 3.4667, "x": 2.43, "y": 2.94, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 4.2667, "x": 9.1, "y": 10.99, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 5.3333, "x": 0.64, "y": 0.77}]}, "shui22": {"translate": [{"x": 0.07, "y": 0.28, "curve": 0.288, "c2": 0.64, "c3": 0.617}, {"time": 0.1667, "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 0.8, "x": 1.24, "y": 4.77, "curve": 0.24, "c2": 0.49, "c3": 0.451}, {"time": 1.5, "x": 2.97, "y": 11.47, "curve": 0.605, "c2": 0.01, "c3": 0.589, "c4": 0.81}, {"time": 2.6667, "x": 0.07, "y": 0.28, "curve": 0.288, "c2": 0.64, "c3": 0.617}, {"time": 2.8333, "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 3.4667, "x": 1.24, "y": 4.77, "curve": 0.24, "c2": 0.49, "c3": 0.451}, {"time": 4.1667, "x": 2.97, "y": 11.47, "curve": 0.605, "c2": 0.01, "c3": 0.589, "c4": 0.81}, {"time": 5.3333, "x": 0.07, "y": 0.28}]}, "shui23": {"translate": [{"x": 2.18, "y": 8.4, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 0.8, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.1333, "x": 2.97, "y": 11.47, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 2.6667, "x": 2.18, "y": 8.4, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 3.4667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.8, "x": 2.97, "y": 11.47, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 5.3333, "x": 2.18, "y": 8.4}]}, "shui24": {"translate": [{"x": -0.77, "y": 13.33, "curve": 0.548, "c2": 0.12, "c3": 0.704, "c4": 0.65}, {"time": 0.8, "x": -0.15, "y": 2.61, "curve": 0.234, "c2": 0.58, "c3": 0.528}, {"time": 1.2333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.5667, "x": -0.77, "y": 13.48, "curve": 0.366, "c2": 0.03, "c3": 0.697, "c4": 0.38}, {"time": 2.6667, "x": -0.77, "y": 13.33, "curve": 0.548, "c2": 0.12, "c3": 0.704, "c4": 0.65}, {"time": 3.4667, "x": -0.15, "y": 2.61, "curve": 0.234, "c2": 0.58, "c3": 0.528}, {"time": 3.9, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.2333, "x": -0.77, "y": 13.48, "curve": 0.366, "c2": 0.03, "c3": 0.697, "c4": 0.38}, {"time": 5.3333, "x": -0.77, "y": 13.33}]}, "shui25": {"translate": [{"x": 9.44, "y": 6.52, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 0.2667, "x": 10.15, "y": 7.01, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 0.8, "x": 7.44, "y": 5.14, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 1.6, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 2.6667, "x": 9.44, "y": 6.52, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 2.9333, "x": 10.15, "y": 7.01, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 3.4667, "x": 7.44, "y": 5.14, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 4.2667, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 5.3333, "x": 9.44, "y": 6.52}]}, "shui26": {"translate": [{"x": 2.54, "y": 9.82, "curve": 0.242, "c2": 0.59, "c3": 0.545}, {"time": 0.3667, "x": 2.97, "y": 11.47, "curve": 0.474, "c2": 0.01, "c3": 0.771, "c4": 0.43}, {"time": 0.8, "x": 2.5, "y": 9.65, "curve": 0.309, "c2": 0.39, "c3": 0.422}, {"time": 1.7, "curve": 0.609, "c2": 0.01, "c3": 0.707, "c4": 0.64}, {"time": 2.6667, "x": 2.54, "y": 9.82, "curve": 0.242, "c2": 0.59, "c3": 0.545}, {"time": 3.0333, "x": 2.97, "y": 11.47, "curve": 0.474, "c2": 0.01, "c3": 0.771, "c4": 0.43}, {"time": 3.4667, "x": 2.5, "y": 9.65, "curve": 0.309, "c2": 0.39, "c3": 0.422}, {"time": 4.3667, "curve": 0.609, "c2": 0.01, "c3": 0.707, "c4": 0.64}, {"time": 5.3333, "x": 2.54, "y": 9.82}]}, "shui27": {"translate": [{"x": 0.64, "y": 0.77, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 0.2667, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 0.8, "x": 2.43, "y": 2.94, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 1.6, "x": 9.1, "y": 10.99, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 2.6667, "x": 0.64, "y": 0.77, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 2.9333, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 3.4667, "x": 2.43, "y": 2.94, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 4.2667, "x": 9.1, "y": 10.99, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 5.3333, "x": 0.64, "y": 0.77}]}, "shui28": {"translate": [{"x": 0.07, "y": 0.28, "curve": 0.288, "c2": 0.64, "c3": 0.617}, {"time": 0.1667, "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 0.8, "x": 1.24, "y": 4.77, "curve": 0.24, "c2": 0.49, "c3": 0.451}, {"time": 1.5, "x": 2.97, "y": 11.47, "curve": 0.605, "c2": 0.01, "c3": 0.589, "c4": 0.81}, {"time": 2.6667, "x": 0.07, "y": 0.28, "curve": 0.288, "c2": 0.64, "c3": 0.617}, {"time": 2.8333, "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 3.4667, "x": 1.24, "y": 4.77, "curve": 0.24, "c2": 0.49, "c3": 0.451}, {"time": 4.1667, "x": 2.97, "y": 11.47, "curve": 0.605, "c2": 0.01, "c3": 0.589, "c4": 0.81}, {"time": 5.3333, "x": 0.07, "y": 0.28}]}, "shui29": {"scale": [{"x": 1.085, "y": 1.085, "curve": 0.312, "c2": 0.27, "c3": 0.679, "c4": 0.71}, {"time": 1.2333, "x": 1.369, "y": 1.369, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 2, "x": 1.476, "y": 1.476, "curve": "stepped"}, {"time": 2.0333, "curve": 0.277, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": 1.085, "y": 1.085, "curve": 0.312, "c2": 0.27, "c3": 0.679, "c4": 0.71}, {"time": 3.9, "x": 1.369, "y": 1.369, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 4.6667, "x": 1.476, "y": 1.476, "curve": "stepped"}, {"time": 4.7, "curve": 0.277, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "x": 1.085, "y": 1.085}]}, "shui30": {"translate": [{"x": 1.24, "y": 4.77, "curve": 0.24, "c2": 0.49, "c3": 0.451}, {"time": 0.7, "x": 2.97, "y": 11.47, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 1.2333, "x": 2.18, "y": 8.4, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 2.0333, "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 2.6667, "x": 1.24, "y": 4.77, "curve": 0.24, "c2": 0.49, "c3": 0.451}, {"time": 3.3667, "x": 2.97, "y": 11.47, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 3.9, "x": 2.18, "y": 8.4, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 4.7, "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 5.3333, "x": 1.24, "y": 4.77}]}, "shui31": {"translate": [{"x": -0.03, "y": 0.45, "curve": 0.429, "c2": 0.25, "c3": 0.433}, {"time": 1.1333, "x": -0.77, "y": 13.48, "curve": 0.366, "c2": 0.03, "c3": 0.697, "c4": 0.38}, {"time": 1.2333, "x": -0.77, "y": 13.33, "curve": 0.548, "c2": 0.12, "c3": 0.704, "c4": 0.65}, {"time": 2.0333, "x": -0.15, "y": 2.61, "curve": 0.234, "c2": 0.58, "c3": 0.528}, {"time": 2.4667, "curve": 0.4, "c2": 0.01, "c3": 0.726, "c4": 0.39}, {"time": 2.6667, "x": -0.03, "y": 0.45, "curve": 0.429, "c2": 0.25, "c3": 0.433}, {"time": 3.8, "x": -0.77, "y": 13.48, "curve": 0.366, "c2": 0.03, "c3": 0.697, "c4": 0.38}, {"time": 3.9, "x": -0.77, "y": 13.33, "curve": 0.548, "c2": 0.12, "c3": 0.704, "c4": 0.65}, {"time": 4.7, "x": -0.15, "y": 2.61, "curve": 0.234, "c2": 0.58, "c3": 0.528}, {"time": 5.1333, "curve": 0.4, "c2": 0.01, "c3": 0.726, "c4": 0.39}, {"time": 5.3333, "x": -0.03, "y": 0.45}]}, "shui32": {"translate": [{"x": 0.22, "y": 0.15, "curve": 0.288, "c2": 0.64, "c3": 0.617}, {"time": 0.1667, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 1.2333, "x": 9.44, "y": 6.52, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 1.5, "x": 10.15, "y": 7.01, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 2.0333, "x": 7.44, "y": 5.14, "curve": 0.304, "c2": 0.38, "c3": 0.515, "c4": 0.83}, {"time": 2.6667, "x": 0.22, "y": 0.15, "curve": 0.288, "c2": 0.64, "c3": 0.617}, {"time": 2.8333, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 3.9, "x": 9.44, "y": 6.52, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 4.1667, "x": 10.15, "y": 7.01, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 4.7, "x": 7.44, "y": 5.14, "curve": 0.304, "c2": 0.38, "c3": 0.515, "c4": 0.83}, {"time": 5.3333, "x": 0.22, "y": 0.15}]}, "shui33": {"translate": [{"x": 0.16, "y": 0.63, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 0.2667, "curve": 0.609, "c2": 0.01, "c3": 0.707, "c4": 0.64}, {"time": 1.2333, "x": 2.54, "y": 9.82, "curve": 0.242, "c2": 0.59, "c3": 0.545}, {"time": 1.6, "x": 2.97, "y": 11.47, "curve": 0.474, "c2": 0.01, "c3": 0.771, "c4": 0.43}, {"time": 2.0333, "x": 2.5, "y": 9.65, "curve": 0.367, "c2": 0.32, "c3": 0.57, "c4": 0.77}, {"time": 2.6667, "x": 0.16, "y": 0.63, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 2.9333, "curve": 0.609, "c2": 0.01, "c3": 0.707, "c4": 0.64}, {"time": 3.9, "x": 2.54, "y": 9.82, "curve": 0.242, "c2": 0.59, "c3": 0.545}, {"time": 4.2667, "x": 2.97, "y": 11.47, "curve": 0.474, "c2": 0.01, "c3": 0.771, "c4": 0.43}, {"time": 4.7, "x": 2.5, "y": 9.65, "curve": 0.367, "c2": 0.32, "c3": 0.57, "c4": 0.77}, {"time": 5.3333, "x": 0.16, "y": 0.63}]}, "shui34": {"translate": [{"x": 8.9, "y": 10.76, "curve": 0.288, "c2": 0.64, "c3": 0.617}, {"time": 0.1667, "x": 9.1, "y": 10.99, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 1.2333, "x": 0.64, "y": 0.77, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 1.5, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 2.0333, "x": 2.43, "y": 2.94, "curve": 0.304, "c2": 0.38, "c3": 0.515, "c4": 0.83}, {"time": 2.6667, "x": 8.9, "y": 10.76, "curve": 0.288, "c2": 0.64, "c3": 0.617}, {"time": 2.8333, "x": 9.1, "y": 10.99, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 3.9, "x": 0.64, "y": 0.77, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 4.1667, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 4.7, "x": 2.43, "y": 2.94, "curve": 0.304, "c2": 0.38, "c3": 0.515, "c4": 0.83}, {"time": 5.3333, "x": 8.9, "y": 10.76}]}, "shui35": {"translate": [{"x": 2.96, "y": 11.42, "curve": 0.318, "c2": 0.66, "c3": 0.651}, {"time": 0.0667, "x": 2.97, "y": 11.47, "curve": 0.605, "c2": 0.01, "c3": 0.589, "c4": 0.81}, {"time": 1.2333, "x": 0.07, "y": 0.28, "curve": 0.288, "c2": 0.64, "c3": 0.617}, {"time": 1.4, "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 2.0333, "x": 1.24, "y": 4.77, "curve": 0.252, "c2": 0.46, "c3": 0.476, "c4": 0.93}, {"time": 2.6667, "x": 2.96, "y": 11.42, "curve": 0.318, "c2": 0.66, "c3": 0.651}, {"time": 2.7333, "x": 2.97, "y": 11.47, "curve": 0.605, "c2": 0.01, "c3": 0.589, "c4": 0.81}, {"time": 3.9, "x": 0.07, "y": 0.28, "curve": 0.288, "c2": 0.64, "c3": 0.617}, {"time": 4.0667, "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 4.7, "x": 1.24, "y": 4.77, "curve": 0.252, "c2": 0.46, "c3": 0.476, "c4": 0.93}, {"time": 5.3333, "x": 2.96, "y": 11.42}]}, "shui36": {"translate": [{"x": 1.24, "y": 4.77, "curve": 0.24, "c2": 0.49, "c3": 0.451}, {"time": 0.7, "x": 2.97, "y": 11.47, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 1.2333, "x": 2.18, "y": 8.4, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 2.0333, "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 2.6667, "x": 1.24, "y": 4.77, "curve": 0.24, "c2": 0.49, "c3": 0.451}, {"time": 3.3667, "x": 2.97, "y": 11.47, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 3.9, "x": 2.18, "y": 8.4, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 4.7, "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 5.3333, "x": 1.24, "y": 4.77}]}, "shui37": {"translate": [{"x": -0.03, "y": 0.45, "curve": 0.429, "c2": 0.25, "c3": 0.433}, {"time": 1.1333, "x": -0.77, "y": 13.48, "curve": 0.366, "c2": 0.03, "c3": 0.697, "c4": 0.38}, {"time": 1.2333, "x": -0.77, "y": 13.33, "curve": 0.548, "c2": 0.12, "c3": 0.704, "c4": 0.65}, {"time": 2.0333, "x": -0.15, "y": 2.61, "curve": 0.234, "c2": 0.58, "c3": 0.528}, {"time": 2.4667, "curve": 0.4, "c2": 0.01, "c3": 0.726, "c4": 0.39}, {"time": 2.6667, "x": -0.03, "y": 0.45, "curve": 0.429, "c2": 0.25, "c3": 0.433}, {"time": 3.8, "x": -0.77, "y": 13.48, "curve": 0.366, "c2": 0.03, "c3": 0.697, "c4": 0.38}, {"time": 3.9, "x": -0.77, "y": 13.33, "curve": 0.548, "c2": 0.12, "c3": 0.704, "c4": 0.65}, {"time": 4.7, "x": -0.15, "y": 2.61, "curve": 0.234, "c2": 0.58, "c3": 0.528}, {"time": 5.1333, "curve": 0.4, "c2": 0.01, "c3": 0.726, "c4": 0.39}, {"time": 5.3333, "x": -0.03, "y": 0.45}]}, "shui38": {"translate": [{"x": 0.22, "y": 0.15, "curve": 0.288, "c2": 0.64, "c3": 0.617}, {"time": 0.1667, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 1.2333, "x": 9.44, "y": 6.52, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 1.5, "x": 10.15, "y": 7.01, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 2.0333, "x": 7.44, "y": 5.14, "curve": 0.304, "c2": 0.38, "c3": 0.515, "c4": 0.83}, {"time": 2.6667, "x": 0.22, "y": 0.15, "curve": 0.288, "c2": 0.64, "c3": 0.617}, {"time": 2.8333, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 3.9, "x": 9.44, "y": 6.52, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 4.1667, "x": 10.15, "y": 7.01, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 4.7, "x": 7.44, "y": 5.14, "curve": 0.304, "c2": 0.38, "c3": 0.515, "c4": 0.83}, {"time": 5.3333, "x": 0.22, "y": 0.15}]}, "shui39": {"translate": [{"x": 0.16, "y": 0.63, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 0.2667, "curve": 0.609, "c2": 0.01, "c3": 0.707, "c4": 0.64}, {"time": 1.2333, "x": 2.54, "y": 9.82, "curve": 0.242, "c2": 0.59, "c3": 0.545}, {"time": 1.6, "x": 2.97, "y": 11.47, "curve": 0.474, "c2": 0.01, "c3": 0.771, "c4": 0.43}, {"time": 2.0333, "x": 2.5, "y": 9.65, "curve": 0.367, "c2": 0.32, "c3": 0.57, "c4": 0.77}, {"time": 2.6667, "x": 0.16, "y": 0.63, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 2.9333, "curve": 0.609, "c2": 0.01, "c3": 0.707, "c4": 0.64}, {"time": 3.9, "x": 2.54, "y": 9.82, "curve": 0.242, "c2": 0.59, "c3": 0.545}, {"time": 4.2667, "x": 2.97, "y": 11.47, "curve": 0.474, "c2": 0.01, "c3": 0.771, "c4": 0.43}, {"time": 4.7, "x": 2.5, "y": 9.65, "curve": 0.367, "c2": 0.32, "c3": 0.57, "c4": 0.77}, {"time": 5.3333, "x": 0.16, "y": 0.63}]}, "shui40": {"translate": [{"x": 8.9, "y": 10.76, "curve": 0.288, "c2": 0.64, "c3": 0.617}, {"time": 0.1667, "x": 9.1, "y": 10.99, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 1.2333, "x": 0.64, "y": 0.77, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 1.5, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 2.0333, "x": 2.43, "y": 2.94, "curve": 0.304, "c2": 0.38, "c3": 0.515, "c4": 0.83}, {"time": 2.6667, "x": 8.9, "y": 10.76, "curve": 0.288, "c2": 0.64, "c3": 0.617}, {"time": 2.8333, "x": 9.1, "y": 10.99, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 3.9, "x": 0.64, "y": 0.77, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 4.1667, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 4.7, "x": 2.43, "y": 2.94, "curve": 0.304, "c2": 0.38, "c3": 0.515, "c4": 0.83}, {"time": 5.3333, "x": 8.9, "y": 10.76}]}, "shui41": {"translate": [{"x": 2.96, "y": 11.42, "curve": 0.318, "c2": 0.66, "c3": 0.651}, {"time": 0.0667, "x": 2.97, "y": 11.47, "curve": 0.605, "c2": 0.01, "c3": 0.589, "c4": 0.81}, {"time": 1.2333, "x": 0.07, "y": 0.28, "curve": 0.288, "c2": 0.64, "c3": 0.617}, {"time": 1.4, "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 2.0333, "x": 1.24, "y": 4.77, "curve": 0.252, "c2": 0.46, "c3": 0.476, "c4": 0.93}, {"time": 2.6667, "x": 2.96, "y": 11.42, "curve": 0.318, "c2": 0.66, "c3": 0.651}, {"time": 2.7333, "x": 2.97, "y": 11.47, "curve": 0.605, "c2": 0.01, "c3": 0.589, "c4": 0.81}, {"time": 3.9, "x": 0.07, "y": 0.28, "curve": 0.288, "c2": 0.64, "c3": 0.617}, {"time": 4.0667, "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 4.7, "x": 1.24, "y": 4.77, "curve": 0.252, "c2": 0.46, "c3": 0.476, "c4": 0.93}, {"time": 5.3333, "x": 2.96, "y": 11.42}]}, "shui42": {"scale": [{"x": 1.075, "y": 1.075, "curve": 0.327, "c2": 0.31, "c3": 0.757}, {"time": 2.0333, "x": 1.476, "y": 1.476, "curve": "stepped"}, {"time": 2.1, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 2.6667, "x": 1.075, "y": 1.075, "curve": 0.327, "c2": 0.31, "c3": 0.757}, {"time": 4.7, "x": 1.476, "y": 1.476, "curve": "stepped"}, {"time": 4.7667, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 5.3333, "x": 1.075, "y": 1.075}]}, "shui43": {"translate": [{"x": 1, "y": 3.85, "curve": 0.252, "c2": 0.47, "c3": 0.441}, {"time": 0.7667, "x": 2.97, "y": 11.47, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.1, "curve": 0.528, "c2": 0.01, "c3": 0.785, "c4": 0.48}, {"time": 2.6667, "x": 1, "y": 3.85, "curve": 0.252, "c2": 0.47, "c3": 0.441}, {"time": 3.4333, "x": 2.97, "y": 11.47, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.7667, "curve": 0.528, "c2": 0.01, "c3": 0.785, "c4": 0.48}, {"time": 5.3333, "x": 1, "y": 3.85}]}, "shui44": {"translate": [{"x": -0.02, "y": 0.31, "curve": 0.465, "c2": 0.2, "c3": 0.442}, {"time": 1.1667, "x": -0.77, "y": 13.48, "curve": 0.604, "c2": 0.01, "c3": 0.729, "c4": 0.61}, {"time": 2.1, "x": -0.15, "y": 2.61, "curve": 0.234, "c2": 0.58, "c3": 0.528}, {"time": 2.5333, "curve": 0.383, "c2": 0.02, "c3": 0.712, "c4": 0.38}, {"time": 2.6667, "x": -0.02, "y": 0.31, "curve": 0.465, "c2": 0.2, "c3": 0.442}, {"time": 3.8333, "x": -0.77, "y": 13.48, "curve": 0.604, "c2": 0.01, "c3": 0.729, "c4": 0.61}, {"time": 4.7667, "x": -0.15, "y": 2.61, "curve": 0.234, "c2": 0.58, "c3": 0.528}, {"time": 5.2, "curve": 0.383, "c2": 0.02, "c3": 0.712, "c4": 0.38}, {"time": 5.3333, "x": -0.02, "y": 0.31}]}, "shui45": {"translate": [{"x": 0.38, "y": 0.26, "curve": 0.274, "c2": 0.63, "c3": 0.599}, {"time": 0.2, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.5667, "x": 10.15, "y": 7.01, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 2.1, "x": 7.44, "y": 5.14, "curve": 0.315, "c2": 0.37, "c3": 0.542, "c4": 0.8}, {"time": 2.6667, "x": 0.38, "y": 0.26, "curve": 0.274, "c2": 0.63, "c3": 0.599}, {"time": 2.8667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.2333, "x": 10.15, "y": 7.01, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 4.7667, "x": 7.44, "y": 5.14, "curve": 0.315, "c2": 0.37, "c3": 0.542, "c4": 0.8}, {"time": 5.3333, "x": 0.38, "y": 0.26}]}, "shui46": {"translate": [{"x": 0.26, "y": 1, "curve": 0.251, "c2": 0.6, "c3": 0.563}, {"time": 0.3333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.6667, "x": 2.97, "y": 11.47, "curve": 0.474, "c2": 0.01, "c3": 0.771, "c4": 0.43}, {"time": 2.1, "x": 2.5, "y": 9.65, "curve": 0.375, "c2": 0.31, "c3": 0.597, "c4": 0.74}, {"time": 2.6667, "x": 0.26, "y": 1, "curve": 0.251, "c2": 0.6, "c3": 0.563}, {"time": 3, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.3333, "x": 2.97, "y": 11.47, "curve": 0.474, "c2": 0.01, "c3": 0.771, "c4": 0.43}, {"time": 4.7667, "x": 2.5, "y": 9.65, "curve": 0.375, "c2": 0.31, "c3": 0.597, "c4": 0.74}, {"time": 5.3333, "x": 0.26, "y": 1}]}, "shui47": {"translate": [{"x": 8.76, "y": 10.58, "curve": 0.274, "c2": 0.63, "c3": 0.599}, {"time": 0.2, "x": 9.1, "y": 10.99, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.5667, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 2.1, "x": 2.43, "y": 2.94, "curve": 0.315, "c2": 0.37, "c3": 0.542, "c4": 0.8}, {"time": 2.6667, "x": 8.76, "y": 10.58, "curve": 0.274, "c2": 0.63, "c3": 0.599}, {"time": 2.8667, "x": 9.1, "y": 10.99, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.2333, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 4.7667, "x": 2.43, "y": 2.94, "curve": 0.315, "c2": 0.37, "c3": 0.542, "c4": 0.8}, {"time": 5.3333, "x": 8.76, "y": 10.58}]}, "shui48": {"translate": [{"x": 2.94, "y": 11.37, "curve": 0.303, "c2": 0.65, "c3": 0.634}, {"time": 0.1, "x": 2.97, "y": 11.47, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.4333, "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 2.1, "x": 1.24, "y": 4.77, "curve": 0.264, "c2": 0.43, "c3": 0.501, "c4": 0.87}, {"time": 2.6667, "x": 2.94, "y": 11.37, "curve": 0.303, "c2": 0.65, "c3": 0.634}, {"time": 2.7667, "x": 2.97, "y": 11.47, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.1, "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 4.7667, "x": 1.24, "y": 4.77, "curve": 0.264, "c2": 0.43, "c3": 0.501, "c4": 0.87}, {"time": 5.3333, "x": 2.94, "y": 11.37}]}, "feiniao": {"rotate": [{"time": 2.6667}, {"time": 5.3333, "angle": 83.55}]}, "bone2": {"rotate": [{"angle": 5.56, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 5.56, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 5.56, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 5.56, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 5.56}]}, "bone3": {"rotate": [{"angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": 5.56, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1, "angle": -4.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3, "angle": 0.63, "curve": "stepped"}, {"time": 1.3333, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "angle": 5.56, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.3333, "angle": -4.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6333, "angle": 0.63, "curve": "stepped"}, {"time": 2.6667, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3, "angle": 5.56, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 3.6667, "angle": -4.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.9667, "angle": 0.63, "curve": "stepped"}, {"time": 4, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.3333, "angle": 5.56, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5, "angle": -4.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3, "angle": 0.63}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 9.37, "y": -1.04, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 9.37, "y": -1.04, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 9.37, "y": -1.04, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 9.37, "y": -1.04, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone4": {"rotate": [{"angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 5.56, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": -4.3, "curve": "stepped"}, {"time": 1.3333, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 5.56, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": -4.3, "curve": "stepped"}, {"time": 2.6667, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 5.56, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "angle": -4.3, "curve": "stepped"}, {"time": 4, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 5.56, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "angle": -4.3}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 8.06, "y": -0.99, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 8.06, "y": -0.99, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 8.06, "y": -0.99, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 8.06, "y": -0.99, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone5": {"rotate": [{"angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 5.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3, "angle": 0.63, "curve": "stepped"}, {"time": 1.3333, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 5.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6333, "angle": 0.63, "curve": "stepped"}, {"time": 2.6667, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 5.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.9667, "angle": 0.63, "curve": "stepped"}, {"time": 4, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.3333, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 5.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3, "angle": 0.63}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 7.79, "y": -0.36, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 7.79, "y": -0.36, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 7.79, "y": -0.36, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 7.79, "y": -0.36, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "ren10": {"rotate": [{"angle": 1.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2, "angle": 2.85, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 1.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.6667, "angle": 2.85, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 1.42}]}, "ren17": {"rotate": [{"angle": -7.03}]}, "ren18": {"rotate": [{"angle": 13.99, "curve": "stepped"}, {"time": 2.6667, "angle": 13.99, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.3333, "angle": 13.99}]}, "ren19": {"rotate": [{"angle": 7.71}]}, "ren20": {"rotate": [{"angle": -17.99}]}, "ren14": {"rotate": [{"angle": 7.46, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 3.73, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.6667, "angle": 7.46, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": 3.73, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.3333, "angle": 7.46}]}, "ren12": {"translate": [{"x": 0.94, "y": 0.72, "curve": 0.285, "c2": 0.17, "c3": 0.657, "c4": 0.63}, {"time": 0.6667, "x": 0.33, "y": 0.26, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1.1667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.5, "x": 1, "y": 0.77, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.6667, "x": 0.94, "y": 0.72, "curve": 0.285, "c2": 0.17, "c3": 0.657, "c4": 0.63}, {"time": 3.3333, "x": 0.33, "y": 0.26, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.8333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.1667, "x": 1, "y": 0.77, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 5.3333, "x": 0.94, "y": 0.72}]}, "ren13": {"translate": [{"x": 0.69, "y": -1.08, "curve": 0.285, "c2": 0.17, "c3": 0.657, "c4": 0.63}, {"time": 0.6667, "x": 0.25, "y": -0.39, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1.1667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.5, "x": 0.73, "y": -1.15, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.6667, "x": 0.69, "y": -1.08, "curve": 0.285, "c2": 0.17, "c3": 0.657, "c4": 0.63}, {"time": 3.3333, "x": 0.25, "y": -0.39, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.8333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.1667, "x": 0.73, "y": -1.15, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 5.3333, "x": 0.69, "y": -1.08}]}, "ren2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.41, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 1.41, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "ren5": {"translate": [{"x": 0.34, "y": 0.37, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.8333, "x": 1.01, "y": 1.1, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": 0.34, "y": 0.37, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.5, "x": 1.01, "y": 1.1, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "x": 0.34, "y": 0.37}]}, "ren4": {"translate": [{"x": 0.29, "y": -0.34, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.8333, "x": 0.87, "y": -1, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": 0.29, "y": -0.34, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.5, "x": 0.87, "y": -1, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "x": 0.29, "y": -0.34}]}, "ren3": {"rotate": [{"angle": 0.26, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.6667, "angle": 1.41, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 0.26, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.3333, "angle": 1.41, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 0.26}]}, "ren7": {"rotate": [{"angle": -2.64, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.3333, "angle": -3.23, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -2.64, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5, "angle": -3.23, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -2.64}]}, "ren6": {"rotate": [{"angle": -1.62, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2, "angle": -3.23, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.62, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.6667, "angle": -3.23, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -1.62}]}, "ren9": {"rotate": [{"angle": 0.67, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.6667, "angle": 3.65, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.3333, "angle": 1.82, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 2.6667, "angle": 0.67, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.3333, "angle": 3.65, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5, "angle": 1.82, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 5.3333, "angle": 0.67}], "translate": [{"x": -0.34, "y": -0.17, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.6667, "x": -1.88, "y": -0.93, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.3333, "x": -0.94, "y": -0.46, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 2.6667, "x": -0.34, "y": -0.17, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.3333, "x": -1.88, "y": -0.93, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5, "x": -0.94, "y": -0.46, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 5.3333, "x": -0.34, "y": -0.17}]}, "ren21": {"rotate": [{"angle": -3.85, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 0.6667, "angle": -0.87, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.3333, "angle": -4.73, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -3.85, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 3.3333, "angle": -0.87, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.6667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5, "angle": -4.73, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -3.85}]}, "ren11": {"rotate": [{"angle": 2.32, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 0.6667, "angle": 0.53, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.3333, "angle": 2.85, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 2.32, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 3.3333, "angle": 0.53, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.6667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5, "angle": 2.85, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": 2.32}]}, "ren22": {"rotate": [{"angle": -4.71, "curve": 0.324, "c2": 0.66, "c3": 0.657}, {"time": 0.0333, "angle": -4.73, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 0.6667, "angle": -2.52, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 1.3667, "curve": 0.583, "c2": 0.01, "c3": 0.504, "c4": 0.95}, {"time": 2.6667, "angle": -4.71, "curve": 0.324, "c2": 0.66, "c3": 0.657}, {"time": 2.7, "angle": -4.73, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 3.3333, "angle": -2.52, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 4.0333, "curve": 0.583, "c2": 0.01, "c3": 0.504, "c4": 0.95}, {"time": 5.3333, "angle": -4.71}]}, "ren16": {"rotate": [{"angle": 2.85, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 1.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.6667, "angle": 2.85, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": 1.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.3333, "angle": 2.85}]}, "ren15": {"rotate": [{"angle": 6.68, "curve": 0.248, "c2": 0.6, "c3": 0.559}, {"time": 0.3333, "angle": 7.46, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.6667, "angle": 6.09, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.6667, "curve": 0.612, "c2": 0.01, "c3": 0.689, "c4": 0.67}, {"time": 2.6667, "angle": 6.68, "curve": 0.248, "c2": 0.6, "c3": 0.559}, {"time": 3, "angle": 7.46, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 3.3333, "angle": 6.09, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.3333, "curve": 0.612, "c2": 0.01, "c3": 0.689, "c4": 0.67}, {"time": 5.3333, "angle": 6.68}]}, "target1": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -1.34, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -1.34, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "huo1": {"scale": [{"curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.6333, "x": 1.177, "y": 1.177, "curve": "stepped"}, {"time": 2.6667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.3, "x": 1.177, "y": 1.177, "curve": "stepped"}, {"time": 5.3333}]}, "bone6": {"rotate": [{"angle": -1.29, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 5.56, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.5667, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": 5.56, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "angle": -1.29, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 2.9, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": 5.56, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.2333, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "angle": 5.56, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 5.3333, "angle": -1.29}]}, "bone9": {"rotate": [{"angle": -3.48, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 0.5667, "angle": 5.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.8667, "angle": 0.63, "curve": "stepped"}, {"time": 0.9, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.2333, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": 5.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.2, "angle": 0.63, "curve": "stepped"}, {"time": 2.2333, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5667, "angle": -4.3, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 2.6667, "angle": -3.48, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 3.2333, "angle": 5.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.5333, "angle": 0.63, "curve": "stepped"}, {"time": 3.5667, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.9, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 4.5667, "angle": 5.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.8667, "angle": 0.63, "curve": "stepped"}, {"time": 4.9, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.2333, "angle": -4.3, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 5.3333, "angle": -3.48}], "translate": [{"x": 5.42, "y": -0.25, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "x": 7.79, "y": -0.36, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.5667, "x": 7.79, "y": -0.36, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "x": 5.42, "y": -0.25, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 2.9, "x": 7.79, "y": -0.36, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.2333, "x": 7.79, "y": -0.36, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 5.3333, "x": 5.42, "y": -0.25}]}, "bone8": {"rotate": [{"angle": 2.56, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": 5.56, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -4.3, "curve": "stepped"}, {"time": 0.9, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": 5.56, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "angle": -4.3, "curve": "stepped"}, {"time": 2.2333, "angle": -4.3, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "angle": 2.56, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 2.9, "angle": 5.56, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "angle": -4.3, "curve": "stepped"}, {"time": 3.5667, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 4.2333, "angle": 5.56, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": -4.3, "curve": "stepped"}, {"time": 4.9, "angle": -4.3, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 5.3333, "angle": 2.56}], "translate": [{"x": 5.61, "y": -0.69, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "x": 8.06, "y": -0.99, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.5667, "x": 8.06, "y": -0.99, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "x": 5.61, "y": -0.69, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 2.9, "x": 8.06, "y": -0.99, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.2333, "x": 8.06, "y": -0.99, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 5.3333, "x": 5.61, "y": -0.69}]}, "bone7": {"rotate": [{"angle": 5.28, "curve": 0.438, "c2": 0.23, "c3": 0.435}, {"time": 0.5667, "angle": -4.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.8667, "angle": 0.63, "curve": "stepped"}, {"time": 0.9, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.2333, "angle": 5.56, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.9, "angle": -4.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.2, "angle": 0.63, "curve": "stepped"}, {"time": 2.2333, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5667, "angle": 5.56, "curve": 0.396, "c2": 0.01, "c3": 0.723, "c4": 0.38}, {"time": 2.6667, "angle": 5.28, "curve": 0.438, "c2": 0.23, "c3": 0.435}, {"time": 3.2333, "angle": -4.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.5333, "angle": 0.63, "curve": "stepped"}, {"time": 3.5667, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.9, "angle": 5.56, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.5667, "angle": -4.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.8667, "angle": 0.63, "curve": "stepped"}, {"time": 4.9, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.2333, "angle": 5.56, "curve": 0.396, "c2": 0.01, "c3": 0.723, "c4": 0.38}, {"time": 5.3333, "angle": 5.28}], "translate": [{"x": 6.51, "y": -0.72, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "x": 9.37, "y": -1.04, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.5667, "x": 9.37, "y": -1.04, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "x": 6.51, "y": -0.72, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 2.9, "x": 9.37, "y": -1.04, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.2333, "x": 9.37, "y": -1.04, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 5.3333, "x": 6.51, "y": -0.72}]}, "huo2": {"scale": [{"x": 1.141, "y": 1.141, "curve": 0.233, "c2": 0.57, "c3": 0.524}, {"time": 0.8667, "x": 1.177, "y": 1.177, "curve": "stepped"}, {"time": 0.9, "curve": 0.602, "c2": 0.01, "c3": 0.734, "c4": 0.61}, {"time": 2.6667, "x": 1.141, "y": 1.141, "curve": 0.233, "c2": 0.57, "c3": 0.524}, {"time": 3.5333, "x": 1.177, "y": 1.177, "curve": "stepped"}, {"time": 3.5667, "curve": 0.602, "c2": 0.01, "c3": 0.734, "c4": 0.61}, {"time": 5.3333, "x": 1.141, "y": 1.141}]}, "bone10": {"rotate": [{"angle": -3.48, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 0.5667, "angle": 5.56, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": 5.56, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.5667, "angle": -4.3, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 2.6667, "angle": -3.48, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 3.2333, "angle": 5.56, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 4.5667, "angle": 5.56, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.2333, "angle": -4.3, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 5.3333, "angle": -3.48}]}, "bone13": {"rotate": [{"angle": 2.56, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": 5.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "angle": 0.63, "curve": "stepped"}, {"time": 0.5667, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.9, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": 5.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.8667, "angle": 0.63, "curve": "stepped"}, {"time": 1.9, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.2333, "angle": -4.3, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "angle": 2.56, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 2.9, "angle": 5.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.2, "angle": 0.63, "curve": "stepped"}, {"time": 3.2333, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.5667, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 4.2333, "angle": 5.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.5333, "angle": 0.63, "curve": "stepped"}, {"time": 4.5667, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.9, "angle": -4.3, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 5.3333, "angle": 2.56}], "translate": [{"x": 7.14, "y": -0.33, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": 7.79, "y": -0.36, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.5667, "x": 7.79, "y": -0.36, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 2.6667, "x": 7.14, "y": -0.33, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 3.2333, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "x": 7.79, "y": -0.36, "curve": 0.25, "c3": 0.75}, {"time": 4.5667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.2333, "x": 7.79, "y": -0.36, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 5.3333, "x": 7.14, "y": -0.33}]}, "bone12": {"rotate": [{"angle": 4.68, "curve": 0.308, "c2": 0.24, "c3": 0.757}, {"time": 0.5333, "angle": -4.3, "curve": "stepped"}, {"time": 0.5667, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 5.56, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -4.3, "curve": "stepped"}, {"time": 1.9, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": 5.56, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 2.6667, "angle": 4.68, "curve": 0.308, "c2": 0.24, "c3": 0.757}, {"time": 3.2, "angle": -4.3, "curve": "stepped"}, {"time": 3.2333, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "angle": 5.56, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "angle": -4.3, "curve": "stepped"}, {"time": 4.5667, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 5.2333, "angle": 5.56, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 5.3333, "angle": 4.68}], "translate": [{"x": 7.4, "y": -0.9, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": 8.06, "y": -0.99, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.5667, "x": 8.06, "y": -0.99, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 2.6667, "x": 7.4, "y": -0.9, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 3.2333, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "x": 8.06, "y": -0.99, "curve": 0.25, "c3": 0.75}, {"time": 4.5667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.2333, "x": 8.06, "y": -0.99, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 5.3333, "x": 7.4, "y": -0.9}]}, "bone11": {"rotate": [{"angle": -1.94, "curve": 0.23, "c2": 0.57, "c3": 0.515}, {"time": 0.2333, "angle": -4.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "angle": 0.63, "curve": "stepped"}, {"time": 0.5667, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.9, "angle": 5.56, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.5667, "angle": -4.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.8667, "angle": 0.63, "curve": "stepped"}, {"time": 1.9, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.2333, "angle": 5.56, "curve": 0.598, "c2": 0.01, "c3": 0.743, "c4": 0.59}, {"time": 2.6667, "angle": -1.94, "curve": 0.23, "c2": 0.57, "c3": 0.515}, {"time": 2.9, "angle": -4.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.2, "angle": 0.63, "curve": "stepped"}, {"time": 3.2333, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.5667, "angle": 5.56, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.2333, "angle": -4.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.5333, "angle": 0.63, "curve": "stepped"}, {"time": 4.5667, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.9, "angle": 5.56, "curve": 0.598, "c2": 0.01, "c3": 0.743, "c4": 0.59}, {"time": 5.3333, "angle": -1.94}], "translate": [{"x": 8.59, "y": -0.95, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": 9.37, "y": -1.04, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.5667, "x": 9.37, "y": -1.04, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 2.6667, "x": 8.59, "y": -0.95, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 3.2333, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "x": 9.37, "y": -1.04, "curve": 0.25, "c3": 0.75}, {"time": 4.5667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.2333, "x": 9.37, "y": -1.04, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 5.3333, "x": 8.59, "y": -0.95}]}, "huo3": {"scale": [{"x": 1.022, "y": 1.022, "curve": 0.327, "c2": 0.37, "c3": 0.421}, {"time": 1.8667, "x": 1.177, "y": 1.177, "curve": "stepped"}, {"time": 1.9, "curve": 0.461, "c2": 0.01, "c3": 0.765, "c4": 0.42}, {"time": 2.6667, "x": 1.022, "y": 1.022, "curve": 0.327, "c2": 0.37, "c3": 0.421}, {"time": 4.5333, "x": 1.177, "y": 1.177, "curve": "stepped"}, {"time": 4.5667, "curve": 0.461, "c2": 0.01, "c3": 0.765, "c4": 0.42}, {"time": 5.3333, "x": 1.022, "y": 1.022}]}, "bone14": {"translate": [{"x": 0.62, "y": 1.24, "curve": 0.382, "c2": 0.55, "c3": 0.741}, {"time": 1.0333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 3.7, "x": 1.77, "y": 3.54, "curve": 0.245, "c3": 0.639, "c4": 0.56}, {"time": 5.3333, "x": 0.62, "y": 1.24}]}, "bone38": {"translate": [{"x": -4.78, "y": -3.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6333, "x": -9.49, "y": -6.78, "curve": "stepped"}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": -4.78, "y": -3.42}], "scale": [{"x": 1.044, "y": 1.044, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6333, "x": 1.087, "y": 1.087, "curve": "stepped"}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 1.044, "y": 1.044}]}, "bone15": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 5.3, "x": 1.115, "y": 1.048, "curve": "stepped"}, {"time": 5.3333}]}, "bone16": {"scale": [{"x": 1.059, "y": 1.024, "curve": 0.376, "c2": 0.5, "c3": 0.75}, {"time": 2.6333, "x": 1.115, "y": 1.048, "curve": "stepped"}, {"time": 2.6667, "curve": 0.25, "c3": 0.626, "c4": 0.5}, {"time": 5.3333, "x": 1.059, "y": 1.024}]}, "yun2": {"translate": [{"x": 12.45, "y": -8.89}], "scale": [{"x": 1.062, "y": 1.062, "curve": 0.367, "c2": 0.63, "c3": 0.704}, {"time": 0.7333, "x": 1.067, "y": 1.067, "curve": "stepped"}, {"time": 0.8, "curve": 0.244, "c3": 0.698, "c4": 0.78}, {"time": 5.3333, "x": 1.062, "y": 1.062}]}, "yun9": {"translate": [{"x": 12.45, "y": -8.89}], "scale": [{"x": 1.021, "y": 1.021, "curve": 0.327, "c2": 0.31, "c3": 0.664, "c4": 0.66}, {"time": 0.8, "x": 1.034, "y": 1.034, "curve": 0.376, "c2": 0.5, "c3": 0.75}, {"time": 3.4, "x": 1.067, "y": 1.067, "curve": "stepped"}, {"time": 3.4667, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "x": 1.021, "y": 1.021}]}, "yun6": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 5.3, "x": 1.064, "y": 1.064, "curve": "stepped"}, {"time": 5.3333}]}, "yun10": {"scale": [{"x": 1.032, "y": 1.032, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6333, "x": 1.064, "y": 1.064, "curve": "stepped"}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 1.032, "y": 1.032}]}, "yun4": {"scale": [{"x": 1.008, "y": 1.008, "curve": 0.324, "c2": 0.3, "c3": 0.757}, {"time": 4.1667, "x": 1.056, "y": 1.056, "curve": "stepped"}, {"time": 4.2, "curve": 0.282, "c3": 0.624, "c4": 0.39}, {"time": 5.3333, "x": 1.008, "y": 1.008}]}, "yun11": {"scale": [{"x": 1.044, "y": 1.044, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 1.5, "x": 1.056, "y": 1.056, "curve": "stepped"}, {"time": 1.5333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.2, "x": 1.028, "y": 1.028, "curve": 0.341, "c2": 0.36, "c3": 0.681, "c4": 0.71}, {"time": 5.3333, "x": 1.044, "y": 1.044}]}, "yun5": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 5.3, "x": 1.042, "y": 1.042, "curve": "stepped"}, {"time": 5.3333}]}, "yun12": {"scale": [{"x": 1.021, "y": 1.021, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6333, "x": 1.042, "y": 1.042, "curve": "stepped"}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 1.021, "y": 1.021}]}, "yun8": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 5.3, "x": 1.063, "y": 1.063, "curve": "stepped"}, {"time": 5.3333}]}, "yun13": {"scale": [{"x": 1.032, "y": 1.032, "curve": 0.376, "c2": 0.5, "c3": 0.749}, {"time": 2.6, "x": 1.063, "y": 1.063, "curve": "stepped"}, {"time": 2.6333, "curve": 0.249, "c3": 0.626, "c4": 0.5}, {"time": 5.3333, "x": 1.032, "y": 1.032}]}, "yun3": {"translate": [{"curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.3, "x": -30.98, "y": 8.45, "curve": "stepped"}, {"time": 5.3333}], "scale": [{"curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.3, "x": 1.141, "y": 1.141, "curve": "stepped"}, {"time": 5.3333}]}, "yun14": {"translate": [{"x": -14.4, "y": 3.93, "curve": 0.235, "c2": 0.5, "c3": 0.459}, {"time": 2.6333, "x": -30.98, "y": 8.45, "curve": "stepped"}, {"time": 2.6667, "curve": 0.554, "c2": 0.01, "c3": 0.783, "c4": 0.51}, {"time": 5.3333, "x": -14.4, "y": 3.93}], "scale": [{"x": 1.065, "y": 1.065, "curve": 0.235, "c2": 0.5, "c3": 0.459}, {"time": 2.6333, "x": 1.141, "y": 1.141, "curve": "stepped"}, {"time": 2.6667, "curve": 0.554, "c2": 0.01, "c3": 0.783, "c4": 0.51}, {"time": 5.3333, "x": 1.065, "y": 1.065}]}, "yun7": {"scale": [{"x": 1.063, "y": 1.063, "curve": 0.251, "c2": 0.6, "c3": 0.564}, {"time": 1.2667, "x": 1.07, "y": 1.07, "curve": "stepped"}, {"time": 1.3, "curve": 0.612, "c2": 0.01, "c3": 0.682, "c4": 0.68}, {"time": 5.3333, "x": 1.063, "y": 1.063}]}, "yun15": {"scale": [{"x": 1.007, "y": 1.007, "curve": 0.408, "c2": 0.28, "c3": 0.708, "c4": 0.64}, {"time": 1.3, "x": 1.033, "y": 1.033, "curve": 0.235, "c2": 0.5, "c3": 0.459}, {"time": 3.9333, "x": 1.07, "y": 1.07, "curve": "stepped"}, {"time": 3.9667, "curve": 0.445, "c2": 0.01, "c3": 0.756, "c4": 0.41}, {"time": 5.3333, "x": 1.007, "y": 1.007}]}, "bone23": {"translate": [{"x": 0.11, "y": 0.11, "curve": 0.293, "c2": 0.64, "c3": 0.623}, {"time": 0.5667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 3.2333, "x": 3.54, "y": 3.54, "curve": 0.268, "c2": 0.42, "c3": 0.522, "c4": 0.84}, {"time": 5.3333, "x": 0.11, "y": 0.11}]}, "bone24": {"translate": [{"curve": 0.552, "c2": 0.01, "c3": 0.783, "c4": 0.5}, {"time": 2.6667, "y": 3.54, "curve": 0.236, "c2": 0.5, "c3": 0.458}, {"time": 5.3333}]}, "hulu": {"translate": [{"x": -1.89, "y": 1.89, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 1.2667, "x": -3.54, "y": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 5.3333, "x": -1.89, "y": 1.89}]}, "bone22": {"translate": [{"x": 1.84, "y": 1.82, "curve": 0.374, "c2": 0.49, "c3": 0.751}, {"time": 1.3667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.0333, "x": 3.56, "y": 3.53, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 5.3333, "x": 1.84, "y": 1.82}]}, "bone": {"translate": [{"x": -1.36, "y": 1.36, "curve": 0.381, "c2": 0.54, "c3": 0.743}, {"time": 1.1, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 3.7667, "x": -3.54, "y": 3.54, "curve": 0.246, "c3": 0.635, "c4": 0.55}, {"time": 5.3333, "x": -1.36, "y": 1.36}]}, "bone25": {"translate": [{"x": 1.11, "y": 1.48, "curve": 0.38, "c2": 0.53, "c3": 0.744}, {"time": 1.1333, "x": 1.85, "y": 2.47, "curve": 0.25, "c3": 0.75}, {"time": 3.8, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 5.3333, "x": 1.11, "y": 1.48}]}, "bone19": {"translate": [{"curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.3, "x": 5.73, "y": 1.41, "curve": "stepped"}, {"time": 5.3333}], "scale": [{"curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.3, "x": 1.047, "y": 1.047, "curve": "stepped"}, {"time": 5.3333}]}, "bone21": {"translate": [{"x": 5.2, "y": 1.28, "curve": 0.253, "c2": 0.61, "c3": 0.567}, {"time": 1.2333, "x": 5.73, "y": 1.41, "curve": "stepped"}, {"time": 1.2667, "curve": 0.613, "c2": 0.01, "c3": 0.677, "c4": 0.68}, {"time": 5.3333, "x": 5.2, "y": 1.28}], "scale": [{"x": 1.043, "y": 1.043, "curve": 0.253, "c2": 0.61, "c3": 0.567}, {"time": 1.2333, "x": 1.047, "y": 1.047, "curve": "stepped"}, {"time": 1.2667, "curve": 0.613, "c2": 0.01, "c3": 0.677, "c4": 0.68}, {"time": 5.3333, "x": 1.043, "y": 1.043}]}, "bone18": {"translate": [{"x": 1.13, "y": 0.28, "curve": 0.29, "c2": 0.41, "c3": 0.425}, {"time": 3.4333, "x": 5.73, "y": 1.41, "curve": "stepped"}, {"time": 3.4667, "curve": 0.489, "c2": 0.01, "c3": 0.777, "c4": 0.44}, {"time": 5.3333, "x": 1.13, "y": 0.28}], "scale": [{"x": 1.009, "y": 1.009, "curve": 0.29, "c2": 0.41, "c3": 0.425}, {"time": 3.4333, "x": 1.047, "y": 1.047, "curve": "stepped"}, {"time": 3.4667, "curve": 0.489, "c2": 0.01, "c3": 0.777, "c4": 0.44}, {"time": 5.3333, "x": 1.009, "y": 1.009}]}, "bone20": {"translate": [{"x": 5.25, "y": 1.29, "curve": 0.256, "c2": 0.61, "c3": 0.572}, {"time": 1.1667, "x": 5.73, "y": 1.41, "curve": "stepped"}, {"time": 1.2, "curve": 0.613, "c2": 0.01, "c3": 0.668, "c4": 0.7}, {"time": 5.3333, "x": 5.25, "y": 1.29}], "scale": [{"x": 1.043, "y": 1.043, "curve": 0.256, "c2": 0.61, "c3": 0.572}, {"time": 1.1667, "x": 1.047, "y": 1.047, "curve": "stepped"}, {"time": 1.2, "curve": 0.613, "c2": 0.01, "c3": 0.668, "c4": 0.7}, {"time": 5.3333, "x": 1.043, "y": 1.043}]}, "bone26": {"translate": [{"x": 0.93, "y": 0.23, "curve": 0.383, "c2": 0.3, "c3": 0.682, "c4": 0.66}, {"time": 1.2667, "x": 3.44, "y": 0.84, "curve": 0.228, "c2": 0.53, "c3": 0.481}, {"time": 3.5667, "x": 5.73, "y": 1.41, "curve": "stepped"}, {"time": 3.6, "curve": 0.477, "c2": 0.01, "c3": 0.773, "c4": 0.43}, {"time": 5.3333, "x": 0.93, "y": 0.23}], "scale": [{"x": 1.008, "y": 1.008, "curve": 0.383, "c2": 0.3, "c3": 0.682, "c4": 0.66}, {"time": 1.2667, "x": 1.028, "y": 1.028, "curve": 0.228, "c2": 0.53, "c3": 0.481}, {"time": 3.5667, "x": 1.047, "y": 1.047, "curve": "stepped"}, {"time": 3.6, "curve": 0.477, "c2": 0.01, "c3": 0.773, "c4": 0.43}, {"time": 5.3333, "x": 1.008, "y": 1.008}]}, "bone27": {"translate": [{"x": 1.01, "y": 0.25, "curve": 0.377, "c2": 0.31, "c3": 0.679, "c4": 0.66}, {"time": 1.2, "x": 3.44, "y": 0.84, "curve": 0.228, "c2": 0.53, "c3": 0.481}, {"time": 3.5, "x": 5.73, "y": 1.41, "curve": "stepped"}, {"time": 3.5333, "curve": 0.483, "c2": 0.01, "c3": 0.775, "c4": 0.44}, {"time": 5.3333, "x": 1.01, "y": 0.25}], "scale": [{"x": 1.008, "y": 1.008, "curve": 0.377, "c2": 0.31, "c3": 0.679, "c4": 0.66}, {"time": 1.2, "x": 1.028, "y": 1.028, "curve": 0.228, "c2": 0.53, "c3": 0.481}, {"time": 3.5, "x": 1.047, "y": 1.047, "curve": "stepped"}, {"time": 3.5333, "curve": 0.483, "c2": 0.01, "c3": 0.775, "c4": 0.44}, {"time": 5.3333, "x": 1.008, "y": 1.008}]}, "bone28": {"translate": [{"x": 3.44, "y": 0.84, "curve": 0.228, "c2": 0.53, "c3": 0.481}, {"time": 2.3, "x": 5.73, "y": 1.41, "curve": "stepped"}, {"time": 2.3333, "curve": 0.576, "c2": 0.01, "c3": 0.772, "c4": 0.54}, {"time": 5.3333, "x": 3.44, "y": 0.84}], "scale": [{"x": 1.028, "y": 1.028, "curve": 0.228, "c2": 0.53, "c3": 0.481}, {"time": 2.3, "x": 1.047, "y": 1.047, "curve": "stepped"}, {"time": 2.3333, "curve": 0.576, "c2": 0.01, "c3": 0.772, "c4": 0.54}, {"time": 5.3333, "x": 1.028, "y": 1.028}]}, "bone29": {"translate": [{"x": 5.68, "y": 1.39, "curve": 0.302, "c2": 0.65, "c3": 0.633}, {"time": 0.4333, "x": 5.73, "y": 1.41, "curve": "stepped"}, {"time": 0.4667, "curve": 0.576, "c2": 0.01, "c3": 0.772, "c4": 0.54}, {"time": 3.4667, "x": 3.44, "y": 0.84, "curve": 0.252, "c2": 0.45, "c3": 0.527, "c4": 0.86}, {"time": 5.3333, "x": 5.68, "y": 1.39}], "scale": [{"x": 1.046, "y": 1.046, "curve": 0.302, "c2": 0.65, "c3": 0.633}, {"time": 0.4333, "x": 1.047, "y": 1.047, "curve": "stepped"}, {"time": 0.4667, "curve": 0.576, "c2": 0.01, "c3": 0.772, "c4": 0.54}, {"time": 3.4667, "x": 1.028, "y": 1.028, "curve": 0.252, "c2": 0.45, "c3": 0.527, "c4": 0.86}, {"time": 5.3333, "x": 1.046, "y": 1.046}]}, "bone31": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 5.3, "x": 9.51, "y": -7.61, "curve": "stepped"}, {"time": 5.3333}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 5.3, "x": 1.075, "y": 1.075, "curve": "stepped"}, {"time": 5.3333}]}, "bone36": {"translate": [{"x": 4.72, "y": -3.77, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "x": 9.51, "y": -7.61, "curve": "stepped"}, {"time": 2.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 4.72, "y": -3.77}], "scale": [{"x": 1.037, "y": 1.037, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "x": 1.075, "y": 1.075, "curve": "stepped"}, {"time": 2.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 1.037, "y": 1.037}]}, "bone35": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 5.3, "x": -13.55, "y": -6.78, "curve": "stepped"}, {"time": 5.3333}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 5.3, "x": 1.161, "y": 1.161, "curve": "stepped"}, {"time": 5.3333}]}, "bone34": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 5.3, "x": -9.49, "y": -6.78, "curve": "stepped"}, {"time": 5.3333}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 5.3, "x": 1.087, "y": 1.087, "curve": "stepped"}, {"time": 5.3333}]}, "bone37": {"translate": [{"x": -6.83, "y": -3.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6333, "x": -13.55, "y": -6.78, "curve": "stepped"}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": -6.83, "y": -3.42}], "scale": [{"x": 1.081, "y": 1.081, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6333, "x": 1.161, "y": 1.161, "curve": "stepped"}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 1.081, "y": 1.081}]}, "bone32": {"translate": [{"x": -8.24, "y": -5.88, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.0667, "x": -9.49, "y": -6.78, "curve": "stepped"}, {"time": 1.1, "curve": 0.243, "c3": 0.679, "c4": 0.71}, {"time": 5.3333, "x": -8.24, "y": -5.88}], "scale": [{"x": 1.075, "y": 1.075, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.0667, "x": 1.087, "y": 1.087, "curve": "stepped"}, {"time": 1.1, "curve": 0.243, "c3": 0.679, "c4": 0.71}, {"time": 5.3333, "x": 1.075, "y": 1.075}]}, "bone39": {"translate": [{"x": -2.24, "y": -1.6, "curve": 0.321, "c2": 0.29, "c3": 0.66, "c4": 0.65}, {"time": 1.1, "x": -4.78, "y": -3.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.7333, "x": -9.49, "y": -6.78, "curve": "stepped"}, {"time": 3.7667, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 5.3333, "x": -2.24, "y": -1.6}], "scale": [{"x": 1.02, "y": 1.02, "curve": 0.321, "c2": 0.29, "c3": 0.66, "c4": 0.65}, {"time": 1.1, "x": 1.044, "y": 1.044, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.7333, "x": 1.087, "y": 1.087, "curve": "stepped"}, {"time": 3.7667, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 5.3333, "x": 1.02, "y": 1.02}]}, "bone40": {"rotate": [{"angle": 44.65, "curve": 0.25, "c3": 0.75}, {"time": 5.2333, "angle": -43.42}]}}}, "game_loading_02": {"slots": {"kaishijm023_3": {"color": [{"color": "ffffff98", "curve": 0.38, "c2": 0.53, "c3": 0.744}, {"time": 0.5667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.9, "color": "ffffff00", "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 2.6667, "color": "ffffff98", "curve": 0.38, "c2": 0.53, "c3": 0.744}, {"time": 3.2333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4.5667, "color": "ffffff00", "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 5.3333, "color": "ffffff98"}]}, "light": {"color": [{"color": "fffac700", "curve": "stepped"}, {"time": 1.3333, "color": "fffac700", "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "color": "fffac76e", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "fffac700"}]}, "kaishijm020": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffff00"}]}, "kaishijm032": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffff00"}]}, "kaishijm013": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffff00"}]}, "kaishijm11": {"color": [{"color": "ffffffcb", "curve": 0.234, "c2": 0.57, "c3": 0.526}, {"time": 0.8667, "color": "ffffffff", "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 1.2, "color": "ffffffee", "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 3.5333, "color": "ffffff00", "curve": 0.603, "c2": 0.01, "c3": 0.732, "c4": 0.61}, {"time": 5.3333, "color": "ffffffcb"}]}, "kaishijm20": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffffff"}]}, "kaishijm29": {"color": [{"color": "ffffff98", "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 1.5333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 4.2, "color": "ffffffff", "curve": 0.256, "c3": 0.62, "c4": 0.47}, {"time": 5.3333, "color": "ffffff98"}]}, "kaishijm019": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffff00"}]}, "kaishijm029": {"color": [{"color": "ffffff66", "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 1.5333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4.2, "color": "ffffff00", "curve": 0.256, "c3": 0.62, "c4": 0.47}, {"time": 5.3333, "color": "ffffff66"}]}, "kaishijm023_1": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffff00"}]}, "kaishijm31": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffffff"}]}, "kaishijm2": {"color": [{"color": "ffffff9c", "curve": 0.381, "c2": 0.54, "c3": 0.743}, {"time": 1.1, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.7667, "color": "ffffff00", "curve": 0.246, "c3": 0.635, "c4": 0.55}, {"time": 5.3333, "color": "ffffff9c"}]}, "kaishijm036": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffff00"}]}, "kaishijm027": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffff00"}]}, "kaishijm016": {"color": [{"color": "ffffff77", "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 1.2667, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 3.9333, "color": "ffffffff", "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 5.3333, "color": "ffffff77"}]}, "kaishijm6": {"color": [{"color": "ffffffa1", "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.1333, "color": "ffffffff", "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "color": "ffffffa1", "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.4667, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.8, "color": "ffffffff", "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.3333, "color": "ffffffa1"}]}, "kaishijm27": {"color": [{"color": "fffffffd", "curve": 0.255, "c2": 0.02, "c3": 0.751}, {"time": 2.6333, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.3, "color": "ffffffff", "curve": 0.329, "c3": 0.663, "c4": 0.34}, {"time": 5.3333, "color": "fffffffd"}]}, "kaishijm02": {"color": [{"color": "ffffff62", "curve": 0.381, "c2": 0.54, "c3": 0.743}, {"time": 1.1, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 3.7667, "color": "ffffffff", "curve": 0.246, "c3": 0.635, "c4": 0.55}, {"time": 5.3333, "color": "ffffff62"}]}, "kaishijm17": {"color": [{"color": "ffffffc1", "curve": 0.23, "c2": 0.57, "c3": 0.515}, {"time": 0.9333, "color": "ffffffff", "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 1.2667, "color": "ffffffee", "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 3.6, "color": "ffffff00", "curve": 0.598, "c2": 0.01, "c3": 0.743, "c4": 0.59}, {"time": 5.3333, "color": "ffffffc1"}]}, "kaishijm5": {"color": [{"color": "ffffffa1", "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.1333, "color": "ffffffff", "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "color": "ffffffa1", "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.4667, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.8, "color": "ffffffff", "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.3333, "color": "ffffffa1"}]}, "kaishijm36": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffffff"}]}, "kaishijm32": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffffff"}]}, "kaishijm8": {"color": [{"color": "ffffff6a", "curve": 0.24, "c2": 0.49, "c3": 0.451}, {"time": 0.7, "color": "ffffffff", "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.2333, "color": "ffffffa1", "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.0333, "color": "ffffff00", "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 2.6667, "color": "ffffff6a", "curve": 0.24, "c2": 0.49, "c3": 0.451}, {"time": 3.3667, "color": "ffffffff", "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.9, "color": "ffffffa1", "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.7, "color": "ffffff00", "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 5.3333, "color": "ffffff6a"}]}, "kaishijm031": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffff00"}]}, "kaishijm025": {"color": [{"color": "ffffffb3", "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffffb3", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffffb3"}]}, "kaishijm06": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffff00"}]}, "kaishijm18": {"color": [{"color": "ffffff1b", "curve": 0.372, "c2": 0.62, "c3": 0.711}, {"time": 0.4667, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 3.1333, "color": "ffffffff", "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 3.4667, "color": "ffffffee", "curve": 0.287, "c2": 0.17, "c3": 0.697, "c4": 0.77}, {"time": 5.3333, "color": "ffffff1b"}]}, "kaishijm13": {"color": [{"color": "ffffffee", "curve": 0.298, "c2": 0.2, "c3": 0.756}, {"time": 2.3333, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5, "color": "ffffffff", "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 5.3333, "color": "ffffffee"}]}, "kaishijm7": {"color": [{"color": "ffffff6a", "curve": 0.24, "c2": 0.49, "c3": 0.451}, {"time": 0.7, "color": "ffffffff", "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.2333, "color": "ffffffa1", "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.0333, "color": "ffffff00", "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 2.6667, "color": "ffffff6a", "curve": 0.24, "c2": 0.49, "c3": 0.451}, {"time": 3.3667, "color": "ffffffff", "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 3.9, "color": "ffffffa1", "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 4.7, "color": "ffffff00", "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 5.3333, "color": "ffffff6a"}]}, "kaishijm28": {"color": [{"color": "ffffff83", "curve": 0.376, "c2": 0.51, "c3": 0.749}, {"time": 1.3, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "color": "ffffff00", "curve": 0.249, "c3": 0.626, "c4": 0.51}, {"time": 5.3333, "color": "ffffff83"}]}, "kaishijm9": {"color": [{"color": "ffffff6b", "curve": 0.368, "c2": 0.47, "c3": 0.753}, {"time": 0.7667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.1, "color": "ffffff00", "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 2.6667, "color": "ffffff6b", "curve": 0.368, "c2": 0.47, "c3": 0.753}, {"time": 3.4333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4.7667, "color": "ffffff00", "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 5.3333, "color": "ffffff6b"}]}, "shuiliu/liushui_01": {"attachment": [{"name": "shuiliu/liushui_1"}, {"time": 0.1, "name": "shuiliu/liushui_02"}, {"time": 0.1667, "name": "shuiliu/liushui_03"}, {"time": 0.2667, "name": "shuiliu/liushui_04"}, {"time": 0.3333, "name": "shuiliu/liushui_05"}, {"time": 0.4333, "name": "shuiliu/liushui_06"}, {"time": 0.5333, "name": "shuiliu/liushui_07"}, {"time": 0.6, "name": "shuiliu/liushui_08"}, {"time": 0.7, "name": "shuiliu/liushui_09"}, {"time": 0.7667, "name": "shuili<PERSON>/liushui_10"}, {"time": 0.8667, "name": "shui<PERSON><PERSON>/liushui_11"}, {"time": 0.9333, "name": "shuili<PERSON>/liushui_12"}, {"time": 1.0333, "name": "shui<PERSON><PERSON>/liushui_13"}, {"time": 1.1333, "name": "shui<PERSON><PERSON>/liushui_14"}, {"time": 1.2, "name": "shui<PERSON><PERSON>/liushui_15"}, {"time": 1.3, "name": "shui<PERSON><PERSON>/liushui_16"}, {"time": 1.3667, "name": "shuiliu/liushui_1"}, {"time": 1.4667, "name": "shuiliu/liushui_02"}, {"time": 1.5667, "name": "shuiliu/liushui_03"}, {"time": 1.6333, "name": "shuiliu/liushui_04"}, {"time": 1.7333, "name": "shuiliu/liushui_05"}, {"time": 1.8, "name": "shuiliu/liushui_06"}, {"time": 1.9, "name": "shuiliu/liushui_07"}, {"time": 1.9667, "name": "shuiliu/liushui_08"}, {"time": 2.0667, "name": "shuiliu/liushui_09"}, {"time": 2.1667, "name": "shuili<PERSON>/liushui_10"}, {"time": 2.2333, "name": "shui<PERSON><PERSON>/liushui_11"}, {"time": 2.3333, "name": "shuili<PERSON>/liushui_12"}, {"time": 2.4, "name": "shui<PERSON><PERSON>/liushui_13"}, {"time": 2.5, "name": "shui<PERSON><PERSON>/liushui_14"}, {"time": 2.6, "name": "shui<PERSON><PERSON>/liushui_15"}, {"time": 2.6667, "name": "shuiliu/liushui_1"}, {"time": 2.7667, "name": "shuiliu/liushui_02"}, {"time": 2.8333, "name": "shuiliu/liushui_03"}, {"time": 2.9333, "name": "shuiliu/liushui_04"}, {"time": 3, "name": "shuiliu/liushui_05"}, {"time": 3.1, "name": "shuiliu/liushui_06"}, {"time": 3.2, "name": "shuiliu/liushui_07"}, {"time": 3.2667, "name": "shuiliu/liushui_08"}, {"time": 3.3667, "name": "shuiliu/liushui_09"}, {"time": 3.4333, "name": "shuili<PERSON>/liushui_10"}, {"time": 3.5333, "name": "shui<PERSON><PERSON>/liushui_11"}, {"time": 3.6, "name": "shuili<PERSON>/liushui_12"}, {"time": 3.7, "name": "shui<PERSON><PERSON>/liushui_13"}, {"time": 3.8, "name": "shui<PERSON><PERSON>/liushui_14"}, {"time": 3.8667, "name": "shui<PERSON><PERSON>/liushui_15"}, {"time": 3.9667, "name": "shui<PERSON><PERSON>/liushui_16"}, {"time": 4.0333, "name": "shuiliu/liushui_1"}, {"time": 4.1333, "name": "shuiliu/liushui_02"}, {"time": 4.2333, "name": "shuiliu/liushui_03"}, {"time": 4.3, "name": "shuiliu/liushui_04"}, {"time": 4.4, "name": "shuiliu/liushui_05"}, {"time": 4.4667, "name": "shuiliu/liushui_06"}, {"time": 4.5667, "name": "shuiliu/liushui_07"}, {"time": 4.6333, "name": "shuiliu/liushui_08"}, {"time": 4.7333, "name": "shuiliu/liushui_09"}, {"time": 4.8333, "name": "shuili<PERSON>/liushui_10"}, {"time": 4.9, "name": "shui<PERSON><PERSON>/liushui_11"}, {"time": 5, "name": "shuili<PERSON>/liushui_12"}, {"time": 5.0667, "name": "shui<PERSON><PERSON>/liushui_13"}, {"time": 5.1667, "name": "shui<PERSON><PERSON>/liushui_14"}, {"time": 5.2667, "name": "shui<PERSON><PERSON>/liushui_15"}, {"time": 5.3333, "name": "shui<PERSON><PERSON>/liushui_16"}]}, "kaishijm033": {"color": [{"color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 0.8667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.9, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.6667, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 3.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 4.5667, "color": "ffffffff", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.3333, "color": "ffffff00"}]}, "kaishijm4": {"color": [{"color": "ffffffa1", "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.1333, "color": "ffffffff", "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "color": "ffffffa1", "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 3.4667, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.8, "color": "ffffffff", "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 5.3333, "color": "ffffffa1"}]}, "kaishijm30": {"color": [{"color": "ffffffc1", "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.8, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "color": "ffffff00", "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 5.3333, "color": "ffffffc1"}]}, "kaishijm10": {"color": [{"color": "fffffffd", "curve": 0.337, "c2": 0.66, "c3": 0.671}, {"time": 0.0333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.7, "color": "ffffff00", "curve": 0.249, "c3": 0.745, "c4": 0.98}, {"time": 5.3333, "color": "fffffffd"}]}, "kaishijm16": {"color": [{"color": "ffffffc1", "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.8, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "color": "ffffff00", "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 5.3333, "color": "ffffffc1"}]}, "kaishijm030": {"color": [{"color": "ffffff3d", "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.8, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 3.4667, "color": "ffffffff", "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 5.3333, "color": "ffffff3d"}]}, "kaishijm19": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffffff"}]}, "kaishijm011": {"color": [{"color": "ffffff6e", "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 1.2, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 3.8667, "color": "ffffffff", "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 5.3333, "color": "ffffff6e"}]}, "kaishijm12": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffffff"}]}, "kaishijm028": {"color": [{"color": "ffffff7b", "curve": 0.376, "c2": 0.51, "c3": 0.749}, {"time": 1.3, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 3.9667, "color": "ffffffff", "curve": 0.249, "c3": 0.626, "c4": 0.51}, {"time": 5.3333, "color": "ffffff7b"}]}, "kaishijm04": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffff00"}]}, "kaishijm023_2": {"color": [{"color": "ffffffb9", "curve": 0.35, "c2": 0.39, "c3": 0.757}, {"time": 0.9, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.2333, "color": "ffffffff", "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "color": "ffffffb9", "curve": 0.35, "c2": 0.39, "c3": 0.757}, {"time": 3.5667, "color": "ffffff00", "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.9, "color": "ffffffff", "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 5.3333, "color": "ffffffb9"}]}, "kaishijm07": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffff00"}]}}, "bones": {"shui2": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 2.6333, "x": 1.476, "y": 1.476, "curve": "stepped"}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "x": 1.476, "y": 1.476, "curve": "stepped"}, {"time": 5.3333}]}, "shui4": {"translate": [{"x": -0.15, "y": 2.61, "curve": 0.234, "c2": 0.58, "c3": 0.528}, {"time": 0.4333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.7667, "x": -0.77, "y": 13.48, "curve": 0.604, "c2": 0.01, "c3": 0.729, "c4": 0.61}, {"time": 2.6667, "x": -0.15, "y": 2.61, "curve": 0.234, "c2": 0.58, "c3": 0.528}, {"time": 3.1, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.4333, "x": -0.77, "y": 13.48, "curve": 0.604, "c2": 0.01, "c3": 0.729, "c4": 0.61}, {"time": 5.3333, "x": -0.15, "y": 2.61}]}, "shui3": {"translate": [{"curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.3333, "x": 2.97, "y": 11.47, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.6667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4, "x": 2.97, "y": 11.47, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.3333}]}, "shui5": {"translate": [{"x": 7.44, "y": 5.14, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 0.8, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.1333, "x": 10.15, "y": 7.01, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 2.6667, "x": 7.44, "y": 5.14, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 3.4667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.8, "x": 10.15, "y": 7.01, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 5.3333, "x": 7.44, "y": 5.14}]}, "shui6": {"translate": [{"x": 2.5, "y": 9.65, "curve": 0.309, "c2": 0.39, "c3": 0.422}, {"time": 0.9, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.2667, "x": 2.97, "y": 11.47, "curve": 0.474, "c2": 0.01, "c3": 0.771, "c4": 0.43}, {"time": 2.6667, "x": 2.5, "y": 9.65, "curve": 0.309, "c2": 0.39, "c3": 0.422}, {"time": 3.5667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.9333, "x": 2.97, "y": 11.47, "curve": 0.474, "c2": 0.01, "c3": 0.771, "c4": 0.43}, {"time": 5.3333, "x": 2.5, "y": 9.65}]}, "shui7": {"translate": [{"x": 2.43, "y": 2.94, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 0.8, "x": 9.1, "y": 10.99, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.1333, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 2.6667, "x": 2.43, "y": 2.94, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 3.4667, "x": 9.1, "y": 10.99, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.8, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 5.3333, "x": 2.43, "y": 2.94}]}, "shui8": {"translate": [{"x": 1.24, "y": 4.77, "curve": 0.24, "c2": 0.49, "c3": 0.451}, {"time": 0.7, "x": 2.97, "y": 11.47, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.0333, "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 2.6667, "x": 1.24, "y": 4.77, "curve": 0.24, "c2": 0.49, "c3": 0.451}, {"time": 3.3667, "x": 2.97, "y": 11.47, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.7, "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 5.3333, "x": 1.24, "y": 4.77}]}, "shui9": {"scale": [{"x": 1.369, "y": 1.369, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.7667, "x": 1.476, "y": 1.476, "curve": "stepped"}, {"time": 0.8, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 2.6667, "x": 1.369, "y": 1.369, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 3.4333, "x": 1.476, "y": 1.476, "curve": "stepped"}, {"time": 3.4667, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 5.3333, "x": 1.369, "y": 1.369}]}, "shui10": {"translate": [{"x": 2.18, "y": 8.4, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 0.8, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.1333, "x": 2.97, "y": 11.47, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 2.6667, "x": 2.18, "y": 8.4, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 3.4667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.8, "x": 2.97, "y": 11.47, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 5.3333, "x": 2.18, "y": 8.4}]}, "shui11": {"translate": [{"x": -0.77, "y": 13.33, "curve": 0.548, "c2": 0.12, "c3": 0.704, "c4": 0.65}, {"time": 0.8, "x": -0.15, "y": 2.61, "curve": 0.234, "c2": 0.58, "c3": 0.528}, {"time": 1.2333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.5667, "x": -0.77, "y": 13.48, "curve": 0.366, "c2": 0.03, "c3": 0.697, "c4": 0.38}, {"time": 2.6667, "x": -0.77, "y": 13.33, "curve": 0.548, "c2": 0.12, "c3": 0.704, "c4": 0.65}, {"time": 3.4667, "x": -0.15, "y": 2.61, "curve": 0.234, "c2": 0.58, "c3": 0.528}, {"time": 3.9, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.2333, "x": -0.77, "y": 13.48, "curve": 0.366, "c2": 0.03, "c3": 0.697, "c4": 0.38}, {"time": 5.3333, "x": -0.77, "y": 13.33}]}, "shui12": {"translate": [{"x": 9.44, "y": 6.52, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 0.2667, "x": 10.15, "y": 7.01, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 0.8, "x": 7.44, "y": 5.14, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 1.6, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 2.6667, "x": 9.44, "y": 6.52, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 2.9333, "x": 10.15, "y": 7.01, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 3.4667, "x": 7.44, "y": 5.14, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 4.2667, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 5.3333, "x": 9.44, "y": 6.52}]}, "shui13": {"translate": [{"x": 2.54, "y": 9.82, "curve": 0.242, "c2": 0.59, "c3": 0.545}, {"time": 0.3667, "x": 2.97, "y": 11.47, "curve": 0.474, "c2": 0.01, "c3": 0.771, "c4": 0.43}, {"time": 0.8, "x": 2.5, "y": 9.65, "curve": 0.309, "c2": 0.39, "c3": 0.422}, {"time": 1.7, "curve": 0.609, "c2": 0.01, "c3": 0.707, "c4": 0.64}, {"time": 2.6667, "x": 2.54, "y": 9.82, "curve": 0.242, "c2": 0.59, "c3": 0.545}, {"time": 3.0333, "x": 2.97, "y": 11.47, "curve": 0.474, "c2": 0.01, "c3": 0.771, "c4": 0.43}, {"time": 3.4667, "x": 2.5, "y": 9.65, "curve": 0.309, "c2": 0.39, "c3": 0.422}, {"time": 4.3667, "curve": 0.609, "c2": 0.01, "c3": 0.707, "c4": 0.64}, {"time": 5.3333, "x": 2.54, "y": 9.82}]}, "shui14": {"translate": [{"x": 0.64, "y": 0.77, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 0.2667, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 0.8, "x": 2.43, "y": 2.94, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 1.6, "x": 9.1, "y": 10.99, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 2.6667, "x": 0.64, "y": 0.77, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 2.9333, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 3.4667, "x": 2.43, "y": 2.94, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 4.2667, "x": 9.1, "y": 10.99, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 5.3333, "x": 0.64, "y": 0.77}]}, "shui15": {"translate": [{"x": 0.07, "y": 0.28, "curve": 0.288, "c2": 0.64, "c3": 0.617}, {"time": 0.1667, "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 0.8, "x": 1.24, "y": 4.77, "curve": 0.24, "c2": 0.49, "c3": 0.451}, {"time": 1.5, "x": 2.97, "y": 11.47, "curve": 0.605, "c2": 0.01, "c3": 0.589, "c4": 0.81}, {"time": 2.6667, "x": 0.07, "y": 0.28, "curve": 0.288, "c2": 0.64, "c3": 0.617}, {"time": 2.8333, "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 3.4667, "x": 1.24, "y": 4.77, "curve": 0.24, "c2": 0.49, "c3": 0.451}, {"time": 4.1667, "x": 2.97, "y": 11.47, "curve": 0.605, "c2": 0.01, "c3": 0.589, "c4": 0.81}, {"time": 5.3333, "x": 0.07, "y": 0.28}]}, "shui16": {"scale": [{"x": 1.369, "y": 1.369, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.7667, "x": 1.476, "y": 1.476, "curve": "stepped"}, {"time": 0.8, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 2.6667, "x": 1.369, "y": 1.369, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 3.4333, "x": 1.476, "y": 1.476, "curve": "stepped"}, {"time": 3.4667, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 5.3333, "x": 1.369, "y": 1.369}]}, "shui17": {"translate": [{"x": 2.18, "y": 8.4, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 0.8, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.1333, "x": 2.97, "y": 11.47, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 2.6667, "x": 2.18, "y": 8.4, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 3.4667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.8, "x": 2.97, "y": 11.47, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 5.3333, "x": 2.18, "y": 8.4}]}, "shui18": {"translate": [{"x": -0.77, "y": 13.33, "curve": 0.548, "c2": 0.12, "c3": 0.704, "c4": 0.65}, {"time": 0.8, "x": -0.15, "y": 2.61, "curve": 0.234, "c2": 0.58, "c3": 0.528}, {"time": 1.2333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.5667, "x": -0.77, "y": 13.48, "curve": 0.366, "c2": 0.03, "c3": 0.697, "c4": 0.38}, {"time": 2.6667, "x": -0.77, "y": 13.33, "curve": 0.548, "c2": 0.12, "c3": 0.704, "c4": 0.65}, {"time": 3.4667, "x": -0.15, "y": 2.61, "curve": 0.234, "c2": 0.58, "c3": 0.528}, {"time": 3.9, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.2333, "x": -0.77, "y": 13.48, "curve": 0.366, "c2": 0.03, "c3": 0.697, "c4": 0.38}, {"time": 5.3333, "x": -0.77, "y": 13.33}]}, "shui19": {"translate": [{"x": 9.44, "y": 6.52, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 0.2667, "x": 10.15, "y": 7.01, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 0.8, "x": 7.44, "y": 5.14, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 1.6, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 2.6667, "x": 9.44, "y": 6.52, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 2.9333, "x": 10.15, "y": 7.01, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 3.4667, "x": 7.44, "y": 5.14, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 4.2667, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 5.3333, "x": 9.44, "y": 6.52}]}, "shui20": {"translate": [{"x": 2.54, "y": 9.82, "curve": 0.242, "c2": 0.59, "c3": 0.545}, {"time": 0.3667, "x": 2.97, "y": 11.47, "curve": 0.474, "c2": 0.01, "c3": 0.771, "c4": 0.43}, {"time": 0.8, "x": 2.5, "y": 9.65, "curve": 0.309, "c2": 0.39, "c3": 0.422}, {"time": 1.7, "curve": 0.609, "c2": 0.01, "c3": 0.707, "c4": 0.64}, {"time": 2.6667, "x": 2.54, "y": 9.82, "curve": 0.242, "c2": 0.59, "c3": 0.545}, {"time": 3.0333, "x": 2.97, "y": 11.47, "curve": 0.474, "c2": 0.01, "c3": 0.771, "c4": 0.43}, {"time": 3.4667, "x": 2.5, "y": 9.65, "curve": 0.309, "c2": 0.39, "c3": 0.422}, {"time": 4.3667, "curve": 0.609, "c2": 0.01, "c3": 0.707, "c4": 0.64}, {"time": 5.3333, "x": 2.54, "y": 9.82}]}, "shui21": {"translate": [{"x": 0.64, "y": 0.77, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 0.2667, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 0.8, "x": 2.43, "y": 2.94, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 1.6, "x": 9.1, "y": 10.99, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 2.6667, "x": 0.64, "y": 0.77, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 2.9333, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 3.4667, "x": 2.43, "y": 2.94, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 4.2667, "x": 9.1, "y": 10.99, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 5.3333, "x": 0.64, "y": 0.77}]}, "shui22": {"translate": [{"x": 0.07, "y": 0.28, "curve": 0.288, "c2": 0.64, "c3": 0.617}, {"time": 0.1667, "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 0.8, "x": 1.24, "y": 4.77, "curve": 0.24, "c2": 0.49, "c3": 0.451}, {"time": 1.5, "x": 2.97, "y": 11.47, "curve": 0.605, "c2": 0.01, "c3": 0.589, "c4": 0.81}, {"time": 2.6667, "x": 0.07, "y": 0.28, "curve": 0.288, "c2": 0.64, "c3": 0.617}, {"time": 2.8333, "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 3.4667, "x": 1.24, "y": 4.77, "curve": 0.24, "c2": 0.49, "c3": 0.451}, {"time": 4.1667, "x": 2.97, "y": 11.47, "curve": 0.605, "c2": 0.01, "c3": 0.589, "c4": 0.81}, {"time": 5.3333, "x": 0.07, "y": 0.28}]}, "shui23": {"translate": [{"x": 2.18, "y": 8.4, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 0.8, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.1333, "x": 2.97, "y": 11.47, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 2.6667, "x": 2.18, "y": 8.4, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 3.4667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.8, "x": 2.97, "y": 11.47, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 5.3333, "x": 2.18, "y": 8.4}]}, "shui24": {"translate": [{"x": -0.77, "y": 13.33, "curve": 0.548, "c2": 0.12, "c3": 0.704, "c4": 0.65}, {"time": 0.8, "x": -0.15, "y": 2.61, "curve": 0.234, "c2": 0.58, "c3": 0.528}, {"time": 1.2333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.5667, "x": -0.77, "y": 13.48, "curve": 0.366, "c2": 0.03, "c3": 0.697, "c4": 0.38}, {"time": 2.6667, "x": -0.77, "y": 13.33, "curve": 0.548, "c2": 0.12, "c3": 0.704, "c4": 0.65}, {"time": 3.4667, "x": -0.15, "y": 2.61, "curve": 0.234, "c2": 0.58, "c3": 0.528}, {"time": 3.9, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.2333, "x": -0.77, "y": 13.48, "curve": 0.366, "c2": 0.03, "c3": 0.697, "c4": 0.38}, {"time": 5.3333, "x": -0.77, "y": 13.33}]}, "shui25": {"translate": [{"x": 9.44, "y": 6.52, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 0.2667, "x": 10.15, "y": 7.01, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 0.8, "x": 7.44, "y": 5.14, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 1.6, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 2.6667, "x": 9.44, "y": 6.52, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 2.9333, "x": 10.15, "y": 7.01, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 3.4667, "x": 7.44, "y": 5.14, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 4.2667, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 5.3333, "x": 9.44, "y": 6.52}]}, "shui26": {"translate": [{"x": 2.54, "y": 9.82, "curve": 0.242, "c2": 0.59, "c3": 0.545}, {"time": 0.3667, "x": 2.97, "y": 11.47, "curve": 0.474, "c2": 0.01, "c3": 0.771, "c4": 0.43}, {"time": 0.8, "x": 2.5, "y": 9.65, "curve": 0.309, "c2": 0.39, "c3": 0.422}, {"time": 1.7, "curve": 0.609, "c2": 0.01, "c3": 0.707, "c4": 0.64}, {"time": 2.6667, "x": 2.54, "y": 9.82, "curve": 0.242, "c2": 0.59, "c3": 0.545}, {"time": 3.0333, "x": 2.97, "y": 11.47, "curve": 0.474, "c2": 0.01, "c3": 0.771, "c4": 0.43}, {"time": 3.4667, "x": 2.5, "y": 9.65, "curve": 0.309, "c2": 0.39, "c3": 0.422}, {"time": 4.3667, "curve": 0.609, "c2": 0.01, "c3": 0.707, "c4": 0.64}, {"time": 5.3333, "x": 2.54, "y": 9.82}]}, "shui27": {"translate": [{"x": 0.64, "y": 0.77, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 0.2667, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 0.8, "x": 2.43, "y": 2.94, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 1.6, "x": 9.1, "y": 10.99, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 2.6667, "x": 0.64, "y": 0.77, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 2.9333, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 3.4667, "x": 2.43, "y": 2.94, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 4.2667, "x": 9.1, "y": 10.99, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 5.3333, "x": 0.64, "y": 0.77}]}, "shui28": {"translate": [{"x": 0.07, "y": 0.28, "curve": 0.288, "c2": 0.64, "c3": 0.617}, {"time": 0.1667, "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 0.8, "x": 1.24, "y": 4.77, "curve": 0.24, "c2": 0.49, "c3": 0.451}, {"time": 1.5, "x": 2.97, "y": 11.47, "curve": 0.605, "c2": 0.01, "c3": 0.589, "c4": 0.81}, {"time": 2.6667, "x": 0.07, "y": 0.28, "curve": 0.288, "c2": 0.64, "c3": 0.617}, {"time": 2.8333, "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 3.4667, "x": 1.24, "y": 4.77, "curve": 0.24, "c2": 0.49, "c3": 0.451}, {"time": 4.1667, "x": 2.97, "y": 11.47, "curve": 0.605, "c2": 0.01, "c3": 0.589, "c4": 0.81}, {"time": 5.3333, "x": 0.07, "y": 0.28}]}, "shui29": {"scale": [{"x": 1.085, "y": 1.085, "curve": 0.312, "c2": 0.27, "c3": 0.679, "c4": 0.71}, {"time": 1.2333, "x": 1.369, "y": 1.369, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 2, "x": 1.476, "y": 1.476, "curve": "stepped"}, {"time": 2.0333, "curve": 0.277, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": 1.085, "y": 1.085, "curve": 0.312, "c2": 0.27, "c3": 0.679, "c4": 0.71}, {"time": 3.9, "x": 1.369, "y": 1.369, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 4.6667, "x": 1.476, "y": 1.476, "curve": "stepped"}, {"time": 4.7, "curve": 0.277, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "x": 1.085, "y": 1.085}]}, "shui30": {"translate": [{"x": 1.24, "y": 4.77, "curve": 0.24, "c2": 0.49, "c3": 0.451}, {"time": 0.7, "x": 2.97, "y": 11.47, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 1.2333, "x": 2.18, "y": 8.4, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 2.0333, "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 2.6667, "x": 1.24, "y": 4.77, "curve": 0.24, "c2": 0.49, "c3": 0.451}, {"time": 3.3667, "x": 2.97, "y": 11.47, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 3.9, "x": 2.18, "y": 8.4, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 4.7, "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 5.3333, "x": 1.24, "y": 4.77}]}, "shui31": {"translate": [{"x": -0.03, "y": 0.45, "curve": 0.429, "c2": 0.25, "c3": 0.433}, {"time": 1.1333, "x": -0.77, "y": 13.48, "curve": 0.366, "c2": 0.03, "c3": 0.697, "c4": 0.38}, {"time": 1.2333, "x": -0.77, "y": 13.33, "curve": 0.548, "c2": 0.12, "c3": 0.704, "c4": 0.65}, {"time": 2.0333, "x": -0.15, "y": 2.61, "curve": 0.234, "c2": 0.58, "c3": 0.528}, {"time": 2.4667, "curve": 0.4, "c2": 0.01, "c3": 0.726, "c4": 0.39}, {"time": 2.6667, "x": -0.03, "y": 0.45, "curve": 0.429, "c2": 0.25, "c3": 0.433}, {"time": 3.8, "x": -0.77, "y": 13.48, "curve": 0.366, "c2": 0.03, "c3": 0.697, "c4": 0.38}, {"time": 3.9, "x": -0.77, "y": 13.33, "curve": 0.548, "c2": 0.12, "c3": 0.704, "c4": 0.65}, {"time": 4.7, "x": -0.15, "y": 2.61, "curve": 0.234, "c2": 0.58, "c3": 0.528}, {"time": 5.1333, "curve": 0.4, "c2": 0.01, "c3": 0.726, "c4": 0.39}, {"time": 5.3333, "x": -0.03, "y": 0.45}]}, "shui32": {"translate": [{"x": 0.22, "y": 0.15, "curve": 0.288, "c2": 0.64, "c3": 0.617}, {"time": 0.1667, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 1.2333, "x": 9.44, "y": 6.52, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 1.5, "x": 10.15, "y": 7.01, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 2.0333, "x": 7.44, "y": 5.14, "curve": 0.304, "c2": 0.38, "c3": 0.515, "c4": 0.83}, {"time": 2.6667, "x": 0.22, "y": 0.15, "curve": 0.288, "c2": 0.64, "c3": 0.617}, {"time": 2.8333, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 3.9, "x": 9.44, "y": 6.52, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 4.1667, "x": 10.15, "y": 7.01, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 4.7, "x": 7.44, "y": 5.14, "curve": 0.304, "c2": 0.38, "c3": 0.515, "c4": 0.83}, {"time": 5.3333, "x": 0.22, "y": 0.15}]}, "shui33": {"translate": [{"x": 0.16, "y": 0.63, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 0.2667, "curve": 0.609, "c2": 0.01, "c3": 0.707, "c4": 0.64}, {"time": 1.2333, "x": 2.54, "y": 9.82, "curve": 0.242, "c2": 0.59, "c3": 0.545}, {"time": 1.6, "x": 2.97, "y": 11.47, "curve": 0.474, "c2": 0.01, "c3": 0.771, "c4": 0.43}, {"time": 2.0333, "x": 2.5, "y": 9.65, "curve": 0.367, "c2": 0.32, "c3": 0.57, "c4": 0.77}, {"time": 2.6667, "x": 0.16, "y": 0.63, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 2.9333, "curve": 0.609, "c2": 0.01, "c3": 0.707, "c4": 0.64}, {"time": 3.9, "x": 2.54, "y": 9.82, "curve": 0.242, "c2": 0.59, "c3": 0.545}, {"time": 4.2667, "x": 2.97, "y": 11.47, "curve": 0.474, "c2": 0.01, "c3": 0.771, "c4": 0.43}, {"time": 4.7, "x": 2.5, "y": 9.65, "curve": 0.367, "c2": 0.32, "c3": 0.57, "c4": 0.77}, {"time": 5.3333, "x": 0.16, "y": 0.63}]}, "shui34": {"translate": [{"x": 8.9, "y": 10.76, "curve": 0.288, "c2": 0.64, "c3": 0.617}, {"time": 0.1667, "x": 9.1, "y": 10.99, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 1.2333, "x": 0.64, "y": 0.77, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 1.5, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 2.0333, "x": 2.43, "y": 2.94, "curve": 0.304, "c2": 0.38, "c3": 0.515, "c4": 0.83}, {"time": 2.6667, "x": 8.9, "y": 10.76, "curve": 0.288, "c2": 0.64, "c3": 0.617}, {"time": 2.8333, "x": 9.1, "y": 10.99, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 3.9, "x": 0.64, "y": 0.77, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 4.1667, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 4.7, "x": 2.43, "y": 2.94, "curve": 0.304, "c2": 0.38, "c3": 0.515, "c4": 0.83}, {"time": 5.3333, "x": 8.9, "y": 10.76}]}, "shui35": {"translate": [{"x": 2.96, "y": 11.42, "curve": 0.318, "c2": 0.66, "c3": 0.651}, {"time": 0.0667, "x": 2.97, "y": 11.47, "curve": 0.605, "c2": 0.01, "c3": 0.589, "c4": 0.81}, {"time": 1.2333, "x": 0.07, "y": 0.28, "curve": 0.288, "c2": 0.64, "c3": 0.617}, {"time": 1.4, "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 2.0333, "x": 1.24, "y": 4.77, "curve": 0.252, "c2": 0.46, "c3": 0.476, "c4": 0.93}, {"time": 2.6667, "x": 2.96, "y": 11.42, "curve": 0.318, "c2": 0.66, "c3": 0.651}, {"time": 2.7333, "x": 2.97, "y": 11.47, "curve": 0.605, "c2": 0.01, "c3": 0.589, "c4": 0.81}, {"time": 3.9, "x": 0.07, "y": 0.28, "curve": 0.288, "c2": 0.64, "c3": 0.617}, {"time": 4.0667, "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 4.7, "x": 1.24, "y": 4.77, "curve": 0.252, "c2": 0.46, "c3": 0.476, "c4": 0.93}, {"time": 5.3333, "x": 2.96, "y": 11.42}]}, "shui36": {"translate": [{"x": 1.24, "y": 4.77, "curve": 0.24, "c2": 0.49, "c3": 0.451}, {"time": 0.7, "x": 2.97, "y": 11.47, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 1.2333, "x": 2.18, "y": 8.4, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 2.0333, "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 2.6667, "x": 1.24, "y": 4.77, "curve": 0.24, "c2": 0.49, "c3": 0.451}, {"time": 3.3667, "x": 2.97, "y": 11.47, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 3.9, "x": 2.18, "y": 8.4, "curve": 0.267, "c2": 0.45, "c3": 0.432}, {"time": 4.7, "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 5.3333, "x": 1.24, "y": 4.77}]}, "shui37": {"translate": [{"x": -0.03, "y": 0.45, "curve": 0.429, "c2": 0.25, "c3": 0.433}, {"time": 1.1333, "x": -0.77, "y": 13.48, "curve": 0.366, "c2": 0.03, "c3": 0.697, "c4": 0.38}, {"time": 1.2333, "x": -0.77, "y": 13.33, "curve": 0.548, "c2": 0.12, "c3": 0.704, "c4": 0.65}, {"time": 2.0333, "x": -0.15, "y": 2.61, "curve": 0.234, "c2": 0.58, "c3": 0.528}, {"time": 2.4667, "curve": 0.4, "c2": 0.01, "c3": 0.726, "c4": 0.39}, {"time": 2.6667, "x": -0.03, "y": 0.45, "curve": 0.429, "c2": 0.25, "c3": 0.433}, {"time": 3.8, "x": -0.77, "y": 13.48, "curve": 0.366, "c2": 0.03, "c3": 0.697, "c4": 0.38}, {"time": 3.9, "x": -0.77, "y": 13.33, "curve": 0.548, "c2": 0.12, "c3": 0.704, "c4": 0.65}, {"time": 4.7, "x": -0.15, "y": 2.61, "curve": 0.234, "c2": 0.58, "c3": 0.528}, {"time": 5.1333, "curve": 0.4, "c2": 0.01, "c3": 0.726, "c4": 0.39}, {"time": 5.3333, "x": -0.03, "y": 0.45}]}, "shui38": {"translate": [{"x": 0.22, "y": 0.15, "curve": 0.288, "c2": 0.64, "c3": 0.617}, {"time": 0.1667, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 1.2333, "x": 9.44, "y": 6.52, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 1.5, "x": 10.15, "y": 7.01, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 2.0333, "x": 7.44, "y": 5.14, "curve": 0.304, "c2": 0.38, "c3": 0.515, "c4": 0.83}, {"time": 2.6667, "x": 0.22, "y": 0.15, "curve": 0.288, "c2": 0.64, "c3": 0.617}, {"time": 2.8333, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 3.9, "x": 9.44, "y": 6.52, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 4.1667, "x": 10.15, "y": 7.01, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 4.7, "x": 7.44, "y": 5.14, "curve": 0.304, "c2": 0.38, "c3": 0.515, "c4": 0.83}, {"time": 5.3333, "x": 0.22, "y": 0.15}]}, "shui39": {"translate": [{"x": 0.16, "y": 0.63, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 0.2667, "curve": 0.609, "c2": 0.01, "c3": 0.707, "c4": 0.64}, {"time": 1.2333, "x": 2.54, "y": 9.82, "curve": 0.242, "c2": 0.59, "c3": 0.545}, {"time": 1.6, "x": 2.97, "y": 11.47, "curve": 0.474, "c2": 0.01, "c3": 0.771, "c4": 0.43}, {"time": 2.0333, "x": 2.5, "y": 9.65, "curve": 0.367, "c2": 0.32, "c3": 0.57, "c4": 0.77}, {"time": 2.6667, "x": 0.16, "y": 0.63, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 2.9333, "curve": 0.609, "c2": 0.01, "c3": 0.707, "c4": 0.64}, {"time": 3.9, "x": 2.54, "y": 9.82, "curve": 0.242, "c2": 0.59, "c3": 0.545}, {"time": 4.2667, "x": 2.97, "y": 11.47, "curve": 0.474, "c2": 0.01, "c3": 0.771, "c4": 0.43}, {"time": 4.7, "x": 2.5, "y": 9.65, "curve": 0.367, "c2": 0.32, "c3": 0.57, "c4": 0.77}, {"time": 5.3333, "x": 0.16, "y": 0.63}]}, "shui40": {"translate": [{"x": 8.9, "y": 10.76, "curve": 0.288, "c2": 0.64, "c3": 0.617}, {"time": 0.1667, "x": 9.1, "y": 10.99, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 1.2333, "x": 0.64, "y": 0.77, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 1.5, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 2.0333, "x": 2.43, "y": 2.94, "curve": 0.304, "c2": 0.38, "c3": 0.515, "c4": 0.83}, {"time": 2.6667, "x": 8.9, "y": 10.76, "curve": 0.288, "c2": 0.64, "c3": 0.617}, {"time": 2.8333, "x": 9.1, "y": 10.99, "curve": 0.612, "c2": 0.01, "c3": 0.654, "c4": 0.72}, {"time": 3.9, "x": 0.64, "y": 0.77, "curve": 0.262, "c2": 0.62, "c3": 0.581}, {"time": 4.1667, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 4.7, "x": 2.43, "y": 2.94, "curve": 0.304, "c2": 0.38, "c3": 0.515, "c4": 0.83}, {"time": 5.3333, "x": 8.9, "y": 10.76}]}, "shui41": {"translate": [{"x": 2.96, "y": 11.42, "curve": 0.318, "c2": 0.66, "c3": 0.651}, {"time": 0.0667, "x": 2.97, "y": 11.47, "curve": 0.605, "c2": 0.01, "c3": 0.589, "c4": 0.81}, {"time": 1.2333, "x": 0.07, "y": 0.28, "curve": 0.288, "c2": 0.64, "c3": 0.617}, {"time": 1.4, "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 2.0333, "x": 1.24, "y": 4.77, "curve": 0.252, "c2": 0.46, "c3": 0.476, "c4": 0.93}, {"time": 2.6667, "x": 2.96, "y": 11.42, "curve": 0.318, "c2": 0.66, "c3": 0.651}, {"time": 2.7333, "x": 2.97, "y": 11.47, "curve": 0.605, "c2": 0.01, "c3": 0.589, "c4": 0.81}, {"time": 3.9, "x": 0.07, "y": 0.28, "curve": 0.288, "c2": 0.64, "c3": 0.617}, {"time": 4.0667, "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 4.7, "x": 1.24, "y": 4.77, "curve": 0.252, "c2": 0.46, "c3": 0.476, "c4": 0.93}, {"time": 5.3333, "x": 2.96, "y": 11.42}]}, "shui42": {"scale": [{"x": 1.075, "y": 1.075, "curve": 0.327, "c2": 0.31, "c3": 0.757}, {"time": 2.0333, "x": 1.476, "y": 1.476, "curve": "stepped"}, {"time": 2.1, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 2.6667, "x": 1.075, "y": 1.075, "curve": 0.327, "c2": 0.31, "c3": 0.757}, {"time": 4.7, "x": 1.476, "y": 1.476, "curve": "stepped"}, {"time": 4.7667, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 5.3333, "x": 1.075, "y": 1.075}]}, "shui43": {"translate": [{"x": 1, "y": 3.85, "curve": 0.252, "c2": 0.47, "c3": 0.441}, {"time": 0.7667, "x": 2.97, "y": 11.47, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.1, "curve": 0.528, "c2": 0.01, "c3": 0.785, "c4": 0.48}, {"time": 2.6667, "x": 1, "y": 3.85, "curve": 0.252, "c2": 0.47, "c3": 0.441}, {"time": 3.4333, "x": 2.97, "y": 11.47, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.7667, "curve": 0.528, "c2": 0.01, "c3": 0.785, "c4": 0.48}, {"time": 5.3333, "x": 1, "y": 3.85}]}, "shui44": {"translate": [{"x": -0.02, "y": 0.31, "curve": 0.465, "c2": 0.2, "c3": 0.442}, {"time": 1.1667, "x": -0.77, "y": 13.48, "curve": 0.604, "c2": 0.01, "c3": 0.729, "c4": 0.61}, {"time": 2.1, "x": -0.15, "y": 2.61, "curve": 0.234, "c2": 0.58, "c3": 0.528}, {"time": 2.5333, "curve": 0.383, "c2": 0.02, "c3": 0.712, "c4": 0.38}, {"time": 2.6667, "x": -0.02, "y": 0.31, "curve": 0.465, "c2": 0.2, "c3": 0.442}, {"time": 3.8333, "x": -0.77, "y": 13.48, "curve": 0.604, "c2": 0.01, "c3": 0.729, "c4": 0.61}, {"time": 4.7667, "x": -0.15, "y": 2.61, "curve": 0.234, "c2": 0.58, "c3": 0.528}, {"time": 5.2, "curve": 0.383, "c2": 0.02, "c3": 0.712, "c4": 0.38}, {"time": 5.3333, "x": -0.02, "y": 0.31}]}, "shui45": {"translate": [{"x": 0.38, "y": 0.26, "curve": 0.274, "c2": 0.63, "c3": 0.599}, {"time": 0.2, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.5667, "x": 10.15, "y": 7.01, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 2.1, "x": 7.44, "y": 5.14, "curve": 0.315, "c2": 0.37, "c3": 0.542, "c4": 0.8}, {"time": 2.6667, "x": 0.38, "y": 0.26, "curve": 0.274, "c2": 0.63, "c3": 0.599}, {"time": 2.8667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.2333, "x": 10.15, "y": 7.01, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 4.7667, "x": 7.44, "y": 5.14, "curve": 0.315, "c2": 0.37, "c3": 0.542, "c4": 0.8}, {"time": 5.3333, "x": 0.38, "y": 0.26}]}, "shui46": {"translate": [{"x": 0.26, "y": 1, "curve": 0.251, "c2": 0.6, "c3": 0.563}, {"time": 0.3333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.6667, "x": 2.97, "y": 11.47, "curve": 0.474, "c2": 0.01, "c3": 0.771, "c4": 0.43}, {"time": 2.1, "x": 2.5, "y": 9.65, "curve": 0.375, "c2": 0.31, "c3": 0.597, "c4": 0.74}, {"time": 2.6667, "x": 0.26, "y": 1, "curve": 0.251, "c2": 0.6, "c3": 0.563}, {"time": 3, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.3333, "x": 2.97, "y": 11.47, "curve": 0.474, "c2": 0.01, "c3": 0.771, "c4": 0.43}, {"time": 4.7667, "x": 2.5, "y": 9.65, "curve": 0.375, "c2": 0.31, "c3": 0.597, "c4": 0.74}, {"time": 5.3333, "x": 0.26, "y": 1}]}, "shui47": {"translate": [{"x": 8.76, "y": 10.58, "curve": 0.274, "c2": 0.63, "c3": 0.599}, {"time": 0.2, "x": 9.1, "y": 10.99, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.5667, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 2.1, "x": 2.43, "y": 2.94, "curve": 0.315, "c2": 0.37, "c3": 0.542, "c4": 0.8}, {"time": 2.6667, "x": 8.76, "y": 10.58, "curve": 0.274, "c2": 0.63, "c3": 0.599}, {"time": 2.8667, "x": 9.1, "y": 10.99, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.2333, "curve": 0.51, "c2": 0.01, "c3": 0.783, "c4": 0.46}, {"time": 4.7667, "x": 2.43, "y": 2.94, "curve": 0.315, "c2": 0.37, "c3": 0.542, "c4": 0.8}, {"time": 5.3333, "x": 8.76, "y": 10.58}]}, "shui48": {"translate": [{"x": 2.94, "y": 11.37, "curve": 0.303, "c2": 0.65, "c3": 0.634}, {"time": 0.1, "x": 2.97, "y": 11.47, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.4333, "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 2.1, "x": 1.24, "y": 4.77, "curve": 0.264, "c2": 0.43, "c3": 0.501, "c4": 0.87}, {"time": 2.6667, "x": 2.94, "y": 11.37, "curve": 0.303, "c2": 0.65, "c3": 0.634}, {"time": 2.7667, "x": 2.97, "y": 11.47, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.1, "curve": 0.545, "c2": 0.01, "c3": 0.784, "c4": 0.49}, {"time": 4.7667, "x": 1.24, "y": 4.77, "curve": 0.264, "c2": 0.43, "c3": 0.501, "c4": 0.87}, {"time": 5.3333, "x": 2.94, "y": 11.37}]}, "feiniao": {"rotate": [{"time": 2.6667}, {"time": 5.3333, "angle": 83.55}]}, "bone2": {"rotate": [{"angle": 5.56, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 5.56, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 5.56, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 5.56, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "angle": 5.56}]}, "bone3": {"rotate": [{"angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": 5.56, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1, "angle": -4.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3, "angle": 0.63, "curve": "stepped"}, {"time": 1.3333, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "angle": 5.56, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.3333, "angle": -4.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6333, "angle": 0.63, "curve": "stepped"}, {"time": 2.6667, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3, "angle": 5.56, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 3.6667, "angle": -4.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.9667, "angle": 0.63, "curve": "stepped"}, {"time": 4, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.3333, "angle": 5.56, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5, "angle": -4.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3, "angle": 0.63}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 9.37, "y": -1.04, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 9.37, "y": -1.04, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 9.37, "y": -1.04, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 9.37, "y": -1.04, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone4": {"rotate": [{"angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 5.56, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": -4.3, "curve": "stepped"}, {"time": 1.3333, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 5.56, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": -4.3, "curve": "stepped"}, {"time": 2.6667, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 5.56, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "angle": -4.3, "curve": "stepped"}, {"time": 4, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 5.56, "curve": 0.25, "c3": 0.75}, {"time": 5.3, "angle": -4.3}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 8.06, "y": -0.99, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 8.06, "y": -0.99, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 8.06, "y": -0.99, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 8.06, "y": -0.99, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "bone5": {"rotate": [{"angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 5.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3, "angle": 0.63, "curve": "stepped"}, {"time": 1.3333, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 5.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6333, "angle": 0.63, "curve": "stepped"}, {"time": 2.6667, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 5.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.9667, "angle": 0.63, "curve": "stepped"}, {"time": 4, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.3333, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": 5.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3, "angle": 0.63}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 7.79, "y": -0.36, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 7.79, "y": -0.36, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 7.79, "y": -0.36, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 7.79, "y": -0.36, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "ren10": {"rotate": [{"angle": 1.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2, "angle": 2.85, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 1.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.6667, "angle": 2.85, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": 1.42}]}, "ren17": {"rotate": [{"angle": -7.03}]}, "ren18": {"rotate": [{"angle": 13.99, "curve": "stepped"}, {"time": 2.6667, "angle": 13.99, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.3333, "angle": 13.99}]}, "ren19": {"rotate": [{"angle": 7.71}]}, "ren20": {"rotate": [{"angle": -17.99}]}, "ren14": {"rotate": [{"angle": 7.46, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 3.73, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.6667, "angle": 7.46, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": 3.73, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.3333, "angle": 7.46}]}, "ren12": {"translate": [{"x": 0.94, "y": 0.72, "curve": 0.285, "c2": 0.17, "c3": 0.657, "c4": 0.63}, {"time": 0.6667, "x": 0.33, "y": 0.26, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1.1667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.5, "x": 1, "y": 0.77, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.6667, "x": 0.94, "y": 0.72, "curve": 0.285, "c2": 0.17, "c3": 0.657, "c4": 0.63}, {"time": 3.3333, "x": 0.33, "y": 0.26, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.8333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.1667, "x": 1, "y": 0.77, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 5.3333, "x": 0.94, "y": 0.72}]}, "ren13": {"translate": [{"x": 0.69, "y": -1.08, "curve": 0.285, "c2": 0.17, "c3": 0.657, "c4": 0.63}, {"time": 0.6667, "x": 0.25, "y": -0.39, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1.1667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.5, "x": 0.73, "y": -1.15, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.6667, "x": 0.69, "y": -1.08, "curve": 0.285, "c2": 0.17, "c3": 0.657, "c4": 0.63}, {"time": 3.3333, "x": 0.25, "y": -0.39, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.8333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.1667, "x": 0.73, "y": -1.15, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 5.3333, "x": 0.69, "y": -1.08}]}, "ren2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 1.41, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 1.41, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "ren5": {"translate": [{"x": 0.34, "y": 0.37, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.8333, "x": 1.01, "y": 1.1, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": 0.34, "y": 0.37, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.5, "x": 1.01, "y": 1.1, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "x": 0.34, "y": 0.37}]}, "ren4": {"translate": [{"x": 0.29, "y": -0.34, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.8333, "x": 0.87, "y": -1, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": 0.29, "y": -0.34, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.1667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.5, "x": 0.87, "y": -1, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.3333, "x": 0.29, "y": -0.34}]}, "ren3": {"rotate": [{"angle": 0.26, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.6667, "angle": 1.41, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 0.26, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.3333, "angle": 1.41, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 5.3333, "angle": 0.26}]}, "ren7": {"rotate": [{"angle": -2.64, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.3333, "angle": -3.23, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -2.64, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.6667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5, "angle": -3.23, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -2.64}]}, "ren6": {"rotate": [{"angle": -1.62, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2, "angle": -3.23, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -1.62, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.6667, "angle": -3.23, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "angle": -1.62}]}, "ren9": {"rotate": [{"angle": 0.67, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.6667, "angle": 3.65, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.3333, "angle": 1.82, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 2.6667, "angle": 0.67, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.3333, "angle": 3.65, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5, "angle": 1.82, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 5.3333, "angle": 0.67}], "translate": [{"x": -0.34, "y": -0.17, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.6667, "x": -1.88, "y": -0.93, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.3333, "x": -0.94, "y": -0.46, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 2.6667, "x": -0.34, "y": -0.17, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.3333, "x": -1.88, "y": -0.93, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5, "x": -0.94, "y": -0.46, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 5.3333, "x": -0.34, "y": -0.17}]}, "ren21": {"rotate": [{"angle": -3.85, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 0.6667, "angle": -0.87, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.3333, "angle": -4.73, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": -3.85, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 3.3333, "angle": -0.87, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.6667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5, "angle": -4.73, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": -3.85}]}, "ren11": {"rotate": [{"angle": 2.32, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 0.6667, "angle": 0.53, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.3333, "angle": 2.85, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "angle": 2.32, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 3.3333, "angle": 0.53, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 3.6667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5, "angle": 2.85, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.3333, "angle": 2.32}]}, "ren22": {"rotate": [{"angle": -4.71, "curve": 0.324, "c2": 0.66, "c3": 0.657}, {"time": 0.0333, "angle": -4.73, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 0.6667, "angle": -2.52, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 1.3667, "curve": 0.583, "c2": 0.01, "c3": 0.504, "c4": 0.95}, {"time": 2.6667, "angle": -4.71, "curve": 0.324, "c2": 0.66, "c3": 0.657}, {"time": 2.7, "angle": -4.73, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 3.3333, "angle": -2.52, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 4.0333, "curve": 0.583, "c2": 0.01, "c3": 0.504, "c4": 0.95}, {"time": 5.3333, "angle": -4.71}]}, "ren16": {"rotate": [{"angle": 2.85, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 1.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.6667, "angle": 2.85, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.3333, "angle": 1.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.3333, "angle": 2.85}]}, "ren15": {"rotate": [{"angle": 6.68, "curve": 0.248, "c2": 0.6, "c3": 0.559}, {"time": 0.3333, "angle": 7.46, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.6667, "angle": 6.09, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.6667, "curve": 0.612, "c2": 0.01, "c3": 0.689, "c4": 0.67}, {"time": 2.6667, "angle": 6.68, "curve": 0.248, "c2": 0.6, "c3": 0.559}, {"time": 3, "angle": 7.46, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 3.3333, "angle": 6.09, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.3333, "curve": 0.612, "c2": 0.01, "c3": 0.689, "c4": 0.67}, {"time": 5.3333, "angle": 6.68}]}, "target1": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -1.34, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -1.34, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "huo1": {"scale": [{"curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.6333, "x": 1.177, "y": 1.177, "curve": "stepped"}, {"time": 2.6667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.3, "x": 1.177, "y": 1.177, "curve": "stepped"}, {"time": 5.3333}]}, "bone6": {"rotate": [{"angle": -1.29, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 5.56, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.5667, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": 5.56, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "angle": -1.29, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 2.9, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "angle": 5.56, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.2333, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "angle": 5.56, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 5.3333, "angle": -1.29}]}, "bone9": {"rotate": [{"angle": -3.48, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 0.5667, "angle": 5.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.8667, "angle": 0.63, "curve": "stepped"}, {"time": 0.9, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.2333, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": 5.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.2, "angle": 0.63, "curve": "stepped"}, {"time": 2.2333, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5667, "angle": -4.3, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 2.6667, "angle": -3.48, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 3.2333, "angle": 5.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.5333, "angle": 0.63, "curve": "stepped"}, {"time": 3.5667, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.9, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 4.5667, "angle": 5.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.8667, "angle": 0.63, "curve": "stepped"}, {"time": 4.9, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.2333, "angle": -4.3, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 5.3333, "angle": -3.48}], "translate": [{"x": 5.42, "y": -0.25, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "x": 7.79, "y": -0.36, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.5667, "x": 7.79, "y": -0.36, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "x": 5.42, "y": -0.25, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 2.9, "x": 7.79, "y": -0.36, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.2333, "x": 7.79, "y": -0.36, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 5.3333, "x": 5.42, "y": -0.25}]}, "bone8": {"rotate": [{"angle": 2.56, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": 5.56, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -4.3, "curve": "stepped"}, {"time": 0.9, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": 5.56, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "angle": -4.3, "curve": "stepped"}, {"time": 2.2333, "angle": -4.3, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "angle": 2.56, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 2.9, "angle": 5.56, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "angle": -4.3, "curve": "stepped"}, {"time": 3.5667, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 4.2333, "angle": 5.56, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": -4.3, "curve": "stepped"}, {"time": 4.9, "angle": -4.3, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 5.3333, "angle": 2.56}], "translate": [{"x": 5.61, "y": -0.69, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "x": 8.06, "y": -0.99, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.5667, "x": 8.06, "y": -0.99, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "x": 5.61, "y": -0.69, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 2.9, "x": 8.06, "y": -0.99, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.2333, "x": 8.06, "y": -0.99, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 5.3333, "x": 5.61, "y": -0.69}]}, "bone7": {"rotate": [{"angle": 5.28, "curve": 0.438, "c2": 0.23, "c3": 0.435}, {"time": 0.5667, "angle": -4.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.8667, "angle": 0.63, "curve": "stepped"}, {"time": 0.9, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.2333, "angle": 5.56, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.9, "angle": -4.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.2, "angle": 0.63, "curve": "stepped"}, {"time": 2.2333, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5667, "angle": 5.56, "curve": 0.396, "c2": 0.01, "c3": 0.723, "c4": 0.38}, {"time": 2.6667, "angle": 5.28, "curve": 0.438, "c2": 0.23, "c3": 0.435}, {"time": 3.2333, "angle": -4.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.5333, "angle": 0.63, "curve": "stepped"}, {"time": 3.5667, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.9, "angle": 5.56, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.5667, "angle": -4.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.8667, "angle": 0.63, "curve": "stepped"}, {"time": 4.9, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 5.2333, "angle": 5.56, "curve": 0.396, "c2": 0.01, "c3": 0.723, "c4": 0.38}, {"time": 5.3333, "angle": 5.28}], "translate": [{"x": 6.51, "y": -0.72, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "x": 9.37, "y": -1.04, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.5667, "x": 9.37, "y": -1.04, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "x": 6.51, "y": -0.72, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 2.9, "x": 9.37, "y": -1.04, "curve": 0.25, "c3": 0.75}, {"time": 3.5667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.2333, "x": 9.37, "y": -1.04, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 5.3333, "x": 6.51, "y": -0.72}]}, "huo2": {"scale": [{"x": 1.141, "y": 1.141, "curve": 0.233, "c2": 0.57, "c3": 0.524}, {"time": 0.8667, "x": 1.177, "y": 1.177, "curve": "stepped"}, {"time": 0.9, "curve": 0.602, "c2": 0.01, "c3": 0.734, "c4": 0.61}, {"time": 2.6667, "x": 1.141, "y": 1.141, "curve": 0.233, "c2": 0.57, "c3": 0.524}, {"time": 3.5333, "x": 1.177, "y": 1.177, "curve": "stepped"}, {"time": 3.5667, "curve": 0.602, "c2": 0.01, "c3": 0.734, "c4": 0.61}, {"time": 5.3333, "x": 1.141, "y": 1.141}]}, "bone10": {"rotate": [{"angle": -3.48, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 0.5667, "angle": 5.56, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": 5.56, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.5667, "angle": -4.3, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 2.6667, "angle": -3.48, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 3.2333, "angle": 5.56, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 4.5667, "angle": 5.56, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.2333, "angle": -4.3, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 5.3333, "angle": -3.48}]}, "bone13": {"rotate": [{"angle": 2.56, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.2333, "angle": 5.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "angle": 0.63, "curve": "stepped"}, {"time": 0.5667, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.9, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": 5.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.8667, "angle": 0.63, "curve": "stepped"}, {"time": 1.9, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.2333, "angle": -4.3, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 2.6667, "angle": 2.56, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 2.9, "angle": 5.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.2, "angle": 0.63, "curve": "stepped"}, {"time": 3.2333, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.5667, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 4.2333, "angle": 5.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.5333, "angle": 0.63, "curve": "stepped"}, {"time": 4.5667, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.9, "angle": -4.3, "curve": 0.244, "c3": 0.646, "c4": 0.59}, {"time": 5.3333, "angle": 2.56}], "translate": [{"x": 7.14, "y": -0.33, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": 7.79, "y": -0.36, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.5667, "x": 7.79, "y": -0.36, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 2.6667, "x": 7.14, "y": -0.33, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 3.2333, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "x": 7.79, "y": -0.36, "curve": 0.25, "c3": 0.75}, {"time": 4.5667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.2333, "x": 7.79, "y": -0.36, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 5.3333, "x": 7.14, "y": -0.33}]}, "bone12": {"rotate": [{"angle": 4.68, "curve": 0.308, "c2": 0.24, "c3": 0.757}, {"time": 0.5333, "angle": -4.3, "curve": "stepped"}, {"time": 0.5667, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 5.56, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -4.3, "curve": "stepped"}, {"time": 1.9, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": 5.56, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 2.6667, "angle": 4.68, "curve": 0.308, "c2": 0.24, "c3": 0.757}, {"time": 3.2, "angle": -4.3, "curve": "stepped"}, {"time": 3.2333, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "angle": 5.56, "curve": 0.25, "c3": 0.75}, {"time": 4.5333, "angle": -4.3, "curve": "stepped"}, {"time": 4.5667, "angle": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 5.2333, "angle": 5.56, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 5.3333, "angle": 4.68}], "translate": [{"x": 7.4, "y": -0.9, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": 8.06, "y": -0.99, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.5667, "x": 8.06, "y": -0.99, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 2.6667, "x": 7.4, "y": -0.9, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 3.2333, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "x": 8.06, "y": -0.99, "curve": 0.25, "c3": 0.75}, {"time": 4.5667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.2333, "x": 8.06, "y": -0.99, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 5.3333, "x": 7.4, "y": -0.9}]}, "bone11": {"rotate": [{"angle": -1.94, "curve": 0.23, "c2": 0.57, "c3": 0.515}, {"time": 0.2333, "angle": -4.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "angle": 0.63, "curve": "stepped"}, {"time": 0.5667, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.9, "angle": 5.56, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 1.5667, "angle": -4.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.8667, "angle": 0.63, "curve": "stepped"}, {"time": 1.9, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.2333, "angle": 5.56, "curve": 0.598, "c2": 0.01, "c3": 0.743, "c4": 0.59}, {"time": 2.6667, "angle": -1.94, "curve": 0.23, "c2": 0.57, "c3": 0.515}, {"time": 2.9, "angle": -4.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 3.2, "angle": 0.63, "curve": "stepped"}, {"time": 3.2333, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.5667, "angle": 5.56, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.2333, "angle": -4.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.5333, "angle": 0.63, "curve": "stepped"}, {"time": 4.5667, "angle": 0.63, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 4.9, "angle": 5.56, "curve": 0.598, "c2": 0.01, "c3": 0.743, "c4": 0.59}, {"time": 5.3333, "angle": -1.94}], "translate": [{"x": 8.59, "y": -0.95, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": 9.37, "y": -1.04, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 2.5667, "x": 9.37, "y": -1.04, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 2.6667, "x": 8.59, "y": -0.95, "curve": 0.306, "c2": 0.23, "c3": 0.756}, {"time": 3.2333, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "x": 9.37, "y": -1.04, "curve": 0.25, "c3": 0.75}, {"time": 4.5667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.2333, "x": 9.37, "y": -1.04, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 5.3333, "x": 8.59, "y": -0.95}]}, "huo3": {"scale": [{"x": 1.022, "y": 1.022, "curve": 0.327, "c2": 0.37, "c3": 0.421}, {"time": 1.8667, "x": 1.177, "y": 1.177, "curve": "stepped"}, {"time": 1.9, "curve": 0.461, "c2": 0.01, "c3": 0.765, "c4": 0.42}, {"time": 2.6667, "x": 1.022, "y": 1.022, "curve": 0.327, "c2": 0.37, "c3": 0.421}, {"time": 4.5333, "x": 1.177, "y": 1.177, "curve": "stepped"}, {"time": 4.5667, "curve": 0.461, "c2": 0.01, "c3": 0.765, "c4": 0.42}, {"time": 5.3333, "x": 1.022, "y": 1.022}]}, "bone14": {"translate": [{"x": 0.62, "y": 1.24, "curve": 0.382, "c2": 0.55, "c3": 0.741}, {"time": 1.0333, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 3.7, "x": 1.77, "y": 3.54, "curve": 0.245, "c3": 0.639, "c4": 0.56}, {"time": 5.3333, "x": 0.62, "y": 1.24}]}, "bone38": {"translate": [{"x": -4.78, "y": -3.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6333, "x": -9.49, "y": -6.78, "curve": "stepped"}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": -4.78, "y": -3.42}], "scale": [{"x": 1.044, "y": 1.044, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6333, "x": 1.087, "y": 1.087, "curve": "stepped"}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 1.044, "y": 1.044}]}, "bone15": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 5.3, "x": 1.115, "y": 1.048, "curve": "stepped"}, {"time": 5.3333}]}, "bone16": {"scale": [{"x": 1.059, "y": 1.024, "curve": 0.376, "c2": 0.5, "c3": 0.75}, {"time": 2.6333, "x": 1.115, "y": 1.048, "curve": "stepped"}, {"time": 2.6667, "curve": 0.25, "c3": 0.626, "c4": 0.5}, {"time": 5.3333, "x": 1.059, "y": 1.024}]}, "yun2": {"translate": [{"x": 12.45, "y": -8.89}], "scale": [{"x": 1.062, "y": 1.062, "curve": 0.367, "c2": 0.63, "c3": 0.704}, {"time": 0.7333, "x": 1.067, "y": 1.067, "curve": "stepped"}, {"time": 0.8, "curve": 0.244, "c3": 0.698, "c4": 0.78}, {"time": 5.3333, "x": 1.062, "y": 1.062}]}, "yun9": {"translate": [{"x": 12.45, "y": -8.89}], "scale": [{"x": 1.021, "y": 1.021, "curve": 0.327, "c2": 0.31, "c3": 0.664, "c4": 0.66}, {"time": 0.8, "x": 1.034, "y": 1.034, "curve": 0.376, "c2": 0.5, "c3": 0.75}, {"time": 3.4, "x": 1.067, "y": 1.067, "curve": "stepped"}, {"time": 3.4667, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 5.3333, "x": 1.021, "y": 1.021}]}, "yun6": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 5.3, "x": 1.064, "y": 1.064, "curve": "stepped"}, {"time": 5.3333}]}, "yun10": {"scale": [{"x": 1.032, "y": 1.032, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6333, "x": 1.064, "y": 1.064, "curve": "stepped"}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 1.032, "y": 1.032}]}, "yun4": {"scale": [{"x": 1.008, "y": 1.008, "curve": 0.324, "c2": 0.3, "c3": 0.757}, {"time": 4.1667, "x": 1.056, "y": 1.056, "curve": "stepped"}, {"time": 4.2, "curve": 0.282, "c3": 0.624, "c4": 0.39}, {"time": 5.3333, "x": 1.008, "y": 1.008}]}, "yun11": {"scale": [{"x": 1.044, "y": 1.044, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 1.5, "x": 1.056, "y": 1.056, "curve": "stepped"}, {"time": 1.5333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4.2, "x": 1.028, "y": 1.028, "curve": 0.341, "c2": 0.36, "c3": 0.681, "c4": 0.71}, {"time": 5.3333, "x": 1.044, "y": 1.044}]}, "yun5": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 5.3, "x": 1.042, "y": 1.042, "curve": "stepped"}, {"time": 5.3333}]}, "yun12": {"scale": [{"x": 1.021, "y": 1.021, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6333, "x": 1.042, "y": 1.042, "curve": "stepped"}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 1.021, "y": 1.021}]}, "yun8": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 5.3, "x": 1.063, "y": 1.063, "curve": "stepped"}, {"time": 5.3333}]}, "yun13": {"scale": [{"x": 1.032, "y": 1.032, "curve": 0.376, "c2": 0.5, "c3": 0.749}, {"time": 2.6, "x": 1.063, "y": 1.063, "curve": "stepped"}, {"time": 2.6333, "curve": 0.249, "c3": 0.626, "c4": 0.5}, {"time": 5.3333, "x": 1.032, "y": 1.032}]}, "yun3": {"translate": [{"curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.3, "x": -30.98, "y": 8.45, "curve": "stepped"}, {"time": 5.3333}], "scale": [{"curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.3, "x": 1.141, "y": 1.141, "curve": "stepped"}, {"time": 5.3333}]}, "yun14": {"translate": [{"x": -14.4, "y": 3.93, "curve": 0.235, "c2": 0.5, "c3": 0.459}, {"time": 2.6333, "x": -30.98, "y": 8.45, "curve": "stepped"}, {"time": 2.6667, "curve": 0.554, "c2": 0.01, "c3": 0.783, "c4": 0.51}, {"time": 5.3333, "x": -14.4, "y": 3.93}], "scale": [{"x": 1.065, "y": 1.065, "curve": 0.235, "c2": 0.5, "c3": 0.459}, {"time": 2.6333, "x": 1.141, "y": 1.141, "curve": "stepped"}, {"time": 2.6667, "curve": 0.554, "c2": 0.01, "c3": 0.783, "c4": 0.51}, {"time": 5.3333, "x": 1.065, "y": 1.065}]}, "yun7": {"scale": [{"x": 1.063, "y": 1.063, "curve": 0.251, "c2": 0.6, "c3": 0.564}, {"time": 1.2667, "x": 1.07, "y": 1.07, "curve": "stepped"}, {"time": 1.3, "curve": 0.612, "c2": 0.01, "c3": 0.682, "c4": 0.68}, {"time": 5.3333, "x": 1.063, "y": 1.063}]}, "yun15": {"scale": [{"x": 1.007, "y": 1.007, "curve": 0.408, "c2": 0.28, "c3": 0.708, "c4": 0.64}, {"time": 1.3, "x": 1.033, "y": 1.033, "curve": 0.235, "c2": 0.5, "c3": 0.459}, {"time": 3.9333, "x": 1.07, "y": 1.07, "curve": "stepped"}, {"time": 3.9667, "curve": 0.445, "c2": 0.01, "c3": 0.756, "c4": 0.41}, {"time": 5.3333, "x": 1.007, "y": 1.007}]}, "bone23": {"translate": [{"x": 0.11, "y": 0.11, "curve": 0.293, "c2": 0.64, "c3": 0.623}, {"time": 0.5667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 3.2333, "x": 3.54, "y": 3.54, "curve": 0.268, "c2": 0.42, "c3": 0.522, "c4": 0.84}, {"time": 5.3333, "x": 0.11, "y": 0.11}]}, "bone24": {"translate": [{"curve": 0.552, "c2": 0.01, "c3": 0.783, "c4": 0.5}, {"time": 2.6667, "y": 3.54, "curve": 0.236, "c2": 0.5, "c3": 0.458}, {"time": 5.3333}]}, "hulu": {"translate": [{"x": -1.89, "y": 1.89, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 1.2667, "x": -3.54, "y": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 5.3333, "x": -1.89, "y": 1.89}]}, "bone22": {"translate": [{"x": 1.84, "y": 1.82, "curve": 0.374, "c2": 0.49, "c3": 0.751}, {"time": 1.3667, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 4.0333, "x": 3.56, "y": 3.53, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 5.3333, "x": 1.84, "y": 1.82}]}, "bone": {"translate": [{"x": -1.36, "y": 1.36, "curve": 0.381, "c2": 0.54, "c3": 0.743}, {"time": 1.1, "curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 3.7667, "x": -3.54, "y": 3.54, "curve": 0.246, "c3": 0.635, "c4": 0.55}, {"time": 5.3333, "x": -1.36, "y": 1.36}]}, "bone25": {"translate": [{"x": 1.11, "y": 1.48, "curve": 0.38, "c2": 0.53, "c3": 0.744}, {"time": 1.1333, "x": 1.85, "y": 2.47, "curve": 0.25, "c3": 0.75}, {"time": 3.8, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 5.3333, "x": 1.11, "y": 1.48}]}, "bone19": {"translate": [{"curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.3, "x": 5.73, "y": 1.41, "curve": "stepped"}, {"time": 5.3333}], "scale": [{"curve": 0.575, "c2": 0.01, "c3": 0.48}, {"time": 5.3, "x": 1.047, "y": 1.047, "curve": "stepped"}, {"time": 5.3333}]}, "bone21": {"translate": [{"x": 5.2, "y": 1.28, "curve": 0.253, "c2": 0.61, "c3": 0.567}, {"time": 1.2333, "x": 5.73, "y": 1.41, "curve": "stepped"}, {"time": 1.2667, "curve": 0.613, "c2": 0.01, "c3": 0.677, "c4": 0.68}, {"time": 5.3333, "x": 5.2, "y": 1.28}], "scale": [{"x": 1.043, "y": 1.043, "curve": 0.253, "c2": 0.61, "c3": 0.567}, {"time": 1.2333, "x": 1.047, "y": 1.047, "curve": "stepped"}, {"time": 1.2667, "curve": 0.613, "c2": 0.01, "c3": 0.677, "c4": 0.68}, {"time": 5.3333, "x": 1.043, "y": 1.043}]}, "bone18": {"translate": [{"x": 1.13, "y": 0.28, "curve": 0.29, "c2": 0.41, "c3": 0.425}, {"time": 3.4333, "x": 5.73, "y": 1.41, "curve": "stepped"}, {"time": 3.4667, "curve": 0.489, "c2": 0.01, "c3": 0.777, "c4": 0.44}, {"time": 5.3333, "x": 1.13, "y": 0.28}], "scale": [{"x": 1.009, "y": 1.009, "curve": 0.29, "c2": 0.41, "c3": 0.425}, {"time": 3.4333, "x": 1.047, "y": 1.047, "curve": "stepped"}, {"time": 3.4667, "curve": 0.489, "c2": 0.01, "c3": 0.777, "c4": 0.44}, {"time": 5.3333, "x": 1.009, "y": 1.009}]}, "bone20": {"translate": [{"x": 5.25, "y": 1.29, "curve": 0.256, "c2": 0.61, "c3": 0.572}, {"time": 1.1667, "x": 5.73, "y": 1.41, "curve": "stepped"}, {"time": 1.2, "curve": 0.613, "c2": 0.01, "c3": 0.668, "c4": 0.7}, {"time": 5.3333, "x": 5.25, "y": 1.29}], "scale": [{"x": 1.043, "y": 1.043, "curve": 0.256, "c2": 0.61, "c3": 0.572}, {"time": 1.1667, "x": 1.047, "y": 1.047, "curve": "stepped"}, {"time": 1.2, "curve": 0.613, "c2": 0.01, "c3": 0.668, "c4": 0.7}, {"time": 5.3333, "x": 1.043, "y": 1.043}]}, "bone26": {"translate": [{"x": 0.93, "y": 0.23, "curve": 0.383, "c2": 0.3, "c3": 0.682, "c4": 0.66}, {"time": 1.2667, "x": 3.44, "y": 0.84, "curve": 0.228, "c2": 0.53, "c3": 0.481}, {"time": 3.5667, "x": 5.73, "y": 1.41, "curve": "stepped"}, {"time": 3.6, "curve": 0.477, "c2": 0.01, "c3": 0.773, "c4": 0.43}, {"time": 5.3333, "x": 0.93, "y": 0.23}], "scale": [{"x": 1.008, "y": 1.008, "curve": 0.383, "c2": 0.3, "c3": 0.682, "c4": 0.66}, {"time": 1.2667, "x": 1.028, "y": 1.028, "curve": 0.228, "c2": 0.53, "c3": 0.481}, {"time": 3.5667, "x": 1.047, "y": 1.047, "curve": "stepped"}, {"time": 3.6, "curve": 0.477, "c2": 0.01, "c3": 0.773, "c4": 0.43}, {"time": 5.3333, "x": 1.008, "y": 1.008}]}, "bone27": {"translate": [{"x": 1.01, "y": 0.25, "curve": 0.377, "c2": 0.31, "c3": 0.679, "c4": 0.66}, {"time": 1.2, "x": 3.44, "y": 0.84, "curve": 0.228, "c2": 0.53, "c3": 0.481}, {"time": 3.5, "x": 5.73, "y": 1.41, "curve": "stepped"}, {"time": 3.5333, "curve": 0.483, "c2": 0.01, "c3": 0.775, "c4": 0.44}, {"time": 5.3333, "x": 1.01, "y": 0.25}], "scale": [{"x": 1.008, "y": 1.008, "curve": 0.377, "c2": 0.31, "c3": 0.679, "c4": 0.66}, {"time": 1.2, "x": 1.028, "y": 1.028, "curve": 0.228, "c2": 0.53, "c3": 0.481}, {"time": 3.5, "x": 1.047, "y": 1.047, "curve": "stepped"}, {"time": 3.5333, "curve": 0.483, "c2": 0.01, "c3": 0.775, "c4": 0.44}, {"time": 5.3333, "x": 1.008, "y": 1.008}]}, "bone28": {"translate": [{"x": 3.44, "y": 0.84, "curve": 0.228, "c2": 0.53, "c3": 0.481}, {"time": 2.3, "x": 5.73, "y": 1.41, "curve": "stepped"}, {"time": 2.3333, "curve": 0.576, "c2": 0.01, "c3": 0.772, "c4": 0.54}, {"time": 5.3333, "x": 3.44, "y": 0.84}], "scale": [{"x": 1.028, "y": 1.028, "curve": 0.228, "c2": 0.53, "c3": 0.481}, {"time": 2.3, "x": 1.047, "y": 1.047, "curve": "stepped"}, {"time": 2.3333, "curve": 0.576, "c2": 0.01, "c3": 0.772, "c4": 0.54}, {"time": 5.3333, "x": 1.028, "y": 1.028}]}, "bone29": {"translate": [{"x": 5.68, "y": 1.39, "curve": 0.302, "c2": 0.65, "c3": 0.633}, {"time": 0.4333, "x": 5.73, "y": 1.41, "curve": "stepped"}, {"time": 0.4667, "curve": 0.576, "c2": 0.01, "c3": 0.772, "c4": 0.54}, {"time": 3.4667, "x": 3.44, "y": 0.84, "curve": 0.252, "c2": 0.45, "c3": 0.527, "c4": 0.86}, {"time": 5.3333, "x": 5.68, "y": 1.39}], "scale": [{"x": 1.046, "y": 1.046, "curve": 0.302, "c2": 0.65, "c3": 0.633}, {"time": 0.4333, "x": 1.047, "y": 1.047, "curve": "stepped"}, {"time": 0.4667, "curve": 0.576, "c2": 0.01, "c3": 0.772, "c4": 0.54}, {"time": 3.4667, "x": 1.028, "y": 1.028, "curve": 0.252, "c2": 0.45, "c3": 0.527, "c4": 0.86}, {"time": 5.3333, "x": 1.046, "y": 1.046}]}, "bone31": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 5.3, "x": 9.51, "y": -7.61, "curve": "stepped"}, {"time": 5.3333}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 5.3, "x": 1.075, "y": 1.075, "curve": "stepped"}, {"time": 5.3333}]}, "bone36": {"translate": [{"x": 4.72, "y": -3.77, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "x": 9.51, "y": -7.61, "curve": "stepped"}, {"time": 2.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 4.72, "y": -3.77}], "scale": [{"x": 1.037, "y": 1.037, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6667, "x": 1.075, "y": 1.075, "curve": "stepped"}, {"time": 2.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 1.037, "y": 1.037}]}, "bone35": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 5.3, "x": -13.55, "y": -6.78, "curve": "stepped"}, {"time": 5.3333}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 5.3, "x": 1.161, "y": 1.161, "curve": "stepped"}, {"time": 5.3333}]}, "bone34": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 5.3, "x": -9.49, "y": -6.78, "curve": "stepped"}, {"time": 5.3333}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 5.3, "x": 1.087, "y": 1.087, "curve": "stepped"}, {"time": 5.3333}]}, "bone37": {"translate": [{"x": -6.83, "y": -3.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6333, "x": -13.55, "y": -6.78, "curve": "stepped"}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": -6.83, "y": -3.42}], "scale": [{"x": 1.081, "y": 1.081, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6333, "x": 1.161, "y": 1.161, "curve": "stepped"}, {"time": 2.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 5.3333, "x": 1.081, "y": 1.081}]}, "bone32": {"translate": [{"x": -8.24, "y": -5.88, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.0667, "x": -9.49, "y": -6.78, "curve": "stepped"}, {"time": 1.1, "curve": 0.243, "c3": 0.679, "c4": 0.71}, {"time": 5.3333, "x": -8.24, "y": -5.88}], "scale": [{"x": 1.075, "y": 1.075, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.0667, "x": 1.087, "y": 1.087, "curve": "stepped"}, {"time": 1.1, "curve": 0.243, "c3": 0.679, "c4": 0.71}, {"time": 5.3333, "x": 1.075, "y": 1.075}]}, "bone39": {"translate": [{"x": -2.24, "y": -1.6, "curve": 0.321, "c2": 0.29, "c3": 0.66, "c4": 0.65}, {"time": 1.1, "x": -4.78, "y": -3.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.7333, "x": -9.49, "y": -6.78, "curve": "stepped"}, {"time": 3.7667, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 5.3333, "x": -2.24, "y": -1.6}], "scale": [{"x": 1.02, "y": 1.02, "curve": 0.321, "c2": 0.29, "c3": 0.66, "c4": 0.65}, {"time": 1.1, "x": 1.044, "y": 1.044, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.7333, "x": 1.087, "y": 1.087, "curve": "stepped"}, {"time": 3.7667, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 5.3333, "x": 1.02, "y": 1.02}]}, "bone40": {"rotate": [{"angle": 44.65, "curve": 0.25, "c3": 0.75}, {"time": 5.2333, "angle": -43.42}]}}}}}