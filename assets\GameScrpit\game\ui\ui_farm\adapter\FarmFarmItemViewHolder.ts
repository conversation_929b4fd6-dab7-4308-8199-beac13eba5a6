import { _decorator, Label, Node } from "cc";
import { ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { FarmSimpleMessage, FarmSlotMessage } from "../../../net/protocol/Farm";
import { Sprite } from "cc";
import ResMgr from "../../../../lib/common/ResMgr";
import { BundleEnum } from "../../../bundleEnum/BundleEnum";
import { BgList } from "../../../../module/farm/FarmSlotUITool";
import { UIMgr } from "../../../../lib/ui/UIMgr";
import { FarmRouteName } from "../../../../module/farm/FarmRoute";
import { PlayerModule } from "../../../../module/player/PlayerModule";
import { FontColor } from "../../../common/FmConstant";
import FmUtils from "../../../../lib/utils/FmUtils";
import MsgMgr from "../../../../lib/event/MsgMgr";
import MsgEnum from "../../../event/MsgEnum";
import { FarmModule } from "../../../../module/farm/FarmModule";
import { FarmAudioName } from "db://assets/GameScrpit/module/farm/FarmConfig";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
import { LangMgr } from "../../../mgr/LangMgr";

export interface FarmInfo extends FarmSimpleMessage {
  hatred?: number;
}

const { ccclass, property } = _decorator;
@ccclass("FarmFarmItemViewHolder")
export class FarmFarmItemViewHolder extends ViewHolder {
  @property(Label)
  private lblName: Label;

  @property(Node)
  private nodeHate: Node;

  @property(Node)
  private layoutHulu: Node;

  @property(Node)
  private btnHeader: Node;

  private farmInfo: FarmInfo;

  public init() {}

  public updateData(position: number, args: any) {
    this.farmInfo = args;

    this.lblName.string = this.farmInfo.simpleMessage.nickname;

    this.nodeHate.active = (this.farmInfo.hatred || 0) > 0;
    this.nodeHate.getComponent(Label).string = LangMgr.txMsgCode(457, [this.farmInfo.hatred]); // `敌对值:${this.farmInfo.hatred}`;

    FmUtils.setHeaderNode(this.btnHeader, this.farmInfo.simpleMessage);

    let winLoseNone = 0;

    for (let i = 0; i < this.farmInfo.slotList.length; i++) {
      const slot = this.farmInfo.slotList[i];
      let nodeItem = this.layoutHulu.getChildByName("item" + (i + 1));
      this.setHuluItem(nodeItem, slot);

      if (slot.otherCollectorMessage?.playerBaseMessage?.userId == PlayerModule.data.playerId) {
        if (slot.otherCollectorMessage.beeNum > (slot.ownCollectorMessage?.beeNum || 0)) {
          winLoseNone = 1;
        } else {
          winLoseNone = 2;
        }
      }
    }

    const nodeCollect = this.node.getChildByName("lbl_collect");
    const lblCollect = nodeCollect.getComponent(Label);
    if (winLoseNone == 1) {
      nodeCollect.active = true;
      lblCollect.color = FontColor.GREEN;
      lblCollect.outlineColor = FontColor.GREEN_STROKE;
    } else if (winLoseNone == 2) {
      nodeCollect.active = true;
      lblCollect.color = FontColor.RED;
      lblCollect.outlineColor = FontColor.RED_STROKE;
    } else {
      nodeCollect.active = false;
    }
  }

  private setHuluItem(item: Node, slot: FarmSlotMessage) {
    item.active = false;
    if (slot.gourdId == -1) {
      return;
    }

    let cfgBlessLand = FarmModule.data.getConfigBlessLand(slot.gourdId);

    ResMgr.loadImage(
      `${BundleEnum.BUNDLE_COMMON_ITEM}?autoItem/${BgList[cfgBlessLand.color - 1]}`,
      item.getChildByName("bg").getComponent(Sprite),
      null,
      () => {
        item.active = true;
      }
    );

    item.getChildByName("icon_hulu").children.forEach((child) => {
      child.active = `icon_hulu_${slot.gourdId - 1}` == child.name;
    });

    // 有他人采集，变暗
    const collectUserId = slot.otherCollectorMessage?.playerBaseMessage?.userId || -1;
    if (collectUserId != -1 && collectUserId != PlayerModule.data.playerId) {
      FmUtils.setNodeDark(item, 100);
    } else {
      FmUtils.setNodeDark(item, 255);
    }
  }

  public onGo() {
    AudioMgr.instance.playEffect(FarmAudioName.Effect.探寻点击前往);
    UIMgr.instance.showDialog(
      FarmRouteName.UIFarmMainOther,
      {
        farm: this.farmInfo,
      },
      () => {
        MsgMgr.emit(MsgEnum.ON_FARM_FIND);
      }
    );
  }
}
