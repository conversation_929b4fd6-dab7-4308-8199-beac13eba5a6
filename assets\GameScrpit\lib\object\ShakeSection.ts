import { Vec2, _decorator, v3 } from "cc";
import TickerMgr from "../ticker/TickerMgr";
import GameObject from "./GameObject";
import { Section } from "./Section";

const { ccclass, property } = _decorator;

const ShakeEffect = {
  1000: {
    id: 1004,
    name: "boss死亡震动",
    order: 300,
    offsetX: 30,
    offsetY: 30,
    time: 0.3,
    decelerate: true,
    times: 16,
  },
  1001: {
    id: 1001,
    name: "小震动",
    order: 100,
    offsetX: 0,
    offsetY: 10,
    time: 0.3,
    decelerate: true,
    times: 16,
  },
  1002: {
    id: 1002,
    name: "中等震动",
    order: 200,
    offsetX: 0,
    offsetY: 30,
    time: 0.5,
    decelerate: true,
    times: 16,
  },
  1003: {
    id: 1003,
    name: "强震动",
    order: 300,
    offsetX: 0,
    offsetY: 50,
    time: 0.8,
    decelerate: true,
    times: 32,
  },
  1004: {
    id: 1004,
    name: "特别震动",
    order: 300,
    offsetX: 30,
    offsetY: 30,
    time: 0.3,
    decelerate: true,
    times: 16,
  },
};

@ccclass("ShakeSection")
export default class ShakeSection extends Section {
  private _running: any = null;
  private _originPos: Vec2 = Vec2.ZERO;

  public static sectionName(): string {
    return "ShakeSection";
  }

  public onInit(sub: GameObject, args: any) {
    super.onInit(sub, args);
    this._originPos = new Vec2(this.getSub().getPosition().x, this.getSub().getPosition().y);
    this.ready();
  }

  /**
   * 触发屏幕抖动效果
   * @param shakeId 抖动效果ID
   * @param time 延迟时间
   */
  public shake(shakeId: number, time: number = 0) {
    const shakeConfig = ShakeEffect[shakeId];
    if (!shakeConfig) {
      return;
    }

    TickerMgr.setTimeout(
      time,
      () => {
        const offsetX = shakeConfig.offsetX;
        const offsetY = shakeConfig.offsetY;
        const times = shakeConfig.times;
        const isDece = shakeConfig.decelerate;
        const deceOffsetX = isDece ? offsetX / times : 0;
        const deceOffsetY = isDece ? offsetY / times : 0;
        const order = shakeConfig.order;
        const dict = {
          offset: new Vec2(offsetX, offsetY),
          times: times,
          dece: new Vec2(deceOffsetX, deceOffsetY),
          order: order,
        };

        if (this._running && order > this._running.order) {
          this.resetObjectPos();
          this._running = dict;
        } else if (!this._running) {
          this.resetObjectPos();
          this._running = dict;
        }
      },
      this
    );
  }

  /**
   * 重置对象位置到原始位置
   */
  protected resetObjectPos() {
    this.getSub().setPosition(v3(this._originPos.x, this._originPos.y, 1));
  }

  /**
   * 更新抖动效果
   * @param dt 时间间隔
   */
  public updateSelf(dt: number) {
    if (!this._running) {
      return;
    }

    const randX = Math.round(Math.random() * this._running.offset.x * 2) - this._running.offset.x;
    const randY = Math.round(Math.random() * this._running.offset.y * 2) - this._running.offset.y;

    this.getSub().setPosition(v3(this._originPos.x + randX, this._originPos.y + randY, 1));

    this._running.offset.x -= this._running.dece.x;
    this._running.offset.y -= this._running.dece.y;
    this._running.times -= 1;

    if (this._running.times <= 0) {
      // 结束
      this.resetObjectPos();
      this._running = null;
    }
  }
}
