import { _decorator, Component, instantiate, Label, Node } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import ResMgr from "../../../lib/common/ResMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
const { ccclass, property } = _decorator;

const nameMap: Map<number, string> = new Map([
  [7, "师徒一行"],
  [8, "四大天王"],
]);

@ccclass("UIPortraitGroup")
export class UIPortraitGroup extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_COLLECT}?prefab/ui/UIPortraitGroup`;
  }

  private _type: number = null;
  public init(args: any): void {
    super.init(args);
    this._type = args.type;
  }

  protected onEvtShow(): void {
    let lab = this.getNode("btn_back").getChildByName("Label").getComponent(Label);
    lab.string = nameMap.get(this._type);

    ResMgr.loadPrefab(`${BundleEnum.BUNDLE_G_COLLECT}?prefab/common/group${this._type}`, (prefab) => {
      let node = instantiate(prefab);
      this.getNode("main").addChild(node);
      node.walk((child) => (child.layer = this.node.layer));
    });
  }

  private on_click_btn_back() {
    AudioMgr.instance.playEffect(520);
    UIMgr.instance.back();
  }
}
