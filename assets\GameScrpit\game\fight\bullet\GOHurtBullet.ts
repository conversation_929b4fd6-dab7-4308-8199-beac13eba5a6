import { _decorator, Details, sp, UITransform, v2, v3, Widget } from "cc";
import { ActionType, CallBulletDetail, HurtDetail, RecoverDetail, STUN_TYPE_ENUM } from "../FightDefine";
import RenderSection from "../../../lib/object/RenderSection";
import { GOBullet } from "./GOBullet";
import { actionDB, AnimationSection } from "../section/AnimationSection";
import DirectSection, { DIRECT } from "../../../lib/object/DirectSection";
import ToolExt from "../../common/ToolExt";
import BulletRenderSection from "../../../lib/object/BulletRenderSection";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.STOP);
const { ccclass, property } = _decorator;

@ccclass("GOHurtBullet")
export class GOHurtBullet extends GOBullet {
  /**通用受击表现脚本 */
  public async onInitDetail(detail: CallBulletDetail) {
    super.onInitDetail(detail);
    this.setPosVec3(this.setHurtEffectPos(detail));
    this.createAllSection(detail.bulletId);
  }

  private setHurtEffectPos(detail: CallBulletDetail) {
    let CrashPos = ToolExt.transferOfAxes(detail.target.getSection(RenderSection).getHurtEffect(), this.parent);
    return v2(CrashPos.x, CrashPos.y);
    return;
    // let CrashPos = ToolExt.transferOfAxes(detail.target.getSection(RenderSection).getRender(), this.parent);
    // return v2(
    //   CrashPos.x,
    //   CrashPos.y +
    //     (detail.target.getSection(RenderSection).getRender().getComponent(UITransform).height *
    //       detail.target.getSection(RenderSection).getRender().getScale().y) /
    //       detail.target.getSection(RenderSection).getDbScale().y /
    //       2
    // );
  }

  public async createAllSection(bulletId: number) {
    await new Promise(async (res) => {
      let param = {
        bulletId: bulletId,
        callBack: res,
      };
      this.createSection(BulletRenderSection, param);
    });
    this.createSection(AnimationSection);
    this.createSection(DirectSection, this.getDir());
    switch (this.detail.actionType) {
      case ActionType.CRITICAL:
        this.getSection(AnimationSection).playAction(102, false);
        break;
      default:
        this.getSection(AnimationSection).playAction(103, false);
        break;
    }
  }

  protected async AnimationStart(detail) {
    //log.log("通用受击打动画开始播放", detail);
  }
  protected async PlayAnimation(event) {
    //log.log("通用受击打动画事件", event);
    if (event["data"]["name"] == "atk") {
      this.hurtRoleMsg();
      this.atkRoleMsg();
    }
  }
  protected async AnimationCompleted(data) {
    //log.log("通用受击打动画播放结束", data);
    if (data.animation.name == actionDB[102].actName || data.animation.name == actionDB[103].actName) {
      this.resolveBack(true);
      this.remove();
    }
  }

  /**返回位置索引 */
  public getDir() {
    return this.target.getDir();
  }
}
