import { _decorator, Label } from "cc";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { JsonMgr } from "../../mgr/JsonMgr";
import { UINode } from "../../../lib/ui/UINode";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { SoulModule } from "../../../module/soul/SoulModule";
import { DialogZero } from "../../GameDefine";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
const { ccclass, property } = _decorator;

@ccclass("UISoulSlotPop")
export class UISoulSlotPop extends UINode {
  protected _openAct: boolean = true;
  protected _isAddToBottom: boolean = true;
  public zOrder(): number {
    return DialogZero.UISoulSlotPop;
  }
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_SOUL}?prefab/ui/UISoulSlotPop`;
  }
  protected dependOn(): BundleEnum[] {
    return [BundleEnum.BUNDLE_COMMON_UI, BundleEnum.BUNDLE_COMMON_ITEM];
  }
  protected onEvtShow(): void {
    let configBaseSoul = JsonMgr.instance.jsonList.c_soul[41001];
    let src = `item_${configBaseSoul.costPlaceList[0]}`;
    this.getNode("item_cost_lab").getComponent(Label).string = `x${configBaseSoul.costPlaceList[1]}`;
  }

  private on_click_btn_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  private on_click_btn_cancel() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this.on_click_btn_close();
  }

  /**解锁槽位 */
  private on_click_btn_sure() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let configBaseSoul = JsonMgr.instance.jsonList.c_soul[41001];
    if (PlayerModule.data.getItemNum(configBaseSoul.costPlaceList[0]) < configBaseSoul.costPlaceList[1]) {
      UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
        itemId: configBaseSoul.costPlaceList[0],
        needNum: configBaseSoul.costPlaceList[1],
      });
      return;
    }
    SoulModule.api.unLockSlot((data: number) => {
      this.on_click_btn_close();
    });
  }
}
