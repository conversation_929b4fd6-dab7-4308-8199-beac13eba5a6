import { _decorator, CCFloat, CCString, Component, sp } from "cc";
import MsgMgr from "../../../lib/event/MsgMgr";
import { CityEvent } from "../../../module/city/CityEvent";
import { JsonMgr } from "../../mgr/JsonMgr";
import { CityModule } from "../../../module/city/CityModule";
import MsgEnum from "../../event/MsgEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { CityRouteName } from "../../../module/city/CityConstant";
const { ccclass, property } = _decorator;

@ccclass("Cloud")
export class Cloud extends Component {
  @property(CCFloat)
  cloudId: number = 0;

  @property(CCString)
  aniKeyS: string[] = [];

  protected onLoad(): void {
    MsgMgr.on(CityEvent.ON_CITY_BUILD_ANI, this.cloudAni, this);
  }

  // 修改onEnable方法
  protected onEnable(): void {
    const liziNode = this.node.getChildByName("lizi");
    const spineComp = this.node.getComponent(sp.Skeleton);

    if (CityModule.data.cityAggregateMessage.openFogCityList.includes(this.cloudId) == false) {
      if (this.node.isValid) this.node.active = true;
      if (spineComp && spineComp.isValid) {
        spineComp.setAnimation(0, this.aniKeyS[0], false);
      }
    } else if (liziNode && liziNode.isValid) {
      liziNode.active = false;
      if (this.node.isValid) this.node.active = false;
    }
  }

  protected onDisable(): void {}

  private async cloudAni(list: number[]) {
    let db = JsonMgr.instance.jsonList.c_buildShow;
    let dbData = db[this.cloudId];
    for (let i = 0; i < list.length; i++) {
      await new Promise(async (res) => {
        let id = list[i];
        let bool1 = dbData.buildShowId.includes(id);
        let bool2 = CityModule.data.cityAggregateMessage.openFogCityList.includes(this.cloudId);
        if (bool1 == true && bool2 == false) {
          CityModule.api.openFog(this.cloudId, async (data) => {
            UIMgr.instance.showPage(CityRouteName.UIGameMap);
            MsgMgr.emit(MsgEnum.ON_ZHAOGE_CITY_FOCOUS, "city_" + id);
            this.scheduleOnce(async () => {
              const spineComp = this.node.getComponent(sp.Skeleton);
              const liziNode = this.node.getChildByName("lizi");

              if (spineComp && spineComp.isValid) {
                spineComp.setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
                  if (liziNode && liziNode.isValid) {
                    liziNode.active = false;
                  }
                  if (this.node.isValid) this.node.active = false;
                  res(true);
                });
              }

              if (liziNode && liziNode.isValid) {
                liziNode.active = true;
              }
              if (spineComp && spineComp.isValid) {
                spineComp.setAnimation(0, this.aniKeyS[1], false);
              }
            }, 1);
          });
        } else {
          res(true);
        }
      });
    }
  }
}
