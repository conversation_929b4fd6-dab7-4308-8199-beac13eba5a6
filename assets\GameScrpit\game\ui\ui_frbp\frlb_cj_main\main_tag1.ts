import { _decorator } from "cc";
import { main_tag } from "./main_tag";
import { CityModule } from "db://assets/GameScrpit/module/city/CityModule";
import GuideMgr from "db://assets/GameScrpit/ext_guide/GuideMgr";
const { ccclass, property } = _decorator;

@ccclass("main_tag1")
export class main_tag1 extends main_tag {
  click_btn_task_go(event) {
    // 处理按钮点击事件，例如打开任务界面或执行其他操作
    let args: any = {};
    args.buildId = CityModule.service.findMinCostCallCityId();
    GuideMgr.startGuide({ stepId: 23, args: args });
  }
}
