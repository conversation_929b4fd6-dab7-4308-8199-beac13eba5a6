import { _decorator, sp } from "cc";
import GOSkill from "./GOSkill";
import { CallBulletDetail, CallSkillDetail } from "../FightDefine";
import RenderSection from "../../../lib/object/RenderSection";
import { JsonMgr } from "../../mgr/JsonMgr";
import FightManager from "../manager/FightManager";
import BulletManager from "../manager/BulletManager";
import StateSection, { STATE } from "../section/StateSection";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
const { ccclass, property } = _decorator;

@ccclass("GOSkillCommonAtk")
export class GOSkillCommonAtk extends GOSkill {
  protected async onEnter(detail: CallSkillDetail) {
    super.onEnter(detail);
    // let render = detail.src.getSection(RenderSection).getRender();
    // render.getComponent(sp.Skeleton).setCompleteListener((data) => {});
    // await render.getComponent(sp.Skeleton).setEventListener(async (animation, event) => {
    //   if (event["data"]["name"] == "atk") {
    //     let bulletDetail: CallBulletDetail = {
    //       actionType: detail.actionType,
    //       resolveBack: detail.resolveBack,
    //       movementInfo: detail.movementInfo,
    //       bulletId: skilldb.bulletId,
    //       skillId: detail.skillId,
    //       target: detail.target,
    //       src: detail.src,
    //     };
    //     FightManager.instance.getSection(BulletManager).doCallObject(bulletDetail);
    //     this.remove();
    //   }
    // });
  }

  protected doBullet() {
    super.doBullet();
    let skilldb = JsonMgr.instance.jsonList.c_skillShow[this._detail.skillId];
    let bulletDetail: CallBulletDetail = {
      goSkill: this,
      actionType: this._detail.actionType,
      resolveBack: this._detail.resolveBack,
      movementInfo: this._detail.movementInfo,
      bulletId: skilldb.bulletId,
      skillId: this._detail.skillId,
      target: this._detail.target,
      src: this._detail.src,
    };
    FightManager.instance.getSection(BulletManager).doCallObject(bulletDetail);
  }
}

//
