<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>JZSJ_btn_haozhao.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{-1,0}</string>
                <key>spriteSize</key>
                <string>{177,157}</string>
                <key>spriteSourceSize</key>
                <string>{179,157}</string>
                <key>textureRect</key>
                <string>{{736,1},{177,157}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>JZSJ_btn_shengji.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{-1,0}</string>
                <key>spriteSize</key>
                <string>{177,157}</string>
                <key>spriteSourceSize</key>
                <string>{179,157}</string>
                <key>textureRect</key>
                <string>{{915,1},{177,157}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>bg_fanrongdu.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{204,39}</string>
                <key>spriteSourceSize</key>
                <string>{204,39}</string>
                <key>textureRect</key>
                <string>{{1,197},{204,39}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_jindutiao_menkeshu.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{88,13}</string>
                <key>spriteSourceSize</key>
                <string>{90,15}</string>
                <key>textureRect</key>
                <string>{{462,197},{88,13}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_jindutiao_shengji.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{122,17}</string>
                <key>spriteSourceSize</key>
                <string>{132,27}</string>
                <key>textureRect</key>
                <string>{{341,221},{122,17}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_jindutiaodi_menkeshu.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{119,22}</string>
                <key>spriteSourceSize</key>
                <string>{119,22}</string>
                <key>textureRect</key>
                <string>{{341,197},{119,22}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_jindutiaodi_shengji.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{132,27}</string>
                <key>spriteSourceSize</key>
                <string>{132,27}</string>
                <key>textureRect</key>
                <string>{{207,197},{132,27}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_shuxing.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{733,185}</string>
                <key>spriteSourceSize</key>
                <string>{733,185}</string>
                <key>textureRect</key>
                <string>{{1,1},{733,185}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg_zhenshouwujiang.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{78,148}</string>
                <key>spriteSourceSize</key>
                <string>{78,148}</string>
                <key>textureRect</key>
                <string>{{736,160},{78,148}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>icon_jiantou_shang.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{20,24}</string>
                <key>spriteSourceSize</key>
                <string>{20,24}</string>
                <key>textureRect</key>
                <string>{{952,201},{20,24}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icon_jiantou_shengji.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{30,19}</string>
                <key>spriteSourceSize</key>
                <string>{30,19}</string>
                <key>textureRect</key>
                <string>{{952,180},{30,19}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>icon_wenhao_weihuode.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{46,64}</string>
                <key>spriteSourceSize</key>
                <string>{46,64}</string>
                <key>textureRect</key>
                <string>{{886,180},{46,64}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>line_fengexian.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{700,7}</string>
                <key>spriteSourceSize</key>
                <string>{700,7}</string>
                <key>textureRect</key>
                <string>{{1,188},{700,7}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>UICityDetail.png</string>
            <key>size</key>
            <string>{1073,239}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:b93f9b3b98c26cf03b6194647b03e8b6:76497f64e25f0a509a6470ae5018d313:77adccdedfa2b195d9398ee28841fa62$</string>
            <key>textureFileName</key>
            <string>UICityDetail.png</string>
        </dict>
    </dict>
</plist>
