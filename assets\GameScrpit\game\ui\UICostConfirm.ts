import { _decorator, RichText } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { Label } from "cc";
import { instantiate } from "cc";
import { UIMgr } from "../../lib/ui/UIMgr";
import { PlayerModule } from "../../module/player/PlayerModule";
import { Toggle } from "cc";
import ToolExt from "../common/ToolExt";
import { LangMgr } from "../mgr/LangMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
const { ccclass, property } = _decorator;

/**
 *
 * hopewsw
 * Thu Aug 29 2024 20:22:03 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/UICostConfirm.ts
 *
 */
export interface ConfirmMsg {
  title?: string;
  msg: string;
  itemList?: number[];
  stopHintOption?: boolean;
  okText?: string;
}

@ccclass("UICostConfirm")
export class UICostConfirm extends UINode {
  protected _openAct: boolean = true;
  private stopHist = false;
  private _args: ConfirmMsg = null;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_COMMON_MAIN}?prefab/ui/UICostConfirm`;
  }

  //=================================================
  public init(args: any): void {
    super.init(args);
    this._args = args;
    this._args.title = this._args.title || LangMgr.txMsgCode(99999999, [], "提示");
    if (!this._args.itemList) {
      this._args.itemList = [];
    }
  }

  protected onEvtShow(): void {
    super.onEvtShow();
    AudioMgr.instance.playEffect(AudioName.Effect.二级弹窗提示);

    this.getNode("btn_no").getComponentInChildren(Label).string = LangMgr.txMsgCode(136, [], "取消");

    this.getNode("btn_yes").getComponentInChildren(Label).string =
      this._args.okText || LangMgr.txMsgCode(137, [], "确定");

    if (this._args.title.length > 0) {
      let str = this._args.title;
      let tx1 = str[0];
      let tx2 = "";
      for (let i = 1; i < str.length; i++) {
        tx2 += str[i];
      }
      let title_str = `<outline color=#ffa200 width=3><color=#ffffff><size=68>${tx1}</size></color></outline><outline color=#ffa200 width=3><color=#ffffff><size=60>${tx2}</size></color></outline>`;
      this.getNode("lbl_title").getComponent(RichText).string = title_str;
    }
    this.getNode("radio").active = this._args.stopHintOption;
    this.getNode("lbl_msg").getComponent(Label).string = this._args.msg;

    for (let i = 0; i < this._args.itemList.length; i += 2) {
      let id = this._args.itemList[i];

      let itemNode = instantiate(this.getNode("item"));
      ToolExt.setItemIcon(itemNode, id);

      itemNode.active = true;
      this.getNode("layout_cost").insertChild(itemNode, 0);

      let itemNumNode = instantiate(this.getNode("lbl_num"));
      let playerItemNum = PlayerModule.data.getItemNum(id);
      itemNumNode.active = true;
      itemNumNode.getComponent(Label).string = `${playerItemNum}/${this._args.itemList[i + 1]}`;
      this.getNode("layout_cost").insertChild(itemNumNode, 1);

      this.getNode("Toggle").getComponent(Toggle).isChecked = this.stopHist;
    }
  }

  private on_click_btn_yes() {
    UIMgr.instance.back({ resp: { stopHint: this.stopHist, ok: true } });
  }

  private on_click_btn_no() {
    UIMgr.instance.back({ resp: { stopHint: false, ok: false } });
  }

  private on_click_radio() {
    this.stopHist = !this.stopHist;
    this.getNode("Toggle").getComponent(Toggle).isChecked = this.stopHist;
  }
}
