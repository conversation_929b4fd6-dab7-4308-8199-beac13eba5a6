import { _decorator, Node, NodeEventType, tween, v3 } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { ClubModule } from "../../../module/club/ClubModule";
import { ClubRewardMessage } from "../../net/protocol/Club";
import { UIMgr } from "../../../lib/ui/UIMgr";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import FmUtils from "../../../lib/utils/FmUtils";
import { PublicRouteName } from "../../../module/player/PlayerConstant";
import { ConfirmMsg } from "../UICostConfirm";
import { JsonMgr } from "../../mgr/JsonMgr";
import { HdVipCardModule } from "../../../module/hd_vipcard/HdVipCardModule";
import { routeConfig } from "db://assets/platform/src/core/managers/RouteTableManager";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Tue Aug 20 2024 14:50:13 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/club/UIClubDonate.ts
 *
 */

@ccclass("UIClubDonate")
@routeConfig({
  bundle: BundleEnum.BUNDLE_G_CLUB,
  url: "prefab/ui/UIClubDonate",
  nextHop: [],
  exit: "dialog_close",
})
export class UIClubDonate extends BaseCtrl {
  public playShowAni: boolean = true;

  //=================================================
  public init(args: any): void {
    super.init(args);
  }
  protected start(): void {
    super.start();
    this.getNode("donate_1").getChildByName("btn_donate").on(NodeEventType.TOUCH_END, this.click_donate_1, this);
    this.getNode("donate_2").getChildByName("btn_donate").on(NodeEventType.TOUCH_END, this.click_donate_2, this);
    this.getNode("donate_3").getChildByName("btn_donate").on(NodeEventType.TOUCH_END, this.click_donate_3, this);
    this.getNode("donate_4").getChildByName("btn_donate").on(NodeEventType.TOUCH_END, this.click_donate_4, this);
    this.refreshUI();
    this.getNode("content").children.forEach((node) => {
      tween(node)
        .set({ scale: v3(0.1, 0.1, 1) })
        .to(0.3, { scale: v3(1, 1, 1) }, { easing: "sineOut" })
        .start();
    });
  }
  private refreshLeftTime() {
    let donateMap = ClubModule.data.clubFormMessage.donateMap;
    if (
      donateMap &&
      donateMap[1] &&
      new Date().getTime() < donateMap[1].nextDonateDeadline &&
      ClubModule.config.getDonateConfig(1).max > donateMap[1].count
    ) {
      this.getNode("donate_1").getChildByName("btn_donate").getChildByName("lbl_cd").active = true;
      FmUtils.setCd(
        this.getNode("donate_1").getChildByName("btn_donate").getChildByName("lbl_cd"),
        donateMap[1].nextDonateDeadline,
        true,
        () => {
          this.getNode("donate_1").getChildByName("btn_donate").getChildByName("txt").active = true;
        }
      );
      this.getNode("donate_1").getChildByName("btn_donate").getChildByName("txt").active = false;
    } else {
      this.getNode("donate_1").getChildByName("btn_donate").getChildByName("lbl_cd").active = false;
      this.getNode("donate_1").getChildByName("btn_donate").getChildByName("txt").active = true;
    }
  }
  private refreshItem(node: Node, id: number) {
    let donate = ClubModule.config.getDonateConfig(id);
    let list = donate.rewardList;
    for (let i = 0; i < list.length; i++) {
      let item = node.getChildByName(`item_${i + 1}`);
      item.active = true;
      FmUtils.setItemNode(item, list[i][0], list[i][1]);
    }
  }
  private refreshUI() {
    let donateMap = ClubModule.data.clubFormMessage.donateMap;
    if (ClubModule.config.getDonateConfig(1).max > (donateMap[1]?.count ?? 0)) {
      this.getNode("donate_1").getChildByName("btn_donate").active = true;
      this.getNode("donate_1").getChildByName("done").active = false;
    } else {
      this.getNode("donate_1").getChildByName("btn_donate").active = false;
      this.getNode("donate_1").getChildByName("done").active = true;
    }
    this.refreshItem(this.getNode("donate_1"), 1);
    this.refreshItem(this.getNode("donate_2"), 2);
    this.refreshItem(this.getNode("donate_3"), 3);
    this.refreshItem(this.getNode("donate_4"), 4);
    this.refreshLeftTime();

    if (ClubModule.config.getDonateConfig(2).max > (donateMap[2]?.count ?? 0)) {
      this.getNode("donate_2").getChildByName("btn_donate").active = true;
      this.getNode("donate_2").getChildByName("done").active = false;
    } else {
      this.getNode("donate_2").getChildByName("done").active = true;
      this.getNode("donate_2").getChildByName("btn_donate").active = false;
    }

    if (ClubModule.config.getDonateConfig(3).max > (donateMap[3]?.count ?? 0)) {
      this.getNode("donate_3").getChildByName("btn_donate").active = true;
      this.getNode("donate_3").getChildByName("done").active = false;
    } else {
      this.getNode("donate_3").getChildByName("done").active = true;
      this.getNode("donate_3").getChildByName("btn_donate").active = false;
    }

    if (ClubModule.config.getDonateConfig(4).max > (donateMap[4]?.count ?? 0)) {
      this.getNode("donate_4").getChildByName("btn_donate").active = true;
      this.getNode("donate_4").getChildByName("done").active = false;
    } else {
      this.getNode("donate_4").getChildByName("done").active = true;
      this.getNode("donate_4").getChildByName("btn_donate").active = false;
    }
  }
  private click_donate_1() {
    if (ClubModule.service.isInterceptOperation()) {
      return;
    }
    if (this.getNode("donate_1").getChildByName("btn_donate").getChildByName("lbl_cd").active) {
      return;
    }
    ClubModule.api.donate(1, (data: ClubRewardMessage) => {
      //
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardMessage?.rewardList });
      this.refreshUI();
    });
  }
  private click_donate_2() {
    if (ClubModule.service.isInterceptOperation()) {
      return;
    }
    let donate = ClubModule.config.getDonateConfig(2);
    let name = JsonMgr.instance.getConfigItem(donate.costList[0]).name;
    let cost = donate.costList[1];
    let msg: ConfirmMsg = {
      msg: `您确定花费'${cost}${name}'进行${donate.name}`,
      itemList: donate.costList,
    };
    UIMgr.instance.showDialog(PublicRouteName.UICostConfirm, msg, (resp) => {
      if (resp?.ok) {
        ClubModule.api.donate(2, (data: ClubRewardMessage) => {
          MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardMessage?.rewardList });
          this.refreshUI();
        });
      }
    });
  }
  private click_donate_3() {
    if (ClubModule.service.isInterceptOperation()) {
      return;
    }
    if (HdVipCardModule.data.isMonthCard) {
      ClubModule.api.donate(3, (data: ClubRewardMessage) => {
        MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardMessage?.rewardList });
        this.refreshUI();
      });
      return;
    }
    let donate = ClubModule.config.getDonateConfig(3);
    let name = JsonMgr.instance.getConfigItem(donate.costList[0]).name;
    let cost = donate.costList[1];
    let msg: ConfirmMsg = {
      msg: `您确定花费'${cost}${name}'进行${donate.name}`,
      itemList: donate.costList,
    };
    UIMgr.instance.showDialog(PublicRouteName.UICostConfirm, msg, (resp) => {
      if (resp?.ok) {
        ClubModule.api.donate(3, (data: ClubRewardMessage) => {
          MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardMessage?.rewardList });
          this.refreshUI();
        });
      }
    });
  }
  private click_donate_4() {
    if (ClubModule.service.isInterceptOperation()) {
      return;
    }
    if (HdVipCardModule.data.isMonthCard) {
      ClubModule.api.donate(4, (data: ClubRewardMessage) => {
        MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardMessage?.rewardList });
        this.refreshUI();
      });
      return;
    }
    let donate = ClubModule.config.getDonateConfig(4);
    let name = JsonMgr.instance.getConfigItem(donate.costList[0]).name;
    let cost = donate.costList[1];
    let msg: ConfirmMsg = {
      msg: `您确定花费'${cost}${name}'进行${donate.name}`,
      itemList: donate.costList,
    };
    UIMgr.instance.showDialog(PublicRouteName.UICostConfirm, msg, (resp) => {
      if (resp?.ok) {
        ClubModule.api.donate(4, (data: ClubRewardMessage) => {
          MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardMessage?.rewardList });
          this.refreshUI();
        });
      }
    });
  }
}
