import { _decorator } from "cc";
import GameObject from "../../../lib/object/GameObject";
import { BuffDetail } from "../FightDefine";
import BEBuff from "./BEBuff";
import { BESwkStun } from "./BESwkStun";
import { JsonMgr } from "../../mgr/JsonMgr";
import { GORole } from "../role/GORole";
import { BEYuanxuan } from "./BEYuanxuan";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.DEBUG);
const { ccclass, property } = _decorator;
@ccclass("GOBuff")
export class GOBuff extends GameObject {
  private _detail: BuffDetail;
  private _BeSectionName: string;
  protected onInit(detail: BuffDetail) {
    super.onInit(detail);
    let buffDB = JsonMgr.instance.jsonList.c_buff[detail.buffId];
    if (buffDB == null) {
      this.remove();
      return;
    }
  }

  protected _buffId: number;
  public get buffId() {
    return this._buffId;
  }

  protected _attachment: GORole;
  public get attachment() {
    return this._attachment;
  }

  private _roundCount: number;

  public onInitDetail(detail: BuffDetail) {
    //log.log("创建buff=====", detail);
    this._roundCount = 1;
    this._buffId = detail.buffId;
    this._attachment = detail.attachment;

    this.createEffectSection();
  }

  private createEffectSection() {
    let db = JsonMgr.instance.jsonList.c_buff[this._buffId];
    let effectName = db["buffScrpit"];

    let type = BEMap.get(effectName);
    if (type == null) {
      log.warn("没有找到对应的buff类名字:" + effectName);
      return;
    }
    this.createSection(type, { buffId: this.buffId });
  }

  protected onStart(): void {
    super.onStart();
  }

  public setRoundCount(roundCount: number) {
    this._roundCount = roundCount;
  }

  public getDetail(): BuffDetail {
    return this._detail;
  }

  public getCurSection() {
    return this.getSectionByName(this._BeSectionName);
  }

  protected onRemove(): void {
    super.onRemove();
  }
}

const BEMap: Map<string, any> = new Map([
  ["BEBuff", BEBuff],
  ["BEYuanxuan", BEYuanxuan],
  ["BESwkStun", BESwkStun],
]);
