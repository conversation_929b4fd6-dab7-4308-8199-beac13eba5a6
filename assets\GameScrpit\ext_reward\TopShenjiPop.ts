import { _decorator, Label, Node, sp, UITransform } from "cc";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
import { TipsMgr } from "../../platform/src/TipsHelper";
const { ccclass, property } = _decorator;

@ccclass("TopShenjiPop")
export class TopShenjiPop extends BaseCtrl {
  @property(Node)
  private nodeGuadian: Node;
  @property(Node)
  private nodeMask: Node;
  @property(Label)
  private lblDes: Label;
  @property(Label)
  private lblTitle: Label;

  private _args: any;

  init(args: any): void {
    super.init(args);
    this._args = args;
  }
  start() {
    TipsMgr.setEnableTouch(false, 0.2);
    super.start();
    this.nodeGuadian.on(
      Node.EventType.TRANSFORM_CHANGED,
      () => {
        let width = this.nodeGuadian.position.x - this.nodeMask.position.x;
        this.nodeMask.getComponent(UITransform).setContentSize(width, this.nodeMask.getComponent(UITransform).height);
      },
      this
    );
    this.node.getComponentInChildren(sp.Skeleton).setCompleteListener(() => {
      this.closeBack();
    });
    this.lblTitle.string = this._args.title;
    this.lblDes.string = this._args.desc;
  }
}
