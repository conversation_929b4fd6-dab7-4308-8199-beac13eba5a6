import { _decorator, Component, instantiate, isValid, Label, Node, sp, Sprite } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { GoodsModule } from "../../../module/goods/GoodsModule";
import { JsonMgr } from "../../mgr/JsonMgr";
import ToolExt from "../../common/ToolExt";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { HeroModule } from "../../../module/hero/HeroModule";
import ResMgr from "../../../lib/common/ResMgr";
import TipMgr from "../../../lib/tips/TipMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import TickerMgr from "../../../lib/ticker/TickerMgr";
const { ccclass, property } = _decorator;

@ccclass("UIHuntSummon")
export class UIHuntSummon extends UINode {
  protected _openAct: boolean = true; //打开动作
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_HUNT}?prefab/ui/UIHuntSummon`;
  }

  private _shopId = 0;

  private _needNum = 0;

  private _needId = 0;
  protected onEvtShow(): void {
    let list = GoodsModule.data.getShopItemList(9);

    this._shopId = list[0];

    let c_shop = JsonMgr.instance.jsonList.c_shop;
    let c_info_shop = c_shop[this._shopId];

    let c_hero = JsonMgr.instance.jsonList.c_hero;
    let c_info_hero = c_hero[c_info_shop.itemsList[0][0]];

    HeroModule.service.updateHeroRaceIcon(this.getNode("spr_hero_type").getComponent(Sprite), c_info_hero.id, this);
    HeroModule.service.updateHeroColorBg(this.getNode("color_bkg").getComponent(Sprite), c_info_hero.id, this);
    this.getNode("lab_hero_name").getComponent(Label).string = c_info_hero.name;
    this.getNode("lab_hero_aptitude").getComponent(Label).string = "总资质：" + c_info_hero.quality;
    this.getNode("lab_hero_hua").getComponent(Label).string =
      c_info_hero.des || "角亢之精,吐云郁熙,咬雷发声,\n飞翔八极,周游四冥,未立吾左。";

    let c_info_item = JsonMgr.instance.getConfigItem(c_info_shop.cointype);

    ToolExt.setItemIcon(this.getNode("spr_need_icon"), c_info_item.id, this);
    let myItem = PlayerModule.data.getItemNum(c_info_shop.cointype);
    this.getNode("lab_need_num").getComponent(Label).string = myItem + "/" + c_info_shop.coinPrice;

    if (myItem >= c_info_shop.coinPrice) {
      ToolExt.setLabColor(this.getNode("lab_need_num").getComponent(Label), "#00AF04");
    } else {
      ToolExt.setLabColor(this.getNode("lab_need_num").getComponent(Label), "#FF0000");
    }

    this._needNum = c_info_shop.coinPrice;
    this._needId = c_info_shop.cointype;
    this.isShowBuyBtn();

    this.addHero(c_info_hero.id);
  }

  private addHero(id) {
    ResMgr.loadPrefab(
      `${BundleEnum.BUNDLE_COMMON_HERO_FULL}?prefabs/hero_${id}`,
      (prefab) => {
        if (isValid(this.node) == false) {
          return;
        }
        let node: Node = instantiate(prefab);
        node.walk((val) => {
          val.layer = this.node.layer;
        });
        this.getNode("headMask").addChild(node);
        node.name = "heroSpine";

        node.children[0].getComponent(sp.Skeleton).setAnimation(0, "animation1", true);
      },
      this
    );
  }

  private on_click_btn_get_hero() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let myItem = PlayerModule.data.getItemNum(this._needId);

    let c_shop = JsonMgr.instance.jsonList.c_shop;
    let c_info_shop = c_shop[this._shopId];
    if (c_info_shop.maxtype == 4 && !PlayerModule.service.isShopBuy(c_info_shop.itemsList[0][0])) {
      TipMgr.showTip("已拥有该物品，无法重复获得");
      return;
    }

    if (myItem < this._needNum) {
      UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
        itemId: this._needId,
        needNum: this._needNum,
      });
      return;
    }

    GoodsModule.api.redeemGoods(this._shopId, 1, (data: number[]) => {
      UIMgr.instance.back();
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data });
      this.isShowBuyBtn();
    });
  }

  private isShowBuyBtn() {
    let num = GoodsModule.data.hasBuyCount(this._shopId);
    let c_shop = JsonMgr.instance.jsonList.c_shop;
    let c_info_shop = c_shop[this._shopId];

    if (num >= c_info_shop.max || !PlayerModule.service.isShopBuy(c_info_shop.itemsList[0][0])) {
      this.getNode("btn_get_hero").active = false;
      MsgMgr.emit(MsgEnum.ON_CLOSE_CALL_ENTRY);
    } else {
      this.getNode("btn_get_hero").active = true;
    }
  }

  private on_click_btn_close() {
    // 关闭窗口 effect_guanbiyinxiao
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
}
