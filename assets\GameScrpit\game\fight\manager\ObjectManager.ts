import { Node, _decorator, isValid } from "cc";
import ManagerSection from "./ManagerSection";
import GameObject from "../../../lib/object/GameObject";
import FightManager from "./FightManager";
import { BattlePlayerBackInfo, PlayerBackInfo, RoleSourceEnum } from "../FightDefine";

import { GORole } from "../role/GORole";
import { JsonMgr } from "../../mgr/JsonMgr";
import { GOPlayer } from "../role/GOPlayer";
import { GOMonster } from "../role/GOMonster";
import TipMgr from "../../../lib/tips/TipMgr";
import { GOClubMirrBoss } from "../role/GOClubMirrBoss";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { ggLogger } from "db://gg-hot-update/scripts/hotupdate/GGLogger";
const { ccclass, property } = _decorator;

export const roleMap: Map<string, any> = new Map([
  ["GORole", GORole],
  ["GOPlayer", GOPlayer],
  ["GOMonster", GOMonster],
  ["GOClubMirrBoss", GOClubMirrBoss],
]);

@ccclass("ObjectManager")
export class ObjectManager extends ManagerSection {
  public onInit(go: GameObject, args) {
    super.onInit(go, args);
    this.createRoot("ObjectRoot", FightManager.instance);
    this.ready();
  }

  public async callObject(detail: PlayerBackInfo) {
    if (FightManager.instance.fightOver == true) {
      return;
    }
    if (isValid(this.root) == false) {
      return;
    }
    let role = null;
    let battlePlayerBackInfo = detail.battlePlayerBackInfo;
    switch (battlePlayerBackInfo.g) {
      case RoleSourceEnum.C_LEADERSKIN:
        role = await this.objectPlayer(detail);
        break;
      case RoleSourceEnum.C_MONSTERSHOW:
        role = await this.objectMoster(detail);
        break;
      default:
        console.error("未知的玩家类型");
        break;
    }

    return role;
  }

  /**创建用户可用形象 */
  private async objectPlayer(detail: PlayerBackInfo) {
    if (FightManager.instance.fightOver == true) {
      return;
    }
    if (isValid(this.root) == false) {
      return;
    }
    let db1 = JsonMgr.instance.jsonList.c_leaderSkin[detail.dbId];
    if (!db1) {
      ggLogger.error("没有找到对应的主角形象" + detail.dbId);
      ggLogger.error("服务端给的主角id错误，服务端需要检测一下");
      detail.dbId = PlayerModule.data.sex == 1 ? 1701 : 1711;
      db1 = JsonMgr.instance.jsonList.c_leaderSkin[detail.dbId];
    }
    let db2 = JsonMgr.instance.jsonList.c_spineShow[db1.spineId];

    let role = new (roleMap.get(db2.scrpitName))();
    this.root.addChild(role);
    role.onInitDetail(detail, detail.playerName); //
    await role.createAllSection();
    role.walk((child) => (child.layer = this.root.layer));
    this.objectsPush(role);

    return role;
  }

  /**创建怪物可用形象 */
  private async objectMoster(detail: PlayerBackInfo) {
    if (FightManager.instance.fightOver == true) {
      return;
    }
    if (isValid(this.root) == false) {
      return;
    }
    let db1 = JsonMgr.instance.jsonList.c_monsterShow[detail.dbId];
    if (!db1) {
      ggLogger.error("没有找到对应的怪物形象" + detail.dbId);
      ggLogger.error("服务端给的怪物id错误，服务端需要检测一下");
      detail.dbId = 10001;
      db1 = JsonMgr.instance.jsonList.c_monsterShow[detail.dbId];
    }

    let db2 = JsonMgr.instance.jsonList.c_spineShow[db1.spineId];
    if (!db2) {
      TipMgr.showTip("没有找到对应的怪物SPINE配置" + db1.spineId);
    }

    let role = new (roleMap.get(db2.scrpitName))();
    this.root.addChild(role);
    role.onInitDetail(detail, detail.playerName); //detail.playerName;
    await role.createAllSection();
    role.walk((child) => (child.layer = this.root.layer));
    this.objectsPush(role);

    return role;
  }

  public onRemove() {
    for (let i = 0; i < this.objects.length; i++) {
      this.objects[i].remove();
    }
    this.root && this.root.removeFromParent();
    this.root && this.root.destroy();
  }
}
