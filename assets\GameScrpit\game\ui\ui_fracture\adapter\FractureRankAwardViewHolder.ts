import { _decorator, Component, instantiate, Label, Node } from "cc";
import { ListAdapter, ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { RankReward } from "db://assets/GameScrpit/module/fracture/FractureConstant";
import FmUtils from "db://assets/GameScrpit/lib/utils/FmUtils";
const { ccclass, property } = _decorator;

@ccclass("FractureRankAwardViewHolder")
export class FractureRankAwardViewHolder extends ViewHolder {
  @property(Node)
  private lblTitle: Node;
  @property(Node)
  private itemLayout: Node;

  start() {}

  update(deltaTime: number) {}
  updateData(data: RankReward, last: RankReward) {
    //
    let rank = `第 ${data.rank} 名`;
    if (last && data.rank - last.rank > 1) {
      rank = `${last.rank + 1} - ${data.rank}`;
    }
    this.lblTitle.getComponent(Label).string = rank;
    let childIndex = 0;
    for (let i = 0; i < data.rewardList.length; i += 2) {
      //
      let item = this.itemLayout.children[childIndex];
      if (!item) {
        item = instantiate(this.itemLayout.children[0]);
        this.itemLayout.addChild(item);
      }
      item.active = true;
      FmUtils.setItemNode(item, data.rewardList[i], data.rewardList[i + 1]);
      childIndex++;
    }
    for (let i = childIndex; i < this.itemLayout.children.length; i++) {
      this.itemLayout.children[i].active = false;
    }
  }
}

export class FractureRankAwardAdapter extends ListAdapter {
  private _item: Node;
  private _data: RankReward[];
  constructor(item: Node) {
    super();
    this._item = item;
  }
  public setData(data: RankReward[]) {
    this._data = data;
    this.notifyDataSetChanged();
  }
  onCreateView(viewType: number): Node {
    let item = instantiate(this._item);
    item.active = true;
    return item;
  }
  onBindData(node: Node, position: number): void {
    node.getComponent(FractureRankAwardViewHolder).updateData(this._data[position], this._data[position - 1]);
  }
  getCount(): number {
    return this._data.length;
  }
}
