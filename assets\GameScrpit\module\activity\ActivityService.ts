import GameHttpApi from "../../game/httpNet/GameHttpApi";
import { JsonMgr } from "../../game/mgr/JsonMgr";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import { TimeUtils } from "../../lib/utils/TimeUtils";
import { FriendModule } from "../friend/FriendModule";
import { HeroModule } from "../hero/HeroModule";
import { PlayerModule } from "../player/PlayerModule";
import { ActivityModule } from "./ActivityModule";
import { BaseActivityConfigVO, ActivityUnlockEnum, RedeemPackVO } from "./ActivityConfig";

const log = Logger.getLoger(LOG_LEVEL.DEBUG);
export class ActivityService {
  public async init() {}

  // 获取所有活动信息
  public async httpGetAllActivity(): Promise<{ [key: number]: BaseActivityConfigVO }> {
    return new Promise(async (resolve, reject) => {
      let resp: { [key: number]: BaseActivityConfigVO } = await GameHttpApi.getActivityConfigAll();
      ActivityModule.data.allActivityConfig = resp;
      resolve(resp);
    });
  }
  private getUnLockCondition(value: number): number {
    switch (value) {
      case ActivityUnlockEnum.PLAYER_LEVEL:
        return PlayerModule.data.getPlayerInfo().level;
    }
    return 0;
  }
  public checkActivityUnlock(activityId: number): boolean {
    //
    let activity = ActivityModule.data.allActivityConfig[activityId];
    if (!activity) {
      return false;
    }
    if (activity.close) {
      return false;
    }
    let unLock = activity.unlockList;
    // 逻辑关系 OR 或 AND
    let unLockList: number[][] = [[]];
    for (let i = 0; i < unLock.length; i++) {
      if (unLock[i] == -1) {
        unLockList.push([]);
      } else {
        unLockList[unLockList.length - 1].push(unLock[i]);
      }
    }
    // log.log("checkActivityUnlock", unLock, unLockList);
    let unlockOR = false;
    // 字段List<Long> unlockList控制活动是否解锁。如[1,15,2,1732690217862]要按满足等级大于等于15，
    // 且时间大于指定时间戳才能解锁，如[1,15,-1,2,1732690217862],数组中的-1表示或关系，
    // 表示等级大于等于15或时间大于指定时间戳才能解锁，整体逻辑关系同编程代码中的布尔表达式，这里的或是短路或，与是短路与；
    for (let i = 0; i < unLockList.length; i++) {
      let unlockAND = true;
      for (let i2 = 0; i2 < unLockList[i].length; i2 += 2) {
        //多条件同时成立才可以解锁，只要有一个不成立，则不解锁
        if (this.getUnLockCondition(unLockList[i][i2]) < unLockList[i][i2 + 1]) {
          unlockAND = false;
          break;
        }
      }
      unlockOR = unlockAND;
      if (unlockOR) {
        break;
      }
    }
    let isActivityStart = TimeUtils.serverTime > activity.startTime && TimeUtils.serverTime < activity.endTime;

    return unlockOR && isActivityStart;
  }

  public getHasBuy(info: RedeemPackVO) {
    let itemId = info.rewardList[0];
    let info_c_item = JsonMgr.instance.getConfigItem(itemId);
    if (!info_c_item) {
      log.error("没有找到物品");
      return false;
    }
    let has = false;

    switch (info_c_item.goodsType) {
      case 1:
        if (PlayerModule.data.getItemNum(info_c_item.id) > 0) {
          has = true;
        }
        break;
      case 2:
        if (PlayerModule.data.getItemNum(info_c_item.id) > 0) {
          has = true;
        }
        break;
      case 3:
        if (HeroModule.data.getHeroMessage(info_c_item.id)) {
          has = true;
        }
        break;
      case 4:
        log.log("徒弟");
        break;
      case 5:
        log.log("珍兽");
        break;
      case 6:
        if (FriendModule.data.getFriendMessage(info_c_item.id)) {
          has = true;
        }
        break;
      case 7:
        log.log("领地装扮");
        break;
      case 8:
        log.log("坐骑道具");
        break;
      case 9:
        log.log("活动道具");
        break;
      case 10:
        log.log("武魂道具");
        break;
      case 11:
        log.log("主角皮肤");
        break;
      case 12:
        log.log("头像气泡称号皮肤");
        break;
      default:
        break;
    }

    return has;
  }
}
