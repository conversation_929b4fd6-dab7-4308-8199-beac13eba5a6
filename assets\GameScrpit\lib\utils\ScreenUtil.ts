import { view, sys, math, ResolutionPolicy } from "cc";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class ScreenUtil {
  private static _isAdapted: boolean = false;

  private static noOldIphone(model: string): boolean {
    if (model.startsWith("iPhone 11") || model.startsWith("iPhone 12") || model.startsWith("iPhone 13")) {
      return true;
    }

    if (["iPhone 14 Plus", "iPhone 14"].includes(model)) {
      return true;
    }
    return false;
  }

  public static adaptScreen() {
    // 是否适配过
    if (ScreenUtil._isAdapted) {
      return;
    }

    ScreenUtil._isAdapted = true;

    // 适配方法
    let designSize = view.getDesignResolutionSize();
    let designHighWidthRate = designSize.height / designSize.width;
    // log.log("设计分辨率：", designHighWidthRate);

    const rectCocos = sys.getSafeAreaRect();
    // log.log("cocos安全区域", rectCocos);

    const visibleSize = view.getVisibleSize();
    // log.log("可视区域", visibleSize);

    let rect: math.Rect;
    // 按机型自定义适配
    if (sys.platform == sys.Platform.WECHAT_GAME) {
      const deviceInfo = wx.getDeviceInfo();
      // log.log(deviceInfo);

      let sfLeft = 0;
      let sfTop = 0;
      let sfRight = 0;
      let sfBottom = 0;
      if (deviceInfo.model.indexOf("iPhone") > -1) {
        //苹果机型
        let visibleRate = visibleSize.height / visibleSize.width;
        if (visibleRate < designHighWidthRate) {
          // log.log("适配-16:9 更老机型");
          sfTop = 0;
        } else if (ScreenUtil.noOldIphone(deviceInfo.model)) {
          // log.log("适配-19.5:9-无灵动岛");
          sfTop = 60;
        } else {
          // log.log("适配-19.5:9-有灵动岛");
          sfTop = 80;
        }
      } else {
        // 安卓机型，只处理顶部安全区域
        sfTop = visibleSize.height - rectCocos.height - rectCocos.y;
      }

      rect = new math.Rect(
        sfLeft,
        sfBottom,
        visibleSize.width - sfLeft - sfRight,
        visibleSize.height - sfBottom - sfTop
      );
    } else {
      let sfTop = visibleSize.height - rectCocos.height - rectCocos.y;
      rect = new math.Rect(0, 0, visibleSize.width, visibleSize.height - sfTop);
      log.info("安全区域sfTop", sfTop);
      log.info("安全区域rect", rect);
      log.info("安全区域rectCocos", rectCocos);
    }

    // 安全区域比例
    let rectRatio = rect.height / rect.width;

    // 按比例适配
    if (rectRatio <= designHighWidthRate) {
      log.log("SHOW_ALL适配1-按高适配", rectRatio);
      view.setResolutionPolicy(ResolutionPolicy.SHOW_ALL);
    } else if (rectRatio < 1646 / 750) {
      log.log("SHOW_ALL适配2-按宽适配", rectRatio);
      view.setDesignResolutionSize(750, 750 * rectRatio, ResolutionPolicy.FIXED_WIDTH);
    } else {
      log.log("FIXED_WIDTH适配3", rectRatio);
      view.setDesignResolutionSize(750, 1646, ResolutionPolicy.FIXED_WIDTH);
    }
  }
}
