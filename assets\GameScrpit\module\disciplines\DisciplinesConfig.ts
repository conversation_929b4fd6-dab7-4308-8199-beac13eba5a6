import { JsonMgr } from "../../game/mgr/JsonMgr";
import {
  AchieveMessage,
  DaySignMessage,
  DayTaskMessage,
  LeaderRechargeMessage,
} from "../../game/net/protocol/Activity";
/**
 * 本地配置文件数据结构
 * 数据基础处理，过滤等，按id取
 */
export class DisciplinesConfig {}

export class DisciplinesMsgEnum {
  /**红点更新 */
  public static readonly DISCIPLINESMSGENUM_RED_DOT_UPDATE = "DISCIPLINESMSGENUM_RED_DOT_UPDATE";
}

export const DisciplinesAudioName = {
  Effect: {
    点击修行图标: 1341,
    点击下方页签: 1342,
    点击领取按钮: 1343,
    点击前往按钮: 1344,
    点击道具图标: 1345,
  },
  Sound: {},
};
