{"skeleton": {"hash": "YqMP90lTYLa+tGhTGqvkbv3MO6I=", "spine": "3.8.75", "x": -1499.5, "y": -575.5, "width": 3500, "height": 1646, "images": "./images/", "audio": "D:/spine导出/地图云雾"}, "bones": [{"name": "root"}, {"name": "yaoall", "parent": "root", "x": 123.88, "y": -815.78}, {"name": "ya<PERSON>u", "parent": "yaoall", "x": -83.03, "y": -6.16}, {"name": "yaozu3", "parent": "ya<PERSON>u", "x": -343.49, "y": 154.93, "color": "ff0000ff"}, {"name": "yaozu4", "parent": "ya<PERSON>u", "x": -370.08, "y": 281.24, "color": "ff0000ff"}, {"name": "yaozu5", "parent": "ya<PERSON>u", "x": -283.66, "y": 344.4, "color": "ff0000ff"}, {"name": "yaozu6", "parent": "ya<PERSON>u", "x": -310.25, "y": 454.09, "color": "ff0000ff"}, {"name": "yaozu7", "parent": "ya<PERSON>u", "x": -260.39, "y": 573.76, "color": "ff0000ff"}, {"name": "yaozu8", "parent": "ya<PERSON>u", "x": -326.87, "y": 673.48, "color": "ff0000ff"}, {"name": "yaozu9", "parent": "ya<PERSON>u", "x": -433.24, "y": 753.25, "color": "ff0000ff"}, {"name": "yaozu10", "parent": "ya<PERSON>u", "x": -549.58, "y": 796.46, "color": "ff0000ff"}, {"name": "yaozu11", "parent": "ya<PERSON>u", "x": -645.97, "y": 849.65, "color": "ff0000ff"}, {"name": "yaozu12", "parent": "ya<PERSON>u", "x": -735.72, "y": 962.66, "color": "ff0000ff"}, {"name": "yaozu13", "parent": "ya<PERSON>u", "x": -705.8, "y": 1082.33, "color": "ff0000ff"}, {"name": "yaozu14", "parent": "ya<PERSON>u", "x": -652.62, "y": 1192.02, "color": "ff0000ff"}, {"name": "yaozu15", "parent": "ya<PERSON>u", "x": -542.93, "y": 1271.79, "color": "ff0000ff"}, {"name": "yaozu16", "parent": "ya<PERSON>u", "x": -463.15, "y": 1321.65, "color": "ff0000ff"}, {"name": "yaozu17", "parent": "ya<PERSON>u", "x": -419.94, "y": 1428.02, "color": "ff0000ff"}, {"name": "yaozu18", "parent": "ya<PERSON>u", "x": -370.08, "y": 1551.01, "color": "ff0000ff"}, {"name": "yaozu19", "parent": "ya<PERSON>u", "x": -340.16, "y": 1667.35, "color": "ff0000ff"}, {"name": "yaozu20", "parent": "ya<PERSON>u", "x": -233.8, "y": 1760.42, "color": "ff0000ff"}, {"name": "yaozu21", "parent": "ya<PERSON>u", "x": -127.43, "y": 1777.04, "color": "ff0000ff"}, {"name": "yaozu22", "parent": "ya<PERSON>u", "x": -22.3, "y": 1738.58, "color": "ff0000ff"}, {"name": "yaozu23", "parent": "ya<PERSON>u", "x": 57.19, "y": 1670.13, "color": "ff0000ff"}, {"name": "yaozu24", "parent": "ya<PERSON>u", "x": 79.27, "y": 1612.72, "color": "ff0000ff"}, {"name": "yaozu25", "parent": "ya<PERSON>u", "x": 63.81, "y": 1531.03, "color": "ff0000ff"}, {"name": "yaozu26", "parent": "ya<PERSON>u", "x": 132.26, "y": 1475.83, "color": "ff0000ff"}, {"name": "yaozu27", "parent": "ya<PERSON>u", "x": 176.42, "y": 1405.17, "color": "ff0000ff"}, {"name": "yaozu28", "parent": "ya<PERSON>u", "x": 176.42, "y": 1330.1, "color": "ff0000ff"}, {"name": "yaozu29", "parent": "ya<PERSON>u", "x": 172, "y": 1243.99, "color": "ff0000ff"}, {"name": "yaozu30", "parent": "ya<PERSON>u", "x": 213.95, "y": 1173.33, "color": "ff0000ff"}, {"name": "yaozu31", "parent": "ya<PERSON>u", "x": 311.11, "y": 1073.97, "color": "ff0000ff"}, {"name": "yaozu32", "parent": "ya<PERSON>u", "x": 392.8, "y": 1032.02, "color": "ff0000ff"}, {"name": "yaozu33", "parent": "ya<PERSON>u", "x": 498.79, "y": 961.36, "color": "ff0000ff"}, {"name": "yaozu34", "parent": "ya<PERSON>u", "x": 591.52, "y": 888.5, "color": "ff0000ff"}, {"name": "yaozu35", "parent": "ya<PERSON>u", "x": 684.26, "y": 793.55, "color": "ff0000ff"}, {"name": "yaozu36", "parent": "ya<PERSON>u", "x": 770.37, "y": 656.66, "color": "ff0000ff"}, {"name": "yaozu37", "parent": "ya<PERSON>u", "x": 768.16, "y": 583.79, "color": "ff0000ff"}, {"name": "yaozu38", "parent": "ya<PERSON>u", "x": 730.63, "y": 464.56, "color": "ff0000ff"}, {"name": "yaozu39", "parent": "ya<PERSON>u", "x": 796.87, "y": 402.74, "color": "ff0000ff"}, {"name": "yaozu40", "parent": "ya<PERSON>u", "x": 779.2, "y": 321.04, "color": "ff0000ff"}, {"name": "yaozu41", "parent": "ya<PERSON>u", "x": 684.26, "y": 263.63, "color": "ff0000ff"}, {"name": "yaozu42", "parent": "ya<PERSON>u", "x": 523.07, "y": 219.47, "color": "ff0000ff"}, {"name": "yaozu43", "parent": "ya<PERSON>u", "x": 370.72, "y": 221.68, "color": "ff0000ff"}, {"name": "yaozu44", "parent": "ya<PERSON>u", "x": 249.28, "y": 217.27, "color": "ff0000ff"}, {"name": "yaozu45", "parent": "ya<PERSON>u", "x": 231.62, "y": 133.36, "color": "ff0000ff"}, {"name": "yaozu46", "parent": "ya<PERSON>u", "x": 169.79, "y": 75.95, "color": "ff0000ff"}, {"name": "yaozu47", "parent": "ya<PERSON>u", "x": 70.43, "y": 49.46, "color": "ff0000ff"}, {"name": "yaozu48", "parent": "ya<PERSON>u", "x": -70.88, "y": 60.5, "color": "ff0000ff"}, {"name": "yaozu49", "parent": "ya<PERSON>u", "x": -209.98, "y": 82.58, "color": "ff0000ff"}, {"name": "yaozu50", "parent": "ya<PERSON>u", "x": -286.88, "y": 108.81, "color": "ff0000ff"}, {"name": "yaozu51", "parent": "ya<PERSON>u", "x": -146.88, "y": 1610.58, "color": "ff0000ff"}, {"name": "yaozu52", "parent": "ya<PERSON>u", "x": -146.88, "y": 1471.62, "color": "ff0000ff"}, {"name": "yaozu53", "parent": "ya<PERSON>u", "x": -126.04, "y": 1328.03, "color": "ff0000ff"}, {"name": "yaozu54", "parent": "ya<PERSON>u", "x": -100.56, "y": 1193.7, "color": "ff0000ff"}, {"name": "yaozu55", "parent": "ya<PERSON>u", "x": -72.77, "y": 1059.37, "color": "ff0000ff"}, {"name": "yaozu56", "parent": "ya<PERSON>u", "x": -38.03, "y": 920.41, "color": "ff0000ff"}, {"name": "yaozu57", "parent": "ya<PERSON>u", "x": 3.66, "y": 774.51, "color": "ff0000ff"}, {"name": "yaozu58", "parent": "ya<PERSON>u", "x": 47.66, "y": 589.23, "color": "ff0000ff"}, {"name": "yaozu59", "parent": "ya<PERSON>u", "x": 82.4, "y": 447.95, "color": "ff0000ff"}, {"name": "yaozu60", "parent": "ya<PERSON>u", "x": 381.17, "y": 496.59, "color": "ff0000ff"}, {"name": "yaozu61", "parent": "ya<PERSON>u", "x": 353.37, "y": 633.23, "color": "ff0000ff"}, {"name": "yaozu62", "parent": "ya<PERSON>u", "x": 249.15, "y": 827.77, "color": "ff0000ff"}, {"name": "yaozu63", "parent": "ya<PERSON>u", "x": 179.67, "y": 971.37, "color": "ff0000ff"}, {"name": "yaozu64", "parent": "ya<PERSON>u", "x": 17.56, "y": 1369.72, "color": "ff0000ff"}, {"name": "yaozu65", "parent": "ya<PERSON>u", "x": -327.53, "y": 1221.49, "color": "ff0000ff"}, {"name": "yaozu66", "parent": "ya<PERSON>u", "x": -420.17, "y": 1094.11, "color": "ff0000ff"}, {"name": "yaozu67", "parent": "ya<PERSON>u", "x": -376.17, "y": 941.26, "color": "ff0000ff"}, {"name": "yaozu68", "parent": "ya<PERSON>u", "x": -51.93, "y": 260.35, "color": "ff0000ff"}, {"name": "yao<PERSON>z", "parent": "yaoall"}, {"name": "wuzuall", "parent": "root", "x": 872.93, "y": -118.85}, {"name": "wuzuyun", "parent": "wuzuall", "x": -186.54, "y": -54.47}, {"name": "bone3", "parent": "wuzuyun", "x": -48.49, "y": 81.72, "color": "ecff00ff"}, {"name": "bone4", "parent": "wuzuyun", "x": -166.81, "y": 109.3, "color": "ecff00ff"}, {"name": "bone5", "parent": "wuzuyun", "x": -274.69, "y": 143.2, "color": "ecff00ff"}, {"name": "bone6", "parent": "wuzuyun", "x": -353.57, "y": 217.44, "color": "ecff00ff"}, {"name": "bone7", "parent": "wuzuyun", "x": -441.73, "y": 297.48, "color": "ecff00ff"}, {"name": "bone8", "parent": "wuzuyun", "x": -490.34, "y": 342.76, "color": "ecff00ff"}, {"name": "bone9", "parent": "wuzuyun", "x": -588.94, "y": 415.84, "color": "ecff00ff"}, {"name": "bone10", "parent": "wuzuyun", "x": -682.9, "y": 498.2, "color": "ecff00ff"}, {"name": "bone11", "parent": "wuzuyun", "x": -766.42, "y": 607.24, "color": "ecff00ff"}, {"name": "bone12", "parent": "wuzuyun", "x": -801.22, "y": 731.36, "color": "ecff00ff"}, {"name": "bone13", "parent": "wuzuyun", "x": -796.58, "y": 872.88, "color": "ecff00ff"}, {"name": "bone14", "parent": "wuzuyun", "x": -740.9, "y": 979.6, "color": "ecff00ff"}, {"name": "bone15", "parent": "wuzuyun", "x": -634.7, "y": 1110.92, "color": "ecff00ff"}, {"name": "bone16", "parent": "wuzuyun", "x": -550.02, "y": 1182.84, "color": "ecff00ff"}, {"name": "bone17", "parent": "wuzuyun", "x": -466.5, "y": 1327.84, "color": "ecff00ff"}, {"name": "bone18", "parent": "wuzuyun", "x": -168.38, "y": 1455.44, "color": "ecff00ff"}, {"name": "bone19", "parent": "wuzuyun", "x": 185.73, "y": 1400.92, "color": "ecff00ff"}, {"name": "bone20", "parent": "wuzuyun", "x": 295.93, "y": 1238.52, "color": "ecff00ff"}, {"name": "bone21", "parent": "wuzuyun", "x": 285.49, "y": 1139.92, "color": "ecff00ff"}, {"name": "bone22", "parent": "wuzuyun", "x": 255.33, "y": 1057.56, "color": "ecff00ff"}, {"name": "bone23", "parent": "wuzuyun", "x": 197.33, "y": 1005.36, "color": "ecff00ff"}, {"name": "bone24", "parent": "wuzuyun", "x": 139.33, "y": 948.52, "color": "ecff00ff"}, {"name": "bone25", "parent": "wuzuyun", "x": 208.93, "y": 874.28, "color": "ecff00ff"}, {"name": "bone26", "parent": "wuzuyun", "x": 298.25, "y": 800.04, "color": "ecff00ff"}, {"name": "bone27", "parent": "wuzuyun", "x": 414.25, "y": 745.52, "color": "ecff00ff"}, {"name": "bone28", "parent": "wuzuyun", "x": 524.45, "y": 682.88, "color": "ecff00ff"}, {"name": "bone29", "parent": "wuzuyun", "x": 688.01, "y": 615.6, "color": "ecff00ff"}, {"name": "bone30", "parent": "wuzuyun", "x": 717.01, "y": 534.4, "color": "ecff00ff"}, {"name": "bone31", "parent": "wuzuyun", "x": 737.89, "y": 449.72, "color": "ecff00ff"}, {"name": "bone32", "parent": "wuzuyun", "x": 736.73, "y": 351.12, "color": "ecff00ff"}, {"name": "bone33", "parent": "wuzuyun", "x": 707.73, "y": 260.64, "color": "ecff00ff"}, {"name": "bone34", "parent": "wuzuyun", "x": 609.1, "y": 220.31, "color": "ecff00ff"}, {"name": "bone35", "parent": "wuzuyun", "x": 498.93, "y": 167.34, "color": "ecff00ff"}, {"name": "bone36", "parent": "wuzuyun", "x": 377.59, "y": 141.4, "color": "ecff00ff"}, {"name": "bone37", "parent": "wuzuyun", "x": 271.29, "y": 120.8, "color": "ecff00ff"}, {"name": "bone38", "parent": "wuzuyun", "x": 112.65, "y": 97.08, "color": "ecff00ff"}, {"name": "bone39", "parent": "wuzuyun", "x": -93.11, "y": 1276.13, "color": "ecff00ff"}, {"name": "bone40", "parent": "wuzuyun", "x": -235.89, "y": 1137.37, "color": "ecff00ff"}, {"name": "bone41", "parent": "wuzuyun", "x": -332.42, "y": 940.29, "color": "ecff00ff"}, {"name": "bone42", "parent": "wuzuyun", "x": -326.39, "y": 781.42, "color": "ecff00ff"}, {"name": "bone43", "parent": "wuzuyun", "x": -177.57, "y": 570.27, "color": "ecff00ff"}, {"name": "bone44", "parent": "wuzuyun", "x": -20.72, "y": 383.24, "color": "ecff00ff"}, {"name": "bone45", "parent": "wuzuyun", "x": 226.64, "y": 353.08, "color": "ecff00ff"}, {"name": "bone46", "parent": "wuzuyun", "x": 220.6, "y": 590.38, "color": "ecff00ff"}, {"name": "bone47", "parent": "wuzuyun", "x": -64.96, "y": 709.02, "color": "ecff00ff"}, {"name": "bone48", "parent": "wuzuyun", "x": -143.39, "y": 912.14, "color": "ecff00ff"}, {"name": "bone49", "parent": "wuzuyun", "x": -66.97, "y": 1101.17, "color": "ecff00ff"}, {"name": "bone50", "parent": "wuzuyun", "x": -543.58, "y": 662.77, "color": "ecff00ff"}, {"name": "bone51", "parent": "wuzuyun", "x": -398.78, "y": 509.94, "color": "ecff00ff"}, {"name": "bone52", "parent": "wuzuyun", "x": -147.41, "y": 224.37, "color": "ecff00ff"}, {"name": "wuzujz", "parent": "wuzuall"}, {"name": "mingzuall", "parent": "root", "x": 1533.62, "y": -759.64}, {"name": "mingzucloud", "parent": "mingzuall"}, {"name": "mingzujz", "parent": "mingzuall"}, {"name": "bone", "parent": "mingzuall", "x": 112.8, "y": 4.77, "color": "000affff"}, {"name": "bone2", "parent": "bone", "x": -417.18, "y": 45.15, "color": "000affff"}, {"name": "bone53", "parent": "bone", "x": -730.52, "y": 38.62, "color": "000affff"}, {"name": "bone54", "parent": "bone", "x": -819.74, "y": 116.96, "color": "000affff"}, {"name": "bone55", "parent": "bone", "x": -959, "y": 180.06, "color": "000affff"}, {"name": "bone56", "parent": "bone", "x": -1067.8, "y": 251.87, "color": "000affff"}, {"name": "bone57", "parent": "bone", "x": -1146.14, "y": 338.91, "color": "000affff"}, {"name": "bone58", "parent": "bone", "x": -1254.94, "y": 473.82, "color": "000affff"}, {"name": "bone59", "parent": "bone", "x": -1298.46, "y": 600.03, "color": "000affff"}, {"name": "bone60", "parent": "bone", "x": -1348.51, "y": 719.71, "color": "000affff"}, {"name": "bone61", "parent": "bone", "x": -1263.64, "y": 839.39, "color": "000affff"}, {"name": "bone62", "parent": "bone", "x": -1164.92, "y": 881.99, "color": "000affff"}, {"name": "bone63", "parent": "bone", "x": -1037.87, "y": 887.52, "color": "000affff"}, {"name": "bone64", "parent": "bone", "x": -917.72, "y": 859.9, "color": "000affff"}, {"name": "bone65", "parent": "bone", "x": -811.38, "y": 888.9, "color": "000affff"}, {"name": "bone66", "parent": "bone", "x": -749.24, "y": 966.23, "color": "000affff"}, {"name": "bone67", "parent": "bone", "x": -641.52, "y": 964.85, "color": "000affff"}, {"name": "bone68", "parent": "bone", "x": -579.38, "y": 1047.71, "color": "000affff"}, {"name": "bone69", "parent": "bone", "x": -595.95, "y": 1158.19, "color": "000affff"}, {"name": "bone70", "parent": "bone", "x": -435.34, "y": 1165.1, "color": "000affff"}, {"name": "bone71", "parent": "bone", "x": -295.86, "y": 1149.91, "color": "000affff"}, {"name": "bone72", "parent": "bone", "x": -139.81, "y": 1130.57, "color": "000affff"}, {"name": "bone73", "parent": "bone", "x": -7.23, "y": 1130.57, "color": "000affff"}, {"name": "bone74", "parent": "bone", "x": 89.44, "y": 1102.95, "color": "000affff"}, {"name": "bone75", "parent": "bone", "x": 147.44, "y": 1057.38, "color": "000affff"}, {"name": "bone76", "parent": "bone", "x": 289.77, "y": 1147.14, "color": "000affff"}, {"name": "bone77", "parent": "bone", "x": 376.77, "y": 1170.62, "color": "000affff"}, {"name": "bone78", "parent": "bone", "x": 383.44, "y": 962.97, "color": "000affff"}, {"name": "bone79", "parent": "bone", "x": 351.1, "y": 710.72, "color": "000affff"}, {"name": "bone80", "parent": "bone", "x": 351.1, "y": 484.34, "color": "000affff"}, {"name": "bone81", "parent": "bone", "x": 342.48, "y": 257.96, "color": "000affff"}, {"name": "bone82", "parent": "bone", "x": 260.55, "y": 115.66, "color": "000affff"}, {"name": "bone83", "parent": "bone", "x": -69.32, "y": 81.16, "color": "000affff"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parent": "root", "x": 1708.6, "y": 180.22}, {"name": "shenzucloud", "parent": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parent": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "shenzuall3", "parent": "shenzucloud", "x": -140.05, "y": 55.7, "color": "c400ffff"}, {"name": "shenzuall4", "parent": "shenzucloud", "x": -260.88, "y": 61.97, "color": "c400ffff"}, {"name": "shenzuall5", "parent": "shenzucloud", "x": -412.13, "y": 72.71, "color": "c400ffff"}, {"name": "shenzuall6", "parent": "shenzucloud", "x": -571.78, "y": 117.01, "color": "c400ffff"}, {"name": "shenzuall7", "parent": "shenzucloud", "x": -663.07, "y": 255.73, "color": "c400ffff"}, {"name": "shenzuall8", "parent": "shenzucloud", "x": -813.71, "y": 311.71, "color": "c400ffff"}, {"name": "shenzuall9", "parent": "shenzucloud", "x": -933.64, "y": 374.36, "color": "c400ffff"}, {"name": "shenzuall10", "parent": "shenzucloud", "x": -984.66, "y": 448.65, "color": "c400ffff"}, {"name": "shenzuall11", "parent": "shenzucloud", "x": -964.97, "y": 547.1, "color": "c400ffff"}, {"name": "shenzuall12", "parent": "shenzucloud", "x": -887.1, "y": 626.75, "color": "c400ffff"}, {"name": "shenzuall13", "parent": "shenzucloud", "x": -888.89, "y": 684.93, "color": "c400ffff"}, {"name": "shenzuall14", "parent": "shenzucloud", "x": -972.13, "y": 707.3, "color": "c400ffff"}, {"name": "shenzuall15", "parent": "shenzucloud", "x": -944.38, "y": 796.8, "color": "c400ffff"}, {"name": "shenzuall16", "parent": "shenzucloud", "x": -907.69, "y": 878.25, "color": "c400ffff"}, {"name": "shenzuall17", "parent": "shenzucloud", "x": -661.75, "y": 875.07, "color": "c400ffff"}, {"name": "shenzuall18", "parent": "shenzucloud", "x": -492.29, "y": 875.07, "color": "c400ffff"}, {"name": "shenzuall19", "parent": "shenzucloud", "x": -230.79, "y": 871.64, "color": "c400ffff"}, {"name": "shenzuall20", "parent": "shenzucloud", "x": 15.39, "y": 864.77, "color": "c400ffff"}, {"name": "shenzuall21", "parent": "shenzucloud", "x": 244.39, "y": 861.33, "color": "c400ffff"}, {"name": "shenzuall22", "parent": "shenzucloud", "x": 235.23, "y": 644.93, "color": "c400ffff"}, {"name": "shenzuall23", "parent": "shenzucloud", "x": 230.65, "y": 473.18, "color": "c400ffff"}, {"name": "shenzuall24", "parent": "shenzucloud", "x": 228.36, "y": 245.32, "color": "c400ffff"}, {"name": "shenzuall25", "parent": "shenzucloud", "x": 212.33, "y": 119.37, "color": "c400ffff"}, {"name": "shenzuall26", "parent": "shenzucloud", "x": 101.26, "y": 85.02, "color": "c400ffff"}, {"name": "shenzuall27", "parent": "shenzucloud", "x": -18.96, "y": 67.85, "color": "c400ffff"}, {"name": "shenzuall28", "parent": "shenzucloud", "x": 58.14, "y": 276.4, "color": "c400ffff"}, {"name": "shenzuall29", "parent": "shenzucloud", "x": -244.16, "y": 307.67, "color": "c400ffff"}, {"name": "shenzuall30", "parent": "shenzucloud", "x": -562.83, "y": 382.34, "color": "c400ffff"}, {"name": "shenzuall31", "parent": "shenzucloud", "x": -730.5, "y": 574.85, "color": "c400ffff"}, {"name": "shenzuall32", "parent": "shenzucloud", "x": -600.09, "y": 740.45, "color": "c400ffff"}, {"name": "shenzuall33", "parent": "shenzucloud", "x": -188.16, "y": 668, "color": "c400ffff"}, {"name": "shenzuall34", "parent": "shenzucloud", "x": 78.87, "y": 616.25, "color": "c400ffff"}, {"name": "shenzuall35", "parent": "shenzucloud", "x": -43.26, "y": 454.79, "color": "c400ffff"}, {"name": "shenzuall36", "parent": "shenzucloud", "x": -337.2, "y": 510.68, "color": "c400ffff"}], "slots": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "bone": "ya<PERSON>u"}, {"name": "wuzuyun", "bone": "wuzuyun"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "bone": "mingzucloud"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "bone": "shenzucloud"}, {"name": "yao", "bone": "yao<PERSON>z"}, {"name": "yaozulight", "bone": "yaoall", "blend": "additive"}, {"name": "wu2", "bone": "wuzujz"}, {"name": "wuzulight", "bone": "wuzuall", "color": "ffeb00ff", "blend": "additive"}, {"name": "ming", "bone": "mingzujz"}, {"name": "mingzulight", "bone": "mingzuall", "color": "ffc6005c", "blend": "additive"}, {"name": "shen", "bone": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "shenzulight", "bone": "<PERSON><PERSON><PERSON><PERSON>", "color": "ffcd0051", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"mingzuyun": {"mingzuyun": {"type": "mesh", "uvs": [0.26689, 0.61505, 0.22616, 0.59743, 0.18528, 0.57975, 0.15568, 0.54296, 0.12061, 0.49936, 0.09883, 0.4633, 0.07442, 0.42288, 0.06642, 0.38864, 0.05518, 0.34053, 0.03866, 0.31126, 0.01976, 0.27778, 0.04651, 0.24155, 0.06826, 0.21209, 0.1082, 0.19308, 0.1568, 0.16993, 0.26304, 0.18954, 0.30846, 0.13562, 0.34541, 0.13856, 0.3739, 0.0964, 0.35894, 0.01168, 0.41958, 0.00859, 0.50022, 0.03021, 0.56974, 0.03829, 0.63662, 0.03549, 0.69803, 0.06203, 0.75834, 0.02013, 0.82961, 0.02153, 0.86346, 0.07828, 0.85939, 0.15141, 0.85376, 0.25273, 0.84852, 0.3468, 0.83823, 0.53186, 0.83198, 0.64419, 0.77468, 0.64681, 0.67396, 0.65142, 0.56424, 0.65644, 0.46199, 0.66112, 0.399, 0.664, 0.34506, 0.66647, 0.29639, 0.66869, 0.14761, 0.24404, 0.11757, 0.28867, 0.12842, 0.37473, 0.14343, 0.47036, 0.1985, 0.55642, 0.41376, 0.06447, 0.47466, 0.0496, 0.35869, 0.17497, 0.31447, 0.22491, 0.81841, 0.24935, 0.79171, 0.07935, 0.71495, 0.11335, 0.62151, 0.12822, 0.50553, 0.10485, 0.48718, 0.20685, 0.43295, 0.32267, 0.42544, 0.53836, 0.65071, 0.55855, 0.8109, 0.52774, 0.77836, 0.34711, 0.63903, 0.27379, 0.55559, 0.38004, 0.64487, 0.41936], "triangles": [56, 55, 61, 44, 55, 56, 36, 56, 35, 36, 37, 56, 37, 38, 56, 39, 0, 38, 56, 0, 44, 56, 38, 0, 0, 1, 44, 2, 44, 1, 43, 44, 3, 2, 3, 44, 48, 43, 42, 5, 42, 43, 4, 5, 43, 48, 44, 43, 4, 43, 3, 6, 7, 42, 5, 6, 42, 8, 9, 41, 42, 41, 40, 8, 41, 42, 7, 8, 42, 9, 10, 11, 9, 11, 41, 12, 13, 41, 11, 12, 41, 40, 13, 14, 41, 13, 40, 15, 40, 14, 15, 16, 48, 55, 44, 48, 48, 42, 40, 48, 40, 15, 47, 48, 16, 47, 17, 18, 47, 16, 17, 55, 47, 54, 48, 47, 55, 61, 54, 60, 55, 54, 61, 54, 18, 45, 54, 47, 18, 45, 19, 20, 18, 19, 45, 45, 20, 46, 53, 45, 46, 46, 20, 21, 53, 21, 22, 46, 21, 53, 54, 45, 53, 52, 54, 53, 52, 22, 23, 53, 22, 52, 52, 23, 24, 51, 24, 25, 52, 24, 51, 51, 50, 49, 60, 52, 51, 60, 51, 49, 54, 52, 60, 51, 25, 50, 50, 25, 26, 50, 26, 27, 28, 50, 27, 49, 50, 28, 29, 49, 28, 30, 49, 29, 59, 60, 49, 59, 49, 30, 62, 60, 59, 58, 59, 30, 62, 59, 58, 31, 58, 30, 57, 62, 58, 32, 58, 31, 33, 57, 58, 33, 58, 32, 34, 57, 33, 61, 60, 62, 57, 35, 61, 57, 61, 62, 35, 57, 34, 56, 61, 35], "vertices": [2, 130, 104.63, -78.26, 0.12746, 129, -34.63, -15.16, 0.87254, 2, 130, 9.26, -45.87, 0.8613, 129, -130, 17.23, 0.1387, 2, 131, 22.31, -85.16, 0.51716, 130, -86.49, -13.35, 0.48284, 2, 132, 31.33, -104.54, 0.18282, 131, -47, -17.5, 0.81718, 3, 133, 58, -159.27, 0.08588, 132, -50.8, -24.35, 0.89433, 131, -129.14, 62.69, 0.01979, 3, 139, -330.23, -479.03, 4e-05, 133, 6.99, -92.95, 0.54517, 132, -101.81, 41.96, 0.4548, 3, 134, -6.66, -144.84, 0.0679, 133, -50.18, -18.63, 0.90595, 132, -158.98, 116.28, 0.02616, 2, 134, -25.4, -81.87, 0.47901, 133, -68.92, 44.34, 0.52099, 3, 135, -1.69, -113.07, 0.21798, 134, -51.74, 6.61, 0.77988, 133, -95.26, 132.82, 0.00214, 2, 135, -40.38, -59.24, 0.76213, 134, -90.43, 60.44, 0.23787, 2, 135, -84.63, 2.32, 0.99981, 134, -134.68, 122, 0.00019, 2, 136, -106.85, -50.74, 0.25292, 135, -21.98, 68.94, 0.74708, 2, 136, -55.9, 3.44, 0.76383, 135, 28.96, 123.12, 0.23617, 3, 137, -61.09, -4.19, 0.43542, 136, 37.63, 38.42, 0.56246, 134, 72.45, 277.78, 0.00213, 3, 138, -74.33, 32.85, 0.43167, 137, 52.72, 38.37, 0.5683, 134, 186.26, 320.33, 3e-05, 13, 158, -794.07, 803.14, 3e-05, 155, -1214.49, 399.97, 1e-05, 154, -1214.49, 173.59, 0, 150, -1010.83, -173.08, 1e-05, 147, -723.58, -246.27, 1e-05, 140, -52, -4.59, 0.53856, 139, 54.34, 24.41, 0.45889, 133, 391.55, 410.48, 2e-05, 132, 282.75, 545.39, 0.0006, 131, 204.42, 632.43, 0.00018, 130, 95.62, 704.24, 0.00042, 129, -43.65, 767.35, 0.00087, 127, -446.21, 839.15, 0.00039, 1, 141, -7.77, 17.23, 1, 4, 143, -91.09, -69.65, 0.01484, 142, -28.94, 13.21, 0.80274, 141, 78.77, 11.83, 0.18095, 140, 140.92, 89.16, 0.00148, 3, 144, -7.81, -102.6, 0.05337, 143, -24.38, 7.88, 0.89399, 142, 37.77, 90.74, 0.05264, 2, 144, -42.83, 53.2, 1, 131, 429.03, 959.53, 0, 3, 145, -61.43, 51.98, 0.64845, 144, 99.17, 58.88, 0.35109, 143, 82.6, 169.36, 0.00046, 2, 146, -12.05, 27.41, 0.91538, 145, 127.43, 12.22, 0.08462, 4, 148, -137.86, 31.89, 0.01143, 147, -5.28, 31.89, 0.9372, 146, 150.77, 12.56, 0.05137, 131, 922.71, 910.59, 0, 4, 149, -77.88, 64.65, 0.13208, 148, 18.79, 37.03, 0.86792, 139, 929.28, 307.7, 0, 131, 1079.36, 915.73, 0, 4, 151, -134.4, -28.34, 0.15542, 150, 7.92, 61.43, 0.5658, 149, 65.93, 15.85, 0.27878, 131, 1223.17, 866.94, 0, 2, 152, -80.17, 25.22, 0.2138, 151, 6.84, 48.7, 0.7862, 4, 153, 80.08, 230.31, 0.13575, 152, 86.75, 22.66, 0.86425, 139, 1381.25, 333.38, 0, 131, 1531.33, 941.41, 0, 3, 153, 159.36, 125.94, 0.48676, 152, 166.04, -81.71, 0.51324, 131, 1610.61, 837.04, 0, 4, 154, 182.18, 243.7, 0.08867, 153, 149.84, -8.55, 0.76544, 152, 156.51, -216.2, 0.14588, 131, 1601.08, 702.55, 0, 5, 155, 168.98, 283.77, 0.03417, 154, 168.98, 57.39, 0.62961, 153, 136.64, -194.86, 0.33602, 152, 143.31, -402.51, 0.00019, 131, 1587.89, 516.24, 0, 6, 156, 165.35, 337.15, 0.00185, 155, 156.72, 110.77, 0.49293, 154, 156.72, -115.61, 0.49205, 153, 124.38, -367.87, 0.01316, 139, 1425.55, -264.79, 0, 131, 1575.63, 343.23, 0, 5, 157, 223.17, 139.13, 0.05879, 156, 141.24, -3.17, 0.82059, 155, 132.62, -229.55, 0.12062, 139, 1401.44, -605.11, 0, 131, 1551.52, 2.92, 0, 3, 157, 208.53, -67.45, 0.52469, 156, 126.61, -209.74, 0.47531, 131, 1536.89, -203.66, 0, 3, 157, 74.32, -72.27, 0.80872, 156, -7.61, -214.56, 0.19128, 131, 1402.67, -208.48, 0, 5, 158, 168.31, -46.25, 0.47582, 157, -161.55, -80.74, 0.52344, 155, -252.11, -449.42, 0.00072, 150, -48.44, -1022.46, 2e-05, 147, 238.8, -1095.66, 0, 10, 158, -88.65, -55.48, 0.78828, 154, -509.07, -685.03, 4e-05, 150, -305.41, -1031.7, 0.00022, 147, -18.16, -1104.89, 0.00031, 146, 137.89, -1124.22, 0.00013, 143, 421.41, -1022.03, 9e-05, 142, 483.55, -939.17, 0.00128, 140, 653.42, -863.21, 0.00016, 131, 909.83, -226.19, 0, 127, 259.21, -19.47, 0.2095, 3, 158, -328.13, -64.09, 0.02541, 131, 670.36, -234.79, 0, 127, 19.73, -28.07, 0.97459, 9, 150, -692.4, -1045.6, 1e-05, 147, -405.15, -1118.79, 3e-05, 146, -249.1, -1138.13, 2e-05, 142, 96.56, -953.07, 0.00291, 140, 266.42, -877.12, 0.00265, 139, 372.76, -848.12, 0.00065, 129, 274.78, -105.18, 0.00155, 128, 185.56, -26.84, 0.39892, 127, -127.78, -33.37, 0.59327, 6, 142, -29.77, -957.61, 0.00071, 140, 140.1, -881.66, 0.00071, 139, 246.43, -852.66, 0.00012, 131, 396.51, -244.63, 0, 128, 59.23, -31.38, 0.86733, 127, -254.11, -37.91, 0.13113, 3, 131, 282.53, -248.73, 0, 129, 34.47, -113.81, 0.17677, 128, -54.75, -35.48, 0.82323, 14, 142, -492.2, -180.77, 3e-05, 140, -322.34, -104.81, 0.00027, 139, -216, -75.81, 0.07071, 138, -95.86, -103.43, 0.22602, 137, 31.2, -97.91, 0.39492, 136, 129.92, -55.31, 0.15795, 135, 214.78, 64.37, 0.01536, 134, 164.73, 184.05, 0.10907, 133, 121.21, 310.26, 0.01149, 132, 12.41, 445.17, 0.01172, 131, -65.92, 532.21, 0.00028, 130, -174.72, 604.02, 0.00083, 129, -313.99, 667.13, 0.00132, 127, -716.55, 738.93, 5e-05, 13, 142, -562.55, -262.84, 1e-05, 140, -392.68, -186.88, 0.00068, 139, -286.35, -157.88, 0.0466, 138, -166.2, -185.5, 0.05949, 137, -39.15, -179.98, 0.1356, 136, 59.57, -137.37, 0.20778, 135, 144.44, -17.69, 0.15116, 134, 94.39, 101.99, 0.35544, 133, 50.87, 228.19, 0.02748, 132, -57.93, 363.11, 0.01417, 130, -245.07, 521.95, 0.0005, 129, -384.33, 585.06, 0.00108, 127, -786.89, 656.87, 1e-05, 13, 142, -537.14, -421.11, 0.00173, 140, -367.28, -345.16, 0.00728, 139, -260.94, -316.15, 0.05316, 138, -140.8, -343.77, 0.02657, 137, -13.75, -338.25, 0.02356, 136, 84.98, -295.65, 0.0137, 134, 119.79, -56.29, 0.32, 133, 76.27, 69.92, 0.42563, 132, -32.53, 204.83, 0.11975, 131, -110.86, 291.87, 0.00163, 130, -219.66, 363.68, 0.00276, 129, -358.93, 426.78, 0.00382, 127, -761.49, 498.59, 0.0004, 12, 147, -1003.69, -762.69, 0, 142, -501.97, -596.97, 0.0008, 140, -332.11, -521.02, 0.00234, 139, -225.77, -492.01, 0.00748, 138, -105.63, -519.63, 0.00163, 137, 21.43, -514.11, 0.00064, 134, 154.96, -232.15, 0.00344, 133, 111.44, -105.94, 0.09971, 132, 2.64, 28.97, 0.87983, 130, -184.49, 187.82, 0.00254, 129, -323.76, 250.92, 0.00138, 127, -726.32, 322.73, 0.0002, 11, 150, -1161.97, -847.77, 1e-05, 147, -874.72, -920.96, 1e-05, 146, -718.67, -940.3, 0, 142, -373.01, -755.24, 0.00058, 140, -203.15, -679.29, 0.00112, 139, -96.81, -650.29, 0.00157, 138, 23.34, -677.91, 6e-05, 134, 283.93, -390.42, 3e-05, 131, 53.27, -42.26, 0.4801, 130, -55.53, 29.55, 0.51631, 127, -597.35, 164.46, 0.00022, 11, 158, -441.08, 1033.15, 0.00025, 155, -861.5, 629.97, 0.0001, 154, -861.5, 403.59, 0.00018, 150, -657.84, 56.93, 0.00035, 147, -370.59, -16.26, 0.00066, 146, -214.54, -35.6, 0.00407, 145, -75.06, -50.79, 0.40139, 144, 85.55, -43.88, 0.24194, 143, 68.98, 66.6, 0.35068, 129, 309.34, 997.35, 9e-05, 127, -93.22, 1069.16, 0.00028, 13, 158, -298.44, 1060.5, 0.00051, 155, -718.86, 657.33, 0.00023, 154, -718.86, 430.95, 0.00043, 150, -515.2, 84.29, 0.00088, 147, -227.95, 11.09, 0.00133, 146, -71.9, -8.24, 0.48145, 145, 67.58, -23.43, 0.4763, 143, 211.62, 93.95, 0.03102, 142, 273.76, 176.81, 0.00687, 140, 443.63, 252.77, 0.00023, 131, 700.05, 889.8, 0, 129, 451.98, 1024.71, 0.00021, 127, 49.42, 1096.52, 0.00054, 17, 158, -570.05, 829.93, 0.00321, 155, -990.47, 426.76, 0.00113, 154, -990.47, 200.38, 0.00145, 150, -786.8, -146.29, 0.00273, 147, -499.55, -219.48, 0.00555, 146, -343.5, -238.81, 0.00852, 145, -204.02, -254, 0.00093, 143, -59.99, -136.62, 0.02397, 142, 2.16, -53.76, 0.74195, 141, 109.88, -55.14, 0.09346, 140, 172.02, 22.2, 0.10065, 139, 278.36, 51.2, 0.00058, 132, 506.78, 572.18, 0.00061, 131, 428.44, 659.22, 0.00019, 130, 319.64, 731.03, 0.00134, 129, 180.38, 794.14, 0.00639, 127, -222.18, 865.94, 0.00733, 17, 158, -673.61, 738.09, 0.00368, 155, -1094.03, 334.92, 0.00114, 154, -1094.03, 108.54, 0.00124, 150, -890.36, -238.12, 0.00241, 147, -603.12, -311.32, 0.00394, 146, -447.06, -330.65, 0.00361, 142, -101.4, -145.6, 0.23926, 141, 6.31, -146.98, 0.06884, 140, 68.46, -69.64, 0.54461, 139, 174.8, -40.64, 0.08351, 134, 555.53, 219.23, 0.00126, 133, 512.01, 345.43, 0.00041, 132, 403.21, 480.35, 0.00608, 131, 324.88, 567.39, 0.00213, 130, 216.08, 639.19, 0.00634, 129, 76.81, 702.3, 0.01801, 127, -325.75, 774.11, 0.01355, 5, 155, 86.19, 289.98, 0.0154, 154, 86.19, 63.6, 0.67279, 153, 53.85, -188.65, 0.30935, 150, 289.85, -283.07, 0.00245, 131, 1505.09, 522.44, 0, 5, 153, -8.68, 123.99, 0.32682, 152, -2.01, -83.67, 0.47128, 151, 85, -60.19, 0.17682, 150, 227.32, 29.57, 0.02508, 131, 1442.57, 835.08, 0, 12, 158, 264.31, 943.26, 0.00062, 155, -156.11, 540.09, 8e-05, 154, -156.11, 313.71, 0.02532, 153, -188.45, 61.46, 0.10022, 151, -94.77, -122.72, 0.10292, 150, 47.56, -32.95, 0.76804, 147, 334.8, -106.15, 0.00143, 143, 774.37, -23.29, 0.00023, 142, 836.52, 59.57, 0.00079, 140, 1006.38, 135.53, 9e-05, 129, 1014.73, 907.47, 5e-05, 127, 612.17, 979.28, 0.00021, 14, 158, 45.46, 915.91, 0.01182, 155, -374.96, 512.73, 0.00934, 154, -374.96, 286.35, 0.04872, 153, -407.3, 34.1, 0.00126, 150, -171.29, -60.31, 0.23275, 149, -113.29, -105.88, 0.11604, 148, -16.62, -133.5, 0.28019, 147, 115.96, -133.5, 0.2318, 146, 272.01, -152.84, 0.01515, 143, 555.52, -50.64, 0.0135, 142, 617.67, 32.22, 0.02714, 140, 787.53, 108.17, 0.00381, 129, 795.89, 880.11, 0.00192, 127, 393.33, 951.92, 0.00656, 14, 158, -226.14, 958.89, 0.00776, 155, -646.56, 555.72, 0.00414, 154, -646.56, 329.34, 0.00914, 150, -442.9, -17.32, 0.02084, 148, -288.23, -90.51, 0.00285, 147, -155.65, -90.51, 0.20352, 146, 0.4, -109.85, 0.47476, 145, 139.88, -125.04, 0.09237, 143, 283.92, -7.65, 0.10103, 142, 346.06, 75.21, 0.06719, 140, 515.93, 151.16, 0.00615, 130, 663.54, 860, 1e-05, 129, 524.28, 923.1, 0.0029, 127, 121.72, 994.91, 0.00734, 16, 158, -269.13, 771.31, 0.03551, 155, -689.55, 368.14, 0.01787, 154, -689.55, 141.76, 0.0303, 150, -485.89, -204.91, 0.05767, 148, -331.21, -278.1, 0.00706, 147, -198.64, -278.1, 0.13863, 146, -42.59, -297.43, 0.15704, 145, 96.89, -312.62, 0.03249, 143, 240.93, -195.24, 0.15293, 142, 303.07, -112.38, 0.26366, 140, 472.94, -36.42, 0.04797, 139, 579.28, -7.42, 0.00091, 132, 807.69, 513.56, 8e-05, 130, 620.56, 672.41, 0.00109, 129, 481.29, 735.52, 0.01768, 127, 78.73, 807.32, 0.03912, 19, 158, -396.14, 558.32, 0.05891, 155, -816.56, 155.15, 0.01999, 154, -816.56, -71.23, 0.02223, 150, -612.9, -417.89, 0.03672, 148, -458.22, -491.08, 0.00069, 147, -325.65, -491.08, 0.05394, 146, -169.6, -510.42, 0.04646, 145, -30.11, -525.61, 0.00111, 143, 113.92, -408.22, 0.05129, 142, 176.07, -325.36, 0.33585, 140, 345.93, -249.41, 0.15026, 139, 452.27, -220.41, 0.02684, 134, 833, 39.46, 3e-05, 132, 680.68, 300.58, 0.00381, 131, 602.35, 387.62, 0.00135, 130, 493.55, 459.43, 0.0108, 129, 354.28, 522.53, 0.06629, 128, 265.07, 600.87, 0.00304, 127, -48.28, 594.34, 0.11042, 16, 158, -413.73, 161.66, 0.0686, 155, -834.15, -241.51, 0.00907, 154, -834.15, -467.89, 0.00655, 150, -630.48, -814.55, 0.01084, 147, -343.23, -887.75, 0.01229, 146, -187.18, -907.08, 0.00736, 143, 96.33, -804.89, 0.00493, 142, 158.48, -722.03, 0.08105, 140, 328.34, -646.07, 0.05241, 139, 434.68, -617.07, 0.01648, 132, 663.1, -96.08, 0.00101, 131, 584.76, -9.04, 0.00012, 130, 475.96, 62.76, 0.00752, 129, 336.7, 125.87, 0.11902, 128, 247.48, 204.2, 0.10393, 127, -65.86, 197.68, 0.49883, 15, 158, 113.85, 124.54, 0.54504, 157, -216.01, 90.04, 0.25807, 156, -297.94, -52.26, 0.0659, 155, -306.57, -278.64, 0.06309, 154, -306.57, -505.02, 0.00886, 150, -102.9, -851.68, 0.0127, 148, 51.77, -924.87, 3e-05, 147, 184.35, -924.87, 0.00775, 146, 340.4, -944.21, 0.00256, 143, 623.91, -842.01, 0.00261, 142, 686.06, -759.15, 0.01457, 140, 855.92, -683.2, 0.00396, 139, 962.26, -654.2, 0, 129, 864.28, 88.74, 0.00215, 127, 461.72, 160.55, 0.01271, 5, 157, 159.15, 146.71, 0.03929, 156, 77.23, 4.41, 0.85783, 155, 68.6, -221.97, 0.10289, 139, 1337.43, -597.53, 0, 131, 1487.51, 10.5, 0, 12, 158, 412.82, 513.38, 0.00468, 155, -7.6, 110.21, 0.5107, 154, -7.6, -116.17, 0.47014, 150, 196.06, -462.83, 0.00762, 148, 350.73, -536.03, 0.0001, 147, 483.31, -536.03, 0.00233, 146, 639.36, -555.36, 0.00036, 143, 922.88, -453.17, 0.00054, 142, 985.02, -370.31, 0.00215, 140, 1154.88, -294.35, 0.0004, 129, 1163.24, 477.59, 0.00019, 127, 760.68, 549.4, 0.00081, 16, 158, 86.5, 648.21, 0.07239, 157, -243.37, 613.71, 5e-05, 156, -325.3, 471.42, 0.00168, 155, -333.92, 245.04, 0.09903, 154, -333.92, 18.66, 0.23002, 153, -366.26, -233.6, 0.00774, 150, -130.26, -328.01, 0.266, 149, -72.26, -373.58, 0.00264, 148, 24.41, -401.2, 0.02835, 147, 156.99, -401.2, 0.10757, 146, 313.04, -420.53, 0.02799, 143, 596.56, -318.34, 0.02567, 142, 658.7, -235.48, 0.07648, 140, 828.57, -159.53, 0.01558, 129, 836.92, 612.41, 0.00806, 127, 434.36, 684.22, 0.03075, 18, 158, -108.9, 452.81, 0.1929, 157, -438.77, 418.31, 0.00183, 156, -520.7, 276.02, 0.00758, 155, -529.32, 49.64, 0.09091, 154, -529.32, -176.74, 0.07925, 150, -325.66, -523.41, 0.09592, 148, -170.99, -596.6, 0.00579, 147, -38.41, -596.6, 0.07941, 146, 117.64, -615.93, 0.03875, 143, 401.16, -513.74, 0.03585, 142, 463.3, -430.88, 0.15976, 140, 633.17, -354.93, 0.05002, 139, 739.5, -325.92, 0.00329, 132, 967.92, 195.06, 2e-05, 130, 780.78, 353.91, 0.00086, 129, 641.52, 417.01, 0.03133, 128, 552.3, 495.35, 0.00019, 127, 238.96, 488.82, 0.12636, 15, 158, 100.18, 380.51, 0.23873, 157, -229.69, 346.01, 0.03239, 156, -311.62, 203.72, 0.0619, 155, -320.24, -22.66, 0.24354, 154, -320.24, -249.04, 0.113, 150, -116.58, -595.71, 0.08694, 148, 38.09, -668.9, 0.00331, 147, 170.67, -668.9, 0.04519, 146, 326.72, -688.23, 0.01491, 143, 610.24, -586.04, 0.01487, 142, 672.38, -503.18, 0.0657, 140, 842.24, -427.22, 0.01718, 139, 948.58, -398.22, 0.00014, 129, 850.6, 344.72, 0.00997, 127, 448.04, 416.52, 0.05221], "hull": 40, "edges": [28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 0, 78, 66, 68, 68, 70, 70, 72, 72, 74, 24, 26, 26, 28, 20, 22, 22, 24, 16, 18, 18, 20, 12, 14, 14, 16, 8, 10, 10, 12, 4, 6, 6, 8, 0, 2, 2, 4, 74, 76, 76, 78, 52, 54, 64, 66, 54, 56, 56, 58, 62, 64, 58, 60, 60, 62], "width": 400, "height": 314}}, "mingzulight": {"mingzulight": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [666.98, 2.69, -1231.97, 2.69, -1231.97, 1258.04, 666.98, 1258.04], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 298, "height": 197}}, "wu2": {"wu": {"type": "mesh", "uvs": [0.46923, 0.12029, 0.46616, 0.45751, 0.62782, 0.68159, 0.74446, 0.5206, 0.73525, 0.20514, 0.61554, 0.01586, 0.52448, 0.03762], "triangles": [4, 1, 5, 2, 4, 3, 1, 6, 5, 1, 0, 6, 1, 4, 2], "vertices": [-731.28, 991.4, -742.02, 436.34, -176.22, 67.5, 232.01, 332.49, 199.78, 851.74, -219.19, 1163.28, -537.9, 1127.47], "hull": 7, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 0, 12], "width": 3500, "height": 1646}}, "ming": {"ming": {"type": "mesh", "uvs": [0.63755, 0.65286, 0.72768, 0.39355, 1, 0.32966, 1, 0.61716, 0.85671, 0.7487, 0.64903, 0.75433], "triangles": [4, 1, 2, 3, 4, 2, 0, 1, 4, 5, 0, 4], "vertices": [-802.46, 755.48, -486.97, 1182.31, 466.13, 1287.48, 466.13, 814.25, -35.4, 597.74, -762.25, 588.46], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 3500, "height": 1646}}, "wuzuyun": {"wuzuyun": {"type": "mesh", "uvs": [0.11723, 0.65958, 0.15349, 0.72166, 0.22685, 0.79134, 0.26401, 0.83285, 0.31086, 0.88519, 0.36079, 0.9292, 0.41735, 0.97904, 0.46366, 0.98733, 0.53448, 1, 0.63308, 0.98774, 0.71623, 0.9774, 0.77751, 0.96113, 0.84637, 0.94285, 0.93168, 0.89692, 0.99163, 0.86465, 0.99513, 0.81675, 1, 0.75007, 0.9884, 0.67428, 0.97802, 0.6064, 0.91148, 0.5758, 0.85544, 0.55002, 0.79914, 0.51487, 0.73893, 0.47728, 0.68162, 0.42218, 0.64057, 0.38271, 0.68995, 0.33396, 0.72531, 0.29905, 0.72882, 0.23823, 0.73287, 0.16811, 0.69883, 0.09752, 0.67235, 0.04262, 0.59002, 0.01795, 0.5301, 0, 0.44577, 0.00867, 0.34549, 0.01898, 0.30487, 0.06431, 0.26075, 0.11355, 0.24328, 0.1589, 0.22292, 0.21176, 0.16716, 0.27546, 0.11428, 0.33586, 0.08158, 0.40586, 0.05302, 0.467, 0.06921, 0.5398, 0.08269, 0.60044, 0.50125, 0.139, 0.408, 0.22944, 0.35074, 0.35528, 0.35401, 0.50077, 0.42436, 0.61677, 0.52415, 0.7603, 0.68774, 0.81535, 0.84152, 0.7367, 0.68284, 0.6384, 0.53887, 0.56565, 0.48652, 0.42606, 0.52251, 0.29236, 0.20023, 0.42999, 0.22968, 0.58335, 0.32129, 0.71114, 0.47507, 0.89989], "triangles": [60, 50, 9, 8, 7, 60, 9, 8, 60, 6, 5, 60, 7, 6, 60, 1, 0, 58, 0, 44, 58, 44, 43, 58, 57, 43, 42, 57, 42, 41, 40, 39, 57, 41, 40, 57, 46, 38, 37, 47, 39, 38, 35, 34, 46, 36, 35, 46, 37, 36, 46, 45, 33, 32, 46, 34, 33, 31, 45, 32, 31, 29, 45, 30, 29, 31, 27, 29, 28, 26, 25, 27, 54, 24, 23, 53, 22, 21, 20, 53, 21, 52, 53, 20, 52, 20, 19, 19, 17, 52, 17, 19, 18, 17, 16, 52, 52, 16, 15, 14, 13, 15, 52, 15, 13, 12, 52, 13, 12, 11, 52, 10, 51, 11, 46, 33, 45, 47, 38, 46, 56, 47, 46, 47, 57, 39, 48, 47, 55, 49, 48, 55, 59, 48, 49, 57, 47, 48, 50, 49, 54, 51, 53, 52, 11, 51, 52, 9, 50, 51, 9, 51, 10, 53, 23, 22, 54, 23, 53, 50, 54, 53, 50, 53, 51, 54, 55, 24, 54, 49, 55, 56, 45, 29, 46, 45, 56, 27, 56, 29, 27, 25, 56, 24, 56, 25, 55, 56, 24, 55, 47, 56, 58, 57, 48, 43, 57, 58, 59, 58, 48, 2, 1, 58, 59, 2, 58, 3, 2, 59, 4, 3, 59, 50, 59, 49, 60, 59, 50, 5, 4, 59, 60, 5, 59], "vertices": [2, 80, 22.69, -49.55, 0.52602, 79, -60.83, 59.49, 0.47398, 2, 80, 84.43, -137.51, 0.35903, 79, 0.91, -28.47, 0.64097, 2, 77, -66.72, 28.23, 0.72393, 76, -115.32, 73.51, 0.27607, 3, 76, -52.03, 14.68, 0.49049, 75, -140.19, 94.72, 0.41025, 74, -219.07, 168.96, 0.09926, 4, 76, 27.74, -59.48, 0.32528, 75, -60.42, 20.56, 0.40547, 74, -139.3, 94.8, 0.19579, 73, -247.18, 128.7, 0.07347, 4, 75, 24.62, -41.79, 0.40223, 74, -54.26, 32.45, 0.31453, 73, -162.14, 66.34, 0.27548, 72, -280.46, 93.93, 0.00775, 5, 76, 209.09, -192.47, 0.05884, 75, 120.93, -112.43, 0.18944, 74, 42.05, -38.19, 0.25908, 73, -65.83, -4.29, 0.37194, 72, -184.15, 23.29, 0.1207, 4, 75, 199.81, -124.17, 0.07705, 74, 120.93, -49.93, 0.17897, 73, 13.05, -16.03, 0.47848, 72, -105.27, 11.55, 0.26549, 3, 74, 241.54, -67.88, 0.08815, 73, 133.66, -33.99, 0.40065, 72, 15.34, -6.4, 0.5112, 2, 73, 301.57, -16.61, 0.32131, 72, 183.25, 10.97, 0.67869, 2, 106, 5.06, -13.46, 0.75954, 72, 324.85, 25.62, 0.24046, 2, 106, 109.43, 9.59, 0.39889, 105, 3.14, -11, 0.60111, 2, 105, 120.39, 14.9, 0.41097, 104, -0.95, -11.04, 0.58903, 2, 103, 34.16, 1.06, 0.44378, 102, -64.46, -39.27, 0.55622, 2, 102, 37.64, 6.47, 0.57545, 101, 8.64, -84.01, 0.42455, 2, 102, 43.6, 74.34, 0.40966, 101, 14.6, -16.14, 0.59034, 2, 101, 22.89, 78.34, 0.48807, 100, 21.73, -20.26, 0.51193, 2, 99, 22.86, 2.46, 0.55392, 98, 51.86, -78.74, 0.44608, 2, 99, 5.17, 98.64, 0.34942, 98, 34.17, 17.44, 0.65058, 2, 98, -79.13, 60.8, 0.48121, 97, 84.43, -6.48, 0.51879, 2, 97, -11.01, 30.05, 0.61192, 96, 99.19, -32.59, 0.38808, 2, 97, -106.89, 79.86, 0.41464, 96, 3.31, 17.22, 0.58536, 2, 96, -99.24, 70.49, 0.42994, 95, 16.76, 15.97, 0.57006, 2, 94, 8.49, 19.8, 0.59084, 93, 78.09, -54.44, 0.40916, 2, 94, -61.42, 75.73, 0.40757, 93, 8.18, 1.49, 0.59243, 2, 93, 92.27, 70.57, 0.39535, 91, -23.73, -38.47, 0.60465, 2, 91, 36.49, 10.99, 0.56641, 90, 6.33, -71.37, 0.43359, 2, 90, 12.31, 14.82, 0.55015, 89, 1.87, -83.78, 0.44985, 2, 89, 8.77, 15.58, 0.62258, 88, 118.97, -146.82, 0.37742, 2, 89, -49.21, 115.6, 0.40792, 88, 60.99, -46.8, 0.59208, 2, 89, -94.31, 193.39, 0.23051, 88, 15.89, 30.99, 0.76949, 2, 88, -124.31, 65.95, 0.64396, 87, 229.8, 11.43, 0.35604, 2, 88, -226.35, 91.39, 0.38876, 87, 127.76, 36.87, 0.61124, 2, 87, -15.85, 24.58, 0.73308, 86, 282.27, 152.18, 0.26692, 2, 87, -186.63, 9.97, 0.50012, 86, 111.49, 137.57, 0.49988, 2, 87, -255.81, -54.26, 0.29149, 86, 42.31, 73.34, 0.70851, 2, 86, -32.82, 3.57, 0.73582, 85, 50.7, 148.57, 0.26418, 2, 86, -62.57, -60.69, 0.54281, 85, 20.95, 84.31, 0.45719, 2, 85, -13.73, 9.41, 0.66139, 84, 70.95, 81.33, 0.33861, 2, 85, -108.69, -80.85, 0.44513, 84, -24.01, -8.93, 0.55487, 2, 83, -7.87, 36.8, 0.6053, 82, 47.81, 143.52, 0.3947, 2, 83, -63.55, -62.39, 0.41969, 82, -7.87, 44.33, 0.58031, 2, 82, -56.51, -42.3, 0.53518, 81, -51.87, 99.22, 0.46482, 2, 82, -28.94, -145.46, 0.34662, 81, -24.3, -3.94, 0.65338, 2, 81, -1.34, -89.86, 0.49775, 80, -36.14, 34.26, 0.50225, 1, 108, 3.35, 19.22, 1, 1, 109, -12.67, 29.82, 1, 2, 110, -13.65, 48.6, 0.84803, 109, -110.18, -148.48, 0.15197, 1, 111, -14.11, 1.3, 1, 2, 112, -43.13, 48.08, 0.77913, 111, 105.69, -163.07, 0.22087, 2, 113, -30.04, 31.73, 0.85145, 112, 126.82, -155.29, 0.14855, 2, 114, 1.21, -16.11, 0.99961, 72, 276.34, 255.25, 0.00039, 2, 114, 263.09, 95.33, 0.46242, 97, -34.72, -234.48, 0.53758, 1, 115, -1.12, -2.67, 1, 2, 116, 39.28, -18.24, 0.92095, 115, -246.29, 100.41, 0.07905, 2, 117, 28.55, -23.54, 0.88173, 116, -49.88, 179.57, 0.11827, 1, 118, 13.43, -23.13, 1, 2, 119, -58.81, 220.25, 0.49277, 83, 138.52, -96.58, 0.50723, 1, 119, -8.66, 2.94, 1, 2, 120, 2.56, -25.31, 0.98785, 75, -42.65, 267.19, 0.01215, 2, 73, 32.48, 107.87, 0.41193, 72, -85.84, 135.45, 0.58807], "hull": 45, "edges": [2, 4, 64, 66, 66, 68, 60, 62, 62, 64, 56, 58, 58, 60, 52, 54, 54, 56, 48, 50, 50, 52, 44, 46, 46, 48, 40, 42, 42, 44, 36, 38, 38, 40, 32, 34, 34, 36, 28, 30, 30, 32, 24, 26, 26, 28, 20, 22, 22, 24, 16, 18, 18, 20, 12, 14, 14, 16, 8, 10, 10, 12, 4, 6, 6, 8, 2, 0, 0, 88, 84, 86, 86, 88, 80, 82, 82, 84, 76, 78, 78, 80, 72, 74, 74, 76, 68, 70, 70, 72], "width": 290, "height": 242}}, "wuzulight": {"wuzulight": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [312.51, 24.73, -1030.63, 24.73, -1030.63, 1114.55, 312.51, 1114.55], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 281, "height": 228}}, "yao": {"yao": {"type": "mesh", "uvs": [0.16384, 0.5044, 0.33557, 0.03563, 0.40634, 0.00109, 0.47944, 0.07757, 0.55602, 0.58582, 0.4632, 0.9855, 0.2158, 0.8366], "triangles": [0, 4, 6, 1, 2, 3, 1, 4, 0, 3, 4, 1, 5, 6, 4], "vertices": [-1049.03, 1063.17, -448.01, 1834.76, -200.28, 1891.61, 55.56, 1765.72, 323.58, 929.15, -1.3, 271.27, -867.17, 516.37], "hull": 7, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 0, 12], "width": 3500, "height": 1646}}, "yaozuyun": {"yaozuyun": {"type": "mesh", "uvs": [0.42661, 0.00834, 0.45377, 0.00834, 0.49146, 0.00834, 0.52834, 0.01655, 0.56518, 0.02474, 0.58961, 0.04727, 0.61774, 0.07323, 0.63417, 0.1109, 0.64573, 0.13743, 0.65611, 0.17954, 0.65746, 0.21868, 0.6379, 0.23825, 0.67095, 0.25917, 0.69186, 0.30101, 0.69186, 0.33475, 0.6696, 0.34824, 0.71007, 0.38671, 0.71951, 0.4191, 0.77437, 0.42262, 0.81433, 0.4403, 0.83431, 0.47567, 0.87735, 0.48336, 0.91654, 0.52257, 0.93652, 0.57332, 0.93114, 0.60023, 0.96956, 0.61254, 0.99262, 0.6679, 0.998, 0.73863, 1, 0.82014, 0.97571, 0.88857, 0.91347, 0.91394, 0.84569, 0.92174, 0.77294, 0.91507, 0.69953, 0.92508, 0.66549, 0.93911, 0.62211, 0.97717, 0.56205, 1, 0.50139, 1, 0.43154, 0.99423, 0.35258, 0.98132, 0.30855, 0.96384, 0.27514, 0.92054, 0.24857, 0.87724, 0.23794, 0.8461, 0.25995, 0.81115, 0.25312, 0.76709, 0.25526, 0.71859, 0.27693, 0.67609, 0.26306, 0.62318, 0.19025, 0.59543, 0.12437, 0.56941, 0.07843, 0.52431, 0.05762, 0.44885, 0.06109, 0.38033, 0.1092, 0.30995, 0.15112, 0.26014, 0.11531, 0.22256, 0.14501, 0.17887, 0.17133, 0.16822, 0.19687, 0.11624, 0.24529, 0.06779, 0.299, 0.0352, 0.36063, 0.0167, 0.42308, 0.04909, 0.42668, 0.11873, 0.43268, 0.19918, 0.44228, 0.28322, 0.45308, 0.36967, 0.47228, 0.45251, 0.49988, 0.54857, 0.53348, 0.63501, 0.57068, 0.74547, 0.60427, 0.84633, 0.30788, 0.11993, 0.30668, 0.20038, 0.31508, 0.27722, 0.31508, 0.38768, 0.34988, 0.48613, 0.38108, 0.58459, 0.40748, 0.67704, 0.43028, 0.7875, 0.45668, 0.86314, 0.34333, 0.8885, 0.34333, 0.80823, 0.33691, 0.71192, 0.25188, 0.52572, 0.1861, 0.44706, 0.18129, 0.34754, 0.23423, 0.26888, 0.2198, 0.18059, 0.53906, 0.09391, 0.55029, 0.16294, 0.53264, 0.24641, 0.57756, 0.31383, 0.58398, 0.42619, 0.63532, 0.52251, 0.68185, 0.59635, 0.69949, 0.68784, 0.73479, 0.82429, 0.85512, 0.78576, 0.83586, 0.67821, 0.76848, 0.53535], "triangles": [72, 36, 81, 81, 38, 82, 81, 83, 80, 81, 71, 72, 81, 80, 71, 85, 77, 78, 77, 85, 76, 85, 86, 76, 86, 87, 76, 76, 88, 75, 93, 66, 92, 69, 94, 95, 70, 95, 96, 96, 95, 101, 71, 70, 97, 70, 96, 97, 97, 96, 100, 96, 101, 100, 71, 97, 98, 98, 97, 100, 72, 71, 98, 71, 80, 79, 71, 79, 70, 79, 69, 70, 79, 78, 69, 70, 69, 95, 78, 68, 69, 78, 77, 68, 69, 68, 94, 77, 67, 68, 68, 67, 94, 77, 76, 67, 67, 93, 94, 76, 66, 67, 93, 67, 66, 76, 75, 66, 75, 65, 66, 66, 65, 92, 91, 92, 65, 75, 74, 65, 64, 74, 73, 74, 64, 65, 2, 65, 64, 40, 82, 39, 38, 39, 82, 38, 81, 37, 36, 37, 81, 35, 36, 72, 35, 72, 34, 34, 72, 33, 33, 72, 98, 33, 98, 32, 31, 32, 99, 32, 98, 99, 30, 31, 99, 30, 99, 29, 99, 28, 29, 28, 99, 27, 98, 100, 99, 99, 100, 27, 26, 27, 100, 25, 26, 24, 24, 26, 100, 24, 100, 101, 24, 101, 22, 24, 22, 23, 21, 101, 20, 101, 21, 22, 101, 19, 20, 101, 18, 19, 101, 17, 18, 95, 17, 101, 94, 17, 95, 17, 94, 16, 16, 94, 15, 14, 15, 13, 94, 93, 15, 15, 93, 12, 15, 12, 13, 12, 93, 11, 93, 92, 11, 92, 91, 11, 11, 9, 10, 11, 91, 9, 91, 8, 9, 91, 7, 8, 2, 90, 65, 91, 65, 90, 3, 90, 2, 7, 90, 6, 6, 90, 5, 90, 7, 91, 90, 4, 5, 90, 3, 4, 1, 2, 63, 73, 63, 64, 73, 62, 63, 2, 64, 63, 1, 63, 0, 63, 62, 0, 73, 59, 60, 60, 61, 73, 73, 61, 62, 74, 89, 73, 58, 59, 89, 73, 89, 59, 55, 89, 88, 88, 89, 74, 55, 58, 89, 88, 74, 75, 87, 88, 76, 87, 55, 88, 56, 57, 55, 55, 57, 58, 53, 54, 87, 54, 55, 87, 86, 53, 87, 53, 86, 52, 51, 52, 86, 50, 86, 85, 50, 51, 86, 49, 85, 48, 49, 50, 85, 48, 85, 78, 84, 47, 78, 47, 48, 78, 84, 79, 80, 79, 84, 78, 44, 45, 83, 83, 45, 84, 45, 46, 84, 46, 47, 84, 44, 83, 82, 81, 82, 83, 83, 84, 80, 42, 44, 82, 42, 43, 44, 40, 41, 82, 41, 42, 82], "vertices": [3, 20, 106.92, 76.18, 0.06412, 21, 0.55, 59.56, 0.86106, 22, -104.58, 98.02, 0.07482, 3, 21, 50.53, 59.56, 0.62595, 22, -54.6, 98.02, 0.37137, 23, -134.09, 166.47, 0.00268, 3, 21, 119.87, 59.56, 0.19047, 22, 14.74, 98.02, 0.70251, 23, -64.75, 166.47, 0.10702, 5, 21, 187.74, 44.48, 0.01843, 22, 82.61, 82.94, 0.56548, 23, 3.12, 151.39, 0.399, 24, -18.96, 208.8, 0.01598, 26, -71.95, 345.69, 0.00111, 5, 22, 150.39, 67.88, 0.30427, 23, 70.9, 136.33, 0.58919, 24, 48.82, 193.74, 0.09116, 26, -4.17, 330.63, 0.01322, 27, -48.33, 401.29, 0.00217, 5, 22, 195.33, 26.44, 0.13936, 23, 115.84, 94.89, 0.58257, 24, 93.76, 152.3, 0.21979, 26, 40.77, 289.19, 0.04573, 27, -3.39, 359.85, 0.01255, 5, 22, 247.1, -21.3, 0.03521, 23, 167.61, 47.15, 0.43055, 24, 145.53, 104.56, 0.3744, 26, 92.54, 241.46, 0.11779, 27, 48.38, 312.11, 0.04204, 5, 22, 277.32, -90.58, 0.00276, 23, 197.83, -22.13, 0.23355, 24, 175.75, 35.28, 0.41773, 26, 122.76, 172.18, 0.23179, 27, 78.6, 242.83, 0.11417, 4, 23, 219.11, -70.91, 0.12567, 24, 197.03, -13.5, 0.35864, 26, 144.04, 123.39, 0.31149, 27, 99.88, 194.05, 0.20419, 6, 23, 238.21, -148.35, 0.03825, 24, 216.13, -90.94, 0.21554, 26, 163.14, 45.95, 0.34573, 27, 118.98, 116.61, 0.39897, 28, 118.98, 191.68, 0.00148, 30, 81.44, 348.45, 3e-05, 7, 23, 240.69, -220.33, 0.01119, 24, 218.61, -162.92, 0.11383, 26, 165.62, -26.03, 0.25609, 27, 121.46, 44.63, 0.57428, 28, 121.46, 119.7, 0.03978, 30, 83.93, 276.47, 0.00464, 31, -13.23, 375.83, 0.0002, 8, 23, 204.71, -256.32, 0.00254, 24, 182.63, -198.91, 0.04384, 26, 129.63, -62.01, 0.10704, 27, 85.47, 8.64, 0.63095, 28, 85.47, 83.71, 0.18181, 29, 89.89, 169.83, 0.00305, 30, 47.94, 240.48, 0.02751, 31, -49.21, 339.84, 0.00326, 7, 24, 243.43, -237.38, 0.00244, 26, 190.44, -100.49, 0.00199, 27, 146.28, -29.83, 0.35835, 28, 146.28, 45.24, 0.44001, 29, 150.7, 131.35, 0.03636, 30, 108.75, 202.01, 0.1347, 31, 11.59, 301.37, 0.02615, 5, 27, 184.75, -106.77, 0.16818, 28, 184.75, -31.7, 0.40527, 29, 189.17, 54.41, 0.06846, 30, 147.22, 125.07, 0.27478, 31, 50.07, 224.43, 0.08331, 6, 27, 184.75, -168.82, 0.09953, 28, 184.75, -93.75, 0.32561, 29, 189.17, -7.64, 0.07259, 30, 147.22, 63.02, 0.35499, 31, 50.07, 162.38, 0.14719, 32, -31.63, 204.33, 9e-05, 6, 27, 143.8, -193.64, 0.0458, 28, 143.8, -118.57, 0.19765, 29, 148.22, -32.46, 0.06008, 30, 106.26, 38.2, 0.4207, 31, 9.11, 137.56, 0.27013, 32, -72.58, 179.51, 0.00564, 7, 27, 218.26, -264.38, 0.0011, 28, 218.26, -189.31, 0.0182, 29, 222.68, -103.19, 1e-05, 30, 180.72, -32.54, 0.1101, 31, 83.57, 66.82, 0.60803, 32, 1.88, 108.77, 0.26235, 33, -104.11, 179.43, 0.00021, 5, 28, 235.63, -248.87, 0.00101, 30, 198.1, -92.11, 0.00452, 31, 100.95, 7.25, 0.24549, 32, 19.25, 49.21, 0.66658, 33, -86.73, 119.86, 0.0824, 4, 31, 201.89, 0.78, 0.00023, 32, 120.19, 42.73, 0.3267, 33, 14.21, 113.39, 0.64527, 34, -78.53, 186.25, 0.02781, 3, 32, 193.72, 10.21, 0.07699, 33, 87.74, 80.87, 0.70939, 34, -5, 153.73, 0.21362, 4, 32, 230.49, -54.83, 0.0042, 33, 124.5, 15.82, 0.32154, 34, 31.77, 88.69, 0.61522, 35, -60.97, 183.63, 0.05903, 3, 33, 203.69, 1.68, 0.03003, 34, 110.95, 74.55, 0.60867, 35, 18.21, 169.49, 0.3613, 3, 34, 183.06, 2.43, 0.25171, 35, 90.33, 97.38, 0.69238, 36, 4.22, 234.27, 0.05591, 3, 34, 219.83, -90.89, 0.02222, 35, 127.09, 4.05, 0.64973, 36, 40.98, 140.95, 0.32805, 4, 35, 117.19, -45.44, 0.3786, 36, 31.08, 91.46, 0.61118, 37, 33.29, 164.32, 0.00833, 39, 4.59, 345.38, 0.00189, 4, 35, 187.89, -68.06, 0.07271, 36, 101.78, 68.84, 0.78379, 37, 103.99, 141.7, 0.11591, 39, 75.29, 322.76, 0.02759, 3, 36, 144.2, -32.97, 0.41905, 37, 146.41, 39.89, 0.41559, 39, 117.71, 220.95, 0.16537, 5, 36, 154.1, -163.06, 0.04714, 37, 156.31, -90.2, 0.35559, 38, 193.84, 29.04, 0.00557, 39, 127.6, 90.86, 0.57785, 40, 145.27, 172.56, 0.01386, 4, 37, 159.99, -240.08, 0.03029, 39, 131.29, -59.02, 0.55341, 40, 148.95, 22.67, 0.416, 41, 243.9, 80.08, 0.0003, 3, 39, 86.6, -184.87, 0.09396, 40, 104.26, -103.17, 0.78208, 41, 199.21, -45.77, 0.12396, 3, 40, -10.27, -149.84, 0.43986, 41, 84.67, -92.43, 0.55539, 42, 245.86, -48.27, 0.00475, 3, 40, -134.98, -164.18, 0.01208, 41, -40.04, -106.77, 0.67574, 42, 121.15, -62.61, 0.31218, 3, 41, -173.89, -94.49, 0.01093, 42, -12.71, -50.33, 0.87389, 43, 139.65, -52.54, 0.11518, 4, 42, -147.79, -68.75, 0.06986, 43, 4.57, -70.96, 0.73912, 44, 126.01, -66.55, 0.04263, 45, 143.67, 17.36, 0.14839, 3, 43, -58.06, -96.75, 0.31076, 44, 63.38, -92.33, 0.11749, 45, 81.04, -8.43, 0.57175, 3, 43, -137.88, -166.75, 0.00147, 45, 1.22, -78.43, 0.454, 46, 63.05, -21.02, 0.54453, 2, 46, -47.47, -63.01, 0.42566, 47, 51.89, -36.51, 0.57434, 3, 47, -59.72, -36.51, 0.61034, 48, 81.59, -47.55, 0.38763, 68, 62.64, -247.41, 0.00203, 2, 48, -46.93, -36.94, 0.70435, 49, 92.17, -59.02, 0.29565, 3, 3, 80.39, -107.62, 0.00087, 49, -53.11, -35.27, 0.49271, 50, 23.78, -61.51, 0.50642, 2, 3, -0.63, -75.49, 0.34609, 50, -57.24, -29.37, 0.65391, 3, 3, -62.1, 4.14, 0.77255, 4, -35.51, -122.18, 0.22439, 50, -118.71, 50.25, 0.00306, 3, 3, -111, 83.77, 0.22856, 4, -84.4, -42.55, 0.76648, 6, -144.24, -215.39, 0.00497, 3, 3, -130.55, 141.04, 0.05883, 4, -103.96, 14.73, 0.88275, 6, -163.79, -158.12, 0.05842, 4, 4, -63.45, 78.99, 0.61402, 5, -149.87, 15.84, 0.04183, 6, -123.28, -93.86, 0.33778, 8, -106.66, -313.24, 0.00638, 5, 4, -76.02, 160.02, 0.21004, 5, -162.45, 96.86, 0.01217, 6, -135.85, -12.83, 0.71383, 7, -185.71, -132.49, 0.00861, 8, -119.23, -232.21, 0.05535, 5, 4, -72.08, 249.22, 0.04858, 6, -131.91, 76.38, 0.66995, 7, -181.77, -43.29, 0.08473, 8, -115.29, -143.01, 0.19621, 9, -8.92, -222.78, 0.00053, 5, 4, -32.21, 327.38, 0.00333, 6, -92.04, 154.53, 0.28203, 7, -141.9, 34.87, 0.12168, 8, -75.42, -64.85, 0.53065, 9, 30.95, -144.63, 0.0623, 4, 6, -117.56, 251.83, 0.01084, 8, -100.94, 32.44, 0.29885, 9, 5.43, -47.33, 0.68511, 10, 121.77, -90.55, 0.0052, 3, 9, -128.55, 3.71, 0.04857, 10, -12.21, -39.51, 0.90657, 11, 84.19, -92.69, 0.04486, 3, 10, -133.43, 8.34, 0.043, 11, -37.03, -44.84, 0.9092, 12, 52.71, -157.86, 0.0478, 2, 11, -121.57, 38.1, 0.29401, 12, -31.82, -74.92, 0.70599, 3, 12, -70.1, 63.85, 0.49966, 13, -100.02, -55.82, 0.4938, 14, -153.2, -165.51, 0.00653, 3, 12, -63.72, 189.85, 0.01593, 13, -93.64, 70.19, 0.68251, 14, -146.82, -39.5, 0.30156, 6, 13, -5.12, 199.63, 0.02587, 14, -58.3, 89.93, 0.74176, 15, -167.99, 10.16, 0.21528, 16, -247.77, -39.7, 0.0005, 17, -290.98, -146.07, 0.01507, 18, -340.84, -269.06, 0.00153, 6, 14, 18.84, 181.53, 0.1411, 15, -90.86, 101.76, 0.49666, 16, -170.63, 51.9, 0.0893, 17, -213.84, -54.47, 0.21792, 18, -263.7, -177.46, 0.05492, 19, -293.62, -293.8, 9e-05, 6, 14, -47.05, 250.63, 0.0316, 15, -156.74, 170.86, 0.40353, 16, -236.52, 121, 0.08552, 17, -279.73, 14.63, 0.35367, 18, -329.59, -108.36, 0.12225, 19, -359.51, -224.7, 0.00343, 6, 14, 7.59, 330.98, 0.01047, 15, -102.1, 251.21, 0.31327, 16, -181.88, 201.35, 0.07292, 17, -225.09, 94.98, 0.3912, 18, -274.95, -28.01, 0.18866, 19, -304.87, -144.35, 0.02348, 6, 14, 56.03, 350.57, 0.00294, 15, -53.67, 270.8, 0.2203, 16, -133.44, 220.94, 0.05297, 17, -176.65, 114.57, 0.38709, 18, -226.51, -8.42, 0.27273, 19, -256.43, -124.76, 0.06397, 5, 15, -6.69, 366.38, 0.06867, 16, -86.46, 316.52, 0.00547, 17, -129.67, 210.15, 0.21377, 18, -179.53, 87.16, 0.42367, 19, -209.45, -29.18, 0.28842, 5, 15, 82.41, 455.48, 0.01054, 17, -40.57, 299.25, 0.04018, 18, -90.43, 176.26, 0.21338, 19, -120.35, 59.92, 0.70755, 20, -226.72, -33.15, 0.02834, 5, 15, 181.23, 515.42, 2e-05, 17, 58.25, 359.19, 9e-05, 18, 8.39, 236.2, 0.00189, 19, -21.53, 119.86, 0.6181, 20, -127.9, 26.79, 0.37989, 3, 19, 91.87, 153.88, 0.03518, 20, -14.5, 60.81, 0.89305, 21, -120.87, 44.19, 0.07177, 6, 19, 206.78, 94.31, 0.00026, 20, 100.41, 1.24, 0.05297, 21, -5.96, -15.38, 0.89102, 22, -111.09, 23.08, 0.00733, 25, -197.2, 230.64, 2e-05, 51, 13.49, 151.08, 0.0484, 8, 20, 107.03, -126.83, 0.01679, 21, 0.66, -143.45, 0.06198, 22, -104.46, -104.98, 0.06652, 23, -183.95, -36.53, 0.00913, 24, -206.03, 20.88, 0.00421, 25, -190.57, 102.57, 0.01891, 51, 20.12, 23.02, 0.82209, 64, -144.32, 263.88, 0.00037, 8, 22, -93.42, -252.92, 0.00039, 23, -172.91, -184.47, 0.00052, 24, -194.99, -127.06, 0.00145, 25, -179.53, -45.36, 0.08586, 51, 31.16, -124.92, 0.10226, 52, 31.16, 14.04, 0.73536, 53, 10.31, 157.63, 0.01853, 64, -133.28, 115.94, 0.05563, 6, 25, -161.87, -199.92, 0.00244, 29, -270.06, 87.12, 0.00672, 52, 48.82, -140.52, 0.02361, 53, 27.98, 3.07, 0.76945, 54, 2.5, 137.4, 0.04844, 64, -115.62, -38.62, 0.14934, 6, 29, -250.19, -71.86, 0.02967, 30, -292.14, -1.2, 0.0077, 54, 22.37, -21.58, 0.7752, 55, -5.42, 112.75, 0.16414, 63, -257.86, 200.76, 0.00722, 64, -95.74, -197.59, 0.01608, 7, 29, -214.86, -224.21, 0.00918, 30, -256.81, -153.56, 0.01182, 55, 29.91, -39.6, 0.66322, 56, -4.83, 99.36, 0.2571, 63, -222.53, 48.41, 0.0558, 64, -60.42, -349.94, 0.00069, 67, 333.31, 78.52, 0.0022, 7, 7, 268.31, 269.38, 0.00063, 8, 334.79, 169.66, 0.00282, 56, 45.95, -77.28, 0.37086, 57, 4.27, 68.63, 0.52486, 62, -241.23, 15.36, 0.03056, 63, -171.75, -128.23, 0.06801, 67, 384.09, -98.12, 0.00226, 9, 7, 330.14, 110.4, 0.00483, 8, 396.62, 10.68, 0.00032, 57, 66.09, -90.35, 0.38784, 58, 22.09, 94.93, 0.4563, 59, -12.65, 236.21, 0.00126, 60, -311.42, 187.57, 0.00096, 61, -283.63, 50.93, 0.04241, 62, -179.41, -143.62, 0.1026, 63, -109.93, -287.21, 0.00349, 7, 43, -232.53, 259.34, 0.00395, 44, -111.09, 263.76, 0.0516, 58, 90.53, -108.2, 0.14667, 59, 55.79, 33.07, 0.64327, 60, -242.97, -15.56, 0.09013, 61, -215.18, -152.21, 0.05749, 62, -110.96, -346.75, 0.00688, 8, 43, -170.7, 73.87, 0.02661, 44, -49.26, 78.28, 0.59846, 46, 30.23, 219.6, 0.00654, 47, 129.59, 246.09, 0.00801, 59, 117.62, -152.4, 0.21927, 60, -181.15, -201.04, 0.07464, 61, -153.35, -337.68, 0.00045, 68, 251.95, 35.2, 0.06601, 6, 15, 197.57, 359.59, 0.00041, 17, 74.58, 203.37, 0.00123, 18, 24.72, 80.38, 0.30232, 19, -5.19, -35.96, 0.66092, 51, -198.48, 20.81, 0.02816, 52, -198.48, 159.77, 0.00696, 7, 16, 115.59, 161.8, 0.00227, 17, 72.38, 55.43, 0.3213, 18, 22.52, -67.56, 0.50149, 51, -200.68, -127.13, 0.01579, 52, -200.68, 11.83, 0.10431, 53, -221.53, 155.42, 0.02615, 65, -20.04, 261.96, 0.02869, 7, 16, 131.04, 20.49, 0.19111, 17, 87.83, -85.88, 0.24485, 18, 37.97, -208.87, 0.02945, 52, -185.23, -129.48, 0.0874, 53, -206.07, 14.11, 0.11622, 54, -231.55, 148.44, 0.00198, 65, -4.58, 120.65, 0.32899, 7, 15, 210.82, -132.79, 0.00198, 54, -231.55, -54.7, 0.05002, 55, -259.34, 79.63, 0.04918, 56, -294.08, 218.59, 0.00264, 65, -4.58, -82.49, 0.49173, 66, 88.06, 44.89, 0.33635, 67, 44.06, 197.75, 0.06809, 10, 7, -7.69, 384.19, 0.00192, 8, 58.79, 284.47, 0.0512, 9, 165.16, 204.7, 0.01498, 54, -167.51, -235.75, 0.02086, 55, -195.31, -101.42, 0.15202, 56, -230.05, 37.54, 0.12663, 57, -271.73, 183.44, 0.01606, 65, 59.45, -263.54, 0.04792, 66, 152.09, -136.16, 0.05432, 67, 108.09, 16.69, 0.51409, 9, 7, 49.72, 203.14, 0.10999, 8, 116.2, 103.42, 0.3062, 9, 222.57, 23.64, 0.03968, 55, -137.9, -282.48, 0.0172, 56, -172.64, -143.52, 0.15575, 57, -214.33, 2.39, 0.18041, 58, -258.33, 187.67, 0.01723, 65, 116.86, -444.6, 0.00025, 67, 165.5, -164.36, 0.17331, 10, 5, 121.57, 262.48, 0.01932, 6, 148.16, 152.79, 0.00232, 7, 98.3, 33.12, 0.55007, 8, 164.78, -66.6, 0.07483, 56, -124.06, -313.54, 0.01136, 57, -165.75, -167.63, 0.12856, 58, -209.75, 17.65, 0.16657, 59, -244.49, 158.93, 0.02008, 67, 214.07, -334.38, 0.00881, 68, -110.16, 346.52, 0.01807, 9, 5, 163.52, 59.34, 0.25344, 6, 190.11, -50.35, 0.05762, 7, 140.25, -170.01, 0.15392, 49, 89.84, 321.16, 0.0017, 50, 166.74, 294.93, 0.00028, 57, -123.8, -370.76, 0.00121, 58, -167.8, -185.48, 0.08333, 59, -202.54, -44.21, 0.14162, 68, -68.21, 143.39, 0.30687, 9, 3, 271.92, 109.71, 0.0011, 5, 212.09, -79.76, 0.05511, 6, 238.68, -189.45, 0.00074, 7, 188.82, -309.12, 0.00505, 48, -0.69, 204.14, 0.00757, 49, 138.42, 182.06, 0.0167, 50, 215.32, 155.82, 0.00282, 58, -119.23, -324.59, 0.00217, 68, -19.64, 4.28, 0.90874, 7, 3, 63.37, 63.07, 0.30324, 4, 89.96, -63.24, 0.17067, 5, 3.54, -126.4, 0.27137, 48, -209.24, 157.5, 0.00205, 49, -70.14, 135.42, 0.06192, 50, 6.76, 109.19, 0.10979, 68, -228.19, -42.35, 0.08096, 6, 5, 3.54, 21.2, 0.83206, 6, 30.13, -88.49, 0.14075, 7, -19.73, -208.15, 0.00927, 58, -327.78, -223.62, 0.00265, 59, -362.52, -82.35, 0.00304, 68, -228.19, 105.25, 0.01222, 6, 5, -8.27, 198.32, 0.00231, 6, 18.32, 88.63, 0.32725, 7, -31.54, -31.03, 0.59951, 8, 34.94, -130.75, 0.06805, 59, -374.33, 94.77, 0.00097, 68, -240, 282.37, 0.00191, 11, 8, -121.51, 211.68, 0.00282, 9, -15.15, 131.9, 0.20027, 10, 101.19, 88.69, 0.21296, 11, 197.59, 35.51, 0.03176, 12, 287.34, -77.51, 0.00081, 13, 257.42, -197.17, 0.01022, 14, 204.24, -306.86, 5e-05, 56, -410.35, -35.26, 0.00055, 57, -452.04, 110.65, 0.00029, 66, -28.21, -208.96, 0.04219, 67, -72.22, -56.1, 0.49807, 8, 10, -19.84, 233.34, 0.04821, 11, 76.56, 180.16, 0.11546, 12, 166.31, 67.14, 0.07063, 13, 136.39, -52.52, 0.22938, 14, 83.21, -162.22, 0.07759, 15, -26.49, -241.99, 0.02852, 66, -149.25, -64.31, 0.32032, 67, -193.25, 88.55, 0.10989, 7, 10, -28.69, 416.36, 8e-05, 11, 67.7, 363.18, 0.00108, 13, 127.53, 130.5, 0.0088, 14, 74.35, 20.81, 0.39207, 15, -35.34, -58.97, 0.50944, 65, -250.74, -8.67, 0.00278, 66, -158.1, 118.71, 0.08574, 4, 15, 62.07, 85.68, 0.12974, 16, -17.7, 35.82, 0.62109, 17, -60.91, -70.55, 0.24326, 18, -110.77, -193.54, 0.00591, 6, 14, 145.2, 327.82, 9e-05, 15, 35.51, 248.04, 0.11988, 16, -44.27, 198.18, 0.03503, 17, -87.48, 91.81, 0.41736, 18, -137.34, -31.18, 0.36031, 19, -167.26, -147.52, 0.06733, 5, 22, 102.33, -59.34, 0.02311, 23, 22.84, 9.11, 0.83289, 24, 0.76, 66.52, 0.13174, 26, -52.23, 203.42, 0.00949, 27, -96.39, 274.07, 0.00278, 3, 24, 21.42, -60.41, 0.35012, 25, 36.88, 21.28, 0.43608, 26, -31.57, 76.48, 0.21379, 7, 25, 4.41, -132.22, 0.08879, 26, -64.04, -77.02, 0.16979, 27, -108.2, -6.37, 0.09028, 28, -108.2, 68.71, 0.05873, 29, -103.79, 154.82, 0.00808, 52, 215.1, -72.82, 0.00325, 64, 50.66, 29.09, 0.58108, 6, 28, -25.55, -55.28, 0.30039, 29, -21.13, 30.83, 0.61244, 54, 251.43, 81.12, 0.01536, 55, 223.64, 215.45, 0.00381, 63, -28.8, 303.45, 0.00098, 64, 133.32, -94.9, 0.06703, 8, 29, -9.32, -175.81, 0.05685, 30, -51.27, -105.15, 0.30616, 31, -148.43, -5.79, 0.0982, 54, 263.24, -125.52, 0.01658, 55, 235.45, 8.81, 0.07166, 56, 200.71, 147.77, 0.0168, 63, -16.99, 96.81, 0.42993, 64, 145.12, -301.54, 0.00382, 9, 31, -53.96, -182.91, 0.04031, 32, -135.66, -140.96, 0.06425, 33, -241.64, -70.3, 0.01734, 34, -334.38, 2.56, 0.00094, 56, 295.17, -29.35, 0.00138, 57, 253.49, 116.55, 0.00254, 61, -96.23, 257.83, 0.00368, 62, 7.99, 63.29, 0.61284, 63, 77.47, -80.31, 0.25671, 10, 32, -50.05, -276.75, 0.01399, 33, -156.03, -206.1, 0.05136, 34, -248.77, -133.23, 0.0389, 35, -341.51, -38.29, 0.00779, 37, -425.41, 171.47, 0.0002, 38, -387.87, 290.7, 0.00167, 57, 339.09, -19.24, 0.00104, 58, 295.09, 166.04, 0.00809, 61, -10.62, 122.04, 0.43526, 62, 93.6, -72.51, 0.44171, 9, 34, -216.3, -301.5, 0.00043, 35, -309.03, -206.55, 0.00476, 36, -395.15, -69.66, 0.00055, 37, -392.94, 3.21, 0.00188, 38, -355.4, 122.44, 0.01282, 58, 327.56, -2.22, 0.00381, 59, 292.82, 139.05, 0.00674, 60, -5.94, 90.42, 0.32507, 61, 21.85, -46.23, 0.64393, 8, 35, -244.09, -457.47, 6e-05, 38, -290.46, -128.48, 0.06772, 41, -244.09, 72.45, 0.01756, 42, -82.9, 116.61, 0.25317, 43, 69.45, 114.4, 0.27498, 44, 190.89, 118.82, 0.04271, 59, 357.77, -111.87, 0.00837, 60, 59, -160.5, 0.33545, 9, 35, -22.69, -386.62, 0.00216, 38, -69.06, -57.63, 0.6029, 39, -135.3, 4.19, 0.01632, 40, -117.63, 85.89, 0.04389, 41, -22.69, 143.3, 0.1797, 42, 138.5, 187.46, 0.06087, 43, 290.85, 185.25, 0.00092, 60, 280.4, -89.65, 0.08933, 61, 308.2, -226.3, 0.00392, 10, 34, 34.62, -283.78, 0.0155, 35, -58.11, -188.84, 0.14278, 36, -144.23, -51.94, 0.11238, 37, -142.02, 20.92, 0.17088, 38, -104.48, 140.15, 0.30565, 41, -58.11, 341.08, 0.00385, 42, 103.07, 385.24, 0.00698, 60, 244.98, 108.13, 0.12378, 61, 272.77, -28.51, 0.11758, 62, 376.99, -223.06, 0.00062, 9, 32, 109.36, -164.58, 0.00914, 33, 3.37, -93.92, 0.37294, 34, -89.36, -21.06, 0.38344, 35, -182.1, 73.89, 0.02778, 37, -266, 283.65, 0.00035, 38, -228.47, 402.88, 0.00412, 60, 121, 370.86, 0.00062, 61, 148.79, 234.21, 0.10847, 62, 253.01, 39.67, 0.09314], "hull": 63, "edges": [12, 14, 14, 16, 8, 10, 10, 12, 4, 6, 6, 8, 0, 2, 2, 4, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 0, 124], "width": 314, "height": 314}}, "yaozulight": {"yaozulight": {"x": -190.87, "y": 921.92, "scaleX": 6.3015, "scaleY": 6.3015, "width": 200, "height": 255}}, "shenzuyun": {"shenzuyun": {"type": "mesh", "uvs": [0.00134, 0.7325, 0.02744, 0.77748, 0.09191, 0.81924, 0.16309, 0.84334, 0.19667, 0.92526, 0.29336, 0.96702, 0.42767, 0.97344, 0.54451, 0.96862, 0.62644, 0.95738, 0.68783, 0.98482, 0.69068, 0.81658, 0.69326, 0.66459, 0.69606, 0.4999, 0.69762, 0.40804, 0.56783, 0.41284, 0.4025, 0.41894, 0.30337, 0.4226, 0.19852, 0.42648, 0.08549, 0.43065, 0.00193, 0.43374, 0.03953, 0.48996, 0.01401, 0.53815, 0.02207, 0.57991, 0.06505, 0.6024, 0.0355, 0.63131, 0, 0.6538, 0.63853, 0.89716, 0.51469, 0.92397, 0.41006, 0.93036, 0.2702, 0.9112, 0.21895, 0.85119, 0.15169, 0.78224, 0.06842, 0.72861, 0.08977, 0.65072, 0.10899, 0.56007, 0.06415, 0.51665, 0.07803, 0.45536, 0.20401, 0.47707, 0.39084, 0.47196, 0.56486, 0.47324, 0.63319, 0.69286, 0.52323, 0.77713, 0.37803, 0.77458, 0.21255, 0.67754, 0.21255, 0.5805, 0.40472, 0.59326, 0.55098, 0.61114], "triangles": [6, 28, 27, 5, 28, 6, 5, 29, 28, 29, 30, 42, 4, 30, 29, 4, 29, 5, 30, 3, 31, 43, 30, 31, 4, 3, 30, 2, 32, 31, 2, 31, 3, 1, 32, 2, 0, 25, 32, 1, 0, 32, 25, 24, 32, 33, 23, 34, 24, 23, 33, 32, 24, 33, 32, 33, 31, 34, 23, 35, 22, 21, 35, 23, 22, 35, 21, 20, 35, 36, 19, 18, 20, 19, 36, 35, 20, 36, 18, 37, 36, 34, 36, 37, 35, 36, 34, 37, 17, 16, 37, 18, 17, 44, 37, 16, 38, 16, 15, 38, 15, 39, 45, 38, 39, 44, 38, 45, 38, 44, 16, 39, 15, 14, 13, 39, 14, 12, 39, 13, 46, 39, 12, 40, 46, 11, 10, 40, 11, 41, 40, 10, 26, 41, 10, 27, 41, 26, 8, 7, 26, 9, 26, 10, 8, 26, 9, 27, 28, 41, 26, 7, 27, 6, 27, 7, 42, 30, 43, 41, 28, 42, 29, 42, 28, 31, 33, 43, 34, 37, 44, 44, 33, 34, 43, 33, 44, 45, 43, 44, 42, 43, 45, 42, 45, 41, 46, 45, 39, 11, 46, 12, 41, 45, 46, 41, 46, 40], "vertices": [3, 170, -72.91, -117.18, 0.06712, 169, -53.22, -18.73, 0.85897, 168, -104.24, 55.56, 0.07391, 2, 169, -0.35, -94.92, 0.24851, 168, -51.36, -20.63, 0.75149, 2, 168, 79.25, -91.38, 0.19896, 167, -40.68, -28.73, 0.80104, 4, 190, 20.32, -332.68, 0.00187, 167, 103.53, -69.54, 0.17917, 166, -47.11, -13.56, 0.79423, 165, -138.4, 125.16, 0.02473, 2, 166, 20.91, -152.34, 0.17743, 165, -70.38, -13.61, 0.82257, 2, 165, 125.53, -84.36, 0.09669, 164, -34.11, -40.05, 0.90331, 2, 163, 86.73, -40.2, 0.24395, 162, -34.09, -33.93, 0.75605, 2, 186, 81.55, -37.92, 0.37988, 185, -38.68, -55.09, 0.62012, 2, 185, 127.3, -36.05, 0.10316, 184, 16.24, -70.4, 0.89684, 2, 184, 140.61, -116.87, 0.96659, 183, 124.58, -242.82, 0.03341, 3, 184, 146.4, 168.12, 0.05875, 183, 130.36, 42.17, 0.70452, 182, 128.07, -185.69, 0.23673, 4, 183, 135.59, 299.63, 0.01896, 182, 133.3, 71.78, 0.52007, 181, 128.72, -99.97, 0.45831, 180, 119.56, -316.38, 0.00266, 2, 181, 134.39, 179.03, 0.23798, 180, 125.23, -37.38, 0.76202, 1, 180, 128.39, 118.23, 1, 3, 180, -134.57, 110.1, 0.38959, 179, 94.43, 106.67, 0.61019, 178, 340.6, 99.8, 0.00021, 3, 179, -240.52, 96.32, 0.0603, 178, 5.66, 89.45, 0.89906, 177, 267.16, 86.02, 0.04065, 4, 195, -88.77, 444.21, 0.00032, 178, -195.18, 83.25, 0.23016, 177, 66.33, 79.81, 0.76936, 176, 235.79, 79.81, 0.00016, 3, 177, -146.11, 73.25, 0.15276, 176, 23.35, 73.25, 0.83858, 175, 269.28, 70.08, 0.00866, 3, 176, -205.65, 66.18, 0.14644, 175, 40.29, 63.01, 0.83581, 172, 21.49, 256.33, 0.01776, 2, 175, -129.02, 57.78, 0.99893, 174, -92.32, 139.22, 0.00107, 2, 175, -52.83, -37.46, 0.44534, 174, -16.13, 43.99, 0.55466, 2, 174, -67.83, -37.64, 0.45059, 173, -40.09, 51.86, 0.54941, 2, 173, -23.76, -18.89, 0.99967, 171, -108.78, 61.66, 0.00033, 4, 173, 63.31, -56.98, 0.06727, 172, -19.92, -34.61, 0.35482, 171, -21.71, 23.57, 0.56448, 170, 56.15, 103.22, 0.01343, 3, 172, -79.78, -83.59, 0.00031, 171, -81.57, -25.41, 0.3205, 170, -3.71, 54.24, 0.6792, 3, 171, -153.5, -63.5, 0.00161, 170, -75.64, 16.15, 0.82739, 169, -55.95, 114.6, 0.171, 2, 184, 40.73, 31.62, 0.70675, 183, 24.7, -94.33, 0.29325, 7, 194, 45.41, -349.21, 0.00028, 188, 246.31, -202.1, 0.01371, 187, -55.98, -170.83, 0.10846, 186, 21.12, 37.72, 0.69622, 185, -99.11, 20.55, 0.18085, 183, -226.2, -139.75, 9e-05, 162, 142.2, 49.87, 0.0004, 7, 194, -166.56, -360.03, 0.00042, 189, 353.01, -287.58, 0.00012, 188, 34.34, -212.92, 0.0882, 187, -267.96, -181.64, 0.01351, 186, -190.86, 26.91, 0.00208, 163, 51.06, 32.79, 0.54142, 162, -69.77, 39.06, 0.35426, 6, 195, -155.98, -383.48, 0.00067, 189, 69.65, -255.14, 0.04677, 188, -249.02, -180.47, 0.03305, 166, 169.89, -128.53, 0.01442, 165, 78.6, 10.19, 0.49165, 164, -81.04, 54.5, 0.41345, 5, 189, -34.17, -153.47, 0.14052, 188, -352.84, -78.81, 0.01734, 166, 66.07, -26.87, 0.53902, 165, -25.22, 111.86, 0.28331, 164, -184.87, 156.16, 0.01981, 7, 190, -2.77, -229.18, 0.10536, 189, -170.44, -36.67, 0.12907, 171, 153.83, -281.09, 0.00673, 170, 231.7, -201.43, 0.0015, 168, 200.37, -28.7, 0.00952, 167, 80.44, 33.95, 0.45832, 166, -70.2, 89.93, 0.28949, 7, 190, -171.48, -138.34, 0.09102, 189, -339.15, 54.17, 0.00314, 171, -14.88, -190.24, 0.07733, 170, 62.98, -110.59, 0.08555, 169, 82.67, -12.14, 0.21907, 168, 31.66, 62.15, 0.42775, 167, -88.27, 124.8, 0.09613, 8, 190, -128.22, -6.39, 0.20467, 189, -295.89, 186.12, 0.00034, 172, 30.17, -116.47, 0.00634, 171, 28.38, -58.3, 0.58814, 170, 106.24, 21.36, 0.1425, 169, 125.93, 119.81, 0.00823, 168, 74.92, 194.09, 0.02729, 167, -45.01, 256.74, 0.02249, 7, 191, -219.7, -18.42, 0.07302, 190, -89.29, 147.18, 0.10568, 176, -158.04, -153.05, 0.08745, 175, 87.9, -156.22, 0.05627, 174, 124.59, -74.77, 0.07962, 172, 69.1, 37.1, 0.55183, 171, 67.31, 95.28, 0.04613, 6, 191, -310.55, 55.12, 0.00523, 190, -180.14, 220.72, 0.00225, 176, -248.88, -79.5, 0.02308, 175, -2.95, -82.68, 0.14191, 174, 33.75, -1.23, 0.6762, 172, -21.74, 110.64, 0.15133, 5, 191, -282.43, 158.95, 0.00088, 190, -152.02, 324.55, 0.00034, 176, -220.76, 24.32, 0.08059, 175, 25.17, 21.15, 0.90021, 172, 6.38, 214.47, 0.01798, 3, 191, -27.19, 122.17, 0.11652, 177, -134.99, -12.45, 0.13882, 176, 34.47, -12.45, 0.74467, 5, 195, 88.44, 360.6, 0.00239, 192, -60.6, 203.28, 0.00039, 191, 351.33, 130.83, 0.002, 178, -17.97, -0.36, 0.95273, 177, 243.54, -3.8, 0.04249, 5, 193, 24.94, 252.86, 0.04646, 192, 291.97, 201.11, 0.00014, 181, -131.41, 224.18, 0.02734, 180, -140.57, 7.78, 0.31947, 179, 88.43, 4.34, 0.60659, 3, 193, 163.37, -119.17, 0.01428, 182, 11.6, 23.9, 0.86237, 181, 7.02, -147.85, 0.12335, 8, 194, 62.71, -100.47, 0.34749, 193, -59.42, -261.93, 0.01144, 188, 263.62, 46.64, 0.0409, 187, -38.68, 77.92, 0.53573, 186, 38.42, 286.47, 0.00537, 183, -208.9, 108.99, 0.00694, 182, -211.19, -118.86, 0.04802, 162, 159.51, 298.62, 0.00411, 8, 195, 62.49, -152.03, 0.2005, 194, -231.45, -96.14, 0.04328, 192, -86.55, -309.35, 0.00638, 189, 288.12, -23.69, 0.0414, 188, -30.55, 50.97, 0.69639, 187, -332.85, 82.24, 0.00036, 165, 297.06, 241.64, 0.00236, 164, 137.42, 285.94, 0.00933, 8, 195, -272.78, 12.35, 0.08806, 192, -421.82, -144.97, 4e-05, 191, -9.89, -217.42, 0.15501, 190, 120.52, -51.82, 0.39697, 189, -47.15, 140.69, 0.34005, 177, -117.69, -352.04, 0.00043, 167, 203.73, 211.32, 0.01658, 166, 53.09, 267.3, 0.00286, 10, 195, -272.78, 176.74, 0.03685, 192, -421.82, 19.42, 0.00202, 191, -9.89, -53.03, 0.72308, 190, 120.52, 112.57, 0.17525, 189, -47.15, 305.08, 0.03639, 178, -379.19, -184.22, 0.00048, 176, 51.77, -187.65, 0.00831, 175, 297.71, -190.83, 0.00065, 174, 334.4, -109.38, 0.00032, 172, 278.91, 2.49, 0.01665, 7, 195, 116.56, 155.11, 0.09247, 194, -177.38, 211, 0.00057, 192, -32.48, -2.21, 0.83959, 191, 379.45, -74.66, 0.01331, 188, 23.52, 358.12, 0.00024, 178, 10.15, -205.85, 0.03845, 177, 271.65, -209.28, 0.01537, 5, 193, -3.18, 19.26, 0.91753, 192, 263.85, -32.49, 0.01819, 181, -159.53, -9.42, 0.0224, 180, -168.69, -225.83, 0.00701, 179, 60.31, -229.26, 0.03487], "hull": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 0, 50, 16, 18, 36, 38, 34, 36, 26, 28, 28, 30, 30, 32, 32, 34, 24, 26, 22, 24, 18, 20, 20, 22], "width": 345, "height": 289}}, "shenzulight": {"shenzulight": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [426.84, -58.04, -1030.18, -58.04, -1030.18, 1016.08, 426.84, 1016.08], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 293, "height": 216}}, "shen": {"shen": {"type": "mesh", "uvs": [0.66317, 0.23836, 0.71863, 0.42557, 0.83285, 0.50504, 1, 0.45912, 1, 0.25072, 1, 0, 0.85106, 0, 0.69297, 0.03526], "triangles": [6, 5, 4, 6, 0, 7, 2, 1, 6, 1, 0, 6, 4, 2, 6, 3, 2, 4], "vertices": [-892.56, 492.94, -697.79, 184.8, -296.62, 53.99, 290.39, 129.57, 290.39, 472.59, 290.39, 885.29, -232.67, 885.29, -787.91, 827.25], "hull": 8, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 0, 14], "width": 3512, "height": 1646}}}}], "events": {"lizi_appear": {}}, "animations": {"mingzu": {"slots": {"mingzuyun": {"attachment": [{"name": "<PERSON><PERSON><PERSON><PERSON>"}]}, "ming": {"attachment": [{"name": "ming"}]}}, "bones": {"bone2": {"translate": [{"x": 28.96, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.7667}, {"time": 2.1, "x": 48.34, "curve": 0.256, "c3": 0.62, "c4": 0.47}, {"time": 2.6667, "x": 28.96}]}, "bone53": {"translate": [{"x": 8.84, "curve": 0.313, "c2": 0.27, "c3": 0.66, "c4": 0.65}, {"time": 0.4, "x": 27.37, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 1, "x": 48.34, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": 8.84}]}, "bone54": {"translate": [{"x": 47.18, "curve": 0.28, "c2": 0.13, "c3": 0.634, "c4": 0.54}, {"time": 0.4667, "x": 28.96, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 1.2333}, {"time": 2.5667, "x": 48.34, "curve": 0.311, "c3": 0.646, "c4": 0.35}, {"time": 2.6667, "x": 47.18}]}, "bone55": {"translate": [{"x": 33.62, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.8667, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 1.6, "x": 27.37, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 2.2, "x": 48.34, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "x": 33.62}]}, "bone56": {"translate": [{"x": 47.75, "curve": 0.273, "c2": 0.09, "c3": 0.629, "c4": 0.52}, {"time": 0.5, "x": 28.96, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 1.2667}, {"time": 2.6, "x": 48.34, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 2.6667, "x": 47.75}]}, "bone57": {"translate": [{"x": 27.37, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.6, "x": 48.34, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 2.6667, "x": 27.37}]}, "bone58": {"translate": [{"x": 28.96, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.7667}, {"time": 2.1, "x": 48.34, "curve": 0.256, "c3": 0.62, "c4": 0.47}, {"time": 2.6667, "x": 28.96}]}, "bone59": {"translate": [{"x": 48.34, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.6, "x": 6.22, "curve": 0.304, "c2": 0.24, "c3": 0.656, "c4": 0.63}, {"time": 2.0667, "x": 27.37, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 2.6667, "x": 48.34}]}, "bone60": {"translate": [{"x": 36.26}, {"time": 0.3333, "x": 48.34, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 0.4, "x": 47.75, "curve": 0.273, "c2": 0.09, "c3": 0.629, "c4": 0.52}, {"time": 0.9, "x": 28.96, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 1.6667}, {"time": 2.6667, "x": 36.26}]}, "bone61": {"translate": [{"x": 27.37, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.6, "x": 48.34, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 2.6667, "x": 27.37}]}, "bone62": {"translate": [{"x": 28.96, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.7667}, {"time": 2.1, "x": 48.34, "curve": 0.256, "c3": 0.62, "c4": 0.47}, {"time": 2.6667, "x": 28.96}]}, "bone63": {"translate": [{"x": 6.22, "curve": 0.304, "c2": 0.24, "c3": 0.656, "c4": 0.63}, {"time": 0.4667, "x": 27.37, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 1.0667, "x": 48.34, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "x": 6.22}]}, "bone64": {"translate": [{"x": 47.75, "curve": 0.273, "c2": 0.09, "c3": 0.629, "c4": 0.52}, {"time": 0.5, "x": 28.96, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 1.2667}, {"time": 2.6, "x": 48.34, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 2.6667, "x": 47.75}]}, "bone65": {"translate": [{"x": 47.77, "curve": 0.28, "c2": 0.1, "c3": 0.628, "c4": 0.5}, {"time": 0.4, "x": 33.62, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 1.2667, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 2, "x": 27.37, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 2.6, "x": 48.34, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 2.6667, "x": 47.77}]}, "bone66": {"translate": [{"x": 28.96, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.7667}, {"time": 2.1, "x": 48.34, "curve": 0.256, "c3": 0.62, "c4": 0.47}, {"time": 2.6667, "x": 28.96}]}, "bone67": {"translate": [{"x": 6.22, "curve": 0.304, "c2": 0.24, "c3": 0.656, "c4": 0.63}, {"time": 0.4667, "x": 27.37, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 1.0667, "x": 48.34, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "x": 6.22}]}, "bone68": {"translate": [{"x": 28.96, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.7667}, {"time": 2.1, "x": 48.34, "curve": 0.256, "c3": 0.62, "c4": 0.47}, {"time": 2.6667, "x": 28.96}]}, "bone69": {"translate": [{"x": 27.37, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.6, "x": 48.34, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 2.6667, "x": 27.37}]}, "bone70": {"translate": [{"x": 33.84}, {"time": 0.4, "x": 48.34, "curve": 0.311, "c3": 0.646, "c4": 0.35}, {"time": 0.5, "x": 47.18, "curve": 0.28, "c2": 0.13, "c3": 0.634, "c4": 0.54}, {"time": 0.9667, "x": 28.96, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 1.7333}, {"time": 2.6667, "x": 33.84}]}, "bone71": {"translate": [{"x": 8.84, "curve": 0.313, "c2": 0.27, "c3": 0.66, "c4": 0.65}, {"time": 0.4, "x": 27.37, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 1, "x": 48.34, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": 8.84}]}, "bone72": {"translate": [{"x": 28.96, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "x": 48.34, "curve": 0.256, "c3": 0.62, "c4": 0.47}, {"time": 2.6667, "x": 28.96}]}, "bone73": {"translate": [{"x": 33.62, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.8667, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 1.6, "x": 27.37, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 2.2, "x": 48.34, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "x": 33.62}]}, "bone74": {"translate": [{"x": 36.26}, {"time": 0.3333, "x": 48.34, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 0.4, "x": 47.75, "curve": 0.273, "c2": 0.09, "c3": 0.629, "c4": 0.52}, {"time": 0.9, "x": 28.96, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 1.6667}, {"time": 2.6667, "x": 36.26}]}, "bone75": {"translate": [{"x": 6.22, "curve": 0.304, "c2": 0.24, "c3": 0.656, "c4": 0.63}, {"time": 0.4667, "x": 27.37, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 1.0667, "x": 48.34, "curve": 0.25, "c3": 0.75}, {"time": 2.4, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "x": 6.22}]}, "bone76": {"translate": [{"x": 28.96, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.7667}, {"time": 2.1, "x": 48.34, "curve": 0.256, "c3": 0.62, "c4": 0.47}, {"time": 2.6667, "x": 28.96}]}, "bone77": {"translate": [{"x": 8.84, "curve": 0.313, "c2": 0.27, "c3": 0.66, "c4": 0.65}, {"time": 0.4, "x": 27.37, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 1, "x": 48.34, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": 8.84}]}, "bone78": {"translate": [{"x": 28.96, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 0.7667}, {"time": 2.1, "x": 48.34, "curve": 0.256, "c3": 0.62, "c4": 0.47}, {"time": 2.6667, "x": 28.96}]}, "bone79": {"translate": [{"x": 33.62, "curve": 0.354, "c2": 0.41, "c3": 0.756}, {"time": 0.8667, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 1.6, "x": 27.37, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 2.2, "x": 48.34, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "x": 33.62}]}, "bone80": {"translate": [{"x": 37.47}, {"time": 0.3, "x": 48.34, "curve": 0.311, "c3": 0.646, "c4": 0.35}, {"time": 0.4, "x": 47.18, "curve": 0.28, "c2": 0.13, "c3": 0.634, "c4": 0.54}, {"time": 0.8667, "x": 28.96, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 1.6333}, {"time": 2.6667, "x": 37.47}]}, "bone81": {"translate": [{"x": 27.37, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.6, "x": 48.34, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 2.6667, "x": 27.37}]}, "bone82": {"translate": [{"x": 47.18, "curve": 0.28, "c2": 0.13, "c3": 0.634, "c4": 0.54}, {"time": 0.4667, "x": 28.96, "curve": 0.366, "c2": 0.46, "c3": 0.754}, {"time": 1.2333}, {"time": 2.5667, "x": 48.34, "curve": 0.311, "c3": 0.646, "c4": 0.35}, {"time": 2.6667, "x": 47.18}]}, "bone83": {"translate": [{"x": 27.37, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.6, "x": 48.34, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 2.6667, "x": 27.37}]}}}, "mingzu_disappear": {"slots": {"mingzuyun": {"color": [{"time": 0.6667, "color": "ffffffff"}, {"time": 2, "color": "ffffff00"}], "attachment": [{"name": "<PERSON><PERSON><PERSON><PERSON>"}]}, "mingzulight": {"color": [{"time": 0.6667, "color": "ffc50000"}, {"time": 1.3333, "color": "ffc5005c"}, {"time": 2.3333, "color": "ffc50000"}], "attachment": [{"time": 0.6667, "name": "mingzulight"}]}, "ming": {"color": [{"color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{"name": "ming"}]}}, "bones": {"bone80": {"translate": [{"x": 37.47}]}, "bone60": {"translate": [{"x": 36.26}]}, "bone61": {"translate": [{"x": 27.37}]}, "bone59": {"translate": [{"x": 48.34}]}, "bone67": {"translate": [{"x": 6.22}]}, "bone72": {"translate": [{"x": 28.96}]}, "bone77": {"translate": [{"x": 8.84}]}, "bone56": {"translate": [{"x": 47.75}]}, "bone83": {"translate": [{"x": 27.37}]}, "bone79": {"translate": [{"x": 33.62}]}, "bone76": {"translate": [{"x": 28.96}]}, "bone78": {"translate": [{"x": 28.96}]}, "bone57": {"translate": [{"x": 27.37}]}, "bone82": {"translate": [{"x": 47.18}]}, "bone68": {"translate": [{"x": 28.96}]}, "bone2": {"translate": [{"x": 28.96}]}, "bone63": {"translate": [{"x": 6.22}]}, "bone73": {"translate": [{"x": 33.62}]}, "bone62": {"translate": [{"x": 28.96}]}, "bone65": {"translate": [{"x": 47.77}]}, "bone64": {"translate": [{"x": 47.75}]}, "bone58": {"translate": [{"x": 28.96}]}, "bone54": {"translate": [{"x": 47.18}]}, "bone81": {"translate": [{"x": 27.37}]}, "bone69": {"translate": [{"x": 27.37}]}, "bone74": {"translate": [{"x": 36.26}]}, "bone75": {"translate": [{"x": 6.22}]}, "bone71": {"translate": [{"x": 8.84}]}, "bone70": {"translate": [{"x": 33.84}]}, "bone53": {"translate": [{"x": 8.84}]}, "bone55": {"translate": [{"x": 33.62}]}, "bone66": {"translate": [{"x": 28.96}]}, "bone": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2, "x": 194.91}]}}}, "shenzu": {"slots": {"shenzuyun": {"attachment": [{"name": "<PERSON><PERSON><PERSON><PERSON>"}]}, "shen": {"attachment": [{"name": "shen"}]}}, "bones": {"shenzuall3": {"translate": [{"x": 38.07, "curve": 0.301, "c2": 0.22, "c3": 0.645, "c4": 0.59}, {"time": 0.3333, "x": 26.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.1333}, {"time": 2.4667, "x": 41.36, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 2.6667, "x": 38.07}]}, "shenzuall4": {"translate": [{"x": 14.48}, {"time": 0.8667, "x": 41.36, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.5333, "x": 20.68, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.2}, {"time": 2.6667, "x": 14.48}]}, "shenzuall5": {"translate": [{"x": 38.07, "curve": 0.301, "c2": 0.22, "c3": 0.645, "c4": 0.59}, {"time": 0.3333, "x": 26.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.1333}, {"time": 2.4667, "x": 41.36, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 2.6667, "x": 38.07}]}, "shenzuall6": {"translate": [{"x": 24.82}, {"time": 0.5333, "x": 41.36, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 0.7333, "x": 38.02, "curve": 0.295, "c2": 0.2, "c3": 0.647, "c4": 0.6}, {"time": 1.2, "x": 20.68, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.8667}, {"time": 2.6667, "x": 24.82}]}, "shenzuall7": {"translate": [{"x": 26.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8}, {"time": 2.1333, "x": 41.36, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "x": 26.15}]}, "shenzuall8": {"translate": [{"x": 20.68, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 41.36, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 20.68}]}, "shenzuall9": {"translate": [{"x": 16.55}, {"time": 0.8, "x": 41.36, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.3333, "x": 26.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.1333}, {"time": 2.6667, "x": 16.55}]}, "shenzuall10": {"translate": [{"x": 12.55, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 0.4667}, {"time": 1.8, "x": 41.36, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.1333, "x": 33.8, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 2.4667, "x": 20.68, "curve": 0.337, "c2": 0.34, "c3": 0.673, "c4": 0.69}, {"time": 2.6667, "x": 12.55}]}, "shenzuall11": {"translate": [{"x": 2.07}, {"time": 1.2667, "x": 41.36, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.8, "x": 26.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.6}, {"time": 2.6667, "x": 2.07}]}, "shenzuall12": {"translate": [{"x": 14.48}, {"time": 0.8667, "x": 41.36, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.5333, "x": 20.68, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.2}, {"time": 2.6667, "x": 14.48}]}, "shenzuall13": {"translate": [{"x": 36.19}, {"time": 0.1667, "x": 41.36, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 0.4667, "x": 35, "curve": 0.316, "c2": 0.27, "c3": 0.654, "c4": 0.62}, {"time": 0.7, "x": 26.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.5}, {"time": 2.6667, "x": 36.19}]}, "shenzuall14": {"translate": [{"x": 39.3}, {"time": 0.0667, "x": 41.36, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.7333, "x": 20.68, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.4}, {"time": 2.6667, "x": 39.3}]}, "shenzuall15": {"translate": [{"x": 26.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8}, {"time": 2.1333, "x": 41.36, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "x": 26.15}]}, "shenzuall16": {"translate": [{"x": 33.8, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.3333, "x": 20.68, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1}, {"time": 2.3333, "x": 41.36, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": 33.8}]}, "shenzuall17": {"translate": [{"x": 26.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8}, {"time": 2.1333, "x": 41.36, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "x": 26.15}]}, "shenzuall18": {"translate": [{"x": 2.43, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667}, {"time": 0.4333, "x": 8.27, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 14.48}, {"time": 1.5, "x": 41.36, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.1667, "x": 20.68, "curve": 0.356, "c2": 0.41, "c3": 0.711, "c4": 0.82}, {"time": 2.6667, "x": 2.43}]}, "shenzuall19": {"translate": [{"x": 38.07, "curve": 0.301, "c2": 0.22, "c3": 0.645, "c4": 0.59}, {"time": 0.3333, "x": 26.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.1333}, {"time": 2.4667, "x": 41.36, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 2.6667, "x": 38.07}]}, "shenzuall20": {"translate": [{"x": 32.06}, {"time": 0.3, "x": 41.36, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 0.7333, "x": 30.11, "curve": 0.324, "c2": 0.3, "c3": 0.662, "c4": 0.65}, {"time": 0.9667, "x": 20.68, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6333}, {"time": 2.6667, "x": 32.06}]}, "shenzuall21": {"translate": [{"x": 40.86, "curve": 0.275, "c2": 0.1, "c3": 0.629, "c4": 0.51}, {"time": 0.4667, "x": 26.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.2667}, {"time": 2.6, "x": 41.36, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 2.6667, "x": 40.86}]}, "shenzuall22": {"translate": [{"x": 20.68, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}, {"time": 2, "x": 41.36, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 20.68}]}, "shenzuall23": {"translate": [{"x": 11.31, "curve": 0.382, "c2": 0.57, "c3": 0.734}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": 41.36, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 1.9667, "x": 38.07, "curve": 0.301, "c2": 0.22, "c3": 0.645, "c4": 0.59}, {"time": 2.3, "x": 26.15, "curve": 0.332, "c2": 0.33, "c3": 0.677, "c4": 0.7}, {"time": 2.6667, "x": 11.31}]}, "shenzuall24": {"translate": [{"x": 20.68, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}, {"time": 2, "x": 41.36, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 20.68}]}, "shenzuall25": {"translate": [{"x": 33.83, "curve": 0.319, "c2": 0.29, "c3": 0.656, "c4": 0.63}, {"time": 0.2, "x": 26.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1}, {"time": 2.3333, "x": 41.36, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "x": 33.83}]}, "shenzuall26": {"translate": [{"x": 20.68, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}, {"time": 2, "x": 41.36, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 20.68}]}, "shenzuall27": {"translate": [{"x": 10.34}, {"time": 1, "x": 41.36, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.5333, "x": 26.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 2.3333}, {"time": 2.6667, "x": 10.34}]}, "shenzuall28": {"translate": [{"x": 28.81, "curve": 0.327, "c2": 0.31, "c3": 0.663, "c4": 0.66}, {"time": 0.2, "x": 20.68}, {"time": 0.8667}, {"time": 2.2, "x": 41.36, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "x": 28.81}]}, "shenzuall29": {"translate": [{"x": 40.86, "curve": 0.275, "c2": 0.1, "c3": 0.629, "c4": 0.51}, {"time": 0.4667, "x": 26.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.2667}, {"time": 2.6, "x": 41.36, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 2.6667, "x": 40.86}]}, "shenzuall30": {"translate": [{"x": 37.1, "curve": 0.3, "c2": 0.22, "c3": 0.65, "c4": 0.61}, {"time": 0.4333, "x": 20.68, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.1}, {"time": 2.4333, "x": 41.36, "curve": 0.289, "c3": 0.628, "c4": 0.38}, {"time": 2.6667, "x": 37.1}]}, "shenzuall31": {"translate": [{"x": 38.07, "curve": 0.301, "c2": 0.22, "c3": 0.645, "c4": 0.59}, {"time": 0.3333, "x": 26.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.1333}, {"time": 2.4667, "x": 41.36, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 2.6667, "x": 38.07}]}, "shenzuall32": {"translate": [{"x": 14.48}, {"time": 0.8667, "x": 41.36, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.5333, "x": 20.68, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.2}, {"time": 2.6667, "x": 14.48}]}, "shenzuall33": {"translate": [{"x": 40.86, "curve": 0.275, "c2": 0.1, "c3": 0.629, "c4": 0.51}, {"time": 0.4667, "x": 26.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.2667}, {"time": 2.6, "x": 41.36, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 2.6667, "x": 40.86}]}, "shenzuall34": {"translate": [{"x": 20.68, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}, {"time": 2, "x": 41.36, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 20.68}]}, "shenzuall35": {"translate": [{"x": 38.07, "curve": 0.301, "c2": 0.22, "c3": 0.645, "c4": 0.59}, {"time": 0.3333, "x": 26.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.1333}, {"time": 2.4667, "x": 41.36, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 2.6667, "x": 38.07}]}, "shenzuall36": {"translate": [{"x": 20.68, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}, {"time": 2, "x": 41.36, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "x": 20.68}]}}}, "shenzu_disappear": {"slots": {"shenzuyun": {"color": [{"time": 0.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffff00"}], "attachment": [{"name": "<PERSON><PERSON><PERSON><PERSON>"}]}, "shen": {"color": [{"color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{"name": "shen"}]}, "shenzulight": {"color": [{"time": 0.6667, "color": "ffcd0000"}, {"time": 1.3333, "color": "ffcd0051"}, {"time": 2.3333, "color": "ffcd0000"}], "attachment": [{"time": 0.6667, "name": "shenzulight"}]}}, "bones": {"shenzuall10": {"translate": [{"x": 12.55}]}, "shenzuall32": {"translate": [{"x": 14.48}]}, "shenzuall14": {"translate": [{"x": 39.3}]}, "shenzuall16": {"translate": [{"x": 33.8}]}, "shenzuall23": {"translate": [{"x": 11.31}]}, "shenzuall6": {"translate": [{"x": 24.82}]}, "shenzuall27": {"translate": [{"x": 10.34}]}, "shenzuall29": {"translate": [{"x": 40.86}]}, "shenzuall22": {"translate": [{"x": 20.68}]}, "shenzuall31": {"translate": [{"x": 38.07}]}, "shenzuall12": {"translate": [{"x": 14.48}]}, "shenzuall19": {"translate": [{"x": 38.07}]}, "shenzuall28": {"translate": [{"x": 28.81}]}, "shenzuall20": {"translate": [{"x": 32.06}]}, "shenzuall11": {"translate": [{"x": 2.07}]}, "shenzucloud": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2, "x": 130.72}]}, "shenzuall9": {"translate": [{"x": 16.55}]}, "shenzuall5": {"translate": [{"x": 38.07}]}, "shenzuall4": {"translate": [{"x": 14.48}]}, "shenzuall34": {"translate": [{"x": 20.68}]}, "shenzuall21": {"translate": [{"x": 40.86}]}, "shenzuall13": {"translate": [{"x": 36.19}]}, "shenzuall24": {"translate": [{"x": 20.68}]}, "shenzuall7": {"translate": [{"x": 26.15}]}, "shenzuall15": {"translate": [{"x": 26.15}]}, "shenzuall25": {"translate": [{"x": 33.83}]}, "shenzuall18": {"translate": [{"x": 2.43}]}, "shenzuall17": {"translate": [{"x": 26.15}]}, "shenzuall3": {"translate": [{"x": 38.07}]}, "shenzuall26": {"translate": [{"x": 20.68}]}, "shenzuall33": {"translate": [{"x": 40.86}]}, "shenzuall36": {"translate": [{"x": 20.68}]}, "shenzuall8": {"translate": [{"x": 20.68}]}, "shenzuall35": {"translate": [{"x": 38.07}]}, "shenzuall30": {"translate": [{"x": 37.1}]}}}, "wuzu": {"slots": {"wuzuyun": {"attachment": [{"name": "wuzuyun"}]}, "wu2": {"attachment": [{"name": "wu"}]}}, "bones": {"bone3": {"translate": [{"x": 15.85, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 23.86, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "x": 15.85}]}, "bone4": {"translate": [{"x": 20.28}, {"time": 0.2, "x": 23.86, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 0.8333, "x": 12.72, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 1.5333}, {"time": 2.6667, "x": 20.28}]}, "bone5": {"translate": [{"x": 17.89}, {"time": 0.3333, "x": 23.86, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.8333, "x": 15.85, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.6667}, {"time": 2.6667, "x": 17.89}]}, "bone6": {"translate": [{"x": 8.01, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5}, {"time": 1.8333, "x": 23.86, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": 8.01}]}, "bone7": {"translate": [{"x": 5.37}, {"time": 0.8333, "x": 20.28, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "x": 23.86, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 1.5333, "x": 15.85, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 2.3667}, {"time": 2.6667, "x": 5.37}]}, "bone8": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 23.86, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone9": {"translate": [{"x": 15.85, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333}, {"time": 2.1667, "x": 23.86, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "x": 15.85}]}, "bone10": {"translate": [{"x": 23.86, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.8333, "x": 8.01, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1.3333}, {"time": 2.6667, "x": 23.86}]}, "bone11": {"translate": [{"x": 17.89}, {"time": 0.3333, "x": 23.86, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.8333, "x": 15.85, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.6667}, {"time": 2.6667, "x": 17.89}]}, "bone12": {"translate": [{"x": 15.85, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333}, {"time": 2.1667, "x": 23.86, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "x": 15.85}]}, "bone13": {"translate": [{"x": 15.85, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333}, {"time": 2.1667, "x": 23.86, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "x": 15.85}]}, "bone14": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 23.86, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone15": {"translate": [{"x": 11.33}, {"time": 0.7, "x": 23.86, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 1.2, "x": 15.85, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 2.0333}, {"time": 2.6667, "x": 11.33}]}, "bone16": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 23.86, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone17": {"translate": [{"x": 17.89}, {"time": 0.3333, "x": 23.86, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.8333, "x": 15.85, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.6667}, {"time": 2.6667, "x": 17.89}]}, "bone18": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 23.86, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone19": {"translate": [{"x": 20.28}, {"time": 0.2, "x": 23.86, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.7, "x": 15.85, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.5333}, {"time": 2.6667, "x": 20.28}]}, "bone20": {"translate": [{"x": 8.01, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5}, {"time": 1.8333, "x": 23.86, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": 8.01}]}, "bone21": {"translate": [{"x": 15.85, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333}, {"time": 2.1667, "x": 23.86, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "x": 15.85}]}, "bone22": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 23.86, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone23": {"translate": [{"x": 15.85, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333}, {"time": 2.1667, "x": 23.86, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "x": 15.85}]}, "bone24": {"translate": [{"x": 22.89, "curve": 0.278, "c2": 0.14, "c3": 0.654, "c4": 0.62}, {"time": 0.7, "x": 8.01, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1.2}, {"time": 2.5333, "x": 23.86, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2.6667, "x": 22.89}]}, "bone25": {"translate": [{"x": 15.85, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333}, {"time": 2.1667, "x": 23.86, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "x": 15.85}]}, "bone26": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 23.86, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone27": {"translate": [{"x": 23.86, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.5, "x": 15.85, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.3333}, {"time": 2.6667, "x": 23.86}]}, "bone28": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 23.86, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone29": {"translate": [{"x": 20.28}, {"time": 0.2, "x": 23.86, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.7, "x": 15.85, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.5333}, {"time": 2.6667, "x": 20.28}]}, "bone30": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 23.86, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone31": {"translate": [{"x": 23.86, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.5, "x": 15.85, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.3333}, {"time": 2.6667, "x": 23.86}]}, "bone32": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 23.86, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone33": {"translate": [{"x": 15.85, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333}, {"time": 2.1667, "x": 23.86, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "x": 15.85}]}, "bone34": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 23.86, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone35": {"translate": [{"x": 17.89}, {"time": 0.3333, "x": 23.86, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.8333, "x": 15.85, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.6667}, {"time": 2.6667, "x": 17.89}]}, "bone36": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 23.86, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone37": {"translate": [{"x": 15.85, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333}, {"time": 2.1667, "x": 23.86, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "x": 15.85}]}, "bone38": {"translate": [{"x": 12.72, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 0.7}, {"time": 2.0333, "x": 23.86, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 2.6667, "x": 12.72}]}, "bone39": {"translate": [{"x": 17.89}, {"time": 0.3333, "x": 23.86, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.8333, "x": 15.85, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.6667}, {"time": 2.6667, "x": 17.89}]}, "bone40": {"translate": [{"x": 8.01, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.5}, {"time": 1.8333, "x": 23.86, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "x": 8.01}]}, "bone41": {"translate": [{"x": 15.85, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333}, {"time": 2.1667, "x": 23.86, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "x": 15.85}]}, "bone42": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 23.86, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone43": {"translate": [{"x": 15.85, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333}, {"time": 2.1667, "x": 23.86, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "x": 15.85}]}, "bone44": {"translate": [{"x": 12.72, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 0.7}, {"time": 2.0333, "x": 23.86, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 2.6667, "x": 12.72}]}, "bone45": {"translate": [{"x": 23.86, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.5, "x": 15.85, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.3333}, {"time": 2.6667, "x": 23.86}]}, "bone46": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 23.86, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone47": {"translate": [{"x": 15.85, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333}, {"time": 2.1667, "x": 23.86, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "x": 15.85}]}, "bone48": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 23.86, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone49": {"translate": [{"x": 11.33}, {"time": 0.7, "x": 23.86, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 1.2, "x": 15.85, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 2.0333}, {"time": 2.6667, "x": 11.33}]}, "bone50": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 23.86, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "bone51": {"translate": [{"x": 15.85, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333}, {"time": 2.1667, "x": 23.86, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "x": 15.85}]}, "bone52": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 23.86, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}}}, "wuzu_disappear": {"slots": {"wuzuyun": {"color": [{"time": 0.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2, "color": "ffffff00"}], "attachment": [{"name": "wuzuyun"}]}, "wu2": {"color": [{"color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{"name": "wu"}]}, "wuzulight": {"color": [{"time": 0.3667, "color": "ffeb0000"}, {"time": 1.3667, "color": "ffeb004e"}, {"time": 2.3333, "color": "ffeb0000"}], "attachment": [{"time": 0.3667, "name": "wuzulight"}]}}, "bones": {"wuzuyun": {"translate": [{}, {"time": 2, "x": 133.18}]}}, "events": [{"time": 1.5, "name": "lizi_appear"}]}, "yaozu": {"slots": {"yaozuyun": {"attachment": [{"name": "<PERSON><PERSON><PERSON><PERSON>"}]}, "yao": {"attachment": [{"name": "yao"}]}}, "bones": {"yaozu68": {"translate": [{"x": 0.88, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.309, "c2": 0.52, "c3": 0.653, "c4": 0.82}, {"time": 0.6333, "x": 11.83, "curve": 0.294, "c2": 0.63, "c3": 0.744}, {"time": 1.5, "x": 14.94, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.5667, "x": 1.94, "curve": 0.346, "c2": 0.39, "c3": 0.68, "c4": 0.73}, {"time": 2.6667, "x": 0.88}]}, "yaozu67": {"translate": [{"x": 9.45, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8, "curve": 0.321, "c2": 1.2, "c3": 0.75}, {"time": 2.1333, "x": 14.94, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "x": 9.45}]}, "yaozu66": {"translate": [{"x": 8.46, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.6, "x": 14.94, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 2.6667, "x": 8.46}]}, "yaozu65": {"translate": [{"x": 9.45, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8, "curve": 0.321, "c2": 1.2, "c3": 0.75}, {"time": 2.1333, "x": 14.94, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "x": 9.45}]}, "yaozu64": {"translate": [{"x": 10.34, "curve": 0.333, "c2": 0.33, "c3": 0.698, "c4": 0.77}, {"time": 0.6, "x": 1.94, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.8667, "curve": 0.321, "c2": 1.2, "c3": 0.75}, {"time": 2.2, "x": 14.94, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "x": 10.34}]}, "yaozu63": {"translate": [{"x": 13.03, "curve": 0.311, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 0.2667, "x": 9.45, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.0667, "curve": 0.321, "c2": 1.2, "c3": 0.75}, {"time": 2.4, "x": 14.94, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2.6667, "x": 13.03}]}, "yaozu62": {"translate": [{"x": 2.76, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 0.7, "x": 3.17, "curve": 0.317, "c2": 0.28, "c3": 0.661, "c4": 0.65}, {"time": 1.0667, "x": 8.46, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 1.6667, "x": 14.94, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 2.76}]}, "yaozu61": {"translate": [{"x": 9.45, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8, "curve": 0.321, "c2": 1.2, "c3": 0.75}, {"time": 2.1333, "x": 14.94, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "x": 9.45}]}, "yaozu60": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 14.94, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "yaozu59": {"translate": [{"x": 2.75, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.321, "c2": 1.2, "c3": 0.75}, {"time": 1.6667, "x": 14.94, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.9333, "x": 13.03, "curve": 0.311, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 2.2, "x": 9.45, "curve": 0.336, "c2": 0.34, "c3": 0.688, "c4": 0.73}, {"time": 2.6667, "x": 2.75}]}, "yaozu58": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 14.94, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "yaozu57": {"translate": [{"x": 9.45, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8, "curve": 0.268, "c2": 0.67, "c3": 0.71, "c4": 0.81}, {"time": 2.1333, "x": 14.94, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "x": 9.45}]}, "yaozu56": {"translate": [{"x": 10.81, "curve": 0.33, "c2": 0.32, "c3": 0.698, "c4": 0.77}, {"time": 0.6333, "x": 1.94, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.9, "curve": 0.321, "c2": 1.2, "c3": 0.75}, {"time": 2.2333, "x": 14.94, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "x": 10.81}]}, "yaozu55": {"translate": [{"x": 14.07, "curve": 0.296, "c2": 0.19, "c3": 0.641, "c4": 0.57}, {"time": 0.3667, "x": 9.45, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.1667, "curve": 0.321, "c2": 1.2, "c3": 0.75}, {"time": 2.5, "x": 14.94, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2.6667, "x": 14.07}]}, "yaozu54": {"translate": [{"x": 7.97, "curve": 0.372, "c2": 0.49, "c3": 0.751}, {"time": 0.7, "curve": 0.321, "c2": 1.2, "c3": 0.75}, {"time": 2.0333, "x": 14.94, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 2.6667, "x": 7.97}]}, "yaozu53": {"translate": [{"x": 0.18, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.0667, "curve": 0.321, "c2": 1.2, "c3": 0.75}, {"time": 1.4, "x": 14.94, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.9333, "x": 9.45, "curve": 0.355, "c2": 0.41, "c3": 0.737, "c4": 0.92}, {"time": 2.6667, "x": 0.18}]}, "yaozu52": {"translate": [{"x": 6.9, "curve": 0.348, "c2": 0.38, "c3": 0.693, "c4": 0.75}, {"time": 0.3667, "x": 1.94, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.6333, "curve": 0.321, "c2": 1.2, "c3": 0.75}, {"time": 1.9667, "x": 14.94, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 2.6667, "x": 6.9}]}, "yaozu51": {"translate": [{"x": 9.45, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8, "curve": 0.321, "c2": 1.2, "c3": 0.75}, {"time": 2.1333, "x": 14.94, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "x": 9.45}]}, "yaozu50": {"translate": [{"x": 11.13, "curve": 0.377, "c2": 0.51, "c3": 0.748}, {"time": 0.6333, "curve": 0.321, "c2": 1.2, "c3": 0.75}, {"time": 1.9667, "x": 23.83, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 2.6667, "x": 11.13}]}, "yaozu49": {"translate": [{"x": 23.97, "curve": 0.353, "c2": 0.55, "c3": 0.688}, {"time": 0.1333, "x": 23.83, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "x": 15.07, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.4667, "curve": 0.314, "c2": 1.08, "c3": 0.723, "c4": 1.02}, {"time": 2.6667, "x": 23.97}]}, "yaozu48": {"translate": [{"x": 13.49, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.6, "x": 23.83, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 2.6667, "x": 13.49}]}, "yaozu47": {"translate": [{"x": 15.07, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8, "curve": 0.321, "c2": 1.2, "c3": 0.75}, {"time": 2.1333, "x": 23.83, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "x": 15.07}]}, "yaozu46": {"translate": [{"x": 3.1, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2667, "curve": 0.321, "c2": 1.2, "c3": 0.75}, {"time": 1.6, "x": 23.83, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2.6667, "x": 3.1}]}, "yaozu45": {"translate": [{"x": 23.97, "curve": 0.353, "c2": 0.55, "c3": 0.688}, {"time": 0.1333, "x": 23.83, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "x": 15.07, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.4667, "curve": 0.314, "c2": 1.08, "c3": 0.723, "c4": 1.02}, {"time": 2.6667, "x": 23.97}]}, "yaozu44": {"translate": [{"x": 13.49, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.6, "x": 23.83, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 2.6667, "x": 13.49}]}, "yaozu43": {"translate": [{"x": 15.07, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8, "curve": 0.321, "c2": 1.2, "c3": 0.75}, {"time": 2.1333, "x": 23.83, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "x": 15.07}]}, "yaozu42": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 23.83, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "yaozu41": {"translate": [{"x": 24.1, "curve": 0.364, "c2": 0.38, "c3": 0.701}, {"time": 0.2333, "x": 23.83, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.7667, "x": 15.04, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 1.0333, "x": 8.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.5667, "curve": 0.311, "c2": 0.98, "c3": 0.707, "c4": 1.02}, {"time": 2.6667, "x": 24.1}]}, "yaozu40": {"translate": [{"x": 17.35, "curve": 0.365, "c2": 1.39, "c3": 0.746}, {"time": 0.9333, "x": 23.83, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6, "x": 11.92, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.2667, "curve": 0.311, "c2": 0.48, "c3": 0.652, "c4": 0.8}, {"time": 2.6667, "x": 17.35}]}, "yaozu39": {"translate": [{"x": 10.47, "curve": 0.348, "c2": 1.27, "c3": 0.75}, {"time": 1.1333, "x": 23.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.9333, "x": 8.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.4667, "curve": 0.319, "c2": 0.4, "c3": 0.655, "c4": 0.73}, {"time": 2.6667, "x": 10.47}]}, "yaozu38": {"translate": [{"x": 11.01, "curve": 0.348, "c2": 0.38, "c3": 0.693, "c4": 0.75}, {"time": 0.3667, "x": 3.1, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.6333, "curve": 0.321, "c2": 1.2, "c3": 0.75}, {"time": 1.9667, "x": 23.83, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 2.6667, "x": 11.01}]}, "yaozu37": {"translate": [{"x": 8.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5333, "curve": 0.321, "c2": 1.2, "c3": 0.75}, {"time": 1.8667, "x": 23.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "x": 8.77}]}, "yaozu36": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 23.83, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "yaozu35": {"translate": [{"x": 24.07, "curve": 0.361, "c2": 0.45, "c3": 0.697}, {"time": 0.2, "x": 23.83, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 0.6667, "x": 16.57, "curve": 0.326, "c2": 0.31, "c3": 0.669, "c4": 0.67}, {"time": 1, "x": 8.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.5333, "curve": 0.312, "c2": 1.01, "c3": 0.712, "c4": 1.02}, {"time": 2.6667, "x": 24.07}]}, "yaozu34": {"translate": [{"x": 12.62, "curve": 0.342, "c2": 0.36, "c3": 0.687, "c4": 0.73}, {"time": 0.3667, "x": 4.4, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.7, "curve": 0.321, "c2": 1.2, "c3": 0.75}, {"time": 2.0333, "x": 23.83, "curve": 0.252, "c3": 0.623, "c4": 0.49}, {"time": 2.6667, "x": 12.62}]}, "yaozu33": {"translate": [{"x": 23.55, "curve": 0.263, "c2": 0.08, "c3": 0.645, "c4": 0.59}, {"time": 0.7333, "x": 8.77, "curve": 0.336, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.8, "x": 7.22, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 1.2667, "curve": 0.319, "c2": 0.4, "c3": 0.655, "c4": 0.73}, {"time": 1.4667, "x": 10.47, "curve": 0.348, "c2": 1.27, "c3": 0.75}, {"time": 2.6, "x": 23.83, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 2.6667, "x": 23.55}]}, "yaozu32": {"translate": [{"x": 5.04, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.3667, "curve": 0.321, "c2": 1.2, "c3": 0.75}, {"time": 1.7, "x": 23.83, "curve": 0.242, "c3": 0.661, "c4": 0.65}, {"time": 2.6667, "x": 5.04}]}, "yaozu31": {"translate": [{"x": 0.09, "curve": 0.341, "c2": 0.66, "c3": 0.675}, {"time": 0.0333, "curve": 0.321, "c2": 1.2, "c3": 0.75}, {"time": 1.3667, "x": 23.83, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.9, "x": 15.04, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 2.1667, "x": 8.77, "curve": 0.376, "c2": 0.51, "c3": 0.733, "c4": 0.95}, {"time": 2.6667, "x": 0.09}]}, "yaozu30": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 23.83, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "yaozu29": {"translate": [{"x": 15.04, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.2667, "x": 8.77, "curve": 0.363, "c2": 0.44, "c3": 0.711, "c4": 0.83}, {"time": 0.6667, "x": 0.95, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.8, "curve": 0.319, "c2": 0.4, "c3": 0.655, "c4": 0.73}, {"time": 1, "x": 10.47, "curve": 0.348, "c2": 1.27, "c3": 0.75}, {"time": 2.1333, "x": 23.83, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "x": 15.04}]}, "yaozu28": {"translate": [{"x": 4.4, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.313, "c2": 0.71, "c3": 0.75}, {"time": 1.6667, "x": 23.83, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "x": 4.4}]}, "yaozu27": {"translate": [{"x": 8.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5333, "curve": 0.317, "c2": 0.66, "c3": 0.75}, {"time": 1.9, "x": 23.83, "curve": 0.232, "c2": 0.07, "c3": 0.637, "c4": 0.56}, {"time": 2.6667, "x": 8.77}]}, "yaozu26": {"translate": [{"x": 24.02, "curve": 0.357, "c2": 0.5, "c3": 0.692}, {"time": 0.1667, "x": 23.83, "curve": 0.249, "c3": 0.628, "c4": 0.51}, {"time": 0.8667, "x": 11.01, "curve": 0.348, "c2": 0.38, "c3": 0.693, "c4": 0.75}, {"time": 1.2333, "x": 3.1, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.5, "curve": 0.313, "c2": 1.04, "c3": 0.717, "c4": 1.02}, {"time": 2.6667, "x": 24.02}]}, "yaozu25": {"translate": [{"curve": 0.319, "c2": 0.4, "c3": 0.655, "c4": 0.73}, {"time": 0.2, "x": 10.47, "curve": 0.348, "c2": 1.27, "c3": 0.75}, {"time": 1.3333, "x": 23.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.1333, "x": 8.77, "curve": 0.336, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 2.2, "x": 7.22, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 2.6667}]}, "yaozu24": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 23.83, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "yaozu23": {"translate": [{"x": 22.87, "curve": 0.278, "c2": 0.15, "c3": 0.651, "c4": 0.61}, {"time": 0.6667, "x": 8.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.2, "curve": 0.321, "c2": 1.2, "c3": 0.75}, {"time": 2.5333, "x": 23.83, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 2.6667, "x": 22.87}]}, "yaozu22": {"translate": [{"x": 13.49, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.6, "x": 23.83, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 2.6667, "x": 13.49}]}, "yaozu21": {"translate": [{"x": 15.04, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.2667, "x": 8.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.8, "curve": 0.263, "c2": 0.7, "c3": 0.75, "c4": 0.92}, {"time": 2.1333, "x": 23.83, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2.6667, "x": 15.04}]}, "yaozu20": {"translate": [{"x": 5.04, "curve": 0.381, "c2": 0.59, "c3": 0.71, "c4": 0.94}, {"time": 0.3667, "curve": 0.339, "c2": 0.61, "c3": 0.741, "c4": 0.84}, {"time": 1.7, "x": 23.83, "curve": 0.242, "c3": 0.661, "c4": 0.65}, {"time": 2.6667, "x": 5.04}]}, "yaozu19": {"translate": [{"x": 10.47, "curve": 0.348, "c2": 1.27, "c3": 0.75}, {"time": 1.1333, "x": 23.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.9333, "x": 8.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.4667, "curve": 0.319, "c2": 0.4, "c3": 0.655, "c4": 0.73}, {"time": 2.6667, "x": 10.47}]}, "yaozu18": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 23.83, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "yaozu17": {"translate": [{"x": 17.34, "curve": 0.326, "c2": 0.31, "c3": 0.661, "c4": 0.65}, {"time": 0.1667, "x": 13.47, "curve": 0.333, "c2": 0.33, "c3": 0.67, "c4": 0.68}, {"time": 0.3667, "x": 8.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.9, "curve": 0.321, "c2": 1.2, "c3": 0.75}, {"time": 2.2333, "x": 23.83, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "x": 17.34}]}, "yaozu16": {"translate": [{"x": 1.98, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.2, "curve": 0.321, "c2": 1.2, "c3": 0.75}, {"time": 1.5333, "x": 23.83, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 2.6667, "x": 1.98}]}, "yaozu15": {"translate": [{"x": 23.7, "curve": 0.254, "c2": 0.04, "c3": 0.641, "c4": 0.57}, {"time": 0.7667, "x": 8.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.3, "curve": 0.319, "c2": 0.4, "c3": 0.655, "c4": 0.73}, {"time": 1.5, "x": 10.47, "curve": 0.348, "c2": 1.27, "c3": 0.75}, {"time": 2.6333, "x": 23.83, "curve": 0.325, "c3": 0.659, "c4": 0.34}, {"time": 2.6667, "x": 23.7}]}, "yaozu14": {"translate": [{"x": 20.06, "curve": 0.311, "c2": 0.26, "c3": 0.695, "c4": 0.76}, {"time": 0.7667, "x": 3.1, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.0333, "curve": 0.473, "c2": 0.81, "c3": 0.661, "c4": 0.94}, {"time": 2.3667, "x": 23.83, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 2.6667, "x": 20.06}]}, "yaozu13": {"translate": [{"x": 23.86, "curve": 0.403, "c2": 0.12, "c3": 0.72}, {"time": 0.4333, "x": 23.83, "curve": 0.403, "c2": 0.12, "c3": 0.621, "c4": 0.48}, {"time": 1.0333, "x": 13.47, "curve": 0.403, "c2": 0.12, "c3": 0.67, "c4": 0.68}, {"time": 1.2333, "x": 8.77, "curve": 0.403, "c2": 0.12, "c3": 0.742}, {"time": 1.7667, "curve": 0.403, "c2": 0.12, "c3": 0.68, "c4": 0.97}, {"time": 2.6667, "x": 23.86}]}, "yaozu12": {"translate": [{"x": 6.52, "curve": 0.35, "c2": 0.39, "c3": 0.757}, {"time": 0.9, "x": 23.83, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "curve": 0.266, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "x": 6.52}]}, "yaozu11": {"translate": [{"x": 24.07, "curve": 0.361, "c2": 0.45, "c3": 0.697}, {"time": 0.2, "x": 23.83, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.8, "x": 13.47, "curve": 0.333, "c2": 0.33, "c3": 0.67, "c4": 0.68}, {"time": 1, "x": 8.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.5333, "curve": 0.312, "c2": 1.01, "c3": 0.712, "c4": 1.02}, {"time": 2.6667, "x": 24.07}]}, "yaozu10": {"translate": [{"x": 10.22, "curve": 0.349, "c2": 0.39, "c3": 0.692, "c4": 0.75}, {"time": 0.3333, "x": 3.1, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.6, "curve": 0.223, "c2": 0.33, "c3": 0.746, "c4": 0.65}, {"time": 1.9333, "x": 23.83, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 2.6667, "x": 10.22}]}, "yaozu9": {"translate": [{"x": 13.47, "curve": 0.333, "c2": 0.33, "c3": 0.67, "c4": 0.68}, {"time": 0.2, "x": 8.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.7333, "curve": 0.696, "c2": 0.48, "c3": 0.5, "c4": 0.58}, {"time": 2.0667, "x": 23.83, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 2.6667, "x": 13.47}]}, "yaozu8": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 23.83, "curve": 0.25, "c3": 0.75}, {"time": 2.6667}]}, "yaozu7": {"translate": [{"x": 21.93, "curve": 0.306, "c2": 0.23, "c3": 0.646, "c4": 0.59}, {"time": 0.2667, "x": 16.57, "curve": 0.326, "c2": 0.31, "c3": 0.669, "c4": 0.67}, {"time": 0.6, "x": 8.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.1333, "curve": 0.321, "c2": 1.2, "c3": 0.75}, {"time": 2.4667, "x": 23.83, "curve": 0.294, "c3": 0.631, "c4": 0.37}, {"time": 2.6667, "x": 21.93}]}, "yaozu6": {"translate": [{"x": 18.7, "curve": 0.321, "c2": 0.29, "c3": 0.697, "c4": 0.76}, {"time": 0.7, "x": 3.1, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.9667, "curve": 0.321, "c2": 1.2, "c3": 0.75}, {"time": 2.3, "x": 23.83, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 2.6667, "x": 18.7}]}, "yaozu5": {"translate": [{"x": 16.57, "curve": 0.326, "c2": 0.31, "c3": 0.669, "c4": 0.67}, {"time": 0.3333, "x": 8.77, "curve": 0.381, "c2": 0.55, "c3": 0.747, "c4": 0.92}, {"time": 0.8667, "curve": 0.25, "c2": 0.55, "c3": 0.75}, {"time": 2.2, "x": 23.83, "curve": 0.263, "c3": 0.618, "c4": 0.43}, {"time": 2.6667, "x": 16.57}]}, "yaozu4": {"translate": [{"x": 10.22, "curve": 0.339, "c2": 0.35, "c3": 0.674, "c4": 0.69}, {"time": 0.1667, "x": 7.84, "curve": 0.346, "c2": 0.38, "c3": 0.682, "c4": 0.72}, {"time": 0.3333, "x": 3.1, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.6, "curve": 0.321, "c2": 1.2, "c3": 0.75}, {"time": 1.9333, "x": 23.83, "curve": 0.247, "c3": 0.63, "c4": 0.53}, {"time": 2.6667, "x": 10.22}]}, "yaozu3": {"translate": [{"x": 3.84, "curve": 0.331, "c2": 1.22, "c3": 0.75}, {"time": 1.2667, "x": 23.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2.0667, "x": 8.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 2.6, "curve": 0.328, "c2": 0.35, "c3": 0.662, "c4": 0.69}, {"time": 2.6667, "x": 3.84}]}}}, "yaozu_disappear": {"slots": {"yaozuyun": {"color": [{"time": 0.6667, "color": "ffffffff"}, {"time": 2, "color": "ffffff00"}], "attachment": [{"name": "<PERSON><PERSON><PERSON><PERSON>"}]}, "yao": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{"name": "yao"}]}, "yaozulight": {"color": [{"color": "ffc90000", "curve": "stepped"}, {"time": 0.6333, "color": "ffc90000", "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "color": "ffc90047", "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "color": "ffc90000"}], "attachment": [{"time": 0.6333, "name": "yaozulight"}]}}, "bones": {"yaozu": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 2, "x": 115.88}]}}, "events": [{"time": 1.5, "name": "lizi_appear"}]}}}